### 国内万傲瑞达V6000 2.2.0 & 海外百傲瑞达V5000 4.2.0（20210930发布版本）
1、新增推送《考勤记录》到事件中心 -- lgq-20210712
2、调整涉及radio、checkbox、select组件，统一使用平台组件 -- lgq-20210712
2.1、《考勤设备》授权、编辑设备是否登记机、升级固件、同步软件到设备、重新上传数据。
2.2、《按区域设置人员》导入区域人员
2.3、《原始记录表》导入U盘记录、导出考勤照片
2.4、《考勤规则》按上班比例时长计算、启用实时点名、是否启动员工自助登录入口
2.5、《报表推送》新增、编辑
2.6、《流程设置》新增、编辑
2.7、《考勤点》新增、编辑
2.8、《时间段》正常时间段新增、编辑
2.9、区域树、部门树时候包含下级
2.10、临时排班界面新增、编辑
2.11、《原始记录表》同步考勤点记录，选择考勤点列表模块搜索
2.12、《班次》添加时间段

### 国内万傲瑞达V6000 2.1.0 & 海外百傲瑞达V5000 4.1.0
1、数据库敏感字段加解密 -- 20210402-ljf
1.1、AttAutoExport：FTP密码 ftpPassword 30->250、邮件接收人 emailRecipients 255->500
1.2、AttTransaction：移动端签到地址 attPlace 50->250
2、图片加密功能 -- 20210527-ljf


V2.1.0======================
2019-04-24
1、ATT_TEMPSCH临时排班表新增字段PERS_PERSON_PIN，类型为字符串，用来解决人员离职时临时排班的离职人员信息为空的问题 --jinxian.huang

V2.2.0======================
2019-05-15
1.ATT_AUTOEXPORT表新增SEND_FORMAT（发送方式，String类型）,FTP_URL（FTP服务器地址，String类型）,FTP_PORT（FTP服务器端口，int类型）,
FTP_USERNAME（FTP登录账号，String类型）,FTP_PASSWORD（FTP登录密码，String类型）字段。－－jinxian.huang

2019-06-05
1.增加表ATT_BREAK_TIME 休息时间段;
2.修改表ATT_TIMESLOT时间段:
新增字段:
BEFORE_TO_WORK_MINUTES (Integer) 上班前n分钟内签到有效
AFTER_TO_WORK_MINUTES (Integer) 上班后n分钟内签到有效
BEFORE_OFF_WORK_MINUTES (Integer) 下班前n分钟内签到有效
AFTER_OFF_WORK_MINUTES (Integer) 下班后n分钟内签到有效
BEFORE_WORK_OVERTIME_MINUTES (Integer) 上班n分钟前签到记加班
MIN_BEFORE_OVERTIME_MINUTES (Integer) 上班前最短加班分钟数
AFTER_WORK_OVERTIME_MINUTES (Integer) 下班n分钟后开始记加班
MIN_AFTER_OVERTIME_MINUTES (Integer) 下班后最短加班分钟数
--ENABLE_WORKING_HOURS (String) 是否启动工作时长
舍弃字段:
PERIOD_NO 编号
END_SIGN_IN_TIME 结束签到时间 改用 AFTER_TO_WORK_MINUTES 上班后n分钟内签到有效
START_SIGN_OFF_TIME 开始签退时间 改用 BEFORE_OFF_WORK_MINUTES 下班前n分钟内签到有效
startSegmentTime 开始段间时间 改用休息时间段代替
endSegmentTime 结束段间时间 改用休息时间段代替
signInAdvanceTime 签到早于时间 改用 BEFORE_WORK_OVERTIME_MINUTES 上班n分钟前签到记加班
signOutPosponeTime 签退晚于时间 改用 AFTER_WORK_OVERTIME_MINUTES 下班n分钟后开始记加班
--by ljf

2019-06-11
1.修改表 ATT_TRANSACTION 原始记录
新增字段 ATT_PLACE(String 50) 签到地点;
-- by lxl

2019-06-12
1.新增表ATT_OVERTIME加班表的OVERTIME_LONG(加班时长,Integer类型)字段。——jinxian.huang

2019-06-13
1、时间段ATT_TIMESLOT新增字段：
ENABLE_FLEXIBLE_WORK (String 10) 是否启动弹性上班(true:启用。false：不启用)
ADVANCE_WORK_MINUTES (Integer) 可提前上班分钟数
DELAYED_WORK_MINUTES (Integer) 可延后上班分钟数
-- by gaoqi.lian

2019-06-13
1.新增ATT_SIGN_ADDRESS(APP签到地址)表,有ADDRESS(地址),LONGITUDE(经度),LATITUDE(纬度),
VALID_RANGE(范围距离(m:米))4个字段，类型皆为String类型。
2.新增ATT_SIGN_ADDRESS_AREA(签到地址-区域 关联)表,有SIGN_ADDRESS_ID(地址id)和AREA_ID(区域id)
两个字段,类型皆为String类型。－－jinxian.huang

2019-06-17
修改表ATT_SHIFT：增加“工作类型”字段，必填字段，字符串类型，默认为正常上班；

2019-07-24
1.新增表 ATT_CUSTOM_RULE 自定义规则;
-- by ljf

2019-11-01
1.修改表 ATT_TRANSACTION 原始记录
新增字段 DEVICE_NAME(String 50) 设备名称;
-- by ljf

2019-11-15
1.修改表 ATT_LEAVETYPE 假种
去掉 LEAVETYPE_SYMBOL 假种符号 字段

2019-12-02
1.新增表 ATT_CUSTOM_RULE_ORG 自定义规则使用对象 代替之前的自定义规则里的部门/分组;

2019-12-04
1.修改表 ATT_CUSTOM_RULE_ORG 自定义规则使用对象
修改字段 ORG_ID 部门或分组ID 字段类型文本为普通字符串类型 修复定义类型错误;
-- by ljf

Access简单考勤、国内3.0版本（对应考勤版本分别为2.3.0、2.3.1）===========
2020-2-18
1.修改假种初始化名称保存为Key，用于海外根据不同语言显示；

国内3.2.0版本====================
2020-03-31
1.表 ATT_SHIFT 班次
新增字段 PERIOD_START_MODE (String类型) 周期起始方式(0/按起始日期, 1/按排班日期)

V12.0.0版本====================
20200724
1.删除原始记录表ATT_TRANSACTION的唯一建att_tran_pin_datetime_idx
-- by lgq

20200730
1.假种类型表AttLeaveType新增字段
  最小单位-数值: Double CONVERT_COUNT
  最小单位-单位: String CONVERT_UNIT length = 20
  舍入控制: String CONVERT_TYPE length = 20
  报表展示符号: String SYMBOL length = 50
  标识: String MARK length = 20
-- by lgq

2020-08-03
1.修改表ATT_TIMESLOT 时间段:
  新增字段:
   MAX_BEFORE_OVERTIME_MINUTES (Integer) 上班前最大加班分钟数
   MAX_AFTER_OVERTIME_MINUTES (Integer) 下班后最大加班分钟数
-- by ljf


20200803
1.假种类型表AttLeaveType假种编号长度改成50
-- by lgq

2020-08-04
1.修改表 ATT_PERSON 考勤人员表:
新增字段:
VERIFY_MODE(Short) 验证方式 (数值参照考勤PUSH通讯协议-验证方式)

2020-10-23
1.修改表 ATT_TRANSACTION 原始记录表:
新增字段:
ATT_PHOTO_URL(String 255) 照片文件路径 (用来保存设备及第三方当考勤点推送过来的照片路径)

2020-12-07
1.修改表 ATT_TRANSACTION 原始记录表:
新增字段:
MASK_FLAG(String) 是否带口罩 0：表示没有佩戴口罩；1：表示有佩戴口罩
TEMPERATURE(String) 体温 摄氏度℃;

2020-12-22
1.修改表 ATT_RECORD 考勤结果记录表:
新增字段:
CARD_STATUS(String) 考勤卡点状态
CROSS_DAY(String) 跨天日期
