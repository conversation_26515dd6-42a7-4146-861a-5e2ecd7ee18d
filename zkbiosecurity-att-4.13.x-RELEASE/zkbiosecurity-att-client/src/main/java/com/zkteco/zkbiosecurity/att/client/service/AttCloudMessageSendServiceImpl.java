package com.zkteco.zkbiosecurity.att.client.service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import com.zkteco.zkbiosecurity.att.api.vo.AttApiDistanceDuration;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.api.vo.MessagePushCloudSendMessageItem;
import com.zkteco.zkbiosecurity.att.client.utils.AttUploadPageUtil;
import com.zkteco.zkbiosecurity.att.service.AttCloudMessageSendService;
import com.zkteco.zkbiosecurity.att.vo.AttRecordItem;
import com.zkteco.zkbiosecurity.att.vo.AttTransactionItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.license.provider.BaseLicenseProvider;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseClientService;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseService;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;

import lombok.extern.slf4j.Slf4j;

/**
 * 云平台数据推送
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/2 9:13
 * @since 1.0.0
 */
@Component
@Slf4j
public class AttCloudMessageSendServiceImpl implements AttCloudMessageSendService {

    @Autowired
    private BaseLicenseService baseLicenseService;
    @Autowired
    private BaseLicenseClientService baseLicenseClientService;
    @Autowired
    private BaseLicenseProvider baseLicenseProvider;

    @Override
    public void sendRuleToCloud(Map<String, String> params) {
        /*// 判断是否注册了云服务且是否激活了当前模块许可,才发送。
        if (StringUtils.isNotBlank(baseLicenseService.getAppId()) && isActiveLicense()) {
            try {
                AttApiDistanceDuration.AttCloudRuleItem attCloudRuleItem = new AttApiDistanceDuration.AttCloudRuleItem();
                // 组装消息体
                ZKMessage message = new ZKMessage();
                // ZkMessage 要传 appId by ljf 2019/03/01
                message.setAppId(baseLicenseService.getAppId());
                message.setModuleCode("att");
                message.setMessageId("attCloudRuleServiceImpl#saveCloudRule");
                message.setListContent(Collections.singletonList(attCloudRuleItem));
                // 发送消息
                // 修改发送信息方式 by ljf 2019/02/26
                baseLicenseClientService.sendMessage(message);
            } catch (Exception e) {
                // 捕获异常，避免事务回滚
                log.error("AttCloudMessageSendService sendRuleToCloud Error", e);
            }
        }*/
    }

    @Override
    public void asyncPushTransactionToCloud(List<AttTransactionItem> attTransactionItemList) {
        // 考勤不需要推送记录到云端，由云端主动来获取
        /*// 判断是否注册了云服务且是否激活了当前模块许可，才发送。
        if (StringUtils.isNotBlank(baseLicenseService.getAppId()) && isActiveLicense()
            && !CollectionUtil.isEmpty(attTransactionItemList)) {
            // 异步发送数据，避免kafka调用超时影响正常业务
            CompletableFuture.runAsync(() -> {
                // 对事件进行分组，30条一组
                List<List<AttTransactionItem>> attTransactionItemsList =
                    CollectionUtil.split(attTransactionItemList, AttUploadPageUtil.pageSize);
                ZKMessage zkMessage = new ZKMessage();
                // ZkMessage 要传 appId by ljf 2019/03/01
                zkMessage.setAppId(baseLicenseService.getAppId());
                // 平台分发处理 必须设置模块码和下列形式的消息id
                zkMessage.setModuleCode("att");
                zkMessage.setMessageId("attCloudTransactionHandleMessage#handleAttTransactionMessage"); // 发送消息到云平台。
                attTransactionItemsList.forEach(attTransactionItems -> {
                    zkMessage.setListContent(attTransactionItems);
                    // 修改发送信息方式 by ljf 2019/02/26
                    baseLicenseClientService.sendMessage(zkMessage);
                });
            });
        }*/
    }

    @Override
    public void asyncPushRecordToCloud(List<AttRecordItem> attRecordItemList) {
        // 考勤不需要推送记录到云端，由云端主动来获取
        /*// 判断是否注册了云服务且是否激活了当前模块许可，才发送。
        if (StringUtils.isNotBlank(baseLicenseService.getAppId()) && isActiveLicense()
            && !CollectionUtil.isEmpty(attRecordItemList)) {
            // 异步发送数据，避免kafka调用超时影响正常业务
            CompletableFuture.runAsync(() -> {
                // 对事件进行分组，30条一组
                List<List<AttRecordItem>> attRecordItemsList =
                    CollectionUtil.split(attRecordItemList, AttUploadPageUtil.pageSize);
                ZKMessage zkMessage = new ZKMessage();
                // ZkMessage 要传 appId by ljf 2019/03/01
                zkMessage.setAppId(baseLicenseService.getAppId());
                // 平台分发处理 必须设置模块码和下列形式的消息id
                zkMessage.setModuleCode("att");
                zkMessage.setMessageId("attCloudRecordHandleMessage#handleAttRecordMessage"); // 发送消息到云平台。
                attRecordItemsList.forEach(attRecordItems -> {
                    zkMessage.setListContent(attRecordItems);
                    // 修改发送信息方式 by ljf 2019/02/26
                    baseLicenseClientService.sendMessage(zkMessage);
                });
            });
        }*/
    }

    @Override
    public boolean isActiveLicense() {
        List<String> activeModuleCodeList = baseLicenseProvider.getActiveModuleLIist();
        if (activeModuleCodeList.contains(ConstUtil.SYSTEM_MODULE_ATT)) {
            return true;
        }
        return false;
    }

    @Override
    public void asyncPushTransactionWxMsgToCloud(List<MessagePushCloudSendMessageItem> messageItemList) {
        if (StringUtils.isNotBlank(baseLicenseService.getAppId()) && isActiveLicense()
            && !CollectionUtil.isEmpty(messageItemList)) {
            CompletableFuture.runAsync(() -> {
                List<List<MessagePushCloudSendMessageItem>> pageList =
                    CollectionUtil.split(messageItemList, AttUploadPageUtil.pageSize);
                ZKMessage zkMessage = new ZKMessage();
                zkMessage.setAppId(baseLicenseService.getAppId());
                zkMessage.setModuleCode("pers");
                zkMessage.setMessageId("messagePushHandleMessage#sendWxAndAppMessageHandle");
                pageList.forEach(items -> {
                    zkMessage.setListContent(items);
                    baseLicenseClientService.sendMessage(zkMessage);
                });

            });
        }
    }
}
