package com.zkteco.zkbiosecurity.att.client.service;

import com.zkteco.zkbiosecurity.att.client.utils.AttUploadPageUtil;
import com.zkteco.zkbiosecurity.att.service.AttCloudMessageSendService;
import com.zkteco.zkbiosecurity.att.service.AttDeviceService;
import com.zkteco.zkbiosecurity.att.vo.AttDeviceCloudItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseClientService;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseService;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
public class AttDeviceMessageUpload {
    @Autowired
    private AttDeviceService attDeviceService;
    @Autowired
    private BaseLicenseService baseLicenseService;
    @Autowired
    private BaseLicenseClientService baseLicenseClientService;
    @Autowired
    private AttCloudMessageSendService attCloudMessageSendService;

    @Scheduled(cron = "0 0 0/1 * * ?") // 每隔一分钟，业务可以自己控制
    public ZKResultMsg attDeviceMessageUpload() {
        // 判断是否注册了云服务且是否激活了当前模块许可,才发送。
        if (StringUtils.isNotBlank(baseLicenseService.getAppId()) && attCloudMessageSendService.isActiveLicense()) {

            Long pushTime = attDeviceService.getLastPushTime();

            ZKMessage zkMessage = new ZKMessage();
            // 上传要带appId by ljf 2019/03/06
            zkMessage.setAppId(baseLicenseService.getAppId());

            // 平台分发处理 必须设置模块码和下列形式的消息id
            zkMessage.setModuleCode("att");
            zkMessage.setMessageId("attCloudDeviceHandleMessage#handleAttDeviceMessage"); // 发送消息到云平台。

            if (pushTime == null) {

                Long count = attDeviceService.getAllDeviceCount();
                if (count > 0) {
                    int page = AttUploadPageUtil.getPage(count);
                    // 分批推送消息， 避免上传的内容太大占带宽。
                    for (int i = 0; i < page; i++) {
                        // 分页获取考勤设备信息并推送到云端
                        List<AttDeviceCloudItem> attDeviceCloudItemList = attDeviceService
                            .getDeviceCloudItems(new AttDeviceCloudItem(), i, AttUploadPageUtil.pageSize);
                        if (attDeviceCloudItemList != null && attDeviceCloudItemList.size() > 0) {
                            // 设置设备监控状态
                            for (AttDeviceCloudItem attDeviceCloudItem : attDeviceCloudItemList) {
                                attDeviceCloudItem
                                    .setOnlineStatus(attDeviceService.getDevStatus(attDeviceCloudItem.getDevSn()));
                            }
                            zkMessage.setListContent(attDeviceCloudItemList);
                            // 修改发送方式 by ljf 2019/03/06
                            baseLicenseClientService.sendMessage(zkMessage);
                        }

                    }
                }
            } else {
                Date lastUpdate = new Date(pushTime);
                lastUpdate = DateUtil.addMinute(lastUpdate, -1);
                List<AttDeviceCloudItem> devInfoList = attDeviceService.getUploadCloudDevicesByLastPushTime(lastUpdate);// 分页获取数据，限制消息体大小
                if (devInfoList != null && !devInfoList.isEmpty()) {
                    List<List<AttDeviceCloudItem>> devInfos =
                        CollectionUtil.split(devInfoList, AttUploadPageUtil.pageSize);
                    for (List<AttDeviceCloudItem> devInfo : devInfos) {
                        if (devInfo != null && devInfo.size() > 0) {
                            // 设置设备监控状态
                            for (AttDeviceCloudItem attDeviceCloudItem : devInfo) {
                                attDeviceCloudItem
                                    .setOnlineStatus(attDeviceService.getDevStatus(attDeviceCloudItem.getDevSn()));
                            }
                            zkMessage.setListContent(devInfo);
                            // kafka异步发送
                            baseLicenseClientService.sendMessage(zkMessage);
                        }

                    }
                }
            }
            attDeviceService.setLastPushTime();
        }
        return ZKResultMsg.successMsg();
    }
}
