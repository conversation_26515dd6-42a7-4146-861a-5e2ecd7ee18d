package com.zkteco.zkbiosecurity.att.client.service;

import com.alibaba.fastjson.JSONArray;
import com.zkteco.zkbiosecurity.att.service.AttPersonService;
import com.zkteco.zkbiosecurity.att.service.AttTransactionService;
import com.zkteco.zkbiosecurity.att.vo.AttPersonItem;
import com.zkteco.zkbiosecurity.att.vo.AttTransactionItem;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class AttTransactionMessageUpload {
    @Autowired
    private AttTransactionService attTransactionService;
    @Autowired
    private AttPersonService attPersonService;
    
    /**
     * 考勤签到接口(保存打卡事件)，云端调用保存，不能删除！!
     * 
     * @param message
     */
    public void saveTransactionItem(ZKMessage message) {
        List<String> attTransactionList = message.getListContent();
        if (attTransactionList != null && attTransactionList.size() > 0) {
            List<AttTransactionItem> attTransactionItems =
                JSONArray.parseArray(attTransactionList.toString(), AttTransactionItem.class);
            attTransactionItems.forEach(attTransactionItem -> {
                AttPersonItem attPersonItem = attPersonService.getItemByPersonPin(attTransactionItem.getPersonPin());
                if (attPersonItem != null
                    && attTransactionService.countByPersonPinAndAttDatetime(attTransactionItem.getPersonPin(),
                        attTransactionItem.getAttDatetime()) == 0) {
                    attTransactionItem.setDeptCode(attPersonItem.getDeptCode());
                    attTransactionItem.setDeptId(attPersonItem.getDeptId());
                    attTransactionItem.setDeptName(attPersonItem.getDeptName());
                    attTransactionService.saveTransactionItem(attTransactionItem);
                }
            });
        }
    }
}
