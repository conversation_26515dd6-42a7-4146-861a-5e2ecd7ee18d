package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 停车场当考勤、停车场区域对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 17:05
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class AttParkAreaItem extends BaseItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 进出口区域名称
     */
    private String name;

    /**
     * 车场区域名称
     */
    private String areaName;

    /**
     * 车场区域ID
     */
    private String areaId;
}
