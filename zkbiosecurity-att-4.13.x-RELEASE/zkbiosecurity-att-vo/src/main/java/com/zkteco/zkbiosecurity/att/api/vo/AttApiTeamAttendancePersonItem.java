package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class AttApiTeamAttendancePersonItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    private String personName;

    /**
     * 人员编号
     */
    private String personPin;

    /**
     * 职位名称
     */
    private String positionName;

    /**
     * 头像地址
     */
    private String avatarUrl;

    /**
     * 班次名称
     */
    private String shiftName;

    /**
     * 打卡数据
     */
    private String cardValidData;

    /**
     * 考勤结果状态（多状态）
     */
    private String attResultType;
}
