package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 考勤人员验证方式
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 17:03
 * @since 1.0.0
 */
@From(
    after = "ATT_PERSON ap LEFT JOIN PERS_PERSON pp ON ap.PERS_PERSON_PIN = pp.PIN LEFT JOIN AUTH_DEPARTMENT ad ON ad.ID = pp.AUTH_DEPT_ID")
@OrderBy(after = "pp.UPDATE_TIME DESC, pp.ID DESC")
@Setter
@Getter
@Accessors(chain = true)
@GridConfig(operate = true, idField = "id", winHeight = 250, winWidth = 420,
    operates = {@GridOperate(type = "edit", permission = "att:personVerifyMode:setting",
        url = "attPerson.do?editVerifyMode&pins=(personPin)&ids=(id)", label = "common_op_edit")})
public class AttPersonVerifyModeItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    @Column(name = "ap.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    @Column(name = "pp.ID")
    private String personId;

    /** 人员编号 */
    @Column(name = "pp.PIN")
    @GridColumn(label = "pers_person_pin", width = "150", encryptMode = "${pers.pin.encryptMode}",
        permission = "att:pin:encryptProp")
    private String personPin;

    /** 人员姓名 */
    @Column(name = "pp.NAME")
    @GridColumn(label = "pers_person_name", width = "150", encryptMode = "${pers.name.encryptMode}",
        permission = "att:name:encryptProp")
    private String personName;

    /** 人员 */
    @Column(name = "pp.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", showExpression = "#language!='zh_CN'", width = "150",
        encryptMode = "${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String personLastName;

    @Column(name = "ad.NAME")
    @GridColumn(label = "pers_dept_deptName", width = "150")
    private String deptName;

    /** 验证方式 */
    @Column(name = "ap.VERIFY_MODE")
    @GridColumn(label = "common_verifyMode_entiy", width = "150", columnType = "dic", key = "AttVerifyMode")
    private Short verifyMode;

    @Condition(value = "(lower(pp.NAME) LIKE ''%{0}%'' OR lower(pp.LAST_NAME) LIKE ''%{0}%'')")
    private String likeName;

    @Condition(
        value = "pp.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    private String attPersonIds;

    private String pins;

    public AttPersonVerifyModeItem() {
        super();
    }

    public AttPersonVerifyModeItem(String id) {
        super(true);
        this.id = id;
    }
}