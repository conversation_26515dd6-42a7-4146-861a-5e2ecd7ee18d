package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 日打卡详情
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 18:27 2020/6/28
 */
@From(after = "ATT_DAY_CARD_DETAIL t ")
@OrderBy(after = "t.ATT_DATE DESC")
@GridConfig(operate = true, operates = {@GridOperate(type = "custom", permission = "att:dayDetailReport",
    click = "attDayCardDetail", label = "att_statistical_dayCardDetail")})
@Setter
@Getter
@Accessors(chain = true)
public class AttDayCardDetailItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    @GridColumn(show = false)
    @Column(name = "t.ID")
    private String id;

    /**
     * 人员编号
     */
    @Column(name = "t.PERS_PERSON_PIN")
    @GridColumn(label = "att_person_pin", width = "120", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String personPin;

    /**
     * 姓名
     */
    @Column(name = "t.PERS_PERSON_NAME")
    @GridColumn(label = "att_person_name", width = "120", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String personName;

    /**
     * 英文（lastName）
     */
    @Column(name = "t.PERS_PERSON_LASTNAME")
    @GridColumn(label = "att_person_lastName", showExpression = "#language!='zh_CN'", width = "120", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String personLastName;

    /**
     * 部门编号
     */
    @Column(name = "t.AUTH_DEPT_ID")
    private String deptId;

    /**
     * 部门编号
     */
    @Column(name = "t.AUTH_DEPT_CODE")
    @GridColumn(label = "pers_dept_deptNo", width = "120", show = false)
    private String deptCode;

    /**
     * 部门名称
     */
    @Column(name = "t.AUTH_DEPT_NAME")
    @GridColumn(label = "pers_dept_deptName", width = "130")
    private String deptName;

    /**
     * 打卡日期
     */
    @Column(name = "t.ATT_DATE")
    @GridColumn(label = "att_statistical_cardDate", width = "120")
    private String attDate;

    /**
     * 打卡次数
     */
    @Column(name = "t.CARD_COUNT")
    @GridColumn(label = "att_statistical_cardNumber", width = "120")
    private Integer cardCount;

    /**
     * 最早时间
     */
    @Column(name = "t.EARLIEST_TIME")
    @GridColumn(label = "att_statistical_earliestTime", width = "120")
    private String earliestTime;

    /**
     * 最晚时间
     */
    @Column(name = "t.LATEST_TIME")
    @GridColumn(label = "att_statistical_latestTime", width = "120")
    private String latestTime;

    /**
     * 打卡时间
     */
    @Column(name = "t.ATT_TIMES")
    @GridColumn(label = "att_statistical_cardTime", width = "250")
    private String attTimes;

    @Condition(value = "t.ATT_DATE", equalTag = ">=")
    private String beginDate;

    @Condition(value = "t.ATT_DATE", equalTag = "<=")
    private String endDate;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "t.PERS_PERSON_PIN", equalTag = "in")
    private String inPersonPin;

    @Condition(value = "(t.PERS_PERSON_NAME LIKE ''%{0}%'' OR t.PERS_PERSON_LASTNAME LIKE ''%{0}%'')")
    private String likeName;

    // 是否包含下级
    private String isIncludeLower;

    /**
     * 解决grid最后一个字段宽度自适应
     */
    private String hiddenField;

    public AttDayCardDetailItem() {
        super();
    }

    public AttDayCardDetailItem(Boolean equals) {
        super(equals);
    }

    public AttDayCardDetailItem(String id) {
        super(true);
        this.id = id;
    }
}