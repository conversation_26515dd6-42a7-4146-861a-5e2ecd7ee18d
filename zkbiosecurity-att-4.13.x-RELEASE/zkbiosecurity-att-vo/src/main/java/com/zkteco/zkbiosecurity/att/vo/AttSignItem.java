package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 补签
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 17:01
 * @since 1.0.0
 */
@From(after = "ATT_SIGN t LEFT JOIN AUTH_DEPARTMENT ad ON t.AUTH_DEPT_ID = ad.ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600,
    operates = {@GridOperate(type = "del", permission = "att:sign:del", url = "attSign.do?del&pins=(personPin)",
        label = "common_op_del", filter = "attApplyShowDel")})
@Getter
@Setter
public class AttSignItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 人员id
     */
    @Column(name = "t.PERS_PERSON_ID")
    private String personId;

    /**
     * 人员编号
     */
    @Column(name = "t.PERS_PERSON_PIN")
    @GridColumn(label = "att_person_pin", width = "120", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String personPin;

    /**
     * 姓名
     */
    @GridColumn(label = "att_person_name", width = "120", sort = "na", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String personName;

    /**
     * 英文 lastName
     */
    @GridColumn(label = "att_person_lastName", showExpression = "#language!='zh_CN'", width = "120", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String personLastName;

    /**
     * 部门id
     */
    @Column(name = "t.AUTH_DEPT_ID")
    private String deptId;

    /**
     * 部门编号
     */
    @Column(name = "ad.CODE")
    @GridColumn(label = "att_common_deptNo", width = "120", sort = "na")
    private String deptCode;

    /**
     * 部门名称
     */
    @Column(name = "ad.NAME")
    @GridColumn(label = "att_common_deptName", width = "130", sort = "na")
    private String deptName;

    /**
     * 补签日期时间
     */
    @Column(name = "t.SIGN_DATETIME")
    @GridColumn(label = "att_sign_signTime", width = "150")
    private Date signDatetime;

    /**
     * 流程状态
     */
    @GridColumn(label = "common_status", width = "120", format = "0=att_approve_wait,1=att_exception_stop,2=att_apply_pass,3=att_apply_revoke,4=att_exception_delete,5=att_exception_refuse,6=att_exception_end")
    @Column(name = "t.FLOW_STATUS", equalTag = "=")
    private String flowStatus;

    @Column(name = "t.ATT_STATE")
    private String attState;

    /**
     * 备注
     */
    @Column(name = "t.REMARK")
    @GridColumn(label = "common_remark", width = "130")
    private String remark;

    /**
     * 操作日期时间
     */
    @Column(name = "t.CREATE_TIME")
    @GridColumn(label = "att_common_operateTime", width = "130")
    private Date operateDatetime;

    /**
     * 流程业务ID,全局唯一
     */
    @Column(name = "t.BUSINESS_KEY")
    private String businessKey;

    /**
     * 记录补签异常打卡
     */
    @Column(name = "t.BEFORE_SIGN_RECORD")
    private String beforeSignRecord;

    /**
     * 记录补签时间计算后结果
     */
    @Column(name = "t.AFTER_SIGN_RECORD")
    private String afterSignRecord;

    /**
     * 创建人
     */
    @Column(name = "t.CREATER_NAME")
    private String creatName;

    @Condition(value = "t.SIGN_DATETIME", equalTag = ">=")
    private Date startTime;

    @Condition(value = "t.SIGN_DATETIME", equalTag = "<=")
    private Date endTime;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "t.PERS_PERSON_ID", equalTag = "in")
    private String inPersonId;

    @Condition(value = "t.PERS_PERSON_PIN", equalTag = "in")
    private String inPersonPin;

    @Condition(value = "t.PERS_PERSON_PIN", equalTag = "=")
    private String personPinEq;

    /**
     * 开始补签时间范围
     */
    @Condition(value = "t.SIGN_DATETIME", equalTag = ">=")
    private Date startSignTime;

    /**
     * 结束补签时间范围
     */
    @Condition(value = "t.SIGN_DATETIME", equalTag = "<=")
    private Date endSignTime;

    /**
     * 流程状态
     */
    @Condition(value = "t.FLOW_STATUS", equalTag = "in")
    private String flowStatusIn;

    @Condition(
            value = "t.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    private String likeName;

    /**
     * 行数，导入用
     */
    private Integer rowNum;

    /** 是否包含下级 */
    private String isIncludeLower;

    /**
     * 申请编号
     */
    private String applyNo;
    /**
     * 申请类型
     */
    private String applyType;

    /**
     * 流程编号
     */
    private String flowNo;

    /** 知会人 */
    private String notifierPerIds;

    /** 任务ID */
    private String taskId;

    public AttSignItem() {
        super();
    }

    public AttSignItem(Boolean equals) {
        super(equals);
    }

    public AttSignItem(String id) {
        super(true);
        this.id = id;
    }

}
