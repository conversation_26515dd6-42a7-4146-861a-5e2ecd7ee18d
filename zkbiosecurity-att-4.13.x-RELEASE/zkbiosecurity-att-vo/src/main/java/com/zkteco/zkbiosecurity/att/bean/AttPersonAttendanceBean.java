package com.zkteco.zkbiosecurity.att.bean;

import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.att.calc.bo.AttLeaveBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttOvertimeBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;

import lombok.Getter;
import lombok.Setter;

/**
 * 考勤计算-人天班计算对象
 *
 * <AUTHOR>
 * @date 2020-06-19 11:37
 * @since 1.0.0
 */
@Getter
@Setter
public class AttPersonAttendanceBean {

    /**
     * pin=Date
     */
    private String pinAndDate;

    /**
     * 是否考勤（true:正常考勤/false:免打卡）
     */
    private Boolean isAttendance;

    /**
     * 每人每天对应的排班对象
     */
    private List<AttPersonSchBO> personSchDataMap;

    /**
     * 当天打卡记录集合(key：pin=date)
     */
    private Map<String, List<String>> transactionDataMap;

    /**
     * 请假申请集合
     * 
     */
    private List<AttLeaveBO> attLeaveBOList;

    /**
     * 加班申请集合
     * 
     */
    private List<AttOvertimeBO> attOvertimeBOList;

    /**
     * 考勤规则
     */
    private AttRuleParamBean attRuleParamBean;

    /**
     * 时间段集合
     */
    private Map<String, AttTimeSlotItem> attTimeSlotItemMap;
}
