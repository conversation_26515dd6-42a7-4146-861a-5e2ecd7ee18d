package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 2019/9/2 17:14
 */
@Getter
@Setter
public class AppAttExceptionItem implements Serializable {

    /**
     * 异常名称（leave:请假、trip:出差、out外出等）
     */
    private String exceptionType;

    /**
     * 异常名称（事假、病假、出差、外出等）
     */
    private String exceptionName;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 时长
     */
    private String timeLong;

    /* ------------ 补签异常数据字段 ----------- */
    /**
     * 补签时间
     */
    private String signTime;

    /**
     * 流程id
     */
    private String taskId;

    /**
     * 流程状态
     */
    private String taskStatus;
}
