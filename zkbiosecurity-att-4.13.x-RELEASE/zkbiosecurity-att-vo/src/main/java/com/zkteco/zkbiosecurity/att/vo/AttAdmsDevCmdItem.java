package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import com.zkteco.zkbiosecurity.base.annotation.Condition;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * 服务器下发命令
 *
 * <AUTHOR>
 * @date 2020年4月16日 下午6:05:51
 * @version V1.0
 */
@GridConfig(operate = false, idField = "id")
@Getter
@Setter
@Accessors(chain = true)
public class AttAdmsDevCmdItem extends BaseItem {
    /**
     *
     */
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @GridColumn(checkbox = false, width = "40", sortNo = 0, label = "ID", show = false)
    private String id;

    /** 指令ID */
    @GridColumn(label = "ID", width = "100")
    private Long cmdId;

    /** 设备序列号 */
    @GridColumn(label = "common_dev_sn", width = "140")
    private String sn;

    /** 命令内容 */
    @GridColumn(label = "adms_devCmd_content", width = "400", columnType = "txt")
    private String content;

    /** 紧急命令 */
    @GridColumn(label = "adms_devCmd_imme", width = "130", columnType = "custom", convert = "convertToIcon")
    private Boolean isImme;

    /** 提交时间 */
    @GridColumn(label = "adms_devCmd_submitTime", width = "145")
    private Date commitTime;

    /** 返回时间 */
    @GridColumn(label = "adms_devCmd_returnTime", width = "145")
    private Date returnTime;

    /** 返回值 */
    @GridColumn(label = "adms_devCmd_returnedValue", width = "140")
    private Integer returnValue;

    @GridColumn(label = "common_remark", columnType = "txt")
    private String remark;

    @GridColumn(label = "adms_devCmd_appName", show = false)
    private String appName;

    @GridColumn(label = "adms_devCmd_commType", show = false)
    private Short commType;

    @Condition(value = "t.COMMIT_TIME", equalTag = ">=")
    private Date submitStartTime;

    @Condition(value = "t.COMMIT_TIME", equalTag = "<=")
    private Date submitEndTime;

    @Condition(value = "t.RETURN_TIME", equalTag = ">=")
    private Date returnStartTime;

    @Condition(value = "t.RETURN_TIME", equalTag = "<=")
    private Date returnEndTime;
}
