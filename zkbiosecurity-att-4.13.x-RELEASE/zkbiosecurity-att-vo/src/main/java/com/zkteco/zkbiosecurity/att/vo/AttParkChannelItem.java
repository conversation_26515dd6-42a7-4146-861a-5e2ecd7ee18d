package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 停车场当考勤、停车场通道对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 17:05
 * @since 1.0.0
 */
@GridConfig(operate = false, idField = "id")
@Setter
@Getter
@Accessors(chain = true)
public class AttParkChannelItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /**
     * 通道名称
     */
    @Column(name = "t.NAME")
    @GridColumn(label = "park_channel_name", sortNo = 1, width = "150", columnType = "edit",
        editPermission = "park:channel:edit", editUrl = "parkChannel.do?edit")
    private String name;

    /**
     * 岗亭id
     */
    @Column(name = "pp.ID")
    private String pavilioId;

    /**
     * 岗亭名称
     */
    @Column(name = "pp.NAME")
    @GridColumn(label = "park_pavilio_name", sortNo = 2, width = "150")
    private String pavilioName;

    /**
     * 通道状态（1：大车场入口、2：大车场出口、3：小车场入口、4：小车场出口、5：中央缴费定点、6：中央缴费出口）
     */
    @Column(name = "t.STATE")
    @GridColumn(columnType = "dic", key = "parkChannelState", label = "park_channel_state", sortNo = 3, width = "150")
    private Short state;

    /**
     * 主设备ID
     */
    @Column(name = "(SELECT dev.ID FROM PARK_DEVICE dev WHERE dev.CHANNEL_ID = t.ID AND dev.PRIORITIES = 1)")
    private String devMaster;

    /**
     * 主设备名称
     */
    @Column(name = "(SELECT dev.NAME FROM PARK_DEVICE dev WHERE dev.CHANNEL_ID = t.ID AND dev.PRIORITIES = 1)")
    @GridColumn(label = "park_channel_IPC1_IP", sortNo = 4, width = "150")
    private String devMasterName;

    /**
     * 主设备显示位置
     */
    @Column(name = "(SELECT dev.AVOPTION FROM PARK_DEVICE dev WHERE dev.CHANNEL_ID = t.ID AND dev.PRIORITIES = 1)")
    @GridColumn(label = "park_channel_IPC1avoption", sortNo = 5, width = "150")
    private Short devMasterAvoption;

    /**
     * 从设备ID
     */
    @Column(name = "(SELECT dev.ID FROM PARK_DEVICE dev WHERE dev.CHANNEL_ID = t.ID AND dev.PRIORITIES = 2)")
    private String devSlave;

    /**
     * 从设备名称
     */
    @Column(name = "(SELECT dev.NAME FROM PARK_DEVICE dev WHERE dev.CHANNEL_ID = t.ID AND dev.PRIORITIES = 2)")
    @GridColumn(label = "park_channel_IPC2_IP", sortNo = 6, width = "150")
    private String devSlaveName;

    /**
     * 从设备显示位置
     */
    @Column(name = "(SELECT dev.AVOPTION FROM PARK_DEVICE dev WHERE dev.CHANNEL_ID = t.ID AND dev.PRIORITIES = 2)")
    @GridColumn(label = "park_channel_IPC2avoption", sortNo = 7, width = "150")
    private Short devSlaveAvoption;

    /**
     * 固定车开闸方式（1：确认放行，2：直接放行）
     */
    @Column(name = "t.MONTHCAR_OPEN_TYPE")
    @GridColumn(label = "park_channel_monthCarOpenType", sortNo = 8, width = "150",
        format = "1=park_channel_carOpenTypeConfirm,2=park_channel_carOpenTypeDirect")
    private Short monthCarOpenType;

    /**
     * 临时车开闸方式（1：确认放行，2：直接放行）
     */
    @Column(name = "t.TEMPCAR_OPEN_TYPE")
    @GridColumn(label = "park_channel_tempCarOpenType", sortNo = 9, width = "150",
        format = "1=park_channel_carOpenTypeConfirm,2=park_channel_carOpenTypeDirect")
    private Short tempCarOpenType;

    /**
     * 0：禁用，1：开启(默认是禁用)
     */
    @Column(name = "t.LIMIT_MODE")
    private Short limitMode;

    /**
     * 限行模式禁止通行车辆类型
     */
    @Column(name = "t.RESTRICT_VEHICLETYPE")
    private String restrictVehicleType;

    /**
     * 开闸机号
     */
    @Column(name = "t.OPENGAGE_DEVICEID")
    private Short opengageDeviceid;

    /**
     * 串口号
     */
    @Column(name = "t.PORT")
    private String port;

    /**
     * 卡机类型（1：出卡机、2：吞卡机）
     */
    @Column(name = "t.CARD_DEVICE_TYPE")
    private Short cardDeviceType;

    /**
     * 图片抓拍方式（1：刷卡、2：地感、3：抓人脸）
     */
    @Column(name = "t.PICTURE_CAPTURE_MODE")
    private Short pictureCaptureMode;

    public AttParkChannelItem() {
        super();
    }

    public AttParkChannelItem(Boolean equals) {
        super(equals);
    }

    public AttParkChannelItem(String id) {
        super(true);
        this.id = id;
    }
}
