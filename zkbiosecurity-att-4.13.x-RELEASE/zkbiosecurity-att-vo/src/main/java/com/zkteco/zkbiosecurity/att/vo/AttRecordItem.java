package com.zkteco.zkbiosecurity.att.vo;

import java.math.BigDecimal;
import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 考勤计算结果、日明细表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:57
 * @since 1.0.0
 */
@From(after = "ATT_RECORD t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600, operates = {
    @GridOperate(type = "edit", permission = "att:record:edit", url = "attRecord.do?edit", label = "common_op_edit"),
    @GridOperate(type = "del", permission = "att:record:del", url = "attRecord.do?delete", label = "common_op_del")})
@Setter
@Getter
@Accessors(chain = true)
public class AttRecordItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 部门id
     */
    @Column(name = "t.AUTH_DEPT_ID")
    private String deptId;

    /**
     * 分组ID
     */
    private String groupId;

    /**
     * 部门编号
     */
    @Column(name = "t.AUTH_DEPT_CODE")
    @GridColumn(label = "att_common_dept", secHeader = "common_number")
    private String deptCode;

    /**
     * 部门名称
     */
    @Column(name = "t.AUTH_DEPT_NAME")
    @GridColumn(label = "#cspan", secHeader = "common_name")
    private String deptName;

    /**
     * 人员编号
     */
    @Column(name = "t.PERS_PERSON_PIN")
    @GridColumn(label = "att_common_person", secHeader = "att_common_pin", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String personPin;

    /**
     * 姓名
     */
    @Column(name = "t.PERS_PERSON_NAME")
    @GridColumn(label = "#cspan", secHeader = "att_person_name", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String personName;

    /**
     * 英文（lastName）
     */
    @Column(name = "t.PERS_PERSON_LAST_NAME")
    @GridColumn(label = "#cspan", secHeader = "att_person_lastName", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String personLastName;

    /**
     * 考勤日期
     */
    @Column(name = "t.ATT_DATE")
    @GridColumn(label = "att_statistical_attDate")
    private Date attDate;

    /**
     * 星期
     */
    @Column(name = "t.WEEK")
    @GridColumn(label = "att_statistical_week")
    private String week;

    /**
     * 班次编号
     */
    @Column(name = "t.SHIFT_NO")
    @GridColumn(label = "att_statistical_shiftInfo", secHeader = "common_number")
    private String shiftNo;

    /**
     * 班次名称
     */
    @Column(name = "t.SHIFT_NAME")
    @GridColumn(label = "#cspan", secHeader = "common_name")
    private String shiftName;

    /**
     * 时间段集合，多个以逗号隔开
     */
    @Column(name = "t.TIMESLOT_NAME")
    @GridColumn(label = "#cspan", secHeader = "common_name")
    private String timeSlotName;

    /**
     * 班次时间数据
     */
    @Column(name = "t.SHIFT_TIME_DATA")
    @GridColumn(label = "#cspan", secHeader = "att_statistical_shiftTimeData")
    private String shiftTimeData;

    /**
     * 有效打卡数据
     */
    @Column(name = "t.CARD_VALID_DATA")
    @GridColumn(label = "att_statistical_cardValidData")
    private String cardValidData;

    /**
     * 有效打卡次数
     */
    @Column(name = "t.CARD_VALID_COUNT")
    @GridColumn(label = "att_statistical_cardValidCount")
    private Integer cardValidCount;

    /**
     * 应出勤分钟数
     */
    @Column(name = "t.SHOULD_MINUTE")
    @GridColumn(label = "att_common_attendance(common_minute)", secHeader = "att_statistical_should")
    private Integer shouldMinute;

    /**
     * 应出勤分钟数辅助计算字段(处理休息加班、节假日加班，且加班换算为天的时候，应上如果置为0，换算出来的加班时长也为0)
     */
    @Column(name = "t.SHOULD_MINUTE_EX")
    private Integer shouldMinuteEx;

    /**
     * 实际出勤分钟数
     */
    @Column(name = "t.ACTUAL_MINUTE")
    @GridColumn(label = "#cspan", secHeader = "att_statistical_actual")
    private Integer actualMinute;

    /**
     * 有效出勤分钟数
     */
    @Column(name = "t.VALID_MINUTE")
    @GridColumn(label = "#cspan", secHeader = "att_statistical_valid")
    private Integer validMinute;

    /**
     * 迟到次数数据
     */
    @Column(name = "t.LATE_COUNT_DATA")
    @GridColumn(label = "att_statistical_lateCount", secHeader = "att_statistical_countData")
    private String lateCountData;
    /**
     * 迟到总次数
     */
    @Column(name = "t.LATE_COUNT_TOTAL")
    @GridColumn(label = "#cspan", secHeader = "att_statistical_total")
    private Integer lateCountTotal;

    /**
     * 迟到分钟数数据
     */
    @Column(name = "t.LATE_MINUTE_DATA")
    @GridColumn(label = "att_statistical_lateMinute", secHeader = "att_statistical_minuteData")
    private String lateMinuteData;
    /**
     * 迟到总分钟数
     */
    @Column(name = "t.LATE_MINUTE_TOTAL")
    @GridColumn(label = "#cspan", secHeader = "att_statistical_total")
    private Integer lateMinuteTotal;

    /**
     * 早退次数数据
     */
    @Column(name = "t.EARLY_COUNT_DATA")
    @GridColumn(label = "att_statistical_earlyCount", secHeader = "att_record_earlyCountData")
    private String earlyCountData;
    /**
     * 早退总次数
     */
    @Column(name = "t.EARLY_COUNT_TOTAL")
    @GridColumn(label = "#cspan", secHeader = "att_statistical_total")
    private Integer earlyCountTotal;

    /**
     * 早退分钟数数据
     */
    @Column(name = "t.EARLY_MINUTE_DATA")
    @GridColumn(label = "att_statistical_earlyMinute", secHeader = "att_record_earlyMinuteData")
    private String earlyMinuteData;
    /**
     * 早退总分钟数
     */
    @Column(name = "t.EARLY_MINUTE_TOTAL")
    @GridColumn(label = "#cspan", secHeader = "att_statistical_total")
    private Integer earlyMinuteTotal;

    /**
     * 旷工分钟数
     */
    @Column(name = "t.ABSENT_MINUTE")
    @GridColumn(label = "att_record_absentMinute")
    private Integer absentMinute;

    /**
     * 加班分钟数-平时
     */
    @Column(name = "t.OVERTIME_USUAL_MINUTE")
    @GridColumn(label = "att_record_overtimeUsualMinute")
    private Integer overtimeUsualMinute;

    /**
     * 加班分钟数-休息
     */
    @Column(name = "t.OVERTIME_REST_MINUTE")
    @GridColumn(label = "att_record_overtimeRestMinute")
    private Integer overtimeRestMinute;

    /**
     * 加班分钟数-节日
     */
    @Column(name = "t.OVERTIME_HOLIDAY_MINUTE")
    @GridColumn(label = "att_record_overtimeHolidayMinute")
    private Integer overtimeHolidayMinute;

    /**
     * 加班分钟数
     */
    @Column(name = "t.OVERTIME_MINUTE")
    @GridColumn(label = "att_record_overtimeMinute")
    private Integer overtimeMinute;

    /**
     * 请假分钟数
     */
    @Column(name = "t.LEAVE_MINUTE")
    @GridColumn(label = "att_record_leaveMinute")
    private Integer leaveMinute;

    /**
     * 出差分钟数
     */
    @Column(name = "t.TRIP_MINUTE")
    @GridColumn(label = "att_record_tripMinute")
    private Integer tripMinute;

    /**
     * 外出分钟数
     */
    @Column(name = "t.OUT_MINUTE")
    @GridColumn(label = "att_record_outMinute")
    private Integer outMinute;

    /**
     * 应出勤天数
     */
    @Column(name = "t.SHOULD_DAYS")
    @GridColumn(label = "att_record_shouldDays")
    private BigDecimal shouldDays;

    /**
     * 实际出勤天数
     */
    @Column(name = "t.ACTUAL_DAYS")
    @GridColumn(label = "att_record_actualDays")
    private BigDecimal actualDays;

    /**
     * 有效出勤天数
     */
    @Column(name = "t.VALID_DAYS")
    @GridColumn(label = "att_record_validDays")
    private BigDecimal validDays;

    /**
     * 旷工天数
     */
    @Column(name = "t.ABSENT_DAYS")
    @GridColumn(label = "att_record_absentDays")
    private BigDecimal absentDays;

    /**
     * 请假天数
     */
    @Column(name = "t.LEAVE_DAYS")
    @GridColumn(label = "att_record_leaveDays")
    private BigDecimal leaveDays;

    /**
     * 出差天数
     */
    @Column(name = "t.TRIP_DAYS")
    @GridColumn(label = "att_record_tripDays")
    private BigDecimal tripDays;

    /**
     * 外出天数
     */
    @Column(name = "t.OUT_DAYS")
    @GridColumn(label = "att_record_outDays")
    private BigDecimal outDays;

    /**
     * 异常排班类型
     */
    @Deprecated
    @Column(name = "t.EXCEPTION_SCH_TYPE")
    @GridColumn(label = "att_record_exceptionSchType")
    private Integer exceptionSchType;

    /** 考勤状态 */
    @Column(name = "t.ATTENDANCE_STATUS")
    private String attendanceStatus;

    /** 考勤单状态查询 */
    @Condition(value = "(t.ATTENDANCE_STATUS LIKE ''{0}%'' OR t.ATTENDANCE_STATUS LIKE ''%,{0}%'')")
    private String attendanceStatusSingle;

    /** 漏卡状态查询 */
    @Condition(value = "{0}")
    private String leakageStatus;

    /**
     * 备注
     */
    @Column(name = "t.REMARK")
    @GridColumn(label = "att_record_remark")
    private String remark;

    @Condition(value = "t.PERS_PERSON_PIN", equalTag = "in")
    private String inPersonPin;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "t.ATT_DATE", equalTag = ">=")
    private Date startDate;
    @Condition(value = "t.ATT_DATE", equalTag = "<=")
    private Date endDate;

    //推送微信 1=推送 2=不需要
    @Column(name = "t.push_wechat_flag")
    private String pushWechatFlag;

    /**
     * 请假详情
     * <p>
     * 保存各请假类型对应时长，格式:code-时长(分钟)，多个以逗号隔开
     * </p>
     */
    @Column(name = "t.LEAVE_DETAILS")
    private String leaveDetails;

    /**
     * 卡点状态
     */
    @Column(name = "t.CARD_STATUS")
    private String cardStatus;

    /**
     * 跨天日期
     */
    @Column(name = "t.CROSS_DAY")
    private String crossDay;

    /**
     * 默认构造方法
     */
    public AttRecordItem() {}

    /**
     * 构造方法
     */
    public AttRecordItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AttRecordItem(String id) {
        super(true);
        this.id = id;
    }

}