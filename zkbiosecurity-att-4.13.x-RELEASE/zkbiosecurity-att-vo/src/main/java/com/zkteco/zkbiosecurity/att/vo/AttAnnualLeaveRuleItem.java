package com.zkteco.zkbiosecurity.att.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 年休假时长规则
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 9:49 2020/9/29
 * @version v1.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class AttAnnualLeaveRuleItem {

    /**
     * 工龄大于
     */
    private String startYear;

    /**
     * 工龄小于
     */
    private String endYear;

    /**
     * 年假天数
     */
    private String days;
}
