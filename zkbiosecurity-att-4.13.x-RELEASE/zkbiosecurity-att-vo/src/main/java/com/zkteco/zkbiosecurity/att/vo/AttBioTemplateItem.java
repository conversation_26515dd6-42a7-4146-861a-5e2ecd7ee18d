package com.zkteco.zkbiosecurity.att.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * 生物模板统一模板结构
 *
 * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
 * @version V1.0
 * @date Created In 9:50 2018/11/8
 */
@Setter
@Getter
public class AttBioTemplateItem {

    /** 人员编号 */
    private String personPin;
    /** 生物特征类型0：通用的1：指纹2：面部(近红外人脸)3：声纹4：虹膜5：视网膜6：掌纹7：指静脉 8：掌静脉 9：可见光人脸 */
    private Short bioType;
    /** 生物特征模板编号 */
    private Short templateNo;
    /** 生物特征模板对应索引 */
    private Short templateNoIndex;
    /** 生物特征是否有效 */
    private Short validType;
    /** 生物特征版本 */
    private String version;
    /** 生物特征模板内容 */
    private String template;
    /** 是否胁迫 */
    private Boolean duress;
    /** 下发类型，0：base64 1：url */
    private Short format;
    /** 生物图像URL地址 */
    private String url;
}
