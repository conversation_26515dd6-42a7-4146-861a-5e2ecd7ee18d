package com.zkteco.zkbiosecurity.att.api.msg.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 改变消息状态VO
 * 
 * <AUTHOR> href:"mailto:<EMAIL>">zbx.zhong</a>
 * @version v1.0
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel
public class AppMessageStateItem implements Serializable {
    /* 消息ID */
    @ApiModelProperty(value = "消息id,多个消息id以逗号隔开", example = "")
    private String id;
    /* 关联百傲瑞达v5000 id */
    @ApiModelProperty(value = "appId", example = "1234")
    private String appId;
}
