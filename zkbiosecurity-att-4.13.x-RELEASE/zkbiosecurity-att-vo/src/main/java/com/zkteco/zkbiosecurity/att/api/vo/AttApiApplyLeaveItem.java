package com.zkteco.zkbiosecurity.att.api.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 申请请假
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 16:48 2021/11/15
 * @version v1.0
 */
@Setter
@Getter
@Accessors(chain = true)
@ApiModel(description = "AttApiApplyLeaveItem")
public class AttApiApplyLeaveItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 人员编号 */
    @ApiModelProperty(value = "人员编号", required = true, example = "'1'")
    private String personPin;

    /** 假种编号 */
    @ApiModelProperty(value = "假种编号（详见/api/attApply/getLeaveTypeList）", required = true, example = "L1")
    private String leaveTypeNo;

    /** 开始时间 */
    @ApiModelProperty(value = "开始时间", required = true, example = "2021-11-19 13:00:00")
    private String startDatetime;

    /** 结束时间 */
    @ApiModelProperty(value = "结束时间", required = true, example = "2021-11-19 18:00:00")
    private String endDatetime;

    /** 请假照片 */
    @ApiModelProperty(value = "请假照片路径（详见/api/attApply/uploadLeaveImage）", example = "")
    private String leaveImagePath;

    /** 备注 */
    @ApiModelProperty(value = "备注", example = "api")
    private String remark;
}
