package com.zkteco.zkbiosecurity.att.constants;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

/**
 * 考勤班次常量类
 * <p>
 * 定义相关班次常量，命名以Att开头，Constant结尾
 * 
 * <AUTHOR>
 * @version 1.0.0.0
 * @since 2017年11月7日 上午11:34:00
 */
public abstract class AttShiftConstant {
    /* -------------工作类型(正常工作/周末加班/节假日加班)-------------*/
    /** 班次工作类型-正常工作 */
    public static final String SHIFT_WORKTYPE_NORMALWORK = "normalWork";
    /** 班次工作类型-周末加班 */
    public static final String SHIFT_WORKTYPE_WEEKENDOT = "weekendOt";
    /** 班次工作类型-节假日加班 */
    public static final String SHIFT_WORKTYPE_HOLIDAYOT = "holidayOt";

    /**
     * 班次类型（0/规律班次，1/弹性班次）
     */
    public static final class ShiftType {
        /** 规律班次 */
        public static final short REGULAR_SHIFT = 0;
        /** 弹性班次 */
        public static final short FLEXIBLE_SHIFT = 1;
    }

    /**
     * 周期单位（0/天，1/周，2/月）
     */
    public static final class CycleUnit {
        /** 天 */
        public static final short DAY = 0;

        /** 周 */
        public static final short WEEK = 1;

        /** 月 */
        public static final short MONTH = 2;
    }

    /**
     * 考勤方式（0/按班次正常刷卡，1/一天内任刷一次有效卡，2/只计算刷卡时间，3/免刷卡）
     */
    public static final class AttendanceMode {
        /** 按班次正常刷卡 */
        public static final short SHIFT_NORMAL = 0;
        /** 一天内任刷一次有效卡 */
        public static final short ONE_DAY_ONE_CARD = 1;
        /** 只计算刷卡时间 */
        public static final short ONLY_BRUSH_TIME = 2;
        /** 免刷卡 */
        public static final short NOT_BRUSH_CARD = 3;
    }

    /**
     * 加班方式（0/电脑自动计算，1/加班必须申请，2/必须加班否则旷工，3/时长为较小者-延后加班和加班单对比，4/不算加班）
     */
    public static final class OvertimeMode {
        /** 电脑自动计算 */
        public static final short AUTO_CALC = 0;
        /** 加班必须申请 */
        public static final short MUST_APPLY = 1;
        /** 必须加班否则旷工 */
        public static final short MUST_OVERTIME = 2;
        /** 时长为较小者-延后加班和加班单对比 */
        public static final short TIME_SMALLER = 3;
        /** 不算加班 */
        public static final short NOT_BRUSH_CARD = 4;
    }

    /**
     * 加班标记（0/平时，1/休息日，2/节假日）
     */
    public static final class OvertimeSign {
        /** 平时 */
        public static final short NORMAL = 0;
        /** 休息日 */
        public static final short REST = 1;
        /** 节假日 */
        public static final short HOLIDAY = 2;
    }

    /** 加班类型对应国际化 */
    public static final Map<String, String> ATT_OVERTIME_TYPE;
    static {
        Map<String, String> map = new HashMap<>();
        map.put("0", I18nUtil.i18nCode("att_overtime_normal"));
        map.put("1", I18nUtil.i18nCode("att_overtime_rest"));
        map.put("2", I18nUtil.i18nCode("att_shift_holidayOt"));
        ATT_OVERTIME_TYPE = Collections.unmodifiableMap(map);
    }
}
