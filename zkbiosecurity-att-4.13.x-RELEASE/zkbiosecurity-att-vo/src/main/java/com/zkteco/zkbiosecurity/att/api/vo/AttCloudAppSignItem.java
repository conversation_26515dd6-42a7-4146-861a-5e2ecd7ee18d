package com.zkteco.zkbiosecurity.att.api.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class AttCloudAppSignItem implements Serializable {

    /**
     * 人员编号
     */
    private String personPin;

    /**
     * 姓名
     */
    private String personName;

    /**
     * 部门编号
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 补签日期时间
     */
    private String signDate;

    /**
     * 补签日期时间
     */
    private String signDatetime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 英文（lastName）
     */
    private String personLastName;

    /** 知会人 */
    private String notifierPins;

    /**
     * 记录补签异常打卡
     */
    private String beforeSignRecord;

    /**
     * 记录补签时间计算后结果
     */
    private String afterSignRecord;

    /**
     * 补签时间
     */
    private String signTime;

    /**
     * 流程任务ID
     */
    private String taskId;

    /**
     * 流程状态
     */
    private String taskStatus;
}
