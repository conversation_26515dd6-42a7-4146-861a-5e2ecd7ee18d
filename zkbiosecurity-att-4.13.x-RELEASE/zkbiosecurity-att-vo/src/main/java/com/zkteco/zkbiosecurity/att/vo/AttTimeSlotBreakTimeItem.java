package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;

/**
 * 时间段的休息时间段
 * 
 * <AUTHOR>
 * @date 2019/6/3
 */
@From(
    after = "ATT_TIMESLOT_BREAKTIME t LEFT JOIN  ATT_TIMESLOT a ON t.TIMESLOT_ID = a.ID LEFT JOIN ATT_BREAK_TIME b ON t.BREAKTIME_ID = b.ID")
@GridConfig
@Setter
@Getter
public class AttTimeSlotBreakTimeItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    @Column(name = "t.BREAKTIME_ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    @Column(name = "b.name")
    @GridColumn(label = "common_name", width = "120")
    private String name;

    @Column(name = "b.START_TIME")
    @GridColumn(label = "att_breakTime_startTime", width = "120")
    private String startTime;

    @Column(name = "b.END_TIME")
    @GridColumn(label = "att_breakTime_endTime", width = "120")
    private String endTime;

    @Condition(value = "t.TIMESLOT_ID", equalTag = "=")
    private String timeSlotId;
}
