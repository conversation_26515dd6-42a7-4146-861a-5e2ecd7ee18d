package com.zkteco.zkbiosecurity.att.bean;

import java.util.Date;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * dx 日历控件事件对象bean，用于新增接收和数据展示
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 15:31 2020/5/7
 */
@Getter
@Setter
@NoArgsConstructor
public class AttDXCalendarEventBean {

    private Date start_date;

    private Date end_date;

    private String text;

    private String attTimeSlotIds;

    /**
     * 前端控件如果为false需要减一天
     */
    private boolean _timed = true;
}
