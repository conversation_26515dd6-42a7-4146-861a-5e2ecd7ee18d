package com.zkteco.zkbiosecurity.att.calc.bo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
 * @version V1.0
 * @date Created In 9:30 2018/7/9
 */
@Setter
@Getter
@ToString
public class AttTimeSlotBO implements Serializable {

    /**
     * 是否跨天 "0"当前 "1"跨天
     */
    private String isInterDay;
    /**
     * 跨天的第一天日期 yyyy-MM-dd
     */
    private String firstDay;
    /**
     * 跨天的第二天日期 yyyy-MM-dd
     */
    private String secondDay;
    /**
     * 时间段ID主键
     */
    private String attTimeSlotId;
    /**
     * 上班时间
     */
    private String toWorkTime;
    /**
     * 下班时间
     */
    private String offWorkTime;
    /**
     * 两两打卡累积时长，打卡记录
     */
    private List<String> elasticWorkTimeArray;

    /**
     * 早退次数
     */
    private Integer earlyCount;
    /**
     * 早退分钟数数据
     */
    private Integer earlyMinute;

    /**
     * 迟到次数
     */
    private Integer lateCount;
    /**
     * 迟到分钟数
     */
    private Integer lateMinute;

    /**
     * 旷工次数
     */
    @Deprecated
    private Integer absentCount;
    /**
     * 旷工分钟数为该时间段的工作时长-迟到分钟数-早退分钟数
     */
    private Integer absentMinute;

    /**
     * 加班次数
     */
    @Deprecated
    private Integer overtimeCount;
    /**
     * 加班分钟数
     */
    private Integer overtimeMinute;
    /**
     * 加班分钟数-平时
     */
    private Integer overtimeUsualMinute;

    /**
     * 加班分钟数-休息
     */
    private Integer overtimeRestMinute;
    /**
     * 加班分钟数-节日
     */
    private Integer overtimeHolidayMinute;
    /**
     * 加班标记（平时，休息日，节假日）
     */
    private Integer overtimeSign;

    /**
     * 请假次数
     */
    @Deprecated
    private Integer leaveCount;
    /**
     * 请假分钟数
     */
    private Integer leaveMinute;
    /**
     * 请假是否扣上班时长
     */
    private Integer leaveDeductWorkLong;

    /**
     * 出差次数
     */
    @Deprecated
    private Integer tripCount;
    /**
     * 出差分钟数
     */
    private Integer tripMinute;

    /**
     * 外出次数
     */
    @Deprecated
    private Integer outCount;
    /**
     * 外出分钟数
     */
    private Integer outMinute;

    /**
     * 【弹性上班】提前或延后上班分钟数(默认值0)
     */
    private Integer flexibleTime;

    /**
     * 异常时间段（保存顺序：出差，外出，请假）
     * <p>
     * 保存当天所有异常的开始和结束 格式:开始时间-结束时间，多个以豆号隔开（yyyy-MM-dd HH:mm:ss）
     * </p>
     */
    private String exceptionTime;

    /**
     * 异常类型时长集合(编号,时长)
     * 
     */
    private Map<String, Integer> exceptionTimeLongMap;

    /**
     * 是否未签到未签退被记为不完整(是:true,否:false)
     */
    private Boolean isInComplete;

    /** 上班卡点状态 */
    private String toWorkTimeStatus;

    /** 下班卡点状态 */
    private String offWorkTimeStatus;

    /**
     * 构造方法给参数默认值
     */
    public AttTimeSlotBO() {
        this.earlyCount = 0;
        this.earlyMinute = 0;
        this.lateCount = 0;
        this.lateMinute = 0;
        this.absentCount = 0;
        this.absentMinute = 0;
        this.overtimeCount = 0;
        this.overtimeMinute = 0;
        this.overtimeUsualMinute = 0;
        this.overtimeRestMinute = 0;
        this.overtimeHolidayMinute = 0;
        this.overtimeSign = 0;
        this.leaveCount = 0;
        this.leaveMinute = 0;
        this.leaveDeductWorkLong = 0;
        this.tripCount = 0;
        this.tripMinute = 0;
        this.outCount = 0;
        this.outMinute = 0;
        this.flexibleTime = 0;
    }
}
