package com.zkteco.zkbiosecurity.att.api.vo;

import com.zkteco.zkbiosecurity.att.vo.AttDeviceItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 设备-VO
 * 
 * @Auther: lambert.li
 * @Date: 2018/11/7 18:01
 */
@Getter
@Setter
@Accessors(chain = true)
public class AttApiDeviceItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /* 设备ID */
    private String id;

    /* 设备SN */
    private String sn;

    /* 设备名称 */
    private String name;

    /* 设备类型 */
    private String type;

    /* 设备状态 */
    private String status;

    /* 设备模块 */
    private String module;

    public static AttApiDeviceItem createApiDevice(AttDeviceItem attDeviceItem) {
        AttApiDeviceItem attApiDeviceItem = null;
        if (attDeviceItem != null) {
            attApiDeviceItem = new AttApiDeviceItem();
            attApiDeviceItem.setId(attDeviceItem.getId());
            attApiDeviceItem.setSn(attDeviceItem.getDevSn());
            attApiDeviceItem.setName(attDeviceItem.getDevName());
            attApiDeviceItem.setType(attDeviceItem.getDevModel());
            attApiDeviceItem.setStatus(attDeviceItem.getStatus() ? "1" : "0");
            attApiDeviceItem.setModule("att");
        }
        return attApiDeviceItem;
    }
}
