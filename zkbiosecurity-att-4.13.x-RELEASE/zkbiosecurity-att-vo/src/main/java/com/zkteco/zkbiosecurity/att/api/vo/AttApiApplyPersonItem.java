package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 2019/3/5 15:22
 */
@Getter
@Setter
@Accessors(chain = true)
public class AttApiApplyPersonItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 人员编号 */
    private String id;

    /** 人员编号 */
    private String pin;

    /** 审批排序 */
    private Integer sortNo;

    /** 人员姓名 */
    private String name;

    /** 人员头像 */
    private String avatarUrl;

    /** 审批结果：0 审批通过 1 未审批 2 已驳回 */
    private String auditResult;

    /** 审批时间 */
    private String auditTime;

    /** 审批备注 */
    private String remark;
}
