package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 假期结余
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 15:30 2020/11/17
 * @version v1.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class AttApiLaveHolidayItem {

    /** 是否是假期结余 */
    private String isLaveHoliday;

    /** 享有数 */
    private String total = "0";

    /** 等待审核数 */
    private String approvedDay = "0";

    /** 已用时间 */
    private String useDay = "0";

    /** 结余数 */
    private String lessDay = "0";

    /** 单位（Day/Hours) */
    private String timeUnit = "Day";

    /** 单位名称(天/时） */
    private String timeUnitName = "0";

    /** 有效开始时间 */
    private String startDate;

    /** 有效结束时间 */
    private String endDate;

    /** 入职日期 */
    private Date hireDate;
}
