package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 考勤点
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:50
 * @since 1.0.0
 */
@From(after = "ATT_POINT t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 300, winWidth = 420,
    operates = {
        @GridOperate(type = "edit", permission = "att:point:edit", url = "attPoint.do?edit", label = "common_op_edit"),
        @GridOperate(type = "del", permission = "att:point:del", url = "attPoint.do?del", label = "common_op_del")})
@Setter
@Getter
@Accessors(chain = true)
public class AttPointItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 考勤点名称
     */
    @Column(name = "t.POINT_NAME")
    @GridColumn(label = "att_attPoint_name", width = "180", sort = "na")
    private String pointName;

    /**
     * 区域名称
     */
    @GridColumn(label = "base_area_name", width = "150", sort = "na")
    private String areaName;

    /**
     * 设备名称
     */
    @GridColumn(label = "common_dev_name", width = "150", sort = "na")
    private String deviceName;

    /**
     * 设备模块
     */
    @Column(name = "t.DEVICE_MODULE")
    @GridColumn(label = "att_attPoint_deviceModule", width = "130", sort = "na")
    private String deviceModule;

    /**
     * PSG的拉取记录类型(1对应正常通行记录，2对应验证记录)
     */
    @Column(name = "t.RECORD_TYPE")
    @GridColumn(label = "att_attPoint_recordTypeList",
        format = "1=att_attPoint_normalPassRecord,2=att_attPoint_verificationRecord", width = "130", sort = "na")
    private Short recordType;

    /**
     * 最近数据拉取时间
     */
    @GridColumn(label = "att_attPoint_lastTransactionTime", width = "150", sort = "na")
    private Date lastTransactionTime;

    /**
     * 停车场的通道id或者门禁的门id或者视频的通道ID或通道的闸Id
     */
    @Column(name = "t.DEVICE_ID")
    private String deviceId;

    /**
     * 停车场或者门禁或者通道的设备sn
     */
    @Column(name = "t.DEVICE_SN")
    private String deviceSn;

    /**
     * 设备所属模块（门编号） (门禁当考勤时为门编号,VMS当考勤点时为通道编号,PSG当考勤时为闸编号 --by ljf 2019/12/26)
     */
    @Column(name = "t.DOOR_NO")
    private Short doorNo;

    @Column(name = "t.IP_ADDRESS")
    private String ipAddress;

    /**
     * 停车场的设备属性（出、入）
     */
    @Column(name = "t.STATUS")
    private Short status;

    /**
     * 区域id
     */
    @Column(name = "t.AREA_ID")
    private String areaId;

    @Condition(value = "t.AREA_ID", equalTag = "in")
    private String inAreaId;

    @Condition(value = "t.DEVICE_ID", equalTag = "in")
    private String inDeviceId;

    @Condition(value = "t.DEVICE_SN", equalTag = "in")
    private String inDeviceSn;

    /**
     * 默认构造方法
     */
    public AttPointItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AttPointItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AttPointItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param pointName
     * @param areaId
     * @param areaName
     * @param deviceId
     * @param deviceSn
     * @param deviceName
     * @param deviceModule
     * @param doorNo
     * @param status
     */
    public AttPointItem(String id, String pointName, String areaId, String areaName, String deviceId, String deviceSn,
        String deviceName, String deviceModule, Short doorNo, Short status) {
        super();
        this.id = id;
        this.pointName = pointName;
        this.areaId = areaId;
        this.areaName = areaName;
        this.deviceId = deviceId;
        this.deviceSn = deviceSn;
        this.deviceName = deviceName;
        this.deviceModule = deviceModule;
        this.doorNo = doorNo;
        this.status = status;
    }
}