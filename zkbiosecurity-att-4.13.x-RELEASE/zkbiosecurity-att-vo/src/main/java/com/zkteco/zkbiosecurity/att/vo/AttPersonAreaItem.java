/**
 * @author: GenerationTools
 * @date: 2018-02-08 下午08:27 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 考勤人员区域
 *
 * @author: GenerationTools
 * @date: 2018-02-08 下午08:27
 */
@From(after = "ATT_PERSON t ")
@Setter
@Getter
@Accessors(chain = true)
@GridConfig(operate = true, operates = {@GridOperate(type = "custom", permission = "att:personArea:addArea",
    click = "attPersonAddArea", label = "att_areaPerson_addArea"),})
public class AttPersonAreaItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    @Column(name = "t.PERS_PERSON_ID", equalTag = "=")
    @GridColumn
    private String personId;

    @Column(name = "t.PERS_PERSON_PIN")
    @GridColumn(label = "att_person_pin", width = "150", sort = "na", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String personPin;

    @Column(name = "t.PERS_PERSON_NAME")
    @GridColumn(label = "att_person_name", width = "150", sort = "na", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String personName;

    @Column(name = "t.PERS_PERSON_LASTNAME")
    @GridColumn(label = "att_person_lastName", showExpression = "#language!='zh_CN'", width = "150", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String personLastName;

    @Column(name = "t.AUTH_DEPT_ID")
    private String deptId;

    @Column(name = "t.AUTH_DEPT_CODE")
    private String deptCode;

    @Column(name = "t.AUTH_DEPT_NAME")
    @GridColumn(label = "pers_dept_deptName", width = "150", sort = "na")
    private String deptName;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "(lower(t.PERS_PERSON_NAME) LIKE ''%{0}%'' OR lower(t.PERS_PERSON_LASTNAME) LIKE ''%{0}%'')")
    private String likeName;
}