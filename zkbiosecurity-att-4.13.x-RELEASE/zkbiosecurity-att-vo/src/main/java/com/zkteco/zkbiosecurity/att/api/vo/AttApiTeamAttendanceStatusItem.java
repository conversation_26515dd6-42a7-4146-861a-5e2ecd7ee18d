package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class AttApiTeamAttendanceStatusItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer shouldSign; // 应到

    private Integer actualSign; // 实到

    private Integer lateCount; // 迟到

    private Integer leaveCount; // 休假

    private Integer earlyCount; // 早退

    private Integer leakageCount; // 漏卡

    private Integer tripCount; // 出差

    private Integer outCount; // 外出

    private Integer normalCount; // 正常

}
