package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 分组双列表人员VO
 *
 * @author: GenerationTools
 * @date: 2018-02-08 下午08:27
 */
@From(after = "ATT_PERSON t LEFT JOIN  ATT_GROUP ag ON t.GROUP_ID = ag.ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id", winHeight = 400, winWidth = 600)
@Setter
@Getter
@Accessors(chain = true)
public class AttGroupPersonItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 人员编号
     */
    @GridColumn(label = "att_person_pin", sort = "na", width = "120", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String personPin;

    /**
     * 名字
     */
    @Column(name = "t.PERS_PERSON_NAME")
    @GridColumn(label = "att_person_name", sort = "na", width = "120", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String personName;

    @GridColumn(label = "att_person_lastName", width = "120", showExpression = "#language!='zh_CN'", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String personLastName;

    /**
     * 部门编码
     */
    @GridColumn(label = "pers_dept_deptNo", show = false)
    private String deptCode;

    /**
     * 部门名称
     */
    @GridColumn(label = "pers_dept_deptName", width = "120", sort = "na")
    private String deptName;

    @Column(name = "ag.ID")
    @GridColumn(show = false)
    private String groupId;

    @Column(name = "t.PERS_PERSON_ID")
    @GridColumn(show = false)
    private String personId;

    private String likeName;

    /**
     * 默认构造方法
     */
    public AttGroupPersonItem() {
        super();
    }
}