package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 调休补班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 16:06
 * @since 1.0.0
 */
@From(after = "ATT_ADJUST t LEFT JOIN AUTH_DEPARTMENT ad ON t.AUTH_DEPT_ID = ad.ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600, operates = {@GridOperate(type = "del",
    permission = "att:adjust:del", url = "attAdjust.do?del&pins=(personPin)", label = "common_op_del")})
@Setter
@Getter
@Accessors(chain = true)
public class AttAdjustItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 人员id
     */
    @Column(name = "t.PERS_PERSON_ID")
    private String personId;

    /**
     * 人员编号
     */
    @Column(name = "t.PERS_PERSON_PIN")
    @GridColumn(label = "att_person_pin", width = "120", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String personPin;

    /**
     * 姓名
     */
    @GridColumn(label = "att_person_name", width = "120", sort = "na", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String personName;

    /**
     * 英文（lastName）
     */
    @GridColumn(label = "att_person_lastName", showExpression = "#language!='zh_CN'", width = "120", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String personLastName;

    /**
     * 部门id
     */
    @Column(name = "t.AUTH_DEPT_ID")
    private String deptId;

    /**
     * 部门编号
     */
    @Column(name = "ad.CODE")
    @GridColumn(label = "att_common_deptNo", width = "120", sort = "na")
    private String deptCode;

    /**
     * 部门名称
     */
    @Column(name = "ad.NAME")
    @GridColumn(label = "att_common_deptName", width = "120", sort = "na")
    private String deptName;

    /**
     * 调整类型
     */
    @Column(name = "t.ADJUST_TYPE")
    @GridColumn(label = "att_adjust_type", width = "120", format = "0=att_rule_off,1=att_rule_class", show = false)
    private Short adjustType;

    /**
     * 调整日期
     */
    @Column(name = "t.ADJUST_DATE")
    @DateType(type = "date")
    @GridColumn(label = "att_adjust_adjustDate", width = "120")
    private Date adjustDate;

    /**
     * 班次id
     */
    @Column(name = "t.SHIFT_ID")
    private String shiftId;

    /** 班次编号 **/
    private String shiftNo;

    /** 班次名称 **/
    @GridColumn(label = "att_shift_name", width = "120", show = false)
    private String shiftName;

    /**
     * 流程状态
     */
    @GridColumn(label = "common_status", width = "120", format = "0=att_approve_wait,1=att_exception_stop,2=att_apply_pass,3=att_apply_revoke,4=att_exception_delete,5=att_exception_refuse,6=att_exception_end")
    @Column(name = "t.FLOW_STATUS")
    private String flowStatus;

    /**
     * 备注
     */
    @Column(name = "t.REMARK")
    @GridColumn(label = "common_remark", width = "120")
    private String remark;

    /**
     * 操作日期时间
     */
    @Column(name = "t.CREATE_TIME")
    @GridColumn(label = "att_common_operateTime", width = "130")
    private Date operateDatetime;

    /**
     * 流程业务ID,全局唯一
     */
    @Column(name = "t.BUSINESS_KEY")
    private String businessKey;

    @Condition(value = "t.PERS_PERSON_PIN", equalTag = "=")
    private String personPinEq;

    @Condition(value = "t.CREATE_TIME", equalTag = ">=")
    private Date startTime;

    @Condition(value = "t.CREATE_TIME", equalTag = "<=")
    private Date endTime;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "t.PERS_PERSON_ID", equalTag = "in")
    private String inPersonId;

    @Condition(value = "t.PERS_PERSON_PIN", equalTag = "in")
    private String inPersonPin;

    @Condition(value = "t.ADJUST_DATE", equalTag = ">=")
    private Date adjustDateGE;

    @Condition(value = "t.ADJUST_DATE", equalTag = "<=")
    private Date adjustDateLE;

    @Condition(value = "t.FLOW_STATUS", equalTag = "in")
    private String flowStatusIn;

    @Condition(
            value = "t.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    private String likeName;

    /**
     * 申请编号
     */
    private String applyNo;
    /**
     * 申请类型
     */
    private String applyType;

    /**
     * 流程编号
     */
    private String flowNo;

    /**
     * 行数，导入用
     */
    private Integer rowNum;

    /** 是否包含下级 */
    private String isIncludeLower;

    /**
     * 默认构造方法
     */
    public AttAdjustItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AttAdjustItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AttAdjustItem(String id) {
        super(true);
        this.id = id;
    }
}