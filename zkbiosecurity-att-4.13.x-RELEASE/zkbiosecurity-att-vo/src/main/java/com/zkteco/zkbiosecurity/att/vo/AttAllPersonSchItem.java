package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 人员排班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 18:23
 * @since 1.0.0
 */
@From(after = "PERS_PERSON PP LEFT JOIN AUTH_DEPARTMENT AD ON PP.AUTH_DEPT_ID = AD.ID LEFT JOIN ATT_PERSON AP ON AP.PERS_PERSON_ID = PP.ID")
@OrderBy(after = "PP.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id", winHeight = 710, winWidth = 800,
    operates = {@GridOperate(type = "edit", url = "attPersonSch.do?info", label = "att_personSch_showSchInfo")})
@Setter
@Getter
@Accessors(chain = true)
public class AttAllPersonSchItem extends BaseItem {


    private static final long serialVersionUID = 1L;

    @Column(name = "PP.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    @Column(name = "PP.ID")
    private String personId;

    @Column(name = "PP.PIN")
    @GridColumn(label = "att_person_pin", width = "100", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String personPin;

    @Column(name = "PP.NAME")
    @GridColumn(label = "att_person_name", width = "100", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String personName;

    @Column(name = "PP.LAST_NAME")
    @GridColumn(label = "att_person_lastName", showExpression = "#language!='zh_CN'", width = "100", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String personLastName;

    @Column(name = "AD.ID")
    private String deptId;

    @Condition(value = "AD.ID", equalTag = "in")
    private String inDeptId;

    @Column(name = "AD.CODE")
    private String deptCode;

    @Column(name = "AD.NAME")
    @GridColumn(label = "pers_dept_entity", width = "*", sort = "na")
    private String deptName;

    @Condition(value = "(LOWER (PP.NAME) LIKE LOWER (''%{0}%'') OR LOWER (PP.LAST_NAME) LIKE LOWER (''%{0}%''))")
    private String likeName;

    @Condition(
            value = "PP.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    // 分组ID
    @Condition(value = "AP.GROUP_ID", equalTag = "=")
    private String groupId;

    @Condition(value = "{0}")
    private String orSqlCondition;


    @GridColumn(dynamicColumn = "attPersonSchDynamicColumn", sortNo = 99)
    private Map<String, Object> map = new HashMap<>();

    // 是否包含下级
    private String isIncludeLower;

    private Date startDate;

    private Date endDate;

    // 排班类型
    private Short scheduleType;

    // 对应排班类型组件id
    private String scheduleId;

    private String searchMonth;



    // 排班状态
    private String schStatus;

    public AttAllPersonSchItem() {
        super();
    }

    public AttAllPersonSchItem(Boolean equals) {
        super(equals);
    }

    public AttAllPersonSchItem(String id) {
        super(true);
        this.id = id;
    }

    public AttAllPersonSchItem(String id, Date startDate, Date endDate, Short scheduleType) {
        super();
        this.id = id;
        this.startDate = startDate;
        this.endDate = endDate;
        this.scheduleType = scheduleType;
    }
}