package com.zkteco.zkbiosecurity.att.vo;

import java.io.Serializable;
import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.Column;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 人员汇总表(减少返回字段)
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:27
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain = true)
public class AttMonthStatisticalReportExItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private String personPin;

    /** 考勤日期 */
    private Date attDate;

    /** 应出勤分钟数 */
    private Integer shouldMinute;
    /** 应出勤分钟数辅助计算字段(处理休息加班、节假日加班，且加班换算为天的时候，应上如果置为0，换算出来的加班时长也为0) */
    private Integer shouldMinuteEx;

    /** 实际出勤分钟数 */
    private Integer actualMinute;

    /** 有效出勤分钟数 */
    private Integer validMinute;

    /** 旷工分钟数 */
    private Integer absentMinute;


    /**
     * 请假详情
     * <p>
     * 保存各请假类型对应时长，格式:code-时长(分钟)，多个以逗号隔开
     * </p>
     */
    @Column(name = "t.LEAVE_DETAILS")
    private String leaveDetails;

    /**
     * 请假分钟数
     */
    private Integer leaveMinute;

    /**
     * 迟到总分钟数
     */
    private Integer lateMinuteTotal;

    /**
     * 早退总分钟数
     */
    private Integer earlyMinuteTotal;

    /**
     * 加班分钟数-平时
     */
    private Integer overtimeUsualMinute;

    /**
     * 加班分钟数-休息
     */
    private Integer overtimeRestMinute;

    /**
     * 加班分钟数-节日
     */
    private Integer overtimeHolidayMinute;

    /**
     * 加班分钟数
     */
    private Integer overtimeMinute;
}