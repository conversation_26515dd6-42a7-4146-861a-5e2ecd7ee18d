package com.zkteco.zkbiosecurity.att.calc.bo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

/**
 * 异常申请-加班 计算使用对象
 * 
 * <AUTHOR>
 * @date 2020/6/15
 */
@Setter
@Getter
public class AttOvertimeBO implements Serializable {

    private String id;

    /**
     * 加班标记（平时，休息日，节假日）
     */
    private Short overtimeSign;

    /**
     * 加班开始日期时间
     */
    private String startDateTime;

    /**
     * 加班结束日期时间
     */
    private String endDateTime;
}
