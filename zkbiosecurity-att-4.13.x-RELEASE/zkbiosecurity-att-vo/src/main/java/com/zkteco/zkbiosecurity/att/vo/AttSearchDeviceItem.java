package com.zkteco.zkbiosecurity.att.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Setter
@Getter
@Accessors(chain = true)
public class AttSearchDeviceItem implements Serializable {
    /*设备ip*/
    private String ip;
    /*设备sn*/
    private String sn;
    /*mac地址*/
    private String macAddress;
    /*子网掩码*/
    private String subnetMask;
    /*网关*/
    private String gateway;
    /*设备类型*/
    private String deviceType;
    /*服务器地址*/
    private String serverUrl;
    /*设备类型*/
    private String deviceName;
}
