package com.zkteco.zkbiosecurity.att.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 区域人员信息
 * 
 * @Auther: lambert.li
 * @Date: 2018/11/7 17:45
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel
public class AttApiAreaPersonItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /* 区域编号 */
    @ApiModelProperty(allowableValues = "1")
    private String code;

    /* 人员编号集合 */
    @ApiModelProperty(example = "[1,2]")
    private List<String> pins = new ArrayList<>();
}
