package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600, operates = {})
@Setter
@Getter
@Accessors(chain = true)
public class AttRealTimeCallRollItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    @GridColumn(show = false, sortNo = 0)
    private String id;

    /**
     * 人员编号
     */
    @GridColumn(label = "att_person_pin", width = "120", sortNo = 1)
    private String personPin;

    /**
     * 姓名
     */
    @GridColumn(label = "att_person_name", width = "120", sortNo = 2)
    private String personName;

    /**
     * 部门id
     */
    private String deptId;

    /**
     * 部门编号
     */
    @GridColumn(label = "pers_dept_deptNo", width = "150", sortNo = 3)
    private String deptCode;

    /**
     * 部门名称
     */
    @GridColumn(label = "pers_dept_deptName", width = "150", sortNo = 4)
    private String deptName;

    /**
     * 签到时间
     */
    @GridColumn(label = "att_realTime_signDateTime", width = "150", sortNo = 5)
    private Date attDatetime;

    /**
     * 签到状态 0已签到 1未签到 2请假
     */
    @GridColumn(label = "common_status", width = "150", sortNo = 6,
        format = "0=att_realTime_signPers,1=att_rule_noSignIn,2=att_leftMenu_leave", show = false)
    private Integer realTimeStatus;

    @GridColumn(label = "common_status", width = "150", sortNo = 7)
    private String exceptionName;

    /**
     * 开始时间
     */
    private String beginDate;
    /**
     * 结束时间
     */
    private String endDate;

}
