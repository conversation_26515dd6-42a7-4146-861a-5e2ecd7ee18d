package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 周期排班（分组/部门/人员）
 */
@Setter
@Getter
@NoArgsConstructor
@From(after = "ATT_CYCLESCH t LEFT JOIN ATT_GROUP ag ON ag.ID = t.GROUP_ID LEFT JOIN PERS_PERSON pp ON pp.pin = t.PERS_PERSON_PIN LEFT JOIN AUTH_DEPARTMENT ad ON (ad.ID = t.AUTH_DEPT_ID OR ad.ID = pp.auth_dept_id)")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winWidth = 440, winHeight = 540,
    operates = {
        @GridOperate(type = "edit", permission = "att:schDetails:edit", url = "attCycleSch.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "custom", permission = "att:schDetails:del", click = "attSchDetailsDelVo",
            label = "common_op_del")})
public class AttCycleSchItem extends BaseItem implements Serializable {

    private static final long serialVersionUID = 1L;
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /** 人员id */
    @Column(name = "t.PERS_PERSON_ID")
    private String personId;

    /** 人员编号 */
    @Column(name = "t.PERS_PERSON_PIN")
    @GridColumn(label = "att_person_pin", width = "120", sort = "na", columnType = "edit",
        editPermission = "att:schDetails:edit", editUrl = "attCycleSch.do?edit", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String personPin;


    /** 人员姓名 **/
    @Column(name = "pp.NAME")
    @GridColumn(label = "att_person_name", width = "120", sort = "na", columnType = "edit",
        editPermission = "att:schDetails:edit", editUrl = "attCycleSch.do?edit", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String personName;

    @Column(name = "pp.LAST_NAME")
    @GridColumn(label = "att_person_lastName", showExpression = "#language!='zh_CN'", width = "120", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String personLastName;

    /** 部门id */
    @Column(name = "ad.ID")
    private String deptId;

    @Column(name = "ad.CODE")
    private String deptCode;

    @Column(name = "ad.NAME")
    @GridColumn(label = "att_common_deptName", width = "120", sort = "na", columnType = "edit",
        editPermission = "att:schDetails:edit", editUrl = "attCycleSch.do?edit")
    private String deptName;

    /** 分组id */
    @Column(name = "t.GROUP_ID")
    private String groupId;

    @Column(name = "ag.GROUP_NAME")
    @GridColumn(label = "att_common_groupName", width = "120", sort = "na", columnType = "edit",
        editPermission = "att:schDetails:edit", editUrl = "attCycleSch.do?edit")
    private String groupName;

    /** 班次名称 **/
    @GridColumn(label = "att_shift_name", width = "180", sort = "na")
    private String shiftName;

    /** 起始日期 */
    @Column(name = "t.START_DATE")
    @DateType(type = "date")
    @GridColumn(label = "common_startTime", width = "120")
    private Date startDate;

    /** 结束日期 */
    @Column(name = "t.END_DATE")
    @DateType(type = "date")
    @GridColumn(label = "common_endTime", width = "120")
    private Date endDate;

    /** 查询条件：起始日期 */
    @Condition(value = "t.END_DATE", equalTag = ">=")
    private Date startTime;

    /** 查询条件：结束日期 */
    @Condition(value = "t.START_DATE", equalTag = "<=")
    private Date endTime;

    /** 排班类型(普通排班=0、智能找班=1) */
    @Column(name = "t.SCHEDULE_TYPE")
    @GridColumn(label = "att_schedule_type", width = "120", format = "0=att_schedule_normal,1=att_schedule_intelligent")
    private Short scheduleType;

    /** 周期排班类型（分组=0、部门=1、人员=2） */
    @Column(name = "t.CYCLE_TYPE")
    private Short cycleType;

    @Condition(value = "t.PERS_PERSON_PIN", equalTag = "in")
    private String inPersonPin;


    @Condition(value = "ad.ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "t.GROUP_ID", equalTag = "in")
    private String inGroupId;

    /* 新增时，前端传的班次ids */
    private String shiftIds;

    /** 是否包含下级 */
    private String isIncludeLower;

    /** 行数，导入用 */
    private Integer rowNum;

    public AttCycleSchItem(String id) {
        super(true);
        this.id = id;
    }
}
