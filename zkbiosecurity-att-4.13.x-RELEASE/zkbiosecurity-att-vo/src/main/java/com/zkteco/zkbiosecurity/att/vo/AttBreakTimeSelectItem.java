package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;

/**
 * 休息时间段选择Item
 * 
 * <AUTHOR>
 * @date 2019/6/3
 */
@GridConfig
@Getter
@Setter
public class AttBreakTimeSelectItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;
    private String type;
    private String selectId;
    private String linkId;
    private String likeName;

    @GridColumn(label = "common_name", width = "120")
    private String name;

    @GridColumn(label = "att_breakTime_startTime", width = "120")
    private String startTime;

    @GridColumn(label = "att_breakTime_endTime", width = "120")
    private String endTime;

}
