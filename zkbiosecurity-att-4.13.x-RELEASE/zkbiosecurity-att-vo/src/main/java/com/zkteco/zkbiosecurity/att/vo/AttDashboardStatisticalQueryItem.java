package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 考勤面板统计工作时长
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:27
 * @since 1.0.0
 */
@From(after = "ATT_RECORD t ")
@GroupBy(after = "t.PERS_PERSON_PIN")
@OrderBy(after = "t.PERS_PERSON_PIN DESC")
@GridConfig
@Setter
@Getter
@Accessors(chain = true)
public class AttDashboardStatisticalQueryItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    @GridColumn(show = false)
    private String id;

    /** 编号 */
    @Column(name = "t.PERS_PERSON_PIN")
    private String pin;

    /** 姓名 */
    private String name;

    /** 英文（lastName） */
    private String lastName;

    /** 实际 */
    @Column(name = "(COALESCE(SUM(t.ACTUAL_MINUTE),0))")
    private String actualHour;

    /** 实际时长查询条件 */
    @Condition(value = "(COALESCE(SUM(t.ACTUAL_MINUTE),0))", equalTag = ">=", coditionType = "having")
    private Integer actualMinuteGe;

    @Condition(value = "t.ATT_DATE", equalTag = ">=")
    private Date monthStart;

    @Condition(value = "t.ATT_DATE", equalTag = "<=")
    private Date monthEnd;
}
