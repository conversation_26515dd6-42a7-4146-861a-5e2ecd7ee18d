package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class AttApiValidCardItem implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 实际上班时间
     */
    private String startWorkTime;
    /**
     * 上班打卡状态
     */
    private String startWorkStatus;
    /**
     * 实际下班时间
     */
    private String endWorkTime;
    /**
     * 下班打卡状态
     */
    private String endWorkStatus;
    /**
     * 应该上班时间
     */
    private String shouldStartWorkTime;
    /**
     * 应该下班时间
     */
    private String shouldEndWorkTime;
}