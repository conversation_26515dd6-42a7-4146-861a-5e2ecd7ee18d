package com.zkteco.zkbiosecurity.att.api.vo;

import com.zkteco.zkbiosecurity.att.calc.bo.AttExceptionBO;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
public class AttApiTeamSignMonthItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    private String personName;

    /**
     * 人员编号
     */
    private String personPin;

    /**
     * 补签次数
     */
    private Integer count;
}
