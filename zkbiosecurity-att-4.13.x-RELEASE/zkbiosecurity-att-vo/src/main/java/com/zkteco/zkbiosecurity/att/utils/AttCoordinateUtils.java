package com.zkteco.zkbiosecurity.att.utils;

/**
 * 坐标计算工具类
 *
 * <AUTHOR>
 * @date 2025-03-07 16:51
 * @since 1.0.0
 */
public class AttCoordinateUtils {

    // 坐标系转换方法
    public static double[] wgs84ToGcj02(double lng, double lat) {
        return new double[] {lng, lat};
    }

    public static double[] gcj02ToWgs84(double lng, double lat) {
        return new double[] {lng, lat};
    }

    private static double[] gcj02Bd09Offset() {
        return new double[] {0.00662, 0.00546};
    }

    public static double[] gcj02ToBd09(double lng, double lat) {
        double z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * Math.PI);
        double theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * Math.PI);
        return new double[] {z * Math.cos(theta) + gcj02Bd09Offset()[0], z * Math.sin(theta) + gcj02Bd09Offset()[1]};
    }

    public static double[] bd09ToGcj02(double lng, double lat) {
        double x = lng - gcj02Bd09Offset()[0], y = lat - gcj02Bd09Offset()[1];
        double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * Math.PI);
        double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * Math.PI);
        return new double[] {z * Math.cos(theta), z * Math.sin(theta)};
    }

    // 距离计算方法（Haversine公式）
    public static int distanceMeters(double lng1, double lat1, String fromCoord, double lng2, double lat2,
        String toCoord) {
        // 统一转换为WGS84坐标系计算
        double[] point1 = convertToWgs84(lng1, lat1, fromCoord);
        double[] point2 = convertToWgs84(lng2, lat2, toCoord);

        double radLat1 = Math.toRadians(point1[1]);
        double radLng1 = Math.toRadians(point1[0]);
        double radLat2 = Math.toRadians(point2[1]);
        double radLng2 = Math.toRadians(point2[0]);

        double dLat = radLat2 - radLat1;
        double dLng = radLng2 - radLng1;
        double a =
            Math.pow(Math.sin(dLat / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(dLng / 2), 2);
        return (int)(6371000 * 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)));
    }

    // 私有辅助方法
    private static double[] convertToWgs84(double lng, double lat, String coord) {
        switch (coord.toUpperCase()) {
            case "GCJ02":
                return gcj02ToWgs84(lng, lat);
            case "BD09":
                return bd09ToGcj02(lng, lat);
            default:
                return new double[] {lng, lat};
        }
    }

    public static void main(String[] args) {
        // GCJ02 BD09 WGS84
        // https://api.map.baidu.com/lbsapi/getpoint/index.html
        // https://lbs.amap.com/tools/picker
        // https://www.google.com/maps

        // double[] gcj02ToBd09 = gcj02ToBd09(118.036228, 24.608841);
        // System.out.println("gcj02 -> bd09: " + gcj02ToBd09[0] + ", " + gcj02ToBd09[1]);

        // double[] bd09ToGcj02 = bd09ToGcj02(86.348571, 48.080891);
        // System.out.println("bd09 -> gcj02: " + bd09ToGcj02[0] + ", " + bd09ToGcj02[1]);

        // double[] gcj02ToWgs84 = gcj02ToWgs84(118.036228, 24.608839);
        // System.out.printf("gcj02 -> wgs84: %.6f, %.6f%n", gcj02ToWgs84[1], gcj02ToWgs84[0]);

//        int distance = AttCoordinateUtils.distanceMeters(118.042783, 24.614639,
//                "BD09", 118.036190, 24.608857, "WGS84");
//        System.out.println("AttSignAddress distance = " + distance);

    }
}