package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 人员补签记录
 * 
 * <AUTHOR>
 * @date 2019/03/07 19:13
 */
@Setter
@Getter
@Accessors(chain = true)
@From(after = "ATT_SIGN t ")
@OrderBy(after = "t.PERS_PERSON_PIN")
@GroupBy(after = "t.PERS_PERSON_PIN")
public class AttTeamSignMonthItem extends BaseItem {

    private String id;

    /**
     * 人员编号
     */
    @Column(name = "t.PERS_PERSON_PIN")
    private String personPin;

    /**
     * 补签次数
     */
    @Column(name = "count(t.PERS_PERSON_PIN)")
    private Integer count;

    /**
     * 流程状态
     */
    @Condition(value = "t.FLOW_STATUS", equalTag = "=")
    private String flowStatus;

    /**
     * 人员集合
     */
    @Condition(value = "t.PERS_PERSON_PIN", equalTag = "in")
    private String inPersonPin;

    /**
     * 开始时间
     */
    @Condition(value = "t.SIGN_DATETIME", equalTag = ">=")
    private Date startDate;

    /**
     * 结束时间
     */
    @Condition(value = "t.SIGN_DATETIME", equalTag = "<=")
    private Date endDate;

}
