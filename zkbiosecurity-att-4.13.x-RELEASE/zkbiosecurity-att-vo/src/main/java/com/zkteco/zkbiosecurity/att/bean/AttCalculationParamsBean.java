package com.zkteco.zkbiosecurity.att.bean;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.zkteco.zkbiosecurity.att.calc.bo.AttLeaveBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttOvertimeBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.vo.AttShiftItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 手动计算-参数对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/13 10:25
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class AttCalculationParamsBean {

    private List<String> pins;

    private Date startDate;

    private Date endDate;

    private Map<String, List<AttLeaveBO>> attLeaveMap;

    private Map<String, List<AttOvertimeBO>> attOvertimeMap;

    private Map<String, List<AttPersonSchBO>> attAdjustMap;

    private Map<String, List<AttPersonSchBO>> attClassMap;

    private AttRuleParamBean attRuleParamBean;

    private Set<String> attHolidaySet;

    private Map<String, AttShiftItem> attShiftItemMap;

    private Map<String, AttTimeSlotItem> attTimeSlotItemMap;
}
