package com.zkteco.zkbiosecurity.att.constants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AppConstant {

    public static final String MODULE_NAME = "api/v1";

    /** ----------------- 手机APP权限编码 --------------- */
    /** 考勤日报表 */
    public static final String APP_ATT_DAILY_REPORT = "AttendanceDailyReport";
    /** 考勤月报表 */
    public static final String APP_ATT_MONTHLY_REPORT = "AttendanceMonthlyReport";
    /** 车辆管理 */
    public static final String APP_PARKING_MAIN = "ParkingMain";
    /** 车辆信息 */
    public static final String APP_PARKING_INFO = "ParkingInfo";
    /** 门禁管理 */
    public static final String APP_ACC_MANAGEMENT = "AccessManagement";
    /** 门禁记录 */
    public static final String APP_ACC_RECORD = "AccessRecord";
    /** 访客邀约 */
    public static final String APP_VIS_INVITE = "InviteVisitor";
    /** 邀约记录 */
    public static final String APP_VIS_INVITE_RECORD = "InviteRecord";
    /** 访客来访记录 */
    public static final String APP_VIS_VISIT_RRECORD = "VisitRecord";
    /** 消费报表 */
    public static final String APP_POS_RECORD = "PosRecord";

    /** 初始化部门code */
    public static final String DEFAULT_CODE = "1";
    /** 默认密码（123456） */
    public static final String PERS_DEFAULT_PWD = "123456";
    /** 印尼APP */
    public static final String INDONESIA_PERS_APP = "INDONESIA_PERS_APP";
    /** 用户APP */
    public static final String PERS_CLOUD_APP = "PERS_CLOUD_APP";
    /** 用户激活 */
    public static final String PERS_CLOUD_ACTIVATED = "ACTIVATED";
    /** 微信公众号 */
    public static final String WX_PERS = "WX_CHACT_PERS";

    /** 微信小程序 */
    public static final String WX_MINI_PERS = "WX_MINI_PERS";

    /** 登录方式 密码登录(PSW_LOGIN) */
    public static final String PSW_LOGIN = "PSW_LOGIN";

    /** 登录方式 动态验证码登录(PHONE_CODE_LOGIN) */
    public static final String PHONE_CODE_LOGIN = "PHONE_CODE_LOGIN";

    /* ----------------- 获取TOKEN类型 ------------------- */
    /** 创建token */
    public static final String APP_TOKEN_GRANT_TYPE_CREATE = "client_credential";
    /** 刷新token */
    public static final String APP_TOKEN_GRANT_TYPE_REFRESH = "refresh_token";
    /** access_token Redis key */
    public static final String APP_TOKEN_ACCESS_TOKEN_CACHE_KEY = "app:access_token:";
    /** refresh_token Redis key */
    public static final String APP_TOKEN_REFRESH_TOKEN_CACHE_KEY = "app:refresh_token:";

    /* ----------------- 同意、拒绝预约 ------------------- */
    /** 同意 */
    public static final String VIS_CLOUD_AUDIT = "VIS_CLOUD_AUDIT";
    /** 拒绝 */
    public static final String VIS_CLOUD_REFUSE = "VIS_CLOUD_REFUSE";

    /** 邀约 */
    public static final String VIS_CLOUD_INVITATION = "1";
    /** 预约 */
    public static final String VIS_CLOUD_RESERVATION = "0";

    /* 预约邀约 数据来源  手机app类型 VIS_COULD_APP  小程序类型  VIS_COULD_MINIPROGRAM  微信公众号 VIS_COULD_WECHATMP*/
    public static final String VIS_COULD_APP = "VIS_COULD_APP";
    public static final String VIS_COULD_MINIPROGRAM = "VIS_COULD_MINIPROGRAM";
    public static final String VIS_COULD_WECHATMP = "VIS_COULD_WECHATMP";

    public static final String VIS_TENDENCY_PERSON = "VIS_TENDENCY_PERSON";
    public static final String VIS_TENDENCY_VISITOR = "VIS_TENDENCY_VISITOR";

    /** 企业管理员 */
    public static final String COMPANY_ADMIN_USER = "1";
    /** 企业员工 */
    public static final String COMPANY_EMPLOYEE_USER = "2";

    /* --------------- 工作流类型 -------------- */
    /** 补签类型 **/
    public static final String FLOW_TYPE_SIGN = "sign";

    /** 请假类型 **/
    public static final String FLOW_TYPE_LEAVE = "leave";

    /** 加班类型 **/
    public static final String FLOW_TYPE_OVERTIME = "overtime";

    /** 出差类型 **/
    public static final String FLOW_TYPE_TRIP = "trip";

    /** 外出类型 **/
    public static final String FLOW_TYPE_OUT = "out";

    /* --------------- 考勤标识符 -------------- */
    /** 考勤标识符 **/
    public static final String ATT_SYMBOLS = "√,L,E,□,[,],∆,+,○,●,T,G";;

    /* --------------- 考勤状态 -------------- */
    /** 正常 **/
    public static final String ATT_STATUS_NORMAL = "arrive";
    /** 漏卡 **/
    public static final String ATT_STATUS_LEAKAGE = "leakage";
    /** 迟到 **/
    public static final String ATT_STATUS_LATE = "late";
    /** 早退 **/
    public static final String ATT_STATUS_EARLY = "early";
    /** 休假 **/
    public static final String ATT_STATUS_LEAVE = "leave";
    /** 出差 **/
    public static final String ATT_STATUS_TRIP = "trip";
    /** 外出 **/
    public static final String ATT_STATUS_OUT = "out";

    /* --------------- 消息定义 -------------- */
    /** 待办消息集合 */
    public static final List<String> TODO_MSG_CODE_LIST = new ArrayList<>();
    /** 知会消息集合 */
    public static final List<String> NOTIFY_MSG_CODE_LIST = new ArrayList<>();
    /** 访客动态 */
    public static final List<String> VIS_TENDENCY_CODE_LIST = new ArrayList<>();
    /** 报警消息 */
    public static final List<String> WARN_MSG_CODE_LIST = new ArrayList<>();
    /** 消息提醒 */
    public static final List<String> REMIND_MSG_CODE_LIST = new ArrayList<>();
    /** 消息业务编码集合 */
    public static final Map<String, List<String>> BUSINESS_CODE_MAP = new HashMap<>();

    static {
        // 待办消息 business_code 定义
        TODO_MSG_CODE_LIST.add("AttLeave");
        TODO_MSG_CODE_LIST.add("AttSign");
        TODO_MSG_CODE_LIST.add("AttOvertime");
        TODO_MSG_CODE_LIST.add("AttOut");
        TODO_MSG_CODE_LIST.add("AttTrip");
        TODO_MSG_CODE_LIST.add("OaMeetingRoom");
        TODO_MSG_CODE_LIST.add("OaApplyCar");
        TODO_MSG_CODE_LIST.add("VisReservation");
        TODO_MSG_CODE_LIST.add("OaDorm");

        // 知会消息 business_code 定义
        NOTIFY_MSG_CODE_LIST.add("AttLeave");
        NOTIFY_MSG_CODE_LIST.add("AttSign");
        NOTIFY_MSG_CODE_LIST.add("AttOvertime");
        NOTIFY_MSG_CODE_LIST.add("AttOut");
        NOTIFY_MSG_CODE_LIST.add("AttTrip");
        NOTIFY_MSG_CODE_LIST.add("OaMeetingRoom");
        NOTIFY_MSG_CODE_LIST.add("OaApplyCar");
        NOTIFY_MSG_CODE_LIST.add("OaDorm");

        // 访客动态 buiness_code 定义
        VIS_TENDENCY_CODE_LIST.add("VisTendencyMsg");

        // 报警消息 business_code 定义
        WARN_MSG_CODE_LIST.add("AccWarn");
        WARN_MSG_CODE_LIST.add("ParkOvertimeWarn");

        // 消息提醒 business_code 定义
        REMIND_MSG_CODE_LIST.add("OaMettingNotify");
        REMIND_MSG_CODE_LIST.add("OaDormResultNotify");
        REMIND_MSG_CODE_LIST.add("BirthdayNotify");
        REMIND_MSG_CODE_LIST.add("ParkDelayNotify");

        // 消息业务编码集合定义
        BUSINESS_CODE_MAP.put("TodoMsg", TODO_MSG_CODE_LIST);
        BUSINESS_CODE_MAP.put("NotifyMsg", NOTIFY_MSG_CODE_LIST);
        BUSINESS_CODE_MAP.put("VisTendency", VIS_TENDENCY_CODE_LIST);
        BUSINESS_CODE_MAP.put("WarnMsg", WARN_MSG_CODE_LIST);
        BUSINESS_CODE_MAP.put("RemindMsg", REMIND_MSG_CODE_LIST);
    }

    /** 考勤日历默认打卡数据 **/
    public static final String ATT_CALENDAR_DEFAULT_CARD = "--:--";
    /** 考勤日历卡点状态 0 正常 **/
    public static final String ATT_CALENDAR_CARD_STATUS_NORMAL = "0";
    /** 考勤日历卡点状态 1 异常 **/
    public static final String ATT_CALENDAR_CARD_STATUS_EXCEPTION = "1";
    /** 考勤日历卡点状态 2 补签 **/
    public static final String ATT_CALENDAR_CARD_STATUS_SIGN = "2";

    /** 考勤日历 L 漏卡 **/
    public static final String ATT_CALENDAR_LACK_CARD = "L";
    /** 考勤日历 W 未打卡 **/
    public static final String ATT_CALENDAR_WAIT_CARD = "W";
    /** 考勤跨天规则 记第一日 **/
    public static final String ATT_CROSS_DAY_RULE_FIRST = "0";
    /** 考勤跨天规则 记第二日 **/
    public static final String ATT_CROSS_DAY_RULE_SECONDS = "1";

    /** 申请备注最大长度 **/
    public static final Integer APPLY_REMARK_MAX_LENGTH = 200;

    /**
     * 考勤日历接口状态 <br/>
     * 0-正常、1-异常、2-请假、3-休息、4-漏卡、5-加班、6-外出、7-出差、8-未排班、9-调休、10-补班、11-调班
     */
    /** 正常 */
    public static final int ATT_RESULT_TYPE_NORMAL = 0;
    /** 异常 */
    public static final int ATT_RESULT_TYPE_EXCEPTION = 1;
    /** 请假 */
    public static final int ATT_RESULT_TYPE_LEAVE = 2;
    /** 休息 */
    public static final int ATT_RESULT_TYPE_REST = 3;
    /** 考勤状态 漏卡 */
    public static final int ATT_RESULT_TYPE_LACK_CARD = 4;
    /** 加班 */
    public static final int ATT_RESULT_TYPE_OVERTIME = 5;
    /** 外出 */
    public static final int ATT_RESULT_TYPE_OUT = 6;
    /** 出差 */
    public static final int ATT_RESULT_TYPE_TRIP = 7;
    /** 未排班 */
    public static final int ATT_RESULT_TYPE_NO_SCHEDULING = 8;
    /** 考勤状态 调休 */
    public static final int ATT_RESULT_TUNE_OFF = 9;
    /** 考勤状态 补班 */
    public static final int ATT_RESULT_TAKE_CLASS = 10;
    /** 考勤状态 调班 */
    public static final int ATT_RESULT_ADJUST_CLASS = 11;

}
