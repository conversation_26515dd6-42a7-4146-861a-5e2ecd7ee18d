package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 ** <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
 * @description 考勤照片导出查询item 减少字段查询
 * @date 2020/6/18
 **/
@From(after = "ATT_TRANSACTION t ")
@OrderBy(after = "t.ATT_DATETIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600, operates = {})
@Setter
@Getter
@Accessors(chain = true)
public class AttPhotoExportQueryItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    private String id;

    /**
     * 人员编号
     */
    @Column(name = "t.PERS_PERSON_PIN")
    private String personPin;

    /**
     * 设备序列号
     */
    @Column(name = "t.DEVICE_SN")
    private String deviceSn;

    /**
     * 考勤日期时间
     */
    @Column(name = "t.ATT_DATETIME")
    private Date attDatetime;

    /**
     * 考勤日期
     */
    @Column(name = "t.ATT_DATE")
    private String attDate;

    @Condition(value = "t.ATT_DATETIME", equalTag = ">=")
    private Date beginDate;

    @Condition(value = "t.ATT_DATETIME", equalTag = "<=")
    private Date endDate;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "t.AUTH_AREA_ID", equalTag = "in")
    private String inAreaId;

    @Condition(value = "t.DEVICE_ID", equalTag = "in")
    private String inDeviceId;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 压缩目录
     */
    private String compressDirectory;

    /**
     * 默认构造方法
     */
    public AttPhotoExportQueryItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AttPhotoExportQueryItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AttPhotoExportQueryItem(String id) {
        super(true);
        this.id = id;
    }

}