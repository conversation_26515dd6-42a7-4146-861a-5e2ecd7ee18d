package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 报表推送
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 17:12
 * @since 1.0.0
 */
@From(after = "ATT_AUTOEXPORT t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 580, winWidth = 900,
    operates = {
        @GridOperate(type = "edit", permission = "att:autoExport:edit", url = "attAutoExport.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "att:autoExport:del", url = "attAutoExport.do?del",
            label = "common_op_del")})
@Setter
@Getter
@Accessors(chain = true)
public class AttAutoExportItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 文件名称
     */
    @Column(name = "t.FILE_NAME")
    @GridColumn(label = "att_autoExport_fileName", width = "150", columnType = "edit",
        editPermission = "att:autoExport:edit", editUrl = "attAutoExport.do?edit")
    private String fileName;

    /**
     * 发送方式（0：邮箱发送方式，1：FTP发送方式）
     */
    @Column(name = "t.SEND_FORMAT")
    @GridColumn(label = "att_autoExport_sendFormat", width = "150",
        format = "0=att_autoExport_mailFormat,1=att_autoExport_ftpFormat,2=att_autoExport_sftpFormat")
    private String sendFormat;

    /**
     * 报表类型（0:原始记录表、1:日打卡详情表）
     */
    @Column(name = "t.REPORT_TYPE", equalTag = "=")
    @GridColumn(label = "att_autoExport_reportType", width = "150",
        format = "0=att_leftMenu_transaction,1=att_leftMenu_dayCardDetailReport,2=att_leftMenu_dayDetailReport,3=att_leftMenu_abnormal,4=att_leftMenu_monthDetailReport,5=att_leftMenu_monthStatisticalReport")
    private String reportType;

    /**
     * 文件类型（TXT）
     */
    @Column(name = "t.FILE_TYPE", equalTag = "=")
    // @GridColumn(label = "att_autoExport_fileType", width = "90", format = "0=TXT")
    private String fileType;

    /**
     * 文件日期格式
     */
    @Column(name = "t.FILE_DATE_FORMAT")
    @GridColumn(label = "att_autoExport_fileDateFormat", show = false)
    private String fileDateFormat;

    /**
     * 文件时间格式
     */
    @Column(name = "t.FILE_TIME_FORMAT")
    @GridColumn(label = "att_autoExport_fileTimeFormat", show = false)
    private String fileTimeFormat;

    /**
     * 文件内容格式
     */
    @Column(name = "t.FILE_CONTENT_FORMAT")
    // @GridColumn(label = "att_autoExport_fileContentFormat", width = "120")
    private String fileContentFormat;

    /**
     * 文件字段转换
     */
    @Column(name = "t.FILE_FIELD_CONVERT")
    @GridColumn(label = "att_autoExport_fileFieldConvert", show = false)
    private String fileFieldConvert;

    /**
     * 时间发送频率（日、月）
     */
    @Column(name = "t.TIME_SEND_FREQUENCY", equalTag = "=")
    @GridColumn(label = "att_autoExport_timeSendFrequency", format = "0=common_day,1=common_month", width = "150")
    private String timeSendFrequency;

    /**
     * 时间发送间隔
     */
    @Column(name = "t.TIME_SEND_INTERVAL")
    @GridColumn(label = "att_autoExport_timeSendInterval", width = "150",
        format = "firstDayOfMonth=att_autoExport_firstDayofMonth,lastDayOfMonth=att_autoExport_lastDayofMonth")
    private String timeSendInterval;

    /**
     * 邮件类型（0:按人员、1:按部门、2:按区域）
     */
    @Column(name = "t.EMAIL_TYPE", equalTag = "=")
    // @GridColumn(label = "att_autoExport_emailType", format =
    // "0=att_common_person,1=att_common_dept,2=att_leftMenu_area",width = "120")
    private String emailType;

    /**
     * 邮件接收人
     */
    @Column(name = "t.EMAIL_RECIPIENTS", encryptConverter = true)
    // @GridColumn(label = "att_autoExport_emailRecipients", width = "150")
    private String emailRecipients;

    /**
     * 邮件标题
     */
    @Column(name = "t.EMAIL_SUBJECT")
    // @GridColumn(label = "att_autoExport_emailSubject", width = "100")
    private String emailSubject;

    /**
     * 邮件正文
     */
    @Column(name = "t.EMAIL_CONTENT")
    // @GridColumn(label = "att_autoExport_emailContent", width = "130")
    private String emailContent;

    /**
     * FTP服务器地址
     */
    @Column(name = "FTP_URL")
    // @GridColumn(label = "att_autoExport_ftpUrl",width = "130")
    private String ftpUrl;

    /**
     * FTP服务器端口
     */
    @Column(name = "FTP_PORT")
    private Integer ftpPort;

    /**
     * FTP登录账号
     */
    @Column(name = "FTP_USERNAME")
    private String ftpUsername;

    /**
     * FTP登录密码
     */
    @Column(name = "FTP_PASSWORD", encryptConverter = true)
    private String ftpPassword;

    /**
     * 任务名称
     */
    @Column(name = "t.JOB_NAME")
    @GridColumn(label = "att_autoExport_jobName", show = false)
    private String jobName;

    /**
     * 任务执行类（com.zk.att.quartz.AttQuartzJob）
     */
    @Column(name = "t.JOB_CLASS")
    @GridColumn(label = "att_autoExport_jobClass", show = false)
    private String jobClass;

    /**
     * 任务Cron表达式
     */
    @Column(name = "t.JOB_CRON")
    @GridColumn(label = "att_autoExport_jobCron", show = false)
    private String jobCron;

    /**
     * 任务状态（启动、禁用），默认启动
     */
    @Column(name = "t.JOB_STATUS", equalTag = "=")
    @GridColumn(label = "common_inOutStatus", width = "80", columnType = "custom", convert = "jobStatusConvertToIcon")
    private String jobStatus;

    /**
     * 部门id
     */
    @Column(name = "t.AUTH_DEPT_ID")
    private String deptId;

    /**
     * 区域id
     */
    @Column(name = "t.AUTH_AREA_ID")
    private String areaId;

    /**
     * 默认构造方法
     */
    public AttAutoExportItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AttAutoExportItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AttAutoExportItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param reportType
     * @param fileType
     * @param fileName
     * @param fileDateFormat
     * @param fileTimeFormat
     * @param fileContentFormat
     * @param fileFieldConvert
     * @param timeSendFrequency
     * @param timeSendInterval
     * @param emailType
     * @param emailRecipients
     * @param emailSubject
     * @param emailContent
     * @param jobName
     * @param jobClass
     * @param jobCron
     * @param jobStatus
     */
    public AttAutoExportItem(String id, String reportType, String fileType, String fileName, String fileDateFormat,
        String fileTimeFormat, String fileContentFormat, String fileFieldConvert, String timeSendFrequency,
        String timeSendInterval, String emailType, String emailRecipients, String emailSubject, String emailContent,
        String jobName, String jobClass, String jobCron, String jobStatus, String sendFormat, String ftpUrl,
        int ftpPort, String ftpUsername, String ftpPassword) {
        super();
        this.id = id;
        this.reportType = reportType;
        this.fileType = fileType;
        this.fileName = fileName;
        this.fileDateFormat = fileDateFormat;
        this.fileTimeFormat = fileTimeFormat;
        this.fileContentFormat = fileContentFormat;
        this.fileFieldConvert = fileFieldConvert;
        this.timeSendFrequency = timeSendFrequency;
        this.timeSendInterval = timeSendInterval;
        this.emailType = emailType;
        this.emailRecipients = emailRecipients;
        this.emailSubject = emailSubject;
        this.emailContent = emailContent;
        this.jobName = jobName;
        this.jobClass = jobClass;
        this.jobCron = jobCron;
        this.sendFormat = sendFormat;
        this.jobStatus = jobStatus;
        this.ftpUrl = ftpUrl;
        this.ftpPort = ftpPort;
        this.ftpUsername = ftpUsername;
        this.ftpPassword = ftpPassword;
    }
}