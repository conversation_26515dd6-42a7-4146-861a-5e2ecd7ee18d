package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
public class AttApiTeamOverTimePersonItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    private String personName;

    /**
     * 人员编号
     */
    private String personPin;

    /**
     * 头像地址
     */
    private String avatarUrl;

    /**
     * 加班类型名称
     */
    private String overTimeTypeName;

    /**
     * 加班时长
     */
    private String overTimeLong;

    /**
     * 加班时间
     */
    private String overTime;

    /**
     * 申请时间
     */
    private String applyTime;

    /**
     * 任务ID
     */
    private String taskId;
}
