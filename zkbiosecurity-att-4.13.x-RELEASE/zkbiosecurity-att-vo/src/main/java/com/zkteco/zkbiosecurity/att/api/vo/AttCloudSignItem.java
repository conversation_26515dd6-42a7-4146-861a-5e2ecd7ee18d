package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 2019/3/21 10:47
 */
@Getter
@Setter
@Accessors(chain = true)
public class AttCloudSignItem implements Serializable {
    /**
     * 人员编号
     */
    private String personPin;

    /**
     * 姓名
     */
    private String personName;

    /**
     * 部门编号
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 补签日期时间
     */
    private String signDate;

    /**
     * 补签日期时间
     */
    private String signDatetime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 英文（lastName）
     */
    private String personLastName;

    /** 知会人 */
    private String notifierPins;

    /**
     * 记录补签异常打卡
     */
    private String beforeSignRecord;

    /**
     * 记录补签时间计算后结果
     */
    private String afterSignRecord;

    /**
     * 补签时间
     */
    private String signTime;


    /**
     * 考勤状态
     */
    private String attState;
}