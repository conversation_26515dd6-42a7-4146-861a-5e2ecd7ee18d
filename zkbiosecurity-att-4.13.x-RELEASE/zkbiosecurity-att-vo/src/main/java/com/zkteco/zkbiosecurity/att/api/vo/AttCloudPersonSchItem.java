package com.zkteco.zkbiosecurity.att.api.vo;

import java.io.Serializable;
import java.util.List;

import com.zkteco.zkbiosecurity.att.calc.bo.AttExceptionBO;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 人员排班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 9:31
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class AttCloudPersonSchItem implements Serializable {
    private static final long serialVersionUID = 4068012148155133567L;

    // 排班日期
    private String date;

    // 排班类型（正常排班，临时排班）
    private String scheduleType;

    // 人员编号
    private String personPin;

    /**
     * 是否跨天：false 当前 true 跨天
     */
    private Boolean interDay = false;

    /**
     * 班次ID
     */
    private String shiftId;

    // 班次名称
    private String shiftName;

    // 班次时间段
    private String timeSlot;

    // 异常状态（0-正常，1-异常，2-请假，3-出差，4-外出，5-调休，6-补班，7-调班，8-节假日）
    private String exceptionState;

    // 必须签到签退，与时间段匹配（1：是，0：否）
    private String isMustSign;

    /**
     * 班次开始时间
     */
    private String startTime;

    /**
     * 班次结束时间
     */
    private String endTime;

    /**
     * 班次时长
     */
    private Integer timeLong;

    /**
     * 班次时长(小时)
     */
    private String timeLongHour;

    /**
     * 人员ID
     */
    private String personId;

    /**
     * 跨天规则 0 第一日，1 第二日
     */
    private String crossDayRule;

    /**
     * 班次时间段
     */
    private List<AttCloudTimeSlotItem> attCloudTimeSlotItemList;

    /**
     * 补签记录
     */
    private List<AttCloudAppSignItem> attCloudSignItemList;

    /**
     * 异常记录集合（加班、请假、外出、出差）
     */
    private List<AttExceptionBO> attExceptionList;

    /**
     * 工作类型（正常上班/周末加班/节假日加班）
     */
    private String workType;
}
