package com.zkteco.zkbiosecurity.att.constants;

/**
 * 考勤排班常量类
 * <p>
 * 定义相关排班常量，命名以Att开头，Constant结尾
 * 
 * <AUTHOR>
 * @version 1.0.0.0
 * @since 2017年11月7日 上午11:34:00
 */
public abstract class AttCommonSchConstant {

    /** 排班状态:已排班 **/
    public static final String SCH_STATUS_YES = "0";
    /** 排班状态:未排班 **/
    public static final String SCH_STATUS_NO = "1";

    public static final class GroupConstant {
        public static final String DEFAULT_NO_GROUP_ID = "NOGROUP";
    }

    /**
     * 排班类型
     */
    public static final class ScheduleType {
        /** 普通排班 */
        public static final short NORMAL_SCHEDULE = 0;
        /** 智能排班 */
        public static final short INTELLIGENT_SCHEDULE = 1;
    }

    /* ------------- 排班类型-------------*/
    /** 分组 {@code 0} */
    public static final Short SCH_TYPE_GROUP = 0;
    /** 部门 {@code 1} */
    public static final Short SCH_TYPE_DEPT = 1;
    /** 人员 {@code 2} */
    public static final Short SCH_TYPE_PERSON = 2;
}
