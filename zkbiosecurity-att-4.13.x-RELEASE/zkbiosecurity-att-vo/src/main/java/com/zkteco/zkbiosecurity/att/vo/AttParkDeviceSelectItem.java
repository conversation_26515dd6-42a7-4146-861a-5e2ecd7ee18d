package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 停车场当考勤、停车场设备选择对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 17:05
 * @since 1.0.0
 */
@GridConfig(operate = false)
@Setter
@Getter
@Accessors(chain = true)
public class AttParkDeviceSelectItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @GridColumn(columnType = "ra", width = "40", sortNo = 0)
    private String id;

    /**
     * 设备名称
     */
    @GridColumn(label = "park_device_name", width = "120", sortNo = 1)
    private String name;

    @GridColumn(label = "common_ipAddress", width = "120", sortNo = 2)
    private String ipAddress;

    /**
     * 通道ID
     */
    @GridColumn(columnType = "ra", width = "40")
    private String channelId;

    /**
     * 通道名称
     */
    @GridColumn(label = "park_channel_name", width = "120")
    private String channelName;

    /**
     * 进出口状态
     */
    @GridColumn(label = "park_channel_state", columnType = "dic", key = "parkChannelState")
    private Short status;
    /**
     * 判断是左列表noSelect、还是右列表select
     */
    private String type;

    /**
     * 当前选中的ids
     */
    private String selectId;

    private String entranceAreaId;

    public AttParkDeviceSelectItem() {
        super();
    }

    public AttParkDeviceSelectItem(Boolean equals) {
        super(equals);
    }

    public AttParkDeviceSelectItem(String id) {
        super(true);
        this.id = id;
    }
}
