package com.zkteco.zkbiosecurity.att.app.vo;

import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Setter
@Getter
@Accessors(chain = true)
public class AttAppSignItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 人员编号 */
    private String personPin;

    /** 姓名 */
    private String personName;

    /** 英文（lastName） */
    private String personLastName;

    /** 部门名称 */
    private String deptName;

    /** 区域名称 */
    private String areaName;

    /** 考勤日期时间 */
    private Date attDatetime;

    /** 考勤日期 */
    private String attDate;

    /** 考勤时间 */
    private String attTime;

    /** 新增签到地点属性 */
    private String attPlace;

    /** 标识 */
    private String mark;

    /** 经度 */
    private String longitude;

    /** 纬度 */
    private String latitude;
}