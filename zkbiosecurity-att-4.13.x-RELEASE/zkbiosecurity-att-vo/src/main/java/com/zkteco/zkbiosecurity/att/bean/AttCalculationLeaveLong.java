package com.zkteco.zkbiosecurity.att.bean;

import lombok.Getter;
import lombok.Setter;

/**
 * 时长计算返回对象
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 18:14 2020/8/18
 */
@Getter
@Setter
public class AttCalculationLeaveLong {

    /**
     * 请假时长(分钟)
     */
    private Integer leaveMinutes = 0;

    /**
     * 请假时长(小时)
     */
    private Float hour = 0f;


    /**
     * 请假时长(天)
     */
    private Float days = 0f;
}
