package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@From(after = "ATT_DEVICE t")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
@Getter
@Setter
@Accessors(chain = true)
public class AttSelectDeviceItem extends BaseItem {

    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /** 设备名称 */
    @Column(name = "t.DEV_NAME")
    @GridColumn(label = "common_dev_name", sortNo = 1, width = "120")
    private String devName;

    /** 设备型号 */
    @Column(name = "t.DEV_MODEL")
    @GridColumn(label = "common_dev_deviceModel", sortNo = 2, width = "90")
    private String devModel;

    /** 区域ID */
    @Column(name = "t.AUTH_AREA_ID", equalTag = "=")
    private String areaId;

    /** 区域名称 */
    @GridColumn(label = "base_area_name", width = "90", sortNo = 4)
    private String authAreaName;

    /** 设备序列号 */
    @Column(name = "t.DEV_SN")
    @GridColumn(label = "att_device_devSn", sortNo = 3, width = "*")
    private String devSn;

    /**
     * 启用状态
     */
    @Column(name = "t.STATUS")
    @GridColumn(label = "att_device_status", columnType = "custom", convert = "convertToIcon")
    private Boolean status;

    @Column(name = "t.ID", equalTag = "in")
    private String inId;

    @Column(name = "t.ID", equalTag = "not in")
    private String notInId;

    @Condition(value = "t.AUTH_AREA_ID IN (%s)", formatType = "quote")
    private String areaIdIn;
}
