package com.zkteco.zkbiosecurity.att.api.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 请假图片
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 16:48 2021/11/15
 * @version v1.0
 */
@Setter
@Getter
@Accessors(chain = true)
public class AttApiApplyLeaveImageItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 图片Base64编码 */
    @ApiModelProperty(value = "图片Base64编码", required = true, example = "base64Image")
    private String base64Image;
}
