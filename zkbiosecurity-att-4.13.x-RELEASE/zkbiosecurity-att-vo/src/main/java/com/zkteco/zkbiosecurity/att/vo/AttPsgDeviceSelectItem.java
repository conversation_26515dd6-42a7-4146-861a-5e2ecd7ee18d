package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 选择闸列表
 *
 * <AUTHOR>
 * @date 2021/7/12 10:16
 */
@GridConfig(operate = false, idField = "id")
@Setter
@Getter
@Accessors(chain = true)
public class AttPsgDeviceSelectItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 (闸ID)
     */
    @GridColumn(columnType = "ra", width = "40", sort = "na")
    private String id;

    /**
     * 闸编号
     */
    @GridColumn(label = "att_attPoint_gateNumber", width = "100")
    private Short gateNo;

    /**
     * 闸名称
     */
    @GridColumn(label = "att_attPoint_gateName", width = "100")
    private String name;

    /**
     * 所属设备ID
     */
    private String deviceId;

    /**
     * 所属设备序列号
     */
    @GridColumn(label = "common_dev_sn", width = "120")
    private String deviceSn;

    /**
     * 所属设备名称
     */
    @GridColumn(label = "common_dev_name", width = "120")
    private String deviceAlias;

    /**
     * 判断是左列表noSelect、还是右列表select
     */
    private String type;

    /**
     * 当前选中的ids
     */
    private String selectId;
    /**
     * 所属区域id
     */
    private String authAreaId;

    public AttPsgDeviceSelectItem() {
        super();
    }
}
