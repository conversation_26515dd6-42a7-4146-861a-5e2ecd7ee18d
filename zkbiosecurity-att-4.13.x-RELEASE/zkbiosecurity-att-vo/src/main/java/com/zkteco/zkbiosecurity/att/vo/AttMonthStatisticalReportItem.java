package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 人员汇总表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:27
 * @since 1.0.0
 */
@From(after = "ATT_RECORD t ")
@GroupBy(after = "t.PERS_PERSON_PIN")
@OrderBy(after = "t.PERS_PERSON_PIN DESC")
@GridConfig
@Setter
@Getter
@Accessors(chain = true)
public class AttMonthStatisticalReportItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    @GridColumn(show = false)
    private String id;

    // 人员
    /** 编号 */
    @Column(name = "t.PERS_PERSON_PIN")
    @GridColumn(label = "att_common_person", secHeader = "att_common_pin", sortNo = 1, width = "90", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String pin;

    /** 姓名 */
    @GridColumn(label = "#cspan", secHeader = "att_person_name", sortNo = 2, width = "90", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String name;

    /** 英文（lastName） */
    @GridColumn(label = "#cspan", secHeader = "att_person_lastName", showExpression = "#language!='zh_CN'", sortNo = 3,
        width = "90", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String lastName;

    // 部门
    /** ID */
    @GridColumn(show = false, sortNo = 4)
    @Condition(value = "t.AUTH_DEPT_ID")
    private String deptId;

    /** 编号 */
    @GridColumn(label = "pers_dept_deptNo", width = "90", sortNo = 5, show = false)
    private String deptCode;

    /** 部门名称 */
    @GridColumn(label = "pers_dept_deptName", sortNo = 6, width = "130")
    private String deptName;

    // 出勤
    /** 应该 */
    @GridColumn(label = "att_rule_arrive", secHeader = "att_statistical_should", sortNo = 7)
    private String shouldHour;

    /** 实际 */
    @GridColumn(label = "#cspan", secHeader = "att_statistical_actual", sortNo = 8)
    private String actualHour;

    /** 有效 */
    @GridColumn(label = "#cspan", secHeader = "att_statistical_valid", sortNo = 9)
    private String validHour;

    // 迟到
    /** 时长 */
    @GridColumn(label = "att_common_late", secHeader = "att_common_timeLongs", sortNo = 10)
    private String lateMinute;

    /** 次数 */
    @Column(name = "(COALESCE(SUM(t.LATE_COUNT_TOTAL),0))")
    @GridColumn(label = "#cspan", secHeader = "att_statistical_numberOfTimes", sortNo = 11)
    private String lateCountTotal;

    // 早退
    /** 时长 */
    @GridColumn(label = "att_common_early", secHeader = "att_common_timeLongs", sortNo = 12)
    private String earlyMinute;

    /** 次数 */
    @Column(name = "(COALESCE(SUM(t.EARLY_COUNT_TOTAL),0))")
    @GridColumn(label = "#cspan", secHeader = "att_statistical_numberOfTimes", sortNo = 13)
    private String earlyCount;

    // 加班
    /** 平时 */
    @GridColumn(label = "att_leftMenu_overtime", secHeader = "att_statistical_usually", sortNo = 14)
    private String overtimeUsualHour;

    /** 休息 */
    @GridColumn(label = "#cspan", secHeader = "att_statistical_rest", sortNo = 15)
    private String overtimeRestHour;

    /** 节日 */
    @GridColumn(label = "#cspan", secHeader = "att_statistical_holiday", sortNo = 16)
    private String overtimeHolidayHour;

    /** 合计 */
    @GridColumn(label = "#cspan", secHeader = "att_statistical_total", sortNo = 17)
    private String overtimeHour;

    /** 加班等级 */
    @GridColumn(label = "att_param_overTimeLevel", secHeader = "OT1",  sortNo = 18)
    private String overTimeOT1;

    @GridColumn(label = "#cspan", secHeader = "OT2", sortNo = 19)
    private String overTimeOT2;

    @GridColumn(label = "#cspan", secHeader = "OT3", sortNo = 20)
    private String overTimeOT3;

    /** 旷工 */
    @GridColumn(label = "att_common_absent", width = "130", sortNo = 60)
    private String absentHour;

    /** 请假 */
    @GridColumn(dynamicColumn = "attLeaveTypeDynamicColumn", sortNo = 61, label = "att_statistical_leaveDetail")
    private Map<String, Object> attMonthDetailReportLeaveHourMap = new HashMap<>();

    @Deprecated
    private String searchMonth;

    @Condition(value = "t.ATT_DATE", equalTag = ">=")
    private Date monthStart;

    @Condition(value = "t.ATT_DATE", equalTag = "<=")
    private Date monthEnd;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    private String likeName;

    @Condition(value = "t.PERS_PERSON_PIN", equalTag = "in")
    private String inPersonPin;

    private String leaveMinuteTotal;

    /** 应该(带单位) */
    private String should;

    /** 实际(带单位) */
    private String actual;

    /** 有效(带单位) */
    private String valid;

    /** 迟到(带单位) */
    private String late;

    /** 早退(带单位) */
    private String early;

    /** 旷工 */
    private String absent;

    /** 平时(带单位) */
    private String overtimeUsual;

    /** 休息(带单位) */
    private String overtimeRest;

    /** 节日(带单位) */
    private String overtimeHoliday;

    /** 加班合计(带单位) */
    private String overtime;

    /** 请假(带单位) */
    private String leave;

    /** 出差(带单位) */
    private String trip;

    /** 外出(带单位) */
    private String out;

    /** 默认构造方法 */
    public AttMonthStatisticalReportItem() {
        super();
        this.id = UUID.randomUUID().toString();
    }
}
