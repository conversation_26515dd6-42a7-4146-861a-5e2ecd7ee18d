package com.zkteco.zkbiosecurity.att.constants;

import java.util.*;

import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

/**
 * 
 * @version V1.0
 * @date Created In 10:31 2018/12/5
 */
public class AttConstant {
    /** 逗号常量 {@code ,}. */
    public static final String COMMA = ",";
    /** The empty String {@code ""}. */
    public static final String EMPTY = "";
    /** 常用的默认传入数值 -1 {@code "-1"}. */
    public static final String COMM_DEF_VALUE = "-1";
    /** 导出类型值为 3 {@code "3"} */
    public static final String EXPORTTYPE = "3";
    /** 双列表中 类型判断值未 noSelected {@code "noSelected"} */
    public static final String NOSELECT = "noSelected";
    /** 考勤原始记录表——mark字段的人证标志 */
    public static final String ATTTRANSACTION_MARK_PID = "pid";
    /** 考勤照片保存路径 */
    public static final String TRANSACTION_PHOTP_PTAH = "upload/att/transactionPhoto/";

    /* ------------- 考勤实时计算-------------*/
    /** 【实时计算】时间范围，默认只计算上个月1号以后的打卡事件 */
    public static final Short ATT_EVENTTIME_BEFORE = -2;
    /** 【实时计算】事件标记(格式：att:realTimeEvent:日期) */
    public static final String ATT_REALTIMEEVENT = "att:realTimeEvent:";
    /* 考勤实时计算的基础信息的缓存标识（格式：att:base:类型:ID） */
    /** 【实时计算】人员信息（格式：att:base_person:pin） */
    public static final String ATT_BASE_PERSON = "att:base:person:";
    /** 【实时计算】假种 */
    public static final String ATT_BASE_LEAVETYPEDATA = "att:base:leaveType";
    /** 【实时计算】节假日 */
    public static final String ATT_BASE_HOLIDAYDATA = "att:base:holidayData";
    /** 【实时计算】时间段 */
    public static final String ATT_BASE_TIMESLOTDATA = "att:base:timeSlotData";
    /** 【实时计算】班次 */
    public static final String ATT_BASE_SHIFTDATA = "att:base:shiftData";
    /** 【实时计算】通用排班对象--分组(临时)/部门(临时)/人员临时排班 */
    public static final String ATT_BASE_COMMSCHDATA = "att:base:commSchData";
    /* 考勤实时计算的异常信息的缓存标识（格式：att:exception:类型:ID） */
    /** 【实时计算】补签 */
    public static final String ATT_EXCEPTION_SIGNDATA = "att:exception:signData";
    /** 【实时计算】请假 */
    public static final String ATT_EXCEPTION_LEAVEDATA = "att:exception:leaveData:";
    /** 【实时计算】加班 */
    public static final String ATT_EXCEPTION_OVERTIMEDATA = "att:exception:overTimeData:";
    /** 【实时计算】调休/补班 */
    public static final String ATT_EXCEPTION_ADJUSTDATA = "att:exception:adjustData";
    /** 【实时计算】调班 */
    public static final String ATT_EXCEPTION_CLASSDATA = "att:exception:classData";

    /* ------------- 考勤规则-------------*/
    /** 自定义规则类型-分组规则 */
    public static final String ATT_RULE_TYPE_GROUP = "0";
    /** 自定义规则类型-部门规则 */
    public static final String ATT_RULE_TYPE_DEPT = "1";
    /** 自定义规则 */
    public static final String CUSTOM_RULE_KEY = "att:custom_rule_key";

    /* ------------- 指纹版本-------------*/
    /** 生物识别模板表的版本(9.0和10.0) */
    public static final String FP_BIO_VERSION_10 = "10";
    /** 生物识别模板表的版本(12) */
    public static final String FP_BIO_VERSION_12 = "12";

    /* ------------- 调休补班类型-------------*/
    /** 调休 {@code 0} */
    public static final short ADJUST_TYPE_OFF = 0;
    /** 补班 {@code 1} */
    public static final short ADJUST_TYPE_CLASSES = 1;

    /* ------------- 调班调整类型-------------*/
    /** 个人同日期调班 {@code 0} */
    public static final short CLASS_TYPE_SAMETIMEMOVE = 0;
    /** 个人不同日期调班 {@code 1} */
    public static final short CLASS_TYPE_DIFFTIMEMOVE = 1;
    /** 二人对调 {@code 2} */
    public static final short CLASS_TYPE_PEOPLEMOVE = 2;

    /** 操作日志对应的操作类型 */
    public static final Map<Integer, String> ATTOPLOG_OPTYPE = new HashMap<Integer, String>();

    static {
        ATTOPLOG_OPTYPE.put(0, I18nUtil.i18nCode("att_device_op_log_opType_0"));
        ATTOPLOG_OPTYPE.put(1, I18nUtil.i18nCode("att_device_op_log_opType_1"));
        ATTOPLOG_OPTYPE.put(2, I18nUtil.i18nCode("att_device_op_log_opType_2"));
        ATTOPLOG_OPTYPE.put(3, I18nUtil.i18nCode("att_device_op_log_opType_3"));
        ATTOPLOG_OPTYPE.put(4, I18nUtil.i18nCode("att_device_op_log_opType_4"));
        ATTOPLOG_OPTYPE.put(5, I18nUtil.i18nCode("att_device_op_log_opType_5"));
        ATTOPLOG_OPTYPE.put(6, I18nUtil.i18nCode("att_device_op_log_opType_6"));
        ATTOPLOG_OPTYPE.put(7, I18nUtil.i18nCode("att_device_op_log_opType_7"));
        ATTOPLOG_OPTYPE.put(8, I18nUtil.i18nCode("att_device_op_log_opType_8"));
        ATTOPLOG_OPTYPE.put(9, I18nUtil.i18nCode("att_device_op_log_opType_9"));
        ATTOPLOG_OPTYPE.put(10, I18nUtil.i18nCode("att_device_op_log_opType_10"));
        ATTOPLOG_OPTYPE.put(11, I18nUtil.i18nCode("att_device_op_log_opType_11"));
        ATTOPLOG_OPTYPE.put(12, I18nUtil.i18nCode("att_device_op_log_opType_12"));
        ATTOPLOG_OPTYPE.put(13, I18nUtil.i18nCode("att_device_op_log_opType_13"));
        ATTOPLOG_OPTYPE.put(14, I18nUtil.i18nCode("att_device_op_log_opType_14"));
        ATTOPLOG_OPTYPE.put(15, I18nUtil.i18nCode("att_device_op_log_opType_15"));
        ATTOPLOG_OPTYPE.put(16, I18nUtil.i18nCode("att_device_op_log_opType_16"));
        ATTOPLOG_OPTYPE.put(17, I18nUtil.i18nCode("att_device_op_log_opType_17"));
        ATTOPLOG_OPTYPE.put(18, I18nUtil.i18nCode("att_device_op_log_opType_18"));
        ATTOPLOG_OPTYPE.put(19, I18nUtil.i18nCode("att_device_op_log_opType_19"));
        ATTOPLOG_OPTYPE.put(20, I18nUtil.i18nCode("att_device_op_log_opType_20"));
        ATTOPLOG_OPTYPE.put(21, I18nUtil.i18nCode("att_device_op_log_opType_21"));
        ATTOPLOG_OPTYPE.put(22, I18nUtil.i18nCode("att_device_op_log_opType_22"));
        ATTOPLOG_OPTYPE.put(23, I18nUtil.i18nCode("att_device_op_log_opType_23"));
        ATTOPLOG_OPTYPE.put(24, I18nUtil.i18nCode("att_device_op_log_opType_24"));
        ATTOPLOG_OPTYPE.put(25, I18nUtil.i18nCode("att_device_op_log_opType_25"));
        ATTOPLOG_OPTYPE.put(26, I18nUtil.i18nCode("att_device_op_log_opType_26"));
        ATTOPLOG_OPTYPE.put(27, I18nUtil.i18nCode("att_device_op_log_opType_27"));
        ATTOPLOG_OPTYPE.put(28, I18nUtil.i18nCode("att_device_op_log_opType_28"));
        ATTOPLOG_OPTYPE.put(29, I18nUtil.i18nCode("att_device_op_log_opType_29"));
        ATTOPLOG_OPTYPE.put(30, I18nUtil.i18nCode("att_device_op_log_opType_30"));
        ATTOPLOG_OPTYPE.put(31, I18nUtil.i18nCode("att_device_op_log_opType_31"));
        ATTOPLOG_OPTYPE.put(32, I18nUtil.i18nCode("att_device_op_log_opType_32"));

        ATTOPLOG_OPTYPE.put(34, I18nUtil.i18nCode("att_device_op_log_opType_34"));
        ATTOPLOG_OPTYPE.put(35, I18nUtil.i18nCode("att_device_op_log_opType_35"));
        ATTOPLOG_OPTYPE.put(36, I18nUtil.i18nCode("att_device_op_log_opType_36"));
        ATTOPLOG_OPTYPE.put(37, I18nUtil.i18nCode("att_device_op_log_opType_37"));
        ATTOPLOG_OPTYPE.put(38, I18nUtil.i18nCode("att_device_op_log_opType_38"));
        ATTOPLOG_OPTYPE.put(39, I18nUtil.i18nCode("att_device_op_log_opType_39"));
        ATTOPLOG_OPTYPE.put(40, I18nUtil.i18nCode("att_device_op_log_opType_40"));
        ATTOPLOG_OPTYPE.put(41, I18nUtil.i18nCode("att_device_op_log_opType_41"));
        ATTOPLOG_OPTYPE.put(42, I18nUtil.i18nCode("att_device_op_log_opType_42"));
        ATTOPLOG_OPTYPE.put(43, I18nUtil.i18nCode("att_device_op_log_opType_43"));

        ATTOPLOG_OPTYPE.put(53, I18nUtil.i18nCode("att_device_op_log_opType_53"));
        ATTOPLOG_OPTYPE.put(54, I18nUtil.i18nCode("att_device_op_log_opType_54"));
        ATTOPLOG_OPTYPE.put(55, I18nUtil.i18nCode("att_device_op_log_opType_55"));
        ATTOPLOG_OPTYPE.put(56, I18nUtil.i18nCode("att_device_op_log_opType_56"));

        ATTOPLOG_OPTYPE.put(68, I18nUtil.i18nCode("att_device_op_log_opType_68"));
        ATTOPLOG_OPTYPE.put(69, I18nUtil.i18nCode("att_device_op_log_opType_69"));
        ATTOPLOG_OPTYPE.put(70, I18nUtil.i18nCode("att_device_op_log_opType_70"));
        ATTOPLOG_OPTYPE.put(71, I18nUtil.i18nCode("att_device_op_log_opType_71"));

        ATTOPLOG_OPTYPE.put(76, I18nUtil.i18nCode("att_device_op_log_opType_76"));
        ATTOPLOG_OPTYPE.put(77, I18nUtil.i18nCode("att_device_op_log_opType_77"));
        ATTOPLOG_OPTYPE.put(78, I18nUtil.i18nCode("att_device_op_log_opType_78"));
        ATTOPLOG_OPTYPE.put(79, I18nUtil.i18nCode("att_device_op_log_opType_79"));
        ATTOPLOG_OPTYPE.put(80, I18nUtil.i18nCode("att_device_op_log_opType_80"));
        ATTOPLOG_OPTYPE.put(81, I18nUtil.i18nCode("att_device_op_log_opType_81"));
        ATTOPLOG_OPTYPE.put(82, I18nUtil.i18nCode("att_device_op_log_opType_82"));
        ATTOPLOG_OPTYPE.put(83, I18nUtil.i18nCode("att_device_op_log_opType_83"));

        ATTOPLOG_OPTYPE.put(87, I18nUtil.i18nCode("att_device_op_log_opType_87"));
        ATTOPLOG_OPTYPE.put(88, I18nUtil.i18nCode("att_device_op_log_opType_88"));
        ATTOPLOG_OPTYPE.put(89, I18nUtil.i18nCode("att_device_op_log_opType_89"));
        ATTOPLOG_OPTYPE.put(90, I18nUtil.i18nCode("att_device_op_log_opType_90"));
        ATTOPLOG_OPTYPE.put(91, I18nUtil.i18nCode("att_device_op_log_opType_91"));
        ATTOPLOG_OPTYPE.put(92, I18nUtil.i18nCode("att_device_op_log_opType_92"));

        ATTOPLOG_OPTYPE.put(100, I18nUtil.i18nCode("att_device_op_log_opType_100"));
        ATTOPLOG_OPTYPE.put(101, I18nUtil.i18nCode("att_device_op_log_opType_101"));
        ATTOPLOG_OPTYPE.put(102, I18nUtil.i18nCode("att_device_op_log_opType_102"));
        ATTOPLOG_OPTYPE.put(103, I18nUtil.i18nCode("att_device_op_log_opType_103"));
        ATTOPLOG_OPTYPE.put(104, I18nUtil.i18nCode("att_device_op_log_opType_104"));
        ATTOPLOG_OPTYPE.put(105, I18nUtil.i18nCode("att_device_op_log_opType_105"));
        ATTOPLOG_OPTYPE.put(106, I18nUtil.i18nCode("att_device_op_log_opType_106"));
        ATTOPLOG_OPTYPE.put(107, I18nUtil.i18nCode("att_device_op_log_opType_107"));
        ATTOPLOG_OPTYPE.put(108, I18nUtil.i18nCode("att_device_op_log_opType_108"));
        ATTOPLOG_OPTYPE.put(109, I18nUtil.i18nCode("att_device_op_log_opType_109"));
        ATTOPLOG_OPTYPE.put(110, I18nUtil.i18nCode("att_device_op_log_opType_110"));
        ATTOPLOG_OPTYPE.put(111, I18nUtil.i18nCode("att_device_op_log_opType_111"));
        ATTOPLOG_OPTYPE.put(112, I18nUtil.i18nCode("att_device_op_log_opType_112"));
        ATTOPLOG_OPTYPE.put(113, I18nUtil.i18nCode("att_device_op_log_opType_113"));
        ATTOPLOG_OPTYPE.put(114, I18nUtil.i18nCode("att_device_op_log_opType_114"));
        ATTOPLOG_OPTYPE.put(115, I18nUtil.i18nCode("att_device_op_log_opType_115"));
        ATTOPLOG_OPTYPE.put(116, I18nUtil.i18nCode("att_device_op_log_opType_116"));
        ATTOPLOG_OPTYPE.put(117, I18nUtil.i18nCode("att_device_op_log_opType_117"));
        ATTOPLOG_OPTYPE.put(118, I18nUtil.i18nCode("att_device_op_log_opType_118"));
    }

    /** 操作日志对应的报警说明 */
    public static final Map<Integer, String> ATTOPLOG_ALARM = new HashMap<Integer, String>();
    static {
        ATTOPLOG_ALARM.put(50, "Door Close Detected");
        ATTOPLOG_ALARM.put(51, "Door Open Detected");
        ATTOPLOG_ALARM.put(53, "Out Door Button");
        ATTOPLOG_ALARM.put(54, "Door Broken Accidentally");
        ATTOPLOG_ALARM.put(55, "Machine Been Broken");
        ATTOPLOG_ALARM.put(58, "Try Invalid Verification");
        ATTOPLOG_ALARM.put(65535, "Alarm Cancelled");
    }

    /* ------------- 流程类型-------------*/
    /** 补签类型 **/
    public static final String FLOW_TYPE_SIGN = "sign";
    /** 请假类型 **/
    public static final String FLOW_TYPE_LEAVE = "leave";
    /** 加班类型 **/
    public static final String FLOW_TYPE_OVERTIME = "overtime";
    /** 出差类型 **/
    public static final String FLOW_TYPE_TRIP = "trip";
    /** 外出类型 **/
    public static final String FLOW_TYPE_OUT = "out";
    /** 流程类型 adjust 调休补班 */
    public static final String FLOW_TYPE_ADJUST = "adjust";
    /** 流程类型 classShift 调班 */
    public static final String FLOW_TYPE_CLASS_SHIFT = "classShift";

    public static final List<String> ATT_FLOW_TYPE_LIST;
    static {
        List<String> list = new ArrayList<>();
        list.add(FLOW_TYPE_SIGN);
        list.add(FLOW_TYPE_LEAVE);
        list.add(FLOW_TYPE_OVERTIME);
        list.add(FLOW_TYPE_TRIP);
        list.add(FLOW_TYPE_OUT);
        list.add(FLOW_TYPE_ADJUST);
        list.add(FLOW_TYPE_CLASS_SHIFT);
        ATT_FLOW_TYPE_LIST = Collections.unmodifiableList(list);
    }

    /* ------------- 流程状态-------------*/
    /** 流程状态 创建/待审批/进行中 */
    public static final String FLOW_STATUS_CREATE = "0";
    /** 流程状态 暂停/待激活 */
    public static final String FLOW_STATUS_PAUSE = "1";
    /** 流程状态 结束-完成 */
    public static final String FLOW_STATUS_COMPLETE = "2";
    /** 流程状态 撤销 */
    public static final String FLOW_STATUS_REVOKE = "3";
    /** 流程状态 删除 */
    public static final String FLOW_STATUS_DELETE = "4";
    /** 流程状态 驳回 */
    public static final String FLOW_STATUS_REFUSE = "5";
    /** 流程状态 异常结束 */
    public static final String FLOW_STATUS_ABNORMAL_END = "6";

    /* ------------- 考勤状态-------------*/
    /** 考勤状态:(0)正常 */
    public static final Integer ATT_STATUS_NORMAL = 0;
    /** 考勤状态:(1)异常 */
    public static final Integer ATT_STATUS_EXCEPTION = 1;
    /** 考勤状态:(2)请假 */
    public static final Integer ATT_STATUS_LEAVE = 2;
    /** 考勤状态:(3)休息 */
    public static final Integer ATT_STATUS_RESET = 3;
    /** 考勤状态:(4)漏卡 */
    public static final Integer ATT_STATUS_LACK_CARD = 4;
    /** 考勤状态:(5)加班 */
    public static final Integer ATT_STATUS_OVERTIME = 5;
    /** 考勤状态:(6)外出 */
    public static final Integer ATT_STATUS_OUT = 6;
    /** 考勤状态:(7)出差 */
    public static final Integer ATT_STATUS_TRIP = 7;
    /** 考勤状态 未排班 */
    public static final Integer ATT_STATUS_NO_SHIFT = 8;
    /** 考勤状态 调休 */
    public static final Integer ATT_STATUS_TAKE_OFF = 9;
    /** 考勤状态 补班 */
    public static final Integer ATT_STATUS_TAKE_CLASS = 10;
    /** 考勤状态 调班 */
    public static final Integer ATT_STATUS_ADJUST_CLASS = 11;

    /* ------------- 考勤结果类型-------------*/
    /** 节假日(-70) */
    public static final Integer ATT_EXCEPTION_HOLIDAY = -70;
    /** 调班-两人对调(-60) */
    public static final Integer ATT_EXCEPTION_CHANGE_SHIFT_TWO = -60;
    /** 调班-个人不同日期(-50) */
    public static final Integer ATT_EXCEPTION_CHANGE_SHIFT_DIFF_DATE = -50;
    /** 个人同日期调班-（已排班且休息、已排班且不休息）(-40) */
    public static final Integer ATT_EXCEPTION_CHANGE_SHIFT_SAME_DATE = -40;
    /** 补班(-30) */
    public static final Integer ATT_EXCEPTION_ADD_SHIFT = -30;
    /** 调休(-20) */
    public static final Integer ATT_EXCEPTION_DAY_OFF = -20;
    /** 未排班(-10) */
    public static final Integer ATT_EXCEPTION_NOT_SHIFT = -10;
    /** 排班且休息(0) */
    public static final Integer ATT_EXCEPTION_REST = 0;
    /** 旷工(10) */
    public static final Integer ATT_EXCEPTION_ABSENT = 10;
    /** 加班(20) */
    public static final Integer ATT_EXCEPTION_OVER = 20;
    /** 外出 (30) */
    public static final Integer ATT_EXCEPTION_OUT = 30;
    /** 出差 (40) */
    public static final Integer ATT_EXCEPTION_TRIP = 40;
    /** 请假 (50) */
    public static final Integer ATT_EXCEPTION_LEAVE = 50;
    /** 未签到 (60) */
    public static final Integer ATT_EXCEPTION_NOT_SIGN_IN = 60;
    /** 未签退(70) */
    public static final Integer ATT_EXCEPTION_NOT_SIGN_OUT = 70;
    /** 迟到(80) */
    public static final Integer ATT_EXCEPTION_LATE = 80;
    /** 早退(90) */
    public static final Integer ATT_EXCEPTION_EARLY = 90;
    /** 应到/实到 (100) */
    public static final Integer ATT_EXCEPTION_NORMAL = 100;

    /** 固件保存路径 */
    public static final String ATT_FIREWARE_PATH = "/upload/att/fireware";

    /** V5000排班状态与H5的转换 */
    public static final Map<Integer, Integer> H5_PERSON_SCH_TYPE_MAP;
    static {
        Map<Integer, Integer> map = new HashMap<>();
        /** 正常 */
        map.put(0, ATT_STATUS_NORMAL);
        /** 异常 */
        map.put(1, ATT_STATUS_EXCEPTION);
        /** 请假 */
        map.put(2, ATT_STATUS_LEAVE);
        /** 出差 */
        map.put(3, ATT_STATUS_TRIP);
        /** 外出 */
        map.put(4, ATT_STATUS_OUT);
        /** 休息 */
        map.put(8, ATT_STATUS_RESET);
        H5_PERSON_SCH_TYPE_MAP = Collections.unmodifiableMap(map);
    }

    /** 考勤remark标识考勤状态(0-正常、1-异常、2请假) */
    public static final Map<Integer, Integer> exceptionRemarkMap;
    static {
        Map<Integer, Integer> map = new HashMap<>();
        /** 节假日 */
        map.put(ATT_EXCEPTION_HOLIDAY, ATT_STATUS_RESET);
        /** 调班-两人对调 */
        map.put(ATT_EXCEPTION_CHANGE_SHIFT_TWO, ATT_STATUS_ADJUST_CLASS);
        /** 调班-个人不同日期 */
        map.put(ATT_EXCEPTION_CHANGE_SHIFT_DIFF_DATE, ATT_STATUS_ADJUST_CLASS);
        /** 个人同日期调班-（已排班且休息、已排班且不休息） */
        map.put(ATT_EXCEPTION_CHANGE_SHIFT_SAME_DATE, ATT_STATUS_ADJUST_CLASS);
        /** 补班 */
        map.put(ATT_EXCEPTION_ADD_SHIFT, ATT_STATUS_TAKE_CLASS);
        /** 调休 */
        map.put(ATT_EXCEPTION_DAY_OFF, ATT_STATUS_TAKE_OFF);
        /** 未排班 */
        map.put(ATT_EXCEPTION_NOT_SHIFT, ATT_STATUS_NO_SHIFT);
        /** 排班且休息 */
        map.put(ATT_EXCEPTION_REST, ATT_STATUS_RESET);
        /** 旷工 */
        map.put(ATT_EXCEPTION_ABSENT, ATT_STATUS_EXCEPTION);
        /** 加班 */
        map.put(ATT_EXCEPTION_OVER, ATT_STATUS_OVERTIME);
        /** 外出 */
        map.put(ATT_EXCEPTION_OUT, ATT_STATUS_OUT);
        /** 出差 */
        map.put(ATT_EXCEPTION_TRIP, ATT_STATUS_TRIP);
        /** 请假 */
        map.put(ATT_EXCEPTION_LEAVE, ATT_STATUS_LEAVE);
        /** 未签到 */
        map.put(ATT_EXCEPTION_NOT_SIGN_IN, ATT_STATUS_LACK_CARD);
        /** 未签退 */
        map.put(ATT_EXCEPTION_NOT_SIGN_OUT, ATT_STATUS_LACK_CARD);
        /** 迟到 */
        map.put(ATT_EXCEPTION_LATE, ATT_STATUS_EXCEPTION);
        /** 早退 */
        map.put(ATT_EXCEPTION_EARLY, ATT_STATUS_EXCEPTION);
        /** 应到/实到 */
        map.put(ATT_EXCEPTION_NORMAL, ATT_STATUS_NORMAL);
        exceptionRemarkMap = Collections.unmodifiableMap(map);
    }

    /** 地图 */
    /** 谷歌地图 */
    public static final String ATT_MAP_GOOGLEMAP = "googleMap";
    /** 高德地图 */
    public static final String ATT_MAP_GAODEMAP = "gaodeMap";
    /** 百度 */
    public static final String ATT_MAP_BAIDUMAP = "baiduMap";

    /** 最大加班时长限制类型 **/
    /** 无限制 **/
    public static final String MAX_OVERTIME_TYPE_NO_LIMITED = "notLimited";
    /** 按周 **/
    public static final String MAX_OVERTIME_TYPE_NO_WEEK = "week";
    /** 按月 **/
    public static final String MAX_OVERTIME_TYPE_NO_MONTH = "month";

    /** 未签到签退考勤规则 **/
    /** 未签到记为缺勤 **/
    public static final String ATT_NOSIGNIN_COUNTTYPE_ABSENT = "absent";
    /** 未签到记为迟到 **/
    public static final String ATT_NOSIGNIN_COUNTTYPE_LATE = "late";
    /** 未签到记为不完整 **/
    public static final String ATT_NOSIGNIN_COUNTTYPE_INCOMPLETE = "incomplete";
    /** 未签退记为缺勤 **/
    public static final String ATT_NOSIGNOFF_COUNTTYPE_ABSENT = "absent";
    /** 未签退记为早退 **/
    public static final String ATT_NOSIGNOFF_COUNTTYPE_EARLY = "early";
    /** 未签退记为不完整 **/
    public static final String ATT_NOSIGNOFF_COUNTTYPE_INCOMPLETE = "incomplete";

    /** 周期起始日期 */
    public static final String ATT_PERIODSTARTMODE_BYPERIOD = "0";
    /** 排班开始日期 */
    public static final String ATT_PERIODSTARTMODE_BYSCH = "1";

    /* ------------- 时段类型-------------*/
    /** 正常时间段 {@code 0} */
    public static final Short PERIODTYPE_NORMAL = 0;
    /** 弹性时间段 {@code 1} */
    public static final Short PERIODTYPE_ELASTIC = 1;

    /** 缓存key-规则 */
    public static final String CACHE_KEY_RULE_PARAM_BEAN = "att:calculate:ruleParamBean";
    /** 缓存key-节假日 */
    public static final String CACHE_KEY_HOLIDAY_SET = "att:calculate:holidaySet";
    /** 缓存key-假种 */
    public static final String CACHE_KEY_LEAVE_TYPE_ITEM_MAP = "att:calculate:leaveTypeItemMap";
    /** 缓存key-班次集合 */
    public static final String CACHE_KEY_SHIFT_ITEM_MAP = "att:calculate:shiftItemMap";
    /** 缓存key-时间段集合 */
    public static final String CACHE_KEY_TIME_SLOT_ITEM_MAP = "att:calculate:timeSlotItemMap";

    /** 实时点名：签到 */
    public static final Integer ROLLCALL_SIGNIN = 0;
    /** 实时点名：未签到 */
    public static final Integer ROLLCALL_NOSIGNIN = 1;
    /** 实时点名：请假 */
    public static final Integer ROLLCALL_LEAVE = 2;

    /** 考勤点拉取记录 */
    public static final String ATT_ATTENDPOINT = "att:attendPoint:";
    /** 考勤点是否在拉取记录标记 */
    public static final String ATT_ATTENDPOINT_TRANSACTION = "att:transactionFlag:";

    /* ------------- 计算数据转换-------------*/
    /** 最小单位 */
    /** 分钟 */
    public static final String ATT_CONVERT_UNIT_MINUTE = "minute";
    /** 工作日 */
    public static final String ATT_CONVERT_UNIT_DAY = "day";
    /** 小时 */
    public static final String ATT_CONVERT_UNIT_HOUR = "hour";

    /** 舍入控制 */
    /** 向下（舍弃） */
    public static final String ATT_CONVERT_ABORT = "abort";
    /** 四舍五入 */
    public static final String ATT_CONVERT_ROUNDING = "rounding";
    /** 向上（进位） */
    public static final String ATT_CONVERT_CARRY = "carry";

    /** 标记 */
    /** 参数设置 */
    public static final String ATT_CONVERT_MARK_PARAMS = "params";
    /** 假种 */
    public static final String ATT_CONVERT_MARK_LEAVETYPE = "leaveType";
    /** 无感考勤推送消息间隔时间 */
    public static final long ATT_PUSH_TRANSACTION_MESSAGE_FREQUENCY = 10 * 60 * 1000L;
    /** 人员无感考勤打卡记录 */
    public static final String ATT_PERSON_TRANSACTION_MAP = "att:transaction:personTransactionMap";

    /** 通道当考勤 */
    public static final Short ATT_NORMAL_PASS_RECORD = 1;
    public static final Short ATT_VERIFICATION_RECORD = 2;
    public static final Short ATT_NORMAL_PASS_NO = 4001;
    public static final Short ATT_VERIFICATION_PASS_NO = 0;

    // 实时点击发布、订阅主题
    public static final String ATT_REALTIMEPUSH_TOPIC = "attRealTimePushTopic";
}
