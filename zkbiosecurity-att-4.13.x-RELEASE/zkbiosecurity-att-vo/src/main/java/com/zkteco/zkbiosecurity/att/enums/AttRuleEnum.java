package com.zkteco.zkbiosecurity.att.enums;

/**
 * 考勤规则枚举
 * <p>
 * 定义相关规则枚举，命名以Att开头，Enum结尾
 * 
 * <AUTHOR>
 * @version 0.0.0.1
 * @since 2017年11月7日 下午3:07:06
 */
public enum AttRuleEnum {
    /** 上班签到取卡记录原则 */
    SignIn("att.baseRule.signIn", "0", "1"),
    /** 下班签到取卡记录原则 */
    SignOut("att.baseRule.signOut", "0", "1"),
    /** 最短的考勤时段应大于（分钟） */
    ShortestMinutes("att.baseRule.shortestMinutes"),
    /** 最长的考勤时段应小于（分钟） */
    LongestMinutes("att.baseRule.longestMinutes"),
    /** 迟到且早退算旷工 */
    LateAndEarly("att.baseRule.lateAndEarly", "false", "true"),
    /** 是否统计加班 */
    CountOvertime("att.baseRule.countOvertime", "false", "true"),
    /** 查找排班记录顺序 */
    FindSchSort("att.baseRule.findSchSort", "0", "1"),
    /** 智能找班原则 */
    SmartFindClass("att.baseRule.smartFindClass", "0", "1"),
    /** 班次时间段跨天时，考勤计算结果 */
    CrossDay("att.baseRule.crossDay", "0", "1"),
    /* *//** 最短的加班时长应大于（分钟） *//*
                              ShortestOvertimeMinutes("att.baseRule.shortestOvertimeMinutes"),*/
    /** 弹性时长计算方式 */
    ElasticCal("att.baseRule.elasticCal", "0", "1"),
    /** 休息时段是否打卡 **/
    SignBreakTime("att.baseRule.signBreakTime", "false", "true"),
    /** 未签到计为类型（缺勤、迟到、不完整） */
    NoSignInCountType("att.baseRule.noSignInCountType", "absent", "late", "incomplete"),
    /** 未签到记为迟到分钟数 */
    noSignInCountLateMinute("att.baseRule.noSignInCountLateMinute"),
    /** 未签退计为类型（缺勤、早退、不完整） */
    noSignOffCountType("att.baseRule.noSignOffCountType", "absent", "early", "incomplete"),
    /** 未签退记为早退分钟数 */
    noSignOffCountEarlyMinute("att.baseRule.noSignOffCountEarlyMinute"),

    /** 小数点精确位数 */
    Decimal("att.countConvert.decimal", "0", "1", "2"),

    /** 授权设备-新增设备自动添加 */
    AutoAdd("att.device.autoAdd", "0", "1"),
    /** 授权设备-仅接收数据库中存在的人员 */
    ReceivePersonOnlyDb("att.device.receivePersonOnlyDb", "0", "1"),
    /** 短消息uid */
    Uid("att.sms.uid", "ʘ");

    private String key;
    private String valueOne;
    private String valueTwo;
    private String valueThree;

    private AttRuleEnum(String key) {
        this.key = key;
    }

    private AttRuleEnum(String key, String valueOne) {
        this.key = key;
        this.valueOne = valueOne;
    }

    private AttRuleEnum(String key, String valueOne, String valueTwo) {
        this.key = key;
        this.valueOne = valueOne;
        this.valueTwo = valueTwo;
    }

    private AttRuleEnum(String key, String valueOne, String valueTwo, String valueThree) {
        this.key = key;
        this.valueOne = valueOne;
        this.valueTwo = valueTwo;
        this.valueThree = valueThree;
    }

    public String getKey() {
        return key;
    }

    public String getValueOne() {
        return valueOne;
    }

    public String getValueTwo() {
        return valueTwo;
    }

    public String getValueThree() {
        return valueThree;
    }

}
