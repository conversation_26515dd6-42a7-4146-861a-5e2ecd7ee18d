package com.zkteco.zkbiosecurity.att.api.vo;

import java.io.Serializable;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 移动端考勤日历--人员考勤详情
 * 
 * @date 2020/11/5 14:31
 * @return
 */
@Getter
@Setter
@Accessors(chain = true)
public class AttApiPersonSchItem implements Serializable {

    private static final long serialVersionUID = 1L;
    /** 日期 2018-09-01 */
    private String date;
    /** 班次ID */
    private String shiftId;
    /** 班次名称 */
    private String shiftName;
    /** 排班状态：(0:正常排班、1:排班且休息、2:节假日、3:请假、4:未排班) */
    private String schStatus;
    /** 排班状态名称(0:正常排班、1:排班且休息、2:节假日、3:请假、4:未排班) */
    private String schStatusName;
    /** 考勤状态(0:正常、1:异常--迟到、早退、缺卡) */
    private Integer attStatus;

    /** 时间段集合 */
    private List<AttApiWorkTimeItem> workTimes;
    /** 考勤异常集合 */
    private List<AppAttExceptionItem> attExceptionList;
}
