package com.zkteco.zkbiosecurity.att.api.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 申请加班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/11/16 17:16
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain = true)
@ApiModel(description = "AttApiApplyOvertimeItem")
public class AttApiApplyOvertimeItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 人员编号 */
    @ApiModelProperty(value = "人员编号", required = true, example = "'1'")
    private String personPin;

    /** 开始时间 */
    @ApiModelProperty(value = "开始时间", required = true, example = "2021-11-19 13:00:00")
    private String startDatetime;

    /** 结束时间 */
    @ApiModelProperty(value = "结束时间", required = true, example = "2021-11-19 18:00:00")
    private String endDatetime;

    /** 备注 */
    @ApiModelProperty(value = "备注", example = "api")
    private String remark;
}