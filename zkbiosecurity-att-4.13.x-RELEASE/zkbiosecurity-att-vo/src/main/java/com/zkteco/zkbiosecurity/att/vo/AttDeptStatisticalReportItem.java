package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 *  部门汇总表
 *
 * <AUTHOR> href:"mailto:<EMAIL>">yuanjun.zou</a>
 * @version v1.0
 */
@From(after = "ATT_RECORD t ")
@GroupBy(after = "t.AUTH_DEPT_ID")
@GridConfig(operate = false, idField = "id", winHeight = 400, winWidth = 600)
@Setter
@Getter
@Accessors(chain = true)
public class AttDeptStatisticalReportItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    @GridColumn(show = false)
    private String id;

    // 部门
    /** ID */
    @GridColumn(show = false, sortNo = 1)
    @Column(name = "t.AUTH_DEPT_ID")
    private String deptId;

    /** 编号 */
    @GridColumn(label = "att_common_dept", secHeader = "common_number", sortNo = 2, width = "90")
    private String deptCode;

    /** 部门名称 */
    @GridColumn(label = "#cspan", secHeader = "common_name", sortNo = 3, width = "130")
    private String deptName;

    // 出勤
    /** 应该 */
    @GridColumn(label = "att_rule_arrive", secHeader = "att_statistical_should", sortNo = 4)
    private String shouldHour;

    /** 实际 */
    @GridColumn(label = "#cspan", secHeader = "att_statistical_actual", sortNo = 5)
    private String actualHour;

    /** 有效 */
    @GridColumn(label = "#cspan", secHeader = "att_statistical_valid", sortNo = 6)
    private String validHour;

    // 迟到
    /** 时长 */
    @GridColumn(label = "att_common_late", secHeader = "att_common_timeLongs", sortNo = 7)
    private String lateMinute;

    /** 次数 */
    @Column(name = "(COALESCE(SUM(t.LATE_COUNT_TOTAL),0))")
    @GridColumn(label = "#cspan", secHeader = "att_statistical_numberOfTimes", sortNo = 8)
    private String lateCountTotal;

    // 早退
    /** 时长 */
    @GridColumn(label = "att_common_early", secHeader = "att_common_timeLongs", sortNo = 9)
    private String earlyMinute;

    /** 次数 */
    @Column(name = "(COALESCE(SUM(t.EARLY_COUNT_TOTAL),0))")
    @GridColumn(label = "#cspan", secHeader = "att_statistical_numberOfTimes", sortNo = 10)
    private String earlyCount;

    // 加班
    /** 平时 */
    @GridColumn(label = "att_common_overtime", secHeader = "att_statistical_usually", sortNo = 11)
    private String overtimeUsualHour;

    /** 休息 */
    @GridColumn(label = "#cspan", secHeader = "att_statistical_rest", sortNo = 12)
    private String overtimeRestHour;

    /** 节日 */
    @GridColumn(label = "#cspan", secHeader = "att_statistical_holiday", sortNo = 13)
    private String overtimeHolidayHour;

    /** 合计 */
    @GridColumn(label = "#cspan", secHeader = "att_statistical_total", sortNo = 14)
    private String overtimeHour;

    /** 加班等级 */
    @GridColumn(label = "att_param_overTimeLevel", secHeader = "OT1",  sortNo = 15)
    private String overTimeOT1;

    @GridColumn(label = "#cspan", secHeader = "OT2", sortNo = 16)
    private String overTimeOT2;

    @GridColumn(label = "#cspan", secHeader = "OT3", sortNo = 17)
    private String overTimeOT3;

    /**
     * 旷工
     */
    @GridColumn(label = "att_common_absent", sortNo = 60, width = "130")
    private String absentHour;

    /**
     * 请假
     */
    @GridColumn(dynamicColumn = "attLeaveTypeDynamicColumn", sortNo = 63, label = "att_statistical_leaveDetail")
    private Map<String, Object> attMonthDetailReportLeaveHourMap = new HashMap<>();

    private String searchMonth;

    @Condition(value = "t.ATT_DATE", equalTag = ">=")
    private Date monthStart;

    @Condition(value = "t.ATT_DATE", equalTag = "<=")
    private Date monthEnd;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    /**
     * 默认构造方法
     */
    public AttDeptStatisticalReportItem() {
        super();
        this.id = UUID.randomUUID().toString();
    }
}