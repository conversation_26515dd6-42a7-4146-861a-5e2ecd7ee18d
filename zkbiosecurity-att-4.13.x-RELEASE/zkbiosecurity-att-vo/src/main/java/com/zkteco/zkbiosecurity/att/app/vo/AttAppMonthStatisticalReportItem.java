package com.zkteco.zkbiosecurity.att.app.vo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;


import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 人员汇总表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:27
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain = true)
public class AttAppMonthStatisticalReportItem implements Serializable {

    private static final long serialVersionUID = 1L;

    // 人员
    /** 编号 */
    private String pin;

    /** 姓名 */
    private String name;

    /** 英文（lastName） */
    private String lastName;

    // 部门
    /** ID */
    private String deptId;

    /** 编号 */
    private String deptCode;

    /** 部门名称 */
    private String deptName;

    /** 迟到次数 */
    private String lateCountTotal;

    /** 早退次数 */
    private String earlyCount;

    /** 请假集合 */
    private Map<String, Object> attMonthDetailReportLeaveHourMap = new HashMap<>();

    private String leaveMinuteTotal;

    /** 应该(带单位) */
    private String should;

    /** 实际(带单位) */
    private String actual;

    /** 有效(带单位) */
    private String valid;

    /** 迟到(带单位) */
    private String late;

    /** 早退(带单位) */
    private String early;

    /** 旷工 */
    private String absent;

    /** 平时(带单位) */
    private String overtimeUsual;

    /** 休息(带单位) */
    private String overtimeRest;

    /** 节日(带单位) */
    private String overtimeHoliday;

    /** 加班合计(带单位) */
    private String overtime;

    /** 请假(带单位) */
    private String leave;

    /** 出差(带单位) */
    private String trip;

    /** 外出(带单位) */
    private String out;
}
