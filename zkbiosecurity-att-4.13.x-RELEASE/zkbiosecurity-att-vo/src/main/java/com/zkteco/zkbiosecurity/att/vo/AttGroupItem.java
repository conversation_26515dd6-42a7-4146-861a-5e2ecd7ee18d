package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 分组
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 17:09
 * @since 1.0.0
 */
@From(after = "ATT_GROUP t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 260, winWidth = 400,
    operates = {
        @GridOperate(type = "edit", permission = "att:group:edit", url = "attGroup.do?edit", label = "common_op_edit"),
        @GridOperate(type = "custom", click = "addGroupPerson", permission = "att:group:addPerson",
            label = "common_op_addPerson")})
@Setter
@Getter
@Accessors(chain = true)
public class AttGroupItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 分组名称
     */
    @Column(name = "t.GROUP_NAME")
    @GridColumn(label = "common_name", width = "100")
    private String groupName;

    /**
     * 分组编号
     */
    // @Column(name = "t.GROUP_NO")
    // @GridColumn(label = "common_number",width = "100")
    private String groupNo;

    // @Column(name = "(SELECT COUNT(1) FROM ATT_PERSON WHERE GROUP_ID=t.ID)")
    // @GridColumn(label = "pers_common_personCount", width = "80", sort = "na")
    private Integer personCount;

    /**
     * 分组自定义规则
     */
    // @Column(name = "(SELECT RULE_NAME FROM ATT_CUSTOM_RULE cr LEFT JOIN ATT_CUSTOM_RULE_ORG crg ON cr.ID =
    // crg.RULE_ID WHERE crg.ORG_ID=t.ID)")
    // @GridColumn(label = "att_rule_type_group", width = "100", sort = "na")
    private String customRuleName;

    /**
     * 备注
     */
    @Column(name = "t.REMARK")
    private String remark;

    /**
     * 默认构造方法
     */
    public AttGroupItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AttGroupItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AttGroupItem(String id) {
        super(true);
        this.id = id;
    }

}