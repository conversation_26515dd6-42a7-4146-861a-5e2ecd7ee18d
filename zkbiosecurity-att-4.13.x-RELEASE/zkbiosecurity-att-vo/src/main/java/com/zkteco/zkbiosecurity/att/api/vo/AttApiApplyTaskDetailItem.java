package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2019/3/5 15:06
 */
@Getter
@Setter
@Accessors(chain = true)
public class AttApiApplyTaskDetailItem extends AttApiApplyTaskItem {

    private static final long serialVersionUID = 1L;

    /** 审批人 */
    private List<AttApiApplyPersonItem> approvePersons;

    /** 知会人 */
    private List<AttApiApplyPersonItem> notifierPersons;

    private List<AttApiWorkTimeItem> appAttWorkTimeItems;

    /** 请假图片地址 */
    private String photoUrlList;
}
