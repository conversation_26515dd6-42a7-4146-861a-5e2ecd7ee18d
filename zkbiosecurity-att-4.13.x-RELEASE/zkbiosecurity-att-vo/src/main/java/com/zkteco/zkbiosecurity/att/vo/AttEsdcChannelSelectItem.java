package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 设备选择列表对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/7/27 13:46
 * @since 1.0.0
 */
@GridConfig(operate = false, idField = "id")
@Setter
@Getter
@Accessors(chain = true)
public class AttEsdcChannelSelectItem extends BaseItem {

    /** 设备ID*/
    @GridColumn(columnType = "ra", width = "40", sort = "na")
    private String id;

    /*** 通道名称*/
    @GridColumn(label = "common_dev_channelName", width = "120")
    private String name;

    /*** 通道编号*/
    @GridColumn(label = "esdc_device_channel_code", width = "120")
    private Integer videoNo;

    /** IP地址 */
    @GridColumn(label = "common_ipAddress", width = "*")
    private String ipAddress;

    private String authAreaId;

    /** 判断是左列表noSelect、还是右列表select */
    private String type;

    /** 当前选中的ids */
    private String selectId;
}