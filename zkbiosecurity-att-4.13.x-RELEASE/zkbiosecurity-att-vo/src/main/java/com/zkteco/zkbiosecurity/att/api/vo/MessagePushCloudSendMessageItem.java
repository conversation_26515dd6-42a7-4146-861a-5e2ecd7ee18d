package com.zkteco.zkbiosecurity.att.api.vo;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 线下推微信消息给云端
 * 
 * <AUTHOR>
 * @date 2020-11-26 14:06
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain = true)
public class MessagePushCloudSendMessageItem implements Serializable {

    /**
     * 需要通知人员的pin号 多个用","隔开
     */
    private String touser;

    /**
     * 类型 meetingWxMessage会议 guestRoomWxMessage客房 busWxMessage公车申请 accDoorEventWxMessage 无感考勤打卡记录
     * attTransactionWxMessage
     */
    private String messageType;

    /**
     * todoMsg 待办消息 notifyMsg知会消息 warnMsg报警 remindMsg提醒 visTendency访客动态
     */
    private String sendMessageType;

    /**
     * 公众号模板消息first参数需要显示的内容 （非必需）
     */
    private String firstContent;

    /**
     * 通用字段1
     */
    private String commonContent1;
    /**
     * 通用字段2
     */
    private String commonContent2;
    /**
     * 通用字段3
     */
    private String commonContent3;
    /**
     * 通用字段4
     */
    private String commonContent4;

    /**
     * 备注
     */
    private String remark;
}
