package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 人员排班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 17:03
 * @since 1.0.0
 */
@From(after = "ATT_PERSONSCH t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id", winHeight = 710, winWidth = 800,
    operates = {@GridOperate(type = "edit", url = "attPersonSch.do?info", label = "att_personSch_showSchInfo")})
@Setter
@Getter
@Accessors(chain = true)
public class AttPersonSchItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 起始日期
     */
    @Column(name = "t.START_DATE")
    private Date startDate;

    /**
     * 结束日期
     */
    @Column(name = "t.END_DATE")
    private Date endDate;

    /**
     * 排班类型
     */
    @Column(name = "t.SCHEDULE_TYPE")
    private Short scheduleType;

    /** 人员id **/
    @Column(name = "t.PERS_PERSON_ID")
    private String personId;

    /** 对应排班类型组件id **/
    @Column(name = "t.SCHEDULE_ID")
    private String scheduleId;

    /** 人员编号 **/
    @GridColumn(label = "att_person_pin", width = "120", sort = "na", sortNo = 1, columnType = "edit",
        editUrl = "attPersonSch.do?info", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String personPin;

    /** 人员姓名 **/
    @GridColumn(label = "att_person_name", width = "120", sort = "na", sortNo = 2, encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String personName;

    @GridColumn(label = "att_person_lastName", showExpression = "#language!='zh_CN'", width = "120", sortNo = 3, encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String personLastName;

    @GridColumn(label = "att_common_deptName", width = "120", sort = "na", sortNo = 4)
    private String deptName;

    private String searchMonth;

    @GridColumn(dynamicColumn = "attPersonSchDynamicColumn", sortNo = 6)
    private Map<String, Object> map = new HashMap<>();

    // 部门id
    private String deptId;
    // 部门编号
    private String deptCode;

    // 分组ID
    private String groupId;

    // 排班类型，
    private String schStatus;

    // 处理前端最后一列显示换行
    @GridColumn(label = "likeName", width = "10", sortNo = 999)
    private String likeName;

    // 是否包含下级
    private String isIncludeLower;

    public AttPersonSchItem() {
        super();
    }

    public AttPersonSchItem(Boolean equals) {
        super(equals);
    }

    public AttPersonSchItem(String id) {
        super(true);
        this.id = id;
    }

    public AttPersonSchItem(String id, Date startDate, Date endDate, Short scheduleType) {
        super();
        this.id = id;
        this.startDate = startDate;
        this.endDate = endDate;
        this.scheduleType = scheduleType;
    }
}