package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 设备选择列表对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/7/27 13:46
 * @since 1.0.0
 */
@GridConfig(operate = false, idField = "id")
@Setter
@Getter
@Accessors(chain = true)
public class AttIvsDeviceSelectItem extends BaseItem {

    @GridColumn(columnType = "ra", width = "40", sort = "na")
    private String id;

    /** 设备编码 长度限制64字节 */
    private String cameraCode;

    private String channelCode;

    private String channelNo;

    private String domainCode;

    private String parentType;

    /** 通道id */
    private String deviceId;

    @GridColumn(label = "att_attPoint_channelName", width = "150")
    private String name;

    @GridColumn(label = "att_attPoint_cameraName", width = "100")
    private String deviceName;

    @GridColumn(label = "att_attPoint_cameraIP", width = "100")
    private String deviceIp;

    @GridColumn(label = "att_attPoint_channelIP", width = "100")
    private String ip;

    /** 判断是左列表noSelect、还是右列表select */
    private String type;

    /** 当前选中的ids */
    private String selectId;

    @GridColumn(label = "common_ownedDev", width = "150")
    private String alias;

    @GridColumn(label = "common_dev_devType", format = "0=ivs_parent_device_IVS1800,1=ivs_parent_device_IVS3800,2=ivs_parent_device_NVR800," +
            "5=ivs_parent_device_SDC,4=ivs_parent_device_CLOUD,7=ivs_parent_device_ZKNVR,9=ivs_parent_device_tianDy,10=ivs_parent_device_HIKVISION,11=ONVIF,12=ONVIF,13=ZKTeco," +
            "14=DHNVR,15=ivs_parent_device_AIBox1,16=ivs_parent_device_AIBox2")
    private String deviceType;

    /** 主设备ID */
    private String parentDeviceId;
}