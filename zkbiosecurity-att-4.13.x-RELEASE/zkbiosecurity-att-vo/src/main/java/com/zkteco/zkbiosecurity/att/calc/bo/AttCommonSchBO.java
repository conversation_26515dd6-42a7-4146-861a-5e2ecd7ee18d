package com.zkteco.zkbiosecurity.att.calc.bo;

import lombok.Getter;
import lombok.Setter;

/**
 * 【实时计算】缓存排班信息
 * 
 * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
 * @date 2020/7/14 18:24
 * 
 * @return
 */
@Setter
@Getter
public class AttCommonSchBO {
    /**
     * 排班类型（0：周期排班--普通排班、1：周期排班--智能找班、2：临时排班）
     * <p>
     * 【实时计算】用于更新redis时判断
     * </p>
     */
    private Short scheduleType;

    /**
     * 工作类型（正常上班/周末加班/节假日加班）
     */
    private String workType;

    /**
     * 考勤方式
     */
    private Short attendanceMode;

    /**
     * 加班方式
     */
    private Short overtimeMode;
    /**
     * 加班标记
     */
    private Short overtimeRemark;

    /**
     * 班次id集合
     */
    private String shiftIds;

    /**
     * 时间段Id集合
     */
    private String timeSlotIds;
}
