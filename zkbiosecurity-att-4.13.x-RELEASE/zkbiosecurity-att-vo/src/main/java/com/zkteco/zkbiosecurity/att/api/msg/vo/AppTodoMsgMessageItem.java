package com.zkteco.zkbiosecurity.att.api.msg.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @author: GenerationTools
 * @date: 2018-03-01 下午04:18
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AppTodoMsgMessageItem extends AppBaseMessageItem {
    /** 姓名（人员编号） */
    private String personInfo;

    /** 待办内容1 */
    private String todoContent1;

    /** 待办内容2 */
    private String todoContent2;
}
