package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 补签获取当日的排班和打卡记录
 *
 * <AUTHOR>
 *
 */
@Getter
@Setter
public class AttApiSignDayValidCardItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 人员PIN号
     */
    private String personPin;
    /**
     * 日期
     */
    private String date;

    /**
     * 班次名称
     */
    private String shiftName;

    /**
     * 时间段名称
     */
    private String timeSlots;

    /**
     * 卡点
     */
    private List<AttApiValidCardItem> appAttValidCardItems;

}
