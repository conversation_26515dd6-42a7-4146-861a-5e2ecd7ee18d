package com.zkteco.zkbiosecurity.att.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.Condition;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * 区域人员
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 18:24
 * @since 1.0.0
 */
@From(after = "ATT_AREA_PERSON t LEFT JOIN PERS_PERSON pp ON pp.ID=t.PERS_PERSON_ID LEFT JOIN AUTH_DEPARTMENT ad ON ad.ID = pp.AUTH_DEPT_ID LEFT JOIN AUTH_AREA aa ON aa.ID=t.AUTH_AREA_ID")
@GridConfig(operate = true, idField = "id")
@OrderBy(after = "t.CREATE_TIME DESC")
@Setter
@Getter
@Accessors(chain = true)
public class AttAreaPersonItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 人员编号
     */
    @Column(name = "pp.PIN")
    @GridColumn(label = "att_person_pin", width = "150", sort = "na", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String pin;

    /**
     * 姓名
     */
    @Column(name = "pp.NAME")
    @GridColumn(label = "att_person_name", width = "150", sort = "na", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String name;

    /**
     * 英文姓名
     */
    @Column(name = "pp.LAST_NAME")
    @GridColumn(label = "att_person_lastName", showExpression = "#language!='zh_CN'", width = "150", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String lastName;

    /**
     * 部门id
     */
    @Column(name = "ad.ID")
    private String deptId;

    /**
     * 部门编号
     */
    @Column(name = "ad.CODE")
    @GridColumn(label = "auth_dept_deptNo", width = "150", sort = "na")
    private String deptCode;

    /**
     * 部门名称
     */
    @Column(name = "ad.NAME")
    @GridColumn(label = "pers_dept_deptName", width = "150", sort = "na")
    private String deptName;

    /**
     * 区域id
     */
    @Column(name = "t.AUTH_AREA_ID")
    private String areaId;

    /**
     * 区域编号
     */
    @Column(name = "aa.CODE")
    @GridColumn(label = "base_area_code", width = "150", sort = "na")
    private String authAreaNo;

    /**
     * 区域名称
     */
    @Column(name = "aa.NAME")
    @GridColumn(label = "base_area_name", width = "150", sort = "na")
    private String authAreaName;

    private String cardNo;

    /**
     * 生物模板（暂时不显示）
     */
    // @GridColumn(columnType = "custom", label = "pers_person_biotemplateCount", sort = "na", width = "120", convert =
    // "attConvertBioTemplate")
    private String bioTemplateCount;



    /**
     * 人员ID
     */
    @Column(name = "t.PERS_PERSON_ID")
    private String personId;

    /**
     * 行数，导入用
     */
    private Integer rowNum;

    @Condition(value = "t.PERS_PERSON_ID", equalTag = "in")
    private String inPersPersonId;

    @Condition(value = "t.AUTH_AREA_ID", equalTag = "in")
    private String inAreaId;

    @Condition(value = "ad.ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "(LOWER (pp.NAME) LIKE LOWER (''%{0}%'') OR LOWER (pp.LAST_NAME) LIKE LOWER (''%{0}%''))")
    private String likeName;

    /** 是否包含下级 */
    private String isIncludeLower;

    /**
     * 默认构造方法
     */
    public AttAreaPersonItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AttAreaPersonItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AttAreaPersonItem(String id) {
        super(true);
        this.id = id;
    }
}