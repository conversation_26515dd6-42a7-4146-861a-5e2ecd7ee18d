package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 出差
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 17:00
 * @since 1.0.0
 */
@From(after = "ATT_LEAVE t " + "LEFT JOIN ATT_LEAVETYPE alt ON t.LEAVETYPE_ID=alt.ID "
    + "LEFT JOIN ATT_PERSON p ON t.PERS_PERSON_ID=p.PERS_PERSON_ID")
@Where(after = "AND alt.LEAVETYPE_NO = 'L9'")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600,
    operates = {@GridOperate(type = "del", permission = "att:trip:del", url = "attTrip.do?del&pins=(personPin)",
        label = "common_op_del", filter = "attApplyShowDel")})
@Setter
@Getter
@Accessors(chain = true)
public class AttTripItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 人员编号
     */
    @Column(name = "t.PERS_PERSON_PIN", equalTag = "=")
    @GridColumn(label = "att_person_pin", width = "100")
    private String personPin;

    /**
     * 姓名
     */
    @Column(name = "p.PERS_PERSON_NAME")
    @GridColumn(label = "att_person_name", width = "100", sort = "na")
    private String personName;

    /**
     * 英文（lastName）
     */
    @Column(name = "p.PERS_PERSON_LASTNAME")
    @GridColumn(label = "att_person_lastName", showExpression = "#language!='zh_CN'", width = "100")
    private String personLastName;

    /**
     * 部门编号
     */
    @GridColumn(label = "pers_dept_deptNo", width = "150", sort = "na")
    private String deptCode;

    /**
     * 部门名称
     */
    @GridColumn(label = "pers_dept_deptName", width = "150", sort = "na")
    private String deptName;

    /**
     * 开始日期时间
     */
    @Column(name = "t.START_DATETIME")
    @GridColumn(label = "common_startTime", width = "150")
    private Date startDatetime;

    /**
     * 结束日期时间
     */
    @Column(name = "t.END_DATETIME")
    @GridColumn(label = "common_endTime", width = "130")
    private Date endDatetime;

    /**
     * 出差时长(分)
     */
    @Column(name = "t.LEAVE_LONG")
    @GridColumn(label = "att_trip_tripLongMinute", width = "170")
    private Integer leaveLong;

    /**
     * 出差时长(天)
     */
    @GridColumn(label = "att_trip_tripLongDay", width = "130", show = false)
    private String tripLongDay;

    /**
     * 出差时长(时)
     */
    @GridColumn(label = "att_trip_tripLongHour", width = "130")
    private String tripHour;

    /**
     * 备注
     */
    @Column(name = "t.REMARK")
    @GridColumn(label = "common_remark", width = "130")
    private String remark;

    /**
     * 操作日期时间
     */
    @Column(name = "t.CREATE_TIME")
    @GridColumn(label = "att_common_operateTime", width = "130")
    private Date operateDatetime;

    /**
     * 部门id
     */
    @Column(name = "t.AUTH_DEPT_ID")
    private String deptId;

    /**
     * 人员id
     */
    @Column(name = "t.PERS_PERSON_ID")
    private String personId;

    @Condition(value = "t.CREATE_TIME", equalTag = ">")
    private Date startTime;

    @Condition(value = "t.CREATE_TIME", equalTag = "<")
    private Date endTime;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "t.PERS_PERSON_ID", equalTag = "in")
    private String inPersonId;

    @Condition(value = "t.PERS_PERSON_PIN", equalTag = "in")
    private String inPersonPin;

    @Condition(value = "(lower(p.PERS_PERSON_NAME) LIKE ''%{0}%'' OR lower(p.PERS_PERSON_LASTNAME) LIKE ''%{0}%'')")
    private String likeName;

    /** 假种id */
    private String leaveTypeId;

    private String leaveImagePath;

    /**
     * 小程序请假图片地址
     */
    private String photoUrlList;

    /**
     * 申请编号
     */
    private String applyNo;
    /**
     * 申请类型
     */
    private String applyType;
    /**
     * 流程状态
     */
    @Column(name = "t.FLOW_STATUS", equalTag = "=")
    private String flowStatus;
    /**
     * 流程编号
     */
    private String flowNo;

    /**
     * 流程业务ID,全局唯一
     */
    @Column(name = "t.BUSINESS_KEY")
    private String businessKey;
    /**
     * 知会人
     */
    private String notifierPerIds;
    private String notifierPins;

    /**
     * 申请开始时间范围
     */
    @Condition(value = "t.END_DATETIME", equalTag = ">=")
    private Date startApplyDateTime;
    /**
     * 申请结束时间范围
     */
    @Condition(value = "t.START_DATETIME", equalTag = "<=")
    private Date endApplyDateTime;

    /**
     * 默认构造方法
     */
    public AttTripItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AttTripItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AttTripItem(String id) {
        super(true);
        this.id = id;
    }
}