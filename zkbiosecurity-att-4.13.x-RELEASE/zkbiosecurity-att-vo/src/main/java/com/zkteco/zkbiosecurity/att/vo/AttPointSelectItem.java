package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 人证当考勤点，导入模块双列表实体
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
 * @version V1.0
 * @date Created In 14:49 2018/11/14
 */
@GridConfig(operate = false, idField = "id")
@Setter
@Getter
@Accessors(chain = true)
public class AttPointSelectItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @GridColumn(checkbox = true, width = "35", sort = "na")
    private String id;

    /**
     * 考勤点名称
     */
    @GridColumn(label = "att_attPoint_name", width = "110")
    private String pointName;

    /**
     * 设备名称
     */
    @GridColumn(label = "common_dev_name", width = "120")
    private String deviceName;

    /**
     * 设备序列号
     */
    @GridColumn(label = "common_dev_sn", width = "80", show = false)
    private String devSn;

    /**
     * 设备模块
     */
    @GridColumn(label = "att_attPoint_deviceModule", width = "80")
    private String deviceModule;

    /**
     * 双列表选中的类型
     */
    private String type;

    /**
     * 被选中的行id
     */
    private String selectId;

    /**
     * 默认构造方法
     */
    public AttPointSelectItem() {
        super();
    }

    /**
     * @param id
     */
    public AttPointSelectItem(String id) {
        super(true);
        this.id = id;
    }
}
