package com.zkteco.zkbiosecurity.att.calc.bo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

/**
 * 【实时计算】缓存假种信息
 * 
 * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
 * @date 2020/7/23 14:31
 * @return
 */
@Setter
@Getter
public class AttLeaveTypeBO implements Serializable {

    /**
     * 假种编号
     */
    private String leaveTypeNo;

    /**
     * 是否扣上班时长（false：否/0，true：是/1）
     */
    private Boolean isDeductWorkLong;
}