package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 班次
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:04
 * @since 1.0.0
 */
@From(after = "ATT_SHIFT t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 540, winWidth = 860, operates = {
    @GridOperate(type = "custom", permission = "att:shift:edit", click = "attEditShift", label = "common_op_edit"),
    @GridOperate(type = "custom", permission = "att:shift:edit", click = "addTimeSlot", label = "att_timeSlot_add")})
@Setter
@Getter
@Accessors(chain = true)
public class AttShiftItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 班次名称
     */
    @Column(name = "t.SHIFT_NAME")
    @GridColumn(columnType = "custom", label = "common_name", width = "120", convert = "showEditAttShift")
    private String shiftName;

    /**
     * 班次编号
     */
    @Column(name = "t.SHIFT_NO")
    @GridColumn(label = "common_number", width = "120")
    private String shiftNo;

    /**
     * 班次类型
     */
    @Column(name = "t.SHIFT_TYPE")
    @GridColumn(label = "att_shift_type", width = "90", format = "0=att_shift_regularShift,1=att_shift_flexibleShift")
    private Short shiftType;

    /**
     * 颜色
     */
    @Column(name = "t.SHIFT_COLOR")
    @GridColumn(label = "att_shift_shiftColor", show = false)
    private String shiftColor;

    /**
     * 周期单位（0/天，1/周，2/月）
     */
    @Column(name = "t.PERIODIC_UNIT")
    @GridColumn(label = "att_shift_periodicUnit", width = "90", format = "0=common_day,1=common_week,2=common_month")
    private Short periodicUnit;

    /**
     * 周期数
     */
    @Column(name = "t.PERIOD_NUMBER")
    @GridColumn(label = "att_shift_periodNumber", width = "90")
    private Short periodNumber;

    /**
     * 周期起始方式(0/按起始日期, 1/按排班日期)
     */
    @Column(name = "t.PERIOD_START_MODE")
    private String periodStartMode;

    /**
     * 周期起始日期
     */
    @Column(name = "t.START_DATE")
    @DateType(type = "date")
    @GridColumn(label = "att_shift_startDate_firstDay", width = "120")
    private Date startDate;

    /**
     * 是否月内轮班（false：否/0，true：是/1）
     */
    @Column(name = "t.IS_SHIFT_WITHIN_MONTH")
    // @GridColumn(label = "att_shift_isShiftWithinMonth", width = "150", format = "true=common_yes,false=common_no")
    private Boolean isShiftWithinMonth;

    /**
     * 时间段明细ids(SqlServer nvarchar类型字段最大容纳的是4000，目前就暂定4000定长（其他三种数据库都支持）)
     */
    @Column(name = "t.TIME_SLOT_DETAIL_IDS")
    @GridColumn(label = "att_shift_timeSlotDetailIds", show = false)
    private String timeSlotDetailIds;

    /**
     * 考勤方式（0/按班次正常刷卡，1/一天内任刷一次有效卡，2/只计算刷卡时间，3/免刷卡）
     */
    @Column(name = "t.ATTENDANCE_MODE")
    @GridColumn(label = "att_shift_attendanceMode", show = false)
    private Short attendanceMode;

    /**
     * 加班方式（0/电脑自动计算，1/加班必须申请，2/必须加班否则旷工，3/时长为较小者-延后加班和加班单对比，4/不算加班）
     */
    @Column(name = "t.OVERTIME_MODE")
    @GridColumn(label = "att_shift_overtimeMode", show = false)
    private Short overtimeMode;

    /**
     * 加班标记（0/平时，1/休息日，2/节假日）
     */
    @Column(name = "t.OVERTIME_SIGN")
    @GridColumn(label = "att_shift_overtimeSign", show = false)
    private Short overtimeSign;

    /**
     * 时段编号
     */
    private String periodNo;

    /**
     * 数据迁移，需要保存的时间段id
     */
    private Integer timeSlotId;

    private String timeSlotIds;

    /**
     * 工作类型 （正常工作\周末加班\节假日加班）
     */
    @Column(name = "t.SHIFT_WORKTYPE")
    @GridColumn(label = "att_shift_workType", width = "120", columnType = "dic", key = "AttShiftWorkType")
    private String workType;

    /**
     * 默认构造方法
     */
    public AttShiftItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AttShiftItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AttShiftItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param shiftType
     * @param shiftNo
     * @param shiftName
     * @param shiftColor
     * @param periodicUnit
     * @param periodNumber
     * @param startDate
     * @param isShiftWithinMonth
     * @param timeSlotDetailIds
     * @param attendanceMode
     * @param overtimeMode
     * @param overtimeSign
     */
    public AttShiftItem(String id, Short shiftType, String shiftNo, String shiftName, String shiftColor,
        Short periodicUnit, Short periodNumber, Date startDate, Boolean isShiftWithinMonth, String timeSlotDetailIds,
        Short attendanceMode, Short overtimeMode, Short overtimeSign) {
        super();
        this.id = id;
        this.shiftType = shiftType;
        this.shiftNo = shiftNo;
        this.shiftName = shiftName;
        this.shiftColor = shiftColor;
        this.periodicUnit = periodicUnit;
        this.periodNumber = periodNumber;
        this.startDate = startDate;
        this.isShiftWithinMonth = isShiftWithinMonth;
        this.timeSlotDetailIds = timeSlotDetailIds;
        this.attendanceMode = attendanceMode;
        this.overtimeMode = overtimeMode;
        this.overtimeSign = overtimeSign;
    }
}