package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * 考勤自定义报表
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 10:25 2023/7/18
 * @version v1.0
 */
@GridConfig(operate = true, winWidth = 800, winHeight = 600,
    operates = {
        @GridOperate(type = "edit", permission = "att:customReport:add", url = "attCustomReport.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "att:customReport:del", url = "attCustomReport.do?del&names=(name)",
            label = "common_op_del", click="attCustomReportLeftGridDelCallback")})
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class AttCustomReportItem extends BaseItem {

    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /** 名称 */
    @GridColumn(label = "common_name", width = "120")
    private String name;

    /**
     * 模块 例如：考勤att、门禁acc
     */
    private String moduleCode;

    /**
     * 自定义报表类型 例如：考勤日报表AttDayDetailReport、部门汇总AttDeptStatisticalReport、人员汇总AttMonthStatisticalReport，模块自定义标记处理
     */
    private String reportType;

    /**
     * 自定义表报字段集合
     */
    private List<AttCustomReportFieldItem> customReportFieldItemList = new ArrayList<>();

    /**
     * 用于前端编辑
     */
    private String customReportFieldItemString;
}
