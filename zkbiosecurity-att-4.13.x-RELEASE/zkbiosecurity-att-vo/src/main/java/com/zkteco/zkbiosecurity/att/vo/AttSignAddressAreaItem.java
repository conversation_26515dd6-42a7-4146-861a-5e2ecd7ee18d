package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.Condition;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;

@From(after = "ATT_SIGN_ADDRESS_AREA T")
@OrderBy(after = "t.CREATE_TIME DESC")
@Getter
@Setter
public class AttSignAddressAreaItem extends BaseItem {

    @Column(name = "t.ID")
    private String id;

    @Column(name = "t.SIGN_ADDRESS_ID")
    private String signAddressId;

    @Column(name = "t.AREA_ID")
    private String areaId;

    @Condition(value = "t.AREA_ID", equalTag = "in")
    private String inAreaId;

}
