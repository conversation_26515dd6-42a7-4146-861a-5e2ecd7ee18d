package com.zkteco.zkbiosecurity.att.api.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 考勤人员
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 16:48 2021/11/15
 * @version v1.0
 */
@Setter
@Getter
@Accessors(chain = true)
@ApiModel(description = "AttApiPersonItem")
public class AttApiPersonItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 人员编号 */
    @ApiModelProperty(value = "人员编号", required = true, example = "'1'")
    private String personPin;

    /** 是否考勤 */
    @ApiModelProperty(value = "是否考勤（true是、false否）", required = true, example = "'1'", allowableValues = "true,false")
    private Boolean isAttendance;

    /** 设备操作权限 0普通人员、2登记员、6管理员、14超级管理员*/
    @ApiModelProperty(value = "设备操作权限（0普通人员、2登记员、6管理员、14超级管理员）", required = true, example = "0", allowableValues = "0,2,6,14")
    private Short perDevAuth;

    /** 验证方式 */
    @ApiModelProperty(value = "验证方式（0=自动识别、1=仅指纹、2=工号验证、3=仅密码、4=仅卡、5=指纹或密码、6=指纹或卡、7=卡或密码、8=工号加指纹、9=指纹加密码、10=卡加指纹、11=卡加密码、12=指纹加密码加卡、13=工号加指纹加密码、14=(工号加指纹)或(卡加指纹)、15=人脸、16=人脸加指纹、17=人脸加密码、18=人脸加卡、19=人脸加指纹加卡、20=人脸加指纹加密码、21=指静脉、22=指静脉加密码、23=指静脉加卡、24=指静脉加密码加卡、25=掌纹、26=掌纹加卡、27=掌纹加面部、28=掌纹加指纹、29=掌纹加指纹加面部）", required = true, example = "0")
    private Short verifyMode;
}
