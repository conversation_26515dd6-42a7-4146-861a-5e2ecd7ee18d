package com.zkteco.zkbiosecurity.att.calc.bo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 异常申请-请假 计算使用对象
 * 
 * <AUTHOR>
 * @date 2020/6/15
 */
@Setter
@Getter
@ToString
public class AttLeaveBO implements Serializable {

    private String id;

    /**
     * 假种编号
     */
    private String leaveTypeNo;

    /**
     * 是否扣上班时长（false/否，true/是）
     */
    private Boolean isDeductWorkLong;

    /**
     * 开始时间
     */
    private String startDateTime;

    /**
     * 结束时间
     */
    private String endDateTime;
}
