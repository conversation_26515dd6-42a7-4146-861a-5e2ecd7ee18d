package com.zkteco.zkbiosecurity.att.calc.bo;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 初始化调班信息到缓存的对象，便于实时计算时加载
 * 
 * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
 * @date 2019年10月17日
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AttClassBO {
    /** 个人同日调班(-40)、个人不同日调班(-50)、两人对调(-60) */
    private String exceptionSchType;
    /** 个人同日调班:班次Id */
    private String shiftId;
    /** 调班日期 */
    private Date adjustDate;
    /** 对调日期 */
    private Date swapDate;
    /** 1(调班人)、2（被调人） */
    private String modelType;
    /** 调班人或被调人pin */
    private String personPin;
}
