package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 2019/1/14 19:25
 */
@Getter
@Setter
@Accessors(chain = true)
public class AttApiSignInItem implements Serializable {

    /** 人员ID */
    private String customerId;

    /** 人员编号 */
    private String pin;

    /** 人员姓名 */
    private String name;

    /** 人员姓氏（海外） */
    private String lastName;

    /** 部门编号 */
    private String deptCode;

    /** 人员名称 */
    private String deptName;

    /** 部门ID */
    private String deptId;

    /** 签到地点 */
    private String attPlace;

    /** 经纬度 */
    private String latLongitude;

    /** 人员头像路径 */
    private String photoUrl;

    /** 签到时间 */
    private String time;

    /** 当前日期 */
    private String date;

    /** 数据来源 */
    private String mark;

    /** 考勤地址id */
    private String areaId;
}
