package com.zkteco.zkbiosecurity.att.api.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 申请补签
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 16:48 2021/11/15
 * @version v1.0
 */
@Setter
@Getter
@Accessors(chain = true)
@ApiModel(description = "AttApiApplySignItem")
public class AttApiApplySignItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 人员编号 */
    @ApiModelProperty(value = "人员编号",required = true, example = "'1'")
    private String personPin;

    /** 补签时间 */
    @ApiModelProperty(value = "补签时间",required = true, example = "2021-11-19 18:00:00")
    private String signDatetime;

    /** 备注 */
    @ApiModelProperty(value = "备注", example = "api")
    private String remark;
}
