package com.zkteco.zkbiosecurity.att.calc.bo;

import java.io.Serializable;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 【考勤计算】人/天/班
 * 
 * @date Created In 9:25 2018/7/9
 */
@Setter
@Getter
public class AttPersonSchBO implements Serializable {

    private String id;

    /**
     * 异常排班类型
     */
    @Deprecated
    private String attExceptionSchType;

    /**
     * 考勤状态
     */
    private String attendStatus;

    /**
     * 排班主键ID
     */
    private String attShiftId;

    /** 班次名称 */
    private String attShiftName;

    /** 班次编码 */
    private String attShiftNo;

    /**
     * 班次类型（0/规律班次，1/弹性班次）
     */
    private Short shiftType;

    /**
     * 考勤方式
     */
    private Short attendanceMode;

    /**
     * 加班方式（0/电脑自动计算，1/加班必须申请，2/必须加班否则旷工，3/时长为较小者-延后加班和加班单对比，4/不算加班）
     */
    private Short overtimeMode;

    /**
     * 加班标记（0/平时，1/休息日，2/节假日）
     */
    private Short overtimeRemark;

    /**
     * 工作类型（正常上班/周末加班/节假日加班）
     */
    private String workType;

    /**
     * 时间段数组
     */
    private List<AttTimeSlotBO> attTimeSlotArray;
}
