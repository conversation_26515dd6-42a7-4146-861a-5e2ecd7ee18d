package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 考勤点双列表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 17:01
 * @since 1.0.0
 */
@From(after = "ATT_POINT t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
@Setter
@Getter
@Accessors(chain = true)
public class AttSelectPointItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 考勤点名称
     */
    @Column(name = "t.POINT_NAME")
    @GridColumn(label = "att_attPoint_name", width = "150")
    private String pointName;

    /**
     * 停车场的设备id或者门禁的门id
     */
    @Column(name = "t.DEVICE_ID")
    private String deviceId;
    /**
     * 设备序列号
     */
    @Column(name = "t.DEVICE_SN")
    private String devSn;

    /**
     * 停车场的设备名称或者门禁的门名称
     */
    @GridColumn(label = "att_attPoint_doorOrParkDeviceName", width = "*")
    private String deviceName;

    /**
     * 设备所属模块
     */
    @Column(name = "t.DEVICE_MODULE", equalTag = "=")
    private String deviceModule;

    /**
     * 设备所属模块（门编号） (门禁当考勤时为门编号,VMS当考勤点时为通道编号 --by ljf 2019/12/26)
     */
    @Column(name = "t.DOOR_NO")
    private Short doorNo;

    private String type;

    @Column(name = "t.ID", equalTag = "not in")
    private String selectId;

    /**
     * 默认构造方法
     */
    public AttSelectPointItem() {
        super();
    }

    /**
     * @param id
     */
    public AttSelectPointItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param pointName
     * @param deviceName
     */
    public AttSelectPointItem(String id, String pointName, String deviceName) {
        super();
        this.id = id;
        this.pointName = pointName;
        this.deviceName = deviceName;
    }
}