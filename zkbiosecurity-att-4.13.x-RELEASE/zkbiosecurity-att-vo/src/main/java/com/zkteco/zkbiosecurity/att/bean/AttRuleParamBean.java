package com.zkteco.zkbiosecurity.att.bean;

import lombok.Getter;
import lombok.Setter;

/**
 * 考勤全局规则参数
 *
 * <AUTHOR>
 * @date 2020-06-10 18:56
 * @sine 1.0.0
 */
@Getter
@Setter
public class AttRuleParamBean {
    /**
     * 时间段跨天，考勤结果算第一天还是第二天(0:第一天、1:第二天)
     */
    private String crossDay;

    /**
     * 智能找班原则(0/时长最长,1/异常最少)
     */
    private String smartFindClass;

    /**
     * 上班签到取卡记录原则(0/最早原则, 1/就近原则)
     */
    private String signIn;

    /**
     * 下班签到取卡记录原则(0/最晚原则, 1/就近原则)
     */
    private String signOut;

    /**
     * 迟到且早退算旷工(true/false)
     */
    private String lateAndEarlyAsAbsent;

    /**
     * 是否统计加班(true/false)
     */
    private String overtime;

    /**
     * 未签到([)
     */
    private String noSignIn;

    /**
     * 未签退（]）
     */
    private String noSignOff;

    /**
     * 小数点精确位数
     */
    private String decimal;

    /**
     * 未签到记为(缺勤/迟到/不完整)
     */
    private String noSignInAsType;

    /**
     * 未签到记为迟到分钟数
     */
    private String noSignInAsLateMinute;

    /**
     * 未签退记为(缺勤/迟到/不完整)
     */
    private String noSignOutAsType;

    /**
     * 未签退记为早退分钟数
     */
    private String noSignOutAsEarlyMinute;

//    /**
//     * 旷工天数转换基准(0/小数点精确位数,1/记为工作日数为准)
//     */
//    private String absentDay;
//
//    /**
//     * 天数转换基准(0/,1/)
//     */
//    private String day;
//
//    /**
//     * 余数-记为一天
//     */
//    private String oneDay;
//
//    /**
//     * 余数-记为半天
//     */
//    private String halfAnDay;
//
//    /**
//     * 小时转换基准
//     */
//    private String hour;
}
