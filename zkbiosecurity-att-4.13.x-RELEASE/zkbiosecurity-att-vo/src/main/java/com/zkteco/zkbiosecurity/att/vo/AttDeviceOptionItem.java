package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 设备参数
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 17:09
 * @since 1.0.0
 */
@From(after = "ATT_DEVICE_OPTION t " + "LEFT JOIN ATT_DEVICE d on t.DEV_ID=d.ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600,
    operates = {
        @GridOperate(type = "edit", permission = "att:deviceOption:edit", url = "attDeviceOption.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "att:deviceOption:del", url = "attDeviceOption.do?del",
            label = "common_op_del")})
@Setter
@Getter
@Accessors(chain = true)
public class AttDeviceOptionItem extends BaseItem {

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /**  */
    @Column(name = "d.ID")
    @GridColumn(label = "att_deviceOption_device")
    private String deviceId;

    /**  */
    @Column(name = "t.OPTION_NAME")
    @GridColumn(label = "att_deviceOption_name")
    private String name;

    /**  */
    @Column(name = "t.OPTION_VALUE")
    @GridColumn(label = "att_deviceOption_value")
    private String value;

    /**
     * 默认构造方法
     */
    public AttDeviceOptionItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AttDeviceOptionItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AttDeviceOptionItem(String id) {
        super(true);
        this.id = id;
    }

    public AttDeviceOptionItem(String deviceId, String name) {
        super(true);
        this.deviceId = deviceId;
        this.name = name;
    }

    /**
     * @param id
     * @param name
     * @param value
     */
    public AttDeviceOptionItem(String id, String name, String value) {
        super();
        this.id = id;
        this.name = name;
        this.value = value;
    }
}