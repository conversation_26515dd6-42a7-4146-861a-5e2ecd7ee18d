package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @author: GenerationTools
 * @date: 2018-02-28 上午11:19
 */
@From(after = "ATT_LEAVE t LEFT JOIN ATT_LEAVETYPE alt ON alt.ID = t.LEAVETYPE_ID LEFT JOIN AUTH_DEPARTMENT ad ON t.AUTH_DEPT_ID = ad.ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600,
    operates = {
        @GridOperate(type = "del", permission = "att:leave:del", url = "attLeave.do?del&pins=(personPin)",
            label = "common_op_del", filter = "attApplyShowDel"),
        @GridOperate(type = "custom", permission = "att:leave:image", click = "openLeaveImage",
            label = "att_leave_image")})
@Setter
@Getter
@Accessors(chain = true)
public class AttLeaveItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 人员id
     */
    @Column(name = "t.PERS_PERSON_ID")
    private String personId;

    /**
     * 人员编号
     */
    @Column(name = "t.PERS_PERSON_PIN")
    @GridColumn(label = "att_person_pin", width = "120", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String personPin;

    /**
     * 姓名
     */
    @GridColumn(label = "att_person_name", width = "120", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String personName;

    /**
     * 英文（lastName）
     */
    @GridColumn(label = "att_person_lastName", showExpression = "#language!='zh_CN'", width = "120", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String personLastName;

    /**
     * 部门id
     */
    @Column(name = "t.AUTH_DEPT_ID")
    private String deptId;

    /**
     * 部门编号（数据库不保存部门编号，查询转化成ID查询，列表获取最新部门编号展示）
     */
    @Column(name = "ad.CODE")
    @GridColumn(label = "att_common_deptNo", width = "120")
    private String deptCode;

    /**
     * 部门名称（数据库不保存部门名，查询转化成ID查询，列表获取最新部门名称展示）
     */
    @Column(name = "ad.NAME")
    @GridColumn(label = "att_common_deptName", width = "120")
    private String deptName;

    /**
     * 假种名称 i18n = true 处理假种名称初始化改为key值显示时未翻译问题
     */
    @Column(name = "alt.LEAVETYPE_NAME")
    @GridColumn(label = "att_leave_arilName", width = "120", i18n = true, sort = "na")
    private String leaveTypeName;

    /**
     * 假种编号
     */
    @Column(name = "alt.LEAVETYPE_NO")
    private String leaveTypeNo;

    /**
     * 开始日期时间
     */
    @Column(name = "t.START_DATETIME")
    @GridColumn(label = "common_startTime", width = "150")
    private Date startDatetime;

    /**
     * 结束日期时间
     */
    @Column(name = "t.END_DATETIME")
    @GridColumn(label = "common_endTime", width = "150")
    private Date endDatetime;

    /**
     * 请假时长(分)
     */
    @Column(name = "t.LEAVE_LONG")
    @GridColumn(label = "att_leave_leaveLongMinute", width = "120")
    private Integer leaveLong;

    /**
     * 请假时长(时)
     */
    @GridColumn(label = "att_leave_leaveLongHour", width = "120", sort = "na")
    private String leaveLongHour;

    /**
     * 请假时长(天)
     */
    @Column(name = "t.DAYS")
    private Float days;

    /**
     * 流程状态
     */
    @GridColumn(label = "common_status", width = "120",
        format = "0=att_approve_wait,1=att_exception_stop,2=att_apply_pass,3=att_apply_revoke,4=att_exception_delete,5=att_exception_refuse,6=att_exception_end")
    @Column(name = "t.FLOW_STATUS", equalTag = "=")
    private String flowStatus;

    /**
     * 备注
     */
    @Column(name = "t.REMARK")
    @GridColumn(label = "common_remark", width = "120")
    private String remark;

    /**
     * 操作日期时间
     */
    @Column(name = "t.CREATE_TIME")
    @GridColumn(label = "att_common_operateTime", width = "130")
    private Date operateDatetime;

    /**
     * 假图id
     */
    @Column(name = "t.LEAVE_IMAGE_PATH")
    private String leaveImagePath;

    @Column(name = "t.CLOUD_IMAGE_URL")
    private String cloudImageUrl;

    /**
     * 小程序请假图片地址
     */
    private String photoUrlList;

    /**
     * 假种id
     */
    @Column(name = "t.LEAVETYPE_ID")
    private String leaveTypeId;

    /**
     * 流程业务ID,全局唯一
     */
    @Column(name = "t.BUSINESS_KEY")
    private String businessKey;


    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "t.PERS_PERSON_ID", equalTag = "in")
    private String inPersonId;

    @Condition(value = "t.PERS_PERSON_PIN", equalTag = "in")
    private String inPersonPin;

    @Condition(value = "t.PERS_PERSON_PIN", equalTag = "=")
    private String personPinEq;

    @Condition(value = "alt.LEAVETYPE_NO", equalTag = "not in")
    private String notInLeaveTypeNo;

    /**
     * 申请开始时间范围
     */
    @Condition(value = "t.END_DATETIME", equalTag = ">=")
    private Date startApplyDateTime;

    /**
     * 申请结束时间范围
     */
    @Condition(value = "t.START_DATETIME", equalTag = "<=")
    private Date endApplyDateTime;

    /**
     * 流程状态
     */
    @Condition(value = "t.FLOW_STATUS", equalTag = "in")
    private String flowStatusIn;

    @Condition(
            value = "t.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    private String likeName;

    /** 是否包含下级 */
    private String isIncludeLower;

    /**
     * 行数，导入用
     */
    private Integer rowNum;

    /**
     * 申请编号
     */
    private String applyNo;

    /**
     * 申请类型
     */
    private String applyType;

    /**
     * 流程编号
     */
    private String flowNo;

    /**
     * 知会人
     */
    private String notifierPerIds;
    private String notifierPins;

    /** 任务ID */
    private String taskId;

    private String createrId;

    private String createrCode;

    private String createrName;

    public AttLeaveItem() {
        super();
    }

    public AttLeaveItem(Boolean equals) {
        super(equals);
    }

    public AttLeaveItem(String id) {
        super(true);
        this.id = id;
    }

}