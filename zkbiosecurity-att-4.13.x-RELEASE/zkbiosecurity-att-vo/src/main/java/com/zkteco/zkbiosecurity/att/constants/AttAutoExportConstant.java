package com.zkteco.zkbiosecurity.att.constants;

/**
 * 考勤自动导出常量类
 * 
 * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
 * @date 2019/5/21 9:34
 */
public abstract class AttAutoExportConstant {
    // 任务状态（0:禁用 1:启动），默认启动
    /** 禁用 */
    public static final String JOBSTATUS_DISABLE = "0";
    /** 启动 */
    public static final String JOBSTATUS_ENABLE = "1";

    // 文件类型（0：txt文本）
    /** txt文本 */
    public static final String FILETYPE_TXT = "0";
    /** excel文件 */
    public static final String FILETYPE_EXCEL = "2";

    // 报表类型（0:原始记录表、1:日打卡详情表、2:日明细表、3:出勤异常表、4:月考勤状态表、5:人员汇总表）
    /** 原始记录表 */
    public static final String REPORTTYPE_TRANSACTION = "0";
    /** 日打卡详情表 */
    public static final String REPORTTYPE_DAYCARDDETAILREPORT = "1";
    /** 日明细表 */
    public static final String REPORTTYPE_DAYDETAILREPORT = "2";
    /** 出勤异常表 */
    public static final String REPORTTYPE_ABNORMAL_REPORT = "3";
    /** 月考勤状态表 */
    public static final String REPORTTYPE_MONTHDETAIL_REPORT = "4";
    /** 人员汇总表 */
    public static final String REPORTTYPE_MONTHSTATISTICAL_REPORT = "5";

    // 时间发送频率（0：日 1:月）
    /** 按日 */
    public static final String TIMESENDFREQUENCY_DAY = "0";
    /** 按月 */
    public static final String TIMESENDFREQUENCY_MONTH = "1";

    // 发送方式（0：邮箱发送方式，1：FTP发送方式）
    /** 邮箱发送方式 */
    public static final String SENDFORMAT_MAIL = "0";
    /** FTP发送方式 */
    public static final String SENDFORMAT_FTP = "1";
    /** SFTP发送方式 */
    public static final String SENDFORMAT_SFTP = "2";

    /**
     * 邮件类型（0:按人员、1:按部门、1:按区域）
     */
    /** 按人员 */
    public static final String EMAILTYPE_PERSON = "0";
    /** 按部门 */
    public static final String EMAILTYPE_DEPT = "1";
    /** 按区域 */
    public static final String EMAILTYPE_AREA = "2";

    /**
     * 时间发送间隔
     */
    /** 每个月的第一天 */
    public static final String TIMEINTERVAL_FIRSTDAYOFMONTH = "firstDayOfMonth";
    /** 每个月的最后一天 */
    public static final String TIMEINTERVAL_LASTDAYOFMONTH = "lastDayOfMonth";

}
