package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 团队外出查询列表
 * 
 * <AUTHOR>
 * @date 2019/04/15
 */
@Getter
@Setter
public class AttApiTeamOutPersonItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    private String personName;

    /**
     * 人员编号
     */
    private String personPin;

    /**
     * 头像地址
     */
    private String avatarUrl;

    /**
     * 申请时长
     */
    private String timeLong;

    /**
     * 开始时间
     */
    private String startDatetime;

    /**
     * 结束时间
     */
    private String endDatetime;

    /**
     * 申请时间
     */
    private String applyTime;

    /**
     * 流程key taskId
     */
    private String taskId;

    /**
     * 申请id 对应att_out表主键id
     */
    private String applyId;
}
