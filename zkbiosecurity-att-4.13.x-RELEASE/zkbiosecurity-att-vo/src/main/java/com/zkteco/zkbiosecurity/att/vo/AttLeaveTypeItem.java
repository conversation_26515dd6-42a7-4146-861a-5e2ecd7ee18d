package com.zkteco.zkbiosecurity.att.vo;

import java.util.ArrayList;
import java.util.List;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 假种
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:18
 * @since 1.0.0
 */
@From(after = "ATT_LEAVETYPE t ")
@OrderBy(after = "t.SORT_NO ASC")
@GridConfig(operate = true, idField = "id", winHeight = 450, winWidth = 420,
    operates = {
        @GridOperate(type = "edit", permission = "att:leaveType:edit", url = "attLeaveType.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "att:leaveType:del", url = "attLeaveType.do?del&names=(leaveTypeName)",
            label = "common_op_del", filter = "attLeaveTypeShowDel")})
@Setter
@Getter
@Accessors(chain = true)
public class AttLeaveTypeItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 假种编号
     */
    @Column(name = "t.LEAVETYPE_NO")
    private String leaveTypeNo;

    /**
     * 假种名称
     */
    @Column(name = "t.LEAVETYPE_NAME")
    @GridColumn(label = "common_name", width = "130", i18n = true, columnType = "edit",
        editPermission = "att:leaveType:edit", editUrl = "attLeaveType.do?edit&id=(id)")
    private String leaveTypeName;

    /**
     * 是否扣上班时长（false：否/0，true：是/1）
     */
    @Column(name = "t.IS_DEDUCT_WORK_LONG")
    @GridColumn(label = "att_leaveType_isDeductWorkLong", width = "190", format = "true=common_yes,false=common_no")
    private Boolean isDeductWorkLong;

    /**
     * 最小单位 (列表组合显示用)
     */
    @GridColumn(label = "att_param_smallestUnit", width = "130")
    private String smallestUnit;

    /**
     * 最小单位-数值
     */
    @Column(name = "t.CONVERT_COUNT")
    // @GridColumn(label = "att_param_smallestUnit", width = "130")
    private Double convertCount;

    /**
     * 最小单位-单位（minute=分钟,day=工作日,hour=小时）
     */
    @Column(name = "t.CONVERT_UNIT")
    // @GridColumn(label = "att_param_smallestUnit", width = "130", format =
    // "minute=common_minutes,day=att_param_workDay,hour=common_hours")
    private String convertUnit;

    /**
     * 舍入控制（abort=向下（舍弃）,rounding=四舍五入,carry=向上（进位））
     */
    @Column(name = "t.CONVERT_TYPE")
    @GridColumn(label = "att_param_roundingControl", width = "130",
        format = "abort=att_param_abort,rounding=att_param_rounding,carry=att_param_carry")
    private String convertType;

    /**
     * 报表展示符号
     */
    @Column(name = "t.SYMBOL")
    @GridColumn(label = "att_param_reportSymbol", width = "120")
    private String symbol;

    /**
     * 标记（params=参数设置、leaveType=假种）
     */
    @Column(name = "t.MARK", equalTag = "=")
    private String mark;

    /** 排序编号*/
    @Column(name = "t.SORT_NO")
    @GridColumn(label = "auth_dept_sort", width = "120")
    private Integer sortNo;

    /** 月考勤状态表显示颜色*/
    @Column(name = "t.COLOR")
    @GridColumn(label = "att_param_reportColor", width = "120", columnType = "custom", convert = "attLeaveTypeColor")
    private String color;

    /**
     * 启用年度限制
     */
    @Column(name = "ENABLE_MAX_DAYS")
    @GridColumn(label = "att_leaveType_enableMaxDays", width = "120", format = "true=common_yes,false=common_no")
    private Boolean enableMaxDays;

    /**
     * 年度限制（天）
     */
    @Column(name = "MAX_DAYS")
    @GridColumn(label = "att_leaveType_maxDays", width = "120")
    private Integer maxDays;

    /**
     * 加入初始化标识位
     */
    @Column(name = "t.INIT_FLAG")
    @GridColumn(show = false)
    private Boolean initFlag;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    /**
     * 假种编号
     */
    @Condition(value = "t.LEAVETYPE_NO", equalTag = "in")
    private String leaveTypeNoIn;

    private List<AttLeaveTypeItem> attLeaveTypeItemList = new ArrayList<>();

    /**
     * 默认构造方法
     */
    public AttLeaveTypeItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AttLeaveTypeItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AttLeaveTypeItem(String id) {
        super(true);
        this.id = id;
    }

    public AttLeaveTypeItem(String leaveTypeNo, Integer sortNo) {
        this.leaveTypeNo = leaveTypeNo;
        this.sortNo = sortNo;
    }

    public AttLeaveTypeItem(String leaveTypeNo, String leaveTypeName, Boolean isDeductWorkLong, Boolean initFlag) {
        super();
        this.leaveTypeNo = leaveTypeNo;
        this.leaveTypeName = leaveTypeName;
        this.isDeductWorkLong = isDeductWorkLong;
        this.initFlag = initFlag;
    }

    public AttLeaveTypeItem(String leaveTypeNo, String leaveTypeName, Boolean isDeductWorkLong, Boolean initFlag,
        Double convertCount, String convertUnit, String convertType, String symbol, String mark) {
        super();
        this.leaveTypeNo = leaveTypeNo;
        this.leaveTypeName = leaveTypeName;
        this.isDeductWorkLong = isDeductWorkLong;
        this.initFlag = initFlag;
        this.convertCount = convertCount;
        this.convertUnit = convertUnit;
        this.convertType = convertType;
        this.symbol = symbol;
        this.mark = mark;
    }
}