/*
 * File Name: AttMonthStatisticalReportItem.java Created by y<PERSON><PERSON>.zou on 2018年3月19日 下午1:58:55. Copyright:Copyright ?
 * 1985-2017 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.att.api.vo;

import java.util.Date;
import java.util.UUID;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 月明细报表-移动端按人员汇总查询
 * 
 * @version v1.0
 */
@From(after = "ATT_RECORD t ")
@GroupBy(after = "t.PERS_PERSON_PIN")
@OrderBy(after = "t.PERS_PERSON_PIN DESC")
@Setter
@Getter
@Accessors(chain = true)
public class AttApiMonthStatisticalReportItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 编号
     */
    @Column(name = "t.PERS_PERSON_PIN")
    private String pin;

    /**
     * 姓名
     */
    @Column(name = "MAX(t.PERS_PERSON_NAME)")
    private String name;

    /**
     * 英文（lastName）
     */
    @Column(name = "MAX(t.PERS_PERSON_LAST_NAME)")
    private String lastName;

    // 部门
    /**
     * ID
     */
    @Column(name = "MAX(t.AUTH_DEPT_ID)")
    private String deptId;

    /**
     * 编号
     */
    @Column(name = "MAX(t.AUTH_DEPT_CODE)")
    private String deptCode;

    /**
     * 部门名称
     */
    @Column(name = "MAX(t.AUTH_DEPT_NAME)")
    private String deptName;

    // 出勤（时）
    /**
     * 应该
     */
    @Column(name = "(COALESCE(SUM(t.SHOULD_MINUTE),0))")
    private String shouldHour;

    /**
     * 实际
     */
    @Column(name = "(COALESCE(SUM(t.ACTUAL_MINUTE),0))")
    private String actualHour;

    @Condition(value = "(COALESCE(SUM(t.ACTUAL_MINUTE),0))", equalTag = ">=", coditionType = "having")
    private Integer actualMinuteGe;

    /**
     * 有效
     */
    @Column(name = "(COALESCE(SUM(t.VALID_MINUTE),0))")
    private String validHour;

    // 出勤（日）
    /**
     * 应该
     */
    @Column(name = "(COALESCE(SUM(t.SHOULD_DAYS),0))")
    private String shouldDay;

    /**
     * 实际
     */
    @Column(name = "(COALESCE(SUM(t.ACTUAL_DAYS),0))")
    private String actualDay;

    /**
     * 有效
     */
    @Column(name = "(COALESCE(SUM(t.VALID_DAYS),0))")
    private String validDay;

    // 迟到
    /**
     * 时长(分)
     */
    @Column(name = "(COALESCE(SUM(t.LATE_MINUTE_TOTAL),0))")
    private String lateMinute;

    /**
     * 次数
     */
    @Column(name = "(COALESCE(SUM(t.LATE_COUNT_TOTAL),0))")
    private String lateCountTotal;

    // 早退
    /**
     * 时长(分)
     */
    @Column(name = "(COALESCE(SUM(t.EARLY_MINUTE_TOTAL),0))")
    private String earlyMinute;
    /**
     * 次数
     */
    @Column(name = "(COALESCE(SUM(t.EARLY_COUNT_TOTAL),0))")
    private String earlyCount;

    // 加班（时）
    /**
     * 平时
     */
    @Column(name = "(COALESCE(SUM(t.OVERTIME_USUAL_MINUTE),0))")
    private String overtimeUsualHour;

    /**
     * 休息
     */
    @Column(name = "(COALESCE(SUM(t.OVERTIME_REST_MINUTE),0))")
    private String overtimeRestHour;

    /**
     * 节日
     */
    @Column(name = "(COALESCE(SUM(t.OVERTIME_HOLIDAY_MINUTE),0))")
    private String overtimeHolidayHour;

    /**
     * 合计
     */
    @Column(name = "(COALESCE(SUM(t.OVERTIME_MINUTE),0))")
    private String overtimeHour;

    // 异常（时）
    /**
     * 旷工
     */
    @Column(name = "(COALESCE(SUM(t.ABSENT_MINUTE),0))")
    private String absentHour;

    /**
     * 请假
     */
    @Column(name = "(COALESCE(SUM(t.LEAVE_MINUTE),0))")
    private String leaveHour;

    /**
     * 出差
     */
    @Column(name = "(COALESCE(SUM(t.TRIP_MINUTE),0))")
    private String tripHour;

    /**
     * 外出
     */
    @Column(name = "(COALESCE(SUM(t.OUT_MINUTE),0))")
    private String outHour;

    // 异常（日）
    /**
     * 旷工
     */
    @Column(name = "(COALESCE(SUM(t.ABSENT_DAYS),0))")
    private String absentDay;

    /**
     * 请假
     */
    @Column(name = "(COALESCE(SUM(t.LEAVE_DAYS),0))")
    private String leaveDay;

    /**
     * 出差
     */
    @Column(name = "(COALESCE(SUM(t.TRIP_DAYS),0))")
    private String tripDay;

    /**
     * 外出
     */
    @Column(name = "(COALESCE(SUM(t.OUT_DAYS),0))")
    private String outDay;

    @Column(name = "(COALESCE(SUM(t.LEAVE_MINUTE),0))")
    private String leaveMinuteTotal;

    private String searchMonth;

    @Condition(value = "t.ATT_DATE", equalTag = ">=")
    private Date monthStart;

    @Condition(value = "t.ATT_DATE", equalTag = "<=")
    private Date monthEnd;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "(lower(t.PERS_PERSON_NAME) LIKE ''%{0}%'' OR lower(t.PERS_PERSON_LAST_NAME) LIKE ''%{0}%'')")
    private String likeName;

    /**
     * 默认构造方法
     */
    public AttApiMonthStatisticalReportItem() {
        super();
        this.id = UUID.randomUUID().toString();
    }
}
