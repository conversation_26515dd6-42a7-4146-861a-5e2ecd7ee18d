package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 区域信息
 * 
 * @Auther: lambert.li
 * @Date: 2018/11/7 17:50
 */
@Getter
@Setter
@Accessors(chain = true)
public class AttApiAreaItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /* 区域ID */
    private String id;

    /* 区域编号 */
    private String code;

    /* 区域名称 */
    private String name;

    public AttApiAreaItem() {}

    public AttApiAreaItem(String id, String code, String name) {
        this.id = id;
        this.code = code;
        this.name = name;
    }
}
