package com.zkteco.zkbiosecurity.att.api.msg.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 统计消息VO
 * 
 * <AUTHOR> href:"mailto:<EMAIL>">zbx.zhong</a>
 * @version v1.0
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel
public class AppStatisticalMsgCountMessageItem implements Serializable {

    /* 人员 id */
    @ApiModelProperty(value = "customerId", example = "")
    private String customerId;
    /* 关联百傲瑞达v5000 id */
    @ApiModelProperty(value = "appId", example = "1234")
    private String appId;
    /**
     * 需要查询的业务code
     */
    private String businessCodes;
}
