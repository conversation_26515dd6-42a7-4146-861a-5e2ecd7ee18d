package com.zkteco.zkbiosecurity.att.bean;

/**
 * usb导入参数对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 9:14
 * @since 1.0.0
 */
public class AttUsbTransactionBean {
    /*
     * 
     第一种方式：参数LogWithSerialNumber=1	CityrayFun=1
    工号、时间、设备ID、状态、验证方式、工作号码、序列号、gAttLog_ID
    
    第二种方式：参数LogWithSerialNumber=1	CityrayFun=0
    工号、时间、设备ID、状态、验证方式、工作号码、序列号
    
    第三种方式：参数LogWithSerialNumber=0	CityrayFun=0
    工号、时间、设备ID、状态、验证方式、工作号码
     */

    /** 用户工号 */
    private String pin;

    /** 验证时间 */
    private String verifyTime;

    private String devId;

    /** 状态 */
    private String status;

    /** 验证方式 */
    private String verifyType;

    /** 工作号码 */
    private String workCodeNum;

    /** 序列号 */
    private String sn;

    /** gAttLog_ID */
    private String gAttLogID;

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public String getVerifyTime() {
        return verifyTime;
    }

    public void setVerifyTime(String verifyTime) {
        this.verifyTime = verifyTime;
    }

    public String getDevId() {
        return devId;
    }

    public void setDevId(String devId) {
        this.devId = devId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getVerifyType() {
        return verifyType;
    }

    public void setVerifyType(String verifyType) {
        this.verifyType = verifyType;
    }

    public String getWorkCodeNum() {
        return workCodeNum;
    }

    public void setWorkCodeNum(String workCodeNum) {
        this.workCodeNum = workCodeNum;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getgAttLogID() {
        return gAttLogID;
    }

    public void setgAttLogID(String gAttLogID) {
        this.gAttLogID = gAttLogID;
    }

    /**
     * 默认构造方法
     */
    public AttUsbTransactionBean() {}

    public AttUsbTransactionBean(String pin, String verifyTime, String devId, String status, String verifyType,
        String workCodeNum) {
        this.pin = pin;
        this.verifyTime = verifyTime;
        this.devId = devId;
        this.status = status;
        this.verifyType = verifyType;
        this.workCodeNum = workCodeNum;
    }

    public AttUsbTransactionBean(String pin, String verifyTime, String devId, String status, String verifyType,
        String workCodeNum, String sn) {
        this.pin = pin;
        this.verifyTime = verifyTime;
        this.devId = devId;
        this.status = status;
        this.verifyType = verifyType;
        this.workCodeNum = workCodeNum;
        this.sn = sn;
    }

    public AttUsbTransactionBean(String pin, String verifyTime, String devId, String status, String verifyType,
        String workCodeNum, String sn, String gAttLogID) {
        this.pin = pin;
        this.verifyTime = verifyTime;
        this.devId = devId;
        this.status = status;
        this.verifyType = verifyType;
        this.workCodeNum = workCodeNum;
        this.sn = sn;
        this.gAttLogID = gAttLogID;
    }
}