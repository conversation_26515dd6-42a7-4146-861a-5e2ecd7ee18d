package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.annotation.GridOperate;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

/**
 * 流程信息
 * 
 * <AUTHOR>
 * @date 2019/5/24
 */
@Setter
@Getter
@GridConfig(operate = true, idField = "id", winHeight = 500, winWidth = 800,
    operates = {
        @GridOperate(type = "custom", url = "attWfApply.do?approve&taskId=(taskId)", click = "handleProcess",
            showConvertor = "showHandleButton", label = "wf_flow_handleProcess"),
        @GridOperate(type = "custom", url = "attWfApply.do?detail&businessKey=(businessKey)", click = "detailView",
            label = "wf_flow_detail_view"),
        @GridOperate(type = "custom", url = "attWfApply.do?cancelApply&businessKey=(businessKey)",
            click = "cancelApplyView", showConvertor = "showcancelApplyButton", label = "att_apply_revoke")})
public class AttProcessInfoItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    @GridColumn(label = "taskId", width = "0", show = true)
    private String taskId;

    @GridColumn(label = "businessKey", width = "0", show = true)
    private String businessKey;

    @GridColumn(checkbox = true, width = "40", sort = "na", show = false)
    private String id;

    private String personId;

    /**
     * 人员编号
     */
    @GridColumn(label = "att_apply_personPin", width = "100", sort = "na", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String personPin;
    /**
     * 姓名
     */
    @GridColumn(label = "att_person_name", width = "100", sort = "na", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String personName;

    /**
     * 英文姓名
     */
    @GridColumn(label = "att_person_lastName", showExpression = "#language!='zh_CN'", width = "100", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String personLastName;

    /**
     * 部门名称
     */
    @GridColumn(label = "att_common_deptName", width = "100", sort = "na")
    private String deptName;

    @GridColumn(label = "att_flow_type", width = "100", sort = "na",
        format = "sign=att_leftMenu_sign,leave=att_leftMenu_leave,trip=att_leftMenu_trip,out=att_leftMenu_out,overtime=att_leftMenu_overtime,adjust=att_leftMenu_adjust,classShift=att_leftMenu_class")
    private String flowType;

    // @GridColumn(label = "att_flow_status", width = "100", sort = "na", format =
    // "1=att_flow_status_processing,2=common_finish")
    private String flowStatus;

    /**
     * 申请时间
     */
    @GridColumn(label = "att_flow_applyTime", width = "140")
    private Date applyTime;

    /**
     * 审批结果
     */
    @GridColumn(label = "att_flow_approvePass", width = "140",
        format = "0=att_flow_status_processing,2=att_apply_pass,-2=att_apply_refuse,3=att_apply_revoke,5=att_exception_refuse,6=att_exception_end")
    private String taskStatus;

    /**
     * 审批时间
     */
    @GridColumn(label = "att_flow_approveTime", width = "140", show = false)
    private Date approveTime;

    @GridColumn(label = "att_flow_operateUser", width = "120", sort = "na", show = false)
    private String operateUser;

    // 控制查看/审批按钮显示
    private boolean handleButton = false;

    public boolean showHandleButton() {
        return handleButton;
    }

    // 控制撤销按钮显示
    private boolean cancelApplyButton = false;

    public boolean showcancelApplyButton() {
        return cancelApplyButton;
    }
}
