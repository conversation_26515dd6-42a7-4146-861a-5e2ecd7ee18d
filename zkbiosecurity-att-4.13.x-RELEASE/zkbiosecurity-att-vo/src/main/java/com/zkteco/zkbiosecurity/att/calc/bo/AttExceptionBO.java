package com.zkteco.zkbiosecurity.att.calc.bo;

import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
 * @version V1.0
 * @date Created In 10:58 2018/7/12
 */
@Setter
@Getter
@ToString
public class AttExceptionBO implements Serializable {

    /**
     * 异常名称（leave:请假、trip:出差、out外出等）
     */
    private String exceptionType;

    /**
     * 异常名称（事假、病假、出差、外出等）
     */
    private String exceptionName;

    /**
     * 异常日期
     */
    private String exceptionDate;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 开始时间
     */
    private Date startDateTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 结束时间
     */
    private Date endDateTime;

    /**
     * 时长
     */
    private String timeLong;

    /* ------------ 补签异常数据字段 ----------- */
    /**
     * 补签时间
     */
    private String signTime;

    /**
     * 流程id
     */
    private String taskId;

    /**
     * 流程状态
     */
    private String taskStatus;
}
