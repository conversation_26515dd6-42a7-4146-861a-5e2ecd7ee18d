package com.zkteco.zkbiosecurity.att.vo;

import java.util.List;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 时间段
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:52
 * @since 1.0.0
 */
@From(after = "ATT_TIMESLOT t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 620, winWidth = 720, minWidth = "180",
    operates = {
        @GridOperate(type = "custom", permission = "att:timeSlot:edit", click = "editTimeSlot",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "att:timeSlot:del", url = "attTimeSlot.do?del&names=(periodName)",
            label = "common_op_del"),})
@Setter
@Getter
@Accessors(chain = true)
public class AttTimeSlotItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 时段名称
     */
    @Column(name = "t.PERIOD_NAME")
    @GridColumn(columnType = "custom", label = "common_name", width = "90", convert = "showEditTimeSlot")
    private String periodName;

    /**
     * 时段编号
     */
    @Column(name = "t.PERIOD_NO")
    @Deprecated
    private String periodNo;

    /**
     * 时段类型（0：正常时间段，1：弹性时间段）
     */
    @Column(name = "t.PERIOD_TYPE")
    @GridColumn(label = "att_timeSlot_periodType", width = "120",
        format = "0=att_timeSlot_normalTime,1=att_timeSlot_elasticTime")
    private Short periodType;

    /**
     * 开始签到时间
     */
    @Column(name = "t.START_SIGN_IN_TIME")
    @GridColumn(label = "att_timeSlot_startSignInTime", width = "150")
    private String startSignInTime;

    /**
     * 上班时间
     */
    @Column(name = "t.TO_WORK_TIME")
    @GridColumn(label = "att_timeSlot_toWorkTime", width = "150")
    private String toWorkTime;

    /**
     * 结束签到时间
     */
    @Column(name = "t.END_SIGN_IN_TIME")
    @GridColumn(label = "att_timeSlot_endSignInTime", show = false)
    @Deprecated
    private String endSignInTime;

    /**
     * 允许迟到分钟数
     */
    @Column(name = "t.ALLOW_LATE_MINUTES")
    @GridColumn(label = "att_timeSlot_allowLateMinutes", show = false)
    private Short allowLateMinutes;

    /**
     * 必须签到（false：否/0，true：是/1）
     */
    @Column(name = "t.IS_MUST_SIGN_IN")
    @GridColumn(label = "att_timeSlot_isMustSignIn", show = false)
    private Boolean isMustSignIn;

    /**
     * 开始签退时间
     */
    @Column(name = "t.START_SIGN_OFF_TIME")
    @GridColumn(label = "att_timeSlot_startSignOffTime", show = false)
    @Deprecated
    private String startSignOffTime;

    /**
     * 下班时间
     */
    @Column(name = "t.OFF_WORK_TIME")
    @GridColumn(label = "att_timeSlot_offWorkTime", width = "150")
    private String offWorkTime;

    /**
     * 结束签退时间
     */
    @Column(name = "t.END_SIGN_OFF_TIME")
    @GridColumn(label = "att_timeSlot_endSignOffTime", width = "150")
    private String endSignOffTime;

    /**
     * 允许早退分钟数
     */
    @Column(name = "t.ALLOW_EARLY_MINUTES")
    @GridColumn(label = "att_timeSlot_allowEarlyMinutes", show = false)
    private Short allowEarlyMinutes;

    /**
     * 必须签退（false：否/0，true：是/1）
     */
    @Column(name = "t.IS_MUST_SIGN_OFF")
    @GridColumn(label = "att_timeSlot_isMustSignOff", show = false)
    private Boolean isMustSignOff;

    /**
     * 工作时长（分钟）
     */
    @Column(name = "t.WORKING_HOURS")
    @GridColumn(label = "att_timeSlot_workingHours", width = "150")
    private Short workingHours;

    /**
     * 是否段间扣除（false：否/0，true：是/1）
     */
    @Column(name = "t.IS_SEGMENT_DEDUCTION")
    @GridColumn(label = "att_timeSlot_isSegmentDeduction", show = false)
    private Boolean isSegmentDeduction;

    /**
     * 段间扣除
     */
    @Column(name = "t.INTER_SEGMENT_DEDUCTION")
    @GridColumn(label = "att_timeSlot_interSegmentDeduction", show = false)
    private Short interSegmentDeduction;

    /**
     * 开始段间时间
     */
    @Column(name = "t.START_SEGMENT_TIME")
    @GridColumn(label = "att_timeSlot_startSegmentTime", show = false)
    @Deprecated
    private String startSegmentTime;

    /**
     * 结束段间时间
     */
    @Column(name = "t.END_SEGMENT_TIME")
    @GridColumn(label = "att_timeSlot_endSegmentTime", show = false)
    @Deprecated
    private String endSegmentTime;

    /**
     * 记为工作日数
     */
    @Column(name = "t.MARK_WORKING_DAYS")
    @GridColumn(label = "att_timeSlot_markWorkingDays", show = false)
    private String markWorkingDays;

    /**
     * 延时是否计加班（false：否/0，true：是/1）
     */
    @Column(name = "t.IS_DELAY_COUNT_OVERTIME")
    @GridColumn(label = "att_timeSlot_isDelayCountOvertime", show = false)
    private Boolean isDelayCountOvertime;

    /**
     * 起记加班时间
     */
    @Column(name = "t.START_OVERTIME")
    @GridColumn(label = "att_timeSlot_startOvertime", show = false)
    private String startOvertime;

    /**
     * 提前是否计加班（false：否/0，true：是/1）
     */
    @Column(name = "t.IS_ADVANCE_COUNT_OVERTIME")
    @GridColumn(label = "att_timeSlot_isAdvanceCountOvertime", show = false)
    private Boolean isAdvanceCountOvertime;

    /**
     * 签到早于时间
     */
    @Column(name = "t.SIGN_IN_ADVANCE_TIME")
    @GridColumn(label = "att_timeSlot_signInAdvanceTime", show = false)
    private String signInAdvanceTime;

    /**
     * 延后是否计加班（false：否/0，true：是/1）
     */
    @Column(name = "t.IS_POSTPONE_COUNT_OVERTIME")
    @GridColumn(label = "att_timeSlot_isPostponeCountOvertime", show = false)
    private Boolean isPostponeCountOvertime;

    /**
     * 签退晚于时间
     */
    @Column(name = "t.SIGN_OUT_POSPONE_TIME")
    @GridColumn(label = "att_timeSlot_signOutPosponeTime", show = false)
    private String signOutPosponeTime;

    /**
     * 是否计加班（false：否/0，true：是/1）
     */
    @Column(name = "t.IS_COUNT_OVERTIME")
    @GridColumn(label = "att_timeSlot_isCountOvertime", show = false)
    private Boolean isCountOvertime;

    /**
     * 弹性时长计算方式 0:两两打卡累积时长 1:首尾打卡计算时长
     */
    @Column(name = "t.ELASTIC_CAL")
    private String elasticCal;

    /**
     * 上班前n分钟内签到有效
     */
    @Column(name = "t.BEFORE_TO_WORK_MINUTES")
    private Integer beforeToWorkMinutes;
    /**
     * 上班后n分钟内签到有效
     */
    @Column(name = "t.AFTER_TO_WORK_MINUTES")
    private Integer afterToWorkMinutes;
    /**
     * 下班前n分钟内签到有效
     */
    @Column(name = "t.BEFORE_OFF_WORK_MINUTES")
    private Integer beforeOffWorkMinutes;
    /**
     * 下班前n分钟内签到有效
     */
    @Column(name = "t.AFTER_OFF_WORK_MINUTES")
    private Integer afterOffWorkMinutes;

    /**
     * 上班n分钟前签到记加班
     */
    @Column(name = "t.BEFORE_WORK_OVERTIME_MINUTES")
    private Integer beforeWorkOvertimeMinutes;
    /**
     * 上班前最短加班分钟数
     */
    @Column(name = "t.MIN_BEFORE_OVERTIME_MINUTES")
    private Integer minBeforeOvertimeMinutes;
    /**
     * 下班n分钟后开始记加班
     */
    @Column(name = "t.AFTER_WORK_OVERTIME_MINUTES")
    private Integer afterWorkOvertimeMinutes;
    /**
     * 下班后最短加班分钟数
     */
    @Column(name = "t.MIN_AFTER_OVERTIME_MINUTES")
    private Integer minAfterOvertimeMinutes;
    /**
     * 是否启动工作时长
     */
    @Column(name = "t.ENABLE_WORKING_HOURS")
    private String enableWorkingHours;

    /**
     * 是否启动弹性上班(true:启用。false：不启用)
     */
    @Column(name = "t.ENABLE_FLEXIBLE_WORK")
    private String enableFlexibleWork;

    /**
     * 可提前上班分钟数
     */
    @Column(name = "t.ADVANCE_WORK_MINUTES")
    private Integer advanceWorkMinutes;

    /**
     * 可延后上班分钟数
     */
    @Column(name = "t.DELAYED_WORK_MINUTES")
    private Integer delayedWorkMinutes;

    /**
     * 上班前最大加班分钟数
     */
    @Column(name = "t.MAX_BEFORE_OVERTIME_MINUTES")
    private Integer maxBeforeOvertimeMinutes;

    /**
     * 下班后最大加班分钟数
     */
    @Column(name = "t.MAX_AFTER_OVERTIME_MINUTES")
    private Integer maxAfterOvertimeMinutes;

    /**
     * 休息时间段数据
     */
    private String breakTimeData;

    /**
     * 休息时间段集合
     */
    private List<AttBreakTimeItem> attBreakTimeItems;

    /**
     * 默认构造方法
     */
    public AttTimeSlotItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AttTimeSlotItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AttTimeSlotItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param periodType
     * @param periodNo
     * @param periodName
     * @param startSignInTime
     * @param toWorkTime
     * @param endSignInTime
     * @param allowLateMinutes
     * @param isMustSignIn
     * @param startSignOffTime
     * @param offWorkTime
     * @param endSignOffTime
     * @param allowEarlyMinutes
     * @param isMustSignOff
     * @param workingHours
     * @param isSegmentDeduction
     * @param interSegmentDeduction
     * @param startSegmentTime
     * @param endSegmentTime
     * @param markWorkingDays
     * @param isDelayCountOvertime
     * @param startOvertime
     * @param isAdvanceCountOvertime
     * @param signInAdvanceTime
     * @param isPostponeCountOvertime
     * @param signOutPosponeTime
     * @param isCountOvertime
     */
    public AttTimeSlotItem(String id, Short periodType, String periodNo, String periodName, String startSignInTime,
        String toWorkTime, String endSignInTime, Short allowLateMinutes, Boolean isMustSignIn, String startSignOffTime,
        String offWorkTime, String endSignOffTime, Short allowEarlyMinutes, Boolean isMustSignOff, Short workingHours,
        Boolean isSegmentDeduction, Short interSegmentDeduction, String startSegmentTime, String endSegmentTime,
        String markWorkingDays, Boolean isDelayCountOvertime, String startOvertime, Boolean isAdvanceCountOvertime,
        String signInAdvanceTime, Boolean isPostponeCountOvertime, String signOutPosponeTime, Boolean isCountOvertime) {
        super();
        this.id = id;
        this.periodType = periodType;
        this.periodNo = periodNo;
        this.periodName = periodName;
        this.startSignInTime = startSignInTime;
        this.toWorkTime = toWorkTime;
        this.endSignInTime = endSignInTime;
        this.allowLateMinutes = allowLateMinutes;
        this.isMustSignIn = isMustSignIn;
        this.startSignOffTime = startSignOffTime;
        this.offWorkTime = offWorkTime;
        this.endSignOffTime = endSignOffTime;
        this.allowEarlyMinutes = allowEarlyMinutes;
        this.isMustSignOff = isMustSignOff;
        this.workingHours = workingHours;
        this.isSegmentDeduction = isSegmentDeduction;
        this.interSegmentDeduction = interSegmentDeduction;
        this.startSegmentTime = startSegmentTime;
        this.endSegmentTime = endSegmentTime;
        this.markWorkingDays = markWorkingDays;
        this.isDelayCountOvertime = isDelayCountOvertime;
        this.startOvertime = startOvertime;
        this.isAdvanceCountOvertime = isAdvanceCountOvertime;
        this.signInAdvanceTime = signInAdvanceTime;
        this.isPostponeCountOvertime = isPostponeCountOvertime;
        this.signOutPosponeTime = signOutPosponeTime;
        this.isCountOvertime = isCountOvertime;
    }
}