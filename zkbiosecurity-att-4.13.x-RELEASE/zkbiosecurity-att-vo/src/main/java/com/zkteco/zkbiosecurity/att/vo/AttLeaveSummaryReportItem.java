package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 请假汇总
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/8/18 11:33
 */
@From(after = "ATT_LEAVE t ")
@GroupBy(after = "t.PERS_PERSON_PIN")
@OrderBy(after = "t.PERS_PERSON_PIN DESC")
@GridConfig(winHeight = 400, winWidth = 600)
@Setter
@Getter
@Accessors(chain = true)
public class AttLeaveSummaryReportItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    @GridColumn(show = false)
    private String id;

    /** 人员ID */
    private String personId;

    /** 人员编号 */
    @Column(name = "t.PERS_PERSON_PIN")
    @GridColumn(label = "att_person_pin", width = "120", sortNo = 1, encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String personPin;

    /** 姓名 */
    @GridColumn(label = "att_person_name", width = "120", sortNo = 2, encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String personName;

    /**
     * 英文（lastName）
     */
    @GridColumn(label = "att_person_lastName", showExpression = "#language!='zh_CN'", width = "100", sortNo = 3, encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String personLastName;

    /** 部门ID */
    @Condition(value = "t.AUTH_DEPT_ID")
    private String deptId;

    /** 部门编号 */
    @GridColumn(label = "pers_dept_deptNo", width = "120", show = false, sortNo = 4)
    private String deptCode;

    /** 部门名称 */
    @GridColumn(label = "pers_dept_deptName", width = "120", sortNo = 5)
    private String deptName;

    @GridColumn(dynamicColumn = "leaveTypeDyna", sortNo = 6)
    private Map<String, Object> leaveTypeMap = new HashMap<>();;

    /**
     * 有GroupBy，用Condition加过滤条件
     */
    @Condition(value = "t.END_DATETIME", equalTag = ">=")
    private Date startApplyDateTime;

    @Condition(value = "t.START_DATETIME", equalTag = "<=")
    private Date endApplyDateTime;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "t.PERS_PERSON_PIN", equalTag = "in")
    private String inPersonPin;

    private String likeName;

    @Condition(value = "t.FLOW_STATUS", equalTag = "=")
    private String flowStatus;

    /**
     * 默认构造方法
     */
    public AttLeaveSummaryReportItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AttLeaveSummaryReportItem(Boolean equals) {
        super(equals);
        this.id = UUID.randomUUID().toString();
    }
}
