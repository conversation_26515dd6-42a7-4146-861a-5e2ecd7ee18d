/**
 * @author: GenerationTools
 * @date: 2018-02-08 下午08:27 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @author: GenerationTools
 * @date: 2018-02-08 下午08:27
 */
@From(after = "ATT_GROUPSCH t LEFT JOIN ATT_GROUP ag ON ag.ID = t.GROUP_ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 500, winWidth = 650,
    operates = {
        @GridOperate(type = "edit", permission = "att:groupsch:edit", url = "attGroupSch.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "att:groupsch:del", click = "flushAttGroupSch",
            url = "attGroupSch.do?del&names=(groupName)", label = "common_op_del")})
@Setter
@Getter
@Accessors(chain = true)
public class AttGroupSchItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 分组名称
     */
    @Column(name = "ag.GROUP_NAME")
    @GridColumn(label = "att_common_groupName", width = "120", columnType = "edit",
        editPermission = "att:groupsch:edit", editUrl = "attGroupSch.do?edit")
    private String groupName;

    /**
     * 分组编号
     */
    @Column(name = "ag.GROUP_NO")
    @GridColumn(label = "att_common_groupNo", width = "120")
    private String groupNo;

    /**
     * 排班类型
     */
    @Column(name = "t.SCHEDULE_TYPE")
    @GridColumn(label = "att_schedule_type", format = "0=att_schedule_normal,1=att_schedule_intelligent", width = "120")
    private Short scheduleType;

    /**
     * 考勤方式（0/按班次正常刷卡，1/一天内任刷一次有效卡，2/只计算刷卡时间，3/免刷卡）
     */
    @Column(name = "t.ATTENDANCE_MODE")
    @GridColumn(label = "att_shift_attendanceMode", width = "120",
        format = "0=att_shift_shiftNormal,1=att_shift_oneDayOneCard,2=att_shift_onlyBrushTime,3=att_shift_notBrushCard",
        show = false)
    private Short attendanceMode;

    /**
     * 加班方式（0/电脑自动计算，1/加班必须申请，2/必须加班否则旷工，3/时长为较小者-延后加班和加班单对比，4/不算加班）
     */
    @Column(name = "t.OVERTIME_MODE")
    @GridColumn(label = "att_shift_overtimeMode", width = "120",
        format = "0=att_shift_autoCalc,1=att_shift_mustApply,2=att_shift_mustOvertime,3=att_shift_timeSmaller,4=att_shift_notOvertime",
        show = false)
    private Short overtimeMode;

    /**
     * 加班标记（0/平时，1/休息日，2/节假日）
     */
    @Column(name = "t.OVERTIME_REMARK")
    @GridColumn(label = "att_shift_overtimeSign", width = "120",
        format = "0=att_shift_normal,1=att_shift_restday,2=common_leftMenu_holiday", show = false)
    private Short overtimeRemark;

    /**
     * 班次编号
     */
    private String shiftNo;

    /** 班次名称 **/
    @GridColumn(label = "att_shift_name", width = "120", sort = "na")
    private String shiftName;

    /**
     * 起始日期
     */
    @Column(name = "t.START_DATE")
    @DateType(type = "date")
    @GridColumn(label = "common_startTime", width = "150")
    private Date startDate;

    /**
     * 结束日期
     */
    @Column(name = "t.END_DATE")
    @DateType(type = "date")
    @GridColumn(label = "common_endTime", width = "150")
    private Date endDate;

    /**
     * 分组id
     */
    @Column(name = "t.GROUP_ID")
    @GridColumn(show = false)
    private String groupId;

    /**
     * 班次ID(多个用逗号分开)
     */
    private String shiftIds;

    /**
     * 默认构造方法
     */
    public AttGroupSchItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AttGroupSchItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AttGroupSchItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param startDate
     * @param endDate
     * @param scheduleType
     * @param attendanceMode
     * @param overtimeMode
     * @param overtimeRemark
     */
    public AttGroupSchItem(String id, Date startDate, Date endDate, Short scheduleType, Short attendanceMode,
        Short overtimeMode, Short overtimeRemark) {
        super();
        this.id = id;
        this.startDate = startDate;
        this.endDate = endDate;
        this.scheduleType = scheduleType;
        this.attendanceMode = attendanceMode;
        this.overtimeMode = overtimeMode;
        this.overtimeRemark = overtimeRemark;
    }
}