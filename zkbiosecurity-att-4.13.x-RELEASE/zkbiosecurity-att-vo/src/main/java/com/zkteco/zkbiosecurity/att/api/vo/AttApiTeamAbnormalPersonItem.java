package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 我的团队考勤-按考勤结果查看人员
 * 
 * <AUTHOR>
 * @date 2019/04/16
 */
@Getter
@Setter
public class AttApiTeamAbnormalPersonItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    private String personName;

    /**
     * 人员编号
     */
    private String personPin;

    /**
     * 职位名称
     */
    private String positionName;

    /**
     * 头像地址
     */
    private String avatarUrl;

    /**
     * 班次名称
     */
    private String shiftName;

}
