package com.zkteco.zkbiosecurity.att.vo;

import java.io.Serializable;
import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Setter;
import lombok.Getter;
import lombok.experimental.Accessors;

/**
 * 设备操作日志
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 17:09
 * @since 1.0.0
 */
@From(after = "ATT_DEVICE_OP_LOG t ")
@OrderBy(after = "t.OP_TIME DESC")
@GridConfig(operate = true, idField = "id")
@Setter
@Getter
@Accessors(chain = true)
public class AttDeviceOpLogItem extends BaseItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(show = false)
    private String id;

    /** 设备SN */
    @Column(name = "t.DEV_SN")
    @GridColumn(label = "att_device_op_log_dev_sn", width = "150")
    private String devSn;

    /** 操作时间 */
    @Column(name = "t.OP_TIME")
    @GridColumn(label = "att_device_op_log_op_time", width = "150")
    private Date opTime;

    /** 操作代码 */
    @Column(name = "t.OP_TYPE")
    @GridColumn(label = "att_device_op_log_op_type", width = "100", show = false)
    private String opType;

    /** 操作人员 */
    @Column(name = "t.OPERATOR_PIN")
    // @GridColumn(label = "att_device_op_log_operator_pin", width = "110")
    private String operator;

    /** 操作人员姓名 */
    @Column(name = "t.OPERATOR_NAME")
    // @GridColumn(label = "att_device_op_log_operator_name", width = "110")
    private String operatorName;

    /** 操作内容 */
    @Column(name = "t.OP_CONTENT")
    @GridColumn(label = "att_device_op_log_op_content", width = "150")
    private String opContent;

    /** 操作对象值 */
    @Column(name = "t.OP_WHO_VALUE")
    @GridColumn(label = "att_device_op_log_op_who_value", width = "100", show = false)
    private String opWhoValue;

    /** 操作对象内容 */
    @Column(name = "t.OP_WHO_CONTENT")
    @GridColumn(label = "att_device_op_log_op_who_content", width = "220")
    private String opWhoContent;

    /** 操作对象1 */
    @Column(name = "t.OP_VALUE1")
    @GridColumn(label = "att_device_op_log_op_value1", width = "100", show = false)
    private String opValue1;

    /** 操作对象1描述 */
    @Column(name = "t.OP_VALUE_CONTENT1")
    @GridColumn(label = "att_device_op_log_op_value_content1", width = "220")
    private String opValueContent1;

    /** 操作对象2 */
    @Column(name = "t.OP_VALUE1")
    @GridColumn(label = "att_device_op_log_op_value1", width = "100", show = false)
    private String opValue2;

    /** 操作对象2描述 */
    @Column(name = "t.OP_VALUE_CONTENT2")
    @GridColumn(label = "att_device_op_log_op_value_content2", width = "220")
    private String opValueContent2;

    /** 操作对象3 */
    @Column(name = "t.OP_VALUE3")
    @GridColumn(label = "att_device_op_log_op_value3", width = "100", show = false)
    private String opValue3;

    /** 操作对象3描述 */
    @Column(name = "t.OP_VALUE_CONTENT3")
    @GridColumn(label = "att_device_op_log_op_value_content3", width = "150", show = false)
    private String opValueContent3;

    @Condition(value = "t.OP_TIME", equalTag = ">=")
    private Date beginDate;

    @Condition(value = "t.OP_TIME", equalTag = "<=")
    private Date endDate;

    /**
     * 区域Id
     */
    @Column(name = "t.AUTH_AREA_ID")
    private String areaId;

    @Condition(value = "t.AUTH_AREA_ID", equalTag = "in")
    private String inAreaId;

    public AttDeviceOpLogItem() {
        super();
    }

    public AttDeviceOpLogItem(String id) {
        super();
        this.id = id;
    }
}