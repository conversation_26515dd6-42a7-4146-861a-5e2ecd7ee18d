package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

/**
 * 签到地址
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:09
 * @since 1.0.0
 */
@From(after = "ATT_SIGN_ADDRESS t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 660, winWidth = 900,
    operates = {
        @GridOperate(type = "edit", permission = "att:signAddress:edit", url = "attSignAddress.do?edit&id=(id)",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "att:signAddress:del", url = "attSignAddress.do?del&id=(id)",
            label = "common_op_del")})
@Getter
@Setter
public class AttSignAddressItem extends BaseItem {

    /**
     * ID
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 区域名称集合
     */
    @GridColumn(label = "base_area_name", width = "150", sort = "na", columnType = "edit",
        editPermission = "att:signAddress:edit", editUrl = "attSignAddress.do?edit&id=(id)")
    private String areaNames;

    /**
     * 经度
     */
    @Column(name = "t.LONGITUDE")
    @GridColumn(label = "att_signAddress_longitude", width = "150", sort = "na")
    private String longitude;

    /**
     * 纬度
     */
    @Column(name = "t.LATITUDE")
    @GridColumn(label = "att_signAddress_latitude", width = "150", sort = "na")
    private String latitude;

    /**
     * 范围距离 (m:米)
     */
    @Column(name = "t.VALID_RANGE")
    @GridColumn(label = "att_signAddress_range", width = "150", sort = "na")
    private Integer validRange;

    /** 地图类型 */
    @Column(name = "t.MAP_TYPE")
    private String mapType;

    /** 地图密钥 */
    @Column(name = "t.MAP_KEY")
    private String mapKey;

    /**
     * 地址
     */
    @Column(name = "t.ADDRESS")
    @GridColumn(label = "att_signAddress_address", width = "150", sort = "na")
    private String address;

    /**
     * ID集合
     */
    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    /**
     * 区域ID集合
     */
    private String areaIds;

    public AttSignAddressItem() {
        super();
    }

    public AttSignAddressItem(String id) {
        super();
        this.id = id;
    }

}
