package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 考勤原始记录、打卡记录
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/11/23 11:10
 * @since 1.0.0
 */
@From(after = "ATT_TRANSACTION t ")
@OrderBy(after = "t.ATT_DATETIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600, operates = {})
@Setter
@Getter
@Accessors(chain = true)
public class AttTransactionItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(show = false, sortNo = 0)
    private String id;

    /**
     * 人员编号
     */
    @Column(name = "t.PERS_PERSON_PIN")
    @GridColumn(label = "att_person_pin", width = "120", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String personPin;

    /**
     * 姓名
     */
    @Column(name = "t.PERS_PERSON_NAME")
    @GridColumn(label = "att_person_name", width = "120", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String personName;

    /**
     * 英文（lastName）
     */
    @Column(name = "t.PERS_PERSON_LAST_NAME")
    @GridColumn(label = "att_person_lastName", showExpression = "#language!='zh_CN'", width = "120", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String personLastName;

    /**
     * 部门id
     */
    @Column(name = "t.AUTH_DEPT_ID", equalTag = "=")
    private String deptId;

    /**
     * 部门编号
     */
    @Column(name = "t.AUTH_DEPT_CODE", equalTag = "=")
    // @GridColumn(label = "pers_dept_deptNo", width = "150")
    private String deptCode;

    /**
     * 部门名称
     */
    @Column(name = "t.AUTH_DEPT_NAME")
    @GridColumn(label = "pers_dept_deptName", width = "130")
    private String deptName;

    /**
     * 区域Id
     */
    @Column(name = "t.AUTH_AREA_ID", equalTag = "=")
    @GridColumn(show = false)
    private String areaId;

    /**
     * 区域编号
     */
    @Column(name = "t.AUTH_AREA_NO")
    @GridColumn(show = false)
    private String areaNo;

    /**
     * 区域名称
     */
    @Column(name = "t.AUTH_AREA_NAME")
    @GridColumn(label = "pers_person_attArea", sort = "na", width = "130")
    private String areaName;

    /**
     * 设备ID (停车场的通道ID/门禁的门ID/视频的通道ID)
     */
    @Column(name = "t.DEVICE_ID", equalTag = "=")
    @GridColumn(show = false)
    private String deviceId;

    /**
     * 设备序列号
     */
    @Column(name = "t.DEVICE_SN")
    @GridColumn(label = "common_dev_sn", width = "130")
    private String deviceSn;

    /**
     * 设备名称
     */
    @Column(name = "t.DEVICE_NAME")
    @GridColumn(label = "common_dev_name", width = "120")
    private String deviceName;

    /**
     * 门编号
     */
    @Column(name = "t.DOOR_NO")
    @GridColumn(show = false)
    private Short doorNo;

    /**
     * 考勤日期时间
     */
    @Column(name = "t.ATT_DATETIME")
    @GridColumn(label = "att_statistical_attDatetime", width = "150")
    private Date attDatetime;

    /**
     * 考勤日期
     */
    @Column(name = "t.ATT_DATE")
    @GridColumn(show = false)
    private String attDate;

    /**
     * 考勤时间
     */
    @Column(name = "t.ATT_TIME")
    @GridColumn(show = false)
    private String attTime;

    /**
     * 考勤状态
     */
    @Column(name = "t.ATT_STATE")
    @GridColumn(label = "att_cardStatus_attState", width = "150")
    private String attState;

    /**
     * 验证方式
     */
    @Column(name = "t.ATT_VERIFY")
    @GridColumn(label = "common_verifyMode_entiy", width = "120", columnType = "dic", key = "AttVerifyMode")
    private String attVerify;

    /**
     * 考勤照片
     */
    @GridColumn(label = "att_statistical_attPhoto", width = "130", convert = "attConvertToLinkage",
            columnType = "custom", sort = "na")
    @Column(name = "t.ATT_PHOTO_URL")
    private String attPhotoUrl;

    // 新增签到地点属性 add by bob.liu 20190611
    @Column(name = "t.ATT_PLACE", encryptConverter = true)
    @GridColumn(show = false)
    private String attPlace;

    /**
     * 标识
     */
    @Column(name = "t.MARK", equalTag = "=")
    @GridColumn(label = "att_statistical_dataSources", width = "120",
        format = "att=att_statistical_att,acc=att_statistical_acc,park=att_statistical_park,ins=att_attPoint_ins,pid=att_attPoint_pid,api-FR=att_statistical_faceRecognition,app=att_statistical_app,vms=att_statistical_vms,att-sign=att_api_sign,vid=att_statistical_vms,att-sdc=att_sdc_name,ivs=ivs_module,psg=att_statistical_psg,esdc=esdc_module")
    private String mark;

    /**
     * 考勤点名称
     */
    @GridColumn(label = "att_attPoint_name", width = "140")
    private String pointName;

    /** 是否带口罩 0：表示没有佩戴口罩；1：表示有佩戴口罩 */
    @Column(name = "t.MASK_FLAG")
    private String maskFlag;

    /** 体温 摄氏度℃ */
    @Column(name = "t.TEMPERATURE")
    private String temperature;

    /** 经度 */
    @Column(name = "t.LONGITUDE")
    @GridColumn(label = "att_signAddress_longitude", width = "120")
    private String longitude;

    /** 纬度 */
    @Column(name = "t.LATITUDE")
    @GridColumn(label = "att_signAddress_latitude", width = "120")
    private String latitude;

    @Condition(value = "t.DEVICE_SN", equalTag = "in")
    private String deviceSnIn;

    @Condition(value = "t.MARK", equalTag = "not in")
    private String notMark;

    @Condition(value = "t.ATT_DATETIME", equalTag = ">=")
    private Date beginDate;

    @Condition(value = "t.ATT_DATETIME", equalTag = "<=")
    private Date endDate;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "(lower(t.PERS_PERSON_NAME) LIKE ''%{0}%'' OR lower(t.PERS_PERSON_LAST_NAME) LIKE ''%{0}%'')")
    private String likeName;

    // park不需要根据区域过滤
    @Condition(value = "(t.mark = ''park'' or t.AUTH_AREA_ID IN ({0}))", formatType = "quote")
    private String inAreaId;

    @Condition(value = "t.CREATE_TIME", equalTag = ">=")
    private Date startCreateTime;

    @Condition(value = "t.CREATE_TIME", equalTag = "<=")
    private Date endCreateTime;

    @Condition(
            value = "t.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    /** 考勤消息推送到line所需字段 */
    /** 设备名称 */
    private String devName;

    public AttTransactionItem() {
        super();
    }

    public AttTransactionItem(Boolean equals) {
        super(equals);
    }

    public AttTransactionItem(String id) {
        super(true);
        this.id = id;
    }

    public AttTransactionItem(String id, String deptId, String deptCode, String deptName, String personPin,
        String personName, String personLastName, String areaId, String areaNo, String areaName, String deviceId,
        String deviceSn, Short doorNo, Date attDatetime, String attDate, String attTime, String attState,
        String attVerify, String mark) {
        super();
        this.id = id;
        this.deptId = deptId;
        this.deptCode = deptCode;
        this.deptName = deptName;
        this.personPin = personPin;
        this.personName = personName;
        this.personLastName = personLastName;
        this.areaId = areaId;
        this.areaNo = areaNo;
        this.areaName = areaName;
        this.deviceId = deviceId;
        this.deviceSn = deviceSn;
        this.doorNo = doorNo;
        this.attDatetime = attDatetime;
        this.attDate = attDate;
        this.attTime = attTime;
        this.attState = attState;
        this.attVerify = attVerify;
        this.mark = mark;
    }
}