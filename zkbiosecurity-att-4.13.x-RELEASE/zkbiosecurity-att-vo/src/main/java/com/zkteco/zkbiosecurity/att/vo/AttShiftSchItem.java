package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 周期排班使用
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 16:32
 * @since 1.0.0
 */
@From(after = "ATT_SHIFT t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig
@Setter
@Getter
@Accessors(chain = true)
public class AttShiftSchItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 班次名称
     */
    @Column(name = "t.SHIFT_NAME")
    @GridColumn(label = "common_name", width = "70")
    private String shiftName;

    /**
     * 班次编号
     */
    @Column(name = "t.SHIFT_NO")
    @GridColumn(label = "common_number", width = "70")
    private String shiftNo;

    /**
     * 班次类型
     */
    @Column(name = "t.SHIFT_TYPE")
    @GridColumn(label = "att_shift_type", width = "70", format = "0=att_shift_regularShift,1=att_shift_flexibleShift")
    private Short shiftType;

    /**
     * 排班类型，普通排班0，弹性排班1
     */
    private String scheduleType;

    public AttShiftSchItem() {
        super();
    }
}