package com.zkteco.zkbiosecurity.att.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class AttShortcutKeyInfoItem {

    /** 设备ID集合 */
    private String deviceIds;


    /** 快捷键ID */
    /** 1=F1、2=F2、3=F3、4=F4、5=F5、6=F6、7=F7、8=F8*/
    private String keyId;

    /** 快捷键功能 */
    /** 0=未定义、1=状态键、2=工作号码、3=短消息、4=按键求助、5=查下考勤记录、6=查询最后考勤记录 */
    private String keyFun;

    /** 考勤状态 */
    private String statusCode;

    /** 状态名称 */
    private String showName;

    /** 自动切换 */
    private String autoState;

    /** 自动切换类型1每天，0按星期 */
    private String autoType;

    /** 周一到周日自动切换时间，08:00;09:00;10:00;11:00;12:00;13:00;14:00 */
    // private String autoTime;

    private String dayAutoTime;

    /** 周日是否切换 */
    private String sun;

    private String sunAutoTime;

    /** 周一是否切换 */
    private String mon;

    private String monAutoTime;

    /** 周二是否切换 */
    private String tue;

    private String tueAutoTime;

    /** 周三是否切换 */
    private String wed;

    private String wedAutoTime;

    /** 周四是否切换 */
    private String thu;

    private String thuAutoTime;

    /** 周五是否切换 */
    private String fri;

    private String friAutoTime;

    /** 周六是否切换 */
    private String sat;

    private String satAutoTime;
}

