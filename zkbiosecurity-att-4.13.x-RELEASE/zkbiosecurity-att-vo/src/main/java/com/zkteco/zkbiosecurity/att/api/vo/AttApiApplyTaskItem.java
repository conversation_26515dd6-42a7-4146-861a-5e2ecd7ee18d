package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 2019/3/5 15:06
 */
@Getter
@Setter
@Accessors(chain = true)
public class AttApiApplyTaskItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 申请任务ID */
    private String taskId;

    /** 人员编号 */
    private String pin;

    /** 人员姓名 */
    private String name;

    /** 人员头像 */
    private String photoPath;

    /** 业务类型，如加班填写加班类型，请假填写假类 */
    private String businessType;

    /** 流程类型：sign=补签、leave=请假、overtime=加班、out=外出、trip=出差 */
    private String flowType;

    /** 时长 */
    private String timeLong;

    /** 审批状态0：审批通过，1：未审批，2：已驳回、3：撤销 */
    private String auditStatus;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    /** 备注 */
    private String remark;

    /** 月补签次数统计 */
    private Integer monthSignCount;

    /** 审批进度：等待XXX审批 */
    private String auditNote;

    /** 班次名称 */
    private String shiftName;

    /** 班次打卡时间 如 08:30-12:00;13:30-],表示下班时间漏卡 */
    private String shiftCardValid;

    /** 补签时间 如 08:30-12:00;13:30-18:30,与打卡时间对应，表示下班时间补卡 */
    private String signCardValid;

    /** 申请时间 */
    private String applyTime;

    /** 补签时间 */
    private String signTime;

    /** 审批时间 */
    private String auditTime;

    /** 请假文件地址 */
    private String photoUrlList;
}
