package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 临时排班（人员、部门、分组）
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:19
 * @since 1.0.0
 */
@From(after = "ATT_TEMPSCH t LEFT JOIN ATT_GROUP ag ON ag.ID = t.GROUP_ID LEFT JOIN PERS_PERSON pp ON pp.pin = t.PERS_PERSON_PIN LEFT JOIN AUTH_DEPARTMENT ad ON (ad.ID = t.AUTH_DEPT_ID OR ad.ID = pp.auth_dept_id)")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 700, winWidth = 1080,
    operates = {
        @GridOperate(type = "edit", permission = "att:schDetails:edit", url = "attTempSch.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "custom", permission = "att:schDetails:del", click = "attSchDetailsDelVo",
            label = "common_op_del")})
@Setter
@Getter
@Accessors(chain = true)
public class AttTempSchItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /** 人员id */
    @Column(name = "t.PERS_PERSON_ID")
    private String personId;

    /** 人员编号 */
    @Column(name = "t.PERS_PERSON_PIN")
    @GridColumn(label = "att_person_pin", sort = "na", width = "120", columnType = "edit",
        editPermission = "att:schDetails:edit", editUrl = "attTempSch.do?edit", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String personPin;

    /** 姓名 */
    @Column(name = "pp.NAME")
    @GridColumn(label = "att_person_name", sort = "na", width = "120", columnType = "edit",
        editPermission = "att:schDetails:edit", editUrl = "attTempSch.do?edit", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String personName;

    @Column(name = "pp.LAST_NAME")
    @GridColumn(label = "att_person_lastName", width = "120", showExpression = "#language!='zh_CN'", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String personLastName;

    private String likeName;

    /** 部门ID */
    @Column(name = "t.AUTH_DEPT_ID", equalTag = "=")
    @GridColumn(show = false)
    private String deptId;

    /** 部门编码 */
    @Column(name = "ad.CODE")
    private String deptCode;

    /** 部门名称 */
    @Column(name = "ad.NAME")
    @GridColumn(label = "pers_dept_deptName", sort = "na", width = "120", columnType = "edit",
        editPermission = "att:schDetails:edit", editUrl = "attTempSch.do?edit")
    private String deptName;

    /** 分组编号 */
    @Column(name = "ag.GROUP_NO")
    private String groupNo;

    /** 分组名称 */
    @Column(name = "ag.GROUP_NAME")
    @GridColumn(label = "att_common_groupName", sort = "na", width = "120", columnType = "edit",
        editPermission = "att:schDetails:edit", editUrl = "attTempSch.do?edit")
    private String groupName;

    /** 临时类型（分组、部门、人员） */
    @Column(name = "t.TEMP_TYPE")
    private Short tempType;

    /** 排班类型 */
    @Column(name = "t.SCHEDULE_TYPE")
    private Short scheduleType;

    /** 工作类型（正常上班/周末加班/节假日加班） */
    @Column(name = "t.SHIFT_WORKTYPE")
    @GridColumn(label = "att_shift_workType", width = "120", format = "normalWork=att_shift_normalWork,weekendOt=att_overtime_rest,holidayOt=att_shift_holidayOt", show = false)
    private String workType;

    /** 起始日期 */
    @Column(name = "t.START_DATE")
    @DateType(type = "date")
    @GridColumn(label = "common_startTime", width = "120")
    private Date startDate;

    /** 结束日期 */
    @Column(name = "t.END_DATE")
    @DateType(type = "date")
    @GridColumn(label = "common_endTime", width = "120")
    private Date endDate;

    /** 班次编号 */
    private String shiftNo;

    /** 班次名称 **/
    private String shiftName;

    /** 时间段名称 **/
    @GridColumn(label = "att_personSch_timeSoltName", width = "180", sort = "na")
    private String timeSlotName;

    /** 考勤方式（0/按班次正常刷卡，1/一天内任刷一次有效卡，2/只计算刷卡时间，3/免刷卡） */
    @Column(name = "t.ATTENDANCE_MODE")
    @GridColumn(label = "att_shift_attendanceMode",
        format = "0=att_shift_shiftNormal,1=att_shift_oneDayOneCard,2=att_shift_onlyBrushTime,3=att_shift_notBrushCard",
        show = false)
    private Short attendanceMode;

    /**
     * 加班方式（0/电脑自动计算，1/加班必须申请，2/必须加班否则旷工，3/时长为较小者-延后加班和加班单对比，4/不算加班）
     */
    @Column(name = "t.OVERTIME_MODE")
    @GridColumn(label = "att_shift_overtimeMode",
        format = "0=att_shift_autoCalc,1=att_shift_mustApply,2=att_shift_mustOvertime,3=att_shift_timeSmaller,4=att_shift_notOvertime",
        show = false)
    private Short overtimeMode;

    /**
     * 加班标记（0/平时，1/休息日，2/节假日）
     */
    @Column(name = "t.OVERTIME_REMARK")
    @GridColumn(label = "att_shift_overtimeSign",
        format = "0=att_shift_normal,1=att_shift_restday,2=common_leftMenu_holiday", show = false)
    private Short overtimeRemark;

    /** 分组id */
    @Column(name = "t.GROUP_ID")
    @GridColumn(show = false)
    private String groupId;

    /** 查询条件：起始日期 */
    @Condition(value = "t.END_DATE", equalTag = ">=")
    private Date startTime;

    /** 查询条件：结束日期 */
    @Condition(value = "t.START_DATE", equalTag = "<=")
    private Date endTime;

    @Condition(value = "ad.ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "t.PERS_PERSON_ID", equalTag = "in")
    private String inPersonId;

    private String inGroupId;

    @Condition(value = "t.PERS_PERSON_PIN", equalTag = "in")
    private String inPersonPin;

    /** 班次ID(多个用逗号分开) */
    private String shiftIds;

    /** 时间段ID(多个用逗号分开) */
    private String timeSlotIds;

    /** 导出使用 */
    @GridColumn(show = false)
    private Map<String, Object> map = new HashMap<>();

    /** 是否包含下级 */
    private String isIncludeLower;

    public AttTempSchItem() {
        super();
    }

    public AttTempSchItem(Boolean equals) {
        super(equals);
    }

    public AttTempSchItem(String id) {
        super(true);
        this.id = id;
    }

    public AttTempSchItem(String id, Date startDate, Date endDate, Short scheduleType, Short tempType,
        Short attendanceMode, Short overtimeMode, Short overtimeRemark) {
        super();
        this.id = id;
        this.startDate = startDate;
        this.endDate = endDate;
        this.scheduleType = scheduleType;
        this.tempType = tempType;
        this.attendanceMode = attendanceMode;
        this.overtimeMode = overtimeMode;
        this.overtimeRemark = overtimeRemark;
    }
}