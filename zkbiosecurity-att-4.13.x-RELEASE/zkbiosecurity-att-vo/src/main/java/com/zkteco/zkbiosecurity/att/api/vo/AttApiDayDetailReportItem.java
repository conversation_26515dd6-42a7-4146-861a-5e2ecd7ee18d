package com.zkteco.zkbiosecurity.att.api.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date: 2019/7/8 16:24
 */
@Getter
@Setter
@Accessors(chain = true)
public class AttApiDayDetailReportItem implements Serializable {

    // 人员
    /**
     * 编号
     */
    private String personPin;

    /**
     * 姓名
     */
    private String personName;

    /**
     * 英文（lastName）
     */
    private String personLastName;

    // 部门
    /**
     * ID
     */
    private String deptId;

    /**
     * 编号
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 考勤日期
     */
    private String attDate;

    /**
     * 星期
     */
    private String week;

    // 班次信息
    /**
     * 编号
     */
    private String shiftNo;

    /**
     * 名称
     */
    private String shiftName;

    /**
     * 上下班时间
     */
    private String shiftTimeData;

    /**
     * 打卡数据
     */
    private String cardValidData;

    /**
     * 打卡次数
     */
    private Integer cardValidCount;

    // 出勤（分）
    /**
     * 应该
     */
    private Integer shouldMinute;

    /**
     * 实际
     */
    private Integer actualMinute;

    /**
     * 有效
     */
    private Integer validMinute;

    // 加班（分）
    /**
     * 平时
     */
    private Integer overtimeUsualMinute;

    /**
     * 休息
     */
    private Integer overtimeRestMinute;

    /**
     * 节日
     */
    private Integer overtimeHolidayMinute;

    /**
     * 合计
     */
    private Integer overtimeMinute;

    // 迟到次数
    /**
     * 次数数据
     */
    private String lateCountData;
    /**
     * 合计
     */
    private Integer lateCountTotal;

    // 迟到分钟数
    /**
     * 分钟数数据
     */
    private String lateMinuteData;
    /**
     * 合计
     */
    private Integer lateMinuteTotal;

    // 早退次数
    /**
     * 次数数据
     */
    private String earlyCountData;
    /**
     * 合计
     */
    private Integer earlyCountTotal;

    // 早退分钟数
    /**
     * 分钟数数据
     */
    private String earlyMinuteData;
    /**
     * 合计
     */
    private Integer earlyMinuteTotal;

    // 异常（分）
    /**
     * 旷工
     */
    private Integer absentMinute;

    /**
     * 请假
     */
    private Integer leaveMinute;

    /**
     * 出差
     */
    private Integer tripMinute;

    /**
     * 外出
     */
    private Integer outMinute;

    // 出勤（时）
    /**
     * 应该
     */
    private String shouldHour;

    /**
     * 实际（有效）
     */
    private String actualHour;

    /**
     * 旷工(时)
     */
    private String absentHour;

    /**
     * 缺卡次数
     */
    private Integer lackCardCount;

    /**
     * 打卡记录
     */
    private String cardRecord;

    /**
     * 应该(带单位)
     */
    private String should;

    /**
     * 实际(带单位)
     */
    private String actual;

    /**
     * 有效(带单位)
     */
    private String valid;
    /**
     * 迟到(带单位)
     */
    private String late;
    /**
     * 早退(带单位)
     */
    private String early;
    /**
     * 旷工
     */
    private String absent;

    /**
     * 平时(带单位)
     */
    private String overtimeUsual;

    /**
     * 休息(带单位)
     */
    private String overtimeRest;

    /**
     * 节日(带单位)
     */
    private String overtimeHoliday;

    /**
     * 加班合计(带单位)
     */
    private String overtime;

    /**
     * 请假(带单位)
     */
    private String leave;

    /**
     * 出差(带单位)
     */
    private String trip;

    /**
     * 外出(带单位)
     */
    private String out;

    /**
     * 补签次数
     */
    private Integer signCount;
}
