package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 月明细
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:22
 * @since 1.0.0
 */
@From(after = "ATT_RECORD t ")
@GroupBy(after = "t.PERS_PERSON_PIN")
@OrderBy(after = "t.PERS_PERSON_PIN DESC")
@GridConfig(operate = false, idField = "id", winHeight = 400, winWidth = 600)
@Setter
@Getter
@Accessors(chain = true)
public class AttMonthDetailReportItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    @GridColumn(show = false, sortNo = 0)
    private String id;

    // 人员
    /** 人员编号 */
    @Column(name = "t.PERS_PERSON_PIN")
    @GridColumn(label = "att_common_person", secHeader = "att_common_pin", sortNo = 1, width = "90", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String pin;

    /** 姓名 */
    @GridColumn(label = "#cspan", secHeader = "att_person_name", sortNo = 2, width = "90", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String name;

    /** 英文（lastName） */
    @GridColumn(label = "#cspan", secHeader = "att_person_lastName", showExpression = "#language!='zh_CN'", sortNo = 3,    width = "90", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String lastName;

    /** ID */
    @Condition(value = "t.AUTH_DEPT_ID")
    @GridColumn(show = false, sortNo = 4)
    private String deptId;

    /** 部门编号 */
    @GridColumn(label = "pers_dept_deptNo", sortNo = 6, width = "90", show = false)
    private String deptCode;

    /** 部门名称 */
    @GridColumn(label = "pers_dept_deptName", sortNo = 7, width = "130")
    private String deptName;

    /** 月明细符号Map */
    @GridColumn(dynamicColumn = "monthDetailDyna", sortNo = 8)
    private Map<String, Object> monthDetailMap = new HashMap<>();

    @Deprecated
    private String searchMonth;

    @Condition(value = "t.ATT_DATE", equalTag = ">=")
    private Date monthStart;

    @Condition(value = "t.ATT_DATE", equalTag = "<=")
    private Date monthEnd;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    private String likeName;

    @Condition(value = "t.PERS_PERSON_PIN", equalTag = "in")
    private String inPersonPin;

    private String leaveDetails;

    /** 默认构造方法 */
    public AttMonthDetailReportItem() {    super();    this.id = UUID.randomUUID().toString();
    }
}
