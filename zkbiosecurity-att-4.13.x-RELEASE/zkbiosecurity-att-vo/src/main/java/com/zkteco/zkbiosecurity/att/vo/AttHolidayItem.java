package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 节假日
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 9:54
 * @since 1.0.0
 */
@From(after = "ATT_HOLIDAY t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 320, winWidth = 420,
    operates = {
        @GridOperate(type = "edit", permission = "att:holiday:edit", url = "attHoliday.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "att:holiday:del", url = "attHoliday.do?del&names=(holidayName)",
            label = "common_op_del")})
@Setter
@Getter
@Accessors(chain = true)
public class AttHolidayItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 节假日名称
     */
    @Column(name = "t.HOLIDAY_NAME")
    @GridColumn(label = "common_name", width = "120", columnType = "edit", editPermission = "att:holiday:edit",
        editUrl = "attHoliday.do?edit&id=(id)")
    private String holidayName;

    /**
     * 开始日期时间
     */
    @Column(name = "t.START_DATETIME")
    @DateType(type = "date")
    @GridColumn(label = "common_op_startTime", width = "150")
    private Date startDatetime;

    /**
     * 结束日期时间
     */
    @Column(name = "t.END_DATETIME")
    @DateType(type = "date")
    @GridColumn(label = "common_op_endTime", width = "150")
    private Date endDatetime;

    /**
     * 持续天数
     */
    @GridColumn(label = "att_holiday_dayNumber", width = "130")
    @Column(name = "t.DAY_NUMBER")
    private Short dayNumber;

    /**
     * 是否全部人放假-（false：否/0，true：是/1）
     */
    @Column(name = "t.IS_ALL_THE_HOLIDAYS")
    private Boolean isAllTheHolidays;

    /**
     * 备注
     */
    @Column(name = "t.REMARK")
    @GridColumn(label = "common_remark", width = "150")
    private String remark;

    /**
     * 默认构造方法
     */
    public AttHolidayItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AttHolidayItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AttHolidayItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param holidayName
     * @param startDatetime
     * @param endDatetime
     * @param isAllTheHolidays
     * @param remark
     */
    public AttHolidayItem(String id, String holidayName, Date startDatetime, Date endDatetime, Short dayNumber,
        Boolean isAllTheHolidays, String remark) {
        super();
        this.id = id;
        this.holidayName = holidayName;
        this.startDatetime = startDatetime;
        this.dayNumber = dayNumber;
        this.endDatetime = endDatetime;
        this.remark = remark;
    }
}