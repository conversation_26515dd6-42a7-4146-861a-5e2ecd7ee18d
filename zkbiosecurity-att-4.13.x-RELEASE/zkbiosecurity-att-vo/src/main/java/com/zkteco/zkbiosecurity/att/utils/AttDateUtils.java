package com.zkteco.zkbiosecurity.att.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.function.Consumer;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 日期工具
 * <p>
 * 常用的日期相关转换，命名以Att开头，Utils结尾
 * 
 * <AUTHOR>
 * @version 0.0.1
 * @since 2017年1月25日 上午9:40:08
 */
@Slf4j
public abstract class AttDateUtils {

    private static final Logger logger = LoggerFactory.getLogger(AttDateUtils.class);

    public static Date getMaxEndDate() {
        Calendar instance = Calendar.getInstance();
        instance.set(3000, 0, 0, 0, 0, 0);
        return instance.getTime();
    }

    @Deprecated
    public static Date parse(SimpleDateFormat sdf, String source) {
        Date date = null;
        try {
            date = sdf.parse(source);
        } catch (ParseException e) {
            logger.error("String parase Date error", e);
        }
        return date;
    }

    /**
     * 获取某年的第一天日期
     * 
     * @param year
     *            年份
     * @return
     */
    public static Date getFirstDayOfYear(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);

        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取某年的最后一天日期
     * 
     * @param year
     *            年份
     * @return
     */
    public static Date getLastDayOfYear(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);

        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 获取某年某月的第一天日期
     * 
     * @param year
     *            年份
     * @param month
     *            月份
     * @return
     */
    public static Date getFirstDayOfMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        // 设置年份
        calendar.set(Calendar.YEAR, year);
        // 设置月份（月份是从0开始的）
        calendar.set(Calendar.MONTH, month - 1);
        // 获取某月最小天数
        int minDays = calendar.getMinimum(Calendar.DATE);
        // 设置日历中月份的最小天数
        calendar.set(Calendar.DAY_OF_MONTH, minDays);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取某年某月的最后一天日期
     * 
     * @param year
     *            年份
     * @param month
     *            月份
     * @return
     */
    public static Date getLastDayOfMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        // 设置年份
        calendar.set(Calendar.YEAR, year);
        // 设置月份（月份是从0开始的）
        calendar.set(Calendar.MONTH, month - 1);
        // 获取某月最大天数
        int maxDays = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        // 设置日历中月份的最大天数
        calendar.set(Calendar.DAY_OF_MONTH, maxDays);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 获取某年某月的最大天数
     * 
     * @param year
     *            年份
     * @param month
     *            月份
     * @return
     */
    public static int getMaxDaysOfMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        // 设置年份
        calendar.set(Calendar.YEAR, year);
        // 设置月份（月份是从0开始的）
        calendar.set(Calendar.MONTH, month - 1);
        // 获取某月最大天数
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 获取某年某月某天的星期
     * 
     * DAY_OF_WEEK：1/日, 2/一, 3/二, 4/三, 5/四, 6/五, 7/六
     * 
     * @param year
     *            年份
     * @param month
     *            月份
     * @param day
     *            天数
     * @return
     */
    public static int getDayOfWeek(int year, int month, int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        // 设置年份
        calendar.set(Calendar.YEAR, year);
        // 设置月份（月份是从0开始的）
        calendar.set(Calendar.MONTH, month - 1);
        // 设置日历中月份的天数
        calendar.set(Calendar.DAY_OF_MONTH, day);
        return calendar.get(Calendar.DAY_OF_WEEK);
    }

    /**
     * 获取某日期的最小时刻
     * 
     * @param date
     *            日期
     * @return
     */
    public static Date getMinOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static Date getMinOfDay(String dateStr) {
        Date date = DateUtil.stringToDate(dateStr);
        return getMinOfDay(date);
    }

    /**
     * 获取某日期的最大时刻
     * 
     * @param date
     *            日期
     * @return
     */
    public static Date getMaxOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    public static Date getMaxOfDay(String dateStr) {
        Date date = DateUtil.stringToDate(dateStr);
        return getMaxOfDay(date);
    }

    /**
     * 获取上周周一的时间
     * 
     * @param date
     *            日期
     * @return
     */
    public static Date getLastMonDayOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.WEEK_OF_YEAR, -1);
        // 前一周的周一
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        return calendar.getTime();
    }

    /**
     * 获取上周周日的时间
     * 
     * @param date
     *            日期
     * @return
     */
    public static Date getLastSunDayOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.WEEK_OF_YEAR, 0);
        // 前一周的周日
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        return calendar.getTime();
    }

    /**
     * 获取上月第一天的时间
     * 
     * @param date
     *            日期
     * @return
     */
    public static Date getLastMonthOfFirstDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, -1);
        // 设置为1号,当前日期既为本月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    /**
     * 获取上月最后一天的时间
     * 
     * @param date
     *            日期
     * @return
     */
    public static Date getLastMonthOfLastDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, -1);
        // 获取上个月的最后一天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return calendar.getTime();
    }

    /**
     * 获取下个月第一天的时间
     * 
     * @param date
     *            日期
     * @return
     */
    public static Date getNextMonthOfFirstDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 1);
        // 设置为1号,当前日期既为本月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    /**
     * 获取下个月最后一天的时间
     * 
     * @param date
     *            日期
     * @return
     */
    public static Date getNextMonthOfLastDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 1);
        // 获取下个月的最后一天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return calendar.getTime();
    }

    /**
     * 获取上个月当天的时间
     * 
     * @param date
     *            日期
     * @return
     */
    public static Date getLastMonthOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        // 设置为当前时间
        calendar.setTime(date);
        // 设置为上一个月
        calendar.add(Calendar.MONTH, -1);
        return calendar.getTime();
    }

    /**
     * 获取本月第一天的时间
     * 
     * @param date
     *            日期
     * @return
     */
    public static Date getMonthOfFirstDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 0);
        // 设置为1号,当前日期既为本月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    /**
     * 获取本月最后一天的时间
     * 
     * @param date
     *            日期
     * @return
     */
    public static Date getMonthOfLastDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, 0);
        // 获取上个月的最后一天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return calendar.getTime();
    }

    /**
     * 取得日期是某周的第几天
     */
    public static int getDayOfWeek(Long dateLong) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(dateLong);
        int dayOfYear = cal.get(Calendar.DAY_OF_WEEK);
        return dayOfYear;
    }

    /**
     * 取得日期是某月的第几天
     */
    public static int getDayOfMonth(Long dateLong) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(dateLong);
        int dayOfMonth = cal.get(Calendar.DAY_OF_MONTH);
        return dayOfMonth;
    }

    /**
     * 取得日期是某年的第几周
     */
    public static int getWeekOfYear(Long dateLong) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(dateLong);
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1; // 注意：Calendar.MONTH是从0开始的，所以需要加1
        int dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH);
        LocalDate localDate = LocalDate.of(year, month, dayOfMonth);
        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        int weekOfYear = localDate.get(weekFields.weekOfYear());
        return weekOfYear;
    }

    /**
     * 取得2个日期相差几月
     */
    public static int getMonth(Long startDateLong, Long endDateLong) {
        int monthday = 0;
        Calendar starCal = Calendar.getInstance();
        starCal.setTimeInMillis(startDateLong);

        int sYear = starCal.get(Calendar.YEAR);
        int sMonth = starCal.get(Calendar.MONTH);

        Calendar endCal = Calendar.getInstance();
        endCal.setTimeInMillis(endDateLong);
        int eYear = endCal.get(Calendar.YEAR);
        int eMonth = endCal.get(Calendar.MONTH);

        monthday = ((eYear - sYear) * 12 + (eMonth - sMonth));

        return monthday;
    }

    /**
     * 取得前一天
     * 
     * @param today
     *            日期
     * @return
     */
    public static String getBeforeDay(String today) {
        Date date = DateUtil.stringToDate(today, DateUtil.DateStyle.YYYY_MM_DD);
        return DateUtil.dateToString(DateUtil.addDay(date, -1), DateUtil.DateStyle.YYYY_MM_DD);
    }

    /**
     * 取得下一天
     * 
     * @param today
     *            日期 yyyy-MM-dd
     * @return
     */
    public static String getNextDay(String today) {
        Date date = DateUtil.stringToDate(today, DateUtil.DateStyle.YYYY_MM_DD);
        return DateUtil.dateToString(DateUtil.addDay(date, 1), DateUtil.DateStyle.YYYY_MM_DD);
    }

    /**
     * 获取当前日期是星期几<br>
     * 
     * @param dt
     * @return 当前日期是星期几
     */
    public static String getWeekOfDate(Date dt) {
        String[] weekDays = {"0", "1", "2", "3", "4", "5", "6"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(dt);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0) {
            w = 0;
        }
        return weekDays[w];
    }

    /**
     * 判断是否缺卡
     * 
     * @auther lambert.li
     * @date 2019/7/8 16:21
     * @param cardValue
     * @param attRule
     * @return
     */
    @Deprecated
    public static int getLackCardCount(String cardValue, Map<String, String> attRule) {
        int lackCount = 0;
        String noSignIn = attRule.get("att.other.noSignIn");
        String noSignOff = attRule.get("att.other.noSignOff");
        if (StringUtils.isNotBlank(cardValue)) {
            String[] cardTime = cardValue.split(";");
            if (cardTime.length > 0) {
                for (String timeSlot : cardTime) {
                    String startWork = timeSlot.split("-", 2)[0];
                    String endWork = timeSlot.split("-", 2)[1];
                    if (noSignIn.equals(startWork)) {
                        lackCount++;
                    }
                    if (noSignOff.equals(endWork)) {
                        lackCount++;
                    }
                }
            }
        }
        return lackCount;
    }

    /**
     * 将对象数据转换成List（Array -> List）
     * 
     * @auther lambert.li
     * @date 2019/6/20 9:27
     * @param object
     * @param clazz
     * @return
     */
    public static <T> List<T> convertToList(Object object, Class<T> clazz) {
        try {
            String jsonStr = JSON.toJSONString(object);
            List<T> list = JSONArray.parseArray(jsonStr, clazz);
            return list;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取时间秒数,格式如10:26
     *
     * @date 2019/3/20 22:15
     * @param time
     *            格式如10:26
     * @return
     */
    public static int getTimeSeconds(String time) {
        String[] timeArray = time.split(":");
        if (timeArray.length != 2) {
            return 0;
        }
        return Integer.parseInt(timeArray[0]) * 60 + Integer.parseInt(timeArray[1]);
    }

    /**
     * 获取两个时间相差多少分钟
     * 
     * @param startTime
     *            开始时间 yyyy-MM-dd HH:mm
     * @param endTime
     *            结束时间 yyyy-MM-dd HH:mm
     * @return
     */
    public static int getMinuteDiff(String startTime, String endTime) {
        int minutes = 0;
        try {
            Date startDate = DateUtil.stringToDate(startTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM);
            Date endDate = DateUtil.stringToDate(endTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM);
            minutes = getMinuteDiff(startDate, endDate);
        } catch (Exception e) {
        }
        return minutes;
    }

    /**
     * 获取两个时间相差多少分钟
     *
     * @param startDate
     *            开始时间
     * @param endDate
     *            结束时间
     * @return
     */
    public static int getMinuteDiff(Date startDate, Date endDate) {
        int minutes = 0;
        try {
            long diff = endDate.getTime() - startDate.getTime();
            minutes = (int)(diff / (1000 * 60));
        } catch (Exception e) {
        }
        return minutes;
    }

    /**
     * 获取两个时间段的交集时长(分钟)
     * 
     * @param s1
     *            时间段1开始时间 yyyy-MM-dd HH:mm
     * @param e1
     *            时间段1结束时间 yyyy-MM-dd HH:mm
     * @param s2
     *            时间段2开始时间 yyyy-MM-dd HH:mm
     * @param e2
     *            时间段2结束时间 yyyy-MM-dd HH:mm
     * @return
     */
    public static int getIntersectionTime(String s1, String e1, String s2, String e2) {
        if (StringUtils.compare(e1, s2) < 0 || StringUtils.compare(s1, e2) > 0) {
            return 0;
        } else {
            List<String> dateTimeList = new ArrayList<>(Arrays.asList(s1, e1, s2, e2));
            Collections.sort(dateTimeList);
            int diffTime = AttDateUtils.getMinuteDiff(dateTimeList.get(1), dateTimeList.get(2));
            return diffTime;
        }
    }

    /**
     * 字符串日期加减分钟数
     * 
     * @param dateTime
     *            日期 yyyy-MM-dd HH:mm:ss
     * @param minutes
     *            分钟数
     * @return
     */
    public static String addMinute(String dateTime, int minutes) {
        Date date1 = DateUtil.stringToDate(dateTime);
        Date date2 = DateUtil.addMinute(date1, minutes);
        String result = DateUtil.dateToString(date2, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
        return result;
    }

    /**
     * 比较异常时间和班次时间
     * 
     * @auther lambert.li
     * @date 2019/9/2 11:35
     * @param exceptionTime
     *            （异常时间）
     * @param startTime
     *            （班次开始时间）
     * @param endTime
     *            （班次结束时间）
     * @param isStartTime
     *            判断是否是开始时间（开始时间和结束时间判断相反）
     * @param interDay
     *            是否跨天
     * @return
     */
    public static String judgeExceptionTime(String exceptionTime, String startTime, String endTime, boolean isStartTime,
        Boolean interDay) {
        if (!exceptionTime.equals(startTime) && !exceptionTime.equals(endTime)) {
            int exceptionMinute = calcTimeMinute(exceptionTime);
            int shiftStartMinute = calcTimeMinute(startTime);
            int shiftEndMinute = calcTimeMinute(endTime);
            if (interDay) {
                if (shiftStartMinute < exceptionMinute || exceptionMinute < shiftEndMinute) {
                    return exceptionTime;
                }
            } else if (shiftStartMinute < exceptionMinute && exceptionMinute < shiftEndMinute) {
                // 判断异常时间是否在班次时间段内
                return exceptionTime;
            }
        }
        return isStartTime ? startTime : endTime;
    }

    public static int calcTimeMinute(String time) {
        if (time != null && time.contains(":")) {
            String[] timeArr = time.split(":", 2);
            return Integer.parseInt(timeArr[0]) * 60 + Integer.parseInt(timeArr[1]);
        }
        return 0;
    }

    /**
     * 根据当前日期，获取某个月份的第一天
     * 
     * @param curDate
     * @return
     */
    public static Date getFirstDayWithMonthsAgo(Date curDate, int monthAmount) {
        Date newDate = DateUtil.addMonth(curDate, monthAmount);
        Date firstDate = getMonthOfFirstDay(newDate);
        return firstDate;
    }

    /**
     * 根据当前日期，获取某个月份的最后一天
     *
     * @param curDate
     * @return
     */
    public static Date getLastDayWithMonthsAfter(Date curDate, int monthAmount) {
        Date newDate = DateUtil.addMonth(curDate, monthAmount);
        Date lastDate = getMonthOfLastDay(newDate);
        return lastDate;
    }

    /**
     * 计算两个日期的天数差
     *
     * @param startDate
     *            yyyy-MM-dd
     * @param endDate
     *            yyyy-MM-dd
     * @return
     */
    public static int diffDays(String startDate, String endDate) {
        Date start = DateUtil.stringToDate(startDate, DateUtil.DateStyle.YYYY_MM_DD);
        Date end = DateUtil.stringToDate(endDate, DateUtil.DateStyle.YYYY_MM_DD);
        return diffDays(start, end);
    }

    /**
     * 计算两个日期的天数差
     *
     * @param startDate
     *            yyyy-MM-dd
     * @param endDate
     *            yyyy-MM-dd
     * @return
     */
    public static int diffDays(Date startDate, Date endDate) {
        Duration duration = Duration.between(date2LocalDateTime(startDate), date2LocalDateTime(endDate));
        return Long.valueOf(duration.toDays()).intValue();
    }

    /**
     * 日期 Date 转 LocalDateTime
     * 
     * @param date
     * @return
     */
    public static LocalDateTime date2LocalDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zoneId);
        return localDateTime;
    }

    /**
     * 循环两日期范围内的天,执行函数
     *
     * @param startDate
     *            yyyy-MM-dd
     * @param endDate
     *            yyyy-MM-dd
     * @param action
     */
    public static void forEachDay(Date startDate, Date endDate, Consumer<? super Date> action) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateUtil.getDayBeginTime(startDate));
        while (calendar.getTime().compareTo(endDate) <= 0) {
            Date date = calendar.getTime();
            action.accept(date);
            calendar.add(Calendar.DAY_OF_YEAR, 1);
        }
    }

    /**
     * 遍历两个日期内的天数
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/4/26 9:58
     * @param begin
     * @param end
     * @return java.util.List<java.lang.String>
     */
    public static List<String> getBetweenDate(String begin, String end) {
        List<String> betweenList = new ArrayList<String>();
        if (StringUtils.compare(begin, end) > 0) {
            return betweenList;
        }
        if (StringUtils.isNotBlank(begin) && StringUtils.isNotBlank(end)) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

            try {
                begin = DateUtil.stringToString(begin, DateUtil.DateStyle.YYYY_MM_DD, DateUtil.DateStyle.YYYY_MM_DD);
                end = DateUtil.stringToString(end, DateUtil.DateStyle.YYYY_MM_DD, DateUtil.DateStyle.YYYY_MM_DD);

                Calendar startDay = Calendar.getInstance();
                startDay.setTime(format.parse(begin));
                startDay.add(Calendar.DATE, -1);

                while (true) {
                    startDay.add(Calendar.DATE, 1);
                    Date newDate = startDay.getTime();
                    String newend = format.format(newDate);
                    betweenList.add(newend);
                    if (end.equals(newend)) {
                        break;
                    }
                }
            } catch (Exception e) {
                log.error("exception = ", e);
            }
        }

        return betweenList;
    }

    /**
     * 判断两个日期如果相差超过2个月，结束时间取从开始时间往后2个月
     * 
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/21 15:31
     * @param begin
     * @param end
     * @return java.lang.String
     */
    public static String getEndDate(String begin, String end) {
        Date endDate = getEndDate(DateUtil.stringToDate(begin, DateUtil.DateStyle.YYYY_MM_DD),
            DateUtil.stringToDate(end, DateUtil.DateStyle.YYYY_MM_DD));
        return DateUtil.dateToString(endDate, DateUtil.DateStyle.YYYY_MM_DD);
    }

    /**
     * 判断两个日期如果相差超过2个月，结束时间取从开始时间往后2个月
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/21 15:31
     * @param begin
     * @param end
     * @return java.lang.String
     */
    public static Date getEndDate(Date begin, Date end) {
        if (diffDays(begin, end) > 60) {
            return DateUtil.addDay(begin, 60);
        }
        return end;
    }

    /**
     * 把日期转换为长日期字符串格式 年月日时分秒 "yyyy-MM-dd HH:mm:ss"
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/6/12 14:58
     * @param date
     * @return: java.lang.String
     **/
    public static String dateToStrAsLong(Date date) {
        return DateUtil.dateToString(date, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 把日期转换为短日期字符串格式 年月日 "yyyy-MM-dd"
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/6/12 14:58
     * @param date
     * @return: java.lang.String
     **/
    public static String dateToStrAsShort(Date date) {
        return DateUtil.dateToString(date, DateUtil.DateStyle.YYYY_MM_DD);
    }

    /**
     * 把日期转换为短日期字符串格式 年月日 时分 yyyy-MM-dd HH:mm
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/6/12 17:32
     * @param date
     * @return: java.lang.String
     **/
    public static String dateToStrAsMedium(Date date) {
        return DateUtil.dateToString(date, DateUtil.DateStyle.YYYY_MM_DD_HH_MM);
    }

    /**
     * 把字符串转成年月日 "yyyy-MM-dd"
     *
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/6/12 14:58
     * @param dateStr
     * @return: java.lang.String
     **/
    public static Date stringToYmdDate(String dateStr) {
        return DateUtil.stringToDate(dateStr, DateUtil.DateStyle.YYYY_MM_DD);
    }

    public static String stringToHHmm(String dateStr) {
        return DateUtil.stringToString(dateStr, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS, DateUtil.DateStyle.HH_MM);
    }

    public static Date stringToYmdHmsDate(String dateStr) {
        return DateUtil.stringToDate(dateStr, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
    }

    public static String getWeekName(Date date) {
        String week = getWeekOfDate(date);
        switch (week) {
            case "0":
                return I18nUtil.i18nCode("common_sunday");
            case "1":
                return I18nUtil.i18nCode("common_monday");
            case "2":
                return I18nUtil.i18nCode("common_tuesday");
            case "3":
                return I18nUtil.i18nCode("common_wednesday");
            case "4":
                return I18nUtil.i18nCode("common_thursday");
            case "5":
                return I18nUtil.i18nCode("common_friday");
            case "6":
                return I18nUtil.i18nCode("common_saturday");
            default:
                return week;
        }
    }

    public static boolean isSameYear(Date d1, Date d2) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(d1);
        int year1 = cal1.get(Calendar.YEAR);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(d2);
        int year2 = cal2.get(Calendar.YEAR);
        return year1 == year2;
    }

    public static int getYear(Date d) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(d);
        return cal.get(Calendar.YEAR);
    }

    public static Date setDate(int year, int month, int day) {
        Calendar calculateDate = Calendar.getInstance();
        calculateDate.set(Calendar.YEAR, year);
        calculateDate.set(Calendar.MONTH, month - 1);
        calculateDate.set(Calendar.DAY_OF_MONTH, day);
        calculateDate.set(Calendar.HOUR_OF_DAY, 0);
        calculateDate.set(Calendar.MINUTE, 0);
        calculateDate.set(Calendar.MILLISECOND, 0);
        return calculateDate.getTime();
    }
}
