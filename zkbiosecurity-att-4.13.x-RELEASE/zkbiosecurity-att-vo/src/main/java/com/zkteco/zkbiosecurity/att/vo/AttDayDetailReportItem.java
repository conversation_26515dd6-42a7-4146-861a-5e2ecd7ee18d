package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.zkteco.zkbiosecurity.base.annotation.*;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 考勤日报表-日明细表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 17:11
 * @since 1.0.0
 */
@From(after = "ATT_RECORD t ")
@OrderBy(after = "t.PERS_PERSON_PIN ASC, t.ATT_DATE ASC")
@GridConfig
@Setter
@Getter
@Accessors(chain = true)
public class AttDayDetailReportItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Column(name = "t.ID", equalTag = "=")
    @GridColumn(show = false)
    private String id;

    /** 编号 */
    @Column(name = "t.PERS_PERSON_PIN")
    @GridColumn(label = "att_common_person", secHeader = "att_common_pin", sortNo = 1, width = "90",
            encryptMode = "${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String personPin;

    /** 姓名 */
    @Column(name = "t.PERS_PERSON_NAME")
    @GridColumn(label = "#cspan", secHeader = "att_person_name", sortNo = 2, width = "90",
            encryptMode = "${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String personName;

    /** 英文（lastName） */
    @Column(name = "t.PERS_PERSON_LAST_NAME")
    @GridColumn(label = "#cspan", secHeader = "att_person_lastName", showExpression = "#language!='zh_CN'", sortNo = 3,
            width = "90", encryptMode = "${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String personLastName;

    /** ID */
    @Column(name = "t.AUTH_DEPT_ID")
    private String deptId;

    /** 编号 */
    @Column(name = "t.AUTH_DEPT_CODE")
    @GridColumn(label = "att_common_dept", secHeader = "common_number", sortNo = 4, show = false, width = "90")
    private String deptCode;

    /** 部门名称 */
    @Column(name = "t.AUTH_DEPT_NAME")
    @GridColumn(label = "pers_dept_deptName", sort = "na", sortNo = 5, width = "130")
    private String deptName;

    /** 考勤日期 */
    @Column(name = "t.ATT_DATE")
    @DateType(type = "date")
    @GridColumn(label = "att_statistical_attDate", width = "110", sortNo = 6)
    private Date attDate;

    private String attDateStr;

    /** 星期 */
    @Column(name = "t.WEEK")
    @GridColumn(label = "att_statistical_week",
            format = "0=common_abb_sunday,1=common_abb_monday,2=common_abb_tuesday,3=common_abb_wednesday,4=common_abb_thursday,5=common_abb_friday,6=common_abb_saturday",
            width = "90", sortNo = 7)
    private String week;

    /** 编号 */
    @Column(name = "t.SHIFT_NO")
    private String shiftNo;

    /** 名称 */
    @Column(name = "t.SHIFT_NAME")
    private String shiftName;

    /** 时间段集合，多个以逗号隔开 */
    @Column(name = "t.TIMESLOT_NAME")
    @GridColumn(label = "att_shift_timeSlotDetail", secHeader = "common_name", width = "90", sortNo = 9)
    private String timeSlotName;

    /** 上下班时间 */
    @Column(name = "t.SHIFT_TIME_DATA")
    @GridColumn(label = "#cspan", secHeader = "att_statistical_shiftTimeData", width = "90", sortNo = 10)
    private String shiftTimeData;

    /** 打卡数据 */
    @Column(name = "t.CARD_VALID_DATA")
    @GridColumn(label = "att_statistical_cardValidData",  width = "100", sortNo = 11)
    private String cardValidData;

    /** 卡点状态 */
    @Column(name = "t.CARD_STATUS")
    private String cardStatus;

    /** 打卡次数 */
    @Column(name = "t.CARD_VALID_COUNT")
    @GridColumn(label = "att_statistical_cardValidCount", sortNo = 12)
    private Integer cardValidCount;

    // 出勤（单位）
    /** 应该 */
    @Column(name = "t.SHOULD_MINUTE")
    private Integer shouldMinute;
    /** 应出勤分钟数辅助计算字段(处理休息加班、节假日加班，且加班换算为天的时候，应上如果置为0，换算出来的加班时长也为0) */
    @Column(name = "t.SHOULD_MINUTE_Ex")
    private Integer shouldMinuteEx;
    @GridColumn(label = "att_rule_arrive", secHeader = "att_statistical_should", sortNo = 13)
    private String shouldConvert;

    /** 实际 */
    @Column(name = "t.ACTUAL_MINUTE")
    private Integer actualMinute;
    @GridColumn(label = "#cspan", secHeader = "att_statistical_actual", sortNo = 14)
    private String actualConvert;

    /** 有效 */
    @Column(name = "t.VALID_MINUTE")
    private Integer validMinute;
    @GridColumn(label = "#cspan", secHeader = "att_statistical_valid", sortNo = 15)
    private String validConvert;

    // 迟到
    /** 次数数据 */
    @Column(name = "t.LATE_COUNT_DATA")
    private String lateCountData;

    /** 次数 */
    @Column(name = "t.LATE_COUNT_TOTAL")
    @GridColumn(label = "att_common_late", secHeader = "att_statistical_numberOfTimes", sortNo = 17)
    private Integer lateCountTotal;

    /** 次数时长 */
    @Column(name = "t.LATE_MINUTE_DATA")
    @GridColumn(label = "#cspan", secHeader = "att_common_timeLongs", sortNo = 18)
    private String lateMinuteData;

    /** 合计 */
    @Column(name = "t.LATE_MINUTE_TOTAL")
    private Integer lateMinuteTotal;
    @GridColumn(label = "#cspan", secHeader = "att_statistical_total", sortNo = 19)
    private String lateMinuteTotalConvert;

    // 早退
    /** 次数数据 */
    @Column(name = "t.EARLY_COUNT_DATA")
    private String earlyCountData;

    /** 次数 */
    @Column(name = "t.EARLY_COUNT_TOTAL")
    @GridColumn(label = "att_common_early", secHeader = "att_statistical_numberOfTimes", sortNo = 21)
    private Integer earlyCountTotal;

    /** 次数时长 */
    @Column(name = "t.EARLY_MINUTE_DATA")
    @GridColumn(label = "#cspan", secHeader = "att_common_timeLongs", sortNo = 22)
    private String earlyMinuteData;

    /** 合计 */
    @Column(name = "t.EARLY_MINUTE_TOTAL")
    private Integer earlyMinuteTotal;
    @GridColumn(label = "#cspan", secHeader = "att_statistical_total", sortNo = 23)
    private String earlyMinuteTotalConvert;

    // 加班（单位）
    /** 平时 */
    @Column(name = "t.OVERTIME_USUAL_MINUTE")
    private Integer overtimeUsualMinute;
    @GridColumn(label = "att_common_overtime", secHeader = "att_statistical_usually", sortNo = 24)
    private String overtimeUsualConvert;

    /** 休息 */
    @Column(name = "t.OVERTIME_REST_MINUTE")
    private Integer overtimeRestMinute;
    @GridColumn(label = "#cspan", secHeader = "att_statistical_rest", sortNo = 25)
    private String overtimeRestConvert;

    /** 节日 */
    @Column(name = "t.OVERTIME_HOLIDAY_MINUTE")
    private Integer overtimeHolidayMinute;
    @GridColumn(label = "#cspan", secHeader = "att_statistical_holiday", sortNo = 26)
    private String overtimeHolidayConvert;

    /** 合计 */
    @Column(name = "t.OVERTIME_MINUTE")
    private Integer overtimeMinute;
    @GridColumn(label = "#cspan", secHeader = "att_statistical_total", sortNo = 27)
    private String overtimeMinuteConvert;

    @GridColumn(label = "att_param_overTimeLevel", secHeader = "OT1",  sortNo = 28)
    private String overTimeOT1;

    @GridColumn(label = "#cspan", secHeader = "OT2", sortNo = 29)
    private String overTimeOT2;

    @GridColumn(label = "#cspan", secHeader = "OT3", sortNo = 30)
    private String overTimeOT3;

    // 异常
    /** 旷工 */
    @Column(name = "t.ABSENT_MINUTE")
    private Integer absentMinute;

    @GridColumn(label = "att_common_absent", width = "130", sortNo = 50)
    private String absentConvert;

    /** 请假 */
    @Column(name = "t.LEAVE_MINUTE")
    private Integer leaveMinute;

    private String leaveMinuteConvert;

    /** 出差 */
    @Column(name = "t.TRIP_MINUTE")
    private Integer tripMinute;

    /** 外出 */
    @Column(name = "t.OUT_MINUTE")
    private Integer outMinute;

    /** 请假详情 */
    @Column(name = "t.LEAVE_DETAILS")
    private String leaveDetails;

    /** 请假详情 */
    @GridColumn(dynamicColumn = "attLeaveTypeDynamicColumn", sortNo = 2000, label = "att_statistical_leaveDetail")
    private Map<String, Object> attDayDetailReportLeaveMinuteMap = new HashMap<>();

    @Condition(value = "t.ACTUAL_MINUTE", equalTag = ">=")
    private Integer actualMinuteGe;

    @Condition(value = "t.ATT_DATE", equalTag = ">=")
    private Date startDatetimeBegin;

    @Condition(value = "t.ATT_DATE", equalTag = "<=")
    private Date startDatetimeEnd;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "(lower(t.PERS_PERSON_NAME) LIKE ''%{0}%'' OR lower(t.PERS_PERSON_LAST_NAME) LIKE ''%{0}%'')")
    private String likeName;

    public AttDayDetailReportItem() {
        super();
    }
}