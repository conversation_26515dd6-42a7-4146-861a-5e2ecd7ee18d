package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@GridConfig(operate = false, idField = "id")
@Setter
@Getter
@Accessors(chain = true)
public class AttSelectDoorItem extends BaseItem {

    /**
     * 主键
     */
    @GridColumn(columnType = "ra", width = "40", sortNo = 0)
    private String id;

    @GridColumn(label = "acc_door_name", sortNo = 1, width = "120")
    private String doorName;

    @GridColumn(label = "acc_door_number", sortNo = 2, width = "60")
    private Short doorNo;

    @GridColumn(label = "common_ownedDev", sortNo = 3, width = "120")
    private String deviceAlias;

    @GridColumn(label = "common_dev_sn", sortNo = 4, width = "*")
    private String deviceSn;

    private String deviceId;

    private String type;

    private String selectId;

    private String areaId;
}
