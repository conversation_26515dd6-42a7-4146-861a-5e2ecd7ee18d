package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 设备
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 17:10
 * @since 1.0.0
 */
@From(after = "ATT_DEVICE t")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", minWidth = "220", winHeight = 300, winWidth = 500, operates = {
    @GridOperate(type = "edit", permission = "att:device:edit", url = "attDevice.do?edit", label = "common_op_edit"),
    @GridOperate(type = "del", permission = "att:device:del", url = "attDevice.do?del&names=(devName)",
        label = "common_op_del"),
    @GridOperate(type = "custom", permission = "att:adms:devCmd", click = "attDeviceViewCommand",
        label = "common_devMonitor_viewTheCommand")})
@Setter
@Getter
@Accessors(chain = true)
public class AttDeviceItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /**
     * 设备序列号
     */
    @Column(name = "t.DEV_SN")
    @GridColumn(label = "common_dev_sn", width = "130", columnType = "edit", editPermission = "att:device:edit",
        editUrl = "attDevice.do?edit")
    private String devSn;

    /**
     * 设备名称
     */
    @Column(name = "t.DEV_NAME")
    @GridColumn(label = "common_dev_name", width = "120")
    private String devName;

    /**
     * 设备型号
     */
    @Column(name = "t.DEV_MODEL")
    @GridColumn(label = "common_dev_deviceModel", width = "120")
    private String devModel;

    /**
     * 固件版本号
     */
    @Column(name = "t.FW_VERSION")
    @GridColumn(label = "att_device_fwVersion", width = "130")
    private String fwVersion;
    /**
     * ip地址
     */
    @Column(name = "t.IP_ADDRESS")
    @GridColumn(label = "common_ipAddress", width = "130")
    private String ipAddress;

    /**
     * 区域名称
     */
    @GridColumn(label = "pers_person_attArea", width = "130", sort = "na")
    private String authAreaName;

    /**
     * 区域编号
     */
    private String authAreaCode;

    /**
     * 启用状态
     */
    @Column(name = "t.STATUS")
    // @GridColumn(label = "att_device_status", columnType = "custom", convert = "convertToIcon",width = "100")
    private Boolean status;
    /**
     * 状态( 1：在线 ,0：离线，2: 禁用)
     */
    @GridColumn(label = "common_status", columnType = "custom", convert = "convertToString", sort = "na", width = "70")
    private String onlineStatus;

    /**
     * 是否登记机
     */
    @Column(name = "t.IS_REG_DEVICE")
    @GridColumn(label = "att_device_register", columnType = "custom", convert = "convertToIcon", width = "110")
    private Boolean isRegDevice;

    /**
     * 待执行命令数
     */
    @GridColumn(label = "att_device_waitCmdCount", sort = "na", width = "160")
    private Integer waitCmdCount;

    /**
     * 人员数
     */
    @Column(name = "t.PERSON_COUNT")
    @GridColumn(label = "att_deviceOption_UserCount", width = "120")
    private Integer personCount;

    /**
     * 指纹数
     */
    @Column(name = "t.FP_COUNT")
    @GridColumn(label = "pers_person_templateCount", width = "120")
    private Integer fpCount;

    /**
     * 人脸数
     */
    @Column(name = "t.FACE_COUNT")
    @GridColumn(label = "pers_person_faceTemplateCount", width = "100")
    private Integer faceCount;

    /**
     * 指纹版本
     */
    @Column(name = "t.FP_VERSION")
    private String fpVersion;

    /**
     * 面部版本
     */
    @Column(name = "t.FACE_VERSION")
    private String faceVersion;

    /**
     * 通讯端口
     */
    @Column(name = "t.COMM_PORT")
    private Integer commPort;

    /**
     * 通信方式
     */
    @Column(name = "t.PROTOCOL")
    private String protocol;

    /**
     * 通信密码
     */
    @Column(name = "t.PUSH_COMMKEY")
    private String pushcommkey;

    /**
     * 数据更新标志
     */
    @Column(name = "t.UPDATE_FLAG")
    private String updateFlag;

    /**
     * 时区
     */
    @Column(name = "t.TIME_ZONE")
    private String timeZone;

    /**
     * 刷新间隔时间(分)
     */
    @Column(name = "t.TRANS_INTERVAL")
    private Short transInterval;

    /**
     * 定时传送时间(如：00:00;14:05)
     */
    @Column(name = "t.TRANS_TIMES")
    private String transTimes;

    /**
     * 实时上传数据
     */
    @Column(name = "t.REAL_TIME")
    private Boolean realTime;

    /**
     * 和服务器通讯的最大命令个数
     */
    @Column(name = "t.CMD_COUNT")
    private Short cmdCount;

    /**
     * 查询记录时间（秒）
     */
    @Column(name = "t.SEARCH_INTERVAL")
    private Short searchInterval;

    /**
     * 数据下发标志
     */
    @Column(name = "t.DATA_DOWN_FLAG")
    private String dataDownFlag;

    /**
     * 记录数
     */
    @Column(name = "t.RECORD_COUNT")
    private Integer recordCount;

    /**
     * 区域Id
     */
    @Column(name = "t.AUTH_AREA_ID")
    private String areaId;

    @Condition(value = "t.AUTH_AREA_ID", equalTag = "in")
    private String inAreaId;

    /**
     * 面部9.0时间戳
     */
    private String bioDataStamp;

    /**
     * 是否加密
     */
    private String encrypt;

    /**
     * 支持功能，info value10
     */
    private String attSupportFunList;

    /** 是否包含下级 */
    private String isIncludeLower;

    /** 设备区域状态 */
    private String deviceAreaStatus;

    /**
     * 默认构造方法
     */
    public AttDeviceItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AttDeviceItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AttDeviceItem(String id) {
        super(true);
        this.id = id;
    }

    public AttDeviceItem(String id, String devSn) {
        super(true);
        this.id = id;
        this.devSn = devSn;
    }
}