package com.zkteco.zkbiosecurity.att.vo;


import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 自定义表报字段集合
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 10:25 2023/7/18
 * @version v1.0
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class AttCustomReportFieldItem extends BaseItem {

    private String id;

    /** 归属自定义报表ID */
    private String customReportId;

    /** 字段名称 */
    private String fieldName;

    /** 字段显示文本 */
    private String fieldLabel;

    /** 排序 */
    private Integer sortNo;
}
