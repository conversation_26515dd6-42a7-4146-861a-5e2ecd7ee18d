package com.zkteco.zkbiosecurity.att.constants;

import java.util.*;

/**
 * 
 * 考勤设备常量类
 * <p>
 * 定义相关设备常量，命名以Att开头，Constant结尾
 * 
 * <AUTHOR>
 * @version 1.0.0.0
 * @since 2017年11月7日 上午11:37:03
 */
public abstract class AttDeviceConstant {

    /** 设备自动上传标记(登记机模式) */
    public static final String ATT_UPDATE_FLAG = "111110101101";
    /** 设备不自动上传标记（非登记机模式） */
    public static final String ATT_NON_REGISTERED = "111000000000";
    /** 人员下发时，要查找的设备参数列表 */
    public static final String DEVOPTIONS =
        "FingerFunOn,FaceFunOn,AttSupportFunList,VisilightFun,PhotoFunOn,PvFunOn,PvVersion,FvFunOn,BioDataFun,BioPhotoFun,SubcontractingUpgradeFunOn,MultiBioDataSupport,MultiBioPhotoSupport,UserPicURLFunOn,MultiBioVersion";

    // 查看设备参数需要展示的参数信息
    public static final List<String> ATT_DEVICE_OPTION_SHOWLIST;
    static {
        List<String> list = new ArrayList();
        list.add("UserCount");
        // ATT_DEVICE_OPTION_SHOWLIST.add("~MaxUserCount");
        list.add("FaceCount");
        // ATT_DEVICE_OPTION_SHOWLIST.add("~MaxFaceCount");
        list.add("FPCount");
        // ATT_DEVICE_OPTION_SHOWLIST.add("~MaxFingerCount");
        list.add("FvCount");
        // ATT_DEVICE_OPTION_SHOWLIST.add("~MaxFvCount");
        list.add("PvCount");
        // ATT_DEVICE_OPTION_SHOWLIST.add("~MaxPvCount");
        list.add("TransactionCount");
        // ATT_DEVICE_OPTION_SHOWLIST.add("~MaxAttLogCount");
        // ATT_DEVICE_OPTION_SHOWLIST.add("UserPhotoCount");
        list.add("~MaxUserPhotoCount");
        list.add("FaceVersion");
        list.add("FPVersion");
        list.add("FvVersion");
        list.add("PvVersion");
        list.add("FWVersion");
        list.add("PushVersion");
        ATT_DEVICE_OPTION_SHOWLIST = Collections.unmodifiableList(list);
    }
    // 设备参数名称国际化
    public static final Map<String, String> ATT_DEVICE_OPTION_I18N;
    static {
        Map<String, String> map = new HashMap<>();
        map.put("UserCount", "pers_common_personCount");
        // ATT_DEVICE_OPTION_I18N.put("~MaxUserCount", "att_deviceOption_MaxUserCount");
        map.put("FaceCount", "pers_person_faceTemplateCount");
        // ATT_DEVICE_OPTION_I18N.put("~MaxFaceCount", "att_deviceOption_MaxFaceCount");
        map.put("FPCount", "pers_person_templateCount");
        // ATT_DEVICE_OPTION_I18N.put("~MaxFingerCount", "att_deviceOption_MaxFingerCount");
        map.put("FvCount", "pers_person_VeinTemplateCount");
        // ATT_DEVICE_OPTION_I18N.put("~MaxFvCount", "att_deviceOption_MaxFvCount");
        map.put("PvCount", "pers_person_palmTemplateCount");
        // ATT_DEVICE_OPTION_I18N.put("~MaxPvCount", "att_deviceOption_MaxPvCount");
        map.put("TransactionCount", "att_statistical_cardValidData");
        // ATT_DEVICE_OPTION_I18N.put("~MaxAttLogCount", "att_deviceOption_MaxAttLogCount");
        // ATT_DEVICE_OPTION_I18N.put("UserPhotoCount","att_deviceOption_UserPhotoCount");
        map.put("~MaxUserPhotoCount", "att_deviceOption_MaxUserPhotoCount");
        map.put("FaceVersion", "pers_person_regFace");
        map.put("FPVersion", "pers_person_regFinger");
        map.put("FvVersion", "pers_person_regVein");
        map.put("PvVersion", "pers_person_metacarpalVein");
        map.put("FWVersion", "att_deviceOption_FWVersion");
        map.put("PushVersion", "att_deviceOption_PushVersion");
        ATT_DEVICE_OPTION_I18N = Collections.unmodifiableMap(map);
    }

    // 新协议设备参数名称 --add by hook.fang 2020-03-05
    public static final List<String> NEW_DEVICE_OPTION_NAME;
    static {
        List<String> list = new ArrayList();
        list.add("UserCount");
        list.add("~MaxUserCount");

        list.add("FaceCount_2");
        // NEW_DEVICE_OPTION_NAME.add("~MaxFaceCount");
        // NEW_DEVICE_OPTION_NAME.add("FacePhotoCount");
        // NEW_DEVICE_OPTION_NAME.add("~MaxFacePhotoCount");

        list.add("VLFaceCount_9");
        // NEW_DEVICE_OPTION_NAME.add("VLMaxFaceCount");
        list.add("VLFacePhotoCount");
        // NEW_DEVICE_OPTION_NAME.add("VLMaxFacePhotoCount");

        list.add("FPCount_1");
        // NEW_DEVICE_OPTION_NAME.add("~MaxFingerCount");
        // NEW_DEVICE_OPTION_NAME.add("FPPhotoCount");
        // NEW_DEVICE_OPTION_NAME.add("~MaxFPPhotoCount");

        list.add("FvCount_7");
        // NEW_DEVICE_OPTION_NAME.add("~MaxFvCount");
        // NEW_DEVICE_OPTION_NAME.add("FvPhotoCount");
        // NEW_DEVICE_OPTION_NAME.add("~MaxFvPhotoCount");

        list.add("PvCount_8");
        // NEW_DEVICE_OPTION_NAME.add("~MaxPvCount");
        // NEW_DEVICE_OPTION_NAME.add("PvPhotoCount");
        // NEW_DEVICE_OPTION_NAME.add("~MaxPvPhotoCount");

        list.add("TransactionCount");
        list.add("~MaxAttLogCount");

        list.add("~MaxUserPhotoCount");
        list.add("FaceVersion_2");
        list.add("VLFaceVersion_9");
        list.add("FPVersion_1");
        list.add("FvVersion_7");
        list.add("PvVersion_8");
        list.add("FWVersion");
        list.add("PushVersion");
        list.add("IRISCount_4");
        list.add("VLPvCount_10");
        list.add("IRISVersion_4");
        list.add("VLPvVersion_10");
        NEW_DEVICE_OPTION_NAME = Collections.unmodifiableList(list);
    }
    // 新协议生物模板、图像及版本 --add by hook.fang 2020-03-05
    public static final Map<String, String> NEW_DEVICE_OPTION_I18N;
    static {
        Map<String, String> map = new HashMap<>();
        map.put("UserCount", "pers_common_personCount");
        // NEW_DEVICE_OPTION_I18N.put("UserCount", "att_deviceOption_UserCount");
        // NEW_DEVICE_OPTION_I18N.put("~MaxUserCount", "att_deviceOption_MaxUserCount");

        map.put("FaceCount_2", "pers_person_faceTemplateCount");// 近红外人脸模板数
        // NEW_DEVICE_OPTION_I18N.put("FaceCount_2", "att_deviceOption_FaceCount");// 近红外人脸模板数
        // NEW_DEVICE_OPTION_I18N.put("~MaxFaceCount", "att_deviceOption_MaxFaceCount");// 最大近红外人脸模板数
        // NEW_DEVICE_OPTION_I18N.put("FacePhotoCount", "att_deviceOption_FacePhotoCount");// 近红外人脸图像数
        // NEW_DEVICE_OPTION_I18N.put("~MaxFacePhotoCount", "att_deviceOption_MaxFacePhotoCount");// 最大近红外人脸图像数

        map.put("VLFaceCount_9", "pers_person_visibleFace");// 可见光人脸模板数
        map.put("VLFacePhotoCount", "pers_person_cropFaceCount");// 可见光人脸图像数
        // NEW_DEVICE_OPTION_I18N.put("VLFaceCount_9", "att_deviceOption_FaceCount");// 可见光人脸模板数
        // NEW_DEVICE_OPTION_I18N.put("VLMaxFaceCount", "att_deviceOption_MaxFaceCount");// 最大可见光人脸模板数
        // NEW_DEVICE_OPTION_I18N.put("VLFacePhotoCount", "att_deviceOption_FacePhotoCount");// 可见光人脸图像数
        // NEW_DEVICE_OPTION_I18N.put("VLMaxFacePhotoCount", "att_deviceOption_MaxFacePhotoCount");// 最大可见光人脸图像数

        map.put("FPCount_1", "pers_person_templateCount");// 指纹模板数
        // NEW_DEVICE_OPTION_I18N.put("FPCount_1", "att_deviceOption_FingerCount");// 指纹模板数
        // NEW_DEVICE_OPTION_I18N.put("~MaxFingerCount", "att_deviceOption_MaxFingerCount");// 最大指纹模板数
        // NEW_DEVICE_OPTION_I18N.put("FPPhotoCount", "att_deviceOption_FingerPhotoCount");// 指纹图像数
        // NEW_DEVICE_OPTION_I18N.put("~MaxFPPhotoCount", "att_deviceOption_MaxFingerPhotoCount");// 最大指纹图像数

        map.put("FvCount_7", "pers_person_VeinTemplateCount");// 指静脉模板数
        // NEW_DEVICE_OPTION_I18N.put("FvCount_7", "att_deviceOption_FvCount");// 指静脉模板数
        // NEW_DEVICE_OPTION_I18N.put("~MaxFvCount", "att_deviceOption_MaxFvCount");// 最大指静脉模板数
        // NEW_DEVICE_OPTION_I18N.put("FvPhotoCount", "att_deviceOption_FvPhotoCount");// 指静脉图像数
        // NEW_DEVICE_OPTION_I18N.put("~MaxFvPhotoCount", "att_deviceOption_MaxFvPhotoCount");// 最大指静脉图像数

        map.put("PvCount_8", "pers_person_palmTemplateCount");// 手掌模板数
        // NEW_DEVICE_OPTION_I18N.put("PvCount_6", "att_deviceOption_PvCount");// 手掌模板数
        // NEW_DEVICE_OPTION_I18N.put("~MaxPvCount", "att_deviceOption_MaxPvCount");// 最大手掌模板数
        // NEW_DEVICE_OPTION_I18N.put("PvPhotoCount", "att_deviceOption_PvPhotoCount");// 手掌图像数
        // NEW_DEVICE_OPTION_I18N.put("~MaxPvPhotoCount", "att_deviceOption_MaxPvPhotoCount");// 最大手掌图像数

        map.put("TransactionCount", "att_statistical_cardValidData");
        // NEW_DEVICE_OPTION_I18N.put("TransactionCount", "att_deviceOption_TransactionCount");
        // NEW_DEVICE_OPTION_I18N.put("~MaxAttLogCount", "att_deviceOption_MaxAttLogCount");

        map.put("~MaxUserPhotoCount", "att_deviceOption_MaxUserPhotoCount");
        map.put("FaceVersion_2", "pers_person_regFace");// 近红外人脸模板版本
        map.put("VLFaceVersion_9", "pers_person_visibleFace");// 可见光人脸模板版本
        map.put("FPVersion_1", "pers_person_regFinger");
        map.put("FvVersion_7", "pers_person_regVein");
        map.put("PvVersion_8", "pers_person_metacarpalVein");
        map.put("FWVersion", "att_deviceOption_FWVersion");
        map.put("PushVersion", "att_deviceOption_PushVersion");
        map.put("IRISCount_4", "pers_person_iris");// 虹膜
        map.put("VLPvCount_10", "pers_person_visiblePalm");// 可见光手掌
        map.put("IRISVersion_4", "pers_person_iris"); // 虹膜
        map.put("VLPvVersion_10", "pers_person_visiblePalm");// 可见光手掌
        NEW_DEVICE_OPTION_I18N = Collections.unmodifiableMap(map);
    }
}
