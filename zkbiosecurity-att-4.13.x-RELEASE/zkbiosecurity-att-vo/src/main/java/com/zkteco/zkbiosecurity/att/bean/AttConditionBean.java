package com.zkteco.zkbiosecurity.att.bean;

import lombok.Getter;
import lombok.Setter;

/**
 * 组装姓名和部门条件过滤对象
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 13:44 2020/9/10
 */
@Getter
@Setter
public class AttConditionBean {

    private String isIncludeLower;

    private String likeName;

    private String deptId;

    private String deptCode;

    private String deptName;

    private String inPersonPin;

    private String inDeptId;

    public AttConditionBean() {}

    public AttConditionBean(String isIncludeLower, String likeName, String deptId, String deptCode, String deptName) {
        this.isIncludeLower = isIncludeLower;
        this.likeName = likeName;
        this.deptId = deptId;
        this.deptCode = deptCode;
        this.deptName = deptName;
    }
}
