/*
 * File Name: AppAttWorkTimeItem <NAME_EMAIL> on 2018/9/27 17:32. Copyright:Copyright © 1999-2018
 * ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.att.api.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class AttApiWorkTimeItem implements Serializable {

    private static final long serialVersionUID = 1L;
    /* 应该上班时间 */
    private String shouldStartWorkTime;
    /* 实际上班时间 */
    private String startWorkTime;
    /* 上班打卡状态(0:正常、1:异常、2:补签) */
    private String startWorkStatus;
    /* 上班打卡状态名称(0:正常、1:异常、2:补签) */
    private String workTimeStatusName;
    /* 应该下班时间 */
    private String shouldEndWorkTime;
    /* 实际下班时间 */
    private String endWorkTime;
    /* 下班打卡状态(0:正常、1:异常、2:补签) */
    private String endWorkStatus;
    /* 下班打卡状态名称(0:正常、1:异常、2:补签) */
    private String offTimeStatusName;
}
