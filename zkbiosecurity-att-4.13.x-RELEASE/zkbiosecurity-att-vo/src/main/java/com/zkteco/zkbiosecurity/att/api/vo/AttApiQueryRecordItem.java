package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date: 2019/6/19 17:28
 */
@Getter
@Setter
@Accessors(chain = true)
public class AttApiQueryRecordItem implements Serializable {

    /**
     * 开始时间
     */
    private Date attDateStart;

    /**
     * 结束时间
     */
    private Date attDateEnd;

    /**
     * 人员编号支持in查询
     */
    private String inPin;
}