package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 时间段
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 9:37
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors
public class AttCloudTimeSlotItem implements Serializable {
    private static final long serialVersionUID = -1467989017368956935L;

    /**
     * 时间段类型（0：正常时间段，1：弹性时间段）
     */
    private Short periodType;

    /**
     * 上班时间(弹性时间段模式：开始签到时间)
     */
    private String toWorkTime;

    /**
     * 必须签到（false：否/0，true：是/1）
     */
    private Boolean isMustSignIn;

    /**
     * 下班时间(弹性时间段模式：开始签退时间)
     */
    private String offWorkTime;

    /**
     * 必须签退（false：否/0，true：是/1）
     */
    private Boolean isMustSignOff;

    /**
     * 允许迟到分钟数
     */
    private Short allowLateMinutes;

    /**
     * 允许早退分钟数
     */
    private Short allowEarlyMinutes;

    /**
     * 是否段间扣除（false：否/0，true：是/1）
     */
    private Boolean isSegmentDeduction;

    /**
     * 开始段间时间
     */
    private String startSegmentTime;

    /**
     * 结束段间时间
     */
    private String endSegmentTime;

    /**
     * 考勤方式（0/按班次正常刷卡，1/一天内任刷一次有效卡，2/只计算刷卡时间，3/免刷卡）
     */
    private Short attendanceMode;

    /**
     * 是否启动弹性上班(true:启用。false：不启用)
     */
    private String enableFlexibleWork;

    /**
     * 可提前上班分钟数
     */
    private Integer advanceWorkMinutes;

    /**
     * 可延后上班分钟数
     */
    private Integer delayedWorkMinutes;
}

