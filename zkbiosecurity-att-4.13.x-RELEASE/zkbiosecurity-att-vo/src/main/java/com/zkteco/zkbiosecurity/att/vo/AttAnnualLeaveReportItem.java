package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 年假结余表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/9/29 17:51
 * @since 1.0.0
 */
@From(after = "ATT_PERSON t LEFT JOIN PERS_PERSON p ON t.PERS_PERSON_PIN = p.PIN LEFT JOIN AUTH_DEPARTMENT d ON p.AUTH_DEPT_ID = d.ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@Setter
@Getter
@Accessors(chain = true)
@GridConfig
public class AttAnnualLeaveReportItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    // 人员
    /** 编号 */
    @Column(name = "t.PERS_PERSON_PIN")
    @GridColumn(label = "att_common_pin", sortNo = 1, width = "110", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String persPin;

    /** 姓名 */
    @Column(name = "p.NAME")
    @GridColumn(label = "att_person_name", sortNo = 2, width = "110", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String persName;

    /** 英文（lastName） */
    @Column(name = "p.LAST_NAME")
    @GridColumn(label = "att_person_lastName", showExpression = "#language!='zh_CN'", sortNo = 3, width = "110", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String personLastName;

    // 部门
    /** ID */
    @Column(name = "p.AUTH_DEPT_ID")
    private String deptId;

    /** 编号 */
    @Column(name = "d.CODE")
    @GridColumn(label = "att_common_dept", sortNo = 4, show = false, width = "110")
    private String deptCode;

    /** 部门名称 */
    @Column(name = "d.NAME")
    @GridColumn(label = "pers_dept_deptName", sort = "na", sortNo = 5, width = "110")
    private String deptName;

    /** 入职日期 */
    @Column(name = "p.HIRE_DATE")
    @DateType(type = "date")
    @GridColumn(label = "pers_cardTemplate_entryDate", sortNo = 6, width = "110")
    private Date hireDate;

    /** 工龄 */
    @Column(name = "t.WORK_LONG")
    @GridColumn(label = "att_annualLeave_years", sortNo = 7, width = "110")
    private String workLong;

    /** 年假总数 */
    @Column(name = "t.ANNUAL_LEAVE_DAYS")
    @GridColumn(label = "att_annualLeave_calculateDay", sortNo = 9, width = "110")
    private Integer annualLeaveDays;

    /**
     * 年假调整天数
     */
    @Column(name = "t.ANNUAL_ADJUST_DAYS")
    @GridColumn(label = "att_annualLeave_adjustDay", sortNo = 10, width = "110")
    private Integer annualAdjustDays;

    /** 剩余年假 */
    @GridColumn(label = "att_annualLeave_remainingDays", sortNo = 11, width = "110", columnType = "custom", convert = "attAnnualLeaveViewDetail")
    private Float annualRemaining;

    /** 有效期 */
    @Column(name = "t.ANNUAL_VALID_DATE")
    @DateType(type = "date")
    private Date annualValidDate;

    /** 有效期 */
    @GridColumn(label = "att_annualLeave_validDate", sortNo = 12, minWidth = "200")
    private String annualValidDateStr;

    @Column(name = "p.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "(lower(p.NAME) LIKE ''%{0}%'' OR lower(p.LAST_NAME) LIKE ''%{0}%'')")
    private String likeName;

    public AttAnnualLeaveReportItem() {}

    public AttAnnualLeaveReportItem(String id) {
        super(true);
        this.id = id;
    }
}