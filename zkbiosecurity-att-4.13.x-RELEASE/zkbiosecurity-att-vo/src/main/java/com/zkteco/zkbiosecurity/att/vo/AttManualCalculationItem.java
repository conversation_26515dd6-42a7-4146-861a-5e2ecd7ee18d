package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 考勤计算
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:16
 * @since 1.0.0
 */
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600, operates = {})
@Setter
@Getter
@Accessors(chain = true)
public class AttManualCalculationItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 人员编号
     */
    @GridColumn(label = "att_person_pin", width = "150", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String pin;

    /**
     * 名字
     */
    @GridColumn(label = "att_person_name", width = "150", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String name;

    /**
     * 姓氏
     */
    @GridColumn(label = "att_person_lastName", width = "150", showExpression = "#language!='zh_CN'", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String lastName;

    /**
     * 部门编码
     */
    @GridColumn(label = "pers_dept_deptNo", width = "150", sort = "na")
    private String deptCode;

    /**
     * 部门名称
     */
    @GridColumn(label = "pers_dept_deptName", width = "150", sort = "na")
    private String deptName;

    /**
     * 部门ID
     */
    private String deptId;

    private String likeName;

    // 是否包含下级
    private String isIncludeLower;

    /**
     * 默认构造方法
     */
    public AttManualCalculationItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AttManualCalculationItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AttManualCalculationItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param persPin
     * @param persName
     * @param persLastName
     */
    public AttManualCalculationItem(String id, String persPin, String persName, String persLastName) {
        super();
        this.id = id;
        this.pin = persPin;
        this.name = persName;
        this.lastName = persLastName;
    }
}
