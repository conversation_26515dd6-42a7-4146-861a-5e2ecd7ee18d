package com.zkteco.zkbiosecurity.att.constants;

/**
 * 考勤计算常量类
 * <p>
 * 定义相关计算常量，命名以Att开头，Constant结尾
 * 
 * <AUTHOR>
 * @version 1.0.0.0
 * @since 2017年11月7日 上午11:34:00
 */
public abstract class AttCalculationConstant {
    /** key连接符 */
    public static final String KEY_CONNECTOR = "=";
    /** 每次计算人数 */
    public static final int NUMBER = 800;
    /** 计算进度100 */
    public static final int FULL_PROGRESS = 100;
    /** 一天的时间戳 */
    public static final int A_DAY_TIME_STAMP = 86400000;
    /** 考勤记录表名 */
    public static final String TABLE = "att_record";

    public static final class AttBaseDataMapKey {
        /** 人员数据 */
        public static final String ATT_PERSON_DATA = "attPersonData";
        /** 规则数据 */
        public static final String ATT_RULE_DATA = "attRuleData";
        /** 节假日数据 */
        public static final String ATT_HOLIDAY_DATA = "attHolidayData";
        /** 时间段数据 */
        public static final String ATT_TIMESLOT_DATA = "attTimeSlotData";
        /** 班次数据 */
        public static final String ATT_SHIFT_DATA = "attShiftData";
        /** 班次排班数据 */
        public static final String ATT_SHIFTSCH_DATA = "attShiftSchData";
        /** 分组排班数据 */
        public static final String ATT_GROUPSCH_DATA = "attGroupSchData";
        /** 部门排班数据 */
        public static final String ATT_DEPTSCH_DATA = "attDeptSchData";
        /** 人员临时排班数据 */
        public static final String ATT_PERSONTEMPSCH_DATA = "attPersonTempSchData";
    }

    public static final class AttExceptionDataMapKey {
        /** 补签数据 */
        public static final String ATT_SIGN_DATA = "attSignData";
        /** 请假数据 */
        public static final String ATT_LEAVE_DATA = "attLeaveData";
        /** 出差数据 */
        public static final String ATT_TRIP_DATA = "attTripData";
        /** 外出数据 */
        public static final String ATT_OUT_DATA = "attOutData";
        /** 加班数据 */
        public static final String ATT_OVERTIME_DATA = "attOvertimeData";
        /** 调整数据 */
        public static final String ATT_ADJUST_DATA = "attAdjustData";
        /** 调班-个人同日期数据 */
        public static final String ATT_CLASSSAMEDATE_DATA = "attClassSameDateData";
        /** 调班-个人不同日期数据 */
        public static final String ATT_CLASSDIFFDATE_DATA = "attClassDiffDateData";
        /** 调班-两人对调数据 */
        public static final String ATT_CLASSPERSON_DATA = "attClassPersonData";
    }

    /** 考勤状态 */
    public static final class AttAttendStatus {
        /** 应到/实到 */
        public static final String ACTUAL = "actual";
        /** 早退 */
        public static final String EARLY = "early";
        /** 迟到 */
        public static final String LATE = "late";
        /** 未签退 */
        public static final String NO_CHECK_OUT = "noCheckOut";
        /** 未签到 */
        public static final String NO_CHECK_IN = "noCheckIn";
        /** 请假 */
        public static final String LEAVE = "leave";
        /** 出差 */
        public static final String TRIP = "trip";
        /** 外出 */
        public static final String OUT = "out";
        /** 加班 */
        public static final String OVERTIME = "overtime";
        /** 旷工 */
        public static final String ABSENT = "absent";
        /** 排班且休息 */
        public static final String SCHEDULING_AND_REST = "schAndRest";
        /** 未排班 */
        public static final String NO_SCHEDULING = "noSch";
        /** 调休 */
        public static final String TUNE_OFF = "tuneOff";
        /** 补班 */
        @Deprecated
        public static final String SUPPLEMENT_CLASS = "supplementClass";
        /** 调班 */
        public static final String CLASS = "class";
        /** 调班-个人同日期 */
        public static final String SAME_DATE_CLASS = "sameDateClass";
        /** 调班-个人不同日期 */
        public static final String DIFF_DATE_CLASS = "diffDateClass";
        /** 调班-两人对调 */
        public static final String PERSON_SWAP_CLASS = "personSwapClass";
        /** 节假日 */
        public static final String HOLIDAY = "holiday";
        /** 未签到不完整 */
        public static final String NO_CHECK_IN_INCOMPLETE = "noCheckInInc";
        /** 未签退不完整 */
        public static final String NO_CHECK_OUT_INCOMPLETE = "noCheckOutInc";
    }

    /**
     * 异常排班类型
     *
     * @deprecated Use {@link AttAttendStatus} instead.
     */
    @Deprecated
    public static final class AttExceptionSchType {
        /** 应到/实到 */
        public static final String ARRIVE = "100";
        /** 早退 */
        public static final String EARLY = "90";
        /** 迟到 */
        public static final String LATE = "80";
        /** 未签退 */
        public static final String NO_SIGN_OFF = "70";
        /** 未签到 */
        public static final String NO_SIGN_IN = "60";
        /** 请假 */
        public static final String LEAVE = "50";
        /** 出差 */
        public static final String TRIP = "40";
        /** 外出 */
        public static final String OUT = "30";
        /** 加班 */
        public static final String OVERTIME = "20";
        /** 旷工 */
        public static final String ABSENT = "10";
        /** 排班且休息 */
        public static final String SCHEDULING_AND_REST = "0";
        /** 未排班 */
        public static final String NO_SCHEDULING = "-10";
        /** 调休 */
        public static final String OFF = "-20";
        /** 补班 */
        public static final String CLASSES = "-30";
        /** 调班-个人同日期 */
        public static final String SAME_DATE_CLASS = "-40";
        /** 调班-个人不同日期 */
        public static final String DIFFERENCE_DATE_CLASS = "-50";
        /** 调班-两人对调 */
        public static final String PEOPLE_SWAP_CLASS = "-60";
        /** 节假日 */
        public static final String HOLIDAY = "-70";
        /** 未签到不完整 */
        public static final String NO_CHECK_IN_INCOMPLETE = "110";
        /** 未签退不完整 */
        public static final String NO_CHECK_OUT_INCOMPLETE = "120";
    }

    /**
     * 是否跨天
     */
    public static final class IsInterDay {
        /** 当天 */
        public static final String THE_SAME_DAY = "0";
        /** 跨天 */
        public static final String THE_INTER_DAY = "1";
    }

}
