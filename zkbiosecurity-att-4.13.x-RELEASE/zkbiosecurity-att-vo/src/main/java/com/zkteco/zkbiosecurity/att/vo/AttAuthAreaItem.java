package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 区域添加人员
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 18:25
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain = true)
@GridConfig(operate = true, operates = {@GridOperate(type = "custom", permission = "att:areaPerson:addPerson",
    click = "attAreaAddPerson", label = "common_op_addPerson"),})
public class AttAuthAreaItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    @GridColumn(label = "base_area_code", width = "120")
    private String code;

    @GridColumn(label = "base_area_name", width = "120")
    private String name;

    private String parentId;

    @GridColumn(label = "base_area_parentAreaCode", width = "120")
    private String parentAreaCode;

    @GridColumn(label = "base_area_parentAreaName", width = "*")
    private String parentAreaName;

    @GridColumn(label = "base_area_remark", width = "*")
    private String remark;

    @GridColumn
    private String personId;

    private String authAreaIdIn;

    // 判断是左列表noSelect、还是右列表select
    private String type;

    // 当前选中的ids
    private String selectId;
}