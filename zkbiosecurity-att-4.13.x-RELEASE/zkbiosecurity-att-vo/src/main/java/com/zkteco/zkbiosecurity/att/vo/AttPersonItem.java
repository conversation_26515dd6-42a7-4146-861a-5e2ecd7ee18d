package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 考勤人员
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 17:03
 * @since 1.0.0
 */
@From(after = "ATT_PERSON t LEFT JOIN PERS_PERSON p ON t.PERS_PERSON_PIN = p.PIN LEFT JOIN AUTH_DEPARTMENT d ON p.AUTH_DEPT_ID = d.ID")
@Setter
@Getter
@Accessors(chain = true)
@GridConfig(operate = false)
public class AttPersonItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /** 是否考勤 */
    @Column(name = "t.IS_ATTENDANCE")
    private Boolean isAttendance;

    /** 人员设备权限 */
    @Column(name = "t.PER_DEV_AUTH")
    private Short perDevAuth;

    /** 人员id */
    @Column(name = "t.PERS_PERSON_ID", equalTag = "=")
    private String personId;

    /** 分组ID */
    @Column(name = "t.GROUP_ID")
    private String groupId;

    /** 分组名称 */
    private String groupName;

    /** 人员编号 */
    @Column(name = "t.PERS_PERSON_PIN")
    private String personPin;

    /** 人员姓名 */
    @Column(name = "p.NAME")
    private String personName;

    /** 人员 */
    @Column(name = "p.LAST_NAME")
    private String personLastName;

    @Column(name = "p.AUTH_DEPT_ID")
    private String deptId;

    @Column(name = "d.CODE")
    private String deptCode;

    @Column(name = "d.NAME")
    private String deptName;

    /** 入职时间 */
    @Column(name = "p.HIRE_DATE")
    private Date hireDate;

    /** 年假总数 */
    @Column(name = "t.ANNUAL_LEAVE_DAYS")
    private Integer annualLeaveDays;

    @Column(name = "t.ANNUAL_ADJUST_DAYS")
    private Integer annualAdjustDays;

    /** 年假有效开始时间 */
    @Column(name = "t.ANNUAL_VALID_DATE")
    private Date annualValidDate;

    /** 工龄 */
    @Column(name = "t.WORK_LONG")
    private String  workLong;

    @Condition(value = "p.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "p.AUTH_DEPT_ID", equalTag = "not in")
    private String notInDeptId;

    @Condition(value = "t.GROUP_ID", equalTag = "not in")
    private String notInGroupId;

    @Condition(value = "t.ID", equalTag = "not in")
    private String notInId;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    @Condition(value = "t.PERS_PERSON_ID", equalTag = "not in")
    private String notPersonInId;

    @Condition(value = "t.PERS_PERSON_ID", equalTag = "in")
    private String personIdIn;

    @Column(name = "t.PERS_PERSON_PIN", equalTag = "in")
    private String personPinIn;

    @Condition(value = "(lower(p.NAME) LIKE ''%{0}%'' OR lower(p.LAST_NAME) LIKE ''%{0}%'')")
    private String likeName;

    /** 人员设备权限 */
    @Condition(value = "t.PER_DEV_AUTH", equalTag = "in")
    private String perDevAuthIn;

    /** 验证方式 */
    @Column(name = "t.VERIFY_MODE")
    private Short verifyMode;

    /** 离职时间 */
    private Date leaveDate;

    /** 区域ID */
    private String areaId;

    /* 导入年假判断 */
    private Float annualLeaveDaysFloat;

    public AttPersonItem() {
        super();
    }

    public AttPersonItem(Boolean equals) {
        super(equals);
    }

    public AttPersonItem(String id) {
        super(true);
        this.id = id;
    }

    public AttPersonItem(String id, Boolean isAttendance, Short perDevAuth) {
        super();
        this.id = id;
        this.isAttendance = isAttendance;
        this.perDevAuth = perDevAuth;
    }
}