package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 事件记录-VO
 * 
 * @Auther: lambert.li
 * @Date: 2018/11/7 19:24
 */
@Getter
@Setter
@Accessors(chain = true)
public class AttApiTransactionItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /* 事件记录ID */
    private String id;

    /* 事件时间 */
    private String eventTime;

    /* 人员编号 */
    private String pin;

    /* 人员姓名 */
    private String name;

    /* 海外（人员姓氏） */
    private String lastName;

    /* 部门名称 */
    private String deptName;

    /* 区域名称 */
    private String areaName;

    /* 卡号 */
    private String cardNo;

    /* 设备SN */
    private String devSn;

    /* 验证方式 */
    private String verifyModeName;

    /* 事件名称 */
    private String eventName;

    /* 事件点 */
    private String eventPointName;

    /* 读头名称 */
    private String readerName;

    /* 区域 */
    private String accZone;

    /* 设备名称 */
    private String devName;

    /* 日志ID */
    private Integer logId;

    /* 签到地点 */
    private String attPlace;

    /* 数据来源类型 */
    private String mark;
}
