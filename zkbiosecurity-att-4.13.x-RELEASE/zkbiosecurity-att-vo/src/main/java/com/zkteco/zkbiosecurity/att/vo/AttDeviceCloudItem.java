package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@From(after = "ATT_DEVICE t")
@OrderBy(after = "t.CREATE_TIME DESC")
@Setter
@Getter
@Accessors(chain = true)
public class AttDeviceCloudItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /**
     * 设备序列号
     */
    @Column(name = "t.DEV_SN")
    @GridColumn(label = "common_dev_sn", width = "110", columnType = "edit", editPermission = "attCloud:device:edit",
        editUrl = "attCloudDevice.do?edit")
    private String devSn;

    /**
     * 设备名称
     */
    @Column(name = "t.DEV_NAME")
    @GridColumn(label = "common_dev_name", width = "100")
    private String devName;

    /**
     * 设备型号
     */
    @Column(name = "t.DEV_MODEL")
    @GridColumn(label = "common_dev_deviceModel")
    private String devModel;

    /**
     * ip地址
     */
    @Column(name = "t.IP_ADDRESS")
    @GridColumn(label = "common_ipAddress", width = "100")
    private String ipAddress;

    /**
     * 区域名称
     */
    @GridColumn(label = "pers_person_attArea", width = "100", sort = "na")
    private String authAreaName;

    /**
     * 启用状态
     */
    @Column(name = "t.STATUS")
    @GridColumn(label = "att_device_status", columnType = "custom", convert = "convertToIcon")
    private Boolean status;
    /**
     * 在线状态
     */
    @GridColumn(label = "att_device_isOnLine", columnType = "custom", convert = "convertToString", sort = "na")
    private String onlineStatus;

    /**
     * 区域Id
     */
    @Column(name = "t.AUTH_AREA_ID")
    @Condition(value = "t.AUTH_AREA_ID", equalTag = "notNull")
    private String areaId;

    @Condition(value = "t.AUTH_AREA_ID", equalTag = "in")
    private String inAreaId;

    /** appId */
    @Column(name = "t.APP_ID", equalTag = "=")
    private String appId;

    private String authAreaCode;

    private String parentAreaCode;

    @Condition(value = "t.UPDATE_TIME", equalTag = ">")
    private Date lastUpdateDate;
}
