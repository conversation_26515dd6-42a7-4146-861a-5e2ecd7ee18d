package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 休息时间段
 *
 * <AUTHOR>
 * @date 2019/5/30
 */
@Getter
@Setter
@Accessors(chain = true)
@From(after = "ATT_BREAK_TIME t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, winWidth = 420, winHeight = 300,
    operates = {
        @GridOperate(type = "edit", permission = "att:breakTime:edit", url = "attBreakTime.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "att:breakTime:del", url = "attBreakTime.do?del&names=(name)",
            label = "common_op_del")})
public class AttBreakTimeItem extends BaseItem {
    private static final long serialVersionUID = -4572805156115390807L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    @Column(name = "t.name")
    @GridColumn(label = "common_name", width = "120", columnType = "edit")
    private String name;

    @Column(name = "t.START_TIME")
    @GridColumn(label = "att_breakTime_startTime", width = "120")
    private String startTime;

    @Column(name = "t.END_TIME")
    @GridColumn(label = "att_breakTime_endTime", width = "120")
    private String endTime;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    @Condition(value = "t.ID", equalTag = "not in")
    private String notInId;

    @Condition(value = "({0})")
    private String timeBetween;

    public AttBreakTimeItem() {

    }

    public AttBreakTimeItem(String id) {
        this.id = id;
    }
}
