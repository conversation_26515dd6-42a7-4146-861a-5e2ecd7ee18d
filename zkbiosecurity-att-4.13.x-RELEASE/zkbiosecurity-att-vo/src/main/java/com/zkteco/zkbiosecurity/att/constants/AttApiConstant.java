package com.zkteco.zkbiosecurity.att.constants;

/**
 * @Auther: lambert.li
 * @Date: 2018/11/18 01:40
 */
public class AttApiConstant {

    // 成功
    public static final int OP_SUCCESS = 0;
    // 程序错误
    public static final int API_PROGRAM_ERROR = -1;
    // 区域不存在
    public static final int BASE_AREA_NO_EXIST = -80;
    // pins数据不允许为空
    public static final int PINS_DATA_NOT_NULL = -81;
    // pins数据不允许超过500个值
    public static final int PINS_DATA_OVER_SIZE = -82;
    // 区域编号不能为空
    public static final int BASE_AREA_CODE_NOTNULL = -83;
    // pageNo或者pageSize不能设置小于等于0
    public static final int API_WRONG_PAGE = -90;
    // pageSize 大于1000
    public static final int API_PAGE_OVERSIZE = -91;
    // 非法时间
    public static final int API_DATE_ERROR = -100;
    // 开始时间不能大于结束时间
    public static final int API_DATE_STARTTIME_LARGE = -101;

    /* 考勤结果类型 attResultType */
    /** 正常 */
    public static final Integer ATT_RESULT_TYPE_ARRIVE = 0;
    /** 迟到 */
    public static final Integer ATT_RESULT_TYPE_LATE = 1;
    /** 早退 */
    public static final Integer ATT_RESULT_TYPE_EARLY = 2;
    /** 旷工 */
    public static final Integer ATT_RESULT_TYPE_ABSENT = 3;
    /** 请假 */
    public static final Integer ATT_RESULT_TYPE_LEAVE = 4;
    /** 加班 */
    public static final Integer ATT_RESULT_TYPE_OVERTIME = 5;
    /** 出差 */
    public static final Integer ATT_RESULT_TYPE_TRIP = 6;
    /** 外出 */
    public static final Integer ATT_RESULT_TYPE_OUT = 7;
    /** 未排班 */
    public static final Integer ATT_RESULT_TYPE_NO_SCHEDULING = 8;
    /** 休息 */
    public static final Integer ATT_RESULT_TYPE_REST = 9;
    /** 考勤状态 补班 */
    public static final Integer ATT_RESULT_TAKE_CLASS = 10;
    /** 考勤状态 调班 */
    public static final Integer ATT_RESULT_ADJUST_CLASS = 11;
    /** 考勤状态 漏卡 */
    public static final Integer ATT_RESULT_LACK_CARD = 12;

    /*正常/漏卡/迟到/早退/休假/出差/外出*/
    /** 应到/实到 */
    public static final String ARRIVE = "arrive";
    /** 漏卡 */
    public static final String LEAKAGE = "leakage";
    /** 早退 */
    public static final String EARLY = "early";
    /** 迟到 */
    public static final String LATE = "late";
    /** 请假 */
    public static final String LEAVE = "leave";
    /** 出差 */
    public static final String TRIP = "trip";
    /** 外出 */
    public static final String OUT = "out";
    /** 加班 */
    public static final String OVERTIME = "overtime";
    /** 补签 **/
    public static final String SIGN = "sign";

    // "0：审批通过，1：未审批，2：已驳回, 3: 已撤销"
    /** 0：审批通过 **/
    public static final String AUDIT_STATUS_APPROVAL = "0";
    /** 1：未审批 **/
    public static final String AUDIT_STATUS_UNAPPROVAL = "1";
    /** 2：已驳回 **/
    public static final String AUDIT_STATUS_REJECT = "2";
    /** 3: 已撤销 **/
    public static final String AUDIT_STATUS_REVOKE = "3";
    /** 4: 异常结束 **/
    public static final String AUDIT_STATUS_ABNORMAL_END = "4";
}
