package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Getter
@Setter
@Accessors(chain = true)
public class AttTeamLeavePersonItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    private String personName;

    /**
     * 人员编号
     */
    private String personPin;

    /**
     * 头像地址
     */
    private String avatarUrl;

    /**
     * 假种名称
     */
    private String leaveTypeName;

    /**
     * 请假时长
     */
    private String leaveLong;

    /**
     * 开始时间
     */
    private String startDatetime;

    /**
     * 结束时间
     */
    private String endDatetime;

    /**
     * 申请时间
     */
    private String applyTime;

    /**
     * 流程任务Id
     */
    private String taskId;

    /**
     * 申请id 对应att_leave表主键id
     */
    private String applyId;

}
