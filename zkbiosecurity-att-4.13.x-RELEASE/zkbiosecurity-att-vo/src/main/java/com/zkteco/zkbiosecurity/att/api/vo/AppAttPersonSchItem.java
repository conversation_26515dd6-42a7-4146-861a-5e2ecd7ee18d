/*
 * File Name: AppAttPersonSchItem <NAME_EMAIL> on 2018/9/27 16:58. Copyright:Copyright © 1999-2018
 * ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.att.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel
public class AppAttPersonSchItem implements Serializable {

    /** 日期 2018-09-01 */
    @ApiModelProperty(value = "日期(2018-09-01)")
    private String date;
    /** 班次ID */
    @ApiModelProperty(value = "班次ID")
    private String shiftId;
    /** 班次名称 */
    @ApiModelProperty(value = "班次名称")
    private String shiftName;
    /** 时间段 */
    @ApiModelProperty(value = "时间段")
    private List<AppAttWorkTimeItem> workTimes;
    /** 排班类型：0-正常排班、1-临时排班 */
    @ApiModelProperty(value = "排班类型：0-正常排班、1-临时排班")
    private String type;
    /** 考勤状态 0-正常、1-异常、2-请假、3-休息、4-漏卡 */
    @ApiModelProperty(value = "考勤状态 0-正常、1-异常、2-请假、3-休息、4-漏卡、5-加班、6-外出、7-出差、8-未排班")
    private Integer attState;
    /** 上下班打卡时间(根班次时间段对应) */
    private List<AppAttValidCardItem> appAttValidCardItems;
    /** 考勤异常集合 */
    private List<AppAttExceptionItem> attExceptionList;
    /** 应勤时间 */
    private String shouldHour;
    /** 实勤时间 */
    private String actualHour;
    /** 旷工时间 */
    private String absentHour;
    /** 缺卡次数 */
    private String lackCardCount;
}
