package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
public class AttApiTeamSignPersonDetailItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 补签日期 (yyyy年MM月dd日)
     */
    private String signDate;

    /**
     * 补签日 (星期N)
     */
    private String signDay;

    /**
     * 班次名称
     */
    private String shiftName;

    /**
     * 原本打卡时间
     */
    private String beforeSignTime;

    /**
     * 补签后时间
     */
    private String signTime;

    /**
     * 备注
     */
    private String remaker;

    /**
     * 申请时间
     */
    private String applyTime;

    /**
     * 是否是管理员添加
     */
    private boolean adminAdd;
}
