package com.zkteco.zkbiosecurity.att.vo;

import java.util.UUID;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 用来作为查询的SQL组装，不做界面显示使用
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/7/1 11:49
 * @return
 */
@From(after = "ATT_TRANSACTION t ")
@GroupBy(after = "t.PERS_PERSON_PIN, t.ATT_DATE")
@OrderBy(after = "t.ATT_DATE ASC")
@Setter
@Getter
@Accessors(chain = true)
public class AttDayCardDetailReportItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    private String id;

    @Column(name = "t.PERS_PERSON_PIN")
    private String personPin;

    @Column(name = "t.ATT_DATE")
    private String attDate;

    @Column(name = "(COUNT(t.ATT_DATE))")
    private Integer cardCount;

    @Column(name = "MIN(t.ATT_TIME)")
    private String earliestTime;

    @Column(name = "MAX(t.ATT_TIME)")
    private String latestTime;

    @Condition(value = "t.PERS_PERSON_PIN", equalTag = "in")
    private String inPersonPin;

    @Condition(value = "t.ATT_DATE", equalTag = ">=")
    private String attDateBegin;

    public AttDayCardDetailReportItem() {
        this.id = UUID.randomUUID().toString();
    }

    public AttDayCardDetailReportItem(String attDate, String inPersonPin) {
        this.attDate = attDate;
        this.inPersonPin = inPersonPin;
    }
}