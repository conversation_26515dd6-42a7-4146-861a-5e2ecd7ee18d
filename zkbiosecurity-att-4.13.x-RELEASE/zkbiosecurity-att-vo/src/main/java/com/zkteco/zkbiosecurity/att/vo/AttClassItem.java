package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 调班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:06
 * @since 1.0.0
 */
@From(after = "ATT_CLASS t LEFT JOIN ATT_SHIFT ast ON t.SWAPSHIFT_ID=ast.ID LEFT JOIN AUTH_DEPARTMENT ad ON t.AUTH_ADJUSTDEPT_ID = ad.ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 440, winWidth = 600, operates = {@GridOperate(type = "del",
    permission = "att:class:del", url = "attClass.do?del&pins=(adjustPersonPin)", label = "common_op_del")})
@Setter
@Getter
@Accessors(chain = true)
public class AttClassItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 调整人员id
     */
    @Column(name = "t.PERS_ADJUSTPERSON_ID")
    private String adjustPersonId;

    /**
     * 调整人员编号
     */
    @Column(name = "t.PERS_ADJUSTPERSON_PIN")
    @GridColumn(label = "att_person_pin", width = "100", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String adjustPersonPin;

    /**
     * 调整人员姓名
     */
    @GridColumn(label = "att_person_name", sort = "na", width = "80", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String adjustPersonName;

    /**
     * 调整人员英文（lastName）
     */
    @GridColumn(label = "att_person_lastName", showExpression = "#language!='zh_CN'", width = "80", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String adjustPersonLastName;

    /**
     * 调整人员部门id
     */
    @Column(name = "t.AUTH_ADJUSTDEPT_ID")
    private String adjustDeptId;

    /**
     * 调整人员部门编号
     */
    @Column(name = "ad.CODE")
    @GridColumn(label = "att_common_deptNo", sort = "na", width = "130")
    private String adjustDeptCode;

    /**
     * 调整人员部门名称
     */
    @Column(name = "ad.NAME")
    @GridColumn(label = "att_common_deptName", width = "130", sort = "na")
    private String adjustDeptName;

    /**
     * 调整类型
     */
    @Column(name = "t.ADJUST_TYPE")
    @GridColumn(label = "att_class_type", width = "100",
        format = "0=att_class_sameTimeMoveShift,1=att_class_differenceTimeMoveShift,2=att_class_twoPeopleMove")
    private Short adjustType;

    /**
     * 调整日期
     */
    @Column(name = "t.ADJUST_DATE")
    @DateType(type = "date")
    @GridColumn(label = "att_adjust_adjustDate", width = "100")
    private Date adjustDate;

    /**
     * 调整班次名称
     **/
    @Column(name = "ast.SHIFT_NAME")
    @GridColumn(label = "att_class_shiftName", width = "100")
    private String swapShiftName;

    /**
     * 对调人员id
     */
    @Column(name = "t.PERS_SWAPPERSON_ID")
    private String swapPersonId;

    /**
     * 对调人员编号
     */
    @Column(name = "t.PERS_SWAPPERSON_PIN")
    @GridColumn(label = "att_class_movePersonPin", width = "100", encryptMode="${pers.pin.encryptMode}", permission = "att:pin:encryptProp")
    private String swapPersonPin;

    /**
     * 对调人员姓名
     */
    @GridColumn(label = "att_class_movePersonName", width = "150", encryptMode="${pers.name.encryptMode}", permission = "att:name:encryptProp")
    private String swapPersonName;

    /**
     * 对调人员英文（lastName）
     */

    @GridColumn(label = "att_class_movePersonLastName", showExpression = "#language!='zh_CN'", width = "170", encryptMode="${pers.lastName.encryptMode}", permission = "att:name:encryptProp")
    private String swapPersonLastName;

    /**
     * 对调人员部门id
     */
    @Column(name = "t.AUTH_SWAPDEPT_ID")
    private String swapDeptId;


    /**
     * 对调人员部门编号
     */
    @GridColumn(label = "att_common_deptNo", width = "130")
    private String swapDeptCode;

    /**
     * 对调人员部门名称
     */
    @GridColumn(label = "att_common_deptName", width = "130")
    private String swapDeptName;

    /**
     * 对调日期
     */
    @Column(name = "t.SWAP_DATE")
    @DateType(type = "date")
    @GridColumn(label = "att_class_moveDate", width = "100")
    private Date swapDate;

    /**
     * 备注
     */
    @Column(name = "t.REMARK")
    @GridColumn(label = "common_remark")
    private String remark;

    /**
     * 操作日期时间
     */
    @Column(name = "t.CREATE_TIME")
    @GridColumn(label = "att_common_operateTime", width = "100")
    private Date operateDatetime;

    /**
     * 对调班次id
     */
    @Column(name = "t.SWAPSHIFT_ID")
    private String swapShiftId;


    @GridColumn(label = "common_status", width = "120", format = "0=att_approve_wait,1=att_exception_stop,2=att_apply_pass,3=att_apply_revoke,4=att_exception_delete,5=att_exception_refuse,6=att_exception_end")
    @Column(name = "t.FLOW_STATUS")
    private String flowStatus;

    /**
     * 流程业务ID,全局唯一
     */
    @Column(name = "t.BUSINESS_KEY")
    private String businessKey;

    /**
     * 对调班次编号
     */
    private String shiftNo;

    private String applyNo;
    private String applyType;

    private String flowNo;

    @Condition(value = "t.CREATE_TIME", equalTag = ">=")
    private Date startTime;

    @Condition(value = "t.CREATE_TIME", equalTag = "<=")
    private Date endTime;

    @Condition(value = "t.AUTH_ADJUSTDEPT_ID", equalTag = "in")
    private String inAdjustDeptId;

    @Condition(value = "t.PERS_ADJUSTPERSON_ID", equalTag = "in")
    private String inAdjustPersonId;

    @Condition(value = "t.PERS_ADJUSTPERSON_PIN", equalTag = "in")
    private String inAdjustPersonPin;

    @Condition(value = "t.ADJUST_DATE", equalTag = ">=")
    private Date adjustDateGE;

    @Condition(value = "t.ADJUST_DATE", equalTag = "<=")
    private Date adjustDateLE;

    @Condition(value = "t.PERS_SWAPPERSON_PIN", equalTag = "in")
    private String inSwapPersonPin;

    @Condition(value = "t.SWAP_DATE", equalTag = ">=")
    private Date swapDateGE;

    @Condition(value = "t.SWAP_DATE", equalTag = "<=")
    private Date swapDateLE;

    @Condition(
            value = "t.AUTH_ADJUSTDEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    private String likeName;

    /**
     * 行数，导入用
     */
    private Integer rowNum;

    /** 是否包含下级 */
    private String isIncludeLower;

    /** 前端部门树点击搜索 */
    private String deptId;



    /**
     * 默认构造方法
     */
    public AttClassItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AttClassItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AttClassItem(String id) {
        super(true);
        this.id = id;
    }

}