package com.zkteco.zkbiosecurity.att.api.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Getter
@Setter
@Accessors(chain = true)
public class AttCloudTeamOverTimeByTypePersonItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    private String personName;

    /**
     * 人员编号
     */
    private String personPin;

    /**
     * 头像地址
     */
    private String avatarUrl;

    /**
     * 加班类型名称
     */
    private String overTimeTypeName;

    /**
     * 加班时长
     */
    private String overTimeLong;

    /**
     * 加班次数
     */
    private Integer overTimeCount;

    /**
     * 流程任务Id
     */
    private String taskId;

}
