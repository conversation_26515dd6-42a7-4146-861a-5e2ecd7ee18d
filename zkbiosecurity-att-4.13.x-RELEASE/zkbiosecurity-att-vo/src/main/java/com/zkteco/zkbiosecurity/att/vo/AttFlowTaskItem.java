package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;
import java.util.Map;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.annotation.GridOperate;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

/**
 * 考勤流程查询
 * 
 * <AUTHOR>
 * @date 2019/04/11
 */
@GridConfig(operate = true, idField = "id", winHeight = 500, winWidth = 500,
    operates = {
        @GridOperate(type = "custom", url = "attWfApply.do?approve&taskId=(taskId)", click = "handleProcess",
            showConvertor = "showHandleButton", label = "wf_flow_handleProcess"),
        @GridOperate(type = "custom", url = "attWfApply.do?detail&businessKey=(businessKey)", click = "detailView",
            label = "wf_flow_detail_view")})
@Getter
@Setter
public class AttFlowTaskItem extends BaseItem {

    @GridColumn(label = "taskId", width = "0", show = true)
    private String taskId;

    @GridColumn(label = "businessKey", width = "0", show = true)
    private String businessKey;

    /**
     * 主键
     */
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    @GridColumn(label = "processInstanceId", width = "100", show = false)
    private String processInstanceId;
    @GridColumn(label = "processIndex", width = "100", show = false)
    private Integer processIndex; // 流程所在环节
    /**
     * 人员ID
     */
    private String personId;
    /**
     * 人员ID
     */
    private String loginUserId;
    /**
     * 人员编号
     */
    @GridColumn(label = "att_person_pin", width = "100", sort = "na")
    private String personPin;
    /**
     * 姓名
     */
    @GridColumn(label = "att_person_name", width = "100", sort = "na")
    private String personName;
    /**
     * 部门编号
     */
    @GridColumn(label = "att_common_deptNo", width = "100", show = false)
    private String deptCode;

    /**
     * 部门名称
     */
    @GridColumn(label = "att_common_deptName", width = "100", sort = "na")
    private String deptName;

    /**
     * 假种名称
     */
    @GridColumn(label = "wf_flow_type", width = "100", sort = "na",
        format = "sign=att_leftMenu_sign,leave=att_leftMenu_leave,trip=att_leftMenu_trip,out=att_leftMenu_out,overtime=att_leftMenu_overtime,adjust=att_leftMenu_adjust,classShift=att_leftMenu_class")
    private String flowType;

    @GridColumn(label = "wf_flow_operateUser", width = "120", sort = "na")
    private String operateUser;
    /**
     * 操作日期时间
     */
    @GridColumn(label = "att_common_operateTime", width = "130", show = false)
    private Date operateDatetime;

    /**
     * 假种名称
     */
    @GridColumn(label = "wf_flow_processStageInfo", width = "400", sort = "na", show = false)
    private String processStageInfo;

    /**
     * 流程状态，是否走完流程，用于统计业务，如请假是否扣除工时，这时候就得用到看流程状态。 0表示流程正常, -1表示流程软删除，1表示流程暂停，2流程完成
     */
    @GridColumn(label = "wf_flow_Status", width = "100", sort = "na",
        format = "1=wf_flow_processing,2=wf_flow_finished")
    private Integer taskStatus;

    private Map<String, Object> bussinessData;
    private Map<String, String> notifierPers;

    private boolean handleButton = true;

    private String companyId;

    public boolean showHandleButton() {
        return handleButton;
    }
}
