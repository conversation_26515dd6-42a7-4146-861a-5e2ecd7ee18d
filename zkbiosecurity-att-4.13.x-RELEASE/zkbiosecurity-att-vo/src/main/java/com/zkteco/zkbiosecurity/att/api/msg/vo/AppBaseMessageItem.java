/**
 * @author: GenerationTools
 * @date: 2018-03-01 下午04:18 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.att.api.msg.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.Condition;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @author: GenerationTools
 * @date: 2018-03-01 下午04:18
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AppBaseMessageItem extends BaseItem {

    /**
     * 主键
     */
    private String id;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 业务编码
     */
    private String businessCode;

    /**
     * 别名
     */
    private String title;

    /**
     * 消息类型
     */
    private String type;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 接收人
     */
    private String receiverId;

    /**
     * 链接名称
     */
    private String hrefTitle;

    /**
     * 路径
     */
    private String href;

    /**
     * 消息提醒方式，弹窗,App,web,email,短信等等
     */
    // private String remindType;

    private String remindTime;

    /**
     * 消息过期时间
     */
    private String expireTime;

    /**
     * 消息状态 -1删除 0 创建 1已发送，2阅读
     */
    private Short status;

    /* 抠图路径 */
    private String photoPath;
}