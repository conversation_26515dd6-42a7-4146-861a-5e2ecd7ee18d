/*
 * File Name: AppAttWorkTimeItem <NAME_EMAIL> on 2018/9/27 17:32. Copyright:Copyright © 1999-2018
 * ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.att.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel
public class AppAttWorkTimeItem implements Serializable {

    /** 实际上班时间 */
    @ApiModelProperty(value = "实际上班时间(L：漏卡，W：未打卡)")
    private String startWorkTime;
    /** 上班打卡状态 0 正常 1 异常 */
    @ApiModelProperty(value = "上班打卡状态")
    private String startWorkStatus;
    /** 实际下班时间 */
    @ApiModelProperty(value = "实际下班时间")
    private String endWorkTime;
    /** 下班打卡状态 0 正常 1 异常 */
    @ApiModelProperty(value = "下班打卡状态")
    private String endWorkStatus;
    /** 班次时长 */
    private String timeLong;
}
