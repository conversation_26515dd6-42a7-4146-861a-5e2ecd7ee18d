package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

@Getter
@Setter
@Accessors(chain = true)
@From(after = "ATT_OVERTIME t ")
@OrderBy(after = "t.PERS_PERSON_PIN,t.OVERTIME_SIGN")
@GroupBy(after = "t.PERS_PERSON_PIN,t.OVERTIME_SIGN")
public class AttTeamOverTimeByTypePersonItem extends BaseItem {

    private String id;

    /**
     * 人员编号
     */
    @Column(name = "t.PERS_PERSON_PIN")
    private String personPin;

    /**
     * 加班标记（平时，休息日，节假日）
     */
    @Column(name = "t.OVERTIME_SIGN")
    private Short overtimeSign;

    /**
     * 加班时长
     */
    @Column(name = "sum(t.OVERTIME_LONG)")
    private Integer overTimeLong;

    /**
     * 加班次数
     */
    @Column(name = "count(t.PERS_PERSON_PIN)")
    private Integer overTimeCount;

    /**
     * 人员集合
     */
    @Condition(value = "t.PERS_PERSON_PIN", equalTag = "in")
    private String inPersonPin;

    /**
     * 开始时间
     */
    @Condition(value = "t.END_DATETIME", equalTag = ">=")
    private Date startDate;

    /**
     * 结束时间
     */
    @Condition(value = "t.START_DATETIME", equalTag = "<=")
    private Date endDate;

    /**
     * 流程状态
     */
    @Condition(value = "t.FLOW_STATUS", equalTag = "=")
    private String flowStatus;

}
