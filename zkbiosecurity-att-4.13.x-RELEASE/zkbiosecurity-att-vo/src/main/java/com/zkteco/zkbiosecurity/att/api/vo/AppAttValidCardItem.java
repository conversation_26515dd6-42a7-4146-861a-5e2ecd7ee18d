package com.zkteco.zkbiosecurity.att.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Auther: lambert.li
 * @Date: 2018/11/14 17:16
 */
@Getter
@Setter
@Accessors(chain = true)
public class AppAttValidCardItem extends AppAttWorkTimeItem implements Serializable {

    /* 应该上班时间 */
    @ApiModelProperty(value = "应该上班时间")
    private String shouldStartWorkTime;

    /* 应该下班时间 */
    @ApiModelProperty(value = "应该下班时间")
    private String shouldEndWorkTime;
}
