package com.zkteco.zkbiosecurity.att.api.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 申请时长查询
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 16:48 2021/11/15
 * @version v1.0
 */
@Setter
@Getter
@Accessors(chain = true)
@ApiModel(description = "AttApiApplyTimeLongItem")
public class AttApiApplyTimeLongItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 人员编号 */
    @ApiModelProperty(value = "人员编号", required = true, example = "'1'")
    private String personPin;

    /** 开始日期时间 */
    @ApiModelProperty(value = "开始时间", required = true, example = "2021-11-19 13:00:00")
    private String startDatetime;

    /** 结束日期时间 */
    @ApiModelProperty(value = "结束时间", required = true, example = "2021-11-19 18:00:00")
    private String endDatetime;

    /** 假种编 */
    @ApiModelProperty(value = "假种编号（详见/api/attApply/getLeaveTypeList）", required = true, example = "L1")
    private String leaveTypeNo;
}
