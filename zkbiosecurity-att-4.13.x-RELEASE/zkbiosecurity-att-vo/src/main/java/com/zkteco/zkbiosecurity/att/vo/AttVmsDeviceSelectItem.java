/**
 * @author: GenerationTools
 * @date: 2018-02-08 下午08:35 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @author: GenerationTools
 * @date: 2018-02-08 下午08:35
 */
@GridConfig(operate = false, idField = "id")
@Setter
@Getter
@Accessors(chain = true)
public class AttVmsDeviceSelectItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 (视频通道ID)
     */
    @GridColumn(columnType = "ra", width = "40", sort = "na")
    private String id;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 设备序列号
     */
    @GridColumn(label = "common_dev_sn", width = "120", sortNo = 1)
    private String deviceSn;

    /**
     * 设备名称
     */
    @GridColumn(label = "common_dev_name", width = "120", sortNo = 2)
    private String deviceName;

    /**
     * 通道编号
     */
    @GridColumn(label = "vms_channel_num", width = "100", sortNo = 3)
    private Short channelNo;

    /**
     * 通道名称
     */
    @GridColumn(label = "vms_channel_name", width = "100", sortNo = 4)
    private String channelName;

    /**
     * 判断是左列表noSelect、还是右列表select
     */
    private String type;

    /**
     * 当前选中的ids
     */
    private String selectId;

    private String authAreaId;

    public AttVmsDeviceSelectItem() {
        super();
    }

    public AttVmsDeviceSelectItem(Boolean equals) {
        super(equals);
    }

    public AttVmsDeviceSelectItem(String id) {
        super(true);
        this.id = id;
    }
}
