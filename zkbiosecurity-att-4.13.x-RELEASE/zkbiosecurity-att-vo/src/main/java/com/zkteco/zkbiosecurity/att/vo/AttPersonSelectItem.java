package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@From(after = "PERS_PERSON PP LEFT JOIN AUTH_DEPARTMENT AD ON PP.AUTH_DEPT_ID = AD.ID")
@GridConfig
@Setter
@Getter
@Accessors(chain = true)
public class AttPersonSelectItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    @Column(name = "PP.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    @Column(name = "PP.ID")
    private String personId;

    @Column(name = "PP.PIN")
    @GridColumn(label = "att_person_pin", width = "100")
    private String personPin;

    @Column(name = "PP.NAME")
    @GridColumn(label = "att_person_name", width = "100")
    private String personName;

    @Column(name = "PP.LAST_NAME")
    @GridColumn(label = "att_person_lastName", showExpression = "#language!='zh_CN'", width = "100")
    private String personLastName;

    @GridColumn(label = "att_person_internalCard", width = "100")
    private String cardNo;

    @Column(name = "PP.GENDER")
    @GridColumn(label = "common_gender", format = "F=pers_person_female,M=pers_person_male,=---", width = "60")
    private String gender;

    @Column(name = "AD.ID")
    private String deptId;

    @Condition(value = "AD.ID", equalTag = "in")
    private String inDeptId;

    @Column(name = "AD.CODE")
    private String deptCode;

    @Column(name = "AD.NAME")
    @GridColumn(label = "pers_dept_entity", width = "*", sort = "na")
    private String deptName;

    @Condition(value = "(LOWER (PP.NAME) LIKE LOWER (''%{0}%'') OR LOWER (PP.LAST_NAME) LIKE LOWER (''%{0}%''))")
    private String likeName;

    @Condition(
        value = "PP.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID = ''{0}'')")
    private String userId;

    @Condition(
            value = "PP.ID NOT IN (SELECT AAP.PERS_PERSON_ID FROM ATT_AREA_PERSON AAP WHERE AAP.AUTH_AREA_ID = ''{0}'')")
    private String linkId;

    // 判断是左列表（值为noSelect）还是右列表（值为select）
    private String type;

    // 双列表右侧选中的人员ID
    @Condition(value = "PP.ID", equalTag = "not in")
    private String selectId;

    // 是否包含下级
    private String isIncludeLower;
}