<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zkteco</groupId>
        <artifactId>zkbiosecurity-att</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zkteco</groupId>
    <artifactId>zkbiosecurity-att-system</artifactId>
    <version>${project.parent.version}</version>
    <name>${project.artifactId}</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-core</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-auth-api</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-system-api</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-att-service</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-att-web</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-att-client</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>