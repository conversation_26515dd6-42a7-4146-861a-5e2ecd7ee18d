package com.zkteco.zkbiosecurity.att.init;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.service.AttPointService;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.utils.VersionUtil;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.jdbc.JdbcOperateTemplate;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.system.constants.BaseDataConstants;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryService;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryValueService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseDictionaryItem;
import com.zkteco.zkbiosecurity.system.vo.BaseDictionaryValueItem;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@Order(value = 40)
public class AttInit implements CommandLineRunner {
    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private BaseDictionaryService baseDictionaryService;
    @Autowired
    private BaseDictionaryValueService baseDictionaryValueService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;
    @Autowired
    private JdbcOperateTemplate jdbcOperateTemplate;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttPointService attPointService;

    @Value("${system.productCode}")
    private String productCode;
    @Value("${system.language:zh_CN}")
    private String language;

    @Override
    public void run(String... args) throws Exception {
        boolean alreadyInit = baseSysParamService.getAlreadyInitModule("AttProInit");
        if (!alreadyInit) {
            // 初始化菜单
            initAuthPermission();
            // 初始化考勤模块参数
            initAttParams();
            // 初始化假种
            initAttLeaveType();
            // 初始化字典参数
            initAttDictionary();
            // 首次初始化才给设置,模块版本为当前版本,从AttInit旧版本升级上来不需要设置,否则导致以为是最新版本不升级
            if (!baseSysParamService.getAlreadyInitModule("AttInit")) {
                // 初始化模块版本信息
                initUpgradeVersion();
            }
            // 初始化管理员APP权限、menus.json
            initAppMenus();
            baseSysParamService.setAlreadyInitModule("AttProInit");
        }

        // 初始化业务数据（每次启动都执行）
        initAttData();
    }

    /**
     * 初始化业务数据（每次启动都执行）
     */
    private void initAttData() {
        try {
            // 初始化考勤点标记
            attPointService.initAttPointTransaction();
        } catch (Exception e) {
            log.error("ATT initAttData Exception", e);
        }
    }

    /**
     * 初始化功能菜单
     */
    private void initAuthPermission() {
        // 系统模块菜单权限
        AuthPermissionItem systemMenuItem = null;
        // 父类菜单权限
        AuthPermissionItem parentMenuItem = null;
        // 子类菜单权限
        AuthPermissionItem childMenuItem = null;
        // 按钮菜单权限
        AuthPermissionItem buttonMenuItem = null;

        /* ------------- 系统菜单 -------------*/
        systemMenuItem =
            new AuthPermissionItem("Att", "att_module", "att", AuthContants.RESOURCE_TYPE_SYSTEM, ZKConstant.TRUE, 4);
        systemMenuItem = authPermissionService.saveItem(systemMenuItem);

        /* ------------- 一级菜单:考勤管理 -------------*/
        parentMenuItem = new AuthPermissionItem("AttAttendanceDevice", "att_leftMenu_attendanceDevice",
            "att:attendance:device", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 1);
        parentMenuItem.setParentId(systemMenuItem.getId());
        parentMenuItem.setActionLink("attDevice.do");
        parentMenuItem.setImg("att_device.png");
        parentMenuItem.setImgHover("att_device_over.png");
        parentMenuItem = authPermissionService.saveItem(parentMenuItem);

        /* ------------- 二级菜单:人员验证方式 -------------*/
        childMenuItem = new AuthPermissionItem("AttPersonVerifyMode", "att_personVerifyMode", "att:personVerifyMode",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 0);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attPerson.do?verifyModeIndex");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttPersonVerifyModeRefresh", "common_op_refresh",
            "att:personVerifyMode:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 验证方式设置
        buttonMenuItem = new AuthPermissionItem("AttPersonVerifyModeSetting", "att_personVerifyMode_setting",
            "att:personVerifyMode:setting", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:按区域设置人员 -------------*/
        childMenuItem = new AuthPermissionItem("AttAreaPerson", "att_areaPerson_byAreaPerson", "att:areaPerson",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 1);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attAreaPerson.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttAreaPersonRefresh", "common_op_refresh", "att:areaPerson:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 删除人员
        buttonMenuItem = new AuthPermissionItem("AttAreaPersonDel", "common_op_del", "att:areaPerson:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 添加人员
        buttonMenuItem = new AuthPermissionItem("AttAreaPersonAddPerson", "att_op_forZoneAddPers",
            "att:areaPerson:addPerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 对私短消息
        buttonMenuItem = new AuthPermissionItem("AttAreaPersonSetUserSms", "att_op_dataUserSms",
            "att:areaPerson:setUserSms", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 重新同步到设备
        buttonMenuItem = new AuthPermissionItem("AttAreaPersonSyncDev", "att_op_syncPers",
            "att:areaPerson:syncPerToDev", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 导入批量删除
        buttonMenuItem = new AuthPermissionItem("AttAreaPersonImportBatchDel", "att_areaPerson_importBatchDel",
            "att:areaPerson:importBatchDel", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 导入
        buttonMenuItem = new AuthPermissionItem("AttAreaPersonImport", "att_areaPerson_importAreaPerson",
            "att:areaPerson:import", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttAreaPersonExport", "common_op_export", "att:areaPerson:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:考勤设备-------------*/
        childMenuItem = new AuthPermissionItem("AttDevice", "att_leftMenu_device", "att:device",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attDevice.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttDeviceRefresh", "common_op_refresh", "att:device:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 设备编辑
        buttonMenuItem = new AuthPermissionItem("AttDeviceEdit", "common_op_edit", "att:device:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 设备搜索
        buttonMenuItem = new AuthPermissionItem("AttDeviceSearch", "common_dev_searchDev", "att:device:search",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 设备删除
        buttonMenuItem = new AuthPermissionItem("AttDeviceDel", "common_op_del", "att:device:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 设备启用
        buttonMenuItem = new AuthPermissionItem("AttDeviceEnable", "common_enable", "att:device:enable",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 设备禁用
        buttonMenuItem = new AuthPermissionItem("AttDeviceDisable", "common_disable", "att:device:disable",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 同步软件数据到设备
        buttonMenuItem = new AuthPermissionItem("AttDeviceSyncDev", "att_op_syncDev", "att:device:syncdev",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 考勤数据校对
        buttonMenuItem = new AuthPermissionItem("AttDeviceSetTime", "att_op_account", "att:device:settime",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 获取指定人员数据
        buttonMenuItem = new AuthPermissionItem("AttDeviceFillPin", "att_device_getPin", "att:device:fillpin",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 9);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 重新上传数据
        buttonMenuItem = new AuthPermissionItem("AttDeviceSetStamp", "att_op_check", "att:device:setstamp",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 10);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 清除设备命令
        buttonMenuItem = new AuthPermissionItem("AttDeviceDeleteCmd", "att_op_deleteCmd", "att:device:deletecmd",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 11);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 对公短消息
        buttonMenuItem = new AuthPermissionItem("AttDeviceSetSms", "att_op_dataSms", "att:device:setsms",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 12);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 清除考勤照片
        buttonMenuItem = new AuthPermissionItem("AttDeviceClearAttPic", "att_op_clearAttPic", "att:device:clearattpic",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 13);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 清除考勤记录
        buttonMenuItem = new AuthPermissionItem("AttDeviceClearAttLog", "att_op_clearAttLog", "att:device:clearattlog",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 14);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 重启设备
        buttonMenuItem = new AuthPermissionItem("AttDeviceReboot", "common_dev_reboot", "att:device:reboot",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 15);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 获取设备参数
        buttonMenuItem = new AuthPermissionItem("AttDeviceGetDevOpt", "common_dev_getDevOpt", "att:device:getdevopt",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 16);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 清除设备人员
        buttonMenuItem = new AuthPermissionItem("AttDeviceClearAttPers", "att_op_clearAttPers",
            "att:device:clearattpers", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 17);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 查看设备参数
        buttonMenuItem = new AuthPermissionItem("AttDeviceQueryOption", "att_deviceOption_query",
            "att:device:queryOption", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 18);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 授权区域
        buttonMenuItem = new AuthPermissionItem("AttDeviceAuthArea", "auth_user_authorizedAreas", "att:device:authArea",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 19);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttDeviceExport", "common_op_export", "att:device:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 20);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 升级固件
        buttonMenuItem = new AuthPermissionItem("AttUpgradeFirmware", "common_dev_upgradeFirmware",
            "att:device:upgradeFirmware", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 21);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 设置考勤状态
        buttonMenuItem = new AuthPermissionItem("AttSetShortcutKey", "att_cardStatus_setting",
            "att:device:setShortcutKey", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 22);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:考勤点-------------*/
        childMenuItem = new AuthPermissionItem("AttPoint", "att_leftMenu_point", "att:point",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 4);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attPoint.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttPointRefresh", "common_op_refresh", "att:point:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 新增
        buttonMenuItem = new AuthPermissionItem("AttPointAdd", "common_op_new", "att:point:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttPointExport", "common_op_export", "att:point:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 删除
        buttonMenuItem = new AuthPermissionItem("AttPointDel", "common_op_del", "att:point:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 编辑
        buttonMenuItem = new AuthPermissionItem("AttPointEdit", "common_op_edit", "att:point:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:实时点名-------------*/
        childMenuItem = new AuthPermissionItem("AttRealTimeCallRoll", "att_leftMenu_realTime_callRoll",
            "att:realTime:callRoll", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 5);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attRealTimeCallRoll.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);

        /* ------------- 二级菜单:APP考勤签到位置-------------*/
        childMenuItem = new AuthPermissionItem("AttSignAddress", "att_leftMenu_sign_address", "att:signAddress",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 6);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("/attSignAddress.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttSignAddressRefresh", "common_op_refresh", "att:signAddress:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 新增
        buttonMenuItem = new AuthPermissionItem("AttSignAddressAdd", "common_op_new", "att:signAddress:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 删除
        buttonMenuItem = new AuthPermissionItem("AttSignAddressDel", "common_op_del", "att:signAddress:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 编辑
        buttonMenuItem = new AuthPermissionItem("AttSignAddressEdit", "common_op_edit", "att:signAddress:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:服务器下发命令-------------*/
        childMenuItem = new AuthPermissionItem("AttAdmsDevCmd", "att_leftMenu_adms_devCmd", "att:adms:devCmd",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 7);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attAdmsDevCmd.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttAdmsDevCmdRefresh", "common_op_refresh", "att:adms:devCmd:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 清空命令表
        buttonMenuItem = new AuthPermissionItem("AttAdmsDevCmdClearCmd", "adms_devCmd_clearCmd",
            "att:adms:devCmd:clearCmd", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttAdmsDevCmdExport", "common_op_export", "att:adms:devCmd:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 删除
        buttonMenuItem = new AuthPermissionItem("AttAdmsDevCmdDel", "common_op_del", "att:adms:devCmd:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:设备操作日志-------------*/
        childMenuItem = new AuthPermissionItem("AttDeviceOpLog", "att_leftMenu_device_op_log", "att:deviceoplog",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 8);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attDeviceOpLog.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttDeviceOpLogRefresh", "common_op_refresh", "att:deviceoplog:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttDeviceOpLogExport", "common_op_export", "att:deviceoplog:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 一级菜单:考勤设置 -------------*/
        parentMenuItem = new AuthPermissionItem("AttBasicInformation", "att_person_attSet", "att:basic:information",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
        parentMenuItem.setParentId(systemMenuItem.getId());
        parentMenuItem.setActionLink("attRule.do");
        parentMenuItem.setImg("att_basic.png");
        parentMenuItem.setImgHover("att_basic_over.png");
        parentMenuItem = authPermissionService.saveItem(parentMenuItem);

        // 人事编辑标签页权限初始化，约定在“人员菜单下考勤orderId为101”
        childMenuItem = authPermissionService.getItemByCode("PersPerson");
        if (null != childMenuItem) {
            buttonMenuItem = new AuthPermissionItem("PersPersonAttEdit", "pers_person_attSet", "pers:person:attEdit",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 101);
            buttonMenuItem.setParentId(childMenuItem.getId());
            buttonMenuItem = authPermissionService.initData(buttonMenuItem);
        }

        /* ------------- 二级菜单:考勤规则-------------*/
        childMenuItem = new AuthPermissionItem("AttRule", "att_leftMenu_base_rule", "att:rule",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 1);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attRule.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 编辑
        buttonMenuItem = new AuthPermissionItem("AttRuleEdit", "common_op_edit", "att:rule:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        // 实时点名设置
        buttonMenuItem = new AuthPermissionItem("AttRuleRealTimeSet", "att_realTime_realTimeSet",
            "att:rule:realTimeSet", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        // 员工自助设置
        buttonMenuItem = new AuthPermissionItem("AttRuleEmployeeSet", "auth_login_personLogin", "att:rule:employeeSet",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        // 加班等级设置
        buttonMenuItem = new AuthPermissionItem("AttRuleOverTimeLevelSet", "att_param_overTimeSetting", "att:rule:overTimeLevelSet",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:节假日-------------*/
        childMenuItem = new AuthPermissionItem("AttHoliday", "att_leftMenu_holiday", "att:holiday",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 3);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attHoliday.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttHolidayRefresh", "common_op_refresh", "att:holiday:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 新增
        buttonMenuItem = new AuthPermissionItem("AttHolidayAdd", "common_op_new", "att:holiday:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 删除
        buttonMenuItem = new AuthPermissionItem("AttHolidayDel", "common_op_del", "att:holiday:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 编辑
        buttonMenuItem = new AuthPermissionItem("AttHolidayEdit", "common_op_edit", "att:holiday:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:假种-------------*/
        childMenuItem = new AuthPermissionItem("AttLeaveType", "att_leftMenu_leaveType", "att:leaveType",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 4);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attLeaveType.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttLeaveTypeRefresh", "common_op_refresh", "att:leaveType:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 新增
        buttonMenuItem = new AuthPermissionItem("AttLeaveTypeAdd", "common_op_new", "att:leaveType:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 删除
        buttonMenuItem = new AuthPermissionItem("AttLeaveTypeDel", "common_op_del", "att:leaveType:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 编辑
        buttonMenuItem = new AuthPermissionItem("AttLeaveTypeEdit", "common_op_edit", "att:leaveType:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:报表推送-------------*/
        childMenuItem = new AuthPermissionItem("AttAutoExport", "att_leftMenu_autoExport", "att:autoExport",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 5);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attAutoExport.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttAutoExportRefresh", "common_op_refresh", "att:autoExport:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 新增
        buttonMenuItem = new AuthPermissionItem("AttAutoExportAdd", "common_op_new", "att:autoExport:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 删除
        buttonMenuItem = new AuthPermissionItem("AttAutoExportDel", "common_op_del", "att:autoExport:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        // 启用
        buttonMenuItem = new AuthPermissionItem("AttAutoExportEnable", "common_enable", "att:autoExport:enable",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 禁用
        buttonMenuItem = new AuthPermissionItem("AttAutoExportDisable", "common_disable", "att:autoExport:disable",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 编辑
        buttonMenuItem = new AuthPermissionItem("AttAutoExportEdit", "common_op_edit", "att:autoExport:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        /* 员工自助菜单，由考勤模块初始化，防止先加了工作流再加考勤导致菜单未初始化 */
        /* ------------- 二级菜单:流程设置-------------*/
        childMenuItem = new AuthPermissionItem("WfFlowableList", "wf_leftMenu_flow_setting", "wf:flow",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 6);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("/wfFlow.do?moduleCode=att");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("WfFlowItemRefresh", "common_op_refresh", "wf:flow:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 添加
        buttonMenuItem = new AuthPermissionItem("WfFlowItemAdd", "common_op_add", "wf:flow:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 编辑
        buttonMenuItem = new AuthPermissionItem("WfFlowItemEdit", "common_op_edit", "wf:flow:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 删除
        buttonMenuItem = new AuthPermissionItem("WfFlowItemDel", "common_op_del", "wf:flow:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 启用
        buttonMenuItem = new AuthPermissionItem("WfFlowItemEnable", "common_enable", "wf:flow:enable",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 禁用
        buttonMenuItem = new AuthPermissionItem("WfFlowItemDisable", "common_commStatus_disable", "wf:flow:disable",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 一级菜单:排班管理 -------------*/
        parentMenuItem = new AuthPermissionItem("AttScheduleManagement", "att_leftMenu_scheduleManagement",
            "att:schedule:management", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 4);
        parentMenuItem.setParentId(systemMenuItem.getId());
        parentMenuItem.setActionLink("attGroup.do");
        parentMenuItem.setImg("att_schedule.png");
        parentMenuItem.setImgHover("att_schedule_over.png");
        parentMenuItem = authPermissionService.saveItem(parentMenuItem);

        /* ------------- 二级菜单:时间段-------------*/
        childMenuItem = new AuthPermissionItem("AttTimeSlot", "att_leftMenu_timeSlot", "att:timeSlot",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attTimeSlot.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttTimeSlotRefresh", "common_op_refresh", "att:timeSlot:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 新增
        buttonMenuItem = new AuthPermissionItem("AttTimeSlotAdd", "common_op_new", "att:timeSlot:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 删除
        buttonMenuItem = new AuthPermissionItem("AttTimeSlotDel", "common_op_del", "att:timeSlot:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        // 编辑
        buttonMenuItem = new AuthPermissionItem("AttTimeSlotEdit", "common_op_edit", "att:timeSlot:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:班次-------------*/
        childMenuItem = new AuthPermissionItem("AttShift", "att_leftMenu_shift", "att:shift",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 3);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attShift.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttShiftRefresh", "common_op_refresh", "att:shift:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 新增
        buttonMenuItem = new AuthPermissionItem("AttShiftAdd", "common_op_new", "att:shift:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 删除
        buttonMenuItem = new AuthPermissionItem("AttShiftDel", "common_op_del", "att:shift:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        // 编辑
        buttonMenuItem = new AuthPermissionItem("AttShiftEdit", "common_op_edit", "att:shift:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 清空时间段
        buttonMenuItem = new AuthPermissionItem("AttShiftClearTimeSlot", "att_shift_cleanTimeSlot",
            "att:shift:clearTimeSlot", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:人员排班-------------*/
        childMenuItem = new AuthPermissionItem("AttPersonSch", "att_leftMenu_personSch", "att:personsch",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 4);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attPersonSch.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);

        // 周期排班
        buttonMenuItem = new AuthPermissionItem("AttPersonSchCycleSch", "att_personSch_cycleSch",
            "att:personsch:cycleSch", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 临时排班
        buttonMenuItem = new AuthPermissionItem("AttPersonSchTempSch", "att_leftMenu_tempSch", "att:personsch:tempSch",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 清除周期排班
        buttonMenuItem = new AuthPermissionItem("AttPersonSchCleanCycleSch", "att_personSch_cleanCycleSch",
            "att:personsch:cleanCycleSch", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 清除临时排班
        buttonMenuItem = new AuthPermissionItem("AttPersonSchCleanTempSch", "att_personSch_cleanTempSch",
            "att:personsch:cleanTempSch", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出人员排班
        buttonMenuItem = new AuthPermissionItem("AttPersonSchExport", "att_personSch_export", "att:personsch:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导入人员临时排班
        buttonMenuItem = new AuthPermissionItem("AttPersonSchImport", "att_personSch_import", "att:personsch:import",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 下载人员临时排班模版
        buttonMenuItem = new AuthPermissionItem("AttPersonSchExportTemplate", "att_personSch_exportTemplate",
            "att:personsch:exportTemplate", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 导入人员临时排班
        buttonMenuItem = new AuthPermissionItem("AttPersonSchImportCycSch", "att_personSch_importCycSch",
            "att:personsch:importCycSch", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 9);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 下载人员临时排班模版
        buttonMenuItem =
            new AuthPermissionItem("AttPersonSchExportCycSchTemplate", "att_personSch_exportCycSchTemplate",
                "att:personsch:exportCycSchTemplate", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 10);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:分组排班-------------*/
        childMenuItem = new AuthPermissionItem("AttGroup", "att_leftMenu_groupSch", "att:group",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 5);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attGroup.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttGroupRefresh", "common_op_refresh", "att:group:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 新增
        buttonMenuItem = new AuthPermissionItem("AttGroupAdd", "common_op_new", "att:group:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 删除分组
        buttonMenuItem = new AuthPermissionItem("AttGroupDel", "common_op_del", "att:group:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 编辑分组
        buttonMenuItem = new AuthPermissionItem("AttGroupEdit", "common_op_edit", "att:group:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 分组添加人员
        buttonMenuItem = new AuthPermissionItem("AttGroupAddPerson", "common_op_addPerson", "att:group:addPerson",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 刷新分组人员
        buttonMenuItem = new AuthPermissionItem("AttGroupPersonRefresh", "common_op_refresh", "att:groupPerson:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 删除分组人员
        buttonMenuItem = new AuthPermissionItem("AttGroupPersonDel", "pers_common_delPerson", "att:groupPerson:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 周期排班
        buttonMenuItem = new AuthPermissionItem("AttGroupSchCycleSch", "att_personSch_cycleSch",
            "att:personsch:cycleSch", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 9);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 临时排班
        buttonMenuItem = new AuthPermissionItem("AttGroupSchTempSch", "att_leftMenu_tempSch", "att:personsch:tempSch",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 10);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 清除周期排班
        buttonMenuItem = new AuthPermissionItem("AttGroupSchCleanCycleSch", "att_personSch_cleanCycleSch",
            "att:personsch:cleanCycleSch", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 11);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 清除临时排班
        buttonMenuItem = new AuthPermissionItem("AttGroupSchCleanTempSch", "att_personSch_cleanTempSch",
            "att:personsch:cleanTempSch", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 12);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:排班详情-------------*/
        childMenuItem = new AuthPermissionItem("AttSchDetails", "att_leftMenu_schDetails", "att:schDetails",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 6);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attSchDetails.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 删除排班
        buttonMenuItem = new AuthPermissionItem("AttSchDetailsDel", "att_personSch_delSch", "att:schDetails:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 编辑
        buttonMenuItem = new AuthPermissionItem("AttSchDetailsEdit", "common_op_edit", "att:schDetails:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttSchDetailsDxport", "common_op_export", "att:schDetails:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 一级菜单:异常管理 -------------*/
        parentMenuItem = new AuthPermissionItem("AttExceptionManagement", "att_leftMenu_exceptionManagement",
            "att:exception:management", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 5);
        parentMenuItem.setParentId(systemMenuItem.getId());
        parentMenuItem.setActionLink("attSign.do");
        parentMenuItem.setImg("att_exception.png");
        parentMenuItem.setImgHover("att_exception_over.png");
        parentMenuItem = authPermissionService.saveItem(parentMenuItem);

        /* ------------- 二级菜单:补签单-------------*/
        childMenuItem = new AuthPermissionItem("AttSign", "att_leftMenu_sign", "att:sign",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 1);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attSign.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttSignRefresh", "common_op_refresh", "att:sign:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 新增
        buttonMenuItem = new AuthPermissionItem("AttSignAdd", "common_op_new", "att:sign:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 删除
        buttonMenuItem = new AuthPermissionItem("AttSignDel", "common_op_del", "att:sign:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttSignExport", "common_op_export", "att:sign:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        // 导入
        buttonMenuItem = new AuthPermissionItem("AttSignImport", "common_op_import", "att:sign:import",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        // 审批
        buttonMenuItem = new AuthPermissionItem("AttSignApproval", "att_flow_approve", "att:sign:approval",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:请假-------------*/
        childMenuItem = new AuthPermissionItem("AttLeave", "att_leftMenu_leave", "att:leave",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attLeave.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttLeaveRefresh", "common_op_refresh", "att:leave:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 新增
        buttonMenuItem = new AuthPermissionItem("AttLeaveAdd", "common_op_new", "att:leave:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 删除
        buttonMenuItem = new AuthPermissionItem("AttLeaveDel", "common_op_del", "att:leave:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttLeaveExport", "common_op_export", "att:leave:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 请假单照片
        buttonMenuItem = new AuthPermissionItem("AttLeaveImage", "att_leave_image", "att:leave:image",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        // 导入
        buttonMenuItem = new AuthPermissionItem("AttLeaveImport", "common_op_import", "att:leave:import",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        // 审批
        buttonMenuItem = new AuthPermissionItem("AttLeaveApproval", "att_flow_approve", "att:leave:approval",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:出差-------------*/
        childMenuItem = new AuthPermissionItem("AttTrip", "att_leftMenu_trip", "att:trip",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 3);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attTrip.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttTripRefresh", "common_op_refresh", "att:trip:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 新增
        buttonMenuItem = new AuthPermissionItem("AttTripAdd", "common_op_new", "att:trip:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 删除
        buttonMenuItem = new AuthPermissionItem("AttTripDel", "common_op_del", "att:trip:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttTripExport", "common_op_export", "att:trip:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:外出-------------*/
        childMenuItem = new AuthPermissionItem("AttOut", "att_leftMenu_out", "att:out", AuthContants.RESOURCE_TYPE_MENU,
            ZKConstant.TRUE, 4);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attOut.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttOutRefresh", "common_op_refresh", "att:out:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 新增
        buttonMenuItem = new AuthPermissionItem("AttOutAdd", "common_op_new", "att:out:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 删除
        buttonMenuItem = new AuthPermissionItem("AttOutDel", "common_op_del", "att:out:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttOutExport", "common_op_export", "att:out:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:加班-------------*/
        childMenuItem = new AuthPermissionItem("AttOvertime", "att_leftMenu_overtime", "att:overtime",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 5);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attOvertime.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttOvertimeRefresh", "common_op_refresh", "att:overtime:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 新增
        buttonMenuItem = new AuthPermissionItem("AttOvertimeAdd", "common_op_new", "att:overtime:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 删除
        buttonMenuItem = new AuthPermissionItem("AttOvertimeDel", "common_op_del", "att:overtime:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttOvertimeExport", "common_op_export", "att:overtime:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        // 导入
        buttonMenuItem = new AuthPermissionItem("AttOvertimeImport", "common_op_import", "att:overtime:import",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        // 审批
        buttonMenuItem = new AuthPermissionItem("AttOvertimeApproval", "att_flow_approve", "att:overtime:approval",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:调休-------------*/
        childMenuItem = new AuthPermissionItem("AttAdjust", "att_schedule_offDetail", "att:adjust",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 6);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attAdjust.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttAdjustRefresh", "common_op_refresh", "att:adjust:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 新增
        buttonMenuItem = new AuthPermissionItem("AttAdjustAdd", "common_op_new", "att:adjust:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 删除
        buttonMenuItem = new AuthPermissionItem("AttAdjustDel", "common_op_del", "att:adjust:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttAdjustExport", "common_op_export", "att:adjust:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        // 导入
        buttonMenuItem = new AuthPermissionItem("AttAdjustImport", "common_op_import", "att:adjust:import",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        // 审批
        buttonMenuItem = new AuthPermissionItem("AttAdjustApproval", "att_flow_approve", "att:adjust:approval",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:调班-------------*/
        childMenuItem = new AuthPermissionItem("AttClass", "att_leftMenu_class", "att:class",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 7);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attClass.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttClassRefresh", "common_op_refresh", "att:class:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 新增
        buttonMenuItem = new AuthPermissionItem("AttClassAdd", "common_op_new", "att:class:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 删除
        buttonMenuItem = new AuthPermissionItem("AttClassDel", "common_op_del", "att:class:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttClassExport", "common_op_export", "att:class:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        // 导入
        buttonMenuItem = new AuthPermissionItem("AttClassImport", "common_op_import", "att:class:import",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        // 审批
        buttonMenuItem = new AuthPermissionItem("AttClassApproval", "att_flow_approve", "att:class:approval",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 一级菜单:考勤记录报表 -------------*/
        parentMenuItem = new AuthPermissionItem("AttDetailReport", "att_leftMenu_detailReport", "att:detail:report",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 6);
        parentMenuItem.setParentId(systemMenuItem.getId());
        parentMenuItem.setActionLink("attManualCalculation.do");
        parentMenuItem.setImg("att_report.png");
        parentMenuItem.setImgHover("att_report_over.png");
        authPermissionService.saveItem(parentMenuItem);

        /* ------------- 二级菜单:考勤计算-------------*/
        childMenuItem = new AuthPermissionItem("AttManualCalculation", "att_leftMenu_manualCalculation",
            "att:manualCalculation", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 1);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attManualCalculation.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        buttonMenuItem = new AuthPermissionItem("AttManualCalculationRefresh", "common_op_refresh",
            "att:manualCalculation:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        buttonMenuItem = new AuthPermissionItem("AttManualCalculationCalculate", "att_op_calculation",
            "att:manualCalculation:calculate", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:原始记录表-------------*/
        childMenuItem = new AuthPermissionItem("AttTransaction", "att_leftMenu_transaction", "att:transaction",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attTransaction.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttTransactionRefresh", "common_op_refresh", "att:transaction:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttTransactionExport", "common_op_export", "att:transaction:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
        // 导入U盘记录
        buttonMenuItem = new AuthPermissionItem("AttTransactionImportUSBRecord", "att_op_importUSBRecord",
            "att:transaction:importUSBRecord", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出考勤照片
        buttonMenuItem = new AuthPermissionItem("AttTransactionExportAttPhoto", "att_transaction_exportAttPhoto",
            "att:transaction:exportAttPhoto", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 9);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 同步考勤点记录
        buttonMenuItem = new AuthPermissionItem("AttTransactionSyncRecord", "att_transaction_SyncRecord",
            "att:transaction:SyncRecord", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 10);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:打卡详情表-------------*/
        childMenuItem = new AuthPermissionItem("AttDayCardDetailReport", "att_leftMenu_dayCardDetailReport",
            "att:dayCardDetailReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 3);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attDayCardDetailReport.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttDayCardDetailReportRefresh", "common_op_refresh",
            "att:dayCardDetailReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttDayCardDetailReportExport", "common_op_export",
            "att:dayCardDetailReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 一级菜单:考勤日报表 -------------*/
        parentMenuItem = new AuthPermissionItem("AttDailyReport", "att_leftMenu_dailyReport", "att:daily:report",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 7);
        parentMenuItem.setParentId(systemMenuItem.getId());
        parentMenuItem.setActionLink("attDayDetailReport.do");
        parentMenuItem.setImg("att_report.png");
        parentMenuItem.setImgHover("att_report_over.png");
        authPermissionService.saveItem(parentMenuItem);

        /* ------------- 二级菜单:日明细报表-------------*/
        childMenuItem = new AuthPermissionItem("AttDayDetailReport", "att_leftMenu_dayDetailReport",
            "att:dayDetailReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 1);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attDayDetailReport.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttDayDetailReportRefresh", "common_op_refresh",
            "att:dayDetailReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttDayDetailReportExport", "common_op_export",
            "att:dayDetailReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:工作时长表-------------*/
        childMenuItem = new AuthPermissionItem("AttWorkTimeReport", "att_leftMenu_workTimeReport", "att:workTimeReport",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attDayDetailReport.do?indexWorkTimeReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttWorkTimeReportRefresh", "common_op_refresh",
            "att:workTimeReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttWorkTimeReportExport", "common_op_export",
            "att:workTimeReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:加班报表------------*/
        childMenuItem = new AuthPermissionItem("AttOvertimeReport", "att_leftMenu_overtimeReport", "att:overtimeReport",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 3);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attDayDetailReport.do?indexOvertimeReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        buttonMenuItem = new AuthPermissionItem("AttOvertimeReportRefresh", "common_op_refresh",
            "att:overtimeReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        buttonMenuItem = new AuthPermissionItem("AttOvertimeReportExport", "common_op_export",
            "att:overtimeReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:请假报表-------------*/
        childMenuItem = new AuthPermissionItem("AttLeaveReport", "att_leftMenu_leaveReport", "att:leaveReport",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 4);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attDayDetailReport.do?indexLeaveReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttLeaveReportRefresh", "common_op_refresh", "att:leaveReport:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttLeaveReportExport", "common_op_export", "att:leaveReport:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:出勤异常-------------*/
        childMenuItem = new AuthPermissionItem("AttAbnormalReport", "att_leftMenu_abnormal", "att:abnormalReport",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 5);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attDayDetailReport.do?indexAbnormalReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttAbnormalReportRefresh", "common_op_refresh",
            "att:abnormalReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttAbnormalReportExport", "common_op_export",
            "att:abnormalReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:迟到报表------------*/
        childMenuItem = new AuthPermissionItem("AttLateReport", "att_leftMenu_lateReport", "att:lateReport",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 6);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attDayDetailReport.do?indexLateReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        buttonMenuItem = new AuthPermissionItem("AttLateReportRefresh", "common_op_refresh", "att:lateReport:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        buttonMenuItem = new AuthPermissionItem("AttLateReportExport", "common_op_export", "att:lateReport:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:早退报表------------*/
        childMenuItem = new AuthPermissionItem("AttEarlyReport", "att_leftMenu_earlyReport", "att:earlyReport",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 7);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attDayDetailReport.do?indexEarlyReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        buttonMenuItem = new AuthPermissionItem("AttEarlyReportRefresh", "common_op_refresh", "att:earlyReport:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        buttonMenuItem = new AuthPermissionItem("AttEarlyReportExport", "common_op_export", "att:earlyReport:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:缺勤报表------------*/
        childMenuItem = new AuthPermissionItem("AttAbsentReport", "att_leftMenu_absentReport", "att:absentReport",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 8);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attDayDetailReport.do?indexAbsentReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        buttonMenuItem = new AuthPermissionItem("AttAbsentReportRefresh", "common_op_refresh",
            "att:absentReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        buttonMenuItem = new AuthPermissionItem("AttAbsentReportExport", "common_op_export", "att:absentReport:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 一级菜单:考勤月报表 -------------*/
        parentMenuItem = new AuthPermissionItem("AttMonthReport", "att_leftMenu_monthReport", "att:month:report",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 8);
        parentMenuItem.setParentId(systemMenuItem.getId());
        parentMenuItem.setActionLink("attMonthDetailReport.do");
        parentMenuItem.setImg("att_report.png");
        parentMenuItem.setImgHover("att_report_over.png");
        authPermissionService.saveItem(parentMenuItem);

        /* ------------- 二级菜单:月考勤状态表-------------*/
        childMenuItem = new AuthPermissionItem("AttMonthDetailReport", "att_leftMenu_monthDetailReport",
            "att:monthDetailReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 1);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attMonthDetailReport.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttMonthDetailReportRefresh", "common_op_refresh",
            "att:monthDetailReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttMonthDetailReportExport", "common_op_export",
            "att:monthDetailReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:月工作时长报表-------------*/
        childMenuItem = new AuthPermissionItem("AttMonthWorkTimeReport", "att_leftMenu_monthWorkTimeReport",
            "att:monthWorkTimeReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attMonthDetailReport.do?indexMonthWorkTimeReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttMonthWorkTimeReportRefresh", "common_op_refresh",
            "att:monthWorkTimeReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttMonthWorkTimeReportExport", "common_op_export",
            "att:monthWorkTimeReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:月打卡表-------------*/
        childMenuItem = new AuthPermissionItem("AttMonthCardReport", "att_leftMenu_monthCardReport",
            "att:monthCardReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 3);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attMonthDetailReport.do?indexMonthCardReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttMonthCardReportRefresh", "common_op_refresh",
            "att:monthCardReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttMonthCardReportExport", "common_op_export",
            "att:monthCardReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:月加班表-------------*/
        childMenuItem = new AuthPermissionItem("AttMonthOvertimeReport", "att_leftMenu_monthOvertimeReport",
            "att:monthOvertimeReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 3);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attMonthDetailReport.do?indexMonthOvertimeReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttMonthOvertimeReportRefresh", "common_op_refresh",
            "att:monthOvertimeReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttMonthOvertimeReportExport", "common_op_export",
            "att:monthOvertimeReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 一级菜单:考勤汇总报表 -------------*/
        parentMenuItem = new AuthPermissionItem("AttStatisticalReport", "att_leftMenu_statisticalReport",
            "att:statistical:report", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 9);
        parentMenuItem.setParentId(systemMenuItem.getId());
        parentMenuItem.setActionLink("attMonthStatisticalReport.do");
        parentMenuItem.setImg("att_statistics_report.png");
        parentMenuItem.setImgHover("att_statistics_report_over.png");
        parentMenuItem = authPermissionService.saveItem(parentMenuItem);

        /* ------------- 二级菜单:人员汇总表-------------*/
        childMenuItem = new AuthPermissionItem("AttMonthStatisticalReport", "att_leftMenu_monthStatisticalReport",
            "att:monthStatisticalReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 1);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attMonthStatisticalReport.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttMonthStatisticalReportRefresh", "common_op_refresh",
            "att:monthStatisticalReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttMonthStatisticalReportExport", "common_op_export",
            "att:monthStatisticalReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:人员加班汇总表-------------*/
        childMenuItem = new AuthPermissionItem("AttOvertimeSummaryReport", "att_leftMenu_overtimeSummaryReport",
            "att:overtimeSummaryReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attMonthStatisticalReport.do?indexOvertimeSummaryReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttOvertimeSummaryReportRefresh", "common_op_refresh",
            "att:overtimeSummaryReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttOvertimeSummaryReportExport", "common_op_export",
            "att:overtimeSummaryReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:人员请假汇总表-------------*/
        childMenuItem = new AuthPermissionItem("AttLeaveSummaryReport", "att_leftMenu_leaveSummaryReport",
            "att:leaveSummaryReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 4);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attMonthStatisticalReport.do?indexLeaveSummaryReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttLeaveSummaryReportRefresh", "common_op_refresh",
            "att:leaveSummaryReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttLeaveSummaryReportExport", "common_op_export",
            "att:leaveSummaryReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:部门汇总表-------------*/
        childMenuItem = new AuthPermissionItem("AttDeptStatisticalReport", "att_leftMenu_deptStatisticalReport",
            "att:deptStatistiCalReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 5);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attDeptStatisticalReport.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttDeptStatisticalReportRefresh", "common_op_refresh",
            "att:deptStatistiCalReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttDeptStatisticalReportExport", "common_op_export",
            "att:deptStatistiCalReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:部门加班汇总表-------------*/
        childMenuItem = new AuthPermissionItem("AttDeptOvertimeSummaryReport", "att_leftMenu_deptOvertimeSummaryReport",
            "att:deptOvertimeSummaryReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 6);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attDeptStatisticalReport.do?indexDeptOvertimeSummaryReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttDeptOvertimeSummaryReportRefresh", "common_op_refresh",
            "att:deptOvertimeSummaryReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttDeptOvertimeSummaryReportExport", "common_op_export",
            "att:deptOvertimeSummaryReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:部门请假汇总表-------------*/
        childMenuItem = new AuthPermissionItem("AttDeptLeaveSummaryReport", "att_leftMenu_deptLeaveSummaryReport",
            "att:deptLeaveSummaryReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 7);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attDeptStatisticalReport.do?indexDeptLeaveSummaryReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttDeptLeaveSummaryReportRefresh", "common_op_refresh",
            "att:deptLeaveSummaryReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttDeptLeaveSummaryReportExport", "common_op_export",
            "att:deptLeaveSummaryReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:年假结余表-------------*/
        childMenuItem = new AuthPermissionItem("AttAnnualLeaveReport", "att_annualLeave_report",
            "att:annualLeaveReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 10);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attAnnualLeaveReport.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttAnnualLeaveReportRefresh", "common_op_refresh",
            "att:annualLeaveReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttAnnualLeaveReportExport", "common_op_export",
            "att:annualLeaveReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* 签到点名表 */
        childMenuItem = new AuthPermissionItem("AttSignCallRollReport", "att_leftMenu_attSignCallRollReport",
            "att:signCallRollReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 11);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attSignCallRollReport.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttSignCallRollReportRefresh", "common_op_refresh",
            "att:signCallRollReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttSignCallRollReportExport", "common_op_export",
            "att:signCallRollReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 一级菜单:考勤自定义报表 -------------*/
        parentMenuItem = new AuthPermissionItem("AttCustomReportManage", "att_customReport", "att:customReport:manage",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 10);
        parentMenuItem.setParentId(systemMenuItem.getId());
        parentMenuItem.setActionLink("attCustomReport.do");
        parentMenuItem.setImg("att_statistics_report.png");
        parentMenuItem.setImgHover("att_statistics_report_over.png");
        parentMenuItem = authPermissionService.saveItem(parentMenuItem);

        /* ------------- 二级菜单:考勤自定义报表-------------*/
        childMenuItem = new AuthPermissionItem("AttCustomReport", "att_customReport", "att:customReport",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 1);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attCustomReport.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttCustomReportRefresh", "common_op_refresh",
            "att:customReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 新增
        buttonMenuItem = new AuthPermissionItem("AttCustomReportAdd", "common_op_new", "att:customReport:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 删除
        buttonMenuItem = new AuthPermissionItem("AttCustomReportDel", "common_op_del", "att:customReport:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttCustomReportExport", "common_op_export", "att:customReport:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* 员工自助菜单，由考勤模块初始化，防止先加了工作流再加考勤导致菜单未初始化 */
        /* ------------- 一级菜单:流程任务 -------------*/
        parentMenuItem = new AuthPermissionItem("WfFlowable", "wf_leftMenu_flow", "wf:task",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 11);
        parentMenuItem.setParentId(systemMenuItem.getId());
        parentMenuItem.setImg("wf_flow.png");
        parentMenuItem.setImgHover("wf_flow_over.png");
        parentMenuItem.setActionLink("attWorkflow.do?myApply");
        parentMenuItem = authPermissionService.saveItem(parentMenuItem);

        /* ------------- 二级菜单:我的申请-------------*/
        childMenuItem = new AuthPermissionItem("WFFlowableMyApply", "wf_leftMenu_flowable_myapply", "wf:myApply:list",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 1);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attFlowable.do?myApply");
        childMenuItem = authPermissionService.saveItem(childMenuItem);

        /* ------------- 二级菜单:待审批任务-------------*/
        childMenuItem = new AuthPermissionItem("WfFlowablePendingTask", "wf_leftMenu_flowable", "wf:pendingTask:list",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attFlowable.do?pendingTask");
        childMenuItem = authPermissionService.saveItem(childMenuItem);

        /* ------------- 二级菜单:已审批任务-------------*/
        childMenuItem = new AuthPermissionItem("WfFlowableApprovedTask", "wf_leftMenu_flowable_his",
            "wf:approvedTask:list", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 3);
        childMenuItem.setParentId(parentMenuItem.getId());
        childMenuItem.setActionLink("attFlowable.do?approvedTask");
        childMenuItem = authPermissionService.saveItem(childMenuItem);

        /** --------------------------- 敏感信息保护控制 --------------------------- */
        parentMenuItem = new AuthPermissionItem("AttEncryptProp", "common_param_infoProtection", "att:encryptProp",
                AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 999);
        parentMenuItem.setParentId(systemMenuItem.getId());
        parentMenuItem = authPermissionService.saveItem(parentMenuItem);
        childMenuItem = new AuthPermissionItem("AttPinEncryptProp", "pers_person_pin", "att:pin:encryptProp",
                AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 1);
        childMenuItem.setParentId(parentMenuItem.getId());
        authPermissionService.saveItem(childMenuItem);
        childMenuItem = new AuthPermissionItem("AttNameEncryptProp", "pers_person_wholeName", "att:name:encryptProp",
                AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 2);
        childMenuItem.setParentId(parentMenuItem.getId());
        authPermissionService.saveItem(childMenuItem);
        childMenuItem = new AuthPermissionItem("AttPhotoEncryptProp", "att_statistical_attPhoto", "att:photo:encryptProp",
                AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 3);
        childMenuItem.setParentId(parentMenuItem.getId());
        authPermissionService.saveItem(childMenuItem);
    }

    /**
     * 初始化参数
     */
    public void initAttParams() {
        /* ------------- 一、考勤规则-基础规则设置-------------*/
        baseSysParamService
            .initData(new BaseSysParamItem("att.baseRule.signIn", "0", I18nUtil.i18nCode("att_rule_baseRuleSignIn")));// 上班签到取卡记录原则
        baseSysParamService
            .initData(new BaseSysParamItem("att.baseRule.signOut", "0", I18nUtil.i18nCode("att_rule_baseRuleSignOut")));// "下班签退取卡记录原则"
        baseSysParamService.initData(new BaseSysParamItem("att.baseRule.shortestMinutes", "120",
            I18nUtil.i18nCode("att_rule_baseRuleShortestMinutes")));// "最短的考勤时段应大于（分钟）"
        baseSysParamService.initData(new BaseSysParamItem("att.baseRule.longestMinutes", "600",
            I18nUtil.i18nCode("att_rule_baseRuleLongestMinutes")));// "最长的考勤时段应小于（分钟）"
        // baseSysParamService.initData(new BaseSysParamItem("att.baseRule.shortestOvertimeMinutes", "5",
        // I18nUtil.i18nCode("att_rule_baseRuleShortestOvertimeMinutes")));// "最短的加班时长应大于（分钟）"
        // baseSysParamService.initData(new BaseSysParamItem("att.baseRule.maxOvertimeType", "notLimited",
        // "Maximum overtime type (this week/this month)"));// "加班时长限制类型：不限制/本周/本月"
        // baseSysParamService.initData(new BaseSysParamItem("att.baseRule.maxOvertimeMinutes", "0",
        // I18nUtil.i18nCode("att_rule_baseRuleMaxOvertimeMinutes")));// "本周/本月最大加班时长（分钟）"
        baseSysParamService.initData(
            new BaseSysParamItem("att.baseRule.elasticCal", "0", I18nUtil.i18nCode("att_rule_baseRuleElasticCal")));// "弹性时长计算方式"
        baseSysParamService.initData(new BaseSysParamItem("att.baseRule.lateAndEarly", "false",
            I18nUtil.i18nCode("att_rule_baseRuleLateAndEarly")));// "迟到且早退算旷工"
        baseSysParamService.initData(new BaseSysParamItem("att.baseRule.countOvertime", "true",
            I18nUtil.i18nCode("att_rule_baseRuleCountOvertime")));// "是否统计加班"
        // baseSysParamService.initData(new BaseSysParamItem("att.baseRule.findSchSort", "1",
        // I18nUtil.i18nCode("att_rule_baseRuleFindSchSort")));//"查找排班记录顺序"
        baseSysParamService.initData(new BaseSysParamItem("att.baseRule.smartFindClass", "1",
            I18nUtil.i18nCode("att_rule_baseRuleSmartFindClass")));// "智能找班原则"
        baseSysParamService.initData(
            new BaseSysParamItem("att.baseRule.crossDay", "0", I18nUtil.i18nCode("att_rule_baseRuleCrossDay")));// "班次时间段跨天时，考勤计算结果"
        // baseSysParamService.initData(new BaseSysParamItem("att.baseRule.signBreakTime", "true",
        // I18nUtil.i18nCode("att_rule_baseRuleSignBreakTime")));// 休息时段是否打卡
        // 海外新需求-未签到未签退考勤规则 by ljf 2019/12/18
        baseSysParamService.initData(new BaseSysParamItem("att.baseRule.noSignInCountType", "absent",
            I18nUtil.i18nCode("att_rule_noSignInCountType")));
        baseSysParamService.initData(new BaseSysParamItem("att.baseRule.noSignInCountLateMinute", "0",
            I18nUtil.i18nCode("att_rule_noSignInCountLateMinute")));
        baseSysParamService.initData(new BaseSysParamItem("att.baseRule.noSignOffCountType", "absent",
            I18nUtil.i18nCode("att_rule_noSignOffCountType")));
        baseSysParamService.initData(new BaseSysParamItem("att.baseRule.noSignOffCountEarlyMinute", "0",
            I18nUtil.i18nCode("att_rule_noSignOffCountEarlyMinute")));

        // "小数点精确位数"
        baseSysParamService.initData(
            new BaseSysParamItem("att.countConvert.decimal", "1", I18nUtil.i18nCode("att_rule_countConvertDecimal")));

        // 短消息uid
        baseSysParamService.initData(new BaseSysParamItem("att.sms.uid", "0", "sms uid"));

        // 考勤参数-报表设置
        baseSysParamService.initData(new BaseSysParamItem("att.reportSetting.shortDateFormat", "yyyy/MM/DD", "日期格式"));
        baseSysParamService.initData(new BaseSysParamItem("att.reportSetting.shortTimeFormat", "HH:mm", "时间格式"));

        /**
         * 地图参数-api设置
         */
        // 默认高德 谷歌地图 0 高德地图 1
        baseSysParamService
            .initData(new BaseSysParamItem("att.map.mapName", "gaodeMap", I18nUtil.i18nCode("att_h5_defaultMap")));
       /* baseSysParamService.initData(new BaseSysParamItem("att.map.distanceMatrixApi",
            "https://restapi.amap.com/v3/distance?origins=%s&destination=%s&type=0&key=", "根据经纬度算距离"));
        baseSysParamService.initData(new BaseSysParamItem("att.map.placeApi",
            "https://restapi.amap.com/v3/geocode/regeo?output=json&location=%s&key=", "根据经纬度获取地址"));
        baseSysParamService
            .initData(new BaseSysParamItem("att.map.mapKey", "4aeb1569617bc236f832a9ed0d6c3398", "云平台KEY"));*/
        // 谷歌
         /*baseSysParamService
            .initData(new BaseSysParamItem("att.map.googleMap", "googleMap", I18nUtil.i18nCode("att_h5_googleMap")));
       baseSysParamService.initData(new BaseSysParamItem("att.map.google.distanceMatrixApi",
            "https://maps.google.cn/maps/api/distancematrix/json?origins=%s&destinations=%s&key=", "谷歌api根据经纬度算距离"));
        baseSysParamService.initData(new BaseSysParamItem("att.map.google.placeApi",
            "https://maps.google.cn/maps/api/geocode/json?latlng=%s&key=", "谷歌api根据经纬度获取地址"));*/
        baseSysParamService.initData(
            new BaseSysParamItem("att.map.google.mapKey", "AIzaSyBOP5cLG70Jd0tC0j4sxCwCB3JaBGlsq40", "谷歌云平台KEY"));
        // 高德
        /*baseSysParamService
            .initData(new BaseSysParamItem("att.map.gaodeMap", "gaodeMap", I18nUtil.i18nCode("att_h5_gaodeMap")));
        baseSysParamService.initData(new BaseSysParamItem("att.map.gaode.distanceMatrixApi",
            "https://restapi.amap.com/v3/distance?origins=%s&destination=%s&type=0&key=", "高德api根据经纬度算距离"));
        baseSysParamService.initData(new BaseSysParamItem("att.map.gaode.placeApi",
            "https://restapi.amap.com/v3/geocode/regeo?output=json&location=%s&key=", "高德api根据经纬度获取地址"));
        baseSysParamService.initData(
            new BaseSysParamItem("att.map.gaode.mapKey", "4aeb1569617bc236f832a9ed0d6c3398", "高德云平台JAVA-KEY"));*/
        baseSysParamService.initData(
            new BaseSysParamItem("att.map.gaode.mapJsKey", "042328d3d9dace44bbf87030e4cd9ccc", "高德云平台JS-KEY"));
        // 考勤h5 二维码 url
        // baseSysParamService.initData(new BaseSysParamItem("att.url.qrcodeUrl", "", "二维码url"));

        // 授权设备 0启用 1禁用 默认启用
        baseSysParamService.initData(new BaseSysParamItem("att.device.autoAdd", "1", "新增设备自动添加"));
        baseSysParamService.initData(new BaseSysParamItem("att.device.receivePersonOnlyDb", "1", "仅接收数据库中存在的人员数据"));

        // 是否启用实时点名设置（0启用、1禁用、默认禁用）
        baseSysParamService.initData(new BaseSysParamItem("att.realTime.rollCall", "1", "实时点名"));

        // 自助登录入口设置（0启用、1禁用、默认为启用）
        String enableEmpLogin = "0";
        if (BaseConstants.ZKBIO_ACCESS.equals(productCode)) {
            enableEmpLogin = "1";
        }
        baseSysParamService.initData(new BaseSysParamItem("att.self.loginEntrance", enableEmpLogin, "员工自助登录入口"));

        // 考勤事件和记录清理参数
        JSONObject values = new JSONObject();
        values.put("keptMonth", "15");
        values.put("runtime", "01:00:00");
        values.put("keptType", BaseDataConstants.DATA_CLEAN_KEPTMONTH);
        values.put("keptPhoto", "15");
        values.put("keptPhotoType", BaseDataConstants.DATA_CLEAN_KEPTMONTH);
        BaseSysParamItem item =
            new BaseSysParamItem("attReportDataClean", values.toString(), "base_dataClean_attTrans");
        baseSysParamService.initData(item);

        // 考勤实时计算相关参数
        baseSysParamService.initData(new BaseSysParamItem("att.realTime.enable", "0", "是否启实时计算（0启用、1禁用、默认启用）"));
        baseSysParamService.initData(new BaseSysParamItem("att.realTime.calculate", "0 0/2 * * * ?", "考勤实时计算频率"));
        baseSysParamService.initData(new BaseSysParamItem("att.realTime.updateSchInfo", "0 0 1 * * ?", "每天定时更新排班信息"));
        Calendar defaultDate = Calendar.getInstance();
        defaultDate.set(Calendar.HOUR_OF_DAY, 2);
        defaultDate.set(Calendar.MINUTE, 0);
        defaultDate.set(Calendar.SECOND, 0);
        defaultDate.set(Calendar.MILLISECOND, 0);
        baseSysParamService.initData(new BaseSysParamItem("att.realTime.calculateAllTime",
            DateUtil.dateToString(defaultDate.getTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS), "计算所有人员的时间"));

        // 考勤日打卡详情报表统计时间间隔
        baseSysParamService
            .initData(new BaseSysParamItem("att.report.updateDayCardDetail", "0 */10 * * * ?", "每10分钟统计一次"));

        // 考勤点定时获取记录频率
        baseSysParamService.initData(new BaseSysParamItem("att.point.pullTransaction", "0 0/5 * * * ?", "考勤点定时获取记录频率"));

        /* ------------- 年假结余设置 -------------*/
        baseSysParamService
            .initData(new BaseSysParamItem("att.annualLeave.enable", "0", "是否启实年假（0启用、1禁用（空也是禁用）、默认启用）"));
        // 年假清零发放日期
        baseSysParamService.initData(new BaseSysParamItem("att.annualLeave.calculateDate", "", "年假清零发放日期"));
        // 年假清零发放计划月份
        baseSysParamService.initData(new BaseSysParamItem("att.annualLeave.calculateMonth", "1", "年假清零发放月份"));
        // 年假清零计划日
        baseSysParamService.initData(new BaseSysParamItem("att.annualLeave.calculateDay", "1", "年假清零清零发放日"));
        // 按工龄月份比例时长计算方式（abort=向下（舍弃）,rounding=四舍五入,carry=向上（进位））
        baseSysParamService.initData(new BaseSysParamItem("att.annualLeave.calculateType", "rounding",
            "按工龄月份比例时长计算方式（abort=向下（舍弃）,rounding=四舍五入,carry=向上（进位））"));
        // 年假时长规则,根式1-5=5表示工龄1到5年有5天年假,（开始工龄-结束工龄-年假天数）
        baseSysParamService
            .initData(new BaseSysParamItem("att.annualLeave.rule", "0-1-0,1-10-5,10-20-10,20-999-15", "年假时长规则"));
        // 是否启用按工龄月份比例时长计算（true启用、false禁用）
        baseSysParamService
            .initData(new BaseSysParamItem("att.annualLeave.enableCalculateType", "false", "是否启用按工龄月份比例时长计算"));

        // 初始化消息推送参数
        JSONObject notificationValues = new JSONObject();
        notificationValues.put("Email", "1");
        notificationValues.put("SMS", "1");
        notificationValues.put("Whatsapp", "1");
        notificationValues.put("Line", "1");
        BaseSysParamItem attMessageNotification =
            new BaseSysParamItem("attMessageNotification", notificationValues.toString(), "base_dataClean_attTrans");
        baseSysParamService.initData(attMessageNotification);

        // 初始化打卡状态参数，数据格式：快捷键IDkeyId,考勤状态statusCode,状态名称showName
        baseSysParamService.initData(
            new BaseSysParamItem("att.cardStatus.f1", "1,0," + I18nUtil.i18nCode("att_cardStatus_signIn"), "签到"));
        baseSysParamService.initData(
            new BaseSysParamItem("att.cardStatus.f2", "2,1," + I18nUtil.i18nCode("att_cardStatus_signOut"), "签退"));
        baseSysParamService.initData(
            new BaseSysParamItem("att.cardStatus.f3", "3,2," + I18nUtil.i18nCode("att_cardStatus_out"), "外出"));
        baseSysParamService.initData(
            new BaseSysParamItem("att.cardStatus.f4", "4,3," + I18nUtil.i18nCode("att_cardStatus_outReturn"), "外出返回"));
        baseSysParamService.initData(new BaseSysParamItem("att.cardStatus.f5",
            "5,4," + I18nUtil.i18nCode("att_cardStatus_overtime_signIn"), "加班签到"));
        baseSysParamService.initData(new BaseSysParamItem("att.cardStatus.f6",
            "6,5," + I18nUtil.i18nCode("att_cardStatus_overtime_signOut"), "加班签退"));

        // 是否启用加班等级计算（0启用、1禁用、默认为禁用）
        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.enable", "1", "是否启用加班等级计算"));

        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.normalOT1", "0-1", "平时加班OT1"));
        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.normalOT2", "1-2", "平时加班OT2"));
        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.normalOT3", "2-3", "平时加班OT3"));

        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.restOT1", "0-4", "休息日加班OT1"));
        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.restOT2", "4-6", "休息日加班OT2"));
        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.restOT3", "6-8", "休息日加班OT3"));

        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.holidayOT1", "0-4", "节假日加班OT1"));
        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.holidayOT2", "4-6", "节假日加班OT2"));
        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.holidayOT3", "6-8", "节假日加班OT3"));
    }

    private void initAttLeaveType() {

        // 旧数据长度为10，兼容旧数据升级，这里统一更新为50长度
        jdbcOperateTemplate.alterTableCharLen("ATT_LEAVETYPE", "LEAVETYPE_NO", "50");

        List<AttLeaveTypeItem> attLeaveTypeItemList = new ArrayList<>();
        AttLeaveTypeItem leaveThing = new AttLeaveTypeItem("L1", I18nUtil.i18nCode("att_leaveType_leaveThing"), true,
            true, new Double(0.5), AttConstant.ATT_CONVERT_UNIT_HOUR, AttConstant.ATT_CONVERT_ROUNDING,
            I18nUtil.i18nCode("att_other_leaveThing"), AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        leaveThing.setSortNo(1);
        leaveThing.setEnableMaxDays(false);
        leaveThing.setMaxDays(20);
        leaveThing.setColor("#000000");
        attLeaveTypeItemList.add(leaveThing);

        AttLeaveTypeItem leaveAnnual = new AttLeaveTypeItem("L5", I18nUtil.i18nCode("att_leaveType_leaveAnnual"), false,
            true, new Double(0.5), AttConstant.ATT_CONVERT_UNIT_DAY, AttConstant.ATT_CONVERT_ROUNDING,
            I18nUtil.i18nCode("att_other_leaveAnnual"), AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        leaveAnnual.setSortNo(2);
        leaveAnnual.setEnableMaxDays(false);
        leaveAnnual.setMaxDays(20);
        leaveAnnual.setColor("#000000");
        attLeaveTypeItemList.add(leaveAnnual);

        AttLeaveTypeItem leaveSick = new AttLeaveTypeItem("L4", I18nUtil.i18nCode("att_leaveType_leaveSick"), true,
            true, new Double(1), AttConstant.ATT_CONVERT_UNIT_HOUR, AttConstant.ATT_CONVERT_CARRY,
            I18nUtil.i18nCode("att_other_leaveSick"), AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        leaveSick.setSortNo(3);
        leaveSick.setEnableMaxDays(false);
        leaveSick.setMaxDays(20);
        leaveSick.setColor("#000000");
        attLeaveTypeItemList.add(leaveSick);

        AttLeaveTypeItem leaveMarriage = new AttLeaveTypeItem("L2", I18nUtil.i18nCode("att_leaveType_leaveMarriage"),
            false, true, new Double(1), AttConstant.ATT_CONVERT_UNIT_DAY, AttConstant.ATT_CONVERT_CARRY,
            I18nUtil.i18nCode("att_other_leaveMarriage"), AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        leaveMarriage.setSortNo(4);
        leaveMarriage.setEnableMaxDays(false);
        leaveMarriage.setMaxDays(20);
        leaveMarriage.setColor("#000000");
        attLeaveTypeItemList.add(leaveMarriage);

        AttLeaveTypeItem leaveBirth = new AttLeaveTypeItem("L3", I18nUtil.i18nCode("att_leaveType_leaveBirth"), false,
            true, new Double(1), AttConstant.ATT_CONVERT_UNIT_DAY, AttConstant.ATT_CONVERT_CARRY,
            I18nUtil.i18nCode("att_other_leaveBirth"), AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        leaveBirth.setSortNo(5);
        leaveBirth.setEnableMaxDays(false);
        leaveBirth.setMaxDays(150);
        leaveBirth.setColor("#000000");
        attLeaveTypeItemList.add(leaveBirth);

        AttLeaveTypeItem leaveNursing = new AttLeaveTypeItem("L8", I18nUtil.i18nCode("att_leaveType_leaveNursing"),
            false, true, new Double(1), AttConstant.ATT_CONVERT_UNIT_HOUR, AttConstant.ATT_CONVERT_ROUNDING,
            I18nUtil.i18nCode("att_other_leaveNursing"), AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        leaveNursing.setSortNo(6);
        leaveNursing.setEnableMaxDays(false);
        leaveNursing.setMaxDays(180);
        leaveNursing.setColor("#000000");
        attLeaveTypeItemList.add(leaveNursing);

        AttLeaveTypeItem leaveHome = new AttLeaveTypeItem("L7", I18nUtil.i18nCode("att_leaveType_leaveHome"), false,
            true, new Double(1), AttConstant.ATT_CONVERT_UNIT_DAY, AttConstant.ATT_CONVERT_ROUNDING,
            I18nUtil.i18nCode("att_other_leaveHome"), AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        leaveHome.setSortNo(7);
        leaveHome.setEnableMaxDays(false);
        leaveHome.setMaxDays(20);
        leaveHome.setColor("#000000");
        attLeaveTypeItemList.add(leaveHome);

        AttLeaveTypeItem leaveFuneral = new AttLeaveTypeItem("L6", I18nUtil.i18nCode("att_leaveType_leaveFuneral"),
            false, true, new Double(1), AttConstant.ATT_CONVERT_UNIT_DAY, AttConstant.ATT_CONVERT_CARRY,
            I18nUtil.i18nCode("att_other_leaveFuneral"), AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        leaveFuneral.setSortNo(8);
        leaveFuneral.setEnableMaxDays(false);
        leaveFuneral.setMaxDays(20);
        leaveFuneral.setColor("#000000");
        attLeaveTypeItemList.add(leaveFuneral);

        AttLeaveTypeItem trip =
            new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.TRIP, I18nUtil.i18nCode("att_leftMenu_trip"),
                false, true, new Double(0.5), AttConstant.ATT_CONVERT_UNIT_DAY, AttConstant.ATT_CONVERT_ROUNDING,
                I18nUtil.i18nCode("att_other_leavetrip"), AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        trip.setSortNo(9);
        trip.setEnableMaxDays(false);
        trip.setMaxDays(20);
        trip.setColor("#000000");
        attLeaveTypeItemList.add(trip);

        AttLeaveTypeItem out =
            new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.OUT, I18nUtil.i18nCode("att_leftMenu_out"),
                false, true, new Double(0.5), AttConstant.ATT_CONVERT_UNIT_HOUR, AttConstant.ATT_CONVERT_ROUNDING,
                I18nUtil.i18nCode("att_other_leaveout"), AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        out.setSortNo(10);
        out.setEnableMaxDays(false);
        out.setMaxDays(20);
        out.setColor("#000000");
        attLeaveTypeItemList.add(out);

        // 应到/实到-√
        AttLeaveTypeItem actual = new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.ACTUAL,
            I18nUtil.i18nCode("att_rule_arrive"), false, true, new Double(1), AttConstant.ATT_CONVERT_UNIT_MINUTE,
            AttConstant.ATT_CONVERT_CARRY, I18nUtil.i18nCode("att_other_arrive"), AttConstant.ATT_CONVERT_MARK_PARAMS);
        actual.setColor("#000000");
        attLeaveTypeItemList.add(actual);
        // 早退-早
        AttLeaveTypeItem early = new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.EARLY,
            I18nUtil.i18nCode("att_common_early"), false, true, new Double(1), AttConstant.ATT_CONVERT_UNIT_MINUTE,
            AttConstant.ATT_CONVERT_CARRY, I18nUtil.i18nCode("att_other_early"), AttConstant.ATT_CONVERT_MARK_PARAMS);
        early.setColor("#000000");
        attLeaveTypeItemList.add(early);
        // 迟到-迟
        AttLeaveTypeItem late = new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.LATE,
            I18nUtil.i18nCode("att_common_late"), false, true, new Double(1), AttConstant.ATT_CONVERT_UNIT_MINUTE,
            AttConstant.ATT_CONVERT_CARRY, I18nUtil.i18nCode("att_other_late"), AttConstant.ATT_CONVERT_MARK_PARAMS);
        late.setColor("#000000");
        attLeaveTypeItemList.add(late);
        // 未签退-缺]
        AttLeaveTypeItem noCheckOut = new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.NO_CHECK_OUT,
            I18nUtil.i18nCode("att_rule_noSignOff"), false, true, new Double(-1), AttConstant.ATT_CONVERT_UNIT_HOUR,
            AttConstant.ATT_CONVERT_CARRY, I18nUtil.i18nCode("att_other_noSignOff"),
            AttConstant.ATT_CONVERT_MARK_PARAMS);
        noCheckOut.setColor("#000000");
        attLeaveTypeItemList.add(noCheckOut);
        // 未签到-[缺
        AttLeaveTypeItem noCheckIn = new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.NO_CHECK_IN,
            I18nUtil.i18nCode("att_rule_noSignIn"), false, true, new Double(-1), AttConstant.ATT_CONVERT_UNIT_HOUR,
            AttConstant.ATT_CONVERT_CARRY, I18nUtil.i18nCode("att_other_noSignIn"),
            AttConstant.ATT_CONVERT_MARK_PARAMS);
        noCheckIn.setColor("#000000");
        attLeaveTypeItemList.add(noCheckIn);
        // 加班-加
        AttLeaveTypeItem overtime = new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.OVERTIME,
            I18nUtil.i18nCode("att_common_overtime"), false, true, new Double(0.5), AttConstant.ATT_CONVERT_UNIT_HOUR,
            AttConstant.ATT_CONVERT_CARRY, I18nUtil.i18nCode("att_other_overtime"),
            AttConstant.ATT_CONVERT_MARK_PARAMS);
        overtime.setColor("#000000");
        attLeaveTypeItemList.add(overtime);
        // 旷工-旷
        AttLeaveTypeItem absent = new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.ABSENT,
            I18nUtil.i18nCode("att_common_absent"), false, true, new Double(0.5), AttConstant.ATT_CONVERT_UNIT_HOUR,
            AttConstant.ATT_CONVERT_CARRY, I18nUtil.i18nCode("att_other_absent"), AttConstant.ATT_CONVERT_MARK_PARAMS);
        absent.setColor("#000000");
        attLeaveTypeItemList.add(absent);
        // 排班且休息-休
        AttLeaveTypeItem schAndRest = new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.SCHEDULING_AND_REST,
            I18nUtil.i18nCode("att_common_schAndRest"), false, true, new Double(-1), AttConstant.ATT_CONVERT_UNIT_HOUR,
            AttConstant.ATT_CONVERT_CARRY, I18nUtil.i18nCode("att_schedule_off"), AttConstant.ATT_CONVERT_MARK_PARAMS);
        schAndRest.setColor("#000000");
        attLeaveTypeItemList.add(schAndRest);
        // 未排班-未
        AttLeaveTypeItem noSch = new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.NO_SCHEDULING,
            I18nUtil.i18nCode("att_schedule_noSchDetail"), false, true, new Double(-1),
            AttConstant.ATT_CONVERT_UNIT_HOUR, AttConstant.ATT_CONVERT_CARRY,
            I18nUtil.i18nCode("att_statistical_remarkNoSch"), AttConstant.ATT_CONVERT_MARK_PARAMS);
        noSch.setColor("#000000");
        attLeaveTypeItemList.add(noSch);
        // 调休-调休
        AttLeaveTypeItem tuneOff = new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.TUNE_OFF,
            I18nUtil.i18nCode("att_rule_off"), false, true, new Double(-1), AttConstant.ATT_CONVERT_UNIT_HOUR,
            AttConstant.ATT_CONVERT_CARRY, I18nUtil.i18nCode("att_other_off"), AttConstant.ATT_CONVERT_MARK_PARAMS);
        tuneOff.setColor("#000000");
        attLeaveTypeItemList.add(tuneOff);
        // 调班-调
        AttLeaveTypeItem attClass = new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.CLASS,
            I18nUtil.i18nCode("att_schedule_classDetail"), false, true, new Double(-1),
            AttConstant.ATT_CONVERT_UNIT_HOUR, AttConstant.ATT_CONVERT_CARRY, I18nUtil.i18nCode("att_schedule_class"),
            AttConstant.ATT_CONVERT_MARK_PARAMS);
        attClass.setColor("#000000");
        attLeaveTypeItemList.add(attClass);

        // 节假日-节
        AttLeaveTypeItem holiday = new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.HOLIDAY,
            I18nUtil.i18nCode("att_leftMenu_holiday"), false, true, new Double(-1), AttConstant.ATT_CONVERT_UNIT_HOUR,
            AttConstant.ATT_CONVERT_CARRY, I18nUtil.i18nCode("att_statistical_remarkHoliday"),
            AttConstant.ATT_CONVERT_MARK_PARAMS);
        holiday.setColor("#000000");
        attLeaveTypeItemList.add(holiday);
        // 未签到不完整-缺
        AttLeaveTypeItem noCheckInInc =
            new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.NO_CHECK_IN_INCOMPLETE,
                I18nUtil.i18nCode("att_rule_noCheckInIncomplete"), false, true, new Double(-1),
                AttConstant.ATT_CONVERT_UNIT_HOUR, AttConstant.ATT_CONVERT_CARRY,
                I18nUtil.i18nCode("att_other_incomplete"), AttConstant.ATT_CONVERT_MARK_PARAMS);
        noCheckInInc.setColor("#000000");
        attLeaveTypeItemList.add(noCheckInInc);
        // 未签退不完整-缺
        AttLeaveTypeItem noCheckOutInc =
            new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.NO_CHECK_OUT_INCOMPLETE,
                I18nUtil.i18nCode("att_rule_noCheckOutIncomplete"), false, true, new Double(-1),
                AttConstant.ATT_CONVERT_UNIT_HOUR, AttConstant.ATT_CONVERT_CARRY,
                I18nUtil.i18nCode("att_other_outcomplete"), AttConstant.ATT_CONVERT_MARK_PARAMS);
        noCheckOutInc.setColor("#000000");
        attLeaveTypeItemList.add(noCheckOutInc);

        for (AttLeaveTypeItem attLeaveTypeItem : attLeaveTypeItemList) {
            attLeaveTypeService.initData(attLeaveTypeItem);
        }
    }

    // 字典初始化
    private void initAttDictionary() {
        // 班次工作类型(正常工作/周末加班/节假日加班)
        BaseDictionaryItem baseDictionaryItem =
            new BaseDictionaryItem("AttShiftWorkType", "att_shift_workType", false, "att_module");
        baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("normalWork", "att_shift_normalWork", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("weekendOt", "att_overtime_rest", 1, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("holidayOt", "att_shift_holidayOt", 2, baseDictionaryItem.getId()));

        // 验证方式
        baseDictionaryItem = new BaseDictionaryItem("AttVerifyMode", "common_verifyMode_entiy", false, "att_module");
        baseDictionaryService.initData(baseDictionaryItem);
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("0", "att_verifyMode_0", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("1", "att_verifyMode_1", 1, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("2", "att_verifyMode_2", 2, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("3", "att_verifyMode_3", 3, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("4", "att_verifyMode_4", 4, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("5", "att_verifyMode_5", 5, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("6", "att_verifyMode_6", 6, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("7", "att_verifyMode_7", 7, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("8", "att_verifyMode_8", 8, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("9", "att_verifyMode_9", 9, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("10", "att_verifyMode_10", 10, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("11", "att_verifyMode_11", 11, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("12", "att_verifyMode_12", 12, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("13", "att_verifyMode_13", 13, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("14", "att_verifyMode_14", 14, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("15", "att_verifyMode_15", 15, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("16", "att_verifyMode_16", 16, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("17", "att_verifyMode_17", 17, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("18", "att_verifyMode_18", 18, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("19", "att_verifyMode_19", 19, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("20", "att_verifyMode_20", 20, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("21", "att_verifyMode_21", 21, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("22", "att_verifyMode_22", 22, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("23", "att_verifyMode_23", 23, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("24", "att_verifyMode_24", 24, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("25", "att_verifyMode_25", 25, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("26", "att_verifyMode_26", 26, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("27", "att_verifyMode_27", 27, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("28", "att_verifyMode_28", 28, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("29", "att_verifyMode_29", 29, baseDictionaryItem.getId()));
    }

    /**
     * 初始化模块版本信息
     */
    private void initUpgradeVersion() {
        String curVersion = VersionUtil.getReleaseGitTags(BaseConstants.ATT);
        // 快照版取不到tag name 不执行需要执行以下代码
        if (StringUtils.isNotBlank(curVersion)) {
            BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("AttUpgradeVersion");
            if (StringUtils.isBlank(baseSysParamItem.getId())) {
                // 没有则初始化版本信息
                baseSysParamItem = new BaseSysParamItem("AttUpgradeVersion", curVersion, "Att Upgrade Version", true);
                baseSysParamService.saveItem(baseSysParamItem);
            }
        }
    }

    /**
     * 初始化管理员app权限
     */
    private void initAppMenus() {

        // 管理员不需要这些权限
        /*AuthPermissionItem appModuleItem = authPermissionService.getItemByCode("App");
        if (null == appModuleItem) {
            // 管理员APP模块
            appModuleItem = new AuthPermissionItem("App", "app_module", "app", AuthContants.RESOURCE_TYPE_APP_SYSTEM,
                    ZKConstant.TRUE, 998);
            authPermissionService.initData(appModuleItem);
        }
        AuthPermissionItem appMenuItem = new AuthPermissionItem("AppAtt", "att_module", "app:APPatt",
                AuthContants.RESOURCE_TYPE_APP_MENU, ZKConstant.TRUE, 7);
        appMenuItem.setParentId(appModuleItem.getId());
        authPermissionService.initData(appMenuItem);

        // 签到
        AuthPermissionItem appButtonItem = new AuthPermissionItem("AppAttSignIn", "att_cardStatus_signIn", "app:APPattSignIn",
                AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 1);
        appButtonItem.setParentId(appMenuItem.getId());
        authPermissionService.initData(appButtonItem);

        // 我的考勤
        appButtonItem = new AuthPermissionItem("AppAttAttendance", "att_app_attendance", "app:APPattAttendance",
                AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 2);
        appButtonItem.setParentId(appMenuItem.getId());
        authPermissionService.initData(appButtonItem);

        // 考勤申请
        appButtonItem = new AuthPermissionItem("AppAttApply", "att_app_apply", "app:APPattApply",
                AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 3);
        appButtonItem.setParentId(appMenuItem.getId());
        authPermissionService.initData(appButtonItem);

        // 考勤申请
        appButtonItem = new AuthPermissionItem("AppAttApprove", "att_app_approve", "app:APPattApprove",
                AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 4);
        appButtonItem.setParentId(appMenuItem.getId());
        authPermissionService.initData(appButtonItem);*/

    }
}