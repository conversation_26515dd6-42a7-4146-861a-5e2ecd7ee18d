<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zkteco</groupId>
        <artifactId>zkbiosecurity-pom</artifactId>
        <version>3.3.0-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zkbiosecurity-att</artifactId>
    <name>${project.artifactId}</name>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <properties>
        <module.name>att</module.name>
        <revision>4.13.0-RELEASE_YFDZ2025071400104</revision>
        <boot.version>3.12.0-RELEASE</boot.version>
        <module.api.version>3.13.0-RELEASE_YFDZ2025071400104</module.api.version>
        <auth.version>3.11.0-RELEASE</auth.version>
        <license.version>3.10.0-RELEASE</license.version>
        <system.version>3.14.0-RELEASE</system.version>
        <pers.version>3.14.0-RELEASE_YFDZ2025071400104</pers.version>
        <adms.version>3.13.0-RELEASE</adms.version>
        <cmd.version>3.10.0-RELEASE</cmd.version>
        <foldex.version>2.5.0-RELEASE</foldex.version>
    </properties>

    <modules>
        <module>zkbiosecurity-att-vo</module>
        <module>zkbiosecurity-att-api</module>
        <module>zkbiosecurity-att-remote</module>
        <module>zkbiosecurity-att-service</module>
        <module>zkbiosecurity-att-web</module>
        <module>zkbiosecurity-att-system</module>
        <module>zkbiosecurity-att-i18n</module>
        <module>zkbiosecurity-att-client</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-base</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-system-api</artifactId>
                <version>${system.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-license-api</artifactId>
                <version>${license.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-auth-api</artifactId>
                <version>${auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-auth-provider</artifactId>
                <version>${auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-pers-api</artifactId>
                <version>${pers.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-adms-api</artifactId>
                <version>${adms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-cmd</artifactId>
                <version>${cmd.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-module-all</artifactId>
                <version>${module.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-scheduler</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-core</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-model</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-redis</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-security</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-web</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <!-- SelectPersContent 使用 -->
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-ui</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-foldex</artifactId>
                <version>${foldex.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <!-- 引入maven release 插件, 用于正式版本的自动发布 -->
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
            </plugin>
            <!--这个插件的作用是，生成一个压缩版的pom.xml文件，然后在install和deploy阶段使用压缩后的pom.xml文件，替换原来的pom.xml文件-->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
            <!--用于生成git.properties文件，给版本升级获取版本源-->
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <!-- gitlab地址, 用于生产环境自动打TAG -->
    <scm>
        <developerConnection>scm:git:*******************************:biosecurity-pro/zkbiosecurity-att.git
        </developerConnection>
        <tag>HEAD</tag>
    </scm>

    <repositories>
        <repository>
            <id>zkteco-internal-repository-releases</id>
            <name>zkteco-internal-repository-releases</name>
            <url>http://192.168.200.31:8080/artifactory/zkteco-internal-repository</url>
        </repository>
        <repository>
            <id>zkteco-internal-repository-snapshots</id>
            <name>zkteco-internal-repository-snapshots</name>
            <url>http://192.168.200.31:8080/artifactory/zkteco-internal-repository</url>
        </repository>
    </repositories>
</project>