.attSchTip {
    clear: both;
    font-size: 12px !important;
    left: auto !important;
    right: 25px;
    width: 400px;
    line-height: 20px !important;
}

.attCommonTipClass {
    position: absolute;
    left: 18px;
    top: 18px;
    height: auto;
    background-color: #f3f5f0;
    z-index: 10;
    padding: 10px;
    border: 2px solid #dcdfe2;
    text-align: left;
    border-radius: 5px;
    visibility: hidden;
    font-size: 12px !important;
    line-height: 20px !important;
    width: 300px;
}

.attCommonTipClassTop {
    top: auto;
    bottom: 18px;
}

.attCommonTipClassTopLeft {
    top: auto;
    bottom: 18px;
    right: 18px;
    left: auto;
}

.attCommonTipClassLeft {
    right: 18px;
    left: auto;
}

.attCommonTipClass .title {
    font-weight: 600;
}

.attCommonTipClass .content {
    padding-left: 20px
}

.attCommonTipClass400 {
    width: 400px;
}

.attCommonTipClass300 {
    width: 300px;
}

.attSchBoxDiv {
    width: 100%;
    position: relative;
    overflow: hidden;
    height: 95%;
}


.attTooltip {
    position: relative;
    display: inline-block;
    height: 22px;
    vertical-align: bottom;
}

.att_icv {
    font-size: 15px;
    cursor: pointer;
    margin: 0px 5px;
    vertical-align: text-top;
    display: inline-block;
}

.att_icv-green {
    color: #7ac143;
}

.attTooltip:hover .attCommonTipClass {
    visibility: visible;
}

.dhx_left_tree_box {
    height: 100%;
    width: 100%;
    position: relative;
}

.dhx_left_tree_box .dhx_tree_box,
.dhx_left_tree_box .dhx_tree_box > div {
    height: 100%;
    width: 100%;
    position: relative;
}

.select_tree_box {
    overflow: auto !important;
    position: absolute;
    width: 100%;
    top: 30px;
    bottom: 0px;
}

.select_tree_top_box {
    display: flex;
    align-items: center;
    width: 100%;
    position: absolute;
    top: 0px;
    height: 30px;
}

.select_tree_top_box .plus_minus_img {
    margin-top: 0px;
}

.attSelectPersonDiv {
    height: 400px;
    margin-bottom: 20px;
    overflow: hidden;
}

.attFlowableSpanImage {
    color: #bdc0ba;
    font-size: 17px;
    display: inline-block;
    margin-top: 35px;
    width: 100%;
    text-align: center;
}

.attFlowableLeaveImagePreview {
    vertical-align: middle!important;
    width: 290px;
    height: 104px;
    overflow-y: auto;
    border: 1px dashed #aeb4b8 !important;
    box-sizing: border-box;
}

.attFlowableImageDiv {
    display: inline-block;
    width: 80px;
    height: 90px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    background: #f8f8f8;
    position: relative;
    overflow: hidden;
    margin: 4px;
    cursor: pointer;
    vertical-align: top;
}

.attFlowableDivCancel {
    position: absolute;
    background: url('images/attDivCancel.png') no-repeat;
    width: 20px;
    height: 20px;
    top: 4px;
    right: 0;
    z-index: 1;
    cursor: pointer;
}