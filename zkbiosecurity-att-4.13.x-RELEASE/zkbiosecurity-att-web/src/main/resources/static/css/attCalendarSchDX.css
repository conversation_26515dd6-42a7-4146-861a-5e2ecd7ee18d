.attTimeSlotForm {
    height: 300px;
    overflow-y: auto;
    border-bottom: 1px solid #c3c2c2;
}

.attTimeSlotForm_sch {
    border-bottom: 1px solid #eee;
    padding: 8px 0px;
    cursor: pointer;
    display: block;
}

.attTimeSlotForm_checkbox {
    display: inline-block;
    vertical-align: sub;
    font-size: 20px;
    width: 15px;
    height: 15px;
    margin-right: 3px;
    cursor: pointer;
}

.dhx_cal_lsection {
    display: none;
}

.attTimeSlotForm_hidden {
    padding: 0px !important;
    height: 0px !important;
    width: 0px !important;
    border: none !important;
    position: absolute;
}

.attTimeSlotTree {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    background-color: #f3f5f0;
}

.attTimeSlotTree .standartTreeImage {
    display: none;
}

.attTimeSlotTree table:nth-of-type(1) td:nth-of-type(2) {
    border-bottom: 1px solid #ededed;
}

.attTimeSlotTree .standartTreeRow {
    padding: 8px;
}

.attTimeSlotTree span.selectedTreeRow {
    background-color: transparent !important;
}

.attTimeSlotTree .standartTreeRow:hover {
    background-color: #f1f9ec;
}

.attTimeSlotTree .containerTableStyle table tr:hover {
    background-color: #f1f9ec;
}

.attTooltipDX {
    float: left;
    position: absolute;
    z-index: 99;
    margin: 22px 0px 0px 17px;
}


.attTooltipDX .attSchTipDX {
    width: 460px;
}

.attTPersonSchOpTip {
    padding-left: 5px;
    display: flex;
    display: -webkit-flex;
    align-items: center;
    height: 100%;
}

.dhx_cal_data td {
    background-color: #fff;
}

.dhx_cal_data td:hover {
    background-color: #f7f7f7;
}