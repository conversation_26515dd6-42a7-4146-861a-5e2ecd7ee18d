* {
    margin: 0;
    padding: 0;
}

.dateBox {
    width: 100%;
    margin: 0px auto;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

.nowDay {
    height: 35px;
    line-height: 35px;
    font-size: 18px;
    color: #3f3f3f;
    text-align: center
}

.nowDay span {
    display: inline-block;
    cursor: pointer;
}

.nowDay span:nth-child(2) {
    width: 120px;
    text-align: center;
}

.weekBox ul:after, .monthBox:after {
    content: "";
    display: block;
    clear: both;
}

.weekBox ul li {
    list-style: none;
    width: 101px;
    text-align: center;
    float: left;
    margin: 5px 3px;
    font-weight: bold;
    font-size: 16px;
    cursor: pointer;
}

.monthBox {
    width: 773px;
    height: 528px;
    position: relative;
    text-align: center;
    margin-top: 2px;
}

.monthBox #attMoveSelected {
    position: absolute;
    background-color: #333;
    opacity: 0.3;
    top: 0;
    left: 0;
    z-index: 10;
}

.monthBox .selected {
    background-color: #dcdfe2;
}

.monthBox ul li {
    list-style: none;
    width: 108px;
    height: 89px;
    text-align: right;
    display: inline-block;
    border: 1px solid;
    box-sizing: border-box;
    vertical-align: bottom;
    margin-left: -1px;
    margin-top: -1px;
    /*position: relative;*/
}

.monthBox ul .attCalendarLi {
    z-index: 2;
}


.monthBox ul li.other {
    z-index: 1;
    color: #929292
}

.monthBox ul li.today {
    color: #fff;
}

.schBox {
    margin: 0 auto;
    width: 100%;
    height: 100%;
    overflow: hidden;
    cursor: pointer;
}

.schBox .day {
    display: inline-block;
    text-align: right;
    font-size: 16px;
    font-weight: bold;
    color: #33A6B8;
    padding: 2px 2px 0px 0px;
    vertical-align: top;
}

.schBox .schDateBox {
    height: 66px;
    overflow-y: auto;
    overflow-x: hidden;
    -ms-overflow-style: none;
}

/*滚动条整体部分,必须要设置*/
.schBox .schDateBox::-webkit-scrollbar {
    width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;;
}

/*滚动条的轨道*/
.schBox .schDateBox::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    background: #EDEDED;
}

/*滚动条的滑块按钮*/
.schBox .schDateBox::-webkit-scrollbar-thumb {
    border-radius: 3px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #999;
}

/*滚动条整体部分,必须要设置*/
.schBox .schDateBox .-o-scrollbar {
    width: 5px; /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;;
}

/*滚动条的轨道*/
.schBox .schDateBox::-o-scrollbar-track {
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    background: #EDEDED;
}

/*滚动条的滑块按钮*/
.schBox .schDateBox::-o-scrollbar-thumb {
    border-radius: 3px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: #999;
}

.schBox .sch {
    display: block;
    margin: 2px;
    width: 100px;
    height: 22px;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    overflow: hidden;
    text-align: center;
    line-height: 22px;
    border-radius: 5px;
    border: 1px solid transparent;
    cursor: pointer;
    position: relative;
}

.schBox .sch .attTimeSlotDel {
    position: absolute;
    display: none;
    width: 18px;
    height: 18px;
    background: #f3f5f0;
    right: 1px;
    padding: 2px;
}

.schBox .sch:hover .attTimeSlotDel {
    display: inline-block;
}


.schBox .schInter {
    display: block;
    position: absolute;
    margin: 2px 0 0;
    width: 90px;
    height: 24px;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    overflow: hidden;
    text-align: center;
    line-height: 24px;
    border-radius: 5px;
    background-color: #7ac143;
}

.schBox .schFirstPre {
    display: block;
    position: absolute;
    margin: 2px 0 0;
    width: 75px;
    height: 24px;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    overflow: hidden;
    text-align: center;
    line-height: 24px;
    border-radius: 5px 0 0 5px;
}

.schBox .schFirstNext {
    display: block;
    position: absolute;
    margin: 2px 0 0;
    width: 22px;
    height: 24px;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    overflow: hidden;
    text-align: center;
    line-height: 24px;
    border-radius: 0 5px 5px 0;
}

.schBox .schSecondPre {
    display: block;
    position: absolute;
    margin: 2px 0 0;
    width: 15px;
    height: 24px;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    overflow: hidden;
    text-align: center;
    line-height: 24px;
    border-radius: 5px 0 0 5px;
}

.schBox .schSecondNext {
    display: block;
    position: absolute;
    margin: 2px 0 0;
    width: 75px;
    height: 24px;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    overflow: hidden;
    text-align: center;
    line-height: 24px;
    border-radius: 0 5px 5px 0;
}

.schBox .otherDay {
    display: inline-block;
    text-align: right;
    font-size: 16px;
    font-weight: bold;
    padding: 2px 2px 0px 0px;
}

.schBox .exception {
    font-size: 15px;
    font-weight: bold;
    width: 60px;
    text-align: center;
    color: #fff;
    border-radius: 0 0 5px 5px;
    margin: 0 auto;
    display: inline-block;
    font-family: "KaiTi", "SimSun", "Microsoft YaHei", "STKaiti";
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    overflow: hidden;
    line-height: 18px;
    vertical-align: top;
    margin-right: 5px;
}

.calendarSchInfo {
    position: absolute;
    left: 670px;
}

.calendarSchInfo span {
    display: block;
    position: absolute;
    margin: 2px 0 0;
    width: 280px;
    height: 24px;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    overflow: hidden;
    text-align: left;
    line-height: 24px;
    border-radius: 0 5px 5px 0;
}

#idAttTimeSlotDiv {
    overflow-y: auto;
}

#idAttTimeSlotDiv .sch {
    border-bottom: 1px solid #eee;
    padding: 8px;
    cursor: pointer;
    display: block;
}

#idAttTimeSlotDiv .sch .attTimeSlotDel {
    display: none;
}

.attPersonSchInfoView {
    position: fixed;
    position: absolute;
    left: 21px;
    top: 67px;
    height: auto!important;
    z-index: 10;
    padding: 4px 4px 4px 4px!important;
    border: 2px solid #dcdfe2;
    text-align: left;
    border-radius: 5px;
    visibility: hidden;
    width: 260px;
}

.attPersonSchInfoView .schDateBox {
    height: auto!important;
    display: flex;
    flex-direction: column;
}

.attPersonSchInfoView .sch, .attPersonSchInfoView .schInter, .attPersonSchInfoView .schFirstPre{
    background-color: transparent;
    padding: 4px 10px;
    margin-top: 4px;
    border-radius: 5px;
    display: block;
    width: auto!important;
}
.attPersonSchInfoView .schInter {
    background-color: #7ac143;
}
.attPersonSchInfoView .exception {
    font-size: 15px;
    font-weight: bold;
    color: #fff;
    border-radius: 5px;
    padding: 0px 10px;
    display: block;
    font-family: "KaiTi", "SimSun", "Microsoft YaHei", "STKaiti";
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    line-height: 20px;
}
.attPersonSchInfoView .inter {
    height: auto!important;
}