/**
 *  注意标签ID命名+uuid
 */
var attGlobalInitOrClearUUID = "";

function attGlobalInitOrClearCondition() {
    setMonthFirstDay("beginTime" + attGlobalInitOrClearUUID);
    setNowDay("endTime" + attGlobalInitOrClearUUID);
    $("#pin" + attGlobalInitOrClearUUID).val("");
    $("#deptName" + attGlobalInitOrClearUUID).val("");
    $("#likeName" + attGlobalInitOrClearUUID).val("");
    loadSearchText(attGlobalGridName);
    return false;
}

/**
 * 设置为本月第一天，时分秒为00.00.00
 *
 * @date 2020/7/2 18:18
 */
function setMonthFirstDay(id) {
    var nowDate = new Date();
    var beginTime = new Date(nowDate.getFullYear(), nowDate.getMonth(), 1);
    beginTime.setHours(0);
    beginTime.setMinutes(0);
    beginTime.setSeconds(0);
    ZKUI.Input.get(id).setDate(beginTime);
}

/**
 /**
 * 设置为当前时间，时分秒为23.59.59
 *
 * @date 2020/7/2 18:18
 */
function setNowDay(id) {
    var nowDate = new Date();
    nowDate.setHours(23);
    nowDate.setMinutes(59);
    nowDate.setSeconds(59);
    var maxStartTime = ZKUI.Input.get(id).options.max;
    ZKUI.Input.get(id).setMaxDate(new Date(new Date(maxStartTime).setDate(new Date(maxStartTime).getDate() + 10)));
    ZKUI.Input.get(id).setDate(nowDate);
    ZKUI.Input.get(id).setMaxDate(maxStartTime);
}


/**
 * 请输入数字且只允许一位小数
 */
jQuery.validator.addMethod("convertCountValid", function (value, element) {
    var pattenChar = /^\d+(\.\d{1})?$/;
    return this.optional(element) || (pattenChar.test(value));
}, function () {
    return I18n.getValue('att_param_convertCountValid');
});

/**
 * 根据皮肤显示用户默认头像
 */
function attUserImageBySkin() {
    return (sysCfg.skin=="default"||!sysCfg.skin) ? "userImage.gif": sysCfg.skin + "/userImage.gif";
}

/**
* 结合deptIncludeLower.js，选择部门或者列表操作
*/
function attCommonDeptOrRowsOperate(id, bar, opt) {

    var type = "person";
    var text = I18n.getValue("att_annualLeave_sureSelectPerson").format(opt.text);

    var gridName = bar.gridName || "gridbox";
    var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
    if (!ids) {
        type = "dept";
        ids = ZKUI.Tree.get(attGlobalDeptTreeId).tree.getAllChecked();
        text = I18n.getValue("att_annualLeave_sureSelectDept").format(opt.text);
    }
    if (!ids) {
        openMessage(msgType.warning, I18n.getValue("att_personSch_checkDeptOrPersFirst"), 4000);
        return "";
    }

    actionConfirm(function(result) {
        if (result) {
            openMessage(msgType.loading);
            var param = splitURL(fillParamsFromGrid(gridName, ids, id));
            param.data.ids = ids;
            param.data.type = type;
            $.ajax({
                url: param.url,
                type: "post",
                data: param.data,
                success: function(result) {
                    closeMessage();
                    dealRetResult(eval(result),
                    function() {
                        ZKUI.Grid.reloadGrid(gridName)
                    })
                },
            })
        }
    },
    text)
}

/**
* 结合deptIncludeLower.js，选择部门或者列表操作弹窗
*/
function attCommonDeptOrRowsOperateOpen(id, bar, opt) {
    var type = "person";
    var text = I18n.getValue("att_annualLeave_sureSelectPerson").format(opt.text);
    var gridName = bar.gridName || "gridbox";
   var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
    if (!ids) {
        type = "dept";
        ids = ZKUI.Tree.get(attGlobalDeptTreeId).tree.getAllChecked();
        text = I18n.getValue("att_annualLeave_sureSelectDept").format(opt.text);
    }
    if (!ids) {
        openMessage(msgType.warning, I18n.getValue("att_personSch_checkDeptOrPersFirst"), 4000);
        return "";
    }
    actionConfirm(function(result) {
        if (result) {
            id = toURL(id, "ids=" + ids + "&type=" + type);
            var c = $.extend({
                path: fillParamsFromGrid(gridName, ids, id),
                gridName: gridName
            },
            JSON.parse(JSON.stringify(opt)));
            DhxCommon.createWindow(c)
        }
    },
    text)
}