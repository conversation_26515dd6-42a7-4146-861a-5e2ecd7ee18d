var attCalendarDragId = "";

var today = new Date();
var year = today.getFullYear();
var month = today.getMonth() + 1;
var day = today.getDate();

function setMonthAndYear(month, year, uuid) {
    if (month < 10) {
        $("#dateSelected" + uuid).val(year + "-0" + month);
        $("#todayDay" + uuid).text(year + "-0" + month);
    } else {
        $("#dateSelected" + uuid).val(year + "-" + month);
        $("#todayDay" + uuid).text(year + "-" + month);
    }
}

function bindPreNextClick(uuid, schType) {
    $("#pre" + uuid).click(function () {
        year = $("#dateSelected" + uuid).val().split("-")[0];
        month = $("#dateSelected" + uuid).val().split("-")[1];
        if (month == 1) {
            month = 12;
            year--;
        } else {
            month--;
        }
        setMonthAndYear(month, year, uuid);
        listJsonSch(uuid, schType);
    });

    $("#next" + uuid).click(function () {
        year = $("#dateSelected" + uuid).val().split("-")[0];
        month = $("#dateSelected" + uuid).val().split("-")[1];
        if (month == 12) {
            month = 1;
            year++;
        } else {
            month++;
        }
        setMonthAndYear(month, year, uuid);
        listJsonSch(uuid, schType);
    });
}

function initCalendar(month, day, uuid, op) {

    var monthDayNum = getMonthDay(year, month);

    $("#monthBox" + uuid + " ul").html("");

    // 获取第一天星期几
    var week = weekHandle();
    var liText = "";
    var preMonthDay = month - 1 == 0 ? getMonthDay(year, 12) : getMonthDay(year, month - 1);
    var preLen = preMonthDay - week + 1;
    var nextLen = 42 - monthDayNum - week;

    // 拼凑日期box的id
    var preYearStr = "" + year;
    var preMonthStr = "" + (month - 1);

    var nextYearStr = "" + year;
    var nextMonthStr = "" + (month + 1);

    var monthStr = "" + month;

    // 上个月
    if ((month - 1) < 10) {
        preMonthStr = "0" + (month - 1);
    }
    if (month - 1 == 0) {
        preYearStr = "" + (year - 1);
        preMonthStr = "12";
    }

    // 本月
    if (month < 10) {
        monthStr = "0" + month;
    }

    // 下个月
    if ((month + 1) < 10) {
        nextMonthStr = "0" + (month + 1);
    }
    if (month + 1 > 12) {
        nextYearStr = "" + (year + 1);
        nextMonthStr = "01";
    }

    // 上个月末尾
    for (var i = week; i > 0; i--) {
        var idStr = preYearStr + "-" + preMonthStr + "-" + preLen + uuid;
        var id = preYearStr + "-" + preMonthStr + "-" + preLen;
        liText += "<li class=\"zk-border-color attCalendarLi other\" id=\"" + id + "\" ondrop=\"attCalendarDrop(event)\" ondragover=\"attCalendarAllowDrop(event)\">";
        liText += "<div class=\"schBox\">"
        liText += "<span class=\"exception\" id=\"exception_" + idStr + "\"></span>"
        liText += "<span class=\"otherDay\">" + preLen + "</span>";
        liText += "<div class=\"inter\" id=\"inter_" + idStr + "\"></div>";
        liText += "<div class=\"schDateBox\" id=" + idStr + "></div>";
        liText += "</div>";
        liText += "</li>";
        preLen++;
    }

    // 本月
    for (var i = 1; i <= monthDayNum; i++) {
        var dayStr = i < 10 ? "0" + i : "" + i;
        var idStr = year + "-" + monthStr + "-" + dayStr + uuid;
        var id = year + "-" + monthStr + "-" + dayStr;
        liText += "<li class=\"zk-border-color attCalendarLi\" id=\"" + id + "\" ondrop=\"attCalendarDrop(event)\" ondragover=\"attCalendarAllowDrop(event)\">";
        liText += "<div class=\"schBox\">";
        liText += "<span class=\"exception\" id=\"exception_" + idStr + "\"></span>";
        liText += "<span class=\"day\">" + i + "</span>";
        liText += "<div class=\"inter\" id=\"inter_" + idStr + "\"></div>";
        liText += "<div class=\"schDateBox\" id=" + idStr + "></div>";
        liText += "</div>";
        liText += "</li>";
    }

    // 下个月初
    for (var i = 1; i <= nextLen; i++) {
        var dayStr = i < 10 ? "0" + i : "" + i;
        var idStr = nextYearStr + "-" + nextMonthStr + "-" + dayStr + uuid;
        var id = nextYearStr + "-" + nextMonthStr + "-" + dayStr;
        liText += "<li class=\"zk-border-color attCalendarLi other\" id=\"" + id + "\" ondrop=\"attCalendarDrop(event)\" ondragover=\"attCalendarAllowDrop(event)\">";
        liText += "<div class=\"schBox\">"
        liText += "<span class=\"exception\" id=\"exception_" + idStr + "\"></span>"
        liText += "<span class=\"otherDay\">" + i + "</span>";
        liText += "<div class=\"inter\" id=\"inter_" + idStr + "\"></div>";
        liText += "<div class=\"schDateBox\" id=" + idStr + "></div>";
        liText += "</div>";
        liText += "</li>";
    }

    liText += "<div id=\"attMoveSelected\"></div>"


    $("#monthBox" + uuid + " ul").append(liText);

    if (op) {
        initAttTimeSlotDel();
        initAttSchBoxDivMousemove();
    }
}

/*Drop事件，应该在initCalendar之前执行*/
function attCalendarAllowDrop(ev) {
    ev.preventDefault();
}

function attCalendarDrag(ev) {
    attCalendarDragId = ev.target.id;
    //ev.dataTransfer.setData("Text", );
}

function attCalendarDrop(ev) {
    var selectedCalendars = $(".attCalendarLi.selected");
    if (selectedCalendars && selectedCalendars.length > 0) {
        for (var i = 0; i < selectedCalendars.length; i++) {
            // 重复元素不新增
            if ($(selectedCalendars[i]).find(".schDateBox").children("#" + attCalendarDragId).length < 1) {
                var timeSlot = $("#" + attCalendarDragId).clone();
                timeSlot.append("<img class='attTimeSlotDel' src='images/delTimeSlot.png'>");
                $(selectedCalendars[i]).find(".schDateBox").append(timeSlot);
            } else {
                openMessage(msgType.info, I18n.getValue("att_personSch_repeatTimeSlotNoAdd"));
            }
        }
    } else {
        openMessage(msgType.info, I18n.getValue("att_personSch_checkCalendarFirst"));
    }
    attCalendarDragId = "";
    ev.preventDefault();
    // var data = ev.dataTransfer.getData("Text");
    // ev.target.appendChild(document.getElementById(data));
}

/*清空所有选中*/
function attCalendarCleanCheck() {
    var selectedCalendars = $(".attCalendarLi.selected");
    if (selectedCalendars && selectedCalendars.length > 0) {
        for (var i = 0; i < selectedCalendars.length; i++) {
            $(selectedCalendars[i]).removeClass('selected');
        }
    }
}

/*清除选中时间段*/
function attCalendarDelTimeSlot() {
    var selectedCalendars = $(".attCalendarLi.selected");
    if (selectedCalendars && selectedCalendars.length > 0) {
        messageBox({
            messageType: "confirm", text: I18n.getValue("att_personSch_delTimeSlot"),
            callback: function (result) {
                if (result) {
                    for (var i = 0; i < selectedCalendars.length; i++) {
                        $(selectedCalendars[i]).find(".schDateBox").empty();
                        $(selectedCalendars[i]).removeClass('selected');
                    }
                }
            }
        });
    }
}

function initAttTimeSlotDel() {
    /*因为append无法使事件生效，所以这边监听动态绑定点击事件*/
    $(".schDateBox").on('mousedown', '.attTimeSlotDel', function (event) {
        messageBox({
            messageType: "confirm", text: I18n.getValue("att_personSch_delTimeSlot"),
            callback: function (result) {
                if (result) {
                    $(event.toElement).parent().remove()
                }
            }
        });

        event.preventDefault();
        event.stopPropagation();
        return false;
    });
}

/**
 * 鼠标点击滑动
 */
function initAttSchBoxDivMousemove() {

    var Y = $('.attCalendarUl').offset().top;
    var X = $('.attCalendarUl').offset().left;

    var attMoveSelected = $('#attMoveSelected')[0];

    // 开启拖拽的标志
    var attMousemoveFlag = false;
    var startLeft = 0;
    var startTop = 0;

    // 鼠标按下时开启拖拽多选，将遮罩定位并展现
    $(".attCalendarUl").mousedown(function (event) {
        attMousemoveFlag = true;
        attMoveSelected.style.top = (event.pageY - Y) + 'px';
        attMoveSelected.style.left = (event.pageX - X) + 'px';
        startLeft = (event.pageX - X);
        startTop = (event.pageY - Y);
        event.stopPropagation(); // 阻止事件冒泡
    });

    // 鼠标移动时计算遮罩的位置，宽 高
    $(".attCalendarUl").mousemove(function (event) {
        if (!attMousemoveFlag) return;//只有开启了拖拽，才进行mouseover操作
        var pageX = (event.pageX - X);
        var pageY = (event.pageY - Y);

        if (pageX < startLeft) {//向左拖
            attMoveSelected.style.left = pageX + 'px';
            attMoveSelected.style.width = (startLeft - pageX) + 'px';
        } else {
            attMoveSelected.style.width = (pageX - startLeft) + 'px';
        }

        if (pageY < startTop) {//向上
            attMoveSelected.style.top = pageY + 'px';
            attMoveSelected.style.height = (startTop - pageY) + 'px';
        } else {
            attMoveSelected.style.height = (pageY - startTop) + 'px';
        }
        event.stopPropagation(); // 阻止事件冒泡
    });

    //鼠标抬起时计算遮罩的right 和 bottom，找出遮罩覆盖的块，关闭拖拽选中开关，清除遮罩数据
    $(".attCalendarUl").mouseup(function (event) {
        attMoveSelected.style.bottom = Number(attMoveSelected.style.top.split('px')[0]) + Number(attMoveSelected.style.height.split('px')[0]) + 'px';
        attMoveSelected.style.right = Number(attMoveSelected.style.left.split('px')[0]) + Number(attMoveSelected.style.width.split('px')[0]) + 'px';
        findSelected();
        attMousemoveFlag = false;
        clearDragData();

        event.stopPropagation(); // 阻止事件冒泡
    });

    $(".attCalendarUl").mouseleave(function (event) {
        attMousemoveFlag = false;
        attMoveSelected.style.width = 0;
        attMoveSelected.style.height = 0;
        attMoveSelected.style.top = 0;
        attMoveSelected.style.left = 0;
        event.preventDefault();  // 阻止默认行为
        event.stopPropagation(); // 阻止事件冒泡
    });

    function findSelected() {

        var Y = $('.attCalendarUl').offset().top;
        var X = $('.attCalendarUl').offset().left;

        var attMoveSelectedWidth = attMoveSelected.style.width.split('px')[0];
        var attMoveSelectedHeight = attMoveSelected.style.height.split('px')[0];

        //判断每个块是否被遮罩盖住（即选中）
        var attMoveSelectedLeft = attMoveSelected.style.left.split('px')[0];
        var attMoveSelectedRight = attMoveSelected.style.right.split('px')[0];
        var attMoveSelectedTop = attMoveSelected.style.top.split('px')[0];
        var attMoveSelectedBottom = attMoveSelected.style.bottom.split('px')[0];

        /*将选择的区域填充放大，计算*//*跟样式有关系*/
        /*方式二*/
        var offsetx = 11;
        var offsety = -1;
        var width = 108;
        var height = 89;
        attMoveSelectedLeft = Math.floor(attMoveSelectedLeft / width) * width + offsetx - Math.floor(attMoveSelectedLeft / width);
        attMoveSelectedRight = Math.ceil(attMoveSelectedRight / width) * width + offsetx - Math.floor(attMoveSelectedRight / width);
        attMoveSelectedTop = Math.floor(attMoveSelectedTop / height) * height + offsety - Math.floor(attMoveSelectedTop / height);
        attMoveSelectedBottom = Math.ceil(attMoveSelectedBottom / height) * height + offsety - Math.floor(attMoveSelectedBottom / height);

        var attCalendarList = $('.attCalendarUl').find('li');
        for (var i = 0; i < attCalendarList.length; i++) {
            //计算每个块的定位信息
            var left = $(attCalendarList[i]).offset().left - X;
            var right = $(attCalendarList[i]).width() + left;
            var top = $(attCalendarList[i]).offset().top - Y;
            var bottom = $(attCalendarList[i]).height() + top;

            // 点击反选
            if (attMoveSelectedWidth < 1 || attMoveSelectedHeight < 1) {
                if (attMoveSelectedLeft == left && attMoveSelectedTop == top) {
                    if ($(attCalendarList[i]).hasClass('selected')) {
                        $(attCalendarList[i]).removeClass('selected');
                    } else {
                        $(attCalendarList[i]).addClass('selected');
                    }
                }
            } else {
                /*方式二*/
                if ((left >= attMoveSelectedLeft && right <= attMoveSelectedRight) && (top >= attMoveSelectedTop && bottom <= attMoveSelectedBottom)) {
                    $(attCalendarList[i]).addClass('selected');
                }
            }


            /*方式一*/
            // var leftFlag = attMoveSelectedLeft <= left && left <= attMoveSelectedRight;
            // var rightFlag = attMoveSelectedLeft <= right && right <= attMoveSelectedRight;
            // var topFlag = attMoveSelectedTop <= top && top <= attMoveSelectedBottom;
            // var bottomFlag = attMoveSelectedTop <= bottom && bottom <= attMoveSelectedBottom;
            //
            // if (((leftFlag || rightFlag) && (topFlag || bottomFlag))) {
            // 	$(attCalendarList[i]).addClass('selected');
            // }

        }
    }

    function clearDragData() {
        attMoveSelected.style.width = 0;
        attMoveSelected.style.height = 0;
        attMoveSelected.style.top = 0;
        attMoveSelected.style.left = 0;
        attMoveSelected.style.bottom = 0;
        attMoveSelected.style.right = 0;
    }
}

//跨天班次临时存储函数
function createObject(interBox, html, otherDateBox, className, periodName, titleStr, uuid) {
    var object = {};

    object.interBox = "inter_" + interBox + uuid;
    object.html = html; // 每一个对象的函数对象都指向同一个函数对象
    object.className = className;
    object.periodName = periodName;
    object.titleStr = titleStr;
    // 设置跨天排班所占位置高度，由于底层是浮动布局，跨天排班是定位布局，所以不会自动撑高div块，所以需要手动设置高度，下同
    $("#inter_" + interBox + uuid).css("height", "28px");
    $("#" + interBox + uuid).css("height", "38px");
    if (otherDateBox) {
        object.otherDateBox = otherDateBox;
    }
    return object;
}

// 获取这一个月的天数
function getMonthDay(year, month) {
    var arr = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    // 判断是否是瑞年
    var leap = isLeapYear(year);
    if (leap && month == 2) {
        return 29;
    } else {
        return arr[month - 1];
    }
}

// 判断是否是瑞年
function isLeapYear(year) {
    if ((year % 4 == 0 && year % 100 != 0)
        || (year % 100 == 0 && year % 400 == 0)) {
        return true;
    } else {
        return false;
    }
}

// 获取一个月的第一天星期几
function weekHandle() {
    var weekArr = new Array(0, 1, 2, 3, 4, 5, 6);
    var date = new Date(year + "/" + month + "/" + 1);
    return date.getDay();
}

// 获取第二天日期
function getNextDay(date) {
    var tomorrow = new Date(new Date(date).getTime() + 86400000);
    return formatDate(tomorrow);
}

// 获取前一天日期
function getPreDay(date) {
    var yesterday = new Date(new Date(date).getTime() - 86400000);
    return formatDate(yesterday);
}

function formatDate(date) {
    var y = date.getFullYear();
    var m = date.getMonth() + 1;
    m = m < 10 ? '0' + m : m;
    var d = date.getDate();
    d = d < 10 ? ('0' + d) : d;
    return y + '-' + m + '-' + d;
}

// 获取星期几
function getDay(date) {
    return new Date(date).getDay();
}

// 跨天排班的班次相关html处理
function createHtml(attTimeSlotJson, titleStr, interSchArray, dateBoxWidth, date, uuid) {
    var htmlInter = "";
    var htmlOther = "";
    var positionWidth = 0;
    if (attTimeSlotJson.crossDay == "0") {// 记为第一日
        var torrowDate = getNextDay(date);
        var today = getDay(date);
        if (today == 6) {// 星期六
            positionWidth = 7 * dateBoxWidth - 54;
            htmlInter = "<span class=\"schFirstPre\" style=\"left:" + positionWidth + "px; width:58px; background:" + attTimeSlotJson.timeSlotColor + ";\" title=\"" + titleStr + "\">" + attTimeSlotJson.periodName + "</span>";
            htmlOther = "<span class=\"schFirstNext\" style=\"left: 12px; width: 22px;background:" + attTimeSlotJson.timeSlotColor + ";\" title=\"" + titleStr + "\">" + attTimeSlotJson.periodName + "</span>";
            interSchArray.push(createObject(date, htmlInter, torrowDate, "schFirstPre", attTimeSlotJson.periodName, titleStr, uuid));
            interSchArray.push(createObject(torrowDate, htmlOther, null, "schFirstNext", attTimeSlotJson.periodName, titleStr, uuid));
        } else {
            positionWidth = (today + 1) * dateBoxWidth - 54;
            htmlInter = "<span class=\"schInter\" style=\"left:" + positionWidth + "px; background:" + attTimeSlotJson.timeSlotColor + ";\" title=\"" + titleStr + "\">" + attTimeSlotJson.periodName + "</span>";
            interSchArray.push(createObject(date, htmlInter, torrowDate, "schInter", attTimeSlotJson.periodName, titleStr, uuid));
        }
    } else {// 记为第二日
        var yesterDate = getPreDay(date);
        var today = getDay(date);
        if (today == 0) {// 星期日
            positionWidth = 7 * dateBoxWidth - 18;// 根据盒子模型计算
            htmlInter = "<span class=\"schSecondNext\" style=\"left: 12px; width:60px; background:" + attTimeSlotJson.timeSlotColor + ";\" title=\"" + titleStr + "\">" + attTimeSlotJson.periodName + "</span>";
            htmlOther = "<span class=\"schSecondPre\" style=\"left:" + positionWidth + "px; width:22px; background:" + attTimeSlotJson.timeSlotColor + ";\" title=\"" + titleStr + "\">" + attTimeSlotJson.periodName + "</span>";
            interSchArray.push(createObject(date, htmlInter, yesterDate, "schSecondNext", attTimeSlotJson.periodName, titleStr, uuid));
            interSchArray.push(createObject(yesterDate, htmlOther, null, "schSecondPre", attTimeSlotJson.periodName, titleStr, uuid));
        } else {
            positionWidth = today * dateBoxWidth - 18;
            htmlInter = "<span class=\"schInter\" style=\"left:" + positionWidth + "px; background:" + attTimeSlotJson.timeSlotColor + ";\" title=\"" + titleStr + "\">" + attTimeSlotJson.periodName + "</span>";
            interSchArray.push(createObject(date, htmlInter, yesterDate, "schInter", attTimeSlotJson.periodName, titleStr, uuid));
        }
    }
}

//日历填充数据
function listJsonSch(uuid, schType) {
    var schId = $("#schId" + uuid).val();
    var dateSelected = $("#dateSelected" + uuid).val();
    var html = "";
    var interSchArray = [];
    var dateBoxWidth = 108;//日历格子宽度
    if (schId) {
        $.ajax({
            data: {
                schId: schId,
                dateSelected: dateSelected
            },
            dataType: "json",
            type: "post",
            url: "att" + schType + "Sch.do?getAttShiftSchJsonByDate",
            success: function (data) {
                initCalendar(month, day, uuid, false);
                initSchBox();
                $.each(data.data.attSchJsonArray, function (i, attDateJson) {
                    //日历格子还原到初始化状态
                    $("#" + attDateJson.date + uuid).empty();
                    $("#" + attDateJson.date + uuid).css("height", "66px");
                    $("#inter_" + attDateJson.date + uuid).empty();
                    $("#inter_" + attDateJson.date + uuid).css("height", "0");
                    //判断是否有排班
                    if (attDateJson.times > 0) {
                        $.each(attDateJson.attTimeSlotJsonArray, function (j, attTimeSlotJson) {
                            //将时间段信息放入班次title
                            var titleStr = attTimeSlotJson.periodName + "&#13;";
                            $.each(attTimeSlotJson.attTimeSlotArray, function (k, value) {
                                titleStr += value + "&#13;";
                            });
                            //判断班次是否包含时间段信息，没有则不显示班次
                            if (attTimeSlotJson.times > 0) {
                                if (attTimeSlotJson.interDay == "0") {//不是跨天
                                    //正常排班
                                    html += "<span class=\"sch zk-bg-color\" style=\"background:" + attTimeSlotJson.timeSlotColor + ";\" title=\"" + titleStr + "\">" + attTimeSlotJson.periodName + "</span>";
                                } else {//跨天
                                    createHtml(attTimeSlotJson, titleStr, interSchArray, dateBoxWidth, attDateJson.date, uuid);
                                }
                            }
                        });
                    }
                    //非跨天排班数据填充渲染
                    $("#" + attDateJson.date + uuid).html(html);
                    html = "";
                });
                // 渲染节假日
                if (schType == "Person") {
                    $.each(data.data.attExceptionJsonArray, function (i, attDateJson) {
                        $("#exception_" + attDateJson.date + uuid).text(attDateJson.exceptionSymbol);
                        $("#exception_" + attDateJson.date + uuid).css("background", "#e57a14");
                        $("#exception_" + attDateJson.date + uuid).attr("title", attDateJson.exceptionValue);
                    });
                }
                //渲染跨天排班
                for (var item in interSchArray) {
                    var spans = $("#" + interSchArray[item].interBox + " ." + interSchArray[item].className);
                    if (spans.length == 0) {
                        $("#" + interSchArray[item].interBox).append(interSchArray[item].html);
                    } else {
                        //多个跨天班次
                        var oldText = spans.eq(0).html();
                        var oldTitle = spans.eq(0).attr("title");
                        spans.eq(0).html(oldText + "," + interSchArray[item].periodName);
                        var curTitle = $('<div/>').html(interSchArray[item].titleStr).text();
                        spans.eq(0).attr("title", oldTitle + curTitle);
                    }

                    //跨天排班超出部分占据一个格子
                    if (interSchArray[item].otherDateBox) {
                        $("#inter_" + interSchArray[item].otherDateBox + uuid).css("height", "28px");
                        $("#" + interSchArray[item].otherDateBox + uuid).css("height", "38px");
                    }
                }
                interSchArray = [];
            }
        });
    } else {
        initCalendar(month, day, uuid);
        initSchBox();
    }
}
function initSchBox() {
    $(".attSchBoxDiv .monthBox .schBox").hover(function() {
        var el = $(this);
        if ($(this).find(".schDateBox") && !($(this).find(".schDateBox").html())) {
            return;
        }
        if ($(this).position().left > 320) {
            $(".attPersonSchInfoView").css("left", "21px");
            $(".attPersonSchInfoView").css("right", "auto");
        } else {
            $(".attPersonSchInfoView").css("left", "auto");
            $(".attPersonSchInfoView").css("right", "23px");
        }
        $(".attPersonSchInfoView").html($(this).html());
        $(".attPersonSchInfoView .day").remove();
        $(".attPersonSchInfoView .otherDay").remove();
        $(".attPersonSchInfoView .schFirstNext").remove();
        $(".attPersonSchInfoView").css("visibility", "visible");
    }, function(){
        $(".attPersonSchInfoView").html("");
        $(".attPersonSchInfoView").css("visibility", "hidden");
    });
}