I18n.load([
    "att_personSch_checkCalendarFirst",
    "att_personSch_delTimeSlot",
    "att_personSch_repeatTimeSlotNoAdd",
    "att_timeSlot_add",
    "att_timeSlot_select",
    "att_timeSlot_repeat",
    "att_personSch_today",
    "att_timeSlot_overlapping",
    "att_timeSlot_addFirst",
    "att_timeSlot_overlapping",
    "att_timeSlot_repeat",
    "common_tree_containsSubordinate",
    "att_personSch_checkDeptOrPersFirst",
    "att_personSch_sureToCycleSchDeptOrGroup",
    "att_personSch_sureToCycleSch",
    "att_personSch_sureToTempSchDeptOrGroup",
    "att_personSch_sureToTempSch",
    "att_personSch_sureCleanCycleSchDeptOrGroup",
    "att_personSch_sureCleanCycleSch",
    "att_personSch_sureCleanTempSchDeptOrGroup",
    "att_personSch_sureCleanTempSch",
    "att_personSch_checkGroupFirst",
    "att_param_convertCountValid",
    "att_timeSlot_alertMoreSignOutPosponeTime",
    "att_timeSlot_alertMoreSignInAdvanceTime",
    "att_timeSlot_delayedWorkMinutesValidMsg1",
    "att_timeSlot_delayedWorkMinutesValidMsg2",
    "att_timeSlot_delayedWorkMinutesValidMsg3",
    "att_timeSlot_delayedWorkMinutesValidMsg4",
    "att_timeSlot_delayedWorkMinutesValidMsg5",
    "att_timeSlot_delayedWorkMinutesValidMsg6",
    "att_timeSlot_delayedWorkMinutesValidMsg7",
    "att_timeSlot_allowEarlyMinutesValidMsg1",
    "att_timeSlot_allowEarlyMinutesValidMsg2",
    "att_timeSlot_allowLateMinutesValidMsg1",
    "att_timeSlot_allowLateMinutesValidMsg2",
    "att_timeSlot_advanceWorkMinutesValidMsg1",
    "att_timeSlot_advanceWorkMinutesValidMsg2",
    "att_timeSlot_advanceWorkMinutesValidMsg3",
    "att_timeSlot_advanceWorkMinutesValidMsg4",
    "att_timeSlot_advanceWorkMinutesValidMsg5",
    "att_timeSlot_advanceWorkMinutesValidMsg6",
    "att_timeSlot_advanceWorkMinutesValidMsg7",
    "att_statistical_dayCardDetail",
    "att_apply_revoke",
    "att_leave_image",
    "att_annualLeave_sureSelectDept",
    "att_annualLeave_sureSelectPerson"
], true);