if (Web) {
    Web.attachEvent("onAfterInit", function () {
        if (system && system.myAcc) {
            system.myAcc.attachEvent("onBeforeActive", function (id, state) {
                var returnStr = true;
                var menus = this.cells(id).dataObj._idpull;
                for (var i in menus) {
                    var menuId = String(menus[i].id);
                    if (menuId.indexOf("attFlowable.do") > -1) {
                        $.ajax({
                            type: "post",
                            url: "attFlowable.do?isCanOpenFlowable",
                            dataType: "json",
                            async: false,
                            success: function (result) {
                                var resultData = result.data;
                                if (resultData == false) {
                                    var ptimeout = sysCfg.ptimeout;
                                    openMessage(msgType.warning, result.msg, ptimeout);
                                    returnStr = false;
                                }
                            }
                        });
                    }
                }
                return returnStr;
            })
        }
    });
}

var attParams = {};
loadAttParams();

function loadAttParams() {
    $.ajax({
        url: "attRule.do?getParams",
        success: function (result) {
            if (result[sysCfg.ret] == sysCfg.success) {
                attParams = $.extend({}, attParams, result[sysCfg.data]);
                console.log("loadAttParams", attParams)
                /*禁用实时点名时，不显示“实时点名”、“签到点名表”菜单*/
                if (attParams['att.realTime.rollCall'] == "0") {
                    attShowMenu("attRealTimeCallRoll.do");
                    attShowMenu("attSignCallRollReport.do");
                } else {
                    attHideMenu("attRealTimeCallRoll.do");
                    attHideMenu("attSignCallRollReport.do");
                }
            }
        }
    });
}

/*显示菜单*/
function attShowMenu(actionLink) {
    Web.attachEvent("onLoadMenu", function () {
        for (var key in system.myAcc.t) {
            var menuItem = system.myAcc.t[key].cell.dataObj._idpull[actionLink];
            if (menuItem) {
                $(menuItem.htmlNode).show();
                break;
            }
        }
    });
}

/*隐藏菜单*/
function attHideMenu(actionLink) {
    Web.attachEvent("onLoadMenu", function () {
        for (var key in system.myAcc.t) {
            var menuItem = system.myAcc.t[key].cell.dataObj._idpull[actionLink];
            if (menuItem) {
                $(menuItem.htmlNode).hide();
                break;
            }
        }
    });
}
