/*拖拽时间段树对象*/
var attTimeSlotTreeDrag = null;
/*日期控件编辑时的事件ID，用于时间段验证*/
var attTimeSlotEditEventId = null;
/*保存时间段id和对象集合，验证的时候直接获取对象进行判断*/
var attTimeSlotMap = {};
/*是否初始化日历控件事件，空间事件不能重复初始化，否在事件会跑多次*/
var schedulerEventInit = false;

/*初始化时间段id和对象集合*/
function initAttTimeSlotMap() {
    $.ajax({
        type: "post",
        url: "attTimeSlot.do?getNormalList",
        success: function (ret) {
            if (ret.data) {
                $.each(ret.data, function (i, value) {
                    attTimeSlotMap[value.id] = value;
                });
            } else {
                openMessage(msgType.info, I18n.getValue("att_timeSlot_addFirst"));
            }
        }
    })
}

/*初始化时间段拖拽树*/
function initAttTimeSlotTree(id) {

    attTimeSlotTreeDrag = new dhtmlXTreeObject(id, "100%", "100%", 0);
    attTimeSlotTreeDrag.enableDragAndDrop(true);
    attTimeSlotTreeDrag.setSkin(sysCfg.dhxSkin);
    attTimeSlotTreeDrag.setImagePath(sysCfg.treeImgPath);
    attTimeSlotTreeDrag.setDataMode("json");
    attTimeSlotTreeDrag.loadJSON("attTimeSlot.do?getTree", function(res) {
        var resJson = JSON.parse(res.response)
        if (!resJson.item || resJson.item.length === 0 ) {
            appendEmptyTip($("#" + id));
        }
    });
    attTimeSlotTreeDrag.attachEvent("onDragIn", function (value, text) {
    })
}

/*勾选时间段验证*/
function attCheckedTimeSlotValid(spanEvent) {
    var checkboxId = spanEvent.htmlFor;
    var checked = $("#"+checkboxId).prop("checked");
    if (!checked) {
        var value = $("#"+checkboxId).val();
        var addTimeSlot = attTimeSlotMap[value];
        var checkeds = $("input[type=checkbox][name='attTimeSlotCheckbox']:checked");
        checkeds.each(function (index, el) {
            var existTimeSlot = attTimeSlotMap[$(this).val()];
            if (existTimeSlot.id != addTimeSlot.id) {
                if (timeSlotCross(existTimeSlot.toWorkTime, existTimeSlot.offWorkTime,
                    addTimeSlot.toWorkTime, addTimeSlot.offWorkTime)) {
                    setTimeout(function() {
                         $("#"+checkboxId).prop("checked", false)
                         openMessage(msgType.info, formatParams(I18n.getValue("att_timeSlot_overlapping"), [addTimeSlot.periodName, existTimeSlot.periodName]));
                    }, 300)
                    return false;
                }
            }
        });
    }
}

/*初始化日历组件*/
function initAttScheduler(id) {

    attTimeSlotEditEventId = null;
    scheduler.config.details_on_dblclick = false;
    scheduler.config.details_on_create = false;

    scheduler.init(id, new Date(), "month");

    // 设置弹窗标题
    scheduler.templates.lightbox_header = function (start, end, event) {
        return I18n.getValue("att_timeSlot_add");
    };

    // 设置事件条显示内容前缀
    scheduler.templates.event_bar_date = function (start, end, event) {
        return "";
    };

    if (!schedulerEventInit) {

        // 双击点击事件改成点击也可以选择
        scheduler.attachEvent("onClick", function (id) {
            attTimeSlotEditEventId = id;
            scheduler.showLightbox(id);
        });

        scheduler.attachEvent("onAfterLightbox", function () {
            attTimeSlotEditEventId = null;
        });

        // 保存校验
        scheduler.attachEvent("onEventSave", function (id, ev) {
            if (!ev.text) {
                openMessage(msgType.info, I18n.getValue("att_timeSlot_select"));
                return false;
            }

            // 不是双击编辑的时间会多一天~
            var end_date = new Date(ev.end_date.getTime());
            if (!ev._timed) {
                end_date.setDate(ev.end_date.getDate() - 1);
            }
            var attTimeSlotIds = ev.attTimeSlotIds;
            if (!validTimeSlot(ev.start_date, end_date, attTimeSlotIds, 0, attTimeSlotEditEventId)) {
                return false;
            }

            return true;
        });

        // 判断是否显示删除按钮
        scheduler.attachEvent("onBeforeLightbox", function (id) {
            /*重置！！！*/
            scheduler.resetLightbox();
            var ev = scheduler.getEvent(id);
            if (!ev.text) {
                setTimeout(function(){
                    $(".dhx_delete_btn").parent().hide();
                }, 50);
            } else {
                setTimeout(function(){
                    $(".dhx_delete_btn").parent().show();
                }, 50);
            }
            return true;
        });

        // 拖拽时间段渲染事件/判断时间段是否重复
        scheduler.attachEvent("onExternalDragIn", function (id, source, e) {
            console.log("onExternalDragIn ......");
            var start_date = new Date(scheduler.getActionData(e).date);
            var attTimeSlotIds = attTimeSlotTreeDrag._dragged[0].id;
            if (validTimeSlot(start_date, start_date, attTimeSlotIds, 0)) {
                var text = attTimeSlotTreeDrag.getItemText(attTimeSlotIds);
                scheduler.addEvent({
                    start_date: start_date,
                    end_date: start_date,
                    text: text,
                    attTimeSlotIds: attTimeSlotIds
                });
            }
            // 返回false不执行自带弹窗功能
            return false;
        });

        // 事件移动改变判断时间段是否重复
        scheduler.attachEvent("onBeforeEventChanged", function (ev, e, is_new, original) {
            if (!is_new) {
                // 不是双击编辑的时间会多一天~
                var end_date = new Date(ev.end_date.getTime());
                if (!ev._timed) {
                    end_date.setDate(ev.end_date.getDate() - 1);
                }

                if (!validTimeSlot(ev.start_date, end_date, ev.attTimeSlotIds, 1)) {
                    return false;
                }
            }
            return true;
        });

        schedulerEventInit = true;
    }

    /*初始化时间段勾选列表*/
    $.ajax({
        async: false,
        url: "attTimeSlot.do?getNormalList",
        success: function (res) {
            if (res.data) {
                scheduler.form_blocks["my_editor"] = {
                    render: function (sns) {
                        var attTimeSlotForm = "";
                        $.each(res.data, function (index, value) {
                            var text = value.periodName;
                            if (value.toWorkTime && value.offWorkTime) {
                               text +=  " (" + value.toWorkTime + " - " + value.offWorkTime + ")";
                            } else if (value.startSignInTime && value.endSignOffTime) {
                               text +=  " (" + value.startSignInTime + " - " + value.endSignOffTime + ")";
                            }
                            // attTimeSlotForm += "<label class='attTimeSlotForm_sch' for='" + value.id + "'>"
                            attTimeSlotForm += "<span class='attTimeSlotForm_sch'>"
                            + "<span id='attTimeSlotSpan"+index+"'></span>"
                            + "<span>" + text + "</span></span>";

                             loadUIToDiv("input", "#attTimeSlotSpan" + index, {
                                useInputReq: true,
                                type: "checkbox",
                                onclick: "attCheckedTimeSlotValid(this)",
                                name: "attTimeSlotCheckbox",
                                id: value.id,
                                value: value.id,
                                text: text
                            })
                        });

                        return "<div class='attTimeSlotForm'>"
                            + "<input class='attTimeSlotForm_hidden' name='text' type='text'>"
                            + "<input class='attTimeSlotForm_hidden' name='attTimeSlotIds' type='text'>"
                            + attTimeSlotForm + "</div>";
                    },
                    set_value: function (node, value, ev) {
                        node.querySelector("[name='text']").value = value || "";
                        node.querySelector("[name='attTimeSlotIds']").value = ev.attTimeSlotIds || "";
                        $("input[type=checkbox][name='attTimeSlotCheckbox']").prop("checked", false);
                        setTimeout(function() {
                            if (ev.attTimeSlotIds) {
                                var attTimeSlotIdArray = ev.attTimeSlotIds.split(',');
                                $.each(attTimeSlotIdArray, function (index, value) {
                                    if (value) {
                                        $("input[type=checkbox][name='attTimeSlotCheckbox'][value=" + value + "]").prop("checked", true);
                                    }
                                })
                            }
                        }, 300)
                    },
                    get_value: function (node, ev) {
                        // 点击确认后获取勾选的信息
                        var checkeds = $("input[type=checkbox][name='attTimeSlotCheckbox']:checked");
                        var text = "";
                        var attTimeSlotIds = "";
                        checkeds.each(function (index, el) {
                            var attTimeSlotId = $(this).val();
                            attTimeSlotIds += attTimeSlotId;
                            text += ZKUI.Input.get(attTimeSlotId).options.text;
                            if (checkeds.length != (index + 1)) {
                                attTimeSlotIds += ",";
                                text += ";";
                            }
                        });
                        node.querySelector("[name='attTimeSlotIds']").value = attTimeSlotIds;
                        node.querySelector("[name='text']").value = text;

                        ev.attTimeSlotIds = node.querySelector("[name='attTimeSlotIds']").value;
                        return node.querySelector("[name='text']").value;
                    },
                    focus: function (node) {
                        var input = node.querySelector("[name='text']");
                        input.select();
                        input.focus();
                    }
                };

                scheduler.locale.labels.section_description = "Details";
                scheduler.config.lightbox.sections = [
                    {name: "description", map_to: "text", type: "my_editor", focus: true}
                ];
            }
        }
    })
}

/*验证时间段*//*num因为onBeforeEventChanged事件会先把数据保存，判断重复出现的id需要大于1*/
function validTimeSlot(start_date, end_date, timeSlotIds, num, eventId) {
    // 找出当前存在的所有事件，组装日期和时间段ID
    var allEventsDateAndTimeSlotIds = [];
    var events = scheduler.getEvents();
    for (var i = 0; i < events.length; i++) {
        // 编辑的时候不跟自己对比！
        var event = events[i];
        if (eventId && eventId == event.id) {
            continue;
        }

        // 不是双击编辑的时间会多一天~
        var event_end_date = new Date(event.end_date);
        if (!event._timed) {
            event_end_date.setDate(event.end_date.getDate() - 1);
        }

        var allEventsDate = getAllDate(new Date(event.start_date), event_end_date);
        for (var j = 0; j < allEventsDate.length; j++) {
            var date = allEventsDate[j];
            if (allEventsDateAndTimeSlotIds[date]) {
                if (event.attTimeSlotIds) {
                    allEventsDateAndTimeSlotIds[date] = allEventsDateAndTimeSlotIds[date] + "," + event.attTimeSlotIds;
                }
            } else {
                if (event.attTimeSlotIds) {
                    allEventsDateAndTimeSlotIds[date] = event.attTimeSlotIds;
                }
            }
        }
    }
    //console.log("找出当前存在的所有事件，组装日期和时间段ID", allEventsDateAndTimeSlotIds);


    // 找出当前要添加的事件，组装日期和时间段ID
    var addEventsDateAndTimeSlotIds = [];
    var addEventsDate = getAllDate(new Date(start_date), new Date(end_date));
    for (var z = 0; z < addEventsDate.length; z++) {
        var date = addEventsDate[z];
        if (addEventsDateAndTimeSlotIds[date]) {
            addEventsDateAndTimeSlotIds[date] = addEventsDateAndTimeSlotIds[date] + "," + timeSlotIds;
        } else {
            addEventsDateAndTimeSlotIds[date] = timeSlotIds;
        }
    }
    //console.log("找出当前要添加的事件，组装日期和时间段ID", addEventsDateAndTimeSlotIds);

    // 遍历要新增的时间段，判断是否重复
    var addDateKeys = Object.keys(addEventsDateAndTimeSlotIds);
    for (var i = 0; i < addDateKeys.length; i++) {
        var addDateKey = addDateKeys[i];
        var existTimeSlotIds = allEventsDateAndTimeSlotIds[addDateKey];
        if (existTimeSlotIds) {
            var existTimeSlotIdArrays = existTimeSlotIds.split(",");
            var addTimeSlotIds = addEventsDateAndTimeSlotIds[addDateKey];
            if (addTimeSlotIds) {
                var addTimeSlotIdArrays = addTimeSlotIds.split(",");
                for (var j = 0; j < addTimeSlotIdArrays.length; j++) {
                    var addTimeSlotId = addTimeSlotIdArrays[j];
                    if (patch(existTimeSlotIds, addTimeSlotId) > num) {
                        var addTimeSlot = attTimeSlotMap[addTimeSlotId];
                        openMessage(msgType.info, formatParams(I18n.getValue("att_timeSlot_repeat"), [addTimeSlot.periodName]));
                        return false;
                    } else {
                        for (var z = 0; z < existTimeSlotIdArrays.length; z++) {
                            var existTimeSlotId = existTimeSlotIdArrays[z];
                            var existTimeSlot = attTimeSlotMap[existTimeSlotId];
                            var addTimeSlot = attTimeSlotMap[addTimeSlotId];
                            if (existTimeSlot.id != addTimeSlot.id) {
                                if (timeSlotCross(existTimeSlot.toWorkTime, existTimeSlot.offWorkTime,
                                    addTimeSlot.toWorkTime, addTimeSlot.offWorkTime)) {
                                    openMessage(msgType.info, formatParams(I18n.getValue("att_timeSlot_overlapping"), [addTimeSlot.periodName, existTimeSlot.periodName]));
                                    return false;
                                }
                            }
                        }
                    }
                }
            }

        }
    }
    return true;
}

/*判断两个时间段是否有交集*/
function timeSlotCross(start1, end1, start2, end2) {
    if (start1 >= end1) {
        end1 = "3" + end1;
    }

    if (start2 >= end2) {
        end2 = "3" + end2;
    }
    return start1 <= end2 && end1 >= start2;
}

/*获取两个时间内的所有日期*/
function getAllDate(startTime, endTime) {
    var arr = [];
    while ((endTime.getTime() - startTime.getTime()) >= 0) {
        var year = startTime.getFullYear();
        var monthEx = startTime.getMonth() + 1;
        var month = monthEx.toString().length == 1 ? "0" + monthEx.toString() : monthEx;
        var day = startTime.getDate().toString().length == 1 ? "0" + startTime.getDate() : startTime.getDate();
        arr.push(year + "-" + month + "-" + day);
        startTime.setDate(startTime.getDate() + 1);
    }
    return arr;
}

function patch(s, re) {
    re = eval("/" + re + "/ig")
    if (s.match(re)) {
        return s.match(re).length;
    }
    return 0;
}

function formatParams(source, params) {
    $.each(params, function (i, n) {
        source = source.replace(new RegExp("\\{" + i + "\\}", "g"), n);
    });
    return source;
}
