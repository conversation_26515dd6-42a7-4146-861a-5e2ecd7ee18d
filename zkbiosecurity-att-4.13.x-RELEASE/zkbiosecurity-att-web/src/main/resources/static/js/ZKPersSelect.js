/*************************************************************************
 *                          选人组件--带部门树
 * @描述
 *     基于SelectContent开发,携带有部门树过滤条件.
 * @配置属性说明
 *     对比SelectContent增加：
 *          deptQuery: 部门树数据获取接口
 * @方法说明
 *     getRightGridSelectIds() ：获取右侧grid选中项的ids;
 *     getSelectRows() ： 获取右侧选中行数据
 * @注意事项
 *      vo属性对应query返回值
 *      deptQuery为必填项,若不需要部门树过滤,请选择SelectContent组件.
 * @调用示例
 *      <@ZKUI.SelectPersContent gridName="selectPerson${uuid!}"  copy="true" vo="com.zkteco.zkbiosecurity.pers.vo.PersPersonItem" deptQuery= "/authDepartment.do?dynaTree&showPersonCount=true" query="/persPerson.do?list">
 *      </@ZKUI.SelectPersContent>
 * <AUTHOR>
 * @Since  2020-5-11 17:12:35
 * @Copyright  Copyright © 2020 ZKTECO CO., LTD. All rights reserved
 *
 **************************************************************************/
(function () {
    ZKPersSelect.PULL = [];
    var suport_evts = ["onSure", "onCancel", "onCheck", "onInit", "onClose", "onBeforeInit"];
    var MAX_SELECT_NUM = 500;

    ZKPersSelect.suport_evts = suport_evts;

    function ZKPersSelect(opts) {
        this.options = opts;
        extendOptions.apply(this, []);
        ZKPersSelect.PULL[this.options.gridName] = this;
        DhxCommon.extendEventSuport(ZKPersSelect);
        this.events = {};
        DhxCommon.initEvents(this, suport_evts, this.options);
        this.callEvent("onBeforeInit", [this]);
        this.init();
        this.callEvent("onInit", [this]);
        initEvent.apply(this);
        DhxCommon.attachDestroy(this);
    }

    function extendOptions() {
        if (this.options.parent) {
            var opts = $(this.options.parent).data("options");
            if (opts) {
                $.extend(this.options, opts);
            }
        }
    }

    /**
     * 初始化事件
     */
    function initEvent() {
        initButtonEvent.apply(this, [this]);
        initGridEvent.apply(this, [this]);
    }

    function initButtonEvent() {
        var gridName = this.options.gridName;
        var that = this;
        $(document.getElementById(gridName + "ok")).on("click", function (evt) {
            if (!that.callEvent("onSure", [that.getRightGridSelectIds(), "", evt])) {
                return;
            }
            buttonOKClick.apply(that, [evt]);
        });

        $(document.getElementById(gridName + "cancel")).on("click", function (evt) {
            if (!that.callEvent("onCancel", [evt])) {
                return;
            }
            buttonCancelClick.apply(that, [evt]);
        });
        $(document.getElementById(gridName + "Bottom")).find("button").data("selectContent", this);
    }

    /**
     *    确定按钮事件
     */
    function buttonOKClick() {
        DhxCommon.closeWindow();
        this.callEvent("onClose", []);
        delete ZKPersSelect.PULL[this.options.gridName];
    }

    /**
     * 取消按钮事件
     */
    function buttonCancelClick() {
        DhxCommon.closeWindow();
        this.callEvent("onClose", []);
        delete ZKPersSelect.PULL[this.options.gridName];
    }

    function initGridEvent() {
        var that = this;
        this.leftGrid.grid.attachEvent("onCheck", function (rId, cInd, state) {
            var scope = this;
            if (state) {
                confirmSelect(that, function () {
                    leftGridOnCheck.apply(that, [rId, cInd, scope]);
                }, 1);
            } else {
                leftGridOnCancle.apply(that, [rId, cInd, scope]);
            }
        });
        // 左表格单击事件
        this.leftGrid.grid.attachEvent("onRowSelect", function (rId, cInd, state) {
            this.cells(rId, 0).changeState(state);
        });

        // 右表格选中事件
        this.rightGrid.grid.attachEvent("onCheck", function (rId, cInd, state) {
            var scope = this;
            if (state) {
                rightGridOnCheck.apply(that, [rId, cInd, scope]);
            } else {
                rightGridOnCancle.apply(that, [rId, cInd, scope]);
            }
        });
    }

    // 右表格取消选中事件
    function rightGridOnCancle(rId, cInd, scope) {
        delete this.options.rightCheckValue[rId];
        this.setRightTitle(I18n.getValue("common_selected") + "({0})".format(Object.keys(this.options.rightCheckValue) ? Object.keys(this.options.rightCheckValue).length : 0));
        this.setButtonDisabled();
    }

    // 右表格选中事件
    function rightGridOnCheck(rId, cInd, scope) {
        this.options.rightCheckValue[rId] = this.options.leftCheckValue[rId];
        this.setRightTitle(I18n.getValue("common_selected") + "({0})".format(Object.keys(this.options.rightCheckValue) ? Object.keys(this.options.rightCheckValue).length : 0));
        this.setButtonDisabled();
    }

    // 左表格选中事件
    function leftGridOnCheck(rId, cInd, grid) {
        addLeftRowToRight.apply(this, [rId]);
    }

    // 左表格取消选中事件
    function leftGridOnCancle(rId, cInd, grid) {
        delete this.options.rightAllValue[rId];
        delete this.options.rightCheckValue[rId];
        delete this.options.leftCheckValue[rId];
        this.reloadRightGrid();
    }

    // 右侧grid全选后回调事件
    function checkAllRightGrid(evt) {
        var that = this.allThis;
        var checkedRows = this.grid.getCheckedRows(0);
        var allRowIdArr = this.grid.getAllRowIds().split(",");
        if (checkedRows.length > 0) {
            allRowIdArr.forEach(function (id) {
                that.options.rightCheckValue[id] = that.options.rightAllValue[id];
            });
            this.allThis.setRightTitle(I18n.getValue("common_selected") + "({0})".format(Object.keys(that.options.rightCheckValue).length));
        } else {
            allRowIdArr.forEach(function (id) {
                delete that.options.rightCheckValue[id];
            });
            this.allThis.setRightTitle(I18n.getValue("common_selected") + "({0})".format(Object.keys(that.options.rightCheckValue).length));
        }
        that.setButtonDisabled();
    }

    // 左侧grid全选后回调事件
    function checkAllLeftGrid(evt) {
        var that = this.allThis;
        var checkedRows = this.grid.getCheckedRows(0);
        // 全选
        if (checkedRows && checkedRows.length > 0) {
            confirmSelect(that, function () {
                addLeftRowToRight.apply(that, [checkedRows]);
            }, that.leftGrid.grid.rowsCol.length);
            // 全不选
        } else {
            var leftAllRowIdsArr = that.leftGrid.grid.getAllRowIds().split(",");
            leftAllRowIdsArr.forEach(function (rowId) {
                delete that.options.rightAllValue[rowId];
                delete that.options.rightCheckValue[rowId];
                delete that.options.leftCheckValue[rowId];
            });
            that.reloadRightGrid();
        }
    }

    function addLeftRowToRight(checkedRows) {
        var that = this;
        var checkedRowsArr = checkedRows.split(",");
        checkedRowsArr.forEach(function (row) {
            var rightAllValue = that.options.rightAllValue;
            if (!rightAllValue.hasOwnProperty(row)) {
                var rowData = that.leftGrid.grid.rowsAr[row]._attrs.data;
                that.options.rightAllValue[row] = rowData;
                that.options.rightCheckValue[row] = rowData;
                that.options.leftCheckValue[row] = rowData;
            }
        });
        that.reloadRightGrid();
    }

    function confirmSelect(select, func, num) {
        num = num || 0;
        if (select.options.value) {
            num = num + select.options.value.split(",").length;
        }
        if (num > MAX_SELECT_NUM) {
            messageBox({messageType: "alert", text: I18n.getValue("common_widget_limitNum").format(MAX_SELECT_NUM)});
        } else {
            func();
        }
    }

    ZKPersSelect.prototype.reloadRightGrid = function () {
        var that = this;
        that.rightGrid.grid.clearAll();
        var rightAllValue = this.options.rightAllValue;
        var rightCheckValue = this.options.rightCheckValue;
        $.each(rightAllValue, function (id, data) {
            that.rightGrid.grid.addRow(id, data).check;
            if (rightCheckValue.hasOwnProperty(id)) {
                that.rightGrid.grid.cells(id, 0).changeState(true);
            }
        });
        var rightCheckNum = Object.keys(this.options.rightCheckValue).length;
        this.setRightTitle(I18n.getValue("common_selected") + "({0})".format(Object.keys(this.options.rightCheckValue) ? rightCheckNum : 0));
        this.setButtonDisabled();
        // 修复左侧取消勾选右侧依旧全选
        if (that.rightGrid.grid.getCheckedRows(0).split(",").length < that.rightGrid.grid.getAllRowIds().split(",").length || rightCheckNum <= 0) {
            that.rightGrid.setCheckState(false);
        }
    }

    ZKPersSelect.prototype.init = function () {
        var gridName = this.options.gridName;
        var layoutId = gridName + "Grid";
        var layout = new dhtmlXLayoutObject(layoutId, "3W", sysCfg.dhxSkin);
        var layout_a = this.options.enableRTL ? "c" : "a";
        var layout_c = this.options.enableRTL ? "a" : "c";
        layout.cells(layout_a).hideHeader();
        layout.cells("b").hideHeader();
        layout.cells(layout_c).hideHeader();
        layout.cells(layout_a).attachObject(gridName + "Left");
        layout.cells("b").attachObject(gridName + "Center");
        layout.cells(layout_c).attachObject(gridName + "Right");
        layout.setSeparatorSize(0, 0);
        layout.setSeparatorSize(1, 0);
        var cw = (document.getElementById(layoutId).clientWidth) / 10;
        layout.cells(layout_a).setWidth(cw * 2.2);
        layout.cells("b").setWidth(cw * 4.2);
        layout.cells(layout_c).setWidth(cw * 3.6);

        $(document.getElementById(gridName + "Top")).html(decodeURIComponent(this.options.content.replace(/[+]+/g, " ")));
        delete this.options.content;
        // 定义全局变量
        this.options.rightAllValue = this.options.rightAllValue || {};
        this.options.rightCheckValue = this.options.rightCheckValue || {};
        this.options.rightCancleCheckValue = this.options.rightCancleCheckValue || {};
        this.options.leftCheckValue = this.options.leftCheckValue || {};
        this.options.treeClickValue = this.options.treeClickValue || {};
        this.options.isIncludeLower = true;
        //
        var leftTreeElement = document.getElementById("leftTree" + gridName);

        var treeBoxTop = document.createElement("div");
        treeBoxTop.id = "leftTreeTop" + gridName;
        treeBoxTop.className = "select_tree_top_box";
        leftTreeElement.appendChild(treeBoxTop);

        var image1 = document.createElement("span");
        /*image1.src = "public/images/plus.gif";*/
        image1.className = "plus_minus_img icv-head-plus";
        image1.addEventListener("click", function () {
            var tree = ZKUI.Tree.get("leftTreeBox" + gridName).tree;
            tree.openAllItems(0);
        })
        treeBoxTop.appendChild(image1);

        var image2 = document.createElement("span");
        /*image2.src = "public/images/minus.gif";*/
        image2.className = "plus_minus_img icv-head-minus";
        image2.addEventListener("click", function () {
            var tree = ZKUI.Tree.get("leftTreeBox" + gridName).tree;
            tree.closeAllItems(0);
        })
        treeBoxTop.appendChild(image2);

        var spanElement = document.createElement("span");
        spanElement.id = layoutId + "-TreeCheckboxSpan";
        loadUIToDiv("input", "#" + layoutId + "-TreeCheckboxSpan", {
            id: layoutId + "-TreeCheckbox",
            useInputReq: true,
            type: "checkbox",
            value: true,
            eventCheck: true,
        })

        var _this = this;
        setTimeout(function() {
             $("#"+layoutId + "-TreeCheckbox")[0].addEventListener("click", function (event) {
                    _this.options.isIncludeLower = !_this.options.isIncludeLower;
                    var isIncludeLower = _this.options.isIncludeLower;
                    var deptId = "";
                    if (_this.options.treeClickValue["clickValue"]) {
                        deptId = _this.options.treeClickValue["clickValue"].id;
                    }
                     console.log("isIncludeLower", isIncludeLower)
                    _this.leftGrid.reload(function () {
                        _this.checkClickRows();
                    }, {
                        deptId: deptId, isIncludeLower: isIncludeLower
                    })
                });
        }, 500);

        this.treeTitleInput = treeBoxTop.appendChild(spanElement);
        this.treeTitleInput.allThis = this;

        var span = document.createElement("span");
        span.innerText = I18n.getValue("common_tree_containsSubordinate");
        treeBoxTop.appendChild(span);

        var treeBox = document.createElement("div");
        treeBox.id = "leftTreeBox" + gridName;
        treeBox.className = "select_tree_box";
        leftTreeElement.appendChild(treeBox);

        var _opts = JSON.parse(JSON.stringify(this.options));
        this.options.treeOptions = JSON.parse(JSON.stringify(_opts));
        this.options.treeOptions.onClick = "onSelectTreeClick";
        this.options.treeOptions.dynamic = "true";
        this.options.treeOptions.id = treeBox.id;
        this.options.treeOptions.url = this.options.treeOptions.deptQuery;
        this.treeBox = new ZKUI.Tree(this.options.treeOptions);
        this.treeBox.treeSelect = this;

        var _opts = JSON.parse(JSON.stringify(this.options));
        this.options.leftOptions = JSON.parse(JSON.stringify(_opts));
        this.options.leftOptions.gridName = "center" + gridName;
        // this.options.leftOptions.gridType = "right";
        this.options.leftOptions.onCheckA = "checkAllLeftGrid"
        // 保留参数,暂时无用
        this.options.leftOptions.queryParam = {type: "noSelected", selectId: ""};
        this.leftGrid = new ZKUI.Grid(this.options.leftOptions);
        this.leftGrid.allThis = this;

        this.options.rightOptions = JSON.parse(JSON.stringify(_opts));
        this.options.rightOptions.gridName = "right" + gridName;
        this.options.rightOptions.gridType = "right";
        this.options.rightOptions.nopaging = true;
        this.options.rightOptions.onCheckA = "checkAllRightGrid";
        if (this.options.copy) {
            this.options.rightOptions.originSort = true
        }
        // 保留参数,暂时无用
        this.options.rightOptions.queryParam = {type: "selected", selectId: ""};
        this.rightGrid = new ZKUI.Grid(this.options.rightOptions);
        this.rightGrid.allThis = this;

        this.setLeftTitle(this.options.leftTitle || I18n.getValue("common_alternative"));
        this.options.rightTitle = this.options.rightTitle || (I18n.getValue("common_selected") + "({0})");
        this.setRightTitle(this.options.rightTitle.format(this.options.rightCheckValue ? Object.keys(this.options.rightCheckValue).length : 0));
        if (!this.options.noAutoDisable) {
            if (Object.keys(this.options.rightCheckValue).length <= 0) {
                $("#" + this.options.gridName + "ok").attr("disabled", true);
            } else {
                $("#" + this.options.gridName + "ok").attr("disabled", false);
            }
        }
        if (document.getElementById("center" + gridName)) {
            ZKUI.Searchbar.get("center" + gridName).allThis = this;
            var newGridName = "centergridbox" + gridName.substring(7, gridName.length);
            var thisOptions = this.options;
            ZKUI.Searchbar.get("center" + gridName).attachEvent("onQuery", function () {
                this.allThis.checkClickRows();
            });
            ZKUI.Searchbar.get("center" + gridName).attachEvent("onBeforeClear", function () {
                if (Object.keys(thisOptions.treeClickValue).length > 0) {
                    this.allThis.leftGrid.options.queryParam["deptId"] = "";
                    delete thisOptions.treeClickValue["clickValue"];
                }
            });
            ZKUI.Searchbar.get("center" + gridName).attachEvent("onAfterLoad", function () {
                if (Object.keys(thisOptions.treeClickValue).length > 0) {
                    $("#searchText" + newGridName).append("<span style='display:inline-block'>" + I18n.getValue("base_printTemplate_dept") + ":</span><span style='display:inline-block'>(" + thisOptions.treeClickValue["clickValue"].text + ")&nbsp;&nbsp;</span>");
                }
            });
        }
    }



    ZKPersSelect.prototype.setLeftTitle = function (t) {
        $(document.getElementById(this.options.gridName + "Center")).children(".select_content_title").html(t);
    }

    ZKPersSelect.prototype.setRightTitle = function (t) {
        if (this.options.rightSort) {
            t = t + "<b>" +
                "<a href=\"javascript:selectUp('" + this.options.gridName + "')\"><img src='public/images/up.png'/></a>" +
                "<a href=\"javascript:selectDown('" + this.options.gridName + "')\"><img src='public/images/down.png'/></a>" +
                "</b>"
        }
        $(document.getElementById(this.options.gridName + "Right")).children(".select_content_title").html(t);
    }

    ZKPersSelect.prototype.setButtonDisabled = function (t) {
        if (!this.options.noAutoDisable) {
            if (Object.keys(this.options.rightCheckValue).length <= 0) {
                $("#" + this.options.gridName + "ok").attr("disabled", true);
            } else {
                $("#" + this.options.gridName + "ok").attr("disabled", false);
            }
        }
    }

    ZKPersSelect.prototype.checkClickRows = function () {
        // 重载回调 过滤默认选中
        var that = this;
        var rightAllValue = this.options.rightAllValue;
        if (Object.keys(rightAllValue).length > 0) {
            var leftGridIdsArr = that.leftGrid.grid.getAllRowIds(0).split(",");
            leftGridIdsArr.forEach(function (id) {
                if (rightAllValue.hasOwnProperty(id)) {
                    that.leftGrid.grid.cells(id, 0).changeState(false);
                }
            });
        }
    }

    // 树点击事件
    function onSelectTreeClick(id) {
        var dhxTree = this;
        var zkTree = ZKUI.Tree.get(dhxTree.parentObject.id);
        var item = dhxTree.item(id);
        zkTree.treeSelect.options.treeClickValue["clickValue"] = item;
        var isIncludeLower = zkTree.treeSelect.options.isIncludeLower;
        // 重载左grid
        zkTree.treeSelect.leftGrid.reload(function () {
            var gridName = this.gridName;
            this.allThis.checkClickRows();
            if (document.getElementById("searchBox" + gridName)) {
                loadSearchText(gridName);
            }
        }, {
            deptId: item.id,
            isIncludeLower: isIncludeLower
        })
    }

    // 获取控件对象
    ZKPersSelect.get = function (id) {
        return ZKPersSelect.PULL[id];
    }

    // 获取右侧选中项ids
    ZKPersSelect.prototype.getRightGridSelectIds = function () {
        var rightGridIds = this.rightGrid.grid.getCheckedRows(0);
        return rightGridIds;
    }

    // 获取右侧选中行数据
    ZKPersSelect.prototype.getSelectRows = function () {
        var val = this.rightGrid.grid.getCheckedRows(0);
        var rows = [];
        var that = this;
        if (val) {
            var ids = val.split(",");
            ids.forEach(function (id) {
                var row = that.rightGrid.grid.getRowData(id);
                if (row) {
                    rows.push(row);
                }
            });
        }
        return rows;
    }

    window.checkAllRightGrid = checkAllRightGrid;
    window.checkAllLeftGrid = checkAllLeftGrid;
    window.onSelectTreeClick = onSelectTreeClick;
    window.ZKUI = window.ZKUI || {};
    window.ZKUI.PersSelect = ZKPersSelect;
})();