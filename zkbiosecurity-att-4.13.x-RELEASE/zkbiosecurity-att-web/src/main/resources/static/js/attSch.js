/*分组0/部门1/人员2*/
var attPersonSchType;

/*group、pers*/
var attPersonSchPageType;

/*选择的人员姓名集合*/
var attPersonSchGridNames;
/*保存选择的部门ID*/
var attPersonSchDeptTreeCheckIds;
/*选择的部门名称集合*/
var attPersonSchDeptTreeCheckNames;

/*选择的分组名称集合*/
var attGroupSchNames = "";

/*操作集合名称提示长度*/
var attNamesSize = 100;

/*点击部门树设置id和部门名称集合*/
function attPersonSchDeptTreeCheck(id) {
    var tree = ZKUI.Tree.get("attPersonSchDeptTree").tree;
    attPersonSchDeptTreeCheckIds = tree.getAllChecked();

    attPersonSchDeptTreeCheckNames = "";
    var treeCheckIdsArray = attPersonSchDeptTreeCheckIds.split(",");
    var length = treeCheckIdsArray.length;
    for (var i = 0; i < length; i++) {
        attPersonSchDeptTreeCheckNames += " \"" + tree.getItemText(treeCheckIdsArray[i]) + "\" ";
    }
}

function isSelect(bar) {
    // 分组排班
    if (attPersonSchPageType == "group") {

        var rightGridName = bar.gridName || "gridbox";
        var rightGrid = ZKUI.Grid.get(rightGridName);
        var checkedRowsIds = rightGrid.grid.getCheckedRows(0);

        var leftGridName = rightGridName.replace('right', 'left');
        var leftGrid = ZKUI.Grid.get(leftGridName);
        var leftGridCheckedRowsIds = leftGrid.grid.getCheckedRows(0);

        if (checkedRowsIds) {
            var checkedRowsIdsArray = checkedRowsIds.split(",");
            var length = checkedRowsIdsArray.length;
            attPersonSchGridNames = "";
            for (var i = 0; i < length; i++) {
                var rowData = rightGrid.grid.getRowData(checkedRowsIdsArray[i]);
                if (rowData) {
                    attPersonSchGridNames += " \"" + rowData.personName + "\" ";
                }
            }
            attPersonSchType = "2";
            return checkedRowsIds;
        } else if (leftGridCheckedRowsIds) {
            var checkedRowsIdsArray = leftGridCheckedRowsIds.split(",");
            var length = checkedRowsIdsArray.length;
            attGroupSchNames = "";
            for (var i = 0; i < length; i++) {
                var rowData = leftGrid.grid.getRowData(checkedRowsIdsArray[i]);
                if (rowData) {
                    attGroupSchNames += " \"" + rowData.groupName + "\" ";
                }
            }
            attPersonSchType = "0";
            return leftGridCheckedRowsIds;
        } else {
            openMessage(msgType.warning, I18n.getValue("att_personSch_checkGroupFirst"));
            return "";
        }

    } else {
        // 人员排班、部门排班
        var gridName = bar.gridName || "gridbox";
        var grid = ZKUI.Grid.GRID_PULL[gridName].grid;
        var checkedRowsIds = grid.getCheckedRows(0);

        var tree = ZKUI.Tree.get("attPersonSchDeptTree").tree;
        attPersonSchDeptTreeCheckIds = tree.getAllChecked();

        if (checkedRowsIds) {
            var checkedRowsIdsArray = checkedRowsIds.split(",");
            var length = checkedRowsIdsArray.length;
            attPersonSchGridNames = "";
            for (var i = 0; i < length; i++) {
                var rowData = grid.getRowData(checkedRowsIdsArray[i]);
                if (rowData) {
                    attPersonSchGridNames += " \"" + rowData.personName + "\" ";
                }
            }
            attPersonSchType = "2";
            return checkedRowsIds;
        } else if (attPersonSchDeptTreeCheckIds) {
            attPersonSchType = "1";
            return attPersonSchDeptTreeCheckIds;
        } else {
            openMessage(msgType.warning, I18n.getValue("att_personSch_checkDeptOrPersFirst"));
            return "";
        }
    }
}

/*周期排班*/
function attPersonSchCycleSch(f, e, b) {
    var ids = isSelect(e);
    if (ids) {
        // 人员周期排班
        if (attPersonSchType == 2) {
            var a = e.gridName || "gridbox";
            f = toURL(f, "type=" + attPersonSchType, "ids=" + ids);
            var d = $.extend({
                    path: fillParamsFromGrid(a, ids, f),
                    gridName: a
                },
                JSON.parse(JSON.stringify(b)));
            DhxCommon.createWindow(d)
        } else {

            var names = attPersonSchDeptTreeCheckNames;
            if (attPersonSchType == 0) {
                names = attGroupSchNames;
            }

            var namesLength = names.length;
            if (namesLength > attNamesSize) {
                names = names.substring(0, attNamesSize) + "...";
            }

            var text = formatParams(I18n.getValue("att_personSch_sureToCycleSchDeptOrGroup"), [names]);
            if (attPersonSchType == 0) {
                text = formatParams(I18n.getValue("att_personSch_sureToCycleSchDeptOrGroup"), [names]);
            }

            actionConfirm(function (result) {
                if (result) {
                    var a = e.gridName || "gridbox";
                    f = toURL(f, "type=" + attPersonSchType, "ids=" + ids);
                    var d = $.extend({
                        path: fillParamsFromGrid(a, ids, f),
                        gridName: a
                    },
                    JSON.parse(JSON.stringify(b)));
                    DhxCommon.createWindow(d)
                }
            }, text);
        }
    }
}

/*临时排班*/
function attPersonSchTempSch(f, e, b) {
    var ids = isSelect(e);
    if (ids) {
        if (attPersonSchType == 2) {
            var a = e.gridName || "gridbox";
            f = toURL(f, "type=" + attPersonSchType, "ids=" + ids);
            var d = $.extend({
                    path: fillParamsFromGrid(a, ids, f),
                    gridName: a
                },
                JSON.parse(JSON.stringify(b)));
            DhxCommon.createWindow(d)
        } else {


            var names = attPersonSchDeptTreeCheckNames;
            if (attPersonSchType == 0) {
                names = attGroupSchNames;
            }

            var namesLength = names.length;
            if (namesLength > attNamesSize) {
                names = names.substring(0, attNamesSize) + "...";
            }

            var text = formatParams(I18n.getValue("att_personSch_sureToTempSchDeptOrGroup"), [names]);
            if (attPersonSchType == 0) {
                text = formatParams(I18n.getValue("att_personSch_sureToTempSchDeptOrGroup"), [names]);
            }
            actionConfirm(function (result) {
                if (result) {
                    var a = e.gridName || "gridbox";
                    f = toURL(f, "type=" + attPersonSchType, "ids=" + ids);
                    var d = $.extend({
                            path: fillParamsFromGrid(a, ids, f),
                            gridName: a
                        },
                        JSON.parse(JSON.stringify(b)));
                    DhxCommon.createWindow(d)
                }
            }, text);
        }
    }
}

/*清除周期排班*/
function attPersonSchCleanCycleSch(id, bar, opt) {
    var ids = isSelect(bar);
    if (ids) {
        var names = attPersonSchGridNames;
        if (attPersonSchType == 1) {
            names = attPersonSchDeptTreeCheckNames;
        } else if (attPersonSchType == 0) {
            names = attGroupSchNames;
        }

        var namesLength = names.length;
        if (namesLength > attNamesSize) {
            names = names.substring(0, attNamesSize) + "...";
        }

        var text = formatParams(I18n.getValue("att_personSch_sureCleanCycleSchDeptOrGroup"), [names, $("input[name='startDate']").val(), $("input[name='endDate']").val()]);
        if (attPersonSchType == 2) {
            text = formatParams(I18n.getValue("att_personSch_sureCleanCycleSch"), [names, $("input[name='startDate']").val(), $("input[name='endDate']").val()]);
        }
        var gridName = bar.gridName || "gridbox";
        actionConfirm(function (result) {
            if (result) {
                openMessage(msgType.loading);
                var param = splitURL(fillParamsFromGrid(gridName, ids, id));
                param.data.ids = ids;
                param.data.type = attPersonSchType;
                param.data.startDate = $("input[name='startDate']").val();
                param.data.endDate = $("input[name='endDate']").val();
                param.data.names = names;
                param.data.includeLower = attGlobalIsIncludeLower;
                $.ajax({
                    url: param.url,
                    type: "post",
                    data: param.data,
                    success: function (result) {
                        closeMessage();
                        dealRetResult(eval(result), function () {
                            if (opt.callback && typeof (window[opt.callback]) == "function") {
                                window[opt.callback]();
                            } else if (typeof (opt.callback) == "function") {
                                opt.callback();
                            } else {
                                ZKUI.Grid.reloadGrid(gridName);
                            }
                        });
                    }
                });
            }
        }, text);
    }
}

/*清除临时排班*/
function attPersonSchCleanTempSch(id, bar, opt) {
    var ids = isSelect(bar);
    if (ids) {
        var names = attPersonSchGridNames;
        if (attPersonSchType == 1) {
            names = attPersonSchDeptTreeCheckNames;
        } else if (attPersonSchType == 0) {
            names = attGroupSchNames;
        }

        var namesLength = names.length;
        if (namesLength > attNamesSize) {
            names = names.substring(0, attNamesSize) + "...";
        }

        var text = formatParams(I18n.getValue("att_personSch_sureCleanTempSchDeptOrGroup"), [names, $("input[name='startDate']").val(), $("input[name='endDate']").val()]);
        if (attPersonSchType == 2) {
            text = formatParams(I18n.getValue("att_personSch_sureCleanTempSch"), [names, $("input[name='startDate']").val(), $("input[name='endDate']").val()]);
        }
        var gridName = bar.gridName || "gridbox";
        actionConfirm(function (result) {
            if (result) {
                openMessage(msgType.loading);
                var param = splitURL(fillParamsFromGrid(gridName, ids, id));
                param.data.ids = ids;
                param.data.type = attPersonSchType;
                param.data.startDate = $("input[name='startDate']").val();
                param.data.endDate = $("input[name='endDate']").val();
                param.data.names = names;
                param.data.includeLower = attGlobalIsIncludeLower;
                $.ajax({
                    url: param.url,
                    type: "post",
                    data: param.data,
                    success: function (result) {
                        closeMessage();
                        dealRetResult(eval(result), function () {
                            if (opt.callback && typeof (window[opt.callback]) == "function") {
                                window[opt.callback]();
                            } else if (typeof (opt.callback) == "function") {
                                opt.callback();
                            } else {
                                ZKUI.Grid.reloadGrid(gridName);
                            }
                        });
                    }
                });
            }
        }, text);
    }
}

function formatParams(source, params) {
    $.each(params, function (i, n) {
        source = source.replace(new RegExp("\\{" + i + "\\}", "g"), n);
    });
    return source;
}