// 定义全局变量
/*是否包含下级*/
var attGlobalIsIncludeLower;
/*当前部门树ID*/
var attGlobalDeptTreeId;
/*当前gridName*/
var attGlobalGridName;
/*是否显示包含下级选项*/
var attGlobalShowIncludeLower;
/*当前点击的部门ID*/
var attGlobalDeptTreeClickId;

/**初始化部门树的顶级按钮*/
function attGlobalInitDeptTree(layout) {
    // 默认包含子部门
    attGlobalIncludeLowerClick(true, false);

    var containsSubordinateText = I18n.getValue("common_tree_containsSubordinate");

    var html = "<span><div id='idAttDeptIncludeLower' class='tree_header_box'>" +
        "<span onclick='attGlobalImgClick(1)' class='plus_minus_img icv-head-plus'></span>" +
        "<span onclick='attGlobalImgClick(0)' class='plus_minus_img icv-head-minus'></span>";
    if (attGlobalShowIncludeLower) {
        html += "<span id='attGlobalIncludeLowerCheckboxSpan'></span><span>" + containsSubordinateText + "</span></div></span>";
    }
    layout.cells("a").setText(html);

    loadUIToDiv("input", "#attGlobalIncludeLowerCheckboxSpan", {
        id: "attGlobalIncludeLowerCheckbox",
        useInputReq: true,
        type: "checkbox",
        value: attGlobalIsIncludeLower,
        eventCheck: true,
        onclick: "attGlobalIncludeLowerChange()"
    })

    layout.attachEvent("onExpand", function (name) {
        resizeUI();
        $("#idAttDeptIncludeLower").show();
        loadUIToDiv("input", "#attGlobalIncludeLowerCheckboxSpan", {
            id: "attGlobalIncludeLowerCheckbox",
            useInputReq: true,
            type: "checkbox",
            value: attGlobalIsIncludeLower,
            eventCheck: true,
            onclick: "attGlobalIncludeLowerChange()"
        })
    });

    layout.attachEvent("onCollapse", function (name) {
        resizeUI();
        $("#idAttDeptIncludeLower").hide();
    });
}

/** 选中控制部门树是否包含下级 */
function attGlobalIncludeLowerChange() {
    // 由loadUIToDiv的onclick的点击事件，选中为false，未选中为true
    attGlobalIncludeLowerClick(!$("#attGlobalIncludeLowerCheckbox").prop("checked"), true)
}

/**是否包含下级部门*/
function attGlobalIncludeLowerClick(value, refresh) {
    attGlobalIsIncludeLower = value;
    if(ZKUI.Tree.get(attGlobalDeptTreeId)){
        var tree = ZKUI.Tree.get(attGlobalDeptTreeId).tree;
        tree.enableThreeStateCheckboxes(value);
        if (refresh) {
            var selectDeptId = tree.getSelected();
            if (selectDeptId) {
                attGlobalDeptTreeClick(selectDeptId);
            }
        }
    }
}

/**部门树的展开与合并*/
function attGlobalImgClick(flag) {
    var tree = ZKUI.Tree.get(attGlobalDeptTreeId).tree;
    if (flag == 1) {
        tree.openAllItems(0);
    } else {
        tree.closeAllItems(0);
    }
}

/*部门树点击事件刷新列表*/
function attGlobalDeptTreeClick(id) {
    attGlobalDeptTreeClickId = id;
    ZKUI.Grid.reloadGrid(attGlobalGridName, function () {
    }, {deptId: id, isIncludeLower: attGlobalIsIncludeLower});
}

/*刷新列表*/
function attGlobalReloadGrid() {
    ZKUI.Grid.reloadGrid(attGlobalGridName);
    //ZKUI.Grid.get(attGlobalGridName).reload()
}

/*导出模版，提交表单*/
function attGlobalImportTemplate() {
    $("#attImportTemplateFrom").ajaxSubmit({
        async:false,
        success: function(result){
        }
    });
}