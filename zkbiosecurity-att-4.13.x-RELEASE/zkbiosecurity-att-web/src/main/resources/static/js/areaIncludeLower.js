//定义全局变量
/*是否包含下级*/
var attGlobalAreaIsIncludeLower;
/*当前gridName*/
var attGlobalAreaGridName;
/*当前区域树ID*/
var attGlobalAreaTreeId;

/**初始化区域树的顶级按钮*/
function initAttGlobalAreaTree(layout) {

    // 默认包含子区域
    attGlobalAreaIncludeLowerLink(true, false);

    var containsSubordinateText = I18n.getValue("common_tree_containsSubordinate");
    var html = "<span><div id='idAttDeptIncludeLower' class='tree_header_box'>" +
            "<span onclick='attGlobalAreaImgClick(1)' class='plus_minus_img icv-head-plus'></span>" +
            "<span onclick='attGlobalAreaImgClick(0)' class='plus_minus_img icv-head-minus'></span>" +
        "<span id='attGlobalAreaIncludeLowerCheckboxSpan'></span><span>" + containsSubordinateText + "</span></div></span>";
    layout.cells("a").setText(html);

    loadUIToDiv("input", "#attGlobalAreaIncludeLowerCheckboxSpan", {
        id: "attGlobalAreaIncludeLowerCheckbox",
        useInputReq: true,
        type: "checkbox",
        value: attGlobalAreaIsIncludeLower,
        eventCheck: true,
        onclick: "attGlobalAreaIncludeLowerLinkChange()"
    })

    layout.attachEvent("onExpand", function (name) {
        resizeUI();
        $("#idAttDeptIncludeLower").show();
        loadUIToDiv("input", "#attGlobalAreaIncludeLowerCheckboxSpan", {
            id: "attGlobalAreaIncludeLowerCheckbox",
            useInputReq: true,
            type: "checkbox",
            value: attGlobalAreaIsIncludeLower,
            eventCheck: true,
            onclick: "attGlobalAreaIncludeLowerLinkChange()"
        })
    });

    layout.attachEvent("onCollapse", function (name) {
        resizeUI();
        $("#idAttDeptIncludeLower").hide();
    });
}

/** 选中控制区域树是否包含下级 */
function attGlobalAreaIncludeLowerLinkChange() {
    // 由loadUIToDiv的onclick的点击事件，选中为false，未选中为true
    attGlobalAreaIncludeLowerLink(!$("#attGlobalAreaIncludeLowerCheckbox").prop("checked"), true)
}

/**是否包含下级区域*/
function attGlobalAreaIncludeLowerLink(value, refresh) {
    attGlobalAreaIsIncludeLower = value;
    if(ZKUI.Tree.get(attGlobalAreaTreeId)){
        var tree = ZKUI.Tree.get(attGlobalAreaTreeId).tree;
        tree.enableThreeStateCheckboxes(value);
        if (refresh) {
            var selectAreaId = tree.getSelected();
            if (selectAreaId) {
                attGlobalAreaTreeClick(selectAreaId);

            }
        }
    }
}

/**部门树的展开与合并*/
function attGlobalAreaImgClick(flag) {
    var tree = ZKUI.Tree.get(attGlobalAreaTreeId).tree;
    if (flag == 1) {
        tree.openAllItems(0);
    } else {
        tree.closeAllItems(0);
    }
}

function attGlobalAreaTreeClick(id) {
    ZKUI.Grid.reloadGrid(attGlobalAreaGridName, function () {
    }, {areaId: id, inAreaId: "", isIncludeLower: attGlobalAreaIsIncludeLower});
}

function attGlobalAreaReloadGrid() {
    ZKUI.Grid.reloadGrid(attGlobalAreaGridName);
}