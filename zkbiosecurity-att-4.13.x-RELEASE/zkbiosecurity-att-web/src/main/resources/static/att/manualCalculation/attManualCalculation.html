<#assign gridName="attManualCalculationGrid${uuid!}">
<style>
    .content_div {
        position: relative;
    }
</style>
<script type="text/javascript">

    /*参考deptIncludeLower.js配置是否显示下级部门树，以及部门树相关操作*/
    /*当前部门树ID*/
    attGlobalDeptTreeId = "tree${uuid!}";
    /*当前gridName*/
    attGlobalGridName = "${gridName}";
    /*是否显示包含下级*/
    attGlobalShowIncludeLower = true;

    //定义全局变量
    var attCalculateDeptIds, attCalculatePersonIds, attCalculatePins ,attCalculateParam;

	function attManualCalculate(path) {
	
	    attCalculateParam = "";
	    var tree = ZKUI.Tree.get("tree${uuid!}").tree;
	    attCalculateDeptIds = tree.getAllChecked();
	
	    if (attCalculateDeptIds) {
	        attCalculateParam = "dept";
	    }
	
	    var idArray = [];
	    var pinArray = [];
	
	    var attCalcuation = ZKUI.Grid.get("${gridName}").grid;
	    var tempPersonIds = attCalcuation.getCheckedRows(0);//查看人员是否被选中
	    if(tempPersonIds){
	        attCalcuation.forEachRow(function (id) {
	            var cbxValue =  attCalcuation.cells(id, 0).getValue();
	            if (cbxValue == 1) {
	                pinArray.push(attCalcuation.cells(id, 1).getValue());
	                idArray.push(id);
	            }
	        });
	    }
	
	    if(pinArray != null && pinArray != ""){
	        attCalculateParam = "pers";
	    }
	    attCalculatePersonIds = idArray.join(",");
	    attCalculatePins = pinArray.join(",");

        // 选中人员或者部门时直接弹出进行考勤计算
	    if (attCalculatePersonIds || attCalculateDeptIds) {
            attCalculateCreateWindow();
        } else {
	        // 未选中人员或者部门时，提示：确认对所有人员进行考勤计算吗？
            actionConfirm(function (result) {
                if (result) {
                    attCalculateCreateWindow();
                }
            }, "<@i18n 'att_statistical_sureAllCalculate'/>");
        }
	}

	function attCalculateCreateWindow() {
        if(attCalculateParam == "dept") {
            $.ajax({
                async: false,
                data: {
                    deptIds: attCalculateDeptIds
                },
                dataType: "json",
                type: "post",
                url: "attManualCalculation.do?getDeptPins",
                success: function (result) {
                    attCalculatePins = result.pins;
                }
            });
        }
        var paths = "skip.do?page=att_manualCalculation_editManualCalculation";
        var opts = {
            path: paths,
            width: 400,
            height: 300,
            title: "<@i18n 'att_op_calculation'/>"
        };
        DhxCommon.createWindow(opts);
    }
</script>
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="pin"  maxlength="30" title="att_person_pin" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="likeName"  maxlength="30" title="pers_person_wholeName" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    <@ZKUI.Layout style="position:absolute; top:70px;bottom:0px;left:0px;right:0px;height:auto;" onInit="attGlobalInitDeptTree">
        <@ZKUI.Cell width="240" treeId="tree${uuid!}">
            <@ZKUI.Tree dynamic="true" id="tree${uuid!}" type="checkbox" url="authDepartment.do?dynaTree&showPersonCount=false" onClick="attGlobalDeptTreeClick"></@ZKUI.Tree>
        </@ZKUI.Cell>
        <@ZKUI.Cell hideHeader="true">
            <@ZKUI.Toolbar>
                <@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:manualCalculation:refresh"/>
                <@ZKUI.ToolItem id="attManualCalculation.do?calculate" text="att_op_calculation" img="att_calculate.png" action="attManualCalculate" permission="att:manualCalculation:calculate"/>
            </@ZKUI.Toolbar>
            <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttManualCalculationItem" query="attManualCalculation.do?list"/>
        </@ZKUI.Cell>
    </@ZKUI.Layout>
</@ZKUI.GridBox>