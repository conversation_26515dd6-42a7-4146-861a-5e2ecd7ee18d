<#include '/public/template/editTemplate.html'>
<#macro editContent>
	<table class='tableStyle' id="editManualCalculationId">
		<tr>
			<!-- 开始时间 -->
			<th><label><@i18n 'common_startTime'/></label><span class='required'>*</span></th>
			<td><@ZKUI.Input id="beginTime" endId="endTime" type="datetime" name="startDatetimeBegin" todayRange="start" max="today" today="-2" hideLabel="true" noOverToday="true" readonly="false" title="common_startTime"/></td>
		</tr>
		<tr>
			<!-- 结束时间 -->
			<th><label><@i18n 'common_endTime'/></label><span class='required'>*</span></th>
			<td><@ZKUI.Input type="datetime" id="endTime" name="startDatetimeEnd" max="today" todayRange="end" today="true" noOverToday="true" hideLabel="true" readonly="false" title="common_endTime"/></td>
		</tr>
		<tr>
			<!-- 是否计算离职人员 -->
			<th><label><@i18n 'att_op_calculation_leave'/></label></th>
			<td>
    			<label for="yes">
					<@ZKUI.Input hideLabel="true" name="totalLeaver" id="yes" type="radio" value="true"/>
					<span><@i18n 'common_yes' /></span>
				</label>
                <label for="no">
					<@ZKUI.Input hideLabel="true" name="totalLeaver" id="no" type="radio" value="false" checked="checked"/>
                    <span><@i18n 'common_no' /></span>
				</label>
    		</td>
		</tr>
	</table>
	<div style="position: absolute;bottom: 0;">
		<span class="warningImage"></span> <span class="warningColor"><@i18n 'att_statistical_noticeTime'/></span>
	</div>
</#macro>
<#macro buttonContent>
	<button class='button-form' onclick="attCalculate()"><@i18n 'att_op_calculation'/></button>
	<button class='button-form  button-close' onclick="DhxCommon.closeWindow()"><@i18n 'common_op_close'/></button>
</#macro>
<script type='text/javascript'>
	// 当月第一天
	setMonthFirstDay("beginTime");
	// 最小前两个月
	var endTime = ZKUI.Input.PULL['endTime'].obj.getDate();
	var minDate = new Date(endTime.getFullYear(), endTime.getMonth() - 2);
	ZKUI.Input.get("beginTime").setMinDate(minDate);
	ZKUI.Input.get("endTime").setMinDate(minDate);
	
	function attCalculate()
	{
		var totalLeaver = $("input[name='totalLeaver']:checked").val();
        var startTime = $("#editManualCalculationId input[name='startDatetimeBegin']").val();
		var endTime = $("#editManualCalculationId input[name='startDatetimeEnd']").val();

        var pathUrl = "attManualCalculation.do?calculate";
        DhxCommon.closeWindow();
        messageBox({
            messageType: "confirm",
            text: "<@i18n 'att_statistical_sureCalculation'/>",
            callback: function(result) {
                if(result) {

                    onLoading(function() {
                        $.ajax({
                            async: true,
                            data: {
                                personIds: attCalculatePersonIds,
                                deptIds: attCalculateDeptIds,
                                startDateStr: startTime,
                                endDateStr: endTime,
                                pins: attCalculatePins,
                                isIncludeLower: attGlobalIsIncludeLower
                            },
                            dataType: "json",
                            type: "post",
                            url: "attManualCalculation.do?calculating",
                            success: function(result) {
                                closeMessage();
                                if (result.ret == 400) {
                                    //如果存在其它客户端在计算，则弹出提示
                                    messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: result.msg});
                                } else {
                                    var progressParams;
                                    if (attCalculateParam == "dept") {
                                        progressParams = {
                                            title: "<@i18n 'common_progress_proCmd'/>",
                                            dealPath: pathUrl,
                                            type: "single",//进度类型，取值single,public,custom
                                            height: 250,
                                            useReq:true,//使用请求参数作为进度条参数
                                            delay:5,//自动关闭时间(10秒后自动关闭)
                                            autoClose:true,//自动关闭
                                            data:{
                                                startDateStr:startTime,
                                                endDateStr:endTime,
                                                deptIds:attCalculateDeptIds,
                                                personIds: attCalculatePersonIds,
                                                pins:attCalculatePins,
                                                totalLeaver:totalLeaver,
                                                isIncludeLower: attGlobalIsIncludeLower
                                            }

                                        };
                                    } else {
                                        progressParams = {
                                            title: "<@i18n 'common_progress_proCmd'/>",
                                            dealPath: pathUrl,
                                            type: "single",
                                            height: 250,
                                            useReq:true,//使用请求参数作为进度条参数
                                            delay:5,//自动关闭时间(10秒后自动关闭)
                                            autoClose:true,//自动关闭
                                            data:{
                                                startDateStr:startTime,
                                                endDateStr:endTime,
                                                personIds:attCalculatePersonIds,
                                                pins:attCalculatePins,
                                                totalLeaver:totalLeaver,
                                                isIncludeLower: attGlobalIsIncludeLower
                                            }
                                        }
                                    }
                                    openProcess(progressParams);
                                }
                            },
                            error : function(XMLHttpRequest, textStatus, errorThrown) {
                                closeMessage();
                                messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
                            }
                        })
                    });
                }
            }
        });
	}

</script>