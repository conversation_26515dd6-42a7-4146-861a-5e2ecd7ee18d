<@ZKUI.SelectPersContent onSure="attAreaAfterSelectPerson" gridType = "right" showColumns="checkbox,personPin,personName,deptName" copy="true" textField="personPin"  vo="com.zkteco.zkbiosecurity.att.vo.AttPersonSelectItem" deptQuery= "authDepartment.do?dynaTree" query="attPerson.do?attAreaSelectPerson&linkId=${areaId}">
	<@ZKUI.Searchbar autoReload="false">
    	<@ZKUI.SearchTop>
    		<tr>
			    <td valign="middle">
			    	<@ZKUI.Input name="personPin"  maxlength="30" title="att_person_pin" type="text"/>
			    </td>
				<td valign="middle">
					<@ZKUI.Input name="likeName"  maxlength="30" title="pers_person_wholeName" type="text"/>
			    </td>
			</tr>
		</@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
</@ZKUI.SelectPersContent>
<script type="text/javascript">
	var areaId = "${areaId}";
	function attAreaAfterSelectPerson(value, text, event) {
        onLoading(function () {
			$.ajax({
				type: "POST",
				dataType: "json",
				url: "attAreaPerson.do?addPerson",
				data: {
					"personIds" : value,
					"areaId" : areaId
				},
				success: function (data)
				{
					if (data.ret == "ok")
					{
						openMessage(msgType.success);
						attGlobalAreaTreeClick(areaId);
					}
					else
					{
						openMessage(msgType.error, data.msg);
					}
				}
			});
        });
    }
</script>