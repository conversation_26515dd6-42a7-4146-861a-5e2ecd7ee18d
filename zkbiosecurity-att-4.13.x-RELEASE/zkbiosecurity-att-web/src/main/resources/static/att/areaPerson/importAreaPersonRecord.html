<#assign editPage = "true">
<#include "/public/template/editTemplate.html">
<#macro editContent>
<script type="text/javascript">
function uploadFileChange(obj) {
    var f = document.getElementById('attAreaPersonUpload').files[0];
    if(f){
        var label = $("#${formId} #id_uploadMsg");
        label.html(f.name);
        $('#${formId} #id_uploadFileButton').valid();

    } else {
        var label = $("#${formId} #id_uploadMsg");
        label.html("<@i18n 'base_file_notUpload'/>");//未上传
    }
}
</script>
<form action='attAreaPerson.do?import' method='post' id='${formId}' enctype='multipart/form-data'>
	<table class="imp_table" cellpadding="10" style="height:140px;margin: 10px 10px 10px 10px;">
		<tr style="margin: 10px 10px 10px 10px;">
			<td valign="middle" style="padding-right: 60px; min-width:80px;">
				<@i18n 'pers_import_fileFormat'/>
			</td>
			<td style="padding-right: 10px;">
				<@ZKUI.Input hideLabel="true" name="fileType" value="Excel" type="radio" checked="checked"/>
				Excel
			</td>
		</tr>
		<tr style="margin: 10px 10px 10px 10px;">
			<td valign="middle" style="padding-right: 60px;">
				<@i18n 'common_dev_selectFile'/>
			</td>
			<td style="padding-right: 10px;">
				<input id="id_uploadFileButton" name="uploadFileButton" type="button" value="<@i18n 'base_file_browse'/>" onClick="upload.click()"/>
				<label id="id_uploadMsg" onClick="upload.click()"><@i18n 'base_file_notUpload'/></label>
				<div style="visibility: hidden;height:0px">
				 <input id="attAreaPersonUpload" type="file" name="upload" onchange="uploadFileChange(this);"/>
				 </div>
			</td>
		</tr>
		<tr>
			<td valign="middle" colspan="2" style="padding-right: 10px;">
				<span  class="warningColor"><@i18n 'pers_import_startRowNote'/></span>
			</td>
		</tr>
		<tr>
			<td valign="middle" colspan="2" style="padding-right: 10px;">
				<span  class="warningColor"><@i18n 'att_areaPerson_importTip1'/></span>
			</td>
		</tr>
		<tr>
			<td valign="middle" colspan="2" style="padding-right: 10px;">
				<span  class="warningColor"><@i18n 'att_areaPerson_importTip2'/></span>
			</td>
		</tr>
	</table>
</form>
</#macro>
<#macro buttonContent>
<button class='button-form' id="${formId}OK"><@i18n 'common_edit_ok'/><tton>
<button class='button-form' onclick="DhxCommon.closeWindow()"><@i18n 'common_edit_cancel'/><tton>
</#macro>
<script type="text/javascript">
    function onImportInit() {
        var params = {};
        params.upload = document.getElementById('attAreaPersonUpload').files[0];
        params.clientId=this.options.clientId;
        $('#${formId}').ajaxSubmit({
            async:false,
            data: params,
            success: function(result){
                //dealRetResult(result);
            }
        });
        this.beginProgress(true);
    }
$().ready(function(){
	$("#${formId}").validate({
		debug : true,
		submitHandler: function(form){
			var fileName = $("#attAreaPersonUpload")[0].value;
            if(fileName == ""){
                messageBox({messageType : "alert", text : "<@i18n 'common_dev_selectFile'/>"});
                return false;
            }
            var pattenChar = /\.xl.{1,2}$/;
            if(!pattenChar.test(fileName))
			{
                messageBox({messageType : "alert", text : "<@i18n 'pers_import_selectCorrectFile'/>"});
                return false;
			}
            var opts = {
                useReq:true,//必须
                noAutoProcess:true,//阻断自动调动进度
                dealPath:"attAreaPerson.do?import",
                height:250,//弹窗高度
                type: "single",//进度类型，取值single,public,custom
                title:"<@i18n 'common_op_import'/>",
                onInit:"onImportInit",
				callback:"onImpportCallBack",
                hideStopButton:true,
                onFinish:"attGlobalAreaReloadGrid"
            }
            openProcess(opts);
		}
	});
});

//监听窗口关闭事件，联动关闭底层导入窗口
function onImpportCallBack() {
	//关闭页面时，监听关闭的onclick事件
	DhxCommon.getCurrentWindow().attachEvent("onClose", function(){
		setTimeout(function () {
			//联动延迟关闭底层窗口
			DhxCommon.closeWindow();
		},200);
		//关闭页面
		return true;
	});
}
</script>
