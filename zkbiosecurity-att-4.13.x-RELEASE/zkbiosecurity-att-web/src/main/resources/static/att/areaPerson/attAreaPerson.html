<#assign gridName="attAreaPersonGrid${uuid!}">
<script type="text/javascript">

	/*参考areaIncludeLower.js配置是否显示下级部门树，以及部门树相关操作*/
	/*当前gridName*/
	attGlobalAreaGridName ="${gridName}";
	/*当前区域树ID*/
	attGlobalAreaTreeId = "areaTree";

    //区域添加人员弹窗
    function attAreaAddPerson(gridName, obj, rid){
        var tree = ZKUI.Tree.get("areaTree").tree;
        var selectAreaId = tree.getSelected();
        var areaName = tree.getSelectedItemText();
        if ((selectAreaId == null || selectAreaId=="")) {
            messageBox({messageType: "alert", text: "<@i18n 'att_areaPerson_choiceArea'/>"});
            return;
        }
        var path = "skip.do?page=att_areaPerson_attAreaSelectPerson&areaId=" + selectAreaId;
        var opts = {
            path: path,
            width: 1100,
            height: 550,
            title: "<@i18n 'pers_common_addPerson'/>"+"("+areaName+")",
            gridName: "gridbox",
            // onSure:"afterSelectPerson"//回调事件
        };
        DhxCommon.createWindow(opts);
    }

    /*同步区域人员到设备*/
    function syncAreaPersonToDev(id, bar, opt) {
        var tree = ZKUI.Tree.get("areaTree").tree;
        var selectAreaId = tree.getSelected();
        var ids =  ZKUI.Grid.get("${gridName}").grid.getCheckedRows(0);
        if(!ids && !selectAreaId){
            messageBox({
                messageType : "alert",
                text : "<@i18n 'common_prompt_selectObj'/>"
            });
        }
        else {
            var text = opt.sureText || "<@i18n 'common_prompt_executeOperate'/>".format(opt.text);//"你确定要执行{0}操作吗？".format(opt.text);
            var path = id;
            deleteConfirm(function(result) {
                if (result) {
                    $.ajax({
                        url : path,
                        type : "post",
                        data : {
                            areaId : selectAreaId,
                            ids : ids
                        },
                        success : function(result) {
                            dealRetResult(eval(result), function() {
                                ZKUI.Grid.reloadGrid("${gridName}");
                            });
                        }
                    });
                }
            }, text);
        }
    }

</script>

<!--导出模版表单-->
<form style="display: none" method="POST" action="attAreaPerson.do?importTemplate" id="attImportTemplateFrom" enctype="multipart/form-data">
	<input type="hidden" name="reportType" value="XLS"/>
	<input type="hidden" name="tableNameParam" value="<@i18n "att_areaPerson_byAreaPerson"/>"/>
</form>

<@ZKUI.GridBox gridName="${gridName}">
<@ZKUI.Searchbar>
<@ZKUI.SearchTop>
	<tr>
		<td valign="middle">
			<@ZKUI.Input name="authAreaName"  maxlength="30" title="base_area_name" type="text"/>
		</td>
		<td valign="middle">
			<!-- 人员pin号 -->
			<@ZKUI.Input name="pin"  maxlength="30" title="att_person_pin" type="text"/>
		</td>
		<td valign="middle">
			<!-- 姓名 -->
			<@ZKUI.Input name="likeName"  maxlength="30" title="pers_person_wholeName" type="text"/>
		</td>
	</tr>
</@ZKUI.SearchTop>
<@ZKUI.SearchBelow>
	<tr>
		<td valign="middle">
			<!-- 部门 -->
			<@ZKUI.Input name="deptName"  maxlength="30" title="pers_dept_deptName" type="text"/>
		</td>
	</tr>
</@ZKUI.SearchBelow>
</@ZKUI.Searchbar>
<@ZKUI.Layout style="position:absolute; top:70px;bottom:0px;left:0px;right:0px;height:auto;" onInit="initAttGlobalAreaTree">
	<@ZKUI.Cell width="240" treeId="areaTree">
		<@ZKUI.Tree dynamic="true" id="areaTree" url="authArea.do?dynaTree&showPersonCount=true" onClick="attGlobalAreaTreeClick"></@ZKUI.Tree>
	</@ZKUI.Cell>
	<@ZKUI.Cell hideHeader="true">
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:areaPerson:refresh"/>
			<@ZKUI.ToolItem id="attAreaPerson.do?addPerson" action="attAreaAddPerson" text="att_areaPerson_addAreaPerson" img="comm_add.png" permission="att:areaPerson:addPerson"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="attAreaPerson.do?del&names={name}" action="commonDel" text="att_areaPerson_delAreaPerson" img="comm_del.png" permission="att:areaPerson:del"></@ZKUI.ToolItem>
            <@ZKUI.ToolItem id="attAreaPerson.do?export" type="export" action="commonExport" text="common_op_export" img="comm_export.png" permission="att:areaPerson:export"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem type="more" text="common_op_import" img="comm_import.png">
				<@ZKUI.ToolItem id="skip.do?page=att_areaPerson_importAreaPersonRecord" img="comm_import.png" action="commonOpen" text="att_areaPerson_importAreaPerson" title="pers_import_personInfo" width="500" height="280" permission="att:areaPerson:import"/>
				<@ZKUI.ToolItem id="attAreaPerson.do?importBatchDel" type="import" showImportProcess="true" text="att_areaPerson_importDelAreaPerson" onFinish="attGlobalAreaReloadGrid" title="pers_import_personInfo" width="500" height="250" permission="att:areaPerson:importBatchDel"/>
				<@ZKUI.ToolItem action="attGlobalImportTemplate" type="export" text="att_exception_downTemplate" title="att_exception_downTemplate" img="common_download.png" permission="att:areaPerson:import"/>
			</@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="attAreaPerson.do?setUserSms" action="commonOpenOperate" text="att_op_dataUserSms" title="att_op_dataUserSms" width="450" img="att_userSms.png" permission="att:areaPerson:setUserSms"/>
			<@ZKUI.ToolItem id="attAreaPerson.do?syncPerToDev" action="syncAreaPersonToDev" text="att_op_syncPers" width="500" img="comm_syncAllData.png" permission="att:areaPerson:syncPerToDev"/>
		</@ZKUI.Toolbar>
	<@ZKUI.Grid  vo="com.zkteco.zkbiosecurity.att.vo.AttAreaPersonItem" query="attAreaPerson.do?list"/>
	</@ZKUI.Cell>
</@ZKUI.Layout>
</@ZKUI.GridBox>