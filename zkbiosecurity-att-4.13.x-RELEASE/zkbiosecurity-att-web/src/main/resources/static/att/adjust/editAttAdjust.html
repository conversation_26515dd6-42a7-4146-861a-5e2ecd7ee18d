<#include '/public/template/editTemplate.html'>
<#macro editContent>

	<div class="attSelectPersonDiv">
		<#include '/att/person/selectPersonContent.html'>
	</div>

	<form action='attAdjust.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
		<input type='hidden' name='id' value='${(item.id)!}'/>
		<!-- 人员id -->
		<input id="personIds" name="personIds" type="hidden" />
		<input id="personPins" name="personPin" type="hidden" />
		<table class='tableStyle' style="margin-top: 10px">
			<tr>
				<th><label><@i18n 'att_adjust_adjustDate'/></label><span class='required'>*</span></th>
				<td>
					<@ZKUI.Input id="attAdjustDateId" type="date" value="${(item.adjustDate)!}" name="adjustDate" readonly="true" today="true" hideLabel="true" title="att_adjust_adjustDate"/>
				</td>
				<th style="display: none"><label><@i18n 'att_adjust_type'/></label><span class='required'>*</span></th>
				<td style="display: none">
					<@ZKUI.Combo id="adjustType${uuid}" empty="false" hideLabel="true" name="adjustType" value="${(item.adjustType)!}" width="148" onChange="adjustTypeChange">
						<option value="0"><@i18n 'att_rule_off'/></option>
						<option value="1"><@i18n 'att_rule_class'/></option>
					</@ZKUI.Combo>
				</td>
				<th rowspan="2" style="text-align: right; padding-right: 30px"><label><@i18n 'common_remark'/></label></th>
				<td rowspan="2"><textarea name='remark' type='text' maxlength="100" value='${(item.remark)!}' rows="2" style="width: 200px;height: 50px"/></td>
			</tr>
			<tr id="bcmc">
				<!-- 班次 -->
				<th><label><@i18n 'att_adjust_shiftName'/></label><span class="required">*</span></th>
				<td>
					<@ZKUI.Combo id="attAdjustSelectShift${uuid}" width="148" hideLabel="true" empty="false" name="shiftId" value="${attShiftId}" readonly="true" path="attShift.do?getShiftList">
					</@ZKUI.Combo>
				</td>
				<td colspan="2">
					<span class="warningImage"></span><span class="warningColor"><@i18n 'att_adjust_shiftPeriodStartMode'/></span>
				</td>
			</tr>
		</table>
	</form>

	<script type='text/javascript'>
		$().ready(function() {
			$('#${formId}').validate( {
				debug : true,
				rules :
				{
					'adjustDate' : {
						required : true
					},
					'adjustType' :{
						required : true
					}
				},
				submitHandler : function()
				{

				    var personIds = ZKUI.PersSelect.get("selectPerson${uuid!}").getRightGridSelectIds();
                    if(personIds == "") {
                        messageBox({messageType : "alert", text : "<@i18n 'att_common_neesSelectPerson'/>"});
                        return false;
                    }

                    var adjustType = ZKUI.Combo.get("adjustType${uuid}").combo.getSelected();

					if(adjustType=="1"){
                        var attAdjustSelectShift = ZKUI.Combo.get("attAdjustSelectShift${uuid}").combo.getSelected();
                        if (!attAdjustSelectShift) {
                            messageBox({messageType: "alert", text: "<@i18n 'att_adjust_selectClass'/>"});
                            return false;
                        }
					}

                    if(adjustType=="0"){
                        ZKUI.Combo.get('attAdjustSelectShift${uuid}').combo.setComboValue("")
                    }

					//判断相同时间内是否存在其他申请
					var adjustDate = $("#${formId} input[name='adjustDate']").val();
					$.ajax({
						type: "post",
						url: "attLeave.do?existAdjustOrClassApply",
						data:{
							adjustPersonId: personIds,
							adjustDate: adjustDate
						},
						dataType: "json",
						async: false,
						success: function (result) {
							if(result.ret == "ok")
							{
							    var selectConten = ZKUI.PersSelect.get("selectPerson${uuid!}").getSelectRows();
								var personPins = "";
								for(var i=0; i<selectConten.length; i++)
								{
									personPins += selectConten[i].personPin + ",";
								}
								$("#personPins").val(personPins.substring(0,personPins.length-1));
								$("#personIds").val(personIds);

								<@submitHandler/>
							}
							else
							{
								var ptimeout = sysCfg.ptimeout;
								openMessage(msgType.warning, result[sysCfg.msg], ptimeout);
							}
						}
					});
				}
			});
		});
		
		$("#bcmc").hide();
		function adjustTypeChange(value, text) {
			if (value == 0) {
				$("#bcmc").hide();
			} else if (value == 1){
				$("#bcmc").show();
			}
		}
	</script>
</#macro>