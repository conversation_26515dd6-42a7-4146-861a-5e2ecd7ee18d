<#include '/public/template/editTemplate.html'>
<#macro editContent>
<#assign editPage = true>
<form action='attPerson.do?saveVerifyMode' method='post' id='${formId}' enctype='multipart/form-data'>
	<input type="hidden" name="attPersonIds" value="${attPersonIds!}" />
	<input type="hidden" name="pins" value="${pins!}" />
	<table class='tableStyle'>
		<tr>
			<th><@i18n 'common_verifyMode_entiy'/></th>
			<td>
				<@ZKUI.Combo empty="false" name="verifyMode"  hideLabel="true" value="${(item.verifyMode)!'0'}" key="AttVerifyMode" />
			</td>
		</tr>
	</table>
</form>
<script type='text/javascript'>
	$('#${formId}').validate({
		debug : true,
		rules : {},
		submitHandler : function() {
			<@submitHandler />
		}
	});
</script>
</#macro>