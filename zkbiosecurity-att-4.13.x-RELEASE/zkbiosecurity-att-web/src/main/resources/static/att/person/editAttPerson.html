<#assign leftRTL = "left">
<#assign rightRTL = "right">
<#if enableRTL?? && enableRTL=="true">
    <!-- 定义全局对齐方式变量(针对阿拉伯页面) -->
    <#assign leftRTL = "right">
    <#assign rightRTL = "left">
</#if>
<div id="idAttPersonDiv">
	<table>
		<tr>
			<!-- 人事模块 ==》 人员 ==》 新增页面的考勤设置 -->
			<td valign="top" style="padding-top:3px;">
				<fieldset class="zk-border-color-gray" style="width: 360px; height: 250px; border: 1px solid #DDDDDD; margin: 0px; padding: 0px;">
					<legend style="border: 0px;"><@i18n 'pers_person_attArea'/></legend>
                    <input type="hidden" name="att.personAreas" id="attPersonAreas" value="${attPersonAreas}">
					<div id="treeboxbox_tree" style="float:${leftRTL!'left'}; width:350px; height:225px; overflow:hidden;">
                        <@ZKUI.Tree class="dhx_tree_box_white" id="tree${uuid!}" type="checkbox" value="${attPersonAreas}" url="authArea.do?tree"></@ZKUI.Tree>
                    </div>
				</fieldset>
			</td>
			<td valign="top" style="padding-${leftRTL!'left'}: 100px;">
                <table class="tableStyle">
                    <tr>
                        <th style="width: 55% !important;"><@i18n 'att_person_attendanceMode'/></th><!--  考勤模式  -->
                        <td>
                            <@ZKUI.Combo empty="false" id="comboAttendance${uuid}" name="att.isAttendance" width="148" hideLabel="true" readonly="readonly" value="${(item.isAttendance?string('true','false'))!'true'}">
                                <option value="true"><@i18n 'att_person_normalAttendance'/></option>
                                <option value="false"><@i18n 'att_person_noPunchCard'/></option>
                            </@ZKUI.Combo>
                        </td>
                    </tr>
                    <tr>
                        <th><@i18n 'pers_person_devOpAuth'/></th><!-- 设备操作权限 -->
                        <td>
                            <@ZKUI.Combo empty="false" id="comboPerDevAuth${uuid}" name="att.perDevAuth" width="148" hideLabel="true" readonly="readonly" value="${(item.perDevAuth)!0}">
                                <option value="0"><@i18n 'att_common_staff'/></option>
                                <option value="2"><@i18n 'common_level_enroller'/></option>
                                <option value="6"><@i18n 'common_level_administrator'/></option>
                                <option value="14"><@i18n 'att_common_superadmin'/></option>
                            </@ZKUI.Combo>
                        </td>
                    </tr>
                    <tr>
                        <th><@i18n 'common_verifyMode_entiy'/></th>
                        <td>
                            <@ZKUI.Combo empty="true" id="comboVerifyMode{uuid}" width="148" name="att.verifyMode"  hideLabel="true" value="${(item.verifyMode)!'0'}" key="AttVerifyMode" />
                        </td>
                    </tr>
                </table>
		    </td>
		</tr>
	</table>
</div>
<script type="text/javascript">
$(function(){
    var tree = ZKUI.Tree.get("tree${uuid!}").tree;
    tree.attachEvent("onCheck", function(id, state){

        // 没有权限不显示且已经选择的区域
        var noAuthAndSelectedIds = "";

        // 选择的所有区域
        var allSelectedAreaIds = $("#attPersonAreas").val();
        var allSelectedAreaIdsArray = [];
        if (allSelectedAreaIds) {
            allSelectedAreaIdsArray = allSelectedAreaIds.split(",");
        }

        // 权限过滤后的所有区域
        var checkedAreasIds = tree.getAllChecked();
        var unCheckedAreasIds = tree.getAllUnchecked();
        var allIds = checkedAreasIds + "," + unCheckedAreasIds;
        var allIdsArray = allIds.split(",");

        // 选择的区域不在权限过滤后的所有区域，说明是被权限过滤掉的，需要提交到后台，否则区域会丢失
        for (var i in allSelectedAreaIdsArray) {
            var selectId = allSelectedAreaIdsArray[i];
            var noAuth = true;
            for (var i in allIdsArray) {
                var id = allIdsArray[i];
                if (id && id != 0) {
                    if (selectId == id) {
                        noAuth = false;
                        break;
                    }
                }
            }
            if (noAuth) {
                if (noAuthAndSelectedIds) {
                    noAuthAndSelectedIds = noAuthAndSelectedIds + "," + selectId;
                } else {
                    noAuthAndSelectedIds = selectId;
                }
            }
        }

        var ids = "";
        if (checkedAreasIds) {
            ids = checkedAreasIds + "," + noAuthAndSelectedIds;
        } else {
            ids = noAuthAndSelectedIds;
        }

        $("#attPersonAreas").val(ids);

    });
});
</script>
