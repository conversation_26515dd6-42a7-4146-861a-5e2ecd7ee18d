<#assign gridName="attPersonVerifyModeGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
				<td valign="middle">
					<@ZKUI.Input name="personPin" maxlength="30" title="pers_person_pin" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="likeName" maxlength="30" title="pers_person_wholeName" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="deptName" maxlength="30" title="pers_dept_deptName" type="text"/>
				</td>
 			</tr>
 		</@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar gridName="${gridName}">
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:personVerifyMode:refresh"></@ZKUI.ToolItem>
    	<@ZKUI.ToolItem id="attPerson.do?editVerifyMode&pins=(personPin)" text="att_personVerifyMode_setting" width="420" height="250" img="comm_add.png" action="commonOpenOperate" permission="att:personVerifyMode:setting"></@ZKUI.ToolItem>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid gridName="${gridName}" vo="com.zkteco.zkbiosecurity.att.vo.AttPersonVerifyModeItem" query="attPerson.do?verifyModeList"/>
</@ZKUI.GridBox>