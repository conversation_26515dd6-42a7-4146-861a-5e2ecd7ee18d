<style type="text/css">
.imageBox {
	 height: 99%; text-align: center; width: 99%; overflow: hidden; position: relative;
}
.imageBox span {
	color: #bdc0ba; font-size: 25px; height: 280px; line-height: 280px; display: inline-block;
}
.attLeaveImage {
	position: absolute;
	top: 50%;
	left: 50%;
	-webkit-transform: translate(-50%,-50%);
	transform: translate(-50%,-50%);
	display: block;
	max-width: 100%;
	max-height: 100%;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	outline: none;
}
</style>

<!-- 请假单图片 -->
<div id="imageBox" class="imageBox">
	<!-- 隐藏域 -->
	<input id="idLeaveImage" name="leaveImage" type="hidden" value="${(leaveImage)!}" />
</div>

<script type="text/javascript">
//显示图片
var htmlStr;
var imgStr = $("#idLeaveImage").val();
if (imgStr == "") {
	htmlStr = "<span style=\"\"><@i18n 'att_leave_imageShow'/></span>"
} else {
	htmlStr	= "<img src='" + imgStr + "' class='attLeaveImage'/>";
}
$("#imageBox").append(htmlStr);
</script>
