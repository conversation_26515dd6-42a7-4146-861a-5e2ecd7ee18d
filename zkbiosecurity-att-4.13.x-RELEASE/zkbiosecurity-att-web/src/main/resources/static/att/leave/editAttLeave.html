<#include '/public/template/editTemplate.html'>
<#macro editContent>
<style type="text/css">
	.attSpanImage {
		color: #bdc0ba;
		font-size: 17px;
		display: inline-block;
		margin-top: 56px;
		width: 100%;
		text-align: center;
	}

	.attLeaveImageTd {
		border: 1px dashed #aeb4b8 !important;
		padding: 5px 0px !important;
		height: 0px;
		width: 380px !important;
		overflow-y: auto;
	}

	.attLeaveImagePreview {
        width: 100%;
        height: 127px;
		overflow-y: auto;
    }

	.attImageDiv {
		display: inline-block;
		width: 80px;
		height: 100px;
		-webkit-box-sizing: border-box;
		-moz-box-sizing: border-box;
		box-sizing: border-box;
		background: #f8f8f8;
		position: relative;
		overflow: hidden;
		margin: 5px;
		cursor: pointer;
		vertical-align: top;
	}

	.attDivCancel {
		position: absolute;
		background: url('images/attDivCancel.png') no-repeat;
		width: 20px;
		height: 20px;
		top: 4px;
		right: 0;
		z-index: 1;
		cursor: pointer;
	}
</style>
	<div class="attSelectPersonDiv">
		<#include '/att/person/selectPersonContent.html'>
	</div>
	<form action='attLeave.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
		<!-- 请假id -->
		<input type='hidden' name='id' value='${(item.id)!}'/>
		<!-- 出差外出typeId -->
		<input id="tripAndOutId" type='hidden' value='${(tripAndOutId)!}'/>
		<!-- 人员id -->
		<input id="personIds" name="personIds" type="hidden" />
		<input id="personPins" name="personPin" type="hidden" />
		<table class='tableStyle' style="margin-top: 10px; table-layout: fixed; padding-right: 10px">
			<tr>
				<!-- 假种 -->
				<th><label><@i18n 'att_leftMenu_leaveType'/></label><span class="required">*</span></th>
				<td>
					<@ZKUI.Combo id="id_leaveType" width="148" hideLabel="true" empty="true" name="leaveTypeId" path="attLeaveType.do?listJsonLeaveType"></@ZKUI.Combo>
				</td>
				<th width="30%" id="admin_leave_image" style="text-align: right; padding-right: 20px">
					<@i18n 'att_leave_image'/>
				</th>
				<td rowspan="5" class="attLeaveImageTd" id="admin_imageTd">
					<!-- 请假单图片 -->
					<div id="preview" class="attLeaveImagePreview">
						<#if !((item.id)?exists)>
							<span id="idSpanImage" class="attSpanImage"><@i18n 'att_leave_imageShow'/></span>
						</#if>
					</div>
					<div style="display: none;">
						<!-- accept:规定能够通过文件上传进行提交的文件类型，最后的结尾需要","结束，否则表单不能提交 -->
						<input id="leaveImage" multiple="multiple" onchange="attLeaveChangePicture(this);" type="file" accept="image/gif,image/jpeg,image/jpg,image/png," />
					</div>
				</td>
			</tr>
			<tr>
				<!-- 请假开始时间 -->
				<th><label><@i18n 'common_startTime'/></label><span class='required'>*</span></th>
				<td><@ZKUI.Input id="attLeaveStartTimeId" type="datetime" value="${(item.startDatetime)!}" readonly="true" today="true" offsetField="D" today="-1" name="startDatetime" hideLabel="true" title="common_startTime" /></td>
				<th id="admin_brow" style="text-align: right; padding-right: 20px">
					<input class="button-form" id="idBrowImage" value="<@i18n 'common_op_browse'/>" name="browImage" onclick="leaveImage.click();" type="button" />
				</th>
			</tr>
			<tr>
				<!-- 请假结束时间 -->
				<th><label><@i18n 'common_endTime'/></label><span class='required'>*</span></th>
				<td><@ZKUI.Input id="attLeaveEndTimeId" type="datetime" value="${(item.endDatetime)!}" readonly="true" today="true" name="endDatetime" hideLabel="true" title="common_endTime"/></td>
			</tr>
			<tr>
				<!-- 请假理由 -->
				<th rowspan="2"><label><@i18n 'common_remark'/></label></th>
				<td rowspan="2"><textarea rows="4" name='remark' maxlength="100" type='text' value='${(item.remark)!}' style="width: 148px; height: 50px"/></td>
			</tr>
		</table>
	</form>

	<script type='text/javascript'>


		var userAgent = navigator.userAgent; //用于判断浏览器类型
		var fileCount = 0;
		var fileArray = new Array();
		var pathArray = new Array();

		function attLeaveChangePicture(file) {

			//获取选择图片的对象
			var docObj = $(file)[0];

			//得到所有的图片文件
			var fileList = docObj.files;
			if (fileList == null) {
				return false;
			}

			//清除错误信息
			$("#picTip").html("");
			if (fileCount > 4 || (fileCount + fileList.length) > 4) {
				messageBox({
					messageType: "alert",
					title: "<@i18n 'common_prompt_title'/>",
					text: "<@i18n 'att_leave_maxFileCount'/>"
				});
				return false;
			}

			// 校验图片格式
			for (var i = 0; i < fileList.length; i++){
				var extname = fileList[i].name.substring(fileList[i].name.lastIndexOf(".") + 1, fileList[i].name.length);
				extname = extname.toLowerCase();//处理了大小写
				if (extname != "jpeg" && extname != "jpg" && extname != "gif" && extname != "png") {
					openMessage (msgType.warning, "<@i18n 'att_leave_imageType'/>");
					return false;
				}
				//过滤图片大小
				var size = fileList[i].size;
				if (size > 4194304) {
					openMessage(msgType.warning, "<@i18n 'att_leave_imageSize'/>");
					return false;
				}
			}


			if (fileList.length > 0) {
				$("#idSpanImage").hide();
			}
			if (fileList.length <= 0) {
				$("#preview").html("<span id=\"idSpanImage\" class=\"attSpanImage\"><@i18n 'att_leave_imageShow'/></span>");
			}
			fileCount = fileCount + fileList.length;
			for (var i = 0; i < fileList.length; i++) {
				fileArray.push(fileList[i]);
			}

			eachFileArray(fileArray, docObj);
			$("#leaveImage")[0].value = null;
		}


		/*删除功能*/
		function delImage(obj, index) {
			var _this = $(obj);
			_this.parents(".attImageDiv").remove();
			if (fileCount > 0) {
				fileCount = fileCount -1;
				fileArray.splice(index, 1);
				pathArray.splice(index, 1);
			}
			if (fileCount <= 0) {
				$("#preview").html("<span id=\"idSpanImage\" class=\"attSpanImage\"><@i18n 'att_leave_imageShow'/></span>");
			}
		}


		function eachFileArray(fileArray, docObj) {
			var picDiv = $("#preview");
			document.getElementById('preview').innerHTML = "";
			// picDiv.innerHTML = "";
			//循环遍历
			for (var i = 0; i < fileArray.length; i++) {
				//动态添加html元素
				var picHtml = "<div class='attImageDiv' ><div class='divImg'> <img id='img" + fileArray[i].name + "' /></div><div class='attDivCancel' onclick='delImage(this,"+i+")'></div> </div>";

				picDiv.prepend(picHtml);
				//获取图片imgi的对象
				var imgObjPreview = document.getElementById("img" + fileArray[i].name);
				if (fileArray[i]) {
					//图片属性
					imgObjPreview.style.display = 'block';
					imgObjPreview.style.width = '80px';
					imgObjPreview.style.height = '100px';
					//imgObjPreview.src = docObj.files[0].getAsDataURL();
					//火狐7以上版本不能用上面的getAsDataURL()方式获取，需要以下方式
					if (userAgent.indexOf('MSIE') == -1) {
						//IE以外浏览器
						imgObjPreview.src = window.URL.createObjectURL(fileArray[i]); //获取上传图片文件的物理路径;
						pathArray.push(imgObjPreview.src);
						// var msgHtml = '<input type="file" id="fileInput" multiple/>';
					} else {
						//IE浏览器
						if (docObj.value.indexOf(",") != -1) {
							var srcArr = docObj.value.split(",");
							imgObjPreview.src = srcArr[i];
						} else {
							imgObjPreview.src = docObj.value;
						}
					}
				}
			}
		}

		$().ready(function() {

			// 请假时间设置
			var minDate = new Date();
			minDate.setMonth(minDate.getMonth()-2);
			minDate.setDate(1);
			ZKUI.Input.PULL.attLeaveStartTimeId.setMinDate(minDate);
			ZKUI.Input.PULL.attLeaveEndTimeId.setMinDate(minDate);


			$('#${formId}').validate( {
				debug : true,
				rules :
				{
					'startDatetime' :
					{
						required : true
					},
					'endDatetime' :
					{
						required : true
					},
					'leaveTypeId' :
					{
						required : true
					}
				},
				submitHandler : function()
				{
                    var startTime = $("#${formId} input[name='startDatetime']").val();
                    var endTime = $("#${formId} input[name='endDatetime']").val();
					var personIds = ZKUI.PersSelect.get("selectPerson${uuid!}").getRightGridSelectIds();// 选人控件取值
					var selectConten = ZKUI.PersSelect.get("selectPerson${uuid!}").getSelectRows();

                    //开始时间与结束时间对比
                    if (startTime > endTime) {
                        //开始时间不能大于结束时间
                        messageBox({
                            messageType: "alert",
                            title: "<@i18n 'common_prompt_title'/>",
                            text: "<@i18n 'common_dsTime_timeValid4'/>"
                        });
                        return false;
                    }
                    else if(startTime == endTime){
                    	//结束时间不能等于开始时间
                    	messageBox({
                            messageType : "alert",
                            title : "<@i18n 'common_prompt_title'/>",
                            text : "<@i18n 'common_dsTime_timeValid1'/>"
                        });
                        return false;
                    }
					else if(personIds == "")
					{
                        messageBox({
                            messageType : "alert",
                            text : "<@i18n 'att_common_neesSelectPerson'/>"
                        });
					}
					else
					{
                        $("#personIds").val(personIds);

                        var personPins = "";
                        for(var i=0; i<selectConten.length; i++)
                        {
                            personPins += selectConten[i].personPin + ",";
                        }
                        $("#personPins").val(personPins.substring(0,personPins.length-1));

						//对上传的请假单图片进行过滤
						if (fileArray.length > 0) {
							if (fileArray.length > 4) {
								messageBox({
									messageType: "alert",
									title: "<@i18n 'common_prompt_title'/>",
									text: "<@i18n 'att_leave_maxFileCount'/>"
								});
								return false;
							}

							for (var i = 0; i < fileArray.length; i++) {
								//过滤图片格式
								var extname = fileArray[i].name.substring(fileArray[i].name.lastIndexOf(".") + 1, fileArray[i].name.length);
								extname = extname.toLowerCase();//处理了大小写
								if (extname != "jpeg" && extname != "jpg" && extname != "gif" && extname != "png") {
									$("#newImage").css("height", "90px");
									$("#picTip").css("display", "inline-block");
									$("#picTip").html("<span style=\"color:Red;\"><@i18n 'att_leave_imageType'/></span>");
									return false;
								}

								//过滤图片大小
								var size = fileArray[i].size;
								if (size > 4194304) {
									$("#newImage").css("height", "90px");
									$("#picTip").css("display", "inline-block");
									$("#picTip").html("<span style=\"color:Red\"><@i18n 'att_leave_imageSize'/></span>");
									return false;
								}
							}
						}

                        //判断相同时间内是否存在其他申请
                        $.ajax({
							type: "post",
							url: "attLeave.do?existApply",
							data:{
								personIds:personIds,
								startTime:startTime,
								endTime:endTime,
								exceptionType:"leave",
								/*leaveTypeId: ZKUI.Combo.get("id_leaveType").getValue()*/
							},
							dataType: "json",
							async: false,
							success: function (result) {
								if(result.ret == "ok"){

									var url = $("#${formId}").attr("action");
									var fd = new FormData(document.getElementById("${formId}"));
									for (var i=0; i<fileArray.length; i++) {
										fd.append("leaveImage", fileArray[i]);
									}

									onLoading(function () {
										$.ajax({
											url: url,
											type: 'post',
											processData: false,
											contentType: false,
											data: fd,
											success: function (result) {
												if (result.ret == "ok") {
													dealRetResult(eval(result), attGlobalReloadGrid);
													if(isContinueAdd) {
														DhxCommon.refreshCurrentWindow();
													} else {
														DhxCommon.closeWindow();
													}
												} else {
													openMessage(msgType.warning, result.msg);
												}
											}
										});
									});

								}
								else {
									openMessage(msgType.warning, result.msg);
								}
							}
                         });

					}

				}
			});
		});

	</script>
</#macro>