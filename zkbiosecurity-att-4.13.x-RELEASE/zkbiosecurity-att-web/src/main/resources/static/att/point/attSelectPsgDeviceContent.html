<@ZKUI.SelectContent gridName="attSelectPsgGrid" type="radio" onSure="attPointAfterSelectPsg"
showColumns="name,deviceAlias,deviceSn" textField="deviceName" copy="true" authAreaId="${areaId!}"
vo="com.zkteco.zkbiosecurity.att.vo.AttPsgDeviceSelectItem"
query="attDoorOrPark.do?selectPsgDeviceList&&authAreaId=${areaId!}">
<@ZKUI.Searchbar autoReload="false">
<@ZKUI.SearchTop>
<tr>
    <td valign="middle">
        <@ZKUI.Input name="deviceName"  maxlength="30" title="common_dev_name" type="text"/>
    </td>
    <td valign="middle">
        <@ZKUI.Input name="deviceSn"  maxlength="15" title="common_dev_sn" type="text"/>
    </td>
</tr>
</@ZKUI.SearchTop>
</@ZKUI.Searchbar>
</@ZKUI.SelectContent>
