<@ZKUI.SelectContent gridName="attSelectDoorGrid" type="radio" onSure="attPointAfterSelectDoor" showColumns="doorName,doorNo,deviceAlias,deviceSn" textField="doorName" copy="true" areaId="${areaId!}" vo="com.zkteco.zkbiosecurity.att.vo.AttSelectDoorItem" query="attDoorOrPark.do?selectDoorList&areaId=${areaId!}">
    <@ZKUI.Searchbar autoReload="false">
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="doorName"  maxlength="30" title="acc_door_name" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="deviceSn"  maxlength="15" title="common_dev_sn" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
</@ZKUI.SelectContent>
