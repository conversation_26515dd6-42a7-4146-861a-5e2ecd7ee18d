<#assign gridName="attPointGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">
	<@ZKUI.Searchbar>
		<@ZKUI.SearchTop>
			<tr>
				<td valign="middle">
					<@ZKUI.Input name="pointName" maxlength="30" title="att_attPoint_name" type="text" />
				</td>
			</tr>
		</@ZKUI.SearchTop>
		<@ZKUI.SearchBelow>
		</@ZKUI.SearchBelow>
	</@ZKUI.Searchbar>
	<@ZKUI.Toolbar>
		<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:point:refresh"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="attPoint.do?edit" text="common_op_new" width="480" height="245" img="comm_add.png" action="commonAdd" permission="att:point:edit"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="attPoint.do?export" type="export" permission="att:point:export"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="attPoint.do?del" text="common_op_del" img="comm_del.png" action="commonDel" permission="att:point:del"></@ZKUI.ToolItem>
	</@ZKUI.Toolbar>
	<@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttPointItem" query="attPoint.do?list"/>
</@ZKUI.GridBox>