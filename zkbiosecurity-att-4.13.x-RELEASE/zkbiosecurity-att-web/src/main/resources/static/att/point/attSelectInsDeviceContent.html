<@ZKUI.SelectContent gridName="attSelectInsGrid" onSure="attPointAfterSelectIns" type="radio" showColumns="devName,ipAddress,sn" textField="devName" copy="true" authAreaId="${areaId!}" vo="com.zkteco.zkbiosecurity.att.vo.AttInsDeviceSelectItem" query="attDoorOrPark.do?selectInsDeviceList&&authAreaId=${areaId!}">
    <@ZKUI.Searchbar autoReload="false">
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="devName"  maxlength="30" title="common_dev_name" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
</@ZKUI.SelectContent>
