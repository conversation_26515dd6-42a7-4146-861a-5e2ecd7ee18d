<@ZKUI.SelectContent gridName="attSelectIvsGrid" type="radio" onSure="attPointAfterSelectIvs"
    showColumns="id,name,alias"  textField="id" copy="true" authAreaId="${areaId!}"
    vo="com.zkteco.zkbiosecurity.att.vo.AttIvsDeviceSelectItem"
    query="attDoorOrPark.do?selectIvsDeviceList&authAreaId=${areaId!}">
    <@ZKUI.Searchbar autoReload="false">
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.ComboTree type="radio" title="att_attPoint_masterDevice" empty="false" name="parentDeviceId"
                    url="attDoorOrPark.do?ivsParentDeviceTree" id="parentDeviceId${uuid}" width="148" />
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="name" maxlength="30" title="att_attPoint_channelName" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
</@ZKUI.SelectContent>
