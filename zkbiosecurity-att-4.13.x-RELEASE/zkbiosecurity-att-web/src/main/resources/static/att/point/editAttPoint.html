<#include '/public/template/editTemplate.html'>
<#macro editContent>
<script type='text/javascript'>
    var flag;
	function changeDeviceModule(value) {
        $("#deviceId").val("");
        $("#deviceSn").val("");
        $("#ipAddress").val("");
        $("#doorNo").val("");

        if(value == "acc"){
            flag = "door";
            $("#idEsdcDeviceTr").hide();
            $("#idIvsDeviceTr").hide();
            $("#idAccDeviceTr").show();
            $("#idParkDeviceTr").hide();
            $("#idInsDeviceTr").hide();
            $("#idPidDeviceTr").hide();
            $("#idVmsDeviceTr").hide();
            $("#idPsgDeviceTr").hide();
            $("#idPsgRecordTypeTr").hide();
            var attPointSelectAreaTree = ZKUI.ComboTree.get("attPointAreaSelectId");
            attPointSelectAreaTree.clear();
            attPointSelectAreaTree.options.url = "authArea.do?tree";
            attPointSelectAreaTree.reloadTree();
        }
        else if(value == "park"){
            flag = "park";
            $("#idEsdcDeviceTr").hide();
            $("#idIvsDeviceTr").hide();
            $("#idAccDeviceTr").hide();
            $("#idInsDeviceTr").hide();
            $("#idPidDeviceTr").hide();
            $("#idParkDeviceTr").show();
            $("#idVmsDeviceTr").hide();
            $("#idPsgDeviceTr").hide();
            $("#idPsgRecordTypeTr").hide();
            var attPointSelectAreaTree = ZKUI.ComboTree.get("attPointAreaSelectId");
            attPointSelectAreaTree.clear();
            attPointSelectAreaTree.options.url = "attPoint.do?getAllParkEntranceArea";
            attPointSelectAreaTree.reloadTree();
        }else if(value == "ins"){
            flag = "ins";
            $("#idEsdcDeviceTr").hide();
            $("#idIvsDeviceTr").hide();
            $("#idAccDeviceTr").hide();
            $("#idParkDeviceTr").hide();
            $("#idPidDeviceTr").hide();
            $("#idInsDeviceTr").show();
            $("#idVmsDeviceTr").hide();
            $("#idPsgDeviceTr").hide();
            $("#idPsgRecordTypeTr").hide();
            var attPointSelectAreaTree = ZKUI.ComboTree.get("attPointAreaSelectId");
            attPointSelectAreaTree.clear();
            attPointSelectAreaTree.options.url = "authArea.do?tree";
            attPointSelectAreaTree.reloadTree();
        }else if(value == "pid"){
            flag = "pid";
            $("#idEsdcDeviceTr").hide();
            $("#idIvsDeviceTr").hide();
            $("#idAccDeviceTr").hide();
            $("#idParkDeviceTr").hide();
            $("#idInsDeviceTr").hide();
            $("#idPidDeviceTr").show();
            $("#idVmsDeviceTr").hide();
            $("#idPsgDeviceTr").hide();
            $("#idPsgRecordTypeTr").hide();
            var attPointSelectAreaTree = ZKUI.ComboTree.get("attPointAreaSelectId");
            attPointSelectAreaTree.clear();
            attPointSelectAreaTree.options.url = "authArea.do?tree";
            attPointSelectAreaTree.reloadTree();
        } else if(value == "vms"){
            flag = "vms";
            $("#idEsdcDeviceTr").hide();
            $("#idIvsDeviceTr").hide();
            $("#idAccDeviceTr").hide();
            $("#idInsDeviceTr").hide();
            $("#idPidDeviceTr").hide();
            $("#idParkDeviceTr").hide();
            $("#idVmsDeviceTr").show();
            $("#idPsgDeviceTr").hide();
            $("#idPsgRecordTypeTr").hide();
            var attPointSelectAreaTree = ZKUI.ComboTree.get("attPointAreaSelectId");
            attPointSelectAreaTree.clear();
            attPointSelectAreaTree.options.url = "authArea.do?tree";
            attPointSelectAreaTree.reloadTree();
        } else if(value == "psg"){
            flag = "psg";
            $("#idEsdcDeviceTr").hide();
            $("#idIvsDeviceTr").hide();
            $("#idAccDeviceTr").hide();
            $("#idInsDeviceTr").hide();
            $("#idPidDeviceTr").hide();
            $("#idParkDeviceTr").hide();
            $("#idVmsDeviceTr").hide();
            $("#idPsgDeviceTr").show();
            $("#idPsgRecordTypeTr").show();
            var attPointSelectAreaTree = ZKUI.ComboTree.get("attPointAreaSelectId");
            attPointSelectAreaTree.clear();
            attPointSelectAreaTree.options.url = "authArea.do?tree";
            attPointSelectAreaTree.reloadTree();
        } else if(value == "ivs"){
            flag = "ivs";
            $("#idEsdcDeviceTr").hide();
            $("#idIvsDeviceTr").show();
            $("#idAccDeviceTr").hide();
            $("#idInsDeviceTr").hide();
            $("#idPidDeviceTr").hide();
            $("#idParkDeviceTr").hide();
            $("#idVmsDeviceTr").hide();
            $("#idPsgDeviceTr").hide();
            $("#idPsgRecordTypeTr").hide();
            var attPointSelectAreaTree = ZKUI.ComboTree.get("attPointAreaSelectId");
            attPointSelectAreaTree.clear();
            attPointSelectAreaTree.options.url = "authArea.do?tree";
            attPointSelectAreaTree.reloadTree();
        } else if(value == "esdc"){
            flag = "esdc";
            $("#idEsdcDeviceTr").show();
            $("#idIvsDeviceTr").hide();
            $("#idAccDeviceTr").hide();
            $("#idInsDeviceTr").hide();
            $("#idPidDeviceTr").hide();
            $("#idParkDeviceTr").hide();
            $("#idVmsDeviceTr").hide();
            $("#idPsgDeviceTr").hide();
            $("#idPsgRecordTypeTr").hide();
            var attPointSelectAreaTree = ZKUI.ComboTree.get("attPointAreaSelectId");
            attPointSelectAreaTree.clear();
            attPointSelectAreaTree.options.url = "authArea.do?tree";
            attPointSelectAreaTree.reloadTree();
        } else {
            $("#idAccDeviceTr").hide();
            $("#idParkDeviceTr").hide();
            $("#idInsDeviceTr").hide();
            $("#idPidDeviceTr").hide();
            $("#idVmsDeviceTr").hide();
            $("#idIvsDeviceTr").hide();
            $("#idPsgDeviceTr").hide();
            $("#idPsgRecordTypeTr").hide();
            $("#idEsdcDeviceTr").hide();
        }

	}

    /**
     *区域下拉树的树对象选中事件
     */
    var attPointAreaId = ""
    function attPointOnTreeChecked(id, state) {
        attPointAreaId = id;
        //区域变动之后清空已选的门或设备
        $("#deviceId").val("");
        $("#widgetObjectName").val("");
        $("#idParkDeviceSelect").val("");

    }


    function attPointSelectDoor(){
        var opts = {
            path:"skip.do?page=att_point_attSelectDoorContent&areaId="+attPointAreaId,
            width: 880,
            height: 510,
            title:"<@i18n 'att_attPoint_door'/>",
            gridName:"gridBox"
        };
        openSelectContent(opts);
    }
    function attPointAfterSelectDoor(id,text,event) {
        var selectConten = ZKUI.Select.get("attSelectDoorGrid").getSelectRows()[0];
        $("#deviceId").val(selectConten.id);
        $("#widgetObjectName").val(selectConten.doorName);
        $("#doorNo").val(selectConten.doorNo);
        $("#deviceSn").val(selectConten.deviceSn);

    }

    function attPointSelectPark(){

        var parkopts = {
            path:"skip.do?page=att_point_attSelectParkDeviceContent&areaId="+attPointAreaId,
            width: 880,
            height: 510,
            title:"<@i18n 'att_attPoint_device'/>",
            gridName:"gridBox",
        };
        openSelectContent(parkopts);
    }

    function attPointAfterSelectPark(id,text,event) {
        var selectConten = ZKUI.Select.get("attSelectParkGrid").getSelectRows()[0];
        $("#deviceId").val(selectConten.channelId);
        $("#idParkDeviceSelect").val(selectConten.channelName);
        $("#ipAddress").val(selectConten.ipAdress);
        $("#idStatus").val(selectConten.status);

    }

    function attPointSelectIns(){
        var opts = {
            path:"skip.do?page=att_point_attSelectInsDeviceContent&areaId="+attPointAreaId,
            width: 880,
            height: 510,
            title:"<@i18n 'att_attPoint_device'/>",
            gridName:"gridBox"
        };
        openSelectContent(opts);
    }
    function attPointAfterSelectPid(id,text,event) {
        var selectConten = ZKUI.Select.get("attSelectPidGrid").getSelectRows()[0];
        $("#deviceId").val(selectConten.id);
        $("#deviceSn").val(selectConten.sn);
        $("#idPidDeviceSelect").val(selectConten.devName);
        $("#ipAddress").val(selectConten.ipAdress);
    }

    function attPointSelectPid(){
        var opts = {
            path:"skip.do?page=att_point_attSelectPidDeviceContent&areaId="+attPointAreaId,
            width: 880,
            height: 510,
            title:"<@i18n 'att_attPoint_device'/>",
            gridName:"gridBox"
        };
        openSelectContent(opts);
    }
    function attPointAfterSelectIns(id,text,event) {
        var selectConten = ZKUI.Select.get("attSelectInsGrid").getSelectRows()[0];
        $("#deviceId").val(selectConten.id);
        $("#deviceSn").val(selectConten.sn);
        $("#idInsDeviceSelect").val(selectConten.devName);
        $("#ipAddress").val(selectConten.ipAdress);
    }

    function attPointSelectVms(){
        var opts = {
            path:"skip.do?page=att_point_attSelectVmsDeviceContent&areaId="+attPointAreaId,
            width: 880,
            height: 510,
            title:"<@i18n 'att_attPoint_device'/>",
            gridName:"gridBox"
        };
        openSelectContent(opts);
    }

    function attPointAfterSelectVms(id,text,event) {
        var selectContent = ZKUI.Select.get("attSelectVmsGrid").getSelectRows()[0];
        $("#deviceId").val(selectContent.id);
        $("#deviceSn").val(selectContent.deviceSn);
        $("#doorNo").val(selectContent.channelNo);
        $("#idVmsDeviceSelect").val(selectContent.deviceName+"-"+selectContent.channelName);
    }
     function attPointSelectPsg(){
        var opts = {
            path:"skip.do?page=att_point_attSelectPsgDeviceContent&areaId="+attPointAreaId,
            width: 880,
            height: 510,
            title:"<@i18n 'att_attPoint_device'/>",
            gridName:"gridBox"
        };
        openSelectContent(opts);
    }

    function attPointAfterSelectPsg(id,text,event) {
        var selectContent = ZKUI.Select.get("attSelectPsgGrid").getSelectRows()[0];
        $("#deviceId").val(selectContent.id);
        $("#deviceSn").val(selectContent.deviceSn);
        $("#doorNo").val(selectContent.gateNo);
        $("#idPsgDeviceSelect").val(selectContent.name);
    }

    function attPointSelectIvs(){
        var opts = {
            path:"skip.do?page=att_point_attSelectIvsDeviceContent&areaId="+attPointAreaId,
            width: 880,
            height: 510,
            title:"<@i18n 'att_attPoint_device'/>",
            gridName:"gridBox"
        };
        openSelectContent(opts);
    }

    function attPointAfterSelectIvs(id,text,event) {
        var selectContent = ZKUI.Select.get("attSelectIvsGrid").getSelectRows()[0];
        //设备id为通道id
        $("#deviceId").val(selectContent.id);
        $("#deviceSn").val(selectContent.deviceSn);
        $("#doorNo").val(selectContent.channelNo);
        //通道的ip+name作为设备名称
        $("#idIvsDeviceSelect").val(selectContent.name);
    }

    function attPointSelectEsdc(){
        var opts = {
            path:"skip.do?page=att_point_attSelectEsdcDeviceContent&areaId="+attPointAreaId,
            width: 880,
            height: 510,
            title:"<@i18n 'att_attPoint_device'/>",
            gridName:"gridBox"
        };
        openSelectContent(opts);
    }

    function attPointAfterSelectEsdc(id,text,event) {
        var selectContent = ZKUI.Select.get("attSelectEsdcGrid").getSelectRows()[0];
        $("#deviceId").val(selectContent.id);
        $("#deviceSn").val(selectContent.deviceSn);
        $("#doorNo").val(selectContent.channelNo);
        $("#idEsdcDeviceSelect").val(selectContent.name);
    }

    $().ready(function() {

        $('#${formId}').validate( {
            debug : true,
            rules :
            {
                'pointName' :
                {
                    required: true,
                    unInputChar:true,
                    overRemote: ["attPoint.do?isExist", "${(item.pointName)!}"]
                },
                'areaId' :
                {
                    required: true
                }
            },
            submitHandler : function()
            {
                //判断是否选了门或设备
                var deviceId = $("#deviceId").val();
                if(deviceId == "")
                {
                    var text = "<@i18n 'att_attPoint_door'/>";//默认门
                    if(flag == "park")
                    {
                        text = "<@i18n 'att_attPoint_device'/>"
                    }
                    if(flag == "ins")
                    {
                        text = "<@i18n 'att_attPoint_device'/>"
                    }
                    if(flag == "pid")
                    {
                        text = "<@i18n 'att_attPoint_device'/>"
                    }
                    if(flag == "vms")
                    {
                        text = "<@i18n 'att_attPoint_device'/>"
                    }
                    if(flag == "ivs")
                    {
                        text = "<@i18n 'att_attPoint_device'/>"
                    }
                    if(flag == "esdc")
                    {
                        text = "<@i18n 'att_attPoint_device'/>"
                    }
                    if(flag == "psg")
                    {
                        text = "<@i18n 'att_attPoint_gate'/>"
                    }
                    messageBox({
                        messageType : "alert",
                        text : text
                    });
                }
                else if("${(item.id)}" != "")//编辑时
                {
                    <@submitHandler/>
                }
                else//新增时
                {
                    var text = "<@i18n 'att_attPoint_count'/>";
                    var deviceModule = flag;
                    //判断点数是否超了
                    $.ajax({
                        type: "post",
                        url: "attPoint.do?checkLicenseCount",
                        data:{
                            deviceModule:deviceModule
                        },
                        dataType: "json",
                        async: false,
                        success: function (result) {
                            if(result.ret == "ok")
                            {
                                <@submitHandler/>
                            }
                            else
                            {
                                messageBox({
                                    messageType : "alert",
                                    text : text
                                });
                            }
                        }
                    });
                }
            }
        });
    });
	</script>

	<form action='attPoint.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
		<input type='hidden' name='id' value='${(item.id)!}'/>
        <input id="idStatus" name="status" type="hidden" value="${(item.status)!}" />
        <input id="idSelectedDeviceIds" name="selectedDeviceIds" type="hidden" />
        <input id="deviceId" name="deviceId" type="hidden" value="${(item.deviceId)!}" />
        <input id="deviceSn" name="deviceSn" type="hidden" value="${(item.deviceSn)!}" />
        <input id="ipAddress" name="ipAdress" type="hidden" value="${(item.ipAddress)!}" />
        <input id="doorNo" name="doorNo" type="hidden" value="${(item.doorNo)!}" />

		<table class='tableStyle' >
			<tr>
				<!-- 考勤点名称 -->
				<th><label><@i18n 'att_attPoint_name'/></label><span class='required'>*</span></th>
				<td><input name='pointName' type='text' value='${(item.pointName)!}' maxlength="30"/></td>
			</tr>
			<tr>
                <!-- 设备模块 -->
                <th><label><@i18n 'att_attPoint_deviceModule'/></label><span class='required'>*</span></th>
                <td>
                    <@ZKUI.Combo id="attPointDeviceModuleId" hideLabel="true" width="148" empty="false" name="deviceModule" onChange="changeDeviceModule">
                        <#if !(item.id)>
                            <option selected="selected" disabled="disabled"  style='display: none' value='0'>-----------</option>
                        </#if>
                        <#if (accModule)>
                            <option value="acc" <#if (item.deviceModule)?exists && item.deviceModule=="acc">selected="selected"</#if>>
                                <@i18n 'att_attPoint_acc'/>
                            </option>
                        </#if>
                       <#if (parkModule)>
                            <option value="park"
                             <#if (item.deviceModule)?exists && item.deviceModule=="park">selected="selected"</#if>>
                             <@i18n 'att_attPoint_park'/>
                           </option>
                        </#if>
                        <#if (insModule)>
                            <option value="ins" <#if (item.deviceModule)?exists && item.deviceModule=="ins">selected="selected"</#if>>
                                <@i18n 'att_attPoint_ins'/>
                            </option>
                        </#if>
                        <#if (pidModule)>
                            <option value="pid" <#if (item.deviceModule)?exists && item.deviceModule=="pid">selected="selected"</#if>>
                                <@i18n 'att_attPoint_pid'/>
                            </option>
                        </#if>
                        <#if (vmsModule)>
                            <option value="vms" <#if (item.deviceModule)?exists && item.deviceModule=="vms">selected="selected"</#if>>
                                <@i18n 'att_attPoint_vms'/>
                            </option>
                        </#if>
                        <#if (psgModule)>
                            <option value="psg" <#if (item.deviceModule)?exists && item.deviceModule=="psg">selected="selected"</#if>>
                                <@i18n 'att_attPoint_psg'/>
                            </option>
                        </#if>

                        <#if Application['system.productCode'] != 'ZKBioAccess'>
                            <#if (ivsModule)>
                                <option value="ivs" <#if (item.deviceModule)?exists && item.deviceModule=="ivs">selected="selected"</#if>>
                                    <@i18n 'ivs_module'/>
                                </option>
                            </#if>
                        </#if>
                    </@ZKUI.Combo>
                </td>
            </tr>


            <#if (item.id)?exists>
			<tr>
				<!-- 区域名称 -->
				<th><label><@i18n 'base_area_name'/></label><span class='required'>*</span></th>
				<td><input name="areaName" type="text" value="${(item.areaName)!}" disabled="disabled" /></td>
			</tr>
			<#else>
            <tr>
                <!-- 区域名称 -->
                <th><label><@i18n 'base_area_name'/></label><span class="required">*</span></th>
                <td>
                    <@ZKUI.ComboTree id="attPointAreaSelectId" width="148" autoFirst="true" tree_onCheck="attPointOnTreeChecked"
                        type="radio" url="authArea.do?tree"  value="${(item.areaId)}"  hideLabel="true" name="areaId"/>
                </td>
            </tr>
            </#if>
            <#if (item.id)?exists>
                    <#if item.deviceModule=="acc">
                        <tr>
                            <!-- 选择门双列表 -->
                            <th><label><@i18n 'att_attPoint_doorList'/></label><span class="required">*</span></th>
                            <td>
                                <input id="widgetObjectName" name="deviceName" type="text"
                                       placeholder="${att_attPoint_door}" value="${(item.deviceName)!}"  disabled="disabled"/>
                            </td>
                        </tr>
                    <#else>
                        <tr>
                            <!-- 设备列表 -->
                            <th><label id="idParkLabel"><@i18n 'att_attPoint_deviceList'/></label><span class="required">*</span></th>
                            <td>
                                <input id="idDeviceName" name="deviceName" type="text" value="${(item.deviceName)!}" readonly="readonly" />
                            </td>
                        </tr>
                    </#if>
            </#if>
            <tr id="idAccDeviceTr"  style="display: none;">
                <!-- 门列表 -->
                <th><label><@i18n 'att_attPoint_doorList'/></label><span class="required">*</span></th>
                <td>
                    <input id="widgetObjectName" name="deviceName" type="text" onclick="attPointSelectDoor()"
                           placeholder="<@i18n 'att_attPoint_door'/>"  />
                </td>
            </tr>
            <tr id="idParkDeviceTr" style="display: none;">
                <!-- 通道列表 -->
                <th><label><@i18n 'att_attPoint_channelList'/></label><span class="required">*</span></th>
                <td>
                    <input id="idParkDeviceSelect" name="parkDeviceSelect" type="text" onclick="attPointSelectPark()"
                           placeholder="<@i18n 'att_attPoint_park'/>"  />
                </td>
            </tr>
            <tr id="idInsDeviceTr" style="display: none;">
                <!-- 信息屏列表 -->
                <th><label><@i18n 'att_attPoint_deviceList'/></label><span class="required">*</span></th>
                <td>
                    <input id="idInsDeviceSelect" name="InsDeviceSelect" type="text" onclick="attPointSelectIns()"
                           placeholder="<@i18n 'att_attPoint_ins'/>"  />
                </td>
            </tr>
            <tr id="idPidDeviceTr" style="display: none;">
                <!-- 人证设备列表 -->
                <th><label><@i18n 'att_attPoint_deviceList'/></label><span class="required">*</span></th>
                <td>
                    <input id="idPidDeviceSelect" name="PidDeviceSelect" type="text" onclick="attPointSelectPid()"
                           placeholder="<@i18n 'att_attPoint_pid'/>"  />
                </td>
            </tr>
            <tr id="idVmsDeviceTr" style="display: none;">
                <!-- Vms设备列表 -->
                <th><label><@i18n 'att_attPoint_deviceList'/></label><span class="required">*</span></th>
                <td>
                    <input id="idVmsDeviceSelect" name="VmsDeviceSelect" type="text" onclick="attPointSelectVms()"
                           placeholder="<@i18n 'att_attPoint_vms'/>"  />
                </td>
            </tr>
            <tr id="idPsgDeviceTr" style="display: none;">
                <!-- Psg设备列表 -->
                <th><label><@i18n 'att_attPoint_gateList'/></label><span class="required">*</span></th>
                <td>
                    <input id="idPsgDeviceSelect" name="PsgDeviceSelect" type="text" onclick="attPointSelectPsg()"
                           placeholder="<@i18n 'att_attPoint_gate'/>"  />
                </td>
            </tr>
            <tr id="idPsgRecordTypeTr" style="display: none;">
                <!-- Psg拉取记录类型 -->
                <th><label><@i18n 'att_attPoint_recordTypeList'/></label><span class="required">*</span></th>
                <td>
                    <@ZKUI.Combo id="recordType" hideLabel="true" width="148" empty="false" name="recordType" value="${item.recordType!1}">
                        <option value='1'>
                            <@i18n 'att_attPoint_normalPassRecord'/>
                        </option>
                        <option value='2'>
                            <@i18n 'att_attPoint_verificationRecord'/>
                        </option>
                    </@ZKUI.Combo>
                </td>
            </tr>
            <tr id="idIvsDeviceTr" style="display: none;">
                <!-- ivs设备列表 -->
                <th><label><@i18n 'att_attPoint_deviceList'/></label><span class="required">*</span></th>
                <td>
                    <input id="idIvsDeviceSelect" name="ivsDeviceSelect" type="text" onclick="attPointSelectIvs()"
                           placeholder="<@i18n 'ivs_module'/>"  />
                </td>
            </tr>
            <tr id="idEsdcDeviceTr" style="display: none;">
                <!-- esdc设备列表 -->
                <th><label><@i18n 'att_attPoint_deviceList'/></label><span class="required">*</span></th>
                <td>
                    <input id="idEsdcDeviceSelect" name="esdcDeviceSelect" type="text" onclick="attPointSelectEsdc()"
                           placeholder="<@i18n 'esdc_module'/>"  />
                </td>
            </tr>
		</table>
	</form>

<script type='text/javascript'>
    $(function() {
        if ("${item.id}") {
            ZKUI.Combo.get("attPointDeviceModuleId").combo.disable(true);
        }
        // 当考勤点为通道设备时，显示拉去记录类型
        if("${item.id}" && "${item.deviceModule}"=="psg"){
            ZKUI.Combo.get("recordType").combo.disable(true);
            $("#idPsgRecordTypeTr").show();
        }
    });
</script>
</#macro>