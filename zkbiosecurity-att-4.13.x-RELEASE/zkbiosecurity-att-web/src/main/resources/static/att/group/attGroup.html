<#assign gridName="attGroupGrid${uuid!}">
<@ZKUI.DGrid gridName="${gridName}">
    <@ZKUI.LeftGrid title="att_group_editGroup" width="300">
        <@ZKUI.Searchbar>
            <@ZKUI.SearchTop>
                <tr>
                    <td valign="middle">
                        <@ZKUI.Input name="groupName" maxlength="30" title="common_name" type="text"/>
					</td>
				</tr>
	    	</@ZKUI.SearchTop>
		</@ZKUI.Searchbar>
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem type="refresh" action="commonRefresh" permission="att:group:refresh"></@ZKUI.ToolItem>
    		<@ZKUI.ToolItem id="attGroup.do?edit" text="common_op_new" width="400" height="260" img="comm_add.png" action="commonAdd" permission="att:group:add"></@ZKUI.ToolItem>
    		<@ZKUI.ToolItem id="attGroup.do?del&names=(groupName)" text="common_op_del" img="comm_del.png" action="deleteAttGroup" permission="att:group:del"></@ZKUI.ToolItem>
		</@ZKUI.Toolbar>
		<!-- 配置左表格选中事件 -->
		<@ZKUI.Grid onRowSelect="leftGridClickAttGroup"  vo="com.zkteco.zkbiosecurity.att.vo.AttGroupItem" query="attGroup.do?list"/>
	</@ZKUI.LeftGrid>
	<@ZKUI.RightGrid title="att_group_browseGroupPerson" leftFieldName="groupId" showColumns="!likeName">
		<@ZKUI.Searchbar onBeforeQuery="loadAttPersonSchGrid">
			<@ZKUI.SearchTop>
				<tr>
					<td valign="middle" id="attGroupDateSearch">
						<@ZKUI.Input type="date" id="startDate${uuid!}" endId="endDate${uuid!}" name="startDate" title="common_time_from" todayRange="start" today="true" readonly="false"/>
						&nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
						<@ZKUI.Input type="date" id="endDate${uuid!}"  name="endDate" title="common_to" today="true" offset="1" hideLabel="true" readonly="false"/>
					</td>
					<td valign="middle">
						<@ZKUI.Input name="personPin" maxlength="30" title="att_person_pin" type="text"/>
					</td>
				</tr>
			</@ZKUI.SearchTop>
			<@ZKUI.SearchBelow>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="likeName"  maxlength="30" title="pers_person_wholeName" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Input name="deptName" title="pers_dept_deptName" type="text"/>
					</td>
				</tr>
			</@ZKUI.SearchBelow>
		</@ZKUI.Searchbar>
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem type="refresh" permission="att:groupPerson:refresh"></@ZKUI.ToolItem>
    		<@ZKUI.ToolItem id="attGroupPerson.do?del&names=(personName)" text="pers_common_delPerson" img="comm_del.png" action="delAttGroupPerson" permission="att:groupPerson:del"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="attPersonSch.do?cycleSch" text="att_personSch_cycleSch" title="att_personSch_cycleSch" width="440" height="540" img="comm_add.png" action="attPersonSchCycleSch" permission="att:personsch:cycleSch"/>
			<@ZKUI.ToolItem id="attPersonSch.do?tempSch" text="att_leftMenu_tempSch" title="att_leftMenu_tempSch" width="1080" height="700" img="comm_add.png" action="attPersonSchTempSch" permission="att:personsch:tempSch"/>
			<@ZKUI.ToolItem id="attCycleSch.do?delCycleSch" text="att_personSch_cleanCycleSch" img="comm_del.png" action="attPersonSchCleanCycleSch" permission="att:personsch:cleanCycleSch"/>
			<@ZKUI.ToolItem id="attTempSch.do?delTempSch" text="att_personSch_cleanTempSch" img="comm_del.png" action="attPersonSchCleanTempSch" permission="att:personsch:cleanTempSch"/>
		</@ZKUI.Toolbar>
		<@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttPersonSchItem" query="attPersonSch.do?list"/>
	</@ZKUI.RightGrid>
</@ZKUI.DGrid>
<script type="text/javascript">

	/**
	 * 排班后回调刷新界面
	 */
	function attPersonSchReloadGrid(){
		loadAttPersonSchGrid();
	}

	/*标记分组排班*/
	attPersonSchPageType = "group";

	/*查询重新加载grid*/
	function loadAttPersonSchGrid() {

		var dbGrid = ZKUI.DGrid.get("${gridName}");
		var queryParam = {
			groupId: dbGrid.leftGrid.grid.getSelectedRowId()
		};



		var opts = {
			gridName: "right${gridName}",
			startDate: $('#attGroupDateSearch input[name="startDate"]').val(),
			endDate: $('#attGroupDateSearch input[name="endDate"]').val(),
			queryParam: JSON.stringify(queryParam),
			vo: "com.zkteco.zkbiosecurity.att.vo.AttPersonSchItem",
			query: "attPersonSch.do?list",
			showColumns: "!likeName" // 处理前端最后一列显示换行
		};
		$("#right" + "${gridName}").loadGrid(opts);
		return false;
	}

	/*rigthGrid 删除点击事件调用方法*/
    function delAttGroupPerson(){
		var dbGrid = ZKUI.DGrid.get("${gridName}");
        var leftGrid = dbGrid.leftGrid;
		var rightGridName = dbGrid.leftGrid.gridName.replace('left', 'right');
		var rightGrid = ZKUI.Grid.get(rightGridName);
        var persIds = rightGrid.grid.getCheckedRows(0);
        var groupId = leftGrid.grid.getSelectedRowId();
        if(persIds=="" || persIds==null ){
            messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});  //common_prompt_selectObj=请选择你要操作的对象！
        }else {
            deleteConfirm(function(result){
                if(result){
                    onLoading(function () {
                        $.ajax({
                            url: "attGroupPerson.do?del",
                            type: "post",
                            dataType: "json",
                            data: {
                                ids: persIds,
                                groupId: groupId
                            },
                            success: function (result) {
                                openMessage(msgType.success);
                                leftGridClickAttGroup(groupId);
                            }
                        });
                    });
                }
            },"common_prompt_sureToDelThese");
        }
    }

	/*leftGrid行选中点击事件*/
    function leftGridClickAttGroup(rid, ind) {
		loadAttPersonSchGrid();
    }

    /*leftGrid点击删除,实现删除功能*/
    function deleteAttGroup(id, bar) {
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        var leftGrid = dbGrid.leftGrid;
        var groupIds = leftGrid.grid.getCheckedRows(0);
        if (groupIds == "" || groupIds == null) {
            messageBox({messageType: "alert", text: "<@i18n 'common_prompt_selectObj'/>"});
        } else {
            var checkSelectIds = ZKUI.Grid.get(bar.gridName).grid.getCheckedRows(0);
            var param = splitURL(fillParamsFromGrid(bar.gridName, checkSelectIds, id));
            param.data.ids = checkSelectIds;


            var text = "";
            var checkSelectIdsArray = checkSelectIds.split(",");
            var length = checkSelectIdsArray.length;
            attPersonSchGridNames = "";
            for (var i = 0; i < length; i++) {
                var rowData = leftGrid.grid.getRowData(checkSelectIdsArray[i]);
                if (rowData) {
                    text += " \"" + rowData.groupName + "\" ";
                }
            }

            deleteConfirm(function (result) {
                if (result) {
                    $.ajax({
                        data: param.data,
                        dataType: "json",
                        type: "post",
                        url: param.url,
                        success: function (data) {
                            if (data.ret === sysCfg.success) {
                                openMessage(msgType.success);
                                var dbGrid = ZKUI.DGrid.get("${gridName}");
                                var leftGrid = dbGrid.leftGrid;
                                var rightGrid = dbGrid.rightGrid;
                                leftGrid.reload();
                                rightGrid.reload();
                            } else {
                                openMessage(msgType.error, data.msg);
                            }
                        }
                    });
                }
            }, "<@i18n 'att_personSch_sureDeleteGroup'/>".format(text));
        }
    }

	/*分组添加人员弹窗*/
	function addGroupPerson(gridName, obj, rid) {
		var opts = {
			path: "skip.do?page=att_group_attGroupSelectPerson&groupId=" + rid +"&gridNamePage=${gridName}",
			width: 1100,
			height: 550,
			title: "<@i18n 'pers_common_addPerson'/>",
			gridName: "gridbox"
		};
		DhxCommon.createWindow(opts);
	}

</script>