<#assign editPage = "true">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
<form action='attGroup.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
	<input type='hidden' name='id' value='${(item.id)!}'/>
	<table class='tableStyle'>
		<tr>
			<th><label><@i18n 'common_name'/></label><span class='required'>*</span></th>
			<td><input name='groupName' maxlength="30" type='text' value='${(item.groupName)!}' /></td>
		</tr>
		<tr>
			<th><label><@i18n 'common_remark'/></label></th>
			<td><textarea cols="22" maxlength="50" name="remark" rows="5" style="width: 148px;">${(item.remark)!}</textarea></td>
		</tr>
	</table>
</form>
<script type='text/javascript'>
$('#${formId}').validate({
	debug: true,
	rules: {
		'groupName': {
			required: true,
            unInputChar: true,
			overRemote: ["attGroup.do?validName", "${(item.groupName)!}"]
		}
	},
	submitHandler: function() {
		<@submitHandler/>
	}
});
</script>
</#macro>