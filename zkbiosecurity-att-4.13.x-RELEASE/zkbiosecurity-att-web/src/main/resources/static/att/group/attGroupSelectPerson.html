<@ZKUI.SelectPersContent onSure="attGroupAfterSelectPerson" onCancel="attGroupCancelSelectPerson" gridType = "right" showColumns="checkbox,personPin,personName,deptName" copy="true" textField="personId" vo="com.zkteco.zkbiosecurity.att.vo.AttGroupPersonSelectItem" deptQuery= "authDepartment.do?dynaTree" query="attGroup.do?selectList&linkId=${groupId}">
	<@ZKUI.Searchbar autoReload="false">
    	<@ZKUI.SearchTop>
    		<tr>
			    <td valign="middle">
			    	<@ZKUI.Input name="personPin" maxlength="30" title="att_person_pin" type="text"/>
			    </td>
				<td valign="middle">
					<@ZKUI.Input name="likeName" maxlength="30" title="pers_person_wholeName" type="text"/>
			    </td>
			</tr>
		</@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
</@ZKUI.SelectPersContent>
<script type="text/javascript">
	function attGroupAfterSelectPerson(value, text, event) {
        onLoading(function () {
			$.ajax({
				type: "POST",
				dataType: "json",
				url: "attGroupPerson.do?save",
				data: {
					"personIds" : value,
					"groupId" : "${groupId}"
				},
				success: function (data)
				{
					if (data.ret == "ok")
					{
						openMessage(msgType.success);
						var dbGrid = ZKUI.DGrid.get("${gridNamePage}");
						var leftGrid = dbGrid.leftGrid;
						var groupId = leftGrid.grid.getSelectedRowId();
						attSelectRow = leftGrid.grid.getRowIndex(leftGrid.grid.getSelectedRowId());
						leftGridClickAttGroup(groupId,attSelectRow);
					}
					else
					{
						openMessage(msgType.error, data.msg);
					}
				}
			});
        });
    }

    function attGroupCancelSelectPerson() {
		var dbGrid = ZKUI.DGrid.get("${gridNamePage}");
		var leftGrid = dbGrid.leftGrid;
		var groupId = leftGrid.grid.getSelectedRowId();
		attSelectRow = leftGrid.grid.getRowIndex(leftGrid.grid.getSelectedRowId());
		leftGridClickAttGroup(groupId,attSelectRow);
    }
</script>