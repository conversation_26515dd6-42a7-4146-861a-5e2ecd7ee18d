<div id="divSearchType" style="height:25px;line-height:25px;display:block">
    <label style="margin-${leftRTL!'left'}:5px"><input name="searchType" value="1" checked="" onclick="deptSwith(1);" type="radio">&nbsp;<@i18n 'att_widget_searchType'/></label>
    <label style="margin-${leftRTL!'left'}:30px"><input name="searchType" value="2" type="radio" onclick="deptSwith(2);">&nbsp;<@i18n 'pers_dept_entity'/></label>
</div>
<div id="maskLayer${uuid}" style="position: absolute;width: 100%;display: none; top:30px;bottom:40px;z-index: 10000;opacity: 0.5;background-color: #dcdfe2;"></div>
<@ZKUI.SelectContent uuid="${uuid}" gridName="attSelect${uuid}"  style="position:absolute;top:30px;bottom:0px;left:0px;right:0px;height:auto;" copy="true" textField="personPin" linkId="${linkId!}" vo="com.zkteco.zkbiosecurity.att.vo.AttGroupPersonSelectItem" query="attGroup.do?selectList&linkId=${linkId!}">
    <div id="deptTree${uuid}" class="zk-bg-color" style="position: absolute;z-index: 10001;display: none;background-color: #f3f5f0;opacity:1;width:100%;height: 42px;padding-top: 15px;">
        <@ZKUI.ComboTree id="personDeptTree${uuid}" type="checkbox" url="authDepartment.do?tree" tree_onCheck="onTreeChecked" title="att_common_deptName" width="148" readonly="true" name="deptIds"/>
        <div id="deptZone" style="display:inline-block;">
            <span style="color:#FF9900; position : relative; padding-${leftRTL!'left'}: 50px;"><@i18n 'att_widget_deptHint'/></span>
        </div>
    </div>
    <@ZKUI.Searchbar autoReload="false">
        <@ZKUI.SearchTop>
            <tr>
                <!--人员编号-->
                <td valign="middle">
                    <@ZKUI.Input name="personPin"  maxlength="30" title="att_person_pin" type="text"/>
                </td>
                <!--姓名-->
                <td valign="middle">
                    <@ZKUI.Input name="likeName" maxlength="30" title="pers_person_wholeName" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
        <@ZKUI.SearchBelow>
            <tr>
                <!--部门-->
                <td valign="middle">
                    <@ZKUI.ComboTree width="148" type="radio" url="authDepartment.do?tree" title="att_common_dept" name="deptId"/>
                </td>
            </tr>
        </@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
</@ZKUI.SelectContent>
<script type="text/javascript">
    function deptSwith(t){
        if(t == 2){
            $("#maskLayer${uuid},#deptTree${uuid}").show();
            $("#"+ ZKUI.Select.get("attSelect${uuid}").options.gridName+"ok").attr("disabled", false);
        }else{
            ZKUI.ComboTree.get("personDeptTree${uuid}").getValue("");
            $("#maskLayer${uuid},#deptTree${uuid}").hide();

        }
    };
</script>