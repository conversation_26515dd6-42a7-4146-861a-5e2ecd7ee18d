<#include "/public/template/setTemplate.html">
<#assign formId="attRuleEditForm${uuid!}">
<#macro setContent>
<link href="css/sidenavFieldset.css" rel="stylesheet" type="text/css" />
<script src="js/sidenavFieldset.js"></script>
<script>
	/** 点击checkbox，选中赋值name为0，不选中赋值name为1*/
	function attCheckBoxClickExt(el, name) {
		if ($(el).prop("checked")) {
			$("input[name='" + name + "']").val(0);
		} else {
			$("input[name='" + name + "']").val(1);
		}
	}

	function attLeaveTypeNameClick(id) {

		$('p[id*="id_attLeaveType"]').each(function () {
			$(this).attr("class", "att-hover-color zk-hover-color");
			$(this).removeClass("zk-bg-color");
		});

		$("#id_attLeaveType" + id).addClass("zk-bg-color");

		$('div[id*="id_Fieldset"]').each(function () {
			$(this).attr("class", "attConvertContent attConvertContentHiden");
		});

		$("#id_Fieldset" + id).attr("class", "attConvertContent attConvertContentShow");
	}

</script>
<style>
	.attConvertSetting {
		height: auto!important;
	}
	.attConvertType {
		width: 400px!important;
		border: 1px solid #B9C5D0;
		display: inline-block;
		vertical-align: top;
		height: 150px!important;
		overflow-y: auto;
		padding: 10px 0px;
		border-radius: 4px;
	}
	.attConvertType p {
		padding: 5px 12px;
		cursor: pointer;
	}

	.attConvertContentHiden {
		display: none;
	}

	.attConvertContentShow {
		display: inline-block!important;
	}

	.attConvertType .att-hover-color:hover{
		background-color: #e8eaeb;
	}

	.attConvertContent {
		vertical-align: top;
		margin-left: 0px;
		margin-top: 14px;
		display: none;
	}

	.attRule-annualLeave-setting {
		 margin-bottom: 15px;
	}
	.attRule-annualLeave-setting .tableStyle td {
		padding: 5px 21px 4px 0;
	}
	.attRule-annualLeave-rule {
		width: 100%;
		margin-left: 10px;
	}
	.attRule-annualLeave-rule td{
		padding: 5px 0px 4px 0px!important;
	}
	.attRule-annualLeave-input {
		max-width: 30px!important;
		text-align: center;
		margin: 0px 5px;
	}
	.attRule-annualLeave-rule tr .attRule-annualLeave-img-add {
        visibility: hidden;
    }
    .attRule-annualLeave-rule tr:nth-last-child(2) .attRule-annualLeave-img-add{
        visibility: visible;
    }
    .attSetFormTemplate .input400 {
    	width: 394px!important;
    }
    .attSetFormTemplate .newPadding {
    	padding-bottom: 16px;
    	padding-top: 8px;
    	position: relative;
    }
    .attCardStatusClass {
    	width:500px;
    }

    .attCardStatusClass td {
    	padding: 5px 10px 5px 0px;
    }
    .attCardStatusClass .search-label {
    	padding: 0px;
    }

</style>
<div id="setTemplate">
	<div class="setFormTemplate attSetFormTemplate">
		<form action="attRule.do?save" enctype="multipart/form-data"
			id="${formId}" method="post" onkeydown="if(event.keyCode==13){return false;}">
			<fieldset>
				<legend><@i18n 'att_rule_baseRuleSet'/></legend>
				<table class="tableStyle">
					<!-- 上班签到取卡记录原则 -->
					<tr>
						<td class="formTitle"><@i18n 'att_rule_baseRuleSignIn'/></td>
					</tr>
					<tr>
						<td class="newPadding">
							<@ZKUI.Combo width="400" id="att_baseRuleSignIn" readonly="true" empty="false" value="${attParams['att.baseRule.signIn']!}" hideLabel="true" name="att.baseRule.signIn">
								<option value="0"><@i18n 'att_rule_earliestPrinciple'/></option>
								<option value="1"><@i18n 'att_rule_principleOfProximity'/></option>
							</@ZKUI.Combo>
						</td>
					</tr>
					<!-- 下班签退取卡记录原则 -->
					<tr>
						<td class="formTitle"><@i18n 'att_rule_baseRuleSignOut'/></td>
					</tr>
					<tr>
						<td class="newPadding">
							<@ZKUI.Combo width="400" id="att_baseRuleSignOut" readonly="true" empty="false" value="${attParams['att.baseRule.signOut']!}" hideLabel="true" name="att.baseRule.signOut">
								<option value="0"><@i18n 'att_rule_theLatestPrinciple'/></option>
								<option value="1"><@i18n 'att_rule_principleOfProximity'/></option>
							</@ZKUI.Combo>
						</td>
					</tr>
					<!-- 班次时间段跨天时，考勤计算结果 -->
					<tr>
						<td class="formTitle"><@i18n 'att_rule_baseRuleCrossDay'/></td>
					</tr>
					<tr>
						<td class="newPadding">
							<@ZKUI.Combo width="400" id="att_baseRuleCrossDay" readonly="true" empty="false" value="${attParams['att.baseRule.crossDay']!}" hideLabel="true" name="att.baseRule.crossDay">
							<option value="0"><@i18n 'att_rule_firstDay'/></option>
							<option value="1"><@i18n 'att_rule_secondDay'/></option>
						</@ZKUI.Combo>
						</td>
					</tr>
					<!-- 是否统计加班 -->
					<tr>
						<td class="formTitle"><@i18n 'att_rule_baseRuleCountOvertime'/></td>
					</tr>
					<tr>
						<td class="newPadding">
							<@ZKUI.Combo width="400" id="att_baseRuleCountOvertime" readonly="true" empty="false" value="${attParams['att.baseRule.countOvertime']!}" hideLabel="true" name="att.baseRule.countOvertime">
								<option value="true"><@i18n 'common_yes'/></option>
								<option value="false"><@i18n 'common_no'/></option>
							</@ZKUI.Combo>
						</td>
					</tr>

					<!-- 智能找班原则 -->
					<tr>
						<td class="formTitle">
							<@i18n 'att_rule_baseRuleSmartFindClass'/>
							<span class="attTooltip">
								<div class="att_icv att_icv-green icv-ic_que zk-colors-fg-green" />
								<div class="attCommonTipClass attCommonTipClass400">
									<p class="warningColor"><@i18n 'att_rule_baseRuleSmartFindRemark1'/></p>
									<p class="warningColor"><@i18n 'att_rule_baseRuleSmartFindRemark2'/></p>
								</div>
							</span>
						</td>
					</tr>
					<tr>
						<td class="newPadding">
							<@ZKUI.Combo width="400" id="att_baseRuleSmartFindClass" readonly="true" empty="false" value="${attParams['att.baseRule.smartFindClass']!}" hideLabel="true" name="att.baseRule.smartFindClass">
								<option value="0"><@i18n 'att_rule_timeLongest'/></option>
								<option value="1"><@i18n 'att_rule_exceptionLeast'/></option>
							</@ZKUI.Combo>
						</td>
					</tr>
					<!-- 迟到早退算旷工 -->
					<tr>
						<td class="formTitle"><@i18n 'att_rule_baseRuleLateAndEarly'/></td>
					</tr>
					<tr>
						<td class="newPadding">
							<@ZKUI.Combo width="400" id="att_baseRuleLateAndEarly" readonly="true" empty="false" value="${attParams['att.baseRule.lateAndEarly']!}" hideLabel="true" name="att.baseRule.lateAndEarly">
							<option value="true"><@i18n 'common_yes'/></option>
							<option value="false"><@i18n 'common_no'/></option>
						</@ZKUI.Combo>
						</td>
					</tr>
					<!-- 未签到考勤规则 -->
					<tr>
						<td class="formTitle"><@i18n 'att_rule_noSignInCountType'/></td>
					</tr>
					<tr>
						<td class="newPadding">
							<@ZKUI.Combo width="194" id="att_baseRuleNoSignInCountType" readonly="true" empty="false" value="${attParams['att.baseRule.noSignInCountType']!}" hideLabel="true" name="att.baseRule.noSignInCountType">
								<option value="absent"><@i18n 'att_common_absent'/></option>
								<option value="late"><@i18n 'att_common_late'/></option>
								<option value="incomplete"><@i18n 'att_rule_incomplete'/></option>
							</@ZKUI.Combo>
							<input style="width: 188px;margin-left:6px;" id="att_baseRuleNoSignInCountLateMinute" maxlength="5" name="att.baseRule.noSignInCountLateMinute"
								   onkeyup="this.value=this.value.replace(/\D/g,'');" onafterpaste="this.value=this.value.replace(/\D/g,'');"
								   type="text" value="${attParams['att.baseRule.noSignInCountLateMinute']!30}" onblur="checkValue(this)"/>
							<label><@i18n 'att_rule_minutes'/></label>
							<span class="attTooltip">
								<div class="att_icv att_icv-green icv-ic_que zk-colors-fg-green" />
								<div class="attCommonTipClass attCommonTipClass400">
									<p class="warningColor"><@i18n 'att_rule_isInCompleteTip'/></p>
									<p class="warningColor"><@i18n 'att_rule_baseRuleNoSignInCountLateMinuteRemark'/></p>
									<p class="warningColor"><@i18n 'att_rule_absentTip'/></p>
								</div>
							</span>
						</td>
					</tr>
					<!-- 未签退考勤规则 -->
					<tr>
						<td class="formTitle"><@i18n 'att_rule_noSignOffCountType'/></td>
					</tr>
					<tr>
						<td class="newPadding">
							<@ZKUI.Combo width="194" id="att_baseRuleNoSignOffCountType" readonly="true" empty="false" value="${attParams['att.baseRule.noSignOffCountType']!}" hideLabel="true" name="att.baseRule.noSignOffCountType">
								<option value="absent"><@i18n 'att_common_absent'/></option>
								<option value="early"><@i18n 'att_rule_earlyLeave'/></option>
								<option value="incomplete"><@i18n 'att_rule_incomplete'/></option>
							</@ZKUI.Combo>
							<input style="width: 188px;margin-left:6px;" id="att_baseRuleNoSignOffCountEarlyMinute" maxlength="5" name="att.baseRule.noSignOffCountEarlyMinute"
								   onkeyup="this.value=this.value.replace(/\D/g,'');" onafterpaste="this.value=this.value.replace(/\D/g,'');"
								   type="text" value="${attParams['att.baseRule.noSignOffCountEarlyMinute']!30}" onblur="checkValue(this)"/>
							<label><@i18n 'att_rule_minutes'/></label>
							<div class="attTooltip">
								<div class="att_icv att_icv-green icv-ic_que zk-colors-fg-green" />
								<div class="attCommonTipClass attCommonTipClass400">
									<p class="warningColor"><@i18n 'att_rule_isInCompleteTip'/></p>
									<p class="warningColor"><@i18n 'att_rule_baseRuleNoSignOffCountEarlyMinuteRemark'/></p>
								</div>
							</span>
						</td>
					</tr>
					<!-- 最短的考勤时段 -->
					<tr>
						<td class="formTitle"><@i18n 'att_rule_baseRuleShortestMinutes'/></td>
					</tr>
					<tr>
						<td class="newPadding">
							<input class="input400" id="att_baseRuleShortestMinutes" maxlength="3" name="att.baseRule.shortestMinutes"
								   onkeyup="this.value=this.value.replace(/\D/g,'');" onafterpaste="this.value=this.value.replace(/\D/g,'');"
								   type="text" value="${attParams['att.baseRule.shortestMinutes']!}" />
						</td>
					</tr>
					<!-- 最长的考勤时段 -->
					<tr>
						<td class="formTitle"><@i18n 'att_rule_baseRuleLongestMinutes'/></td>
					</tr>
					<tr>
						<td class="newPadding">
							<input class="input400" id="att_baseRuleLongestMinutes" maxlength="4" name="att.baseRule.longestMinutes"
								   onkeyup="this.value=this.value.replace(/\D/g,'');" onafterpaste="this.value=this.value.replace(/\D/g,'');"
								   type="text" value="${attParams['att.baseRule.longestMinutes']!}" />
						</td>
					</tr>
					<!-- 小数点精确位数 -->
					<tr>
						<td class="formTitle"><@i18n 'att_rule_countConvertDecimal'/></td>
					</tr>
					<tr>
						<td class="newPadding">
							<@ZKUI.Combo width="400" id="att_countConvertDecimal" readonly="true" empty="false" value="${attParams['att.countConvert.decimal']!}" hideLabel="true" name="att.countConvert.decimal">
								<option value="1">1</option>
								<option value="2">2</option>
							</@ZKUI.Combo>
						</td>
					</tr>
				</table>
			</fieldset>

			<!--非假类计算设置-->
			<fieldset style="padding: 8px">
				<legend><@i18n 'att_param_notLeaveSetting'/></legend>
				<div class="attConvertSetting">
					<div class="attConvertType zk-border-color-gray">
						<#list attLeaveTypeItemList as item>
							<p class="att-hover-color zk-hover-color <#if item_index == '0'> zk-bg-color </#if>" id="id_attLeaveType${item.id}" onclick="attLeaveTypeNameClick('${item.id}')">${item.leaveTypeName}</p>
						</#list>
					</div>
				</div>
				<#list attLeaveTypeItemList as item>
				<div id="id_Fieldset${item.id}" class="attConvertContent <#if item_index == '0'>attConvertContentShow</#if>">
					<!--<legend>${item.leaveTypeName}</legend>-->
					<table class="tableStyle">
						<input type='hidden' name='attLeaveTypeItemList[${item_index}].id' value='${(item.id)!}'/>
						<#if item.convertCount != '-1'>
						<!-- 最小单位 -->
						<tr>
							<td class="formTitle"><@i18n 'att_param_smallestUnit'/><span class='required'>*</span></td>
						</tr>
						<tr>
							<td class="newPadding">
								<input style="width: 188px;margin-right:6px;" name="attLeaveTypeItemList[${item_index}].convertCount" value="${item.convertCount?c}" id="id_convertCount" maxlength="10" type="text" />
								<@ZKUI.Combo width="194" name="attLeaveTypeItemList[${item_index}].convertUnit" value="${item.convertUnit}" empty="false" hideLabel="true" title="att_param_smallestUnit">
									<option value="minute"><@i18n 'common_minute'/></option>
									<option value="hour"><@i18n 'common_hours'/></option>
									<option value="day"><@i18n 'att_param_workDay'/></option>
								</@ZKUI.Combo>
							</td>
						</tr>
						<!-- 舍入控制 -->
						<tr>
							<td class="formTitle"><@i18n 'att_param_roundingControl'/><span class='required'>*</span></td>
						</tr>
						<tr>
							<td class="newPadding">
								<@ZKUI.Combo width="400" name="attLeaveTypeItemList[${item_index}].convertType" value="${item.convertType}" empty="false" hideLabel="true" title="att_param_roundingControl">
									<option value="abort"><@i18n 'att_param_abort'/></option>
									<option value="rounding"><@i18n 'att_param_rounding'/></option>
									<option value="carry"><@i18n 'att_param_carry'/></option>
								</@ZKUI.Combo>
							</td>
						</tr>
						</#if>
						<!-- 报表展示符号 -->
						<tr>
							<td class="formTitle"><@i18n 'att_param_reportSymbol'/><span class='required'>*</span></td>
						</tr>
						<tr>
							<td class="newPadding">
								<input class="input400" name="attLeaveTypeItemList[${item_index}].symbol" value="${item.symbol}" maxlength="20" type="text" />
							</td>
						</tr>
						<!-- 报表展示颜色 -->
						<tr>
							<td class="formTitle"><@i18n 'att_param_reportColor'/><span class='required'>*</span></td>
						</tr>
						<tr>
							<td class="newPadding">
								<input class="input400" name="attLeaveTypeItemList[${item_index}].color" value="${item.color}" maxlength="20" type="color" />
							</td>
						</tr>
					</table>
				</div>
				</#list>
			</fieldset>

			<!-- 年休假结余设置 -->
			<#if attParams['att.annualLeave.enable']=="0">
			<!--<fieldset class="attRule-annualLeave-setting">-->
			<fieldset>
				<legend><@i18n 'att_annualLeave_setting'/></legend>
				<table class="tableStyle">
					<tr><td><span style="position: relative; bottom: -3px;" class="warningImage"></span><label class="warningColor"><@i18n 'att_annualLeave_settingTip1'/></label></td></tr>
					<tr><td style="padding-bottom: 14px;"><span style="position: relative; bottom: -3px;" class="warningImage"></span><label class="warningColor"><@i18n 'att_annualLeave_settingTip2'/></label></td></tr>
					<tr>
						<td class="formTitle"><@i18n 'att_annualLeave_calculate'/></td>
					</tr>
					<tr>
						<td class="newPadding">
							<label><@i18n 'att_annualLeave_eachYear'/></label>
								<@ZKUI.Combo id="attAnnualLeaveCalculateMonth" width="150" name="att.annualLeave.calculateMonth" value="${attParams['att.annualLeave.calculateMonth']!}" readonly="true" empty="false" hideLabel="true">
									<#list 1..12 as month>
										<option value="${month}">${month}</option>
									</#list>
								</@ZKUI.Combo>
							<label style="margin:0px 5px"><@i18n 'common_month'/></label>
								<@ZKUI.Combo id="attAnnualLeaveCalculateDay" width="150" name="att.annualLeave.calculateDay" value="${attParams['att.annualLeave.calculateDay']!}" readonly="true" empty="false" hideLabel="true">
								</@ZKUI.Combo>
							<label style="margin-left: 5px"><@i18n 'common_day'/></label>
						</td>
					</tr>
					<tr>
						<td class="formTitle attAnnualLeaveCalculateTypeClass">
							<span style="font-size: 12px;"><@ZKUI.Input id="attAnnualLeaveCalculateType" hideLabel="true" type="checkbox" name="att.annualLeave.enableCalculateType" value="${attParams['att.annualLeave.enableCalculateType']!}" trueValue="true" falseValue="false" eventCheck=true/></span>
							<@i18n 'att_annualLeave_workTimeCalculate'/>
							<span class="attTooltip">
								<div class="att_icv att_icv-green icv-ic_que zk-colors-fg-green" />
								<div class="attCommonTipClass attCommonTipClass400">
									<div class="title warningColor"><@i18n "att_annualLeave_calculateTip1"/></div>
									<div class="title warningColor"><@i18n "att_annualLeave_calculateTip2"/></div>
								</div>
							</span>
						</td>
					</tr>
					<tr>
						<td style="padding-bottom: 12px;padding-top:2px;" class="attAnnualLeaveCalculateTypeClass">
							<label for="attAnnualLeaveAbort">
								<@ZKUI.Input hideLabel="true" name="att.annualLeave.calculateType" value="abort" id="attAnnualLeaveAbort" type="radio"/>
								<span><@i18n "att_param_abort"/></span>
							</label>
							<label style="margin-left: 20px;" for="attAnnualLeaveRounding">
								<@ZKUI.Input hideLabel="true" name="att.annualLeave.calculateType" value="rounding" id="attAnnualLeaveRounding" type="radio"/>
								<span><@i18n "att_param_rounding"/></span>
							</label>
							<label style="margin-left: 20px;" for="attAnnualLeaveCarry">
								<@ZKUI.Input hideLabel="true" name="att.annualLeave.calculateType" value="carry" id="attAnnualLeaveCarry" type="radio"/>
								<span><@i18n "att_param_carry"/></span>
							</label>
						</td>
					</tr>
					<tr>
						<td>
							<div style="display: inline-block; vertical-align: top;">
								<span class="formTitle"><@i18n 'att_annualLeave_rule'/></span>
							</div>
						</td>
					</tr>
					<tr>
						<td>

							<div style="display: inline-block;min-height: 108px;">
								<table id="attAnnualLeaveRule" class="attRule-annualLeave-rule">
									<#list attAnnualLeaveRuleItemList as item>
									<tr id="annualLeaveRuleTr${item_index}">
										<td style="text-align:center">
											<#if item_index == 0 >
											<input onkeyup="this.value=this.value.replace(/\D/g,'')" class="attRule-annualLeave-input" name="attAnnualLeaveRule[${item_index}].startYear" value="${item.startYear}" type="hidden" />
											<#elseif item_index == 1>
											<input onkeyup="this.value=this.value.replace(/\D/g,'')" class="attRule-annualLeave-input" name="attAnnualLeaveRule[${item_index}].startYear" value="${item.startYear}" maxlength="3" type="text" readonly/><@i18n "base_cron_year"/> ≤
											<#elseif item_index == (attAnnualLeaveRuleItemList?size - 1)>
											<input onkeyup="this.value=this.value.replace(/\D/g,'')" class="attRule-annualLeave-input" name="attAnnualLeaveRule[999].endYear" value="${item.endYear}" type="hidden" />
											<#else>
											<input onkeyup="this.value=this.value.replace(/\D/g,'')" class="attRule-annualLeave-input" name="attAnnualLeaveRule[${item_index}].startYear" value="${item.startYear}" maxlength="3" type="text" /><@i18n "base_cron_year"/> ≤
										</#if>
										</td>
										<td>
											<#if item_index == (attAnnualLeaveRuleItemList?size - 1) >
												<@i18n "att_annualLeave_years"/> ≥
												<#else>
												<@i18n "att_annualLeave_years"/> <
											</#if>
										</td>
										<td>
											<#if item_index == 0 >
												<input onkeyup="this.value=this.value.replace(/\D/g,'')" class="attRule-annualLeave-input" name="attAnnualLeaveRule[${item_index}].endYear" value="${item.endYear}" maxlength="3" type="text" readonly/>
												<#elseif item_index == (attAnnualLeaveRuleItemList?size - 1) >
												<input onkeyup="this.value=this.value.replace(/\D/g,'')" class="attRule-annualLeave-input" name="attAnnualLeaveRule[999].startYear" value="${item.startYear}" maxlength="3" type="text"/>
												<#else>
												<input onkeyup="this.value=this.value.replace(/\D/g,'')" class="attRule-annualLeave-input" name="attAnnualLeaveRule[${item_index}].endYear" value="${item.endYear}" maxlength="3" type="text"/>
											</#if>
										</td>
										<td><@i18n "base_cron_year"/>，</td>
										<td>
											<@i18n "att_annualLeave_have"/>
											<#if item_index == (attAnnualLeaveRuleItemList?size - 1)>
												<input onkeyup="this.value=this.value.replace(/\D/g,'')" name="attAnnualLeaveRule[999].days" value="${item.days}" maxlength="3" type="text" class="attRule-annualLeave-input"/>
												<#else>
												<input onkeyup="this.value=this.value.replace(/\D/g,'')" name="attAnnualLeaveRule[${item_index}].days" value="${item.days}" maxlength="3" type="text" class="attRule-annualLeave-input"/>
											</#if>
											<@i18n "att_annualLeave_days"/>
										</td>
										<td>
											<div onclick="addAnnualLeaveRuleTr()" class="attRule-annualLeave-img-add att_icv att_icv-green icv-ic_add zk-colors-fg-green"/>
											<#if item_index != 0 && item_index != 1 && item_index != (attAnnualLeaveRuleItemList?size - 1)>
												<div onclick="delAnnualLeaveRuleTr(this)" class="att_icv warningColor icv-ic_del"/>
											</#if>
										</td>
									</tr>
								</#list >
								</table>
							</div>
						</td>
					</tr>
				</table>
			</fieldset>
			</#if>
			<!-- 打卡状态设置 -->
			<fieldset style="margin-bottom: 15px;">
				<legend><@i18n 'att_cardStatus_setting'/></legend>
				<table class="tableStyle attCardStatusClass">
					<!--名称,值,别名-->
					<tr>
						<td class="formTitle"><@i18n 'att_cardStatus_name'/> :</td>
						<td class="formTitle" style="display: none;"><@i18n 'att_cardStatus_value'/> :</td>
						<td class="formTitle"><@i18n 'att_cardStatus_alias'/> :</td>
					</tr>
					<!--签到-->
					<tr>
						<td><@i18n 'att_cardStatus_signIn'/></td>
						<td style="display: none;" ><@ZKUI.Input id="valueOne" type="text" maxlength="30" onchange="updateValueOne()"/></td>
						<td><@ZKUI.Input name="att.cardStatus.f1.showName" id="desOne" type="text" maxlength="30" onchange="updateValueOne()"/></td>
						<td><@ZKUI.Input name="att.cardStatus.f1" type="text" style="display: none;"/></td>
					</tr>
					<!--签退-->
					<tr>
						<td><@i18n 'att_cardStatus_signOut'/></td>
						<td style="display: none;" ><@ZKUI.Input id="valueTwo" type="text" maxlength="30" onchange="updateValueTwo()"/></td>
						<td><@ZKUI.Input name="att.cardStatus.f2.showName" id="desTwo" type="text" maxlength="30" onchange="updateValueTwo()"/></td>
						<td><@ZKUI.Input name="att.cardStatus.f2" type="text" style="display: none;"/></td>
					</tr>
					<!--外出-->
					<tr>
						<td><@i18n 'att_cardStatus_out'/></td>
						<td style="display: none;" ><@ZKUI.Input id="valueThree" type="text" maxlength="30" onchange="updateValueThree()"/></td>
						<td><@ZKUI.Input name="att.cardStatus.f3.showName" id="desThree" type="text" maxlength="30" onchange="updateValueThree()"/></td>
						<td><@ZKUI.Input name="att.cardStatus.f3" type="text" style="display: none;"/></td>
					</tr>
					<!--外出返回-->
					<tr>
						<td><@i18n 'att_cardStatus_outReturn'/></td>
						<td style="display: none;" ><@ZKUI.Input id="valueFour" type="text" maxlength="30" onchange="updateValueFour()"/></td>
						<td><@ZKUI.Input name="att.cardStatus.f4.showName" id="desFour"type="text" maxlength="30" onchange="updateValueFour()"/></td>
						<td><@ZKUI.Input name="att.cardStatus.f4" type="text" style="display: none;"/></td>
					</tr>
					<!--加班签到-->
					<tr>
						<td><@i18n 'att_cardStatus_overtime_signIn'/></td>
						<td style="display: none;" ><@ZKUI.Input id="valueFive" type="text" maxlength="30" onchange="updateValueFive()"/></td>
						<td><@ZKUI.Input name="att.cardStatus.f5.showName" id="desFive" type="text" maxlength="30" onchange="updateValueFive()"/></td>
						<td><@ZKUI.Input name="att.cardStatus.f5" type="text" style="display: none;"/></td>
					</tr>
					<!--加班签退-->
					<tr>
						<td><@i18n 'att_cardStatus_overtime_signOut'/></td>
						<td style="display: none;" ><@ZKUI.Input id="valueSix" type="text" maxlength="30" onchange="updateValueSix()"/></td>
						<td><@ZKUI.Input name="att.cardStatus.f6.showName" id="desSix" type="text" maxlength="30" onchange="updateValueSix()"/></td>
						<td><@ZKUI.Input name="att.cardStatus.f6" type="text" style="display: none;"/></td>
					</tr>
				</table>
			</fieldset>

			<!-- 加班等级设置 -->
			<@ZKUI.Permission name="att:rule:overTimeLevelSet">
			<fieldset style="margin-bottom: 15px;">
				<legend><@i18n 'att_param_overTimeSetting'/></legend>
				<table class="tableStyle"  style="width:500px">
					<tr><td class="formTitle"><@i18n 'att_param_overTimeLevelEnable'/></td></tr>
					<tr>
						<td class="newPadding">
							<label for="attOvertimeSettingEnable0">
								<@ZKUI.Input hideLabel="true" id="attOvertimeSettingEnable0" name="att.overtimeLevel.enable" type="radio" value="0" />
								<@i18n 'common_enable'/>
							</label>
							<label style="margin-left: 20px;" for="attOvertimeSettingEnable1">
								<@ZKUI.Input hideLabel="true" id="attOvertimeSettingEnable1" name="att.overtimeLevel.enable" type="radio" value="1" />
								<@i18n 'common_disable'/>
							</label>
						</td>
					</tr>
					<tr class="attOvertimeSettingTr"><td class="formTitle"><@i18n 'att_overtime_normal'/><@i18n 'att_overtime_overtimeHour'/></td></tr>
					<tr class="attOvertimeSettingTr">
						<td class="newPadding">
							OT1: <input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')" id="attNormalOT1Start" />
							-
							<input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')" id="attNormalOT1End" />
							<input name="att.overtimeLevel.normalOT1" type="hidden" />
						</td>
					</tr>
					<tr class="attOvertimeSettingTr">
						<td class="newPadding">
							OT2: <input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')" id="attNormalOT2Start" />
							-
							<input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')" id="attNormalOT2End" />
							<input name="att.overtimeLevel.normalOT2" type="hidden" />
						</td>
					</tr>
					<tr class="attOvertimeSettingTr">
						<td class="newPadding">
							OT3: <input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')" id="attNormalOT3Start" />
							-
							<input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')" id="attNormalOT3End" />
							<input name="att.overtimeLevel.normalOT3" type="hidden" />
						</td>
					</tr>
					<!-- 休息日加班 -->
					<tr class="attOvertimeSettingTr"><td class="formTitle"><@i18n 'att_overtime_rest'/><@i18n 'att_overtime_overtimeHour'/></td></tr>
					<tr class="attOvertimeSettingTr">
						<td class="newPadding">
							OT1: <input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')" id="attRestOT1Start" />
							-
							<input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')" id="attRestOT1End" />
							<input name="att.overtimeLevel.restOT1" type="hidden" />
						</td>
					</tr>
					<tr class="attOvertimeSettingTr">
						<td class="newPadding">
							OT2: <input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')" id="attRestOT2Start" />
							-
							<input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')" id="attRestOT2End" />
							<input name="att.overtimeLevel.restOT2" type="hidden" />
						</td>
					</tr>
					<tr class="attOvertimeSettingTr">
						<td class="newPadding">
							OT3: <input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')" id="attRestOT3Start" />
							-
							<input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')" id="attRestOT3End" />
							<input name="att.overtimeLevel.restOT3" type="hidden" />
						</td>
					</tr>
					<!-- 节假日加班 -->
					<tr class="attOvertimeSettingTr"><td class="formTitle"><@i18n 'att_shift_holidayOt'/><@i18n 'att_overtime_overtimeHour'/></td></tr>
					<tr class="attOvertimeSettingTr">
						<td class="newPadding">
							OT1: <input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')" id="attHolidayOT1Start" />
							-
							<input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')" id="attHolidayOT1End" />
							<input name="att.overtimeLevel.holidayOT1" type="hidden" />
						</td>
					</tr>
					<tr class="attOvertimeSettingTr">
						<td class="newPadding">
							OT2: <input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')" id="attHolidayOT2Start" />
							-
							<input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')" id="attHolidayOT2End" />
							<input name="att.overtimeLevel.holidayOT2" type="hidden" />
						</td>
					</tr>
					<tr class="attOvertimeSettingTr">
						<td class="newPadding">
							OT3: <input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')" id="attHolidayOT3Start" />
							-
							<input type="text" onkeyup="this.value=this.value.replace(/\D/g,'')" id="attHolidayOT3End" />
							<input name="att.overtimeLevel.holidayOT3" type="hidden" />
						</td>
					</tr>
				</table>
			</fieldset>
			</@ZKUI.Permission>

			<!-- 实时点名参数设置 -->
			<@ZKUI.Permission name="att:rule:realTimeSet">
			<fieldset style="margin-bottom: 15px;">
				<legend><@i18n 'att_realTime_realTimeSet'/></legend>
				<table class="tableStyle"  style="width:500px">
					<tr>
						<td>
							<@ZKUI.Input id="attRealTimeRollCall" hideLabel="true" type="checkbox" name="att.realTime.rollCall" value="${attParams['att.realTime.rollCall']!}" trueValue="0" falseValue="1" eventCheck=true/>
							<span><@i18n "att_realTime_openRealTime"/></span>
						</td>
					</tr>
				</table>
			</fieldset>
			</@ZKUI.Permission>

			<!-- 员工自助入口设置 -->
			<#if showEmployeeSet?? && showEmployeeSet=="true">
			<@ZKUI.Permission name="att:rule:employeeSet">
			<fieldset style="margin-bottom: 15px;">
				<legend><@i18n 'att_self_persSelfLogin'/></legend>
				<table class="tableStyle" id="" style="width:500px">
					<tr>
						<td class="formTitle"><@i18n 'att_self_isOpenSelfLogin'/></td>
					</tr>
					<tr>
						<td>
							<label for="att_self_loginEntrance0">
								<@ZKUI.Input hideLabel="true" id="att_self_loginEntrance0" name="att.self.loginEntrance" type="radio" value="0" />
								<@i18n 'common_enable'/>
							</label>
							<label style="margin-left: 20px;" for="att_self_loginEntrance1">
								<@ZKUI.Input hideLabel="true" id="att_self_loginEntrance1" name="att.self.loginEntrance" type="radio" value="1" />
								<@i18n 'common_disable'/>
							</label>
						</td>
					</tr>
				</table>
			</fieldset>
			</@ZKUI.Permission>
			</#if>
		</form>
	</div>
	<div class="sidenavContext"></div>
</div>
<script type="text/javascript">

getInfo();
function getInfo() {
	let f1 ="${attParams['att.cardStatus.f1']!}"
	document.getElementById("valueOne").value=getCardValue(f1,1);
	document.getElementById("desOne").value=getCardValue(f1,2);
	let f2 ="${attParams['att.cardStatus.f2']!}"
	document.getElementById("valueTwo").value=getCardValue(f2,1);
	document.getElementById("desTwo").value=getCardValue(f2,2);
	let f3 ="${attParams['att.cardStatus.f3']!}"
	document.getElementById("valueThree").value=getCardValue(f3,1);
	document.getElementById("desThree").value=getCardValue(f3,2);
	let f4 ="${attParams['att.cardStatus.f4']!}"
	document.getElementById("valueFour").value=getCardValue(f4,1);
	document.getElementById("desFour").value=getCardValue(f4,2);
	let f5 ="${attParams['att.cardStatus.f5']!}"
	document.getElementById("valueFive").value=getCardValue(f5,1);
	document.getElementById("desFive").value=getCardValue(f5,2);
	let f6 ="${attParams['att.cardStatus.f6']!}"
	document.getElementById("valueSix").value=getCardValue(f6,1);
	document.getElementById("desSix").value=getCardValue(f6,2);
}

//获取打卡状态的值
function getCardValue(value,second){
	let result;
	let arr = value.split(",");
	if (arr.length===3){
		for (let i = 0; i < arr.length; i++) {
			if (second===i){
				result = arr[i];
			}
		}
	}
	return result;
}

updateValueOne()
function updateValueOne() {
	var value = document.getElementById("valueOne").value;
	var des = document.getElementById("desOne").value;
	document.getElementsByName("att.cardStatus.f1")[0].value ="1" + "," +value + "," + des;
}
updateValueTwo()
function updateValueTwo() {
	var value = document.getElementById("valueTwo").value;
	var des = document.getElementById("desTwo").value;
	document.getElementsByName("att.cardStatus.f2")[0].value ="2" + "," +value + "," + des;
}
updateValueThree()
function updateValueThree() {
	var value = document.getElementById("valueThree").value;
	var des = document.getElementById("desThree").value;
	document.getElementsByName("att.cardStatus.f3")[0].value ="3" + "," +value + "," + des;
}
updateValueFour()
function updateValueFour() {
	var value = document.getElementById("valueFour").value;
	var des = document.getElementById("desFour").value;
	document.getElementsByName("att.cardStatus.f4")[0].value ="4" + "," +value + "," + des;
}
updateValueFive()
function updateValueFive() {
	var value = document.getElementById("valueFive").value;
	var des = document.getElementById("desFive").value;
	document.getElementsByName("att.cardStatus.f5")[0].value ="5" + "," +value + "," + des;
}
updateValueSix()
function updateValueSix() {
	var value = document.getElementById("valueSix").value;
	var des = document.getElementById("desSix").value;
	document.getElementsByName("att.cardStatus.f6")[0].value ="6" + "," +value + "," + des;
}

// 新增年假时长规则
function addAnnualLeaveRuleTr() {

	var allTr=$("#attAnnualLeaveRule tr");
	if(allTr.size() > 49) {
		messageBox({messageType: "alert",text: '<@i18n "att_annualLeave_ruleCountOver"/>'});
		return;
	}

	var lastAddTrId = $("#attAnnualLeaveRule").find("tr").eq(-2).attr("id");
	var lastAddTr=$("#"+lastAddTrId);
	var lastAddTrIdNumber = lastAddTrId.replace("annualLeaveRuleTr", "");
	var newAddTrIdNumber = parseInt(lastAddTrIdNumber) + 1;
	var newAddTrId = "annualLeaveRuleTr" + newAddTrIdNumber;
	var newAddTrHTML = '<tr id="'+newAddTrId+'">'+
		'<td><input onkeyup="this.value=this.value.replace(\/\\D\/g,\'\')" name="attAnnualLeaveRule['+newAddTrIdNumber+'].startYear" value="" maxlength="3" type="text" class="attRule-annualLeave-input"/><@i18n "base_cron_year"/> ≤</td>'+
		'<td><@i18n "att_annualLeave_years"/> <</td>'+
		'<td><input onkeyup="this.value=this.value.replace(\/\\D\/g,\'\')" name="attAnnualLeaveRule['+newAddTrIdNumber+'].endYear" value="" maxlength="3" type="text" class="attRule-annualLeave-input"/></td>'+
		'<td><@i18n "base_cron_year"/>，</td>'+
		'<td><@i18n "att_annualLeave_have"/> <input onkeyup="this.value=this.value.replace(\/\\D\/g,\'\')" name="attAnnualLeaveRule['+newAddTrIdNumber+'].days" value="" maxlength="3" type="text" class="attRule-annualLeave-input"/><@i18n "att_annualLeave_days"/></td>'+
		'<td>'+
			'<div onclick="addAnnualLeaveRuleTr()" class="attRule-annualLeave-img-add att_icv att_icv-green icv-ic_add zk-colors-fg-green"/>'+
			'<div onclick="delAnnualLeaveRuleTr(this)" class="att_icv warningColor icv-ic_del"/>'+
		'</td>'+
	'</tr>';
	lastAddTr.after(newAddTrHTML);
}

// 删除年假时长规则
function delAnnualLeaveRuleTr(_this) {
	$(_this).parent().parent().remove();
}

//迟到且早退算旷工时未签到/退默认为缺勤，不可编辑。
ZKUI.Combo.get("att_baseRuleLateAndEarly").combo.attachEvent("onChange",function(value,text){
    if(value == "true"){
        ZKUI.Combo.get("att_baseRuleNoSignInCountType").combo.setComboValue("absent");
        ZKUI.Combo.get("att_baseRuleNoSignInCountType").combo.disable(true);
        ZKUI.Combo.get("att_baseRuleNoSignOffCountType").combo.setComboValue("absent");
        ZKUI.Combo.get("att_baseRuleNoSignOffCountType").combo.disable(true);
    }else{
        ZKUI.Combo.get("att_baseRuleNoSignInCountType").combo.disable(false);
        ZKUI.Combo.get("att_baseRuleNoSignOffCountType").combo.disable(false);
	}
});


function checkValue(el)
{
	if($(el).val() == "")
	{
		$("input[name='" + $(el).attr("name") + "']").val(0);
	}
}

/*未签到*/
ZKUI.Combo.get("att_baseRuleNoSignInCountType").combo.attachEvent("onChange",function(value,text){
	baseRuleNoSignInCountTypeEvent(value);
});
function baseRuleNoSignInCountTypeEvent(value){
	if ("late" == value) {
        var lateMinute = $("#att_baseRuleNoSignInCountLateMinute").val();
	    if (!lateMinute || "0" == lateMinute) {
	        // 如果没填或为0 则默认30,否则就是原来的值
            $("#att_baseRuleNoSignInCountLateMinute").val("30");
		}
		$("#att_baseRuleNoSignInCountLateMinute").attr("readonly", false);
	} else {
		$("#att_baseRuleNoSignInCountLateMinute").val("0");
		$("#att_baseRuleNoSignInCountLateMinute").attr("readonly", true);
	}
}

/*未签退*/
ZKUI.Combo.get("att_baseRuleNoSignOffCountType").combo.attachEvent("onChange",function(value,text){
	baseRuleNoSignOffCountType(value);
});
function baseRuleNoSignOffCountType(value){
	if ("early" == value) {
        var earlyMinute = $("#att_baseRuleNoSignOffCountEarlyMinute").val();
        if (!earlyMinute || "0" == earlyMinute) {
            $("#att_baseRuleNoSignOffCountEarlyMinute").val("30");
        }
		$("#att_baseRuleNoSignOffCountEarlyMinute").attr("readonly", false);
	} else {
		$("#att_baseRuleNoSignOffCountEarlyMinute").val("0");
		$("#att_baseRuleNoSignOffCountEarlyMinute").attr("readonly", true);
	}
}
baseRuleNoSignInCountTypeEvent("${attParams['att.baseRule.noSignInCountType']!}");
baseRuleNoSignOffCountType("${attParams['att.baseRule.noSignOffCountType']!}");

function buildAttAnnualLeaveCalculateDay() {

 	var calculateMonth = ZKUI.Combo.get("attAnnualLeaveCalculateMonth").getValue();
	var day = 30
	if (calculateMonth == 1 || calculateMonth == 3 || calculateMonth == 5 || calculateMonth == 7 || calculateMonth == 8 || calculateMonth == 10 || calculateMonth == 12) {
		day = 31
	} else if (calculateMonth == 2) {
		day = 28
	}

	var options = [];
	for (var i = 1; i <=day; i ++ ) {
		var option = {
			text: i,
			value: i
		}
		options.push(option);
	}
	ZKUI.Combo.get('attAnnualLeaveCalculateDay').combo.clearAll();
	ZKUI.Combo.get('attAnnualLeaveCalculateDay').addOption(options);

	var attAnnualLeaveCalculateDay = ZKUI.Combo.get("attAnnualLeaveCalculateDay");
	var calculateDay = attAnnualLeaveCalculateDay.getValue();
	if (calculateDay) {
		if (calculateDay > day) {
			attAnnualLeaveCalculateDay.setValue(day);
		}
	} else {
		attAnnualLeaveCalculateDay.setValue(1);
	}
}

$(function () {

	function attInitOvertimeSettingEnable(enable){
		if (enable == 0) {
			$(".attOvertimeSettingTr").show();
		} else {
			 $(".attOvertimeSettingTr").hide();
		}
	}

	// 初始化加班等级设置参数，因为数据库保存数据结构为xx-xx，所以前端需要处理
	attInitOvertimeSetting();
	function attInitOvertimeSetting() {

		// 回填是否启动加班等级
		attInitOvertimeSettingEnable("${attParams['att.overtimeLevel.enable']!'0'}");
		$("input[name='att.overtimeLevel.enable'][value='${attParams['att.overtimeLevel.enable']!}']").prop("checked", true);
		$("input[name='att.overtimeLevel.enable']").on("change", function() {
			var value = $(this).val();
			attInitOvertimeSettingEnable(value);
		});

		$("#attNormalOT1Start").val(attSplitValue("${attParams['att.overtimeLevel.normalOT1']!}", 0));
		$("#attNormalOT1End").val(attSplitValue("${attParams['att.overtimeLevel.normalOT1']!}", 1));
		$("#attNormalOT2Start").val(attSplitValue("${attParams['att.overtimeLevel.normalOT2']!}", 0));
		$("#attNormalOT2End").val(attSplitValue("${attParams['att.overtimeLevel.normalOT2']!}", 1));
		$("#attNormalOT3Start").val(attSplitValue("${attParams['att.overtimeLevel.normalOT3']!}", 0));
		$("#attNormalOT3End").val(attSplitValue("${attParams['att.overtimeLevel.normalOT3']!}", 1));

		$("#attRestOT1Start").val(attSplitValue("${attParams['att.overtimeLevel.restOT1']!}", 0));
		$("#attRestOT1End").val(attSplitValue("${attParams['att.overtimeLevel.restOT1']!}", 1));
		$("#attRestOT2Start").val(attSplitValue("${attParams['att.overtimeLevel.restOT2']!}", 0));
		$("#attRestOT2End").val(attSplitValue("${attParams['att.overtimeLevel.restOT2']!}", 1));
		$("#attRestOT3Start").val(attSplitValue("${attParams['att.overtimeLevel.restOT3']!}", 0));
		$("#attRestOT3End").val(attSplitValue("${attParams['att.overtimeLevel.restOT3']!}", 1));

		$("#attHolidayOT1Start").val(attSplitValue("${attParams['att.overtimeLevel.holidayOT1']!}", 0));
		$("#attHolidayOT1End").val(attSplitValue("${attParams['att.overtimeLevel.holidayOT1']!}", 1));
		$("#attHolidayOT2Start").val(attSplitValue("${attParams['att.overtimeLevel.holidayOT2']!}", 0));
		$("#attHolidayOT2End").val(attSplitValue("${attParams['att.overtimeLevel.holidayOT2']!}", 1));
		$("#attHolidayOT3Start").val(attSplitValue("${attParams['att.overtimeLevel.holidayOT3']!}", 0));
		$("#attHolidayOT3End").val(attSplitValue("${attParams['att.overtimeLevel.holidayOT3']!}", 1));
	}

	function attSetOvertimeSettingValue() {
		$("input[name='att.overtimeLevel.normalOT1']").val($("#attNormalOT1Start").val()+"-"+$("#attNormalOT1End").val());
		$("input[name='att.overtimeLevel.normalOT2']").val($("#attNormalOT2Start").val()+"-"+$("#attNormalOT2End").val());
		$("input[name='att.overtimeLevel.normalOT3']").val($("#attNormalOT3Start").val()+"-"+$("#attNormalOT3End").val());

		$("input[name='att.overtimeLevel.restOT1']").val($("#attRestOT1Start").val()+"-"+$("#attRestOT1End").val());
		$("input[name='att.overtimeLevel.restOT2']").val($("#attRestOT2Start").val()+"-"+$("#attRestOT2End").val());
		$("input[name='att.overtimeLevel.restOT3']").val($("#attRestOT3Start").val()+"-"+$("#attRestOT3End").val());

		$("input[name='att.overtimeLevel.holidayOT1']").val($("#attHolidayOT1Start").val()+"-"+$("#attHolidayOT1End").val());
		$("input[name='att.overtimeLevel.holidayOT2']").val($("#attHolidayOT2Start").val()+"-"+$("#attHolidayOT2End").val());
		$("input[name='att.overtimeLevel.holidayOT3']").val($("#attHolidayOT3Start").val()+"-"+$("#attHolidayOT3End").val());
	}

	function attSplitValue(value, index) {
		let arr = value.split("-");
		if (arr.length===2){
			for (let i = 0; i < arr.length; i++) {
				if (index===i){
				 	return arr[i];
				}
			}
		}
		return "";
	}


	// 回填按上班比例时长计算类型
	$("input[name='att.annualLeave.calculateType'][value='${attParams['att.annualLeave.calculateType']!}']").prop("checked", true);
	// 回填是否启动员工自助登录入口
	$("input[name='att.self.loginEntrance'][value='${attParams['att.self.loginEntrance']!}']").prop("checked", true);

	var lateAndEarly = ZKUI.Combo.get("att_baseRuleLateAndEarly").combo.getSelected();
	if(lateAndEarly == 'true'){
		ZKUI.Combo.get("att_baseRuleNoSignInCountType").combo.disable(true);
		ZKUI.Combo.get("att_baseRuleNoSignOffCountType").combo.disable(true);
	}

	buildAttAnnualLeaveCalculateDay();
	ZKUI.Combo.get('attAnnualLeaveCalculateMonth').combo.attachEvent("onChange", function(){
        buildAttAnnualLeaveCalculateDay();
    });


	function attAnnualLeaveCalculateTypeChange() {
		var parkRecordPrecisionCombo = ZKUI.Combo.get("parkRecordPrecisionCombo");
		if ($("#attAnnualLeaveCalculateType").prop("checked")) {
			$(".attAnnualLeaveCalculateTypeClass").css("color","");
		} else {
			$(".attAnnualLeaveCalculateTypeClass").css("color","#999");
		}
	}
	attAnnualLeaveCalculateTypeChange();
    $("#attAnnualLeaveCalculateType").change(function(){
		attAnnualLeaveCalculateTypeChange();
	})


	/**表单验证*/
	$("#${formId}").validate({
		debug: true,
		rules: {
			"att.cardStatus.f1.showName": {
				required: true,
				unInputChar: true
			},
			"att.cardStatus.f2.showName": {
				required: true,
				unInputChar: true
			},
			"att.cardStatus.f3.showName": {
				required: true,
				unInputChar: true
			},
			"att.cardStatus.f4.showName": {
				required: true,
				unInputChar: true
			},
			"att.cardStatus.f5.showName": {
				required: true,
				unInputChar: true
			},
			"att.cardStatus.f6.showName": {
				required: true,
				unInputChar: true
			},
			"att.baseRule.shortestMinutes": {
				required: true,
				min: 10
			},
			"att.baseRule.longestMinutes": {
				required: true,
				min: 10,
				max: 1440
			},
			<#list attLeaveTypeItemList as item>
				<#if item.convertCount != '-1'>
					'attLeaveTypeItemList[${item_index}].convertCount': {
						required: true,
						convertCountValid: true,
						min: 0.1,
					},
				</#if>
				'attLeaveTypeItemList[${item_index}].symbol': {
					required: true,
					unInputChar: true
				},
			</#list>
		},
		submitHandler: function() {

			var baseRuleShortestMinutes = $("#att_baseRuleShortestMinutes").val();
			var baseRuleLongestMinutes = $("#att_baseRuleLongestMinutes").val();
			if (parseInt(baseRuleShortestMinutes) > parseInt(baseRuleLongestMinutes)) {
				//最短的考勤时间段不能大于最长的考勤时间段
				messageBox({messageType: "alert", text: "<@i18n 'att_rule_shortLessLong'/>"});
				return false;
			}

			var noSignInCountType = ZKUI.Combo.get("att_baseRuleNoSignInCountType").combo.getSelected();
			var noSignOffCountType = ZKUI.Combo.get("att_baseRuleNoSignOffCountType").combo.getSelected();
			var lateMinute = $("#att_baseRuleNoSignInCountLateMinute").val();
			var earlyMinute = $("#att_baseRuleNoSignOffCountEarlyMinute").val();
			if ("late" == noSignInCountType && (Number(lateMinute) <= 0 || Number(lateMinute) > Number(baseRuleLongestMinutes))) {
				// 未签到记为迟到分钟数应大于0且小于最长的考勤时段时长
				messageBox({messageType: "alert", text: "<@i18n 'att_rule_lateMinuteWarning'/>"});
				return false;
			}
			if ("early" == noSignOffCountType && (Number(earlyMinute) <= 0 || Number(earlyMinute) > Number(baseRuleLongestMinutes))) {
				// 未签退记为早退分钟数应大于0且小于最长的考勤时段时长
				messageBox({messageType: "alert", text: "<@i18n 'att_rule_earlyMinuteWarning'/>"});
				return false;
			}

			// 提交前校验年假规则设置必须为连续年份
			var trsLen = $("#attAnnualLeaveRule").find("tr").length;
			for(var trIdx = 0; trIdx < trsLen-1; trIdx++) {
				var endYear1 = $("input[name='attAnnualLeaveRule[" + trIdx + "].endYear']").val();
				var startYear2 = $("input[name='attAnnualLeaveRule[" + (trIdx + 1) + "].startYear']").val();
				// 最后一行下标被固定设置为999
				if(trIdx == (trsLen - 2)) {
					var startYear2 = $("input[name='attAnnualLeaveRule[999].startYear']").val();
				}
				if(endYear1 != startYear2) {
					openMessage(msgType.error, "<@i18n 'att_annualLeave_consecutive'/>");
					return;
				}
			}

			// 设置加班等级参数配置
			attSetOvertimeSettingValue();

			onLoading(function(){
				$('#${formId}').ajaxSubmit({
					async : true,
					dataType : 'json',
					success: function(result)
					{
						closeMessage();
						if (sysCfg.warning == result.ret) {
							openMessage(msgType.warning, result.msg);
							return;
						}

						if (sysCfg.error == result.ret) {
							openMessage(msgType.error, result.msg);
							return;
						}

						openMessage(msgType.success);

						//启用禁用需要刷新界面
						var isRefresh = false;
						var oldAttRealTimeRollCall = "${(attParams['att.realTime.rollCall'])!}";
						var attRealTimeRollCall = $("#attRealTimeRollCall").prop("checked");
						if ((oldAttRealTimeRollCall == 0 && !attRealTimeRollCall) || (oldAttRealTimeRollCall == 1 && attRealTimeRollCall)) {
							isRefresh = true;
						}

						if (isRefresh) {
							setTimeout(function () {
								location.reload();
							}, 1300);
						} else {
							// 更新参数设置
							loadAttParams();
						}
					}
				});
			});
		}
	});
});
</script>
</#macro>
