<#assign gridName="attDeviceOpLogGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">
  <@ZKUI.Searchbar>
   <@ZKUI.SearchTop>
	<tr>
		<td valign="middle">
			<@ZKUI.Input type="datetime" id="beginTime" endId="endTime" name="beginDate" title="common_time_from" todayRange="start" max="today" today="-3" noOverToday="true" readonly="false"/>
			&nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
			<@ZKUI.Input type="datetime" id="endTime" name="endDate" title="common_to" max="today" todayRange="end" today="true" noOverToday="true" hideLabel="true" readonly="false"/>
		</td>
		<td valign="middle">
            <@ZKUI.Input name="devSn" maxlength="50" title="att_device_op_log_dev_sn" type="text"/>
        </td>
	</tr>
   </@ZKUI.SearchTop>
  </@ZKUI.Searchbar>
<@ZKUI.Toolbar>
    <@ZKUI.ToolItem type="refresh" permission="att:deviceoplog:refresh" ></@ZKUI.ToolItem>
    <@ZKUI.ToolItem id="attDeviceOpLog.do?export" type="export" permission="att:deviceoplog:export"></@ZKUI.ToolItem>
</@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttDeviceOpLogItem" query="attDeviceOpLog.do?list"/>
</@ZKUI.GridBox>