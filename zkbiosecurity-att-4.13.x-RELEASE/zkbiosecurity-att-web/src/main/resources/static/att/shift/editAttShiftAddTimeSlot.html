<#include '/public/template/editTemplate.html'>
<style>
    .attTimeSlotTableDiv {
        width: 100%;
        height: 100%;
        border-collapse: collapse;
    }

    .attTimeSlotTableClass {
        width: 100%;
        background-color: #fff;
        position:relative;

    }

    .attTimeSlotTableClass .att-bg-thead-color {
        background-color: #eff1f2;
        text-align: left;
        padding-left: 10px;
        height: 40px;
        line-height: 40px;
        min-width: 140px;
        max-width: 250px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .attTimeSlotTableClass td {
        text-align: left;
        padding-left: 10px;
        height: 38px;
        line-height: 38px;
    }

    .attTimeSlotTableClass .att-hover-color:hover {
        background: #ededed;
    }


    .attTimeSlotCheckClass {
        cursor: pointer;
        zoom: 1.1;
    }

</style>
<script type='text/javascript'>
    // 所有时间段集合
    var attGlobalTimeSlotMap = {};

    // 展示时间段名称
    function showEditTimeSlot(name, el) {
        return name;
    }

    // 时间段修改
    function attTimeSlotComboGridCheck(timeSlotId, cInd, state) {
        if (state) {
            attAddTimeSlot(timeSlotId);
        } else {
            attDelTimeSlot(timeSlotId);
        }
    }

    // Grid清空
    function attTimeSlotComboGridClearAll() {
        $("#attTimeSlotTableId tr :not(:first-child)").remove();
    }

    // Grid全选/取消全选
    function attTimeSlotComboGridCheckAll(e) {
        var timeSlotIds = ZKUI.ComboGrid.PULL.attTimeSlotComboGridId.getValue();
        if (timeSlotIds) {
            attTimeSlotComboGridClearAll();
            $.each(timeSlotIds.split(","),function(i,timeSlotId) {
                attAddTimeSlot(timeSlotId);
            })
        } else {
            attTimeSlotComboGridClearAll();
        }
        return true;
    }

    // 隐藏comboGrid全选按钮（comboGrid目前不支持全选回调）
    function attTimeSlotComboGridOpen() {
        $("#attTimeSlotComboGridIdGridBoxId .combo_grid_cont .gridbox table .hdrcell img").hide()
    }

</script>
<#macro editContent>
    <@ZKUI.Layout pattern="2E">
        <@ZKUI.Cell height="80" fixSize="1" hideArrow="true" title="att_leftMenu_timeSlot">
            <div style="padding: 0px 10px;">
                <@ZKUI.ComboGrid hideLabel="true" width="750" title="att_leftMenu_timeSlot" id="attTimeSlotComboGridId"
                queryField="periodName" name="timeSlotIds" value="${(item.timeSlotIds)!}"
                grid_showColumns="${attTimeSlotColumns}"
                grid_vo="com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem"
                grid_query="attTimeSlot.do?list&periodType=${(item.shiftType)!}"
                grid_onCheck="attTimeSlotComboGridCheck"
                grid_onClearAll="attTimeSlotComboGridClearAll"
                grid_onCheckA="attTimeSlotComboGridCheckAll"
                onOpen="attTimeSlotComboGridOpen"/>
            </div>
        </@ZKUI.Cell>
        <@ZKUI.Cell fixSize="1" hideArrow="true" title="att_shift_timeSlotDetail">
            <div class="attTimeSlotTableDiv">
                <table id="attTimeSlotTableId" class="attTimeSlotTableClass zk-content-bg-color">
                    <thead></thead>
                    <tbody></tbody>
                </table>
            </div>
        </@ZKUI.Cell>
    </@ZKUI.Layout>
</#macro>

<#macro buttonContent>
    <button class='button-form' id="${uuid}ok"><@i18n 'common_edit_ok'/></button>
    <button class='button-form button-close' onclick="DhxCommon.closeWindow()"><@i18n 'common_close'/></button>
</#macro>

<script type='text/javascript'>

    // 根据单位初始化周期
    initAttPeriodNumber();
    function initAttPeriodNumber() {
        // 单位
        var periodicUnit = "${(item.periodicUnit)!}";
        // 周期
        var periodNumber = "${(item.periodNumber)!}";
        // 表头
        var thead = "<tr><th class='att-bg-thead-color zk-bg-color'><@i18n 'att_shift_periodNumber'/></th></tr>";
        $("#attTimeSlotTableId>thead").append(thead);

        if (periodicUnit == 0) { // 按天
            // 周期起始类型
            var periodStartMode = "${(item.periodStartMode)!}";
            // 周期起始日期
            var startDate = new Date("${((item.startDate)?string('yyyy-MM-dd'))!}");
            var tds = ""
            for (var i = 0; i < parseInt(periodNumber); i++) {
                tds+="<tr class='att-hover-color zk-hover-color'><td><@i18n 'att_shift_NO'/>" + (i + 1) + "<@i18n 'common_days'/>";
                if (periodStartMode == 0) {
                    tds+= "<span class='attTimeSlotTipClass'> ( "+startDate.format("yyyy-MM-dd")+" ) </span>"
                }
                tds+="</td></tr>";
                startDate.setDate(startDate.getDate()+1);
            }
            $("#attTimeSlotTableId>tbody").append(tds);
        } else if (periodicUnit == 1) { // 按周
            var weekArray = new Array("<@i18n 'common_monday'/>", "<@i18n 'common_tuesday'/>","<@i18n 'common_wednesday'/>",
        "<@i18n 'common_thursday'/>", "<@i18n 'common_friday'/>", "<@i18n 'common_saturday'/>", "<@i18n 'common_sunday'/>");
            var tds = ""
            for (var i = 0; i < parseInt(periodNumber); i++) {
                for (var j = 0; j < 7; j++) {
                    tds+= "<tr class='att-hover-color zk-hover-color'><td>"+weekArray[j]+"</td></tr>"
                }
            }
            $("#attTimeSlotTableId>tbody").append(tds);
        } else if (periodicUnit == 2) { // 按月
            var tds = ""
            for (var i = 0; i < parseInt(periodNumber); i++) {
                for (var j = 0; j < 31; j++) {
                     tds+="<tr><td><@i18n 'att_shift_NO'/>" + (j + 1) + "<@i18n 'common_days'/>";
                }
	        }
	        $("#attTimeSlotTableId>tbody").append(tds);
        }
    }

    // 初始化时间段
    initAttTimeSlotMap()
    function initAttTimeSlotMap() {
        attGlobalTimeSlotMap = {}
        $.ajax({
            type: "post",
            url: "attTimeSlot.do?getAllList",
            success: function (ret) {
                if (ret.data) {
                    $.each(ret.data, function (i, value) {
                        attGlobalTimeSlotMap[value.id] = value;
                    });
                    // 获取时间段后，如果已经选择了时间段需要初始化
                    var timeSlotIds = "${(item.timeSlotIds)!}";
                    if (timeSlotIds) {
                         // 勾选的周期
                        var timeSlotDetailIds = "${(item.timeSlotDetailIds)!}";
                        var timeSlotIdsArray = timeSlotIds.split(",");
                        for (var index = 0; index < timeSlotIdsArray.length; index ++ ) {
                            var timeSlotId = timeSlotIdsArray[index];
                            attAddTimeSlot(timeSlotId, timeSlotDetailIds);
                        }
                    }
                } else {
                    openMessage(msgType.info, I18n.getValue("att_timeSlot_addFirst"));
                }
            }
        })
    }

    // 全选/取消全选
    function attTimeSlotCheckAll(el) {
        var timeSlotId = $(el).val();
        var isCheck = $(el).prop("checked");
        // 没有全选，是否存在与其他时间段冲突
        var noCheckAll = false;
        $("#attTimeSlotTableId tbody tr ." + timeSlotId + " input").each(function(index, elEx) {
            // 与其他时间段冲突
            if (isCheck && judgeConflict(elEx)) {
                noCheckAll = true;
                return true
            }
            $(elEx).prop("checked", isCheck);
        });

        if (isCheck && noCheckAll) {
            // openMessage(msgType.info, "<@i18n 'att_timeSlot_conflict'/>")
            $(el).prop("checked", false);
        }
    }

    // 单选/取消单选
    function attTimeSlotCheck(el) {
        var isAllCheck = true;
        // 勾选了才需要判断冲突
        if (isAllCheck && judgeConflict(el)) {
            $(el).prop("checked", false)
            openMessage(msgType.info, "<@i18n 'att_timeSlot_conflict'/>")
            return false;
        }
        var timeSlotId = $(el).val();
        let timeSlotIdsArray = $("#attTimeSlotTableId tbody tr ." + timeSlotId);
        for (var index = 0; index < timeSlotIdsArray.length; index ++ ) {
            if (!$("#checkTd_" + timeSlotId +"_"+ index).prop("checked")) {
                isAllCheck = false;
                break;
            }
        }
        $("#checkThead_" + timeSlotId).prop("checked", isAllCheck);
    }

    // 判断时间是否冲突
    function judgeConflict(judgeEl) {
         var allCheckTimeSlotArray = [];

         // 当前选中的checkboxId
         var currentId = $(judgeEl)[0].id;
         console.log("currentId = ", currentId)

         // 当前选中的text，即时间段
         var currentTimeSlot = ZKUI.Input.get(currentId).options.text;
         console.log("currentTimeSlot = ", currentTimeSlot)

         // 当前选中所在的Tr标签
         var judgeTimeSlotTr = $(judgeEl).parent().parent().parent();
         // 遍历当前选中所在的Tr标签下的所有td
         judgeTimeSlotTr.children("td").each(function(index, el) {
            if (index > 0 ) {

                 // 该行的其他checkboxId
                 var otherId = $(el).children('span').children('input')[0].id;
                 console.log("otherId = ", otherId)

                 // 该行的其他text，即时间段
                 var otherTimeSlot = ZKUI.Input.get(otherId).options.text;
                 console.log("otherTimeSlot = ", otherTimeSlot)

                 // 过滤当前选中
                 if ($("#"+otherId).prop("checked") && currentId != otherId) {
                    allCheckTimeSlotArray.push(otherTimeSlot)
                 }
            }
        });

        if (allCheckTimeSlotArray && allCheckTimeSlotArray.length > 0) {
            var isConflict = false;
            $.each(allCheckTimeSlotArray, function(index ,checkTimeSlot) {
                if (attTimeSlotCross(currentTimeSlot.split(" - ")[0], currentTimeSlot.split(" - ")[1], checkTimeSlot.split(" - ")[0], checkTimeSlot.split(" - ")[1])) {
                    isConflict = true;
                    return;
                }
            });
            return isConflict;
        } else {
            return false;
        }
    }

    /*判断两个时间段是否有交集*/
    function attTimeSlotCross(start1, end1, start2, end2) {
        if (start1 >= end1) {
            end1 = "3" + end1;
        }

        if (start2 >= end2) {
            end2 = "3" + end2;
        }
        return start1 <= end2 && end1 >= start2;
    }

    // 新增/初始化时间段
    function attAddTimeSlot(timeSlotId, timeSlotDetailIds) {
        // 时间段详情
        var attTimeSlot = attGlobalTimeSlotMap[timeSlotId];
        if (!attTimeSlot) {
            return;
        }

        //  行选择框
        var shiftType = "${(item.shiftType)!}";
        var text = attTimeSlot.toWorkTime + " - " + attTimeSlot.offWorkTime;
        if (shiftType == 1) {
            text = attTimeSlot.startSignInTime + " - " + attTimeSlot.endSignOffTime
        }
        let trs = $("#attTimeSlotTableId>tbody>tr");
        var isAllCheck = true;
        for (var index = 0; index < trs.length; index ++) {
            let el = trs[index];
            var checkTdId = "spanCheckTd_" + timeSlotId +"_"+ index;
            var workTime = "<td class='" + timeSlotId + "'>" + "<span id='"+checkTdId+"'></span>" + "<span class='attTimeSlotTipClass'>" + text + "</span></td>";

            // 初始化就判断是否勾选、是否全选，否则组件初始化后会存在延迟，导致有些时段未勾选
            var isChecked = false;
            if (timeSlotDetailIds) {
                let timeSlotDetailIdArray = timeSlotDetailIds.split(",");
                for (var i = 0; i < timeSlotDetailIdArray.length; i ++ ) {
                    let id_index = timeSlotDetailIdArray[i];
                    if (id_index.includes(timeSlotId)) {
                        if (id_index == timeSlotId + "_" + index) {
                            isChecked = true;
                            break;
                        }
                    }
                }
            }

            // 只要一个不是勾选的，就不是全选
            if (!isChecked) {
                isAllCheck = false;
            }

            loadUIToDiv("input", "#"+ checkTdId, {
                useInputReq: true,
                type: "checkbox",
                id: "checkTd_" + timeSlotId +"_"+ index,
                onchange: "attTimeSlotCheck(this)",
                timeSlotId: timeSlotId,
                class: "attTimeSlotCheckClass",
                value: timeSlotId,
                text: text,
                checked: isChecked,
            })
            $(el).append(workTime);
        }


        // 表头/列选择框
        var checkTheadId = "spanCheckThead_"+timeSlotId;
        var timeSlotThead = "<th class=' att-bg-thead-color zk-bg-color " + timeSlotId + "' title='" + attTimeSlot.periodName + "'>" + "<span id='"+checkTheadId+"'></span>" + attTimeSlot.periodName + "</th>";
        loadUIToDiv("input", "#"+ checkTheadId, {
            useInputReq: true,
            type: "checkbox",
            id: "checkThead_" + timeSlotId,
            onchange: "attTimeSlotCheckAll(this)",
            timeSlotId: timeSlotId,
            class: "attTimeSlotCheckClass",
            value: timeSlotId,
            checked: isAllCheck,
        })
        $("#attTimeSlotTableId>thead>tr").append(timeSlotThead);
    }

    // 删除时间段（根据class来删除）
    function attDelTimeSlot(timeSlotId) {
         $("#attTimeSlotTableId tr ." + timeSlotId).remove();
    }

    /*提交*/
    $("#${uuid}ok").click(function() {
        var timeSlotIds = ZKUI.ComboGrid.PULL.attTimeSlotComboGridId.getValue();
        if (!timeSlotIds) {
            //请选择时间段
            messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text : "<@i18n 'att_timeSlot_selectFirst'/>"});
            return false;
        }

        var timeSlotDetailIds = "";
        if (timeSlotIds) {
            $.each(timeSlotIds.split(","), function(index ,timeSlotId) {
                $("#attTimeSlotTableId tbody tr ." + timeSlotId).each(function(index, el) {
                     if ($("#checkTd_" + timeSlotId +"_"+ index).prop("checked")) {
                        timeSlotDetailIds += timeSlotId + "_" + index + ",";
                     }
                });
            });
        }
        if (!timeSlotDetailIds) {
            //请选择时间段明细
            messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text : "<@i18n 'att_shift_pleaseAllDetailTimeSlot'/>"});
            return false;
        }

        // 去掉最后一个逗号
        timeSlotDetailIds = timeSlotDetailIds.substring(0, timeSlotDetailIds.length - 1);

        onLoading(function() {
            $.ajax({
                type: "post",
                url: "attShift.do?saveTimeSlot",
                data:{
                    id: "${(item.id)!}",
                    timeSlotIds: timeSlotIds,
                    timeSlotDetailIds: timeSlotDetailIds
                },
                dataType: "json",
                success: function (result) {
                    closeMessage();
                    if ("ok" == result.ret){
                        openMessage(msgType.success)
                        DhxCommon.closeWindow();
                        reloadAttShiftGrid();
                    } else {
                        openMessage(msgType.info, result.msg)
                    }
                }
            });
        });
    })

    $(function() {
         // 设置placeholder（comboGrid目前不支持）
        $("#attTimeSlotComboGridId .dhxcombo_dhx_web .dhxcombo_input").attr("placeholder", "<@i18n 'att_timeSlot_selectFirst'/>");
    })

</script>
