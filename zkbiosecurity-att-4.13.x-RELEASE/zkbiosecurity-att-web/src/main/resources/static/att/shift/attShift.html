<#assign gridName="attShiftGrid${uuid!}">
<style>
	.att-timeslot-td {
		padding: 8px 0px 8px 5px;
	}

	.att-timeslot-span {
		line-height: 20px;
	}

</style>
<script>

	function showEditAttShift(name, el)
	{
		return "<a href=\"javascript:void(0)\" onclick=\"attEditShift('${gridName}',this,'"+ arguments[4]+"')\" title=\"" + name + "\">" + name + "</a>";
	}

	function attEditShift(gridName, obj, rid) {
		var grid = ZKUI.Grid.GRID_PULL["${gridName}"].grid;
		var row = grid.getRowData(rid);
		var opts = {
			path: "attShift.do?edit&id=" + rid,
			width: 860,
			height: 440,
			title: "<@i18n 'common_op_edit'/>",
			editPage:true,
			gridName: gridName
		};
		if (row.shiftType == "<@i18n 'att_shift_flexibleShift'/>") {
			opts.height = 350;
		}
		DhxCommon.createWindow(opts);
	};

</script>
<@ZKUI.Layout id="layout${uuid}" pattern="2U" style="height:100%;width:100%;">
	<@ZKUI.Cell hideHeader="true">
		<@ZKUI.GridBox gridName="${gridName}">
		    <@ZKUI.Searchbar>
		    	<@ZKUI.SearchTop>
		    		<tr>
						<td valign="middle">
							<@ZKUI.Input name="shiftNo" maxlength="30" title="common_number" type="text"/>
						</td>
						<td valign="middle">
							<@ZKUI.Input name="shiftName" maxlength="30" title="common_name" type="text"/>
						</td>
						<td valign="middle">
							<@ZKUI.Combo empty="true" name="shiftType" readonly="true" title="att_shift_type">
					    		<option value="0"><@i18n 'att_shift_regularShift'/></option>
					    		<option value="1"><@i18n 'att_shift_flexibleShift'/></option>
					    	</@ZKUI.Combo>
						</td>
					</tr>
		    	</@ZKUI.SearchTop>
		    </@ZKUI.Searchbar>
		    <@ZKUI.Toolbar>
		    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:shift:refresh"></@ZKUI.ToolItem>
		    	<@ZKUI.ToolItem id="attShift.do?edit&shiftType=0" text="att_shift_addRegularShift" width="860" height="440" img="comm_add.png" action="commonAdd" permission="att:shift:add"></@ZKUI.ToolItem>
				<@ZKUI.ToolItem id="attShift.do?edit&shiftType=1" text="att_shift_addFlexibleShift" width="860" height="350" img="comm_add.png" action="commonAdd" permission="att:shift:add"></@ZKUI.ToolItem>
				<@ZKUI.ToolItem id="attShift.do?del&names=(shiftName)" text="common_op_del" img="comm_del.png" action="commonDel" permission="att:shift:del"></@ZKUI.ToolItem>
                <@ZKUI.ToolItem text="att_shift_cleanTimeSlot" img="comm_del.png" action="clearShiftCallbackFun" permission="att:shift:clearTimeSlot"></@ZKUI.ToolItem>
		        <@ZKUI.ToolItem id="attShift.do?export" isShow="false" type="export" permission="att:shift:export"/>
		    </@ZKUI.Toolbar>
		    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttShiftItem" query="attShift.do?list" onXLE="attShitQueryFirstSelectClick"/>
		</@ZKUI.GridBox>
	</@ZKUI.Cell>
	<@ZKUI.Cell fixSize="1" hideArrow="true" title="att_shift_timeSlotDetail" width="350px">
        <div style="cursor:pointer">
            <table class="ltsdtbl" id="ltsdtbl${uuid}" width="100%" style="border-collapse:collapse;"></table>
        </div>
	</@ZKUI.Cell>
</@ZKUI.Layout>
<style type="text/css">
.dhx_cell_cont_layout{
	overflow: auto !important;
}
</style>
<script type="text/javascript">

	/* 设置时间段 */
	function addTimeSlot(gridName, obj, rid)
	{
		var opts = {
			path: "attShift.do?addTimeSlot&id=" + rid,
			width: 800,
			height: 540,
			title: "<@i18n 'att_timeSlot_add'/>",
			editPage: true,
			gridName: gridName
		};
		DhxCommon.createWindow(opts);
	}

	//栅格最后一行选中的事件，默认选中第一行。
	function attShitQueryFirstSelectClick() {
		var attShiftGrid = ZKUI.Grid.get("${gridName}").grid;
		attShiftGrid.selectRow(attGlobalSelectRow, true);
		//获取栅格数据选中第几条
		var attSelectRow = attShiftGrid.getRowIndex(attShiftGrid.getSelectedRowId());
		//若是无数据，右边的时间显示为空！
		if (attSelectRow == -1) {
			appendEmptyTip($("#ltsdtbl${uuid}"))
		}
	}

	var attGlobalSelectRow = 0;
	function inputHtml(id) {
		var attShiftGrid = ZKUI.Grid.get("${gridName}").grid;
		attGlobalSelectRow = attShiftGrid.getRowIndex(attShiftGrid.getSelectedRowId());
	    $.ajax({
	        data: {
		        id: id
	        },
	        dataType: "json",
	        type: "post",
	        url: "attShift.do?getAttTimeSlotJson",
	        success: function(data) {
		        if (data.result == "success") {
			        var tsdIds = data.tsdIds;
			        var tsdIdArray = [];
			        if (tsdIds) {
				        tsdIdArray = tsdIds.split(",");
			        }
			        var periodicUnit = $("#${gridName}")[0].grid.cells(id, $("#${gridName}")[0].grid.getColIndexById("periodicUnit")).getValue();
			        var periodNumber = $("#${gridName}")[0].grid.cells(id, $("#${gridName}")[0].grid.getColIndexById("periodNumber")).getValue();
			        if (periodicUnit == "<@i18n 'common_day'/>") {
                        var startDate = "";
                        if (data.startDate) {
                            startDate = new Date(data.startDate);
                        }
			        	inputDayHtml(data, tsdIdArray, periodNumber, startDate);//天
			        }
			        if (periodicUnit == "<@i18n 'common_week'/>") {
			        	inputWeekHtml(data, tsdIdArray, periodNumber);//周
			        }
			        if (periodicUnit == "<@i18n 'common_month'/>") {
			        	inputMonthHtml(data, tsdIdArray, periodNumber);//月
			        }
		        }
	        }
	    });
	}
	
	//周期单位为天
	function inputDayHtml(data, tsdIdArray, periodNumber, startDate) {
		var dayHtml = "";
	    for (var i = 0; i < parseInt(periodNumber); i++) {
	        var tempDayHtml = "";
	        dayHtml += "<tr class=\"att-timeslot-tr zk-hover-color\" align=\"center\">";
	        var ids = new Array();
	        for (var k = 0; k < data.tsdList.length; k++) {
		        var count = 0;
		        var temp = data.tsdList[k].split("_")[1] + "_" + i;//时间段id和下标拼凑字段 ---廖福安，2017/7/4
		        for (var g = 0; g < tsdIdArray.length; g++) {
			        if (tsdIdArray[g] == temp) {
				        count = 1;
				        break;
			        }
		        }
		        if (count == 1) {
			        tempDayHtml += data.tsdList[k].split("_")[0] + "<br>";
			        ids.push(temp);
		        }
	        }
	        if (ids.length > 0) {
		        dayHtml += "<td width=\"33%\" class=\"att-timeslot-td\" style=\"text-align:${leftRTL!'left'}\" ondblclick=\"liDbClick('" + (i + 1) + "','" + ids.join(",") + "');\"><span><@i18n 'att_shift_NO'/>" + (i + 1) + "<@i18n 'common_days'/></span></td>";
                if (startDate) {
                    dayHtml += "<td width=\"33%\" style=\"text-align:${leftRTL!'left'}\" ondblclick=\"liDbClick('" + (i + 1) + "','" + ids.join(",") + "');\"><span>" + dhx4.date2str(startDate, sysCfg.dhxShortDateFmt) + "</span></td>";
                }
                dayHtml += "<td width=\"33%\" style=\"text-align:${leftRTL!'left'}\" ondblclick=\"liDbClick('" + (i + 1) + "','" + ids.join(",") + "');\"><span class=\"att-timeslot-span\" id=\"" + (i + 1) + "\">";
	        } else {
		        dayHtml += "<td width=\"33%\" class=\"att-timeslot-td\" style=\"text-align:${leftRTL!'left'}\"><span><@i18n 'att_shift_NO'/>" + (i + 1) + "<@i18n 'common_days'/></span><td>";
                if (startDate) {
                    dayHtml += "<td width=\"33%\" style=\"text-align:${leftRTL!'left'}\"></td>";
                }
		        dayHtml += "<td width=\"33%\" style=\"text-align:${leftRTL!'left'}\"><span class=\"att-timeslot-span\">";
	        }
	        dayHtml += tempDayHtml;
	        dayHtml += "</span></td></tr>";
	        if (startDate) {
                startDate.setDate(startDate.getDate()+1);
			}
	    }
        $("#ltsdtbl${uuid}").html(dayHtml);
	}

	//周期单位为周
	function inputWeekHtml(data, tsdIdArray, periodNumber) {
		var weekHtml = "";
		var weekArray = new Array("<@i18n 'common_monday'/>", "<@i18n 'common_tuesday'/>", 
									"<@i18n 'common_wednesday'/>", "<@i18n 'common_thursday'/>", 
									"<@i18n 'common_friday'/>", "<@i18n 'common_saturday'/>", "<@i18n 'common_sunday'/>");
	    for (var i = 0; i < parseInt(periodNumber); i++) {
	        for (var j = 0; j < 7; j++) {
		        var tempWeekHtml = "&nbsp;&nbsp;";
		        weekHtml += "<tr class=\"att-timeslot-tr zk-hover-color\" align=\"center\">";
		        var ids = new Array();
		        for (var k = 0; k < data.tsdList.length; k++) {
			        var count = 0;
			        var temp = data.tsdList[k].split("_")[1] + "_" + (6 * i + i + j);//正常一周是七天，而这边*6且起始下标为0，所以需要再加上i ---廖福安，2017/7/4
			        for (var g = 0; g < tsdIdArray.length; g++) {
				        if (tsdIdArray[g] == temp) {
					        count = 1;
					        break;
				        }
			        }
			        if (count == 1) {
				        tempWeekHtml += data.tsdList[k].split("_")[0] + "&nbsp;&nbsp;";
				        ids.push(temp);
			        }
		        }
		        if (ids.length > 0) {
			        weekHtml += "<td width=\"30%\" class=\"att-timeslot-td\" style=\"text-align:${leftRTL!'left'}\" ondblclick=\"liDbClick('" + (6 * i + i + j) + "','" + ids.join(",") + "');\"><span>" + weekArray[j] + "</span></td>";
			        weekHtml += "<td width=\"70%\" ondblclick=\"liDbClick('" + (6 * i + i + j) + "','" + ids.join(",") + "');\"><span id=\"" + (6 * i + i + j) + "\">";
		        } else {
			        weekHtml += "<td width=\"30%\" class=\"att-timeslot-td\" style=\"text-align:${leftRTL!'left'}\"><span>" + weekArray[j] + "</span></td>";
			        weekHtml += "<td width=\"70%\"><span>";
		        }
		        weekHtml += tempWeekHtml;
		        weekHtml += "</span></td></tr>";
	        }
	    }
	    $("#ltsdtbl${uuid}").html(weekHtml);
	}

	//周期单位为月
	function inputMonthHtml(data, tsdIdArray, periodNumber) {
		var monthHtml = "";
	    for (var i = 0; i < parseInt(periodNumber); i++) {
	        for (var j = 0; j < 31; j++) {
		        var tempMonthHtml = "&nbsp;&nbsp;";
		        monthHtml += "<tr class=\"att-timeslot-tr zk-hover-color\" align=\"center\">";
		        var ids = new Array();
		        for (var k = 0; k < data.tsdList.length; k++) {
			        var count = 0;
			        var temp = data.tsdList[k].split("_")[1] + "_" + (30 * i + i + j);//每个月都按31天来算,这边*30，所以需要加i ---廖福安，2017/7/4
			        for (var g = 0; g < tsdIdArray.length; g++) {
				        if (tsdIdArray[g] == temp) {
					        count = 1;
					        break;
				        }
			        }
			        if (count == 1) {
				        tempMonthHtml += data.tsdList[k].split("_")[0] + "&nbsp;&nbsp;";
				        ids.push(temp);
			        }
		        }
		        if (ids.length > 0) {
			        monthHtml += "<td width=\"30%\" class=\"att-timeslot-td\" style=\"text-align:${leftRTL!'left'}\" ondblclick=\"liDbClick('" + (31 * i + i + j) + "','" + ids.join(",") + "');\"><span>" + (j + 1) + "<@i18n 'common_days'/></span></td>";
			        monthHtml += "<td width=\"70%\" ondblclick=\"liDbClick('" + (31 * i + i + j) + "','" + ids.join(",") + "');\"><span id=\"" + (31 * i + i + j) + "\">";
		        } else {
			        monthHtml += "<td width=\"30%\" class=\"att-timeslot-td\" style=\"text-align:${leftRTL!'left'}\" ><span>" + (j + 1) + "<@i18n 'common_days'/>"+"</span></td>";
			        monthHtml += "<td width=\"70%\"><span>";
		        }
		        monthHtml += tempMonthHtml;
		        monthHtml += "</span></td></tr>";
	        }
	    }
        $("#ltsdtbl${uuid}").html(monthHtml);
	}

    //双击时间段删除
    function liDbClick(no, idp) {
        var selectedId = ZKUI.Grid.get("${gridName}").grid.getSelectedRowId();
        messageBox({messageType:"confirm",text:"<@i18n 'att_shift_cleanAllTimeSlot'/>",callback:function(rest){
            if(rest){
                $.ajax({
                    data: {id: selectedId,idp:idp},
                    dataType: "json",
                    type: "post",
                    url: "attShift.do?cleanByIds",
                    success: function(data) {
                        if (data.ret == "ok") {
                            inputHtml(selectedId);
                        }
                    }
                });
			}
		}});
    };//att_shift_cleanAllTimeSlot
    //清楚所有字段 删除
    function clearShiftCallbackFun(id,bar,opt){
		var gridName = bar.gridName || "gridbox";
		var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
		if (!ids) {
			messageBox({messageType: "alert", text: "<@i18n 'common_prompt_selectObj'/>"});
		} else {
			messageBox({messageType:"confirm",text:"<@i18n 'att_shift_cleanAllTimeSlot'/>",callback:function(rest){
				if(rest){
					$.ajax({
						data: {id: ids},
						dataType: "json",
						type: "post",
						url: "attShift.do?clean",
						success: function(data) {
							if (data.ret == "ok") {
								var arr = ids.split(",");
								for (var i = 0; i < arr.length; i++) {
									inputHtml(arr[i]);
								}
								ZKUI.Grid.reloadGrid(gridName);
							}
						}
					});
				}
			}});
		}
	};

    $(function(){
        ZKUI.Grid.get("${gridName}").grid.attachEvent("onRowSelect", function(id, ind) {
            inputHtml(id);
        });
        //执行
        ZKUI.Layout.get("layout${uuid}").layout.cells("b").attachStatusBar({text: "<strong class='zk-msg-error' style='font-weight: bold;'><@i18n 'att_shift_doubleDeleteTimeSlot'/></strong>", height: 32});
    })

	/**
	 * 刷新表格列表
	 */
    function reloadAttShiftGrid() {
     	ZKUI.Grid.get("${gridName}").reload();
     	var attShiftGrid = ZKUI.Grid.get("${gridName}").grid;
		attShiftGrid.selectRow(attGlobalSelectRow, true);
    }
</script>