<#include '/public/template/editTemplate.html'>
<#macro editContent>
<form action='attShift.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
    <input type='hidden' name='id' value='${(item.id)!}'/>
    <input id="id_timeSlotDetailIds" name="timeSlotDetailIds" type="hidden" value="${(item.timeSlotDetailIds)!}" />
    <input id="id_timeSlotIds" name="timeSlotIds" type="hidden" value="${timeSlotIds!}" />
    <input id="id_timeSlotDetailIdsTemp" name="id_timeSlotDetailIdsTemp" type="hidden" value="${(item.timeSlotDetailIds)!}" />
    <input id="id_timeSlotIdsTemp" name="id_timeSlotIdsTemp" type="hidden" value="${timeSlotIds!}" />
    <fieldset style="height: 160px">
        <legend style="text-align: ${leftRTL!'left'}"><@i18n "att_shift_basicSet"/></legend>
        <table class="tableStyle">
            <tr>
                <!-- 名称 -->
                <th><label><@i18n 'common_name'/></label><span class='required'>*</span></th>
                <td><input id='shiftName'  name='shiftName' type='text' value='${(item.shiftName)!}' maxlength="30" />
                </td>
                <!-- 编号 -->
                <th><label><@i18n 'common_number'/></label><span class='required'>*</span></th>
                <td><input name='shiftNo' type='text' value='${(item.shiftNo)!}' maxlength="20" /></td>
            </tr>
            <tr style="display: none">
                <!-- 班次类型 -->
                <th><label><@i18n 'att_shift_type'/></label><span class='required'>*</span></th>
                <td>
                    <@ZKUI.Combo id="id_shiftType${uuid!}" width="148" empty="false" value="${(item.shiftType)!}"  hideLabel="true" name="shiftType">
                        <option value="0"><@i18n 'att_shift_regularShift'/></option>
                        <option value="1"><@i18n 'att_shift_flexibleShift'/></option>
                    </@ZKUI.Combo>
                </td>
                <!-- 班次颜色 -->
                <th><label><@i18n 'att_shift_color'/></label></th>
                <td>
                    <@ZKUI.Input type="color" name="shiftColor" readonly="readonly" style="width: 143px;" hideLabel="true" value="${(item.shiftColor)!'#66FF66'}"/>
                </td>
            </tr>
            <tr>
                <!-- 周期单位 -->
                <th><label><@i18n 'att_shift_periodicUnit'/></label><span class='required'>*</span></th>
                <td>
                    <@ZKUI.Combo id="id_periodicUnit${uuid!}" width="148" empty="false" value="${(item.periodicUnit)!}"  hideLabel="true" name="periodicUnit">
                        <option value="0"><@i18n 'common_day'/></option>
                        <option value="1"><@i18n 'common_week'/></option>
                        <option value="2"><@i18n 'common_month'/></option>
                    </@ZKUI.Combo>
                </td>
                <!-- 周期数 -->
                <th><label><@i18n 'att_shift_periodNumber'/></label><span id="range" class='required'>*</span></th>
                <td>
                    <input maxlength="2" id="id_periodNumber" name='periodNumber' type='text' value='${(item.periodNumber)!}'
                           onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'');if(this.value>$('#range').html().substring(3,5)){this.value=$('#range').html().substring(3,5)}}"
                           onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'');if(this.value>$('#range').html().substring(3,5)){this.value=$('#range').html().substring(3,5)}}"/>
                </td>
            </tr>
            <tr class="id_dayPeriodicUnit">
                <!-- 周期开始类型 -->
                <th>
                    <label><@i18n 'att_shift_periodStartMode'/></label><span class='required'>*</span>
                </th>
                <td>
                    <@ZKUI.Combo id="id_periodStartMode${uuid!}" name="periodStartMode" value="${(item.periodStartMode)!'0'}" hideLabel="true" empty="false" width="148">
                        <option value="0"><@i18n 'att_shift_periodStartModeByPeriod'/></option>
                        <option value="1"><@i18n 'att_shift_periodStartModeBySch'/></option>
                    </@ZKUI.Combo>
                </td>
                <!-- 是否月内轮班 -->
                <th><label><@i18n 'att_shift_isShiftWithinMonth'/></label><span class='required'>*</span>
                    <span class="attTooltip">
                            <span class="att_icv att_icv-green icv-ic_que zk-colors-fg-green" />
                            <span class="attCommonTipClass attCommonTipClassLeft">
                                <p class="warningColor"><@i18n 'att_shift_isShiftWithinMonthRemark1'/></p>
                                <p class="warningColor"><@i18n 'att_shift_isShiftWithinMonthRemark2'/></p>
                            </span>
                    </span>
                </th>
                <td>
                    <@ZKUI.Combo id="id_isShiftWithinMonth${uuid!}" empty="false" width="148" value="${(item.isShiftWithinMonth?string('true','false'))!'true'}" hideLabel="true" name="isShiftWithinMonth">
                        <option value="true"><@i18n 'common_yes'/></option>
                        <option value="false"><@i18n 'common_no'/></option>
                    </@ZKUI.Combo>
                </td>
            </tr>
            <tr class="periodStartModeByPeriodClass">
                <!-- 起始日期 -->
                <th><label><@i18n 'att_shift_startDate_firstDay'/></label><span class='required'>*</span>
                    <div class="attTooltip">
                            <span class="att_icv att_icv-green icv-ic_que zk-colors-fg-green" />
                            <span class="attCommonTipClass">
                                <p class="warningColor">
                                    <@i18n 'att_shift_attShiftStartDateRemark'/>
                                    <br />
                                    <@i18n 'att_shift_attShiftStartDateRemark2'/>
                                </p>
                            </span>
                    </div>
                </th>
                <td>
                    <@ZKUI.Input id="id_startDate" type="date" value="${((item.startDate)?string('yyyy-MM-dd'))!}" readonly="true" today="true" name="startDate" hideLabel="true" title="att_shift_startDate_firstDay"/>
                </td>
            </tr>
        </table>
    </fieldset>
    <fieldset id="id_attShiftAdvancedSet" style="margin-top: 5px">
        <legend style="text-align: ${leftRTL!'left'}"><@i18n "att_shift_advancedSet"/></legend>
            <table class="tableStyle">
            <tr class="attendanceMode">
                <!-- 工作类型 -->
                <th><label><@i18n 'att_shift_workType'/></label><span class='required'>*</span>
                    <span class="attTooltip">
                            <span class="att_icv att_icv-green icv-ic_que zk-colors-fg-green" />
                            <span class="attCommonTipClass">
                                <p class="warningColor"><@i18n 'att_shift_workTypeRemark1'/></p>
                            </span>
                    </span>
                </th>
                <td>
                    <@ZKUI.Combo width="148" id="shiftWorkType" empty="false" value="${(item.workType)!}" hideLabel="true" name="workType" key="AttShiftWorkType" autoFirst="true" />
                </td>
                <!-- 考勤方式 -->
                <th><label><@i18n 'att_shift_attendanceMode'/></label><span class='required'>*</span>
                    <span class="attTooltip">
                            <span class="att_icv att_icv-green icv-ic_que zk-colors-fg-green" />
                            <span class="attCommonTipClass attCommonTipClassTopLeft">
                                <p class="warningColor"><@i18n 'att_shift_attendanceModeRemark1'/></p>
                                <p class="warningColor"><@i18n 'att_shift_attendanceModeRemark2'/></p>
                                <p class="warningColor"><@i18n 'att_shift_attendanceModeRemark3'/></p>
                            </span>
                    </span>
                </th>
                <td>
                    <@ZKUI.Combo id="attendanceMode${uuid!}" width="148" empty="false" value="${(item.attendanceMode)!}" hideLabel="true" name="attendanceMode">
                        <option value="0"><@i18n 'att_shift_shiftNormal'/></option>
                        <option value="1"><@i18n 'att_shift_oneDayOneCard'/></option>
                        <!--<option value="2"><@i18n 'att_shift_onlyBrushTime'/></option>-->
                        <option value="3"><@i18n 'att_shift_notBrushCard'/></option>
                    </@ZKUI.Combo>
                </td>
            </tr>
            <tr class="attendanceMode" id="overTimeTag">
                <!-- 加班标记 -->
                <th style="display: none" class="attendanceMode_th"><label><@i18n 'att_shift_overtimeSign'/></label><span class='required'>*</span></th>
                <td style="display: none">
                    <@ZKUI.Combo id="overtimeSign${uuid!}" width="148" empty="false" value="${(item.overtimeSign)!}" hideLabel="true" name="overtimeSign">
                        <option value="0"><@i18n 'att_shift_normal'/></option>
                        <option value="1"><@i18n 'att_shift_restday'/></option>
                        <option value="2"><@i18n 'common_leftMenu_holiday'/></option>
                    </@ZKUI.Combo>
                </td>
                <!-- 加班方式 -->
                <th class="attendanceMode_th"><label><@i18n 'att_shift_overtimeMode'/></label><span class='required'>*</span></th>
                <td>
                    <@ZKUI.Combo id="overtimeMode${uuid!}" width="148" empty="false" value="${(item.overtimeMode)!}" hideLabel="true" name="overtimeMode">
                        <option value="0"><@i18n 'att_shift_autoCalc'/></option>
                        <option value="1"><@i18n 'att_shift_mustApply'/></option>
                        <!-- <option value="2"><@i18n 'att_shift_mustOvertime'/></option> -->
                        <!--<option value="3"><@i18n 'att_shift_timeSmaller'/></option>-->
                        <option value="4"><@i18n 'att_shift_notOvertime'/></option>
                    </@ZKUI.Combo>
                </td>
            </tr>
        </table>
    </fieldset>
</form>

<script type='text/javascript'>

    /*页面布局或初始化*/
    //国际化星期
    var weekArray = new Array("<@i18n 'common_monday'/>", "<@i18n 'common_tuesday'/>","<@i18n 'common_wednesday'/>",
        "<@i18n 'common_thursday'/>", "<@i18n 'common_friday'/>", "<@i18n 'common_saturday'/>", "<@i18n 'common_sunday'/>");

    $(function(){

        //工作类型选择事件
        attChangeWorkType("${(item.workType)!}");
        ZKUI.Combo.get("shiftWorkType").combo.attachEvent("onChange",function(value,text){
            attChangeWorkType(value);
        });
        function attChangeWorkType(value, overtimeMode) {

            // 根据班次类型，动态变更加班方式
            var overtimeModeCombo = ZKUI.Combo.get("overtimeMode${uuid!}").combo;
            var overtimeModeValue = overtimeModeCombo.getSelectedValue();
            overtimeModeCombo.clearAll();
            if(value != "" && value != "normalWork") {
                overtimeModeCombo.addOption([
                     {"value": "0", "text": "<@i18n 'att_shift_autoCalc'/>"},
                     {"value": "1", "text": "<@i18n 'att_shift_mustApply'/>"},
                ]);
                if (overtimeModeValue == 4) {
                    overtimeModeCombo.setComboValue(0);
                } else {
                    overtimeModeCombo.setComboValue(overtimeModeValue);
                }

            } else {
                overtimeModeCombo.addOption([
                    {"value": "0", "text": "<@i18n 'att_shift_autoCalc'/>"},
                    {"value": "1", "text": "<@i18n 'att_shift_mustApply'/>"},
                    {"value": "4", "text": "<@i18n 'att_shift_notOvertime'/>"},
                ]);
                overtimeModeCombo.setComboValue(overtimeModeValue);
            }

            var checkFlag = true;
            if(value != "" && value != "normalWork") {
                checkFlag = false;
                // 根据工作类型设置，加班标记(休息日加班weekendOt=休息日，节假日加班holidayOt=节假日)
                ZKUI.Combo.get("overtimeSign${uuid!}").combo.setComboValue(value=="weekendOt"?"1":"2");
                ZKUI.Combo.get("attendanceMode${uuid!}").combo.setComboValue("0");
            } else {
                // 根据工作类型设置，加班标记(正常工作normalWork=平时)
                ZKUI.Combo.get("overtimeSign${uuid!}").combo.setComboValue(0);
            }
            ZKUI.Combo.get("overtimeSign${uuid!}").combo.disable(!checkFlag);
            ZKUI.Combo.get("attendanceMode${uuid!}").combo.disable(!checkFlag);
        }

        /*考勤方式选择事件*/
        attChangeAttendanceMode("${(item.attendanceMode)!}");
        ZKUI.Combo.get("attendanceMode${uuid!}").combo.attachEvent("onChange",function(value,text){
            attChangeAttendanceMode(value);
        });
        function attChangeAttendanceMode(value) {
            // 免打卡或者一天内任打一次有效卡，不统计加班
            if (value == "1" || value == "3") {
                // 加班方式设置为不算加班
                ZKUI.Combo.get("overtimeMode${uuid!}").combo.setComboValue("4");
                // 加班方式设置不可修改
                ZKUI.Combo.get("overtimeMode${uuid!}").combo.disable(true);
            } else {
                // 加班方式可修改
                ZKUI.Combo.get("overtimeMode${uuid!}").combo.disable(false);
                var workType = ZKUI.Combo.get("shiftWorkType").combo.getSelected();
                // 防止未加载完成，取不到值
                if(workType == null)
                {
                    workType = "${(item.workType)!}";
                }
                attChangeWorkType(workType);
            }
        }

        /*表单验证*/
        $('#${formId}').validate( {
            debug: true,
            rules: {
                'shiftNo': {
                    required: true,
                    validCode: true,
                    overRemote: ["attShift.do?validNo", "${(item.shiftNo)!}"]
                },
                'shiftName': {
                    required: true,
                    unInputChar: true,
                    overRemote: ["attShift.do?validName", "${(item.shiftName)!}"]
                },
                'periodNumber': {
                    required: true
                }
            },
            messages:{
                'shiftNo': {
                    validCode: "<@i18n 'att_common_letterNumber'/>"
                }
            },
            submitHandler: function() {

                // 单位或者周期修改时，提示设置时间段（适用新增、编辑场景）
                var oldPeriodicUnit = "${(item.periodicUnit)!}";
                var oldPeriodNumber = "${(item.periodNumber)!}";
                var newPeriodicUnit = ZKUI.Combo.get("id_periodicUnit${uuid!}").combo.getSelectedValue();
                var newPeriodNumber = $("#id_periodNumber").val();
                var showAddTimeSlot = false;
                if (oldPeriodicUnit != newPeriodicUnit) {
                    showAddTimeSlot = true;
                } else if (oldPeriodNumber != newPeriodNumber) {
                    showAddTimeSlot = true;
                }
                if(showAddTimeSlot && !isContinueAdd) {
                     var fun = function(result) {
                        var attShiftId = result.data.id;
                        messageBox({messageType:"confirm", text: "<@i18n 'att_timeSlot_add'/>",
                            callback: function(isConfirm){
                                if (isConfirm) {
                                    var opts = {
                                        path: "attShift.do?addTimeSlot&id=" + attShiftId,
                                        width: 800,
                                        height: 540,
                                        title: "<@i18n 'att_timeSlot_add'/>",
                                        editPage: true,
                                        gridName: "gridBox"
                                    };
                                    DhxCommon.createWindow(opts);
                                }
                            }
                        });
                        reloadAttShiftGrid();
                     }
                    <@submitHandler callBackFun="fun(result)"/>
                } else {
                    <@submitHandler/>
                }
            }
        });

        var idPeriodicUnit ="id_periodicUnit${uuid!}";
        ZKUI.Combo.get(idPeriodicUnit).combo.attachEvent("onChange",function(value,text) {
            if (value == 0) {
                ZKUI.Combo.get("id_isShiftWithinMonth${uuid!}").combo.disable(false);
                $("#range").html("(1-99)*");
            } else {
                ZKUI.Combo.get("id_isShiftWithinMonth${uuid!}").combo.disable(true);
                ZKUI.Combo.get("id_isShiftWithinMonth${uuid!}").combo.setComboValue("false");
                if (value == 1) {
                    $("#range").html("(1-15)*");
                } else {
                    $("#range").html("(1-12)*");
                }
            }
            $("#id_periodNumber").val("");
            $("#id_timeSlotDetailIds").val("");
        });

        ZKUI.Combo.get("id_periodStartMode${uuid!}").combo.attachEvent("onChange",function(value,text) {
            if (value == 0) {
                $(".periodStartModeByPeriodClass").attr("style","visibility:visible");
            } else {
                $(".periodStartModeByPeriodClass").attr("style","visibility:hidden");
            }
            $("#id_timeSlotDetailIds").val("");
            $("#id_timeSlotIds").val("");
        });

        $("#id_periodNumber").change(function(e){
            $("#id_timeSlotDetailIds").val("");
            $("#id_timeSlotIds").val("");
        });
    });

    /*初始化数据和界面布局*/
    editAttShit();

     /*初始化数据和界面布局*/
    function editAttShit() {
        var periodStartMode = ZKUI.Combo.get("id_periodStartMode${uuid}").combo.getSelected();
        if (periodStartMode == 0) {
            $(".periodStartModeByPeriodClass").attr("style","visibility:visible");
        } else {
            $(".periodStartModeByPeriodClass").attr("style","visibility:hidden");
        }

    	var workType = ZKUI.Combo.get("attendanceMode${uuid!}").combo.getSelected();
        var periodicUnit = ZKUI.Combo.get("id_periodicUnit${uuid}").combo.getSelected();
        if (periodicUnit == 0) {
            $("#range").html("(1-99)*");
            ZKUI.Combo.get("id_isShiftWithinMonth${uuid!}").combo.disable(false);
        } else if (periodicUnit == 1){
            $("#range").html("(1-15)*");
            ZKUI.Combo.get("id_isShiftWithinMonth${uuid!}").combo.disable(true);
            ZKUI.Combo.get("id_isShiftWithinMonth${uuid!}").combo.setComboValue("false")
        } else {
            ZKUI.Combo.get("id_isShiftWithinMonth${uuid!}").combo.disable(true);
            ZKUI.Combo.get("id_isShiftWithinMonth${uuid!}").combo.setComboValue("false")
            $("#range").html("(1-12)*");
        }

        var shiftType = ZKUI.Combo.get("id_shiftType${uuid}").combo.getSelected();
        if (shiftType == 1) {
            $("#id_attShiftAdvancedSet").hide();
        }
    };

</script>
</#macro>