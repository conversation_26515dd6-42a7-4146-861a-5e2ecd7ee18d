<#include '/public/template/editTemplate.html'>
<#macro editContent>
	<div class="attSelectPersonDiv">
		<#include '/att/person/selectPersonContent.html'>
	</div>

	<form action='attSign.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
		<input type='hidden' name='id' value='${(item.id)!}'/>
		<input id="personIds" name="personIds" type="hidden" />
		<input id="personPins" name="personPin" type="hidden" />
		<input id="idSignDatetime" name="signDatetime" type="hidden" />
		<table class='tableStyle' style="margin-top: 10px">
			<tr>
				<!--补签日期-->
				<td><label><@i18n 'att_sign_signDate'/></label><span class='required'>*</span></td>
				<td><@ZKUI.Input type="date" id="attSignDateId" name="attSignDate" max="today" today="true" noOverToday="true" readonly="true" hideLabel="true"/>
					<@ZKUI.Input id="attSignTimeId" ignoreFormat="true" name="attSignTime" type="datetime" onlyTime="true" hideSeconds="true" format="%H:%i" today="true" readonly="true" hideLabel="true"/>
				</td>
				<!--备注-->
				<td rowspan="2" style="text-align: right; padding-right: 30px"><label><@i18n 'common_remark'/></label></td>
				<td  rowspan="2"><textarea name='remark' maxlength="100" type='text' value='${(item.remark)!}' rows="2" style="width: 200px;height: 50px"/></td>
			</tr>
			<tr>
				<td><label><@i18n 'att_cardStatus_attState'/></label><span class='required'>*</span></td>
				<td>
					<@ZKUI.Combo autoFirst="true" width="148" hideLabel="true" empty="false" name="attState" path="attDevice.do?getShortcutKeyName"></@ZKUI.Combo>
				</td>
			</tr>
		</table>
	</form>

	<script type='text/javascript'>
		$().ready(function() {
		   	$("#gridbox${uuid}Bottom").html();
			$('#${formId}').validate({
				debug : true,
				rules :
				{
					'attSignDate' :
					{
						required : true
					},
					'attSignTime' :{
					    required : true,
                	}
				},
				submitHandler : function()
				{
					//补签日期
					var attSignDate = $("#${formId} input[name='attSignDate']").val();
					//补签时间
					var attSignTime = $("#${formId} input[name='attSignTime']").val();
					$("#idSignDatetime").val(attSignDate+" "+attSignTime+":00");

					var personIds = ZKUI.PersSelect.get("selectPerson${uuid!}").getRightGridSelectIds();
					if(personIds == "")
					{
						messageBox({
							messageType : "alert",
							text : "<@i18n 'att_common_neesSelectPerson'/>"
						});
					}
					else
					{
						var selectConten = ZKUI.PersSelect.get("selectPerson${uuid!}").getSelectRows();
						var personPins = "";
						for(var i=0; i<selectConten.length; i++)
						{
							personPins += selectConten[i].personPin + ",";
						}
						$("#personPins").val(personPins.substring(0,personPins.length-1));
						$("#personIds").val(personIds);
						<@submitHandler/>
					}
				}
			}) ;
		});

		// 设置申请时间不超过上上月1号
        var minDate = new Date();
        minDate.setMonth(minDate.getMonth()-2);
        minDate.setDate(1);
        ZKUI.Input.PULL.attSignDateId.setMinDate(minDate);

	</script>
</#macro>