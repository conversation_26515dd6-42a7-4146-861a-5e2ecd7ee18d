<#assign gridName="attTimeSlotGrid${uuid!}">
<script>

	function showEditTimeSlot(name, el)
	{
		return "<a href=\"javascript:void(0)\" onclick=\"editTimeSlot('${gridName}',this,'"+ arguments[4]+"')\" title=\"" + name + "\">" + name + "</a>";
	}

	function editTimeSlot(gridName, obj, rid) {
		var grid = ZKUI.Grid.GRID_PULL["${gridName}"].grid;
		var row = grid.getRowData(rid);
		var opts = {
			path: "attTimeSlot.do?edit&id=" + rid,
			width: 720,
			height: 620,
			title: "<@i18n 'att_timeSlot_normalTime'/>",
			editPage:true,
			gridName: gridName
		};
		if (row.periodType == "<@i18n 'att_timeSlot_elasticTime'/>") {
			opts.width = 500;
			opts.height = 360;
			opts.title = "<@i18n 'att_timeSlot_elasticTime'/>";
		}
		DhxCommon.createWindow(opts);
	};

</script>
<@ZKUI.GridBox gridName="${gridName}">
		<@ZKUI.Searchbar>
			<@ZKUI.SearchTop>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="periodName" maxlength="30" title="common_name" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Combo empty="true" name="periodType" title="att_timeSlot_periodType">
							<option value="0"><@i18n 'att_timeSlot_normalTime'/></option>
							<option value="1"><@i18n 'att_timeSlot_elasticTime'/></option>
						</@ZKUI.Combo>
					</td>
				</tr>
			</@ZKUI.SearchTop>
		</@ZKUI.Searchbar>
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:timeSlot:refresh"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="attTimeSlot.do?edit&periodType=0" text="att_timeSlot_normalTimeAdd" width="720" height="620" img="comm_add.png" action="commonAdd" permission="att:timeSlot:add"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="attTimeSlot.do?edit&periodType=1" text="att_timeSlot_elasticTimeAdd" width="500" height="360" img="comm_add.png" action="commonAdd" permission="att:timeSlot:add"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="attTimeSlot.do?del&names=(periodName)" text="common_op_del" img="comm_del.png" action="commonDel" permission="att:timeSlot:del"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="attTimeSlot.do?export" isShow="false" type="export" permission="att:timeSlot:export"/>
		</@ZKUI.Toolbar>
		<@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem" query="attTimeSlot.do?list" />
</@ZKUI.GridBox>

<script type="text/javascript">

    var attSelectRow = 0;//记录leftGrid选中的行
    //rigthGrid 删除点击事件调用方法
    function delBreakTime(){
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        var leftGrid = dbGrid.leftGrid;
        var rightGrid = dbGrid.rightGrid;
        var breakTimeIds = rightGrid.grid.getCheckedRows(0);
        var timeSlotId = leftGrid.grid.getSelectedRowId();
        if(breakTimeIds=="" || breakTimeIds==null ){
            messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});  //common_prompt_selectObj=请选择你要操作的对象！
        }else {
            deleteConfirm(function(result){
                if(result){//弹出提示，是否要删除
                    $.ajax({
                        url:"attTimeSlot.do?delBreakTime",
                        type:"post",
                        dataType:"json",
                        data:{
                            breakTimeIds :breakTimeIds,
                            timeSlotId:timeSlotId
                        },
                        success:function (result) {
                            openMessage(msgType.success);
                            var dbGrid = ZKUI.DGrid.get("${gridName}");
                            var leftGrid = dbGrid.leftGrid;
                            var rightGrid = dbGrid.rightGrid;
                            attSelectRow = leftGrid.grid.getRowIndex(leftGrid.grid.getSelectedRowId());
                            leftGrid.reload();
                            // leftGridClickAttGroup(timeSlotId,attSelectRow);
							leftGridRowSelect(timeSlotId,attSelectRow);
                        }
                    });
                }
            },"common_prompt_sureToDelThese");
        }
    }

    //leftGrid行选中点击事件
    function leftGridRowSelect(rid, ind) {
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        var leftGrid = dbGrid.leftGrid;
        var rightGrid = dbGrid.rightGrid;
        attSelectRow = ind;
        rightGrid.reload(function() {},{timeSlotId: rid});
    }

    //leftGrid点击删除,实现删除功能
    function deleteAttGroup(id,bar){
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        var leftGrid = dbGrid.leftGrid;
        var groupIds = leftGrid.grid.getCheckedRows(0);
        if(groupIds=="" || groupIds==null ){
            messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});  //common_prompt_selectObj=请选择你要操作的对象！
        }else{
            var checkSelectIds=ZKUI.Grid.get(bar.gridName).grid.getCheckedRows(0);
            deleteConfirm(function(result){
                if(result){//弹出提示，是否要删除
                    $.ajax({
                        data: {ids: checkSelectIds},
                        dataType: "json",
                        type: "post",
                        url: id,
                        success: function(data) {
                            if(data.ret === sysCfg.success){
                                openMessage(msgType.success);
                                var dbGrid = ZKUI.DGrid.get("${gridName}");
                                var leftGrid = dbGrid.leftGrid;
                                var rightGrid = dbGrid.rightGrid;
                                attSelectRow=0;
                                leftGrid.reload();
                                rightGrid.reload();
                            }
                            else {
                                openMessage(msgType.error, data.msg);
                            }
                        }
                    });
                }
            },"common_prompt_sureToDelThese");
        }
    }

    function attQueryFirstSelectClick (){
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        var leftGrid = dbGrid.leftGrid;
        //左侧选中第一行。
        attSelectRow=0;
        leftGrid.grid.selectRow(attSelectRow,true);
        //右侧选中单机事件
    }

    /**
	 * 添加休息时间段回调
     * @param value
     * @param text
     * @param event
     * @returns {boolean}
     */
    function afterAddBreakTime(value, text, event) {
		var ret = false;
		var dbGrid = ZKUI.DGrid.get("${gridName}");
		var leftGrid = dbGrid.leftGrid;
		$.ajax({
			data: {
				timeSlotId: leftGrid.grid.getSelectedRowId(),
				breakTimeIds: value
			},
			dataType: "json",
			type: "post",
			async :false,
			url: "attTimeSlot.do?addBreakTime",
			success: function(data) {
				if(data) {
					if (!data.success) {
						messageBox({messageType: "alert",text: data.msg});
					} else {
						ret = true;
						var timeSlotId = leftGrid.grid.getSelectedRowId();
						attSelectRow = leftGrid.grid.getRowIndex(leftGrid.grid.getSelectedRowId());
						leftGrid.reload();
						leftGridRowSelect(timeSlotId, attSelectRow);
						openMessage(msgType.success, "<@i18n 'common_op_succeed'/>");
					}
				} else {
					openMessage(msgType.error, "<@i18n 'common_prompt_exception_dataaccess'/>");
				}
			}
		});
		return ret;
    }

</script>