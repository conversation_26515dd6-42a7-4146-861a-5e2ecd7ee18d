<#include "/public/template/editTemplate.html">
<#macro editContent>
<style type="text/css">
    #breakTimeTable tr .addAttBreakTime{
        visibility: hidden;
        cursor: pointer;
    }
    #breakTimeTable tr:last-child .addAttBreakTime{
        visibility: visible;
    }
</style>
<form action="attTimeSlot.do?save" id="${formId}" method="post" enctype='multipart/form-data' style="overflow: hidden;">
    <!-- 时间段id -->
    <input id="att_timeSlotId" name="id" type="hidden" value="${(item.id)!}" />
    <#if (item.id)?exists>
    <!-- 时段类型（0：正常时间段，1：弹性时间段） -->
    <input name="periodType" type="hidden" value="${(item.periodType)!}" />
    <#else>
        <#if baseRuleCountOvertime=="false">
        <!-- 提前是否计加班 -->
        <input name="isAdvanceCountOvertime" type="hidden" value="false" />
        <!-- 延后是否计加班 -->
        <input name="isPostponeCountOvertime" type="hidden" value="false" />
        <!-- 是否计加班 -->
        <input name="isCountOvertime" type="hidden" value="false" />
        </#if>
    </#if>
    <!-- 段间扣除 -->
    <input id="id_interSegmentDeduction" name="interSegmentDeduction" type="hidden" value="${(item.interSegmentDeduction)!}" />

    <!-- 考勤规则：最短的考勤时段 -->
    <input id="id_baseRuleShortestMinutes" name="baseRuleShortestMinutes" type="hidden" value="${attParams['att.baseRule.shortestMinutes']!}" />
    <!-- 考勤规则：最长的考勤时段 -->
    <input id="id_baseRuleLongestMinutes" name="baseRuleLongestMinutes" type="hidden" value="${attParams['att.baseRule.longestMinutes']!}" />
    <!-- 考勤规则：是否统计加班 -->
    <input id="id_baseRuleCountOvertime" name="baseRuleCountOvertime" type="hidden" value="${attParams['att.baseRule.countOvertime']!}" />
    <!-- 休息时间段数据 -->
    <input id="id_breakTimeData" name="breakTimeData" type="hidden" value='${(item.breakTimeData)!}' />
    <fieldset style="margin-bottom: 15px;">
        <table class="tableStyle">
            <tr>
                <!-- 名称 -->
                <th style="width: 128px;"><label><@i18n "common_name"/></label><span class="required">*</span></th>
                <td>
                    <input id="id_periodName" maxlength="30" name="periodName" type="text" value="${(item.periodName)!}" />
                </td>
                <!-- 时间段类型 -->
                <th style="display: none;"><label><@i18n "att_timeSlot_periodType"/></label><span class="required">*</span></th>
                <td style="display: none;">
                    <@ZKUI.Combo id="id_periodType" width="148" empty="false" value="${(item.periodType)!}"  hideLabel="true" name="periodType">
                        <option value="0"><@i18n "att_timeSlot_normalTime"/></option>
                        <option value="1"><@i18n "att_timeSlot_elasticTime"/></option>
                    </@ZKUI.Combo>
                </td>
            </tr>
        </table>
    </fieldset>
    <!-- 正常时间段 -->
    <div class="class_normalTime">
        <fieldset style="margin-bottom: 15px;">
            <table class="tableStyle">

                <tr>
                    <!-- 上班时间 -->
                    <th style="width: 128px;"><label><@i18n "att_timeSlot_toWorkTime"/></label><span class="required">*</span></th>
                    <td>
                        <div id="toWorkTime" onkeyup="narmolWorkingHours()"></div>
                    </td>
                    <!-- 下班时间 -->
                    <th style="width: 128px;"><label><@i18n "att_timeSlot_offWorkTime"/></label><span class="required">*</span></th>
                    <td>
                        <div id="offWorkTime" onkeyup="narmolWorkingHours()"></div>
                    </td>
                </tr>
                <tr>
                    <!-- 上班前 -->
                    <th><label><@i18n "att_timeSlot_beforeToWork"/></label><span class="required">*</span></th>
                    <td>
                        <input maxlength="3" id="beforeToWorkMinutes" name="beforeToWorkMinutes" type="text" value="120" style="width: 25px;">
                        <label><@i18n "att_timeSlot_minutesSignInValid"/></label>
                    </td>
                    <!-- 下班前 -->
                    <th><label><@i18n "att_timeSlot_beforeOffWork"/></label><span class="required">*</span></th>
                    <td>
                        <input maxlength="3" id="beforeOffWorkMinutes" name="beforeOffWorkMinutes" type="text" value="120" style="width: 25px;" >
                        <label><@i18n "att_timeSlot_minutesSignInValid"/></label>
                    </td>
                </tr>
                <tr>
                    <!-- 上班后 -->
                    <th><label><@i18n "att_timeSlot_afterToWork"/></label><span class="required">*</span></th>
                    <td>
                        <input maxlength="3" id="afterToWorkMinutes" name="afterToWorkMinutes" type="text" value="120" style="width: 25px;">
                        <label><@i18n "att_timeSlot_minutesSignInValid"/></label>
                    </td>
                    <!-- 下班后 -->
                    <th><label><@i18n "att_timeSlot_afterOffWork"/></label><span class="required">*</span></th>
                    <td>
                        <input maxlength="3" id="afterOffWorkMinutes" name="afterOffWorkMinutes" type="text" value="120" style="width: 25px;">
                        <label><@i18n "att_timeSlot_minutesSignInValid"/></label>
                    </td>
                </tr>
                <tr>
                    <!-- 允许迟到分钟数 -->
                    <th><label><@i18n "att_timeSlot_allowLateMinutes"/></label>
                        <span class="attTooltip">
                            <span class="att_icv att_icv-green icv-ic_que zk-colors-fg-green" />
                            <span class="attCommonTipClass">
                                <p class="warningColor"><@i18n 'att_timeSlot_allowLateMinutesRemark'/></p>
                            </span>
                        </span>
                    </th>
                    <td>
                        <input id="id_allowLateMinutes" maxlength="3" name="allowLateMinutes"
                               onkeyup="this.value=this.value.replace(/\D/g,'');" onafterpaste="this.value=this.value.replace(/\D/g,'');"
                               type="text" value="${(item.allowLateMinutes)!}" />
                    </td>
                    <!-- 允许早退分钟数 -->
                    <th><label><@i18n "att_timeSlot_allowEarlyMinutes"/></label>
                        <span class="attTooltip">
                            <span class="att_icv att_icv-green icv-ic_que zk-colors-fg-green" />
                            <span class="attCommonTipClass attCommonTipClassLeft">
                                <p class="warningColor"><@i18n 'att_timeSlot_allowEarlyMinutesRemark'/></p>
                            </span>
                        </span>
                    </th>
                    <td>
                        <input id="id_allowEarlyMinutes" maxlength="3" name="allowEarlyMinutes"
                               onkeyup="this.value=this.value.replace(/\D/g,'');" onafterpaste="this.value=this.value.replace(/\D/g,'');"
                               type="text" value="${(item.allowEarlyMinutes)!}" />
                    </td>
                </tr>
                <tr>
                    <!-- 是否必须签到 -->
                    <th><label><@i18n "att_timeSlot_isMustSignIn"/></label><span class="required">*</span></th>
                    <td>
                        <@ZKUI.Combo id="id_isMustSignIn" width="148" empty="false"  value="${(item.isMustSignIn?string('true','false'))!'true'}" hideLabel="true" name="isMustSignIn">
                        <option value="true"> <@i18n "common_yes"/></option>
                        <option value="false"> <@i18n "common_no"/></option>
                        </@ZKUI.Combo>
                    </td>
                    <!-- 是否必须签退 -->
                    <th><label><@i18n "att_timeSlot_isMustSignOff"/></label><span class="required">*</span></th>
                    <td>
                        <@ZKUI.Combo id="id_isMustSignOff" width="148" empty="false" value="${(item.isMustSignOff?string('true','false'))!'true'}" hideLabel="true" name="isMustSignOff">
                        <option value="true"><@i18n "common_yes"/></option>
                        <option value="false"> <@i18n "common_no"/></option>
                        </@ZKUI.Combo>
                    </td>
                </tr>
            </table>
        </fieldset>
        <fieldset style="margin-bottom: 15px;">
            <table class="tableStyle" id="breakTimeTable">
                <tr>
                    <!-- 是否段间扣除 -->
                    <th><label><@i18n "att_timeSlot_isSegmentDeduction"/></label><span class="required">*</span>
                        <span class="attTooltip">
                            <span class="att_icv att_icv-green icv-ic_que zk-colors-fg-green" />
                            <span class="attCommonTipClass">
                                <p class="warningColor"><@i18n 'att_timeSlot_isSegmentDeductionRemark'/></p>
                            </span>
                        </span>
                    </th>
                    <td>
                        <@ZKUI.Combo id="id_isSegmentDeduction" width="148" empty="false" value="${(item.isSegmentDeduction?string('true','false'))!'false'}"  hideLabel="true" name="isSegmentDeduction">
                        <option value="false"> <@i18n "common_no"/></option>
                        <option value="true"><@i18n "common_yes"/></option>
                    </@ZKUI.Combo>
                    </td>
                    <!-- 工作时长 -->
                    <th style="width: 128px;"><label><@i18n "att_timeSlot_workingHours"/></label><span class="required">*</span></th>
                    <td>
                        <input maxlength="5" id="id_workingHours" name="workingHours"
                               onkeyup="this.value=this.value.replace(/\D/g,'');" onafterpaste="this.value=this.value.replace(/\D/g,'');"
                               type="text" value="${(item.workingHours)!}" readonly="readonly" />
                    </td>
                </tr>

            </table>
        </fieldset>
        <fieldset>
            <table class="tableStyle">
                <tr>
                    <!-- 提前是否计加班 -->
                    <th>
                        <input id="isAdvanceCountOvertime" name="isAdvanceCountOvertime" value="${(item.isAdvanceCountOvertime)!}" type="hidden">
                        <@ZKUI.Input hideLabel="true" id="beforeToWorkAsOvertime" type="checkbox"/>
                        <span><@i18n "att_timeSlot_toWork"/></span>
                        <span class="beforeToWorkAsOvertimeClass">
                            <input class="beforeToWorkAsOvertimeClass" maxlength="3" id="beforeWorkOvertimeMinutes" name="beforeWorkOvertimeMinutes" type="text" value="60" style="width: 30px;margin-left: 10px">
                            <label><@i18n "att_timeSlot_minutesSignInAsOvertime"/></label>
                            <label>,</label>
                            <label><@i18n "att_timeSlot_minOvertimeMinutes"/></label>
                            <input class="beforeToWorkAsOvertimeClass" maxlength="3" id="minBeforeOvertimeMinutes" name="minBeforeOvertimeMinutes" type="text" value="60" style="width: 30px;">
                             <label>,</label>
                            <label><@i18n "att_timeSlot_maxOvertimeMinutes"/></label>
                            <input class="beforeToWorkAsOvertimeClass" maxlength="3" id="maxBeforeOvertimeMinutes" name="maxBeforeOvertimeMinutes" type="text" value="${item.maxBeforeOvertimeMinutes!0}" style="width: 30px;">
                        </span>
                    </th>
                </tr>
                <tr>
                    <!-- 延后是否计加班 -->
                    <th>
                        <input id="isPostponeCountOvertime" name="isPostponeCountOvertime" value="${(item.isPostponeCountOvertime)!}" type="hidden">
                        <@ZKUI.Input hideLabel="true" id="afterOffWorkAsOvertime" type="checkbox"/>
                        <span><@i18n "att_timeSlot_offWork"/></span>
                        <span class="afterOffWorkAsOvertimeClass">
                            <input class="afterOffWorkAsOvertimeClass" maxlength="3" id="afterWorkOvertimeMinutes" name="afterWorkOvertimeMinutes" type="text" value="60" style="width: 30px;margin-left: 10px">
                            <label><@i18n "att_timeSlot_minutesSignOutAsOvertime"/></label>
                            <label>,</label>
                            <label><@i18n "att_timeSlot_minOvertimeMinutes"/></label>
                            <input class="afterOffWorkAsOvertimeClass" maxlength="3" id="minAfterOvertimeMinutes" name="minAfterOvertimeMinutes" type="text" value="60" style="width: 30px;">
                             <label>,</label>
                            <label><@i18n "att_timeSlot_maxOvertimeMinutes"/></label>
                            <input class="afterOffWorkAsOvertimeClass" maxlength="3" id="maxAfterOvertimeMinutes" name="maxAfterOvertimeMinutes" type="text" value="${item.maxAfterOvertimeMinutes!0}" style="width: 30px;">
                        </span>
                    </th>
                </tr>

            </table>
        </fieldset>
        <div class="enableFlexibleWorkClass" style="<#if "${Application['system.productCode']}" == "ZKBioAccess">display:none;</#if>">
        <fieldset style="margin-top: 15px">
            <table class="tableStyle">
                <tr>
                    <th rowspan="2" style="width: 130px;vertical-align: middle;">
                        <input id="attEnableFlexibleWork" name="enableFlexibleWork" value="${(item.enableFlexibleWork)!'false'}" type="hidden">
                        <label>
                            <@ZKUI.Input hideLabel="true" id="attEnableFlexibleWorkEx" type="checkbox" />
                            <@i18n "att_timeSlot_enableFlexibleWork"/>
                        </label>
                        <div class="attTooltip">
                            <span class="att_icv att_icv-green icv-ic_que zk-colors-fg-green" />
                            <span class="attCommonTipClass attCommonTipClassTop">
                               <!-- <p><@i18n 'att_timeSlot_attEnableFlexibleWorkRemark1'/></p>-->
                                <p class="warningColor"><@i18n 'att_timeSlot_advanceWorkMinutesValidMsg2'/></p>
                                <p class="warningColor"><@i18n 'att_timeSlot_delayedWorkMinutesValidMsg2'/></p>
                                <!--<p><@i18n 'att_timeSlot_afterToWorkRemark'/></p>-->
                                <p class="warningColor"><@i18n 'att_timeSlot_delayedWorkMinutesValidMsg5'/></p>
                                <!--<p><@i18n 'att_timeSlot_beforeOffWorkRemark'/></p>-->
                                <p class="warningColor"><@i18n 'att_timeSlot_advanceWorkMinutesValidMsg5'/></p>
                                <p class="warningColor"><@i18n 'att_timeSlot_attEnableFlexibleWorkRemark2'/></p>
                                <p class="warningColor"><@i18n 'att_timeSlot_attEnableFlexibleWorkRemark3'/></p>
                                <p class="warningColor"><@i18n 'att_timeSlot_attEnableFlexibleWorkRemark4'/></p>
                            </span>
                        </div>
                        <label>

                        </label>
                    </th>
                    <td class="attEnableFlexibleWorkClass">
                        <@i18n "att_timeSlot_advanceWorkMinutes"/>
                        <span class="required attEnableFlexibleWorkRequired">*</span>
                        <input maxlength="3" id="attAdvanceWorkMinutes" name="advanceWorkMinutes" type="text" value="${(item.advanceWorkMinutes)!'60'}" style="width: 30px;" class="attEnableFlexibleWorkClass"> <@i18n "common_minutes"/>
                    </td>
                    <td>&nbsp;</td>
                </tr>
                <tr>
                    <td class="attEnableFlexibleWorkClass">
                        <@i18n "att_timeSlot_delayedWorkMinutes"/>
                        <span class="required attEnableFlexibleWorkRequired">*</span>
                        <input maxlength="3" id="attDelayedWorkMinutes" name="delayedWorkMinutes" type="text" value="${(item.delayedWorkMinutes)!'60'}" style="width: 30px;" class="attEnableFlexibleWorkClass"> <@i18n "common_minutes"/>
                    </td>
                    <td>&nbsp;</td>
                </tr>

            </table>
        </fieldset>
        </div>
    </div>
</form>

<script type="text/javascript">
    timeFormat = "HH:MM";//这个timeFormat的定义在timer.js
    var timeSlotId = $("#att_timeSlotId").val();
    var normalTimeObjJson = {
        beforeToWorkMinutes: timeSlotId ? "${(item.beforeToWorkMinutes)!}" : "60",
        toWorkTime: timeSlotId ? "${(item.toWorkTime)!}" : "09:00",//上班时间
        afterToWorkMinutes: timeSlotId ? "${(item.afterToWorkMinutes)!}" : "60",//结束签到时间
        beforeOffWorkMinutes: timeSlotId ? "${(item.beforeOffWorkMinutes)!}" : "60",//开始签退时间
        offWorkTime: timeSlotId ? "${(item.offWorkTime)!}" : "18:00",//下班时间
        afterOffWorkMinutes: timeSlotId ? "${(item.afterOffWorkMinutes)!}" : "60",//结束签退时间
        allowLateMinutes: timeSlotId ? "${(item.allowLateMinutes)!}" : 0,//允许迟到分钟数
        isMustSignIn: false,//是否必须签到
        allowEarlyMinutes: timeSlotId ? "${(item.allowEarlyMinutes)!}" : 0,//允许早退分钟数
        isMustSignOff: false,//是否必须签退
        markWorkingDays: timeSlotId ? "${(item.markWorkingDays)!}" : "1.0",//记为工作日数
        workingHours: timeSlotId ? "${(item.workingHours)!}" : 540,//工作时长
        isSegmentDeduction: false,//是否段间扣除
        isAdvanceCountOvertime: timeSlotId ? "${(item.isAdvanceCountOvertime)!}" : false,//提前是否计加班
        beforeWorkOvertimeMinutes: timeSlotId ? "${(item.beforeWorkOvertimeMinutes)!}" : "0",//签到早于时间
        minBeforeOvertimeMinutes: timeSlotId ? "${(item.minBeforeOvertimeMinutes)!}" : "30",
        isPostponeCountOvertime: timeSlotId ? "${(item.isPostponeCountOvertime)!}" : false,//延后是否计加班
        afterWorkOvertimeMinutes: timeSlotId ? "${(item.afterWorkOvertimeMinutes)!}" : "0",//签退晚于时间
        minAfterOvertimeMinutes: timeSlotId ? "${(item.minAfterOvertimeMinutes)!}" : "30",
        //休息时间段初始值
        segmentDeductionStart0:"12:00",
        segmentDeductionEnd0:"13:00",
        enableFlexibleWork: timeSlotId ? "${(item.enableFlexibleWork)!}" : false,
    };

    //考勤规则：是否统计加班
    var baseRuleCountOvertime = $("#id_baseRuleCountOvertime").val();

    //允许迟到分钟数change事件
    $("#id_allowLateMinutes").change(function() {
        if ($(this).val().length == 0) {
            $(this).val("0");
        }
    });

    //允许早退分钟数change事件
    $("#id_allowEarlyMinutes").change(function() {
        if ($(this).val().length == 0) {
            $(this).val("0");
        }
    });

    //工作时长change事件
    $("#id_workingHours").change(function() {
        if ($(this).val().length == 0) {
            $(this).val("0");
        }
    });

    /*提前计加班click事件*/
    $("#beforeToWorkAsOvertime").click(function(){
        changeAdvanceCountOvertime($(this).is(':checked'));
    });

    /*延后记加班checkbox click事件*/
    $("#afterOffWorkAsOvertime").click(function(){
        changePostponeCountOvertime($(this).is(':checked'));
    });

    $("#attEnableFlexibleWorkEx").click(function(){
        attEnableFlexibleWorkExClick();
    });

    $("#id_elasticWorkingHours").change(function(){
        $("#id_workingHours").val($(this).val());
    });

    /*启用弹性上班点击时数据回填以及界面变化*/
    function attEnableFlexibleWorkExClick() {
        var enable = $("#attEnableFlexibleWorkEx").prop("checked");
        // 皮肤切换兼容不自定义颜色
        // $(".attEnableFlexibleWorkClass").css("color", enable ? "#000" : "#999");
        // $(".attEnableFlexibleWorkClassEx").css("color", enable ? "#999" : "#000");
        $(".attEnableFlexibleWorkRequired").css("visibility", enable ? "visible" : "hidden");
        $(".attEnableFlexibleWorkRequiredEx").css("visibility", !enable ? "visible" : "hidden");
        /*启用弹性上班*/
        $("input[name='enableFlexibleWork']").val(enable);

        /*可提前上班*/
        $("#attAdvanceWorkMinutes").attr("readonly", !enable);
        var advanceWorkMinutes = "${(item.advanceWorkMinutes)!'0'}";
        $("#attAdvanceWorkMinutes").val(enable ? advanceWorkMinutes : '0');
        /*可延后下班*/
        $("#attDelayedWorkMinutes").attr("readonly", !enable);
        var delayedWorkMinutes = "${(item.delayedWorkMinutes)!'0'}";
        $("#attDelayedWorkMinutes").val(enable ? delayedWorkMinutes : '0');

        if (enable) {

            // $("#afterToWorkMinutes").val(delayedWorkMinutes);
            // $("#beforeOffWorkMinutes").val(advanceWorkMinutes);

            $("#attDelayedWorkMinutes").rules("add", {
                required: true
            });
            $("#attAdvanceWorkMinutes").rules("add", {
                required: true
            });
        } else {

            $("#attAdvanceWorkMinutes").rules("remove", "required");
            $("#attAdvanceWorkMinutes").valid();
            $("#attDelayedWorkMinutes").rules("remove", "required");
            $("#attDelayedWorkMinutes").valid();
        }
    }

    //添加休息时间段
    function addTr(table, row, workingHours, showTimer){
        var trId = $("#breakTimeTable").find("tr:last").attr("id");
        var newNumber = 0;
        if(trId) {
            var numberId = trId.charAt(trId.length-1);
            newNumber = parseInt(numberId)+1;
        }
        var newTrId = "segmentDeduction" + newNumber;
        var startId  = "segmentDeductionStart" + newNumber;
        var endId  = "segmentDeductionEnd" + newNumber;
        let trhtml="<tr id="+newTrId+">\n" +
            "                            <th style=\"width: 128px;\">\n" +
            "                                <label><@i18n 'att_breakTime_startTime'/></label><span class=\"required\">*</span>\n" +
            "                            </th>\n" +
            "                            <td>\n" +
            "                                <div id="+startId+" onkeyup=\"narmolWorkingHours()\"></div>\n" +
            "                            </td>\n" +
            "                              <th style=\"width: 128px;\">\n"+
            "                                   <label><@i18n 'att_breakTime_endTime'/></label><span class=\"required\">*</span>\n"+
            "                            </th>\n" +
            "                            <td>\n" +
            "                                <div id="+endId+" onkeyup=\"narmolWorkingHours()\"></div>\n" +
            "                            </td>\n" +
            "                            <td>\n" +
            "                                <img class=\"addAttBreakTime\" onclick='addTr(\"breakTimeTable\",-1, true, true)' src='/images/add.png'>\n" +
            "                            </td>\n" +
            "                            <td>\n" +
            "                                <img class=\"delAttBreakTime\" onclick='delTr(\"breakTimeTable\",this)' src='/images/del.png'>\n" +
            "                            </td>\n" +
            "                        </tr>";

        //获取table最后一行 $("#tab tr:last")
        //获取table第一行 $("#tab tr").eq(0)
        //获取table倒数第二行 $("#tab tr").eq(-2)
        //这里是-1为最后一行，倒数第一行
        let $tr=$("#"+table+" tr").eq(row);
        // tr总行数
        let allTr=$("#"+table+" tr");
        if($tr.size()===0){
            $("#"+table+" tbody").html(trhtml);
        }else{
            if(allTr.size()=== 4){
                messageBox({messageType: "alert",text: '<@i18n "att_timeSlot_mostThree"/>'});
            }else{
                $tr.after(trhtml);
            }
        }
        //初始化时间
        if (showTimer) {
            attTimeSlotShowTimer(startId, startId, "12:00");
            attTimeSlotShowTimer(endId,  endId,  "13:00");
        }
        if (workingHours) {
            narmolWorkingHours();
        }
    }

    //一开始初始化休息时间段的值
    var breakTimeData = $("#id_breakTimeData").val();
    if(breakTimeData != null && breakTimeData != ''){
        var breakTimeObjList =  JSON.parse(breakTimeData);
        for(i = 0; i < breakTimeObjList.length; i++) {
            addTr("breakTimeTable",-1, false, false);
        }
        for(var i in breakTimeObjList){
            var startTimeId = "segmentDeductionStart"+i;
            var endTimeId = "segmentDeductionEnd"+i;
            var startTime = breakTimeObjList[i].startTime;
            var endTime = breakTimeObjList[i].endTime;
            attTimeSlotShowTimer(startTimeId,startTimeId, startTime);
            attTimeSlotShowTimer(endTimeId,endTimeId, endTime);
        }
    }
    //删除休息时间段
    function delTr(table,$this){
        //如果只有一条就不能继续删了
        let $tr=$("#"+table+" tr");
        if($tr.size()===2){
            messageBox({messageType: "alert",text: '<@i18n "att_timeSlot_atLeastOne"/>'});
            return false;
        }
        $($this).parent().parent().remove();
        narmolWorkingHours();
    }


    /*“可提前上班”与“上班前”验证*/
    var advanceWorkMinutesValidMsg = '';
    jQuery.validator.addMethod("advanceWorkMinutesValid", function (value, element) {
        var valid = true;
        var advanceWorkMinutes = $("#attAdvanceWorkMinutes").val();           //弹性：可提前上班
        var beforeToWorkMinutes = Number($("#beforeToWorkMinutes").val());            //上班前N分钟签到有效
        var beforeWorkOvertimeMinutes = $("#beforeWorkOvertimeMinutes").val();//上班N分钟前签到记加班
        var afterOffWorkMinutes = Number($("#afterOffWorkMinutes").val());

        if (Number(advanceWorkMinutes) > Number(beforeToWorkMinutes)) {
            if (element.id == "beforeToWorkMinutes") {
                /*“上班前”的分钟数要大于“可提前上班”的分钟数*/
                advanceWorkMinutesValidMsg = I18n.getValue("att_timeSlot_advanceWorkMinutesValidMsg1");
                valid = false;
            } else if(element.id == "attAdvanceWorkMinutes")
            {
                /*“可提前上班”的分钟数要小于“上班前”的分钟数*/
                advanceWorkMinutesValidMsg = I18n.getValue("att_timeSlot_advanceWorkMinutesValidMsg2");
                valid = false;
            }
        }
        // 下班前N分钟签到有效
        var beforeOffWorkMinutes = $("#beforeOffWorkMinutes").val();
        if (Number(advanceWorkMinutes) > Number(beforeOffWorkMinutes)) {
            if ("beforeOffWorkMinutes" == element.id) {
                // 下班前签到有效分钟数 要大于 可提前上班分钟数
                advanceWorkMinutesValidMsg = I18n.getValue("att_timeSlot_advanceWorkMinutesValidMsg5");
                valid = false;
            } else if ("attAdvanceWorkMinutes" == element.id) {
                // 可提前上班分钟数 要小于 下班前签到有效分钟数
                advanceWorkMinutesValidMsg = I18n.getValue("att_timeSlot_advanceWorkMinutesValidMsg6");
                valid = false;
            }
        }

        if($("#beforeToWorkAsOvertime").prop("checked") && Number(advanceWorkMinutes) > Number(beforeWorkOvertimeMinutes))//弹性提前上班时间要小于等于提前记加班时长
        {
        	if (element.id == "attAdvanceWorkMinutes")
        	{
        		/*'上班前签到记加班'的分钟数要大于‘可提前上班'的分钟数*/
        		advanceWorkMinutesValidMsg = I18n.getValue("att_timeSlot_advanceWorkMinutesValidMsg3");
        		valid = false;
        	} else if(element.id == "beforeWorkOvertimeMinutes")
        	{
                /*'可提前上班'的分钟数要小于‘上班前签到记加班'的分钟数*/
                advanceWorkMinutesValidMsg = I18n.getValue("att_timeSlot_advanceWorkMinutesValidMsg4");
                valid = false;
            }
        }

        if (element.id == "beforeToWorkMinutes") {
            var toWorkTime = Number($("#toWorkTime_HH").val())*60 + Number($("#toWorkTime_MM").val());
            var offWorkTime = Number($("#offWorkTime_HH").val())*60  + Number($("#offWorkTime_MM").val());
            // 上班前开始签到时间不能和下班后结束签退时间重叠,即同个时段最大时长不能超过24小时
            if ((offWorkTime + afterOffWorkMinutes) >= (toWorkTime - beforeToWorkMinutes + 60*24)) {
                advanceWorkMinutesValidMsg = I18n.getValue("att_timeSlot_advanceWorkMinutesValidMsg7");
                valid = false;
            }
        }

        return valid;
    }, function () {
        return advanceWorkMinutesValidMsg;
    });

    /*“可延后上班”与“下班后”验证"*/
    var delayedWorkMinutesValidMsg = '';
    jQuery.validator.addMethod("delayedWorkMinutesValid", function(value, element){
        var valid = true;
        var delayedWorkMinutes = $("#attDelayedWorkMinutes").val();
        var afterOffWorkMinutes = Number($("#afterOffWorkMinutes").val());
        var afterWorkOvertimeMinutes = $("#afterWorkOvertimeMinutes").val();  //下班N分钟后签退记加班
        var beforeToWorkMinutes = Number($("#beforeToWorkMinutes").val());

        if (Number(delayedWorkMinutes) > Number(afterOffWorkMinutes)) {
            if (element.id == "afterOffWorkMinutes") {
                /*“下班后”的分钟数要大于“可延后上班”的分钟数*/
                delayedWorkMinutesValidMsg = I18n.getValue("att_timeSlot_delayedWorkMinutesValidMsg1");
                valid = false;
            } else if(element.id == "attDelayedWorkMinutes")
            {
                /*“可延后上班”的分钟数要小于“下班后”的分钟数*/
                delayedWorkMinutesValidMsg = I18n.getValue("att_timeSlot_delayedWorkMinutesValidMsg2");
                valid = false;
            }
        }
        // 上班后N分钟前签到有效
        var afterToWorkMinutes = $("#afterToWorkMinutes").val();
        if (Number(delayedWorkMinutes) > Number(afterToWorkMinutes)) {
            if ("afterToWorkMinutes" == element.id) {
                // 上班后签到有效分钟数 要大于 可延后上班分钟数
                delayedWorkMinutesValidMsg = I18n.getValue("att_timeSlot_delayedWorkMinutesValidMsg5");;
                valid = false;
            } else if(element.id == "attDelayedWorkMinutes")
            {
                // 延后上班分钟数 要小于 上班后签到有效分钟数
                delayedWorkMinutesValidMsg = I18n.getValue("att_timeSlot_delayedWorkMinutesValidMsg6");
                valid = false;
            }
        }

        if($("#afterOffWorkAsOvertime").prop("checked") && Number(delayedWorkMinutes) > Number(afterWorkOvertimeMinutes))//弹性延后上班时间要小于等于下班N分钟后记加班时长
        {
        	if (element.id == "attDelayedWorkMinutes")
        	{
        		/*'下班后签退开始记加班'的分钟数要大于‘可延后上班'的分钟数*/
        		delayedWorkMinutesValidMsg = I18n.getValue("att_timeSlot_delayedWorkMinutesValidMsg3");
        		valid = false;
        	} else if(element.id == "afterWorkOvertimeMinutes")
        	{
                /*‘可延后上班'的分钟数'要小于‘下班后签退开始记加班'的分钟数*/
                delayedWorkMinutesValidMsg = I18n.getValue("att_timeSlot_delayedWorkMinutesValidMsg4");
                valid = false;
            }
        }

        if (element.id == "afterOffWorkMinutes") {
            var toWorkTime = Number($("#toWorkTime_HH").val())*60 + Number($("#toWorkTime_MM").val());
            var offWorkTime = Number($("#offWorkTime_HH").val())*60  + Number($("#offWorkTime_MM").val());
            // 下班后结束签退时间不能和上班前开始签到时间重叠,即同个时段最大时长不能超过24小时
            if ((offWorkTime + afterOffWorkMinutes) >= (toWorkTime - beforeToWorkMinutes + 60*24)) {
                delayedWorkMinutesValidMsg = I18n.getValue("att_timeSlot_delayedWorkMinutesValidMsg7");
                valid = false;
            }
        }

        return valid;
    },function(){
        return delayedWorkMinutesValidMsg;
    });

    /*“允许迟到分钟数”与“上班后”验证*/
    var allowLateMinutesValidMsg = '';
    jQuery.validator.addMethod("allowLateMinutesValid", function(value, element){
        var valid = true;
//        var beforeOffWorkMinutes = $("#beforeOffWorkMinutes").val();
		var afterToWorkMinutes = $("#afterToWorkMinutes").val();
        var allowLateMinutes = $("#id_allowLateMinutes").val();
        if (Number(allowLateMinutes) > Number(afterToWorkMinutes)) {
            if (element.id == "id_allowLateMinutes") {
                /*“允许迟到分钟数”要小于“上班后”的分钟数*/
                allowLateMinutesValidMsg = I18n.getValue("att_timeSlot_allowLateMinutesValidMsg1");
            } else {
                /*“上班后”的分钟数要大于“允许迟到分钟数”的分钟数*/
                allowLateMinutesValidMsg = I18n.getValue("att_timeSlot_allowLateMinutesValidMsg2");
            }
            valid = false;
        }
        return valid;
    },function(){
        return allowLateMinutesValidMsg;
    });

    /*“允许早退分钟数”与“下班前”验证*/
    var allowEarlyMinutesValidMsg = '';
    jQuery.validator.addMethod("allowEarlyMinutesValid", function(value, element){
        var valid = true;
        //var afterToWorkMinutes = $("#afterToWorkMinutes").val();
        var beforeOffWorkMinutes = $("#beforeOffWorkMinutes").val();
        var allowEarlyMinutes = $("#id_allowEarlyMinutes").val();
        if (Number(allowEarlyMinutes) > Number(beforeOffWorkMinutes)) {
            if (element.id == "id_allowEarlyMinutes") {
                /*"允许早退分钟数"要小于"下班前"的分钟数*/
                allowEarlyMinutesValidMsg = I18n.getValue("att_timeSlot_allowEarlyMinutesValidMsg1");
            } else {
                /*"下班前"的分钟要大于"允许早退分钟数"的分钟数*/
                allowEarlyMinutesValidMsg = I18n.getValue("att_timeSlot_allowEarlyMinutesValidMsg2");
            }
            valid = false;
        }
        return valid;
    },function(){
        return allowEarlyMinutesValidMsg;
    });

    /**上班后签到有效分钟数校验*/
    var afterToWorkMinutesValidMsg = "";
    jQuery.validator.addMethod("afterToWorkMinutesValid", function(value, element) {
        var afterToWorkMinutes = $("#afterToWorkMinutes").val();

        var allowLateMinutes = $("#id_allowLateMinutes").val();
        var delayedWorkMinutes = $("#attDelayedWorkMinutes").val();

        if (Number(afterToWorkMinutes) < Number(allowLateMinutes)) {
            /*“上班后”的分钟数要大于“允许迟到分钟数”的分钟数*/
            afterToWorkMinutesValidMsg = I18n.getValue("att_timeSlot_allowLateMinutesValidMsg2");
            return false;
        }

        if (Number(afterToWorkMinutes) < Number(delayedWorkMinutes)) {
            // 上班后签到有效分钟数 要大于 可延后上班分钟数
            afterToWorkMinutesValidMsg = I18n.getValue("att_timeSlot_delayedWorkMinutesValidMsg5");
            return false;
        }

        return true;
    }, function() {
        return afterToWorkMinutesValidMsg;
    });

    /** 下班前签到有效分钟数校验 */
    var beforeOffWorkMinutesValidMsg = "";
    jQuery.validator.addMethod("beforeOffWorkMinutesValid", function(value, element) {
        var valid = true;
        var beforeOffWorkMinutes = $("#beforeOffWorkMinutes").val();
        var allowEarlyMinutes = $("#id_allowEarlyMinutes").val();
        if (Number(beforeOffWorkMinutes) < Number(allowEarlyMinutes)) {
            /*"下班前"的分钟要大于"允许早退分钟数"的分钟数*/
            beforeOffWorkMinutesValidMsg = I18n.getValue("att_timeSlot_allowEarlyMinutesValidMsg2");
            return false;
        }

        var advanceWorkMinutes = $("#attAdvanceWorkMinutes").val();
        if (Number(beforeOffWorkMinutes) < Number(advanceWorkMinutes)) {
            // 下班前签到有效分钟数 要大于 可提前上班分钟数
            beforeOffWorkMinutesValidMsg = I18n.getValue("att_timeSlot_advanceWorkMinutesValidMsg5");
            return false;
        }

        return valid;
    }, function() {
        return beforeOffWorkMinutesValidMsg;
    });

    /*上班前、下班前、上班记加班、可提前上班、允许早退分钟数*/
    function validBefore() {
        $("#beforeToWorkMinutes").valid();/*上班前*/
        $("#beforeOffWorkMinutes").valid();/*下班前*/
        $("#beforeWorkOvertimeMinutes").valid();/*上班记加班*/
        $("#attAdvanceWorkMinutes").valid();/*可提前上班*/
        $("#id_allowEarlyMinutes").valid();/*允许早退分钟数*/
    }
    /*上班前*/
    $("#beforeToWorkMinutes").keyup(function() {
        validBefore()
    });
    /*下班前*/
    $("#beforeOffWorkMinutes").keyup(function() {
        validBefore()
    });
    /*上班记加班*/
    $("#beforeWorkOvertimeMinutes").keyup(function() {
        validBefore()
    });
    /*可提前上班*/
    $("#attAdvanceWorkMinutes").keyup(function() {
        validBefore()
    });
    /*允许早退分钟数*/
    $("#id_allowEarlyMinutes").keyup(function() {
        validBefore()
    });


    /*上班后、下班后、下班记加班、可延后上班、允许迟到分钟数*/
    function validAfter() {
        $("#afterToWorkMinutes").valid();/*上班后*/
        $("#afterOffWorkMinutes").valid();/*下班后*/
        $("#afterWorkOvertimeMinutes").valid();/*下班记加班*/
        $("#attDelayedWorkMinutes").valid();/*可延后上班*/
        $("#id_allowLateMinutes").valid();/*允许迟到分钟数*/
    }
    /*上班后*/
    $("#afterToWorkMinutes").keyup(function() {
        validAfter()
    });
    /*下班后*/
    $("#afterOffWorkMinutes").keyup(function() {
        validAfter()
    });
    /*下班记加班*/
    $("#afterWorkOvertimeMinutes").keyup(function() {
        validAfter()
    });
    /*可延后上班*/
    $("#attDelayedWorkMinutes").keyup(function() {
        validAfter()
    });
    /*允许迟到分钟数*/
    $("#id_allowLateMinutes").keyup(function() {
        validAfter()
    });

	//上班后、下班前的有效分钟数判断，确保上下班取卡时间不冲突
	function longTimeValid()
	{
		var validTag = true;
		var afterWork = $("#afterToWorkMinutes").val();//上班后
    	var beforeWork = $("#beforeOffWorkMinutes").val();//下班前
    	var longTime = countWorkingHours(0);
    	if(longTime <= (parseInt(afterWork) + parseInt(beforeWork)))
    	{
    		validTag = false;
    	}
    	return validTag;
	}

    $(function(){
        //时间段类型change事件
        ZKUI.Combo.get("id_periodType").combo.attachEvent("onChange",function(value,text){
            changePeriodType(value);
        });
        //是否段间扣除初始化
        var isSegmentDeduction = ZKUI.Combo.get("id_isSegmentDeduction").combo.getSelected();
        if(isSegmentDeduction == "true"){
            $('[id^="segmentDeduction"]').show();
        }else{
            $('[id^="segmentDeduction"]').hide();
        }
        //是否段间扣除change事件
        ZKUI.Combo.get("id_isSegmentDeduction").combo.attachEvent("onChange",function(value,text){
            if(value == "true"){
                if($('[id^="segmentDeduction"]').length == 0) {
                    addTr("breakTimeTable",-1, false, true);
                }
                $('[id^="segmentDeduction"]').show();
            }else{
                $('[id^="segmentDeduction"]').hide();
            }
            narmolWorkingHours();
        });

        $("#${formId}").validate({
            debug: true,
            rules: {
                "periodName": {
                    required: true,
                    unInputChar: true,
                    overRemote: ["attTimeSlot.do?validName", "${(item.periodName)!}"]
                },
                "beforeToWorkMinutes": { /*上班前*/
                    digits : true,
                    required: true,
                    advanceWorkMinutesValid: true
                },
                "afterToWorkMinutes": { /*上班后*/
                    digits : true,
                    required: true,
                    afterToWorkMinutesValid: true
                },
                "beforeOffWorkMinutes": { /*下班前*/
                    digits : true,
                    required: true,
                    beforeOffWorkMinutesValid: true
                },
                "afterOffWorkMinutes": { /*下班后*/
                    digits : true,
                    required: true,
                    delayedWorkMinutesValid: true
                },
                "allowLateMinutes": { /*允许迟到分钟数*/
                    digits : true,
                    required: true,
                    allowLateMinutesValid: true
                },
                "allowEarlyMinutes": { /*允许早退分钟数*/
                    digits : true,
                    required: true,
                    allowEarlyMinutesValid: true
                },
                "beforeWorkOvertimeMinutes": { /*上班xx分钟前签到记加班*/
                    digits : true,
                    advanceWorkMinutesValid:true
                },
                "minBeforeOvertimeMinutes": { /* 最短加班分钟数*/
                    digits : true
                },
                "afterWorkOvertimeMinutes": { /*下班xx分钟前签到记加班*/
                    digits : true,
                    delayedWorkMinutesValid: true
                },
                "minAfterOvertimeMinutes": { /* 最短加班分钟数*/
                    digits : true
                },
                "advanceWorkMinutes": { /*可提前上班*/
                    digits : true,
                    required: true,
                    advanceWorkMinutesValid: true
                },
                "delayedWorkMinutes": { /*可延后上班*/
                    digits : true,
                    required: true,
                    delayedWorkMinutesValid: true
                },
                "maxBeforeOvertimeMinutes": {
                    digits : true,
                    required: true
                },
                "maxAfterOvertimeMinutes": {
                    digits : true,
                    required: true
                }
            },
            messages:{

            },
            submitHandler: function() {

                // 上班时间不能等于下班时间
                var toWorkTime = $("input[name='toWorkTime']").val();
                var offWorkTime = $("input[name='offWorkTime']").val();
                if (toWorkTime == offWorkTime) {
                    messageBox({messageType: "alert",text: "<@i18n 'att_timeSlot_workTimeNotEqual'/>"});
                    return false;
                }

                //是否段间扣除
                var isSegmentDeduction = ZKUI.Combo.get("id_isSegmentDeduction").combo.getSelected();
                var breakTimeArray = [];
                //开启段间扣除才进行时间段是否重合校验
                if (isSegmentDeduction == "true") {

                    //获取休息时间段数据
                    var startEqualEnd = false;
                    $('[id^="segmentDeductionStart"]').each(function(){
                        var breakTimeObject = {};
                        var trId = $(this).attr("id");
                        var number = trId.charAt(trId.length-1);
                        if(isNaN(number)){
                           return;
                        }
                        var startTimeHH = "segmentDeductionStart"+number+"_HH";
                        var startTimeMM = "segmentDeductionStart"+number+"_MM";
                        var endTimeHH = "segmentDeductionEnd"+number+"_HH";
                        var endTimeMM = "segmentDeductionEnd"+number+"_MM";
                        var startTime = $("#"+startTimeHH).val() + ":" + $("#"+startTimeMM).val();
                        var endTime = $("#"+endTimeHH).val() + ":" + $("#"+endTimeMM).val();
                        breakTimeObject["startTime"] =  startTime;
                        breakTimeObject["endTime"] =  endTime;
                        if( startTime == endTime){
                            startEqualEnd = true;
                        }
                        breakTimeArray.push(breakTimeObject)
                    });
                    //判断开始时间和结束时间是否相等
                    if(startEqualEnd){
                        messageBox({messageType: "alert",text: "<@i18n 'att_timeSlot_canNotEqual'/>"});
                        return false;
                    }

                    //判断时间段是否重复
                    var sameTime = false;
                    for (var i = 0; i < breakTimeArray.length; i++) {
                        for (var j = i + 1; j < breakTimeArray.length; j++) {
                            if (breakTimeArray[i].startTime == breakTimeArray[j].startTime && breakTimeArray[i].endTime==breakTimeArray[j].endTime) {
                                sameTime = true;
                                break;
                            }
                        }
                        if (sameTime) break;
                    }
                    if(sameTime){
                        messageBox({messageType: "alert",text: "<@i18n 'att_timeSlot_repeatBreakTime'/>"});
                        return false;
                    }

                    $("#id_breakTimeData").val(JSON.stringify(breakTimeArray));

                    // 判断休息时间段是否在工作时间范围内
                    var ret = true;
                    var breakTimeListStr = JSON.stringify(breakTimeArray)
                    $.ajax({
                        data: {
                            breakTimeListStr:breakTimeListStr,
                            toWorkTime: $("#toWorkTime_HH").val() + ":" + $("#toWorkTime_MM").val(),
                            offWorkTime: $("#offWorkTime_HH").val() + ":" + $("#offWorkTime_MM").val()
                        },
                        dataType: "json",
                        type: "post",
                        async :false,
                        url: "attTimeSlot.do?breakTimeIsInWorkTime",
                        success: function(data) {
                            if (data.data == 'false') {
                                ret = false;

                            }
                        }
                    });
                    if (!ret) {
                        messageBox({messageType: "alert",text: "<@i18n 'att_timeSlot_shoudInWorkTime'/>"});
                        return false;
                    }
                }

                var periodType = ZKUI.Combo.get("id_periodType").combo.getSelected();
                if (periodType == 0) {
                	//判断上下班取卡时间是否重叠
                    var validTag = longTimeValid();
                    if(!validTag)
                    {
                    	messageBox({messageType: "alert",text: "<@i18n 'att_timeSlot_longTimeValidRemark'/>"});
                        return false;
                    }

                    var timeParamObjJson = {
                        toWorkTimeJoint: $("#toWorkTime_HH").val() + $("#toWorkTime_MM").val(),//上班时间
                        offWorkTimeJoint: $("#offWorkTime_HH").val() + $("#offWorkTime_MM").val(),//下班时间
                    };
                    //是否跨天
                    var isCrossDay = parseInt(timeParamObjJson.toWorkTimeJoint) >= parseInt(timeParamObjJson.offWorkTimeJoint) ? true : false;
                    //是否段间扣除
                    var isSegmentDeduction = ZKUI.Combo.get("id_isSegmentDeduction").combo.getSelected();
                    if (startSignInAndMustSignOffVerify(isCrossDay, isSegmentDeduction, timeParamObjJson)) {
                        if (workingHoursVerify()) {
                            if (isAdvanceCountOvertimeVerify(isCrossDay, timeParamObjJson)) {
                                if (isPostponeCountOvertimeVerify(isCrossDay, timeParamObjJson)) {
                                    <@submitHandler/>
                                }
                            }
                        }
                    }
                } else {

                    <@submitHandler/>
                }
            }
        });


        /*初始化或回显正常时间段属性*/
        initOrEchoNormalTimeProperties();

        /*编辑数据回填以及界面变化*/
        attEnableFlexibleWorkExClick();
    });


    /*
    ******************
    * 事件定义
    ******************
    */
    //初始化或回显正常时间段属性
    function initOrEchoNormalTimeProperties() {

        if ($("#elastic_startSignInTime_hidden").length > 0) {
            $("#elastic_startSignInTime_hidden").attr("disabled", true);
        }
        //初始化上班时间
        attTimeSlotShowTimer("toWorkTime", "toWorkTime", normalTimeObjJson.toWorkTime);
        //初始化下班时间
        attTimeSlotShowTimer("offWorkTime", "offWorkTime", normalTimeObjJson.offWorkTime);

        //是否段间扣除可编辑
        ZKUI.Combo.get("id_isSegmentDeduction").combo.disable(normalTimeObjJson.isSegmentDeduction);
        //段间扣除
        $("#id_interSegmentDeduction").val(normalTimeObjJson.interSegmentDeduction);
        if ($("#elastic_endSignOffTime_hidden").length > 0) {
            $("#elastic_endSignOffTime_hidden").attr("disabled", true);
        }
        //允许迟到分钟数
        $("#id_allowLateMinutes").val(normalTimeObjJson.allowLateMinutes);
        //是否必须签到
        ZKUI.Combo.get("id_isMustSignIn").combo.disable(normalTimeObjJson.isMustSignIn);
        //允许早退分钟数
        $("#id_allowEarlyMinutes").val(normalTimeObjJson.allowEarlyMinutes);
        //是否必须签退
        ZKUI.Combo.get("id_isMustSignOff").combo.disable(normalTimeObjJson.isMustSignIn);
        //记为工作日数
        $("#id_markWorkingDays").val(normalTimeObjJson.markWorkingDays);
        //工作时长
        $("#id_workingHours").val(normalTimeObjJson.workingHours);

        $("#beforeToWorkMinutes").val(normalTimeObjJson.beforeToWorkMinutes);
        $("#afterToWorkMinutes").val(normalTimeObjJson.afterToWorkMinutes);
        $("#beforeOffWorkMinutes").val(normalTimeObjJson.beforeOffWorkMinutes);
        $("#afterOffWorkMinutes").val(normalTimeObjJson.afterOffWorkMinutes);
        //加班设置
        $("#beforeWorkOvertimeMinutes").val(normalTimeObjJson.beforeWorkOvertimeMinutes);
        $("#minBeforeOvertimeMinutes").val(normalTimeObjJson.minBeforeOvertimeMinutes);
        $("#afterWorkOvertimeMinutes").val(normalTimeObjJson.afterWorkOvertimeMinutes);
        $("#minAfterOvertimeMinutes").val(normalTimeObjJson.minAfterOvertimeMinutes);

        changeAdvanceCountOvertime(normalTimeObjJson.isAdvanceCountOvertime);
        changePostponeCountOvertime(normalTimeObjJson.isPostponeCountOvertime);

        $("#attEnableFlexibleWorkEx").attr("checked", normalTimeObjJson.enableFlexibleWork == "true" ? true : false);
    }

    /**
     * 提前记加班
     * @param value
     */
    function changeAdvanceCountOvertime(value){
        $("#beforeToWorkAsOvertime").attr("checked", value ? true : false);
        // 皮肤切换兼容不自定义颜色
        // $(".beforeToWorkAsOvertimeClass").css("color", value ? "#000" : "#999");
        $(".beforeToWorkAsOvertimeClass input").attr("readonly",value ? false : true);
        $("#isAdvanceCountOvertime").val(value);
    }

    /**
     * 延后记加班
     * @param value
     */
    function changePostponeCountOvertime(value){
        $("#afterOffWorkAsOvertime").attr("checked", value ? true : false);
        // 皮肤切换兼容不自定义颜色
        // $(".afterOffWorkAsOvertimeClass").css("color", value ? "#000" : "#999");
        $(".afterOffWorkAsOvertimeClass input").attr("readonly",!value);
        $("#isPostponeCountOvertime").val(value);
    }

    //时间的校验处理
    function startSignInAndMustSignOffVerify (isCrossDay, isSegmentDeduction, timeParamObjJson) {
        var startSignOffGreater = true;
        var offWorkTimeGreater = true;
        var startTimeGreater = true;
        var endSignoutTimeGreater = true;
        if (isCrossDay) {
            //正常时间段-跨天
            //开始签到时间要小于上班时间
            startTimeGreater = judgeLeftRightSize(true, timeParamObjJson.startSignInTimeJoint, timeParamObjJson.toWorkTimeJoint, "<@i18n 'att_timeSlot_alertStartSignInTime'/>");
            //判断是否是段间扣除
            if (isSegmentDeduction == "false") {
                //如果结束签到时间大于开始签退时间
                if (parseInt(timeParamObjJson.endSignInTimeJoint) > parseInt(timeParamObjJson.startSignOffTimeJoint)) {
                    //上班时间要小于结束签到时间
                    var endTimeGreater = judgeLeftRightSize(startTimeGreater, timeParamObjJson.toWorkTimeJoint, timeParamObjJson.endSignInTimeJoint, "<@i18n 'att_timeSlot_alertEndSignInTime'/>");
                    //开始签退时间要小于下班时间
                    offWorkTimeGreater = judgeLeftRightSize(endTimeGreater, timeParamObjJson.startSignOffTimeJoint, timeParamObjJson.offWorkTimeJoint, "<@i18n 'att_timeSlot_alertStartSignOffTime'/>");
                } else {
                    //开始签退时间大于上班时间，就靠左边，上班时间要小于结束签到时间；否则，靠右边，则开始签退时间小于下班时间
                    if(parseInt(timeParamObjJson.startSignOffTimeJoint) > parseInt(timeParamObjJson.toWorkTimeJoint)) {
                        var endTimeGreater = judgeLeftRightSize(startTimeGreater, timeParamObjJson.toWorkTimeJoint, timeParamObjJson.endSignInTimeJoint, "<@i18n 'att_timeSlot_alertEndSignInTime'/>");
                        offWorkTimeGreater = endTimeGreater;
                    } else {
                        offWorkTimeGreater = judgeLeftRightSize(startTimeGreater, timeParamObjJson.startSignOffTimeJoint, timeParamObjJson.offWorkTimeJoint, "<@i18n 'att_timeSlot_alertStartSignOffTime'/>");
                    }
                }
            }
            //下班时间要小于结束签退时间
            endSignoutTimeGreater = judgeLeftRightSize(offWorkTimeGreater, timeParamObjJson.offWorkTimeJoint, timeParamObjJson.endSignOffTimeJoint, "<@i18n 'att_timeSlot_alertEndSignOffTime'/>");
        } else {
            //正常时间段-不跨天
            //上班时间要小于结束签到时间
            var endTimeGreater = judgeLeftRightSize(startTimeGreater, timeParamObjJson.toWorkTimeJoint, timeParamObjJson.endSignInTimeJoint, "<@i18n 'att_timeSlot_alertEndSignInTime'/>");
            //判断是否是段间扣除
            if (isSegmentDeduction == "false") {
                //结束签到时间要小于开始签退时间
                startSignOffGreater = judgeLeftRightSize(endTimeGreater, timeParamObjJson.endSignInTimeJoint, timeParamObjJson.startSignOffTimeJoint, "<@i18n 'att_timeSlot_alertStartSignInAndEndSignIn'/>");
            } else {
                startSignOffGreater = endTimeGreater;
            }
            //开始签退时间要小于下班时间
            offWorkTimeGreater = judgeLeftRightSize(startSignOffGreater, timeParamObjJson.startSignOffTimeJoint, timeParamObjJson.offWorkTimeJoint, "<@i18n 'att_timeSlot_alertStartSignOffTime'/>");
            endSignoutTimeGreater = offWorkTimeGreater;
        }
        return endSignoutTimeGreater;
    }

    //提前是否计加班的校验处理
    function isAdvanceCountOvertimeVerify(isCrossDay, timeParamObjJson) {
        //提前是否计加班
        var isAdvanceCountOvertime = $("#isAdvanceCountOvertime").val();
        if (isAdvanceCountOvertime == "true") {
            //签到早于时间
            //签到早于时间要大于开始签到时间
            //签到早于时间要小于上班班时间
            return judgeLeftRightSize(true, $("#beforeWorkOvertimeMinutes").val(), $("#beforeToWorkMinutes").val(), I18n.getValue("att_timeSlot_alertMoreSignInAdvanceTime"))
        }else {
            return true;
        }
    }

    //延后是否计加班的校验处理
    function isPostponeCountOvertimeVerify(isCrossDay, timeParamObjJson) {
        //延后是否计加班
        var isPostponeCountOvertime = $("#isPostponeCountOvertime").val();
        if (isPostponeCountOvertime == "true") {
            //签退晚于时间
            //签退晚于时间要小于结束签退时间
            //签退晚于时间要大于下班时间
            return judgeLeftRightSize(true, $("#afterWorkOvertimeMinutes").val(), $("#afterOffWorkMinutes").val(), I18n.getValue("att_timeSlot_alertMoreSignOutPosponeTime"));
        }else {
            return true;
        }
    }

    //判断左边大于右边提示消息
    function judgeLeftRightSize(judgeBoole, left, right, text) {
        //用于判断是否依次执行提示框
        if (judgeBoole) {
            if (parseInt(left) > parseInt(right)) {
                messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: text});
                return false;
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    //工作时长校验
    function workingHoursVerify () {
        var workingHours = $("#id_workingHours").val();
        var baseRuleShortestMinutes = $("#id_baseRuleShortestMinutes").val();
        var baseRuleLongestMinutes = $("#id_baseRuleLongestMinutes").val();
        if (parseInt(workingHours) >= parseInt(baseRuleShortestMinutes) && parseInt(workingHours) <= parseInt(baseRuleLongestMinutes)) {
            return true;
        } else {
            //时间段时长介于
            messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'att_timeSlot_timeSlotLong'/>" + baseRuleShortestMinutes + "-" + baseRuleLongestMinutes});
            return false;
        }
    }

    //计算段间扣除时长
    function countSegmentTime() {
        var isSegmentDeduction = ZKUI.Combo.get("id_isSegmentDeduction").combo.getSelected();
        if (isSegmentDeduction == "true") {

            var timeLong = 0;
            var breakTimeArray = getBreakTimeArray();
            if (breakTimeArray && breakTimeArray.length > 0 ) {
                $.ajax({
                    url: "attTimeSlot.do?countSegmentTime",
                    type: "POST",
                    async: false,
                    data: {
                        timeSlotId: timeSlotId,
                        breakTimeArray: JSON.stringify(breakTimeArray)
                    },
                    dataType: "json",
                    success: function (result) {
                        timeLong = result.data;
                    },
                    error: function (e) {
                    }
                });
                return timeLong;
            }
            else {
                return 0;
            }
        } else {
            return 0;
        }
    }


    // 获取休息时间段数据
    function getBreakTimeArray() {
        var breakTimeArray = [];
        $('[id^="segmentDeductionStart"]').each(function(){
            var breakTimeObject = {};
            var trId = $(this).attr("id");
            var number = trId.charAt(trId.length-1);
            if(isNaN(number)){
                return;
            }
            var startTimeHH = "segmentDeductionStart"+number+"_HH";
            var startTimeMM = "segmentDeductionStart"+number+"_MM";
            var endTimeHH = "segmentDeductionEnd"+number+"_HH";
            var endTimeMM = "segmentDeductionEnd"+number+"_MM";

            if ($("#"+startTimeHH).val() && $("#"+startTimeMM).val() && $("#"+endTimeHH).val() && $("#"+endTimeMM).val()) {
                var startTime = $("#"+startTimeHH).val().padStart(2, '0') + ":" + $("#"+startTimeMM).val();
                var endTime = $("#"+endTimeHH).val().padStart(2, '0') + ":" + $("#"+endTimeMM).val();
                breakTimeObject["startTime"] =  startTime;
                breakTimeObject["endTime"] =  endTime;
                breakTimeArray.push(breakTimeObject)
            }
        });
       return breakTimeArray;
    }

    //计算工作时长
    function countWorkingHours(segmentTime) {
        var workingHours;
        // 上班时间
        var toWorkTimeJoint = $("#toWorkTime_hidden").val().split(":");
        var toWorkTimeHour = isNaN(parseInt(toWorkTimeJoint[0])) ? 0 : parseInt(toWorkTimeJoint[0]);
        var toWorkTimeMinute = isNaN(parseInt(toWorkTimeJoint[1])) ? 0 : parseInt(toWorkTimeJoint[1]);;
        // 下班时间
        var offWorkTimeJoint = $("#offWorkTime_hidden").val().split(":");
        var offWorkTimeHour = isNaN(parseInt(offWorkTimeJoint[0])) ? 0: parseInt(offWorkTimeJoint[0]);
        var offWorkTimeMinute = isNaN(parseInt(offWorkTimeJoint[1])) ? 0: parseInt(offWorkTimeJoint[1]);

        var toWorkTimeTotal = (toWorkTimeHour * 60 + toWorkTimeMinute);
        var offWorkTimeTotal = (offWorkTimeHour * 60 + offWorkTimeMinute);
        if (toWorkTimeTotal < offWorkTimeTotal) {
            workingHours = offWorkTimeTotal - toWorkTimeTotal - segmentTime;
        } else {
            workingHours = 1440 - (toWorkTimeTotal - offWorkTimeTotal) - segmentTime;
        }
        return workingHours < 0 ? 0 : workingHours;
    }

    //正常工作时长
    function narmolWorkingHours() {
        var periodType = ZKUI.Combo.get("id_periodType").combo.getSelected();
        if (periodType == 0) {
            $("#id_workingHours").val(countWorkingHours(countSegmentTime()));
        }
    }

    //重写底层attTimeSlotCheckNum方法
    function attTimeSlotCheckNum(id, sign, value) {
        if (value) {
            value = isNaN(parseInt(value)) ? 0: parseInt(value);
            if (value < 10) {
                value = complete(value);
                getTag(id, "input", sign, "name").value = value;
            } else {
                if (value > eval(sign)) {
                    getTag(id, "input", sign, "name").value = eval(sign);
                }
            }
        } else {
            getTag(id, "input", sign, "name").value = "00";
        }
        setHiddenValue(id);
    }

    //重写底层showTimer方法
    function attTimeSlotShowTimer(tempId, hiddenName, timeStr) {
        var timeArray = timeStr.split(":");

        mode[tempId + "mode"] = "HH";
        var hourContent = "<input type=\"text\" maxlength=\"2\" style=\"width: 25px\" id=\"" + tempId + "_HH\" name=\"HH\""
            + " value=\"" + timeArray[0] + "\""
            + " onchange=\"attTimeSlotCheckNum('" + tempId + "', 'HH', this.value)\""
            + " onkeydown=\"keyDown('" + tempId + "', event.keyCode, this)\""
            + " onkeyup=\"keyUp('" + tempId + "', event.keyCode)\""
            + " onfocus=\"setMode('" + tempId + "', 'HH')\""
            + " onblur=\"bgBlur(this)\" />";
        var minuteContent = "<input type=\"text\" maxlength=\"2\" style=\"width: 25px\" id=\"" + tempId + "_MM\" name=\"MM\""
            + " value=\"" + timeArray[1] + "\""
            + " onchange=\"attTimeSlotCheckNum('" + tempId + "', 'MM', this.value)\""
            + " onkeydown=\"keyDown('" + tempId + "', event.keyCode, this)\""
            + " onkeyup=\"keyUp('" + tempId + "', event.keyCode)\""
            + " onfocus=\"setMode('" + tempId + "', 'MM')\""
            + " onblur=\"bgBlur(this)\" />";
        var hiddenContent = "<input id=\"" + tempId + "_hidden\" name=\"" + hiddenName + "\" type=\"hidden\" />";

        document.getElementById(tempId).innerHTML = hourContent + "&nbsp;:&nbsp;" + minuteContent + "（HH:MM）" + hiddenContent;
        setHiddenValue(tempId);
    }
</script>
</#macro>