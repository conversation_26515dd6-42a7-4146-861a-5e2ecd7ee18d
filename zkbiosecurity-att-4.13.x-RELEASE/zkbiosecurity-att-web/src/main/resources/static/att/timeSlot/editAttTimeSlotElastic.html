<#assign editPage = true>
<#include "/public/template/editTemplate.html">
<#macro editContent>
<form action="attTimeSlot.do?save" id="${formId}" method="post" enctype='multipart/form-data'>
    <input id="att_timeSlotId" name="id" type="hidden" value="${(item.id)!}" />
    <input name="periodType" type="hidden" value="${(item.periodType)!'1'}" />
    <input name="isSegmentDeduction" type="hidden" value="${(item.isSegmentDeduction)!'false'}" />
    <table class="tableStyle">
        <tr>
            <!-- 名称 -->
            <th><label><@i18n "common_name"/></label><span class="required">*</span></th>
            <td>
                <input maxlength="30" name="periodName" type="text" value="${(item.periodName)!}" />
            </td>
        </tr>
        <tr>
            <!-- 工作时长 -->
            <th>
                <label><@i18n "att_timeSlot_workingHours"/></label><span class="required">*</span>
                <span class="attTooltip">
                    <span class="att_icv att_icv-green icv-ic_que zk-colors-fg-green" />
                    <span class="attCommonTipClass">
                        <p class="warningColor"><@i18n 'att_timeSlot_elasticTip1'/></p>
                        <p class="warningColor"><@i18n 'att_timeSlot_elasticTip2'/></p>
                        <p class="warningColor"><@i18n 'att_timeSlot_elasticTip3'/></p>
                    </span>
                </span>
            </th>
            <td>
                <input maxlength="4" name="workingHours" type="text" value="${(item.workingHours)!0}"  onkeyup="this.value=this.value.replace(/\D/g,'');" onafterpaste="this.value=this.value.replace(/\D/g,'');"/>
            </td>
        </tr>
        <tr>
            <!-- 弹性时长计算方式 -->
            <th><label><@i18n 'att_rule_baseRuleElasticCal'/></label></th>
            <td>
                <@ZKUI.Combo id="att_baseRuleElasticCal" readonly="true" empty="false" value="${(item.elasticCal)!}" hideLabel="true" name="elasticCal" width="148">
                    <option value="0"><@i18n 'att_rule_baseRuleTwoPunch'/></option>
                    <option value="1"><@i18n 'att_rule_baseRuleStartEnd'/></option>
                </@ZKUI.Combo>
            </td>
        </tr>
        <tr>
            <!-- 开始签到时间 -->
            <th><label><@i18n "att_timeSlot_startSignInTime"/></label><span class="required">*</span></th>
            <td id="elastic_startSignInTime"></td>

        </tr>
        <tr>
            <!-- 签退结束时间 -->
            <th><label><@i18n "att_timeSlot_endSignOffTime"/></label><span class="required">*</span></th>
            <td id="elastic_endSignOffTime"></td>
        </tr>
    </table>
</form>

<script type="text/javascript">
    timeFormat = "HH:MM";
    var timeSlotId = $("#att_timeSlotId").val();
    var elasticTimeObjJson = {
        startSignInTime: timeSlotId ? "${(item.startSignInTime)!}" : "09:00",//开始签到时间
        endSignOffTime: timeSlotId ? "${(item.endSignOffTime)!}" : "18:00",//结束签退时间
    };

    $(function(){
        $("#${formId}").validate({
            debug: true,
            rules: {
                "periodName": {
                    required: true,
                    unInputChar: true,
                    overRemote: ["attTimeSlot.do?validName", "${(item.periodName)!}"]
                },
                 "workingHours": {
                    required: true,
                }
            },
            submitHandler: function() {

                // 开始签到时间不能等于结束签退时间
                var startSignInTime = $("input[name='startSignInTime']").val();
                var endSignOffTime = $("input[name='endSignOffTime']").val();
                if (startSignInTime == endSignOffTime) {
                    messageBox({messageType: "alert",text: "<@i18n 'att_timeSlot_signTimeNotEqual'/>"});
                    return false;
                }

                var maxWorkingHours = countWorkingHours(startSignInTime, endSignOffTime);
                var workingHours = $("input[name='workingHours']").val();
                if (workingHours > maxWorkingHours) {
                    messageBox({messageType: "alert",text: "<@i18n 'att_timeSlot_maxWorkingHours'/> " + maxWorkingHours});
                    return false;
                }

                <@submitHandler/>
            }
        });

        /*初始化或回显弹性时间段属性*/
        initOrEchoElasticTimeProperties()
    });

    //计算工作时长
    function countWorkingHours() {

        // 上班时间
        var toWorkTimeJoint = $("input[name='startSignInTime']").val().split(":");
        var toWorkTimeHour = isNaN(parseInt(toWorkTimeJoint[0])) ? 0 : parseInt(toWorkTimeJoint[0]);
        var toWorkTimeMinute = isNaN(parseInt(toWorkTimeJoint[1])) ? 0 : parseInt(toWorkTimeJoint[1]);;
        // 下班时间
        var offWorkTimeJoint = $("input[name='endSignOffTime']").val().split(":");
        var offWorkTimeHour = isNaN(parseInt(offWorkTimeJoint[0])) ? 0: parseInt(offWorkTimeJoint[0]);
        var offWorkTimeMinute = isNaN(parseInt(offWorkTimeJoint[1])) ? 0: parseInt(offWorkTimeJoint[1]);
        var toWorkTimeTotal = (toWorkTimeHour * 60 + toWorkTimeMinute);
        var offWorkTimeTotal = (offWorkTimeHour * 60 + offWorkTimeMinute);
        var maxWorkingHours = 0;
        if (toWorkTimeTotal < offWorkTimeTotal) {
            maxWorkingHours = offWorkTimeTotal - toWorkTimeTotal;
        } else {
            maxWorkingHours = 1440 - (toWorkTimeTotal - offWorkTimeTotal);
        }
        return maxWorkingHours < 0 ? 0 : maxWorkingHours;
    }

    //初始化或回显弹性时间段属性
    function initOrEchoElasticTimeProperties() {
        //初始化开始签到时间
        attTimeSlotShowTimer("elastic_startSignInTime", "startSignInTime", elasticTimeObjJson.startSignInTime);
        //初始化结束签退时间
        attTimeSlotShowTimer("elastic_endSignOffTime", "endSignOffTime", elasticTimeObjJson.endSignOffTime);
    }

    //重写底层attTimeSlotCheckNum方法
    function attTimeSlotCheckNum(id, sign, value) {
        if (value) {
            value = isNaN(parseInt(value)) ? 0: parseInt(value);
            if (value < 10) {
                value = complete(value);
                getTag(id, "input", sign, "name").value = value;
            } else {
                if (value > eval(sign)) {
                    getTag(id, "input", sign, "name").value = eval(sign);
                }
            }
        } else {
            getTag(id, "input", sign, "name").value = "00";
        }
        setHiddenValue(id);
    }

    //重写底层showTimer方法
    function attTimeSlotShowTimer(tempId, hiddenName, timeStr) {
        var timeArray = timeStr.split(":");

        mode[tempId + "mode"] = "HH";
        var hourContent = "<input type=\"text\" maxlength=\"2\" style=\"width: 25px\" id=\"" + tempId + "_HH\" name=\"HH\""
            + " value=\"" + timeArray[0] + "\""
            + " onchange=\"attTimeSlotCheckNum('" + tempId + "', 'HH', this.value)\""
            + " onkeydown=\"keyDown('" + tempId + "', event.keyCode, this)\""
            + " onkeyup=\"keyUp('" + tempId + "', event.keyCode)\""
            + " onfocus=\"setMode('" + tempId + "', 'HH')\""
            + " onblur=\"bgBlur(this)\" />";
        var minuteContent = "<input type=\"text\" maxlength=\"2\" style=\"width: 25px\" id=\"" + tempId + "_MM\" name=\"MM\""
            + " value=\"" + timeArray[1] + "\""
            + " onchange=\"attTimeSlotCheckNum('" + tempId + "', 'MM', this.value)\""
            + " onkeydown=\"keyDown('" + tempId + "', event.keyCode, this)\""
            + " onkeyup=\"keyUp('" + tempId + "', event.keyCode)\""
            + " onfocus=\"setMode('" + tempId + "', 'MM')\""
            + " onblur=\"bgBlur(this)\" />";
        var hiddenContent = "<input id=\"" + tempId + "_hidden\" name=\"" + hiddenName + "\" type=\"hidden\" />";

        document.getElementById(tempId).innerHTML = hourContent + "&nbsp;:&nbsp;" + minuteContent + "（HH:MM）" + hiddenContent;
        setHiddenValue(tempId);
    }

</script>
</#macro>