<#include '/public/template/editTemplate.html'>
<#macro editContent>
	<script type='text/javascript'>
		$().ready(function() {
			$('#${formId}').validate( {
				debug : true,
				rules :
				{
					'deptId' :
					{
						required : true
					},
					'deptCode' :
					{
						required : true
					},
					'deptName' :
					{
						required : true
					},
					'personPin' :
					{
						required : true
					},
					'personName' :
					{
						required : true
					},
					'personLastName' :
					{
						required : true
					},
					'areaId' :
					{
						required : true
					},
					'areaNo' :
					{
						required : true
					},
					'areaName' :
					{
						required : true
					},
					'deviceId' :
					{
						required : true
					},
					'deviceSn' :
					{
						required : true
					},
					'doorNo' :
					{
						required : true
					},
					'attDatetime' :
					{
						required : true
					},
					'attDate' :
					{
						required : true
					},
					'attTime' :
					{
						required : true
					},
					'attState' :
					{
						required : true
					},
					'attVerify' :
					{
						required : true
					},
					'mark' :
					{
						required : true
					},
				},
				submitHandler : function()
				{
					<@submitHandler/>
				}
			});
		});
	</script>

	<form action='attTransaction.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
		<input type='hidden' name='id' value='${(item.id)!}'/>
		<table class='tableStyle'>
			<tr>
				<!-- 部门id -->
				<th><label><@i18n 'att_transaction_deptId'/></label><span class='required'>*</span></th>
				<td><input name='deptId' type='text' value='${(item.deptId)!}'/></td>
			</tr>
			<tr>
				<!-- 部门编号 -->
				<th><label><@i18n 'att_transaction_deptNo'/></label><span class='required'>*</span></th>
				<td><input name='deptCode' type='text' value='${(item.deptCode)!}'/></td>
			</tr>
			<tr>
				<!-- 部门名称 -->
				<th><label><@i18n 'att_transaction_deptName'/></label><span class='required'>*</span></th>
				<td><input name='deptName' type='text' value='${(item.deptName)!}'/></td>
			</tr>
			<tr>
				<!-- 人员编号 -->
				<th><label><@i18n 'att_transaction_personPin'/></label><span class='required'>*</span></th>
				<td><input name='personPin' type='text' value='${(item.personPin)!}'/></td>
			</tr>
			<tr>
				<!-- 姓名 -->
				<th><label><@i18n 'att_transaction_personName'/></label><span class='required'>*</span></th>
				<td><input name='personName' type='text' value='${(item.personName)!}'/></td>
			</tr>
			<tr>
				<!-- 英文（lastName） -->
				<th><label><@i18n 'att_transaction_personLastName'/></label><span class='required'>*</span></th>
				<td><input name='personLastName' type='text' value='${(item.personLastName)!}'/></td>
			</tr>
			<tr>
				<!-- 区域Id -->
				<th><label><@i18n 'att_transaction_areaId'/></label><span class='required'>*</span></th>
				<td><input name='areaId' type='text' value='${(item.areaId)!}'/></td>
			</tr>
			<tr>
				<!-- 区域编号 -->
				<th><label><@i18n 'att_transaction_areaNo'/></label><span class='required'>*</span></th>
				<td><input name='areaNo' type='text' value='${(item.areaNo)!}'/></td>
			</tr>
			<tr>
				<!-- 区域名称 -->
				<th><label><@i18n 'att_transaction_areaName'/></label><span class='required'>*</span></th>
				<td><input name='areaName' type='text' value='${(item.areaName)!}'/></td>
			</tr>
			<tr>
				<!-- 设备Id -->
				<th><label><@i18n 'att_transaction_deviceId'/></label><span class='required'>*</span></th>
				<td><input name='deviceId' type='text' value='${(item.deviceId)!}'/></td>
			</tr>
			<tr>
				<!-- 设备序列号 -->
				<th><label><@i18n 'att_transaction_deviceSn'/></label><span class='required'>*</span></th>
				<td><input name='deviceSn' type='text' value='${(item.deviceSn)!}'/></td>
			</tr>
			<tr>
				<!-- 门编号 -->
				<th><label><@i18n 'att_transaction_doorNo'/></label><span class='required'>*</span></th>
				<td><input name='doorNo' type='text' value='${(item.doorNo)!}'/></td>
			</tr>
			<tr>
				<!-- 考勤日期时间 -->
				<th><label><@i18n 'att_transaction_attDatetime'/></label><span class='required'>*</span></th>
				<td><@ZKUI.Input type="date" value="${(item.attDatetime)?string('yyyy-MM-dd')}" name="attDatetime" hideLabel="true" title="att_transaction_attDatetime"/></td>
			</tr>
			<tr>
				<!-- 考勤日期 -->
				<th><label><@i18n 'att_transaction_attDate'/></label><span class='required'>*</span></th>
				<td><input name='attDate' type='text' value='${(item.attDate)!}'/></td>
			</tr>
			<tr>
				<!-- 考勤时间 -->
				<th><label><@i18n 'att_transaction_attTime'/></label><span class='required'>*</span></th>
				<td><input name='attTime' type='text' value='${(item.attTime)!}'/></td>
			</tr>
			<tr>
				<!-- 考勤状态 -->
				<th><label><@i18n 'att_transaction_attState'/></label><span class='required'>*</span></th>
				<td><input name='attState' type='text' value='${(item.attState)!}'/></td>
			</tr>
			<tr>
				<!-- 验证方式 -->
				<th><label><@i18n 'att_transaction_attVerify'/></label><span class='required'>*</span></th>
				<td><input name='attVerify' type='text' value='${(item.attVerify)!}'/></td>
			</tr>
			<tr>
				<!-- 标识 -->
				<th><label><@i18n 'att_transaction_mark'/></label><span class='required'>*</span></th>
				<td><input name='mark' type='text' value='${(item.mark)!}'/></td>
			</tr>
		</table>
	</form>
</#macro>