<#assign gridName="attTransactionGrid${uuid!}">
<script type="text/javascript">

	/*参考deptIncludeLower.js配置是否显示下级部门树，以及部门树相关操作*/
	/*当前部门树ID*/
	attGlobalDeptTreeId = "deptTree";
	/*当前gridName*/
	attGlobalGridName = "${gridName}";
	/*是否显示包含下级*/
	attGlobalShowIncludeLower = false;

	function attConvertToLinkage(url) {
		if (url) {
			return "<img src='images/base_vid_capture.png' height='18px' style='cursor: pointer;margin-left:10px;' onmouseover='attShowCapturePhoto(\"" + url + "\")' onmouseout='$.jBox.close()' />";
		}
		return ""
	}

	function attShowCapturePhoto(url) {
		var capturePhotoHtml = "<div style='width: 95%; height: 95%; text-align: center;margin: 0 auto;padding-top: 5px'>" +
				"<img src='attTransaction.do?getDecryptFileBase64&path=" + url + "' onerror=\"this.src='/images/"+attUserImageBySkin()+"'\" style='height: 100%; width: 100%;' /></div>";
		$.jBox.open(capturePhotoHtml, "<@i18n 'att_statistical_attPhoto'/>", 250, 320, {
			id: "capturePhoto${uuid!}",
			opacity: 0,
			top: '25%',
			showIcon: 'jbox-title-icon-video',
			buttons: {}
		});
	}

</script>
<@ZKUI.GridBox gridName="${gridName}" showColumns="!attVerify,longitude,latitude">
    <@ZKUI.Searchbar onClear="updateSearchBarCondition">
        <@ZKUI.SearchTop>
            <tr>
				<td valign="middle">
					<@ZKUI.Input type="datetime" id="beginTime${uuid!}" endId="endTime${uuid!}" name="beginDate" title="common_time_from" todayRange="start" max="today" today="-1" noOverToday="true" readonly="false"/>
					&nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
					<@ZKUI.Input type="datetime" id="endTime${uuid!}" name="endDate" title="common_to" max="today" todayRange="end" today="true" noOverToday="true" hideLabel="true" readonly="false"/>
				</td>
				<#if !user.staff>
	                <td valign="middle">
	                    <@ZKUI.Input name="personPin" maxlength="30" title="att_person_pin" type="text"/>
	                </td>
					<td valign="middle">
						<@ZKUI.Input name="likeName" maxlength="30" title="pers_person_wholeName" type="text"/>
					</td>

			    </#if>
            </tr>
        </@ZKUI.SearchTop>
        <@ZKUI.SearchBelow>
            <tr>
				<td valign="middle">
					<@ZKUI.Input name="deptName" maxlength="30" title="pers_dept_deptName" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="areaName" maxlength="30" title="pers_person_attArea" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="deviceSn" maxlength="30" title="common_dev_sn" type="text"/>
				</td>
				<#if Application['system.language'] != "zh_CN">
				<td valign="middle">
					<@ZKUI.Input name="deviceName" maxlength="30" title="common_dev_name" type="text"/>
				</td>
				</#if>
				<td valign="middle">
					<@ZKUI.Combo name="mark" title="att_statistical_dataSources" readonly="true">
						<option value="att"><@i18n 'att_statistical_att'/></option>
						<option value="att-sign"><@i18n 'att_api_sign'/></option>
						<option value="app"><@i18n 'att_statistical_app'/></option>
						<#if systemModules?lower_case?index_of("acc")!=-1><option value="acc"><@i18n 'att_statistical_acc'/></option></#if>
						<#if systemModules?lower_case?index_of("park")!=-1><option value="park"><@i18n 'att_statistical_park'/></option></#if>
						<#if systemModules?lower_case?index_of("ins")!=-1><option value="ins"><@i18n 'att_attPoint_ins'/></option></#if>
						<#if systemModules?lower_case?index_of("pid")!=-1><option value="pid"><@i18n 'att_attPoint_pid'/></option></#if>
						<#if systemModules?lower_case?index_of("vms")!=-1><option value="vms"><@i18n 'att_statistical_vms'/></option></#if>
						<#if systemModules?lower_case?index_of("psg")!=-1><option value="psg"><@i18n 'att_statistical_psg'/></option></#if>
						<#if systemModules?lower_case?index_of("ivs")!=-1><option value="ivs"><@i18n 'ivs_module'/></option></#if>
						<#if systemModules?lower_case?index_of("esdc")!=-1><option value="esdc"><@i18n 'esdc_module'/></option></#if>
					</@ZKUI.Combo>
				</td>
            </tr>
        </@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
    <#if user.staff>
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:transaction:refresh"/>
			<@ZKUI.ToolItem id="attTransaction.do?export" type="export" img="comm_export.png" permission="att:transaction:export"/>
		</@ZKUI.Toolbar>
		<@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttTransactionItem" query="attTransaction.do?list"/>
    </#if>
    <#if !user.staff>
	    <@ZKUI.Layout style="position:absolute; top:70px;bottom:0px;left:0px;right:0px;height:auto;" pattern="2U" onInit="attGlobalInitDeptTree">
	    	<@ZKUI.Cell width="240" treeId="deptTree">
	            <@ZKUI.Tree dynamic="true" id="deptTree" url="authDepartment.do?dynaTree&showPersonCount=false" onClick="attGlobalDeptTreeClick"></@ZKUI.Tree>
	        </@ZKUI.Cell>
	        <@ZKUI.Cell hideHeader="true">
	            <@ZKUI.Toolbar>
					<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:transaction:refresh"/>
					<@ZKUI.ToolItem type="more" text="common_op_export" img="comm_export.png">
						<@ZKUI.ToolItem id="attTransaction.do?export" type="export" img="comm_export.png" text="att_transaction_exportRecord" title="att_transaction_exportRecord" permission="att:transaction:export"/>
						<@ZKUI.ToolItem id="attTransaction.do?exportAttPhoto" type="export" action="exportAttPhoto" text="att_transaction_exportAttPhoto" title="att_transaction_exportAttPhoto" permission="att:transaction:exportAttPhoto"/>
					</@ZKUI.ToolItem>
					<@ZKUI.ToolItem id="attTransaction.do?importUSBRecord" action="importUSBRecord" text="att_op_importUSBRecord" onFinish="attGlobalReloadGrid" img="comm_import.png" permission="att:transaction:importUSBRecord"/>
					<@ZKUI.ToolItem isShow="${showSyncAttRecord}" id="attTransaction.do?syncAttRecord" action="attSyncRecord" text="att_transaction_SyncRecord" onFinish="attGlobalReloadGrid" img="att_syncPoint.png" permission="att:transaction:SyncRecord"/>
	            </@ZKUI.Toolbar>
	            <@ZKUI.Grid pageList="true" limitCount="100000" vo="com.zkteco.zkbiosecurity.att.vo.AttTransactionItem" query="attTransaction.do?list"/>
	        </@ZKUI.Cell>
	    </@ZKUI.Layout>
    </#if>
</@ZKUI.GridBox>
<script type="text/javascript">

	$(function(){
		updateSearchBarCondition("","");
	});

	function updateSearchBarCondition(searchId, gridName){
		setMonthFirstDay("beginTime" + "${uuid!}");
    	setNowDay("endTime" + "${uuid!}");
    }

	//选择要同步记录的考勤点
	function attSyncRecord(id, bar, opt){
		if(bar){
			var path = "skip.do?page=att_transaction_importRecord&editPage=true&actionId=" + id;
			var text = "<@i18n 'att_transaction_SyncRecord'/>";
			var opts = {
				path: path,
				width: 420,
				height: 350,
				title: text,
				gridName: "gridbox"
			};
			DhxCommon.createWindow(opts);
		}
	}

	//导入USB记录
	function importUSBRecord(id, bar, opt) {
		if(bar){
			var path = "skip.do?page=att_transaction_importUsbRecord&editPage=true&importType=Usb";
			var text = "<@i18n 'att_op_importUSBRecord'/>";

				opts = {
					path: path,
					width: 500,
					height: 300,
					title: text,
					gridName: "gridbox"
				};
			DhxCommon.createWindow(opts);
		}
	}

    function exportAttPhoto(id, bar, opts) {
		if (bar) {
			var gridName = bar.gridName;
			opts.path = opts.path || "skip.do?page=att_transaction_opExportAttPhoto&gridName=" + gridName + "&actionName=" + encodeURIComponent(id);
			if(opts.maxExportCount) {
				opts.path = opts.path + "&maxExportCount=" + opts.maxExportCount;
			}
			opts.width = opts.width || 450;
			opts.height = opts.height || 330;
			DhxCommon.createWindow(opts);
		}
    }
</script>