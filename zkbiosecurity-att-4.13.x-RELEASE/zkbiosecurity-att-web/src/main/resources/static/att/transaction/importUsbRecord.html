<#assign editPage = "true">
<#include "/public/template/editTemplate.html">
<#macro editContent>
<script type="text/javascript">
$().ready(function(){
	$("#${formId}").validate({
		debug : true,
		submitHandler: function(form){
			var file = ZKUI.Upload.get("id_attTransactionUpload").getFile()[0];
            if(!file){
                messageBox({messageType : "alert", text : "<@i18n 'common_dev_selectFile'/>"});
                return false;
            }
            var pattenChar = /\.dat$/;
            if(!pattenChar.test(file.name))
			{
                messageBox({messageType : "alert", text : "<@i18n 'att_import_selectCorrectFile'/>"});
                return false;
			}
            var flag = false;
            $.ajax({
	            url:"attTransaction.do?validFileName",
	            type:"post",
	            dataType:"json",
	            async :false,
	            data:{
	                "fileName": file.name
				},
	            success: function (result) {
	                flag = result;
	            }
	        });
            if(flag == false) {
            	messageBox({messageType : "alert", text : "<@i18n 'att_import_snNoExist'/>"});
                return false;
            }
            
			<#if showImportProcess?? && showImportProcess=="true">
				var opts = {
					useReq:true,//必须
					noAutoProcess:true,//阻断自动调动进度
					dealPath:"",
					height:250,//弹窗高度
					type: "single",//进度类型，取值single,public,custom
					title:"<@i18n 'common_op_import'/>",
					onInit:"onImportInit",
					hideStopButton:true,
					onFinish:"${onFinish!}"
				}
				openProcess(opts);
			<#else>
				$('#${formId}').ajaxSubmit({
					async:false,
					success: function(result){
						dealRetResult(eval(result),function(){
							var gridNameId = DhxCommon.getCurrentWindow().opts.gridName;
							if(isContinueAdd)
							{
								DhxCommon.refreshCurrentWindow();
							}
							else
							{
								isSureClose=true;
								DhxCommon.closeWindow();
							}
							
							//重新加载数据列表
							if(ZKUI) {
								reloadAttTransGrid();
							}
						});
					}
				});
				openMessage(msgType.loading);
				setTimeout(function(){
					closeMessage();
				}, 3000);
			</#if>
		}
	});
});
</script>
<form action="attTransaction.do?importUSBRecord" method='post' id='${formId}' enctype='multipart/form-data'>
	<table class="tableStyle" style="height:120px;margin: 10px 10px 10px 10px;">
		<tr>
			<td ><@i18n 'att_import_fileFormat'/></td>
			<td><@ZKUI.Input hideLabel="true" name="fileType" value="Excel" type="radio" checked="checked"/> dat</td>
		</tr>
		<tr>
			<td><@i18n 'common_dev_selectFile'/></td>
			<td><@ZKUI.Upload id="id_attTransactionUpload" name="upload" /></td>
		</tr>
		<tr>
			<td valign="middle" colspan="2" style="padding-right: 10px;">
				<span  class="warningColor"><@i18n 'att_import_fileName_msg'/></span>
			</td>
		</tr>
	</table>
</form>
</#macro>
<#macro buttonContent>
<button class='button-form' id="${formId}OK"><@i18n 'common_edit_ok'/><tton>
<button class='button-form' onclick="DhxCommon.closeWindow()"><@i18n 'common_edit_cancel'/><tton>
</#macro>
