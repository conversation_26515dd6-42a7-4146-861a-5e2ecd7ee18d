<#include "/public/template/editTemplate.html">
<#macro editContent>
<form action="${actionId}" method="post" id="${formId}" onkeydown="if(event.keyCode==13){return false;}" autocomplete="off">
	<input type="hidden" id="attPointIds" name="attPointIds" />
	<table class="tableStyle">
		<tbody>
			<tr>
				<!-- 开始时间 -->
				<th><label><@i18n 'common_startTime'/></label></th>
				<td>
					<@ZKUI.Input endId="endTime${uuid!}" type="datetime" todayRange="start" readonly="true" today="true" name="startDatetime" hideLabel="true" title="common_startTime"/>
				</td>
			</tr>

			<tr>
				<!-- 结束时间 -->
				<th><label><@i18n 'common_endTime'/></label></th>
				<td>
					<@ZKUI.Input id="endTime${uuid!}" type="datetime" todayRange="end" readonly="true" today="true" name="endDatetime" hideLabel="true" title="common_endTime"/>
				</td>
			</tr>
			<tr>
				<!-- 考勤点列表 -->
				<th><label><@i18n 'att_attPoint_list'/></label></th>
			   	<td>
			   		<input type="text" readonly id="attTransSelectPointId" name="attTransSelectPointId" placeholder="<@i18n 'att_attPoint_point'/>" onclick="selectAttPoint()" />
				</td>
			</tr>
		</tbody>
	</table>
</form>
<script type="text/javascript">

/*
******************
* 表单验证
******************
*/
$("#${formId}").validate({
    debug: true,
    rules: {},
    submitHandler: function() {
    	var id = $("#attPointIds").val();
    	if (!id) {
    		//请选择考勤点
    		messageBox({messageType: "alert", text: "<@i18n 'att_attPoint_point'/>"});
    	 	return;
    	}
        <@submitHandler callBackFun="attGlobalReloadGrid()"/>
    }
});

//考勤点列表
function selectAttPoint() {
	var path = "skip.do?page=att_point_selectPointContent";
	var opts = {
		path: path,
		width: 800,
		height: 500,
		title: "<@i18n 'att_attPoint_point'/>",
		gridName: "gridbox"
	};
	DhxCommon.createWindow(opts);
}

//选择考勤点之后
function attTransAfterSelectPoint(id,text,event) {
	$("#attPointIds").val(id);
	$("#attTransSelectPointId").val(text);
}
</script>
</#macro>