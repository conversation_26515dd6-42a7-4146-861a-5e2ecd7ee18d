<#assign gridName="attBreakTimeGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
				<td valign="middle">
					<@ZKUI.Input name="name" maxlength="30" title="common_name" type="text"/>
				</td>
 			</tr>
 		</@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar gridName="${gridName}">
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:breakTime:refresh"></@ZKUI.ToolItem>
    	<@ZKUI.ToolItem id="attBreakTime.do?edit" text="common_op_new" width="420" height="300" img="comm_add.png" action="commonAdd" permission="att:breakTime:add"></@ZKUI.ToolItem>
    	<@ZKUI.ToolItem id="attBreakTime.do?del&names=(name)" text="common_op_del" img="comm_del.png" action="commonDel" permission="att:breakTime:del"></@ZKUI.ToolItem>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid gridName="${gridName}" vo="com.zkteco.zkbiosecurity.att.vo.AttBreakTimeItem" query="attBreakTime.do?list"/>
</@ZKUI.GridBox>