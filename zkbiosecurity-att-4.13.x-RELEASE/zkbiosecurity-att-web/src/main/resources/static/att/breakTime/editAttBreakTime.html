<#include '/public/template/editTemplate.html'>
<#assign formId="attBreakTimeEditForm${uuid!}">
<#macro editContent>
	<!--编辑状态下且存在关联数据时编辑框的样式-->
	<style>
		.warning-wrapper {
			position: absolute;
			left: 10px;
			bottom: 0px;
		}
		.warning-image {
			width: 18px;
			height: 15px;
			font-weight: bold;
			display: inline-block;
			margin-right: 3px;
			margin-left: 5px;
			vertical-align: bottom;
			background-image: url("/public/images/alert.png");
			background-repeat: no-repeat;
			background-position: 0px -1px;
		}
		.warning-color {
			color: #E57A14;
		}
	</style>

	<form action='attBreakTime.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
	<input type='hidden' name='id' value='${(item.id)!}'/>
	<input type='hidden' id="startTime" name="startTime" value="${(item.startTime)!}"/>
	<input type='hidden' id="endTime" name="endTime" value="${(item.endTime)!}"/>
	<table class='tableStyle'>
		<tr>
			<!-- 名称 -->
			<th><label><@i18n 'common_name'/></label><span class='required'>*</span></th>
			<td><input name='name' maxlength="30" type='text' value='${(item.name)!}'/></td>
		</tr>
		<tr>
			<!-- 开始时间 -->
			<th><label><@i18n 'att_breakTime_startTime'/></label><span class='required'>*</span></th>
			<td>
				<@ZKUI.Timer name="attStartTimeEx" id="attStartTime" value="${(item.startTime)!}" format="HH:MM" showFormat="true"></@ZKUI.Timer>
			</td>
		</tr>
		<tr>
			<!-- 结束时间 -->
			<th><label><@i18n 'att_breakTime_endTime'/></label><span class='required'>*</span></th>
			<td>
				<@ZKUI.Timer name="attEndTimeEx" id="attEndTime" value="${(item.endTime)!}" format="HH:MM" showFormat="true"></@ZKUI.Timer>
			</td>
		</tr>
	</table>
</form>
	<!--开始时间和结束时间格式化的样式调整-->
	<!--将基线放到中间对齐-->
	<style>
		.tableStyle td {
			vertical-align: middle;
		}
	</style>
<script type='text/javascript'>

$('#${formId}').validate({
	debug: true,
	rules: {
		'name': {
			required: true,
            unInputChar: true,
            overRemote: ["attBreakTime.do?validName", "${(item.name)!}"]
		},
        'attStartTimeEx': {
            timeValid: true
        },
        'attEndTimeEx': {
            timeValid: true
        }
	},
	submitHandler: function() {
	    //保存开始时间和结束时间处理
        var attStartTime = ZKUI.Timer.PULL["attStartTime"].getValue();
        var attEndTime = ZKUI.Timer.PULL["attEndTime"].getValue();
        var id = '${(item.id)!}';
        $("#startTime").val(attStartTime);
        $("#endTime").val(attEndTime);

        if(attStartTime == attEndTime){
            //结束时间不能等于开始时间
            messageBox({
                messageType : "alert",
                title : "<@i18n 'common_prompt_title'/>",
                text : "<@i18n 'common_dsTime_timeValid1'/>"
            });
            return false;
        }

        $.ajax({
			async: false,
            data: {
			    id: id,
                startTime: attStartTime,
                endTime: attEndTime
            },
        	dataType: "json",
            type: "post",
            url: "attBreakTime.do?isExistBreakTime",
            success: function (result) {
			   if(result.data){
					messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_jqMsg_remote'/>"});
			   }else{
               		<@submitHandler/>;
			   }
           	},
		   	error : function(XMLHttpRequest, textStatus, errorThrown) {
				   closeMessage();
				   messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
				}
		});

	}
});
    //验证时间是否为数字
    jQuery.validator.addMethod("timeValid", function (value, element) {
        var ret = true;
        var attTime = value.split(":");
        for (var i = 0; i < attTime.length; i++) {
            if (attTime[i] && isNaN(attTime[i])) {
                ret = false;
            }
        }
        return ret;
    }, function () {
        return "<@i18n 'att_timeSlot_time'/>";
    });

	var editAttBreakTimeFormId="#${formId}";
	//如果是编辑状态下且存在关联数据 则编辑框内容不可编辑
	if ("${(item.id)!}"!="" && "${isExistFkData}"){
		$(editAttBreakTimeFormId+" input[name='name']").attr("readonly","readonly");
		$("input[name='HH']").attr("disabled", "disabled").end().find("input[name='MM']").attr("disabled", "disabled");
		$("#attStartTime").find("input[type='button']").attr("disabled", "disabled");
		$("#attEndTime").find("input[type='button']").attr("disabled", "disabled");
		$(".content_div").css({"position": "relative"}).append("<div class='warning-wrapper'><span class='warning-image'></span><span class='warning-color'><@i18n 'att_common_relationDataCanNotEdit'/></span></div>");
	}

</script>
</#macro>