<style>
	.attCustomReportRightGridClass {
		bottom: 0px;
		top: 0px;
		right: 0px;
		left: 0px;
		position: absolute;
	}
</style>
<@ZKUI.Layout style="height:100%;width:100%;">
	<@ZKUI.Cell title="base_customReport" hideArrow="true" width="320">
		<@ZKUI.GridBox gridName="attCustomReportLeftGridName" autoFirst="true">
			<@ZKUI.Searchbar>
				<@ZKUI.SearchTop>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="name" maxlength="50" title="common_name" type="text"/>
					</td>
				</tr>
				</@ZKUI.SearchTop>
			</@ZKUI.Searchbar>
			<@ZKUI.Toolbar>
				<@ZKUI.ToolItem type="refresh" action="commonRefresh" permission="att:customReport:refresh"></@ZKUI.ToolItem>
				<@ZKUI.ToolItem id="attCustomReport.do?edit" text="common_op_new" width="800" height="600" img="comm_add.png" action="commonAdd" permission="att:customReport:add"></@ZKUI.ToolItem>
				<@ZKUI.ToolItem id="attCustomReport.do?del&names=(name)" text="common_op_del" img="comm_del.png" action="commonDel" callback="attCustomReportLeftGridDelCallback" permission="att:customReport:del"></@ZKUI.ToolItem>
			</@ZKUI.Toolbar>
			<!-- 配置左表格选中事件 -->
			<@ZKUI.Grid onRowSelect="attCustomReportLeftGridRowSelect" vo="com.zkteco.zkbiosecurity.att.vo.AttCustomReportItem" query="attCustomReport.do?list"/>
		</@ZKUI.GridBox>
	</@ZKUI.Cell>
	<@ZKUI.Cell title="base_customReport_detail" hideArrow="true">
		<div id="attCustomReportRightGridId" class="attCustomReportRightGridClass"></div>
	</@ZKUI.Cell>
</@ZKUI.Layout>
<script type="text/javascript" src="/system/js/baseCustomReport.js" charset="UTF-8"></script>
<script type="text/javascript">

	// 左列表选择事件
	function attCustomReportLeftGridRowSelect(id) {
		loadAttCustomReportRightGrid(id)
	}

	// 加载又列表自定义报表
	function loadAttCustomReportRightGrid(id) {
		setIdHtmlByPath("attCustomReport.do?toCustomReport&id="+id, "attCustomReportRightGridId");
	}

	// 自定义报表导出，设置导出文件名
	function attCustomReportExport(id, bar, opts) {
		baseCustomReportExport(id, bar, opts, "attCustomReportLeftGridName")
	}

	// 删除回调清空右边自定义报表详情列表，刷新左边自定义报表列表
	function attCustomReportLeftGridDelCallback(value, text, event) {
		$("#attCustomReportRightGridId").empty();
		ZKUI.Grid.get("attCustomReportLeftGridName").reload();
	}

	function editBaseCustomReportOnClose() {
		var selectedId = ZKUI.Grid.get("attCustomReportLeftGridName").grid.getSelectedId()
		if (selectedId) {
			loadAttCustomReportRightGrid(selectedId);
		}
	}

</script>