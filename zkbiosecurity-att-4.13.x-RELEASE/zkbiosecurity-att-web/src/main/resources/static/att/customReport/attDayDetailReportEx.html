<#assign gridName="attDayDetailReportExGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}" showColumns="${showColumns!}">
    <@ZKUI.Searchbar onClear="updateSearchBarCondition">
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input type="date" id="beginTime${uuid!}" endId="endTime${uuid!}" name="startDatetimeBegin" title="common_time_from" todayRange="start" readonly="false"/>
                    &nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
                    <@ZKUI.Input type="date" id="endTime${uuid!}" name="startDatetimeEnd" title="common_to" max="today" todayRange="end" today="true" hideLabel="true" readonly="false"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="personPin" id="pin${uuid!}" maxlength="30" title="att_person_pin" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
        <@ZKUI.SearchBelow>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="likeName" id="likeName${uuid!}" maxlength="30" title="pers_person_wholeName" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="deptName" id="deptName${uuid!}" maxlength="30" title="pers_dept_deptName" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
        <@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:dayDetailReport:refresh"/>
        <@ZKUI.ToolItem type="export" id="attDayDetailReport.do?export" permission="att:dayDetailReport:export" action="attCustomReportExport" permission="att:customReport:export"/>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid pageList="true" limitCount="100000" vo="com.zkteco.zkbiosecurity.att.vo.AttDayDetailReportItem" query="attDayDetailReport.do?list"/>

</@ZKUI.GridBox>
<script type="text/javascript">
    attGlobalInitOrClearUUID = "${uuid!}";
    $(function(){
        updateSearchBarCondition("${uuid!}", "${gridName}");
    });
    function updateSearchBarCondition(searchId, gridName){
		setMonthFirstDay("beginTime" + "${uuid!}");
    	setNowDay("endTime" + "${uuid!}");
    }
</script>