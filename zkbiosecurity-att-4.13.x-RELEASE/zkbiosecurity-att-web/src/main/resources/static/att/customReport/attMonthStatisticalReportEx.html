<#assign gridName="attMonthStatisticalReportExGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}" showColumns="${showColumns!}">
    <@ZKUI.Searchbar onClear="updateSearchBarCondition">
        <@ZKUI.SearchTop>
             <tr>
                 <td valign="middle">
                     <@ZKUI.Input type="date" id="beginTime${uuid!}" endId="endTime${uuid!}" name="monthStart" title="common_time_from" todayRange="start" max="today" readonly="false"/>
                     &nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
                     <@ZKUI.Input type="date" id="endTime${uuid!}" name="monthEnd" title="common_to" todayRange="end" today="true" hideLabel="true" max="today" readonly="false"/>
                 </td>
                 <td valign="middle">
                     <@ZKUI.Input name="pin" id="pin${uuid!}" maxlength="30" title="att_person_pin" type="text"/>
                 </td>
            </tr>
        </@ZKUI.SearchTop>
        <@ZKUI.SearchBelow>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="likeName" id="likeName${uuid!}" maxlength="30" title="pers_person_wholeName" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="deptName" id="deptName${uuid!}" maxlength="30" title="pers_dept_deptName" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>

    <@ZKUI.Toolbar>
        <@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:monthStatisticalReport:refresh"/>
        <@ZKUI.ToolItem type="export" id="attMonthStatisticalReport.do?export" permission="att:monthStatisticalReport:export" action="attCustomReportExport" permission="att:customReport:export"/>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttMonthStatisticalReportItem" query="attMonthStatisticalReport.do?list"/>
</@ZKUI.GridBox>
<script type="text/javascript">
    attGlobalInitOrClearUUID = "${uuid!}";
    $(function(){
        updateSearchBarCondition("${uuid!}", "${gridName}");
    });
    function updateSearchBarCondition(searchId, gridName){
		setMonthFirstDay("beginTime" + "${uuid!}");
    	setNowDay("endTime" + "${uuid!}");
    }
</script>