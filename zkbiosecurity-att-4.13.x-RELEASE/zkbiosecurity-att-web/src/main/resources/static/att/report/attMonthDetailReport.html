<#assign gridName="attMonthDetailReportGrid${uuid!}">

<style>
    .attSymbolsJsonClass {
        /*border: 2px solid #dcdfe2;*/
        padding: 2px 4px;
        box-sizing: border-box;
        margin-right: 4px;
        box-sizing: border-box;
        background-color: #f3f5f0;
        position: absolute;
        white-space: normal;
        height: 40px;
        line-height: 18px;
        display: inline-block;
        cursor: pointer;
        overflow-y: auto;
    }

    .attSymbolsJsonClassDiv {
        position: relative;
    }

</style>

<script type="text/javascript">

    /*参考deptIncludeLower.js配置是否显示下级部门树，以及部门树相关操作*/
    /*当前部门树ID*/
    attGlobalDeptTreeId = "tree${uuid!}";
    /*当前gridName*/
    attGlobalGridName = "${gridName}";
    /*是否显示包含下级*/
    attGlobalShowIncludeLower = false;

</script>
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar onBeforeQuery="searchAttMonthDetail" onClear="updateSearchBarCondition" >
        <@ZKUI.SearchTop>
            <tr id="attMonthDetailSearchbar">
                <td valign="middle">
                    <@ZKUI.Input type="date" id="beginTime${uuid!}" endId="endTime${uuid!}" name="monthStart" title="common_time_from" todayRange="start" readonly="false"/>
                    &nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
                    <@ZKUI.Input type="date" id="endTime${uuid!}" name="monthEnd" title="common_to" todayRange="end" today="true" max="today" hideLabel="true" readonly="false"/>
                </td>
                <#if !user.staff>
	                <!-- 人员编号 -->
	                <td valign="middle">
	                    <@ZKUI.Input name="pin" id="pin${uuid!}" maxlength="30" title="att_person_pin" type="text"/>
	                </td>
	                <td valign="middle">
	                    <@ZKUI.Input name="deptName" id="deptName${uuid!}"  maxlength="30" title="pers_dept_deptName" type="text"/>
	                </td>
	            </#if>
            </tr>
        </@ZKUI.SearchTop>
        <#if !user.staff>
        <@ZKUI.SearchBelow>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="likeName" id="likeName${uuid!}"  maxlength="30" title="pers_person_wholeName" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchBelow>
        </#if>
    </@ZKUI.Searchbar>
    <@ZKUI.Layout id="layout${uuid}" style="position:absolute; top:70px;bottom:0px;left:0px;right:0px;height:auto;" onInit="attGlobalInitDeptTree">
	    <#if user.staff>
	        <@ZKUI.Cell hideHeader="true">
			    <@ZKUI.Toolbar>
			        <@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:monthDetailReport:refresh"/>
			        <@ZKUI.ToolItem id="attMonthDetailReport.do?export" isShow="true" type="export" permission="att:monthDetailReport:export"/>
			    </@ZKUI.Toolbar>
			    <div id="box${uuid}" style="width:100%;bottom:0px;top:38px;position:absolute;"></div>
			</@ZKUI.Cell>
		</#if>
		<#if !user.staff>
	        <@ZKUI.Cell width="240" treeId="tree${uuid!}" >
	            <@ZKUI.Tree dynamic="true" id="tree${uuid!}" url="authDepartment.do?dynaTree&showPersonCount=false" onClick="attGlobalDeptTreeClick"></@ZKUI.Tree>
	        </@ZKUI.Cell>
	        <@ZKUI.Cell hideHeader="true">
                <@ZKUI.Toolbar>
                    <@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:monthDetailReport:refresh"/>
                    <@ZKUI.ToolItem id="attMonthDetailReport.do?export" isShow="true" type="export" permission="att:monthDetailReport:export"/>
                </@ZKUI.Toolbar>
                <div id="box${uuid}" style="width:100%;bottom:0px;top:38px;position:absolute;"></div>
            </@ZKUI.Cell>
		</#if>
	</@ZKUI.Layout>
</@ZKUI.GridBox>
<script type="text/javascript">

    attGlobalInitOrClearUUID = "${uuid!}";
    $(function () {
        updateSearchBarCondition("${uuid!}", "${gridName}");
        loadAttMonthGrid();
    });

    function updateSearchBarCondition(searchId, gridName){
		setMonthFirstDay("beginTime" + "${uuid!}");
    	setNowDay("endTime" + "${uuid!}");
    };

    /**
     * 点击搜索
     */
    function searchAttMonthDetail(){
        loadAttMonthGrid();
        return false;
    };

    /**
     * 数据加载
     */
    function loadAttMonthGrid() {

        var queryParam = {};
        if (ZKUI.Tree.get("tree${uuid!}")) {
            queryParam.deptId = ZKUI.Tree.get("tree${uuid!}").tree.getSelectedItemId();
        }

        var opts = {
            gridName:"${gridName}",//设置grid名称
            monthStart: $("#attMonthDetailSearchbar input[name='monthStart']").val(),
            monthEnd: $("#attMonthDetailSearchbar input[name='monthEnd']").val(),
            vo:"com.zkteco.zkbiosecurity.att.vo.AttMonthDetailReportItem",//设置vo
            query:"attMonthDetailReport.do?list",//查询请求
            queryParam:JSON.stringify(queryParam),//请求参数对象
            onXLE:"afterAttMonthDetailLoadedHandler"//注册数据加载结束事件
        };
        $("#box${uuid}").loadGrid(opts)
    }

    /***
     * 数据加载完成回调
     */
    function afterAttMonthDetailLoadedHandler(dhxGrid) {
        loadSymbols();
    }

    /***
     * 加载考勤状态标识
     */
    function loadSymbols() {
        $.ajax({
            url:"attMonthDetailReport.do?getAttSymbolsJson",
            type:"post",
            dataType:"json",
            data:{
            },
            success:function (data) {
                var attSymbols="<span>";
                $.each(data, function (key, value) {
                    attSymbols += key + "&nbsp;-&nbsp;<span style = \"margin-right: 5px;\">" + value + "；</span>";
                });
                var text = "<div class='attSymbolsJsonClassDiv'><div class='attSymbolsJsonClass' id='attSymbolsJson' " + attSymbols + "</div></div>";
                <#if user.staff>
                    ZKUI.Layout.get("layout${uuid}").layout.cells("a").attachStatusBar({text: text, height: 42});
                </#if>
                <#if !user.staff>
                    ZKUI.Layout.get("layout${uuid}").layout.cells(IS_ENABLE_RTL?"a":"b").attachStatusBar({text: text, height: 42});
                </#if>
                ZKUI.Grid.resizeGird("${gridName}");//重新调整布局,避免滚动条被覆盖
            }
        });
    }

</script>


