<#assign gridName="attAnnualLeaveReportGrid${uuid!}">

<script type="text/javascript">

    /*参考deptIncludeLower.js配置是否显示下级部门树，以及部门树相关操作*/
    /*当前部门树ID*/
    attGlobalDeptTreeId = "tree${uuid!}";
    /*当前gridName*/
    attGlobalGridName = "${gridName}";
    /*是否显示包含下级*/
    attGlobalShowIncludeLower = false;

     function attAnnualLeaveViewDetail(value,gridColumn,grid,index,id) {
        if (value !== '') {
             return "<a href='javascript:annualLeaveViewDetail(\""+id+"\")'>"+value+"</a>";
        } else {
            return "<a>"+value+"</a>";
        }
    }

    function annualLeaveViewDetail(personId) {
        var path = "skip.do?page=att_report_attAnnualLeaveDetail&personId=" + personId;
        var opts = {
            path: path,
            width: 720,
            height: 400,
            title: "<@i18n 'auth_license_details'/>",
            gridName: "gridbox"
        };
        DhxCommon.createWindow(opts);
    }

</script>
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="persPin" maxlength="30" title="att_person_pin" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="deptName" maxlength="30" title="pers_dept_deptName" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="likeName" maxlength="30" title="pers_person_wholeName" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    <@ZKUI.Layout style="position:absolute; top:70px;bottom:0px;left:0px;right:0px;height:auto;" pattern="2U" onInit="attGlobalInitDeptTree">
        <@ZKUI.Cell width="240" treeId="tree${uuid!}" >
            <@ZKUI.Tree id="tree${uuid!}" dynamic="true" type="checkbox" url="authDepartment.do?dynaTree&showPersonCount=false"
                onClick="attGlobalDeptTreeClick" onCheck="attGlobalDeptTreeCheck"></@ZKUI.Tree>
        </@ZKUI.Cell>
        <@ZKUI.Cell hideHeader="true">
            <@ZKUI.Toolbar>
                <@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:annualLeaveReport:refresh"/>
                <@ZKUI.ToolItem id="attAnnualLeaveReport.do?recalculate" text="att_annualLeave_recalculate" img="att_calculate.png" action="attCommonDeptOrRowsOperate"></@ZKUI.ToolItem>
                <@ZKUI.ToolItem id="attAnnualLeaveReport.do?adjustView"  height="200" text="att_annualLeave_adjustDay" img="att_calculate.png" action="attCommonDeptOrRowsOperateOpen"></@ZKUI.ToolItem>
                <@ZKUI.ToolItem type="export" id="attAnnualLeaveReport.do?export" permission="att:annualLeaveReport:export"/>
            </@ZKUI.Toolbar>
            <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttAnnualLeaveReportItem" query="attAnnualLeaveReport.do?list"/>
        </@ZKUI.Cell>
    </@ZKUI.Layout>
</@ZKUI.GridBox>