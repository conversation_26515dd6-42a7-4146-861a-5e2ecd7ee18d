<#assign gridName="attDayDetailReportGrid${uuid!}">
<script type="text/javascript">

    /*参考deptIncludeLower.js配置是否显示下级部门树，以及部门树相关操作*/
    /*当前部门树ID*/
    attGlobalDeptTreeId = "tree${uuid!}";
    /*当前gridName*/
    attGlobalGridName = "${gridName}";
    /*是否显示包含下级*/
    attGlobalShowIncludeLower = false;

</script>
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
        <@ZKUI.SearchTop>
        <tr>
            <td valign="middle">
                <@ZKUI.Input type="datetime" id="beginDate" endId="endTime" name="beginDate" todayRange="start" title="common_time_from" max="today" today="true" noOverToday="true" readonly="false"/>
                &nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
                <@ZKUI.Input type="datetime" id="endDate" name="endDate" title="common_to" max="today" todayRange="end" today="true" noOverToday="true" hideLabel="true" readonly="false"/>
            </td>
            <td valign="middle">
                <@ZKUI.Combo empty="true" id="realTimeStatus" name="realTimeStatus" readonly="true" title="common_status">
                    <option value="0"><@i18n 'att_realTime_signPers'/></option>
                    <option value="1"><@i18n 'att_rule_noSignIn'/></option>
                    <option value="2"><@i18n 'att_leftMenu_leave'/></option>
                </@ZKUI.Combo>
            </td>
        </tr>
        </@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    <@ZKUI.Layout style="position:absolute; top:70px;bottom:0px;left:0px;right:0px;height:auto;" pattern="2U" onInit="attGlobalInitDeptTree">
        <@ZKUI.Cell width="240" treeId="tree${uuid!}" >
            <@ZKUI.Tree dynamic="true" id="tree${uuid!}" url="authDepartment.do?dynaTree&showPersonCount=false" onClick="attGlobalDeptTreeClick"></@ZKUI.Tree>
        </@ZKUI.Cell>
        <@ZKUI.Cell hideHeader="true">
            <@ZKUI.Toolbar>
                <@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:signCallRollReport:refresh"/>
                <@ZKUI.ToolItem id="attSignCallRollReport.do?export" isShow="true" type="export" permission="att:signCallRollReport:export"/>
            </@ZKUI.Toolbar>
            <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttRealTimeCallRollItem" showColumns="!deptCode" query="attSignCallRollReport.do?list"/>
        </@ZKUI.Cell>
    </@ZKUI.Layout>
</@ZKUI.GridBox>