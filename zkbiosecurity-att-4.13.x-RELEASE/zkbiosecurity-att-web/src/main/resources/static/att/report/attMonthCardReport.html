<#assign gridName="attMonthCardReportGrid${uuid!}">

<script type="text/javascript">

    /*参考deptIncludeLower.js配置是否显示下级部门树，以及部门树相关操作*/
    /*当前部门树ID*/
    attGlobalDeptTreeId = "tree${uuid!}";
    /*当前gridName*/
    attGlobalGridName = "${gridName}";
    /*是否显示包含下级*/
    attGlobalShowIncludeLower = false;

</script>
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar onBeforeQuery="searchAttMonthDetail" onClear="updateSearchBarCondition" >
        <@ZKUI.SearchTop>
            <tr id="attMonthDetailSearchbar">
                <td valign="middle">
                    <@ZKUI.Input type="date" id="beginTime${uuid!}" endId="endTime${uuid!}" name="monthStart" title="common_time_from" todayRange="start" readonly="false"/>
                    &nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
                    <@ZKUI.Input type="date" id="endTime${uuid!}" name="monthEnd" title="common_to" todayRange="end" today="true" max="today" hideLabel="true" readonly="false"/>
                </td>
                <!-- 人员编号 -->
                <td valign="middle">
                    <@ZKUI.Input name="pin" id="pin${uuid!}" maxlength="30" title="att_person_pin" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="deptName" id="deptName${uuid!}"  maxlength="30" title="pers_dept_deptName" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
        <@ZKUI.SearchBelow>
        <tr>
            <td valign="middle">
                <@ZKUI.Input name="likeName" id="likeName${uuid!}"  maxlength="30" title="pers_person_wholeName" type="text"/>
            </td>
        </tr>
        </@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
    <@ZKUI.Layout id="layout${uuid}" style="position:absolute; top:70px;bottom:0px;left:0px;right:0px;height:auto;" onInit="attGlobalInitDeptTree">
        <@ZKUI.Cell width="240" treeId="tree${uuid!}" >
            <@ZKUI.Tree dynamic="true" id="tree${uuid!}" url="authDepartment.do?dynaTree&showPersonCount=false" onClick="attGlobalDeptTreeClick"></@ZKUI.Tree>
        </@ZKUI.Cell>
        <@ZKUI.Cell hideHeader="true">
            <@ZKUI.Toolbar>
                <@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:monthCardReport:refresh"/>
                <@ZKUI.ToolItem id="attMonthDetailReport.do?exportMonthCardReport" isShow="true" type="export" permission="att:monthCardReport:export"/>
            </@ZKUI.Toolbar>
            <div id="box${uuid}" style="width:100%;bottom:0px;top:38px;position:absolute;"></div>
        </@ZKUI.Cell>
    </@ZKUI.Layout>
</@ZKUI.GridBox>
<script type="text/javascript">

    attGlobalInitOrClearUUID = "${uuid!}";
    $(function () {
        updateSearchBarCondition("${uuid!}", "${gridName}");
        loadAttMonthGrid();
    });

    function updateSearchBarCondition(searchId, gridName) {
		setMonthFirstDay("beginTime" + "${uuid!}");
    	setNowDay("endTime" + "${uuid!}");
    };

    function searchAttMonthDetail() {
        loadAttMonthGrid();
        return false;
    };

    function loadAttMonthGrid() {
        var queryParam = {};
        if (ZKUI.Tree.get("tree${uuid!}")) {
            queryParam.deptId = ZKUI.Tree.get("tree${uuid!}").tree.getSelectedItemId();
        }
        var opts = {
            gridName:"${gridName}",
            monthStart: $("#attMonthDetailSearchbar input[name='monthStart']").val(),
            monthEnd: $("#attMonthDetailSearchbar input[name='monthEnd']").val(),
            vo:"com.zkteco.zkbiosecurity.att.vo.AttMonthDetailReportItem",
            query:"attMonthDetailReport.do?listMonthCardReport",
            queryParam:JSON.stringify(queryParam),
            pageList:true,
            limitCount:100000
        };
        $("#box${uuid}").loadGrid(opts)
    }

</script>


