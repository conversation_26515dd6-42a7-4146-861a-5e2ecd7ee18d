<#assign gridName="attViewDevCmdGrid${uuid!}">
<script type="text/javascript">
    function attConvertToLinkage(url) {
		if (url) {
			return "<img src='images/base_vid_capture.png' height='18px' style='cursor: pointer;margin-left:10px;' onmouseover='attShowCapturePhoto(\"" + url + "\")' onmouseout='$.jBox.close()' />";
		}
		return ""
	}

	function attShowCapturePhoto(url) {
		var capturePhotoHtml = "<div style='width: 95%; height: 95%; text-align: center;margin: 0 auto;padding-top: 5px'>" +
				"<img src='attTransaction.do?getDecryptFileBase64&path=" + url + "' onerror=\"this.src='/images/"+attUserImageBySkin()+"'\" style='height: 100%; width: 100%;' /></div>";
		$.jBox.open(capturePhotoHtml, "<@i18n 'att_statistical_attPhoto'/>", 250, 320, {
			id: "capturePhoto${uuid!}",
			opacity: 0,
			top: '25%',
			showIcon: 'jbox-title-icon-video',
			buttons: {}
		});
	}
</script>

<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttTransactionItem" query="attTransaction.do?getDayCardDetailView&attDayCardDetailId=${id!}&attDate=${attDate!}"/>
</@ZKUI.GridBox>

