<#assign gridName="attLeaveSummaryReportGrid${uuid!}">

<script type="text/javascript">

    /*参考deptIncludeLower.js配置是否显示下级部门树，以及部门树相关操作*/
    /*当前部门树ID*/
    attGlobalDeptTreeId = "tree${uuid!}";
    /*当前gridName*/
    attGlobalGridName = "${gridName}";
    /*是否显示包含下级*/
    attGlobalShowIncludeLower = false;

    function attLeaveViewDetail(v) {
        if(v == "0")
        {
            return v;
        }
        else
        {
            var value = v.split("_");
            return "<a href='javascript:viewLeaveDetail(\""+value[0]+"\",\""+value[1]+"\")'>"+value[2]+"</a>";
        }
    }

    function viewLeaveDetail(personPin, leaveTypeId) {
        var startTime = $("#beginTime${uuid!}").val();
        var endTime = $("#endTime${uuid!}").val();
        var path = "skip.do?page=att_report_attLeaveSummaryDetail&personPin=" + personPin + "&leaveTypeId=" + leaveTypeId + "&startTime=" + startTime + "&endTime=" + endTime;
        var opts = {
            path: path,
            width: 700,
            height: 300,
            title: "<@i18n 'att_statistical_leaveDetail'/>",
            gridName: "gridbox"
        };
        DhxCommon.createWindow(opts);
    }
</script>
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar onClear="updateSearchBarCondition">
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input type="datetime" id="beginTime${uuid!}" endId="endTime${uuid!}" name="startApplyDateTime" title="common_time_from" todayRange="start" readonly="false"/>
                    &nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
                    <@ZKUI.Input type="datetime" id="endTime${uuid!}" name="endApplyDateTime" title="common_to" max="today" todayRange="end" today="true" hideLabel="true" readonly="false"/>
                </td>
                <#if !user.staff>
	                <td valign="middle">
	                    <@ZKUI.Input name="personPin" id="pin${uuid!}" maxlength="30" title="att_person_pin" type="text"/>
	                </td>
	                <td valign="middle">
	                    <@ZKUI.Input name="deptName" id="deptName${uuid!}" title="pers_dept_deptName" type="text"/>
	                </td>
	            </#if>
            </tr>
        </@ZKUI.SearchTop>
        <#if !user.staff>
        <@ZKUI.SearchBelow>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="likeName" id="likeName${uuid!}" maxlength="30" title="pers_person_wholeName" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchBelow>
        </#if>
    </@ZKUI.Searchbar>
    <#if user.staff>
        <@ZKUI.Layout style="position:absolute; top:70px;bottom:0px;left:0px;right:0px;height:auto;" pattern="1C">
        <@ZKUI.Cell hideHeader="true">
            <@ZKUI.Toolbar>
                <@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:leaveSummaryReport:refresh"/>
                <@ZKUI.ToolItem id="attLeaveSummaryReport.do?export" isShow="true" type="export" permission="att:leaveSummaryReport:export"/>
            </@ZKUI.Toolbar>
            <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttLeaveSummaryReportItem" query="attLeaveSummaryReport.do?list"/>
        </@ZKUI.Cell>
    </@ZKUI.Layout> 
    </#if>
    <#if !user.staff>
	    <@ZKUI.Layout style="position:absolute; top:70px;bottom:0px;left:0px;right:0px;height:auto;" pattern="2U" onInit="attGlobalInitDeptTree">
	    	<@ZKUI.Cell width="240" treeId="tree${uuid!}" >
	            <@ZKUI.Tree dynamic="true" id="tree${uuid!}" url="authDepartment.do?dynaTree&showPersonCount=false" onClick="attGlobalDeptTreeClick"></@ZKUI.Tree>
	        </@ZKUI.Cell>
	        <@ZKUI.Cell hideHeader="true">
			    <@ZKUI.Toolbar>
			        <@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:leaveSummaryReport:refresh"/>
			        <@ZKUI.ToolItem id="attLeaveSummaryReport.do?export" isShow="true" type="export" permission="att:leaveSummaryReport:export"/>
			    </@ZKUI.Toolbar>
			    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttLeaveSummaryReportItem" query="attLeaveSummaryReport.do?list"/>
			</@ZKUI.Cell>
		</@ZKUI.Layout>	
	</#if>	
</@ZKUI.GridBox>
<script type="text/javascript">
    attGlobalInitOrClearUUID = "${uuid!}";
    $(function(){
        updateSearchBarCondition("${uuid!}", "${gridName}");
    });
    function updateSearchBarCondition(searchId, gridName){
		setMonthFirstDay("beginTime" + "${uuid!}");
    	setNowDay("endTime" + "${uuid!}");
    }
</script>