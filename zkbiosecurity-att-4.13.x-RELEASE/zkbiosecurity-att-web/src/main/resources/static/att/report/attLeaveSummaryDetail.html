<#include "/public/template/infoTemplate.html">
<#macro infoContent>
<script type="text/javascript">
	$("#closeButton").focus();

    leaveDetailGrid = new dhtmlXGridObject('attLeaveDetailGrid');
	leaveDetailGrid.setImagePath("/public/controls/dhtmlx/dhtmlxGrid/codebase/imgs/");
	leaveDetailGrid.setHeader("<@i18n 'att_person_pin'/>, <@i18n 'att_person_name'/>, <@i18n 'att_person_lastName'/>, <@i18n 'att_leftMenu_leaveType'/>, <@i18n 'common_startTime'/>, <@i18n 'common_endTime'/>");
	leaveDetailGrid.setColumnIds("personPin,personName,lastName,leaveType,startTime,endTime");
	leaveDetailGrid.setInitWidths("100, 100, 100, 100, 150,");
	leaveDetailGrid.setColAlign("left, left, left, left, left, left");
	leaveDetailGrid.setColTypes("ro,ro,ro,ro,ro,ro");
	leaveDetailGrid.enableAutoHeight(false);
	leaveDetailGrid.enableAutoWidth(true);
	leaveDetailGrid.init();
	leaveDetailGrid.setSkin(sysCfg.dhxSkin);
	leaveDetailGrid.setDateFormat(sysCfg.dhxLongDateFmt);

    <#if "${Application['system.language']}" == "zh_CN">
        leaveDetailGrid.setColumnHidden(2, true);
    </#if>

     //加载规则
    var personPin = "${personPin!}";
    var leaveTypeId = "${leaveTypeId!}";
    var startTime = "${startTime!}";
    var endTime = "${endTime!}";
    loadLeaveDetail();
    function loadLeaveDetail()
    {
		$.ajax({
		    type: "post",
		    url: "attLeaveSummaryReport.do?getLeaveDetail",
            data:{
                personPin: personPin,
                leaveTypeId: leaveTypeId,
                startTime: startTime,
                endTime: endTime
            },
		    dataType: "json",
		    async: true,
		    success: function(result)
		    {
		        var data = result.data;
				leaveDetailGrid.clearAll();
				var count = 0
				if(data != null && data.length > 0)
				{
					for(index in data)
					{
						var rowData = data[index];
						leaveDetailGrid.addRow(leaveDetailGrid.uid(), [rowData.personPin, rowData.personName, rowData.lastName, rowData.leaveType,rowData.startTime,rowData.endTime], index);
					}
					//leaveDetailGrid.sortRows(3, sort_custom,"des");//???
					count = leaveDetailGrid.getRowsNum();
				}
		    },
		    error:function (XMLHttpRequest, textStatus, errorThrown)
		    {
	 			messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>" + "-628"});
	 		}
		});
	}

</script>

<div id="attLeaveDetailGrid" style="height: 200px; border: 1px solid Silver;"></div>
</#macro>