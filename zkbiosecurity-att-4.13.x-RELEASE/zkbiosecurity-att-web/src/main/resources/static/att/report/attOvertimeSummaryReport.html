<#assign gridName="attOvertimeSummaryReportGrid${uuid!}">
<#assign treeId="departmentTree${uuid!}">
<script type="text/javascript">

    /*参考deptIncludeLower.js配置是否显示下级部门树，以及部门树相关操作*/
    /*当前部门树ID*/
    attGlobalDeptTreeId = "tree${uuid!}";
    /*当前gridName*/
    attGlobalGridName = "${gridName}";
    /*是否显示包含下级*/
    attGlobalShowIncludeLower = false;

</script>
<@ZKUI.GridBox gridName="${gridName}" showColumns="!shouldHour,actualHour,actualMinuteGe,validHour,lateMinute,lateCountTotal,earlyMinute,earlyCount,absentHour,attMonthDetailReportLeaveHourMap,${showColumns}">
    <@ZKUI.Searchbar onClear="updateSearchBarCondition">
        <@ZKUI.SearchTop>
             <tr>
                 <td valign="middle">
                     <@ZKUI.Input type="date" id="beginTime${uuid!}" endId="endTime${uuid!}" name="monthStart" title="common_time_from" todayRange="start" max="today" readonly="false"/>
                     &nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
                     <@ZKUI.Input type="date" id="endTime${uuid!}" name="monthEnd" title="common_to" todayRange="end" today="true" hideLabel="true" max="today" readonly="false"/>
                 </td>
                 <td valign="middle">
	                    <@ZKUI.Input name="pin" id="pin${uuid!}" maxlength="30" title="att_person_pin" type="text"/>
                 </td>
                 <td valign="middle">
	                     <@ZKUI.Input name="deptName" id="deptName${uuid!}" maxlength="30" title="pers_dept_deptName" type="text"/>
                 </td>
            </tr>
        </@ZKUI.SearchTop>
        <@ZKUI.SearchBelow>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="likeName" id="likeName${uuid!}" maxlength="30" title="pers_person_wholeName" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
    <@ZKUI.Layout style="position:absolute; top:70px;bottom:0px;left:0px;right:0px;height:auto;" onInit="attGlobalInitDeptTree">
        <@ZKUI.Cell width="240" treeId="tree${uuid!}" >
            <@ZKUI.Tree dynamic="true" id="tree${uuid!}" url="authDepartment.do?dynaTree&showPersonCount=false" onClick="attGlobalDeptTreeClick"></@ZKUI.Tree>
        </@ZKUI.Cell>
        <@ZKUI.Cell hideHeader="true">
            <@ZKUI.Toolbar>
                <@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:overtimeSummaryReport:refresh"/>
                <@ZKUI.ToolItem type="export" id="attMonthStatisticalReport.do?exportOvertimeSummaryReport" permission="att:overtimeSummaryReport:export"/>
            </@ZKUI.Toolbar>
            <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttMonthStatisticalReportItem" query="attMonthStatisticalReport.do?listOvertimeSummaryReport"/>
        </@ZKUI.Cell>
    </@ZKUI.Layout>
</@ZKUI.GridBox>
<script type="text/javascript">
    attGlobalInitOrClearUUID = "${uuid!}";
    $(function(){
        updateSearchBarCondition("${uuid!}", "${gridName}");
    });
    function updateSearchBarCondition(searchId, gridName){
		setMonthFirstDay("beginTime" + "${uuid!}");
    	setNowDay("endTime" + "${uuid!}");
    }
</script>