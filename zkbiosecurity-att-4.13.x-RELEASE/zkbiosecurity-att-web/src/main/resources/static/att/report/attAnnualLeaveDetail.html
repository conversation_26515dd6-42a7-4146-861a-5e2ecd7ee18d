<#include "/public/template/infoTemplate.html">
<#macro infoContent>
<div id="attLeaveDetailGrid${uuid}!" style="height:300px;"></div>

<script type="text/javascript">

$(function(){

    initLeaveDetailGrid();
    function initLeaveDetailGrid() {
        annualLeaveDetailGrid = new dhtmlXGridObject('attLeaveDetailGrid${uuid}!');
        annualLeaveDetailGrid.setImagePath("/public/controls/dhtmlx/dhtmlxGrid/codebase/imgs/");
        annualLeaveDetailGrid.setHeader("<@i18n 'att_person_pin'/>, <@i18n 'att_person_name'/>, <@i18n 'common_startTime'/>, <@i18n 'common_endTime'/>, <@i18n 'common_remark'/>");
        annualLeaveDetailGrid.setColumnIds("personPin,personName,leaveType,startTime,endTime");
        annualLeaveDetailGrid.setInitWidths("100, 100, 150, 150, *");
        annualLeaveDetailGrid.setColAlign("left, left, left, left, left");
        annualLeaveDetailGrid.setColTypes("ro,ro,ro,ro,ro");
        annualLeaveDetailGrid.enableAutoHeight(false);
        annualLeaveDetailGrid.enableAutoWidth(false);
        annualLeaveDetailGrid.init();
        annualLeaveDetailGrid.setSkin(sysCfg.dhxSkin);
        annualLeaveDetailGrid.setDateFormat(sysCfg.dhxLongDateFmt);
        loadLeaveDetailGridData();
    }

    function loadLeaveDetailGridData() {
        $.ajax({
            type: "post",
            url: "attAnnualLeaveReport.do?getDetail",
            data:{
                personId: "${personId!}"
            },
            dataType: "json",
            async: true,
            success: function(result)
            {
                var data = result.data;
                annualLeaveDetailGrid.clearAll();
                var count = 0
                if(data != null && data.length > 0)
                {
                    for(index in data)
                    {
                        var rowData = data[index];
                        annualLeaveDetailGrid.addRow(annualLeaveDetailGrid.uid(), [rowData.personPin, rowData.personName,rowData.startTime,rowData.endTime,rowData.remarks], index);
                    }
                    count = annualLeaveDetailGrid.getRowsNum();
                }
            },
            error:function (XMLHttpRequest, textStatus, errorThrown)
            {
                messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>" + "-628"});
            }
        });
    }

});
</script>
</#macro>