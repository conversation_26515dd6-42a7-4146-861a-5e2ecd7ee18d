<#assign gridName="attEarlyReportGrid${uuid!}">
<script type="text/javascript">

    /*参考deptIncludeLower.js配置是否显示下级部门树，以及部门树相关操作*/
    /*当前部门树ID*/
    attGlobalDeptTreeId = "tree${uuid!}";
    /*当前gridName*/
    attGlobalGridName = "${gridName}";
    /*是否显示包含下级*/
    attGlobalShowIncludeLower = false;

</script>
<@ZKUI.GridBox gridName="${gridName}" showColumns="!attDayDetailReportLeaveMinuteMap,absentConvert,overtimeMinuteConvert,overtimeHolidayConvert,overtimeRestConvert,overtimeUsualConvert,lateMinuteTotalConvert,lateMinuteData,lateCountTotal,shouldConvert,actualConvert,validConvert,overTimeOT1,overTimeOT2,overTimeOT3">
    <@ZKUI.Searchbar onClear="updateSearchBarCondition">
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input type="date" id="beginTime${uuid!}" endId="endTime${uuid!}" name="startDatetimeBegin" title="common_time_from" todayRange="start" readonly="false"/>
                    &nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
                    <@ZKUI.Input type="date" id="endTime${uuid!}" name="startDatetimeEnd" title="common_to" max="today" todayRange="end" today="true" hideLabel="true" readonly="false"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="personPin" id="pin${uuid!}" maxlength="30" title="att_person_pin" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="deptName" id="deptName${uuid!}" maxlength="30" title="pers_dept_deptName" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
        <@ZKUI.SearchBelow>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="likeName" id="likeName${uuid!}" maxlength="30" title="pers_person_wholeName" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>

    <@ZKUI.Layout style="position:absolute; top:70px;bottom:0px;left:0px;right:0px;height:auto;" pattern="2U" onInit="attGlobalInitDeptTree">
        <@ZKUI.Cell width="240" treeId="tree${uuid!}" >
            <@ZKUI.Tree dynamic="true" id="tree${uuid!}" url="authDepartment.do?dynaTree&showPersonCount=false" onClick="attGlobalDeptTreeClick"></@ZKUI.Tree>
        </@ZKUI.Cell>
        <@ZKUI.Cell hideHeader="true">
            <@ZKUI.Toolbar>
                <@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:earlyReport:refresh"/>
                <@ZKUI.ToolItem type="export" id="attDayDetailReport.do?exportEarlyReport" permission="att:earlyReport:export"/>
            </@ZKUI.Toolbar>
            <@ZKUI.Grid pageList="true" limitCount="100000" vo="com.zkteco.zkbiosecurity.att.vo.AttDayDetailReportItem" query="attDayDetailReport.do?listEarlyReport"/>
        </@ZKUI.Cell>
    </@ZKUI.Layout>
</@ZKUI.GridBox>
<script type="text/javascript">
    attGlobalInitOrClearUUID = "${uuid!}";
    $(function(){
        updateSearchBarCondition("${uuid!}", "${gridName}");
    });
    function updateSearchBarCondition(searchId, gridName){
		setMonthFirstDay("beginTime" + "${uuid!}");
    	setNowDay("endTime" + "${uuid!}");
    }
</script>