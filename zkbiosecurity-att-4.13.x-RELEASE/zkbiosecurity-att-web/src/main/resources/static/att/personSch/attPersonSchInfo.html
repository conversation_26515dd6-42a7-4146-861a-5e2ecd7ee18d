<#include '/public/template/editTemplate.html'>
<#macro editContent>
<#assign editPage = true>
<div class="content_div attPersonSchInfoView">
</div>
<div>
	<input id="schId${uuid!}" type="hidden"/>
	<div class="nowDay">
		<span class="pre" id="pre${uuid!}"><<</span>
		<span id="todayDay${uuid!}"></span>
		<span class="next" id="next${uuid!}">>></span>
		<div style="float: right;margin: 5px 10px 0px 0px;" class="attTooltip">
			<span class="att_icv att_icv-green icv-ic_que zk-colors-fg-green" />
			<div class="attCommonTipClass attCommonTipClassLeft">
				<p class="warningColor" title="<@i18n 'att_schedule_normalSchInfo'/>"><@i18n 'att_schedule_normalSchInfo'/></p>
				<p class="warningColor" title="<@i18n 'att_schedule_multipleInterSchInfo'/>"><@i18n 'att_schedule_multipleInterSchInfo'/></p>
				<p class="warningColor" title="<@i18n 'att_schedule_inderSchFirstDayInfo'/>"><@i18n 'att_schedule_inderSchFirstDayInfo'/></p>
				<p class="warningColor" title="<@i18n 'att_schedule_inderSchSecondDayInfo'/>"><@i18n 'att_schedule_inderSchSecondDayInfo'/></p>
			</div>
		</div>
	</div>
	<div class="attSchBoxDiv">
		<div class="dateBox">
			<input id="dateSelected${uuid!}" type="hidden"/>

			<div class="allTime">
				<div class="weekBox">
					<ul>
						<!-- 周日 -->
						<li class="required"><@i18n "common_abb_sunday"/></li>
						<!-- 周一 -->
						<li><@i18n "common_abb_monday"/></li>
						<!-- 周二 -->
						<li><@i18n "common_abb_tuesday"/></li>
						<!-- 周三 -->
						<li><@i18n "common_abb_wednesday"/></li>
						<!-- 周四 -->
						<li><@i18n "common_abb_thursday"/></li>
						<!-- 周五 -->
						<li><@i18n "common_abb_friday"/></li>
						<!-- 周六 -->
						<li class="required"><@i18n "common_abb_saturday"/></li>
					</ul>
				</div>
				<div class="monthBox" id="monthBox${uuid!}">
					<ul class="attCalendarUl"></ul>
				</div>
			</div>
		</div>
	</div>
</div>
<script type='text/javascript'>
	$(function() {

		/*排班详情*/
		DhxCommon.getCurrentWindow().setText("<@i18n 'auth_license_details'/>");

		/*初始化日期控件*/
		setMonthAndYear(month, year, "${uuid!}");
		bindPreNextClick("${uuid!}", "Person");
		$("#schId${uuid!}").val("pers_${id!}");
		listJsonSch("${uuid!}", "Person");
	});
</script>
</#macro>
<#macro buttonContent>
	<button class='button-form button-close' onclick="DhxCommon.closeWindow()"><@i18n 'common_op_close'/></button>
</#macro>