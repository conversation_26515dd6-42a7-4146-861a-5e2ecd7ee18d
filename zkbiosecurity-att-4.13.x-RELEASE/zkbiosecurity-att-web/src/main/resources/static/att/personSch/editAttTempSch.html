<#include '/public/template/editTemplate.html'>
<#macro editContent>
<#assign editPage = true>
<style>
	.attTimeSlotTree {
		background: transparent;
		background-color: transparent;
	}
</style>
<@ZKUI.Layout pattern="4G" style="height:100%;width:100%;">
	<@ZKUI.Cell fixSize="1" width="360" height="400" hideArrow="true" title="att_leftMenu_timeSlot">
		<div class="attTimeSlotTree" id="attTimeSlotTree${uuid}" ></div>
	</@ZKUI.Cell>
	<@ZKUI.Cell hideArrow="true" hideHeader="true">
		<form action='attTempSch.do?saveTempSch' method='post' id='${formId}' enctype='multipart/form-data'>
			<input type="hidden" name="id" value="${(item.id)!}" />
			<input type="hidden" name="tempType" value="${type!}" />
			<input type="hidden" name="addIds" value="${ids!}" />
			<input id="idDXCalendarEvents" type="hidden" name="dXCalendarEvent" />
			<input type="hidden" name="includeLower" id="includeLower${uuid}" value="" />

			<table class='tableStyle'>
<!--				<tr style="display: none">-->
<!--                    <th><label><#if type=='1'><@i18n 'att_common_dept'/><#elseif type=='0'><@i18n 'att_common_groupName'/><#else><@i18n 'att_common_person'/></#if></label></th>-->
<!--					<td><textarea name="showInfo" readonly rows='8' style='resize: none;padding: 4px;vertical-align: bottom;width: 190px;background-color: #eee;' title="${(showInfo)!}" >${(showInfo)!}</textarea></td>-->
<!--				</tr>-->
				<tr>
					<!-- 工作类型 -->
					<th>
						<label><@i18n 'att_shift_workType'/></label><span class='required'>*</span>
						<span class="attTooltip">
                            <span class="att_icv att_icv-green icv-ic_que zk-colors-fg-green" />
                            <span class="attCommonTipClass" style="width: 220px">
                                <p class="warningColor"><@i18n 'att_personSch_shiftWorkTypeTip1'/></p>
								<p class="warningColor"><@i18n 'att_personSch_shiftWorkTypeTip2'/></p>
                            </span>
                        </span>
					</th>
					<td>
						<@ZKUI.Combo id="shiftWorkType" empty="false" width="198" value="${(item.workType)!}" hideLabel="true" name="workType">
							<option value="normalWork"><@i18n 'att_shift_normalWork'/></option>
							<option value="weekendOt"><@i18n 'att_overtime_rest'/></option>
							<option value="holidayOt"><@i18n 'att_shift_holidayOt'/></option>
						</@ZKUI.Combo>
					</td>
				</tr>
				<tr>
					<!--考勤方式 -->
					<th><label><@i18n 'att_shift_attendanceMode'/></label><span class='required'>*</span></th>
					<td>
						<@ZKUI.Combo id="attendanceMode${uuid!}" width="198" empty="false" value="${(item.attendanceMode)!}" hideLabel="true" name="attendanceMode">
							<option value="0"><@i18n 'att_shift_shiftNormal'/></option>
							<option value="1"><@i18n 'att_shift_oneDayOneCard'/></option>
							<option value="3"><@i18n 'att_shift_notBrushCard'/></option>
						</@ZKUI.Combo>
					</td>
				</tr>
				<tr id="overTimeTag">
					 <!--加班方式 -->
					<th><label><@i18n 'att_shift_overtimeMode'/></label><span class='required'>*</span></th>
					<td>
						<@ZKUI.Combo id="overtimeMode${uuid!}" width="198" empty="false" value="${(item.overtimeMode)!}" hideLabel="true" name="overtimeMode">
							<option value="0"><@i18n 'att_shift_autoCalc'/></option>
							<option value="1"><@i18n 'att_shift_mustApply'/></option>
							<!-- <option value="2"><@i18n 'att_shift_mustOvertime'/></option> -->
							<!--<option value="3"><@i18n 'att_shift_timeSmaller'/></option>-->
							<option value="4"><@i18n 'att_shift_notOvertime'/></option>
						</@ZKUI.Combo>
					</td>
				</tr>
			</table>
		</form>
	</@ZKUI.Cell>

    <@ZKUI.Cell fixSize="1" hideHeader="true" height="130">
        <div class="attTPersonSchOpTip warningColor">
            <div>
                <div><@i18n 'att_personSch_opTip'/></div>
                <div><@i18n 'att_personSch_opTip1'/></div>
                <div><@i18n 'att_personSch_opTip2'/></div>
                <div><@i18n 'att_personSch_opTip3'/></div>
            </div>
        </div>
    </@ZKUI.Cell>
	<@ZKUI.Cell hideHeader="true" height="180">
    <div class="attTooltip attTooltipDX">
		<span class="att_icv att_icv-green icv-ic_que zk-colors-fg-green" />
        <div class="attCommonTipClass attSchTipDX">
            <p class="warningColor" title="<@i18n 'att_schedule_normalSchInfo'/>"><@i18n 'att_personSch_schRules'/></p>
            <p class="warningColor" title="<@i18n 'att_personSch_schRules1'/>"><@i18n 'att_personSch_schRules1'/></p>
            <p class="warningColor" title="<@i18n 'att_personSch_schRules2'/>"><@i18n 'att_personSch_schRules2'/></p>
            <p class="warningColor" title="<@i18n 'att_personSch_schRules3'/>"><@i18n 'att_personSch_schRules3'/></p>
        </div>
    </div>
	<div id="attScheduler${uuid}" class="dhx_cal_container" style='width:100%; height:100%;'>
		<div class="dhx_cal_navline" style='background: #fff;background-color: #fff;'>
			<div class="dhx_cal_prev_button" style="background-image:none;text-align: center;"><<</div>
			<div class="dhx_cal_next_button" style="background-image:none;text-align: center;">>></div>
			<div class="dhx_cal_today_button" ></div>
			<div class="dhx_cal_date"></div>
		</div>
		<div class="dhx_cal_header" style='background: #fff;background-color: #fff;'>
		</div>
		<div class="dhx_cal_data" style='background: #fff;background-color: #fff;'>
		</div>
	</div>
	</@ZKUI.Cell>
</@ZKUI.Layout>
<script type='text/javascript'>

	$(function() {

		//工作类型选择事件
        attChangeWorkType("${(item.workType)!}");
		ZKUI.Combo.get("shiftWorkType").combo.attachEvent("onChange",function(value,text) {
			attChangeWorkType(value);
		});
		function attChangeWorkType(value) {

			// 根据班次类型，动态变更加班方式
            var overtimeModeCombo = ZKUI.Combo.get("overtimeMode${uuid!}").combo;
            var overtimeModeValue = overtimeModeCombo.getSelectedValue();
            overtimeModeCombo.clearAll();
            if(value != "" && value != "normalWork") {
                overtimeModeCombo.addOption([
                     {"value": "0", "text": "<@i18n 'att_shift_autoCalc'/>"},
                     {"value": "1", "text": "<@i18n 'att_shift_mustApply'/>"},
                ]);
                if (overtimeModeValue == 4) {
                    overtimeModeCombo.setComboValue(0);
                } else {
                    overtimeModeCombo.setComboValue(overtimeModeValue);
                }

            } else {
                overtimeModeCombo.addOption([
                    {"value": "0", "text": "<@i18n 'att_shift_autoCalc'/>"},
                    {"value": "1", "text": "<@i18n 'att_shift_mustApply'/>"},
                    {"value": "4", "text": "<@i18n 'att_shift_notOvertime'/>"},
                ]);
                overtimeModeCombo.setComboValue(overtimeModeValue);
            }

			var checkFlag = true;
            if(value != "" && value != "normalWork") {
                checkFlag = false;
                ZKUI.Combo.get("attendanceMode${uuid!}").combo.setComboValue("0");
            }
            ZKUI.Combo.get("attendanceMode${uuid!}").combo.disable(!checkFlag);
		}

		/*考勤方式选择事件*/
        attChangeAttendanceMode("${(item.attendanceMode)!}");
		ZKUI.Combo.get("attendanceMode${uuid!}").combo.attachEvent("onChange",function(value,text){
			attChangeAttendanceMode(value);
		});
		function attChangeAttendanceMode(value)
		{
			// 免打卡或者一天内任打一次有效卡，不统计加班
            if (value == "1" || value == "3") {
                // 加班方式设置为不算加班
                ZKUI.Combo.get("overtimeMode${uuid!}").combo.setComboValue("4");
                // 加班方式设置不可修改
                ZKUI.Combo.get("overtimeMode${uuid!}").combo.disable(true);
            } else {
                // 加班方式可修改
                ZKUI.Combo.get("overtimeMode${uuid!}").combo.disable(false);
                var workType = ZKUI.Combo.get("shiftWorkType").combo.getSelected();
                // 防止未加载完成，取不到值
                if(workType == null)
                {
                    workType = "${(item.workType)!}";
                }
                attChangeWorkType(workType);
            }
		}

		/*初始化时间段map集合，用于前端判断时间段是否重复*/
		initAttTimeSlotMap();

		/*初始化下拉树，并添加拖拽事件*/
		initAttTimeSlotTree("attTimeSlotTree${uuid}");
		/*初始化日历控件*/
		initAttScheduler("attScheduler${uuid}");
		// 初始化之后，清空事件！！！
		scheduler.clearAll();

		/*编辑事件回填*/
		if ("${dXCalendarEvent}") {
			scheduler.parse([
				{
					start_date: "${((dXCalendarEvent.start_date)?string('yyyy-MM-dd HH:mm:ss'))!}",
					end_date: "${((dXCalendarEvent.end_date)?string('yyyy-MM-dd HH:mm:ss'))!}",
					text: "${(dXCalendarEvent.text)!}",
					attTimeSlotIds: "${(dXCalendarEvent.attTimeSlotIds)!}"
				}
			],"json");
		}
	});


	$('#${formId}').validate({
		debug : true,
		submitHandler : function()
		{
			if (scheduler.getEvents() && scheduler.getEvents().length > 0) {
				$("#idDXCalendarEvents").val(JSON.stringify(scheduler.getEvents()))
				 $("#includeLower${uuid}").val(attGlobalIsIncludeLower);
				<@submitHandler callBackFun="attPersonSchReloadGrid();"/>
			} else {
				openMessage(msgType.info, "<@i18n 'att_timeSlot_select'/>");
			}
		}
	});
</script>
</#macro>