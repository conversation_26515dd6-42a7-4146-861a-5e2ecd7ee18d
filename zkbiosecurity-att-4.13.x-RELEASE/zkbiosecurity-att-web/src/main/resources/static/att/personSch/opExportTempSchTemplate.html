<#assign editPage = "true">
<#include "/public/template/editTemplate.html">
<#macro editContent>
<form method='POST' action='${actionName}' id='${formId}' enctype='multipart/form-data'>
	<input type="hidden" name="jsonColumn" id="jsonColumn${uuid}"/>
	<input type="hidden" name="pageXmlPath" id="pageXmlPath${uuid}"/>
	<input type="hidden" name="tableNameParam" value="<@i18n 'att_personSch_tempSchTemplate'/>"/>
	<input type="hidden" name="logMethod" value="<@i18n 'att_personSch_exportTemplate'/>">
	<input type="hidden" name="exportType" value="1">
	<table class="tableStyle">
		<tbody>
			<tr><td></td></tr>
			<tr>
				<th><label><@i18n 'common_report_fileType'/></label><span class='required'>*</span></th>
				<td>
					<select class='exportselect' name='reportType' id='reportType${uuid}'>
					 	<option value='XLS'><@i18n 'common_report_excel'/></option>
					</select>
				</td>
			</tr>
			<tr>
				<th><label><@i18n 'common_op_startTime'/></label><span class='required'>*</span></th>
				<td><@ZKUI.Input endId="endTime${uuid}" id="startTime${uuid}" name="startTime" today="0" title="common_op_startTime" type="date" hideLabel="true" readonly="true"/></td>
			</tr>
			<tr>
				<th><label><@i18n 'common_op_endTime'/></label><span class='required'>*</span></th>
				<td><@ZKUI.Input id="endTime${uuid}" name="endTime" title="common_op_endTime" today="0" offsetField="M" offset="1" type="date" hideLabel="true" readonly="true"/></td>
			</tr>
			<tr>
				<th colspan="2"><span style="margin-top: 2px" class="warningImage"></span><span class="warningColor"><@i18n 'att_personSch_tempSchTemplateTip'/></span></th>
			</tr>
		</tbody>
	</table>
</form>
</#macro>
<script type="text/javascript">
$().ready(function(){

    var columns = {personPin: "", personName: "", deptName: "", workType: "", map: ""};
	document.getElementById("jsonColumn${uuid}").value = JSON.stringify(columns);
	document.getElementById("pageXmlPath${uuid}").value = "com.zkteco.zkbiosecurity.att.vo.AttTempSchItem";

	$("#${formId}").validate({
		debug : true,
		rules :
		{
			'startTime' :
			{
				required : true
			},
			'endTime' :
			{
				required : true
			}
		},
		submitHandler: function(form){
			openMessage(msgType.loading);
			$('#${formId}').ajaxSubmit({
				async:false,
				success: function(result){
					dealRetResult(result);
				}
			});
			setTimeout(getExportResult, 2000);
		}
	});

	function getExportResult() {
		$.ajax({
			url: "skip.do?getExportResult",
			success: function (res) {
				if (res[sysCfg.data] == "end") {
					closeMessage();
					DhxCommon.closeWindow();
				} else {
					setTimeout(getExportResult, 3000);
				}
			}
		});
	}
});
</script>
