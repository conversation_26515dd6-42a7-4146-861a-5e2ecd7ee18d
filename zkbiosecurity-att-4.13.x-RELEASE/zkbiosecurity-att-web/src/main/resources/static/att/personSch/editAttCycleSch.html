<#include '/public/template/editTemplate.html'>
<#macro editContent>
<#assign editPage = true>
<style>
	.attPersonSchCycleInput input {
		width: 193px!important;
	}

</style>
<@ZKUI.Layout pattern="2E" style="height:100%;width:100%;">
	<@ZKUI.Cell hideHeader="true" height="130">
		<form action='attCycleSch.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
			<input name="id" type="hidden" value="${item.id!}" />
			<input id="idShiftIds" name="shiftIds" type="hidden" value="${item.shiftIds!}" />
			<!--周期排班类型（分组0/部门1/人员2）-->
			<input type="hidden" name="cycleType" value="${type!}" />
			<input type="hidden" name="addIds" value="${ids!}" />
			<input type="hidden" name="deptId" value="${deptId!}" />
			<input type="hidden" name="personId" value="${personId!}" />
            <input type="hidden" name="groupId" value="${groupId!}" />
			<input type="hidden" name="includeLower" id="includeLower${uuid}" value="" />

			<table class='tableStyle'>
				<!--<tr style="display: none">
					<th><label><#if type=='1'><@i18n 'att_common_dept'/><#elseif type=='0'><@i18n 'att_common_groupName'/><#else><@i18n 'att_common_person'/></#if></label></th>
					<td><textarea name="showInfo" readonly rows='8' style='resize: none;padding: 4px;vertical-align: bottom;width: 190px;background-color: #eee;' title="${(showInfo)!}">${(showInfo)!}</textarea></td>
				</tr>-->
				<tr>
					<!-- 班次类型 -->
					<th><label><@i18n 'att_schedule_type'/></label><span class='required'>*</span></th>
					<td>
						<@ZKUI.Combo id="idScheduleType${uuid}" empty="false" width="198" value="${(item.scheduleType)!}" hideLabel="true" name="scheduleType">
							<option value="0"><@i18n 'att_schedule_normal'/></option>
							<option value="1"><@i18n 'att_schedule_intelligent'/></option>
						</@ZKUI.Combo>
					</td>
				</tr>
				<tr>
					<!-- 开始时间 -->
					<th><label><@i18n 'common_startTime'/></label><span class='required'>*</span></th>
					<td class="attPersonSchCycleInput"><@ZKUI.Input endId="idEndDate" id="idStartDate" readonly="true" type="date" value="${((item.startDate)?string('yyyy-MM-dd'))!}" name="startDate" hideLabel="true"/></td>
				</tr>
				<tr>
					<!-- 结束时间 -->
					<th><label><@i18n 'common_endTime'/></label><span class='required'>*</span></th>
					<td class="attPersonSchCycleInput">
						<@ZKUI.Input id="idEndDate" readonly="true" type="date" value="${((item.endDate)?string('yyyy-MM-dd'))!}" name="endDate" hideLabel="true"/>
					</td>
				</tr>
			</table>
		</form>
	</@ZKUI.Cell>
	<@ZKUI.Cell hideHeader="true">
		<div id="box${uuid}" style="width: 100%;height: 100%;"></div>
	</@ZKUI.Cell>
</@ZKUI.Layout>
<script type='text/javascript'>

$('#${formId}').validate({
	debug : true,
	rules :
	{
		'startDate' :
		{
			required : true
		},
		'endDate' :
		{
			required : true
		}
	},
	submitHandler : function()
	{
		var isChoose = $("#idShiftIds").val();
	    if (!isChoose) {
	    	//请选择相应的数据！
			messageBox({messageType: "alert", text: "<@i18n 'att_schedule_arrange'/>"});
		   	return false;
		}
	    
	    $("#includeLower${uuid}").val(attGlobalIsIncludeLower);

		<@submitHandler callBackFun="attPersonSchReloadGrid();"/>
	}
});

function loadSchShiftGrid(idScheduleType) {
	var scheduleType = ZKUI.Combo.get(idScheduleType).combo.getSelected();
    var opts = {
        gridName: "attSchShiftGrid${uuid}",//设置grid名称
        vo: "com.zkteco.zkbiosecurity.att.vo.AttShiftSchItem",//设置vo
        nopaging: true,//隐藏分页栏
        query: "attShift.do?allList",//查询请求
        queryParam: JSON.stringify({scheduleType: scheduleType}),//查询参数
        onCheck: "onCheck",
        onXLE: "onXLE",
    };
    //加载Grid
    $("#box${uuid}").loadGrid(opts);
}

function onCheck(rId, cInd, state){
	var shiftIds = $("#idShiftIds").val();
    var tsIdArray = [];
    if (shiftIds) {
        tsIdArray = shiftIds.split(",");
    }
    if (state) {
		var scheduleType = ZKUI.Combo.get("idScheduleType${uuid}").combo.getSelected();
        if (scheduleType == 0 && tsIdArray.length > 0) {
        	ZKUI.Grid.get("attSchShiftGrid${uuid}").grid.cells(rId, 0).setValue(0);
            //普通排班 只能选择一个班次！
            messageBox({messageType: "alert", text: "<@i18n 'att_schedule_selectOne'/>"});
        } else {
            //搜索数组中指定值并返回它的索引（如果没有找到则返回-1）
            var index = $.inArray(rId, tsIdArray);
            if (index == -1) {
                tsIdArray.push(rId);
            }
        }
    } else {
        tsIdArray.splice($.inArray(rId, tsIdArray), 1);
    }
    $("#idShiftIds").val(tsIdArray.join(","));
}

function onXLE() {
	var shiftIds = $("#idShiftIds").val();
    var tsIdArray = shiftIds.split(",");
    ZKUI.Grid.get("attSchShiftGrid${uuid}").grid.forEachRow(function(id) {
        var index = $.inArray(id, tsIdArray);
        if (index > -1) {
        	ZKUI.Grid.get("attSchShiftGrid${uuid}").grid.setRowTextStyle(id, "color:blue;");
        	ZKUI.Grid.get("attSchShiftGrid${uuid}").grid.cells(id, 0).setValue(1);
        }
    });
    $("#attSchShiftGrid${uuid} img[checkhead='true']").hide();
}


$(function(){
    var idScheduleType = "idScheduleType${uuid}";
    loadSchShiftGrid(idScheduleType);
    ZKUI.Combo.get(idScheduleType).combo.attachEvent("onChange",function(){
        $("#idShiftIds").val("");
        loadSchShiftGrid(idScheduleType);
    });

});
</script>
</#macro>