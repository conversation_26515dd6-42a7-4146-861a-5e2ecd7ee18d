<#assign gridName="attPersonSchGrid${uuid!}">
<script type="text/javascript">

	/*参考deptIncludeLower.js配置是否显示下级部门树，以及部门树相关操作*/
	/*当前部门树ID*/
	attGlobalDeptTreeId = "attPersonSchDeptTree";
	/*当前gridName*/
	attGlobalGridName = "${gridName}";
	/*是否显示包含下级*/
	attGlobalShowIncludeLower = true;
	/*当前选中ID*/
	attGlobalDeptTreeClickId = "";

	/*导出临时排班模版*/
	function attOpExportTempSchTemplate(id, bar, opts) {
		if (bar) {
			var gridName = bar.gridName;
			opts.path = opts.path || "skip.do?page=att_personSch_opExportTempSchTemplate&gridName=" + gridName + "&actionName=" + encodeURIComponent(id);
			if (opts.maxExportCount) {
				opts.path = opts.path + "&maxExportCount=" + opts.maxExportCount;
			}
			opts.width = opts.width || 550;
			opts.height = opts.height || 250;
			DhxCommon.createWindow(opts);
		}
	}

	/*导出临时排班模版*/
	function attOpExportCycleSchTemplate(id, bar, opts) {
		if (bar) {
			var gridName = bar.gridName;
			opts.path = opts.path || "skip.do?page=att_personSch_opExportCycleSchTemplate&gridName=" + gridName + "&actionName=" + encodeURIComponent(id);
			if (opts.maxExportCount) {
				opts.path = opts.path + "&maxExportCount=" + opts.maxExportCount;
			}
			opts.width = opts.width || 550;
			opts.height = opts.height || 250;
			DhxCommon.createWindow(opts);
		}
	}

	/*新增/导入后刷新*/
	function attPersonSchReloadGrid(){
		ZKUI.Grid.reloadGrid("${gridName}");
	}

</script>
<@ZKUI.GridBox gridName="${gridName}" showColumns="!likeName">
	<@ZKUI.Searchbar onBeforeQuery="loadAttPersonSchGrid">
		<@ZKUI.SearchTop>
			<tr>
				<td valign="middle" id="attPersonSchDateSearch">
					<@ZKUI.Input type="date" id="startDate${uuid!}" endId="endDate${uuid!}" name="startDate" title="common_time_from" todayRange="start" today="true" readonly="false"/>
					&nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
					<@ZKUI.Input type="date" id="endDate${uuid!}"  name="endDate" title="common_to" today="true" offset="1" hideLabel="true" readonly="false"/>
				</td>
				<#if !staff>
					<td valign="middle">
						<@ZKUI.Input name="personPin" maxlength="30" title="att_person_pin" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Combo empty="true" name="schStatus" readonly="true" title="att_personSch_schStatus">
							<option value="0"><@i18n 'att_personSch_sched'/></option>
							<option value="1"><@i18n 'att_schedule_noSchDetail'/></option>
						</@ZKUI.Combo>
					</td>
				</#if>
			</tr>
		</@ZKUI.SearchTop>
		<@ZKUI.SearchBelow>
		<tr>
			<td valign="middle">
				<@ZKUI.Input name="likeName"  maxlength="30" title="pers_person_wholeName" type="text"/>
			</td>
			<td valign="middle">
				<@ZKUI.Input name="deptName" title="pers_dept_deptName" type="text"/>
			</td>
		</tr>
		</@ZKUI.SearchBelow>
	</@ZKUI.Searchbar>
	<@ZKUI.Layout style="position:absolute; top:70px;bottom:0px;left:0px;right:0px;height:auto;" pattern="2U" onInit="attGlobalInitDeptTree">
		<@ZKUI.Cell width="240">
			<@ZKUI.Tree id="attPersonSchDeptTree" dynamic="true" type="checkbox" url="authDepartment.do?dynaTree&showPersonCount=false" onClick="attGlobalDeptTreeClick"
				onCheck="attPersonSchDeptTreeCheck"></@ZKUI.Tree>
		</@ZKUI.Cell>
		<@ZKUI.Cell hideHeader="true">
			<@ZKUI.Toolbar>
				<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission=""/>
				<@ZKUI.ToolItem id="attPersonSch.do?cycleSch" text="att_personSch_cycleSch" title="att_personSch_cycleSch" width="440" height="540" img="comm_add.png" action="attPersonSchCycleSch" permission="att:personsch:cycleSch"/>
				<@ZKUI.ToolItem id="attPersonSch.do?tempSch" text="att_leftMenu_tempSch" title="att_leftMenu_tempSch" width="1080" height="700" img="comm_add.png" action="attPersonSchTempSch" permission="att:personsch:tempSch"/>
				<@ZKUI.ToolItem id="attCycleSch.do?delCycleSch" text="att_personSch_cleanCycleSch" img="comm_del.png" action="attPersonSchCleanCycleSch" permission="att:personsch:cleanCycleSch"/>
				<@ZKUI.ToolItem id="attTempSch.do?delTempSch" text="att_personSch_cleanTempSch" img="comm_del.png" action="attPersonSchCleanTempSch" permission="att:personsch:cleanTempSch"/>
				<@ZKUI.ToolItem type="more">
					<@ZKUI.ToolItem id="attPersonSch.do?export" type="export" text="att_personSch_export" title="att_personSch_export" permission="att:personsch:export"/>
					<@ZKUI.ToolItem id="attTempSch.do?import" type="import" text="att_personSch_import" title="att_personSch_import" permission="att:personsch:import" showImportProcess="true" onFinish="attPersonSchReloadGrid" width="500" height="250"/>
					<@ZKUI.ToolItem id="attCycleSch.do?import" type="import" text="att_personSch_importCycSch" title="att_personSch_importCycSch" permission="att:personsch:import" showImportProcess="true" onFinish="attPersonSchReloadGrid" width="500" height="250"/>
					<@ZKUI.ToolItem id="attTempSch.do?exportTemplate" type="export" action="attOpExportTempSchTemplate" text="att_personSch_exportTemplate" title="att_exception_downTemplate" img="common_download.png" permission="att:personsch:exportTemplate" />
					<@ZKUI.ToolItem id="attCycleSch.do?exportTemplate" type="export" action="attOpExportCycleSchTemplate" text="att_personSch_exportCycSchTemplate" title="att_exception_downTemplate" img="common_download.png" permission="att:personsch:exportTemplate" />
				</@ZKUI.ToolItem>
			</@ZKUI.Toolbar>
			<@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttPersonSchItem" query="attPersonSch.do?list"/>
		</@ZKUI.Cell>
	</@ZKUI.Layout>
</@ZKUI.GridBox>
<script>

	/*标记人员排班*/
	attPersonSchPageType = "pers";

	function loadAttPersonSchGrid() {

		var queryParam = {
			deptId: attGlobalDeptTreeClickId,
			isIncludeLower: attGlobalIsIncludeLower
		};

		var opts = {
			gridName: "${gridName}",
			startDate: $('#attPersonSchDateSearch input[name="startDate"]').val(),
			endDate: $('#attPersonSchDateSearch input[name="endDate"]').val(),
			vo: "com.zkteco.zkbiosecurity.att.vo.AttPersonSchItem",
			query: "attPersonSch.do?list",
			queryParam: JSON.stringify(queryParam),
			showColumns: "!likeName" // 处理前端最后一列显示换行
		};
		$("#" + "${gridName}").loadGrid(opts);
		return false;
	}
</script>