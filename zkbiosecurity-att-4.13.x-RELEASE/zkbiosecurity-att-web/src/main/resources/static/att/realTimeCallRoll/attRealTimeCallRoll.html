<#assign gridName="attRealTimeCallRoll${uuid!}">
<#assign treeId="tree${uuid!}">
<script type="text/javascript">
	/** 定义全局变量*/
	var attRealTimeCallRollDeptIds, isIncludeLower,beginTime,endTime;
	/** 初始化*/
	function initAttPersDeptTree(layout) {

		isIncludeLower = false;

		 var html = "<span><div id='attIsIncludeLower' class='tree_header_box'>" +
            "<span onclick='imgClick(1)' class='plus_minus_img icv-head-plus'></span>" +
            "<span onclick='imgClick(0)' class='plus_minus_img icv-head-minus'></span>" +
            "<span id='attRealTimeCallRollCheckboxSpan'></span><span><@i18n 'common_tree_containsSubordinate'/></span></div></span>";
		layout.cells("a").setText(html);

        loadUIToDiv("input", "#attRealTimeCallRollCheckboxSpan", {
            id: "attRealTimeCallRollCheckbox",
            useInputReq: true,
            type: "checkbox",
            value: isIncludeLower,
            eventCheck: true,
            onclick: "attRealTimeCallRollCheckChange()"
        })

        layout.attachEvent("onExpand", function(name){
            resizeUI();
            $("#attIsIncludeLower").show();
            loadUIToDiv("input", "#attRealTimeCallRollCheckboxSpan", {
                id: "attRealTimeCallRollCheckbox",
                useInputReq: true,
                type: "checkbox",
                value: isIncludeLower,
                eventCheck: true,
                onclick: "attRealTimeCallRollCheckChange()"
            })
        });

        layout.attachEvent("onCollapse", function(name){
            resizeUI();
            $("#attIsIncludeLower").hide();
        });
	}

	function attRealTimeCallRollCheckChange() {
	    var value = !$("#attRealTimeCallRollCheckbox").prop("checked");
        var tree = ZKUI.Tree.get("tree${uuid!}").tree;
		tree.enableThreeStateCheckboxes(value);
		isIncludeLower = value;
	}

	/** 部门树的展开与合并*/
	function imgClick(flag) {
		var treeId = "tree${uuid!}";
		var tree = ZKUI.Tree.get(treeId).tree;
		if (flag == 1) {
			tree.openAllItems(0);
		} else {
			tree.closeAllItems(0);
		}
	}

	var rollCallEndTimer = null;
	// 确认事件搜索
	function getAttPersSignRecord() {
		attRealTimeCallRollDeptIds = ZKUI.Tree.get("tree${uuid!}").getValue();
		if (typeof attRealTimeCallRollDeptIds === 'undefined' || attRealTimeCallRollDeptIds == null || attRealTimeCallRollDeptIds === '') {
			messageBox({
				messageType : "alert",
				title : "<@i18n 'common_prompt_title'/>",
				text : "<@i18n 'att_realTime_selectDept'/>"
			});
			return false;
		}
		beginTime = $("#beginTime").val();
		endTime = $("#endTime").val();
		$.ajax({
            url : "attRealTimeCallRoll.do?getAttRealTimeCallRollList",
            type : "POST",
            data : {
            	beginDate : beginTime,
            	endDate : endTime,
            	deptId : attRealTimeCallRollDeptIds
            },
            success : function(res) {
                if (res.ret == "ok") {
                    var dhxGrid4Sign = ZKUI.Grid.get("attPersSignRecord${uuid!}").grid;
                    var dhxGrid4NoSign = ZKUI.Grid.get("attPersNoSignRecord${uuid!}").grid;
                    // 确定清空gird
                    dhxGrid4Sign.clearAll();
                    dhxGrid4NoSign.clearAll();
                    // 遍历渲染数据
                    var jsonMap = res.data;
                    var noSignJson = jsonMap.noSign;
                    if(noSignJson.length > 0){
	                    for(var i in noSignJson){
	                        dhxGrid4NoSign.addRow(noSignJson[i].id, noSignJson[i].data, 0);
	                   	}
                    }
                    var signJson = jsonMap.sign;
                    if(signJson.length > 0){
	                    for(var i in signJson){
	                    	dhxGrid4Sign.addRow(signJson[i].id, signJson[i].data, 0);
	                    }
                    }
                }
            }
        });

		// 到了点名结束时间,提示实时点名结束
        if (rollCallEndTimer) {
            // 清除上一次未执行结束的timeout
            clearTimeout(rollCallEndTimer);
        }
		var timeLong = new Date(endTime.replace(/-/g,'/')).getTime() - new Date().getTime();
        rollCallEndTimer = window.setTimeout(function() {
            messageBox({
                messageType : "alert",
                title : "<@i18n 'common_prompt_title'/>",
                text : "<@i18n 'att_realTime_rollCallEnd'/>"
            });
        }, timeLong);
    }

	(function() {
		/** 订阅设备签到 */
		var clientId = Math.round(Math.random() * 10000) + "${uuid!}";

		function initEventSocket() {
			var client = Web.getSocket({
				id : "eventClient",
				url : "attRTMonitor/getSignEventData",
				param : JSON.stringify({
					"clientId" : clientId
				}),
				onMessage : function(resp) {
				    // 当前时间大于结束时间则不需要在更新了
				    if(new Date().getTime() > new Date(endTime.replace(/-/g,'/')).getTime()) {
				        return;
                    }

					var item = JSON.parse(resp.body);
					var dhxGrid4Sign = ZKUI.Grid.get("attPersSignRecord${uuid!}").grid;
					var dhxGrid4NoSign = ZKUI.Grid.get("attPersNoSignRecord${uuid!}").grid;
					if(typeof attRealTimeCallRollDeptIds === 'undefined' || attRealTimeCallRollDeptIds == null || attRealTimeCallRollDeptIds === ''){
						   return false;
					}else {
						if(attRealTimeCallRollDeptIds.indexOf(item.deptId) != -1){
							var beainStrTime = new Date(beginTime.replace(/-/g,'/'));
							var endStrTime = new Date(endTime.replace(/-/g,'/'));
							if (item.pushType == "transaction") {
								if (beainStrTime <= new Date(item.attDatetime) && new Date(item.attDatetime) <= endStrTime) {
								    if (!(dhxGrid4Sign.getRowById(item.pin))) {
								    	dhxGrid4Sign.addRow(item.pin, item.data, 0);
								    }
									dhxGrid4NoSign.deleteRow(item.pin);
									return false;
								}
							} else if (item.pushType == "leave") {
								if ((beainStrTime <= new Date(item.startDatetime) && new Date(item.startDatetime) <= endStrTime) 
										|| (beainStrTime <= new Date(item.endDatetime) && new Date(item.endDatetime) <= endStrTime)) {
										if (!(dhxGrid4Sign.getRowById(item.pin))) {
										    dhxGrid4Sign.addRow(item.pin, item.data, 0);
										}
									dhxGrid4NoSign.deleteRow(item.pin);
								    return false;
								    }
							}
						}
					}
				}
			});
			return client;
		}
		var client = initEventSocket();
	})();
</script>
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar hideQueryButton="true">
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input type="datetime" id="beginTime" endId="endTime" name="beginDate" title="common_time_from" max="today" today="true" noOverToday="true" readonly="false"/>
                    &nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
                    <@ZKUI.Input type="datetime" id="endTime" name="endDate" title="common_to"  todayRange="end" today="true" hideLabel="true" readonly="false"/>
                    <button class="button-form" onclick="getAttPersSignRecord()"><@i18n 'base_remote_confirm'/></button>
                </td>
            </tr>
        </@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    <@ZKUI.Layout style="position:absolute; top:70px;bottom:0px;left:0px;right:0px;height:auto;" onInit="initAttPersDeptTree" pattern="3L">
        <@ZKUI.Cell width="240" treeId="tree${uuid!}">
            <@ZKUI.Tree id="tree${uuid!}" type="checkbox" url="authDepartment.do?tree&showPersonCount=false"></@ZKUI.Tree>
        </@ZKUI.Cell>
        <!-- 未签到人员 -->
        <@ZKUI.Cell hideArrow="true" title="att_realTime_noSignPers">
             <@ZKUI.Grid sortInPage="true" gridName="attPersNoSignRecord${uuid!}"showColumns="!attDatetime" vo="com.zkteco.zkbiosecurity.att.vo.AttRealTimeCallRollItem" query="attRealTimeCallRoll.do?attPersSignRecord" nopaging="true"/>
        </@ZKUI.Cell>
        <!-- 签到监控 -->
        <@ZKUI.Cell height="300" title="att_realTime_signMonitor">
              <@ZKUI.Grid sortInPage="true" gridName="attPersSignRecord${uuid!}" vo="com.zkteco.zkbiosecurity.att.vo.AttRealTimeCallRollItem"  nopaging="true"/>
        </@ZKUI.Cell>
    </@ZKUI.Layout>
</@ZKUI.GridBox>