<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no">
    <meta charset="utf-8">
    <style>        /* 搜索框样式 */
        .search-box {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 999;
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 6px rgba(0,0,0,.3);
            font-family: "微软雅黑";
        }

        /* 搜索框输入框样式 */
        .search-box input[type="text"] {
            width: 200px;
            height: 30px;
            padding: 4px;
            font-size: 14px;
            border: 1px solid #ccc;
            border-radius: 4px;
            outline: none;
        }

        /* 地图容器样式 */
        #baiduMapContainer {
            overflow: hidden;
            width: 100%;
            height: 380px;
            margin: 0;
            font-family: "微软雅黑";
        }
    </style>
</head>
<body style="overflow: hidden;margin:0px;height: 100%;">

<!-- 新增：地址搜索框与建议下拉 -->
<div class="search-box">
    <input type="text" id="addressInput" placeholder="">
</div>

<!-- 地图容器 -->
<div id="baiduMapContainer" style="overflow: hidden; height: 418px;"></div>

<script type="text/javascript">
window.onload = function() {
    var placeholder = parent.document.getElementById("searchKey").innerText;
    document.getElementById("addressInput").setAttribute("placeholder", placeholder);
}

// 获取父页面传入的经纬度值
var tempCenterX = parent.document.getElementById('longitude').value;
var tempCenterY = parent.document.getElementById('latitude').value;

// 加载百度地图API脚本
loadJScript();
function loadJScript() {

    var key = parent.document.getElementById("attSignAddressMapKeyId").value;
    var script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = 'https://api.map.baidu.com/api?v=2.0&ak='+key+'&s=1&callback=init';
    document.body.appendChild(script);
}

// 百度地图实例
var baiduMap = null;
// 存储所有 marker 的数组
var markersArray = [];

// 初始化地图
function init() {
    var autoLocation = false;
    if (tempCenterX == "" || tempCenterY == "") {
        tempCenterX = "116.331398"; // 默认北京经度
        tempCenterY = "39.897445"; // 默认北京纬度
        autoLocation = true;
    }

    // 初始化地图
    baiduMap = new BMap.Map("baiduMapContainer", {enableMapClick: false});
    var point = new BMap.Point(tempCenterX, tempCenterY);
    baiduMap.centerAndZoom(point, 20); // 设置中心点和缩放级别
    baiduMap.enableScrollWheelZoom(true); // 启用滚轮缩放
    baiduMap.disableDoubleClickZoom();   // 禁用双击放大

    // 添加定位控件
    var locationControl = new BMap.GeolocationControl();
    locationControl.addEventListener("locationSuccess", function(e) {
        placeMarker(e.point);
        getAddressByLngLat(e.point.lng, e.point.lat);
    });
    baiduMap.addControl(locationControl);

    // 点击地图设置 marker
    baiduMap.addEventListener("click", function(e) {
        placeMarker(e.point);
        getAddressByLngLat(e.point.lng, e.point.lat);
    });

    // 创建自动完成对象
    var ac = new BMap.Autocomplete({
        "input": "addressInput",
        "location": baiduMap
    });

    // 监听选中事件
    ac.addEventListener("onconfirm", function(e) {
        var _value = e.item.value;
        var address = _value.province +  _value.city +  _value.district +  _value.street +  _value.business;
        var geocoder = new BMap.Geocoder();
        // 获取地址坐标
        geocoder.getPoint(address, function(point) {
            if (point) {
                placeMarker(point);
                getAddressByLngLat(point.lng, point.lat);
            } else {
                alert("未找到该地址的坐标");
            }
        }, _value.city); // 指定城市范围提升准确性
    });

    // 页面加载时自动定位或使用已有坐标
    if (autoLocation) {
        var geolocation = new BMap.Geolocation();
        geolocation.getCurrentPosition(function(r) {
            if (this.getStatus() === BMAP_STATUS_SUCCESS) {
                placeMarker(r.point);
                getAddressByLngLat(r.point.lng, r.point.lat);
            } else {
                alert("无法获取当前位置，请检查浏览器权限");
            }
        });
    } else {
        var centerPoint = new BMap.Point(tempCenterX, tempCenterY);
        placeMarker(centerPoint);
    }
}

// 更新地图 marker，并回填经纬度到父页面
function placeMarker(point) {
    baiduMap.clearOverlays();
    var mk = new BMap.Marker(point);
    baiduMap.addOverlay(mk);
    markersArray.push(mk);
    baiduMap.panTo(point);
}

// 根据经纬度获取地址并回调父页面方法
function getAddressByLngLat(lng, lat) {
    var geoc = new BMap.Geocoder();
    var point = new BMap.Point(lng, lat);
    geoc.getLocation(point, function(rs) {
        if (rs && rs.address) {
            parent.setGoogleAddress(lng, lat, rs.address);
        }
    });
}

</script>

</body>
</html>