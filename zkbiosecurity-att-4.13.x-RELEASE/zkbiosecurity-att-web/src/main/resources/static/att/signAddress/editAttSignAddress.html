<#include '/public/template/editTemplate.html'>
<#assign formId="attSignAddressEditForm${uuid!}">
<#macro editContent>
<form action='attSignAddress.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
	<input type='hidden' id="id" name='id' value='${(item.id)!}' />
	<table class='tableStyle'>
		<tr>
			<!-- 区域名称 -->
			<th>
				<label><@i18n 'base_area_name'/></label>
				<span class="required">*</span>
			</th>
			<td><@ZKUI.ComboTree id="attPointAreaSelectId" width="148" autoFirst="true" type="check" url="authArea.do?tree" value="${(item.areaIds)}" hideLabel="true" name="areaIds"/></td>
			<th>
				<label><@i18n 'att_signAddress_range'/></label>
				<span class='required'>*</span>
			</th>
			<td>
				<input name='validRange' maxlength="4" type='text' value='${(item.validRange)!}' />
				<label style="margin: 0px 4px;"><@i18n 'att_signAddress_rangeUnit'/></label>
			</td>
		</tr>
		<tr>
			<th>
				<label><@i18n 'map_type'/></label>
				<span class='required'>*</span>
			</th>
			<td>
				<@ZKUI.Combo id="attSignAddressMapTypeId" width="148" empty="false" value="${(item.mapType)!}"  hideLabel="true" name="mapType">
					<option value="baiduMap"><@i18n "map_type_baidu"/></option>
					<option value="googleMap"><@i18n "map_type_google"/></option>
				</@ZKUI.Combo>
			</td>
			<th><label><@i18n 'map_key'/></label><span class='required'>*</span></th>
			<td>
				<input id="attSignAddressMapKeyId" name='mapKey' type='text' value='${(item.mapKey)!}' />
				<button class="button-form" type="button" id="attSignAddressMapInitBtnId" title="<@i18n 'att_signAddress_init'/>">
					<@i18n 'att_signAddress_init'/>
                </button>
			</td>
		</tr>
		<tr>
			<th>
				<label><@i18n 'att_signAddress_longitude'/></label>
				<span class='required'>*</span>
			</th>
			<td>
				<input id="longitude" name='longitude' readonly="true" maxlength="30" type='text' value='${(item.longitude)!}' />
			</td>
			<th>
				<label><@i18n 'att_signAddress_latitude'/></label>
				<span class='required'>*</span>
			</th>
			<td>
				<input id="latitude" name='latitude' readonly="true" maxlength="30" type='text' value='${(item.latitude)!}' />
			</td>
		</tr>
		<tr>
			<th rowspan="3">
				<label><@i18n 'att_signAddress_address'/></label>
				<span class='required'>*</span>
			</th>
			<td rowspan="3">
				<textarea id="address" name='address' readonly="true" maxlength="100" style="width: 148px" rows="3" type='text' value='${(item.address)!}' >${(item.address)!}</textarea>
			</td>
		</tr>
	</table>
</form>

<!-- 地图参数占位 -->
<div id="map" style="width:100%;height: 420px;" class="zk-bg-color">
	<div style="width: 100%;height: 100%;display: flex;justify-content: center;align-items: center;font-size: 14px;"><@i18n "att_signAddress_initTips"/></div>
</div>

<div id="searchKey" style="display:none;"><@i18n 'att_h5_enterKeyWords'/></div>

<script type='text/javascript'>


$(function(){

	$('#attSignAddressMapInitBtnId').on('click', function() {
        // 在这里编写点击按钮后需要执行的操作
         var key = $("#attSignAddressMapKeyId").val();
         if (key) {
			$('#map').empty(); // 清空地图容器
			initMap(); // 调用初始化地图的函数
         } else {
         	openMessage(msgType.warning, "<@i18n 'map_pickCoordinates_prompt'/>");
         }
    });


	 ZKUI.Combo.get("attSignAddressMapTypeId").combo.attachEvent("onChange", function(){
	 	console.log(123);
		$('#map').empty(); // 清空 #map 容器中的所有内容
	 	$('#longitude').val("");
	 	$('#latitude').val("");
	 	$('#address').val("");
	 	$('#attSignAddressMapKeyId').val("");
		var tips = '<div style="width: 100%;height: 100%;display: flex;justify-content: center;align-items: center;font-size: 14px;"><@i18n "att_signAddress_initTips"/></div>'
	 	$('#map').append(tips);
	 });

    // 编辑自动初始化加载地图
    if ("${(item.id)!}") {
      initMap();
    }
    function initMap() {
        var mapName = ZKUI.Combo.get("attSignAddressMapTypeId").combo.getSelectedValue();
        if ("gaoDeMap" == mapName) {
            $('<iframe id="gaoDeMap" src="/att/signAddress/gaoDeMap.html" width="100%" height="418px"></iframe>').prependTo('#map');
        } else if ("googleMap" == mapName) {
            $('<iframe id="googleMap" src="/att/signAddress/googleMap.html" width="100%" height="418px"></iframe>').prependTo('#map');
        } else if ("baiduMap" == mapName) {
        	 $('<iframe id="baiduMap" src="/att/signAddress/baiduMap.html" width="100%" height="418px"></iframe>').prependTo('#map');
        }
    };

    jQuery.validator.addMethod("spaceValid", function(value, element) {
        var pattenChar1 = /(^\S)/;
        var pattenChar2 = /(\S$)/;
        return this.optional(element) || (pattenChar1.test(value) && pattenChar2.test(value));
    }, function() {
        return "<@i18n 'att_common_nameNoSpace'/>";
    });

    $('#${formId}').validate({
        debug : true,
        rules : {
        	'areaIds' : {
                required : true
            },
            'address' : {
                required : true
            },
            'longitude' : {
                required : true,
            },
            'latitude' : {
                required : true,
            },
            'validRange' : {
                required : true,
                digits:true
            },
            'mapKey' : {
                required : true,
            },
        },
        messages : {},
        submitHandler : function() {
            <@submitHandler/>
        }
    });
});

function setGaodeAddress(position, address) {
	var longitudeFromChild = position;
	var longitude = longitudeFromChild.toString().split(',')
	$("#longitude").val(longitude[0]);
	$("#latitude").val(longitude[1]);
	var address = address;
	$("#address").val(address);
}

function setGoogleAddress(lng, lat, address) {
	$("#longitude").val(lng);
	$("#latitude").val(lat);
	$("#address").val(address);
}

function setBaiduAddress(lng, lat, address) {
	$("#longitude").val(lng);
	$("#latitude").val(lat);
	$("#address").val(address);
}

// 用于接收子地图页面报错信息,作出显示 add bob.liu 20191202
function mapFailToLoad() {
	var ptimeout = sysCfg.ptimeout;
	openMessage(msgType.warning, "<@i18n 'att_sign_mapWarn'/>", ptimeout);
}

</script>
</#macro>
