<!doctype html>
<html lang="zh-CN">
<head>
<base href="https://webapi.amap.com/ui/1.0/ui/misc/PositionPicker/examples/" />
<meta charset="utf-8">
<meta name="viewport" content="initial-scale=1.0, user-scalable=no, width=device-width">
<title></title>
<script>
    var key = parent.document.getElementById("gaoDeMapJsKey").innerText;
    document.write('<script src="https://webapi.amap.com/maps?v=1.4.15&plugin=AMap.ToolBar&key=' + key + '"><\/script>"');
</script>
<link rel="stylesheet" href="https://cache.amap.com/lbs/static/main1119.css" />
</head>
<body>
	<div id="container"></div>
	<div id="myPageTop">
		<table>
			<tr>
				<td>
					<input id="tipinput" type="text" />
				</td>
			</tr>
		</table>
	</div>
	<script src="https://webapi.amap.com/ui/1.0/main.js?v=1.0.11"></script>
	<script type="text/javascript" src="https://cache.amap.com/lbs/static/addToolbar.js"></script>
	<script type="text/javascript">
        window.onload = function() {
            var key = parent.document.getElementById("searchKey").innerText;
            document.getElementById("tipinput").setAttribute("placeholder", key);
        }
        try{
	        AMap.plugin([ 'AMap.Geolocation', 'AMap.Autocomplete', 'AMap.PlaceSearch' ], function() {
	            var id = parent.document.getElementById("id").value;
	            if(id == null || id == ""){
	                var map = new AMap.Map('container', {
			            zoom : 16,
			            scrollWheel : true
			        })
	                var geolocation = new AMap.Geolocation({
	                    enableHighAccuracy : true,
	                    timeout : 10000,
	                    buttonPosition : 'RB',
	                    buttonOffset : new AMap.Pixel(10, 20),
	                    zoomToAccuracy : true,
	                });
	                map.addControl(geolocation);

					var autoOptions = {
					   input : "tipinput"
					};
					var auto = new AMap.Autocomplete(autoOptions);
					var placeSearch = new AMap.PlaceSearch({
						map : map
					});
					AMap.event.addListener(auto, "select", select);
					function select(e) {
						placeSearch.setCity(e.poi.adcode);
						placeSearch.search(e.poi.name);
					}
					AMapUI.loadUI([ 'misc/PositionPicker' ], function(PositionPicker) {
						var positionPicker = new PositionPicker({
							mode : 'dragMap',
							map : map
						});
						positionPicker.on('success', function(positionResult) {
							parent.setGaodeAddress(positionResult.position, positionResult.address);
						});
						positionPicker.on('fail', function(positionResult) {
							parent.setGaodeAddress(positionResult.position, positionResult.address);
						});
						positionPicker.start();
				   });
	            } else if (id != null && id != "") {
	                var longitude = parent.document.getElementById("longitude").value;
	                var latitude = parent.document.getElementById("latitude").value;
	                var map = new AMap.Map('container', {
	                    zoom : 16,
	                    scrollWheel : true,
	                    center: [longitude,latitude]
	                })
	                var autoOptions = {
	                    input : "tipinput"
	                };
	                var auto = new AMap.Autocomplete(autoOptions);
	                var placeSearch = new AMap.PlaceSearch({
	                    map : map
	                });
	                AMap.event.addListener(auto, "select", select);
	                function select(e) {
	                    placeSearch.setCity(e.poi.adcode);
	                    placeSearch.search(e.poi.name);
	                }
	                AMapUI.loadUI([ 'misc/PositionPicker' ], function(PositionPicker) {
	                    var positionPicker = new PositionPicker({
	                        mode : 'dragMap',
	                        map : map
	                    });
	                    positionPicker.on('success', function(positionResult) {
	                        parent.setGaodeAddress(positionResult.position, positionResult.address);
	                    });
	                    positionPicker.on('fail', function(positionResult) {
	                        parent.setGaodeAddress(positionResult.position, positionResult.address);
	                    });
	                    positionPicker.start();
	                });
	            }
	        });
        }catch(error){
        	if("ReferenceError: AMap is not defined" == error){
                parent.mapFailToLoad();
            }
        }
    </script>
</body>
</html>