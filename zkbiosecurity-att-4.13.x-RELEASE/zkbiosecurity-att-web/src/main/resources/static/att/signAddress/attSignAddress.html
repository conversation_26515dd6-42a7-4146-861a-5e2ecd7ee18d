<#assign gridName="attSignAddressGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">

    <@ZKUI.Toolbar gridName="${gridName}">
        <@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:signAddress:refresh"></@ZKUI.ToolItem>
        <@ZKUI.ToolItem id="attSignAddress.do?edit" text="common_op_new" width="900" height="660" img="comm_add.png" action="commonAdd" permission="att:signAddress:add"></@ZKUI.ToolItem>
        <@ZKUI.ToolItem id="attSignAddress.do?del&ids=(id)" text="common_op_del" img="comm_del.png" action="commonDel" permission="att:signAddress:del"></@ZKUI.ToolItem>
    </@ZKUI.Toolbar>

    <@ZKUI.Grid gridName="${gridName}" vo="com.zkteco.zkbiosecurity.att.vo.AttSignAddressItem" query="attSignAddress.do?list"/>

</@ZKUI.GridBox>