<!DOCTYPE html>
<html>
<head>
	<meta name="viewport" content="initial-scale=1.0, user-scalable=no">
	<meta charset="utf-8">
	<style>
html, body {
	height: 100%;
	padding: 0;
}

.controls {
	margin: 10px;
	border: 1px solid transparent;
	border-radius: 2px 0 0 2px;
	box-sizing: border-box;
	-moz-box-sizing: border-box;
	height: 32px;
	outline: none;
	box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
	position: fixed;
    z-index: 99;
}
</style>
</head>
<body style="overflow: hidden;margin:0px;height: 100%;">
<input id="pac-input" class="controls" type="text">
<div id="map" style="width: 100%; height: 100%"></div>
<script>

	window.onload = function() {
		var placeholder = parent.document.getElementById("searchKey").innerText;
		document.getElementById("addressInput").setAttribute("placeholder", placeholder);
	}

	var key = parent.document.getElementById("attSignAddressMapKeyId").value;
	document.write('<script src="https://maps.googleapis.com/maps/api/js?libraries=places&callback=initAutocomplete&key='+key+'"async defer><\/script>');
</script>
<script type="text/javascript">
        window.onload = function() {
            var key = parent.document.getElementById("searchKey").innerText;
            document.getElementById("pac-input").setAttribute("placeholder", key);
        }
        var lngtxt;
        var lattxt;
        var id = parent.document.getElementById("id").value;
        if(id == null || id == ""){
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function(position) {
                    lngtxt = position.coords.longitude;
                    lattxt = position.coords.latitude;
                })
            }
        }else if (id != null && id != ""){
           lngtxt = parent.document.getElementById("longitude").value;
           lattxt = parent.document.getElementById("latitude").value;
        }
        var addresstxt;
        var map;
        var marker;
        var infowindow;
        var geocoder;
        var markersArray = [];
        function initAutocomplete() {
            var latlng = new google.maps.LatLng(lattxt, lngtxt);
            var myOptions = {
            	mapTypeControl: false,
                zoom : 18,
                center : latlng,
                mapTypeId : google.maps.MapTypeId.ROADMAP
            };
            map = new google.maps.Map(document.getElementById('map'), myOptions);

            geocoder = new google.maps.Geocoder(); //实例化地址解析
            //监听点击地图事件
            google.maps.event.addListener(map, 'click', function(event) {
                placeMarker(event.latLng);
            });
            // Create the search box and link it to the UI element.
            var input = document.getElementById('pac-input');
            var searchBox = new google.maps.places.SearchBox(input);
            map.controls[google.maps.ControlPosition.TOP_LEFT].push(input);

            // Bias the SearchBox results towards current map's viewport.
            map.addListener('bounds_changed', function() {
                searchBox.setBounds(map.getBounds());
            });

            var markers = [];
            // Listen for the event fired when the user selects a prediction and retrieve
            // more details for that place.
            searchBox.addListener('places_changed', function() {
                var places = searchBox.getPlaces();

                if (places.length == 0) {
                    return;
                }

                // Clear out the old markers.
                markers.forEach(function(marker) {
                    marker.setMap(null);
                });
                markers = [];

                // For each place, get the icon, name and location.
                var bounds = new google.maps.LatLngBounds();
                places.forEach(function(place) {
                    if (!place.geometry) {
                        return;
                    }

                    placeMarker(place.geometry.location)

                    mapClick(place.geometry.location.lng(), place.geometry.location.lat(), place.name);

                    if (place.geometry.viewport) {
                        // Only geocodes have viewport.
                        bounds.union(place.geometry.viewport);
                    } else {
                        bounds.extend(place.geometry.location);
                    }
                });
                map.fitBounds(bounds);
            });
        }

        function placeMarker(location) {
            clearOverlays(infowindow); //清除地图中的标记
            marker = new google.maps.Marker({
                position : location,
                map : map
            });
            markersArray.push(marker);
            //根据经纬度获取地址
            if (geocoder) {
                geocoder.geocode({
                    'location' : location
                }, function(results, status) {
                    if (status == google.maps.GeocoderStatus.OK) {
                        if (results[0]) {
                            attachSecretMessage(marker, results[0].geometry.location, results[0].formatted_address);
                        }
                    } else {
                        alert("Geocoder failed due to: " + status);
                    }
                });
            }
        }
        //在地图上显示经纬度地址
        function attachSecretMessage(marker, piont, address) {
             mapClick(piont.lng(), piont.lat(), address);
        }
        //删除所有标记阵列中消除对它们的引用
        function clearOverlays(infowindow) {
            if (markersArray && markersArray.length > 0) {
                for (var i = 0; i < markersArray.length; i++) {
                    markersArray[i].setMap(null);
                }
                markersArray.length = 0;
            }
            if (infowindow) {
                infowindow.close();
            }
        }

        function setiInit() {
            // 页面加载显示默认lng lat address---begin
            if (lattxt != '' && lngtxt != '' && addresstxt != '') {
                var latlng = new google.maps.LatLng(lattxt, lngtxt);
                placeMarker(latlng);
            }
        }

        function mapClick(lng, lat, address) {
            parent.setGoogleAddress(lng, lat, address);
        }
        window.onload = function() {
        	try{
	            setiInit();
        	}catch(error){
        		if("ReferenceError: google is not defined" == error){
                    parent.mapFailToLoad();
                }
        	}
        }
    </script>
</body>
</html>
