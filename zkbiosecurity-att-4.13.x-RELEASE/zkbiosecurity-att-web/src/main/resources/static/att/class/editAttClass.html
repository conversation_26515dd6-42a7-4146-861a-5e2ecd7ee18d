<#include '/public/template/editTemplate.html'>
<#macro editContent>
<#if !user.staff>
		<form action='attClass.do?save' method='post' id='${formId}' enctype='multipart/form-data' onkeydown="if(event.keyCode==13){return false;}">
		<input id="flowStatus" name="flowStatus" readonly="readonly" type="hidden" value="${(item.flowStatus)!}" />
<#else>
		<form action='flowable.do?start' method='post' id='${formId}' enctype='multipart/form-data' onkeydown="if(event.keyCode==13){return false;}">
		<input type='hidden' name='flowType' value='classShift'/>
</#if>
		<input type='hidden' name='id' value='${(item.id)!}'/>
		<table class="tableStyle">
			<input id="idSwapPersonId" name="swapPersonId" readonly="readonly" type="hidden" value="${(item.swapPersonId)!}" />
			<input id="idAdjustPersonId" name="adjustPersonId" readonly="readonly" type="hidden" value="${(item.adjustPersonId)!}" />
			<input id="idAdjustDeptCode" name="adjustDeptCode" readonly="readonly" type="hidden" value="${(item.adjustDeptCode)!}" />
			<input id="idSwapDeptCode" name="swapDeptCode" readonly="readonly" type="hidden" value="${(item.swapDeptCode)!}" />
			<input id="idAdjustDeptId" name="adjustDeptId" readonly="readonly" type="hidden" value="${(item.adjustDeptId)!}" />
			<input id="idSwapDeptId" name="swapDeptId" readonly="readonly" type="hidden" value="${(item.swapDeptId)!}" />
			<tbody>
				<tr>
					<th><label><@i18n 'att_class_type'/></label></th>
					<td>
						<@ZKUI.Combo id="idAdjustType" width="148" empty="false" value="${(item.adjustType)!}" hideLabel="true" name="adjustType" onChange="adjustTypeChange">
		                    <option value="0"><@i18n 'att_class_sameTimeMoveShift'/></option>
		                    <option value="1"><@i18n 'att_class_differenceTimeMoveShift'/></option>
		                    <option value="2"><@i18n 'att_class_twoPeopleMove'/></option>
		                </@ZKUI.Combo>
					</td>
				</tr>
				<tr>
					<th><label><@i18n 'att_person_pin'/></label><span class="required">*</span></th>
					<td>
						<input id="idAdjustPersonPin" maxlength="30" name="adjustPersonPin" onblur="getPersonInfo(id, 'Adjust');" type="text" value="${(item.adjustPersonPin)!}" />
					</td>
					<th id="one" style="display: none;"><label><@i18n 'att_class_movePersonPin'/></label><span class="required">*</span></th>
					<td id="two" style="display: none;">
						<input id="idSwapPersonPin" maxlength="30" name="swapPersonPin" onblur="getPersonInfo(id, 'Swap');" type="text" value="${(item.swapPersonPin)!}" />
					</td>
				</tr>
				<tr>
					<th><label><@i18n 'att_person_name'/></label></th>
					<td>
						<input id="idAdjustPersonName" name="adjustPersonName" readonly="readonly" type="text" value="${(item.adjustPersonName)!}" />
					</td>
					<th id="three" style="display: none;"><label><@i18n 'att_class_movePersonName'/></label></th>
					<td id="four" style="display: none;">
						<input id="idSwapPersonName" name="swapPersonName" readonly="readonly" type="text" value="${(item.swapPersonName)!}" />
					</td>
				</tr>
				<tr>
					<th style="display: none;"><label><@i18n 'att_person_lastName'/></label></th>
					<td style="display: none;">
						<input id="idAdjustPersonLastName" name="adjustPersonLastName" readonly="readonly" type="text" value="${(item.adjustPersonLastName)!}" />
					</td>
					<th id="three2" style="display: none;"><label><@i18n 'att_class_movePersonLastName'/></label></th>
					<td id="four2" style="display: none;">
						<input id="idSwapPersonLastName" name="swapPersonLastName" readonly="readonly" type="text" value="${(item.swapPersonLastName)!}" />
					</td>
				</tr>
				<tr>
					<th><label><@i18n 'pers_dept_deptName'/></label></th>
					<td>
						<input id="idAdjustDeptName" name="adjustDeptName" readonly="readonly" type="text" value="${(item.adjustDeptName)!}" />
					</td>
					<th id="five" style="display: none;"><label><@i18n 'att_class_moveDeptName'/></label></th>
					<td id="six" style="display: none;">
						<input id="idSwapDeptName" name="swapDeptName" readonly="readonly" type="text" value="${(item.swapDeptName)!}" />
					</td>
				</tr>
				<tr>
					<th><label><@i18n 'att_adjust_adjustDate'/></label><span class="required">*</span></th>
					<td>
						<@ZKUI.Input id="attClassAdjustDateId" type="date" value="${(item.adjustDate)!}" min="-1" today="true" readonly="true" name="adjustDate" hideLabel="true" title="att_adjust_adjustDate"/>
					</td>
					<th id="seven" style="display: none;"><label><@i18n 'att_class_moveDate'/></label></th>
					<td id="eight" style="display: none;">
						<@ZKUI.Input type="date" id="attSwapDateId" value="${(item.swapDate)!}" name="swapDate" min="-1" today="true" readonly="true" hideLabel="true"/>
					</td>
					<th id="eleven"><label><@i18n 'att_class_shiftName'/></label></th>
					<td id="twelve">
						<@ZKUI.Combo width="148" id="swapShiftId${uuid!}" hideLabel="true" empty="true" name="swapShiftId" path="attShift.do?getShiftList">
						</@ZKUI.Combo>
					</td>
				</tr>
				<tr>
					<th><label><@i18n 'common_remark'/></label></th>
					<td colspan="4">
						<input id="idAdjustReason" maxlength="25" name="remark" style="height: 50px; width: 543px;" type="text" value="${(item.remark)!}" />
					</td>
				</tr>
			</tbody>
		</table>
	</form>

	<script type='text/javascript'>
		$().ready(function() {
			$('#${formId}').validate( {
				debug : true,
				rules :{
					'adjustPersonPin' :{
						required : true
					}
				},
				submitHandler : function()
				{
                    //判断相同时间内是否存在其他申请
					var adjustPersonId = $("#idAdjustPersonId").val();
					var adjustDate = $("#${formId} input[name='adjustDate']").val();
					var swapPersonId = $("#idSwapPersonId").val();
					var swapDate = $("#${formId} input[name='swapDate']").val();

					//根据判断是否存在对应的提示

                    var adJuestType = ZKUI.Combo.get("idAdjustType").combo.getSelected();

                    if(adJuestType == 0){
                        var swapshiftVal=ZKUI.Combo.get("swapShiftId${uuid!}").combo.getSelected();
                        if(swapshiftVal==""){
                            messageBox({messageType: "alert", text: "<@i18n 'att_class_shiftNameNoNull'/>"});
                            return false;
                        }
                    }
                    if(adJuestType == 1){
                        if(adjustDate == swapDate){
                            messageBox({messageType: "alert", text: "<@i18n 'att_class_dateNoSame'/>"});
                            return false;
                        }
                    }
                    if(adJuestType == 2){

                        if ($("#idAdjustPersonPin").val() == $("#idSwapPersonPin").val()) {
                            //调整人员和对调人员不能相同，请重新添加
                            messageBox({messageType: "alert", text: "<@i18n 'att_class_personNoSame'/>"});
                            return false;
                        }

                        if ($("#idSwapPersonPin").val() == "") {
                            //对调人员不能为空
                            messageBox({messageType: "alert", text: "<@i18n 'att_class_personPinNoNull'/>"});
                            return false;
                        }
					}
                    $.ajax({
                        type: "post",
                        url: "attLeave.do?existAdjustOrClassApply",
                        data:{
							adjustPersonId: adjustPersonId,
							adjustDate: adjustDate,
							swapPersonId: swapPersonId,
							swapDate: swapDate
                        },
                        dataType: "json",
                        async: false,
                        success: function (result) {
                            if(result.ret == "ok"){
                            	<@submitHandler/>
                            }
                            else {
                                var ptimeout = sysCfg.ptimeout;
                                openMessage(msgType.warning, result[sysCfg.msg], ptimeout);
                            }
                        }
                    });
				}
			});
		});
		

		function adjustTypeChange(value, text) {
			if (value == 0) {
				$("#one").hide();
		  	 	$("#two").hide();
		  	 	$("#three").hide();
		   	 	$("#four").hide();
		   	 	$("#five").hide();
		   	 	$("#six").hide();
		   	 	$("#seven").hide();
		     	$("#eight").hide();
		     	$("#eleven").show();
			 	$("#twelve").show();
		     	$("#nine").show();
		     	$("#ten").show();
		    	// $("#idAdjustReason").css("width", "579px");
		 	} else if (value == 1) {
				$("#one").hide();
		  	 	$("#two").hide();
		  	 	$("#three").hide();
		   	 	$("#four").hide();
		   	 	$("#five").hide();
		   	 	$("#six").hide();
		   	 	$("#seven").show();
		     	$("#eight").show();
		   	 	$("#nine").show();
		     	$("#ten").show();
		     	$("#eleven").hide();
			   	$("#twelve").hide();
				// $("#idAdjustReason").css("width", "579px");
				//调班只允许到下个月末
				nextMonthLastDay("attClassAdjustDateId");
				nextMonthLastDay("attSwapDateId");
		 	} else if (value == 2) {
				$("#one").show();
		 	 	$("#two").show();
		 	 	$("#three").show();
		  	 	$("#four").show();
		  	 	$("#five").show();
		  	 	$("#six").show();
		  	 	$("#seven").show();
		     	$("#eight").show();
		     	$("#nine").show();
		     	$("#ten").show();
		     	$("#eleven").hide();
			   	$("#twelve").hide();
				// $("#idAdjustReason").css("width", "585px");
				nextMonthLastDay("attClassAdjustDateId");
				nextMonthLastDay("attSwapDateId");
		 	}
		}
		//日期显示为次月最后一天
		function nextMonthLastDay(dateTimeId){
		    var dateTime = new Date();
			var time = new Date(Date.parse(dateTime));
		    var year = time.getFullYear();
		    //var year = 1900; //用于测试
		    var month = time.getMonth() + 2;
		    //var month = 0 + 2; //用于测试
		    if (month > 12) {
		        month = month - 12;
		        year = year + 1;
		    }
		    var day = nextMonthDay(year, month);
		    var dateTimeNew = new Date(year, month-1, day);//js中 getMonth() 返回值是从0到11
		    ZKUI.Input.get(dateTimeId).setMaxDate(dateTimeNew);
		}
		//判断每月多少天
		function nextMonthDay(year, month) {
		    var day31 = [1, 3, 5, 7, 8, 10, 12];
		    var day30 = [4, 6, 9, 11];
		    if (day31.indexOf(month) > -1) {
		        return 31;
		    } else if (day30.indexOf(month) > -1) {
		        return 30;
		    } else {
		        if (isLeapYear(year)) {
		            return 29;
		        } else {
		            return 28;
		        }
		    }
		}
		//判断是否为闰年
		function isLeapYear(year) {
		    return (year % 4 == 0) && (year % 100 != 0 || year % 400 == 0);
		}
		
		/*
		 ******************
		 * 事件定义
		 ******************
		 */
		// 获取人员信息
		function getPersonInfo(personPin, adjustOrSwap) {
			var personPin = $("#" + personPin).val();
		   	$.ajax({
		        dataType: "json",
		        type: "post",
		        data: {
		        	pin: personPin
		        },
		    	url: "attPerson.do?getPersonInfo",
		        success: function(result) {
			        if (result) {
			        	$("#id" + adjustOrSwap + "PersonId").val(result.data.personId);
			        	$("#id" + adjustOrSwap + "PersonPin").val(result.data.personPin);
					    $("#id" + adjustOrSwap + "PersonName").val(result.data.personName);
					    $("#id" + adjustOrSwap + "PersonLastName").val(result.data.personLastName);
					    $("#id" + adjustOrSwap + "DeptId").val(result.data.deptId);
					    $("#id" + adjustOrSwap + "DeptCode").val(result.data.deptCode);
					    $("#id" + adjustOrSwap + "DeptName").val(result.data.deptName);
			        }
		        }
		    });	 
		}
	</script>
</#macro>