<#assign gridName="attClassGrid${uuid!}">
<script type="text/javascript">

	/*参考deptIncludeLower.js配置是否显示下级部门树，以及部门树相关操作*/
	/*当前部门树ID*/
	attGlobalDeptTreeId = "deptTree";
	/*当前gridName*/
	attGlobalGridName = "${gridName}";
	/*是否显示包含下级*/
	attGlobalShowIncludeLower = true;

</script>

<!--导出模版表单-->
<form style="display: none" method="POST" action="attClass.do?importTemplate" id="attImportTemplateFrom" enctype="multipart/form-data">
	<input type="hidden" name="reportType" value="XLS"/>
	<input type="hidden" name="tableNameParam" value="<@i18n 'att_exception_classImportTemplate'/>"/>
</form>

<@ZKUI.GridBox gridName="${gridName}" showColumns="!adjustDeptCode,swapDeptCode,operateDatetime">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
				<td valign="middle">
					<@ZKUI.Input type="date" id="startTime${uuid}" endId="endTimeId${uuid}" name="adjustDateGE" title="common_time_from" todayRange="start" today="-1"  readonly="false"/>
					&nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
					<@ZKUI.Input type="date" id="endTimeId${uuid}" name="adjustDateLE" title="common_to" todayRange="end" today="true" offset="1"  hideLabel="true" readonly="false"/>
    			</td>
				<td valign="middle">
					<@ZKUI.Input name="adjustPersonPin"  maxlength="30" title="att_person_pin" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="likeName"  maxlength="30" title="pers_person_wholeName" type="text"/>
				</td>
			</tr>
    	</@ZKUI.SearchTop>
 		<@ZKUI.SearchBelow>
 			<tr>
 				<td valign="middle">
					<@ZKUI.Input name="adjustDeptCode"  maxlength="30" title="pers_dept_deptNo" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="adjustDeptName"  maxlength="30" title="pers_dept_deptName" type="text"/>
				</td>
			</tr>
 		</@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
<@ZKUI.Layout style="position:absolute; top:70px;bottom:0px;left:0px;right:0px;height:auto;" onInit="attGlobalInitDeptTree">
	<@ZKUI.Cell width="240" treeId="deptTree">
		<@ZKUI.Tree dynamic="true" id="deptTree"  url="authDepartment.do?dynaTree&showPersonCount=false" onClick="attGlobalDeptTreeClick"></@ZKUI.Tree>
	</@ZKUI.Cell>
	<@ZKUI.Cell hideHeader="true">
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:class:refresh"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="attClass.do?edit" text="common_op_new" width="800" height="320" img="comm_add.png" action="commonAdd" permission="att:class:add"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="attClass.do?del&pins=(adjustPersonPin)" text="common_op_del" img="comm_del.png" action="commonDel" permission="att:class:del"></@ZKUI.ToolItem>
        <@ZKUI.ToolItem id="attClass.do?export" type="export" text="common_op_export" img="comm_export.png" permission="att:class:export"/>
		<@ZKUI.ToolItem type="more" text="att_flow_approve" img="att_approval.png" >
			<@ZKUI.ToolItem id="attClass.do?approval&pins=(personPin)" text="att_apply_pass" img="att_approval_pass.png" action="commonOperate" permission="att:attClass:approval"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="attClass.do?refuse&pins=(personPin)" text="att_exception_refuse" img="att_approval_refuse.png" action="commonOperate" permission="att:attClass:approval"></@ZKUI.ToolItem>
		</@ZKUI.ToolItem>
		<@ZKUI.ToolItem type="more" text="common_op_import" img="comm_import.png" >
			<@ZKUI.ToolItem id="attClass.do?import" type="import" showImportProcess="true" onFinish="attGlobalReloadGrid" text="common_op_import" img="comm_import.png" permission="att:class:import"/>
			<@ZKUI.ToolItem action="attGlobalImportTemplate" type="export" text="att_exception_downTemplate" title="att_exception_downTemplate" img="common_download.png" permission="att:class:import"/>
		</@ZKUI.ToolItem>

	</@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttClassItem" query="attClass.do?list"/>
	</@ZKUI.Cell>
</@ZKUI.Layout>
</@ZKUI.GridBox>