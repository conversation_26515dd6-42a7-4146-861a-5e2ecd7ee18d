<style>
	.classAttSchDetailsTabBar {
		width: 100%;
		bottom: 0px;
		top: 30px;
		position: absolute;
	}

	.classAttDeptCycSchTabBox {
		bottom: 0px;
		top: 0px;
		right: 0px;
		left: 0px;
		position: absolute;
	}
</style>
<script type="text/javascript">
	/*当前点击的tab编号*/
	var attSchDetailsTabId = "attPersonCycSchTab";
	var attSchDetailsTabBar = null;
	/*当前点击的部门ID*/
	var attSchDetailsDeptTreeClickId = "";
	/*是否包含下级*/
	var attSchDetailsIsIncludeLower = true;

	/*获取当前tab点击的Grid名称*/
	function getGridName() {
		return attSchDetailsTabId + "Grid";
	}

	/**是否包含下级部门*/
	function attSchDetailsDeptHandler(layout) {
		$("#isIncludeLower").val(false);//默认不包含子部门
		var html = "<span><div id='attIsIncludeLower' class='tree_header_box'>" +
            "<span onclick='attSchDetailsImgClick(1)' class='plus_minus_img icv-head-plus'></span>" +
            "<span onclick='attSchDetailsImgClick(0)' class='plus_minus_img icv-head-minus'></span>" +
        	"<span id='attSchDetailsIncludeLowerCheckboxSpan'></span><span><@i18n 'common_tree_containsSubordinate'/></span></div></span>";
		layout.cells("a").setText(html);
		loadUIToDiv("input", "#attSchDetailsIncludeLowerCheckboxSpan", {
			id: "attSchDetailsIncludeLowerCheckbox",
			useInputReq: true,
			type: "checkbox",
			value: true,
			eventCheck: true,
			onclick: "attSchDetailsIncludeLowerLink()"
		})

		layout.attachEvent("onExpand", function(name){
			if (attSchDetailsTabBar) {
				attSchDetailsTabBar.setSizes();
			}
			resizeUI();
			$("#attIsIncludeLower").show();
		});

		layout.attachEvent("onCollapse", function(name){
			if (attSchDetailsTabBar) {
				attSchDetailsTabBar.setSizes();
			}
			resizeUI();
			$("#attIsIncludeLower").hide();
		});
	}

	/**是否包含下级部门*/
	function attSchDetailsIncludeLowerLink() {
		var value = !$("#attSchDetailsIncludeLowerCheckbox").prop("checked")
		var tree = ZKUI.Tree.get("attSchDetailsDeptTree").tree;
		tree.enableThreeStateCheckboxes(value);
		attSchDetailsIsIncludeLower = value;
		loadAttSchDetailsGrid();
	}

	/*部门树展开、收缩*/
	function attSchDetailsImgClick(flag) {
		var tree = ZKUI.Tree.get("attSchDetailsDeptTree").tree;
		if (flag == 1) {
			tree.openAllItems(0);
		} else {
			tree.closeAllItems(0);
		}
	}

	/*点击部门树刷新考勤人员列表*/
	function attSchDetailsDeptTreeClick(id) {
		attSchDetailsDeptTreeClickId = id;
		loadAttSchDetailsGrid();
	}

	/*编辑后刷新*/
	function attPersonSchReloadGrid() {
		ZKUI.Grid.reloadGrid(getGridName());
	}

	/*导出*/
	function attSchDetailsExport(d, c, b) {
		if (attSchDetailsTabId == "attPersonCycSchTab") {
			d = "attCycleSch.do?export&cycleType=2";
		} else if (attSchDetailsTabId == "attDeptCycSchTab") {
			d = "attCycleSch.do?export&cycleType=1";
		} else if (attSchDetailsTabId == "attGroupCycSchTab") {
			d = "attCycleSch.do?export&cycleType=0";
		} else if (attSchDetailsTabId == "attPersonTempSchTab") {
			d = "attTempSch.do?export&tempType=2";
		} else if (attSchDetailsTabId == "attDeptTempSchTab") {
			d = "attTempSch.do?export&tempType=1";
		} else if (attSchDetailsTabId == "attGroupTempSchTab") {
			d = "attTempSch.do?export&tempType=0";
		}

		var a = getGridName();
		b.path = "skip.do?page=public_template_opExportRecord&gridName=" + a + "&actionName=" + encodeURIComponent(d);
		if (b.maxExportCount) {
			b.path = b.path + "&maxExportCount=" + b.maxExportCount
		}
		b.width = b.width || 550;
		b.height = b.height || 330;
		DhxCommon.createWindow(b)
	}

	/*vo配置点击删除函数*/
	function attSchDetailsDelVo(gridName, obj, rid) {
		var id;
		if (attSchDetailsTabId == "attPersonCycSchTab") {
			id = "attCycleSch.do?del&names=(personPin)";
		} else if (attSchDetailsTabId == "attDeptCycSchTab") {
			id = "attCycleSch.do?del&names=(deptName)";
		} else if (attSchDetailsTabId == "attGroupCycSchTab") {
			id = "attCycleSch.do?del&names=(groupName)";
		} else if (attSchDetailsTabId == "attPersonTempSchTab") {
			id = "attTempSch.do?del&names=(personPin)";
		} else if (attSchDetailsTabId == "attDeptTempSchTab") {
			id = "attTempSch.do?del&names=(deptName)";
		} else if (attSchDetailsTabId == "attGroupTempSchTab") {
			id = "attTempSch.do?del&names=(groupName)";
		}
		var gridName = getGridName();
		deleteConfirm(function (result) {
			if (result) {
				openMessage(msgType.loading);
				var param = splitURL(fillParamsFromGrid(gridName, rid, id));
				param.data.ids = rid;
				$.ajax({
					url: param.url,
					type: "post",
					data: param.data,
					success: function (result) {
						closeMessage();
						dealRetResult(eval(result),
								function () {
									ZKUI.Grid.reloadGrid(gridName);
								})
					}
				})
			}
		},
		"common_prompt_sureToDelThese")
	}

	function attSchDetailsDel(id, bar, opt) {

		if (attSchDetailsTabId == "attPersonCycSchTab") {
			id = "attCycleSch.do?del&names=(personPin)";
		} else if (attSchDetailsTabId == "attDeptCycSchTab") {
			id = "attCycleSch.do?del&names=(deptName)";
		} else if (attSchDetailsTabId == "attGroupCycSchTab") {
			id = "attCycleSch.do?del&names=(groupName)";
		} else if (attSchDetailsTabId == "attPersonTempSchTab") {
			id = "attTempSch.do?del&names=(personPin)";
		} else if (attSchDetailsTabId == "attDeptTempSchTab") {
			id = "attTempSch.do?del&names=(deptName)";
		} else if (attSchDetailsTabId == "attGroupTempSchTab") {
			id = "attTempSch.do?del&names=(groupName)";
		}

		if (bar) {
			var gridName = getGridName();
			var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
			if (ids == "") {
				messageBox({
					messageType: "alert",
					text: "common_prompt_selectObj"
				})
			} else {
				deleteConfirm(function (result) {
					if (result) {
						openMessage(msgType.loading);
						var param = splitURL(fillParamsFromGrid(gridName, ids, id));
						param.data.ids = ids;
						$.ajax({
							url: param.url,
							type: "post",
							data: param.data,
							success: function (result) {
								closeMessage();
								dealRetResult(eval(result),
									function () {
										if (opt.callback && typeof (window[opt.callback]) == "function") {
											window[opt.callback](gridName, result)
										} else {
											if (typeof (opt.callback) == "function") {
												opt.callback(gridName, result)
											} else {
												ZKUI.Grid.reloadGrid(gridName)
											}
										}
									})
							}
						})
					}
				},
				"common_prompt_sureToDelThese")
			}
		}
	}

</script>
<@ZKUI.GridBox showColumns="!map">
	<@ZKUI.Searchbar onBeforeQuery="attSchDetailsOnBeforeQuery">
		<@ZKUI.SearchTop>
			<tr>
				<td valign="middle">
					<@ZKUI.Input type="date" name="attSchDetails_startDate" id="startDate${uuid!}" endId="endDate${uuid!}" title="common_time_from" todayRange="start" today="true" readonly="false"/>
					&nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
					<@ZKUI.Input type="date" name="attSchDetails_endDate" id="endDate${uuid!}" title="common_to" today="true" offset="1" hideLabel="true" readonly="false"/>
				</td>
				<#if !staff>
					<td valign="middle">
						<@ZKUI.Input id="personPin${uuid!}" title="att_person_pin" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Input id="deptName${uuid!}" title="pers_dept_deptName" type="text"/>
					</td>
				</#if>
			</tr>
		</@ZKUI.SearchTop>
		<@ZKUI.SearchBelow>
		<tr>
			<td valign="middle">
				<@ZKUI.Input id="groupName${uuid!}" maxlength="30" title="att_common_groupName" type="text"/>
			</td>
		</tr>
		</@ZKUI.SearchBelow>
	</@ZKUI.Searchbar>
	<@ZKUI.Layout style="position:absolute; top:70px;bottom:0px;left:0px;right:0px;height:auto;" pattern="2U" onInit="attSchDetailsDeptHandler">
		<@ZKUI.Cell width="240">
			<@ZKUI.Tree dynamic="true" id="attSchDetailsDeptTree"  url="authDepartment.do?dynaTree&showPersonCount=false" onClick="attSchDetailsDeptTreeClick"></@ZKUI.Tree>
		</@ZKUI.Cell>
		<@ZKUI.Cell hideHeader="true">
			<@ZKUI.Toolbar>
				<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="loadAttSchDetailsGrid" permission=""/>
				<@ZKUI.ToolItem text="common_op_del" img="comm_del.png" action="attSchDetailsDel" permission="att:schDetails:del"/>
				<@ZKUI.ToolItem text="common_op_export" img="comm_export.png" action="attSchDetailsExport" type="export" permission="att:schDetails:export"/>
			</@ZKUI.Toolbar>
			<div class="classAttSchDetailsTabBar" id="attSchDetailsTabBar">
				<div id="attPersonCycSchTab">
					<div class="classAttDeptCycSchTabBox" id="attPersonCycSchTabBox"></div>
				</div>
				<div id="attDeptCycSchTab">
					<div class="classAttDeptCycSchTabBox" id="attDeptCycSchTabBox"></div>
				</div>
				<div id="attGroupCycSchTab">
					<div class="classAttDeptCycSchTabBox" id="attGroupCycSchTabBox"></div>
				</div>
				<div id="attPersonTempSchTab">
					<div class="classAttDeptCycSchTabBox" id="attPersonTempSchTabBox"></div>
				</div>
				<div id="attDeptTempSchTab">
					<div class="classAttDeptCycSchTabBox" id="attDeptTempSchTabBox"></div>
				</div>
				<div id="attGroupTempSchTab">
					<div class="classAttDeptCycSchTabBox" id="attGroupTempSchTabBox"></div>
				</div>
			</div>
		</@ZKUI.Cell>
	</@ZKUI.Layout>
</@ZKUI.GridBox>
<script type="text/javascript">

	$().ready(function() {
		/*初始化tab*/
		initAttSchDetailsTabBar();
		/*加载数据*/
		loadAttSchDetailsGrid();
	});

	/*初始化*/
	function initAttSchDetailsTabBar() {
		attSchDetailsTabBar = new dhtmlXTabBar("attSchDetailsTabBar", "top");
		/*人员周期排班*/
		attSchDetailsTabBar.addTab("attPersonCycSchTab", "<@i18n 'att_personSch_personCycleSch'/>");
		attSchDetailsTabBar.tabs("attPersonCycSchTab").attachObject("attPersonCycSchTab", true);
		attSchDetailsTabBar.tabs("attPersonCycSchTab").setActive();
		/*部门周期排班*/
		attSchDetailsTabBar.addTab("attDeptCycSchTab", "<@i18n 'att_personSch_deptCycleSch'/>");
		attSchDetailsTabBar.tabs("attDeptCycSchTab").attachObject("attDeptCycSchTab", true);
		/*分组周期排班*/
		attSchDetailsTabBar.addTab("attGroupCycSchTab", "<@i18n 'att_personSch_groupCycleSch'/>");
		attSchDetailsTabBar.tabs("attGroupCycSchTab").attachObject("attGroupCycSchTab", true);
		/*人员临时排班*/
		attSchDetailsTabBar.addTab("attPersonTempSchTab", "<@i18n 'att_personSch_personTempSch'/>");
		attSchDetailsTabBar.tabs("attPersonTempSchTab").attachObject("attPersonTempSchTab", true);
		/*部门临时排班*/
		attSchDetailsTabBar.addTab("attDeptTempSchTab", "<@i18n 'att_personSch_deptTempSch'/>");
		attSchDetailsTabBar.tabs("attDeptTempSchTab").attachObject("attDeptTempSchTab", true);
		/*分组临时排班*/
		attSchDetailsTabBar.addTab("attGroupTempSchTab", "<@i18n 'att_personSch_groupTempSch'/>");
		attSchDetailsTabBar.tabs("attGroupTempSchTab").attachObject("attGroupTempSchTab", true);
		attSchDetailsTabBar.attachEvent("onSelect", function (id, lastId) {
			attSchDetailsTabId = id;
			loadAttSchDetailsGrid();
			return true;
		});
	}

	/*点击搜索*/
	function attSchDetailsOnBeforeQuery() {
		loadAttSchDetailsGrid();
		return false;
	}

	/*根据当前tab加载对应的grid数据*/
	function loadAttSchDetailsGrid() {
		var queryParamCommon = {
			startTime: $('input[name="attSchDetails_startDate"]').val(),
			endTime: $('input[name="attSchDetails_endDate"]').val(),
			deptId: attSchDetailsDeptTreeClickId,
			personPin: $('#personPin${uuid!}').val(),
			likeName: $('#likeName${uuid!}').val(),
			deptName: $('#deptName${uuid!}').val(),
			groupName: $('#groupName${uuid!}').val(),
			isIncludeLower: attSchDetailsIsIncludeLower
		};
		if (attSchDetailsTabId == "attPersonCycSchTab") {
		    var queryParam = {
				cycleType: '2'
            };
			jQuery.extend(queryParam, queryParamCommon);
			var opts = {
				gridName: getGridName(),
				vo: "com.zkteco.zkbiosecurity.att.vo.AttCycleSchItem",
				query: "attCycleSch.do?list",
                queryParam: JSON.stringify(queryParam),
				showColumns: "!groupName"
			};
			$("#attPersonCycSchTabBox").loadGrid(opts);

		} else if (attSchDetailsTabId == "attDeptCycSchTab") {
			var queryParam = {
				cycleType: '1'
			};
			jQuery.extend(queryParam, queryParamCommon);
			var opts = {
				gridName: getGridName(),
				vo: "com.zkteco.zkbiosecurity.att.vo.AttCycleSchItem",
				query: "attCycleSch.do?list",
				queryParam: JSON.stringify(queryParam),
				showColumns: "!groupName,personPin,personName,personLastName"
			};
			$("#attDeptCycSchTabBox").loadGrid(opts);

		} else if (attSchDetailsTabId == "attGroupCycSchTab") {
			var queryParam = {
				cycleType: '0'
			};
			jQuery.extend(queryParam, queryParamCommon);
			var opts = {
				gridName: getGridName(),
				vo: "com.zkteco.zkbiosecurity.att.vo.AttCycleSchItem",
				query: "attCycleSch.do?list",
				queryParam: JSON.stringify(queryParam),
				showColumns: "!deptName,personPin,personName,personLastName"
			};
			$("#attGroupCycSchTabBox").loadGrid(opts);

		} else if (attSchDetailsTabId == "attPersonTempSchTab") {
			var queryParam = {
				tempType: '2'
			};
			jQuery.extend(queryParam, queryParamCommon);
			var opts = {
				gridName: getGridName(),
				vo: "com.zkteco.zkbiosecurity.att.vo.AttTempSchItem",
				query: "attTempSch.do?list",
				queryParam: JSON.stringify(queryParam),
				showColumns: "!groupName"
			};
			$("#attPersonTempSchTabBox").loadGrid(opts);

		} else if (attSchDetailsTabId == "attDeptTempSchTab") {
			var queryParam = {
				tempType: '1'
			};
			jQuery.extend(queryParam, queryParamCommon);
			var opts = {
				gridName: getGridName(),
				vo: "com.zkteco.zkbiosecurity.att.vo.AttTempSchItem",
				query: "attTempSch.do?list",
				queryParam: JSON.stringify(queryParam),
				showColumns: "!groupName,personPin,personName,personLastName"
			};
			$("#attDeptTempSchTabBox").loadGrid(opts);
		} else if (attSchDetailsTabId == "attGroupTempSchTab") {
			var queryParam = {
				tempType: '0'
			};
			jQuery.extend(queryParam, queryParamCommon);
			var opts = {
				gridName: getGridName(),
				vo: "com.zkteco.zkbiosecurity.att.vo.AttTempSchItem",
				query: "attTempSch.do?list",
				queryParam: JSON.stringify(queryParam),
				showColumns: "!deptName,personPin,personName,personLastName"
			};
			$("#attGroupTempSchTabBox").loadGrid(opts);
		}
	}

</script>