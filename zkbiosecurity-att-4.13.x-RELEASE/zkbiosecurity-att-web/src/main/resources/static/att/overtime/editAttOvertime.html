<#include '/public/template/editTemplate.html'>
<script type='text/javascript'>
function setAttOvertimeLong(){
	setTimeout(function(){
		console.log(1)
		var attOverStartTime = $("#${formId} input[name='startDatetime']").val();
		var attOverEndTime = $("#${formId} input[name='endDatetime']").val();
		var overStartTime = new Date(attOverStartTime.replace(/-/g, "/"));
		var overEndTime = new Date(attOverEndTime.replace(/-/g, "/"));
		console.log(overStartTime)
		console.log(overEndTime)
		if (overEndTime.getTime()>overStartTime.getTime()){
			var second = (overEndTime.getTime() - overStartTime.getTime())/1000;
			var attOverLongMinute = Math.ceil(second / 60);
			$("#idOvertimeLong").val(attOverLongMinute);
		} else {
			$("#idOvertimeLong").val(0);
		}
	},100);
}
</script>
<#macro editContent>
	<div class="attSelectPersonDiv">
		<#include '/att/person/selectPersonContent.html'>
	</div>
	<form action='attOvertime.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
		<input type='hidden' name='id' value='${(item.id)!}'/>
		<!-- 人员id -->
		<input id="personIds" name="personIds" type="hidden" />
		<input id="personPins" name="personPin" type="hidden" />
		<table class='tableStyle' style="margin-top: 10px">
			<tr>
				<!-- 加班开始时间 -->
				<th><label><@i18n 'common_startTime'/></label><span class='required'>*</span></th>
				<td><@ZKUI.Input id="attOverStartTimeId" endId="attOverEndTimeId" onHide="setAttOvertimeLong" type="datetime" value="${(item.startDatetime)!}" readonly="true" today="true" name="startDatetime" hideLabel="true" title="common_startTime"/></td>
				<!-- 加班时长 -->
				<th><label><@i18n 'att_overtime_overtimeLong'/></label></th>
				<td><input id="idOvertimeLong" name="overtimeLong"  maxlength="30" type="text" value="${(item.overtimeLong)!'0'}" readonly="readonly"/></td>
			</tr>
			<tr>
				<!-- 加班结束时间 -->
				<th><label><@i18n 'common_endTime'/></label><span class='required'>*</span></th>
				<td><@ZKUI.Input id="attOverEndTimeId" onHide="setAttOvertimeLong" type="datetime" value="${(item.endDatetime)!}" readonly="true" today="true" name="endDatetime" hideLabel="true" title="common_endTime"/></td>
				<!-- 加班理由 -->
				<th rowspan="2"><label><@i18n 'common_remark'/></label></th>
				<td rowspan="2"><textarea rows="2" style="width: 148px;height: 50px" name='remark' maxlength="100" type='text' value='${(item.remark)!}'/></td>
			</tr>
			<tr style="display:none">
				<th><label><@i18n 'att_shift_overtimeSign'/></label><span class='required'>*</span></th>
				<td>
					<@ZKUI.Combo empty="false" hideLabel="true" name="overtimeSign" value="${(item.overtimeSign)!}" title="att_overtime_type" width="148">
					<option value="0"><@i18n 'att_overtime_normal'/></option>
					<option value="1"><@i18n 'att_overtime_rest'/></option>
					<option value="2"><@i18n 'att_shift_holidayOt'/></option>
				</@ZKUI.Combo>
				</td>
			</tr>
		</table>
	</form>

	<script type='text/javascript'>



		$().ready(function() {
			$('#${formId}').validate( {
				debug : true,
				rules :
				{
					'startDatetime' :
					{
						required : true
					},
					'endDatetime' :
					{
						required : true
					}
				},
				submitHandler : function()
				{
                    var startTime = $("#${formId} input[name='startDatetime']").val();
                    var endTime = $("#${formId} input[name='endDatetime']").val();
                    var personIds = ZKUI.PersSelect.get("selectPerson${uuid!}").getRightGridSelectIds();// 选人控件取值
                    var selectConten = ZKUI.PersSelect.get("selectPerson${uuid!}").getSelectRows();

                    var start_date = new Date(startTime.replace(/-/g,"/"));
                    var end_date = new Date(endTime.replace(/-/g,"/"));
                    var result = end_date.getTime()-start_date.getTime();
                    //开始时间与结束时间对比
                    if (startTime > endTime) {
                        //开始时间不能大于结束时间
                        messageBox({
                            messageType: "alert",
                            title: "<@i18n 'common_prompt_title'/>",
                            text: "<@i18n 'common_dsTime_timeValid4'/>"
                        });
                        return false;
                    }
                    else if(startTime == endTime){
                    	//结束时间不能等于开始时间
                    	messageBox({
                            messageType : "alert",
                            title : "<@i18n 'common_prompt_title'/>",
                            text : "<@i18n 'common_dsTime_timeValid1'/>"
                        });
                        return false;
                    }
                    else if(result > (24*3600*1000)){
						//开始时间不能大于结束时间
						messageBox({
							messageType: "alert",
							title: "<@i18n 'common_prompt_title'/>",
							text: "<@i18n 'att_overtime_notice'/>"
						});
						return false;
					}
                    else if(personIds == "")
                    {
                        messageBox({
                            messageType : "alert",
                            text : "<@i18n 'att_common_neesSelectPerson'/>"
                        });
                    }
                    else
                    {
                        $("#personIds").val(personIds);

						var personPins = "";
						for(var i=0; i<selectConten.length; i++)
						{
							personPins += selectConten[i].personPin + ",";
						}
						$("#personPins").val(personPins.substring(0,personPins.length-1));

                        //判断相同时间内是否存在其他申请
                        $.ajax({
                            type: "post",
                            url: "attLeave.do?existApply",
                            data:{
                                personIds:personIds,
                                startTime:startTime,
                                endTime:endTime,
                                exceptionType:"overtime"
                            },
                            dataType: "json",
                            async: false,
                            success: function (result) {
                                if(result.ret == "ok")
                                {
                                	<@submitHandler/>
                                }
                                else
                                {
                                    var ptimeout = sysCfg.ptimeout;
                                    openMessage(msgType.warning, result[sysCfg.msg], ptimeout);
                                }
                            }
                        });
                    }
				}
			});
		});

        var minDate = new Date();
        minDate.setMonth(minDate.getMonth()-2);
        minDate.setDate(1);
        ZKUI.Input.PULL.attOverStartTimeId.setMinDate(minDate);
        ZKUI.Input.PULL.attOverEndTimeId.setMinDate(minDate);

	</script>
</#macro>