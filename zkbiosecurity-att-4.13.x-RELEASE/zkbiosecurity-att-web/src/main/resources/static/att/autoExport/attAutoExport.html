<#assign gridName="attAutoExportGrid${uuid!}">
<script type="text/javascript">

    function jobStatusConvertToIcon(v) {
        if(v == "0") {
        	return '<div class="icon_state_no"></div>';
        } else {
            return '<div class="icon_state_yes"></div>';
        }
    }

</script>
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
    			<td valign="middle">
					<@ZKUI.Input name="fileName"  maxlength="30" title="att_autoExport_fileName" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Combo empty="true" name="reportType" title="att_autoExport_reportType">
			    		<option value="0"><@i18n 'att_leftMenu_transaction'/></option>
			    		<option value="1"><@i18n 'att_leftMenu_dayCardDetailReport'/></option>
						<option value="2"><@i18n 'att_leftMenu_dayDetailReport'/></option>
						<!-- 出勤异常表 -->
						<option value="3"><@i18n 'att_leftMenu_abnormal'/></option>
						<!-- 月考勤状态表 -->
						<option value="4"><@i18n 'att_leftMenu_monthDetailReport'/></option>
						<!-- 人员汇总表 -->
						<option value="5"><@i18n 'att_leftMenu_monthStatisticalReport'/></option>
			    	</@ZKUI.Combo>
				</td>
				<td valign="middle">
					<@ZKUI.Combo empty="true" name="sendFormat" title="att_autoExport_sendFormat">
					<option value="0"><@i18n 'att_autoExport_mailFormat'/></option>
					<option value="1"><@i18n 'att_autoExport_ftpFormat'/></option>
					</@ZKUI.Combo>
				</td>
			</tr>
    	</@ZKUI.SearchTop>
 		<@ZKUI.SearchBelow>
			<tr>
				<td valign="middle">
					<@ZKUI.Combo empty="true" name="jobStatus" title="common_inOutStatus">
			    		<option value="0"><@i18n 'common_disable'/></option>
			    		<option value="1"><@i18n 'common_enable'/></option>
			    	</@ZKUI.Combo>
				</td>
			</tr>
 		</@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:autoExport:refresh"></@ZKUI.ToolItem>
    	<@ZKUI.ToolItem id="attAutoExport.do?edit" text="common_op_new" width="900" height="580" img="comm_add.png" action="commonAdd" permission="att:autoExport:add"></@ZKUI.ToolItem>
    	<@ZKUI.ToolItem id="attAutoExport.do?del" text="common_op_del" img="comm_del.png" action="commonDel" permission="att:autoExport:del"></@ZKUI.ToolItem>
        <@ZKUI.ToolItem id="attAutoExport.do?export" isShow="false" type="export" text="common_op_export" img="comm_export.png" permission="att:autoExport:export"/>
        <@ZKUI.ToolItem id="attAutoExport.do?enable" text="common_enable" width="500" img="comm_enable.png" action="commonOperate" permission="att:autoExport:enable"/>
        <@ZKUI.ToolItem id="attAutoExport.do?disable" text="common_commStatus_disable" width="500" img="comm_disable.png" action="commonOperate" permission="att:autoExport:disable"/>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttAutoExportItem" query="attAutoExport.do?list"/>
</@ZKUI.GridBox>