<#include '/public/template/editTemplate.html'>
<#macro editContent>

	<form action='attAutoExport.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
		<input type='hidden' name='id' value='${(item.id)!}'/>
		<input id="idTimeSendInterval" name="timeSendInterval" type="hidden" value="${(item.timeSendInterval)!}" />
		<table class="tableStyle">
			<tbody>
				<tr>
					<td style="width: 50% !important;">
						<!-- 左侧 -->
						<fieldset class="autoExportLeft">
							<!-- 报表设置 -->
							<legend><@i18n 'att_autoExport_reportSet'/></legend>
					    	<table class="tableStyle">
						    	<tbody>
						    		<tr>
						    			<!-- 报表类型 -->
							    		<th style="width: 100px !important;"><label><@i18n 'att_autoExport_reportType'/></label><span class="required">*</span></th>
							    		<td>
											<@ZKUI.Combo id="idReportType" width="148" empty="false" value="${(item.reportType)!}"  hideLabel="true" name="reportType">
												<!-- 原始记录表 -->
												<option value="0">
													<@i18n 'att_leftMenu_transaction'/>
												</option>
												<!-- 日打卡详情表 -->
												<option value="1">
													<@i18n 'att_leftMenu_dayCardDetailReport'/>
												</option>
												<!-- 日详情表 -->
												<option value="2">
													<@i18n 'att_leftMenu_dayDetailReport'/>
												</option>
												<!-- 出勤异常表 -->
												<option value="3">
													<@i18n 'att_leftMenu_abnormal'/>
												</option>
												<!-- 月考勤状态表 -->
												<option value="4">
													<@i18n 'att_leftMenu_monthDetailReport'/>
												</option>
												<!-- 人员汇总表 -->
												<option value="5">
													<@i18n 'att_leftMenu_monthStatisticalReport'/>
												</option>
											</@ZKUI.Combo>
							    		</td>
						    		</tr>
						    		<tr>
						    			<!-- 文件名称 -->
							    		<th><label><@i18n 'att_autoExport_fileName'/></label><span class="required">*</span></th>
							    		<td>
								    		<input id="idFileName" name="fileName" type='text' maxlength="50" value="${(item.fileName)!}" />
							    		</td>
						    		</tr>
									<tr>
										<th><@i18n 'att_autoExport_fileDateFormat'/></label></th>
										<td>
											<@ZKUI.Combo width="148" empty="false" hideLabel="true" id="idFileDateFormat" name="fileDateFormat" style="margin-bottom:3px">
												<option value="yyyyMMdd" <#if (item.fileDateFormat)?exists && item.fileDateFormat=="yyyymmdd">selected="selected"</#if>>
												yyyyMMdd
												</option>
												<option value="yyyy-MM-dd" <#if (item.fileDateFormat)?exists && item.fileDateFormat=="yyyy-MM-dd">selected="selected"</#if>>
											yyyy-MM-dd
											</option>
											</@ZKUI.Combo>
										</td>
									</tr>
							    	<tr>
							    		<!-- 文件类型 -->
							    		<th><label><@i18n 'att_autoExport_fileType'/></label><span class="required">*</span></th>
							    		<td>
							    			<@ZKUI.Combo width="148" empty="false" hideLabel="true" id="idFileType" name="fileType" value="${(item.fileType)!'2'}">
							    				<option value="0">TXT</option>
												<option value="2">EXCEL</option>
								    		</@ZKUI.Combo>
							    		</td>
							    	</tr>						    		
					    		</tbody>
					    	</table>
						</fieldset>
						<br/>
						<fieldset class="autoExportRightTop">
							<legend><@i18n 'att_autoExport_timeSendFrequency'/></legend>
							<table class="tableStyle">
								<tbody>
								<tr>
									<!-- 发送频率 -->
									<th style="width: 100px !important;"><label><@i18n 'att_autoExport_timeSendFrequency'/></label></th>
									<td>
										<@ZKUI.Combo width="148" empty="false" hideLabel="true" id="idTimeSendFrequency" name="timeSendFrequency" value="${(item.timeSendFrequency)!}">
										<option value="0"><@i18n 'att_autoExport_byDay'/></option>
										<option value="1"><@i18n 'att_autoExport_byMonth'/></option>
									</@ZKUI.Combo>
									</td>
								</tr>
								<tr>
									<!-- 按日 -->
									<th id="day1"></th>
									<td id="day2">
										<div style="margin-bottom: 2px;">
											<@ZKUI.Combo hideLabel="true" width="70" empty="false" id="idHour1" name="hour1" onchange="hourChange(this.value, 1);">
												<option value="-1" selected="selected">----</option>
											</@ZKUI.Combo><@i18n 'common_hour'/> ：
											<@ZKUI.Combo hideLabel="true" width="70" empty="false" id="idMinute1" name="minute1">
												<option value="-1" selected="selected">----</option>
											</@ZKUI.Combo><@i18n 'common_minute'/>
										</div>
										<div style="margin-bottom: 2px;">
											<@ZKUI.Combo hideLabel="true" width="70" empty="false" id="idHour2" name="hour2" onchange="hourChange(this.value, 2);">
												<option value="-1" selected="selected">----</option>
											</@ZKUI.Combo><@i18n 'common_hour'/> ：
											<@ZKUI.Combo hideLabel="true" width="70" empty="false" id="idMinute2" name="minute2">
												<option value="-1" selected="selected">----</option>
											</@ZKUI.Combo><@i18n 'common_minute'/>
										</div>
										<div style="margin-bottom: 2px;">
											<@ZKUI.Combo hideLabel="true" width="70" empty="false" id="idHour3" name="hour3" onchange="hourChange(this.value, 3);">
												<option value="-1" selected="selected">----</option>
											</@ZKUI.Combo><@i18n 'common_hour'/> ：
											<@ZKUI.Combo hideLabel="true" width="70" empty="false" id="idMinute3" name="minute3">
												<option value="-1" selected="selected">----</option>
											</@ZKUI.Combo><@i18n 'common_minute'/>
										</div>
										<div style="margin-bottom: 2px;">
											<@ZKUI.Combo hideLabel="true" width="70" empty="false" id="idHour4" name="hour4" onchange="hourChange(this.value, 4);">
												<option value="-1" selected="selected">----</option>
											</@ZKUI.Combo><@i18n 'common_hour'/> ：
											<@ZKUI.Combo hideLabel="true" width="70" empty="false" id="idMinute4" name="minute4">
												<option value="-1" selected="selected">----</option>
											</@ZKUI.Combo><@i18n 'common_minute'/>
										</div>
										<div style="margin-bottom: 2px;">
											<@ZKUI.Combo hideLabel="true" width="70" empty="false" id="idHour5" name="hour5" onchange="hourChange(this.value, 5);">
												<option value="-1" selected="selected">----</option>
											</@ZKUI.Combo><@i18n 'common_hour'/> ：
											<@ZKUI.Combo hideLabel="true" width="70" empty="false" id="idMinute5" name="minute5">
												<option value="-1" selected="selected">----</option>
											</@ZKUI.Combo><@i18n 'common_minute'/>
										</div>
										<div style="margin-bottom: 2px;">
											<@ZKUI.Combo hideLabel="true" width="70" empty="false" id="idHour6" name="hour6">
												<option value="-1" selected="selected">----</option>
											</@ZKUI.Combo><@i18n 'common_hour'/> ：
											<@ZKUI.Combo hideLabel="true" width="70" empty="false" id="idMinute6" name="minute6">
												<option value="-1" selected="selected">----</option>
											</@ZKUI.Combo><@i18n 'common_minute'/>
										</div>
									</td>
									<!-- 按月 -->
									<th id="month1"></th>
									<td id="month2">
										<@ZKUI.Input hideLabel="true" id="idLastDayofMonth" name="checkOfMonth" type="radio" value="0" />
										<label for="idLastDayofMonth"><@i18n 'att_autoExport_lastDayofMonth'/></label>
										<br/>
										<@ZKUI.Input hideLabel="true" id="idFirstDayofMonth" name="checkOfMonth" type="radio" value="1" />
										<label for="idFirstDayofMonth"><@i18n 'att_autoExport_firstDayofMonth'/></label>
										<br/>
										<@ZKUI.Input hideLabel="true" id="idDayofMonthCheck" name="checkOfMonth" type="radio" value="2" />
										<label for="idDayofMonthCheck"><@i18n 'att_autoExport_dayofMonthCheck'/></label>
										<@ZKUI.Combo hideLabel="true" width="148" empty="false" id="idDayofMonth" name="dayofMonth">
											<option value="-1" selected="selected">--</option>
										</@ZKUI.Combo>
										<label for="idDayofMonth">（1-28）</label>
									</td>
								</tr>
								</tbody>
							</table>
						</fieldset>
					</td>
					<td style="width: 50% !important;">
						<!-- 右侧顶部 -->
						<fieldset >
							<legend><@i18n 'att_autoExport_sendFormatSet'/></legend>
							<table class="tableStyle">
								<tr>
									<!-- 发送方式-->
									<th style="width: 100px !important;"><label><@i18n 'att_autoExport_sendFormat'/></label><span class="required">*</span></th>
									<td>
										<@ZKUI.Combo id="idSendFormat" width="148" empty="false" value="${(item.sendFormat)!}"  hideLabel="true" name=" sendFormat">
										<!-- 邮箱发送形式 -->
										<option  value="0">
											<@i18n 'att_autoExport_mailFormat'/>
										</option>
										<!-- ftp发送形式 -->
										<option value="1">
											<@i18n 'att_autoExport_ftpFormat'/>
										</option>
										<!-- sftp发送形式 -->
										<option value="2">
											<@i18n 'att_autoExport_sftpFormat'/>
										</option>
									</@ZKUI.Combo>
									</td>
								</tr>
							</table>
						</fieldset>
						<br/>
						<!-- 右侧底部 -->
						<fieldset class="autoExportRightButtom" id="autoExportEmail">
							<legend ><@i18n 'att_autoExport_emailSet'/></legend>
							<table class="tableStyle">
								<tbody>
									<tr>
										<!-- 接收人设置 -->
										<th style="width: 100px !important;"><label><@i18n 'att_autoExport_emailTypeSet'/></label></th>
										<td>
											<@ZKUI.Combo width="148" empty="false" hideLabel="true" id="idEmailType" name="emailType" value="${(item.emailType)!}">
												<option value="0"><@i18n 'att_autoExport_byPersonSet'/></option>
												<option value="1"><@i18n 'att_autoExport_byDeptSet'/></option>
												<option value="2"><@i18n 'att_autoExport_byAreaSet'/></option>
											</@ZKUI.Combo >
										</td>
									</tr>
									<tr id="idEmailAddress">
										<!-- 邮箱地址 -->
										<th><label><@i18n 'att_autoExport_emailAddress'/></label><span class="required">*</span></th>
										<td>
											<textarea cols="22" id="idEmailInput" maxlength="200" name="emailRecipients" 
												placeholder="<@i18n 'common_linkIO_errorMailTip'/><@i18n 'att_autoExport_emailExample'/>"
													rows="5" style="width: 100%;vertical-align: bottom;">${(item.emailRecipients)!}</textarea>
							        	</td>
									</tr>
									<tr id="idAttDeptShowhide">
										<!-- 部门 -->
										<th><label><@i18n 'att_common_dept'/></label></th>
										<td>
											<!-- <div id="idDeptTree" name="deptId" objName="deptId" type="radio"></div> -->
										 	<@ZKUI.ComboTree width="148" type="radio" url="authDepartment.do?tree"  id="idDeptTree" value="${(item.deptId)!}"  hideLabel="true" name="deptId" readonly="true"/>
							        	</td>
									</tr>
									<tr id="idAttAreaShowhide">
										<!-- 考勤区域 -->
										<th><label><@i18n 'pers_person_attArea'/></label></th>
										<td>
											<!-- <div id="idAttAreaTree" name="areaId" objName="areaId" type="radio"></div> -->
										 	<@ZKUI.ComboTree width="148" type="radio" url="authArea.do?tree"  id="idAttAreaTree" value="${(item.areaId)!}" hideLabel="true" name="areaId" readonly="true"/>
										</td>
									</tr>
									<tr>
										<!-- 标题设置 -->
										<th><label><@i18n 'att_autoExport_emailSubjectSet'/></label><span class="required">*</span></th>
										<td>
											<textarea cols="12" id="idEmailSubject" maxlength="50" name="emailSubject"
												placeholder="<@i18n 'common_length'/>50"
												rows="2" style="width: 100%;vertical-align: bottom;">${(item.emailSubject)!}</textarea>
							        	</td>
									</tr>
									<tr>
										<!-- 正文设置 -->
										<th><label><@i18n 'att_autoExport_emailContentSet'/></label></th>
										<td>
											<textarea cols="22" id="idEmailContent" maxlength="200" name="emailContent"
												placeholder="<@i18n 'common_length'/>200" rows="7" style="width: 100%;">${(item.emailContent)!}</textarea>
							        	</td>
									</tr>
								</tbody>
							</table>
							<div id="setMailParam" style="margin-left: 10px; text-align: left;display:none;">
								<span class="warningImage"></span> <span class="warningColor"><@i18n 'common_email_checkMailServer'/></span>
								<br/>
								<@ZKUI.Permission name="system:mail:getMailParam">
								<a href="javascript:openSystemMailSetting()"><@i18n 'common_email_jumpToSetMail'/></a>
								</@ZKUI.Permission>
							</div>
						</fieldset>
						<fieldset class="autoExportRightButtom" id="autoExportFtp">
							<legend><@i18n 'common_leftMenu_paramSet'/></legend>
							<table class="tableStyle">
								<tbody>
								<tr >
									<td>
										<label><@i18n 'common_dev_serverAddress' /></label><span class="required">*</span>
									</td>
									<td>
										<input id="ftpUrl" name="ftpUrl" type="text" value="${(item.ftpUrl)!}"  maxlength="30" style="ime-mode:disabled" /><span class="gray" style="font-family: 'Times New Roman', Times, serif">(192.168.xxx.xxx)</span>
									</td>
								</tr>
								<tr >
									<td>
										<label><@i18n 'base_db_port' /></label><span class="required">*</span>
									</td>
									<td>
										<input id="ftpPort" name="ftpPort" type="text" value="${(item.ftpPort)!}"  maxlength="10" style="ime-mode:disabled" />
									</td>
								</tr>
								<tr>
									<td>
										<label><@i18n 'auth_user_username' /></label><span class="required">*</span>
									</td>
									<td>
										<input id="ftpUsername" name="ftpUsername" type="text" value="${(item.ftpUsername)!}"  maxlength="30" style="ime-mode:disabled" />
									</td>
								</tr>
								<tr >
									<td>
										<label><@i18n 'auth_user_password' /></label><span class="required">*</span>
									</td>
									<td>
										<input type="password" style="display:none"><!-- 为了去除谷歌浏览器记住密码 问题-->
										<input type="password" id="ftpPassword" name="ftpPassword" value="${(item.ftpPassword)!}" maxlength="30" style="ime-mode:disabled"  />
										<input type="password" style="display:none"><!-- 为了去除谷歌浏览器记住密码 问题-->
									</td>
								</tr>
								</tbody>
							</table>
							<!--<div style="margin-top: 10px;margin-left: 10px;">
								<span class="warningImage"></span>
								<span class="warningColor"><@i18n 'att_autoExport_correctFtpParam' /></span>
							</div>-->
							<div style="margin-top: 10px;margin-left: 10px;">
								<span class="warningImage"></span>
								<span class="warningColor"><@i18n 'att_autoExport_correctFtpTestParam' /></span>
							</div>
							<div style="margin-top: 10px;margin-left: 10px;">
								<input class="button-form" type="button" id="attTestSendButton" onclick="ftpTest()" name="attTestSendButton" value="<@i18n 'base_mail_connectTest' />" />&nbsp;&nbsp;&nbsp;<img id="loadImg" src="${base}/public/images/searching.gif" style="display:none"/><label id="testResultText"></label>
							</div>
						</fieldset>
					</td>
				</tr>
			</tbody>
		</table>
	</form>

<script type='text/javascript'>
	//added by jinxian.huang 2019-05-13
	function ftpTest(){
		clearTestResult();
		var ftpUrl = $("#ftpUrl").val();
		var ftpPort = $("#ftpPort").val();
		var ftpUsername = $("#ftpUsername").val();
		var ftpPassword = $("#ftpPassword").val();
		var sendFormatValue =  ZKUI.Combo.get("idSendFormat").combo.getSelected();
        if (ftpUrl == "" || ftpUrl == null) {
            // 请输入ftp服务地址
            messageBox({messageType: "alert", text: "<@i18n 'att_autoExport_inputFtpUrl'/>"});
            return false;
        }
        if (ftpPort == "" || ftpPort == null) {
            // 请输入ftp服务器端口
            messageBox({messageType: "alert", text: "<@i18n 'att_autoExport_inputFtpPort'/>"});
            return false;
        }
        if (ftpUsername == "" || ftpUsername == null) {
            // 请输入用户名
            messageBox({messageType: "alert", text: "<@i18n 'auth_mail_inputUser'/>"});
            return false;
        }
        if (ftpPassword == "" || ftpPassword == null) {
            // 请输入密码
            messageBox({messageType: "alert", text: "<@i18n 'auth_mail_inputPwd'/>"});
            return false;
        }
        
        // 测试等待加入旋转图标,(类似进度条)。 add by bob.liu 20191011
        $('#${formId} #attTestSendButton').attr("disabled","disabled");
        $('#${formId} #loadImg').css("display","inline");
        var ajax_option={
			dataType: "json",
			type: "post",
			url: "attAutoExport.do?ftpTest",
			data: {
				sendFormat: sendFormatValue,
				ftpUrl: ftpUrl,
				ftpPort: ftpPort,
				ftpUsername: ftpUsername,
				ftpPassword: window.btoa(ftpPassword)
			},
			success:function(data){
				$('#${formId} #loadImg').css("display","none");
                $('#${formId} #attTestSendButton').attr("disabled",false);
				var text = "";
				if (data && data.ret=="ok"){
					text = "<font color='green'>"+data.msg+"</font>";
				}else {
					text = "<font color='red'>"+data.msg+"</font>";
				}
				$("#${formId} #testResultText").html(text);
			},
			error:function(){
				$('#${formId} #loadImg').css("display","none");
				$('#${formId} #attTestSendButton').attr("disabled",false);
				document.getElementById("testResultText").innerText="<@i18n 'att_autoExport_ftpFail' />";
			}
		};
        $.ajax(ajax_option);
	}
	// 清空原测试结果 add by bob.liu 20191011
	function clearTestResult(){
        $("#${formId} #testResultText").html("");
    }

        //判断系统管理是否设置了邮箱参数，如果没有则提示设置邮箱。
	var isSetSystemMailParam = "${isSetSystemMailParam}";
	if(isSetSystemMailParam == "false")
	{
		$("#setMailParam").css("display","");
	}

    //打开系统管理邮箱参数设置
    function openSystemMailSetting(){

        DhxCommon.createWindow("baseMail.do?getMailParam^0^0^500^520^<@i18n 'base_mail_paramSetting'/>");
    }
	/*
	 ******************
	 * 自定义校验
	 ******************
	 */
	// 增加邮箱地址校验
	jQuery.validator.addMethod("emailValid", function(value, element) {
		var emailType = ZKUI.Combo.get("idEmailType").combo.getSelected();
		if (emailType == "0") {
			var strs = new Array(); //定义一数组
			strs = value.split(","); //字符分割
			for (i = 0; i < strs.length; i++) {
				var myreg = /^[_a-zA-Z0-9\-]+(\.[_a-zA-Z0-9\-]*)*@[a-zA-Z0-9\-]+([\.][a-zA-Z0-9\-]+)+$/;
				var bool = this.optional(element) || (myreg.test(strs[i]));
			}
	        if (bool == false) {
	        	return false;
	        } else {
	         	return true;
	        }
		} else {
			return true;
		}
	}, function() {
		return "<@i18n 'common_email_inputEmailError'/>";
	});
	jQuery.validator.addMethod("stringCheck", function(value, element) {
		var myreg = /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/;
		return this.optional(element) || myreg.test(value);
	}, function() {
		return "<@i18n 'common_prompt_unIncludeChar'/>";
	});
	//校验ftp地址
    jQuery.validator.addMethod("validFtpUrl", function(value, element) {
        var myreg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
        return this.optional(element) || myreg.test(value);
    }, function() {
        return "<@i18n 'att_autoExport_validFtp'/>";
    });
    jQuery.validator.addMethod("validPort", function(value, element) {
        var myreg = /^([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{4}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/;
        return this.optional(element) || myreg.test(value);
    }, function() {
        return "<@i18n 'att_autoExport_validPort'/>";
    });

    /*
     ******************
     * 表单验证
     ******************
     */
	$("#${formId}").validate({
		debug: true,
		rules: {
			"fileName": {
				required: true,
				//stringCheck: true
				unInputChar: true
			},
			"fileType": {
				required: true
			}
		},
		submitHandler: function() {
			// 给隐藏域idTimeSendInterval赋值，并校验参数
			before (function() {
		        <@submitHandler/>
	        });
		}
	});
	/*
	 ******************
	 * 事件定义
	 ******************
	 */
	// 载入部门树图
	// loadComboxTree("idDeptTree", "attDeptSchAction!getAttDeptTreeJson.action");
	// 载入区域树图
	// loadComboxTree("idAttAreaTree", "attAreaAction!getAreaTreeJson.action");
	/* checkMailParam();//验证发件邮箱是否设置
	function checkMailParam() {
		$.ajax({
		    async: false,
		    dataType: "text",
		    type: "post",
		    url: "attAutoExportAction!checkMailParam.action",
		    success: function(data) {
			    if (data == "ok") {
			    	$("#setMailParam").hide();
			    } else {
			    	$("#setMailParam").show();
			    }
		    }
		});
	} */

        // 根据发送方式显示对应的fieldset
        var showFieldset = "${(item.sendFormat)!}" == "" ? "0" : "${(item.sendFormat)!}";
        sendFormatShowHide(showFieldset);
        function sendFormatShowHide(sendFormat) {
            var sendFormat = sendFormat.toString();
            if (sendFormat == "0") {
<!--                $("#idMailTimeSet").show();-->
                $("#autoExportEmail").show();
<!--                $("#idFtpTimeSet").hide();-->
                $("#autoExportFtp").hide();
                $("#idEmailSubject").rules("add", {
                    required : true
                });
                $("#idEmailInput").rules("add", {
                    emailValid: true
                });
                $("#ftpUrl").rules("remove");
                $("#ftpPort").rules("remove");
                $("#ftpUsername").rules("remove");
                $("#ftpPassword").rules("remove");
                $("#ftpUrl").valid();
                $("#ftpPort").valid();
                $("#ftpUsername").valid();
                $("#ftpPassword").valid();

            } else if (sendFormat == "1" || sendFormat == "2") {
                $("#autoExportEmail").hide();
<!--                $("#idMailTimeSet").hide();-->
                $("#autoExportFtp").show();
<!--                $("#idFtpTimeSet").show();-->
                $("#ftpUrl").rules("add", {
                    required: true,
                    validFtpUrl:true
                });
                $("#ftpPort").rules("add", {
                    required: true,
                    validPort:true
                });
                $("#ftpUsername").rules("add", {
                    required: true,
                });
                $("#ftpPassword").rules("add", {
                    required: true
                });
                $("#idEmailSubject").rules("remove");
                $("#idEmailInput").rules("remove");
                $("#idEmailSubject").valid();
                $("#idEmailInput").valid();
            }
        }

        // 根据发送频率类型，展示相应字段
	var isShow = "${(item.timeSendFrequency)!}" == "" ? "0" : "${(item.timeSendFrequency)!}";
	timeSendFrequencyShowHide(isShow);
	function timeSendFrequencyShowHide(timeSendFrequency) {
		var timeSendFrequency = timeSendFrequency.toString();
		if (timeSendFrequency == "0") {
	  		// 按日
	  	 	$("#day1").show();
	  	 	$("#day2").show();
	  	 	$("#month1").hide();
	  	 	$("#month2").hide();
		} else if (timeSendFrequency == "1") {
	  		// 按月
	  	 	$("#day1").hide();
	  	 	$("#day2").hide();
	  	 	$("#month1").show();
	  	 	$("#month2").show();
		}
	}

	// 根据报表类型展示字段属性
 	var showFiled = "${(item.reportType)!}" == "" ? "0" : "${(item.reportType)!}";
	reportTypeShowHide(showFiled);
	function reportTypeShowHide(reportType) {
		var reportType = reportType.toString();
		// var isTxt = ZKUI.Combo.get("idFileType").combo.getSelectedValue() == "0";
		if (reportType == "0") {
			$("#idTransaction").show();
	  	 	$("#idDayCard").hide();
	  	 	// if (isTxt) {
	  	 		var content = "<@i18n 'pers_dept_deptNo'/>:{deptCode} <@i18n 'pers_dept_deptName'/>:{deptName} <@i18n 'att_person_pin'/>:{personPin} <@i18n 'att_person_name'/>:{personName} <#if Application['system.language'] != 'zh_CN'><@i18n 'att_person_lastName'/>:{personLastName}</#if> <@i18n 'common_dev_sn'/>:{deviceSn} <@i18n 'common_dev_name'/>:{deviceName} <@i18n 'att_attPoint_name'/>:{pointName} <@i18n 'pers_person_attArea'/>:{areaName} <@i18n 'att_statistical_attDatetime'/>:{attDatetime}";
				$("#idFileContentFormat").val(content);
			// }

		} else if (reportType == "1") {
			$("#idTransaction").hide();
	  	 	$("#idDayCard").show();
	  	 	// if (isTxt) {
				var content = "<@i18n 'pers_dept_deptNo'/>:{deptCode} <@i18n 'pers_dept_deptName'/>:{deptName} <@i18n 'att_person_pin'/>:{personPin} <@i18n 'att_person_name'/>:{personName} <#if Application['system.language'] != 'zh_CN'><@i18n 'att_person_lastName'/>:{personLastName}</#if> <@i18n 'att_statistical_cardDate'/>:{attDate} <@i18n 'att_statistical_earliestTime'/>:{earliestTime} <@i18n 'att_statistical_latestTime'/>:{latestTime} <@i18n 'att_statistical_cardTime'/>:{attTime}";
				$("#idFileContentFormat").val(content);
			// }
		}
	}

/* 	fileTypeChange("${(item.fileType)!'2'}");
	function fileTypeChange(fileType) {
		var isTxt = fileType == "0";
		$(".attFileContentTr").css("color", isTxt ? "#000" : "#999");
		$("#idFileContentFormat").attr("readonly", !isTxt);
		if (isTxt) { // TXT文件
			$("#idFileContentFormat").rules("add", {
				required: true
			});
			$("#attFileContentFormatSpan").show();
			//reportTypeShowHide(showFiled);
		} else if (fileType == "2") { // EXCEL文件
			$("#idFileContentFormat").rules("remove");
			$("#idFileContentFormat").valid();
			$("#attFileContentFormatSpan").hide();
		}
	} */

	// 邮件接收人设置触发事件
	var showEmailType = "${(item.emailType)!}" == "" ? "0" : "${(item.emailType)!}";
	emailTypeShowHide(showEmailType);
	function emailTypeShowHide(showEmailType) {
		var showEmailType = showEmailType.toString();
		if (showEmailType == "0") {
			$("#idEmailAddress").show();
	 		$("#idAttDeptShowhide").hide();
	 		$("#idAttAreaShowhide").hide();

		} else if (showEmailType == "1") {
			$("#idEmailAddress").hide();
	 		$("#idAttAreaShowhide").hide();
	 		$("#idAttDeptShowhide").show();
		} else if (showEmailType == "2") {
			$("#idEmailAddress").hide();
	 		$("#idAttAreaShowhide").show();
	 		$("#idAttDeptShowhide").hide();
		}
	}

	// 发送方式修改触发事件
    ZKUI.Combo.get("idSendFormat").combo.attachEvent("onChange",function(value,text){
        sendFormatShowHide(value);
    });


	// 发送频率修改触发事件
    ZKUI.Combo.get("idTimeSendFrequency").combo.attachEvent("onChange",function(value,text){
        timeSendFrequencyShowHide(value);
    });

	// 报表类型修改触发事件
    ZKUI.Combo.get("idReportType").combo.attachEvent("onChange",function(value,text){
        reportTypeShowHide(value);
    });

	// 文件类型修改触发事件
/* 	ZKUI.Combo.get("idFileType").combo.attachEvent("onChange",function(value,text){
		fileTypeChange(value);
	}); */

	// 邮件接收人设置触发事件
    ZKUI.Combo.get("idEmailType").combo.attachEvent("onChange",function(value,text){
        emailTypeShowHide(value);
    });

    ZKUI.Combo.get("idMinute1").combo.attachEvent("onChange",function(value,text){
        minute1Change(value);
    });

	// 分钟下拉框JS
	function minute1Change(value) {

		for(var i = 2; i < 7; i++) {
			ZKUI.Combo.get("idMinute" + i).combo.clearAll();
			ZKUI.Combo.get("idMinute" + i).clear();
			ZKUI.Combo.get("idMinute" + i).addOption([{value:"-1", text:"----"}]);
			ZKUI.Combo.get("idMinute" + i).combo.setComboValue("-1");
		}

		var minuteText = new Array();
		if (value != -1) {
			minuteText.push({text:value, value:value});
		}

		for(var i = 2; i < 7; i++) {
			ZKUI.Combo.get("idMinute" + i).addOption(minuteText);
		}
	}

	ZKUI.Combo.get("idHour1").combo.attachEvent("onChange",function(value,text){
			hourChange(value,1);
	});
	ZKUI.Combo.get("idHour2").combo.attachEvent("onChange",function(value,text){
			hourChange(value,2);
	});
	ZKUI.Combo.get("idHour3").combo.attachEvent("onChange",function(value,text){
			hourChange(value,3);
	});
	ZKUI.Combo.get("idHour4").combo.attachEvent("onChange",function(value,text){
			hourChange(value,4);
	});
	ZKUI.Combo.get("idHour5").combo.attachEvent("onChange",function(value,text){
			hourChange(value,5);
	});

	// 小时下拉框JS
	function hourChange(value, j) {
		var hourText = new Array();
		hourText.push({text:"----", value:"-1"});
		for (var i = parseInt(value) + 1; i < 24; i++) {
			if (i < 10) {
				hourText.push({text:"0"+i, value:"0"+i});
			} else {
				hourText.push({text:i+"", value:i+""});
			}
		}
		switch (j) {
			case 1:
				for (var k = 2; k < 7; k++) {
					// 时间下拉框JS
					ZKUI.Combo.get("idHour" + k).combo.clearAll();
					ZKUI.Combo.get("idHour" + k).addOption(hourText);
					ZKUI.Combo.get("idHour" + k).combo.setComboValue("-1");
				}
				break;
			case 2:
				for (var k = 3; k < 7; k++) {
					// 时间下拉框JS
					ZKUI.Combo.get("idHour" + k).combo.clearAll();
					ZKUI.Combo.get("idHour" + k).addOption(hourText);
					ZKUI.Combo.get("idHour" + k).combo.setComboValue("-1");
				}
				break;
			case 3:
				for (var k = 4; k < 7; k++) {
					// 时间下拉框JS
					ZKUI.Combo.get("idHour" + k).combo.clearAll();
					ZKUI.Combo.get("idHour" + k).addOption(hourText);
					ZKUI.Combo.get("idHour" + k).combo.setComboValue("-1");
				}
				break;
			case 4:
				for (var k = 5; k < 7; k++) {
					// 时间下拉框JS
					ZKUI.Combo.get("idHour" + k).combo.clearAll();
					ZKUI.Combo.get("idHour" + k).addOption(hourText);
					ZKUI.Combo.get("idHour" + k).combo.setComboValue("-1");
				}
				break;
			case 5:
				// 时间下拉框JS
				ZKUI.Combo.get("idHour6").combo.clearAll();
				ZKUI.Combo.get("idHour6").addOption(hourText);
				ZKUI.Combo.get("idHour6").combo.setComboValue("-1");
				break;
			default:
				break;
		}
	}

	// 时间下拉框JS
	//var hourText = "";
	var hourText = new Array();
	for (var i = 0; i < 24; i++) {
		if (i < 10) {
			hourText.push({text:"0"+ i, value:"0"+i});
		} else {
			hourText.push({text:i+"", value:i+""});
		}
	}
	var minuteText = new Array();
	for (var i = 0; i < 60; i++) {
		if (i < 10) {
			minuteText.push({text:"0"+i, value:"0"+i});
		} else {
			minuteText.push({text:i+"", value:i+""});
		}
	}
	var dayText = new Array();
	for (var i = 1; i < 29; i++) {
		dayText.push({text:i+"", value:i+""});
	}
	ZKUI.Combo.get("idHour1").addOption(hourText);
	ZKUI.Combo.get("idMinute1").addOption(minuteText);
	ZKUI.Combo.get("idDayofMonth").addOption(dayText);


	//按月选择时选择具体日期时，日期下拉框为可选
	ZKUI.Combo.get('idDayofMonth').combo.disable(true);
	$("input[name=\"checkOfMonth\"]").click(function(){
		if($('#idDayofMonthCheck').is(':checked')) {
			ZKUI.Combo.get('idDayofMonth').combo.disable(false);
		} else {
			ZKUI.Combo.get('idDayofMonth').combo.disable(true);
			ZKUI.Combo.get('idDayofMonth').combo.setComboValue("-1");
		}
	});

	// 时间回选
    ZKUI.Combo.get("idSendFormat").combo.setComboValue("${(item.sendFormat)!'0'}");
    ZKUI.Combo.get("idFileType").combo.setComboValue("${(item.fileType)!}");
    ZKUI.Combo.get("idReportType").combo.setComboValue("${(item.reportType)!}");
    ZKUI.Combo.get("idTimeSendFrequency").combo.setComboValue("${(item.timeSendFrequency)!}");
    ZKUI.Combo.get("idEmailType").combo.setComboValue("${(item.emailType)!}");
	$("#idTimeSendInterval").val("${(item.timeSendInterval)!}");
	var autoExportId = "${(item.id)!}";
    var sendFormatValue =  ZKUI.Combo.get("idSendFormat").combo.getSelected();
	if (autoExportId) {
		var timeSendFrequency = ZKUI.Combo.get("idTimeSendFrequency").combo.getSelected();
		var timeSendInterval = $("#idTimeSendInterval").val();
		// 发送频率回选
	 	if (timeSendFrequency == "0") {
			var timStr = timeSendInterval.split(";");
			for (i = 0; i < timStr.length-1; i++) {
				var time = timStr[i].split(":");
				switch (i) {
	                case 0:
	                	hourChange(time[0], 1);
						minute1Change(time[1]);
		                break;
	                case 1:
	                	hourChange(time[0], 2);
		                break;
	                case 2:
	                	hourChange(time[0], 3);
		                break;
	                case 3:
	                	hourChange(time[0], 4);
		                break;
	                case 4:
	                	hourChange(time[0], 5);
		                break;
	            }
				ZKUI.Combo.get('idHour'+(i+1)).combo.setComboValue(time[0]);
				ZKUI.Combo.get('idMinute'+(i+1)).combo.setComboValue(time[1]);
			} 
		} else {
			if (timeSendInterval == "lastDayOfMonth") {
				$("input[name=\"checkOfMonth\"][value=0]").prop("checked", true);
				ZKUI.Combo.get("idDayofMonth").combo.disable(true);
			} else if (timeSendInterval == "firstDayOfMonth") {
				$("input[name=\"checkOfMonth\"][value=1]").prop("checked", true);
				ZKUI.Combo.get("idDayofMonth").combo.disable(true);
			} else {
				$("input[name=\"checkOfMonth\"][value=2]").prop("checked", true);
				ZKUI.Combo.get("idDayofMonth").combo.disable(false);
				ZKUI.Combo.get("idDayofMonth").combo.setComboValue(timeSendInterval);
			}
		}

		var emailType = ZKUI.Combo.get("idEmailType").combo.getSelected();
	 	var deptId = "${(item.deptId)!}";
	 	var areaId = "${(item.areaId)!}"
		// 邮件类型部门区域回选
		if ("0" ==sendFormatValue) {
			if ("1" == emailType) {
				$("#idEmailInput").val("");
				$("#idDeptTree").val(deptId);
			} else if ("2" == emailType) {
				$("#idEmailInput").val("");

				$("#idAttAreaTree").val(areaId);
			}
		}

	}

	// 提交表单前校验方法
	function before(submitFunc) {
		var timeSendFrequency = ZKUI.Combo.get("idTimeSendFrequency").combo.getSelected();
		// 根据发送频率不同给隐藏域 timeSendInterval赋值
		if (timeSendFrequency == "0") {
			var hour1 = ZKUI.Combo.get("idHour1").combo.getSelected();
			var minute1 = ZKUI.Combo.get("idMinute1").combo.getSelected();
			var hour2 = ZKUI.Combo.get("idHour2").combo.getSelected();
			var minute2 = ZKUI.Combo.get("idMinute2").combo.getSelected();
			var hour3 = ZKUI.Combo.get("idHour3").combo.getSelected();
			var minute3 = ZKUI.Combo.get("idMinute3").combo.getSelected();
			var hour4 = ZKUI.Combo.get("idHour4").combo.getSelected();
			var minute4 = ZKUI.Combo.get("idMinute4").combo.getSelected();
			var hour5 = ZKUI.Combo.get("idHour5").combo.getSelected();
			var minute5 = ZKUI.Combo.get("idMinute5").combo.getSelected();
			var hour6 = ZKUI.Combo.get("idHour6").combo.getSelected();
			var minute6 = ZKUI.Combo.get("idMinute6").combo.getSelected();

			if (hour1 == "-1" || minute1 == "-1" || (hour2 != "-1" && minute2 == "-1") 
					|| (hour2 == "-1" && minute2 != "-1") || (hour3 != "-1" && minute3 == "-1")
					|| (hour3 == "-1" && minute3 != "-1") || (hour4 != "-1" && minute4 == "-1") 
					|| (hour4 == "-1" && minute4 != "-1") || (hour5 != "-1" && minute5 == "-1") 
					|| (hour5 == "-1" && minute5 != "-1") || (hour6 != "-1" && minute6 == "-1") 
					|| (hour6 == "-1" && minute6 != "-1")) {
				// 请选择正确的发送时间点
				messageBox({messageType: "alert", text: "<@i18n 'att_autoExport_timePointAlert'/>"});
			    return false;
		    }
			var time1 = hour1 + ":" + minute1;
			var time2 = hour2 + ":" + minute2;
			var time3 = hour3 + ":" + minute3;
			var time4 = hour4 + ":" + minute4;
			var time5 = hour5 + ":" + minute5;
			var time6 = hour6 + ":" + minute6;

			$("#idTimeSendInterval").val(time1 + ";" + time2 + ";" + time3 + ";" + time4 +";" + time5 + ";" + time6+ ";");

		} else if (timeSendFrequency == "1") {
			var monthCheck = $("input:radio[name=\"checkOfMonth\"]:checked").val();
			if (monthCheck == "" || monthCheck == null || monthCheck == undefined || monthCheck == "-1") {
				// 请选择具体日期
				messageBox({messageType: "alert", text: "<@i18n 'att_autoExport_dayofMonthCheckAlert'/>"});
			    return false;
		    }
			if (monthCheck == "0") {
				// 每月最后一天
				$("#idTimeSendInterval").val("lastDayOfMonth");
			} else if (monthCheck == "1") {
				// 每月第一天
				$("#idTimeSendInterval").val("firstDayOfMonth");
			} else if (monthCheck == "2") {
				// 具体日期
				var dayOfMonth = ZKUI.Combo.get("idDayofMonth").combo.getSelectedValue();
				if (dayOfMonth == "-1") {
					// 请选择具体日期
					messageBox({messageType: "alert", text: "<@i18n 'att_autoExport_dayofMonthCheckAlert'/>"});
				    return false;
				}
				$("#idTimeSendInterval").val(dayOfMonth);
			}
		}
		// 发件人表单校验

		var emailType = ZKUI.Combo.get("idEmailType").combo.getSelected();
        var sendFormat =  ZKUI.Combo.get("idSendFormat").combo.getSelected();
		if(sendFormat=="0"){
			if (emailType == "0") {
				var emailInput = $("#idEmailInput").val();
				if (emailInput == "" || emailInput == null) {
					// 请输入邮箱地址
					messageBox({messageType: "alert", text: "<@i18n 'att_autoExport_emailSetAlert'/>"});
					return false;
				}
			} else if (emailType == "1") {
				//var deptId = $("input[name=\"deptId\"]").val();
				var deptId = ZKUI.ComboTree.PULL["idDeptTree"].getValue();
				if (deptId == "" || deptId == null) {
					// 请选择部门
					messageBox({messageType: "alert", text: "<@i18n 'att_autoExport_chooseDeptAlert'/>"});
					return false;
				}
			} else if (emailType == "2") {
				//var areaId = $("input[name=\"areaId\"]").val();idAttAreaTreeidDeptTree
				var areaId = ZKUI.ComboTree.PULL["idAttAreaTree"].getValue();
				if (areaId == "" || areaId == null) {
					// 请选择区域
					messageBox({messageType: "alert", text: "<@i18n 'att_areaPerson_choiceArea'/>"});
					return false;
				}
			}
		} else {
			var ftpPassword = $("#ftpPassword").val();
			if(ftpPassword) {
				$("#ftpPassword").val(window.btoa(ftpPassword));
			}
		}
		submitFunc();
	}
	</script>
</#macro>