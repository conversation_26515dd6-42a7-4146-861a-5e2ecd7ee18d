<#assign gridName="attViewDevCmdGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@i18n 'att_devCmd_submitTime'/>&nbsp;<@i18n 'common_from'/>&nbsp;&nbsp;
                    <@ZKUI.Input type="datetime" endId="submitEndTime${uuid}" title="att_devCmd_submitTime" max="today" name="submitStartTime" hideLabel="true" readonly="false"/>
                    &nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
                    <@ZKUI.Input type="datetime" id="submitEndTime${uuid}" title="common_to" name="submitEndTime" max="today" hideLabel="true" readonly="false"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Combo name="returnValue" title="att_devCmd_returnedResult">
                        <option value="2"><@i18n 'base_admsDevCmd_successed'/></option>
                        <option value="1"><@i18n 'base_admsDevCmd_failed'/></option>
                        <option value="0"><@i18n 'base_admsDevCmd_noReturned'/></option>
                    </@ZKUI.Combo>
                </td>
            </tr>
        </@ZKUI.SearchTop>
        <@ZKUI.SearchBelow>
            <tr>
                <td valign="middle">
                    <@i18n 'att_devCmd_returnTime'/>&nbsp;<@i18n 'common_from'/>&nbsp;&nbsp;
                    <@ZKUI.Input type="datetime" endId="returnEndTime${uuid}" title="base_admsDevCmd_returnTime" name="returnStartTime" max="today" hideLabel="true" readonly="false"/>
                    &nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
                    <@ZKUI.Input type="datetime" id="returnEndTime${uuid}" name="returnEndTime" title="common_to" max="today" hideLabel="true" readonly="false"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="content" maxlength="30" title="att_devCmd_content" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
        <@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" permission="att:adms:devCmd:refresh" action="commonRefresh"></@ZKUI.ToolItem>
        <@ZKUI.ToolItem id="attAdmsDevCmd.do?clean&ids=(id)" text="att_devCmd_clearCmd" img="comm_del.png" permission="att:adms:devCmd:clearCmd" action="commonOperate" ></@ZKUI.ToolItem>
        <@ZKUI.ToolItem id="attAdmsDevCmd.do?export" type="export" text="common_op_export" permission="att:adms:devCmd:export" img="comm_export.png"></@ZKUI.ToolItem>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttAdmsDevCmdItem" query="attAdmsDevCmd.do?list&sn=${sn!}"/>
</@ZKUI.GridBox>