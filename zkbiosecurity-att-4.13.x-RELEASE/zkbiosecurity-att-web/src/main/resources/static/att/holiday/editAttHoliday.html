<#assign editPage = "true">
<#assign formId="attHolidayEditForm${uuid!}">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
<form action='attHoliday.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
	<input type='hidden' name='id' value='${(item.id)!}'/>
	<table class='tableStyle'>
		<tr>
			<!-- 节假日名称 -->
			<th><label><@i18n 'common_name'/></label><span class='required'>*</span></th>
			<td><input name='holidayName' maxlength="30" type='text' value='${(item.holidayName)!}' /></td>
		</tr>
		<tr>
			<!-- 开始日期时间 -->
			<th><label><@i18n 'common_op_startTime'/></label><span class='required'>*</span></th>
			<td><@ZKUI.Input type="date" Style="width: 148;" id="att_startDatetime" todayRange="start" today="true" readonly="true" value="${((item.startDatetime)?string('yyyy-MM-dd'))!}" name="startDatetime" hideLabel="true"/></td>
		</tr>
		<tr>
			<!-- 持续天数 -->
			<th><label><@i18n 'att_holiday_dayNumber'/></label><span class='required'>*</span></th>
			<td><input type='text' id="att_dayNumber" maxlength="2" value="${item.dayNumber}" name="dayNumber" /></td>
		</tr>
		<tr>
			<!-- 备注 -->
			<th><label><@i18n 'common_remark'/></label></th>
			<td><textarea rows="3" Style="width: 148px;" maxlength="255" name='remark' type='text' style="width: 148px;" >${(item.remark)!}</textarea></td>
		</tr>
	</table>
</form>
<script type='text/javascript'>

jQuery.validator.addMethod("spaceValid", function(value, element) {
	var pattenChar1 = /(^\S)/;
	var pattenChar2 = /(\S$)/;
	return this.optional(element) || (pattenChar1.test(value) && pattenChar2.test(value));
}, function() {
	return "<@i18n 'att_common_nameNoSpace'/>";
});

$('#${formId}').validate({
	debug: true,
	rules: {
		'holidayName': {
			required: true,
			spaceValid: true,
            unInputChar: true,
        	overRemote: ["attHoliday.do?validName", "${(item.holidayName)!}"]
		},
		'startDatetime': {
			required: true
		},
        'dayNumber': {
            required: true,
            digits: true,
            range : [1,99]
        },
		'remark': {
			unInputChar: true
		},

	},
	submitHandler: function() {
		<@submitHandler/>
	}
});
</script>
</#macro>