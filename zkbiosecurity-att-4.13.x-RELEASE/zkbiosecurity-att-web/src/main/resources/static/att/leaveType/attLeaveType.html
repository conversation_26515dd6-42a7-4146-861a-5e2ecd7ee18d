<#assign gridName="attLeaveTypeGrid${uuid!}">
<style>
.attLeaveTypeColor {
	border: none;
	height: 12px;
	width: 12px;
}

.attLeaveTypeColor::-webkit-color-swatch-wrapper {
	padding: 0;
}
.attLeaveTypeColor::-webkit-color-swatch {
	border: 0px;
}
.attLeaveTypeColor {
	border: 1px solid #B9C5D0;
}

</style>
<script type='text/javascript'>

function attLeaveTypeColor(val) {
	return "<input disabled class='attLeaveTypeColor' type='color' value='"+val+"'/>";
}

</script>
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
				<td valign="middle">
					<@ZKUI.Input name="leaveTypeName" maxlength="30" title="common_name" type="text"/>
				</td>
 			</tr>
 		</@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar gridName="${gridName}">
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:leaveType:refresh"></@ZKUI.ToolItem>
    	<@ZKUI.ToolItem id="attLeaveType.do?edit" text="common_op_new" width="420" height="450" img="comm_add.png" action="commonAdd" permission="att:leaveType:add"></@ZKUI.ToolItem>
    	<@ZKUI.ToolItem id="attLeaveType.do?del&names=(leaveTypeName)" text="common_op_del" img="comm_del.png" action="commonDel" permission="att:leaveType:del"></@ZKUI.ToolItem>
        <@ZKUI.ToolItem id="attLeaveType.do?export" isShow="false" type="export" permission="att:leaveType:export"/>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid gridName="${gridName}" vo="com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem" query="attLeaveType.do?list"/>
</@ZKUI.GridBox>