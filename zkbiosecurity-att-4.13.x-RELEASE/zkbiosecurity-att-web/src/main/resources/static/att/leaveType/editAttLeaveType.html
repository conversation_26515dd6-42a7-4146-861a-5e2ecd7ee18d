<#assign editPage = "true">
<#assign formId="attLeaveTypeEditForm${uuid!}">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
<style>
.attEditLeaveTypeColor {
	border: none;
	height: 22px;
	width: 150px;
	border-radius: 3px;
}

.attEditLeaveTypeColor::-webkit-color-swatch-wrapper {
	padding: 0;
}
.attEditLeaveTypeColor::-webkit-color-swatch {
	border: 0px;
}
.attEditLeaveTypeColor {
	border: 1px solid #B9C5D0;
}

.att_readonly {
	background-color: #eee!important;
}
</style>
<form action='attLeaveType.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
	<input type='hidden' name='id' value='${(item.id)!}'/>
	<#if (item.id)?exists>
		<input type='hidden' name='initFlag' value='${(item.initFlag)!?string("true", "false")}'/>
	<#else>
		<input type='hidden' name='initFlag' value='${(item.initFlag)!"false"}'/>
	</#if>
	<table class='tableStyle'>
		<tr>
			<!-- 假种名称 -->
			<th><label><@i18n 'common_name'/></label><span class='required'>*</span></th>
			<td><input id="leaveTypeName" <#if item.leaveTypeNo == 'out' || item.leaveTypeNo == 'trip'>readonly class='att_readonly'</#if> name='leaveTypeName' maxlength="30" type='text' value='${(item.leaveTypeName)!}' /></td>
		</tr>
		<tr>
			<!-- 是否扣上班时长（false：否/0，true：是/1） -->
			<th><label><@i18n 'att_leaveType_isDeductWorkLong'/></label><span class='required'>*</span></th>
			<td>
				<@ZKUI.Combo width="148" empty="false" value="${(item.isDeductWorkLong?string('true','false'))!'true'}" hideLabel="true" name="isDeductWorkLong" title="att_leaveType_isDeductWorkLong">
					<option value="true"><@i18n 'common_yes'/></option>
					<option value="false"><@i18n 'common_no'/></option>
				</@ZKUI.Combo>
			</td>
		</tr>
		<tr>
			<!-- 最小单位 -->
			<th><label><@i18n 'att_param_smallestUnit'/></label><span class='required'>*</span></th>
			<td>
				<input style="width: 57px" name='convertCount' maxlength="10" type='text' value='${(item.convertCount?c)!}' />
				<@ZKUI.Combo width="80" empty="false" value="${(item.convertUnit)!}" hideLabel="true" name="convertUnit" title="att_param_smallestUnit">
					<option value="minute"><@i18n 'common_minute'/></option>
					<option value="hour"><@i18n 'common_hours'/></option>
					<option value="day"><@i18n 'att_param_workDay'/></option>
				</@ZKUI.Combo>
			</td>
		</tr>
		<tr>
			<!-- 舍入控制 -->
			<th><label><@i18n 'att_param_roundingControl'/></label><span class='required'>*</span></th>
			<td>
				<@ZKUI.Combo width="148" empty="false" value="${(item.convertType)!}" hideLabel="true" name="convertType" title="att_param_roundingControl">
					<option value="abort"><@i18n 'att_param_abort'/></option>
					<option value="rounding"><@i18n 'att_param_rounding'/></option>
					<option value="carry"><@i18n 'att_param_carry'/></option>
				</@ZKUI.Combo>
			</td>
		</tr>
		<tr>
			<!-- 报表展示符号 -->
			<th><label><@i18n 'att_param_reportSymbol'/></label><span class='required'>*</span></th>
			<td>
				<input name='symbol' maxlength="10" type='text' value='${(item.symbol)!}' />
			</td>
		</tr>
		<tr>
			<th><label><@i18n 'auth_dept_sort'/></label><span class="required">*</span></th>
			<td><input name="sortNo" type="text" value="${(item.sortNo)!99}"/></td>
		</tr>
		<tr>
			<th><label><@i18n 'att_param_reportColor'/></label><span class="required">*</span></th>
			<td><input class="attEditLeaveTypeColor" name="color" type="color" value="${(item.color)!}"/></td>
		</tr>
		<tr>
			<th><label><@i18n 'att_leaveType_enableMaxDays'/></label><span class='required'>*</span></th>
			<td>
				<@ZKUI.Combo id="attLeaveTypeEnableMaxDays" width="148" empty="false" value="${(item.enableMaxDays?string('true','false'))!'false'}" hideLabel="true" name="enableMaxDays" title="att_leaveType_enableMaxDays">
					<option value="true"><@i18n 'common_yes'/></option>
					<option value="false"><@i18n 'common_no'/></option>
				</@ZKUI.Combo>
			</td>
		</tr>
		<tr>
			<th><label><@i18n 'att_leaveType_maxDays'/></label><span class="required">*</span></th>
			<td><input class="attLeaveTypeMaxDaysClass" name="maxDays" type="text" value="${(item.maxDays)!20}"/></td>
		</tr>
	</table>
</form>
<script type='text/javascript'>
	$().ready(function() {
		var systemSkin = (sysCfg.skin=='default' || !sysCfg.skin) ? "" : sysCfg.skin;
		if (systemSkin == "techblue") {
			$(".attEditLeaveTypeColor").css("border", "1px solid #00375B");
		}

		var attLeaveTypeEnableMaxDays = ZKUI.Combo.get("attLeaveTypeEnableMaxDays");
		if (attLeaveTypeEnableMaxDays) {
			attLeaveTypeEnableMaxDays.combo.attachEvent("onChange", function(value, text) {
				initAttLeaveTypeEnableMaxDays(value);
			});
			initAttLeaveTypeEnableMaxDays("${(item.enableMaxDays)!}");
		}

		function initAttLeaveTypeEnableMaxDays(value) {
			if (value == "true") {
				$(".attLeaveTypeMaxDaysClass").attr("readonly",false);
			} else {
				$(".attLeaveTypeMaxDaysClass").attr("readonly",true);
			}
		}
	})

	var attLeaveTypeNameValidMsg;
	var attLeaveTypeNameOldValue = "${(item.leaveTypeName)!}";
	jQuery.validator.addMethod("attLeaveTypeNameValid", function(value, element){
		var valid = true;
		var attLeaveTypeNameNewValue = $("#leaveTypeName").val();
		if(attLeaveTypeNameOldValue == attLeaveTypeNameNewValue) {
			return true;
		}
		$.ajax({
			async: false,
			url: "attLeaveType.do?isLeaveTypeName",
			data: {"leaveTypeName" : attLeaveTypeNameNewValue},
			success: function (res){

				if ("ok" != res.ret) {
					valid = false;
					attLeaveTypeNameValidMsg = res.msg;
				}
         	}
         });
		return valid;
	},function(){
		return attLeaveTypeNameValidMsg;
	});

$('#${formId}').validate({
	debug: true,
	rules: {
		'leaveTypeName': {
			required: true,
            unInputChar: true,
            attLeaveTypeNameValid: true
		},
		'isDeductWorkLong': {
			required: true
		},
		'convertCount': {
			required: true,
			convertCountValid: true,
			min: 0.1,
		},
		'symbol': {
			required: true,
			unInputChar: true
		},
		'sortNo': {
			required: true,
			digits: true,
			range:[1,99]
		},
		'maxDays': {
			required: true,
			digits: true,
			range:[1,366]
		}
	},
	submitHandler: function() {
		<@submitHandler/>
	}
});
</script>
</#macro>