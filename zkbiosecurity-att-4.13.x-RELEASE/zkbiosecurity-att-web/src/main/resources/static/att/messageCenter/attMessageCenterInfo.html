<#include '/public/template/editTemplate.html'>
<#macro editContent>
<table class='tableStyle'>

	<#if (item.adjustPersonPin)?exists>
	<tr>
		<th><@i18n 'att_person_pin'/></th>
		<td>${(item.adjustPersonPin)!}</td>
	</tr>
	</#if>

	<#if (item.adjustPersonName)?exists>
	<tr>
		<th><@i18n 'att_person_name'/></th>
		<td>${(item.adjustPersonName)!}</td>
	</tr>
	</#if>

	<#if (item.personPin)?exists>
	<tr>
		<th><@i18n 'att_person_pin'/></th>
		<td>${(item.personPin)!}</td>
	</tr>
	</#if>

	<#if (item.personName)?exists>
	<tr>
		<th><@i18n 'pers_person_wholeName'/></th>
		<td>${(item.personName)!}</td>
	</tr>
	</#if>

	<!--假种类型-->
	<#if (item.leaveTypeName)?exists>
	<tr>
		<th><@i18n 'att_leftMenu_leaveType'/></th>
		<td>${(item.leaveTypeName)!}</td>
	</tr>
	</#if>

	<#if (item.startDatetime)?exists>
	<tr>
		<!-- 开始时间 -->
		<th><@i18n 'common_startTime'/></th>
		<td>${((item.startDatetime)?string('yyyy-MM-dd HH:mm:ss'))!}</td>
	</tr>
	</#if>

	<#if (item.endDatetime)?exists>
	<tr>
		<!-- 结束时间 -->
		<th><@i18n 'common_endTime'/></th>
		<td>${((item.endDatetime)?string('yyyy-MM-dd HH:mm:ss'))!}</td>
	</tr>
	</#if>

	<#if (item.signDatetime)?exists>
	<tr>
		<!-- 补签时间 -->
		<th><@i18n 'att_sign_signTime'/></th>
		<td>${((item.signDatetime)?string('yyyy-MM-dd HH:mm:ss'))!}</td>
	</tr>
	</#if>

	<#if (item.adjustDate)?exists>
	<tr>
		<!-- 调整日期 -->
		<th><@i18n 'att_adjust_adjustDate'/></th>
		<td>${((item.adjustDate)?string('yyyy-MM-dd'))!}</td>
	</tr>
	</#if>

	<#if (item.swapShiftName)?exists>
	<tr>
		<!-- 调整班次 -->
		<th><@i18n 'att_class_shiftName'/></th>
		<td>${(item.swapShiftName)!}</td>
	</tr>
	</#if>

	<#if (item.swapPersonPin)?exists>
	<tr>
		<!-- 对调人员编号 -->
		<th><@i18n 'att_class_movePersonPin'/></th>
		<td>${(item.swapPersonPin)!}</td>
	</tr>
	</#if>

	<#if (item.swapPersonName)?exists>
	<tr>
		<!-- 对调人员姓名 -->
		<th><@i18n 'att_class_movePersonName'/></th>
		<td>${(item.swapPersonName)!}</td>
	</tr>
	</#if>

	<#if (item.swapDate)?exists>
	<tr>
		<!-- 对调日期 -->
		<th><@i18n 'att_class_moveDate'/></th>
		<td>${((item.swapDate)?string('yyyy-MM-dd'))!}</td>
	</tr>
	</#if>

	<tr>
		<!-- 创建时间 -->
		<th><@i18n 'pers_person_createTime'/></th>
		<td>${((item.operateDatetime)?string('yyyy-MM-dd HH:mm:ss'))!}</td>
	</tr>

	<tr>
		<!-- 备注 -->
		<th><@i18n 'common_remark'/></th>
		<td>${(item.remark)!}</td>
	</tr>
</table>
</#macro>
<#macro buttonContent>
<button class='button-form button-close' onclick="DhxCommon.closeWindow()"><@i18n 'common_op_close'/></button>
</#macro>
