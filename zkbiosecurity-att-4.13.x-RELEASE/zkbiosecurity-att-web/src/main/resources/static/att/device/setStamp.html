<#include '/public/template/editTemplate.html'>
<#macro editContent>
<#assign editPage = "setStamp">
<script type='text/javascript'>
    $().ready(function() {
			$('#${formId}').validate( {
				submitHandler : function()
				{
					<@submitHandler/>
				}
			});
		});
	</script>

	<form action='attDevice.do?reUpload' method='post' id='${formId}' enctype='multipart/form-data'>
	    <!-- 隐藏域 -->
		<input type='hidden' name='ids' value='${ids}'/>
		<table class='tableStyle'>
			<tr>
				<!-- 是否上传考勤记录 -->
				<th><label><@i18n 'att_device_attLog'/></label></th>
				<td><@ZKUI.Input hideLabel="true" name="isUploadAttLog" type="checkbox" value="1" /></td>
			</tr>
			<!--进行若是登记机的情况下进行数据显示,若不是进行隐藏-->
			<#if (isRegDevice)?exists && isRegDevice>
			<tr>
                <!-- 是否上传人员信息 -->
                <th><label><@i18n 'att_device_operLog'/></label></th>
				<td><@ZKUI.Input hideLabel="true" name="isUploadOperLog" type="checkbox" value="1" /></td>
            </tr>
			</#if>
            <tr>
                <!-- 是否上传考勤照片 -->
                <th><label><@i18n 'att_device_attPhoto'/></label></th>
				<td><@ZKUI.Input hideLabel="true" name="isUploadAttPhoto" type="checkbox" value="1" /></td>
            </tr>
		</table>
	</form>
</#macro>