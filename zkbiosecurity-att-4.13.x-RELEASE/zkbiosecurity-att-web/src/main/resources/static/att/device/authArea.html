<#include '/public/template/editTemplate.html'>
<#macro editContent>
<#assign editPage = "authArea">
<form action='attDevice.do?authArea' method='post' id='${formId}' enctype='multipart/form-data'>
	<!-- 隐藏域 -->
	<input type='hidden' name='deviceIds' value='${deviceIds}'/>
	<table class='tableStyle'>
			<!-- 授权区域 -->
        <th><label><@i18n 'pers_person_attArea'/></label><span class='required'>*</span></th>
        <td><@ZKUI.ComboTree width="148" type="radio" url="authArea.do?tree"  hideLabel="true" name="authAreaId"  readonly="readonly"/></td>
		</td>
	</table>
</form>
<script type='text/javascript'>
    $(function(){
        $('#${formId}').validate( {
            rules :
                {
                    "authAreaId" : {
                    	required : true
                    }
                },
            submitHandler : function()
            {
                <@submitHandler callBackFun="attGlobalAreaReloadGrid()"/>
            }
        });
    })

</script>
</#macro>