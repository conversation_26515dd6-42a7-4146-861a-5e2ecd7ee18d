<#include '/public/template/editTemplate.html'>
<#macro editContent>
<script type="text/javascript">
function checkIsRegDevice(obj) {
	$("#${formId} input[name='isRegDevice']").val($(obj).is(":checked"));
}
(function() {
	$('#${formId}').validate( {
		rules :{
			"devName": {
				required: true,
                unInputChar: true,
				overRemote : ["attDevice.do?devNameVaild", "${(item.devName)!}"]
			},
			"devSn": {
				required: true,
				rangelength: [1, 30],
				overRemote : ["attDevice.do?devSnVaild", "${(item.devSn)!}"]
			},
			"areaId": {
				required: true
			},
			"timeZone":
			{
				required:true
			},
		},
		submitHandler : function()
		{
			$("input[name='devSn']").removeAttr("disabled");
			$("input[name='ipAddress']").removeAttr("disabled");
			<@submitHandler />
		}
	});
})();
</script>

<form action='attDevice.do?auth' method='post' id='${formId}' enctype='multipart/form-data'>
	<!-- 隐藏域 -->
	<input type='hidden' name='id' value='${(item.id)!}'/>
	<table class='tableStyle'>
		<tr>
			<!-- 设备名称 -->
			<th><label><@i18n 'common_dev_name'/></label><span class='required'>*</span></th>
			<td><input name='devName' maxlength="20" type='text' value='${(item.devName)!}'/></td>
		</tr>
		<tr>
			<!-- 设备序列号 -->
			<th><label><@i18n 'common_dev_sn'/></label><span class='required'>*</span></th>
			<td>
				<input name='devSn' type='text' maxlength="50" value='${(item.devSn)!}' readOnly="true" disabled="disabled"/>
			</td>
		</tr>
		<tr>
			<!-- Ip地址 -->
			<th><label><@i18n 'common_ipAddress'/></label></th>
			<td>
				<div style="display:inherit;">
				<@ZKUI.IP name="ipAddress" onValueChange="_ipOnValueChange" onInit="_ipOnInit" value='${(item.ipAddress)!}' readOnly="true" disabled="true"/>
				</div>
			</td>
		</tr>

		<tr>
			<!-- 考勤区域 -->
			<th><label><@i18n 'pers_person_attArea'/></label><span class='required'>*</span></th>
			<td><@ZKUI.ComboTree width="148" type="radio" url="authArea.do?tree"  value="${(item.areaId)!}"  hideLabel="true" name="areaId" readonly="readonly"/></td>
		</tr>

		<tr>
			<!-- 时区-->
			<th><label><@i18n 'att_device_timeZone'/></label><span class='required'>*</span></th>
			<td>
                <@ZKUI.Combo width="148" empty="true" name="timeZone" path="baseDictionaryValue.do?selectList&key=systemTimeZone" hideLabel="true" value="${(item.timeZone)!}" />
			</td>
		</tr>

		<tr>
			<!-- 是否登记机 -->
			<th><label><@i18n 'att_device_register'/></label></th>
			<td>
				<@ZKUI.Input hideLabel="true" type="checkbox" name="isRegDevice" value="${(item.isRegDevice)!}" trueValue="true" falseValue="false" eventCheck=true/>
			</td>
		</tr>
	</table>
	<div style="margin-top: 10px;margin-left: 10px;">
		<span class="warningImage"></span>
		<span class="warningColor"><@i18n 'att_device_isRegDev'/></span>
	</div>
</form>
</#macro>