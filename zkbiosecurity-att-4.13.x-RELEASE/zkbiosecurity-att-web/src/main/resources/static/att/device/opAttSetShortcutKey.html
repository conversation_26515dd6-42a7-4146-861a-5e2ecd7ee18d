<#include '/public/template/editTemplate.html'>
<#macro editContent>
<style>
	.opAttSetShortcutKeyTimeClass {
		margin-left: 13px;
		width: 110px!important;
	}
	.opAttSetShortcutKeyWeekTrClass th {
    	padding: 2px 10px;
	}
</style>
<form action='attDevice.do?setShortcutKey' method='post' id='${formId}' enctype='multipart/form-data'>
	<input type='hidden' name='deviceIds' value='${(ids)!}'/>
	<input type='hidden' name='names' value='${(names)!}'/>
	<table class='tableStyle'>
		<tr>
			<th><label><@i18n 'att_cardStatus_attState'/></label><span class='required'>*</span></th>
			<td>
				<@ZKUI.Combo autoFirst="true" width="148" hideLabel="true" empty="false" name="keyId" path="attRule.do?getCardStatus"></@ZKUI.Combo>
			</td>
		</tr>
		<tr class="opAttSetShortcutKeyTypeTr">
			<th><label><@i18n 'att_cardStatus_every_day'/></label><span class='required'>*</span></th>
			<td>
				<@ZKUI.Input id="attSetShortcutKeyEveryDayId" name="autoType" hideLabel="true" type="checkbox" trueValue="1" falseValue="0" eventCheck="true"/>
				<@ZKUI.Input id="attSetShortcutKeyEveryDayTimeId" class="opAttSetShortcutKeyTimeClass" ignoreFormat="true" name="dayAutoTime" type="datetime" onlyTime="true" hideSeconds="true" format="%H:%i" today="true" readonly="true" hideLabel="true" value="00:00" />
			</td>
		</tr>

		<tr class="opAttSetShortcutKeyWeekTrClass">
			<th><label><@i18n 'common_monday'/></label></th>
			<td>
				<@ZKUI.Input id="attSetShortcutKeyWeekMon" name="mon" hideLabel="true" type="checkbox" trueValue="1" falseValue="0" eventCheck="true"/>
				<@ZKUI.Input style="display: none" id="attSetShortcutKeyWeekMonTime"name="monAutoTime" class="opAttSetShortcutKeyTimeClass" width="100" ignoreFormat="true" type="datetime" onlyTime="true" hideSeconds="true" format="%H:%i" today="true" readonly="true" hideLabel="true" value="00:00"/>
			</td>
		</tr>
		<tr class="opAttSetShortcutKeyWeekTrClass">
			<th><label><@i18n 'common_tuesday'/></label></th>
			<td>
				<@ZKUI.Input id="attSetShortcutKeyWeekTue" name="tue" hideLabel="true" type="checkbox" trueValue="1" falseValue="0" eventCheck="true"/>
				<@ZKUI.Input style="display: none" id="attSetShortcutKeyWeekTueTime" name="tueAutoTime" class="opAttSetShortcutKeyTimeClass" width="100" ignoreFormat="true" type="datetime" onlyTime="true" hideSeconds="true" format="%H:%i" today="true" readonly="true" hideLabel="true" value="00:00"/>
			</td>
		</tr>
		<tr class="opAttSetShortcutKeyWeekTrClass">
			<th><label><@i18n 'common_wednesday'/></label></th>
			<td>
				<@ZKUI.Input id="attSetShortcutKeyWeekWed" name="wed" hideLabel="true" type="checkbox" trueValue="1" falseValue="0" eventCheck="true"/>
				<@ZKUI.Input style="display: none" id="attSetShortcutKeyWeekWedTime" name="wedAutoTime" class="opAttSetShortcutKeyTimeClass" width="100" ignoreFormat="true" type="datetime" onlyTime="true" hideSeconds="true" format="%H:%i" today="true" readonly="true" hideLabel="true" value="00:00"/>
			</td>
		</tr>
		<tr class="opAttSetShortcutKeyWeekTrClass">
			<th><label><@i18n 'common_thursday'/></label></th>
			<td>
				<@ZKUI.Input id="attSetShortcutKeyWeekThu" name="thu" hideLabel="true" type="checkbox" trueValue="1" falseValue="0" eventCheck="true"/>
				<@ZKUI.Input style="display: none" id="attSetShortcutKeyWeekThuTime" name="thuAutoTime" class="opAttSetShortcutKeyTimeClass" width="100" ignoreFormat="true" type="datetime" onlyTime="true" hideSeconds="true" format="%H:%i" today="true" readonly="true" hideLabel="true" value="00:00"/>
			</td>
		</tr>
		<tr class="opAttSetShortcutKeyWeekTrClass">
			<th><label><@i18n 'common_friday'/></label></th>
			<td>
				<@ZKUI.Input id="attSetShortcutKeyWeekFri" name="fri" hideLabel="true" type="checkbox" trueValue="1" falseValue="0" eventCheck="true"/>
				<@ZKUI.Input style="display: none" id="attSetShortcutKeyWeekFriTime" name="friAutoTime" class="opAttSetShortcutKeyTimeClass" width="100" ignoreFormat="true" type="datetime" onlyTime="true" hideSeconds="true" format="%H:%i" today="true" readonly="true" hideLabel="true" value="00:00"/>
			</td>
		</tr>
		<tr class="opAttSetShortcutKeyWeekTrClass">
			<th><label><@i18n 'common_saturday'/></label></th>
			<td>
				<@ZKUI.Input id="attSetShortcutKeyWeekSat" name="sat" hideLabel="true" type="checkbox" trueValue="1" falseValue="0" eventCheck="true"/>
				<@ZKUI.Input style="display: none" id="attSetShortcutKeyWeekSatTime" name="satAutoTime" class="opAttSetShortcutKeyTimeClass" width="100" ignoreFormat="true" type="datetime" onlyTime="true" hideSeconds="true" format="%H:%i" today="true" readonly="true" hideLabel="true" value="00:00"/>
			</td>
		</tr>
		<tr class="opAttSetShortcutKeyWeekTrClass">
			<th><label><@i18n 'common_sunday'/></label></th>
			<td>
				<@ZKUI.Input id="attSetShortcutKeyWeekSun" name="sun" hideLabel="true" type="checkbox" trueValue="1" falseValue="0" eventCheck="true"/>
				<@ZKUI.Input style="display: none" id="attSetShortcutKeyWeekSunTime" name="sunAutoTime" class="opAttSetShortcutKeyTimeClass" width="100" ignoreFormat="true" type="datetime" onlyTime="true" hideSeconds="true" format="%H:%i" today="true" readonly="true" hideLabel="true" value="00:00"/>
			</td>
		</tr>
	</table>
</form>
<script type='text/javascript'>
$().ready(function() {

	function attSetShortcutKeyEveryDayChange(isEveryDay) {
		if(isEveryDay) {
			$("#attSetShortcutKeyEveryDayTimeId").show();
			$(".opAttSetShortcutKeyWeekTrClass").hide();
		} else {
			$(".opAttSetShortcutKeyWeekTrClass").show();
			$("#attSetShortcutKeyEveryDayTimeId").hide();
		}
	}
	attSetShortcutKeyEveryDayChange(false);
	$("#attSetShortcutKeyEveryDayId").change(function()  {
		attSetShortcutKeyEveryDayChange(this.checked)
	});

	$("[id^='attSetShortcutKeyWeek']").change(function()  {
		var timeId = this.id + "Time"
		if (this.checked) {
			$("#" + timeId).show();
		} else {
			$("#" + timeId).hide();
		}
	});

	$('#${formId}').validate({
		debug: true,
		rules: {
			'leaveTypeName': {
				required: true,
				unInputChar: true,
				attLeaveTypeNameValid: true
			},
		},
		submitHandler: function() {
			<@submitHandler/>
		}
	});
});

</script>
</#macro>