<#include "/public/template/infoTemplate.html">
<#macro infoContent>
<script type="text/javascript">
	$("#closeButton").focus();
	
    attDeviceOptionGrid = new dhtmlXGridObject('attDeviceOptionGrid');
	attDeviceOptionGrid.setImagePath("/public/controls/dhtmlx/dhtmlxGrid/codebase/imgs/");
	attDeviceOptionGrid.setHeader("<@i18n 'att_deviceOption_name'/>, <@i18n 'att_deviceOption_value'/>");
	attDeviceOptionGrid.setColumnIds("ruleType, ruleName");
	attDeviceOptionGrid.setInitWidths("240, 240");
	attDeviceOptionGrid.setColAlign("${leftRTL!'left'}, ${leftRTL!'left'}");
	attDeviceOptionGrid.setColTypes("ro,ro");
	attDeviceOptionGrid.enableAutoHeight(false);
	attDeviceOptionGrid.enableAutoWidth(true);
	attDeviceOptionGrid.init();
	attDeviceOptionGrid.setSkin(sysCfg.dhxSkin);
	attDeviceOptionGrid.setDateFormat(sysCfg.dhxLongDateFmt);
	ZKUI.Grid.attachSizes(attDeviceOptionGrid);
    attDeviceOptionGrid.setSizes();

     //加载规则
    var devId = "${devId!}";
    loadRulesInZone();
    function loadRulesInZone(zoneId)
    {
		$.ajax({
		    type: "GET",
		    url: 'attDevice.do?queryDeviceOption&devId='+ devId,
		    dataType: "json",
		    async: true,
		    success: function(result)
		    {
		        var data = result.data;
				attDeviceOptionGrid.clearAll();
				var count = 0
				if(data != null && data.length > 0)
				{
					for(index in data)
					{
						var rowData = data[index];
						attDeviceOptionGrid.addRow(attDeviceOptionGrid.uid(), [rowData.optionName, rowData.optionValue], index);
					}
					//personGrid.sortRows(3, sort_custom,"des");//???
					count = attDeviceOptionGrid.getRowsNum();
				}
                else
                {
                    attDeviceOptionGrid.addRow(attDeviceOptionGrid.uid(), ["<span class='warningImage' style='margin-top:6px;'></span><span class='warningColor'><@i18n 'att_deviceOption_noOption'/></span>", ""], 0);
                }
		    },
		    error:function (XMLHttpRequest, textStatus, errorThrown)
		    {
	 			messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>" + "-628"});
	 		}
		});
	}

</script>

<div id="attDeviceOptionGrid" style="height: 490px;"></div>
</#macro>