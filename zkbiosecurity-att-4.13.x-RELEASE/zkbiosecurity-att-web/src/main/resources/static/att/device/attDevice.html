<#assign gridName="attDeviceGrid${uuid!}">
<script type="text/javascript">

    /*参考areaIncludeLower.js配置是否显示下级部门树，以及部门树相关操作*/
    /*当前gridName*/
    attGlobalAreaGridName ="${gridName}";
    /*当前区域树ID*/
    attGlobalAreaTreeId = "areaTree";

    /*查看设备参数*/
    function queryDeviceOption(id, bar) {
        if (bar) {
            var gridName = bar.gridName || "gridbox";
            var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
            if (ids == "")
            {
                messageBox({
                    messageType : "alert",
                    text : "<@i18n 'common_prompt_selectObj'/>"
                });
            }
            else if(ids.split(",").length > 1)
            {
                messageBox({
                    messageType : "alert",
                    text : "<@i18n 'common_prompt_onlySelectOneObject'/>"
                });
            }
            else
            {
                var path = "skip.do?page=att_device_opAttDeviceOption&devId=" + ids;
                var opts = {
                    path: path,
                    width: 500,
                    height: 600,
                    title: "<@i18n 'att_deviceOption_query'/>",
                    gridName: "gridbox"
                };
                DhxCommon.createWindow(opts);
            }
        }
    }

    /*导出考勤设备*/
    function attDeviceExport(id, bar, opts) {
        var tree = ZKUI.Tree.get("areaTree").tree;
        var treeAreaId = tree.getSelected();
        var gridName = bar.gridName;
        var deviceAreaStatus = ZKUI.Combo.get("deviceAreaStatus").getValue();
        // 导出人员信息
        opts.path = "skip.do?page=public_template_opExportRecord&gridName=" + gridName + "&actionName=" + encodeURIComponent(id) + "&treeAreaId=" + treeAreaId + "&isIncludeLower=" + attGlobalAreaIsIncludeLower + "&deviceAreaStatus=" + deviceAreaStatus;
        if (opts.maxExportCount) {
            opts.path = opts.path + "&maxExportCount=" + opts.maxExportCount;
        }
        // opts.width = opts.width || 450;
        opts.height = opts.height || 280;
        DhxCommon.createWindow(opts);
    }

    /*升级固件*/
    function upgradeFirmware(id, bar, opt) {
        var maxSelectItem = 10;
        if(bar) {
            var gridName = bar.gridName;
            var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
            if(ids == "") {
                messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});
            }
            else if (ids.split(",").length > maxSelectItem) {
                messageBox({messageType:"alert",text: "<@i18n 'common_prompt_noPass'/>".format(maxSelectItem)});
            }
            else {
                var opts = $.extend({path:fillParamsFromGrid(gridName,ids,id) + "&ids=" + ids, gridName:gridName}, JSON.parse(JSON.stringify(opt)));
                DhxCommon.createWindow(opts);
            }
        }
    }

    /*查看命令*/
    function attDeviceViewCommand(gridName, obj, rid, rowIndex, column, row) {
        attViewCommand(row.devSn);
    }
    function attViewCommand(sn) {
            var opts = {
                path: "skip.do?page=att_admsDevCmd_attViewDevCmd&sn="+sn,
                width: 1200,
                height: 600,
                title: "<@i18n 'common_devMonitor_viewTheCommand'/>",
                gridName: "attViewDevCmdGrid"
            };
            DhxCommon.createWindow(opts);
    }

    /*验证设备状态，并重新获取数据*/
    function validateAttDeviceExitIsReg(id,bar,opt){
        //针对登记机的，智能单个数据进行操作，不能多个进行操作，针对上传设备，获取指定人员的设备。
        if(bar){
            var gridName = bar.gridName || "gridbox";
            var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
            if (ids == "") {
                messageBox({
                    messageType : "alert",
                    text : "<@i18n 'common_prompt_selectObj'/>"
                });
            }
            else if(ids.split(",").length > 1){
                messageBox({
                    messageType : "alert",
                    text : "<@i18n 'common_prompt_onlySelectOneObject'/>"
                });
            }
            else {
                $.ajax({
                    type: "post",
                    url: "attDevice.do?checkExitIsRegDevice",
                    data: {
                        ids: ids
                    },
                    dataType: "json",
                    async: false,
                    success: function (result) {
                        if (result.ret == "fail") {
                            openMessage(msgType.warning, result[sysCfg.msg], 3000);
                        } else if (id.indexOf("setStamp")>0) {
                            commonOpenOperate(id, bar, opt);
                        } else if (result.ret == "noIsReg") {
                             messageBox({
                                messageType : "alert",
                                text : "<@i18n 'att_device_existNotRegDevice'/>"
                            });
                        } else if(id.indexOf("fillPin")>0 ){
                            commonOpenOperate(id, bar, opt);
                        }
                    }
                });
            }
        }
    }

    /*授权区域*/
    function authArea(id,bar,opt){
        if(bar){
            var gridName = bar.gridName || "gridbox";
            var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
            if (ids == "") {
                messageBox({
                    messageType : "alert",
                    text : "<@i18n 'common_prompt_selectObj'/>"
                });
            }else {
                //跳出授权区域界面
                var path = "skip.do?page=att_device_authArea&deviceIds="+ ids;
                var opts = {
                    path:path,
                    width:400,
                    height:200,
                    title:"<@i18n 'auth_user_authorizedAreas'/>",
                    gridName:"gridbox"
                };
                DhxCommon.createWindow(opts);
            }
        }
    }

    /*验证设备状态，并根据id解析执行对应操作*/
    function validAttDeviceStatus(id, bar, opt)
    {
        if (bar) {
            var gridName = bar.gridName || "gridbox";
            var grid = ZKUI.Grid.GRID_PULL[gridName].grid;
            var ids = grid.getCheckedRows(0);
            if (ids == "")
            {
                messageBox({
                    messageType : "alert",
                    text : "<@i18n 'common_prompt_selectObj'/>"
                });
            }
            else
            {
                var idsArray = ids.split(",");
                var names = "";
                for (var i = 0; i<idsArray.length; i++) {
                    if (i != 0) {
                        names += ",";
                    }
                    names += grid.cells(idsArray[i], grid.getColIndexById("devName")).getValue();
                }

               //判断设备启用和在线状态
                $.ajax({
                    type: "post",
                    url: "attDevice.do?checkDeviceStatus",
                    data:{
                        ids:ids
                    },
                    dataType: "json",
                    async: false,
                    success: function (result) {
                        if(result.ret != "ok")
                        {
                            openMessage(msgType.warning, result[sysCfg.msg], 3000);
                        }
                        else if(id.indexOf("queryDeviceOption")>0)
                        {
                            queryDeviceOption(id, bar);
                        }
                        else if(id.indexOf("setSms")>0 || id.indexOf("setTime")>0 || id.indexOf("setStamp")>0 || id.indexOf("fillPin")>0)
                        {
                            commonOpenOperate(id,bar,opt);
                        }
                        else if(id.indexOf("syncDev")>0)
                        {
                             var opts = {
                                path: "skip.do?page=att_device_opAttSyncClearData&ids=" + ids + "&names=" + names,
                                width: 380,
                                height: 200,
                                title: "<@i18n 'common_prompt_title'/>",
                                gridName: "gridbox"
                            };
                            DhxCommon.createWindow(opts);
                        }
                        else
                        {
                            commonOperate(id,bar,opt);
                        }
                    }
                });
            }
        }
    }

    /*状态转化文字*/
    function convertToString(v) {
        if(v == "1") {
            return "<span class='zk-msg-normal'><@i18n 'common_online'/></span>";
        } else if(v == "0") {
            return "<span class='zk-msg-error'><@i18n 'common_offline'/></span>";
        } else { //v=2
            return "<span class='zk-msg-warn'><@i18n 'common_commStatus_disable'/></span>";
        }
    }
</script>
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <!-- 设备序列号 -->
                    <@ZKUI.Input name="devSn"  maxlength="30" title="common_dev_sn" type="text"/>
                </td>
                <td valign="middle">
                    <!-- 设备名称 -->
                    <@ZKUI.Input name="devName"  maxlength="30" title="common_dev_name" type="text"/>
                </td>
                <td valign="middle">
                    <!-- 设备区域状态 -->
                    <@ZKUI.Combo empty="true" name="deviceAreaStatus" id="deviceAreaStatus" title="att_device_areaStatus">
                        <option value="1"><@i18n 'att_device_areaEmpty'/></option>
                        <option value="2"><@i18n 'att_device_areaCommon'/></option>
                    </@ZKUI.Combo>
                </td>
            </tr>
        </@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
<@ZKUI.Layout style="position:absolute; top:70px;bottom:0px;left:0px;right:0px;height:auto;" onInit="initAttGlobalAreaTree">
    <@ZKUI.Cell width="240" treeId="areaTree">
        <@ZKUI.Tree dynamic="true"  id="areaTree" url="authArea.do?dynaTree&showPersonCount=true" onClick="attGlobalAreaTreeClick"></@ZKUI.Tree>
    </@ZKUI.Cell>
        <@ZKUI.Cell hideHeader="true">
        <@ZKUI.Toolbar>
            <@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:device:refresh"/>
            <@ZKUI.ToolItem type="delete" id="attDevice.do?del&names=(devName)" permission="att:device:del"></@ZKUI.ToolItem>
            <@ZKUI.ToolItem id="attDevice.do?getSearchDevInfo&type=searchDev" text="att_device_authDevice" title="att_device_authDevice" img="att_authDevice.png" width="800" height="510" action="commonOpen" permission="att:device:search"/>
            <!-- 设备控制 -->
            <@ZKUI.ToolItem type="more" text="att_devMenu_control" img="att_dev_control.png">
                <!-- 启用 -->
                <@ZKUI.ToolItem id="attDevice.do?enable&names=(devName)" text="common_enable" width="500" img="comm_enable.png" action="commonOperate" permission="att:device:enable"/>
                <!-- 禁用 -->
                <@ZKUI.ToolItem id="attDevice.do?disable&names=(devName)" text="common_commStatus_disable" width="500" img="comm_disable.png" action="commonOperate" permission="att:device:disable"/>
                <!-- 升级固件 -->
                <@ZKUI.ToolItem id="attDevice.do?getDevInfoWithUpgradeFirmware" title="common_dev_upgradeFirmware" text="common_dev_upgradeFirmware" width="600" height="440" img="comm_upgradeFirmware.png" action="upgradeFirmware" permission="att:device:upgradeFirmware"/>
                <!-- 重启设备 -->
                <@ZKUI.ToolItem id="attDevice.do?reboot&names=(devName)" text="common_dev_reboot" width="500" img="comm_rebootDevice.png" action="validAttDeviceStatus" permission="att:device:reboot"/>
                <!-- 设置对公消息 -->
                <@ZKUI.ToolItem id="attDevice.do?setSms" title="att_op_dataSms" text="att_op_dataSms" width="450" img="att_allSms.png" action="validAttDeviceStatus" permission="att:device:setsms"/>
                <!-- 同步软件数据到设备 -->
                <@ZKUI.ToolItem id="attDevice.do?syncDev&names=(devName)" text="att_op_syncDev" width="500" img="comm_syncAllData.png" action="validAttDeviceStatus" permission="att:device:syncdev"/>
                <!-- 授权区域 -->
                <@ZKUI.ToolItem id="attDevice.do?authArea" text="auth_user_authorizedAreas" width="500" img="att_authArea.png" action="authArea" permission="att:device:authArea"/>
                <!-- 考勤状态设置 -->
                <@ZKUI.ToolItem id="attDevice.do?opAttSetShortcutKey" action="commonOpenOperate" text="att_cardStatus_setting" title="att_cardStatus_setting" width="400" height="440" img="att_userSms.png" permission="att:device:setShortcutKey"/>
            </@ZKUI.ToolItem>

                <!-- 查看和获取信息 -->
            <@ZKUI.ToolItem type="more" text="att_devMenu_viewOrGetInfo" img="att_dev_viewOrGetInfo.png">
                <!-- 获取设备参数 -->
                <@ZKUI.ToolItem id="attDevice.do?getDevOpt" text="common_dev_getDevOpt" width="500" img="comm_getOptFromDev.png" action="validAttDeviceStatus"  permission="att:device:getdevopt" />
                <!-- 查看设备参数 -->
                <@ZKUI.ToolItem id="attDevice.do?queryDeviceOption" text="att_deviceOption_query" width="500" height="600" img="att_viewing_info.png" action="queryDeviceOption" permission="att:device:queryOption"/>
                <!-- 考勤数据校对 -->
                <@ZKUI.ToolItem id="attDevice.do?setTime" text="att_op_account" title="att_op_account" width="400" height="250" img="att_reUpload.png" action="validAttDeviceStatus"  permission="att:device:settime"/>
                <!-- 重新上传数据 -->
                <@ZKUI.ToolItem id="attDevice.do?setStamp" text="att_op_check" title="att_op_check" width="400" img="att_reUpload.png" action="validateAttDeviceExitIsReg" permission="att:device:setstamp"/>
                <!-- 获取指定人员数据 -->
                <@ZKUI.ToolItem id="attDevice.do?fillPin" text="att_device_getPin" title="att_device_getPin" width="400" img="att_reUpload.png" action="validateAttDeviceExitIsReg" permission="att:device:fillpin"/>
            </@ZKUI.ToolItem>

            <!-- 清除设备数据 -->
            <@ZKUI.ToolItem type="more" text="att_devMenu_clearData" img="comm_del.png">
                <!-- 清除设备命令 -->
                <@ZKUI.ToolItem id="attDevice.do?deleteCmd&names=(devName)" text="att_op_deleteCmd" width="500" img="comm_del.png" action="commonOperate" permission="att:device:deletecmd"/>
                <!-- 清除考勤照片 -->
                <@ZKUI.ToolItem id="attDevice.do?clearAttPic&names=(devName)" text="att_op_clearAttPic" width="500" img="comm_del.png" action="validAttDeviceStatus"  permission="att:device:clearattpic"/>
                <!-- 清除考勤记录 -->
                <@ZKUI.ToolItem id="attDevice.do?clearAttLog&names=(devName)" text="att_op_clearAttLog" width="500" img="comm_del.png" action="validAttDeviceStatus" permission="att:device:clearattlog"/>
                <!-- 清除设备人员 -->
                <@ZKUI.ToolItem id="attDevice.do?clearAttPers&names=(devName)" text="att_op_clearAttPers" width="500" img="comm_del.png" action="validAttDeviceStatus" permission="att:device:clearattpers"/>
            </@ZKUI.ToolItem>
            <@ZKUI.ToolItem id="attDevice.do?export" type="export"  action="attDeviceExport" text="common_op_export" img="comm_export.png" permission="att:device:export"></@ZKUI.ToolItem>
        </@ZKUI.Toolbar>
     <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttDeviceItem" query="attDevice.do?list"/>
    </@ZKUI.Cell>
    </@ZKUI.Layout>
</@ZKUI.GridBox>