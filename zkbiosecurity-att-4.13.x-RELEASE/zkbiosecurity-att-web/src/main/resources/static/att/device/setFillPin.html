<#include '/public/template/editTemplate.html'>
<#macro editContent>
<#assign editPage = "setFillPin">
<script type='text/javascript'>
    $().ready(function() {
        $("#id_pin").focus();

		$('#${formId}').validate( {
			rules :
			{
				 //人员编号
				"pin" : {
					required : true
				}
			},
			submitHandler : function()
			{
				<@submitHandler/>
			}
		});
	});
</script>

<form action='attDevice.do?getPersonInfo' method='post' id='${formId}' enctype='multipart/form-data'>
	<!-- 隐藏域 -->
	<input type='hidden' name='ids' value='${ids}'/>
	<table class='tableStyle'>
		<tr>
			<!-- 设备名称 -->
			<th><label><@i18n 'att_device_InputPin'/></label><span class='required'>*</span></th>
			<td><textarea id="id_pin" maxlength="50" rows="5" name="pin" type="text" value=""  onkeyup="this.value=this.value.replace(/\s+/g,'').replace(/[， ]/g,'')"/></td>
		</tr>
	</table>
	<div style="margin-top: 10px; margin-left: 15px; text-align: left;"
	class="trEditHidden">
	<span class="warningImage"></span> <span class="warningColor"><@i18n 'att_device_separatedPin'/></span>
	</div>
</form>
</#macro>