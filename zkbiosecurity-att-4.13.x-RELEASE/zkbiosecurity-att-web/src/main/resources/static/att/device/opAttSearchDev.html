<!--  <div style="border:0px solid #71A8D8; height: 500px; overflow: auto; width: 100%;">-->
<div id="searchDevGrid${uuid!}" style="padding-left:3px;padding-right:3px;padding-top:3px;">
    <table width="100%">
        <tr>
            <td width="100%">
                <table class='searchTable'>
                    <tr>
                        <td><@i18n 'common_ipAddress'/><input id="devIPAddress${uuid!}" type="text" style="width: 120px; margin-left:7px;" onchange="devInfoFilter(this)" onkeyup="devInfoFilter(this)" disabled="true" maxlength="15"/></td>
                        <!--<td><@i18n 'common_dev_devType'/><input id="devType${uuid!}" type="text" style="width: 120px; margin-left:7px;" onchange="devInfoFilter(this)" onkeyup="devInfoFilter(this)" disabled="true" maxlength="30"/></td>-->
                        <td><@i18n 'common_dev_sn'/><input id="devSN${uuid!}" type="text" style="width: 120px; margin-left:7px;" onchange="devInfoFilter(this)" onkeyup="devInfoFilter(this)" disabled="true" maxlength="30"/></td>
                        <td>
                            <div id="search" style="margin-left: 5px; display: inline-block;" class="search_button" onclick="devInfoFilter();" title="<@i18n 'common_search_query'/>">
                            </div>
                            <div id="clearSearch" style="margin-left: 5px; display: inline-block;" class="search_clear_button" onclick="attSearchDevClearCondition();devInfoFilter();" title="<@i18n 'common_search_clearTitle'/>">
                            </div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr height="5px;"><td></td></tr>
        <tr>
            <td>
                <div id="devGridBox${uuid}!" style="height:330px;"></div>
            </td>
        </tr>
    </table>
    <div style="margin-top: 10px; margin-left: 15px; text-align: left;">
	    <span class="warningImage"></span>
	    <span class="warningColor" id="warningColor${uuid!}"></span>
	</div>
</div>
<!--暂时屏蔽功能-->
<div style="display:none">
    <div id="div_autoAdd" style="margin-left: 10px;margin-top: 10px;">
        <input id="attDeviceAutoAdd" onclick="attCheckBoxClick(this,'att.device.autoAdd')" <#if autoAdd =="0">checked="checked"</#if> type="checkbox" />
        <input type="hidden" name="att.device.autoAdd" value="${(autoAdd)!}"/>
        <span><@i18n "att_device_autoAdd"/></span>
    </div>
    <div id="div_receivePersonOnlyDb" style="margin-left: 10px;margin-top: 10px;">
        <input id="receivePersonOnlyDb" onclick="attCheckBoxClick(this,'att.device.receivePersonOnlyDb')" <#if receivePersonOnlyDb =="0">checked="checked"</#if> type="checkbox" />
        <input type="hidden" name="att.device.receivePersonOnlyDb" value="${(receivePersonOnlyDb)!}"/>
        <span><@i18n "att_device_receivePersonOnlyDb"/></span>
    </div>
</div>
<!-- 关闭按钮 -->
<div class="bottomDiv bottomDivR" style="margin-top:15px;padding-top:15px">
    <button class='button-form' onclick="DhxCommon.closeWindow()" id="closeButton"><@i18n 'common_op_close'/></button>
</div>
<script type="text/javascript">
    function attCheckBoxClick(el, name) {
        if ($(el).prop("checked")) {
            $("input[name='" + name + "']").val(0);
        } else {
            $("input[name='" + name + "']").val(1);
        }
        $.ajax({
            type: "post",
            url: "attParam.do?save",
            data : {
                "att.device.autoAdd" :  $("input[name='att.device.autoAdd']").val(),
                "att.device.receivePersonOnlyDb" :  $("input[name='att.device.receivePersonOnlyDb']").val()
            },
            success:function (result) {
                openMessage(msgType.success);
            },
            error: function (XMLHttpRequest, textStatus, errorThrown)
            {
                messageBox(msgType.error);
            }
        });
    }

	$().ready(function(){
		$("#warningColor${uuid!}").html("<@i18n 'common_dev_checkServerPortTip'/>".format("${hostPort}"));
	});

	if(typeof(ajaxDevSearchObj) != "undefined" && ajaxDevSearchObj != null)  //将还没有返回的ajax调用全部终止掉
	{
		errorFlag = false;//用于判断是否是手动中断ajax请求，如果是手动中断，不提示错误信息
		ajaxDevSearchObj.abort();
	}
	var interval;  //定时器的变量

	 //进度条id
	var totalProgress_node_id = "totalProgressId";
	 //进度条进度
	var totalProgress = 0;
	 //进度条的速度
	var progressSpeed;
	function beginProgress()
	{
	    $("#loadingImg").show();
	    progressSpeed = 130;
	    totalProgress = 1;
	    interval = setInterval("doProgress()",progressSpeed);//1000为1秒钟
        DhxCommon.getCurrentWindow().attachEvent("onClose", function(){
            if(interval){
                clearInterval(interval);
            }
            return true;
        });
	    reloadSearchCondition();
	}

	/**
	 * 重新加载搜索条件
	 *
	 * <AUTHOR>
	 * @since 2015年3月3日 下午2:37:14
	 */
	var searchDivHtml = $("#searchDiv").html();
	function reloadSearchCondition()
	{
	    $("#devIPAddress${uuid!}").val("");
	    $("#devType${uuid!}").val("");
	    $("#devSN${uuid!}").val("");
	    $("#devIPAddress${uuid!}").attr("disabled", true);
		$("#devType${uuid!}").attr("disabled", true);
		$("#devSN${uuid!}").attr("disabled", true);
		$("#clearSearch").hide();
		$("#searchDiv").addClass("gray");
		$("#searchDiv").append($("#downloadSearchTool").html());
		$("#downloadSearchTool").remove();
	}
	//清除搜索条件
    function attSearchDevClearCondition() {
        $("#devIPAddress${uuid!}").val("");
        $("#devType${uuid!}").val("");
        $("#devSN${uuid!}").val("");
    }


	//设置进度
	function setProgress(node_id,progress)
	{
	    if (node_id)
	    {
	        //var obj = document.getElmentById(node_id);
	        $("#" + node_id + " > span").css("width", String(progress) + "%");
	        $("#percentNum").html(progress + "%");
	      	//计算进度条数值位置
	        $("#percentNum").css("left", $("#totalProgressId").position().left + $("#totalProgressId").width() / 2 - $("#percentNum").width() / 2);
	        //$("#" + node_id + "Text").html(String(progress) + "%");
	    }
	}


	//进行循环获取进度阶段
	function doProgress()
	{
        setProgress(totalProgress_node_id,totalProgress);
        totalProgress++;
        /* if(totalProgress == 55)
        {
        	clearInterval(interval);  
            progressSpeed = 250;
            interval = setInterval("doProgress()", progressSpeed);//1000为1秒钟    
        } */
        if(totalProgress == 100)
        {
        	clearInterval(interval);
        }
	}
</script>
<!-- leo -->
<script type="text/javascript">
	$("#OK").remove();//
	var devGrid;
	loadDevGrid();
	function loadDevGrid()// 生成列表
	{
		devGrid = new dhtmlXGridObject('devGridBox${uuid}!');
		devGrid.setImagePath(sysCfg.rootPath+"/public/controls/dhtmlx/dhtmlxGrid/codebase/imgs/");
        devGrid.setHeader("<@i18n 'common_ipAddress'/>, <@i18n 'common_dev_sn'/>, <@i18n 'common_relatedOp'/>");
        devGrid.setColumnMinWidth("100,100,100");
        devGrid.setInitWidths("200,200,*");
        devGrid.setColAlign("left,left,left");
        devGrid.setColTypes("ro,ro,ro");
        devGrid.enableAutoHeight(false);
        devGrid.enableAutoWidth(false);
        devGrid.setColSorting("str,str,str");
        devGrid.init();
        devGrid.enableHeaderMenu("true,true,false");
		devGrid.setSkin(sysCfg.dhxSkin);
		ZKUI.Grid.attachSizes(devGrid);
        devGrid.setSizes();
	}

	// var ipv4 = null;// 定义一个ip4，避免验证前出错
	var ipArray = new Array();//全局变量，存放所有ip地址
	function getAllIPSn()// 获取所有Ip和Sn
	{
		//获取数据库中所有的IP地址、sn,避免用户修改ip地址时写入数据库中已有的ip地址（包含考勤和门禁
		var stamp = new Date().getTime();
		$.ajax({
			type: "POST",
			url: "attDevice.do?getAllIPSn",
			dataType: "json",
			async: false,
			success: function(retData)
			{
			    printLog(retData);
				ipArray = retData["data"]["ipAddress"];
				snArray = retData["data"]["sn"];
			},
			error: function(XMLHttpRequest, textStatus, errorThrown)
			{
				messageBox({messageType:"alert", text: "<@i18n 'common_prompt_serverError'/>"+ "-612"});
			}
		});
	}

	var searching = false;//当前处于未搜索状态
	var network_segment = "";//本机ip网段
	var ipv4Ip = "";
	var ipv4Gateway = "";
	var ipv4SubnetMask = "";
	var addDevCount = 0;
	nowTime = new Date().getTime();
	$(function searchDev()// 开始搜索设备
	{
		getAllIPSn();
		devGrid = null;
		loadDevGrid();//？为什么要再初始化一次,清空的话使用下面的clearAll即可---陈立
		//devGrid.clearAll();
//		$("#idSearching").show();
		$("#idHasDev").hide();
		//$("#idSearchResult").hide();
		// showLoading(true, "devGridBox");
		searching = true;
		ajaxDevSearchObj = $.ajax({
	        url: "attDevice.do?searchDev&nowTime=" + nowTime,
	        type: "POST",
	        dataType:"json",
	        async: true,
	        success:function(retData)
	        {
	        	//先清楚掉所有Interval
	            clearInterval(interval);

				if(retData["data"]["nowTime"] == nowTime)
				{
					searching = false;
					//leo 搜索结束后，讲按钮文本改回为“开始搜索”
					//$("#idSearching").hide();
					if(retData["ret"] == "500")// 和设备通信异常
					{
						messageBox({messageType:"alert", text: "<@i18n 'common_dev_searchFails'/>" + retData['msg']});
					}
					else if(retData["ret"] == "400")// 内部程序异常
					{
						messageBox({messageType:"alert", text: "<@i18n 'common_prompt_serverError'/>"+ "-615"});
					}
					else
					{
					    //搜索的设备数不为0时
						if(retData["data"]["devCount"] != "0")
						{
				        	var data = retData["data"]["devData"];
				        	var oprateHtml = "";
				        	var moreOprateHtml = "";
				        	var devSNArray = getDevGridSN();
				        	for(var i = data.length-1; i >= 0; i--)
				        	{
                                //是否已添加
                                var isAdded = false;
				        		if(snArray.length > 0)
				        		{
				        			if($.inArray(data[i].sn, devSNArray) >= 0)
				        			{
				        				break;
				        			}
				        			for(var j = 0; j < snArray.length; j++)
					        		{
					        			if(data[i].sn == snArray[j])
					        			{
                                            isAdded = true;
					        				oprateHtml = "<@i18n 'common_dev_devHasAdd'/>";
					        				$("#addDevHint").show();
					        				addDevCount += 1;
					        				$("#addDevCount").text(addDevCount);
					        				break;
					        			}
					        			else if(j == snArray.length-1)// 最后一次循环
					        			{
					        				if(data[i].sn != snArray[j])
					        				{
					        					oprateHtml = "<a href='javascript:void(0)' id='addDev_"+i+"'><@i18n 'common_op_add'/></a>&nbsp;";
					        				}
					        			}
					        		}
				        		}
				        		else
				        		{
					        		oprateHtml = "<a href='javascript:void(0)' id='addDev_"+i+"'><@i18n 'common_op_add'/></a>&nbsp;";
					        	}
				        		if(typeof(data[i].exception) != "undefined")
                        		{
                        			oprateHtml = data[i].exception;
                        		}
                                //没有添加过才展示
                                if(!isAdded){
                                    devGrid.addRow(i, [data[i].ip, data[i].sn, oprateHtml], 0);
                                }
				        		if(typeof(data[i].exception) != "undefined")
                        		{
                        			devGrid.setRowColor(i,"red");
                        		}
				        		ipArray.push(data[i].ip);
				        	}
				        	devGrid.sortRows(0);//按ip排序
				        	devGrid.setSizes(false);

				        	//添加设备操作页面
				        	var isSupportPinLetter = "${persParams['pers.pinSupportLetter']}";
				        	var pinLen = "${persParams['pers.pinLen']}";
				        	var cardLen = "${persParams['pers.cardLen']}";
							$("a[id^='addDev_']").each(function(){
								$(this).click(function(){
									var rowId = $(this).attr("id").split("_")[1];
									var ipAddress = devGrid.cells(rowId, 0).cell.innerHTML;
									var devSn=devGrid.cells(rowId, 1).cell.innerHTML;
									createWindow('attDevice.do?edit&ipAddress='+ipAddress+'&devSn='+devSn+'^0^0^500^300^<@i18n "common_op_add"/>');
								});
							});
				        }
						$("#devIPAddress${uuid!}").attr("disabled", false);
						$("#devType${uuid!}").attr("disabled", false);
						$("#devSN${uuid!}").attr("disabled", false);
						$("#clearSearch").show();
						$("#searchDiv").removeClass("gray");
						$("#searchDiv").html(searchDivHtml)
					}
					// showLoading(false, "devGridBox");
					$("#searchDevCount").html(devGrid.getRowsNum());
					$("#opSearch").attr("disabled", false);
				}
	        },
	        error:function(XMLHttpRequest, textStatus, errorThrown)
			{
	        	$("#opSearch").attr("disabled", false);
	            //先清除Interval
	            clearInterval(interval);
	        	searching = false;
				//$("#idSearching").hide();
				// showLoading(false, "devGridBox");
				if(errorFlag)
				{
					messageBox({messageType:"alert", text: "<@i18n 'common_prompt_serverError'/>"+ "-615"});
				}
				errorFlag = true;
			}
	    });
	})

	function getMoreOperate(operateId)
	{
		var divInfo = $("#moreOperate_"+operateId);
		divInfo.show();
        divInfo.css("margin-left","50px");
		if(divInfo.offset().top > 400)
		{
			divInfo.css("margin-top","-69px");
		}
		if(divInfo.offset().top < 300)
		{
            divInfo.css("margin-top","0px");
		}
		divInfo.mouseover(function(){
			divInfo.show();
        }).mouseout(function(){
        	divInfo.hide();
        });
	}

	function closeMoreOperate(operateId)
	{
		$("#moreOperate_"+operateId).hide();
	}

	//进行条件过滤
	function devInfoFilter(obj)
	{
		devGrid.filterBy(0, function(value, id){
            if(($("#devIPAddress${uuid!}").val() == "" || devGrid.cells(id,0).getValue().indexOf($("#devIPAddress${uuid!}").val()) != -1) && ($("#devSN${uuid!}").val() == "" || devGrid.cells(id,1).getValue().indexOf($("#devSN${uuid!}").val()) != -1))
			{
				return true;
			}
			else
			{
				return false;
			}
		});

		//处理谷歌，IE浏览器器搜索设备，过滤后要点击2次才有效果的问题
		$(obj).blur();
		$(obj).focus();

		//$("#searchDevCount").html(devGrid.getRowsNum());
	}

	/**
	 * 获取设备列表中已存在的设备sn数组--防止重复显示设备信息
	 * add by wenxin
	 * @return devSNArray
	 */
	function getDevGridSN()
	{
		var devSNArray = new Array();
		var rowNum = devGrid.getRowsNum();
		for(var i = 0; i < rowNum; i ++)
		{
			devSNArray[devSNArray.length] = devGrid.cells(i, 1).cell.innerHTML;
		}
		return devSNArray;
	}

	function createWindow(ids) {
        DhxCommon.createWindow(ids);
    }
</script>