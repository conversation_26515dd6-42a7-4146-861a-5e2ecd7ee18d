<#assign editPage = "true">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
<form action="attDevice.do?syncDev" method="post" id="${formId}" onkeydown="if(event.keyCode==13){return false;}">
    <input type='hidden' name='ids' value='${ids}'/>
    <input type='hidden' name='names' value='${names}'/>
    <table cellpadding="8" class="tableStyle">
        <tr>
            <th style="width:100%;"><label id="syncDataClearContent${uuid}"></label></th>
        </tr>
        <tr>
            <th><@i18n 'common_dev_clearDevDataBeforeSync'/></th>
            <td><@ZKUI.Input hideLabel="true" type='checkbox' value="true" name="isSyncClearData"/></td>
        </tr>
    </table>
</form>
<script>

    $().ready(function() {
        $("#syncDataClearContent${uuid}").html(I18n.getValue("common_prompt_executeOperate").format(I18n.getValue("common_dev_syncAllDataToDev")));
    })

    $('#${formId}').validate({
		debug : true,
		rules : { },
		submitHandler : function() {
		   <@submitHandler/>
		}
	});
</script>
</#macro>
