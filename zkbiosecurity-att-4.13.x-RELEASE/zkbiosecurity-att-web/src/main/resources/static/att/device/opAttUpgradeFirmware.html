<#assign formId="attUpgradeFirmwareform${uuid}">
<#include "/public/template/editTemplate.html">
<#macro editMain></#macro>
<style>
        .att-access-infoDiv {
            border: solid 1px #aeb4b8;
            background-color: white;
            width: 100%;
            box-sizing: border-box;
            height: 135px;
            overflow: auto;
            margin-bottom: 8px;
        }

        // 解决展开，收缩详情界面抖动
         .attUpgradeFirmwareClass .opFooter {
            bottom: auto;
        }
    </style>
<@ZKUI.Process id="attUpgradeFirmware${uuid}" class="attUpgradeFirmwareClass" type="single" dealPath="" confirmText="common_dev_upgrade" height="600" onSure="attUpgradeFirmware">
<@ZKUI.Content name="op">
    <form style="padding: 0px 10px; height: 180px" method="post" id="${formId}" action="attDevice.do?upgradeFirmware" enctype="multipart/form-data" >
        <div style="margin-bottom: 2px"><label><@i18n 'common_dev_selectedDev'/></label></div>
        <div class="att-access-infoDiv zk-border-color zk-content-bg-color">
            <div name="selectDev" id="selectDev"></div>
        </div>
        <div style="display: flex;align-items: center">
            <@i18n 'common_dev_selectFile'/>
            <@ZKUI.Upload id="devFile${uuid}" name="devFile" />
        </div>
    </form>

    <script type="text/javascript">
        $(function(){
            var onlineOldProtocolDevName = "${(onlineOldProtocolDevName)!}";
            var onlineNewProtocolDevName = "${(onlineNewProtocolDevName)!}";
            var disabledDevNameStr = "${(disabledDevName)!}";
            var offlineDevNameStr = "${(offlineDevName)!}";
            var oldProtocolDeviceIds = "${(oldProtocolDeviceIds)!}";
            var newProtocolDeviceIds = "${(newProtocolDeviceIds)!}"
            if(onlineOldProtocolDevName.length == 0 && onlineNewProtocolDevName.length == 0)
            {
                $("#attUpgradeFirmware${uuid}ConfirmButton").attr("disabled", true);
                ZKUI.Upload.get("devFile${uuid}").disabled(true);
            }
            else
            {
                $("<div style='padding-left: 10px;margin-top: 5px;white-space:nowrap;'><b><@i18n 'att_device_canUpgrade'/></b><br/>" +"</div>").appendTo("#selectDev");
                var checked = false;
                if(onlineOldProtocolDevName.length != 0) {
                    checked = true;
                    $("<div style='white-space:nowrap;padding-left: 10px;'>" +
                        "<span id='span${uuid}'></span>" +
                        "<span style='font-weight: bold;margin-left: 5px;'>"+ "<@i18n 'att_device_oldProtocol'/>    "+onlineOldProtocolDevName + "</span>"+
                        "</div>").appendTo("#selectDev");
                    loadUIToDiv("input", "#span${uuid}", {
				   		useInputReq:true,
				   		type:"radio",
				   		id:"attUpgradeProtocolOld",
				   		name:"devIds",
				   		value:"old",
				   		devIds: oldProtocolDeviceIds,
				   		defaultChecked: checked
					})
                }
               if(onlineNewProtocolDevName.length != 0){
                   $("<div style='white-space:nowrap;padding-left: 10px;'>" +
                       "<span id='span${uuid}'></span>" +
                       "<span style='font-weight: bold;margin-left: 5px'>"+"<@i18n 'att_device_newProtocol'/>    "+ onlineNewProtocolDevName + "</span>"+
                       "</div>").appendTo("#selectDev");
                   loadUIToDiv("input", "#span${uuid}", {
				   		useInputReq:true,
				   		type:"radio",
				   		id:"attUpgradeProtocolNew",
				   		name:"devIds",
				   		value:"new",
				   		devIds: newProtocolDeviceIds,
				   		defaultChecked: !checked
					})
               }
            }

            if(disabledDevNameStr != "" && disabledDevNameStr.length > 0)
            {
                $("<div style='padding-left: 10px; margin-top: 5px; color: red;white-space:nowrap;'><b><@i18n 'att_device_disabled'/></b><br/>" + disabledDevNameStr + "</div>").appendTo("#selectDev");
            }

            if(offlineDevNameStr != "" && offlineDevNameStr.length > 0)
            {
                $("<div style='padding-left: 10px; margin-top: 5px; color: red;white-space:nowrap;'><b><@i18n 'att_device_offline'/></b><br/>" + offlineDevNameStr + "</div>").appendTo("#selectDev");
            }

            //升级文件名自定义的验证,必须使用emfw.cfg，同时兼容读头升级固件，文件名为*.bin;（新增支持img后缀文件上传，支持CFace01固件升级）
            jQuery.validator.addMethod("fileNameValid", function(value, element){
                if (value.substring(value.lastIndexOf('\\') + 1).toLowerCase().indexOf(".cfg") > -1)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }, "<@i18n 'common_prompt_fileNameSuffix'/>".format(".cfg"));

            $("#${formId}").validate({
                debug : true,
                rules :
                    {
                        "devFile" :{
                            required : true,
                            fileNameValid : true
                        }
                    },
                submitHandler: function(form) {
                    $("#confirmButton").attr("disabled", true);  //禁用掉升级按钮，防止在操作过程中多次点击该按钮
                    var protocol = $("input[id^='attUpgradeProtocol']:checked").val();
                    if(protocol == 'old'){
                        //文件大小超过20M校验
                        var f = ZKUI.Upload.get("devFile${uuid}").getFile()[0];
                    }
                    var inputCheckedId = $("input[id^='attUpgradeProtocol']:checked")[0].id;
                    var devIds = ZKUI.Input.get(inputCheckedId).options.devIds
                    $('#${formId}').ajaxSubmit({
                        async : true,
                        dataType : 'json',
                        data: {
                            devIds:devIds,
                            clientId: ZKUI.Process.get("attUpgradeFirmware${uuid}").options.clientId
                        },
                        success: function(data)
                        {
                            if (!data)
                            {
                                openMessage(msgType.error);
                            }
                            else
                            {
                                ZKUI.Grid.reloadGrid("${gridName}");
                            }
                        },
                        error:function (XMLHttpRequest, textStatus, errorThrown)
                        {
                            messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
                        }
                    });
                    ZKUI.Process.get("attUpgradeFirmware${uuid}").beginProgress(true);
                }
            });
        });

        function attUpgradeFirmware()
        {
            $("#${formId}").submit();
        }

    </script>

</@ZKUI.Content>
</@ZKUI.Process>
