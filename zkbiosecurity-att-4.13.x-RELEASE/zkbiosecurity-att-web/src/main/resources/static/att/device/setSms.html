<#include '/public/template/editTemplate.html'>
<#macro editContent>
<script type='text/javascript'>
    $().ready(function() {
			$('#${formId}').validate( {
				debug : true,
				rules :
				{
					//短消息内容
			        "msg": {
			            required: true
			        },
			        //开始时间
			        "startTime": {
			            required: true
			        },
			        //短消息持续时间
			        "min": {
			            required: true,
			            digits: true,
						min:1
			        },
				},
				submitHandler : function()
				{
					<@submitHandler/>
				}
			});
		});
	</script>

	<form action='attDevice.do?addSms' method='post' id='${formId}' enctype='multipart/form-data'>
	    <!-- 隐藏域 -->
		<input type='hidden' name='ids' value='${ids}'/>
		<table class='tableStyle'>
		    <tr>
                <!-- 短消息内容 -->
                <th><label><@i18n 'att_common_msg'/></label><span class='required'>*</span></th>
                <td><textarea id="id_msg" name="msg" maxlength="40" style="width: 148px; height: 50px;vertical-align: bottom;"></textarea></td>
            </tr>
			<tr>
				<!-- 开始时间 -->
				<th><label><@i18n 'common_startTime'/></label><span class='required'>*</span></th>
				<td><@ZKUI.Input id="id_startTime" name='startTime' type="datetime" hideLabel="true" readonly="true"/></td>
			</tr>
			<tr>
                <!-- 短消息持续时间（分） -->
                <th><label><@i18n 'att_common_min'/></label><span class='required'>*</span></th>
                <td><input id="id_min" name="min" type="text" value="60" maxlength="6" /></td>
            </tr>
		</table>
	</form>
</#macro>