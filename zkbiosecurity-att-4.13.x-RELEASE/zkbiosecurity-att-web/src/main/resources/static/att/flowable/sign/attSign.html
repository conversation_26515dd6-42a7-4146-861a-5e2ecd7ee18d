<#assign gridName="attSignGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}" showColumns="!zkGridOp">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
				<td valign="middle">
					<@ZKUI.Input type="datetime" startTimeId="startTime${uuid}" name="startTime" title="common_time_from" todayRange="start" max="today" today="-3" noOverToday="true" readonly="false"/>
					&nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
					<@ZKUI.Input type="datetime" endTimeId="endTimeId${uuid}" name="endTime" title="common_to" max="today" todayRange="end" today="true" noOverToday="true" hideLabel="true" readonly="false"/>
				</td>
			</tr>
    	</@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:sign:refresh"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="attWfSelfApply.do?startApply&flowType=sign" text="common_op_new" width="600" height="490" img="comm_add.png" action="openApplySignWindow" permission="att:sign:add"></@ZKUI.ToolItem>
        <@ZKUI.ToolItem id="attSign.do?export" type="export" text="common_op_export" img="comm_export.png" permission="att:sign:export"/>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttSignItem" query="attSign.do?list"/>
</@ZKUI.GridBox>

<script>

function afterAddSign() {
	ZKUI.Grid.reloadGrid("${gridName}");
}


/**
 * 打开新建窗口
 */
function openApplySignWindow(gridName, obj, rid) {
   $.ajax({
        url: "attWfSelfApply.do?validApplyFlow&flowType=${flowType}",
        success: function(res) {
            if ("ok" == res.ret) {
                var opts = {
                    path: "attWfSelfApply.do?startApply&flowType=${flowType}",
                    width: 500,
                    height: 400,
                    title: "<@i18n 'common_op_new'/>",
                    gridName: "gridbox",
                };
                DhxCommon.createWindow(opts);
            } else {
                messageBox({messageType:"alert", text: res.msg});
            }
        },
        error: function(e) {
            messageBox({messageType:"error", text: e});
        }
    });
}
</script>