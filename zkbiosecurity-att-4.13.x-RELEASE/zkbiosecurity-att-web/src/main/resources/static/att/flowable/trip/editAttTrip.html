<#if detail == "readonly">
    <#assign closeButtonText = "${getI18nByCode('common_op_close')}">
</#if>
<#include '/public/template/editTemplate.html'>
<#macro editContent>
<#if task?? && task.taskId?exists >
	<form action='attFlowable.do?complete&taskId=${task.taskId}' method='post' id='${formId}' enctype="multipart/form-data">
<#else>
	<form action='attFlowable.do?startTrip&flowType=${flowType}' method='post' id='${formId}' enctype="multipart/form-data">
</#if>
    <input type='hidden' id="id" name='id' value='${(item.id)!}'/>
	<input type='hidden' id="leaveImagePath" name='leaveImagePath' value='${(item.leaveImagePath)!}' />
	<!-- 假种为出差的id -->
	<input type='hidden' id="leaveTypeId" name='leaveTypeId' value='${(leaveTypeTripId)!}'/>
    <fieldset style="margin-bottom: 15px;">
	    <legend><@i18n 'att_flow_apply'/></legend>
	    <table id="applyTable" class='tableStyle'>
			<tr>
				<th><label><@i18n 'att_apply_type'/></label></th>
				<td><input value="<@i18n 'att_common_trip'/>" readonly type="text" />
				<#if !task || !task.processIndex?exists >
				<th style="text-align: right">
					<label><@i18n 'att_leave_image'/></label>
				</th>
				</#if>
				<td rowspan="4" style="padding: 1px 0px;">
					<!-- 请假单图片 -->
					<div id="preview" class="attFlowableLeaveImagePreview">
						<span id="idSpanImage" class="attFlowableSpanImage"><@i18n 'att_leave_imageShow'/></span>
					</div>
					<div style="display: none;">
						<!-- accept:规定能够通过文件上传进行提交的文件类型，最后的结尾需要","结束，否则表单不能提交 -->
						<input id="leaveImage" multiple="multiple" onchange="attFlowableChangePicture(this);" type="file" accept="image/gif,image/jpeg,image/jpg,image/png," />
					</div>
				</td>
			</tr>
	        <tr>
				<!-- 出差开始时间 -->
	            <th><label><@i18n 'common_startTime'/></label><span class='required'>*</span></th>
	            <td><@ZKUI.Input id="attTripStartTimeId" type="datetime" value="${(item.startDatetime?string('yyyy-MM-dd HH:mm:ss'))!}" readonly="true" today="true" name="startDatetime" endId="attTripEndTimeId" hideLabel="true" title="common_startTime" onHide="calTrip" /></td>
				<#if !task || !task.processIndex?exists >
					<th style="text-align: right">
						<input class="button-form" id="idBrowImage" value="<@i18n 'common_op_browse'/>" name="browImage" onclick="leaveImage.click();" type="button" />
					</th>
				</#if>
	        </tr>
	        <tr>
				<!-- 出差结束时间 -->
	            <th><label><@i18n 'common_endTime'/></label><span class='required'>*</span></th>
	            <td><@ZKUI.Input id="attTripEndTimeId" type="datetime" value="${(item.endDatetime?string('yyyy-MM-dd HH:mm:ss'))!}" readonly="true" today="true" name="endDatetime" hideLabel="true" title="common_endTime" onHide="calTrip"/></td>
			</tr>
	        <tr>
	             <!-- 出差时长 -->
	             <th><label><@i18n 'att_trip_tripLongHour'/></label><span class='required'>*</span></th>
	             <td><input name='leaveLongHour' id="leaveLongHour" readonly='true' maxlength="255" type='text' value='${(item.leaveLongHour)!}'/></td>
	             <div style="display: none;">
		             <input name='leaveLong' id="leaveLong" maxlength="255" type="text" value='${(item.leaveLong)!}' />
	             </div>
	        </tr>
			<tr>
				<#include '/att/flowable/approveContent.html' />
				<!-- 请假理由 -->
				<th style="text-align: right">
					<label><@i18n 'common_remark'/></label>
				</th>
				<td style="padding: 1px 0px;">
					<textarea name='remark' maxlength="255" rows="4" cols="22" style="width: 289px; height: 63px;">${(item.remark)!}</textarea>
				</td>
			</tr>
	    </table>
    </fieldset>
    <#include '/att/flowable/approveRecord.html'/>
</form>
<script type="text/javascript">
function calTrip(date){
	setTimeout(function() {
		var personId = "${personId}";
		var startTime = $("#${formId} input[name='startDatetime']").val();
		var endTime = $("#${formId} input[name='endDatetime']").val();
		console.log("startTime", startTime);
		console.log("endTime", endTime);
		//开始时间与结束时间对比
		if (startTime > endTime) {
			//开始时间不能大于结束时间
			messageBox({
				messageType: "alert",
				title: "<@i18n 'common_prompt_title'/>",
				text: "<@i18n 'common_dsTime_timeValid4'/>"
			});
			return;
		}
		$.ajax({
			url: "attLeave.do?calLeaveTime",
			type:"POST",
			data:{
				personId :personId,
				startDateTime : startTime,
				endDateTime : endTime,
				leaveTypeId : $("#leaveTypeId").val()
			},
			success: function (res) {
				if (res.ret == "ok") {
					$("#leaveLongHour").val(res.data.hour + ("<@i18n 'att_flowable_hour'/>"));
					$("#leaveLong").val(res.data.minute);
				}
			}
		});
	},100);
}

var userAgent = navigator.userAgent; //用于判断浏览器类型
var fileCount = 0;
var fileArray = new Array();
var pathArray = new Array();

function attFlowableChangePicture(file) {

	//获取选择图片的对象
	var docObj = $(file)[0];

	//得到所有的图片文件
	var fileList = docObj.files;
	if (fileList == null) {
		return false;
	}

	//清除错误信息
	$("#picTip").html("");
	if (fileCount > 4 || (fileCount + fileList.length) > 4) {
		messageBox({
			messageType: "alert",
			title: "<@i18n 'common_prompt_title'/>",
			text: "<@i18n 'att_leave_maxFileCount'/>"
		});
		return false;
	}

	// 校验图片格式
	for (var i = 0; i < fileList.length; i++){
		var extname = fileList[i].name.substring(fileList[i].name.lastIndexOf(".") + 1, fileList[i].name.length);
		extname = extname.toLowerCase();//处理了大小写
		if (extname != "jpeg" && extname != "jpg" && extname != "gif" && extname != "png") {
			openMessage (msgType.warning, "<@i18n 'att_leave_imageType'/>");
			return false;
		}
		//过滤图片大小
		var size = fileList[i].size;
		if (size > 4194304) {
			openMessage(msgType.warning, "<@i18n 'att_leave_imageSize'/>");
			return false;
		}
	}

	if (fileList.length > 0) {
		$("#idSpanImage").hide();
	}
	if (fileList.length <= 0) {
		$("#preview").html("<span id=\"idSpanImage\" class=\"attFlowableSpanImage\"><@i18n 'att_leave_imageShow'/></span>");
	}
	fileCount = fileCount + fileList.length;
	for (var i = 0; i < fileList.length; i++) {
		fileArray.push(fileList[i]);
	}

	attFlowableEachFileArray(fileArray, docObj);
	$("#leaveImage")[0].value = null;
}


/*删除功能*/
function attFlowableDelImage(obj, index) {
	var _this = $(obj);
	_this.parents(".attFlowableImageDiv").remove();
	if (fileCount > 0) {
		fileCount = fileCount -1;
		fileArray.splice(index, 1);
		pathArray.splice(index, 1);
	}
	if (fileCount <= 0) {
		$("#preview").html("<span id=\"idSpanImage\" class=\"attFlowableSpanImage\"><@i18n 'att_leave_imageShow'/></span>");
	}
}


function attFlowableEachFileArray(fileArray, docObj) {
	var picDiv = $("#preview");
	document.getElementById('preview').innerHTML = "";
	// picDiv.innerHTML = "";
	//循环遍历
	for (var i = 0; i < fileArray.length; i++) {
		//动态添加html元素
		var picHtml = "<div class='attFlowableImageDiv' ><div class='divImg'> <img id='img" + fileArray[i].name + "' /></div><div class='attFlowableDivCancel' onclick='attFlowableDelImage(this,"+i+")'></div> </div>";

		picDiv.prepend(picHtml);
		//获取图片imgi的对象
		var imgObjPreview = document.getElementById("img" + fileArray[i].name);
		if (fileArray[i]) {
			//图片属性
			imgObjPreview.style.display = 'block';
			imgObjPreview.style.width = '80px';
			imgObjPreview.style.height = '90px';
			//imgObjPreview.src = docObj.files[0].getAsDataURL();
			//火狐7以上版本不能用上面的getAsDataURL()方式获取，需要以下方式
			if (userAgent.indexOf('MSIE') == -1) {
				//IE以外浏览器
				imgObjPreview.src = window.URL.createObjectURL(fileArray[i]); //获取上传图片文件的物理路径;
				pathArray.push(imgObjPreview.src);
				// var msgHtml = '<input type="file" id="fileInput" multiple/>';
			} else {
				//IE浏览器
				if (docObj.value.indexOf(",") != -1) {
					var srcArr = docObj.value.split(",");
					imgObjPreview.src = srcArr[i];
				} else {
					imgObjPreview.src = docObj.value;
				}
			}
		}
	}
}

function attFlowableLeave() {
	var idImageDiv = $("#idImageDiv");
	if (pathArray.length > 0 && idImageDiv.length <= 0) {
		//动态添加html元素
		var picHtml = "";
		for (var i = 0; i < pathArray.length; i++) {
			picHtml = picHtml + "<div onclick='attFlowableImageClick()' class='attFlowableImageDiv' id='idImageDiv' ><div class='divImg'> <img id='idLeaveImage' width='80px' height='90px' src='"+pathArray[i]+"' /></div></div></div>";
		}
		document.getElementById("preview").innerHTML = picHtml;
		$("#idSpanImage").hide();
	} else if (pathArray.length <= 0) {
		$("#idSpanImage").show();
	}
}

function attFlowableImageClick() {
	var urlArray = new Array();
	for (var i=0; i<pathArray.length; i++) {
		var tempObject = {id: pathArray[i]};

		urlArray.push(tempObject);
	}
	$.jPic(urlArray,{action:"attLeave.do?getLeaveImage&url=",width:"640",height:"480"});
}

$().ready(function() {

	var minDate = new Date();
	minDate.setMonth(minDate.getMonth()-2);
	minDate.setDate(1);
	ZKUI.Input.PULL.attTripStartTimeId.setMinDate(minDate);
	ZKUI.Input.PULL.attTripEndTimeId.setMinDate(minDate);

	var leaveImagePath = "${item.leaveImagePath}";
	if (leaveImagePath.length > 0) {
		var photoUrlArray = leaveImagePath.split(",");
		for (var i=0; i<photoUrlArray.length; i++) {
			pathArray.push(photoUrlArray[i])
		}
		attFlowableLeave();
	}

	$("#${formId}").validate({
		debug: true,
		<#if task.flowStatus == null>
			rules: {
				'startDatetime': {
					required: true
				},
				'endDatetime': {
					required: true
				},
				"remark" : {
					unInputChar: true
				}
			},
		</#if>
		submitHandler: function () {
			<#if task.flowStatus == null>
				var startTime = $("#${formId} input[name='startDatetime']").val();
				var endTime = $("#${formId} input[name='endDatetime']").val();
				var personId = "${personId}";
				if (startTime > endTime) {
					//开始时间不能大于结束时间
					messageBox({
						messageType: "alert",
						title: "<@i18n 'common_prompt_title'/>",
						text: "<@i18n 'common_dsTime_timeValid4'/>"
					});
					return false;
				}
				else if(startTime == endTime){
					//结束时间不能等于开始时间
					messageBox({
						messageType : "alert",
						title : "<@i18n 'common_prompt_title'/>",
						text : "<@i18n 'common_dsTime_timeValid1'/>"
					});
					return false;
				}

				//对上传的请假单图片进行过滤
				if (fileArray.length > 0) {
					if (fileArray.length > 4) {
						messageBox({
							messageType: "alert",
							title: "<@i18n 'common_prompt_title'/>",
							text: "<@i18n 'att_leave_maxFileCount'/>"
						});
						return false;
					}

					for (var i = 0; i < fileArray.length; i++) {
						//过滤图片格式
						var extname = fileArray[i].name.substring(fileArray[i].name.lastIndexOf(".") + 1, fileArray[i].name.length);
						extname = extname.toLowerCase();//处理了大小写
						if (extname != "jpeg" && extname != "jpg" && extname != "gif" && extname != "png") {
							$("#newImage").css("height", "90px");
							$("#picTip").css("display", "inline-block");
							$("#picTip").html("<span style=\"color:Red;\"><@i18n 'att_leave_imageType'/></span>");
							return false;
						}

						//过滤图片大小
						var size = fileArray[i].size;
						if (size > 4194304) {
							$("#newImage").css("height", "90px");
							$("#picTip").css("display", "inline-block");
							$("#picTip").html("<span style=\"color:Red\"><@i18n 'att_leave_imageSize'/></span>");
							return false;
						}
					}
				}

				//判断相同时间内是否存在其他申请
				$.ajax({
					type: "post",
					url: "attLeave.do?existApply",
					data:{
						personIds:personId,
						startTime:startTime,
						endTime:endTime,
						exceptionType:"trip"
					},
					dataType: "json",
					async: false,
					success: function (result) {
						if(result.ret == "ok"){

							var url = $("#${formId}").attr("action");
							var fd = new FormData(document.getElementById("${formId}"));
							for (var i = 0; i < fileArray.length; i++) {
								fd.append("leaveImage", fileArray[i]);
							}

							onLoading(function () {
								$.ajax({
									url: url,
									type: 'post',
									processData: false,
									contentType: false,
									data: fd,
									success: function (result) {
										if (result.ret == "ok") {
											dealRetResult(eval(result), afterAddAttLeaveInfo);
											if(isContinueAdd) {
												DhxCommon.refreshCurrentWindow();
											} else {
												DhxCommon.closeWindow();
											}
										} else {
											openMessage(msgType.warning, result.msg);
										}

									}
								});
							});

						} else {
							var ptimeout = sysCfg.ptimeout;
							openMessage(msgType.warning, result[sysCfg.msg], ptimeout);
						}
					}
				});
			<#else>
				<@submitHandler callBackFun="reloadPendingTaskGrid()"/>
			</#if>
		}
	});
});
</script>
</#macro>