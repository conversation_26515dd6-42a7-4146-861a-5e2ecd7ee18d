<th>
	<label><@i18n 'wf_flow_node_notifier_Pers_names'/></label>
</th>
<td>
	<input id="notifierPerIds" type="hidden" name="notifierPerIds" value="${(item.notifierPerIds)!}" readonly />
	<textarea id="notifierPerNames" name="notifierPerNames" value="${(item.notifierPerNames)!}" rows="4" cols="22" style="width: 148px; height: 63px;cursor:pointer;vertical-align: bottom;" readonly></textarea>
	<a class="detail_hide" style="padding: 0px 2px; margin: 1px;vertical-align: text-bottom" onclick="$('#notifierPerIds').val('');$('#notifierPerNames').val('')"><@i18n "common_search_clear"/></a>

</td>
<style>
.notifierPers {
	margin: 10px;
	width: 400px;
	text-align: center;
}

.notifierPerItem {
	list-style: none;
	cursor: pointer;
	float: left;
	padding: 6px 16px;
	border: 1px solid #c2e7b0;
	border-radius: 5px;
	background-color: #f0f9eb;
	margin: 3px;
	color: #67c23a;
}

.notifierPerItem:hover {
	background: #67c23a;
	color: #fff;
	border-color: #67c23a;
}
</style>
<script>
    //选人控件回调事件
    function selectNotifierHandler(value, text, event) {
        $('#notifierPerIds').val(value)
        $('#notifierPerNames').val(text)
    }
    $(function(){
        var notifierPerIds='';
        var notifierPerNames='';
        <#if task?? && task.notifierPers??>
        <#list task.notifierPers?keys as key>
        notifierPerIds+=',${key}';
        notifierPerNames+=',${task.notifierPers["${key}"]}';
        </#list>
        </#if>
        
        if(notifierPerIds.length>0){
            $('#notifierPerIds').val(notifierPerIds.substring(1));
            $('#notifierPerNames').val(notifierPerNames.substring(1));
        }

        $("#notifierPerNames").keydown(function(){
            $(this).blur();
            $(this).attr("readonly","readonly");
            $(this).css('backgroundColor','#fff')
        }).blur();

        //选知会人窗口创建
        function createNotifierWindow(id) {
            $(id).data("hasReg", true);
            $(id).on("click", function(evt) {
                var selectId=$('#notifierPerIds').val();
                var searchType='4';
                var opts = {//选人控件弹窗配置
                    path:"skip.do?page=wf_flow_selectPersonContent&searchType="+searchType+"&value=" + selectId ,//弹窗路径
                    title:"<@i18n 'wf_flow_node_select_notifier'/>",//弹窗标题
                    width:700,//窗口宽度
                    height:450,//窗口高度
                    onSure:"selectNotifierHandler"//回调事件
                }
                if (opts.path && opts.path.indexOf("value=") == -1 && opts.selectValue) {
                    opts.path = opts.path+"&value=" + opts.value;
                }

                opts.callback = opts.callback || function() {
                        var sid = $(this.cell).find(".select_layout_box")[0];
                        if (sid) {
                            DhxCommon.initEvents(ZKUI.Select.get(sid.id), ZKUI.Select.suport_evts, opts);
                            if (!opts.onSure) {
                                ZKUI.Select.get(sid.id).attachEvent("onSure", function() {
                                    /* $(id).val(this.getSelectRowTexts());*/
                                    $(id).blur();
                                })
                            }
                        }
                    }
                DhxCommon.createWindow(opts);
            });
        }
        createNotifierWindow("#notifierPerNames");//给按钮注册选人事件
    });
    $('#${formId}SaveContinue').remove();
</script>

