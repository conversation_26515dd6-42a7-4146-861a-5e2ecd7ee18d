<style>
.att_approveRecord_approve {
	margin-top: 2px;
    line-height: 22px;
    width:100%;
}

.att_approveRecord_approve th {
    vertical-align:top;
    padding: 1px 10px;
    min-width: 91px;
    max-width: 150px;
    width: auto;
    text-align: left;
}

.att_approveRecord_detail_fieldset {
	padding: 1px 15px;
	margin-bottom: 15px;
}
.att_approveRecord_detail {
	border-collapse:separate;
	border-spacing:0px 10px;
   	width:100%;
   	text-align: left;
}

</style>

<#if task.approve>
<fieldset style="margin-bottom: 15px;">
	<legend><@i18n 'att_flow_approve'/></legend>
	<table class='att_approveRecord_approve'>
		<tr>
			<th>
				<label><@i18n 'att_flow_approveComment'/></label>
			</th>
			<td>
				<textarea id="comment" name='comment' maxlength="20" rows="2" cols="22" style="width: 148px;">${(item.comment)!}</textarea>
			</td>
		</tr>
		<tr>
			<th>
				<label><@i18n 'att_flow_approvePass'/></label>
				<span class='required'>*</span>
			</th>
			<td>
				<input id="passtrue" name="pass" type="radio" checked value="true" />
				<label for="passtrue"><@i18n 'att_apply_pass'/></label>
				<input id="passfalse" name="pass" type="radio" value="false" />
				<label for="passfalse"><@i18n 'att_apply_refuse'/></label>
			</td>
		</tr>
	</table>
</fieldset>
</#if> 
<#if task.detail>
<fieldset class="att_approveRecord_detail_fieldset">
	<legend><@i18n 'att_flow_node'/></legend>
	<table class="att_approveRecord_detail">
		<thead>
			<tr>
				<th><@i18n 'att_flow_operateUser'/></th>
				<th><@i18n 'att_flow_approveTime'/></th>
				<th><@i18n 'att_flow_approvePass'/></th>
				<th><@i18n 'att_flow_approveComment'/></th>
			</tr>
		</thead>
		<#list task.taskInfoItemList!! as taskInfoItem>
			<tr>
				<td style="width: 100px">${taskInfoItem.approvalUserName}</td>
				<td style="width: 150px">
					<#if taskInfoItem.approvalDateTime??>
					   ${(taskInfoItem.approvalDateTime)?string('yyyy-MM-dd HH:mm:ss')}
					<#else>
						-
					</#if>
				</td>
				<td style="width: 100px">
					<#if taskInfoItem.taskStatus == "0">
						 <@i18n 'att_apply_pass'/>
					<#elseif taskInfoItem.taskStatus == "1">
						 <@i18n 'att_approve_wait'/>
					<#elseif taskInfoItem.taskStatus == "2">
						 <@i18n 'att_apply_refuse'/>
					<#elseif taskInfoItem.taskStatus == "3">
						 <@i18n 'att_apply_revoke'/>
					</#if>
				</td>
				<td>
					${(taskInfoItem.comment)!'-'}
				</td>
			</tr>
		</#list>
	</table>
</fieldset>
</#if>
<script>
    <#if detail == "readonly">
		/*查看界面,删除可操作标签*/
		$('#${formId}').find("input,textarea").attr("disabled", "disabled");
		$('#${formId}OK').remove();
		// 查看界面隐藏图片上传按钮(浏览)
		$('.detail_hide').attr("style","visibility: hidden;");
		$("#idBrowImage").attr("style","visibility: hidden;");
		$("#viewLeavePhoto").removeAttr("disabled");
    </#if>
    <#if task.approve == true>
		/*审核界面,删除可操作标签*/
		$("#applyTable").find("input,textarea").attr("disabled", "disabled");
		$('#commentId').attr("disabled",true);
		$('.detail_hide').attr("style","visibility: hidden;");
		$("#idBrowImage").attr("style","visibility: hidden;");
		$("#viewLeavePhoto").removeAttr("disabled");
    </#if>
    <#if task.flowType == "sign">
    	$("#comment").width("265px");
    </#if>
    <#if task.flowType == "leave" || task.flowType == "out" || task.flowType == "trip">
    	$("#comment").width("560px");
    </#if>
    <#if task.flowType == "leave">
    	ZKUI.Combo.get("leaveTypeCombo").combo.disable(true);
    </#if>
    <#if task.flowType == "overtime">
    	ZKUI.Combo.get("overtimeSign").combo.disable(true);
    </#if>
</script>