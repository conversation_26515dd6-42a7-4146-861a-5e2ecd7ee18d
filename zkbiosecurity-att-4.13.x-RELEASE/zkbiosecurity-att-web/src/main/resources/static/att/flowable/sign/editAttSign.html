<#if detail == "readonly">
	<#assign closeButtonText = "${getI18nByCode('common_op_close')}">
</#if>
<#include '/public/template/editTemplate.html'>

<style>
#attSignTimeId {
	display:block!important;
}
</style>
<#macro editContent>
<#if task?? && task.taskId?exists >
	<form action='attFlowable.do?complete&taskId=${task.taskId}' method='post' id='${formId}' enctype="multipart/form-data">
<#else>
	<form action='attFlowable.do?startSign&flowType=${flowType}' method='post' id='${formId}' enctype="multipart/form-data">
</#if>
		<input type='hidden' id="id" name='id' value='${(item.id)!}' />
		<fieldset style="margin-bottom: 15px;">
			<legend><@i18n 'att_flow_apply'/></legend>
			<table id="applyTable" class='tableStyle'>
				<tr>
					<th>
						<label><@i18n 'att_sign_signDate'/></label>
						<span class='required'>*</span>
					</th>
					<td>
						<div style="display:inline-block;vertical-align: top;">
							<@ZKUI.Input style="width:111px" type="date" id="signDatetime" value="${(item.signDatetime?string('yyyy-MM-dd'))!}" max="today" noOverToday="true" name="signDatetime" today="true" hideLabel="true"/>
						</div>
						<div style="display:inline-block;">
							<@ZKUI.Input style="width:111px;" ignoreFormat="true" name="afterSignRecord" type="datetime" onlyTime="true" value="${(item.signDatetime?string('HH:mm'))!}" hideSeconds="true" format="%H:%i" today="true" readonly="true" hideLabel="true"/>
						</div>
						</td>
				</tr>
				<tr>
					<th><label><@i18n 'att_cardStatus_attState'/></label><span class='required'>*</span></th>
					<td>
						<@ZKUI.Combo autoFirst="true" width="148" hideLabel="true" empty="false" name="attState" path="attDevice.do?getShortcutKeyName"></@ZKUI.Combo>
					</td>
				</tr>
				<#include '/att/flowable/approveContent.html' />
				<tr>
					<th>
						<label><@i18n 'common_remark'/></label>
					</th>
					<td>
						<textarea name='remark' maxlength="20" type='text' rows="4" cols="22" style="width: 240px; height: 63px;">${(item.remark)!}</textarea>
					</td>
				</tr>
			</table>
		</fieldset>
		<#include '/att/flowable/approveRecord.html'/>
	</form>

	<script type="text/javascript">
        $("#notifierPerNames").width("240px");
        var workList = "";
        var isHaveWorkTime = true;
        $().ready(function() {
            $("#${formId}").validate({
                debug : true,
                <#if task.flowStatus == null>
					rules : {
						'signDatetime' : {
							required : true
						},
						"remark" : {
							unInputChar: true
						}
					},
                </#if>
                submitHandler : function() {
                    <#if task.flowStatus == null>
						<@submitHandler callBackFun="afterAddSign()"/>
					</#if>
					<#if task.flowStatus != null>
						<@submitHandler callBackFun="reloadPendingTaskGrid()"/>
					</#if>
				}
		   });
        });

        var minDate = new Date();
        minDate.setMonth(minDate.getMonth()-2);
        minDate.setDate(1);
        ZKUI.Input.PULL.signDatetime.setMinDate(minDate);
    </script>
	</#macro>