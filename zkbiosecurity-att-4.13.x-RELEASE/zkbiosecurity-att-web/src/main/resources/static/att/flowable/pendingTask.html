<#assign gridName="flowableGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">
	<@ZKUI.Searchbar>
		<@ZKUI.SearchTop>
			<tr>
				<!-- 流程类型 -->
				<td valign="middle">
					<@ZKUI.Combo empty="true" name="flowType" title="wf_flow_type">
						<option value="sign"><@i18n 'wf_leftMenu_sign'/></option>
						<option value="leave"><@i18n 'wf_leftMenu_leave'/></option>
						<option value="trip"><@i18n 'wf_leftMenu_trip'/></option>
						<option value="out"><@i18n 'wf_leftMenu_out'/></option>
						<option value="overtime"><@i18n 'wf_leftMenu_overtime'/></option>
					</@ZKUI.Combo>
				</td>
				<!-- 名称 -->
				<td valign="middle">
					<input id="idPersonId" name="personId"  type="hidden"/>
					<@ZKUI.Input name="personPin"  maxlength="30" title="att_apply_personPin" type="text"/>
				</td>
			</tr>
		</@ZKUI.SearchTop>
	</@ZKUI.Searchbar>

	<@ZKUI.Toolbar>
		<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="wf:apply:refresh"></@ZKUI.ToolItem>
	</@ZKUI.Toolbar>
	<@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttProcessInfoItem" showColumns="!taskStatus" query="attFlowable.do?pendingTaskList"/>
</@ZKUI.GridBox>
<script type="text/javascript">
    /*流程信息详情查看*/
    function detailView(gridName, obj, rid, rowIndex, column, row){
        var opt = {};
        opt.gridName = gridName;
        opt.path = "attWfSelfApply.do?detail&businessKey=(businessKey)";

        opt.path = replaceJsonToString(opt.path, row);
        opt.width = 500;
        opt.height = 470;
        opt.title = row.flowType;
        opt.editPage = true;
        if(row.flowType=="<@i18n 'att_leftMenu_leave'/>" || row.flowType=="<@i18n 'att_leftMenu_out'/>" || row.flowType=="<@i18n 'att_leftMenu_trip'/>") {
            opt.width = 820;
            opt.height = 470;
        }
        DhxCommon.createWindow(opt);
    }

    /*流程处理*/
    function handleProcess(gridName, obj, rid, rowIndex, column, row){
        var opt = {};
        opt.gridName = gridName;
        opt.path = "attWfSelfApply.do?approve&taskId=(taskId)";
        opt.path = replaceJsonToString(opt.path, row);
        opt.width = 500;
        opt.height = 610;
        opt.title = row.flowType;
        opt.editPage = true;
       	if(row.flowType=="<@i18n 'att_leftMenu_leave'/>" || row.flowType=="<@i18n 'att_leftMenu_out'/>" || row.flowType=="<@i18n 'att_leftMenu_trip'/>") {
            opt.width = 820;
            opt.height = 570;
        }
        DhxCommon.createWindow(opt);
    }
    //初始化按人员查询搜索框
    function initPersonInfo(value){
        if (value == null|| value.length==0) {
            /* $("#idPersonId").val("");
             $("#idPersonName").val("");
             $("#idDeptName").val("");
             $("#idPersonPin").val("");*/
        }else{
            $.ajax({
                async: true,
                dataType: "json",
                type: "post",
                url: "attFlowable.do?getPersPersonByBlurPin&pinOnly=true",
                data: {pin: value},
                success: function (data) {
                    if (!$.isEmptyObject(data)) {
                        $.each(data, function (n, person) {
                            $("#idPersonId").val(person.personId);
                        });
                    }
                }
            });
        }
    }
    /*
    function initPersonCombo() {
        var personCombo = ZKUI.Combo.get("personPin${uuid}").combo;
        personCombo.setPlaceholder("<@i18n 'att_node_searchPerson' />");
        $("#personPin${uuid} input[class='dhxcombo_input']").keyup(function () {//模糊搜索动态显示park人员、输入触发
            var pin = $.trim(personCombo.getComboText());// 模糊搜索条件
            personCombo.clearAll(true);//清空原有的下拉人员
            //异步请求对应的数据
            $.ajax({
                async: true,
                dataType: "json",
                type: "post",
                url: "attFlowable.do?getPersPersonByBlurPin",
                data: {pin: pin},
                success: function (data) {
                    var resultMap = "";
                    resultMap += "<complete>";
                    $.each(data, function (n, value) {
                        resultMap += "<option value='" + value.nameKey + "'>"
                            + value.nameValue + "</option>"
                    });
                    resultMap += "</complete>";
                    personCombo.load(resultMap, function () {
                        personCombo.openSelect();// 展开人员下拉框
                    });
                }
            });
        });
        personCombo.attachEvent("onChange", function (value, text) {
            initPersonInfo(value);
            personCombo.clearAll(true);//清空原有的下拉人员
        });
    }
    initPersonCombo()
    */
    /**
     * 刷新列表
     */
    function reloadPendingTaskGrid() {
    	ZKUI.Grid.reloadGrid("${gridName}");
    }
</script>