<#assign gridName="attLeaveGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}" showColumns="!zkGridOp">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
				<td valign="middle">
					<@ZKUI.Input type="datetime" startTimeId="startTime${uuid}" name="startApplyDateTime" title="common_time_from" todayRange="start" max="today" today="-1" noOverToday="true" readonly="false"/>
					&nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
					<@ZKUI.Input type="datetime" endTimeId="endTimeId${uuid}" name="endApplyDateTime" title="common_to" todayRange="end" today="true" noOverToday="true" offset="1" hideLabel="true" readonly="false"/>
    			</td>
			</tr>
    	</@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:leave:refresh"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="attWfSelfApply.do?startApply&flowType=${flowType}" text="common_op_new" img="comm_add.png" action="openApplyLeaveWindow" permission="att:leave:add"></@ZKUI.ToolItem>
        <@ZKUI.ToolItem id="attLeave.do?export&flowType=${flowType}" type="export" text="common_op_export" img="comm_export.png" permission="att:leave:export"/>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttLeaveItem" query="/attLeave.do?list&flowType=${flowType}"/>
</@ZKUI.GridBox>
<script type="text/javascript">

function afterAddAttLeaveInfo() {
	ZKUI.Grid.reloadGrid("${gridName}");
}

function openLeaveImage(gridName, domObj, rid) {
	$.ajax({
		type: "POST",
		url: "/attLeave.do?getImageUrl&id=" + rid,
		dataType: "json",
		async: false,
		success: function(result)
		{
			if (result.ret == "ok") {
				var photoUrlArray = result.data.split(",");
				var urlArray = new Array();
				for (var i=0; i<photoUrlArray.length; i++) {
					var tempObject = {id: photoUrlArray[i]};

					urlArray.push(tempObject);
				}
				$.jPic(urlArray,{action:"/attLeave.do?getLeaveImage&url=",width:"640",height:"480"});
			} else {
				messageBox({messageType:"alert", text: result.msg});
			}
		},
		error: function(XMLHttpRequest, textStatus, errorThrown)
		{
			messageBox({messageType:"alert", text: "<@i18n 'common_prompt_serverError'/>"+ "-612"});
		}
	});
}

function openApplyLeaveWindow(gridName, obj, rid) {
  $.ajax({
        url: "attWfSelfApply.do?validApplyFlow&flowType=${flowType}",
        success: function(res) {
            if ("ok" == res.ret) {
                var opts = {
                    path: "attWfSelfApply.do?startApply&flowType=${flowType}",
                    width: '820',
                    height: 370,
                    title: "<@i18n 'common_op_new'/>",
                    gridName: "gridbox",
                };
                DhxCommon.createWindow(opts);
            } else {
                messageBox({messageType:"alert", text: res.msg});
            }
        },
        error: function(e) {
            messageBox({messageType:"error", text: e});
        }
    });
}
</script>