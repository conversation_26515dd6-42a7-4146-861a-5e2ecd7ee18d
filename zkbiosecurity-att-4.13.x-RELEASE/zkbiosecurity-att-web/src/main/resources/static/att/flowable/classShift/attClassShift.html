<#assign gridName="attClassGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
				<td valign="middle">
					<@ZKUI.Input type="datetime" startTimeId="startTime${uuid}" name="startTime" title="common_time_from" todayRange="start" max="today" today="-3" noOverToday="true" readonly="false"/>
					&nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
					<@ZKUI.Input type="datetime" endTimeId="endTimeId${uuid}" name="endTime" title="common_to" max="today" todayRange="end" today="true" noOverToday="true" hideLabel="true" readonly="false"/>
    			</td>
				<td valign="middle">
					<@ZKUI.Input name="adjustPersonPin"  maxlength="30" title="att_person_pin" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="likeName"  maxlength="30" title="pers_person_wholeName" type="text"/>
				</td>
			</tr>
    	</@ZKUI.SearchTop>
 		<@ZKUI.SearchBelow>
 			<tr>
 				<td valign="middle">
					<@ZKUI.Input name="adjustDeptCode"  maxlength="30" title="pers_dept_deptNo" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="adjustDeptName"  maxlength="30" title="pers_dept_deptName" type="text"/>
				</td>
			</tr>
 		</@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:class:refresh"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="attWfSelfApply.do?startApply&flowType=classShift" text="common_op_new" width="850" height="320" img="comm_add.png" action="commonAdd" permission="att:class:add"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="attClass.do?del&pins=(adjustPersonPin)" text="common_op_del" img="comm_del.png" action="commonDel" permission="att:class:del"></@ZKUI.ToolItem>
        <@ZKUI.ToolItem id="attClass.do?export" type="export" text="common_op_export" img="comm_export.png" permission="att:class:export"/>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttClassItem" query="attClass.do?list"/>
</@ZKUI.GridBox>