<#assign gridName="attOvertimeGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}" showColumns="!zkGridOp">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
				<td valign="middle">
					<@ZKUI.Input type="datetime" startTimeId="startTime${uuid}" name="startTime" title="common_time_from" todayRange="start" max="today" today="-1" noOverToday="true" readonly="false"/>
					&nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
					<@ZKUI.Input type="datetime" endTimeId="endTimeId${uuid}" name="endTime" title="common_to" max="today" todayRange="end" today="true" noOverToday="true" offset="1" hideLabel="true" readonly="false"/>
    			</td>
			</tr>
    	</@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="att:overtime:refresh"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="attWfSelfApply.do?startApply&flowType=overtime" text="common_op_new" width="450" height="500" img="comm_add.png" action="openApplyOvertimeWindow" permission="att:overtime:add"></@ZKUI.ToolItem>
        <@ZKUI.ToolItem id="attOvertime.do?export" type="export" text="common_op_export" img="comm_export.png" permission="att:overtime:export"/>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.att.vo.AttOvertimeItem" query="attOvertime.do?list"/>
</@ZKUI.GridBox>
<script>

function afterAddOvertime() {
	ZKUI.Grid.reloadGrid("${gridName}");
}

/**
 * 打开新建窗口
 */
function openApplyOvertimeWindow(gridName, obj, rid) {
   $.ajax({
        url: "attWfSelfApply.do?validApplyFlow&flowType=${flowType}",
        success: function(res) {
            if ("ok" == res.ret) {
                var opts = {
                    path: "attWfSelfApply.do?startApply&flowType=${flowType}",
                    width: 450,
                    height: 500,
                    title: "<@i18n 'common_op_new'/>",
                    gridName: "gridbox",
                };
                DhxCommon.createWindow(opts);
            } else {
                messageBox({messageType:"alert", text: res.msg});
            }
        },
        error: function(e) {
            messageBox({messageType:"error", text: e});
        }
    });
}
</script>