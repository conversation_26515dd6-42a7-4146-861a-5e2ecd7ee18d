<#include '/public/template/editTemplate.html'>
<#macro editContent>

<#if task?? && task.taskId?exists >
    <form action='attFlowable.do?complete&taskId=${task.taskId}' method='post' id='${formId}'>
<#else>
    <form action='attFlowable.do?start&flowType=${flowType}' method='post' id='${formId}'>
</#if>


    <input type='hidden' id="id" name='id' value='${(item.id)!}'/>

    <table class='tableStyle'>

        <tr>
            <th><label><@i18n 'att_adjust_type'/></label><span class='required'>*</span></th>
            <td>
                <@ZKUI.Combo id="adjustType${uuid}" empty="false" hideLabel="true" name="adjustType" value="${(item.adjustType)!}" width="148" onChange="adjustTypeChange">
                <option value="0"><@i18n 'att_rule_off'/></option>
                <option value="1"><@i18n 'att_rule_class'/></option>
            </@ZKUI.Combo>
            </td>
        </tr>
        <tr>
            <th><label><@i18n 'att_adjust_adjustDate'/></label><span class='required'>*</span></th>
            <td>
                <@ZKUI.Input id="attAdjustDateId" type="date" value="${(item.adjustDate)!}" name="adjustDate" readonly="true" today="true" hideLabel="true" title="att_adjust_adjustDate"/>
            </td>
        </tr>
        <tr id="bcmc">
            <!-- 班次 -->
            <th><label><@i18n 'att_adjust_shiftName'/></label><span class="required">*</span></th>
            <td>
                <@ZKUI.Combo id="attAdjustSelectShift${uuid}" width="148" hideLabel="true" empty="false" name="shiftId" value="${attShiftId}" readonly="true" path="attShift.do?getShiftList">
            </@ZKUI.Combo>
            </td>
        </tr>
        <tr>
            <th><label><@i18n 'common_remark'/></label></th>
            <td><input name='remark' type='text' maxlength="255" value='${(item.remark)!}'/></td>
        </tr>

        <#if task.processIndex?exists >
        <tr>
            <!-- 请假时长 -->
            <th><label><@i18n '批注'/></label></th>
            <td><textarea name='comment' style="width: 400px" maxlength="255" rows="4" >${(item.comment)!}</textarea></td>
        </tr>
        <tr>
            <!-- 请假时长 -->
            <th><@i18n '审批结果'/></label></th>
            <td>
                <input id="passtrue" name="pass" type="radio" checked value="true"/><label for="passtrue"><@i18n '通过'/></label>&nbsp;&nbsp;&nbsp;&nbsp;
                <input id="passfalse" name="pass" type="radio" value="false"/><label for="passfalse"><@i18n '驳回'/></label>
            </td>
        </tr>
        </#if>

        <#include '/att/flowable/approveContent.html' />
    </table>
</form>
<script type='text/javascript'>
    $().ready(function() {
        $('#${formId}').validate( {
            debug : true,
            rules :
                {
                    'adjustDate' : {
                        required : true
                    },
                    'adjustType' :{
                        required : true
                    }
                },
            submitHandler : function()
            {

                var personId = "${personId}";


                var adjustType = ZKUI.Combo.get("adjustType${uuid}").combo.getSelected();

                if(adjustType=="1"){
                    var attAdjustSelectShift = ZKUI.Combo.get("attAdjustSelectShift${uuid}").combo.getSelected();
                    if (!attAdjustSelectShift) {
                        messageBox({messageType: "alert", text: "<@i18n 'att_adjust_selectClass'/>"});
                        return false;
                    }
                }

                if(adjustType=="0"){
                    ZKUI.Combo.get('attAdjustSelectShift${uuid}').combo.setComboValue("")
                }

                //判断相同时间内是否存在其他申请
                var startTime = $("#attAdjustDateId").val();
                $.ajax({
                    type: "post",
                    url: "attLeave.do?existApply",
                    data:{
                        personIds:personId,
                        startTime:startTime,
                        endTime:startTime,
                        exceptionType:"adjust"
                    },
                    dataType: "json",
                    async: false,
                    success: function (result) {
                        if(result.ret == "ok")
                        {
                        <@submitHandler/>
                        }
                        else
                        {
                            var ptimeout = sysCfg.ptimeout;
                            openMessage(msgType.warning, result[sysCfg.msg], ptimeout);
                        }
                    }
                });
            }
        });
    });

    $("#bcmc").hide();
    function adjustTypeChange(value, text) {
        if (value == 0) {
            $("#bcmc").hide();
        } else if (value == 1){
            $("#bcmc").show();
        }
    }
</script>
</#macro>