<#include '/public/template/editTemplate.html'>
<#macro editContent>
    <#if task?? && task.taskId?exists >
        <form action='attFlowable.do?complete&taskId=${task.taskId}' method='post' id='${formId}'>
            <#else>
                <form action='attFlowable.do?start&flowType=${flowType}' method='post' id='${formId}'>
    </#if>

    <input type='hidden' id="id" name='id' value='${(item.id)!}'/>
    <input id="idSwapPersonId" name="swapPersonId" readonly="readonly" type="hidden" value="${(item.swapPersonId)!}" />
    <input id="idAdjustPersonId" name="adjustPersonId" readonly="readonly" type="hidden" value="${(item.adjustPersonId)!}" />
    <input id="idAdjustDeptCode" name="adjustDeptCode" readonly="readonly" type="hidden" value="${(item.adjustDeptCode)!}" />
    <input id="idSwapDeptCode" name="swapDeptCode" readonly="readonly" type="hidden" value="${(item.swapDeptCode)!}" />
    <input id="idAdjustDeptId" name="adjustDeptId" readonly="readonly" type="hidden" value="${(item.adjustDeptId)!}" />
    <input id="idSwapDeptId" name="swapDeptId" readonly="readonly" type="hidden" value="${(item.swapDeptId)!}" />


    <table class='tableStyle'>

        <tr>
            <th><label><@i18n 'att_class_type'/></label></th>
            <td>
                <@ZKUI.Combo id="idAdjustType" width="148" empty="false" value="${(item.adjustType)!}" hideLabel="true" name="adjustType" onChange="adjustTypeChange">
                <option value="0"><@i18n 'att_class_sameTimeMoveShift'/></option>
                <option value="1"><@i18n 'att_class_differenceTimeMoveShift'/></option>
                <option value="2"><@i18n 'att_class_twoPeopleMove'/></option>
            </@ZKUI.Combo>
            </td>
        </tr>
        <tr>
            <th><label><@i18n 'att_person_pin'/></label><span class="required">*</span></th>
            <td>
                <input id="idAdjustPersonPin" maxlength="30" name="adjustPersonPin" readonly="readonly" type="text" value="${(item.adjustPersonPin)!}" />
            </td>
            <th id="one" style="display: none;"><label><@i18n 'att_class_movePersonPin'/></label><span class="required">*</span></th>
            <td id="two" style="display: none;">
                <input id="idSwapPersonPin" maxlength="30" name="swapPersonPin" onblur="getPersonInfo(id, 'Swap');" type="text" value="${(item.swapPersonPin)!}" />
            </td>
        </tr>
        <tr>
            <th><label><@i18n 'att_person_name'/></label></th>
            <td>
                <input id="idAdjustPersonName" name="adjustPersonName" readonly="readonly" type="text" value="${(item.adjustPersonName)!}" />
            </td>
            <th id="three" style="display: none;"><label><@i18n 'att_class_movePersonName'/></label></th>
            <td id="four" style="display: none;">
                <input id="idSwapPersonName" name="swapPersonName" readonly="readonly" type="text" value="${(item.swapPersonName)!}" />
            </td>
        </tr>
        <tr>
            <th style="display: none;"><label><@i18n 'att_person_lastName'/></label></th>
            <td style="display: none;">
                <input id="idAdjustPersonLastName" name="adjustPersonLastName" readonly="readonly" type="text" value="${(item.adjustPersonLastName)!}" />
            </td>
            <th id="three2" style="display: none;"><label><@i18n 'att_class_movePersonLastName'/></label></th>
            <td id="four2" style="display: none;">
                <input id="idSwapPersonLastName" name="swapPersonLastName" readonly="readonly" type="text" value="${(item.swapPersonLastName)!}" />
            </td>
        </tr>
        <tr>
            <th><label><@i18n 'pers_dept_deptName'/></label></th>
            <td>
                <input id="idAdjustDeptName" name="adjustDeptName" readonly="readonly" type="text" value="${(item.adjustDeptName)!}" />
            </td>
            <th id="five" style="display: none;"><label><@i18n 'att_class_moveDeptName'/></label></th>
            <td id="six" style="display: none;">
                <input id="idSwapDeptName" name="swapDeptName" readonly="readonly" type="text" value="${(item.swapDeptName)!}" />
            </td>
        </tr>
        <tr>
            <th><label><@i18n 'att_adjust_adjustDate'/></label><span class="required">*</span></th>
            <td>
                <@ZKUI.Input id="attClassAdjustDateId" type="date" value="${(item.adjustDate)!}" today="true" readonly="true" name="adjustDate" hideLabel="true" title="att_adjust_adjustDate"/>
            </td>
            <th id="seven" style="display: none;"><label><@i18n 'att_class_moveDate'/></label></th>
            <td id="eight" style="display: none;">
                <@ZKUI.Input type="date" value="${(item.swapDate)!}" name="swapDate" today="true" readonly="true" hideLabel="true"/>
            </td>
            <th id="eleven"><label><@i18n 'att_class_shiftName'/></label></th>
            <td id="twelve">
                <@ZKUI.Combo width="148" id="swapShiftId${uuid!}" hideLabel="true" empty="true" name="swapShiftId" path="attShift.do?getShiftList">
            </@ZKUI.Combo>
            </td>
        </tr>
        <tr>
            <th><label><@i18n 'common_remark'/></label></th>
            <td colspan="4">
                <input id="idAdjustReason" maxlength="25" name="remark" style="height: 50px; width: 585px;" type="text" value="${(item.remark)!}" />
            </td>
        </tr>


        <#if task.processIndex?exists >
        <tr>
            <th><label><@i18n '批注'/></label></th>
            <td><textarea name='comment' style="width: 400px" maxlength="255" rows="4" >${(item.comment)!}</textarea></td>
        </tr>
        <tr>
            <th><@i18n '审批结果'/></label></th>
            <td>
                <input id="passtrue" name="pass" type="radio" checked value="true"/><label for="passtrue"><@i18n '通过'/></label>&nbsp;&nbsp;&nbsp;&nbsp;
                <input id="passfalse" name="pass" type="radio" value="false"/><label for="passfalse"><@i18n '驳回'/></label>
            </td>
        </tr>
        </#if>

        <#include '/att/flowable/approveContent.html' />
    </table>
</form>
<script type='text/javascript'>
    $().ready(function() {
        $('#${formId}').validate( {
            debug : true,
            rules :{
                'adjustPersonPin' :{
                    required : true
                }
            },
            submitHandler : function()
            {
                //判断相同时间内是否存在其他申请
                var personIds = $("#idAdjustPersonId").val();
                var startTime = $("#attClassAdjustDateId").val();
                //根据判断是否存在对应的提示

                var adJuestType = ZKUI.Combo.get("idAdjustType").combo.getSelected();

                if(adJuestType == 0){
                    var swapshiftVal=ZKUI.Combo.get("swapShiftId${uuid!}").combo.getSelected();
                    if(swapshiftVal==""){
                        messageBox({messageType: "alert", text: "<@i18n 'att_class_shiftNameNoNull'/>"});
                        return false;
                    }
                }
                if(adJuestType == 2){

                    if ($("#idAdjustPersonPin").val() == $("#idSwapPersonPin").val()) {
                        //调整人员和对调人员不能相同，请重新添加
                        messageBox({messageType: "alert", text: "<@i18n 'att_class_personNoSame'/>"});
                        return false;
                    }

                    if ($("#idSwapPersonPin").val() == "") {
                        //对调人员不能为空
                        messageBox({messageType: "alert", text: "<@i18n 'att_class_personPinNoNull'/>"});
                        return false;
                    }
                }
                $.ajax({
                    type: "post",
                    url: "attLeave.do?existApply",
                    data:{
                        personIds:personIds,
                        startTime:startTime,
                        endTime:startTime,
                        exceptionType:"class"
                    },
                    dataType: "json",
                    async: false,
                    success: function (result) {
                        if(result.ret == "ok"){
                        <@submitHandler/>
                        }
                        else {
                            var ptimeout = sysCfg.ptimeout;
                            openMessage(msgType.warning, result[sysCfg.msg], ptimeout);
                        }
                    }
                });
            }
        });

        var personPin = "${personPin}";
        $.ajax({
            dataType: "json",
            type: "post",
            data: {
                pin: personPin
            },
            url: "attPerson.do?getPersonInfo",
            success: function(result) {
                if (result) {
                    $("#idAdjustPersonId").val(result.data.personId);
                    $("#idAdjustPersonPin").val(result.data.personPin);
                    $("#idAdjustPersonName").val(result.data.personName);
                    $("#idAdjustPersonLastName").val(result.data.personLastName);
                    $("#idAdjustDeptId").val(result.data.deptId);
                    $("#idAdjustDeptCode").val(result.data.deptCode);
                    $("#idAdjustDeptName").val(result.data.deptName);
                }
            }
        });

    });


    function adjustTypeChange(value, text) {
        if (value == 0) {
            $("#one").hide();
            $("#two").hide();
            $("#three").hide();
            $("#four").hide();
            $("#five").hide();
            $("#six").hide();
            $("#seven").hide();
            $("#eight").hide();
            $("#eleven").show();
            $("#twelve").show();
            $("#nine").show();
            $("#ten").show();
            $("#idAdjustReason").css("width", "579px");
        } else if (value == 1) {
            $("#one").hide();
            $("#two").hide();
            $("#three").hide();
            $("#four").hide();
            $("#five").hide();
            $("#six").hide();
            $("#seven").show();
            $("#eight").show();
            $("#nine").show();
            $("#ten").show();
            $("#eleven").hide();
            $("#twelve").hide();
            $("#idAdjustReason").css("width", "579px");
        } else if (value == 2) {
            $("#one").show();
            $("#two").show();
            $("#three").show();
            $("#four").show();
            $("#five").show();
            $("#six").show();
            $("#seven").show();
            $("#eight").show();
            $("#nine").show();
            $("#ten").show();
            $("#eleven").hide();
            $("#twelve").hide();
            $("#idAdjustReason").css("width", "585px");
        }
    }

    /*
     ******************
     * 事件定义
     ******************
     */
    // 获取人员信息
    function getPersonInfo(personPin, adjustOrSwap) {
        var personPin = $("#" + personPin).val();
        $.ajax({
            dataType: "json",
            type: "post",
            data: {
                pin: personPin
            },
            url: "attPerson.do?getPersonInfo",
            success: function(result) {
                if (result) {
                    $("#id" + adjustOrSwap + "PersonId").val(result.data.personId);
                    $("#id" + adjustOrSwap + "PersonPin").val(result.data.personPin);
                    $("#id" + adjustOrSwap + "PersonName").val(result.data.personName);
                    $("#id" + adjustOrSwap + "PersonLastName").val(result.data.personLastName);
                    $("#id" + adjustOrSwap + "DeptId").val(result.data.deptId);
                    $("#id" + adjustOrSwap + "DeptCode").val(result.data.deptCode);
                    $("#id" + adjustOrSwap + "DeptName").val(result.data.deptName);
                }
            }
        });
    }
</script>
</#macro>