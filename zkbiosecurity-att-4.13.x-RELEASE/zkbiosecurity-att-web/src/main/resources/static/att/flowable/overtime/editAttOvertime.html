<#if detail == "readonly">
    <#assign closeButtonText = "${getI18nByCode('common_op_close')}">
</#if>
<#include '/public/template/editTemplate.html'> 
<style>
.tableStyle4 th{
    min-width:84px;
}
</style>
<#macro editContent> 
<#if task?? && task.taskId?exists >
    <form action='attFlowable.do?complete&taskId=${task.taskId}' method='post' id='${formId}' enctype="multipart/form-data">
	<#else>
	<form action='attFlowable.do?startOvertime&flowType=${flowType}' method='post' id='${formId}' enctype="multipart/form-data">
	</#if>
		<input type='hidden' id="id" name='id' value='${(item.id)!}'/>
		<fieldset style="margin-bottom: 15px;">
        <legend><@i18n 'att_flow_apply'/></legend>
		<table id="applyTable" class='tableStyle'>
			<tr style="display:none">
				<th>
					<label><@i18n 'att_overtime_type'/></label>
					<span class='required'>*</span>
				</th>
				<td>
					<@ZKUI.Combo empty="false" hideLabel="true" id="overtimeSign" name="overtimeSign" readonly="true" value="${(item.overtimeSign)!}" title="att_overtime_type" width="148">
						<option value="0"><@i18n 'att_overtime_normal'/></option>
						<option value="1"><@i18n 'att_overtime_rest'/></option>
						<option value="2"><@i18n 'att_shift_holidayOt'/></option>
					</@ZKUI.Combo>
				</td>
			</tr>
			<!-- 加班开始时间 -->
			<tr>
				<th>
					<label><@i18n 'common_startTime'/></label>
					<span class='required'>*</span>
				</th>
				<td><@ZKUI.Input id="attOverStartTimeId" type="datetime" value="${(item.startDatetime?string('yyyy-MM-dd HH:mm:ss'))!}" readonly="true" today="true" name="startDatetime" hideLabel="true" title="common_startTime" onHide="calOverTime"/></td>
			</tr>
			<tr>
				<!-- 加班结束时间 -->
				<th>
					<label><@i18n 'common_endTime'/></label>
					<span class='required'>*</span>
				</th>
				<td><@ZKUI.Input id="attOverEndTimeId" type="datetime" value="${(item.endDatetime?string('yyyy-MM-dd HH:mm:ss'))!}" readonly="true" today="true" name="endDatetime" hideLabel="true" title="common_endTime" onHide="calOverTime"/></td>
			</tr>
			<tr>
				<!-- 加班时长 -->
				<th>
					<label><@i18n 'att_overtime_overtimeHour'/></label>
					<span class='required'>*</span>
				</th>
				<td>
					<input name='overTimeHour' id="overTimeHour" readonly='true' maxlength="255" type='text' value='${(item.overTimeHour)!}' />
				</td>
			</tr>
			<#include '/att/flowable/approveContent.html' />
			<!-- 加班理由 -->
			<tr>
				<th>
					<label><@i18n 'common_remark'/></label>
				</th>
				<td>
					<textarea name='remark' maxlength="255" rows="4" cols="22" style="width: 148px; height: 63px;">${(item.remark)!}</textarea>
				</td>
			</tr>
		</table>
		</fieldset>
        <#include '/att/flowable/approveRecord.html'/>
	</form>
	<script type="text/javascript">
        var overtime = 0;
		$().ready(function() {
            $("#${formId}").validate({
                debug : true,
                <#if task.flowStatus == null>
                rules : {
                    'startDatetime' : {
                        required : true
                    },
                    'endDatetime' : {
                        required : true
                    },
					"remark" : {
						unInputChar: true
					}
                },
                </#if>
                submitHandler : function() {
                    <#if task.flowStatus == null>
                    var startTime = $("#${formId} input[name='startDatetime']").val();
                    var endTime = $("#${formId} input[name='endDatetime']").val();
                    var personId = "${personId}";
                    var personPin = "${personPin}";

                    var start_date = new Date(startTime.replace(/-/g, "/"));
                    var end_date = new Date(endTime.replace(/-/g, "/"));
                    var result = end_date.getTime() - start_date.getTime();
                    //开始时间与结束时间对比
                    if (startTime > endTime) {
                        //开始时间不能大于结束时间
                        messageBox({
                            messageType : "alert",
                            title : "<@i18n 'common_prompt_title'/>",
                            text : "<@i18n 'common_dsTime_timeValid4'/>"
                        });
                        return false;
                    }
                    else if(startTime == endTime){
                    	//结束时间不能等于开始时间
                    	messageBox({
                            messageType : "alert",
                            title : "<@i18n 'common_prompt_title'/>",
                            text : "<@i18n 'common_dsTime_timeValid1'/>"
                        });
                        return false;
                    }
                    else if (result > (24 * 3600 * 1000)) {
                        //开始时间不能大于结束时间
                        messageBox({
                            messageType : "alert",
                            title : "<@i18n 'common_prompt_title'/>",
                            text : "<@i18n 'att_overtime_notice'/>"
                        });
                        return false;
                    }

                    //判断相同时间内是否存在其他申请
                    $.ajax({
                        type : "post",
                        url : "attLeave.do?existApply",
                        data : {
                            personIds : personId,
                            startTime : startTime,
                            endTime : endTime,
                            exceptionType:"overtime"
                        },
                        dataType : "json",
                        async : false,
                        success : function(result) {
                            if (result.ret == "ok") {
                                <@submitHandler callBackFun="afterAddOvertime()"/>
                            } else {
                                var ptimeout = sysCfg.ptimeout;
                                openMessage(msgType.warning, result[sysCfg.msg], ptimeout);
                            }
                        }
                    });
                    <#else>
                    	<@submitHandler callBackFun="reloadPendingTaskGrid()"/>
                    </#if>
                }
            });
        });
        /**
         * 计算加班时长
         */
        function calOverTime(date) {
			setTimeout(function() {
				var startTime = $("#${formId} input[name='startDatetime']").val();
				var endTime = $("#${formId} input[name='endDatetime']").val();
				console.log("startTime", startTime);
				console.log("endTime", endTime);
				//开始时间与结束时间对比
				if (startTime > endTime) {
					//开始时间不能大于结束时间
					messageBox({
						messageType : "alert",
						title : "<@i18n 'common_prompt_title'/>",
						text : "<@i18n 'common_dsTime_timeValid4'/>"
					});
					return;
				}
				var overStartTime = new Date(startTime.replace(/-/g, "/"));
				var overEndTime = new Date(endTime.replace(/-/g, "/"));
				var s1 = overStartTime.getTime();
				var s2 = overEndTime.getTime();
				$("#overTimeHour").val(((s2 - s1) / 1000 / 60 / 60).toFixed(2) + ("<@i18n 'att_flowable_hour'/>"));
            },100);
        }

        var minDate = new Date();
        minDate.setMonth(minDate.getMonth()-2);
        minDate.setDate(1);
        ZKUI.Input.PULL.attOverStartTimeId.setMinDate(minDate);
        ZKUI.Input.PULL.attOverEndTimeId.setMinDate(minDate);

    </script>
	</#macro>