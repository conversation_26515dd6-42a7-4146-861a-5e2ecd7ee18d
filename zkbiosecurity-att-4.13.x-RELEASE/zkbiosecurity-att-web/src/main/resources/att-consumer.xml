<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd ">
    <dubbo:reference id="attAdjustService" interface="com.zkteco.zkbiosecurity.att.service.AttAdjustService"/>
    <!--<dubbo:reference id="attApplyService" interface="com.zkteco.zkbiosecurity.att.service.AttApplyService"/>
    <dubbo:reference id="attApproveService" interface="com.zkteco.zkbiosecurity.att.service.AttApproveService"/>-->
    <dubbo:reference id="attAreaPersonService" interface="com.zkteco.zkbiosecurity.att.service.AttAreaPersonService"/>
    <dubbo:reference id="attAutoExportService" interface="com.zkteco.zkbiosecurity.att.service.AttAutoExportService"/>
    <!--<dubbo:reference id="attBaseDataService" interface="com.zkteco.zkbiosecurity.att.service.AttBaseDataService"/>-->
    <dubbo:reference id="attCalculateService" interface="com.zkteco.zkbiosecurity.att.service.AttCalculateService"/>
    <dubbo:reference id="attClassService" interface="com.zkteco.zkbiosecurity.att.service.AttClassService"/>
    <!--<dubbo:reference id="attCommonSchService" interface="com.zkteco.zkbiosecurity.att.service.AttCommonSchService"/>-->
    <!--<dubbo:reference id="attDeptSchService" interface="com.zkteco.zkbiosecurity.att.service.AttDeptSchService"/>-->
    <dubbo:reference id="attDeviceService" interface="com.zkteco.zkbiosecurity.att.service.AttDeviceService"/>
    <dubbo:reference id="attExceptionDataService"
                     interface="com.zkteco.zkbiosecurity.att.service.AttExceptionDataService"/>
    <!--<dubbo:reference id="attFlowNodeService" interface="com.zkteco.zkbiosecurity.att.service.AttFlowNodeService" />
    <dubbo:reference id="attFlowService" interface="com.zkteco.zkbiosecurity.att.service.AttFlowService" />-->
    <!--<dubbo:reference id="attGroupSchService" interface="com.zkteco.zkbiosecurity.att.service.AttGroupSchService"/>-->
    <dubbo:reference id="attGroupService" interface="com.zkteco.zkbiosecurity.att.service.AttGroupService"/>
    <dubbo:reference id="attHolidayService" interface="com.zkteco.zkbiosecurity.att.service.AttHolidayService"/>
    <dubbo:reference id="attLeaveService" interface="com.zkteco.zkbiosecurity.att.service.AttLeaveService"/>
    <dubbo:reference id="attLeaveTypeService" interface="com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService"/>
    <!--<dubbo:reference id="attOutService" interface="com.zkteco.zkbiosecurity.att.service.AttOutService"/>-->
    <dubbo:reference id="attOvertimeService" interface="com.zkteco.zkbiosecurity.att.service.AttOvertimeService"/>
    <dubbo:reference id="attPersonSchService" interface="com.zkteco.zkbiosecurity.att.service.AttPersonSchService"/>
    <dubbo:reference id="attPersonService" interface="com.zkteco.zkbiosecurity.att.service.AttPersonService"/>
    <dubbo:reference id="attPointService" interface="com.zkteco.zkbiosecurity.att.service.AttPointService"/>
    <dubbo:reference id="attRecordService" interface="com.zkteco.zkbiosecurity.att.service.AttRecordService"/>
    <dubbo:reference id="attRuleService" interface="com.zkteco.zkbiosecurity.att.service.AttRuleService"/>
    <dubbo:reference id="attShiftService" interface="com.zkteco.zkbiosecurity.att.service.AttShiftService"/>
    <dubbo:reference id="attSignService" interface="com.zkteco.zkbiosecurity.att.service.AttSignService"/>
    <dubbo:reference id="attTempSchService" interface="com.zkteco.zkbiosecurity.att.service.AttTempSchService"/>
    <dubbo:reference id="attTimeSlotService" interface="com.zkteco.zkbiosecurity.att.service.AttTimeSlotService"/>
    <!--<dubbo:reference id="attTimingService" interface="com.zkteco.zkbiosecurity.att.service.AttTimingService" />-->
    <dubbo:reference id="attTransactionService" interface="com.zkteco.zkbiosecurity.att.service.AttTransactionService"/>
    <!--<dubbo:reference id="attTripService" interface="com.zkteco.zkbiosecurity.att.service.AttTripService"/>-->
</beans>