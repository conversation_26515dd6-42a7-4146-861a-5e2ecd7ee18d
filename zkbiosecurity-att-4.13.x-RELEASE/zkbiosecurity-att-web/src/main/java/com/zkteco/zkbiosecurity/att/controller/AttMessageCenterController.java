package com.zkteco.zkbiosecurity.att.controller;

import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.remote.AttMessageCenterRemote;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.base.controller.BaseController;

import java.util.Objects;

/**
 * 考勤事件中心
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 19:19 2021/8/19
 * @version v1.0
 */
@Controller
public class AttMessageCenterController extends BaseController implements AttMessageCenterRemote {

    @Autowired
    private AttLeaveService attLeaveService;
    @Autowired
    private AttOvertimeService attOvertimeService;
    @Autowired
    private AttSignService attSignService;
    @Autowired
    private AttAdjustService attAdjustService;
    @Autowired
    private AttClassService attClassService;
    @Autowired
    private PersPersonService persPersonService;

    @Override
    public ModelAndView info(String id, String type) {
        if (AttConstant.FLOW_TYPE_LEAVE.equals(type)) {
            AttLeaveItem item = attLeaveService.getItemById(id);
            if (item == null) {
                throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_ERROR,
                        I18nUtil.i18nCode("common_prompt_exception_datanoexists"));
            }
            String name = buildName(item.getPersonPin());
            item.setPersonName(name);
            request.setAttribute("item", item);
        } else if (AttConstant.FLOW_TYPE_SIGN.equals(type)) {
            AttSignItem item = attSignService.getItemById(id);
            if (item == null) {
                throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_ERROR,
                        I18nUtil.i18nCode("common_prompt_exception_datanoexists"));
            }
            String name = buildName(item.getPersonPin());
            item.setPersonName(name);
            request.setAttribute("item", item);
        } else if (AttConstant.FLOW_TYPE_OVERTIME.equals(type)) {
            AttOvertimeItem item = attOvertimeService.getItemById(id);
            if (item == null) {
                throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_ERROR,
                        I18nUtil.i18nCode("common_prompt_exception_datanoexists"));
            }
            String name = buildName(item.getPersonPin());
            item.setPersonName(name);
            request.setAttribute("item", item);
        } else if (AttConstant.FLOW_TYPE_ADJUST.equals(type)) {
            AttAdjustItem item = attAdjustService.getItemById(id);
            if (item == null) {
                throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_ERROR,
                        I18nUtil.i18nCode("common_prompt_exception_datanoexists"));
            }
            String name = buildName(item.getPersonPin());
            item.setPersonName(name);
            request.setAttribute("item", item);
        } else if (AttConstant.FLOW_TYPE_CLASS_SHIFT.equals(type)) {
            AttClassItem item = attClassService.getItemById(id);
            if (item == null) {
                throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_ERROR,
                        I18nUtil.i18nCode("common_prompt_exception_datanoexists"));
            }
            String adjustName = buildName(item.getAdjustPersonPin());
            item.setAdjustPersonName(adjustName);
            String swapName = buildName(item.getSwapPersonPin());
            item.setSwapPersonName(swapName);
            request.setAttribute("item", item);
        }
        return new ModelAndView("att/messageCenter/attMessageCenterInfo");
    }

    private String buildName(String pin) {

        PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
        if (Objects.isNull(persPersonItem)) {
            return "";
        }

        String personName = persPersonItem.getName();
        String lastName = persPersonItem.getLastName();
        if (StringUtils.isBlank(personName) && StringUtils.isBlank(lastName)) {
            return "";
        }

        String name = StringUtils.isNotBlank(personName) ? personName : "";
        if (StringUtils.isNotBlank(lastName)) {
            if (StringUtils.isNotBlank(name)) {
                name += (" " + lastName);
            } else {
                name = lastName;
            }
        }
        return name;
    }
}
