package com.zkteco.zkbiosecurity.att.api.controller;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.api.vo.AttApiTeamAbnormalItem;
import com.zkteco.zkbiosecurity.att.api.vo.AttApiTeamAttendanceStatusItem;
import com.zkteco.zkbiosecurity.att.api.vo.AttApiTeamSignPersonItem;
import com.zkteco.zkbiosecurity.att.service.AttTeamWorkFlowService;
import com.zkteco.zkbiosecurity.base.annotation.ApiPermissions;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

/**
 * 我的团队 接口查询相关
 */
@Controller
@RequestMapping(value = "/api/attTeam")
public class AttApiTeamController {

    @Autowired
    AttTeamWorkFlowService attTeamWorkFlowService;

    /**
     * 根据日期查找团队成员的休假信息列表
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/findTeamLeaveTask")
    @ResponseBody
    @Deprecated
    public ZKResultMsg findTeamLeaveTask(@RequestBody ZKMessage zkMessage) {
        String startDate = MapUtils.getString(zkMessage.getContent(), "startDate");
        String endDate = MapUtils.getString(zkMessage.getContent(), "endDate");
        String personPin = MapUtils.getString(zkMessage.getContent(), "personPin");
        Integer pageNo = MapUtils.getInteger(zkMessage.getContent(), "pageNo");
        Integer pageSize = MapUtils.getInteger(zkMessage.getContent(), "pageSize");

        Pager pager = attTeamWorkFlowService.findTeamLeaveTask(startDate, endDate, personPin, pageNo, pageSize);

        return new ZKResultMsg(pager);
    }

    /**
     * 查询团队当月申请加班的信息列表
     * 
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/findTeamOvertimeTask")
    @ResponseBody
    @Deprecated
    public ZKResultMsg findTeamOvertimeTask(@RequestBody ZKMessage zkMessage) {
        String month = MapUtils.getString(zkMessage.getContent(), "month");
        String personPin = MapUtils.getString(zkMessage.getContent(), "personPin");
        Integer pageNo = MapUtils.getInteger(zkMessage.getContent(), "pageNo");
        Integer pageSize = MapUtils.getInteger(zkMessage.getContent(), "pageSize");

        Pager pager = attTeamWorkFlowService.findTeamOvertimeTask(month, personPin, pageNo, pageSize);

        return new ZKResultMsg(pager);
    }

    /**
     * 按月统计所有成员当月的加班工时
     * 
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getTeamOvertimeHours")
    @ResponseBody
    @Deprecated
    public ZKResultMsg getTeamOvertimeHours(@RequestBody ZKMessage zkMessage) {
        String month = MapUtils.getString(zkMessage.getContent(), "month");
        String personPin = MapUtils.getString(zkMessage.getContent(), "personPin");

        String overtimeHours = attTeamWorkFlowService.getTeamOvertimeHours(month, personPin);

        return new ZKResultMsg(overtimeHours);
    }

    /**
     * 查询当月指定员工的加班申请信息列表
     * 
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/findPersonOvertimeTask")
    @ResponseBody
    @Deprecated
    public ZKResultMsg findPersonOvertimeTask(@RequestBody ZKMessage zkMessage) {
        String month = MapUtils.getString(zkMessage.getContent(), "month");
        String personPin = MapUtils.getString(zkMessage.getContent(), "personPin");
        Integer pageNo = MapUtils.getInteger(zkMessage.getContent(), "pageNo");
        Integer pageSize = MapUtils.getInteger(zkMessage.getContent(), "pageSize");

        Pager pager = attTeamWorkFlowService.findPersonOvertimeTask(month, personPin, pageNo, pageSize);

        return new ZKResultMsg(pager);
    }

    /**
     * 按月统计当月团队总的补签次数和各成员的补签次数
     * 
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getTeamSignTimes")
    @ResponseBody
    @Deprecated
    public ZKResultMsg getTeamSignTimes(@RequestBody ZKMessage zkMessage) {
        String month = MapUtils.getString(zkMessage.getContent(), "month");
        String personPin = MapUtils.getString(zkMessage.getContent(), "personPin");

        List<Map<String, Object>> list = attTeamWorkFlowService.getTeamSignTimes(month, personPin);

        return new ZKResultMsg(list);
    }

    /**
     * 根据成员查找当月的所有补签信息
     *
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/findPersonSignTask")
    @ResponseBody
    @Deprecated
    public ZKResultMsg findPersonSignTask(@RequestBody ZKMessage zkMessage) {

        String month = MapUtils.getString(zkMessage.getContent(), "month");
        String personPin = MapUtils.getString(zkMessage.getContent(), "personPin");
        Integer pageNo = MapUtils.getInteger(zkMessage.getContent(), "pageNo");
        Integer pageSize = MapUtils.getInteger(zkMessage.getContent(), "pageSize");

        Pager pager = attTeamWorkFlowService.findPersonSignTask(month, personPin, pageNo, pageSize);

        return new ZKResultMsg(pager);
    }

    /**
     * 获取团队成员pin号集合
     * 
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getTeamMemberPins")
    @ResponseBody
    public ZKResultMsg getTeamMemberPins(@RequestBody ZKMessage zkMessage) {
        String personPin = MapUtils.getString(zkMessage.getContent(), "personPin");

        List<String> teamMemberPins = attTeamWorkFlowService.getTeamMemberPins(personPin);

        return new ZKResultMsg(teamMemberPins);
    }

    /** =======================以下按原型接口====================== **/

    /**
     * 团队考勤（按状态）
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getTeamAttendanceStatusItem")
    @ResponseBody
    public ZKResultMsg getTeamAttendanceStatusItem(@RequestBody ZKMessage zkMessage) {
        Map<String, Object> content = zkMessage.getContent();
        String personPin = MapUtils.getString(content, "personPin");
        String deptCode = MapUtils.getString(content, "deptCode");
        String attDate = MapUtils.getString(content, "attDate");

        AttApiTeamAttendanceStatusItem item =
            attTeamWorkFlowService.getTeamAttendanceStatusItem(personPin, deptCode, attDate);

        return new ZKResultMsg(item);
    }

    /**
     * 团队考勤（按员工）
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getTeamAttendancePersonItem")
    @ResponseBody
    public ZKResultMsg getTeamAttendancePersonItem(@RequestBody ZKMessage zkMessage) {
        Map<String, Object> content = zkMessage.getContent();
        String personPin = MapUtils.getString(content, "personPin");
        String deptCode = MapUtils.getString(content, "deptCode");
        String attDate = MapUtils.getString(content, "attDate");
        Integer pageNo = MapUtils.getInteger(zkMessage.getContent(), "pageNo");
        Integer pageSize = MapUtils.getInteger(zkMessage.getContent(), "pageSize");

        Pager pager =
            attTeamWorkFlowService.getTeamAttendancePersonItem(personPin, deptCode, attDate, pageNo, pageSize);

        return new ZKResultMsg(JSONObject.toJSONString(pager));
    }

    /**
     * 漏卡明细
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getTeamLeakagePersonItem")
    @ResponseBody
    public ZKResultMsg getTeamLeakagePersonItem(@RequestBody ZKMessage zkMessage) {
        Map<String, Object> content = zkMessage.getContent();
        String personPin = MapUtils.getString(content, "personPin");
        String attDate = MapUtils.getString(content, "attDate");
        Integer pageNo = MapUtils.getInteger(zkMessage.getContent(), "pageNo");
        Integer pageSize = MapUtils.getInteger(zkMessage.getContent(), "pageSize");
        String attType = MapUtils.getString(zkMessage.getContent(), "attType");

        Pager pager = attTeamWorkFlowService.getTeamLeakagePersonItem(personPin, attDate, attType, pageNo, pageSize);

        return new ZKResultMsg(JSONObject.toJSONString(pager));
    }

    /**
     * 团队异常统计 休假/加班
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getTeamAbnormalItem")
    @ResponseBody
    public ZKResultMsg getTeamAbnormalItem(@RequestBody ZKMessage zkMessage) {
        Map<String, Object> content = zkMessage.getContent();
        String personPin = MapUtils.getString(content, "personPin");
        String attDate = MapUtils.getString(content, "attDate");
        String type = MapUtils.getString(content, "type");

        AttApiTeamAbnormalItem item = attTeamWorkFlowService.getTeamAbnormalItem(personPin, attDate, type);

        return new ZKResultMsg(item);
    }

    /**
     * 团队休假
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getTeamLeavePersonItem")
    @ResponseBody
    public ZKResultMsg getTeamLeavePersonItem(@RequestBody ZKMessage zkMessage) {
        Map<String, Object> content = zkMessage.getContent();
        String personPin = MapUtils.getString(content, "personPin");
        String attDate = MapUtils.getString(content, "attDate");
        Integer pageNo = MapUtils.getInteger(zkMessage.getContent(), "pageNo");
        Integer pageSize = MapUtils.getInteger(zkMessage.getContent(), "pageSize");

        Pager pager = attTeamWorkFlowService.getTeamLeavePersonItem(personPin, attDate, pageNo, pageSize);

        return new ZKResultMsg(JSONObject.toJSONString(pager));
    }

    /**
     * 团队加班（申请明细）
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getTeamOvertimePersonItem")
    @ResponseBody
    public ZKResultMsg getTeamOvertimePersonItem(@RequestBody ZKMessage zkMessage) {
        Map<String, Object> content = zkMessage.getContent();
        String personPin = MapUtils.getString(content, "personPin");
        String attDate = MapUtils.getString(content, "attDate");
        Integer pageNo = MapUtils.getInteger(zkMessage.getContent(), "pageNo");
        Integer pageSize = MapUtils.getInteger(zkMessage.getContent(), "pageSize");

        Pager pager = attTeamWorkFlowService.getTeamOvertimePersonItem(personPin, attDate, pageNo, pageSize);

        return new ZKResultMsg(JSONObject.toJSONString(pager));
    }

    /**
     * 团队加班（工时统计）
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getTeamOverTimeByTypePersonItem")
    @ResponseBody
    public ZKResultMsg getTeamOverTimeByTypePersonItem(@RequestBody ZKMessage zkMessage) {
        Map<String, Object> content = zkMessage.getContent();
        String personPin = MapUtils.getString(content, "personPin");
        String attDate = MapUtils.getString(content, "attDate");
        Integer pageNo = MapUtils.getInteger(zkMessage.getContent(), "pageNo");
        Integer pageSize = MapUtils.getInteger(zkMessage.getContent(), "pageSize");

        Pager pager = attTeamWorkFlowService.getTeamOverTimeByTypePersonItem(personPin, attDate, pageNo, pageSize);

        return new ZKResultMsg(pager);
    }

    /**
     * 团队补签（月详情）
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getTeamSignMonthItem")
    @ResponseBody
    public ZKResultMsg getTeamSignMonthItem(@RequestBody ZKMessage zkMessage) {
        Map<String, Object> content = zkMessage.getContent();
        String personPin = MapUtils.getString(content, "personPin");
        String month = MapUtils.getString(content, "attDate");
        Integer pageNo = MapUtils.getInteger(zkMessage.getContent(), "pageNo");
        Integer pageSize = MapUtils.getInteger(zkMessage.getContent(), "pageSize");

        Pager pager = attTeamWorkFlowService.getTeamSignMonthItem(personPin, month, pageNo, pageSize);

        return new ZKResultMsg(pager);
    }

    /**
     * 团队补签（月次数补签次数统计）
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getTeamSignCount")
    @ResponseBody
    public ZKResultMsg getTeamSignCount(@RequestBody ZKMessage zkMessage) {
        Map<String, Object> content = zkMessage.getContent();
        String personPin = MapUtils.getString(content, "personPin");
        String month = MapUtils.getString(content, "attDate");

        Integer count = attTeamWorkFlowService.getTeamSignCount(personPin, month);

        return new ZKResultMsg(count);
    }

    /**
     * 团队成员补签（月）
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getTeamSignPersonItem")
    @ResponseBody
    public ZKResultMsg getTeamSignPersonItem(@RequestBody ZKMessage zkMessage) {
        Map<String, Object> content = zkMessage.getContent();
        String personPin = MapUtils.getString(content, "personPin");
        String month = MapUtils.getString(content, "attDate");

        AttApiTeamSignPersonItem item = attTeamWorkFlowService.getTeamSignPersonItem(personPin, month);

        return new ZKResultMsg(item);
    }

    /**
     * 团队出差统计
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getTeamTripAbnormalItem")
    @ResponseBody
    public ZKResultMsg getTeamTripAbnormalItem(@RequestBody ZKMessage zkMessage) {
        Map<String, Object> content = zkMessage.getContent();
        String personPin = MapUtils.getString(content, "personPin");
        String attDate = MapUtils.getString(content, "attDate");
        String type = "trip";

        AttApiTeamAbnormalItem item = attTeamWorkFlowService.getTeamAbnormalItem(personPin, attDate, type);

        return new ZKResultMsg(item);
    }

    /**
     * 团队出差-分页列表
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getTeamTripPersonItem")
    @ResponseBody
    public ZKResultMsg getTeamTripPersonItem(@RequestBody ZKMessage zkMessage) {
        Map<String, Object> content = zkMessage.getContent();
        String personPin = MapUtils.getString(content, "personPin");
        String attDate = MapUtils.getString(content, "attDate");
        Integer pageNo = MapUtils.getInteger(zkMessage.getContent(), "pageNo");
        Integer pageSize = MapUtils.getInteger(zkMessage.getContent(), "pageSize");

        Pager pager = attTeamWorkFlowService.getTeamTripPersonItem(personPin, attDate, pageNo, pageSize);

        return new ZKResultMsg(JSONObject.toJSONString(pager));
    }

    /**
     * 团队外出统计
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getTeamOutAbnormalItem")
    @ResponseBody
    public ZKResultMsg getTeamOutAbnormalItem(@RequestBody ZKMessage zkMessage) {
        Map<String, Object> content = zkMessage.getContent();
        String personPin = MapUtils.getString(content, "personPin");
        String attDate = MapUtils.getString(content, "attDate");
        String type = "out";

        AttApiTeamAbnormalItem item = attTeamWorkFlowService.getTeamAbnormalItem(personPin, attDate, type);

        return new ZKResultMsg(item);
    }

    /**
     * 团队外出-分页列表
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getTeamOutPersonItem")
    @ResponseBody
    public ZKResultMsg getTeamOutPersonItem(@RequestBody ZKMessage zkMessage) {
        Map<String, Object> content = zkMessage.getContent();
        String personPin = MapUtils.getString(content, "personPin");
        String attDate = MapUtils.getString(content, "attDate");
        Integer pageNo = MapUtils.getInteger(zkMessage.getContent(), "pageNo");
        Integer pageSize = MapUtils.getInteger(zkMessage.getContent(), "pageSize");

        Pager pager = attTeamWorkFlowService.getTeamOutPersonItem(personPin, attDate, pageNo, pageSize);

        return new ZKResultMsg(JSONObject.toJSONString(pager));
    }

    /**
     * 团队考勤-按考勤结果类型查询人员
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getTeamAbnormalPersonItem")
    @ResponseBody
    public ZKResultMsg getTeamAbnormalPersonItem(@RequestBody ZKMessage zkMessage) {
        Map<String, Object> content = zkMessage.getContent();
        String personPin = MapUtils.getString(content, "personPin");
        String deptCode = MapUtils.getString(content, "deptCode");
        String attDate = MapUtils.getString(content, "attDate");
        String attType = MapUtils.getString(content, "attType");
        Integer pageNo = MapUtils.getInteger(zkMessage.getContent(), "pageNo");
        Integer pageSize = MapUtils.getInteger(zkMessage.getContent(), "pageSize");

        Pager pager =
            attTeamWorkFlowService.getTeamAbnormalPersonItem(personPin, deptCode, attDate, attType, pageNo, pageSize);

        return new ZKResultMsg(JSONObject.toJSONString(pager));
    }
}
