package com.zkteco.zkbiosecurity.att.controller;

import com.zkteco.zkbiosecurity.att.constants.AttCommonSchConstant;
import com.zkteco.zkbiosecurity.att.remote.AttPersonSchRemote;
import com.zkteco.zkbiosecurity.att.service.AttGroupService;
import com.zkteco.zkbiosecurity.att.service.AttPersonSchService;
import com.zkteco.zkbiosecurity.att.vo.AttGroupItem;
import com.zkteco.zkbiosecurity.att.vo.AttAllPersonSchItem;
import com.zkteco.zkbiosecurity.att.vo.AttPersonSchItem;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.Tree;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.LocaleMessageSourceUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 人员排班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:39
 * @since 1.0.0
 */
@Controller
public class AttPersonSchController extends ExportController implements AttPersonSchRemote {

    @Autowired
    private AttPersonSchService attPersonSchService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AttGroupService attGroupService;

    @RequiresPermissions("att:personsch")
    @Override
    public ModelAndView index() {
        return new ModelAndView("att/personSch/attPersonSch");
    }

    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("item", attPersonSchService.getItemById(id));
        }
        return new ModelAndView("att/personSch/editAttPersonSch");
    }

    @Override
    public ZKResultMsg save(AttPersonSchItem item) {
        ZKResultMsg res = new ZKResultMsg();
        attPersonSchService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @Override
    public DxGrid list(AttAllPersonSchItem condition) {
        String sessionId = request.getSession().getId();
        Pager pager = attPersonSchService.getAllPersonSch(sessionId, condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public ZKResultMsg delete(String ids) {
        attPersonSchService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public ZKResultMsg getAttShiftSchJsonByDate() {
        String schId = request.getParameter("schId");
        String dateSelected = request.getParameter("dateSelected");
        String personId = null;
        // 这边需要根据数据进行划分
        if (StringUtils.isNotBlank(schId)) {
            String[] splits = schId.split("_");
            if ("pers".equals(splits[0])) {
                personId = splits[1];
            }
        }

        if (StringUtils.isNotBlank(dateSelected)) {
            return attPersonSchService.getAttPersonSchJson(personId, dateSelected);
        }
        return null;
    }

    @Override
    public ModelAndView attSelectPersonContent() {
        return new ModelAndView("att/personSch/attSelectPersonContent");
    }

    @Override
    public ModelAndView cycleSch(Short type, String ids) {
        setPersOrDeptInfo(type, ids);
        return new ModelAndView("att/personSch/editAttCycleSch");
    }

    @Override
    public ModelAndView tempSch(Short type, String ids) {
        setPersOrDeptInfo(type, ids);
        return new ModelAndView("att/personSch/editAttTempSch");
    }

    @Override
    public ModelAndView info(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("id", id);
        }
        return new ModelAndView("att/personSch/attPersonSchInfo");
    }

    @LogRequest(module = "att_module", object = "att_leftMenu_personSch", opType = "common_op_export",
        opContent = "common_op_export")
    @RequiresPermissions("att:personsch:export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AttAllPersonSchItem attAllPersonSchItem = new AttAllPersonSchItem();
        String sessionId = request.getSession().getId();

        setConditionValue(attAllPersonSchItem);

        String queryCondition = request.getParameter("queryConditions");
        String[] conditionAry = queryCondition.replaceAll("%20", " ").split("&");
        for (String condition : conditionAry) {
            String[] strAry = condition.split("=", 2);
            String fieldName = strAry[0];
            String fieldValue = strAry[1];
            if ("startDate".equals(fieldName)) {
                attAllPersonSchItem.setStartDate(DateUtil.stringToDate(fieldValue, DateUtil.DateStyle.YYYY_MM_DD));
            } else if ("endDate".equals(fieldName)) {
                attAllPersonSchItem.setEndDate(DateUtil.stringToDate(fieldValue, DateUtil.DateStyle.YYYY_MM_DD));
            }
        }

        Pager pager = attPersonSchService.getAllPersonSch(sessionId, attAllPersonSchItem, getBeginIndex(), getEndIndex() + 1);
        List<AttPersonSchItem> itemList = (List<AttPersonSchItem>)pager.getData();
        excelExport(itemList, AttPersonSchItem.class);
    }

    /**
     * 前端选择人员或者部门ids，组装显示名称
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/4/21 14:15
     * @param type
     *            分组0/部门1/人员2
     * @param ids
     * @return void
     */
    private void setPersOrDeptInfo(Short type, String ids) {
        /*StringBuffer showInfo = new StringBuffer("");
        if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(type)) {
            List<PersPersonItem> persPersonItemList =
                persPersonService.getSimpleItemsByIds(Arrays.asList(ids.split(",")));

            for (PersPersonItem persPersonItem : persPersonItemList) {
                showInfo.append(persPersonItem.getName());
                if (StringUtils.isNotBlank(persPersonItem.getLastName())
                    && !"zh_CN".equals(LocaleMessageSourceUtil.language)) {
                    showInfo.append(persPersonItem.getLastName());
                }
                showInfo.append("(").append(persPersonItem.getPin()).append(")");
                showInfo.append(";");
            }
        } else if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(type)) {
            List<AuthDepartmentItem> authDepartmentItemList = authDepartmentService.getItemsByIds(ids);
            for (AuthDepartmentItem authDepartmentItem : authDepartmentItemList) {
                showInfo.append(authDepartmentItem.getName()).append(";");
            }
        } else if (AttCommonSchConstant.SCH_TYPE_GROUP.equals(type)) {
            List<AttGroupItem> attGroupSchItemList = attGroupService.getItemsByIds(ids);
            for (AttGroupItem attGroupItem : attGroupSchItemList) {
                showInfo.append(attGroupItem.getGroupName()).append(";");
            }
        }
        showInfo.deleteCharAt(showInfo.length() - 1);
        request.setAttribute("showInfo", showInfo.toString());*/
        request.setAttribute("type", type);
        request.setAttribute("ids", ids);
    }
}