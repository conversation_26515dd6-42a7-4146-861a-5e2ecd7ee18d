package com.zkteco.zkbiosecurity.att.controller;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.att.bean.AttCalculationParamsBean;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.bean.AttRuleParamBean;
import com.zkteco.zkbiosecurity.att.calc.bo.AttLeaveBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttOvertimeBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.remote.AttManualCalculationRemote;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.Att4PersPersonItem;
import com.zkteco.zkbiosecurity.att.vo.AttManualCalculationItem;
import com.zkteco.zkbiosecurity.att.vo.AttShiftItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 考勤计算
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:16
 * @since 1.0.0
 */
@Controller
public class AttManualCalculationController extends BaseController implements AttManualCalculationRemote {

    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttCalculateService attCalculateService;
    @Autowired
    private Att4PersPersonService att4PersPersonService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttHolidayService attHolidayService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;
    @Autowired
    private AttShiftService attShiftService;
    @Autowired
    private AttTimeSlotService attTimeSlotService;
    @Autowired
    private AttSignService attSignService;
    @Autowired
    private AttLeaveService attLeaveService;
    @Autowired
    private AttOvertimeService attOvertimeService;
    @Autowired
    private AttAdjustService attAdjustService;
    @Autowired
    private AttClassService attClassService;
    @Autowired
    private AttRecordService attRecordService;

    @RequiresPermissions("att:manualCalculation")
    @Override
    public ModelAndView index() {
        return new ModelAndView("att/manualCalculation/attManualCalculation");
    }

    @RequiresPermissions("att:manualCalculation:refresh")
    @Override
    public DxGrid list(AttManualCalculationItem condition) {
        Pager pager = attPersonService.getItemsByPageForAttCal(request.getSession().getId(), condition, getPageNo(),
            getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public ZKResultMsg calculating() {
        ZKResultMsg res = new ZKResultMsg();
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("att:manualCalculation:calculate")
    @LogRequest(module = "att_module", object = "att_op_calculation", opType = "att_op_calculation",
        requestParams = {"pins"}, opContent = "att_person_pin")
    @Override
    public void calculate(String personIds, String deptIds, String startDateStr, String endDateStr, String pins,
        String totalLeaver, String isIncludeLower) {
        try {

            /** 开始 */
            progressCache.beginProcess(ProcessBean.createNormalContent(I18nUtil.i18nCode("common_op_startProcessing")));
            int currentProgress = 0;
            int totalProgress = 0;

            /** 准备过滤条件 */
            Date startDate = AttDateUtils.getMinOfDay(startDateStr);
            Date endDate = AttDateUtils.getMaxOfDay(endDateStr);

            // 离职人员是否考勤
            boolean attendanceLeaver = ConstUtil.SYSTEM_COMMON_TRUE.equals(totalLeaver) ? true : false;
            // 选择按部门考勤且勾选包含子部门
            if (StringUtils.isNotBlank(deptIds) && ConstUtil.SYSTEM_COMMON_TRUE.equals(isIncludeLower)) {
                List<String> authDeptIdAry = attPersonService.getAllChildDeptById(deptIds);
                deptIds = StringUtils.join(authDeptIdAry.toArray(), ",");
            }

            // 把查询的人、部门转为人员pin号的集合
            List<String> pinList = attPersonService.filterPinList(personIds, deptIds, startDate, attendanceLeaver);
            int personTotal = pinList.size();
            log.info("Manual calculate personTotal = {}", personTotal);
            progressCache.setProcess(
                new ProcessBean(10, totalProgress += 10, I18nUtil.i18nCode("att_statistical_filter") + "<br/>"));

            /** 初始化基础数据 */
            // 加载规则 参数map换成AttRuleParamBean
            AttRuleParamBean attRuleParamBean = attParamService.getRuleParam();

            // 节假日集合 set(date)
            Set<String> holidaySet = attHolidayService.getHolidayDateSet(startDate, endDate);

            // 获取全部班次集合(ID, Item)
            List<AttShiftItem> attShiftItems = attShiftService.getByCondition(new AttShiftItem());
            Map<String, AttShiftItem> attShiftItemMap =
                attShiftItems.stream().collect(Collectors.toMap(AttShiftItem::getId, Function.identity()));

            // 获取所有时间段集合(ID, Item)
            List<AttTimeSlotItem> attTimeSlotItems = attTimeSlotService.getAllTimeSlotItem();
            Map<String, AttTimeSlotItem> attTimeSlotItemMap =
                attTimeSlotItems.stream().collect(Collectors.toMap(AttTimeSlotItem::getId, Function.identity()));

            progressCache.setProcess(
                new ProcessBean(10, totalProgress += 10, I18nUtil.i18nCode("att_statistical_initData") + "<br/>"));

            // 防止补签、请假、加班跨天漏取记录
            Date dateBefore = DateUtil.getDateBefore(startDate, 2);
            Date dateAfter = DateUtil.getDateAfter(endDate, 2);
            // 请假记录集合 Map(pin=date, List<AttLeaveBO>)
            Map<String, List<AttLeaveBO>> attLeaveMap = attLeaveService.getLeaveMap(null, dateBefore, dateAfter);
            // 加班记录集合 Map(pin=date, List<AttOvertimeBO>)
            Map<String, List<AttOvertimeBO>> attOvertimeMap =
                attOvertimeService.getOvertimeMap(null, dateBefore, dateAfter);

            // 调休补班 Map(pin=date, List<AttPersonSchBO>)
            Map<String, List<AttPersonSchBO>> attAdjustMap = attAdjustService.getAdjustMap(null, startDate, endDate);

            // 调班 Map(pin=date, List<AttPersonSchBO>)
            Map<String, List<AttPersonSchBO>> attClassMap = attClassService.getClassMap(null, startDate, endDate);

            progressCache.setProcess(
                new ProcessBean(10, totalProgress += 10, I18nUtil.i18nCode("att_statistical_exception") + "<br/>"));

            progressCache
                .setProcess(new ProcessBean(0, totalProgress, I18nUtil.i18nCode("att_statistical_begin") + "<br/>"));
            int remainProgress = 100 - totalProgress;
            int batchNum = personTotal % AttCalculationConstant.NUMBER == 0
                ? personTotal / AttCalculationConstant.NUMBER : (personTotal / AttCalculationConstant.NUMBER + 1);
            int batchIndex = 0;

            // 计算线程数
            int threadCount = Runtime.getRuntime().availableProcessors() / 2 + 1;
            List<CompletableFuture> completableFutureList = new ArrayList<>(threadCount);
            log.info("Manual calculate threadCount = {}", threadCount);

            // 循环批次获取数据计算
            for (int fromIndex = 0; fromIndex < personTotal; fromIndex = fromIndex + AttCalculationConstant.NUMBER) {
                int toIndex = Math.min(personTotal, fromIndex + AttCalculationConstant.NUMBER);
                log.info("Manual calculate fromIndex = {} -> toIndex = {},", fromIndex, toIndex);

                if (completableFutureList.size() >= threadCount) {
                    // 线程已满,等待有线程执行结束,只要一个退出即可
                    CompletableFuture
                        .anyOf(completableFutureList.toArray(new CompletableFuture[completableFutureList.size()]))
                        .join();
                    // 判断 谁完成了 完成了的删除掉list 移除
                    Iterator<CompletableFuture> iterator = completableFutureList.iterator();
                    while (iterator.hasNext()) {
                        CompletableFuture next = iterator.next();
                        if (next.isDone()) {
                            iterator.remove();

                            batchIndex++;
                            currentProgress = 100 * batchIndex / batchNum;
                            totalProgress = 100 - remainProgress + (remainProgress * batchIndex / batchNum);
                            progressCache.setProcess(new ProcessBean(currentProgress, totalProgress,
                                I18nUtil.i18nCode("att_op_calculation") + totalProgress + "%...<br/>"));
                        }
                    }
                }

                List<String> subPinList = pinList.subList(fromIndex, toIndex);

                // 参数组装
                AttCalculationParamsBean calculationParamsBean = new AttCalculationParamsBean();
                calculationParamsBean.setPins(subPinList).setStartDate(startDate).setEndDate(endDate)
                        .setAttLeaveMap(attLeaveMap).setAttOvertimeMap(attOvertimeMap).setAttAdjustMap(attAdjustMap)
                        .setAttClassMap(attClassMap).setAttRuleParamBean(attRuleParamBean).setAttHolidaySet(holidaySet)
                        .setAttShiftItemMap(attShiftItemMap).setAttTimeSlotItemMap(attTimeSlotItemMap);
                CompletableFuture completableFuture = attCalculateService.calculateBatchPerson(calculationParamsBean);

                completableFutureList.add(completableFuture);
            }

            // 等待剩下计算线程全部结束
            while (true) {
                if (completableFutureList.isEmpty()) {
                    break;
                }
                Iterator<CompletableFuture> iterator = completableFutureList.iterator();
                while (iterator.hasNext()) {
                    CompletableFuture next = iterator.next();
                    // 循环判断线程是否结束,结束则更新进度
                    if (next.isDone()) {
                        iterator.remove();

                        batchIndex++;
                        currentProgress = 100 * batchIndex / batchNum;
                        totalProgress = 100 - remainProgress + (remainProgress * batchIndex / batchNum);
                        progressCache.setProcess(new ProcessBean(currentProgress, totalProgress,
                            I18nUtil.i18nCode("att_op_calculation") + totalProgress + "%...<br/>"));
                    }
                }
                try {
                    Thread.sleep(500L);
                } catch (InterruptedException e) {

                }
            }

            log.info("Manual calculate finish");
            progressCache.setProcess(new ProcessBean(100, 100, I18nUtil.i18nCode("att_statistical_end") + "<br/>"));
            progressCache.finishProcess(ProcessBean.createNormalContent(I18nUtil.i18nCode("common_op_succeed")));
        } catch (Exception e) {
            log.error("Manual calculate error", e);
            progressCache
                .setProcess(new ProcessBean(AttCalculationConstant.FULL_PROGRESS, AttCalculationConstant.FULL_PROGRESS,
                    "<span style='color:red;'>" + I18nUtil.i18nCode("att_statistical_error") + "<span><br/>"));
            progressCache.finishProcess(I18nUtil.i18nCode("att_statistical_end"));
        }
    }

    @Override
    public String getDeptPins(String deptIds) {
        JSONObject jsonObject = new JSONObject();
        List<String> dptIds = Arrays.asList(deptIds.split(","));
        List<Att4PersPersonItem> persPersonItems = att4PersPersonService.getPersPersonByDeptIds(dptIds);
        StringBuilder pins = new StringBuilder();
        StringBuilder persIds = new StringBuilder();
        int index = 1;
        for (Att4PersPersonItem persPersonItem : persPersonItems) {
            if (index != persPersonItems.size()) {
                pins.append(persPersonItem.getPersonPin() + ",");
                persIds.append(persPersonItem.getPersonId() + ",");
            } else {
                pins.append(persPersonItem.getPersonPin());
                persIds.append(persPersonItem.getPersonId());
            }
            index++;
        }
        jsonObject.put("pins", pins);
        jsonObject.put("persIds", persIds);
        return jsonObject.toJSONString();
    }

}
