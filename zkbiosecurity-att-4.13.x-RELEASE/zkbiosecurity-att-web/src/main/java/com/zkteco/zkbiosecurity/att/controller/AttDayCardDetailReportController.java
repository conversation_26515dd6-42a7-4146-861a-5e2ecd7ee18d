package com.zkteco.zkbiosecurity.att.controller;

import com.zkteco.zkbiosecurity.att.remote.AttDayCardDetailReportRemote;
import com.zkteco.zkbiosecurity.att.service.AttDayCardDetailService;
import com.zkteco.zkbiosecurity.att.service.AttTransactionService;
import com.zkteco.zkbiosecurity.att.vo.AttDayCardDetailItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 日打卡详情
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 18:27 2020/6/28
 */
@Controller
public class AttDayCardDetailReportController extends ExportController implements AttDayCardDetailReportRemote {

    @Autowired
    private AttTransactionService attTransactionService;

    @Autowired
    private AttDayCardDetailService attDayCardDetailService;

    @RequiresPermissions("att:dayCardDetailReport")
    @Override
    public ModelAndView index() {
        return new ModelAndView("att/report/attDayCardDetailReport");
    }

    @RequiresPermissions("att:dayCardDetailReport:refresh")
    @Override
    public DxGrid list(AttDayCardDetailItem condition) {
        Pager pager = attDayCardDetailService.getItemsByPageByAuthFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:dayCardDetailReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_dayCardDetailReport", opType = "common_op_export",
        requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AttDayCardDetailItem attDayCardDetailItem = new AttDayCardDetailItem();
        setConditionValue(attDayCardDetailItem);
        List<AttDayCardDetailItem> intemList = attDayCardDetailService.exportItemListByAuthFilter(
            request.getSession().getId(), attDayCardDetailItem, getBeginIndex(), getEndIndex());
        excelExport(intemList, AttDayCardDetailItem.class);
    }
}
