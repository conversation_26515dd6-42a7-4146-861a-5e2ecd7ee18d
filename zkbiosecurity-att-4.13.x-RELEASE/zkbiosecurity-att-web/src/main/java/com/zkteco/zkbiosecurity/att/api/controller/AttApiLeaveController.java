package com.zkteco.zkbiosecurity.att.api.controller;

import com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.base.annotation.ApiPermissions;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2019/6/11 11:31
 */
@RestController
@RequestMapping(value = {"/api/attLeave"})
public class AttApiLeaveController {

    @Autowired
    private AttLeaveTypeService attLeaveTypeService;

    /**
     * 获取考勤假类
     * 
     * @auther lambert.li
     * @date 2019/6/20 8:32
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping(path = "/getLeaveTypeList", method = RequestMethod.POST, produces = "application/json")
    public ZKResultMsg getLeaveTypeList(@RequestBody ZKMessage zkMessage) {
        List<AttLeaveTypeItem> attLeaveTypeItems = attLeaveTypeService.listLeaveTypeFilterTripAndOut();
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        resultMsg.setData(attLeaveTypeItems);
        return I18nUtil.i18nMsg(resultMsg);
    }
}
