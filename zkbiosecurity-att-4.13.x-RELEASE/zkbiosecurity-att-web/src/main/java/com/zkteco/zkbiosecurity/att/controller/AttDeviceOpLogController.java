package com.zkteco.zkbiosecurity.att.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.remote.AttDeviceOpLogRemote;
import com.zkteco.zkbiosecurity.att.service.AttDeviceOpLogService;
import com.zkteco.zkbiosecurity.att.vo.AttDeviceOpLogItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 设备操作日志
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:26
 * @since 1.0.0
 */
@Controller
public class AttDeviceOpLogController extends ExportController implements AttDeviceOpLogRemote {

    @Autowired
    private AttDeviceOpLogService attDeviceOpLogService;

    @Override
    @RequiresPermissions("att:deviceoplog")
    public ModelAndView index() {
        return new ModelAndView("att/deviceoplog/attDeviceOpLog");
    }

    @Override
    @RequiresPermissions("att:deviceoplog:refresh")
    public DxGrid list(AttDeviceOpLogItem condition) {
        Pager pager = attDeviceOpLogService.getItemsByAuthUserPage(request.getSession().getId(), condition, getPageNo(),
            getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    @RequiresPermissions("att:deviceoplog:export")
    public void export(HttpServletRequest req, HttpServletResponse resp) {
        AttDeviceOpLogItem attDeviceOpLogItem = new AttDeviceOpLogItem();
        setConditionValue(attDeviceOpLogItem);
        List<AttDeviceOpLogItem> list = attDeviceOpLogService
            .getItemData(AttDeviceOpLogItem.class, attDeviceOpLogItem, getBeginIndex(), getEndIndex());
        excelExport(list, AttDeviceOpLogItem.class);
    }
}