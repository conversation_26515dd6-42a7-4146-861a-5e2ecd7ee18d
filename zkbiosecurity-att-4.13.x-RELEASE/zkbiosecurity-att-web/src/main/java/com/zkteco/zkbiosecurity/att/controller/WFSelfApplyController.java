package com.zkteco.zkbiosecurity.att.controller;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.remote.AttWfApplyRemote;
import com.zkteco.zkbiosecurity.att.service.AttFlowableService;
import com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService;
import com.zkteco.zkbiosecurity.att.service.AttPersonService;
import com.zkteco.zkbiosecurity.att.vo.AttPersonItem;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;

/**
 * 异常申请、查看（员工自助）
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 15:54
 * @since 1.0.0
 */
@Controller
public class WFSelfApplyController extends BaseController implements AttWfApplyRemote {

    @Autowired
    private AttFlowableService attFlowableService;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private HttpServletRequest request = null;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;

    @Override
    public ModelAndView apply(String flowType) {
        SecuritySubject securitySubject = getCurrentSubject();
        AttPersonItem attPersonItem = attPersonService.getItemByPersonPin(securitySubject.getUserCode());
        String personId = "-1";
        String personPin = "";
        if (Objects.nonNull(attPersonItem)) {
            personId = attPersonItem.getPersonId();
            personPin = attPersonItem.getPersonPin();
        }
        if (personId != null && request.getAttribute("task") == null) {
            Map<String, String> firstNotifier = attFlowableService.apply(personId, flowType);

            Map<String, Object> task = new HashMap<>();
            task.put("notifierPers", firstNotifier);
            request.setAttribute("task", task);
        }
        request.setAttribute("personId", personId);
        request.setAttribute("personPin", personPin);
        // 出差假种的id
        request.setAttribute("leaveTypeTripId",
            attLeaveTypeService.getItemByLeaveTypeNo(AttCalculationConstant.AttAttendStatus.TRIP).getId());
        // 外出假种的id
        request.setAttribute("leaveTypeOutId",
            attLeaveTypeService.getItemByLeaveTypeNo(AttCalculationConstant.AttAttendStatus.OUT).getId());
        request.setAttribute("flowType", flowType);// 流程数据
        return new ModelAndView("att/flowable/" + flowType + "/editAtt" + StrUtil.toUpperCase(flowType.substring(0, 1))
            + flowType.substring(1));
    }

    @Override
    public ModelAndView approve(String taskId) {
        Map resMap = attFlowableService.approve(taskId);
        request.setAttribute("item", resMap.get("item"));// 业务数据
        request.setAttribute("task", resMap.get("task"));// 流程数据
        return apply(resMap.get("flowType").toString());
    }

    @Override
    public ModelAndView detail(String businessKey) {
        Map resMap = attFlowableService.detail(businessKey);
        request.setAttribute("item", resMap.get("item"));// 业务数据
        request.setAttribute("task", resMap.get("task"));// 流程数据
        request.setAttribute("detail", "readonly");
        return apply(resMap.get("flowType").toString());
    }

    @Override
    public ModelAndView cancelApply(String businessKey) {
        request.setAttribute("businessKey", businessKey);
        return new ModelAndView("/att/flowable/cancelApply");
    }

    @Override
    public ZKResultMsg validApplyFlow(String flowType) {
        try {
            SecuritySubject securitySubject = getCurrentSubject();
            AttPersonItem attPersonItem = attPersonService.getItemByPersonPin(securitySubject.getUserCode());
            if (Objects.nonNull(attPersonItem)) {
                attFlowableService.apply(attPersonItem.getPersonId(), flowType);
            }
        } catch (Exception e) {
            return ZKResultMsg.getFailMsg(e.getMessage());
        }
        return ZKResultMsg.successMsg();
    }
}
