package com.zkteco.zkbiosecurity.att.controller;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.zkteco.zkbiosecurity.att.util.AttExcelUtil;
import com.zkteco.zkbiosecurity.att.util.AttImportWithRowNumUtil;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.constants.AttCommonSchConstant;
import com.zkteco.zkbiosecurity.att.remote.AttCycleSchRemote;
import com.zkteco.zkbiosecurity.att.service.AttCycleSchService;
import com.zkteco.zkbiosecurity.att.service.AttGroupService;
import com.zkteco.zkbiosecurity.att.vo.AttCycleSchItem;
import com.zkteco.zkbiosecurity.att.vo.AttGroupItem;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.annotation.LogChangeRequest;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 周期排班（分组/部门/人员）
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/5/6 10:52
 */
@Controller
public class AttCycleSchController extends ExportController implements AttCycleSchRemote {

    @Autowired
    private AttCycleSchService attCycleSchService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AttGroupService attGroupService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private AttExcelUtil attExcelUtil;

    @Override
    public DxGrid list(AttCycleSchItem condition) {
        Pager pager = attCycleSchService.loadPagerByAuthUserFilter(request.getSession().getId(), condition, getPageNo(),
            getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            AttCycleSchItem attCycleSchItem = attCycleSchService.getItemById(id);
            request.setAttribute("item", attCycleSchItem);
            request.setAttribute("type", attCycleSchItem.getCycleType());
            // 根据排班类型设置人员名称或者部门名称
            // setShowInfo(attCycleSchItem);
        }
        return new ModelAndView("att/personSch/editAttCycleSch");
    }

    /**
     * 根据排班类型设置人员名称或者部门名称
     * 
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/27 19:57
     * @param attCycleSchItem
     * @return void
     */
    /*private void setShowInfo(AttCycleSchItem attCycleSchItem) {
        if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(attCycleSchItem.getCycleType())) {
            PersPersonItem personItem = persPersonService.getSimpleItemById(attCycleSchItem.getPersonId());
            if (personItem != null) {
                request.setAttribute("showInfo", personItem.getName() + "(" + personItem.getPin() + ")");
            }
        } else if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(attCycleSchItem.getCycleType())) {
            AuthDepartmentItem authDepartmentItem = authDepartmentService.getItemById(attCycleSchItem.getDeptId());
            request.setAttribute("showInfo", authDepartmentItem.getName());
        } else if (AttCommonSchConstant.SCH_TYPE_GROUP.equals(attCycleSchItem.getCycleType())) {
            AttGroupItem attGroupItem = attGroupService.getItemById(attCycleSchItem.getGroupId());
            request.setAttribute("showInfo", attGroupItem.getGroupName());
        }
    }*/

    @RequiresPermissions("att:personsch:cycleSch")
    @LogChangeRequest(module = "att_module", object = "att_personSch_sch", opType = "att_personSch_cycleSch",
        vo = AttCycleSchItem.class, service = AttCycleSchService.class)
    @Override
    public ZKResultMsg save(AttCycleSchItem item) {
        String addIds = request.getParameter("addIds");
        if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(item.getCycleType())) {
            addIds = includeLowerDept(addIds);
        }
        attCycleSchService.saveItem(addIds, item);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("att:schDetails:del")
    @LogRequest(module = "att_module", object = "att_leftMenu_schDetails", opType = "att_personSch_delSch",
        requestParams = {"names"}, opContent = "att_personSch_delSch")
    @Override
    public ZKResultMsg delete(String ids) {
        attCycleSchService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("att:personsch:cleanCycleSch")
    @LogRequest(module = "att_module", object = "att_personSch_sch", opType = "att_personSch_cleanCycleSch",
        requestParams = {"names"}, opContent = "att_personSch_cleanCycleSch")
    @Override
    public ZKResultMsg delCycleSch(String ids, Short type, Date startDate, Date endDate) {
        if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(type)) {
            ids = includeLowerDept(ids);
        }
        attCycleSchService.delCycleSch(ids, type, startDate, endDate);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    /**
     * 
     * 是否包含下级,解决前端部门树懒加载 包含子部门没有成功获取,需要后台递归实现
     * 
     * @param ids:
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021-06-11 16:28
     * @since 1.0.0
     */
    private String includeLowerDept(String ids) {
        String includeLower = request.getParameter("includeLower");
        if ("true".equals(includeLower)) {
            List<AuthDepartmentItem> departmentItems = authDepartmentService.getChildDeptItemsByDeptIds(ids);
            ids = Optional.ofNullable(departmentItems).filter(l -> !l.isEmpty())
                .map(l -> l.stream().map(AuthDepartmentItem::getId).filter(Objects::nonNull).map(String::valueOf)
                    .distinct().collect(Collectors.joining(",")))
                .orElse(null);

        }
        return ids;
    }

    @RequiresPermissions("att:schDetails:export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AttCycleSchItem attCycleSchItem = new AttCycleSchItem();
        setConditionValue(attCycleSchItem);

        String queryCondition = request.getParameter("queryConditions");
        String[] conditionAry = queryCondition.replaceAll("%20", " ").split("&");
        for (String condition : conditionAry) {
            if (StringUtils.isNotBlank(condition)) {
                String[] strAry = condition.split("=", 2);
                String fieldName = strAry[0];
                String fieldValue = strAry[1];
                if ("startTime".equals(fieldName)) {
                    attCycleSchItem.setStartTime(DateUtil.stringToDate(fieldValue, DateUtil.DateStyle.YYYY_MM_DD));
                } else if ("endTime".equals(fieldName)) {
                    attCycleSchItem.setEndTime(DateUtil.stringToDate(fieldValue, DateUtil.DateStyle.YYYY_MM_DD));
                }
            }
        }

        List<AttCycleSchItem> attCycleSchItemList = attCycleSchService.getItemData(request.getSession().getId(),
            attCycleSchItem, getBeginIndex(), getEndIndex());
        excelExport(attCycleSchItemList, AttCycleSchItem.class);
    }

    @RequiresPermissions("att:personsch:exportCycSchTemplate")
    @Override
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        List<AttCycleSchItem> attCycleSchItemList = new ArrayList<>();
        AttCycleSchItem attCycleSchItem = new AttCycleSchItem();
        attCycleSchItemList.add(attCycleSchItem);
        excelExport(attCycleSchItemList, AttCycleSchItem.class, false);
    }

    @RequiresPermissions("att:personsch:importCycSch")
    @Override
    public ZKResultMsg importExcel(MultipartFile upload) throws IOException {
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            progressCache.setProcess(
                    new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_uploadFileSuccess") + "<br/>"));
            List<AttCycleSchItem> itemList =
                    AttImportWithRowNumUtil.excelImport(upload.getInputStream(), AttCycleSchItem.class);
            progress += 10;
            progressCache.setProcess(
                    new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_resolutionComplete") + "<br/>"));
            return I18nUtil.i18nMsg(attCycleSchService.importItem(itemList));
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                    "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                        + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                        new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("Import Person Info Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
    }
}