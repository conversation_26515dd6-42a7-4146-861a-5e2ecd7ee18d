package com.zkteco.zkbiosecurity.att.api.controller;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.zkteco.zkbiosecurity.att.constants.AttApiConstant;
import com.zkteco.zkbiosecurity.att.service.AttTransactionService;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 考勤记录获取
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/11/28 14:09
 * @since 1.0.0
 */
@Controller
@RequestMapping(value = {"/api/v2/transaction"})
@Api(tags = "AttTransaction", description = "att transaction")
public class AttApiV2TransactionController {

    @Autowired
    private AttTransactionService attTransactionService;

    @ApiLogRequest(requestParams = {"personPin", "startDate", "endDate", "pageNo", "pageSize"})
    @ResponseBody
    @RequestMapping(value = "/listAttTransaction", method = RequestMethod.GET, produces = "application/json")
    @ApiOperation(value = "Get Att Transactions Pager", notes = "Return Att Transactions Pager",
        response = ApiResultMessage.class)
    public ApiResultMessage listAttTransaction(@RequestParam(required = false) String personPin,
        @RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate,
        @RequestParam Integer pageNo, @RequestParam Integer pageSize) {
        Date startTime = null;
        Date endTime = null;
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(AttApiConstant.API_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        if (pageSize > 1000) {
            return ApiResultMessage.message(AttApiConstant.API_PAGE_OVERSIZE,
                I18nUtil.i18nCode("common_api_pageSizeOver"));
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            startTime = DateUtil.stringToDate(startDate);
            endTime = DateUtil.stringToDate(endDate);
            if (startTime == null || endTime == null) {// 时间格式错误
                return ApiResultMessage.message(AttApiConstant.API_DATE_ERROR,
                    I18nUtil.i18nCode("common_dsTime_timeValid2"));
            }
            if (startTime.getTime() > endTime.getTime()) {// 开始时间不能大于结束时间
                return ApiResultMessage.message(AttApiConstant.API_DATE_STARTTIME_LARGE,
                    I18nUtil.i18nCode("common_dsTime_timeValid4"));
            }
        }
        return ApiResultMessage.successMessage(attTransactionService.getApiTransactionPager(personPin, startTime, endTime, pageNo, pageSize));
    }
}
