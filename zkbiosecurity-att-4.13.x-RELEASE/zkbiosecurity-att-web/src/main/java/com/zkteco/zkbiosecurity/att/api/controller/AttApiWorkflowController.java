package com.zkteco.zkbiosecurity.att.api.controller;

import java.util.*;

import com.zkteco.zkbiosecurity.att.api.vo.AttCloudSignItem;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.api.vo.AttApiApplyPersonItem;
import com.zkteco.zkbiosecurity.att.api.vo.AttApiApplyTaskDetailItem;
import com.zkteco.zkbiosecurity.att.service.AttLeaveService;
import com.zkteco.zkbiosecurity.att.service.AttWorkflowService;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.base.annotation.ApiPermissions;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;

/**
 * 考勤工作流相关 新建流程、查询流程、审批操作等
 */
@Controller
@RequestMapping(value = "/api/attWorkflow")
public class AttApiWorkflowController {

    @Autowired
    private AttWorkflowService attWorkflowService;
    @Autowired
    private AttLeaveService attLeaveService;

    /**
     * 根据当前用户及审批类型获取第一环节审批人的接口
     *
     * @paramzkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getFirstApprove")
    @ResponseBody
    public ZKResultMsg getFirstApprove(@RequestBody ZKMessage zkMessage) {
        try {
            // personPin 人员pin号
            String personPin = MapUtils.getString(zkMessage.getContent(), "personPin");
            // flowType 流程类型(补签：sign，请假：leave，加班：overtime, trip:出差, out: 外出)
            String flowType = MapUtils.getString(zkMessage.getContent(), "flowType");

            List<AttApiApplyPersonItem> list = attWorkflowService.getFirstApprove(personPin, flowType);

            return new ZKResultMsg(JSONArray.toJSONString(list));

        } catch (Exception e) {
            return ZKResultMsg.getFailMsg(I18nUtil.i18nCode(e.getMessage()));
        }
    }

    /**
     * 根据请假条件获取流程审批人列表
     *
     * @paramzkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getLeaveApprove")
    @ResponseBody
    public ZKResultMsg getLeaveApprove(@RequestBody ZKMessage zkMessage) {
        try {
            // personPin 人员pin号
            String personPin = MapUtils.getString(zkMessage.getContent(), "personPin");
            // 请假类型
            String leaveTypeNo = MapUtils.getString(zkMessage.getContent(), "leaveType");
            // 请假起始时间
            String startTime = MapUtils.getString(zkMessage.getContent(), "startTime");
            String endTime = MapUtils.getString(zkMessage.getContent(), "endTime");

            List<AttApiApplyPersonItem> list =
                attWorkflowService.getLeaveApprove(personPin, leaveTypeNo, startTime, endTime);

            return new ZKResultMsg(JSONArray.toJSONString(list));

        } catch (Exception e) {
            return ZKResultMsg.getFailMsg(e.getMessage());
        }
    }

    /**
     * 根据当前用户及申请类型获取知会人列表
     *
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getFirstNotifier")
    @ResponseBody
    public ZKResultMsg getFirstNotifier(@RequestBody ZKMessage zkMessage) {
        try {
            // 人员pin号
            String personPin = MapUtils.getString(zkMessage.getContent(), "personPin");
            // flowType 流程类型(补签：sign，请假：leave，加班：overtime)
            String flowType = MapUtils.getString(zkMessage.getContent(), "flowType");

            List<AttApiApplyPersonItem> list = attWorkflowService.getFirstNotifier(personPin, flowType);

            return new ZKResultMsg(JSONArray.toJSONString(list));

        } catch (ZKBusinessException e) {
            return ZKResultMsg.getFailMsg(e.getMessage());
        }
    }

    /**
     * 我的申请
     *
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/findApplyTask")
    @ResponseBody
    public ZKResultMsg findApplyTask(@RequestBody ZKMessage zkMessage) {

        // personPin 人员pin号
        String personPin = MapUtils.getString(zkMessage.getContent(), "personPin");
        // flowType 流程类型 sign/leave/overtime
        String flowType = MapUtils.getString(zkMessage.getContent(), "flowType");
        // taskStatus 流程状态 0进行中,2已完成
        Integer taskStatus = MapUtils.getInteger(zkMessage.getContent(), "taskStatus");
        // pageNo 分页
        Integer pageNo = MapUtils.getInteger(zkMessage.getContent(), "pageNo");
        // pageSize 每页条数
        Integer pageSize = MapUtils.getInteger(zkMessage.getContent(), "pageSize");
        String filter = MapUtils.getString(zkMessage.getContent(), "filter");

        if (Objects.isNull(taskStatus)) {
            taskStatus = 0;
        }
        Pager pager = attWorkflowService.findApplyTask(personPin, flowType, taskStatus, pageNo, pageSize, filter);

        return new ZKResultMsg(pager);
    }

    /**
     * 获取流程信息
     *
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getWorkflowInfoByBusinessKey")
    @ResponseBody
    public ZKResultMsg getWorkflowInfoByBusinessKey(@RequestBody ZKMessage zkMessage) {

        String businessKey = MapUtils.getString(zkMessage.getContent(), "businessKey");
        if (StringUtils.isBlank(businessKey)) {
            return ZKResultMsg.getFailMsg(I18nUtil.i18nCode("att_apply_withoutDetail"));
        }
        try {
            AttApiApplyTaskDetailItem item = attWorkflowService.findByBusinessKey(businessKey);
            return new ZKResultMsg(JSONObject.toJSONString(item));
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return ZKResultMsg.failMsg(e.getMessage());
            }
            return ZKResultMsg.failMsg();
        }
    }

    /**
     * 获取流程审批信息
     * 
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getWorkflowInfoByTaskId")
    @ResponseBody
    public ZKResultMsg getWorkflowInfoByTaskId(@RequestBody ZKMessage zkMessage) {

        // 任务节点ID
        String taskId = MapUtils.getString(zkMessage.getContent(), "taskId");
        try {
            AttApiApplyTaskDetailItem item = attWorkflowService.findByTaskId(taskId);
            return new ZKResultMsg(JSONObject.toJSONString(item));
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return ZKResultMsg.failMsg(e.getMessage());
            }
            return ZKResultMsg.failMsg();
        }
    }

    /**
     * 我的审批-待审批
     *
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/findPersonalTask")
    @ResponseBody
    public ZKResultMsg findPersonalTask(@RequestBody ZKMessage zkMessage) {

        // personPin 人员pin号
        String personPin = MapUtils.getString(zkMessage.getContent(), "personPin");
        // flowType 流程类型
        String flowType = MapUtils.getString(zkMessage.getContent(), "flowType");
        // pageNo 分页
        Integer pageNo = MapUtils.getInteger(zkMessage.getContent(), "pageNo");
        // pageSize 每页条数
        Integer pageSize = MapUtils.getInteger(zkMessage.getContent(), "pageSize");
        // 关键词过滤
        String filter = MapUtils.getString(zkMessage.getContent(), "filter");

        Pager pager = attWorkflowService.findPersonalTask(personPin, flowType, pageNo, pageSize, filter);

        return new ZKResultMsg(pager);
    }

    /**
     * 我的审批-已审批
     *
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/findApprovedTask")
    @ResponseBody
    public ZKResultMsg findApprovedTask(@RequestBody ZKMessage zkMessage) {

        String personPin = MapUtils.getString(zkMessage.getContent(), "personPin");
        String flowType = MapUtils.getString(zkMessage.getContent(), "flowType");
        Integer pageNo = MapUtils.getInteger(zkMessage.getContent(), "pageNo");
        Integer pageSize = MapUtils.getInteger(zkMessage.getContent(), "pageSize");
        String filter = MapUtils.getString(zkMessage.getContent(), "filter");

        Pager pager = attWorkflowService.findApprovedTask(personPin, flowType, pageNo, pageSize, filter);

        return new ZKResultMsg(pager);
    }

    /**
     * 流程处理-审批动作
     *
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/completeTask")
    @ResponseBody
    public ZKResultMsg completeTask(@RequestBody ZKMessage zkMessage) {

        Map<String, Object> content = zkMessage.getContent();
        // personPin 人员pin
        String personPin = MapUtils.getString(content, "personPin");
        // taskId 审批任务节点ID
        String taskId = MapUtils.getString(content, "taskId");
        // approve 审批结果 true/false
        String approve = MapUtils.getString(content, "approve");
        // comment 批注
        String comment = MapUtils.getString(content, "comment");
        // notifierPins 知会人pins
        String notifierPins = MapUtils.getString(content, "notifierPins");

        attWorkflowService.completeTask(personPin, taskId, approve, comment, notifierPins);

        return ZKResultMsg.successMsg();
    }

    /**
     * 获取人员月累计补签次数
     * 
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getPersonMonthSignCount")
    @ResponseBody
    public ZKResultMsg getPersonMonthSignCount(@RequestBody ZKMessage zkMessage) {

        Map<String, Object> content = zkMessage.getContent();
        // personPin 人员pin
        String personPin = MapUtils.getString(content, "personPin");
        // 月份
        String month = MapUtils.getString(content, "attDate");

        Integer count = attWorkflowService.getPersonMonthSignCount(personPin, month);

        return new ZKResultMsg(count);
    }

    /**
     * 保存补签申请
     * 
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/saveSignItem")
    @ResponseBody
    public ZKResultMsg saveSignItem(@RequestBody ZKMessage zkMessage) {
        try {
            List<AttCloudSignItem> attSignItemList =
                JSONArray.parseArray(zkMessage.getListContent().toString(), AttCloudSignItem.class);
            attWorkflowService.saveSignItem(attSignItemList);
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg(e.getMessage()));
            }
            return ZKResultMsg.failMsg();
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 保存请假申请
     * 
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/saveLeaveItem")
    @ResponseBody
    public ZKResultMsg saveLeaveItem(@RequestBody ZKMessage zkMessage) {
        ZKResultMsg res = null;
        try {
            JSONArray jsonArray = JSONArray.parseArray(zkMessage.getListContent().toString());
            if (jsonArray != null && jsonArray.size() > 0) {
                JSONObject json = jsonArray.getJSONObject(0);
                json.put("leaveLong", ""); // 排除leaveLong,否则类型转化报错
                AttLeaveItem attLeaveItem = json.toJavaObject(AttLeaveItem.class);

                // 小程序照片保存到本地，防止临时链接过期请假文件无法查看
                String photoUrlList = attLeaveItem.getPhotoUrlList();
                String leaveImagePath = attLeaveService.saveCloudImage(photoUrlList);
                attLeaveItem.setLeaveImagePath(leaveImagePath);
                attLeaveItem.setCloudImageUrl(photoUrlList);
                res = attWorkflowService.isExistApply(attLeaveItem.getPersonPin(), attLeaveItem.getStartDatetime(),
                    attLeaveItem.getEndDatetime());
                if (res.isSuccess()) {
                    attWorkflowService.saveLeaveItem(Arrays.asList(attLeaveItem));
                }
            }
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                String message = I18nUtil.i18nCode(e.getMessage());
                message = message.replace("{0}", "");
                return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg(message));
            }
            return ZKResultMsg.failMsg();
        }
        return res;
    }

    /**
     * 保存加班申请
     * 
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/saveOvertimeItem")
    @ResponseBody
    public ZKResultMsg saveOvertimeItem(@RequestBody ZKMessage zkMessage) {
        ZKResultMsg res = null;
        try {
            List<AttOvertimeItem> attOvertimeItemList =
                JSONArray.parseArray(zkMessage.getListContent().toString(), AttOvertimeItem.class);
            AttOvertimeItem attOvertimeItem = attOvertimeItemList.get(0);
            res = attWorkflowService.isExistApply(attOvertimeItem.getPersonPin(), attOvertimeItem.getStartDatetime(),
                attOvertimeItem.getEndDatetime());
            if (res.isSuccess()) {
                attWorkflowService.saveOvertimeItem(attOvertimeItemList);
            }
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg(e.getMessage()));
            }
            return ZKResultMsg.failMsg();
        }
        return res;
    }

    /**
     * 保存出差申请
     * 
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/saveTripItem")
    @ResponseBody
    public ZKResultMsg saveTripItem(@RequestBody ZKMessage zkMessage) {
        ZKResultMsg res = null;
        try {
            List<AttTripItem> attTripItemList =
                JSONArray.parseArray(zkMessage.getListContent().toString(), AttTripItem.class);
            AttTripItem attTripItem = attTripItemList.get(0);
            res = attWorkflowService.isExistApply(attTripItem.getPersonPin(), attTripItem.getStartDatetime(),
                attTripItem.getEndDatetime());
            if (res.isSuccess()) {
                attWorkflowService.saveTripItem(attTripItemList);
            }
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg(e.getMessage()));
            }
            return ZKResultMsg.failMsg();
        }
        return res;
    }

    /**
     * 保存加班申请
     * 
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/saveOutItem")
    @ResponseBody
    public ZKResultMsg saveOutItem(@RequestBody ZKMessage zkMessage) {
        ZKResultMsg res = null;
        try {
            List<AttOutItem> attOutItemList =
                JSONArray.parseArray(zkMessage.getListContent().toString(), AttOutItem.class);
            AttOutItem attOutItem = attOutItemList.get(0);
            res = attWorkflowService.isExistApply(attOutItem.getPersonPin(), attOutItem.getStartDatetime(),
                attOutItem.getEndDatetime());
            if (res.isSuccess()) {
                attWorkflowService.saveOutItem(attOutItemList);
            }
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg(e.getMessage()));
            }
            return ZKResultMsg.failMsg();
        }
        return res;
    }

    /**
     * 撤销流程
     * 
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/revokeProcess")
    @ResponseBody
    public ZKResultMsg revokeProcess(@RequestBody ZKMessage zkMessage) {
        try {
            Map<String, Object> content = zkMessage.getContent();
            String businessKey = MapUtils.getString(content, "businessKey");
            String personPin = MapUtils.getString(content, "personPin");
            String revokeReason = MapUtils.getString(content, "revokeReason");

            return I18nUtil.i18nMsg(attWorkflowService.revokeProcess(businessKey, personPin, revokeReason));
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg(e.getMessage()));
            }
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        }
    }

    /**
     * 获取异常申请时长
     * 
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/getAttApplyTimeLong")
    @ResponseBody
    public ZKResultMsg getAttApplyTimeLong(@RequestBody ZKMessage zkMessage) {
        try {
            Map<String, Object> content = zkMessage.getContent();
            String personPin = MapUtils.getString(content, "personPin");
            Long startTime = MapUtils.getLong(content, "startTime");
            Long endTime = MapUtils.getLong(content, "endTime");
            String businessType = MapUtils.getString(content, "businessType");
            ZKResultMsg zkResultMsg =
                attLeaveService.getLeaveLongByType(personPin, new Date(startTime), new Date(endTime), businessType);
            return zkResultMsg;
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg(e.getMessage()));
            }
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        }
    }

    /**
     * 根据人员判断申请时间和上班时间是否有重叠是否允许申请
     *
     * @param zkMessage
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping("/checkApplyAndWorkTimeValid")
    @ResponseBody
    public ZKResultMsg checkApplyAndWorkTimeValid(@RequestBody ZKMessage zkMessage) {
        try {
            Map<String, Object> content = zkMessage.getContent();
            String personPin = MapUtils.getString(content, "personPin");
            String flowType = MapUtils.getString(content, "flowType");
            String startTime = MapUtils.getString(content, "startTime");
            String endTime = MapUtils.getString(content, "endTime");

            return attWorkflowService.checkApplyAndWorkTimeValid(personPin, flowType, startTime, endTime);
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg(e.getMessage()));
            }
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        }
    }
}
