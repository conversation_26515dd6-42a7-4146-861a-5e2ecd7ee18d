package com.zkteco.zkbiosecurity.att.controller;

import com.zkteco.zkbiosecurity.att.remote.AttDeptStatisticalReportRemote;
import com.zkteco.zkbiosecurity.att.service.AttDeptStatisticalReportService;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.vo.AttDeptStatisticalReportItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

/**
 * 部门汇总表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:25
 * @since 1.0.0
 */
@Controller
public class AttDeptStatisticalReportController extends ExportController implements AttDeptStatisticalReportRemote {

    @Autowired
    private AttDeptStatisticalReportService attDeptStatisticalReportService;
    @Autowired
    private AttParamService attParamService;

    @RequiresPermissions("att:deptStatistiCalReport")
    @Override
    public ModelAndView index() {
        attDeptStatisticalReportService.modifyItemLabel();

        boolean overtimeLevelEnable = attParamService.overtimeLevelEnable();
        if (!overtimeLevelEnable) {
            request.setAttribute("showColumns", "!overTimeOT1,overTimeOT2,overTimeOT3");
        }

        return new ModelAndView("att/report/attDeptStatisticalReport");
    }

    @RequiresPermissions("att:deptStatistiCalReport:refresh")
    @Override
    public DxGrid list(AttDeptStatisticalReportItem condition) {
        Pager pager = attDeptStatisticalReportService.loadPagerByAuthUserFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:deptStatistiCalReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_deptStatisticalReport", opType = "common_op_export",
        requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void export() {
        AttDeptStatisticalReportItem attDeptStatisticalReportItem = new AttDeptStatisticalReportItem();
        setConditionValue(attDeptStatisticalReportItem);
        List<AttDeptStatisticalReportItem> itemList = attDeptStatisticalReportService.getDeptStatisticalReportItemData(
            request.getSession().getId(), attDeptStatisticalReportItem, getBeginIndex(), getEndIndex());
        excelExport(itemList, AttDeptStatisticalReportItem.class);
    }

    @RequiresPermissions("att:deptDeptOvertimeSummaryReport")
    @Override
    public ModelAndView indexDeptOvertimeSummaryReport() {
        attDeptStatisticalReportService.modifyItemLabel();

        boolean overtimeLevelEnable = attParamService.overtimeLevelEnable();
        if (!overtimeLevelEnable) {
            request.setAttribute("showColumns", "overTimeOT1,overTimeOT2,overTimeOT3");
        }

        return new ModelAndView("att/report/attDeptOvertimeSummaryReport");
    }

    @RequiresPermissions("att:deptDeptOvertimeSummaryReport:refresh")
    @Override
    public DxGrid listDeptOvertimeSummaryReport(AttDeptStatisticalReportItem condition) {
        Pager pager = attDeptStatisticalReportService.loadPagerByAuthUserFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @SuppressWarnings("unchecked")
    @RequiresPermissions("att:deptDeptOvertimeSummaryReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_deptDeptOvertimeSummaryReport",
        opType = "common_op_export", requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void exportDeptOvertimeSummaryReport() {
        AttDeptStatisticalReportItem attMonthStatisticalReportItem = new AttDeptStatisticalReportItem();
        setConditionValue(attMonthStatisticalReportItem);
        List<AttDeptStatisticalReportItem> itemList = attDeptStatisticalReportService.getDeptStatisticalReportItemData(
            request.getSession().getId(), attMonthStatisticalReportItem, getBeginIndex(), getEndIndex());
        excelExport(itemList, AttDeptStatisticalReportItem.class);
    }

    @RequiresPermissions("att:leaveSummaryReport")
    @Override
    public ModelAndView indexDeptLeaveSummaryReport() {
        attDeptStatisticalReportService.modifyItemLabel();
        return new ModelAndView("att/report/attDeptLeaveSummaryReport");
    }

    @RequiresPermissions("att:leaveSummaryReport:refresh")
    @Override
    public DxGrid listDeptLeaveSummaryReport(AttDeptStatisticalReportItem condition) {
        Pager pager = attDeptStatisticalReportService.loadPagerByAuthUserFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @SuppressWarnings("unchecked")
    @RequiresPermissions("att:leaveSummaryReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_leaveSummaryReport", opType = "common_op_export",
        requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void exportDeptLeaveSummaryReport() {
        AttDeptStatisticalReportItem attMonthStatisticalReportItem = new AttDeptStatisticalReportItem();
        setConditionValue(attMonthStatisticalReportItem);
        List<AttDeptStatisticalReportItem> itemList = attDeptStatisticalReportService.getDeptStatisticalReportItemData(
            request.getSession().getId(), attMonthStatisticalReportItem, getBeginIndex(), getEndIndex());
        excelExport(itemList, AttDeptStatisticalReportItem.class);
    }
}
