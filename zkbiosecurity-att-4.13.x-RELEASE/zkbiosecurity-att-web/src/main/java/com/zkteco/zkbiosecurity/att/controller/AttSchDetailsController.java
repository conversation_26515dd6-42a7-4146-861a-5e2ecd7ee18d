package com.zkteco.zkbiosecurity.att.controller;

import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.remote.AttSchDetailsRemote;
import com.zkteco.zkbiosecurity.base.controller.BaseController;

/**
 * 排班详情界面跳转
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/5/22 16:32
 * @return
 */
@Controller
public class AttSchDetailsController extends BaseController implements AttSchDetailsRemote {

    @Override
    @RequiresPermissions("att:schDetails")
    public ModelAndView index() {
        return new ModelAndView("att/schDetails/attSchDetails");
    }
}