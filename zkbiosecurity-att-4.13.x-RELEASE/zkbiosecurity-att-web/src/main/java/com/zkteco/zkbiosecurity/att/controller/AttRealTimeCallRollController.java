package com.zkteco.zkbiosecurity.att.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSONArray;
import com.zkteco.zkbiosecurity.att.remote.AttRealTimeCallRollRemote;
import com.zkteco.zkbiosecurity.att.service.AttRealTimeCallRollService;
import com.zkteco.zkbiosecurity.att.vo.AttRealTimeCallRollItem;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 实时点名
 * 
 * <AUTHOR>
 * @date 2020年4月17日 上午9:17:24
 * @version V1.0
 */
@Controller
public class AttRealTimeCallRollController extends BaseController implements AttRealTimeCallRollRemote {

    @Autowired
    private AttRealTimeCallRollService attRealTimeCallRollService;

    @Override
    public ModelAndView index() {
        return new ModelAndView("att/realTimeCallRoll/attRealTimeCallRoll");
    }

    @Override
    public ZKResultMsg getAttRealTimeCallRollList(AttRealTimeCallRollItem condition) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        Map<String, JSONArray> resultMap = attRealTimeCallRollService.getAttRealTimeCallRollList(condition);
        zkResultMsg.setData(resultMap);
        return zkResultMsg;
    }
}
