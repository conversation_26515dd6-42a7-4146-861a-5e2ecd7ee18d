package com.zkteco.zkbiosecurity.att.controller;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.remote.AttPersonRemote;
import com.zkteco.zkbiosecurity.att.service.AttAreaPersonService;
import com.zkteco.zkbiosecurity.att.service.AttPersonService;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 考勤人员
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 16:56
 * @since 1.0.0
 */
@Controller
public class AttPersonController extends BaseController implements AttPersonRemote {
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttAreaPersonService attAreaPersonService;

    @RequiresPermissions("pers:person:attEdit")
    @Override
    public ModelAndView edit(@RequestParam(value = "personId", required = false) String personId) {
        if (StringUtils.isNotBlank(personId)) {
            request.setAttribute("item", attPersonService.getItemByPersonId(personId));
            request.setAttribute("attPersonAreas", attAreaPersonService.getAreaIdsByPersonId(personId));
        } else {
            request.setAttribute("attPersonAreas", attAreaPersonService.getRootAreaId());
        }
        return new ModelAndView("att/person/editAttPerson");
    }

    @Override
    public DxGrid attPersonList(AttPersonAreaItem condition) {
        SecuritySubject authUser = getCurrentSubject();
        if (!authUser.getIsSuperuser()) {
            if (authUser.getDepartmentIds() != null && authUser.getDepartmentIds().size() > 0) {
                condition
                    .setInDeptId(authUser.getDepartmentIds().toString().replaceAll("[\\[\\]]", "").replaceAll(" ", ""));
            }
        }
        // Pager pager = attPersonService.getItemsByPage(condition, getPageNo(), getPageSize());
        Pager pager = attPersonService.getPersonAreaItemsByPage(request.getSession().getId(), condition, getPageNo(),
            getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public DxGrid selectList(AttPersonSelectItem condition) {
        Pager pager = new Pager();
        if (condition.getType().equals("noSelected")) {
            pager = attPersonService.getAttPersonSelectByPage(request.getSession().getId(), condition, getPageNo(),
                getPageSize());
        } else if (condition.getType().equals("selected")) {
            pager.setData(new ArrayList<AttPersonSelectItem>());
        }
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public DxGrid attAreaSelectPerson(AttPersonSelectItem condition) {
        Pager pager = new Pager();
        if (condition.getType().equals("noSelected")) {
            pager = attPersonService.getNoExistPerson(request.getSession().getId(), condition, null, getPageNo(),
                getPageSize());
        } else if (condition.getType().equals("selected")) {
            pager.setData(new ArrayList<AttPersonSelectItem>());
        }
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public ZKResultMsg getPersonInfo(String ids) {
        String pin = request.getParameter("pin");
        AttPersonItem attPersonItem = attPersonService.getByPinFromPersonCache(pin);
        JSONObject jsonObject = new JSONObject();
        if (null != attPersonItem) {
            jsonObject.put("personId", attPersonItem.getPersonId());
            jsonObject.put("personPin", attPersonItem.getPersonPin());
            jsonObject.put("personName", attPersonItem.getPersonName());
            jsonObject.put("personLastName", attPersonItem.getPersonLastName());
            jsonObject.put("deptName", attPersonItem.getDeptName());
            jsonObject.put("deptCode", attPersonItem.getDeptCode());
            jsonObject.put("deptId", attPersonItem.getDeptId());
        }
        return new ZKResultMsg(jsonObject);
    }

    @Override
    @RequiresPermissions("att:personVerifyMode")
    public ModelAndView verifyModeIndex() {
        return new ModelAndView("att/person/attPersonVerifyMode");
    }

    @Override
    @RequiresPermissions("att:personVerifyMode:refresh")
    public DxGrid verifyModeList(AttPersonVerifyModeItem condition) {
        Pager pager =
            attPersonService.verifyModeList(request.getSession().getId(), condition, getPageNo(), getPageSize());
        buildItems((List<AttPersonVerifyModeItem>)pager.getData());
        return GridUtil.convert(pager, condition.getClass());
    }

    private void buildItems(List<AttPersonVerifyModeItem>  attPersonVerifyModeItemList) {
        for(AttPersonVerifyModeItem item : attPersonVerifyModeItemList) {
            // 空默认显示为自动识别
            if (item.getVerifyMode() == null) {
                item.setVerifyMode((short)0);
            }
        }
    }

    @Override
    @RequiresPermissions("att:personVerifyMode:setting")
    public ModelAndView editVerifyMode(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            String[] idsArray = ids.split(",");
            if (idsArray.length == 1) {
                request.setAttribute("item", attPersonService.getItemById(idsArray[0]));
            } else {
                request.setAttribute("item", new AttPersonVerifyModeItem());
            }
        }
        request.setAttribute("attPersonIds", ids);
        String pins = request.getParameter("pins");
        request.setAttribute("pins", pins);
        return new ModelAndView("att/person/editAttPersonVerifyMode");
    }

    @Override
    @RequiresPermissions("att:personVerifyMode:setting")
    @LogRequest(module = "att_module", object = "att_personVerifyMode", opType = "att_personVerifyMode_setting",
        requestParams = {"pins"}, opContent = "pers_person_pin")
    public ZKResultMsg saveVerifyMode(AttPersonVerifyModeItem attPersonVerifyModeItem) {
        attPersonService.saveVerifyMode(attPersonVerifyModeItem);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }
}
