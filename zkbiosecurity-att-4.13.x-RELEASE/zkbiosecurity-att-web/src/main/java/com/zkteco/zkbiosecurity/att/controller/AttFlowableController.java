package com.zkteco.zkbiosecurity.att.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.remote.AttFlowableRemote;
import com.zkteco.zkbiosecurity.att.service.AttFlowableService;
import com.zkteco.zkbiosecurity.att.service.AttWorkflowService;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.auth.service.AuthSessionServcie;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.FileUtil;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.system.service.BaseMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import java.util.Date;
import java.util.Objects;

/**
 * 考勤工作流
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 18:08
 * @since 1.0.0
 */
@Controller
public class AttFlowableController extends BaseController implements AttFlowableRemote {

    @Autowired
    private AttFlowableService attFlowableService;
    @Autowired
    private AttWorkflowService attWorkflowService;
    @Autowired
    private BaseMessageService baseMessageService;
    @Autowired
    private AuthSessionServcie authSessionServcie;

    @Override
    public ModelAndView index() {
        return null;
    }

    @Override
    public ModelAndView myApply() {
        return new ModelAndView("/att/flowable/myApply");
    }

    @Override
    public ModelAndView pendingTask() {
        return new ModelAndView("/att/flowable/pendingTask");
    }

    @Override
    public ModelAndView approvedTask() {
        return new ModelAndView("/att/flowable/approvedTask");
    }

    @Override
    public DxGrid myApplyList(AttProcessInfoItem condition) {
        Pager pager =
            attFlowableService.findMyApplyList(request.getSession().getId(), condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public DxGrid pendingTaskList(AttProcessInfoItem condition) {
        Pager pager =
            attFlowableService.findPendingTaskList(request.getSession().getId(), condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public DxGrid approvedTaskList(AttProcessInfoItem condition) {
        Pager pager = attFlowableService.findApprovedTaskList(request.getSession().getId(), condition, getPageNo(),
            getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public ZKResultMsg startSign(AttSignItem attSignItem) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        attSignItem.setPersonPin(getCurrentSubject().getUserCode());
        attFlowableService.startSign(attSignItem);
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg startLeave(AttLeaveItem attLeaveItem, @RequestParam(value="leaveImage",required = false) MultipartFile[] files) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        attLeaveItem.setPersonPin(getCurrentSubject().getUserCode());
        attLeaveItem.setLeaveImagePath(saveApplyImage(files));
        attFlowableService.startLeave(attLeaveItem);
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg startOvertime(AttOvertimeItem attOvertimeItem) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        attOvertimeItem.setPersonPin(getCurrentSubject().getUserCode());
        attFlowableService.startOvertime(attOvertimeItem);
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg startOut(AttOutItem attOutItem, @RequestParam(value="leaveImage",required = false) MultipartFile[] files) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        attOutItem.setPersonPin(getCurrentSubject().getUserCode());
        attOutItem.setLeaveImagePath(saveApplyImage(files));
        attFlowableService.startOut(attOutItem);
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg startTrip(AttTripItem attTripItem, @RequestParam(value="leaveImage",required = false) MultipartFile[] files) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        attTripItem.setPersonPin(getCurrentSubject().getUserCode());
        attTripItem.setLeaveImagePath(saveApplyImage(files));
        attFlowableService.startTrip(attTripItem);
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg startClass(AttClassItem attClassItem) {
        attFlowableService.startClass(attClassItem);
        return new ZKResultMsg();
    }

    @Override
    public ZKResultMsg startAdjust(AttAdjustItem attAdjustItem) {
        attFlowableService.startAdjust(attAdjustItem);
        return new ZKResultMsg();
    }

    @Override
    public ZKResultMsg attachment(MultipartFile file) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        String filePath = null;
        if (Objects.nonNull(file) && !file.isEmpty()) {
            Date date = new Date();
            filePath = FileUtil.saveFileToServer("att", "leave/image", date.getTime() + ".jpg", file);
        }
        zkResultMsg.setData(filePath);
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg complete(String taskId, String pass, String comment, String notifierPerIds) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        String personPin = getPersonPin();
        attWorkflowService.completeTask(personPin, taskId, pass, comment, notifierPerIds);
        baseMessageService.saveTodoMsgRead(personPin, taskId);
        return zkResultMsg;
    }

    private String getPersonPin() {
        SecuritySubject securitySubject = getCurrentSubject();
        return securitySubject.getUserCode();
    }

    @Override
    public ZKResultMsg cancelApply(String businessKey, String revokeReason) {
        SecuritySubject securitySubject = authSessionServcie.getSecuritySubject(request.getSession().getId());
        if (securitySubject != null) {
            ZKResultMsg resultMsg =
                attWorkflowService.revokeProcess(businessKey, securitySubject.getUserCode(), revokeReason);
            if (resultMsg != null && "ok".equals(resultMsg.getRet()) && resultMsg.getData() != null) {
                JSONObject data = JSONObject.parseObject(JSON.toJSONString(resultMsg.getData()));
                String personPin = data.getString("pin");
                String processTaskId = data.getString("taskId");
                baseMessageService.saveTodoMsgRead(personPin, processTaskId);
            }
            return resultMsg;
        }
        return ZKResultMsg.failMsg();
    }

    @Override
    public ZKResultMsg isCanOpenFlowable() {
        ZKResultMsg result = new ZKResultMsg();
        SecuritySubject securitySubject = authSessionServcie.getSecuritySubject(request.getSession().getId());
        /**
         * 登录类型为pers则是员工自助登录,开放流程任务菜单 超级管理员也开放,其他一律禁用菜单
         */
        if ("PERS".equals(securitySubject.getLoginType()) || securitySubject.getIsSuperuser()) {
            result.setData(true);
            return result;
        }
        result.setData(false);
        result.setMsg("att_flowable_noAuth");
        return I18nUtil.i18nMsg(result);
    }

    /**
     * 保存申请照片
     *
     * @param files:
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/10/20 11:42
     * @since 1.0.0
     */
    private String saveApplyImage(MultipartFile[] files) {
        StringBuffer leaveImageBuf = new StringBuffer("");
        if (Objects.nonNull(files) && files.length > 0) {
            for (MultipartFile uploadFile : files) {
                if (!uploadFile.isEmpty()) {
                    String leaveImageStr = FileUtil.saveFileToServer("att", "leave/image",
                            System.currentTimeMillis() + ".jpg", uploadFile);
                    leaveImageBuf.append(leaveImageStr).append(",");
                }
            }
            if (leaveImageBuf.length() > 0) {
                leaveImageBuf.deleteCharAt(leaveImageBuf.length() - 1);
            }
        }
        return leaveImageBuf.toString();
    }
}
