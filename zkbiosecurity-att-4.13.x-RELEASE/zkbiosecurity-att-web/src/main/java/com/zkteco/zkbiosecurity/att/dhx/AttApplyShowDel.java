package com.zkteco.zkbiosecurity.att.dhx;

import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.core.utils.ShowGridColumn;
import com.zkteco.zkbiosecurity.security.SecurityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 *
 * 异常申请根据当前用户角色是否显示删除操作按钮
 * 
 * <AUTHOR>
 * @date 2020/4/8
 *
 */
@Component
public class AttApplyShowDel implements ShowGridColumn {

    @Autowired(required = false)
    private SecurityService securityService;

    @Override
    public boolean isShow(Object o) {
        ServletRequestAttributes attrs = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
        String sessionId = attrs == null ? null : attrs.getRequest().getSession().getId();
        if (null != securityService) {
            SecuritySubject securitySubject = securityService.getSecuritySubject(sessionId);
            if (null != securitySubject) {
                Boolean staff = securitySubject.getStaff();
                if (null != staff && staff) {
                    // 如果是员工自助则不显示删除操作按钮
                    return false;
                }
            }
        }
        return true;
    }
}
