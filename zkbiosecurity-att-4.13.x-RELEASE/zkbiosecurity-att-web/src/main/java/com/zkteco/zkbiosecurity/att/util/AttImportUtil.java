package com.zkteco.zkbiosecurity.att.util;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import com.zkteco.zkbiosecurity.att.bean.AttUsbTransactionBean;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date: 2019/4/19 11:42
 */
@Slf4j
public class AttImportUtil {

    public static List<AttUsbTransactionBean> getUsbFile(InputStream inputStream) {
        List<AttUsbTransactionBean> list = new ArrayList<>();
        try {
            int len = 0;
            byte[] buffer = new byte[inputStream.available()];
            String message = "";
            // 获取所有字符
            while ((len = inputStream.read(buffer)) != -1) {
                message = new String(buffer, 0, len, "GBK"); // 为防止中文乱码，设置字符集为GBK
            }
            String[] arr = message.split("\n"); // 将获取的字符按行分割
            // 获取每一行的数据并封装成T对象
            for (int i = 0; i < arr.length; i++) {
                // 解析文件，第一行是null的则跳过
                if (arr[i] == null) {
                    continue;
                }
                boolean isLineNull = false;
                String[] line = arr[i].split("\t");
                if (line.length < 6) {
                    continue;
                }
                // 去掉字符串前后的空格
                for (int ll = 0; ll < line.length; ll++) {
                    if (line[ll] == null) {
                        isLineNull = true;
                        break;
                    }
                    line[ll] = replaceBlank(line[ll]);
                }
                // 数据包含null的，则跳过
                if (isLineNull) {
                    continue;
                }
                AttUsbTransactionBean usbTrans = null;
                if (line.length == 6) {
                    usbTrans = new AttUsbTransactionBean(line[0], line[1], line[2], line[3], line[4], line[5]);
                } else if (line.length == 7) {
                    usbTrans = new AttUsbTransactionBean(line[0], line[1], line[2], line[3], line[4], line[5], line[6]);
                } else if (line.length == 8) {
                    usbTrans = new AttUsbTransactionBean(line[0], line[1], line[2], line[3], line[4], line[5], line[6],
                        line[7]);
                }
                if (usbTrans != null) {
                    list.add(usbTrans);
                }
            }
        } catch (Exception e) {
            log.error("read file error", e);
            throw new ZKBusinessException("common_op_failed");
        }
        return list;
    }

    public static List<AttUsbTransactionBean> usbImport(InputStream inputStream) {
        List<AttUsbTransactionBean> list;
        try {
            list = getUsbFile(inputStream);
        } catch (ZKBusinessException e) {
            log.error("import business error", e);
            throw e;
        } catch (Exception e) {
            log.error("import error", e);
            throw new ZKBusinessException("common_op_failed");
        }
        return list;
    }

    public static String replaceBlank(String str) {
        String dest = null;
        if (str == null) {
            return dest;
        } else {
            String regStartSpace = "^[ ]*";
            String regEndSpace = "[ ]*$";
            dest = str.replaceAll(regStartSpace, "").replaceAll(regEndSpace, "");
            // Pattern p = Pattern.compile("\\s*|\t|\r|\n");
            // Matcher m = p.matcher(str);
            // dest = m.replaceAll("");
            return dest;
        }
    }
}
