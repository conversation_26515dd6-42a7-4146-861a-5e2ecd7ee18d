package com.zkteco.zkbiosecurity.att.controller;

import java.util.Map;

import com.zkteco.zkbiosecurity.att.remote.AttSignAddressRemote;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.service.AttSignAddressService;
import com.zkteco.zkbiosecurity.att.vo.AttSignAddressItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

/**
 * 签到地址
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:09
 * @since 1.0.0
 */
@Controller
public class AttSignAddressController extends ExportController implements AttSignAddressRemote {

    @Autowired
    private AttSignAddressService attSignAddressService;
    @Autowired
    private AttParamService attParamService;

    @RequiresPermissions("att:signAddress")
    @Override
    public ModelAndView index() {
        return new ModelAndView("/att/signAddress/attSignAddress");
    }

    @RequiresPermissions("att:signAddress:edit")
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            // AttSignAddressItem item = attSignAddressService.getItemById(id);
            AttSignAddressItem item = attSignAddressService.getAttSignAddressItemById(id);
            request.setAttribute("item", item);
        }
        // 域对象加入考勤参数,用于判断使用哪种地图 add by bob.liu 20190730
        Map<String, String> attParams = attParamService.getAttParams();
        request.setAttribute("attParams", attParams);
        return new ModelAndView("/att/signAddress/editAttSignAddress");
    }

    @RequiresPermissions("att:signAddress:edit")
    @Override
    public ZKResultMsg save(AttSignAddressItem item) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        attSignAddressService.saveItem(item);
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @RequiresPermissions("att:signAddress:refresh")
    @Override
    public DxGrid list(AttSignAddressItem condition) {
        Pager pager = attSignAddressService.loadPagerByAuthUserFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:signAddress:del")
    @Override
    public ZKResultMsg delete(String ids) {
        attSignAddressService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }
}
