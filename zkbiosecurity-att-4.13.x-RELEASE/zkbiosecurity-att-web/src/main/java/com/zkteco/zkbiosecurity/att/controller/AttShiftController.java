package com.zkteco.zkbiosecurity.att.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.att.vo.AttShiftSchItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.constants.AttShiftConstant;
import com.zkteco.zkbiosecurity.att.remote.AttShiftRemote;
import com.zkteco.zkbiosecurity.att.service.AttShiftService;
import com.zkteco.zkbiosecurity.att.vo.AttShiftItem;
import com.zkteco.zkbiosecurity.base.annotation.LogChangeRequest;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 班次
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:04
 * @since 1.0.0
 */
@Controller
public class AttShiftController extends BaseController implements AttShiftRemote {

    @Autowired
    private AttShiftService attShiftService;

    @RequiresPermissions("att:shift")
    @Override
    public ModelAndView index() {
        return new ModelAndView("att/shift/attShift");
    }

    @RequiresPermissions("att:shift:edit")
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("timeSlotIds", attShiftService.getTimeSlotIds(id));
            request.setAttribute("item", attShiftService.getItemById(id));
            // 设置对应此数据是否有链接的外键，若是有，则不予页面编辑。
            boolean isExist = attShiftService.isExistFkData(id);
            request.setAttribute("editPage", isExist);
        } else {
            AttShiftItem item = new AttShiftItem();
            item.setShiftType(Short.valueOf(request.getParameter("shiftType")));
            request.setAttribute("item", item);
        }
        return new ModelAndView("att/shift/editAttShift");
    }

    @Override
    public ModelAndView addTimeSlot(String id) {
        if (StringUtils.isNotBlank(id)) {
            AttShiftItem item = attShiftService.getItemById(id);
            item.setTimeSlotIds(attShiftService.getTimeSlotIds(id));
            request.setAttribute("item", attShiftService.getItemById(id));
            if (AttShiftConstant.ShiftType.REGULAR_SHIFT == item.getShiftType()) {
                request.setAttribute("attTimeSlotColumns", "checkbox,periodName,toWorkTime,offWorkTime");
            } else {
                request.setAttribute("attTimeSlotColumns", "checkbox,periodName,startSignInTime,endSignOffTime");
            }
        }
        return new ModelAndView("att/shift/editAttShiftAddTimeSlot");
    }

    @RequiresPermissions("att:shift:edit")
    @LogChangeRequest(module = "att_module", object = "att_leftMenu_shift", opType = "common_op_edit",
        vo = AttShiftItem.class, service = AttShiftService.class)
    @Override
    public ZKResultMsg save(AttShiftItem item) {
        ZKResultMsg res = new ZKResultMsg();
        String attTimeSlotIds = request.getParameter("timeSlotIds");
        if (item.getShiftColor() == null) {
            item.setShiftColor("#66FF66");
        }
        // 班次按排班日期开始不要周期开始日期
        if (AttConstant.ATT_PERIODSTARTMODE_BYSCH.equals(item.getPeriodStartMode())) {
            item.setStartDate(null);
        }
        attShiftService.saveItem(item, attTimeSlotIds);
        res.setData(item);
        return I18nUtil.i18nMsg(res);
    }

    @Override
    public ZKResultMsg saveTimeSlot(AttShiftItem item) {
        ZKResultMsg resultMsg = attShiftService.saveTimeSlot(item);
        return I18nUtil.i18nMsg(resultMsg);
    }

    @RequiresPermissions("att:shift:refresh")
    @Override
    public DxGrid list(AttShiftItem condition) {
        Pager pager = attShiftService.getItemsByPage(condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:shift:del")
    @LogRequest(module = "att_module", object = "att_leftMenu_shift", opType = "common_op_del",
        requestParams = {"shiftName"}, opContent = "common_name")
    @Override
    public ZKResultMsg delete(String ids) {
        // 是否存在外键关联的数据
        if (attShiftService.isExistFkData(ids)) {
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("att_common_relationDataCanNotDel"));
        }

        attShiftService.deleteByIds(ids);

        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @Override
    public Map<String, Object> getAttTimeSlotJson() {
        // 后台取时间段
        String id = request.getParameter("id");
        List<String> tsdList = attShiftService.listTsd(id);
        AttShiftItem attShiftItem = attShiftService.getItemById(id);
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("startDate", attShiftItem.getStartDate());
        result.put("tsdIds", attShiftItem.getTimeSlotDetailIds());
        result.put("tsdList", tsdList);
        result.put("result", "success");

        return result;
    }

    @Override
    public ZKResultMsg getShiftList() {
        List<SelectItem> items = new ArrayList<SelectItem>();
        List<AttShiftItem> attShiftList = attShiftService.getByCondition(new AttShiftItem());
        for (AttShiftItem attShift : attShiftList) {
            if (attShift.getStartDate() == null) {
                items.add(new SelectItem(attShift.getShiftName(), attShift.getId()));
            } else {
                items.add(
                    new SelectItem(attShift.getShiftName() + "(" + attShift.getStartDate() + ")", attShift.getId()));
            }
        }
        return new ZKResultMsg(items);
    }

    @Override
    public String validNo(String shiftNo) {
        boolean rs = !attShiftService.existsByShiftNo(shiftNo);
        return rs + "";
    }

    @Override
    public String validName(String shiftName) {
        boolean rs = !attShiftService.existsByShiftName(shiftName);
        return rs + "";
    }

    @RequiresPermissions("att:shift:clear")
    @Override
    public ZKResultMsg clean(String id) {
        attShiftService.cleanByShiftIds(id);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("att:shift:clear")
    @Override
    public ZKResultMsg cleanByIds(String id, String idp) {
        attShiftService.cleanShiftTimeSolt(id, idp);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public DxGrid allList(AttShiftSchItem condition) {
        if ("1".equals(condition.getScheduleType())) {
            condition.setShiftType((short)0);
        }
        Pager pager = attShiftService.getItemsByPage(condition, getPageNo(), 100000);
        return GridUtil.convert(pager, condition.getClass());
    }
}
