package com.zkteco.zkbiosecurity.att.controller;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.remote.AttGroupRemote;
import com.zkteco.zkbiosecurity.att.service.AttGroupService;
import com.zkteco.zkbiosecurity.att.vo.AttGroupItem;
import com.zkteco.zkbiosecurity.att.vo.AttGroupPersonSelectItem;
import com.zkteco.zkbiosecurity.base.annotation.LogChangeRequest;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.format.TreeBuilder;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 分组
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 18:03
 * @since 1.0.0
 */
@Controller
public class AttGroupController extends BaseController implements AttGroupRemote {

    @Autowired
    private AttGroupService attGroupService;

    @RequiresPermissions("att:group")
    @Override
    public ModelAndView index() {
        return new ModelAndView("att/group/attGroup");
    }

    @RequiresPermissions("att:group:edit")
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("item", attGroupService.getItemById(id));
        }
        return new ModelAndView("att/group/editAttGroup");
    }

    @RequiresPermissions("att:group:edit")
    @LogChangeRequest(module = "att_module", object = "att_leftMenu_group", opType = "common_op_edit",
        vo = AttGroupItem.class, service = AttGroupService.class)
    @Override
    public ZKResultMsg save(AttGroupItem item) {
        ZKResultMsg res = new ZKResultMsg();
        attGroupService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("att:group:refresh")
    @Override
    public DxGrid list(AttGroupItem condition) {
        Pager pager = attGroupService.getItemsByPage(condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:group:del")
    @LogRequest(module = "att_module", object = "att_leftMenu_group", opType = "common_op_del",
        requestParams = {"names"}, opContent = "common_name")
    @Override
    public ZKResultMsg delete(String ids) {
        attGroupService.deleteByGroup(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @Override
    public TreeItem tree() {
        List<AttGroupItem> BaseDepartmentItems = attGroupService.getByCondition(new AttGroupItem());
        List<TreeItem> items = new ArrayList<TreeItem>();
        TreeItem item = null;
        TreeItem pItem = null;
        for (AttGroupItem attGroupItem : BaseDepartmentItems) {
            item = new TreeItem();
            item.setId(attGroupItem.getId());
            item.setText(attGroupItem.getGroupName());
            pItem = new TreeItem("0");
            item.setParent(pItem);
            items.add(item);
        }
        List<TreeItem> treeItems = TreeBuilder.newTreeBuilder(TreeItem.class, String.class).buildToTreeList(items);

        return new TreeItem("0", treeItems);
    }

    @Override
    public String validName(String groupName) {
        boolean existsGroupName = attGroupService.existsByGroupName(groupName);
        return String.valueOf(!existsGroupName);
    }

    @Override
    public DxGrid selectList(AttGroupPersonSelectItem condition) {
        Pager pager = null;
        if (AttConstant.NOSELECT.equals(condition.getType())) {
            pager =
                attGroupService.getNoExistPerson(request.getSession().getId(), condition, getPageNo(), getPageSize());
        } else {
            pager = new Pager(getPageNo(), getPageSize());
            pager.setData(new ArrayList<AttGroupPersonSelectItem>());
        }
        return GridUtil.convert(pager, condition.getClass());
    }
}