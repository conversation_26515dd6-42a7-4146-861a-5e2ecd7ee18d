package com.zkteco.zkbiosecurity.att.util;

import com.zkteco.zkbiosecurity.base.vo.ZKPageResultMsg;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @Date: 2019/1/10 15:51
 */
public class I18nMsgUtil {

    public static ZKPageResultMsg i18nMsg(ZKPageResultMsg resultMsg) {
        String msg = resultMsg.getMsg();
        if (StringUtils.isBlank(msg)) {
            return resultMsg;
        }
        resultMsg.setMsg(I18nUtil.i18nCode(msg));
        return resultMsg;
    }
}
