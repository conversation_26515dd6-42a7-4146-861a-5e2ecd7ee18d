package com.zkteco.zkbiosecurity.att.api.controller;

import com.zkteco.zkbiosecurity.att.api.vo.AttApiAreaItem;
import com.zkteco.zkbiosecurity.att.api.vo.AttApiAreaPersonItem;
import com.zkteco.zkbiosecurity.att.constants.AttApiConstant;
import com.zkteco.zkbiosecurity.att.service.AttAreaPersonService;
import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 考勤区域接口
 * 
 * @Auther: lambert.li
 * @Date: 2018/11/15 17:03
 */
@Controller
@RequestMapping(value = {"/api/attAreaPerson"})
@Slf4j
@Api(tags = "AttAreaPerson", description = "att area person")
public class AttApiAreaPersonController {

    @Autowired
    private AttAreaPersonService attAreaPersonService;

    /**
     * 分页获取考勤区域信息
     * 
     * @auther lambert.li
     * @date 2018/11/15 17:05
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiLogRequest(requestParams = {"pageNo", "pageSize"})
    @ApiOperation(value = "Get Area List", notes = "Return Area List", response = ApiResultMessage.class)
    @RequestMapping(value = "/area/list", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public ApiResultMessage listArea(@RequestParam Integer pageNo, @RequestParam Integer pageSize) {
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(AttApiConstant.API_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        if (pageSize > 1000) {
            return ApiResultMessage.message(AttApiConstant.API_PAGE_OVERSIZE,
                I18nUtil.i18nCode("common_api_pageSizeOver"));
        }
        List<AttApiAreaItem> apiBaseAreas = attAreaPersonService.getApiAreaList(pageNo, pageSize);
        return ApiResultMessage.successMessage(apiBaseAreas);
    }

    /**
     * 根据code获取区域信息
     * 
     * @auther lambert.li
     * @date 2018/11/19 9:06
     * @param code
     * @return
     */
    @ApiLogRequest(requestParams = {"code"})
    @ApiOperation(value = "Get Area By Code", notes = "Return Area", response = ApiResultMessage.class)
    @RequestMapping(value = "/area/get/{code}", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public ApiResultMessage getArea(@PathVariable String code) {
        if (StringUtils.isBlank(code)) {
            return ApiResultMessage.message(AttApiConstant.BASE_AREA_CODE_NOTNULL,
                I18nUtil.i18nCode("att_api_areaCodeNotNull"));
        }
        AttApiAreaItem attApiAreaItem = attAreaPersonService.getApiAreaByCode(code);
        return ApiResultMessage.successMessage(attApiAreaItem);
    }

    /**
     * 区域添加人员
     * 
     * @auther lambert.li
     * @date 2018/11/19 9:06
     * @param areaPerson
     * @return
     */
    @ApiLogRequest(requestParams = {"code", "pins"})
    @ApiOperation(value = "Set Area Person", notes = "Set Area Person", response = ApiResultMessage.class)
    @RequestMapping(value = {"/set"}, method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public ApiResultMessage setAreaPerson(@RequestBody AttApiAreaPersonItem areaPerson) {
        ApiResultMessage rs = null;
        try {
            rs = attAreaPersonService.addApiAreaPerson(areaPerson);
        } catch (Exception e) {
            log.error("api attAreaPerson/set error ", e);
            rs = ApiResultMessage.message(AttApiConstant.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("common_api_programError"));
        }
        return rs;
    }

    /**
     * 区域删除人员
     * 
     * @auther lambert.li
     * @date 2018/11/19 9:06
     * @param areaPerson
     * @return
     */
    @ApiLogRequest(requestParams = {"code", "pins"})
    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Delete Area Person", notes = "Delete Area Person", response = ApiResultMessage.class)
    public ApiResultMessage delAreaPerson(@RequestBody AttApiAreaPersonItem areaPerson) {
        if (StringUtils.isBlank(areaPerson.getCode())) {// 区域编号为空
            return ApiResultMessage.message(AttApiConstant.BASE_AREA_CODE_NOTNULL,
                    I18nUtil.i18nCode("att_api_areaCodeNotNull"));
        } else if (areaPerson.getPins() == null || areaPerson.getPins().isEmpty()) {// 人员编号集合为空
            return ApiResultMessage.message(AttApiConstant.PINS_DATA_NOT_NULL,
                    I18nUtil.i18nCode("att_api_pinsNotNull"));
        } else if (areaPerson.getPins().size() > 500) {// 人员编号集合长度超过
            return ApiResultMessage.message(AttApiConstant.PINS_DATA_OVER_SIZE,
                    I18nUtil.i18nCode("att_api_pinsOverSize"));
        }
        return attAreaPersonService.delApiAreaPerson(areaPerson);
    }
}
