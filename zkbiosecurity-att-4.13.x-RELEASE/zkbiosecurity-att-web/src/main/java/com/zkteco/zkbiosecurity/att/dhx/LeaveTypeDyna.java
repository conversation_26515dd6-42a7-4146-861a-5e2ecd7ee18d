/*
 * Project Name: zkbiosecurity-demo-web File Name: DemoDepartmentDyna.java Copyright: Copyright(C) 1985-2018 ZKTeco Inc.
 * All rights reserved.
 */
package com.zkteco.zkbiosecurity.att.dhx;

import com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.base.bean.GridColumnItem;
import com.zkteco.zkbiosecurity.core.utils.DynamicColumn;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class LeaveTypeDyna implements DynamicColumn {

    @Autowired
    private AttLeaveTypeService attLeaveTypeService;

    @Override
    public List<GridColumnItem> getColumn(String fieldName) {
        List<GridColumnItem> columns = new ArrayList<>();
        List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getLeaveTypeItems();
        for (int i = 0; i < attLeaveTypeItemList.size(); i++) {
            AttLeaveTypeItem attLeaveType = attLeaveTypeItemList.get(i);
            String leaveTypeNameUnit = attLeaveTypeService.getConvertUnit(attLeaveType);
            // 次数
            GridColumnItem count = new GridColumnItem();
            count.setName(attLeaveType.getLeaveTypeName() + I18nUtil.i18nCode("att_statistical_numberOfTimes"));
            count.setColumnType("custom");
            count.setConvert("attLeaveViewDetail");
            count.setSort("na");
            count.setSortNo(i * 2 + 7);
            count.setWidth("70");
            count.setLabel(leaveTypeNameUnit);
            count.setSecHeader(I18nUtil.i18nCode("att_statistical_numberOfTimes"));
            count.setFieldName(fieldName);
            columns.add(count);
            // 时长
            GridColumnItem sum = new GridColumnItem();
            sum.setName(leaveTypeNameUnit);
            sum.setColumnType("ro");
            sum.setSort("na");
            sum.setSortNo(count.getSortNo() + 1);
            sum.setWidth("70");
            sum.setLabel("#cspan");
            sum.setSecHeader(I18nUtil.i18nCode("att_common_timeLongs"));
            sum.setFieldName(fieldName);
            columns.add(sum);
        }
        return columns;
    }

}
