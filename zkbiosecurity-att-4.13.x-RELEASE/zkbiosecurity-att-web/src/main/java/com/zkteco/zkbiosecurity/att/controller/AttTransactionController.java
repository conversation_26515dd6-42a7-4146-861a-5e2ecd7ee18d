package com.zkteco.zkbiosecurity.att.controller;

import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.service.AttDayCardDetailService;
import com.zkteco.zkbiosecurity.att.vo.AttDayCardDetailItem;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.bean.AttUsbTransactionBean;
import com.zkteco.zkbiosecurity.att.remote.AttTransactionRemote;
import com.zkteco.zkbiosecurity.att.service.AttPointService;
import com.zkteco.zkbiosecurity.att.service.AttTransactionService;
import com.zkteco.zkbiosecurity.att.util.AttImportUtil;
import com.zkteco.zkbiosecurity.att.vo.AttPhotoExportQueryItem;
import com.zkteco.zkbiosecurity.att.vo.AttTransactionItem;
import com.zkteco.zkbiosecurity.base.annotation.LogChangeRequest;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryValueService;

/**
 * 考勤原始记录、打卡记录
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/11/23 11:10
 * @since 1.0.0
 */
@Controller
public class AttTransactionController extends ExportController implements AttTransactionRemote {

    @Autowired
    private AttTransactionService attTransactionService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private AttPointService attPointService;
    @Autowired
    private BaseDictionaryValueService baseDictionaryValueService;
    @Autowired
    private AttDayCardDetailService attDayCardDetailService;
    @Autowired(required = false)
    private AuthUserService authUserService;

    @Value("${security.session.timeout:1800}")
    private Long sessionTimeout;

    @RequiresPermissions("att:transaction")
    @Override
    public ModelAndView index() {
        request.setAttribute("showSyncAttRecord", attPointService.existOtherAttModule() + "");
        return new ModelAndView("att/transaction/attTransaction");
    }

    @RequiresPermissions("att:transaction:edit")
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("item", attTransactionService.getItemById(id));
        }
        return new ModelAndView("att/transaction/editAttTransaction");
    }

    @RequiresPermissions("att:transaction:edit")
    @LogChangeRequest(module = "att_module", object = "att_leftMenu_transaction", opType = "common_op_new",
        vo = AttTransactionItem.class, service = AttTransactionService.class)
    @Override
    public ZKResultMsg save(AttTransactionItem item) {
        ZKResultMsg res = new ZKResultMsg();
        attTransactionService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("att:transaction:refresh")
    @Override
    public DxGrid list(AttTransactionItem condition) {
        Pager pager = attTransactionService.loadPagerByAuthUserFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize(), getLimitCount());
        return GridUtil.convert(pager, condition.getClass(), getPageList());
    }

    @RequiresPermissions("att:transaction:del")
    @LogRequest(module = "att_module", object = "att_leftMenu_transaction", opType = "common_op_del",
        requestParams = {"ids"}, opContent = "att_common_id")
    @Override
    public ZKResultMsg delete(String ids) {
        attTransactionService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("att:transaction:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_transaction", opType = "common_op_export",
        requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AttTransactionItem attTransactionItem = new AttTransactionItem();
        setConditionValue(attTransactionItem);
        List<AttTransactionItem> intemList = attTransactionService.exportItemListByAuthFilter(
            request.getSession().getId(), AttTransactionItem.class, attTransactionItem, getBeginIndex(), getEndIndex());
        Map<String, String> attVerifyModeMap = baseDictionaryValueService.getDictionaryValuesMap("AttVerifyMode");
        Map<String, Map<String, String>> map = new HashMap<>();
        map.put("attVerify", attVerifyModeMap);
        excelExport(intemList, AttTransactionItem.class, map);
    }

    @Override
    public ZKResultMsg getPhoto(@RequestParam(value = "pin") String pin, @RequestParam(value = "sn") String sn,
        @RequestParam(value = "attDate") String attDate) {
        ZKResultMsg res = new ZKResultMsg();
        String photoDate = attDate.replaceAll("-", "").replaceAll(" ", "").replaceAll(":", "");
        String photoName = photoDate + "-" + pin + ".jpg";
        // 获取考勤照片路径，补丁版本临时先在本模块自己组装 --add by hook.fang 2019-06-26
        String src = "/upload/att/transactionPhoto/" + sn + "/" + attDate.split(" ")[0] + "/" + photoName;
        // String src = "/" + FileUtil.createUploadFileRootPath("att", "transactionPhoto/" + sn) + photoName;
        res.setData(src);
        return res;
    }

    @RequiresPermissions("att:transaction:importUSBRecord")
    @LogRequest(module = "att_module", object = "att_leftMenu_transaction", opType = "att_op_importUSBRecord",
        requestParams = {}, opContent = "att_op_importUSBRecord")
    @Override
    public ZKResultMsg importUSBRecord(MultipartFile upload) {
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("att_import_uploadFileSuccess") + "<br/>"));
            String sn = "";
            if (upload.getOriginalFilename().contains("_")) {
                sn = upload.getOriginalFilename().split("_")[0].toString();
            }
            List<AttUsbTransactionBean> itemList = AttImportUtil.usbImport(upload.getInputStream());
            progress += 10;
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("att_import_resolutionComplete") + "<br/>"));
            return I18nUtil.i18nMsg(attTransactionService.importData(itemList, sn));
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                    + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                    new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("Import Person Info Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
    }


    @Override
    public Boolean isValidFileName(String fileName) {
        // 解析文件名，U盘导入要求文件名必须是SN_*。
        Boolean isDev = false;
        File file = new File(fileName);
        if (file.getName().contains("_")) {
            String sn = file.getName().split("_")[0].toString();
            isDev = attTransactionService.validSn(sn);
        }
        // sn对应的设备不存在，则不允许导入数据
        return isDev;
    }

    @RequiresPermissions("att:transaction:exportAttPhoto")
    @Override
    public void exportAttPhoto() throws Exception {
        // 设置导出结果的状态
        stringRedisTemplate.opsForValue().set(EXPORT_RESULT + request.getSession().getId(), "start");
        stringRedisTemplate.expire(EXPORT_RESULT + request.getSession().getId(), sessionTimeout, TimeUnit.SECONDS);

        // 验证用户登录密码
        if(isNeedValid("pwd")) {
            try {
                String loginPwd = request.getParameter("loginPwd");
                boolean ret = authUserService.verifySecurity(request.getSession().getId(), loginPwd, "pwd");
                if (!ret) {
                    throw new ZKBusinessException("auth_user_pwdIncorrect");
                }
            } catch (ZKBusinessException e) {
                throw e;
            } catch (Exception e) {
                log.error("", e);
            }
        }

        try {

            // 根据时间范围查询图片文件路径集合
            String startDate = request.getParameter("startDate");
            String endDate = request.getParameter("endDate");
            String deviceIds = request.getParameter("deviceIds");

            AttPhotoExportQueryItem condition = new AttPhotoExportQueryItem();
            condition.setBeginDate(DateUtil.stringToDate(startDate));
            condition.setEndDate(DateUtil.stringToDate(endDate));
            condition.setInDeviceId(deviceIds);
            // 图片路径集合
            List<AttPhotoExportQueryItem> attPhotoItems =
                attTransactionService.getAllAttPhotoListByAuthUserFilter(request.getSession().getId(), condition);

            // 图片文件集合
            List<AttPhotoExportQueryItem> exportFileList = new ArrayList<>();
            // 文件大小 (单位:byte)
            Long fileSizeTotal = 0L;
            for (AttPhotoExportQueryItem item : attPhotoItems) {
                File file = new File(FileUtils.getLocalFullPath(item.getFilePath()));
                if (file.exists()) {
                    item.setFilePath(file.getAbsolutePath());
                    exportFileList.add(item);
                    fileSizeTotal += file.length();
                }
            }
            // 文件数量为0，不进行导出。
            if (exportFileList.size() == 0) {
                throw ZKBusinessException.warnException("common_report_dataSourceNull");
            }

            // 如果文件总的大小超过 500M/压缩率(20%-80%) 暂设 1G 且 选择多天的 days>1 提示缩小时间范围导出
            if (fileSizeTotal > 1 * 1024 * 1024 * 1024) {
                throw ZKBusinessException.warnException(I18nUtil.i18nCode("att_transaction_fileIsTooLarge"));
            }
            // 导出压缩包名称前缀
            String fileNamePrefix = I18nUtil.i18nCode("att_statistical_attPhoto");
            String name = fileNamePrefix;

            /*----------页面reponse设置----------*/
            String agent = request.getHeader("User-Agent");
            boolean isMSIE = (agent != null && (agent.indexOf("MSIE") != -1 || agent.indexOf("Trident") != -1));
            // IE浏览器
            if (isMSIE) {
                fileNamePrefix = java.net.URLEncoder.encode(fileNamePrefix, "UTF-8");
                // IE会将空格转成+号
                fileNamePrefix = fileNamePrefix.replaceAll("\\+", " ");
            } else {
                fileNamePrefix = new String(fileNamePrefix.getBytes("UTF-8"), "ISO-8859-1");
            }
            // Edge浏览器兼容处理
            if (request.getHeader("User-Agent") != null && request.getHeader("User-Agent").indexOf("Edge") != -1) {
                // 空格预处理
                fileNamePrefix = fileNamePrefix.replaceAll(" ", "%20");
                // 编码转换
                fileNamePrefix = URLEncoder.encode(fileNamePrefix, "ISO-8859-1");
                // 空格还原
                fileNamePrefix = fileNamePrefix.replace("%2520", " ");
            }
            String isEncrypt = request.getParameter("isEncrypt");
            if (StringUtils.isNotBlank(isEncrypt) && (!ExcelUtil.ENCRYPT_PASSWORD_YES.equals(isEncrypt))) {
                name = fileNamePrefix;
            }

            // 导出的压缩包路径 RootPath/upload/att/fileNamePrefix_yyyyMMdd_yyyyMMdd.zip
            String tempPath = (ClassUtil.getRootPath() + File.separator + FileUtils.systemFilePath + File.separator
                    + "upload" + File.separator + "att").replace("/", File.separator) + File.separator + name + "_"
                    + DateUtil.dateToString(condition.getBeginDate(), DateUtil.DateStyle.YYYYMMDDHHMMSS) + "_"
                    + DateUtil.dateToString(condition.getEndDate(), DateUtil.DateStyle.YYYYMMDDHHMMSS) + ".zip";

            File zipTempFile = new File(tempPath);
            if (!zipTempFile.exists()) {
                zipTempFile.createNewFile();
            }

            // 解密照片存储的临时路径
            String tempDecryptPath = AttConstant.TRANSACTION_PHOTP_PTAH + File.separator + "export_decrypt";
            response.reset();

            FileOutputStream fileOutputStream = new FileOutputStream(zipTempFile);
            ZipOutputStream zipOutputStream = new ZipOutputStream(fileOutputStream);
            try {
                File file;
                for (AttPhotoExportQueryItem item : exportFileList) {
                    String compressDir = item.getCompressDirectory();
                    file = new File(item.getFilePath());
                    String fileName = file.getName();
                    ZipEntry zipEntry = new ZipEntry(compressDir + fileName);

                    String photoPath = item.getFilePath()
                            .substring(item.getFilePath().indexOf(File.separator + "upload"));
                    byte[] decPhoto = FileEncryptUtil.getDecryptFile(photoPath);
                    if (Objects.nonNull(decPhoto)) {
                        // 保存解密后的图片
                        String decryFilePath =
                                FileEncryptUtil.saveByte2File(tempDecryptPath + File.separator + fileName, decPhoto);
                        file = new File(FileUtils.getLocalFullPath(decryFilePath));
                    }

                    FileInputStream fileInputStream = new FileInputStream(file);
                    BufferedInputStream bufferedInputStream = new BufferedInputStream(fileInputStream, 512);
                    zipOutputStream.putNextEntry(zipEntry);
                    try {
                        byte[] buffer = new byte[512];
                        int index;
                        while ((index = bufferedInputStream.read(buffer)) != -1) {
                            zipOutputStream.write(buffer, 0, index);
                        }
                    } finally {
                        if (null != bufferedInputStream) {
                            bufferedInputStream.close();
                        }
                        if (null != fileInputStream) {
                            fileInputStream.close();
                        }
                    }
                }
            } finally {
                if (null != zipOutputStream) {
                    zipOutputStream.close();
                }
                if (null != fileOutputStream) {
                    fileOutputStream.close();
                }
            }
            // 导出压缩包下载
            try {

                if (StringUtils.isNotBlank(isEncrypt) && ExcelUtil.ENCRYPT_PASSWORD_YES.equals(isEncrypt)) {
                    ArrayList<File> files = new ArrayList<>();
                    files.add(zipTempFile);
                    String zipPath = (ClassUtil.getRootPath() + File.separator + FileUtils.systemFilePath
                            + File.separator + "upload").replace("/", File.separator) + File.separator + fileNamePrefix
                            + "_" + DateUtil.dateToString(condition.getBeginDate(), DateUtil.DateStyle.YYYYMMDDHHMMSS) + "_"
                            + DateUtil.dateToString(condition.getEndDate(), DateUtil.DateStyle.YYYYMMDDHHMMSS) + ".zip";
                    FileUtil.fileEncryptZip(zipPath, files, request.getParameter("encryptPassword"), request, response);
                    // 删除临时文件
                    File file = new File(zipPath);
                    if (file.exists()) {
                        file.delete();
                    }
                } else {
                    FileUtils.downloadZip(zipTempFile, response);
                }
                response.flushBuffer();
            } finally {
                // 删除临时文件夹
                if (zipTempFile.exists()) {
                    // 删除临时压缩包
                    zipTempFile.delete();
                }
                // 删除解密临时文件夹
                FileUtil.deleteDirectory(FileUtil.getLocalFullPath(tempDecryptPath));
            }

        } finally {
            // 导出结束
            stringRedisTemplate.opsForValue().set(EXPORT_RESULT + request.getSession().getId(), "end");
        }
    }

    @Override
    public ZKResultMsg syncAttRecord(String attPointIds, Date startDatetime, Date endDatetime) {
        ZKResultMsg res = attTransactionService.syncAttRecord(attPointIds, startDatetime, endDatetime);
        return I18nUtil.i18nMsg(res);
    }

    @Override
    public void getDecryptFileBase64(String path) {
        if (StringUtils.isNotBlank(path)) {
            String decryptFileBase64 = FileEncryptUtil.getDecryptFileBase64(path);
            if (GridUtil.isNeedEncrypt("att:photo:encryptProp")) {
                decryptFileBase64 = ImgEncodeUtil.base64BoxBlurFilter(decryptFileBase64);
            }
            try {
                ServletOutputStream outputStream = response.getOutputStream();
                response.reset();
                outputStream.write(Base64.getDecoder().decode(decryptFileBase64));
            } catch (IOException e) {
                log.error("exception = ", e);
            }
        }
    }

    @Override
    public DxGrid getDayCardDetailView(String attDayCardDetailId, String attDate) {
        AttTransactionItem condition = new AttTransactionItem();
        condition.setAttDate(attDate);
        condition.setEquals(true);
        AttDayCardDetailItem attDayCardDetailItem = attDayCardDetailService.getItemById(attDayCardDetailId);
        if (attDayCardDetailItem != null) {
            condition.setPersonPin(attDayCardDetailItem.getPersonPin());
        } else {
            condition.setPersonPin("-1");
         }
        Pager pager = attTransactionService.loadPagerByAuthUserFilter(request.getSession().getId(), condition,
                getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }
}