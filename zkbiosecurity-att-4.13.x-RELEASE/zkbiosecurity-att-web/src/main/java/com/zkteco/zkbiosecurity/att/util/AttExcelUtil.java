/**
 * <AUTHOR>
 * @date 2020/5/13 9:26
 */
package com.zkteco.zkbiosecurity.att.util;

import java.io.*;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections.map.LinkedMap;
import org.apache.poi.common.usermodel.HyperlinkType;
import org.apache.poi.hssf.usermodel.HSSFClientAnchor;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import com.zkteco.zkbiosecurity.base.annotation.DateType;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 9:26 2020/5/13
 */
@Slf4j
@Component
public class AttExcelUtil {

    public final static String XLS = "xls";
    public final static String XLSX = "xlsx";
    public final static String XSLM = "xlsm";
    public final static String PDF = "pdf";
    public final static String CSV = "csv";
    public final static String TXT = "txt";

    public final static String IMPORT_RESULT = "import-result:";
    public final static String EXPORT_RESULT = "export-result:";
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Value("${security.session.timeout:1800}")
    private Long sessionTimeout;

    private final static Logger loger = LoggerFactory.getLogger(AttExcelUtil.class);

    /**
     * 动态表头数据导入
     * 
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/13 11:50
     * @param inputStream
     * @param cls
     * @param dynamicFieldName
     *            动态表头item的字段名称
     * @param dynamicMap
     *            动态表头的固定Map格式
     * @param dynamicRegex
     *            动态表头的非固定Map格式，需要传表头正则匹配
     * @return java.util.List<T>
     */
    public <T> List<T> excelImportDynamic(InputStream inputStream, Class<T> cls, String dynamicFieldName,
        Map<String, String> dynamicMap, String dynamicRegex) {
        Workbook workbook = createImportWorkBook(inputStream);
        Sheet sheet = workbook.getSheetAt(0);
        Field[] fields = cls.getDeclaredFields();
        List<Field> fieldList =
            Arrays.stream(fields).filter(f -> f.getAnnotation(GridColumn.class) != null).collect(Collectors.toList());
        Row columnNames = sheet.getRow(1);
        List<T> list = new ArrayList();
        try {
            for (Row row : sheet) {
                if (row.getRowNum() > 1) {
                    T t = cls.newInstance();
                    Map<String, Object> attrMap = new HashMap();
                    for (Cell cell : row) {
                        Cell columnNameCell = columnNames.getCell(cell.getColumnIndex());
                        if (columnNameCell != null) {
                            columnNameCell.setCellType(CellType.STRING);
                            if (StringUtils.isEmpty(columnNameCell.getStringCellValue())) {
                                throw new ZKBusinessException("common_report_columnError");
                            }
                            String columnName = columnNameCell.getStringCellValue();

                            Field field = null;
                            if (dynamicMap != null) {
                                if (dynamicMap.containsKey(columnName)) {
                                    field = cls.getDeclaredField(dynamicFieldName);
                                }
                            }
                            if (dynamicRegex != null) {
                                boolean flag = Pattern.matches(dynamicRegex, columnName);
                                if (flag) {
                                    field = cls.getDeclaredField(dynamicFieldName);
                                }
                            }
                            if (field == null) {
                                field = fieldList.stream()
                                    .filter(f -> columnName
                                        .equals(I18nUtil.i18nCode(f.getAnnotation(GridColumn.class).label())))
                                    .findFirst()
                                    .orElseThrow(() -> new ZKBusinessException("common_report_columnError"));
                            }

                            field.setAccessible(true);
                            if (field.getType() == Date.class || field.getType() == Timestamp.class) {
                                DateType dateType = field.getAnnotation(DateType.class);
                                String format = ExcelUtil.getDateFormt(dateType);

                                if (cell.getCellType() == CellType.NUMERIC) {
                                    if (cell.getNumericCellValue() > 0) {
                                        field.set(t, org.apache.poi.ss.usermodel.DateUtil
                                            .getJavaDate(cell.getNumericCellValue()));
                                    }
                                } else {
                                    cell.setCellType(CellType.STRING);
                                    if (!StringUtils.isEmpty(cell.getStringCellValue())) {
                                        field.set(t, DateUtil.stringToDate(cell.getStringCellValue(), format));
                                    }
                                }

                            }
                            if (Map.class.equals(field.getType())) {
                                cell.setCellType(CellType.STRING);
                                if (!StringUtils.isEmpty(cell.getStringCellValue())) {
                                    attrMap.put(columnName, cell.getStringCellValue());
                                    field.set(t, ConvertUtils.convert(attrMap, field.getType()));
                                }
                            } else {
                                cell.setCellType(CellType.STRING);
                                if (!StringUtils.isEmpty(cell.getStringCellValue())) {
                                    field.set(t, ConvertUtils.convert(cell.getStringCellValue(), field.getType()));
                                }
                            }
                        }
                    }
                    list.add(t);
                }
            }
        } catch (ZKBusinessException e) {
            loger.error("import business error", e);
            throw e;
        } catch (Exception e) {
            loger.error("import error", e);
            throw new ZKBusinessException("common_op_failed");
        }
        return list;
    }

    /**
     * 创建工作目录
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/13 9:28
     * @param inputStream
     * @return org.apache.poi.ss.usermodel.Workbook
     */
    private Workbook createImportWorkBook(InputStream inputStream) {
        Workbook workbook = null;
        try {
            workbook = getImportWorkBook(inputStream);
        } catch (Exception ex) {
            throw new ZKBusinessException("Open the EXCEL file flow failure!", ex);
        }
        return workbook;
    }

    /**
     * 根据不同版本的office软件创建不同excel文件
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/13 9:28
     * @param inputStream
     * @return org.apache.poi.ss.usermodel.Workbook
     */
    private Workbook getImportWorkBook(InputStream inputStream) throws IOException {
        Workbook workbook = null;
        // 输入流必须支持mark/reset方法
        if (!inputStream.markSupported()) {
            inputStream = new PushbackInputStream(inputStream, 8);
        }
        // 使用微软Office文件系统,Excel2003
        // if (POIFSFileSystem.hasPOIFSHeader(inputStream)) {
        // workbook = new HSSFWorkbook(inputStream);
        // } else if (DocumentFactoryHelper.hasOOXMLHeader(inputStream)) {
        // workbook = new XSSFWorkbook(inputStream);
        // }
        workbook = WorkbookFactory.create(inputStream);

        return workbook;
    }

    /**
     * @param list
     *            listBean数据
     * @param cls
     *            bean class
     * @param map
     *            key为vo中需要转化字典的字段，value为该字段的字典数据
     * @return void
     * @Description: 数据中需要字典转换的导出
     * <AUTHOR>
     * @date 2018/5/9 15:14
     */
    public <T> void attExcelExport(HttpServletRequest request, HttpServletResponse response, List<T> list, Class<T> cls,
        Map<String, Map<String, String>> map) {
        // if (list == null || 0 == list.size()) {
        // throw new ZKBusinessException("common_report_dataSourceNull");
        // }
        String reportType = request.getParameter("reportType");
        String jsonColumn = request.getParameter("jsonColumn");
        String fileName = request.getParameter("tableNameParam");
        String tableName = request.getParameter("tableNameSearch");
        // 增加自定义列名
        if (map != null && map.get("jsonColumn") != null) {
            Map<String, String> jsonColumnMap = map.get("jsonColumn");
            jsonColumn = jsonColumnMap.get("jsonColumn");
        }
        // 双列表的导出文件名取表名
        if (!org.apache.commons.lang3.StringUtils.isBlank(tableName)) {
            fileName = tableName;
        }
        // 增加自定义表名
        if (map != null && map.get("tableName") != null) {
            Map<String, String> tableNameMap = map.get("tableName");
            fileName = tableNameMap.get("tableName");
        }
        String gridName = fileName;
        if (org.apache.commons.lang3.StringUtils.isBlank(reportType)) {
            // 默认导出07
            reportType = ExcelUtil.XLSX;
        }
        // 文件后缀转为小写
        reportType = reportType.toLowerCase();
        try {
            String agent = request.getHeader("User-Agent").toLowerCase();
            if (request.getHeader("User-Agent") != null && agent.indexOf("edge") != -1) {// Edge浏览器兼容处理
                fileName = fileName.replaceAll(" ", "%20");// 空格预处理

                // 修复edge中文乱码问题 ----modify by train.chen 2020-1-20 11:01:12
                fileName = URLEncoder.encode(fileName, "UTF-8");// 编码转换
                fileName = fileName.replace("%2520", " ");// 空格还原
            } else if (agent != null
                && (agent.indexOf("msie") != -1 || (agent.indexOf("rv") != -1 && agent.indexOf("firefox") == -1))) {
                // fileName = new String(fileName.getBytes("gbk"), "ISO-8859-1");

                // ----------------处理IE下海外语言乱码问题 --------------modify by train.chen 2019-06-10
                fileName = fileName.replaceAll(" ", "%20");// 空格预处理
                fileName = URLEncoder.encode(fileName, "UTF-8");// 编码转换
                fileName = fileName.replace("%2520", " ");// 空格还原
                // -------------------------------------------------------------------------------------

            } else {
                fileName = new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
            }
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "_"
                + (new SimpleDateFormat("yyyyMMddHHmmss")).format(new Date()) + "." + reportType + "\"");
            response.setContentType("application/octet-stream");
            OutputStream os = response.getOutputStream();
            attExcelExport(list, cls, jsonColumn, gridName, os, reportType, map);
            os.flush();

        } catch (Exception e) {
            loger.error(fileName + " export error", e);
            response.setHeader("Content-Disposition", "");
            response.setContentType("application/json");
            throw new ZKBusinessException("common_report_exportFaild");
        } finally {
            stringRedisTemplate.opsForValue().set(EXPORT_RESULT + request.getSession().getId(), "end");
            stringRedisTemplate.expire(EXPORT_RESULT + request.getSession().getId(), sessionTimeout, TimeUnit.SECONDS);
        }
    }

    private <T> void attExcelExport(List<T> list, Class<T> cls, String jsonColumn, String fileName,
        OutputStream outputStream, String reportType, Map<String, Map<String, String>> map) throws IOException {
        OutputStream ops = new BufferedOutputStream(outputStream, 8192);
        Workbook workbook = null;
        // 创建xls或者xlsx
        if (XLS.equals(reportType)) {
            workbook = new HSSFWorkbook();
        } else {
            workbook = new XSSFWorkbook();
        }
        Sheet sheet = workbook.createSheet(fileName);
        Drawing patriarch = sheet.createDrawingPatriarch();
        JSONObject jsonObject = JSONObject.parseObject(jsonColumn, Feature.OrderedField);
        Map<String, Object> extraDataMap = new LinkedMap();
        List<String> fieldList = new ArrayList();
        String extraDataName = "";
        try {
            for (String s : jsonObject.keySet()) {
                Field field = cls.getDeclaredField(s);
                field.setAccessible(true);
                if (field.getAnnotation(GridColumn.class) != null
                    && !field.getAnnotation(GridColumn.class).isExportExcel()) {
                    continue;
                }
                if (("java.util.Map").equals(cls.getDeclaredField(s).getType().getTypeName())) {
                    // 自定义可扩展字段处理（表数据）
                    extraDataMap = (Map<String, Object>)field.get(list.get(0));
                    extraDataName = s;
                    for (Map.Entry<String, Object> entry : extraDataMap.entrySet()) {
                        fieldList.add(entry.getKey());
                    }
                } else {
                    fieldList.add(s);
                }
            }

            // 标题格式
            CellStyle headStyle = workbook.createCellStyle();
            Font headFont = workbook.createFont();
            headFont.setFontName("ARIAL");
            headFont.setFontHeightInPoints((short)14);
            headFont.setBold(true);
            headStyle.setFont(headFont);
            headStyle.setAlignment(HorizontalAlignment.CENTER);
            headStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 列名格式
            CellStyle titleStyle = workbook.createCellStyle();
            Font titleFont = workbook.createFont();
            titleFont.setFontName("ARIAL");
            titleFont.setFontHeightInPoints((short)13);
            titleStyle.setFont(titleFont);
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 字段格式
            CellStyle cellStyle = workbook.createCellStyle();
            cellStyle.setAlignment(HorizontalAlignment.LEFT);
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            cellStyle.setWrapText(true);

            // 合并第一行单元格（标题）
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, fieldList.size() - 1));
            int isMergedRegion = 2;
            Row headRow = sheet.createRow(0);
            Cell headCell = headRow.createCell(0);
            headRow.setHeightInPoints(20);
            headRow.setHeight((short)600);
            headCell.setCellStyle(headStyle);
            headCell.setCellValue(fileName);

            // 列名
            Row titleRow = sheet.createRow(1);
            titleRow.setHeightInPoints(20);
            titleRow.setHeight((short)500);

            // 扩展列名
            Row titleRowEx = sheet.createRow(2);
            int mergedRow = 0;
            int mergedRowj = 0;

            // 遍历字段，填充列表
            for (int i = 0; i < fieldList.size(); i++) {
                String width = "";
                String columValue = "";
                String secHeader = "";
                if (!extraDataMap.containsKey(fieldList.get(i))) {
                    width = cls.getDeclaredField(fieldList.get(i)).getAnnotation(GridColumn.class).width();
                    columValue = cls.getDeclaredField(fieldList.get(i)).getAnnotation(GridColumn.class).label();
                    secHeader = cls.getDeclaredField(fieldList.get(i)).getAnnotation(GridColumn.class).secHeader();
                } else {
                    // 自定义扩展字段导出时设置默认宽度90 add by alex.yang 2019-08-01 14:27:35
                    width = "90";
                    columValue = fieldList.get(i);
                }
                if (StringUtils.isEmpty(columValue)) {
                    columValue = cls.getDeclaredField(fieldList.get(i)).getName();
                }

                if (!StringUtils.isEmpty(width) && !"*".equals(width)) {
                    sheet.setColumnWidth(i, 50 * Integer.parseInt(width));
                }
                Cell columCell = titleRow.createCell(i);
                columCell.setCellStyle(titleStyle);
                // 增加合并单元格的情况 start
                if (!"".equals(secHeader) && !"#cspan".equals(columValue)) {
                    Cell columCell3 = titleRowEx.createCell(i);
                    columCell3.setCellStyle(titleStyle);
                    columCell3.setCellValue(I18nUtil.i18nCode(secHeader));
                }
                if (!"".equals(secHeader) && "#cspan".equals(columValue)) {
                    mergedRow = mergedRow + 1;
                    mergedRowj = mergedRowj + 1;
                    columCell = titleRowEx.createCell(i);
                    columValue = secHeader;
                    isMergedRegion = 3;
                } else {
                    if (mergedRowj != 0) {
                        CellRangeAddress region = new CellRangeAddress(1, 1, i - mergedRowj - 1, i - 1);
                        sheet.addMergedRegion(region);
                    }
                    mergedRowj = 0;
                }
                if (mergedRowj != 0 && i == fieldList.size() - 1) {
                    CellRangeAddress region = new CellRangeAddress(1, 1, i - mergedRowj, i);
                    sheet.addMergedRegion(region);
                }
                if ("".equals(secHeader) && mergedRow != 0) {
                    CellRangeAddress region = new CellRangeAddress(1, 2, i, i);
                    sheet.addMergedRegion(region);
                }
                // 增加合并单元格的情况 end

                // 批注
                Comment comment =
                    patriarch.createCellComment(new HSSFClientAnchor(0, 0, 0, 0, (short)1, 2, (short)4, 8));
                comment.setString(
                    new HSSFRichTextString(getCellCommentByField(cls.getDeclaredField(fieldList.get(i)).getName(), cls.getSimpleName())));
                columCell.setCellComment(comment);

                columCell.setCellStyle(titleStyle);
                columCell.setCellValue(I18nUtil.i18nCode(columValue));
            }

            // 数据
            String cellValue = "";
            CreationHelper creationHelper = workbook.getCreationHelper();
            for (int i = 0; i < list.size(); i++) {
                Row dataRows = sheet.createRow(i + isMergedRegion);
                for (int j = 0; j < fieldList.size(); j++) {
                    Cell dataCell = dataRows.createCell(j);
                    dataCell.setCellStyle(cellStyle);
                    if (!extraDataMap.containsKey(fieldList.get(j))) {
                        Field field = cls.getDeclaredField(fieldList.get(j));
                        field.setAccessible(true);
                        Object cellObject = field.get(list.get(i));
                        // 增加图片的url连接
                        if (field.getName().contains("picture") && cellObject != null) {
                            Hyperlink hyperLink = creationHelper.createHyperlink(HyperlinkType.FILE);
                            hyperLink.setAddress((String)cellObject);
                            dataCell.setHyperlink(hyperLink);
                        }
                        if (!StringUtils.isEmpty(cellObject)) {
                            if (field.getType() == Date.class || field.getType() == Timestamp.class) {
                                DateType dateType = field.getAnnotation(DateType.class);
                                String format = getDateFormt(dateType);
                                cellValue = new SimpleDateFormat(format).format(cellObject);

                            } else if (map != null && map.containsKey(field.getName())) {
                                // 增加带自定义字典的处理
                                cellValue = String.valueOf(cellObject);
                                if (map.get(field.getName()).containsKey(cellValue)) {
                                    cellValue = I18nUtil.i18nCode(map.get(field.getName()).get(cellValue));
                                }
                            } else {
                                cellValue = String.valueOf(cellObject);
                                // 如果数据是key值，则需要国际化
                                if (field.getAnnotation(GridColumn.class).i18n()) {
                                    cellValue = I18nUtil.i18nCode(cellValue);
                                }
                                // 通用字典处理
                                if (!StringUtils.isEmpty(field.getAnnotation(GridColumn.class).format())) {
                                    cellValue = I18nUtil.i18nCode(GridUtil
                                        .getDictionaryVal(field.getAnnotation(GridColumn.class).format(), cellValue));
                                }
                            }
                        } else {
                            cellValue = "";
                        }
                    } else {
                        // 可扩展字段导出
                        Field field = cls.getDeclaredField(extraDataName);
                        field.setAccessible(true);
                        Map extraDataMaps = (Map<String, Object>)field.get(list.get(i));
                        cellValue = String.valueOf(extraDataMaps.get(fieldList.get(j)));
                    }
                    dataCell.setCellValue(cellValue);
                    if (StringUtils.isEmpty(cellValue) || "null".equals(cellValue)) {
                        dataCell.setCellValue("");
                    }

                }
            }

            if (PDF.equals(reportType)) {
                ExecltoPdf(workbook, ops);
            } else if (CSV.equals(reportType) || TXT.equals(reportType)) {
                ExecltoCsv(workbook, ops);
            } else {
                workbook.write(ops);
            }
        } catch (Exception e) {
            loger.error("", e);
        }
        ops.flush();
        ops.close();
        outputStream.close();
    }

    /**
     * @return java.io.InputStream
     * @Description: 把EXECL的输入流转化为pdf的输入流
     * <AUTHOR>
     * @date 2018/5/2 10:33
     */
    @SuppressWarnings("deprecation")
    private void ExecltoPdf(Workbook workbook, OutputStream out) throws DocumentException, FileNotFoundException {
        try {
            // 1.新建document对象
            Document iText_xls_2_pdf = new Document(PageSize.A3, 50, 50, 50, 50);

            // 2.建立一个书写器(Writer)与document对象关联，通过书写器(Writer)可以将文档写入到磁盘中。
            PdfWriter writer = PdfWriter.getInstance(iText_xls_2_pdf, out);
            AttExcelUtil.Header header = new AttExcelUtil.Header();
            writer.setPageEvent(header);
            // BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            // com.itextpdf.text.Font font = new com.itextpdf.text.Font(bfChinese, 10, com.itextpdf.text.Font.NORMAL);
            // 3.打开文档
            iText_xls_2_pdf.open();

            // 得到execl表总表数
            int sheetSize = workbook.getNumberOfSheets();
            PdfPCell table_cell;
            for (int i = 0; i < sheetSize; i++) {
                Sheet my_worksheet = workbook.getSheetAt(i);
                Iterator<Row> rowIterator = my_worksheet.iterator();

                while (rowIterator.hasNext()) {
                    Row row = rowIterator.next();
                    // 每行的列数
                    int num = row.getPhysicalNumberOfCells();
                    // 如果当前的列数为0就跳过当次循环
                    if (num == 0) {
                        continue;
                    }
                    PdfPTable my_table = new PdfPTable(num);
                    my_table.setWidthPercentage(100);
                    int[] widths = new int[num];
                    Iterator<Cell> cellIterator = row.cellIterator();
                    while (cellIterator.hasNext()) {
                        Cell cell = cellIterator.next();
                        switch (cell.getCellType()) {
                            case STRING:
                                table_cell = new PdfPCell(new Paragraph(cell.getStringCellValue(),
                                    ZKBaseFontUtil.getBodyFont(cell.getStringCellValue())));
                                table_cell.setHorizontalAlignment(Element.ALIGN_CENTER);
                                // 列名背景色改为浅灰色
                                if (1 == cell.getRowIndex()) {
                                    table_cell.setBackgroundColor(new BaseColor(217, 217, 217));
                                } else if (0 == cell.getRowIndex()) {// 标题字体改大
                                    table_cell = new PdfPCell(new Paragraph(cell.getStringCellValue(),
                                        ZKBaseFontUtil.getTitleFont(cell.getStringCellValue())));
                                    table_cell.setBorder(0);
                                    table_cell.setPaddingBottom(20);
                                    table_cell.setHorizontalAlignment(Element.ALIGN_CENTER);
                                }
                                widths[cell.getColumnIndex()] = my_worksheet.getColumnWidth(cell.getColumnIndex());
                                my_table.addCell(table_cell);
                                break;
                            default:
                                break;
                        }
                    }
                    my_table.setWidths(widths);
                    iText_xls_2_pdf.add(my_table);
                }
            }
            iText_xls_2_pdf.close();
            writer.close();
        } catch (Exception e) {
            log.error("exception = ", e);
        }
    }

    /**
     * @param workbook
     * @param out
     * @return void
     * @Description: 导出csv
     * <AUTHOR>
     * @date 2018/5/2 14:24
     */
    @SuppressWarnings("deprecation")
    private void ExecltoCsv(Workbook workbook, OutputStream out) throws IOException {

        // 修复cvs导出中文乱码问题 -----modify by train.chen 2020-5-12 11:45:21
        byte[] uft8bom = {(byte)0xef, (byte)0xbb, (byte)0xbf};
        out.write(uft8bom);
        OutputStreamWriter osw = new OutputStreamWriter(out, "UTF-8");

        BufferedWriter bw = new BufferedWriter(osw);
        Sheet my_worksheet = workbook.getSheetAt(0);
        Iterator<Row> rowIterator = my_worksheet.iterator();
        try {
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                // 每行的列数
                int num = row.getPhysicalNumberOfCells();
                // 如果当前的列数为0就跳过当次循环
                if (num == 0) {
                    continue;
                }
                Iterator<Cell> cellIterator = row.cellIterator();
                while (cellIterator.hasNext()) {
                    Cell cell = cellIterator.next();
                    switch (cell.getCellType()) {
                        case STRING:
                            // 决解数据内部是逗号隔开的数据，导致导出csv数据错乱 modify by alex.yang 2019-08-01 14:28:56
                            bw.append("\"" + cell.getStringCellValue() + "\"");
                            bw.append(",");
                            break;
                        default:
                            break;
                    }
                }
                bw.append(System.getProperty("line.separator"));
            }
            bw.flush();
            out.close();
            bw.close();
            osw.close();
        } catch (IOException e) {
            log.error("exception = ", e);
        }
    }

    private static class Header extends PdfPageEventHelper {
        @Override
        public void onEndPage(PdfWriter pdfWriter, Document document) {
            ColumnText.showTextAligned(pdfWriter.getDirectContent(), Element.ALIGN_RIGHT,
                new Phrase("Page:" + pdfWriter.getCurrentPageNumber()), 450, 30, 0);
        }

    }

    private String getDateFormt(DateType dateType) {
        String format = "yyyy-MM-dd HH:mm:ss";
        if (dateType != null) {
            switch (dateType.type()) {
                case "date":
                    format = "yyyy-MM-dd";
                    break;
                case "time":
                    format = "HH:mm:ss";
                    break;
                case "timestamp":
                    format = "yyyy-MM-dd HH:mm:ss";
                    break;
                default:
                    break;
            }
        }
        return format;
    }

    private String getCellCommentByField(String field, String className) {
        String cellComment = I18nUtil.i18nCode("att_exception_cellDefault");
        switch (field) {
            case "pin":
            case "personPin":
            case "authAreaNo":
            case "adjustPersonPin":
                cellComment = I18nUtil.i18nCode("att_exception_cellRequired");
                break;
            case "signDatetime":
            case "startDatetime":
            case "endDatetime":
                cellComment = I18nUtil.i18nCode("att_exception_cellDateTime");
                break;
            case "leaveTypeName":
                cellComment = I18nUtil.i18nCode("att_exception_cellLeaveTypeName");
                break;
            case "overtimeSign":
                cellComment = I18nUtil.i18nCode("att_exception_cellOvertimeSign");
                break;
            case "adjustType":
                if ("AttClassItem".equals(className)) {
                    cellComment = MessageFormat.format(I18nUtil.i18nCode("att_exception_cellClassAdjustType"),
                            I18nUtil.i18nCode("att_class_sameTimeMoveShift"),
                            I18nUtil.i18nCode("att_class_differenceTimeMoveShift"),
                            I18nUtil.i18nCode("att_class_twoPeopleMove")) ;
                } else {
                    cellComment = I18nUtil.i18nCode("att_exception_cellAdjustType");
                }
                break;
            case "adjustDate":
                cellComment = I18nUtil.i18nCode("att_exception_cellAdjustDate");
                break;
            case "swapDate":
                cellComment = I18nUtil.i18nCode("att_exception_swapDateDate");
                break;
            case "shiftName":
                cellComment = I18nUtil.i18nCode("att_exception_cellShiftName");
                break;
            default:
                break;
        }
        return cellComment;
    }
}
