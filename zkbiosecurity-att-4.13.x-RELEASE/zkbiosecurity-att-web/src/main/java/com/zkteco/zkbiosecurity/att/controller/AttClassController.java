package com.zkteco.zkbiosecurity.att.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.util.AttExcelUtil;
import com.zkteco.zkbiosecurity.core.utils.LocaleMessageSourceUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.remote.AttClassRemote;
import com.zkteco.zkbiosecurity.att.service.AttClassService;
import com.zkteco.zkbiosecurity.att.util.AttImportWithRowNumUtil;
import com.zkteco.zkbiosecurity.att.vo.AttClassItem;
import com.zkteco.zkbiosecurity.base.annotation.LogChangeRequest;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 调班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:06
 * @since 1.0.0
 */
@Controller
public class AttClassController extends ExportController implements AttClassRemote {

    @Autowired
    private AttClassService attClassService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private AttExcelUtil attExcelUtil;

    @RequiresPermissions("att:class")
    @Override
    public ModelAndView index() {
        if (getCurrentSubject().getStaff()) {
            return new ModelAndView("att/flowable/classShift/attClassShift");
        } else {
            return new ModelAndView("att/class/attClass");
        }
    }

    @RequiresPermissions("att:class:add")
    @Override
    public ModelAndView edit(String id) {
        SecuritySubject securitySubject = getCurrentSubject();
        Boolean staff = securitySubject.getStaff();

        AttClassItem item = new AttClassItem();
        if (StringUtils.isNotBlank(id)) {
            item = attClassService.getItemById(id);
        } else if (staff == null || !staff) {
            item.setFlowStatus(AttConstant.FLOW_STATUS_COMPLETE);
        }
        request.setAttribute("item", item);
        return new ModelAndView("att/class/editAttClass");
    }

    @RequiresPermissions("att:class:add")
    @LogChangeRequest(module = "att_module", object = "att_leftMenu_class", opType = "common_op_new",
        vo = AttClassItem.class, service = AttClassService.class)
    @Override
    public ZKResultMsg save(AttClassItem item) {
        ZKResultMsg res = attClassService.saveItem(item, request.getSession().getId());
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("att:class:refresh")
    @Override
    public DxGrid list(AttClassItem condition) {
        String sessionId = request.getSession().getId();
        Pager pager = attClassService.loadPagerByAuthUserFilter(sessionId, condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:class:del")
    @LogRequest(module = "att_module", object = "att_leftMenu_class", opType = "common_op_del",
        requestParams = {"pins"}, opContent = "att_person_pin")
    @Override
    public ZKResultMsg delete(String ids) {
        attClassService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("att:class:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_class", opType = "common_op_export",
        requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AttClassItem attClassItem = new AttClassItem();
        setConditionValue(attClassItem);
        List<AttClassItem> intemList = attClassService.getItemData(request.getSession().getId(),
            attClassItem, getBeginIndex(), getEndIndex());
        excelExport(intemList, AttClassItem.class);
    }

    @Override
    public ZKResultMsg importExcel(MultipartFile upload) throws IOException {
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_uploadFileSuccess") + "<br/>"));
            List<AttClassItem> itemList =
                AttImportWithRowNumUtil.excelImport(upload.getInputStream(), AttClassItem.class);
            progress += 10;
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_resolutionComplete") + "<br/>"));
            return I18nUtil.i18nMsg(attClassService.importExcel(itemList, request.getSession().getId()));
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                    + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                    new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("Import AttClass Info Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
    }

    @Override
    public void importTemplate(HttpServletRequest request, HttpServletResponse response) {
        JSONObject jsonColumn = new JSONObject(true);
        jsonColumn.put("adjustPersonPin", ""); // 人员编号
        jsonColumn.put("adjustPersonName", ""); // 姓名
        if (!"zh_CN".equals(LocaleMessageSourceUtil.language)) {
            jsonColumn.put("adjustPersonLastName", "");
        }
        jsonColumn.put("adjustDeptName", ""); // 部门名称
        jsonColumn.put("adjustType", ""); // 调班类型
        jsonColumn.put("adjustDate", ""); // 调整日期
        jsonColumn.put("swapShiftName", ""); // 调整班次名称
        jsonColumn.put("swapPersonPin", ""); // 对调人员编号
        jsonColumn.put("swapPersonName", ""); // 对调人员姓名
        if (!"zh_CN".equals(LocaleMessageSourceUtil.language)) {
            jsonColumn.put("swapPersonLastName", "");
        }
        jsonColumn.put("swapDeptName", ""); // 对调人员部门名称
        jsonColumn.put("swapDate", ""); // 对调日期
        jsonColumn.put("remark", "");
        Map<String, Map<String, String>> jsonCloumnMap = new HashMap<>();
        Map<String, String> jsonMap = new HashMap<>();
        jsonMap.put("jsonColumn", jsonColumn.toJSONString());
        jsonCloumnMap.put("jsonColumn", jsonMap);
        attExcelUtil.attExcelExport(request, response, new ArrayList<>(), AttClassItem.class, jsonCloumnMap);
    }

    @RequiresPermissions("att:class:approval")
    @LogRequest(module = "att_module", object = "att_leftMenu_class", opType = "att_apply_pass",
            requestParams = {"pins"}, opContent = "att_person_pin")
    @Override
    public ZKResultMsg approval(String ids) {
        attClassService.approval(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @RequiresPermissions("att:class:approval")
    @LogRequest(module = "att_module", object = "att_leftMenu_class", opType = "att_exception_refuse",
            requestParams = {"pins"}, opContent = "att_person_pin")
    @Override
    public ZKResultMsg refuse(String ids) {
        attClassService.refuse(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }
}