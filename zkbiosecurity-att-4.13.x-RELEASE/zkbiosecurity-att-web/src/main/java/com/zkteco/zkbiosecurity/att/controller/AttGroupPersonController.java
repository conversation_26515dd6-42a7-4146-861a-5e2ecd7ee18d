package com.zkteco.zkbiosecurity.att.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.zkteco.zkbiosecurity.att.remote.AttGroupPersonRemote;
import com.zkteco.zkbiosecurity.att.service.AttPersonService;
import com.zkteco.zkbiosecurity.att.vo.AttGroupPersonItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 分组人员
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 18:12
 * @since 1.0.0
 */
@Controller
public class AttGroupPersonController extends BaseController implements AttGroupPersonRemote {
    @Autowired
    private AttPersonService attPersonService;

    @RequiresPermissions("att:groupPerson:refresh")
    @Override
    public DxGrid list(AttGroupPersonItem condition) {
        Pager pager =
            attPersonService.loadPagerByAuthFilterByGroup(request.getSession().getId(), condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public ZKResultMsg save() {
        ZKResultMsg res = new ZKResultMsg();
        attPersonService.updateAttPerson(request.getParameter("groupId"), request.getParameter("personIds"),
            request.getParameter("deptIds"));
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("att:groupPerson:del")
    @LogRequest(module = "att_module", object = "att_leftMenu_groupPerson", opType = "common_op_del",
        requestParams = {"names"}, opContent = "common_name")
    @Override
    public ZKResultMsg delete(String ids, String groupId) {
        attPersonService.deleteByIds(ids, groupId);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

}