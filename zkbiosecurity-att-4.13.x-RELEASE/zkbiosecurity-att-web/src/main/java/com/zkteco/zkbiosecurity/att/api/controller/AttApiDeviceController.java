package com.zkteco.zkbiosecurity.att.api.controller;

import com.zkteco.zkbiosecurity.att.api.vo.AttApiDeviceItem;
import com.zkteco.zkbiosecurity.att.service.AttDeviceService;
import com.zkteco.zkbiosecurity.att.vo.AttDeviceItem;
import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 考勤设备接口
 * 
 * @Auther: lambert.li
 * @Date: 2018/11/15 19:46
 */
@Controller
@RequestMapping(value = {"/api/device"})
@Api(tags = "AttDevice", description = "att device")
public class AttApiDeviceController {

    @Autowired
    private AttDeviceService attDeviceService;

    /**
     * 根据设备sn获取设备
     * 
     * @auther lambert.li
     * @date 2018/11/21 14:10
     * @param sn
     * @return
     */
    @ApiOperation(value = "Get Att Device Info By Sn", notes = "Return Att Device Object",
        response = ApiResultMessage.class)
    @RequestMapping(value = {"/getAtt"}, method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"sn"})
    public ApiResultMessage getByAttDeviceSn(@RequestParam(name = "sn") String sn) {
        AttDeviceItem attDeviceItem = attDeviceService.getItemBySn(sn);
        AttApiDeviceItem attApiDeviceItem = AttApiDeviceItem.createApiDevice(attDeviceItem);
        return ApiResultMessage.successMessage(attApiDeviceItem);
    }
}
