package com.zkteco.zkbiosecurity.att.dhx;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.base.bean.GridColumnItem;
import com.zkteco.zkbiosecurity.core.utils.DynamicColumn;

/**
 * 请假类型动态表头 参考实例：日报表/月报表
 *
 * @author: bob.liu
 * @date: 2020-05-20 15:34
 * @since 1.0.0
 **/
@Component
public class AttLeaveTypeDynamicColumn implements DynamicColumn {

    @Autowired
    private AttLeaveTypeService attLeaveTypeService;

    @Override
    public List<GridColumnItem> getColumn(String filedName) {
        List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getLeaveTypeItems();
        List<GridColumnItem> columns = new ArrayList<GridColumnItem>();

        for (int i = 0; i < attLeaveTypeItemList.size(); i++) {

            AttLeaveTypeItem attLeaveTypeItem = attLeaveTypeItemList.get(i);
            String leaveTypeName = attLeaveTypeService.getConvertUnit(attLeaveTypeItem);
            GridColumnItem columnItem = new GridColumnItem();
            columnItem.setName(attLeaveTypeItem.getLeaveTypeName());
            columnItem.setColumnType("ro");
            columnItem.setSort("na");
            columnItem.setWidth("110");
            columnItem.setSortNo(i + 2000);
            columnItem.setFieldName(filedName);
            if ("attAbnormalReportLeaveMap".equals(filedName)) {
                columnItem.setLabel(leaveTypeName);
            } else {
                if (i == 0) {
                    columnItem.setLabel("att_statistical_leaveDetail");
                } else {
                    columnItem.setLabel("#cspan");
                }
                columnItem.setSecHeader(leaveTypeName);

            }
            columns.add(columnItem);
        }
        return columns;
    }
}
