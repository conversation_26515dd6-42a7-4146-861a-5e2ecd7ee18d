package com.zkteco.zkbiosecurity.att.api.controller;

import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.zkteco.zkbiosecurity.att.api.vo.AppAttPersonSchItem;
import com.zkteco.zkbiosecurity.att.service.AttPersonSchDataService;
import com.zkteco.zkbiosecurity.att.service.AttPersonSchService;
import com.zkteco.zkbiosecurity.base.annotation.ApiPermissions;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;

/**
 * <AUTHOR>
 * @Date: 2019/6/11 11:37
 */
@RestController
@RequestMapping(value = {"/api/personSch"})
public class AttApiPersonSchController {

    @Autowired
    private AttPersonSchService attPersonSchService;

    @Autowired
    private AttPersonSchDataService attPersonSchDataService;

    /**
     * 查询人员排班记录
     * 
     * @auther lambert.li
     * @date 2019/6/19 18:45
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping(path = "/getPersonSch", method = RequestMethod.POST, produces = "application/json")
    public ZKResultMsg getPersonSch(@RequestBody ZKMessage message) {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        Map dataMap = message.getContent();
        String pin = MapUtils.getString(dataMap, "pin");
        String startDate = MapUtils.getString(dataMap, "startDate");
        String endDate = MapUtils.getString(dataMap, "endDate");
        if (StringUtils.isNotBlank(pin) && StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            List<AppAttPersonSchItem> attCloudPersonSchItems =
                attPersonSchService.getAttCalendarForApp(pin, startDate, endDate);
            resultMsg.setData(attCloudPersonSchItems);
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    /**
     * 获取人员当日排班时间及工作时长
     * 
     * @param message
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping(path = "/getTodayWorkTime", method = RequestMethod.POST, produces = "application/json")
    public ZKResultMsg getTodayWorkTime(@RequestBody ZKMessage message) {

        Map<String, Object> content = message.getContent();
        String pin = MapUtils.getString(content, "pin");
        String attDate = MapUtils.getString(content, "attDate");
        ZKResultMsg zkResultMsg = attPersonSchDataService.getTodayWorkTime(pin, attDate);
        return I18nUtil.i18nMsg(zkResultMsg);
    }

}
