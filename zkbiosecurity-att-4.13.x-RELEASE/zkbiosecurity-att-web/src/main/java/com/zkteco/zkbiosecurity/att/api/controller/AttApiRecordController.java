package com.zkteco.zkbiosecurity.att.api.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.zkteco.zkbiosecurity.att.api.vo.AttApiDayDetailReportItem;
import com.zkteco.zkbiosecurity.att.api.vo.AttApiQueryRecordItem;
import com.zkteco.zkbiosecurity.att.service.AttMonthStatisticalReportService;
import com.zkteco.zkbiosecurity.att.service.AttRecordService;
import com.zkteco.zkbiosecurity.att.service.AttRuleService;
import com.zkteco.zkbiosecurity.att.vo.AttMonthStatisticalReportItem;
import com.zkteco.zkbiosecurity.att.vo.AttRecordItem;
import com.zkteco.zkbiosecurity.base.annotation.ApiPermissions;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;

/**
 * <AUTHOR>
 * @Date: 2019/6/18 13:44
 */
@RestController
@RequestMapping(value = {"/api/attRecord"})
public class AttApiRecordController {

    @Autowired
    private AttRecordService attRecordService;
    @Autowired
    private AttMonthStatisticalReportService attMonthStatisticalReportService;
    @Autowired
    private AttRuleService attRuleService;

    /**
     * 根据人员编号和日期获取考勤计算结果
     * 
     * @auther lambert.li
     * @date 2019/6/19 18:43
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping(path = "/getRecordByPinAndData", method = RequestMethod.POST, produces = "application/json")
    public ZKResultMsg getRecordByPinAndData(@RequestBody ZKMessage message) {
        Map data = message.getContent();
        String personPin = MapUtils.getString(data, "pin");
        String startTime = MapUtils.getString(data, "startTime");
        String endTime = MapUtils.getString(data, "endTime");
        Date startDate = DateUtil.stringToDate(startTime);
        Date endDate = DateUtil.stringToDate(endTime);
        if (startDate == null || endDate == null) {
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("common_dsTime_timeValid2"));
        }
        if (startDate.getTime() > endDate.getTime()) {
            // 开始时间不能大于结束时间
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("common_dsTime_timeValid4"));
        }
        AttRecordItem attRecordItem = new AttRecordItem();
        if (StringUtils.isNotBlank(personPin)) {
            // attRecordItem.setInPersonPin(personPin);
            attRecordItem.setPersonPin(personPin);
        }
        attRecordItem.setStartDate(startDate);
        attRecordItem.setEndDate(endDate);
        attRecordItem.setEquals(true);
        List<AttRecordItem> attRecordItems = attRecordService.getByCondition(attRecordItem);
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        resultMsg.setData(attRecordItems);
        return I18nUtil.i18nMsg(resultMsg);
    }

    /**
     * 分页查询考勤计算结果
     * 
     * @auther lambert.li
     * @date 2019/6/19 18:44
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping(path = "/getRecordPage", method = RequestMethod.POST, produces = "application/json")
    public ZKResultMsg getRecordPage(@RequestBody ZKMessage message) {
        Map data = message.getContent();
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        if (data.containsKey("attRecord")) {
            AttApiQueryRecordItem attApiQueryRecordItem =
                JSON.parseObject(JSON.toJSONString(data.get("attRecord")), AttApiQueryRecordItem.class);
            AttRecordItem attRecordItem = new AttRecordItem();
            attRecordItem.setStartDate(attApiQueryRecordItem.getAttDateStart());
            attRecordItem.setEndDate(attApiQueryRecordItem.getAttDateEnd());
            attRecordItem.setInPersonPin(attApiQueryRecordItem.getInPin());
            Integer page = MapUtils.getInteger(data, "page");
            Integer pageSize = MapUtils.getInteger(data, "pageSize");
            Pager pager = attRecordService.getItemsByPage(attRecordItem, page, pageSize);
            resultMsg.setData(pager);
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    /**
     * 获取考勤月统计报表记录
     * 
     * @auther lambert.li
     * @date 2019/6/19 18:44
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping(path = "/getMonthStatisticalReport", method = RequestMethod.POST, produces = "application/json")
    public ZKResultMsg getMonthStatisticalReport(@RequestBody ZKMessage message) {
        Map data = message.getContent();
        String pin = MapUtils.getString(data, "pin");
        String monthStart = MapUtils.getString(data, "monthStart");
        String monthEnd = MapUtils.getString(data, "monthEnd");
        Date monthStartDate = DateUtil.stringToDate(monthStart);
        Date monthEndDate = DateUtil.stringToDate(monthEnd);
        if (StringUtils.isBlank(pin)) {
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("pers_import_pinNotEmpty"));
        }
        if (monthStartDate == null || monthEndDate == null) {
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("common_dsTime_timeValid2"));
        }
        if (monthStartDate.getTime() > monthEndDate.getTime()) {
            // 开始时间不能大于结束时间
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("common_dsTime_timeValid4"));
        }
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        AttMonthStatisticalReportItem attMonthStatisticalReportItem =
            attMonthStatisticalReportService.getItemByPinAndDate(pin, monthStartDate, monthEndDate);
        resultMsg.setData(attMonthStatisticalReportItem);
        return resultMsg;
    }

    /**
     * 获取考勤日报表数据
     * 
     * @auther lambert.li
     * @date 2019/6/19 18:44
     * @return
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping(path = "/getPersonDetailReport", method = RequestMethod.POST, produces = "application/json")
    public ZKResultMsg getPersonDetailReport(@RequestBody ZKMessage message) {
        Map data = message.getContent();
        String pin = MapUtils.getString(data, "pin");
        String dayStart = MapUtils.getString(data, "startTime");
        String dayEnd = MapUtils.getString(data, "endTime");
        Date dayStartDate = DateUtil.stringToDate(dayStart);
        Date dayEndDate = DateUtil.stringToDate(dayEnd);
        if (StringUtils.isBlank(pin)) {
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("pers_import_pinNotEmpty"));
        }
        if (dayStartDate == null || dayEndDate == null) {
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("common_dsTime_timeValid2"));
        }
        if (dayStartDate.getTime() > dayEndDate.getTime()) {
            // 开始时间不能大于结束时间
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("common_dsTime_timeValid4"));
        }
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        AttApiDayDetailReportItem attDayDetailReportItem =
            attRecordService.getPersonDetailReport(pin, dayStartDate, dayEndDate);
        resultMsg.setData(attDayDetailReportItem);
        return resultMsg;
    }
}
