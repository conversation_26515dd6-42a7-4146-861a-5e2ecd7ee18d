package com.zkteco.zkbiosecurity.att.app.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONArray;
import com.zkteco.zkbiosecurity.att.api.vo.*;
import com.zkteco.zkbiosecurity.att.app.vo.AttAppSignItem;
import com.zkteco.zkbiosecurity.att.constants.AppConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.pers.service.PersPersonCacheService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem;
import com.zkteco.zkbiosecurity.system.utils.BaseLanguageUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.base.vo.AppResultMessage;

import javax.servlet.http.HttpServletRequest;

/**
 * app接口
 *
 * 针对海外app相关接口："/app/v1"
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/2/19 11:32
 * @since 1.0.0
 */
@RestController
@RequestMapping(value = {"/app/v1", "/app/v1/att"})
public class AttAppController {

    @Autowired
    protected HttpServletRequest request;
    @Autowired
    private AttAppService attAppService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;
    @Autowired
    private AttPersonSchService attPersonSchService;
    @Autowired
    private AttSignAddressService attSignAddressService;
    @Autowired
    private AttRecordService attRecordService;
    @Autowired
    private AttTransactionService attTransactionService;
    @Autowired
    private AttMonthStatisticalReportService attMonthStatisticalReportService;
    @Autowired
    private AttWorkflowService attWorkflowService;
    @Autowired
    private PersPersonCacheService persPersonCacheService;
    @Autowired
    private AttLeaveService attLeaveService;

    /**
     * 获取考勤日历/查询人员排班
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/20 14:45
     * @since 1.0.0
     */
    @RequestMapping(value = "/getAttCalendar", method = RequestMethod.POST)
    public AppResultMessage getAttCalendar(@RequestBody JSONObject data) {
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        String pin = request.getParameter("user_name");
        String startDate = data.getString("startDate");
        String endDate = data.getString("endDate");
        if (StringUtils.isNotBlank(pin) && StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            List<AppAttPersonSchItem> attCloudPersonSchItems =
                attPersonSchService.getAttCalendarForApp(pin, startDate, endDate);
            appResultMessage.setData(attCloudPersonSchItems);
        }
        return appResultMessage;
    }

    /**
     * 获取考勤状态列表
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2025-02-07 15:00
     * @since 1.0.0
     */
    @RequestMapping(value = "/getAttStateList", method = RequestMethod.POST)
    public AppResultMessage getAttStateList(@RequestBody JSONObject data) {
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        JSONArray jsonArray = new JSONArray();

        // 正常
        JSONObject normal = new JSONObject();
        normal.put("attState", AppConstant.ATT_RESULT_TYPE_NORMAL);
        normal.put("name", I18nUtil.i18nCode("common_normal"));
        jsonArray.add(normal);
        // 异常
        JSONObject exception = new JSONObject();
        exception.put("attState", AppConstant.ATT_RESULT_TYPE_EXCEPTION);
        exception.put("name", I18nUtil.i18nCode("common_exception"));
        jsonArray.add(exception);
        // 请假
        JSONObject leave = new JSONObject();
        leave.put("attState", AppConstant.ATT_RESULT_TYPE_LEAVE);
        leave.put("name", I18nUtil.i18nCode("att_common_leave"));
        jsonArray.add(leave);
        // 休息
        JSONObject rest = new JSONObject();
        rest.put("attState", AppConstant.ATT_RESULT_TYPE_REST);
        rest.put("name", I18nUtil.i18nCode("att_schedule_off"));
        jsonArray.add(rest);
        // 加班
        JSONObject overtime = new JSONObject();
        overtime.put("attState", AppConstant.ATT_RESULT_TYPE_OVERTIME);
        overtime.put("name", I18nUtil.i18nCode("att_common_overtime"));
        jsonArray.add(overtime);
        // 外出
        JSONObject out = new JSONObject();
        out.put("attState", AppConstant.ATT_RESULT_TYPE_OUT);
        out.put("name", I18nUtil.i18nCode("att_common_out"));
        jsonArray.add(out);
        // 出差
        JSONObject trip = new JSONObject();
        trip.put("attState", AppConstant.ATT_RESULT_TYPE_TRIP);
        trip.put("name", I18nUtil.i18nCode("att_common_trip"));
        jsonArray.add(trip);
        // 未排班
        JSONObject noScheduling = new JSONObject();
        noScheduling.put("attState", AppConstant.ATT_RESULT_TYPE_NO_SCHEDULING);
        noScheduling.put("name", I18nUtil.i18nCode("att_schedule_noSchDetail"));
        jsonArray.add(noScheduling);
        appResultMessage.setData(jsonArray);
        return appResultMessage;
    }

    /**
     * 【签到】获取人员签到地点
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/20 17:28
     * @since 1.0.0
     */
    @RequestMapping(value = "/getAttSignAddress", method = RequestMethod.POST)
    public AppResultMessage getAttSignAddress(@RequestBody JSONObject data) {
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        String pin = request.getParameter("user_name");
        List<AttSignAddressItem> signAddressList = attSignAddressService.getPersonSignAddress(pin);
        appResultMessage.setData(signAddressList);
        return appResultMessage;
    }

    /**
     * 判断是否在签到范围内
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2025-03-20 9:53
     * @since 1.0.0
     */
    @RequestMapping(value = "/checkSignInRange", method = RequestMethod.POST)
    public AppResultMessage checkSignInRange(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        String longitude = data.getString("longitude");
        String latitude = data.getString("latitude");
        String mapType = data.getString("mapType");
        String lang = data.getString("lang");

        ZKResultMsg resultMsg = attSignAddressService.checkSignInRange(pin, longitude, latitude, mapType);
        if (resultMsg.isSuccess()) {
            AppResultMessage appResultMessage = AppResultMessage.successMessage();
            appResultMessage.setData(resultMsg.getData());
            return appResultMessage;
        } else {
            return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, resultMsg.getMsg()));
        }
    }

    /**
     * 【签到】人员签到
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/20 17:28
     * @since 1.0.0
     */
    @RequestMapping(value = "/signIn", method = RequestMethod.POST)
    public AppResultMessage signIn(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        String clientId = request.getHeader("clientId");
        String lang = data.getString("lang");
        if (StringUtils.isBlank(clientId)) {
            clientId = data.getString("clientId");
        }
        ZKResultMsg resultMsg = attTransactionService.signIn(clientId, pin, data);
        if (resultMsg.isSuccess()) {
            AppResultMessage appResultMessage = AppResultMessage.successMessage();
            appResultMessage.setData(resultMsg.getData());
            return appResultMessage;
        } else {
            return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, resultMsg.getMsg()));
        }
    }

    /**
     * 【签到】分页获取签到列表
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/20 17:28
     * @since 1.0.0
     */
    @RequestMapping(value = "/getSignList", method = RequestMethod.POST)
    public AppResultMessage getSignList(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        String date = data.getString("date");
        int pageNo = data.getIntValue("pageNo");
        int pageSize = data.getIntValue("pageSize");

        // 查询补签记录
        AttTransactionItem condition = new AttTransactionItem();
        condition.setPersonPin(pin);
        condition.setAttDate(date);
        condition.setEquals(true);
        Pager pager = attTransactionService.getSignInItemsByPage(condition, pageNo - 1, pageSize);
        List<AttTransactionItem> attTransactionItemList = (List<AttTransactionItem>)pager.getData();

        // 对象转化
        List<AttAppSignItem> attAppSignItemList = attTransactionItemList.stream().map(source -> {
            AttAppSignItem attAppSignItem = new AttAppSignItem();
            BeanUtils.copyProperties(source, attAppSignItem);
            return attAppSignItem;
        }).collect(Collectors.toList());
        pager.setData(attAppSignItemList);

        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        appResultMessage.setData(pager);
        return appResultMessage;
    }

    /**
     * 【补签】获取补签审批人
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getSignApprove", method = RequestMethod.POST)
    public AppResultMessage getSignApprove(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        try {
            List<AttApiApplyPersonItem> list = attWorkflowService.getFirstApprove(pin, AttConstant.FLOW_TYPE_SIGN);
            AppResultMessage appResultMessage = AppResultMessage.successMessage();
            appResultMessage.setData(list);
            return appResultMessage;
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【补签】获取补签知会人
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getSignNotifier", method = RequestMethod.POST)
    public AppResultMessage getSignNotifier(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        try {
            List<AttApiApplyPersonItem> list = attWorkflowService.getFirstNotifier(pin, AttConstant.FLOW_TYPE_SIGN);
            AppResultMessage appResultMessage = AppResultMessage.successMessage();
            appResultMessage.setData(list);
            return appResultMessage;
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【补签】获取人员月补签次数
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getPersonMonthSignCount", method = RequestMethod.POST)
    public AppResultMessage getPersonMonthSignCount(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        String attDate = data.getString("attDate");
        Integer count = attWorkflowService.getPersonMonthSignCount(pin, attDate);
        JSONObject ret = new JSONObject();
        ret.put("count", count != null ? count : 0);
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        appResultMessage.setData(ret);
        return appResultMessage;
    }

    /**
     * 【补签】补签申请
     * 
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/saveSign", method = RequestMethod.POST)
    public AppResultMessage saveSign(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        String signDate = data.getString("signDate");
        String signTime = data.getString("signTime");
        String notifierPersPins = data.getString("notifierPersPins");
        String remark = data.getString("remark");

        PersPersonCacheItem persPersonItem = persPersonCacheService.getPersonCacheByPin(pin);
        AttCloudSignItem attCloudSignItem = new AttCloudSignItem();
        attCloudSignItem.setPersonPin(persPersonItem.getPin());
        attCloudSignItem.setPersonName(persPersonItem.getName());
        attCloudSignItem.setPersonLastName(persPersonItem.getLastName());
        attCloudSignItem.setDeptCode(persPersonItem.getDeptCode());
        attCloudSignItem.setDeptName(persPersonItem.getDeptName());
        attCloudSignItem.setSignDate(signDate);
        attCloudSignItem.setSignDatetime(signTime);
        attCloudSignItem.setRemark(remark);
        attCloudSignItem.setNotifierPins(notifierPersPins);
        List<AttCloudSignItem> attSignItemList = new ArrayList<>();
        attSignItemList.add(attCloudSignItem);

        try {
            attWorkflowService.saveSignItem(attSignItemList);
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
        return AppResultMessage.successMessage();
    }

    /**
     * 【请假】获取考勤假类
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/20 14:45
     * @since 1.0.0
     */
    @RequestMapping(value = "/getLeaveTypeList", method = RequestMethod.POST)
    public AppResultMessage getLeaveTypeList(@RequestBody JSONObject data) {
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getLeaveTypeItems();
        JSONArray leaveTypeList = new JSONArray();
        for (AttLeaveTypeItem attLeaveTypeItem : attLeaveTypeItemList) {
            String leaveTypeNo = attLeaveTypeItem.getLeaveTypeNo();
            if (AppConstant.FLOW_TYPE_OUT.equals(leaveTypeNo) || AppConstant.FLOW_TYPE_TRIP.equals(leaveTypeNo)) {
                continue;
            }
            JSONObject leaveType = new JSONObject();
            leaveType.put("id", attLeaveTypeItem.getId());
            leaveType.put("leaveTypeNo", attLeaveTypeItem.getLeaveTypeNo());
            leaveType.put("leaveTypeName", attLeaveTypeItem.getLeaveTypeName());
            leaveTypeList.add(leaveType);

        }
        appResultMessage.setData(leaveTypeList);
        return appResultMessage;
    }

    /**
     * 【请假】获取请假审批人
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getLeaveApprove", method = RequestMethod.POST)
    public AppResultMessage getLeaveApprove(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        // 请假类型
        String leaveTypeNo = data.getString("leaveTypeNo");
        // 请假起始时间
        String startTime = data.getString("startTime");
        String endTime = data.getString("endTime");
        try {
            List<AttApiApplyPersonItem> list = attWorkflowService.getLeaveApprove(pin, leaveTypeNo, startTime, endTime);
            AppResultMessage appResultMessage = AppResultMessage.successMessage();
            appResultMessage.setData(list);
            return appResultMessage;
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【请假】获取请假知会人
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getLeaveNotifier", method = RequestMethod.POST)
    public AppResultMessage getLeaveNotifier(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        try {
            List<AttApiApplyPersonItem> list = attWorkflowService.getFirstNotifier(pin, AttConstant.FLOW_TYPE_LEAVE);
            AppResultMessage appResultMessage = AppResultMessage.successMessage();
            appResultMessage.setData(list);
            return appResultMessage;
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【请假】获取申请时长
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/calcApplyTimeLong", method = RequestMethod.POST)
    public AppResultMessage calcApplyTimeLong(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        // 请假类型
        String leaveTypeNo = data.getString("leaveTypeNo");
        // 请假起始时间
        String startTime = data.getString("startTime");
        String endTime = data.getString("endTime");
        try {
            ZKResultMsg zkResultMsg = attLeaveService.getLeaveLongByType(pin, DateUtil.stringToDate(startTime),
                DateUtil.stringToDate(endTime), leaveTypeNo);
            AppResultMessage appResultMessage = AppResultMessage.successMessage();
            JSONObject result = (JSONObject)zkResultMsg.getData();

            String unit = "";
            switch (result.getString("convertUnit")) {
                case AttConstant.ATT_CONVERT_UNIT_MINUTE:
                    unit = BaseLanguageUtil.getI18nByAppLang(lang, "common_minutes");
                    break;
                case AttConstant.ATT_CONVERT_UNIT_HOUR:
                    unit = BaseLanguageUtil.getI18nByAppLang(lang, "common_hour");
                    break;
                case AttConstant.ATT_CONVERT_UNIT_DAY:
                    unit = BaseLanguageUtil.getI18nByAppLang(lang, "common_days");
                    break;
                default:
            }
            result.put("unit", unit);
            appResultMessage.setData(result);
            return appResultMessage;
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【请假/加班/外出/出差】上传申请图片
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/uploadApplyImage", method = RequestMethod.POST)
    public AppResultMessage uploadApplyImage(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        // 业务类型
        String businessType = data.getString("businessType");
        // 图片base64数据
        String imageBase64 = data.getString("imageBase64");
        try {
            String fileName = System.currentTimeMillis() + ".jpg";
            String filePath = FileUtils.createUploadFileRootPath(ConstUtil.SYSTEM_MODULE_ATT, businessType + "/image");
            FileUtils.saveFile(filePath, fileName, imageBase64, false);
            FileEncryptUtil.encryptFileByPath(filePath + fileName);

            AppResultMessage appResultMessage = AppResultMessage.successMessage();
            JSONObject ret = new JSONObject();
            String path = "/" + filePath + fileName;
            ret.put("path", path);
            appResultMessage.setData(ret);
            return appResultMessage;
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【请假】请假申请
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/saveAppLeave", method = RequestMethod.POST)
    public AppResultMessage saveAppLeave(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        // 请假类型
        String leaveTypeNo = data.getString("leaveTypeNo");
        // 请假起始时间
        String startTime = data.getString("startTime");
        String endTime = data.getString("endTime");
        String notifierPersPins = data.getString("notifierPersPins");
        String remark = data.getString("remark");
        String leaveImagePath = data.getString("leaveImagePath");
        // String photoUrlList = data.getString("photoUrlList");

        PersPersonCacheItem persPersonItem = persPersonCacheService.getPersonCacheByPin(pin);
        AttLeaveItem attLeaveItem = new AttLeaveItem();
        attLeaveItem.setPersonPin(persPersonItem.getPin());
        attLeaveItem.setPersonName(persPersonItem.getName());
        attLeaveItem.setPersonLastName(persPersonItem.getLastName());
        attLeaveItem.setDeptCode(persPersonItem.getDeptCode());
        attLeaveItem.setDeptName(persPersonItem.getDeptName());
        attLeaveItem.setStartDatetime(DateUtil.stringToDate(startTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        attLeaveItem.setEndDatetime(DateUtil.stringToDate(endTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        attLeaveItem.setNotifierPins(notifierPersPins);
        attLeaveItem.setLeaveTypeNo(leaveTypeNo);
        attLeaveItem.setLeaveImagePath(leaveImagePath);
        // attLeaveItem.setCloudImageUrl(photoUrlList);
        attLeaveItem.setRemark(remark);

        try {
            ZKResultMsg res = attWorkflowService.isExistApply(attLeaveItem.getPersonPin(),
                attLeaveItem.getStartDatetime(), attLeaveItem.getEndDatetime());
            if (res.isSuccess()) {
                attWorkflowService.saveLeaveItem(Arrays.asList(attLeaveItem));
                return AppResultMessage.successMessage();
            } else {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, res.getMsg()));
            }
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【加班】获取加班类型
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/20 14:45
     * @since 1.0.0
     */
    @RequestMapping(value = "/getOvertimeSignList", method = RequestMethod.POST)
    public AppResultMessage getOvertimeSignList(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        JSONArray overtimeTypeArray = new JSONArray();
        // 平时加班
        JSONObject normal = new JSONObject();
        normal.put("overtimeSign", "0");
        normal.put("name", BaseLanguageUtil.getI18nByAppLang(lang, "att_overtime_normal"));
        overtimeTypeArray.add(normal);
        // 休息日加班
        JSONObject rest = new JSONObject();
        rest.put("overtimeSign", "1");
        rest.put("name", BaseLanguageUtil.getI18nByAppLang(lang, "att_overtime_rest"));
        overtimeTypeArray.add(rest);
        // 节假日加班
        JSONObject holiday = new JSONObject();
        holiday.put("overtimeSign", "2");
        holiday.put("name", BaseLanguageUtil.getI18nByAppLang(lang, "att_shift_holidayOt"));
        overtimeTypeArray.add(holiday);
        appResultMessage.setData(overtimeTypeArray);
        return appResultMessage;
    }

    /**
     * 【加班】获取加班审批人
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getOvertimeApprove", method = RequestMethod.POST)
    public AppResultMessage getOvertimeApprove(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        try {
            List<AttApiApplyPersonItem> list = attWorkflowService.getFirstApprove(pin, AttConstant.FLOW_TYPE_OVERTIME);
            AppResultMessage appResultMessage = AppResultMessage.successMessage();
            appResultMessage.setData(list);
            return appResultMessage;
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【加班】获取加班知会人
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getOvertimeNotifier", method = RequestMethod.POST)
    public AppResultMessage getOvertimeNotifier(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        try {
            List<AttApiApplyPersonItem> list = attWorkflowService.getFirstNotifier(pin, AttConstant.FLOW_TYPE_OVERTIME);
            AppResultMessage appResultMessage = AppResultMessage.successMessage();
            appResultMessage.setData(list);
            return appResultMessage;
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【加班】加班申请
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/saveOvertime", method = RequestMethod.POST)
    public AppResultMessage saveOvertime(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        // 请假类型
        String overtimeSign = data.getString("overtimeSign");
        // 请假起始时间
        String startTime = data.getString("startTime");
        String endTime = data.getString("endTime");
        String notifierPersPins = data.getString("notifierPersPins");
        String remark = data.getString("remark");

        PersPersonCacheItem persPersonItem = persPersonCacheService.getPersonCacheByPin(pin);
        AttOvertimeItem attOvertimeItem = new AttOvertimeItem();
        attOvertimeItem.setPersonPin(persPersonItem.getPin());
        attOvertimeItem.setPersonName(persPersonItem.getName());
        attOvertimeItem.setPersonLastName(persPersonItem.getLastName());
        attOvertimeItem.setDeptCode(persPersonItem.getDeptCode());
        attOvertimeItem.setDeptName(persPersonItem.getDeptName());
        attOvertimeItem.setStartDatetime(DateUtil.stringToDate(startTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        attOvertimeItem.setEndDatetime(DateUtil.stringToDate(endTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        attOvertimeItem.setNotifierPins(notifierPersPins);
        attOvertimeItem.setOvertimeSign(Short.valueOf(overtimeSign));
        attOvertimeItem.setRemark(remark);

        try {
            ZKResultMsg res = attWorkflowService.isExistApply(attOvertimeItem.getPersonPin(),
                attOvertimeItem.getStartDatetime(), attOvertimeItem.getEndDatetime());
            if (res.isSuccess()) {
                attWorkflowService.saveOvertimeItem(Arrays.asList(attOvertimeItem));
                return AppResultMessage.successMessage();
            } else {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, res.getMsg()));
            }
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【外出】获取外出审批人
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getOutApprove", method = RequestMethod.POST)
    public AppResultMessage getOutApprove(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        String lang = data.getString("lang");
        try {
            List<AttApiApplyPersonItem> list = attWorkflowService.getFirstApprove(pin, AttConstant.FLOW_TYPE_OUT);
            AppResultMessage appResultMessage = AppResultMessage.successMessage();
            appResultMessage.setData(list);
            return appResultMessage;
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【外出】获取外出知会人
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getOutNotifier", method = RequestMethod.POST)
    public AppResultMessage getOutNotifier(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        try {
            List<AttApiApplyPersonItem> list = attWorkflowService.getFirstNotifier(pin, AttConstant.FLOW_TYPE_OUT);
            AppResultMessage appResultMessage = AppResultMessage.successMessage();
            appResultMessage.setData(list);
            return appResultMessage;
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【外出】外出申请
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/saveOut", method = RequestMethod.POST)
    public AppResultMessage saveOut(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        String startTime = data.getString("startTime");
        String endTime = data.getString("endTime");
        String notifierPersPins = data.getString("notifierPersPins");
        String remark = data.getString("remark");

        PersPersonCacheItem persPersonItem = persPersonCacheService.getPersonCacheByPin(pin);
        AttOutItem attOutItem = new AttOutItem();
        attOutItem.setPersonPin(persPersonItem.getPin());
        attOutItem.setPersonName(persPersonItem.getName());
        attOutItem.setPersonLastName(persPersonItem.getLastName());
        attOutItem.setDeptCode(persPersonItem.getDeptCode());
        attOutItem.setDeptName(persPersonItem.getDeptName());
        attOutItem.setStartDatetime(DateUtil.stringToDate(startTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        attOutItem.setEndDatetime(DateUtil.stringToDate(endTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        attOutItem.setNotifierPins(notifierPersPins);
        attOutItem.setRemark(remark);

        try {
            ZKResultMsg res = attWorkflowService.isExistApply(attOutItem.getPersonPin(), attOutItem.getStartDatetime(),
                attOutItem.getEndDatetime());
            if (res.isSuccess()) {
                attWorkflowService.saveOutItem(Arrays.asList(attOutItem));
                return AppResultMessage.successMessage();
            } else {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, res.getMsg()));
            }
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【出差】获取出差审批人
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getTripApprove", method = RequestMethod.POST)
    public AppResultMessage getTripApprove(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        try {
            List<AttApiApplyPersonItem> list = attWorkflowService.getFirstApprove(pin, AttConstant.FLOW_TYPE_TRIP);
            AppResultMessage appResultMessage = AppResultMessage.successMessage();
            appResultMessage.setData(list);
            return appResultMessage;
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【出差】获取出差知会人
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getTripNotifier", method = RequestMethod.POST)
    public AppResultMessage getTripNotifier(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        try {
            List<AttApiApplyPersonItem> list = attWorkflowService.getFirstNotifier(pin, AttConstant.FLOW_TYPE_TRIP);
            AppResultMessage appResultMessage = AppResultMessage.successMessage();
            appResultMessage.setData(list);
            return appResultMessage;
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【出差】出差申请
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/saveTrip", method = RequestMethod.POST)
    public AppResultMessage saveTrip(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        String startTime = data.getString("startTime");
        String endTime = data.getString("endTime");
        String notifierPersPins = data.getString("notifierPersPins");
        String remark = data.getString("remark");

        PersPersonCacheItem persPersonItem = persPersonCacheService.getPersonCacheByPin(pin);
        AttTripItem attTripItem = new AttTripItem();
        attTripItem.setPersonPin(persPersonItem.getPin());
        attTripItem.setPersonName(persPersonItem.getName());
        attTripItem.setPersonLastName(persPersonItem.getLastName());
        attTripItem.setDeptCode(persPersonItem.getDeptCode());
        attTripItem.setDeptName(persPersonItem.getDeptName());
        attTripItem.setStartDatetime(DateUtil.stringToDate(startTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        attTripItem.setEndDatetime(DateUtil.stringToDate(endTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        attTripItem.setNotifierPins(notifierPersPins);
        attTripItem.setRemark(remark);

        try {
            ZKResultMsg res = attWorkflowService.isExistApply(attTripItem.getPersonPin(),
                attTripItem.getStartDatetime(), attTripItem.getEndDatetime());
            if (res.isSuccess()) {
                attWorkflowService.saveTripItem(Arrays.asList(attTripItem));
                return AppResultMessage.successMessage();
            } else {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, res.getMsg()));
            }
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【我的申请】获取补签申请列表
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getSignApplyList", method = RequestMethod.POST)
    public AppResultMessage getSignApplyList(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        int pageNo = data.getIntValue("pageNo");
        int pageSize = data.getIntValue("pageSize");
        String filter = data.getString("filter");
        Integer status = data.getInteger("status");
        Pager pager =
            attWorkflowService.findApplyTask(pin, AttConstant.FLOW_TYPE_SIGN, status, pageNo - 1, pageSize, filter);
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        appResultMessage.setData(pager);
        return appResultMessage;
    }

    /**
     * 【我的申请】获取请假申请列表
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getLeaveApplyList", method = RequestMethod.POST)
    public AppResultMessage getLeaveApplyList(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        int pageNo = data.getIntValue("pageNo");
        int pageSize = data.getIntValue("pageSize");
        String filter = data.getString("filter");
        Integer status = data.getInteger("status");
        Pager pager =
            attWorkflowService.findApplyTask(pin, AttConstant.FLOW_TYPE_LEAVE, status, pageNo - 1, pageSize, filter);
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        appResultMessage.setData(pager);
        return appResultMessage;
    }

    /**
     * 【我的申请】获取加班申请列表
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getOvertimeApplyList", method = RequestMethod.POST)
    public AppResultMessage getOvertimeApplyList(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        int pageNo = data.getIntValue("pageNo");
        int pageSize = data.getIntValue("pageSize");
        String filter = data.getString("filter");
        Integer status = data.getInteger("status");
        Pager pager =
            attWorkflowService.findApplyTask(pin, AttConstant.FLOW_TYPE_OVERTIME, status, pageNo - 1, pageSize, filter);
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        appResultMessage.setData(pager);
        return appResultMessage;
    }

    /**
     * 【我的申请】获取外出申请列表
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getOutApplyList", method = RequestMethod.POST)
    public AppResultMessage getOutApplyList(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        int pageNo = data.getIntValue("pageNo");
        int pageSize = data.getIntValue("pageSize");
        String filter = data.getString("filter");
        Integer status = data.getInteger("status");
        Pager pager =
            attWorkflowService.findApplyTask(pin, AttConstant.FLOW_TYPE_OUT, status, pageNo - 1, pageSize, filter);
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        appResultMessage.setData(pager);
        return appResultMessage;
    }

    /**
     * 【我的申请】获取出差申请列表
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getTripApplyList", method = RequestMethod.POST)
    public AppResultMessage getTripApplyList(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        int pageNo = data.getIntValue("pageNo");
        int pageSize = data.getIntValue("pageSize");
        String filter = data.getString("filter");
        Integer status = data.getInteger("status");
        Pager pager =
            attWorkflowService.findApplyTask(pin, AttConstant.FLOW_TYPE_TRIP, status, pageNo - 1, pageSize, filter);
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        appResultMessage.setData(pager);
        return appResultMessage;
    }

    /**
     * 【我的申请】申请详情
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getApplyDetailInfo", method = RequestMethod.POST)
    public AppResultMessage getApplyDetailInfo(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        String lang = data.getString("lang");
        // taskId 审批任务节点ID
        String businessKey = data.getString("taskId");
        try {
            AttApiApplyTaskDetailItem item = attWorkflowService.findByBusinessKey(businessKey);
            item.setTaskId(businessKey);
            AppResultMessage appResultMessage = AppResultMessage.successMessage();
            appResultMessage.setData(item);
            return appResultMessage;
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【我的申请】撤销申请
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/cancelApply", method = RequestMethod.POST)
    public AppResultMessage cancelApply(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        // taskId 审批任务节点ID
        String businessKey = data.getString("taskId");
        // revokeReason 批注
        String revokeReason = data.getString("remark");
        try {
            attWorkflowService.revokeProcess(businessKey, pin, revokeReason);
            return AppResultMessage.successMessage();
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【我的审批】获取待审批补签任务
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getNoApprovalSignTask", method = RequestMethod.POST)
    public AppResultMessage getNoApprovalSignTask(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        int pageNo = data.getIntValue("pageNo");
        int pageSize = data.getIntValue("pageSize");
        String filter = data.getString("filter");
        Pager pager =
            attWorkflowService.findPersonalTask(pin, AttConstant.FLOW_TYPE_SIGN, pageNo - 1, pageSize, filter);
        List<String> dataList = (List<String>)pager.getData();
        List<JSONObject> list = new ArrayList<>();
        for (String str : dataList) {
            list.add(JSONObject.parseObject(str));
        }
        pager.setData(list);
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        appResultMessage.setData(pager);
        return appResultMessage;
    }

    /**
     * 【我的审批】获取已审批补签任务
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getApprovalSignTask", method = RequestMethod.POST)
    public AppResultMessage getApprovalSignTask(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        int pageNo = data.getIntValue("pageNo");
        int pageSize = data.getIntValue("pageSize");
        String filter = data.getString("filter");
        Pager pager =
            attWorkflowService.findApprovedTask(pin, AttConstant.FLOW_TYPE_SIGN, pageNo - 1, pageSize, filter);
        List<String> dataList = (List<String>)pager.getData();
        List<JSONObject> list = new ArrayList<>();
        for (String str : dataList) {
            list.add(JSONObject.parseObject(str));
        }
        pager.setData(list);
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        appResultMessage.setData(pager);
        return appResultMessage;
    }

    /**
     * 【我的审批】获取待审批请假任务
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getNoApprovalLeaveTask", method = RequestMethod.POST)
    public AppResultMessage getNoApprovalLeaveTask(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        int pageNo = data.getIntValue("pageNo");
        int pageSize = data.getIntValue("pageSize");
        String filter = data.getString("filter");
        Pager pager =
            attWorkflowService.findPersonalTask(pin, AttConstant.FLOW_TYPE_LEAVE, pageNo - 1, pageSize, filter);
        List<String> dataList = (List<String>)pager.getData();
        List<JSONObject> list = new ArrayList<>();
        for (String str : dataList) {
            list.add(JSONObject.parseObject(str));
        }
        pager.setData(list);
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        appResultMessage.setData(pager);
        return appResultMessage;
    }

    /**
     * 【我的审批】获取已审批请假任务
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getApprovalLeaveTask", method = RequestMethod.POST)
    public AppResultMessage getApprovalLeaveTask(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        int pageNo = data.getIntValue("pageNo");
        int pageSize = data.getIntValue("pageSize");
        String filter = data.getString("filter");
        Pager pager =
            attWorkflowService.findApprovedTask(pin, AttConstant.FLOW_TYPE_LEAVE, pageNo - 1, pageSize, filter);
        List<String> dataList = (List<String>)pager.getData();
        List<JSONObject> list = new ArrayList<>();
        for (String str : dataList) {
            list.add(JSONObject.parseObject(str));
        }
        pager.setData(list);
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        appResultMessage.setData(pager);
        return appResultMessage;
    }

    /**
     * 【我的审批】获取待审批加班任务
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getNoApprovalOvertimeTask", method = RequestMethod.POST)
    public AppResultMessage getNoApprovalOvertimeTask(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        int pageNo = data.getIntValue("pageNo");
        int pageSize = data.getIntValue("pageSize");
        String filter = data.getString("filter");
        Pager pager =
            attWorkflowService.findPersonalTask(pin, AttConstant.FLOW_TYPE_OVERTIME, pageNo - 1, pageSize, filter);
        List<String> dataList = (List<String>)pager.getData();
        List<JSONObject> list = new ArrayList<>();
        for (String str : dataList) {
            list.add(JSONObject.parseObject(str));
        }
        pager.setData(list);
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        appResultMessage.setData(pager);
        return appResultMessage;
    }

    /**
     * 【我的审批】获取已审批加班任务
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getApprovalOvertimeTask", method = RequestMethod.POST)
    public AppResultMessage getApprovalOvertimeTask(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        int pageNo = data.getIntValue("pageNo");
        int pageSize = data.getIntValue("pageSize");
        String filter = data.getString("filter");
        Pager pager =
            attWorkflowService.findApprovedTask(pin, AttConstant.FLOW_TYPE_OVERTIME, pageNo - 1, pageSize, filter);
        List<String> dataList = (List<String>)pager.getData();
        List<JSONObject> list = new ArrayList<>();
        for (String str : dataList) {
            list.add(JSONObject.parseObject(str));
        }
        pager.setData(list);
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        appResultMessage.setData(pager);
        return appResultMessage;
    }

    /**
     * 【我的审批】获取待审批外出任务
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getNoApprovalOutTask", method = RequestMethod.POST)
    public AppResultMessage getNoApprovalOutTask(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        int pageNo = data.getIntValue("pageNo");
        int pageSize = data.getIntValue("pageSize");
        String filter = data.getString("filter");
        Pager pager = attWorkflowService.findPersonalTask(pin, AttConstant.FLOW_TYPE_OUT, pageNo - 1, pageSize, filter);
        List<String> dataList = (List<String>)pager.getData();
        List<JSONObject> list = new ArrayList<>();
        for (String str : dataList) {
            list.add(JSONObject.parseObject(str));
        }
        pager.setData(list);
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        appResultMessage.setData(pager);
        return appResultMessage;
    }

    /**
     * 【我的审批】获取已审批外出任务
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getApprovalOutTask", method = RequestMethod.POST)
    public AppResultMessage getApprovalOutTask(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        int pageNo = data.getIntValue("pageNo");
        int pageSize = data.getIntValue("pageSize");
        String filter = data.getString("filter");
        Pager pager = attWorkflowService.findApprovedTask(pin, AttConstant.FLOW_TYPE_OUT, pageNo - 1, pageSize, filter);
        List<String> dataList = (List<String>)pager.getData();
        List<JSONObject> list = new ArrayList<>();
        for (String str : dataList) {
            list.add(JSONObject.parseObject(str));
        }
        pager.setData(list);
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        appResultMessage.setData(pager);
        return appResultMessage;
    }

    /**
     * 【我的审批】获取待审批出差任务
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getNoApprovalTripTask", method = RequestMethod.POST)
    public AppResultMessage getNoApprovalTripTask(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        int pageNo = data.getIntValue("pageNo");
        int pageSize = data.getIntValue("pageSize");
        String filter = data.getString("filter");
        Pager pager =
            attWorkflowService.findPersonalTask(pin, AttConstant.FLOW_TYPE_TRIP, pageNo - 1, pageSize, filter);
        List<String> dataList = (List<String>)pager.getData();
        List<JSONObject> list = new ArrayList<>();
        for (String str : dataList) {
            list.add(JSONObject.parseObject(str));
        }
        pager.setData(list);
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        appResultMessage.setData(pager);
        return appResultMessage;
    }

    /**
     * 【我的审批】获取已审批出差任务
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getApprovalTripTask", method = RequestMethod.POST)
    public AppResultMessage getApprovalTripTask(@RequestBody JSONObject data) {
        String pin = request.getParameter("user_name");
        int pageNo = data.getIntValue("pageNo");
        int pageSize = data.getIntValue("pageSize");
        String filter = data.getString("filter");
        Pager pager =
            attWorkflowService.findApprovedTask(pin, AttConstant.FLOW_TYPE_TRIP, pageNo - 1, pageSize, filter);
        List<String> dataList = (List<String>)pager.getData();
        List<JSONObject> list = new ArrayList<>();
        for (String str : dataList) {
            list.add(JSONObject.parseObject(str));
        }
        pager.setData(list);
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        appResultMessage.setData(pager);
        return appResultMessage;
    }

    /**
     * 【我的审批】审批详情
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/getApprovalDetailInfo", method = RequestMethod.POST)
    public AppResultMessage getApprovalDetailInfo(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        // taskId 审批任务节点ID
        String taskId = data.getString("taskId");
        try {
            AttApiApplyTaskDetailItem item = attWorkflowService.findByTaskId(taskId);
            AppResultMessage appResultMessage = AppResultMessage.successMessage();
            appResultMessage.setData(item);
            return appResultMessage;
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    /**
     * 【我的审批】申请审批
     *
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/31 13:52
     * @since 1.0.0
     */
    @RequestMapping(value = "/auditTask", method = RequestMethod.POST)
    public AppResultMessage auditTask(@RequestBody JSONObject data) {
        String lang = data.getString("lang");
        String pin = request.getParameter("user_name");
        // taskId 审批任务节点ID
        String taskId = data.getString("taskId");
        // approve 审批结果 true/false
        String approve = data.getString("result");
        // comment 批注
        String comment = data.getString("remark");
        // notifierPins 知会人pins
        String notifierPins = data.getString("notifierPins");

        try {
            attWorkflowService.completeTask(pin, taskId, approve, comment, notifierPins);
            return AppResultMessage.successMessage();
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return AppResultMessage.failMessage(BaseLanguageUtil.getI18nByAppLang(lang, e.getMessage()));
            }
            return AppResultMessage.failMessage();
        }
    }

    // /**
    // * 获取人员日统计报表
    // *
    // * @param data:
    // * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
    // * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
    // * @date 2024/12/30 14:08
    // * @since 1.0.0
    // */
    // @RequestMapping(value = "/getDayDetailReport", method = RequestMethod.POST)
    // public AppResultMessage getDayDetailReport(@RequestBody JSONObject data) {
    // AppResultMessage appResultMessage = AppResultMessage.successMessage();
    // String pin = request.getParameter("user_name");
    // String date = data.getString("date");
    // Date attDate = DateUtil.stringToDate(date, DateUtil.DateStyle.YYYY_MM_DD);
    // Date startDate = DateUtil.getDayBeginTime(attDate);
    // Date endDate = DateUtil.getDayEndTime(attDate);
    // AttApiDayDetailReportItem attDayDetailReportItem =
    // attRecordService.getPersonDetailReport(pin, startDate, endDate);
    // AttAppDayDetailReportItem appDayDetailReportItem = new AttAppDayDetailReportItem();
    // BeanUtils.copyProperties(attDayDetailReportItem, appDayDetailReportItem);
    // appResultMessage.setData( appDayDetailReportItem);
    // return appResultMessage;
    // }
    //
    // /**
    // * 获取人员月统计报表
    // *
    // * @param data:
    // * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
    // * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
    // * @date 2024/12/30 14:13
    // * @since 1.0.0
    // */
    // @RequestMapping(value = "/getMonthReport", method = RequestMethod.POST)
    // public AppResultMessage getMonthReport(@RequestBody JSONObject data) {
    // AppResultMessage appResultMessage = AppResultMessage.successMessage();
    // String pin = request.getParameter("user_name");
    // String date = data.getString("date");
    // Date monthStart = DateUtil.stringToDate(date + "-01 00:00:00", DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
    // Date monthEnd = DateUtil.getDayEndTime(AttDateUtils.getMonthOfLastDay(monthStart));
    //
    // AttMonthStatisticalReportItem attMonthStatisticalReportItem =
    // attMonthStatisticalReportService.getItemByPinAndDate(pin, monthStart, monthEnd);
    //
    // AttAppMonthStatisticalReportItem AppMonthStatistical = new AttAppMonthStatisticalReportItem();
    // BeanUtils.copyProperties(attMonthStatisticalReportItem, AppMonthStatistical);
    // appResultMessage.setData(AppMonthStatistical);
    // return appResultMessage;
    // }

    // /**
    // * 获取人员考勤计算结果
    // *
    // * @param data:
    // * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
    // * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
    // * @date 2024/12/25 14:24
    // * @since 1.0.0
    // */
    // @RequestMapping(value = "/getRecordByPinAndData", method = RequestMethod.POST)
    // public AppResultMessage getRecordByPinAndData(@RequestBody JSONObject data) {
    // AppResultMessage appResultMessage = AppResultMessage.successMessage();
    // String pin = request.getParameter("user_name");
    // String startTime = data.getString("startTime");
    // String endTime = data.getString("endTime");
    // Date startDate = DateUtil.stringToDate(startTime);
    // Date endDate = DateUtil.stringToDate(endTime);
    //
    // AttRecordItem attRecordItem = new AttRecordItem();
    // attRecordItem.setPersonPin(pin);
    // attRecordItem.setStartDate(startDate);
    // attRecordItem.setEndDate(endDate);
    // attRecordItem.setEquals(true);
    // List<AttRecordItem> attRecordItems = attRecordService.getByCondition(attRecordItem);
    // appResultMessage.setData(attRecordItems);
    // return appResultMessage;
    // }
    //
    // /**
    // * 分页查询考勤计算结果
    // *
    // * @param data:
    // * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
    // * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
    // * @date 2024/12/25 14:24
    // * @since 1.0.0
    // */
    // @RequestMapping(value = "/getRecordPage", method = RequestMethod.POST)
    // public AppResultMessage getRecordPage(@RequestBody JSONObject data) {
    // AppResultMessage appResultMessage = AppResultMessage.successMessage();
    // String pin = request.getParameter("user_name");
    // String inPin = data.getString("inPin");
    // String startTime = data.getString("attDateStart");
    // String endTime = data.getString("attDateEnd");
    // Date startDate = DateUtil.stringToDate(startTime);
    // Date endDate = DateUtil.stringToDate(endTime);
    // int pageNo = data.getIntValue("pageNo");
    // int pageSize = data.getIntValue("pageSize");
    // AttRecordItem attRecordItem = new AttRecordItem();
    // attRecordItem.setStartDate(startDate);
    // attRecordItem.setEndDate(endDate);
    // attRecordItem.setInPersonPin(inPin);
    // Pager pager = attRecordService.getItemsByPage(attRecordItem, pageNo - 1, pageSize);
    // appResultMessage.setData(pager);
    // return appResultMessage;
    // }

    /******** 1、新增海外APP考勤人员月统计和月出勤接口。- 20210220 add *********/
    /**
     * 获取人员月统计报表数据
     *
     * @param jsonObject:
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/2/19 11:33
     * @since 1.0.0
     */
    @RequestMapping(value = "/monthlyStatistics", method = RequestMethod.POST)
    public JSONObject monthlyStatistics(@RequestBody JSONObject jsonObject) {
        String pin = jsonObject.getString("pin");
        String date = jsonObject.getString("date");
        // 根据年月获取这个月的第一天和最后一天
        String[] dateArray = date.split("-");
        int year = Integer.parseInt(dateArray[0]);
        int month = Integer.parseInt(dateArray[1]);
        Date startDate = AttDateUtils.getFirstDayOfMonth(year, month);
        Date endDate = AttDateUtils.getLastDayOfMonth(year, month);
        return attAppService.monthlyStatistics(pin, startDate, endDate);
    }

    /**
     * 获取人员月考勤数据
     *
     * @param jsonObject:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/2/19 11:31
     * @since 1.0.0
     */
    @RequestMapping(value = "/monthlyAttendanceCalendar", method = RequestMethod.POST)
    public AppResultMessage monthlyAttendanceCalendar(@RequestBody JSONObject jsonObject) {
        String pin = jsonObject.getString("pin");
        String date = jsonObject.getString("time");
        return attAppService.getAttendanceCalendar(pin, date);
    }
}