package com.zkteco.zkbiosecurity.att.api.controller;

import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.att.api.vo.AttApiAreaItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.zkteco.zkbiosecurity.att.constants.AttApiConstant;
import com.zkteco.zkbiosecurity.att.service.AttAreaPersonService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 考勤区域接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/11/28 16:50
 * @since 1.0.0
 */
@Controller
@RequestMapping(value = {"/api/v2/attAreaPerson"})
@Slf4j
@Api(tags = "AttAreaPerson", description = "att area person")
public class AttApiV2AreaPersonController {

    @Autowired
    private AttAreaPersonService attAreaPersonService;

    @ApiLogRequest(requestParams = {"pageNo", "pageSize"})
    @ApiOperation(value = "Get Area Pager", notes = "Return Area Pager", response = ApiResultMessage.class)
    @RequestMapping(value = "/area/list", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public ApiResultMessage list(@RequestParam Integer pageNo, @RequestParam Integer pageSize) {
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(AttApiConstant.API_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        if (pageSize > 1000) {
            return ApiResultMessage.message(AttApiConstant.API_PAGE_OVERSIZE,
                I18nUtil.i18nCode("common_api_pageSizeOver"));
        }
        Pager pager = attAreaPersonService.getApiAreaPager(pageNo, pageSize);
        return ApiResultMessage.successMessage(pager);
    }
    /**
     * 根据code获取区域信息
     * @param code
     * @return
     */
    @ApiLogRequest(requestParams = {"code"})
    @ApiOperation(value = "Get Area By Code", notes = "Return Area", response = ApiResultMessage.class)
    @RequestMapping(value = "/area/get", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public ApiResultMessage getArea(@RequestParam String code) {
        if (StringUtils.isBlank(code)) {
            return ApiResultMessage.message(AttApiConstant.BASE_AREA_CODE_NOTNULL,
                    I18nUtil.i18nCode("att_api_areaCodeNotNull"));
        }
        AttApiAreaItem attApiAreaItem = attAreaPersonService.getApiAreaByCode(code);
        return ApiResultMessage.successMessage(attApiAreaItem);
    }
}
