package com.zkteco.zkbiosecurity.att.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.remote.AttTripRemote;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 出差（员工自助）
 */
@Controller
public class AttTripController extends BaseController implements AttTripRemote {

    @RequiresPermissions("att:trip")
    @Override
    public ModelAndView index() {
        request.setAttribute("flowType", AttConstant.FLOW_TYPE_TRIP);
        return new ModelAndView("att/flowable/leave/attLeave");
    }
}