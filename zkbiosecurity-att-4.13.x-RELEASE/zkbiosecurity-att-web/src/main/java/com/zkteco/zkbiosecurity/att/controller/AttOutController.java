package com.zkteco.zkbiosecurity.att.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.remote.AttOutRemote;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 外出（员工自助）
 */
@Controller
public class AttOutController extends BaseController implements AttOutRemote {

    @RequiresPermissions("att:out")
    @Override
    public ModelAndView index() {
        request.setAttribute("flowType", AttConstant.FLOW_TYPE_OUT);
        return new ModelAndView("att/flowable/leave/attLeave");
    }
}