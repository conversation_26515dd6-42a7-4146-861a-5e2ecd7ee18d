package com.zkteco.zkbiosecurity.att.util;

import java.io.IOException;
import java.io.InputStream;
import java.io.PushbackInputStream;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;

import com.google.common.collect.Maps;
import com.zkteco.zkbiosecurity.base.annotation.DateType;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
 * @version V1.0
 * @Description 考勤导入工具类（获取行号）
 * @date 2020/4/22
 */
@Slf4j
public class AttImportWithRowNumUtil {
    public static <T> List<T> excelImport(InputStream inputStream, Class<T> cls) {
        Workbook workbook = createImportWorkBook(inputStream);
        Sheet sheet = workbook.getSheetAt(0);
        Field[] fields = cls.getDeclaredFields();
        List<Field> fieldList =
            Arrays.stream(fields).filter(f -> f.getAnnotation(GridColumn.class) != null).collect(Collectors.toList());
        Row columnNames = sheet.getRow(1);
        DataFormatter formatter = new DataFormatter();
        List<T> list = new ArrayList();
        Field field = null;
        try {
            for (Row row : sheet) {
                int rowNum = row.getRowNum();
                if (rowNum > 1) {
                    T t = cls.newInstance();
                    for (Cell cell : row) {
                        Cell columnNameCell = columnNames.getCell(cell.getColumnIndex());
                        if (StringUtils.isBlank(formatter.formatCellValue(columnNameCell))) {
                            new ZKBusinessException("common_report_columnError");
                        }

                        String cellValue = formatter.formatCellValue(cell);

                        String columnName = formatter.formatCellValue(columnNameCell);
                        field = fieldList.stream().filter(
                            f -> columnName.equals(I18nUtil.i18nCode(f.getAnnotation(GridColumn.class).label())))
                            .findFirst().orElseThrow(() -> new ZKBusinessException("common_report_columnError"));

                        field.setAccessible(true);
                        // 增加导入存在format值类型的判断
                        String formatStr = field.getAnnotation(GridColumn.class).format();
                        // format = "0=att_overtime_normal,1=att_overtime_rest,2=att_shift_holidayOt"
                        if (!"".equals(formatStr)) {
                            if (StringUtils.isBlank(cellValue)) {
                                continue;
                            }
                            String[] splitArr = StringUtils.split(formatStr, ",");
                            List<String> fomatString = Arrays.asList(splitArr);
                            // key是应该设的值，value是对应的国际化，导入时值是对应的国际化
                            Map formatMap = Maps.newHashMap();
                            for (String s : fomatString) {
                                String[] splitEqual = s.split("=");
                                formatMap.put(I18nUtil.i18nCode(splitEqual[1]), splitEqual[0]);
                            }
                            Set strValue = formatMap.keySet();
                            if (strValue.contains(cellValue)) {
                                field.set(t, ConvertUtils.convert(formatMap.get(cellValue),field.getType()));
                            } else {
                                field.set(t, null);
                            }
                        } else {
                            if (field.getType() == Date.class || field.getType() == Timestamp.class) {
                                DateType dateType = field.getAnnotation(DateType.class);
                                String format = getDateFormt(dateType);
                                if (cell.getCellType() == CellType.NUMERIC) {
                                    if (cell.getNumericCellValue() > 0) {
                                        field.set(t, org.apache.poi.ss.usermodel.DateUtil.getJavaDate(cell.getNumericCellValue()));
                                    }
                                } else {
                                    if (StringUtils.isNotBlank(cellValue)) {
                                        field.set(t, DateUtil.stringToDate(cellValue, format));
                                    }
                                }
                            } else {
                                if (StringUtils.isNotBlank(cellValue)) {
                                    field.set(t, ConvertUtils.convert(cellValue, field.getType()));
                                }
                            }
                        }
                    }
                    // 设置行数
                    field = cls.getDeclaredField("rowNum");
                    field.setAccessible(true);
                    field.set(t, ConvertUtils.convert(rowNum + 1, Integer.class));
                    list.add(t);
                }
            }
        } catch (ZKBusinessException e) {
            log.error("import business error", e);
            throw e;
        } catch (Exception e) {
            log.error("import error", e);
            throw new ZKBusinessException("common_op_failed");
        }
        return list;
    }

    /**
     * 创建工作目录
     *
     * @param inputStream
     * @return org.apache.poi.ss.usermodel.Workbook
     * @Description:
     * <AUTHOR>
     * @date 2018/4/25 15:46
     */
    public static Workbook createImportWorkBook(InputStream inputStream) {
        Workbook workbook = null;
        try {
            workbook = getImportWorkBook(inputStream);
        } catch (Exception ex) {
            throw new ZKBusinessException("Open the EXCEL file flow failure!", ex);
        }
        return workbook;
    }

    /**
     * @param inputStream
     * @return org.apache.poi.ss.usermodel.Workbook
     * @Description: 根据不同版本的office软件创建不同excel文件
     * <AUTHOR>
     * @date 2018/4/25 15:47
     */
    public static Workbook getImportWorkBook(InputStream inputStream) throws IOException {
        Workbook workbook = null;
        // 输入流必须支持mark/reset方法
        if (!inputStream.markSupported()) {
            inputStream = new PushbackInputStream(inputStream, 8);
        }
        // 使用微软Office文件系统,Excel2003
        // if (POIFSFileSystem.hasPOIFSHeader(inputStream)) {
        // workbook = new HSSFWorkbook(inputStream);
        // } else if (DocumentFactoryHelper.hasOOXMLHeader(inputStream)) {
        // workbook = new XSSFWorkbook(inputStream);
        // }
        workbook = WorkbookFactory.create(inputStream);
        return workbook;
    }

    public static String getDateFormt(DateType dateType) {
        String format = "yyyy-MM-dd HH:mm:ss";
        if (dateType != null) {
            switch (dateType.type()) {
                case "date":
                    format = "yyyy-MM-dd";
                    break;
                case "time":
                    format = "HH:mm:ss";
                    break;
                case "timestamp":
                    format = "yyyy-MM-dd HH:mm:ss";
                    break;
                default:
                    break;
            }
        }
        return format;
    }
}
