package com.zkteco.zkbiosecurity.att.controller;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.remote.AttLeaveRemote;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.util.AttExcelUtil;
import com.zkteco.zkbiosecurity.att.util.AttImportWithRowNumUtil;
import com.zkteco.zkbiosecurity.att.util.HttpsUtils;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveItem;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.att.vo.AttPersonItem;
import com.zkteco.zkbiosecurity.base.annotation.LogChangeRequest;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 请假
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 9:56
 * @since 1.0.0
 */
@Controller
public class AttLeaveController extends ExportController implements AttLeaveRemote {

    @Autowired
    private AttLeaveService attLeaveService;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttAdjustService attAdjustService;
    @Autowired
    private AttOvertimeService attOvertimeService;
    @Autowired
    private AttClassService attClassService;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private AttExcelUtil attExcelUtil;

    @RequiresPermissions("att:leave")
    @Override
    public ModelAndView index() {

        if (getCurrentSubject().getStaff()) {
            request.setAttribute("flowType", AttConstant.FLOW_TYPE_LEAVE);
            return new ModelAndView("att/flowable/leave/attLeave");
        } else {
            return new ModelAndView("att/leave/attLeave");
        }
    }

    @RequiresPermissions("att:leave:add")
    @Override
    public ModelAndView edit(String id) {
        SecuritySubject securitySubject = getCurrentSubject();
        Boolean staff = securitySubject.getStaff();
        AttLeaveItem item = null;
        if (StringUtils.isNotBlank(id)) {
            item = attLeaveService.getItemById(id);
        } else {
            if (null != staff && staff) {
                item = new AttLeaveItem();
                item.setPersonId(securitySubject.getUserId());
            }
        }
        request.setAttribute("item", item);
        request.setAttribute("tripAndOutId", attLeaveTypeService.getTripAndOutId());
        return new ModelAndView("att/leave/editAttLeave");
    }

    @Override
    public ModelAndView viewLeavePhoto(String key) {
        request.setAttribute("key", key);
        return new ModelAndView("att/flowable/leave/leavePhoto");
    }

    @RequiresPermissions("att:leave:add")
    @LogChangeRequest(module = "att_module", object = "att_leftMenu_leave", opType = "common_op_new",
        vo = AttLeaveItem.class, service = AttLeaveService.class)
    @Override
    public ZKResultMsg save(AttLeaveItem item,
        @RequestParam(value = "leaveImage", required = false) MultipartFile[] files) {

        // 获取申请人员id和保存请假单照片
        String personIds = request.getParameter("personIds");
        StringBuffer leaveImageBuf = new StringBuffer("");
        if (Objects.nonNull(files) && files.length > 0) {
            for (MultipartFile uploadFile : files) {
                if (!uploadFile.isEmpty()) {
                    String filename = System.currentTimeMillis() + ".jpg";
                    String leaveImageStr = FileUtils.saveFileToServer("att", "leave/image", filename, uploadFile);
                    // 图片加密
                    FileEncryptUtil.encryptFileByPath(leaveImageStr);
                    // 缩略图加密
                    String thumbPic =
                        leaveImageStr.replaceFirst(filename, "") + FileUtils.thumbPath + FileUtils.separator + filename;
                    FileEncryptUtil.encryptFileByPath(thumbPic);
                    leaveImageBuf.append(leaveImageStr.replaceAll("\\\\", "/")).append(",");
                }
            }
            if (leaveImageBuf.length() > 0) {
                leaveImageBuf.deleteCharAt(leaveImageBuf.length() - 1);
            }
        }
        item.setLeaveImagePath(leaveImageBuf.toString());
        attLeaveService.saveItem(personIds, item, request.getSession().getId());

        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("att:leave:refresh")
    @Override
    public DxGrid list(AttLeaveItem condition) {
        buildCondition(condition, request);
        Pager pager = attLeaveService.loadPagerByAuthUserFilter(request.getSession().getId(), condition, getPageNo(),
            getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:leave:del")
    @LogRequest(module = "att_module", object = "att_leftMenu_leave", opType = "common_op_del",
        requestParams = {"pins"}, opContent = "att_person_pin")
    @Override
    public ZKResultMsg delete(String ids) {
        attLeaveService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("att:leave:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_leave", opType = "common_op_export",
        requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AttLeaveItem attLeaveItem = new AttLeaveItem();
        setConditionValue(attLeaveItem);
        buildCondition(attLeaveItem, request);
        List<AttLeaveItem> intemList = attLeaveService.getItemData(request.getSession().getId(),
            attLeaveItem, getBeginIndex(), getEndIndex());
        for (AttLeaveItem attLeave : intemList) {
            float leaveLongHour = ((float)attLeave.getLeaveLong()) / 60;
            double result = new BigDecimal(leaveLongHour).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
            attLeave.setLeaveLongHour(String.valueOf(result));
        }
        excelExport(intemList, AttLeaveItem.class);
    }

    /**
     * 人工自助请假、外出、出差界面分开需要传标识获取对应的数据 查询已完成和导入未审批的数据
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/14 10:32
     * @param condition
     * @param request
     * @return void
     */
    private void buildCondition(AttLeaveItem condition, HttpServletRequest request) {
        String flowType = request.getParameter("flowType");
        if (StringUtils.isNotBlank(flowType)) {
            if ("trip".equals(flowType)) {
                condition.setLeaveTypeNo(AttCalculationConstant.AttAttendStatus.TRIP);
            } else if ("out".equals(flowType)) {
                condition.setLeaveTypeNo(AttCalculationConstant.AttAttendStatus.OUT);
            } else {
                condition.setNotInLeaveTypeNo(
                    AttCalculationConstant.AttAttendStatus.TRIP + "," + AttCalculationConstant.AttAttendStatus.OUT);
            }
        }
    }

    @Override
    public ModelAndView showImage(String id) {
        AttLeaveItem attLeaveItem = attLeaveService.getItemById(id);
        String leaveImage = StringUtils.defaultString(attLeaveItem.getLeaveImagePath(), "");
        request.setAttribute("leaveImage", leaveImage);
        return new ModelAndView("att/leave/leaveImage");
    }

    @Override
    public ZKResultMsg calLeaveTime() {
        String personId = request.getParameter("personId");
        String startDateTimeStr = request.getParameter("startDateTime");
        String endDateTimeStr = request.getParameter("endDateTime");
        String leaveTypeId = request.getParameter("leaveTypeId");
        AttLeaveTypeItem attLeaveTypeItem = new AttLeaveTypeItem();
        if (StringUtils.isNotBlank(leaveTypeId)) {
            attLeaveTypeItem = attLeaveTypeService.getItemById(leaveTypeId);
        } else {
            attLeaveTypeItem.setConvertCount(1.0);
            attLeaveTypeItem.setConvertType(AttConstant.ATT_CONVERT_ABORT);
            attLeaveTypeItem.setConvertUnit(AttConstant.ATT_CONVERT_UNIT_MINUTE);
        }
        Date startDateTime = DateUtil.stringToDate(startDateTimeStr, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
        Date endDateTime = DateUtil.stringToDate(endDateTimeStr, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
        int minute = attLeaveService.calLeaveTimeMinutes(personId, startDateTime, endDateTime, attLeaveTypeItem);
        String hour = attParamService.minutesToHourFormat(new BigDecimal(minute));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("minute", minute);
        jsonObject.put("hour", hour);
        return I18nUtil.i18nMsg(new ZKResultMsg(jsonObject));
    }

    @Override
    public ZKResultMsg existApply(String personIds, Date startTime, Date endTime, String exceptionType,
        String leaveTypeId) {
        return attLeaveService.existApply(personIds, startTime, endTime);
    }

    @Override
    public ZKResultMsg existAdjustOrClassApply(String adjustPersonId, Date adjustDate, String swapPersonId,
        Date swapDate) {
        // 判断是否存在调休补班
        if (attAdjustService.existAdjust(Arrays.asList(adjustPersonId.split(",")), adjustDate, adjustDate)) {
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("att_apply_adjustTips"));
        } else if (attClassService.existApply(adjustPersonId, adjustDate, swapPersonId, swapDate)) {
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("att_apply_classTips"));
        }
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @Override
    public void importTemplate(HttpServletRequest request, HttpServletResponse response) {
        JSONObject jsonColumn = new JSONObject(true);
        jsonColumn.put("personPin", "");
        jsonColumn.put("personName", "");
        if (!"zh_CN".equals(LocaleMessageSourceUtil.language)) {
            jsonColumn.put("personLastName", "");
        }
        jsonColumn.put("leaveTypeName", "");
        jsonColumn.put("startDatetime", "");
        jsonColumn.put("endDatetime", "");
        jsonColumn.put("remark", "");
        Map<String, Map<String, String>> jsonCloumnMap = new HashMap<>();
        Map<String, String> jsonMap = new HashMap<>();
        jsonMap.put("jsonColumn", jsonColumn.toJSONString());
        jsonCloumnMap.put("jsonColumn", jsonMap);
        attExcelUtil.attExcelExport(request, response, new ArrayList<>(), AttLeaveItem.class, jsonCloumnMap);
    }

    @Override
    public ZKResultMsg importExcel(MultipartFile upload) throws IOException {
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_uploadFileSuccess") + "<br/>"));
            List<AttLeaveItem> itemList =
                AttImportWithRowNumUtil.excelImport(upload.getInputStream(), AttLeaveItem.class);
            progress += 10;
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_resolutionComplete") + "<br/>"));
            return I18nUtil.i18nMsg(attLeaveService.importExcel(itemList, request.getSession().getId()));
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                    + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                    new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("Import AttSign Info Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
    }

    @RequiresPermissions("att:leave:approval")
    @LogRequest(module = "att_module", object = "att_leftMenu_leave", opType = "att_apply_pass",
        requestParams = {"pins"}, opContent = "att_person_pin")
    @Override
    public ZKResultMsg approval(String ids) {
        if (attLeaveService.hasStaffApply(ids)) {
            return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg("att_approve_withoutPermissionApproval"));
        }
        attLeaveService.approval(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @RequiresPermissions("att:leave:approval")
    @LogRequest(module = "att_module", object = "att_leftMenu_leave", opType = "att_exception_refuse",
        requestParams = {"pins"}, opContent = "att_person_pin")
    @Override
    public ZKResultMsg refuse(String ids) {
        if (attLeaveService.hasStaffApply(ids)) {
            return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg("att_approve_withoutPermissionApproval"));
        }
        attLeaveService.refuse(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @Override
    public String getLeaveImage() {
        ByteArrayOutputStream out = null;
        HttpURLConnection httpUrl = null;
        InputStream is = null;
        byte[] bytes = new byte[1024];
        try {
            String url = request.getParameter("url");
            if (StringUtils.isNotBlank(url)) {
                if (!url.startsWith("http") && !url.startsWith("https")) {
                    String decryptFileBase64 = FileEncryptUtil.getDecryptFileBase64(url);
                    // 读取加密文件图片
                    bytes = Base64.getDecoder().decode(decryptFileBase64);
                } else {
                    if (url.startsWith("https")) {
                        bytes = HttpsUtils.doGet(url);
                    } else if (url.startsWith("http")) {
                        URL photoUrl = new URL(url);
                        try {
                            httpUrl = (HttpURLConnection)photoUrl.openConnection();
                            httpUrl.setConnectTimeout(3000);
                            httpUrl.connect();
                            is = httpUrl.getInputStream();
                            if (is != null) {
                                out = new ByteArrayOutputStream();
                                int len = 0;
                                while ((len = is.read(bytes)) != -1) {
                                    out.write(bytes, 0, len);
                                }
                                bytes = out.toByteArray();
                            }
                        } finally {
                            if (is != null) {
                                is.close();
                            }
                            if (out != null) {
                                out.close();
                            }
                            if (httpUrl != null) {
                                httpUrl.disconnect();
                            }
                        }
                    }
                }
                response.reset();
                response.getOutputStream().write(bytes);
            }
        } catch (Exception e) {
            log.error("exception", e);
        }
        return null;
    }

    @Override
    public ZKResultMsg getImageUrl(String id) {
        AttLeaveItem attLeaveItem = attLeaveService.getItemById(id);
        ZKResultMsg resultMsg = new ZKResultMsg();
        if (attLeaveItem != null && StringUtils.isNotBlank(attLeaveItem.getLeaveImagePath())) {
            resultMsg.setRet("ok");
            resultMsg.setData(attLeaveItem.getLeaveImagePath());
        } else {
            resultMsg.setRet("fail");
            resultMsg.setMsg(I18nUtil.i18nCode("att_leave_imageShow"));
        }
        return resultMsg;
    }
}