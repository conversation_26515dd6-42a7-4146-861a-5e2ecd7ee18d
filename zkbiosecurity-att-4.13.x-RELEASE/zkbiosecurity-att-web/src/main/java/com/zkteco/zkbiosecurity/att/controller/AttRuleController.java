package com.zkteco.zkbiosecurity.att.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.core.utils.SpringContextUtil;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.remote.AttRuleRemote;
import com.zkteco.zkbiosecurity.att.service.AttAnnualLeaveReportService;
import com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService;
import com.zkteco.zkbiosecurity.att.service.AttRuleService;
import com.zkteco.zkbiosecurity.att.vo.AttAnnualLeaveRuleItem;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 考勤规则
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:00
 * @since 1.0.0
 */
@Controller
public class AttRuleController extends BaseController implements AttRuleRemote {

    @Autowired
    private AttRuleService attRuleService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;
    @Autowired
    private AttAnnualLeaveReportService attAnnualLeaveReportService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @RequiresPermissions("att:rule")
    @Override
    public ModelAndView index() {
        Map<String, String> attParams = attRuleService.getAttParams();
        if (Objects.isNull(attParams.get("att.baseRule.maxOvertimeType"))) {
            attParams.put("att.baseRule.maxOvertimeType", "notLimited");
        }
        if (Objects.isNull(attParams.get("att.baseRule.maxOvertimeMinutes"))) {
            attParams.put("att.baseRule.maxOvertimeMinutes", "0");
        }
        request.setAttribute("attParams", attParams);

        // 假种前端展示处理
        AttLeaveTypeItem condition = new AttLeaveTypeItem();
        condition.setMark(AttConstant.ATT_CONVERT_MARK_PARAMS);
        List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getByCondition(condition);
        request.setAttribute("attLeaveTypeItemList", attLeaveTypeItemList);

        // 年假前端展示处理
        List<AttAnnualLeaveRuleItem> attAnnualLeaveRuleItemList = new ArrayList<>();
        if (attParams.containsKey("att.annualLeave.rule")) {
            String ruleStr = attParams.get("att.annualLeave.rule");
            attAnnualLeaveRuleItemList = attAnnualLeaveReportService.buildRuleItem(ruleStr);
        }
        request.setAttribute("attAnnualLeaveRuleItemList", attAnnualLeaveRuleItemList);

        try {
            SpringContextUtil.getBean("workflowInit");
            request.setAttribute("showEmployeeSet", "true");
        } catch (Exception e) {
            request.setAttribute("showEmployeeSet", "false");
        }

        return new ModelAndView("att/rule/attRule");
    }

    @RequiresPermissions("att:rule:edit")
    @LogRequest(module = "att_module", object = "att_leftMenu_rule", opType = "common_op_edit",
        opContent = "att_rule_baseRuleSet",
        sysParams = {"att.baseRule.signIn", "att.baseRule.shortestMinutes", "att.baseRule.signOut",
            "att.baseRule.longestMinutes", "att.baseRule.crossDay", "att.baseRule.countOvertime",
            "att.baseRule.lateAndEarly", "att.baseRule.smartFindClass", "att.baseRule.noSignInCountType",
            "att.baseRule.noSignInCountLateMinute", "att.baseRule.noSignOffCountType",
            "att.baseRule.noSignOffCountEarlyMinute", "att.countConvert.decimal", "att.self.loginEntrance"})
    @Override
    public ZKResultMsg save(@RequestParam Map<String, String> params, AttLeaveTypeItem attLeaveTypeItem) {

        // 假种配置处理
        for (AttLeaveTypeItem item : attLeaveTypeItem.getAttLeaveTypeItemList()) {
            attLeaveTypeService.saveItem(item);
        }

        // 年假配置处理
        attAnnualLeaveReportService.buildRuleValue(params);

        attRuleService.saveItem(params);

        // 启用定时清空发放年假
        attAnnualLeaveReportService.setAnnualLeaveScheduled(params.get("att.annualLeave.calculateMonth"),
            params.get("att.annualLeave.calculateDay"));

        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public ZKResultMsg getParams() {
        Map<String, String> map = attRuleService.getAttParams();
        return I18nUtil.i18nMsg(new ZKResultMsg(map));
    }

    @Override
    public ZKResultMsg getCardStatus() {
        BaseSysParamItem sysParamItem = new BaseSysParamItem();
        sysParamItem.setParamName("att.cardStatus.");
        List<BaseSysParamItem> baseSysParamItemList = baseSysParamService.getByCondition(sysParamItem);
        List<SelectItem> items = new ArrayList<>();
        for (BaseSysParamItem item : baseSysParamItemList) {
            SelectItem selectItem = new SelectItem();
            String[] valueArray = item.getParamValue().split(",");
            selectItem.setValue(item.getParamName());
            selectItem.setText(valueArray[2]+"（"+item.getDescription()+"）");
            items.add(selectItem);
        }
        return new ZKResultMsg(items);
    }
}