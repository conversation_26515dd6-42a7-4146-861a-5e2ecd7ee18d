package com.zkteco.zkbiosecurity.att.api.controller;

import com.zkteco.zkbiosecurity.att.constants.AttApiConstant;
import com.zkteco.zkbiosecurity.att.service.AttTransactionService;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * @Auther: lambert.li
 * @Date: 2018/11/20 08:39
 */
@Controller
@RequestMapping(value = {"/api/third"})
@Slf4j
public class AttApiThirdPartyController {

    @Autowired
    private AttTransactionService attTransactionService;

    /**
     * 产生考勤记录(大掌柜设备当考勤设备)---API
     * 
     * @auther lambert.li
     * @date 2018/11/19 9:38
     * @param deviceNum
     * @param pin
     * @param time
     * @return
     */
    @RequestMapping(value = "/addAttTransaction", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public ApiResultMessage addAttTransaction(@RequestParam(name = "deviceNum") Integer deviceNum,
        @RequestParam(name = "pin") String pin, @RequestParam(name = "time") String time) {
        ApiResultMessage rs = ApiResultMessage.successMessage();
        try {
            attTransactionService.addApiTransaction(deviceNum, pin, time);
        } catch (Exception e) {
            log.error("api third/addAttTransaction error ", e);
            rs = ApiResultMessage.message(AttApiConstant.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("common_api_programError"));
        }
        return rs;
    }
}
