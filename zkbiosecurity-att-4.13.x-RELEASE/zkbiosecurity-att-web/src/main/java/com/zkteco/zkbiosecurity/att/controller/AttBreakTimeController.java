package com.zkteco.zkbiosecurity.att.controller;

import java.util.ArrayList;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.remote.AttBreakTimeRemote;
import com.zkteco.zkbiosecurity.att.service.AttBreakTimeService;
import com.zkteco.zkbiosecurity.att.vo.AttBreakTimeItem;
import com.zkteco.zkbiosecurity.att.vo.AttBreakTimeSelectItem;
import com.zkteco.zkbiosecurity.base.annotation.LogChangeRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 休息时间段
 *
 * <AUTHOR>
 * @date 2019/5/30
 */
@Controller
public class AttBreakTimeController extends BaseController implements AttBreakTimeRemote {

    @Autowired
    private AttBreakTimeService attBreakTimeService;

    @Override
    public ModelAndView index() {
        return new ModelAndView("att/breakTime/attBreakTime");
    }

    @RequiresPermissions("att:breakTime:edit")
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("item", attBreakTimeService.getItemById(id));
            request.setAttribute("isExistFkData", attBreakTimeService.isExistFkData(id));
        }
        return new ModelAndView("att/breakTime/editAttBreakTime");
    }

    @RequiresPermissions("att:breakTime:edit")
    @LogChangeRequest(module = "att_module", object = "att_leftMenu_breakTime", opType = "common_op_edit",
        vo = AttBreakTimeItem.class, service = AttBreakTimeService.class)
    @Override
    public ZKResultMsg save(AttBreakTimeItem item) {
        ZKResultMsg res = new ZKResultMsg();
        attBreakTimeService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("att:breakTime:refresh")
    @Override
    public DxGrid list(AttBreakTimeItem condition) {
        Pager pager = attBreakTimeService.getItemsByPage(condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:breakTime:del")
    @Override
    public ZKResultMsg delete(String ids) {
        // 判断 删除的数据是否存在外键关联 add by jinxian.huang 2019-07-04
        if (attBreakTimeService.isExistFkData(ids)) {
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("att_common_relationDataCanNotDel"));
        }
        attBreakTimeService.deleteByIds(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @Override
    public DxGrid selectList(AttBreakTimeSelectItem condition) {
        Pager pager = null;
        if (AttConstant.NOSELECT.equals(condition.getType())) {
            pager = attBreakTimeService.selectList(request.getSession().getId(), condition, getPageNo(), getPageSize());
        } else {
            pager = new Pager(getPageNo(), getPageSize());
            pager.setData(new ArrayList<AttBreakTimeSelectItem>());
        }
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public String validName(String name) {
        boolean existsByName = !attBreakTimeService.existsByName(name);
        return String.valueOf(existsByName);
    }

    @Override
    public ZKResultMsg isExistBreakTime(String id, String startTime, String endTime) {
        AttBreakTimeItem item = attBreakTimeService.getItemByStartTimeAndEndTime(startTime, endTime);
        boolean isExist = false;
        if (Objects.nonNull(item)) {
            // 编辑时应排除自己如果没有修改动时间,修复编辑时修改名称保存提示已存在问题, by ljf 2019/11/28
            if (!item.getId().equals(id)) {
                isExist = true;
            }
        }
        return I18nUtil.i18nMsg(new ZKResultMsg(isExist));
    }
}