package com.zkteco.zkbiosecurity.att.api.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONArray;
import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.zkteco.zkbiosecurity.att.api.vo.AttApiTransactionItem;
import com.zkteco.zkbiosecurity.att.constants.AttApiConstant;
import com.zkteco.zkbiosecurity.att.service.AttTransactionService;
import com.zkteco.zkbiosecurity.base.annotation.ApiPermissions;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import springfox.documentation.annotations.ApiIgnore;

/**
 * @Auther: lambert.li
 * @Date: 2018/11/16 10:57
 */
@Controller
@RequestMapping(value = {"/api/transaction"})
@Api(tags = "AttTransaction", description = "att transaction")
public class AttApiTransactionController {

    @Autowired
    private AttTransactionService attTransactionService;

    /**
     * 分页获取考勤记录
     * 
     * @auther lambert.li
     * @date 2018/11/21 17:45
     * @param personPin
     * @param startDate
     * @param endDate
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiLogRequest(requestParams = {"personPin", "startDate", "endDate", "pageNo", "pageSize"})
    @ResponseBody
    @RequestMapping(value = "/listAttTransaction", method = RequestMethod.GET, produces = "application/json")
    @ApiOperation(value = "Get Att Transactions List", notes = "Return Att Transactions List",
        response = ApiResultMessage.class)
    public ApiResultMessage listAttTransaction(@RequestParam(required = false) String personPin,
        @RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate,
        @RequestParam Integer pageNo, @RequestParam Integer pageSize) {
        Date startTime = null;
        Date endTime = null;
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(AttApiConstant.API_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        if (pageSize > 1000) {
            return ApiResultMessage.message(AttApiConstant.API_PAGE_OVERSIZE,
                I18nUtil.i18nCode("common_api_pageSizeOver"));
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            startTime = DateUtil.stringToDate(startDate);
            endTime = DateUtil.stringToDate(endDate);
            if (startTime == null || endTime == null) {// 时间格式错误
                return ApiResultMessage.message(AttApiConstant.API_DATE_ERROR,
                    I18nUtil.i18nCode("common_dsTime_timeValid2"));
            }
            if (startTime.getTime() > endTime.getTime()) {// 开始时间不能大于结束时间
                return ApiResultMessage.message(AttApiConstant.API_DATE_STARTTIME_LARGE,
                    I18nUtil.i18nCode("common_dsTime_timeValid4"));
            }
        }
        List<AttApiTransactionItem> attApiTransactionItemList =
            attTransactionService.getApiTransactionList(personPin, startTime, endTime, pageNo, pageSize, false);
        return ApiResultMessage.successMessage(attApiTransactionItemList);
    }

    /**
     * 分页获取考勤打卡记录 支持线上同步调用
     * 
     * <AUTHOR>
     * @Date 2020/7/27 14:57
     * @param zkMessage
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     */
    @RequestMapping(value = "/getPersonAttTrans", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiIgnore
    public ZKResultMsg getPersonAttTrans(@RequestBody ZKMessage zkMessage) {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        try {
            Map<String, Object> content = zkMessage.getContent();
            Integer pageNo = MapUtils.getInteger(content, "pageNo");
            Integer pageSize = MapUtils.getInteger(content, "pageSize");
            String startDate = MapUtils.getString(content, "startDate");
            String endDate = MapUtils.getString(content, "endDate");
            String personPin = MapUtils.getString(content, "personPin");
            Date startTime = null;
            Date endTime = null;
            if (pageNo == null || pageNo < 1) {
                pageNo = 1;
            }
            if (pageSize == null) {
                pageSize = 50;
            }
            if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
                startTime = DateUtil.stringToDate(startDate);
                endTime = DateUtil.stringToDate(endDate);
                List<AttApiTransactionItem> attApiTransactionItemList =
                    attTransactionService.getApiTransactionList(personPin, startTime, endTime, pageNo, pageSize, true);
                resultMsg.setData(JSONArray.toJSONString(attApiTransactionItemList));
            }
        } catch (Exception e) {
            resultMsg = ZKResultMsg.failMsg();
        }
        return I18nUtil.i18nMsg(resultMsg);
    }
}
