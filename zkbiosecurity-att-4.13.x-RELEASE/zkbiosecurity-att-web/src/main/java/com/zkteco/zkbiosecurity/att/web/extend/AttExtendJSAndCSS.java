/*
 * File Name: AttExtendJSAndCSS.java Created by yuan<PERSON>.zou on 2018年3月21日 上午10:21:47. Copyright:Copyright ? 1985-2017
 * ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.att.web.extend;

import com.zkteco.zkbiosecurity.core.utils.PropertiesUtil;
import com.zkteco.zkbiosecurity.core.web.extend.ExtendJSAndCSS;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR> href:"mailto:<EMAIL>">yuanjun.zou</a>
 * @version v1.0
 */
@Component
public class AttExtendJSAndCSS implements ExtendJSAndCSS {

    @Override
    public List<String> extend() {
        List<String> exts = new ArrayList<String>();

        // 添加js
        exts.add("js/i18n-keys-att.js");
        exts.add("js/attCalendarSchDX.js");
        exts.add("js/attCalendarSchEX.js");
        exts.add("dhtmlx/dhtmlxscheduler.js?v=5.3.6");
        exts.add("dhtmlx/ext/dhtmlxscheduler_outerdrag.js?v=5.3.6");
        exts.add("dhtmlx/ext/dhtmlxscheduler_limit.js?v=5.3.6");
        exts.add("js/attSch.js");
        exts.add("js/att.js");
        exts.add("js/deptIncludeLower.js");
        exts.add("js/areaIncludeLower.js");
        // 添加css
        exts.add("css/att.css");
        exts.add("css/attCalendarSchDX.css");
        exts.add("css/attCalendarSchEX.css");
        exts.add("dhtmlx/dhtmlxscheduler_material.css");

        // 考勤菜单控制js
        exts.add("js/attMenuControl.js");
        // 阿拉伯风格css
        if (PropertiesUtil.enableRTL()) {
            exts.add("css/attRTL.css");
        }
        return exts;
    }

}
