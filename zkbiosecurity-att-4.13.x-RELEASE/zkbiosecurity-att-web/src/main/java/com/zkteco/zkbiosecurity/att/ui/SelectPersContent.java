package com.zkteco.zkbiosecurity.att.ui;

/**
 * 选人控件--带部门
 *
 * @author: bob.liu
 * @date: 2020-05-25 18:05
 * @since 1.0.0
 **/

import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.net.URLEncoder;
import java.util.Enumeration;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.PropertiesUtil;
import com.zkteco.zkbiosecurity.ui.web.freemarker.annotation.ZKUIDirective;
import com.zkteco.zkbiosecurity.ui.web.freemarker.tags.DIV;
import com.zkteco.zkbiosecurity.ui.web.freemarker.tags.Document;
import com.zkteco.zkbiosecurity.ui.web.freemarker.tags.HTMLElement;
import com.zkteco.zkbiosecurity.ui.web.freemarker.util.BeanUtil;

import freemarker.core.Environment;
import freemarker.template.TemplateDirectiveBody;
import freemarker.template.TemplateDirectiveModel;
import freemarker.template.TemplateException;
import freemarker.template.TemplateModel;

/**
 * 选人控件--带部门
 *
 * @author: bob.liu
 * @date: 2020-05-25 14:01
 * @since 1.0.0
 **/
@Component
@ZKUIDirective(script = "js/ZKPersSelect.js")
public class SelectPersContent implements TemplateDirectiveModel {
    /**
     * 获取右槽选中项ids
     */
    @SuppressWarnings("rawtypes")
    @Override
    public void execute(Environment env, Map params, TemplateModel[] loopVars, TemplateDirectiveBody body)
        throws TemplateException, IOException {
        boolean enableRTL = PropertiesUtil.enableRTL();

        JSONObject options = new JSONObject();
        addRequestParams(options);
        BeanUtil.setParamsToJson(options, params);
        String gridName = options.getString("gridName");
        if (StringUtils.isEmpty(gridName)) {
            gridName = "gridbox" + BeanUtil.getUUID();
        }
        options.put("gridName", gridName);

        DIV doc = new DIV();
        doc.addAttributesOnly(params).id(gridName).addClass("select_layout_box");
        HTMLElement topBox = doc.div().id(gridName + "Top").addClass("select_top_box");
        HTMLElement gridBox = doc.div().id(gridName + "Grid").addClass("select_grid_box");

        // 左侧部门树
        HTMLElement leftTree = doc.div().id(gridName + "Left").addClass("dhx_left_tree_box");
        leftTree.div().addClass("dhx_tree_box").div().id("leftTree" + gridName);

        // 中间人员grid
        HTMLElement centerBox = doc.div().id(gridName + "Center");
        centerBox.div().addClass("select_content_title");
        HTMLElement pagerDive = centerBox.div().addClass("select_content_grid").div().id("center" + gridName);
        // 分页
        pagerDive.parent().div().id("center" + gridName + "_paging");
        // 右侧人员grid
        HTMLElement rightBox = doc.div().id(gridName + "Right");
        rightBox.div().addClass("select_content_title");
        rightBox.div().addClass("select_content_grid").div().id("right" + gridName);

        JSONObject optionsTemp = (JSONObject)env.getCurrentNamespace().toMap().get("options");
        env.getCurrentNamespace().put("options", options);
        if (body != null) {
            options.put("gridName", "center" + gridName);
            options.put("onQuery", gridName);
            StringWriter bodystr = new StringWriter();
            body.render(bodystr);
            options.put("gridName", gridName);
            options.put("content", URLEncoder.encode(bodystr.toString().replaceAll("[\r\n\t]", "").trim(), "utf-8"));
        } else {
            options.put("content", "");
        }
        env.getCurrentNamespace().put("options", optionsTemp);
        if (StringUtils.isEmpty(options.getString("noButtonContent"))
            || !options.getString("noButtonContent").equals("true")) {
            JSONObject buttonContent = options.getJSONObject("buttonContent");
            HTMLElement bottomBox = doc.div().id(gridName + "Bottom").addClass("select_bottom_btn");
            if (buttonContent == null) {
                buttonContent = options.getJSONObject("addButtonContent");
                if (buttonContent != null) {
                    bottomBox.appendContent(buttonContent.getString("body"));
                    buttonContent.remove("body");
                }
                HTMLElement bottomTemp = Document.createElement();
                bottomTemp.button().addClass("button-form")
                    .text(I18nUtil.i18nCode(BeanUtil.getParam(params, "okText", "common_edit_ok"))).id(gridName + "ok");
                bottomTemp.button().addClass("button-form")
                    .text(I18nUtil.i18nCode(BeanUtil.getParam(params, "cancelText", "common_edit_cancel")))
                    .id(gridName + "cancel");
                bottomBox.appendContent(bottomTemp.toString());
            } else {
                bottomBox.appendContent(buttonContent.getString("body"));
                buttonContent.remove("body");
            }
        } else {
            gridBox.style("bottom:0px");
        }
        if (!StringUtils.isEmpty(options.getString("topHeight"))) {
            topBox.style("height:" + options.getString("topHeight") + "px");
            gridBox.style(gridBox.attr("style") + ";top:" + options.getString("topHeight") + "px");
        }
        String vo = BeanUtil.getParam(params, "vo", "");
        JSONObject gridData = GridUtil.getGridHeader(vo, gridName);
        options.putAll(gridData);
        if (StringUtils.isNotBlank(options.getString("rightVo"))) {
            options.put("rightColumns", GridUtil.getGridHeader(options.getString("rightVo"), "right" + gridName));
        }
        if (StringUtils.isBlank(options.getString("setMode"))) {
            options.put("setMode", "true,true,false");
        }
        if (enableRTL) {
            options.put("enableRTL", true);
        }
        doc.script().appendContent("new ZKUI.PersSelect(" + options.toJSONString() + ");");
        Writer out = env.getOut();
        out.write(doc.toString());
    }

    private void addRequestParams(JSONObject opts) {
        ServletRequestAttributes servletRequestAttributes =
            (ServletRequestAttributes)RequestContextHolder.currentRequestAttributes();
        HttpServletRequest request = servletRequestAttributes.getRequest();
        Enumeration<String> names = request.getParameterNames();
        while (names.hasMoreElements()) {
            String name = names.nextElement();
            opts.put(name, request.getParameter(name));
        }
    }
}
