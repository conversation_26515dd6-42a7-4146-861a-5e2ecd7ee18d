package com.zkteco.zkbiosecurity.att.controller;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.remote.AttAreaPersonRemote;
import com.zkteco.zkbiosecurity.att.service.AttAreaPersonService;
import com.zkteco.zkbiosecurity.att.util.AttExcelUtil;
import com.zkteco.zkbiosecurity.att.util.AttImportWithRowNumUtil;
import com.zkteco.zkbiosecurity.att.vo.AttAreaPersonItem;
import com.zkteco.zkbiosecurity.att.vo.AttAuthAreaItem;
import com.zkteco.zkbiosecurity.att.vo.AttPersonSelectItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.LocaleMessageSourceUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 区域人员
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 15:44
 * @since 1.0.0
 */
@Controller
public class AttAreaPersonController extends ExportController implements AttAreaPersonRemote {
    @Autowired
    private AttAreaPersonService attAreaPersonService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private AttExcelUtil attExcelUtil;

    @Override
    public DxGrid authAreaList(AttAuthAreaItem authAreaItem) {
        SecuritySubject authUser = getCurrentSubject();
        if (!authUser.getIsSuperuser()) {
            if (authUser.getAreaIds() != null && authUser.getDepartmentIds().size() > 0) {
                authAreaItem
                    .setAuthAreaIdIn(authUser.getAreaIds().toString().replaceAll("[\\[\\]]", "").replaceAll(" ", ""));
            }
        }
        Pager pager = attAreaPersonService.authAreaList(authAreaItem, getPageNo(), getPageSize());
        return GridUtil.convert(pager, authAreaItem.getClass());
    }

    @RequiresPermissions("att:areaPerson")
    @Override
    public ModelAndView index() {
        return new ModelAndView("att/areaPerson/attAreaPerson");
    }

    @RequiresPermissions("att:areaPerson:refresh")
    @Override
    public DxGrid list(AttAreaPersonItem condition) {
        Pager pager = attAreaPersonService.loadPagerByAuthUserFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:areaPerson:del")
    @LogRequest(module = "att_module", object = "att_op_forZoneAddPers", opType = "common_op_del",
        requestParams = {"names"}, opContent = "common_name")
    @Override
    public ZKResultMsg delete(String ids) {
        attAreaPersonService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("att:areaPerson:setUserSms")
    @Override
    public ModelAndView setUserSms(String ids) {
        request.setAttribute("ids", ids);
        request.setAttribute("editPage", "true");
        return new ModelAndView("att/areaPerson/setUserSms");
    }

    @Override
    public ZKResultMsg addSms(String ids) {
        String startTime = request.getParameter("startTime");
        String msg = request.getParameter("msg");
        String min = request.getParameter("min");
        ZKResultMsg zKResultMsg = attAreaPersonService.addSms(ids, startTime, msg, min);
        return I18nUtil.i18nMsg(zKResultMsg);
    }

    @RequiresPermissions("att:areaPerson:syncPerToDev")
    @Override
    public ZKResultMsg syncPerToDev(String areaId, String ids) {
        ZKResultMsg res = attAreaPersonService.syncPerToDev(areaId, ids);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("att:areaPerson:addPerson")
    @LogRequest(module = "att_module", object = "att_op_forZoneAddPers", opType = "common_op_new",
        requestParams = {"personIds"}, opContent = "att_areaPerson_personId")
    @Override
    public ZKResultMsg addPerson() {
        String personIds = request.getParameter("personIds");
        String areaId = request.getParameter("areaId");

        ZKResultMsg res = attAreaPersonService.addPerson(personIds, areaId, "-1");
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("att:areaPerson:export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AttAreaPersonItem attAreaPersonItem = new AttAreaPersonItem();
        setConditionValue(attAreaPersonItem);
        List<AttAreaPersonItem> itemList = attAreaPersonService.loadListByAuthFilter(attAreaPersonItem, getBeginIndex(),
            getEndIndex(), request.getSession().getId());
        excelExport(itemList, AttAreaPersonItem.class);
    }

    @Override
    public void importTemplate(HttpServletRequest request, HttpServletResponse response) {
        /*jsonColumn设置要显示的字段*/
        JSONObject jsonColumn = new JSONObject(true);
        jsonColumn.put("pin", "");
        jsonColumn.put("name", "");
        if (!"zh_CN".equals(LocaleMessageSourceUtil.language)) {
            jsonColumn.put("lastName", "");
        }
        jsonColumn.put("deptCode", "");
        jsonColumn.put("deptName", "");
        jsonColumn.put("authAreaNo", "");
        jsonColumn.put("authAreaName", "");
        Map<String, Map<String, String>> jsonCloumnMap = new HashMap<>();
        Map<String, String> jsonMap = new HashMap<>();
        jsonMap.put("jsonColumn", jsonColumn.toJSONString());
        jsonCloumnMap.put("jsonColumn", jsonMap);
        attExcelUtil.attExcelExport(request, response, new ArrayList<>(), AttAreaPersonItem.class, jsonCloumnMap);
    }

    @Override
    public ZKResultMsg importExcel(MultipartFile upload) throws IOException {
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_uploadFileSuccess") + "<br/>"));
            List<AttAreaPersonItem> itemList =
                AttImportWithRowNumUtil.excelImport(upload.getInputStream(), AttAreaPersonItem.class);
            progress += 10;
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_resolutionComplete") + "<br/>"));
            return I18nUtil.i18nMsg(attAreaPersonService.importAreaPerson(itemList));
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                    + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                    new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("Import Person Info Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
    }

    @Override
    public ZKResultMsg importBatchDel(MultipartFile upload) throws IOException {
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_uploadFileSuccess") + "<br/>"));
            List<AttAreaPersonItem> itemList =
                AttImportWithRowNumUtil.excelImport(upload.getInputStream(), AttAreaPersonItem.class);
            progress += 10;
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_resolutionComplete") + "<br/>"));
            return I18nUtil.i18nMsg(attAreaPersonService.importBatchDel(itemList));
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                    + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                    new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("Import Person Info Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
    }
}