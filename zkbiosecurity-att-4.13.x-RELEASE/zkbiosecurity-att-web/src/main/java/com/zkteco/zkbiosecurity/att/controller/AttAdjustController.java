package com.zkteco.zkbiosecurity.att.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.remote.AttAdjustRemote;
import com.zkteco.zkbiosecurity.att.service.AttAdjustService;
import com.zkteco.zkbiosecurity.att.service.AttShiftService;
import com.zkteco.zkbiosecurity.att.util.AttExcelUtil;
import com.zkteco.zkbiosecurity.att.util.AttImportWithRowNumUtil;
import com.zkteco.zkbiosecurity.att.vo.AttAdjustItem;
import com.zkteco.zkbiosecurity.att.vo.AttShiftItem;
import com.zkteco.zkbiosecurity.base.annotation.LogChangeRequest;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.LocaleMessageSourceUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 调休补班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 16:06
 * @since 1.0.0
 */
@Controller
public class AttAdjustController extends ExportController implements AttAdjustRemote {

    @Autowired
    private AttAdjustService attAdjustService;
    @Autowired
    private AttShiftService attShiftService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private AttExcelUtil attExcelUtil;

    @RequiresPermissions("att:adjust")
    @Override
    public ModelAndView index() {
        if (getCurrentSubject().getStaff()) {
            return new ModelAndView("att/flowable/adjust/attAdjust");
        } else {
            return new ModelAndView("att/adjust/attAdjust");
        }
    }

    @RequiresPermissions("att:adjust:add")
    @Override
    public ModelAndView edit(String id) {

        AttAdjustItem item = attAdjustService.getItemById(id);
        request.setAttribute("item", item);
        List<AttShiftItem> shiftItemList = attShiftService.getByCondition(new AttShiftItem());
        // 默认选中第一个班次。
        if (shiftItemList != null && shiftItemList.size() > 0) {
            request.setAttribute("attShiftId", shiftItemList.get(0).getId());
        }
        return new ModelAndView("att/adjust/editAttAdjust");
    }

    @RequiresPermissions("att:adjust:add")
    @LogChangeRequest(module = "att_module", object = "att_leftMenu_adjust", opType = "common_op_new",
        vo = AttAdjustItem.class, service = AttAdjustService.class)
    @Override
    public ZKResultMsg save(AttAdjustItem item) {
        String personIds = request.getParameter("personIds");
        ZKResultMsg res = attAdjustService.saveItem(item, personIds, request.getSession().getId());
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("att:adjust:refresh")
    @Override
    public DxGrid list(AttAdjustItem condition) {
        Pager pager = attAdjustService.loadPagerByAuthUserFilter(request.getSession().getId(), condition, getPageNo(),
            getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:adjust:del")
    @LogRequest(module = "att_module", object = "att_leftMenu_adjust", opType = "common_op_del",
        requestParams = {"pins"}, opContent = "att_person_pin")
    @Override
    public ZKResultMsg delete(String ids) {
        attAdjustService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("att:adjust:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_adjust", opType = "common_op_export",
        requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AttAdjustItem attAdjustItem = new AttAdjustItem();
        setConditionValue(attAdjustItem);
        List<AttAdjustItem> intemList = attAdjustService.getItemData(request.getSession().getId(),
            AttAdjustItem.class, attAdjustItem, getBeginIndex(), getEndIndex());
        excelExport(intemList, AttAdjustItem.class);
    }

    @Override
    public void importTemplate(HttpServletRequest request, HttpServletResponse response) {
        JSONObject jsonColumn = new JSONObject(true);
        jsonColumn.put("personPin", "");
        jsonColumn.put("personName", "");
        if (!"zh_CN".equals(LocaleMessageSourceUtil.language)) {
            jsonColumn.put("personLastName", "");
        }
        jsonColumn.put("adjustDate", "");
        jsonColumn.put("remark", "");
        Map<String, Map<String, String>> jsonCloumnMap = new HashMap<>();
        Map<String, String> jsonMap = new HashMap<>();
        jsonMap.put("jsonColumn", jsonColumn.toJSONString());
        jsonCloumnMap.put("jsonColumn", jsonMap);
        attExcelUtil.attExcelExport(request, response, new ArrayList<>(), AttAdjustItem.class, jsonCloumnMap);
    }

    @Override
    public ZKResultMsg importExcel(MultipartFile upload) throws IOException {
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_uploadFileSuccess") + "<br/>"));
            List<AttAdjustItem> itemList =
                AttImportWithRowNumUtil.excelImport(upload.getInputStream(), AttAdjustItem.class);
            progress += 10;
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_resolutionComplete") + "<br/>"));
            return I18nUtil.i18nMsg(attAdjustService.importExcel(itemList, request.getSession().getId()));
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                    + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                    new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("Import AttAdjust Info Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
    }

    @RequiresPermissions("att:adjust:approval")
    @LogRequest(module = "att_module", object = "att_leftMenu_adjust", opType = "att_apply_pass",
        requestParams = {"pins"}, opContent = "att_person_pin")
    @Override
    public ZKResultMsg approval(String ids) {
        attAdjustService.approval(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @RequiresPermissions("att:adjust:approval")
    @LogRequest(module = "att_module", object = "att_leftMenu_adjust", opType = "att_exception_refuse",
        requestParams = {"pins"}, opContent = "att_person_pin")
    @Override
    public ZKResultMsg refuse(String ids) {
        attAdjustService.refuse(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }
}