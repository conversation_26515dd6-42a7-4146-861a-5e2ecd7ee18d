package com.zkteco.zkbiosecurity.att.api.controller;

import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.service.AttAnnualLeaveReportService;
import com.zkteco.zkbiosecurity.base.annotation.ApiPermissions;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;

/**
 * 年假结余
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 14:05 2020/11/17
 * @version v1.0
 */
@RestController
@RequestMapping(value = {"/api/attLaveHoliday"})
public class AttApiLaveHolidayController {

    @Autowired
    private AttAnnualLeaveReportService attAnnualLeaveReportService;

    /**
     * 考勤年假结余
     *
     * @param zkMessage:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/11/17 14:07
     * @since 1.0.0
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping(path = "/getLeaveLaveHoliday", method = RequestMethod.POST, produces = "application/json")
    public ZKResultMsg getLeaveLaveHoliday(@RequestBody ZKMessage zkMessage) {
        Map<String, Object> content = zkMessage.getContent();
        String personPin = MapUtils.getString(content, "personPin");
        ZKResultMsg resultMsg = attAnnualLeaveReportService.getDetailByPersonPin(personPin);
        return I18nUtil.i18nMsg(resultMsg);
    }

    /**
     * 获取加班类型
     *
     * @param zkMessage:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/11/17 14:07
     * @since 1.0.0
     */
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    @RequestMapping(path = "/getOvertimeTypeList", method = RequestMethod.POST, produces = "application/json")
    public ZKResultMsg getOvertimeTypeList(@RequestBody ZKMessage zkMessage) {
        JSONArray overtimeTypeArray = new JSONArray();
        // 平时加班
        JSONObject normal = new JSONObject();
        normal.put("code", "0");
        normal.put("value", I18nUtil.i18nCode("att_overtime_normal"));
        overtimeTypeArray.add(normal);
        // 休息日加班
        JSONObject rest = new JSONObject();
        rest.put("code", "1");
        rest.put("value", I18nUtil.i18nCode("att_overtime_rest"));
        overtimeTypeArray.add(rest);
        // 节假日加班
        JSONObject holiday = new JSONObject();
        holiday.put("code", "2");
        holiday.put("value", I18nUtil.i18nCode("att_shift_holidayOt"));
        overtimeTypeArray.add(holiday);
        return I18nUtil.i18nMsg(new ZKResultMsg(overtimeTypeArray));
    }
}
