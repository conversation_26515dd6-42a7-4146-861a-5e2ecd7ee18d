package com.zkteco.zkbiosecurity.att.dhx;

import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.base.bean.GridColumnItem;
import com.zkteco.zkbiosecurity.core.utils.ApplicationPropertiesUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.DynamicColumn;
import com.zkteco.zkbiosecurity.core.utils.WebContextUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 标准工作时间报表动态表头
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 10:00 2020/9/10
 */
@Component
public class AttWorkTimeDetailDyna implements DynamicColumn {

    @Autowired
    private AttParamService attParamService;

    @Override
    public List<GridColumnItem> getColumn(String filedName) {
        HttpServletRequest request = WebContextUtil.getCurrentRequest();
        String startDate = request.getParameter("startDatetimeBegin");
        String endDate = request.getParameter("startDatetimeEnd");

        // 导出处理
        String queryCondition = request.getParameter("queryConditions");
        if (queryCondition != null) {
            String[] conditionAry = queryCondition.replaceAll("%20", " ").split("&");
            for (String condition : conditionAry) {
                if (StringUtils.isBlank(condition)) {
                    continue;
                }
                String[] strAry = condition.split("=", 2);
                String fieldName = strAry[0];
                String fieldValue = strAry[1];
                if ("startDatetimeBegin".equals(fieldName)) {
                    startDate = fieldValue;
                } else if ("startDatetimeEnd".equals(fieldName)) {
                    endDate = fieldValue;
                }
            }
        }

        List<String> dayList = AttDateUtils.getBetweenDate(startDate, AttDateUtils.getEndDate(startDate, endDate));
        List<GridColumnItem> columns = new ArrayList<GridColumnItem>();
        for (int i = 1; i <= dayList.size(); i++) {
            GridColumnItem c1 = new GridColumnItem();
            c1.setName(dayList.get(i - 1));
            c1.setColumnType("ro");
            c1.setSort("na");
            c1.setLabel("#cspan");
            c1.setWidth("110");
            c1.setSortNo(i + 10);
            c1.setSecHeader(AttDateUtils.getWeekName(DateUtil.stringToDate(dayList.get(i - 1))));
            String labelDate = attParamService.dateToLocaleString(DateUtil.stringToDate(dayList.get(i - 1)));
            c1.setLabel(labelDate);
            c1.setFieldName(filedName);
            columns.add(c1);
        }
        return columns;
    }

}
