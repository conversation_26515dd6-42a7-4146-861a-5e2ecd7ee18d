package com.zkteco.zkbiosecurity.att.controller;

import com.zkteco.zkbiosecurity.att.remote.AttMonthStatisticalReportRemote;
import com.zkteco.zkbiosecurity.att.service.AttMonthStatisticalReportService;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.vo.AttDayDetailReportItem;
import com.zkteco.zkbiosecurity.att.vo.AttMonthStatisticalReportItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

/**
 * 人员汇总表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:25
 * @since 1.0.0
 */
@Controller
public class AttMonthStatisticalReportController extends ExportController implements AttMonthStatisticalReportRemote {

    @Autowired
    private AttMonthStatisticalReportService attMonthStatisticalReportService;
    @Autowired
    private AttParamService attParamService;

    @RequiresPermissions("att:monthStatisticalReport")
    @Override
    public ModelAndView index() {
        attMonthStatisticalReportService.modifyItemLabel();

        boolean overtimeLevelEnable = attParamService.overtimeLevelEnable();
        if (!overtimeLevelEnable) {
            request.setAttribute("showColumns", "!overTimeOT1,overTimeOT2,overTimeOT3");
        }

        return new ModelAndView("att/report/attMonthStatisticalReport");
    }

    @RequiresPermissions("att:monthStatisticalReport:refresh")
    @Override
    public DxGrid list(AttMonthStatisticalReportItem condition) {
        Pager pager = attMonthStatisticalReportService.loadPagerByAuthUserFilter(request.getSession().getId(),
            condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @SuppressWarnings("unchecked")
    @RequiresPermissions("att:monthStatisticalReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_monthStatisticalReport", opType = "common_op_export",
        requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void export() {
        AttMonthStatisticalReportItem attMonthStatisticalReportItem = new AttMonthStatisticalReportItem();
        setConditionValue(attMonthStatisticalReportItem);
        List<AttMonthStatisticalReportItem> itemList =
            attMonthStatisticalReportService.getMonthStatisticalReportItemData(request.getSession().getId(),
                attMonthStatisticalReportItem, getBeginIndex(), getEndIndex());
        excelExport(itemList, AttMonthStatisticalReportItem.class);
    }

    @RequiresPermissions("att:overtimeSummaryReport")
    @Override
    public ModelAndView indexOvertimeSummaryReport() {
        attMonthStatisticalReportService.modifyItemLabel();

        boolean overtimeLevelEnable = attParamService.overtimeLevelEnable();
        if (!overtimeLevelEnable) {
            request.setAttribute("showColumns", "overTimeOT1,overTimeOT2,overTimeOT3");
        }

        return new ModelAndView("att/report/attOvertimeSummaryReport");
    }

    @RequiresPermissions("att:overtimeSummaryReport:refresh")
    @Override
    public DxGrid listOvertimeSummaryReport(AttMonthStatisticalReportItem condition) {
        Pager pager = attMonthStatisticalReportService.loadPagerByAuthUserFilter(request.getSession().getId(),
            condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @SuppressWarnings("unchecked")
    @RequiresPermissions("att:overtimeSummaryReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_overtimeSummaryReport", opType = "common_op_export",
        requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void exportOvertimeSummaryReport() {
        AttMonthStatisticalReportItem attMonthStatisticalReportItem = new AttMonthStatisticalReportItem();
        setConditionValue(attMonthStatisticalReportItem);
        List<AttMonthStatisticalReportItem> itemList =
            attMonthStatisticalReportService.getMonthStatisticalReportItemData(request.getSession().getId(),
                attMonthStatisticalReportItem, getBeginIndex(), getEndIndex());
        excelExport(itemList, AttMonthStatisticalReportItem.class);
    }

    @RequiresPermissions("att:leaveSummaryReport")
    @Override
    public ModelAndView indexLeaveSummaryReport() {
        attMonthStatisticalReportService.modifyItemLabel();
        return new ModelAndView("att/report/attLeaveSummaryReport");
    }

    @RequiresPermissions("att:leaveSummaryReport:refresh")
    @Override
    public DxGrid listLeaveSummaryReport(AttMonthStatisticalReportItem condition) {
        Pager pager = attMonthStatisticalReportService.loadPagerByAuthUserFilter(request.getSession().getId(),
            condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @SuppressWarnings("unchecked")
    @RequiresPermissions("att:leaveSummaryReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_leaveSummaryReport", opType = "common_op_export",
        requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void exportLeaveSummaryReport() {
        AttMonthStatisticalReportItem attMonthStatisticalReportItem = new AttMonthStatisticalReportItem();
        setConditionValue(attMonthStatisticalReportItem);
        List<AttMonthStatisticalReportItem> itemList =
            attMonthStatisticalReportService.getMonthStatisticalReportItemData(request.getSession().getId(),
                attMonthStatisticalReportItem, getBeginIndex(), getEndIndex());
        excelExport(itemList, AttMonthStatisticalReportItem.class);
    }
}
