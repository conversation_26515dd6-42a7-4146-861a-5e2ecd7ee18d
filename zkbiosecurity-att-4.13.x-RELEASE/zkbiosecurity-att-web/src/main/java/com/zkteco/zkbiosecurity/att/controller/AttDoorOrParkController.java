package com.zkteco.zkbiosecurity.att.controller;

import java.util.ArrayList;
import java.util.List;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.format.TreeBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.remote.AttDoorOrParkRemote;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;

/**
 * 考勤点添加门或停车设备
 *
 * @author: verber
 * @date: 2018-07-02 下午02:15
 */
@Controller
public class AttDoorOrParkController extends BaseController implements AttDoorOrParkRemote {

    @Autowired
    private AttParkDeviceService attParkDeviceService;
    @Autowired
    private AttInsDeviceService attInsDeviceService;
    @Autowired
    private AttPidDeviceService attPidDeviceService;
    @Autowired
    private AttAccDeviceService attAccDeviceService;
    @Autowired
    private AttVmsDeviceService attVmsDeviceService;
    @Autowired
    private AttIvsDeviceService attIvsDeviceService;
    @Autowired
    private AttPsgDeviceService attPsgDeviceService;

    @Override
    public DxGrid selectDoorList(AttSelectDoorItem condition) {
        Pager pager = null;
        if (AttConstant.NOSELECT.equals(condition.getType())) {
            if (StringUtils.isBlank(condition.getSelectId())) {
                condition.setSelectId(AttConstant.COMM_DEF_VALUE);
            }
            pager = attAccDeviceService.getSelectAccDevicePager(condition, getPageNo(), getPageSize());
        } else {
            pager = new Pager(getPageNo(), getPageSize());
            pager.setData(new ArrayList<>());
        }
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public DxGrid selectParkDeviceList(AttParkDeviceSelectItem codition) {
        Pager pager = null;
        if (AttConstant.NOSELECT.equals(codition.getType())) {
            if (StringUtils.isBlank(codition.getSelectId())) {
                codition.setSelectId(AttConstant.COMM_DEF_VALUE);
            }
            pager = attParkDeviceService.getSelectParkDevicePager(codition, getPageNo(), getPageSize());
        } else {
            pager = new Pager(getPageNo(), getPageSize());
            pager.setData(new ArrayList<>());
        }

        return GridUtil.convert(pager, codition.getClass());
    }

    @Override
    public DxGrid selectInsDeviceList(AttInsDeviceSelectItem condition) {
        Pager pager = null;
        if (AttConstant.NOSELECT.equals(condition.getType())) {
            if (StringUtils.isBlank(condition.getSelectId())) {
                condition.setSelectId(AttConstant.COMM_DEF_VALUE);
            }
            pager = attInsDeviceService.getSelectInsDevicePager(condition, getPageNo(), getPageSize());
        } else {
            pager = new Pager(getPageNo(), getPageSize());
            pager.setData(new ArrayList<>());
        }
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public DxGrid selectPidDeviceList(AttPidDeviceSelectItem condition) {
        Pager pager = null;
        if (AttConstant.NOSELECT.equals(condition.getType())) {
            if (StringUtils.isBlank(condition.getSelectId())) {
                condition.setSelectId(AttConstant.COMM_DEF_VALUE);
            }
            pager = attPidDeviceService.getSelectPidDevicePager(condition, getPageNo(), getPageSize());
        } else {
            pager = new Pager(getPageNo(), getPageSize());
            pager.setData(new ArrayList<>());
        }
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public DxGrid selectVmsDeviceList(AttVmsDeviceSelectItem condition) {
        Pager pager = null;
        if (AttConstant.NOSELECT.equals(condition.getType())) {
            if (StringUtils.isBlank(condition.getSelectId())) {
                condition.setSelectId(AttConstant.COMM_DEF_VALUE);
            }
            pager = attVmsDeviceService.getSelectVmsDevicePager(condition, getPageNo(), getPageSize());
        } else {
            pager = new Pager(getPageNo(), getPageSize());
            pager.setData(new ArrayList<>());
        }
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public DxGrid selectPsgGateList(AttPsgDeviceSelectItem condition) {
        Pager pager = null;
        if (AttConstant.NOSELECT.equals(condition.getType())) {
            if (StringUtils.isBlank(condition.getSelectId())) {
                condition.setSelectId(AttConstant.COMM_DEF_VALUE);
            }
            pager = attPsgDeviceService.getSelectPsgDevicePager(condition, getPageNo(), getPageSize());
        } else {
            pager = new Pager(getPageNo(), getPageSize());
            pager.setData(new ArrayList<>());
        }
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public DxGrid selectIvsDeviceList(AttIvsDeviceSelectItem condition) {
        Pager pager = null;
        if (AttConstant.NOSELECT.equals(condition.getType())) {
            if (StringUtils.isBlank(condition.getSelectId())) {
                condition.setSelectId(AttConstant.COMM_DEF_VALUE);
            }
            pager = attIvsDeviceService.getSelectIvsDevicePager(condition, getPageNo(), getPageSize());
        } else {
            pager = new Pager(getPageNo(), getPageSize());
            pager.setData(new ArrayList<>());
        }
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public TreeItem ivsParentDeviceTree() {
        List<AttIvsParentDeviceItem> attIvsParentDeviceItems =
            attIvsDeviceService.getDeviceTree(request.getSession().getId());
        List<TreeItem> items = Lists.newArrayList();
        TreeItem item = null;
        TreeItem pItem = null;
        for (AttIvsParentDeviceItem ivsParentDeviceItem : attIvsParentDeviceItems) {
            item = new TreeItem();
            item.setId(ivsParentDeviceItem.getId());
            item.setText(ivsParentDeviceItem.getName());
            pItem = new TreeItem("0");
            item.setParent(pItem);
            items.add(item);
        }
        List<TreeItem> treeItems = TreeBuilder.newTreeBuilder(TreeItem.class, String.class).buildToTreeList(items);
        return new TreeItem("0", treeItems);
    }
}