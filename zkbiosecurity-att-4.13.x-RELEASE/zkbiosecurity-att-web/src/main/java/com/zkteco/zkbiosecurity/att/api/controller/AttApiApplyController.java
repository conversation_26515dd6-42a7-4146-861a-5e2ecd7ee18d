package com.zkteco.zkbiosecurity.att.api.controller;

import com.zkteco.zkbiosecurity.att.api.vo.*;
import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.zkteco.zkbiosecurity.att.service.AttApiService;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 考勤异常申请
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/11/19 16:10
 * @since 1.0.0
 */
@Controller
@RequestMapping(value = {"/api/attApply"})
@Slf4j
@Api(tags = "AttApply", description = "att apply")
public class AttApiApplyController {

    @Autowired
    private AttApiService attApiService;

    /**
     * 申请时长
     *
     * @param attApiApplyTimeLongItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/15 17:02
     * @since 1.0.0
     */
    @ResponseBody
    @RequestMapping(value = "/getApplyTimeLong", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Apply Time", notes = "Apply Time", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"personPin", "leaveTypeNo", "startDatetime", "endDatetime"})
    public ApiResultMessage getApplyTimeLong(@RequestBody AttApiApplyTimeLongItem attApiApplyTimeLongItem) {
        if (StringUtils.isBlank(attApiApplyTimeLongItem.getPersonPin())) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_notNull", "personPin"));
        }
        if (StringUtils.isBlank(attApiApplyTimeLongItem.getLeaveTypeNo())) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_notNull", "leaveTypeNo"));
        }
        if (StringUtils.isBlank(attApiApplyTimeLongItem.getStartDatetime())) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_notNull", "startDatetime"));
        }
        if (StringUtils.isBlank(attApiApplyTimeLongItem.getEndDatetime())) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_notNull", "endDatetime"));
        }
        if (attApiApplyTimeLongItem.getStartDatetime().compareTo(attApiApplyTimeLongItem.getEndDatetime()) >= 0) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_startDateGeEndDate"));
        }
        return attApiService.getApplyTimeLong(attApiApplyTimeLongItem);
    }

    /**
     * 请假类型
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/15 17:02
     * @since 1.0.0
     */
    @ApiOperation(value = "Get LeaveType List", notes = "Get LeaveType List", response = ApiResultMessage.class)
    @RequestMapping(value = "/getLeaveTypeList", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest
    public ApiResultMessage getLeaveTypeList() {
        return attApiService.getLeaveTypeList();
    }

    /**
     * 申请补签
     *
     * @param attApiApplySignItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/15 17:02
     * @since 1.0.0
     */
    @ResponseBody
    @RequestMapping(value = "/applySign", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Apply Sign", notes = "Apply Sign", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"personPin", "signDatetime"})
    public ApiResultMessage applySign(@RequestBody AttApiApplySignItem attApiApplySignItem) {
        if (StringUtils.isBlank(attApiApplySignItem.getPersonPin())) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_notNull", "personPin"));
        }
        if (StringUtils.isBlank(attApiApplySignItem.getSignDatetime())) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_notNull", "signDatetime"));
        }
        return attApiService.applySign(attApiApplySignItem);
    }

    /**
     * 申请加班
     *
     * @param attApiApplyOvertimeItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/15 17:02
     * @since 1.0.0
     */
    @ResponseBody
    @RequestMapping(value = "/applyOvertime", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Apply Overtime", notes = "Apply Overtime", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"personPin", "startDatetime", "endDatetime"})
    public ApiResultMessage applyOvertime(@RequestBody AttApiApplyOvertimeItem attApiApplyOvertimeItem) {
        if (StringUtils.isBlank(attApiApplyOvertimeItem.getPersonPin())) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_notNull", "personPin"));
        }
        if (StringUtils.isBlank(attApiApplyOvertimeItem.getStartDatetime())) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_notNull", "startDatetime"));
        }
        if (StringUtils.isBlank(attApiApplyOvertimeItem.getEndDatetime())) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_notNull", "endDatetime"));
        }
        if (attApiApplyOvertimeItem.getStartDatetime().compareTo(attApiApplyOvertimeItem.getEndDatetime()) >= 0) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_startDateGeEndDate"));
        }
        return attApiService.applyOvertime(attApiApplyOvertimeItem);
    }

    /**
     * 申请请假
     *
     * @param attApiApplyLeaveItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/15 17:02
     * @since 1.0.0
     */
    @ResponseBody
    @RequestMapping(value = "/applyLeave", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Apply Leave", notes = "Apply Leave", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"personPin", "leaveTypeNo", "startDatetime", "endDatetime", "leaveImagePath"})
    public ApiResultMessage applyLeave(@RequestBody AttApiApplyLeaveItem attApiApplyLeaveItem) {
        if (StringUtils.isBlank(attApiApplyLeaveItem.getPersonPin())) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_notNull", "personPin"));
        }
        if (StringUtils.isBlank(attApiApplyLeaveItem.getLeaveTypeNo())) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_notNull", "leaveTypeNo"));
        }
        if (StringUtils.isBlank(attApiApplyLeaveItem.getStartDatetime())) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_notNull", "startDatetime"));
        }
        if (StringUtils.isBlank(attApiApplyLeaveItem.getEndDatetime())) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_notNull", "endDatetime"));
        }
        if (attApiApplyLeaveItem.getStartDatetime().compareTo(attApiApplyLeaveItem.getEndDatetime()) >= 0) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_startDateGeEndDate"));
        }
        String leaveImagePath = attApiApplyLeaveItem.getLeaveImagePath();
        if (StringUtils.isNotBlank(leaveImagePath)) {
            if (leaveImagePath.split(",").length > 4) {
                return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_leave_maxFileCount"));
            }
            if (leaveImagePath.length() > 2000) {
                return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_imageLengthNot2000"));
            }
        }
        return attApiService.applyLeave(attApiApplyLeaveItem);
    }

    /**
     * 上传请假图片
     *
     * @param attApiApplyLeaveImageItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/17 14:12
     * @since 1.0.0
     */
    @ApiOperation(value = "Upload Leave Image", notes = "Upload Leave Image", response = ApiResultMessage.class)
    @RequestMapping(value = "/uploadLeaveImage", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"base64Image"})
    public ApiResultMessage uploadLeaveImage(@RequestBody AttApiApplyLeaveImageItem attApiApplyLeaveImageItem) {
        if (StringUtils.isBlank(attApiApplyLeaveImageItem.getBase64Image())) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_notNull", "base64Image"));
        }
        return attApiService.uploadLeaveImage(attApiApplyLeaveImageItem);
    }
}
