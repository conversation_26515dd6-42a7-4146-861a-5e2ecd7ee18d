package com.zkteco.zkbiosecurity.att.controller;

import com.zkteco.zkbiosecurity.att.remote.AttRecordRemote;
import com.zkteco.zkbiosecurity.att.service.AttRecordService;
import com.zkteco.zkbiosecurity.att.vo.AttRecordItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

/**
 * 考勤计算结果、日明细表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:57
 * @since 1.0.0
 */
@Controller
public class AttRecordController extends BaseController implements AttRecordRemote {
    @Autowired
    private AttRecordService attRecordService;

    @RequiresPermissions("att:record:view")
    @Override
    public ModelAndView index() {
        return new ModelAndView("att/record/attRecord");
    }

    @RequiresPermissions("att:record:edit")
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("item", attRecordService.getItemById(id));
        }
        return new ModelAndView("att/record/editAttRecord");
    }

    @RequiresPermissions("att:record:edit")
    @Override
    public ZKResultMsg save(AttRecordItem item) {
        ZKResultMsg res = new ZKResultMsg();
        attRecordService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @Override
    public DxGrid list(AttRecordItem condition) {
        Pager pager = attRecordService.getItemsByPage(condition, getPageNo(), getPageSize(), getLimitCount());
        return GridUtil.convert(pager, condition.getClass(), getPageList());
    }

    @Override
    public ZKResultMsg delete(String ids) {
        attRecordService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }
}