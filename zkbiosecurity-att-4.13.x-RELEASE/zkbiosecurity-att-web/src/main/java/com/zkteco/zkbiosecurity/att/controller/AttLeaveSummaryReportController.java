package com.zkteco.zkbiosecurity.att.controller;

import com.zkteco.zkbiosecurity.att.remote.AttLeaveSummaryReportRemote;
import com.zkteco.zkbiosecurity.att.service.AttLeaveSummaryReportService;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveSummaryReportItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
 * 请假汇总表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/8/18 11:42
 */
@Controller
public class AttLeaveSummaryReportController extends ExportController implements AttLeaveSummaryReportRemote {

    @Autowired
    private AttLeaveSummaryReportService attLeaveSummaryReportService;

    @RequiresPermissions("att:leaveSummaryReport")
    @Override
    public ModelAndView index() {
        return new ModelAndView("att/report/attLeaveSummaryReportBak");
    }

    @RequiresPermissions("att:leaveSummaryReport:refresh")
    @Override
    public DxGrid list(AttLeaveSummaryReportItem condition) {
        Pager pager = attLeaveSummaryReportService.loadPagerByAuthUserFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:leaveSummaryReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_leaveSummaryReport", opType = "common_op_export",
        requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AttLeaveSummaryReportItem attLeaveSummaryReportItem = new AttLeaveSummaryReportItem();
        setConditionValue(attLeaveSummaryReportItem);
        List<AttLeaveSummaryReportItem> intemList = attLeaveSummaryReportService
            .getItemData(request.getSession().getId(), attLeaveSummaryReportItem, getBeginIndex(), getEndIndex());
        excelExport(intemList, AttLeaveSummaryReportItem.class);
    }

    @Override
    public ZKResultMsg getLeaveDetail(String personPin, String leaveTypeId, Date startTime, Date endTime) {
        return attLeaveSummaryReportService.getLeaveDetail(personPin, leaveTypeId, startTime, endTime);
    }
}
