package com.zkteco.zkbiosecurity.att.controller;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.vo.AttEsdcChannelSelectItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.remote.AttPointRemote;
import com.zkteco.zkbiosecurity.att.service.AttPointService;
import com.zkteco.zkbiosecurity.att.vo.AttPointItem;
import com.zkteco.zkbiosecurity.att.vo.AttPointSelectItem;
import com.zkteco.zkbiosecurity.base.annotation.LogChangeRequest;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.format.TreeBuilder;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 考勤点
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:50
 * @since 1.0.0
 */
@Controller
public class AttPointController extends ExportController implements AttPointRemote {
    @Autowired
    private AttPointService attPointService;

    @RequiresPermissions("att:point")
    @Override
    public ModelAndView index() {
        return new ModelAndView("att/point/attPoint");
    }

    @RequiresPermissions("att:point:edit")
    @Override
    public ModelAndView edit(String id) {
        request.setAttribute("accModule", attPointService.isExistAcc());
        request.setAttribute("parkModule", attPointService.isExistPark());
        request.setAttribute("insModule", attPointService.isExistIns());
        request.setAttribute("pidModule", attPointService.isExistPid());
        request.setAttribute("vmsModule", attPointService.isExistVms());
        request.setAttribute("psgModule", attPointService.isExistPsg());
        request.setAttribute("ivsModule", attPointService.isExistIvs());
        request.setAttribute("esdcModule", attPointService.isExistEsdc());
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("item", attPointService.getItemById(id));
        } else {
            request.setAttribute("item", new AttPointItem());
        }
        return new ModelAndView("att/point/editAttPoint");
    }

    @RequiresPermissions("att:point:edit")
    @LogChangeRequest(module = "att_module", object = "att_leftMenu_point", opType = "common_op_new",
        vo = AttPointItem.class, service = AttPointService.class)
    @Override
    public ZKResultMsg save(AttPointItem item) {
        ZKResultMsg res = new ZKResultMsg();
        attPointService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @Override
    public boolean isExist(@RequestParam(value = "pointName") String pointName) {
        return attPointService.isExist(pointName);
    }

    @Override
    public ZKResultMsg checkLicenseCount(String deviceModule) {
        return attPointService.checkLicenseCount(deviceModule);
    }

    @Override
    public TreeItem getAllParkEntranceArea() {
        List<TreeItem> items = attPointService.getAllParkEntranceArea();
        List<TreeItem> treeItems = TreeBuilder.newTreeBuilder(TreeItem.class, String.class).buildToTreeList(items);
        return new TreeItem("0", treeItems);
    }

    @RequiresPermissions("att:point:refresh")
    @Override
    public DxGrid list(AttPointItem condition) {
        Pager pager =
            attPointService.loadPagerByAuthFilter(request.getSession().getId(), condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public DxGrid selectAttPointList(AttPointSelectItem condition) {
        Pager pager = null;
        if (condition.getType().equals("noSelected")) {
            if (StringUtils.isBlank(condition.getSelectId())) {
                condition.setSelectId("-1");
            }
            pager = attPointService.loadSelectPagerByAuthFilter(request.getSession().getId(), condition, getPageNo(),
                getPageSize());
        } else {
            pager = new Pager(getPageNo(), getPageSize());
            pager.setData(new ArrayList<AttPointSelectItem>());
        }
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:point:del")
    @LogRequest(module = "att_module", object = "att_leftMenu_point", opType = "common_op_del", requestParams = {"ids"},
        opContent = "att_common_id")
    @Override
    public ZKResultMsg delete(String ids) {
        attPointService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    @RequiresPermissions("att:point:export")
    public void export(HttpServletRequest req, HttpServletResponse resp) {
        AttPointItem attPointItem = new AttPointItem();
        setConditionValue(attPointItem);
        List<AttPointItem> list = attPointService.getItemData(AttPointItem.class, attPointItem,
            getBeginIndex(), getEndIndex());
        excelExport(list, AttPointItem.class);
    }

    @Override
    public ZKResultMsg getPointModule() {
        return new ZKResultMsg(attPointService.getPointModule());
    }

    @Override
    public DxGrid esdcChannelList(AttEsdcChannelSelectItem condition) {
        Pager pager = null;
        if (AttConstant.NOSELECT.equals(condition.getType())) {
            if (StringUtils.isBlank(condition.getSelectId())) {
                condition.setSelectId(AttConstant.COMM_DEF_VALUE);
            }
            pager = attPointService.getEsdcChannelSelectList(condition, getPageNo(), getPageSize());
        } else {
            pager = new Pager(getPageNo(), getPageSize());
            pager.setData(new ArrayList<>());
        }
        return GridUtil.convert(pager, condition.getClass());
    }
}