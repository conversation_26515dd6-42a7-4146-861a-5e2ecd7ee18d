package com.zkteco.zkbiosecurity.att.controller;

import com.zkteco.zkbiosecurity.att.remote.AttSignCallRollReportRemote;
import com.zkteco.zkbiosecurity.att.service.AttRealTimeCallRollService;
import com.zkteco.zkbiosecurity.att.vo.AttRealTimeCallRollItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @version v1.0
 * @ClassName: AttSignCallRollReportRemote
 * @Author: bob.liu
 * @Date: 2020/5/12 10:19
 */
@Controller
public class AttSignCallRollReportController extends ExportController implements AttSignCallRollReportRemote {

    @Autowired
    private AttRealTimeCallRollService attRealTimeCallRollService;

    @Override
    public ModelAndView index() {
        return new ModelAndView("/att/report/attSignCallRollReport");
    }

    @Override
    public DxGrid list(AttRealTimeCallRollItem condition) {
        Pager pager = attRealTimeCallRollService.getItemByCondition(condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AttRealTimeCallRollItem condition = new AttRealTimeCallRollItem();
        setConditionValue(condition);
        List<AttRealTimeCallRollItem> itemList =
            attRealTimeCallRollService.getExportItemByCondition(condition, getBeginIndex(), getEndIndex());
        excelExport(itemList, AttRealTimeCallRollItem.class);
    }
}
