package com.zkteco.zkbiosecurity.att.controller;

import com.zkteco.zkbiosecurity.att.remote.AttDayDetailReportRemote;
import com.zkteco.zkbiosecurity.att.service.AttDayDetailReportService;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.vo.AttDayDetailReportItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

/**
 * 考勤日报表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:23
 * @since 1.0.0
 */
@Controller
public class AttDayDetailReportController extends ExportController implements AttDayDetailReportRemote {

    @Autowired
    private AttDayDetailReportService attDayDetailReportService;

    @Autowired
    private AttParamService attParamService;

    @RequiresPermissions("att:dayDetailReport")
    @Override
    public ModelAndView index() {
        attDayDetailReportService.modifyItemLabel(AttDayDetailReportItem.class);
        boolean overtimeLevelEnable = attParamService.overtimeLevelEnable();
        if (!overtimeLevelEnable) {
            request.setAttribute("showColumns", "!overTimeOT1,overTimeOT2,overTimeOT3");
        }
        return new ModelAndView("att/report/attDayDetailReport");
    }

    @RequiresPermissions("att:dayDetailReport:refresh")
    @Override
    public DxGrid list(AttDayDetailReportItem condition) {
        Pager pager = attDayDetailReportService.loadPagerByAuthUserFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize(), getLimitCount());
        return GridUtil.convert(pager, condition.getClass(), getPageList());
    }

    @SuppressWarnings("unchecked")
    @RequiresPermissions("att:dayDetailReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_dayDetailReport", opType = "common_op_export",
        requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void export() {
        AttDayDetailReportItem attDayDetailReportItem = new AttDayDetailReportItem();
        setConditionValue(attDayDetailReportItem);
        List<AttDayDetailReportItem> intemList = attDayDetailReportService.getDayDetailReportItemData(
            request.getSession().getId(), attDayDetailReportItem, getBeginIndex(), getEndIndex());
        excelExport(intemList, AttDayDetailReportItem.class);
    }

    @RequiresPermissions("att:workTimeReport")
    @Override
    public ModelAndView indexWorkTimeReport() {
        attDayDetailReportService.modifyItemLabel(AttDayDetailReportItem.class);

        attDayDetailReportService.modifyItemLabel(AttDayDetailReportItem.class);
        boolean overtimeLevelEnable = attParamService.overtimeLevelEnable();
        if (!overtimeLevelEnable) {
            request.setAttribute("showColumns", "overTimeOT1,overTimeOT2,overTimeOT3");
        }

        return new ModelAndView("att/report/attWorkTimeReport");
    }

    @RequiresPermissions("att:workTimeReport:refresh")
    @Override
    public DxGrid listWorkTimeReport(AttDayDetailReportItem condition) {
        Pager pager = attDayDetailReportService.loadWorkTimePagerByAuthUserFilter(request.getSession().getId(),
            condition, getPageNo(), getPageSize(), getLimitCount());
        return GridUtil.convert(pager, condition.getClass(), getPageList());
    }

    @RequiresPermissions("att:workTimeReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_workTimeReport", opType = "common_op_export",
        requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void exportWorkTimeReport() {
        AttDayDetailReportItem condition = new AttDayDetailReportItem();
        setConditionValue(condition);
        List<AttDayDetailReportItem> itemList = attDayDetailReportService
            .getWorkTimeReportItemData(request.getSession().getId(), condition, getBeginIndex(), getEndIndex());
        excelExport(itemList, AttDayDetailReportItem.class);
    }

    @RequiresPermissions("att:overtimeReport")
    @Override
    public ModelAndView indexOvertimeReport() {
        attDayDetailReportService.modifyItemLabel(AttDayDetailReportItem.class);

        attDayDetailReportService.modifyItemLabel(AttDayDetailReportItem.class);
        boolean overtimeLevelEnable = attParamService.overtimeLevelEnable();
        if (!overtimeLevelEnable) {
            request.setAttribute("showColumns", "overTimeOT1,overTimeOT2,overTimeOT3");
        }

        return new ModelAndView("att/report/attOvertimeReport");
    }

    @RequiresPermissions("att:overtimeReport:refresh")
    @Override
    public DxGrid listOvertimeReport(AttDayDetailReportItem condition) {
        Pager pager = attDayDetailReportService.loadOvertimePagerByAuthUserFilter(request.getSession().getId(),
                condition, getPageNo(), getPageSize(), getLimitCount());
        return GridUtil.convert(pager, condition.getClass(), getPageList());
    }

    @RequiresPermissions("att:overtimeReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_overtimeReport", opType = "common_op_export",
            requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void exportOvertimeReport() {
        AttDayDetailReportItem condition = new AttDayDetailReportItem();
        setConditionValue(condition);
        List<AttDayDetailReportItem> itemList = attDayDetailReportService
                .getOvertimeReportItemData(request.getSession().getId(), condition, getBeginIndex(), getEndIndex());
        excelExport(itemList, AttDayDetailReportItem.class);
    }

    @RequiresPermissions("att:leaveReport")
    @Override
    public ModelAndView indexLeaveReport() {
        attDayDetailReportService.modifyItemLabel(AttDayDetailReportItem.class);
        return new ModelAndView("att/report/attLeaveReport");
    }

    @RequiresPermissions("att:leaveReport:refresh")
    @Override
    public DxGrid listLeaveReport(AttDayDetailReportItem condition) {
        Pager pager = attDayDetailReportService.loadLeavePagerByAuthUserFilter(request.getSession().getId(),
                condition, getPageNo(), getPageSize(), getLimitCount());
        return GridUtil.convert(pager, condition.getClass(), getPageList());
    }

    @RequiresPermissions("att:leaveReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_leaveReport", opType = "common_op_export",
            requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void exportLeaveReport() {
        AttDayDetailReportItem condition = new AttDayDetailReportItem();
        setConditionValue(condition);
        List<AttDayDetailReportItem> itemList = attDayDetailReportService
                .getLeaveReportItemData(request.getSession().getId(), condition, getBeginIndex(), getEndIndex());
        excelExport(itemList, AttDayDetailReportItem.class);
    }

    @RequiresPermissions("att:abnormalReport")
    @Override
    public ModelAndView indexAbnormalReport() {
        attDayDetailReportService.modifyItemLabel(AttDayDetailReportItem.class);
        return new ModelAndView("att/report/attAbnormalReport");
    }

    @RequiresPermissions("att:abnormalReport:refresh")
    @Override
    public DxGrid listAbnormalReport(AttDayDetailReportItem condition) {
        Pager pager = attDayDetailReportService.loadAbnormalReportPagerByAuthUserFilter(request.getSession().getId(),
            condition, getPageNo(), getPageSize(), getLimitCount());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:abnormalReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_abnormal", opType = "common_op_export",
        requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void exportAbnormalReport() {
        AttDayDetailReportItem condition = new AttDayDetailReportItem();
        setConditionValue(condition);
        List<AttDayDetailReportItem> itemList = attDayDetailReportService
            .getAbnormalReportItemData(request.getSession().getId(), condition, getBeginIndex(), getEndIndex());
        excelExport(itemList, AttDayDetailReportItem.class);
    }

    @RequiresPermissions("att:lateReport")
    @Override
    public ModelAndView indexLateReport() {
        attDayDetailReportService.modifyItemLabel(AttDayDetailReportItem.class);
        return new ModelAndView("att/report/attLateReport");
    }

    @RequiresPermissions("att:lateReport:refresh")
    @Override
    public DxGrid listLateReport(AttDayDetailReportItem condition) {
        Pager pager = attDayDetailReportService.loadLateReportPagerByAuthUserFilter(request.getSession().getId(),
                condition, getPageNo(), getPageSize(), getLimitCount());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:lateReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_lateReport", opType = "common_op_export",
            requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void exportLateReport() {
        AttDayDetailReportItem condition = new AttDayDetailReportItem();
        setConditionValue(condition);
        List<AttDayDetailReportItem> itemList = attDayDetailReportService
                .getLateReportItemData(request.getSession().getId(), condition, getBeginIndex(), getEndIndex());
        excelExport(itemList, AttDayDetailReportItem.class);
    }

    @RequiresPermissions("att:earlyReport")
    @Override
    public ModelAndView indexEarlyReport() {
        attDayDetailReportService.modifyItemLabel(AttDayDetailReportItem.class);
        return new ModelAndView("att/report/attEarlyReport");
    }

    @RequiresPermissions("att:earlyReport:refresh")
    @Override
    public DxGrid listEarlyReport(AttDayDetailReportItem condition) {
        Pager pager = attDayDetailReportService.loadEarlyReportPagerByAuthUserFilter(request.getSession().getId(),
                condition, getPageNo(), getPageSize(), getLimitCount());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:earlyReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_earlyReport", opType = "common_op_export",
            requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void exportEarlyReport() {
        AttDayDetailReportItem condition = new AttDayDetailReportItem();
        setConditionValue(condition);
        List<AttDayDetailReportItem> itemList = attDayDetailReportService
                .getEarlyReportItemData(request.getSession().getId(), condition, getBeginIndex(), getEndIndex());
        excelExport(itemList, AttDayDetailReportItem.class);
    }

    @RequiresPermissions("att:absentReport")
    @Override
    public ModelAndView indexAbsentReport() {
        attDayDetailReportService.modifyItemLabel(AttDayDetailReportItem.class);
        return new ModelAndView("att/report/attAbsentReport");
    }

    @RequiresPermissions("att:absentReport:refresh")
    @Override
    public DxGrid listAbsentReport(AttDayDetailReportItem condition) {
        Pager pager = attDayDetailReportService.loadAbsentReportPagerByAuthUserFilter(request.getSession().getId(),
                condition, getPageNo(), getPageSize(), getLimitCount());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:absentReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_absentReport", opType = "common_op_export",
            requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void exportAbsentReport() {
        AttDayDetailReportItem condition = new AttDayDetailReportItem();
        setConditionValue(condition);
        List<AttDayDetailReportItem> itemList = attDayDetailReportService
                .getAbsentReportItemData(request.getSession().getId(), condition, getBeginIndex(), getEndIndex());
        excelExport(itemList, AttDayDetailReportItem.class);
    }
}
