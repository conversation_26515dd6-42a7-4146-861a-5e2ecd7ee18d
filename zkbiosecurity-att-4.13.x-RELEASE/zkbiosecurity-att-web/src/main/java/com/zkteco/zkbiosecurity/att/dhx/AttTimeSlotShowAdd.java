package com.zkteco.zkbiosecurity.att.dhx;

import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import com.zkteco.zkbiosecurity.core.utils.ShowGridColumn;

/**
 * 弹性时间段不允许添加休息时间段
 * 
 * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
 * @date 2019/6/6 15:12
 */
@Component
public class AttTimeSlotShowAdd implements ShowGridColumn {

    @Override
    public boolean isShow(Object item) {
        AttTimeSlotItem timeSlotItem = (AttTimeSlotItem)item;
        short periodType = timeSlotItem.getPeriodType();// 时段类型（0：正常时间段，1：弹性时间段）
        return periodType == (short)0 ? true : false;
    }

}
