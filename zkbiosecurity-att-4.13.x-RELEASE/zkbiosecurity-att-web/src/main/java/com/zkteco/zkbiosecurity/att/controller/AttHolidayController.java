package com.zkteco.zkbiosecurity.att.controller;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.remote.AttHolidayRemote;
import com.zkteco.zkbiosecurity.att.service.AttHolidayService;
import com.zkteco.zkbiosecurity.att.vo.AttHolidayItem;
import com.zkteco.zkbiosecurity.base.annotation.LogChangeRequest;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 节假日
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 9:53
 * @since 1.0.0
 */
@Controller
public class AttHolidayController extends BaseController implements AttHolidayRemote {

    @Autowired
    private AttHolidayService attHolidayService;

    @RequiresPermissions("att:holiday")
    @Override
    public ModelAndView index() {
        return new ModelAndView("att/holiday/attHoliday");
    }

    @RequiresPermissions("att:holiday:edit")
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("item", attHolidayService.getItemById(id));
        }
        return new ModelAndView("att/holiday/editAttHoliday");
    }

    @RequiresPermissions("att:holiday:edit")
    @LogChangeRequest(module = "att_module", object = "att_leftMenu_holiday", opType = "common_op_new",
        vo = AttHolidayItem.class, service = AttHolidayService.class)
    @Override
    public ZKResultMsg save(AttHolidayItem item) {
        ZKResultMsg res = new ZKResultMsg();
        attHolidayService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("att:holiday:refresh")
    @Override
    public DxGrid list(AttHolidayItem condition) {
        Pager pager =
            attHolidayService.loadPagerByFilter(request.getSession().getId(), condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:holiday:del")
    @LogRequest(module = "att_module", object = "att_leftMenu_holiday", opType = "common_op_del",
        requestParams = {"names"}, opContent = "common_name")
    @Override
    public ZKResultMsg delete(String ids) {
        attHolidayService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public String validName(String holidayName) {
        boolean rs = !attHolidayService.existsByHolidayName(holidayName);
        return rs + "";
    }
}