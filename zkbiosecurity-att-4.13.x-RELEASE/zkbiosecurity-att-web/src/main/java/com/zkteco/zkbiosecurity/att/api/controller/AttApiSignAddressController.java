package com.zkteco.zkbiosecurity.att.api.controller;

import com.alibaba.fastjson.JSONArray;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.service.AttSignAddressService;
import com.zkteco.zkbiosecurity.att.utils.AttCoordinateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttSignAddressItem;
import com.zkteco.zkbiosecurity.base.annotation.ApiPermissions;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Map;

/**
 * APP考勤签到地址查询
 * 
 * <AUTHOR>
 * @date 2019/04/17
 */
@Controller
@RequestMapping(value = "/api/attSignAddress")
public class AttApiSignAddressController {

    @Autowired
    private AttSignAddressService attSignAddressService;

    @RequestMapping("/getPersonSignAddress")
    @ResponseBody
    @ApiPermissions(moduleCode = "att", moduleName = "att_module")
    public ZKResultMsg getPersonSignAddress(@RequestBody ZKMessage zkMessage) {
        Map<String, Object> content = zkMessage.getContent();
        String personPin = MapUtils.getString(content, "personPin");
        List<AttSignAddressItem> signAddressList = attSignAddressService.getPersonSignAddress(personPin);
        for  (AttSignAddressItem item : signAddressList) {
            double lng = Double.parseDouble(item.getLongitude());
            double lat = Double.parseDouble(item.getLatitude());
            // 默认高德，因为旧版本这个数据是空的，就是高德地图坐标
            String signMapType = item.getMapType();
            if (StringUtils.isBlank(signMapType)) {
                signMapType = AttConstant.ATT_MAP_GAODEMAP;
            }

            if (AttConstant.ATT_MAP_BAIDUMAP.equals(signMapType)) {
                double[] gcj02 = AttCoordinateUtils.bd09ToGcj02(lng, lat);
                lng = gcj02[0];
                lat  = gcj02[1];
            } else if (AttConstant.ATT_MAP_GAODEMAP.equals(signMapType)) {
                //
            } else if (AttConstant.ATT_MAP_GOOGLEMAP.equals(signMapType)) {
                double[] gcj02 = AttCoordinateUtils.wgs84ToGcj02(lng, lat);
                lng = gcj02[0];
                lat  = gcj02[1];
            }
            item.setLongitude(String.valueOf(lng));
            item.setLatitude(String.valueOf(lat));
        }
        return new ZKResultMsg(JSONArray.toJSONString(signAddressList));
    }
}
