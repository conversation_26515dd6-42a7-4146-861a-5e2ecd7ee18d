package com.zkteco.zkbiosecurity.att.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.remote.AttParamRemote;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 考勤规则
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:34
 * @since 1.0.0
 */
@Controller
public class AttParamController extends BaseController implements AttParamRemote {

    @Autowired
    private AttParamService attParamService;

    @RequiresPermissions("att:param:edit")
    @LogRequest(module = "att_module", object = "att_leftMenu_param", opType = "common_op_edit",
        opContent = "att_rule_baseRuleSet")
    @Override
    public ZKResultMsg save(@RequestParam Map<String, String> params) {
        attParamService.saveItem(params);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

}
