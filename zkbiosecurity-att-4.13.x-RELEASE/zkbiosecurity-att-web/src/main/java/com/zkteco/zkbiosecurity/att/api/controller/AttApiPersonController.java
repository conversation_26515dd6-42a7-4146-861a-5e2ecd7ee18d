package com.zkteco.zkbiosecurity.att.api.controller;

import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.zkteco.zkbiosecurity.att.api.vo.*;
import com.zkteco.zkbiosecurity.att.service.AttApiService;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 考勤人员接口
 * 
 * @Auther: lambert.li
 * @Date: 2018/11/15 17:03
 */
@Controller
@RequestMapping(value = {"/api/attPerson"})
@Slf4j
@Api(tags = "AttPerson", description = "att person")
public class AttApiPersonController {

    @Autowired
    private AttApiService attApiService;

    /**
     * 保存考勤人员信息
     *
     * @param attApiPersonItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/15 17:02
     * @since 1.0.0
     */
    @ApiLogRequest(requestParams = {"personPin", "isAttendance"})
    @ResponseBody
    @RequestMapping(value = "/editAttPerson", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Edit AttPerson", notes = "Edit AttPerson", response = ApiResultMessage.class)
    public ApiResultMessage editAttPerson(@RequestBody AttApiPersonItem attApiPersonItem) {
        if (StringUtils.isBlank(attApiPersonItem.getPersonPin())) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_notNull", "personPin"));
        }
        if (attApiPersonItem.getIsAttendance() == null) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_notNull", "isAttendance"));
        }
        if (attApiPersonItem.getPerDevAuth() == null) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_notNull", "perDevAuth"));
        }
        if (attApiPersonItem.getVerifyMode() == null) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_notNull", "verifyMode"));
        }
        return attApiService.editAttPerson(attApiPersonItem);
    }
}
