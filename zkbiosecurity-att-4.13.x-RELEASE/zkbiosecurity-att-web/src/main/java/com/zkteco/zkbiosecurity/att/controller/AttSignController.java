package com.zkteco.zkbiosecurity.att.controller;

import java.io.IOException;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.remote.AttSignRemote;
import com.zkteco.zkbiosecurity.att.service.AttPersonService;
import com.zkteco.zkbiosecurity.att.service.AttSignService;
import com.zkteco.zkbiosecurity.att.util.AttExcelUtil;
import com.zkteco.zkbiosecurity.att.util.AttImportWithRowNumUtil;
import com.zkteco.zkbiosecurity.att.vo.AttPersonItem;
import com.zkteco.zkbiosecurity.att.vo.AttSignItem;
import com.zkteco.zkbiosecurity.base.annotation.LogChangeRequest;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.LocaleMessageSourceUtil;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 补签
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 15:52
 * @since 1.0.0
 */
@Controller
public class AttSignController extends ExportController implements AttSignRemote {

    @Autowired
    private AttSignService attSignService;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private AttExcelUtil attExcelUtil;

    @RequiresPermissions("att:sign")
    @Override
    public ModelAndView index() {
        if (getCurrentSubject().getStaff()) {
            request.setAttribute("flowType", AttConstant.FLOW_TYPE_SIGN);
            return new ModelAndView("att/flowable/sign/attSign");
        } else {
            return new ModelAndView("att/sign/attSign");
        }
    }

    @RequiresPermissions("att:sign:add")
    @Override
    public ModelAndView edit(String id) {
        AttSignItem item = attSignService.getItemById(id);
        request.setAttribute("item", item);
        return new ModelAndView("att/sign/editAttSign");
    }

    @RequiresPermissions("att:sign:add")
    @LogChangeRequest(module = "att_module", object = "att_leftMenu_sign", opType = "common_op_new",
        vo = AttSignItem.class, service = AttSignService.class)
    @Override
    public ZKResultMsg save(AttSignItem item) {
        ZKResultMsg res = new ZKResultMsg();
        String personIds = request.getParameter("personIds");
        if (StringUtils.isBlank(personIds)) {
            throw new ZKBusinessException("EXCEPTIONLEVEL_WARN", "common_prompt_selectObj");
        }

        // 异常申请开始时间不能小于入职时间 add by bob.liu at 20191023
        List<AttPersonItem> persPersonList = attPersonService.getItemByPersonIds(StrUtil.strToList(personIds));
        for (AttPersonItem attPersonItem : persPersonList) {
            Date hireDate = attPersonItem.getHireDate();
            if (hireDate != null && (hireDate.getTime() > item.getSignDatetime().getTime())) {
                res.setMsg((I18nUtil.i18nCode("att_h5_persNoHire")));
                res.setData("false");
                return res;
            }
        }
        attSignService.saveItem(item, personIds, request.getSession().getId());
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("att:sign:refresh")
    @Override
    public DxGrid list(AttSignItem condition) {
        Pager pager =
            attSignService.loadPagerByAuthFilter(request.getSession().getId(), condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:sign:del")
    @LogRequest(module = "att_module", object = "att_leftMenu_sign", opType = "common_op_del", requestParams = {"pins"},
        opContent = "att_person_pin")
    @Override
    public ZKResultMsg delete(String ids) {
        attSignService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @SuppressWarnings("unchecked")
    @RequiresPermissions("att:sign:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_sign", opType = "common_op_export",
        requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AttSignItem attSignItem = new AttSignItem();
        setConditionValue(attSignItem);
        List<AttSignItem> intemList = attSignService.getItemData(request.getSession().getId(),
            attSignItem, getBeginIndex(), getEndIndex());
        excelExport(intemList, AttSignItem.class);
    }

    @Override
    public void importTemplate(HttpServletRequest request, HttpServletResponse response) {

        /*jsonColumn设置要显示的字段*/
        JSONObject jsonColumn = new JSONObject(true);
        jsonColumn.put("personPin", "");
        jsonColumn.put("personName", "");
        if (!"zh_CN".equals(LocaleMessageSourceUtil.language)) {
            jsonColumn.put("personLastName", "");
        }
        jsonColumn.put("signDatetime", "");
        jsonColumn.put("remark", "");
        Map<String, Map<String, String>> jsonCloumnMap = new HashMap<>();
        Map<String, String> jsonMap = new HashMap<>();
        jsonMap.put("jsonColumn", jsonColumn.toJSONString());
        jsonCloumnMap.put("jsonColumn", jsonMap);
        attExcelUtil.attExcelExport(request, response, new ArrayList<>(), AttSignItem.class, jsonCloumnMap);
    }

    @Override
    public ZKResultMsg importExcel(MultipartFile upload) throws IOException {
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_uploadFileSuccess") + "<br/>"));
            List<AttSignItem> itemList =
                AttImportWithRowNumUtil.excelImport(upload.getInputStream(), AttSignItem.class);
            progress += 10;
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_resolutionComplete") + "<br/>"));
            return I18nUtil.i18nMsg(attSignService.importExcel(itemList, request.getSession().getId()));
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                    + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                    new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("Import AttSign Info Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
    }

    @RequiresPermissions("att:sign:approval")
    @LogRequest(module = "att_module", object = "att_leftMenu_sign", opType = "att_apply_pass",
        requestParams = {"pins"}, opContent = "att_person_pin")
    @Override
    public ZKResultMsg approval(String ids) {
        if (attSignService.hasStaffApply(ids)) {
            // 存在员工自助申请, 提示管理员a不让操作
            return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg("att_approve_withoutPermissionApproval"));
        }
        attSignService.approval(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @RequiresPermissions("att:sign:approval")
    @LogRequest(module = "att_module", object = "att_leftMenu_sign", opType = "att_exception_refuse",
        requestParams = {"pins"}, opContent = "att_person_pin")
    @Override
    public ZKResultMsg refuse(String ids) {
        if (attSignService.hasStaffApply(ids)) {
            return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg("att_approve_withoutPermissionApproval"));
        }
        attSignService.refuse(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }
}