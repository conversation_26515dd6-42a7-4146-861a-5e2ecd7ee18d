package com.zkteco.zkbiosecurity.att.controller;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.remote.AttAutoExportRemote;
import com.zkteco.zkbiosecurity.att.service.AttAutoExportService;
import com.zkteco.zkbiosecurity.att.vo.AttAutoExportItem;
import com.zkteco.zkbiosecurity.base.annotation.LogChangeRequest;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.utils.Base64Util;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 报表推送
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 16:32
 * @since 1.0.0
 */
@Controller
public class AttAutoExportController extends BaseController implements AttAutoExportRemote {

    @Autowired
    private AttAutoExportService attAutoExportService;

    @RequiresPermissions("att:autoExport")
    @Override
    public ModelAndView index() {
        return new ModelAndView("att/autoExport/attAutoExport");
    }

    @RequiresPermissions("att:autoExport:edit")
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("item", attAutoExportService.getItemById(id));
        }
        // 判断是否设置了系统管理里的邮件参数
        if (attAutoExportService.completeMailInfo()) {
            request.setAttribute("isSetSystemMailParam", "true");
        } else {
            request.setAttribute("isSetSystemMailParam", "false");
        }
        return new ModelAndView("att/autoExport/editAttAutoExport");
    }

    @RequiresPermissions("att:autoExport:edit")
    @LogChangeRequest(module = "att_module", object = "att_leftMenu_autoExport", opType = "common_op_edit",
        vo = AttAutoExportItem.class, service = AttAutoExportService.class)
    @Override
    public ZKResultMsg save(AttAutoExportItem item) {
        ZKResultMsg res = new ZKResultMsg();
        // 密码加密绕过平台特殊字符过滤后解密
        if (StringUtils.isNotBlank(item.getFtpPassword())) {
            item.setFtpPassword(new String(Base64Util.decode(item.getFtpPassword())));
        }
        attAutoExportService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("att:autoExport:refresh")
    @Override
    public DxGrid list(AttAutoExportItem condition) {
        Pager pager = attAutoExportService.getItemsByPage(condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:autoExport:del")
    @LogRequest(module = "att_module", object = "att_leftMenu_autoExport", opType = "common_op_del",
        requestParams = {"ids"}, opContent = "att_common_id")
    @Override
    public ZKResultMsg delete(String ids) {
        attAutoExportService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("att:autoExport:enable")
    @Override
    public ZKResultMsg enable(String ids) {
        attAutoExportService.enable(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("att:autoExport:disable")
    @Override
    public ZKResultMsg disable(String ids) {
        attAutoExportService.disable(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public ZKResultMsg ftpTest(String sendFormat, String ftpUrl, int ftpPort, String ftpUsername, String ftpPassword) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        try {
            // 密码加密绕过平台特殊字符过滤后解密
            ftpPassword = new String(Base64Util.decode(ftpPassword));
            boolean b = attAutoExportService.ftpTest(sendFormat, ftpUrl, ftpPort, ftpUsername, ftpPassword);
            if (!b) {
                zkResultMsg.setRet("fail");
                zkResultMsg.setMsg("att_autoExport_ftpFail");
            } else {
                zkResultMsg.setMsg("att_autoExport_ftpSuccess");
            }
        } catch (Exception e) {
            log.error("exception = ", e);
        }
        return I18nUtil.i18nMsg(zkResultMsg);
    }
}