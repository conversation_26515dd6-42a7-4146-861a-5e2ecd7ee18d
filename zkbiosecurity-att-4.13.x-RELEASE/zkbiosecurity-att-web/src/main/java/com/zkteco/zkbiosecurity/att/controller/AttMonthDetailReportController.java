package com.zkteco.zkbiosecurity.att.controller;

import com.zkteco.zkbiosecurity.att.remote.AttMonthDetailReportRemote;
import com.zkteco.zkbiosecurity.att.service.AttMonthDetailReportService;
import com.zkteco.zkbiosecurity.att.vo.AttMonthDetailReportItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;
import java.util.Map;

/**
 * 考勤月报表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:22
 * @since 1.0.0
 */
@Controller
public class AttMonthDetailReportController extends ExportController implements AttMonthDetailReportRemote {

    @Autowired
    private AttMonthDetailReportService attMonthDetailReportService;

    @RequiresPermissions("att:monthDetailReport")
    @Override
    public ModelAndView index() {

        // 动态修改表头
        attMonthDetailReportService.modifyItemLabel();

        return new ModelAndView("att/report/attMonthDetailReport");
    }

    @RequiresPermissions("att:monthDetailReport:refresh")
    @Override
    public DxGrid list(AttMonthDetailReportItem condition) {
        Pager pager = attMonthDetailReportService.loadPagerByAuthUserFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @SuppressWarnings("unchecked")
    @RequiresPermissions("att:monthDetailReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_monthDetailReport", opType = "common_op_export",
        requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void export() {
        String reportType = request.getParameter("reportType");
        boolean showColor = false;
        if (StringUtils.isNotBlank(reportType) && "XLS".equals(reportType)) {
            showColor = true;
        }
        AttMonthDetailReportItem attMonthDetailReportItem = new AttMonthDetailReportItem();
        setConditionValue(attMonthDetailReportItem);
        List<AttMonthDetailReportItem> itemList = attMonthDetailReportService.getMonthDetailReportItemData(
            request.getSession().getId(), attMonthDetailReportItem, getBeginIndex(), getEndIndex(), showColor);
        excelExport(itemList, AttMonthDetailReportItem.class);

    }

    @RequiresPermissions("att:monthWorkTimeReport")
    @Override
    public ModelAndView indexMonthWorkTimeReport() {

        // 动态修改表头
        attMonthDetailReportService.modifyItemLabel();

        return new ModelAndView("att/report/attMonthWorkTimeReport");
    }

    @RequiresPermissions("att:monthWorkTimeReport:refresh")
    @Override
    public DxGrid listMonthWorkTimeReport(AttMonthDetailReportItem condition) {
        Pager pager = attMonthDetailReportService.loadMonthWorkTimeReportPager(request.getSession().getId(), condition,
                getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @SuppressWarnings("unchecked")
    @RequiresPermissions("att:monthWorkTimeReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_monthWorkTimeReport", opType = "common_op_export",
            requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void exportMonthWorkTimeReport() {
        AttMonthDetailReportItem attMonthDetailReportItem = new AttMonthDetailReportItem();
        setConditionValue(attMonthDetailReportItem);
        List<AttMonthDetailReportItem> itemList = attMonthDetailReportService.getMonthWorkTimeReportItemData(
                request.getSession().getId(), attMonthDetailReportItem, getBeginIndex(), getEndIndex());
        excelExport(itemList, AttMonthDetailReportItem.class);

    }

    @RequiresPermissions("att:monthCardReport")
    @Override
    public ModelAndView indexMonthCardReport() {
        attMonthDetailReportService.modifyItemLabel();
        return new ModelAndView("att/report/attMonthCardReport");
    }

    @RequiresPermissions("att:monthCardReport:refresh")
    @Override
    public DxGrid listMonthCardReport(AttMonthDetailReportItem condition) {
        Pager pager = attMonthDetailReportService.loadMonthCardReportPager(request.getSession().getId(), condition,
                getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @SuppressWarnings("unchecked")
    @RequiresPermissions("att:monthCardReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_monthCardReport", opType = "common_op_export",
            requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void exportMonthCardReport() {
        AttMonthDetailReportItem attMonthDetailReportItem = new AttMonthDetailReportItem();
        setConditionValue(attMonthDetailReportItem);
        List<AttMonthDetailReportItem> itemList = attMonthDetailReportService.getMonthCardReportItemData(
                request.getSession().getId(), attMonthDetailReportItem, getBeginIndex(), getEndIndex());
        excelExport(itemList, AttMonthDetailReportItem.class);

    }

    @RequiresPermissions("att:monthOvertimeReport")
    @Override
    public ModelAndView indexMonthOvertimeReport() {
        attMonthDetailReportService.modifyItemLabel();
        return new ModelAndView("att/report/attMonthOvertimeReport");
    }

    @RequiresPermissions("att:monthOvertimeReport:refresh")
    @Override
    public DxGrid listMonthOvertimeReport(AttMonthDetailReportItem condition) {
        Pager pager = attMonthDetailReportService.loadMonthOvertimeReportPager(request.getSession().getId(), condition,
                getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @SuppressWarnings("unchecked")
    @RequiresPermissions("att:monthOvertimeReport:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_monthOvertimeReport", opType = "common_op_export",
            requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void exportMonthOvertimeReport() {
        AttMonthDetailReportItem attMonthDetailReportItem = new AttMonthDetailReportItem();
        setConditionValue(attMonthDetailReportItem);
        List<AttMonthDetailReportItem> itemList = attMonthDetailReportService.getMonthOvertimeReportItemData(
                request.getSession().getId(), attMonthDetailReportItem, getBeginIndex(), getEndIndex());
        excelExport(itemList, AttMonthDetailReportItem.class);

    }

    @Override
    public Map<String, String> getAttSymbolsJson() {
        return attMonthDetailReportService.getAttSymbolsI18nJson();
    }
}
