package com.zkteco.zkbiosecurity.att.controller;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.remote.AttLeaveTypeRemote;
import com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.base.annotation.LogChangeRequest;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.PropertiesUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 假种
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:13
 * @since 1.0.0
 */
@Controller
public class AttLeaveTypeController extends BaseController implements AttLeaveTypeRemote {

    @Autowired
    private AttLeaveTypeService attLeaveTypeService;

    @RequiresPermissions("att:leaveType")
    @Override
    public ModelAndView index() {
        return new ModelAndView("att/leaveType/attLeaveType");
    }

    @RequiresPermissions("att:leaveType:edit")
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            AttLeaveTypeItem leaveTypeItem = attLeaveTypeService.getItemById(id);
            if (leaveTypeItem.getInitFlag()) {
                String lang = PropertiesUtil.getCurrentLanguage();// 从前端获取当前语言
                // 针对初始化的假种名称进行国际化转换--add by hook.fang 2020-2-18
                leaveTypeItem.setLeaveTypeName(I18nUtil.i18nCode(leaveTypeItem.getLeaveTypeName(), lang));
            }
            request.setAttribute("item", leaveTypeItem);
        }
        return new ModelAndView("att/leaveType/editAttLeaveType");
    }

    @RequiresPermissions("att:leaveType:edit")
    @LogChangeRequest(module = "att_module", object = "att_leftMenu_leaveType", opType = "common_op_edit",
        vo = AttLeaveTypeItem.class, service = AttLeaveTypeService.class)
    @Override
    public ZKResultMsg save(AttLeaveTypeItem item) {
        ZKResultMsg res = new ZKResultMsg();
        attLeaveTypeService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("att:leaveType:refresh")
    @Override
    public DxGrid list(AttLeaveTypeItem condition) {
        condition.setMark(AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        Pager pager = attLeaveTypeService.getItemsByPage(condition, getPageNo(), getPageSize());

        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:leaveType:del")
    @LogRequest(module = "att_module", object = "att_leftMenu_leaveType", opType = "common_op_del",
        requestParams = {"names"}, opContent = "common_name")
    @Override
    public ZKResultMsg delete(String ids) {
        // 判断删除是是否有初始化数据
        if (attLeaveTypeService.isInitData(ids)) {
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("common_prompt_initDataCanNotDel"));
        }
        // 判断删除的 数据是否存在外键关联
        if (attLeaveTypeService.isExistFkData(ids)) {
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("att_common_relationDataCanNotDel"));
        }

        attLeaveTypeService.deleteByIds(ids);

        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @Override
    public ZKResultMsg isLeaveTypeName(String leaveTypeName) {
        AttLeaveTypeItem condition = new AttLeaveTypeItem();
        condition.setEquals(true);
        condition.setLeaveTypeName(leaveTypeName);
        List<AttLeaveTypeItem> list = attLeaveTypeService.getByCondition(condition);
        if (CollectionUtils.isEmpty(list)) {
            return ZKResultMsg.successMsg();
        }
        AttLeaveTypeItem attLeaveTypeItem = list.get(0);
        ZKResultMsg zkResultMsg = ZKResultMsg.failMsg();
        if (AttConstant.ATT_CONVERT_MARK_LEAVETYPE.equals(attLeaveTypeItem.getMark())) {
            zkResultMsg.setMsg(I18nUtil.i18nCode("common_jqMsg_remote"));
        } else {
            zkResultMsg.setMsg(I18nUtil.i18nCode("att_leave_typeNameConflict"));
        }
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg listJsonLeaveType() {
        return attLeaveTypeService.listJsonLeaveType();
    }

    @Override
    public ZKResultMsg listJsonLeaveTypeFilterTripAndOut() {
        return attLeaveTypeService.listJsonLeaveTypeFilterTripAndOut();
    }
}