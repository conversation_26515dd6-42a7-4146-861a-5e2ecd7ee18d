/**
 * <AUTHOR>
 * @date 2020/4/24 16:54
 */
package com.zkteco.zkbiosecurity.att.dhx;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import com.zkteco.zkbiosecurity.att.service.AttParamService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.base.bean.GridColumnItem;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.DynamicColumn;
import com.zkteco.zkbiosecurity.core.utils.WebContextUtil;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 16:54 2020/4/24
 */
@Component
public class AttPersonSchDynamicColumn implements DynamicColumn {

    @Autowired
    private AttParamService attParamService;

    @Override
    public List<GridColumnItem> getColumn(String filedName) {
        List<GridColumnItem> columns = new ArrayList<GridColumnItem>();
        HttpServletRequest request = WebContextUtil.getCurrentRequest();
        String startDate = request.getParameter("startDate");
        String endDate = request.getParameter("endDate");

        if (StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
            startDate = DateUtil.getDate(new Date(), DateUtil.DateStyle.YYYY_MM_DD);
            endDate = AttDateUtils.dateToStrAsShort(
                    DateUtil.addMonth(DateUtil.stringToDate(startDate, DateUtil.DateStyle.YYYY_MM_DD), 1));
        }

        // 导出处理
        String queryCondition = request.getParameter("queryConditions");
        if (queryCondition != null) {
            String[] conditionAry = queryCondition.replaceAll("%20", " ").split("&");
            for (String condition : conditionAry) {
                if (StringUtils.isBlank(condition)) {
                    continue;
                }
                String[] strAry = condition.split("=", 2);
                String fieldName = strAry[0];
                String fieldValue = strAry[1];
                if ("startDate".equals(fieldName)) {
                    startDate = fieldValue;
                } else if ("endDate".equals(fieldName)) {
                    endDate = fieldValue;
                }
            }
        }

        List<String> dayList = AttDateUtils.getBetweenDate(startDate, AttDateUtils.getEndDate(startDate, endDate));

        for (int i = 1; i <= dayList.size(); i++) {
            GridColumnItem columnItem = new GridColumnItem();
            columnItem.setName(dayList.get(i - 1));
            columnItem.setColumnType("ro");
            columnItem.setSort("na");
            columnItem.setLabel("#cspan");
            columnItem.setWidth("110");
            columnItem.setSortNo(i + 5);
            String labelDate = attParamService.dateToLocaleString(DateUtil.stringToDate(dayList.get(i - 1)));
            columnItem.setLabel(labelDate);
            columnItem.setFieldName(filedName);
            columns.add(columnItem);
        }
        return columns;
    }
}
