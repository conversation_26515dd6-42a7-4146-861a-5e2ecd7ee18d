package com.zkteco.zkbiosecurity.att.controller;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSONArray;
import com.zkteco.zkbiosecurity.att.bean.AttDXCalendarEventBean;
import com.zkteco.zkbiosecurity.att.constants.AttCommonSchConstant;
import com.zkteco.zkbiosecurity.att.remote.AttTempSchRemote;
import com.zkteco.zkbiosecurity.att.service.AttGroupService;
import com.zkteco.zkbiosecurity.att.service.AttTempSchService;
import com.zkteco.zkbiosecurity.att.util.AttExcelUtil;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttGroupItem;
import com.zkteco.zkbiosecurity.att.vo.AttTempSchItem;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.annotation.LogChangeRequest;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 临时排班（人员、部门、分组）
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:19
 * @since 1.0.0
 */
@Controller
public class AttTempSchController extends ExportController implements AttTempSchRemote {

    @Autowired
    private AttTempSchService attTempSchService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Value("${security.session.timeout:1800}")
    private Long sessionTimeout;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AttGroupService attGroupService;
    @Autowired
    private AttExcelUtil attExcelUtil;

    @RequiresPermissions("att:tempsch:edit")
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            AttTempSchItem attTempSchItem = attTempSchService.getItemById(id);
            // 根据排班类型设置人员名称或者部门名称
            // setShowInfo(attTempSchItem);
            AttDXCalendarEventBean dXCalendarEvent = new AttDXCalendarEventBean();
            dXCalendarEvent.setAttTimeSlotIds(attTempSchItem.getTimeSlotIds());
            dXCalendarEvent.setStart_date(attTempSchItem.getStartDate());
            // 前端控件相差大于一天需要再加一天处理
            int days = AttDateUtils.diffDays(attTempSchItem.getStartDate(), attTempSchItem.getEndDate());
            if (days > 0) {
                dXCalendarEvent.setEnd_date(DateUtil.addDay(attTempSchItem.getEndDate(), 1));
                dXCalendarEvent.set_timed(false);
            } else {
                dXCalendarEvent.setEnd_date(attTempSchItem.getEndDate());
            }
            dXCalendarEvent.setText(attTempSchItem.getTimeSlotName());
            request.setAttribute("type", attTempSchItem.getTempType());
            request.setAttribute("dXCalendarEvent", dXCalendarEvent);
            request.setAttribute("item", attTempSchItem);
        }
        return new ModelAndView("att/personSch/editAttTempSch");
    }

    /**
     * 根据排班类型设置人员名称或者部门名称
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/27 19:57
     * @param attTempSchItem
     * @return void
     */
//    private void setShowInfo(AttTempSchItem attTempSchItem) {
//        if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(attTempSchItem.getTempType())) {
//            PersPersonItem personItem = persPersonService.getSimpleItemById(attTempSchItem.getPersonId());
//            if (personItem != null) {
//                request.setAttribute("showInfo", personItem.getName() + "(" + personItem.getPin() + ")");
//            }
//        } else if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(attTempSchItem.getTempType())) {
//            AuthDepartmentItem authDepartmentItem = authDepartmentService.getItemById(attTempSchItem.getDeptId());
//            request.setAttribute("showInfo", authDepartmentItem.getName());
//        } else if (AttCommonSchConstant.SCH_TYPE_GROUP.equals(attTempSchItem.getTempType())) {
//            AttGroupItem attGroupItem = attGroupService.getItemById(attTempSchItem.getGroupId());
//            request.setAttribute("showInfo", attGroupItem.getGroupName());
//        }
//    }

    @Override
    public DxGrid list(AttTempSchItem condition) {
        Pager pager = attTempSchService.loadPagerByAuthUserFilter(request.getSession().getId(), condition, getPageNo(),
            getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:schDetails:del")
    @LogRequest(module = "att_module", object = "att_leftMenu_schDetails", opType = "att_personSch_delSch",
        requestParams = {"names"}, opContent = "common_op_del")
    @Override
    public ZKResultMsg delete(String ids) {
        attTempSchService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    /*    @Override
    public AttShiftTempSchResultDTO getAttShiftSchJson() {
        return attTempSchService.getAttShiftSchJson(request.getParameter("id"));
    }*/

    @RequiresPermissions("att:personsch:tempSch")
    @LogChangeRequest(module = "att_module", object = "att_personSch_sch", opType = "att_leftMenu_tempSch",
        vo = AttTempSchItem.class, service = AttTempSchService.class)
    @Override
    public ZKResultMsg saveTempSch(AttTempSchItem item) {
        String addIds = request.getParameter("addIds");
        String dXCalendarEvent = request.getParameter("dXCalendarEvent");
        List<AttDXCalendarEventBean> attDXCalendarEventBeanList =
            JSONArray.parseArray(dXCalendarEvent, AttDXCalendarEventBean.class);
        if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(item.getTempType())) {
            addIds = includeLowerDept(addIds);
        }
        attTempSchService.saveTempSch(addIds, item, attDXCalendarEventBeanList);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("att:personsch:cleanTempSch")
    @LogRequest(module = "att_module", object = "att_personSch_sch", opType = "att_personSch_cleanTempSch",
        requestParams = {"names"}, opContent = "att_personSch_cleanTempSch")
    @Override
    public ZKResultMsg delTempSch(String ids, Short type, Date startDate, Date endDate) {
        if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(type)) {
            ids = includeLowerDept(ids);
        }
        attTempSchService.delTempSch(ids, type, startDate, endDate);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    /**
     *
     * 是否包含下级,解决前端部门树懒加载 包含子部门没有成功获取,需要后台递归实现
     *
     * @param ids:
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021-06-11 16:28
     * @since 1.0.0
     */
    private String includeLowerDept(String ids) {
        String includeLower = request.getParameter("includeLower");
        if ("true".equals(includeLower)) {
            List<AuthDepartmentItem> departmentItems = authDepartmentService.getChildDeptItemsByDeptIds(ids);
            ids = Optional.ofNullable(departmentItems).filter(l -> !l.isEmpty())
                .map(l -> l.stream().map(AuthDepartmentItem::getId).filter(Objects::nonNull).map(String::valueOf)
                    .distinct().collect(Collectors.joining(",")))
                .orElse(null);

        }
        return ids;
    }

    @RequiresPermissions("att:personsch:exportTemplate")
    @Override
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        String begin = request.getParameter("startTime");
        String end = request.getParameter("endTime");
        List<AttTempSchItem> attTempSchItemList = new ArrayList<>();
        AttTempSchItem attTempSchItem = new AttTempSchItem();
        List<String> dayList = AttDateUtils.getBetweenDate(begin, end);
        Map map = new LinkedHashMap<String, Object>();
        for (int i = 0; i < dayList.size(); i++) {
            map.put(dayList.get(i), "");
        }
        attTempSchItem.setMap(map);
        attTempSchItemList.add(attTempSchItem);
        excelExport(attTempSchItemList, AttTempSchItem.class, false);
    }

    @RequiresPermissions("att:personsch:import")
    @Override
    public ZKResultMsg importExcel(MultipartFile upload) throws IOException {
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_startProcessing") + "<br/>");
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_uploadFileSuccess") + "<br/>"));
            stringRedisTemplate.opsForValue().set(IMPORT_RESULT + request.getSession().getId(), "start");
            String dynamicRegex = "^\\d{4}(\\-|\\/)\\d{1,2}(\\-|\\/)\\d{1,2}$";
            List<AttTempSchItem> attTempSchItemList = attExcelUtil.excelImportDynamic(upload.getInputStream(),
                AttTempSchItem.class, "map", null, dynamicRegex);
            stringRedisTemplate.opsForValue().set(IMPORT_RESULT + request.getSession().getId(), "end");
            stringRedisTemplate.expire(IMPORT_RESULT + request.getSession().getId(), sessionTimeout, TimeUnit.SECONDS);
            progress += 10;
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_resolutionComplete") + "<br/>"));
            return I18nUtil.i18nMsg(attTempSchService.importExcel(attTempSchItemList));
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                    + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                    new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("Import AttPersonSch Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
    }

    @RequiresPermissions("att:schDetails:export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AttTempSchItem attTempSchItem = new AttTempSchItem();
        setConditionValue(attTempSchItem);

        String queryCondition = request.getParameter("queryConditions");
        String[] conditionAry = queryCondition.replaceAll("%20", " ").split("&");
        for (String condition : conditionAry) {
            if (StringUtils.isNotBlank(condition)) {
                String[] strAry = condition.split("=", 2);
                String fieldName = strAry[0];
                String fieldValue = strAry[1];
                if ("startTime".equals(fieldName)) {
                    attTempSchItem.setStartTime(DateUtil.stringToDate(fieldValue, DateUtil.DateStyle.YYYY_MM_DD));
                } else if ("endTime".equals(fieldName)) {
                    attTempSchItem.setEndTime(DateUtil.stringToDate(fieldValue, DateUtil.DateStyle.YYYY_MM_DD));
                }
            }
        }

        List<AttTempSchItem> attCycleSchItemList =
            attTempSchService.getItemData(request.getSession().getId(), attTempSchItem, getBeginIndex(), getEndIndex());
        excelExport(attCycleSchItemList, AttTempSchItem.class);
    }
}