package com.zkteco.zkbiosecurity.att.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.remote.AttAdmsDevCmdRemote;
import com.zkteco.zkbiosecurity.att.service.AttAdmsDevCmdService;
import com.zkteco.zkbiosecurity.att.vo.AttAdmsDevCmdItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 服务器下发命令
 * 
 * <AUTHOR>
 * @date 2020年4月16日 下午6:05:51
 * @version V1.0
 */
@Controller
public class AttAdmsDevCmdController extends ExportController implements AttAdmsDevCmdRemote {

    @Autowired
    private AttAdmsDevCmdService attAdmsDevCmdService;

    @RequiresPermissions("att:adms:devCmd")
    @Override
    public ModelAndView index() {
        return new ModelAndView("att/admsDevCmd/attAdmsDevCmd");
    }

    @RequiresPermissions("att:adms:devCmd:refresh")
    @Override
    public DxGrid list(AttAdmsDevCmdItem codition) {
        Pager pager = attAdmsDevCmdService.getAttAdmsDevCmdList(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, AttAdmsDevCmdItem.class);
    }

    @RequiresPermissions("att:adms:devCmd:export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AttAdmsDevCmdItem codition = new AttAdmsDevCmdItem();
        setConditionValue(codition);
        List<AttAdmsDevCmdItem> list = attAdmsDevCmdService.export(codition, getBeginIndex(), getEndIndex());
        excelExport(list, AttAdmsDevCmdItem.class);
    }

    @RequiresPermissions("att:adms:devCmd:clearCmd")
    @Override
    public ZKResultMsg clean(String ids) {
        return attAdmsDevCmdService.cleanAttCmd(ids);
    }
}
