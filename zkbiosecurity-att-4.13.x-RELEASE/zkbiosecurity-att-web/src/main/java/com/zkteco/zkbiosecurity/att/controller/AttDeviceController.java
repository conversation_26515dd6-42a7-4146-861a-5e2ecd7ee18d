package com.zkteco.zkbiosecurity.att.controller;

import static com.zkteco.zkbiosecurity.att.constants.AttDeviceConstant.*;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.zkteco.zkbiosecurity.att.vo.AttShortcutKeyInfoItem;
import com.zkteco.zkbiosecurity.base.bean.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.remote.AttDeviceRemote;
import com.zkteco.zkbiosecurity.att.service.AttDeviceOptionService;
import com.zkteco.zkbiosecurity.att.service.AttDeviceService;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.vo.AttDeviceItem;
import com.zkteco.zkbiosecurity.att.vo.AttSearchDeviceItem;
import com.zkteco.zkbiosecurity.base.annotation.LogChangeRequest;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.format.TreeBuilder;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.ClassUtil;
import com.zkteco.zkbiosecurity.core.utils.EmptyUtil;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryValueService;

/**
 * 考勤设备
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:30
 * @since 1.0.0
 */
@Controller
public class AttDeviceController extends ExportController implements AttDeviceRemote {
    @Autowired
    private AttDeviceService attDeviceService;
    @Autowired
    private AttDeviceOptionService attDeviceOptionService;
    @Autowired
    private AttParamService attParamService;
    @Value("${adms.push.port:8088}")
    private int hostPort;
    @Value("${server.port:8098}")
    private int serverPort;
    @Value("${system.filePath:BioSecurityFile}")
    private String systemFilePath;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private BaseDictionaryValueService baseDictionaryValueService;

    @RequiresPermissions("att:device")
    @Override
    public ModelAndView index() {
        return new ModelAndView("att/device/attDevice");
    }

    @RequiresPermissions("att:device:edit")
    @Override
    public ModelAndView edit(String id) {
        String ipAddress = request.getParameter("ipAddress");
        String devSn = request.getParameter("devSn");
        AttDeviceItem attDeviceItem = null;
        if (StringUtils.isNotBlank(id)) {
            attDeviceItem = attDeviceService.getItemById(id);
        } else {
            attDeviceItem = new AttDeviceItem();
            attDeviceItem.setDevName(devSn);// 添加设备时，把sn作为设备名称
            attDeviceItem.setDevSn(devSn);
            attDeviceItem.setIpAddress(ipAddress);
            attDeviceItem.setIsRegDevice(false);
            // 设置默认时区
            attDeviceItem.setTimeZone(DateFormatUtils.format(new Date(), "Z"));
        }
        request.setAttribute("item", attDeviceItem);
        request.setAttribute("editPage", "true");
        return new ModelAndView("att/device/editAttDevice");
    }

    @RequiresPermissions("att:device:edit")
    @LogChangeRequest(module = "att_module", object = "att_leftMenu_device", opType = "common_op_add",
        vo = AttDeviceItem.class, service = AttDeviceService.class)
    @Override
    public ZKResultMsg auth(AttDeviceItem item) {
        // 授权时候兼容旧的代码，添加默认值
        AttDeviceItem attDeviceItem = attDeviceService.getItemBySn(item.getDevSn());
        if (StringUtils.isBlank(item.getId()) && Objects.isNull(attDeviceItem)) {
            // 首次添加
            item.setStatus(true);
        } else {
            if (Objects.nonNull(attDeviceItem) && StringUtils.isBlank(item.getId())) {
                item.setId(attDeviceItem.getId());
            }
            // 更新，设备名称、区域、是否登记机
            attDeviceService.updateEditInfo(item.getId(), item.getDevName(), item.getAreaId(), item.getTimeZone(),
                item.getIsRegDevice());
        }
        // 以上参数系统默认，modified by max 20180515
        ZKResultMsg res = attDeviceService.authDevice(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("att:device:refresh")
    @Override
    public DxGrid list(AttDeviceItem condition) {
        Pager pager = attDeviceService.getItemsByAuthUserPage(request.getSession().getId(), condition, getPageNo(),
            getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:device:del")
    @LogRequest(module = "att_module", object = "att_leftMenu_device", opType = "common_op_del",
        requestParams = {"names"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg delete(String ids) {
        attDeviceService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    // 启用
    @RequiresPermissions("att:device:enable")
    @LogRequest(module = "att_module", object = "att_leftMenu_device", opType = "common_enable",
        requestParams = {"names"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg enable(String ids) {
        ZKResultMsg zKResultMsg = attDeviceService.enable(ids);
        return I18nUtil.i18nMsg(zKResultMsg);
    }

    // 禁用
    @RequiresPermissions("att:device:disable")
    @LogRequest(module = "att_module", object = "att_leftMenu_device", opType = "common_disable",
        requestParams = {"names"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg disable(String ids) {
        ZKResultMsg zKResultMsg = attDeviceService.disable(ids);
        return I18nUtil.i18nMsg(zKResultMsg);
    }

    // 同步软件数据到设备
    @LogRequest(module = "att_module", object = "att_leftMenu_device", opType = "att_op_syncDev",
        requestParams = {"names"}, opContent = "common_dev_name")
    @RequiresPermissions("att:device:syncdev")
    @Override
    public ZKResultMsg syncDev(String ids, String isSyncClearData) {
        ZKResultMsg zKResultMsg = attDeviceService.syncDev(ids, isSyncClearData);
        return I18nUtil.i18nMsg(zKResultMsg);
    }

    // 考勤数据校对页面跳转
    @RequiresPermissions("att:device:settime")
    @Override
    public ModelAndView setTime(String ids) {
        request.setAttribute("ids", ids);
        return new ModelAndView("att/device/setTime");
    }

    // 考勤数据校对
    @LogRequest(module = "att_module", object = "att_leftMenu_device", opType = "att_op_account",
        requestParams = {"startTime", "endTime"}, opContent = "att_op_account")
    @Override
    public ZKResultMsg verify(String ids) {
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        ZKResultMsg zKResultMsg = attDeviceService.verify(ids, startTime, endTime);
        return I18nUtil.i18nMsg(zKResultMsg);
    }

    // 获取指定人员数据页面跳转
    @RequiresPermissions("att:device:fillpin")
    @Override
    public ModelAndView fillPin(String ids) {
        request.setAttribute("ids", ids);
        return new ModelAndView("att/device/setFillPin");
    }

    // 获取指定人员数据
    @Override
    public ZKResultMsg getPersonInfo(String ids) {
        String pin = request.getParameter("pin");
        ZKResultMsg zKResultMsg = attDeviceService.getPersonInfo(ids, pin);
        return I18nUtil.i18nMsg(zKResultMsg);
    }

    // 重新上传数据页面跳转
    @RequiresPermissions("att:device:setstamp")
    @Override
    public ModelAndView setStamp(String ids) {
        request.setAttribute("ids", ids);
        boolean isRegDevice = attDeviceService.checkExitIsRegDevice(ids);
        request.setAttribute("isRegDevice", isRegDevice);
        return new ModelAndView("att/device/setStamp");
    }

    // 重新上传数据
    @LogRequest(module = "att_module", object = "att_leftMenu_device", opType = "att_op_check", requestParams = {"ids"},
        opContent = "att_common_deviceId")
    @Override
    public ZKResultMsg reUpload(String ids) {
        String isUploadAttLog = request.getParameter("isUploadAttLog") == null ? "0" : "1";
        String isUploadOperLog = request.getParameter("isUploadOperLog") == null ? "0" : "1";
        String isUploadAttPhoto = request.getParameter("isUploadAttPhoto") == null ? "0" : "1";
        ZKResultMsg zKResultMsg = attDeviceService.reUpload(ids, isUploadAttLog, isUploadOperLog, isUploadAttPhoto);
        return I18nUtil.i18nMsg(zKResultMsg);
    }

    // 清除设备命令
    @RequiresPermissions("att:device:deletecmd")
    @LogRequest(module = "att_module", object = "att_leftMenu_device", opType = "att_op_deleteCmd",
        requestParams = {"names"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg deleteCmd(String ids) {
        ZKResultMsg zKResultMsg = attDeviceService.deleteCmd(ids);
        return I18nUtil.i18nMsg(zKResultMsg);
    }

    // 对公短消息页面跳转
    @RequiresPermissions("att:device:setsms")
    @Override
    public ModelAndView setSms(String ids) {
        request.setAttribute("ids", ids);
        request.setAttribute("editPage", "true");
        return new ModelAndView("att/device/setSms");
    }

    // 对公短消息
    @LogRequest(module = "att_module", object = "att_leftMenu_device", opType = "att_op_dataSms",
        requestParams = {"msg"}, opContent = "att_common_msg")
    @Override
    public ZKResultMsg addSms(String ids) {
        String startTime = request.getParameter("startTime");
        String msg = request.getParameter("msg");
        String min = request.getParameter("min");
        ZKResultMsg zKResultMsg = attDeviceService.addSms(ids, startTime, msg, min);
        return I18nUtil.i18nMsg(zKResultMsg);
    }

    // 清除考勤照片
    @RequiresPermissions("att:device:clearattpic")
    @LogRequest(module = "att_module", object = "att_leftMenu_device", opType = "att_op_clearAttPic",
        requestParams = {"names"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg clearAttPic(String ids) {
        ZKResultMsg zKResultMsg = attDeviceService.clearAttPic(ids);
        return I18nUtil.i18nMsg(zKResultMsg);
    }

    // 清除考勤记录
    @RequiresPermissions("att:device:clearattlog")
    @LogRequest(module = "att_module", object = "att_leftMenu_device", opType = "att_op_clearAttLog",
        requestParams = {"names"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg clearAttLog(String ids) {
        ZKResultMsg zKResultMsg = attDeviceService.clearAttLog(ids);
        return I18nUtil.i18nMsg(zKResultMsg);
    }

    @Override
    @RequiresPermissions("att:device:clearattpers")
    @LogRequest(module = "att_module", object = "att_leftMenu_device", opType = "att_op_clearAttPers",
        requestParams = {"names"}, opContent = "common_dev_name")
    public ZKResultMsg clearAttPers(@RequestParam(value = "ids") String ids) {
        ZKResultMsg zKResultMsg = attDeviceService.clearAttPers(ids);
        return I18nUtil.i18nMsg(zKResultMsg);
    }

    // 重启设备
    @RequiresPermissions("att:device:reboot")
    @LogRequest(module = "att_module", object = "att_leftMenu_device", opType = "common_dev_reboot",
        requestParams = {"names"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg reboot(String ids) {
        ZKResultMsg zKResultMsg = attDeviceService.reboot(ids);
        return I18nUtil.i18nMsg(zKResultMsg);
    }

    // 获取设备参数
    @RequiresPermissions("att:device:getdevopt")
    @Override
    public ZKResultMsg getDevOpt(String ids) {
        ZKResultMsg zKResultMsg = attDeviceService.getDevOpt(ids);
        return I18nUtil.i18nMsg(zKResultMsg);
    }

    // 设备名称验证
    @Override
    public String devNameVaild() {
        String devName = request.getParameter("devName");
        boolean ret = attDeviceService.vaildName(devName);
        return ret + "";
    }

    // 设备序列号验证
    @Override
    public String devSnVaild() {
        String devSn = request.getParameter("devSn");
        boolean ret = attDeviceService.vaildSn(devSn);
        return ret + "";
    }

    // 设备Ip验证
    @Override
    public String devIpVaild() {
        String ipAddress = request.getParameter("ipAddress");
        boolean ret = attDeviceService.vaildIpAdress(ipAddress);
        return ret + "";
    }

    @Override
    public ZKResultMsg checkDeviceStatus(String ids) {
        if (!attDeviceService.checkDeviceStatus(ids)) {
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("att_device_disabledOrOffline"));
        }
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @Override
    public ZKResultMsg checkExitIsRegDevice(String ids) {
        if (!attDeviceService.checkDeviceStatus(ids)) {// 判断是否在线的问题
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("att_device_disabledOrOffline"));
        } else {
            ZKResultMsg resultMsg = new ZKResultMsg();
            if (attDeviceService.checkExitIsRegDevice(ids)) {
                // 全部是登记机
                resultMsg.setRet("isReg");
            } else {
                resultMsg.setRet("noIsReg");
            }
            return resultMsg;
        }
    }

    // 搜索设备
    @RequiresPermissions("att:device:search")
    @LogRequest(module = "att_module", object = "common_leftMenu_device", opType = "common_dev_searchDev",
        opContent = "common_dev_searchDev")
    @Override
    public ZKResultMsg searchDev(Long nowTime) {
        Map<String, Object> devInfo = new HashMap<>();
        List<AttSearchDeviceItem> devcieItems = attDeviceService.searchDeviceList();
        devInfo.put("devCount", devcieItems.size());
        devInfo.put("devData", devcieItems);
        devInfo.put("nowTime", nowTime);
        return I18nUtil.i18nMsg(new ZKResultMsg(devInfo));
    }

    // 获取设备Ip
    @Override
    public ZKResultMsg getAllIPSn() {
        return I18nUtil.i18nMsg(new ZKResultMsg(attDeviceService.getAllIPSn()));
    }

    @RequiresPermissions("att:device:queryOption")
    @Override
    public ZKResultMsg queryDeviceOption(String devId) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        JSONArray jsonArray = null;
        Map<String, String> optionMap = attDeviceOptionService.getOptionMapByDevId(devId);
        if (optionMap.isEmpty())// 没有参数，直接返回
        {
            return resultMsg;
        }
        // 如果包含这两个参数(值不可能为“”)，则按新协议解析 -- add by hook.fang 2020-03-05
        if (optionMap.containsKey("MultiBioDataCount") || optionMap.containsKey("MultiBioPhotoCount")) {
            jsonArray = buildDevOptionInfoExp(optionMap);
        } else {
            jsonArray = buildDevOptionInfo(optionMap);
        }
        resultMsg.setData(jsonArray);
        return resultMsg;
    }

    /**
     * 新协议：组装设备参数
     */
    private JSONArray buildDevOptionInfoExp(Map<String, String> optionMap) {
        String multiBioDataSupport = optionMap.get("MultiBioDataSupport");
        String[] bioDataSupportAry = multiBioDataSupport.split(":");
        String multiBioPhotoSupport = optionMap.get("MultiBioPhotoSupport");
        String[] bioPhotoSupportAry = multiBioPhotoSupport.split(":");
        String multiBioDataCount = optionMap.get("MultiBioDataCount");
        String[] bioDataCountAry = multiBioDataCount.split(":");
        String maxMultiBioDataCount = optionMap.get("MaxMultiBioDataCount");
        String[] maxBioDataCountAry = maxMultiBioDataCount.split(":");
        String multiBioPhotoCount = optionMap.get("MultiBioPhotoCount");
        String[] bioPhotoCountAry = multiBioPhotoCount.split(":");
        String maxMultiBioPhotoCount = optionMap.get("MaxMultiBioPhotoCount");
        String[] maxBioPhotoCountAry = maxMultiBioPhotoCount.split(":");
        String bioVersion = optionMap.get("MultiBioVersion");
        String[] bioVersionAry = bioVersion.split(":");

        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject = null;
        int i = 0;
        while (i < NEW_DEVICE_OPTION_NAME.size()) {
            String optionName = null;
            String optionValue = null;
            String optionKey1 = "";
            String optionKey2 = "";
            String optionKey = "";
            String index = "";
            jsonObject = new JSONObject();
            switch (i) {
                case 0:
                case 8:
                    optionKey1 = NEW_DEVICE_OPTION_NAME.get(i);
                    optionKey2 = NEW_DEVICE_OPTION_NAME.get(i + 1);
                    optionName = I18nUtil.i18nCode(NEW_DEVICE_OPTION_I18N.get(optionKey1));
                    // optionName = I18nUtil.i18nCode(NEW_DEVICE_OPTION_I18N.get(optionKey1)) + "/"
                    // + I18nUtil.i18nCode(NEW_DEVICE_OPTION_I18N.get(optionKey2));
                    String keyContent = optionMap.get(optionKey2);
                    if (optionKey2.contains("AttLog")) {
                        // 当前记录数/最大记录数
                        optionValue = optionMap.get(optionKey1) + "/"
                            + Integer.parseInt("".equals(keyContent) ? "0" : keyContent) * 10000;
                    } else {
                        // 当前用户数/最大用户数
                        optionValue = optionMap.get(optionKey1) + "/"
                            + Integer.parseInt("".equals(keyContent) ? "0" : keyContent) * 100;
                    }
                    jsonObject.put("optionName", optionName);
                    jsonObject.put("optionValue", optionValue);
                    jsonArray.add(jsonObject);
                    i = i + 2;
                    break;
                case 10:// 24
                case 16:
                case 17:
                    // 固件版本、push版本
                    optionKey = NEW_DEVICE_OPTION_NAME.get(i);
                    optionName = I18nUtil.i18nCode(NEW_DEVICE_OPTION_I18N.get(optionKey));
                    optionValue = optionMap.get(optionKey);
                    jsonObject.put("optionName", optionName);
                    jsonObject.put("optionValue", optionValue);
                    jsonArray.add(jsonObject);
                    i++;
                    break;
                case 2:
                case 3:
                case 5:
                case 6:
                case 7:
                case 18:
                case 19:
                    // 生物模板数量(近红外人脸/可见光人脸/指纹/指静脉/掌静脉)
                    optionKey1 = NEW_DEVICE_OPTION_NAME.get(i);
                    index = optionKey1.split("_")[1];
                    if (StringUtils.isNotBlank(index)) {
                        Integer bioDataType = Integer.parseInt(index);
                        if (bioDataType < bioDataSupportAry.length && "1".equals(bioDataSupportAry[bioDataType])) {
                            optionName = I18nUtil.i18nCode(NEW_DEVICE_OPTION_I18N.get(optionKey1));
                            optionValue = bioDataCountAry[bioDataType] + "/" + maxBioDataCountAry[bioDataType];
                            jsonObject.put("optionName", optionName);
                            jsonObject.put("optionValue", optionValue);
                            jsonArray.add(jsonObject);
                        }
                        // 生物图像数量(可见光比对照片)
                        if (3 == i) {
                            if (bioDataType < bioPhotoSupportAry.length
                                && "1".equals(bioPhotoSupportAry[bioDataType])) {
                                jsonObject = new JSONObject();
                                optionKey1 = NEW_DEVICE_OPTION_NAME.get(i + 1);
                                optionName = I18nUtil.i18nCode(NEW_DEVICE_OPTION_I18N.get(optionKey1));
                                optionValue = bioPhotoCountAry[bioDataType] + "/" + maxBioPhotoCountAry[bioDataType];
                                jsonObject.put("optionName", optionName);
                                jsonObject.put("optionValue", optionValue);
                                jsonArray.add(jsonObject);
                            }
                            i++;
                        }
                    }
                    i++;
                    break;
                case 11:
                case 12:
                case 13:
                case 14:
                case 15:
                case 20:
                case 21:
                   /* if (11 == i) {
                        jsonObject.put("optionName", I18nUtil.i18nCode("pers_person_templateVersion") + ":");
                        jsonObject.put("optionValue", "");
                        jsonArray.add(jsonObject);
                    }*/
                    // 近红外人脸/可见光人脸/指纹/指静脉/手掌算法版本
                    optionKey = NEW_DEVICE_OPTION_NAME.get(i);
                    index = optionKey.split("_")[1];
                    if (StringUtils.isNotBlank(index)) {
                        Integer bioDataType = Integer.parseInt(index);
                        if (bioDataType < bioVersionAry.length && !"0".equals(bioVersionAry[bioDataType])) {
                            optionName = I18nUtil.i18nCode(NEW_DEVICE_OPTION_I18N.get(optionKey));
                            optionValue = bioVersionAry[bioDataType];
                            jsonObject = new JSONObject();
                            jsonObject.put("optionName", optionName);
                            jsonObject.put("optionValue", optionValue);
                            jsonArray.add(jsonObject);
                        }
                    }
                    i++;
                    break;
                default:
                    break;
            }
        }
        return jsonArray;
    }

    /**
     * 旧协议：组装设备参数
     */
    private JSONArray buildDevOptionInfo(Map<String, String> optionMap) {
        String userCount = optionMap.get("UserCount");
        String maxUserCount = optionMap.get("~MaxUserCount");
        String faceCount = optionMap.get("FaceCount");
        String maxFaceCount = optionMap.get("~MaxFaceCount");
        String fPCount = optionMap.get("FPCount");
        String maxFingerCount = optionMap.get("~MaxFingerCount");
        String fvCount = optionMap.get("FvCount");
        String maxFvCount = optionMap.get("~MaxFvCount");
        String pvCount = optionMap.get("PvCount");
        String maxPvCount = optionMap.get("~MaxPvCount");
        String transactionCount = optionMap.get("TransactionCount");
        String maxAttLogCount = optionMap.get("~MaxAttLogCount");
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject = null;
        int i = 0;
        while (i < ATT_DEVICE_OPTION_SHOWLIST.size()) {
            String optionKey = ATT_DEVICE_OPTION_SHOWLIST.get(i);
            String optionName = I18nUtil.i18nCode(ATT_DEVICE_OPTION_I18N.get(optionKey));
            String optionValue = null;
            jsonObject = new JSONObject();
            if (i < 6) {
                switch (i) {
                    case 0:// 用户数 最大用户数单位百
                        optionValue =
                            userCount + "/" + Integer.parseInt("".equals(maxUserCount) ? "0" : maxUserCount) * 100;
                        break;
                    case 1:// 面部数 最大面部数单位个
                        optionValue = faceCount + "/" + maxFaceCount;
                        break;
                    case 2:// 指纹数 最大指纹数单位百
                        optionValue =
                            fPCount + "/" + Integer.parseInt("".equals(maxFingerCount) ? "0" : maxFingerCount) * 100;
                        break;
                    case 3:// 指静脉数 最大指静脉数单位百
                        optionValue = fvCount + "/" + Integer.parseInt("".equals(maxFvCount) ? "0" : maxFvCount) * 100;
                        break;
                    case 4:// 掌静脉数 最大掌静脉数单位个
                        optionValue = pvCount + "/" + maxPvCount;
                        break;
                    case 5:// 打卡数据 最大考勤记录数，单位万
                        optionValue = transactionCount + "/"
                            + Integer.parseInt("".equals(maxAttLogCount) ? "0" : maxAttLogCount) * 10000;
                        break;
                    default:
                        break;
                }
            } else {
                if (i == 7) {
                    // 增加生物模板分割
                    JSONObject templateVersion = new JSONObject();
                    templateVersion.put("optionName", I18nUtil.i18nCode("pers_person_templateVersion") + ":");
                    templateVersion.put("optionValue", "");
                    jsonArray.add(templateVersion);
                }
                optionValue = optionMap.get(optionKey);
            }
            jsonObject.put("optionName", optionName);
            jsonObject.put("optionValue", optionValue);
            jsonArray.add(jsonObject);
            i++;
        }
        return jsonArray;
    }

    @RequiresPermissions("att:device:search")
    @Override
    public ModelAndView getSearchDevInfo() {
        request.setAttribute("hostPort", hostPort);
        request.setAttribute("autoAdd", attParamService.getAutoAddDevice());
        request.setAttribute("receivePersonOnlyDb", attParamService.getReceivePersonOnlyDb());
        return new ModelAndView("att/device/opAttSearchDev");
    }

    @RequiresPermissions("att:device:authArea")
    @Override
    public ZKResultMsg authArea(String authAreaId, String deviceIds) {
        return attDeviceService.authArea(authAreaId, deviceIds);
    }

    @RequiresPermissions("att:device:export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AttDeviceItem attDeviceItem = new AttDeviceItem();
        setConditionValue(attDeviceItem);
        List<AttDeviceItem> itemList = attDeviceService.loadListByAuthFilter(attDeviceItem, getBeginIndex(),
            getEndIndex(), request.getSession().getId());
        Map<String, Map<String, String>> map = new HashMap<>();
        Map<String, String> isRegDevice = new HashMap<>();
        isRegDevice.put("true", I18nUtil.i18nCode("common_yes"));
        isRegDevice.put("false", I18nUtil.i18nCode("common_no"));
        map.put("isRegDevice", isRegDevice);
        Map<String, String> onlineStatus = new HashMap<>();
        onlineStatus.put("0", I18nUtil.i18nCode("common_offline"));
        onlineStatus.put("1", I18nUtil.i18nCode("common_online"));
        onlineStatus.put("2", I18nUtil.i18nCode("common_commStatus_disable"));
        map.put("onlineStatus", onlineStatus);
        excelExport(itemList, AttDeviceItem.class, map);
    }

    @RequiresPermissions("att:device:upgradeFirmware")
    @Override
    public ModelAndView getDevInfoWithUpgradeFirmware(String ids) {
        List<AttDeviceItem> deviceItemList = attDeviceService.getItemsByIds(Arrays.asList(ids.split(",")));
        StringBuffer onlineOldProtocolDevName = new StringBuffer();
        StringBuffer onlineNewProtocolDevName = new StringBuffer();
        StringBuffer disabledDevName = new StringBuffer();
        StringBuffer offlineDevName = new StringBuffer();
        AttDeviceItem dev;
        StringBuffer oldProtocolDeviceIds = new StringBuffer();
        StringBuffer newProtocolDeviceIds = new StringBuffer();
        for (int i = 0; i < deviceItemList.size(); i++) {
            dev = deviceItemList.get(i);
            // 在线
            if ("1".equals(attDeviceService.getDevStatus(dev.getDevSn()))) {
                // 根据此参数区分新旧协议 没有该参数为旧协议
                if (!attDeviceOptionService.isSupportFunction(dev.getId(), "SubcontractingUpgradeFunOn")) {
                    oldProtocolDeviceIds.append(dev.getId()).append(",");
                    onlineOldProtocolDevName.append(dev.getDevName()).append("-").append(dev.getDevModel()).append(";");
                } else {
                    newProtocolDeviceIds.append(dev.getId()).append(",");
                    onlineNewProtocolDevName.append(dev.getDevName()).append("-").append(dev.getDevModel()).append(";");
                }
            } else {
                // 禁用
                if ("2".equals(attDeviceService.getDevStatus(dev.getDevSn()))) {
                    disabledDevName.append(dev.getDevName()).append("-").append(dev.getDevModel()).append(",");
                } else {
                    // 离线
                    offlineDevName.append(dev.getDevName()).append("-").append(dev.getDevModel()).append(",");
                }
            }
        }
        request.setAttribute("oldProtocolDeviceIds", oldProtocolDeviceIds.toString().equals("") ? ""
            : oldProtocolDeviceIds.substring(0, oldProtocolDeviceIds.length() - 1));
        request.setAttribute("newProtocolDeviceIds", newProtocolDeviceIds.toString().equals("") ? ""
            : newProtocolDeviceIds.substring(0, newProtocolDeviceIds.length() - 1));
        request.setAttribute("onlineOldProtocolDevName", onlineOldProtocolDevName.toString().equals("") ? ""
            : onlineOldProtocolDevName.substring(0, onlineOldProtocolDevName.length() - 1));
        request.setAttribute("onlineNewProtocolDevName", onlineNewProtocolDevName.toString().equals("") ? ""
            : onlineNewProtocolDevName.substring(0, onlineNewProtocolDevName.length() - 1));
        request.setAttribute("disabledDevName",
            disabledDevName.toString().equals("") ? "" : disabledDevName.substring(0, disabledDevName.length() - 1));
        request.setAttribute("offlineDevName",
            offlineDevName.toString().equals("") ? "" : offlineDevName.substring(0, offlineDevName.length() - 1));
        return new ModelAndView("att/device/opAttUpgradeFirmware");
    }

    @RequiresPermissions("att:device:upgradeFirmware")
    @Override
    public ZKResultMsg upgradeFirmware(String devIds, MultipartFile devFile) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        if (StringUtils.isNotBlank(devIds)) {
            String ret = "ok";
            try {
                progressCache.beginProcess(I18nUtil.i18nCode("common_op_startProcessing") + "<br/>");
                List<AttDeviceItem> deviceItemList = attDeviceService.getItemsByIds(Arrays.asList(devIds.split(",")));
                progressCache.setProcess(new ProcessBean(100, 0,
                    "<font color='black'>" + I18nUtil.i18nCode("att_device_transferFilesTip") + "...</font><br/>"));// 第二次表单提交会刷新进度条，故这里再加入提示。
                File file = null;
                if (devFile != null) {
                    file = saveFile(devFile, "emfw.cfg");
                }
                progressCache.setProcess(new ProcessBean(100, 45,
                    "<font color='black'>" + I18nUtil.i18nCode("common_dev_fileUploadSuccess") + "...</font><br/>"));// 数据处理进度初始化
                List<Long> cmdIdList = attDeviceService.upgradeFirmware(devIds, file, "localhost", serverPort);
                progressCache.setProcess(
                    new ProcessBean(100, 50, "<font color='black'>" + I18nUtil.i18nCode("common_dev_cmdSendSucceed")
                        + "," + I18nUtil.i18nCode("common_dev_upgrade") + "...</font><br/>"));
                // 判断是否执行成功
                List<String> devIdList = new ArrayList<>();// 执行成功需要重启的设备ID list
                for (int index = 0; index < cmdIdList.size(); index++) {
                    long cmdId = cmdIdList.get(index);
                    Map<String, String> resultMap = attDeviceService.getCmdResultById(cmdId, 1800);
                    if (Objects.nonNull(resultMap)) {
                        int total = (50 / deviceItemList.size()) * (index + 1) + 30;
                        Integer result = Integer.parseInt(resultMap.get("result"));
                        AttDeviceItem dev = attDeviceService.getItemBySn(resultMap.get("sn"));
                        if (result >= 0) {
                            progressCache.setProcess(new ProcessBean(70, total,
                                "<font color='black'>" + dev.getDevName() + ":"
                                    + I18nUtil.i18nCode("common_dev_upgradeSuccess") + ","
                                    + I18nUtil.i18nCode("common_dev_reboot") + "...</font><br/>"));
                            devIdList.add(dev.getId());
                        } else {
                            String failedInfo = getCommReason(result);
                            failedInfo =
                                StringUtils.isNotBlank(failedInfo) ? I18nUtil.i18nCode(failedInfo) : result.toString();
                            progressCache.setProcess(new ProcessBean(total + 20, total + 20, "<font color='red'>"
                                + dev.getDevName() + ":" + I18nUtil.i18nCode("common_dev_upgradeFail") + ","
                                + I18nUtil.i18nCode("common_dev_errorCode") + ":" + failedInfo + "...</font><br/>"));
                        }
                    }
                }
                if (devIdList.size() > 0) {// 执行成功重启设备
                    attDeviceService.reboot(StringUtils.join(devIdList, ","));
                }
            } catch (Exception e) {
                ret = "error";
                resultMsg.setRet(ret);
                resultMsg.setMsg("common_dev_upgradeFail");
            } finally {
                if (ret.equals("error")) {
                    progressCache.finishProcess(
                        "<font color='red'>" + I18nUtil.i18nCode("common_dev_upgradeFail") + "...</font><br/>");
                } else {
                    progressCache.finishProcess(
                        "<font color='black'>" + I18nUtil.i18nCode("common_progress_finish") + "</font><br/>");
                }
            }
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    private File saveFile(MultipartFile devFile, String fileName) throws Exception {
        String realPath = systemFilePath + AttConstant.ATT_FIREWARE_PATH;
        File tempFile = new File(realPath);
        if (!tempFile.isAbsolute()) {
            realPath = ClassUtil.getRootPath() + "/" + realPath;
            tempFile = new File(realPath);
        }
        if (!tempFile.exists()) {
            tempFile.mkdirs();
        }
        File file = new File(realPath + "/" + fileName);
        if (file.exists()) {
            file.delete();
        }
        devFile.transferTo(file);
        return file;
    }

    private String getCommReason(int ret) {
        Map<String, String> commStatus = baseDictionaryValueService.getDictionaryValuesMap("commStatus");
        return commStatus.containsKey(String.valueOf(ret)) ? commStatus.get(String.valueOf(ret)) : "";
    }

    @Override
    public TreeItem tree() {
        List<AttDeviceItem> attDeviceItemList = attDeviceService.getByCondition(new AttDeviceItem());
        List<TreeItem> items = new ArrayList<>();
        TreeItem pItem = new TreeItem("0");
        TreeItem item = null;
        for (AttDeviceItem attDeviceItem : attDeviceItemList) {
            item = new TreeItem();
            item.setId(attDeviceItem.getId());
            item.setText(attDeviceItem.getDevName());
            item.setParent(pItem);
            items.add(item);
        }
        List<TreeItem> treeItems = TreeBuilder.newTreeBuilder(TreeItem.class, String.class).buildToTreeList(items);
        return new TreeItem("0", treeItems);
    }

    @Override
    public ModelAndView opAttSetShortcutKey(String ids) {
        request.setAttribute("ids", ids);
        List<AttDeviceItem> list = attDeviceService.getItemsByIds(Arrays.asList(ids.split(",")));
        String names = list.stream().map(AttDeviceItem::getDevName).filter(Objects::nonNull).map(String::valueOf)
            .distinct().collect(Collectors.joining(","));
        request.setAttribute("names", names);
        return new ModelAndView("att/device/opAttSetShortcutKey");
    }

    @RequiresPermissions("att:device:setShortcutKey")
    @LogRequest(module = "att_module", object = "att_leftMenu_device", opType = "att_cardStatus_setting",
        requestParams = {"names"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg setShortcutKey(AttShortcutKeyInfoItem attShortcutKeyInfoItem) {
        return attDeviceService.setShortcutKey(attShortcutKeyInfoItem);
    }

    @Override
    public ZKResultMsg getShortcutKeyName() {
        Map<String, String> shortcutKeyNameMap = attDeviceService.getShortcutKeyNameMap();
        List<SelectItem> items = new ArrayList<>();
        for (Map.Entry<String, String> entry : shortcutKeyNameMap.entrySet()) {
            SelectItem selectItem = new SelectItem();
            selectItem.setValue(entry.getKey());
            selectItem.setText(entry.getValue());
            items.add(selectItem);
        }
        return new ZKResultMsg(items);
    }
}