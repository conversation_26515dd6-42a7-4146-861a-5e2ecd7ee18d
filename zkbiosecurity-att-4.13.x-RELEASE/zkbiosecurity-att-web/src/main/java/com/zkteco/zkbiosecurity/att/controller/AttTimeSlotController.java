package com.zkteco.zkbiosecurity.att.controller;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.remote.AttTimeSlotRemote;
import com.zkteco.zkbiosecurity.att.service.AttRuleService;
import com.zkteco.zkbiosecurity.att.service.AttTimeSlotService;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotBreakTimeItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import com.zkteco.zkbiosecurity.base.annotation.LogChangeRequest;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 时间段
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:52
 * @since 1.0.0
 */
@Controller
public class AttTimeSlotController extends BaseController implements AttTimeSlotRemote {

    @Autowired
    private AttTimeSlotService attTimeSlotService;

    @Autowired
    private AttRuleService attRuleService;

    @RequiresPermissions("att:timeSlot")
    @Override
    public ModelAndView index() {
        return new ModelAndView("att/timeSlot/attTimeSlot");
    }

    @RequiresPermissions("att:timeSlot:edit")
    @Override
    public ModelAndView edit(String id) {
        String periodType = request.getParameter("periodType");
        if (StringUtils.isNotBlank(id)) {
            AttTimeSlotItem item = attTimeSlotService.getItemById(id);
            periodType = item.getPeriodType() + "";
            request.setAttribute("item", item);
        }
        Map<String, String> attParams = attRuleService.getAttParams();
        request.setAttribute("attParams", attParams);
        // 前端弹性和正常时间段编辑
        if ("0".equals(periodType)) {
            return new ModelAndView("att/timeSlot/editAttTimeSlot");
        } else {
            return new ModelAndView("att/timeSlot/editAttTimeSlotElastic");
        }
    }

    @RequiresPermissions("att:timeSlot:edit")
    @LogChangeRequest(module = "att_module", object = "att_leftMenu_timeSlot", opType = "common_op_edit",
        vo = AttTimeSlotItem.class, service = AttTimeSlotService.class)
    @Override
    public ZKResultMsg save(AttTimeSlotItem item) {
        attTimeSlotService.saveItem(item);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @RequiresPermissions("att:timeSlot:refresh")
    @Override
    public DxGrid list(AttTimeSlotItem condition) {
        Pager pager = attTimeSlotService.getItemsByPage(condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:timeSlot:del")
    @LogRequest(module = "att_module", object = "att_leftMenu_timeSlot", opType = "common_op_del",
        requestParams = {"names"}, opContent = "common_name")
    @Override
    public ZKResultMsg delete(String ids) {
        // 判断 删除的数据是否存在外键关联
        if (attTimeSlotService.isExistFkData(ids)) {
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("att_common_relationDataCanNotDel"));
        }
        attTimeSlotService.deleteByIds(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    // @Override
    // public String validNo(String periodNo) {
    // boolean rs = !attTimeSlotService.existsByPeriodNo(periodNo);
    // return rs + "";
    // }

    @Override
    public String validName(String periodName) {
        boolean rs = !attTimeSlotService.existsByPeriodName(periodName);
        return rs + "";
    }

    @Override
    public DxGrid breakTimeList(AttTimeSlotBreakTimeItem condition) {
        Pager pager = attTimeSlotService.findBreakTimeListByTimeSlotId(condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public ZKResultMsg addBreakTime(String timeSlotId, String breakTimeIds) {
        String result = attTimeSlotService.addBreakTime(timeSlotId, breakTimeIds);
        return I18nUtil.i18nMsg(new ZKResultMsg(result));
    }

    @Override
    public ZKResultMsg delBreakTime(String timeSlotId, String breakTimeIds) {
        attTimeSlotService.delBreakTime(timeSlotId, breakTimeIds);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @Override
    public ZKResultMsg countSegmentTime(String timeSlotId, String breakTimeArray) {
        int timeLong = attTimeSlotService.countSegmentTime(timeSlotId, breakTimeArray);
        return new ZKResultMsg(timeLong);
    }

    @Override
    public ZKResultMsg timeJudgment(String timeSlotId, String toWorkTime, String offWorkTime) {
        String result = attTimeSlotService.timeJudgment(timeSlotId, toWorkTime, offWorkTime);
        return I18nUtil.i18nMsg(new ZKResultMsg(result));
    }

    @Override
    public ZKResultMsg breakTimeIsInWorkTime(String breakTimeListStr, String toWorkTime, String offWorkTime) {
        String result = attTimeSlotService.breakTimeIsInWorkTime(breakTimeListStr, toWorkTime, offWorkTime);
        return I18nUtil.i18nMsg(new ZKResultMsg(result));
    }

    @Override
    public ZKResultMsg getNormalList() {
        AttTimeSlotItem attTimeSlotItem = new AttTimeSlotItem();
        attTimeSlotItem.setPeriodType(AttConstant.PERIODTYPE_NORMAL);
        List<AttTimeSlotItem> attTimeSlotItemList = attTimeSlotService.getByCondition(attTimeSlotItem);
        return new ZKResultMsg(attTimeSlotItemList);
    }

    @Override
    public TreeItem getTree() {
        return attTimeSlotService.getTree();
    }

    @Override
    public ZKResultMsg getAllList() {
        List<AttTimeSlotItem> attTimeSlotItemList = attTimeSlotService.getByCondition(new AttTimeSlotItem());
        return new ZKResultMsg(attTimeSlotItemList);
    }
}