package com.zkteco.zkbiosecurity.att.controller;

import com.zkteco.zkbiosecurity.att.service.AttCustomReportService;
import com.zkteco.zkbiosecurity.att.service.AttDayDetailReportService;
import com.zkteco.zkbiosecurity.att.service.AttDeptStatisticalReportService;
import com.zkteco.zkbiosecurity.att.service.AttMonthStatisticalReportService;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.system.service.BaseCustomReportService;
import com.zkteco.zkbiosecurity.system.vo.BaseCustomReportFieldItem;
import com.zkteco.zkbiosecurity.system.vo.BaseCustomReportItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.remote.AttCustomReportRemote;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 考勤自定义报表
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 10:25 2023/7/18
 * @version v1.0
 */
@Controller
public class AttCustomReportController extends BaseController implements AttCustomReportRemote {

    @Autowired
    private AttCustomReportService attCustomReportService;
    @Autowired
    private BaseCustomReportService baseCustomReportService;
    @Autowired
    private AttDayDetailReportService attDayDetailReportService;
    @Autowired
    private AttMonthStatisticalReportService attMonthStatisticalReportService;
    @Autowired
    private AttDeptStatisticalReportService attDeptStatisticalReportService;

    @RequiresPermissions("att:customReport")
    @Override
    public ModelAndView index() {
        return new ModelAndView("att/customReport/attCustomReport");
    }

    @RequiresPermissions("att:customReport:add")
    @Override
    public ModelAndView edit(String id) {

        BaseCustomReportItem baseCustomReportItem = null;
        if (StringUtils.isNotBlank(id)) {
            baseCustomReportItem = baseCustomReportService.getItemById(id);
        }
        if (baseCustomReportItem == null) {
            baseCustomReportItem = new BaseCustomReportItem();
            baseCustomReportItem.setModuleCode(ConstUtil.SYSTEM_MODULE_ATT);
        }
        request.setAttribute("item", baseCustomReportItem);

        // 自定义类型
        List<SelectItem> selectItemList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        map.put("AttDayDetailReport", I18nUtil.i18nCode("att_customReport_byDayDetail"));
        map.put("AttMonthStatisticalReport", I18nUtil.i18nCode("att_customReport_byPerson"));
        map.put("AttDeptStatisticalReport", I18nUtil.i18nCode("att_customReport_byDept"));
        for (Map.Entry<String, String> entry : map.entrySet()) {
            SelectItem selectItem = new SelectItem();
            selectItem.setValue(entry.getKey());
            selectItem.setText(entry.getValue());
            selectItemList.add(selectItem);
        }
        request.setAttribute("types", selectItemList);

        // 自定义表单提交路径（为了自定义处理业务、生成对应的系统日志）
        request.setAttribute("actionUrl", "attCustomReport.do?save");

        // 自定义字段数据获取路径
        request.setAttribute("customReportFieldUrl", "attCustomReport.do?getCustomReportField");

        return new ModelAndView("system/customReport/editBaseCustomReport");
    }

    @RequiresPermissions("att:customReport:add")
    @LogRequest(module = "att_module", object = "att_customReport", opType = "common_op_edit", requestParams = {"name"},
        opContent = "common_name")
    @Override
    public ZKResultMsg save(AttCustomReportItem item) {
        ZKResultMsg res = new ZKResultMsg();
        attCustomReportService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("att:customReport:refresh")
    @Override
    public DxGrid list(AttCustomReportItem condition) {
        Pager pager = attCustomReportService.getItemsByPage(condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:customReport:del")
    @LogRequest(module = "att_module", object = "att_customReport", opType = "common_op_del", requestParams = {"names"},
        opContent = "common_name")
    @Override
    public ZKResultMsg delete(String ids) {
        baseCustomReportService.deleteByIds(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @Override
    public ZKResultMsg getCustomReportField(String id, String type) {

        // 已选字段
        Map<String, List<BaseCustomReportFieldItem>> resultMap = new HashMap<>();
        List<BaseCustomReportFieldItem> selectedFields = new ArrayList<>();
        if (StringUtils.isNotBlank(id)) {
            BaseCustomReportItem baseCustomReportItem = baseCustomReportService.getItemById(id);
            selectedFields = baseCustomReportItem.getCustomReportFieldItemList();
        }
        resultMap.put("selectedFields", selectedFields);

        // 待选字段
        List<BaseCustomReportFieldItem> baseCustomReportFieldItemList = new ArrayList<>();
        if ("AttDayDetailReport".equals(type)) {
            baseCustomReportFieldItemList =
                baseCustomReportService.getCustomReportFieldByItem(AttDayDetailReportItem.class);
        } else if ("AttDeptStatisticalReport".equals(type)) {
            baseCustomReportFieldItemList =
                baseCustomReportService.getCustomReportFieldByItem(AttDeptStatisticalReportItem.class);
        } else if ("AttMonthStatisticalReport".equals(type)) {
            baseCustomReportFieldItemList =
                baseCustomReportService.getCustomReportFieldByItem(AttMonthStatisticalReportItem.class);
        }

        // 待选字段过滤已选字段
        for (BaseCustomReportFieldItem selectedField : selectedFields) {
            Iterator<BaseCustomReportFieldItem> iterator = baseCustomReportFieldItemList.iterator();
            while (iterator.hasNext()) {
                BaseCustomReportFieldItem baseCustomReportFieldItem = iterator.next();
                if (baseCustomReportFieldItem.getFieldName().equals(selectedField.getFieldName())) {
                    iterator.remove();
                }
            }
        }

        resultMap.put("fields", baseCustomReportFieldItemList);
        return new ZKResultMsg(resultMap);
    }

    @Override
    public ModelAndView toCustomReport(String id) {
        BaseCustomReportItem baseCustomReportItem = baseCustomReportService.getItemById(id);
        String path = "att/customReport/attDayDetailReportEx";

        if (baseCustomReportItem != null) {
            String reportType = baseCustomReportItem.getReportType();
            List<BaseCustomReportFieldItem> customReportFieldItemList =
                baseCustomReportItem.getCustomReportFieldItemList();
            if (customReportFieldItemList != null) {
                List<BaseCustomReportFieldItem> newCustomReportFieldItemList = new ArrayList<>();
                if ("AttDayDetailReport".equals(reportType)) {

                    // 修改表头单位
                    attDayDetailReportService.modifyItemLabel(AttDayDetailReportItem.class);

                    newCustomReportFieldItemList = baseCustomReportService
                        .buildGridColumnByReportField(customReportFieldItemList, AttDayDetailReportItem.class);
                    path = "att/customReport/attDayDetailReportEx";
                } else if ("AttDeptStatisticalReport".equals(reportType)) {

                    // 修改表头单位
                    attDeptStatisticalReportService.modifyItemLabel();

                    newCustomReportFieldItemList = baseCustomReportService
                        .buildGridColumnByReportField(customReportFieldItemList, AttDeptStatisticalReportItem.class);
                    path = "att/customReport/attDeptStatisticalReportEx";
                } else if ("AttMonthStatisticalReport".equals(reportType)) {

                    // 修改表头单位
                    attMonthStatisticalReportService.modifyItemLabel();

                    newCustomReportFieldItemList = baseCustomReportService
                        .buildGridColumnByReportField(customReportFieldItemList, AttMonthStatisticalReportItem.class);
                    path = "att/customReport/attMonthStatisticalReportEx";
                }

                String showColumns = newCustomReportFieldItemList.stream().map(BaseCustomReportFieldItem::getFieldName)
                    .collect(Collectors.joining(","));
                request.setAttribute("showColumns", showColumns);
            }
        }
        return new ModelAndView(path);
    }
}