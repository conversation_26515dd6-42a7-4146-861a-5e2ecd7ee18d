package com.zkteco.zkbiosecurity.att.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.zkteco.zkbiosecurity.att.remote.AttAnnualLeaveReportRemote;
import com.zkteco.zkbiosecurity.att.service.AttAnnualLeaveReportService;
import com.zkteco.zkbiosecurity.att.vo.AttAnnualLeaveReportItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 年假结余表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/8/17 13:36
 */
@Controller
public class AttAnnualLeaveReportController extends ExportController implements AttAnnualLeaveReportRemote {

    @Autowired
    private AttAnnualLeaveReportService attAnnualLeaveReportService;

    @RequiresPermissions("att:annualLeaveReport")
    @Override
    public ModelAndView index() {
        return new ModelAndView("att/report/attAnnualLeaveReport");
    }

    @RequiresPermissions("att:annualLeaveReport:refresh")
    @Override
    public DxGrid list(AttAnnualLeaveReportItem condition) {
        Pager pager = attAnnualLeaveReportService.loadPagerByAuthUserFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:annualLeaveReport:export")
    @LogRequest(module = "att_module", object = "att_annualLeave_report", opType = "common_op_export",
        requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AttAnnualLeaveReportItem attAnnualLeaveReportItem = new AttAnnualLeaveReportItem();
        setConditionValue(attAnnualLeaveReportItem);
        List<AttAnnualLeaveReportItem> intemList = attAnnualLeaveReportService.getItemData(request.getSession().getId(),
            attAnnualLeaveReportItem, getBeginIndex(), getEndIndex());
        excelExport(intemList, AttAnnualLeaveReportItem.class);
    }

    @Override
    public ZKResultMsg getDetail(String personId) {
        return attAnnualLeaveReportService.getDetail(personId);
    }

    @Override
    public ZKResultMsg recalculate(String ids, String type) {
        return attAnnualLeaveReportService.recalculate(ids, type);
    }

    @Override
    public ModelAndView adjustView(String ids, String type) {
        request.setAttribute("ids", ids);
        request.setAttribute("type", type);
        return new ModelAndView("att/report/attAnnealLeaveAdjust");
    }

    @Override
    public ZKResultMsg adjust(String ids, Integer adjustDays, String type) {
        return attAnnualLeaveReportService.adjust(ids, adjustDays, type);
    }
}
