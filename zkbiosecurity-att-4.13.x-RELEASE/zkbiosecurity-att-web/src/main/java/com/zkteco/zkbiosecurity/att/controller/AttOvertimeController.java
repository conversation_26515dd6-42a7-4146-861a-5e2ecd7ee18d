package com.zkteco.zkbiosecurity.att.controller;

import java.io.IOException;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.remote.AttOvertimeRemote;
import com.zkteco.zkbiosecurity.att.service.AttOvertimeService;
import com.zkteco.zkbiosecurity.att.util.AttExcelUtil;
import com.zkteco.zkbiosecurity.att.util.AttImportWithRowNumUtil;
import com.zkteco.zkbiosecurity.att.vo.AttOvertimeItem;
import com.zkteco.zkbiosecurity.base.annotation.LogChangeRequest;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.LocaleMessageSourceUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 加班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:30
 * @since 1.0.0
 */
@Controller
public class AttOvertimeController extends ExportController implements AttOvertimeRemote {

    @Autowired
    private AttOvertimeService attOvertimeService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private AttExcelUtil attExcelUtil;

    @RequiresPermissions("att:overtime")
    @Override
    public ModelAndView index() {
        if (getCurrentSubject().getStaff()) {
            request.setAttribute("flowType", AttConstant.FLOW_TYPE_OVERTIME);
            return new ModelAndView("att/flowable/overtime/attOvertime");
        } else {
            return new ModelAndView("att/overtime/attOvertime");
        }
    }

    @RequiresPermissions("att:overtime:add")
    @Override
    public ModelAndView edit(String id) {
        SecuritySubject securitySubject = getCurrentSubject();
        Boolean staff = getCurrentSubject().getStaff();
        AttOvertimeItem item = null;
        if (StringUtils.isNotBlank(id)) {
            item = attOvertimeService.getItemById(id);
        } else {
            if (null != staff && staff) {
                item = new AttOvertimeItem();
                item.setPersonId(securitySubject.getUserId());
            }
        }
        request.setAttribute("item", item);
        return new ModelAndView("att/overtime/editAttOvertime");
    }

    @RequiresPermissions("att:overtime:add")
    @LogChangeRequest(module = "att_module", object = "att_leftMenu_overtime", opType = "common_op_new",
        vo = AttOvertimeItem.class, service = AttOvertimeService.class)
    @Override
    public ZKResultMsg save(AttOvertimeItem item) {
        String personIds = request.getParameter("personIds");
        if (StringUtils.isBlank(personIds)) {
            throw new ZKBusinessException("EXCEPTIONLEVEL_WARN", "common_prompt_selectObj");
        }
        // 判断申请时间是否超过上上个月
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -2);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date theFirstDayOfLastMonth = DateUtil.getDayBeginTime(calendar.getTime());
        if (theFirstDayOfLastMonth.compareTo(item.getStartDatetime()) > 0) {
            return ZKResultMsg.getFailMsg(I18nUtil.i18nCode("att_apply_overLastMonth"));
        }
        ZKResultMsg res = new ZKResultMsg();
        attOvertimeService.saveItem(item, personIds, request.getSession().getId());
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("att:overtime:refresh")
    @Override
    public DxGrid list(AttOvertimeItem condition) {
        Pager pager = attOvertimeService.loadPagerByAuthUserFilter(request.getSession().getId(), condition, getPageNo(),
            getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("att:overtime:del")
    @LogRequest(module = "att_module", object = "att_leftMenu_overtime", opType = "common_op_del",
        requestParams = {"pins"}, opContent = "att_person_pin")
    @Override
    public ZKResultMsg delete(String ids) {
        attOvertimeService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @SuppressWarnings("unchecked")
    @RequiresPermissions("att:overtime:export")
    @LogRequest(module = "att_module", object = "att_leftMenu_overtime", opType = "common_op_export",
        requestParams = {"exportType"}, opContent = "common_report_fileType")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AttOvertimeItem attOvertimeItem = new AttOvertimeItem();
        setConditionValue(attOvertimeItem);
        List<AttOvertimeItem> intemList = attOvertimeService.getItemData(request.getSession().getId(), attOvertimeItem, getBeginIndex(), getEndIndex());
        excelExport(intemList, AttOvertimeItem.class);
    }

    @Override
    public void importTemplate(HttpServletRequest request, HttpServletResponse response) {
        JSONObject jsonColumn = new JSONObject(true);
        jsonColumn.put("personPin", "");
        jsonColumn.put("personName", "");
        if (!"zh_CN".equals(LocaleMessageSourceUtil.language)) {
            jsonColumn.put("personLastName", "");
        }
        jsonColumn.put("startDatetime", "");
        jsonColumn.put("endDatetime", "");
        jsonColumn.put("remark", "");
        Map<String, Map<String, String>> jsonCloumnMap = new HashMap<>();
        Map<String, String> jsonMap = new HashMap<>();
        jsonMap.put("jsonColumn", jsonColumn.toJSONString());
        jsonCloumnMap.put("jsonColumn", jsonMap);
        attExcelUtil.attExcelExport(request, response, new ArrayList<>(), AttOvertimeItem.class, jsonCloumnMap);
    }

    @Override
    public ZKResultMsg importExcel(MultipartFile upload) throws IOException {
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_uploadFileSuccess") + "<br/>"));
            List<AttOvertimeItem> itemList =
                AttImportWithRowNumUtil.excelImport(upload.getInputStream(), AttOvertimeItem.class);
            progress += 10;
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_resolutionComplete") + "<br/>"));
            return I18nUtil.i18nMsg(attOvertimeService.importExcel(itemList, request.getSession().getId()));
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                    + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                    new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("Import AttOverTime Info Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
    }

    @RequiresPermissions("att:overtime:approval")
    @LogRequest(module = "att_module", object = "att_leftMenu_overtime", opType = "att_apply_pass",
        requestParams = {"pins"}, opContent = "att_person_pin")
    @Override
    public ZKResultMsg approval(String ids) {
        if (attOvertimeService.hasStaffApply(ids)) {
            return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg("att_approve_withoutPermissionApproval"));
        }
        attOvertimeService.approval(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @RequiresPermissions("att:overtime:approval")
    @LogRequest(module = "att_module", object = "att_leftMenu_overtime", opType = "att_exception_refuse",
        requestParams = {"pins"}, opContent = "att_person_pin")
    @Override
    public ZKResultMsg refuse(String ids) {
        if (attOvertimeService.hasStaffApply(ids)) {
            return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg("att_approve_withoutPermissionApproval"));
        }
        attOvertimeService.refuse(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }
}