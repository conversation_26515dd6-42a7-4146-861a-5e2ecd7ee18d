<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zkteco</groupId>
        <artifactId>zkbiosecurity-att</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zkteco</groupId>
    <artifactId>zkbiosecurity-att-web</artifactId>
    <version>${project.parent.version}</version>
    <name>${project.artifactId}</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-web</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-att-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-att-i18n</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-att-remote</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-auth-api</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-module-all</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-system-api</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-pers-api</artifactId>
            <optional>true</optional>
        </dependency>
        <!-- SelectPersContent 使用 -->
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-ui</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>yuicompressor-maven-plugin</artifactId>
                <version>1.5.1</version>
                <executions>
                    <execution>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>compress</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <!-- 读取js,css文件采用UTF-8编码 -->
                    <encoding>UTF-8</encoding>
                    <!-- 不显示js可能的错误 -->
                    <jswarn>false</jswarn>
                    <!-- 若存在已压缩的文件，会先对比源文件是否有改动  有改动便压缩，无改动就不压缩 -->
                    <force>true</force>
                    <!-- 在指定的列号后插入新行 -->
                    <linebreakpos>-1</linebreakpos>
                    <!-- 压缩之前先执行聚合文件操作 -->
                    <preProcessAggregates>true</preProcessAggregates>
                    <!-- 压缩后保存文件后缀 无后缀 -->
                    <nosuffix>true</nosuffix>
                    <!-- 源目录，即需压缩的根目录 -->
                    <sourceDirectory>src/main/resources</sourceDirectory>
                    <!-- 压缩js和css文件 -->
                    <includes>
                        <include>**/*.js</include>
                        <include>**/*.css</include>
                    </includes>
                    <!-- 以下目录和文件不会被压缩 -->
                    <excludes>
                        <exclude>**/*.min.js</exclude>
                        <exclude>**/*.min.css</exclude>
                        <exclude>**/swagger/**</exclude>
                    </excludes>
                </configuration>
            </plugin>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>