package com.zkteco.zkbiosecurity.att.service;

import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.att.api.vo.MessagePushCloudSendMessageItem;
import com.zkteco.zkbiosecurity.att.vo.AttRecordItem;
import com.zkteco.zkbiosecurity.att.vo.AttTransactionItem;

/**
 * 云平台数据推送
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/2 9:13
 * @since 1.0.0
 */
public interface AttCloudMessageSendService {

    /**
     * 推送考勤计算转换设置规则到云服务
     * 
     * @auther lambert.li
     * @date 2018/12/17 10:23
     * @param params
     * @return
     *
     * @deprecated 考勤不需要推送记录到云端，由云端主动来获取
     */
    @Deprecated
    void sendRuleToCloud(Map<String, String> params);

    /**
     * @Description 异步同步考勤数据到云服务
     * <AUTHOR>
     * @Date 2019/1/16 16:37
     * @Param attTransactionItemList
     * @Return void
     *
     * @deprecated 考勤不需要推送记录到云端，由云端主动来获取
     */
    @Deprecated
    void asyncPushTransactionToCloud(List<AttTransactionItem> attTransactionItemList);

    /**
     * @Description 异步同步考勤计算到云服务
     * <AUTHOR>
     * @Date 2019/1/16 17:19
     * @Param attRecordItemList
     * @Return void
     *
     *  @deprecated 考勤不需要推送记录到云端，由云端主动来获取
     *
     */
    @Deprecated
    void asyncPushRecordToCloud(List<AttRecordItem> attRecordItemList);

    /**
     * 判断是否激活当前模块许可，激活后才能推送数据到云端
     *
     * @return
     * <AUTHOR>
     * @date 2019/11/20 10:37
     */
    boolean isActiveLicense();

    /**
     * 异步推送微信公众号消息给云端
     * 
     * @param messageItemList
     * @return void
     * <AUTHOR>
     * @throws //
     *             @date 2020-11-26 10:07
     * @since 1.0.0
     */
    void asyncPushTransactionWxMsgToCloud(List<MessagePushCloudSendMessageItem> messageItemList);
}
