package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.AttLeaveItem;
import com.zkteco.zkbiosecurity.att.vo.AttTransactionItem;

import java.util.List;

/**
 * 考勤记录实时推送
 *
 * <AUTHOR>
 * @date 2020-05-27 10:33
 * @since 1.0.0
 */
public interface AttRealTimePushService {

    /**
     * 实时点名推送考勤记录
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/27 11:46
     * @param attTransactionItemList
     * @return: void
     **/
    void pushTransaction(List<AttTransactionItem> attTransactionItemList);

    /**
     * 实时点名推送请假
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/27 17:00
     * @param attLeaveItem
     * @return: void
     **/
    void pushLeave(AttLeaveItem attLeaveItem);
}
