package com.zkteco.zkbiosecurity.att.service;

import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONArray;
import com.zkteco.zkbiosecurity.att.vo.AttRealTimeCallRollItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;

/**
 * 实时点名
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:55
 * @since 1.0.0
 */
public interface AttRealTimeCallRollService {

    /**
     * 实时点名：已签到/未签到 数据获取
     * 
     * <AUTHOR>
     * @since 2020年4月21日下午4:30:56
     * @param condition
     * @return
     */
    Map<String, JSONArray> getAttRealTimeCallRollList(AttRealTimeCallRollItem condition);

    /**
     * 列表数据获取
     *
     * <AUTHOR>
     * @since 2020年5月12日上午11:19:56
     * @param condition
     * @return
     */
    Pager getItemByCondition(AttRealTimeCallRollItem condition, int pageNo, int pageSize);

    /**
     * 获取导出数据
     *
     * <AUTHOR>
     * @since 2020年5月12日下午16:41:15
     * @param condition
     *            beginIndex endIndex
     * @return
     */
    List<AttRealTimeCallRollItem> getExportItemByCondition(AttRealTimeCallRollItem condition, int beginIndex,
        int endIndex);
}
