package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotBreakTimeItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import java.util.List;
import java.util.Map;

/**
 * 时间段
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:52
 * @since 1.0.0
 */
public interface AttTimeSlotService extends BaseService {

    AttTimeSlotItem saveItem(AttTimeSlotItem item);

    List<AttTimeSlotItem> getByCondition(AttTimeSlotItem condition);

    AttTimeSlotItem getItemById(String id);

    /**
     * 初始化时间段Map
     * 
     * @return
     */
    Map<String, AttTimeSlotItem> initAttTimeSlotMap();

    /**
     * 时间段编号 是否已经存在
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/6/25 15:04
     * @param periodNo
     * @return boolean
     */
    @Deprecated
    boolean existsByPeriodNo(String periodNo);

    /**
     * 时间段名称 是否已经存在
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/6/25 15:04
     * @param periodName
     * @return boolean
     */
    boolean existsByPeriodName(String periodName);

    /**
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/6/25 16:07
     * @param attTimeSlotItemClass
     * @param condition
     * @param beginIndex
     * @param endIndex
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem>
     */
    List<AttTimeSlotItem> getItemData(Class<AttTimeSlotItem> attTimeSlotItemClass, BaseItem condition, int beginIndex,
        int endIndex);

    /**
     * 判断时间段 是否存在外键关联
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/6/28 15:03
     * @param ids
     * @return boolean
     */
    boolean isExistFkData(String ids);

    Long getAllTimeSlotCount();

    /**
     * 时间段数据迁移
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/7 11:59
     * @param attTimeSlotItems
     * @return void
     */
    void handlerTransfer(List<AttTimeSlotItem> attTimeSlotItems);

    /**
     * 根据条件查询时间段的休息时间段
     * 
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager findBreakTimeListByTimeSlotId(AttTimeSlotBreakTimeItem condition, int pageNo, int pageSize);

    /**
     * 添加休息时间段
     * 
     * @param timeSlotId
     * @param breakTimeIds
     */
    String addBreakTime(String timeSlotId, String breakTimeIds);

    /**
     * 删除休息时间段
     * 
     * @param timeSlotId
     * @param breakTimeIds
     */
    void delBreakTime(String timeSlotId, String breakTimeIds);

    /**
     * 计算时间段的间段时长
     * 
     * @param timeSlotId
     * @return
     */
    int countSegmentTime(String timeSlotId, String breakTimeArray);

    /**
     * 编辑的时候，判断上班时间是否小于所选择的最小休息时间段。 判断下班的时间是否大于所选择的最大休息时间段。
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2019/6/19 11:14
     * @param timeSlotId
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    String timeJudgment(String timeSlotId, String toWorkTime, String offWorkTime);

    /**
     ** <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @Description 判断休息时间段是否在工作时间范围内
     * @date 2020/5/7
     **/
    String breakTimeIsInWorkTime(String breakTimeListStr, String toWorkTime, String offWorkTime);;

    /**
     ** <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @Description 升级到2.2.0R版
     * @date 2019/12/31
     **/
    void upgradeTo_220();

    /**
     * 获取正常时间段树数据，用于前端拖拽
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/4/28 15:24
     * @param
     * @return com.zkteco.zkbiosecurity.base.bean.TreeItem
     */
    TreeItem getTree();

    /**
     * 获取所有的时段信息用于考勤计算用(包含休息时间段)
     * 
     * @return
     */
    List<AttTimeSlotItem> getAllTimeSlotItem();
}