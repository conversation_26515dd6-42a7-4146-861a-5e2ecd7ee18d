package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.AttParkAreaItem;
import com.zkteco.zkbiosecurity.att.vo.AttParkDeviceSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.util.Collection;
import java.util.List;

/**
 * 停车场当考勤、获取停车场设备、区域信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:04
 * @since 1.0.0
 */
public interface AttParkDeviceService {

    AttParkDeviceSelectItem getParkDeviceById(String deviceId);

    AttParkAreaItem getParkAreaByAreaId(String areaId);

    List<AttParkAreaItem> getParkAreaByAreaIds(Collection<String> areaIds);

    List<AttParkDeviceSelectItem> getParkDeviceByIds(Collection<String> deviceIds);

    List<AttParkAreaItem> getParkAreaByAreaAll();

    Pager getSelectParkDevicePager(AttParkDeviceSelectItem condition, int page, int size);
}