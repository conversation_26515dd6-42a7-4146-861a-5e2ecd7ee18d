package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.AttDeptStatisticalReportItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.util.List;

/**
 * 部门汇总表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:25
 * @since 1.0.0
 */
public interface AttDeptStatisticalReportService extends BaseService {

    /**
     * 动态修改表头
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/5 13:41
     * @param
     * @return void
     */
    void modifyItemLabel();

    /**
     * 根据权限用户查询记录
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinjie.you</a>
     * @date 2018/9/3 15:59
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     */
    Pager loadPagerByAuthUserFilter(String sessionId, AttDeptStatisticalReportItem condition, int pageNo, int pageSize);

    /**
     * 部门统计报表导出获取数据
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/29 20:06
     * @param sessionId
     * @param attDeptStatisticalReportItem
     * @param beginIndex
     * @param endIndex
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttDeptStatisticalReportItem>
     */
    List<AttDeptStatisticalReportItem> getDeptStatisticalReportItemData(String sessionId, AttDeptStatisticalReportItem attDeptStatisticalReportItem, int beginIndex, int endIndex);
}