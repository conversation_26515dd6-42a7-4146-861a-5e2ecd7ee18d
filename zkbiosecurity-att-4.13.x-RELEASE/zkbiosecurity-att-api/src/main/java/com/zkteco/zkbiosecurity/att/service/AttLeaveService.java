package com.zkteco.zkbiosecurity.att.service;

import java.util.*;

import com.zkteco.zkbiosecurity.att.bean.AttCalculationLeaveLong;
import com.zkteco.zkbiosecurity.att.calc.bo.AttLeaveBO;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveItem;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 请假
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 9:56
 * @since 1.0.0
 */
public interface AttLeaveService extends BaseService {

    /**
     * 请假申请
     *
     * @param personIds:
     * @param item:
     * @param sessionId: 北向接口调用sessionId=admin
     * @return com.zkteco.zkbiosecurity.att.vo.AttLeaveItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/16 16:53
     * @since 1.0.0
     */
    AttLeaveItem saveItem(String personIds, AttLeaveItem item, String sessionId);

    @Deprecated
    AttLeaveItem saveItem(AttLeaveItem item);

    List<AttLeaveItem> getByCondition(AttLeaveItem condition);

    AttLeaveItem getItemById(String id);

    List<AttLeaveItem> getItemData(String sessionId, AttLeaveItem attLeaveItem, int beginIndex, int endIndex);

    /**
     * 判断是否存在请假
     *
     * @param personIdList
     * @param startTime
     * @param endTime
     * @return
     */
    boolean existLeave(List<String> personIdList, Date startTime, Date endTime);

    /**
     * 根据pin和开始结束时间判断是否存在请假
     *
     * @param pin
     * @param startTime
     * @param endTime
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/3 10:13
     */
    boolean existLeave(String pin, Date startTime, Date endTime);

    /**
     * 根据权限 进行请假查询
     *
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/10 19:27
     */
    Pager loadPagerByAuthUserFilter(String sessionId, AttLeaveItem condition, int pageNo, int pageSize);

    /**
     * @param attLeaveItems
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/10 19:26
     */
    void handlerTransfer(List<AttLeaveItem> attLeaveItems);

    /**
     * 更新流程状态
     *
     * @param businessKey
     * @param status
     */
    void updateFlowStatus(String businessKey, String status);

    /**
     * 根据businessKey 查询对象
     *
     * @param businessKey
     * @return
     */
    AttLeaveItem getByBusinessKey(String businessKey);

    /**
     * 计算人员请假时长分钟数
     *
     * @param personId
     *            人员ID
     * @param startDatetime
     *            请假开始时间
     * @param endDatetime
     *            请假结束时间
     * @return
     */
    int calLeaveTimeMinutes(String personId, Date startDatetime, Date endDatetime, AttLeaveTypeItem attLeaveTypeItem);

    /**
     * 更新流程businessKey
     *
     * @param id
     * @param businessKey
     */
    void updateBusinessKeyById(String id, String businessKey);

    /**
     * 考勤人员的最早请假记录
     *
     * @param beginDate
     * @param endDate
     * @param attPersPins
     * @return
     * <AUTHOR>
     * @since 2020年4月28日下午4:40:06
     */
    List<AttLeaveItem> getItemListByTimeAndPersPins(Date beginDate, Date endDate, Collection<String> attPersPins);

    /**
     * 导入请假
     *
     * @param itemList
     * @param sessionId
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/14 18:59
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    ZKResultMsg importExcel(List<AttLeaveItem> itemList, String sessionId);

    /**
     * 审批（通过）
     *
     * @param ids
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/14 9:20
     */
    void approval(String ids);

    /**
     * 审批（拒绝）
     *
     * @param ids
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/14 9:20
     */
    void refuse(String ids);

    /**
     * 查询时间范围内的请假记录
     *
     * @param pins
     * @param startDate
     * @param endDate
     * @return Map(pin = date, List < AttLeaveBO >)
     */
    Map<String, List<AttLeaveBO>> getLeaveMap(List<String> pins, Date startDate, Date endDate);

    /**
     * 计算请假天数
     *
     * @param item
     * @return
     */
    String calculateLeaveDays(AttLeaveItem item);

    /**
     * 计算请假时长
     *
     * @param pin:
     * @param startDatetime:
     * @param endDatetime:
     * @param businessType:
     * @return com.zkteco.zkbiosecurity.att.bean.AttCalculationLeaveLong
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/11/17 9:22
     * @since 1.0.0
     */
    AttCalculationLeaveLong calculationLeaveLong(String pin, Date startDatetime, Date endDatetime, String businessType);

    /**
     * 请假数据迁移
     * 
     * @param attLeaveItems
     */
    void handlerTransferZKTime(List<AttLeaveItem> attLeaveItems);

    /**
     * 是否有员工自助申请
     *
     * @param ids
     * @return
     */
    boolean hasStaffApply(String ids);

    /**
     * 根据类型计算请假时长，和时长单位
     *
     * @param pin:
     * @param startDatetime:
     * @param endDatetime:
     * @param businessType:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/12/10 20:10
     * @since 1.0.0
     */
    ZKResultMsg getLeaveLongByType(String pin, Date startDatetime, Date endDatetime, String businessType);

    /**
     * 判断人员在相同时间内是否有申请
     *
     * @param personIds:
     * @param startTime:
     * @param endTime:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/16 18:44
     * @since 1.0.0
     */
    ZKResultMsg existApply(String personIds, Date startTime, Date endTime);

    /**
     * 权限判断，
     *
     * @param sessionId: admin：管理员权限（北向api接口），空：没有权限，其他值调用平台判断是否有权限
     * @param permission:
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/25 11:14
     * @since 1.0.0
     */
    boolean checkPermission(String sessionId,String permission);

    /**
     * pinList拆分，查询请假审批，
     *
     * @param pinList:
     * @param leaveCode:
     * @param calculateDate:
     * @param calculateDateEnd:
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttLeaveItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/12/9 11:19
     * @since 1.0.0
     */
    List<AttLeaveItem> getByPersonPinAndLeaveTypeIdAndDate(Collection<String> pinList, String leaveCode, Date calculateDate, Date calculateDateEnd);

    /**
     * 保存云平台下来的异常申请附件到本地
     *
     * @param urlList:
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/9/12 17:10
     * @since 1.0.0
     */
    String saveCloudImage(String urlList);
}