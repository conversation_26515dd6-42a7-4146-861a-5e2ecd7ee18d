package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.vo.AttAdjustItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 调休补班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 16:07
 * @since 1.0.0
 */
public interface AttAdjustService extends BaseService {

    ZKResultMsg saveItem(AttAdjustItem item, String personIds, String sessionId);

    AttAdjustItem saveItem(AttAdjustItem item);

    List<AttAdjustItem> getByCondition(AttAdjustItem condition);

    AttAdjustItem getItemById(String id);

    List<AttAdjustItem> getItemData(String sessionId, Class<AttAdjustItem> attAdjustItemClass, AttAdjustItem attAdjustItem,
        int beginIndex, int endIndex);

    /**
     * 判断是否存在调休补班
     * 
     * @param personIdList: 
     * @param startTime: 
     * @param endTime: 
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/2 9:05
     * @since 1.0.0
     */
    boolean existAdjust(List<String> personIdList, Date startTime, Date endTime);

    /**
     * 根据权限用户查询记录
     *
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/7 10:59
     */
    Pager loadPagerByAuthUserFilter(String sessionId, AttAdjustItem condition, int pageNo, int pageSize);

    /**
     * 调休补班数据迁移
     *
     * @param attAdjustItems
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/6 16:54
     */
    void handlerTransfer(List<AttAdjustItem> attAdjustItems);

    /**
     * 更新流程状态
     *
     * @param businessKey
     * @param status
     */
    void updateFlowStatus(String businessKey, String status);

    /**
     * 根据businessKey 查询对象
     *
     * @param businessKey
     * @return
     */
    AttAdjustItem getByBusinessKey(String businessKey);

    /**
     * 导入调休补班
     *
     * @param itemList
     * @param sessionId
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/15 15:43
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    ZKResultMsg importExcel(List<AttAdjustItem> itemList, String sessionId);

    /**
     * 审批（通过）
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/14 9:20
     * @param ids
     * @return void
     */
    void approval(String ids);

    /**
     * 审批（拒绝）
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/14 9:20
     * @param ids
     * @return void
     */
    void refuse(String ids);

    /**
     * 查询时间范围内的申请记录
     *
     * @param pins
     * @param startDate
     * @param endDate
     * @return
     */
    Map<String, List<AttPersonSchBO>> getAdjustMap(List<String> pins, Date startDate, Date endDate);
}