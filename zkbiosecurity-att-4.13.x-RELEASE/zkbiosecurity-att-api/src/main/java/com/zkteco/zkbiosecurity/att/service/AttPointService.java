package com.zkteco.zkbiosecurity.att.service;

import java.util.List;

import com.zkteco.zkbiosecurity.att.vo.AttEsdcChannelSelectItem;
import com.zkteco.zkbiosecurity.att.vo.AttPointItem;
import com.zkteco.zkbiosecurity.att.vo.AttPointSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 考勤点
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:50
 * @since 1.0.0
 */
public interface AttPointService extends BaseService {

    /**
     * 判断是否存在门禁模块
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/6/19 20:30
     * @return boolean
     */
    boolean isExistAcc();

    /**
     * 判断是否存在停车模块
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/6/19 20:30
     * @return boolean
     */
    boolean isExistPark();

    /**
     * 判断是否存在大屏机
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/9/26 14:02
     * @param
     * @return boolean
     */
    boolean isExistIns();

    /**
     * 判断是否存在人证模块
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/7 9:54
     * @param
     * @return boolean
     */
    boolean isExistPid();

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    AttPointItem saveItem(AttPointItem item);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AttPointItem> getByCondition(AttPointItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    AttPointItem getItemById(String id);

    /**
     * 根据设备类型(门禁，停车)获取所有的DeviceIds
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/6/19 20:31
     * @param deviceModule
     * @return java.util.List<java.lang.String>
     */
    List<String> getDeviceIdsByDeviceModule(String deviceModule);

    boolean isExist(String pointName);

    ZKResultMsg checkLicenseCount(String deviceModule);

    List<TreeItem> getAllParkEntranceArea();

    int getCountByDeviceModule(String deviceModule);

    int getPointCount();

    /**
     * 根据自动登录用户权限进行数据显示 (未实现权限过滤，接口预留，若是有需要，请针对自动登录点进行权限过滤数据设计）
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/6/19 20:31
     * @param sessionId
     *            用户sessionid
     * @param condition
     *            条件
     * @param pageNo
     *            当前页
     * @param pageSize
     *            页面页数
     * @return
     */
    Pager loadSelectPagerByAuthFilter(String sessionId, AttPointSelectItem condition, int pageNo, int pageSize);

    /**
     * 根据设备sn查询考勤点
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/7 9:55
     * @param deviceSn
     * @return com.zkteco.zkbiosecurity.att.vo.AttPointItem
     */
    AttPointItem getItemByDeviceSn(String deviceSn);

    /**
     * 门禁模块一台设备SN 可以对应多个考勤点
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2019/1/8 11:21
     * @param deviceSn
     *            设备SN
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttPointItem>
     */
    List<AttPointItem> getItemsByDeviceSn(String deviceSn);

    /**
     * 根据设备id查询考勤点
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/7 9:55
     * @param deviceId
     * @return
     */
    AttPointItem getItemByDeviceId(String deviceId);

    /**
     * 根据设备sn和们编号 查询考勤点
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/7 9:55
     * @param deviceSn
     * @param doorNo
     * @return
     */
    AttPointItem getItemByDeviceSnAndDoorNo(String deviceSn, Short doorNo);

    /**
     * 考勤点数据迁移(只迁移门禁当考勤点)
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/13 17:18
     * @param attPointItems
     * @return void
     */
    void handlerTransfer(List<AttPointItem> attPointItems);

    /**
     * 根据权限过滤显示页面数据
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/5/21 19:18
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     */
    Pager loadPagerByAuthFilter(String sessionId, AttPointItem condition, int pageNo, int pageSize);

    /**
     * 判断是否存在VMS模块
     *
     * @return
     */
    boolean isExistVms();

    /**
     * 分页查询数据用以导出
     * 
     * <AUTHOR>
     * @since 2020年4月16日上午11:37:43
     * @param targetClass
     * @param codition
     * @param begin
     * @param end
     * @return
     */
    List<AttPointItem> getItemData(Class targetClass, AttPointItem codition, int begin, int end);

    /**
     * 启动时初始化考勤点缓存 初始化考勤点绑定设备的最近拉取记录时间
     *
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/7/16 11:58
     * @return: void
     **/
    void initAttPointTransaction();

    /**
     * 根据缓存点获取对应的key
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/7/16 14:12
     * @param attPoint
     * @return: java.lang.String
     **/
    String getAttPointKey(AttPointItem attPoint);

    /**
     * 开启考勤点定时获取记录
     *
     * @return void
     * @throws <AUTHOR>             <EMAIL>
     * @date 2020/11/5 16:38
     * @since 1.0.0
     */
    void startPointPullTransactionTask();

    /**
     * 判断是否存在第三方当考勤的模块
     * 
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/1/4 20:48
     * @since 1.0.0
     */
    boolean existOtherAttModule();

    /**
     * 判断是否存在psg模块
     *
     * @return
     */
    boolean isExistPsg();

    /**
     * 获取存在考勤点的模块，返回拉下组件格式列表
     *
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/7/16 15:40
     * @since 1.0.0
     */
    List<SelectItem> getPointModule();

    /**
     * 判断是否存在智能视频模块
     *
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/7/27 11:49
     * @since 1.0.0
     */
    boolean isExistIvs();

    /**
     * 判断是否存在智能场景模块
     *
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/7/27 11:49
     * @since 1.0.0
     */
    boolean isExistEsdc();

    /**
     * 取esdc通道双列表数据
     *
     * @param condition:
     * @return com.zkteco.zkbiosecurity.base.bean.DxGrid
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/8/5 14:53
     * @since 1.0.0
     */
    Pager getEsdcChannelSelectList(AttEsdcChannelSelectItem condition, int pageNo, int pageSize);
}