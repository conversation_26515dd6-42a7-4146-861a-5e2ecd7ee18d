package com.zkteco.zkbiosecurity.att.service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.Map;

import com.zkteco.zkbiosecurity.att.bean.AttRuleParamBean;

/**
 * 考勤规则
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:34
 * @since 1.0.0
 */
public interface AttParamService {

    void saveItem(Map<String, String> params);

    Map<String, String> getAttParams();

    /**
     * 获取考勤规则：查找排班记录顺序
     *
     * @return
     * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
     * @since 2019年6月10日 下午15:42:13
     */
    String getFindSchSort();

    /**
     * 获取考勤规则：班次时间段跨天时，考勤计算结果算第一天，还是第二天
     * <p>
     * 考勤计算时不需要查库，已在计算前统一加载
     * </p>
     *
     * @param groupId
     *            分组ID
     * @param deptId
     *            部门ID
     * @param querySql
     *            是否是库(考勤计算时不需要查库)
     * @return
     * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
     * @since 2019年6月10日 下午15:42:13
     */
    @Deprecated
    String getCrossDay(String groupId, String deptId, boolean querySql);

    /**
     * 获取考勤参数：新增设备是否自动添加
     *
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/28 18:2
     * @return: java.lang.String
     **/
    String getAutoAddDevice();

    /**
     * 获取考勤参数：仅接收数据库中存在的人员
     * <p>
     * 0:仅接收数据库中存在的人员；1:以设备为准
     * </p>
     *
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/28 18:22
     * @return: java.lang.String
     **/
    String getReceivePersonOnlyDb();

    /**
     * 获取考勤参数: 未签到
     *
     * @return
     */
    String getNoSignIn();

    /**
     * 获取考勤参数: 未签退
     *
     * @return
     */
    String getNoSignOff();

    /**
     * 获取考勤参数: 小数点精确位数
     *
     * @return
     */
    String getDecimal();

    /**
     * 天数格式化转换
     *
     * @param dayStr
     *            字符串型的天数（带小数，如1.2天）
     * @return
     */
    String dayFormat(String dayStr);

    /**
     * 根据参数设置的小数点位数，初始化格式化对象
     *
     * @return
     */
    DecimalFormat getDecimalFormat();

    /**
     * 小时转换基准( 以公式计算结果为准; 公式：小时数 = 分钟数 / 60；)
     *
     * @param minutesTime
     * @return
     *
     */
    String minutesToHourFormat(BigDecimal minutesTime);

    /**
     * 小时转换基准( 以公式计算结果为准; 公式：小时数 = 分钟数 / 60；)
     * <p>
     * 存在多次计算时，小数点精确度应该由参数传入，防止多次查询redis
     * <p/>
     * @param minutesTime
     * @return
     *
     */
    String minutesToHourFormat(String minutesTime, String decimal);

    /**
     * 判断是否为单考勤系统
     *
     * @param
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/4/17 13:43
     */
    boolean singleAttSystem();

    /**
     * 获取考勤规则参数对象
     *
     * @return
     */
    AttRuleParamBean getRuleParam();

    /**
     * 获取定时统计日打卡详情的时间间隔
     *
     * @param
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/6/30 17:05
     */
    String getUpdateDayCardDetail();

    /**
     * 获取班次时间段跨天时，考勤计算结果
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/28 16:44
     * @param
     * @return java.lang.String
     */
    String getCrossDay();

    /**
     * 是否启用实时计算
     *
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/15 14:11
     * @since 1.0.0
     */
    boolean realTimeEnable();

    /**
     * 获取年假计算方式
     *
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/30 9:36
     * @since 1.0.0
     */
    String getAnnualLeaveCalculateType();

    /**
     * 获取年假时长规则
     *
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/30 9:36
     * @since 1.0.0
     */
    String getAnnualLeaveCalculateRule();


    Date getAnnualLeaveCalculateStartDate();

    /**
     * 获取年假计算截至日期
     * 清零发放日期如果是2023-02-01
     * 1、当前时间为2023-03-01，则年假计算截至日期2023-02-01
     * 2、当前时间为2023-01-01，则年假计算截至日期2022-02-01
     *
     * @return java.util.Date
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/2/28 16:25
     * @since 1.0.0
     */
    Date getAnnualLeaveCalculateDate();

    /**
     * 年休假清零发放日期几月
     * 
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/10/12 17:50
     * @since 1.0.0
     */
    String getAnnualLeaveCalculateMonth();

    /**
     * 年休假清零发放日期几号
     * 
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/10/12 17:51
     * @since 1.0.0
     */
    String getAnnualLeaveCalculateDay();

    /**
     * 是否启用年假
     *
     * @return boolean true 启用，false 不启用
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/10/23 10:31
     * @since 1.0.0
     */
    boolean enableAnnualLeave();

    /**
     * 是否启用按工龄月份比例时长计算
     *
     * @return
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/2/28 11:38
     * @since 1.0.0
     */
    boolean enableCalculateType();


    /**
     * 前端时间格式展示转换
     *
     * @param date:
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/3/4 16:05
     * @since 1.0.0
     */
    String dateToLocaleString(Date date);

    /**
     * 前端时间格式展示转换
     *
     * @param date:
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/3/4 16:05
     * @since 1.0.0
     */
    String dateTimeToLocaleString(Date date);

    /**
     * 获取加班等级配置
     * 
     * @return java.util.Map<java.lang.String,java.util.Map<java.lang.String,java.lang.String>>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/9/12 9:35
     * @since 1.0.0
     */
    Map<String, String> getOvertimeSetting();

    /**
     * 是否启用加班等级
     * 
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/9/12 16:50
     * @since 1.0.0
     */
    boolean overtimeLevelEnable();
}