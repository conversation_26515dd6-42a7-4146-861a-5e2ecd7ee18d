package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.AttInsDeviceSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;

import java.util.Collection;
import java.util.List;

/**
 * 信息屏当考勤、获取信息屏设备信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:04
 * @since 1.0.0
 */
public interface AttInsDeviceService {

    /**
     * 根据设备的id获取 大屏机大屏信息
     * 
     * @param deviceId
     * @return
     */
    AttInsDeviceSelectItem getInsDeviceByDeviceId(String deviceId);

    /**
     * 根据设备的id获取 大屏机大屏信息
     * 
     * @param deviceIds
     * @return
     */
    List<AttInsDeviceSelectItem> getInsDeviceByDeviceIds(Collection<String> deviceIds);

    /**
     * 获取大屏机双列表
     * 
     * @param condition
     * @param page
     * @param size
     * @return
     */
    Pager getSelectInsDevicePager(AttInsDeviceSelectItem condition, int page, int size);

}