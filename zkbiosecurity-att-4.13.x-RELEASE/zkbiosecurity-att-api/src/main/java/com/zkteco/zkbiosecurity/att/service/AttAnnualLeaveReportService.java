package com.zkteco.zkbiosecurity.att.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.att.vo.AttAnnualLeaveReportItem;
import com.zkteco.zkbiosecurity.att.vo.AttAnnualLeaveRuleItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 年假结余表
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 9:23 2020/8/4
 */
public interface AttAnnualLeaveReportService extends BaseService {

    Pager loadPagerByAuthUserFilter(String id, AttAnnualLeaveReportItem condition, int pageNo, int pageSize);

    List<AttAnnualLeaveReportItem> getItemData(String id, AttAnnualLeaveReportItem attDayDetailReportItem,
        int beginIndex, int endIndex);

    /**
     * 设置定时清空发放年假
     *
     * @param calculateMonth:
     * @param calculateDay:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/29 18:50
     * @since 1.0.0
     */
    void setAnnualLeaveScheduled(String calculateMonth, String calculateDay);

    /**
     * 初始化定时清空发放年假
     *
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/10/9 14:23
     * @since 1.0.0
     */
    void initAnnualLeaveScheduled();

    /**
     * 计算年假
     *
     * @param hireDate:
     *            入职日期
     * @param calculateType:
     * @param attAnnualLeaveRuleItemList:
     * @param calculateDate:
     *            计算日期
     * @return int
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/10/9 14:15
     * @since 1.0.0
     */
    Map<String, String> calculateAnnualLeaveDays(Date hireDate, String calculateType,
        List<AttAnnualLeaveRuleItem> attAnnualLeaveRuleItemList, Date calculateDate);

    /**
     * 组装年假时长规则
     *
     * @param attAnnualLeaveRule:
     *            年假时长规则
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttAnnualLeaveRuleItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/30 9:22
     * @since 1.0.0
     */
    List<AttAnnualLeaveRuleItem> buildRuleItem(String attAnnualLeaveRule);

    /**
     * 根据前端map中的参数，找出年假时长规则参数，并赋值
     *
     * @param params:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/30 9:30
     * @since 1.0.0
     */
    void buildRuleValue(Map<String, String> params);

    /**
     * 年假详情
     * 
     * @param personId:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/10/10 14:32
     * @since 1.0.0
     */
    ZKResultMsg getDetail(String personId);

    /**
     * 根据pin获取年假结余
     *
     * @param personPin:
     * @return com.zkteco.zkbiosecurity.att.vo.AttAnnualLeaveReportItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/11/17 14:18
     * @since 1.0.0
     */
    ZKResultMsg getDetailByPersonPin(String personPin);

    /**
     * 重新计算年假
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/1/3 11:15
     * @since 1.0.0
     */
    ZKResultMsg recalculate(String ids, String type);

    /**
     * 调整年假
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/1/3 11:15
     * @since 1.0.0
     */
    ZKResultMsg adjust(String ids, Integer adjustDays, String type);
}