package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.bean.AttPersonAttendanceBean;
import com.zkteco.zkbiosecurity.att.vo.AttRecordItem;

/**
 * 考勤计算入口，针对每人每天的考勤计算
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/2 9:09
 * @since 1.0.0
 */
public interface AttAttendanceCalculateService {

    /**
     * 考勤计算入口
     * <p>
     * 计算每人每天的考勤结果
     * </p>
     * 
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/6/10 17:13
     * @param attPersonAttendanceBean
     *            人天班计算对象
     * @return
     */
    AttRecordItem calculate(AttPersonAttendanceBean attPersonAttendanceBean);
}
