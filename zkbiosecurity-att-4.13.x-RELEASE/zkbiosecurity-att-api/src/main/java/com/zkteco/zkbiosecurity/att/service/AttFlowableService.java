package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.base.bean.Pager;

import java.util.Map;

/**
 * 异常申请、查看（员工自助）
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 15:54
 * @since 1.0.0
 */
public interface AttFlowableService {

    /**
     * 查询我的申请
     * 
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager findMyApplyList(String sessionId, AttProcessInfoItem condition, int pageNo, int pageSize);

    /***
     * 查询我的待审批任务
     * 
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager findPendingTaskList(String sessionId, AttProcessInfoItem condition, int pageNo, int pageSize);

    /**
     * 查询我的已审批任务
     * 
     * @param condition
     * @param pageNo
     * @param pageSize
     *
     * @return
     */
    Pager findApprovedTaskList(String sessionId, AttProcessInfoItem condition, int pageNo, int pageSize);

    /**
     * 补签申请
     * 
     * @param attSignItem
     */
    void startSign(AttSignItem attSignItem);

    /**
     * 请假申请
     * 
     * @param attLeaveItem
     */
    void startLeave(AttLeaveItem attLeaveItem);

    /**
     * 加班申请
     * 
     * @param attOvertimeItem
     */
    void startOvertime(AttOvertimeItem attOvertimeItem);

    /**
     * 外出申请
     * 
     * @param attOutItem
     */
    void startOut(AttOutItem attOutItem);

    /**
     * 出差申请
     * 
     * @param attTripItem
     */
    void startTrip(AttTripItem attTripItem);

    /**
     * 调休申请
     * 
     * @param attClassItem
     */
    void startClass(AttClassItem attClassItem);

    /**
     * 调班申请
     * 
     * @param attAdjustItem
     */
    void startAdjust(AttAdjustItem attAdjustItem);

    /**
     * 申请查询
     * 
     * @param personId
     * @param flowType
     * @return
     */
    Map<String, String> apply(String personId, String flowType);

    /**
     * 审批
     * 
     * @param taskId
     * @return
     */
    Map approve(String taskId);

    /**
     * 详情
     * 
     * @param businessKey
     * @return
     */
    Map detail(String businessKey);
}
