package com.zkteco.zkbiosecurity.att.service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.zkteco.zkbiosecurity.att.bean.AttRuleParamBean;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 考勤计算人员排班数据组装
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:39
 * @since 1.0.0
 */
public interface AttPersonSchDataService {

    /**
     * 获取人员集合排班信息（周期排班、临时排班）
     *
     * @param pins
     *            人员pin号集合
     * @param startDate
     *            yyyy-MM-dd
     * @param endDate
     *            yyyy-MM-dd
     * @return
     */
    Map<String, List<AttPersonSchBO>> getPersonSchData(List<String> pins, Date startDate, Date endDate);

    /**
     * 填充周期排班、临时排班
     * <p>
     * 精确查询，但需要进行多次查库
     * </p>
     * 
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/6/17 16:38
     * @param attPersonItems
     * @param startDate
     * @param endDate
     * @param attShiftItemMap
     * @param attTimeSlotItemMap
     * @param attRuleParamBean
     * @return java.util.Map<java.lang.String,java.util.List<com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO>>
     */
    Map<String, List<AttPersonSchBO>> buildPersonSchData(List<AttPersonItem> attPersonItems, Date startDate,
        Date endDate, Map<String, AttShiftItem> attShiftItemMap, Map<String, AttTimeSlotItem> attTimeSlotItemMap,
        AttRuleParamBean attRuleParamBean);

    /**
     * 获取人员排班集合
     * 
     * @param pins:
     * @param startDate:
     * @param endDate:
     * @return java.util.Map<java.lang.String,java.util.List<com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO>>
     * <AUTHOR>
     * @date 2020-12-25 15:35
     * @since 1.0.0
     */
    Map<String, List<AttPersonSchBO>> buildPersonSchData(List<String> pins, Date startDate, Date endDate);

    /**
     * 填充周期排班、临时排班
     * <p>
     * 减少查库存次数，但会多查无关数据出来，适用于大批量手动考勤计算
     * </p>
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/6/17 16:38
     * @param attPersonItems
     * @param startDate
     * @param endDate
     * @param attShiftItemMap
     * @param attTimeSlotItemMap
     * @param attRuleParamBean
     * @param attCycleSchItemList
     * @param attTempSchItemList
     * @return java.util.Map<java.lang.String,java.util.List<com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO>>
     */
    Map<String, List<AttPersonSchBO>> buildPersonSchData2(List<AttPersonItem> attPersonItems, Date startDate,
        Date endDate, Map<String, AttShiftItem> attShiftItemMap, Map<String, AttTimeSlotItem> attTimeSlotItemMap,
        AttRuleParamBean attRuleParamBean, List<AttCycleSchItem> attCycleSchItemList,
        List<AttTempSchItem> attTempSchItemList);

    /**
     * 获取人员集合排班信息（周期排班、临时排班、调休补班、调班）
     * 
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/6/17 16:09
     * @param pins
     * @param startDate
     * @param endDate
     * @return java.util.Map<java.lang.String,java.util.List<com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO>>
     */
    Map<String, List<AttPersonSchBO>> getPersonAllSchData(List<String> pins, Date startDate, Date endDate);

    /**
     * 填充调休补班、调班、节假日
     * 
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/6/17 16:32
     * @param schDataMap
     * @param attAdjustMap
     * @param attClassMap
     * @param holidaySet
     * @return void
     */
    void buildPersonSchExData(Map<String, List<AttPersonSchBO>> schDataMap,
        Map<String, List<AttPersonSchBO>> attAdjustMap, Map<String, List<AttPersonSchBO>> attClassMap,
        Set<String> holidaySet);

    /**
     * 获取人员当日排班时间及工作时长
     *
     * @param pin
     * @param attDate
     *            yyyy-MM-dd
     * @return
     */
    ZKResultMsg getTodayWorkTime(String pin, String attDate);
}
