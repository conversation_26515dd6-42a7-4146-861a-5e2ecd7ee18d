package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.api.vo.AttApiTeamAbnormalItem;
import com.zkteco.zkbiosecurity.att.api.vo.AttApiTeamAttendanceStatusItem;
import com.zkteco.zkbiosecurity.att.api.vo.AttApiTeamSignPersonItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;

import java.util.List;
import java.util.Map;

/**
 * 考勤-我的团队
 * 
 * <AUTHOR>
 * @date 2019/03/02 10:05
 */
public interface AttTeamWorkFlowService {

    /**
     * 根据日期查找团队成员的休假信息列表
     * 
     * @param startDate
     * @param endDate
     * @param personPin
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Deprecated
    Pager findTeamLeaveTask(String startDate, String endDate, String personPin, Integer pageNo, Integer pageSize);

    /**
     * 查询团队当月申请加班的信息列表
     * 
     * @param month
     * @param personPin
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Deprecated
    Pager findTeamOvertimeTask(String month, String personPin, Integer pageNo, Integer pageSize);

    /**
     * 按月统计所有成员当月的加班工时
     * 
     * @param month
     * @param personPin
     * @return
     */
    @Deprecated
    String getTeamOvertimeHours(String month, String personPin);

    /**
     * 查询当月指定员工的申请信息列表
     * 
     * @param month
     * @param personPin
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Deprecated
    Pager findPersonOvertimeTask(String month, String personPin, Integer pageNo, Integer pageSize);

    /**
     * 按月统计当月团队总的补签次数和各成员的补签次数
     * 
     * @param month
     * @param personPin
     * @return
     */
    @Deprecated
    List<Map<String, Object>> getTeamSignTimes(String month, String personPin);

    /**
     * 根据成员查找当月的所有补签信息
     * 
     * @param month
     * @param personPin
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Deprecated
    Pager findPersonSignTask(String month, String personPin, Integer pageNo, Integer pageSize);

    /**
     * 获取团队成员pins集合
     * 
     * @param personPin
     * @return
     */
    List<String> getTeamMemberPins(String personPin);

    /**
     * 团队考勤（按状态）
     * 
     * @param personPin
     * @param deptCode
     * @param attDate
     * @return
     */
    AttApiTeamAttendanceStatusItem getTeamAttendanceStatusItem(String personPin, String deptCode, String attDate);

    /**
     * 团队考勤（按员工）
     * 
     * @param personPin
     * @param deptCode
     * @param attDate
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager getTeamAttendancePersonItem(String personPin, String deptCode, String attDate, Integer pageNo,
        Integer pageSize);

    /**
     * 异常明细
     * 
     * @param personPin
     * @param attDate
     * @param attType
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager getTeamLeakagePersonItem(String personPin, String attDate, String attType, Integer pageNo, Integer pageSize);

    /**
     * 团队异常统计 加班 休假
     * 
     * @param personPin
     * @param attDate
     * @return
     */
    AttApiTeamAbnormalItem getTeamAbnormalItem(String personPin, String attDate, String type);

    /**
     * 团队休假
     * 
     * @param personPin
     * @param month
     *            yyyy-MM
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager getTeamLeavePersonItem(String personPin, String month, Integer pageNo, Integer pageSize);

    /**
     * 团队加班（申请明细）
     * 
     * @param personPin
     * @param attDate
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager getTeamOvertimePersonItem(String personPin, String attDate, Integer pageNo, Integer pageSize);

    /**
     * 团队加班（工时统计）
     * 
     * @param personPin
     * @param month
     *            yyyy-MM
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager getTeamOverTimeByTypePersonItem(String personPin, String month, Integer pageNo, Integer pageSize);

    /**
     * 团队补签（月详情）
     * 
     * @param personPin
     * @param month
     *            yyyy-MM
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager getTeamSignMonthItem(String personPin, String month, Integer pageNo, Integer pageSize);

    /**
     * 团队补签（月次数补签次数统计）
     * 
     * @param personPin
     * @param month
     *            yyyy-MM
     * @return
     */
    Integer getTeamSignCount(String personPin, String month);

    /**
     * 团队成员补签（月）
     * 
     * @param personPin
     * @param month
     *            yyyy-MM
     * @return
     */
    AttApiTeamSignPersonItem getTeamSignPersonItem(String personPin, String month);

    /**
     * 团队出差-分页列表
     * 
     * @param personPin
     * @param month
     *            yyyy-MM
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager getTeamTripPersonItem(String personPin, String month, Integer pageNo, Integer pageSize);

    /**
     * 团队外出-分页列表
     * 
     * @param personPin
     * @param month
     *            yyyy-MM
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager getTeamOutPersonItem(String personPin, String month, Integer pageNo, Integer pageSize);

    /**
     * 团队考勤-按考勤结果类型查询人员
     *
     * @param personPin
     * @param deptCode
     * @param attDate
     * @param attType
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager getTeamAbnormalPersonItem(String personPin, String deptCode, String attDate, String attType, Integer pageNo,
        Integer pageSize);
}
