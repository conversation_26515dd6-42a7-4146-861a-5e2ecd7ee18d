package com.zkteco.zkbiosecurity.att.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 假种
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:13
 * @since 1.0.0
 */
public interface AttLeaveTypeService extends BaseService {

    AttLeaveTypeItem saveItem(AttLeaveTypeItem item);

    List<AttLeaveTypeItem> getByCondition(AttLeaveTypeItem condition);

    AttLeaveTypeItem getItemById(String id);

    /**
     * 初始化假种
     *
     * @param item:
     * @return com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/1 10:16
     * @since 1.0.0
     */
    AttLeaveTypeItem initData(AttLeaveTypeItem item);

    /**
     * 获取全部假种JSON
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/1 10:16
     * @since 1.0.0
     */
    ZKResultMsg listJsonLeaveType();

    /**
     * 是否为初始化数据
     *
     * @param ids:
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/1 10:15
     * @since 1.0.0
     */
    boolean isInitData(String ids);

    /**
     * 判断假种名称 是否有效/允许
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/6/28 9:48
     * @param leaveTypeName
     * @return java.lang.String
     */
    boolean isLeaveTypeName(String leaveTypeName);

    /**
     * 判断假种类型是否存在外键关联
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/6/28 13:52
     * @param ids
     * @return boolean
     */
    boolean isExistFkData(String ids);

    /**
     * 导出
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/9 15:14
     * @param attLeaveTypeItemClass
     * @param attLeaveTypeItem
     * @param beginIndex
     * @param endIndex
     * @return java.lang.Object
     */
    Object getItemData(Class<AttLeaveTypeItem> attLeaveTypeItemClass, AttLeaveTypeItem attLeaveTypeItem, int beginIndex,
        int endIndex);

    /**
     * 请假类型数据迁移
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/10 17:19
     * @param attLeaveTypeItems
     * @return void
     */
    void handlerTransfer(List<AttLeaveTypeItem> attLeaveTypeItems);

    /**
     * 获取请假类型
     * 
     * <AUTHOR>
     * @since 2019年6月18日 下午4:05:53
     * @return
     */
    ZKResultMsg listLeaveType();

    /**
     * 升级到2.3.2_R版本
     *
     */
    void upgradeTo_232();

    /***
     * 获取假种为出差和外出的id
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/12
     * @return: java.lang.String
     **/
    String getTripAndOutId();

    /**
     * 获取除出差和外出的所有假种
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/13 16:51
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    ZKResultMsg listJsonLeaveTypeFilterTripAndOut();

    /**
     * 根据假种编号来获取假种
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/14 10:12
     * @param leaveTypeNo
     * @return: com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem
     **/
    AttLeaveTypeItem getItemByLeaveTypeNo(String leaveTypeNo);

    /***
     *
     * 根据ids获取请假类型
     * 
     * @param leaveTypeIdList
     * @return: java.util.List<com.zkteco.zkbiosecurity.att.model.AttLeaveType>
     * @throws:
     * @author: bob.liu
     * @time: 2020/5/25 13:47
     * @since 1.0.0
     */
    List<AttLeaveTypeItem> getLeaveTypeByIds(Collection<String> leaveTypeIdList);

    /**
     * 获取非假类计算设置
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/3 14:39
     * @param
     * @return java.util.Map<java.lang.String,com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem>
     */
    Map<String, AttLeaveTypeItem> getLeaveTypeParamsMap();

    /**
     * 获取假种
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/4 15:02
     * @param
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem>
     */
    List<AttLeaveTypeItem> getLeaveTypeItems();

    /**
     * 获取假种
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/3 14:39
     * @param
     * @return java.util.Map<java.lang.String,com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem>
     */
    Map<String, AttLeaveTypeItem> getLeaveTypeMap();

    /**
     * 获取转化后的单位名称
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/4 15:11
     * @param attLeaveTypeItem
     * @return java.lang.String
     */
    String getConvertUnit(AttLeaveTypeItem attLeaveTypeItem);

    /**
     * 请假类型数据迁移
     * 
     * @param attLeaveTypeItems
     */
    void handlerTransferZKTime(List<AttLeaveTypeItem> attLeaveTypeItems);

    /**
     * 获取除出差和外出的所有假种
     * 
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem>
     * <AUTHOR>
     * @date 2020-12-09 17:59
     * @since 1.0.0
     */
    List<AttLeaveTypeItem> listLeaveTypeFilterTripAndOut();

    /**
     * 根据假种编号更新排序序号
     *
     * @param attLeaveTypeItemList:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/2/2 11:43
     * @since 1.0.0
     */
    void updateSortNoByLeaveTypeNo(List<AttLeaveTypeItem> attLeaveTypeItemList);
}