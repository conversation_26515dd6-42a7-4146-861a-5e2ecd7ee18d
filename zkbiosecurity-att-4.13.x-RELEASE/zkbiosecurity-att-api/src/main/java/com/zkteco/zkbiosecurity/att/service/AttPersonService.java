package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.bean.AttConditionBean;
import com.zkteco.zkbiosecurity.att.bean.AttPersPersonInfoBean;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 考勤人员
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:44
 * @since 1.0.0
 */
public interface AttPersonService extends BaseService {

    AttPersonItem saveItem(AttPersonItem item);

    List<AttPersonItem> getByCondition(AttPersonItem condition);

    Pager loadPagerByAuthFilterByGroup(String sessionId, BaseItem condition, int page, int size);

    AttPersonItem getItemById(String id);

    List<AttPersonItem> getItemByIds(String ids);

    /**
     * 根据deptIds查询（不包含考勤参数）
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/7 10:00
     * @param deptIds
     * @return com.zkteco.zkbiosecurity.att.vo.AttPersonItem
     */
    List<AttPersonItem> getItemByDeptIds(String deptIds);

    /**
     * 根据personId查询（不包含考勤参数）
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/7 10:00
     * @param personId
     * @return com.zkteco.zkbiosecurity.att.vo.AttPersonItem
     */
    AttPersonItem getItemByPersonId(String personId);

    /**
     * 根据人员ID获取人员集合（不包含考勤参数）
     *
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/7/18 14:20
     * @param personIds
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttPersonItem>
     */
    List<AttPersonItem> getItemByPersonIds(Collection<String> personIds);

    /**
     * 根据人员pin获取人员对象（不包含考勤参数）
     *
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/7/18 14:26
     * @param personPin
     * @return com.zkteco.zkbiosecurity.att.vo.AttPersonItem
     */
    AttPersonItem getItemByPersonPin(String personPin);

    /**
     * 根据人员pins查询（不包含考勤参数）
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2019/2/15 16:36
     * @param personPins
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttPersonItem>
     */
    List<AttPersonItem> getItemByPersonPins(Collection<String> personPins);

    /**
     * 通用选人控件（不过滤）
     *
     */
    Pager getAttPersonSelectByPage(String sessionId, AttPersonSelectItem condition, int page, int size);

    /**
     * 手动计算
     * 
     * @param condition
     * @param page
     * @param size
     * @return
     */
    Pager getItemsByPageForAttCal(String sessionId, AttManualCalculationItem condition, int page, int size);

    /**
     * 根据 登入人员的权限进行过滤显示
     * 
     * @param sessionId
     * @param condition
     * @param type
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager getNoExistPerson(String sessionId, AttPersonSelectItem condition, String type, int pageNo, int pageSize);

    /**
     * 解析用户数据
     * 
     * @param sn
     * @param data
     */
    void analysisUserData(String sn, String data);

    /**
     * 处理考勤生物模板一体化数据
     * 
     * @param sn
     * @param data
     */
    void handlerBioData(String sn, String data);

    /**
     * 分组添加人员 1.第一次添加的人，要添加到考勤人员从表，且建立人员与分组的关系； 2.再次添加的人员，要修改人员与分组的关系；
     * 
     * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
     * @param groupId
     * @param personIds
     * @param deptIds
     * @return
     */
    void updateAttPerson(String groupId, String personIds, String deptIds);

    void deleteByIds(String personIds, String groupId);

    /**
     * 异地考勤获取人员信息
     *
     * @param sn
     * @param pin
     * @return
     */
    String getRemoteAttPersonData(String sn, String pin);

    /**
     * 考勤人员 数据迁移
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/26 14:28
     * @param attPersonItems
     * @return void
     */
    void handlerTransfer(List<AttPersonItem> attPersonItems);

    /**
     * 根据部门Id循环查找所有子部门
     * 
     * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
     * @since 2019年8月23日 下午3:53:44
     * @param deptIds
     * @return
     */
    List<String> getAllChildDeptById(String deptIds);

    /**
     * 验证员工权限
     * 
     * <AUTHOR>
     * @param sessionId
     * @return
     */
    String getPinByLoginType(String sessionId);

    /**
     * 按人设置查询
     * 
     * @param condition
     * @param page
     * @param size
     * @return
     */
    Pager getPersonAreaItemsByPage(String sessionId, AttPersonAreaItem condition, int page, int size);


    /**
     * 把查询的人、部门转为人员pin号的集合,过滤离职时间小于查询的开始时间的人员
     * 
     * @param personIds
     * @param deptIds
     * @param startDate
     * @param attendanceLeaver
     * @return
     */
    List<String> filterPinList(String personIds, String deptIds, Date startDate, boolean attendanceLeaver);

    /**
     * 查询人员和离职人员pin号（列表组装搜索使用）
     *
     * @param likeName:
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/8/24 18:05
     * @since 1.0.0
     */
     String getPinsByLikeName(String sessionId, String likeName);

     /**
      * 根据pin集合查询人员和离职人员信息（（列表组装数据使用）
      *
      * @param pinList:
      * @return java.util.Map<java.lang.String,com.zkteco.zkbiosecurity.att.bean.AttPersPersonInfoBean>
      * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
      * @date 2023/8/25 9:48
      * @since 1.0.0
      */
     Map<String, AttPersPersonInfoBean> getPersonInfoByPinList(Collection<String> pinList);

    /**
     * 根据权限获取条件组装对象（列表组装搜索使用）
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/10 13:47
     * @param sessionId
     * @param attConditionBean
     * @return com.zkteco.zkbiosecurity.att.bean.AttConditionBean
     */
    void getConditionBySessionId(String sessionId, AttConditionBean attConditionBean);

    /**
     * 更新人员缓存考勤附加字段集合
     *
     * @param attPersonItem:
     * @return java.util.Map<java.lang.String,java.util.Map<java.lang.String,java.lang.Object>>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 14:09
     * @since 1.0.0
     */
    void updatePersonCacheExtParams(AttPersonItem attPersonItem);

    /**
     * 批量更新人员缓存考勤附加字段集合
     * 
     * @param attPersonItemList:
     * @return java.util.Map<java.lang.String,java.util.Map<java.lang.String,java.lang.Object>>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 14:09
     * @since 1.0.0
     */
    void updatePersonCacheExtParams(List<AttPersonItem> attPersonItemList);

    /**
     * 组装人员缓存信息的考勤附加属性
     * 
     * @param attPersonItem:
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 14:38
     * @since 1.0.0
     */
    Map<String, Object> buildPersonCacheExtParams(AttPersonItem attPersonItem);

    /**
     * 根据人员编号集合获取缓存的人员信息，并组装成AttPersonItem对象（包含考勤参数）
     *
     * @param pins:
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttPersonItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/16 14:02
     * @since 1.0.0
     */
    List<AttPersonItem> getByPinsFromPersonCache(List<String> pins);

    /**
     * 根据人员编号获取缓存的人员信息，并组装成AttPersonItem对象（包含考勤参数）
     * 
     * @param pin:
     * @return com.zkteco.zkbiosecurity.att.vo.AttPersonItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/16 14:29
     * @since 1.0.0
     */
    AttPersonItem getByPinFromPersonCache(String pin);

    /**
     * 根据人员ID，获取对应考勤人员map集合（不包含考勤参数）
     *
     * @param personIds:
     * @return java.util.Map<java.lang.String,com.zkteco.zkbiosecurity.att.vo.AttPersonItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/5/26 9:36
     * @since 1.0.0
     */
    Map<String, AttPersonItem> getPersonIdAndAttPersonItemMap(Collection<String> personIds);

    /**
     * 获取人员验证方式列表数据
     *
     * @param sessionId:
     * @param condition:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/1/6 17:21
     * @since 1.0.0
     */
    Pager verifyModeList(String sessionId, AttPersonVerifyModeItem condition, int pageNo, int pageSize);

    /**
     * 保存考勤人员验证方式
     *
     * @param attPersonVerifyModeItem:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/1/6 17:21
     * @since 1.0.0
     */
    void saveVerifyMode(AttPersonVerifyModeItem attPersonVerifyModeItem);
}