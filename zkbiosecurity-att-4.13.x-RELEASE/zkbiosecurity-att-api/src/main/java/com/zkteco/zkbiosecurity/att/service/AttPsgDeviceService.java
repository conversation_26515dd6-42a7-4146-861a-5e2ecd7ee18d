package com.zkteco.zkbiosecurity.att.service;

import java.util.Collection;
import java.util.List;

import com.zkteco.zkbiosecurity.att.vo.AttPsgDeviceSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;

/**
 * 通道当考勤，获取通道设备信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:53
 * @since 1.0.0
 */
public interface AttPsgDeviceService {

    /**
     * 查询双列表
     *
     * @param condition
     * @param page
     * @param size
     * @return
     */
    Pager getSelectPsgDevicePager(AttPsgDeviceSelectItem condition, int page, int size);

    /**
     * 通过设备ID集合获取PSG设备信息集合
     *
     * @param deviceIds
     * @return
     */
    List<AttPsgDeviceSelectItem> getPsgDeviceByDeviceIds(Collection<String> deviceIds);
}
