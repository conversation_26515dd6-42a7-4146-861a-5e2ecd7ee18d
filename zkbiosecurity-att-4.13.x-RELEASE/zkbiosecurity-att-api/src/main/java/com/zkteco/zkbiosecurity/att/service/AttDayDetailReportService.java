package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.AttDayDetailReportItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.util.List;

/**
 * 考勤日报表
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 9:23 2020/8/4
 */
public interface AttDayDetailReportService extends BaseService {

    void modifyItemLabel(Class clazz);

    Pager loadPagerByAuthUserFilter(String sessionId, AttDayDetailReportItem condition, int pageNo, int pageSize, long limitCount);

    List<AttDayDetailReportItem> getDayDetailReportItemData(String sessionId, AttDayDetailReportItem condition, int beginIndex, int endIndex);

    /**
     * 考勤日报表-工作时长表-列表
     */
    Pager loadWorkTimePagerByAuthUserFilter(String sessionId, AttDayDetailReportItem condition, int pageNo, int pageSize, long limitCount);

    /**
     * 考勤日报表-工作时长表-导出
     */
    List<AttDayDetailReportItem> getWorkTimeReportItemData(String sessionId, AttDayDetailReportItem condition, int beginIndex, int endIndex);


    /**
     * 考勤日报表-加班报表-列表
     */
    Pager loadOvertimePagerByAuthUserFilter(String sessionId, AttDayDetailReportItem condition, int pageNo, int pageSize, long limitCount);

    /**
     * 考勤日报表-加班报表-导出
     */
    List<AttDayDetailReportItem> getOvertimeReportItemData(String sessionId, AttDayDetailReportItem condition, int beginIndex, int endIndex);

    /**
     * 考勤日报表-请假报表-列表
     */
    Pager loadLeavePagerByAuthUserFilter(String sessionId, AttDayDetailReportItem condition, int pageNo, int pageSize, long limitCount);

    /**
     * 考勤日报表-请假报表-导出
     */
    List<AttDayDetailReportItem> getLeaveReportItemData(String sessionId, AttDayDetailReportItem condition, int beginIndex, int endIndex);

    /**
     * 考勤日报表-出勤异常表-列表
     */
    Pager loadAbnormalReportPagerByAuthUserFilter(String sessionId, AttDayDetailReportItem condition, int pageNo, int pageSize, long limitCount);

    /**
     * 考勤日报表-出勤异常表-导出
     */
    List<AttDayDetailReportItem> getAbnormalReportItemData(String sessionId, AttDayDetailReportItem condition, int beginIndex, int endIndex);

    /**
     * 考勤日报表-迟到报表-列表
     */
    Pager loadLateReportPagerByAuthUserFilter(String sessionId, AttDayDetailReportItem condition, int pageNo, int pageSize, long limitCount);

    /**
     * 考勤日报表-迟到报表-导出
     */
    List<AttDayDetailReportItem> getLateReportItemData(String sessionId, AttDayDetailReportItem condition, int beginIndex, int endIndex);

    /**
     * 考勤日报表-早退报表-列表
     */
    Pager loadEarlyReportPagerByAuthUserFilter(String sessionId, AttDayDetailReportItem condition, int pageNo, int pageSize, long limitCount);

    /**
     * 考勤日报表-早退报表-导出
     */
    List<AttDayDetailReportItem> getEarlyReportItemData(String sessionId, AttDayDetailReportItem condition, int beginIndex, int endIndex);

    /**
     * 考勤日报表-缺勤报表-列表
     */
    Pager loadAbsentReportPagerByAuthUserFilter(String sessionId, AttDayDetailReportItem condition, int pageNo, int pageSize, long limitCount);

    /**
     * 考勤日报表-缺勤报表-导出
     */
    List<AttDayDetailReportItem> getAbsentReportItemData(String sessionId, AttDayDetailReportItem condition, int beginIndex, int endIndex);

    List<AttDayDetailReportItem> getByCondition(AttDayDetailReportItem condition);
}