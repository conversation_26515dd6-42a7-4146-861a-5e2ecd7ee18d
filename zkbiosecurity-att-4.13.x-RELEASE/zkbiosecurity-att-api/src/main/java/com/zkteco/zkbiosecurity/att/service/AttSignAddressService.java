package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.AttSignAddressItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.List;

/**
 * 签到地址
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:09
 * @since 1.0.0
 */
public interface AttSignAddressService extends BaseService {

    AttSignAddressItem saveItem(AttSignAddressItem item);

    AttSignAddressItem getItemById(String id);

    List<AttSignAddressItem> getByCondition(AttSignAddressItem condition);

    /**
     * 查询
     * 
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager loadPagerByAuthUserFilter(String sessionId, AttSignAddressItem condition, int pageNo, int pageSize);

    /**
     * 查询人员的考勤签到地址
     * 
     * @param personPin
     * @return
     */
    List<AttSignAddressItem> getPersonSignAddress(String personPin);

    /**
     * 获取签到地址全部信息(包括打卡区域名)
     * 
     * <AUTHOR>
     * @since 2019年8月12日 下午2:13:29
     * @return
     */
    AttSignAddressItem getAttSignAddressItemById(String id);

    /**
     * 判断是否在签到范围内
     *
     * @param pin:
     * @return boolean
     * <AUTHOR>
     * @throws
     * @date 2025-03-20 9:55
     * @since 1.0.0
     */
    ZKResultMsg checkSignInRange(String pin, String longitude, String latitude, String mapType);
}
