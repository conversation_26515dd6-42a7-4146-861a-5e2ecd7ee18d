package com.zkteco.zkbiosecurity.att.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.att.bean.AttRuleParamBean;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.vo.AttCycleSchItem;
import com.zkteco.zkbiosecurity.att.vo.AttShiftItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 周期排班（分组/部门/人员）
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 10:34 2020/5/6
 */
public interface AttCycleSchService extends BaseService {

    AttCycleSchItem saveItem(String addIds, AttCycleSchItem item);

    AttCycleSchItem getItemById(String id);

    List<AttCycleSchItem> getByCondition(AttCycleSchItem condition);

    /**
     * 清除周期排班
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/4/26 14:02
     * @param type
     *            分组0/部门1/人员2
     * @param ids
     *            分组ID/部门ID/人员ID
     * @param startDate
     *            搜索开始时间
     * @param endDate
     *            搜索结束时间
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    void delCycleSch(String ids, Short type, Date startDate, Date endDate);

    /**
     * 获取指定日期内的分组/部门/人员排班
     *
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/5/27 9:55
     * @param schType
     *            分组-0、部门-1、人员-2
     * @param startDate
     * @param endDate
     * @return
     */
    List<AttCycleSchItem> getCycleSchList(Short schType, Date startDate, Date endDate);

    /**
     * 获取指定日期内的周期排班(存在shiftIds对应的值)
     *
     * @param startDate:
     * @param endDate:
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttCycleSchItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/9/23 14:32
     * @since 1.0.0
     */
    List<AttCycleSchItem> getCycleSchListByDate(Date startDate, Date endDate);

    /**
     * 获取指定日期内的周期排班(存在shiftIds对应的值)
     *
     * @param condition:
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttCycleSchItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/9/23 15:33
     * @since 1.0.0
     */
    List<AttCycleSchItem> getCycleSchListByCondition(AttCycleSchItem condition);

    /**
     * 根据当前用户加载分页数据
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/25 8:59
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     */
    Pager loadPagerByAuthUserFilter(String sessionId, AttCycleSchItem condition, int pageNo, int pageSize);

    /**
     * 根据当前用户导出数据
     * 
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/29 11:09
     * @param sessionId
     * @param attCycleSchItem
     * @param beginIndex
     * @param endIndex
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttCycleSchItem>
     */
    List<AttCycleSchItem> getItemData(String sessionId, AttCycleSchItem attCycleSchItem, int beginIndex, int endIndex);

    /**
     * 根据排班类型查询周期排班并拆分成天
     *
     * @param cycleType
     *            周期排班类型
     * @param ids
     *            部门ID或分组ID或人员PIN集合
     * @param startDate
     * @param endDate
     * @param attShiftItemMap
     * @param attTimeSlotItemMap
     * @param attRuleParamBean
     * @return
     */
    Map<String, List<AttPersonSchBO>> getCycleSch(Short cycleType, String ids, Date startDate, Date endDate,
        Map<String, AttShiftItem> attShiftItemMap, Map<String, AttTimeSlotItem> attTimeSlotItemMap,
        AttRuleParamBean attRuleParamBean);

    /**
     * 导入人员周期排班
     *
     * @param attCycleSchItemList:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/1/22 10:29
     * @since 1.0.0
     */
    ZKResultMsg importItem(List<AttCycleSchItem> attCycleSchItemList);
}