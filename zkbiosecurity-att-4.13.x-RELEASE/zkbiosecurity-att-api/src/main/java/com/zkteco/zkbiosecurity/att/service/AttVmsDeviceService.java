package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.AttVmsDeviceSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;

import java.util.Collection;
import java.util.List;

/**
 * VMS当考勤
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 14:19
 * @since 1.0.0
 */
public interface AttVmsDeviceService {

    /**
     * 根据设备ID获取设备信息
     * 
     * @param deviceId
     * @return
     */
    AttVmsDeviceSelectItem getVmsDeviceByDeviceId(String deviceId);

    /**
     * 通过设备ID集合获取VMS设备信息集合
     *
     * @param deviceIds
     * @return
     */
    List<AttVmsDeviceSelectItem> getVmsDeviceByDeviceIds(Collection<String> deviceIds);

    /**
     * 查询双列表
     *
     * @param condition
     * @param page
     * @param size
     * @return
     */
    Pager getSelectVmsDevicePager(AttVmsDeviceSelectItem condition, int page, int size);

}