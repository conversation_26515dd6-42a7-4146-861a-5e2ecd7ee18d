package com.zkteco.zkbiosecurity.att.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.vo.AttClassItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 调班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:07
 * @since 1.0.0
 */
public interface AttClassService extends BaseService {

    ZKResultMsg saveItem(AttClassItem item, String saveItem);

    List<AttClassItem> getByCondition(AttClassItem condition);

    AttClassItem getItemById(String id);

    List<AttClassItem> getItemData(String sessionId, AttClassItem attClassItem, int beginIndex, int endIndex);

    boolean existClass(List<String> personIdList, Date startTime, Date endTime);

    /**
     * 根据权限进行调班查询
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/10 18:21
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     */
    Pager loadPagerByAuthUserFilter(String sessionId, AttClassItem condition, int pageNo, int pageSize);

    /**
     * 调班 数据迁移
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/10 18:20
     * @param attClassItems
     * @return void
     */
    void handlerTransfer(List<AttClassItem> attClassItems);

    /**
     * 更新流程状态
     * 
     * @param businessKey
     * @param status
     */
    void updateFlowStatus(String businessKey, String status);

    /**
     * 根据businessKey 查询对象
     * 
     * @param businessKey
     * @return
     */
    AttClassItem getByBusinessKey(String businessKey);

    /**
     * 导入调班
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/15 15:55
     * @param itemList
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    ZKResultMsg importExcel(List<AttClassItem> itemList, String sessionId);

    /**
     * 判断是否存在调班
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/6/15 9:18
     * @param adjustPersonId
     * @param adjustDate
     * @param swapPersonId
     * @param swapDate
     * @return boolean
     */
    boolean existApply(String adjustPersonId, Date adjustDate, String swapPersonId, Date swapDate);

    /**
     * 查询时间范围内的申请记录
     *
     * @param pins
     * @param startDate
     * @param endDate
     * @return
     */
    Map<String, List<AttPersonSchBO>> getClassMap(List<String> pins, Date startDate, Date endDate);

    /**
     * 审批（通过）
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/14 9:20
     * @param ids
     * @return void
     */
    void approval(String ids);

    /**
     * 审批（拒绝）
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/14 9:20
     * @param ids
     * @return void
     */
    void refuse(String ids);
}