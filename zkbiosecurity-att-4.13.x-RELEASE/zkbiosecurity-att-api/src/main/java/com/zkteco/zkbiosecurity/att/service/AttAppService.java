package com.zkteco.zkbiosecurity.att.service;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.base.vo.AppResultMessage;

import java.util.Date;

/**
 * 海外app相关接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/2/19 11:33
 * @since 1.0.0
 */
public interface AttAppService {

    /**
     * 获取人员月考勤数据
     * 
     * @param pin: 
     * @param date: 
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/2/19 15:04
     * @since 1.0.0
     */
    AppResultMessage getAttendanceCalendar(String pin, String date);

    /**
     * 获取人员月统计报表数据
     * 
     * @param pin: 
     * @param startDate: 
     * @param endDate: 
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/2/19 15:04
     * @since 1.0.0
     */
    JSONObject monthlyStatistics(String pin, Date startDate, Date endDate);
}
