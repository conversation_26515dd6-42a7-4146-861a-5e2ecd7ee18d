package com.zkteco.zkbiosecurity.att.service;

import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.att.vo.AttMonthDetailReportItem;
import com.zkteco.zkbiosecurity.att.vo.AttRecordItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;

/**
 * 考勤月报表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:22
 * @since 1.0.0
 */
public interface AttMonthDetailReportService extends BaseService {

    void modifyItemLabel();



    /**
     * 考勤月报表-月考勤状态
     * 
     */
    Pager loadPagerByAuthUserFilter(String sessionId, AttMonthDetailReportItem condition, int pageNo, int pageSize);

    /**
     * 考勤月报表-月考勤状态-导出
     *
     */
    List<AttMonthDetailReportItem> getMonthDetailReportItemData(String sessionId, AttMonthDetailReportItem condition,
        int beginIndex, int endIndex, boolean showColor);

    /**
     * 考勤月报表-月工作时长
     *
     */
    Pager loadMonthWorkTimeReportPager(String sessionId, AttMonthDetailReportItem condition, int pageNo, int pageSize);

    /**
     * 考勤月报表-月工作时长-导出
     *
     */
    List<AttMonthDetailReportItem> getMonthWorkTimeReportItemData(String sessionId, AttMonthDetailReportItem condition,
                                                                int beginIndex, int endIndex);

    /**
     * 考勤月报表-月打卡表
     *
     */
    Pager loadMonthCardReportPager(String sessionId, AttMonthDetailReportItem condition, int pageNo, int pageSize);

    /**
     * 考勤月报表-月打卡表-导出
     *
     */
    List<AttMonthDetailReportItem> getMonthCardReportItemData(String sessionId, AttMonthDetailReportItem condition, int beginIndex, int endIndex);

    /**
     * 考勤月报表-月加班表
     *
     */
    Pager loadMonthOvertimeReportPager(String sessionId, AttMonthDetailReportItem condition, int pageNo, int pageSize);

    /**
     * 考勤月报表-月加班表-导出
     *
     */
    List<AttMonthDetailReportItem> getMonthOvertimeReportItemData(String sessionId, AttMonthDetailReportItem condition, int beginIndex, int endIndex);

    /**
     * 获取假种名称以及报表展示标识
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/7 13:50
     * @param
     * @return java.util.Map<java.lang.String,java.lang.String>
     */
    Map<String, String> getAttSymbolsI18nJson();

}