package com.zkteco.zkbiosecurity.att.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.att.api.vo.*;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 移动端考勤申请（工作流）
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 16:50
 * @since 1.0.0
 */
public interface AttWorkflowService {

    /**
     * 获取人员月累计补签次数
     * 
     * @param personPin
     * @param month
     *            yyyy-MM
     * @return
     */
    Integer getPersonMonthSignCount(String personPin, String month);

    /**
     * 保存补签申请
     * 
     * @param attSignItemList
     */
    void saveSignItem(List<AttCloudSignItem> attSignItemList);

    /**
     * 保存请假申请
     * 
     * <AUTHOR>
     * @since 2019年6月17日 下午4:44:02
     * @param attLeaveItemList
     */

    void saveLeaveItem(List<AttLeaveItem> attLeaveItemList);

    /**
     * 保存加班申请
     * 
     * <AUTHOR>
     * @since 2019年6月17日 下午4:36:45
     * @param attOvertimeItemList
     */
    void saveOvertimeItem(List<AttOvertimeItem> attOvertimeItemList);

    /**
     * 保存出差申请
     * 
     * <AUTHOR>
     * @since 2019年6月17日 下午4:49:22
     * @param attTripItemList
     */
    void saveTripItem(List<AttTripItem> attTripItemList);

    /**
     * 保存外出申请
     * 
     * @param attOutItemList
     */
    void saveOutItem(List<AttOutItem> attOutItemList);

    /**
     * 根据当前用户及审批类型获取第一环节审批人的接口
     * 
     * @param personPin
     * @param flowType
     * @return
     */
    List<AttApiApplyPersonItem> getFirstApprove(String personPin, String flowType);

    /**
     * 根据请假条件获取流程审批人列表
     * 
     * @param personPin
     * @param leaveTypeNo
     * @param startTime
     *            yyyy-MM-dd HH:mm:ss
     * @param endTime
     *            yyyy-MM-dd HH:mm:ss
     * @return
     */
    List<AttApiApplyPersonItem> getLeaveApprove(String personPin, String leaveTypeNo, String startTime, String endTime);

    /**
     * 根据当前用户及申请类型获取知会人列表
     * 
     * @param personPin
     * @param flowType
     * @return
     */
    List<AttApiApplyPersonItem> getFirstNotifier(String personPin, String flowType);

    /**
     * 我的申请
     * 
     * @param personPin
     * @param flowType
     * @param taskStatus
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager findApplyTask(String personPin, String flowType, Integer taskStatus, Integer pageNo, Integer pageSize,
        String filter);

    /**
     * 获取流程信息 by businessKey
     * 
     * @param businessKey
     * @return
     */
    AttApiApplyTaskDetailItem findByBusinessKey(String businessKey);

    /**
     * 获取流程审批信息 by taskId
     * 
     * @param taskId
     * @return
     */
    AttApiApplyTaskDetailItem findByTaskId(String taskId);

    /**
     * 我的审批-待审批
     * 
     * @param personPin
     * @param flowType
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager findPersonalTask(String personPin, String flowType, Integer pageNo, Integer pageSize, String filter);

    /**
     * 我的审批-已审批
     * 
     * @param personPin
     * @param flowType
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager findApprovedTask(String personPin, String flowType, Integer pageNo, Integer pageSize, String filter);

    /**
     * 流程处理-审批动作
     * 
     * @param personPin
     * @param taskId
     * @param approve
     * @param comment
     * @param notifierPins
     */
    void completeTask(String personPin, String taskId, String approve, String comment, String notifierPins);

    /**
     * 我的申请
     * 
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager findMyApplyList(AttFlowTaskItem condition, int pageNo, int pageSize);

    /**
     * 我的待审
     * 
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager findMyApproveList(AttFlowTaskItem condition, int pageNo, int pageSize);

    /**
     * 我的已审
     * 
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager findPersonalHisTask(AttFlowTaskItem condition, int pageNo, int pageSize);

    Map approve(String taskId);

    Map detail(String businessKey);

    Map<String, String> apply(Object o, String personId, String flowType);

    /**
     * 审批任务
     * 
     * @param personId
     * @param taskId
     * @param pass
     * @param notifierPerIds
     */
    void complete(String personId, String taskId, String pass, String notifierPerIds);

    /**
     * 撤销流程
     * 
     * @param businessKey
     * @param personPin
     * @param revokeReason
     */
    ZKResultMsg revokeProcess(String businessKey, String personPin, String revokeReason);

    /**
     * 补签获取当日的排班和打卡记录
     * 
     * @param personPin
     *            人员Pin号
     * @param attDate
     *            补签日期 yyyy-MM-dd
     */
    @Deprecated
    AttApiSignDayValidCardItem getSignDayScheduleAndRecords(String personPin, String attDate);

    /**
     * 补签获取当日的排班
     *
     * @param personPin
     *            人员Pin号
     * @param attDate
     *            补签日期 yyyy-MM-dd
     */
    AttApiSignDayValidCardItem getSignDaySchedule(String personPin, String attDate);

    /**
     * 删除流程实例信息
     * 
     * @param businessKey
     * @return
     */
    ZKResultMsg deleteProcessInstance(String businessKey);

    /**
     * 获取异常申请时长
     * 
     * @param personPin
     *            人员Pin号
     * @param startTime
     *            开始时间 yyyy-MM-dd HH:mm:ss
     * @param endTime
     *            结束时间 yyyy-MM-dd HH:mm:ss
     * @return 时长 (小时)
     */
    @Deprecated
    String calcApplyTimeLong(String personPin, String startTime, String endTime);

    /**
     * 查找人员在对应的时间内是否有其他异常申请
     * 
     * @param personPin
     * @param startTime
     * @param endTime
     * @return
     */
    ZKResultMsg isExistApply(String personPin, Date startTime, Date endTime);

    /**
     * 检查申请时间和上班时间是否有重叠是否允许申请
     * 
     * @param personPin
     * @param flowType
     * @param startTime
     *            yyyy-MM-dd HH:mm
     * @param endTime
     *            yyyy-MM-dd HH:mm
     * @return
     */
    ZKResultMsg checkApplyAndWorkTimeValid(String personPin, String flowType, String startTime, String endTime);
}
