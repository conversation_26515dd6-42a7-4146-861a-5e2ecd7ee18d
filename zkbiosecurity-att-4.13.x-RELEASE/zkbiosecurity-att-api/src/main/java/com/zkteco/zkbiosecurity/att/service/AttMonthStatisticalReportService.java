/**
 * File Name: AttLeave Created by GenerationTools on 2018-02-08 下午08:27 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.AttMonthStatisticalReportItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.util.Date;
import java.util.List;

/**
 * 人员汇总表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:25
 * @since 1.0.0
 */
public interface AttMonthStatisticalReportService extends BaseService {

    /**
     * 动态修改表头
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/5 11:15
     * @param
     * @return void
     */
    void modifyItemLabel();

    /**
     * 根据权限用户查询记录
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinjie.you</a>
     * @date 2018/9/3 15:43
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     */
    Pager loadPagerByAuthUserFilter(String sessionId, AttMonthStatisticalReportItem condition, int pageNo, int pageSize);


    /**
     * 月统计报表导出获取数据
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/29 19:38
     * @param sessionId
     * @param attMonthStatisticalReportItem
     * @param beginIndex
     * @param endIndex
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttMonthStatisticalReportItem>
     */
    List<AttMonthStatisticalReportItem> getMonthStatisticalReportItemData(String sessionId, AttMonthStatisticalReportItem attMonthStatisticalReportItem, int beginIndex, int endIndex);

    /**
     * 月考勤明细
     * 
     * <AUTHOR>
     * @since 2019年7月17日 上午10:48:38
     * @param pin
     * @param monthStart
     * @param monthEnd
     * @return
     */
    AttMonthStatisticalReportItem getItemByPinAndDate(String pin, Date monthStart, Date monthEnd);
}
