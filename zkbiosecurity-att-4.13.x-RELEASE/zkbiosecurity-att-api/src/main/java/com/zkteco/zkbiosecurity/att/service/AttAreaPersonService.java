package com.zkteco.zkbiosecurity.att.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.att.api.vo.AttApiAreaItem;
import com.zkteco.zkbiosecurity.att.api.vo.AttApiAreaPersonItem;
import com.zkteco.zkbiosecurity.att.vo.AttAreaPersonItem;
import com.zkteco.zkbiosecurity.att.vo.AttAuthAreaItem;
import com.zkteco.zkbiosecurity.att.vo.AttPersonSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 区域人员
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 15:47
 * @since 1.0.0
 */
public interface AttAreaPersonService extends BaseService {

    AttAreaPersonItem saveItem(AttAreaPersonItem item);

    List<AttAreaPersonItem> getByCondition(AttAreaPersonItem condition);

    Pager loadPagerByAuthUserFilter(String sessionId, AttAreaPersonItem condition, int pageNo, int pageSize);

    AttAreaPersonItem getItemById(String id);

    /**
     * 同步数据到设备
     *
     * @param areaId:
     * @param ids:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/3/31 15:45
     * @since 1.0.0
     */
    ZKResultMsg syncPerToDev(String areaId, String ids);

    /**
     * 对私短消息
     *
     * @param ids:
     * @param startTime:
     * @param msg:
     * @param min:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/3/31 15:45
     * @since 1.0.0
     */
    ZKResultMsg addSms(String ids, String startTime, String msg, String min);

    /**
     * 为区域添加人员
     *
     * @param personIds:
     * @param areaId:
     * @param devSn:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/3/31 15:46
     * @since 1.0.0
     */
    ZKResultMsg addPerson(String personIds, String areaId, String devSn);

    /**
     * 保存区域人员；同步人员信息，人员生物模板（生物模板从人员再次查询下发）
     *
     * @param personIds:
     * @param areaId:
     * @param devSn:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/3/31 15:46
     * @since 1.0.0
     */
    void syncAttPersonToDevice(String personIds, String areaId, String devSn);

    /**
     * 根据人员获取考勤区域ids
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/7 10:09
     * @param personId
     * @return java.lang.String
     */
    String getAreaIdsByPersonId(String personId);

    /**
     * 获取顶级区域ID用于默认区域
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/7 15:52
     * @param
     * @return java.lang.String
     */
    String getRootAreaId();

    /**
     * 根据人员id获取区域人员
     *
     * @param personIdList:
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttAreaPersonItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/3/31 15:46
     * @since 1.0.0
     */
    List<AttAreaPersonItem> getAreaPersonByPersonIds(Collection<String> personIdList);

    /**
     * 获取考勤区域---API
     * 
     * @auther lambert.li
     * @date 2018/11/19 8:33
     * @param pageNo
     * @param pageSize
     * @return
     */
    List<AttApiAreaItem> getApiAreaList(int pageNo, int pageSize);

    /**
     * 获取考勤区域分页数据
     *
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/11/28 16:50
     * @since 1.0.0
     */
    Pager getApiAreaPager(int pageNo, int pageSize);

    /**
     * 根据code获取区域---API
     * 
     * @auther lambert.li
     * @date 2018/11/19 8:47
     * @param code
     * @return
     */
    AttApiAreaItem getApiAreaByCode(String code);

    /**
     * 添加区域内人员---API
     * 
     * @auther lambert.li
     * @date 2018/11/19 8:55
     * @param areaPerson
     * @return
     */
    ApiResultMessage addApiAreaPerson(AttApiAreaPersonItem areaPerson);

    /**
     * 删除区域内人员---API
     * 
     * @auther lambert.li
     * @date 2018/11/19 8:55
     * @param areaPerson
     * @return
     */
    ApiResultMessage delApiAreaPerson(AttApiAreaPersonItem areaPerson);

    /**
     * 区域人员数据迁移
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/10 18:13
     * @param attAreaPersonItems
     * @return void
     */
    void handlerTransfer(List<AttAreaPersonItem> attAreaPersonItems);

    /**
     * 根据选择的人员，获取考勤区域
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2019/6/10 15:08
     * @param authAreaItem
     * @param pageNo
     * @param pageSize
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     */
    Pager authAreaList(AttAuthAreaItem authAreaItem, int pageNo, int pageSize);

    /**
     ** <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @Description 根据条件查询导出区域人员列表
     * @date 2020/4/17
     **/
    List<AttAreaPersonItem> loadListByAuthFilter(AttAreaPersonItem condition, int beginIndex, int endIndex,
        String sessionId);

    /**
     * 导入区域人员
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/4/20
     * @param itemList
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    ZKResultMsg importAreaPerson(List<AttAreaPersonItem> itemList);

    /**
     * 导入要删除的区域人员
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/4/20
     * @param itemList
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    ZKResultMsg importBatchDel(List<AttAreaPersonItem> itemList);
}