package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.AttBreakTimeItem;
import com.zkteco.zkbiosecurity.att.vo.AttBreakTimeSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.util.List;

/**
 * 休息时间段
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/2 9:13
 * @since 1.0.0
 */
public interface AttBreakTimeService extends BaseService {

    AttBreakTimeItem saveItem(AttBreakTimeItem item);

    AttBreakTimeItem getItemById(String id);

    List<AttBreakTimeItem> getByCondition(AttBreakTimeItem condition);

    /**
     * 选择列表
     * 
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager selectList(String sessionId, AttBreakTimeSelectItem condition, int pageNo, int pageSize);

    /**
     * 判断休息时间段 是否存在外键关联
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/7/3 20:50
     * @param ids
     * @return boolean
     */
    boolean isExistFkData(String ids);

    /**
     * 判断休息时间段的名称是否存在
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/7/31 15:21
     * @param name
     * @return boolean
     */
    boolean existsByName(String name);

    /**
     * 通过开始时间和结束时间查找对象
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/7/31 15:58
     * @param startTime
     * @param endTime
     * @return com.zkteco.zkbiosecurity.att.vo.AttBreakTimeItem
     */
    AttBreakTimeItem getItemByStartTimeAndEndTime(String startTime, String endTime);
}
