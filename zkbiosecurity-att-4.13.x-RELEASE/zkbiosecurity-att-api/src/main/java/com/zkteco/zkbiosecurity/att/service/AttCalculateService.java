package com.zkteco.zkbiosecurity.att.service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import com.zkteco.zkbiosecurity.att.bean.AttCalculationParamsBean;
import com.zkteco.zkbiosecurity.att.calc.bo.AttLeaveBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttOvertimeBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;

/**
 * 考勤计算
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:14
 * @since 1.0.0
 */
public interface AttCalculateService {

    /**
     * 批量计算人员考勤
     *
     * @param calculationParamsBean:
     * @return java.util.concurrent.CompletableFuture
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/13 10:15
     * @since 1.0.0
     */
    CompletableFuture calculateBatchPerson(AttCalculationParamsBean calculationParamsBean);

    /**
     * 实时计算入口
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/26 10:03
     * @param pinList
     * @param beginTime
     * @param endDate
     * @return java.util.concurrent.CompletableFuture
     */
    List<CompletableFuture<Boolean>> realTimeCalculate(List<String> pinList, Date beginTime, Date endDate);
}
