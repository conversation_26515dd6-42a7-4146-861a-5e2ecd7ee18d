package com.zkteco.zkbiosecurity.att.service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.api.vo.AttApiTransactionItem;
import com.zkteco.zkbiosecurity.att.bean.AttUsbTransactionBean;
import com.zkteco.zkbiosecurity.att.vo.AttPhotoExportQueryItem;
import com.zkteco.zkbiosecurity.att.vo.AttTransactionItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 考勤原始记录、打卡记录
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/11/23 11:10
 * @since 1.0.0
 */
public interface AttTransactionService extends BaseService {

    AttTransactionItem saveItem(AttTransactionItem item);

    List<AttTransactionItem> getByCondition(AttTransactionItem condition);

    AttTransactionItem getItemById(String id);

    /**
     * 处理考勤设备上传考勤记录
     *
     * @param sn
     * @param data
     */
    void handlerAttLog(String sn, String data);

    /**
     * 处理批量导出
     *
     * @param condition
     * @param beginIndex
     * @param endIndex
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttTransactionItem>
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/6/29 14:27
     */
    List<?> getItemData(Class<?> cls, BaseItem condition, int beginIndex, int endIndex);

    /**
     * 数据原始记录导出集合处理，具有权限过滤功能
     *
     * @param sessionId 用户的sessionid
     * @param cls 转换返回对象
     * @param condition 参数条件
     * @param beginIndex 开始位置
     * @param endIndex 结束位置
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttTransactionItem>
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/11/9 11:02
     */
    List<AttTransactionItem> exportItemListByAuthFilter(String sessionId, Class<?> cls, AttTransactionItem condition,
        int beginIndex, int endIndex);

    /**
     * 处理考勤设备上传考勤照片
     *
     * @param data
     */
    void handlerAttPhoto(String data);

    /**
     * 根据权限用户查询记录
     *
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager loadPagerByAuthUserFilter(String sessionId, AttTransactionItem condition, int pageNo, int pageSize,
        long limitCount);

    /**
     * 根据权限用户查询记录
     *
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager loadPagerByAuthUserFilter(String sessionId, AttTransactionItem condition, int pageNo, int pageSize);

    /**
     * 获取全部报表数量
     *
     * @return
     */
    Long getAllTransactionCount();

    /**
     * 分页获取云服务所需要的报表数据
     *
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    List<AttTransactionItem> getTransactionCloudItems(AttTransactionItem condition, int pageNo, int pageSize);

    /**
     * 判断考勤记录是否存在
     *
     * @param pin
     * @param attDateTime
     * @return
     */
    int countByPersonPinAndAttDatetime(String pin, Date attDateTime);

    /**
     * 获取考勤事件记录---API
     *
     * @param personPin
     * @param startDate
     * @param endDate
     * @param pageNo
     * @param pageSize
     * @return
     * @auther lambert.li
     * @date 2018/11/19 9:38
     */
    List<AttApiTransactionItem> getApiTransactionList(String personPin, Date startDate, Date endDate, int pageNo,
        int pageSize, boolean isApp);

    /**
     * 获取考勤事件记录
     *
     * @param personPin:
     * @param startDate:
     * @param endDate:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/11/28 16:56
     * @since 1.0.0
     */
    Pager getApiTransactionPager(String personPin, Date startDate, Date endDate, int pageNo, int pageSize);

    /**
     * 产生考勤记录(大掌柜设备当考勤设备)---API
     *
     * @param deviceNum
     * @param pin
     * @param time
     * @return
     * @auther lambert.li
     * @date 2018/11/19 9:38
     */
    ApiResultMessage addApiTransaction(int deviceNum, String pin, String time);

    /**
     * 设备打开记录数据迁移
     *
     * @param attTransactionItems
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/10 11:53
     */
    void handlerTransfer(List<AttTransactionItem> attTransactionItems);

    /**
     * 默认库中，无记录，再次执行，进行库中数据删除，防止数据重复插入 出现 唯一键冲突
     *
     * @param
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/28 20:50
     */
    void deleteDataTransfer();

    /**
     * 获取时间段内的考勤记录总条数
     *
     * @param startTime
     * @param endTime
     * @return java.lang.Long
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/4/22 10:20
     */
    Integer countAttTransaction(Date startTime, Date endTime);

    /**
     * 导入考勤原始数据
     *
     * @param attTransItemList
     * @return
     * <AUTHOR>
     * @version 2019年6月10日 下午2:34:31
     */
    ZKResultMsg importData(List<AttUsbTransactionBean> attTransItemList, String sn) throws Exception;

    /**
     * 判断导入的文件名称sn号是否存在
     *
     * @param sn
     * @return
     * <AUTHOR>
     * @version 2019年6月11日 上午9:33:01
     */
    Boolean validSn(String sn);

    /**
     * 定期清理考勤事件和记录
     *
     * @param paramValue
     * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
     * @since 2019年9月4日 上午10:48:06
     */
    void executeAttTimingDataClean(String paramValue);

    /**
     * 保存云端的打卡事件
     *
     * @param item
     * @return
     */
    AttTransactionItem saveTransactionItem(AttTransactionItem item);

    /**
     * 考勤人员的最早打卡记录
     *
     * @param beginDate
     * @param endDate
     * @param attPersPins
     * @return
     * <AUTHOR>
     * @since 2020年4月28日下午2:19:11
     */
    List<AttTransactionItem> getItemListByTimeAndPersPins(Date beginDate, Date endDate, Collection<String> attPersPins);

    /**
     * 获取人员集合日期范围内的记录
     *
     * @param startDate
     * @param endDate
     * @param pins
     * @return
     */
    Map<String, List<String>> getTransactionMap(Date startDate, Date endDate, List<String> pins);

    /**
     * 根据打卡时间和pin删除原始记录
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/23 17:39
     * @param attDatetime
     * @param personPin
     * @param mark
     * @return void
     */
    void deleteByAttDatetimeAndPinAndmark(Date attDatetime, String personPin, String mark);

    /**
     * 获取考勤照片路径集合
     *
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/6/18 14:15
     * @param sessionId
     * @param condition
     * @return
     **/
    List<AttPhotoExportQueryItem> getAllAttPhotoListByAuthUserFilter(String sessionId,
        AttPhotoExportQueryItem condition);

    /**
     * 拉取第三方记录
     *
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/7/16 14:05
     * @param module
     * @param ids ID集合: 信息屏、人证为设备ID/门禁为门ID/停车为通道ID/VMS为通道ID
     * @param startDateTime
     * @param endDateTime
     * @return: java.util.Date
     **/
    Date pullThirdPartyRecord(String module, String ids, Date startDateTime, Date endDateTime);

    /**
     * 根据模块获取最近记录的时间
     * 
     * @param module
     * @return
     */
    Date getMaxCreateTimeByModule(String module);

    /**
     * 手动同步考勤点记录
     * 
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/10/21 11:46
     * @param attPointIds
     * @param startDatetime
     * @param endDatetime
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    ZKResultMsg syncAttRecord(String attPointIds, Date startDatetime, Date endDatetime);

    /**
     * 根据设备SN获取记录总数
     * 
     * @param devSn:
     * @param startDateTime:
     * @param endDateTime:
     * @return void
     * <AUTHOR>
     * @date 2020-12-10 14:27
     * @since 1.0.0
     */
    Integer countAttTransactionByCreateTime(String devSn, Date startDateTime, Date endDateTime);

    /**
     * 人员签到
     * 
     * @param clientId: 
     * @param pin: 
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/25 17:46
     * @since 1.0.0
     */
    ZKResultMsg signIn(String clientId, String pin, JSONObject data);

    /**
     * 分页获取签到列表
     * 
     * @param condition: 
     * @param pageNo:
     * @param pageSize: 
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/12/26 9:43
     * @since 1.0.0
     */
    Pager getSignInItemsByPage(AttTransactionItem condition, int pageNo, int pageSize);

    void pushAttLateToAcc(String pin, Date attDate,String timeSlotToWorkTime);
}