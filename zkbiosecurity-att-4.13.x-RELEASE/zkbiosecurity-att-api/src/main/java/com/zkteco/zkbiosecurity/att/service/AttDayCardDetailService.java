package com.zkteco.zkbiosecurity.att.service;

import java.util.List;

import com.zkteco.zkbiosecurity.att.vo.AttDayCardDetailItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;

/**
 * 日打卡详情
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 18:27 2020/6/28
 */
public interface AttDayCardDetailService extends BaseService {

    AttDayCardDetailItem getItemById(String id);

    List<AttDayCardDetailItem> getByCondition(AttDayCardDetailItem condition);

    List<AttDayCardDetailItem> getByCondition(AttDayCardDetailItem condition, int beginIndex, int endIndex);

    Pager getItemsByPageByAuthFilter(String sessionId, AttDayCardDetailItem condition, int pageNo, int pageSize);

    List<AttDayCardDetailItem> exportItemListByAuthFilter(String sessionId, AttDayCardDetailItem attDayCardDetailItem,
        int beginIndex, int endIndex);

    /**
     * 定时更新日打卡详情
     *
     * @param
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/6/28 16:11
     */
    void timingUpdateDayCardDetail();

    /**
     * 数据清理
     * 
     * @param keptDate:
     * @return void
     * <AUTHOR>
     * @date 2020-12-29 14:28
     * @since 1.0.0
     */
    void dataClean(String keptDate);
}