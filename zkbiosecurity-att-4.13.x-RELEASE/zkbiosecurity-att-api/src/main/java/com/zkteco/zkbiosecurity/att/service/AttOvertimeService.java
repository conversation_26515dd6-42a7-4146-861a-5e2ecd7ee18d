package com.zkteco.zkbiosecurity.att.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.att.calc.bo.AttOvertimeBO;
import com.zkteco.zkbiosecurity.att.vo.AttOvertimeItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 加班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:30
 * @since 1.0.0
 */
public interface AttOvertimeService extends BaseService {

    AttOvertimeItem saveItem(AttOvertimeItem item, String personIds, String sessionId);

    List<AttOvertimeItem> getByCondition(AttOvertimeItem condition);

    AttOvertimeItem getItemById(String id);

    List<AttOvertimeItem> getItemData(String sessionId, AttOvertimeItem attOvertimeItem, int beginIndex, int endIndex);

    boolean existOvertime(List<String> personIdList, Date startTime, Date endTime);

    /**
     * 根据人员pin查找时间范围内的所有有效申请
     *
     * @param pin
     * @param startTime
     * @param endTime
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/3 10:23
     */
    boolean existOvertime(String pin, Date startTime, Date endTime);

    Pager loadPagerByAuthUserFilter(String sessionId, AttOvertimeItem condition, int pageNo, int pageSize);

    /**
     * 加班数据迁移
     *
     * @param attOvertimeItems
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/11 15:00
     */
    void handlerTransfer(List<AttOvertimeItem> attOvertimeItems);

    AttOvertimeItem saveItem(AttOvertimeItem attOvertimeItem);

    void updateFlowStatus(String businessKey, String status);

    /**
     * 根据businessKey 查询对象
     *
     * @param businessKey
     * @return
     */
    AttOvertimeItem getByBusinessKey(String businessKey);

    /**
     * 更新流程businessKey
     *
     * @param id
     * @param businessKey
     */
    void updateBusinessKeyById(String id, String businessKey);

    /**
     * * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     *
     * @Description 升级到2.2.0R版
     * @date 2019/12/31
     **/
    void upgradeTo_220();

    /**
     * 导入加班
     *
     * @param itemList
     * @param sessionId
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/15 14:27
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    ZKResultMsg importExcel(List<AttOvertimeItem> itemList, String sessionId);

    /**
     * 审批（通过）
     *
     * @param ids
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/14 9:20
     */
    void approval(String ids);

    /**
     * 审批（拒绝）
     *
     * @param ids
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/14 9:20
     */
    void refuse(String ids);

    /**
     * 查询时间范围内的加班申请记录
     *
     * @param pins
     * @param startDate
     * @param endDate
     * @return map(pin = date, List < AttOvertimeBO >)
     */
    Map<String, List<AttOvertimeBO>> getOvertimeMap(List<String> pins, Date startDate, Date endDate);

    /**
     * 是否有员工自助申请
     *
     * @param ids
     * @return
     */
    boolean hasStaffApply(String ids);
}