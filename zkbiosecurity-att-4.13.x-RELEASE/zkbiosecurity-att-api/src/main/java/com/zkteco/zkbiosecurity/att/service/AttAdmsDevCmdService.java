package com.zkteco.zkbiosecurity.att.service;

import java.util.List;

import com.zkteco.zkbiosecurity.att.vo.AttAdmsDevCmdItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 服务器命令
 * 
 * <AUTHOR>
 * @date 2020年4月20日 下午5:59:15
 * @version V1.0
 */
public interface AttAdmsDevCmdService {

    /**
     * 获取考勤服务器命令
     * 
     * <AUTHOR>
     * @since 2020年4月20日下午5:59:03
     * @return
     */
    Pager getAttAdmsDevCmdList(AttAdmsDevCmdItem codition, int pageNo, int pageSize);

    /**
     * 导出考勤服务器命令
     *
     * @param codition:
     * @param beginIndex:
     * @param endIndex:
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttAdmsDevCmdItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/2 9:07
     * @since 1.0.0
     */
    List<AttAdmsDevCmdItem> export(AttAdmsDevCmdItem codition, int beginIndex, int endIndex);

    /**
     * 清空考勤命令
     *
     * @param ids:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/2 9:08
     * @since 1.0.0
     */
    ZKResultMsg cleanAttCmd(String ids);

}
