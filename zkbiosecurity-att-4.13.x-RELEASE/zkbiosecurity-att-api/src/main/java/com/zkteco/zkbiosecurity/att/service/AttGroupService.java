package com.zkteco.zkbiosecurity.att.service;

import java.util.List;

import com.zkteco.zkbiosecurity.att.vo.AttGroupItem;
import com.zkteco.zkbiosecurity.att.vo.AttGroupPersonSelectItem;
import com.zkteco.zkbiosecurity.att.vo.AttPersonItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;

/**
 * 分组
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 18:03
 * @since 1.0.0
 */
public interface AttGroupService extends BaseService {

    AttGroupItem saveItem(AttGroupItem item);

    List<AttGroupItem> getByCondition(AttGroupItem condition);

    AttGroupItem getItemById(String id);

    boolean existsByGroupName(String groupName);

    /**
     * 根据分组ids进行删除涉及到与人事模块的调用
     * 
     * @param ids
     * @return
     */
    boolean deleteByGroup(String ids);

    /**
     * 从AttPerson筛选出未添加到当前分组的人员
     * 
     * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
     * @since 2019年3月22日 下午5:36:36
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager getNoExistPerson(String sessionId, AttGroupPersonSelectItem condition, int pageNo, int pageSize);

    /**
     * 分组数据迁移
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/7 11:32
     * @param attGroupItems
     * @return void
     */
    void handlerTransfer(List<AttGroupItem> attGroupItems);

    /**
     * 人员查询增加权限过滤
     * 
     * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
     * @since 2019年3月26日 下午3:32:01
     * @param sessionId
     * @param condition
     */
    void addCondition(String sessionId, AttPersonItem condition);

    /**
     * 按ID集合查询分组集合
     * 
     * @param ids
     * @return
     */
    List<AttGroupItem> getItemsByIds(String ids);
}