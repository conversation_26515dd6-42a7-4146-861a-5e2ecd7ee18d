package com.zkteco.zkbiosecurity.att.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.att.vo.AttDeviceOpLogItem;

/**
 * 设备操作日志
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:26
 * @since 1.0.0
 */
public interface AttDeviceOpLogService extends BaseService {

    AttDeviceOpLogItem saveItem(AttDeviceOpLogItem item);

    List<AttDeviceOpLogItem> getByCondition(AttDeviceOpLogItem condition);

    AttDeviceOpLogItem getItemById(String id);

    /**
     * 过滤用户的权限
     * 
     * <AUTHOR>
     * @version 2019年6月12日 上午11:44:25
     * @param sessionId
     * @param condition
     * @param page
     * @param size
     * @return
     */
    Pager getItemsByAuthUserPage(String sessionId, BaseItem condition, int page, int size);

    /**
     * 解析设备操作日志并保存
     * 
     * <AUTHOR>
     * @version 2019年6月12日 下午2:06:52
     * @param sn
     * @param dataStr
     */
    void handlerAttDeviceOpLog(String sn, String dataStr);

    /**
     * 查询数据用以导出
     * 
     * <AUTHOR>
     * @since 2020年4月15日下午4:09:10
     * @param targetClass
     * @param codition
     * @param begin
     * @param end
     * @return
     */
    List<AttDeviceOpLogItem> getItemData(Class targetClass, AttDeviceOpLogItem codition, int begin, int end);
}