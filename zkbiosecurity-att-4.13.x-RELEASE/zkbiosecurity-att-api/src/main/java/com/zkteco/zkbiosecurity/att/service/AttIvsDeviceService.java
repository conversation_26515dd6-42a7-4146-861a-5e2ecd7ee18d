package com.zkteco.zkbiosecurity.att.service;

import java.util.Collection;
import java.util.List;

import com.zkteco.zkbiosecurity.att.vo.AttIvsDeviceSelectItem;
import com.zkteco.zkbiosecurity.att.vo.AttIvsParentDeviceItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;

/**
 * 智能视频当考勤、获取智能视频设备信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:04
 * @since 1.0.0
 */
public interface AttIvsDeviceService {

    /**
     * 根据设备ID获取设备信息
     * 
     * @param deviceId
     * @return
     */
    AttIvsDeviceSelectItem getIvsDeviceByDeviceId(String deviceId);

    /**
     * 通过设备ID集合获取设备信息集合
     *
     * @param deviceIds
     * @return
     */
    List<AttIvsDeviceSelectItem> getIvsDeviceByDeviceIds(Collection<String> deviceIds);

    /**
     * 查询双列表
     *
     * @param condition
     * @param page
     * @param size
     * @return
     */
    Pager getSelectIvsDevicePager(AttIvsDeviceSelectItem condition, int page, int size);

    /**
     * 视频模块主设备树
     * 
     * @param sessionId:
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttIvsParentDeviceItem>
     * <AUTHOR>
     * @date 2021-02-04 17:00
     * @since 1.0.0
     */
    List<AttIvsParentDeviceItem> getDeviceTree(String sessionId);
}