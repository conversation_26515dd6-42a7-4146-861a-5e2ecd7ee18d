package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.api.vo.*;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;

/**
 * 北向API接口
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 16:22 2021/11/15
 * @version v1.0
 */
public interface AttApiService {

    /**
     * 获取请假时长
     *
     * @param attApiApplyTimeLongItem:
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/16 9:35
     * @since 1.0.0
     */
    ApiResultMessage getApplyTimeLong(AttApiApplyTimeLongItem attApiApplyTimeLongItem);

    /**
     * 获取假种类型列表
     *
     * @return com.alibaba.fastjson.JSONArray
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/16 9:35
     * @since 1.0.0
     */
    ApiResultMessage getLeaveTypeList();

    /**
     * 请假
     * 
     * @param attApiApplyLeaveItem: 
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/16 9:45
     * @since 1.0.0
     */
    ApiResultMessage applyLeave(AttApiApplyLeaveItem attApiApplyLeaveItem);

    /**
     * 补签
     *
     * @param attApiApplySignItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/16 17:07
     * @since 1.0.0
     */
    ApiResultMessage applySign(AttApiApplySignItem attApiApplySignItem);

    /**
     * 加班
     *
     * @param attApiApplyOvertimeItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/16 17:31
     * @since 1.0.0
     */
    ApiResultMessage applyOvertime(AttApiApplyOvertimeItem attApiApplyOvertimeItem);

    /**
     * 上传请假图片
     * 
     * @param attApiApplyLeaveImageItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/17 14:14
     * @since 1.0.0
     */
    ApiResultMessage uploadLeaveImage(AttApiApplyLeaveImageItem attApiApplyLeaveImageItem);

    /**
     * 保存考勤人员
     * 
     * @param attApiPersonItem: 
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/17 16:09
     * @since 1.0.0
     */
    ApiResultMessage editAttPerson(AttApiPersonItem attApiPersonItem);
}