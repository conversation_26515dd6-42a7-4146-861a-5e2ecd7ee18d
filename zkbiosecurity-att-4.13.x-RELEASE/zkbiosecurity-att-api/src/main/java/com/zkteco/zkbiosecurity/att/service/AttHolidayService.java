package com.zkteco.zkbiosecurity.att.service;

import java.util.Date;
import java.util.List;
import java.util.Set;

import com.zkteco.zkbiosecurity.att.vo.AttHolidayItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;

/**
 * 节假日
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 9:50
 * @since 1.0.0
 */
public interface AttHolidayService extends BaseService {

    AttHolidayItem saveItem(AttHolidayItem item);

    List<AttHolidayItem> getByCondition(AttHolidayItem condition);

    AttHolidayItem getItemById(String id);

    /**
     * 判断节假日名称是否已存在
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/6 16:54
     * @param holidayName
     * @return boolean
     */
    boolean existsByHolidayName(String holidayName);

    /**
     * 节假日数据迁移
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/6 16:54
     * @param holidayItems
     * @return void
     */
    void handlerTransfer(List<AttHolidayItem> holidayItems);

    /**
     * 导出
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/9 14:53
     * @param attHolidayItemClass
     * @param attHolidayItem
     * @param beginIndex
     * @param endIndex
     * @return java.lang.Object
     */
    Object getItemData(Class<AttHolidayItem> attHolidayItemClass, AttHolidayItem attHolidayItem, int beginIndex,
        int endIndex);

    /**
     * 根据权限过滤查询
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/5/24 15:18
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     */
    Pager loadPagerByFilter(String sessionId, AttHolidayItem condition, int pageNo, int pageSize);

    /**
     * 获取所有节假日日期集合
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/6/15 15:22
     * @param
     * @return java.util.List<java.lang.String>
     */
    Set<String> getHolidayDateSet(Date startDatetime, Date endDateTime);

}