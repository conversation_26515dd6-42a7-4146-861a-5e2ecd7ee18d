package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.*;

/**
 * 考勤消息推送中心
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 14:35 2021/8/13
 * @version v1.0
 */
public interface AttMessageCenterService {

    /**
     * 推送请假
     *
     * @param attLeaveItem:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/16 10:50
     * @since 1.0.0
     */
    void pushLeaveMessage(AttLeaveItem attLeaveItem);

    /**
     * 推送请假知会消息
     *
     * @param attLeaveItem:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2025-03-13 16:19
     * @since 1.0.0
     */
    void pushLeaveNotifyMessage(String pin, String title, AttLeaveItem attLeaveItem);

    /**
     * 推送请假待办消息
     *
     * @param attLeaveItem:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2025-03-13 16:18
     * @since 1.0.0
     */
    void pushLeaveTodoMessage(String pin, String businessCode, AttLeaveItem attLeaveItem);

    /**
     * 推送加班
     *
     * @param attOvertimeItem:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/16 10:50
     * @since 1.0.0
     */
    void pushOvertimeMessage(AttOvertimeItem attOvertimeItem);

    /**
     * 推送加班知会消息
     *
     * @param attOvertimeItem:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2025-03-13 16:19
     * @since 1.0.0
     */
    void pushOvertimeNotifyMessage(String pin, AttOvertimeItem attOvertimeItem);

    /**
     * 推送加班待办消息
     *
     * @param attOvertimeItem:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2025-03-13 16:18
     * @since 1.0.0
     */
    void pushOvertimeTodoMessage(String pin, AttOvertimeItem attOvertimeItem);

    /**
     * 推送补签
     *
     * @param attSignItem:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/16 10:50
     * @since 1.0.0
     */
    void pushSignMessage(AttSignItem attSignItem);

    /**
     * 推送补签知会消息
     *
     * @param attSignItem:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2025-03-13 16:19
     * @since 1.0.0
     */
    void pushSignNotifyMessage(String pin, AttSignItem attSignItem);

    /**
     * 推送补签待办消息
     *
     * @param attSignItem:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2025-03-13 16:18
     * @since 1.0.0
     */
    void pushSignTodoMessage(String pin, AttSignItem attSignItem);

    /**
     * 推送调休
     *
     * @param attAdjustItem:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/16 10:50
     * @since 1.0.0
     */
    void pushAdjustMessage(AttAdjustItem attAdjustItem);

    /**
     * 推送调班
     *
     * @param attClassItem:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/16 10:50
     * @since 1.0.0
     */
    void pushClassMessage(AttClassItem attClassItem);
}