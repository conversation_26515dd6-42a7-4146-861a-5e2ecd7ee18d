package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.AttPidDeviceSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.util.Collection;
import java.util.List;

/**
 * 人证当考勤、获取人证设备信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:04
 * @since 1.0.0
 */
public interface AttPidDeviceService {

    AttPidDeviceSelectItem getPidDeviceByDeviceId(String deviceId);

    List<AttPidDeviceSelectItem> getPidDeviceByDeviceIds(Collection<String> deviceIds);

    Pager getSelectPidDevicePager(AttPidDeviceSelectItem conndtion, int page, int size);

}