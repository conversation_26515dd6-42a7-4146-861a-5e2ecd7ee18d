package com.zkteco.zkbiosecurity.att.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.att.bean.AttDXCalendarEventBean;
import com.zkteco.zkbiosecurity.att.bean.AttRuleParamBean;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.vo.AttTempSchItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 临时排班（人员、部门、分组）
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:19
 * @since 1.0.0
 */
public interface AttTempSchService extends BaseService {

    List<AttTempSchItem> getByCondition(AttTempSchItem condition);

    AttTempSchItem getItemById(String id);

    /**
     * 加载页面数据，根据当前用户过滤
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/27 14:22
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     */
    Pager loadPagerByAuthUserFilter(String sessionId, AttTempSchItem condition, int pageNo, int pageSize);

    /**
     * 根据当前用户导出数据
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/29 11:13
     * @param sessionId
     * @param attTempSchItem
     * @param beginIndex
     * @param endIndex
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttTempSchItem>
     */
    List<AttTempSchItem> getItemData(String sessionId, AttTempSchItem attTempSchItem, int beginIndex, int endIndex);

    /**
     * 计算部门排班的数量
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/27 14:06
     * @param deptId
     * @return java.lang.Long
     */
    Long countByDeptId(String deptId);

    /**
     * 临时排班数据迁移
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/11 17:21
     * @param attTempSchItems
     * @return void
     */
    void handlerTransfer(List<AttTempSchItem> attTempSchItems);

    /**
     * 获取指定日期内的分组/部门/人员临时排班
     *
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/5/26 17:54
     * @param schType
     *            分组-0、部门-1、人员-2
     * @param startDate
     * @param endDate
     * @return
     */
    List<AttTempSchItem> getTempSchList(Short schType, Date startDate, Date endDate);

    /**
     * 获取指定日期内的临时排班(存在timeSlotIds的值)
     *
     * @param startDate:
     * @param endDate:
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttTempSchItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/9/23 14:25
     * @since 1.0.0
     */
    List<AttTempSchItem> getTempSchListByDate(Date startDate, Date endDate);
    
    /**
     * 根据条件获取临时排班(存在timeSlotIds的值)
     * 
     * @param condition: 
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttTempSchItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/9/23 16:24
     * @since 1.0.0
     */
    List<AttTempSchItem> getTempSchListByCondition(AttTempSchItem condition);

    /**
     * 保存临时排班
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/7 15:35
     * @param item
     * @param attDXCalendarEventBeanList
     * @return void
     */
    void saveTempSch(String addIds, AttTempSchItem item, List<AttDXCalendarEventBean> attDXCalendarEventBeanList);

    /**
     * 清除临时排班
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/4/26 14:02
     * @param type
     *            分组0/部门1/人员2
     * @param ids
     *            分组ID/部门ID/人员ID
     * @param startDate
     *            搜索开始时间
     * @param endDate
     *            搜索结束时间
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    void delTempSch(String ids, Short type, Date startDate, Date endDate);

    /**
     * 导入临时排班
     * 
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/13 13:40
     * @param attTempSchItemList
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    ZKResultMsg importExcel(List<AttTempSchItem> attTempSchItemList);

    /**
     * 根据排班类型查询临时排班并拆分成天
     *
     * @param tempType
     *            临时排班类型
     * @param ids
     *            部门ID或分组ID或人员PIN集合
     * @param startDate
     * @param endDate
     * @param attTimeSlotItemMap
     * @param attRuleParamBean
     * @return
     */
    Map<String, List<AttPersonSchBO>> getTempSch(Short tempType, String ids, Date startDate, Date endDate,
        Map<String, AttTimeSlotItem> attTimeSlotItemMap, AttRuleParamBean attRuleParamBean);
}