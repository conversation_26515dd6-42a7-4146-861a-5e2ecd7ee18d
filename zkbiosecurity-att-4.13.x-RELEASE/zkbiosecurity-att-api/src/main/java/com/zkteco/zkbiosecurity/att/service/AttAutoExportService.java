package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.AttAutoExportItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.io.IOException;
import java.util.List;

/**
 * 报表推送
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 16:33
 * @since 1.0.0
 */
public interface AttAutoExportService extends BaseService {

    AttAutoExportItem saveItem(AttAutoExportItem item);

    List<AttAutoExportItem> getByCondition(AttAutoExportItem condition);

    AttAutoExportItem getItemById(String id);

    /**
     * 初始化定时自动导出考勤信息到邮箱或FTP
     *
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/2 9:12
     * @since 1.0.0
     */
    void initAutoExport();

    /**
     * 启用
     *
     * @param ids:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/2 9:12
     * @since 1.0.0
     */
    void enable(String ids);

    /**
     * 禁用
     *
     * @param ids:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/2 9:12
     * @since 1.0.0
     */
    void disable(String ids);

    /**
     * 判断是否设置了系统管理里的邮件参数
     *
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/2 9:12
     * @since 1.0.0
     */
    boolean completeMailInfo();

    /**
     * ftp测试连接是否成功
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/4/16 9:03
     * @return void
     */
    boolean ftpTest(String sendFormat, String ftpUrl, int ftpPort, String ftpUsername, String ftpPassword);

}