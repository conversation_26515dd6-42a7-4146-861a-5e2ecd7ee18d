package com.zkteco.zkbiosecurity.att.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.att.vo.AttSignItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 补签
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 15:52
 * @since 1.0.0
 */
public interface AttSignService extends BaseService {

    AttSignItem saveItem(AttSignItem item, String personIds, String sessionId);

    AttSignItem saveItem(AttSignItem attSignItem);

    List<AttSignItem> getByCondition(AttSignItem condition);

    AttSignItem getItemById(String id);

    List<AttSignItem> getItemData(String sessionId, AttSignItem attSignItem, int beginIndex, int endIndex);

    Pager loadPagerByAuthFilter(String sessionId, AttSignItem condition, int pageNo, int pageSize);

    /**
     * 补签单数据迁移
     *
     * @param attSignItems
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/11 15:19
     */
    void handlerTransfer(List<AttSignItem> attSignItems);

    /**
     * 更新流程状态
     *
     * @param businessKey
     * @param status
     */
    void updateFlowStatus(String businessKey, String status);

    /**
     * 根据businessKey查询对象
     *
     * @param businessKey
     * @return
     */
    AttSignItem getByBusinessKey(String businessKey);

    /**
     * 更新流程businessKey
     *
     * @param id
     * @param businessKey
     */
    void updateBusinessKeyById(String id, String businessKey);

    /**
     * 按月统计当月团队总的补签次数和各成员的补签次数
     *
     * @param startDateTime
     * @param endDateTime
     * @param personIds
     * @return
     */
    List<Map<String, Object>> getTeamSignTimes(Date startDateTime, Date endDateTime, String personIds);

    /**
     * 补签获取当日排班和打卡记录
     *
     * @param personId
     * @param date
     * @return
     * <AUTHOR>
     * @since 2019年8月22日 上午10:33:11
     */
    @Deprecated
    ZKResultMsg getSignDayScheduleAndRecords(String personId, String date);

    /**
     * 补签获取当日排班
     *
     * @param personId
     * @param date
     * @return
     * <AUTHOR>
     * @since 2019年8月22日 上午10:33:11
     */
    ZKResultMsg getSignDaySchedule(String personId, String date);

    /**
     * 导入补签单
     *
     * @param itemList
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/11
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    ZKResultMsg importExcel(List<AttSignItem> itemList, String sessionId);

    /**
     * 审批（通过）
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/14 9:20
     * @param ids
     * @return void
     */
    void approval(String ids);

    /**
     * 审批（拒绝）
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/14 9:20
     * @param ids
     * @return void
     */
    void refuse(String ids);

    /**
     * 获取时间范围内的人员补签记录集合
     *
     * @param pins
     * @param startDate
     * @param endDate
     * @return (pin = date, List < String >)
     */
    Map<String, List<String>> getSignMap(List<String> pins, Date startDate, Date endDate);

    /**
     * 是否有员工自助申请
     *
     * @param ids
     * @return
     */
    boolean hasStaffApply(String ids);

    /**
     * 查询人员补签记录(审批中和已审批)
     * 
     * @param pin:
     * @param startDate:
     * @param endDate:
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttSignItem>
     * <AUTHOR>
     * @date 2020-12-11 18:25
     * @since 1.0.0
     */
    List<AttSignItem> getItemByPinAndDate(String pin, Date startDate, Date endDate);

    /**
     * 补签获取班次名称
     *
     * @param attSignItem:
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020-12-25 15:45
     * @since 1.0.0
     */
    String getSignShiftName(AttSignItem attSignItem);
}