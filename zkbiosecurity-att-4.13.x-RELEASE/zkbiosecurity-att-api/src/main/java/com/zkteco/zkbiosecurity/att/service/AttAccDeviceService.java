package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.AttSelectDoorItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;

import java.util.Collection;
import java.util.List;

/**
 * 门禁当考勤
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/2 9:04
 * @since 1.0.0
 */
public interface AttAccDeviceService {

    /**
     * 获取门禁双列表
     *
     * @param condition:
     * @param page:
     * @param size:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/2 9:05
     * @since 1.0.0
     */
    Pager getSelectAccDevicePager(AttSelectDoorItem condition, int page, int size);

    /**
     * 根据门id获取门信息
     *
     * @param deviceId:
     * @return com.zkteco.zkbiosecurity.att.vo.AttSelectDoorItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/2 9:05
     * @since 1.0.0
     */
    AttSelectDoorItem getAccDeviceByDeviceId(String deviceId);

    /**
     * 通过deviceSn和doorNo获取门信息
     *
     * @param deviceSn:
     * @param doorNo:
     * @return com.zkteco.zkbiosecurity.att.vo.AttSelectDoorItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/2 9:05
     * @since 1.0.0
     */
    AttSelectDoorItem getAccDeviceByDevSnAndDoorNo(String deviceSn, Short doorNo);

    /**
     * 根据门id集合获取门信息
     *
     * @param deviceIds:
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttSelectDoorItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/2 9:05
     * @since 1.0.0
     */
    List<AttSelectDoorItem> getAccDeviceByDeviceIds(Collection<String> deviceIds);
}
