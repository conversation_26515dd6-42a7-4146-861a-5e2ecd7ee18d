package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.base.vo.ZKPageResultMsg;

/**
 * 云端消息推送
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:29
 * @since 1.0.0
 */
public interface AttMsgService {

    /**
     * 待办消息
     * 
     * <AUTHOR>
     * @since 2019年8月28日 上午11:20:36
     * @param customerId
     * @param page
     * @param pageSize
     * @param businessCodes
     * @param filter
     * @return
     */
    ZKPageResultMsg getTodoMsg(String customerId, int page, int pageSize, String businessCodes, String filter);

    /**
     * 知会消息
     * 
     * <AUTHOR>
     * @since 2019年8月28日 上午11:20:50
     * @param customerId
     * @param page
     * @param pageSize
     * @param businessCodes
     * @param filter
     * @return
     */
    ZKPageResultMsg getNotifyMsg(String customerId, int page, int pageSize, String businessCodes, String filter);

    /**
     * 设置已读
     * 
     * <AUTHOR>
     * @since 2019年8月28日 上午11:21:12
     * @param messageIds
     * @return
     */
    ZKPageResultMsg changeMsgStatus(String messageIds);

    /**
     * 知会-待办消息统计
     * 
     * <AUTHOR>
     * @since 2019年8月28日 上午11:21:20
     * @param customerId
     * @param businessCodes
     * @return
     */
    ZKPageResultMsg statisticalMsgCount(String customerId, String businessCodes);
}
