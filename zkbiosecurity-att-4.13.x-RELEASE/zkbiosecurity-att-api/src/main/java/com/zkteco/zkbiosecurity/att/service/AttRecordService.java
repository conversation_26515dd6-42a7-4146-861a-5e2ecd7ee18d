package com.zkteco.zkbiosecurity.att.service;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import com.zkteco.zkbiosecurity.att.api.vo.AttApiDayDetailReportItem;
import com.zkteco.zkbiosecurity.att.vo.AttRecordItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * 考勤计算结果、日明细表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:57
 * @since 1.0.0
 */
public interface AttRecordService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    AttRecordItem saveItem(AttRecordItem item);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AttRecordItem> getByCondition(AttRecordItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    AttRecordItem getItemById(String id);

    /**
     * 导出获取数据
     * 
     * @param cls
     * @param condition
     * @param beginIndex
     * @param endIndex
     * @return
     */
    List<?> getItemData(Class<?> cls, BaseItem condition, int beginIndex, int endIndex);

    /**
     * 根据人员编号集合 请调用的时候，注意personPins 超限问题，请调用前自己分批次分割。
     * 
     * @param personPins
     *            人员编号集合
     * @param startDatetime
     *            开始时间
     * @param endDatetime
     *            结束时间
     * @return
     */
    List<AttRecordItem> getByPinsAndAttDate(Collection<String> personPins, Date startDatetime, Date endDatetime);

    List<AttRecordItem> getByDeptsAndAttDate(Collection<String> deptIds, Date startDatetime, Date endDatetime);

    Long getAllRecordCount();

    List<AttRecordItem> getUploadCloudRecord(int pageNo, int pageSize);

    /**
     * 考勤记录数据迁移
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/10 9:10
     * @param attRecordItems
     * @return void
     */
    void handlerTransfer(List<AttRecordItem> attRecordItems);

    /**
     * 删除所有记录
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/8/16 8:43
     * @param
     * @return void
     */
    void deleteDataTransfer();

    /**
     * 根据人员编号和日期获取人员考勤计算结果
     * 
     * @auther lambert.li
     * @date 2019/6/19 16:52
     * @param pin
     * @param dayStart
     * @param dayEnd
     * @return
     */
    AttApiDayDetailReportItem getPersonDetailReport(String pin, Date dayStart, Date dayEnd);

    /**
     * 删除人员集合日期范围内的记录
     * 
     * @param pins
     * @param startDate
     * @param endDate
     */
    void delete(List<String> pins, Date startDate, Date endDate);

    /**
     * 批量保存数据
     * 
     * @param recordItemList
     */
    void save(List<AttRecordItem> recordItemList);

    /**
     * 根据人员pin和日期获取人员当天考勤结果
     * 
     * @param personPin:
     * @param attDate:
     * @return com.zkteco.zkbiosecurity.att.vo.AttRecordItem
     * <AUTHOR>
     * @date 2020-11-27 14:10
     * @since 1.0.0
     */
    AttRecordItem getItemByPinAndDate(String personPin, String attDate);

    /**
     * 删除旧记录，保存新记录
     *
     * @param pinList:
     * @param startDate:
     * @param endDate:
     * @param recordItemList:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/12 18:21
     * @since 1.0.0
     */
    void delAndSaveRecord(List<String> pinList, Date startDate, Date endDate, List<AttRecordItem> recordItemList);

    /**
     * 自定义排序组装，默认都会根据考勤时间排序
     *
     * @param sb:
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/7/11 11:29
     * @since 1.0.0
     */
    String buildOrderBySQL(StringBuffer sb);
}
