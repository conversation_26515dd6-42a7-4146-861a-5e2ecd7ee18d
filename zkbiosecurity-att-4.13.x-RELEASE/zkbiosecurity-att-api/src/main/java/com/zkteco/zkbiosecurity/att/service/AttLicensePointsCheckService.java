package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 考勤许可点数校验
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 10:16 2021/8/25
 * @version v1.0
 */
public interface AttLicensePointsCheckService {

    /**
     * 许可校验
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/25 13:39
     * @since 1.0.0
     */
    ZKResultMsg check();

    /**
     * （必须要在设备新增保存后调用）更新许可
     *
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/25 13:39
     * @since 1.0.0
     */
    boolean update();

//    /**
//     * 考勤点检测
//     *
//     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
//     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
//     * @date 2021/8/25 17:49
//     * @since 1.0.0
//     */
//    ZKResultMsg checkAttPoint(String deviceModule);
//
//    /**
//     * （必须要在考勤点保存后调用）更新许可
//     *
//     * @return void
//     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
//     * @date 2021/8/25 13:39
//     * @since 1.0.0
//     */
//    boolean updateAttPoint(String deviceModule);
}