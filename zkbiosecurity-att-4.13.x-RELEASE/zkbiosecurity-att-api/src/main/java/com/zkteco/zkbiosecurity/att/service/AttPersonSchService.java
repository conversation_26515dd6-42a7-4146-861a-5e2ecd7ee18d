package com.zkteco.zkbiosecurity.att.service;

import java.util.Date;
import java.util.List;

import com.zkteco.zkbiosecurity.att.api.vo.AppAttPersonSchItem;
import com.zkteco.zkbiosecurity.att.api.vo.AttApiTeamSignMonthItem;
import com.zkteco.zkbiosecurity.att.vo.AttAllPersonSchItem;
import com.zkteco.zkbiosecurity.att.vo.AttPersonSchItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.Tree;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 人员排班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:39
 * @since 1.0.0
 */
public interface AttPersonSchService extends BaseService {

    AttPersonSchItem saveItem(AttPersonSchItem item);

    List<AttPersonSchItem> getByCondition(AttPersonSchItem condition);

    AttPersonSchItem getItemById(String id);

    ZKResultMsg getAttPersonSchJson(String personId, String dateSelected);

    /**
     * 删除人员排班
     * 
     * @param scheduleId
     *            班类型组件id
     */
    void deleteByScheduleId(String scheduleId);

    /**
     * 获取所有人员排班列表
     *
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
     * @since 2019年7月19日 下午18:08:30
     * @return
     */
    Pager getAllPersonSch(String sessionId, AttAllPersonSchItem condition, int pageNo, int pageSize);

    /**
     * 根据pin号，判断当日对应的排班及考勤情况
     * 
     * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
     * @since 2019年9月12日 上午11:37:13
     * @param personPin
     * @param dateTime
     * @return
     */
    String isWorkd(String personPin, Date dateTime);

    /**
     * 获取人员当日排班
     *
     * <AUTHOR>
     * @since 2019年8月23日 下午1:51:09
     * @param personId
     * @param date
     * @return
     */
    ZKResultMsg getPersonWorkTime(String personId, String date);

    /**
     * app或小程序考勤日历数据构造
     *
     * @param personPin
     * @param startDateStr
     *            yyyy-MM-dd
     * @param endDateStr
     *            yyyy-MM-dd
     */
    List<AppAttPersonSchItem> getAttCalendarForApp(String personPin, String startDateStr, String endDateStr);
}
