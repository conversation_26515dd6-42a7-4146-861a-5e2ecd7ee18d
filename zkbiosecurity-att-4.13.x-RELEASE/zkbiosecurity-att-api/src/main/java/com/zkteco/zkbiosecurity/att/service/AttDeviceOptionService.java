/**
 * File Name: AccAuxIn Created by GenerationTools on 2018-03-13 下午05:00 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.AttDeviceItem;
import com.zkteco.zkbiosecurity.att.vo.AttDeviceOptionItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 设备参数
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:28
 * @since 1.0.0
 */
public interface AttDeviceOptionService extends BaseService {

    AttDeviceOptionItem saveItem(AttDeviceOptionItem item);

    List<AttDeviceOptionItem> getByCondition(AttDeviceOptionItem condition);

    AttDeviceOptionItem getItemById(String id);

    /**
     * 更新设备参数信息
     *
     * @param attDevice
     * @param key
     * @param s
     */
    @Deprecated
    // 20180910
    void upadateDeviceOption(AttDeviceItem attDevice, String key, String s);

    /**
     * 批量更新设备参数信息
     *
     * @param devSn
     * @param optionsMap
     */
    void updateDeviceOption(String devSn, Map<String, String> optionsMap);

    /**
     * 获取设备参数信息
     *
     * @param devId
     */
    Map<String, String> getOptionMapByDevId(String devId);

    /**
     * 判断设备是否支持某个参数功能
     *
     * @param devId
     * @param devOption
     * @return
     */
    boolean isSupportFunction(String devId, String devOption);

    /**
     * 根据sn判断设备是否支持某个参数功能
     * 
     * @param deviceSn
     * @param devOption
     * @return
     */
    boolean isSupportFunctionBySn(String deviceSn, String devOption);

    /**
     * 判断 attSupportFunList 根据位来判断功能支持参数</br>
     * 0 FingerFunOn，是否支持指纹 0：不支持 1：支持</br>
     * 1 FaceFunOn，是否支持人脸 0：不支持 1：支持</br>
     * 2 PhotoFunOn，是否支持照片 0：不支持 1：支持</br>
     * 3 BioPhotoFun，是否支持比对照片 0：不支持 1：支持</br>
     * 4 BioDataFun，是否支持可见光模板 0：不支持 1：支持</br>
     *
     * @param devSn
     * @param index
     * @return
     */
    boolean isSupportFunList(String devSn, int index);

    /**
     * 根据设备sn和参数名称获取参数值
     *
     * @param devSn
     * @param name
     * @return
     */
    String getValueByDevSnAndName(String devSn, String name);

    void deleteByDevId(String devId);

    /**
     * 获取支持功能(or)的设备SN
     * 
     * @param functions:
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2020-12-10 14:15
     * @since 1.0.0
     */
    List<String> getSupportFunctionsDevSn(List<String> functions);

    /**
     * 判断是否支持口罩或者温度
     *
     * @param sn:
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/8 17:40
     * @since 1.0.0
     */
    boolean isSupportMaskOrTempBySn(String sn);

    /**
     * 根据设备ID，获取ID和DeviceOptionItem对应关系集合
     *
     * @param devIds:
     * @return java.util.Map<java.lang.String,java.util.List<com.zkteco.zkbiosecurity.att.vo.AttDeviceOptionItem>>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/5/26 10:24
     * @since 1.0.0
     */
    Map<String, List<AttDeviceOptionItem>> getDeviceIdAndDeviceOptionMap(Collection<String> devIds);
}