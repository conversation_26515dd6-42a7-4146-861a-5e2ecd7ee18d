package com.zkteco.zkbiosecurity.att.service;

import java.util.List;

import com.zkteco.zkbiosecurity.att.vo.AttShiftItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 班次
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:04
 * @since 1.0.0
 */
public interface AttShiftService extends BaseService {

    AttShiftItem saveItem(AttShiftItem item, String attTimeSlotIds);

    /**
     * 班次设置时间段
     *
     * @param item:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/2/5 15:48
     * @since 1.0.0
     */
    ZKResultMsg saveTimeSlot(AttShiftItem item);

    List<AttShiftItem> getByCondition(AttShiftItem condition);

    AttShiftItem getItemById(String id);

    String getTimeSlotIds(String id);

    /**
     * 获取时间段明细列表
     *
     * @param id:
     * @return java.util.List<java.lang.String>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/1 11:06
     * @since 1.0.0
     */
    List<String> listTsd(String id);

    /**
     * 班次编号是否存在
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/11 14:07
     * @param shiftNo
     * @return boolean
     */
    boolean existsByShiftNo(String shiftNo);

    /**
     * 班次名称是否存在
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/11 14:07
     * @param shiftName
     * @return boolean
     */
    boolean existsByShiftName(String shiftName);

    /**
     * 删除-是否存在外键数据
     *
     * @param ids:
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/1 11:06
     * @since 1.0.0
     */
    boolean isExistFkData(String ids);

    /**
     * 清空时间段的方法
     *
     * @param id:
     * @param idp:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/1 11:06
     * @since 1.0.0
     */
    void cleanShiftTimeSolt(String id, String idp);

    /**
     * 批量清空时间段
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/29 9:29
     * @param ids
     * @return void
     */
    void cleanByShiftIds(String ids);

    List<AttShiftItem> getItemData(Class<AttShiftItem> attShiftItemClass, BaseItem condition, int beginIndex,
        int endIndex);

    /**
     * 班次数
     *
     * @return java.lang.Long
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/1 11:06
     * @since 1.0.0
     */
    Long getAllShiftCount();

    /**
     * 班次数据迁移
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/11 14:06
     * @param attShiftItems
     * @return void
     */
    void handlerTransfer(List<AttShiftItem> attShiftItems);

    /**
     * 处理 班次与时间段关联关系 数据迁移
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/28 19:18
     * @param attShiftItems
     * @return void
     */
    void shiftTimeSlotDataTransfer(List<AttShiftItem> attShiftItems);

    /**
     * 升级到2.2.0R版
     *
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/1 11:05
     * @since 1.0.0
     */
    void upgradeTo_220();

    /**
     * 升级到2.4.0R
     *
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/1 11:05
     * @since 1.0.0
     */
    void upgradeTo_240();
}