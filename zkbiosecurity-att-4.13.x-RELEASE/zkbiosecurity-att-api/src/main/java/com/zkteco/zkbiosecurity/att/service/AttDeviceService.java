package com.zkteco.zkbiosecurity.att.service;

import java.io.File;
import java.util.*;

import com.zkteco.zkbiosecurity.att.vo.AttDeviceCloudItem;
import com.zkteco.zkbiosecurity.att.vo.AttDeviceItem;
import com.zkteco.zkbiosecurity.att.vo.AttSearchDeviceItem;
import com.zkteco.zkbiosecurity.att.vo.AttShortcutKeyInfoItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 考勤设备
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:30
 * @since 1.0.0
 */
public interface AttDeviceService extends BaseService {

    ZKResultMsg saveItem(AttDeviceItem item);

    List<AttDeviceItem> getByCondition(AttDeviceItem condition);

    AttDeviceItem getItemById(String id);

    /**
     * 根据name查询
     *
     * @param name
     * @return
     */
    boolean vaildName(String name);

    /**
     * 根据序列号查询
     * 
     * @param sn
     * @return
     */
    boolean vaildSn(String sn);

    /**
     * 根据ip查询
     * 
     * @param ipAdress
     * @return
     */
    boolean vaildIpAdress(String ipAdress);

    /**
     * 设备启用
     * 
     * @param ids
     * @return
     */
    ZKResultMsg enable(String ids);

    /**
     * 设备禁用
     * 
     * @param ids
     * @return
     */
    ZKResultMsg disable(String ids);

    /**
     * 同步数据到设备
     * 
     * @param ids
     * @return
     */
    ZKResultMsg syncDev(String ids, String isSyncClearData);

    /**
     * 考勤数据校对
     * 
     * @param ids
     *            设备id
     * @param endTime
     *            开始时间
     * @param startTime
     *            结束时间
     * @return
     */
    ZKResultMsg verify(String ids, String startTime, String endTime);

    /**
     * 获取指定人员数据
     * 
     * @param ids
     *            设备Id
     * @param pin
     *            人员pin号
     * @return
     */
    ZKResultMsg getPersonInfo(String ids, String pin);

    /**
     * 重新上传数据
     * 
     * @param id
     *            设备id
     * @param isUploadAttPhoto
     *            是否上传考勤照片
     * @param isUploadOperLog
     *            是否上传人员数据
     * @param isUploadAttLog
     *            是否上传考勤记录
     * @return
     */
    ZKResultMsg reUpload(String ids, String isUploadAttLog, String isUploadOperLog, String isUploadAttPhoto);

    /**
     * 清除设备命令
     * 
     * @param ids
     * @return
     */
    ZKResultMsg deleteCmd(String ids);

    /**
     * 对公短消息
     * 
     * @param ids
     *            设备ID
     * @param min
     *            持续分钟数
     * @param msg
     *            消息内容
     * @param startTime
     *            开始时间
     * @return
     */
    ZKResultMsg addSms(String ids, String startTime, String msg, String min);

    /**
     * 清除考勤照片
     * 
     * @param ids
     * @return
     */
    ZKResultMsg clearAttPic(String ids);

    /**
     * 清除考勤记录
     * 
     * @param ids
     * @return
     */
    ZKResultMsg clearAttLog(String ids);

    /**
     * 清除设备人员
     * 
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/8/7 16:01
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    ZKResultMsg clearAttPers(String ids);

    /**
     * 设备重启
     * 
     * @param ids
     * @return
     */
    ZKResultMsg reboot(String ids);

    /**
     * 获取设备参数
     * 
     * @param ids
     * @return
     */
    ZKResultMsg getDevOpt(String ids);

    /**
     * 获取搜索设备
     * 
     * @return
     */
    List<AttSearchDeviceItem> searchDeviceList();

    /**
     * 获取所有IP，用于前端验证Ip
     * 
     * @return
     */
    Map<String, Collection<String>> getAllIPSn();

    /**
     * adms调用添加授权设备
     * 
     * @param optionMap
     * @return
     */
    Map<String, String> addDevice(Map<String, String> optionMap);

    /**
     * 许可授权设备
     * 
     * @param item
     * @return
     */
    ZKResultMsg authDevice(AttDeviceItem item);

    /**
     * 更新设备主动上传的通信参数统计信息
     * 
     * @param attDeviceItem
     */
    void updateUploadInfo(AttDeviceItem attDeviceItem);

    /**
     *
     * @param devSn
     * @return
     */
    Integer getWaitCmdCount(String devSn);

    /**
     * 更新通信模块发送回来的设备参数信息更新
     * 
     * @param optionMap
     * @return
     */
    boolean updateDeviceOptions(Map<String, String> optionMap);

    /**
     * 判断是否是登记机设备
     * 
     * @param sn
     * @return
     */
    boolean checkIsRegDevice(String sn);

    /**
     * 更新前端界面编辑的设备基础信息
     * 
     * @param id
     * @param devName
     * @param areaId
     * @param timeZone
     * @param isRegDevice
     */
    void updateEditInfo(String id, String devName, String areaId, String timeZone, Boolean isRegDevice);

    boolean checkDeviceStatus(String ids);

    int getDeviceCount();

    Pager getItemsByAuthUserPage(String sessionId, BaseItem condition, int page, int size);

    /**
     * 获取所有考勤设备数量
     * 
     * @return
     */
    Long getAllDeviceCount();

    /**
     * 分页获取云服务所需要的设备数据
     * 
     * <AUTHOR>
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    List<AttDeviceCloudItem> getDeviceCloudItems(AttDeviceCloudItem condition, int pageNo, int pageSize);

    /**
     * 是否是登记机
     * 
     * @param id
     *            设备ID
     * @return
     */
    boolean checkExitIsRegDevice(String id);

    /**
     * 根据sn获取考勤设备信息
     * 
     * @auther lambert.li
     * @date 2018/11/16 8:47
     * @param sn
     * @return
     */
    AttDeviceItem getItemBySn(String sn);

    /**
     * 设备 数据迁移
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/10 19:19
     * @param attDeviceItems
     * @return void
     */
    void handlerTransfer(List<AttDeviceItem> attDeviceItems);

    /**
     * 下发人员到设备
     * 
     * @auther lambert.li
     * @date 2019/9/18 16:05
     * @param personIdList
     * @param attDeviceIdList
     * @return
     */
    void setPersonToDev(List<String> personIdList, List<String> attDeviceIdList);

    /**
     * 获取考勤设备
     * 
     * <AUTHOR>
     * @since 2019-10-11 16:53
     * @Param []
     * @return
     */
    List<AttDeviceItem> getAllItems();

    /**
     * 根据id获取考勤设备数据
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2019-10-24 18:14
     * @param devIdList
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttDeviceItem>
     */
    List<AttDeviceItem> getItemsByIds(Collection<String> devIdList);

    /**
     * 从二号项目迁移代码：获取设备最后推送时间
     * 
     * @auther zbx.zhong
     * @date 2019/6/19 15:44
     * @return
     */
    Long getLastPushTime();

    /**
     * 从二号项目迁移代码：根据最后推送时间获取设备信息上传到云服务
     * 
     * @auther zbx.zhong
     * @date 2019/6/20 15:56
     * @param lastUpdate
     * @return
     */
    List<AttDeviceCloudItem> getUploadCloudDevicesByLastPushTime(Date lastUpdate);

    /**
     * 从二号项目迁移代码：设置设备最后推送时间
     * 
     * @auther zbx.zhong
     * @date 2019/6/20 15:46
     * @return
     */
    void setLastPushTime();

    /**
     * 设备授权区域
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/6/5 9:57
     * @param areaId
     * @param deviceIds
     * @return: void
     **/
    ZKResultMsg authArea(String areaId, String deviceIds);

    /**
     * 考勤设备导出列表
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/6/10 10:32
     * @param attDeviceItem
     * @param beginIndex
     * @param endIndex
     * @param id
     * @return: java.util.List<com.zkteco.zkbiosecurity.att.vo.AttDeviceItem>
     **/
    List<AttDeviceItem> loadListByAuthFilter(AttDeviceItem attDeviceItem, int beginIndex, int endIndex, String id);

    /**
     * 下发固件升级命令
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/7/2 10:40
     * @param devIds
     * @param file
     * @param host
     * @param port
     * @return: java.util.List<java.lang.Long>
     **/
    List<Long> upgradeFirmware(String devIds, File file, String host, int port);

    /**
     * 根据命令id 获取命令返回值
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/7/2 11:43
     * @param cmdId
     * @param timeout
     * @return: java.util.Map<java.lang.String,java.lang.String>
     **/
    Map<String, String> getCmdResultById(long cmdId, int timeout);

    /**
     * 根据区域获取设备列表
     *
     * @param areaId:
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttDeviceItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/9/7 18:25
     * @since 1.0.0
     */
    List<AttDeviceItem> getByAreaId(String areaId);

    /**
     * 更新缓存设备信息统一入口
     *
     * @param attDeviceItem:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/9/7 18:25
     * @since 1.0.0
     */
    AttDeviceItem updateCacheDeviceInfo(AttDeviceItem attDeviceItem);

    /**
     * 判断设备是否在线
     *
     * @param sn:
     * @return java.lang.Boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/18 17:05
     * @since 1.0.0
     */
    boolean isOnline(String sn);

    /**
     * 获取设备状态，0-离线 1-在线 2-禁用
     *
     * @param sn:
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/18 17:10
     * @since 1.0.0
     */
    String getDevStatus(String sn);

    /**
     * 根据人员ID，获取人员对应区域，对应的设备
     *
     * @param personIds:
     * @return java.util.Map<java.lang.String,java.util.Set<AttDevice>>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/5/26 9:13
     * @since 1.0.0
     */
    Map<String, List<AttDeviceItem>> getPinAndDeviceListMap(Collection<String> personIds);

    /**
     * 设置考勤状态键
     *
     * @param attShortcutKeyInfoItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/4/25 20:01
     * @since 1.0.0
     */
    ZKResultMsg setShortcutKey(AttShortcutKeyInfoItem attShortcutKeyInfoItem);

    /**
     * 获取快捷键名称map
     *
     * @return java.util.Map<java.lang.String,java.lang.String>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/4/28 14:03
     * @since 1.0.0
     */
    Map<String, String> getShortcutKeyNameMap();
}