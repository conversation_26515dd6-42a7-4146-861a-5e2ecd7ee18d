package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.AttLeaveSummaryReportItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.Date;
import java.util.List;

/**
 * 请假汇总表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/8/18 11:42
 */
public interface AttLeaveSummaryReportService extends BaseService {

    List<AttLeaveSummaryReportItem> getItemData(String sessionId, AttLeaveSummaryReportItem attLeaveSummaryReportItem,
        int beginIndex, int endIndex);

    ZKResultMsg getLeaveDetail(String personId, String leaveTypeId, Date startTime, Date endTime);

    Pager loadPagerByAuthUserFilter(String sessionId, AttLeaveSummaryReportItem condition, int pageNo, int pageSize);
}