<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd ">
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttAdjustService" ref="attAdjustServiceImpl"/>
    <!--<dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttApplyService" ref="attApplyServiceImpl" />
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttApproveService" ref="attApproveServiceImpl" />-->
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttAreaPersonService"
                   ref="attAreaPersonServiceImpl"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttAutoExportService"
                   ref="attAutoExportServiceImpl"/>
    <!--<dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttBaseDataService" ref="attBaseDataServiceImpl"/>-->
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttCalculateService" ref="attCalculateServiceImpl"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttClassService" ref="attClassServiceImpl"/>
    <!--<dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttCommonSchService" ref="attCommonSchServiceImpl"/>-->
    <!--<dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttDeptSchService" ref="attDeptSchServiceImpl"/>-->
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttDeviceService" ref="attDeviceServiceImpl"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttExceptionDataService"
                   ref="attExceptionDataServiceImpl"/>
    <!--<dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttFlowNodeService" ref="attFlowNodeServiceImpl" />
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttFlowService" ref="attFlowServiceImpl" />-->
    <!--<dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttGroupSchService" ref="attGroupSchServiceImpl"/>-->
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttGroupService" ref="attGroupServiceImpl"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttHolidayService" ref="attHolidayServiceImpl"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttLeaveService" ref="attLeaveServiceImpl"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService" ref="attLeaveTypeServiceImpl"/>
    <!--<dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttOutService" ref="attOutServiceImpl"/>-->
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttOvertimeService" ref="attOvertimeServiceImpl"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttPersonSchService" ref="attPersonSchServiceImpl"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttPersonService" ref="attPersonServiceImpl"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttPointService" ref="attPointServiceImpl"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttRecordService" ref="attRecordServiceImpl"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttRuleService" ref="attRuleServiceImpl"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttShiftService" ref="attShiftServiceImpl"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttSignService" ref="attSignServiceImpl"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttTempSchService" ref="attTempSchServiceImpl"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttTimeSlotService" ref="attTimeSlotServiceImpl"/>
    <!--<dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttTimingService" ref="attTimingServiceImpl" />-->
    <dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttTransactionService"
                   ref="attTransactionServiceImpl"/>
    <!--<dubbo:service interface="com.zkteco.zkbiosecurity.att.service.AttTripService" ref="attTripServiceImpl"/>-->
</beans>