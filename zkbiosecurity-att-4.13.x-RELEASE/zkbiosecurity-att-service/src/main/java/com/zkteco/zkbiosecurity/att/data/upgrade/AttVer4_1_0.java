package com.zkteco.zkbiosecurity.att.data.upgrade;

import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveItem;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * 升级到4.1.0
 * <p>1、新增调班的审批权限</p>
 *
 */
@Slf4j
@Component
public class AttVer4_1_0 implements UpgradeVersionManager {

    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;

    @Override
    public String getModule() {
        return BaseConstants.ATT;
    }

    @Override
    public String getVersion() {
        return "v4.1.0";
    }

    @Override
    public boolean executeUpgrade() {

        // 新增调班的审批权限
        AuthPermissionItem attClassPermission = authPermissionService.getItemByCode("AttClass");
        if (Objects.nonNull(attClassPermission)) {
            AuthPermissionItem attClassApproval = new AuthPermissionItem("AttClassApproval", "att_flow_approve", "att:class:approval",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
            attClassApproval.setParentId(attClassPermission.getId());
            authPermissionService.saveItem(attClassApproval);
        }

        // 新增调班标识
        AttLeaveTypeItem attLeaveTypeItem = attLeaveTypeService.getItemByLeaveTypeNo(AttCalculationConstant.AttAttendStatus.CLASS);
        if (Objects.isNull(attLeaveTypeItem)) {
            AttLeaveTypeItem attClass = new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.CLASS,
                    I18nUtil.i18nCode("att_schedule_classDetail"), false, true, new Double(-1), AttConstant.ATT_CONVERT_UNIT_HOUR,
                    AttConstant.ATT_CONVERT_CARRY, I18nUtil.i18nCode("att_schedule_class"), AttConstant.ATT_CONVERT_MARK_PARAMS);
            attLeaveTypeService.initData(attClass);
        }

        // 调休不展示最小单位
        AttLeaveTypeItem tuneOff = attLeaveTypeService.getItemByLeaveTypeNo(AttCalculationConstant.AttAttendStatus.TUNE_OFF);
        if (Objects.nonNull(tuneOff)) {
            tuneOff.setConvertCount(new Double(-1));
            attLeaveTypeService.initData(tuneOff);
        }

        return true;
    }

}
