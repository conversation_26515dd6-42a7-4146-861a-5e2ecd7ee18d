package com.zkteco.zkbiosecurity.att.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.att.vo.AttDayDetailReportItem;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.att.api.vo.AttApiMonthStatisticalReportItem;
import com.zkteco.zkbiosecurity.att.bean.AttConditionBean;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttRecordDao;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.att.vo.AttMonthStatisticalReportExItem;
import com.zkteco.zkbiosecurity.att.vo.AttMonthStatisticalReportItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.dao.ext.SqlParser;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.pers.service.PersLeavePersonService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonCacheService;
import com.zkteco.zkbiosecurity.pers.vo.PersLeavePersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 人员汇总表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:25
 * @since 1.0.0
 */
@Slf4j
@Service
public class AttMonthStatisticalReportServiceImpl implements AttMonthStatisticalReportService {

    @Autowired
    private AttRecordDao attRecordDao;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;
    @Autowired
    private PersPersonCacheService persPersonCacheService;

    @Override
    public boolean deleteByIds(String arg0) {
        return false;
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int pageNo, int pageSize) {
        Pager pager = new Pager(pageNo, pageSize);
        List<AttMonthStatisticalReportItem> items = queryReportItem(null, (AttMonthStatisticalReportItem)condition,
            AttCommonUtils.getBeginIndex(pageNo, pageSize), pageSize);
        String countSql = new SqlParser().getSmartCountSql(SQLUtil.getSqlByItem(condition));
        List<Object[]> countList = attRecordDao.getArrayData(countSql);
        if (countList != null && countList.size() > 0 && Objects.nonNull(countList.get(0)[0])) {
            pager.setTotal(Integer.valueOf(countList.get(0)[0] + ""));
        }
        pager.setData(items);
        return pager;
    }

    @Override
    public void modifyItemLabel() {

        // 考勤规则最小单位配置
        Map<String, AttLeaveTypeItem> attLeaveTypeItemMap = attLeaveTypeService.getLeaveTypeParamsMap();

        // 应到/实到
        AttLeaveTypeItem actual = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.ACTUAL);
        if (actual != null) {
            // VO对应的字段名称
            String fieldName = "shouldHour";
            String label = attLeaveTypeService.getConvertUnit(actual);
            AttCommonUtils.modifyItemLabelByFieldName(AttMonthStatisticalReportItem.class, fieldName, label);
        }

        // 迟到
        AttLeaveTypeItem late = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.LATE);
        if (late != null) {
            // VO对应的字段名称
            String fieldName = "lateMinute";
            String label = attLeaveTypeService.getConvertUnit(late);
            AttCommonUtils.modifyItemLabelByFieldName(AttMonthStatisticalReportItem.class, fieldName, label);
        }

        // 早退
        AttLeaveTypeItem early = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.EARLY);
        if (early != null) {
            // VO对应的字段名称
            String fieldName = "earlyMinute";
            String label = attLeaveTypeService.getConvertUnit(early);
            AttCommonUtils.modifyItemLabelByFieldName(AttMonthStatisticalReportItem.class, fieldName, label);
        }

        // 加班
        AttLeaveTypeItem overtime = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.OVERTIME);
        if (overtime != null) {
            // VO对应的字段名称
            String fieldName = "overtimeUsualHour";
            String label = attLeaveTypeService.getConvertUnit(overtime);
            AttCommonUtils.modifyItemLabelByFieldName(AttMonthStatisticalReportItem.class, fieldName, label);
        }

        // 异常
        // 旷工
        AttLeaveTypeItem absent = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.ABSENT);
        if (absent != null) {
            // VO对应的字段名称
            String fieldName = "absentHour";
            String label = attLeaveTypeService.getConvertUnit(absent);
            AttCommonUtils.modifyItemLabelByFieldName(AttMonthStatisticalReportItem.class, fieldName, label);
        }
    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, AttMonthStatisticalReportItem condition, int pageNo,
        int pageSize) {
        Pager pager = new Pager(pageNo, pageSize);
        List<AttMonthStatisticalReportItem> items =
            queryReportItem(sessionId, condition, AttCommonUtils.getBeginIndex(pageNo, pageSize), pageSize);
        String countSql = new SqlParser().getSmartCountSql(SQLUtil.getSqlByItem(condition));
        // log.info("Att countSql {}", countSql);
        List<Object[]> countList = attRecordDao.getArrayData(countSql);
        if (countList != null && countList.size() > 0 && Objects.nonNull(countList.get(0)[0])) {
            pager.setTotal(Integer.valueOf(countList.get(0)[0] + ""));
        }
        pager.setData(items);
        return pager;
    }

    @Override
    public List<AttMonthStatisticalReportItem> getMonthStatisticalReportItemData(String sessionId,
        AttMonthStatisticalReportItem condition, int beginIndex, int endIndex) {
        return queryReportItem(sessionId, condition, beginIndex, AttCommonUtils.getPageSize(beginIndex, endIndex));
    }

    /**
     * 大数据航专列统计查询
     */
    private List<AttMonthStatisticalReportItem> queryReportItem(String sessionId,
        AttMonthStatisticalReportItem condition, int beginIndex, int endIndex) {

        // 组装条件过滤
        if (StringUtils.isNotBlank(sessionId)) {
            buildCondition(sessionId, condition);
        }

        Map<String, Object> filedMap = new LinkedHashMap();
        filedMap.put("SHOULD_MINUTE", 0);
        filedMap.put("ACTUAL_MINUTE", 0);
        filedMap.put("VALID_MINUTE", 0);
        filedMap.put("ABSENT_MINUTE", 0);
        filedMap.put("LATE_MINUTE_TOTAL", 0);
        filedMap.put("EARLY_MINUTE_TOTAL", 0);
        filedMap.put("LEAVE_MINUTE", 0);
        filedMap.put("OVERTIME_USUAL_MINUTE", 0);
        filedMap.put("OVERTIME_REST_MINUTE", 0);
        filedMap.put("OVERTIME_HOLIDAY_MINUTE", 0);
        filedMap.put("OVERTIME_MINUTE", 0);
        filedMap.put("LEAVE_DETAILS", "''");
        filedMap.put("SHOULD_MINUTE_EX", 0);
        String sql = AttCommonUtils.buildRecordRowToColumnSql(SQLUtil.getSqlByItem(condition),
            condition.getMonthStart(), condition.getMonthEnd(), filedMap);
        List<Object[]> objectsList = attRecordDao.createSqlQueryPage(sql, beginIndex, endIndex);

        // 大数据统计组装
        List<AttMonthStatisticalReportItem> items = buildReportItem(condition, objectsList, filedMap.size());

        return items;
    }

    /**
     * 数据组装
     */
    private List<AttMonthStatisticalReportItem> buildReportItem(AttMonthStatisticalReportItem condition,
        List<Object[]> objectsList, int filedNum) {

        // 考勤加班等级
        Map<String, String> overtimeParamsMap = attParamService.getOvertimeSetting();

        // 精确小数点位数
        int decimal = Integer.parseInt(attParamService.getDecimal());
        String defaultValue = BigDecimal.ZERO.setScale(decimal) + "";

        // 考勤节假日最小单位配置
        List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getLeaveTypeItems();
        Map<String, AttLeaveTypeItem> leaveTypeItemMap =
            CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getLeaveTypeNo);

        // 考勤规则最小单位配置
        Map<String, AttLeaveTypeItem> leaveTypeParamsMap = attLeaveTypeService.getLeaveTypeParamsMap();
        AttLeaveTypeItem actualType = leaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.ACTUAL);
        AttLeaveTypeItem lateType = leaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.LATE);
        AttLeaveTypeItem earlyType = leaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.EARLY);
        AttLeaveTypeItem overtimeType = leaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.OVERTIME);
        AttLeaveTypeItem absentType = leaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.ABSENT);
        AttLeaveTypeItem tripType = leaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.TRIP);
        AttLeaveTypeItem outType = leaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.OUT);
        // 请假不分类别 统一按天
        // 默认天数转化
        AttLeaveTypeItem defaultLeaveTypeDay = new AttLeaveTypeItem();
        defaultLeaveTypeDay.setConvertUnit(AttConstant.ATT_CONVERT_UNIT_DAY);
        defaultLeaveTypeDay.setConvertType(AttConstant.ATT_CONVERT_CARRY);
        defaultLeaveTypeDay.setConvertCount(0.1);

        // 查询的时间区间
        List<String> days = AttDateUtils.getBetweenDate(AttDateUtils.dateToStrAsShort(condition.getMonthStart()),
            AttDateUtils.dateToStrAsShort(condition.getMonthEnd()));

        List<AttMonthStatisticalReportItem> items = new ArrayList<>();
        for (Object[] objects : objectsList) {

            String pin = AttCommonUtils.getStringValue(objects, 0);
            String lateCount = AttCommonUtils.getStringValue(objects, 1);
            String earlyCount = AttCommonUtils.getStringValue(objects, 2);

            AttMonthStatisticalReportItem item = new AttMonthStatisticalReportItem();
            item.setPin(pin);
            item.setLateCountTotal(lateCount);
            item.setEarlyCount(earlyCount);

            // 先换算再累加
            BigDecimal shouldHourTotal = BigDecimal.ZERO;
            BigDecimal actualHourTotal = BigDecimal.ZERO;
            BigDecimal validHourTotal = BigDecimal.ZERO;
            BigDecimal lateMinuteTotal = BigDecimal.ZERO;
            BigDecimal earlyMinuteTotal = BigDecimal.ZERO;
            BigDecimal overtimeUsualHourTotal = BigDecimal.ZERO;
            BigDecimal overtimeRestHourTotal = BigDecimal.ZERO;
            BigDecimal overtimeHolidayHourTotal = BigDecimal.ZERO;
            BigDecimal overtimeHourTotal = BigDecimal.ZERO;

            // 加班等级
            BigDecimal overTimeOT1Total = BigDecimal.ZERO;
            BigDecimal overTimeOT2Total = BigDecimal.ZERO;
            BigDecimal overTimeOT3Total = BigDecimal.ZERO;

            BigDecimal absentHourTotal = BigDecimal.ZERO;
            BigDecimal leaveTotal = BigDecimal.ZERO;

            Map<String, Object> leaveDetailMap = new HashMap<>();
            for (int i = 0; i < days.size(); i++) {

                Integer shouldMinute = AttCommonUtils.getIntegerValue(objects, i * filedNum + 3);
                Integer actualMinute = AttCommonUtils.getIntegerValue(objects, i * filedNum + 4);
                Integer validMinute = AttCommonUtils.getIntegerValue(objects, i * filedNum + 5);
                Integer absentMinute = AttCommonUtils.getIntegerValue(objects, i * filedNum + 6);
                Integer lateMinute = AttCommonUtils.getIntegerValue(objects, i * filedNum + 7);
                Integer earlyMinute = AttCommonUtils.getIntegerValue(objects, i * filedNum + 8);
                Integer leaveMinute = AttCommonUtils.getIntegerValue(objects, i * filedNum + 9);
                Integer overtimeUsualMinute = AttCommonUtils.getIntegerValue(objects, i * filedNum + 10);
                Integer overtimeRestMinute = AttCommonUtils.getIntegerValue(objects, i * filedNum + 11);
                Integer overtimeHolidayMinute = AttCommonUtils.getIntegerValue(objects, i * filedNum + 12);
                Integer overtimeMinute = AttCommonUtils.getIntegerValue(objects, i * filedNum + 13);
                String leaveDetails = AttCommonUtils.getStringValue(objects, i * filedNum + 14);
                Integer shouldMinuteEx = AttCommonUtils.getIntegerValue(objects, i * filedNum + 15);

                // 组装请假详情
                AttCommonUtils.buildLeaveDetail(leaveDetails, shouldMinute, decimal, leaveTypeItemMap, leaveDetailMap);

                shouldHourTotal = shouldHourTotal
                    .add(AttCommonUtils.convertMinuteToBD(shouldMinute, shouldMinute, actualType, decimal));
                actualHourTotal = actualHourTotal
                    .add(AttCommonUtils.convertMinuteToBD(actualMinute, shouldMinute, actualType, decimal));
                validHourTotal = validHourTotal
                    .add(AttCommonUtils.convertMinuteToBD(validMinute, shouldMinute, actualType, decimal));
                absentHourTotal = absentHourTotal
                    .add(AttCommonUtils.convertMinuteToBD(absentMinute, shouldMinute, absentType, decimal));
                lateMinuteTotal =
                    lateMinuteTotal.add(AttCommonUtils.convertMinuteToBD(lateMinute, shouldMinute, lateType, decimal));
                earlyMinuteTotal = earlyMinuteTotal
                    .add(AttCommonUtils.convertMinuteToBD(earlyMinute, shouldMinute, earlyType, decimal));
                leaveTotal = leaveTotal
                    .add(AttCommonUtils.convertMinuteToBD(leaveMinute, shouldMinute, defaultLeaveTypeDay, decimal));

                Integer should = shouldMinute == 0 ? shouldMinuteEx : shouldMinute;
                BigDecimal overtimeUsual =
                    AttCommonUtils.convertMinuteToBD(overtimeUsualMinute, should, overtimeType, decimal);
                overtimeUsualHourTotal = overtimeUsualHourTotal.add(overtimeUsual);
                BigDecimal overtimeRest =
                    AttCommonUtils.convertMinuteToBD(overtimeRestMinute, should, overtimeType, decimal);
                overtimeRestHourTotal = overtimeRestHourTotal.add(overtimeRest);
                BigDecimal overtimeHoliday =
                    AttCommonUtils.convertMinuteToBD(overtimeHolidayMinute, should, overtimeType, decimal);
                overtimeHolidayHourTotal = overtimeHolidayHourTotal.add(overtimeHoliday);
                overtimeHourTotal = overtimeHourTotal
                    .add(AttCommonUtils.convertMinuteToBD(overtimeMinute, should, overtimeType, decimal));

                // 加班等级
                AttDayDetailReportItem detailReportItem = new AttDayDetailReportItem();
                detailReportItem.setOvertimeUsualConvert(overtimeUsual + "");
                detailReportItem.setOvertimeRestConvert(overtimeRest + "");
                detailReportItem.setOvertimeHolidayConvert(overtimeHoliday + "");
                overTimeOT1Total = overTimeOT1Total.add(AttCommonUtils.convertOverTimeOT(detailReportItem, should, overtimeType,
                    decimal, overtimeParamsMap, "OT1"));
                overTimeOT2Total = overTimeOT2Total.add(AttCommonUtils.convertOverTimeOT(detailReportItem, should, overtimeType,
                    decimal, overtimeParamsMap, "OT2"));
                overTimeOT3Total = overTimeOT3Total.add(AttCommonUtils.convertOverTimeOT(detailReportItem, should, overtimeType,
                    decimal, overtimeParamsMap, "OT3"));
            }

            // 园区带单位
            item.setShould(shouldHourTotal.toString() + getConvertUnit(actualType));
            item.setActual(actualHourTotal + getConvertUnit(actualType));
            item.setValid(validHourTotal + getConvertUnit(actualType));
            item.setLate(lateMinuteTotal + getConvertUnit(lateType));
            item.setEarly(earlyMinuteTotal + getConvertUnit(earlyType));
            item.setAbsent(absentHourTotal + getConvertUnit(absentType));
            item.setOvertimeUsual(overtimeUsualHourTotal + getConvertUnit(overtimeType));
            item.setOvertimeRest(overtimeRestHourTotal + getConvertUnit(overtimeType));
            item.setOvertimeHoliday(overtimeHolidayHourTotal + getConvertUnit(overtimeType));
            item.setOvertime(overtimeHourTotal + getConvertUnit(overtimeType));
            item.setLeave(leaveTotal + getConvertUnit(defaultLeaveTypeDay));
            item.setTrip(MapUtils.getObject(item.getAttMonthDetailReportLeaveHourMap(), tripType.getLeaveTypeName(),
                defaultValue) + getConvertUnit(tripType));
            item.setOut(
                MapUtils.getObject(item.getAttMonthDetailReportLeaveHourMap(), outType.getLeaveTypeName(), defaultValue)
                    + getConvertUnit(outType));

            // 出勤（单位）
            // 应该
            item.setShouldHour(shouldHourTotal.toString());
            // 实际
            item.setActualHour(actualHourTotal.toString());
            // 有效
            item.setValidHour(validHourTotal.toString());
            // 迟到
            item.setLateMinute(lateMinuteTotal.toString());
            // 早退
            item.setEarlyMinute(earlyMinuteTotal.toString());
            // 加班（单位）
            // 平时
            item.setOvertimeUsualHour(overtimeUsualHourTotal.toString());
            // 休息
            item.setOvertimeRestHour(overtimeRestHourTotal.toString());
            // 节日
            item.setOvertimeHolidayHour(overtimeHolidayHourTotal.toString());
            // 合计
            item.setOvertimeHour(overtimeHourTotal.toString());

            // 加班等级
            item.setOverTimeOT1(overTimeOT1Total.toString());
            item.setOverTimeOT2(overTimeOT2Total.toString());
            item.setOverTimeOT3(overTimeOT3Total.toString());

            // 旷工
            item.setAbsentHour(absentHourTotal.toString());
            // 请假详情
            item.setAttMonthDetailReportLeaveHourMap(leaveDetailMap);
            items.add(item);
        }

        buildDeptNamePersonName(items);

        return items;
    }

    /**
     * 组装条件过滤
     */
    private void buildCondition(String sessionId, AttMonthStatisticalReportItem condition) {

        // 员工自助
        String persperPin = attPersonService.getPinByLoginType(sessionId);
        if (persperPin != null) {
            condition.setEquals(true);
            condition.setPin(persperPin);
        } else {

            // 组装部门权限和姓名模糊搜索条件
            AttConditionBean attConditionBean = new AttConditionBean(ZKConstant.TRUE, condition.getLikeName(),
                condition.getDeptId(), condition.getDeptCode(), condition.getDeptName());
            attPersonService.getConditionBySessionId(sessionId, attConditionBean);
            condition.setInPersonPin(attConditionBean.getInPersonPin());
            condition.setDeptId(attConditionBean.getDeptId());
            condition.setInDeptId(attConditionBean.getInDeptId());
        }
    }

    private Map<String, Object> leaveFormatForMonthStatistical(Map<String, AttLeaveTypeItem> attLeaveTypeItemMap,
        List<AttMonthStatisticalReportExItem> leaveRecordList, int decimal) {
        Map<String, Object> map = new HashMap<>();
        for (AttMonthStatisticalReportExItem item : leaveRecordList) {
            Integer shouldMinute = item.getShouldMinute();
            String[] leaveDetails = item.getLeaveDetails().split(",");
            for (String leaveDetail : leaveDetails) {
                if (StringUtils.isNotBlank(leaveDetail)) {
                    String[] leaveDetailArr = leaveDetail.split("-");
                    String leaveTypeNo = leaveDetailArr[0];
                    int num = AttCommonUtils.strToInt(leaveDetailArr[1]);
                    if (map.containsKey(leaveTypeNo)) {
                        BigDecimal nowNum = BigDecimal.valueOf(Double.valueOf(map.get(leaveTypeNo) + ""));
                        String addRet = AttCommonUtils.convertMinute(num, shouldMinute,
                            attLeaveTypeItemMap.get(leaveTypeNo), decimal);
                        BigDecimal addNum = BigDecimal.valueOf(Double.valueOf(addRet));
                        map.put(leaveTypeNo, nowNum.add(addNum).setScale(decimal) + "");
                    } else {
                        map.put(leaveTypeNo, AttCommonUtils.convertMinute(num, shouldMinute,
                            attLeaveTypeItemMap.get(leaveTypeNo), decimal));
                    }
                }
            }
        }

        Map<String, Object> result = new HashMap<>();
        String defaultValue = BigDecimal.ZERO.setScale(decimal) + "";
        for (Map.Entry<String, AttLeaveTypeItem> entry : attLeaveTypeItemMap.entrySet()) {
            AttLeaveTypeItem attLeaveTypeItem = entry.getValue();
            Object value = MapUtils.getObject(map, attLeaveTypeItem.getLeaveTypeNo(), defaultValue);
            result.put(attLeaveTypeItem.getLeaveTypeName(), value);
        }
        return result;
    }

    /**
     * 获取最小单位名称
     *
     * @param attLeaveTypeItem:
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020-11-20 17:42
     * @since 1.0.0
     */
    private String getConvertUnit(AttLeaveTypeItem attLeaveTypeItem) {
        if (Objects.isNull(attLeaveTypeItem) || StringUtils.isBlank(attLeaveTypeItem.getConvertUnit())) {
            return "";
        }
        String convertUnit = "";
        switch (attLeaveTypeItem.getConvertUnit()) {
            case AttConstant.ATT_CONVERT_UNIT_MINUTE:
                convertUnit = I18nUtil.i18nCode("common_minutes");
                break;
            case AttConstant.ATT_CONVERT_UNIT_HOUR:
                convertUnit = I18nUtil.i18nCode("common_hours");
                break;
            case AttConstant.ATT_CONVERT_UNIT_DAY:
                convertUnit = I18nUtil.i18nCode("common_days");
                break;
            default:
        }
        return convertUnit;
    }

    /**
     * 组装人员姓名、部门名称信息
     */
    private void buildDeptNamePersonName(List<AttMonthStatisticalReportItem> itemList) {
        Collection<String> personPinList =
            CollectionUtil.getPropertyList(itemList, AttMonthStatisticalReportItem::getPin, "-1");

        // 根据pin号查找对应的人员列表
        List<PersPersonCacheItem> persPersonItemList =
            persPersonCacheService.getPersonCacheByPins((List<String>)personPinList);
        Map<String, PersPersonCacheItem> persPersonItemMap =
            CollectionUtil.listToKeyMap(persPersonItemList, PersPersonCacheItem::getPin);

        // 根据pin号查找对应的离职人员列表
        List<List<String>> personPinsGroup = CollectionUtil.split(personPinList, CollectionUtil.splitSize);
        List<PersLeavePersonItem> persLeavePersonItemList = new ArrayList<>();
        for (List<String> pins : personPinsGroup) {
            List<PersLeavePersonItem> persLeavePersonItems = persLeavePersonService.getItemByPins(pins);
            persLeavePersonItemList.addAll(persLeavePersonItems);
        }
        Map<String, PersLeavePersonItem> leavePersonItemMap =
            CollectionUtil.listToKeyMap(persLeavePersonItemList, PersLeavePersonItem::getPin);

        itemList.forEach(item -> {
            String personPin = item.getPin();
            if (persPersonItemMap.containsKey(personPin)) {
                PersPersonCacheItem persPersonItem = persPersonItemMap.get(personPin);
                item.setName(persPersonItem.getName());
                item.setLastName(persPersonItem.getLastName());
                item.setDeptName(persPersonItem.getDeptName());
                item.setDeptCode(persPersonItem.getDeptCode());
            } else {
                if (leavePersonItemMap.containsKey(personPin)) {
                    PersLeavePersonItem persLeavePersonItem = leavePersonItemMap.get(personPin);
                    item.setName(persLeavePersonItem.getName());
                    item.setLastName(persLeavePersonItem.getLastName());
                    item.setDeptName(persLeavePersonItem.getDeptName());
                    item.setDeptCode(persLeavePersonItem.getDeptCode());

                }
            }
        });
    }

    @Override
    public AttMonthStatisticalReportItem getItemByPinAndDate(String pin, Date monthStartDate, Date monthEndDate) {
        // 设置查询条件
        AttApiMonthStatisticalReportItem condition = new AttApiMonthStatisticalReportItem();
        condition.setPin(pin);
        condition.setMonthStart(monthStartDate);
        condition.setMonthEnd(monthEndDate);
        condition.setEquals(true);
        // 修改vo查询分组按pin，解决人员变更部门，记录有不同部门，查询拆分部门没有汇总 by ljf 2020/1/13
        List<AttApiMonthStatisticalReportItem> itemList = (List<AttApiMonthStatisticalReportItem>)attRecordDao
            .getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
        if (!CollectionUtil.isEmpty(itemList)) {
            AttApiMonthStatisticalReportItem item = itemList.get(0);
            AttMonthStatisticalReportItem reportItem =
                ModelUtil.copyProperties(item, new AttMonthStatisticalReportItem());
            // 组装人员名称部门姓名时间数据转换
            buildItems(Arrays.asList(reportItem), monthStartDate, monthEndDate);
            return reportItem;
        }
        return null;
    }

    /**
     * 组装人员姓名、部门名称、数据格式转化
     */
    private void buildItems(List<AttMonthStatisticalReportItem> itemList, Date monthStart, Date monthEnd) {

        // 组装人员姓名、部门名称信息
        buildDeptNamePersonName(itemList);

        // 根据人员编号获取对应的记录，组装请假详情
        Collection<String> personPins =
            CollectionUtil.getPropertyList(itemList, AttMonthStatisticalReportItem::getPin, "-1");
        List<List<String>> splitPerssonPins = CollectionUtil.split(personPins, CollectionUtil.splitSize);
        List<AttMonthStatisticalReportExItem> attMonthStatisticalReportExItemList = new ArrayList<>();
        splitPerssonPins.forEach(splitPerssonPin -> {
            // 0、personPin/1、attDate/2、leaveDetails/3、shouldMinute/、4actualMinute/ 5、validMinute/6、absentMinute
            // 7、leaveMinute/8、lateMinuteTotal/9、earlyMinuteTotal/10、overtimeUsualMinute
            // 11、overtimeRestMinute/12、overtimeHolidayMinute/13、OvertimeMinute
            List<Object[]> attRecordObjectList =
                attRecordDao.findByPinsAndAttDateForMonthStatistical(splitPerssonPin, monthStart, monthEnd);
            for (Object[] attRecordObject : attRecordObjectList) {
                AttMonthStatisticalReportExItem item = new AttMonthStatisticalReportExItem();
                item.setPersonPin(attRecordObject[0] + "");
                item.setAttDate((Date)attRecordObject[1]);
                item.setLeaveDetails(Objects.isNull(attRecordObject[2]) ? "" : attRecordObject[2] + "");
                item.setShouldMinute((Integer)attRecordObject[3]);
                item.setActualMinute((Integer)attRecordObject[4]);
                item.setValidMinute((Integer)attRecordObject[5]);
                item.setAbsentMinute((Integer)attRecordObject[6]);
                item.setLeaveMinute((Integer)attRecordObject[7]);
                item.setLateMinuteTotal((Integer)attRecordObject[8]);
                item.setEarlyMinuteTotal((Integer)attRecordObject[9]);
                item.setOvertimeUsualMinute((Integer)attRecordObject[10]);
                item.setOvertimeRestMinute((Integer)attRecordObject[11]);
                item.setOvertimeHolidayMinute((Integer)attRecordObject[12]);
                item.setOvertimeMinute((Integer)attRecordObject[13]);
                item.setShouldMinuteEx((Integer)attRecordObject[14]);
                attMonthStatisticalReportExItemList.add(item);
            }
        });
        Map<String, List<AttMonthStatisticalReportExItem>> pinRecordsMap = new HashMap<>();
        if (!CollectionUtil.isEmpty(attMonthStatisticalReportExItemList)) {
            pinRecordsMap = attMonthStatisticalReportExItemList.stream()
                .collect(Collectors.groupingBy(AttMonthStatisticalReportExItem::getPersonPin));
        }

        // 考勤规则最小单位配置
        Map<String, AttLeaveTypeItem> attLeaveTypeParamsMap = attLeaveTypeService.getLeaveTypeParamsMap();
        // 考勤节假日最小单位配置
        List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getLeaveTypeItems();
        Map<String, AttLeaveTypeItem> attLeaveTypeItemMap =
            CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getLeaveTypeNo);
        // 精确小数点位数
        int decimal = Integer.parseInt(attParamService.getDecimal());
        String defaultValue = BigDecimal.ZERO.setScale(decimal) + "";

        AttLeaveTypeItem actualType = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.ACTUAL);
        AttLeaveTypeItem lateType = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.LATE);
        AttLeaveTypeItem earlyType = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.EARLY);
        AttLeaveTypeItem overtimeType = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.OVERTIME);
        AttLeaveTypeItem absentType = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.ABSENT);
        AttLeaveTypeItem tripType = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.TRIP);
        AttLeaveTypeItem outType = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.OUT);

        for (AttMonthStatisticalReportItem item : itemList) {

            // 组装请假详情
            List<AttMonthStatisticalReportExItem> currentPersonRecords = pinRecordsMap.get(item.getPin());
            List<AttMonthStatisticalReportExItem> leaveRecordList = currentPersonRecords.stream()
                .filter(tmp -> StringUtils.isNotBlank(tmp.getLeaveDetails())).collect(Collectors.toList());
            item.setAttMonthDetailReportLeaveHourMap(
                leaveFormatForMonthStatistical(attLeaveTypeItemMap, leaveRecordList, decimal));

            // 先换算再累加
            BigDecimal shouldHourTotal = BigDecimal.ZERO;
            BigDecimal actualHourTotal = BigDecimal.ZERO;
            BigDecimal validHourTotal = BigDecimal.ZERO;
            BigDecimal lateMinuteTotal = BigDecimal.ZERO;
            BigDecimal earlyMinuteTotal = BigDecimal.ZERO;
            BigDecimal overtimeUsualHourTotal = BigDecimal.ZERO;
            BigDecimal overtimeRestHourTotal = BigDecimal.ZERO;
            BigDecimal overtimeHolidayHourTotal = BigDecimal.ZERO;
            BigDecimal overtimeHourTotal = BigDecimal.ZERO;
            BigDecimal absentHourTotal = BigDecimal.ZERO;
            BigDecimal leaveTotal = BigDecimal.ZERO;

            // 请假不分类别 统一按天
            // 默认天数转化
            AttLeaveTypeItem defaultLeaveTypeDay = new AttLeaveTypeItem();
            defaultLeaveTypeDay.setConvertUnit(AttConstant.ATT_CONVERT_UNIT_DAY);
            defaultLeaveTypeDay.setConvertType(AttConstant.ATT_CONVERT_CARRY);
            defaultLeaveTypeDay.setConvertCount(0.1);

            for (AttMonthStatisticalReportExItem attRecordItem : currentPersonRecords) {
                // 应上分钟数
                Integer shouldMinute = Optional.ofNullable(attRecordItem.getShouldMinute()).orElse(0);
                Integer shouldMinuteEx = Optional.ofNullable(attRecordItem.getShouldMinuteEx()).orElse(0);

                leaveTotal = leaveTotal.add(AttCommonUtils.convertMinuteToBD(attRecordItem.getLeaveMinute(),
                    shouldMinute, defaultLeaveTypeDay, decimal));

                shouldHourTotal = shouldHourTotal
                    .add(AttCommonUtils.convertMinuteToBD(shouldMinute, shouldMinute, actualType, decimal));
                actualHourTotal = actualHourTotal.add(AttCommonUtils.convertMinuteToBD(attRecordItem.getActualMinute(),
                    shouldMinute, actualType, decimal));
                validHourTotal = validHourTotal.add(AttCommonUtils.convertMinuteToBD(attRecordItem.getValidMinute(),
                    shouldMinute, actualType, decimal));
                lateMinuteTotal = lateMinuteTotal.add(AttCommonUtils
                    .convertMinuteToBD(attRecordItem.getLateMinuteTotal(), shouldMinute, lateType, decimal));
                earlyMinuteTotal = earlyMinuteTotal.add(AttCommonUtils
                    .convertMinuteToBD(attRecordItem.getEarlyMinuteTotal(), shouldMinute, earlyType, decimal));

                Integer should = shouldMinute == 0 ? shouldMinuteEx : shouldMinute;
                overtimeUsualHourTotal = overtimeUsualHourTotal.add(AttCommonUtils
                    .convertMinuteToBD(attRecordItem.getOvertimeUsualMinute(), should, overtimeType, decimal));
                overtimeRestHourTotal = overtimeRestHourTotal.add(AttCommonUtils
                    .convertMinuteToBD(attRecordItem.getOvertimeRestMinute(), should, overtimeType, decimal));
                overtimeHolidayHourTotal = overtimeHolidayHourTotal.add(AttCommonUtils
                    .convertMinuteToBD(attRecordItem.getOvertimeHolidayMinute(), should, overtimeType, decimal));
                overtimeHourTotal = overtimeHourTotal.add(AttCommonUtils
                    .convertMinuteToBD(attRecordItem.getOvertimeMinute(), should, overtimeType, decimal));

                absentHourTotal = absentHourTotal.add(AttCommonUtils.convertMinuteToBD(attRecordItem.getAbsentMinute(),
                        shouldMinute, absentType, decimal));
            }

            // 园区带单位
            item.setShould(shouldHourTotal.toString() + getConvertUnit(actualType));
            item.setActual(actualHourTotal + getConvertUnit(actualType));
            item.setValid(validHourTotal + getConvertUnit(actualType));
            item.setLate(lateMinuteTotal + getConvertUnit(lateType));
            item.setEarly(earlyMinuteTotal + getConvertUnit(earlyType));
            item.setAbsent(absentHourTotal + getConvertUnit(absentType));
            item.setOvertimeUsual(overtimeUsualHourTotal + getConvertUnit(overtimeType));
            item.setOvertimeRest(overtimeRestHourTotal + getConvertUnit(overtimeType));
            item.setOvertimeHoliday(overtimeHolidayHourTotal + getConvertUnit(overtimeType));
            item.setOvertime(overtimeHourTotal + getConvertUnit(overtimeType));
            item.setLeave(leaveTotal + getConvertUnit(defaultLeaveTypeDay));
            item.setTrip(MapUtils.getObject(item.getAttMonthDetailReportLeaveHourMap(), tripType.getLeaveTypeName(),
                defaultValue) + getConvertUnit(tripType));
            item.setOut(
                MapUtils.getObject(item.getAttMonthDetailReportLeaveHourMap(), outType.getLeaveTypeName(), defaultValue)
                    + getConvertUnit(outType));

            // 出勤（单位）
            // 应该
            item.setShouldHour(shouldHourTotal.toString());
            // 实际
            item.setActualHour(actualHourTotal.toString());
            // 有效
            item.setValidHour(validHourTotal.toString());
            // 迟到
            item.setLateMinute(lateMinuteTotal.toString());
            // 早退
            item.setEarlyMinute(earlyMinuteTotal.toString());
            // 加班（单位）
            // 平时
            item.setOvertimeUsualHour(overtimeUsualHourTotal.toString());
            // 休息
            item.setOvertimeRestHour(overtimeRestHourTotal.toString());
            // 节日
            item.setOvertimeHolidayHour(overtimeHolidayHourTotal.toString());
            // 合计
            item.setOvertimeHour(overtimeHourTotal.toString());
            // 旷工
            item.setAbsentHour(absentHourTotal.toString());
        }
    }
}
