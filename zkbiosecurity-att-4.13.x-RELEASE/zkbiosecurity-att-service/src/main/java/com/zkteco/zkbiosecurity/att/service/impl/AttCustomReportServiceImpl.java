package com.zkteco.zkbiosecurity.att.service.impl;


import com.zkteco.zkbiosecurity.att.service.AttCustomReportService;
import com.zkteco.zkbiosecurity.att.vo.AttCustomReportItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.system.service.BaseCustomReportService;
import com.zkteco.zkbiosecurity.system.vo.BaseCustomReportItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 考勤自定义报表
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 17:48 2023/7/18
 * @version v1.0
 */

@Service
public class AttCustomReportServiceImpl implements AttCustomReportService {

    @Autowired
    private BaseCustomReportService baseCustomReportService;

    @Override
    public void saveItem(AttCustomReportItem item) {
        BaseCustomReportItem baseCustomReportItem = new BaseCustomReportItem();
        ModelUtil.copyProperties(item, baseCustomReportItem);
        baseCustomReportService.saveItem(baseCustomReportItem);
    }

    @Override
    public Pager getItemsByPage(AttCustomReportItem condition, int pageNo, int pageSize) {
        condition.setModuleCode(ConstUtil.SYSTEM_MODULE_ATT);
        BaseCustomReportItem baseCustomReportItem = new BaseCustomReportItem();
        ModelUtil.copyProperties(condition, baseCustomReportItem);
        Pager pager = baseCustomReportService.getItemsByPage(baseCustomReportItem, pageNo, pageSize);
        List<BaseCustomReportItem> baseCustomReportItemList = (List<BaseCustomReportItem>)pager.getData();
        List<AttCustomReportItem> attCustomReportItemList = ModelUtil.copyListProperties(baseCustomReportItemList, AttCustomReportItem.class);
        pager.setData(attCustomReportItemList);
        return pager;
    }
}

