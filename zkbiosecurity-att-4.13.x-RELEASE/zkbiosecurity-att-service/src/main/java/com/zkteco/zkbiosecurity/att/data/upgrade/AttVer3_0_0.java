package com.zkteco.zkbiosecurity.att.data.upgrade;

import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.bean.AttDXCalendarEventBean;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttCommonSchConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.constants.AttShiftConstant;
import com.zkteco.zkbiosecurity.att.dao.*;
import com.zkteco.zkbiosecurity.att.model.*;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.utils.AttShiftSchUtils;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.service.AuthRoleService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.jdbc.JdbcOperateTemplate;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.LocaleMessageSourceUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 16:40 2020/4/21
 */
@Slf4j
@Component
public class AttVer3_0_0 implements UpgradeVersionManager {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private AttLeaveTypeDao attLeaveTypeDao;
    @Autowired
    private AttTripDao attTripDao;
    @Autowired
    private AttOutDao attOutDao;
    @Autowired
    private AttLeaveDao attLeaveDao;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AttHolidayDao attHolidayDao;
    @Autowired
    private AttGroupSchDao attGroupSchDao;
    @Autowired
    private AttDeptSchDao attDeptSchDao;
    @Autowired
    private AttCycleSchService attCycleSchService;
    @Autowired
    private AttLeaveService attLeaveService;
    @Autowired
    private AttHolidayService attHolidayService;
    @Autowired
    private AttTimeSlotService attTimeSlotService;
    @Autowired
    private AttTempSchDao attTempSchDao;
    @Autowired
    private AttTempSchService attTempSchService;
    @Autowired
    private AttTimeSlotDao attTimeSlotDao;
    @Autowired
    private AttShiftDao attShiftDao;
    @Autowired
    private AuthRoleService authRoleService;
    @Autowired
    private JdbcOperateTemplate jdbcOperateTemplate;

    @Override
    public String getVersion() {
        return "v3.0.0";
    }

    @Override
    public String getModule() {
        return BaseConstants.ATT;
    }

    @Override
    public boolean executeUpgrade() {

        // 菜单升级
        upgradeMenu();

        // 删除原始记录的唯一键
        upgradeTransaction();

        // 新增字段dayNumber，根据旧数据开始结束时间计算dayNumber天数
        upgradeHoliday();

        // 新增字段convertCount、convertUnit、convertType、symbol、mark升级保存默认值
        upgradeLeaveType();

        // 新增字段maxBeforeOvertimeMinutes、maxAfterOvertimeMinutes升级保存默认值
        upgradeTimeSlot();

        // 新增AttCycleSCh,AttGroupSch、AttDeptSch表数据迁移到AttCycleSch
        upgradeCycleSch();

        // 升级AttTempleSch
        upgradeTempSch();

        // 出差、外出表数据 保存到请假表
        upgradeLeave();

        // 请假文件路径长度升级
        jdbcOperateTemplate.alterTableCharLen("ATT_LEAVE", "LEAVE_IMAGE_PATH", "1000");

        return true;
    }

    private void upgradeTempSch() {

        // 先删除中间表的外键约束
        try {

            jdbcTemplate.execute("ALTER TABLE ATT_TEMPSCH_SHIFT DROP CONSTRAINT fkd8vl8mbglgo79e6hli3b88a5j");
            jdbcTemplate.execute("ALTER TABLE ATT_TEMPSCH_SHIFT DROP CONSTRAINT fkpmxm46gm65a833vpuqy563q8m");
        } catch (DataAccessException e) {
            log.error("att_tempsch_shift constraint is already delete ", e);
        }
        // 数据拷贝外键
        try {
            jdbcTemplate.execute("ALTER TABLE ATT_TEMPSCH_SHIFT DROP CONSTRAINT att_tempsch_shift_shift_id_fkey");
            jdbcTemplate.execute("ALTER TABLE ATT_TEMPSCH_SHIFT DROP CONSTRAINT att_tempsch_shift_tempsch_id_fkey");
        } catch (DataAccessException e) {
            log.error("att_tempsch_shift constraint is already delete ", e);
        }

        // 查询所有临时排班
        List<AttTempSch> attTempSchList = attTempSchDao.findAll();
        if (attTempSchList != null) {

            // 查询所有班次，避免在for循环中查询数据库
            List<AttShift> attShiftList = attShiftDao.findAll();
            Map<String, AttShift> attShiftMap = CollectionUtil.listToKeyMap(attShiftList, AttShift::getId);

            for (AttTempSch attTempSch : attTempSchList) {

                // 基本信息拷贝
                AttTempSchItem attTempSchItem = new AttTempSchItem();
                ModelUtil.copyProperties(attTempSch, attTempSchItem);

                // 根据临时车排班ID查询中间表对应的班次信息，并将班次转时间段处理
                List<AttDXCalendarEventBean> attDXCalendarEventBeanList = new ArrayList<>();
                List<String> shiftIdList = null;
                try {
                    shiftIdList = attTempSchDao.findShiftIdByTempSchId(attTempSchItem.getId());
                } catch (Exception e) {
                    log.error("upgradeTempSch att_tempsch_shift does not exist ", e);
                }
                if (shiftIdList != null && shiftIdList.size() > 0) {
                    for (String shiftId : shiftIdList) {
                        if (attShiftMap.containsKey(shiftId)) {
                            AttShift attShift = attShiftMap.get(shiftId);
                            // 新版本没有临时弹性排班，所以升级过滤不处理。
                            if (AttShiftConstant.ShiftType.FLEXIBLE_SHIFT == attShift.getShiftType()) {
                                continue;
                            }
                            attTempSchItem.setWorkType(attShift.getWorkType());
                            attTempSchItem.setAttendanceMode(attShift.getAttendanceMode());
                            attTempSchItem.setOvertimeMode(attShift.getOvertimeMode());
                            AttShiftItem attShiftItem = ModelUtil.copyProperties(attShift, new AttShiftItem());
                            // 给班次填充时间段
                            AttDateUtils.forEachDay(attTempSchItem.getStartDate(), attTempSchItem.getEndDate(),
                                date -> {
                                    List<String> timeSlot = AttShiftSchUtils.getTimeSlotByShiftAndDate(attShiftItem,
                                        date, attTempSchItem.getStartDate());
                                    // 时间段未空则不保存
                                    if (timeSlot != null && timeSlot.size() > 0) {
                                        AttDXCalendarEventBean attDXCalendarEventBean = new AttDXCalendarEventBean();
                                        attDXCalendarEventBean.setStart_date(date);
                                        attDXCalendarEventBean.setEnd_date(date);
                                        attDXCalendarEventBean.setAttTimeSlotIds(StringUtils.join(timeSlot, ","));
                                        attDXCalendarEventBeanList.add(attDXCalendarEventBean);
                                    }
                                });
                        }
                    }

                    // 更新临时排班
                    attTempSchService.saveTempSch(null, attTempSchItem, attDXCalendarEventBeanList);
                    // 删除中间表数据
                    attTempSchDao.deleteByTempSchId(attTempSchItem.getId());
                }
            }
        }
        log.info("upgradeTempSch success");
    }

    /**
     * 新增AttCycleSCh,将AttGroupSch、AttDeptSch表数据迁移到AttCycleSch 删除旧数据
     *
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/23 11:16
     * @since 1.0.0
     */
    private void upgradeCycleSch() {

        // 查询所有分组周期排班
        List<AttGroupSch> attGroupSchList = attGroupSchDao.findAll();
        if (attGroupSchList != null && attGroupSchList.size() > 0) {
            for (AttGroupSch attGroupSch : attGroupSchList) {
                AttCycleSchItem attCycleSchItem = new AttCycleSchItem();
                attCycleSchItem.setCycleType(AttCommonSchConstant.SCH_TYPE_GROUP);
                attCycleSchItem.setStartDate(attGroupSch.getStartDate());
                attCycleSchItem.setEndDate(attGroupSch.getEndDate());
                attCycleSchItem.setGroupId(attGroupSch.getGroupId());
                attCycleSchItem.setScheduleType(attGroupSch.getScheduleType());
                Set<AttShift> attShiftSet = attGroupSch.getAttShiftSet();
                attCycleSchItem.setShiftIds(CollectionUtil.getPropertys(attShiftSet, AttShift::getId));
                attCycleSchService.saveItem(attGroupSch.getGroupId(), attCycleSchItem);
            }
            // 迁移之后删除分组周期排班
            // attGroupSchDao.delete(attGroupSchList);
            attGroupSchDao.deleteAll(attGroupSchList);
        }

        // 查询所有部门周期排班
        List<AttDeptSch> attDeptSchList = attDeptSchDao.findAll();
        if (attDeptSchList != null && attDeptSchList.size() > 0) {
            for (AttDeptSch attDeptSch : attDeptSchList) {
                AttCycleSchItem attCycleSchItem = new AttCycleSchItem();
                attCycleSchItem.setCycleType(AttCommonSchConstant.SCH_TYPE_DEPT);
                attCycleSchItem.setStartDate(attDeptSch.getStartDate());
                attCycleSchItem.setEndDate(attDeptSch.getEndDate());
                attCycleSchItem.setDeptId(attDeptSch.getDeptId());
                attCycleSchItem.setScheduleType(attDeptSch.getScheduleType());
                Set<AttShift> attShiftSet = attDeptSch.getAttShiftSet();
                attCycleSchItem.setShiftIds(CollectionUtil.getPropertys(attShiftSet, AttShift::getId));
                attCycleSchService.saveItem(attDeptSch.getDeptId(), attCycleSchItem);
            }
            // 迁移之后删除部门周期排班
            // attDeptSchDao.delete(attDeptSchList);
            attDeptSchDao.deleteAll(attDeptSchList);
        }
        log.info("upgradeCycleSch success");
    }

    /**
     * 新增字段maxBeforeOvertimeMinutes、maxAfterOvertimeMinutes升级保存默认值
     *
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/23 10:56
     * @since 1.0.0
     */
    private void upgradeTimeSlot() {
        jdbcTemplate.execute(
            "UPDATE ATT_TIMESLOT SET MAX_AFTER_OVERTIME_MINUTES = 0 , MAX_BEFORE_OVERTIME_MINUTES = 0 WHERE MAX_AFTER_OVERTIME_MINUTES is null");
        log.info("upgradeTransaction success");
    }

    /**
     * 新增字段convertCount、convertUnit、convertType、symbol、mark升级保存默认值
     *
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/23 10:37
     * @since 1.0.0
     */
    private void upgradeLeaveType() {

        // 查询所有假种
        List<AttLeaveType> attLeaveTypeList = attLeaveTypeDao.findAll();
        if (attLeaveTypeList != null) {
            List<AttLeaveType> upgradeLeaveTypeList = new ArrayList<>();
            for (AttLeaveType attLeaveType : attLeaveTypeList) {
                // 判断新增字段是否有值，没有值需要默认赋值
                if (StringUtils.isBlank(attLeaveType.getConvertUnit())) {
                    attLeaveType.setConvertCount(new Double(1));
                    attLeaveType.setConvertType(AttConstant.ATT_CONVERT_ROUNDING);
                    attLeaveType.setConvertUnit(AttConstant.ATT_CONVERT_UNIT_MINUTE);
                    attLeaveType.setMark(AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
                    upgradeLeaveTypeList.add(attLeaveType);
                }
            }
            if (upgradeLeaveTypeList.size() > 0) {
                attLeaveTypeDao.saveAll(upgradeLeaveTypeList);
            }
        }
    }

    /**
     * 根据旧数据开始结束时间计算dayNumber天数
     *
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/23 10:09
     * @since 1.0.0
     */
    private void upgradeHoliday() {

        // 查询所有节假日
        List<AttHoliday> attHolidayList = attHolidayDao.findAll();
        if (attHolidayList != null) {
            for (AttHoliday attHoliday : attHolidayList) {
                // 判断新增字段是否有值，没有值需要默认赋值
                if (attHoliday.getDayNumber() == null) {
                    AttHolidayItem attHolidayItem = new AttHolidayItem();
                    ModelUtil.copyProperties(attHoliday, attHolidayItem);
                    int diffDays =
                        AttDateUtils.diffDays(attHoliday.getStartDatetime(), attHoliday.getEndDatetime()) + 1;
                    attHolidayItem.setDayNumber((short)diffDays);
                    attHolidayService.saveItem(attHolidayItem);
                }
            }
        }
        log.info("upgradeHoliday success");
    }

    /**
     * 出差、外出表数据，保存到请假表 删除旧数据
     *
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/22 15:13
     * @since 1.0.0
     */
    private void upgradeLeave() {

        // 查询所有出差申请
        AttLeaveType leaveTypeTrip = attLeaveTypeDao.findByLeaveTypeNo(AttCalculationConstant.AttAttendStatus.TRIP);
        List<AttTrip> allTripList = attTripDao.findAll();
        if (allTripList != null) {
            for (AttTrip attTrip : allTripList) {
                AttLeaveItem attLeaveItem = new AttLeaveItem();
                ModelUtil.copyPropertiesIgnoreId(attTrip, attLeaveItem);
                attLeaveItem.setLeaveLong(attTrip.getTripLong());
                attLeaveItem.setLeaveTypeId(leaveTypeTrip.getId());
                attLeaveService.saveItem(attTrip.getPersonId(), attLeaveItem, null);
            }
            // 删除旧数据
            attTripDao.deleteAll(allTripList);
        }

        // 查询所有外出申请
        AttLeaveType leaveTypeOut = attLeaveTypeDao.findByLeaveTypeNo(AttCalculationConstant.AttAttendStatus.OUT);
        List<AttOut> allOutList = attOutDao.findAll();
        if (allOutList != null) {
            for (AttOut attOut : allOutList) {
                AttLeaveItem attLeaveItem = new AttLeaveItem();
                ModelUtil.copyPropertiesIgnoreId(attOut, attLeaveItem);
                attLeaveItem.setLeaveLong(attOut.getOutLong());
                attLeaveItem.setLeaveTypeId(leaveTypeOut.getId());
                attLeaveService.saveItem(attOut.getPersonId(), attLeaveItem, null);
            }
            // 删除旧数据
            attOutDao.deleteAll(allOutList);
        }
        log.info("upgradeLeave success");
    }

    /**
     * 删除原始记录的唯一键
     *
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/22 15:33
     * @since 1.0.0
     */
    private void upgradeTransaction() {
        try {
            jdbcTemplate.execute("ALTER TABLE att_transaction DROP CONSTRAINT att_tran_pin_datetime_idx");
        } catch (Exception e) {
            log.error("att_transaction att_tran_pin_datetime_idx is already delete ", e);
        }
        log.info("upgradeTransaction success");
    }

    /**
     * 删除原始记录的唯一键
     *
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/22 15:33
     * @since 1.0.0
     */
    private void upgradeMenu() {

        // 《按人员设置》设为无效
        setUnavailableByCode("AttPersonArea");

        // 《自定义规则》设为无效
        setUnavailableByCode("AttCustomRule");

        // 《参数设置》设为无效
        setUnavailableByCode("AttParam");

        // 《班次管理》设置成无效
        setUnavailableByCode("AttShiftManagement");

        // 《分组排班》设置无效
        setUnavailableByCode("AttGroupSch");

        // 《部门排班》设置无效
        setUnavailableByCode("AttDeptSch");

        // 《临时排班》设置无效
        setUnavailableByCode("AttTempSch");

        // 《人员排班》的<新增临时排班>设置成无效
        setUnavailableByCode("AttPersonSchAddTempSch");

        // 《未排班人员》设置无效
        setUnavailableByCode("AttNonSch");

        // 国内《调班》设置无效
        if ("zh_CN".equals(LocaleMessageSourceUtil.language)) {
            setUnavailableByCode("AttClass");
        }

        // 《年度统计表（按人）》设置无效
        setUnavailableByCode("AttYearStatisticalReport");

        // 将工作流的流程设置菜单放到考勤设置菜单下
        AuthPermissionItem parentMenuItem = authPermissionService.getItemByCode("AttBasicInformation");
        AuthPermissionItem wfFlowableList = authPermissionService.getItemByCode("WfFlowableList");
        if (Objects.nonNull(parentMenuItem) && Objects.nonNull(wfFlowableList)) {
            wfFlowableList.setParentId(parentMenuItem.getId());
            wfFlowableList.setOrderNo(6);
            authPermissionService.saveItem(wfFlowableList);
        }

        // 《流程管理》设置无效
        setUnavailableByCode("WfFlowableManagement");

        // 《出勤异常表》设置无效
        setUnavailableByCode("AttWorkAbnormalReport");

        // 禁用功能菜单：导入门禁记录、导入停车记录、导入信息屏记录、导入人证记录、导入VMS记录
        setUnavailableByCode("AttTransactionImportAccRecord");
        setUnavailableByCode("AttTransactionParkRecord");
        setUnavailableByCode("AttTransactionImportInsRecord");
        setUnavailableByCode("AttTransactionImportPidRecord");
        setUnavailableByCode("AttTransactionImportVmsRecord");

        // 员工自助新增报表明细权限
        AuthPermissionItem attDetailReport = authPermissionService.getItemByCode("AttDetailReport");
        if (attDetailReport != null) {
            authRoleService.authRolePermission("employee", Arrays.asList(attDetailReport.getId()));
        }

        // 禁用功能菜单：导入门禁记录、导入停车记录、导入信息屏记录、导入人证记录、导入VMS记录
        setUnavailableByCode("AttTransactionImportAccRecord");
        setUnavailableByCode("AttTransactionParkRecord");
        setUnavailableByCode("AttTransactionImportInsRecord");
        setUnavailableByCode("AttTransactionImportPidRecord");
        setUnavailableByCode("AttTransactionImportVmsRecord");

        // 添加原始记录表 同步考勤点记录 按钮权限
        AuthPermissionItem buttonMenuItem = authPermissionService.getItemByCode("AttTransactionSyncRecord");
        if (Objects.isNull(buttonMenuItem)) {
            AuthPermissionItem childMenuItem = authPermissionService.getItemByCode("AttTransaction");
            if (Objects.nonNull(childMenuItem)) {
                buttonMenuItem = new AuthPermissionItem("AttTransactionSyncRecord", "att_transaction_SyncRecord",
                    "att:transaction:SyncRecord", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 10);
                buttonMenuItem.setParentId(childMenuItem.getId());
                authPermissionService.saveItem(buttonMenuItem);
            }
        }

        log.info("upgradeMenu success");
    }

    /**
     * 将菜单设置成无效
     *
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/22 14:03
     * @since 1.0.0
     */
    private void setUnavailableByCode(String code) {
        AuthPermissionItem menu = authPermissionService.getItemByCode(code);
        if (menu != null && ZKConstant.TRUE.equals(menu.getAvailable())) {
            menu.setAvailable(ZKConstant.FALSE);
            authPermissionService.saveItem(menu);
        }
    }
}
