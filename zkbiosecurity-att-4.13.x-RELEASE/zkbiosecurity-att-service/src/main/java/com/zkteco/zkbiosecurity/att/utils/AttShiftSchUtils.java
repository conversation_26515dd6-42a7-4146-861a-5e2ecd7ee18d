package com.zkteco.zkbiosecurity.att.utils;

import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;

import org.apache.commons.lang3.StringUtils;

import com.zkteco.zkbiosecurity.att.calc.bo.AttTimeSlotBO;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.constants.AttShiftConstant;
import com.zkteco.zkbiosecurity.att.model.AttShift;
import com.zkteco.zkbiosecurity.att.model.AttTimeSlot;
import com.zkteco.zkbiosecurity.att.vo.AttShiftItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 
 * 班次排班工具
 * 
 * @version 0.0.1
 * @since 2017年9月12日 下午2:16:56
 */
@Component
public class AttShiftSchUtils {

    private static String language;

    @Value("${system.language}")
    public void setLanguage(String language) {
        AttShiftSchUtils.language = language;
    }

    /**
     * 获取班次的该天attDate的时间段 (班次某天的包含时间段, 比如周一 9-18, 周末 空)
     *
     * @param attShiftItem
     * @param attDate
     * @param schStartDate
     * @return
     */
    public static List<String> getTimeSlotByShiftAndDate(AttShiftItem attShiftItem, Date attDate, Date schStartDate) {
        String timeSlotDetailIds = attShiftItem.getTimeSlotDetailIds();
        if (StringUtils.isBlank(timeSlotDetailIds)) {
            return new ArrayList<>();
        }
        int mod = 0;
        Short periodicUnit = attShiftItem.getPeriodicUnit();
        switch (periodicUnit) {
            case AttShiftConstant.CycleUnit.DAY:
                mod = countDayMod(attShiftItem, attDate, schStartDate);
                break;
            case AttShiftConstant.CycleUnit.WEEK:
                mod = countWeekMod(attShiftItem, attDate, schStartDate);
                break;
            case AttShiftConstant.CycleUnit.MONTH:
                mod = countMonthMod(attShiftItem, attDate, schStartDate);
                break;
            default:
        }

        String[] timeSlotDetailIdArr = timeSlotDetailIds.split(",");
        List<String> timeSlotIdList = new ArrayList<>();
        for (String timeSlotDetailId : timeSlotDetailIdArr) {
            if (StringUtils.isNotBlank(timeSlotDetailId) && timeSlotDetailId.endsWith("_" + String.valueOf(mod))) {
                String[] timeSlotIdAndIndex = timeSlotDetailId.split("_");
                timeSlotIdList.add(timeSlotIdAndIndex[0]);
            }
        }

        return timeSlotIdList;
    }

    /**
     * 按天为周期的取模
     *
     * @param attShiftItem 班次对象
     * @param attDate 计算日期
     * @param schStartDate 排班开始日期
     * @return
     */
    public static int countDayMod(AttShiftItem attShiftItem, Date attDate, Date schStartDate) {

        String periodStartMode = attShiftItem.getPeriodStartMode();
        if (StringUtils.isBlank(periodStartMode)) {
            periodStartMode = AttConstant.ATT_PERIODSTARTMODE_BYSCH;
        }
        Date startDate = null;
        switch (periodStartMode) {
            // 按周期起始日期
            case AttConstant.ATT_PERIODSTARTMODE_BYPERIOD:
                startDate = attShiftItem.getStartDate();
                break;
            // 按排班开始日期
            case AttConstant.ATT_PERIODSTARTMODE_BYSCH:
                startDate = schStartDate;
                break;
            default:
                break;
        }
        if (startDate == null) {
            return 0;
        }
        int diffDay = (int)((attDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

        // 月内循环
        if (attShiftItem.getIsShiftWithinMonth()) {
            // 判断是否同个月份
            int diffMonth = AttDateUtils.getMonth(startDate.getTime(), attDate.getTime());
            if (diffMonth != 0) {
                // 不是则根据月内循环规则,取得该天为当月第几天
                diffDay = AttDateUtils.getDayOfMonth(attDate.getTime()) - 1;
            }
        }
        int dayMod = Math.floorMod(diffDay, attShiftItem.getPeriodNumber());
        return dayMod;
    }

    /**
     * 按周为周期的取模
     *
     * @param attShiftItem
     * @param attDate
     * @return
     */
    public static int countWeekMod(AttShiftItem attShiftItem, Date attDate, Date schStartDate) {

        // 前端可以选择周期起始日期 - lgq 2024-02-26
        String periodStartMode = attShiftItem.getPeriodStartMode();
        if (StringUtils.isBlank(periodStartMode)) {
            periodStartMode = AttConstant.ATT_PERIODSTARTMODE_BYSCH;
        }
        Date startDate = null;
        switch (periodStartMode) {
            // 按周期起始日期
            case AttConstant.ATT_PERIODSTARTMODE_BYPERIOD:
                startDate = attShiftItem.getStartDate();
                break;
            // 按排班开始日期
            case AttConstant.ATT_PERIODSTARTMODE_BYSCH:
                startDate = schStartDate;
                break;
            default:
                break;
        }
        if (startDate == null) {
            return 0;
        }

        // 格式转化
        startDate = new Date(startDate.getTime());
        attDate = new Date(attDate.getTime());

        LocalDate date1 = (startDate.toInstant()).atZone(ZoneId.systemDefault()).toLocalDate();
        date1 = date1.with(DayOfWeek.MONDAY);
        LocalDate date2 = (attDate.toInstant()).atZone(ZoneId.systemDefault()).toLocalDate();
        date2 = date2.with(DayOfWeek.MONDAY);
        int weekOfYear = (int)ChronoUnit.WEEKS.between(date1, date2);

        // 海外处理,周日-周六为一周的情况
        if (!"zh_CN".equals(language)) {
            // 如果从周日开始排班,周日就是第一周,周一到周六也是第一周,所以这边周数减一
            int startDateDayOfWeek = AttDateUtils.getDayOfWeek(startDate.getTime());
            if (startDateDayOfWeek == 1) {
                weekOfYear = weekOfYear - 1;
            }
            // 如果当前日期是周日,那么就是下一周的开始,所以这边要加一
            int attDateDayOfWeek = AttDateUtils.getDayOfWeek(attDate.getTime());
            if (attDateDayOfWeek == 1) {
                weekOfYear = weekOfYear + 1;
            }
        }

        // 周几
        int dayOfWeek = AttDateUtils.getDayOfWeek(attDate.getTime()) - 1;
        if (dayOfWeek > 0) {
            dayOfWeek = dayOfWeek - 1;
        } else {
            dayOfWeek = dayOfWeek + 6;
        }

        // 第几周
        int modWeek = weekOfYear % attShiftItem.getPeriodNumber();

        // 偏移值
        int mod = modWeek * 7 + dayOfWeek;

        return mod;
    }

    public static Instant toInstant(Date date) {
        Instant instant = null;
        try {
            // 尝试将 Date 转换为 Instant
            instant = date.toInstant();
        } catch (UnsupportedOperationException e) {
            // 如果不支持 toInstant() 方法，手动转换
            instant = ZonedDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault()).toInstant();
        } catch (NullPointerException e) {
            // 捕获并打印异常信息
            e.printStackTrace();
        }
        return instant;
    }

    /**
     * 按月为周期的取模
     *
     * @param attShiftItem
     * @param attDate
     * @return
     */
    public static int countMonthMod(AttShiftItem attShiftItem, Date attDate, Date schStartDate) {

        // 前端可以选择周期起始日期 - lgq 2024-02-26
        String periodStartMode = attShiftItem.getPeriodStartMode();
        if (StringUtils.isBlank(periodStartMode)) {
            periodStartMode = AttConstant.ATT_PERIODSTARTMODE_BYSCH;
        }
        Date startDate = null;
        switch (periodStartMode) {
            // 按周期起始日期
            case AttConstant.ATT_PERIODSTARTMODE_BYPERIOD:
                Date shiftStartDate = attShiftItem.getStartDate();
                // 兼容处理升级问题
                if (shiftStartDate == null) {
                    shiftStartDate = schStartDate;
                }
                startDate = shiftStartDate;
                break;
            // 按排班开始日期
            case AttConstant.ATT_PERIODSTARTMODE_BYSCH:
                startDate = schStartDate;
                break;
            default:
                break;
        }

        // 周期起始是当年第几个月
        int startDiffMonth = DateUtil.getMonth(startDate);
        // 当前日期是当年第几个月
        int currDiffMonth = DateUtil.getMonth(attDate);
        int diffMonth = Math.abs(currDiffMonth - startDiffMonth);

        // 当前日期是当月的第几天
        int dayOfMonth = AttDateUtils.getDayOfMonth(attDate.getTime()) - 1;
        // 除以周期数，取模
        int modMonth = diffMonth % attShiftItem.getPeriodNumber();
        int mod = modMonth * 31 + dayOfMonth;
        return mod;
    }

    /**
     * 放入班次Map
     */
    private static void putAttShiftSchMap(Map<String, Map<Long, List<String>>> attShiftSchDataMap, AttShift attShift,
        Long attDateLong, int modCommon) {
        String attTimeSlotId = null;
        List<String> attTimeSlotIdList = new ArrayList<String>();

        List<String> attTimeSlotDetailIdList = null;
        // 防止 timeSlotDetailIds 没有值的时候，出现的空指针异常 进行非空判断
        if (StringUtils.isNotBlank(attShift.getTimeSlotDetailIds())) {
            attTimeSlotDetailIdList = Arrays.asList(attShift.getTimeSlotDetailIds().split(","));
        } else {
            attTimeSlotDetailIdList = new ArrayList<>();
        }

        for (AttTimeSlot attTimeSlot : attShift.getAttTimeSlotSet()) {
            if (attTimeSlotDetailIdList.contains(attTimeSlot.getId() + "_" + modCommon)) {
                // 存在符合
                attTimeSlotId = attTimeSlot.getId();

                attTimeSlotIdList.add(attTimeSlotId);
            }
        }

        Collections.sort(attTimeSlotIdList);
        if (!attTimeSlotIdList.isEmpty()) {
            Map<Long, List<String>> attDateMap = attShiftSchDataMap.get(attShift.getId());
            if (attDateMap == null) {
                attDateMap = new HashMap<Long, List<String>>();
            }
            attDateMap.put(attDateLong, attTimeSlotIdList);
            attShiftSchDataMap.put(attShift.getId(), attDateMap);
        }
    }

    /**
     * 给时段排序
     * 
     * @param attTimeSlotBOList:
     * @param attTimeSlotItemMap:
     * @return void
     * <AUTHOR>
     * @date 2020-12-01 10:47
     * @since 1.0.0
     */
    public static void sortTimeSlot(List<AttTimeSlotBO> attTimeSlotBOList,
        Map<String, AttTimeSlotItem> attTimeSlotItemMap) {
        if (null == attTimeSlotBOList || attTimeSlotBOList.isEmpty()) {
            return;
        }
        Collections.sort(attTimeSlotBOList, (o1, o2) -> {
            String attTimeSlotId1 = o1.getAttTimeSlotId();
            String attTimeSlotId2 = o2.getAttTimeSlotId();
            AttTimeSlotItem attTimeSlotItem1 = attTimeSlotItemMap.get(attTimeSlotId1);
            AttTimeSlotItem attTimeSlotItem2 = attTimeSlotItemMap.get(attTimeSlotId2);
            String timeSlotStart1 = attTimeSlotItem1.getToWorkTime();
            String timeSlotStart2 = attTimeSlotItem2.getToWorkTime();
            if (AttConstant.PERIODTYPE_ELASTIC.equals(attTimeSlotItem1.getPeriodType())) {
                timeSlotStart1 = attTimeSlotItem1.getStartSignInTime();
            }
            if (AttConstant.PERIODTYPE_ELASTIC.equals(attTimeSlotItem2.getPeriodType())) {
                timeSlotStart2 = attTimeSlotItem2.getStartSignInTime();
            }
            return StringUtils.compare(timeSlotStart1, timeSlotStart2);
        });
    }
}
