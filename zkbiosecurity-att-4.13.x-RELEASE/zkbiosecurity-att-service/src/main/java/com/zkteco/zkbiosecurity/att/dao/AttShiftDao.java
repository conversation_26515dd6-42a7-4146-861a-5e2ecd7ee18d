package com.zkteco.zkbiosecurity.att.dao;

import com.zkteco.zkbiosecurity.att.model.AttShift;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 班次
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:04
 * @since 1.0.0
 */
public interface AttShiftDao extends BaseDao<AttShift, String> {

    Set<AttShift> findByIdIn(Collection<String> attShiftIdList);

    /**
     * 班次编号是否存在
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/11 14:07
     * @param shiftNo
     *            班次编号
     * @return boolean
     */
    boolean existsByShiftNo(String shiftNo);

    /**
     * 根据班次编号，查询班次
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/24 13:46
     * @param shiftNo
     *            班次编号
     * @return com.zkteco.zkbiosecurity.att.model.AttShift
     */
    AttShift findByShiftNo(String shiftNo);

    /**
     * 班次名称是否存在
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/11 14:08
     * @param shiftName
     *            班次名称
     * @return boolean
     */
    boolean existsByShiftName(String shiftName);

    /**
     * 判断时间段是否存在外键
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/6/28 15:59
     * @param timeslotIdList
     * @return boolean
     */
    boolean existsByAttTimeSlotSet_IdIn(List<String> timeslotIdList);
}