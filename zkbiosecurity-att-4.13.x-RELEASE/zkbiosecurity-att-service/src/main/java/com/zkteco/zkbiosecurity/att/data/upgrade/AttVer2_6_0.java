package com.zkteco.zkbiosecurity.att.data.upgrade;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.jdbc.JdbcOperateTemplate;
import com.zkteco.zkbiosecurity.system.constants.BaseDataConstants;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 升级到2.6.0
 *
 */
@Slf4j
@Component
public class AttVer2_6_0 implements UpgradeVersionManager {

    @Autowired
    private JdbcOperateTemplate jdbcOperateTemplate;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public String getModule() {
        return BaseConstants.ATT;
    }

    @Override
    public String getVersion() {
        return "v2.6.0";
    }

    @Override
    public boolean executeUpgrade() {

        // 部门名称和code长度改为100
        jdbcOperateTemplate.alterTableCharLen("ATT_LEAVE", "AUTH_DEPT_CODE", "100");
        jdbcOperateTemplate.alterTableCharLen("ATT_LEAVE", "AUTH_DEPT_NAME", "100");
        jdbcOperateTemplate.alterTableCharLen("ATT_PERSON", "AUTH_DEPT_CODE", "100");
        jdbcOperateTemplate.alterTableCharLen("ATT_RECORD", "AUTH_DEPT_CODE", "100");
        jdbcOperateTemplate.alterTableCharLen("ATT_RECORD", "AUTH_DEPT_NAME", "100");
        jdbcOperateTemplate.alterTableCharLen("ATT_TRANSACTION", "AUTH_DEPT_CODE", "100");
        jdbcOperateTemplate.alterTableCharLen("ATT_TRANSACTION", "AUTH_DEPT_NAME", "100");
        jdbcOperateTemplate.alterTableCharLen("ATT_TRANSACTION", "AUTH_AREA_NAME", "100");

        // 数据清理的参数修改
        BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("attReportDataClean");
        if (StringUtils.isBlank(baseSysParamItem.getId())) {
            JSONObject values = new JSONObject();
            values.put("keptMonth", "15");
            values.put("runtime", "03:00:00");
            values.put("keptType", BaseDataConstants.DATA_CLEAN_KEPTMONTH);
            BaseSysParamItem item =
                new BaseSysParamItem("attReportDataClean", values.toString(), "base_dataClean_attTrans");
            baseSysParamService.initData(item);
        }

        return true;
    }

}
