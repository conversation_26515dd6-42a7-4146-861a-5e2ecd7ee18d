package com.zkteco.zkbiosecurity.att.calculation;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.bean.AttRuleParamBean;
import com.zkteco.zkbiosecurity.att.calc.bo.AttOvertimeBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttTimeSlotBO;
import com.zkteco.zkbiosecurity.att.constants.AttShiftConstant;
import com.zkteco.zkbiosecurity.att.enums.AttRuleEnum;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttRecordItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;

/**
 * 考勤加班计算
 *
 * <AUTHOR>
 * @date 2020-07-03 09:59
 * @since 1.0.0
 */
@Component
public class AttOvertimeCalculate {

    /**
     * 加班信息
     *
     * @param attPersonSchBO
     */
    public void fillOvertimeData(AttPersonSchBO attPersonSchBO, List<AttOvertimeBO> attOvertimeBOList,
        Map<String, AttTimeSlotItem> attTimeSlotItemMap, AttRuleParamBean attRuleParamBean) {

        // 考勤规则设置是否统计加班
        if (AttRuleEnum.CountOvertime.getValueOne().equals(attRuleParamBean.getOvertime())) {
            // 不统计加班
            noOvertime(attPersonSchBO);
            return;
        }

        // 有统计加班：根据加班模式计算加班时长;
        switch (attPersonSchBO.getOvertimeMode()) {
            // 电脑自动计算（提前延后算加班）
            case AttShiftConstant.OvertimeMode.AUTO_CALC:
                computerAutomaticCalculation(attPersonSchBO, attTimeSlotItemMap, attRuleParamBean);
                break;
            // 加班必须申请（实际打卡与申请单，取较小者；如未打卡则无加班）
            case AttShiftConstant.OvertimeMode.MUST_APPLY:
                overtimeMustApply(attPersonSchBO, attOvertimeBOList, attTimeSlotItemMap, attRuleParamBean);
                break;
            // 两者时长最小者(已删除此逻辑)
            case AttShiftConstant.OvertimeMode.TIME_SMALLER:
                overtimeLeastTime(attPersonSchBO, attOvertimeBOList, attTimeSlotItemMap, attRuleParamBean);
                break;
            // 不计加班
            case AttShiftConstant.OvertimeMode.NOT_BRUSH_CARD:
                noOvertime(attPersonSchBO);
                break;
            default:
                break;
        }

    }

    /**
     * 判断班次工作类型为加班的话则工作时长记为加班时长.
     *
     * @param attPersonSchBO
     * @param attRecordItem
     */
    public void fillOverTimeByWorkType(AttPersonSchBO attPersonSchBO, AttRecordItem attRecordItem) {

        // 工作类型
        String workType = attPersonSchBO.getWorkType();
        // 加班方式
        Short overtimeMode = attPersonSchBO.getOvertimeMode();
        // 有效出勤分钟数
        Integer validMinute = attRecordItem.getValidMinute();
        // 加班分钟数
        Integer overtimeMinute = attRecordItem.getOvertimeMinute();

        switch (workType) {
            // 正常工作
            case AttShiftConstant.SHIFT_WORKTYPE_NORMALWORK:
                break;
            // 休息日加班（电脑自动计算：整个班次计为加班、加班必须申请：根据加班单计算加班、不计实到/有效/迟到/早退/旷工）
            case AttShiftConstant.SHIFT_WORKTYPE_WEEKENDOT:
                // 加班分钟数-休息
                Integer overtimeRestMinute = attRecordItem.getOvertimeRestMinute();
                Integer restMinute;
                switch (overtimeMode) {
                    // 电脑自动计算（工作类型为加班的，不计算提前延后算加班）
                    case AttShiftConstant.OvertimeMode.AUTO_CALC:
                        // 加班时长加上工作有效时长
                        restMinute = overtimeRestMinute + validMinute;
                        overtimeMinute = overtimeMinute + validMinute;
                        attRecordItem.setOvertimeRestMinute(restMinute);
                        attRecordItem.setOvertimeMinute(overtimeMinute);
                        break;
                    // 加班必须申请（实际打卡与申请单，取较小者；如未打卡则无加班）
                    case AttShiftConstant.OvertimeMode.MUST_APPLY:
                        // 加班时长
                        restMinute = overtimeRestMinute;
                        attRecordItem.setOvertimeRestMinute(restMinute);
                        attRecordItem.setOvertimeMinute(overtimeMinute);
                        break;
                    // 不计算加班（正常前端会隐藏该选项，此处逻辑不会执行）
                    case AttShiftConstant.OvertimeMode.NOT_BRUSH_CARD:
                        noOvertime(attPersonSchBO);
                        break;
                    default:
                        break;
                }
                // 班次整体算加班 不计到应到实到有效 迟到早退旷工
                overtimeWorkTypeClearRecordData(attRecordItem);
                break;
            // 节假日加班(电脑自动计算：整个班次计为加班、加班必须申请：根据加班单计算加班、不计实到/有效/迟到/早退/旷工)
            case AttShiftConstant.SHIFT_WORKTYPE_HOLIDAYOT:
                // 加班分钟数-休息
                Integer overtimeHolidayMinute = attRecordItem.getOvertimeHolidayMinute();
                Integer holidayMinute;
                switch (overtimeMode) {
                    // 电脑自动计算（工作类型为加班的，不计算提前延后算加班）
                    case AttShiftConstant.OvertimeMode.AUTO_CALC:
                        // 加班时长加上工作有效时长
                        holidayMinute = overtimeHolidayMinute + validMinute;
                        overtimeMinute = overtimeMinute + validMinute;
                        attRecordItem.setOvertimeHolidayMinute(holidayMinute);
                        attRecordItem.setOvertimeMinute(overtimeMinute);
                        break;
                    // 加班必须申请（实际打卡与申请单，取较小者；如未打卡则无加班）
                    case AttShiftConstant.OvertimeMode.MUST_APPLY:
                        // 加班时长
                        holidayMinute = overtimeHolidayMinute;
                        attRecordItem.setOvertimeHolidayMinute(holidayMinute);
                        attRecordItem.setOvertimeMinute(overtimeMinute);
                        break;
                    // 不计算加班（正常前端会隐藏该选项，此处逻辑不会执行）
                    case AttShiftConstant.OvertimeMode.NOT_BRUSH_CARD:
                        noOvertime(attPersonSchBO);
                        break;
                    default:
                        break;
                }
                // 班次整体算加班 不计到应到实到有效 迟到早退旷工
                overtimeWorkTypeClearRecordData(attRecordItem);
                break;

            default:
                break;
        }
    }

    /**
     * 班次整体算加班 不计到应到实到有效 迟到早退旷工
     */
    private void overtimeWorkTypeClearRecordData(AttRecordItem attRecordItem) {

        // 处理休息加班、节假日加班，且加班换算为天的时候，应上如果置为0，换算出来的加班时长也为0，保存应
        attRecordItem.setShouldMinuteEx(attRecordItem.getShouldMinute());

        attRecordItem.setShouldMinute(0);
        attRecordItem.setShouldDays(BigDecimal.ZERO);
        attRecordItem.setActualMinute(0);
        attRecordItem.setActualDays(BigDecimal.ZERO);
        attRecordItem.setValidMinute(0);
        attRecordItem.setValidDays(BigDecimal.ZERO);

        attRecordItem.setLateCountData("");
        attRecordItem.setLateCountTotal(0);
        attRecordItem.setLateMinuteData("");
        attRecordItem.setLateMinuteTotal(0);

        attRecordItem.setEarlyCountData("");
        attRecordItem.setEarlyCountTotal(0);
        attRecordItem.setEarlyMinuteData("");
        attRecordItem.setEarlyMinuteTotal(0);

        attRecordItem.setAbsentMinute(0);
    }

    /**
     * 电脑自动计算（提前延后算加班）
     *
     * @param attPersonSchBO
     * @return
     */
    private int computerAutomaticCalculation(AttPersonSchBO attPersonSchBO,
        Map<String, AttTimeSlotItem> attTimeSlotItemMap, AttRuleParamBean attRuleParamBean) {

        // 整个班次时间段的加班时长
        int timeLong = 0;
        String noSignIn = attRuleParamBean.getNoSignIn();
        String noSignOff = attRuleParamBean.getNoSignOff();
        List<AttTimeSlotBO> attTimeSlotBOList = attPersonSchBO.getAttTimeSlotArray();
        for (AttTimeSlotBO attTimeSlotBO : attTimeSlotBOList) {
            AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());
            // 时段的加班分钟数
            int overtimeMinute = 0;
            // 提前上班分钟数
            int advanceToWorkMinutes = attTimeSlotBO.getFlexibleTime();
            // 提前是否算加班
            if (attTimeSlotItem.getIsAdvanceCountOvertime()) {
                String actualToWorkTime = attTimeSlotBO.getToWorkTime();
                // 必须签退且未签退
                boolean mustSignOff =
                    attTimeSlotItem.getIsMustSignOff() && noSignOff.equals(attTimeSlotBO.getOffWorkTime());
                // 有上班卡点 + （非必须签退而未签退 或 有异常）
                if (!noSignIn.equals(actualToWorkTime)
                    && (!mustSignOff || StringUtils.isNotBlank(attTimeSlotBO.getExceptionTime()))) {

                    String timeSlotToWorkTime =
                        String.format("%s %s:00", attTimeSlotBO.getFirstDay(), attTimeSlotItem.getToWorkTime());

                    boolean isCardInException = isCardInException(actualToWorkTime, attTimeSlotBO);
                    // 如果卡点落在异常申请里,则不算加班
                    if (!isCardInException) {
                        // 上班N分钟前签到记加班
                        Integer beforeWorkOvertimeMinutes = attTimeSlotItem.getBeforeWorkOvertimeMinutes();
                        String signInAdvanceTime =
                            AttDateUtils.addMinute(timeSlotToWorkTime, -beforeWorkOvertimeMinutes);
                        if (StringUtils.compare(actualToWorkTime, signInAdvanceTime) <= 0) {
                            // 判断异常与班前记加班的结束时间点是否有交集，有则取最小值
                            signInAdvanceTime = getMinTimeForAdvance(signInAdvanceTime, attTimeSlotBO);
                            int diffTime = AttDateUtils.getMinuteDiff(actualToWorkTime, signInAdvanceTime);
                            if (diffTime >= attTimeSlotItem.getMinBeforeOvertimeMinutes()) {
                                // 限制最大加班分钟数,分钟数设置为空或0均表示不限制
                                Integer maxBeforeOvertimeMinutes = attTimeSlotItem.getMaxBeforeOvertimeMinutes();
                                if (null != maxBeforeOvertimeMinutes && 0 != maxBeforeOvertimeMinutes
                                    && diffTime > maxBeforeOvertimeMinutes) {
                                    diffTime = maxBeforeOvertimeMinutes;
                                }
                                overtimeMinute += diffTime;
                            }
                        }
                    }
                }
            }

            // 延后是否算加班
            if (attTimeSlotItem.getIsPostponeCountOvertime()) {
                String actualOffWorkTime = attTimeSlotBO.getOffWorkTime();
                // 必须签到未签到
                boolean mustSignIn =
                    attTimeSlotItem.getIsMustSignIn() && noSignIn.equals(attTimeSlotBO.getToWorkTime());
                // 有下班卡点 + （非必须签到而未签到 或 有异常）
                if (!noSignOff.equals(actualOffWorkTime)
                    && (!mustSignIn || StringUtils.isNotBlank(attTimeSlotBO.getExceptionTime()))) {

                    boolean isCardInException = isCardInException(actualOffWorkTime, attTimeSlotBO);
                    // 如果卡点落在异常申请里,则不算加班
                    if (!isCardInException) {

                        String timeSlotOffWorkTime =
                            String.format("%s %s:00", attTimeSlotBO.getSecondDay(), attTimeSlotItem.getOffWorkTime());
                        Integer afterWorkOvertimeMinutes = attTimeSlotItem.getAfterWorkOvertimeMinutes();
                        Integer minAfterOvertimeMinutes = attTimeSlotItem.getMinAfterOvertimeMinutes();

                        // 如果启用弹性上班时间,提前上班则需要计算出实际下班时间
                        if (ConstUtil.SYSTEM_COMMON_TRUE.equals(attTimeSlotItem.getEnableFlexibleWork())) {
                            timeSlotOffWorkTime = AttDateUtils.addMinute(timeSlotOffWorkTime, -advanceToWorkMinutes);
                        }

                        String signOutPostponeTime =
                            AttDateUtils.addMinute(timeSlotOffWorkTime, afterWorkOvertimeMinutes);
                        // 加班时间为 实际下班时间-延后计加班时间 且要大于最短加班分钟数
                        if (StringUtils.compare(actualOffWorkTime, signOutPostponeTime) >= 0) {
                            // 判断异常与班后记加班的开始时间点是否有交集，有则取最大值
                            signOutPostponeTime = getMaxTimeForAdvance(signOutPostponeTime, attTimeSlotBO);
                            int diffTime = AttDateUtils.getMinuteDiff(signOutPostponeTime, actualOffWorkTime);
                            if (diffTime >= minAfterOvertimeMinutes) {
                                // 限制最大加班分钟数,分钟数设置为空或0均表示不限制
                                Integer maxAfterOvertimeMinutes = attTimeSlotItem.getMaxAfterOvertimeMinutes();
                                if (null != maxAfterOvertimeMinutes && 0 != maxAfterOvertimeMinutes
                                    && diffTime > maxAfterOvertimeMinutes) {
                                    diffTime = maxAfterOvertimeMinutes;
                                }
                                overtimeMinute += diffTime;
                            }
                        }
                    }

                }
            }

            attTimeSlotBO.setOvertimeUsualMinute(0);
            attTimeSlotBO.setOvertimeRestMinute(0);
            attTimeSlotBO.setOvertimeHolidayMinute(0);
            // 加班标记
            switch (attPersonSchBO.getOvertimeRemark()) {
                case AttShiftConstant.OvertimeSign.NORMAL:
                    attTimeSlotBO.setOvertimeUsualMinute(overtimeMinute);
                    break;
                case AttShiftConstant.OvertimeSign.REST:
                    attTimeSlotBO.setOvertimeRestMinute(overtimeMinute);
                    break;
                case AttShiftConstant.OvertimeSign.HOLIDAY:
                    attTimeSlotBO.setOvertimeHolidayMinute(overtimeMinute);
                    break;
                default:
                    break;
            }

            attTimeSlotBO.setOvertimeMinute(overtimeMinute);
            timeLong += overtimeMinute;
        }

        return timeLong;
    }

    /**
     * 判断卡点是否落在异常申请里
     * 
     * @param dateTime
     * @param attTimeSlotBO
     * @return
     */
    public boolean isCardInException(String dateTime, AttTimeSlotBO attTimeSlotBO) {
        String exceptionTime = attTimeSlotBO.getExceptionTime();

        if (StringUtils.isBlank(exceptionTime)) {
            return false;
        }

        String[] exceptionTimeArr = exceptionTime.split(",");
        for (String time : exceptionTimeArr) {
            String[] split = time.split("=");
            if ((StringUtils.compare(split[0], dateTime) <= 0) && (StringUtils.compare(dateTime, split[1]) <= 0)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 加班必须申请
     * 
     * @param attPersonSchBO
     * @param attOvertimeBOList
     * @param attTimeSlotItemMap
     * @param attRuleParamBean
     * @return
     */
    private int overtimeMustApply(AttPersonSchBO attPersonSchBO, List<AttOvertimeBO> attOvertimeBOList,
        Map<String, AttTimeSlotItem> attTimeSlotItemMap, AttRuleParamBean attRuleParamBean) {

        // 没有加班申请
        if (CollectionUtil.isEmpty(attOvertimeBOList)) {
            noOvertime(attPersonSchBO);
            return 0;
        }

        String noSignIn = attRuleParamBean.getNoSignIn();
        String noSignOff = attRuleParamBean.getNoSignOff();
        String noSignInAsType = attRuleParamBean.getNoSignInAsType();
        String noSignOutAsType = attRuleParamBean.getNoSignOutAsType();

        // 整个班次时间段的加班时长
        int timeLong = 0;
        List<AttTimeSlotBO> attTimeSlotBOList = attPersonSchBO.getAttTimeSlotArray();
        for (AttTimeSlotBO attTimeSlotBO : attTimeSlotBOList) {

            AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());

            String toWorkTime = attTimeSlotBO.getToWorkTime();
            String offWorkTime = attTimeSlotBO.getOffWorkTime();

            // 上班时间
            String timeSlotToWorkTime =
                    String.format("%s %s:00", attTimeSlotBO.getFirstDay(), attTimeSlotItem.getToWorkTime());
            // 下班时间
            String timeSlotOffWorkTime =
                    String.format("%s %s:00", attTimeSlotBO.getSecondDay(), attTimeSlotItem.getOffWorkTime());


            // 时间段免打卡 -- lgq 20250121
            // 如果非必须签到且未签到,则卡点按时段上班时间
            if (!attTimeSlotItem.getIsMustSignIn() && noSignIn.equals(toWorkTime)) {
                toWorkTime = timeSlotToWorkTime;
            }

            // 如果非必须签退且未签退,则卡点按时段下班时间
            if (!attTimeSlotItem.getIsMustSignOff() && noSignOff.equals(offWorkTime)) {
                offWorkTime = timeSlotOffWorkTime;
            }

            // 如果未签到或未签退则不计加班
            if (noSignIn.equals(toWorkTime) && attTimeSlotItem.getIsMustSignIn()
                && !AttRuleEnum.NoSignInCountType.getValueTwo().equals(noSignInAsType)) {
                continue;
            }
            if (noSignOff.equals(offWorkTime) && attTimeSlotItem.getIsMustSignOff()
                && !AttRuleEnum.noSignOffCountType.getValueTwo().equals(noSignOutAsType)) {
                continue;
            }

            // 开始签到时间
            String timeSlotStartSignIn = AttDateUtils.addMinute(
                String.format("%s %s:00", attTimeSlotBO.getFirstDay(), attTimeSlotItem.getToWorkTime()),
                -attTimeSlotItem.getBeforeToWorkMinutes());
            // 结束签退时间
            String timeSlotEndSignOff = AttDateUtils.addMinute(
                String.format("%s %s:00", attTimeSlotBO.getSecondDay(), attTimeSlotItem.getOffWorkTime()),
                attTimeSlotItem.getAfterOffWorkMinutes());

            // 时段的加班分钟数
            int overtimeMinute = 0;
            int overtimeUsualMinute = 0;
            int overtimeRestMinute = 0;
            int overtimeHolidayMinute = 0;

            for (AttOvertimeBO attOvertimeBO : attOvertimeBOList) {

                // 加班单时间范围要落在时间段取卡范围
                if (StringUtils.compare(timeSlotStartSignIn, attOvertimeBO.getEndDateTime()) > 0
                    || StringUtils.compare(timeSlotEndSignOff, attOvertimeBO.getStartDateTime()) < 0) {
                    continue;
                }

                // 当员工签到时间大于加班单申请时间，取签到时间
                String actualStartTime =
                        StringUtils.compare(toWorkTime, attOvertimeBO.getStartDateTime()) > 0
                                ? toWorkTime : attOvertimeBO.getStartDateTime();

                // 当员工签退时间小于加班单结束时间时,取签退时间
                String actualEndTime =
                        StringUtils.compare(offWorkTime, attOvertimeBO.getEndDateTime()) < 0
                                ? offWorkTime : attOvertimeBO.getEndDateTime();

                String workType = attPersonSchBO.getWorkType();
                // 如果是正常班次，加班时长不能包含上班时间，即加班时长统计的是上班时间段之外的时间
                if (AttShiftConstant.SHIFT_WORKTYPE_NORMALWORK.equals(workType)) {
                    // 如果加班单开始时间大于上班时间且加班单结束时间小于下班时间，则加班单不生效
                    if (StringUtils.compare(actualStartTime, timeSlotToWorkTime) > 0 && StringUtils.compare(actualEndTime, timeSlotOffWorkTime) < 0) {
                        continue;
                    }

                    // 如果加班单开始时间小于上班时间且加班单结束时间大于上班时间，则取上班时间为加班实际结束时间
                    if (StringUtils.compare(actualStartTime, timeSlotToWorkTime) < 0 && StringUtils.compare(actualEndTime, timeSlotToWorkTime) > 0) {
                        actualEndTime = timeSlotToWorkTime;
                    }

                    // 如果加班单开始时间小于下班时间且加班单结束时间大于下班时间，则取下班时间为加班的实际开始时间
                    if (StringUtils.compare(actualStartTime, timeSlotOffWorkTime) < 0 && StringUtils.compare(actualEndTime, timeSlotOffWorkTime) > 0) {
                        actualStartTime = timeSlotOffWorkTime;
                    }

                    // 如果加班单开始时间大于下班时间或者加班单结束时间小于上班时间，不需要处理

                } else {  // 如果是休息日加班、或者节假日加班，加班时长只能在上班时间段内，即加班是统计上班时间段内的时间
                    // 如果加班单开始时间大于下班时间或者加班结束时间小于上班时间，则该申请单不生效
                    if (StringUtils.compare(actualStartTime, timeSlotOffWorkTime) > 0 || StringUtils.compare(actualEndTime, timeSlotToWorkTime) < 0) {
                        continue;
                    }

                    // 如果加班单开始时间小于上班时间且加班单结束时间大于上班时间，则取上班时间为加班实际开始时间
                    if (StringUtils.compare(actualStartTime, timeSlotToWorkTime) < 0 && StringUtils.compare(actualEndTime, timeSlotToWorkTime) > 0) {
                        actualStartTime = timeSlotToWorkTime;
                    }

                    // 如果加班单开始时间小于下班时间且加班单结束时间大于下班时间，则取下班时间为加班的实际结束时间
                    if (StringUtils.compare(actualStartTime, timeSlotOffWorkTime) < 0 && StringUtils.compare(actualEndTime, timeSlotOffWorkTime) > 0) {
                        actualEndTime = timeSlotOffWorkTime;
                    }

                    //  如果加班单开始时间大于上班时间且加班单结束时间小于下班时间，不需要处理
                }

                int diffTime = AttDateUtils.getMinuteDiff(actualStartTime, actualEndTime);
                if (diffTime > 0) {
                    overtimeMinute += diffTime;

                    // 加班申请已经不设置加班标记（平时，休息日，节假日），这边的加班标记取班次"工作类型"来判断 - lgq 20230905
                    switch (workType) {
                        // 正常工作 = 平时加班
                        case AttShiftConstant.SHIFT_WORKTYPE_NORMALWORK:
                            overtimeUsualMinute += diffTime;
                            break;
                        case AttShiftConstant.SHIFT_WORKTYPE_WEEKENDOT:
                            overtimeRestMinute += diffTime;
                            break;
                        case AttShiftConstant.SHIFT_WORKTYPE_HOLIDAYOT:
                            overtimeHolidayMinute += diffTime;
                            break;
                        default:
                            break;
                    }
                }
            }

            attTimeSlotBO.setOvertimeUsualMinute(overtimeUsualMinute);
            attTimeSlotBO.setOvertimeRestMinute(overtimeRestMinute);
            attTimeSlotBO.setOvertimeHolidayMinute(overtimeHolidayMinute);
            attTimeSlotBO.setOvertimeMinute(overtimeMinute);
            timeLong += overtimeMinute;
        }
        return timeLong;
    }

    /**
     * 两者时长最小者【已删除此功能】
     *
     * @param attPersonSchBO
     */
    @Deprecated
    private void overtimeLeastTime(AttPersonSchBO attPersonSchBO, List<AttOvertimeBO> attOvertimeBOList,
        Map<String, AttTimeSlotItem> attTimeSlotItemMap, AttRuleParamBean attRuleParamBean) {
        AttPersonSchBO attPersonSchBO1 = ModelUtil.copyProperties(attPersonSchBO, new AttPersonSchBO());
        AttPersonSchBO attPersonSchBO2 = ModelUtil.copyProperties(attPersonSchBO, new AttPersonSchBO());
        // 电脑自动计算
        int timeLong1 = computerAutomaticCalculation(attPersonSchBO1, attTimeSlotItemMap, attRuleParamBean);
        // 加班必须申请
        int timeLong2 = overtimeMustApply(attPersonSchBO2, attOvertimeBOList, attTimeSlotItemMap, attRuleParamBean);
        ModelUtil.copyProperties(timeLong1 < timeLong2 ? attPersonSchBO1 : attPersonSchBO2, attPersonSchBO);
    }

    /**
     * 不计加班
     *
     * @param attPersonSchBO
     */
    private void noOvertime(AttPersonSchBO attPersonSchBO) {
        List<AttTimeSlotBO> attTimeSlotBOList = attPersonSchBO.getAttTimeSlotArray();
        for (AttTimeSlotBO attTimeSlotBO : attTimeSlotBOList) {
            attTimeSlotBO.setOvertimeUsualMinute(0);
            attTimeSlotBO.setOvertimeRestMinute(0);
            attTimeSlotBO.setOvertimeHolidayMinute(0);
            attTimeSlotBO.setOvertimeMinute(0);
        }
    }

    /**
     * 判断异常与班前记加班的结束时间点是否有交集，有则取最小值
     * 
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/9/14 19:06
     * @param advanceTime
     * @param attTimeSlotBO
     * @return java.lang.String
     */
    private String getMinTimeForAdvance(String advanceTime, AttTimeSlotBO attTimeSlotBO) {
        String exceptionTime = attTimeSlotBO.getExceptionTime();

        if (StringUtils.isBlank(exceptionTime)) {
            return advanceTime;
        }

        String[] exceptionTimeArr = exceptionTime.split(",");
        for (String time : exceptionTimeArr) {
            String[] split = time.split("=");
            if ((StringUtils.compare(split[0], advanceTime) <= 0)
                && (StringUtils.compare(advanceTime, split[1]) <= 0)) {
                return split[0];
            }
        }

        return advanceTime;
    }

    /**
     * 判断异常与班后记加班的开始时间点是否有交集，有则取最大值
     * 
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/9/14 19:08
     * @return java.lang.String
     */
    private String getMaxTimeForAdvance(String advanceTime, AttTimeSlotBO attTimeSlotBO) {
        String exceptionTime = attTimeSlotBO.getExceptionTime();

        if (StringUtils.isBlank(exceptionTime)) {
            return advanceTime;
        }

        String[] exceptionTimeArr = exceptionTime.split(",");
        for (String time : exceptionTimeArr) {
            String[] split = time.split("=");
            if ((StringUtils.compare(split[0], advanceTime) <= 0)
                && (StringUtils.compare(advanceTime, split[1]) <= 0)) {
                return split[1];
            }
        }

        return advanceTime;
    }
}
