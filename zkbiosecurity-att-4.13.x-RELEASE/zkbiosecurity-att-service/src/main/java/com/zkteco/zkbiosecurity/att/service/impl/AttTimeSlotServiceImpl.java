package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.cache.AttCalculationCacheManager;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.constants.AttShiftConstant;
import com.zkteco.zkbiosecurity.att.dao.AttBreakTimeDao;
import com.zkteco.zkbiosecurity.att.dao.AttShiftDao;
import com.zkteco.zkbiosecurity.att.dao.AttTempSchDao;
import com.zkteco.zkbiosecurity.att.dao.AttTimeSlotDao;
import com.zkteco.zkbiosecurity.att.model.AttBreakTime;
import com.zkteco.zkbiosecurity.att.model.AttTimeSlot;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.service.AttTimeSlotService;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttBreakTimeItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotBreakTimeItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.format.TreeBuilder;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

/**
 * 时间段
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:52
 * @since 1.0.0
 */
@Service
public class AttTimeSlotServiceImpl implements AttTimeSlotService {

    @Autowired
    private AttTimeSlotDao attTimeSlotDao;
    @Autowired
    private AttShiftDao attShiftDao;
    @Autowired
    private AttBreakTimeDao attBreakTimeDao;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AttTempSchDao attTempSchDao;
    @Autowired
    private AttCalculationCacheManager attCalculationCacheManager;
    @Autowired
    private AttParamService attParamService;

    @Override
    @Transactional
    public AttTimeSlotItem saveItem(AttTimeSlotItem item) {
        AttTimeSlot attTimeSlot = Optional.ofNullable(item).map(AttTimeSlotItem::getId).filter(StringUtils::isNotBlank)
            .flatMap(attTimeSlotDao::findById).filter(Objects::nonNull).orElse(new AttTimeSlot());
        // 由于前端已经去掉isCountOvertime的传值 所以暂时给默认值 以免考勤计算出现该字段为空的情况 add by jinxian.huang 2019-07-04
        item.setIsCountOvertime(false);
        ModelUtil.copyPropertiesIgnoreNull(item, attTimeSlot);
        // 规律班次开始签到和结束签退时间用上下班时间对应折算其开始/结束签到分钟数 by ljf 2019/7/8
        if (AttShiftConstant.ShiftType.REGULAR_SHIFT == attTimeSlot.getPeriodType()) {
            String startSignInTime = DateUtil.dateToString(
                DateUtil.addMinute(DateUtil.stringToDate(attTimeSlot.getToWorkTime(), DateUtil.DateStyle.HH_MM),
                    -attTimeSlot.getBeforeToWorkMinutes()),
                DateUtil.DateStyle.HH_MM);
            String endSignOffTime = DateUtil.dateToString(
                DateUtil.addMinute(DateUtil.stringToDate(attTimeSlot.getOffWorkTime(), DateUtil.DateStyle.HH_MM),
                    attTimeSlot.getAfterOffWorkMinutes()),
                DateUtil.DateStyle.HH_MM);
            attTimeSlot.setStartSignInTime(startSignInTime);
            attTimeSlot.setEndSignOffTime(endSignOffTime);
        }
        attTimeSlotDao.save(attTimeSlot);
        item.setId(attTimeSlot.getId());

        // 保存休息时间段
        if (StringUtils.isNotBlank(item.getBreakTimeData())) {
            saveBreakTime(attTimeSlot, item.getBreakTimeData());
        }

        // 【实时计算】更新缓存时间段
        if (attParamService.realTimeEnable()) {
            AttTimeSlotItem cacheItem = new AttTimeSlotItem();
            ModelUtil.copyPropertiesIgnoreNull(attTimeSlot, cacheItem);
            if (attTimeSlot.getAttBreakTimeSet() != null && attTimeSlot.getAttBreakTimeSet().size() > 0) {
                List<AttBreakTimeItem> attBreakTimeItemList = new ArrayList<>();
                for (AttBreakTime attBreakTime : attTimeSlot.getAttBreakTimeSet()) {
                    attBreakTimeItemList.add(ModelUtil.copyProperties(attBreakTime, new AttBreakTimeItem()));
                }
                item.setAttBreakTimeItems(attBreakTimeItemList);
                cacheItem.setAttBreakTimeItems(attBreakTimeItemList);
            }
            attCalculationCacheManager.setTimeSlotItem(cacheItem);
        }

        return item;
    }

    /**
     ** <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @Description 保存休息时间段
     * @date 2020/5/6
     **/
    private void saveBreakTime(AttTimeSlot attTimeSlot, String breakTimeData) {
        // 所有的休息时间段
        List<AttBreakTime> allAttBreakTimeList = attBreakTimeDao.findAll();
        // 开始时间结束时间组成的key
        Map<String, AttBreakTime> breakTimeMap =
            CollectionUtil.listToKeyMap(allAttBreakTimeList, i -> i.getStartTime() + "-" + i.getEndTime());
        // 删除原有绑定的休息时间段
        String timeSlotId = attTimeSlot.getId();
        Set<AttBreakTime> attBreakTimeSet = attTimeSlot.getAttBreakTimeSet();
        String originalBreakTimeIds = CollectionUtil.getModelIds(attBreakTimeSet);
        delBreakTime(timeSlotId, originalBreakTimeIds);
        if (StringUtils.isNotBlank(breakTimeData)) {
            List<AttBreakTimeItem> attBreakTimeItems = JSON.parseArray(breakTimeData, AttBreakTimeItem.class);
            if (!CollectionUtil.isEmpty(attBreakTimeItems)) {
                for (AttBreakTimeItem item : attBreakTimeItems) {
                    AttBreakTime attBreakTime = breakTimeMap.get(item.getStartTime() + "-" + item.getEndTime());
                    // 原来没有的时间段才保存
                    if (Objects.isNull(attBreakTime)) {
                        attBreakTime = new AttBreakTime();
                        ModelUtil.copyProperties(item, attBreakTime);
                        String breakTimeName = UUID.randomUUID().toString();
                        attBreakTime.setName(breakTimeName);
                        attBreakTime = attBreakTimeDao.save(attBreakTime);
                    }
                    // 保存中间表
                    addBreakTime(timeSlotId, attBreakTime.getId());
                }
            }
        }
    }

    @Override
    public List<AttTimeSlotItem> getByCondition(AttTimeSlotItem condition) {
        return (List<AttTimeSlotItem>)attTimeSlotDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return attTimeSlotDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            String[] idArray = StringUtils.split(ids, AttConstant.COMMA);
            for (String id : idArray) {
                attTimeSlotDao.deleteById(id);

                // 【实时计算】删除缓存中的时间段
                if (attParamService.realTimeEnable()) {
                    attCalculationCacheManager.delTimeSlotItem(id);
                }
            }
        }
        return false;
    }

    @Override
    public AttTimeSlotItem getItemById(String id) {
        AttTimeSlot attTimeSlot = attTimeSlotDao.getOne(id);
        if (Objects.nonNull(attTimeSlot)) {
            AttTimeSlotItem item = new AttTimeSlotItem();
            ModelUtil.copyProperties(attTimeSlot, item);
            // 组装休息时间段信息
            AttTimeSlotBreakTimeItem attTimeSlotBreakTimeItem = new AttTimeSlotBreakTimeItem();
            attTimeSlotBreakTimeItem.setTimeSlotId(item.getId());
            List<AttTimeSlotBreakTimeItem> breakTimeItemList = (List<AttTimeSlotBreakTimeItem>)attTimeSlotDao
                .getItemsBySql(AttTimeSlotBreakTimeItem.class, SQLUtil.getSqlByItem(attTimeSlotBreakTimeItem));
            // 排序
            if (breakTimeItemList != null) {
                breakTimeItemList.sort((o1, o2) -> StringUtils.compare(o1.getStartTime(), o2.getStartTime()));
            }
            item.setBreakTimeData(JSONArray.toJSONString(breakTimeItemList));
            return item;
        }
        return null;
    }

    @Override
    public Map<String, AttTimeSlotItem> initAttTimeSlotMap() {
        Integer atsNum = (int)attTimeSlotDao.count();
        Map<String, AttTimeSlotItem> attTimeSlotMap = new HashMap<String, AttTimeSlotItem>((atsNum * 4) / 3);
        List<AttTimeSlot> attTimeSlotList = attTimeSlotDao.findAll();
        AttTimeSlotItem attTimeSlotItem = null;
        for (AttTimeSlot attTimeSlot : attTimeSlotList) {
            attTimeSlotItem = new AttTimeSlotItem();
            ModelUtil.copyProperties(attTimeSlot, attTimeSlotItem);
            attTimeSlotMap.put(attTimeSlot.getId(), attTimeSlotItem);
        }
        return attTimeSlotMap;
    }

    @Override
    public boolean existsByPeriodNo(String periodNo) {
        return attTimeSlotDao.existsByPeriodNo(periodNo);
    }

    @Override
    public boolean existsByPeriodName(String periodName) {
        return attTimeSlotDao.existsByPeriodName(periodName);
    }

    @Override
    public List<AttTimeSlotItem> getItemData(Class<AttTimeSlotItem> attTimeSlotItemClass, BaseItem condition,
        int beginIndex, int endIndex) {
        return attTimeSlotDao.getItemsDataBySql(attTimeSlotItemClass, SQLUtil.getSqlByItem(condition), beginIndex,
            endIndex, true);
    }

    @Override
    public boolean isExistFkData(String ids) {
        if (StringUtils.isNotBlank(ids)) {

            List<String> timeslotIdList = Arrays.asList(ids.split(","));

            // 判断是否在班次中使用
            if (attShiftDao.existsByAttTimeSlotSet_IdIn(timeslotIdList)) {
                return true;
            }

            // 判断是否在临时排班中使用
            if (attTempSchDao.existsByAttTimeSlotSet_IdIn(timeslotIdList)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Long getAllTimeSlotCount() {
        return attTimeSlotDao.count();
    }

    @Override
    @Transactional
    public void handlerTransfer(List<AttTimeSlotItem> attTimeSlotItems) {
        // 时间段数据不会太多，不需要批量处理
        List<AttTimeSlot> attTimeSlotList = new ArrayList<>();
        int index = 1;
        for (AttTimeSlotItem attTimeSlotItem : attTimeSlotItems) {
            String periodNo = attTimeSlotItem.getPeriodNo();
            AttTimeSlot attTimeSlot = attTimeSlotDao.findByPeriodNo(periodNo);
            if (Objects.isNull(attTimeSlot)) {
                attTimeSlot = new AttTimeSlot();
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(attTimeSlotItem, attTimeSlot, "id");
            // 调整间断休息为休息时间段 by ljf 2019/9/29
            if (attTimeSlotItem.getIsSegmentDeduction()) {
                String startSegmentTime = attTimeSlotItem.getStartSegmentTime();
                String endSegmentTime = attTimeSlotItem.getEndSegmentTime();
                AttBreakTime attBreakTime = attBreakTimeDao.findByStartTimeAndEndTime(startSegmentTime, endSegmentTime);
                if (Objects.isNull(attBreakTime)) {
                    attBreakTime = new AttBreakTime();
                    attBreakTime.setName("T" + (index++));
                    attBreakTime.setStartTime(startSegmentTime);
                    attBreakTime.setEndTime(endSegmentTime);
                    attBreakTimeDao.save(attBreakTime);
                }
                Set<AttBreakTime> attBreakTimeSet = attTimeSlot.getAttBreakTimeSet();
                attBreakTimeSet.add(attBreakTime);
            }

            attTimeSlotList.add(attTimeSlot);
        }
        attTimeSlotDao.saveAll(attTimeSlotList);
    }

    @Override
    public Pager findBreakTimeListByTimeSlotId(AttTimeSlotBreakTimeItem condition, int pageNo, int pageSize) {
        Pager pager =
            attTimeSlotDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        return pager;
    }

    @Override
    @Transactional
    public String addBreakTime(String timeSlotId, String breakTimeIds) {
        String result = null;
        if (StringUtils.isNotBlank(breakTimeIds)) {
            AttTimeSlot attTimeSlot = attTimeSlotDao.findById(timeSlotId).get();

            String toWorkTime = attTimeSlot.getToWorkTime();
            String offWorkTime = attTimeSlot.getOffWorkTime();

            // 已有的休息时间段集合
            Set<AttBreakTime> attBreakTimeSet = attTimeSlot.getAttBreakTimeSet();
            // 新增加的休息时间段
            List<AttBreakTime> attBreakTimes = attBreakTimeDao.findByIdList(CollectionUtil.strToList(breakTimeIds));
            attBreakTimeSet.addAll(attBreakTimes);

            Date now = new Date();
            String firstDay = AttDateUtils.dateToStrAsShort(now);
            String secondDay = AttDateUtils.dateToStrAsShort(DateUtil.addDay(now, 1));

            // 验证所选的休息时间段时间是否重叠
            for (AttBreakTime attBreakTime : attBreakTimes) {

                String date = StringUtils.compare(attBreakTime.getStartTime(), toWorkTime) > 0 ? firstDay : secondDay;
                String start1 = String.format("%s %s", date, attBreakTime.getStartTime());
                date = StringUtils.compare(attBreakTime.getEndTime(), toWorkTime) > 0 ? firstDay : secondDay;
                String end1 = String.format("%s %s", date, attBreakTime.getEndTime());

                for (AttBreakTime breakTime : attBreakTimeSet) {
                    if (!attBreakTime.getName().equals(breakTime.getName())) {

                        date = StringUtils.compare(breakTime.getStartTime(), toWorkTime) > 0 ? firstDay : secondDay;
                        String start2 = String.format("%s %s", date, breakTime.getStartTime());
                        date = StringUtils.compare(breakTime.getEndTime(), toWorkTime) > 0 ? firstDay : secondDay;
                        String end2 = String.format("%s %s", date, breakTime.getEndTime());

                        if (StringUtils.compare(start1, end2) <= 0 && StringUtils.compare(end1, start2) >= 0) {
                            throw ZKBusinessException.errorException("att_timeSlot_timeOverlap");
                        }
                    }
                }
            }

            if (attTimeSlot.getIsSegmentDeduction()) {
                int workingHours = diffTime(toWorkTime, offWorkTime);
                int timeLong = countBreakTime(attBreakTimeSet);
                workingHours = workingHours - timeLong;
                attTimeSlot.setWorkingHours((short)workingHours);
            }
            attTimeSlotDao.save(attTimeSlot);

        }
        return result;
    }

    /**
     * 计算两个时间的的差值(单位:分钟)
     * 
     * @param startTime
     *            HH:mm
     * @param endTime
     *            HH:mm
     * @return
     */
    private int diffTime(String startTime, String endTime) {
        int diffTime;
        String[] time1 = startTime.split(":", 2);
        String[] time2 = endTime.split(":", 2);
        if (startTime.compareTo(endTime) <= 0) {
            diffTime = (Integer.parseInt(time2[0]) * 60 + Integer.parseInt(time2[1]))
                - (Integer.parseInt(time1[0]) * 60 + Integer.parseInt(time1[1]));
        } else {
            diffTime = 60 * 24 + (Integer.parseInt(time2[0]) * 60 + Integer.parseInt(time2[1]))
                - (Integer.parseInt(time1[0]) * 60 + Integer.parseInt(time1[1]));
        }
        return diffTime;
    }

    /**
     * 统计休息时间段的时长(单位:分钟)
     * 
     * @param attBreakTimeSet
     * @return
     */
    private int countBreakTime(Set<AttBreakTime> attBreakTimeSet) {
        int timeLong = 0;
        if (!CollectionUtil.isEmpty(attBreakTimeSet)) {
            for (AttBreakTime attBreakTime : attBreakTimeSet) {
                String startTime = attBreakTime.getStartTime();
                String endTime = attBreakTime.getEndTime();
                int diffTime = diffTime(startTime, endTime);
                timeLong += diffTime;
            }
        }
        return timeLong;
    }

    @Override
    @Transactional
    public void delBreakTime(String timeSlotId, String breakTimeIds) {
        AttTimeSlot attTimeSlot = attTimeSlotDao.findById(timeSlotId).get();
        Set<AttBreakTime> attBreakTimeSet = attTimeSlot.getAttBreakTimeSet();
        if (StringUtils.isNotBlank(breakTimeIds)) {
            List<AttBreakTime> attBreakTimes = attBreakTimeDao.findByIdList(CollectionUtil.strToList(breakTimeIds));
            attBreakTimeSet.removeAll(attBreakTimes);

            if (attTimeSlot.getIsSegmentDeduction()) {
                String toWorkTime = attTimeSlot.getToWorkTime();
                String offWorkTime = attTimeSlot.getOffWorkTime();
                int workingHours = diffTime(toWorkTime, offWorkTime);
                int timeLong = countBreakTime(attTimeSlot.getAttBreakTimeSet());
                workingHours = workingHours - timeLong;
                attTimeSlot.setWorkingHours((short)workingHours);
            }

            attTimeSlotDao.save(attTimeSlot);
        }

    }

    @Override
    public int countSegmentTime(String timeSlotId, String breakTimeArray) {
        if (StringUtils.isNotBlank(breakTimeArray)) {
            JSONArray jsonArray = JSONArray.parseArray(breakTimeArray);
            Set<AttBreakTime> attBreakTimeSet = new HashSet<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                AttBreakTime attBreakTime = new AttBreakTime();
                attBreakTime.setId(jsonObject.get("startTime") + " " + jsonObject.get("endTime") + "");
                attBreakTime.setStartTime(jsonObject.get("startTime") + "");
                attBreakTime.setEndTime(jsonObject.get("endTime") + "");
                attBreakTimeSet.add(attBreakTime);
            }
            return countBreakTime(attBreakTimeSet);
        }
        return 0;
    }

    @Override
    public String timeJudgment(String timeSlotId, String toWorkTime, String offWorkTime) {
        String result = null;
        // AttTimeSlot attTimeSlot = attTimeSlotDao.findOne(timeSlotId);
        AttTimeSlot attTimeSlot = attTimeSlotDao.getOne(timeSlotId);
        Set<AttBreakTime> attBreakTimeSet = attTimeSlot.getAttBreakTimeSet();
        if (attBreakTimeSet != null && !attBreakTimeSet.isEmpty()) {

            // 排序
            List<AttBreakTime> attBreakTimeList = new ArrayList<>(attBreakTimeSet);
            Collections.sort(attBreakTimeList, new Comparator<AttBreakTime>() {
                @Override
                public int compare(AttBreakTime o1, AttBreakTime o2) {
                    return o1.getStartTime().compareTo(o2.getStartTime()) < 0 ? -1 : 1;
                }
            });

            // 休息时段的最小开始时间
            String minAttBreakTime = attBreakTimeList.get(0).getStartTime();
            // 休息时段的最大结束时间
            String maxAttBreakTime = attBreakTimeList.get(attBreakTimeList.size() - 1).getEndTime();
            if (toWorkTime.compareTo(offWorkTime) < 0)// 不跨天
            {
                // 上班时间要小于所选的休息时间段的最小开始时间！
                if (toWorkTime.compareTo(minAttBreakTime) > 0) {
                    result = I18nUtil.i18nCode("att_timeSlot_toWorkLe") + minAttBreakTime;
                }
                if (result == null) {
                    // 下班时间要大于所选的休息时间段的最大结束时间！
                    if (offWorkTime.compareTo(maxAttBreakTime) < 0) {
                        result = I18nUtil.i18nCode("att_timeSlot_offWorkGe") + maxAttBreakTime;
                    }
                }
            } else // 跨天
            {
                if (toWorkTime.compareTo(minAttBreakTime) > 0 && offWorkTime.compareTo(minAttBreakTime) < 0) {
                    result = I18nUtil.i18nCode("att_timeSlot_crossDays_toWork") + minAttBreakTime;
                }
                if (result == null) {
                    if (toWorkTime.compareTo(maxAttBreakTime) > 0 && offWorkTime.compareTo(maxAttBreakTime) < 0) {
                        result = I18nUtil.i18nCode("att_timeSlot_crossDays_offWork") + maxAttBreakTime;
                    }
                }
            }
        }
        return result;
    }

    @Override
    public String breakTimeIsInWorkTime(String breakTimeListStr, String toWorkTime, String offWorkTime) {
        List<AttBreakTime> attBreakTimeList = JSON.parseArray(breakTimeListStr, AttBreakTime.class);
        Date now = new Date();
        String firstDay = AttDateUtils.dateToStrAsShort(now);
        String secondDay = AttDateUtils.dateToStrAsShort(DateUtil.addDay(now, 1));
        // 上班开始时间
        String workStartTime = String.format("%s %s", firstDay, toWorkTime);
        // 上班结束时间
        String workEndTime;
        // 上班不跨天
        if (toWorkTime.compareTo(offWorkTime) < 0) {
            workEndTime = String.format("%s %s", firstDay, offWorkTime);
        } else {
            // 上班跨天
            workEndTime = String.format("%s %s", secondDay, offWorkTime);
        }
        if (!CollectionUtil.isEmpty(attBreakTimeList)) {
            for (AttBreakTime breakTime : attBreakTimeList) {
                String breakStartTime;
                String breakEndTime;
                // 休息时间段跨天
                if (StringUtils.compare(breakTime.getStartTime(), breakTime.getEndTime()) > 0) {
                    breakStartTime = String.format("%s %s", firstDay, breakTime.getStartTime());
                    breakEndTime = String.format("%s %s", secondDay, breakTime.getEndTime());
                } else {
                    // 休息时间段不跨天,若休息结束时间小于开始上班时间，则表示第二天，否则是第一天
                    if (breakTime.getEndTime().compareTo(toWorkTime) < 0) {
                        breakStartTime = String.format("%s %s", secondDay, breakTime.getStartTime());
                        breakEndTime = String.format("%s %s", secondDay, breakTime.getEndTime());
                    } else {
                        breakStartTime = String.format("%s %s", firstDay, breakTime.getStartTime());
                        breakEndTime = String.format("%s %s", firstDay, breakTime.getEndTime());
                    }
                }
                if (StringUtils.compare(breakStartTime, workStartTime) < 0
                    || StringUtils.compare(breakEndTime, workEndTime) > 0) {
                    // 如果休息时间小于工作开始时间或者休息结束时间大于工作结束时间表示休息时间段没有在工作时间范围内
                    return "false";
                }

            }
        }
        return "true";
    }

    @Override
    @Transactional
    public void upgradeTo_220() {
        List<AttTimeSlot> attTimeSlotList = attTimeSlotDao.findAll();
        /**
         * 新增字段: BEFORE_TO_WORK_MINUTES (Integer) 上班前n分钟内签到有效 AFTER_TO_WORK_MINUTES (Integer) 上班后n分钟内签到有效
         * BEFORE_OFF_WORK_MINUTES (Integer) 下班前n分钟内签到有效 AFTER_OFF_WORK_MINUTES (Integer) 下班后n分钟内签到有效
         * BEFORE_WORK_OVERTIME_MINUTES (Integer) 上班n分钟前签到记加班 MIN_BEFORE_OVERTIME_MINUTES (Integer) 上班前最短加班分钟数
         * AFTER_WORK_OVERTIME_MINUTES (Integer) 下班n分钟后开始记加班 MIN_AFTER_OVERTIME_MINUTES (Integer) 下班后最短加班分钟数
         * --ENABLE_WORKING_HOURS (String) 是否启动工作时长 舍弃字段: PERIOD_NO 编号 END_SIGN_IN_TIME 结束签到时间 改用 AFTER_TO_WORK_MINUTES
         * 上班后n分钟内签到有效 START_SIGN_OFF_TIME 开始签退时间 改用 BEFORE_OFF_WORK_MINUTES 下班前n分钟内签到有效 startSegmentTime 开始段间时间
         * 改用休息时间段代替 endSegmentTime 结束段间时间 改用休息时间段代替 signInAdvanceTime 签到早于时间 改用 BEFORE_WORK_OVERTIME_MINUTES
         * 上班n分钟前签到记加班 signOutPosponeTime 签退晚于时间 改用 AFTER_WORK_OVERTIME_MINUTES 下班n分钟后开始记加班
         */
        List<List<AttTimeSlot>> splitAttTimeSlotList = CollectionUtil.split(attTimeSlotList, CollectionUtil.splitSize);
        int index = 1;
        String elasticCalValue = baseSysParamService.getValByName("att.baseRule.elasticCal");
        for (List<AttTimeSlot> attTimeSlots : splitAttTimeSlotList) {
            if (!CollectionUtil.isEmpty(attTimeSlots)) {
                for (AttTimeSlot attTimeSlot : attTimeSlots) {
                    if (0 == attTimeSlot.getPeriodType() && Objects.isNull(attTimeSlot.getBeforeToWorkMinutes())) {
                        // 上班时间-开始签到时间
                        Integer beforeToWorkMinutes =
                            diffTime(attTimeSlot.getStartSignInTime(), attTimeSlot.getToWorkTime());
                        // 上班前n分钟内签到有效
                        attTimeSlot.setBeforeToWorkMinutes(beforeToWorkMinutes);

                        // 上班后n分钟内签到有效
                        Integer afterToWorkMinutes =
                            diffTime(attTimeSlot.getToWorkTime(), attTimeSlot.getEndSignInTime());
                        attTimeSlot.setAfterToWorkMinutes(afterToWorkMinutes);

                        // 下班前n分钟内签到有效
                        Integer beforeOffWorkMinutes =
                            diffTime(attTimeSlot.getStartSignOffTime(), attTimeSlot.getOffWorkTime());
                        attTimeSlot.setBeforeOffWorkMinutes(beforeOffWorkMinutes);
                        // 下班前n分钟内签到有效
                        Integer afterOffWorkMinutes =
                            diffTime(attTimeSlot.getOffWorkTime(), attTimeSlot.getEndSignOffTime());
                        attTimeSlot.setAfterOffWorkMinutes(afterOffWorkMinutes);

                        // 上班n分钟前签到记加班
                        Integer beforeWorkOvertimeMinutes =
                            diffTime(attTimeSlot.getSignInAdvanceTime(), attTimeSlot.getToWorkTime());
                        attTimeSlot.setBeforeWorkOvertimeMinutes(beforeWorkOvertimeMinutes);
                        // 上班前最短加班分钟数
                        attTimeSlot.setMinBeforeOvertimeMinutes(0);
                        // 下班n分钟后开始记加班
                        Integer afterWorkOvertimeMinutes =
                            diffTime(attTimeSlot.getOffWorkTime(), attTimeSlot.getSignOutPosponeTime());
                        attTimeSlot.setAfterWorkOvertimeMinutes(afterWorkOvertimeMinutes);
                        // 下班后最短加班分钟数
                        attTimeSlot.setMinAfterOvertimeMinutes(0);

                        // 是否启动弹性上班(true:启用。false：不启用)
                        attTimeSlot.setEnableFlexibleWork("false");
                        // 调整间断休息为休息时间段
                        if (attTimeSlot.getIsSegmentDeduction()) {
                            String startSegmentTime = attTimeSlot.getStartSegmentTime();
                            String endSegmentTime = attTimeSlot.getEndSegmentTime();
                            AttBreakTime attBreakTime =
                                attBreakTimeDao.findByStartTimeAndEndTime(startSegmentTime, endSegmentTime);
                            if (Objects.isNull(attBreakTime)) {
                                attBreakTime = new AttBreakTime();
                                attBreakTime.setName("T" + (index++));
                                attBreakTime.setStartTime(startSegmentTime);
                                attBreakTime.setEndTime(endSegmentTime);
                                attBreakTimeDao.save(attBreakTime);
                            }
                            Set<AttBreakTime> attBreakTimeSet = attTimeSlot.getAttBreakTimeSet();
                            attBreakTimeSet.add(attBreakTime);
                        }
                    } else {
                        // 弹性时间段 设置弹性时长计算方式 如果没有默认 0:两两打卡累积时长
                        attTimeSlot.setElasticCal(StringUtils.isNotBlank(elasticCalValue) ? elasticCalValue : "0");
                    }
                }
                attTimeSlotDao.saveAll(attTimeSlots);
            }
        }
    }

    @Override
    public TreeItem getTree() {
        List<TreeItem> items = new ArrayList<>();
        List<AttTimeSlot> attTimeSlotList = attTimeSlotDao.findByPeriodType(AttConstant.PERIODTYPE_NORMAL);
        for (AttTimeSlot attTimeSlot : attTimeSlotList) {
            TreeItem item = new TreeItem();
            item.setId(attTimeSlot.getId());
            StringBuffer textBuf = new StringBuffer();
            textBuf.append(attTimeSlot.getPeriodName());
            if (AttConstant.PERIODTYPE_NORMAL.equals(attTimeSlot.getPeriodType())) {
                textBuf.append("(").append(attTimeSlot.getToWorkTime()).append("-").append(attTimeSlot.getOffWorkTime())
                    .append(")");
            } else {
                textBuf.append("(").append(attTimeSlot.getStartSignInTime()).append("-")
                    .append(attTimeSlot.getEndSignOffTime()).append(")");
            }
            item.setText(textBuf.toString());
            TreeItem pItem = new TreeItem("0");
            item.setParent(pItem);
            items.add(0, item);
        }
        List<TreeItem> treeItems = TreeBuilder.newTreeBuilder(TreeItem.class, String.class).buildToTreeList(items);
        return new TreeItem("0", treeItems);
    }

    @Override
    public List<AttTimeSlotItem> getAllTimeSlotItem() {
        List<AttTimeSlotItem> itemList = new ArrayList<>();
        List<AttTimeSlot> attTimeSlotList = attTimeSlotDao.findAll();
        for (AttTimeSlot attTimeSlot : attTimeSlotList) {
            AttTimeSlotItem attTimeSlotItem = ModelUtil.copyProperties(attTimeSlot, new AttTimeSlotItem());
            Set<AttBreakTime> attBreakTimeSet = attTimeSlot.getAttBreakTimeSet();
            List<AttBreakTimeItem> attBreakTimeItems =
                ModelUtil.copyListProperties(attBreakTimeSet, AttBreakTimeItem.class);
            attTimeSlotItem.setAttBreakTimeItems(attBreakTimeItems);
            itemList.add(attTimeSlotItem);
        }
        return itemList;
    }
}