package com.zkteco.zkbiosecurity.att.utils;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.lang.reflect.ParameterizedType;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

import org.springframework.beans.BeanUtils;

@Deprecated
public class BeanToMapUtil {
    /**
     * 将一个 Map 对象转化为一个 JavaBean
     * 
     * @param <T>
     *
     * @param type
     *            要转化的类型
     * @param map
     *            包含属性值的 map
     * @return 转化出来的 JavaBean 对象
     * @throws IntrospectionException
     *             如果分析类属性失败
     * @throws IllegalAccessException
     *             如果实例化 JavaBean 失败
     * @throws InstantiationException
     *             如果实例化 JavaBean 失败
     * @throws InvocationTargetException
     *             如果调用属性的 setter 方法失败
     */
    public static <T> T convertMap(Class<T> type, Map<?, ?> map) throws IntrospectionException, IllegalAccessException,
        InstantiationException, InvocationTargetException, ParseException {
        Object obj = type.newInstance(); // 创建 JavaBean 对象
        // 给 JavaBean 对象的属性赋值
        PropertyDescriptor[] propertyDescriptors = BeanUtils.getPropertyDescriptors(type);;;
        for (int i = 0; i < propertyDescriptors.length; i++) {
            PropertyDescriptor descriptor = propertyDescriptors[i];
            String propertyName = descriptor.getName();
            if (map.containsKey(propertyName)) {
                Object value = map.get(propertyName);
                if (value != null && !value.toString().isEmpty()) {
                    Object[] args = new Object[1];
                    if (descriptor.getWriteMethod() == null) {
                        continue;
                    }
                    Class<?>[] parameterTypes = descriptor.getWriteMethod().getParameterTypes();
                    if (parameterTypes[0].getName().equals(Date.class.getName())) {// 时间类型
                        if (value.toString().length() >= 19) {//
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            value = simpleDateFormat.parse(value.toString().substring(0, 19));
                        } else if (Pattern.matches("\\d+", value.toString())) {// 数字
                            String strval = value.toString();
                            long times = Long.parseLong(strval);
                            if (value.toString().length() == 10) {// 秒
                                times = times * 1000;
                            }
                            value = new Date(times);
                        } else {
                            if (!(value instanceof Date)) {
                                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                                value = simpleDateFormat.parse(value.toString().substring(0, 10));
                            }
                        }
                        descriptor.getWriteMethod().invoke(obj, value);
                    } else if (parameterTypes[0].getName().toLowerCase().contains("int")) {
                        if (value != null && value instanceof Double) {
                            value = ((Double)value).intValue();
                        } else if (value != null && value.toString().length() > 0) {
                            args[0] = Integer.parseInt(value + "");
                            descriptor.getWriteMethod().invoke(obj, args);
                        }
                    } else if (parameterTypes[0].getName().toLowerCase().contains("long")) {
                        if (value != null && value instanceof Double) {
                            value = ((Double)value).longValue();
                        } else if (value != null && value.toString().length() > 0) {
                            args[0] = Long.parseLong(value + "");
                            descriptor.getWriteMethod().invoke(obj, args);
                        }
                    } else if (parameterTypes[0].getName().toLowerCase().contains("boolean")) {
                        if (value != null && value.toString().length() > 0) {
                            args[0] = Boolean.valueOf(value + "");
                            descriptor.getWriteMethod().invoke(obj, args);
                        }
                    } else if (parameterTypes[0].getName().toLowerCase().contains("double")) {
                        if (value != null && value.toString().length() > 0) {
                            args[0] = Double.parseDouble(value + "");
                            descriptor.getWriteMethod().invoke(obj, args);
                        }
                    } else if (parameterTypes[0].getName().toLowerCase().contains("list")) {
                        Parameter parameters = descriptor.getWriteMethod().getParameters()[0];
                        ParameterizedType parameterizedType = (ParameterizedType)parameters.getParameterizedType();
                        Class<?> type2 = (Class<?>)parameterizedType.getActualTypeArguments()[0];
                        List<Object> list = (List<Object>)value;
                        List objs = new ArrayList<>();
                        if (!type2.getName().toLowerCase().contains("map")
                            && !type2.getName().toLowerCase().contains("object")) {
                            for (Object objv : list) {
                                objs.add(convertMap(type2, (Map)objv));
                            }
                            args[0] = objs;
                        } else {
                            args[0] = list;
                        }
                        descriptor.getWriteMethod().invoke(obj, args);
                    } else if (parameterTypes[0].getName().toLowerCase().contains("map")) {
                        Class<?> class1 = descriptor.getWriteMethod().getParameterTypes()[0];
                        if (!class1.getName().toLowerCase().contains("map")) {
                            value = convertMap(class1, (Map)value);
                        }
                        args[0] = value;
                        descriptor.getWriteMethod().invoke(obj, args);
                    } else {
                        Class<?> class1 = descriptor.getWriteMethod().getParameterTypes()[0];
                        if (value instanceof Map && !class1.getName().toLowerCase().contains("map")) {
                            value = convertMap(class1, (Map)value);
                            args[0] = value;
                        } else {
                            args[0] = value + "";
                        }
                        descriptor.getWriteMethod().invoke(obj, args);
                    }
                }
            }
        }
        return (T)obj;
    }

    /**
     * 将一个 JavaBean 对象转化为一个 Map
     *
     * @param bean
     *            要转化的JavaBean 对象
     * @return 转化出来的 Map 对象
     * @throws IntrospectionException
     *             如果分析类属性失败
     * @throws IllegalAccessException
     *             如果实例化 JavaBean 失败
     * @throws InvocationTargetException
     *             如果调用属性的 setter 方法失败
     */
    public static Map<String, Object> convertBean(Object bean)
        throws IntrospectionException, IllegalAccessException, InvocationTargetException {
        Class<? extends Object> type = bean.getClass();
        Map<String, Object> returnMap = new HashMap<String, Object>();
        BeanInfo beanInfo = Introspector.getBeanInfo(type);

        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
        for (int i = 0; i < propertyDescriptors.length; i++) {
            PropertyDescriptor descriptor = propertyDescriptors[i];
            String propertyName = descriptor.getName();
            if (!propertyName.equals("class")) {
                Method readMethod = descriptor.getReadMethod();
                Object result = readMethod.invoke(bean, new Object[0]);
                if (result != null) {
                    returnMap.put(propertyName, result);
                } else {
                    returnMap.put(propertyName, "");
                }
            }
        }
        return returnMap;
    }

}
