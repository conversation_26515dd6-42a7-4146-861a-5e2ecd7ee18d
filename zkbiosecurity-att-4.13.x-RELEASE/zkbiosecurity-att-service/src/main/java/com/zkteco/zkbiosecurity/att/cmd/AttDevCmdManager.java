package com.zkteco.zkbiosecurity.att.cmd;

import java.io.File;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.cmd.att.shortcutkey.CmdShortcutKeyConstructor;
import com.zkteco.zkbiosecurity.cmd.att.shortcutkey.CmdShortcutKeyInfo;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.pers.vo.PersBioPhotoItem;
import com.zkteco.zkbiosecurity.pers.vo.PersBioTemplateItem;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.adms.service.AdmsDevCmdService;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.model.AttDevice;
import com.zkteco.zkbiosecurity.att.service.AttDeviceOptionService;
import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.cmd.att.attlog.CmdAttLogConstructor;
import com.zkteco.zkbiosecurity.cmd.att.attlog.CmdAttLogInfo;
import com.zkteco.zkbiosecurity.cmd.att.biodata.CmdBioDataConstructor;
import com.zkteco.zkbiosecurity.cmd.att.biodata.CmdBioDataInfo;
import com.zkteco.zkbiosecurity.cmd.att.biophoto.CmdBiophotoDataConstructor;
import com.zkteco.zkbiosecurity.cmd.att.biophoto.CmdBiophotoDataInfo;
import com.zkteco.zkbiosecurity.cmd.att.biotemplate.CmdBioTemplateConstructor;
import com.zkteco.zkbiosecurity.cmd.att.constants.CmdAttConstants;
import com.zkteco.zkbiosecurity.cmd.att.device.CmdDevice;
import com.zkteco.zkbiosecurity.cmd.att.device.CmdDeviceConstructor;
import com.zkteco.zkbiosecurity.cmd.att.face.CmdFaceDataConstructor;
import com.zkteco.zkbiosecurity.cmd.att.face.CmdFaceDataInfo;
import com.zkteco.zkbiosecurity.cmd.att.finger.CmdFingerDataConstructor;
import com.zkteco.zkbiosecurity.cmd.att.finger.CmdFingerDataInfo;
import com.zkteco.zkbiosecurity.cmd.att.sms.CmdSmsConstructor;
import com.zkteco.zkbiosecurity.cmd.att.sms.CmdSmsInfo;
import com.zkteco.zkbiosecurity.cmd.att.userinfo.CmdUserInfo;
import com.zkteco.zkbiosecurity.cmd.att.userinfo.CmdUserInfoConstructor;
import com.zkteco.zkbiosecurity.cmd.att.userpic.CmdUserPicDataConstructor;
import com.zkteco.zkbiosecurity.cmd.att.userpic.CmdUserPicDataInfo;
import com.zkteco.zkbiosecurity.cmd.att.usersms.CmdUserSmsConstructor;
import com.zkteco.zkbiosecurity.cmd.att.usersms.CmdUserSmsInfo;
import com.zkteco.zkbiosecurity.core.utils.FileEncryptUtil;
import com.zkteco.zkbiosecurity.core.utils.FileUtil;
import com.zkteco.zkbiosecurity.pers.vo.PersCardItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

/**
 * AttDevCmdManager 只做数据下发操作，并不做代码业务逻辑处理，要是想要业务逻辑处理，请在调用之前，处理解决
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
 * @date 2018/11/8 17:31
 */

@Component
public class AttDevCmdManager {

    Logger log = LoggerFactory.getLogger(getClass());

    @Autowired(required = false)
    private AdmsDevCmdService admsDevCmdService;
    @Autowired
    private AttDeviceOptionService attDeviceOptionService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Value("${security.require-ssl:false}")
    private String isSupportHttps;

    /**
     * 下发人员基础信息
     */
    public void sendPersonToDev(String devSn, AttPersonOptItem attPersonOptItem) {
        // 姓名转化
        String name = "";
        if (StringUtils.isNotBlank(attPersonOptItem.getPersonName())) {
            name = attPersonOptItem.getPersonName();
        }
        if (StringUtils.isNotBlank(attPersonOptItem.getPersonLastName())) {
            name = StringUtils.isNotBlank(name) ? name + "&" : name;
            name += attPersonOptItem.getPersonLastName();
        }

        // 卡号进制转化
        String cardNo = "";
        if (StringUtils.isNotBlank(attPersonOptItem.getPersonCardNo())) {
            cardNo = attPersonOptItem.getPersonCardNo();
            String persCardHex = baseSysParamService.getValByName("pers.cardHex");
            if ("1".equals(persCardHex)) {
                cardNo = AttCommonUtils.convertCardNo(attPersonOptItem.getPersonCardNo(), 16, 10);
            }
        }

        // 创建人员基础数据
        CmdUserInfo cmdUserInfo =
            new CmdUserInfo(attPersonOptItem.getPersonPin(), name, attPersonOptItem.getPersonPwd(), cardNo,
                attPersonOptItem.getPersonDevAuth(), attPersonOptItem.getVerifyMode());
        admsDevCmdService.addCmd(devSn, CmdUserInfoConstructor.setUserInfo(cmdUserInfo), false);
    }

    /**
     * 下发用户头像信息 (Base64或URL)
     *
     * @param attDevice
     * @param devOptions
     */
    public void sendPersonPhotoToDev(AttDevice attDevice, AttPersonPhotoOptItem attPersonPhotoOptItem,
        List<AttDeviceOptionItem> devOptions) {
        if (StringUtils.isBlank(attPersonPhotoOptItem.getPersonPhotoContent())) {
            return;
        }
        // 是否支持头像下发
        boolean isPhotoFunOn = isSupportFunction(devOptions, "PhotoFunOn");
        if (isPhotoFunOn) {
            // 是否支持头像下发URL
            boolean userPicURLFunOn = isSupportFunction(devOptions, "UserPicURLFunOn");
            if (userPicURLFunOn) {
                // 下发URL
                CmdUserPicDataInfo userPicDataInfo = new CmdUserPicDataInfo(attPersonPhotoOptItem.getPersonPin(), "",
                    "", "1", attPersonPhotoOptItem.getPersonPhotoUrl());
                admsDevCmdService.addCmd(attDevice.getDevSn(), CmdUserPicDataConstructor.setUserPicUrl(userPicDataInfo),
                    false);
            } else {
                // 下发Base64
                CmdUserPicDataInfo userPicDataInfo = new CmdUserPicDataInfo(attPersonPhotoOptItem.getPersonPin(),
                    String.valueOf(attPersonPhotoOptItem.getPersonPhotoContent().length()),
                    attPersonPhotoOptItem.getPersonPhotoContent());
                admsDevCmdService.addCmd(attDevice.getDevSn(), CmdUserPicDataConstructor.setUserPic(userPicDataInfo),
                    false);
            }
        }
    }

    /**
     * 下发指纹模板（旧协议）
     */
    public void sendPersonFPToDev(AttDevice attDevice, List<AttBioTemplateItem> attBioTemplateList,
        List<AttDeviceOptionItem> devOptions) {
        // 是否支持指纹下发
        boolean isFingerFunOn = isSupportFunction(devOptions, "FingerFunOn");
        if (isFingerFunOn) {
            sendFPToDevByVersion(attDevice.getDevSn(), attDevice.getFpVersion(), attBioTemplateList);
        }
    }

    /**
     * 下发指纹模板（新协议,多模态协议）
     */
    public void sendMultiFPDataToDev(AttDevice attDevice, List<AttBioTemplateItem> attBioTemplateList,
        List<AttDeviceOptionItem> devOptions, Short bioDataType) {
        // 是否支持
        boolean support = isMultiBioDataSupport(devOptions, bioDataType);
        if (support) {
            String fpVersion = getMultiBioVersion(devOptions, bioDataType);
            sendFPToDevByVersion(attDevice.getDevSn(), fpVersion, attBioTemplateList);
        }
    }

    /**
     * 下发人脸模板（兼容人脸算法版本7.0、9.0、12.0版本都走一体化模板下发）
     */
    public boolean sendPersonFaceToDev(AttDevice attDevice, List<AttBioTemplateItem> attBioTemplateList,
        List<AttDeviceOptionItem> devOptions) {
        boolean isSend = false;
        // 是否支持人脸下发
        boolean isFaceFunOn = isSupportFunction(devOptions, "FaceFunOn");
        if (isFaceFunOn) {
            String faceVersion = attDevice.getFaceVersion();
            for (AttBioTemplateItem attBioTemplate : attBioTemplateList) {
                if (BaseConstants.BaseBioType.FACE_BIO_TYPE.equals(attBioTemplate.getBioType())) {
                    if (BaseConstants.BaseBioType.FACE_BIO_VERSION.equals(faceVersion)
                        && faceVersion.equals(attBioTemplate.getVersion())) {
                        // 只做下发命令操作，不能在写逻辑处理代码，需要把处理逻辑代码在调用它之前编写
                        sendPersonFace7ToDev(attDevice.getDevSn(), attBioTemplate);
                        isSend = true;
                    } else if (StringUtils.isNotEmpty(faceVersion) && faceVersion.equals(attBioTemplate.getVersion())) {
                        // 模板一体化都走这个流程，目前人脸算法9.0和12.0都走模板一体化 add by hook.fang 2019-12-18
                        sendBiodataToDev(attDevice.getDevSn(), attBioTemplate);
                        isSend = true;
                    }
                }
            }
        }
        return isSend;
    }

    /**
     * 下发近红外人脸一体化模板（多模态协议）
     */
    public void sendMultiFaceDataToDev(AttDevice attDevice, List<AttBioTemplateItem> attBioTemplateList,
        List<AttDeviceOptionItem> devOptions, Short bioDataType) {
        // 是否支持
        boolean support = isMultiBioDataSupport(devOptions, bioDataType);
        if (support) {
            // 获取算法版本
            String devVer = getMultiBioVersion(devOptions, bioDataType);
            for (AttBioTemplateItem attBioTemplate : attBioTemplateList) {
                if (bioDataType.equals(attBioTemplate.getBioType())) {
                    // 判断人脸版本是否一致，是则下发一体化人脸模板信息
                    if (Math.abs(Float.valueOf(devVer) - Float.valueOf(attBioTemplate.getVersion())) <= 0) {
                        sendBiodataToDev(attDevice.getDevSn(), attBioTemplate);
                    }
                }
            }
        }
    }

    /**
     * 下发可见光人脸一体化模板（多模态协议）
     */
    public boolean sendMultiViFaceDataToDev(AttDevice attDevice, List<AttBioTemplateItem> attBioTemplateList,
        List<AttDeviceOptionItem> devOptions, Short bioPhotoType) {
        boolean isViFaceExecute = false;
        // 是否支持
        boolean support = isMultiBioDataSupport(devOptions, bioPhotoType);
        if (support) {
            // 获取算法版本
            String devVer = getMultiBioVersion(devOptions, bioPhotoType);
            for (AttBioTemplateItem attBioTemplate : attBioTemplateList) {
                if (bioPhotoType.equals(attBioTemplate.getBioType())) {
                    // 判断人脸版本是否一致，是则下发一体化可见光人脸模板
                    if (Math.abs(Float.valueOf(devVer) - Float.valueOf(attBioTemplate.getVersion())) <= 0) {
                        sendBiodataToDev(attDevice.getDevSn(), attBioTemplate);
                        isViFaceExecute = true;
                    }
                }
            }
        }
        return isViFaceExecute;
    }

    /**
     * 下发比对照片（旧协议）
     */
    public void sendBioPhotoToDev(String devSn, String pin, List<AttDeviceOptionItem> devOptions) {
        // 是否支持可见光
        boolean isSuppVisilightFun = isSupportFunList(devOptions, "AttSupportFunList", 3);
        // 是否支持url下发
        boolean isVisilightFun = isSupportFunction(devOptions, "VisilightFun");
        if (isSuppVisilightFun) {
            if (isVisilightFun) {
                sendBioPhotoUrlToDev(devSn, pin);
            } else {
                sendBioPhotoToDev(devSn, pin);
            }
        }
    }

    /**
     * 下发比对照片(新协议，多模态协议)
     */
    public void sendMultiBioPhotoToDev(AttDevice attDevice, List<AttBioPhotoItem> attBioPhotoItemList,
        List<AttDeviceOptionItem> devOptions, Short bioType) {

        if (attBioPhotoItemList == null || attBioPhotoItemList.size() == 0) {
            return;
        }

        boolean support = isMultiBioPhotoSupport(devOptions, bioType);
        if (support) {
            for (AttBioPhotoItem attBioPhotoItem : attBioPhotoItemList) {
                if (bioType.equals(attBioPhotoItem.getBioType())) {
                    CmdBiophotoDataInfo biophotoDataInfo = new CmdBiophotoDataInfo(attBioPhotoItem.getPin(),
                        bioType + "", (short)0, (short)0, "0", "", "1", attBioPhotoItem.getUrl());
                    admsDevCmdService.addCmd(attDevice.getDevSn(),
                        CmdBiophotoDataConstructor.setBioPhotoUrl(biophotoDataInfo), false);
                }
            }
        }
    }

    /**
     * 下发比对照片(新协议，多模态协议)
     */
    public void sendMultiFaceBioPhotoToDev(AttDevice attDevice, String pin, List<AttDeviceOptionItem> devOptions,
        Short bioDataType) {
        // 获取支持多模态生物特征图片参数
        String multiBioPhotoSupport = getOptValue(devOptions, "MultiBioPhotoSupport");
        String[] multiBioPhotoSupportAry = multiBioPhotoSupport.split(":");

        // 是否支持可见光人脸图像
        String isSupportPhoto = multiBioPhotoSupportAry[bioDataType];
        if (CmdAttConstants.SUPPORT.equals(isSupportPhoto)) {
            // 是否支持url下发
            boolean isVisilightFun = isSupportFunction(devOptions, "VisilightFun");
            if (isVisilightFun) {
                sendBioPhotoUrlToDev(attDevice.getDevSn(), pin);
            } else {
                sendBioPhotoToDev(attDevice.getDevSn(), pin);
            }
        }
    }

    public void delUserInfo(AttDevice attDevice, String pin) {
        admsDevCmdService.addCmd(attDevice.getDevSn(), CmdUserInfoConstructor.delUserInfo(pin), false);
    }

    public void cleanCmdBySn(AttDevice attDevice) {
        admsDevCmdService.cleanCmdBySn(attDevice.getDevSn());
    }

    public void clearAttUserInfo(AttDevice attDevice) {
        admsDevCmdService.addCmd(attDevice.getDevSn(), CmdUserInfoConstructor.clearAllUserInfo(), false);
    }

    /**
     * 发送INFO
     */
    public void sendInfo(AttDevice attDevice) {
        admsDevCmdService.addCmd(attDevice.getDevSn(), CmdDeviceConstructor.info(), false);
    }

    /**
     * 重启指令
     */
    public void reboot(AttDevice attDevice) {
        admsDevCmdService.addCmd(attDevice.getDevSn(), CmdDeviceConstructor.reboot(), true);
    }

    /**
     * 发送信息
     *
     * @param attDevice
     * @param tag (253:对公，254:对私)
     * @param uid
     * @param startTime
     * @param min
     * @param msg
     */
    public void sendSms(AttDevice attDevice, String msg, String tag, String uid, String min, String startTime) {
        CmdSmsInfo cmdSmsInfo = new CmdSmsInfo(msg, tag, uid, min, startTime);
        admsDevCmdService.addCmd(attDevice.getDevSn(), CmdSmsConstructor.updateSmsMsg(cmdSmsInfo), false);
    }

    /**
     * 对私信息列表
     */
    public void sendUserSms(AttDevice attDevice, String pin, String uid) {
        CmdUserSmsInfo cmdUserSmsInfo = new CmdUserSmsInfo(pin, uid);
        admsDevCmdService.addCmd(attDevice.getDevSn(), CmdUserSmsConstructor.updateUserSms(cmdUserSmsInfo), false);
    }

    /**
     * 发送根据时间区间获取对应的考勤记录
     */
    public void loadAttLogByTime(AttDevice attDevice, String startTime, String endTime) {
        CmdAttLogInfo cmdAttLogInfo = new CmdAttLogInfo(startTime, endTime);
        admsDevCmdService.addCmd(attDevice.getDevSn(), CmdAttLogConstructor.queryAttlog(cmdAttLogInfo), false);
    }

    /**
     * 发送check指令
     */
    public void sendCheck(AttDevice attDevice) {
        admsDevCmdService.addCmd(attDevice.getDevSn(), CmdDeviceConstructor.check(), false);
    }

    /**
     * 发送获取pin对应的人员信息
     */
    public void uploadUserInfoByPin(AttDevice attDevice, String pin) {
        admsDevCmdService.addCmd(attDevice.getDevSn(), CmdUserInfoConstructor.getUserInfo(pin), false);
    }

    /**
     * 清空考勤照片
     */
    public void cleanPhoto(AttDevice attDevice) {
        admsDevCmdService.addCmd(attDevice.getDevSn(), CmdAttLogConstructor.clearLogPhoto(), false);
    }

    /**
     * 清空考勤记录
     */
    public void cleanLog(AttDevice attDevice) {
        admsDevCmdService.addCmd(attDevice.getDevSn(), CmdAttLogConstructor.clearLog(), false);
    }

    /**
     * 下发模板（最终统一组装下发）
     */
    public void sendBiodataToDev(String devSn, AttBioTemplateItem attBioTemplate) {
        String duress = attBioTemplate.getDuress() ? "1" : "0";
        String[] version = StringUtils.split(attBioTemplate.getVersion(), ".");
        String majaorVer = (null != version && version.length > 0) ? version[0] : "0";
        String minorVer = (null != version && version.length > 1) ? version[1] : "0";
        String format = "0";
        CmdBioDataInfo cmdBioDataInfo = new CmdBioDataInfo(attBioTemplate.getPersonPin(),
            String.valueOf(attBioTemplate.getTemplateNo()), String.valueOf(attBioTemplate.getTemplateNoIndex()),
            String.valueOf(attBioTemplate.getValidType()), duress, String.valueOf(attBioTemplate.getBioType()),
            majaorVer, minorVer, format, attBioTemplate.getTemplate());
        admsDevCmdService.addCmd(devSn, CmdBioDataConstructor.setBioData(cmdBioDataInfo), false);
    }

    /**
     * 下发一体化掌静脉模板（旧协议）
     */
    public void sendPVBioTemplateToDev(AttDevice attDevice, List<AttBioTemplateItem> attBioTemplateList,
        List<AttDeviceOptionItem> devOptions) {
        // 是否支持掌静脉
        boolean isPvFunOn = isSupportFunction(devOptions, "PvFunOn");
        if (isPvFunOn) {
            // 获取掌静脉算法版本
            String pvVersion = getOptValue(devOptions, "PvVersion");
            attBioTemplateList.forEach(attBioTemplate -> {
                if (BaseConstants.BaseBioType.PALM_BIO_TYPE.equals(attBioTemplate.getBioType())) {
                    if (StringUtils.isNotEmpty(pvVersion) && pvVersion.equals(attBioTemplate.getVersion())) {
                        sendBiodataToDev(attDevice.getDevSn(), attBioTemplate);
                    }
                }
            });
        }
    }

    /**
     * 下发一体化模版（新协议，多模态协议）
     */
    public boolean sendMultiBioDataToDev(AttDevice attDevice, List<AttBioTemplateItem> attBioTemplateList,
        List<AttDeviceOptionItem> devOptions, Short bioDataType) {
        boolean isSendMultiBioData = false;
        // 是否支持
        boolean support = isMultiBioDataSupport(devOptions, bioDataType);
        if (support) {
            // 获取算法版本
            String devVer = getMultiBioVersion(devOptions, bioDataType);
            for (AttBioTemplateItem attBioTemplate : attBioTemplateList) {
                if (bioDataType.equals(attBioTemplate.getBioType())) {
                    // 判断版本是否一致，是则下发一体化模板信息
                    if (Math.abs(Float.valueOf(devVer) - Float.valueOf(attBioTemplate.getVersion())) <= 0) {
                        sendBiodataToDev(attDevice.getDevSn(), attBioTemplate);
                        isSendMultiBioData = true;
                    }
                }
            }
        }
        return isSendMultiBioData;
    }

    /**
     * 根据生物识别类型删除人员生物识别信息
     */
    public void delBioTemplate(AttDeviceItem attDeviceItem, List<AttDeviceOptionItem> devOptions, String pin,
        Short bioType, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(attDeviceItem, devOptions);
        switch (bioType) {
            case CmdAttConstants.TEMPLATE_FP:
                admsDevCmdService.addCmd(attDeviceItem.getDevSn(),
                    CmdBioTemplateConstructor.delFingerprints(cmdDevice, pin), imme);
                break;
            case CmdAttConstants.TEMPLATE_FACE:
                admsDevCmdService.addCmd(attDeviceItem.getDevSn(), CmdBioTemplateConstructor.delFaces(cmdDevice, pin),
                    imme);
                break;
            case CmdAttConstants.TEMPLATE_PALM:
                admsDevCmdService.addCmd(attDeviceItem.getDevSn(),
                    CmdBioTemplateConstructor.delPalmveins(cmdDevice, pin), imme);
                break;
            case CmdAttConstants.TEMPLATE_BIOPHOTO:
                admsDevCmdService.addCmd(attDeviceItem.getDevSn(),
                    CmdBioTemplateConstructor.delVislightDatas(cmdDevice, pin), imme);
                break;
            default:
                // 兼容后续新的一体化模板
                admsDevCmdService.addCmd(attDeviceItem.getDevSn(),
                    CmdBioTemplateConstructor.delBioDataByType(cmdDevice, pin, bioType), imme);
                break;
        }
    }

    /**
     * 删除人员比对照片
     */
    public void delBioPhoto(AttDeviceItem attDeviceItem, List<AttDeviceOptionItem> devOptions, String pin,
        boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(attDeviceItem, devOptions);
        admsDevCmdService.addCmd(attDeviceItem.getDevSn(), CmdBiophotoDataConstructor.delAttBiophotos(cmdDevice, pin),
            imme);
    }

    /**
     * 下发生物模板到设备（指纹、面部、掌纹）
     */
    public void setBioTemplate2Dev(AttDeviceItem attDeviceItem, List<AttDeviceOptionItem> devOptions,
        AttBioTemplateItem attBioTemplate, boolean imme) {
        switch (attBioTemplate.getBioType()) {
            case CmdAttConstants.TEMPLATE_FP:
                boolean isFingerFunOn = isSupportFunction(devOptions, "FingerFunOn");
                if (isFingerFunOn) {
                    sendFPToDevByVersion(attDeviceItem.getDevSn(), attDeviceItem.getFpVersion(), new ArrayList() {
                        {
                            add(attBioTemplate);
                        }
                    });
                }
                break;
            case CmdAttConstants.TEMPLATE_FACE:
                String faceVersion = attDeviceItem.getFaceVersion();
                if (BaseConstants.BaseBioType.FACE_BIO_VERSION.equals(faceVersion)
                    && BaseConstants.BaseBioType.FACE_BIO_VERSION.equals(attBioTemplate.getVersion())) {
                    sendPersonFace7ToDev(attDeviceItem.getDevSn(), attBioTemplate);
                } else if (StringUtils.isNotEmpty(faceVersion) && faceVersion.equals(attBioTemplate.getVersion())) {
                    // 模板一体化都走这个流程，目前人脸算法9.0和12.0都走模板一体化
                    sendBiodataToDev(attDeviceItem.getDevSn(), attBioTemplate);
                }
                break;
            case CmdAttConstants.TEMPLATE_PALM:
                String pvVersion = getOptValue(devOptions, "PvVersion");
                if (StringUtils.isNotEmpty(pvVersion) && pvVersion.equals(attBioTemplate.getVersion())) {
                    sendBiodataToDev(attDeviceItem.getDevSn(), attBioTemplate);
                } else {
                    log.error("devSn=" + attDeviceItem.getDevSn() + ",pvVersion=" + pvVersion + ",tempateVersion="
                        + attBioTemplate.getVersion());
                }
                break;
            default:
                // 兼容后续新的一体化模板
                AttDevice attDevice = new AttDevice();
                ModelUtil.copyProperties(attDeviceItem, new AttDevice());
                sendMultiBioDataToDev(attDevice, Arrays.asList(attBioTemplate), devOptions,
                    attBioTemplate.getBioType());
                break;
        }
    }

    /**
     * 下发比对照片到设备【下发其他模块分发的比对照片base64数据】
     */
    public void setBioPhoto2Dev(AttDeviceItem attDevice, List<AttDeviceOptionItem> devOptions,
        AttBioTemplateItem attBioTemplate, boolean imme) {
        boolean isSuppVisilightFun = isSupportFunList(devOptions, "AttSupportFunList", 3);
        if (isSuppVisilightFun) {
            // 下发base64
            String base64Image = attBioTemplate.getTemplate();
            if (StringUtils.isNotBlank(base64Image)) {
                CmdBiophotoDataInfo biophotoDataInfo = new CmdBiophotoDataInfo(attBioTemplate.getPersonPin(),
                    String.valueOf(attBioTemplate.getBioType()), String.valueOf(base64Image.length()), base64Image);
                admsDevCmdService.addCmd(attDevice.getDevSn(), CmdBiophotoDataConstructor.setBioPhoto(biophotoDataInfo),
                    false);
            }
        }
    }

    /**
     * 下发人员头像到设备【下发其他模块分发的人员头像base64数据】
     */
    public void setUserPic2Dev(AttDeviceItem attDevice, List<AttDeviceOptionItem> devOptions,
        AttBioTemplateItem attBioTemplate, boolean imme) {
        boolean isPhotoFunOn = isSupportFunction(devOptions, "PhotoFunOn");
        if (isPhotoFunOn) {
            // 下发base64
            String base64Image = attBioTemplate.getTemplate();
            if (StringUtils.isNotBlank(base64Image)) {
                CmdUserPicDataInfo userPicDataInfo = new CmdUserPicDataInfo(attBioTemplate.getPersonPin(),
                    String.valueOf(base64Image.length()), base64Image);
                admsDevCmdService.addCmd(attDevice.getDevSn(), CmdUserPicDataConstructor.setUserPic(userPicDataInfo),
                    false);
            }
        }
    }

    /**
     * 升级固件
     */
    public Long upgradeFirmware(AttDeviceItem attDevice, File file, String host, int port, boolean imme)
        throws Exception {
        List<AttDeviceOptionItem> devOptions = new ArrayList<>();
        String subcontractingUpgradeFunOn =
            attDeviceOptionService.getValueByDevSnAndName(attDevice.getDevSn(), "SubcontractingUpgradeFunOn");
        if (StringUtils.isNotBlank(subcontractingUpgradeFunOn)) {
            AttDeviceOptionItem devOptionItem = new AttDeviceOptionItem();
            devOptionItem.setName("SubcontractingUpgradeFunOn");
            devOptionItem.setValue(subcontractingUpgradeFunOn);
            devOptions.add(devOptionItem);
        }

        String url = file.toString().substring(file.toString().indexOf("upload") - 1).replace("\\", "/");
        // 支持断点续传文件升级功能的，可使用相对路径
        if (!attDeviceOptionService.isSupportFunction(attDevice.getId(), "SubcontractingUpgradeFunOn")) {
            boolean isSSL = "true".equals(isSupportHttps);
            if (isSSL) {
                url = "https*//" + host + "*" + port + url;
            } else {
                url = "http*//" + host + "*" + port + url;
            }
        }
        CmdDevice cmdDevice = buildCmdDevice(attDevice, devOptions);
        return admsDevCmdService.addCmdWithTimeout(attDevice.getDevSn(),
            CmdDeviceConstructor.upgradeFirmware(cmdDevice, file, url, null), imme, (long)1800);
    }

    /**
     * 更新快捷键
     */
    public void updateShortcutKey(String sn, AttShortcutKeyInfoItem attShortcutKeyInfoItem) {
        CmdShortcutKeyInfo cmdShortcutKeyInfo = new CmdShortcutKeyInfo();
        ModelUtil.copyProperties(attShortcutKeyInfoItem, cmdShortcutKeyInfo);

        StringBuffer autoTime = new StringBuffer();
        autoTime.append(StringUtils.isNotBlank(attShortcutKeyInfoItem.getSunAutoTime())
            ? attShortcutKeyInfoItem.getSunAutoTime() : "");
        autoTime.append(";").append(StringUtils.isNotBlank(attShortcutKeyInfoItem.getMonAutoTime())
            ? attShortcutKeyInfoItem.getMonAutoTime() : "");
        autoTime.append(";").append(StringUtils.isNotBlank(attShortcutKeyInfoItem.getTueAutoTime())
            ? attShortcutKeyInfoItem.getTueAutoTime() : "");
        autoTime.append(";").append(StringUtils.isNotBlank(attShortcutKeyInfoItem.getWedAutoTime())
            ? attShortcutKeyInfoItem.getWedAutoTime() : "");
        autoTime.append(";").append(StringUtils.isNotBlank(attShortcutKeyInfoItem.getThuAutoTime())
            ? attShortcutKeyInfoItem.getThuAutoTime() : "");
        autoTime.append(";").append(StringUtils.isNotBlank(attShortcutKeyInfoItem.getFriAutoTime())
            ? attShortcutKeyInfoItem.getFriAutoTime() : "");
        autoTime.append(";").append(StringUtils.isNotBlank(attShortcutKeyInfoItem.getSatAutoTime())
            ? attShortcutKeyInfoItem.getSatAutoTime() : "");
        cmdShortcutKeyInfo.setAutoTime(autoTime.toString());

        admsDevCmdService.addCmd(sn, CmdShortcutKeyConstructor.updateShortcutKey(cmdShortcutKeyInfo), false);
    }

    /**
     * 组装设备命令、判断是否支持命令下发
     */
    private CmdDevice buildCmdDevice(AttDeviceItem attDevice, List<AttDeviceOptionItem> devOptions) {
        CmdDevice cmdDevice = new CmdDevice();
        cmdDevice.setFingerFunOn(getOptValue(devOptions, "FingerFunOn"));
        cmdDevice.setFaceFunOn(getOptValue(devOptions, "FaceFunOn"));
        cmdDevice.setPvFunOn(getOptValue(devOptions, "PvFunOn"));
        cmdDevice.setFvFunOn(getOptValue(devOptions, "FvFunOn"));
        cmdDevice.setBioDataFun(getOptValue(devOptions, "BioDataFun"));
        cmdDevice.setBioPhotoFun(getOptValue(devOptions, "BioPhotoFun"));
        cmdDevice.setAttSupportFunList(getOptValue(devOptions, "AttSupportFunList"));
        cmdDevice.setzKFPVersion(attDevice.getFpVersion());
        cmdDevice.setFaceVersion(attDevice.getFaceVersion());
        cmdDevice.setPvVersion(getOptValue(devOptions, "PvVersion"));
        cmdDevice.setSubcontractingUpgradeFunOn(getOptValue(devOptions, "SubcontractingUpgradeFunOn"));
        cmdDevice.setMultiBioDataSupport(getOptValue(devOptions, "MultiBioDataSupport"));
        cmdDevice.setMultiBioPhotoSupport(getOptValue(devOptions, "MultiBioPhotoSupport"));
        cmdDevice.setMultiBioVersion(getOptValue(devOptions, "MultiBioVersion"));
        return cmdDevice;
    }

    /**
     * 组装人员基础信息命令下发实体
     */
    public AttPersonOptItem buildPersonOptItem(PersPersonItem personItem, PersCardItem persCardItem,
        AttPersonItem attPersonItem) {
        AttPersonOptItem attPersonOptItem = new AttPersonOptItem();
        attPersonOptItem.setPersonPin(personItem.getPin());
        attPersonOptItem.setPersonName(StringUtils.defaultString(personItem.getName()));
        attPersonOptItem.setPersonLastName(StringUtils.defaultString(personItem.getLastName()));
        attPersonOptItem.setPersonPwd(StringUtils.defaultString(personItem.getPersonPwd()));
        // 获取卡号(卡号默认空，人员有多个卡号时，取第一个主卡号)
        if (Objects.nonNull(persCardItem) && StringUtils.isNotBlank(persCardItem.getCardNo())) {
            attPersonOptItem.setPersonCardNo(persCardItem.getCardNo());
        }
        // 获取人员设备权限
        if (Objects.nonNull(attPersonItem)) {
            attPersonOptItem.setPersonDevAuth(attPersonItem.getPerDevAuth());
            attPersonOptItem.setVerifyMode(attPersonItem.getVerifyMode());
        } else {
            attPersonOptItem.setPersonDevAuth((short)0);
            attPersonOptItem.setVerifyMode((short)0);
        }
        return attPersonOptItem;
    }

    /**
     * 组装人员头像命令下发实体
     */
    public AttPersonPhotoOptItem buildPersonPhotoOptItem(PersPersonItem personItem) {
        AttPersonPhotoOptItem attPersonPhotoOptItem = new AttPersonPhotoOptItem();
        attPersonPhotoOptItem.setPersonPin(personItem.getPin());
        if (StringUtils.isNotBlank(personItem.getPhotoPath())) {
            String thumbPath = FileUtil.getThumbPath(personItem.getPhotoPath());
            // 去掉开头的斜杠
            attPersonPhotoOptItem.setPersonPhotoUrl(thumbPath.replaceFirst("/", ""));
            // 图片文件解密
            String headPhotoBase64 = FileEncryptUtil.getDecryptFileBase64(thumbPath);
            attPersonPhotoOptItem.setPersonPhotoContent(headPhotoBase64);
        }
        return attPersonPhotoOptItem;
    }

    /**
     * 组装人员对应的生物模板列表
     *
     * @param personBioTempList:
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttBioTemplateItem>
     * <AUTHOR>
     * @throws
     * @date 2025-04-25 10:17
     * @since 1.0.0
     */
    public List<AttBioTemplateItem> buildAttBioTemplateItem(List<PersBioTemplateItem> personBioTempList) {
        List<AttBioTemplateItem> persBioTemplateList = new ArrayList<>();
        if (Objects.nonNull(personBioTempList)) {
            for (PersBioTemplateItem item : personBioTempList) {
                AttBioTemplateItem attBioTemplate = new AttBioTemplateItem();
                // 生物模板拼凑
                attBioTemplate.setPersonPin(item.getPersonPin());
                attBioTemplate.setBioType(item.getBioType());
                attBioTemplate.setTemplateNo(item.getTemplateNo());
                attBioTemplate.setTemplateNoIndex(item.getTemplateNoIndex());
                attBioTemplate.setValidType(item.getValidType());
                attBioTemplate.setTemplate(item.getTemplate());
                attBioTemplate.setVersion(item.getVersion());
                attBioTemplate.setDuress(item.getDuress());
                persBioTemplateList.add(attBioTemplate);
            }
        }
        return persBioTemplateList;
    }

    public List<AttBioPhotoItem> buildAttBioPhotoItem(List<PersBioPhotoItem> persBioPhotoList) {
        List<AttBioPhotoItem> attBioPhotoItemList = new ArrayList<>();
        if (Objects.nonNull(persBioPhotoList)) {
            for (PersBioPhotoItem item : persBioPhotoList) {
                AttBioPhotoItem attBioPhotoItem = new AttBioPhotoItem();
                // 生物模板拼凑
                attBioPhotoItem.setPin(item.getPersonPin());
                attBioPhotoItem.setBioType(item.getBioType());
                attBioPhotoItem.setNo((short)0);
                attBioPhotoItem.setIndex((short)0);
                attBioPhotoItem.setFormat((short)0);
                /*attBioPhotoItem.setContent(item.getPhotoBase64());*/
                attBioPhotoItem.setUrl(item.getPhotoPath());
                /*attBioPhotoItem.setSize(item.getPhotoBase64().length());*/
                attBioPhotoItemList.add(attBioPhotoItem);
            }
        }
        return attBioPhotoItemList;
    }

    /**
     * 下发人脸模板(人脸算法版本7.0)
     */
    private void sendPersonFace7ToDev(String devSn, AttBioTemplateItem attBioTemplate) {
        CmdFaceDataInfo faceDataInfo = new CmdFaceDataInfo(attBioTemplate.getPersonPin(),
            String.valueOf(attBioTemplate.getTemplateNoIndex()), String.valueOf(attBioTemplate.getTemplate().length()),
            String.valueOf(attBioTemplate.getValidType()), attBioTemplate.getTemplate());
        admsDevCmdService.addCmd(devSn, CmdFaceDataConstructor.setFace(faceDataInfo), false);
    }

    /**
     * 下发对比照片（base64数据）
     */
    private void sendBioPhotoToDev(String devSn, String pin) {
        String cropFacePath = FileUtil.getCropFacePath(pin);
        File file = new File(FileUtil.getLocalFullPath(cropFacePath));
        File[] listFiles = file.listFiles();
        if (Objects.nonNull(listFiles)) {
            for (File f : listFiles) {
                if (f.isFile()) {
                    String cropFacePathFile = cropFacePath + "/" + f.getName();
                    // 图片文件解密
                    String base64Image = FileEncryptUtil.getDecryptFileBase64(cropFacePathFile);
                    if (StringUtils.isNotBlank(base64Image)) {
                        CmdBiophotoDataInfo biophotoDataInfo = new CmdBiophotoDataInfo(pin, String.valueOf(2),
                            String.valueOf(base64Image.length()), base64Image);
                        admsDevCmdService.addCmd(devSn, CmdBiophotoDataConstructor.setBioPhoto(biophotoDataInfo),
                            false);
                    }
                }
            }
        }
    }

    /**
     * 下发对比照片（url下发）
     */
    private void sendBioPhotoUrlToDev(String devSn, String pin) {
        String cropFacePath = FileUtil.getCropFacePath(pin);
        File file = new File(FileUtil.getLocalFullPath(cropFacePath));
        File[] listFiles = file.listFiles();
        if (Objects.nonNull(listFiles)) {
            for (File f : listFiles) {
                if (f.isFile()) {
                    String cropFacePathFile = cropFacePath + "/" + f.getName();
                    CmdBiophotoDataInfo biophotoDataInfo =
                        new CmdBiophotoDataInfo(pin, "9", "0", "", "1", cropFacePathFile);
                    admsDevCmdService.addCmd(devSn, CmdBiophotoDataConstructor.setBioPhotoUrl(biophotoDataInfo), false);
                }
            }
        }
    }

    /**
     * 从参数列表中查找指定名称的值
     */
    public String getOptValue(List<AttDeviceOptionItem> devOptionAry, String optName) {
        String ret = "";
        if (Objects.nonNull(devOptionAry)) {
            for (AttDeviceOptionItem devOpt : devOptionAry) {
                if (optName.equals(devOpt.getName())) {
                    ret = devOpt.getValue();
                }
            }
        }

        return ret;
    }

    /**
     * 根据设备版本和指纹模版版本判断是否下发模版，以及选择对应版本的模版
     */
    private void sendFPToDevByVersion(String devSn, String fpVersion, List<AttBioTemplateItem> attBioTemplateList) {

        // 过滤指纹模版、过滤大于设备版本的模版
        Map<String, AttBioTemplateItem> bioTemplateFPMap =
            attBioTemplateList.stream().filter(i -> BaseConstants.BaseBioType.FP_BIO_TYPE.equals(i.getBioType()))
                .filter(i -> fpVersion.compareTo(i.getVersion()) >= 0)
                .collect(Collectors.toMap(i -> i.getPersonPin() + "_" + i.getTemplateNo() + "_" + i.getVersion(),
                    Function.identity(), (x, y) -> y));

        for (Map.Entry<String, AttBioTemplateItem> entry : bioTemplateFPMap.entrySet()) {
            AttBioTemplateItem attBioTemplate = entry.getValue();

            // 判断是否存在更高版本的指纹，高版本优先
            String startKey = attBioTemplate.getPersonPin() + "_" + attBioTemplate.getTemplateNo() + "_";
            String version = attBioTemplate.getVersion();
            boolean haveHighVersion = AttCommonUtils.haveHighVersion(bioTemplateFPMap, startKey, version);

            // 不存在比当前大的指纹版本才下发
            if (!haveHighVersion) {
                if (AttConstant.FP_BIO_VERSION_10.equals(fpVersion)) {
                    send10FPToDev(devSn, attBioTemplate);
                } else {
                    send12FPToDev(devSn, attBioTemplate);
                }
            }
        }
    }

    /**
     * 下发指纹信息（设备指纹版本为10，只允许下发指纹版本为10的模版）
     */
    private void send10FPToDev(String devSn, AttBioTemplateItem attBioTemplate) {
        CmdFingerDataInfo fingerDataInfo = new CmdFingerDataInfo(attBioTemplate.getPersonPin(),
            String.valueOf(attBioTemplate.getTemplateNo()), String.valueOf(attBioTemplate.getTemplate().length()),
            String.valueOf(attBioTemplate.getValidType()), attBioTemplate.getTemplate());
        admsDevCmdService.addCmd(devSn, CmdFingerDataConstructor.setFinger(fingerDataInfo), false);
    }

    /**
     * 下发指纹信息（设备指纹版本为12，允许下发指纹版本为10和12的模版）
     */
    private void send12FPToDev(String devSn, AttBioTemplateItem attBioTemplate) {
        // 获取下发的对象
        String duress = attBioTemplate.getDuress() ? "1" : "0";
        attBioTemplate.setTemplateNoIndex((short)0);// 指纹数据虽然保存1，但是为了方便计算数量，但是下发的时候需要默认下发 0，
        String[] version = StringUtils.split(attBioTemplate.getVersion(), ".");
        String majaorVer = (null != version && version.length > 0) ? version[0] : "0";
        String minorVer = (null != version && version.length > 1) ? version[1] : "0";
        String format = "0";
        CmdBioDataInfo cmdBioDataInfo = new CmdBioDataInfo(attBioTemplate.getPersonPin(),
            String.valueOf(attBioTemplate.getTemplateNo()), String.valueOf(attBioTemplate.getTemplateNoIndex()),
            String.valueOf(attBioTemplate.getValidType()), duress, String.valueOf(attBioTemplate.getBioType()),
            majaorVer, minorVer, format, attBioTemplate.getTemplate());
        admsDevCmdService.addCmd(devSn, CmdBioDataConstructor.setBioData(cmdBioDataInfo), false);
    }

    /**
     * 按位来判断功能支持参数
     */
    private boolean isSupportFunList(List<AttDeviceOptionItem> devOptionAry, String optName, int index) {
        // 参数值
        String optVal = getOptValue(devOptionAry, optName);
        if (StringUtils.isNotBlank(optVal) && optVal.length() > index) {
            return optVal.charAt(index) == '1';
        }
        return false;
    }

    /**
     * 根据参数值转换为布尔值1：支持、0：不支持
     */
    private boolean isSupportFunction(List<AttDeviceOptionItem> devOptionAry, String optName) {
        // 参数值
        String optVal = getOptValue(devOptionAry, optName);
        if (StringUtils.isNotBlank(optVal)) {
            if (CmdAttConstants.SUPPORT.equals(optVal)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取支持多模态生物特征模板参数(标识为1的都支持一体化)
     */
    private boolean isMultiBioDataSupport(List<AttDeviceOptionItem> devOptions, Short bioType) {
        String multiBioDataSupport = getOptValue(devOptions, "MultiBioDataSupport");
        String[] multiBioDataSupportAry = multiBioDataSupport.split(":");
        if (bioType < multiBioDataSupportAry.length) {
            String isSupportBio = multiBioDataSupportAry[bioType];
            if (CmdAttConstants.SUPPORT.equals(isSupportBio)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取支持多模态比对照片参数(标识为1的都支持一体化)
     */
    private boolean isMultiBioPhotoSupport(List<AttDeviceOptionItem> devOptions, Short bioType) {
        String multiBioDataSupport = getOptValue(devOptions, "MultiBioPhotoSupport");
        String[] multiBioDataSupportAry = multiBioDataSupport.split(":");
        if (bioType < multiBioDataSupportAry.length) {
            String isSupportBio = multiBioDataSupportAry[bioType];
            if (CmdAttConstants.SUPPORT.equals(isSupportBio)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取多模态生物特征数据版本
     *
     * @param devOptions:
     * @param bioDataType:
     * @return java.lang.String
     * <AUTHOR>
     * @throws
     * @date 2025-04-08 14:24
     * @since 1.0.0
     */
    private String getMultiBioVersion(List<AttDeviceOptionItem> devOptions, Short bioDataType) {
        String multiBioVersion = getOptValue(devOptions, "MultiBioVersion");
        String[] multiBioVersionAry = multiBioVersion.split(":");
        if (bioDataType > multiBioVersionAry.length) {
            return "0";
        }
        return multiBioVersionAry[bioDataType];
    }

}
