package com.zkteco.zkbiosecurity.att.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 节假日
 */
@Entity
@Table(name = "ATT_HOLIDAY")
@Setter
@Getter
@Accessors(chain = true)
public class AttHoliday extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 节假日编号(此字段暂时不用)
     */
    @Deprecated
    @Column(name = "HOLIDAY_NO", length = 10)
    private String holidayNo;

    /**
     * 节假日名称
     */
    @Column(name = "HOLIDAY_NAME", length = 30)
    private String holidayName;

    /**
     * 开始日期时间
     */
    @Column(name = "START_DATETIME")
    private Date startDatetime;

    /**
     * 结束日期时间
     */
    @Column(name = "END_DATETIME")
    private Date endDatetime;

    /**
     * 持续天数
     */
    @Column(name = "DAY_NUMBER")
    private Short dayNumber;

    /**
     * 是否全部人放假-（false：否/0，true：是/1）
     */
    @Column(name = "IS_ALL_THE_HOLIDAYS")
    private Boolean isAllTheHolidays;

    /**
     * 备注
     */
    @Column(name = "REMARK")
    private String remark;
}