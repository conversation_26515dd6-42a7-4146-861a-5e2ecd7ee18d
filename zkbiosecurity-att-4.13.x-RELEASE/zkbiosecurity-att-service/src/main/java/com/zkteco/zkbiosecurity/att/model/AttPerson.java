package com.zkteco.zkbiosecurity.att.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 人员
 */
@Entity
@Table(name = "ATT_PERSON",
    indexes = {@Index(name = "ATT_PERS_PERSON_ID_IDX", columnList = "PERS_PERSON_ID"),
        @Index(name = "ATT_PERS_PERSON_PIN_IDX", columnList = "PERS_PERSON_PIN"),
        @Index(name = "ATT_PERS_DEPT_ID_IDX", columnList = "AUTH_DEPT_ID"),
        @Index(name = "ATT_PERS_GROUP_ID_IDX", columnList = "GROUP_ID")})
@Setter
@Getter
@Accessors(chain = true)
public class Att<PERSON>erson extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 是否考勤 */
    @Column(name = "IS_ATTENDANCE")
    private Boolean isAttendance;

    /** 人员设备权限 */
    @Column(name = "PER_DEV_AUTH")
    private Short perDevAuth;

    /** 人员id */
    @Column(name = "PERS_PERSON_ID", length = 50)
    private String personId;

    /** 人员编号 */
    @Column(name = "PERS_PERSON_PIN", length = 30)
    private String personPin;

    /** 名字 */
    @Column(name = "PERS_PERSON_NAME", length = 50)
    @Deprecated
    private String personName;

    /** 姓氏 */
    @Column(name = "PERS_PERSON_LASTNAME", length = 50)
    @Deprecated
    private String personLastName;

    /** 部门id */
    @Column(name = "AUTH_DEPT_ID", length = 50)
    @Deprecated
    private String deptId;

    /** 部门编号 */
    @Column(name = "AUTH_DEPT_CODE", length = 100)
    @Deprecated
    private String deptCode;

    /** 部门名称 */
    @Column(name = "AUTH_DEPT_NAME", length = 100)
    @Deprecated
    private String deptName;

    /** 入职时间 */
    @Column(name = "HIRE_DATE")
    @Temporal(TemporalType.DATE)
    @Deprecated
    private Date hireDate;

    /** 分组ID */
    @Column(name = "GROUP_ID", length = 50)
    private String groupId;

    /** 验证方式 */
    @Column(name = "VERIFY_MODE")
    private Short verifyMode;

    /** 工龄 */
    @Column(name = "WORK_LONG")
    private String  workLong;

    /** 年假计算天数 */
    @Column(name = "ANNUAL_LEAVE_DAYS")
    private Integer annualLeaveDays;

    /** 年假调整天数 */
    @Column(name = "ANNUAL_ADJUST_DAYS")
    private Integer annualAdjustDays;

    /** 年假有效开始时间 */
    @Column(name = "ANNUAL_VALID_DATE")
    private Date annualValidDate;

    public AttPerson() {
        super();
    }

    public AttPerson(String personId, String personPin, Short perDevAuth) {
        this.personId = personId;
        this.personPin = personPin;
        this.perDevAuth = perDevAuth;
    }

    public AttPerson(String personId, String personPin, Short perDevAuth, Short verifyMode) {
        this.personId = personId;
        this.personPin = personPin;
        this.perDevAuth = perDevAuth;
        this.verifyMode = verifyMode;
    }
}