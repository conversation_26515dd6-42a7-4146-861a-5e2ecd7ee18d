package com.zkteco.zkbiosecurity.att.processor;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.adms.service.AdmsAuthDeviceService;
import com.zkteco.zkbiosecurity.att.cache.AttCacheManager;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.vo.AttDeviceItem;
import com.zkteco.zkbiosecurity.att.vo.AttSearchDeviceItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;

/**
 * 考勤通信数据处理器
 * 
 * <AUTHOR> href:"mailto:<EMAIL>">max.zheng</a>
 * @version v1.0
 */
@Component
@Order(value = 42)
public class AttCommunicationDataProcessor implements CommandLineRunner {

    @Autowired
    private AttCacheManager attCacheManager;

    @Autowired
    private AttPersonService attPersonService;

    @Autowired
    private AttTransactionService attTransactionService;
    @Autowired
    private AttDeviceService attDeviceService;
    @Autowired
    private AttDeviceOpLogService attDeviceOpLogService;
    @Autowired
    private AttParamService attParamService;
    @Autowired(required = false)
    private AdmsAuthDeviceService admsAuthDeviceService;

    @Override
    public void run(String... strings) throws Exception {
        new AttCommunicationDataHandlerThread("AttCommunicationDataHandlerThread").start();
    }

    /**
     * <AUTHOR>
     */
    class AttCommunicationDataHandlerThread extends Thread {
        Logger log = LoggerFactory.getLogger(getClass());

        public AttCommunicationDataHandlerThread(String name) {
            super(name);
        }

        @Override
        public void run() {
            try {
                // 启动先休眠30s，降低cpu占用，供其它任务执行
                Thread.sleep(30000);
            } catch (InterruptedException e) {
                log.error("AttCommunicationDataHandlerThread start sleep error", e);
            }
            log.info("AttCommunicationDataHandlerThread Start Handler Data......");
            while (true) {
                try {

                    // 处理考勤通信上传数据 max
                    boolean hasData = handlerUserInfo();
                    if (hasData) {
                        Thread.sleep(10);
                        continue;
                    }

                    // 处理考勤一体化生物模板
                    hasData = handlerAttBioData();
                    if (hasData) {
                        Thread.sleep(10);
                        continue;
                    }

                    // 不含有用户数据的时候开始处理考勤设备上传的其它数据，避免考勤设备先上传考勤记录后找不到人员问题
                    hasData = handlerAttLog();
                    if (hasData) {
                        Thread.sleep(500);
                        continue;
                    }

                    // 处理考勤照片数据，
                    hasData = handlerAttPhoto();
                    if (hasData) {
                        Thread.sleep(500);
                        continue;
                    }

                    // 处理考勤设备上传的操作日志 wml.wu
                    hasData = handlerAttOpLog();
                    if (!hasData) {
                        // 都没数据的时候休眠5000
                        Thread.sleep(5000);
                    }

                } catch (Exception e) {
                    log.error("AttCommunicationDataHandlerThread Handler Data Error", e);
                    try {
                        Thread.sleep(5000);
                    } catch (InterruptedException e1) {
                        log.error("AttCommunicationDataHandlerThread start sleep error2", e1);
                    }
                }
            }

        }

        /**
         * 处理考勤记录数据
         */
        private boolean handlerAttLog() {
            Set<String> keyList = attCacheManager.loadAttLogKeyList();
            for (String key : keyList) {
                try {
                    String data = attCacheManager.getLeftFirstValue(key);
                    // 兼容集群部署情况下，多个应用取同个key导致data参数为空
                    if (StringUtils.isBlank(data)){
                        continue;
                    }
                    // 执行考勤设备上传考勤数据
                    attTransactionService.handlerAttLog(key.replaceAll("adms:att:attlog:", ""), data);
                } catch (Exception e) {
                    log.error("AttCommunicationDataProcessor.handlerAttLog() Data Error", e);
                }
            }
            return keyList.size() > 0;
        }

        /**
         * 处理考勤上传上来的用户信息
         * 
         * @return
         */
        private boolean handlerUserInfo() {

            // 处理考勤上传上来的用户信息
            Set<String> keyList = attCacheManager.loadOperLogKeyList();

            String sn;
            for (String key : keyList) {
                sn = key.replaceAll("adms:att:operlog:", "");
                // 判断sn是不是登记机
                if (attDeviceService.checkIsRegDevice(sn)) {
                    try {
                        String data = attCacheManager.getLeftFirstValue(key);
                        // 兼容集群部署情况下，多个应用取同个key导致data参数为空
                        if (StringUtils.isBlank(data)){
                            continue;
                        }
                        // 解析用户数据并保存
                        attPersonService.analysisUserData(sn, data);
                    } catch (Exception e) {
                        log.error("AttCommunicationDataProcessor.handlerUserInfo() Data Error", e);
                    }
                } else {
                    // 不是的话移除无效数据
                    attCacheManager.cleanOperLog(sn);
                }
            }
            return keyList.size() > 0;
        }

        /**
         * 处理考勤照片
         */
        private boolean handlerAttPhoto() {
            Set<String> keyList = attCacheManager.loadAttPhotoKeyList();
            for (String key : keyList) {
                try {
                    String data = attCacheManager.getLeftFirstValue(key);
                    // 兼容集群部署情况下，多个应用取同个key导致data参数为空
                    if (StringUtils.isBlank(data)){
                        continue;
                    }
                    // 执行保存考勤照片
                    attTransactionService.handlerAttPhoto(data);
                } catch (Exception e) {
                    log.error("AttCommunicationDataProcessor.handlerAttPhoto() Data Error", e);
                }
            }

            return keyList.size() > 0;
        }

        /**
         * 处理考勤生物模板一体化数据
         */
        private boolean handlerAttBioData() {
            Set<String> keyList = attCacheManager.loadAttBioDataKeyList();
            String sn;
            for (String key : keyList) {
                sn = key.replaceAll("adms:att:biodata:", "");
                // 判断sn是不是登记机
                if (attDeviceService.checkIsRegDevice(sn)) {
                    try {
                        String data = attCacheManager.getLeftFirstValue(key);
                        // 兼容集群部署情况下，多个应用取同个key导致data参数为空
                        if (StringUtils.isBlank(data)){
                            continue;
                        }
                        // 处理考勤生物模板一体化数据
                        attPersonService.handlerBioData(sn, data);
                    } catch (Exception e) {
                        log.error("AttCommunicationDataProcessor.handlerAttBioData() Data Error", e);
                    }
                } else {
                    // 不是的话移除无效数据
                    attCacheManager.cleanBiodata(sn);
                }
            }
            return keyList.size() > 0;
        }

        /**
         * 处理考勤设备上传的操作日志
         */
        private boolean handlerAttOpLog() {
            Set<String> keyList = attCacheManager.loadOpLogKeyList();
            for (String key : keyList) {
                try {
                    String data = attCacheManager.getLeftFirstValue(key);
                    // 兼容集群部署情况下，多个应用取同个key导致data参数为空
                    if (StringUtils.isBlank(data)){
                        continue;
                    }
                    // 执行考勤设备上传考勤操作日志
                    attDeviceOpLogService.handlerAttDeviceOpLog(key.replaceAll("adms:att:oplog:", ""), data);
                } catch (Exception e) {
                    log.error("AttCommunicationDataProcessor.handlerAttOpLog() Data Error", e);
                }
            }
            return keyList.size() > 0;
        }

        /**
         * 处理考勤自动添加设备
         * 
         * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
         * @date: 2020/6/1 8:35
         * @return: void
         **/
        private void handleAutoAddDevice() {
            ZKResultMsg zkResultMsg;
            String autoAddDevice = attParamService.getAutoAddDevice();
            // 0表示新增设备自动添加
            if ("0".equals(autoAddDevice)) {
                try {
                    // 搜索设备
                    List<AttSearchDeviceItem> attSearchDeviceItems = attDeviceService.searchDeviceList();
                    if (!CollectionUtil.isEmpty(attSearchDeviceItems)) {
                        Map<String, Collection<String>> allIPSn = attDeviceService.getAllIPSn();
                        // 添加过设备的sn
                        Collection<String> sns = allIPSn.get("sn");
                        if (!CollectionUtil.isEmpty(sns)) {
                            // 需要过滤掉添加过的设备
                            attSearchDeviceItems = attSearchDeviceItems.stream()
                                .filter(item -> !sns.contains(item.getSn())).collect(Collectors.toList());
                        }
                        if (!CollectionUtil.isEmpty(attSearchDeviceItems)) {
                            for (AttSearchDeviceItem attSearchDeviceItem : attSearchDeviceItems) {
                                AttDeviceItem attDeviceItem = new AttDeviceItem();
                                ModelUtil.copyProperties(attSearchDeviceItem, attDeviceItem);
                                attDeviceItem.setDevSn(attSearchDeviceItem.getSn());
                                attDeviceItem.setIpAddress(attSearchDeviceItem.getIp());
                                // 首次添加 以下参数给默认值
                                attDeviceItem.setStatus(true);
                                attDeviceItem.setDevName(attSearchDeviceItem.getSn());
                                // attDeviceItem.setCommPort(AttDeviceConstant.ATT_COMM_PORT);
                                // attDeviceItem.setUpdateFlag(AttDeviceConstant.ATT_NON_REGISTERED);
                                // attDeviceItem.setDataDownFlag(AttDeviceConstant.ATT_DOWN_FLAG);
                                // attDeviceItem.setTransInterval(AttDeviceConstant.ATT_TRANS_INTERVAL);
                                // attDeviceItem.setTransTimes(AttDeviceConstant.ATT_TRANS_TIMES);
                                // attDeviceItem.setRealTime(AttDeviceConstant.ATT_REAL_TIME);
                                // attDeviceItem.setCmdCount(AttDeviceConstant.ATT_CMD_COUNT);
                                // attDeviceItem.setSearchInterval(AttDeviceConstant.ATT_SEARCH_INTERVAL);
                                // attDeviceItem.setEncrypt(String.valueOf(AttDeviceConstant.ATT_IS_ENCRYPT));
                                // 默认北京时间东八区
                                attDeviceItem.setTimeZone("+0800");
                                // 是否登记机
                                attDeviceItem.setIsRegDevice(false);
                                // 新增设备自动添加区域为空
                                attDeviceItem.setAreaId(null);
                                zkResultMsg = attDeviceService.authDevice(attDeviceItem);
                                if ("fail".equals(zkResultMsg.getRet())) {
                                    log.info("Att Device auto add fail ,sn is" + attDeviceItem.getDevSn()
                                        + ",fail msg is" + zkResultMsg.getMsg());
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("AttCommunicationDataProcessor handleAutoAddDevice error", e);
                }
            }
        }
    }
}
