/**
 * File Name: AttPersonSch Created by GenerationTools on 2018-02-08 下午08:27 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.att.dao;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import com.zkteco.zkbiosecurity.att.model.AttPersonSch;

/**
 * 对应百傲瑞达 AttPersonSchDao
 * 
 * <AUTHOR>
 * @date: 2018-02-08 下午08:27
 * @version v1.0
 */
public interface AttPersonSchDao extends BaseDao<AttPersonSch, String> {

    void deleteByScheduleId(String scheduleId);

    AttPersonSch findByScheduleIdAndScheduleType(String scheduleId, Short scheduleType);

}