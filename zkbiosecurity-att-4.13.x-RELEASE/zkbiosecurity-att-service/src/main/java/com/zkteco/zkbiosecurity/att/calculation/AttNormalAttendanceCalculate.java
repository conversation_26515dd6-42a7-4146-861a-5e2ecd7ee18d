package com.zkteco.zkbiosecurity.att.calculation;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import com.zkteco.zkbiosecurity.att.dao.AttTransactionDao;
import com.zkteco.zkbiosecurity.att.model.AttTransaction;
import com.zkteco.zkbiosecurity.att.service.AttTransactionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.bean.AttRuleParamBean;
import com.zkteco.zkbiosecurity.att.calc.bo.AttLeaveBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttOvertimeBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttTimeSlotBO;
import com.zkteco.zkbiosecurity.att.constants.AppConstant;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.constants.AttShiftConstant;
import com.zkteco.zkbiosecurity.att.enums.AttRuleEnum;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttBreakTimeItem;
import com.zkteco.zkbiosecurity.att.vo.AttRecordItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;

/**
 * 规律班次考勤计算
 *
 * <AUTHOR>
 * @date 2020-07-01 11:08
 * @sine 1.0.0
 */
@Component
@Slf4j
public class AttNormalAttendanceCalculate {

    @Autowired
    private AttOvertimeCalculate attOvertimeCalculate;
    @Autowired
    private AttTransactionDao attTransactionDao;
    @Autowired
    private AttTransactionService attTransactionService;

    /**
     * 规律班次(正常考勤)
     *
     * @param attPersonSchBO
     */
    public void analyseRegularShift(AttPersonSchBO attPersonSchBO, List<AttLeaveBO> attLeaveBOList,
                                    List<AttOvertimeBO> attOvertimeBOList, Map<String, AttTimeSlotItem> attTimeSlotItemMap,
                                    AttRuleParamBean attRuleParamBean, AttRecordItem attRecordItem) {
        // 考勤方式（0/按班次正常刷卡，1/一天内任刷一次有效卡，2/只计算刷卡时间，3/免刷卡）
        Short attendanceMode = attPersonSchBO.getAttendanceMode();
        switch (attendanceMode) {
            // 按班次正常刷卡(正常考勤)
            case AttShiftConstant.AttendanceMode.SHIFT_NORMAL:
                fillShiftNormalData(attPersonSchBO, attLeaveBOList, attOvertimeBOList, attTimeSlotItemMap,
                        attRuleParamBean, attRecordItem);
                break;
            // 一天内任刷一次有效卡(没有打卡则算旷工,任一一次有效打卡则算满勤,计异常申请时长,不算提前或延后的加班时长)
            case AttShiftConstant.AttendanceMode.ONE_DAY_ONE_CARD:
                fillOnceValidCardData(attPersonSchBO, attLeaveBOList, attTimeSlotItemMap, attRuleParamBean,
                        attRecordItem);
                break;
            // 只计算刷卡时间(只算打卡时间统计出来的时长,不计异常申请时长,不算提前或延后的加班时长)<目前没有开发该模式>
            case AttShiftConstant.AttendanceMode.ONLY_BRUSH_TIME:
                fillOnlyCalculateCardData(attPersonSchBO, attTimeSlotItemMap, attRuleParamBean, attRecordItem);
                break;
            // 免刷卡(直接算满勤,不计异常申请时长,不算提前或延后的加班时长)
            case AttShiftConstant.AttendanceMode.NOT_BRUSH_CARD:
                fillFreeCardData(attPersonSchBO, attTimeSlotItemMap, attRuleParamBean, attRecordItem);
                break;
            default:
                break;
        }
    }

    /**
     * 规律班次(免打卡)，填充考勤数据
     */
    public void fillFreeCardData(AttPersonSchBO attPersonSchBO, Map<String, AttTimeSlotItem> attTimeSlotItemMap,
                                 AttRuleParamBean attRuleParamBean, AttRecordItem attRecordItem) {

        String noSignIn = attRuleParamBean.getNoSignIn();
        String noSignOff = attRuleParamBean.getNoSignOff();

        // 班次对应时间段名称
        List<String> timeSlotNameList = new ArrayList<>();
        // 班次时间数据
        List<String> shiftTimeDataList = new ArrayList<>();
        // 有效打卡数据
        List<String> cardValidDataList = new ArrayList<>();

        // 有效打卡次数
        int cardValidCount = 0;
        // 应出勤分钟数
        int shouldMinuteTotal = 0;

        List<String> cardStatusList = new ArrayList<>();
        String firstDay = null;
        String secondDay = null;

        // 遍历打卡数据，组装班次时间数据、有效打卡数据和计算应出勤分钟数
        for (AttTimeSlotBO attTimeSlotBO : attPersonSchBO.getAttTimeSlotArray()) {
            String attTimeSlotId = attTimeSlotBO.getAttTimeSlotId();
            AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotId);
            // 调整规律班次免考勤的上下班时间取开始签到时间和结束签退时间为上下班时间 by ljf 2019/7/8
            shiftTimeDataList
                    .add(String.format("%s-%s", attTimeSlotItem.getToWorkTime(), attTimeSlotItem.getOffWorkTime()));

            attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);
            attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);
            if (Objects.isNull(firstDay)
                    || (AttCalculationConstant.IsInterDay.THE_INTER_DAY.equals(attTimeSlotBO.getIsInterDay()))) {
                firstDay = attTimeSlotBO.getFirstDay();
                secondDay = attTimeSlotBO.getSecondDay();
            }

            timeSlotNameList.add(attTimeSlotItem.getPeriodName());
            String actualToWorkTime = attTimeSlotBO.getToWorkTime();
            String actualOffWorkTime = attTimeSlotBO.getOffWorkTime();
            String validToWorkTime = actualToWorkTime;
            if (!noSignIn.equals(actualToWorkTime)) {
                validToWorkTime = AttDateUtils.stringToHHmm(actualToWorkTime);
                cardValidCount++;
            }
            String validOffWorkTime = actualOffWorkTime;
            if (!noSignOff.equals(actualOffWorkTime)) {
                validOffWorkTime = AttDateUtils.stringToHHmm(actualOffWorkTime);
                cardValidCount++;
            }
            cardValidDataList.add(String.format("%s-%s", validToWorkTime, validOffWorkTime));
            cardStatusList
                    .add(String.format("%s-%s", attTimeSlotBO.getToWorkTimeStatus(), attTimeSlotBO.getOffWorkTimeStatus()));

            shouldMinuteTotal += (int) attTimeSlotItem.getWorkingHours();
        }

        attRecordItem.setShiftName(attPersonSchBO.getAttShiftName());
        // 时间段名称集合，多个以逗号隔开
        attRecordItem.setTimeSlotName(StringUtils.join(timeSlotNameList, ","));
        attRecordItem.setShiftTimeData(StringUtils.join(shiftTimeDataList, ";"));
        if (!CollectionUtil.isEmpty(cardValidDataList) && cardValidDataList.size() > 19) {
            attRecordItem.setCardValidData(StringUtils.join(cardValidDataList.subList(0, 19), ";") + "...");
        } else {
            attRecordItem.setCardValidData(StringUtils.join(cardValidDataList, ";"));
        }
        attRecordItem.setCardValidCount(cardValidCount);

        attRecordItem.setCardStatus(StringUtils.join(cardStatusList, ";"));
        attRecordItem.setCrossDay(String.format("%s_%s", firstDay, secondDay));

        // 应出勤
        attRecordItem.setShouldMinute(shouldMinuteTotal);
        attRecordItem.setShouldDays(BigDecimal.ONE);

        // 实际出勤
        attRecordItem.setActualMinute(shouldMinuteTotal);
        attRecordItem.setActualDays(BigDecimal.ONE);

        // 有效出勤
        attRecordItem.setValidMinute(shouldMinuteTotal);
        attRecordItem.setValidDays(BigDecimal.ONE);

        // 设置考勤状态 免打卡 直接设置实到
        attRecordItem.setAttendanceStatus(AttCalculationConstant.AttAttendStatus.ACTUAL);
    }

    /**
     * 按班次正常刷卡
     */
    private void fillShiftNormalData(AttPersonSchBO attPersonSchBO, List<AttLeaveBO> attLeaveBOList,
                                     List<AttOvertimeBO> attOvertimeBOList, Map<String, AttTimeSlotItem> attTimeSlotItemMap,
                                     AttRuleParamBean attRuleParamBean, AttRecordItem attRecordItem) {

        // 填充异常信息
        fillExceptionData(attPersonSchBO, attLeaveBOList, attTimeSlotItemMap);

        // 迟到信息
        fillLateData(attRecordItem.getPersonPin(), attPersonSchBO, attTimeSlotItemMap, attRuleParamBean);

        // 早退信息
        fillEarlyData(attPersonSchBO, attTimeSlotItemMap, attRuleParamBean);

        // 旷工信息
        fillAbsentData(attPersonSchBO, attTimeSlotItemMap, attRuleParamBean);

        // 加班信息
        attOvertimeCalculate.fillOvertimeData(attPersonSchBO, attOvertimeBOList, attTimeSlotItemMap, attRuleParamBean);

        // 出勤信息(汇总数据)
        fillSummaryData(attPersonSchBO, attTimeSlotItemMap, attRecordItem, attRuleParamBean);

        // 填充考勤状态结果
        fillAttStatus(attRecordItem, attRuleParamBean, attPersonSchBO, attTimeSlotItemMap);
    }

    /**
     * 一天内任刷一次有效卡
     */
    private void fillOnceValidCardData(AttPersonSchBO attPersonSchBO, List<AttLeaveBO> attLeaveBOList,
                                       Map<String, AttTimeSlotItem> attTimeSlotItemMap, AttRuleParamBean attRuleParamBean,
                                       AttRecordItem attRecordItem) {

        // 填充异常信息
        fillExceptionData(attPersonSchBO, attLeaveBOList, attTimeSlotItemMap);

        String noSignIn = attRuleParamBean.getNoSignIn();
        String noSignOff = attRuleParamBean.getNoSignOff();

        // 班次对应时间段名称
        List<String> timeSlotNameList = new ArrayList<>();
        // 班次时间数据
        List<String> shiftTimeDataList = new ArrayList<>();
        // 有效打卡数据
        List<String> cardValidDataList = new ArrayList<>();

        // 有效打卡次数
        int cardValidCount = 0;
        // 应出勤分钟数
        int shouldMinuteTotal = 0;
        // 实际出勤分钟数
        int actualMinuteTotal = 0;

        // 是否出勤
        boolean hasSign = false;

        // 请假分钟数
        int leaveMinute = 0;
        // 请假是否扣上班时长
        int leaveDeductWorkLong = 0;
        // 外出分钟数
        int outMinute = 0;
        // 出差分钟数
        int tripMinute = 0;
        Map<String, Integer> exceptionTimeLongMap = new HashMap<>();

        List<String> cardStatusList = new ArrayList<>();
        String firstDay = null;
        String secondDay = null;

        for (AttTimeSlotBO attTimeSlotBO : attPersonSchBO.getAttTimeSlotArray()) {
            String attTimeSlotId = attTimeSlotBO.getAttTimeSlotId();
            AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotId);
            String shiftTimeDataKey =
                    String.format("%s-%s", attTimeSlotItem.getToWorkTime(), attTimeSlotItem.getOffWorkTime());

            attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);
            attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);
            if (Objects.isNull(firstDay)
                    || (AttCalculationConstant.IsInterDay.THE_INTER_DAY.equals(attTimeSlotBO.getIsInterDay()))) {
                firstDay = attTimeSlotBO.getFirstDay();
                secondDay = attTimeSlotBO.getSecondDay();
            }

            shiftTimeDataList.add(shiftTimeDataKey);
            timeSlotNameList.add(attTimeSlotItem.getPeriodName());
            String actualToWorkTime = attTimeSlotBO.getToWorkTime();
            String actualOffWorkTime = attTimeSlotBO.getOffWorkTime();
            String validToWorkTime = actualToWorkTime;
            if (!noSignIn.equals(actualToWorkTime)) {
                validToWorkTime = AttDateUtils.stringToHHmm(actualToWorkTime);
                cardValidCount++;
            }
            String validOffWorkTime = actualOffWorkTime;
            if (!noSignOff.equals(actualOffWorkTime)) {
                validOffWorkTime = AttDateUtils.stringToHHmm(actualOffWorkTime);
                cardValidCount++;
            }
            cardValidDataList.add(String.format("%s-%s", validToWorkTime, validOffWorkTime));
            shouldMinuteTotal += (int) attTimeSlotItem.getWorkingHours();

            // 若是上下班有打开记录 判断sign是否出勤设置为true
            if (!noSignIn.equals(actualToWorkTime) || !noSignOff.equals(actualOffWorkTime)) {
                hasSign = true;
            }
            cardStatusList
                    .add(String.format("%s-%s", attTimeSlotBO.getToWorkTimeStatus(), attTimeSlotBO.getOffWorkTimeStatus()));
            // 直接累加异常分钟
            leaveMinute += attTimeSlotBO.getLeaveMinute();
            leaveDeductWorkLong += attTimeSlotBO.getLeaveDeductWorkLong();
            outMinute += attTimeSlotBO.getOutMinute();
            tripMinute += attTimeSlotBO.getTripMinute();
            // 汇总时段的异常类型时长
            if (!CollectionUtil.isEmpty(attTimeSlotBO.getExceptionTimeLongMap())) {
                for (Map.Entry<String, Integer> entry : attTimeSlotBO.getExceptionTimeLongMap().entrySet()) {
                    int typeTimeLong = entry.getValue();
                    if (exceptionTimeLongMap.containsKey(entry.getKey())) {
                        typeTimeLong += exceptionTimeLongMap.get(entry.getKey());
                    }
                    exceptionTimeLongMap.put(entry.getKey(), typeTimeLong);
                }
            }
        }

        // 时间段名称集合，多个以逗号隔开
        attRecordItem.setShiftName(attPersonSchBO.getAttShiftName());
        attRecordItem.setTimeSlotName(StringUtils.join(timeSlotNameList, ","));
        attRecordItem.setShiftTimeData(StringUtils.join(shiftTimeDataList, ";"));
        if (!CollectionUtil.isEmpty(cardValidDataList) && cardValidDataList.size() > 19) {
            attRecordItem.setCardValidData(StringUtils.join(cardValidDataList.subList(0, 19), ";") + "...");
        } else {
            attRecordItem.setCardValidData(StringUtils.join(cardValidDataList, ";"));
        }
        attRecordItem.setCardValidCount(cardValidCount);

        // 有效时长： 有签到或签退则满勤，无则判断异常是否扣请假时长，不扣则算有效时长
        int validMinuteTotal =
                hasSign ? shouldMinuteTotal : (leaveMinute + outMinute + tripMinute - leaveDeductWorkLong);
        validMinuteTotal =
                (validMinuteTotal < 0) ? 0 : (validMinuteTotal > shouldMinuteTotal) ? shouldMinuteTotal : validMinuteTotal;

        // 应出勤
        attRecordItem.setShouldMinute(shouldMinuteTotal);
        // attRecordItem.setShouldDays(BigDecimal.ONE);

        // 实际出勤
        attRecordItem.setActualMinute(hasSign ? shouldMinuteTotal : actualMinuteTotal);
        // attRecordItem.setActualDays(hasSign ? BigDecimal.ONE
        // : new BigDecimal(convertDay(actualMinuteTotal, shouldMinuteTotal, attRuleParamBean)));

        // 有效
        attRecordItem.setValidMinute(validMinuteTotal);
        // attRecordItem.setValidDays(new BigDecimal(convertDay(validMinuteTotal, shouldMinuteTotal,
        // attRuleParamBean)));

        attRecordItem.setCardStatus(StringUtils.join(cardStatusList, ";"));
        attRecordItem.setCrossDay(String.format("%s_%s", firstDay, secondDay));

        // 迟到
        attRecordItem.setLateCountData("");
        attRecordItem.setLateMinuteData("");
        attRecordItem.setLateCountTotal(0);
        attRecordItem.setLateMinuteTotal(0);

        // 早退
        attRecordItem.setEarlyCountData("");
        attRecordItem.setEarlyMinuteData("");
        attRecordItem.setEarlyCountTotal(0);
        attRecordItem.setEarlyMinuteTotal(0);

        // 加班
        attRecordItem.setOvertimeUsualMinute(0);
        attRecordItem.setOvertimeRestMinute(0);
        attRecordItem.setOvertimeHolidayMinute(0);
        attRecordItem.setOvertimeMinute(0);

        // 异常
        attRecordItem.setLeaveMinute(leaveMinute);
        // attRecordItem.setLeaveDays(new BigDecimal(convertDay(leaveMinute, shouldMinuteTotal, attRuleParamBean)));
        attRecordItem.setOutMinute(outMinute);
        // attRecordItem.setOutDays(new BigDecimal(convertDay(outMinute, shouldMinuteTotal, attRuleParamBean)));
        attRecordItem.setTripMinute(tripMinute);
        // attRecordItem.setTripDays(new BigDecimal(convertDay(tripMinute, shouldMinuteTotal, attRuleParamBean)));

        // 保存各异常类型对应时长，格式:code-时长(分钟)，多个以逗号隔开
        StringBuffer leaveDetails = new StringBuffer();
        for (Map.Entry<String, Integer> entry : exceptionTimeLongMap.entrySet()) {
            leaveDetails.append(entry.getKey() + "-" + entry.getValue() + ",");
        }
        if (leaveDetails.length() > 0) {
            leaveDetails.deleteCharAt(leaveDetails.length() - 1);
        }
        attRecordItem.setLeaveDetails(leaveDetails.toString());

        // 旷工（没有打卡的情况下，按照旷工计算）
        int absentMinute = hasSign ? 0 : (shouldMinuteTotal - leaveMinute - outMinute - tripMinute);
        absentMinute = absentMinute < 0 ? 0 : absentMinute;
        attRecordItem.setAbsentMinute(absentMinute);
        // attRecordItem.setAbsentDays(absentMinute > 0 ? BigDecimal.ONE : BigDecimal.ZERO);

        // 设置考勤状态 一天内任刷一次有效卡
        String attendanceStatus = "";

        // 请假状态改为具体请假类别 by ljf 2020/8/7
        if (leaveMinute > 0) {
            if (StringUtils.isNotBlank(leaveDetails)) {
                String[] leaveNoAndTimeLongArr = leaveDetails.toString().split(AttConstant.COMMA);
                if (null != leaveNoAndTimeLongArr) {
                    for (String item : leaveNoAndTimeLongArr) {
                        String[] leaveNoAndTimeLong = item.split("-", -1);
                        String leaveNo = leaveNoAndTimeLong[0];
                        try {
                            Integer timeLong = Integer.valueOf(leaveNoAndTimeLong[1]);
                            if (timeLong > 0) {
                                attendanceStatus += leaveNo + ",";
                            }
                        } catch (Exception e) {
                        }
                    }
                }
            }
            // 除了具体假种,有请假还是放请假状态,方便过滤查询
            attendanceStatus += AttCalculationConstant.AttAttendStatus.LEAVE + ",";
        }

        if (outMinute > 0) {
            attendanceStatus += AttCalculationConstant.AttAttendStatus.OUT + ",";
        }
        if (tripMinute > 0) {
            attendanceStatus += AttCalculationConstant.AttAttendStatus.TRIP + ",";
        }
        if (absentMinute > 0) {
            attendanceStatus += AttCalculationConstant.AttAttendStatus.ABSENT + ",";
        }

        // 实到/应到
        if (StringUtils.isBlank(attendanceStatus)) {
            attendanceStatus = AttCalculationConstant.AttAttendStatus.ACTUAL + ",";
        }

        attendanceStatus = StrUtil.removeLastChar(attendanceStatus);
        attRecordItem.setAttendanceStatus(attendanceStatus);
    }

    /**
     * 只计算刷卡时间
     * <p>
     * 因为考勤方式选择已去掉，此方法已不用
     * </p>
     */
    @Deprecated
    private void fillOnlyCalculateCardData(AttPersonSchBO attPersonSchBO,
                                           Map<String, AttTimeSlotItem> attTimeSlotItemMap, AttRuleParamBean attRuleParamBean,
                                           AttRecordItem attRecordItem) {

        String noSignIn = attRuleParamBean.getNoSignIn();
        String noSignOff = attRuleParamBean.getNoSignOff();

        // 班次时间数据
        List<String> shiftTimeDataList = new ArrayList<>();
        // 有效打卡数据
        List<String> cardValidDataList = new ArrayList<>();

        // 有效打卡次数
        int cardValidCount = 0;
        // 应出勤分钟数
        int shouldMinuteTotal = 0;
        // 实际出勤分钟数
        int actualMinuteTotal = 0;
        boolean hasNoSignIn = false;
        boolean hasNoSignOff = false;

        for (AttTimeSlotBO attTimeSlotBO : attPersonSchBO.getAttTimeSlotArray()) {

            String attTimeSlotId = attTimeSlotBO.getAttTimeSlotId();
            AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotId);
            shiftTimeDataList
                    .add(String.format("%s-%s", attTimeSlotItem.getToWorkTime(), attTimeSlotItem.getOffWorkTime()));

            String actualToWorkTime = attTimeSlotBO.getToWorkTime();
            String actualOffWorkTime = attTimeSlotBO.getOffWorkTime();
            String validToWorkTime = actualToWorkTime;
            if (!noSignIn.equals(actualToWorkTime)) {
                validToWorkTime = AttDateUtils.stringToHHmm(actualToWorkTime);
                cardValidCount++;
            }
            String validOffWorkTime = actualOffWorkTime;
            if (!noSignOff.equals(actualOffWorkTime)) {
                validOffWorkTime = AttDateUtils.stringToHHmm(actualOffWorkTime);
                cardValidCount++;
            }
            cardValidDataList.add(String.format("%s-%s", validToWorkTime, validOffWorkTime));
            shouldMinuteTotal += (int) attTimeSlotItem.getWorkingHours();

            if (!noSignIn.equals(actualToWorkTime) && !noSignOff.equals(actualOffWorkTime)) {
                actualMinuteTotal += AttDateUtils.getMinuteDiff(actualToWorkTime, actualOffWorkTime);
            }

            if (noSignIn.equals(actualToWorkTime)) {
                hasNoSignIn = true;
            }

            if (noSignOff.equals(actualOffWorkTime)) {
                hasNoSignOff = true;
            }
        }

        attRecordItem.setShiftName(attPersonSchBO.getAttShiftName());
        attRecordItem.setShiftTimeData(StringUtils.join(shiftTimeDataList, ";"));
        if (!CollectionUtil.isEmpty(cardValidDataList) && cardValidDataList.size() > 19) {
            attRecordItem.setCardValidData(StringUtils.join(cardValidDataList.subList(0, 19), ";") + "...");
        } else {
            attRecordItem.setCardValidData(StringUtils.join(cardValidDataList, ";"));
        }
        attRecordItem.setCardValidCount(cardValidCount);

        // 应出勤
        attRecordItem.setShouldMinute(shouldMinuteTotal);
        // attRecordItem.setShouldDays(BigDecimal.ONE);

        // 实际出勤
        attRecordItem.setActualMinute(actualMinuteTotal);
        // attRecordItem.setActualDays(new BigDecimal(convertDay(actualMinuteTotal, shouldMinuteTotal,
        // attRuleParamBean)));

        // 有效出勤
        int validMinuteTotal = actualMinuteTotal;
        attRecordItem.setValidMinute(validMinuteTotal);
        // attRecordItem.setValidDays(new BigDecimal(convertDay(validMinuteTotal, shouldMinuteTotal,
        // attRuleParamBean)));

        // 设置考勤状态
        String attendanceStatus = "";
        if (!hasNoSignOff && !hasNoSignIn) {
            attendanceStatus = AttCalculationConstant.AttAttendStatus.ACTUAL;
            attRecordItem.setExceptionSchType(Integer.valueOf(AttCalculationConstant.AttExceptionSchType.ARRIVE));
        } else {
            if (hasNoSignOff) {
                attendanceStatus += AttCalculationConstant.AttAttendStatus.NO_CHECK_OUT + ",";
                attRecordItem
                        .setExceptionSchType(Integer.valueOf(AttCalculationConstant.AttExceptionSchType.NO_SIGN_OFF));
            }
            if (hasNoSignIn) {
                attendanceStatus += AttCalculationConstant.AttAttendStatus.NO_CHECK_IN + ",";
                attRecordItem
                        .setExceptionSchType(Integer.valueOf(AttCalculationConstant.AttExceptionSchType.NO_SIGN_IN));
            }
            attendanceStatus = StrUtil.removeLastChar(attendanceStatus);
        }
        attRecordItem.setAttendanceStatus(attendanceStatus);
    }

    /**
     * 填充异常信息（计算请假时长，取交集）
     *
     * @param attPersonSchBO
     * @param attLeaveBOList
     * @param attTimeSlotItemMap
     */
    private void fillExceptionData(AttPersonSchBO attPersonSchBO, List<AttLeaveBO> attLeaveBOList,
                                   Map<String, AttTimeSlotItem> attTimeSlotItemMap) {

        List<AttTimeSlotBO> attTimeSlotBOList = attPersonSchBO.getAttTimeSlotArray();

        // 没有异常申请，则填充0 返回
        if (CollectionUtil.isEmpty(attLeaveBOList)) {
            return;
        }

        // 异常类型和时长集合
        Map<String, Integer> exceptionTimeLongMap = null;
        // 异常集合（格式：开始-结束）
        List<String> exceptionTimeList = null;
        // 有异常申请则计算有效申请时长
        for (AttTimeSlotBO attTimeSlotBO : attTimeSlotBOList) {

            AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());
            String timeSlotToWorkTime =
                    String.format("%s %s:00", attTimeSlotBO.getFirstDay(), attTimeSlotItem.getToWorkTime());
            String timeSlotOffWorkTime =
                    String.format("%s %s:00", attTimeSlotBO.getSecondDay(), attTimeSlotItem.getOffWorkTime());
            // 该时间有提前或延后上班，对应的上下班范围要相应的提前或延后
            if (ConstUtil.SYSTEM_COMMON_TRUE.equals(attTimeSlotItem.getEnableFlexibleWork())
                    && null != attTimeSlotBO.getFlexibleTime()) {
                timeSlotToWorkTime = AttDateUtils.addMinute(timeSlotToWorkTime, -attTimeSlotBO.getFlexibleTime());
                timeSlotOffWorkTime = AttDateUtils.addMinute(timeSlotOffWorkTime, -attTimeSlotBO.getFlexibleTime());
            }

            int leaveMinute = 0;
            int leaveDeductWorkLong = 0;
            int tripMinute = 0;
            int outMinute = 0;
            exceptionTimeList = new ArrayList<>();
            exceptionTimeLongMap = new HashMap<>();

            for (AttLeaveBO attLeaveBO : attLeaveBOList) {

                String leaveStartTime = attLeaveBO.getStartDateTime();
                String leaveEndTime = attLeaveBO.getEndDateTime();

                // 判断时间段和异常申请是否有交集，没有则跳过
                if (StringUtils.compare(leaveEndTime, timeSlotToWorkTime) < 0
                        || StringUtils.compare(leaveStartTime, timeSlotOffWorkTime) > 0) {
                    continue;
                }

                int timeLong =
                        getTimeSlotIntersectionTime(leaveStartTime, leaveEndTime, attTimeSlotBO, attTimeSlotItem);

                switch (attLeaveBO.getLeaveTypeNo()) {
                    // 出差
                    case AttCalculationConstant.AttAttendStatus.TRIP:
                        tripMinute += timeLong;
                        break;
                    // 外出
                    case AttCalculationConstant.AttAttendStatus.OUT:
                        outMinute += timeLong;
                        break;
                    // 默认就当是其他请假申请
                    default:
                        leaveMinute += timeLong;
                        break;
                }

                // 考勤规则修改出差外出也可以设置是否扣上班时长,所以每种异常都判断是否扣时长 by ljf 2020/8/7
                if (attLeaveBO.getIsDeductWorkLong()) {
                    leaveDeductWorkLong += timeLong;
                }

                // 记录异常记录
                if (timeLong > 0) {
                    exceptionTimeList.add(leaveStartTime + "=" + leaveEndTime);
                    // 设置异常类型和累计时长
                    Integer typeTimeLong = exceptionTimeLongMap.get(attLeaveBO.getLeaveTypeNo());
                    typeTimeLong = (null == typeTimeLong ? 0 : typeTimeLong) + timeLong;
                    exceptionTimeLongMap.put(attLeaveBO.getLeaveTypeNo(), typeTimeLong);
                }

            }

            attTimeSlotBO.setLeaveMinute(leaveMinute);
            attTimeSlotBO.setLeaveDeductWorkLong(leaveDeductWorkLong);
            attTimeSlotBO.setTripMinute(tripMinute);
            attTimeSlotBO.setOutMinute(outMinute);
            attTimeSlotBO.setExceptionTime(StringUtils.join(exceptionTimeList, ","));
            attTimeSlotBO.setExceptionTimeLongMap(exceptionTimeLongMap);

        }
    }

    /**
     * 迟到信息
     *
     * @param attPersonSchBO
     */
    private void fillLateData(String pin, AttPersonSchBO attPersonSchBO, Map<String, AttTimeSlotItem> attTimeSlotItemMap,
                              AttRuleParamBean attRuleParamBean) {
        List<AttTimeSlotBO> attTimeSlotBOList = attPersonSchBO.getAttTimeSlotArray();
        for (AttTimeSlotBO attTimeSlotBO : attTimeSlotBOList) {

            attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);

            AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());
            // 非必须签到即无迟到,直接置0
            if (!attTimeSlotItem.getIsMustSignIn()) {
                attTimeSlotBO.setLateMinute(0);
                attTimeSlotBO.setLateCount(0);
                continue;
            }

            int lateCount = 0;
            int lateMinute = 0;
            String timeSlotToWorkTime =
                    String.format("%s %s:00", attTimeSlotBO.getFirstDay(), attTimeSlotItem.getToWorkTime());
            String toWorkTime = attTimeSlotBO.getToWorkTime();
            String exceptionTime = attTimeSlotBO.getExceptionTime();
            String exceptionStartTime = null;
            String exceptionEndTime = null;
            if (StringUtils.isNotBlank(exceptionTime)) {
                String[] exceptionTimeArr = exceptionTime.split(",");
                List<String[]> list = new ArrayList<>();
                for (String time : exceptionTimeArr) {
                    String[] split = time.split("=");
                    list.add(new String[]{split[0], split[1]});
                }
                Collections.sort(list, ((o1, o2) -> StringUtils.compare(o1[0], o2[0])));
                // 异常的最早开始时间
                exceptionStartTime = list.get(0)[0];
                exceptionEndTime = list.get(0)[1];
            }

            // 未签到（无上班卡点）
            if (attRuleParamBean.getNoSignIn().equals(toWorkTime)) {

                attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);

                // 无异常申请
                if (StringUtils.isBlank(exceptionTime)) {
                    // 判断是否设置未签到为迟到,是则迟到；如果未签到为不完整或不完整，则无迟到
                    String noSignInAsType = attRuleParamBean.getNoSignInAsType();
                    if (AttConstant.ATT_NOSIGNIN_COUNTTYPE_LATE.equals(noSignInAsType)) {
                        lateCount = 1;
                        lateMinute = Integer.parseInt(attRuleParamBean.getNoSignInAsLateMinute());
                    } else {
                        lateCount = 0;
                        lateMinute = 0;
                    }
                } else {
                    // 有异常申请
                    // 要判断异常开始<=上班时间(异常已默认跟时段有交集)，是则无迟到，否则是否设置未签到为迟到；如果未签到为不完整或缺勤，则无迟到
                    if (StringUtils.compare(exceptionStartTime, timeSlotToWorkTime) <= 0) {
                        lateCount = 0;
                        lateMinute = 0;
                        attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);
                    } else {
                        String noSignInAsType = attRuleParamBean.getNoSignInAsType();
                        if (AttConstant.ATT_NOSIGNIN_COUNTTYPE_LATE.equals(noSignInAsType)) {
                            lateCount = 1;
                            lateMinute = Integer.parseInt(attRuleParamBean.getNoSignInAsLateMinute());
                        } else {
                            lateCount = 0;
                            lateMinute = 0;
                        }
                    }
                }
            } else {
                // 有签到（有上班卡点）

                // 判断是否开启弹性提前延后上班
                if (ConstUtil.SYSTEM_COMMON_TRUE.equals(attTimeSlotItem.getEnableFlexibleWork())) {
                    // 开启弹性提前延后上班

                    // 无异常(按正常取卡规则)
                    if (StringUtils.isBlank(exceptionTime)) {

                        // 弹性最大延后上班时间
                        String delayedWorkTime =
                                AttDateUtils.addMinute(timeSlotToWorkTime, attTimeSlotItem.getDelayedWorkMinutes());
                        // 迟到需要扣除休息时间段
                        lateMinute = calculateEarlyLate(delayedWorkTime, toWorkTime, attTimeSlotBO, attTimeSlotItem);
                        // 如果迟到分钟数 大于 允许迟到分钟数,则记为迟到,否则不算迟到
                        if (lateMinute > attTimeSlotItem.getAllowLateMinutes()) {
                            lateCount = 1;
                            attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                        } else {
                            // 迟到分钟数 小于 允许可迟到分钟数,不算迟到分钟数
                            lateMinute = 0;
                        }

                    } else {
                        // 有异常

                        // 1.异常结束 <= 时段上班时间
                        if (StringUtils.compare(exceptionEndTime, timeSlotToWorkTime) <= 0) {
                            // 1).卡点 < 异常开始
                            if (StringUtils.compare(toWorkTime, exceptionStartTime) < 0) {
                                // a.异常开始 <= 弹性上班卡点
                                if (StringUtils.compare(exceptionStartTime, timeSlotToWorkTime) <= 0) {
                                    // 无提前上班（异常无效），按时段上班卡点计
                                    // 弹性最大延后上班时间
                                    String delayedWorkTime = AttDateUtils.addMinute(timeSlotToWorkTime,
                                            attTimeSlotItem.getDelayedWorkMinutes());
                                    // 迟到需要扣除休息时间段
                                    lateMinute =
                                            calculateEarlyLate(delayedWorkTime, toWorkTime, attTimeSlotBO, attTimeSlotItem);
                                    // 如果迟到分钟数 大于 允许迟到分钟数,则记为迟到,否则不算迟到
                                    if (lateMinute > attTimeSlotItem.getAllowLateMinutes()) {
                                        lateCount = 1;
                                        attTimeSlotBO
                                                .setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                    } else {
                                        // 迟到分钟数 小于 允许可迟到分钟数,不算迟到分钟数
                                        lateMinute = 0;
                                    }
                                } else if (StringUtils.compare(exceptionStartTime, timeSlotToWorkTime) > 0) {
                                    // b.异常开始 > 弹性上班卡点

                                    // 上班时间=卡点，算提前上班（异常有效）
                                    // 弹性最大延后上班时间
                                    String delayedWorkTime = AttDateUtils.addMinute(timeSlotToWorkTime,
                                            attTimeSlotItem.getDelayedWorkMinutes());
                                    // 迟到需要扣除休息时间段
                                    lateMinute =
                                            calculateEarlyLate(delayedWorkTime, toWorkTime, attTimeSlotBO, attTimeSlotItem);
                                    // 如果迟到分钟数 大于 允许迟到分钟数,则记为迟到,否则不算迟到
                                    if (lateMinute > attTimeSlotItem.getAllowLateMinutes()) {
                                        lateCount = 1;
                                        attTimeSlotBO
                                                .setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                    } else {
                                        // 迟到分钟数 小于 允许可迟到分钟数,不算迟到分钟数
                                        lateMinute = 0;
                                        attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);
                                    }
                                }
                            } else if ((StringUtils.compare(exceptionStartTime, toWorkTime) <= 0)
                                    && (StringUtils.compare(toWorkTime, exceptionEndTime) <= 0)) {
                                // 2).异常开始 <= 卡点 <= 异常结束

                                // 无提前上班（异常无效），按时段上班卡点计；
                                // 弹性最大延后上班时间
                                String delayedWorkTime =
                                        AttDateUtils.addMinute(timeSlotToWorkTime, attTimeSlotItem.getDelayedWorkMinutes());
                                // 迟到需要扣除休息时间段
                                lateMinute =
                                        calculateEarlyLate(delayedWorkTime, toWorkTime, attTimeSlotBO, attTimeSlotItem);
                                // 如果迟到分钟数 大于 允许迟到分钟数,则记为迟到,否则不算迟到
                                if (lateMinute > attTimeSlotItem.getAllowLateMinutes()) {
                                    lateCount = 1;
                                    attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                } else {
                                    // 迟到分钟数 小于 允许可迟到分钟数,不算迟到分钟数
                                    lateMinute = 0;
                                    attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);
                                }
                            } else if (StringUtils.compare(toWorkTime, exceptionEndTime) > 0) {
                                // 3).卡点 > 异常结束
                                // 提前上班（异常无效），上班时间=卡点；
                                // 弹性最大延后上班时间
                                String delayedWorkTime =
                                        AttDateUtils.addMinute(timeSlotToWorkTime, attTimeSlotItem.getDelayedWorkMinutes());
                                // 迟到需要扣除休息时间段
                                lateMinute =
                                        calculateEarlyLate(delayedWorkTime, toWorkTime, attTimeSlotBO, attTimeSlotItem);
                                // 如果迟到分钟数 大于 允许迟到分钟数,则记为迟到,否则不算迟到
                                if (lateMinute > attTimeSlotItem.getAllowLateMinutes()) {
                                    lateCount = 1;
                                    attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                } else {
                                    // 迟到分钟数 小于 允许可迟到分钟数,不算迟到分钟数
                                    lateMinute = 0;
                                    attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);
                                }
                            }
                        } else if ((StringUtils.compare(exceptionStartTime, timeSlotToWorkTime) <= 0)
                                && (StringUtils.compare(exceptionEndTime, timeSlotToWorkTime) > 0)) {
                            // 2.异常开始 <= 时段上班时间 && 异常结束 > 时段上班时间

                            // 1).卡点 < 异常开始
                            if (StringUtils.compare(toWorkTime, exceptionStartTime) < 0) {

                                // 弹性最大延后上班时间
                                String delayedWorkTime =
                                        AttDateUtils.addMinute(timeSlotToWorkTime, attTimeSlotItem.getDelayedWorkMinutes());

                                // a.异常开始 <= 弹性上班卡点
                                if (StringUtils.compare(exceptionStartTime, delayedWorkTime) <= 0) {
                                    // 无迟到、无提前上班(异常的有效开始时间为时段上班卡点)，上班时间=异常结束
                                    attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);

                                } else if (StringUtils.compare(exceptionStartTime, timeSlotToWorkTime) > 0) {
                                    // b.异常开始 > 弹性上班卡点
                                    attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);
                                    // 上班时间=卡点，无迟到、算提前上班（异常有效）
                                }
                            } else if ((StringUtils.compare(exceptionStartTime, toWorkTime) <= 0)
                                    && (StringUtils.compare(toWorkTime, exceptionEndTime) < 0)) {
                                // 2).异常开始 <= 卡点 < 异常结束
                                // 无提前上班(异常的有效开始时间为时段上班卡点)，无迟到、上班时间=异常结束
                                attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);
                            } else if (StringUtils.compare(toWorkTime, exceptionEndTime) >= 0) {
                                // 3).卡点 >= 异常结束
                                // 班前异常申请，如果卡点落在允许迟到范围内则不算迟到，否则 迟到=卡点-异常结束时间，上班时间=卡点
                                // 迟到需要扣除休息时间段
                                lateMinute =
                                        calculateEarlyLate(timeSlotToWorkTime, toWorkTime, attTimeSlotBO, attTimeSlotItem);
                                if (lateMinute > attTimeSlotItem.getAllowLateMinutes()) {
                                    lateMinute = AttDateUtils.getMinuteDiff(exceptionEndTime, toWorkTime);
                                    if (lateMinute > 0) {
                                        lateCount = 1;
                                        attTimeSlotBO
                                                .setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                    } else {
                                        lateMinute = 0;
                                        attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);
                                    }
                                }
                            }
                        } else if ((StringUtils.compare(exceptionStartTime, timeSlotToWorkTime) > 0)
                                && (StringUtils.compare(exceptionEndTime, timeSlotToWorkTime) > 0)) {
                            // 3.异常开始 > 时段上班时间 && 异常结束 > 时段上班时间

                            // 1).卡点 <异常开始
                            if (StringUtils.compare(toWorkTime, exceptionStartTime) < 0) {
                                // 上班时间=卡点，算延后或迟到上班；

                                String delayedWorkTime =
                                        AttDateUtils.addMinute(timeSlotToWorkTime, attTimeSlotItem.getDelayedWorkMinutes());
                                // 迟到需要扣除休息时间段
                                lateMinute =
                                        calculateEarlyLate(delayedWorkTime, toWorkTime, attTimeSlotBO, attTimeSlotItem);
                                if (lateMinute > attTimeSlotItem.getAllowLateMinutes()) {
                                    lateCount = 1;
                                    attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                } else {
                                    lateMinute = 0;
                                    attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);
                                }

                            } else if ((StringUtils.compare(exceptionStartTime, toWorkTime) <= 0)
                                    && (StringUtils.compare(toWorkTime, exceptionEndTime) <= 0)) {
                                // 2).异常开始 <= 卡点 <= 异常结束

                                // 无提前或延后上班、无迟到，算旷工=异常开始-时间上班时间【保存旷工时长】，有效上班的卡点=异常结束时间；
                                Integer absentMinute =
                                        AttDateUtils.getMinuteDiff(timeSlotToWorkTime, exceptionStartTime);
                                attTimeSlotBO.setAbsentMinute(absentMinute);

                                attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);

                            } else if (StringUtils.compare(toWorkTime, exceptionEndTime) > 0) {
                                // 3).卡点 > 异常结束

                                // 无提前或延后上班，旷工(旷工=异常开始-时段上班时间)+迟到(迟到=卡点-异常结束)；
                                Integer absentMinute =
                                        AttDateUtils.getMinuteDiff(timeSlotToWorkTime, exceptionStartTime);
                                attTimeSlotBO.setAbsentMinute(absentMinute);
                                // 迟到需要扣除休息时间段
                                lateMinute =
                                        calculateEarlyLate(exceptionEndTime, toWorkTime, attTimeSlotBO, attTimeSlotItem);
                                if (lateMinute > attTimeSlotItem.getAllowLateMinutes()) {
                                    lateCount = 1;
                                    attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                } else {
                                    lateMinute = 0;
                                    attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);
                                }
                            }

                        }
                    }

                } else {
                    // 没有开启弹性提前延后上班

                    // 无异常，则直接判断是否迟到；
                    if (StringUtils.isBlank(exceptionTime)) {
                        // 迟到需要扣除休息时间段
                        lateMinute = calculateEarlyLate(timeSlotToWorkTime, toWorkTime, attTimeSlotBO, attTimeSlotItem);
                        // 如果迟到分钟数 大于 允许迟到分钟数,则记为迟到,否则不算迟到
                        if (lateMinute > attTimeSlotItem.getAllowLateMinutes()) {
                            lateCount = 1;
                            attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                        } else {
                            // 迟到分钟数 小于 允许可迟到分钟数,不算迟到分钟数
                            lateMinute = 0;
                        }
                    } else {
                        // 有异常

                        // 1.异常开始 <= 时段上班时间
                        if (StringUtils.compare(exceptionStartTime, timeSlotToWorkTime) <= 0) {
                            // a.卡点 <= 异常结束时间
                            if (StringUtils.compare(exceptionStartTime, timeSlotToWorkTime) <= 0) {
                                // 上班时间为异常结束时间，属于班前异常申请，无迟到/旷工/缺卡；
                            } else {
                                // 卡点 > 异常结束时间

                                // 算迟到=卡点-异常结束时间；
                                // 迟到需要扣除休息时间段
                                lateMinute =
                                        calculateEarlyLate(exceptionEndTime, toWorkTime, attTimeSlotBO, attTimeSlotItem);
                                // 如果迟到分钟数 大于 允许迟到分钟数,则记为迟到,否则不算迟到
                                if (lateMinute > attTimeSlotItem.getAllowLateMinutes()) {
                                    lateCount = 1;
                                    attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                } else {
                                    // 迟到分钟数 小于 允许可迟到分钟数,不算迟到分钟数
                                    lateMinute = 0;
                                }
                            }
                        } else {
                            // 2.异常开始 > 时段上班时间

                            // 异常开始 >= 卡点
                            if (StringUtils.compare(exceptionStartTime, toWorkTime) >= 0) {
                                // 上班时间=卡点，算迟到=卡点-时间段上班时间(大于0表示迟到)
                                // 迟到需要扣除休息时间段
                                lateMinute =
                                        calculateEarlyLate(timeSlotToWorkTime, toWorkTime, attTimeSlotBO, attTimeSlotItem);
                                // 如果迟到分钟数 大于 允许迟到分钟数,则记为迟到,否则不算迟到
                                if (lateMinute > attTimeSlotItem.getAllowLateMinutes()) {
                                    lateCount = 1;
                                    attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                } else {
                                    // 迟到分钟数 小于 允许可迟到分钟数,不算迟到分钟数
                                    lateMinute = 0;
                                }
                            } else if ((StringUtils.compare(exceptionStartTime, toWorkTime) < 0)
                                    && (StringUtils.compare(toWorkTime, exceptionEndTime) <= 0)) {
                                // 异常开始 < 卡点 <= 异常结束
                                // 上班时间=异常结束时间，算迟到=异常开始-时间段上班时间
                                // 迟到需要扣除休息时间段
                                lateMinute = calculateEarlyLate(timeSlotToWorkTime, exceptionStartTime, attTimeSlotBO,
                                        attTimeSlotItem);
                                // 如果迟到分钟数 大于 允许迟到分钟数,则记为迟到,否则不算迟到
                                if (lateMinute > attTimeSlotItem.getAllowLateMinutes()) {
                                    lateCount = 1;
                                    attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                } else {
                                    // 迟到分钟数 小于 允许可迟到分钟数,不算迟到分钟数
                                    lateMinute = 0;
                                }
                            } else if (StringUtils.compare(exceptionEndTime, toWorkTime) < 0) {
                                // 异常结束 < 卡点
                                // 迟到需要扣除休息时间段
                                lateMinute = calculateEarlyLate(timeSlotToWorkTime, exceptionStartTime, attTimeSlotBO,
                                        attTimeSlotItem)
                                        + calculateEarlyLate(exceptionEndTime, toWorkTime, attTimeSlotBO, attTimeSlotItem);
                                // 如果迟到分钟数 大于 允许迟到分钟数,则记为迟到,否则不算迟到
                                if (lateMinute > attTimeSlotItem.getAllowLateMinutes()) {
                                    lateCount = 1;
                                    attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                } else {
                                    // 迟到分钟数 小于 允许可迟到分钟数,不算迟到分钟数
                                    lateMinute = 0;
                                }
                            }
                        }
                    }
                }
            }

            //判断是否该考勤记录是否是门禁记录推送过来的

            if (lateCount > 0 && StringUtils.isNotBlank(toWorkTime)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date attData = new Date();
                try {
                    attData = sdf.parse(toWorkTime);
                } catch (ParseException e) {
                    log.error("toWorkTime {} ", toWorkTime);
                    log.error("日期转换错误  {}", e.getLocalizedMessage(), e);
                }
                String id = attTransactionDao.findAttTransactionIsPush(pin, attData, attData.getTime());
                if (StringUtils.isNotBlank(id)) {

                    //异步进行
                    Date finalAttData = attData;
                    CompletableFuture.runAsync(() -> {
                        //需要进行推送
                        //迟到推送到门禁再推送到微信公众号

                        attTransactionService.pushAttLateToAcc(pin, finalAttData,timeSlotToWorkTime);
                        //更新考勤记录修改为不需要进行推送
                        attTransactionDao.updatePushWechatFlag(id);
                    });
                }

            }


            attTimeSlotBO.setLateCount(lateCount);
            attTimeSlotBO.setLateMinute(lateMinute);

        }

    }

    /**
     * 早退信息
     *
     * @param attPersonSchBO
     * @param attTimeSlotItemMap
     * @param attRuleParamBean
     */
    private void fillEarlyData(AttPersonSchBO attPersonSchBO, Map<String, AttTimeSlotItem> attTimeSlotItemMap,
                               AttRuleParamBean attRuleParamBean) {
        List<AttTimeSlotBO> attTimeSlotBOList = attPersonSchBO.getAttTimeSlotArray();
        for (AttTimeSlotBO attTimeSlotBO : attTimeSlotBOList) {

            attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);

            AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());

            // 非必需签退，否则无早退
            if (!attTimeSlotItem.getIsMustSignOff()) {
                attTimeSlotBO.setEarlyMinute(0);
                attTimeSlotBO.setEarlyCount(0);
                continue;
            }

            int earlyMinute = 0;
            int earlyCount = 0;
            String offWorkTime = attTimeSlotBO.getOffWorkTime();
            String timeSlotOffWorkTime =
                    String.format("%s %s:00", attTimeSlotBO.getSecondDay(), attTimeSlotItem.getOffWorkTime());
            if (ConstUtil.SYSTEM_COMMON_TRUE.equals(attTimeSlotItem.getEnableFlexibleWork())
                    && null != attTimeSlotBO.getFlexibleTime()) {
                // 根据上步（迟到）的提前延后分钟数，算出下班时间点
                timeSlotOffWorkTime = AttDateUtils.addMinute(timeSlotOffWorkTime, -attTimeSlotBO.getFlexibleTime());
            }

            String exceptionTime = attTimeSlotBO.getExceptionTime();
            String exceptionStartTime = null;
            String exceptionEndTime = null;
            // 如果存在异常申请,获取异常最后结束时间
            if (StringUtils.isNotBlank(exceptionTime)) {
                String[] exceptionTimeArr = exceptionTime.split(",");
                List<String[]> list = new ArrayList<>();
                for (String time : exceptionTimeArr) {
                    String[] split = time.split("=");
                    list.add(new String[]{split[0], split[1]});
                }
                Collections.sort(list, ((o1, o2) -> StringUtils.compare(o2[1], o1[1])));
                exceptionStartTime = list.get(0)[0];
                exceptionEndTime = list.get(0)[1];
            }

            // 未签退（无下班卡点）
            if (attRuleParamBean.getNoSignOff().equals(attTimeSlotBO.getOffWorkTime())) {

                attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);

                // 无异常
                if (StringUtils.isBlank(exceptionTime)) {
                    // 判断是否设置未签退为早退，是则早退，否则算旷工；如果未签退为不完整，则无早退
                    String noSignOutAsType = attRuleParamBean.getNoSignOutAsType();
                    if (AttConstant.ATT_NOSIGNOFF_COUNTTYPE_EARLY.equals(noSignOutAsType)) {
                        earlyMinute = Integer.parseInt(attRuleParamBean.getNoSignOutAsEarlyMinute());
                        earlyCount = 1;
                    } else {
                        earlyMinute = 0;
                        earlyCount = 0;
                    }
                } else {
                    // 有异常

                    // 要判断时段下班时间<=异常结束，是则无早退；否则是否设置未签退为早退，是则早退，否则算旷工；如果未签退为不完整，则无早退
                    if (StringUtils.compare(timeSlotOffWorkTime, exceptionEndTime) <= 0) {
                        earlyMinute = 0;
                        earlyCount = 0;
                        attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);
                    } else {
                        String noSignOutAsType = attRuleParamBean.getNoSignOutAsType();
                        if (AttConstant.ATT_NOSIGNOFF_COUNTTYPE_EARLY.equals(noSignOutAsType)) {
                            earlyMinute = Integer.parseInt(attRuleParamBean.getNoSignOutAsEarlyMinute());
                            earlyCount = 1;
                        } else {
                            earlyMinute = 0;
                            earlyCount = 0;
                        }
                    }
                }

            } else {
                // 有签到（有下班卡点）

                // 判断是否开启弹性提前延后上班
                if (ConstUtil.SYSTEM_COMMON_TRUE.equals(attTimeSlotItem.getEnableFlexibleWork())) {
                    // 开启弹性提前延后上班

                    // 无异常，则直接判断是否早退；
                    if (StringUtils.isBlank(exceptionTime)) {
                        earlyMinute =
                                calculateEarlyLate(offWorkTime, timeSlotOffWorkTime, attTimeSlotBO, attTimeSlotItem);
                        // 如果早退分钟数 大于 可允许早退分钟数 则记为早退,否则不算早退
                        if (earlyMinute > attTimeSlotItem.getAllowEarlyMinutes()) {
                            earlyCount = 1;
                            attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                        } else {
                            // 早退分钟数 小于 允许可早退分钟数,不算早退分钟数
                            earlyMinute = 0;
                        }
                    } else {
                        // 有异常：

                        // 异常结束 < 弹性下班时间
                        if (StringUtils.compare(exceptionEndTime, timeSlotOffWorkTime) < 0) {
                            // 卡点 < 异常开始
                            if (StringUtils.compare(offWorkTime, exceptionStartTime) < 0) {
                                // 则下班时间=卡点，算早退=(异常开始-卡点)+(弹性下班时间-异常结束)； 注意休息时间段
                                earlyMinute =
                                        calculateEarlyLate(offWorkTime, exceptionStartTime, attTimeSlotBO, attTimeSlotItem)
                                                + calculateEarlyLate(exceptionEndTime, timeSlotOffWorkTime, attTimeSlotBO,
                                                attTimeSlotItem);
                                // 如果早退分钟数 大于 可允许早退分钟数 则记为早退,否则不算早退
                                if (earlyMinute > attTimeSlotItem.getAllowEarlyMinutes()) {
                                    earlyCount = 1;
                                    attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                } else {
                                    // 早退分钟数 小于 允许可早退分钟数,不算早退分钟数
                                    earlyMinute = 0;
                                }
                            } else if ((StringUtils.compare(exceptionStartTime, offWorkTime) <= 0)
                                    && (StringUtils.compare(offWorkTime, exceptionEndTime) <= 0)) {
                                // 异常开始 <= 卡点 <=异常结束

                                // 则下班时间=异常结束时间，算早退；
                                earlyMinute = calculateEarlyLate(exceptionEndTime, timeSlotOffWorkTime, attTimeSlotBO,
                                        attTimeSlotItem);
                                // 如果早退分钟数 大于 可允许早退分钟数 则记为早退,否则不算早退
                                if (earlyMinute > attTimeSlotItem.getAllowEarlyMinutes()) {
                                    earlyCount = 1;
                                    attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                } else {
                                    // 早退分钟数 小于 允许可早退分钟数,不算早退分钟数
                                    earlyMinute = 0;
                                }
                            } else if (StringUtils.compare(offWorkTime, exceptionEndTime) > 0) {
                                // 卡点 > 异常结束

                                // 则下班时间=卡点；算早退；
                                earlyMinute = calculateEarlyLate(offWorkTime, timeSlotOffWorkTime, attTimeSlotBO,
                                        attTimeSlotItem);
                                // 如果早退分钟数 大于 可允许早退分钟数 则记为早退,否则不算早退
                                if (earlyMinute > attTimeSlotItem.getAllowEarlyMinutes()) {
                                    earlyCount = 1;
                                    attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                } else {
                                    // 早退分钟数 小于 允许可早退分钟数,不算早退分钟数
                                    earlyMinute = 0;
                                }
                            }
                        } else {
                            // 异常结束 >= 弹性下班时间

                            // 卡点 < 异常开始
                            if (StringUtils.compare(offWorkTime, exceptionStartTime) < 0) {
                                // 算早退=异常开始-卡点；
                                earlyMinute =
                                        calculateEarlyLate(offWorkTime, exceptionStartTime, attTimeSlotBO, attTimeSlotItem);
                                // 如果早退分钟数 大于 可允许早退分钟数 则记为早退,否则不算早退
                                if (earlyMinute > attTimeSlotItem.getAllowEarlyMinutes()) {
                                    earlyCount = 1;
                                    attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                } else {
                                    // 早退分钟数 小于 允许可早退分钟数,不算早退分钟数
                                    earlyMinute = 0;
                                }
                            } else if ((StringUtils.compare(exceptionStartTime, offWorkTime) <= 0)
                                    && (StringUtils.compare(offWorkTime, exceptionEndTime) <= 0)) {
                                // 异常开始 <= 卡点 <=异常结束
                                // 则下班时间=异常结束时间；无早退/旷工/缺卡；
                            } else if (StringUtils.compare(offWorkTime, exceptionEndTime) > 0) {
                                // 卡点 > 异常结束
                                // 跟早退业务无关，不计在此环节；
                            }
                        }
                    }
                } else {
                    // 没有开启弹性提前延后上班

                    // 无异常，则直接判断是否早退；
                    if (StringUtils.isBlank(exceptionTime)) {
                        earlyMinute =
                                calculateEarlyLate(offWorkTime, timeSlotOffWorkTime, attTimeSlotBO, attTimeSlotItem);
                        // 如果早退分钟数 大于 可允许早退分钟数 则记为早退,否则不算早退
                        if (earlyMinute > attTimeSlotItem.getAllowEarlyMinutes()) {
                            earlyCount = 1;
                            attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                        } else {
                            // 早退分钟数 小于 允许可早退分钟数,不算早退分钟数
                            earlyMinute = 0;
                        }
                    } else {
                        // 有异常：

                        // 异常结束 < 时间段下班时间
                        if (StringUtils.compare(exceptionEndTime, timeSlotOffWorkTime) < 0) {
                            // 卡点 < 异常开始
                            if (StringUtils.compare(offWorkTime, exceptionStartTime) < 0) {
                                // 则下班时间=卡点，算早退=(异常开始-卡点)+(时间段下班时间-异常结束)；注意休息时间段
                                earlyMinute =
                                        calculateEarlyLate(offWorkTime, exceptionStartTime, attTimeSlotBO, attTimeSlotItem)
                                                + calculateEarlyLate(exceptionEndTime, timeSlotOffWorkTime, attTimeSlotBO,
                                                attTimeSlotItem);
                                // 如果早退分钟数 大于 可允许早退分钟数 则记为早退,否则不算早退
                                if (earlyMinute > attTimeSlotItem.getAllowEarlyMinutes()) {
                                    earlyCount = 1;
                                    attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                } else {
                                    // 早退分钟数 小于 允许可早退分钟数,不算早退分钟数
                                    earlyMinute = 0;
                                }
                            } else if ((StringUtils.compare(exceptionStartTime, offWorkTime) <= 0)
                                    && (StringUtils.compare(offWorkTime, exceptionEndTime) <= 0)) {
                                // 异常开始 <= 卡点 <=异常结束

                                // 则下班时间=异常结束时间，算早退=时间段下班时间-异常结束时间；
                                earlyMinute = calculateEarlyLate(exceptionEndTime, timeSlotOffWorkTime, attTimeSlotBO,
                                        attTimeSlotItem);
                                // 如果早退分钟数 大于 可允许早退分钟数 则记为早退,否则不算早退
                                if (earlyMinute > attTimeSlotItem.getAllowEarlyMinutes()) {
                                    earlyCount = 1;
                                    attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                } else {
                                    // 早退分钟数 小于 允许可早退分钟数,不算早退分钟数
                                    earlyMinute = 0;
                                }
                            } else {
                                // 卡点 > 异常结束时

                                // 则下班时间=卡点，算早退；
                                earlyMinute = calculateEarlyLate(offWorkTime, timeSlotOffWorkTime, attTimeSlotBO,
                                        attTimeSlotItem);
                                // 如果早退分钟数 大于 可允许早退分钟数 则记为早退,否则不算早退
                                if (earlyMinute > attTimeSlotItem.getAllowEarlyMinutes()) {
                                    earlyCount = 1;
                                    attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                } else {
                                    // 早退分钟数 小于 允许可早退分钟数,不算早退分钟数
                                    earlyMinute = 0;
                                }
                            }

                        } else if (StringUtils.compare(exceptionEndTime, timeSlotOffWorkTime) >= 0) {
                            // 异常结束 >= 时间段下班时间

                            // 卡点 < 异常开始
                            if (StringUtils.compare(offWorkTime, exceptionEndTime) < 0) {
                                // 算早退=异常开始-卡点；
                                earlyMinute = calculateEarlyLate(offWorkTime, exceptionStartTime, attTimeSlotBO,
                                        attTimeSlotItem);
                                ;
                                // 如果早退分钟数 大于 可允许早退分钟数 则记为早退,否则不算早退
                                if (earlyMinute > attTimeSlotItem.getAllowEarlyMinutes()) {
                                    earlyCount = 1;
                                    attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                                } else {
                                    // 早退分钟数 小于 允许可早退分钟数,不算早退分钟数
                                    earlyMinute = 0;
                                }
                            } else if ((StringUtils.compare(exceptionStartTime, offWorkTime) <= 0)
                                    && (StringUtils.compare(offWorkTime, exceptionEndTime) <= 0)) {
                                // 异常开始 <= 卡点 <=异常结束
                                // 则下班时间=异常结束时间；无早退/旷工/缺卡；
                            } else if (StringUtils.compare(offWorkTime, exceptionEndTime) >= 0) {
                                // 卡点 >= 异常结束
                                // 跟早退业务无关，不计在此环节；
                            }
                        }
                    }
                }
            }

            attTimeSlotBO.setEarlyMinute(earlyMinute);
            attTimeSlotBO.setEarlyCount(earlyCount);

        }

    }

    /**
     * 旷工信息
     *
     * @param attPersonSchBO
     * @param attTimeSlotItemMap
     * @param attRuleParamBean
     */
    private void fillAbsentData(AttPersonSchBO attPersonSchBO, Map<String, AttTimeSlotItem> attTimeSlotItemMap,
                                AttRuleParamBean attRuleParamBean) {

        List<AttTimeSlotBO> attTimeSlotBOList = attPersonSchBO.getAttTimeSlotArray();
        for (AttTimeSlotBO attTimeSlotBO : attTimeSlotBOList) {
            AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());

            int absentMinute = 0;

            Boolean isMustSignIn = attTimeSlotItem.getIsMustSignIn();
            Boolean isMustSignOff = attTimeSlotItem.getIsMustSignOff();
            boolean isNoSignIn = attRuleParamBean.getNoSignIn().equals(attTimeSlotBO.getToWorkTime());
            boolean isNoSignOff = attRuleParamBean.getNoSignOff().equals(attTimeSlotBO.getOffWorkTime());

            // 迟到且早退算旷工，且即迟到又早退
            if (ConstUtil.SYSTEM_COMMON_TRUE.equals(attRuleParamBean.getLateAndEarlyAsAbsent())
                    && attTimeSlotBO.getLateMinute() > 0 && attTimeSlotBO.getEarlyMinute() > 0) {

                // 旷工时长=工作时段时长– 异常时长，且迟到和早退置为零
                absentMinute = attTimeSlotItem.getWorkingHours() - attTimeSlotBO.getLeaveMinute()
                        - attTimeSlotBO.getOutMinute() - attTimeSlotBO.getTripMinute();

                attTimeSlotBO.setLateMinute(0);
                attTimeSlotBO.setLateCount(0);
                attTimeSlotBO.setEarlyMinute(0);
                attTimeSlotBO.setEarlyCount(0);

            } else if ((isMustSignIn && isNoSignIn) && (isMustSignOff && isNoSignOff)) {
                // 必须签到未签到且必须签退未签退
                // 如果有异常且时段上下班时间在异常范围内，则无旷工

                String timeSlotStartDateTime =
                        String.format("%s %s:00", attTimeSlotBO.getFirstDay(), attTimeSlotItem.getToWorkTime());
                String timeSlotEndDateTime =
                        String.format("%s %s:00", attTimeSlotBO.getSecondDay(), attTimeSlotItem.getOffWorkTime());
                boolean signInAtExce = attOvertimeCalculate.isCardInException(timeSlotStartDateTime, attTimeSlotBO);
                boolean signOffAtExce = attOvertimeCalculate.isCardInException(timeSlotEndDateTime, attTimeSlotBO);
                if (!signInAtExce && !signOffAtExce) {

                    // 计算迟到
                    int noSignInAsLateMinute = Integer.valueOf(attRuleParamBean.getNoSignInAsLateMinute());
                    if (noSignInAsLateMinute > 0) {
                        attTimeSlotBO.setLateMinute(Integer.valueOf(noSignInAsLateMinute));
                        attTimeSlotBO.setLateCount(1);
                    } else {
                        attTimeSlotBO.setLateMinute(0);
                        attTimeSlotBO.setLateCount(0);
                    }

                    // 计算早退
                    int noSignOutAsEarlyMinute = Integer.valueOf(attRuleParamBean.getNoSignOutAsEarlyMinute());
                    if (noSignOutAsEarlyMinute > 0) {
                        attTimeSlotBO.setEarlyMinute(noSignOutAsEarlyMinute);
                        attTimeSlotBO.setEarlyCount(1);
                    } else {
                        attTimeSlotBO.setEarlyMinute(0);
                        attTimeSlotBO.setEarlyCount(0);
                    }

                    // 未签到记为不完整或未签退记为不完整，不算旷工，记为不完整
                    if (AttRuleEnum.NoSignInCountType.getValueThree().equals(attRuleParamBean.getNoSignInAsType())
                            || AttRuleEnum.noSignOffCountType.getValueThree()
                            .equals(attRuleParamBean.getNoSignOutAsType())) {
                        attTimeSlotBO.setIsInComplete(true);
                    }
                    // 未签到记为缺勤或未签退记为缺勤，才计算旷工
                    else if (AttRuleEnum.NoSignInCountType.getValueOne().equals(attRuleParamBean.getNoSignInAsType())
                            || AttRuleEnum.noSignOffCountType.getValueOne().equals(attRuleParamBean.getNoSignOutAsType())) {
                        // 旷工时长=时段时长 – 异常时长 - 迟到 - 早退
                        absentMinute = attTimeSlotItem.getWorkingHours() - attTimeSlotBO.getLeaveMinute()
                                - attTimeSlotBO.getOutMinute() - attTimeSlotBO.getTripMinute() - noSignInAsLateMinute
                                - noSignOutAsEarlyMinute;
                        // 防止故意将参数配置记为迟到或记为早退分钟较大的情况下
                        if (absentMinute < 0) {
                            absentMinute = 0;
                        }
                    }
                } else if (signInAtExce && !signOffAtExce) {
                    // 上班异常申请，下班卡未签到
                    absentMinute = fillAbsentAtNoSignOff(attTimeSlotBO, attTimeSlotItem, attRuleParamBean);
                } else if (!signInAtExce && signOffAtExce) {
                    // 上班卡未签到，下班异常申请
                    absentMinute = fillAbsentAtNoSignIn(attTimeSlotBO, attTimeSlotItem, attRuleParamBean);
                }

            } else {
                // 判断上下班实际卡点：

                if ((isMustSignIn && !isNoSignIn) && (isMustSignOff && !isNoSignOff)) {
                    // 有上班卡，且有下班卡

                    // 则无旷工
                    absentMinute = 0;
                } else if ((!isMustSignIn || !isNoSignIn) && (isMustSignOff && isNoSignOff)) {

                    // 有上班卡，无下班卡
                    absentMinute = fillAbsentAtNoSignOff(attTimeSlotBO, attTimeSlotItem, attRuleParamBean);
                } else if ((isMustSignIn && isNoSignIn) && (!isMustSignOff || !isNoSignOff)) {

                    // 无上班卡，有下班卡
                    absentMinute = fillAbsentAtNoSignIn(attTimeSlotBO, attTimeSlotItem, attRuleParamBean);
                }
            }

            if (null != attTimeSlotBO.getAbsentMinute()) {
                absentMinute += attTimeSlotBO.getAbsentMinute();
            }
            attTimeSlotBO.setAbsentMinute(absentMinute);
        }

    }

    /**
     * 【旷工信息】=>有上班卡，无下班卡
     *
     * @param attTimeSlotBO
     * @param attTimeSlotItem
     * @param attRuleParamBean
     * @return int 旷工时长
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/8/31 16:54
     */
    private int fillAbsentAtNoSignOff(AttTimeSlotBO attTimeSlotBO, AttTimeSlotItem attTimeSlotItem,
                                      AttRuleParamBean attRuleParamBean) {
        int absentMinute = 0;
        // 无异常
        if (StringUtils.isBlank(attTimeSlotBO.getExceptionTime())) {
            // 未签退记为缺勤才记为旷工, 未签退记为早退/不完整就不算旷工 by ljf 2020/8/10
            if (AttRuleEnum.noSignOffCountType.getValueOne().equals(attRuleParamBean.getNoSignOutAsType())) {
                // 旷工=时段时长-迟到
                absentMinute = attTimeSlotItem.getWorkingHours() - attTimeSlotBO.getLateMinute();
            } else if (AttRuleEnum.noSignOffCountType.getValueThree().equals(attRuleParamBean.getNoSignOutAsType())) {
                // 未签退记为不完整
                attTimeSlotBO.setIsInComplete(true);
            }
        } else {
            // 有异常

            String[] exceptionTimeArr = attTimeSlotBO.getExceptionTime().split(",");
            List<String[]> list = new ArrayList<>();
            for (String time : exceptionTimeArr) {
                String[] split = time.split("=");
                list.add(new String[]{split[0], split[1]});
            }
            Collections.sort(list, ((o1, o2) -> StringUtils.compare(o2[1], o1[1])));
            String exceptionEndTime = list.get(0)[1];

            String timeSlotEndDateTime =
                    String.format("%s %s:00", attTimeSlotBO.getSecondDay(), attTimeSlotItem.getOffWorkTime());
            // 判断弹性提前/延后是否有值，有的话上下班卡时间要对应的提前或延后；
            if (ConstUtil.SYSTEM_COMMON_TRUE.equals(attTimeSlotItem.getEnableFlexibleWork())
                    && null != attTimeSlotBO.getFlexibleTime()) {
                timeSlotEndDateTime = AttDateUtils.addMinute(timeSlotEndDateTime, -attTimeSlotBO.getFlexibleTime());
            }

            String noSignOutAsType = attRuleParamBean.getNoSignOutAsType();
            // 未签退记为缺勤
            if (AttRuleEnum.noSignOffCountType.getValueOne().equals(noSignOutAsType)) {
                // 旷工= (时段下班时间（不是卡点） - off) + 迟到环节的旷工时长；

                String off = exceptionEndTime;
                boolean isNoSignIn = attRuleParamBean.getNoSignIn().equals(attTimeSlotBO.getToWorkTime());
                if (!isNoSignIn && StringUtils.compare(exceptionEndTime, attTimeSlotBO.getToWorkTime()) < 0) {
                    // 如果最后一个异常结束时间<上班卡点,则取上班卡点
                    off = attTimeSlotBO.getToWorkTime();
                } else if (StringUtils.compare(exceptionEndTime, timeSlotEndDateTime) > 0) {
                    // 取最后一个异常的结束做为时段下班卡点(off)，如果大于时段下班时间，则取时段下班时间
                    off = timeSlotEndDateTime;
                }

                // 判断扣除休息时间段 by ljf 2020/8/26
                absentMinute = getTimeSlotIntersectionTime(off, timeSlotEndDateTime, attTimeSlotBO, attTimeSlotItem);

            } else if (AttRuleEnum.noSignOffCountType.getValueThree().equals(noSignOutAsType)) {
                // 未签退记为不完整
                if (StringUtils.compare(exceptionEndTime, timeSlotEndDateTime) < 0) {
                    attTimeSlotBO.setIsInComplete(true);
                }
            }
        }
        return absentMinute;
    }

    /**
     * 【旷工信息】=>无上班卡，有下班卡
     *
     * @param attTimeSlotBO
     * @param attTimeSlotItem
     * @param attRuleParamBean
     * @return int 旷工时长
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/8/31 18:40
     */
    private int fillAbsentAtNoSignIn(AttTimeSlotBO attTimeSlotBO, AttTimeSlotItem attTimeSlotItem,
                                     AttRuleParamBean attRuleParamBean) {
        int absentMinute = 0;
        // 无异常
        if (StringUtils.isBlank(attTimeSlotBO.getExceptionTime())) {
            // 未签到记为缺勤才记为旷工, 未签到记为迟到/不完整就不算旷工
            if (AttRuleEnum.NoSignInCountType.getValueOne().equals(attRuleParamBean.getNoSignInAsType())) {
                // 旷工=时段时长-早退
                absentMinute = attTimeSlotItem.getWorkingHours() - attTimeSlotBO.getEarlyMinute();
            } else if (AttRuleEnum.NoSignInCountType.getValueThree().equals(attRuleParamBean.getNoSignInAsType())) {
                // 未签退记为不完整
                attTimeSlotBO.setIsInComplete(true);
            }
        } else {
            // 有异常

            String[] exceptionTimeArr = attTimeSlotBO.getExceptionTime().split(",");
            List<String[]> list = new ArrayList<>();
            for (String time : exceptionTimeArr) {
                String[] split = time.split("=");
                list.add(new String[]{split[0], split[1]});
            }
            Collections.sort(list, ((o1, o2) -> StringUtils.compare(o1[0], o2[0])));
            String exceptionStartTime = list.get(0)[0];

            String timeSlotStartDateTime =
                    String.format("%s %s:00", attTimeSlotBO.getFirstDay(), attTimeSlotItem.getToWorkTime());

            // 未签到记为缺勤才记为旷工, 未签到记为迟到/不完整就不算旷工 by ljf 2020/8/10
            if (AttRuleEnum.NoSignInCountType.getValueOne().equals(attRuleParamBean.getNoSignInAsType())) {
                // 旷工 = (on - 上班对应时间(不是卡点)) + 迟到环节的旷工时长；
                String on = exceptionStartTime;
                if (StringUtils.compare(exceptionStartTime, timeSlotStartDateTime) < 0) {
                    // 如果异常开始时间小于时段上班 则无旷工
                } else {
                    boolean isNoSignOff = attRuleParamBean.getNoSignOff().equals(attTimeSlotBO.getOffWorkTime());
                    if (!isNoSignOff && StringUtils.compare(exceptionStartTime, attTimeSlotBO.getOffWorkTime()) > 0) {
                        // 如果异常开始时间大于下班卡点则取下班卡点
                        on = attTimeSlotBO.getOffWorkTime();
                    }
                    // 判断扣除休息时间段 by ljf 2020/8/26
                    absentMinute =
                            getTimeSlotIntersectionTime(timeSlotStartDateTime, on, attTimeSlotBO, attTimeSlotItem);
                }
            } else if (AttRuleEnum.NoSignInCountType.getValueThree().equals(attRuleParamBean.getNoSignInAsType())) {
                // 未签到算不完整，如果异常开始 > 时段上班时间
                if (StringUtils.compare(exceptionStartTime, timeSlotStartDateTime) > 0) {
                    // 未签到记为不完整
                    attTimeSlotBO.setIsInComplete(true);
                }
            }
        }
        return absentMinute;
    }

    /**
     * 出勤信息(汇总数据)
     *
     * @param attPersonSchBO
     * @param attRecordItem
     */
    private void fillSummaryData(AttPersonSchBO attPersonSchBO, Map<String, AttTimeSlotItem> attTimeSlotItemMap,
                                 AttRecordItem attRecordItem, AttRuleParamBean attRuleParamBean) {

        String noSignIn = attRuleParamBean.getNoSignIn();
        String noSignOff = attRuleParamBean.getNoSignOff();

        List<String> timeSlotNameList = new ArrayList<>();
        List<String> shiftTimeDataList = new ArrayList<>();
        List<String> cardValidDataList = new ArrayList<>();
        List<String> cardStatusList = new ArrayList<>();

        // 出勤-应到时长
        int shouldMinuteTotal = 0;
        // 出勤-实际时长
        int actualMinuteTotal = 0;
        // 出勤-有效时长
        int validMinuteTotal = 0;
        // 有效打卡次数
        int cardValidCount = 0;

        List<Integer> lateCountList = new ArrayList<>();
        List<Integer> lateMinuteList = new ArrayList<>();
        int lateCountTotal = 0;
        int lateMinuteTotal = 0;

        List<Integer> earlyCountList = new ArrayList<>();
        List<Integer> earlyMinuteList = new ArrayList<>();
        int earlyCountTotal = 0;
        int earlyMinuteTotal = 0;
        int absentMinuteTotal = 0;
        int leaveMinuteTotal = 0;
        int tripMinuteTotal = 0;
        int outMinuteTotal = 0;
        Map<String, Integer> exceptionTimeLongMap = new HashMap<>();

        // 平时加班时长
        int overtimeUsualMinuteTotal = 0;
        // 休息日加班
        int overtimeRestMinuteTotal = 0;
        // 节假日加班时长
        int overtimeHolidayMinuteTotal = 0;
        // 总加班时长
        int overtimeMinuteTotal = 0;

        // 是否未签到未签退被记为不完整
        boolean isInComplete = false;

        List<AttTimeSlotBO> attTimeSlotBOList = attPersonSchBO.getAttTimeSlotArray();

        String firstDay = null;
        String secondDay = null;

        for (AttTimeSlotBO attTimeSlotBO : attTimeSlotBOList) {
            AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());

            if (Objects.isNull(firstDay)
                    || (AttCalculationConstant.IsInterDay.THE_INTER_DAY.equals(attTimeSlotBO.getIsInterDay()))) {
                firstDay = attTimeSlotBO.getFirstDay();
                secondDay = attTimeSlotBO.getSecondDay();
            }

            String actualToWorkTime = attTimeSlotBO.getToWorkTime();
            String actualOffWorkTime = attTimeSlotBO.getOffWorkTime();

            timeSlotNameList.add(attTimeSlotItem.getPeriodName());
            shiftTimeDataList
                    .add(String.format("%s-%s", attTimeSlotItem.getToWorkTime(), attTimeSlotItem.getOffWorkTime()));

            String validToWorkTime = actualToWorkTime;
            if (!noSignIn.equals(attTimeSlotBO.getToWorkTime())) {
                validToWorkTime = attTimeSlotBO.getToWorkTime().substring(11, 16);
                cardValidCount++;
            }
            String validOffWorkTime = actualOffWorkTime;
            if (!noSignOff.equals(attTimeSlotBO.getOffWorkTime())) {
                validOffWorkTime = actualOffWorkTime.substring(11, 16);
                cardValidCount++;
            }
            cardValidDataList.add(String.format("%s-%s", validToWorkTime, validOffWorkTime));
            cardStatusList
                    .add(String.format("%s-%s", attTimeSlotBO.getToWorkTimeStatus(), attTimeSlotBO.getOffWorkTimeStatus()));

            shouldMinuteTotal += attTimeSlotItem.getWorkingHours();
            if (!noSignIn.equals(attTimeSlotBO.getToWorkTime()) && !noSignOff.equals(attTimeSlotBO.getOffWorkTime())) {
                actualMinuteTotal +=
                        AttDateUtils.getMinuteDiff(attTimeSlotBO.getToWorkTime(), attTimeSlotBO.getOffWorkTime());
            }

            absentMinuteTotal += attTimeSlotBO.getAbsentMinute();

            // 有效时长=应到时长-迟到-早退-旷工-请假扣减时长
            Integer validMinute =
                    attTimeSlotItem.getWorkingHours() - attTimeSlotBO.getLateMinute() - attTimeSlotBO.getEarlyMinute()
                            - attTimeSlotBO.getAbsentMinute() - attTimeSlotBO.getLeaveDeductWorkLong();
            if (validMinute > 0) {
                validMinuteTotal += validMinute;
            }

            leaveMinuteTotal += attTimeSlotBO.getLeaveMinute();
            tripMinuteTotal += attTimeSlotBO.getTripMinute();
            outMinuteTotal += attTimeSlotBO.getOutMinute();

            // 汇总时段的异常类型时长
            if (!CollectionUtil.isEmpty(attTimeSlotBO.getExceptionTimeLongMap())) {
                for (Map.Entry<String, Integer> entry : attTimeSlotBO.getExceptionTimeLongMap().entrySet()) {
                    int typeTimeLong = entry.getValue();
                    if (exceptionTimeLongMap.containsKey(entry.getKey())) {
                        typeTimeLong += exceptionTimeLongMap.get(entry.getKey());
                    }
                    exceptionTimeLongMap.put(entry.getKey(), typeTimeLong);
                }
            }

            overtimeUsualMinuteTotal += attTimeSlotBO.getOvertimeUsualMinute();
            overtimeRestMinuteTotal += attTimeSlotBO.getOvertimeRestMinute();
            overtimeHolidayMinuteTotal += attTimeSlotBO.getOvertimeHolidayMinute();
            overtimeMinuteTotal += attTimeSlotBO.getOvertimeMinute();

            // 迟到
            lateCountList.add(attTimeSlotBO.getLateCount());
            lateMinuteList.add(attTimeSlotBO.getLateMinute());
            lateCountTotal += attTimeSlotBO.getLateCount();
            lateMinuteTotal += attTimeSlotBO.getLateMinute();

            // 早退
            earlyCountList.add(attTimeSlotBO.getEarlyCount());
            earlyMinuteList.add(attTimeSlotBO.getEarlyMinute());
            earlyCountTotal += attTimeSlotBO.getEarlyCount();
            earlyMinuteTotal += attTimeSlotBO.getEarlyMinute();

            // 是否被记为不完整
            if (Boolean.TRUE.equals(attTimeSlotBO.getIsInComplete())) {
                isInComplete = true;
            }
        }

        // 时间段名称集合，多个以逗号隔开
        attRecordItem.setShiftName(attPersonSchBO.getAttShiftName());
        attRecordItem.setTimeSlotName(StringUtils.join(timeSlotNameList, ","));
        attRecordItem.setShiftTimeData(StringUtils.join(shiftTimeDataList, ";"));
        attRecordItem.setCardStatus(StringUtils.join(cardStatusList, ";"));
        attRecordItem.setCrossDay(String.format("%s_%s", firstDay, secondDay));

        if (!CollectionUtil.isEmpty(cardValidDataList) && cardValidDataList.size() > 19) {
            attRecordItem.setCardValidData(StringUtils.join(cardValidDataList.subList(0, 19), ";") + "...");
        } else {
            attRecordItem.setCardValidData(StringUtils.join(cardValidDataList, ";"));
        }
        attRecordItem.setCardValidCount(cardValidCount);

        attRecordItem.setShouldMinute(shouldMinuteTotal);
        attRecordItem.setActualMinute(actualMinuteTotal);
        attRecordItem.setValidMinute(validMinuteTotal);

        attRecordItem.setLateCountData(StringUtils.join(lateCountList, ";"));
        attRecordItem.setLateMinuteData(StringUtils.join(lateMinuteList, ";"));
        attRecordItem.setLateCountTotal(lateCountTotal);
        attRecordItem.setLateMinuteTotal(lateMinuteTotal);

        attRecordItem.setEarlyCountData(StringUtils.join(earlyCountList, ";"));
        attRecordItem.setEarlyMinuteData(StringUtils.join(earlyMinuteList, ";"));
        attRecordItem.setEarlyCountTotal(earlyCountTotal);
        attRecordItem.setEarlyMinuteTotal(earlyMinuteTotal);

        attRecordItem.setAbsentMinute(absentMinuteTotal);

        attRecordItem.setLeaveMinute(leaveMinuteTotal);
        attRecordItem.setTripMinute(tripMinuteTotal);
        attRecordItem.setOutMinute(outMinuteTotal);

        // 保存各异常类型对应时长，格式:code-时长(分钟)，多个以逗号隔开
        StringBuffer leaveDetails = new StringBuffer();
        for (Map.Entry<String, Integer> entry : exceptionTimeLongMap.entrySet()) {
            leaveDetails.append(entry.getKey() + "-" + entry.getValue() + ",");
        }
        if (leaveDetails.length() > 0) {
            leaveDetails.deleteCharAt(leaveDetails.length() - 1);
        }
        attRecordItem.setLeaveDetails(leaveDetails.toString());

        // 加班
        attRecordItem.setOvertimeUsualMinute(overtimeUsualMinuteTotal);
        attRecordItem.setOvertimeRestMinute(overtimeRestMinuteTotal);
        attRecordItem.setOvertimeHolidayMinute(overtimeHolidayMinuteTotal);
        attRecordItem.setOvertimeMinute(overtimeMinuteTotal);

        // 如果未签到或未签退被记为不完整，则不计迟到早退旷工、有效
        if (isInComplete) {
            attRecordItem.setLateMinuteTotal(0);
            attRecordItem.setLateMinuteData(StringUtils.join(new int[lateMinuteList.size()], ';'));
            attRecordItem.setLateCountTotal(0);
            attRecordItem.setLateCountData(StringUtils.join(new int[lateCountList.size()], ';'));

            attRecordItem.setEarlyMinuteTotal(0);
            attRecordItem.setEarlyMinuteData(StringUtils.join(new int[earlyMinuteList.size()], ';'));
            attRecordItem.setEarlyCountTotal(0);
            attRecordItem.setEarlyCountData(StringUtils.join(new int[earlyCountList.size()], ';'));

            attRecordItem.setAbsentMinute(0);
            attRecordItem.setValidMinute(0);
        }

        // 根据班次工作类型\加班方式，计算加班时长
        attOvertimeCalculate.fillOverTimeByWorkType(attPersonSchBO, attRecordItem);
    }

    /**
     * 填充考勤状态结果
     *
     * @param attRecordItem
     * @param attRuleParamBean
     */
    private void fillAttStatus(AttRecordItem attRecordItem, AttRuleParamBean attRuleParamBean,
                               AttPersonSchBO attPersonSchBO, Map<String, AttTimeSlotItem> attTimeSlotItemMap) {

        Set<String> attendanceStatusSet = new HashSet<>();

        if (attRecordItem.getLateMinuteTotal() > 0) {
            // 迟到
            attendanceStatusSet.add(AttCalculationConstant.AttAttendStatus.LATE);
        }

        if (attRecordItem.getEarlyMinuteTotal() > 0) {
            // 早退
            attendanceStatusSet.add(AttCalculationConstant.AttAttendStatus.EARLY);
        }

        for (AttTimeSlotBO attTimeSlotBO : attPersonSchBO.getAttTimeSlotArray()) {
            AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());
            String toWorkTime = attTimeSlotBO.getToWorkTime();
            // 未签到
            if (attRuleParamBean.getNoSignIn().equals(toWorkTime)) {
                // 必须签到
                if (attTimeSlotItem.getIsMustSignIn()) {
                    String timeSlotToWorkTime =
                            String.format("%s %s:00", attTimeSlotBO.getFirstDay(), attTimeSlotItem.getToWorkTime());
                    // 上班时间没有异常申请
                    if (!attOvertimeCalculate.isCardInException(timeSlotToWorkTime, attTimeSlotBO)) {
                        String noSignInAsType = attRuleParamBean.getNoSignInAsType();
                        if (AttRuleEnum.NoSignInCountType.getValueOne().equals(noSignInAsType)) {
                            // 未签到记为缺勤
                            attendanceStatusSet.add(AttCalculationConstant.AttAttendStatus.NO_CHECK_IN);
                        } else if (AttRuleEnum.NoSignInCountType.getValueThree().equals(noSignInAsType)) {
                            // 未签到记为未签到不完整
                            attendanceStatusSet.add(AttCalculationConstant.AttAttendStatus.NO_CHECK_IN_INCOMPLETE);
                        }
                    }

                }
            }

            String offWorkTime = attTimeSlotBO.getOffWorkTime();
            // 未签退
            if (attRuleParamBean.getNoSignOff().equals(offWorkTime)) {
                // 必须签退
                if (attTimeSlotItem.getIsMustSignOff()) {
                    String timeSlotOffWorkTime =
                            String.format("%s %s:00", attTimeSlotBO.getSecondDay(), attTimeSlotItem.getOffWorkTime());
                    // 下班时间没有异常申请
                    if (!attOvertimeCalculate.isCardInException(timeSlotOffWorkTime, attTimeSlotBO)) {
                        String noSignOutAsType = attRuleParamBean.getNoSignOutAsType();
                        if (AttRuleEnum.noSignOffCountType.getValueOne().equals(noSignOutAsType)) {
                            // 未签退记为缺勤
                            attendanceStatusSet.add(AttCalculationConstant.AttAttendStatus.NO_CHECK_OUT);
                        } else if (AttRuleEnum.noSignOffCountType.getValueThree().equals(noSignOutAsType)) {
                            // 未签退记为未签退不完整
                            attendanceStatusSet.add(AttCalculationConstant.AttAttendStatus.NO_CHECK_OUT_INCOMPLETE);
                        }
                    }

                }
            }
        }

        if (attRecordItem.getLeaveMinute() > 0) {
            // 请假状态改为具体请假类别 by ljf 2020/8/7
            String leaveDetails = attRecordItem.getLeaveDetails();
            if (StringUtils.isNotBlank(leaveDetails)) {
                String[] leaveNoAndTimeLongArr = leaveDetails.split(AttConstant.COMMA);
                if (null != leaveNoAndTimeLongArr) {
                    for (String item : leaveNoAndTimeLongArr) {
                        String[] leaveNoAndTimeLong = item.split("-", -1);
                        String leaveNo = leaveNoAndTimeLong[0];
                        try {
                            Integer timeLong = Integer.valueOf(leaveNoAndTimeLong[1]);
                            if (timeLong > 0) {
                                attendanceStatusSet.add(leaveNo);
                            }
                        } catch (Exception e) {
                        }
                    }
                }
            }
            // 除了具体假种,有请假还是放请假状态,方便过滤查询
            attendanceStatusSet.add(AttCalculationConstant.AttAttendStatus.LEAVE);
        }

        if (attRecordItem.getTripMinute() > 0) {
            // 出差
            attendanceStatusSet.add(AttCalculationConstant.AttAttendStatus.TRIP);
        }

        if (attRecordItem.getOutMinute() > 0) {
            // 外出
            attendanceStatusSet.add(AttCalculationConstant.AttAttendStatus.OUT);
        }

        if (attRecordItem.getOvertimeMinute() > 0) {
            // 加班
            attendanceStatusSet.add(AttCalculationConstant.AttAttendStatus.OVERTIME);
        }

        if (attRecordItem.getAbsentMinute() > 0) {
            // 未签到或未签退不完整会清掉旷工分钟数，还旷工说明不是不完整前面判断的要移除
            attendanceStatusSet.remove(AttCalculationConstant.AttAttendStatus.NO_CHECK_IN_INCOMPLETE);
            attendanceStatusSet.remove(AttCalculationConstant.AttAttendStatus.NO_CHECK_OUT_INCOMPLETE);
            // 旷工
            attendanceStatusSet.add(AttCalculationConstant.AttAttendStatus.ABSENT);
        }

        if (attendanceStatusSet.isEmpty()) {
            // 实到/应到
            attendanceStatusSet.add(AttCalculationConstant.AttAttendStatus.ACTUAL);
        }

        attRecordItem.setAttendanceStatus(StringUtils.join(attendanceStatusSet, ","));
    }

    /**
     * 获取时间范围内和时段有效交集时长(扣除休息时间段)
     *
     * @param start           时间范围的开始
     * @param end             时间范围的开始
     * @param attTimeSlotBO   时段计算对象
     * @param attTimeSlotItem 时段对象
     * @return timeLong(分钟)
     */
    public int getTimeSlotIntersectionTime(String start, String end, AttTimeSlotBO attTimeSlotBO,
                                           AttTimeSlotItem attTimeSlotItem) {

        String timeSlotToWorkTime =
                String.format("%s %s:00", attTimeSlotBO.getFirstDay(), attTimeSlotItem.getToWorkTime());
        String timeSlotOffWorkTime =
                String.format("%s %s:00", attTimeSlotBO.getSecondDay(), attTimeSlotItem.getOffWorkTime());
        // 该时间有提前或延后上班，对应的上下班范围要相应的提前或延后
        if (ConstUtil.SYSTEM_COMMON_TRUE.equals(attTimeSlotItem.getEnableFlexibleWork())
                && null != attTimeSlotBO.getFlexibleTime()) {
            timeSlotToWorkTime = AttDateUtils.addMinute(timeSlotToWorkTime, -attTimeSlotBO.getFlexibleTime());
            timeSlotOffWorkTime = AttDateUtils.addMinute(timeSlotOffWorkTime, -attTimeSlotBO.getFlexibleTime());
        }

        String startTime = timeSlotToWorkTime;
        String endTime;
        int timeLong = 0;

        // 如果是扣除休息时间段,则申请单时长对应扣除休息时间段
        if (attTimeSlotItem.getIsSegmentDeduction()) {
            List<AttBreakTimeItem> attBreakTimeItems = attTimeSlotItem.getAttBreakTimeItems();
            if (!CollectionUtil.isEmpty(attBreakTimeItems)) {
                List<String[]> breakTimeList = new ArrayList<>();
                for (AttBreakTimeItem attBreakTimeItem : attBreakTimeItems) {
                    String startDateTime = String.format("%s %s:00",
                            (StringUtils.compare(attBreakTimeItem.getStartTime(), attTimeSlotItem.getToWorkTime()) > 0
                                    ? attTimeSlotBO.getFirstDay() : attTimeSlotBO.getSecondDay()),
                            attBreakTimeItem.getStartTime());
                    String endDateTime = String.format("%s %s:00",
                            (StringUtils.compare(attBreakTimeItem.getEndTime(), attTimeSlotItem.getToWorkTime()) > 0
                                    ? attTimeSlotBO.getFirstDay() : attTimeSlotBO.getSecondDay()),
                            attBreakTimeItem.getEndTime());
                    String[] strArr = new String[]{startDateTime, endDateTime};
                    breakTimeList.add(strArr);
                }
                Collections.sort(breakTimeList, (o1, o2) -> StringUtils.compare(o1[0], o2[0]));
                for (String[] breakTime : breakTimeList) {
                    endTime = breakTime[0];
                    timeLong += AttDateUtils.getIntersectionTime(startTime, endTime, start, end);
                    startTime = breakTime[1];
                }
            }
        }
        endTime = timeSlotOffWorkTime;
        // 计算时段和异常申请的交集即申请的有效时长
        timeLong += AttDateUtils.getIntersectionTime(startTime, endTime, start, end);
        return timeLong;
    }

    /**
     * 计算迟到或者早退时长，扣除休息时间段
     *
     * @return int
     * @throws
     * <AUTHOR>
     * @date 2025-02-17 11:31
     * @since 1.0.0
     */
    private int calculateEarlyLate(String start, String end, AttTimeSlotBO attTimeSlotBO,
                                   AttTimeSlotItem attTimeSlotItem) {
        int minute = AttDateUtils.getMinuteDiff(start, end);
        if (minute > 0) {
            minute = getTimeSlotIntersectionTime(start, end, attTimeSlotBO, attTimeSlotItem);
        }
        return minute;
    }
}
