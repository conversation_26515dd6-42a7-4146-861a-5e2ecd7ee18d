/**
 * File Name: AttPerson Created by GenerationTools on 2018-02-08 下午08:27 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.att.dao;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.att.model.AttPerson;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.transaction.annotation.Transactional;

/**
 * 对应百傲瑞达 AttPersonDao
 * 
 * <AUTHOR>
 * @date: 2018-02-08 下午08:27
 * @version v1.0
 */
public interface AttPersonDao extends BaseDao<AttPerson, String> {

    /**
     * 根据人员ID查询
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/7 20:41
     * @param personId
     * @return com.zkteco.zkbiosecurity.att.model.AttPerson
     */
    AttPerson findByPersonId(String personId);

    /**
     * 根据分组ID集合查询考勤模块的人员数据。
     * 
     * @param groupIds
     * @return
     */
    List<AttPerson> findByGroupIdIn(Collection<String> groupIds);

    /**
     * 根据人员ID集合查询
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/5/30 18:33
     * @param personIds
     *            人员id集合
     * @return java.util.List<com.zkteco.zkbiosecurity.att.model.AttPerson>
     */
    List<AttPerson> findByPersonIdIn(Collection<String> personIds);

    /**
     * 根据部门ID集合查询
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/5/30 18:33
     * @param deptIds
     *            部门id集合
     * @return java.util.List<com.zkteco.zkbiosecurity.att.model.AttPerson>
     */
    List<AttPerson> findByDeptIdIn(Collection<String> deptIds);

    /**
     * 根据人员pin 获取对应人事对象
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/7/18 14:27
     * @param personPin
     *            人员编号
     * @return com.zkteco.zkbiosecurity.att.vo.AttPerson
     */
    AttPerson findByPersonPin(String personPin);

    /**
     * 获取人员编号pins
     * 
     * @param personPins
     * @return
     */
    List<AttPerson> findByPersonPinIn(Collection<String> personPins);

    /**
     * 根据personId集合删除attperson
     * 
     * <AUTHOR>
     * @date 2018/7/25 15:27
     * @param personIdList
     */
    void deleteByPersonIdIn(Collection<String> personIdList);

    List<AttPerson> findByDeptId(String deptId);

    @Query(value = "SELECT T.GROUP_ID FROM ATT_PERSON T WHERE T.AUTH_DEPT_ID IN (?1) GROUP BY T.GROUP_ID",
        nativeQuery = true)
    List<String> findByDeptIdGroupByGroupId(Collection<String> deptIds);

    /**
     * 根据人员ID查找对应的设备权限(减少查整个人员对象)
     * 
     * @param personIds
     * @return
     */
    @Query("SELECT new AttPerson(t.personId,t.personPin,t.perDevAuth, t.verifyMode) FROM AttPerson t WHERE t.personId IN (?1)")
    List<AttPerson> findAttPersonByPersonIdIn(Collection<String> personIds);

    @Query(
            value = "SELECT DISTINCT (pp.EMAIL) FROM PERS_PERSON pp LEFT JOIN ATT_AREA_PERSON aap ON aap.PERS_PERSON_ID=pp.ID WHERE aap.AUTH_AREA_ID IN (?1)",
            nativeQuery = true)
    List<String> getEmailsByAreaIdIn(Collection<String> areaIds);

    @Modifying
    @Transactional
    @Query(value = "UPDATE AttPerson SET verifyMode=?1 WHERE personId in (?2)")
    void updateVerifyModeByPersonId(Short verifyMode, Collection<String> persPersonIdList);
}