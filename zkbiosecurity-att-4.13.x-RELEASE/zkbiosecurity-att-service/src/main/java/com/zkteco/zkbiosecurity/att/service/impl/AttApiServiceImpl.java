package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.api.vo.*;
import com.zkteco.zkbiosecurity.att.constants.AttShiftConstant;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 北向API接口实现类型
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 16:27 2021/11/15
 * @version v1.0
 */
@Slf4j
@Service
public class AttApiServiceImpl implements AttApiService {

    @Autowired
    private AttLeaveTypeService attLeaveTypeService;
    @Autowired
    private AttLeaveService attLeaveService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AttSignService attSignService;
    @Autowired
    private AttOvertimeService attOvertimeService;
    @Autowired
    private Att4PersPersonService att4PersPersonService;

    @Override
    public ApiResultMessage getApplyTimeLong(AttApiApplyTimeLongItem attApiApplyTimeLongItem) {

        // 人员、假种是否存在校验
        PersPersonItem persPersonItem = persPersonService.getItemByPin(attApiApplyTimeLongItem.getPersonPin());
        if (persPersonItem == null) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("pers_api_personNotExist"));
        }
        AttLeaveTypeItem attLeaveTypeItem =
            attLeaveTypeService.getItemByLeaveTypeNo(attApiApplyTimeLongItem.getLeaveTypeNo());
        if (attLeaveTypeItem == null) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_leaveTypeNotExist"));
        }

        // 根据类型计算请假时长，和时长单位
        Date startDatetime = DateUtil.stringToDate(attApiApplyTimeLongItem.getStartDatetime());
        Date endDatetime = DateUtil.stringToDate(attApiApplyTimeLongItem.getEndDatetime());
        ZKResultMsg zkResultMsg = attLeaveService.getLeaveLongByType(attApiApplyTimeLongItem.getPersonPin(),
            startDatetime, endDatetime, attApiApplyTimeLongItem.getLeaveTypeNo());
        return ApiResultMessage.successMessage(zkResultMsg.getData());
    }

    @Override
    public ApiResultMessage getLeaveTypeList() {
        List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getLeaveTypeItems();
        JSONArray leaveTypeList = new JSONArray();
        for (AttLeaveTypeItem attLeaveTypeItem : attLeaveTypeItemList) {
            JSONObject leaveType = new JSONObject();
            leaveType.put("leaveTypeNo", attLeaveTypeItem.getLeaveTypeNo());
            leaveType.put("leaveTypeName", attLeaveTypeItem.getLeaveTypeName());
            leaveTypeList.add(leaveType);
        }
        return ApiResultMessage.successMessage(leaveTypeList);
    }

    @Override
    public ApiResultMessage applyLeave(AttApiApplyLeaveItem attApiApplyLeaveItem) {

        // 人员、假种是否存在校验
        PersPersonItem persPersonItem = persPersonService.getItemByPin(attApiApplyLeaveItem.getPersonPin());
        if (persPersonItem == null) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("pers_api_personNotExist"));
        }
        AttLeaveTypeItem attLeaveTypeItem =
            attLeaveTypeService.getItemByLeaveTypeNo(attApiApplyLeaveItem.getLeaveTypeNo());
        if (attLeaveTypeItem == null) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("att_api_leaveTypeNotExist"));
        }

        // 判断是否已经存在其他申请
        Date startDatetime = DateUtil.stringToDate(attApiApplyLeaveItem.getStartDatetime());
        Date endDatetime = DateUtil.stringToDate(attApiApplyLeaveItem.getEndDatetime());
        ZKResultMsg zkResultMsg = attLeaveService.existApply(persPersonItem.getId(), startDatetime, endDatetime);
        if ("fail".equals(zkResultMsg.getRet())) {
            return ApiResultMessage.message(-1, zkResultMsg.getMsg());
        }

        // 对象赋值
        AttLeaveItem attLeaveItem = new AttLeaveItem();
        ModelUtil.copyProperties(attApiApplyLeaveItem, attLeaveItem);
        attLeaveItem.setStartDatetime(startDatetime);
        attLeaveItem.setEndDatetime(endDatetime);
        attLeaveItem.setLeaveTypeId(attLeaveTypeItem.getId());

        try {
            // 保存申请请假
            attLeaveService.saveItem(persPersonItem.getId(), attLeaveItem, "admin");
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                return ApiResultMessage.message(-1, I18nUtil.i18nCode(e.getMessage(), persPersonItem.getName()));
            }
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("common_api_programError"));
        }
        return ApiResultMessage.successMessage();
    }

    @Override
    public ApiResultMessage applySign(AttApiApplySignItem attApiApplySignItem) {

        // 人员是否存在校验
        PersPersonItem persPersonItem = persPersonService.getItemByPin(attApiApplySignItem.getPersonPin());
        if (persPersonItem == null) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("pers_api_personNotExist"));
        }

        // 对象赋值
        Date signDatetime = DateUtil.stringToDate(attApiApplySignItem.getSignDatetime());
        AttSignItem attSignItem = new AttSignItem();
        ModelUtil.copyProperties(attApiApplySignItem, attSignItem);
        attSignItem.setSignDatetime(signDatetime);
        try {
            // 保存申请补签
            attSignService.saveItem(attSignItem, persPersonItem.getId(), "admin");
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                String message = I18nUtil.i18nCode(e.getMessage());
                message = message.replace("{0}", "");
                return ApiResultMessage.message(-1, message);
            }
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("common_api_programError"));
        }
        return ApiResultMessage.successMessage();
    }

    @Override
    public ApiResultMessage applyOvertime(AttApiApplyOvertimeItem attApiApplyOvertimeItem) {

        // 人员是否存在校验
        PersPersonItem persPersonItem = persPersonService.getItemByPin(attApiApplyOvertimeItem.getPersonPin());
        if (persPersonItem == null) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("pers_api_personNotExist"));
        }

        // 判断是否已经存在其他申请
        Date startDatetime = DateUtil.stringToDate(attApiApplyOvertimeItem.getStartDatetime());
        Date endDatetime = DateUtil.stringToDate(attApiApplyOvertimeItem.getEndDatetime());
        ZKResultMsg zkResultMsg = attLeaveService.existApply(persPersonItem.getId(), startDatetime, endDatetime);
        if ("fail".equals(zkResultMsg.getRet())) {
            return ApiResultMessage.message(-1, zkResultMsg.getMsg());
        }

        // 对象赋值
        AttOvertimeItem attOvertimeItem = new AttOvertimeItem();
        ModelUtil.copyProperties(attApiApplyOvertimeItem, attOvertimeItem);
        attOvertimeItem.setStartDatetime(startDatetime);
        attOvertimeItem.setEndDatetime(endDatetime);
        attOvertimeItem.setOvertimeSign(AttShiftConstant.OvertimeSign.NORMAL);
        attOvertimeItem.setOvertimeLong(AttDateUtils.getMinuteDiff(startDatetime, endDatetime));

        try {
            // 保存加班申请
            attOvertimeService.saveItem(attOvertimeItem, persPersonItem.getId(), "admin");
        } catch (Exception e) {
            if (e instanceof ZKBusinessException) {
                String message = I18nUtil.i18nCode(e.getMessage());
                message = message.replace("{0}", "");
                return ApiResultMessage.message(-1, message);
            }
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("common_api_programError"));
        }
        return ApiResultMessage.successMessage();
    }

    @Override
    public ApiResultMessage uploadLeaveImage(AttApiApplyLeaveImageItem attApiApplyLeaveImageItem) {
        String fileName = System.currentTimeMillis() + ".jpg";
        String filePath = FileUtils.createUploadFileRootPath(ConstUtil.SYSTEM_MODULE_ATT, "leave/image");
        try {
            FileUtils.saveFile(filePath, fileName, attApiApplyLeaveImageItem.getBase64Image(), false);
            FileEncryptUtil.encryptFileByPath(filePath + fileName);
        } catch (Exception e) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("common_api_programError"));
        }
        return ApiResultMessage.successMessage("/" + filePath + fileName);
    }

    @Override
    public ApiResultMessage editAttPerson(AttApiPersonItem attApiPersonItem) {

        // 人员是否存在校验
        PersPersonItem persPersonItem = persPersonService.getItemByPin(attApiPersonItem.getPersonPin());
        if (persPersonItem == null) {
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("pers_api_personNotExist"));
        }

        try {
            Att4PersPersonItem att4PersPersonItem = new Att4PersPersonItem();
            ModelUtil.copyProperties(attApiPersonItem, att4PersPersonItem);
            att4PersPersonItem.setPersonId(persPersonItem.getId());
            att4PersPersonItem.setUpdateAttSet(false);
            att4PersPersonService.editAttPerson(att4PersPersonItem);
        } catch (Exception e) {
            log.error("editAttPerson error", e);
            return ApiResultMessage.message(-1, I18nUtil.i18nCode("common_api_programError"));
        }
        return ApiResultMessage.successMessage();
    }
}
