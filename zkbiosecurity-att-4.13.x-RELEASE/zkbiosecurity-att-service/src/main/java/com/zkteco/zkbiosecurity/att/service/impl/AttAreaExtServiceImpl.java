package com.zkteco.zkbiosecurity.att.service.impl;

import com.zkteco.zkbiosecurity.att.service.AttDeviceService;
import com.zkteco.zkbiosecurity.att.vo.AttDeviceItem;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.att.dao.AttAreaPersonDao;
import com.zkteco.zkbiosecurity.att.dao.AttDeviceDao;
import com.zkteco.zkbiosecurity.auth.service.AuthArea4OtherService;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

import java.util.List;

/**
 * 判断区域是否在考勤模块使用
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/3/12 9:31
 * @since 1.0.0
 */
@Service
public class AttAreaExtServiceImpl implements AuthArea4OtherService {

    @Autowired
    private AttDeviceDao attDeviceDao;
    @Autowired
    private AttAreaPersonDao attAreaPersonDao;
    @Autowired
    private AttDeviceService attDeviceService;

    @Override
    public void checkAreaIsUsed(String areaId) {
        Long count = attDeviceDao.countByAreaId(areaId);
        if (count > 0) {
            throw ZKBusinessException
                .warnException(I18nUtil.i18nCode("base_area_isUsed", I18nUtil.i18nCode("att_module")));
        }
        count = attAreaPersonDao.countByAreaId(areaId);
        if (count > 0) {
            throw ZKBusinessException
                .warnException(I18nUtil.i18nCode("base_area_isUsed", I18nUtil.i18nCode("att_module")));
        }
    }

    @Override
    public void editAuthArea(AuthAreaItem authAreaItem) {
        if (authAreaItem != null && StringUtils.isNotBlank(authAreaItem.getId())) {
            List<AttDeviceItem> attDeviceItemList = attDeviceService.getByAreaId(authAreaItem.getId());
            if (attDeviceItemList != null && attDeviceItemList.size() > 0) {
                for (AttDeviceItem attDeviceItem : attDeviceItemList) {
                    attDeviceItem.setAuthAreaName(authAreaItem.getName());
                    attDeviceService.updateCacheDeviceInfo(attDeviceItem);
                }
            }
        }
    }
}
