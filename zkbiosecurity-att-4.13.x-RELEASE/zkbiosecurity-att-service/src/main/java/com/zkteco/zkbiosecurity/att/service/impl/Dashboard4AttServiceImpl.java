package com.zkteco.zkbiosecurity.att.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.att.service.AttDeviceService;
import com.zkteco.zkbiosecurity.att.vo.AttDashboardStatisticalQueryItem;
import com.zkteco.zkbiosecurity.pers.service.PersParamsService;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.att.constants.AttCommonSchConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.*;
import com.zkteco.zkbiosecurity.att.model.AttDevice;
import com.zkteco.zkbiosecurity.att.model.AttPerson;
import com.zkteco.zkbiosecurity.att.model.AttTempSch;
import com.zkteco.zkbiosecurity.att.service.AttCycleSchService;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttCycleSchItem;
import com.zkteco.zkbiosecurity.att.vo.AttDayDetailReportItem;
import com.zkteco.zkbiosecurity.att.vo.AttMonthStatisticalReportItem;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.dashboard.service.Dashboard4AttService;
import com.zkteco.zkbiosecurity.dashboard.vo.*;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

/**
 * 首页面板统计
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 15:45
 * @since 1.0.0
 */
@Service
public class Dashboard4AttServiceImpl implements Dashboard4AttService {

    @Autowired
    private AttDeviceDao attDeviceDao;
    @Autowired
    private AttRecordDao attRecordDao;
    @Autowired
    private AttTransactionDao attTransactionDao;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AttTempSchDao attTempSchDao;
    @Autowired
    private AttPersonDao attPersonDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttCycleSchService attCycleSchService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AttDeviceService attDeviceService;
    @Autowired
    private PersParamsService persParamsService;

    @Override
    public long getDeviceCount() {
        return attDeviceDao.count();
    }

    @Override
    public List<Dashboard4AttWorkaholicItem> getAttWorkaholicData(String timeType) {
        Map<String, String> attRuleDataMap = baseSysParamService.getParamsByModule("att");
        Date nowDate = new Date();
        Date startMonthDate = null;
        Date endMonthDate = null;

        // 昨日员工工作时长排名
        if ("day".equals(timeType)) {
            Date date = DateUtil.addDay(new Date(), -1);
            startMonthDate = DateUtil.getDayBeginTime(date);
            endMonthDate = DateUtil.getDayEndTime(date);
        }

        // 上周员工工作时长排名
        if ("week".equals(timeType)) {
            // 上周一的时间
            startMonthDate = AttDateUtils.getLastMonDayOfDay(nowDate);
            // 上周日的时间
            endMonthDate = AttDateUtils.getLastSunDayOfDay(nowDate);
        }
        // 上月员工工作时长排名
        if ("month".equals(timeType)) {
            int month = DateUtil.getMonth(nowDate) + 1;
            int year = DateUtil.getYear(nowDate);
            // 当前月份为一月 月份设为12月 年分-1
            if (month == 1) {
                month = 12;
                year -= 1;
            } else {
                month -= 1;
            }
            // 上个月第一天的时间
            startMonthDate = AttDateUtils.getFirstDayOfMonth(year, month);
            // 上个月最后一天的时间
            endMonthDate = AttDateUtils.getLastDayOfMonth(year, month);
        }
        return getAttWorkaholicWeekAndMonthData(attRuleDataMap, startMonthDate, endMonthDate);
    }

    private List<Dashboard4AttWorkaholicItem> getAttWorkaholicWeekAndMonthData(Map<String, String> attRuleDataMap,
        Date startMonthDate, Date endMonthDate) {
        List<Dashboard4AttWorkaholicItem> list = new ArrayList<>();
        AttDashboardStatisticalQueryItem condition = new AttDashboardStatisticalQueryItem();
        // 按实际出勤时长降序
        condition.setSortName("actualHour");
        condition.setSortOrder("desc");
        // 时长大于0
        condition.setActualMinuteGe(0);
        condition.setMonthStart(startMonthDate);
        condition.setMonthEnd(endMonthDate);
        List<AttDashboardStatisticalQueryItem> attMonthStatisticalReportItems = attRecordDao
            .getItemsDataBySql(AttDashboardStatisticalQueryItem.class, SQLUtil.getSqlByItem(condition), 0, 4, true);

        Collection<String> personPinList =
                CollectionUtil.getPropertyList(attMonthStatisticalReportItems, AttDashboardStatisticalQueryItem::getPin, "-1");
        List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(personPinList);
        Map<String, PersPersonItem> persPersonItemMap =
                CollectionUtil.listToKeyMap(persPersonItemList, PersPersonItem::getPin);

        for (AttDashboardStatisticalQueryItem item : attMonthStatisticalReportItems) {
            if (item.getActualHour() != null && Double.parseDouble(item.getActualHour()) > 0) {

                if (persPersonItemMap.containsKey(item.getPin())) {
                    PersPersonItem persPersonItem = persPersonItemMap.get(item.getPin());
                    item.setPin(item.getPin());
                    item.setName(persPersonItem.getName());
                    item.setLastName(persPersonItem.getLastName());
                }

                Dashboard4AttWorkaholicItem workaholicItem = new Dashboard4AttWorkaholicItem();
                workaholicItem.setName((item.getName() == null ? "" : item.getName()) + "(" + item.getPin() + ")");
                String actualHour = StringUtils.isBlank(item.getActualHour()) ? "0" : item.getActualHour();
                workaholicItem.setHour(attParamService.minutesToHourFormat(new BigDecimal(actualHour)));
                list.add(workaholicItem);
            }
        }
        return list;
    }

    /**
     * 本日分段考勤 统计今日各时间段打卡数分布 number字段后面不带百分比 时间段（time字段） "00:00~08:00", "08:00~12:00", "12:00~14:00", "14:00~18:00",
     * "18:00~23:59"
     * 
     * <AUTHOR>
     * @Date 2018/12/26 14:30
     * @param
     * @return
     */
    @Override
    public Map<String, String> getAttTodayCountData() {
        Calendar calendar = Calendar.getInstance();
        String date = AttDateUtils.dateToStrAsShort(calendar.getTime());
        // 当天打卡总次数
        int totalNumber = attTransactionDao.countByAttDate(date);
        // 当天各时间段打卡次数
        List<Object[]> countData = attTransactionDao.getAttTodayCountData(date);
        Map<String, String> timeSlotOneMap = new LinkedHashMap<String, String>();
        timeSlotOneMap.put("00:00~08:00", "0");
        timeSlotOneMap.put("08:00~12:00", "0");
        timeSlotOneMap.put("12:00~14:00", "0");
        timeSlotOneMap.put("14:00~18:00", "0");
        timeSlotOneMap.put("18:00~23:59", "0");
        for (Object[] objArray : countData) {
            if ("00:00~08:00".equals(objArray[0] + "")) {
                timeSlotOneMap.put("00:00~08:00",
                    totalNumber == 0 ? "0" : Math.round(Integer.parseInt(objArray[1] + "") * 100.0 / totalNumber) + "");
            } else if ("08:00~12:00".equals(objArray[0] + "")) {
                timeSlotOneMap.put("08:00~12:00",
                    totalNumber == 0 ? "0" : Math.round(Integer.parseInt(objArray[1] + "") * 100.0 / totalNumber) + "");
            } else if ("12:00~14:00".equals(objArray[0] + "")) {
                timeSlotOneMap.put("12:00~14:00",
                    totalNumber == 0 ? "0" : Math.round(Integer.parseInt(objArray[1] + "") * 100.0 / totalNumber) + "");
            } else if ("14:00~18:00".equals(objArray[0] + "")) {
                timeSlotOneMap.put("14:00~18:00",
                    totalNumber == 0 ? "0" : Math.round(Integer.parseInt(objArray[1] + "") * 100.0 / totalNumber) + "");
            } else if ("18:00~23:59".equals(objArray[0] + "")) {
                timeSlotOneMap.put("18:00~23:59",
                    totalNumber == 0 ? "0" : Math.round(Integer.parseInt(objArray[1] + "") * 100.0 / totalNumber) + "");
            }
        }
        return timeSlotOneMap;
    }

    /**
     * 异常统计(本月) 迟到 早退 请假 旷工 外出 出差人数
     * 
     * <AUTHOR>
     * @Date 2018/12/26 14:30
     * @param
     * @return
     */
    @Override
    public List<Dashboard4AttExceptionItem> getAttExceptionCountData() {
        int month = DateUtil.getMonth(new Date()) + 1;
        int year = DateUtil.getYear(new Date());
        // 本月起止时间
        Date monthStart = DateUtil.stringToDate(year + "-" + month + "-01 00:00:00");
        Date monthEnd = DateUtil.getDayEndTime(AttDateUtils.getMonthOfLastDay(monthStart));
        List<Dashboard4AttExceptionItem> list = new ArrayList<Dashboard4AttExceptionItem>();
        // 迟到
        Dashboard4AttExceptionItem item1 = new Dashboard4AttExceptionItem();
        item1.setExceptionName(I18nUtil.i18nCode("att_common_late"));
        item1.setNumber(attRecordDao.countByAttDateAndLateMinuteTotal(monthStart, monthEnd, 0).size() + "");
        // 早退
        Dashboard4AttExceptionItem item2 = new Dashboard4AttExceptionItem();
        item2.setExceptionName(I18nUtil.i18nCode("att_common_early"));
        item2.setNumber(attRecordDao.countByAttDateAndEarlyMinuteTotal(monthStart, monthEnd, 0).size() + "");
        // 请假
        Dashboard4AttExceptionItem item3 = new Dashboard4AttExceptionItem();
        item3.setExceptionName(I18nUtil.i18nCode("att_common_leave"));
        item3.setNumber(attRecordDao.countByAttDateAndLeaveMinute(monthStart, monthEnd, 0).size() + "");
        // 旷工
        Dashboard4AttExceptionItem item4 = new Dashboard4AttExceptionItem();
        item4.setExceptionName(I18nUtil.i18nCode("att_common_absent"));
        item4.setNumber(attRecordDao.countByAttDateAndAbsentMinute(monthStart, monthEnd, 0).size() + "");
        // 外出
        Dashboard4AttExceptionItem item5 = new Dashboard4AttExceptionItem();
        item5.setExceptionName(I18nUtil.i18nCode("att_common_out"));
        item5.setNumber(attRecordDao.countByAttDateAndOutMinute(monthStart, monthEnd, 0).size() + "");
        // 出差
        Dashboard4AttExceptionItem item6 = new Dashboard4AttExceptionItem();
        item6.setExceptionName(I18nUtil.i18nCode("att_common_trip"));
        item6.setNumber(attRecordDao.countByAttDateAndTripMinute(monthStart, monthEnd, 0).size() + "");
        list.add(item1);
        list.add(item2);
        list.add(item3);
        list.add(item4);
        list.add(item5);
        list.add(item6);
        return list;
    }

    /**
     * 统计今日有排班人员的出勤情况
     * 
     * <AUTHOR>
     * @Date 2019/1/10 19:32
     * @param
     * @return
     */
    @Override
    public Dashboard4AttTodayItem getAttTodayData() {
        Dashboard4AttTodayItem item = new Dashboard4AttTodayItem();
        // 今日开始时间
        Date startDate = DateUtil.getTodayBeginTime();
        // 今日结束时间
        Date endDate = DateUtil.getTodayEndTime();
        // 分组排班人员pin号集合
        Collection<String> personPinGroupList = getAttGroupSchPersonPins(startDate, endDate);
        // 部门排班人员pin号集合
        Collection<String> personPinDeptList = getAttDeptSchPersonPins(startDate, endDate);
        // 人员临时排班pin集合
        Collection<String> attTempPersonPinList = getAttPersonSchPins(startDate, endDate);
        // 汇总分组排班、部门排班、人员排班的人员pin号
        List<String> personPinsList = new ArrayList<>();
        if (!CollectionUtil.isEmpty(personPinGroupList)) {
            personPinsList.addAll(personPinGroupList);
        }
        if (!CollectionUtil.isEmpty(personPinDeptList)) {
            personPinsList.addAll(personPinDeptList);
        }
        if (!CollectionUtil.isEmpty(attTempPersonPinList)) {
            personPinsList.addAll(attTempPersonPinList);
        }
        // 今日排班人员pin号集合 去重
        List<String> collect = personPinsList.stream().distinct().collect(Collectors.toList());
        // 今日排班总人数
        long perpsonNumber = collect.size();
        // 今日有排班实到人数
        long actualNumber = 0;
        if (!CollectionUtil.isEmpty(collect)) {
            List<List<String>> splitPersonPinsList = CollectionUtil.split(collect, CollectionUtil.splitSize);
            for (List<String> personPins : splitPersonPinsList) {
                actualNumber +=
                    attTransactionDao.countTransactionBetweenDateAndByPersPin(personPins, startDate, endDate);;
            }
        }
        if (actualNumber > perpsonNumber) {
            actualNumber = perpsonNumber;
        }
        item.setPerpsonNumber(perpsonNumber + "");
        item.setActualNumber(actualNumber + "");
        item.setNotArrivedNumber((perpsonNumber - actualNumber) + "");
        return item;
    }

    @Override
    public Dashboard4AttDeviceItem getAttDeviceData() {
        Dashboard4AttDeviceItem item = new Dashboard4AttDeviceItem();
        List<AttDevice> list = attDeviceDao.findAll();
        int totalCount = 0;
        int onlineCount = 0;
        int offlineCount = 0;
        if (list != null) {
            totalCount = list.size();
            for (AttDevice attDevice : list) {
                boolean isOnline = attDeviceService.isOnline(attDevice.getDevSn());
                if (isOnline) {
                    onlineCount ++;
                } else {
                    offlineCount ++;
                }
            }
        }
        item.setTotalCount(totalCount);
        item.setOnlineCount(onlineCount);
        item.setOfflineCount(offlineCount);
        return item;
    }

    @Override
    public List<Dashboard4AttAttendanceItem> getAttDeptAttendanceRateData(String timeType) {

        // 根据条件本月或者上月，获取统计的起止时间
        List<Dashboard4AttAttendanceItem> list = new ArrayList<>();
        Date firstDay = AttDateUtils.getMinOfDay(AttDateUtils.getMonthOfFirstDay(new Date()));
        Date lastDay = AttDateUtils.getMaxOfDay(AttDateUtils.getMonthOfLastDay(new Date()));
        if ("lastMonth".equals(timeType)) {
            firstDay = AttDateUtils.getMinOfDay(AttDateUtils.getLastMonthOfFirstDay(new Date()));
            lastDay = AttDateUtils.getMaxOfDay(AttDateUtils.getLastMonthOfLastDay(new Date()));
        }

        // 根据部门分组，获取有打卡的总人数
        List<Object[]> haveCardValidCountObjects = attRecordDao.getHaveCardValidCountGroupByDept(firstDay, lastDay);
        if (haveCardValidCountObjects != null && haveCardValidCountObjects.size() > 0) {
            Map<String, String> DeptIdAndHaveCardValidCountMap = new HashedMap();
            for (Object[] Objects : haveCardValidCountObjects) {
                DeptIdAndHaveCardValidCountMap.put(Objects[0] + "", Objects[1] + "");
            }

            // 根据部门分组，获取有排班的总人数
            List<Object[]> schCountObjects = attRecordDao.getSchCountGroupByDept(firstDay, lastDay);
            Map<String, String> deptIdAndSchCountMap = new HashedMap();
            for (Object[] Objects : schCountObjects) {
                deptIdAndSchCountMap.put(Objects[0] + "", Objects[1] + "");
            }

            // 根据有打卡的总人数和有排班的总人数统计出勤率
            for (Map.Entry<String, String> entry : DeptIdAndHaveCardValidCountMap.entrySet()) {
                String deptId = entry.getKey();
                String cardValidCount = entry.getValue();
                if (deptIdAndSchCountMap.containsKey(deptId)) {
                    String schCount = deptIdAndSchCountMap.get(deptId);
                    String percentage =
                        new BigDecimal(cardValidCount).divide(new BigDecimal(schCount), 4, RoundingMode.HALF_UP)
                            .multiply(BigDecimal.valueOf(100)).setScale(2) + "";
                    Dashboard4AttAttendanceItem item = new Dashboard4AttAttendanceItem();
                    item.setDeptName(deptId);
                    item.setPercentage(percentage);
                    list.add(item);
                }
            }

            // 排序取前5条
            list.sort(
                (o1, o2) -> Double.parseDouble(o1.getPercentage()) > Double.parseDouble(o2.getPercentage()) ? -1 : 1);
            if (list.size() > 5) {
                list = list.subList(0, 5);
            }

            // 赋值部门名称
            String deptIds = CollectionUtil.getPropertys(list, Dashboard4AttAttendanceItem::getDeptName);
            List<AuthDepartmentItem> authDepartmentItemList = authDepartmentService.getItemsByIds(deptIds);
            Map<String, AuthDepartmentItem> deptIdAndNameMap =
                CollectionUtil.listToKeyMap(authDepartmentItemList, AuthDepartmentItem::getId);
            for (Dashboard4AttAttendanceItem item : list) {
                if (deptIdAndNameMap.containsKey(item.getDeptName())) {
                    item.setDeptName(deptIdAndNameMap.get(item.getDeptName()).getName());
                }
            }

            return list;
        }
        return list;
    }

    @Override
    public List<Dashboard4AttAbnormalItem> getAttDeptAbsentRateChartData(String timeType) {
        // 根据条件本月或者上月，获取统计的起止时间
        List<Dashboard4AttAbnormalItem> list = new ArrayList<>();
        Date firstDay = AttDateUtils.getMinOfDay(AttDateUtils.getMonthOfFirstDay(new Date()));
        Date lastDay = AttDateUtils.getMaxOfDay(AttDateUtils.getMonthOfLastDay(new Date()));
        if ("lastMonth".equals(timeType)) {
            firstDay = AttDateUtils.getMinOfDay(AttDateUtils.getLastMonthOfFirstDay(new Date()));
            lastDay = AttDateUtils.getMaxOfDay(AttDateUtils.getLastMonthOfLastDay(new Date()));
        }

        // 根据部门分组，获取没有有打卡的总人数
        List<Object[]> noCardValidCountObjects = attRecordDao.getNoCardValidCountGroupByDept(firstDay, lastDay);
        if (noCardValidCountObjects != null && noCardValidCountObjects.size() > 0) {
            Map<String, String> DeptIdAndNoCardValidCountMap = new HashedMap();
            for (Object[] Objects : noCardValidCountObjects) {
                DeptIdAndNoCardValidCountMap.put(Objects[0] + "", Objects[1] + "");
            }

            // 根据部门分组，获取有排班的总人数
            List<Object[]> schCountObjects = attRecordDao.getSchCountGroupByDept(firstDay, lastDay);
            Map<String, String> deptIdAndSchCountMap = new HashedMap();
            for (Object[] Objects : schCountObjects) {
                deptIdAndSchCountMap.put(Objects[0] + "", Objects[1] + "");
            }

            // 根据有打卡的总人数和有排班的总人数统计出勤率
            for (Map.Entry<String, String> entry : DeptIdAndNoCardValidCountMap.entrySet()) {
                String deptId = entry.getKey();
                String noCardValidCount = entry.getValue();
                if (deptIdAndSchCountMap.containsKey(deptId)) {
                    String schCount = deptIdAndSchCountMap.get(deptId);
                    String percentage =
                            new BigDecimal(noCardValidCount).divide(new BigDecimal(schCount), 4, RoundingMode.HALF_UP)
                                    .multiply(BigDecimal.valueOf(100)).setScale(2) + "";
                    Dashboard4AttAbnormalItem item = new Dashboard4AttAbnormalItem();
                    item.setDeptName(deptId);
                    item.setPercentage(percentage);
                    list.add(item);
                }
            }

            // 排序取前5条
            list.sort(
                    (o1, o2) -> Double.parseDouble(o1.getPercentage()) > Double.parseDouble(o2.getPercentage()) ? -1 : 1);
            if (list.size() > 5) {
                list = list.subList(0, 5);
            }

            // 赋值部门名称
            String deptIds = CollectionUtil.getPropertys(list, Dashboard4AttAbnormalItem::getDeptName);
            List<AuthDepartmentItem> authDepartmentItemList = authDepartmentService.getItemsByIds(deptIds);
            Map<String, AuthDepartmentItem> deptIdAndNameMap =
                    CollectionUtil.listToKeyMap(authDepartmentItemList, AuthDepartmentItem::getId);
            for (Dashboard4AttAbnormalItem item : list) {
                if (deptIdAndNameMap.containsKey(item.getDeptName())) {
                    item.setDeptName(deptIdAndNameMap.get(item.getDeptName()).getName());
                }
            }

            return list;
        }
        return list;
    }

    @Override
    public List<Dashboard4AttRecordTrendItem> getAttRecordTrendChartData() {

        Date firstDay = AttDateUtils.getMonthOfFirstDay(new Date());
        Date lastDay = AttDateUtils.getMonthOfLastDay(new Date());
        String startAttDate = AttDateUtils.dateToStrAsShort(firstDay);
        String endAttDate = AttDateUtils.dateToStrAsShort(lastDay);

        Long totalCount = attTransactionDao.findCountByAttDate(startAttDate, endAttDate);
        List<Object[]> listObject = attTransactionDao.findCountAndAttDateGroupByAttDate(startAttDate, endAttDate);
        Map<String, String> attDateCountMap = new HashedMap();
        if (listObject!=null && listObject.size()>0) {
            for (Object[] Objects: listObject) {
                attDateCountMap.put(Objects[1] + "", Objects[0] + "");
            }
        }

        List<Dashboard4AttRecordTrendItem> list = new ArrayList<>();
        List<String> dateList = AttDateUtils.getBetweenDate(startAttDate, endAttDate);
        for (String date: dateList) {
            Dashboard4AttRecordTrendItem item = new Dashboard4AttRecordTrendItem();
            item.setTime(date);
            if (attDateCountMap.containsKey(date)) {
                String countStr = attDateCountMap.get(date);
                item.setPercentage(
                    new BigDecimal(countStr).divide(BigDecimal.valueOf(totalCount), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)) + "");
            } else {
                item.setPercentage("0");
            }
            list.add(item);
        }

        return list;
    }

    /**
     * 获取时间段内人员排班(含人员临时排班)的人员pin集合
     * 
     * <AUTHOR>
     * @Date 2019/1/10 19:37
     * @param
     * @return
     */
    private Collection<String> getAttPersonSchPins(Date startDate, Date endDate) {

        Collection<String> personPins = new HashSet<>();

        List<AttCycleSchItem> attCycleSchItemList =
            attCycleSchService.getCycleSchList(AttCommonSchConstant.SCH_TYPE_PERSON, startDate, endDate);
        Collection<String> cycleSchPersonPins = null;
        if (!CollectionUtil.isEmpty(attCycleSchItemList)) {
            cycleSchPersonPins = CollectionUtil.getPropertyList(attCycleSchItemList, AttCycleSchItem::getPersonPin,
                AttConstant.COMM_DEF_VALUE);
            personPins.addAll(cycleSchPersonPins);
        }

        // 人员临时排班
        List<AttTempSch> personSchAttTemp = attTempSchDao
            .findByTempTypeAndStartDateAndEndDate(AttCommonSchConstant.SCH_TYPE_PERSON, startDate, endDate);
        // 人员排班pin号集合
        Collection<String> attTempPersonPinList = null;
        if (!CollectionUtil.isEmpty(personSchAttTemp)) {
            Collection<String> attTempPersonIdList =
                CollectionUtil.getPropertyList(personSchAttTemp, AttTempSch::getPersonId, AttConstant.COMM_DEF_VALUE);
            if (!CollectionUtil.isEmpty(attTempPersonIdList)) {
                PersPersonItem persPersonItem = new PersPersonItem();
                persPersonItem.setInId(StringUtils.join(attTempPersonIdList, ","));
                List<PersPersonItem> persPersonItemList = persPersonService.getByCondition(persPersonItem);
                attTempPersonPinList = CollectionUtil.getPropertyList(persPersonItemList, PersPersonItem::getPin,
                    AttConstant.COMM_DEF_VALUE);
                personPins.addAll(attTempPersonPinList);
            }
        }
        return personPins;
    }

    /**
     * 获取时间段内分组排班(含分组临时排班)的人员pin集合
     * 
     * <AUTHOR>
     * @Date 2019/1/10 19:29
     * @param startDate
     * @param endDate
     * @return
     */
    private Collection<String> getAttGroupSchPersonPins(Date startDate, Date endDate) {
        // 分组临时排班
        List<AttTempSch> groupSchAttTemp =
            attTempSchDao.findByTempTypeAndStartDateAndEndDate(AttCommonSchConstant.SCH_TYPE_GROUP, startDate, endDate);
        Collection<String> attTempGroupIdList = null;
        if (!CollectionUtil.isEmpty(groupSchAttTemp)) {
            attTempGroupIdList =
                CollectionUtil.getPropertyList(groupSchAttTemp, AttTempSch::getGroupId, AttConstant.COMM_DEF_VALUE);
        }
        // 分组排班
        List<AttCycleSchItem> attCycleSchItemList =
            attCycleSchService.getCycleSchList(AttCommonSchConstant.SCH_TYPE_GROUP, startDate, endDate);
        Collection<String> attGroupSchGroupIdList = null;
        if (!CollectionUtil.isEmpty(attCycleSchItemList)) {
            attGroupSchGroupIdList = CollectionUtil.getPropertyList(attCycleSchItemList, AttCycleSchItem::getGroupId,
                AttConstant.COMM_DEF_VALUE);
        }
        // 汇总排班groupId
        Collection<String> groupIdList = new ArrayList<>();
        if (!CollectionUtil.isEmpty(attTempGroupIdList)) {
            groupIdList.addAll(attTempGroupIdList);
        }
        if (!CollectionUtil.isEmpty(attGroupSchGroupIdList)) {
            groupIdList.addAll(attGroupSchGroupIdList);
        }
        List<AttPerson> attPersonList = null;
        if (!CollectionUtil.isEmpty(groupIdList)) {
            attPersonList = attPersonDao.findByGroupIdIn(groupIdList);
        }
        // 排班人员pin号集合
        Collection<String> personPinGroupList = null;
        if (!CollectionUtil.isEmpty(attPersonList)) {
            personPinGroupList =
                CollectionUtil.getPropertyList(attPersonList, AttPerson::getPersonPin, AttConstant.COMM_DEF_VALUE);
        }
        return personPinGroupList;
    }

    /**
     * 获取时间段内部门排班(含部门临时排班)的人员pin集合
     * 
     * <AUTHOR>
     * @Date 2019/1/10 19:28
     * @param startDate
     *            开始时间
     * @param endDate
     *            结束时间
     * @return
     */
    private Collection<String> getAttDeptSchPersonPins(Date startDate, Date endDate) {
        // 部门临时排班的部门id集合
        List<AttTempSch> deptSchAttTemp =
            attTempSchDao.findByTempTypeAndStartDateAndEndDate(AttCommonSchConstant.SCH_TYPE_DEPT, startDate, endDate);
        Collection<String> attTempDeptIdList = null;
        if (!CollectionUtil.isEmpty(deptSchAttTemp)) {
            attTempDeptIdList =
                CollectionUtil.getPropertyList(deptSchAttTemp, AttTempSch::getDeptId, AttConstant.COMM_DEF_VALUE);
        }
        // 部门排班的部门id集合
        List<AttCycleSchItem> attCycleSchItemList =
            attCycleSchService.getCycleSchList(AttCommonSchConstant.SCH_TYPE_DEPT, startDate, endDate);
        Collection<String> attDeptSchDeptIdList = null;
        if (!CollectionUtil.isEmpty(attCycleSchItemList)) {
            attDeptSchDeptIdList = CollectionUtil.getPropertyList(attCycleSchItemList, AttCycleSchItem::getDeptId,
                AttConstant.COMM_DEF_VALUE);
        }
        // 汇总部门排班和部门临时排班
        List<String> deptIdList = new ArrayList<>();
        if (!CollectionUtil.isEmpty(attTempDeptIdList)) {
            deptIdList.addAll(attTempDeptIdList);
        }
        if (!CollectionUtil.isEmpty(attDeptSchDeptIdList)) {
            deptIdList.addAll(attDeptSchDeptIdList);
        }
        // 获取部门下的人员集合
        Collection<String> personPinDeptList = null;
        if (!CollectionUtil.isEmpty(deptIdList)) {
            PersPersonItem persPersonItem = new PersPersonItem();
            persPersonItem.setInDeptId(StringUtils.join(deptIdList, ","));
            List<PersPersonItem> persPersonItemList = persPersonService.getByCondition(persPersonItem);
            personPinDeptList =
                CollectionUtil.getPropertyList(persPersonItemList, PersPersonItem::getPin, AttConstant.COMM_DEF_VALUE);
        }
        return personPinDeptList;
    }
}
