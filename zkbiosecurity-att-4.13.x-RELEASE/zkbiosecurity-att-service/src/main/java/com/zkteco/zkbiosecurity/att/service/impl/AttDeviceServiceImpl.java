package com.zkteco.zkbiosecurity.att.service.impl;

import java.io.File;
import java.util.*;
import java.util.concurrent.*;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.adms.service.AdmsAuthDeviceService;
import com.zkteco.zkbiosecurity.adms.service.AdmsDevCmdService;
import com.zkteco.zkbiosecurity.adms.service.AdmsDeviceService;
import com.zkteco.zkbiosecurity.adms.service.AdmsParamsService;
import com.zkteco.zkbiosecurity.adms.vo.AdmsAuthDeviceItem;
import com.zkteco.zkbiosecurity.att.cache.AttCacheManager;
import com.zkteco.zkbiosecurity.att.cmd.AttDevCmdManager;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.constants.AttDeviceConstant;
import com.zkteco.zkbiosecurity.att.dao.AttAreaPersonDao;
import com.zkteco.zkbiosecurity.att.dao.AttDeviceDao;
import com.zkteco.zkbiosecurity.att.model.AttDevice;
import com.zkteco.zkbiosecurity.att.operate.AttSetPersonToDevOperate;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthSessionServcie;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.utils.BaseDataUtil;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.license.provider.BaseLicenseProvider;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 考勤设备
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:30
 * @since 1.0.0
 */
@Slf4j
@Service
public class AttDeviceServiceImpl implements AttDeviceService {

    @Autowired
    private AttDeviceDao attDeviceDao;
    @Autowired
    private AttAreaPersonDao attAreaPersonDao;
    @Autowired(required = false)
    private AdmsAuthDeviceService admsAuthDeviceService;
    @Autowired(required = false)
    private AdmsDeviceService admsDeviceService;
    @Autowired(required = false)
    private AdmsDevCmdService admsDevCmdService;
    @Autowired
    private AttDevCmdManager attDevCmdManager;
    @Autowired
    private AttCacheManager attCacheManager;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private AttDeviceOptionService attDeviceOptionService;
    @Autowired
    private AttSetPersonToDevOperate attSetPersonToDevOperate;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private BaseLicenseProvider baseLicenseProvider;
    @Autowired
    private AuthSessionServcie authSessionServcie;
    @Autowired(required = false)
    private AdmsParamsService admsParamsService;
    @Autowired(required = false)
    private AttDevice4OtherService[] attDevice4OtherServices;
    @Autowired
    private AttLicensePointsCheckService attLicensePointsCheckService;
    @Autowired
    private AttAreaPersonService attAreaPersonService;
    @Autowired
    private PersPersonService persPersonService;

    @Override
    public ZKResultMsg saveItem(AttDeviceItem item) {
        ZKResultMsg res = new ZKResultMsg();
        return res;
    }

    @Override
    public boolean checkIsRegDevice(String sn) {
        AttDevice attDevice = attDeviceDao.findByDevSn(sn);
        return attDevice == null ? false : attDevice.getIsRegDevice();
    }

    @Override
    @Transactional
    public void updateEditInfo(String id, String devName, String areaId, String timeZone, Boolean isRegDevice) {
        AttDevice attDevice = attDeviceDao.findById(id).get();
        String oldAreaId = "";
        String oldTimeZone = "";
        boolean oldRegDev = false;
        if (attDevice != null) {
            // 自动添加上来的设备未绑定区域 add by hook.fang 2020-06-29
            oldAreaId = attDevice.getAreaId() == null ? "" : attDevice.getAreaId();
            oldTimeZone = attDevice.getTimeZone();
            oldRegDev = attDevice.getIsRegDevice();
            attDevice.setDevName(devName);
            attDevice.setAreaId(areaId);
            attDevice.setTimeZone(timeZone);
            // 登记机状态变更，要重启设备并更新标记位 add by hook.fang 2020-06-29
            if (oldRegDev != isRegDevice) {
                attDevice.setUpdateFlag(
                    isRegDevice == true ? AttDeviceConstant.ATT_UPDATE_FLAG : AttDeviceConstant.ATT_NON_REGISTERED);
            }
            attDevice.setIsRegDevice(isRegDevice);
            attDeviceDao.save(attDevice);
            // 更新设备缓存
            updateToCacheDeviceInfo(attDevice);
        }

        // 开启登记机，则下发上传人员信息命令
        if (oldRegDev != isRegDevice && isRegDevice) {
            // 需要把时间戳设置为0，在下发check命令
            admsDeviceService.updateAttStamp(attDevice.getDevSn(), "1", "1", "1");
            attDevCmdManager.sendCheck(attDevice);
        }
        // 判断设备的区域是否变动，如果变动则重新同步新区域人员到设备
        if (StringUtils.isNotBlank(areaId) && !oldAreaId.equals(areaId)) {
            if (StringUtils.isNotBlank(oldAreaId)) {
                // 1. 发送清空设备人员信息指令
                attDevCmdManager.clearAttUserInfo(attDevice);
            }
            // 2. 开始同步设备人员信息
            List<String> personIdList = attAreaPersonDao.getPersonIdListByAreaId(attDevice.getAreaId());
            if (!CollectionUtil.isEmpty(personIdList)) {
                CompletableFuture.runAsync(() -> {
                    attSetPersonToDevOperate.sendPersonToDevice(personIdList, Arrays.asList(attDevice));
                });
            }
        }

        // 时区变动或是否登记机变更，则下发重启命令
        if (!oldTimeZone.equals(timeZone) || oldRegDev != isRegDevice) {
            attDevCmdManager.reboot(attDevice);
        }

        // 通知其他模块更新设备
        if (attDevice4OtherServices != null) {
            AttDevice4OtherItem attDevice4OtherItem = new AttDevice4OtherItem();
            attDevice4OtherItem.setId(attDevice.getId()).setSn(attDevice.getDevSn()).setAlias(attDevice.getDevName())
                .setAuthAreaId(attDevice.getAreaId());
            Arrays.stream(attDevice4OtherServices)
                .forEach(attDevice4OtherService -> attDevice4OtherService.editAttDeviceInfo(attDevice4OtherItem));
        }
    }

    @Override
    public List<AttDeviceItem> getByCondition(AttDeviceItem condition) {
        return (List<AttDeviceItem>)attDeviceDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByAuthUserPage(String sessionId, BaseItem condition, int page, int size) {
        AttDeviceItem attDeviceItem = (AttDeviceItem)condition;
        String excuteSql = getDeviceQuerySql(sessionId, attDeviceItem);
        Pager pager;
        if (StringUtils.isNotBlank(excuteSql)) {
            pager = attDeviceDao.getItemsBySql(attDeviceItem.getClass(), excuteSql, page, size);
            if (!CollectionUtil.isEmpty(pager.getData())) {
                buildArea(pager.getData());
            }
        } else {
            pager = new Pager();
        }
        return pager;
    }

    private String getDeviceQuerySql(String sessionId, AttDeviceItem attDeviceItem) {
        String excuteSql = "";
        String devAreaStatus = attDeviceItem.getDeviceAreaStatus() == null ? "0" : attDeviceItem.getDeviceAreaStatus();

        // 1表示查询区域为空的所有设备，不受选择的区域影响
        if ("1".equals(devAreaStatus)) {
            attDeviceItem.setAreaId(null);
            StringBuilder emptyAreaSql = new StringBuilder();
            emptyAreaSql.append(SQLUtil.getSqlByItem(attDeviceItem));
            emptyAreaSql.insert(emptyAreaSql.indexOf("ORDER BY"), " AND t.AUTH_AREA_ID IS NUll ");
            excuteSql = emptyAreaSql.toString();
            return excuteSql;
        }

        // 设备状态不为空时，对区域的判断
        if (StringUtils.isBlank(attDeviceItem.getAreaId())) {
            String deviceAreaStatus =
                attDeviceItem.getDeviceAreaStatus() == null ? "0" : attDeviceItem.getDeviceAreaStatus();
            if (StringUtils.isNotBlank(deviceAreaStatus)) {
                SecuritySubject securitySubject = authSessionServcie.getSecuritySubject(sessionId);
                switch (deviceAreaStatus) {
                    case "0": // 查询全部
                        if (!securitySubject.getIsSuperuser()) {// 非超级管理员既要权限过滤又要查出空区域
                            attDeviceItem
                                .setInAreaId(authAreaService.getAreaIdsByIdAndCodeAndName(sessionId, null, null, null));
                            StringBuilder allDeviceSql = new StringBuilder();
                            allDeviceSql.append(SQLUtil.getSqlByItem(attDeviceItem));
                            allDeviceSql.insert(allDeviceSql.indexOf("ORDER BY"), " OR t.AUTH_AREA_ID IS NUll ");
                            excuteSql = allDeviceSql.toString();
                        } else {
                            attDeviceItem
                                .setInAreaId(authAreaService.getAreaIdsByIdAndCodeAndName(sessionId, null, null, null));
                            excuteSql = SQLUtil.getSqlByItem(attDeviceItem);
                        }
                        break;
                    case "2": // 查询正常区域的
                        if (!securitySubject.getIsSuperuser()) {
                            attDeviceItem
                                .setInAreaId(authAreaService.getAreaIdsByIdAndCodeAndName(sessionId, null, null, null));
                            excuteSql = SQLUtil.getSqlByItem(attDeviceItem);
                        } else {
                            StringBuilder noEmptyAreaSql = new StringBuilder();
                            // 超级管理员需要过滤掉区域为空的
                            noEmptyAreaSql.append(SQLUtil.getSqlByItem(attDeviceItem));
                            noEmptyAreaSql.insert(noEmptyAreaSql.indexOf("ORDER BY"),
                                " AND t.AUTH_AREA_ID IS NOT NUll ");
                            excuteSql = noEmptyAreaSql.toString();
                        }
                        break;
                    default:
                        break;
                }
            }
        } else {
            // 是否包含子区域
            if ("true".equals(attDeviceItem.getIsIncludeLower())) {
                attDeviceItem.setInAreaId(authAreaService.getAreaIdsByIdAndCodeAndName(sessionId,
                    attDeviceItem.getAreaId(), null, attDeviceItem.getAuthAreaName()));
                attDeviceItem.setAreaId(null);
            }
            excuteSql = SQLUtil.getSqlByItem(attDeviceItem);
        }
        return excuteSql;
    }

    @Override
    public Long getAllDeviceCount() {
        return attDeviceDao.count();
    }

    @Override
    public List<AttDeviceCloudItem> getDeviceCloudItems(AttDeviceCloudItem condition, int pageNo, int pageSize) {
        Pager pager =
            attDeviceDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        List<AttDeviceCloudItem> attDeviceCloudItems = (List<AttDeviceCloudItem>)pager.getData();
        String areaIds = CollectionUtil.getPropertys(attDeviceCloudItems, AttDeviceCloudItem::getAreaId);
        List<AuthAreaItem> areaItems = authAreaService.getItemsByIds(areaIds);
        Map<String, AuthAreaItem> areaMap = CollectionUtil.itemListToIdMap(areaItems);
        AuthAreaItem area = null;
        for (AttDeviceCloudItem item : attDeviceCloudItems) {
            area = areaMap.get(item.getAreaId());
            item.setAuthAreaName(area.getName());
            item.setAuthAreaCode(area.getCode());
            item.setParentAreaCode(area.getParentAreaCode());
        }
        return attDeviceCloudItems;
    }

    @Override
    public boolean checkExitIsRegDevice(String id) {
        boolean isRegDevice = false;
        if (StringUtils.isNotBlank(id)) {
            AttDevice attDevice = attDeviceDao.findById(id).orElse(null);
            if (Objects.nonNull(attDevice)) {
                isRegDevice = attDevice.getIsRegDevice();
            }
        }
        return isRegDevice;
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        Pager pager = attDeviceDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        buildArea(pager.getData());
        return pager;
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {

            // 通知其他模块删除设备
            if (attDevice4OtherServices != null) {
                Arrays.stream(attDevice4OtherServices)
                    .forEach(attDevice4OtherService -> attDevice4OtherService.checkAttDeviceIsUsedByDevIds(ids));
            }

            String[] idArray = StringUtils.split(ids, ",");
            AttDevice attDevice = null;
            for (String id : idArray) {
                attDevice = attDeviceDao.findById(id).get();
                // 通信先删除，保证分布式数据一致
                admsDeviceService.delBySn(attDevice.getDevSn());
                // 删除本业务
                attDeviceOptionService.deleteByDevId(id);
                attDeviceDao.deleteById(id);
                // 删除设备缓存
                attCacheManager.delDeviceInfo(attDevice.getDevSn());
            }

            // 更新许可
            attLicensePointsCheckService.update();
        }
        return false;
    }

    @Override
    public AttDeviceItem getItemById(String id) {
        List<AttDeviceItem> items = getByCondition(new AttDeviceItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public boolean vaildName(String devName) {
        AttDevice attDevice = attDeviceDao.findByDevName(devName);
        return attDevice == null;
    }

    @Override
    public boolean vaildSn(String devSn) {
        AttDevice attDevice = attDeviceDao.findByDevSn(devSn);
        return attDevice == null;
    }

    @Override
    public boolean vaildIpAdress(String ipAddress) {
        AttDevice attDevice = attDeviceDao.findByIpAddress(ipAddress);
        return attDevice == null;
    }

    /**
     * 设备启用（暂未加逻辑）
     */
    @Override
    @Transactional
    public ZKResultMsg enable(String ids) {
        List<String> idList = Arrays.asList(StringUtils.split(ids, ","));
        List<AttDevice> attDeviceList = attDeviceDao.findAllById(idList);
        for (AttDevice attDevice : attDeviceList) {
            // 通信启用
            admsDeviceService.enableDevice(attDevice.getDevSn());
            attDevice.setStatus(true);
            attDeviceDao.save(attDevice);
            // 更新设备缓存
            updateToCacheDeviceInfo(attDevice);
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 设备禁用（暂未加逻辑）
     */
    @Override
    @Transactional
    public ZKResultMsg disable(String ids) {
        List<String> idList = Arrays.asList(StringUtils.split(ids, ","));
        // List<AttDevice> attDeviceList = attDeviceDao.findAll(idList);
        List<AttDevice> attDeviceList = attDeviceDao.findAllById(idList);
        for (AttDevice attDevice : attDeviceList) {
            // 通信禁用
            admsDeviceService.disableDevice(attDevice.getDevSn());
            attDevice.setStatus(false);
            attDeviceDao.save(attDevice);
            // 更新设备缓存
            updateToCacheDeviceInfo(attDevice);
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 同步数据到设备（暂未加逻辑）
     */
    @Override
    public ZKResultMsg syncDev(String ids, String isSyncClearData) {
        ZKResultMsg res = new ZKResultMsg();
        List<String> idList = Arrays.asList(StringUtils.split(ids, ","));
        List<AttDevice> attDeviceList = attDeviceDao.findAllById(idList);
        Map<String, List<AttDevice>> attAreaDeviceMap = new HashMap<>();// 把设备按设备区域归类
        for (AttDevice attDevice : attDeviceList) {
            if (attDevice.getStatus())// 判断设备状态是否可用
            {
                // 1. 删除设备redis中命令，再同步
                attDevCmdManager.cleanCmdBySn(attDevice);
                // 2. 发送清空设备人员信息指令
                if (ZKConstant.TRUE.equals(isSyncClearData)) {
                    attDevCmdManager.clearAttUserInfo(attDevice);
                }
                Long personTotal = attAreaPersonDao.countByAreaId(attDevice.getAreaId());
                if (personTotal > 0) {
                    // 把设备按区域id的归类
                    if (attAreaDeviceMap.containsKey(attDevice.getAreaId())) {
                        attAreaDeviceMap.get(attDevice.getAreaId()).add(attDevice);
                    } else {
                        List<AttDevice> areaDeviceList = new ArrayList<>();
                        areaDeviceList.add(attDevice);
                        attAreaDeviceMap.put(attDevice.getAreaId(), areaDeviceList);
                    }
                }
            }
        }
        // 防止页面因为人员过多，加载过久，导致，程序执行超时，使用异步的方式，进行数据下发。
        CompletableFuture.runAsync(() -> {
            // 循环按区域归类后的设备map集合，根据区域id获取人员id集合，并下发人员到设备
            Iterator<Map.Entry<String, List<AttDevice>>> iterator = attAreaDeviceMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, List<AttDevice>> next = iterator.next();
                String areaId = next.getKey();
                List<AttDevice> deviceList = next.getValue();
                List<String> personIdList = attAreaPersonDao.getPersonIdListByAreaId(areaId);
                attSetPersonToDevOperate.sendPersonToDevice(personIdList, deviceList);
            }
        });
        res.setMsg("att_areaPerson_syncToDevSuccess");
        return res;
    }

    /**
     * 清除设备命令
     */
    @Override
    public ZKResultMsg deleteCmd(String ids) {
        // 1.组装发送命令
        List<String> idList = Arrays.asList(StringUtils.split(ids, ","));
        List<AttDevice> attDeviceList = attDeviceDao.findAllById(idList);
        for (AttDevice dev : attDeviceList) {
            admsDeviceService.clearDevCmd(dev.getDevSn());// 清空设备在缓存中存在的命令
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 清除考勤照片
     */
    @Override
    public ZKResultMsg clearAttPic(String ids) {
        // 1.组装发送命令
        List<String> idList = Arrays.asList(StringUtils.split(ids, ","));
        List<AttDevice> attDeviceList = attDeviceDao.findAllById(idList);
        for (AttDevice dev : attDeviceList) {
            attDevCmdManager.cleanPhoto(dev);
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 清除考勤记录
     */
    @Override
    public ZKResultMsg clearAttLog(String ids) {
        // 1.组装发送命令
        List<String> idList = Arrays.asList(StringUtils.split(ids, ","));
        List<AttDevice> attDeviceList = attDeviceDao.findAllById(idList);
        for (AttDevice dev : attDeviceList) {
            attDevCmdManager.cleanLog(dev);
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    public ZKResultMsg clearAttPers(String ids) {
        List<AttDevice> attDeviceList = attDeviceDao.findByIdList(StrUtil.strToList(ids));
        for (AttDevice dev : attDeviceList) {
            attDevCmdManager.clearAttUserInfo(dev);
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 重启设备
     */
    @Override
    public ZKResultMsg reboot(String ids) {
        // 1.组装发送命令
        List<AttDevice> attDevices = attDeviceDao.findByIdList(StrUtil.strToList(ids));
        for (AttDevice dev : attDevices) {
            attDevCmdManager.reboot(dev);
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 获取设备参数
     */
    @Override
    public ZKResultMsg getDevOpt(String ids) {
        // 1.组装发送命令
        List<AttDevice> attDevices = attDeviceDao.findByIdList(StrUtil.strToList(ids));
        for (AttDevice dev : attDevices) {
            attDevCmdManager.sendInfo(dev);
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 考勤数据校对
     */
    @Override
    public ZKResultMsg verify(String ids, String startTime, String endTime) {
        ZKResultMsg zKResultMsg = new ZKResultMsg();
        // 1.组装发送命令
        List<AttDevice> attDevices = attDeviceDao.findByIdList(StrUtil.strToList(ids));
        for (AttDevice dev : attDevices) {
            attDevCmdManager.loadAttLogByTime(dev, startTime, endTime);
        }
        return zKResultMsg;
    }

    /**
     * 获取指定人员信息
     */
    @Override
    public ZKResultMsg getPersonInfo(String ids, String pin) {
        // 切割逗号分离pin号
        String[] pins = pin.split(",");
        Set<String> pinSet = new HashSet<String>();
        for (String p : pins) {
            if (StringUtils.isNotBlank(p)) {
                pinSet.add(p);
            }
        }
        // 组装下发的命令
        List<AttDevice> attDevices = attDeviceDao.findByIdList(StrUtil.strToList(ids));
        for (AttDevice dev : attDevices) {
            for (String p : pinSet) {
                attDevCmdManager.uploadUserInfoByPin(dev, p);
            }
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 重新上传数据
     */
    @Override
    public ZKResultMsg reUpload(String ids, String isUploadAttLog, String isUploadOperLog, String isUploadAttPhoto) {
        ZKResultMsg zKResultMsg = new ZKResultMsg();
        List<AttDevice> attDevices = attDeviceDao.findByIdList(StrUtil.strToList(ids));
        for (AttDevice dev : attDevices) {
            admsDeviceService.updateAttStamp(dev.getDevSn(), isUploadAttLog, isUploadOperLog, isUploadAttPhoto);
            // 发送CHECK指令
            attDevCmdManager.sendCheck(dev);
        }
        return zKResultMsg;
    }

    /**
     * 公共短消息
     */
    @Override
    @Transactional
    public ZKResultMsg addSms(String ids, String startTime, String msg, String min) {
        ZKResultMsg zKResultMsg = new ZKResultMsg();
        List<AttDevice> attDevices = attDeviceDao.findByIdList(StrUtil.strToList(ids));
        BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("att.sms.uid");
        Integer uid = Integer.parseInt(baseSysParamItem.getParamValue()) + 1;
        for (AttDevice dev : attDevices) {
            if (dev.getStatus()) {
                attDevCmdManager.sendSms(dev, msg, "253", uid.toString(), min, startTime);
            }
        }
        baseSysParamService.saveValueByName("att.sms.uid", uid.toString());
        return zKResultMsg;
    }

    @Override
    public List<AttSearchDeviceItem> searchDeviceList() {
        List<AdmsAuthDeviceItem> admsAuthDeviceItems = admsAuthDeviceService.searchDevice(BaseConstants.ATT);
        return ModelUtil.copyListProperties(admsAuthDeviceItems, AttSearchDeviceItem.class);
    }

    @Override
    public Map<String, Collection<String>> getAllIPSn() {
        Map<String, Collection<String>> map = new HashMap<>();
        List<AttDevice> devList = attDeviceDao.findAll();
        map.put("ipAddress", CollectionUtil.getPropertyList(devList, AttDevice::getIpAddress, "-1"));
        map.put("sn", CollectionUtil.getPropertyList(devList, AttDevice::getDevSn, "-1"));
        return map;
    }

    @Override
    @Transactional
    public Map<String, String> addDevice(Map<String, String> optionMap) {
        AttDevice attDevice = attDeviceDao.findByDevSn(optionMap.get("SN"));
        AttDeviceItem attDeviceItem = attCacheManager.getAuthDeviceInfo(optionMap.get("SN"));
        if (attDevice != null) {
            loadSetDevOptions(optionMap, attDevice);
        } else if (attDeviceItem == null) {
            // 未授权
            log.warn("att module need auth sn = {}", optionMap.get("SN"));
            optionMap.put("result", "0");
        } else {
            ZKResultMsg zkResultMsg = attLicensePointsCheckService.check();
            if (zkResultMsg.isSuccess()) {
                attDevice = new AttDevice();
                ModelUtil.copyPropertiesIgnoreNull(attDeviceItem, attDevice);
                attDeviceDao.save(attDevice);
                // 更新许可
                boolean res = attLicensePointsCheckService.update();
                if (res) {
                    // 更新设备缓存
                    updateToCacheDeviceInfo(attDevice);
                    // 首次添加设备发送info
                    attDevCmdManager.sendInfo(attDevice);
                    if (attDevice.getIsRegDevice()) // 勾选了设成登记机，则下发命令上传人员信息。
                    {
                        attDevCmdManager.sendCheck(attDevice);
                    }
                    attDeviceOptionService.updateDeviceOption(attDevice.getDevSn(), optionMap);
                    loadSetDevOptions(optionMap, attDevice);
                } else {
                    log.warn("att license points over");
                    attDeviceDao.delete(attDevice);
                    // 许可点数不足
                    optionMap.put("result", "-1");
                }
            } else {
                // 许可点数不足
                optionMap.put("result", "-1");
            }
        }
        return optionMap;
    }

    /**
     * 加载业务上设置的设备参数信息
     * <p>
     * 需要业务端动态修改的参数才要此配置，否则全都在AdmsDeviceService组装
     * </p>
     * 
     * @param optionMap
     * @param attDevice
     */
    private void loadSetDevOptions(Map<String, String> optionMap, AttDevice attDevice) {
        // 将业务设置的相关参数会设置给通信
        optionMap.put("TransFlag", attDevice.getUpdateFlag());
        optionMap.put("TimeZone", BaseDataUtil.changeTimeZone(attDevice.getTimeZone()));
        optionMap.put("ModuleType", ConstUtil.SYSTEM_MODULE_ATT);
        // 添加成功标记反馈
        optionMap.put("result", "1");
    }

    @Override
    @Transactional
    public ZKResultMsg authDevice(AttDeviceItem item) {
        ZKResultMsg res = new ZKResultMsg();
        if (item.getIsRegDevice()) {
            item.setUpdateFlag(AttDeviceConstant.ATT_UPDATE_FLAG);
        } else {
            item.setUpdateFlag(AttDeviceConstant.ATT_NON_REGISTERED);
        }
        AttDevice attDevice = attDeviceDao.findByDevSn(item.getDevSn());
        if (attDevice != null && attDevice.getStatus()) {
            attCacheManager.putAuthDeviceInfo(item);
            return res;
        }

        res = attLicensePointsCheckService.check();
        if (res.isSuccess()) {
            attCacheManager.putAuthDeviceInfo(item);
            AdmsAuthDeviceItem admsAuthDeviceItem = admsAuthDeviceService.authDevice(item.getDevSn());
            if (!admsAuthDeviceItem.getAuthFlag()) {
                return ZKResultMsg.msg(false);
            }
        }
        return res;
    }

    /**
     * 更新设备主动上传的通信参数统计信息
     *
     * @param attDeviceItem
     */
    @Override
    @Transactional
    public void updateUploadInfo(AttDeviceItem attDeviceItem) {
        AttDevice attDevice = attDeviceDao.findByDevSn(attDeviceItem.getDevSn());
        if (attDevice != null) {
            Map<String, String> optionsMap = new HashMap<>();
            attDevice.setFaceVersion(attDeviceItem.getFaceVersion());
            optionsMap.put("FaceVersion", attDeviceItem.getFaceVersion());

            attDevice.setFpVersion(attDeviceItem.getFpVersion());
            optionsMap.put("FPVersion", attDeviceItem.getFpVersion());

            attDevice.setFwVersion(attDeviceItem.getFwVersion());
            optionsMap.put("FWVersion", attDeviceItem.getFwVersion());

            attDevice.setIpAddress(attDeviceItem.getIpAddress());
            optionsMap.put("IPAddress", attDeviceItem.getIpAddress());

            attDevice.setFaceCount(attDeviceItem.getFaceCount());
            optionsMap.put("FaceCount", attDeviceItem.getFaceCount().toString());

            attDevice.setFpCount(attDeviceItem.getFpCount());
            optionsMap.put("FPCount", attDeviceItem.getFpCount().toString());

            attDevice.setPersonCount(attDeviceItem.getPersonCount());
            optionsMap.put("UserCount", attDeviceItem.getPersonCount().toString());

            attDevice.setRecordCount(attDeviceItem.getRecordCount());
            optionsMap.put("TransactionCount", attDeviceItem.getRecordCount().toString());

            optionsMap.put("AttSupportFunList", attDeviceItem.getAttSupportFunList());

            attDeviceOptionService.updateDeviceOption(attDevice.getDevSn(), optionsMap);
            attDeviceDao.save(attDevice);

            // 更新设备缓存
            updateToCacheDeviceInfo(attDevice);
        }
    }

    private List<?> buildArea(List<?> items) {
        List<AttDeviceItem> list = (List<AttDeviceItem>)items;
        String areaIds = CollectionUtil.getPropertys(list, AttDeviceItem::getAreaId);
        List<AuthAreaItem> areaItems = authAreaService.getItemsByIds(areaIds);
        Map<String, AuthAreaItem> areaMap = CollectionUtil.itemListToIdMap(areaItems);
        AuthAreaItem area = null;
        for (AttDeviceItem item : list) {
            area = areaMap.get(item.getAreaId());
            if (area != null)// 防止对象不存在 --add by hook.fang 2019-03-28
            {
                item.setAuthAreaName(area.getName());
            }

            item.setWaitCmdCount(getWaitCmdCount(item.getDevSn()));
            item.setOnlineStatus(getDevStatus(item.getDevSn()));
        }
        return items;
    }

    @Override
    public Integer getWaitCmdCount(String devSn) {
        return admsDevCmdService.getWaitCmdCount(devSn);
    }

    /**
     * 更新通信模块发送回来的设备参数信息更新
     *
     * @param optionMap
     * @return
     */
    @Override
    @Transactional
    public boolean updateDeviceOptions(Map<String, String> optionMap) {
        AttDevice attDevice = attDeviceDao.findByDevSn(optionMap.get("SN"));
        if (attDevice != null) {
            attDevice.setDevModel(optionMap.get("~DeviceName"));
            attDevice.setFaceCount(MapUtils.getInteger(optionMap, "FaceCount", 0));
            attDevice.setFaceVersion(optionMap.get("FaceVersion"));
            attDevice.setFpCount(MapUtils.getInteger(optionMap, "FPCount", 0));
            attDevice.setFpVersion(optionMap.get("FPVersion"));
            attDevice.setFwVersion(optionMap.get("FWVersion"));
            attDevice.setIpAddress(optionMap.get("IPAddress"));
            attDevice.setRecordCount(MapUtils.getInteger(optionMap, "TransactionCount", 0));
            attDevice.setPersonCount(MapUtils.getInteger(optionMap, "UserCount", 0));
            attDeviceOptionService.updateDeviceOption(attDevice.getDevSn(), optionMap);
            attDeviceDao.save(attDevice);
            // 更新设备缓存
            updateToCacheDeviceInfo(attDevice);
        }
        return true;
    }

    @Override
    public boolean checkDeviceStatus(String ids) {
        List<AttDevice> attDeviceList = attDeviceDao.findByIdList(Arrays.asList(ids.split(",")));
        for (AttDevice attDevice : attDeviceList) {
            if (!attDevice.getStatus() || "0".equals(getDevStatus(attDevice.getDevSn()))) {
                return false;
            }
        }
        return true;
    }

    @Override
    public int getDeviceCount() {
        return attDeviceDao.findEnableCount(true);
    }

    @Override
    public AttDeviceItem getItemBySn(String sn) {
        AttDevice attDevice = attDeviceDao.findByDevSn(sn);
        if (attDevice != null) {
            return ModelUtil.copyPropertiesIgnoreNull(attDevice, new AttDeviceItem());
        }
        return null;
    }

    @Override
    @Transactional
    public void handlerTransfer(List<AttDeviceItem> attDeviceItems) {

        List<List<AttDeviceItem>> splitAttDeviceItemList =
            CollectionUtil.split(attDeviceItems, CollectionUtil.splitSize);
        for (List<AttDeviceItem> attDeviceItemList : splitAttDeviceItemList) {
            Collection<String> devSnList =
                CollectionUtil.getPropertyList(attDeviceItemList, AttDeviceItem::getDevSn, AttConstant.COMM_DEF_VALUE);
            List<AttDevice> attDevices = attDeviceDao.findByDevSnIn(devSnList);
            Map<String, AttDevice> attDeviceItemMap = CollectionUtil.listToKeyMap(attDevices, AttDevice::getDevSn);
            List<AttDevice> attDeviceList = new ArrayList<>();
            for (AttDeviceItem attDeviceItem : attDeviceItemList) {
                String devSn = attDeviceItem.getDevSn();
                // AttDevice attDevice = attDeviceDao.findByDevSn(devSn);
                AttDevice attDevice = attDeviceItemMap.get(devSn);
                if (Objects.isNull(attDevice)) {
                    attDevice = new AttDevice();
                }
                ModelUtil.copyPropertiesIgnoreNullWithProperties(attDeviceItem, attDevice, "id", "timeZone");
                String authAreaCode = attDeviceItem.getAuthAreaCode();
                AuthAreaItem authAreaItem = authAreaService.getItemByCode(authAreaCode);
                attDevice.setAreaId(authAreaItem.getId());

                String timeZone = attDeviceItem.getTimeZone();
                int timeZoneInt = Integer.parseInt(timeZone);
                if (timeZoneInt == 0) {
                    timeZone = "-0000";
                }
                if (timeZoneInt < 0) {
                    if (timeZone.length() == 3) {
                        timeZone = timeZone + "00";
                    } else {
                        timeZone = timeZone.substring(0, 1) + "0" + timeZone.substring(1, 2) + "00";
                    }
                }

                if (timeZoneInt > 0) {
                    if (timeZone.length() == 2) {
                        timeZone = "+" + timeZone + "00";
                    } else {
                        timeZone = "+0" + timeZone + "00";
                    }
                }
                attDevice.setTimeZone(timeZone);
                attDeviceList.add(attDevice);
            }
            // attDeviceDao.save(attDeviceList);
            attDeviceDao.saveAll(attDeviceList);

            // 更新设备缓存
            for (AttDevice attDevice : attDeviceList) {
                updateToCacheDeviceInfo(attDevice);
            }
        }
    }

    @Override
    public void setPersonToDev(List<String> personIdList, List<String> attDeviceIdList) {
        if (!CollectionUtil.isEmpty(personIdList) && !CollectionUtil.isEmpty(attDeviceIdList)) {
            List<AttDevice> attDeviceList = attDeviceDao.findByIdList(attDeviceIdList);
            attSetPersonToDevOperate.sendPersonToDevice(personIdList, attDeviceList);
        }
    }

    @Override
    public List<AttDeviceItem> getAllItems() {
        List<AttDevice> attDevices = attDeviceDao.findAll();
        if (attDevices != null && attDevices.size() > 0) {
            return ModelUtil.copyListProperties(attDevices, AttDeviceItem.class);
        }
        return null;
    }

    @Override
    public List<AttDeviceItem> getItemsByIds(Collection<String> devIdList) {
        List<AttDevice> attDevices = attDeviceDao.findByIdList(devIdList);
        if (attDevices != null && attDevices.size() > 0) {
            return ModelUtil.copyListProperties(attDevices, AttDeviceItem.class);
        }
        return null;
    }

    @Override
    public Long getLastPushTime() {
        String lastPushTime = baseSysParamService.getValByName("att.device.uploadCloudlastPushTime");
        return StringUtils.isNotBlank(lastPushTime) ? Long.parseLong(lastPushTime) : null;
    }

    @Override
    public List<AttDeviceCloudItem> getUploadCloudDevicesByLastPushTime(Date lastUpdate) {
        AttDeviceCloudItem condition = new AttDeviceCloudItem();
        condition.setLastUpdateDate(lastUpdate);
        List<AttDeviceCloudItem> attDeviceCloudItems =
            (List<AttDeviceCloudItem>)attDeviceDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
        String areaIds = CollectionUtil.getPropertys(attDeviceCloudItems, AttDeviceCloudItem::getAreaId);
        List<AuthAreaItem> areaItems = authAreaService.getItemsByIds(areaIds);
        Map<String, AuthAreaItem> areaMap = CollectionUtil.itemListToIdMap(areaItems);
        AuthAreaItem area = null;
        for (AttDeviceCloudItem item : attDeviceCloudItems) {
            area = areaMap.get(item.getAreaId());
            item.setAuthAreaName(area.getName());
            item.setAuthAreaCode(area.getCode());
            item.setParentAreaCode(area.getParentAreaCode());
        }
        return attDeviceCloudItems;
    }

    @Override
    @Transactional
    public void setLastPushTime() {
        baseSysParamService.saveValueByName("att.device.uploadCloudlastPushTime", System.currentTimeMillis() + "");
    }

    @Override
    @Transactional
    public ZKResultMsg authArea(String newAreaId, String deviceIds) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        if (StringUtils.isBlank(newAreaId) || Objects.isNull(authAreaService.getItemById(newAreaId))) {
            // 区域不存在直接返回失败
            return ZKResultMsg.failMsg();
        }
        List<AttDevice> attDeviceList = attDeviceDao.findByIdList(StrUtil.strToList(deviceIds));
        if (!CollectionUtil.isEmpty(attDeviceList)) {
            for (AttDevice attDevice : attDeviceList) {
                // 判断设备的区域是否变动，如果变动则重新同步新区域人员到设备
                if (!newAreaId.equals(attDevice.getAreaId())) {
                    // 更改区域保存
                    attDevice.setAreaId(newAreaId);
                    attDeviceDao.save(attDevice);
                    // 更新设备缓存
                    updateToCacheDeviceInfo(attDevice);
                    // 发送清空设备人员信息指令
                    attDevCmdManager.clearAttUserInfo(attDevice);
                    // 开始同步设备人员信息
                    List<String> personIdList = attAreaPersonDao.getPersonIdListByAreaId(attDevice.getAreaId());
                    if (!CollectionUtil.isEmpty(personIdList)) {
                        CompletableFuture.runAsync(() -> {
                            attSetPersonToDevOperate.sendPersonToDevice(personIdList, Arrays.asList(attDevice));
                        });
                    }
                }
            }
        }
        return zkResultMsg;
    }

    @Override
    public List<AttDeviceItem> loadListByAuthFilter(AttDeviceItem attDeviceItem, int beginIndex, int endIndex,
        String sessionId) {
        String excuteSql = getDeviceQuerySql(sessionId, attDeviceItem);
        List<AttDeviceItem> allDeviceAreaItem = new ArrayList<>();
        if (StringUtils.isNotBlank(excuteSql)) {
            allDeviceAreaItem = (List<AttDeviceItem>)attDeviceDao.getItemsDataBySql(attDeviceItem.getClass(), excuteSql,
                beginIndex, endIndex, true);
        }
        if (!CollectionUtil.isEmpty(allDeviceAreaItem)) {
            buildArea(allDeviceAreaItem);
        }
        return allDeviceAreaItem;
    }

    @Override
    public List<Long> upgradeFirmware(String devIds, File file, String host, int port) {
        List<AttDevice> attDeviceList = attDeviceDao.findByIdList((List<String>)CollectionUtil.strToList(devIds));
        List<Long> cmdIdList = new ArrayList<>();
        for (AttDevice dev : attDeviceList) {
            try {
                AttDeviceItem attDeviceItem = new AttDeviceItem();
                ModelUtil.copyProperties(dev, attDeviceItem);
                cmdIdList.add(attDevCmdManager.upgradeFirmware(attDeviceItem, file, host, port, true));// 下发升级固件命令
            } catch (Exception e) {
                throw new ZKBusinessException("common_dev_upgradeFail");
            }
        }
        return cmdIdList;
    }

    @Override
    public Map<String, String> getCmdResultById(long cmdId, int timeout) {
        int sleepTime = 500;// 单位ms
        ExecutorService threadPool = Executors.newSingleThreadExecutor();
        // 必须采用异步线程才能得到值
        Future<Map<String, String>> future = threadPool.submit(new Callable<Map<String, String>>() {
            public Map<String, String> call() throws Exception {
                Map<String, String> rs = null;
                for (int i = 0; i < timeout * 1000 / sleepTime; i++) {
                    rs = admsDevCmdService.getResultMap(cmdId);
                    if (rs != null) {
                        return rs;
                    } else {
                        try {
                            Thread.sleep(sleepTime);
                        } catch (InterruptedException e) {
                            log.error("exception = {}", e);
                        }
                    }
                }
                return null;
            }
        });

        Map<String, String> ret = null;
        try {
            ret = future.get();
        } catch (InterruptedException e) {
            log.error("exception = {}", e);
        } catch (ExecutionException e) {
            log.error("exception = {}", e);
        }
        return ret;
    }

    @Override
    public List<AttDeviceItem> getByAreaId(String areaId) {
        List<AttDevice> attDeviceList = attDeviceDao.findByAreaId(areaId);
        if (Objects.nonNull(attDeviceList) && attDeviceList.size() > 0) {
            return ModelUtil.copyListProperties(attDeviceList, AttDeviceItem.class);
        }
        return null;
    }

    /**
     * model更新，同时更新设备缓存
     *
     * @param attDevice:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/9/7 19:22
     * @since 1.0.0
     */
    private void updateToCacheDeviceInfo(AttDevice attDevice) {
        AttDeviceItem attDeviceItem = new AttDeviceItem();
        ModelUtil.copyProperties(attDevice, attDeviceItem);
        updateCacheDeviceInfo(attDeviceItem);
    };

    @Override
    public AttDeviceItem updateCacheDeviceInfo(AttDeviceItem attDeviceItem) {
        // 区域ID不为空，且区域名称为空，需要查询区域名称
        if (StringUtils.isNotBlank(attDeviceItem.getAreaId()) && StringUtils.isBlank(attDeviceItem.getAuthAreaName())) {
            AuthAreaItem authAreaItem = authAreaService.getItemById(attDeviceItem.getAreaId());
            attDeviceItem.setAuthAreaName(authAreaItem.getName());
        }
        attCacheManager.putDeviceInfo(attDeviceItem);
        return attDeviceItem;
    }

    @Override
    public boolean isOnline(String sn) {
        return attCacheManager.isOnline(sn);
    }

    @Override
    public String getDevStatus(String sn) {

        // 设备信息不存在返回离线
        String devJson = attCacheManager.getAdmsDeviceInfo(sn);
        if (StringUtils.isBlank(devJson)) {
            return "0";
        }

        // 禁用
        boolean enabled = JSONObject.parseObject(devJson).getBoolean("enabled");
        if (!enabled) {
            return "2";
        }

        // 在线/离线
        if (isOnline(sn)) {
            return "1";
        } else {
            return "0";
        }
    }

    @Override
    public Map<String, List<AttDeviceItem>> getPinAndDeviceListMap(Collection<String> personIds) {

        if (CollectionUtil.isEmpty(personIds)) {
            return new HashMap<>();
        }

        // 通过人员找到区域 建立人员跟区域关系
        AttAreaPersonItem attAreaPersonItem = new AttAreaPersonItem();
        attAreaPersonItem.setInPersPersonId(StrUtil.collectionToStr(personIds));
        List<AttAreaPersonItem> attAreaPersonItemList = attAreaPersonService.getByCondition(attAreaPersonItem);
        if (CollectionUtil.isEmpty(attAreaPersonItemList)) {
            return new HashMap<>();
        }

        Set<String> areaIdSet = new HashSet<>();
        Map<String, Set<String>> personIdAreaIdsMap = new HashMap<>();
        for (AttAreaPersonItem item : attAreaPersonItemList) {
            String personId = item.getPersonId();
            String areaId = item.getAreaId();
            Set<String> set = personIdAreaIdsMap.get(personId);
            if (null == set) {
                set = new HashSet<>();
            }
            set.add(areaId);
            personIdAreaIdsMap.put(personId, set);
            areaIdSet.add(areaId);
        }
        if (CollectionUtil.isEmpty(areaIdSet)) {
            return new HashMap<>();
        }

        // 通过区域再找设备 建立区域设备关系
        AttDeviceItem attDeviceItem = new AttDeviceItem();
        attDeviceItem.setInAreaId(String.join(",", areaIdSet));
        List<AttDeviceItem> deviceItemList = getByCondition(attDeviceItem);
        if (CollectionUtil.isEmpty(deviceItemList)) {
            return new HashMap<>();
        }

        Map<String, Set<AttDeviceItem>> areaIdDeviceItemMap = new HashMap<>();
        for (AttDeviceItem device : deviceItemList) {
            String areaId = device.getAreaId();
            if (StringUtils.isBlank(areaId)) {
                continue;
            }
            Set<AttDeviceItem> attDeviceSet = areaIdDeviceItemMap.get(areaId);
            if (null == attDeviceSet) {
                attDeviceSet = new HashSet<>();
            }
            attDeviceSet.add(device);
            areaIdDeviceItemMap.put(areaId, attDeviceSet);
        }

        // 通过人员找到区域 区域再找设备 然后根据人员匹配设备
        Map<String, List<AttDeviceItem>> personPinDeviceItemMap = new HashMap<>();
        Set<AttDeviceItem> personDeviceSet = null;
        List<PersPersonItem> personItems = persPersonService.getItemsByIds(personIds);
        for (PersPersonItem persPersonItem : personItems) {

            Set<String> areaSet = personIdAreaIdsMap.get(persPersonItem.getId());
            if (CollectionUtil.isEmpty(areaSet)) {
                continue;
            }
            // 获取人员归属的每个区域拥有的设备集合累加起来就是人员所对应的所有设备
            personDeviceSet = new HashSet<>();
            for (String areaId : areaSet) {
                Set<AttDeviceItem> attDevices = areaIdDeviceItemMap.get(areaId);
                if (CollectionUtil.isEmpty(attDevices)) {
                    continue;
                }
                personDeviceSet.addAll(attDevices);
            }
            if (CollectionUtil.isEmpty(personDeviceSet)) {
                continue;
            }
            List<AttDeviceItem> attDeviceItemList = new ArrayList<>(personDeviceSet);
            personPinDeviceItemMap.put(persPersonItem.getPin(), attDeviceItemList);
        }
        return personPinDeviceItemMap;
    }

    @Override
    public ZKResultMsg setShortcutKey(AttShortcutKeyInfoItem attShortcutKeyInfoItem) {
        if (StringUtils.isNotBlank(attShortcutKeyInfoItem.getDeviceIds())) {
            List<AttDevice> attDeviceList =
                attDeviceDao.findByIdList(Arrays.asList(attShortcutKeyInfoItem.getDeviceIds().split(",")));

            String paramsValue = baseSysParamService.getValByName(attShortcutKeyInfoItem.getKeyId());
            if (StringUtils.isNotBlank(paramsValue)) {
                String[] paramsValueArray = paramsValue.split(",");
                attShortcutKeyInfoItem.setKeyId(paramsValueArray[0]);
                attShortcutKeyInfoItem.setStatusCode(paramsValueArray[1]);
                attShortcutKeyInfoItem.setShowName(paramsValueArray[2]);
            }
            attShortcutKeyInfoItem.setKeyFun("1");
            attShortcutKeyInfoItem.setAutoState("1");
            // 每天
            String autoType = attShortcutKeyInfoItem.getAutoType();
            if ("1".equals(autoType)) {
                String dayAutoTime = attShortcutKeyInfoItem.getDayAutoTime();
                attShortcutKeyInfoItem.setMon("1");
                attShortcutKeyInfoItem.setMonAutoTime(dayAutoTime);
                attShortcutKeyInfoItem.setTue("1");
                attShortcutKeyInfoItem.setTueAutoTime(dayAutoTime);
                attShortcutKeyInfoItem.setWed("1");
                attShortcutKeyInfoItem.setWedAutoTime(dayAutoTime);
                attShortcutKeyInfoItem.setThu("1");
                attShortcutKeyInfoItem.setThuAutoTime(dayAutoTime);
                attShortcutKeyInfoItem.setFri("1");
                attShortcutKeyInfoItem.setFriAutoTime(dayAutoTime);
                attShortcutKeyInfoItem.setSat("1");
                attShortcutKeyInfoItem.setSatAutoTime(dayAutoTime);
                attShortcutKeyInfoItem.setSun("1");
                attShortcutKeyInfoItem.setSunAutoTime(dayAutoTime);
            }

            for (AttDevice attDevice : attDeviceList) {
                attDevCmdManager.updateShortcutKey(attDevice.getDevSn(), attShortcutKeyInfoItem);
            }
        }
        return new ZKResultMsg();
    }

    @Override
    public Map<String, String> getShortcutKeyNameMap() {
        BaseSysParamItem sysParamItem = new BaseSysParamItem();
        sysParamItem.setParamName("att.cardStatus.");
        List<BaseSysParamItem> baseSysParamItemList = baseSysParamService.getByCondition(sysParamItem);
        Map<String, String> map = new HashMap<>();
        for (BaseSysParamItem baseSysParamItem : baseSysParamItemList) {
            String paramValue = baseSysParamItem.getParamValue();
            if (StringUtils.isNotBlank(paramValue)) {
                String[] paramValueArray = paramValue.split(",");
                map.put(paramValueArray[1], paramValueArray[2]);
            }
        }
        return map;
    }
}