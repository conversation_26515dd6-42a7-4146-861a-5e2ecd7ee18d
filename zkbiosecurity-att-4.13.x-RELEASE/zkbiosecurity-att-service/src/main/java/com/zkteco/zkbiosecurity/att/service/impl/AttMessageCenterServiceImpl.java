package com.zkteco.zkbiosecurity.att.service.impl;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.message.vo.Other2MessageReceiverItem;
import com.zkteco.zkbiosecurity.system.app.constants.BaseAppConstants;
import com.zkteco.zkbiosecurity.system.constants.BaseDataConstants;
import com.zkteco.zkbiosecurity.system.service.BaseRegisterService;
import com.zkteco.zkbiosecurity.system.service.HybridCloudPushService;
import com.zkteco.zkbiosecurity.system.vo.BaseRegisterItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttLeaveTypeDao;
import com.zkteco.zkbiosecurity.att.dao.AttShiftDao;
import com.zkteco.zkbiosecurity.att.model.AttLeaveType;
import com.zkteco.zkbiosecurity.att.model.AttShift;
import com.zkteco.zkbiosecurity.att.service.AttMessageCenterService;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.message.service.Other2MessageCenterService;
import com.zkteco.zkbiosecurity.message.vo.Other2MessageCenterItem;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 消息中心
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 14:34 2021/8/13
 * @version v1.0
 */
@Slf4j
@Service
public class AttMessageCenterServiceImpl implements AttMessageCenterService {

    @Value("${system.language:zh_CN}")
    private String language;

    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AttShiftDao attShiftDao;
    @Autowired
    private AttLeaveTypeDao attLeaveTypeDao;
    @Autowired
    private HybridCloudPushService hybridCloudPushService;
    @Autowired
    private BaseRegisterService baseRegisterService;

    /** 考勤申请 **/
    private final String APPLY_MESSAGE = "1";
    /** 知会消息 **/
    private final String NOTIFY_MESSAGE = "4";
    /** 待办消息 **/
    private final String TODO_MESSAGE = "5";

    /** 请假 **/
    private final String LEAVE = "1"; // "AttLeave";
    /** 加班 **/
    private final String OVERTIME = "2"; // "AttOvertime";
    /** 补签 **/
    private final String SIGN = "3"; // "AttSign";
    /** 调休补班 */
    private final String ADJUST = "4"; // "AttAdjust";
    /** 调班 */
    private final String CLASS = "5"; // "AttClass";
    /** 出差 */
    private final String TRIP = "14";
    /** 出外 */
    private final String OUT = "15";

    @Autowired(required = false)
    private Other2MessageCenterService other2MessageCenterService;

    @Override
    public void pushLeaveMessage(AttLeaveItem attLeaveItem) {
        try {
            Other2MessageCenterItem item = buildMessageCenterItem();
            item.setBusinessType(LEAVE);
            buildLeaveMessage(item, attLeaveItem);
            push2MessageCenter(null, item);
        } catch (Exception e) {
            log.error("Att PushMessage Exception", e);
        }
    }

    @Override
    public void pushLeaveNotifyMessage(String pin, String businessCode, AttLeaveItem attLeaveItem) {
        try {
            Other2MessageCenterItem item = newNotifyMessageItem();
            buildReceiverId(pin, item);
            String businessType = LEAVE;
            String title = i18nLocalLanguage("att_flowable_notifymsg_leave");
            if (AttConstant.FLOW_TYPE_TRIP.equals(attLeaveItem.getLeaveTypeNo())) {
                businessType = TRIP;
                title = i18nLocalLanguage("att_flowable_notifymsg_trip");
            } else if (AttConstant.FLOW_TYPE_OUT.equals(attLeaveItem.getLeaveTypeNo())) {
                businessType = OUT;
                title = i18nLocalLanguage("att_flowable_notifymsg_out");
            }
            item.setBusinessType(businessType);
            item.setTitle(title);
            buildLeaveMessage(item, attLeaveItem);
            push2MessageCenter(pin, item);
        } catch (Exception e) {
            log.error("Att PushMessage Exception", e);
        }
    }

    @Override
    public void pushLeaveTodoMessage(String pin, String businessCode, AttLeaveItem attLeaveItem) {
        try {
            Other2MessageCenterItem item = newTodoMessageItem();
            buildReceiverId(pin, item);
            String businessType = LEAVE;
            String title = i18nLocalLanguage("att_flowable_todomsg_leave");
            if (AttConstant.FLOW_TYPE_TRIP.equals(attLeaveItem.getLeaveTypeNo())) {
                businessType = TRIP;
                title = i18nLocalLanguage("att_flowable_todomsg_trip");
            } else if (AttConstant.FLOW_TYPE_OUT.equals(attLeaveItem.getLeaveTypeNo())) {
                businessType = OUT;
                title = i18nLocalLanguage("att_flowable_todomsg_out");
            }
            item.setBusinessType(businessType);
            item.setTitle(title);
            buildLeaveMessage(item, attLeaveItem);
            push2MessageCenter(pin, item);
        } catch (Exception e) {
            log.error("Att PushMessage Exception", e);
        }
    }

    private void buildLeaveMessage(Other2MessageCenterItem item, AttLeaveItem attLeaveItem) {
        item.setBusinessId(attLeaveItem.getTaskId());
        item.setCreateTime(attLeaveItem.getOperateDatetime());
        String startDatetime = AttDateUtils.dateToStrAsLong(attLeaveItem.getStartDatetime());
        String endDatetime = AttDateUtils.dateToStrAsLong(attLeaveItem.getEndDatetime());
        String name = buildName(attLeaveItem.getPersonPin());
        AttLeaveType attLeaveType = attLeaveTypeDao.getOne(attLeaveItem.getLeaveTypeId());
        String leaveTypeName = attLeaveType.getLeaveTypeName();
        if (StringUtils.isBlank(item.getTitle())) {
            item.setTitle(MessageFormat.format(i18nLocalLanguage("att_message_leave"), leaveTypeName));
        }
        String leaveContent = MessageFormat.format(i18nLocalLanguage("att_message_leaveContent"), name, leaveTypeName,
            leaveTypeName, startDatetime, endDatetime);
        item.setContent(leaveContent);
        String href = "attMessageCenter.do?info&id=" + attLeaveItem.getId() + "&type=" + AttConstant.FLOW_TYPE_LEAVE
            + "^0^0^400^400^" + i18nLocalLanguage("att_leftMenu_leave") + i18nLocalLanguage("auth_license_details");
        item.setHref(href);
    }

    @Override
    public void pushOvertimeMessage(AttOvertimeItem attOvertimeItem) {
        try {
            Other2MessageCenterItem item = buildMessageCenterItem();
            // 考勤加班通知
            item.setTitle(i18nLocalLanguage("att_message_overtime"));
            buildOvertimeMessage(item, attOvertimeItem);
            push2MessageCenter(null, item);
        } catch (Exception e) {
            log.error("Att PushMessage Exception", e);
        }
    }

    @Override
    public void pushOvertimeNotifyMessage(String pin, AttOvertimeItem attOvertimeItem) {
        try {
            Other2MessageCenterItem item = newNotifyMessageItem();
            // 加班知会
            item.setTitle(i18nLocalLanguage("att_flowable_notifymsg_overtime"));
            buildReceiverId(pin, item);
            buildOvertimeMessage(item, attOvertimeItem);
            push2MessageCenter(pin, item);
        } catch (Exception e) {
            log.error("Att PushMessage Exception", e);
        }
    }

    @Override
    public void pushOvertimeTodoMessage(String pin, AttOvertimeItem attOvertimeItem) {
        try {
            Other2MessageCenterItem item = newTodoMessageItem();
            // 加班审评
            item.setTitle(i18nLocalLanguage("att_flowable_todomsg_overtime"));
            buildReceiverId(pin, item);
            buildOvertimeMessage(item, attOvertimeItem);
            push2MessageCenter(pin, item);
        } catch (Exception e) {
            log.error("Att PushMessage Exception", e);
        }
    }

    private void buildOvertimeMessage(Other2MessageCenterItem item, AttOvertimeItem attOvertimeItem) {
        item.setBusinessId(attOvertimeItem.getTaskId());
        item.setCreateTime(attOvertimeItem.getOperateDatetime());
        item.setBusinessType(OVERTIME);
        String startDatetime = AttDateUtils.dateToStrAsLong(attOvertimeItem.getStartDatetime());
        String endDatetime = AttDateUtils.dateToStrAsLong(attOvertimeItem.getEndDatetime());
        String name = buildName(attOvertimeItem.getPersonPin());
        String overtimeContent =
            MessageFormat.format(i18nLocalLanguage("att_message_overtimeContent"), name, startDatetime, endDatetime);
        item.setContent(overtimeContent);
        String href = "attMessageCenter.do?info&id=" + attOvertimeItem.getId() + "&type="
            + AttConstant.FLOW_TYPE_OVERTIME + "^0^0^400^400^" + i18nLocalLanguage("att_leftMenu_overtime")
            + i18nLocalLanguage("auth_license_details");
        item.setHref(href);
    }

    @Override
    public void pushSignMessage(AttSignItem attSignItem) {
        try {
            Other2MessageCenterItem item = buildMessageCenterItem();
            // 考勤补签通知
            item.setTitle(i18nLocalLanguage("att_message_sign"));
            buildSignMessage(item, attSignItem);
            push2MessageCenter(null, item);
        } catch (Exception e) {
            log.error("Att PushMessage Exception", e);
        }
    }

    @Override
    public void pushSignNotifyMessage(String pin, AttSignItem attSignItem) {
        try {
            Other2MessageCenterItem item = newNotifyMessageItem();
            // 补签知会
            item.setTitle(i18nLocalLanguage("att_flowable_notifymsg_sign"));
            buildReceiverId(pin, item);
            buildSignMessage(item, attSignItem);
            push2MessageCenter(pin, item);
        } catch (Exception e) {
            log.error("Att PushMessage Exception", e);
        }
    }

    @Override
    public void pushSignTodoMessage(String pin, AttSignItem attSignItem) {
        try {
            Other2MessageCenterItem item = newTodoMessageItem();
            // 补签审批
            item.setTitle(i18nLocalLanguage("att_flowable_todomsg_sign"));
            buildReceiverId(pin, item);
            buildSignMessage(item, attSignItem);
            push2MessageCenter(pin, item);
        } catch (Exception e) {
            log.error("Att PushMessage Exception", e);
        }
    }

    private void buildSignMessage(Other2MessageCenterItem item, AttSignItem attSignItem) {
        item.setBusinessId(attSignItem.getTaskId());
        item.setCreateTime(attSignItem.getOperateDatetime());
        item.setBusinessType(SIGN);
        String signDatetime = AttDateUtils.dateToStrAsLong(attSignItem.getSignDatetime());
        String name = buildName(attSignItem.getPersonPin());
        String signContent = MessageFormat.format(i18nLocalLanguage("att_message_signContent"), name, signDatetime);
        item.setContent(signContent);
        String href = "attMessageCenter.do?info&id=" + attSignItem.getId() + "&type=" + AttConstant.FLOW_TYPE_SIGN
            + "^0^0^400^400^" + i18nLocalLanguage("att_api_sign") + i18nLocalLanguage("auth_license_details");
        item.setHref(href);
    }

    @Override
    public void pushAdjustMessage(AttAdjustItem attAdjustItem) {
        Other2MessageCenterItem other2MessageCenterItem = buildMessageCenterItem();
        other2MessageCenterItem.setCreateTime(attAdjustItem.getOperateDatetime());
        other2MessageCenterItem.setTitle(i18nLocalLanguage("att_message_adjust"));
        other2MessageCenterItem.setBusinessType(ADJUST);
        String adjustDate = AttDateUtils.dateToStrAsShort(attAdjustItem.getAdjustDate());
        String name = buildName(attAdjustItem.getPersonPin());
        String adjustContent = MessageFormat.format(i18nLocalLanguage("att_message_adjustContent"), name, adjustDate);
        other2MessageCenterItem.setContent(adjustContent);
        String href = "attMessageCenter.do?info&id=" + attAdjustItem.getId() + "&type=" + AttConstant.FLOW_TYPE_ADJUST
            + "^0^0^400^400^" + i18nLocalLanguage("att_rule_off") + i18nLocalLanguage("auth_license_details");
        other2MessageCenterItem.setHref(href);
        push2MessageCenter(null, other2MessageCenterItem);
    }

    @Override
    public void pushClassMessage(AttClassItem attClassItem) {
        Other2MessageCenterItem other2MessageCenterItem = buildMessageCenterItem();
        other2MessageCenterItem.setCreateTime(attClassItem.getOperateDatetime());
        other2MessageCenterItem.setTitle(i18nLocalLanguage("att_message_class"));
        other2MessageCenterItem.setBusinessType(CLASS);
        // 数据格式化
        String adjustDate = AttDateUtils.dateToStrAsShort(attClassItem.getAdjustDate());
        String adjustNameAndPin = buildName(attClassItem.getAdjustPersonPin());
        String adjustShiftName = "";
        if (StringUtils.isNotBlank(attClassItem.getSwapShiftId())) {
            AttShift attShift = attShiftDao.findById(attClassItem.getSwapShiftId()).orElse(null);
            adjustShiftName = Objects.nonNull(attShift) ? attShift.getShiftName() : "";
        }
        String swapDate = AttDateUtils.dateToStrAsShort(attClassItem.getSwapDate());
        String swapPin = attClassItem.getSwapPersonPin();
        String swapNameAndPin = buildName(swapPin);
        if (attClassItem.getAdjustType() == AttConstant.CLASS_TYPE_SAMETIMEMOVE) {
            // 个人同日期调班
            String classContent = MessageFormat.format(i18nLocalLanguage("att_message_classContent0"), adjustNameAndPin,
                adjustDate, adjustShiftName);
            other2MessageCenterItem.setContent(classContent);
        } else if (attClassItem.getAdjustType() == AttConstant.CLASS_TYPE_DIFFTIMEMOVE) {
            // 个人不同日期调班
            String classContent = MessageFormat.format(i18nLocalLanguage("att_message_classContent1"), adjustNameAndPin,
                adjustDate, swapDate);
            other2MessageCenterItem.setContent(classContent);
        } else if (attClassItem.getAdjustType() == AttConstant.CLASS_TYPE_PEOPLEMOVE) {
            // 两人对调
            String classContent = MessageFormat.format(i18nLocalLanguage("att_message_classContent2"), adjustNameAndPin,
                adjustDate, swapNameAndPin, swapDate);
            other2MessageCenterItem.setContent(classContent);
        } else {
            return;
        }
        String href =
            "attMessageCenter.do?info&id=" + attClassItem.getId() + "&type=" + AttConstant.FLOW_TYPE_CLASS_SHIFT
                + "^0^0^400^400^" + i18nLocalLanguage("att_leftMenu_class") + i18nLocalLanguage("auth_license_details");
        other2MessageCenterItem.setHref(href);
        push2MessageCenter(null, other2MessageCenterItem);
    }

    /**
     * 组装公共对象
     *
     * @return com.zkteco.zkbiosecurity.message.vo.Other2MessageCenterItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/16 9:13
     * @since 1.0.0
     */
    private Other2MessageCenterItem buildMessageCenterItem() {
        Other2MessageCenterItem item = new Other2MessageCenterItem();
        /** 消息类型 1.考勤申请 2.系统提醒 3.访客动态 4.知会 5.待办消息 */
        item.setMsgType(APPLY_MESSAGE);
        item.setSourceModule(ConstUtil.SYSTEM_MODULE_ATT);
        return item;
    }

    private Other2MessageCenterItem newNotifyMessageItem() {
        Other2MessageCenterItem item = new Other2MessageCenterItem();
        /** 消息类型 1.考勤申请 2.系统提醒 3.访客动态 4.知会 5.待办消息 */
        item.setMsgType(NOTIFY_MESSAGE);
        item.setSourceModule(ConstUtil.SYSTEM_MODULE_ATT);
        return item;
    }

    private Other2MessageCenterItem newTodoMessageItem() {
        Other2MessageCenterItem item = new Other2MessageCenterItem();
        /** 消息类型 1.考勤申请 2.系统提醒 3.访客动态 4.知会 5.待办消息 */
        item.setMsgType(TODO_MESSAGE);
        item.setSourceModule(ConstUtil.SYSTEM_MODULE_ATT);
        return item;
    }

    /**
     * 推送至消息中心
     *
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/13 14:46
     * @since 1.0.0
     */
    private void push2MessageCenter(String pin, Other2MessageCenterItem messageCenterItem) {
        if (Objects.nonNull(other2MessageCenterService)) {
            log.info("Att push2MessageCenter item = {} ", messageCenterItem);
            other2MessageCenterService.push2MessageCenter(messageCenterItem);
        }
        if (Objects.nonNull(hybridCloudPushService) && StringUtils.isNotBlank(pin)) {
            BaseRegisterItem condition = new BaseRegisterItem();
            condition.setClientType(BaseAppConstants.APP_PERS);
            condition.setEquals(true);
            condition.setClientName(pin);
            List<BaseRegisterItem> baseRegisterItems = baseRegisterService.getByCondition(condition);
            if (CollectionUtils.isNotEmpty(baseRegisterItems)) {
                JSONObject params = new JSONObject();
                JSONObject data = new JSONObject();
                data.put("touser", CollectionUtil
                    .getPropertyList(baseRegisterItems, BaseRegisterItem::getRegistrationKey, "-1").toArray());
                data.put("title", messageCenterItem.getTitle());
                data.put("body", messageCenterItem.getContent());

                data.put("msgType", messageCenterItem.getMsgType());
                data.put("sourceModule", messageCenterItem.getSourceModule());
                data.put("businessType", messageCenterItem.getBusinessType());
                data.put("businessId", messageCenterItem.getBusinessId());

                params.put("params", data);
                log.info("Att CloudPush params = {} ", params);
                hybridCloudPushService.pushMsg(params);
            }
        }
    }

    /**
     * 组装姓名
     * 
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/13 18:15
     * @since 1.0.0
     */
    private String buildName(String pin) {

        PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
        if (Objects.isNull(persPersonItem)) {
            return "";
        }

        String personName = persPersonItem.getName();
        String lastName = persPersonItem.getLastName();
        if (StringUtils.isBlank(personName) && StringUtils.isBlank(lastName)) {
            return "";
        }

        String name = StringUtils.isNotBlank(personName) ? personName : "";
        if (StringUtils.isNotBlank(lastName)) {
            if (StringUtils.isNotBlank(name)) {
                name += (" " + lastName);
            } else {
                name = lastName;
            }
        }
        return name;
    }

    private void buildReceiverId(String pin, Other2MessageCenterItem other2MessageCenterItem) {
        PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
        if (Objects.isNull(persPersonItem)) {
            return;
        }
        Other2MessageReceiverItem other2MessageReceiverItem = new Other2MessageReceiverItem(persPersonItem.getId(),
            BaseDataConstants.MESSAGE_RECEIVER_PERS, BaseDataConstants.MESSAGE_STATE_UNREAD);
        List<Other2MessageReceiverItem> other2MessageReceiverItemList = new ArrayList<>();
        other2MessageReceiverItemList.add(other2MessageReceiverItem);
        other2MessageCenterItem.setOther2MessageReceiverItemList(other2MessageReceiverItemList);
    }

    private String i18nLocalLanguage(String code) {
        return I18nUtil.getI18nByLanguage(language, code);
    }
}
