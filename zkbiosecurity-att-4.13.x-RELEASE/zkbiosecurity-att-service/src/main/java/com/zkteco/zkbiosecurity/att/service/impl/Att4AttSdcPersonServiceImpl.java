package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.att.service.Att4AttSdcPersonService;
import com.zkteco.zkbiosecurity.att.vo.AttSdc4AttPersonItem;
import com.zkteco.zkbiosecurity.pers.service.PersPersonLinkService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;

/**
 * 考勤sdc人员接口实现
 *
 * <AUTHOR>
 * @date 2020/11/11 18:34
 * @since 1.0.0
 */
@Service
public class Att4AttSdcPersonServiceImpl implements Att4AttSdcPersonService {

    @Autowired(required = false)
    private PersPersonLinkService persPersonLinkService;
    @Autowired(required = false)
    private PersPersonService persPersonService;

    @Override
    public List<AttSdc4AttPersonItem> getAttPersonByArea(String areaId) {
        // 已由视屏当考勤代替
        List<AttSdc4AttPersonItem> attSdc4AttPersonItemList = new ArrayList<>();
        return attSdc4AttPersonItemList;
    }
}
