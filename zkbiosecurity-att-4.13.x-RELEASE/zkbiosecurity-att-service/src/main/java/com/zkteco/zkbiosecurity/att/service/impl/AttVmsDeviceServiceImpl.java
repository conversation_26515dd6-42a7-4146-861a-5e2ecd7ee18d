package com.zkteco.zkbiosecurity.att.service.impl;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.vo.Att4VmsDeviceSelectItem;
import com.zkteco.zkbiosecurity.att.vo.AttVmsDeviceSelectItem;
import com.zkteco.zkbiosecurity.att.vo.AttVmsDeviceSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * VMS当考勤
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 14:19
 * @since 1.0.0
 */
@Service
public class AttVmsDeviceServiceImpl implements AttVmsDeviceService {

    @Autowired(required = false)
    private AttGetVmsDeviceService attGetVmsDeviceService;

    @Autowired
    private AttPointService attPointService;

    @Override
    public AttVmsDeviceSelectItem getVmsDeviceByDeviceId(String deviceId) {
        if (Objects.nonNull(attGetVmsDeviceService)) {
            Att4VmsDeviceSelectItem att4VmsDeviceSelectItem = attGetVmsDeviceService.getVmsDeviceByChannelId(deviceId);
            if (Objects.nonNull(att4VmsDeviceSelectItem)) {
                AttVmsDeviceSelectItem attVmsDeviceSelectItem = new AttVmsDeviceSelectItem();
                ModelUtil.copyPropertiesIgnoreNull(att4VmsDeviceSelectItem, attVmsDeviceSelectItem);
                return attVmsDeviceSelectItem;
            }
        }
        return null;
    }

    @Override
    public List<AttVmsDeviceSelectItem> getVmsDeviceByDeviceIds(Collection<String> deviceIds) {
        List<AttVmsDeviceSelectItem> attVmsDeviceSelectItemList = new ArrayList<>();
        if (Objects.nonNull(attGetVmsDeviceService)) {
            List<Att4VmsDeviceSelectItem> vmsDeviceSelectItemList =
                attGetVmsDeviceService.getVmsDeviceByChannelIds(deviceIds);
            if (!CollectionUtil.isEmpty(vmsDeviceSelectItemList)) {
                attVmsDeviceSelectItemList =
                    ModelUtil.copyListProperties(vmsDeviceSelectItemList, AttVmsDeviceSelectItem.class);
            }
        }
        return attVmsDeviceSelectItemList;
    }

    @Override
    public Pager getSelectVmsDevicePager(AttVmsDeviceSelectItem condition, int page, int size) {
        if (Objects.nonNull(attGetVmsDeviceService)) {
            Att4VmsDeviceSelectItem att4VmsDeviceSelectItem = new Att4VmsDeviceSelectItem();
            ModelUtil.copyPropertiesIgnoreNull(condition, att4VmsDeviceSelectItem);
            List<String> deviceModules = attPointService.getDeviceIdsByDeviceModule(ConstUtil.SYSTEM_MODULE_VMS);
            String deviceModuleStr = StringUtils.join(deviceModules, AttConstant.COMMA);
            if (StringUtils.isNotBlank(deviceModuleStr)) {
                att4VmsDeviceSelectItem.setSelectId(condition.getSelectId() + AttConstant.COMMA + deviceModuleStr);
            } else {
                att4VmsDeviceSelectItem.setSelectId(condition.getSelectId());
            }
            Pager pager = attGetVmsDeviceService.getVmsDeviceSelectItemList(att4VmsDeviceSelectItem, page, size);
            List<Att4VmsDeviceSelectItem> vmsDeviceSelectItemList = (List<Att4VmsDeviceSelectItem>)pager.getData();
            List<AttVmsDeviceSelectItem> attVmsDeviceSelectItemList =
                ModelUtil.copyListProperties(vmsDeviceSelectItemList, AttVmsDeviceSelectItem.class);
            pager.setData(attVmsDeviceSelectItemList);
            return pager;
        } else {
            Pager pager = new Pager(page, size);
            pager.setData(new ArrayList<>());
            return pager;
        }
    }
}