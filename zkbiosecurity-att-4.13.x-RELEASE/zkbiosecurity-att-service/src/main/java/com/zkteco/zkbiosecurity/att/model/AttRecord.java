package com.zkteco.zkbiosecurity.att.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 考勤记录
 */
@Entity
@Table(name = "ATT_RECORD",
    indexes = {@Index(name = "ATT_RECORD_DEPT_ID_IDX", columnList = "AUTH_DEPT_ID"),
        @Index(name = "ATT_RECORD_PERS_PIN_IDX", columnList = "PERS_PERSON_PIN"),
        @Index(name = "ATT_RECORD_ATT_DATE_IDX", columnList = "ATT_DATE")})
@Setter
@Getter
@Accessors(chain = true)
public class AttRecord extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门id
     */
    @Column(name = "AUTH_DEPT_ID", length = 50)
    private String deptId;

    /**
     * 部门编号
     */
    @Column(name = "AUTH_DEPT_CODE", length = 100)
    private String deptCode;

    /**
     * 部门名称
     */
    @Column(name = "AUTH_DEPT_NAME", length = 100)
    private String deptName;

    /**
     * 人员编号
     */
    @Column(name = "PERS_PERSON_PIN", length = 50)
    private String personPin;

    /**
     * 姓名
     */
    @Column(name = "PERS_PERSON_NAME", length = 50)
    private String personName;

    /**
     * 英文（lastName）
     */
    @Column(name = "PERS_PERSON_LAST_NAME", length = 50)
    private String personLastName;

    /**
     * 考勤日期
     */
    @Column(name = "ATT_DATE")
    private Date attDate;

    /**
     * 星期
     */
    @Column(name = "WEEK")
    private String week;

    /**
     * 班次编号
     */
    @Column(name = "SHIFT_NO")
    private String shiftNo;

    /**
     * 班次名称
     */
    @Column(name = "SHIFT_NAME")
    private String shiftName;

    /** 时间段名称集合，多个以豆号隔开 */
    @Column(name = "TIMESLOT_NAME")
    private String timeSlotName;

    /**
     * 班次时间数据
     */
    @Column(name = "SHIFT_TIME_DATA")
    private String shiftTimeData;

    /**
     * 有效打卡数据
     */
    @Column(name = "CARD_VALID_DATA")
    private String cardValidData;

    /**
     * 有效打卡次数
     */
    @Column(name = "CARD_VALID_COUNT")
    private Integer cardValidCount;

    /**
     * 应出勤分钟数
     */
    @Column(name = "SHOULD_MINUTE")
    private Integer shouldMinute;


    /**
     * 应出勤分钟数（ 处理休息加班、节假日加班，且加班换算为天的时候，应上如果置为0，换算出来的加班时长也为0）
     */
    @Column(name = "SHOULD_MINUTE_EX")
    private Integer shouldMinuteEx;

    /**
     * 实际出勤分钟数
     */
    @Column(name = "ACTUAL_MINUTE")
    private Integer actualMinute;

    /**
     * 有效出勤分钟数
     */
    @Column(name = "VALID_MINUTE")
    private Integer validMinute;

    /**
     * 迟到次数数据
     */
    @Column(name = "LATE_COUNT_DATA")
    private String lateCountData;

    /**
     * 迟到分钟数数据
     */
    @Column(name = "LATE_MINUTE_DATA")
    private String lateMinuteData;

    /**
     * 迟到总次数
     */
    @Column(name = "LATE_COUNT_TOTAL")
    private Integer lateCountTotal;

    /**
     * 迟到总分钟数
     */
    @Column(name = "LATE_MINUTE_TOTAL")
    private Integer lateMinuteTotal;

    /**
     * 早退次数数据
     */
    @Column(name = "EARLY_COUNT_DATA")
    private String earlyCountData;

    /**
     * 早退分钟数数据
     */
    @Column(name = "EARLY_MINUTE_DATA")
    private String earlyMinuteData;

    /**
     * 早退总次数
     */
    @Column(name = "EARLY_COUNT_TOTAL")
    private Integer earlyCountTotal;

    /**
     * 早退总分钟数
     */
    @Column(name = "EARLY_MINUTE_TOTAL")
    private Integer earlyMinuteTotal;

    /**
     * 旷工分钟数
     */
    @Column(name = "ABSENT_MINUTE")
    private Integer absentMinute;

    /**
     * 加班分钟数-平时
     */
    @Column(name = "OVERTIME_USUAL_MINUTE")
    private Integer overtimeUsualMinute;

    /**
     * 加班分钟数-休息
     */
    @Column(name = "OVERTIME_REST_MINUTE")
    private Integer overtimeRestMinute;

    /**
     * 加班分钟数-节日
     */
    @Column(name = "OVERTIME_HOLIDAY_MINUTE")
    private Integer overtimeHolidayMinute;

    /**
     * 加班分钟数
     */
    @Column(name = "OVERTIME_MINUTE")
    private Integer overtimeMinute;

    /**
     * 请假分钟数
     */
    @Column(name = "LEAVE_MINUTE")
    private Integer leaveMinute;

    /**
     * 出差分钟数
     */
    @Column(name = "TRIP_MINUTE")
    private Integer tripMinute;

    /**
     * 外出分钟数
     */
    @Column(name = "OUT_MINUTE")
    private Integer outMinute;

    /**
     * 应出勤天数
     */
    @Column(name = "SHOULD_DAYS")
    private BigDecimal shouldDays;

    /**
     * 实际出勤天数
     */
    @Column(name = "ACTUAL_DAYS")
    private BigDecimal actualDays;

    /**
     * 有效出勤天数
     */
    @Column(name = "VALID_DAYS")
    private BigDecimal validDays;

    /**
     * 旷工天数
     */
    @Column(name = "ABSENT_DAYS")
    private BigDecimal absentDays;

    /**
     * 请假天数
     */
    @Column(name = "LEAVE_DAYS")
    private BigDecimal leaveDays;

    /**
     * 出差天数
     */
    @Column(name = "TRIP_DAYS")
    private BigDecimal tripDays;

    /**
     * 外出天数
     */
    @Column(name = "OUT_DAYS")
    private BigDecimal outDays;

    /**
     * 考勤状态(单状态)
     */
    @Deprecated
    @Column(name = "EXCEPTION_SCH_TYPE")
    private Integer exceptionSchType;

    /**
     * 考勤状态(多状态，保存假种编码多个以豆号隔开)
     */
    @Column(name = "ATTENDANCE_STATUS")
    private String attendanceStatus;

    /**
     * 请假详情
     * <p>
     * 保存各请假类型对应时长，格式:code-时长(分钟)，多个以豆号隔开
     * </p>
     */
    @Column(name = "LEAVE_DETAILS")
    private String leaveDetails;

    /**
     * 考勤卡点状态
     */
    @Column(name = "CARD_STATUS")
    private String cardStatus;

    /**
     * 跨天日期
     */
    @Column(name = "CROSS_DAY")
    private String crossDay;

    /**
     * 备注
     */
    @Column(name = "REMARK")
    private String remark;


    //推送微信
    @com.zkteco.zkbiosecurity.base.annotation.Column(name = "push_wechat_flag")
    private String pushWechatFlag;

}