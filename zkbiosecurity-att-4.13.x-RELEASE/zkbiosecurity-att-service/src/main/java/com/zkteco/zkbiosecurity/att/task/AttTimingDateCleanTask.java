package com.zkteco.zkbiosecurity.att.task;

import java.util.Objects;
import java.util.concurrent.ScheduledFuture;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.service.AttTransactionService;
import com.zkteco.zkbiosecurity.scheduler.ScheduleService;
import com.zkteco.zkbiosecurity.system.constants.BaseDataConstants;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 定时清空考勤事件记录
 * 
 * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
 * @since 2019年9月3日 下午6:54:17
 */
@Slf4j
@Component
public class AttTimingDateCleanTask {

    private ScheduledFuture<?> scedulefuture;
    @Autowired
    private ScheduleService scheduleService;

    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AttTransactionService attTransactionService;

    public void startTask() {

        log.info("AttTimingDateCleanTask Init ... ");

        try {

            if (scedulefuture != null) {
                scedulefuture.cancel(true);
            }
            // 数据清理
            BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("attReportDataClean");
            JSONObject json = null;
            // 防止参数未成功初始化导致的空指针异常
            if (Objects.isNull(baseSysParamItem) || StringUtils.isBlank(baseSysParamItem.getParamValue())) {
                json = new JSONObject();
                json.put("keptMonth", "15");
                json.put("runtime", "01:00:00");
                json.put("keptType", BaseDataConstants.DATA_CLEAN_KEPTMONTH);
            } else {
                json = JSON.parseObject(baseSysParamItem.getParamValue());
            }
            String paramValue = JSON.toJSONString(json);
            String runtime = json.getString("runtime");
            if (StringUtils.isNotBlank(runtime) && StringUtils.isNotBlank(runtime.split(":")[0])) {
                String cronExpression = "0 0 " + runtime.split(":")[0] + " * * ?";
                scedulefuture = scheduleService.startScheduleTask(
                    () -> attTransactionService.executeAttTimingDataClean(paramValue), cronExpression);
            }

        } catch (Exception e) {

            log.error("AttTimingDateCleanTask Init Exception", e);
        }
    }
}
