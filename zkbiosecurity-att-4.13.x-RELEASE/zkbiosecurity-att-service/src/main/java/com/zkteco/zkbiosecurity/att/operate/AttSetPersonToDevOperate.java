package com.zkteco.zkbiosecurity.att.operate;

import java.util.*;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.att.service.AttPersonService;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.cmd.att.constants.CmdAttConstants;
import com.zkteco.zkbiosecurity.pers.service.PersBioPhotoService;
import com.zkteco.zkbiosecurity.pers.vo.PersBioPhotoItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.cmd.AttDevCmdManager;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttDeviceDao;
import com.zkteco.zkbiosecurity.att.model.AttAreaPerson;
import com.zkteco.zkbiosecurity.att.model.AttDevice;
import com.zkteco.zkbiosecurity.att.service.AttDeviceOptionService;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.pers.service.PersBioTemplateService;
import com.zkteco.zkbiosecurity.pers.service.PersCardService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersBioTemplateItem;
import com.zkteco.zkbiosecurity.pers.vo.PersCardItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 考勤命令下发管理，组合命令下发
 *
 * <AUTHOR>
 * @date 2025-04-01 14:44
 * @since 1.0.0
 */
@Component
@Slf4j
public class AttSetPersonToDevOperate {

    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttDevCmdManager attDevCmdManager;
    @Autowired
    private AttDeviceOptionService attDeviceOptionService;
    @Autowired
    private AttDeviceDao attDeviceDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersBioTemplateService persBioTemplateService;
    @Autowired
    private PersBioPhotoService persBioPhotoService;
    @Autowired
    private PersCardService persCardService;

    public void sendPersonToDeviceByArea(List<AttAreaPerson> attAreaPersonList) {
        // 获取对应的设备
        Map<String, List<String>> attAreaPersonMap = new HashMap<String, List<String>>();
        attAreaPersonList.forEach(attAreaPerson -> {
            String areaId = attAreaPerson.getAreaId();
            String personId = attAreaPerson.getPersonId();
            if (attAreaPersonMap.containsKey(areaId)) {
                List<String> personIdCollection = attAreaPersonMap.get(areaId);
                personIdCollection.add(personId);
            } else {
                List<String> personIdCollection = new ArrayList<String>();
                personIdCollection.add(personId);
                attAreaPersonMap.put(areaId, personIdCollection);
            }
        });

        attAreaPersonMap.forEach((areaId, personIdList) -> {
            List<AttDevice> attDeviceList = attDeviceDao.findByAreaId(areaId);
            sendPersonToDevice(personIdList, attDeviceList);
        });
    }

    /**
     * 下发人员比对照片
     *
     * @param attDevices:
     * @param pin:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/6/8 9:16
     * @since 1.0.0
     */
    public void sendBioPhotoToDevice(Set<AttDevice> attDevices, String pin) {
        // 获取设备的参数
        Collection<String> devIdList =
            CollectionUtil.getPropertyList(attDevices, AttDevice::getId, AttConstant.COMM_DEF_VALUE);
        Map<String, List<AttDeviceOptionItem>> devOptionMap =
            attDeviceOptionService.getDeviceIdAndDeviceOptionMap(devIdList);

        // 下发命令操作
        attDevices.forEach(attDevice -> {
            List<AttDeviceOptionItem> devOptionAry = devOptionMap.get(attDevice.getId());
            // 下发比对照片
            attDevCmdManager.sendBioPhotoToDev(attDevice.getDevSn(), pin, devOptionAry);
        });
    }

    /**
     * 下发人员模板信息
     *
     * @param attDeviceList:
     * @param personItem:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/6/7 16:41
     * @since 1.0.0
     */
    public void sendPersonBioDataToDevice(List<AttDevice> attDeviceList, PersPersonItem personItem,
        List<PersBioTemplateItem> bioTemplateItemList) {
        // 获取设备的参数
        Collection<String> devIdList =
            CollectionUtil.getPropertyList(attDeviceList, AttDevice::getId, AttConstant.COMM_DEF_VALUE);
        Map<String, List<AttDeviceOptionItem>> devOptionMap =
            attDeviceOptionService.getDeviceIdAndDeviceOptionMap(devIdList);
        if (CollectionUtil.isEmpty(devOptionMap)) {
            return;
        }

        // 遍历设备下发
        for (AttDevice attDevice : attDeviceList) {
            List<AttDeviceOptionItem> devOptions = devOptionMap.get(attDevice.getId());

            // 获取人员生物模板列表
            List<AttBioTemplateItem> attBioTemplateItemList =
                attDevCmdManager.buildAttBioTemplateItem(bioTemplateItemList);

            // 获取支持多模态生物特征模板参数(标识为1的都支持一体化)
            String multiBioDataSupport = attDevCmdManager.getOptValue(devOptions, "MultiBioDataSupport");
            // 获取支持多模态生物特征图片参数
            String multiBioPhotoSupport = attDevCmdManager.getOptValue(devOptions, "MultiBioPhotoSupport");

            // "MultiBioDataSupport"和"MultiBioPhotoSupport"不存在或值为空，走原先的业务逻辑
            if (StringUtils.isNotBlank(multiBioDataSupport) || StringUtils.isNotBlank(multiBioPhotoSupport)) {
                // 走新协议（增加MultiBioDataSupport和MultiBioPhotoSupport参数）
                sendMultiBioToDevice(personItem.getPin(), attDevice, devOptions, attBioTemplateItemList, null, false);
            } else {
                // 原有的业务流程(兼容旧协议--未加MultiBioDataSupport和MultiBioPhotoSupport参数以前的协议)
                sendBioToDevice(attBioTemplateItemList, devOptions, personItem.getPin(), attDevice, false);
            }
        }
    }

    /**
     * 下发人员基础信息和模板信息
     *
     * @param personIdList:
     * @param attDeviceList:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/6/7 16:51
     * @since 1.0.0
     */
    public void sendPersonToDevice(List<String> personIdList, List<AttDevice> attDeviceList) {

        if (CollectionUtil.isEmpty(attDeviceList)) {
            return;
        }

        if (CollectionUtil.isEmpty(personIdList)) {
            return;
        }

        // 获取设备的参数
        Collection<String> devIdList =
            CollectionUtil.getPropertyList(attDeviceList, AttDevice::getId, AttConstant.COMM_DEF_VALUE);
        Map<String, List<AttDeviceOptionItem>> devOptionMap =
            attDeviceOptionService.getDeviceIdAndDeviceOptionMap(devIdList);
        if (CollectionUtil.isEmpty(devOptionMap)) {
            return;
        }

        // 分批处理
        List<List<String>> personIdsList = CollectionUtil.split(personIdList, CollectionUtil.splitSize);
        personIdsList.forEach(splitPersonIds -> {
            // 获取考勤人员授权信息
            Map<String, AttPersonItem> personIdAndDevAuthMap =
                attPersonService.getPersonIdAndAttPersonItemMap(splitPersonIds);

            // 获取人员对应的主卡号
            List<PersCardItem> masterCardPersonIdList = persCardService.getMasterCardByPersonIdList(splitPersonIds);
            Map<String, PersCardItem> personIdAndCardMap =
                CollectionUtil.listToKeyMap(masterCardPersonIdList, PersCardItem::getPersonId);

            // 获取人员生物模板
            List<PersBioTemplateItem> bioTemplateItemList =
                persBioTemplateService.getItemByPersonIdList(splitPersonIds);
            // 模板或图像按人员分组
            Map<String, List<PersBioTemplateItem>> bioTemplateItemMap =
                bioTemplateItemList.stream().collect(Collectors.groupingBy(item -> item.getPersonId()));

            // 获取人员比对照片
            List<PersBioPhotoItem> persBioPhotoItemList = persBioPhotoService.findByPersonIdIn(personIdList);
            Map<String, List<PersBioPhotoItem>> persBioPhotoItemMap =
                persBioPhotoItemList.stream().collect(Collectors.groupingBy(PersBioPhotoItem::getPersonId));

            // 获取人员基础信息
            List<PersPersonItem> persPersonItemList = persPersonService.getSimpleItemsByIds(splitPersonIds);
            for (PersPersonItem personItem : persPersonItemList) {

                // 人员禁用不下发
                if (personItem.getEnabledCredential() != null && !personItem.getEnabledCredential()) {
                    continue;
                }

                // 获取人员基础信息
                PersCardItem persCardItem = personIdAndCardMap.get(personItem.getId());

                // 获取考勤人员信息
                AttPersonItem attPerson = personIdAndDevAuthMap.get(personItem.getId());

                // 组装人员基础信息命令下发实体
                AttPersonOptItem attPersonOptItem =
                    attDevCmdManager.buildPersonOptItem(personItem, persCardItem, attPerson);

                // 组装人员头像命令下发实体
                AttPersonPhotoOptItem attPersonPhotoOptItem = attDevCmdManager.buildPersonPhotoOptItem(personItem);

                // 获取人员生物模板列表
                List<PersBioTemplateItem> personBioTempList = bioTemplateItemMap.get(personItem.getId());
                List<AttBioTemplateItem> attBioTemplateItemList =
                    attDevCmdManager.buildAttBioTemplateItem(personBioTempList);

                // 获取人员比对照片列表
                List<PersBioPhotoItem> persBioPhotoList = persBioPhotoItemMap.get(personItem.getId());
                List<AttBioPhotoItem> attBioPhotoItemList = attDevCmdManager.buildAttBioPhotoItem(persBioPhotoList);

                for (AttDevice attDevice : attDeviceList) {
                    List<AttDeviceOptionItem> devOptionList = devOptionMap.get(attDevice.getId());

                    // 下发人员基础信息
                    attDevCmdManager.sendPersonToDev(attDevice.getDevSn(), attPersonOptItem);

                    // 下发用户头像信息 (Base64或URL)
                    attDevCmdManager.sendPersonPhotoToDev(attDevice, attPersonPhotoOptItem, devOptionList);

                    // 获取支持多模态生物特征模板参数(标识为1的都支持一体化)
                    String multiBioDataSupport = attDevCmdManager.getOptValue(devOptionList, "MultiBioDataSupport");
                    // 获取支持多模态生物特征图片参数
                    String multiBioPhotoSupport = attDevCmdManager.getOptValue(devOptionList, "MultiBioPhotoSupport");

                    // "MultiBioDataSupport"和"MultiBioPhotoSupport"不存在或值为空，走原先的业务逻辑
                    if (StringUtils.isNotBlank(multiBioDataSupport) || StringUtils.isNotBlank(multiBioPhotoSupport)) {
                        // 走新协议（增加MultiBioDataSupport和MultiBioPhotoSupport参数）
                        sendMultiBioToDevice(personItem.getPin(), attDevice, devOptionList, attBioTemplateItemList,
                            attBioPhotoItemList, true);
                    } else {
                        // 原有的业务流程(兼容旧协议--未加MultiBioDataSupport和MultiBioPhotoSupport参数以前的协议)
                        sendBioToDevice(attBioTemplateItemList, devOptionList, personItem.getPin(), attDevice, true);
                    }
                }
            }
        });
    }

    /**
     * 发一体化模板，或发比对照片（新协议是指增加MultiBioDataSupport、MultiBioPhotoSupport后的协议） 人员同步先下发人脸模版，人脸模版下发成功，就不下发比对照片
     * 
     * @param sendBioPhoto: 是否下发比对照片（防止设备上来BioPhoto和BioData导致重复下发BioPhoto，如果是设备上来的，不进行BioPhoto的下发）
     */
    private void sendMultiBioToDevice(String pin, AttDevice attDevice, List<AttDeviceOptionItem> devOptionList,
        List<AttBioTemplateItem> attBioTemplateItemList, List<AttBioPhotoItem> attBioPhotoItemList,
        boolean sendBioPhoto) {

        // 优化代码暂时不放开
        // // 获取当前所有类型
        // Set<Short> bioTypeSet =
        // attBioTemplateItemList.stream().map(AttBioTemplateItem::getBioType).collect(Collectors.toSet());
        // Set<Short> bioPhotoSet =
        // attBioPhotoItemList.stream().map(AttBioPhotoItem::getType).collect(Collectors.toSet());
        // bioTypeSet.addAll(bioPhotoSet);
        //
        // // 遍历类型发送
        // for (Short bioType : bioTypeSet) {
        //
        // // 当前类型的模板
        // List<AttBioTemplateItem> bioTemplateItemList = attBioTemplateItemList.stream()
        // .filter(item -> item.getBioType().equals(bioType)).collect(Collectors.toList());
        //
        // if (BaseConstants.BaseBioType.FP_BIO_TYPE.equals(bioType)) {
        //
        // // 下发近红外一体化指纹模板
        // attDevCmdManager.sendMultiFPDataToDev(attDevice, bioTemplateItemList, devOptionList,
        // BaseConstants.BaseBioType.FP_BIO_TYPE);
        //
        // } else if (BaseConstants.BaseBioType.FACE_BIO_TYPE.equals(bioType)) {
        //
        // // 下发近红外人脸一体化模板
        // attDevCmdManager.sendMultiFaceDataToDev(attDevice, bioTemplateItemList, devOptionList,
        // BaseConstants.BaseBioType.FACE_BIO_TYPE);
        //
        // } else if (BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE.equals(bioType)) {
        //
        // // 下发可见光人脸一体化模板（有模块先下模板，没有再下图像）
        // boolean isViFaceExecute = attDevCmdManager.sendMultiViFaceDataToDev(attDevice, bioTemplateItemList,
        // devOptionList, BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
        // // 下发人员生物图像信息(没有模版的时候下发图像)
        // if (sendBioPhoto && !isViFaceExecute) {
        // attDevCmdManager.sendMultiFaceBioPhotoToDev(attDevice, pin, devOptionList,
        // BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
        // }
        // } else {
        // // 其他统一走一样逻辑，先下发模板，没有模板下发比对照片，方法内部会判断是否支持下发
        // boolean isSendMultiBioData =
        // attDevCmdManager.sendMultiBioDataToDev(attDevice, bioTemplateItemList, devOptionList, bioType);
        //
        // if (sendBioPhoto && !isSendMultiBioData && attBioPhotoItemList != null) {
        // // 当前类型的模板
        // List<AttBioPhotoItem> bioPhotoItemList = attBioPhotoItemList.stream()
        // .filter(i -> i.getType().equals(bioType)).collect(Collectors.toList());
        // attDevCmdManager.sendMultiBioPhotoToDev(attDevice, bioPhotoItemList, devOptionList, bioType);
        // }
        // }
        // }

        // 下发近红外一体化指纹模板
        attDevCmdManager.sendMultiFPDataToDev(attDevice, attBioTemplateItemList, devOptionList,
            BaseConstants.BaseBioType.FP_BIO_TYPE);

        // 下发近红外人脸一体化模板
        attDevCmdManager.sendMultiFaceDataToDev(attDevice, attBioTemplateItemList, devOptionList,
            BaseConstants.BaseBioType.FACE_BIO_TYPE);

        // 下发可见光人脸一体化模板（有模块先下模板，没有再下图像）
        boolean isViFaceExecute = attDevCmdManager.sendMultiViFaceDataToDev(attDevice, attBioTemplateItemList, devOptionList,
            BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
        // 下发人员生物图像信息(没有模版的时候下发图像)
        if (sendBioPhoto & !isViFaceExecute) {
            attDevCmdManager.sendMultiFaceBioPhotoToDev(attDevice, pin, devOptionList,
                    BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
        }

        // 下发掌静脉一体化模版
        attDevCmdManager.sendMultiBioDataToDev(attDevice, attBioTemplateItemList, devOptionList,
            BaseConstants.BaseBioType.PALM_BIO_TYPE);

        // 下发可见光手掌
        boolean isSendMultiBioData = attDevCmdManager.sendMultiBioDataToDev(attDevice, attBioTemplateItemList, devOptionList,
            CmdAttConstants.TEMPLATE_VISILIGHT_PALM);
        if (sendBioPhoto & !isSendMultiBioData) {
            attDevCmdManager.sendMultiBioPhotoToDev(attDevice, attBioPhotoItemList, devOptionList,
                    CmdAttConstants.TEMPLATE_VISILIGHT_PALM);
        }

        // 下发虹膜
        attDevCmdManager.sendMultiBioDataToDev(attDevice, attBioTemplateItemList, devOptionList, CmdAttConstants.TEMPLATE_IRIS);
    }

    /**
     * 发一体化模板，或发比对照片 人员同步先下发人脸模版，人脸模版下发成功，就不下发比对照片
     * 
     * @param sendBioPhoto: 是否下发比对照片（防止设备上来BioPhoto和BioData导致重复下发BioPhoto，如果是设备上来的，不进行BioPhoto的下发）
     */
    private void sendBioToDevice(List<AttBioTemplateItem> attBioTemplateItemList,
        List<AttDeviceOptionItem> devOptionList, String pin, AttDevice attDevice, boolean sendBioPhoto) {
        // 下发指纹模板
        attDevCmdManager.sendPersonFPToDev(attDevice, attBioTemplateItemList, devOptionList);

        // 下发一体化掌静脉模版
        attDevCmdManager.sendPVBioTemplateToDev(attDevice, attBioTemplateItemList, devOptionList);

        // 下发人脸模板
        boolean isSendFaceToDev = attDevCmdManager.sendPersonFaceToDev(attDevice, attBioTemplateItemList, devOptionList);

        // 下发对比照片（可见光未做一体化标准，本质属于生物模板下发范畴）
        if (sendBioPhoto && !isSendFaceToDev) {
            attDevCmdManager.sendBioPhotoToDev(attDevice.getDevSn(), pin, devOptionList);
        }
    }

    // 组装人员对应的生物模板列表
    private List<AttBioTemplateItem> getPersonBioTemplate(List<PersBioTemplateItem> personBioTempList) {
        List<AttBioTemplateItem> persBioTemplateList = new ArrayList<>();
        if (Objects.nonNull(personBioTempList)) {
            for (PersBioTemplateItem item : personBioTempList) {
                AttBioTemplateItem attBioTemplate = new AttBioTemplateItem();
                // 生物模板拼凑
                attBioTemplate.setPersonPin(item.getPersonPin());
                attBioTemplate.setBioType(item.getBioType());
                attBioTemplate.setTemplateNo(item.getTemplateNo());
                attBioTemplate.setTemplateNoIndex(item.getTemplateNoIndex());
                attBioTemplate.setValidType(item.getValidType());
                attBioTemplate.setTemplate(item.getTemplate());
                attBioTemplate.setVersion(item.getVersion());
                attBioTemplate.setDuress(item.getDuress());
                persBioTemplateList.add(attBioTemplate);
            }
        }
        return persBioTemplateList;
    }
}
