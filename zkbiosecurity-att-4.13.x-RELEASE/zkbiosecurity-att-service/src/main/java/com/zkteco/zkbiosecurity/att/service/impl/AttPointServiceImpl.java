/**
 * File Name: AttPointServiceImpl Created by GenerationTools on 2018-02-08 下午08:27 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.cache.AttCacheManager;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttPointDao;
import com.zkteco.zkbiosecurity.att.model.AttPoint;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthSessionServcie;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.license.provider.BaseLicenseProvider;
import com.zkteco.zkbiosecurity.system.service.ModuleInfoService;

/**
 * 考勤点
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:50
 * @since 1.0.0
 */
@Slf4j
@Service
public class AttPointServiceImpl implements AttPointService {

    @Autowired
    private AttPointDao attPointDao;
    @Autowired
    private ModuleInfoService moduleInfoService;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private AttAccDeviceService attAccDeviceService;
    @Autowired
    private AttParkDeviceService attParkDeviceService;
    @Autowired
    private AttInsDeviceService attInsDeviceService;
    @Autowired
    private AttPidDeviceService attPidDeviceService;
    @Autowired
    private AttVmsDeviceService attVmsDeviceService;
    @Autowired
    private AttPsgDeviceService attPsgDeviceService;
    @Autowired(required = false)
    private AuthSessionServcie authSessionServcie;
    @Autowired
    private BaseLicenseProvider baseLicenseProvider;
    @Autowired
    private AttCacheManager attCacheManager;
    @Autowired
    private AttPointPullTransactionTask attPointPullTransactionTask;
    @Autowired
    private AttLicensePointsCheckService attLicensePointsCheckService;
    @Autowired
    private AttIvsDeviceService attIvsDeviceService;
    @Autowired(required = false)
    private AttGetEsdcDataService attGetEsdcDataService;

    @Override
    public boolean isExistAcc() {
        return moduleInfoService.isExistModuleByCode(ConstUtil.SYSTEM_MODULE_ACC);
    }

    @Override
    public boolean isExistPark() {
        return moduleInfoService.isExistModuleByCode(ConstUtil.SYSTEM_MODULE_PARK);
    }

    @Override
    public boolean isExistIns() {
        return moduleInfoService.isExistModuleByCode(ConstUtil.SYSTEM_MODULE_INS);
    }

    @Override
    public boolean isExistPid() {
        return moduleInfoService.isExistModuleByCode(ConstUtil.SYSTEM_MODULE_IDENTIFICATION);
    }

    @Override
    @Transactional
    public AttPointItem saveItem(AttPointItem item) {
        AttPoint attPoint = Optional.ofNullable(item).map(AttPointItem::getId).filter(StringUtils::isNotBlank)
            .flatMap(attPointDao::findById).filter(Objects::nonNull).orElse(new AttPoint());
        ModelUtil.copyPropertiesIgnoreNull(item, attPoint);

        // 考勤点新增保存前检验是否已添加过该设备,防止重复添加. by ljf 2020/8/7
        if (StringUtils.isBlank(item.getId())) {
            AttPointItem condition = new AttPointItem();
            condition.setEquals(true);
            switch (item.getDeviceModule()) {
                case ConstUtil.SYSTEM_MODULE_ACC:
                case ConstUtil.SYSTEM_MODULE_VMS:
                    condition.setDeviceSn(item.getDeviceSn());
                    condition.setDoorNo(item.getDoorNo());
                    break;
                case ConstUtil.SYSTEM_MODULE_PARK:
                case ConstUtil.SYSTEM_MODULE_IVS:
                case ConstUtil.SYSTEM_MODULE_PASSAGE:
                case ConstUtil.LICENSE_MODULE_ESDC:
                    condition.setDeviceId(item.getDeviceId());
                    break;
                case ConstUtil.SYSTEM_MODULE_IDENTIFICATION:
                case ConstUtil.SYSTEM_MODULE_INS:
                    condition.setDeviceSn(item.getDeviceSn());
                    break;
                default:
                    break;
            }
            if (!CollectionUtil.isEmpty(getByCondition(condition))) {
                throw ZKBusinessException.errorException(I18nUtil.i18nCode("common_dev_devHasAdd"));
            }
        }
        if (!ConstUtil.SYSTEM_MODULE_PASSAGE.equals(item.getDeviceModule())) {
            attPoint.setRecordType(null);
        }
        attPointDao.save(attPoint);
        // 更新许可
        boolean res = attLicensePointsCheckService.update();
        if (!res) {
            throw ZKBusinessException.errorException(I18nUtil.i18nCode("common_dev_maxCount"));
        }
        // 缓存考勤点以及时间戳,用于定时获取第三方记录
        setPointPullTransactionCache(item);
        item.setId(attPoint.getId());
        return item;
    }

    /**
     * 缓存考勤点以及时间戳,用于定时获取第三方记录
     *
     * @param attPointItem
     */
    private void setPointPullTransactionCache(AttPointItem attPointItem) {
        String deviceModule = attPointItem.getDeviceModule();
        String key = deviceModule;
        if (ConstUtil.SYSTEM_MODULE_PARK.equals(deviceModule)) {
            switch (attPointItem.getStatus()) {
                // 入:1或3
                case 1:
                case 3:
                    key += ":in";
                    break;
                // 出:2或4
                case 2:
                case 4:
                    key += ":out";
                    break;
                default:
                    break;
            }
        }

        String pointPullTransaction = attCacheManager.getPointPullTransaction(key);
        JSONObject jsonObject = new JSONObject();
        if (StringUtils.isBlank(pointPullTransaction)) {
            jsonObject.put("deviceIds", attPointItem.getDeviceId());
            jsonObject.put("timestamp", "");
        } else {
            jsonObject = JSONObject.parseObject(pointPullTransaction);
            jsonObject.put("deviceIds", strIdsAddId(jsonObject.getString("deviceIds"), attPointItem.getDeviceId()));
        }
        attCacheManager.setPointPullTransaction(key, jsonObject.toJSONString());
    }

    /**
     * 字符串ids增加新的id
     *
     * @param ids
     * @param id
     * @return
     */
    private String strIdsAddId(String ids, String id) {
        if (StringUtils.isBlank(id)) {
            return ids;
        }
        if (StringUtils.isBlank(ids)) {
            return null == id ? "" : id;
        }
        Set<String> set = new HashSet<>(Arrays.asList(ids.split(AttConstant.COMMA)));
        set.add(id);
        return String.join(AttConstant.COMMA, set);
    }

    /**
     * 根据条件查询
     *
     * @param condition
     * @return
     */
    @Override
    public List<AttPointItem> getByCondition(AttPointItem condition) {
        List<AttPointItem> attPointItems =
            (List<AttPointItem>)attPointDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
        return attPointItems;
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        if (condition.getSortName() != null) {
            if ("deviceName".equals(condition.getSortName())) {
                condition.setSortName("doorNo");
            }
        }

        Pager pager = attPointDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        buildArea(pager.getData());
        buildDeviceInfo((List<AttPointItem>)pager.getData());
        return pager;
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                // 先删除缓存
                AttPointItem attPointItem = getItemById(id);
                delPointPullTransactionCache(attPointItem);
                attPointDao.deleteById(id);
                // 更新许可
                attLicensePointsCheckService.update();
            }
        }
        return false;
    }

    /**
     * 删除考勤点缓存
     *
     * @param attPointItem
     */
    private void delPointPullTransactionCache(AttPointItem attPointItem) {
        String deviceModule = attPointItem.getDeviceModule();
        String key = deviceModule;
        if (ConstUtil.SYSTEM_MODULE_PARK.equals(deviceModule)) {
            switch (attPointItem.getStatus()) {
                // 入:1或3
                case 1:
                case 3:
                    key += ":in";
                    break;
                // 出:2或4
                case 2:
                case 4:
                    key += ":out";
                    break;
                default:
                    break;
            }
        }

        String pointPullTransaction = attCacheManager.getPointPullTransaction(key);
        if (StringUtils.isBlank(pointPullTransaction)) {
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(pointPullTransaction);
        String deviceIds = strIdsRemoveId(jsonObject.getString("deviceIds"), attPointItem.getDeviceId());
        if (StringUtils.isBlank(deviceIds)) {
            // 如果删除设备后,该模块都没有其他设备了,顺带key也删除
            attCacheManager.delPointPullTransaction(key);
        } else {
            jsonObject.put("deviceIds", deviceIds);
            attCacheManager.setPointPullTransaction(key, jsonObject.toJSONString());
        }
    }

    /**
     * 字符串ids删除指定id
     *
     * @param ids
     * @param id
     * @return
     */
    private String strIdsRemoveId(String ids, String id) {
        if (StringUtils.isBlank(id)) {
            return ids;
        }
        if (StringUtils.isBlank(ids)) {
            return "";
        }
        Set<String> set = new HashSet<>(Arrays.asList(ids.split(AttConstant.COMMA)));
        set.remove(id);
        return String.join(AttConstant.COMMA, set);
    }

    @Override
    public AttPointItem getItemById(String id) {
        if (StringUtils.isNotBlank(id)) {
            AttPoint attPoint = attPointDao.findById(id).orElse(null);
            if (Objects.nonNull(attPoint)) {
                AttPointItem attPointItem = new AttPointItem();
                ModelUtil.copyProperties(attPoint, attPointItem);
                String deviceId = attPointItem.getDeviceId();
                if (ConstUtil.SYSTEM_MODULE_ACC.equals(attPointItem.getDeviceModule())) {
                    if (Objects.nonNull(attAccDeviceService)) {
                        AttSelectDoorItem accDoorItem = attAccDeviceService.getAccDeviceByDeviceId(deviceId);
                        if (Objects.nonNull(accDoorItem)) {
                            attPointItem.setDeviceName(accDoorItem.getDoorName());
                        }
                    } else {
                        attPointItem.setDeviceName("");
                    }
                    AuthAreaItem authArea = authAreaService.getItemById(attPointItem.getAreaId());
                    attPointItem.setAreaName(authArea.getName());
                }
                if (ConstUtil.SYSTEM_MODULE_PARK.equals(attPointItem.getDeviceModule())) {
                    if (Objects.nonNull(attParkDeviceService)) {
                        AttParkDeviceSelectItem parkDeviceItem = attParkDeviceService.getParkDeviceById(deviceId);
                        if (Objects.nonNull(parkDeviceItem)) {
                            attPointItem.setDeviceName(parkDeviceItem.getName());
                        }
                    } else {
                        attPointItem.setDeviceName("");
                    }
                    AttParkAreaItem parkEntranceAreaItem =
                        attParkDeviceService.getParkAreaByAreaId(attPointItem.getAreaId());
                    attPointItem.setAreaName(parkEntranceAreaItem.getName());
                }
                if (ConstUtil.SYSTEM_MODULE_INS.equals(attPointItem.getDeviceModule())) {
                    if (Objects.nonNull(attInsDeviceService)) {
                        AttInsDeviceSelectItem insDeviceItem = attInsDeviceService.getInsDeviceByDeviceId(deviceId);
                        if (Objects.nonNull(insDeviceItem)) {
                            attPointItem.setDeviceName(insDeviceItem.getDevName());
                        }
                    } else {
                        attPointItem.setDeviceName("");
                    }
                    AuthAreaItem authArea = authAreaService.getItemById(attPointItem.getAreaId());
                    attPointItem.setAreaName(authArea.getName());
                }
                if (ConstUtil.SYSTEM_MODULE_IDENTIFICATION.equals(attPointItem.getDeviceModule())) {
                    if (Objects.nonNull(attPidDeviceService)) {
                        AttPidDeviceSelectItem pisDeviceItem = attPidDeviceService.getPidDeviceByDeviceId(deviceId);
                        if (Objects.nonNull(pisDeviceItem)) {
                            attPointItem.setDeviceName(pisDeviceItem.getName());
                        }
                    }
                    AuthAreaItem authArea = authAreaService.getItemById(attPointItem.getAreaId());
                    attPointItem.setAreaName(authArea.getName());
                } else if (ConstUtil.SYSTEM_MODULE_VMS.equals(attPointItem.getDeviceModule())) {
                    if (Objects.nonNull(attVmsDeviceService)) {
                        AttVmsDeviceSelectItem attVmsDeviceSelectItem =
                            attVmsDeviceService.getVmsDeviceByDeviceId(deviceId);
                        if (Objects.nonNull(attVmsDeviceSelectItem)) {
                            attPointItem.setDeviceName(
                                attVmsDeviceSelectItem.getDeviceName() + "-" + attVmsDeviceSelectItem.getChannelName());
                        }
                    }
                    AuthAreaItem authArea = authAreaService.getItemById(attPointItem.getAreaId());
                    attPointItem.setAreaName(authArea.getName());
                } else if (ConstUtil.SYSTEM_MODULE_IVS.equals(attPointItem.getDeviceModule())) {
                    if (Objects.nonNull(attIvsDeviceService)) {
                        AttIvsDeviceSelectItem attIvsDeviceSelectItem =
                                attIvsDeviceService.getIvsDeviceByDeviceId(deviceId);
                        if (Objects.nonNull(attIvsDeviceSelectItem)) {
                            // 通道ip-name 作为设备名称
                            attPointItem.setDeviceName(
                                    attIvsDeviceSelectItem.getIp() + "-" + attIvsDeviceSelectItem.getDeviceName());
                        }
                    }
                    AuthAreaItem authArea = authAreaService.getItemById(attPointItem.getAreaId());
                    attPointItem.setAreaName(authArea.getName());
                } else if (ConstUtil.LICENSE_MODULE_ESDC.equals(attPointItem.getDeviceModule())) {
                    if (Objects.nonNull(attGetEsdcDataService)) {
                        log.info("attGetEsdcDataService.getChannelItemByChannelId start deviceId = {}", deviceId);
                        Att4EsdcChannelSelectItem esdcChannelSelectItem =
                                attGetEsdcDataService.getChannelItemByChannelId(deviceId);
                        log.info("attGetEsdcDataService.getChannelItemByChannelId end esdcChannelSelectItem = {}", JSON.toJSONString(esdcChannelSelectItem));
                        if (Objects.nonNull(esdcChannelSelectItem)) {
                            attPointItem.setDeviceName(esdcChannelSelectItem.getName());
                        }
                    }
                    AuthAreaItem authArea = authAreaService.getItemById(attPointItem.getAreaId());
                    attPointItem.setAreaName(authArea.getName());
                }  else if (ConstUtil.SYSTEM_MODULE_PASSAGE.equals(attPointItem.getDeviceModule())) {
                    if (Objects.nonNull(attPsgDeviceService)) {
                        List<AttPsgDeviceSelectItem> attPsgDeviceSelectItems =
                                attPsgDeviceService.getPsgDeviceByDeviceIds(Arrays.asList(deviceId.split(",")));
                        if (Objects.nonNull(attPsgDeviceSelectItems)) {
                            AttPsgDeviceSelectItem attPsgDeviceSelectItem = attPsgDeviceSelectItems.get(0);
                            // 通道ip-name 作为设备名称
                            attPointItem.setDeviceName(
                                    attPsgDeviceSelectItem.getDeviceAlias() + "-" + attPsgDeviceSelectItem.getName());
                        }
                    }
                    AuthAreaItem authArea = authAreaService.getItemById(attPointItem.getAreaId());
                    attPointItem.setAreaName(authArea.getName());
                }
                return attPointItem;
            }
        }
        return null;
    }

    /**
     * 区域权限过滤
     *
     * @param sessionId
     * @param condition
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/5/21 19:39
     */
    private void buildAreaCondition(String sessionId, AttPointItem condition) {
        // 如果不是管理员则取出拥有权限的区域id集合和所有停车区域id集合（停车拥有自己的区域，停车区域不进行权限过滤）
        // 如果是管理员则不过滤 updated by jinxian.huang 2019-05-30
        SecuritySubject subject = authSessionServcie.getSecuritySubject(sessionId);
        if (!subject.getIsSuperuser().booleanValue()) {
            // 根据用户id获取区域
            List<AuthAreaItem> authAreaItemList = authAreaService.findByUserId(subject.getUserId());
            // 获取所有停车区域
            List<AttParkAreaItem> attParkAreaItemList = attParkDeviceService.getParkAreaByAreaAll();
            Collection<String> aredIds =
                CollectionUtil.getPropertyList(attParkAreaItemList, AttParkAreaItem::getId, "-1");
            if (!CollectionUtil.isEmpty(authAreaItemList)) {
                Collection<String> authAreaIds =
                    CollectionUtil.getPropertyList(authAreaItemList, AuthAreaItem::getId, "-1");
                aredIds = CollectionUtils.union(aredIds, authAreaIds);
            }
            condition.setInAreaId(StringUtils.join(aredIds, ","));
            condition.setAreaId(null);
        }
    }

    /***
     * 封装区域数据
     *
     * @param items
     * @return
     */
    private List<?> buildArea(List<?> items) {
        List<AttPointItem> list = (List<AttPointItem>)items;
        Map<String, List<AttPointItem>> collect =
            list.stream().collect(Collectors.groupingBy(AttPointItem::getDeviceModule));
        collect.forEach((k, v) -> {
            if (CollectionUtil.isEmpty(v)) {
                return;
            }
            List<String> areaIdList = (List<String>)CollectionUtil.getPropertyList(v, AttPointItem::getAreaId, "-1");
            // 门禁/信息屏/人证/视频/通道 区域
            if (ConstUtil.SYSTEM_MODULE_ACC.equals(k) || ConstUtil.SYSTEM_MODULE_INS.equals(k)
                || ConstUtil.SYSTEM_MODULE_IDENTIFICATION.equals(k) || ConstUtil.SYSTEM_MODULE_VMS.equals(k)
                    || ConstUtil.SYSTEM_MODULE_IVS.equals(k) || ConstUtil.SYSTEM_MODULE_PASSAGE.equals(k)
                    || ConstUtil.LICENSE_MODULE_ESDC.equals(k)) {
                List<AuthAreaItem> areaItems = authAreaService.getItemsByIds(areaIdList);
                Map<String, AuthAreaItem> areaMap = CollectionUtil.itemListToIdMap(areaItems);
                v.forEach(item -> {
                    AuthAreaItem area = areaMap.get(item.getAreaId());
                    item.setAreaName(area.getName());
                });
            }
            // 停车区域取的是进出口区域
            else if (ConstUtil.SYSTEM_MODULE_PARK.equals(k)) {
                List<AttParkAreaItem> areaItems = attParkDeviceService.getParkAreaByAreaIds(areaIdList);
                Map<String, AttParkAreaItem> areaItemMap = CollectionUtil.itemListToIdMap(areaItems);
                v.forEach(item -> {
                    AttParkAreaItem parkEntranceAreaItem = areaItemMap.get(item.getAreaId());
                    item.setAreaName(Objects.nonNull(parkEntranceAreaItem) ? parkEntranceAreaItem.getName() : "");
                });
            }
        });
        return items;
    }

    /**
     * 构建设备信息
     *
     * @param list
     * @return
     */
    private List<AttPointItem> buildDeviceInfo(List<AttPointItem> list) {

        Map<String, JSONObject> allCacheAttPointMap = attCacheManager.getAllPointPullTransaction();

        Map<String, List<AttPointItem>> collect =
            list.stream().collect(Collectors.groupingBy(AttPointItem::getDeviceModule));
        collect.forEach((k, v) -> {
            if (StringUtils.isBlank(k) || CollectionUtil.isEmpty(v)) {
                return;
            }
            Collection<String> deviceIds = CollectionUtil.getPropertyList(v, AttPointItem::getDeviceId, "-1");
            // 门禁
            if (ConstUtil.SYSTEM_MODULE_ACC.equals(k)) {
                if (Objects.nonNull(attAccDeviceService)) {
                    List<AttSelectDoorItem> doorItems = attAccDeviceService.getAccDeviceByDeviceIds(deviceIds);
                    Map<String, AttSelectDoorItem> doorMap = CollectionUtil.itemListToIdMap(doorItems);
                    // 获取最近数据拉取时间
                    JSONObject jsonObject = allCacheAttPointMap.get(ConstUtil.SYSTEM_MODULE_ACC);
                    Date lastTransactionTime = null;
                    if (jsonObject != null && StringUtils.isNotBlank(jsonObject.getString("timestamp"))) {
                        lastTransactionTime = new Date(jsonObject.getLong("timestamp"));
                    }
                    for (AttPointItem item : v) {
                        AttSelectDoorItem doorItem = doorMap.get(item.getDeviceId());
                        item.setDeviceName(Objects.isNull(doorItem) ? "" : doorItem.getDoorName());
                        item.setDeviceModule(I18nUtil.i18nCode("acc_module"));
                        item.setLastTransactionTime(lastTransactionTime);
                    }
                }
            }
            // 停车
            else if (ConstUtil.SYSTEM_MODULE_PARK.equals(k)) {
                if (Objects.nonNull(attParkDeviceService)) {
                    List<AttParkDeviceSelectItem> parkDeviceItems = attParkDeviceService.getParkDeviceByIds(deviceIds);
                    Map<String, AttParkDeviceSelectItem> parkDeviceItemMap =
                        CollectionUtil.listToKeyMap(parkDeviceItems, AttParkDeviceSelectItem::getChannelId);
                    // 获取最近数据拉取时间
                    JSONObject jsonObjectIn = allCacheAttPointMap.get(ConstUtil.SYSTEM_MODULE_PARK + ":in");
                    Date lastTransactionTimeIn = null;
                    String deviceIdsIn = null;
                    if (jsonObjectIn != null && StringUtils.isNotBlank(jsonObjectIn.getString("timestamp"))) {
                        lastTransactionTimeIn = new Date(jsonObjectIn.getLong("timestamp"));
                        deviceIdsIn = jsonObjectIn.getString("deviceIds");
                    }
                    JSONObject jsonObjectOut = allCacheAttPointMap.get(ConstUtil.SYSTEM_MODULE_PARK + ":out");
                    Date lastTransactionTimeOut = null;
                    String deviceIdsOut = null;
                    if (jsonObjectOut != null && StringUtils.isNotBlank(jsonObjectOut.getString("timestamp"))) {
                        lastTransactionTimeOut = new Date(jsonObjectOut.getLong("timestamp"));
                        deviceIdsOut = jsonObjectOut.getString("deviceIds");
                    }
                    for (AttPointItem item : v) {
                        AttParkDeviceSelectItem parkDeviceItem = parkDeviceItemMap.get(item.getDeviceId());
                        item.setDeviceName(Objects.isNull(parkDeviceItem) ? "" : parkDeviceItem.getChannelName());
                        item.setDeviceModule(I18nUtil.i18nCode("park_module"));
                        // TODO status目前为空，无法判断，暂时用DeviceId判断
                        /*Short status = parkDeviceItem.getStatus();
                        if (status == 1 || status == 3) {
                            item.setLastTransactionTime(lastTransactionTimeIn);
                        } else if (status == 2 || status == 4) {
                            item.setLastTransactionTime(lastTransactionTimeOut);
                        }*/
                        if (StringUtils.isNotBlank(deviceIdsIn) && deviceIdsIn.contains(item.getDeviceId())) {
                            item.setLastTransactionTime(lastTransactionTimeIn);
                        } else if (StringUtils.isNotBlank(deviceIdsOut) && deviceIdsOut.contains(item.getDeviceId())) {
                            item.setLastTransactionTime(lastTransactionTimeOut);
                        }
                    }
                }
            }
            // 信息屏
            else if (ConstUtil.SYSTEM_MODULE_INS.equals(k)) {
                if (Objects.nonNull(attInsDeviceService)) {
                    List<AttInsDeviceSelectItem> insDeviceItems =
                        attInsDeviceService.getInsDeviceByDeviceIds(deviceIds);
                    Map<String, AttInsDeviceSelectItem> insDeviceItemMap =
                        CollectionUtil.itemListToIdMap(insDeviceItems);
                    // 获取最近数据拉取时间
                    JSONObject jsonObject = allCacheAttPointMap.get(ConstUtil.SYSTEM_MODULE_INS);
                    Date lastTransactionTime = null;
                    if (jsonObject != null && StringUtils.isNotBlank(jsonObject.getString("timestamp"))) {
                        lastTransactionTime = new Date(jsonObject.getLong("timestamp"));
                    }
                    for (AttPointItem item : v) {
                        AttInsDeviceSelectItem insDeviceItem = insDeviceItemMap.get(item.getDeviceId());
                        item.setDeviceName(Objects.isNull(insDeviceItem) ? "" : insDeviceItem.getDevName());
                        item.setDeviceModule(I18nUtil.i18nCode("ins_module"));
                        item.setLastTransactionTime(lastTransactionTime);
                    }
                }
            }
            // 人证
            else if (ConstUtil.SYSTEM_MODULE_IDENTIFICATION.equals(k)) {
                if (Objects.nonNull(attPidDeviceService)) {
                    List<AttPidDeviceSelectItem> pidDeviceItemList =
                        attPidDeviceService.getPidDeviceByDeviceIds(deviceIds);
                    Map<String, AttPidDeviceSelectItem> pidDeviceItemMap =
                        CollectionUtil.itemListToIdMap(pidDeviceItemList);
                    // 获取最近数据拉取时间
                    JSONObject jsonObject = allCacheAttPointMap.get(ConstUtil.SYSTEM_MODULE_IDENTIFICATION);
                    Date lastTransactionTime = null;
                    if (jsonObject != null && StringUtils.isNotBlank(jsonObject.getString("timestamp"))) {
                        lastTransactionTime = new Date(jsonObject.getLong("timestamp"));
                    }
                    for (AttPointItem item : v) {
                        AttPidDeviceSelectItem pidDeviceItem = pidDeviceItemMap.get(item.getDeviceId());
                        if (Objects.nonNull(pidDeviceItem)) {
                            item.setDeviceName(pidDeviceItem.getName());
                        } else {
                            item.setDeviceName("");
                        }
                        item.setDeviceModule(I18nUtil.i18nCode("pid_module"));
                        item.setLastTransactionTime(lastTransactionTime);
                    }
                }
            } else if (ConstUtil.SYSTEM_MODULE_VMS.equals(k)) {
                if (Objects.nonNull(attVmsDeviceService)) {
                    List<AttVmsDeviceSelectItem> attVmsDeviceSelectItems =
                        attVmsDeviceService.getVmsDeviceByDeviceIds(deviceIds);
                    Map<String, AttVmsDeviceSelectItem> vmsDeviceSelectItemMap =
                        CollectionUtil.itemListToIdMap(attVmsDeviceSelectItems);
                    // 获取最近数据拉取时间
                    JSONObject jsonObject = allCacheAttPointMap.get(ConstUtil.SYSTEM_MODULE_VMS);
                    Date lastTransactionTime = null;
                    if (jsonObject != null && StringUtils.isNotBlank(jsonObject.getString("timestamp"))) {
                        lastTransactionTime = new Date(jsonObject.getLong("timestamp"));
                    }
                    for (AttPointItem item : v) {
                        AttVmsDeviceSelectItem attVmsDeviceSelectItem = vmsDeviceSelectItemMap.get(item.getDeviceId());
                        if (attVmsDeviceSelectItem != null) {
                            // 视频当考勤点，设备名称改为 “设备名-通道名” by ljf 2020/1/20
                            item.setDeviceName(
                                attVmsDeviceSelectItem.getDeviceName() + "-" + attVmsDeviceSelectItem.getChannelName());
                        } else {
                            item.setDeviceName("");
                        }
                        item.setDeviceModule(I18nUtil.i18nCode("vms_module"));
                        item.setLastTransactionTime(lastTransactionTime);
                    }
                }
            } else if (ConstUtil.SYSTEM_MODULE_IVS.equals(k)) {
                // add by justin.kang 2020-8-13 vid模块的封装数据
                if (Objects.nonNull(attIvsDeviceService)) {
                    List<AttIvsDeviceSelectItem> atVidDeviceSelectItemList =
                            attIvsDeviceService.getIvsDeviceByDeviceIds(deviceIds);
                    Map<String, AttIvsDeviceSelectItem> atVidDeviceSelectItemMap =
                            CollectionUtil.listToKeyMap(atVidDeviceSelectItemList, AttIvsDeviceSelectItem::getId);
                    // 获取最近数据拉取时间
                    JSONObject jsonObject = allCacheAttPointMap.get(ConstUtil.SYSTEM_MODULE_IVS);
                    Date lastTransactionTime = null;
                    if (jsonObject != null && StringUtils.isNotBlank(jsonObject.getString("timestamp"))) {
                        lastTransactionTime = new Date(jsonObject.getLong("timestamp"));
                    }
                    for (AttPointItem item : v) {
                        AttIvsDeviceSelectItem atVidDeviceSelectItem = atVidDeviceSelectItemMap.get(item.getDeviceId());
                        if (atVidDeviceSelectItem != null) {
                            // 视频当考勤点，设备名称改为 “通道名”
                            item.setDeviceName(atVidDeviceSelectItem.getName());
                        } else {
                            item.setDeviceName("");
                        }
                        item.setDeviceModule(I18nUtil.i18nCode("ivs_module"));
                        item.setLastTransactionTime(lastTransactionTime);
                    }
                }
            } else if (ConstUtil.LICENSE_MODULE_ESDC.equals(k)) {
                if (Objects.nonNull(attGetEsdcDataService)) {
                    log.info("attGetEsdcDataService.getChannelItemListByChannelIds start deviceIds = {}", StringUtils.join(deviceIds, ","));
                    List<Att4EsdcChannelSelectItem> esdcChannelSelectItemList =
                            attGetEsdcDataService.getChannelItemListByChannelIds(deviceIds);
                    log.info("attGetEsdcDataService.getChannelItemListByChannelIds end esdcChannelSelectItemList = {}", JSON.toJSONString(esdcChannelSelectItemList));
                    Map<String, Att4EsdcChannelSelectItem> esdcChannelSelectItemMap =
                            CollectionUtil.listToKeyMap(esdcChannelSelectItemList, Att4EsdcChannelSelectItem::getId);
                    // 获取最近数据拉取时间
                    JSONObject jsonObject = allCacheAttPointMap.get(ConstUtil.LICENSE_MODULE_ESDC);
                    Date lastTransactionTime = null;
                    if (jsonObject != null && StringUtils.isNotBlank(jsonObject.getString("timestamp"))) {
                        lastTransactionTime = new Date(jsonObject.getLong("timestamp"));
                    }
                    for (AttPointItem item : v) {
                        Att4EsdcChannelSelectItem esdcChannelSelectItem = esdcChannelSelectItemMap.get(item.getDeviceId());
                        if (esdcChannelSelectItem != null) {
                            // 视频当考勤点，设备名称改为 “通道名”
                            item.setDeviceName(esdcChannelSelectItem.getName());
                        } else {
                            item.setDeviceName("");
                        }
                        item.setDeviceModule(I18nUtil.i18nCode("esdc_module"));
                        item.setLastTransactionTime(lastTransactionTime);
                    }
                }
            } else if (ConstUtil.SYSTEM_MODULE_PASSAGE.equals(k)) {
                if (Objects.nonNull(attPsgDeviceService)) {
                    List<AttPsgDeviceSelectItem> attPsgDeviceSelectItems =
                        attPsgDeviceService.getPsgDeviceByDeviceIds(deviceIds);
                    Map<String, AttPsgDeviceSelectItem> psgDeviceSelectItemMap =
                        CollectionUtil.itemListToIdMap(attPsgDeviceSelectItems);
                    // 获取最近数据拉取时间
                    JSONObject jsonObject = allCacheAttPointMap.get(ConstUtil.SYSTEM_MODULE_PASSAGE);
                    Date lastTransactionTime = null;
                    if (jsonObject != null && StringUtils.isNotBlank(jsonObject.getString("timestamp"))) {
                        lastTransactionTime = new Date(jsonObject.getLong("timestamp"));
                    }
                    for (AttPointItem item : v) {
                        AttPsgDeviceSelectItem attPsgDeviceSelectItem = psgDeviceSelectItemMap.get(item.getDeviceId());
                        if (attPsgDeviceSelectItem != null) {
                            // 通道当考勤点，设备名称改为 “设备名-闸名”
                            item.setDeviceName(
                                attPsgDeviceSelectItem.getDeviceAlias() + "-" + attPsgDeviceSelectItem.getName());
                        } else {
                            item.setDeviceName("");
                        }
                        item.setDeviceModule(I18nUtil.i18nCode("psg_module"));
                        item.setLastTransactionTime(lastTransactionTime);
                    }
                }
            }
        });

        return list;
    }

    @Override
    public List<String> getDeviceIdsByDeviceModule(String deviceModule) {
        List<AttPoint> deviceModuleList = attPointDao.findByDeviceModule(deviceModule);
        List<String> rest = new ArrayList<String>();
        deviceModuleList.forEach(item -> {
            rest.add(item.getDeviceId());
        });
        return rest;
    }

    @Override
    public boolean isExist(String pointName) {
        AttPoint attPoint = attPointDao.findByPointName(pointName);
        if (attPoint == null) {
            return true;
        }
        return false;
    }

    @Override
    public ZKResultMsg checkLicenseCount(String deviceModule) {
        return attLicensePointsCheckService.check();
    }

    @Override
    public List<TreeItem> getAllParkEntranceArea() {
        List<TreeItem> treeItemList = new ArrayList<>();
        if (Objects.nonNull(attParkDeviceService)) {
            List<AttParkAreaItem> parkAreaByAreaAll = attParkDeviceService.getParkAreaByAreaAll();
            if (!CollectionUtil.isEmpty(parkAreaByAreaAll)) {
                TreeItem treeItem = null;
                for (AttParkAreaItem parkEntranceAreaItem : parkAreaByAreaAll) {
                    treeItem = new TreeItem();
                    treeItem.setId(parkEntranceAreaItem.getId());
                    treeItem.setText(parkEntranceAreaItem.getName());
                    treeItem.setParent(new TreeItem("0"));
                    treeItemList.add(treeItem);
                }
            }
        }
        return treeItemList;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Pager loadSelectPagerByAuthFilter(String session, AttPointSelectItem condition, int pageNo, int pageSize) {
        // sessionId 权限过滤
        AttSelectPointItem attSelectPointItem = new AttSelectPointItem();
        ModelUtil.copyProperties(condition, attSelectPointItem);
        Pager pager = attPointDao.getItemsBySql(attSelectPointItem.getClass(), SQLUtil.getSqlByItem(attSelectPointItem),
            pageNo, pageSize);
        List<AttSelectPointItem> attSelectPointItemList = (List<AttSelectPointItem>)pager.getData();
        Map<String, List<AttSelectPointItem>> attSelectPointMap =
            attSelectPointItemList.stream().collect(Collectors.groupingBy(AttSelectPointItem::getDeviceModule));
        attSelectPointMap.forEach((deviceModule, attSelectPointItems) -> {
            if (!CollectionUtil.isEmpty(attSelectPointItems)) {
                switch (deviceModule) {
                    case ConstUtil.SYSTEM_MODULE_IDENTIFICATION:
                        attPointSelectByPid(attSelectPointItems);// 人证
                        break;
                    case ConstUtil.SYSTEM_MODULE_INS:
                        attPointSelectByIns(attSelectPointItems);// 信息屏
                        break;
                    case ConstUtil.SYSTEM_MODULE_ACC:
                        attPointSelectByAcc(attSelectPointItems);// 门禁
                        break;
                    case ConstUtil.SYSTEM_MODULE_PARK:
                        attPointSelectByPark(attSelectPointItems);// 停车
                        break;
                    case ConstUtil.SYSTEM_MODULE_VMS:
                        attPointSelectByVms(attSelectPointItems);
                        break;
                    case ConstUtil.SYSTEM_MODULE_IVS:
                        attPointSelectByIvs(attSelectPointItems);
                        break;
                    case ConstUtil.LICENSE_MODULE_ESDC:
                        attPointSelectByEsdc(attSelectPointItems);
                        break;
                    case ConstUtil.SYSTEM_MODULE_PASSAGE:
                        attPointSelectByPsg(attSelectPointItems);
                        break;
                    default:
                        break;
                }
            }
        });
        pager.setData(ModelUtil.copyListProperties(attSelectPointItemList, AttPointSelectItem.class));
        return pager;
    }

    @Override
    public int getCountByDeviceModule(String deviceModule) {
        return attPointDao.getCountByDeviceModule(deviceModule);
    }

    @Override
    public int getPointCount() {
        return (int)attPointDao.count();
    }

    @Override
    public AttPointItem getItemByDeviceSn(String deviceSn) {
        if (StringUtils.isNotBlank(deviceSn)) {
            AttPoint attPoint = attPointDao.findByDeviceSn(deviceSn);
            if (Objects.nonNull(attPoint)) {
                AttPointItem attPointItem = new AttPointItem();
                ModelUtil.copyProperties(attPoint, attPointItem);
                return attPointItem;
            }
        }
        return null;
    }

    @Override
    public List<AttPointItem> getItemsByDeviceSn(String deviceSn) {
        List<AttPoint> attPointList = attPointDao.getAttPointByDeviceSn(deviceSn);
        List<AttPointItem> attPointItemList = new ArrayList<>();
        if (!CollectionUtil.isEmpty(attPointList)) {
            attPointItemList = ModelUtil.copyListProperties(attPointList, AttPointItem.class);
        }
        return attPointItemList;
    }

    @Override
    public AttPointItem getItemByDeviceId(String deviceId) {
        if (StringUtils.isNotBlank(deviceId)) {
            AttPoint attPoint = attPointDao.findByDeviceId(deviceId);
            if (Objects.nonNull(attPoint)) {
                AttPointItem attPointItem = new AttPointItem();
                ModelUtil.copyProperties(attPoint, attPointItem);
                buildDeviceInfo(Collections.singletonList(attPointItem));
                return attPointItem;
            }
        }
        return null;
    }

    @Override
    public AttPointItem getItemByDeviceSnAndDoorNo(String deviceSn, Short doorNo) {
        if (StringUtils.isNotBlank(deviceSn) && Objects.nonNull(doorNo)) {
            AttPoint attPoint = attPointDao.findByDeviceSnAndDoorNo(deviceSn, doorNo);
            if (Objects.nonNull(attPoint)) {
                AttPointItem attPointItem = new AttPointItem();
                ModelUtil.copyProperties(attPoint, attPointItem);
                buildDeviceInfo(Collections.singletonList(attPointItem));
                return attPointItem;
            }
        }
        return null;
    }

    @Override
    public Pager loadPagerByAuthFilter(String sessionId, AttPointItem condition, int pageNo, int pageSize) {
        buildAreaCondition(sessionId, condition);
        return getItemsByPage(condition, pageNo, pageSize);
    }

    @Override
    @Transactional
    public void handlerTransfer(List<AttPointItem> attPointItems) {
        // 考勤点不会太多，不需要批量处理 数据迁移,迁移的是门禁模块
        List<AttPoint> attPointList = new ArrayList<>();
        for (AttPointItem attPointItem : attPointItems) {
            // SN不唯一，和门编号才唯一，解决ByDeviceSn查询如果多条返回报错。 by ljf 2019/9/29
            AttPoint attPoint =
                attPointDao.findByDeviceSnAndDoorNo(attPointItem.getDeviceSn(), attPointItem.getDoorNo());
            if (Objects.isNull(attPoint)) {
                attPoint = new AttPoint();
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(attPointItem, attPoint, "id");
            AuthAreaItem authAreaItem = authAreaService.getItemByCode(attPointItem.getAreaId());
            attPoint.setAreaId(authAreaItem.getId());
            attPoint.setDeviceModule("acc");
            attPointList.add(attPoint);
        }
        attPointDao.saveAll(attPointList);
    }

    /**
     * 考勤点列表 人证
     *
     * @param attSelectPointItems
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/4/23 10:51
     */
    private void attPointSelectByPid(List<AttSelectPointItem> attSelectPointItems) {
        Collection<String> deviceIdList = CollectionUtil.getPropertyList(attSelectPointItems,
            AttSelectPointItem::getDeviceId, AttConstant.COMM_DEF_VALUE);
        if (Objects.nonNull(attPidDeviceService)) {
            List<AttPidDeviceSelectItem> pidDeviceItemList = attPidDeviceService.getPidDeviceByDeviceIds(deviceIdList);
            Map<String, AttPidDeviceSelectItem> pidDeviceItemMap = CollectionUtil.itemListToIdMap(pidDeviceItemList);
            attSelectPointItems.forEach(attSelectPoint -> {
                AttPidDeviceSelectItem pidDeviceItem = pidDeviceItemMap.get(attSelectPoint.getDeviceId());
                if (Objects.nonNull(pidDeviceItem)) {
                    attSelectPoint.setDeviceName(pidDeviceItem.getName());
                } else {
                    attSelectPoint.setDeviceName("");
                }
                attSelectPoint.setDeviceModule(I18nUtil.i18nCode("pid_module"));
            });
        }
    }

    /**
     * 考勤点列表 信息屏
     *
     * @param attSelectPointItems
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/4/23 11:23
     */
    private void attPointSelectByIns(List<AttSelectPointItem> attSelectPointItems) {
        Collection<String> deviceIdList = CollectionUtil.getPropertyList(attSelectPointItems,
            AttSelectPointItem::getDeviceId, AttConstant.COMM_DEF_VALUE);
        if (Objects.nonNull(attInsDeviceService)) {
            List<AttInsDeviceSelectItem> insDeviceSelectItemList =
                attInsDeviceService.getInsDeviceByDeviceIds(deviceIdList);
            Map<String, AttInsDeviceSelectItem> insDeviceItemMap =
                CollectionUtil.itemListToIdMap(insDeviceSelectItemList);
            attSelectPointItems.forEach(attSelectPoint -> {
                AttInsDeviceSelectItem insDeviceSelectItem = insDeviceItemMap.get(attSelectPoint.getDeviceId());
                if (Objects.nonNull(insDeviceSelectItem)) {
                    attSelectPoint.setDeviceName(insDeviceSelectItem.getDevName());
                } else {
                    attSelectPoint.setDeviceName("");
                }
                attSelectPoint.setDeviceModule(I18nUtil.i18nCode("ins_module"));
            });
        }
    }

    /**
     * 考勤点列表 门禁
     *
     * @param attSelectPointItems
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/4/23 11:23
     */
    private void attPointSelectByAcc(List<AttSelectPointItem> attSelectPointItems) {
        Collection<String> deviceIdList = CollectionUtil.getPropertyList(attSelectPointItems,
            AttSelectPointItem::getDeviceId, AttConstant.COMM_DEF_VALUE);
        if (Objects.nonNull(attAccDeviceService)) {
            List<AttSelectDoorItem> accDoorSelectItemList = attAccDeviceService.getAccDeviceByDeviceIds(deviceIdList);
            Map<String, AttSelectDoorItem> accDoorItemMap = CollectionUtil.itemListToIdMap(accDoorSelectItemList);
            attSelectPointItems.forEach(attSelectPoint -> {
                AttSelectDoorItem accDoorSelectItem = accDoorItemMap.get(attSelectPoint.getDeviceId());
                if (Objects.nonNull(accDoorSelectItem)) {
                    attSelectPoint.setDeviceName(accDoorSelectItem.getDoorName());
                } else {
                    attSelectPoint.setDeviceName("");
                }
                attSelectPoint.setDeviceModule(I18nUtil.i18nCode("acc_module"));
            });
        }
    }

    /**
     * 填充停车考勤点信息
     *
     * @param attSelectPointItems
     * @return void
     * @date 2019/4/23 11:23
     */
    private void attPointSelectByPark(List<AttSelectPointItem> attSelectPointItems) {
        Collection<String> deviceIdList = CollectionUtil.getPropertyList(attSelectPointItems,
            AttSelectPointItem::getDeviceId, AttConstant.COMM_DEF_VALUE);
        if (Objects.nonNull(attParkDeviceService)) {
            List<AttParkDeviceSelectItem> attParkDeviceSelectItemList =
                attParkDeviceService.getParkDeviceByIds(deviceIdList);
            Map<String, AttParkDeviceSelectItem> attParkDeviceSelectItemMap =
                CollectionUtil.listToKeyMap(attParkDeviceSelectItemList, AttParkDeviceSelectItem::getChannelId);
            attSelectPointItems.forEach(attSelectPoint -> {
                AttParkDeviceSelectItem attParkDeviceSelectItem =
                    attParkDeviceSelectItemMap.get(attSelectPoint.getDeviceId());
                if (Objects.nonNull(attParkDeviceSelectItem)) {
                    attSelectPoint.setDeviceName(attParkDeviceSelectItem.getChannelName());
                } else {
                    attSelectPoint.setDeviceName("");
                }
                attSelectPoint.setDeviceModule(I18nUtil.i18nCode("park_module"));
            });
        }
    }

    @Override
    public boolean isExistVms() {
        return moduleInfoService.isExistModuleByCode(ConstUtil.SYSTEM_MODULE_VMS);
    }

    /**
     * 查询 VMS类型考勤点
     *
     * @param attSelectPointItems
     */
    private void attPointSelectByVms(List<AttSelectPointItem> attSelectPointItems) {
        Collection<String> deviceIdList = CollectionUtil.getPropertyList(attSelectPointItems,
            AttSelectPointItem::getDeviceId, AttConstant.COMM_DEF_VALUE);
        if (Objects.nonNull(attVmsDeviceService)) {
            List<AttVmsDeviceSelectItem> vmsDeviceItemList = attVmsDeviceService.getVmsDeviceByDeviceIds(deviceIdList);
            Map<String, AttVmsDeviceSelectItem> vmsDeviceItemMap = CollectionUtil.itemListToIdMap(vmsDeviceItemList);
            attSelectPointItems.forEach(attSelectPoint -> {
                AttVmsDeviceSelectItem vmsDeviceItem = vmsDeviceItemMap.get(attSelectPoint.getDeviceId());
                if (Objects.nonNull(vmsDeviceItem)) {
                    // 视频当考勤点，设备名称改为 “设备名-通道名” by ljf 2020/1/20
                    attSelectPoint.setDeviceName(vmsDeviceItem.getDeviceName() + "-" + vmsDeviceItem.getChannelName());
                } else {
                    attSelectPoint.setDeviceName("");
                }
                attSelectPoint.setDeviceModule(I18nUtil.i18nCode("vms_module"));
            });
        }
    }

    /**
     * 填充ivs当考勤点信息
     *
     * @param attSelectPointItems
     */
    private void attPointSelectByIvs(List<AttSelectPointItem> attSelectPointItems) {
        Collection<String> deviceIdList = CollectionUtil.getPropertyList(attSelectPointItems,
                AttSelectPointItem::getDeviceId, AttConstant.COMM_DEF_VALUE);
        if (Objects.nonNull(attIvsDeviceService)) {
            List<AttIvsDeviceSelectItem> deviceItems = attIvsDeviceService.getIvsDeviceByDeviceIds(deviceIdList);
            Map<String, AttIvsDeviceSelectItem> deviceSelectItemMap = CollectionUtil.itemListToIdMap(deviceItems);
            attSelectPointItems.forEach(attSelectPoint -> {
                AttIvsDeviceSelectItem deviceSelectItem = deviceSelectItemMap.get(attSelectPoint.getDeviceId());
                if (Objects.nonNull(deviceSelectItem)) {
                    attSelectPoint.setDeviceName(deviceSelectItem.getIp() + "-" + deviceSelectItem.getName());
                } else {
                    attSelectPoint.setDeviceName("");
                }
                attSelectPoint.setDeviceModule(I18nUtil.i18nCode("ivs_module"));
            });
        }
    }

    private void attPointSelectByEsdc(List<AttSelectPointItem> attSelectPointItems) {
        Collection<String> deviceIdList = CollectionUtil.getPropertyList(attSelectPointItems,
                AttSelectPointItem::getDeviceId, AttConstant.COMM_DEF_VALUE);
        if (Objects.nonNull(attGetEsdcDataService)) {
            log.info("attGetEsdcDataService.getChannelItemListByChannelIds start deviceIdList = {}", StringUtils.join(deviceIdList, ","));
            List<Att4EsdcChannelSelectItem> deviceItems = attGetEsdcDataService.getChannelItemListByChannelIds(deviceIdList);
            log.info("attGetEsdcDataService.getChannelItemListByChannelIds end deviceItems = {}", JSON.toJSONString(deviceItems));
            Map<String, Att4EsdcChannelSelectItem> deviceSelectItemMap = CollectionUtil.itemListToIdMap(deviceItems);
            attSelectPointItems.forEach(attSelectPoint -> {
                Att4EsdcChannelSelectItem deviceSelectItem = deviceSelectItemMap.get(attSelectPoint.getDeviceId());
                if (Objects.nonNull(deviceSelectItem)) {
                    attSelectPoint.setDeviceName(deviceSelectItem.getName());
                } else {
                    attSelectPoint.setDeviceName("");
                }
                attSelectPoint.setDeviceModule(I18nUtil.i18nCode("esdc_module"));
            });
        }
    }

    /**
     * 填充通道当考勤点信息
     *
     * @param attSelectPointItems:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/30 18:00
     * @since 1.0.0
     */
    private void attPointSelectByPsg(List<AttSelectPointItem> attSelectPointItems) {
        Collection<String> deviceIds = CollectionUtil.getPropertyList(attSelectPointItems,
                AttSelectPointItem::getDeviceId, AttConstant.COMM_DEF_VALUE);
        if (Objects.nonNull(attPsgDeviceService)) {
            List<AttPsgDeviceSelectItem> deviceItems =
                    attPsgDeviceService.getPsgDeviceByDeviceIds(deviceIds);
            Map<String, AttPsgDeviceSelectItem> deviceSelectItemMap = CollectionUtil.itemListToIdMap(deviceItems);
            attSelectPointItems.forEach(attSelectPoint -> {
                AttPsgDeviceSelectItem deviceSelectItem = deviceSelectItemMap.get(attSelectPoint.getDeviceId());
                if (Objects.nonNull(deviceSelectItem)) {
                    attSelectPoint.setDeviceName(deviceSelectItem.getDeviceAlias() + "-" + deviceSelectItem.getName());
                } else {
                    attSelectPoint.setDeviceName("");
                }
                attSelectPoint.setDeviceModule(I18nUtil.i18nCode("psg_module"));
            });
        }
    }

    @Override
    public List<AttPointItem> getItemData(Class targetClass, AttPointItem codition, int begin, int end) {
        List<AttPointItem> items =
            attPointDao.getItemsDataBySql(targetClass, SQLUtil.getSqlByItem(codition), begin, end, true);
        buildArea(items);
        buildDeviceInfo(items);
        return items;
    }

    @Override
    public void initAttPointTransaction() {

        // 删除所有缓存考勤点
        Map<String, JSONObject> allPointPullTransaction = attCacheManager.getAllPointPullTransaction();
        Map<String, String> keyTimestampMap = new HashMap<>();
        if (!CollectionUtil.isEmpty(allPointPullTransaction)) {
            Iterator<Map.Entry<String, JSONObject>> iterator = allPointPullTransaction.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, JSONObject> next = iterator.next();
                String key = next.getKey();
                JSONObject jsonObject = next.getValue();
                String timestamp = MapUtils.getString(jsonObject, "timestamp");
                if (StringUtils.isNotBlank(timestamp)) {
                    keyTimestampMap.put(key, timestamp);
                }
                attCacheManager.delPointPullTransaction(key);
            }
        }
        // 查找所有考勤点
        List<AttPointItem> attPointItems = getByCondition(new AttPointItem());
        if (CollectionUtil.isEmpty(attPointItems)) {
            return;
        }
        // 分组缓存
        Map<String, List<AttPointItem>> keyPointMap =
            attPointItems.stream().collect(Collectors.groupingBy(item -> getAttPointKey(item)));
        for (Map.Entry<String, List<AttPointItem>> entry : keyPointMap.entrySet()) {
            String key = entry.getKey();
            String deviceIds = CollectionUtil.getPropertys(entry.getValue(), AttPointItem::getDeviceId);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("deviceIds", deviceIds);
            jsonObject.put("timestamp", MapUtils.getString(keyTimestampMap, key, ""));
            attCacheManager.setPointPullTransaction(key, jsonObject.toJSONString());
        }
    }

    @Override
    public String getAttPointKey(AttPointItem attPointItem) {
        String module = attPointItem.getDeviceModule();
        if (ConstUtil.SYSTEM_MODULE_PARK.equals(module)) {
            switch (attPointItem.getStatus()) {
                // 入:1或3
                case 1:
                case 3:
                    module += ":in";
                    break;
                // 出:2或4
                case 2:
                case 4:
                    module += ":out";
                    break;
                default:
                    break;
            }
        }
        return module;
    }

    @Override
    public void startPointPullTransactionTask() {
        // 考勤点定时获取记录
        attPointPullTransactionTask.startTask();
    }

    @Override
    public boolean existOtherAttModule() {
        List<String> otherBeAttCodeList = new ArrayList<>();
        otherBeAttCodeList.add(ConstUtil.SYSTEM_MODULE_ACC);
        otherBeAttCodeList.add(ConstUtil.SYSTEM_MODULE_INS);
        otherBeAttCodeList.add(ConstUtil.SYSTEM_MODULE_PARK);
        otherBeAttCodeList.add(ConstUtil.SYSTEM_MODULE_IDENTIFICATION);
        otherBeAttCodeList.add(ConstUtil.SYSTEM_MODULE_VMS);
        otherBeAttCodeList.add(ConstUtil.SYSTEM_MODULE_IVS);
        otherBeAttCodeList.add(ConstUtil.SYSTEM_MODULE_PASSAGE);
        // 后续新增模块当考勤需要再这加上模块编码...
        for (String moduleCode : otherBeAttCodeList) {
            if (moduleInfoService.isExistModuleByCode(moduleCode)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean isExistPsg() {
        return moduleInfoService.isExistModuleByCode(ConstUtil.SYSTEM_MODULE_PASSAGE);
    }

    @Override
    public List<SelectItem> getPointModule() {
        List<SelectItem> selectItems = new ArrayList<>();
        Map<String, JSONObject> allCacheAttPointMap = attCacheManager.getAllPointPullTransaction();
        Set<String> keyset = allCacheAttPointMap.keySet();
        if (Objects.nonNull(keyset)) {
            Object[] moduleAry = keyset.toArray();
            for (Object object : moduleAry) {
                if (Objects.isNull(object)) {
                    continue;
                }
                String pointModuleCode = object.toString();
                if (pointModuleCode.indexOf(ConstUtil.SYSTEM_MODULE_ACC) >= 0) {
                    selectItems.add(new SelectItem(I18nUtil.i18nCode("att_attPoint_acc"), ConstUtil.SYSTEM_MODULE_ACC));
                } else if (pointModuleCode.indexOf(ConstUtil.SYSTEM_MODULE_PARK) >= 0) {
                    selectItems
                        .add(new SelectItem(I18nUtil.i18nCode("att_attPoint_park"), ConstUtil.SYSTEM_MODULE_PARK));
                } else if (pointModuleCode.indexOf(ConstUtil.SYSTEM_MODULE_INS) >= 0) {
                    selectItems.add(new SelectItem(I18nUtil.i18nCode("att_attPoint_ins"), ConstUtil.SYSTEM_MODULE_INS));
                } else if (pointModuleCode.indexOf(ConstUtil.SYSTEM_MODULE_IDENTIFICATION) >= 0) {
                    selectItems.add(
                        new SelectItem(I18nUtil.i18nCode("att_attPoint_pid"), ConstUtil.SYSTEM_MODULE_IDENTIFICATION));
                } else if (pointModuleCode.indexOf(ConstUtil.SYSTEM_MODULE_VMS) >= 0) {
                    selectItems.add(new SelectItem(I18nUtil.i18nCode("att_attPoint_vms"), ConstUtil.SYSTEM_MODULE_VMS));
                } else if (pointModuleCode.indexOf(ConstUtil.SYSTEM_MODULE_IVS) >= 0) {
                    selectItems.add(new SelectItem(I18nUtil.i18nCode("ivs_module"), ConstUtil.SYSTEM_MODULE_IVS));
                }  else if (pointModuleCode.indexOf(ConstUtil.LICENSE_MODULE_ESDC) >= 0) {
                    selectItems.add(new SelectItem(I18nUtil.i18nCode("esdc_module"), ConstUtil.LICENSE_MODULE_ESDC));
                } else if (pointModuleCode.indexOf(ConstUtil.SYSTEM_MODULE_PASSAGE) >= 0) {
                    selectItems.add(new SelectItem(I18nUtil.i18nCode("att_attPoint_psg"), ConstUtil.SYSTEM_MODULE_PASSAGE));
                }
            }
        }
        return selectItems;
    }

    @Override
    public boolean isExistIvs() {
        return moduleInfoService.isExistModuleByCode(ConstUtil.SYSTEM_MODULE_IVS);
    }

    @Override
    public boolean isExistEsdc() {
        return moduleInfoService.isExistModuleByCode(ConstUtil.LICENSE_MODULE_ESDC);
    }

    @Override
    public Pager getEsdcChannelSelectList(AttEsdcChannelSelectItem condition, int pageNo, int pageSize) {
        if (Objects.nonNull(attGetEsdcDataService)) {
            Att4EsdcChannelSelectItem att4EsdcChannelSelectItem = new Att4EsdcChannelSelectItem();
            ModelUtil.copyPropertiesIgnoreNull(condition, att4EsdcChannelSelectItem);
            // 过滤已选的和已存在的
            List<String> existChannelIds = getDeviceIdsByDeviceModule(ConstUtil.LICENSE_MODULE_ESDC);
            String existChannelIdsStr = StringUtils.join(existChannelIds, AttConstant.COMMA);
            if (StringUtils.isNotBlank(existChannelIdsStr)) {
                att4EsdcChannelSelectItem.setIdNotIn(condition.getSelectId() + AttConstant.COMMA + existChannelIdsStr);
            } else {
                att4EsdcChannelSelectItem.setIdNotIn(condition.getSelectId());
            }

            log.info("attGetEsdcDataService.getChannelItemList start att4EsdcChannelSelectItem = {}, pageNo = {}, pageSize = {}", JSON.toJSONString(att4EsdcChannelSelectItem), pageNo, pageSize);
            Pager pager = attGetEsdcDataService.getChannelItemList(att4EsdcChannelSelectItem, pageNo, pageSize);
            log.info("attGetEsdcDataService.getChannelItemLis end pager = {}", JSON.toJSONString(pager));

            // 对象转换
            List<Att4EsdcChannelSelectItem> att4EsdcChannelSelectItemList = (List<Att4EsdcChannelSelectItem>)pager.getData();
            List<AttEsdcChannelSelectItem> attEsdcChannelSelectItemList =
                    ModelUtil.copyListProperties(att4EsdcChannelSelectItemList, AttEsdcChannelSelectItem.class);
            pager.setData(attEsdcChannelSelectItemList);
            return pager;
        } else {
            Pager pager = new Pager(pageNo, pageSize);
            pager.setData(new ArrayList<>());
            return pager;
        }
    }
}