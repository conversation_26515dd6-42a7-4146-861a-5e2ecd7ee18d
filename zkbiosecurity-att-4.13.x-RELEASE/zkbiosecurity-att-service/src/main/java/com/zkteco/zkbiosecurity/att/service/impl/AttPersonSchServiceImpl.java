package com.zkteco.zkbiosecurity.att.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.api.vo.*;
import com.zkteco.zkbiosecurity.att.bean.AttPersonAttendanceBean;
import com.zkteco.zkbiosecurity.att.bean.AttRuleParamBean;
import com.zkteco.zkbiosecurity.att.calc.bo.AttLeaveBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttOvertimeBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttTimeSlotBO;
import com.zkteco.zkbiosecurity.att.constants.AppConstant;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttCommonSchConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.*;
import com.zkteco.zkbiosecurity.att.model.AttPerson;
import com.zkteco.zkbiosecurity.att.model.AttPersonSch;
import com.zkteco.zkbiosecurity.att.model.AttRecord;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.service.AuthSessionServcie;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.bean.Tree;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.format.TreeBuilder;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.config.DataSourceConfig;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 人员排班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:39
 * @since 1.0.0
 */
@Service
public class AttPersonSchServiceImpl implements AttPersonSchService {

    @Autowired
    private AttPersonSchDao attPersonSchDao;
    @Autowired
    private AttTempSchDao attTempSchDao;
    @Autowired
    private AttShiftDao attShiftDao;
    @Autowired
    private AttTimeSlotService attTimeSlotService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AttPersonDao attPersonDao;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttSignService attSignService;
    @Autowired
    private AttRecordDao attRecordDao;
    @Autowired
    private AttPersonSchDataService attPersonSchDataService;
    @Autowired
    private AttCycleSchDao attCycleSchDao;
    @Autowired
    private AttLeaveService attLeaveService;
    @Autowired
    private AttOvertimeService attOvertimeService;
    @Autowired
    private AttHolidayService attHolidayService;
    @Autowired
    private AttAdjustService attAdjustService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;
    @Autowired
    private AttRecordService attRecordService;
    @Autowired
    private AttTransactionService attTransactionService;
    @Autowired
    private AttAttendanceCalculateService attAttendanceCalculateService;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttClassService attClassService;
    @Autowired
    private AuthSessionServcie authSessionServcie;

    @Override
    @Transactional
    public AttPersonSchItem saveItem(AttPersonSchItem item) {
        AttPersonSch attPersonSch =
            attPersonSchDao.findByScheduleIdAndScheduleType(item.getScheduleId(), item.getScheduleType());
        if (Objects.isNull(attPersonSch)) {
            attPersonSch = new AttPersonSch();
        }
        ModelUtil.copyPropertiesIgnoreNull(item, attPersonSch);
        attPersonSchDao.save(attPersonSch);
        item.setId(attPersonSch.getId());
        return item;
    }

    @Override
    public List<AttPersonSchItem> getByCondition(AttPersonSchItem condition) {
        return (List<AttPersonSchItem>)attPersonSchDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return attPersonSchDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                attPersonSchDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AttPersonSchItem getItemById(String id) {
        List<AttPersonSchItem> items = getByCondition(new AttPersonSchItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public ZKResultMsg getAttPersonSchJson(String personId, String dateSelected) {

        PersPersonItem persPersonItem = persPersonService.getSimpleItemById(personId);
        String pin = persPersonItem.getPin();

        int year = Integer.parseInt(dateSelected.split("-")[0]);
        int month = Integer.parseInt(dateSelected.split("-")[1]);
        Date startDate = null;
        Date endDate = null;
        // 当前选择月份前后多查询一个月，总共三个月
        if (month == 12) {
            startDate = AttDateUtils.getFirstDayOfMonth(year, 11);
            endDate = AttDateUtils.getLastDayOfMonth(year + 1, 1);
        } else if (month == 1) {
            startDate = AttDateUtils.getFirstDayOfMonth(year - 1, 12);
            endDate = AttDateUtils.getLastDayOfMonth(year, 2);
        } else {
            startDate = AttDateUtils.getFirstDayOfMonth(year, month - 1);
            endDate = AttDateUtils.getLastDayOfMonth(year, month + 1);
        }

        // 获取人员排班
        Map<String, List<AttPersonSchBO>> personSchDataMap =
            attPersonSchDataService.getPersonAllSchData(Arrays.asList(pin.split(",")), startDate, endDate);

        // 时间段集合
        List<AttTimeSlotItem> attTimeSlotItemList = attTimeSlotService.getByCondition(new AttTimeSlotItem());
        Map<String, AttTimeSlotItem> attTimeSlotItemMap = CollectionUtil.itemListToIdMap(attTimeSlotItemList);

        // 假种集合
        List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getByCondition(new AttLeaveTypeItem());
        Map<String, AttLeaveTypeItem> attLeaveTypeItemMap =
            CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getLeaveTypeNo);

        // 请假记录集合 Map(pin=date, List<AttLeaveBO>)
        Map<String, List<AttLeaveBO>> attLeaveMap = attLeaveService.getLeaveMap(Arrays.asList(pin), startDate, endDate);

        // 加班记录集合 Map(pin=date, List<AttOvertimeBO>)
        Map<String, List<AttOvertimeBO>> attOvertimeMap =
            attOvertimeService.getOvertimeMap(Arrays.asList(pin), startDate, endDate);

        // 调休记录集合 Map(pin=date, List<AttPersonSchBO>)
        Map<String, List<AttPersonSchBO>> attAdjustMap =
            attAdjustService.getAdjustMap(Arrays.asList(pin), startDate, endDate);

        // 节假日集合 set(date)
        Set<String> holidaySet = attHolidayService.getHolidayDateSet(startDate, endDate);

        // 调班集合
        Map<String, List<AttPersonSchBO>> attClassMap =
            attClassService.getClassMap(Arrays.asList(pin), startDate, endDate);

        // 跨天：0:记为第一日,1:记为第二日
        String crossDay = attParamService.getCrossDay();

        // 存储所有日期的时间段数据
        JSONArray attSchJsonArray = new JSONArray();
        // 存储所有日期的异常排班数据
        JSONArray attExceptionJsonArray = new JSONArray();
        for (Map.Entry<String, List<AttPersonSchBO>> entry : personSchDataMap.entrySet()) {
            String personKey = entry.getKey();
            String dateStr = personKey.split(AttCalculationConstant.KEY_CONNECTOR)[1];

            // 组装时间段数据
            List<AttPersonSchBO> attPersonSchBOList = personSchDataMap.get(personKey);
            JSONObject attSchJson = buildAttSchJson(attPersonSchBOList, dateStr, attTimeSlotItemMap, crossDay);
            attSchJsonArray.add(attSchJson);

            // 组装异常排班数据 节假日-调班-补班-调休-外出-出差-请假
            if (holidaySet.contains(dateStr)) {
                AttLeaveTypeItem holiday = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.HOLIDAY);
                // 节假日
                attExceptionJsonArray.add(buildExceptionJson(dateStr, holiday.getSymbol(), holiday.getLeaveTypeName()));
            } else if (attAdjustMap.containsKey(personKey)) {
                AttLeaveTypeItem tuneOff = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.TUNE_OFF);
                // 调休
                attExceptionJsonArray.add(buildExceptionJson(dateStr, tuneOff.getSymbol(), tuneOff.getLeaveTypeName()));
            } else if (attClassMap.containsKey(personKey)) {
                AttLeaveTypeItem attClass = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.CLASS);
                // 调班
                attExceptionJsonArray
                    .add(buildExceptionJson(dateStr, attClass.getSymbol(), attClass.getLeaveTypeName()));
            } else if (attLeaveMap.containsKey(personKey)) {
                List<AttLeaveBO> attLeaveBOList = attLeaveMap.get(personKey);
                if (attLeaveBOList.size() > 0) {
                    AttLeaveBO attLeaveBO = attLeaveBOList.get(0);
                    AttLeaveTypeItem leave = attLeaveTypeItemMap.get(attLeaveBO.getLeaveTypeNo());
                    // 请假、外出、出差
                    attExceptionJsonArray.add(buildExceptionJson(dateStr, leave.getSymbol(), leave.getLeaveTypeName()));
                }
            } else if (attOvertimeMap.containsKey(personKey)) {

                AttLeaveTypeItem overtime = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.OVERTIME);
                // 加班
                attExceptionJsonArray
                    .add(buildExceptionJson(dateStr, overtime.getSymbol(), overtime.getLeaveTypeName()));
            }
        }

        // 最终人员排班Json对象
        JSONObject attScheduleJson = new JSONObject();
        attScheduleJson.put("attSchJsonArray", attSchJsonArray);
        attScheduleJson.put("attExceptionJsonArray", attExceptionJsonArray);
        return new ZKResultMsg(attScheduleJson);

    }

    /**
     * 组装排班详情
     *
     * @param attPersonSchBOList
     * @param date
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/6/19 16:34
     */
    private JSONObject buildAttSchJson(List<AttPersonSchBO> attPersonSchBOList, String date,
        Map<String, AttTimeSlotItem> attTimeSlotItemMap, String crossDay) {

        JSONObject attSchJson = new JSONObject();
        attSchJson.put("date", date);
        int times = 0;
        JSONArray attTimeSlotJsonArray = new JSONArray();
        for (AttPersonSchBO attPersonSchBO : attPersonSchBOList) {
            List<AttTimeSlotBO> attTimeSlotBOList = attPersonSchBO.getAttTimeSlotArray();
            if (attTimeSlotBOList != null) {
                times += attTimeSlotBOList.size();
                for (AttTimeSlotBO attTimeSlotBO : attTimeSlotBOList) {
                    AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());
                    JSONObject attTimeSlotJson = new JSONObject();
                    attTimeSlotJson.put("periodName", attTimeSlotItem.getPeriodName());
                    attTimeSlotJson.put("times", attTimeSlotBOList.size());
                    if (AttConstant.PERIODTYPE_NORMAL.equals(attTimeSlotItem.getPeriodType())) {
                        String attTimeSlot = attTimeSlotItem.getToWorkTime() + "-" + attTimeSlotItem.getOffWorkTime();
                        attTimeSlotJson.put("attTimeSlotArray", Arrays.asList(attTimeSlot.split(",")));
                    } else {
                        String attTimeSlot =
                            attTimeSlotItem.getStartSignInTime() + "-" + attTimeSlotItem.getEndSignOffTime();
                        attTimeSlotJson.put("attTimeSlotArray", Arrays.asList(attTimeSlot.split(",")));
                    }
                    attTimeSlotJson.put("interDay", attTimeSlotBO.getIsInterDay());
                    attTimeSlotJson.put("timeSlotColor", "#66FF66");
                    // 班次类型：0:正常排班;1:临时排班
                    attTimeSlotJson.put("crossDay", crossDay);
                    attTimeSlotJsonArray.add(attTimeSlotJson);
                }
            }
        }
        attSchJson.put("times", times);
        attSchJson.put("attTimeSlotJsonArray", attTimeSlotJsonArray);
        return attSchJson;
    }

    /**
     * 组装异常排班信息
     *
     * @param dateStr
     * @param exceptionSymbol
     * @param exceptionValue
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/6/19 16:34
     */
    private JSONObject buildExceptionJson(String dateStr, String exceptionSymbol, String exceptionValue) {
        JSONObject attExceptionJson = new JSONObject();
        attExceptionJson.put("date", dateStr);
        attExceptionJson.put("exceptionSymbol", I18nUtil.i18nCode(exceptionSymbol));
        attExceptionJson.put("exceptionValue", I18nUtil.i18nCode(exceptionValue));
        return attExceptionJson;
    }

    private List<AuthDepartmentItem> getTopDept(List<AuthDepartmentItem> demoDepartmentItems) {
        List<AuthDepartmentItem> dept = new ArrayList<AuthDepartmentItem>();
        List<String> deptIds = (List<String>)CollectionUtil.getItemIdsList(demoDepartmentItems);
        demoDepartmentItems.forEach(item -> {
            if (item.getParentId() == null) {
                dept.add(item);
            } else {// 自定义用户
                if (!deptIds.contains(item.getParentId())) {
                    dept.add(item);
                }
            }
        });
        return dept;
    }

    @Override
    @Transactional
    public void deleteByScheduleId(String scheduleId) {
        attPersonSchDao.deleteByScheduleId(scheduleId);
    }

    @Override
    public List<AppAttPersonSchItem> getAttCalendarForApp(String personPin, String startDateStr, String endDateStr) {

        List<AppAttPersonSchItem> appAttPersonSchItemList = new ArrayList<>();
        Date startDate = AttDateUtils.stringToYmdDate(startDateStr);
        Date endDate = AttDateUtils.stringToYmdDate(endDateStr);

        // 获取考勤记录结果
        List<AttRecordItem> recordItemList =
            attRecordService.getByPinsAndAttDate(Arrays.asList(personPin), startDate, endDate);
        Map<Date, AttRecordItem> recordItemMap = CollectionUtil.listToKeyMap(recordItemList, AttRecordItem::getAttDate);

        // 防止补签、请假、加班跨天漏取记录
        Date dateBefore = DateUtil.getDateBefore(startDate, 1);
        Date dateAfter = DateUtil.getDateAfter(endDate, 1);

        // 查询补签记录 Map(pin=date, List<AttSignItem>)
        Map<String, List<AttSignItem>> attSignMap = getAttSignMapByPinAndDate(personPin, dateBefore, dateAfter);
        // 请假记录集合 Map(pin=date, List<AttLeaveBO>)
        Map<String, List<AttLeaveItem>> attLeaveMap = getAttLeaveMapByPinAndDate(personPin, dateBefore, dateAfter);
        // 加班记录集合 Map(pin=date, List<AttOvertimeBO>)
        Map<String, List<AttOvertimeItem>> attOvertimeMap =
            getAttOvertimeByPinAndDate(personPin, dateBefore, dateAfter);

        // 获取所有时间段集合(ID, Item)
        List<AttTimeSlotItem> attTimeSlotItems = attTimeSlotService.getAllTimeSlotItem();
        Map<String, AttTimeSlotItem> attTimeSlotItemMap =
            attTimeSlotItems.stream().collect(Collectors.toMap(AttTimeSlotItem::getId, Function.identity()));
        Map<String, List<String>> attTransactionMap =
            attTransactionService.getTransactionMap(dateBefore, dateAfter, Arrays.asList(personPin));
        // 请假记录集合 Map(pin=date, List<AttLeaveBO>)
        Map<String, List<AttLeaveBO>> attLeaveBOMap =
            attLeaveService.getLeaveMap(Arrays.asList(personPin), dateBefore, dateAfter);
        // 加班记录集合 Map(pin=date, List<AttOvertimeBO>)
        Map<String, List<AttOvertimeBO>> attOvertimeBOMap =
            attOvertimeService.getOvertimeMap(Arrays.asList(personPin), dateBefore, dateAfter);
        Map<String, List<AttPersonSchBO>> attPersonSchMap =
            attPersonSchDataService.getPersonAllSchData(Arrays.asList(personPin), startDate, endDate);

        // 获取人员信息
        AttPersonItem attPersonItem = attPersonService.getByPinFromPersonCache(personPin);

        // 循环天 填充数据
        AttDateUtils.forEachDay(startDate, endDate, date -> {
            String dateStr = AttDateUtils.dateToStrAsShort(date);
            AttRecordItem attRecordItem = recordItemMap.get(date);
            List<AttLeaveItem> attLeaveItems = attLeaveMap.get(dateStr);
            List<AttOvertimeItem> attOvertimeItems = attOvertimeMap.get(dateStr);
            // 处理跨天班补签多取前后一天
            List<AttSignItem> attSignItems = new ArrayList<>();
            List<AttSignItem> daySignItems = attSignMap.get(dateStr);
            if (!CollectionUtil.isEmpty(daySignItems)) {
                attSignItems.addAll(daySignItems);
            }
            daySignItems = attSignMap.get(AttDateUtils.getBeforeDay(dateStr));
            if (!CollectionUtil.isEmpty(daySignItems)) {
                attSignItems.addAll(daySignItems);
            }
            daySignItems = attSignMap.get(AttDateUtils.getNextDay(dateStr));
            if (!CollectionUtil.isEmpty(daySignItems)) {
                attSignItems.addAll(daySignItems);
            }

            String pinAndDate = personPin + AttCalculationConstant.KEY_CONNECTOR + dateStr;

            // 请假/出差/外出/加班信息
            Map<String, List<String>> perTransactionMap = new HashMap<>();
            List<AttLeaveBO> attLeaveBOList = new ArrayList<>();
            List<AttOvertimeBO> attOvertimeBOList = new ArrayList<>();
            // 取前后各一天共三天 给班次时段跨天或取卡范围跨天计算时判断
            String pinAndBeforeDay =
                personPin + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.getBeforeDay(dateStr);
            String pinAndNextDay = personPin + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.getNextDay(dateStr);

            for (String key : new String[] {pinAndBeforeDay, pinAndDate, pinAndNextDay}) {
                List<String> perTranList = attTransactionMap.get(key);
                if (null == perTranList) {
                    perTranList = new ArrayList<>();
                }
                perTransactionMap.put(key, perTranList);
                if (attLeaveBOMap.containsKey(key)) {
                    attLeaveBOList.addAll(attLeaveBOMap.get(key));
                }
                if (attOvertimeBOMap.containsKey(key)) {
                    attOvertimeBOList.addAll(attOvertimeBOMap.get(key));
                }
            }

            List<AttPersonSchBO> attPersonSchBOList = attPersonSchMap.get(personPin + "=" + dateStr);
            // 是否考勤（true:正常考勤/false:免打卡）,默认要考勤
            boolean isAttendance = Optional.ofNullable(attPersonItem.getIsAttendance()).orElse(true);
            AttRuleParamBean attRuleParamBean = attParamService.getRuleParam();

            AttPersonAttendanceBean attPersonAttendanceBean = new AttPersonAttendanceBean();
            attPersonAttendanceBean.setPinAndDate(pinAndDate);
            attPersonAttendanceBean.setIsAttendance(isAttendance);
            attPersonAttendanceBean.setAttRuleParamBean(attRuleParamBean);
            attPersonAttendanceBean.setAttTimeSlotItemMap(attTimeSlotItemMap);
            attPersonAttendanceBean.setPersonSchDataMap(attPersonSchBOList);
            attPersonAttendanceBean.setTransactionDataMap(perTransactionMap);
            attPersonAttendanceBean.setAttLeaveBOList(attLeaveBOList);
            attPersonAttendanceBean.setAttOvertimeBOList(attOvertimeBOList);

            // 当天之后的不需要计算,直接返回排班信息, 其他则按结果组装
            AppAttPersonSchItem appAttPersonSchItem = buildAppAttPersonSchItem(attPersonItem, date, attRecordItem,
                attSignItems, attLeaveItems, attOvertimeItems, attPersonSchMap, attPersonAttendanceBean);

            appAttPersonSchItemList.add(appAttPersonSchItem);
        });

        return appAttPersonSchItemList;
    }

    /**
     * 加班记录集合
     *
     * @param personPin:
     * @param dateBefore:
     * @param dateAfter:
     * @return java.util.Map<java.lang.String, java.util.List < com.zkteco.zkbiosecurity.att.vo.AttOvertimeItem>>
     * <AUTHOR>
     * @date 2020-12-09 16:05
     * @since 1.0.0
     */
    private Map<String, List<AttOvertimeItem>> getAttOvertimeByPinAndDate(String personPin, Date dateBefore,
        Date dateAfter) {
        AttOvertimeItem condition = new AttOvertimeItem();
        condition.setEquals(true);
        condition.setPersonPin(personPin);
        condition.setStartApplyDateTime(dateBefore);
        condition.setEndApplyDateTime(dateAfter);
        condition.setFlowStatusIn(AttConstant.FLOW_STATUS_COMPLETE + "," + AttConstant.FLOW_STATUS_CREATE);
        List<AttOvertimeItem> attOvertimeItemList = attOvertimeService.getByCondition(condition);
        Map<String, List<AttOvertimeItem>> attOvertimeMap = new HashMap<>();
        if (!CollectionUtil.isEmpty(attOvertimeItemList)) {
            for (AttOvertimeItem attOvertimeItem : attOvertimeItemList) {
                // 将记录拆分成天
                Date date1 = attOvertimeItem.getStartDatetime();
                Date date2;
                // 不同天则拆分
                while (!DateUtil.isSameDay(date1, attOvertimeItem.getEndDatetime())) {
                    // 结束时间为当天
                    date2 = DateUtil.getDayEndTime(date1);
                    AttOvertimeItem attLeaveItem1 = ModelUtil.copyProperties(attOvertimeItem, new AttOvertimeItem());
                    attLeaveItem1.setEndDatetime(date2);
                    String key = AttDateUtils.dateToStrAsShort(date1);
                    List<AttOvertimeItem> attOvertimeItems =
                        Optional.ofNullable(attOvertimeMap.get(key)).orElse(new ArrayList<>());
                    attOvertimeItems.add(attOvertimeItem);
                    attOvertimeMap.put(key, attOvertimeItems);
                    // 日期 + 1
                    date1 = DateUtil.getDayBeginTime(DateUtil.addDay(date1, 1));
                }
                date2 = attOvertimeItem.getEndDatetime();
                AttOvertimeItem attLeaveItem1 = ModelUtil.copyProperties(attOvertimeItem, new AttOvertimeItem());
                attLeaveItem1.setEndDatetime(date2);

                String key = AttDateUtils.dateToStrAsShort(date1);
                List<AttOvertimeItem> attOvertimeItems =
                    Optional.ofNullable(attOvertimeMap.get(key)).orElse(new ArrayList<>());
                attOvertimeItems.add(attOvertimeItem);
                attOvertimeMap.put(key, attOvertimeItems);
            }
        }
        return attOvertimeMap;
    }

    /**
     * 请假记录集合
     *
     * @param personPin:
     * @param dateBefore:
     * @param dateAfter:
     * @return java.util.Map<java.lang.String, java.util.List < com.zkteco.zkbiosecurity.att.vo.AttLeaveItem>>
     * <AUTHOR>
     * @date 2020-12-09 16:03
     * @since 1.0.0
     */
    private Map<String, List<AttLeaveItem>> getAttLeaveMapByPinAndDate(String personPin, Date dateBefore,
        Date dateAfter) {
        AttLeaveItem condition = new AttLeaveItem();
        condition.setEquals(true);
        condition.setPersonPin(personPin);
        condition.setStartApplyDateTime(dateBefore);
        condition.setEndApplyDateTime(dateAfter);
        condition.setFlowStatusIn(AttConstant.FLOW_STATUS_COMPLETE + "," + AttConstant.FLOW_STATUS_CREATE);
        List<AttLeaveItem> attLeaveItemList = attLeaveService.getByCondition(condition);
        Map<String, List<AttLeaveItem>> attLeaveMap = new HashMap<>();
        if (!CollectionUtil.isEmpty(attLeaveItemList)) {
            for (AttLeaveItem attLeaveItem : attLeaveItemList) {
                // 将记录拆分成天
                Date date1 = attLeaveItem.getStartDatetime();
                Date date2;
                // 不同天则拆分
                while (!DateUtil.isSameDay(date1, attLeaveItem.getEndDatetime())) {
                    // 结束时间为当天
                    date2 = DateUtil.getDayEndTime(date1);
                    AttLeaveItem attLeaveItem1 = ModelUtil.copyProperties(attLeaveItem, new AttLeaveItem());
                    attLeaveItem1.setEndDatetime(date2);
                    String key = AttDateUtils.dateToStrAsShort(date1);
                    List<AttLeaveItem> attLeaveItems =
                        Optional.ofNullable(attLeaveMap.get(key)).orElse(new ArrayList<>());
                    attLeaveItems.add(attLeaveItem);
                    attLeaveMap.put(key, attLeaveItems);
                    // 日期 + 1
                    date1 = DateUtil.getDayBeginTime(DateUtil.addDay(date1, 1));
                }
                date2 = attLeaveItem.getEndDatetime();
                AttLeaveItem attLeaveItem1 = ModelUtil.copyProperties(attLeaveItem, new AttLeaveItem());
                attLeaveItem1.setEndDatetime(date2);
                String key = AttDateUtils.dateToStrAsShort(date1);
                List<AttLeaveItem> attLeaveItems = Optional.ofNullable(attLeaveMap.get(key)).orElse(new ArrayList<>());
                attLeaveItems.add(attLeaveItem);
                attLeaveMap.put(key, attLeaveItems);
            }
        }
        return attLeaveMap;
    }

    /**
     * 查询补签记录
     *
     * @param personPin:
     * @param startDateTime:
     * @param endDateTime:
     * @return java.util.Map<java.lang.String, java.util.List < com.zkteco.zkbiosecurity.att.vo.AttSignItem>>
     * <AUTHOR>
     * @date 2020-12-09 16:00
     * @since 1.0.0
     */
    private Map<String, List<AttSignItem>> getAttSignMapByPinAndDate(String personPin, Date startDateTime,
        Date endDateTime) {
        AttSignItem condition = new AttSignItem();
        condition.setEquals(true);
        condition.setPersonPin(personPin);
        condition.setStartSignTime(startDateTime);
        condition.setEndSignTime(endDateTime);
        condition.setFlowStatusIn(AttConstant.FLOW_STATUS_COMPLETE + "," + AttConstant.FLOW_STATUS_CREATE);
        List<AttSignItem> attSignItemList = attSignService.getByCondition(condition);
        Map<String, List<AttSignItem>> attSignMap = new HashMap<>();
        if (!CollectionUtil.isEmpty(attSignItemList)) {
            for (AttSignItem attSignItem : attSignItemList) {
                String key = AttDateUtils.dateToStrAsShort(attSignItem.getSignDatetime());
                List<AttSignItem> attSignItems = Optional.ofNullable(attSignMap.get(key)).orElse(new ArrayList<>());
                attSignItems.add(attSignItem);
                attSignMap.put(key, attSignItems);
            }
        }
        return attSignMap;
    }

    /**
     * 封装单天的考勤状态排班之类的信息
     *
     * @param attPersonItem:
     * @param date:
     * @param attRecordItem:
     * @param attSignItems:
     * @param attLeaveItems:
     * @param attOvertimeItems:
     * @param attPersonSchMap:
     * @param attPersonAttendanceBean:
     * @return com.zkteco.zkbiosecurity.att.api.vo.AppAttPersonSchItem
     * <AUTHOR>
     * @date 2020-11-20 9:49
     * @since 1.0.0
     */
    private AppAttPersonSchItem buildAppAttPersonSchItem(AttPersonItem attPersonItem, Date date,
        AttRecordItem attRecordItem, List<AttSignItem> attSignItems, List<AttLeaveItem> attLeaveItems,
        List<AttOvertimeItem> attOvertimeItems, Map<String, List<AttPersonSchBO>> attPersonSchMap,
        AttPersonAttendanceBean attPersonAttendanceBean) {

        AppAttPersonSchItem appAttPersonSchItem;
        String dateStr = AttDateUtils.dateToStrAsShort(date);
        String personPin = attPersonItem.getPersonPin();
        Date hireDate = attPersonItem.getHireDate();
        Date leaveDate = attPersonItem.getLeaveDate();

        // 入职前和离职后日期的排班信息也置为空
        boolean isBeforeHireOrAfterLeave = false;
        if (null != hireDate && date.before(hireDate)) {
            isBeforeHireOrAfterLeave = true;
        }
        if (null != leaveDate && date.after(leaveDate)) {
            isBeforeHireOrAfterLeave = true;
        }
        // 当前日期
        String nowDateStr = AttDateUtils.dateToStrAsShort(new Date());
        // 当前日期之后或入职前离职后的根据排班信息组装信息
        if (dateStr.compareTo(nowDateStr) > 0 || isBeforeHireOrAfterLeave) {
            appAttPersonSchItem =
                buildAppAttPersonSchItemBySch(personPin, dateStr, attPersonSchMap, isBeforeHireOrAfterLeave);
        } else {
            // 没有计算结果的 按排班先去计算下
            if (Objects.isNull(attRecordItem)) {
                // 考勤计算
                attRecordItem = attAttendanceCalculateService.calculate(attPersonAttendanceBean);
                attRecordItem.setAttDate(AttDateUtils.stringToYmdDate(dateStr));
                attRecordItem.setPersonPin(personPin);

                String attendanceStatus = attRecordItem.getAttendanceStatus();
                boolean isNormal = AttCalculationConstant.AttAttendStatus.SCHEDULING_AND_REST.equals(attendanceStatus)
                    || AttCalculationConstant.AttAttendStatus.NO_SCHEDULING.equals(attendanceStatus);
                // 当天且没有计算结果，当前状态设置为正常（解决9点上班，8点进来的时候，状态为异常且旷工）
                if (dateStr.equals(DateUtil.dateToString(new Date(), DateUtil.DateStyle.YYYY_MM_DD)) && !isNormal) {
                    attRecordItem.setAttendanceStatus("");
                    attRecordItem.setAbsentMinute(0);
                }
            }
            // 组装日历信息对象
            appAttPersonSchItem =
                buildAppAttPersonSchItemByRecord(dateStr, attRecordItem, attSignItems, attLeaveItems, attOvertimeItems);
        }
        return appAttPersonSchItem;
    }

    /**
     * 
     * 组装日历信息对象
     * 
     * @param dateStr:
     * @param attRecordItem:
     * @param attSignItems:
     * @param attLeaveItems:
     * @param attOvertimeItems:
     * @return com.zkteco.zkbiosecurity.att.api.vo.AppAttPersonSchItem
     * <AUTHOR>
     * @date 2020-12-09 16:41
     * @since 1.0.0
     */
    private AppAttPersonSchItem buildAppAttPersonSchItemByRecord(String dateStr, AttRecordItem attRecordItem,
        List<AttSignItem> attSignItems, List<AttLeaveItem> attLeaveItems, List<AttOvertimeItem> attOvertimeItems) {

        // List<AppAttWorkTimeItem> appAttWorkTimeItemList = new ArrayList<>();
        List<AppAttExceptionItem> appAttExceptionItemList = new ArrayList<>();

        // 填充异常列表
        AppAttExceptionItem appAttExceptionItem = null;
        if (!CollectionUtil.isEmpty(attSignItems)) {
            String crossDay = attRecordItem.getCrossDay();
            if (StringUtils.isBlank(crossDay)) {
                crossDay = AttDateUtils.dateToStrAsShort(attRecordItem.getAttDate()) + "_"
                    + AttDateUtils.dateToStrAsShort(attRecordItem.getAttDate());
            }
            String[] crossDayArr = crossDay.split("_");
            for (AttSignItem attSignItem : attSignItems) {
                if (!(DateUtil.isSameDay(AttDateUtils.stringToYmdDate(crossDayArr[0]), attSignItem.getSignDatetime())
                    || DateUtil.isSameDay(AttDateUtils.stringToYmdDate(crossDayArr[1]),
                        attSignItem.getSignDatetime()))) {
                    continue;
                }
                appAttExceptionItem = new AppAttExceptionItem();
                appAttExceptionItem.setExceptionType(AppConstant.FLOW_TYPE_SIGN);
                appAttExceptionItem.setExceptionName(I18nUtil.i18nCode("att_api_sign"));
                appAttExceptionItem
                    .setSignTime(DateUtil.dateToString(attSignItem.getSignDatetime(), DateUtil.DateStyle.MM_DD_HH_MM));
                appAttExceptionItem.setTaskId(attSignItem.getBusinessKey());
                appAttExceptionItem
                    .setTaskStatus(AttCommonUtils.buildFlowStatus2AuditStatus(attSignItem.getFlowStatus()));
                appAttExceptionItemList.add(appAttExceptionItem);
            }
        }
        if (!CollectionUtil.isEmpty(attLeaveItems)) {
            for (AttLeaveItem attLeaveItem : attLeaveItems) {
                appAttExceptionItem = new AppAttExceptionItem();

                if (AttConstant.FLOW_TYPE_TRIP.equals(attLeaveItem.getLeaveTypeNo())) {
                    appAttExceptionItem.setExceptionType(AppConstant.FLOW_TYPE_TRIP);
                } else if (AttConstant.FLOW_TYPE_OUT.equals(attLeaveItem.getLeaveTypeNo())) {
                    appAttExceptionItem.setExceptionType(AppConstant.FLOW_TYPE_OUT);
                } else {
                    appAttExceptionItem.setExceptionType(AppConstant.FLOW_TYPE_LEAVE);
                }
                appAttExceptionItem.setExceptionName(attLeaveItem.getLeaveTypeName());
                appAttExceptionItem.setStartTime(
                    DateUtil.dateToString(attLeaveItem.getStartDatetime(), DateUtil.DateStyle.MM_DD_HH_MM));
                appAttExceptionItem
                    .setEndTime(DateUtil.dateToString(attLeaveItem.getEndDatetime(), DateUtil.DateStyle.MM_DD_HH_MM));
                appAttExceptionItem.setTaskId(attLeaveItem.getBusinessKey());
                appAttExceptionItem
                    .setTaskStatus(AttCommonUtils.buildFlowStatus2AuditStatus(attLeaveItem.getFlowStatus()));
                appAttExceptionItemList.add(appAttExceptionItem);
            }
        }
        if (!CollectionUtil.isEmpty(attOvertimeItems)) {
            for (AttOvertimeItem attOvertimeItem : attOvertimeItems) {
                appAttExceptionItem = new AppAttExceptionItem();
                appAttExceptionItem.setExceptionType(AppConstant.FLOW_TYPE_OVERTIME);
                appAttExceptionItem
                    .setExceptionName(AttCommonUtils.overtimeSignName(attOvertimeItem.getOvertimeSign()));
                appAttExceptionItem.setStartTime(
                    DateUtil.dateToString(attOvertimeItem.getStartDatetime(), DateUtil.DateStyle.MM_DD_HH_MM));
                appAttExceptionItem.setEndTime(
                    DateUtil.dateToString(attOvertimeItem.getEndDatetime(), DateUtil.DateStyle.MM_DD_HH_MM));
                appAttExceptionItem.setTaskId(attOvertimeItem.getBusinessKey());
                appAttExceptionItem
                    .setTaskStatus(AttCommonUtils.buildFlowStatus2AuditStatus(attOvertimeItem.getFlowStatus()));
                appAttExceptionItemList.add(appAttExceptionItem);
            }
        }

        // 填充卡点及状态
        List<AppAttValidCardItem> appAttValidCardItemList = fillWorkTime(attRecordItem, attSignItems);

        String attendanceStatus = Optional.ofNullable(attRecordItem.getAttendanceStatus()).orElse("");
        // 接口定义 0-正常、1-异常、2-请假、3-休息、4-漏卡、5-加班、6-外出、7-出差、8-未排班
        int attState = attendanceStatusConvertAttState(attendanceStatus);

        AppAttPersonSchItem appAttPersonSchItem = new AppAttPersonSchItem();
        appAttPersonSchItem.setDate(dateStr);
        appAttPersonSchItem.setShiftId("");
        // 根据是否有班次名称判断正常排班或者临时排班
        String shiftName = attRecordItem.getShiftName();
        appAttPersonSchItem.setShiftName(shiftName);
        if (StringUtils.isBlank(shiftName)) {
            appAttPersonSchItem.setType("1");
        } else {
            appAttPersonSchItem.setType("0");
        }
        appAttPersonSchItem.setAttState(attState);
        appAttPersonSchItem.setShouldHour(attParamService
            .minutesToHourFormat(BigDecimal.valueOf(Optional.ofNullable(attRecordItem.getShouldMinute()).orElse(0))));
        appAttPersonSchItem.setActualHour(attParamService
            .minutesToHourFormat(BigDecimal.valueOf(Optional.ofNullable(attRecordItem.getActualMinute()).orElse(0))));
        appAttPersonSchItem.setAbsentHour(attParamService
            .minutesToHourFormat(BigDecimal.valueOf(Optional.ofNullable(attRecordItem.getAbsentMinute()).orElse(0))));
        // appAttPersonSchItem.setLackCardCount("" + attRecordItem.getLateCountTotal());
        appAttPersonSchItem.setAppAttValidCardItems(appAttValidCardItemList);
        // appAttPersonSchItem.setWorkTimes(appAttWorkTimeItemList);
        appAttPersonSchItem.setAttExceptionList(appAttExceptionItemList);

        return appAttPersonSchItem;
    }

    /**
     * 根据排班信息组装信息
     *
     * @param personPin:
     * @param dateStr:
     * @param attPersonSchMap:
     * @param isBeforeHireOrAfterLeave:
     * @return com.zkteco.zkbiosecurity.att.api.vo.AppAttPersonSchItem
     * <AUTHOR>
     * @date 2020-12-09 16:30
     * @since 1.0.0
     */
    private AppAttPersonSchItem buildAppAttPersonSchItemBySch(String personPin, String dateStr,
        Map<String, List<AttPersonSchBO>> attPersonSchMap, boolean isBeforeHireOrAfterLeave) {
        AppAttPersonSchItem appAttPersonSchItem = new AppAttPersonSchItem();
        appAttPersonSchItem.setDate(dateStr);
        appAttPersonSchItem.setShiftId("");
        appAttPersonSchItem.setType("0");

        List<AttPersonSchBO> attPersonSchBOList = attPersonSchMap.get(personPin + "=" + dateStr);
        int attState = AppConstant.ATT_RESULT_TYPE_NORMAL;
        if (!CollectionUtil.isEmpty(attPersonSchBOList)) {
            AttPersonSchBO attPersonSchBO = attPersonSchBOList.get(0);
            String attendanceStatus = Optional.ofNullable(attPersonSchBO.getAttendStatus()).orElse("");
            // 未排班
            if (attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.NO_SCHEDULING)) {
                attState = AppConstant.ATT_RESULT_TYPE_NO_SCHEDULING;
            }
            // 排班且休息
            if (attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.SCHEDULING_AND_REST)
                || attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.HOLIDAY)) {
                attState = AppConstant.ATT_RESULT_TYPE_REST;
            }
            appAttPersonSchItem.setShiftName(attPersonSchBO.getAttShiftName());
        } else if (isBeforeHireOrAfterLeave) {
            attState = AppConstant.ATT_RESULT_TYPE_NO_SCHEDULING;
        }
        appAttPersonSchItem.setAttState(attState);
        return appAttPersonSchItem;
    }

    /**
     * 考勤多状态转接口状态
     *
     * @return int
     * <AUTHOR>
     * @date 2020-11-25 18:15
     * @since 1.0.0
     */
    private int attendanceStatusConvertAttState(String attendanceStatus) {
        int attState = AppConstant.ATT_RESULT_TYPE_NORMAL;
        // 接口定义 0-正常、1-异常、2-请假、3-休息、4-漏卡、5-加班、6-外出、7-出差、8-未排班、9-调休、10-补班、11-调班
        //
        if (attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.EARLY)
            || attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.LATE)
            || attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.ABSENT)
            || attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.NO_CHECK_IN_INCOMPLETE)
            || attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.NO_CHECK_OUT_INCOMPLETE)) {
            return AppConstant.ATT_RESULT_TYPE_EXCEPTION;
        }
        // 判断是否请假 attendanceStatus存的是假种名称不是直接leave
        List<AttLeaveTypeItem> attLeaveTypeItems = attLeaveTypeService.listLeaveTypeFilterTripAndOut();
        Collection<String> leaveTypeNos =
            CollectionUtil.getPropertyList(attLeaveTypeItems, AttLeaveTypeItem::getLeaveTypeNo, "-1");
        for (String leaveTypeNo : leaveTypeNos) {
            if (attendanceStatus.contains(leaveTypeNo)) {
                return AppConstant.ATT_RESULT_TYPE_LEAVE;
            }
        }
        if (attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.SCHEDULING_AND_REST)
            || attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.HOLIDAY)) {
            return AppConstant.ATT_RESULT_TYPE_REST;
        }
        if (attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.NO_CHECK_IN)
            || attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.NO_CHECK_OUT)) {
            return AppConstant.ATT_RESULT_TYPE_LACK_CARD;
        }
        if (attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.OUT)) {
            return AppConstant.ATT_RESULT_TYPE_OUT;
        }
        if (attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.TRIP)) {
            return AppConstant.ATT_RESULT_TYPE_TRIP;
        }
        if (attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.OVERTIME)) {
            return AppConstant.ATT_RESULT_TYPE_OVERTIME;
        }
        if (attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.NO_SCHEDULING)) {
            return AppConstant.ATT_RESULT_TYPE_NO_SCHEDULING;
        }
        return attState;
    }

    /**
     * 填充卡点时间级状态
     *
     * @param recordItem:
     * @param attSignItems:
     * @return java.util.List<com.zkteco.zkbiosecurity.att.api.vo.AppAttValidCardItem>
     * <AUTHOR>
     * @date 2020-11-27 10:35
     * @since 1.0.0
     */
    private List<AppAttValidCardItem> fillWorkTime(AttRecordItem recordItem, List<AttSignItem> attSignItems) {

        List<AppAttValidCardItem> appAttValidCardItemList = new ArrayList<>();

        // 时间段集合
        String workTimeStr = recordItem.getShiftTimeData();
        if (StringUtils.isBlank(workTimeStr)) {
            // 无时段
            return appAttValidCardItemList;
        }
        String[] workTimeAry = workTimeStr.split(";");

        // 有效打卡数据
        String cardValidStr = recordItem.getCardValidData();
        String[] cardValidAry = cardValidStr.split(";");

        // 卡点状态
        String cardStatusStr = recordItem.getCardStatus();
        String[] cardStatusArr = null;
        if (StringUtils.isNotBlank(cardStatusStr)) {
            cardStatusArr = cardStatusStr.split(";");
        }

        // 跨天
        String crossDay = recordItem.getCrossDay();
        if (StringUtils.isBlank(crossDay)) {
            crossDay = AttDateUtils.dateToStrAsShort(recordItem.getAttDate()) + "_"
                + AttDateUtils.dateToStrAsShort(recordItem.getAttDate());
        }
        String[] crossDayArr = crossDay.split("_");

        AppAttValidCardItem appAttValidCardItem = null;
        Date nowDate = new Date();
        String nowTime = DateUtil.dateToString(nowDate, DateUtil.DateStyle.YYYY_MM_DD_HH_MM);

        for (int i = 0; i < workTimeAry.length; i++) {
            appAttValidCardItem = new AppAttValidCardItem();

            // 班次时段时间
            String[] workTime = workTimeAry[i].split("-");
            appAttValidCardItem.setShouldStartWorkTime(workTime[0]);
            appAttValidCardItem.setShouldEndWorkTime(workTime[1]);

            // 实际打卡点（多卡点取第一个卡点和最后一个卡点）
            String[] cardValids = cardValidAry[i].split(",");
            String[] startCardValid = cardValids[0].split("-");
            String startWorkTime = startCardValid[0];
            String[] endCardValid = cardValids[cardValids.length - 1].split("-");
            String endWorkTime = endCardValid[1];
            appAttValidCardItem.setStartWorkTime(startWorkTime);
            appAttValidCardItem.setEndWorkTime(endWorkTime);

            String firstDay = crossDayArr[0];
            String secondDay = StringUtils.compare(appAttValidCardItem.getShouldStartWorkTime(),
                appAttValidCardItem.getShouldEndWorkTime()) < 0 ? crossDayArr[0] : crossDayArr[1];

            String[] cardStatus = cardStatusArr[i].split("-");
            String startCardStatus = cardStatus[0];
            String endCardStatus = cardStatus[1];

            // 默认卡点状态
            appAttValidCardItem.setStartWorkStatus(startCardStatus);
            appAttValidCardItem.setEndWorkStatus(endCardStatus);
            appAttValidCardItem.setTimeLong("");

            if (AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION.equals(startCardStatus)
                && (StringUtils.isBlank(startWorkTime) || !startWorkTime.contains(":"))) {
                appAttValidCardItem.setStartWorkTime(AppConstant.ATT_CALENDAR_LACK_CARD);

                if (StringUtils.compare(firstDay + " " + appAttValidCardItem.getShouldStartWorkTime(), nowTime) > 0) {
                    appAttValidCardItem.setStartWorkTime(AppConstant.ATT_CALENDAR_WAIT_CARD);
                    appAttValidCardItem.setStartWorkStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);
                }
            }
            if (AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION.equals(endCardStatus)
                && (StringUtils.isBlank(endWorkTime) || !endWorkTime.contains(":"))) {
                appAttValidCardItem.setEndWorkTime(AppConstant.ATT_CALENDAR_LACK_CARD);

                if (StringUtils.compare(secondDay + " " + appAttValidCardItem.getShouldEndWorkTime(), nowTime) > 0) {
                    appAttValidCardItem.setEndWorkTime(AppConstant.ATT_CALENDAR_WAIT_CARD);
                    appAttValidCardItem.setEndWorkStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);
                }
            }

            // 即使卡点状态正常,缺卡返回给日历接口L处理
            if (StringUtils.isBlank(appAttValidCardItem.getStartWorkTime())
                || !appAttValidCardItem.getStartWorkTime().contains(":")) {
                appAttValidCardItem.setStartWorkTime(AppConstant.ATT_CALENDAR_LACK_CARD);
            }
            if (StringUtils.isBlank(appAttValidCardItem.getEndWorkTime())
                || !appAttValidCardItem.getEndWorkTime().contains(":")) {
                appAttValidCardItem.setEndWorkTime(AppConstant.ATT_CALENDAR_LACK_CARD);
            }

            // 如有补签显示卡点时间注明该卡点为补签状态
            if (!CollectionUtil.isEmpty(attSignItems)) {
                for (AttSignItem item : attSignItems) {
                    String signDatetime = DateUtil.getDate(item.getSignDatetime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM);
                    if (StringUtils.equals(signDatetime, firstDay + " " + startWorkTime)) {
                        appAttValidCardItem.setStartWorkStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_SIGN);
                    }
                    if (StringUtils.equals(signDatetime, secondDay + " " + endWorkTime)) {
                        appAttValidCardItem.setEndWorkStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_SIGN);
                    }
                }
            }

            appAttValidCardItemList.add(appAttValidCardItem);
        }
        return appAttValidCardItemList;
    }

    @Override
    public Pager getAllPersonSch(String sessionId, AttAllPersonSchItem condition, int pageNo, int pageSize) {
        new Pager();
        Date endDate = AttDateUtils.getEndDate(condition.getStartDate(), condition.getEndDate());

        // 当前登录权限过滤
        if (StringUtils.isNotBlank(sessionId)) {
            SecuritySubject securitySubject = authSessionServcie.getSecuritySubject(sessionId);
            // 非超级用户进行数据权限过滤
            if (!securitySubject.getIsSuperuser() && securitySubject.getDepartmentIds() != null
                && securitySubject.getDepartmentIds().size() > 0) {
                condition.setUserId(securitySubject.getUserId());
            }
        }
        // 是否包含下级
        if ("true".equals(condition.getIsIncludeLower()) && !StringUtils.isBlank(condition.getDeptId())) {
            List<AuthDepartmentItem> itemAndChildIds = authDepartmentService.getItemAndChildById(condition.getDeptId());
            if (itemAndChildIds.size() > 0) {
                condition.setInDeptId(CollectionUtil.getItemIds(itemAndChildIds));
                condition.setDeptId(null);
            }
        }

        if (StringUtils.isNotBlank(condition.getSchStatus())) {
            String startDateStr = AttDateUtils.dateToStrAsLong(condition.getStartDate());
            String endDateStr = AttDateUtils.dateToStrAsLong(endDate);
            String selectCycSchSQL = null;
            String selectTempSchSQL = null;
            String dataSource = DataSourceConfig.getDbType();
            if (ZKConstant.ORACLE.equals(dataSource)) {
                selectCycSchSQL =
                    "SELECT %s FROM ATT_CYCLESCH T WHERE TO_CHAR(T.START_DATE,'yyyy-mm-dd HH24:mi:ss') <= '"
                        + endDateStr + "' AND TO_CHAR(T.END_DATE ,'yyyy-mm-dd HH24:mi:ss') >= '" + startDateStr + "'";
                selectTempSchSQL =
                    "SELECT %s FROM ATT_TEMPSCH T WHERE TO_CHAR(T.START_DATE,'yyyy-mm-dd HH24:mi:ss') <= '" + endDateStr
                        + "' AND T.END_DATE >= '" + startDateStr + "'";
            } else {
                selectCycSchSQL = "SELECT %s FROM ATT_CYCLESCH T WHERE T.START_DATE <= '" + endDateStr
                    + "' AND T.END_DATE >= '" + startDateStr + "'";
                selectTempSchSQL = "SELECT %s FROM ATT_TEMPSCH T WHERE T.START_DATE <= '" + endDateStr
                    + "' AND T.END_DATE >= '" + startDateStr + "'";
            }

            StringBuffer sqlCondition = new StringBuffer();
            if (AttCommonSchConstant.SCH_STATUS_YES.equals(condition.getSchStatus())) {
                sqlCondition.append("(");
                sqlCondition.append("PP.ID IN (").append(String.format(selectCycSchSQL, "T.PERS_PERSON_ID"))
                    .append(" AND T.CYCLE_TYPE = 2").append(")");
                sqlCondition.append(" OR PP.ID IN (").append(String.format(selectTempSchSQL, "T.PERS_PERSON_ID"))
                    .append(" AND T.TEMP_TYPE = 2").append(")");
                sqlCondition.append(" OR PP.AUTH_DEPT_ID IN (").append(String.format(selectCycSchSQL, "T.AUTH_DEPT_ID"))
                    .append(" AND T.CYCLE_TYPE = 1").append(")");
                sqlCondition.append(" OR PP.AUTH_DEPT_ID IN (")
                    .append(String.format(selectTempSchSQL, "T.AUTH_DEPT_ID")).append(" AND T.TEMP_TYPE = 1")
                    .append(")");
                sqlCondition.append(" OR (AP.GROUP_ID IS NOT NULL AND AP.GROUP_ID <> '' AND AP.GROUP_ID IN (")
                    .append(String.format(selectCycSchSQL, "T.GROUP_ID")).append(" AND T.CYCLE_TYPE = 0").append("))");
                sqlCondition.append(" OR (AP.GROUP_ID IS NOT NULL AND AP.GROUP_ID <> '' AND AP.GROUP_ID IN (")
                    .append(String.format(selectTempSchSQL, "T.GROUP_ID")).append(" AND T.TEMP_TYPE = 0").append("))");
                sqlCondition.append(")");
                condition.setOrSqlCondition(sqlCondition.toString());
            } else if (AttCommonSchConstant.SCH_STATUS_NO.equals(condition.getSchStatus())) {
                sqlCondition.append("(");
                sqlCondition.append("PP.ID NOT IN (").append(String.format(selectCycSchSQL, "T.PERS_PERSON_ID"))
                    .append(" AND T.CYCLE_TYPE = 2").append(")");
                sqlCondition.append(" AND PP.ID NOT IN (").append(String.format(selectTempSchSQL, "T.PERS_PERSON_ID"))
                    .append(" AND T.TEMP_TYPE = 2").append(")");
                sqlCondition.append(" AND PP.AUTH_DEPT_ID NOT IN (")
                    .append(String.format(selectCycSchSQL, "T.AUTH_DEPT_ID")).append(" AND T.CYCLE_TYPE = 1")
                    .append(")");
                sqlCondition.append(" AND PP.AUTH_DEPT_ID NOT IN (")
                    .append(String.format(selectTempSchSQL, "T.AUTH_DEPT_ID")).append(" AND T.TEMP_TYPE = 1")
                    .append(")");
                sqlCondition.append(" AND (AP.GROUP_ID IS NULL OR AP.GROUP_ID = '' OR AP.GROUP_ID NOT IN (")
                    .append(String.format(selectCycSchSQL, "T.GROUP_ID")).append(" AND T.CYCLE_TYPE = 0").append("))");
                sqlCondition.append(" AND (AP.GROUP_ID IS NULL OR AP.GROUP_ID = '' OR AP.GROUP_ID NOT IN (")
                    .append(String.format(selectTempSchSQL, "T.GROUP_ID")).append(" AND T.TEMP_TYPE = 0").append("))");
                sqlCondition.append(")");
                condition.setOrSqlCondition(sqlCondition.toString());
            }
        }

        Pager pager =
            attPersonSchDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);

        List<AttAllPersonSchItem> attAllPersonSchItemList = (List<AttAllPersonSchItem>)pager.getData();

        List<String> pins = (List<String>)CollectionUtil.getPropertyList(attAllPersonSchItemList,
            AttAllPersonSchItem::getPersonPin, "-1");
        Map<String, List<AttPersonSchBO>> personSchData =
            attPersonSchDataService.getPersonAllSchData(pins, condition.getStartDate(), endDate);

        List<AttTimeSlotItem> attTimeSlotItems = attTimeSlotService.getByCondition(new AttTimeSlotItem());
        Map<String, AttTimeSlotItem> timeSlotItemMap =
            CollectionUtil.listToKeyMap(attTimeSlotItems, AttTimeSlotItem::getId);

        List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getByCondition(new AttLeaveTypeItem());
        Map<String, AttLeaveTypeItem> attLeaveTypeItemMap =
            CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getLeaveTypeNo);

        if (attAllPersonSchItemList != null && attAllPersonSchItemList.size() > 0) {
            attAllPersonSchItemList.forEach(item -> {
                String personPin = item.getPersonPin();
                Map<String, Object> map = new LinkedHashMap<>();
                AttDateUtils.forEachDay(condition.getStartDate(), endDate, date -> {
                    String dateStr = AttDateUtils.dateToStrAsShort(date);
                    List<AttPersonSchBO> attPersonSchBOList = personSchData.get(personPin + "=" + dateStr);
                    if (!CollectionUtil.isEmpty(attPersonSchBOList)) {
                        List<String> timeSlotName = new ArrayList<>(16);
                        for (AttPersonSchBO attPersonSchBO : attPersonSchBOList) {
                            List<AttTimeSlotBO> attTimeSlotBOList = attPersonSchBO.getAttTimeSlotArray();
                            if (attTimeSlotBOList != null && attTimeSlotBOList.size() > 0) {
                                for (AttTimeSlotBO attTimeSlotBO : attTimeSlotBOList) {
                                    AttTimeSlotItem attTimeSlotItem =
                                        timeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());
                                    if (null != attTimeSlotItem) {
                                        timeSlotName.add(attTimeSlotItem.getPeriodName());
                                    }
                                }
                            } else if (attLeaveTypeItemMap.containsKey(attPersonSchBO.getAttendStatus())) {

                                timeSlotName.add(attLeaveTypeItemMap.get(attPersonSchBO.getAttendStatus()).getSymbol());
                            }
                        }
                        map.put(dateStr, StringUtils.join(timeSlotName.iterator(), ","));
                    }
                });

                item.setMap(map);
            });
        }
        pager.setData(attAllPersonSchItemList);
        return pager;
    }

    @Override
    public String isWorkd(String personPin, Date dateTime) {
        String ret = AttCalculationConstant.AttAttendStatus.NO_SCHEDULING;
        // 根据日期和pin号找出当前的排班信息
        Map<String, List<AttPersonSchBO>> attPersonSchMap =
            attPersonSchDataService.getPersonSchData(Arrays.asList(personPin), dateTime, dateTime);
        if (attPersonSchMap != null) {
            String key = personPin + AttCalculationConstant.KEY_CONNECTOR
                + DateUtil.dateToString(dateTime, DateUtil.DateStyle.YYYY_MM_DD);
            List<AttPersonSchBO> attPersonSchBOList = attPersonSchMap.get(key);
            if (attPersonSchBOList != null && attPersonSchBOList.size() > 0) {
                // 如果多个排班中只要一个符合时间段不为空，就返回(应到/实到)标记
                for (AttPersonSchBO attPersonSchBO : attPersonSchBOList) {
                    if (attPersonSchBO.getAttTimeSlotArray() != null
                        && attPersonSchBO.getAttTimeSlotArray().size() > 0) {
                        return AttCalculationConstant.AttAttendStatus.ACTUAL;
                    }
                }
                // 有排班，但是班次没有选择该日期
                return AttCalculationConstant.AttAttendStatus.SCHEDULING_AND_REST;
            }
        }
        return ret;
    }

    @Override
    public ZKResultMsg getPersonWorkTime(String personId, String date) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        PersPersonItem personItem = persPersonService.getItemById(personId);
        if (Objects.isNull(personItem)) {
            return ZKResultMsg.failMsg();
        }
        String pin = personItem.getPin();
        AttRecord attRecord = attRecordDao.findByAttDateAndPersonPin(AttDateUtils.stringToYmdDate(date), pin);
        String shiftTimeData = attRecord.getShiftTimeData();
        ArrayList<AttApiValidCardItem> attApiValidCardItemList = new ArrayList<AttApiValidCardItem>();
        String[] workTimeArr = shiftTimeData.split(";");
        for (String workTime : workTimeArr) {
            AttApiValidCardItem attApiValidCardItem = new AttApiValidCardItem();
            String[] timeArr = workTime.split("-");
            attApiValidCardItem.setShouldStartWorkTime(timeArr[0]);
            attApiValidCardItem.setShouldEndWorkTime(timeArr[1]);
            attApiValidCardItemList.add(attApiValidCardItem);
        }
        zkResultMsg.setData(attApiValidCardItemList);
        return zkResultMsg;
    }

//    @Override
//    public ZKResultMsg getPersonWorkTimeCustome(String personId, String date) {
//        PersPersonItem personItem = persPersonService.getItemById(personId);
//        if (Objects.isNull(personItem)) {
//            return ZKResultMsg.failMsg();
//        }
//        String pin = personItem.getPin();
//        AttRecord attRecord = attRecordDao.findByAttDateAndPersonPin(AttDateUtils.stringToYmdDate(date), pin);
//        String shiftTimeData = attRecord.getShiftTimeData();
//        ArrayList<AttApiValidCardItem> attApiValidCardItemList = new ArrayList<AttApiValidCardItem>();
//        String[] workTimeArr = shiftTimeData.split(";");
//        for (String workTime : workTimeArr) {
//            AttApiValidCardItem attApiValidCardItem = new AttApiValidCardItem();
//            String[] timeArr = workTime.split("-");
//            attApiValidCardItem.setShouldStartWorkTime(timeArr[0]);
//            attApiValidCardItem.setShouldEndWorkTime(timeArr[1]);
//            attApiValidCardItemList.add(attApiValidCardItem);
//        }
//        zkResultMsg.setData(attApiValidCardItemList);
//        return zkResultMsg;
//    }
}
