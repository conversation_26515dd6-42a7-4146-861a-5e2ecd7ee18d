package com.zkteco.zkbiosecurity.att.dao;

import com.zkteco.zkbiosecurity.att.model.AttDayCardDetail;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Modifying;

import javax.transaction.Transactional;
import java.util.Collection;

/**
 * 日打卡详情
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 18:27 2020/6/28
 */
public interface AttDayCardDetailDao extends BaseDao<AttDayCardDetail, String> {

    @Modifying
    @Transactional
    void deleteByAttDateAndPersonPinIn(String attDate, Collection<String> pinList);
}