package com.zkteco.zkbiosecurity.att.service.impl;

import com.zkteco.zkbiosecurity.att.service.AttDeviceService;
import com.zkteco.zkbiosecurity.att.service.AttPersonService;
import com.zkteco.zkbiosecurity.att.service.AttSelectDeviceService;
import com.zkteco.zkbiosecurity.att.vo.AttDeviceItem;
import com.zkteco.zkbiosecurity.att.vo.AttPersonItem;
import com.zkteco.zkbiosecurity.att.vo.AttSelectDeviceItem;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.oameeting.service.OaMeeting4AttService;
import com.zkteco.zkbiosecurity.oameeting.vo.Meeting4AttDeviceSelect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 会议模块调用att设备
 *
 * <AUTHOR>
 * @date 2022-03-29 15:11
 * @since 1.0.0
 */
@Service
public class OaMeeting4AttServiceImpl implements OaMeeting4AttService {

    @Autowired
    private AttDeviceService attDeviceService;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private AttSelectDeviceService attSelectDeviceService;
    @Autowired
    private AttPersonService attPersonService;

    @Override
    public void setPersonToAttDev(List<String> personIdList, List<String> attDeviceIdList) {
        // 下发人员到考勤设备
        attDeviceService.setPersonToDev(personIdList, attDeviceIdList);
    }

    @Override
    public Pager getAttDevice(Meeting4AttDeviceSelect condition, int pageNo, int pageSize) {
        Pager pager = attDeviceService.getItemsByPage(condition, pageNo, pageSize);
        List<Meeting4AttDeviceSelect> meeting4AttDeviceSelects = (List<Meeting4AttDeviceSelect>) pager.getData();
        String areaIds = CollectionUtil.getPropertys(meeting4AttDeviceSelects, Meeting4AttDeviceSelect::getAreaId);
        List<AuthAreaItem> areaItems = authAreaService.getItemsByIds(areaIds);
        Map<String, AuthAreaItem> areaMap = CollectionUtil.itemListToIdMap(areaItems);
        AuthAreaItem area;
        for (Meeting4AttDeviceSelect meeting4AttDeviceSelect : meeting4AttDeviceSelects) {
            area = areaMap.get(meeting4AttDeviceSelect.getAreaId());
            meeting4AttDeviceSelect.setAuthAreaName(area.getName());
        }
        return pager;
    }

    @Override
    public Meeting4AttDeviceSelect getAttDevice(String sn) {
        AttDeviceItem attDeviceItem = attDeviceService.getItemBySn(sn);
        Meeting4AttDeviceSelect meeting4AttDeviceSelect = new Meeting4AttDeviceSelect();
        ModelUtil.copyPropertiesIgnoreNull(attDeviceItem, meeting4AttDeviceSelect);
        return meeting4AttDeviceSelect;
    }

    @Override
    public Pager getItemByAuthFilter(String sessionId, Meeting4AttDeviceSelect meeting4AttDeviceSelect, int pageNo, int pageSize) {
        AttSelectDeviceItem attSelectDeviceItem = ModelUtil.copyProperties(meeting4AttDeviceSelect, new AttSelectDeviceItem());
        Pager pager = attSelectDeviceService.getItemByAuthFilter(sessionId, attSelectDeviceItem, pageNo, pageSize);
        List<AttSelectDeviceItem> data = (List<AttSelectDeviceItem>) pager.getData();
        pager.setData(ModelUtil.copyListProperties(data, Meeting4AttDeviceSelect.class));
        buildArea(pager.getData());
        return pager;
    }

    @SuppressWarnings("unchecked")
    private List<?> buildArea(List<?> items) {
        List<Meeting4AttDeviceSelect> list = (List<Meeting4AttDeviceSelect>) items;
        String areaIds = CollectionUtil.getPropertys(list, Meeting4AttDeviceSelect::getAreaId);
        List<AuthAreaItem> areaItems = authAreaService.getItemsByIds(areaIds);
        Map<String, AuthAreaItem> areaMap = CollectionUtil.itemListToIdMap(areaItems);
        AuthAreaItem area = null;
        for (Meeting4AttDeviceSelect item : list) {
            area = areaMap.get(item.getAreaId());
            //防止对象不存在  --add by hook.fang 2019-03-28
            if (area != null) {
                item.setAuthAreaName(area.getName());
            }
        }
        return items;
    }

    @Override
    public Meeting4AttDeviceSelect getItemById(String devId) {
        AttDeviceItem attDeviceItem = attDeviceService.getItemById(devId);
        if (null == attDeviceItem) {
            return null;
        }
        return ModelUtil.copyProperties(attDeviceItem, new Meeting4AttDeviceSelect());
    }

    @Override
    public List<String> getAllAttDevAdminPersonId() {
        List<String> devAdminPersonIds = new ArrayList<>();

        AttPersonItem attPersonItem = new AttPersonItem();
        // 设置查询设备管理员和超级管理员
        attPersonItem.setPerDevAuthIn("6,14");
        List<AttPersonItem> attPersonItemList = attPersonService.getByCondition(attPersonItem);
        if (!CollectionUtil.isEmpty(attPersonItemList)) {
            devAdminPersonIds.addAll(CollectionUtil.getPropertyList(attPersonItemList, AttPersonItem::getPersonId, "-1"));
        }
        return devAdminPersonIds;
    }
}
