package com.zkteco.zkbiosecurity.att.service.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import com.zkteco.zkbiosecurity.att.dao.AttDeviceDao;
import com.zkteco.zkbiosecurity.att.model.AttDevice;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttDeviceOpLogDao;
import com.zkteco.zkbiosecurity.att.model.AttDeviceOpLog;
import com.zkteco.zkbiosecurity.att.service.AttDeviceOpLogService;
import com.zkteco.zkbiosecurity.att.vo.AttDeviceOpLogItem;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthSessionServcie;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 设备操作日志
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:26
 * @since 1.0.0
 */
@Slf4j
@Service
public class AttDeviceOpLogServiceImpl implements AttDeviceOpLogService {

    @Autowired
    private AttDeviceOpLogDao attDeviceOpLogDao;
    @Autowired
    private AuthSessionServcie authSessionServcie;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AttDeviceDao attDeviceDao;

    @Override
    @Transactional
    public AttDeviceOpLogItem saveItem(AttDeviceOpLogItem item) {
        AttDeviceOpLog attDeviceOpLog = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(attDeviceOpLogDao::findById).orElse(new AttDeviceOpLog());
        ModelUtil.copyPropertiesIgnoreNull(item, attDeviceOpLog);
        attDeviceOpLogDao.save(attDeviceOpLog);
        item.setId(attDeviceOpLog.getId());
        return item;
    }

    @Override
    public List<AttDeviceOpLogItem> getByCondition(AttDeviceOpLogItem condition) {
        return (List<AttDeviceOpLogItem>)attDeviceOpLogDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return attDeviceOpLogDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        return false;
    }

    @Override
    public AttDeviceOpLogItem getItemById(String id) {
        if (StringUtils.isNotBlank(id)) {
            List<AttDeviceOpLogItem> items = getByCondition(new AttDeviceOpLogItem(id));
            return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
        }
        return null;
    }

    @Override
    public Pager getItemsByAuthUserPage(String sessionId, BaseItem condition, int page, int size) {
        AttDeviceOpLogItem attDeviceOpLogItem = (AttDeviceOpLogItem)condition;
        SecuritySubject securitySubject = authSessionServcie.getSecuritySubject(sessionId);
        if (!securitySubject.getIsSuperuser() && securitySubject.getAreaIds() != null
            && securitySubject.getAreaIds().size() > 0) {
            // 非超级用户进行数据权限过滤
            attDeviceOpLogItem.setInAreaId(StrUtil.collectionToStr(securitySubject.getAreaIds()));
        }
        Pager pager = attDeviceOpLogDao.getItemsBySql(attDeviceOpLogItem.getClass(),
            SQLUtil.getSqlByItem(attDeviceOpLogItem), page, size);
        return pager;
    }

    /**
     * 处理考勤设备上传考勤设备操作记录
     *
     * @param sn
     * @param dataStr
     */
    @Override
    @Transactional
    public void handlerAttDeviceOpLog(String sn, String dataStr) {

        // 如果设备不存在，则该设备上传的记录不保存
        AttDevice attDevice = attDeviceDao.findByDevSn(sn);
        if (attDevice == null) {
            log.warn("AttDevice Not Exist");
            return;
        }

        // 查询考勤区域
        AuthAreaItem authAreaItem = null;
        if (StringUtils.isNotBlank(attDevice.getAreaId())) {
            authAreaItem = authAreaService.getItemById(attDevice.getAreaId());
        }

        // 处理考勤记录，保存入库
        List<AttDeviceOpLog> attLogList = new ArrayList<>();
        AttDeviceOpLog attDeviceOpLog = null;
        String[] dataAry = dataStr.split("\n");
        for (String data : dataAry) {
            // 操作代码 操作人员工号 时间 操作对象1，2，3，4
            // data : OPLOG 4 14 2015-07-30 10:22:34 0 0 0 0
            attDeviceOpLog = new AttDeviceOpLog();

            if (authAreaItem != null) {
                attDeviceOpLog.setAreaId(authAreaItem.getId());
            }
            String[] split = data.split(" ", 2);
            String table = split[0];
            String infoData = split[1];
            String[] attDeviceOpLogArray = infoData.split("\t");

            String opType = attDeviceOpLogArray[0];
            // 不存在此列表中的先跳过
            if (!AttConstant.ATTOPLOG_OPTYPE.containsKey(Integer.parseInt(opType))) {
                continue;
            }
            // 操作人员信息
            String operatorPin = attDeviceOpLogArray[1];
            PersPersonItem persPersonItem = persPersonService.getItemByPin(operatorPin);
            if (null != persPersonItem) {
                attDeviceOpLog.setOperatorName(persPersonItem.getName());
            }

            Date attOptime = Timestamp.valueOf(attDeviceOpLogArray[2]);

            attDeviceOpLog.setDevSn(sn);
            attDeviceOpLog.setOpTime(attOptime);
            attDeviceOpLog.setOpType(opType);
            attDeviceOpLog.setOpContent(AttConstant.ATTOPLOG_OPTYPE.get(Integer.parseInt(opType)));
            attDeviceOpLog.setOpWhoValue(attDeviceOpLogArray[3]);
            attDeviceOpLog.setOpValue1(attDeviceOpLogArray[4]);
            attDeviceOpLog.setOpValue2(attDeviceOpLogArray[5]);
            attDeviceOpLog.setOpValue3(attDeviceOpLogArray[6]);

            attDeviceOpLog.setOpWhoContent("");
            attDeviceOpLog.setOpValueContent1("");
            attDeviceOpLog.setOpValueContent2("");
            attDeviceOpLog.setOpValueContent3("");

            switch (Integer.parseInt(opType)) {
                case 2:
                    // 若为 1:1 验证，则为用户工号
                    attDeviceOpLog.setOpWhoContent(
                        I18nUtil.i18nCode("att_device_op_log_content_pin") + attDeviceOpLogArray[3]);
                    break;
                case 3:
                    // 报警 报警的原因
                    // 3 0 2019-06-12 16:17:12 55 0 0 0
                    // attDeviceOpLog.setOpWhoContent(I18nUtil.i18nCode("att_device_op_log_content_alarm")+attDeviceOpLogArray[3]);
                    int oplogAlarm;
                    try {
                        oplogAlarm = Integer.parseInt(attDeviceOpLogArray[3]);
                    } catch (Exception e) {
                        oplogAlarm = -1;
                        log.error("AttDeviceOpLogServiceImpl.handlerAttDeviceOpLog() fail: " + sn
                            + " Upgrade firmware,Huawei customization impact.");
                    }
                    attDeviceOpLog.setOpWhoContent(I18nUtil.i18nCode("att_device_op_log_content_alarm_reason")
                        + AttConstant.ATTOPLOG_ALARM.get(oplogAlarm));
                    break;
                case 5:
                    // 被修改的设置项的序号 新修改后的值
                    attDeviceOpLog.setOpWhoContent(
                        I18nUtil.i18nCode("att_device_op_log_content_update_no") + attDeviceOpLogArray[3]);
                    attDeviceOpLog.setOpValueContent1(
                        I18nUtil.i18nCode("att_device_op_log_content_update_value") + attDeviceOpLogArray[4]);
                    break;
                case 6:
                    // 用户的工号 指纹的序号 指纹模板的长度
                    attDeviceOpLog.setOpWhoContent(
                        I18nUtil.i18nCode("att_device_op_log_content_pin") + attDeviceOpLogArray[3]);
                    // attDeviceOpLog.setOpValueContent1(I18nUtil.i18nCode("att_device_op_log_content_finger_no")+attDeviceOpLogArray[4]);
                    // attDeviceOpLog.setOpValueContent2(I18nUtil.i18nCode("att_device_op_log_content_finger_size")+attDeviceOpLogArray[5]);
                    break;
                case 9:
                case 10:
                case 11:
                case 12:
                    // 用户的工号
                    attDeviceOpLog.setOpWhoContent(
                        I18nUtil.i18nCode("att_device_op_log_content_pin") + attDeviceOpLogArray[3]);
                    break;
                default:
                    break;
            }

            attLogList.add(attDeviceOpLog);
        }

        if (attLogList.size() > 0) {
            attDeviceOpLogDao.saveAll(attLogList);
        }
    }

    @Override
    public List<AttDeviceOpLogItem> getItemData(Class targetClass, AttDeviceOpLogItem codition, int begin, int end) {
        List<AttDeviceOpLogItem> items =
            attDeviceOpLogDao.getItemsDataBySql(targetClass, SQLUtil.getSqlByItem(codition), begin, end, true);
        return items;
    }
}