package com.zkteco.zkbiosecurity.att.dao;

import java.util.Collection;
import java.util.List;
import java.util.Set;

import com.zkteco.zkbiosecurity.att.model.AttTimeSlot;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 时间段
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:52
 * @since 1.0.0
 */
public interface AttTimeSlotDao extends BaseDao<AttTimeSlot, String> {

    Set<AttTimeSlot> findByIdIn(Collection<String> attTimeSlotIdList);

    /**
     * 判断时段编号是否已经存在
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/24 11:56
     * @param periodNo
     *            时段编号
     * @return boolean
     */
    boolean existsByPeriodNo(String periodNo);

    /**
     * 判断时段名称是否已经存在
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/24 11:56
     * @param periodName
     *            时段名称是
     * @return boolean
     */
    boolean existsByPeriodName(String periodName);

    /**
     * 根据时段编号，获取时段对象
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/24 11:57
     * @param periodNo
     *            时段编号
     * @return com.zkteco.zkbiosecurity.att.model.AttTimeSlot
     */
    AttTimeSlot findByPeriodNo(String periodNo);

    /**
     * 判断休息时间段 是否存在外键关联
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/7/3 20:51
     * @param breakTimeIds
     * @return boolean
     */
    boolean existsByAttBreakTimeSet_IdIn(Collection<String> breakTimeIds);

    List<AttTimeSlot> findByPeriodType(Short periodType);

}