package com.zkteco.zkbiosecurity.att.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.zkteco.zkbiosecurity.core.convert.EncryptConverter;
import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 自动导出
 */
@Entity
@Table(name = "ATT_AUTOEXPORT")
@Setter
@Getter
@Accessors(chain = true)
public class AttAutoExport extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 报表类型（原始记录表、日打卡详情表）
     */
    @Column(name = "REPORT_TYPE", length = 30)
    private String reportType;

    /**
     * 文件类型（TXT）
     */
    @Column(name = "FILE_TYPE", length = 30)
    private String fileType;

    /**
     * 文件名称
     */
    @Column(name = "FILE_NAME", length = 50)
    private String fileName;

    /**
     * 文件日期格式
     */
    @Column(name = "FILE_DATE_FORMAT", length = 30)
    private String fileDateFormat;

    /**
     * 文件时间格式
     */
    @Column(name = "FILE_TIME_FORMAT", length = 50)
    private String fileTimeFormat;

    /**
     * 文件内容格式
     */
    @Column(name = "FILE_CONTENT_FORMAT")
    private String fileContentFormat;

    /**
     * 文件字段转换 【该字段未用到】
     */
    @Column(name = "FILE_FIELD_CONVERT")
    private String fileFieldConvert;

    /**
     * 时间发送频率（时、日、周、月）
     */
    @Column(name = "TIME_SEND_FREQUENCY", length = 30)
    private String timeSendFrequency;

    /**
     * 时间发送间隔
     */
    @Column(name = "TIME_SEND_INTERVAL", length = 50)
    private String timeSendInterval;

    /**
     * 邮件类型（按人员、按部门、按区域）
     */
    @Column(name = "EMAIL_TYPE", length = 30)
    private String emailType;

    /**
     * 邮件接收人
     */
    @Column(name = "EMAIL_RECIPIENTS", length = 500)
    @Convert(converter = EncryptConverter.class)
    private String emailRecipients;

    /**
     * 邮件标题
     */
    @Column(name = "EMAIL_SUBJECT")
    private String emailSubject;

    /**
     * 邮件正文
     */
    @Column(name = "EMAIL_CONTENT")
    private String emailContent;

    /**
     * 任务名称
     */
    @Column(name = "JOB_NAME", length = 50)
    private String jobName;

    /**
     * 任务执行类
     */
    @Column(name = "JOB_CLASS", length = 50)
    private String jobClass;

    /**
     * 任务Cron表达式
     */
    @Column(name = "JOB_CRON", length = 50)
    private String jobCron;

    /**
     * 任务状态（启动、禁用），默认启动
     */
    @Column(name = "JOB_STATUS", length = 30)
    private String jobStatus;

    /**
     * 部门id
     */
    @Column(name = "AUTH_DEPT_ID", length = 50)
    private String deptId;

    /**
     * 区域id
     */
    @Column(name = "AUTH_AREA_ID", length = 50)
    private String areaId;

    /**
     * 发送方式
     */
    @Column(name = "SEND_FORMAT", length = 30)
    private String sendFormat;

    /**
     * FTP服务器地址
     */
    @Column(name = "FTP_URL", length = 30)
    private String ftpUrl;

    /**
     * FTP服务器端口
     */
    @Column(name = "FTP_PORT", length = 10)
    private Integer ftpPort;

    /**
     * FTP登录账号
     */
    @Column(name = "FTP_USERNAME", length = 30)
    private String ftpUsername;

    /**
     * FTP登录密码
     */
    @Column(name = "FTP_PASSWORD", length = 250)
    @Convert(converter = EncryptConverter.class)
    private String ftpPassword;
}