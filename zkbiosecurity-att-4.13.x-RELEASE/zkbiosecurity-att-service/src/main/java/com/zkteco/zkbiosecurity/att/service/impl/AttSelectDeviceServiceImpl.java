package com.zkteco.zkbiosecurity.att.service.impl;

import com.zkteco.zkbiosecurity.att.dao.AttDeviceDao;
import com.zkteco.zkbiosecurity.att.service.AttSelectDeviceService;
import com.zkteco.zkbiosecurity.att.vo.AttSelectDeviceItem;
import com.zkteco.zkbiosecurity.auth.provider.AuthSessionProvider;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 考勤设备选择
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:02
 * @since 1.0.0
 */
@Service
public class AttSelectDeviceServiceImpl implements AttSelectDeviceService {

    @Autowired
    private AttDeviceDao attDeviceDao;
    @Autowired
    private AuthSessionProvider authSessionProvider;
    @Autowired
    private AuthAreaService authAreaService;

    @Override
    public Pager getItemByAuthFilter(String sessionId, AttSelectDeviceItem condition, int pageNo, int pageSize) {
        String areaIds = "";
        // 获取当前登录用户信息
        SecuritySubject securitySubject = authSessionProvider.getSecuritySubject(sessionId);
        if (!securitySubject.getIsSuperuser() && securitySubject.getAreaIds() != null
            && securitySubject.getAreaIds().size() > 0) {
            // 非超级用户进行数据权限过滤
            areaIds = StrUtil.collectionToStr(securitySubject.getAreaIds());
        }
        if (StringUtils.isNotBlank(areaIds)) {
            condition.setAreaIdIn(areaIds);
        }
        Pager pager =
            attDeviceDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        if (!CollectionUtil.isEmpty(pager.getData())) {
            buildArea(pager.getData());
        }
        return pager;
    }

    private List<?> buildArea(List<?> items) {
        List<AttSelectDeviceItem> list = (List<AttSelectDeviceItem>)items;
        String areaIds = CollectionUtil.getPropertys(list, AttSelectDeviceItem::getAreaId);
        List<AuthAreaItem> areaItems = authAreaService.getItemsByIds(areaIds);
        Map<String, AuthAreaItem> areaMap = CollectionUtil.itemListToIdMap(areaItems);
        AuthAreaItem area = null;
        for (AttSelectDeviceItem item : list) {
            area = areaMap.get(item.getAreaId());
            if (area != null) {
                item.setAuthAreaName(area.getName());
            }
        }
        return items;
    }
}
