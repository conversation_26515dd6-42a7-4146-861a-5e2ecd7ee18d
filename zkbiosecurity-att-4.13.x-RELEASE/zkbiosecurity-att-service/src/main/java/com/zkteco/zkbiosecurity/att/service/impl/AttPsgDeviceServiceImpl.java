package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.service.AttGetPsgDeviceService;
import com.zkteco.zkbiosecurity.att.service.AttPointService;
import com.zkteco.zkbiosecurity.att.service.AttPsgDeviceService;
import com.zkteco.zkbiosecurity.att.vo.Att4PsgDeviceSelectItem;
import com.zkteco.zkbiosecurity.att.vo.AttPsgDeviceSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;

/**
 * 通道当考勤，获取通道设备信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:53
 * @since 1.0.0
 */
@Service
public class AttPsgDeviceServiceImpl implements AttPsgDeviceService {

    @Autowired(required = false)
    private AttGetPsgDeviceService attGetPsgDeviceService;

    @Autowired
    AttPointService attPointService;

    @Override
    public Pager getSelectPsgDevicePager(AttPsgDeviceSelectItem condition, int page, int size) {
        if (Objects.nonNull(attGetPsgDeviceService)) {
            Att4PsgDeviceSelectItem att4PsgDeviceSelectItem = new Att4PsgDeviceSelectItem();
            ModelUtil.copyPropertiesIgnoreNull(condition, att4PsgDeviceSelectItem);
            List<String> deviceModules = attPointService.getDeviceIdsByDeviceModule(ConstUtil.SYSTEM_MODULE_PASSAGE);
            // 过滤选中的id
            deviceModules.remove(condition.getSelectId());
            String deviceModuleStr = StringUtils.join(deviceModules, AttConstant.COMMA);
            if (StringUtils.isNotBlank(deviceModuleStr)) {
                att4PsgDeviceSelectItem.setIdIn(deviceModuleStr);
            } else {
                att4PsgDeviceSelectItem.setSelectId(condition.getSelectId());
            }
            Pager pager = attGetPsgDeviceService.getPsgDeviceSelectItemList(att4PsgDeviceSelectItem, page, size);
            List<Att4PsgDeviceSelectItem> selectItemList = (List<Att4PsgDeviceSelectItem>)pager.getData();
            List<AttPsgDeviceSelectItem> itemList = new ArrayList<>();
            selectItemList.forEach(item -> {
                AttPsgDeviceSelectItem attPsgDeviceSelectItem = new AttPsgDeviceSelectItem();
                ModelUtil.copyPropertiesIgnoreNull(item, attPsgDeviceSelectItem);
                itemList.add(attPsgDeviceSelectItem);
            });
            pager.setData(itemList);
            return pager;
        } else {
            Pager pager = new Pager(page, size);
            pager.setData(new ArrayList<>());
            return pager;
        }

    }

    @Override
    public List<AttPsgDeviceSelectItem> getPsgDeviceByDeviceIds(Collection<String> deviceIds) {
        List<AttPsgDeviceSelectItem> attPsgDeviceSelectItemList = new ArrayList<>();
        if (Objects.nonNull(attGetPsgDeviceService)) {
            List<Att4PsgDeviceSelectItem> psgDeviceSelectItemList =
                attGetPsgDeviceService.getPsgDeviceByGateIds(deviceIds);
            if (!CollectionUtil.isEmpty(psgDeviceSelectItemList)) {
                attPsgDeviceSelectItemList =
                    ModelUtil.copyListProperties(psgDeviceSelectItemList, AttPsgDeviceSelectItem.class);
            }
        }
        return attPsgDeviceSelectItemList;
    }

}
