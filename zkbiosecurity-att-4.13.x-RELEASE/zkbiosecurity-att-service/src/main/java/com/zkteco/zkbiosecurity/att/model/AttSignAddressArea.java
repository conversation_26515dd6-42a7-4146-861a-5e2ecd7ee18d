package com.zkteco.zkbiosecurity.att.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 签到地址-区域关联
 */
@Entity
@Table(name = "ATT_SIGN_ADDRESS_AREA")
@Setter
@Getter
@Accessors(chain = true)
public class AttSignAddressArea extends BaseModel {

    @Column(name = "SIGN_ADDRESS_ID")
    private String signAddressId;

    @Column(name = "AREA_ID")
    private String areaId;
}
