package com.zkteco.zkbiosecurity.att.model;

import java.io.Serializable;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.Set;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 周期排班（分组/部门/人员）
 */
@Entity
@Table(name = "ATT_CYCLESCH")
@Setter
@Getter
@Accessors(chain = true)
public class AttCycleSch extends BaseModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 起始日期
     */
    @Column(name = "START_DATE")
    private Date startDate;

    /**
     * 结束日期
     */
    @Column(name = "END_DATE")
    private Date endDate;

    /**
     * 排班类型(普通排班=0、智能找班=1)
     */
    @Column(name = "SCHEDULE_TYPE")
    private Short scheduleType;

    /**
     * 周期排班类型（分组=0、部门=1、人员=2）
     */
    @Column(name = "CYCLE_TYPE")
    private Short cycleType;

    /**
     * 分组id
     */
    @Column(name = "GROUP_ID", length = 50)
    private String groupId;

    /**
     * 部门id
     */
    @Column(name = "AUTH_DEPT_ID", length = 50)
    private String deptId;

    /**
     * 人员id
     */
    @Column(name = "PERS_PERSON_ID", length = 50)
    private String personId;

    /**
     * 人员编号
     */
    @Column(name = "PERS_PERSON_PIN", length = 50)
    private String personPin;

    /**
     * 班次周期排班中间表
     */
    @ManyToMany(targetEntity = AttShift.class, fetch = FetchType.LAZY)
    @JoinTable(name = "ATT_CYCLESCH_SHIFT",
        joinColumns = @JoinColumn(name = "CYCLESCH_ID", referencedColumnName = "ID"),
        inverseJoinColumns = @JoinColumn(name = "SHIFT_ID", referencedColumnName = "ID"))
    private Set<AttShift> attShiftSet = new LinkedHashSet<AttShift>();
}
