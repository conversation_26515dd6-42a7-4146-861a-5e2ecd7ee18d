package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.transaction.Transactional;

import com.zkteco.zkbiosecurity.att.model.AttTempSch;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.cmd.att.constants.CmdAttConstants;
import com.zkteco.zkbiosecurity.pers.service.PersPersonExtService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.att.cache.AttCacheManager;
import com.zkteco.zkbiosecurity.att.cmd.AttDevCmdManager;
import com.zkteco.zkbiosecurity.att.constants.AttCommonSchConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.*;
import com.zkteco.zkbiosecurity.att.model.AttAreaPerson;
import com.zkteco.zkbiosecurity.att.model.AttDevice;
import com.zkteco.zkbiosecurity.att.model.AttPerson;
import com.zkteco.zkbiosecurity.att.operate.AttSetPersonToDevOperate;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

@Service
public class Att4PersPersonServiceImpl implements Att4PersPersonService {

    @Autowired
    private AttPersonDao attPersonDao;
    @Autowired
    private AttAreaPersonService attAreaPersonService;
    @Autowired
    private AttDeviceDao attDeviceDao;
    @Autowired
    private AttAreaPersonDao attAreaPersonDao;
    @Autowired
    private AttDevCmdManager attDevCmdManager;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private AttTempSchDao attTempSchDao;
    @Autowired
    private AttSetPersonToDevOperate attSetPersonToDevOperate;
    @Autowired
    private AttCacheManager attCacheManager;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttCycleSchDao attCycleSchDao;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttAnnualLeaveReportService attAnnualLeaveReportService;
    @Autowired(required = false)
    private AttSdc4AttPersonService attSdc4AttPersonService;
    @Resource(name="attPersonExtServiceImpl")
    private PersPersonExtService attPersonExtService;

    @Override
    @Transactional
    public Boolean editAttPerson(Att4PersPersonItem attPerson) {
        String personId = attPerson.getPersonId();
        AttPerson tempPerson = attPersonDao.findByPersonId(personId);

        Short verifyMode = attPerson.getVerifyMode();
        Short perDevAuth = attPerson.getPerDevAuth();
        Boolean isAttendance = attPerson.getIsAttendance();

        if (Objects.isNull(tempPerson)) {
            tempPerson = new AttPerson();
            tempPerson.setGroupId(AttCommonSchConstant.GroupConstant.DEFAULT_NO_GROUP_ID);
            // 考勤参数
            tempPerson.setVerifyMode(verifyMode == null ? 0 : verifyMode);
            tempPerson.setPerDevAuth(perDevAuth == null ? 0 : perDevAuth);
            tempPerson.setIsAttendance(isAttendance == null ? true: isAttendance);
        } else if (attPerson.getUpdateAttSet()) {
            if (verifyMode != null) {
                tempPerson.setVerifyMode(verifyMode);
            }
            if (perDevAuth != null ) {
                tempPerson.setPerDevAuth(perDevAuth);
            }
            if (isAttendance != null ) {
                tempPerson.setIsAttendance(isAttendance);
            }
        }

        // 人员基本信息
        tempPerson.setPersonId(attPerson.getPersonId());
        tempPerson.setPersonPin(attPerson.getPersonPin());

        // 计算年假
        if (attParamService.enableAnnualLeave()) {
            // 人员更新计算总年假
            if (attPerson.getHireDate() != null) {
                String calculateType = attParamService.getAnnualLeaveCalculateType();
                String rule = attParamService.getAnnualLeaveCalculateRule();
                List<AttAnnualLeaveRuleItem> attAnnualLeaveRuleItemList =
                    attAnnualLeaveReportService.buildRuleItem(rule);
                Date annualLeaveCalculateDate = attParamService.getAnnualLeaveCalculateDate();
                Map<String, String> annualLeaveMap = attAnnualLeaveReportService.calculateAnnualLeaveDays(
                    attPerson.getHireDate(), calculateType, attAnnualLeaveRuleItemList, annualLeaveCalculateDate);
                tempPerson.setAnnualLeaveDays(Integer.valueOf(annualLeaveMap.get("days")));
                tempPerson.setAnnualValidDate(
                    DateUtil.stringToDate(annualLeaveMap.get("validDate"), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                tempPerson.setWorkLong(annualLeaveMap.get("workLong"));
            } else {
                tempPerson.setAnnualLeaveDays(null);
                tempPerson.setWorkLong("");
                if (tempPerson.getAnnualAdjustDays() == null) {
                    tempPerson.setAnnualValidDate(null);
                }
            }
        }

        // 保存考勤人员
        attPersonDao.save(tempPerson);
        // 人事人员编辑更新人事人员缓存的考勤附加参数
        attPersonService.updatePersonCacheExtParams(ModelUtil.copyProperties(tempPerson, new AttPersonItem()));

        // 解决自定义系统用户，非考勤设置权限时候，编辑人事，下发删除指令后，设备上信息和权限丢失问题 add by max 20190521
        if (attPerson.getUpdateAttSet()) {
            updateAttSet(attPerson, personId);
        } else {
            // 判断人员是否在区域里，区域是否关联考勤设备，如果有的话需要下发人员基本信息给考勤设备 add by max 20190521
            List<AttAreaPerson> areaPersonList = attAreaPersonDao.findByPersonIdIn(Arrays.asList(personId));
            if (!CollectionUtils.isEmpty(areaPersonList)) {
                for (AttAreaPerson attAreaPerson : areaPersonList) {

                    // 同步人员之前先清空模板
                    cleanBioDataToDevice(personId);
                    // 同步人员
                    attAreaPersonService.syncAttPersonToDevice(personId, attAreaPerson.getAreaId(), "-1");
                    // sdc设备添加人员
                    if (Objects.nonNull(attSdc4AttPersonService)) {
                        ArrayList<String> areaIdList = new ArrayList<>();
                        areaIdList.add(attAreaPerson.getAreaId());
                        this.addPersonToSdc(attPerson, areaIdList);
                    }
                }
            }
        }
        return true;
    }

    private void updateAttSet(Att4PersPersonItem attPerson, String personId) {
        // 根据人员所在区域，添加指定区域，下发人员信息
        if (StringUtils.isNotBlank(attPerson.getAreaIds())) {
            // 下发人员需要过滤的设备
            String devSn = StringUtils.defaultString(attPerson.getDevSn(), AttConstant.COMM_DEF_VALUE);
            // 查询此人员下的areaId
            List<AttAreaPerson> areaPersonList = attAreaPersonDao.findByPersonIdIn(Arrays.asList(personId));
            List<String> oldAreaIdList =
                areaPersonList.stream().map(AttAreaPerson::getAreaId).collect(Collectors.toList());
            // 修改以后的区域
            List<String> newAreaIdList = Arrays.asList(StringUtils.split(attPerson.getAreaIds(), ","));
            // 修改以后的区域,跟之前已有的区域进行对比,筛除已经没有选中的区域,进行删除。
            List<String> deleteAreaList = ListUtils.removeAll(oldAreaIdList, newAreaIdList);

            for (String deleteAreaId : deleteAreaList) {
                attAreaPersonDao.deleteByPersonIdAndAreaId(personId, deleteAreaId);
                // 删除设备的数据
                List<AttDevice> attDeviceList =
                    attDeviceDao.findByAreaIdAndDevSnNotIn(deleteAreaId, CollectionUtil.strToList(devSn));
                attDeviceList.forEach(attDevice -> {
                    // 删除区域人员命令
                    attDevCmdManager.delUserInfo(attDevice, attPerson.getPersonPin());
                });
                if (Objects.nonNull(attSdc4AttPersonService)) {
                    // 删除区域的设备的人员
                    attSdc4AttPersonService.deletePerson(deleteAreaId, attPerson.getPersonPin());
                }
            }
            // 直接下发人员信息到设备（去掉先删除再下发的操作） -- add by hook.fang 2020-12-14
            for (String areaId : newAreaIdList) {

                // 同步人员之前先清空模板
                cleanBioDataToDevice(personId);
                // 同步人员
                attAreaPersonService.syncAttPersonToDevice(personId, areaId, devSn);
            }
            if (Objects.nonNull(attSdc4AttPersonService)) {
                this.addPersonToSdc(attPerson, newAreaIdList);
            }
        } else {
            // 没有设置对应的区域的情况下
            // 下发人员需要过滤的设备
            String devSn = StringUtils.defaultString(attPerson.getDevSn(), AttConstant.COMM_DEF_VALUE);
            // 查询此人员下的areaId
            List<AttAreaPerson> areaPersonList = attAreaPersonDao.findByPersonIdIn(Arrays.asList(personId));
            List<String> oldAreaIdList =
                areaPersonList.stream().map(AttAreaPerson::getAreaId).collect(Collectors.toList());
            // 修改以后的区域
            List<String> newAreaIdList = new ArrayList<>();
            // 修改以后的区域,跟之前已有的区域进行对比,筛除已经没有选中的区域,进行删除。
            List<String> deleteAreaList = ListUtils.removeAll(oldAreaIdList, newAreaIdList);

            for (String deleteAreaId : deleteAreaList) {
                attAreaPersonDao.deleteByPersonIdAndAreaId(personId, deleteAreaId);
                // 删除设备的数据
                List<AttDevice> attDeviceList =
                    attDeviceDao.findByAreaIdAndDevSnNotIn(deleteAreaId, CollectionUtil.strToList(devSn));
                attDeviceList.forEach(attDevice -> {
                    // 删除区域人员命令
                    attDevCmdManager.delUserInfo(attDevice, attPerson.getPersonPin());
                });
                if (Objects.nonNull(attSdc4AttPersonService)) {
                    // 删除区域的设备的人员
                    attSdc4AttPersonService.deletePerson(deleteAreaId, attPerson.getPersonPin());
                }
            }
        }
    }

    /**
     * 人员模板删除后，需要先清空，再同步，不然软件删除后，设备还会在
     * 1、删除指纹模板
     * @param personId:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2025-04-08 10:19
     * @since 1.0.0
     */
    private void cleanBioDataToDevice(String personId) {
        List<Short> bioTypes = new ArrayList<>();
        bioTypes.add(BaseConstants.BaseBioType.FP_BIO_TYPE);
        bioTypes.add(CmdAttConstants.TEMPLATE_VISILIGHT_PALM);
        attPersonExtService.deletePersonBioTemplate(Arrays.asList(personId), bioTypes);
    }

    /**
     * sdc设备添加人员
     *
     * @param attPerson
     * @param newAreaIdList
     * @return void
     * <AUTHOR>
     * @date 2020-11-23 9:34
     * @since 1.0.0
     */
    private void addPersonToSdc(Att4PersPersonItem attPerson, List<String> newAreaIdList) {
        // 添加人员到区域的设备
        PersPersonItem persPersonItem = persPersonService.getItemByPin(attPerson.getPersonPin());
        if (StringUtils.isNotBlank(persPersonItem.getPhotoPath())) {
            AttSdc4AttPersonItem attSdc4AttPersonItem = new AttSdc4AttPersonItem();
            attSdc4AttPersonItem.setPin(persPersonItem.getPin());
            attSdc4AttPersonItem.setName(persPersonItem.getName());
            attSdc4AttPersonItem.setGender(
                StringUtils.isBlank(persPersonItem.getGender()) ? 2 : ("M".equals(persPersonItem.getGender()) ? 0 : 1));
            attSdc4AttPersonItem.setPhotoPath(FileUtils.getLocalFullPath(persPersonItem.getPhotoPath()));
            attSdc4AttPersonItem.setAreaIds(newAreaIdList);
            attSdc4AttPersonService.addPerson(attSdc4AttPersonItem);
        }
    }

    @Override
    public Boolean checkDelPerson(String personIds) {
        return true;
    }

    @Override
    @Transactional
    public void delAttPerson(String personIds) {

        if (StringUtils.isBlank(personIds)) {
            return;
        }

        // 人员删除，删除对应的设备人员信息
        delDevUserInfo(personIds);

        // 人员删除，删除对应的人员排班
        List<String> personIdList = StrUtil.strToList(personIds);
        attTempSchDao.deleteByPersonIdIn(personIdList);
        attCycleSchDao.deleteByPersonIdIn(personIdList);
    }

    @Override
    public Boolean leaveAttPerson(String personIds) {

        if (StringUtils.isBlank(personIds)) {
            return true;
        }

        // 人员离职，删除对应的设备人员信息
        delDevUserInfo(personIds);

        return true;
    }

    /**
     * 人员删除或者离职，删除对应的设备人员信息
     *
     * @param personIds:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/3/30 10:49
     * @since 1.0.0
     */
    private void delDevUserInfo(String personIds) {

        // 人员ID和编号集合
        List<String> personIdList = StrUtil.strToList(personIds);
        List<PersPersonItem> persPersonItemList = persPersonService.getSimpleItemsByIds(personIdList);
        Map<String, String> personIdPinMap =
            persPersonItemList.stream().collect(Collectors.toMap(PersPersonItem::getId, PersPersonItem::getPin));

        // 人员ID和人员区域集合
        List<Object[]> objectList = attAreaPersonDao.getAreaIdsByPersonIds(personIdList);
        // key 为 areaID Object[] 下标：0：persionId 下标：1 areaid
        Map<String, List<Object[]>> areaIdPersonMap =
            objectList.stream().collect(Collectors.groupingBy((Object[] o) -> o[1].toString()));

        // 下发删除设备人员信息
        Set<String> areaIdSet = areaIdPersonMap.keySet();
        if (areaIdSet != null && areaIdSet.size() > 0) {
            List<List<String>> areaIdListGroup = CollectionUtil.split(areaIdPersonMap.keySet(), 500);
            for (List<String> areaIdList : areaIdListGroup) {
                List<AttDevice> attDeviceList = attDeviceDao.findByAreaIdIn(areaIdList);
                attDeviceList.forEach(device -> {
                    List<Object[]> objects = areaIdPersonMap.get(device.getAreaId());
                    if (!CollectionUtil.isEmpty(objects)) {
                        objects.forEach((Object[] o) -> {
                            attDevCmdManager.delUserInfo(device, personIdPinMap.get(o[0].toString()));
                        });
                    }
                });
            }
        }

        // 下发删除SDC设备人员信息
        if (Objects.nonNull(attSdc4AttPersonService)) {
            for (PersPersonItem persPersonItem : persPersonItemList) {
                // 删除区域的设备的人员
                List<String> areaIds = attAreaPersonDao.getAreaIdsByPersonId(persPersonItem.getId());
                for (String areaId : areaIds) {
                    attSdc4AttPersonService.deletePerson(areaId, persPersonItem.getPin());
                }
            }
        }

        // 删除考勤区域 人员
        attAreaPersonDao.deleteByPersonIdIn(personIdList);
        // 删除考勤人员
        attPersonDao.deleteByPersonIdIn(personIdList);
    }

    @Override
    public Map<String, AttPers4PersLeaveItem> getAttPersonGroupId(String personIds) {
        List<String> personIdList = new ArrayList<String>(Arrays.asList(personIds.split(",")));
        List<AttPerson> attPersonList = attPersonDao.findByPersonIdIn(personIdList);
        Map<String, AttPers4PersLeaveItem> groupIdMap = new HashMap<String, AttPers4PersLeaveItem>();
        attPersonList.forEach(attPerson -> {
            AttPers4PersLeaveItem attPersLeaveItem = new AttPers4PersLeaveItem();
            attPersLeaveItem.setIsAttendance(attPerson.getIsAttendance());
            if (!AttCommonSchConstant.GroupConstant.DEFAULT_NO_GROUP_ID.equals(attPerson.getGroupId())) {
                attPersLeaveItem.setGroupId(attPerson.getGroupId());
            } else {
                attPersLeaveItem.setGroupId(null);
            }
            groupIdMap.put(attPerson.getPersonId(), attPersLeaveItem);
        });
        return groupIdMap;
    }

    @Override
    @Transactional
    public void batchDeptChange(List<String> personIds, String deptId) {
        List<AttTempSch> attTempSchList = attTempSchDao.findByPersonIdIn(personIds);
        attTempSchList.forEach(attTempSch -> {
            attTempSch.setDeptId(deptId);
        });
        attTempSchDao.saveAll(attTempSchList);
    }

    @Override
    @Transactional
    public void batchPerson(List<Att4PersPersonItem> attPersons) {

    }

    @Override
    public List<Att4PersPersonItem> getPersPersonByDeptIds(List<String> deptIds) {
        List<Att4PersPersonItem> att4PersPersonItems = new ArrayList<>();
        if (CollectionUtil.isEmpty(deptIds)) {
            return att4PersPersonItems;
        }
        List<List<String>> splitDeptIds = CollectionUtil.split(deptIds, CollectionUtil.splitSize);
        for (List<String> splitDeptId : splitDeptIds) {
            List<PersPersonItem> persPersonItems = persPersonService.getPersPersonByDeptIds(splitDeptId);
            persPersonItems.forEach(persPersonItem -> {
                Att4PersPersonItem att4PersPersonItem = new Att4PersPersonItem();
                att4PersPersonItem.setDeptCode(persPersonItem.getDeptCode());
                att4PersPersonItem.setDeptId(persPersonItem.getDeptId());
                att4PersPersonItem.setDeptName(persPersonItem.getDeptName());
                att4PersPersonItem.setHireDate(persPersonItem.getHireDate());
                att4PersPersonItem.setPersonId(persPersonItem.getId());
                att4PersPersonItem.setPersonLastName(persPersonItem.getLastName());
                att4PersPersonItem.setPersonName(persPersonItem.getName());
                att4PersPersonItem.setPersonPin(persPersonItem.getPin());

                att4PersPersonItems.add(att4PersPersonItem);
            });
        }
        return att4PersPersonItems;
    }

    @Override
    public Map<String, String> getAttPersonExtParam(String personId) {
        Map<String, String> extParams = new HashMap<>();
        AttPerson attPerson = attPersonDao.findByPersonId(personId);
        if (Objects.nonNull(attPerson)) {
            List<String> areaIdList = attAreaPersonDao.getAreaIdsByPersonId(personId);
            extParams.put("att.personAreas",
                CollectionUtil.isEmpty(areaIdList) ? "" : StringUtils.join(areaIdList, ","));
            extParams.put("att.isAttendance", String.valueOf(attPerson.getIsAttendance()));
            extParams.put("att.perDevAuth", String.valueOf(attPerson.getPerDevAuth()));
        }
        return extParams;
    }

    @Override
    public void reIssuePersonInfo(String personId) {
        AttPerson attPerson = attPersonDao.findByPersonId(personId);
        if (Objects.nonNull(attPerson)) {
            List<AttAreaPerson> attAreaPersonList = attAreaPersonDao.findByPersonId(personId);
            if (!CollectionUtil.isEmpty(attAreaPersonList)) {
                for (AttAreaPerson attAreaPerson : attAreaPersonList) {
                    List<AttDevice> attDeviceList = attDeviceDao.findByAreaIdAndDevSnNotIn(attAreaPerson.getAreaId(),
                        CollectionUtil.strToList(AttConstant.COMM_DEF_VALUE));
                    attSetPersonToDevOperate.sendPersonToDevice(Arrays.asList(personId), attDeviceList);
                }
            }
        }

    }

    @Override
    public Boolean singleAttSystem() {
        return attParamService.singleAttSystem();
    }
}
