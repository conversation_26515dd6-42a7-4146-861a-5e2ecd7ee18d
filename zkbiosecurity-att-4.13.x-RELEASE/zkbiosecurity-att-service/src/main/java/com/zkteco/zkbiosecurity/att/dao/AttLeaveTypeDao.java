/**
 * File Name: AttLeaveType Created by GenerationTools on 2018-02-08 下午08:27 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.att.dao;

import com.zkteco.zkbiosecurity.att.model.AttLeaveType;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;

/**
 * 对应百傲瑞达 AttLeaveTypeDao
 * 
 * <AUTHOR>
 * @date: 2018-02-08 下午08:27
 * @version v1.0
 */
public interface AttLeaveTypeDao extends BaseDao<AttLeaveType, String> {

    /**
     * 根据请假编号，获取请假类型对象
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/24 11:43
     * @param leaveTypeNo
     * @return com.zkteco.zkbiosecurity.att.model.AttLeaveType
     */
    AttLeaveType findByLeaveTypeNo(String leaveTypeNo);

    /**
     * 根据请假名称，获取请假类型对象
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/5/24 16:56
     * @param leaveTypeName
     * @return com.zkteco.zkbiosecurity.att.model.AttLeaveType
     */
    AttLeaveType findByLeaveTypeName(String leaveTypeName);

    /**
     * 判断请假名称，是否存在
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/24 11:42
     * @param leaveTypeName
     * @return boolean
     */
    boolean existsByLeaveTypeName(String leaveTypeName);

    /**
     * 查询所有假种编号
     * 
     * @return
     */
    @Query("SELECT DISTINCT t.leaveTypeNo FROM AttLeaveType t")
    List<String> findLeaveTypeNo();

    /***
     *
     * 根据ids获取请假类型
     *
     * @param leaveTypeIdList
     * @return: java.util.List<com.zkteco.zkbiosecurity.att.model.AttLeaveType>
     * @throws:
     * @author: bob.liu
     * @time: 2020/5/25 17:57
     * @since 1.0.0
     */
    List<AttLeaveType> findByIdIn(Collection<String> leaveTypeIdList);

    List<AttLeaveType> findByMark(String mark);

    List<AttLeaveType> findByMarkOrderBySortNoAsc(String mark);
}