package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.service.AttAccDeviceService;
import com.zkteco.zkbiosecurity.att.service.AttGetAccDoorSelectService;
import com.zkteco.zkbiosecurity.att.service.AttGetAccDoorService;
import com.zkteco.zkbiosecurity.att.service.AttPointService;
import com.zkteco.zkbiosecurity.att.vo.Att4AccDoorSelect;
import com.zkteco.zkbiosecurity.att.vo.AttSelectDoorItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;

/**
 * 门禁当考勤
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/2 9:04
 * @since 1.0.0
 */
@Service
public class AttAccDeviceServiceImpl implements AttAccDeviceService {

    @Autowired(required = false)
    private AttGetAccDoorSelectService attGetAccDoorSelectService;
    @Autowired(required = false)
    private AttGetAccDoorService attGetAccDoorService;
    @Autowired
    private AttPointService attPointService;

    @Override
    public AttSelectDoorItem getAccDeviceByDeviceId(String deviceId) {
        if (Objects.nonNull(attGetAccDoorService)) {
            Att4AccDoorSelect att4AccDoorSelect = attGetAccDoorService.getDoorById(deviceId);
            if (Objects.nonNull(att4AccDoorSelect)) {
                AttSelectDoorItem attSelectDoorItem = new AttSelectDoorItem();
                ModelUtil.copyProperties(att4AccDoorSelect, attSelectDoorItem);
                return attSelectDoorItem;
            }
        }
        return null;
    }

    @Override
    public AttSelectDoorItem getAccDeviceByDevSnAndDoorNo(String deviceSn, Short doorNo) {
        if (Objects.nonNull(attGetAccDoorService)) {
            Att4AccDoorSelect att4AccDoorSelect = attGetAccDoorService.getDoorByDevSnAndDoorNo(deviceSn, doorNo);
            if (Objects.nonNull(att4AccDoorSelect)) {
                AttSelectDoorItem attSelectDoorItem = new AttSelectDoorItem();
                ModelUtil.copyProperties(att4AccDoorSelect, attSelectDoorItem);
                return attSelectDoorItem;
            }
        }
        return null;
    }

    @Override
    public List<AttSelectDoorItem> getAccDeviceByDeviceIds(Collection<String> deviceIds) {
        List<AttSelectDoorItem> attSelectDoorItemList = new ArrayList<>();
        if (Objects.nonNull(attGetAccDoorService)) {
            List<Att4AccDoorSelect> att4AccDoorSelectList = attGetAccDoorService.getDoorsByIds((List<String>)deviceIds);
            if (!CollectionUtil.isEmpty(att4AccDoorSelectList)) {
                attSelectDoorItemList = ModelUtil.copyListProperties(att4AccDoorSelectList, AttSelectDoorItem.class);
            }
        }
        return attSelectDoorItemList;
    }

    @Override
    public Pager getSelectAccDevicePager(AttSelectDoorItem condition, int page, int size) {
        if (Objects.nonNull(attGetAccDoorSelectService)) {
            // 已经添加的门
            List<String> deviceModules = attPointService.getDeviceIdsByDeviceModule(ConstUtil.SYSTEM_MODULE_ACC);
            // 构建模块间调用vo查询实体对象 add by max 20181119
            Att4AccDoorSelect att4AccDoorSelect = new Att4AccDoorSelect();
            ModelUtil.copyPropertiesIgnoreNull(condition, att4AccDoorSelect);
            String deviceModuleStr = StringUtils.join(deviceModules, ",");
            if (StringUtils.isNotBlank(deviceModuleStr)) {
                att4AccDoorSelect.setNotInId(condition.getSelectId() + "," + deviceModuleStr);
            } else {
                att4AccDoorSelect.setNotInId(condition.getSelectId());
            }

            Pager pager = attGetAccDoorSelectService.getAccDoorSelectList(att4AccDoorSelect, page, size);
            List<Att4AccDoorSelect> att4SelectDoors = (List<Att4AccDoorSelect>)pager.getData();
            List<AttSelectDoorItem> itemList = new ArrayList<>();
            att4SelectDoors.forEach(item -> {
                AttSelectDoorItem attSelectDoorItem = new AttSelectDoorItem();
                ModelUtil.copyPropertiesIgnoreNull(item, attSelectDoorItem);
                itemList.add(attSelectDoorItem);
            });
            pager.setData(itemList);
            return pager;
        } else {
            Pager pager = new Pager(page, size);
            pager.setData(new ArrayList<>());
            return pager;
        }
    }
}
