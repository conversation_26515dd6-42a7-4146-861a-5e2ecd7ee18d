package com.zkteco.zkbiosecurity.att.utils;

import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * 文本操作工具 文本操作相关方法，命名以Att开头，Utils结尾
 * 
 * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
 * @date 2019/4/24 17:51
 */
public class AttTxtUtils {
    /**
     * 描述:导出TXT文本数据
     * 
     * @param data List<Map<String, Object>> 数据
     * @param dataFormat 数据格式 必须为 ：xx{XXXX}xx 这种格式
     * @param filePath 文件路径
     * @param fileName 文件名称
     * @param filterData 过虑数据
     * @throws Exception
     */
    public static void exportTxtFile(List<Map<String, Object>> data, String dataFormat, String filePath,
        String fileName, Map<String, Object> filterData, List<AttLeaveTypeItem> attLeaveTypeItemList) throws Exception {
//        if (data != null && !data.isEmpty()) {
            File file = new File(filePath + fileName);
            // 修改中文乱码问题
            OutputStreamWriter osw = new OutputStreamWriter(new FileOutputStream(file), StandardCharsets.UTF_8);
            BufferedWriter bw = new BufferedWriter(osw);

            Map<String, AttLeaveTypeItem> attLeaveTypeNameMap = null;
            if (attLeaveTypeItemList != null) {
                attLeaveTypeNameMap =
                    CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getLeaveTypeName);
            }
            for (Map<String, Object> map : data) {
                bw.append(handlerRowData(map, dataFormat, filterData, attLeaveTypeNameMap));
                bw.flush();
            }
            bw.close();
            osw.close();
            osw = null;
            bw = null;
//        } else {
//            throw new RuntimeException("data cannot be null. ");
//        }
    }

    private static String handlerRowData(Map<String, Object> map, String dataFormat, Map<String, Object> filterData,
        Map<String, AttLeaveTypeItem> attLeaveTypeNameMap) {
        if (dataFormat == null || "".equals(dataFormat)) {
            throw new RuntimeException("dataFormat cannot be null. ");
        }
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if ("attDayDetailReportLeaveMinuteMap".equals(entry.getKey()) || "attMonthDetailReportLeaveHourMap".equals(entry.getKey())) {
                // 日报表的请假详情处理
                Map<String, Object> attDayDetailReportLeaveMinuteMap = (Map<String, Object>)entry.getValue();
                StringBuffer sb = new StringBuffer();
                for (Map.Entry<String, Object> leaveEntry : attDayDetailReportLeaveMinuteMap.entrySet()) {
                    String unit = "";
                    if (attLeaveTypeNameMap != null) {
                        unit = AttCommonUtils.getUnit(attLeaveTypeNameMap.get(leaveEntry.getKey()));
                    }
                    sb.append(leaveEntry.getKey()).append(":").append(leaveEntry.getValue()).append(unit).append(" ");
                }
                if ("attDayDetailReportLeaveMinuteMap".equals(entry.getKey())){
                    dataFormat = dataFormat.replace("{attDayDetailReportLeaveMinuteMap}", sb.toString());
                } else if ("attMonthDetailReportLeaveHourMap".equals(entry.getKey())){
                    dataFormat = dataFormat.replace("{attMonthDetailReportLeaveHourMap}", sb.toString());
                }

            } else if ("monthDetailMap".equals(entry.getKey())) {
                // 月考勤状态表详情处理
                Map<String, Object> monthDetailMap = (Map<String, Object>)entry.getValue();
                StringBuffer sb = new StringBuffer();
                for (Map.Entry<String, Object> leaveEntry : monthDetailMap.entrySet()) {
                    sb.append(leaveEntry.getKey()).append(":").append(leaveEntry.getValue()).append(" ");
                }
                dataFormat = dataFormat.replace("{monthDetailMap}", sb.toString());
            } else {
                dataFormat = dataFormat.replace("{" + entry.getKey() + "}", handlerValue(entry.getValue(), filterData));
            }
        }
        return dataFormat;
    }

    private static String handlerValue(Object val, Map<String, Object> filterData) {
        if (val == null || "".equals(val)) {
            return "";
        }
        String value = String.valueOf(val);
        if (filterData != null && !filterData.isEmpty()) {
            for (Map.Entry<String, Object> entry : filterData.entrySet()) {
                value = value.replace(entry.getKey(), String.valueOf(entry.getValue()));
            }
        }
        return value;
    }

}
