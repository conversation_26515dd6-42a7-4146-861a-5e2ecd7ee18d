package com.zkteco.zkbiosecurity.att.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备
 */
@Entity
@Table(name = "ATT_DEVICE")
@Setter
@Getter
@Accessors(chain = true)
public class AttDevice extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备名称
     */
    @Column(name = "DEV_NAME", length = 50)
    private String devName;

    /**
     * 设备型号
     */
    @Column(name = "DEV_MODEL", length = 50)
    private String devModel;

    /**
     * 设备序列号
     */
    @Column(name = "DEV_SN", unique = true, length = 50)
    private String devSn;

    /**
     * 设备状态
     */
    @Column(name = "DEV_STATUS")
    private Short devStatus;

    /**
     * 固件版本号
     */
    @Column(name = "FW_VERSION", length = 50)
    private String fwVersion;

    /**
     * 指纹版本
     */
    @Column(name = "FP_VERSION", length = 50)
    private String fpVersion;

    /**
     * 面部版本
     */
    @Column(name = "FACE_VERSION", length = 50)
    private String faceVersion;

    /**
     * ip地址
     */
    @Column(name = "IP_ADDRESS", length = 50)
    private String ipAddress;

    /**
     * 通讯端口
     */
    @Column(name = "COMM_PORT")
    private Integer commPort;

    /**
     * 通信方式
     */
    @Column(name = "PROTOCOL", length = 50)
    private String protocol;

    /**
     * 通信密码
     */
    @Column(name = "PUSH_COMMKEY", length = 50)
    private String pushcommkey;

    /**
     * 指纹数
     */
    @Column(name = "FP_COUNT")
    private Integer fpCount;

    /**
     * 人员数
     */
    @Column(name = "PERSON_COUNT")
    private Integer personCount;

    /**
     * 人脸数
     */
    @Column(name = "FACE_COUNT")
    private Integer faceCount;

    /**
     * 是否登记机
     */
    @Column(name = "IS_REG_DEVICE")
    private Boolean isRegDevice;

    /**
     * 数据更新标志
     */
    @Column(name = "UPDATE_FLAG", length = 50)
    private String updateFlag;

    /**
     * 时区
     */
    @Column(name = "TIME_ZONE", length = 50)
    private String timeZone;

    /**
     * 刷新间隔时间(分)
     */
    @Column(name = "TRANS_INTERVAL")
    private Short transInterval;

    /**
     * 定时传送时间(如：00:00;14:05)
     */
    @Column(name = "TRANS_TIMES")
    private String transTimes;

    /**
     * 实时上传数据
     */
    @Column(name = "REAL_TIME")
    private Boolean realTime;

    /**
     * 和服务器通讯的最大命令个数
     */
    @Column(name = "CMD_COUNT")
    private Short cmdCount;

    /**
     * 查询记录时间（秒）
     */
    @Column(name = "SEARCH_INTERVAL")
    private Short searchInterval;

    /**
     * 数据下发标志
     */
    @Column(name = "DATA_DOWN_FLAG")
    private String dataDownFlag;

    /**
     * 启用状态
     */
    @Column(name = "STATUS")
    private Boolean status;

    /**
     * 记录数
     */
    @Column(name = "RECORD_COUNT")
    private Integer recordCount;

    /**
     * 考勤记录时间戳
     */
    // @Column(name = "ATT_LOG_STAMP")
    // private String attLogStamp;

    /**
     * 操作记录时间戳
     */
    // @Column(name = "OP_LOG_STAMP")
    // private String opLogStamp;

    /**
     * 考勤照片时间戳
     */
    // @Column(name = "ATT_PHOTO_STAMP")
    // private String attPhotoStamp;

    /**
     * 区域id
     */
    @Column(name = "AUTH_AREA_ID")
    private String areaId;

    /**
     * 设备参数
     */
    @OneToMany(mappedBy = "attDevice")
    private List<AttDeviceOption> attDeviceOptionList = new ArrayList<>();

    public AttDevice() {
        super();
    }

    public AttDevice(String id) {
        this.id = id;
    }
}