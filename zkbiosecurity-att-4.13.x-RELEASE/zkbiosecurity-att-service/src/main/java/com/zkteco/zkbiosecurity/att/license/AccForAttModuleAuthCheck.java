//package com.zkteco.zkbiosecurity.att.license;
//
//import com.zkteco.zkbiosecurity.att.service.AttPointService;
//import com.zkteco.zkbiosecurity.core.guard.annotation.InmutableClassSign;
//import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
//import com.zkteco.zkbiosecurity.license.vo.IModuleAuthDefault;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * 门禁当考勤许可-登录检测
// *
// * <AUTHOR>
// * @version 0.0.1
// * @since 2018年07月25日 上午10:49:47
// */
//
//@Component
//@InmutableClassSign(module = ConstUtil.SYSTEM_MODULE_ATT)
//public class AccForAttModuleAuthCheck implements IModuleAuthDefault {
//    @Autowired
//    private AttPointService attPointService;
//
//    @Override
//    public boolean enable() {
//        return true;
//    }
//
//    @Override
//    public String module() {
//        return ConstUtil.LICENSE_MODULE_ACCESS_CONTROL_DOOR;
//    }
//
//    @Override
//    public int controlCount() {
//        return attPointService.getCountByDeviceModule(ConstUtil.SYSTEM_MODULE_ACC);
//    }
//}
