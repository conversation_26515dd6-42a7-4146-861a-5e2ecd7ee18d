package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.att.service.AttPointService;
import com.zkteco.zkbiosecurity.att.vo.AttPointItem;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.psg.service.PsgDevice4OtherService;

/**
 * 通道设备通知实现
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:52
 * @since 1.0.0
 */
@Service
public class AttPsgDevice4OtherServiceImpl implements PsgDevice4OtherService {

    @Autowired
    private AttPointService attPointService;

    @Override
    public void checkPsgDeviceIsUsedByGateIds(String gateIds) {
        AttPointItem condition = new AttPointItem();
        condition.setInDeviceId(gateIds);
        List<AttPointItem> attPointItemList = attPointService.getByCondition(condition);
        if (!CollectionUtil.isEmpty(attPointItemList)) {
            throw ZKBusinessException
                .warnException(I18nUtil.i18nCode("common_prompt_canNotDel", I18nUtil.i18nCode("att_module")));
        }
    }
}
