package com.zkteco.zkbiosecurity.att.service.impl;

import com.zkteco.zkbiosecurity.att.service.AttDeviceService;
import com.zkteco.zkbiosecurity.att.vo.AttDeviceItem;
import com.zkteco.zkbiosecurity.line.service.LineGetAttDeviceService;
import com.zkteco.zkbiosecurity.line.vo.LineGetAttDeviceItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-10-11 16:26
 */
@Service
public class LineGetAttDeviceServiceImpl implements LineGetAttDeviceService {

    @Autowired
    private AttDeviceService attDeviceService;

    @Override
    public List<LineGetAttDeviceItem> getAttDeviceItems() {
        List<AttDeviceItem> attDeviceItems = attDeviceService.getAllItems();
        List<LineGetAttDeviceItem> lineGetAttDeviceItems = null;
        if (attDeviceItems != null && attDeviceItems.size() > 0) {
            lineGetAttDeviceItems = new ArrayList<LineGetAttDeviceItem>();
            LineGetAttDeviceItem item = null;
            for (AttDeviceItem attDeviceItem : attDeviceItems) {
                item = new LineGetAttDeviceItem();
                item.setAttDevId(attDeviceItem.getId());
                item.setAttDevName(attDeviceItem.getDevName());
                item.setAttDevSn(attDeviceItem.getDevSn());
                lineGetAttDeviceItems.add(item);
            }
        }
        return lineGetAttDeviceItems;
    }

    @Override
    public List<LineGetAttDeviceItem> getAttDeviceItemByDevIdIn(Collection<String> devIdList) {
        List<AttDeviceItem> attDeviceItems = attDeviceService.getItemsByIds(devIdList);
        List<LineGetAttDeviceItem> lineGetAttDeviceItems = null;
        if (attDeviceItems != null && attDeviceItems.size() > 0) {
            lineGetAttDeviceItems = new ArrayList<LineGetAttDeviceItem>();
            LineGetAttDeviceItem item = null;
            for (AttDeviceItem attDeviceItem : attDeviceItems) {
                item = new LineGetAttDeviceItem();
                item.setAttDevId(attDeviceItem.getId());
                item.setAttDevName(attDeviceItem.getDevName());
                item.setAttDevSn(attDeviceItem.getDevSn());
                lineGetAttDeviceItems.add(item);
            }
        }
        return lineGetAttDeviceItems;
    }
}
