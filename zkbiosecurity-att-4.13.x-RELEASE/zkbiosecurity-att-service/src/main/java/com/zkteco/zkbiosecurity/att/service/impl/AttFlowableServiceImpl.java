package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;

import javax.transaction.Transactional;

import com.zkteco.zkbiosecurity.att.api.vo.AttApiDistanceDuration;
import com.zkteco.zkbiosecurity.att.api.vo.AttCloudSignItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.workflow.service.Workflow4OtherFlowableService;
import com.zkteco.zkbiosecurity.workflow.vo.WorkflowApplyInfoItem;
import com.zkteco.zkbiosecurity.workflow.vo.WorkflowProcessInfoItem;
import com.zkteco.zkbiosecurity.workflow.vo.WorkflowTaskInfoItem;

/**
 * 异常申请、查看（员工自助）
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 15:54
 * @since 1.0.0
 */
@Service
public class AttFlowableServiceImpl implements AttFlowableService {

    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AttSignService attSignService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;
    @Autowired
    private AttLeaveService attLeaveService;
    @Autowired
    private AttOvertimeService attOvertimeService;
    @Autowired(required = false)
    private Workflow4OtherFlowableService workflowService;
    @Autowired
    private AttWorkflowService attWorkflowService;
    @Autowired
    private AttPersonService attPersonService;

    @Override
    public Pager findMyApplyList(String sessionId, AttProcessInfoItem condition, int pageNo, int pageSize) {
        Pager pager = new Pager();
        pager.setData(new ArrayList<String>());
        if (Objects.isNull(workflowService)) {
            return pager;
        }
        WorkflowProcessInfoItem workflowProcessInfoItem =
            ModelUtil.copyProperties(condition, new WorkflowProcessInfoItem());

        // WorkflowProcessInfoItem没有pin号概念,如果过滤条件有pin,那么把pin号转换为申请人id,加入查询 add by bob.liu 20191010
        if (condition.getPersonPin() != null) {
            PersPersonItem persPersonItem = persPersonService.getItemByPin(condition.getPersonPin());
            if (Objects.nonNull(persPersonItem)) {
                workflowProcessInfoItem.setApplyUserId(persPersonItem.getId());
            } else {
                // 人员不存在返回空
                return pager;
            }
        }

        // 状态类型不一致,无法copy，手动set add by bob.liu 20190802
        if (condition.getFlowStatus() != null) {
            workflowProcessInfoItem.setFlowStatus(Integer.valueOf(condition.getFlowStatus()));
        }

        // 权限控制,只查看自己的申请 add by bob.liu 20190731
        String personPin = attPersonService.getPinByLoginType(sessionId);
        if (personPin != null) {
            workflowProcessInfoItem.setApplyUserId(persPersonService.getItemByPin(personPin).getId());
        }
        if (StringUtils.isBlank(condition.getFlowType())) {
            // 不按流程类型查询时，设置返回所有考勤流程类型 modify by lambert.li 20191212
            workflowProcessInfoItem.setFlowTypeList(AttConstant.ATT_FLOW_TYPE_LIST);
        }
        pager = workflowService.findProcessInstance(workflowProcessInfoItem, pageNo, pageSize);
        List<WorkflowProcessInfoItem> data = (List<WorkflowProcessInfoItem>)pager.getData();
        if (!CollectionUtil.isEmpty(data)) {
            String personIds = CollectionUtil.getPropertys(data, WorkflowProcessInfoItem::getApplyUserId);
            List<PersPersonItem> personItems = persPersonService.getItemsByIds(personIds);
            Map<String, PersPersonItem> persPersonItemMap =
                CollectionUtil.listToKeyMap(personItems, PersPersonItem::getId);
            List<AttProcessInfoItem> dataList = new ArrayList<>();
            data.forEach(item -> {
                AttProcessInfoItem attProcessInfoItem = new AttProcessInfoItem();
                attProcessInfoItem.setId(item.getBusinessKey());
                attProcessInfoItem.setBusinessKey(item.getBusinessKey());
                attProcessInfoItem.setFlowType(item.getFlowType());
                attProcessInfoItem.setFlowStatus(String.valueOf(item.getFlowStatus()));
                attProcessInfoItem.setPersonId(item.getApplyUserId());
                attProcessInfoItem.setTaskStatus(getFlowStatusByFlowType(item.getFlowType(), item.getBusinessKey()));
                if (persPersonItemMap.containsKey(item.getApplyUserId())) {
                    PersPersonItem persPersonItem = persPersonItemMap.get(item.getApplyUserId());
                    attProcessInfoItem.setPersonPin(persPersonItem.getPin());
                    attProcessInfoItem.setPersonName(persPersonItem.getName());
                    attProcessInfoItem.setPersonLastName(persPersonItem.getLastName());
                    attProcessInfoItem.setDeptName(persPersonItem.getDeptName());
                    attProcessInfoItem.setApplyTime(item.getApplyTime());
                    attProcessInfoItem.setApproveTime(item.getApproveTime());
                }
                if (StringUtils.isNotBlank(item.getApprovalUserId())) {
                    PersPersonItem personItem = persPersonService.getSimpleItemById(item.getApprovalUserId());
                    if (Objects.nonNull(personItem)) {
                        attProcessInfoItem.setOperateUser(personItem.getName() + "(" + personItem.getPin() + ")");
                    }
                }
                // 存在pin 即为员工自助登录,显示撤销按钮 add by bob.liu at 20191010
                if (personPin != null) {
                    attProcessInfoItem.setCancelApplyButton(true);
                }
                dataList.add(attProcessInfoItem);
            });
            pager.setData(dataList);
        }
        return pager;
    }

    @Override
    public Pager findPendingTaskList(String sessionId, AttProcessInfoItem condition, int pageNo, int pageSize) {
        Pager pager = new Pager();
        pager.setData(new ArrayList<String>());
        if (Objects.isNull(workflowService)) {
            return pager;
        }
        WorkflowProcessInfoItem workflowProcessInfoItem =
            ModelUtil.copyProperties(condition, new WorkflowProcessInfoItem());

        if (condition.getPersonPin() != null) {
            workflowProcessInfoItem.setPin(condition.getPersonPin());
        }

        // 权限控制,只查看自己的申请 add by bob.liu 20190731
        String personPin = attPersonService.getPinByLoginType(sessionId);
        if (personPin != null) {
            workflowProcessInfoItem.setApprovalUserId(persPersonService.getItemByPin(personPin).getId());
        }
        if (StringUtils.isBlank(condition.getFlowType())) {
            // 不按流程类型查询时，设置返回所有考勤流程类型 modify by lambert.li 20191212
            workflowProcessInfoItem.setFlowTypeList(AttConstant.ATT_FLOW_TYPE_LIST);
        }
        pager = workflowService.findRunTimeTaskInstance(workflowProcessInfoItem, pageNo, pageSize);
        List<WorkflowProcessInfoItem> data = (List<WorkflowProcessInfoItem>)pager.getData();
        if (!CollectionUtil.isEmpty(data)) {
            String personIds = CollectionUtil.getPropertys(data, WorkflowProcessInfoItem::getApplyUserId);
            List<PersPersonItem> personItems = persPersonService.getItemsByIds(personIds);
            Map<String, PersPersonItem> persPersonItemMap =
                CollectionUtil.listToKeyMap(personItems, PersPersonItem::getId);
            List<AttProcessInfoItem> dataList = new ArrayList<>();
            data.forEach(item -> {
                AttProcessInfoItem attProcessInfoItem = new AttProcessInfoItem();
                attProcessInfoItem.setId(item.getTaskId());
                attProcessInfoItem.setTaskId(item.getTaskId());
                attProcessInfoItem.setBusinessKey(item.getBusinessKey());
                attProcessInfoItem.setFlowType(item.getFlowType());
                attProcessInfoItem.setFlowStatus(String.valueOf(item.getFlowStatus()));
                attProcessInfoItem.setTaskStatus(item.getTaskStatus());
                attProcessInfoItem.setPersonId(item.getApplyUserId());
                if (persPersonItemMap.containsKey(item.getApplyUserId())) {
                    PersPersonItem persPersonItem = persPersonItemMap.get(item.getApplyUserId());
                    attProcessInfoItem.setPersonPin(persPersonItem.getPin());
                    attProcessInfoItem.setPersonName(persPersonItem.getName());
                    attProcessInfoItem.setPersonLastName(persPersonItem.getLastName());
                    attProcessInfoItem.setDeptName(persPersonItem.getDeptName());
                    attProcessInfoItem.setApplyTime(item.getApplyTime());
                    attProcessInfoItem.setApproveTime(item.getApproveTime());
                }
                if (StringUtils.isNotBlank(item.getApprovalUserId())) {
                    PersPersonItem personItem = persPersonService.getSimpleItemById(item.getApprovalUserId());
                    if (Objects.nonNull(personItem)) {
                        attProcessInfoItem.setOperateUser(personItem.getName() + "(" + personItem.getPin() + ")");
                    }
                }
                // 存在pin 即为员工自助登录,显示审核按钮 add by bob.liu at 20191010
                if (personPin != null) {
                    attProcessInfoItem.setHandleButton(true);
                }
                dataList.add(attProcessInfoItem);
            });
            pager.setData(dataList);
        }
        return pager;
    }

    @Override
    public Pager findApprovedTaskList(String sessionId, AttProcessInfoItem condition, int pageNo, int pageSize) {
        Pager pager = new Pager();
        pager.setData(new ArrayList<String>());
        if (Objects.isNull(workflowService)) {
            return pager;
        }
        WorkflowProcessInfoItem workflowProcessInfoItem =
            ModelUtil.copyProperties(condition, new WorkflowProcessInfoItem());

        if (condition.getPersonPin() != null) {
            workflowProcessInfoItem.setPin(condition.getPersonPin());
        }

        // 权限控制,只查看自己的申请 add by bob.liu 20190731
        String personPin = attPersonService.getPinByLoginType(sessionId);
        if (personPin != null) {
            workflowProcessInfoItem.setFlowStatus(2);
            workflowProcessInfoItem.setApprovalUserId(persPersonService.getItemByPin(personPin).getId());
        }
        if (StringUtils.isBlank(condition.getFlowType())) {
            // 不按流程类型查询时，设置返回所有考勤流程类型 modify by lambert.li 20191212
            workflowProcessInfoItem.setFlowTypeList(AttConstant.ATT_FLOW_TYPE_LIST);
        }
        pager = workflowService.finHistoryTaskInstance(workflowProcessInfoItem, pageNo, pageSize);
        List<WorkflowProcessInfoItem> data = (List<WorkflowProcessInfoItem>)pager.getData();
        if (!CollectionUtil.isEmpty(data)) {
            String personIds = CollectionUtil.getPropertys(data, WorkflowProcessInfoItem::getApplyUserId);
            List<PersPersonItem> personItems = persPersonService.getItemsByIds(personIds);
            Map<String, PersPersonItem> persPersonItemMap =
                CollectionUtil.listToKeyMap(personItems, PersPersonItem::getId);
            List<AttProcessInfoItem> dataList = new ArrayList<>();
            data.forEach(item -> {
                AttProcessInfoItem attProcessInfoItem = new AttProcessInfoItem();
                attProcessInfoItem.setId(item.getTaskId());
                attProcessInfoItem.setId(item.getBusinessKey());
                attProcessInfoItem.setBusinessKey(item.getBusinessKey());
                attProcessInfoItem.setFlowType(item.getFlowType());
                attProcessInfoItem.setFlowStatus(String.valueOf(item.getFlowStatus()));
                attProcessInfoItem.setTaskStatus(getFlowStatusByFlowType(item.getFlowType(), item.getBusinessKey()));
                attProcessInfoItem.setPersonId(item.getApplyUserId());
                if (persPersonItemMap.containsKey(item.getApplyUserId())) {
                    PersPersonItem persPersonItem = persPersonItemMap.get(item.getApplyUserId());
                    attProcessInfoItem.setPersonPin(persPersonItem.getPin());
                    attProcessInfoItem.setPersonName(persPersonItem.getName());
                    attProcessInfoItem.setPersonLastName(persPersonItem.getLastName());
                    attProcessInfoItem.setDeptName(persPersonItem.getDeptName());
                    attProcessInfoItem.setApplyTime(item.getApplyTime());
                    attProcessInfoItem.setApproveTime(item.getApproveTime());
                }
                PersPersonItem approvalUser = persPersonService.getSimpleItemById(item.getApprovalUserId());
                if (Objects.nonNull(approvalUser)) {
                    attProcessInfoItem.setOperateUser(approvalUser.getName() + "(" + approvalUser.getPin() + ")");
                }
                dataList.add(attProcessInfoItem);
            });
            pager.setData(dataList);
        }
        return pager;
    }

    @Override
    @Transactional
    public void startLeave(AttLeaveItem item) {
        PersPersonItem persPersonItem = persPersonService.getItemByPin(item.getPersonPin());
        AttLeaveItem attLeaveItem = attLeaveService.saveItem(persPersonItem.getId(), item, null);
        WorkflowApplyInfoItem workflowApplyInfoItem = new WorkflowApplyInfoItem();
        workflowApplyInfoItem.setApplyUserId(persPersonItem.getId());
        workflowApplyInfoItem.setPin(persPersonItem.getPin());
        workflowApplyInfoItem.setApplyName(persPersonItem.getName());
        // 出差/外出还是拆出来走各自的流程 by ljf 2020/8/7
        AttLeaveTypeItem attLeaveTypeItem = attLeaveTypeService.getItemById(item.getLeaveTypeId());
        String leaveTypeNo = attLeaveTypeItem.getLeaveTypeNo();
        switch (leaveTypeNo) {
            case AttCalculationConstant.AttAttendStatus.TRIP:
                workflowApplyInfoItem.setFlowType(AttCalculationConstant.AttAttendStatus.TRIP);
                break;
            case AttCalculationConstant.AttAttendStatus.OUT:
                workflowApplyInfoItem.setFlowType(AttCalculationConstant.AttAttendStatus.OUT);
                break;
            default:
                workflowApplyInfoItem.setFlowType(AttCalculationConstant.AttAttendStatus.LEAVE);
                workflowApplyInfoItem.setLeaveLong(attLeaveItem.getLeaveLongHour());
                break;
        }
        workflowApplyInfoItem.setFlowTypeName(attLeaveTypeItem.getLeaveTypeName());
        workflowApplyInfoItem.setApplyStartDatetime(AttDateUtils.dateToStrAsLong(attLeaveItem.getStartDatetime()));
        workflowApplyInfoItem.setApplyEndDatetime(AttDateUtils.dateToStrAsLong(attLeaveItem.getEndDatetime()));
        workflowApplyInfoItem.setFormRecordId(attLeaveItem.getId());
        workflowApplyInfoItem.setNotifierUserIds(item.getNotifierPerIds());
        workflowService.start(workflowApplyInfoItem);
    }

    @Override
    @Transactional
    public void startOvertime(AttOvertimeItem item) {
        String personPin = item.getPersonPin();
        PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
        String personId = persPersonItem.getId();
        item.setPersonId(personId);
        int timeLong = getDateDiffMinutes(item.getStartDatetime(), item.getEndDatetime());
        item.setOvertimeLong(timeLong);
        item.setFlowStatus(AttConstant.FLOW_STATUS_CREATE);

        AttOvertimeItem attOvertimeItem = attOvertimeService.saveItem(item);

        WorkflowApplyInfoItem workflowApplyInfoItem = new WorkflowApplyInfoItem();
        workflowApplyInfoItem.setApplyUserId(personId);
        workflowApplyInfoItem.setPin(personPin);
        workflowApplyInfoItem.setApplyName(persPersonItem.getName());
        workflowApplyInfoItem.setFlowType(AttCalculationConstant.AttAttendStatus.OVERTIME);
        workflowApplyInfoItem.setFlowTypeName(AttCommonUtils.overtimeSignName(item.getOvertimeSign()));
        workflowApplyInfoItem.setApplyStartDatetime(AttDateUtils.dateToStrAsLong(item.getStartDatetime()));
        workflowApplyInfoItem.setApplyEndDatetime(AttDateUtils.dateToStrAsLong(item.getEndDatetime()));
        workflowApplyInfoItem.setFormRecordId(attOvertimeItem.getId());
        workflowApplyInfoItem.setNotifierUserIds(item.getNotifierPerIds());
        workflowService.start(workflowApplyInfoItem);
    }

    @Override
    @Transactional
    public void startOut(AttOutItem item) {
        PersPersonItem persPersonItem = persPersonService.getItemByPin(item.getPersonPin());
        AttLeaveItem attLeaveItem = ModelUtil.copyProperties(item, new AttLeaveItem());
        attLeaveItem = attLeaveService.saveItem(persPersonItem.getId(), attLeaveItem, null);
        WorkflowApplyInfoItem workflowApplyInfoItem = new WorkflowApplyInfoItem();
        workflowApplyInfoItem.setApplyUserId(persPersonItem.getId());
        workflowApplyInfoItem.setPin(item.getPersonPin());
        workflowApplyInfoItem.setApplyName(persPersonItem.getName());
        workflowApplyInfoItem.setFlowType(AttCalculationConstant.AttAttendStatus.OUT);
        workflowApplyInfoItem.setFlowTypeName(I18nUtil.i18nCode("wf_leftMenu_out"));
        workflowApplyInfoItem.setApplyStartDatetime(AttDateUtils.dateToStrAsLong(item.getStartDatetime()));
        workflowApplyInfoItem.setApplyEndDatetime(AttDateUtils.dateToStrAsLong(item.getEndDatetime()));
        workflowApplyInfoItem.setFormRecordId(attLeaveItem.getId());
        workflowApplyInfoItem.setNotifierUserIds(item.getNotifierPerIds());
        workflowService.start(workflowApplyInfoItem);
    }

    @Override
    @Transactional
    public void startTrip(AttTripItem item) {
        PersPersonItem persPersonItem = persPersonService.getItemByPin(item.getPersonPin());
        AttLeaveItem attLeaveItem = ModelUtil.copyProperties(item, new AttLeaveItem());
        attLeaveItem = attLeaveService.saveItem(persPersonItem.getId(), attLeaveItem, null);
        WorkflowApplyInfoItem workflowApplyInfoItem = new WorkflowApplyInfoItem();
        workflowApplyInfoItem.setApplyUserId(persPersonItem.getId());
        workflowApplyInfoItem.setPin(persPersonItem.getPin());
        workflowApplyInfoItem.setApplyName(persPersonItem.getName());
        workflowApplyInfoItem.setFlowType(AttCalculationConstant.AttAttendStatus.TRIP);
        workflowApplyInfoItem.setFlowTypeName(I18nUtil.i18nCode("wf_leftMenu_trip"));
        workflowApplyInfoItem.setApplyStartDatetime(AttDateUtils.dateToStrAsLong(item.getStartDatetime()));
        workflowApplyInfoItem.setApplyEndDatetime(AttDateUtils.dateToStrAsLong(item.getEndDatetime()));
        workflowApplyInfoItem.setFormRecordId(attLeaveItem.getId());
        workflowApplyInfoItem.setNotifierUserIds(item.getNotifierPerIds());
        workflowService.start(workflowApplyInfoItem);
    }

    @Override
    @Transactional
    public void startSign(AttSignItem item) {
        String personPin = item.getPersonPin();
        PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
        List<AttCloudSignItem> attSignItemList = new ArrayList<AttCloudSignItem>();
        AttCloudSignItem attCloudSignItem = new AttCloudSignItem();
        // 组装数据
        attCloudSignItem.setPersonPin(persPersonItem.getPin());
        attCloudSignItem.setPersonName(persPersonItem.getName());
        attCloudSignItem.setPersonLastName(persPersonItem.getLastName());
        attCloudSignItem.setDeptCode(persPersonItem.getDeptCode());
        attCloudSignItem.setDeptName(persPersonItem.getDeptName());
        attCloudSignItem.setSignDate(AttDateUtils.dateToStrAsShort(item.getSignDatetime()));
        attCloudSignItem.setSignDatetime(item.getAfterSignRecord());
        attCloudSignItem.setRemark(item.getRemark());
        attCloudSignItem.setAttState(item.getAttState());
        String pins = buildPinsAndIds(item.getNotifierPerIds());
        attCloudSignItem.setNotifierPins(pins);
        attSignItemList.add(attCloudSignItem);
        attWorkflowService.saveSignItem(attSignItemList);
    }

    @Override
    public void startClass(AttClassItem item) {
        // @TODO 二号项目未增加该流程申请
    }

    @Override
    public void startAdjust(AttAdjustItem item) {
        // @TODO 二号项目未增加该流程申请
    }

    /**
     * 获取两个时间的相差分钟数
     * 
     * @param date1
     *            开始时间
     * @param date2
     *            结束时间
     * @return
     */
    private int getDateDiffMinutes(Date date1, Date date2) {
        long minutes = 0;
        long diff = date2.getTime() - date1.getTime();
        if (diff > 0) {
            minutes = diff / (1000 * 60);
        }
        return (int)minutes;
    }

    @Override
    @Transactional
    public Map<String, String> apply(String personId, String flowType) {
        WorkflowApplyInfoItem workflowApplyInfoItem = new WorkflowApplyInfoItem();
        workflowApplyInfoItem.setApplyUserId(personId);
        workflowApplyInfoItem.setFlowType(flowType);
        Map<String, String> map = new HashMap<>();
        String personIds = workflowService.getFirstNotifier(workflowApplyInfoItem);
        if (StringUtils.isNotBlank(personIds)) {
            List<PersPersonItem> personItems = persPersonService.getItemsByIds(personIds);
            if (Objects.nonNull(personItems)) {
                for (PersPersonItem personItem : personItems) {
                    map.put(personItem.getId(), personItem.getName());
                }
            }
        }
        return map;
    }

    @Override
    @Transactional
    public Map approve(String taskId) {
        WorkflowProcessInfoItem workflowProcessInfoItem = workflowService.findHistoricTaskByTaskId(taskId);
        if (Objects.nonNull(workflowProcessInfoItem)) {
            Map<String, Object> map = new HashMap<>(16);
            map.put("item",
                getItemByFlowType(workflowProcessInfoItem.getFlowType(), workflowProcessInfoItem.getBusinessKey()));
            Map<String, String> NotifierMap = new HashMap<>();
            if (!StringUtils.isBlank(workflowProcessInfoItem.getNotifierUserIds())) {
                List<PersPersonItem> personItems =
                    persPersonService.getItemsByIds(workflowProcessInfoItem.getNotifierUserIds());
                if (Objects.nonNull(personItems)) {
                    for (PersPersonItem personItem : personItems) {
                        NotifierMap.put(personItem.getId(), personItem.getName());
                    }
                }
            }
            taskInfoItemList(workflowProcessInfoItem);
            workflowProcessInfoItem.setApprove(true);
            workflowProcessInfoItem.setNotifierPers(NotifierMap);
            map.put("task", workflowProcessInfoItem);
            map.put("flowType", workflowProcessInfoItem.getFlowType());
            return map;
        }
        return null;
    }

    @Override
    public Map detail(String businessKey) {
        WorkflowProcessInfoItem workflowProcessInfoItem = workflowService.findProcessByBusinessKey(businessKey);
        if (Objects.nonNull(workflowProcessInfoItem)) {
            Map<String, Object> map = new HashMap<>(16);
            map.put("item",
                getItemByFlowType(workflowProcessInfoItem.getFlowType(), workflowProcessInfoItem.getBusinessKey()));
            Map<String, String> NotifierMap = new HashMap<>();
            if (!StringUtils.isBlank(workflowProcessInfoItem.getNotifierUserIds())) {
                List<PersPersonItem> personItems =
                    persPersonService.getItemsByIds(workflowProcessInfoItem.getNotifierUserIds());
                if (Objects.nonNull(personItems)) {
                    for (PersPersonItem personItem : personItems) {
                        NotifierMap.put(personItem.getId(), personItem.getName());
                    }
                }
            }
            taskInfoItemList(workflowProcessInfoItem);
            workflowProcessInfoItem.setNotifierPers(NotifierMap);
            map.put("task", workflowProcessInfoItem);
            map.put("flowType", workflowProcessInfoItem.getFlowType());
            return map;
        }
        return null;
    }

    private BaseItem getItemByFlowType(String flowType, String businessKey) {
        if ("sign".equals(flowType)) {
            return attSignService.getByBusinessKey(businessKey);
        } else if ("leave".equals(flowType)) {
            return attLeaveService.getByBusinessKey(businessKey);
        } else if ("overtime".equals(flowType)) {
            return attOvertimeService.getByBusinessKey(businessKey);
        } else if ("trip".equals(flowType)) {
            return attLeaveService.getByBusinessKey(businessKey);
        } else if ("out".equals(flowType)) {
            return attLeaveService.getByBusinessKey(businessKey);
        }
        return null;
    }

    /**
     * 用于前端展示审批记录
     * 
     * <AUTHOR>
     * @since 2019年8月14日 下午7:03:52
     * @param workflowProcessInfoItem
     * @return
     */
    public WorkflowProcessInfoItem taskInfoItemList(WorkflowProcessInfoItem workflowProcessInfoItem) {
        List<WorkflowTaskInfoItem> detailWorkflowTaskInfoItemList = new ArrayList<WorkflowTaskInfoItem>();
        List<WorkflowTaskInfoItem> taskInfoItemList = workflowProcessInfoItem.getTaskInfoItemList();
        for (WorkflowTaskInfoItem workflowTaskInfoItem : taskInfoItemList) {
            if (!StringUtils.isEmpty(workflowTaskInfoItem.getTaskStatus())) {
                detailWorkflowTaskInfoItemList.add(workflowTaskInfoItem);
            }
        }
        if (detailWorkflowTaskInfoItemList.size() <= 0) {
            workflowProcessInfoItem.setDetail(false);
        } else if (detailWorkflowTaskInfoItemList.size() > 0) {
            workflowProcessInfoItem.setDetail(true);
        }
        workflowProcessInfoItem.setTaskInfoItemList(detailWorkflowTaskInfoItemList);
        return workflowProcessInfoItem;
    }

    /**
     * 将ids转为pins
     * 
     * <AUTHOR>
     * @since 2019年6月25日 下午3:35:33
     * @param ids
     * @return
     */
    private String buildPinsAndIds(String ids) {
        String pins = "";
        if (StringUtils.isNotBlank(ids)) {
            PersPersonItem persPerson = new PersPersonItem();
            persPerson.setInId(ids);
            List<PersPersonItem> persPersonItemList = persPersonService.getByCondition(persPerson);
            if (!CollectionUtil.isEmpty(persPersonItemList)) {
                pins = CollectionUtil.getPropertys(persPersonItemList, PersPersonItem::getPin);
            }
        }
        return pins;
    }

    /**
     * 根据异常类型和businessKey查找申请单的状态
     * 
     * @param type
     * @param businessKey
     * @return
     */
    private String getFlowStatusByFlowType(String type, String businessKey) {
        if (StringUtils.isBlank(type) || StringUtils.isBlank(businessKey)) {
            return "";
        }
        String flowStatus = "";
        switch (type) {
            case AttConstant.FLOW_TYPE_SIGN:
                AttSignItem attSignItem = attSignService.getByBusinessKey(businessKey);
                if (Objects.nonNull(attSignItem)) {
                    flowStatus = attSignItem.getFlowStatus();
                }
                break;
            case AttConstant.FLOW_TYPE_LEAVE:
                AttLeaveItem attLeaveItem = attLeaveService.getByBusinessKey(businessKey);
                if (Objects.nonNull(attLeaveItem)) {
                    flowStatus = attLeaveItem.getFlowStatus();
                }
                break;
            case AttConstant.FLOW_TYPE_OVERTIME:
                AttOvertimeItem attOvertimeItem = attOvertimeService.getByBusinessKey(businessKey);
                if (Objects.nonNull(attOvertimeItem)) {
                    flowStatus = attOvertimeItem.getFlowStatus();
                }
                break;
            case AttConstant.FLOW_TYPE_OUT:
                AttLeaveItem attOutItem = attLeaveService.getByBusinessKey(businessKey);
                if (Objects.nonNull(attOutItem)) {
                    flowStatus = attOutItem.getFlowStatus();
                }
                break;
            case AttConstant.FLOW_TYPE_TRIP:
                AttLeaveItem attTripItem = attLeaveService.getByBusinessKey(businessKey);
                if (Objects.nonNull(attTripItem)) {
                    flowStatus = attTripItem.getFlowStatus();
                }
                break;
            default:
                //
        }
        return flowStatus;
    }
}
