package com.zkteco.zkbiosecurity.att.calculation;

import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.bean.AttRuleParamBean;
import com.zkteco.zkbiosecurity.att.calc.bo.AttLeaveBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttTimeSlotBO;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.constants.AttShiftConstant;
import com.zkteco.zkbiosecurity.att.enums.AttRuleEnum;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;

/**
 * 根据人员排班及实际卡点进行智能找班
 *
 * <AUTHOR>
 * @date 2020-06-12 17:13
 * @sine 1.0.0
 */
@Component
public class AttIntelligentPersonSch {

    @Autowired
    private AttNormalAttendanceCalculate attNormalAttendanceCalculate;

    /**
     * 填充卡点，并返回实际排班对象
     *
     * @param attPersonSchBOList 当天排班集合
     * @param transactionDataMap 打卡数据
     * @return com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO
     * @date 2020/6/12 17:22
     */
    public AttPersonSchBO fillPersSchCardData(String pinAndDate, List<AttPersonSchBO> attPersonSchBOList,
        Map<String, List<String>> transactionDataMap, AttRuleParamBean attRuleParamBean,
        Map<String, AttTimeSlotItem> attTimeSlotItemMap, List<AttLeaveBO> attLeaveBOList) {

        if (attPersonSchBOList.size() == 1) {
            // 普通排班
            AttPersonSchBO attPersonSchBO = attPersonSchBOList.get(0);
            String attShiftId = attPersonSchBO.getAttShiftId();
            // 班次时间段
            List<AttTimeSlotBO> attTimeSlotBOList = attPersonSchBO.getAttTimeSlotArray();
            // 班次类型（0/规律班次，1/弹性班次）
            Short shiftType = attPersonSchBO.getShiftType();
            if (StringUtils.isBlank(attShiftId)) {
                // 没有班次ID, 即临时排班, 临时排班时间段只有规律班次
                fillRegularShiftTime(pinAndDate, attTimeSlotBOList, transactionDataMap, attRuleParamBean,
                    attTimeSlotItemMap, attLeaveBOList);
            } else {
                switch (shiftType) {
                    case AttShiftConstant.ShiftType.REGULAR_SHIFT:
                        // 规律班次 填充卡点
                        fillRegularShiftTime(pinAndDate, attTimeSlotBOList, transactionDataMap, attRuleParamBean,
                            attTimeSlotItemMap, attLeaveBOList);
                        break;
                    case AttShiftConstant.ShiftType.FLEXIBLE_SHIFT:
                        // 弹性班次 填充卡点
                        fillFlexibleShift(pinAndDate, attTimeSlotBOList, transactionDataMap, attRuleParamBean,
                            attTimeSlotItemMap);
                        break;
                    default:
                        break;
                }
            }
            return attPersonSchBO;

        } else {

            // 智能找班
            List<AttPersonSchBO> haveTimeSlotList = new ArrayList<>();
            for (AttPersonSchBO attPersonSchBO : attPersonSchBOList) {
                // 智能找班都是规律班次
                List<AttTimeSlotBO> attTimeSlotBOList = attPersonSchBO.getAttTimeSlotArray();
                // 按规律班次填充卡点
                fillRegularShiftTime(pinAndDate, attTimeSlotBOList, transactionDataMap, attRuleParamBean,
                    attTimeSlotItemMap, attLeaveBOList);
                // 存在时间段的班次
                if (!CollectionUtil.isEmpty(attTimeSlotBOList)) {
                    haveTimeSlotList.add(attPersonSchBO);
                }
            }

            // 优先选择有时间段的班次 -- lgq.20220117
            if (CollectionUtil.isEmpty(haveTimeSlotList)) {
                return attPersonSchBOList.get(0); // 如果都没有时间段，即都是排班且休息，返回随机班次
            } else if (haveTimeSlotList.size() == 1) {
                return haveTimeSlotList.get(0); // 如果只存在一个有时间段的班次，就默认选择这个班次
            }

            // 根据智能排班原则得到最优班次
            AttPersonSchBO attPersonSchBO;
            if (AttRuleEnum.SmartFindClass.getValueOne().equals(attRuleParamBean.getSmartFindClass())) {
                // 时长最长
                attPersonSchBO = longestTime(haveTimeSlotList, attRuleParamBean, attTimeSlotItemMap);
            } else {
                // 异常最少
                attPersonSchBO = leastException(haveTimeSlotList, attRuleParamBean, attTimeSlotItemMap);
            }
            return attPersonSchBO;
        }

    }

    /**
     * 规律班次时间段填充卡点
     * 
     * @param pinAndDate
     * @param attTimeSlotBOList
     * @param transactionDataMap
     * @param attRuleParamBean
     */
    private void fillRegularShiftTime(String pinAndDate, List<AttTimeSlotBO> attTimeSlotBOList,
        Map<String, List<String>> transactionDataMap, AttRuleParamBean attRuleParamBean,
        Map<String, AttTimeSlotItem> attTimeSlotItemMap, List<AttLeaveBO> attLeaveBOList) {

        if (CollectionUtil.isEmpty(attTimeSlotBOList)) {
            return;
        }

        // 循环时段填充卡点
        for (AttTimeSlotBO attTimeSlotBO : attTimeSlotBOList) {

            String[] pinAndDateSplit = pinAndDate.split(AttCalculationConstant.KEY_CONNECTOR);
            String pin = pinAndDateSplit[0];

            String attTimeSlotId = attTimeSlotBO.getAttTimeSlotId();
            AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotId);

            // 时段的上班时间和[开始签到时间,结束签到时间]
            String timeSlotToWorkTime =
                String.format("%s %s:00", attTimeSlotBO.getFirstDay(), attTimeSlotItem.getToWorkTime());
            String startSignInTime =
                AttDateUtils.addMinute(timeSlotToWorkTime, -attTimeSlotItem.getBeforeToWorkMinutes());
            String endSignInTime = AttDateUtils.addMinute(timeSlotToWorkTime, attTimeSlotItem.getAfterToWorkMinutes());

            // 时段的下班时间和[开始签退时间,结束签退时间]
            String timeSlotOffWorkTime =
                String.format("%s %s:00", attTimeSlotBO.getSecondDay(), attTimeSlotItem.getOffWorkTime());
            String startSignOffTime =
                AttDateUtils.addMinute(timeSlotOffWorkTime, -attTimeSlotItem.getBeforeOffWorkMinutes());
            String endSignOffTime =
                AttDateUtils.addMinute(timeSlotOffWorkTime, attTimeSlotItem.getAfterOffWorkMinutes());

            // 打卡记录
            List<String> attTransactionList = new ArrayList<>();
            List<String> dateList = AttDateUtils.getBetweenDate(startSignInTime, endSignOffTime);
            for (String date : dateList) {
                List<String> trans = transactionDataMap.get(pin + AttCalculationConstant.KEY_CONNECTOR + date);
                if (!CollectionUtil.isEmpty(trans)) {
                    attTransactionList.addAll(trans);
                }
            }

            // 设置上班打卡时间
            String toWorkTime = getRegularShiftToWorkTime(timeSlotToWorkTime, startSignInTime, endSignInTime,
                attTransactionList, attRuleParamBean);
            attTimeSlotBO.setToWorkTime(toWorkTime);

            // 有上班卡点，且弹性上班时，计算提前或延后上班时长（迟到/早退环节用到）
            if (!attRuleParamBean.getNoSignIn().equals(toWorkTime)
                && ConstUtil.SYSTEM_COMMON_TRUE.equals(attTimeSlotItem.getEnableFlexibleWork())) {

                timeSlotOffWorkTime = getFlexibleTime(attTimeSlotItem, attLeaveBOList, toWorkTime, attTimeSlotBO);
            }

            // 设置下班时间
            String offWorkTime = getRegularShiftOffWorkTime(timeSlotOffWorkTime, startSignOffTime, endSignOffTime,
                attTransactionList, attRuleParamBean);
            attTimeSlotBO.setOffWorkTime(offWorkTime);
        }
    }

    /**
     * 获取规律班次打卡时间
     * 
     * @param timeSlotToWorkTime
     * @param startSignInTime
     * @param endSignInTime
     * @param attTransactionList
     * @param attRuleParamBean
     * @return
     */
    private String getRegularShiftToWorkTime(String timeSlotToWorkTime, String startSignInTime, String endSignInTime,
        List<String> attTransactionList, AttRuleParamBean attRuleParamBean) {

        // 没有打卡记录直接返回未签到
        if (CollectionUtil.isEmpty(attTransactionList)) {
            return attRuleParamBean.getNoSignIn();
        }

        // 过滤记录获取上班打卡
        List<String> signInList = new ArrayList<>();
        for (String transaction : attTransactionList) {
            if (startSignInTime.compareTo(transaction) <= 0 && endSignInTime.compareTo(transaction) >= 0) {
                signInList.add(transaction);
            }
        }

        if (CollectionUtil.isEmpty(signInList)) {
            // 有效取卡范围内没有打卡记录 记为未签到
            return attRuleParamBean.getNoSignIn();
        }

        // 打卡记录排序下
        Collections.sort(signInList);
        String toWorkTime;
        if (AttRuleEnum.SignIn.getValueOne().equals(attRuleParamBean.getSignIn())) {
            // 最早原则
            toWorkTime = signInList.get(0);
        } else {
            // 就近原则
            int overCount = 0;
            for (String time : signInList) {
                if (time.compareTo(timeSlotToWorkTime) <= 0) {
                    overCount++;
                }
            }
            if (overCount > 0) {
                toWorkTime = signInList.get(overCount - 1);
            } else {
                toWorkTime = signInList.get(0);
            }
        }
        return toWorkTime;

    }

    /**
     * 【规律班次时间段填充卡点】->有上班卡点且启用弹性上班时，计算提前或延后时长，相应的下班时间也要提前或延后
     * 
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/9/7 15:04
     * @param attTimeSlotItem
     * @param attLeaveBOList
     * @param toWorkTime
     * @param attTimeSlotBO
     * @return java.lang.String 提前或延后的下班时间
     */
    private String getFlexibleTime(AttTimeSlotItem attTimeSlotItem, List<AttLeaveBO> attLeaveBOList, String toWorkTime,
        AttTimeSlotBO attTimeSlotBO) {
        // 时段的上班时间和下班时间
        String timeSlotToWorkTime =
            String.format("%s %s:00", attTimeSlotBO.getFirstDay(), attTimeSlotItem.getToWorkTime());
        String timeSlotOffWorkTime =
            String.format("%s %s:00", attTimeSlotBO.getSecondDay(), attTimeSlotItem.getOffWorkTime());
        // 弹性可提前和延后时间
        String advanceWorkTime = AttDateUtils.addMinute(timeSlotToWorkTime, -attTimeSlotItem.getAdvanceWorkMinutes());
        String delayedWorkTime = AttDateUtils.addMinute(timeSlotToWorkTime, attTimeSlotItem.getDelayedWorkMinutes());

        List<AttLeaveBO> exceptionList = new ArrayList<>();
        if (!CollectionUtil.isEmpty(attLeaveBOList)) {
            for (AttLeaveBO attLeaveBO : attLeaveBOList) {
                if ((StringUtils.compare(advanceWorkTime, attLeaveBO.getEndDateTime()) <= 0)
                    && (StringUtils.compare(delayedWorkTime, attLeaveBO.getStartDateTime()) >= 0)) {
                    exceptionList.add(attLeaveBO);
                }
            }
        }

        // 无异常：
        if (CollectionUtil.isEmpty(exceptionList)) {
            // 算提前或延后上班；
            Integer advanceMinutes = AttDateUtils.getMinuteDiff(toWorkTime, timeSlotToWorkTime);
            if (advanceMinutes > attTimeSlotItem.getAdvanceWorkMinutes()) {
                advanceMinutes = attTimeSlotItem.getAdvanceWorkMinutes();
            } else if (advanceMinutes < -attTimeSlotItem.getDelayedWorkMinutes()) {
                advanceMinutes = -attTimeSlotItem.getDelayedWorkMinutes();
            }
            attTimeSlotBO.setFlexibleTime(advanceMinutes);
        } else {
            // 有异常：

            Collections.sort(exceptionList,
                ((o1, o2) -> StringUtils.compare(o1.getStartDateTime(), o2.getStartDateTime())));
            AttLeaveBO attLeaveBO = exceptionList.get(0);
            String exceptionStartTime = attLeaveBO.getStartDateTime();
            String exceptionEndTime = attLeaveBO.getEndDateTime();

            // b2-1.异常结束 <= 时段上班时间
            if (StringUtils.compare(exceptionEndTime, timeSlotToWorkTime) <= 0) {
                // 1).卡点 < 异常开始
                if (StringUtils.compare(toWorkTime, exceptionStartTime) < 0) {

                    // a.异常开始 <= 弹性提前上班卡点，无提前上班（异常无效）；
                    if (StringUtils.compare(exceptionStartTime, advanceWorkTime) <= 0) {

                    } else if (StringUtils.compare(exceptionStartTime, advanceWorkTime) > 0) {
                        // b.异常开始 > 弹性提前上班卡点，算提前上班（异常有效）；
                        Integer advanceMinutes = AttDateUtils.getMinuteDiff(toWorkTime, timeSlotToWorkTime);
                        if (advanceMinutes > attTimeSlotItem.getAdvanceWorkMinutes()) {
                            advanceMinutes = attTimeSlotItem.getAdvanceWorkMinutes();
                        } else if (advanceMinutes < -attTimeSlotItem.getDelayedWorkMinutes()) {
                            advanceMinutes = -attTimeSlotItem.getDelayedWorkMinutes();
                        }
                        attTimeSlotBO.setFlexibleTime(advanceMinutes);
                    }
                } else if ((StringUtils.compare(exceptionStartTime, toWorkTime) <= 0)
                    && (StringUtils.compare(toWorkTime, exceptionEndTime) <= 0)) {
                    // 2).异常开始 <= 卡点 <= 异常结束

                    // 无提前上班（异常无效）；
                } else if (StringUtils.compare(toWorkTime, exceptionEndTime) > 0) {
                    // 3).卡点 > 异常结束

                    // 提前或延后上班（异常无效）；
                    Integer advanceMinutes = AttDateUtils.getMinuteDiff(toWorkTime, timeSlotToWorkTime);
                    if (advanceMinutes > attTimeSlotItem.getAdvanceWorkMinutes()) {
                        advanceMinutes = attTimeSlotItem.getAdvanceWorkMinutes();
                    } else if (advanceMinutes < -attTimeSlotItem.getDelayedWorkMinutes()) {
                        advanceMinutes = -attTimeSlotItem.getDelayedWorkMinutes();
                    }
                    attTimeSlotBO.setFlexibleTime(advanceMinutes);
                }
            } else if ((StringUtils.compare(exceptionStartTime, timeSlotToWorkTime) <= 0)
                && (StringUtils.compare(exceptionEndTime, timeSlotToWorkTime) > 0)) {
                // b2-2.异常开始 <= 时段上班时间 && 异常结束 > 时段上班时间

                // 1).卡点 <异常开始
                if (StringUtils.compare(toWorkTime, exceptionStartTime) < 0) {
                    if (StringUtils.compare(exceptionStartTime, advanceWorkTime) <= 0) {
                        // a.异常开始 <= 弹性提前上班卡点，无提前上班(异常的有效开始时间为时段上班卡点)；
                    } else if (StringUtils.compare(exceptionStartTime, advanceWorkTime) > 0) {
                        // b.异常开始 > 弹性提前上班卡点，算提前上班（异常有效）；
                        Integer advanceMinutes = AttDateUtils.getMinuteDiff(toWorkTime, timeSlotToWorkTime);
                        if (advanceMinutes > attTimeSlotItem.getAdvanceWorkMinutes()) {
                            advanceMinutes = attTimeSlotItem.getAdvanceWorkMinutes();
                        } else if (advanceMinutes < -attTimeSlotItem.getDelayedWorkMinutes()) {
                            advanceMinutes = -attTimeSlotItem.getDelayedWorkMinutes();
                        }
                        attTimeSlotBO.setFlexibleTime(advanceMinutes);
                    }
                } else if ((StringUtils.compare(exceptionStartTime, toWorkTime) <= 0)
                    && (StringUtils.compare(toWorkTime, exceptionEndTime) <= 0)) {
                    // 2).异常开始 <= 卡点 < 异常结束

                    // 无提前上班(异常的有效开始时间为时段上班卡点)；
                } else if (StringUtils.compare(toWorkTime, exceptionEndTime) >= 0) {
                    // 3).卡点 >= 异常结束

                    // 无提前上班，算班前异常申请；
                }
            } else if ((StringUtils.compare(exceptionStartTime, timeSlotToWorkTime) > 0)
                && (StringUtils.compare(exceptionEndTime, timeSlotToWorkTime) > 0)) {
                // b2-3.异常开始 > 时段上班时间 && 异常结束 > 时段上班时间

                if (StringUtils.compare(toWorkTime, exceptionStartTime) <= 0) {
                    // 1).卡点 < 异常开始

                    // 提前、延后或迟到上班
                    Integer advanceMinutes = AttDateUtils.getMinuteDiff(toWorkTime, timeSlotToWorkTime);
                    if (advanceMinutes > attTimeSlotItem.getAdvanceWorkMinutes()) {
                        advanceMinutes = attTimeSlotItem.getAdvanceWorkMinutes();
                    } else if (advanceMinutes < -attTimeSlotItem.getDelayedWorkMinutes()) {
                        advanceMinutes = -attTimeSlotItem.getDelayedWorkMinutes();
                    }
                    attTimeSlotBO.setFlexibleTime(advanceMinutes);
                } else if ((StringUtils.compare(exceptionStartTime, toWorkTime) <= 0)
                    && (StringUtils.compare(toWorkTime, exceptionEndTime) <= 0)) {
                    // 2).异常开始 <= 卡点 <= 异常结束

                    // 无提前或延后上班、算旷工=异常开始-时间上班时间【保存旷工时长】；
                } else if (StringUtils.compare(toWorkTime, exceptionEndTime) > 0) {
                    // 3).卡点 > 异常结束

                    // 无提前或延后上班，旷工(旷工=异常开始-时段上班时间)+迟到(迟到=卡点-异常结束)；
                }
            }
        }

        // 如果提前/延后上班，则下班时间对应提前延后
        if (null != attTimeSlotBO.getFlexibleTime()) {
            timeSlotOffWorkTime = AttDateUtils.addMinute(timeSlotOffWorkTime, -attTimeSlotBO.getFlexibleTime());
        }
        return timeSlotOffWorkTime;
    }

    /**
     * 获取规律班次下班时间
     * 
     * @param timeSlotOffWorkTime
     * @param startSignOffTime
     * @param endSignOffTime
     * @param attTransactionList
     * @param attRuleParamBean
     * @return
     */
    private String getRegularShiftOffWorkTime(String timeSlotOffWorkTime, String startSignOffTime,
        String endSignOffTime, List<String> attTransactionList, AttRuleParamBean attRuleParamBean) {
        // 没有打卡记录直接返回未签退
        if (CollectionUtil.isEmpty(attTransactionList)) {
            return attRuleParamBean.getNoSignOff();
        }

        // 过滤记录获取下班打卡
        List<String> signOffList = new ArrayList<>();
        for (String transaction : attTransactionList) {
            if (startSignOffTime.compareTo(transaction) <= 0 && endSignOffTime.compareTo(transaction) >= 0) {
                signOffList.add(transaction);
            }
        }

        if (CollectionUtil.isEmpty(signOffList)) {
            // 有效取卡范围内没有打卡记录 记为未签退
            return attRuleParamBean.getNoSignOff();
        }

        // 打卡记录排序下
        Collections.sort(signOffList);
        String offWorkTime;
        if (AttRuleEnum.SignOut.getValueOne().equals(attRuleParamBean.getSignOut())) {
            // 最晚原则
            offWorkTime = signOffList.get(signOffList.size() - 1);
        } else {
            // 就近原则
            int overCount = 0;
            for (String time : signOffList) {
                if (time.compareTo(timeSlotOffWorkTime) < 0) {
                    overCount++;
                }
            }
            if (overCount == signOffList.size()) {
                offWorkTime = signOffList.get(overCount - 1);
            } else {
                offWorkTime = signOffList.get(overCount);
            }
        }
        return offWorkTime;
    }

    /**
     * 弹性班次时间段填充卡点
     * 
     * @param pinAndDate
     * @param attTimeSlotBOList
     * @param transactionDataMap
     * @param attRuleParamBean
     */
    private void fillFlexibleShift(String pinAndDate, List<AttTimeSlotBO> attTimeSlotBOList,
        Map<String, List<String>> transactionDataMap, AttRuleParamBean attRuleParamBean,
        Map<String, AttTimeSlotItem> attTimeSlotItemMap) {

        // 循环时段填充卡点
        for (AttTimeSlotBO attTimeSlotBO : attTimeSlotBOList) {

            String[] pinAndDateSplit = pinAndDate.split(AttCalculationConstant.KEY_CONNECTOR);
            String pin = pinAndDateSplit[0];

            AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());

            String startSignInTime =
                String.format("%s %s:00", attTimeSlotBO.getFirstDay(), attTimeSlotItem.getStartSignInTime());
            String endSignOffTime =
                String.format("%s %s:00", attTimeSlotBO.getSecondDay(), attTimeSlotItem.getEndSignOffTime());

            // 打卡记录
            List<String> attTransactionList = new ArrayList<>();
            List<String> dateList = AttDateUtils.getBetweenDate(startSignInTime, endSignOffTime);
            for (String date : dateList) {
                List<String> trans = transactionDataMap.get(pin + AttCalculationConstant.KEY_CONNECTOR + date);
                if (!CollectionUtil.isEmpty(trans)) {
                    attTransactionList.addAll(trans);
                }
            }

            // 过滤打卡时间
            List<String> signList = new ArrayList<>();
            for (String attTransaction : attTransactionList) {
                if (startSignInTime.compareTo(attTransaction) <= 0 && endSignOffTime.compareTo(attTransaction) >= 0) {
                    signList.add(attTransaction);
                }
            }

            Collections.sort(signList);

            // 弹性时长计算方式 (0:两两打卡累积时长 1:首尾打卡计算时长)
            String elasticCal = attTimeSlotItem.getElasticCal();
            // 弹性时长计算方式
            if (AttRuleEnum.ElasticCal.getValueOne().equals(elasticCal)) {
                // 两两打卡累积时长
                attTimeSlotBO.setElasticWorkTimeArray(signList);
            } else {
                // 首尾打卡计算时长
                if (signList.isEmpty()) {
                    attTimeSlotBO.setToWorkTime(attRuleParamBean.getNoSignIn());
                    attTimeSlotBO.setOffWorkTime(attRuleParamBean.getNoSignOff());
                } else if (signList.size() == 1) {
                    // 只打一次卡，根据卡点距离首尾卡点的长短，来判断是未签到还是未签退；
                    String signTime = signList.get(0);
                    Date startTime = DateUtil.stringToDate(startSignInTime);
                    Date endTime = DateUtil.stringToDate(endSignOffTime);
                    Date attTime = DateUtil.stringToDate(signTime);
                    if ((attTime.getTime() - startTime.getTime()) > (endTime.getTime() - attTime.getTime())) {
                        attTimeSlotBO.setToWorkTime(attRuleParamBean.getNoSignIn());
                        attTimeSlotBO.setOffWorkTime(signTime);
                    } else {
                        attTimeSlotBO.setToWorkTime(signTime);
                        attTimeSlotBO.setOffWorkTime(attRuleParamBean.getNoSignOff());
                    }
                } else {
                    attTimeSlotBO.setToWorkTime(signList.get(0));
                    attTimeSlotBO.setOffWorkTime(signList.get(signList.size() - 1));
                }
            }

        }
    }

    /**
     * 获取时长最长的班次
     *
     * @param attPersonSchBOList
     * @return
     */
    private AttPersonSchBO longestTime(List<AttPersonSchBO> attPersonSchBOList, AttRuleParamBean attRuleParamBean,
        Map<String, AttTimeSlotItem> attTimeSlotItemMap) {

        Long longestTime = null;
        // 考勤方式
        Short attMode = null;
        AttPersonSchBO longestTimePersonSchBO = null;
        String noSignIn = attRuleParamBean.getNoSignIn();
        String noSignOff = attRuleParamBean.getNoSignOff();
        String noSignInAsType = attRuleParamBean.getNoSignInAsType();
        String noSignOutAsType = attRuleParamBean.getNoSignOutAsType();

        for (AttPersonSchBO attPersonSchBO : attPersonSchBOList) {
            long timeLong = 0;
            attMode = attPersonSchBO.getAttendanceMode();
            List<AttTimeSlotBO> attTimeSlotBOList = attPersonSchBO.getAttTimeSlotArray();
            if (!CollectionUtil.isEmpty(attTimeSlotBOList)) {
                for (AttTimeSlotBO attTimeSlotBO : attTimeSlotBOList) {

                    String toWorkTime = attTimeSlotBO.getToWorkTime();
                    String offWorkTime = attTimeSlotBO.getOffWorkTime();

                    AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());
                    String timeSlotStartTime =
                        String.format("%s %s:00", attTimeSlotBO.getFirstDay(), attTimeSlotItem.getToWorkTime());
                    String timeSlotEndTime =
                        String.format("%s %s:00", attTimeSlotBO.getSecondDay(), attTimeSlotItem.getOffWorkTime());

                    // 如果非必须签到且未签到,则卡点按时段上班时间
                    if (!attTimeSlotItem.getIsMustSignIn() && noSignIn.equals(toWorkTime)) {
                        toWorkTime = timeSlotStartTime;
                    }
                    // 如果必须签到而未签到，且未签到记为迟到,则卡点按时段上班时间
                    if (attTimeSlotItem.getIsMustSignIn() && noSignIn.equals(toWorkTime)
                        && AttConstant.ATT_NOSIGNIN_COUNTTYPE_LATE.equals(noSignInAsType)) {
                        toWorkTime = AttDateUtils.addMinute(timeSlotStartTime,
                            Integer.parseInt(attRuleParamBean.getNoSignInAsLateMinute()));
                    }

                    // 如果非必须签退且未签退,则卡点按时段下班时间
                    if (!attTimeSlotItem.getIsMustSignOff() && noSignOff.equals(offWorkTime)) {
                        offWorkTime = timeSlotEndTime;
                    }
                    // 如果必须签退而未签退，且未签退记为早退,则卡点按时段下班时间
                    if (attTimeSlotItem.getIsMustSignOff() && noSignOff.equals(offWorkTime)
                        && AttConstant.ATT_NOSIGNOFF_COUNTTYPE_EARLY.equals(noSignOutAsType)) {
                        offWorkTime = AttDateUtils.addMinute(timeSlotEndTime,
                            -Integer.parseInt(attRuleParamBean.getNoSignInAsLateMinute()));
                    }

                    // 如果启用弹性提前延后上班, 时长计算上下班时间对应提前延后时间
                    if (ConstUtil.SYSTEM_COMMON_TRUE.equals(attTimeSlotItem.getEnableFlexibleWork())) {
                        // 签到时间小于最大可提前上班时间, 则按最早可提前上班时间算
                        Date startDate = null;
                        if (!noSignIn.equals(toWorkTime)) {
                            startDate = DateUtil.stringToDate(toWorkTime);
                            Date advanceWorkTime = DateUtil.addMinute(DateUtil.stringToDate(toWorkTime),
                                -attTimeSlotItem.getAdvanceWorkMinutes());
                            if (startDate.getTime() < advanceWorkTime.getTime()) {
                                startDate = advanceWorkTime;
                            }
                        }
                        // 签退时间小于可延后下班时间的, 则按最晚可延后下班时间算
                        Date endDate = null;
                        if (!noSignOff.equals(offWorkTime)) {
                            endDate = DateUtil.stringToDate(offWorkTime);
                            Date delayedWorkTime = DateUtil.addMinute(DateUtil.stringToDate(offWorkTime),
                                -attTimeSlotBO.getFlexibleTime());
                            if (endDate.getTime() > delayedWorkTime.getTime()) {
                                endDate = delayedWorkTime;
                            }
                        }
                        if (null != startDate && null != endDate) {
                            /*timeLong += endDate.getTime() - startDate.getTime();*/
                            // 扣除休息时间段
                            timeLong += attNormalAttendanceCalculate.getTimeSlotIntersectionTime(AttDateUtils.dateToStrAsLong(startDate),
                                AttDateUtils.dateToStrAsLong(endDate), attTimeSlotBO, attTimeSlotItem);
                        } else if (attMode == AttShiftConstant.AttendanceMode.NOT_BRUSH_CARD) {
                            /*timeLong += DateUtil.stringToDate(timeSlotEndTime).getTime()
                                - DateUtil.stringToDate(timeSlotStartTime).getTime();*/
                            // 扣除休息时间段
                            timeLong += attNormalAttendanceCalculate.getTimeSlotIntersectionTime(timeSlotStartTime, timeSlotEndTime, attTimeSlotBO,
                                attTimeSlotItem);
                        } else if (attMode == AttShiftConstant.AttendanceMode.ONE_DAY_ONE_CARD
                            && (null == startDate || null == endDate)) {
                            /*timeLong += DateUtil.stringToDate(timeSlotEndTime).getTime()
                                - DateUtil.stringToDate(timeSlotStartTime).getTime();*/
                            // 扣除休息时间段
                            timeLong += attNormalAttendanceCalculate.getTimeSlotIntersectionTime(timeSlotStartTime, timeSlotEndTime, attTimeSlotBO,
                                attTimeSlotItem);
                        }
                    } else {

                        if (attMode == AttShiftConstant.AttendanceMode.SHIFT_NORMAL
                            && (noSignIn.equals(toWorkTime) || noSignOff.equals(offWorkTime))) {
                            continue;
                        }
                        if (attMode == AttShiftConstant.AttendanceMode.ONE_DAY_ONE_CARD && noSignIn.equals(toWorkTime)
                            && noSignOff.equals(offWorkTime)) {
                            continue;
                        }
                        /*Date toWorkDateTime = DateUtil.stringToDate(
                            StringUtils.compare(toWorkTime, timeSlotStartTime) < 0 ? timeSlotStartTime : toWorkTime);
                        Date offWorkDateTime = DateUtil.stringToDate(
                            StringUtils.compare(offWorkTime, timeSlotEndTime) > 0 ? timeSlotEndTime : offWorkTime);
                        timeLong += (offWorkDateTime.getTime() - toWorkDateTime.getTime());*/
                        // 扣除休息时间段
                        String start =
                            StringUtils.compare(toWorkTime, timeSlotStartTime) < 0 ? timeSlotStartTime : toWorkTime;
                        String end =
                            StringUtils.compare(offWorkTime, timeSlotEndTime) > 0 ? timeSlotEndTime : offWorkTime;
                        timeLong += attNormalAttendanceCalculate.getTimeSlotIntersectionTime(start, end, attTimeSlotBO, attTimeSlotItem);

                    }
                }
            }
            if (null == longestTime || timeLong > longestTime) {
                longestTime = timeLong;
                longestTimePersonSchBO = attPersonSchBO;
            }
        }
        return longestTimePersonSchBO;
    }

    /**
     * 获取异常最少的班次
     * 
     * @param attPersonSchBOList
     * @param attRuleParamBean
     * @param attTimeSlotItemMap
     * @return
     */
    private AttPersonSchBO leastException(List<AttPersonSchBO> attPersonSchBOList, AttRuleParamBean attRuleParamBean,
        Map<String, AttTimeSlotItem> attTimeSlotItemMap) {
        Integer leastException = null;
        AttPersonSchBO leastExceptionPersonSchBO = null;
        for (AttPersonSchBO attPersonSchBO : attPersonSchBOList) {
            int exceptionNum = 0;
            List<AttTimeSlotBO> attTimeSlotBOList = attPersonSchBO.getAttTimeSlotArray();

            if (CollectionUtil.isEmpty(attTimeSlotBOList)) {
                // 没有时段,无异常(节假日/排班且休息) 直接返回
                return attPersonSchBO;
            }

            for (AttTimeSlotBO attTimeSlotBO : attTimeSlotBOList) {
                AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());

                String toWorkTime = attTimeSlotBO.getToWorkTime();
                String offWorkTime = attTimeSlotBO.getOffWorkTime();

                // 必须签到才有异常
                if (attTimeSlotItem.getIsMustSignIn()) {
                    if (attRuleParamBean.getNoSignIn().equals(toWorkTime)) {
                        // 未签到
                        exceptionNum++;
                    } else {
                        String timeSlotToWorkTime =
                            String.format("%s %s:00", attTimeSlotBO.getFirstDay(), attTimeSlotItem.getToWorkTime());
                        if (timeSlotToWorkTime.compareTo(toWorkTime) < 0) {
                            // 迟到
                            exceptionNum++;
                        }
                    }
                }

                if (attTimeSlotItem.getIsMustSignOff()) {
                    if (attRuleParamBean.getNoSignOff().equals(offWorkTime)) {
                        // 未签到
                        exceptionNum++;
                    } else {
                        String timeSlotOffWorkTime =
                            String.format("%s %s:00", attTimeSlotBO.getSecondDay(), attTimeSlotItem.getOffWorkTime());
                        if (timeSlotOffWorkTime.compareTo(offWorkTime) > 0) {
                            // 早退
                            exceptionNum++;
                        }
                    }
                }
            }
            if (null == leastException || exceptionNum < leastException) {
                leastException = exceptionNum;
                leastExceptionPersonSchBO = attPersonSchBO;
            }
        }
        return leastExceptionPersonSchBO;
    }
}
