package com.zkteco.zkbiosecurity.att.service.impl;

import java.text.SimpleDateFormat;
import java.util.*;

import com.zkteco.zkbiosecurity.att.api.vo.AttApiMonthStatisticalReportItem;
import com.zkteco.zkbiosecurity.att.constants.AppConstant;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.dao.AttRecordDao;
import com.zkteco.zkbiosecurity.att.model.AttRecord;
import com.zkteco.zkbiosecurity.att.service.AttAppService;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.base.vo.AppResultMessage;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 海外app相关接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/2/19 11:33
 * @since 1.0.0
 */
@Service
public class AttAppServiceImpl implements AttAppService {

    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AttRecordDao attRecordDao;
    @Autowired
    private AttParamService attParamService;

    @Override
    public AppResultMessage getAttendanceCalendar(String pin, String date) {
        AppResultMessage appResultMessage = new AppResultMessage();
        // 转换时间戳，判定当前是哪一个月
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        Long time = new Long(date);
        String yearMonth = format.format(time);
        String[] yearMonthArray = yearMonth.split("-");
        // 获取当月的第一天和最后一天
        int year = Integer.parseInt(yearMonthArray[0]);
        int month = Integer.parseInt(yearMonthArray[1]);
        Date startDate = AttDateUtils.getFirstDayOfMonth(year, month);
        Date endDate = AttDateUtils.getLastDayOfMonth(year, month);
        // 查找用户
        PersPersonItem personItem = persPersonService.getItemByPin(pin);
        // 外部数据
        JSONObject externalDataJsonObject = new JSONObject();
        externalDataJsonObject.put("name", personItem.getName());
        externalDataJsonObject.put("dept", personItem.getDeptName());
        externalDataJsonObject.put("shift", "");

        // 查询考勤计算记录
        List<AttRecord> attRecordList = attRecordDao
            .findAttRecordByPersonPinAndAttDateGreaterThanEqualAndAttDateLessThanEqual(pin, startDate, endDate);

        // 内部数据
        JSONObject insideDataJsonObject = new JSONObject();
        // 内部每天数据详情
        Map<String, Object> insideDataMap;
        // 内部每天考勤记录
        List<Map<String, Object>> timeList;
        // 小数点精确度
        String decimal = attParamService.getDecimal();
        for (AttRecord attRecord : attRecordList) {
            insideDataMap = new HashMap<>();
            // 当日考勤有效工时
            insideDataMap.put("hours", attParamService.minutesToHourFormat(attRecord.getActualMinute() + "", decimal));
            // 时间段详情
            insideDataMap.put("shift", attRecord.getShiftTimeData());
            // 有效打卡次数
            insideDataMap.put("count", attRecord.getCardValidCount());
            // 考勤状态
            String attendanceStatus = attRecord.getAttendanceStatus();
            // 有效打卡数据
            String cardValidData = attRecord.getCardValidData();
            // 当天考勤状态
            if (attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.EARLY)
                || attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.LATE)
                || attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.ABSENT)
                || attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.NO_CHECK_IN_INCOMPLETE)
                || attendanceStatus.contains(AttCalculationConstant.AttAttendStatus.NO_CHECK_OUT_INCOMPLETE)) {
                // 异常
                insideDataMap.put("state", 0);
            } else if (StringUtils.isBlank(cardValidData)) {
                // 判断是否有出勤
                insideDataMap.put("state", -1);
            } else {
                // 用户有出勤，又不存在异常，就为正常
                insideDataMap.put("state", 1);
            }

            // 打卡状态
            String cardStatusData = attRecord.getCardStatus();
            String shiftTimeData = attRecord.getShiftTimeData();

            if (StringUtils.isNotBlank(cardStatusData) && StringUtils.isNotBlank(shiftTimeData)&& StringUtils.isNotBlank(cardValidData)) {
                timeList = new ArrayList<>();
                String[] cardStatusDataArray = cardStatusData.split(";");
                String[] shiftTimeDataArray = shiftTimeData.split(";");
                String[] cardValidDataArray = cardValidData.split(";");
                Map<String, Object> timeDeatilStart = new HashMap<>();
                Map<String, Object> timeDeatilEnd = new HashMap<>();

                for (int i = 0; i < cardStatusDataArray.length; i++) {
                    String startStatus = "ATT_NORMAL_PUNCH"; //ATT_MISSING_CARD、ATT_LATE、ATT_EARLY
                    // 有效打卡状态
                    String[] cardStatusArray = cardStatusDataArray[i].split("-");
                    String startCardStatus = cardStatusArray[0];
                    String endCardStatus = cardStatusArray[1];
                    // 时间段打卡时间
                    String[] shiftTimeArray = shiftTimeDataArray[i].split("-");
                    String startShiftTime = shiftTimeArray[0];
                    String endShiftTime = shiftTimeArray[1];
                    // 有效打卡时间
                    String[] cardValidArray = cardValidDataArray[i].split("-");
                    String startCardValidTime = cardValidArray[0];
                    String endCardValidTime = cardValidArray[1];

                    timeDeatilStart = new HashMap<>();
                    timeDeatilStart.put("time", startShiftTime);
                    if (AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION.equals(startCardStatus)) {
                        timeDeatilStart.put("state", "-1");
                        if (StringUtils.isBlank(startCardValidTime) || !startCardValidTime.contains(":")) {
                            startStatus = "ATT_MISSING_CARD";
                        } else {
                            startStatus = "ATT_LATE";
                        }
                    }
                    timeDeatilStart.put("desc", startStatus);
                    timeList.add(timeDeatilStart);

                    String endStatus = "ATT_NORMAL_PUNCH"; //ATT_MISSING_CARD、ATT_LATE、ATT_EARLY
                    timeDeatilEnd = new HashMap<>();
                    timeDeatilEnd.put("time", endShiftTime);
                    if (AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION.equals(endCardStatus)) {
                        timeDeatilEnd.put("state", "-1");
                        if (StringUtils.isBlank(endCardValidTime) || !endCardValidTime.contains(":")) {
                            endStatus = "ATT_MISSING_CARD";
                        } else {
                            endStatus = "ATT_EARLY";
                        }
                    }
                    timeDeatilEnd.put("desc", endStatus);
                    timeList.add(timeDeatilEnd);
                }
                insideDataMap.put("items", timeList);
                // 获取当天的日期
                Long dateLong = attRecord.getAttDate().getTime();
                int day = AttDateUtils.getDayOfMonth(dateLong);
                insideDataJsonObject.put(day + "", insideDataMap);
            }
        }
        externalDataJsonObject.put("data", insideDataJsonObject);
        appResultMessage.setData(externalDataJsonObject);
        return appResultMessage;
    }

    @Override
    public JSONObject monthlyStatistics(String pin, Date startDate, Date endDate) {
        // 根据条件查询数据
        AttApiMonthStatisticalReportItem condition = new AttApiMonthStatisticalReportItem();
        condition.setPin(pin).setMonthStart(startDate).setMonthEnd(endDate).setEquals(true);
        List<AttApiMonthStatisticalReportItem> itemList = (List<AttApiMonthStatisticalReportItem>)attRecordDao
            .getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
        AttApiMonthStatisticalReportItem item = new AttApiMonthStatisticalReportItem();
        if (!CollectionUtil.isEmpty(itemList)) {
            item = itemList.get(0);
        }
        String decimal = attParamService.getDecimal();
        JSONObject returnObject = new JSONObject();
        // 出勤
        JSONObject attendanceObject = new JSONObject();
        attendanceObject.put("shouldHour", attParamService.minutesToHourFormat(item.getShouldHour(), decimal));
        attendanceObject.put("actualHour", attParamService.minutesToHourFormat(item.getActualHour(), decimal));
        attendanceObject.put("validHour", attParamService.minutesToHourFormat(item.getValidHour(), decimal));
        returnObject.put("attendanceData", attendanceObject);
        // 迟到
        JSONObject lateObject = new JSONObject();
        lateObject.put("lateHour", attParamService.minutesToHourFormat(item.getLateMinute(), decimal));
        lateObject.put("lateCount", StringUtils.isNotBlank(item.getLateCountTotal()) ? item.getLateCountTotal() : "0");
        returnObject.put("lateData", lateObject);
        // 早退
        JSONObject earlyObject = new JSONObject();
        earlyObject.put("earlyHour", attParamService.minutesToHourFormat(item.getEarlyMinute(), decimal));
        earlyObject.put("earlyCount", StringUtils.isNotBlank(item.getEarlyCount()) ? item.getEarlyCount() : "0");
        returnObject.put("earlyData", earlyObject);
        // 加班
        JSONObject overtimeObject = new JSONObject();
        overtimeObject.put("usualHour", attParamService.minutesToHourFormat(item.getOvertimeUsualHour(), decimal));
        overtimeObject.put("restHour", attParamService.minutesToHourFormat(item.getOvertimeRestHour(), decimal));
        overtimeObject.put("holidayHour", attParamService.minutesToHourFormat(item.getOvertimeHolidayHour(), decimal));
        overtimeObject.put("totalHour", attParamService.minutesToHourFormat(item.getOvertimeHour(), decimal));
        returnObject.put("overtimeData", overtimeObject);
        // 异常
        JSONObject exceptionObject = new JSONObject();
        exceptionObject.put("absentHour", attParamService.minutesToHourFormat(item.getAbsentHour(), decimal));
        exceptionObject.put("leaveHour", attParamService.minutesToHourFormat(item.getLeaveMinuteTotal(), decimal));
        exceptionObject.put("tripHour", attParamService.minutesToHourFormat(item.getTripHour(), decimal));
        exceptionObject.put("outHour", attParamService.minutesToHourFormat(item.getOutHour(), decimal));
        returnObject.put("exceptionData", exceptionObject);
        return returnObject;
    }
}
