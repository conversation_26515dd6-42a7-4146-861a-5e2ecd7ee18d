package com.zkteco.zkbiosecurity.att.dao;

import com.zkteco.zkbiosecurity.att.model.AttBreakTime;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 休息时间段
 *
 * <AUTHOR>
 * @date 2019/5/30
 */
public interface AttBreakTimeDao extends BaseDao<AttBreakTime, String> {
    /**
     * 判断名称是否存在
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/7/31 16:02
     * @param name
     * @return boolean
     */
    boolean existsByName(String name);

    /**
     * 根据开始时间和结束时间查找对象
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/7/31 16:02
     * @param startTime
     * @param endTime
     * @return com.zkteco.zkbiosecurity.att.model.AttBreakTime
     */
    AttBreakTime findByStartTimeAndEndTime(String startTime, String endTime);
}
