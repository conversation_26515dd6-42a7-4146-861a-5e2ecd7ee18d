package com.zkteco.zkbiosecurity.att.license;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * zkTime许可检测
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/1/14 17:37
 * @since 1.0.0
 */
@Component
// 当system.zkcloud.productCode:配置为ZKTime，需要注入该许可检测
@ConditionalOnProperty(name = "system.zkcloud.productCode", havingValue = "ZKTime", matchIfMissing = false)
public class IndustryCodeUtil {
    public int getIndustryCode() {
        // zkTime编码9
        return 9;
    }
}
