//package com.zkteco.zkbiosecurity.att.license;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import com.zkteco.zkbiosecurity.att.service.AttPointService;
//import com.zkteco.zkbiosecurity.core.guard.annotation.InmutableClassSign;
//import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
//import com.zkteco.zkbiosecurity.license.vo.IModuleAuthDefault;
//
///**
// * 智能场景视频当考勤许可-登录检测
// *
// * <AUTHOR>
// * @date 2021-02-04 14:02
// * @since 1.0.0
// */
//@Component
//@InmutableClassSign(module = ConstUtil.SYSTEM_MODULE_ATT)
//public class EsdcForAttModuleAuthCheck implements IModuleAuthDefault {
//
//    @Autowired
//    private AttPointService attPointService;
//
//    @Override
//    public boolean enable() {
//        return true;
//    }
//
//    @Override
//    public String module() {
//        return "esdcatt";
//    }
//
//    @Override
//    public int controlCount() {
//        return attPointService.getCountByDeviceModule(ConstUtil.LICENSE_MODULE_ESDC);
//    }
//}
