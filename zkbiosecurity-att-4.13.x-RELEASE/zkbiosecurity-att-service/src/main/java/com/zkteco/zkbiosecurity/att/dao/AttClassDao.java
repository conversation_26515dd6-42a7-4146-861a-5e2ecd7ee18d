package com.zkteco.zkbiosecurity.att.dao;

import com.zkteco.zkbiosecurity.att.model.AttClass;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 调班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:10
 * @since 1.0.0
 */
public interface AttClassDao extends BaseDao<AttClass, String> {

    boolean existsBySwapShiftIdIn(List<String> shiftIdList);

    /**
     * 根据pin集合查询时间范围内是否有申请
     * 
     * @param personIdList
     * @param startTime
     * @param endTime
     * @return
     */
    @Query(
        value = "SELECT t FROM AttClass t WHERE t.adjustPersonId IN (?1) AND (t.adjustDate >= ?2 AND t.adjustDate <= ?3) AND t.flowStatus IN ('0','2')")
    List<AttClass> getByPersonIdAndTime(List<String> personIdList, Date startTime, Date endTime);

    List<AttClass> findByAdjustPersonIdAndAdjustDateAndFlowStatusIn(String adjustPersonId, Date adjustDate,
        List<String> flowStatusList);

    List<AttClass> findBySwapPersonIdAndSwapDateAndFlowStatusIn(String swapPersonId, Date swapDate,
        List<String> flowStatusList);

    /**
     * 根据人员编号 调班开始时间与结束时间，判断是否已有调班记录
     * 
     * @date 2018/12/28 19:20
     * @param personPin
     *            人员编号
     * @param startTime
     *            开始时间
     * @param endTime
     *            结束时间
     * @return com.zkteco.zkbiosecurity.att.model.AttClass
     */
    @Query(
        value = "SELECT t FROM AttClass t WHERE t.adjustPersonPin = ?1 AND (t.adjustDate >= ?2 AND t.adjustDate <= ?3)")
    AttClass getByPersonPinAndTime(String personPin, Date startTime, Date endTime);

    /**
     * 根据流程key查找调班
     * 
     * @param businessKey
     * @return
     */
    AttClass findByBusinessKey(String businessKey);

    @Query("SELECT t FROM AttClass t WHERE t.flowStatus='2' AND t.adjustDate >= ?1 AND t.adjustDate <= ?2 AND t.adjustType = ?3")
    List<AttClass> findClassByDate(Date startDate, Date endDate, short adjustType);

    /**
     * 根据人员pin和日期范围,查询该人员期间内的调班记录
     *
     * @param startDate
     * @param endDate
     * @param personPin
     * @return
     */
    @Query("SELECT t FROM AttClass t WHERE t.flowStatus='2' AND (t.adjustPersonPin = ?1 OR t.swapPersonPin = ?1) AND ((t.adjustDate >= ?2 AND t.adjustDate <= ?3 ) OR (t.swapDate >= ?2 AND t.swapDate <= ?3))")
    List<AttClass> findClassByPinAndDate(String personPin, Date startDate, Date endDate);

    /**
     * 根据人员pin和日期范围,查询该人员期间内的调班记录
     * 
     * @param pins
     * @param startDate
     * @param endDate
     * 
     * @return
     */
    @Query("SELECT t FROM AttClass t WHERE t.flowStatus='2' AND (t.adjustPersonPin IN (?1) OR t.swapPersonPin IN (?1)) AND ((t.adjustDate >= ?2 AND t.adjustDate <= ?3 ) OR (t.swapDate >= ?2 AND t.swapDate <= ?3))")
    List<AttClass> findClassByPinsAndDate(List<String> pins, Date startDate, Date endDate);

    /**
     * 查找日期范围内的调班记录
     * 
     * @param startDate
     * @param endDate
     * @return
     */
    @Query("SELECT t FROM AttClass t WHERE t.flowStatus='2' AND ((t.adjustDate >= ?1 AND t.adjustDate <= ?2 ) OR (t.swapDate >= ?1 AND t.swapDate <= ?2))")
    List<AttClass> findClassBetweenDate(Date startDate, Date endDate);

    @Modifying
    @Transactional
    @Query("update AttClass t set t.flowStatus=?2 where t.id in (?1)")
    void updateFlowStatus(List<String> asList, String flowStatusComplete);
}