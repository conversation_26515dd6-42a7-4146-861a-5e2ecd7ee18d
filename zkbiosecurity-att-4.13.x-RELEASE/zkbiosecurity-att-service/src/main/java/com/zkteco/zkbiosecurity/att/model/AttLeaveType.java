package com.zkteco.zkbiosecurity.att.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 假种
 */
@Entity
@Table(name = "ATT_LEAVETYPE")
@Setter
@Getter
@Accessors(chain = true)
public class AttLeaveType extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 假种编号
     */
    @Column(name = "LEAVETYPE_NO", length = 50)
    private String leaveTypeNo;

    /**
     * 假种名称
     */
    @Column(name = "LEAVETYPE_NAME", length = 50)
    private String leaveTypeName;

    /**
     * 是否扣上班时长（false：否/0，true：是/1）
     */
    @Column(name = "IS_DEDUCT_WORK_LONG")
    private Boolean isDeductWorkLong;

    /**
     * 最小单位-数
     */
    @Column(name = "CONVERT_COUNT")
    private Double convertCount;

    /**
     * 最小单位-单位（minute=分钟,day=工作日,hour=小时）
     */
    @Column(name = "CONVERT_UNIT", length = 20)
    private String convertUnit;

    /**
     * 舍入控制（abort=向下（舍弃）,rounding=四舍五入,carry=向上（进位））
     */
    @Column(name = "CONVERT_TYPE", length = 20)
    private String convertType;

    /**
     * 报表展示符号
     */
    @Column(name = "SYMBOL", length = 20)
    private String symbol;

    /**
     * 标记（params=参数设置、leaveType=假种）
     */
    @Column(name = "MARK", length = 20)
    private String mark;

    /** 排序编号*/
    @Column(name = "SORT_NO")
    private Integer sortNo;

    /**
     * 月考勤状态表显示颜色
     */
    @Column(name = "COLOR")
    private String color;

    /**
     * 启用年度累计天数
     */
    @Column(name = "ENABLE_MAX_DAYS")
    private Boolean enableMaxDays;

    /**
     * 年度累计天数
     */
    @Column(name = "MAX_DAYS")
    private Integer maxDays;

    /**
     * 初始化标识位
     */
    @Column(name = "INIT_FLAG")
    private Boolean initFlag;

}