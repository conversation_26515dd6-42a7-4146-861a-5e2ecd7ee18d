package com.zkteco.zkbiosecurity.att.provider;

import com.zkteco.zkbiosecurity.acc.service.Acc4AttTransactionExternalService;
import com.zkteco.zkbiosecurity.acc.vo.Acc4AttIsExceptionTimeItem;
import com.zkteco.zkbiosecurity.acc.vo.Acc4AttTransactionExternalItem;
import com.zkteco.zkbiosecurity.att.api.vo.MessagePushCloudSendMessageItem;
import com.zkteco.zkbiosecurity.att.cache.AttCacheManager;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttTimeSlotBO;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttCycleSchDao;
import com.zkteco.zkbiosecurity.att.model.AttBreakTime;
import com.zkteco.zkbiosecurity.att.model.AttCycleSch;
import com.zkteco.zkbiosecurity.att.model.AttShift;
import com.zkteco.zkbiosecurity.att.model.AttTimeSlot;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.ins.service.Ins4AttTransactionService;
import com.zkteco.zkbiosecurity.ins.vo.Ins4AttTransactionItem;
import com.zkteco.zkbiosecurity.park.service.Park4AttTransactionService;
import com.zkteco.zkbiosecurity.park.vo.Park4AttTransactionItem;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.pid.service.Pid4AttTransactionService;
import com.zkteco.zkbiosecurity.pid.vo.Pid4AttTransactionItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 其它模块当做考勤推送数据实现类
 *
 * @version V1.0
 * @date Created In 15:25 2018/11/27
 * @deprecated 第三方考勤记录改为定时获取 2020/7/14
 */
@Slf4j
@Service
@Transactional
public class AttTransactionProviderServiceImpl implements Att4OtherTransactionService, Pid4AttTransactionService,
        Ins4AttTransactionService, Acc4AttTransactionExternalService, Park4AttTransactionService {
    @Autowired
    AttCycleSchDao attCycleSchDao;
    @Autowired
    private AttPointService attPointService;
    @Autowired
    private AttTransactionService attTransactionService;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired(required = false)
    private AttGetParkDeviceAreaService attGetParkDeviceAreaService;
    @Autowired
    private AttRealTimePushService attRealTimePushService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    AttShiftService attShiftService;
    @Autowired
    AttCycleSchService attCycleSchService;
    @Autowired
    AttPersonSchService attPersonSchService;

    @Autowired(required = false)
    private AttCloudMessageSendService attCloudMessageSendService;
    @Autowired
    private AttPersonSchDataService attPersonSchDataService;
    @Autowired
    private AttTimeSlotService attTimeSlotService;
    @Autowired
    private AttRecordService attRecordService;
    @Autowired
    private AttCacheManager attCacheManager;

    @Override
    public void pushTransaction(Att4OtherTransactionItem transactionItem) {
        if (Objects.isNull(transactionItem) || StringUtils.isBlank(transactionItem.getModuleName())) {
            return;
        }

        PersPersonItem persPersonItem = persPersonService.getItemByPin(transactionItem.getPersonPin());
        if (Objects.isNull(persPersonItem)) {
            return;
        }
        // 查找考勤点
        AttPointItem attPointItem = getAttPointItem(transactionItem);
        if (Objects.isNull(attPointItem)) {
            return;
        }

        AttTransactionItem attTransactionItem = new AttTransactionItem();
        ModelUtil.copyPropertiesIgnoreNull(transactionItem, attTransactionItem);
        attTransactionItem.setAttPhotoUrl(transactionItem.getTransactionPhoto());
        attTransactionItem.setPersonName(persPersonItem.getName());
        attTransactionItem.setPersonLastName(persPersonItem.getLastName());
        attTransactionItem.setDeptId(persPersonItem.getDeptId());
        attTransactionItem.setDeptCode(persPersonItem.getDeptCode());
        attTransactionItem.setDeptName(persPersonItem.getDeptName());
        attTransactionItem.setMark(transactionItem.getModuleName());

        // 设备名称
        attTransactionItem.setDeviceName(attPointItem.getDeviceName());
        // 添加区域数据信息
        String areaId = attPointItem.getAreaId();
        if (ConstUtil.SYSTEM_MODULE_PARK.equals(transactionItem.getModuleName())) {
            // 停车模块获取的是车场区域，跟其他模块不一样
            Att4ParkEntranceArea parkEntranceArea = attGetParkDeviceAreaService.getParkEntranceAreaById(areaId);
            if (Objects.nonNull(parkEntranceArea)) {
                attTransactionItem.setAreaId(parkEntranceArea.getId());
                attTransactionItem.setAreaName(parkEntranceArea.getName());
            }
        } else {
            AuthAreaItem authAreaItem = authAreaService.getItemById(areaId);
            if (Objects.nonNull(authAreaItem)) {
                attTransactionItem.setAreaId(authAreaItem.getId());
                attTransactionItem.setAreaNo(authAreaItem.getCode());
                attTransactionItem.setAreaName(authAreaItem.getName());
            }
        }

        attTransactionItem = attTransactionService.saveItem(attTransactionItem);
        // 实时点名推送、包含场景推送
        attRealTimePushService.pushTransaction(Collections.singletonList(attTransactionItem));
    }

    @Override
    public void pushAttTransaction(Att4OtherTransactionItem transactionItem) {
        if (Objects.isNull(transactionItem) || StringUtils.isBlank(transactionItem.getModuleName())) {
            return;
        }
        PersPersonItem persPersonItem = persPersonService.getItemByPin(transactionItem.getPersonPin());
        if (Objects.isNull(persPersonItem)) {
            return;
        }
        AttTransactionItem attTransactionItem = new AttTransactionItem();
        ModelUtil.copyPropertiesIgnoreNull(transactionItem, attTransactionItem);
        attTransactionItem.setPersonName(persPersonItem.getName());
        attTransactionItem.setPersonLastName(persPersonItem.getLastName());
        attTransactionItem.setDeptId(persPersonItem.getDeptId());
        attTransactionItem.setDeptCode(persPersonItem.getDeptCode());
        attTransactionItem.setDeptName(persPersonItem.getDeptName());
        attTransactionItem.setMark(transactionItem.getModuleName());
        attTransactionService.saveItem(attTransactionItem);
        // 实时点名推送
        attRealTimePushService.pushTransaction(Collections.singletonList(attTransactionItem));

        // 推送给微信公众号消息
        try {
            sendTransactionWxMessage(attTransactionItem);
        } catch (Exception e) {
            log.error("sendTransactionWxMessage Exception {}", e);
        }

    }

    /**
     * 推送给微信公众号消息 <br/>
     * <p>
     * 推送频率规则: <br/>
     * 1)第一次则直接推送 <br/>
     * 2)根据考勤计算结果或排班情况,判断是否在班前或班后第一次打卡则直接推送 <br/>
     * 3)否则根据间隔时间推送<br/>
     *
     * @param attTransactionItem:
     * @return void
     * <AUTHOR>
     * @date 2020-11-27 11:54
     * @since 1.0.0
     */
    private void sendTransactionWxMessage(AttTransactionItem attTransactionItem) {

        log.info("sendTransactionWxMessage {}", attTransactionItem);

        String personPin = attTransactionItem.getPersonPin();
        Date attDatetime = attTransactionItem.getAttDatetime();
        String attDate = AttDateUtils.dateToStrAsShort(attDatetime);
        Date dayBeginTime = DateUtil.getDayBeginTime(attDatetime);
        Date dayEndTime = DateUtil.getDayEndTime(attDatetime);

        MessagePushCloudSendMessageItem messageItem = new MessagePushCloudSendMessageItem();
        messageItem.setTouser(personPin);
        messageItem.setSendMessageType("attTransactionWxMessage");
        messageItem.setMessageType("attSignWxMessage"); // 云端定义
        messageItem.setFirstContent(I18nUtil.i18nCode("att_sdc_wxMsg_firstData"));
        messageItem.setCommonContent1(attTransactionItem.getPersonName() + "(" + personPin + ")");
        messageItem.setCommonContent2(DateUtil.dateToString(attDatetime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM));
        messageItem.setCommonContent3(I18nUtil.i18nCode("att_sdc_wxMsg_stateData"));
        messageItem.setRemark(I18nUtil.i18nCode("att_sdc_wxMsg_remark"));

        // 第一次则推送并记录时间
        String lastTime = attCacheManager.getPersonTransactionTime(personPin);
        if (StringUtils.isBlank(lastTime)) {
            attCacheManager.setPersonTransactionTime(personPin, AttDateUtils.dateToStrAsLong(attDatetime));
            if (null != attCloudMessageSendService) {
                attCloudMessageSendService.asyncPushTransactionWxMsgToCloud(Arrays.asList(messageItem));
            }
            return;
        }
        // 上次打卡推送时间
        Date lastDateTime = DateUtil.stringToDate(lastTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);

        // 班前班后临界点的不用等间隔时间
        AttRecordItem attRecordItem = attRecordService.getItemByPinAndDate(personPin, attDate);
        if (Objects.nonNull(attRecordItem)) {
            String shiftTimeData = attRecordItem.getShiftTimeData();
            if (StringUtils.isNotBlank(shiftTimeData)) {
                String[] shiftTimeArr = shiftTimeData.split(";");
                for (String timeSlot : shiftTimeArr) {
                    String[] timeSlotArr = timeSlot.split("-");
                    // 班前班后临界点的不用等间隔时间
                    Date startWorkTime = DateUtil.stringToDate(timeSlotArr[0], DateUtil.DateStyle.HH_MM);
                    Date offWorkTime = DateUtil.stringToDate(timeSlotArr[1], DateUtil.DateStyle.HH_MM);
                    boolean isSend = false;
                    if ((startWorkTime.getTime()
                            - attDatetime.getTime()) < AttConstant.ATT_PUSH_TRANSACTION_MESSAGE_FREQUENCY) {
                        // 上班前打卡
                        if ((startWorkTime.getTime()
                                - lastDateTime.getTime()) > AttConstant.ATT_PUSH_TRANSACTION_MESSAGE_FREQUENCY) {
                            // 上次打卡在间隔前
                            isSend = true;
                        }
                    }
                    if ((attDatetime.getTime()
                            - offWorkTime.getTime()) < AttConstant.ATT_PUSH_TRANSACTION_MESSAGE_FREQUENCY) {
                        // 下班后打卡
                        if (lastDateTime.getTime() < offWorkTime.getTime()) {
                            // 上次打卡《 下班时间 《 当前打卡 《 下班后间隔
                            isSend = true;
                        }
                    }
                    if (isSend) {
                        attCacheManager.setPersonTransactionTime(personPin, AttDateUtils.dateToStrAsLong(attDatetime));
                        attCacheManager.setPersonTransactionTime(personPin, AttDateUtils.dateToStrAsLong(attDatetime));
                        if (null != attCloudMessageSendService) {
                            attCloudMessageSendService.asyncPushTransactionWxMsgToCloud(Arrays.asList(messageItem));
                        }
                        return;
                    }
                }
            }
        } else {
            // 获取排班信息
            Map<String, List<AttPersonSchBO>> personSchData =
                    attPersonSchDataService.getPersonAllSchData(Arrays.asList(personPin), dayBeginTime, dayEndTime);
            List<AttPersonSchBO> attPersonSchBOList = personSchData.get(personPin + "=" + attDate);
            if (!CollectionUtil.isEmpty(attPersonSchBOList)) {
                AttPersonSchBO attPersonSchBO = attPersonSchBOList.get(0);
                List<AttTimeSlotBO> attTimeSlotArray = attPersonSchBO.getAttTimeSlotArray();
                if (!CollectionUtil.isEmpty(attTimeSlotArray)) {
                    List<AttTimeSlotItem> allTimeSlotItem = attTimeSlotService.getAllTimeSlotItem();
                    Map<String, AttTimeSlotItem> timeSlotItemMap = CollectionUtil.itemListToIdMap(allTimeSlotItem);
                    for (AttTimeSlotBO attTimeSlotBO : attTimeSlotArray) {
                        String attTimeSlotId = attTimeSlotBO.getAttTimeSlotId();
                        AttTimeSlotItem attTimeSlotItem = timeSlotItemMap.get(attTimeSlotId);
                        boolean isSend = false;

                        if (AttConstant.PERIODTYPE_NORMAL.equals(attTimeSlotItem.getPeriodType())) {
                            Date startWorkTime = AttDateUtils.stringToYmdHmsDate(String.format("%s %s:00",
                                    attTimeSlotBO.getFirstDay(), attTimeSlotItem.getToWorkTime()));
                            Date offWorkTime = AttDateUtils.stringToYmdHmsDate(String.format("%s %s:00",
                                    attTimeSlotBO.getSecondDay(), attTimeSlotItem.getOffWorkTime()));
                            if ((startWorkTime.getTime()
                                    - attDatetime.getTime()) < AttConstant.ATT_PUSH_TRANSACTION_MESSAGE_FREQUENCY) {
                                // 上班前打卡
                                if ((startWorkTime.getTime()
                                        - lastDateTime.getTime()) > AttConstant.ATT_PUSH_TRANSACTION_MESSAGE_FREQUENCY) {
                                    // 上次打卡在间隔前
                                    isSend = true;
                                }
                            }
                            if ((attDatetime.getTime()
                                    - offWorkTime.getTime()) < AttConstant.ATT_PUSH_TRANSACTION_MESSAGE_FREQUENCY) {
                                // 下班后打卡
                                if (lastDateTime.getTime() < offWorkTime.getTime()) {
                                    // 上次打卡《 下班时间 《 当前打卡 《 下班后间隔
                                    isSend = true;
                                }
                            }
                        }

                        if (isSend) {
                            attCacheManager.setPersonTransactionTime(personPin,
                                    AttDateUtils.dateToStrAsLong(attDatetime));
                            attCacheManager.setPersonTransactionTime(personPin,
                                    AttDateUtils.dateToStrAsLong(attDatetime));
                            if (null != attCloudMessageSendService) {
                                attCloudMessageSendService.asyncPushTransactionWxMsgToCloud(Arrays.asList(messageItem));
                            }
                            return;
                        }

                    }
                }
            }
        }

        // 非上下班临界时间的大于间隔时间才发送
        if ((attDatetime.getTime() - AttDateUtils.stringToYmdHmsDate(lastTime)
                .getTime()) > AttConstant.ATT_PUSH_TRANSACTION_MESSAGE_FREQUENCY) {
            attCacheManager.setPersonTransactionTime(personPin, AttDateUtils.dateToStrAsLong(attDatetime));
            if (null != attCloudMessageSendService) {
                attCloudMessageSendService.asyncPushTransactionWxMsgToCloud(Arrays.asList(messageItem));
            }
        }
    }

    /**
     * 查找考勤点
     *
     * @param transactionItem
     * @return com.zkteco.zkbiosecurity.att.vo.AttPointItem
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/7/30 17:16
     */
    private AttPointItem getAttPointItem(Att4OtherTransactionItem transactionItem) {
        AttPointItem attPointItem = attPointService.getItemByDeviceId(transactionItem.getDeviceId());
        if (Objects.isNull(attPointItem)) {
            List<AttPointItem> attPointItemList = attPointService.getItemsByDeviceSn(transactionItem.getDeviceSn());
            if (attPointItemList.size() > 0) {
                attPointItem = attPointItemList.get(0);
            }
        }
        return attPointItem;
    }

    /**
     * 保存VMS推送记录当考勤记录
     *
     * @param transactionItem
     */
    @Deprecated
    private void pushVmsTransaction(Att4OtherTransactionItem transactionItem) {
        AttTransactionItem attTransactionItem = new AttTransactionItem();
        AttPersonItem attPersonItem = attPersonService.getItemByPersonPin(transactionItem.getPersonPin());
        // 如果人员没有考勤区域则不推送记录到考勤原始记录表
        if (Objects.isNull(attPersonItem)) {
            return;
        }
        // 过滤非加入的考勤点设备推送记录
        AttPointItem attPointItem =
                attPointService.getItemByDeviceSnAndDoorNo(transactionItem.getDeviceSn(), transactionItem.getChannelNo());
        if (Objects.isNull(attPointItem)) {
            return;
        }

        ModelUtil.copyPropertiesIgnoreNull(transactionItem, attTransactionItem);
        attTransactionItem.setDeptId(attPersonItem.getDeptId());
        attTransactionItem.setDeptCode(attPersonItem.getDeptCode());
        attTransactionItem.setDeptName(attPersonItem.getDeptName());
        attTransactionItem.setMark(ConstUtil.SYSTEM_MODULE_VMS);
        // vms视频设备名称由设备名称+通道名称组合 by ljf 2020/1/21
        attTransactionItem.setDeviceName(transactionItem.getDeviceName() + "-" + transactionItem.getChannelName());
        attTransactionItem.setDoorNo(transactionItem.getChannelNo());
        // 添加区域数据信息
        String areaId = attPointItem.getAreaId();
        AuthAreaItem authAreaItem = authAreaService.getItemById(areaId);
        if (Objects.nonNull(authAreaItem)) {
            attTransactionItem.setAreaId(authAreaItem.getId());
            attTransactionItem.setAreaNo(authAreaItem.getCode());
            attTransactionItem.setAreaName(authAreaItem.getName());
        }
        attTransactionService.saveItem(attTransactionItem);
        // 实时点名推送
        attRealTimePushService.pushTransaction(Collections.singletonList(attTransactionItem));
    }

    @Deprecated
    private void pushAccTransaction(Att4OtherTransactionItem transactionItem) {
        AttTransactionItem attTransactionItem = new AttTransactionItem();
        // add by jinxian.huang 2019-04-19
        // 由于门禁人员姓名为空的时候，保存的值为“”，其他模块都存为null值，所以为了统一，做以下操作
        if (StringUtils.isBlank(transactionItem.getPersonName())) {
            transactionItem.setPersonName(null);
        }
        if (StringUtils.isBlank(transactionItem.getPersonLastName())) {
            transactionItem.setPersonLastName(null);
        }
        // 如果人员没有考勤区域则不推送记录到考勤原始记录表
        // updated by jinxian.huang 2019-04-26
        AttPersonItem attPersonItem = attPersonService.getItemByPersonPin(transactionItem.getPersonPin());
        if (Objects.nonNull(attPersonItem)) {

            ModelUtil.copyPropertiesIgnoreNull(transactionItem, attTransactionItem);
            attTransactionItem.setDeptId(attPersonItem.getDeptId());
            attTransactionItem.setDeptCode(attPersonItem.getDeptCode());
            attTransactionItem.setDeptName(attPersonItem.getDeptName());

            attTransactionItem.setMark(ConstUtil.SYSTEM_MODULE_ACC);

            AttPointItem attPointItem = attPointService.getItemByDeviceSnAndDoorNo(attTransactionItem.getDeviceSn(),
                    attTransactionItem.getDoorNo());
            if (Objects.nonNull(attPointItem)) {
                // 设备名称
                attTransactionItem.setDeviceName(attPointItem.getDeviceName());
                // 添加区域数据信息
                String areaId = attPointItem.getAreaId();
                AuthAreaItem authAreaItem = authAreaService.getItemById(areaId);
                if (Objects.nonNull(authAreaItem)) {
                    attTransactionItem.setAreaId(authAreaItem.getId());
                    attTransactionItem.setAreaNo(authAreaItem.getCode());
                    attTransactionItem.setAreaName(authAreaItem.getName());
                }
                attTransactionService.saveItem(attTransactionItem);
                // 实时点名推送
                attRealTimePushService.pushTransaction(Collections.singletonList(attTransactionItem));
            }

        }
    }

    @Deprecated
    private void pushParkTransaction(Att4OtherTransactionItem transactionItem) {
        AttTransactionItem attTransactionItem = new AttTransactionItem();
        // 如果人员没有考勤区域则不推送记录到考勤原始记录表
        // updated by jinxian.huang 2019-04-26
        AttPersonItem attPersonItem = attPersonService.getItemByPersonPin(transactionItem.getPersonPin());
        if (Objects.nonNull(attPersonItem)) {

            ModelUtil.copyPropertiesIgnoreNull(transactionItem, attTransactionItem);
            attTransactionItem.setDeptId(attPersonItem.getDeptId());
            attTransactionItem.setDeptCode(attPersonItem.getDeptCode());
            attTransactionItem.setDeptName(attPersonItem.getDeptName());

            attTransactionItem.setMark(ConstUtil.SYSTEM_MODULE_PARK);
            AttPointItem attPointItem = attPointService.getItemByDeviceId(transactionItem.getDeviceId());
            if (Objects.nonNull(attPointItem)) {
                // 填充设备名称
                attTransactionItem.setDeviceName(attPointItem.getDeviceName());
                // add by jinxian.huang 2019-04-18
                // 添加停车场考勤区域数据信息
                String areaId = attPointItem.getAreaId();
                Att4ParkEntranceArea parkEntranceArea = attGetParkDeviceAreaService.getParkEntranceAreaById(areaId);
                if (Objects.nonNull(parkEntranceArea)) {
                    attTransactionItem.setAreaId(parkEntranceArea.getId());
                    attTransactionItem.setAreaName(parkEntranceArea.getName());
                }
                attTransactionService.saveItem(attTransactionItem);
                // 实时点名推送
                attRealTimePushService.pushTransaction(Collections.singletonList(attTransactionItem));
            }

        }
    }

    @Deprecated
    private void pushInsTransaction(Att4OtherTransactionItem transactionItem) {
        AttTransactionItem attTransactionItem = new AttTransactionItem();
        // 如果人员没有考勤区域则不推送记录到考勤原始记录表
        // updated by jinxian.huang 2019-04-26
        AttPersonItem attPersonItem = attPersonService.getItemByPersonPin(transactionItem.getPersonPin());
        if (Objects.nonNull(attPersonItem)) {
            ModelUtil.copyPropertiesIgnoreNull(transactionItem, attTransactionItem);
            attTransactionItem.setDeptId(attPersonItem.getDeptId());
            attTransactionItem.setDeptCode(attPersonItem.getDeptCode());
            attTransactionItem.setDeptName(attPersonItem.getDeptName());

            attTransactionItem.setMark(ConstUtil.SYSTEM_MODULE_INS);

            AttPointItem attPointItem = attPointService.getItemByDeviceId(transactionItem.getDeviceId());
            if (Objects.nonNull(attPointItem)) {
                attTransactionItem.setDeviceName(attPointItem.getDeviceName());
                // 添加区域数据信息
                String areaId = attPointItem.getAreaId();
                AuthAreaItem authAreaItem = authAreaService.getItemById(areaId);
                if (Objects.nonNull(authAreaItem)) {
                    attTransactionItem.setAreaId(authAreaItem.getId());
                    attTransactionItem.setAreaNo(authAreaItem.getCode());
                    attTransactionItem.setAreaName(authAreaItem.getName());
                }
                attTransactionService.saveItem(attTransactionItem);
                // 实时点名推送
                attRealTimePushService.pushTransaction(Collections.singletonList(attTransactionItem));
            }

        }
    }

    @Deprecated
    private void pushPidTransaction(Att4OtherTransactionItem transactionItem) {
        AttTransactionItem attTransactionItem = new AttTransactionItem();
        // lastName没有值 默认Null 查询考勤打卡记录表会有问题 updated by jinxian.huang 2019-07-17
        if (StringUtils.isBlank(transactionItem.getPersonLastName())) {
            transactionItem.setPersonLastName("");
        }
        // 如果人员没有考勤区域则不推送记录到考勤原始记录表
        // updated by jinxian.huang 2019-04-26
        AttPersonItem attPersonItem = attPersonService.getItemByPersonPin(transactionItem.getPersonPin());
        if (Objects.nonNull(attPersonItem)) {
            ModelUtil.copyPropertiesIgnoreNull(transactionItem, attTransactionItem);
            attTransactionItem.setDeptId(attPersonItem.getDeptId());
            attTransactionItem.setDeptCode(attPersonItem.getDeptCode());
            attTransactionItem.setDeptName(attPersonItem.getDeptName());

            attTransactionItem.setMark(ConstUtil.SYSTEM_MODULE_IDENTIFICATION);

            AttPointItem attPointItem = attPointService.getItemByDeviceSn(transactionItem.getDeviceSn());
            if (Objects.nonNull(attPointItem)) {
                // 设置设备id设备id
                attTransactionItem.setDeviceId(attPointItem.getDeviceId());
                attTransactionItem.setDeviceName(attPointItem.getDeviceName());
                // 添加区域数据信息 add by jinxian.huang 2019-03-20
                String areaId = attPointItem.getAreaId();
                AuthAreaItem authAreaItem = authAreaService.getItemById(areaId);
                if (Objects.nonNull(authAreaItem)) {
                    attTransactionItem.setAreaId(authAreaItem.getId());
                    attTransactionItem.setAreaNo(authAreaItem.getCode());
                    attTransactionItem.setAreaName(authAreaItem.getName());
                }
                attTransactionService.saveItem(attTransactionItem);
                // 实时点名推送
                attRealTimePushService.pushTransaction(Collections.singletonList(attTransactionItem));
            }

        }
    }

    @Override
    public void pushAccTransactions(Acc4AttTransactionExternalItem transactionItem) {
        Att4OtherTransactionItem item = ModelUtil.copyProperties(transactionItem, new Att4OtherTransactionItem());
        item.setModuleName(ConstUtil.SYSTEM_MODULE_ACC);
        pushAccTransaction(item);
    }

    @Override
    public void pushParkTransactions(Park4AttTransactionItem transactionItem) {
        Att4OtherTransactionItem item = ModelUtil.copyProperties(transactionItem, new Att4OtherTransactionItem());
        item.setModuleName(ConstUtil.SYSTEM_MODULE_PARK);
        pushParkTransaction(item);
    }

    @Override
    public void pushInsTransactions(Ins4AttTransactionItem transactionItem) {
        Att4OtherTransactionItem item = ModelUtil.copyProperties(transactionItem, new Att4OtherTransactionItem());
        item.setModuleName(ConstUtil.SYSTEM_MODULE_INS);
        pushInsTransaction(item);
    }

    @Override
    public void pushPidTransactions(Pid4AttTransactionItem transactionItem) {
        Att4OtherTransactionItem item = ModelUtil.copyProperties(transactionItem, new Att4OtherTransactionItem());
        item.setModuleName(ConstUtil.SYSTEM_MODULE_IDENTIFICATION);
        pushPidTransaction(item);
    }


    /**
     * @param pin
     * @param outTime 门禁记录外出开始时间
     * @param inTime  门禁进入
     * @Description: 根据pin获取人员当天排班记录
     * @return:
     * @Author:
     * @date: 2025/7/22 11:36
     */

    public List<Acc4AttIsExceptionTimeItem> isExceptionRecord(String pin, Date outTime, Date inTime) {

        PersPersonItem personItem = persPersonService.getItemByPin(pin);
        SimpleDateFormat sdf_yy = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf_yh = new SimpleDateFormat("yyyy-MM-ddHH:mm:ss");

        boolean TimingCheck=false;//是否是自动检查
        if(inTime==null){
            inTime=new Date();
            TimingCheck=true;
        }
        String yyyyMMssNowDate = sdf_yy.format(new Date());
        List<String> ids = (List<String>) CollectionUtil.strToList(personItem.getId());
//        attPersonSchService.getPersonWorkTime(personItem.getId(), yyyyMMssNowDate);
        List<Acc4AttIsExceptionTimeItem> acc4AttIsExceptionTimeItems = new LinkedList<>();
        Date queryDate =new Date();
        try {
             queryDate =sdf_yh.parse(yyyyMMssNowDate + " " + "00:00:00");
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        List<AttCycleSch> attCycleSchList = attCycleSchDao.findByPersonIdInAndStartDateAndEndDate(ids, queryDate, queryDate);
        log.info("是否获取到人员排班 {}",!attCycleSchList.isEmpty());
        if (!attCycleSchList.isEmpty()) {
            AttCycleSch attCycleSch = attCycleSchList.get(0);
            Set<AttShift> attShiftSet = attCycleSch.getAttShiftSet();
            List<AttShift> attShiftList = new LinkedList<>(attShiftSet);
            AttShift attShift = attShiftList.get(0);
            Set<AttTimeSlot> attTimeSlotSet = attShift.getAttTimeSlotSet();

            log.info("排班时间段 {}",attTimeSlotSet);
            for (AttTimeSlot attTimeSlot : attTimeSlotSet) {

                //获取得休息时间段

                Set<AttBreakTime> attBreakTimeSet = attTimeSlot.getAttBreakTimeSet();
                List<AttBreakTime> attBreakTimeList;
                AttBreakTime attBreakTime=null;
                if(attTimeSlot.getIsSegmentDeduction()){
                    if(!attBreakTimeSet.isEmpty()){
                        //不存在休息时间段
                        attBreakTimeList = new LinkedList<>(attBreakTimeSet);
                        attBreakTime = attBreakTimeList.get(0);
                    }
                }



                //工作时间段的开始时间和结束时间
                Date workStart = new Date();
                Date workEnd = new Date();

                //休息时间段的开始时间和结束时间
                Date attBreakTimeStart = null;
                Date attBreakTimeEnd = null;
                //签到时间
                Date signStart = new Date();
                Date signTimeEnd = new Date();
                try {
                    if(attBreakTime!=null){
                        attBreakTimeStart = sdf_yh.parse(yyyyMMssNowDate + " " + attBreakTime.getStartTime()+":00");
                        attBreakTimeEnd = sdf_yh.parse(yyyyMMssNowDate + " " + attBreakTime.getEndTime()+":00");
                    }
                    workStart = sdf_yh.parse(yyyyMMssNowDate + " " + attTimeSlot.getToWorkTime()+":00");
                    workEnd = sdf_yh.parse(yyyyMMssNowDate + " " + attTimeSlot.getOffWorkTime()+":00");
                    signStart = sdf_yh.parse(yyyyMMssNowDate + " " + attTimeSlot.getStartSignInTime()+":00");
                    signTimeEnd = sdf_yh.parse(yyyyMMssNowDate + " " + attTimeSlot.getEndSignOffTime()+":00");
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
                long attBreakTimeLong = 0;
                long workLong = 0;
                long attBreakTimeAndWorkLong = 0;

                if(TimingCheck){

                    inTime=new Date();
                }
                log.info("outTime.getTime() {}    signStart.getTime() {}  inTime.getTime() {} signTimeEnd.getTime() {}",outTime.getTime(),signStart.getTime(),inTime.getTime(),signTimeEnd.getTime());

                //非上班时间段内打卡，不需要考虑
                if (outTime.getTime() >= signStart.getTime() && inTime.getTime() <= signTimeEnd.getTime()) {

                    //外出是否在休息时间段内外出
                    if (attBreakTimeStart!=null&&outTime.getTime() >= attBreakTimeStart.getTime()&&outTime.getTime() <= attBreakTimeEnd.getTime()) {
                        //统计休息时长
                        if(inTime.getTime()>attBreakTimeEnd.getTime()){
                            //大于休息时间直接用休息时间。
                            attBreakTimeLong = attBreakTimeEnd.getTime() - outTime.getTime();
                        }else {
                            attBreakTimeLong = inTime.getTime() - outTime.getTime();
                        }
                    } else {
                        if (outTime.getTime() >= workStart.getTime() && (attBreakTimeStart!=null&&inTime.getTime() >= attBreakTimeStart.getTime() && inTime.getTime() <= attBreakTimeEnd.getTime())) {
                            //是在上班时间段外出  并且在休息时间段内进入
                            workLong = inTime.getTime() - outTime.getTime();


                        } else {
                            if (outTime.getTime() >= workStart.getTime() && inTime.getTime() > workEnd.getTime()) {
                                //是在上班时间段外出  并且不在休息时间段内进入
                                workLong = workEnd.getTime() - outTime.getTime();
                            }else {
                                workLong = inTime.getTime() - outTime.getTime();
                            }
                        }
                    }
                }
                Acc4AttIsExceptionTimeItem acc4AttIsExceptionTimeItem = new Acc4AttIsExceptionTimeItem();
                acc4AttIsExceptionTimeItem.setAttBreakTimeLong(attBreakTimeLong);
                acc4AttIsExceptionTimeItem.setAttBreakTimeAndWorkLong(attBreakTimeAndWorkLong);
                acc4AttIsExceptionTimeItem.setWorkLong(workLong);
                acc4AttIsExceptionTimeItems.add(acc4AttIsExceptionTimeItem);
            }
        }
        return acc4AttIsExceptionTimeItems;
    }

}