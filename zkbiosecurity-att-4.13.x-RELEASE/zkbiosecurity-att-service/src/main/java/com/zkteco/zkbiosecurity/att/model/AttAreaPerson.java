package com.zkteco.zkbiosecurity.att.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import java.io.Serializable;

/**
 * 区域人员
 */
@Entity
@Table(name = "ATT_AREA_PERSON",
    indexes = {@Index(name = "ATT_AR_CRT_IDX", columnList = "CREATE_TIME"),
        @Index(name = "ATT_AR_UPT_IDX", columnList = "UPDATE_TIME")},
    uniqueConstraints = {
        @UniqueConstraint(name = "ATT_AREA_PERSON_AREA_PERS", columnNames = {"AUTH_AREA_ID", "PERS_PERSON_ID"})})
@Setter
@Getter
@Accessors(chain = true)
public class AttArea<PERSON>erson extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 区域id
     */
    @Column(name = "AUTH_AREA_ID", length = 50)
    private String areaId;

    /**
     * 人员id
     */
    @Column(name = "PERS_PERSON_ID", length = 50)
    private String personId;
}