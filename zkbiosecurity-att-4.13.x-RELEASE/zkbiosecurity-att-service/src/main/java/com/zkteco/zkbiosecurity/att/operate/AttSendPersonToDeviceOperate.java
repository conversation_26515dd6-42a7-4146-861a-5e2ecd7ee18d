package com.zkteco.zkbiosecurity.att.operate;

import java.util.*;

import com.zkteco.zkbiosecurity.att.service.AttPersonService;
import com.zkteco.zkbiosecurity.att.vo.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.cmd.AttDevCmdManager;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttPersonDao;
import com.zkteco.zkbiosecurity.att.model.AttDevice;
import com.zkteco.zkbiosecurity.att.model.AttPerson;
import com.zkteco.zkbiosecurity.att.service.AttDeviceOptionService;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.pers.service.PersBioTemplateService;
import com.zkteco.zkbiosecurity.pers.service.PersCardService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersBioTemplateItem;
import com.zkteco.zkbiosecurity.pers.vo.PersCardItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 考勤设备上来的数据分发到其他设备
 *
 * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
 * @version V1.0
 * @date Created In 16:23 2018/10/23
 */
@Slf4j
@Component
public class AttSendPersonToDeviceOperate {

    @Autowired
    private AttDevCmdManager attDevCmdManager;
    @Autowired
    private AttDeviceOptionService attDeviceOptionService;
    @Autowired
    private PersBioTemplateService persBioTemplateService;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttPersonDao attPersonDao;
    @Autowired
    private PersCardService persCardService;
    @Autowired
    private PersPersonService persPersonService;

    /**
     * 下发人员基础信息到设备
     * 
     * @date 2018/12/6 14:18
     * @param attDeviceList
     *            设备对象集合
     * @param persPersonItemList
     *            人员信息集合
     * @return void
     */
    public void asyncSendPersonToDevHandler(List<AttDevice> attDeviceList, List<PersPersonItem> persPersonItemList) {
        if (CollectionUtil.isEmpty(attDeviceList)) {
            return;
        }
        if (CollectionUtil.isEmpty(persPersonItemList)) {
            return;
        }
        Collection<String> personIdList = CollectionUtil.getItemIdsList(persPersonItemList);
        // 获取对应的cardNo根据卡号获取主卡的信息
        List<PersCardItem> masterCardPersonIdList = persCardService.getMasterCardByPersonIdList(personIdList);
        Map<String, PersCardItem> personIdPersCardItmeMap =
            CollectionUtil.listToKeyMap(masterCardPersonIdList, PersCardItem::getPersonId);
        // 获取考勤人员授权信息
        Map<String, AttPersonItem> personIdAttPersonMap = attPersonService.getPersonIdAndAttPersonItemMap(personIdList);
        // 下发命令操作
        for (PersPersonItem personItem : persPersonItemList) {
            PersCardItem persCardItem = personIdPersCardItmeMap.get(personItem.getId());
            AttPersonItem attPerson = personIdAttPersonMap.get(personItem.getId());
            AttPersonOptItem attPersonOptItem = attDevCmdManager.buildPersonOptItem(personItem, persCardItem, attPerson);
            attDeviceList.forEach(attDevice -> {
                attDevCmdManager.sendPersonToDev(attDevice.getDevSn(), attPersonOptItem);
            });
        }
    }

    /**
     * 下发人员头像到设备
     * 
     * @date 2020/12/9 14:49
     * @param attDeviceList
     * @param personPinList
     * @return void
     */
    public void asyncSendPersonPhotoToDevHandler(List<AttDevice> attDeviceList, List<String> personPinList) {
        if (CollectionUtil.isEmpty(attDeviceList)) {
            return;
        }
        if (CollectionUtil.isEmpty(personPinList)) {
            return;
        }

        Collection<String> devIdList =
                CollectionUtil.getPropertyList(attDeviceList, AttDevice::getId, AttConstant.COMM_DEF_VALUE);
        Map<String, List<AttDeviceOptionItem>> devOptionMap = attDeviceOptionService.getDeviceIdAndDeviceOptionMap(devIdList);
        // 获取考勤人员信息
        List<AttPerson> attPersonList = attPersonDao.findByPersonPinIn(personPinList);
        Collection<String> personIdList = CollectionUtil.getPropertyList(attPersonList, AttPerson::getPersonId, "-1");
        List<PersPersonItem> persPersonItemList = persPersonService.getSimpleItemsByIds(personIdList);

        for (PersPersonItem persPersonItem : persPersonItemList) {
            AttPersonPhotoOptItem attPersonPhotoOptItem = attDevCmdManager.buildPersonPhotoOptItem(persPersonItem);
            // 命令下发的操作过程
            attDeviceList.forEach(attDevice -> {
                List<AttDeviceOptionItem> devOptionAry = devOptionMap.get(attDevice.getId());
                attDevCmdManager.sendPersonPhotoToDev(attDevice, attPersonPhotoOptItem, devOptionAry);
            });
        }
    }

    /**
     * 下发指纹到设备
     * 
     * @date 2020/12/9 15:20
     * @param attDeviceList
     * @param personPinList
     * @return void
     */
    public void asyncSendPersonFPToDevHandler(List<AttDevice> attDeviceList, List<String> personPinList) {
        if (CollectionUtil.isEmpty(attDeviceList)) {
            return;
        }
        if (CollectionUtil.isEmpty(personPinList)) {
            return;
        }

        List<AttPerson> attPersonList = attPersonDao.findByPersonPinIn(personPinList);
        Collection<String> personIdList = CollectionUtil.getPropertyList(attPersonList, AttPerson::getPersonId, "-1");

        Map<String, AttPerson> attPersonMap = CollectionUtil.listToKeyMap(attPersonList, AttPerson::getPersonId);
        List<PersBioTemplateItem> persBioTemplateItemList =
            persBioTemplateService.getItemByPersonIdList((List<String>)personIdList);

        List<AttBioTemplateItem> attBioTemplateItemList = new ArrayList<>();
        for (PersBioTemplateItem persBioTemplateItem : persBioTemplateItemList) {
            AttPerson attPerson = attPersonMap.get(persBioTemplateItem.getPersonId());
            if (BaseConstants.BaseBioType.FP_BIO_TYPE.equals(persBioTemplateItem.getBioType())) { // 指纹
                AttBioTemplateItem attBioTemplate = new AttBioTemplateItem();
                attBioTemplate.setPersonPin(attPerson.getPersonPin());
                attBioTemplate.setBioType(persBioTemplateItem.getBioType());
                attBioTemplate.setTemplateNo(persBioTemplateItem.getTemplateNo());
                attBioTemplate.setTemplateNoIndex(persBioTemplateItem.getTemplateNoIndex());
                attBioTemplate.setValidType(persBioTemplateItem.getValidType());
                attBioTemplate.setTemplate(persBioTemplateItem.getTemplate());
                attBioTemplate.setVersion(persBioTemplateItem.getVersion());
                attBioTemplate.setDuress(persBioTemplateItem.getDuress());
                attBioTemplateItemList.add(attBioTemplate);
            }
        }

        // 获取设备的参数
        Collection<String> devIdList =
                CollectionUtil.getPropertyList(attDeviceList, AttDevice::getId, AttConstant.COMM_DEF_VALUE);
        Map<String, List<AttDeviceOptionItem>> devOptionMap = attDeviceOptionService.getDeviceIdAndDeviceOptionMap(devIdList);

        // 下发人员指纹信息到设备命令
        attDeviceList.forEach(attDevice -> {
            List<AttDeviceOptionItem> devOptionAry = devOptionMap.get(attDevice.getId());
            attDevCmdManager.sendPersonFPToDev(attDevice, attBioTemplateItemList, devOptionAry);
        });
    }

    /**
     * 下发人脸模板到设备
     * 
     * @date 2020/12/10 9:45
     * @param attDeviceList
     * @param personIdList
     * @return void
     */
    public void asyncSendPersonFaceToDevHandler(List<AttDevice> attDeviceList, List<String> personIdList) {
        if (CollectionUtil.isEmpty(attDeviceList)) {
            return;
        }
        if (CollectionUtil.isEmpty(personIdList)) {
            return;
        }
        // 根据人员ID查找模块
        List<PersBioTemplateItem> persBioTemplateItemList = persBioTemplateService.getItemByPersonIdList(personIdList);
        // 面部集合
        List<AttBioTemplateItem> attBioFaceTemplateList = new ArrayList<>();
        for (PersBioTemplateItem persBioTemplateItem : persBioTemplateItemList) {
            if (BaseConstants.BaseBioType.FACE_BIO_TYPE.equals(persBioTemplateItem.getBioType())) {
                AttBioTemplateItem attBioTemplate = new AttBioTemplateItem();
                attBioTemplate.setPersonPin(persBioTemplateItem.getPersonPin());
                attBioTemplate.setBioType(persBioTemplateItem.getBioType());
                attBioTemplate.setTemplateNo(persBioTemplateItem.getTemplateNo());
                attBioTemplate.setTemplateNoIndex(persBioTemplateItem.getTemplateNoIndex());
                attBioTemplate.setValidType(persBioTemplateItem.getValidType());
                attBioTemplate.setTemplate(persBioTemplateItem.getTemplate());
                attBioTemplate.setVersion(persBioTemplateItem.getVersion());
                attBioTemplate.setDuress(persBioTemplateItem.getDuress());
                attBioFaceTemplateList.add(attBioTemplate);
            }
        }

        // 获取设备的参数
        Collection<String> devIdList =
                CollectionUtil.getPropertyList(attDeviceList, AttDevice::getId, AttConstant.COMM_DEF_VALUE);
        Map<String, List<AttDeviceOptionItem>> devOptionMap = attDeviceOptionService.getDeviceIdAndDeviceOptionMap(devIdList);

        // 下发命令操作
        attDeviceList.forEach(attDevice -> {
            List<AttDeviceOptionItem> devOptionAry = devOptionMap.get(attDevice.getId());
            // 下发人脸模板
            attDevCmdManager.sendPersonFaceToDev(attDevice, attBioFaceTemplateList, devOptionAry);
        });
    }
}
