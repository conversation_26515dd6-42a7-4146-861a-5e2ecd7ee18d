package com.zkteco.zkbiosecurity.att.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * 调整（调休补班）
 */
@Entity
@Table(name = "ATT_ADJUST")
@Getter
@Setter
@Accessors(chain = true)
public class AttAdjust extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 部门id */
    @Column(name = "AUTH_DEPT_ID", length = 50)
    private String deptId;

    /** 人员id */
    @Column(name = "PERS_PERSON_ID", length = 50)
    private String personId;

    /** 人员编号 */
    @Column(name = "PERS_PERSON_PIN", length = 30)
    private String personPin;

    /** 调整类型 */
    @Column(name = "ADJUST_TYPE")
    private Short adjustType;

    /** 调整日期 */
    @Column(name = "ADJUST_DATE")
    private Date adjustDate;

    /** 备注 */
    @Column(name = "REMARK")
    private String remark;

    /** 班次id */
    @Column(name = "SHIFT_ID", length = 50)
    private String shiftId;

    /**
     * 流程业务ID,全局唯一
     */
    @Column(name = "BUSINESS_KEY", length = 50)
    private String businessKey;

    @Column(name = "FLOW_STATUS")
    private String flowStatus;

}