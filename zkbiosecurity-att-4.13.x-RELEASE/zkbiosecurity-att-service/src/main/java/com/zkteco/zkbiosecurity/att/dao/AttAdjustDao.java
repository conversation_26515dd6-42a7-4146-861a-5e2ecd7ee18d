/**
 * File Name: AttAdjust Created by GenerationTools on 2018-02-08 下午08:27 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.att.dao;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.att.model.AttAdjust;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 对应百傲瑞达 AttAdjustDao
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-08 下午08:27
 */
public interface AttAdjustDao extends BaseDao<AttAdjust, String> {

    boolean existsByShiftIdIn(List<String> shiftIdList);

    /**
     * 根据PIN集合查询时间范围内是否有申请
     *
     * @param personIdList
     * @param startTime
     * @param endTime
     * @return
     */
    @Query(
        value = "SELECT t FROM AttAdjust t WHERE t.personId IN (?1) AND (t.adjustDate >= ?2 AND t.adjustDate <= ?3) AND t.flowStatus IN ('0','2')")
    List<AttAdjust> getByPersonIdAndTime(List<String> personIdList, Date startTime, Date endTime);

    /**
     * 根据人员编号，判断 人员的开始时间与结束时间 是否有调休补班记录
     *
     * @param personPin
     *            人员编号
     * @param startTime
     *            开始时间
     * @param endTime
     *            结束时间
     * @return com.zkteco.zkbiosecurity.att.model.AttAdjust
     * @date 2018/12/28 19:19
     */
    @Query(value = "SELECT t FROM AttAdjust t WHERE t.personPin = ?1 AND (t.adjustDate >= ?2 AND t.adjustDate <= ?3)")
    AttAdjust getByPersonPinAndTime(String personPin, Date startTime, Date endTime);

    AttAdjust findByBusinessKey(String businessKey);

    @Query("SELECT t FROM AttAdjust t WHERE t.flowStatus='2' AND t.adjustDate >= ?1 AND t.adjustDate <= ?2")
    List<AttAdjust> findAdjusByDate(Date startDate, Date endDate);

    @Query("SELECT t FROM AttAdjust t WHERE t.flowStatus='2' AND t.adjustDate >= ?1 AND t.adjustDate <= ?2 AND t.personPin = ?3")
    List<AttAdjust> findAdjusByDateAndPin(Date startDate, Date endDate, String personPin);

    /**
     * 查询人员集合时间范围申请记录
     *
     * @param startDate
     * @param endDate
     * @param pins
     * @return
     */
    @Query("SELECT t FROM AttAdjust t WHERE t.flowStatus='2' AND t.adjustDate >= ?1 AND t.adjustDate <= ?2 AND t.personPin IN (?3)")
    List<AttAdjust> findAdjusByDateAndPins(Date startDate, Date endDate, List<String> pins);

    @Modifying
    @Query("update AttAdjust t set t.flowStatus=?2 where t.id in (?1)")
    void updateFlowStatus(List<String> asList, String flowStatusComplete);
}