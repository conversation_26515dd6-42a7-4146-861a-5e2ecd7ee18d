package com.zkteco.zkbiosecurity.att.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.att.bean.AttPersPersonInfoBean;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.bean.AttCalculationLeaveLong;
import com.zkteco.zkbiosecurity.att.cache.AttCalculationCacheManager;
import com.zkteco.zkbiosecurity.att.calc.bo.AttLeaveBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttTimeSlotBO;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.constants.AttShiftConstant;
import com.zkteco.zkbiosecurity.att.dao.AttLeaveDao;
import com.zkteco.zkbiosecurity.att.dao.AttLeaveTypeDao;
import com.zkteco.zkbiosecurity.att.model.AttLeave;
import com.zkteco.zkbiosecurity.att.model.AttLeaveType;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.service.PersLeavePersonService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersLeavePersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Base64Utils;

/**
 * 请假
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 9:56
 * @since 1.0.0
 */
@Slf4j
@Service
public class AttLeaveServiceImpl implements AttLeaveService {

    @Autowired
    private AttLeaveDao attLeaveDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private AttLeaveTypeDao attLeaveTypeDao;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttWorkflowService attWorkflowService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private AttRealTimePushService attRealTimePushService;
    @Autowired
    private AttPersonSchDataService attPersonSchDataService;
    @Autowired
    private AttTimeSlotService attTimeSlotService;
    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private AttCalculationCacheManager attCalculationCacheManager;
    @Autowired
    private AttOvertimeService attOvertimeService;
    @Autowired
    private AttMessageCenterService attMessageCenterService;
    @Autowired
    private AuthUserService authUserService;

    @Override
    @Transactional
    public AttLeaveItem saveItem(String personIds, AttLeaveItem item, String sessionId) {

        // 是否有审批权限，如果有权限申请直接通过
        boolean hasApproval = checkPermission(sessionId, "att:leave:approval");

        // 根据人员ID获取人员基本信息，遍历人员批量请假
        List<PersPersonItem> persPersonList = persPersonService.getSimpleItemsByIds(StrUtil.strToList(personIds));

        // 获取人员排班详情，用于计算请假时长
        String pins = CollectionUtil.getPropertys(persPersonList, PersPersonItem::getPin);
        Map<String, List<AttPersonSchBO>> personSchDataMap =
            attPersonSchDataService.getPersonAllSchData(Arrays.asList(pins.split(",")),
                DateUtil.addDay(item.getStartDatetime(), -1), DateUtil.addDay(item.getEndDatetime(), 1));
        // 获取时间段集合，用于计算请假时长
        List<AttTimeSlotItem> attTimeSlotList = attTimeSlotService.getAllTimeSlotItem();
        Map<String, AttTimeSlotItem> attTimeSlotItemMap =
            CollectionUtil.listToKeyMap(attTimeSlotList, AttTimeSlotItem::getId);

        // 计算年假
        AttLeaveTypeItem attLeaveTypeItem = attLeaveTypeService.getItemById(item.getLeaveTypeId());
        // 年假清零发放日期
        Date calculateDate = attParamService.getAnnualLeaveCalculateDate();
        // 年假有效截止日期
        Date validEndDate = DateUtil.addYear(calculateDate, 1);

        Map<String, AttPersonItem> attPersonItemMap = new HashMap<>();
        Map<String, List<AttLeave>> pinLeaveMap = new HashMap<>();
        if ("L5".equals(attLeaveTypeItem.getLeaveTypeNo())) {
            // 获取考勤人员，用于判断年假有效开始时间
            List<AttPersonItem> attPersonItemList =
                attPersonService.getItemByPersonPins(Arrays.asList(pins.split(",")));
            attPersonItemMap = CollectionUtil.listToKeyMap(attPersonItemList, AttPersonItem::getPersonPin);
            // 查询所有年假，用于判断剩余年假
            List<AttLeave> attLeaveList = attLeaveDao.findByPersonPinAndLeaveTypeIdAndDate(
                Arrays.asList(pins.split(",")), attLeaveTypeItem.getId(), calculateDate, validEndDate);
            if (!CollectionUtil.isEmpty(attLeaveList)) {
                pinLeaveMap = attLeaveList.stream().collect(Collectors.groupingBy(AttLeave::getPersonPin));
            }
        }

        // 【实时计算】查询假种
        List<AttLeaveType> attLeaveTypes = attLeaveTypeDao.findAll();
        Map<String, AttLeaveType> attLeaveTypeMap = CollectionUtil.listToKeyMap(attLeaveTypes, AttLeaveType::getId);
        String crossDay = attParamService.getCrossDay();

        // 遍历保存计算请假时长以及保存排班
        List<AttLeave> attLeaveList = new ArrayList<>();
        for (PersPersonItem persPerson : persPersonList) {

            // 基本信息赋值
            AttLeave attLeave = new AttLeave();
            attLeave.setPersonId(persPerson.getId()).setPersonPin(persPerson.getPin()).setDeptId(persPerson.getDeptId())
                .setLeaveTypeId(item.getLeaveTypeId()).setLeaveImagePath(item.getLeaveImagePath())
                .setStartDatetime(item.getStartDatetime()).setEndDatetime(item.getEndDatetime())
                .setRemark(item.getRemark()).setFlowStatus(item.getFlowStatus());
            attLeave.setCloudImageUrl(item.getCloudImageUrl());
            attLeave.setCreaterId(item.getCreaterId());
            attLeave.setCreaterCode(item.getCreaterCode());
            attLeave.setCreaterName(item.getCreaterName());

            if (StringUtils.isBlank(attLeave.getFlowStatus())) {
                // 如果有审批权限，直接标记状态为已完成
                attLeave.setFlowStatus(hasApproval ? AttConstant.FLOW_STATUS_COMPLETE : AttConstant.FLOW_STATUS_CREATE);
            }

            // 根据排班计算请假时长，如果没有排班则请假时长为申请首尾时间
            AttCalculationLeaveLong attCalculationLeaveLong = calculationLeaveLong(persPerson.getPin(),
                item.getStartDatetime(), item.getEndDatetime(), personSchDataMap, attTimeSlotItemMap, attLeaveTypeItem);
            // 年假
            if ("L5".equals(attLeaveTypeItem.getLeaveTypeNo())) {
                // 判断是否有年假
                AttPersonItem attPersonItem = attPersonItemMap.get(persPerson.getPin());
                // 人事用导入人员的时候，考勤人员表为空，所以这边需要对attPersonItem判空处理
                if (attPersonItem != null) {
                    if (attPersonItem.getAnnualLeaveDays() != null || attPersonItem.getAnnualAdjustDays() != null) {
                        // 升级兼容，20230530之前是没有AnnualAdjustDays属性
                        Integer annualLeaveDays = attPersonItem.getAnnualAdjustDays() == null
                            ? attPersonItem.getAnnualLeaveDays() : attPersonItem.getAnnualAdjustDays();

                        Date annualValidDate = attPersonItem.getAnnualValidDate();
                        if (item.getStartDatetime().compareTo(annualValidDate) > 0
                            && item.getEndDatetime().compareTo(validEndDate) < 0) {
                            // 计算剩余年假
                            float remainingAnnualLeave = (float)annualLeaveDays;
                            // 计算当前年假结余
                            List<AttLeave> leaveList = pinLeaveMap.get(persPerson.getPin());
                            if (leaveList != null) {
                                float days = 0f;
                                for (AttLeave leave : leaveList) {
                                    if (leave.getStartDatetime().compareTo(attPersonItem.getAnnualValidDate()) > 0) {
                                        days += leave.getDays();
                                    }
                                }
                                remainingAnnualLeave = (float)annualLeaveDays - days;
                            }
                            if (remainingAnnualLeave < attCalculationLeaveLong.getDays()) {
                                // 提示年假不足
                                throw ZKBusinessException.warnException("att_annualLeave_notEnough",
                                    persPerson.getName());
                            }
                        } else {
                            // 不在年假的有效范围内
                            throw ZKBusinessException.warnException("att_annualLeave_notValidDate",
                                persPerson.getName());
                        }
                    } else if (persPerson.getHireDate() != null) {
                        // 提示没有年假
                        throw ZKBusinessException.warnException("att_annualLeave_notDays", persPerson.getName());
                    }
                }
            }

            attLeave.setLeaveLong(attCalculationLeaveLong.getLeaveMinutes());
            attLeave.setDays(attCalculationLeaveLong.getDays());
            item.setLeaveLong(attCalculationLeaveLong.getLeaveMinutes());
            item.setDays(attCalculationLeaveLong.getDays());
            item.setLeaveLongHour(Float.toString(attCalculationLeaveLong.getHour()));

            // 判断是否超过年度最大申请天数限制
            AttLeaveType attLeaveType = attLeaveTypeMap.get(attLeave.getLeaveTypeId());
            if (attLeaveType != null && attLeaveType.getEnableMaxDays() != null && attLeaveType.getEnableMaxDays()) {

                Integer maxDays = attLeaveType.getMaxDays() != null ? attLeaveType.getMaxDays() : 366;
                Date startDatetime = attLeave.getStartDatetime();
                Date endDatetime = attLeave.getEndDatetime();
                int calculateMonth = Integer.parseInt(attParamService.getAnnualLeaveCalculateMonth());
                int calculateDay = Integer.parseInt(attParamService.getAnnualLeaveCalculateDay());

                // 是否跨年
                boolean isAcross = false;
                // 以申请的开始时间年份为清零日期
                Date judgeCalculateDate =
                    AttDateUtils.setDate(AttDateUtils.getYear(startDatetime), calculateMonth, calculateDay);
                // 跨年日期
                Date acrossDate = judgeCalculateDate;
                // 开始时间小于清零日期，且开始结束时间大于清零日期，即为跨年
                if (startDatetime.compareTo(judgeCalculateDate) < 0) {
                    if (endDatetime.compareTo(judgeCalculateDate) > 0) {
                        isAcross = true;
                    }
                } else {
                    acrossDate = DateUtil.addYear(judgeCalculateDate, 1);
                    if (acrossDate.compareTo(endDatetime) < 0) {
                        isAcross = true;
                    }
                }

                if (isAcross) {
                    Float applyDays = attLeaveDao.findTotalByDateBetweenAndPin(DateUtil.addYear(acrossDate, -1),
                        acrossDate, persPerson.getPin());
                    if (applyDays == null) {
                        applyDays = 0f;
                    }
                    AttCalculationLeaveLong leaveLong = calculationLeaveLong(persPerson.getPin(),
                        item.getStartDatetime(), acrossDate, personSchDataMap, attTimeSlotItemMap, attLeaveTypeItem);
                    Float applyAllDays = applyDays + leaveLong.getDays();
                    if (applyAllDays > maxDays) {
                        // 年度申请不能超过{0}天
                        throw ZKBusinessException.warnException(
                            I18nUtil.i18nCode("att_leaveType_applyMaxDays", maxDays), persPerson.getName());
                    }

                    Float applyDaysEx = attLeaveDao.findTotalByDateBetweenAndPin(acrossDate,
                        DateUtil.addYear(acrossDate, 1), persPerson.getPin());
                    if (applyDaysEx == null) {
                        applyDaysEx = 0f;
                    }
                    AttCalculationLeaveLong leaveLongEx = calculationLeaveLong(persPerson.getPin(), acrossDate,
                        item.getEndDatetime(), personSchDataMap, attTimeSlotItemMap, attLeaveTypeItem);
                    Float applyAllDaysEx = applyDaysEx + leaveLongEx.getDays();
                    if (applyAllDaysEx > maxDays) {
                        // 年度申请不能超过{0}天
                        throw ZKBusinessException.warnException(
                            I18nUtil.i18nCode("att_leaveType_applyMaxDays", maxDays), persPerson.getName());
                    }

                } else {
                    Float applyDays = null;
                    // 申请开始时间大于清零时间，则计算清零到下一年的已存在申请
                    if (startDatetime.compareTo(judgeCalculateDate) > 0) {
                        applyDays = attLeaveDao.findTotalByDateBetweenAndPin(judgeCalculateDate,
                                DateUtil.addYear(judgeCalculateDate, 1), persPerson.getPin());
                    } else {
                        // 申请开始时间小于清零时间，则计算清零日期前一年到清零日期已存在申请
                        applyDays = attLeaveDao.findTotalByDateBetweenAndPin(DateUtil.addYear(judgeCalculateDate, -1),
                                judgeCalculateDate, persPerson.getPin());
                    }
                    if (applyDays == null) {
                        applyDays = 0f;
                    }
                    Float applyAllDays = applyDays + attCalculationLeaveLong.getDays();
                    if (applyAllDays > maxDays) {
                        // 年度申请不能超过{0}天
                        throw ZKBusinessException.warnException(
                            I18nUtil.i18nCode("att_leaveType_applyMaxDays", maxDays), persPerson.getName());
                    }
                }
            }

            attLeaveList.add(attLeave);
        }
        attLeaveDao.save(attLeaveList);

        // 员工自助保存单条记录，赋值id
        if (attLeaveList != null && attLeaveList.size() > 0) {
            item.setId(attLeaveList.get(0).getId());
            for (AttLeave attLeave : attLeaveList) {
                // 实时点名推送请假
                attRealTimePushService.pushLeave(ModelUtil.copyProperties(attLeave, new AttLeaveItem()));
                // 【实时计算】保存请假记录到缓存，新增实时计算事件
                if (hasApproval) {
                    setLeaveAndAddEventCache(attLeave, attLeaveTypeMap, crossDay);
                }
            }
        }

        return item;
    }

    public Date getAnnualLeaveCalculateDate() {
        Calendar calculateDate = Calendar.getInstance();
        calculateDate.set(Calendar.MONTH, Integer.parseInt(attParamService.getAnnualLeaveCalculateMonth()) - 1);
        calculateDate.set(Calendar.DAY_OF_MONTH, Integer.parseInt(attParamService.getAnnualLeaveCalculateDay()));
        calculateDate.set(Calendar.HOUR_OF_DAY, 0);
        calculateDate.set(Calendar.MINUTE, 0);
        calculateDate.set(Calendar.MILLISECOND, 0);
        Calendar nowDate = Calendar.getInstance();
        if (nowDate.compareTo(calculateDate) < 0) {
            calculateDate.add(Calendar.YEAR, -1);
        }
        return calculateDate.getTime();
    }

    /**
     * 根据排班计算请假时长，如果没有排班则请假时长为申请首尾时间
     *
     * @param personSchDataMap
     * @param pin
     * @param startDatetime
     * @param endDatetime
     * @return int
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/7 11:21
     */
    private AttCalculationLeaveLong calculationLeaveLong(String pin, Date startDatetime, Date endDatetime,
        Map<String, List<AttPersonSchBO>> personSchDataMap, Map<String, AttTimeSlotItem> attTimeSlotItemMap,
        AttLeaveTypeItem attLeaveTypeItem) {

        AttCalculationLeaveLong attCalculationLeaveLong = new AttCalculationLeaveLong();
        int leaveMinutesTotal = 0;

        // 日期格式
        String startDateStr = AttDateUtils.dateToStrAsShort(DateUtil.addDay(startDatetime, -1));
        String endDateStr = AttDateUtils.dateToStrAsShort(DateUtil.addDay(endDatetime, 1));

        // 日期时分秒格式
        String startDatetimeStr = AttDateUtils.dateToStrAsLong(startDatetime);
        String endDatetimeStr = AttDateUtils.dateToStrAsLong(endDatetime);

        // 精确小数点位数
        int decimal = Integer.parseInt(attParamService.getDecimal());

        // 按日遍历计算请假时长
        List<String> dayList = AttDateUtils.getBetweenDate(startDateStr, endDateStr);
        for (String dateStr : dayList) {
            String pinDate = pin + AttCalculationConstant.KEY_CONNECTOR + dateStr;
            List<AttPersonSchBO> attPersonSchBOList = personSchDataMap.get(pinDate);

            int dayLeaveMinutes = 0;
            int dayShouldMinutes = 0;

            if (!CollectionUtil.isEmpty(attPersonSchBOList)) {

                // 获取有时间段的班次
                List<AttPersonSchBO> haveTimeSlotList = new ArrayList<>();
                for (AttPersonSchBO attPersonSchBO : attPersonSchBOList) {
                    List<AttTimeSlotBO> attTimeSlotBOList = attPersonSchBO.getAttTimeSlotArray();
                    if (!CollectionUtil.isEmpty(attTimeSlotBOList)) {
                        haveTimeSlotList.add(attPersonSchBO);
                    }
                }

                if (haveTimeSlotList != null && haveTimeSlotList.size() > 0) {
                    AttPersonSchBO attPersonSchBO = haveTimeSlotList.get(0);
                    List<AttTimeSlotBO> attTimeSlotBOList = attPersonSchBO.getAttTimeSlotArray();
                    for (AttTimeSlotBO attTimeSlotBO : attTimeSlotBOList) {

                        AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());

                        // 弹性班次取开始签到、签退时间
                        String toWorkTime = attTimeSlotItem.getToWorkTime();
                        String offWorkTime = attTimeSlotItem.getOffWorkTime();
                        if (AttShiftConstant.ShiftType.FLEXIBLE_SHIFT == attTimeSlotItem.getPeriodType()) {
                            toWorkTime = attTimeSlotItem.getStartSignInTime();
                            offWorkTime = attTimeSlotItem.getEndSignOffTime();
                        }

                        String timeSlotToWorkTime = String.format("%s %s:00", attTimeSlotBO.getFirstDay(), toWorkTime);
                        String timeSlotOffWorkTime =
                            String.format("%s %s:00", attTimeSlotBO.getSecondDay(), offWorkTime);

                        if (AttShiftConstant.ShiftType.FLEXIBLE_SHIFT == attTimeSlotItem.getPeriodType()) {
                            dayShouldMinutes += AttDateUtils.getMinuteDiff(timeSlotToWorkTime, timeSlotOffWorkTime);
                        } else {
                            dayShouldMinutes += Float.valueOf(attTimeSlotItem.getWorkingHours());
                        }

                        // 判断时间段和异常申请是否有交集，没有则跳过
                        if (StringUtils.compare(endDatetimeStr, timeSlotToWorkTime) < 0
                            || StringUtils.compare(startDatetimeStr, timeSlotOffWorkTime) > 0) {
                            continue;
                        }

                        // 如果是扣除休息时间段,则申请单时长对应扣除休息时间段
                        String startTime = timeSlotToWorkTime;
                        String endTime;
                        if (attTimeSlotItem.getIsSegmentDeduction()
                            && !CollectionUtil.isEmpty(attTimeSlotItem.getAttBreakTimeItems())) {
                            List<String[]> breakTimeList = new ArrayList<>();
                            for (AttBreakTimeItem attBreakTimeItem : attTimeSlotItem.getAttBreakTimeItems()) {
                                String startDateTime = String.format("%s %s:00",
                                    (StringUtils.compare(attBreakTimeItem.getStartTime(), toWorkTime) > 0
                                        ? attTimeSlotBO.getFirstDay() : attTimeSlotBO.getSecondDay()),
                                    attBreakTimeItem.getStartTime());
                                String endDateTime = String.format("%s %s:00",
                                    (StringUtils.compare(attBreakTimeItem.getEndTime(), toWorkTime) > 0
                                        ? attTimeSlotBO.getFirstDay() : attTimeSlotBO.getSecondDay()),
                                    attBreakTimeItem.getEndTime());
                                String[] strArr = new String[] {startDateTime, endDateTime};
                                breakTimeList.add(strArr);
                            }
                            Collections.sort(breakTimeList, (o1, o2) -> StringUtils.compare(o1[0], o2[0]));
                            for (String[] breakTime : breakTimeList) {
                                endTime = breakTime[0];
                                leaveMinutesTotal += AttDateUtils.getIntersectionTime(startTime, endTime,
                                    startDatetimeStr, endDatetimeStr);
                                dayLeaveMinutes += AttDateUtils.getIntersectionTime(startTime, endTime,
                                    startDatetimeStr, endDatetimeStr);
                                startTime = breakTime[1];
                            }
                        }
                        endTime = timeSlotOffWorkTime;
                        // 计算时段和异常申请的交集即申请的有效时长
                        leaveMinutesTotal +=
                            AttDateUtils.getIntersectionTime(startTime, endTime, startDatetimeStr, endDatetimeStr);
                        dayLeaveMinutes +=
                            AttDateUtils.getIntersectionTime(startTime, endTime, startDatetimeStr, endDatetimeStr);
                    }
                }
            }
            calLeaveLongByLeaveType(attCalculationLeaveLong, dayLeaveMinutes, dayShouldMinutes, attLeaveTypeItem,
                decimal);
        }

        // 没有排班时，请假时长就是申请的首尾时间
        if (leaveMinutesTotal == 0) {
            boolean isNoSch = true;
            startDateStr = AttDateUtils.dateToStrAsShort(startDatetime);
            endDateStr = AttDateUtils.dateToStrAsShort(endDatetime);
            dayList = AttDateUtils.getBetweenDate(startDateStr, endDateStr);
            for (String dateStr : dayList) {
                String pinDate = pin + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                List<AttPersonSchBO> attPersonSchBOList = personSchDataMap.get(pinDate);
                if (!CollectionUtil.isEmpty(attPersonSchBOList)) {
                    if (!AttCalculationConstant.AttAttendStatus.NO_SCHEDULING
                        .equals(attPersonSchBOList.get(0).getAttendStatus())) {
                        isNoSch = false;
                    }
                }
            }
            // 只有申请的每天都是未排班才算未排班,时长按相减的算
            if (isNoSch) {
                int dayLeaveMinutes = AttDateUtils.getMinuteDiff(startDatetime, endDatetime);
                int dayShouldMinutes = 1440;
                calLeaveLongByLeaveType(attCalculationLeaveLong, dayLeaveMinutes, dayShouldMinutes, attLeaveTypeItem,
                    decimal);
            }
        }
        return attCalculationLeaveLong;
    }

    /**
     * 根据假种规则计算请假时长
     *
     * @param attCalculationLeaveLong:
     * @param dayLeaveMinutes:
     * @param dayShouldMinutes:
     * @param attLeaveTypeItem:
     * @param decimal:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/10/12 14:12
     * @since 1.0.0
     */
    private void calLeaveLongByLeaveType(AttCalculationLeaveLong attCalculationLeaveLong, int dayLeaveMinutes,
        int dayShouldMinutes, AttLeaveTypeItem attLeaveTypeItem, int decimal) {

        // 默认小时转化
        AttLeaveTypeItem defaultLeaveTypeHour = new AttLeaveTypeItem();
        defaultLeaveTypeHour.setConvertUnit(AttConstant.ATT_CONVERT_UNIT_HOUR);
        defaultLeaveTypeHour.setConvertType(AttConstant.ATT_CONVERT_ROUNDING);
        defaultLeaveTypeHour.setConvertCount(0.1);

        // 默认天数转化
        AttLeaveTypeItem defaultLeaveTypeDay = new AttLeaveTypeItem();
        defaultLeaveTypeDay.setConvertUnit(AttConstant.ATT_CONVERT_UNIT_DAY);
        defaultLeaveTypeDay.setConvertType(AttConstant.ATT_CONVERT_ROUNDING);
        defaultLeaveTypeDay.setConvertCount(0.1);

        String daysStr = "0";
        String hoursStr = "0";
        String minutesStr = "0";
        Integer minute = 0;
        switch (attLeaveTypeItem.getConvertUnit()) {
            case AttConstant.ATT_CONVERT_UNIT_MINUTE:
                minutesStr = AttCommonUtils.convertMinute(dayLeaveMinutes, dayShouldMinutes, attLeaveTypeItem, decimal);
                minute = (int)Math.ceil(Float.valueOf(minutesStr));
                hoursStr = AttCommonUtils.convertMinute(minute, dayShouldMinutes, defaultLeaveTypeHour, decimal);
                daysStr = AttCommonUtils.convertMinute(minute, dayShouldMinutes, defaultLeaveTypeDay, decimal);
                break;
            case AttConstant.ATT_CONVERT_UNIT_HOUR:
                hoursStr = AttCommonUtils.convertMinute(dayLeaveMinutes, dayShouldMinutes, attLeaveTypeItem, decimal);
                Float hoursFloat = Float.valueOf(hoursStr);
                minutesStr = Math.ceil(60 * hoursFloat) + "";
                minute = (int)Math.ceil(Float.valueOf(minutesStr));
                daysStr = AttCommonUtils.convertMinute(minute, dayShouldMinutes, defaultLeaveTypeDay, decimal);
                break;
            case AttConstant.ATT_CONVERT_UNIT_DAY:
                daysStr = AttCommonUtils.convertMinute(dayLeaveMinutes, dayShouldMinutes, attLeaveTypeItem, decimal);
                Float daysFloat = Float.valueOf(daysStr);
                minutesStr = Math.ceil(dayShouldMinutes * daysFloat) + "";
                minute = (int)Math.ceil(Float.valueOf(minutesStr));
                hoursStr = AttCommonUtils.convertMinute(minute, dayShouldMinutes, defaultLeaveTypeHour, decimal);
                break;
            default:

        }
        attCalculationLeaveLong.setLeaveMinutes(attCalculationLeaveLong.getLeaveMinutes() + minute);
        attCalculationLeaveLong.setHour(attCalculationLeaveLong.getHour() + Float.valueOf(hoursStr));
        attCalculationLeaveLong.setDays(attCalculationLeaveLong.getDays() + Float.valueOf(daysStr));
    }

    @Override
    public AttLeaveItem saveItem(AttLeaveItem item) {
        return item;
    }

    @Override
    public List<AttLeaveItem> getByCondition(AttLeaveItem condition) {
        return (List<AttLeaveItem>)attLeaveDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    private void buildCondition(String sessionId, AttLeaveItem condition) {

        // 使用员工自助登录的用户,只有自己数据的权限。若登录类型为pers,则返回pin加入查询sql
        String persperPin = attPersonService.getPinByLoginType(sessionId);
        if (persperPin != null) {
            condition.setPersonPinEq(persperPin);
        } else {
            // 部门权限过滤
            String userId = authUserService.getUserIdBySessionId(sessionId);
            if (StringUtils.isNotBlank(userId)) {
                condition.setUserId(userId);
            }
            // 前端是否包含上下级
            if (ZKConstant.TRUE.equals(condition.getIsIncludeLower())) {
                if (StringUtils.isBlank(condition.getInDeptId())) {
                    if (StringUtils.isNotBlank(condition.getDeptId())) {
                        List<String> deptIdList = authDepartmentService.getChildDeptIdsByDeptIds(condition.getDeptId());
                        condition.setInDeptId(StrUtil.collectionToStr(deptIdList));
                        condition.setDeptId(null);
                    }
                }
            }
            // 人员姓名模糊查询（包含离职人员）
            String pins = attPersonService.getPinsByLikeName(sessionId, condition.getLikeName());
            if (StringUtils.isNotBlank(pins)) {
                condition.setInPersonPin(pins);
            }
        }
    }

    /**
     * 构建item数据
     *
     * @param itemList
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/3/27 10:10
     */
    private void buildItem(List<AttLeaveItem> itemList) {

        List<List<AttLeaveItem>> splitItemList = CollectionUtil.split(itemList, CollectionUtil.splitSize);
        for (List<AttLeaveItem> splitItems : splitItemList) {
            Collection<String> personPinList =
                CollectionUtil.getPropertyList(splitItems, AttLeaveItem::getPersonPin, "-1");
            Map<String, AttPersPersonInfoBean> persPersonInfoMap =
                attPersonService.getPersonInfoByPinList(personPinList);

            for (AttLeaveItem item : splitItems) {
                String personPin = item.getPersonPin();
                AttPersPersonInfoBean persLeavePersonItem = persPersonInfoMap.get(personPin);
                if (Objects.nonNull(persLeavePersonItem)) {
                    item.setPersonName(persLeavePersonItem.getPersonName());
                    item.setPersonLastName(persLeavePersonItem.getPersonLastName());
                }
                if (Objects.nonNull(item.getLeaveLong())) {
                    float leaveLongHour = ((float)item.getLeaveLong()) / 60;
                    double result = new BigDecimal(leaveLongHour).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
                    item.setLeaveLongHour(String.valueOf(result));
                }
            }
        }
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        Pager pager = attLeaveDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        buildItem((List<AttLeaveItem>)pager.getData());
        return pager;
    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, AttLeaveItem condition, int pageNo, int pageSize) {
        buildCondition(sessionId, condition);
        Pager pager =
            attLeaveDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        buildItem((List<AttLeaveItem>)pager.getData());
        return pager;
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            List<AttLeave> attLeaveList = attLeaveDao.findByIdList(CollectionUtil.strToList(ids));

            String crossDay = attParamService.getCrossDay();
            for (AttLeave attLeave : attLeaveList) {
                attLeaveDao.delete(attLeave);
                if (StringUtils.isNotBlank(attLeave.getBusinessKey())) {
                    attWorkflowService.deleteProcessInstance(attLeave.getBusinessKey());
                }

                // 【实时计算】删除缓存请假记录，新增实时计算事件
                if (AttConstant.FLOW_STATUS_COMPLETE.equals(attLeave.getFlowStatus())) {
                    delLeaveAndAddEventCache(attLeave, crossDay);
                }
            }
        }
        return false;
    }

    @Override
    public AttLeaveItem getItemById(String id) {
        List<AttLeaveItem> items = getByCondition(new AttLeaveItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public List<AttLeaveItem> getItemData(String sessionId, AttLeaveItem attLeaveItem, int beginIndex, int endIndex) {
        buildCondition(sessionId, attLeaveItem);
        List<AttLeaveItem> list = attLeaveDao.getItemsDataBySql(attLeaveItem.getClass(),
            SQLUtil.getSqlByItem(attLeaveItem), beginIndex, endIndex, true);
        buildItem(list);
        return list;
    }

    @Override
    public boolean existLeave(List<String> personIdList, Date startTime, Date endTime) {
        List<AttLeave> attLeaveList = attLeaveDao.getByPersonIdsAndStartEndTime(personIdList, startTime, endTime);
        if (attLeaveList != null && attLeaveList.size() > 0) {
            return true;
        }
        return false;
    }

    @Override
    public boolean existLeave(String pin, Date startTime, Date endTime) {
        List<AttLeave> attLeaveList = attLeaveDao.getByPinAndStartEndTime(pin, startTime, endTime);
        return attLeaveList != null && attLeaveList.size() > 0;
    }

    @Override
    @Transactional
    public void handlerTransfer(List<AttLeaveItem> attLeaveItems) {
        List<AttLeave> attLeaveList = new ArrayList<>();
        for (AttLeaveItem attLeaveItem : attLeaveItems) {
            String personPin = attLeaveItem.getPersonPin();
            AttLeave attLeave = attLeaveDao.getByPersonPinStartDateAndEndDate(personPin,
                attLeaveItem.getStartDatetime(), attLeaveItem.getEndDatetime());
            if (Objects.isNull(attLeave)) {
                attLeave = new AttLeave();
                // 流程状态默认设置为2——已完成
                attLeave.setFlowStatus("2");
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(attLeaveItem, attLeave, "id");
            if (StringUtils.isNotBlank(attLeaveItem.getLeaveImagePath())) {
                Date date = new Date();
                String imagePath = FileUtil.saveFileToServer("att", "leave/image", date.getTime() + ".jpg",
                    attLeaveItem.getLeaveImagePath());
                attLeave.setLeaveImagePath(imagePath);
            }
            PersPersonItem persPerson = persPersonService.getItemByPin(personPin);
            if (Objects.nonNull(persPerson)) {
                attLeave.setPersonId(persPerson.getId());
                attLeave.setPersonPin(persPerson.getPin());
                attLeave.setDeptId(persPerson.getDeptId());
            } else {
                // 去离职里面去找
                PersLeavePersonItem persLeavePersonItem = persLeavePersonService.getItemByPin(personPin);
                if (Objects.nonNull(persLeavePersonItem)) {
                    attLeave.setPersonPin(persLeavePersonItem.getPin());
                    attLeave.setDeptId(persLeavePersonItem.getDeptId());
                }
            }

            String leaveTypeNo = attLeaveItem.getLeaveTypeNo();
            AttLeaveType attLeaveType = attLeaveTypeDao.findByLeaveTypeNo(leaveTypeNo);
            if (Objects.isNull(attLeaveType)) {
                log.error("AttLeaveItem Move Fail ! LeaveTypeNo is not Exist! " + leaveTypeNo);
                continue;
            }
            attLeave.setLeaveTypeId(attLeaveType.getId());
            attLeaveList.add(attLeave);
        }
        attLeaveDao.saveAll(attLeaveList);
    }

    @Override
    @Transactional
    public void updateFlowStatus(String businessKey, String status) {
        AttLeave attLeave = attLeaveDao.findByBusinessKey(businessKey);

        // 【实时计算】 根据异常状态判断是否需要更新缓存和进行考勤实时计算
        boolean isCalculation = attCalculationCacheManager.judgeStatusCalculation(attLeave.getFlowStatus(), status);

        attLeave.setFlowStatus(status);
        attLeaveDao.save(attLeave);

        // 实时点名推送请假
        attRealTimePushService.pushLeave(ModelUtil.copyProperties(attLeave, new AttLeaveItem()));

        // 【实时计算】保存\删除缓存的请假、新增实时计算事件
        if (isCalculation) {
            List<AttLeaveType> attLeaveTypes = attLeaveTypeDao.findAll();
            Map<String, AttLeaveType> attLeaveTypeMap = CollectionUtil.listToKeyMap(attLeaveTypes, AttLeaveType::getId);
            String crossDay = attParamService.getCrossDay();
            if (AttConstant.FLOW_STATUS_COMPLETE.equals(attLeave.getFlowStatus())) {
                // 保存请假记录到缓存，新增实时计算事件
                setLeaveAndAddEventCache(attLeave, attLeaveTypeMap, crossDay);
            } else {
                // 删除缓存请假记录，新增实时计算事件
                delLeaveAndAddEventCache(attLeave, crossDay);
            }
        }
    }

    @Override
    public AttLeaveItem getByBusinessKey(String businessKey) {
        AttLeave attLeave = attLeaveDao.findByBusinessKey(businessKey);
        if (attLeave != null) {
            AttLeaveItem attLeaveItem = ModelUtil.copyPropertiesIgnoreNull(attLeave, new AttLeaveItem());
            AttLeaveTypeItem attLeaveTypeItem = attLeaveTypeService.getItemById(attLeave.getLeaveTypeId());
            if (attLeaveTypeItem != null) {
                // 设置假类信息
                attLeaveItem.setLeaveTypeNo(attLeaveTypeItem.getLeaveTypeNo());
                attLeaveItem.setLeaveTypeName(attLeaveTypeItem.getLeaveTypeName());
            }
            String hours = attParamService.minutesToHourFormat(new BigDecimal(attLeaveItem.getLeaveLong()));
            attLeaveItem.setLeaveLongHour(hours);
            attLeaveItem.setOperateDatetime(attLeave.getCreateTime());

            AttPersonItem attPersonItem = attPersonService.getItemByPersonPin(attLeaveItem.getPersonPin());
            if (Objects.nonNull(attPersonItem)) {
                attLeaveItem.setPersonName(attPersonItem.getPersonName());
                attLeaveItem.setPersonLastName(attPersonItem.getPersonLastName());
            }
            return attLeaveItem;
        }
        return null;
    }

    @Override
    public int calLeaveTimeMinutes(String personId, Date startDatetime, Date endDatetime,
        AttLeaveTypeItem attLeaveTypeItem) {

        // 校验开始时间不能大于结束时间
        if (startDatetime.after(endDatetime)) {
            throw new ZKBusinessException(I18nUtil.i18nCode("att_leave_endNoLessAndEqualStart"));
        }

        PersPersonItem persPersonItem = persPersonService.getSimpleItemById(personId);
        String pin = persPersonItem.getPin();
        // 获取人员排班详情
        Map<String, List<AttPersonSchBO>> personSchDataMap = attPersonSchDataService.getPersonAllSchData(
            Arrays.asList(pin.split(",")), DateUtil.addDay(startDatetime, -1), DateUtil.addDay(endDatetime, 1));
        // 获取时间段集合
        List<AttTimeSlotItem> attTimeSlotList = attTimeSlotService.getAllTimeSlotItem();
        Map<String, AttTimeSlotItem> attTimeSlotItemMap =
            CollectionUtil.listToKeyMap(attTimeSlotList, AttTimeSlotItem::getId);

        AttCalculationLeaveLong attCalculationLeaveLong = calculationLeaveLong(pin, startDatetime, endDatetime,
            personSchDataMap, attTimeSlotItemMap, attLeaveTypeItem);

        return attCalculationLeaveLong.getLeaveMinutes();
    }

    @Override
    @Transactional
    public void updateBusinessKeyById(String id, String businessKey) {
        AttLeave attLeave = attLeaveDao.findById(id).orElse(null);
        if (attLeave != null) {
            attLeave.setBusinessKey(businessKey);
        }
    }

    @Override
    public List<AttLeaveItem> getItemListByTimeAndPersPins(Date beginDate, Date endDate,
        Collection<String> attPersPins) {
        List<Object[]> attPersLeaveList = attLeaveDao.getItemByTimeAndPersPins(beginDate, endDate, attPersPins);
        ArrayList<AttLeaveItem> attPersLeaveItemList = new ArrayList<AttLeaveItem>();
        if (attPersLeaveList.size() > 0) {
            for (Object[] objects : attPersLeaveList) {
                AttLeaveItem attPersLeaveItem = new AttLeaveItem();
                attPersLeaveItem.setPersonPin((String)objects[0]);
                attPersLeaveItem.setStartDatetime((Date)objects[1]);
                attPersLeaveItem.setEndDatetime((Date)objects[2]);
                attPersLeaveItem.setLeaveTypeId((String)objects[3]);
                attPersLeaveItemList.add(attPersLeaveItem);
            }
        }
        return attPersLeaveItemList;
    }

    @Override
    @Transactional
    public ZKResultMsg importExcel(List<AttLeaveItem> itemList, String sessionId) {

        if (itemList.size() > 30000) {
            // 一次性导入只能30000，超过要分批次导入
            throw ZKBusinessException.warnException("att_import_overData", itemList.size());
        }
        int importSize = itemList.size();
        int beginProgress = 20;

        // 导入的人员要求人事已存在
        Map<String, PersPersonItem> existPersPersonMap = new HashMap<>();
        // 导入的人员要求人事已存在
        Map<String, AttPersonItem> existAttPersonMap = new HashMap<>();
        // 取出待导入数据中人员的pin号
        Collection<String> importPins = CollectionUtil.getPropertyList(itemList, AttLeaveItem::getPersonPin, "-1");
        // 分批处理，一次处理800人
        List<List<String>> pinsList = CollectionUtil.split(importPins, CollectionUtil.splitSize);
        for (List<String> pins : pinsList) {
            // 根据pin号查出人事人员
            List<PersPersonItem> existPersonList = persPersonService.getItemsByPins(pins);
            if (existPersonList != null && existPersonList.size() > 0) {
                existPersPersonMap.putAll(CollectionUtil.listToKeyMap(existPersonList, PersPersonItem::getPin));
            }

            List<AttPersonItem> attPersonItemList = attPersonService.getItemByPersonPins(pins);
            if (attPersonItemList != null && attPersonItemList.size() > 0) {
                existAttPersonMap.putAll(CollectionUtil.listToKeyMap(attPersonItemList, AttPersonItem::getPersonPin));
            }
        }

        // 查出所有假种
        List<AttLeaveType> attLeaveTypeList = attLeaveTypeDao.findByMark(AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        Map<String, AttLeaveTypeItem> leaveTypeNameMap = CollectionUtil.listToKeyMap(
            ModelUtil.copyListProperties(attLeaveTypeList, AttLeaveTypeItem.class), AttLeaveTypeItem::getLeaveTypeName);
        // 年假清零发放日期
        Date calculateDate = attParamService.getAnnualLeaveCalculateDate();
        // 年假有效截止日期
        Date validEndDate = DateUtil.addYear(calculateDate, 1);

        Iterator<AttLeaveItem> itemIterator = itemList.iterator();
        // 先剔除无效数据
        while (itemIterator.hasNext()) {
            AttLeaveItem item = itemIterator.next();
            // 人员编号校验
            if (StringUtils.isBlank(item.getPersonPin())) {
                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                    ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                        I18nUtil.i18nCode("pers_import_pinNotEmpty")))));
                itemIterator.remove();
                continue;
            }
            PersPersonItem persPersonItem = existPersPersonMap.get(item.getPersonPin());
            if (Objects.isNull(persPersonItem)) {
                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                    ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                        I18nUtil.i18nCode("att_areaPerson_persNoExit")))));
                itemIterator.remove();
                continue;
            }
            // 时间空校验
            if (Objects.isNull(item.getStartDatetime())) {
                progressCache
                    .setProcess(new ProcessBean(beginProgress, beginProgress, ProcessBean.createErrorContent(I18nUtil
                        .i18nCode("pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("att_leave_startNotNull")))));
                itemIterator.remove();
                continue;
            }
            if (Objects.isNull(item.getEndDatetime())) {
                progressCache
                    .setProcess(new ProcessBean(beginProgress, beginProgress, ProcessBean.createErrorContent(I18nUtil
                        .i18nCode("pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("att_leave_endNotNull")))));
                itemIterator.remove();
                continue;
            }
            // 结束时间不能小于等于开始时间
            if (item.getEndDatetime().getTime() <= item.getStartDatetime().getTime()) {
                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                    ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                        I18nUtil.i18nCode("att_leave_endNoLessAndEqualStart")))));
                itemIterator.remove();
                continue;
            }
            // 假类名称不存在校验
            Set<String> typeNames = leaveTypeNameMap.keySet();
            if (StringUtils.isBlank(item.getLeaveTypeName()) || !typeNames.contains(item.getLeaveTypeName())) {
                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                    ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                        I18nUtil.i18nCode("att_leave_typeNameNoExsists")))));
                itemIterator.remove();
                continue;
            }

            // 请假时间重复判断
            if (existLeave(item.getPersonPin(), item.getStartDatetime(), item.getEndDatetime())) {
                progressCache
                    .setProcess(new ProcessBean(beginProgress, beginProgress, ProcessBean.createErrorContent(I18nUtil
                        .i18nCode("pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("att_apply_leaveTips")))));
                itemIterator.remove();
                continue;
            }

            // 加班时间重复判断
            if (attOvertimeService.existOvertime(item.getPersonPin(), item.getStartDatetime(), item.getEndDatetime())) {
                progressCache
                    .setProcess(new ProcessBean(beginProgress, beginProgress, ProcessBean.createErrorContent(I18nUtil
                        .i18nCode("pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("att_apply_overtimeTips")))));
                itemIterator.remove();
                continue;
            }

            AttLeaveTypeItem attLeaveTypeItem = leaveTypeNameMap.get(item.getLeaveTypeName());
            if (attLeaveTypeItem != null) {
                // 年假
                if ("L5".equals(attLeaveTypeItem.getLeaveTypeNo())) {
                    // 有入职日期
                    if (persPersonItem.getHireDate() != null) {

                        // 判断是否有年假
                        AttPersonItem attPersonItem = existAttPersonMap.get(item.getPersonPin());
                        if (Objects.isNull(persPersonItem)) {
                            continue;
                        }

                        Float annualLeaveDaysFloat = attPersonItem.getAnnualLeaveDaysFloat();
                        if (annualLeaveDaysFloat == null) {
                            if (attPersonItem.getAnnualLeaveDays() == null
                                && attPersonItem.getAnnualAdjustDays() == null) {
                                annualLeaveDaysFloat = 0f;
                            } else {
                                Integer annualLeaveDays = attPersonItem.getAnnualAdjustDays() == null
                                    ? attPersonItem.getAnnualLeaveDays() : attPersonItem.getAnnualAdjustDays();
                                annualLeaveDaysFloat = Float.valueOf(annualLeaveDays);
                            }
                        }

                        if (annualLeaveDaysFloat == 0) {
                            progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                                ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                                    I18nUtil.i18nCode("att_annualLeave_notDays", persPersonItem.getName())))));
                            itemIterator.remove();
                            continue;
                        }

                        // 判断年假结余
                        Date annualValidDate = attPersonItem.getAnnualValidDate();
                        if (item.getStartDatetime().compareTo(annualValidDate) > 0
                            && item.getEndDatetime().compareTo(validEndDate) < 0) {

                            AttCalculationLeaveLong attCalculationLeaveLong = calculationLeaveLong(
                                persPersonItem.getPin(), item.getStartDatetime(), item.getEndDatetime(), "L5");

                            // 计算当前年假结余
                            if (attPersonItem.getAnnualLeaveDaysFloat() == null) {
                                List<AttLeave> leaveList = attLeaveDao.findByPersonPinAndLeaveTypeIdAndDate(
                                    Arrays.asList(persPersonItem.getPin().split(",")), attLeaveTypeItem.getId(),
                                    calculateDate, validEndDate);
                                if (leaveList != null) {
                                    float days = 0f;
                                    for (AttLeave leave : leaveList) {
                                        if (leave.getStartDatetime()
                                            .compareTo(attPersonItem.getAnnualValidDate()) > 0) {
                                            days += leave.getDays();
                                        }
                                    }
                                    Integer annualLeaveDays = attPersonItem.getAnnualAdjustDays() == null
                                        ? attPersonItem.getAnnualLeaveDays() : attPersonItem.getAnnualAdjustDays();
                                    annualLeaveDaysFloat = (float)annualLeaveDays - days;
                                }
                            } else {
                                annualLeaveDaysFloat = annualLeaveDaysFloat - attCalculationLeaveLong.getDays();
                            }

                            if (annualLeaveDaysFloat < attCalculationLeaveLong.getDays()) {
                                // 提示年假不足
                                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                                    ProcessBean.createErrorContent(I18nUtil.i18nCode("att_annualLeave_notEnough",
                                        item.getRowNum(),
                                        I18nUtil.i18nCode("att_annualLeave_notValidDate", persPersonItem.getName())))));
                                itemIterator.remove();
                                continue;
                            }
                            attPersonItem.setAnnualLeaveDaysFloat(annualLeaveDaysFloat);

                        } else {
                            progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                                ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                                    I18nUtil.i18nCode("att_annualLeave_notValidDate", persPersonItem.getName())))));
                            itemIterator.remove();
                            continue;
                        }
                    }
                }
            }
        }

        // 是否有审批权限
        boolean hasApproval = authPermissionService.checkPermission(sessionId, "att:leave:approval");

        // 【实时计算】查询假种
        Map<String, AttLeaveType> attLeaveTypeMap = CollectionUtil.listToKeyMap(attLeaveTypeList, AttLeaveType::getId);

        // 剩下的可以插入数据库,分批处理，一次处理800条
        List<List<AttLeaveItem>> attLeaveInsertList = CollectionUtil.split(itemList, CollectionUtil.splitSize);
        for (List<AttLeaveItem> insertItemList : attLeaveInsertList) {
            // 保存入库
            List<AttLeave> attLeaveList = new ArrayList<>();
            for (AttLeaveItem item : insertItemList) {
                PersPersonItem persPersonItem = existPersPersonMap.get(item.getPersonPin());
                if (Objects.nonNull(persPersonItem)) {
                    AttLeave attLeave = new AttLeave();
                    ModelUtil.copyPropertiesWithIgnore(item, attLeave, "id");
                    attLeave.setDeptId(persPersonItem.getDeptId());
                    attLeave.setPersonId(persPersonItem.getId());
                    AttLeaveTypeItem attLeaveTypeItem = leaveTypeNameMap.get(item.getLeaveTypeName());
                    attLeave.setLeaveTypeId(attLeaveTypeItem.getId());
                    // 计算时长
                    AttCalculationLeaveLong attCalculationLeaveLong = calculationLeaveLong(persPersonItem.getPin(),
                        item.getStartDatetime(), item.getEndDatetime(), attLeaveTypeItem.getLeaveTypeNo());
                    attLeave.setLeaveLong(attCalculationLeaveLong.getLeaveMinutes());
                    attLeave.setDays(attCalculationLeaveLong.getDays());

                    // 如果有审批权限，直接标记状态为已完成
                    attLeave
                        .setFlowStatus(hasApproval ? AttConstant.FLOW_STATUS_COMPLETE : AttConstant.FLOW_STATUS_CREATE);

                    attLeaveList.add(attLeave);
                }
            }
            attLeaveDao.saveAll(attLeaveList);

            // 【实时计算】保存请假记录到缓存，新增实时计算事件
            if (hasApproval) {
                String crossDay = attParamService.getCrossDay();
                for (AttLeave attLeave : attLeaveList) {
                    setLeaveAndAddEventCache(attLeave, attLeaveTypeMap, crossDay);
                }
            }

        }
        // 失败数量
        int faildCount = importSize - itemList.size();
        // 成功：%s 条，失败：%s 条。
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(99,
            I18nUtil.i18nCode("pers_import_result", itemList.size(), faildCount)));
        if (faildCount > 0) {
            return ZKResultMsg.failMsg();
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    @Transactional
    public void approval(String ids) {
        if (StringUtils.isNotBlank(ids)) {

            List<AttLeave> attLeaveList = attLeaveDao.findByIdList(CollectionUtil.strToList(ids));
            List<AttLeaveType> attLeaveTypes = attLeaveTypeDao.findAll();
            Map<String, AttLeaveType> attLeaveTypeMap = CollectionUtil.listToKeyMap(attLeaveTypes, AttLeaveType::getId);
            String crossDay = attParamService.getCrossDay();
            for (AttLeave attLeave : attLeaveList) {
                if (!AttConstant.FLOW_STATUS_COMPLETE.equals(attLeave.getFlowStatus())) {
                    // 保存请假记录到缓存，新增实时计算事件
                    setLeaveAndAddEventCache(attLeave, attLeaveTypeMap, crossDay);
                }
            }

            attLeaveDao.updateFlowStatus(Arrays.asList(ids.split(",")), AttConstant.FLOW_STATUS_COMPLETE);
        }
    }

    @Override
    @Transactional
    public void refuse(String ids) {
        if (StringUtils.isNotBlank(ids)) {

            // 【实时计算】删除缓存请假记录，新增实时计算事件
            List<AttLeave> attLeaveList = attLeaveDao.findByIdList(CollectionUtil.strToList(ids));
            String crossDay = attParamService.getCrossDay();
            for (AttLeave attLeave : attLeaveList) {
                if (AttConstant.FLOW_STATUS_COMPLETE.equals(attLeave.getFlowStatus())) {
                    delLeaveAndAddEventCache(attLeave, crossDay);
                }
            }

            attLeaveDao.updateFlowStatus(Arrays.asList(ids.split(",")), AttConstant.FLOW_STATUS_REFUSE);
        }
    }

    @Override
    public Map<String, List<AttLeaveBO>> getLeaveMap(List<String> pins, Date startDate, Date endDate) {

        // 查询请假申请记录
        List<AttLeave> attLeaveList = new ArrayList<>();
        if (CollectionUtil.isEmpty(pins)) {
            attLeaveList = attLeaveDao.findLeaveByDate(startDate, endDate);
        } else if (pins.size() == 1) {
            attLeaveList = attLeaveDao.findLeaveByDateAndPin(startDate, endDate, pins.get(0));
        } else {
            List<List<String>> splitPinList = CollectionUtil.split(pins, CollectionUtil.splitSize);
            for (List<String> subPinList : splitPinList) {
                List<AttLeave> subLeaveList = attLeaveDao.findLeaveByDateAndPins(startDate, endDate, subPinList);
                attLeaveList.addAll(subLeaveList);
            }
        }

        // 查询假种
        Collection<String> leaveTypeIds = CollectionUtil.getPropertyList(attLeaveList, AttLeave::getLeaveTypeId, "-1");
        List<AttLeaveType> attLeaveTypes = attLeaveTypeDao.findByIdList(leaveTypeIds);
        Map<String, AttLeaveType> attLeaveTypeMap = CollectionUtil.listToKeyMap(attLeaveTypes, AttLeaveType::getId);

        Map<String, List<AttLeaveBO>> attLeaveMap = new HashMap<>();
        for (AttLeave attLeave : attLeaveList) {
            buildLeaveMap(attLeave, attLeaveTypeMap, attLeaveMap);
        }
        return attLeaveMap;
    }

    /**
     * 组装请假记录
     */
    private void buildLeaveMap(AttLeave attLeave, Map<String, AttLeaveType> attLeaveTypeMap,
        Map<String, List<AttLeaveBO>> attLeaveMap) {
        AttLeaveType attLeaveType = attLeaveTypeMap.get(attLeave.getLeaveTypeId());
        Boolean isDeductWorkLong = attLeaveType.getIsDeductWorkLong();
        String leaveTypeNo = attLeaveType.getLeaveTypeNo();

        // 将记录拆分成天
        Date date1 = attLeave.getStartDatetime();
        Date date2;

        // 不同天则拆分
        while (!DateUtil.isSameDay(date1, attLeave.getEndDatetime())) {
            // 结束时间为第二天的 00:00:00
            date2 = DateUtil.getDayBeginTime(DateUtil.addDay(date1, 1));

            AttLeaveBO attLeaveBO = new AttLeaveBO();
            attLeaveBO.setId(attLeave.getId());
            attLeaveBO.setStartDateTime(AttDateUtils.dateToStrAsLong(date1));
            attLeaveBO.setEndDateTime(AttDateUtils.dateToStrAsLong(date2));
            attLeaveBO.setIsDeductWorkLong(isDeductWorkLong);
            attLeaveBO.setLeaveTypeNo(leaveTypeNo);
            String key =
                attLeave.getPersonPin() + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.dateToStrAsShort(date1);
            List<AttLeaveBO> attLeaveBOList = attLeaveMap.get(key);
            if (null == attLeaveBOList) {
                attLeaveBOList = new ArrayList<>();
            }
            attLeaveBOList.add(attLeaveBO);
            attLeaveMap.put(key, attLeaveBOList);

            // 日期 + 1
            date1 = DateUtil.getDayBeginTime(DateUtil.addDay(date1, 1));
        }

        date2 = attLeave.getEndDatetime();

        AttLeaveBO attLeaveBO = new AttLeaveBO();
        attLeaveBO.setId(attLeave.getId());
        attLeaveBO.setStartDateTime(AttDateUtils.dateToStrAsLong(date1));
        attLeaveBO.setEndDateTime(AttDateUtils.dateToStrAsLong(date2));
        attLeaveBO.setIsDeductWorkLong(isDeductWorkLong);
        attLeaveBO.setLeaveTypeNo(leaveTypeNo);
        String key =
            attLeave.getPersonPin() + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.dateToStrAsShort(date1);
        List<AttLeaveBO> attLeaveBOList = attLeaveMap.get(key);
        if (null == attLeaveBOList) {
            attLeaveBOList = new ArrayList<>();
        }
        attLeaveBOList.add(attLeaveBO);
        attLeaveMap.put(key, attLeaveBOList);
    }

    @Override
    public String calculateLeaveDays(AttLeaveItem item) {
        String pin = item.getPersonPin();
        Date startDatetime = item.getStartDatetime();
        Date endDatetime = item.getEndDatetime();
        // 获取人员排班详情
        Map<String, List<AttPersonSchBO>> personSchDataMap = attPersonSchDataService.getPersonAllSchData(
            Arrays.asList(pin.split(",")), DateUtil.addDay(startDatetime, -1), DateUtil.addDay(endDatetime, 1));
        // 获取时间段集合
        List<AttTimeSlotItem> attTimeSlotList = attTimeSlotService.getAllTimeSlotItem();
        Map<String, AttTimeSlotItem> attTimeSlotItemMap =
            CollectionUtil.listToKeyMap(attTimeSlotList, AttTimeSlotItem::getId);
        // 查询
        AttLeaveTypeItem attLeaveTypeItem = attLeaveTypeService.getItemById(item.getLeaveTypeId());

        AttCalculationLeaveLong attCalculationLeaveLong = calculationLeaveLong(pin, startDatetime, endDatetime,
            personSchDataMap, attTimeSlotItemMap, attLeaveTypeItem);

        return attCalculationLeaveLong.getDays() + "";
    }

    @Override
    public AttCalculationLeaveLong calculationLeaveLong(String pin, Date startDatetime, Date endDatetime,
        String businessType) {
        // 获取人员排班详情
        Map<String, List<AttPersonSchBO>> personSchDataMap = attPersonSchDataService.getPersonAllSchData(
            Arrays.asList(pin.split(",")), DateUtil.addDay(startDatetime, -1), DateUtil.addDay(endDatetime, 1));
        // 获取时间段集合
        List<AttTimeSlotItem> attTimeSlotList = attTimeSlotService.getAllTimeSlotItem();
        Map<String, AttTimeSlotItem> attTimeSlotItemMap =
            CollectionUtil.listToKeyMap(attTimeSlotList, AttTimeSlotItem::getId);
        AttLeaveTypeItem attLeaveTypeItem = null;
        if (StringUtils.isBlank(businessType)) {
            // 传空默认按小时计算
            attLeaveTypeItem = new AttLeaveTypeItem();
            attLeaveTypeItem.setConvertCount(0.1);
            attLeaveTypeItem.setConvertType(AttConstant.ATT_CONVERT_ABORT);
            attLeaveTypeItem.setConvertUnit(AttConstant.ATT_CONVERT_UNIT_MINUTE);
        } else {
            attLeaveTypeItem = attLeaveTypeService.getItemByLeaveTypeNo(businessType);
        }

        AttCalculationLeaveLong attCalculationLeaveLong = calculationLeaveLong(pin, startDatetime, endDatetime,
            personSchDataMap, attTimeSlotItemMap, attLeaveTypeItem);

        return attCalculationLeaveLong;
    }

    /**
     * 【实时计算】保存请假记录到缓存，新增实时计算事件
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/31 11:13
     * @param attLeave
     * @param attLeaveTypeMap
     * @param crossDay
     * @return void
     */
    private void setLeaveAndAddEventCache(AttLeave attLeave, Map<String, AttLeaveType> attLeaveTypeMap,
        String crossDay) {

        // 发送消息
        AttLeaveItem attLeaveItem = new AttLeaveItem();
        ModelUtil.copyProperties(attLeave, attLeaveItem);
        attMessageCenterService.pushLeaveMessage(attLeaveItem);

        if (attParamService.realTimeEnable()) {
            Map<String, List<AttLeaveBO>> attLeaveMap = new HashMap<>();
            buildLeaveMap(attLeave, attLeaveTypeMap, attLeaveMap);
            AttDateUtils.forEachDay(attLeave.getStartDatetime(), attLeave.getEndDatetime(), date -> {
                // 保存在时间范围内的数据
                if (attCalculationCacheManager.judgeCacheDate(date)) {
                    String key = attLeave.getPersonPin() + "=" + AttDateUtils.dateToStrAsShort(date);
                    List<AttLeaveBO> attLeaveBOList = attLeaveMap.get(key);
                    if (attLeaveBOList != null && attLeaveBOList.size() > 0) {
                        attCalculationCacheManager.setLeave(key, attLeaveBOList, true);
                        // 新增实时计算事件
                        attCalculationCacheManager.addRealTimeEvent(attLeave.getPersonPin(), date, crossDay);
                    }
                }
            });
        }
    }

    /**
     * 【实时计算】删除缓存请假记录，新增实时计算事件
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/31 18:24
     * @param attLeave
     * @param crossDay
     * @return void
     */
    private void delLeaveAndAddEventCache(AttLeave attLeave, String crossDay) {
        if (attParamService.realTimeEnable()) {
            AttDateUtils.forEachDay(attLeave.getStartDatetime(), attLeave.getEndDatetime(), date -> {
                if (attCalculationCacheManager.judgeCacheDate(date)) {
                    // 删除缓存请假记录
                    attCalculationCacheManager.delLeave(attLeave.getPersonPin(), attLeave.getId(), date);
                    // 新增实时计算事件
                    attCalculationCacheManager.addRealTimeEvent(attLeave.getPersonPin(), date, crossDay);
                }
            });
        }
    }

    @Override
    @Transactional
    public void handlerTransferZKTime(List<AttLeaveItem> attLeaveItems) {
        List<AttLeave> attLeaveList = new ArrayList<>();
        for (AttLeaveItem attLeaveItem : attLeaveItems) {
            String personPin = attLeaveItem.getPersonPin();
            AttLeave attLeave = attLeaveDao.getByPersonPinStartDateAndEndDate(personPin,
                attLeaveItem.getStartDatetime(), attLeaveItem.getEndDatetime());
            if (Objects.isNull(attLeave)) {
                attLeave = new AttLeave();
                // 流程状态默认设置为2——已完成
                attLeave.setFlowStatus("2");
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(attLeaveItem, attLeave, "id");

            PersPersonItem personItem = persPersonService.getItemByPin(personPin);
            if (Objects.nonNull(personItem)) {
                attLeave.setPersonId(personItem.getId());
                attLeave.setPersonPin(personItem.getPin());
                attLeave.setDeptId(personItem.getDeptId());
            } else {
                // 去离职里面去找
                PersLeavePersonItem persLeavePersonItem = persLeavePersonService.getItemByPin(personPin);
                if (Objects.nonNull(persLeavePersonItem)) {
                    attLeave.setPersonPin(persLeavePersonItem.getPin());
                    attLeave.setDeptId(persLeavePersonItem.getDeptId());
                }
            }

            String leaveTypeName = attLeaveItem.getLeaveTypeName();
            AttLeaveType attLeaveType = attLeaveTypeDao.findByLeaveTypeName(leaveTypeName);
            if (Objects.isNull(attLeaveType)) {
                log.error("AttLeaveItem Move Fail ! leaveTypeName is not Exist! " + leaveTypeName);
                continue;
            }
            attLeave.setLeaveTypeId(attLeaveType.getId());
            attLeaveList.add(attLeave);
        }
        attLeaveDao.saveAll(attLeaveList);
    }

    @Override
    public boolean hasStaffApply(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            List<AttLeave> list = attLeaveDao.findByIdList(CollectionUtil.strToList(ids));
            if (!CollectionUtil.isEmpty(list)) {
                for (AttLeave attLeave : list) {
                    if (StringUtils.isNotBlank(attLeave.getBusinessKey())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    public ZKResultMsg getLeaveLongByType(String pin, Date startDatetime, Date endDatetime, String businessType) {

        AttLeaveTypeItem attLeaveTypeItem = null;
        if (StringUtils.isBlank(businessType)) {
            // 传空默认按小时计算
            attLeaveTypeItem = new AttLeaveTypeItem();
            attLeaveTypeItem.setConvertCount(0.1);
            attLeaveTypeItem.setConvertType(AttConstant.ATT_CONVERT_ABORT);
            attLeaveTypeItem.setConvertUnit(AttConstant.ATT_CONVERT_UNIT_MINUTE);
        } else {
            attLeaveTypeItem = attLeaveTypeService.getItemByLeaveTypeNo(businessType);
        }
        String unitName = AttCommonUtils.getConvertUnit(attLeaveTypeItem);
        JSONObject data = new JSONObject();
        data.put("convertUnit", attLeaveTypeItem.getConvertUnit());
        data.put("unit", unitName);
        if (AttConstant.FLOW_TYPE_OVERTIME.equals(attLeaveTypeItem.getLeaveTypeNo())) {
            // 精确小数点位数
            int decimal = Integer.parseInt(attParamService.getDecimal());
            int minuteLong = AttDateUtils.getMinuteDiff(startDatetime, endDatetime);
            String timeLong = AttCommonUtils.convertMinute(minuteLong, 1440, attLeaveTypeItem, decimal);
            data.put("timeLong", timeLong);
        } else {
            // 获取人员排班详情
            Map<String, List<AttPersonSchBO>> personSchDataMap = attPersonSchDataService.getPersonAllSchData(
                Arrays.asList(pin.split(",")), DateUtil.addDay(startDatetime, -1), DateUtil.addDay(endDatetime, 1));
            // 获取时间段集合
            List<AttTimeSlotItem> attTimeSlotList = attTimeSlotService.getAllTimeSlotItem();
            Map<String, AttTimeSlotItem> attTimeSlotItemMap =
                CollectionUtil.listToKeyMap(attTimeSlotList, AttTimeSlotItem::getId);
            AttCalculationLeaveLong attCalculationLeaveLong = calculationLeaveLong(pin, startDatetime, endDatetime,
                personSchDataMap, attTimeSlotItemMap, attLeaveTypeItem);

            switch (attLeaveTypeItem.getConvertUnit()) {
                case AttConstant.ATT_CONVERT_UNIT_MINUTE:
                    data.put("timeLong", attCalculationLeaveLong.getLeaveMinutes());
                    break;
                case AttConstant.ATT_CONVERT_UNIT_HOUR:
                    data.put("timeLong", attCalculationLeaveLong.getHour());
                    break;
                case AttConstant.ATT_CONVERT_UNIT_DAY:
                    data.put("timeLong", attCalculationLeaveLong.getDays());
                    break;
                default:
            }
        }
        return new ZKResultMsg(data);
    }

    @Override
    public ZKResultMsg existApply(String personIds, Date startTime, Date endTime) {

        // 异常申请开始时间不能小于入职时间 add by bob.liu at 20191023
        List<AttPersonItem> attPersonItemList = attPersonService.getItemByPersonIds(StrUtil.strToList(personIds));
        for (AttPersonItem attPersonItem : attPersonItemList) {
            Date hireDate = attPersonItem.getHireDate();
            if (hireDate != null && (hireDate.getTime() > startTime.getTime())) {
                return I18nUtil.i18nMsg(ZKResultMsg.failMsg("att_h5_persNoHire"));
            }
        }

        // 判断申请开始时间超过了上上个月
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -2);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date theFirstDayOfLastMonth = DateUtil.getDayBeginTime(calendar.getTime());
        if (theFirstDayOfLastMonth.compareTo(startTime) > 0) {
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("att_apply_overLastMonth"));
        }

        List<String> personIdList = Arrays.asList(personIds.split(","));

        AttLeaveItem condition = new AttLeaveItem();
        condition.setInPersonId(personIds);
        condition.setStartApplyDateTime(startTime);
        condition.setEndApplyDateTime(endTime);
        condition.setFlowStatusIn(AttConstant.FLOW_STATUS_CREATE + "," + AttConstant.FLOW_STATUS_COMPLETE);
        List<AttLeaveItem> list = getByCondition(condition);

        // 判断是否存在请假
        if (null != list && !list.isEmpty()) {
            AttLeaveItem item = list.get(0);
            if (AttCalculationConstant.AttAttendStatus.TRIP.equals(item.getLeaveTypeNo())) {
                return I18nUtil.i18nMsg(ZKResultMsg.failMsg("att_apply_tripTips"));
            } else if (AttCalculationConstant.AttAttendStatus.OUT.equals(item.getLeaveTypeNo())) {
                return I18nUtil.i18nMsg(ZKResultMsg.failMsg("att_apply_outTips"));
            } else {
                return I18nUtil.i18nMsg(ZKResultMsg.failMsg("att_apply_leaveTips"));
            }
        } else if (attOvertimeService.existOvertime(personIdList, startTime, endTime)) {
            // 判断是否存在加班
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("att_apply_overtimeTips"));
        }
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @Override
    public boolean checkPermission(String sessionId, String permission) {

        // 为空就是没有审批权限
        if (StringUtils.isBlank(sessionId)) {
            return false;
        }

        // 北向接口会传admin，默认管理员有审批权限
        if ("admin".equals(sessionId)) {
            return true;
        }

        return authPermissionService.checkPermission(sessionId, permission);
    }

    @Override
    public List<AttLeaveItem> getByPersonPinAndLeaveTypeIdAndDate(Collection<String> pinList, String leaveCode,
        Date calculateDate, Date calculateDateEnd) {
        AttLeaveType attLeaveType = attLeaveTypeDao.findByLeaveTypeNo(leaveCode);
        List<List<String>> personPinsGroup = CollectionUtil.split(pinList, CollectionUtil.splitSize);
        List<AttLeaveItem> attLeaveItemList = new ArrayList<>();
        for (List<String> pins : personPinsGroup) {
            List<AttLeave> attLeaveList = attLeaveDao.findByPersonPinAndLeaveTypeIdAndDate(pins, attLeaveType.getId(),
                calculateDate, calculateDateEnd);
            List<AttLeaveItem> attLeaveItems = ModelUtil.copyListProperties(attLeaveList, AttLeaveItem.class);
            attLeaveItemList.addAll(attLeaveItems);
        }
        return attLeaveItemList;
    }

    @Override
    public String saveCloudImage(String urlList) {
        if (StringUtils.isBlank(urlList)) {
            return null;
        }

        StringBuffer imagePathBuffer = new StringBuffer("");
        String[] urlArray = urlList.split(",");
        for (String url : urlArray) {
            String localPath = getLocalPathByUrl(url);
            if (StringUtils.isNotBlank(localPath)) {
                if (imagePathBuffer.length() > 0) {
                    imagePathBuffer.append(",");
                }
                imagePathBuffer.append(localPath);
            }
        }
        return imagePathBuffer.toString();
    }

    private String getLocalPathByUrl(String pathUrl) {
        if (!pathUrl.startsWith("http")) {
            return pathUrl;
        }

        String imgBase64 = "";
        URL url = null;
        HttpURLConnection httpUrl = null;
        InputStream is = null;
        ByteArrayOutputStream outStream = null;
        try {
            url = new URL(pathUrl);
            httpUrl = (HttpURLConnection)url.openConnection();
            httpUrl.connect();
            is = httpUrl.getInputStream();
            // 图片不存在则 stream == null
            if (is != null) {
                outStream = new ByteArrayOutputStream();
                // 创建一个Buffer字符串
                byte[] buffer = new byte[1024];
                // 每次读取的字符串长度，如果为-1，代表全部读取完毕
                int len = 0;
                // 使用一个输入流从buffer里把数据读取出来
                while ((len = is.read(buffer)) != -1) {
                    // 用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
                    outStream.write(buffer, 0, len);
                }
                imgBase64 = Base64Utils.encodeToString(outStream.toByteArray());
            }
        } catch (IOException e) {
            log.error("getLocalPathByUrl error", e);
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
                if (outStream != null) {
                    outStream.close();
                }
                if (httpUrl != null) {
                    httpUrl.disconnect();
                }
            } catch (Exception e) {
                log.error("getLocalPathByUrl error", e);
            }
        }

        String filename = System.currentTimeMillis() + ".jpg";
        String leaveImageStr = FileUtils.saveFileToServer("att", "leave/image", filename, imgBase64);
        FileEncryptUtil.encryptFileByPath(leaveImageStr);

        return leaveImageStr;
    }
}
