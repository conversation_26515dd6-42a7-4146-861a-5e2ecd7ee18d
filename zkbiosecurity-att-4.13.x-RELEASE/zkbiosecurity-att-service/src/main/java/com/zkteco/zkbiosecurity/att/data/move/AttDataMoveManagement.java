// package com.zkteco.zkbiosecurity.att.data.move;
//
// import java.math.BigDecimal;
// import java.util.*;
//
// import org.apache.commons.collections.MapUtils;
// import org.apache.commons.lang3.StringUtils;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.jdbc.core.JdbcTemplate;
// import org.springframework.stereotype.Component;
//
// import com.zkteco.zkbiosecurity.att.constants.AttConstant;
// import com.zkteco.zkbiosecurity.att.dao.AttTempSchDao;
// import com.zkteco.zkbiosecurity.att.model.AttTempSch;
// import com.zkteco.zkbiosecurity.att.service.*;
// import com.zkteco.zkbiosecurity.att.vo.*;
// import com.zkteco.zkbiosecurity.base.annotation.DataMigration;
// import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
// import com.zkteco.zkbiosecurity.core.utils.*;
// import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
// import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
// import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
// import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
// import com.zkteco.zkbiosecurity.system.service.DataMoveManager;
// import com.zkteco.zkbiosecurity.system.service.SystemDataMigrationService;
// import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;
// import com.zkteco.zkbiosecurity.system.vo.SystemDataTransferProcess;
// import com.zkteco.zkbiosecurity.system.vo.SystemDataUpgradeProcess;
//
// import lombok.extern.slf4j.Slf4j;
//
/// **
// * 数据迁移类
// *
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @version V1.0
// * @date Created In 11:51 2018/12/6
// */
// @Component
// @Slf4j
// @DataMigration(order = 80)
// public class AttDataMoveManagement implements DataMoveManager {
//
// @Autowired
// private JdbcTemplate jdbcTemplate;
// @Autowired
// private SystemDataMigrationService systemDataMigrationService;
// @Autowired
// private AttExceptionDataService attExceptionDataService;
// @Autowired
// AttDeviceService attDeviceService;
// @Autowired
// AttHolidayService attHolidayService;
// @Autowired
// AttAdjustService attAdjustService;
// @Autowired
// AttGroupService attGroupService;
// @Autowired
// AttTimeSlotService attTimeSlotService;
// @Autowired
// AttRecordService attRecordService;
// @Autowired
// AttTransactionService attTransactionService;
// @Autowired
// AttTripService attTripService;
// @Autowired
// AttTempSchService attTempSchService;
// @Autowired
// AttGroupSchService attGroupSchService;
// @Autowired
// AttLeaveService attLeaveService;
// @Autowired
// AttLeaveTypeService attLeaveTypeService;
// @Autowired
// AttOvertimeService attOvertimeService;
// @Autowired
// AttPointService attPointService;
// @Autowired
// AttShiftService attShiftService;
// @Autowired
// AttSignService attSignService;
// @Autowired
// AttAreaPersonService attAreaPersonService;
// @Autowired
// AttOutService attOutService;
// @Autowired
// private ProgressCache progressCache;
// @Autowired
// private AttAccDeviceService attAccDeviceService;
// @Autowired
// private PersPersonService persPersonService;
// @Autowired
// AttPersonService attPersonService;
// @Autowired
// private BaseSysParamService baseSysParamService;
// @Autowired
// AttDeptSchService attDeptSchService;
// @Autowired
// AttClassService attClassService;
// @Autowired
// private AttTempSchDao attTempSchDao;
//
// public static final int splitSize = 10000;
//
// /** 限制条数 */
// public static final int limitSize = 500000;
//
// @Override
// public boolean handlerTransfer(SystemDataTransferProcess process) {
//
// if ("ZKTime11.0".equals(process.getSourceSoftwareVersion())) {
// handlerTransferZKTime_11_0(process);
// return true;
// }
//
// long beginTime = System.currentTimeMillis();
// int beginProgress = process.getBeginProgress();
// int endProgress = process.getEndProgress();
// // 共有23张表需要迁移
// int eachProgess = (endProgress - beginProgress) / 23;
//
// String transferProgressMsg = "start Att Module data transfer......";
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, transferProgressMsg));
// // 设备 数据迁移
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Device Data Transfer"));
// deviceDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Device Data Transfer End"));
//
// // 区域人员数据迁移
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AreaPerson Data Transfer"));
// areaPersonDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AreaPerson Data Transfer End"));
//
// // 考勤点数据迁移
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Point Data Transfer"));
// pointDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Point Data Transfer End"));
//
// // 节假日数据迁移
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Holiday Data Transfer"));
// holidayDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Holiday Data Transfer End"));
//
// // 请假类型数据迁移
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "LeaveType Data Transfer"));
// leaveTypeDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "LeaveType Data Transfer End"));
//
// // // 定时器 数据迁移
// // progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Timing Data Transfer"));
// // timingDataTransfer();
// // progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Timing Data Transfer End"));
//
// // 时间段 数据转换
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "TimeSlot Data Transfer"));
// timeSlotDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "TimeSlot Data Transfer End"));
//
// // 班次数据迁移
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Shift Data Transfer"));
// shiftDataTransfer();
// shiftTimeSlotDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Shift Data Transfer End"));
//
// // 分组 数据转换
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Group Data Transfer"));
// groupDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Group Data Transfer End"));
//
// // 分组排班 数据迁移
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Groupsch Data Transfer"));
// groupschDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Groupsch Data Transfer End"));
//
// // 涉及分组与人员的逻辑关系
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AttPerson Data Transfer"));
// attPersonDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AttPerson Data Transfer End"));
//
// // 部门排班 数据迁移
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Deptsch Data Transfer"));
// deptschDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Deptsch Data Transfer End"));
//
// // 临时排班数据迁移
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Tempsch Data Transfer"));
// tempschDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Tempsch Data Transfer End"));
//
// // 补签单数据迁移
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Sign Data Transfer"));
// signDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Sign Data Transfer End"));
//
// // 请假 数据迁移
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "leave Data Transfer"));
// leaveDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "leave Data Transfer End"));
//
// // 出差 数据迁移
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Trip Data Transfer"));
// tripDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Trip Data Transfer End"));
//
// // 外出数据迁移
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Out Data Transfer"));
// outDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Out Data Transfer End"));
//
// // 加班数据迁移
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "OverTime Data Transfer"));
// overtimeDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "OverTime Data Transfer End"));
//
// // 调休补班数据转换
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Adjust Data Transfer"));
// adjustDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Adjust Data Transfer End"));
//
// // 调班模块 数据迁移
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Class Data Transfer"));
// classDataTransfer();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Class Data Transfer End"));
//
// if (process.getIsMigrateTrans()) {
// // 考勤记录表
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Record Data Transfer"));
// // 删除所有记录 add by jinxian.huang 2019-08-16
// attRecordService.deleteDataTransfer();
// recordDataTransfer(beginProgress);
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Record Data Transfer End"));
// }
//
// // 设备打卡记录数据迁移
// if (process.getIsMigrateTrans()) {
// progressCache
// .setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Transaction Data Transfer"));
// boolean alreadyInit = baseSysParamService.getAlreadyInitModule("attTransactionDataTransfer");
// if (!alreadyInit) {
// // 删除所有记录操作
// attTransactionService.deleteDataTransfer();
// transactionDataTransfer(beginProgress);
// baseSysParamService.setAlreadyInitModule("attTransactionDataTransfer");
// }
// progressCache
// .setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Transaction Data Transfer End"));
// }
// // 迁移该模块总时长
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + eachProgess * 23 + 1,
// "Att Module Data End Transfer Time taken:" + (System.currentTimeMillis() - beginTime) + "ms"));
//
// return true;
// }
//
// /**
// * 时间段班次
// *
// * @param
// * @return void
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @date 2018/12/28 9:29
// */
// private void shiftTimeSlotDataTransfer() {
// List<Map<String, Object>> shiftMapList = systemDataMigrationService.excuteSql(
// "select x.period_no,l.*,y.shift_no,y.time_slot_detail_ids from att_shift_timeslot l left join att_timeslot x on
// l.timeslot_id = x.id left join att_shift y on l.shift_id = y.id ");
//
// List<AttShiftItem> attShiftItems = new ArrayList<>();
// AttShiftItem attShiftItem = null;
// for (Map<String, Object> shift : shiftMapList) {
// attShiftItem = new AttShiftItem();
// // 获取对应的数据
// attShiftItem.setPeriodNo(MapUtils.getString(shift, "period_no"));
// // 获取对应的timeslot_id
// attShiftItem.setTimeSlotId(MapUtils.getInteger(shift, "timeslot_id"));
// // 班次编号
// attShiftItem.setShiftNo(MapUtils.getString(shift, "shift_no"));
//
// // 时间段明细ids(SqlServer nvarchar类型字段最大容纳的是4000，目前就暂定4000定长（其他三种数据库都支持）)
// attShiftItem.setTimeSlotDetailIds(MapUtils.getString(shift, "time_slot_detail_ids"));
//
// attShiftItems.add(attShiftItem);
// }
//
// attShiftService.shiftTimeSlotDataTransfer(attShiftItems);
// }
//
// private void attPersonDataTransfer() {
// List<Map<String, Object>> attPersonMapList = systemDataMigrationService.excuteSql(
// "select x.is_attendance,x.per_dev_auth,y.pin,z.group_name from att_person x left join pers_person y on x.id =y.id
// left join att_group z on x.group_id = z.id");
// List<AttPersonItem> attPersonItems = new ArrayList<>();
// AttPersonItem attPersonItem = null;
// for (Map<String, Object> attPerson : attPersonMapList) {
// attPersonItem = new AttPersonItem();
// // 是否考勤
// attPersonItem.setIsAttendance(getBoolean(attPerson, "is_attendance"));
//
// // 人员设备权限
// attPersonItem.setPerDevAuth(MapUtils.getShort(attPerson, "per_dev_auth"));
// // 身份类别
// //
// // 获取人员PIN
// attPersonItem.setPersonPin(MapUtils.getString(attPerson, "pin"));
// // 分组编号
// // attPersonItem.setGroupNo(MapUtils.getString(attPerson,"group_no"));
// // 分组名称
// attPersonItem.setGroupName(MapUtils.getString(attPerson, "group_name"));
//
// attPersonItems.add(attPersonItem);
// }
// attPersonService.handlerTransfer(attPersonItems);
// }
//
// /**
// * 出差 数据迁移
// *
// * @param
// * @return void
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @date 2018/12/11 14:22
// */
// private void tripDataTransfer() {
// // List<Map<String, Object>> tripMapList = systemDataMigrationService.excuteSql("select
// //
// t.person_pin,t.start_datetime,t.end_datetime,t.remark,t.trip_long,t.operate_datetime,y.apply_no,y.apply_type,y.flow_status,z.flow_no
// // from att_trip t left join att_apply y on t.id = y.trip_id left join att_flow z on y.flow_id = z.id");
// List<Map<String, Object>> tripMapList = systemDataMigrationService.excuteSql(
// "select t.person_pin,t.start_datetime,t.end_datetime,t.remark,t.trip_long,t.operate_datetime,t.remark from att_trip
// t");
// List<AttTripItem> attTripItems = new ArrayList<>();
// AttTripItem attTripItem = null;
// for (Map<String, Object> trip : tripMapList) {
// attTripItem = new AttTripItem();
// // 部门id
// /*attTripItem.setDeptId(MapUtils.getString(trip,"dept_id"));
// //部门编号
// attTripItem.setDeptCode(MapUtils.getString(trip,"dept_no"));
// //部门名称
// attTripItem.setDeptName(MapUtils.getString(trip,"dept_name"));
// //人员id
// attTripItem.setPersonId(MapUtils.getString(trip,"person_id"));*/
// // 人员编号
// attTripItem.setPersonPin(MapUtils.getString(trip, "person_pin"));
// /*//姓名
// attTripItem.setPersonName(MapUtils.getString(trip,"person_name"));
// //英文（lastName）
// attTripItem.setPersonLastName(MapUtils.getString(trip,"person_last_name"));*/
// // 开始日期时间
// Object start_datetime = MapUtils.getObject(trip, "start_datetime");
// if (start_datetime instanceof Date) {
// attTripItem.setStartDatetime((Date)start_datetime);
// }
// // 结束日期时间
// Object end_datetime = MapUtils.getObject(trip, "end_datetime");
// if (end_datetime instanceof Date) {
// attTripItem.setEndDatetime((Date)end_datetime);
// }
// // 出差时长--add by hook.fang 修复3150迁移到新架构（3.0）时，出差时长为null，导致列表加载不了的问题
// /*
// * 时长计算 因为 非当时申请环境,基础设置,班次时间段等信息都有可能变化,
// * 且迁移数据也未完全迁移完成,数据不完整 计算可能出异常或计算的不准,
// * 所以迁移过来的异常申请不做计算,置为null
// */
// attTripItem.setLeaveLong(null);
//
// // 操作日期时间
// Object operate_datetime = MapUtils.getObject(trip, "operate_datetime");
// if (operate_datetime instanceof Date) {
// attTripItem.setOperateDatetime((Date)operate_datetime);
// }
// // 备注
// attTripItem.setRemark(MapUtils.getString(trip, "remark"));
//
// /*attTripItem.setApplyNo(MapUtils.getString(trip,"apply_no"));
//
// attTripItem.setApplyType(MapUtils.getString(trip,"apply_type"));
//
// attTripItem.setFlowStatus(MapUtils.getString(trip,"flow_status"));
//
// attTripItem.setFlowNo(MapUtils.getString(trip,"flow_no"));*/
//
// attTripItems.add(attTripItem);
// }
//
// attTripService.handlerTransfer(attTripItems);
// }
//
// /**
// * 临时排班数据迁移
// *
// * @param
// * @return void
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @date 2018/12/11 16:57
// */
// private void tempschDataTransfer() {
// List<Map<String, Object>> tempschMapList = systemDataMigrationService.excuteSql(
// "select t.start_date,t.end_date,t.temp_type,t.schedule_type,x.group_name,y.code,z.pin,v.shift_no from att_tempsch t
// left join att_group x on t.group_id = x.id left join pers_department y on t.dept_id = y.id left join pers_person z on
// t.person_id = z.id left join att_tempsch_shift u on t.id = u.tempsch_id left join att_shift v on u.shift_id = v.id");
// List<AttTempSchItem> attTempSchItems = new ArrayList<>();
// AttTempSchItem attTempSchItem = null;
// for (Map<String, Object> tempsch : tempschMapList) {
// attTempSchItem = new AttTempSchItem();
// // 起始日期
// Object start_date = MapUtils.getObject(tempsch, "start_date");
// if (start_date instanceof Date) {
// attTempSchItem.setStartDate((Date)start_date);
// }
// // 结束日期
// Object end_date = MapUtils.getObject(tempsch, "end_date");
// if (end_date instanceof Date) {
// attTempSchItem.setEndDate((Date)end_date);
// }
// // 排班类型
// attTempSchItem.setScheduleType(MapUtils.getShort(tempsch, "schedule_type"));
// // 临时类型（分组、部门、人员）
// attTempSchItem.setTempType(MapUtils.getShort(tempsch, "temp_type"));
// // 考勤方式（0/按班次正常刷卡，1/一天内任刷一次有效卡，2/只计算刷卡时间，3/免刷卡）
// /* attTempSchItem.setAttendanceMode(MapUtils.getShort(tempsch,"attendance_mode"));*/
// // 加班方式（0/电脑自动计算，1/加班必须申请，2/必须加班否则旷工，3/时长为较小者-延后加班和加班单对比，4/不算加班）
// /*attTempSchItem.setOvertimeMode(MapUtils.getShort(tempsch,"overtime_mode"));
// //加班标记（0/平时，1/休息日，2/节假日）
// attTempSchItem.setOvertimeRemark(MapUtils.getShort(tempsch,"overtime_remark"));*/
// /* //分组id
// attTempSchItem.setGroupId(MapUtils.getString(tempsch,"group_id"));
// //部门id
// attTempSchItem.setDeptId(MapUtils.getString(tempsch,"dept_id"));
// //人员id
// attTempSchItem.setPersonId(MapUtils.getString(tempsch,"person_id"));*/
// // attTempSchItem.setGroupNo(MapUtils.getString(tempsch,"group_no"));
// // 分组名称
// attTempSchItem.setGroupName(MapUtils.getString(tempsch, "group_name"));
//
// attTempSchItem.setDeptCode(MapUtils.getString(tempsch, "code"));
//
// attTempSchItem.setPersonPin(MapUtils.getString(tempsch, "pin"));
//
// attTempSchItem.setShiftNo(MapUtils.getString(tempsch, "shift_no"));
//
// attTempSchItems.add(attTempSchItem);
// }
//
// attTempSchService.handlerTransfer(attTempSchItems);
// }
//
// /**
// * 补签单数据迁移
// *
// * @param
// * @return void
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @date 2018/12/11 15:18
// */
// private void signDataTransfer() {
// // List<Map<String, Object>> signMapList = systemDataMigrationService.excuteSql("select
// // x.person_pin,x.sign_datetime,x.operate_datetime,y.apply_no,y.apply_type,y.flow_status,z.flow_no from att_sign
// // x left join att_apply y on x.id = y.sign_id left join att_flow z on y.flow_id = z.id");
// List<Map<String, Object>> signMapList = systemDataMigrationService
// .excuteSql("select t.person_pin,t.sign_datetime,t.operate_datetime,t.remark from att_sign t");
// List<AttSignItem> attSignItems = new ArrayList<>();
// AttSignItem attSignItem = null;
// for (Map<String, Object> sign : signMapList) {
// attSignItem = new AttSignItem();
//
// // 部门id
// /* attSignItem.setDeptId(MapUtils.getString(sign,"dept_id"));
// //部门编号
// attSignItem.setDeptCode(MapUtils.getString(sign,"dept_no"));
// //部门名称
// attSignItem.setDeptName(MapUtils.getString(sign,"dept_name"));
// //人员id
// attSignItem.setPersonId(MapUtils.getString(sign,"person_id"));*/
// // 人员编号
// attSignItem.setPersonPin(MapUtils.getString(sign, "person_pin"));
// /*//姓名
// attSignItem.setPersonName(MapUtils.getString(sign,"person_name"));
// //英文（lastName）
// attSignItem.setPersonLastName(MapUtils.getString(sign,"person_last_name"));*/
// // 补签日期时间
// Object sign_datetime = MapUtils.getObject(sign, "sign_datetime");
// if (sign_datetime instanceof Date) {
// attSignItem.setSignDatetime((Date)sign_datetime);
// }
// // 操作日期时间
// Object operate_datetime = MapUtils.getObject(sign, "operate_datetime");
// if (operate_datetime instanceof Date) {
// attSignItem.setOperateDatetime((Date)operate_datetime);
// }
// // 备注
// attSignItem.setRemark(MapUtils.getString(sign, "remark"));
//
// /*attSignItem.setApplyNo(MapUtils.getString(sign,"apply_no"));
//
// attSignItem.setApplyType(MapUtils.getString(sign,"apply_type"));
//
// attSignItem.setFlowStatus(MapUtils.getString(sign,"flow_status"));
//
// attSignItem.setFlowNo(MapUtils.getString(sign,"flow_no"));*/
//
// attSignItems.add(attSignItem);
// }
// attSignService.handlerTransfer(attSignItems);
// }
//
// /**
// * 班次数据迁移
// *
// * @param
// * @return void
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @date 2018/12/11 12:00
// */
// private void shiftDataTransfer() {
//
// List<Map<String, Object>> shiftMapList = systemDataMigrationService.excuteSql("select * from att_shift ");
// List<AttShiftItem> attShiftItems = new ArrayList<>();
// AttShiftItem attShiftItem = null;
// for (Map<String, Object> shift : shiftMapList) {
// attShiftItem = new AttShiftItem();
// // 获取对应的数据
// // 班次类型
// attShiftItem.setShiftType(MapUtils.getShort(shift, "shift_type"));
// // 班次编号
// attShiftItem.setShiftNo(MapUtils.getString(shift, "shift_no"));
// // 班次名称
// attShiftItem.setShiftName(MapUtils.getString(shift, "shift_name"));
// // 颜色
// attShiftItem.setShiftColor(MapUtils.getString(shift, "shift_color"));
// // 周期单位（0/天，1/周，2/月）
// attShiftItem.setPeriodicUnit(MapUtils.getShort(shift, "periodic_unit"));
// // 周期数
// attShiftItem.setPeriodNumber(MapUtils.getShort(shift, "period_Number"));
// // 起始日期
// Object start_date = MapUtils.getObject(shift, "start_date");
// attShiftItem.setStartDate((Date)start_date);
// // 是否月内轮班（false：否/0，true：是/1）
// attShiftItem.setIsShiftWithinMonth(getBoolean(shift, "is_shift_within_month"));
//
// // 考勤方式（0/按班次正常刷卡，1/一天内任刷一次有效卡，2/只计算刷卡时间，3/免刷卡）
// attShiftItem.setAttendanceMode(MapUtils.getShort(shift, "attendance_mode"));
// // 加班方式（0/电脑自动计算，1/加班必须申请，2/必须加班否则旷工，3/时长为较小者-延后加班和加班单对比，4/不算加班）
// attShiftItem.setOvertimeMode(MapUtils.getShort(shift, "overtime_mode"));
// // 加班标记（0/平时，1/休息日，2/节假日）
// attShiftItem.setOvertimeSign(MapUtils.getShort(shift, "overtime_sign"));
// attShiftItems.add(attShiftItem);
// }
// attShiftService.handlerTransfer(attShiftItems);
// }
//
// /**
// * 考勤点数据迁移 -- 只迁移门禁当考勤点
// *
// * @param
// * @return void
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @date 2018/12/11 16:36
// */
// private void pointDataTransfer() {
// List<Map<String, Object>> pointTypeMapList = systemDataMigrationService.excuteSql(
// "select t.*,b.code from att_point t left join base_area b on t.area_id = b.id where t.device_module='0'");
// List<AttPointItem> attPointItems = new ArrayList<>();
// AttPointItem attPointItem = null;
// for (Map<String, Object> point : pointTypeMapList) {
// attPointItem = new AttPointItem();
// // 考勤点名称
// attPointItem.setPointName(MapUtils.getString(point, "point_name"));
// // 区域id -->迁移对应区域code
// attPointItem.setAreaId(MapUtils.getString(point, "code"));
//
// // 区域名称
// attPointItem.setAreaName(MapUtils.getString(point, "area_name"));
//
// // 设备sn
// attPointItem.setDeviceSn(MapUtils.getString(point, "device_sn"));
//
// // 设备所属模块（门编号）
// attPointItem.setDoorNo(MapUtils.getShort(point, "door_no"));
//
// // 通过Device sn反查门禁模块获得Device id sn可能获得多个门，需加上doorNo门编号才能唯一
// AttSelectDoorItem item =
// attAccDeviceService.getAccDeviceByDevSnAndDoorNo(attPointItem.getDeviceSn(), attPointItem.getDoorNo());
// if (null != item) {
// attPointItem.setDeviceId(item.getId());
// }
//
// // 门禁的门名称
// attPointItem.setDeviceName(MapUtils.getString(point, "device_name"));
// // 设备所属模块
// attPointItem.setDeviceModule(MapUtils.getString(point, "device_module"));
// // 停车场的设备属性（出、入）
// attPointItem.setStatus(MapUtils.getShort(point, "status"));
//
// attPointItems.add(attPointItem);
// }
//
// attPointService.handlerTransfer(attPointItems);
//
// }
//
// /**
// * 加班数据迁移
// *
// * @param
// * @return void
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @date 2018/12/11 14:58
// */
// private void overtimeDataTransfer() {
// // List<Map<String, Object>> overtimeMapList = systemDataMigrationService.excuteSql("select
// //
// t.person_pin,t.start_datetime,t.end_datetime,t.remark,t.overtime_sign,t.operate_datetime,y.apply_no,y.apply_type,y.flow_status,z.flow_no
// // from att_overtime t left join att_apply y on t.id = y.overtime_id left join att_flow z on y.flow_id = z.id");
// List<Map<String, Object>> overtimeMapList = systemDataMigrationService.excuteSql(
// "select t.person_pin,t.start_datetime,t.end_datetime,t.remark,t.overtime_sign,t.operate_datetime from att_overtime
// t");
// List<AttOvertimeItem> attOvertimeItems = new ArrayList<>();
// AttOvertimeItem attOvertimeItem = null;
//
// for (Map<String, Object> overtime : overtimeMapList) {
// attOvertimeItem = new AttOvertimeItem();
// /*
// //部门id
// attOvertimeItem.setDeptId(MapUtils.getString(overtime,"dept_id"));
// //部门编号
// attOvertimeItem.setDeptCode(MapUtils.getString(overtime,"dept_no"));
// //部门名称
// attOvertimeItem.setDeptName(MapUtils.getString(overtime,"dept_name"));
// //人员id
// attOvertimeItem.setPersonId(MapUtils.getString(overtime,"person_id"));
// */
// // 人员编号
// attOvertimeItem.setPersonPin(MapUtils.getString(overtime, "person_pin"));
// /*//姓名
// attOvertimeItem.setPersonName(MapUtils.getString(overtime,"person_name"));
// //英文（lastName）
// attOvertimeItem.setPersonLastName(MapUtils.getString(overtime,"person_last_name"));*/
// // 加班标记（平时，休息日，节假日）
// attOvertimeItem.setOvertimeSign(MapUtils.getShort(overtime, "overtime_sign"));
// // 开始日期时间
// Object start_datetime = MapUtils.getObject(overtime, "start_datetime");
// if (start_datetime instanceof Date) {
// attOvertimeItem.setStartDatetime((Date)start_datetime);
// }
// // 结束日期时间
// Object end_datetime = MapUtils.getObject(overtime, "end_datetime");
// if (end_datetime instanceof Date) {
// attOvertimeItem.setEndDatetime((Date)end_datetime);
// }
// // 加班时长--add by hook.fang 修复3150迁移到新架构（3.0）时，请假时长为null，导致列表加载不了的问题
// // int timeLong = AttDateUtils.getMinuteDiff(attOvertimeItem.getStartDatetime(),
// // attOvertimeItem.getEndDatetime());
// attOvertimeItem.setOvertimeLong(null);
//
// // 操作日期时间
// Object operate_datetime = MapUtils.getObject(overtime, "operate_datetime");
// if (operate_datetime instanceof Date) {
// attOvertimeItem.setOperateDatetime((Date)operate_datetime);
// }
// // 备注
// attOvertimeItem.setRemark(MapUtils.getString(overtime, "remark"));
//
// /*attOvertimeItem.setApplyNo(MapUtils.getString(overtime,"apply_no"));
//
// attOvertimeItem.setApplyType(MapUtils.getString(overtime,"apply_type"));
//
// attOvertimeItem.setFlowStatus(MapUtils.getString(overtime,"flow_status"));
//
// attOvertimeItem.setFlowNo(MapUtils.getString(overtime,"flow_no"));*/
//
// attOvertimeItems.add(attOvertimeItem);
// }
// attOvertimeService.handlerTransfer(attOvertimeItems);
// }
//
// /**
// * 外出数据迁移
// */
// private void outDataTransfer() {
// // List<Map<String, Object>> outMapList = systemDataMigrationService.excuteSql("select
// //
// t.person_pin,t.start_datetime,t.end_datetime,t.remark,t.out_long,t.operate_datetime,y.apply_no,y.apply_type,y.flow_status,z.flow_no
// // from att_out t left join att_apply y on t.id = y.out_id left join att_flow z on y.flow_id = z.id ");
// List<Map<String, Object>> outMapList = systemDataMigrationService.excuteSql(
// "select t.person_pin,t.start_datetime,t.end_datetime,t.remark,t.out_long,t.operate_datetime from att_out t");
// List<AttOutItem> attOutItems = new ArrayList<>();
// AttOutItem attOutItem = null;
// for (Map<String, Object> out : outMapList) {
// attOutItem = new AttOutItem();
// /*
// //部门id
// attOutItem.setDeptId(MapUtils.getString(out,"dept_id"));
// //部门编号
// attOutItem.setDeptCode(MapUtils.getString(out,"dept_no"));
// //部门名称
// attOutItem.setDeptName(MapUtils.getString(out,"dept_name"));
// //人员id
// attOutItem.setPersonId(MapUtils.getString(out,"person_id"));*/
// // 人员编号
// attOutItem.setPersonPin(MapUtils.getString(out, "person_pin"));
// /*//姓名
// attOutItem.setPersonName(MapUtils.getString(out,"person_name"));
// //英文（lastName）
// attOutItem.setPersonLastName(MapUtils.getString(out,"person_last_name"));*/
// // 开始日期时间
// Object start_datetime = MapUtils.getObject(out, "start_datetime");
// if (start_datetime instanceof Date) {
// attOutItem.setStartDatetime((Date)start_datetime);
// }
// // 结束日期时间
// Object end_datetime = MapUtils.getObject(out, "end_datetime");
// if (end_datetime instanceof Date) {
// attOutItem.setEndDatetime((Date)end_datetime);
// }
// // 外出时长--add by hook.fang 修复3150迁移到新架构（3.0）时，异常时长为null，导致列表加载不了的问题
// attOutItem.setLeaveLong(null);
//
// // 操作日期时间
// Object operate_datetime = MapUtils.getObject(out, "operate_datetime");
// if (operate_datetime instanceof Date) {
// attOutItem.setOperateDatetime((Date)operate_datetime);
// }
// // 备注
// attOutItem.setRemark(MapUtils.getString(out, "remark"));
//
// /*attOutItem.setApplyNo(MapUtils.getString(out,"apply_no"));
//
// attOutItem.setApplyType(MapUtils.getString(out,"apply_type"));
//
// attOutItem.setFlowStatus(MapUtils.getString(out,"flow_status"));
//
// attOutItem.setFlowNo(MapUtils.getString(out,"flow_no"));*/
//
// attOutItems.add(attOutItem);
// }
// attOutService.handlerTransfer(attOutItems);
// }
//
// /**
// * 请假类型数据迁移
// * <p>
// * 假种编码为空不迁移
// */
// private void leaveTypeDataTransfer() {
// List<Map<String, Object>> attLeaveTypeMapList = systemDataMigrationService
// .excuteSql("select * from att_leavetype where leavetype_no != '' or leavetype_no != null");
// List<AttLeaveTypeItem> attLeaveTypeItems = new ArrayList<>();
// AttLeaveTypeItem attLeaveTypeItem = null;
// for (Map<String, Object> attLeaveType : attLeaveTypeMapList) {
// attLeaveTypeItem = new AttLeaveTypeItem();
// // 假种编号
// attLeaveTypeItem.setLeaveTypeNo(MapUtils.getString(attLeaveType, "leavetype_no"));
// // 假种名称
// attLeaveTypeItem.setLeaveTypeName(MapUtils.getString(attLeaveType, "leavetype_name"));
// // 是否扣上班时长（false：否/0，true：是/1）
// attLeaveTypeItem.setIsDeductWorkLong(getBoolean(attLeaveType, "is_deduct_work_Long"));
//
// attLeaveTypeItems.add(attLeaveTypeItem);
// }
// attLeaveTypeService.handlerTransfer(attLeaveTypeItems);
// }
//
// /**
// * 请假 数据迁移
// *
// * @param
// * @return void
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @date 2018/12/10 19:25
// */
// private void leaveDataTransfer() {
// // List<Map<String, Object>> leaveMapList = systemDataMigrationService.excuteSql("select
// //
// t.dept_no,t.person_pin,t.leave_long,t.start_datetime,t.end_datetime,t.operate_datetime,t.remark,l.leavetype_no,w.leave_image,y.apply_no,y.apply_type,y.flow_status,z.flow_no
// // from att_leave t left join att_leavetype l on t.leavetype_id = l.id left join att_leaveimage w on
// // t.leaveimage_id = w.id left join att_apply y on t.id = y.leave_id left join att_flow z on y.flow_id = z.id");
// List<Map<String, Object>> leaveMapList = systemDataMigrationService.excuteSql(
// "select
// t.dept_no,t.person_pin,t.leave_long,t.start_datetime,t.end_datetime,t.operate_datetime,t.remark,l.leavetype_no,w.leave_image
// from att_leave t left join att_leavetype l on t.leavetype_id = l.id left join att_leaveimage w on t.leaveimage_id =
// w.id");
// List<AttLeaveItem> attLeaveItems = new ArrayList<>();
// AttLeaveItem attLeaveItem = null;
// for (Map<String, Object> leave : leaveMapList) {
// attLeaveItem = new AttLeaveItem();
//
// /*//部门id
// attLeaveItem.setDeptId(MapUtils.getString(leave,"dept_id"));
// //部门编号
// attLeaveItem.setDeptCode(MapUtils.getString(leave,"dept_no"));
// //部门名称
// attLeaveItem.setDeptName(MapUtils.getString(leave,"dept_name"));
// //人员id
// attLeaveItem.setPersonId(MapUtils.getString(leave,"person_id"));*/
// // 部门编号
// attLeaveItem.setDeptCode(MapUtils.getString(leave, "dept_no"));
// // 人员编号
// attLeaveItem.setPersonPin(MapUtils.getString(leave, "person_pin"));
// /* //姓名
// attLeaveItem.setPersonName(MapUtils.getString(leave,"person_name"));
// //英文（lastName）
// attLeaveItem.setPersonLastName(MapUtils.getString(leave,"person_last_name"));*/
// // 开始日期时间
// Object start_datetime = MapUtils.getObject(leave, "start_datetime");
// if (start_datetime instanceof Date) {
// attLeaveItem.setStartDatetime((Date)start_datetime);
// }
// // 结束日期时间
// Object end_datetime = MapUtils.getObject(leave, "end_datetime");
// if (end_datetime instanceof Date) {
// attLeaveItem.setEndDatetime((Date)end_datetime);
// }
// // 请假时长--add by hook.fang 修复3150迁移到新架构（3.0）时，请假时长为null，导致列表加载不了的问题
// // 调整迁移时长还是置为null 其他地方使用兼容处理空异常 by ljf 2020/03/31
// attLeaveItem.setLeaveLong(null);
//
// // 操作日期时间
// Object operate_datetime = MapUtils.getObject(leave, "operate_datetime");
// if (operate_datetime instanceof Date) {
// attLeaveItem.setOperateDatetime((Date)operate_datetime);
// }
// // 备注
// attLeaveItem.setRemark(MapUtils.getString(leave, "remark"));
//
// attLeaveItem.setLeaveTypeNo(MapUtils.getString(leave, "leavetype_no"));
//
// attLeaveItem.setLeaveImagePath(MapUtils.getString(leave, "leave_image"));
//
// /*attLeaveItem.setApplyNo(MapUtils.getString(leave,"apply_no"));
//
// attLeaveItem.setApplyType(MapUtils.getString(leave,"apply_type"));
//
// attLeaveItem.setFlowStatus(MapUtils.getString(leave,"flow_status"));
//
// attLeaveItem.setFlowNo(MapUtils.getString(leave,"flow_no"));*/
//
// attLeaveItems.add(attLeaveItem);
// }
// attLeaveService.handlerTransfer(attLeaveItems);
// }
//
// /**
// * 分组排班 数据迁移
// *
// * @param
// * @return void
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @date 2018/12/10 19:21
// */
// private void groupschDataTransfer() {
// List<Map<String, Object>> groupschMapList = systemDataMigrationService.excuteSql(
// "select s.shift_no,y.group_name,x.* from att_groupsch_shift z left join att_groupsch x on z.groupsch_id = x.id left
// join att_group y on x.group_id = y.id left join att_shift s on z.shift_id = s.id");
//
// List<AttGroupSchItem> attGroupSchItems = new ArrayList<>();
//
// AttGroupSchItem attGroupSchItem = null;
//
// for (Map<String, Object> groupsch : groupschMapList) {
// attGroupSchItem = new AttGroupSchItem();
// attGroupSchItem.setId(MapUtils.getString(groupsch, "id"));
// // 起始日期
// Object start_date = MapUtils.getObject(groupsch, "start_date");
// if (start_date instanceof Date) {
// attGroupSchItem.setStartDate((Date)start_date);
// }
// // 结束日期
// Object end_date = MapUtils.getObject(groupsch, "end_date");
// if (end_date instanceof Date) {
// attGroupSchItem.setEndDate((Date)end_date);
// }
// // 排班类型
// attGroupSchItem.setScheduleType(MapUtils.getShort(groupsch, "schedule_type"));
// // 考勤方式（0/按班次正常刷卡，1/一天内任刷一次有效卡，2/只计算刷卡时间，3/免刷卡）
// /* attGroupSchItem.setAttendanceMode(MapUtils.getShort(groupsch,"attendance_mode"));*/
// // 加班方式（0/电脑自动计算，1/加班必须申请，2/必须加班否则旷工，3/时长为较小者-延后加班和加班单对比，4/不算加班）
// /*attGroupSchItem.setOvertimeMode(MapUtils.getShort(groupsch,"overtime_mode"));*/
// // 加班标记（0/平时，1/休息日，2/节假日）
// /*attGroupSchItem.setOvertimeRemark(MapUtils.getShort(groupsch,"overtime_remark"));*/
// // 分组编号
// // attGroupSchItem.setGroupNo(MapUtils.getString(groupsch,"group_no"));
// // 分组名称
// attGroupSchItem.setGroupName(MapUtils.getString(groupsch, "group_name"));
// // 获取班次编号
// attGroupSchItem.setShiftNo(MapUtils.getString(groupsch, "shift_no"));
//
// attGroupSchItems.add(attGroupSchItem);
// }
// attGroupSchService.handlerTransfer(attGroupSchItems);
// }
//
// /**
// * 设备 数据迁移
// *
// * @param
// * @return void
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @date 2018/12/10 19:18
// */
// private void deviceDataTransfer() {
// // List<Map<String, Object>> deviceMapList = systemDataMigrationService.excuteSql("select x.*,y.code from
// // att_device x left join base_area y on x.area_id = y.id");
// // 过滤信息屏设备
// List<Map<String, Object>> deviceMapList = systemDataMigrationService.excuteSql(
// "select x.*,y.code from att_device x left join base_area y on x.area_id = y.id left join adms_device t on t.sn =
// x.dev_sn left join adms_device_option op on op.dev_id=t.id where op.option_value='att' and not exists (select
// op.dev_id from adms_device_option op where op.dev_id=t.id and op.option_value='ins') ");
// List<AttDeviceItem> attDeviceItems = new ArrayList<>();
// AttDeviceItem attDeviceItem = null;
// for (Map<String, Object> device : deviceMapList) {
// attDeviceItem = new AttDeviceItem();
// // 设备名称
// attDeviceItem.setDevName(MapUtils.getString(device, "dev_name"));
// // 设备型号
// attDeviceItem.setDevSn(MapUtils.getString(device, "dev_sn"));
// // 设备状态 dev_status
// // attDeviceItem.setStatus()
// // 固件版本号
// attDeviceItem.setFwVersion(MapUtils.getString(device, "fw_version"));
// // 指纹版本
// attDeviceItem.setFpVersion(MapUtils.getString(device, "fp_Version"));
// // 面部版本
// attDeviceItem.setFaceVersion(MapUtils.getString(device, "face_Version"));
// // ip地址
// attDeviceItem.setIpAddress(MapUtils.getString(device, "ip_address"));
// // 通讯端口
// attDeviceItem.setCommPort(MapUtils.getInteger(device, "comm_port"));
// // 通信方式
// attDeviceItem.setProtocol(MapUtils.getString(device, "protocol"));
// // 通信密码
// attDeviceItem.setPushcommkey(MapUtils.getString(device, "push_commkey"));
//
// attDeviceItem.setDevModel(MapUtils.getString(device, "dev_model"));
//
// // 指纹数
// attDeviceItem.setFpCount(MapUtils.getInteger(device, "fp_count"));
// // 人员数
// attDeviceItem.setPersonCount(MapUtils.getInteger(device, "person_count"));
// // 人脸数
// attDeviceItem.setFaceCount(MapUtils.getInteger(device, "face_count"));
// // 是否是登记机
// // 启用状态
// attDeviceItem.setIsRegDevice(getBoolean(device, "is_reg_device"));
//
// // 数据更新标志
// attDeviceItem.setUpdateFlag(MapUtils.getString(device, "update_flag"));
// // 时区
// attDeviceItem.setTimeZone(MapUtils.getString(device, "time_zone"));
// // 刷新间隔时间(分)
// attDeviceItem.setTransInterval(MapUtils.getShort(device, "trans_interval"));
// // 定时传送时间(如：00:00;14:05)
// attDeviceItem.setTransTimes(MapUtils.getString(device, "trans_times"));
// // 实时上传数据 real_time
// // attDeviceItem.setRealTime(MapUtils.getBoolean(device,"real_time"));
// attDeviceItem.setRealTime(getBoolean(device, "real_time"));
// // 和服务器通讯的最大命令个数
// attDeviceItem.setCmdCount(MapUtils.getShort(device, "cmd_count"));
// // 查询记录时间（秒）
// attDeviceItem.setSearchInterval(MapUtils.getShort(device, "search_interval"));
// // 数据下发标志
// attDeviceItem.setDataDownFlag(MapUtils.getString(device, "data_down_flag"));
//
// // 启用状态
// attDeviceItem.setStatus(getBoolean(device, "status"));
//
// // 记录数
// attDeviceItem.setRecordCount(MapUtils.getInteger(device, "record_count"));
// // 区域
// attDeviceItem.setAuthAreaCode(MapUtils.getString(device, "code"));
//
// attDeviceItems.add(attDeviceItem);
// }
// attDeviceService.handlerTransfer(attDeviceItems);
//
// }
//
// /**
// * 部门排班 数据迁移
// *
// * @param
// * @return void
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @date 2018/12/10 19:13
// */
// private void deptschDataTransfer() {
// List<Map<String, Object>> deptSchMapList = systemDataMigrationService.excuteSql(
// "select s.shift_no,y.code,x.* from att_deptsch_shift z left join att_deptsch x on z.deptsch_id = x.id left join
// pers_department y on x.dept_id = y.id left join att_shift s on z.shift_id = s.id");
// List<AttDeptSchItem> attDeptSchItems = new ArrayList<>();
// AttDeptSchItem attDeptSchItem = null;
// for (Map<String, Object> deptSch : deptSchMapList) {
// attDeptSchItem = new AttDeptSchItem();
// // 起始日期
// Object start_date = MapUtils.getObject(deptSch, "start_date");
// if (start_date instanceof Date) {
// attDeptSchItem.setStartDate((Date)start_date);
// }
// // 结束日期
// Object end_date = MapUtils.getObject(deptSch, "end_date");
// if (end_date instanceof Date) {
// attDeptSchItem.setEndDate((Date)end_date);
// }
// // 排班类型
// attDeptSchItem.setScheduleType(MapUtils.getShort(deptSch, "schedule_type"));
// // 考勤方式（0/按班次正常刷卡，1/一天内任刷一次有效卡，2/只计算刷卡时间，3/免刷卡）
// /* attDeptSchItem.setAttendanceMode(MapUtils.getShort(deptSch,"attendance_mode"));*/
// // 加班方式（0/电脑自动计算，1/加班必须申请，2/必须加班否则旷工，3/时长为较小者-延后加班和加班单对比，4/不算加班）
// /*attDeptSchItem.setOvertimeMode(MapUtils.getShort(deptSch,"overtime_mode"));*/
// // 加班标记（0/平时，1/休息日，2/节假日）
// /*attDeptSchItem.setOvertimeRemark(MapUtils.getShort(deptSch,"overtime_remark"));*/
// // 部门id
// attDeptSchItem.setDeptCode(MapUtils.getString(deptSch, "code"));
// attDeptSchItem.setShiftNo(MapUtils.getString(deptSch, "shift_no"));
//
// attDeptSchItems.add(attDeptSchItem);
// }
// attDeptSchService.handlerTransfer(attDeptSchItems);
// }
//
// /**
// * 调班模块 数据迁移
// */
// private void classDataTransfer() {
// // List<Map<String, Object>> classMapList = systemDataMigrationService.excuteSql("select
// //
// t.adjustperson_pin,t.swapperson_pin,t.adjust_type,t.adjust_date,t.swap_date,t.operate_datetime,t.remark,y.apply_no,y.apply_type,y.flow_status,z.flow_no,x.shift_no
// // from att_class t left join att_apply y on t.id = y.class_id left join att_flow z on y.flow_id = z.id left
// // join att_shift x on t.swapshift_id = x.id");
// List<Map<String, Object>> classMapList = systemDataMigrationService.excuteSql(
// "select
// t.adjustperson_pin,t.swapperson_pin,t.adjust_type,t.adjust_date,t.swap_date,t.operate_datetime,t.remark,x.shift_no
// from att_class t left join att_shift x on t.swapshift_id = x.id");
// List<AttClassItem> attClassItems = new ArrayList<>();
// AttClassItem attClassItem = null;
// for (Map<String, Object> attclass : classMapList) {
// attClassItem = new AttClassItem();
// /* //调整人员部门id
// attClassItem.setAdjustDeptId(MapUtils.getString(attclass,"adjustdept_id"));
// //调整人员部门编号
// attClassItem.setAdjustDeptCode(MapUtils.getString(attclass,"adjustdept_no"));
// //调整人员部门名称
// attClassItem.setAdjustDeptName(MapUtils.getString(attclass,"adjustdept_name"));
// //调整人员id
// attClassItem.setAdjustPersonId(MapUtils.getString(attclass,"adjustperson_id"));*/
// // 调整人员编号
// attClassItem.setAdjustPersonPin(MapUtils.getString(attclass, "adjustperson_pin"));
// /*//调整人员姓名
// attClassItem.setAdjustPersonName(MapUtils.getString(attclass,"adjustperson_name"));
// //调整人员英文（lastName）
// attClassItem.setAdjustPersonLastName(MapUtils.getString(attclass,"adjustperson_last_name"));
// //对调人员部门id
// attClassItem.setSwapDeptId(MapUtils.getString(attclass,"swapdept_id"));
// //对调人员部门编号
// attClassItem.setSwapDeptCode(MapUtils.getString(attclass,"swapdept_no"));
// //对调人员部门名称
// attClassItem.setSwapDeptName(MapUtils.getString(attclass,"swapdept_name"));
// //对调人员id
// attClassItem.setSwapPersonId(MapUtils.getString(attclass,"swapperson_id"));*/
// // 对调人员编号
// attClassItem.setSwapPersonPin(MapUtils.getString(attclass, "swapperson_pin"));
// /* //对调人员姓名
// attClassItem.setSwapPersonName(MapUtils.getString(attclass,"swapperson_name"));
// //对调人员英文（lastName）
// attClassItem.setSwapPersonLastName(MapUtils.getString(attclass,"swapperson_last_name"));*/
// // 调整类型
// attClassItem.setAdjustType(MapUtils.getShort(attclass, "adjust_type"));
// // 调整日期
// Object adjust_date = MapUtils.getObject(attclass, "adjust_date");
// if (adjust_date instanceof Date) {
// attClassItem.setAdjustDate((Date)adjust_date);
// }
// // 对调日期
// Object swap_date = MapUtils.getObject(attclass, "swap_date");
// if (swap_date instanceof Date) {
// attClassItem.setSwapDate((Date)swap_date);
// }
// // 操作日期时间
// Object operate_datetime = MapUtils.getObject(attclass, "operate_datetime");
// if (operate_datetime instanceof Date) {
// attClassItem.setOperateDatetime((Date)operate_datetime);
// }
// // 备注
// attClassItem.setRemark(MapUtils.getString(attclass, "remark"));
//
// // 班次编号
// attClassItem.setShiftNo(MapUtils.getString(attclass, "shift_no"));
//
// /*attClassItem.setApplyNo(MapUtils.getString(attclass,"apply_no"));
//
// attClassItem.setApplyType(MapUtils.getString(attclass,"apply_type"));
//
// attClassItem.setFlowStatus(MapUtils.getString(attclass,"flow_status"));
//
// attClassItem.setFlowNo(MapUtils.getString(attclass,"flow_no"));*/
//
// attClassItems.add(attClassItem);
// }
// attClassService.handlerTransfer(attClassItems);
// }
//
// /**
// * 区域人员数据迁移
// */
// private void areaPersonDataTransfer() {
// // List<Map<String, Object>> areaPersonMapList = systemDataMigrationService.excuteSql("select y.code,z.pin from
// // att_area_person x left join base_area y on x.area_id = y.id LEFT JOIN pers_person z on x.person_id = z.id");
// List<Map<String, Object>> areaPersonMapList = systemDataMigrationService.excuteSql(
// "select distinct * from (select y.code,z.pin from att_area_person x left join base_area y on x.area_id = y.id left
// join pers_person z on x.person_id = z.id) t ");
// List<AttAreaPersonItem> attAreaPersonItems = new ArrayList<>();
// AttAreaPersonItem attAreaPersonItem = null;
// for (Map<String, Object> areaPerson : areaPersonMapList) {
// attAreaPersonItem = new AttAreaPersonItem();
// // 区域id
// attAreaPersonItem.setAuthAreaNo(MapUtils.getString(areaPerson, "code"));
// // 人员id
// attAreaPersonItem.setPin(MapUtils.getString(areaPerson, "pin"));
// attAreaPersonItems.add(attAreaPersonItem);
// }
// attAreaPersonService.handlerTransfer(attAreaPersonItems);
// }
//
// /**
// * 设备打卡记录数据迁移
// *
// * @param
// * @return void
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @date 2018/12/10 11:52
// */
// private void transactionDataTransfer(int beginProgress) {
// // 获取总数
// List<Map<String, Object>> countData = systemDataMigrationService
// .excuteSql("select count(1) as count_number from att_transaction where t >=? and t<=?");
// long attTransactionCount = Long.parseLong(String.valueOf(countData.get(0).get("count_number")));
//
// // 超出限制条数不迁移
// if (attTransactionCount < limitSize) {
// // 迁移的总数据量
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress,
// "The total number " + attTransactionCount + " pieces"));
// // 根据size的带下，根据计算，获取需要查询几次
// long count = attTransactionCount / splitSize;
// if (attTransactionCount % splitSize > 0) {
// count++;
// }
// int size = splitSize;
// for (int page = 0; page < count; page++) {
// List<Map<String, Object>> transactionMapList =
// systemDataMigrationService.excutePageSql("att_transaction", page * size, size);
//
// // 进度条显示剩余的数量
// attTransactionCount = attTransactionCount - transactionMapList.size();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress,
// "Remaining " + attTransactionCount + " pieces"));
// if (!CollectionUtil.isEmpty(transactionMapList)) {
// List<AttTransactionItem> attTransactionItems = new ArrayList<>();
// AttTransactionItem attTransactionItem = null;
// for (Map<String, Object> transaction : transactionMapList) {
// attTransactionItem = new AttTransactionItem();
// // 部门id
// attTransactionItem.setDeptId(MapUtils.getString(transaction, "dept_id"));
// // 部门编号
// attTransactionItem.setDeptCode(MapUtils.getString(transaction, "dept_no"));
// // 部门名称
// attTransactionItem.setDeptName(MapUtils.getString(transaction, "dept_name"));
// // 人员编号
// attTransactionItem.setPersonPin(MapUtils.getString(transaction, "person_pin"));
// // 姓名
// attTransactionItem.setPersonName(MapUtils.getString(transaction, "person_name"));
// // 英文（lastName）
// attTransactionItem.setPersonLastName(MapUtils.getString(transaction, "person_last_name"));
// // 区域Id
// attTransactionItem.setAreaId(MapUtils.getString(transaction, "area_id"));
// // 区域编号
// attTransactionItem.setAreaNo(MapUtils.getString(transaction, "area_no"));
// // 区域名称
// attTransactionItem.setAreaName(MapUtils.getString(transaction, "area_name"));
// // 设备Id
// attTransactionItem.setDeviceId(MapUtils.getString(transaction, "device_id"));
// // 设备序列号
// attTransactionItem.setDeviceSn(MapUtils.getString(transaction, "device_sn"));
// // 门编号
// attTransactionItem.setDoorNo(MapUtils.getShort(transaction, "door_no"));
// // 考勤日期时间
// Object att_datetime = MapUtils.getObject(transaction, "att_datetime");
// if (att_datetime instanceof Date) {
// attTransactionItem.setAttDatetime((Date)att_datetime);
// }
//
// // 考勤日期
// attTransactionItem.setAttDate(MapUtils.getString(transaction, "att_date"));
// // 考勤时间
// attTransactionItem.setAttTime(MapUtils.getString(transaction, "att_time"));
// // 考勤状态
// attTransactionItem.setAttState(MapUtils.getString(transaction, "att_state"));
// // 验证方式
// attTransactionItem.setAttVerify(MapUtils.getString(transaction, "att_verify"));
// // 标识
// attTransactionItem.setMark(MapUtils.getString(transaction, "mark"));
//
// attTransactionItems.add(attTransactionItem);
// }
// attTransactionService.handlerTransfer(attTransactionItems);
// }
// }
// }
// }
//
// /**
// * 考勤记录表
// *
// * @param
// * @return void
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @date 2018/12/10 9:07
// */
// private void recordDataTransfer(Integer beginProgress) {
// // 获取总数
// List<Map<String, Object>> countData =
// systemDataMigrationService.excuteSql("select count(1) as count_number from att_record");
// long attRecordCount = Long.parseLong(String.valueOf(countData.get(0).get("count_number")));
//
// // 超出限制条数不迁移
// if (attRecordCount < limitSize) {
// // 迁移的总数据量
// progressCache.setProcess(
// ProcessBean.createNormalSingleProcess(beginProgress, "The total number " + attRecordCount + " pieces"));
// // 根据size的带下，根据计算，获取需要查询几次
// long count = attRecordCount / splitSize;
// if (attRecordCount % splitSize > 0) {
// count++;
// }
// int size = splitSize;
// for (int page = 0; page < count; page++) {
// List<Map<String, Object>> recordMapList =
// systemDataMigrationService.excutePageSql("att_record", page * size, size);
// // 进度条显示剩余的数量
// attRecordCount = attRecordCount - recordMapList.size();
// progressCache.setProcess(
// ProcessBean.createNormalSingleProcess(beginProgress, "Remaining " + attRecordCount + " pieces"));
// if (!CollectionUtil.isEmpty(recordMapList)) {
// List<AttRecordItem> attRecordItems = new ArrayList<>();
// AttRecordItem attRecordItem = null;
// for (Map<String, Object> record : recordMapList) {
// attRecordItem = new AttRecordItem();
// // 部门id
// attRecordItem.setDeptId(MapUtils.getString(record, "dept_id"));
// // 部门编号
// attRecordItem.setDeptCode(MapUtils.getString(record, "dept_no"));
// // 部门名称
// attRecordItem.setDeptName(MapUtils.getString(record, "dept_name"));
// // 人员编号
// attRecordItem.setPersonPin(MapUtils.getString(record, "pin"));
// // 姓名
// attRecordItem.setPersonName(MapUtils.getString(record, "name"));
// // 英文（lastName）
// attRecordItem.setPersonLastName(MapUtils.getString(record, "last_name"));
// // 考勤日期
// Object att_date = MapUtils.getObject(record, "att_date");
// if (att_date instanceof Date) {
// attRecordItem.setAttDate((Date)att_date);
// }
// // 星期
// attRecordItem.setWeek(MapUtils.getString(record, "week"));
// // 班次编号
// attRecordItem.setShiftNo(MapUtils.getString(record, "shift_no"));
// // 班次名称
// attRecordItem.setShiftName(MapUtils.getString(record, "shift_name"));
// // 班次时间数据
// attRecordItem.setShiftTimeData(MapUtils.getString(record, "shift_time_data"));
// // 有效打卡数据
// attRecordItem.setCardValidData(MapUtils.getString(record, "card_valid_data"));
// // 有效打卡次数
// attRecordItem.setCardValidCount(MapUtils.getInteger(record, "card_valid_count"));
// // 应出勤分钟数
// attRecordItem.setShouldMinute(MapUtils.getInteger(record, "should_minute"));
// // 实际出勤分钟数
// attRecordItem.setActualMinute(MapUtils.getInteger(record, "actual_minute"));
// // 有效出勤分钟数
// attRecordItem.setValidMinute(MapUtils.getInteger(record, "valid_minute"));
// // 迟到次数数据
// attRecordItem.setLateCountData(MapUtils.getString(record, "late_count_data"));
// // 迟到分钟数数据
// attRecordItem.setLateMinuteData(MapUtils.getString(record, "late_minute_data"));
// // 迟到总次数
// attRecordItem.setLateCountTotal(MapUtils.getInteger(record, "late_count_total"));
// // 迟到总分钟数
// attRecordItem.setLateMinuteTotal(MapUtils.getInteger(record, "late_minute_total"));
// // 早退次数数据
// attRecordItem.setEarlyCountData(MapUtils.getString(record, "early_count_data"));
// // 早退分钟数数据
// attRecordItem.setEarlyMinuteData(MapUtils.getString(record, "early_minute_data"));
// // 早退总次数
// attRecordItem.setEarlyCountTotal(MapUtils.getInteger(record, "early_count_total"));
// // 早退总分钟数
// attRecordItem.setEarlyMinuteTotal(MapUtils.getInteger(record, "early_minute_total"));
// // 旷工分钟数
// attRecordItem.setAbsentMinute(MapUtils.getInteger(record, "absent_minute"));
// // 加班分钟数-平时
// attRecordItem.setOvertimeUsualMinute(MapUtils.getInteger(record, "overtime_usual_minute"));
// // 加班分钟数-休息
// attRecordItem.setOvertimeRestMinute(MapUtils.getInteger(record, "overtime_rest_minute"));
// // 加班分钟数-节日
// attRecordItem.setOvertimeHolidayMinute(MapUtils.getInteger(record, "overtime_holiday_minute"));
// // 加班分钟数
// attRecordItem.setOvertimeMinute(MapUtils.getInteger(record, "overtime_minute"));
// // 请假分钟数
// attRecordItem.setLeaveMinute(MapUtils.getInteger(record, "leave_minute"));
// // 出差分钟数
// attRecordItem.setTripMinute(MapUtils.getInteger(record, "trip_minute"));
// // 外出分钟数
// attRecordItem.setOutMinute(MapUtils.getInteger(record, "out_minute"));
// /*//旷工天数
// attRecordItem.setCardValidCount(MapUtils.getInteger(record,"card_valid_count"));
// //请假天数
// attRecordItem.setCardValidCount(MapUtils.getInteger(record,"card_valid_count"));
// //出差天数
// attRecordItem.setCardValidCount(MapUtils.getInteger(record,"card_valid_count"));
// //外出天数
// attRecordItem.setCardValidCount(MapUtils.getInteger(record,"card_valid_count"));*/
// // 应出勤天数
// Object should_days = MapUtils.getObject(record, "should_days");
// if (should_days instanceof BigDecimal) {
// attRecordItem.setShouldDays((BigDecimal)should_days);
// } else {
// attRecordItem.setShouldDays(new BigDecimal("0.00"));
// }
// // 实际出勤天数
// Object actual_days = MapUtils.getObject(record, "actual_days");
// if (actual_days instanceof BigDecimal) {
// attRecordItem.setActualDays((BigDecimal)actual_days);
// } else {
// attRecordItem.setActualDays(new BigDecimal("0.00"));
// }
// // 有效出勤天数
// Object valid_days = MapUtils.getObject(record, "valid_days");
// if (valid_days instanceof BigDecimal) {
// attRecordItem.setValidDays((BigDecimal)valid_days);
// } else {
// attRecordItem.setValidDays(new BigDecimal("0.00"));
// }
// // 旷工天数
// Object absent_days = MapUtils.getObject(record, "absent_days");
// if (absent_days instanceof BigDecimal) {
// attRecordItem.setAbsentDays((BigDecimal)absent_days);
// } else {
// attRecordItem.setAbsentDays(new BigDecimal("0.00"));
// }
// // 请假天数
// Object leave_days = MapUtils.getObject(record, "leave_days");
// if (leave_days instanceof BigDecimal) {
// attRecordItem.setLeaveDays((BigDecimal)leave_days);
// } else {
// attRecordItem.setLeaveDays(new BigDecimal("0.00"));
// }
// // 出差天数
// Object trip_days = MapUtils.getObject(record, "trip_days");
// if (trip_days instanceof BigDecimal) {
// attRecordItem.setTripDays((BigDecimal)trip_days);
// } else {
// attRecordItem.setTripDays(new BigDecimal("0.00"));
// }
// // 外出天数
// Object out_days = MapUtils.getObject(record, "out_days");
// if (out_days instanceof BigDecimal) {
// attRecordItem.setOutDays((BigDecimal)out_days);
// } else {
// attRecordItem.setOutDays(new BigDecimal("0.00"));
// }
// // 异常排班类型
// attRecordItem.setExceptionSchType(MapUtils.getInteger(record, "exception_sch_type"));
// // 备注
// attRecordItem.setRemark(MapUtils.getString(record, "remark"));
// attRecordItems.add(attRecordItem);
// }
// attRecordService.handlerTransfer(attRecordItems);
// }
// }
// }
// }
//
// // /**
// // * 定时器 数据转换
// // *
// // * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// // * @date 2018/12/7 15:45
// // * @param
// // * @return void
// // */
// // @Deprecated
// // private void timingDataTransfer() {
// // // List<Map<String, Object>> timingMapList = systemDataMigrationService.excuteSql("select * from att_timing");
// // // 需要对定时数据进行去重 旧架构没做重复的验证 add by jinxian.huang 2019-07-18
// // List<Map<String, Object>> timingMapList = systemDataMigrationService.excuteSql(
// // "select * from att_timing where id in (select max(id) from att_timing group by job_cron,time_calc_interval)");
// // List<AttTimingItem> attTimingItems = new ArrayList<>();
// //
// // AttTimingItem attTimingItem = null;
// // for (Map<String, Object> timing : timingMapList) {
// // attTimingItem = new AttTimingItem();
// // // 时间计算间隔
// // attTimingItem.setTimeCalcInterval(MapUtils.getString(timing, "time_calc_interval"));
// // // 时间计算频率（时、日、周、月）
// // attTimingItem.setTimeCalcFrequency(MapUtils.getString(timing, "time_calc_frequency"));
// // // 任务名称
// // attTimingItem.setJobName(MapUtils.getString(timing, "job_name"));
// // // 任务执行类（com.zk.att.quartz.AttTimingQuartzJob）
// // attTimingItem.setJobClass(MapUtils.getString(timing, "job_class"));
// // // 任务Cron表达式
// // attTimingItem.setJobCron(MapUtils.getString(timing, "job_cron"));
// // // 任务状态（启动、禁用），默认启动
// // attTimingItem.setJobStatus(getBoolean(timing, "job_status"));
// //
// // attTimingItems.add(attTimingItem);
// // }
// // attTimingService.handlerTransfer(attTimingItems);
// // }
//
// /**
// * 时间段 数据转换
// *
// * @param
// * @return void
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @date 2018/12/7 15:45
// */
// private void timeSlotDataTransfer() {
// List<Map<String, Object>> timeSlotMapList = systemDataMigrationService.excuteSql("select * from att_timeslot");
// List<AttTimeSlotItem> attTimeSlotItems = new ArrayList<>();
//
// AttTimeSlotItem attTimeSlotItem = null;
// for (Map<String, Object> timeSlot : timeSlotMapList) {
// attTimeSlotItem = new AttTimeSlotItem();
//
// // 时段类型（0：正常时间段，1：弹性时间段）
// attTimeSlotItem.setPeriodType(MapUtils.getShort(timeSlot, "period_type"));
// // 时段编号
// attTimeSlotItem.setPeriodNo(MapUtils.getString(timeSlot, "period_no"));
// // 时段名称
// attTimeSlotItem.setPeriodName(MapUtils.getString(timeSlot, "period_name"));
// // 开始签到时间
// attTimeSlotItem.setStartSignInTime(MapUtils.getString(timeSlot, "start_sign_in_time"));
// // 上班时间
// attTimeSlotItem.setToWorkTime(MapUtils.getString(timeSlot, "to_work_time"));
// // 结束签到时间
// attTimeSlotItem.setEndSignInTime(MapUtils.getString(timeSlot, "end_sign_in_time"));
// // 允许迟到分钟数
// attTimeSlotItem.setAllowLateMinutes(MapUtils.getShort(timeSlot, "allow_late_minutes"));
// // 必须签到（false：否/0，true：是/1）
// attTimeSlotItem.setIsMustSignIn(getBoolean(timeSlot, "is_must_sign_in"));
//
// // 开始签退时间
// attTimeSlotItem.setStartSignOffTime(MapUtils.getString(timeSlot, "start_sign_off_time"));
// // 下班时间
// attTimeSlotItem.setOffWorkTime(MapUtils.getString(timeSlot, "off_work_time"));
// // 结束签退时间
// attTimeSlotItem.setEndSignOffTime(MapUtils.getString(timeSlot, "end_sign_off_time"));
// // 允许早退分钟数
// attTimeSlotItem.setAllowEarlyMinutes(MapUtils.getShort(timeSlot, "allow_early_minutes"));
// // 必须签退（false：否/0，true：是/1）
// attTimeSlotItem.setIsMustSignOff(getBoolean(timeSlot, "is_must_sign_off"));
//
// // 工作时长（分钟）
// attTimeSlotItem.setWorkingHours(MapUtils.getShort(timeSlot, "working_hours"));
// // 是否段间扣除（false：否/0，true：是/1）
// attTimeSlotItem.setIsSegmentDeduction(getBoolean(timeSlot, "is_segment_deduction"));
//
// // 段间扣除
// attTimeSlotItem.setInterSegmentDeduction(MapUtils.getShort(timeSlot, "inter_segment_deduction"));
// // 开始段间时间 start_segment_time
// attTimeSlotItem.setStartSegmentTime(MapUtils.getString(timeSlot, "start_segment_time"));
// // 结束段间时间 end_segment_time
// attTimeSlotItem.setEndSegmentTime(MapUtils.getString(timeSlot, "end_segment_time"));
// // 记为工作日数
// attTimeSlotItem.setMarkWorkingDays(MapUtils.getString(timeSlot, "mark_working_days"));
// // 延时是否计加班（false：否/0，true：是/1）
// attTimeSlotItem.setIsDelayCountOvertime(getBoolean(timeSlot, "is_delay_count_overtime"));
//
// // 起记加班时间
// attTimeSlotItem.setStartOvertime(MapUtils.getString(timeSlot, "start_overtime"));
// // 提前是否计加班（false：否/0，true：是/1）
// attTimeSlotItem.setIsAdvanceCountOvertime(getBoolean(timeSlot, "is_advance_count_overtime"));
//
// // 签到早于时间
// attTimeSlotItem.setSignInAdvanceTime(MapUtils.getString(timeSlot, "sign_in_advance_time"));
// // 延后是否计加班（false：否/0，true：是/1）
// attTimeSlotItem.setIsPostponeCountOvertime(getBoolean(timeSlot, "is_postpone_count_overtime"));
//
// // 签退晚于时间
// attTimeSlotItem.setSignOutPosponeTime(MapUtils.getString(timeSlot, "sign_out_pospone_time"));
// // 是否计加班（false：否/0，true：是/1）
// attTimeSlotItem.setIsCountOvertime(getBoolean(timeSlot, "is_count_overtime"));
//
// // 时段类型 0：正常时间段 调整时间为相对上下班时间的分钟数 by ljf 2019/9/29
// if (0 == MapUtils.getShort(timeSlot, "period_type")) {
// // 上班时间-开始签到时间
// Integer beforeToWorkMinutes = diffTime(MapUtils.getString(timeSlot, "start_sign_in_time"),
// MapUtils.getString(timeSlot, "to_work_time"));
// // 上班前n分钟内签到有效
// attTimeSlotItem.setBeforeToWorkMinutes(beforeToWorkMinutes);
//
// // 上班后n分钟内签到有效
// Integer afterToWorkMinutes = diffTime(MapUtils.getString(timeSlot, "to_work_time"),
// MapUtils.getString(timeSlot, "end_sign_in_time"));
// attTimeSlotItem.setAfterToWorkMinutes(afterToWorkMinutes);
//
// // 下班前n分钟内签到有效
// Integer beforeOffWorkMinutes = diffTime(MapUtils.getString(timeSlot, "start_sign_off_time"),
// MapUtils.getString(timeSlot, "off_work_time"));
// attTimeSlotItem.setBeforeOffWorkMinutes(beforeOffWorkMinutes);
// // 下班前n分钟内签到有效
// Integer afterOffWorkMinutes = diffTime(MapUtils.getString(timeSlot, "off_work_time"),
// MapUtils.getString(timeSlot, "end_sign_off_time"));
// attTimeSlotItem.setAfterOffWorkMinutes(afterOffWorkMinutes);
//
// // 上班n分钟前签到记加班
// Integer beforeWorkOvertimeMinutes = diffTime(MapUtils.getString(timeSlot, "sign_in_advance_time"),
// MapUtils.getString(timeSlot, "to_work_time"));
// attTimeSlotItem.setBeforeWorkOvertimeMinutes(beforeWorkOvertimeMinutes);
// // 上班前最短加班分钟数
// attTimeSlotItem.setMinBeforeOvertimeMinutes(0);
// // 下班n分钟后开始记加班
// Integer afterWorkOvertimeMinutes = diffTime(MapUtils.getString(timeSlot, "off_work_time"),
// MapUtils.getString(timeSlot, "sign_out_pospone_time"));
// attTimeSlotItem.setAfterWorkOvertimeMinutes(afterWorkOvertimeMinutes);
// // 下班后最短加班分钟数
// attTimeSlotItem.setMinAfterOvertimeMinutes(0);
//
// // 是否启动工作时长
// attTimeSlotItem.setEnableWorkingHours("false");
//
// // 是否启动弹性上班(true:启用。false：不启用)
// attTimeSlotItem.setEnableFlexibleWork("false");
// // 可提前上班分钟数
// attTimeSlotItem.setAdvanceWorkMinutes(0);
// // 可延后下班分钟数
// attTimeSlotItem.setDelayedWorkMinutes(0);
// }
//
// attTimeSlotItems.add(attTimeSlotItem);
//
// }
// attTimeSlotService.handlerTransfer(attTimeSlotItems);
// }
//
// /**
// * 分组 数据转换
// *
// * @param
// * @return void
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @date 2018/12/7 11:27
// */
// private void groupDataTransfer() {
// List<Map<String, Object>> groupMapList = systemDataMigrationService.excuteSql("select * from att_group");
// List<AttGroupItem> attGroupItems = new ArrayList<>();
// AttGroupItem attGroupItem = null;
// for (Map<String, Object> group : groupMapList) {
// attGroupItem = new AttGroupItem();
// // 分组编号
// // attGroupItem.setGroupNo(MapUtils.getString(group,"group_no"));
// // 分组名称
// attGroupItem.setGroupName(MapUtils.getString(group, "group_name"));
// // 备注
// attGroupItem.setRemark(MapUtils.getString(group, "remark"));
//
// attGroupItems.add(attGroupItem);
// }
// attGroupService.handlerTransfer(attGroupItems);
// }
//
// /**
// * 调休补班数据转换
// *
// * @param
// * @return void
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @date 2018/12/7 10:56
// */
// public void adjustDataTransfer() {
// // List<Map<String, Object>> adjustMapList = systemDataMigrationService.excuteSql("select
// //
// t.person_pin,t.adjust_type,t.adjust_date,t.operate_datetime,t.remark,y.apply_no,y.apply_type,y.flow_status,z.flow_no,x.shift_no
// // from att_adjust t left join att_apply y on t.id = y.adjust_id left join att_flow z on y.flow_id = z.id left
// // join att_shift x on t.shift_id = x.id ");
// List<Map<String, Object>> adjustMapList = systemDataMigrationService.excuteSql(
// "select t.person_pin,t.adjust_type,t.adjust_date,t.operate_datetime,t.remark,x.shift_no from att_adjust t left join
// att_shift x on t.shift_id = x.id ");
// List<AttAdjustItem> attAdjustItems = new ArrayList<>();
// AttAdjustItem attAdjustItem = null;
// for (Map<String, Object> adjust : adjustMapList) {
// attAdjustItem = new AttAdjustItem();
// /*//部门id
// attAdjustItem.setDeptId(MapUtils.getString(adjust,"dept_id"));
// //部门编号
// attAdjustItem.setDeptCode(MapUtils.getString(adjust,"dept_no"));
// //部门名称
// attAdjustItem.setDeptName(MapUtils.getString(adjust,"dept_name"));
// //人员id
// attAdjustItem.setPersonId(MapUtils.getString(adjust,"person_id"));*/
// // 人员编号
// attAdjustItem.setPersonPin(MapUtils.getString(adjust, "person_pin"));
// /*//姓名
// attAdjustItem.setPersonName(MapUtils.getString(adjust,"person_name"));
// //英文（lastName）
// attAdjustItem.setPersonLastName(MapUtils.getString(adjust,"person_last_name"));*/
// // 调整类型
// attAdjustItem.setAdjustType(MapUtils.getShort(adjust, "adjust_type"));
// // 调整日期
// Object adjust_date = MapUtils.getObject(adjust, "adjust_date");
// if (adjust_date instanceof Date) {
// attAdjustItem.setAdjustDate((Date)adjust_date);
// }
// // 操作日期时间
// Object operate_datetime = MapUtils.getObject(adjust, "operate_datetime");
// if (operate_datetime instanceof Date) {
// attAdjustItem.setOperateDatetime((Date)operate_datetime);
// }
// // 备注
// attAdjustItem.setRemark(MapUtils.getString(adjust, "remark"));
// // 班次id
// attAdjustItem.setShiftNo(MapUtils.getString(adjust, "shift_no"));
//
// /* attAdjustItem.setApplyNo(MapUtils.getString(adjust,"apply_no"));
//
// attAdjustItem.setApplyType(MapUtils.getString(adjust,"apply_type"));
//
// attAdjustItem.setFlowStatus(MapUtils.getString(adjust,"flow_status"));
//
// attAdjustItem.setFlowNo(MapUtils.getString(adjust,"flow_no"));*/
//
// attAdjustItems.add(attAdjustItem);
//
// }
// attAdjustService.handlerTransfer(attAdjustItems);
//
// }
//
// /**
// * 节假日的数据转换
// *
// * @return void
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @date 2018/12/6 19:52
// */
// public void holidayDataTransfer() {
// List<Map<String, Object>> holidayMapList = systemDataMigrationService.excuteSql("select * from att_holiday");
// List<AttHolidayItem> holidayItems = new ArrayList<>();
// AttHolidayItem attHolidayItem = null;
// for (Map<String, Object> holiday : holidayMapList) {
//
// attHolidayItem = new AttHolidayItem();
// // 节假日编号
// // attHolidayItem.setHolidayNo(MapUtils.getString(holiday,"holiday_no"));
// // 节假日名称
// attHolidayItem.setHolidayName(MapUtils.getString(holiday, "holiday_name"));
// // 开始日期时间
// Object start_datetime = holiday.get("start_datetime");
// if (start_datetime instanceof Date) {
// attHolidayItem.setStartDatetime((Date)start_datetime);
// }
// // 结束日期时间
// Object end_datetime = holiday.get("end_datetime");
// if (end_datetime instanceof Date) {
// attHolidayItem.setEndDatetime((Date)end_datetime);
// }
//
// // 是否全部人放假-（false：否/0，true：是/1）
// attHolidayItem.setIsAllTheHolidays(getBoolean(holiday, "is_all_the_holidays"));
//
// // 备注
// attHolidayItem.setRemark(MapUtils.getString(holiday, "remark"));
//
// holidayItems.add(attHolidayItem);
// }
// attHolidayService.handlerTransfer(holidayItems);
// }
//
// /**
// * 获取map里key的 boolean值 默认返回false
// *
// * @param map
// * @param key
// * @return
// */
// private Boolean getBoolean(Map map, Object key) {
// return getBoolean(map, key, false);
// }
//
// /**
// * 获取map里key的 boolean值
// *
// * @param map
// * @param key
// * @param def
// * 默认值
// * @return
// */
// private Boolean getBoolean(Map map, Object key, Boolean def) {
// if (null == map || null == map.get(key)) {
// return def;
// }
// Object answer = map.get(key);
// if (answer instanceof Boolean) {
// return (Boolean)answer;
// }
// if (answer instanceof String) {
// String s = (String)answer;
// boolean val = (s != null) && (s.equalsIgnoreCase("true") || s.equalsIgnoreCase("1"));
// return new Boolean(val);
// }
// if (answer instanceof Number) {
// Number n = (Number)answer;
// return n.intValue() != 0 ? Boolean.TRUE : Boolean.FALSE;
// }
// // 添加字符判断 added by jinxian.huang 2019-08-07
// if (answer instanceof Character) {
// Character c = (Character)answer;
// return c.charValue() != '0' ? Boolean.TRUE : Boolean.FALSE;
// }
// return def;
// }
//
// /**
// * 时间转分钟数 09:00 09:30 -> 30
// *
// * @param startTime
// * 开始时间
// * @param endTime
// * 结束时间
// * @return
// */
// private Integer diffTime(String startTime, String endTime) {
// int minutes = 0;
// try {
// Date startDatetime = DateUtil.stringToDate(startTime, DateUtil.DateStyle.HH_MM);
// Date endDatetime = DateUtil.stringToDate(endTime, DateUtil.DateStyle.HH_MM);
// if (StringUtils.compare(startTime, endTime) > 0) {
// startDatetime = DateUtil.addDay(startDatetime, -1);
// }
// long timeLong = endDatetime.getTime() - startDatetime.getTime();
// minutes = (int)timeLong / (1000 * 60);
// } catch (Exception e) {
// }
// return minutes;
// }
//
// @Override
// public void handlerUpgrade(SystemDataUpgradeProcess process) {
// String currentVer = process.getCurrentVersion().split("_")[1];// 当前版本(格式：V5000_1.0.0_R)
// String targertVer = process.getTargetVersion(); // 最新版本
// if (targertVer.contains("SNAPSHOT")) {
// targertVer = "1.0.0"; // 快照版本不进入升级流程(测试时自己改版本)
// } else {
// targertVer = targertVer.split("_")[1];
// }
// // 【重要提醒】新架构考勤是从2.2.0版本(V5000 3.0)才提供升级接口，【第一版本的升级支持V2.1.0升级到V2.2.0】
// // 最新版本大于当前版本才需要升级
// if (targertVer.compareTo(currentVer) > 0) {
// // TODO 升级到2.0.1_R/2.0.2_R/2.0.3_R/2.0.4_R版本
// if (currentVer.compareTo("2.0.4") < 0) {
// // currentVer = "2.0.4";
// }
// if (currentVer.compareTo("2.1.0") == 0) {
// // upgradeTo_210(currentVer);
// }
// if (currentVer.compareTo("2.2.0") < 0 && targertVer.compareTo("2.2.0") >= 0) {
// upgradeTo_220();
// currentVer = "2.2.0";
// }
// if (currentVer.compareTo("2.3.0") < 0 && targertVer.compareTo("2.3.0") >= 0) {
// upgradeTo_230();
// currentVer = "2.3.0";
// }
//
// if (currentVer.compareTo("2.3.2") < 0 && targertVer.compareTo("2.3.2") >= 0) {
// upgradeTo_232();
// currentVer = "2.3.2";
// }
// }
// }
//
// /**
// * 升级到2.3.2_R版本
// */
// public void upgradeTo_232() {
//
// // 考勤初始化假种的key改为国际化语言；
// attLeaveTypeService.upgradeTo_232();
// }
//
// /**
// * 升级到2.3.0_R版本
// */
// public void upgradeTo_230() {
//
// // 增加未签到未签退规则参数初始化
// baseSysParamService.initData(new BaseSysParamItem("att.baseRule.noSignInCountType", "absent",
// I18nUtil.i18nCode("att_rule_noSignInCountType")));
// baseSysParamService.initData(new BaseSysParamItem("att.baseRule.noSignInCountLateMinute", "30",
// I18nUtil.i18nCode("att_rule_noSignInCountLateMinute")));
// baseSysParamService.initData(new BaseSysParamItem("att.baseRule.noSignOffCountType", "absent",
// I18nUtil.i18nCode("att_rule_noSignOffCountType")));
// baseSysParamService.initData(new BaseSysParamItem("att.baseRule.noSignOffCountEarlyMinute", "60",
// I18nUtil.i18nCode("att_rule_noSignOffCountEarlyMinute")));
// baseSysParamService.initData(new BaseSysParamItem("att.other.noCheckInIncomplete", "├",
// I18nUtil.i18nCode("att_rule_noCheckInIncomplete")));
// baseSysParamService.initData(new BaseSysParamItem("att.other.noCheckOutIncomplete", "┤",
// I18nUtil.i18nCode("att_rule_noCheckOutIncomplete")));
//
// }
//
// /**
// * 升级到2.2.0_R版本
// *
// * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
// * @since 2019年12月27日 下午14:48:18
// */
// public void upgradeTo_220() {
// // Att_TimeSlot
// /**
// * BEFORE_TO_WORK_MINUTES (Integer) 上班前n分钟内签到有效 AFTER_TO_WORK_MINUTES (Integer) 上班后n分钟内签到有效
// * BEFORE_OFF_WORK_MINUTES (Integer) 下班前n分钟内签到有效 AFTER_OFF_WORK_MINUTES (Integer) 下班后n分钟内签到有效
// * BEFORE_WORK_OVERTIME_MINUTES (Integer) 上班n分钟前签到记加班 MIN_BEFORE_OVERTIME_MINUTES (Integer) 上班前最短加班分钟数
// * AFTER_WORK_OVERTIME_MINUTES (Integer) 下班n分钟后开始记加班 MIN_AFTER_OVERTIME_MINUTES (Integer) 下班后最短加班分钟数
// * ENABLE_FLEXIBLE_WORK (String 10) 是否启动弹性上班(true:启用。false：不启用)--默认为false
// */
// attTimeSlotService.upgradeTo_220();
// // ATT_OVERTIME(新增OVERTIME_LONG--加班时长)
// attOvertimeService.upgradeTo_220();
// // ATT_SHIFT：增加“工作类型”字段，必填字段，字符串类型，默认为正常上班；
// attShiftService.upgradeTo_220();
// }
//
// /**
// * 升级到2.1.0_R版本
// *
// * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
// * @since 2019年6月26日 下午7:41:18
// */
// private void upgradeTo_210(String currentVer) {
// // 只当前版本是2.1.0，需要删除异常表的唯一键（2.1.0版本才加上的），小于2.1.0版本的不需要
// if (currentVer.compareTo("2.1.0") == 0) {
// try {
// // 删除所有异常表的businessKey的唯一键（因为2.1.1未启用工作流，businessKey的值一直为空，在Oracle下无法重复保存多条）
// attExceptionDataService
// .executeSQL("ALTER TABLE att_adjust DROP CONSTRAINT uk_1j411w3sasp8br8vgqhrsiv80");
// attExceptionDataService
// .executeSQL("ALTER TABLE att_class DROP CONSTRAINT uk_503vst9qy457kqicduen7ps65");
// attExceptionDataService
// .executeSQL("ALTER TABLE att_leave DROP CONSTRAINT uk_fl9loey895pljbbynoh9dopga");
// attExceptionDataService.executeSQL("ALTER TABLE att_out DROP CONSTRAINT uk_6obfepy9cmx2u2lbwwm6seuwd");
// attExceptionDataService
// .executeSQL("ALTER TABLE att_overtime DROP CONSTRAINT uk_tlaeu95wnhfob6030x1cpf9pm");
// attExceptionDataService.executeSQL("ALTER TABLE att_sign DROP CONSTRAINT uk_ptgu1ik39l3xr6p6bog6t7g4h");
// attExceptionDataService.executeSQL("ALTER TABLE att_trip DROP CONSTRAINT uk_hauijgxjmdpsrp6ev8w1aqv6e");
// } catch (Exception e) {
// log.error("exception = ", e);
// }
// } else if (currentVer.compareTo("2.1.0") < 0) {
// // 2.1.0版本在临时排班表（att_tempsch）增加一个字段（pers_person_pin），解决人员离职后，通过人员ID找不到的问题
// AttTempSchItem tempSchItem = new AttTempSchItem();
// tempSchItem.setTempType((short)2);// 查临时排班中的所有人员临时排班
// List<AttTempSchItem> tempSchItemList = attTempSchService.getByCondition(tempSchItem);
// Collection<String> personIdList = CollectionUtil.getPropertyList(tempSchItemList,
// AttTempSchItem::getPersonId, AttConstant.COMM_DEF_VALUE);
// List<PersPersonItem> personItemList = persPersonService.getSimpleItemsByIds(personIdList);
// Map<String, PersPersonItem> personIdMap =
// CollectionUtil.listToKeyMap(personItemList, PersPersonItem::getId);
// tempSchItemList.forEach(item -> {
// if (StringUtils.isBlank(item.getPersonPin())) {
// PersPersonItem personItem = personIdMap.get(item.getPersonId());
// if (Objects.nonNull(personItem)) {
// item.setPersonPin(personItem.getPin());
// AttTempSch tempSch = attTempSchDao.getOne(item.getId());// 因为临时排班没用到简单的item对象保存，所以直接写在这里，后续有共用再抽出来
// ModelUtil.copyPropertiesIgnoreNullWithProperties(item, tempSch, "id");
// attTempSchDao.save(tempSch);
// }
// }
// });
// }
// }
//
// /**
// * TODO 升级到2.1.1_R版本
// * <p>
// * 【从2.1.X升级到2.2.X不需要执行】
// *
// * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
// * @since 2019年6月25日 下午3:24:41
// */
// private void upgradeTo_211() {
// try {
// // 更新异常表的flowStatus为2，解决2.0及以前的版本升级到2.1.1后，异常申请信息查不到的问题，因为查询默认条件是flowStatus=2
// attExceptionDataService.executeSQL("UPDATE att_adjust set flow_status=2");
// attExceptionDataService.executeSQL("UPDATE att_class set flow_status=2");
// attExceptionDataService.executeSQL("UPDATE att_leave set flow_status=2");
// attExceptionDataService.executeSQL("UPDATE att_out set flow_status=2");
// attExceptionDataService.executeSQL("UPDATE att_overtime set flow_status=2");
// attExceptionDataService.executeSQL("UPDATE att_sign set flow_status=2");
// attExceptionDataService.executeSQL("UPDATE att_trip set flow_status=2");
// } catch (Exception e) {
// log.error("exception = ", e);
// }
// }
//
// /**
// * 1、删除原始记录的唯一键
// *
// * @return void
// * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
// * @date 2020/9/22 15:28
// * @since 1.0.0
// */
// public void upgradeTo_1200() {
// try {
// // 删除原始记录的唯一键
// jdbcTemplate.execute("ALTER TABLE att_transaction DROP CONSTRAINT att_tran_pin_datetime_idx");
// } catch (Exception e) {
// log.info("AttVer12_0_0 att_transaction的外键约束att_tran_pin_datetime_idx不存在");
// }
// }
//
// /**
// * 迁移 ZKTime11.0 数据到att-pro
// *
// * @param process
// */
// private void handlerTransferZKTime_11_0(SystemDataTransferProcess process) {
// // 迁移
// long beginTime = System.currentTimeMillis();
// int beginProgress = process.getBeginProgress();
// int endProgress = process.getEndProgress();
// int tableSize = 7;
// int eachProgess = (endProgress - beginProgress) / tableSize;
// progressCache.setProcess(
// ProcessBean.createNormalSingleProcess(beginProgress, "start Person Module data transfer......"));
//
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "empzone Data Transfer"));
// // 区域人员 empzone
// handlerTransferZKTimeEmpzone();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "empzone Data Transfer End"));
//
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "checkinout Data Transfer"));
// // 考勤原始记录 checkinout
// handlerTransferZKTimeCheckinout();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "checkinout Data Transfer End"));
//
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "holidays Data Transfer"));
// // 节假日 holidays
// handlerTransferZKTimeHolidays();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "holidays Data Transfer End"));
//
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "checkexact Data Transfer"));
// // 补签 checkexact
// handlerTransferZKTimeCheckexact();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "checkexact Data Transfer End"));
//
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "leaveclass Data Transfer"));
// // 请假类别 LeaveClass
// handlerTransferZKTimeLeaveClass();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "leaveclass Data Transfer End"));
//
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "user_speday Data Transfer"));
// // 请假 USER_SPEDAY
// handlerTransferZKTimeUserSpeday();
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "user_speday Data Transfer End"));
//
// progressCache
// .setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "user_overtime Data Transfer"));
// // 加班 USER_OVERTIME
// handlerTransferZKTimeUserOvertime();
// progressCache
// .setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "user_overtime Data Transfer End"));
//
// // 调休 daysoff
// // handlerTransferZKTimeDaysoff();
//
// // 迁移该模块总时长
// progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + eachProgess * tableSize + 1,
// "Att Module Data End Transfer Time taken:" + (System.currentTimeMillis() - beginTime) + "ms"));
// }
//
// /**
// * 迁移 区域人员
// */
// private void handlerTransferZKTimeEmpzone() {
// List<Map<String, Object>> data = systemDataMigrationService.excuteSql(
// "select a.*,b.badgenumber as userinfo_badgenumber,c.code as acc_zone_code from empzone a left join userinfo b on
// a.userid=b.userid left join acc_zone c on a.zone_id = c.id");
// List<AttAreaPersonItem> attAreaPersonItems = new ArrayList<>();
// AttAreaPersonItem attAreaPersonItem = null;
// for (Map<String, Object> map : data) {
// attAreaPersonItem = new AttAreaPersonItem();
// // 区域id
// attAreaPersonItem.setAuthAreaNo(MapUtils.getString(map, "acc_zone_code"));
// // 人员id
// attAreaPersonItem.setPin(MapUtils.getString(map, "userinfo_badgenumber"));
// attAreaPersonItems.add(attAreaPersonItem);
// }
// attAreaPersonService.handlerTransfer(attAreaPersonItems);
// }
//
// private void handlerTransferZKTimeCheckinout() {
//
// attTransactionService.deleteDataTransfer();
//
// List<Map<String, Object>> count =
// systemDataMigrationService.excuteSql("select count(1) as count_number from checkinout");
// long totalSize = MapUtils.getLong(count.get(0), "count_number", 0L);
// if (totalSize < limitSize) {
// long pageCount = totalSize / splitSize;
// if (totalSize % splitSize > 0) {
// pageCount = pageCount + 1;
// }
// for (int pageIndex = 0; pageIndex < pageCount; pageIndex++) {
// List<Map<String, Object>> attTransactionMapList =
// systemDataMigrationService.excutePageSql("checkinout", pageIndex * splitSize, splitSize);
// AttTransactionItem attTransactionItem = null;
// List<AttTransactionItem> attTransactionItemList = new ArrayList<>();
//
// List<Map<String, Object>> departmentMap =
// systemDataMigrationService.excuteSql("select deptid,deptnumber from departments");
// Map<String, String> deptId2DeptNumberMap = new HashMap<>();
// if (!CollectionUtil.isEmpty(departmentMap)) {
// for (Map<String, Object> map : departmentMap) {
// String deptid = MapUtils.getString(map, "deptid");
// String deptnumber = MapUtils.getString(map, "deptnumber");
// deptId2DeptNumberMap.put(deptid, deptnumber);
// }
// }
//
// List<Map<String, Object>> userInfoMap =
// systemDataMigrationService.excuteSql("select userid,badgenumber,defaultdeptid from userinfo");
// Map<String, String> userId2BadgeNumberMap = new HashMap<>();
// Map<String, String> userId2DeptNumberMap = new HashMap<>();
// if (!CollectionUtil.isEmpty(userInfoMap)) {
// for (Map<String, Object> map : userInfoMap) {
// String userid = MapUtils.getString(map, "userid");
// String badgenumber = MapUtils.getString(map, "badgenumber");
// String defaultdeptid = MapUtils.getString(map, "defaultdeptid");
// userId2BadgeNumberMap.put(userid, badgenumber);
// userId2DeptNumberMap.put(userid, MapUtils.getString(deptId2DeptNumberMap, defaultdeptid));
// }
// }
//
// for (Map<String, Object> map : attTransactionMapList) {
// attTransactionItem = new AttTransactionItem();
//
// String userid = MapUtils.getString(map, "userid");
// attTransactionItem.setPersonPin(MapUtils.getString(userId2BadgeNumberMap, userid));
// attTransactionItem.setDeptCode(MapUtils.getString(userId2DeptNumberMap, userid));
// Date checktime = null;
// Object checktimeObject = MapUtils.getObject(map, "checktime");
// if (checktimeObject instanceof Date) {
// checktime = (Date)checktimeObject;
// }
// attTransactionItem.setAttDatetime(checktime);
// attTransactionItem.setAttDate(DateUtil.getDate(checktime));
// attTransactionItem.setAttTime(DateUtil.getTime(checktime));
// attTransactionItem.setDeviceSn(MapUtils.getString(map, "sn"));
// attTransactionItem.setMark(ConstUtil.SYSTEM_MODULE_ATT);
//
// attTransactionItemList.add(attTransactionItem);
// }
// attTransactionService.handlerTransfer(attTransactionItemList);
// }
// }
// }
//
// /**
// * 迁移节假日 holidays
// */
// private void handlerTransferZKTimeHolidays() {
// List<Map<String, Object>> holidaysMapList =
// systemDataMigrationService.excuteSql("select * from holidays where deltag = 0");
// if (CollectionUtil.isEmpty(holidaysMapList)) {
// return;
// }
// AttHolidayItem attHolidayItem = null;
// List<AttHolidayItem> attHolidayItemList = new ArrayList<>();
// for (Map<String, Object> map : holidaysMapList) {
// String holidayname = MapUtils.getString(map, "holidayname");
// Date starttime = null;
// Object starttimeObject = MapUtils.getObject(map, "starttime");
// if (starttimeObject instanceof Date) {
// starttime = (Date)starttimeObject;
// }
// Short duration = MapUtils.getShort(map, "duration");
// Date endDatetime = DateUtil.addDay(starttime, duration - 1);
//
// attHolidayItem = new AttHolidayItem();
// attHolidayItem.setHolidayName(holidayname);
// attHolidayItem.setStartDatetime(starttime);
// attHolidayItem.setDayNumber(duration);
// attHolidayItem.setEndDatetime(endDatetime);
//
// attHolidayItemList.add(attHolidayItem);
// }
// attHolidayService.handlerTransfer(attHolidayItemList);
// }
//
// /**
// * 迁移补签 checkexact
// */
// private void handlerTransferZKTimeCheckexact() {
// List<Map<String, Object>> checkexactMapList = systemDataMigrationService.excuteSql(
// "select a.*,b.badgenumber as userinfo_badgenumber from checkexact a left join userinfo b on a.userid=b.userid where
// a.state = 2");
// if (CollectionUtil.isEmpty(checkexactMapList)) {
// return;
// }
// AttSignItem attSignItem = null;
// List<AttSignItem> attSignItemList = new ArrayList<>();
// for (Map<String, Object> map : checkexactMapList) {
//
// Date checktime = null;
// Object checktimeObject = MapUtils.getObject(map, "checktime");
// if (checktimeObject instanceof Date) {
// checktime = (Date)checktimeObject;
// }
// attSignItem = new AttSignItem();
// attSignItem.setPersonPin(MapUtils.getString(map, "userinfo_badgenumber"));
// attSignItem.setSignDatetime(checktime);
// attSignItem.setRemark(MapUtils.getString(map, "yuyin"));
// attSignItem.setFlowStatus(AttConstant.FLOW_STATUS_COMPLETE);
// attSignItemList.add(attSignItem);
// }
// attSignService.handlerTransfer(attSignItemList);
// }
//
// /**
// * 迁移请假类别 LeaveClass
// */
// private void handlerTransferZKTimeLeaveClass() {
// List<Map<String, Object>> attLeaveTypeMapList = systemDataMigrationService.excuteSql(
// "select a.* from leaveclass a where leaveid in ( select min(leaveid) from leaveclass where deltag = 0 group by
// leavename)");
// List<AttLeaveTypeItem> attLeaveTypeItems = new ArrayList<>();
// AttLeaveTypeItem attLeaveTypeItem = null;
// for (Map<String, Object> map : attLeaveTypeMapList) {
// attLeaveTypeItem = new AttLeaveTypeItem();
// // 假种名称
// attLeaveTypeItem.setLeaveTypeName(MapUtils.getString(map, "leavename"));
// // 是否扣上班时长（false：否/0，true：是/1）
// attLeaveTypeItem.setIsDeductWorkLong("128".equalsIgnoreCase(MapUtils.getString(map, "classify")));
// attLeaveTypeItem.setConvertCount(MapUtils.getDouble(map, "minunit"));
// // Unit 1:小时，2:分钟，3:工作日
// Integer unit = MapUtils.getInteger(map, "unit");
// if (null != unit) {
// // minute=分钟,day=工作日,hour=小时
// String convertUnit = "";
// switch (unit) {
// case 1:
// convertUnit = AttConstant.ATT_CONVERT_UNIT_HOUR;
// break;
// case 2:
// convertUnit = AttConstant.ATT_CONVERT_UNIT_MINUTE;
// break;
// case 3:
// convertUnit = AttConstant.ATT_CONVERT_UNIT_DAY;
// break;
// default:
// }
// attLeaveTypeItem.setConvertUnit(convertUnit);
// }
// // 舍入控制（abort=向下（舍弃）,rounding=四舍五入,carry=向上（进位））
// attLeaveTypeItem.setConvertType(AttConstant.ATT_CONVERT_ROUNDING);
// attLeaveTypeItem.setSymbol(MapUtils.getString(map, "reportsymbol"));
// // 标记（params=参数设置、leaveType=假种）
// attLeaveTypeItem.setMark(AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
//
// attLeaveTypeItems.add(attLeaveTypeItem);
// }
// attLeaveTypeService.handlerTransferZKTime(attLeaveTypeItems);
// }
//
// /**
// * 迁移请假 USER_SPEDAY
// */
// private void handlerTransferZKTimeUserSpeday() {
// List<Map<String, Object>> leaveMapList = systemDataMigrationService.excuteSql(
// "select a.*, b.badgenumber as userinfo_badgenumber, c.leavename as leaveclass_leavename from user_speday a left join
// userinfo b on a.userid = b.userid left join leaveclass c on a.dateid=c.leaveid where a.state = 2");
// List<AttLeaveItem> attLeaveItems = new ArrayList<>();
// AttLeaveItem attLeaveItem = null;
// for (Map<String, Object> map : leaveMapList) {
// attLeaveItem = new AttLeaveItem();
// // 人员编号
// attLeaveItem.setPersonPin(MapUtils.getString(map, "userinfo_badgenumber"));
// // 开始日期时间
// Object start_datetime = MapUtils.getObject(map, "startspecday");
// if (start_datetime instanceof Date) {
// attLeaveItem.setStartDatetime((Date)start_datetime);
// }
// // 结束日期时间
// Object end_datetime = MapUtils.getObject(map, "endspecday");
// if (end_datetime instanceof Date) {
// attLeaveItem.setEndDatetime((Date)end_datetime);
// }
// // 备注
// attLeaveItem.setRemark(MapUtils.getString(map, "yuanying"));
// attLeaveItem.setLeaveTypeName(MapUtils.getString(map, "leaveclass_leavename"));
//
// attLeaveItems.add(attLeaveItem);
// }
// attLeaveService.handlerTransferZKTime(attLeaveItems);
// }
//
// /**
// * 迁移加班 USER_OVERTIME
// */
// private void handlerTransferZKTimeUserOvertime() {
// List<Map<String, Object>> overtimeMapList = systemDataMigrationService.excuteSql(
// "select a.*, b.badgenumber as userinfo_badgenumber from user_overtime a left join userinfo b on a.userid = b.userid
// where a.state = 2");
// List<AttOvertimeItem> attOvertimeItems = new ArrayList<>();
// AttOvertimeItem attOvertimeItem = null;
//
// for (Map<String, Object> map : overtimeMapList) {
// // 加班申请跨天的到这边要拆天
// Date startDatetime = null;
// Date endDatetime = null;
// // 开始日期时间
// Object StartOTDay = MapUtils.getObject(map, "startotday");
// if (StartOTDay instanceof Date) {
// startDatetime = (Date)StartOTDay;
// }
// // 结束日期时间
// Object EndOTDay = MapUtils.getObject(map, "endotday");
// if (EndOTDay instanceof Date) {
// endDatetime = (Date)EndOTDay;
// }
// if (null == startDatetime || null == endDatetime) {
// continue;
// }
//
// Date start = startDatetime;
// Date end = null;
// while (!(DateUtil.isSameDay(start, endDatetime)) && (start.before(endDatetime))) {
// end = DateUtil.getDayBeginTime(DateUtil.addDay(start, 1));
// attOvertimeItem = new AttOvertimeItem();
// // 人员编号
// attOvertimeItem.setPersonPin(MapUtils.getString(map, "userinfo_badgenumber"));
// // 加班标记（0平时，1休息日，2节假日）
// attOvertimeItem.setOvertimeSign((short)0);
// // 开始日期时间
// attOvertimeItem.setStartDatetime(start);
// // 结束日期时间
// attOvertimeItem.setEndDatetime(end);
// // attOvertimeItem.setOvertimeLong(null);
// // 操作日期时间
// Object operate_datetime = MapUtils.getObject(map, "applydate");
// if (operate_datetime instanceof Date) {
// attOvertimeItem.setOperateDatetime((Date)operate_datetime);
// }
// // 备注
// attOvertimeItem.setRemark(MapUtils.getString(map, "yuanying"));
//
// attOvertimeItems.add(attOvertimeItem);
//
// start = DateUtil.getDayBeginTime(DateUtil.addDay(start, 1));
// }
// end = endDatetime;
//
// attOvertimeItem = new AttOvertimeItem();
// // 人员编号
// attOvertimeItem.setPersonPin(MapUtils.getString(map, "userinfo_badgenumber"));
// // 加班标记（0平时，1休息日，2节假日）
// attOvertimeItem.setOvertimeSign((short)0);
// // 开始日期时间
// attOvertimeItem.setStartDatetime(start);
// // 结束日期时间
// attOvertimeItem.setEndDatetime(end);
// // 操作日期时间
// Object operate_datetime = MapUtils.getObject(map, "applydate");
// if (operate_datetime instanceof Date) {
// attOvertimeItem.setOperateDatetime((Date)operate_datetime);
// }
// // 备注
// attOvertimeItem.setRemark(MapUtils.getString(map, "yuanying"));
//
// attOvertimeItems.add(attOvertimeItem);
// }
// attOvertimeService.handlerTransfer(attOvertimeItems);
// }
// }
