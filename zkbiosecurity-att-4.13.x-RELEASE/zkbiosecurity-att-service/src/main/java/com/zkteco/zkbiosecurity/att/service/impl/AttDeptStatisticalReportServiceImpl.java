package com.zkteco.zkbiosecurity.att.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.att.vo.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.dao.AttRecordDao;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;

@Service
public class AttDeptStatisticalReportServiceImpl implements AttDeptStatisticalReportService {

    @Autowired
    private AttRecordDao attRecordDao;
    @Autowired(required = false)
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttRecordService attRecordService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;
    @Autowired
    private AttMonthDetailReportService attMonthDetailReportService;

    @Override
    public boolean deleteByIds(String arg0) {
        return false;
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        AttDeptStatisticalReportItem attDeptStatisticalReportItem = (AttDeptStatisticalReportItem)condition;

        // 条件组装
        buildCondition(null, attDeptStatisticalReportItem);

        Pager pager = attRecordDao.getItemsBySql(attDeptStatisticalReportItem.getClass(),
            SQLUtil.getSqlByItem(condition), page, size);

        // 数据转换
        buildItems((List<AttDeptStatisticalReportItem>)pager.getData(), attDeptStatisticalReportItem.getMonthStart(),
            attDeptStatisticalReportItem.getMonthEnd());
        return pager;
    }

    @Override
    public void modifyItemLabel() {

        // 考勤规则最小单位配置
        Map<String, AttLeaveTypeItem> attLeaveTypeItemMap = attLeaveTypeService.getLeaveTypeParamsMap();

        // 应到/实到
        AttLeaveTypeItem actual = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.ACTUAL);
        if (actual != null) {
            // VO对应的字段名称
            String fieldName = "shouldHour";
            String label = attLeaveTypeService.getConvertUnit(actual);
            AttCommonUtils.modifyItemLabelByFieldName(AttDeptStatisticalReportItem.class, fieldName, label);
        }

        // 迟到
        AttLeaveTypeItem late = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.LATE);
        if (late != null) {
            // VO对应的字段名称
            String fieldName = "lateMinute";
            String label = attLeaveTypeService.getConvertUnit(late);
            AttCommonUtils.modifyItemLabelByFieldName(AttDeptStatisticalReportItem.class, fieldName, label);
        }

        // 早退
        AttLeaveTypeItem early = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.EARLY);
        if (early != null) {
            // VO对应的字段名称
            String fieldName = "earlyMinute";
            String label = attLeaveTypeService.getConvertUnit(early);
            AttCommonUtils.modifyItemLabelByFieldName(AttDeptStatisticalReportItem.class, fieldName, label);
        }

        // 加班
        AttLeaveTypeItem overtime = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.OVERTIME);
        if (overtime != null) {
            // VO对应的字段名称
            String fieldName = "overtimeUsualHour";
            String label = attLeaveTypeService.getConvertUnit(overtime);
            AttCommonUtils.modifyItemLabelByFieldName(AttDeptStatisticalReportItem.class, fieldName, label);
        }

        // 异常
        // 旷工
        AttLeaveTypeItem absent = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.ABSENT);
        if (absent != null) {
            // VO对应的字段名称
            String fieldName = "absentHour";
            String label = attLeaveTypeService.getConvertUnit(absent);
            AttCommonUtils.modifyItemLabelByFieldName(AttDeptStatisticalReportItem.class, fieldName, label);
        }
    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, AttDeptStatisticalReportItem condition, int pageNo,
        int pageSize) {
        buildCondition(sessionId, condition);
        Pager pager = attRecordService.getItemsByPage(condition, pageNo, pageSize);
        buildItems((List<AttDeptStatisticalReportItem>)pager.getData(), condition.getMonthStart(),
            condition.getMonthEnd());
        return pager;
    }

    @Override
    public List<AttDeptStatisticalReportItem> getDeptStatisticalReportItemData(String sessionId,
        AttDeptStatisticalReportItem condition, int beginIndex, int endIndex) {

        buildCondition(sessionId, condition);

        List<AttDeptStatisticalReportItem> items = (List<AttDeptStatisticalReportItem>)attRecordService
            .getItemData(condition.getClass(), condition, beginIndex, endIndex);

        buildItems(items, condition.getMonthStart(), condition.getMonthEnd());
        return items;
    }

    /**
     * 根据设置的规则，分转时,时转天
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/24 18:23
     * @param items
     * @return void
     */
    private void buildItems(List<AttDeptStatisticalReportItem> items, Date monthStart, Date monthEnd) {

        // 考勤加班等级
        Map<String, String> overtimeParamsMap = attParamService.getOvertimeSetting();

        Collection<String> depIds =
            CollectionUtil.getPropertyList(items, AttDeptStatisticalReportItem::getDeptId, "-1");
        List<AuthDepartmentItem> authDepartmentItemList = authDepartmentService.getItemsByIds(depIds);
        Map<String, AuthDepartmentItem> authDepartmentMap =
            CollectionUtil.listToKeyMap(authDepartmentItemList, AuthDepartmentItem::getId);

        // 考勤规则最小单位配置
        Map<String, AttLeaveTypeItem> attLeaveTypeParamsMap = attLeaveTypeService.getLeaveTypeParamsMap();

        // 考勤节假日最小单位配置
        List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getLeaveTypeItems();
        Map<String, AttLeaveTypeItem> attLeaveTypeItemMap =
            CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getLeaveTypeNo);
        // 根据部门编号获取对应的记录，组装请假详情
        Collection<String> deptIdList =
            CollectionUtil.getPropertyList(items, AttDeptStatisticalReportItem::getDeptId, "-1");
        List<List<String>> splitDeptIdList = CollectionUtil.split(deptIdList, CollectionUtil.splitSize);
        List<AttDeptStatisticalReportExtItem> attDeptStatisticalReportExtItemList = new ArrayList<>();
        splitDeptIdList.forEach(deptIds -> {
            // 0、personPin/1、attDate/2、leaveDetails/3、shouldMinute/、4actualMinute/ 5、validMinute/6、absentMinute
            // 7、leaveMinute/8、lateMinuteTotal/9、earlyMinuteTotal/10、overtimeUsualMinute
            // 11、overtimeRestMinute/12、overtimeHolidayMinute/13、OvertimeMinute
            List<Object[]> attRecordObjectList = attRecordDao.findByDeptIdsAndAttDateForMonthStatistical(deptIds, monthStart, monthEnd);
            for (Object[] attRecordObject : attRecordObjectList) {
                AttDeptStatisticalReportExtItem item = new AttDeptStatisticalReportExtItem();
                item.setDeptId(attRecordObject[0] + "");
                item.setAttDate((Date)attRecordObject[1]);
                item.setLeaveDetails(Objects.isNull(attRecordObject[2])? "": attRecordObject[2] + "");
                item.setShouldMinute((Integer)attRecordObject[3]);
                item.setActualMinute((Integer)attRecordObject[4]);
                item.setValidMinute((Integer)attRecordObject[5]);
                item.setAbsentMinute((Integer)attRecordObject[6]);
                item.setLeaveMinute((Integer)attRecordObject[7]);
                item.setLateMinuteTotal((Integer)attRecordObject[8]);
                item.setEarlyMinuteTotal((Integer)attRecordObject[9]);
                item.setOvertimeUsualMinute((Integer)attRecordObject[10]);
                item.setOvertimeRestMinute((Integer)attRecordObject[11]);
                item.setOvertimeHolidayMinute((Integer)attRecordObject[12]);
                item.setOvertimeMinute((Integer)attRecordObject[13]);
                item.setShouldMinuteEx((Integer)attRecordObject[14]);
                attDeptStatisticalReportExtItemList.add(item);
            }
        });
        Map<String, List<AttDeptStatisticalReportExtItem>> deptIdRecordsMap = new HashMap<>();
        if (!CollectionUtil.isEmpty(attDeptStatisticalReportExtItemList)) {
            deptIdRecordsMap = attDeptStatisticalReportExtItemList.stream().collect(Collectors.groupingBy(AttDeptStatisticalReportExtItem::getDeptId));
        }
        // 精确小数点位数
        int decimal = Integer.parseInt(attParamService.getDecimal());
        // 应上
        AttLeaveTypeItem actual = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.ACTUAL);
        // 迟到
        AttLeaveTypeItem late = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.LATE);
        // 早退
        AttLeaveTypeItem early = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.EARLY);
        // 加班
        AttLeaveTypeItem overtime = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.OVERTIME);
        // 旷工
        AttLeaveTypeItem absent = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.ABSENT);
        for (AttDeptStatisticalReportItem item : items) {

            // 组装请假详情
            List<AttDeptStatisticalReportExtItem> currentPerssonRecords = deptIdRecordsMap.get(item.getDeptId());
            if (currentPerssonRecords == null) {
                continue;
            }
            List<AttDeptStatisticalReportExtItem> leaveRecordList = currentPerssonRecords.stream()
                    .filter(tmp -> StringUtils.isNotBlank(tmp.getLeaveDetails())).collect(Collectors.toList());
            item.setAttMonthDetailReportLeaveHourMap(leaveFormatDeptStatistical(attLeaveTypeItemMap, leaveRecordList, decimal));

            if (authDepartmentMap.containsKey(item.getDeptId())) {
                AuthDepartmentItem authDepartmentItem = authDepartmentMap.get(item.getDeptId());
                item.setDeptCode(authDepartmentItem.getCode());
                item.setDeptName(authDepartmentItem.getName());
            }

            // 应上
            BigDecimal shouldHourTotal = BigDecimal.ZERO;
            // 实际
            BigDecimal actualHourTotal = BigDecimal.ZERO;
            // 有效
            BigDecimal validHourTotal = BigDecimal.ZERO;
            // 迟到
            BigDecimal lateMinuteTotal = BigDecimal.ZERO;
            // 早退
            BigDecimal earlyMinuteTotal = BigDecimal.ZERO;
            // 加班/平时
            BigDecimal overtimeUsualHourTotal = BigDecimal.ZERO;
            // 加班/休息
            BigDecimal overtimeRestHourTotal = BigDecimal.ZERO;
            // 加班/节日
            BigDecimal overtimeHolidayHourTotal = BigDecimal.ZERO;
            // 加班/合计
            BigDecimal overtimeHourTotal = BigDecimal.ZERO;
            // 加班等级
            BigDecimal overTimeOT1Total = BigDecimal.ZERO;
            BigDecimal overTimeOT2Total = BigDecimal.ZERO;
            BigDecimal overTimeOT3Total = BigDecimal.ZERO;
            // 旷工
            BigDecimal absentHourTotal = BigDecimal.ZERO;
            //根据考勤规则转化对应的单位，并进行遍历累加统计，
            for (AttDeptStatisticalReportExtItem attRecordItem : currentPerssonRecords) {
                // 应上分钟数，用于计算天数
                Integer shouldMinute = Optional.ofNullable(attRecordItem.getShouldMinute()).orElse(0);
                Integer shouldMinuteEx = Optional.ofNullable(attRecordItem.getShouldMinuteEx()).orElse(0);
                // 应上
                shouldHourTotal = shouldHourTotal.add(AttCommonUtils
                    .convertMinuteToBD(shouldMinute, shouldMinute, actual, decimal));
                // 实际
                actualHourTotal = actualHourTotal.add(AttCommonUtils.convertMinuteToBD(attRecordItem.getActualMinute(), shouldMinute,
                        actual, decimal));
                // 有效
                validHourTotal = validHourTotal.add(AttCommonUtils.convertMinuteToBD(attRecordItem.getValidMinute(), shouldMinute,
                        actual, decimal));
                // 迟到
                lateMinuteTotal = lateMinuteTotal.add(AttCommonUtils.convertMinuteToBD(attRecordItem.getLateMinuteTotal(), shouldMinute,
                        late, decimal));
                // 早退
                earlyMinuteTotal = earlyMinuteTotal.add(AttCommonUtils.convertMinuteToBD(attRecordItem.getEarlyMinuteTotal(),
                        shouldMinute, early, decimal));

                Integer should = shouldMinute == 0 ? shouldMinuteEx : shouldMinute;
                // 加班/平时
                BigDecimal overtimeUsual = AttCommonUtils.convertMinuteToBD(attRecordItem.getOvertimeUsualMinute(), should, overtime, decimal);
                overtimeUsualHourTotal = overtimeUsualHourTotal.add(overtimeUsual);
                // 加班/休息
                BigDecimal overtimeRest  = AttCommonUtils.convertMinuteToBD(attRecordItem.getOvertimeRestMinute(), should, overtime, decimal);
                overtimeRestHourTotal = overtimeRestHourTotal.add(overtimeRest);
                // 加班/节日
                BigDecimal overtimeHoliday = AttCommonUtils.convertMinuteToBD(attRecordItem.getOvertimeHolidayMinute(), should, overtime, decimal);
                overtimeHolidayHourTotal = overtimeHolidayHourTotal.add(overtimeHoliday);
                // 加班/合计
                overtimeHourTotal = overtimeHourTotal.add(AttCommonUtils.convertMinuteToBD(attRecordItem.getOvertimeMinute(),
                        should, overtime, decimal));

                // 加班等级
                AttDayDetailReportItem detailReportItem = new AttDayDetailReportItem();
                detailReportItem.setOvertimeUsualConvert(overtimeUsual + "");
                detailReportItem.setOvertimeRestConvert(overtimeRest + "");
                detailReportItem.setOvertimeHolidayConvert(overtimeHoliday + "");

                overTimeOT1Total = overTimeOT1Total.add(AttCommonUtils.convertOverTimeOT(detailReportItem, should, overtime,
                        decimal, overtimeParamsMap, "OT1"));
                overTimeOT2Total = overTimeOT2Total.add(AttCommonUtils.convertOverTimeOT(detailReportItem, should, overtime,
                        decimal, overtimeParamsMap, "OT2"));
                overTimeOT3Total = overTimeOT3Total.add(AttCommonUtils.convertOverTimeOT(detailReportItem, should, overtime,
                        decimal, overtimeParamsMap, "OT3"));

                // 旷工
                absentHourTotal = absentHourTotal.add(AttCommonUtils.convertMinuteToBD(attRecordItem.getAbsentMinute(), shouldMinute,
                        absent, decimal));
            }
            item.setShouldHour(shouldHourTotal.toString());
            item.setActualHour(actualHourTotal.toString());
            item.setValidHour(validHourTotal.toString());
            item.setLateMinute(lateMinuteTotal.toString());
            item.setEarlyMinute(earlyMinuteTotal.toString());
            item.setOvertimeUsualHour(overtimeUsualHourTotal.toString());
            item.setOvertimeRestHour(overtimeRestHourTotal.toString());
            item.setOvertimeHolidayHour(overtimeHolidayHourTotal.toString());
            item.setOvertimeHour(overtimeHourTotal.toString());
            // 加班等级
            item.setOverTimeOT1(overTimeOT1Total.toString());
            item.setOverTimeOT2(overTimeOT2Total.toString());
            item.setOverTimeOT3(overTimeOT3Total.toString());
            item.setAbsentHour(absentHourTotal.toString());
        }
    }

    private Map<String, Object> leaveFormatDeptStatistical(Map<String, AttLeaveTypeItem> attLeaveTypeItemMap,
                                                               List<AttDeptStatisticalReportExtItem> leaveRecordList, int decimal) {
        Map<String, Object> map = new HashMap<>();
        for (AttDeptStatisticalReportExtItem item : leaveRecordList) {
            Integer shouldMinute = item.getShouldMinute();
            String[] leaveDetails = item.getLeaveDetails().split(",");
            for (String leaveDetail : leaveDetails) {
                if (StringUtils.isNotBlank(leaveDetail)) {
                    String[] leaveDetailArr = leaveDetail.split("-");
                    String leaveTypeNo = leaveDetailArr[0];
                    int num = AttCommonUtils.strToInt(leaveDetailArr[1]);
                    if (map.containsKey(leaveTypeNo)) {
                        BigDecimal nowNum = BigDecimal.valueOf(Double.valueOf(map.get(leaveTypeNo) + ""));
                        String addRet = AttCommonUtils.convertMinute(num, shouldMinute,
                                attLeaveTypeItemMap.get(leaveTypeNo), decimal);
                        BigDecimal addNum = BigDecimal.valueOf(Double.valueOf(addRet));
                        map.put(leaveTypeNo, nowNum.add(addNum).setScale(decimal) + "");
                    } else {
                        map.put(leaveTypeNo, AttCommonUtils.convertMinute(num, shouldMinute,
                                attLeaveTypeItemMap.get(leaveTypeNo), decimal));
                    }
                }
            }
        }

        Map<String, Object> result = new HashMap<>();
        String defaultValue = BigDecimal.ZERO.setScale(decimal) + "";
        for (Map.Entry<String, AttLeaveTypeItem> entry : attLeaveTypeItemMap.entrySet()) {
            AttLeaveTypeItem attLeaveTypeItem = entry.getValue();
            Object value = MapUtils.getObject(map, attLeaveTypeItem.getLeaveTypeNo(), defaultValue);
            result.put(attLeaveTypeItem.getLeaveTypeName(), value);
        }
        return result;
    }

    /**
     * 条件组装
     * 
     * @param sessionId
     * @param condition
     */
    private void buildCondition(String sessionId, AttDeptStatisticalReportItem condition) {

        // 人员部门条件组装
        if (Objects.nonNull(authDepartmentService)) {
            // 封装部门条件
            if (StringUtils.isBlank(condition.getInDeptId())) {
                condition.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId,
                    condition.getDeptId(), condition.getDeptCode(), condition.getDeptName()));
                condition.setDeptId(null);
            } else if (StringUtils.isNotBlank(sessionId)) {
                condition.setInDeptId(authDepartmentService.getDeptIdsByAuthAndIds(sessionId, condition.getInDeptId()));
                condition.setDeptId(null);
            }
        }
    }

}
