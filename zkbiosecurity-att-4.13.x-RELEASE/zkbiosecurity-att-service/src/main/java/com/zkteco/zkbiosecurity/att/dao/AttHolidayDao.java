package com.zkteco.zkbiosecurity.att.dao;

import com.zkteco.zkbiosecurity.att.model.AttHoliday;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

/**
 * 节假日
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 9:50
 * @since 1.0.0
 */
public interface AttHolidayDao extends BaseDao<AttHoliday, String> {

    /**
     * 根据节假日名称 查询节假日
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/5/29 13:58
     * @param holidayName
     * @return com.zkteco.zkbiosecurity.att.model.AttHoliday
     */
    AttHoliday findByHolidayName(String holidayName);

    /**
     * 判断节假日名称是否已存在
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/6 16:54
     * @param holidayName
     * @return boolean
     */
    boolean existsByHolidayName(String holidayName);

    /**
     * 根据时间范围查询节假日
     *
     * @param startDatetime
     * @param endDatetime
     * @return
     */
    @Query("SELECT t FROM AttHoliday t WHERE t.startDatetime <= ?2 AND t.endDatetime >= ?1")
    List<AttHoliday> findHolidayByStartTimeAndEndTime(Date startDatetime, Date endDatetime);

    /**
     * 判断时间范围内是否有存在时间重叠的节假日扣除ID本身
     *
     * @param startDatetime
     * @param endDateTime
     * @param id
     * @return
     */
    boolean existsByIdNotAndEndDatetimeGreaterThanEqualAndStartDatetimeLessThanEqual(String id, Date startDatetime,
        Date endDateTime);
}
