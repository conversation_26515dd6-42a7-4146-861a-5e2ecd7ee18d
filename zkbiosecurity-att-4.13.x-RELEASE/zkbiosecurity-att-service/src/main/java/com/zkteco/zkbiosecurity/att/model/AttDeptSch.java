package com.zkteco.zkbiosecurity.att.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * 部门排班
 */
@Deprecated
@Entity
@Table(name = "ATT_DEPTSCH")
@Setter
@Getter
@Accessors(chain = true)
public class AttDeptSch extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 起始日期
     */
    @Column(name = "START_DATE")
    private Date startDate;

    /**
     * 结束日期
     */
    @Column(name = "END_DATE")
    private Date endDate;

    /**
     * 排班类型
     */
    @Column(name = "SCHEDULE_TYPE")
    private Short scheduleType;

    /**
     * 考勤方式（0/按班次正常刷卡，1/一天内任刷一次有效卡，2/只计算刷卡时间，3/免刷卡）
     */
    @Column(name = "ATTENDANCE_MODE")
    private Short attendanceMode;

    /**
     * 加班方式（0/电脑自动计算，1/加班必须申请，2/必须加班否则旷工，3/时长为较小者-延后加班和加班单对比，4/不算加班）
     */
    @Column(name = "OVERTIME_MODE")
    private Short overtimeMode;

    /**
     * 加班标记（0/平时，1/休息日，2/节假日）
     */
    @Column(name = "OVERTIME_REMARK")
    private Short overtimeRemark;

    /**
     * 部门id
     */
    @Column(name = "AUTH_DEPT_ID", length = 50)
    private String deptId;

    /**
     * 班次部门排班中间表
     */
    @ManyToMany(targetEntity = AttShift.class, fetch = FetchType.EAGER)
    @JoinTable(name = "ATT_DEPTSCH_SHIFT", joinColumns = @JoinColumn(name = "DEPTSCH_ID", referencedColumnName = "ID"),
        inverseJoinColumns = @JoinColumn(name = "SHIFT_ID", referencedColumnName = "ID"))
    private Set<AttShift> attShiftSet = new LinkedHashSet<AttShift>();
}