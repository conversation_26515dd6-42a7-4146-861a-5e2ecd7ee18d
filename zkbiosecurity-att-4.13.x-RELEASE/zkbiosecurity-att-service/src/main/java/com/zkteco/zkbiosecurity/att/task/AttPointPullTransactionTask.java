package com.zkteco.zkbiosecurity.att.task;

import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ScheduledFuture;

import com.zkteco.zkbiosecurity.core.utils.SpringUtil;
import com.zkteco.zkbiosecurity.redis.redisson.annotation.RedisLock;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.cache.AttCacheManager;
import com.zkteco.zkbiosecurity.att.service.AttTransactionService;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.scheduler.ScheduleService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

import lombok.extern.slf4j.Slf4j;

/**
 * 考勤点定时获取记录
 *
 * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
 * @date 2020/7/13
 **/
@Slf4j
@Component
public class AttPointPullTransactionTask {
    private ScheduledFuture<?> scedulefuture;
    @Autowired
    private ScheduleService scheduleService;
    @Autowired
    private AttCacheManager attCacheManager;
    @Autowired
    private AttTransactionService attTransactionService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    public void startTask() {

        try {

            String expression = baseSysParamService.getValByName("att.point.pullTransaction");
            if (StringUtils.isBlank(expression)) {
                // 定时拉取
                expression = "0 0/1 * * * ?";
            }

            // 每次重新启动,清空拉取考勤记录的标记
            attCacheManager.deleteAllPullTransactionFlag();

            if (scedulefuture != null) {
                scedulefuture.cancel(true);
            }
            AttPointPullTransactionTask attPointPullTransactionTask = SpringUtil.getApplicationContext().getBean(AttPointPullTransactionTask.class);
            scedulefuture = scheduleService.startScheduleTask(() -> attPointPullTransactionTask.getTransactionTask(), expression);

        } catch (Exception e) {

            log.error("AttPointPullTransactionTask Exception", e);
        }
    }

    /**
     * 从缓存中查找考勤点以及对应的最近拉取记录的时间戳
     *
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/7/13 13:45
     * @return: void
     **/
    @RedisLock(lockName = "getTransactionTaskRedisson", waitTime = 0, expire = 600)
    public void getTransactionTask() {
        // 获取所有的缓存的考勤点
        Map<String, JSONObject> allCacheAttPointMap = attCacheManager.getAllPointPullTransaction();
        if (CollectionUtil.isEmpty(allCacheAttPointMap)) {
            return;
        }

        log.info("AttPointPullTransactionTask start ...");
        String currentTimeMillis = String.valueOf(System.currentTimeMillis());
        for (Map.Entry<String, JSONObject> entry : allCacheAttPointMap.entrySet()) {
            String module = entry.getKey();

            // 缓存空数据判断
            JSONObject jsonObject = entry.getValue();
            if (Objects.isNull(jsonObject)) {
                continue;
            }
            String deviceIds = jsonObject.getString("deviceIds");
            if (StringUtils.isBlank(deviceIds)) {
                continue;
            }

            // 判断模块是否正在拉取，如果拉取中，则不重复执行
            String flag = attCacheManager.getPullTransactionFlag(module);
            if (StringUtils.isNotBlank(flag)) {
                continue;
            }
            // 标记模块正在拉取记录
            attCacheManager.setPullTransactionFlag(module);

            try {

                // 开始时间-缓存中最大的拉取时间
                Date startDateTime;
                String lastTransactionTime = jsonObject.getString("timestamp");
                if (StringUtils.isNotBlank(lastTransactionTime)) {
                    // 考勤点最近一次获取记录的时间戳
                    startDateTime = new Date(Long.parseLong(lastTransactionTime));
                } else {
                    // 查库找该考勤点最近一次记录的时间
                    startDateTime = attTransactionService.getMaxCreateTimeByModule(
                        module.startsWith(ConstUtil.SYSTEM_MODULE_PARK) ? ConstUtil.SYSTEM_MODULE_PARK : module);
                    if (Objects.nonNull(startDateTime)) {
                        // 如果数据库记录不为空,则以该时间往前推半小时,防止漏推记录 by ljf 2020/8/12
                        startDateTime = DateUtil.addMinute(startDateTime, -30);
                    } else {
                        // 数据库没有数据则不执行, 最后时间记为当前时间
                        jsonObject.put("timestamp", currentTimeMillis);
                        attCacheManager.setPointPullTransaction(module, jsonObject.toJSONString());
                        continue;
                    }
                }

                // 开始时间格式化去掉毫秒
                startDateTime = AttDateUtils.stringToYmdHmsDate(AttDateUtils.dateToStrAsLong(startDateTime));

                // 结束时间获取当天23:59:59秒前的数据
                Date endDateTime = AttDateUtils.getMaxOfDay(new Date());
                // 如果开始时间是很早之前的，这里分3天处理一次，避免时间范围太大导致数据过多
                Date endDateTime2 = DateUtil.addDay(startDateTime, 3);
                if(endDateTime2.before(endDateTime)) {
                    endDateTime = endDateTime2;
                    currentTimeMillis = String.valueOf(endDateTime.getTime());
                }

                // 拉取记录, 同时获取记录的最大创建时间
                Date maxAttDateTime =
                    attTransactionService.pullThirdPartyRecord(module, deviceIds, startDateTime, endDateTime);

                if (Objects.nonNull(maxAttDateTime)) {
                    // 更新最后拉取记录的最大创建时间
                    maxAttDateTime = AttDateUtils.stringToYmdHmsDate(AttDateUtils.dateToStrAsLong(maxAttDateTime));
                    jsonObject.put("timestamp", String.valueOf(maxAttDateTime.getTime()));
                    attCacheManager.setPointPullTransaction(module, jsonObject.toJSONString());
                } else {
                    // 未拉取到记录，更新为当前时间
                    jsonObject.put("timestamp", currentTimeMillis);
                    attCacheManager.setPointPullTransaction(module, jsonObject.toJSONString());
                }

            } catch (Throwable e) {
                log.error("AttPointPullTransactionTask Exception", e);
            } finally {
                log.info("AttPointPullTransactionTask end ...");
                // 删除模块正在拉取标记
                attCacheManager.delPullTransactionFlag(module);
            }
        }
    }
}
