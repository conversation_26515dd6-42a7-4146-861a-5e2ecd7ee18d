package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.api.vo.AppAttBusinessItem;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttLeaveDao;
import com.zkteco.zkbiosecurity.att.dao.AttLeaveTypeDao;
import com.zkteco.zkbiosecurity.att.model.AttLeaveType;
import com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.*;

/**
 * 假种
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:13
 * @since 1.0.0
 */
@Service
public class AttLeaveTypeServiceImpl implements AttLeaveTypeService {

    @Autowired
    private AttLeaveTypeDao attLeaveTypeDao;
    @Autowired
    private AttLeaveDao attLeaveDao;

    @Override
    @Transactional
    public AttLeaveTypeItem saveItem(AttLeaveTypeItem item) {
        AttLeaveType attLeaveType =
            Optional.ofNullable(item).map(AttLeaveTypeItem::getId).filter(StringUtils::isNotBlank)
                .flatMap(attLeaveTypeDao::findById).filter(Objects::nonNull).orElse(new AttLeaveType());
        ModelUtil.copyPropertiesIgnoreNull(item, attLeaveType);
        setLeaveTypeNo(attLeaveType);
        attLeaveTypeDao.save(attLeaveType);
        item.setId(attLeaveType.getId());
        return item;
    }

    /**
     * 新增假种设置编号
     * 
     * @param attLeaveType
     */
    private void setLeaveTypeNo(AttLeaveType attLeaveType) {
        if (Objects.nonNull(attLeaveType) && StringUtils.isBlank(attLeaveType.getLeaveTypeNo())) {
            List<String> leaveTypeNoList = attLeaveTypeDao.findLeaveTypeNo();
            Set<String> leaveTypeNoSet = new HashSet<>(leaveTypeNoList);
            String leaveTypeNo;
            do {
                leaveTypeNo = DateUtil.dateToString(new Date(), "ssSSS") + RandomStringUtils.randomNumeric(3);
            } while (leaveTypeNoSet.contains(leaveTypeNo));
            attLeaveType.setLeaveTypeNo(leaveTypeNo);
            attLeaveType.setMark(AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        }
    }

    @Override
    public List<AttLeaveTypeItem> getByCondition(AttLeaveTypeItem condition) {
        List<AttLeaveTypeItem> list = (List<AttLeaveTypeItem>)attLeaveTypeDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
        if (!CollectionUtil.isEmpty(list)) {
            String lang = PropertiesUtil.getCurrentLanguage();
            list.forEach(item -> {
                if (item.getInitFlag()) {
                    // 针对初始化的假种名称进行国际化转换--add by hook.fang 2020-2-18
                    item.setLeaveTypeName(I18nUtil.i18nCode(item.getLeaveTypeName(), lang));
                }
            });
        }
        return list;
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        Pager pager = attLeaveTypeDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        buildItems((List<AttLeaveTypeItem>)pager.getData());
        return pager;
    }

    /**
     * 组装最小单位
     */
    private void buildItems(List<AttLeaveTypeItem> attLeaveTypeItemList) {
        for (AttLeaveTypeItem attLeaveTypeItem : attLeaveTypeItemList) {
            String convertUnit = attLeaveTypeItem.getConvertUnit();
            String convertUnitName = I18nUtil.i18nCode("common_minutes");
            if (AttConstant.ATT_CONVERT_UNIT_DAY.equals(convertUnit)) {
                convertUnitName = I18nUtil.i18nCode("att_param_workDay");
            } else if (AttConstant.ATT_CONVERT_UNIT_HOUR.equals(convertUnit)) {
                convertUnitName = I18nUtil.i18nCode("common_hours");
            }
            attLeaveTypeItem.setSmallestUnit(attLeaveTypeItem.getConvertCount() + convertUnitName);
        }
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                attLeaveTypeDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AttLeaveTypeItem getItemById(String id) {
        List<AttLeaveTypeItem> items = getByCondition(new AttLeaveTypeItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    @Transactional
    public AttLeaveTypeItem initData(AttLeaveTypeItem item) {
        AttLeaveType attLeaveType = attLeaveTypeDao.findByLeaveTypeNo(item.getLeaveTypeNo());
        if (attLeaveType == null) {
            attLeaveType = new AttLeaveType();
        }
        ModelUtil.copyPropertiesIgnoreNull(item, attLeaveType);
        attLeaveTypeDao.save(attLeaveType);
        return item;
    }

    @Override
    public ZKResultMsg listJsonLeaveType() {
        List<SelectItem> items = new ArrayList<SelectItem>();
        String lang = PropertiesUtil.getCurrentLanguage();
        List<AttLeaveType> attLeaveTypeList =
            attLeaveTypeDao.findByMarkOrderBySortNoAsc(AttConstant.ATT_CONVERT_MARK_LEAVETYPE);

        for (AttLeaveType leaveType : attLeaveTypeList) {
            SelectItem selectItem = new SelectItem();
            selectItem.setValue(leaveType.getId());
            String leaveTypeName = leaveType.getLeaveTypeName();
            if (leaveType.getInitFlag()) {
                // 针对初始化的假种名称进行国际化转换--add by hook.fang 2020-2-18
                leaveTypeName = I18nUtil.i18nCode(leaveType.getLeaveTypeName(), lang);
            }
            selectItem.setText(leaveTypeName);
            items.add(selectItem);
        }
        return new ZKResultMsg(items);
    }

    @Override
    public boolean isInitData(String ids) {
        String[] idAry = ids.split(",");
        AttLeaveType attLeaveType = null;
        for (String id : idAry) {
            attLeaveType = attLeaveTypeDao.getOne(id);
            if (attLeaveType.getInitFlag()) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean isLeaveTypeName(String leaveTypeName) {
        return attLeaveTypeDao.existsByLeaveTypeName(leaveTypeName);
    }

    @Override
    public boolean isExistFkData(String ids) {
        boolean exitsLeaveTypeId = false;
        if (StringUtils.isNotBlank(ids)) {
            String[] idArray = StringUtils.split(ids, AttConstant.COMMA);
            for (String id : idArray) {
                exitsLeaveTypeId = attLeaveDao.existsByLeaveTypeId(id);
            }
        }
        return exitsLeaveTypeId;
    }

    @Override
    public Object getItemData(Class<AttLeaveTypeItem> attLeaveTypeItemClass, AttLeaveTypeItem attLeaveTypeItem,
        int beginIndex, int endIndex) {
        return attLeaveTypeDao.getItemsDataBySql(attLeaveTypeItemClass, SQLUtil.getSqlByItem(attLeaveTypeItem),
            beginIndex, endIndex, true);
    }

    @Override
    @Transactional
    public void handlerTransfer(List<AttLeaveTypeItem> attLeaveTypeItems) {
        // 请假类型数据量有限，不需要进行分批次处理
        List<AttLeaveType> attLeaveTypeList = new ArrayList<>();
        for (AttLeaveTypeItem attLeaveTypeItem : attLeaveTypeItems) {
            String leaveTypeNo = attLeaveTypeItem.getLeaveTypeNo();
            AttLeaveType attLeaveType = attLeaveTypeDao.findByLeaveTypeNo(leaveTypeNo);
            if (Objects.isNull(attLeaveType)) {
                attLeaveType = new AttLeaveType();
                attLeaveType.setInitFlag(Boolean.FALSE);
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(attLeaveTypeItem, attLeaveType, "id", "initFlag");
            attLeaveTypeList.add(attLeaveType);
        }
        attLeaveTypeDao.saveAll(attLeaveTypeList);
    }

    @Override
    public ZKResultMsg listLeaveType() {
        List<AppAttBusinessItem> items = new ArrayList<AppAttBusinessItem>();
        List<AttLeaveType> attLeaveType =
            attLeaveTypeDao.findByMarkOrderBySortNoAsc(AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        AppAttBusinessItem typeItem = null;
        String leaveTypeName = "";
        for (AttLeaveType leaveType : attLeaveType) {
            typeItem = new AppAttBusinessItem();
            typeItem.setCode(leaveType.getLeaveTypeNo());
            leaveTypeName = leaveType.getLeaveTypeName();
            typeItem.setValue(leaveTypeName);
            items.add(typeItem);
        }
        return new ZKResultMsg(items);
    }

    @Override
    @Transactional
    public void upgradeTo_232() {
        AttLeaveTypeItem condition = new AttLeaveTypeItem();
        condition.setInitFlag(true);
        List<AttLeaveTypeItem> leaveTypeItems = getByCondition(condition);
        for (AttLeaveTypeItem item : leaveTypeItems) {
            if (item.getInitFlag()) {
                // 考勤初始化假种的key改为国际化语言
                item.setLeaveTypeName(I18nUtil.i18nCode(item.getLeaveTypeName()));
                saveItem(item);
            }
        }

    }

    @Override
    public String getTripAndOutId() {
        List<AttLeaveType> attLeaveTypeList =
            attLeaveTypeDao.findByMarkOrderBySortNoAsc(AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        // 找出出差和外出
        List<AttLeaveType> tripAndOut = attLeaveTypeList.stream()
            .filter(item -> (AttCalculationConstant.AttAttendStatus.TRIP.equals(item.getLeaveTypeNo())
                || AttCalculationConstant.AttAttendStatus.OUT.equals(item.getLeaveTypeNo())))
            .collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(tripAndOut)) {
            return CollectionUtil.getModelIds(tripAndOut);
        }
        return "";
    }

    @Override
    public ZKResultMsg listJsonLeaveTypeFilterTripAndOut() {
        List<SelectItem> items = new ArrayList<SelectItem>();
        SelectItem selectItem = null;
        String leaveTypeName = "";
        String lang = PropertiesUtil.getCurrentLanguage();
        List<AttLeaveType> attLeaveType =
            attLeaveTypeDao.findByMarkOrderBySortNoAsc(AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        for (AttLeaveType leaveType : attLeaveType) {
            // out表示出差 trip表示外出
            if (AttCalculationConstant.AttAttendStatus.TRIP.equals(leaveType.getLeaveTypeNo())
                || AttCalculationConstant.AttAttendStatus.OUT.equals(leaveType.getLeaveTypeNo())) {
                continue;
            }
            selectItem = new SelectItem();
            selectItem.setValue(leaveType.getId());
            leaveTypeName = leaveType.getLeaveTypeName();
            if (leaveType.getInitFlag()) {
                leaveTypeName = I18nUtil.i18nCode(leaveType.getLeaveTypeName(), lang);
            }
            selectItem.setText(leaveTypeName);
            items.add(selectItem);
        }
        return new ZKResultMsg(items);
    }

    @Override
    public AttLeaveTypeItem getItemByLeaveTypeNo(String leaveTypeNo) {
        AttLeaveType attLeaveType = attLeaveTypeDao.findByLeaveTypeNo(leaveTypeNo);
        if (Objects.nonNull(attLeaveType)) {
            return ModelUtil.copyProperties(attLeaveType, new AttLeaveTypeItem());
        }
        return null;
    }

    @Override
    public List<AttLeaveTypeItem> getLeaveTypeByIds(Collection<String> leaveTypeIdList) {
        List<AttLeaveTypeItem> attLeaveTypeItemList = new ArrayList<>();
        List<AttLeaveType> attLeaveTypeList = attLeaveTypeDao.findByIdIn(leaveTypeIdList);
        if (attLeaveTypeList.size() > 0) {
            attLeaveTypeItemList = ModelUtil.copyListProperties(attLeaveTypeList, AttLeaveTypeItem.class);
        }
        return attLeaveTypeItemList;
    }

    @Override
    public Map<String, AttLeaveTypeItem> getLeaveTypeParamsMap() {
        AttLeaveTypeItem condition = new AttLeaveTypeItem();
        condition.setMark(AttConstant.ATT_CONVERT_MARK_PARAMS);
        List<AttLeaveTypeItem> attLeaveTypeItemList = getByCondition(condition);
        return CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getLeaveTypeNo);
    }

    @Override
    public List<AttLeaveTypeItem> getLeaveTypeItems() {
        AttLeaveTypeItem condition = new AttLeaveTypeItem();
        condition.setMark(AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        return getByCondition(condition);
    }

    @Override
    public Map<String, AttLeaveTypeItem> getLeaveTypeMap() {
        AttLeaveTypeItem condition = new AttLeaveTypeItem();
        condition.setMark(AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        List<AttLeaveTypeItem> attLeaveTypeItemList = getByCondition(condition);
        return CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getId);
    }

    @Override
    public String getConvertUnit(AttLeaveTypeItem attLeaveTypeItem) {
        String convertUnit = attLeaveTypeItem.getConvertUnit();
        String convertUnitName = I18nUtil.i18nCode("common_minute");
        if (AttConstant.ATT_CONVERT_UNIT_HOUR.equals(convertUnit)) {
            convertUnitName = I18nUtil.i18nCode("common_hour");
        } else if (AttConstant.ATT_CONVERT_UNIT_DAY.equals(convertUnit)) {
            convertUnitName = I18nUtil.i18nCode("common_day");
        }
        return attLeaveTypeItem.getLeaveTypeName() + "(" + convertUnitName + ")";
    }

    @Override
    @Transactional
    public void handlerTransferZKTime(List<AttLeaveTypeItem> attLeaveTypeItems) {

        List<AttLeaveType> attLeaveTypeList = new ArrayList<>();
        for (AttLeaveTypeItem attLeaveTypeItem : attLeaveTypeItems) {

            String leaveTypeName = attLeaveTypeItem.getLeaveTypeName();
            AttLeaveType attLeaveType = attLeaveTypeDao.findByLeaveTypeName(leaveTypeName);

            if (Objects.isNull(attLeaveType)) {
                attLeaveType = new AttLeaveType();
                attLeaveType.setInitFlag(Boolean.FALSE);
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(attLeaveTypeItem, attLeaveType, "id", "initFlag");
            if (StringUtils.isBlank(attLeaveType.getLeaveTypeNo())) {
                setLeaveTypeNo(attLeaveType);
            }
            attLeaveTypeList.add(attLeaveType);
        }
        attLeaveTypeDao.saveAll(attLeaveTypeList);
    }

    @Override
    public List<AttLeaveTypeItem> listLeaveTypeFilterTripAndOut() {
        List<AttLeaveType> attLeaveTypeList =
            attLeaveTypeDao.findByMarkOrderBySortNoAsc(AttConstant.ATT_CONVERT_MARK_LEAVETYPE);
        if (!CollectionUtil.isEmpty(attLeaveTypeList)) {
            Iterator<AttLeaveType> iterator = attLeaveTypeList.iterator();
            while (iterator.hasNext()) {
                AttLeaveType leaveType = iterator.next();
                // 过滤掉出差外出
                if ((AttCalculationConstant.AttAttendStatus.TRIP.equals(leaveType.getLeaveTypeNo())
                    || AttCalculationConstant.AttAttendStatus.OUT.equals(leaveType.getLeaveTypeNo()))) {
                    iterator.remove();
                }
            }
            List<AttLeaveTypeItem> attLeaveTypeItems =
                ModelUtil.copyListProperties(attLeaveTypeList, AttLeaveTypeItem.class);
            return attLeaveTypeItems;
        }
        return new ArrayList<>();
    }

    @Override
    public void updateSortNoByLeaveTypeNo(List<AttLeaveTypeItem> attLeaveTypeItemList) {
        List<AttLeaveType> attLeaveTypeList = new ArrayList<>();
        for (AttLeaveTypeItem attLeaveTypeItem: attLeaveTypeItemList) {
            AttLeaveType attLeaveType = attLeaveTypeDao.findByLeaveTypeNo(attLeaveTypeItem.getLeaveTypeNo());
            if (attLeaveType != null && attLeaveType.getSortNo() == null) {
                attLeaveType.setSortNo(attLeaveTypeItem.getSortNo());
                attLeaveTypeList.add(attLeaveType);
            }
        }
        if (attLeaveTypeList.size() > 0) {
            attLeaveTypeDao.saveAll(attLeaveTypeList);
        }
    }
}