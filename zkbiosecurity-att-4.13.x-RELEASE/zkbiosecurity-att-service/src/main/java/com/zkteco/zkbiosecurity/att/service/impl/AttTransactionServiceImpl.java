package com.zkteco.zkbiosecurity.att.service.impl;

import static com.zkteco.zkbiosecurity.core.utils.FileEncryptUtil.systemFilePath;

import java.io.File;
import java.sql.Timestamp;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.api.vo.AttApiTransactionItem;
import com.zkteco.zkbiosecurity.att.api.vo.MessagePushCloudSendMessageItem;
import com.zkteco.zkbiosecurity.att.bean.AttUsbTransactionBean;
import com.zkteco.zkbiosecurity.att.cache.AttCacheManager;
import com.zkteco.zkbiosecurity.att.cache.AttCalculationCacheManager;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttTimeSlotBO;
import com.zkteco.zkbiosecurity.att.constants.AttApiConstant;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttDeviceDao;
import com.zkteco.zkbiosecurity.att.dao.AttPointDao;
import com.zkteco.zkbiosecurity.att.dao.AttTransactionDao;
import com.zkteco.zkbiosecurity.att.model.AttDevice;
import com.zkteco.zkbiosecurity.att.model.AttPoint;
import com.zkteco.zkbiosecurity.att.model.AttTransaction;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.utils.DateUtil.DateStyle;
import com.zkteco.zkbiosecurity.event.constants.EventCenterConstants;
import com.zkteco.zkbiosecurity.event.service.Other2EventCenterService;
import com.zkteco.zkbiosecurity.event.vo.Other2EventCenterItem;
import com.zkteco.zkbiosecurity.hep.service.Hep4OtherTransactionService;
import com.zkteco.zkbiosecurity.hep.vo.Hep4OtherTransactionItem;
import com.zkteco.zkbiosecurity.license.provider.BaseLicenseProvider;
import com.zkteco.zkbiosecurity.pers.service.PersPersonCacheService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.push.enums.PushCenter4OtherEnum;
import com.zkteco.zkbiosecurity.push.service.PushCenter4OtherService;
import com.zkteco.zkbiosecurity.push.vo.PushCenter4OtherItem;
import com.zkteco.zkbiosecurity.redis.redisson.annotation.RedisLock;
import com.zkteco.zkbiosecurity.system.constants.BaseDataConstants;
import com.zkteco.zkbiosecurity.system.service.BaseDataCleanService;
import com.zkteco.zkbiosecurity.system.service.BaseMessageNotificationService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.ModuleInfoService;
import com.zkteco.zkbiosecurity.system.vo.BaseMessageNotificationItem;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 考勤原始记录、打卡记录
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/11/23 11:10
 * @since 1.0.0
 */
@Slf4j
@Service
public class AttTransactionServiceImpl implements AttTransactionService {

    @Autowired
    private BaseDataCleanService baseDataCleanService;
    @Autowired
    private AttTransactionDao attTransactionDao;
    @Autowired
    private AttDeviceDao attDeviceDao;
    @Autowired
    private AttPointDao attPointDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private ModuleInfoService moduleInfoService;
    @Autowired
    private AttParkDeviceService attParkDeviceService;
    @Autowired
    private AttDeviceService attDeviceService;
    @Autowired(required = false)
    private AttGetAccTransactionService attGetAccTransactionService;
    @Autowired(required = false)
    private AttGetParkTransactionService attGetParkTransactionService;
    @Autowired(required = false)
    private AttGetInsTransactionService attGetInsTransactionService;
    @Autowired(required = false)
    private AttGetPidTransactionService attGetPidTransactionService;
    @Autowired(required = false)
    private AttGetVmsTransactionService attGetVmsTransactionService;
    @Autowired(required = false)
    private AttCloudMessageSendService attCloudMessageSendService;
    @Autowired
    private BaseLicenseProvider baseLicenseProvider;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired(required = false)
    private Att4LineTransactionService att4LineTransactionService;
    @Autowired
    private AttPointService attPointService;
    @Autowired(required = false)
    private Hep4OtherTransactionService hep4OtherTransactionService;
    @Autowired
    private AttDeviceOptionService attDeviceOptionService;
    @Autowired
    private AttCacheManager attCacheManager;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttRealTimePushService attRealTimePushService;
    @Autowired
    private AttCalculationCacheManager attCalculationCacheManager;
    @Autowired
    private AttDayCardDetailService attDayCardDetailService;
    @Autowired(required = false)
    private Other2EventCenterService other2EventCenterService;
    @Autowired(required = false)
    private AttGetIvsDeviceService attGetIvsDeviceService;
    @Autowired(required = false)
    private AttGetPsgDeviceService attGetPsgDeviceService;
    @Autowired(required = false)
    private PushCenter4OtherService pushCenter4OtherService;
    @Autowired
    private AttPersonSchDataService attPersonSchDataService;
    @Autowired
    private AttTimeSlotService attTimeSlotService;
    @Autowired
    private AttRecordService attRecordService;
    @Autowired
    private PersPersonCacheService persPersonCacheService;
    @Autowired
    private BaseMessageNotificationService baseMessageNotificationService;
    @Autowired(required = false)
    private AttGetEsdcDataService attGetEsdcDataService;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AttSignAddressService attSignAddressService;

    @Override
    @Transactional
    public AttTransactionItem saveItem(AttTransactionItem item) {
        // 如果是人证推送上来的数据则根据约束条件的字段判断是否存在该对象 不存在则新增记录保存 存在则不做任何操作 modified by jinxian.huang 2019-06-28
        List<AttTransaction> attTransactionList = null;
        if (item.getMark().equals(AttConstant.ATTTRANSACTION_MARK_PID)) {
            attTransactionList = attTransactionDao.findAttTransactionByPersonPinAndAttDatetimeAndDeviceSn(
                item.getPersonPin(), item.getAttDatetime(), item.getDeviceSn());
        }
        if (CollectionUtil.isEmpty(attTransactionList)) {
            AttTransaction attTransaction = new AttTransaction();
            ModelUtil.copyPropertiesIgnoreNull(item, attTransaction);
            attTransactionDao.save(attTransaction);
            item.setId(attTransaction.getId());

            // 【实时计算】新增考勤实时事件
            String crossDay = attParamService.getCrossDay();
            attCalculationCacheManager.addRealTimeEvent(attTransaction.getPersonPin(), attTransaction.getAttDatetime(),
                crossDay);
        }
        return item;
    }

    @Override
    public List<AttTransactionItem> getByCondition(AttTransactionItem condition) {
        return (List<AttTransactionItem>)attTransactionDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return loadPagerByAuthUserFilter(null, (AttTransactionItem)condition, page, size);
    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, AttTransactionItem condition, int pageNo, int pageSize,
        long limitCount) {
        buildCondition(sessionId, condition);

        // 自定义排序
        StringBuffer sql = new StringBuffer(SQLUtil.getSqlByItem(condition));
        if (sql.indexOf("ORDER BY t.ATT_DATETIME") < 0) {
            sql.append(", t.ATT_DATETIME ASC");
        }

        Pager pager =
            attTransactionDao.getItemsBySql(condition.getClass(), sql.toString(), pageNo, pageSize, limitCount);

        Map<String, String> shortcutKeyNameMap = attDeviceService.getShortcutKeyNameMap();
        buildItems((List<AttTransactionItem>)pager.getData(), shortcutKeyNameMap);

        return pager;
    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, AttTransactionItem condition, int pageNo, int pageSize) {

        buildCondition(sessionId, condition);

        Pager pager =
            attTransactionDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        Map<String, String> shortcutKeyNameMap = attDeviceService.getShortcutKeyNameMap();
        buildItems((List<AttTransactionItem>)pager.getData(), shortcutKeyNameMap);

        return pager;
    }

    /**
     * 填充考勤点名称
     *
     * @param attTransactionItemList
     */
    private void buildItems(List<AttTransactionItem> attTransactionItemList, Map<String, String> shortcutKeyNameMap) {
        // 集合为空直接返回
        if (CollectionUtil.isEmpty(attTransactionItemList)) {
            return;
        }

        for (AttTransactionItem attTransactionItem : attTransactionItemList) {

            String attState = attTransactionItem.getAttState();
            if (StringUtils.isNotBlank(attState)) {
                String attStateName = shortcutKeyNameMap.get(attState);
                if (StringUtils.isNotBlank(attStateName)) {
                    attTransactionItem.setAttState(attStateName);
                }
            }

            if (StringUtils.isNotBlank(attTransactionItem.getAttPhotoUrl())) {
                // 处理其他模块的路径异常
                attTransactionItem.setAttPhotoUrl(attTransactionItem.getAttPhotoUrl().replace("\\", "/"));
            }

            if (ConstUtil.SYSTEM_MODULE_ATT.equals(attTransactionItem.getMark())
                && StringUtils.isBlank(attTransactionItem.getAttPhotoUrl())) {
                // 兼容如果是考勤设置且照片文件路径为空，拼凑照片路径，考勤记录和图片是分开上传的
                if (StringUtils.isNotBlank(attTransactionItem.getDeviceSn())) {
                    String attPhotoUrl = AttCommonUtils.buildAttPhotoUrl(attTransactionItem.getDeviceSn(),
                        attTransactionItem.getAttDate(), attTransactionItem.getAttDatetime(),
                        attTransactionItem.getPersonPin());
                    attTransactionItem.setAttPhotoUrl(attPhotoUrl);
                }
            }
        }

        // 按照类型分组
        Map<String, List<AttTransactionItem>> attTransactionItemMap =
            attTransactionItemList.stream().collect(Collectors.groupingBy(AttTransactionItem::getMark));
        attTransactionItemMap.forEach((mark, itemList) -> {
            if (Objects.isNull(mark) || CollectionUtil.isEmpty(itemList)) {
                return;
            }
            switch (mark) {
                // 考勤直接过滤
                case ConstUtil.SYSTEM_MODULE_ATT:
                    break;
                // 用于门禁和VMS模块获取对应考勤点
                // 获取门禁考勤点(特点：AttPoint表的deviceId对应的是设备某个门的ID)
                // 获取VMS考勤点(特点：AttPoint表的deviceId对应的是设备某个通道的ID)
                case ConstUtil.SYSTEM_MODULE_ACC:
                case ConstUtil.SYSTEM_MODULE_VMS:
                    String deviceSns = CollectionUtil.getPropertys(itemList, AttTransactionItem::getDeviceSn);
                    AttPointItem attPointItemCondition = new AttPointItem();
                    attPointItemCondition.setInDeviceSn(deviceSns);
                    List<AttPointItem> attPointItems = attPointService.getByCondition(attPointItemCondition);
                    Map<String, AttPointItem> pointItemMap = CollectionUtil.listToKeyMap(attPointItems,
                        item -> item.getDeviceSn() + "_" + (Objects.isNull(item.getDoorNo()) ? "" : item.getDoorNo()));
                    itemList.forEach(item -> {
                        AttPointItem attPointItem = pointItemMap
                            .get(item.getDeviceSn() + "_" + (Objects.isNull(item.getDoorNo()) ? "" : item.getDoorNo()));
                        if (Objects.nonNull(attPointItem)) {
                            item.setPointName(attPointItem.getPointName());
                        }
                    });
                    break;
                // 获取其他模块考勤点(特点：AttPoint表的deviceId对应的就是设备的ID)
                default:
                    String deviceIds = CollectionUtil.getPropertys(itemList, AttTransactionItem::getDeviceId);
                    AttPointItem otherPointCondition = new AttPointItem();
                    otherPointCondition.setInDeviceId(deviceIds);
                    List<AttPointItem> otherPointItems = attPointService.getByCondition(otherPointCondition);
                    Map<String, AttPointItem> otherPointItemMap =
                        CollectionUtil.listToKeyMap(otherPointItems, AttPointItem::getDeviceId);
                    itemList.forEach(item -> {
                        AttPointItem attPointItem = otherPointItemMap.get(item.getDeviceId());
                        if (Objects.nonNull(attPointItem)) {
                            item.setPointName(attPointItem.getPointName());
                        }
                    });
                    break;
            }
        });
    }

    @Override
    public Long getAllTransactionCount() {
        return attTransactionDao.count();
    }

    @Override
    public List<AttTransactionItem> getTransactionCloudItems(AttTransactionItem condition, int pageNo, int pageSize) {
        Pager pager =
            attTransactionDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        return (List<AttTransactionItem>)pager.getData();
    }

    /**
     * 封装条件
     *
     * @param sessionId
     * @param condition
     */
    private void buildCondition(String sessionId, AttTransactionItem condition) {

        // 使用员工自助登录的用户,只有自己数据的权限。若登录类型为pers,则返回pin加入查询sql
        String persperPin = attPersonService.getPinByLoginType(sessionId);
        if (persperPin != null) {
            condition.setEquals(true);
            condition.setPersonPin(persperPin);
        } else {
            // 部门权限过滤
            String userId = authUserService.getUserIdBySessionId(sessionId);
            if (StringUtils.isNotBlank(userId)) {
                condition.setUserId(userId);
            }

            // 前端默认包含下级
            if (StringUtils.isBlank(condition.getInDeptId())) {
                if (StringUtils.isNotBlank(condition.getDeptId())) {
                    List<String> deptIdList = authDepartmentService.getChildDeptIdsByDeptIds(condition.getDeptId());
                    condition.setInDeptId(StrUtil.collectionToStr(deptIdList));
                    condition.setDeptId(null);
                }
            }
        }
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                attTransactionDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AttTransactionItem getItemById(String id) {
        List<AttTransactionItem> items = getByCondition(new AttTransactionItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    @Transactional
    public void handlerAttLog(String sn, String dataStr) {

        // 如果设备不存在，则该设备上传的记录不保存
        AttDevice attDevice = attDeviceDao.findByDevSn(sn);
        if (attDevice == null) {
            log.warn("AttDevice Not Exist");
            return;
        }

        // 查询考勤区域
        AuthAreaItem authAreaItem = null;
        if (StringUtils.isNotBlank(attDevice.getAreaId())) {
            authAreaItem = authAreaService.getItemById(attDevice.getAreaId());
        }

        // 【实时计算】新增考勤事件，跨天计算结果
        String crossDay = attParamService.getCrossDay();

        // 是否支持口罩或体温
        boolean isSupportMaskOrTempFunOn = attDeviceOptionService.isSupportMaskOrTempBySn(sn);

        String[] dataAry = dataStr.split("\n");
        List<AttTransaction> attTransactionList = new ArrayList<>();
        AttTransaction attTransaction = null;
        for (String data : dataAry) {

            if (StringUtils.isBlank(data)) {
                continue;
            }

            // 协议数据拆分
            String[] attTransactionArray = data.split("\t");
            String personPin = attTransactionArray[0];

            // 如果人员不存在，则记录不保存，离职人员也过滤考勤上来
            PersPersonCacheItem personCacheItem = persPersonCacheService.getPersonCacheByPin(personPin);
            if (Objects.isNull(personCacheItem)) {
                continue;
            }

            // C6 Horus设备开启定位功能后，当前设备经纬度信息会附带在ATT_LOG中的Verify_Time字段中进行上传，如：2020-08-21T06:20:59,-1.000000,-1.000000
            String attDatetimeStr = attTransactionArray[1];
            String longitude = null;
            String latitude = null;
            if (attDatetimeStr.contains(",")) {
                String[] attDatetimeAndLatLonArray = attDatetimeStr.split(",");
                attDatetimeStr = attDatetimeAndLatLonArray[0];
                // 经度
                longitude = attDatetimeAndLatLonArray[1];
                // 纬度
                latitude = attDatetimeAndLatLonArray[2];
            }

            Date attDatetime = Timestamp.valueOf(attDatetimeStr);

            // 记录存在不保存
            if (attTransactionDao.countByPersonPinAndAttDatetime(personPin, attDatetime) == 0) {
                attTransaction = new AttTransaction();
                attTransaction.setPersonPin(personPin);

                attTransaction.setPersonName(personCacheItem.getName());
                attTransaction.setPersonLastName(personCacheItem.getLastName());
                attTransaction.setDeptId(personCacheItem.getDeptId());
                attTransaction.setDeptName(personCacheItem.getDeptName());
                attTransaction.setDeptCode(personCacheItem.getDeptCode());

                // 保存设备信息，区域信息
                attTransaction.setDeviceSn(sn);
                attTransaction.setDeviceId(attDevice.getId());
                attTransaction.setDeviceName(attDevice.getDevName());
                if (null != authAreaItem) {
                    attTransaction.setAreaId(attDevice.getAreaId());
                    attTransaction.setAreaNo(authAreaItem.getCode());
                    attTransaction.setAreaName(authAreaItem.getName());
                }

                // 保存考勤时间，以及考勤状态
                String attDate = attDatetimeStr.split(" ")[0];
                String attTime = attDatetimeStr.split(" ")[1];
                // 打卡时间(考勤日期时间) 2018-04-26 11:12:21
                attTransaction.setAttDatetime(attDatetime);
                attTransaction.setAttDate(attDate);// 考勤日期 2018-04-26
                attTransaction.setAttTime(attTime);// 考勤时间 11:12:21
                attTransaction.setAttState(attTransactionArray[2]);// 考勤状态
                attTransaction.setAttVerify(attTransactionArray[3]);// 验证方式
                attTransaction.setMark(ConstUtil.SYSTEM_MODULE_ATT);

                // 记录上来直接指定图片路径不考虑图片延后上传或没图片
                String attPhotoUrl = AttCommonUtils.buildAttPhotoUrl(sn, attDate, attDatetime, personPin);
                attTransaction.setAttPhotoUrl(attPhotoUrl);

                // 支持口罩和体温
                if (isSupportMaskOrTempFunOn) {
                    // 7 第八位代表 口罩
                    if (attTransactionArray.length > 7 && StringUtils.isNotBlank(attTransactionArray[7])) {
                        attTransaction.setMaskFlag(attTransactionArray[7]);
                    }
                    // 8 第九位代表 温度
                    if (attTransactionArray.length > 8 && StringUtils.isNotBlank(attTransactionArray[8])) {
                        attTransaction.setTemperature(attTransactionArray[8]);
                    }
                }

                attTransaction.setLongitude(longitude);
                attTransaction.setLatitude(latitude);

                attTransactionList.add(attTransaction);

                // 【实时计算】新增考勤事件
                attCalculationCacheManager.addRealTimeEvent(attTransaction.getPersonPin(),
                    attTransaction.getAttDatetime(), crossDay);

                // 单条推送至其他模块
                pushTransactionToOthers(attTransaction, personCacheItem);
            }
        }

        if (attTransactionList.size() > 0) {
            // 批量保存
            attTransactionDao.saveAll(attTransactionList);
            // 批量推送至其他模块
            pushTransactionsToOthers(attTransactionList, attDevice);
        }
    }

    /**
     * 批量推送至其他模块
     *
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/9/2 10:01
     * @since 1.0.0
     */
    private void pushTransactionsToOthers(List<AttTransaction> attTransactionList, AttDevice attDevice) {
        List<AttTransactionItem> attTransactionItemList =
            ModelUtil.copyListProperties(attTransactionList, AttTransactionItem.class);

        // 设备推送上来的记录进行实时点名的推送
        attRealTimePushService.pushTransaction(attTransactionItemList);

        // 考勤消息推送到line
        if (Objects.nonNull(att4LineTransactionService)) {
            try {
                att4LineTransactionService.pushTransactionsToLine(
                    ModelUtil.copyListProperties(attTransactionList, Att4LineTransactionItem.class),
                    attDevice.getDevName());
            } catch (Exception e) {
                log.error("sendDataToLine Fail ", e);
            }
        }

        // 推送至数据推送中心
        if (Objects.nonNull(pushCenter4OtherService)) {
            try {
                PushCenter4OtherItem pushCenter4OtherItem = new PushCenter4OtherItem();
                pushCenter4OtherItem.setPushCenter4OtherEnum(PushCenter4OtherEnum.ATT_TRANSACTION);
                List<String> stringList = new ArrayList<>();
                for (AttTransaction attTrans : attTransactionList) {
                    stringList.add(JSON.toJSONString(attTrans));
                }
                pushCenter4OtherItem.setContentList(stringList);
                pushCenter4OtherService.pushData(pushCenter4OtherItem);
            } catch (Exception e) {
                log.error("sendDataToPushCenter Fail ", e);
            }
        }
    }

    /**
     * 单条推送至其他模块
     *
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/9/2 10:01
     * @since 1.0.0
     */
    private void pushTransactionToOthers(AttTransaction attTransaction, PersPersonCacheItem personCacheItem) {

        // 推送支持温度检测口罩佩戴和红外温度的记录到防疫模块 add by liangaoqi 20200305
        try {
            if (StringUtils.isNotBlank(attTransaction.getPersonPin())
                && (StringUtils.isNotBlank(attTransaction.getMaskFlag())
                    || StringUtils.isNotBlank(attTransaction.getTemperature()))) {
                sendDataToHep(attTransaction);
            }
        } catch (Exception e) {
            log.error("sendDataToHep Fail", e);
        }

        // 推送到事件中心
        try {
            if (Objects.nonNull(other2EventCenterService)) {
                sendDataToEventCenter(attTransaction);
            }
        } catch (Exception e) {
            log.error("sendDataToEventCenter Fail", e);
        }

        // 消息推送，发送邮件、SMS等信息
        try {
            sendMessage(attTransaction, personCacheItem);
        } catch (Exception e) {
            log.error("sendDataToEventCenter Fail", e);
        }
    }

    private void sendMessage(AttTransaction attTransaction, PersPersonCacheItem personCacheItem) {
        String pin = attTransaction.getPersonPin();
        if (StringUtils.isNotBlank(pin)) {
            BaseMessageNotificationItem baseMessageNotificationItem = new BaseMessageNotificationItem();
            baseMessageNotificationItem.setPin(pin);
            baseMessageNotificationItem.setModule(ConstUtil.SYSTEM_MODULE_ATT);
            // Email
            String subject = I18nUtil.i18nCode("att_eventCenter_sign");
            baseMessageNotificationItem.setIsSendMail(personCacheItem.getIsSendMail());
            baseMessageNotificationItem.setEmail(personCacheItem.getEmail());
            baseMessageNotificationItem.setSubject(subject);
            // SMS
            baseMessageNotificationItem.setSendSMS(personCacheItem.getSendSMS());
            String mobilePhone = personCacheItem.getMobilePhone();
            if (StringUtils.isNotBlank(mobilePhone)) {
                mobilePhone = mobilePhone.toString().replace("-", "");
                baseMessageNotificationItem.setMobilePhone(mobilePhone);
            }
            // Whatsapp
            baseMessageNotificationItem.setSendWhatsapp(personCacheItem.getSendWhatsapp());
            baseMessageNotificationItem.setWhatsappMobileNo(personCacheItem.getWhatsappMobileNo());
            // 内容
            String content = getMailContent(attTransaction);
            baseMessageNotificationItem.setContent(content);
            baseMessageNotificationService.sendMessage(baseMessageNotificationItem);
        }
    }

    private String getMailContent(AttTransaction attTransaction) {
        String firstName =
            StringUtils.isNotBlank(attTransaction.getPersonName()) ? attTransaction.getPersonName().trim() : "";
        String lastName =
            StringUtils.isNotBlank(attTransaction.getPersonLastName()) ? attTransaction.getPersonLastName().trim() : "";
        String name = "";
        if (StringUtils.isNotBlank(firstName) || StringUtils.isNotBlank(lastName)) {
            name = "(" + (firstName + " " + lastName).trim() + ")";
        }
        name = attTransaction.getPersonPin() + name;
        return I18nUtil.i18nCode("common_time") + ": "
            + DateUtil.dateToString(attTransaction.getAttDatetime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS) + "<br/>"
            + I18nUtil.i18nCode("pers_person") + ": " + name + "<br/>" + I18nUtil.i18nCode("base_area_name") + ": "
            + attTransaction.getAreaName() + "<br/>" + I18nUtil.i18nCode("common_dev_name") + ": "
            + attTransaction.getDeviceName() + "<br/>";
    }

    /**
     * 推送到事件中心
     *
     * @param attTransaction:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/1/26 19:27
     * @since 1.0.0
     */
    private void sendDataToEventCenter(AttTransaction attTransaction) {
        Other2EventCenterItem eventCenterItem = new Other2EventCenterItem();
        eventCenterItem.setObjName(attTransaction.getPersonName());
        eventCenterItem.setObjType(BaseConstants.EVENT_OBJ_TYPE_PER);
        eventCenterItem.setObjKey(attTransaction.getPersonPin());
        eventCenterItem.setEventName(I18nUtil.i18nCode("att_eventCenter_sign"));
        eventCenterItem.setEventTime(attTransaction.getAttDatetime());
        eventCenterItem.setAreaId(attTransaction.getAreaId());
        eventCenterItem.setAreaName(attTransaction.getAreaName());
        eventCenterItem.setSourceKey(attTransaction.getDeviceSn());
        eventCenterItem.setSourceName(attTransaction.getDeviceName());
        eventCenterItem.setSourceModule(BaseConstants.ATT);
        eventCenterItem.setEventTypeCode("0");
        eventCenterItem.setEventLevelVal(EventCenterConstants.EVENT_LEVEL_NORMAL);
        eventCenterItem.setCaptureImgURL(attTransaction.getAttPhotoUrl());
        other2EventCenterService.push2EventCenter(eventCenterItem);
    }

    /**
     * 推送支持温度检测口罩佩戴和红外温度的记录到防疫模块 add by liangaoqi 20200305
     *
     * @param attTransaction
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/3/5 17:23
     */
    private void sendDataToHep(AttTransaction attTransaction) {
        if (Objects.nonNull(hep4OtherTransactionService)) {
            Hep4OtherTransactionItem att4HepTransactionItem = new Hep4OtherTransactionItem();
            att4HepTransactionItem.setEventTime(attTransaction.getAttDatetime());
            att4HepTransactionItem.setDevId(attTransaction.getDeptId());
            att4HepTransactionItem.setDevSn(attTransaction.getDeviceSn());
            att4HepTransactionItem.setDevAlias(attTransaction.getDeviceName());
            att4HepTransactionItem.setDeptId(attTransaction.getDeptId());
            att4HepTransactionItem.setDeptCode(attTransaction.getDeptCode());
            att4HepTransactionItem.setDeptName(attTransaction.getDeptName());
            att4HepTransactionItem.setPin(attTransaction.getPersonPin());
            att4HepTransactionItem.setName(attTransaction.getPersonName());
            att4HepTransactionItem.setLastName(attTransaction.getPersonLastName());
            att4HepTransactionItem.setAreaCode(attTransaction.getAreaNo());
            att4HepTransactionItem.setAreaName(attTransaction.getAreaName());
            att4HepTransactionItem.setMaskFlag(attTransaction.getMaskFlag());
            att4HepTransactionItem.setTemperature(attTransaction.getTemperature());
            att4HepTransactionItem.setIsFrom(ConstUtil.SYSTEM_MODULE_ATT);

            // 增加事件点名称和照片路径 (考勤设备记录和照片是分开上传或设备未开启图片上传,实时推送的未能获取到路径)
            att4HepTransactionItem.setEventPointName(attTransaction.getDeviceName());
            att4HepTransactionItem.setVidLinkageHandle(attTransaction.getAttPhotoUrl());

            hep4OtherTransactionService.pushTransactionsToHep(att4HepTransactionItem);
        }
    }

    @Override
    public List<?> getItemData(Class<?> cls, BaseItem condition, int beginIndex, int endIndex) {
        return attTransactionDao.getItemsDataBySql(cls, SQLUtil.getSqlByItem(condition), beginIndex, endIndex, true);
    }

    @Override
    public List<AttTransactionItem> exportItemListByAuthFilter(String sessionId, Class<?> cls,
        AttTransactionItem condition, int beginIndex, int endIndex) {

        buildCondition(sessionId, condition);
        Map<String, String> shortcutKeyNameMap = attDeviceService.getShortcutKeyNameMap();

        List<AttTransactionItem> attTransactionItemList =
            attTransactionDao.getItemsDataBySql(cls, SQLUtil.getSqlByItem(condition), beginIndex, endIndex, true);
        List<List<AttTransactionItem>> splitAttTransactionLists =
            CollectionUtil.split(attTransactionItemList, CollectionUtil.splitSize);
        splitAttTransactionLists.forEach(splitAttTransactionList -> {
            // 填充考勤点名称信息
            buildItems(splitAttTransactionList, shortcutKeyNameMap);
        });
        return attTransactionItemList;
    }

    @Override
    public void handlerAttPhoto(String data) {
        // 增加对设备上传的照片数据校验，防止因设备异常导致的Data为空的情况（明法发现的问题）--add by hook.fang 2019-07-17
        if (StringUtils.isBlank(data)) {
            log.error("save attPhoto error, data is empty");
            return;
        }
        JSONObject jsonObject = JSON.parseObject(data);
        // PIN = 20220408181849-1.jpg = 考勤时间 + "-" + pin + "jpg"
        String photoName = jsonObject.getString("PIN");
        String sn = jsonObject.getString("SN");
        String imgStr = jsonObject.getString("photo");
        // 修改保存考勤照片的路径组装异常问题，以前默认只保存在当日的目录下，如果历史记录也是保存在获取的那天日期目录下，导致后续再查看照片找不到--add by hook.fang 2019-07-02
        if (StringUtils.isNotBlank(photoName) && photoName.length() > 8) {
            String attDate = photoName.substring(0, 8);
            String filePath =
                AttCommonUtils.buildAttPhotoFilePath(sn, DateUtil.stringToString(attDate, DateStyle.YYYY_MM_DD));
            FileUtils.saveFile(filePath, photoName, imgStr, false);
            // 图片加密
            FileEncryptUtil.encryptFileByPath(filePath + photoName);
            // 推送考勤图片至其他模块
            pushPhotoToOther(sn, photoName, filePath);
        }
    }

    /**
     * 推送考勤图片至其他模块
     *
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/9/2 10:15
     * @since 1.0.0
     */
    private void pushPhotoToOther(String sn, String photoName, String filePath) {
        // 防疫实时监控照片推送 因记录和照片设备是分开上传, 防疫实时记录推送时照片不一定显示的出来,需要再次触发通知
        try {
            if (Objects.nonNull(hep4OtherTransactionService)) {

                // 是否支持口罩或体温
                boolean isSupportMaskOrTempFunOn = attDeviceOptionService.isSupportMaskOrTempBySn(sn);
                if (isSupportMaskOrTempFunOn) {
                    Hep4OtherTransactionItem hep4OtherTransactionItem = new Hep4OtherTransactionItem();
                    hep4OtherTransactionItem.setPhotoName(photoName);
                    hep4OtherTransactionItem
                        .setEventTime(DateUtil.stringToDate(photoName.split("-")[0], DateStyle.YYYYMMDDHHMMSS));
                    hep4OtherTransactionItem.setDevSn(sn);
                    hep4OtherTransactionItem.setVidLinkageHandle(filePath + "" + photoName);
                    hep4OtherTransactionService.pushPhotoToHep(hep4OtherTransactionItem);
                }
            }
        } catch (Exception e) {
            log.error(" pushPhotoToHep Exception: ", e);
        }

        // 推送至数据推送中心
        if (Objects.nonNull(pushCenter4OtherService)) {
            try {
                PushCenter4OtherItem pushCenter4OtherItem = new PushCenter4OtherItem();
                pushCenter4OtherItem.setPushCenter4OtherEnum(PushCenter4OtherEnum.ATT_TRANSACTION_IMAGE);
                pushCenter4OtherItem.setContent(filePath + "" + photoName);
                pushCenter4OtherService.pushData(pushCenter4OtherItem);
            } catch (Exception e) {
                log.error("sendImageDataToPushCenter Fail ", e);
            }
        }
    }

    @Override
    public int countByPersonPinAndAttDatetime(String pin, Date attDateTime) {
        return attTransactionDao.countByPersonPinAndAttDatetime(pin, attDateTime);
    }

    @Override
    public List<AttApiTransactionItem> getApiTransactionList(String personPin, Date startDate, Date endDate, int pageNo,
        int pageSize, boolean isApp) {
        List<AttApiTransactionItem> attApiTransactionItemList = new ArrayList<>();
        AttTransactionItem attTrans = new AttTransactionItem();
        // 设置查询条件
        if (StringUtils.isNotBlank(personPin)) {
            attTrans.setEquals(true);
            attTrans.setPersonPin(personPin);
        }
        if (startDate != null && endDate != null) {
            attTrans.setBeginDate(startDate);
            attTrans.setEndDate(endDate);
        }

        // 小程序、app不返回补签记录
        if (isApp) {
            attTrans.setNotMark("att-sign");
        }

        List<AttTransactionItem> attTransactionItemList =
            (List<AttTransactionItem>)getItemsByPage(attTrans, pageNo - 1, pageSize).getData();
        if (!attTransactionItemList.isEmpty()) {
            attTransactionItemList.forEach(attTransactionItem -> {
                AttApiTransactionItem attApiTransactionItem = buildAttApiTransactionItem(attTransactionItem);
                attApiTransactionItemList.add(attApiTransactionItem);
            });
        }
        return attApiTransactionItemList;
    }

    @Override
    public Pager getApiTransactionPager(String personPin, Date startDate, Date endDate, int pageNo, int pageSize) {

        AttTransactionItem attTrans = new AttTransactionItem();
        // 设置查询条件
        if (StringUtils.isNotBlank(personPin)) {
            attTrans.setPersonPin(personPin);
        }
        if (startDate != null && endDate != null) {
            attTrans.setBeginDate(startDate);
            attTrans.setEndDate(endDate);
        }
        // 打卡记录不过补签记录，向宗亮反馈，产品确认修改
        // attTrans.setNotMark("att-sign");
        Pager pager = getItemsByPage(attTrans, pageNo - 1, pageSize);
        List<AttTransactionItem> attTransactionItemList = (List<AttTransactionItem>)pager.getData();
        List<AttApiTransactionItem> attApiTransactionItemList = new ArrayList<>();
        if (!attTransactionItemList.isEmpty()) {
            attTransactionItemList.forEach(attTransactionItem -> {
                AttApiTransactionItem attApiTransactionItem = buildAttApiTransactionItem(attTransactionItem);
                attApiTransactionItemList.add(attApiTransactionItem);
            });
        }
        pager.setData(attApiTransactionItemList);
        return pager;
    }

    private AttApiTransactionItem buildAttApiTransactionItem(AttTransactionItem attTransactionItem) {
        AttApiTransactionItem attApiTransactionItem = new AttApiTransactionItem();
        attApiTransactionItem.setId(attTransactionItem.getId()).setAreaName(attTransactionItem.getAreaName())
            .setDeptName(attTransactionItem.getDeptName()).setDevSn(attTransactionItem.getDeviceSn())
            .setEventTime(AttDateUtils.dateToStrAsLong(attTransactionItem.getAttDatetime()))
            .setLastName(attTransactionItem.getPersonLastName()).setName(attTransactionItem.getPersonName())
            .setPin(attTransactionItem.getPersonPin()).setVerifyModeName(attTransactionItem.getAttVerify())
            .setAccZone(attTransactionItem.getAreaNo()).setAttPlace(attTransactionItem.getAttPlace())
            .setMark(attTransactionItem.getMark());
        return attApiTransactionItem;
    }

    @Override
    @Transactional
    public ApiResultMessage addApiTransaction(int deviceNum, String pin, String time) {
        int attLicensePoints = baseLicenseProvider.getControlCount(BaseConstants.ATT);
        if (deviceNum > attLicensePoints) {
            return ApiResultMessage.message(AttApiConstant.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("common_license_maxCount"));
        }
        PersPersonItem personItem = persPersonService.getItemByPin(pin);
        if (Objects.isNull(personItem)) {
            return ApiResultMessage.message(AttApiConstant.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("pers_api_personNotExist"));
        }
        // 时间
        Date attDateTime = DateUtil.stringToDate(time);
        String attDate = AttDateUtils.dateToStrAsShort(attDateTime);
        String attTime = DateUtil.dateToString(attDateTime, DateUtil.DateStyle.HH_MM_SS);
        int count = countByPersonPinAndAttDatetime(pin, attDateTime);
        if (count == 0) {
            AttTransactionItem attTran = new AttTransactionItem();
            // 设置人员信息
            attTran.setPersonPin(personItem.getPin());
            attTran.setPersonName(personItem.getName());
            attTran.setPersonLastName(personItem.getLastName());
            // 设置部门信息
            attTran.setDeptId(personItem.getDeptId());
            attTran.setDeptCode(personItem.getDeptCode());
            attTran.setDeptName(personItem.getDeptName());
            // 设置打卡时间
            attTran.setAttDatetime(attDateTime);
            attTran.setAttDate(attDate);
            attTran.setAttTime(attTime);
            // 设置数据来源--大掌柜人脸识别设备
            attTran.setMark("api-FR");
            saveItem(attTran);
        }
        return ApiResultMessage.successMessage();
    }

    @Override
    @Transactional
    public void handlerTransfer(List<AttTransactionItem> attTransactionItems) {
        List<List<AttTransactionItem>> splitAttTransactionItemList =
            CollectionUtil.split(attTransactionItems, CollectionUtil.splitSize);
        Collection<String> personPinList = CollectionUtil.getPropertyList(attTransactionItems,
            AttTransactionItem::getPersonPin, AttConstant.COMM_DEF_VALUE);
        List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(personPinList);
        Map<String, PersPersonItem> persPersonItemMap =
            CollectionUtil.listToKeyMap(persPersonItemList, PersPersonItem::getPin);

        Collection<String> devSnList = CollectionUtil.getPropertyList(attTransactionItems,
            AttTransactionItem::getDeviceSn, AttConstant.COMM_DEF_VALUE);
        List<AttDevice> attDeviceList = attDeviceDao.findByDevSnIn(devSnList);
        Map<String, AttDevice> attDeviceMap = CollectionUtil.listToKeyMap(attDeviceList, AttDevice::getDevSn);

        List<AuthAreaItem> authAreaItemList = authAreaService.getByCondition(new AuthAreaItem());
        Map<String, AuthAreaItem> authAreaItemMap =
            CollectionUtil.listToKeyMap(authAreaItemList, AuthAreaItem::getCode);
        for (List<AttTransactionItem> attTransactionItemList : splitAttTransactionItemList) {
            List<AttTransaction> attTransactions = new ArrayList<>();
            for (AttTransactionItem attTransactionItem : attTransactionItemList) {
                String personPin = attTransactionItem.getPersonPin();
                AttTransaction attTransaction = new AttTransaction();
                ModelUtil.copyPropertiesIgnoreNullWithProperties(attTransactionItem, attTransaction, "id");

                PersPersonItem persPersonItem = persPersonItemMap.get(personPin);
                if (Objects.nonNull(persPersonItem)) {
                    attTransaction.setDeptId(persPersonItem.getDeptId());
                    attTransaction.setDeptCode(persPersonItem.getDeptCode());
                    attTransaction.setDeptName(persPersonItem.getDeptName());
                    attTransaction.setPersonName(persPersonItem.getName());
                    attTransaction.setPersonLastName(persPersonItem.getLastName());
                } else {
                    attTransaction.setDeptId(null);
                }
                String areaNo = attTransactionItem.getAreaNo();
                AuthAreaItem authAreaItem = authAreaItemMap.get(areaNo);
                if (Objects.nonNull(authAreaItem)) {
                    attTransaction.setAreaId(authAreaItem.getId());
                    attTransaction.setAreaNo(authAreaItem.getCode());
                    attTransaction.setAreaName(authAreaItem.getName());
                } else {
                    attTransaction.setAreaId(null);
                }
                String deviceSn = attTransactionItem.getDeviceSn();
                AttDevice attDevice = attDeviceMap.get(deviceSn);
                if (Objects.nonNull(attDevice)) {
                    attTransaction.setDeviceId(attDevice.getId());
                    attTransaction.setDeviceSn(attDevice.getDevSn());
                } else {
                    attTransaction.setDeviceId(null);
                }
                String mark = attTransactionItem.getMark();
                if (StringUtils.isNotBlank(mark)) {
                    if (I18nUtil.i18nCode("att_statistical_att").equals(mark)) {
                        attTransaction.setMark("att");
                    }
                    if (I18nUtil.i18nCode("att_statistical_acc").equals(mark)) {
                        attTransaction.setMark("acc");
                    }
                    if (I18nUtil.i18nCode("att_statistical_park").equals(mark)) {
                        attTransaction.setMark("park");
                    }

                    if (I18nUtil.i18nCode("att_attPoint_ins").equals(mark)) {
                        attTransaction.setMark("ins");
                    }

                    if (I18nUtil.i18nCode("att_attPoint_pid").equals(mark)) {
                        attTransaction.setMark("pid");
                    }
                }
                attTransactions.add(attTransaction);
            }
            attTransactionDao.saveAll(attTransactions);
        }
    }

    @Override
    public Integer countAttTransaction(Date startTime, Date endTime) {
        return attTransactionDao.countAttTransactionBetween(startTime, endTime);
    }

    @Override
    @Transactional
    public void deleteDataTransfer() {
        attTransactionDao.deleteAll();
    }

    @Override
    @Transactional
    public ZKResultMsg importData(List<AttUsbTransactionBean> attTransItemList, String sn) throws Exception {
        // 获取设备信息和对应区域信息
        AttDeviceItem dev = attDeviceService.getItemBySn(sn);
        AuthAreaItem authAreaItem = authAreaService.getItemById(dev.getAreaId());

        // 存人员和时间，避免重复
        ArrayList<String> tempList = new ArrayList<String>();

        int size = CollectionUtil.splitSize;
        long count = attTransItemList.size() / size;
        if (attTransItemList.size() % size > 0) {
            count++;
        }
        List<AttUsbTransactionBean> attUsbTransList = null;

        for (int page = 0; page < count; page++) {
            List<AttTransaction> attTransactionList = new ArrayList<>();
            int toIndex = page * size + size < attTransItemList.size() ? page * size + size : attTransItemList.size();
            attUsbTransList = attTransItemList.subList(page * size, toIndex);

            // 根据pin号，获取人员信息
            Collection<String> personPinList = CollectionUtil.getPropertyList(attUsbTransList,
                AttUsbTransactionBean::getPin, AttConstant.COMM_DEF_VALUE);
            // 改用通过人事接口获取人事信息 modified by jinxian.huang 2019-08-02
            List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(personPinList);
            // 人员信息不存在则跳过
            if (!CollectionUtil.isEmpty(persPersonItemList)) {
                Map<String, PersPersonItem> persPersonItemMap =
                    CollectionUtil.listToKeyMap(persPersonItemList, PersPersonItem::getPin);
                for (AttUsbTransactionBean attUsbData : attUsbTransList) {
                    PersPersonItem persPersonItem = persPersonItemMap.get(attUsbData.getPin());
                    if (Objects.isNull(persPersonItem)) {
                        continue;
                    }

                    AttTransaction attTransaction = new AttTransaction();
                    attTransaction.setPersonPin(persPersonItem.getPin());
                    attTransaction.setPersonName(persPersonItem.getName());
                    attTransaction.setPersonLastName(persPersonItem.getLastName());
                    attTransaction.setDeptId(persPersonItem.getDeptId());
                    attTransaction.setDeptCode(persPersonItem.getDeptCode());
                    attTransaction.setDeptName(persPersonItem.getDeptName());

                    Date attDatetime = DateUtil.parseDate(attUsbData.getVerifyTime(), "yyyy-MM-dd HH:mm:ss");
                    if (attTransactionDao.countByPersonPinAndAttDatetime(attUsbData.getPin(), attDatetime) > 0) {
                        continue;
                    }

                    if (Objects.nonNull(authAreaItem)) {
                        attTransaction.setAreaId(authAreaItem.getId());
                        attTransaction.setAreaNo(authAreaItem.getCode());
                        attTransaction.setAreaName(authAreaItem.getName());
                    }

                    attTransaction.setDeviceId(dev.getId());
                    attTransaction.setDeviceSn(dev.getDevSn());

                    attTransaction.setAttDatetime(attDatetime);
                    attTransaction.setAttDate(DateUtil.getDate(attDatetime));
                    attTransaction.setAttTime(DateUtil.getTime(attDatetime));
                    attTransaction.setAttVerify(attUsbData.getVerifyType());
                    attTransaction.setMark(ConstUtil.SYSTEM_MODULE_ATT);

                    String key = attTransaction.getPersonPin() + "_" + AttDateUtils.dateToStrAsLong(attDatetime);
                    if (!tempList.contains(key)) {
                        attTransactionList.add(attTransaction);
                        tempList.add(key);
                    }
                }
                attTransactionDao.saveAll(attTransactionList);
            }
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    public Boolean validSn(String sn) {
        return !attDeviceService.vaildSn(sn);
    }

    @Override
    @RedisLock(lockName = "executeAttTimingDataCleanRedisson", waitTime = 0, expire = 600)
    @Transactional
    public void executeAttTimingDataClean(String paramValue) {
        // 清除考勤事件(tranaction)
        baseDataCleanService.execute("AttTransaction", "attDatetime", paramValue);
        // 清除考勤记录(record)
        baseDataCleanService.execute("AttRecord", "attDate", paramValue);
        // 其他需要清除的数据
        executeAttDataClean(paramValue);
        // 数据清理照片
        BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("attReportDataClean");
        JSONObject json = null;
        // 防止参数未成功初始化导致的空指针异常
        if (Objects.isNull(baseSysParamItem) || StringUtils.isBlank(baseSysParamItem.getParamValue())) {
            json = new JSONObject();
            json.put("keptPhoto", "15");
            json.put("runtime", "01:00:00");
            json.put("keptPhotoType", BaseDataConstants.DATA_CLEAN_KEPTMONTH);
        } else {
            json = JSON.parseObject(baseSysParamItem.getParamValue());
        }
        String paramValuePhoto = JSON.toJSONString(json);
        // 清除考勤事件照片
        delPhotos(paramValuePhoto);
    }

    /**
     * 考勤数据定时清理
     * 
     * @param paramValue:
     * @return void
     * <AUTHOR>
     * @date 2020-12-29 14:25
     * @since 1.0.0
     */
    private void executeAttDataClean(String paramValue) {

        String keptMonth = null;
        String keptType = BaseDataConstants.DATA_CLEAN_KEPTMONTH;
        try {
            JSONObject json = JSON.parseObject(paramValue);
            if (json.containsKey("keptType")) {
                keptType = json.getString("keptType");
            }
            keptMonth = json.getString("keptMonth");
        } catch (JSONException e) {
            log.info("Incorrect format of data clean paramValue :" + paramValue);
        }
        if (StringUtils.isBlank(keptMonth)) {
            keptMonth = paramValue;
        }
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        if (BaseDataConstants.DATA_CLEAN_KEPTDAY.equals(keptType)) {
            calendar.add(Calendar.DATE, -Integer.parseInt(keptMonth));
        } else if (BaseDataConstants.DATA_CLEAN_KEPWEEK.equals(keptType)) {
            calendar.add(Calendar.WEEK_OF_YEAR, -Integer.parseInt(keptMonth));
        } else {
            calendar.add(Calendar.MONTH, -Integer.parseInt(keptMonth));
        }
        Date keptMonthDate = calendar.getTime();
        String keptMonthDateStr = DateUtil.dateToString(keptMonthDate, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);

        // 清除日打卡详情 因日打卡详情从原始记录表统计出来的 清除原始记录表同时也要清理日打卡详情表,
        // 又因baseDataCleanService.execute()的字段需要Date类型, 没法直接调用,改为自删
        attDayCardDetailService.dataClean(keptMonthDateStr);

    }

    @Override
    @Transactional
    public AttTransactionItem saveTransactionItem(AttTransactionItem item) {
        AttTransaction attTransaction = new AttTransaction();
        ModelUtil.copyPropertiesIgnoreNull(item, attTransaction);
        attTransactionDao.save(attTransaction);
        item.setId(attTransaction.getId());

        // 【实时计算】新增考勤事件
        String crossDay = attParamService.getCrossDay();
        attCalculationCacheManager.addRealTimeEvent(attTransaction.getPersonPin(), attTransaction.getAttDatetime(),
            crossDay);

        // 实时点名推送请假
        attRealTimePushService.pushTransaction(Arrays.asList(item));

        return item;
    }

    @Override
    public List<AttTransactionItem> getItemListByTimeAndPersPins(Date beginDate, Date endDate,
        Collection<String> attPersPins) {
        List<Object[]> attTransactionList = attTransactionDao.getItemByTimeAndPersPins(beginDate, endDate, attPersPins);
        ArrayList<AttTransactionItem> attPersSignItemList = new ArrayList<AttTransactionItem>();
        if (attTransactionList.size() > 0) {
            for (Object[] objects : attTransactionList) {
                AttTransactionItem attPersSignItem = new AttTransactionItem();
                attPersSignItem.setPersonPin((String)objects[0]);
                attPersSignItem.setAttDatetime((Date)objects[1]);
                attPersSignItemList.add(attPersSignItem);
            }
        }
        return attPersSignItemList;
    }

    @Override
    public Map<String, List<String>> getTransactionMap(Date startDate, Date endDate, List<String> pins) {
        Map<String, List<String>> attTransactionMap = new HashMap<>();
        List<List<String>> splitPinList = CollectionUtil.split(pins, CollectionUtil.splitSize);
        for (List<String> subPinList : splitPinList) {
            List<Object[]> list = attTransactionDao.listTransactionBetweenDate(subPinList, startDate, endDate);
            for (Object[] obj : list) {
                String pin = (String)obj[0];
                String date = (String)obj[1];
                String time = (String)obj[2];
                String key = pin + AttCalculationConstant.KEY_CONNECTOR + date;
                List<String> transactionList = attTransactionMap.get(key);
                if (null == transactionList) {
                    transactionList = new ArrayList<>();
                }
                transactionList.add(date + " " + time);
                attTransactionMap.put(key, transactionList);
            }
        }

        return attTransactionMap;
    }

    @Override
    @Transactional
    public void deleteByAttDatetimeAndPinAndmark(Date attDatetime, String personPin, String mark) {
        List<AttTransaction> attTransactionList =
            attTransactionDao.findByAttDatetimeAndPersonPinAndMark(attDatetime, personPin, mark);
        if (!CollectionUtil.isEmpty(attTransactionList)) {
            attTransactionDao.delete(attTransactionList.get(0));
        }
    }

    @Override
    public List<AttPhotoExportQueryItem> getAllAttPhotoListByAuthUserFilter(String sessionId,
        AttPhotoExportQueryItem condition) {

        int pageSize = CollectionUtil.splitSize;
        Pager pager =
            attTransactionDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), 0, pageSize);
        long countTotal = pager.getTotal();
        // 数据量大时,需要查询多次
        long pageCount = countTotal / CollectionUtil.splitSize;
        if (countTotal % CollectionUtil.splitSize > 0) {
            pageCount++;
        }

        // 所有的考勤记录
        List<AttPhotoExportQueryItem> queryItemList = new ArrayList<>();
        List<AttPhotoExportQueryItem> pageList = (List<AttPhotoExportQueryItem>)pager.getData();
        if (pageList.size() > 0) {
            queryItemList.addAll(pageList);
        }
        // 分页查出考勤记录
        for (int page = 1; page < pageCount; page++) {
            pager =
                attTransactionDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, pageSize);
            pageList = (List<AttPhotoExportQueryItem>)pager.getData();
            if (pageList.size() > 0) {
                queryItemList.addAll(pageList);
            }
        }

        Set<String> photoPathSet = new HashSet<>();
        List<AttPhotoExportQueryItem> resultList = new ArrayList<>();
        for (AttPhotoExportQueryItem item : queryItemList) {
            String photoPath = AttCommonUtils.buildAttPhotoUrl(item.getDeviceSn(), item.getAttDate(),
                item.getAttDatetime(), item.getPersonPin());
            if (photoPathSet.contains(photoPath)) {
                // 记录文件去重
                continue;
            }
            photoPathSet.add(photoPath);
            // 压缩目录 设备号/日期/
            item.setCompressDirectory(item.getDeviceSn() + File.separator + item.getAttDate() + File.separator);
            item.setFilePath(photoPath);
            resultList.add(item);
        }

        return resultList;
    }

    @Override
    public Date getMaxCreateTimeByModule(String module) {
        return attTransactionDao.getMaxCreateTimeByMark(module);
    }

    @Override
    public Integer countAttTransactionByCreateTime(String devSn, Date startDateTime, Date endDateTime) {
        return attTransactionDao.countByDeviceSnAndCreateTimeBetween(devSn, startDateTime, endDateTime);
    }

    @Override
    public ZKResultMsg signIn(String clientId, String pin, JSONObject data) {

        String attPlace = data.getString("attPlace");
        String longitude = data.getString("longitude");
        String latitude = data.getString("latitude");
        String mapType = data.getString("mapType");
        String zoneId = data.getString("zoneId");
        if (StringUtils.isBlank(zoneId)) {
            return ZKResultMsg.failMsg("zoneId cannot be empty ");
        }

        // 验证参数和位置
        ZKResultMsg resultMsg = attSignAddressService.checkSignInRange(pin, longitude, latitude, mapType);
        if (!resultMsg.isSuccess()) {
            // 参数校验，错误直接返回
            return resultMsg;
        } else {
            // 校验是否设置打卡位置，校验是否在打卡位置内
            JSONObject ret = (JSONObject)resultMsg.getData();
            int status = ret.getIntValue("status");
            if (status == -1) {
                return ZKResultMsg.failMsg("att_app_noSignAddress");
            } else if (status == 1) {
                return ZKResultMsg.failMsg("att_app_notInSignAddress");
            }
        }

        PersPersonCacheItem personCacheItem = persPersonCacheService.getPersonCacheByPin(pin);
        // 人员不存在
        if (personCacheItem == null) {
            return ZKResultMsg.failMsg("pers_app_personNull");
        }

        // 该设备今天已被其他人签到
        if (StringUtils.isNotBlank(clientId)
            && !attCacheManager.checkPersonSignClient(clientId, personCacheItem.getId())) {
            return ZKResultMsg.failMsg("att_app_signClientTip");
        }


        // 创建一个 ZonedDateTime 对象
        ZonedDateTime zonedDateTime = ZonedDateTime.now(ZoneId.of(zoneId));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateStyle.YYYY_MM_DD_HH_MM_SS.getValue());
        String formattedDate = zonedDateTime.format(formatter);
        Date attDatetime = DateUtil.stringToDate(formattedDate, DateStyle.YYYY_MM_DD_HH_MM_SS.getValue());
        String attDate = DateUtil.dateToString(attDatetime, DateUtil.DateStyle.YYYY_MM_DD);
        String attTime = DateUtil.dateToString(attDatetime, DateUtil.DateStyle.HH_MM_SS);
        String attTimeStr = DateUtil.dateToString(attDatetime, DateUtil.DateStyle.HH_MM);

        if (attTransactionDao.countByPersonPinAndAttDatetime(pin, attDatetime) == 0) {
            AttTransactionItem attTran = new AttTransactionItem();
            attTran.setPersonPin(personCacheItem.getPin());
            attTran.setPersonName(personCacheItem.getName());
            attTran.setPersonLastName(personCacheItem.getLastName());
            attTran.setDeptId(personCacheItem.getDeptId());
            attTran.setDeptCode(personCacheItem.getDeptCode());
            attTran.setDeptName(personCacheItem.getDeptName());
            // 设置打卡时间
            attTran.setAttDatetime(attDatetime);

            attTran.setAttDate(attDate);
            attTran.setAttTime(attTime);
            if (StringUtils.isNotBlank(attPlace)) {
                if (attPlace.length() > 200) {
                    // 超过一定长度将后面字符串省略
                    attPlace = attPlace.substring(0, 196) + "...";
                }
                attTran.setAttPlace(attPlace);
            }
            attTran.setMark("app");
            saveTransactionItem(attTran);

            // 设置签到客户端和人员关系，防止同一个客户端可以多个人打卡
            if (StringUtils.isNotBlank(clientId)) {
                attCacheManager.setPersonSignClient(clientId, personCacheItem.getId());
            }
        }
        String attDatetimeStr = DateUtil.dateToString(attDatetime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS.getValue());
        JSONObject result = new JSONObject();
        result.put("attDatetime", attDatetimeStr);
        result.put("attDate", attDate);
        result.put("attTime", attTimeStr);
        return new ZKResultMsg(result);
    }

    @Override
    public Pager getSignInItemsByPage(AttTransactionItem condition, int pageNo, int pageSize) {
        condition.setNotMark("att-sign");
        StringBuffer sql = new StringBuffer(SQLUtil.getSqlByItem(condition));
        if (sql.indexOf("ORDER BY t.ATT_DATETIME") < 0) {
            sql.append(", t.ATT_DATETIME ASC");
        }
        Pager pager = attTransactionDao.getItemsBySql(condition.getClass(), sql.toString(), pageNo, pageSize);
        List<AttTransactionItem> list = (List<AttTransactionItem>)pager.getData();
        for (AttTransactionItem item : list) {
            /*if (StringUtils.isNotBlank(item.getMark())) {
                item.setMark(I18nUtil.i18nCode(String.format("att_statistical_%s",item.getMark())));
            }*/
        }
        return pager;
    }

    @Override
    @Transactional
    public Date pullThirdPartyRecord(String module, String ids, Date startDateTime, Date endDateTime) {
        Date maxAttDateTime = null;
        try {
            switch (module) {
                case ConstUtil.SYSTEM_MODULE_ACC:
                    maxAttDateTime = pullAccTransaction(ids, startDateTime, endDateTime);
                    break;
                case ConstUtil.SYSTEM_MODULE_PARK + ":in":
                    maxAttDateTime = pullParkTransaction("in", ids, startDateTime, endDateTime);
                    break;
                case ConstUtil.SYSTEM_MODULE_PARK + ":out":
                    maxAttDateTime = pullParkTransaction("out", ids, startDateTime, endDateTime);
                    break;
                case ConstUtil.SYSTEM_MODULE_INS:
                    maxAttDateTime = pullInsTransaction(ids, startDateTime, endDateTime);
                    break;
                case ConstUtil.SYSTEM_MODULE_IDENTIFICATION:
                    maxAttDateTime = pullPidTransaction(ids, startDateTime, endDateTime);
                    break;
                case ConstUtil.SYSTEM_MODULE_VMS:
                    maxAttDateTime = pullVmsTransaction(ids, startDateTime, endDateTime);
                    break;
                case ConstUtil.SYSTEM_MODULE_IVS:
                    maxAttDateTime = pullIvsTransaction(ids, startDateTime, endDateTime);
                    break;
                case ConstUtil.LICENSE_MODULE_ESDC:
                    maxAttDateTime = pullEsdcTransaction(ids, startDateTime, endDateTime);
                    break;
                case ConstUtil.SYSTEM_MODULE_PASSAGE:
                    maxAttDateTime = pullPsgTransaction(ids, startDateTime, endDateTime);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("Pull " + module + " record fail !", e);
        }
        return maxAttDateTime;
    }

    /**
     * 根据门id集合定时拉取门禁事件记录
     *
     * @param doorIds:
     * @param startCreateTime:
     * @param endCreateTime:
     * @return java.util.Date
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/16 11:28
     * @since 1.0.0
     */
    private Date pullAccTransaction(String doorIds, Date startCreateTime, Date endCreateTime) {

        if (attGetAccTransactionService == null) {
            return null;
        }

        // 获取门禁记录总数
        int accTransactionCount =
            attGetAccTransactionService.pullAccTransactionCount(doorIds, startCreateTime, endCreateTime);
        if (accTransactionCount <= 0) {
            return null;
        }

        // 考勤点、区域查询
        String crossDay = attParamService.getCrossDay();
        List<AttPointItem> attPointItems = attPointService.getByCondition(new AttPointItem().setInDeviceId(doorIds));
        Map<String, AttPointItem> attPointItemMap =
            CollectionUtil.listToKeyMap(attPointItems, AttPointItem::getDeviceId);
        Map<String, AuthAreaItem> attPointAuthAreaMap = getAttPointAuthAreaMap(attPointItems);

        // 拉取记录的最大时间
        Date maxAttDateTime = null;

        // 分页处理
        long count = accTransactionCount / CollectionUtil.splitSize;
        if (accTransactionCount % CollectionUtil.splitSize > 0) {
            count++;
        }
        int size = CollectionUtil.splitSize;
        for (int page = 0; page < count; page++) {

            // 分页查询门禁记录
            Pager pager =
                attGetAccTransactionService.pullAccTransactionList(doorIds, startCreateTime, endCreateTime, page, size);
            List<Att4AccTransaction> att4AccTransactionListAll = (List<Att4AccTransaction>)pager.getData();
            if (att4AccTransactionListAll == null || att4AccTransactionListAll.size() == 0) {
                continue;
            }

            // 根据记录事件的最大最小时间，查询已存在的记录
            Set<String> existAttTransaction = getAccExistAttTransaction(att4AccTransactionListAll, null);

            // 过滤同一个人同一分钟、数据库已经存在的门禁记录
            List<Att4AccTransaction> att4AccTransactionList =
                filterMinuteByAccTransaction(att4AccTransactionListAll, existAttTransaction);
            if (att4AccTransactionList == null || att4AccTransactionList.size() == 0) {
                continue;
            }

            // 查询人员信息
            List<String> personPinList =
                (List<String>)CollectionUtil.getPropertyList(att4AccTransactionList, Att4AccTransaction::getPin, "-1");
            Map<String, PersPersonCacheItem> pinPersonMap = getPinPersonMap(personPinList);
            if (pinPersonMap == null) {
                continue;
            }

            // 遍历组装数据并批量保存
            List<AttTransaction> attTransactionList = new ArrayList<>();
            for (Att4AccTransaction att4AccTransaction : att4AccTransactionList) {
                PersPersonCacheItem persPersonItem = pinPersonMap.get(att4AccTransaction.getPin());
                if (Objects.isNull(persPersonItem)) {
                    continue;
                }
                AuthAreaItem authAreaItem = attPointAuthAreaMap.get(att4AccTransaction.getEventPointId());
                AttPointItem attPointItem = attPointItemMap.get(att4AccTransaction.getEventPointId());
                AttTransaction attTransaction =
                    accToAttTransaction(att4AccTransaction, persPersonItem, authAreaItem, attPointItem);
                attTransaction.setPushWechatFlag("1");
                attTransactionList.add(attTransaction);
            }

            if (attTransactionList != null && attTransactionList.size() > 0) {
                // 更新记录的最大创建时间
                Date pageCreateTime = att4AccTransactionListAll.stream()
                    .max(Comparator.comparing(Att4AccTransaction::getCreateTime)).get().getCreateTime();
                if (Objects.isNull(maxAttDateTime)) {
                    maxAttDateTime = pageCreateTime;
                } else {
                    maxAttDateTime =
                        maxAttDateTime.getTime() < pageCreateTime.getTime() ? pageCreateTime : maxAttDateTime;
                }
                attTransactionDao.saveAll(attTransactionList);
                // 实时点名推送
                attRealTimePushService
                    .pushTransaction(ModelUtil.copyListProperties(attTransactionList, AttTransactionItem.class));
                // 【实时计算】新增考勤实时事件
                for (AttTransaction attTransaction : attTransactionList) {
                    attCalculationCacheManager.addRealTimeEvent(attTransaction.getPersonPin(),
                        attTransaction.getAttDatetime(), crossDay);
                }
            }
        }
        return maxAttDateTime;
    }

    /**
     * 停车场当考勤：根据进出口及通道id集合定时拉取停车记录
     *
     * @param status: in:入 out:出
     * @param channelIds:
     * @param startCreateTime:
     * @param endCreateTime:
     * @return java.util.Date
     */
    private Date pullParkTransaction(String status, String channelIds, Date startCreateTime, Date endCreateTime) {
        if (Objects.isNull(attGetParkTransactionService)) {
            return null;
        }

        String crossDay = attParamService.getCrossDay();
        Map<String, AttParkAreaItem> attPointAuthAreaMap = getParkPointAuthAreaMap(channelIds);

        Date maxAttDateTime = null;
        // 车场入口
        if ("in".equals(status)) {

            int parkInTransactionCount =
                attGetParkTransactionService.pullParkInTransactionCount(channelIds, startCreateTime, endCreateTime);
            if (parkInTransactionCount <= 0) {
                return null;
            }

            long count = parkInTransactionCount / CollectionUtil.splitSize;
            if (parkInTransactionCount % CollectionUtil.splitSize > 0) {
                count++;
            }
            int size = CollectionUtil.splitSize;
            for (int page = 0; page < count; page++) {

                Pager parkInTransactionList = attGetParkTransactionService.pullParkInTransactionList(channelIds,
                    startCreateTime, endCreateTime, page, size);
                List<Att4ParkTransaction> att4ParkTransactionListAll =
                    (List<Att4ParkTransaction>)parkInTransactionList.getData();
                if (att4ParkTransactionListAll == null || att4ParkTransactionListAll.size() == 0) {
                    continue;
                }

                // 保存手动拉取记录
                maxAttDateTime =
                    savePullParkTransaction(att4ParkTransactionListAll, attPointAuthAreaMap, crossDay, maxAttDateTime);
            }
        } else {
            int parkOutTransactionCount =
                attGetParkTransactionService.pullParkOutTransactionCount(channelIds, startCreateTime, endCreateTime);
            if (parkOutTransactionCount <= 0) {
                return null;
            }

            long count = parkOutTransactionCount / CollectionUtil.splitSize;
            if (parkOutTransactionCount % CollectionUtil.splitSize > 0) {
                count++;
            }
            int size = CollectionUtil.splitSize;
            for (int page = 0; page < count; page++) {

                Pager parkOutTransactionList = attGetParkTransactionService.pullParkOutTransactionList(channelIds,
                    startCreateTime, endCreateTime, page, size);
                List<Att4ParkTransaction> att4ParkTransactionListAll =
                    (List<Att4ParkTransaction>)parkOutTransactionList.getData();
                if (att4ParkTransactionListAll == null || att4ParkTransactionListAll.size() == 0) {
                    continue;
                }

                // 保存手动拉取记录
                maxAttDateTime =
                    savePullParkTransaction(att4ParkTransactionListAll, attPointAuthAreaMap, crossDay, maxAttDateTime);
            }
        }
        return maxAttDateTime;
    }

    /**
     * 停车场当考勤：保存手动拉取记录
     */
    private Date savePullParkTransaction(List<Att4ParkTransaction> att4ParkTransactionListAll,
        Map<String, AttParkAreaItem> attPointAuthAreaMap, String crossDay, Date maxAttDateTime) {

        // 根据记录事件的最大最小时间，查询已存在的记录
        Set<String> existAttTransaction = getParkExistTransaction(att4ParkTransactionListAll, null);

        // 过滤同一个人同一分钟、数据库已经存在的停车记录
        List<Att4ParkTransaction> att4ParkTransactionList =
            filterMinuteByParkTransaction(att4ParkTransactionListAll, existAttTransaction);
        if (att4ParkTransactionList == null || att4ParkTransactionList.size() == 0) {
            return maxAttDateTime;
        }

        // 获取人员信息
        List<String> personPinList = (List<String>)CollectionUtil.getPropertyList(att4ParkTransactionList,
            Att4ParkTransaction::getPersonPin, "-1");
        Map<String, PersPersonCacheItem> pinPersonMap = getPinPersonMap(personPinList);
        if (pinPersonMap == null) {
            return maxAttDateTime;
        }

        // 遍历组装数据并批量保存
        List<AttTransaction> attTransactionList = new ArrayList<>();
        for (Att4ParkTransaction att4ParkTransaction : att4ParkTransactionList) {
            PersPersonCacheItem persPersonItem = pinPersonMap.get(att4ParkTransaction.getPersonPin());
            if (Objects.isNull(persPersonItem)) {
                continue;
            }
            AttParkAreaItem attParkAreaItem = attPointAuthAreaMap.get(att4ParkTransaction.getDeviceId());
            AttTransaction attTransaction = parkToAttTransaction(att4ParkTransaction, persPersonItem, attParkAreaItem);
            attTransactionList.add(attTransaction);
        }
        if (attTransactionList != null && attTransactionList.size() > 0) {
            // 更新记录的最大创建时间
            Date pageCreateTime = att4ParkTransactionListAll.stream()
                .max(Comparator.comparing(Att4ParkTransaction::getCreateTime)).get().getCreateTime();
            if (Objects.isNull(maxAttDateTime)) {
                maxAttDateTime = pageCreateTime;
            } else {
                maxAttDateTime = maxAttDateTime.getTime() < pageCreateTime.getTime() ? pageCreateTime : maxAttDateTime;
            }
            attTransactionDao.saveAll(attTransactionList);
            // 实时点名推送
            attRealTimePushService
                .pushTransaction(ModelUtil.copyListProperties(attTransactionList, AttTransactionItem.class));
            // 【实时计算】新增考勤实时事件
            for (AttTransaction attTransaction : attTransactionList) {
                attCalculationCacheManager.addRealTimeEvent(attTransaction.getPersonPin(),
                    attTransaction.getAttDatetime(), crossDay);
            }
        }

        return maxAttDateTime;
    }

    /**
     * 通过设备ID集合定时拉取信息屏记录
     *
     * @param deviceIds:
     * @param startCreateTime:
     * @param endCreateTime:
     * @return java.util.Date
     */
    private Date pullInsTransaction(String deviceIds, Date startCreateTime, Date endCreateTime) {

        if (Objects.isNull(attGetInsTransactionService)) {
            return null;
        }

        int insTransactionCount =
            attGetInsTransactionService.pullInsTransactionCount(deviceIds, startCreateTime, endCreateTime);
        if (insTransactionCount <= 0) {
            return null;
        }

        String crossDay = attParamService.getCrossDay();
        List<AttPointItem> attPointItems = attPointService.getByCondition(new AttPointItem().setInDeviceId(deviceIds));
        Map<String, AuthAreaItem> attPointAuthAreaMap = getAttPointAuthAreaMap(attPointItems);

        // 根据size的带下，根据计算，获取需要查询几次
        long count = insTransactionCount / CollectionUtil.splitSize;
        if (insTransactionCount % CollectionUtil.splitSize > 0) {
            count++;
        }
        int size = CollectionUtil.splitSize;
        Date maxAttDateTime = null;
        for (int page = 0; page < count; page++) {

            Pager insTransactionPage = attGetInsTransactionService.pullInsTransactionList(deviceIds, startCreateTime,
                endCreateTime, page, size);
            List<Att4InsTransaction> insTransactionListAll = (List<Att4InsTransaction>)insTransactionPage.getData();
            if (insTransactionListAll == null || insTransactionListAll.size() == 0) {
                continue;
            }

            // 根据记录事件的最大最小时间，查询已存在的记录
            Set<String> existAttTransaction = getInsExistAttTransaction(insTransactionListAll, null);

            // 过滤同一个人同一分钟、数据库已经存在的信息屏记录
            List<Att4InsTransaction> insTransactionList =
                filterMinuteByInsTransaction(insTransactionListAll, existAttTransaction);
            if (insTransactionList == null || insTransactionList.size() == 0) {
                continue;
            }

            // 获取人员信息
            List<String> personPinList = (List<String>)CollectionUtil.getPropertyList(insTransactionList,
                Att4InsTransaction::getPersonPin, "-1");
            Map<String, PersPersonCacheItem> pinPersonMap = getPinPersonMap(personPinList);
            if (pinPersonMap == null) {
                continue;
            }

            // 遍历组装数据并批量保存
            List<AttTransaction> attTransactionList = new ArrayList<>();
            for (Att4InsTransaction att4InsTransaction : insTransactionList) {
                PersPersonCacheItem persPersonItem = pinPersonMap.get(att4InsTransaction.getPersonPin());
                if (Objects.isNull(persPersonItem)) {
                    continue;
                }
                AuthAreaItem authAreaItem = attPointAuthAreaMap.get(att4InsTransaction.getDeviceId());
                AttTransaction attTransaction = insToAttTransaction(att4InsTransaction, persPersonItem, authAreaItem);
                attTransactionList.add(attTransaction);
            }
            if (attTransactionList != null && attTransactionList.size() > 0) {
                // 更新记录的最大创建时间
                Date pageCreateTime = insTransactionListAll.stream()
                    .max(Comparator.comparing(Att4InsTransaction::getCreateTime)).get().getCreateTime();
                if (Objects.isNull(maxAttDateTime)) {
                    maxAttDateTime = pageCreateTime;
                } else {
                    maxAttDateTime =
                        maxAttDateTime.getTime() < pageCreateTime.getTime() ? pageCreateTime : maxAttDateTime;
                }
                attTransactionDao.saveAll(attTransactionList);
                // 实时点名推送
                attRealTimePushService
                    .pushTransaction(ModelUtil.copyListProperties(attTransactionList, AttTransactionItem.class));
                // 【实时计算】新增考勤实时事件
                for (AttTransaction attTransaction : attTransactionList) {
                    attCalculationCacheManager.addRealTimeEvent(attTransaction.getPersonPin(),
                        attTransaction.getAttDatetime(), crossDay);
                }
            }
        }
        return maxAttDateTime;
    }

    /**
     * 根据设备ID集合定时拉取人证记录
     *
     * @param deviceIds:
     * @param startCreateTime:
     * @param endCreateTime:
     * @return java.util.Date
     */
    private Date pullPidTransaction(String deviceIds, Date startCreateTime, Date endCreateTime) {

        if (Objects.isNull(attGetPidTransactionService)) {
            return null;
        }

        int pidTransactionCount =
            attGetPidTransactionService.pullPidTransactionCount(deviceIds, startCreateTime, endCreateTime);
        if (pidTransactionCount <= 0) {
            return null;
        }

        String crossDay = attParamService.getCrossDay();
        List<AttPointItem> attPointItems = attPointService.getByCondition(new AttPointItem().setInDeviceId(deviceIds));
        Map<String, AuthAreaItem> attPointAuthAreaMap = getAttPointAuthAreaMap(attPointItems);

        Date maxAttDateTime = null;
        // 根据size的带下，根据计算，获取需要查询几次
        long count = pidTransactionCount / CollectionUtil.splitSize;
        if (pidTransactionCount % CollectionUtil.splitSize > 0) {
            count++;
        }
        int size = CollectionUtil.splitSize;
        for (int page = 0; page < count; page++) {

            Pager pidTransactionPage = attGetPidTransactionService.pullPidTransactionList(deviceIds, startCreateTime,
                endCreateTime, page, size);
            List<Att4PidTransaction> pidTransactionListAll = (List<Att4PidTransaction>)pidTransactionPage.getData();
            if (pidTransactionListAll == null || pidTransactionListAll.size() == 0) {
                continue;
            }

            // 根据记录事件的最大最小时间，查询已存在的记录
            Set<String> existAttTransaction = getPidExistAttTransaction(pidTransactionListAll, null);

            // 过滤同一个人同一分钟、数据库已经存在的信息屏记录
            List<Att4PidTransaction> pidTransactionList =
                filterMinuteByPidTransaction(pidTransactionListAll, existAttTransaction);
            if (pidTransactionList == null || pidTransactionList.size() == 0) {
                continue;
            }

            // 获取人员信息
            List<String> personPinList = (List<String>)CollectionUtil.getPropertyList(pidTransactionList,
                Att4PidTransaction::getPersonPin, "-1");
            Map<String, PersPersonCacheItem> pinPersonMap = getPinPersonMap(personPinList);
            if (pinPersonMap == null) {
                continue;
            }

            // 遍历组装数据并批量保存
            List<AttTransaction> attTransactionList = new ArrayList<>();
            for (Att4PidTransaction att4PidTransaction : pidTransactionList) {

                PersPersonCacheItem persPersonItem = pinPersonMap.get(att4PidTransaction.getPersonPin());
                if (Objects.isNull(persPersonItem)) {
                    continue;
                }
                AuthAreaItem authAreaItem = attPointAuthAreaMap.get(att4PidTransaction.getDeviceId());
                AttTransaction attTransaction = pidToAttTransaction(att4PidTransaction, persPersonItem, authAreaItem);
                attTransactionList.add(attTransaction);
            }
            if (attTransactionList != null && attTransactionList.size() > 0) {
                // 更新记录的最大创建时间
                Date pageCreateTime = pidTransactionListAll.stream()
                    .max(Comparator.comparing(Att4PidTransaction::getCreateTime)).get().getCreateTime();
                if (Objects.isNull(maxAttDateTime)) {
                    maxAttDateTime = pageCreateTime;
                } else {
                    maxAttDateTime =
                        maxAttDateTime.getTime() < pageCreateTime.getTime() ? pageCreateTime : maxAttDateTime;
                }
                attTransactionDao.saveAll(attTransactionList);
                // 实时点名推送
                attRealTimePushService
                    .pushTransaction(ModelUtil.copyListProperties(attTransactionList, AttTransactionItem.class));
                // 【实时计算】新增考勤实时事件
                for (AttTransaction attTransaction : attTransactionList) {
                    attCalculationCacheManager.addRealTimeEvent(attTransaction.getPersonPin(),
                        attTransaction.getAttDatetime(), crossDay);
                }
            }
        }
        return maxAttDateTime;
    }

    /**
     * 根据通道id集合定时拉取VMS视频事件记录
     *
     * @param channelIds:
     * @param startCreateTime:
     * @param endCreateTime:
     * @return java.util.Date
     */
    private Date pullVmsTransaction(String channelIds, Date startCreateTime, Date endCreateTime) {

        if (Objects.isNull(attGetVmsTransactionService)) {
            return null;
        }

        // 按创建时间查询新增的记录
        int vmsTransactionCount =
            attGetVmsTransactionService.pullVmsTransactionCount(channelIds, startCreateTime, endCreateTime);
        if (vmsTransactionCount <= 0) {
            return null;
        }

        String crossDay = attParamService.getCrossDay();
        List<AttPointItem> attPointItems = attPointService.getByCondition(new AttPointItem().setInDeviceId(channelIds));
        Map<String, AttPointItem> attPointItemMap =
            CollectionUtil.listToKeyMap(attPointItems, item -> item.getDeviceSn() + "_" + item.getDoorNo());
        Map<String, AuthAreaItem> attPointAuthAreaMap = getAttPointAuthAreaMap(attPointItems);

        Date maxAttDateTime = null;
        // 根据size的带下，根据计算，获取需要查询几次
        long count = vmsTransactionCount / CollectionUtil.splitSize;
        if (vmsTransactionCount % CollectionUtil.splitSize > 0) {
            count++;
        }
        int size = CollectionUtil.splitSize;
        for (int page = 0; page < count; page++) {

            Pager vmsTransactionPage = attGetVmsTransactionService.pullVmsTransactionList(channelIds, startCreateTime,
                endCreateTime, page, size);
            List<Att4OtherTransactionItem> vmsTransactionListAll =
                (List<Att4OtherTransactionItem>)vmsTransactionPage.getData();
            if (vmsTransactionListAll == null || vmsTransactionListAll.size() == 0) {
                continue;
            }

            // 根据记录事件的最大最小时间，查询已存在的记录
            Set<String> existAttTransaction = getVmsExistAttTransaction(vmsTransactionListAll, null);

            // 过滤考勤原始记录，同一分钟只保留一条记录
            List<Att4OtherTransactionItem> vmsTransactionList =
                filterMinute(vmsTransactionListAll, existAttTransaction);
            if (vmsTransactionList == null || vmsTransactionList.size() == 0) {
                continue;
            }

            // 获取人员信息
            List<String> personPinList = (List<String>)CollectionUtil.getPropertyList(vmsTransactionList,
                Att4OtherTransactionItem::getPersonPin, "-1");
            Map<String, PersPersonCacheItem> pinPersonMap = getPinPersonMap(personPinList);
            if (pinPersonMap == null) {
                continue;
            }

            List<AttTransaction> attTransactionList = new ArrayList<>();
            for (Att4OtherTransactionItem vmsTransaction : vmsTransactionList) {
                PersPersonCacheItem persPersonItem = pinPersonMap.get(vmsTransaction.getPersonPin());
                if (Objects.isNull(persPersonItem)) {
                    continue;
                }
                AttPointItem attPointItem =
                    attPointItemMap.get(vmsTransaction.getDeviceSn() + "_" + vmsTransaction.getChannelNo());
                if (Objects.isNull(persPersonItem)) {
                    continue;
                }
                AuthAreaItem authAreaItem = attPointAuthAreaMap.get(attPointItem.getDeviceId());
                AttTransaction attTransaction =
                    vmsToAttTransaction(vmsTransaction, persPersonItem, authAreaItem, attPointItem);
                attTransactionList.add(attTransaction);
            }
            if (attTransactionList != null && attTransactionList.size() > 0) {
                // 更新记录的最大创建时间
                Date pageCreateTime = vmsTransactionListAll.stream()
                    .max(Comparator.comparing(Att4OtherTransactionItem::getCreateTime)).get().getCreateTime();
                if (Objects.isNull(maxAttDateTime)) {
                    maxAttDateTime = pageCreateTime;
                } else {
                    maxAttDateTime =
                        maxAttDateTime.getTime() < pageCreateTime.getTime() ? pageCreateTime : maxAttDateTime;
                }
                attTransactionDao.saveAll(attTransactionList);
                // 实时点名推送
                attRealTimePushService
                    .pushTransaction(ModelUtil.copyListProperties(attTransactionList, AttTransactionItem.class));
                // 【实时计算】新增考勤实时事件
                for (AttTransaction attTransaction : attTransactionList) {
                    attCalculationCacheManager.addRealTimeEvent(attTransaction.getPersonPin(),
                        attTransaction.getAttDatetime(), crossDay);
                }
            }
        }
        return maxAttDateTime;
    }

    /**
     * 根据通道id集合定时拉取智能视频事件记录
     *
     * @param channelIds:
     * @param startDateTime:
     * @param endDateTime:
     * @return java.util.Date
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/24 11:44
     * @since 1.0.0
     */
    private Date pullIvsTransaction(String channelIds, Date startDateTime, Date endDateTime) {

        if (Objects.isNull(attGetIvsDeviceService)) {
            return null;
        }

        // 获取ivs模块的数据的总条数
        if (StringUtils.isBlank(channelIds)) {
            return null;
        }

        int ivsTransactionCount = attGetIvsDeviceService.pullCountByChannelIds(channelIds, startDateTime, endDateTime);
        if (ivsTransactionCount <= 0) {
            return null;
        }

        String crossDay = attParamService.getCrossDay();
        List<AttPointItem> attPointItems = attPointService.getByCondition(new AttPointItem().setInDeviceId(channelIds));
        Map<String, AuthAreaItem> attPointAuthAreaMap = getAttPointAuthAreaMap(attPointItems);

        // 最新事件时间
        Date maxAttDateTime = null;

        // 根据size的大小分组，根据计算，获取需要查询几次
        long count = ivsTransactionCount / CollectionUtil.splitSize;
        if (ivsTransactionCount % CollectionUtil.splitSize > 0) {
            count++;
        }
        int size = CollectionUtil.splitSize;
        for (int page = 0; page < count; page++) {

            Pager ivsTransactionPage =
                attGetIvsDeviceService.pullFaceInfoPagerByDevIds(channelIds, startDateTime, endDateTime, page, size);
            List<Att4OtherTransactionItem> ivsTransactionListAll =
                (List<Att4OtherTransactionItem>)ivsTransactionPage.getData();
            if (ivsTransactionListAll == null || ivsTransactionListAll.size() == 0) {
                continue;
            }

            // 根据记录事件的最大最小时间，查询已存在的记录
            Set<String> existAttTransaction =
                getExistAttTransaction(ivsTransactionListAll, null, ConstUtil.SYSTEM_MODULE_IVS);

            // 过滤处理，同一个人一个分钟只保存一条记录
            List<Att4OtherTransactionItem> ivsTransactionList =
                filterMinute(ivsTransactionListAll, existAttTransaction);
            if (ivsTransactionList == null || ivsTransactionList.size() == 0) {
                continue;
            }

            // 获取人员信息
            List<String> personPinList = (List<String>)CollectionUtil.getPropertyList(ivsTransactionList,
                Att4OtherTransactionItem::getPersonPin, "-1");
            Map<String, PersPersonCacheItem> pinPersonMap = getPinPersonMap(personPinList);
            if (pinPersonMap == null) {
                continue;
            }

            List<AttTransaction> attTransactionList = new ArrayList<>();
            for (Att4OtherTransactionItem ivsTransaction : ivsTransactionList) {
                PersPersonCacheItem persPersonItem = pinPersonMap.get(ivsTransaction.getPersonPin());
                if (Objects.isNull(persPersonItem)) {
                    continue;
                }
                AuthAreaItem authAreaItem = attPointAuthAreaMap.get(ivsTransaction.getDeviceId());
                AttTransaction attTransaction = att4OtherTransactionItemToAttTransaction(ivsTransaction, persPersonItem,
                    authAreaItem, ConstUtil.SYSTEM_MODULE_IVS);
                attTransactionList.add(attTransaction);
                // 推送给微信公众号消息
                try {
                    sendTransactionWxMessage(attTransaction);
                } catch (Exception e) {
                    log.error("sendTransactionWxMessage Exception {}", e);
                }
            }
            if (attTransactionList != null && attTransactionList.size() > 0) {
                // 更新记录的最大创建时间
                Date pageCreateTime = ivsTransactionListAll.stream()
                    .max(Comparator.comparing(Att4OtherTransactionItem::getCreateTime)).get().getCreateTime();
                if (Objects.isNull(maxAttDateTime)) {
                    maxAttDateTime = pageCreateTime;
                } else {
                    maxAttDateTime =
                        maxAttDateTime.getTime() < pageCreateTime.getTime() ? pageCreateTime : maxAttDateTime;
                }
                attTransactionDao.saveAll(attTransactionList);
                // 实时点名推送
                attRealTimePushService
                    .pushTransaction(ModelUtil.copyListProperties(attTransactionList, AttTransactionItem.class));
                // 【实时计算】新增考勤实时事件
                for (AttTransaction attTransaction : attTransactionList) {
                    attCalculationCacheManager.addRealTimeEvent(attTransaction.getPersonPin(),
                        attTransaction.getAttDatetime(), crossDay);
                }
            }
        }
        return maxAttDateTime;
    }

    /**
     * 根据通道id集合定时拉取智能场景事件记录
     *
     * @param channelIds:
     * @param startDateTime:
     * @param endDateTime:
     * @return java.util.Date
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/11/24 11:44
     * @since 1.0.0
     */
    private Date pullEsdcTransaction(String channelIds, Date startDateTime, Date endDateTime) {

        if (Objects.isNull(attGetEsdcDataService)) {
            return null;
        }

        // 获取ivs模块的数据的总条数
        if (StringUtils.isBlank(channelIds)) {
            return null;
        }

        log.info(
            "attGetEsdcDataService.getCountByCreateTimeAndChannelIds start channelIds = {}, startDateTime = {}, endDateTime = {}",
            channelIds, startDateTime, endDateTime);
        int transactionCount =
            attGetEsdcDataService.getCountByCreateTimeAndChannelIds(channelIds, startDateTime, endDateTime);
        log.info("attGetEsdcDataService.getCountByCreateTimeAndChannelIds end transactionCount = {}", transactionCount);
        if (transactionCount <= 0) {
            return null;
        }

        String crossDay = attParamService.getCrossDay();
        List<AttPointItem> attPointItems = attPointService.getByCondition(new AttPointItem().setInDeviceId(channelIds));
        Map<String, AuthAreaItem> attPointAuthAreaMap = getAttPointAuthAreaMap(attPointItems);

        // 最新事件时间
        Date maxAttDateTime = null;

        // 根据size的大小分组，根据计算，获取需要查询几次
        long count = transactionCount / CollectionUtil.splitSize;
        if (transactionCount % CollectionUtil.splitSize > 0) {
            count++;
        }
        int size = CollectionUtil.splitSize;
        for (int page = 0; page < count; page++) {

            log.info(
                "attGetEsdcDataService.getTransactionByChannelIdsAndCreateTime start channelIds = {}, startDatetime = {}, endDatetime = {}, pageNo = {}, pageSize = {}",
                channelIds, startDateTime, endDateTime, page, size);
            Pager pager = attGetEsdcDataService.getTransactionByChannelIdsAndCreateTime(channelIds, startDateTime,
                endDateTime, page, size);
            log.info("attGetEsdcDataService.getTransactionByChannelIdsAndCreateTime end pager = {}",
                JSON.toJSONString(pager));
            List<Att4OtherTransactionItem> att4OtherTransactionItemList =
                (List<Att4OtherTransactionItem>)pager.getData();
            if (att4OtherTransactionItemList == null || att4OtherTransactionItemList.size() == 0) {
                continue;
            }

            // 根据记录事件的最大最小时间，查询已存在的记录
            Set<String> existAttTransaction =
                getExistAttTransaction(att4OtherTransactionItemList, null, ConstUtil.LICENSE_MODULE_ESDC);

            // 过滤处理，同一个人一个分钟只保存一条记录
            List<Att4OtherTransactionItem> transactionList =
                filterMinute(att4OtherTransactionItemList, existAttTransaction);
            if (transactionList == null || transactionList.size() == 0) {
                continue;
            }

            // 获取人员信息
            List<String> personPinList = (List<String>)CollectionUtil.getPropertyList(transactionList,
                Att4OtherTransactionItem::getPersonPin, "-1");
            Map<String, PersPersonCacheItem> pinPersonMap = getPinPersonMap(personPinList);
            if (pinPersonMap == null) {
                continue;
            }

            List<AttTransaction> attTransactionList = new ArrayList<>();
            for (Att4OtherTransactionItem att4OtherTransactionItem : transactionList) {
                PersPersonCacheItem persPersonItem = pinPersonMap.get(att4OtherTransactionItem.getPersonPin());
                if (Objects.isNull(persPersonItem)) {
                    continue;
                }
                AuthAreaItem authAreaItem = attPointAuthAreaMap.get(att4OtherTransactionItem.getDeviceId());
                AttTransaction attTransaction = att4OtherTransactionItemToAttTransaction(att4OtherTransactionItem,
                    persPersonItem, authAreaItem, ConstUtil.LICENSE_MODULE_ESDC);
                attTransactionList.add(attTransaction);
                // 推送给微信公众号消息
                try {
                    sendTransactionWxMessage(attTransaction);
                } catch (Exception e) {
                    log.error("sendTransactionWxMessage Exception {}", e);
                }
            }
            if (attTransactionList != null && attTransactionList.size() > 0) {
                // 更新记录的最大创建时间
                Date pageCreateTime = att4OtherTransactionItemList.stream()
                    .max(Comparator.comparing(Att4OtherTransactionItem::getCreateTime)).get().getCreateTime();
                if (Objects.isNull(maxAttDateTime)) {
                    maxAttDateTime = pageCreateTime;
                } else {
                    maxAttDateTime =
                        maxAttDateTime.getTime() < pageCreateTime.getTime() ? pageCreateTime : maxAttDateTime;
                }
                attTransactionDao.saveAll(attTransactionList);
                // 实时点名推送
                attRealTimePushService
                    .pushTransaction(ModelUtil.copyListProperties(attTransactionList, AttTransactionItem.class));
                // 【实时计算】新增考勤实时事件
                for (AttTransaction attTransaction : attTransactionList) {
                    attCalculationCacheManager.addRealTimeEvent(attTransaction.getPersonPin(),
                        attTransaction.getAttDatetime(), crossDay);
                }
            }
        }
        return maxAttDateTime;
    }

    /**
     * 根据闸ID集合定时拉取Psg事件记录
     * 
     * <AUTHOR>
     * @date 2021-08-24 10:40
     * @param
     * @return
     */
    private Date pullPsgTransaction(String gateIds, Date startDateTime, Date endDateTime) {

        if (Objects.isNull(attGetPsgDeviceService)) {
            return null;
        }

        if (StringUtils.isBlank(gateIds)) {
            return null;
        }

        // 获取通道事件记录
        int psgTransactionCount = attGetPsgDeviceService.pullCountByGateIds(gateIds, startDateTime, endDateTime);
        if (psgTransactionCount <= 0) {
            return null;
        }

        String crossDay = attParamService.getCrossDay();
        List<AttPointItem> attPointItems = attPointService.getByCondition(new AttPointItem().setInDeviceId(gateIds));
        Map<String, AttPointItem> attPointItemMap =
            CollectionUtil.listToKeyMap(attPointItems, AttPointItem::getDeviceId);
        Map<String, AuthAreaItem> attPointAuthAreaMap = getAttPointAuthAreaMap(attPointItems);

        // 时间最大创建时间
        Date maxAttDateTime = null;
        // 根据size的大小分组，根据计算，获取需要查询几次
        long count = psgTransactionCount / CollectionUtil.splitSize;
        if (psgTransactionCount % CollectionUtil.splitSize > 0) {
            count++;
        }
        int size = CollectionUtil.splitSize;
        for (int page = 0; page < count; page++) {

            Pager psgTransactionPage =
                attGetPsgDeviceService.pullPsgTransactionList(gateIds, startDateTime, endDateTime, page, size);
            List<Att4PsgTransaction> psgTransactionListAll = (List<Att4PsgTransaction>)psgTransactionPage.getData();
            if (psgTransactionListAll == null || psgTransactionListAll.size() == 0) {
                continue;
            }

            // 查找存在的记录
            Set<String> existAttTransaction = getPsgExistAttTransaction(psgTransactionListAll, null);

            // 获取人员信息
            List<String> personPinList =
                (List<String>)CollectionUtil.getPropertyList(psgTransactionListAll, Att4PsgTransaction::getPin, "-1");
            Map<String, PersPersonCacheItem> pinPersonMap = getPinPersonMap(personPinList);
            if (pinPersonMap == null) {
                continue;
            }

            List<AttTransaction> attTransactionList = new ArrayList<>();
            for (Att4PsgTransaction psgTransaction : psgTransactionListAll) {
                PersPersonCacheItem persPersonItem = pinPersonMap.get(psgTransaction.getPin());
                if (Objects.isNull(persPersonItem)) {
                    continue;
                }
                AttPointItem attPointItem = attPointItemMap.get(psgTransaction.getEventPointId());
                if (Objects.isNull(attPointItem)) {
                    continue;
                }
                // 判断记录是否是考勤记录
                if (!((AttConstant.ATT_NORMAL_PASS_RECORD.equals(attPointItem.getRecordType())
                    && AttConstant.ATT_NORMAL_PASS_NO.equals(psgTransaction.getEventNo()))
                    || (AttConstant.ATT_VERIFICATION_RECORD.equals(attPointItem.getRecordType())
                        && AttConstant.ATT_VERIFICATION_PASS_NO.equals(psgTransaction.getEventNo())))) {
                    continue;
                }

                // 过滤同一个人同一分钟、数据库已经存在的通道记录
                String pingAndMinute = getPingAndMinute(psgTransaction.getPin(), psgTransaction.getEventTime());
                if (existAttTransaction.contains(pingAndMinute)) {
                    continue;
                }
                existAttTransaction.add(pingAndMinute);

                AuthAreaItem authAreaItem = attPointAuthAreaMap.get(psgTransaction.getEventPointId());
                AttTransaction attTransaction =
                    psgToAttTransaction(psgTransaction, persPersonItem, authAreaItem, attPointItem);
                attTransactionList.add(attTransaction);
            }

            if (attTransactionList != null && attTransactionList.size() > 0) {
                // 更新记录的最大创建时间
                Date pageCreateTime = psgTransactionListAll.stream()
                    .max(Comparator.comparing(Att4PsgTransaction::getCreateTime)).get().getCreateTime();
                if (Objects.isNull(maxAttDateTime)) {
                    maxAttDateTime = pageCreateTime;
                } else {
                    maxAttDateTime =
                        maxAttDateTime.getTime() < pageCreateTime.getTime() ? pageCreateTime : maxAttDateTime;
                }
                attTransactionDao.save(attTransactionList);
                // 实时点名推送
                attRealTimePushService
                    .pushTransaction(ModelUtil.copyListProperties(attTransactionList, AttTransactionItem.class));
                // 【实时计算】新增考勤实时事件
                for (AttTransaction attTransaction : attTransactionList) {
                    attCalculationCacheManager.addRealTimeEvent(attTransaction.getPersonPin(),
                        attTransaction.getAttDatetime(), crossDay);
                }
            }
        }
        return maxAttDateTime;
    }

    @Override
    @Transactional
    public ZKResultMsg syncAttRecord(String attPointIds, Date startDatetime, Date endDatetime) {
        List<AttPoint> attPointList = attPointDao.findByIdList(StrUtil.strToList(attPointIds));
        if (attPointList == null || attPointList.size() == 0) {
            log.error("SyncAttRecord attPointList is null");
            return ZKResultMsg.failMsg();
        }

        List<AttPointItem> attPointItemList = ModelUtil.copyListProperties(attPointList, AttPointItem.class);
        for (AttPointItem attPoint : attPointItemList) {
            // 获取各模块考勤点记录数，无新记录则跳过
            int otherModuleRecordCount = getTransactionCount(attPoint, startDatetime, endDatetime);
            if (otherModuleRecordCount <= 0) {
                continue;
            }

            // 获取考勤点记录标记判断
            String attPointKey = attPointService.getAttPointKey(attPoint);
            if (StringUtils.isNotBlank(attCacheManager.getPullTransactionFlag(attPointKey))) {
                // 若正在定时获取则返回
                return ZKResultMsg.failMsg("att_attPoint_pullingRecord");
            }

            try {
                // 标记正在拉取记录
                attCacheManager.setPullTransactionFlag(attPointKey);

                // 设备所在区域，停车模块特殊处理
                AuthAreaItem authAreaItem = authAreaService.getItemById(attPoint.getAreaId());
                AttParkAreaItem attParkAreaItem = null;
                if (ConstUtil.SYSTEM_MODULE_PARK.equals(attPoint.getDeviceModule())) {
                    attParkAreaItem = attParkDeviceService.getParkAreaByAreaId(attPoint.getAreaId());
                }

                long count = otherModuleRecordCount / CollectionUtil.splitSize;
                if (otherModuleRecordCount % CollectionUtil.splitSize > 0) {
                    count++;
                }

                // 保存人员和时间，判断重复
                for (int page = 0; page < count; page++) {
                    switch (attPoint.getDeviceModule()) {
                        case ConstUtil.SYSTEM_MODULE_IDENTIFICATION:
                            // 人证(手动同步考勤点记录，不需要更新缓存的时间点)
                            getPidRecord(attPoint, startDatetime, endDatetime, authAreaItem, page);
                            break;
                        case ConstUtil.SYSTEM_MODULE_INS:
                            // 信息屏(手动同步考勤点记录，不需要更新缓存的时间点)
                            getInsRecord(attPoint, startDatetime, endDatetime, authAreaItem, page);
                            break;
                        case ConstUtil.SYSTEM_MODULE_ACC:
                            // 门禁(手动同步考勤点记录，不需要更新缓存的时间点)
                            getAccRecord(attPoint, startDatetime, endDatetime, authAreaItem, page);
                            break;
                        case ConstUtil.SYSTEM_MODULE_PARK:
                            // 停车(手动同步考勤点记录，不需要更新缓存的时间点)
                            if (attPoint.getStatus() == 1 || attPoint.getStatus() == 3) {// 车场入口
                                getParkInRecord(attPoint, startDatetime, endDatetime, attParkAreaItem, page);
                            } else {
                                getParkOutRecord(attPoint, startDatetime, endDatetime, attParkAreaItem, page);
                            }
                            break;
                        case ConstUtil.SYSTEM_MODULE_VMS:
                            // 视频(手动同步考勤点记录，不需要更新缓存的时间点)
                            getVmsRecord(attPoint, startDatetime, endDatetime, authAreaItem, page);
                            break;
                        case ConstUtil.SYSTEM_MODULE_IVS:
                            // 视频(手动同步考勤点记录，不需要更新缓存的时间点)
                            getIvsRecord(attPoint, startDatetime, endDatetime, authAreaItem, page);
                            break;
                        case ConstUtil.LICENSE_MODULE_ESDC:
                            // 视频(手动同步考勤点记录，不需要更新缓存的时间点)
                            getEsdcRecord(attPoint, startDatetime, endDatetime, authAreaItem, page);
                            break;
                        case ConstUtil.SYSTEM_MODULE_PASSAGE:
                            // 通道(手动同步考勤点记录，不需要更新缓存的时间点)
                            getPsgRecord(attPoint, startDatetime, endDatetime, authAreaItem, page);
                            break;
                        default:
                            break;
                    }
                }
            } catch (Exception e) {
                log.error("SyncAttRecord Exception", e);
                return ZKResultMsg.failMsg();
            } finally {
                // 删除正在拉取标记
                attCacheManager.delPullTransactionFlag(attPointKey);
            }
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 根据时间查找各模块考勤点记录数
     *
     * @return int
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/10/21 16:29
     */
    private int getTransactionCount(AttPointItem attPoint, Date startDatetime, Date endDatetime) {
        int tranCount = 0;
        String module = attPoint.getDeviceModule();
        switch (module) {
            case ConstUtil.SYSTEM_MODULE_IDENTIFICATION:
                if (Objects.nonNull(attGetPidTransactionService)) {
                    // 人证
                    tranCount = attGetPidTransactionService.getPidTransactionCount(attPoint.getDeviceId(),
                        startDatetime, endDatetime);
                }
                break;
            case ConstUtil.SYSTEM_MODULE_INS:
                if (Objects.nonNull(attGetInsTransactionService)) {
                    // 信息屏
                    tranCount = attGetInsTransactionService.getInsTransactionCount(attPoint.getDeviceId(),
                        startDatetime, endDatetime);
                }
                break;
            case ConstUtil.SYSTEM_MODULE_ACC:
                if (Objects.nonNull(attGetAccTransactionService)) {
                    // 门禁
                    tranCount = attGetAccTransactionService.getAccTransactionCount(attPoint.getDeviceId(),
                        startDatetime, endDatetime);
                }
                break;
            case ConstUtil.SYSTEM_MODULE_PARK:
                if (Objects.nonNull(attGetParkTransactionService)) {
                    // 停车
                    if (attPoint.getStatus() == 1 || attPoint.getStatus() == 3) {// 车场入口
                        tranCount = attGetParkTransactionService.getParkInTransactionCount(attPoint.getDeviceId(),
                            startDatetime, endDatetime);
                    } else {
                        tranCount = attGetParkTransactionService.getParkOutTransactionCount(attPoint.getDeviceId(),
                            startDatetime, endDatetime);
                    }
                }
                break;
            case ConstUtil.SYSTEM_MODULE_VMS:
                if (Objects.nonNull(attGetVmsTransactionService)) {
                    // 视频
                    tranCount = attGetVmsTransactionService.getVmsTransactionCount(attPoint.getDeviceId(),
                        startDatetime, endDatetime);
                }
                break;
            case ConstUtil.SYSTEM_MODULE_IVS:
                if (Objects.nonNull(attGetIvsDeviceService)) {
                    // 智能视频
                    tranCount =
                        attGetIvsDeviceService.getCountByChannelIds(attPoint.getDeviceId(), startDatetime, endDatetime);
                }
                break;
            case ConstUtil.LICENSE_MODULE_ESDC:
                if (Objects.nonNull(attGetEsdcDataService)) {
                    log.info(
                        "attGetEsdcDataService.getCountByChannelIds start ChannelIds = {}, startDatetime = {}, endDatetime = {}",
                        attPoint.getDeviceId(), startDatetime, endDatetime);
                    // 智能场景
                    tranCount =
                        attGetEsdcDataService.getCountByChannelIds(attPoint.getDeviceId(), startDatetime, endDatetime);
                    log.info("attGetEsdcDataService.getChannelItemLis end tranCount = {}", tranCount);
                }
                break;
            case ConstUtil.SYSTEM_MODULE_PASSAGE:
                if (Objects.nonNull(attGetPsgDeviceService)) {
                    // 通道
                    tranCount = attGetPsgDeviceService.getPsgTransactionCount(attPoint.getDeviceId(), startDatetime,
                        endDatetime);
                }
                break;
            default:
                break;
        }
        return tranCount;
    }

    /**
     * 获取停车(进场)考勤点记录
     */
    private void getParkInRecord(AttPointItem attPoint, Date startDatetime, Date endDatetime,
        AttParkAreaItem attParkAreaItem, int page) {
        Pager parkInTransactionList = attGetParkTransactionService.getParkInTransactionList(attPoint.getDeviceId(),
            startDatetime, endDatetime, page, CollectionUtil.splitSize);
        List<Att4ParkTransaction> att4ParkTransactionListAll =
            (List<Att4ParkTransaction>)parkInTransactionList.getData();
        if (att4ParkTransactionListAll == null || att4ParkTransactionListAll.size() == 0) {
            return;
        }
        // 保存手动拉取记录
        saveParkRecord(att4ParkTransactionListAll, attParkAreaItem, attPoint.getDeviceId());
    }

    /**
     * 获取停车(出场)考勤点记录
     */
    private void getParkOutRecord(AttPointItem attPoint, Date startDatetime, Date endDatetime,
        AttParkAreaItem attParkAreaItem, int page) {
        Pager parkOutTransactionList = attGetParkTransactionService.getParkOutTransactionList(attPoint.getDeviceId(),
            startDatetime, endDatetime, page, CollectionUtil.splitSize);
        List<Att4ParkTransaction> att4ParkTransactionListAll =
            (List<Att4ParkTransaction>)parkOutTransactionList.getData();
        if (att4ParkTransactionListAll == null || att4ParkTransactionListAll.size() == 0) {
            return;
        }
        // 保存手动拉取记录
        saveParkRecord(att4ParkTransactionListAll, attParkAreaItem, attPoint.getDeviceId());
    }

    /**
     * 停车场当考勤：保存手动拉取记录
     */
    private void saveParkRecord(List<Att4ParkTransaction> att4ParkTransactionListAll, AttParkAreaItem attParkAreaItem,
        String deviceId) {

        // 根据记录事件的最大最小时间，查询已存在的记录
        Set<String> existAttTransaction = getParkExistTransaction(att4ParkTransactionListAll, deviceId);

        // 过滤同一个人同一分钟、数据库已经存在的人证记录
        List<Att4ParkTransaction> att4ParkTransactionList =
            filterMinuteByParkTransaction(att4ParkTransactionListAll, existAttTransaction);
        if (att4ParkTransactionList == null || att4ParkTransactionList.size() == 0) {
            return;
        }

        // 获取人员信息
        List<String> personPinList = (List<String>)CollectionUtil.getPropertyList(att4ParkTransactionList,
            Att4ParkTransaction::getPersonPin, "-1");
        Map<String, PersPersonCacheItem> pinPersonMap = getPinPersonMap(personPinList);
        if (pinPersonMap == null) {
            return;
        }

        // 遍历组装数据并批量保存
        List<AttTransaction> attTransactionList = new ArrayList<>();
        for (Att4ParkTransaction att4ParkTransaction : att4ParkTransactionList) {
            PersPersonCacheItem persPersonItem = pinPersonMap.get(att4ParkTransaction.getPersonPin());
            if (Objects.isNull(persPersonItem)) {
                continue;
            }
            AttTransaction attTransaction = parkToAttTransaction(att4ParkTransaction, persPersonItem, attParkAreaItem);
            attTransactionList.add(attTransaction);
        }
        if (attTransactionList != null && attTransactionList.size() > 0) {
            attTransactionDao.saveAll(attTransactionList);
        }
    }

    /**
     * 获取信息屏考勤点记录
     */
    private void getInsRecord(AttPointItem attPoint, Date startDatetime, Date endDatetime, AuthAreaItem authAreaItem,
        int page) {
        Pager insTransactionPage = attGetInsTransactionService.getInsTransactionList(attPoint.getDeviceId(),
            startDatetime, endDatetime, page, CollectionUtil.splitSize);
        List<Att4InsTransaction> insTransactionListAll = (List<Att4InsTransaction>)insTransactionPage.getData();
        if (insTransactionListAll == null || insTransactionListAll.size() == 0) {
            return;
        }

        // 根据记录事件的最大最小时间，查询已存在的记录
        Set<String> existAttTransaction = getInsExistAttTransaction(insTransactionListAll, attPoint.getDeviceId());

        // 过滤同一个人同一分钟、数据库已经存在的信息屏记录
        List<Att4InsTransaction> insTransactionList =
            filterMinuteByInsTransaction(insTransactionListAll, existAttTransaction);
        if (insTransactionList == null || insTransactionList.size() == 0) {
            return;
        }

        // 获取人员信息
        List<String> personPinList =
            (List<String>)CollectionUtil.getPropertyList(insTransactionList, Att4InsTransaction::getPersonPin, "-1");
        Map<String, PersPersonCacheItem> pinPersonMap = getPinPersonMap(personPinList);
        if (pinPersonMap == null) {
            return;
        }

        // 遍历组装数据并批量保存
        List<AttTransaction> attTransactionList = new ArrayList<>();
        for (Att4InsTransaction att4InsTransaction : insTransactionList) {
            PersPersonCacheItem persPersonItem = pinPersonMap.get(att4InsTransaction.getPersonPin());
            if (Objects.isNull(persPersonItem)) {
                continue;
            }
            AttTransaction attTransaction = insToAttTransaction(att4InsTransaction, persPersonItem, authAreaItem);
            attTransactionList.add(attTransaction);
        }
        if (attTransactionList != null && attTransactionList.size() > 0) {
            attTransactionDao.saveAll(attTransactionList);
        }
    }

    /**
     * 获取门禁考勤点记录
     */
    private void getAccRecord(AttPointItem attPoint, Date startDatetime, Date endDatetime, AuthAreaItem authAreaItem,
        int page) {

        Pager pager = attGetAccTransactionService.getAccTransactionList(attPoint.getDeviceId(), startDatetime,
            endDatetime, page, CollectionUtil.splitSize);

        List<Att4AccTransaction> att4AccTransactionListAll = (List<Att4AccTransaction>)pager.getData();
        if (att4AccTransactionListAll == null || att4AccTransactionListAll.size() == 0) {
            return;
        }

        // 根据记录事件的最大最小时间，查询已存在的记录
        Set<String> existAttTransaction = getAccExistAttTransaction(att4AccTransactionListAll, attPoint.getDeviceId());

        // 过滤同一个人同一分钟、数据库已经存在的信息屏记录
        List<Att4AccTransaction> att4AccTransactionList =
            filterMinuteByAccTransaction(att4AccTransactionListAll, existAttTransaction);
        if (att4AccTransactionList == null || att4AccTransactionList.size() == 0) {
            return;
        }

        // 查询人员信息
        List<String> personPinList = (List<String>)CollectionUtil.getPropertyList(att4AccTransactionList,
            Att4AccTransaction::getPin, AttConstant.COMM_DEF_VALUE);
        Map<String, PersPersonCacheItem> pinPersonMap = getPinPersonMap(personPinList);
        if (pinPersonMap == null) {
            return;
        }

        // 遍历组装数据并批量保存
        List<AttTransaction> attTransactionList = new ArrayList<>();
        for (Att4AccTransaction att4AccTransaction : att4AccTransactionList) {
            PersPersonCacheItem persPersonItem = pinPersonMap.get(att4AccTransaction.getPin());
            if (Objects.isNull(persPersonItem)) {
                continue;
            }
            AttTransaction attTransaction =
                accToAttTransaction(att4AccTransaction, persPersonItem, authAreaItem, attPoint);
            attTransactionList.add(attTransaction);
        }
        if (attTransactionList != null && attTransactionList.size() > 0) {
            attTransactionDao.saveAll(attTransactionList);
        }
    }

    /**
     * 获取人证考勤点记录
     */
    private void getPidRecord(AttPointItem attPoint, Date startDatetime, Date endDatetime, AuthAreaItem authAreaItem,
        int page) {

        Pager pidTransactionPage = attGetPidTransactionService.getPidTransactionList(attPoint.getDeviceId(),
            startDatetime, endDatetime, page, CollectionUtil.splitSize);
        List<Att4PidTransaction> pidTransactionListAll = (List<Att4PidTransaction>)pidTransactionPage.getData();
        if (pidTransactionListAll == null || pidTransactionListAll.size() == 0) {
            return;
        }

        // 根据记录事件的最大最小时间，查询已存在的记录
        Set<String> existAttTransaction = getPidExistAttTransaction(pidTransactionListAll, attPoint.getDeviceId());

        // 过滤同一个人同一分钟、数据库已经存在的人证记录
        List<Att4PidTransaction> pidTransactionList =
            filterMinuteByPidTransaction(pidTransactionListAll, existAttTransaction);
        if (pidTransactionList == null || pidTransactionList.size() == 0) {
            return;
        }

        // 获取人员信息
        List<String> personPinList =
            (List<String>)CollectionUtil.getPropertyList(pidTransactionList, Att4PidTransaction::getPersonPin, "-1");
        Map<String, PersPersonCacheItem> pinPersonMap = getPinPersonMap(personPinList);
        if (pinPersonMap == null) {
            return;
        }

        // 遍历组装数据并批量保存
        List<AttTransaction> attTransactionList = new ArrayList<>();
        for (Att4PidTransaction att4PidTransaction : pidTransactionList) {
            PersPersonCacheItem persPersonItem = pinPersonMap.get(att4PidTransaction.getPersonPin());
            if (Objects.isNull(persPersonItem)) {
                continue;
            }
            AttTransaction attTransaction = pidToAttTransaction(att4PidTransaction, persPersonItem, authAreaItem);
            attTransactionList.add(attTransaction);
        }
        if (attTransactionList != null && attTransactionList.size() > 0) {
            attTransactionDao.saveAll(attTransactionList);
        }
    }

    /**
     * 获取VMS考勤点记录
     */
    private void getVmsRecord(AttPointItem attPoint, Date startDatetime, Date endDatetime, AuthAreaItem authAreaItem,
        int page) {
        Pager vmsTransactionPage = attGetVmsTransactionService.getVmsTransactionList(attPoint.getDeviceId(),
            startDatetime, endDatetime, page, CollectionUtil.splitSize);
        List<Att4OtherTransactionItem> vmsTransactionListAll =
            (List<Att4OtherTransactionItem>)vmsTransactionPage.getData();
        if (vmsTransactionListAll == null || vmsTransactionListAll.size() == 0) {
            return;
        }

        // 根据记录事件的最大最小时间，查询已存在的记录
        Set<String> existAttTransaction = getVmsExistAttTransaction(vmsTransactionListAll, attPoint.getDeviceId());

        // 过滤同一个人同一分钟、数据库已经存在的人证记录
        List<Att4OtherTransactionItem> vmsTransactionList = filterMinute(vmsTransactionListAll, existAttTransaction);
        if (vmsTransactionList == null || vmsTransactionList.size() == 0) {
            return;
        }

        // 获取人员信息
        List<String> personPinList = (List<String>)CollectionUtil.getPropertyList(vmsTransactionList,
            Att4OtherTransactionItem::getPersonPin, "-1");
        Map<String, PersPersonCacheItem> pinPersonMap = getPinPersonMap(personPinList);
        if (pinPersonMap == null) {
            return;
        }

        // 遍历组装数据并批量保存
        List<AttTransaction> attTransactionList = new ArrayList<>();
        for (Att4OtherTransactionItem vmsTransaction : vmsTransactionList) {
            PersPersonCacheItem persPersonItem = pinPersonMap.get(vmsTransaction.getPersonPin());
            if (Objects.isNull(persPersonItem)) {
                continue;
            }
            AttTransaction attTransaction = vmsToAttTransaction(vmsTransaction, persPersonItem, authAreaItem, attPoint);
            attTransactionList.add(attTransaction);
        }
        if (attTransactionList != null && attTransactionList.size() > 0) {
            attTransactionDao.saveAll(attTransactionList);
        }
    }

    /**
     * 获取Ivs考勤点记录
     */
    private void getIvsRecord(AttPointItem attPoint, Date startDatetime, Date endDatetime, AuthAreaItem authAreaItem,
        int page) {
        if (Objects.isNull(attGetIvsDeviceService)) {
            return;
        }

        Pager ivsTransactionPage = attGetIvsDeviceService.getFaceInfoPagerByDevIds(attPoint.getDeviceId(),
            startDatetime, endDatetime, page, CollectionUtil.splitSize);
        List<Att4OtherTransactionItem> ivsTransactionListAll =
            (List<Att4OtherTransactionItem>)ivsTransactionPage.getData();
        if (ivsTransactionListAll == null || ivsTransactionListAll.size() == 0) {
            return;
        }

        // 根据记录事件的最大最小时间，查询已存在的记录
        Set<String> existAttTransaction =
            getExistAttTransaction(ivsTransactionListAll, attPoint.getDeviceId(), ConstUtil.SYSTEM_MODULE_IVS);

        // 过滤处理，同一个人一个分钟只保存一条记录，过滤数据库存在的记录
        List<Att4OtherTransactionItem> ivsTransactionList = filterMinute(ivsTransactionListAll, existAttTransaction);
        if (ivsTransactionList == null || ivsTransactionList.size() == 0) {
            return;
        }

        // 获取人员信息
        List<String> personPinList = (List<String>)CollectionUtil.getPropertyList(ivsTransactionList,
            Att4OtherTransactionItem::getPersonPin, "-1");
        Map<String, PersPersonCacheItem> pinPersonMap = getPinPersonMap(personPinList);
        if (pinPersonMap == null) {
            return;
        }

        // 遍历组装数据并批量保存
        List<AttTransaction> attTransactionList = new ArrayList<>();
        for (Att4OtherTransactionItem transactionItem : ivsTransactionList) {
            PersPersonCacheItem persPersonItem = pinPersonMap.get(transactionItem.getPersonPin());
            if (Objects.isNull(persPersonItem)) {
                continue;
            }
            AttTransaction attTransaction = att4OtherTransactionItemToAttTransaction(transactionItem, persPersonItem,
                authAreaItem, ConstUtil.SYSTEM_MODULE_IVS);
            attTransactionList.add(attTransaction);
        }
        if (attTransactionList != null && attTransactionList.size() > 0) {
            attTransactionDao.save(attTransactionList);
        }
    }

    /**
     * 获取Ivs考勤点记录
     */
    private void getEsdcRecord(AttPointItem attPoint, Date startDatetime, Date endDatetime, AuthAreaItem authAreaItem,
        int page) {

        if (Objects.isNull(attGetEsdcDataService)) {
            return;
        }

        log.info(
            "attGetEsdcDataService.getTransactionByChannelIds start channelIds = {}, startDatetime = {}, endDatetime = {}, pageNo = {}, pageSize = {}",
            attPoint.getDeviceId(), startDatetime, endDatetime, page, CollectionUtil.splitSize);
        Pager pager = attGetEsdcDataService.getTransactionByChannelIds(attPoint.getDeviceId(), startDatetime,
            endDatetime, page, CollectionUtil.splitSize);
        log.info("attGetEsdcDataService.getTransactionByChannelIds end pager = {}", JSON.toJSONString(pager));
        List<Att4OtherTransactionItem> att4OtherTransactionItemList = (List<Att4OtherTransactionItem>)pager.getData();
        if (att4OtherTransactionItemList == null || att4OtherTransactionItemList.size() == 0) {
            return;
        }

        // 根据记录事件的最大最小时间，查询已存在的记录
        Set<String> existAttTransaction =
            getExistAttTransaction(att4OtherTransactionItemList, attPoint.getDeviceId(), ConstUtil.LICENSE_MODULE_ESDC);

        // 过滤处理，同一个人一个分钟只保存一条记录，过滤数据库存在的记录
        List<Att4OtherTransactionItem> filterTransactionItemList =
            filterMinute(att4OtherTransactionItemList, existAttTransaction);
        if (filterTransactionItemList == null || filterTransactionItemList.size() == 0) {
            return;
        }

        // 获取人员信息
        List<String> personPinList = (List<String>)CollectionUtil.getPropertyList(filterTransactionItemList,
            Att4OtherTransactionItem::getPersonPin, "-1");
        Map<String, PersPersonCacheItem> pinPersonMap = getPinPersonMap(personPinList);
        if (pinPersonMap == null) {
            return;
        }

        // 遍历组装数据并批量保存
        List<AttTransaction> attTransactionList = new ArrayList<>();
        for (Att4OtherTransactionItem transactionItem : filterTransactionItemList) {
            PersPersonCacheItem persPersonItem = pinPersonMap.get(transactionItem.getPersonPin());
            if (Objects.isNull(persPersonItem)) {
                continue;
            }
            AttTransaction attTransaction = att4OtherTransactionItemToAttTransaction(transactionItem, persPersonItem,
                authAreaItem, ConstUtil.LICENSE_MODULE_ESDC);
            attTransactionList.add(attTransaction);
        }
        if (attTransactionList != null && attTransactionList.size() > 0) {
            attTransactionDao.save(attTransactionList);
        }
    }

    /**
     * 获取通道考勤点记录
     */
    private void getPsgRecord(AttPointItem attPoint, Date startDatetime, Date endDatetime, AuthAreaItem authAreaItem,
        int page) {
        if (Objects.isNull(attGetPsgDeviceService)) {
            return;
        }

        Pager pager = attGetPsgDeviceService.getPsgTransactionList(attPoint.getRecordType(), attPoint.getDeviceId(),
            startDatetime, endDatetime, page, CollectionUtil.splitSize);
        List<Att4PsgTransaction> att4PsgTransactionListAll = (List<Att4PsgTransaction>)pager.getData();
        if (att4PsgTransactionListAll == null || att4PsgTransactionListAll.size() == 0) {
            return;
        }

        // 查找存在的记录
        Set<String> existAttTransaction = getPsgExistAttTransaction(att4PsgTransactionListAll, attPoint.getDeviceId());

        // 过滤同一个人同一分钟、数据库已经存在的人证记录
        List<Att4PsgTransaction> att4PsgTransactionList =
            filterMinuteByPsgTransaction(att4PsgTransactionListAll, existAttTransaction);
        if (att4PsgTransactionList == null || att4PsgTransactionList.size() == 0) {
            return;
        }

        // 获取人员信息
        List<String> personPinList =
            (List<String>)CollectionUtil.getPropertyList(att4PsgTransactionList, Att4PsgTransaction::getPin, "-1");
        Map<String, PersPersonCacheItem> pinPersonMap = getPinPersonMap(personPinList);
        if (pinPersonMap == null) {
            return;
        }

        List<AttTransaction> attTransactionList = new ArrayList<>();
        for (Att4PsgTransaction att4PsgTransaction : att4PsgTransactionList) {
            PersPersonCacheItem persPersonItem = pinPersonMap.get(att4PsgTransaction.getPin());
            if (Objects.isNull(persPersonItem)) {
                continue;
            }
            AttTransaction attTransaction =
                psgToAttTransaction(att4PsgTransaction, persPersonItem, authAreaItem, attPoint);
            attTransactionList.add(attTransaction);
        }
        if (attTransactionList != null && attTransactionList.size() > 0) {
            attTransactionDao.saveAll(attTransactionList);
        }
    }

    /**
     * vms当考勤：对象组装
     */
    private AttTransaction vmsToAttTransaction(Att4OtherTransactionItem vmsTransaction,
        PersPersonCacheItem persPersonItem, AuthAreaItem authAreaItem, AttPointItem attPointItem) {
        AttTransaction attTransaction = new AttTransaction();
        attTransaction.setPersonPin(persPersonItem.getPin());
        attTransaction.setPersonName(persPersonItem.getName());
        attTransaction.setPersonLastName(persPersonItem.getLastName());
        attTransaction.setDeptId(persPersonItem.getDeptId());
        attTransaction.setDeptCode(persPersonItem.getDeptCode());
        attTransaction.setDeptName(persPersonItem.getDeptName());
        if (Objects.nonNull(authAreaItem)) {
            attTransaction.setAreaId(authAreaItem.getId());
            attTransaction.setAreaNo(authAreaItem.getCode());
            attTransaction.setAreaName(authAreaItem.getName());
        }
        attTransaction.setAttPhotoUrl(vmsTransaction.getTransactionPhoto());
        attTransaction.setDeviceId(attPointItem.getDeviceId());
        attTransaction.setDeviceSn(vmsTransaction.getDeviceSn());
        attTransaction.setDoorNo(vmsTransaction.getChannelNo());
        // vms视频设备名称由设备名称+通道名称组合 by ljf 2020/1/21
        attTransaction.setDeviceName(vmsTransaction.getDeviceName() + "-" + vmsTransaction.getChannelName());
        Date attDatetime = vmsTransaction.getAttDatetime();
        attTransaction.setAttDatetime(attDatetime);
        attTransaction.setAttDate(DateUtil.getDate(attDatetime));
        attTransaction.setAttTime(DateUtil.getTime(attDatetime));
        attTransaction.setAttVerify(vmsTransaction.getAttVerify());
        attTransaction.setMark(ConstUtil.SYSTEM_MODULE_VMS);
        return attTransaction;
    }

    /**
     * vms当考勤：根据记录事件的最大最小时间，查询已存在的记录
     */
    private Set<String> getVmsExistAttTransaction(List<Att4OtherTransactionItem> vmsTransactionListAll,
        String deviceId) {
        // 创建时间
        Date startAttDatetime = vmsTransactionListAll.stream()
            .min(Comparator.comparing(Att4OtherTransactionItem::getAttDatetime)).get().getAttDatetime();
        Date endAttDatetime = vmsTransactionListAll.stream()
            .max(Comparator.comparing(Att4OtherTransactionItem::getAttDatetime)).get().getAttDatetime();
        startAttDatetime = DateUtil.addMinute(startAttDatetime, -1);
        endAttDatetime = DateUtil.addMinute(endAttDatetime, 1);

        // 查找存在的记录
        Set<String> existAttTransaction = new HashSet<>();
        if (StringUtils.isNotBlank(deviceId)) {
            existAttTransaction = getExistAttTransactionByAttDatetimeAndDeviceId(ConstUtil.SYSTEM_MODULE_VMS,
                startAttDatetime, endAttDatetime, deviceId);
        } else {
            existAttTransaction =
                getExistAttTransactionByAttDatetime(ConstUtil.SYSTEM_MODULE_VMS, startAttDatetime, endAttDatetime);
        }
        return existAttTransaction;
    }

    /**
     * 智能视频当考勤：对象组装
     */
    private AttTransaction att4OtherTransactionItemToAttTransaction(Att4OtherTransactionItem att4OtherTransactionItem,
        PersPersonCacheItem persPersonItem, AuthAreaItem authAreaItem, String module) {
        AttTransaction attTransaction = new AttTransaction();
        attTransaction.setPersonPin(persPersonItem.getPin());
        attTransaction.setPersonName(persPersonItem.getName());
        attTransaction.setPersonLastName(persPersonItem.getLastName());
        attTransaction.setDeptId(persPersonItem.getDeptId());
        attTransaction.setDeptCode(persPersonItem.getDeptCode());
        attTransaction.setDeptName(persPersonItem.getDeptName());
        if (Objects.nonNull(authAreaItem)) {
            attTransaction.setAreaId(authAreaItem.getId());
            attTransaction.setAreaNo(authAreaItem.getCode());
            attTransaction.setAreaName(authAreaItem.getName());
        }
        attTransaction.setDeviceId(att4OtherTransactionItem.getDeviceId());
        attTransaction.setDeviceName(att4OtherTransactionItem.getDeviceName());
        Date attDatetime = att4OtherTransactionItem.getAttDatetime();
        attTransaction.setAttDatetime(attDatetime);
        attTransaction.setAttDate(DateUtil.getDate(attDatetime));
        attTransaction.setAttTime(DateUtil.getTime(attDatetime));
        attTransaction.setAttVerify("15"); // 验证方式15:人脸
        attTransaction.setMark(module);
        attTransaction.setAttPhotoUrl(att4OtherTransactionItem.getTransactionPhoto());
        return attTransaction;
    }

    /**
     * 智能视频、智能场景当考勤 根据记录事件的最大最小时间，查询已存在的记录
     */
    private Set<String> getExistAttTransaction(List<Att4OtherTransactionItem> list, String deviceId, String module) {
        // 事件开始时间、结束时间
        Date startAttDatetime =
            list.stream().min(Comparator.comparing(Att4OtherTransactionItem::getAttDatetime)).get().getAttDatetime();
        Date endAttDatetime =
            list.stream().max(Comparator.comparing(Att4OtherTransactionItem::getAttDatetime)).get().getAttDatetime();
        startAttDatetime = DateUtil.addMinute(startAttDatetime, -1);
        endAttDatetime = DateUtil.addMinute(endAttDatetime, 1);

        // 查找存在的记录
        Set<String> existAttTransaction = new HashSet<>();
        if (StringUtils.isNotBlank(deviceId)) {
            existAttTransaction =
                getExistAttTransactionByAttDatetimeAndDeviceId(module, startAttDatetime, endAttDatetime, deviceId);
        } else {
            existAttTransaction = getExistAttTransactionByAttDatetime(module, startAttDatetime, endAttDatetime);
        }
        return existAttTransaction;
    }

    /**
     * 过滤考勤原始记录，同一分钟只保留一条记录
     */
    private List<Att4OtherTransactionItem> filterMinute(List<Att4OtherTransactionItem> list, Set<String> existSet) {
        List<Att4OtherTransactionItem> filterList = new ArrayList<>();
        for (Att4OtherTransactionItem item : list) {
            String pingAndMinute = getPingAndMinute(item.getPersonPin(), item.getAttDatetime());
            if (existSet.contains(pingAndMinute)) {
                continue;
            }
            existSet.add(pingAndMinute);
            filterList.add(item);
        }
        return filterList;
    }

    /**
     * 门禁当考勤：过滤同一个人同一分钟、数据库已经存在的门禁记录
     */
    private List<Att4AccTransaction> filterMinuteByAccTransaction(List<Att4AccTransaction> list, Set<String> existSet) {
        List<Att4AccTransaction> filterList = new ArrayList<>();
        for (Att4AccTransaction item : list) {
            String pingAndMinute = getPingAndMinute(item.getPin(), item.getEventTime());
            if (existSet.contains(pingAndMinute)) {
                continue;
            }
            existSet.add(pingAndMinute);
            filterList.add(item);
        }
        return filterList;
    }

    /**
     * 门禁当考勤：对象组装
     */
    private AttTransaction accToAttTransaction(Att4AccTransaction att4AccTransaction,
        PersPersonCacheItem persPersonItem, AuthAreaItem authAreaItem, AttPointItem attPointItem) {
        AttTransaction attTransaction = new AttTransaction();
        attTransaction.setPersonPin(persPersonItem.getPin());
        attTransaction.setPersonName(persPersonItem.getName());
        attTransaction.setPersonLastName(persPersonItem.getLastName());
        attTransaction.setDeptId(persPersonItem.getDeptId());
        attTransaction.setDeptCode(persPersonItem.getDeptCode());
        attTransaction.setDeptName(persPersonItem.getDeptName());
        if (Objects.nonNull(authAreaItem)) {
            attTransaction.setAreaId(authAreaItem.getId());
            attTransaction.setAreaNo(authAreaItem.getCode());
            attTransaction.setAreaName(authAreaItem.getName());
        }
        if (Objects.nonNull(attPointItem)) {
            attTransaction.setDeviceName(attPointItem.getDeviceName());
            attTransaction.setDoorNo(attPointItem.getDoorNo());
        }
        // 门禁的事件点ID就是对应考勤点的门ID
        attTransaction.setDeviceId(att4AccTransaction.getEventPointId());
        attTransaction.setDeviceSn(att4AccTransaction.getDevSn());

        attTransaction.setAttPhotoUrl(att4AccTransaction.getTransactionPhoto());
        Date attDatetime = att4AccTransaction.getEventTime();
        attTransaction.setAttDatetime(attDatetime);
        attTransaction.setAttDate(DateUtil.getDate(attDatetime));
        attTransaction.setAttTime(DateUtil.getTime(attDatetime));
        attTransaction.setAttVerify(att4AccTransaction.getVerifyModeNo());
        attTransaction.setMark(ConstUtil.SYSTEM_MODULE_ACC);
        return attTransaction;
    }

    /**
     * 门禁当考勤：根据记录事件的最大最小时间，查询已存在的记录
     */
    private Set<String> getAccExistAttTransaction(List<Att4AccTransaction> att4AccTransactionListAll, String deviceId) {
        // 获取记录的最大和最小事件时间
        Date startAttDatetime = att4AccTransactionListAll.stream()
            .min(Comparator.comparing(Att4AccTransaction::getEventTime)).get().getEventTime();
        Date endAttDatetime = att4AccTransactionListAll.stream()
            .max(Comparator.comparing(Att4AccTransaction::getEventTime)).get().getEventTime();
        startAttDatetime = DateUtil.addMinute(startAttDatetime, -1);
        endAttDatetime = DateUtil.addMinute(endAttDatetime, 1);
        // 查找存在的记录
        Set<String> existAttTransaction = new HashSet<>();
        if (StringUtils.isNotBlank(deviceId)) {
            existAttTransaction = getExistAttTransactionByAttDatetimeAndDeviceId(ConstUtil.SYSTEM_MODULE_ACC,
                startAttDatetime, endAttDatetime, deviceId);
        } else {
            existAttTransaction =
                getExistAttTransactionByAttDatetime(ConstUtil.SYSTEM_MODULE_ACC, startAttDatetime, endAttDatetime);
        }
        return existAttTransaction;
    }

    /**
     * 停车场当考勤：过滤同一个人同一分钟、数据库已经存在的停车记录
     */
    private List<Att4ParkTransaction> filterMinuteByParkTransaction(List<Att4ParkTransaction> list,
        Set<String> existSet) {
        List<Att4ParkTransaction> filterList = new ArrayList<>();
        for (Att4ParkTransaction item : list) {
            String pingAndMinute = getPingAndMinute(item.getPersonPin(), item.getAttDatetime());
            if (existSet.contains(pingAndMinute)) {
                continue;
            }
            existSet.add(pingAndMinute);
            filterList.add(item);
        }
        return filterList;
    }

    /**
     * 停车场当考勤：对象组装
     */
    private AttTransaction parkToAttTransaction(Att4ParkTransaction att4ParkTransaction,
        PersPersonCacheItem persPersonItem, AttParkAreaItem attParkAreaItem) {
        AttTransaction attTransaction = new AttTransaction();
        attTransaction.setPersonPin(persPersonItem.getPin());
        attTransaction.setPersonName(persPersonItem.getName());
        attTransaction.setPersonLastName(persPersonItem.getLastName());
        attTransaction.setDeptId(persPersonItem.getDeptId());
        attTransaction.setDeptCode(persPersonItem.getDeptCode());
        attTransaction.setDeptName(persPersonItem.getDeptName());
        if (Objects.nonNull(attParkAreaItem)) {
            attTransaction.setAreaId(attParkAreaItem.getId());
            attTransaction.setAreaName(attParkAreaItem.getName());
        }
        attTransaction.setDeviceId(att4ParkTransaction.getDeviceId());
        attTransaction.setDeviceSn(att4ParkTransaction.getDeviceSn());
        attTransaction.setAttPhotoUrl(att4ParkTransaction.getTransactionPhoto());
        Date attDatetime = att4ParkTransaction.getAttDatetime();
        attTransaction.setAttDatetime(attDatetime);
        attTransaction.setAttDate(DateUtil.getDate(attDatetime));
        attTransaction.setAttTime(DateUtil.getTime(attDatetime));
        attTransaction.setMark(ConstUtil.SYSTEM_MODULE_PARK);
        return attTransaction;
    }

    /**
     * 停车场当考勤：根据记录事件的最大最小时间，查询已存在的记录
     */
    private Set<String> getParkExistTransaction(List<Att4ParkTransaction> att4ParkTransactionListAll, String deviceId) {

        // 获取记录的最大和最小事件时间
        Date startAttDatetime = att4ParkTransactionListAll.stream()
            .min(Comparator.comparing(Att4ParkTransaction::getAttDatetime)).get().getAttDatetime();
        Date endAttDatetime = att4ParkTransactionListAll.stream()
            .max(Comparator.comparing(Att4ParkTransaction::getAttDatetime)).get().getAttDatetime();
        startAttDatetime = DateUtil.addMinute(startAttDatetime, -1);
        endAttDatetime = DateUtil.addMinute(endAttDatetime, 1);

        // 查找存在的记录
        Set<String> existAttTransaction = new HashSet<>();
        if (StringUtils.isNotBlank(deviceId)) {
            existAttTransaction = getExistAttTransactionByAttDatetimeAndDeviceId(ConstUtil.SYSTEM_MODULE_PARK,
                startAttDatetime, endAttDatetime, deviceId);
        } else {
            existAttTransaction =
                getExistAttTransactionByAttDatetime(ConstUtil.SYSTEM_MODULE_PARK, startAttDatetime, endAttDatetime);
        }
        return existAttTransaction;
    }

    /**
     * 信息屏当考勤：过滤同一个人同一分钟、数据库已经存在的信息屏记录
     */
    private List<Att4InsTransaction> filterMinuteByInsTransaction(List<Att4InsTransaction> list, Set<String> existSet) {
        List<Att4InsTransaction> filterList = new ArrayList<>();
        for (Att4InsTransaction item : list) {
            String pingAndMinute = getPingAndMinute(item.getPersonPin(), item.getAttDatetime());
            if (existSet.contains(pingAndMinute)) {
                continue;
            }
            existSet.add(pingAndMinute);
            filterList.add(item);
        }
        return filterList;
    }

    /**
     * 信息屏当考勤：对象组装
     */
    private AttTransaction insToAttTransaction(Att4InsTransaction att4InsTransaction,
        PersPersonCacheItem persPersonItem, AuthAreaItem authAreaItem) {
        AttTransaction attTransaction = new AttTransaction();
        attTransaction.setPersonPin(persPersonItem.getPin());
        attTransaction.setPersonName(persPersonItem.getName());
        attTransaction.setPersonLastName(persPersonItem.getLastName());
        attTransaction.setDeptId(persPersonItem.getDeptId());
        attTransaction.setDeptCode(persPersonItem.getDeptCode());
        attTransaction.setDeptName(persPersonItem.getDeptName());
        attTransaction.setDeviceId(att4InsTransaction.getDeviceId());
        attTransaction.setDeviceSn(att4InsTransaction.getDeviceSn());
        if (Objects.nonNull(authAreaItem)) {
            attTransaction.setAreaId(authAreaItem.getId());
            attTransaction.setAreaNo(authAreaItem.getCode());
            attTransaction.setAreaName(authAreaItem.getName());
        }
        attTransaction.setAttPhotoUrl(att4InsTransaction.getTransactionPhoto());
        Date attDatetime = att4InsTransaction.getAttDatetime();
        attTransaction.setAttDatetime(attDatetime);
        attTransaction.setAttDate(DateUtil.getDate(attDatetime));
        attTransaction.setAttTime(DateUtil.getTime(attDatetime));
        attTransaction.setAttVerify(att4InsTransaction.getVerifyMode());
        attTransaction.setMark(ConstUtil.SYSTEM_MODULE_INS);

        return attTransaction;
    }

    /**
     * 信息屏当考勤：根据记录事件的最大最小时间，查询已存在的记录
     */
    private Set<String> getInsExistAttTransaction(List<Att4InsTransaction> insTransactionListAll, String deviceId) {
        // 事件时间最大最小时间
        Date startAttDatetime = insTransactionListAll.stream()
            .min(Comparator.comparing(Att4InsTransaction::getAttDatetime)).get().getAttDatetime();
        Date endAttDatetime = insTransactionListAll.stream()
            .max(Comparator.comparing(Att4InsTransaction::getAttDatetime)).get().getAttDatetime();
        startAttDatetime = DateUtil.addMinute(startAttDatetime, -1);
        endAttDatetime = DateUtil.addMinute(endAttDatetime, 1);
        // 查找存在的记录
        Set<String> existAttTransaction = new HashSet<>();
        if (StringUtils.isNotBlank(deviceId)) {
            existAttTransaction = getExistAttTransactionByAttDatetimeAndDeviceId(ConstUtil.SYSTEM_MODULE_INS,
                startAttDatetime, endAttDatetime, deviceId);
        } else {
            existAttTransaction =
                getExistAttTransactionByAttDatetime(ConstUtil.SYSTEM_MODULE_INS, startAttDatetime, endAttDatetime);
        }
        return existAttTransaction;
    }

    /**
     * 人证当考勤：过滤同一个人同一分钟、数据库已经存在的人证记录
     */
    private List<Att4PidTransaction> filterMinuteByPidTransaction(List<Att4PidTransaction> list, Set<String> existSet) {
        List<Att4PidTransaction> filterList = new ArrayList<>();
        for (Att4PidTransaction item : list) {
            String pingAndMinute = getPingAndMinute(item.getPersonPin(), item.getVerifyTime());
            if (existSet.contains(pingAndMinute)) {
                continue;
            }
            existSet.add(pingAndMinute);
            filterList.add(item);
        }
        return filterList;
    }

    /**
     * 人证当考勤：对象组装
     */
    private AttTransaction pidToAttTransaction(Att4PidTransaction att4PidTransaction,
        PersPersonCacheItem persPersonItem, AuthAreaItem authAreaItem) {
        AttTransaction attTransaction = new AttTransaction();
        attTransaction.setPersonPin(persPersonItem.getPin());
        attTransaction.setPersonName(persPersonItem.getName());
        attTransaction.setPersonLastName(persPersonItem.getLastName());
        attTransaction.setDeptId(persPersonItem.getDeptId());
        attTransaction.setDeptCode(persPersonItem.getDeptCode());
        attTransaction.setDeptName(persPersonItem.getDeptName());
        if (Objects.nonNull(authAreaItem)) {
            attTransaction.setAreaId(authAreaItem.getId());
            attTransaction.setAreaNo(authAreaItem.getCode());
            attTransaction.setAreaName(authAreaItem.getName());
        }
        attTransaction.setAttPhotoUrl(att4PidTransaction.getCapturePhoto());
        attTransaction.setDeviceId(att4PidTransaction.getDeviceId());
        attTransaction.setDeviceSn(att4PidTransaction.getDeviceSn());
        Date attDatetime = att4PidTransaction.getVerifyTime();
        attTransaction.setAttDatetime(attDatetime);
        attTransaction.setAttDate(DateUtil.getDate(attDatetime));
        attTransaction.setAttTime(DateUtil.getTime(attDatetime));
        attTransaction.setAttVerify(att4PidTransaction.getVerifyMode());
        attTransaction.setMark(ConstUtil.SYSTEM_MODULE_IDENTIFICATION);
        return attTransaction;
    }

    /**
     * 人证当考勤：根据记录事件的最大最小时间，查询已存在的记录
     */
    private Set<String> getPidExistAttTransaction(List<Att4PidTransaction> pidTransactionListAll, String deviceId) {
        // 获取记录的最大和最小事件时间
        Date startAttDatetime = pidTransactionListAll.stream()
            .min(Comparator.comparing(Att4PidTransaction::getVerifyTime)).get().getVerifyTime();
        Date endAttDatetime = pidTransactionListAll.stream()
            .max(Comparator.comparing(Att4PidTransaction::getVerifyTime)).get().getVerifyTime();
        startAttDatetime = DateUtil.addMinute(startAttDatetime, -1);
        endAttDatetime = DateUtil.addMinute(endAttDatetime, 1);
        // 查找存在的记录
        Set<String> existAttTransaction = new HashSet<>();
        if (StringUtils.isNotBlank(deviceId)) {
            existAttTransaction = getExistAttTransactionByAttDatetimeAndDeviceId(ConstUtil.SYSTEM_MODULE_IDENTIFICATION,
                startAttDatetime, endAttDatetime, deviceId);
        } else {
            existAttTransaction = getExistAttTransactionByAttDatetime(ConstUtil.SYSTEM_MODULE_IDENTIFICATION,
                startAttDatetime, endAttDatetime);
        }
        return existAttTransaction;
    }

    /**
     * 通道当考勤：过滤同一个人同一分钟、数据库已经存在的通道记录
     */
    private List<Att4PsgTransaction> filterMinuteByPsgTransaction(List<Att4PsgTransaction> list, Set<String> existSet) {
        List<Att4PsgTransaction> filterList = new ArrayList<>();
        for (Att4PsgTransaction item : list) {
            String pingAndMinute = getPingAndMinute(item.getPin(), item.getEventTime());
            if (existSet.contains(pingAndMinute)) {
                continue;
            }
            existSet.add(pingAndMinute);
            filterList.add(item);
        }
        return filterList;
    }

    /**
     * 通道当考勤：对象组装
     */
    private AttTransaction psgToAttTransaction(Att4PsgTransaction att4PsgTransaction,
        PersPersonCacheItem persPersonItem, AuthAreaItem authAreaItem, AttPointItem attPointItem) {
        AttTransaction attTransaction = new AttTransaction();
        attTransaction.setPersonPin(persPersonItem.getPin());
        attTransaction.setPersonName(persPersonItem.getName());
        attTransaction.setPersonLastName(persPersonItem.getLastName());
        attTransaction.setDeptId(persPersonItem.getDeptId());
        attTransaction.setDeptCode(persPersonItem.getDeptCode());
        attTransaction.setDeptName(persPersonItem.getDeptName());
        if (Objects.nonNull(authAreaItem)) {
            attTransaction.setAreaId(authAreaItem.getId());
            attTransaction.setAreaNo(authAreaItem.getCode());
            attTransaction.setAreaName(authAreaItem.getName());
        }
        attTransaction.setDeviceId(attPointItem.getDeviceId());
        attTransaction.setDeviceName(attPointItem.getDeviceName());
        attTransaction.setDeviceSn(att4PsgTransaction.getDevSn());
        attTransaction.setDoorNo(attPointItem.getDoorNo());
        attTransaction.setAttPhotoUrl(att4PsgTransaction.getTransactionPhoto());
        Date attDatetime = att4PsgTransaction.getEventTime();
        attTransaction.setAttDatetime(attDatetime);
        attTransaction.setAttDate(DateUtil.getDate(attDatetime));
        attTransaction.setAttTime(DateUtil.getTime(attDatetime));
        attTransaction.setAttVerify(att4PsgTransaction.getVerifyModeNo());
        attTransaction.setMark(ConstUtil.SYSTEM_MODULE_PASSAGE);
        return attTransaction;
    }

    /**
     * 通道当考勤：根据记录事件的最大最小时间，查询已存在的记录
     */
    private Set<String> getPsgExistAttTransaction(List<Att4PsgTransaction> psgTransactionListAll, String deviceId) {
        // 事件时间
        Date startAttDatetime = psgTransactionListAll.stream()
            .min(Comparator.comparing(Att4PsgTransaction::getEventTime)).get().getEventTime();
        Date endAttDatetime = psgTransactionListAll.stream().max(Comparator.comparing(Att4PsgTransaction::getEventTime))
            .get().getEventTime();
        startAttDatetime = DateUtil.addMinute(startAttDatetime, -1);
        endAttDatetime = DateUtil.addMinute(endAttDatetime, 1);

        // 查找存在的记录
        Set<String> existAttTransaction = new HashSet<>();
        if (StringUtils.isNotBlank(deviceId)) {
            existAttTransaction = getExistAttTransactionByAttDatetimeAndDeviceId(ConstUtil.SYSTEM_MODULE_PASSAGE,
                startAttDatetime, endAttDatetime, deviceId);
        } else {
            existAttTransaction =
                getExistAttTransactionByAttDatetime(ConstUtil.SYSTEM_MODULE_PASSAGE, startAttDatetime, endAttDatetime);
        }
        return existAttTransaction;
    }

    /**
     * 根据考勤的开始时间和结束时间、考勤标记，获取已经存在的数据
     */
    private Set<String> getExistAttTransactionByAttDatetime(String mark, Date startAttDatetime, Date endAttDatetime) {
        Set<String> existAttTransactionSet = new HashSet<>();
        Long existTransactionCount =
            attTransactionDao.countByMarkAndAttDatetimeBetween(mark, startAttDatetime, endAttDatetime);
        if (existTransactionCount > 0) {
            long countExist = existTransactionCount / CollectionUtil.splitSize;
            if (existTransactionCount % CollectionUtil.splitSize > 0) {
                countExist++;
            }
            for (int page = 0; page < countExist; page++) {
                Pageable pageable = PageRequest.of(page, CollectionUtil.splitSize);
                List<Object[]> existObjectList =
                    attTransactionDao.findByMarkAndAttDatetimeBetween(mark, startAttDatetime, endAttDatetime, pageable);
                if (!CollectionUtil.isEmpty(existObjectList)) {
                    for (Object[] item : existObjectList) {
                        if (Objects.isNull(item[0]) || Objects.isNull(item[1])) {
                            continue;
                        }
                        String uniqueKey = getPingAndMinute(item[0] + "", (Date)item[1]);
                        existAttTransactionSet.add(uniqueKey);
                    }
                }
            }
        }
        return existAttTransactionSet;
    }

    /**
     * 根据考勤的开始时间和结束时间、考勤标记，获取已经存在的数据
     */
    private Set<String> getExistAttTransactionByAttDatetimeAndDeviceId(String mark, Date startAttDatetime,
        Date endAttDatetime, String deivceId) {
        Set<String> existAttTransactionSet = new HashSet<>();
        Long existTransactionCount = attTransactionDao.countByMarkAndAttDatetimeBetweenAndDeviceId(mark,
            startAttDatetime, endAttDatetime, deivceId);
        if (existTransactionCount > 0) {
            long countExist = existTransactionCount / CollectionUtil.splitSize;
            if (existTransactionCount % CollectionUtil.splitSize > 0) {
                countExist++;
            }
            for (int page = 0; page < countExist; page++) {
                Pageable pageable = PageRequest.of(page, CollectionUtil.splitSize);
                List<Object[]> existObjectList = attTransactionDao.findByMarkAndAttDatetimeBetweenAndDeviceId(mark,
                    startAttDatetime, endAttDatetime, deivceId, pageable);
                if (!CollectionUtil.isEmpty(existObjectList)) {
                    for (Object[] item : existObjectList) {
                        if (Objects.isNull(item[0]) || Objects.isNull(item[1])) {
                            continue;
                        }
                        String uniqueKey = getPingAndMinute(item[0] + "", (Date)item[1]);
                        existAttTransactionSet.add(uniqueKey);
                    }
                }
            }
        }
        return existAttTransactionSet;
    }

    /**
     * 第三方当考勤统一一分钟保留一条记录，组装格式 pin=年月日时分，去重判断
     */
    private String getPingAndMinute(String personPin, Date attDatetime) {
        return personPin + "=" + DateUtil.dateToString(attDatetime, DateStyle.YYYY_MM_DD_HH_MM.getValue());
    }

    /**
     * 根据人员编号集合获取人员编号：对象集合
     */
    private Map<String, PersPersonCacheItem> getPinPersonMap(List<String> personPinList) {
        List<PersPersonCacheItem> persPersonItems = persPersonCacheService.getPersonCacheByPins(personPinList);
        if (CollectionUtil.isEmpty(persPersonItems)) {
            return null;
        }
        return CollectionUtil.listToKeyMap(persPersonItems, PersPersonCacheItem::getPin);
    }

    /**
     * 停车场当考勤：获取考勤点对应的区域信息
     */
    private Map<String, AttParkAreaItem> getParkPointAuthAreaMap(String pointDeviceIds) {
        List<AttPointItem> attPointItems =
            attPointService.getByCondition(new AttPointItem().setInDeviceId(pointDeviceIds));
        Collection<String> areaIds = CollectionUtil.getPropertyList(attPointItems, AttPointItem::getAreaId, "-1");
        List<AttParkAreaItem> attParkAreaItems = attParkDeviceService.getParkAreaByAreaIds(areaIds);
        Map<String, AttParkAreaItem> attParkAreaItemMap = CollectionUtil.itemListToIdMap(attParkAreaItems);
        Map<String, AttParkAreaItem> attPointAuthAreaMap = new HashMap<>();
        for (AttPointItem attPointItem : attPointItems) {
            attPointAuthAreaMap.put(attPointItem.getDeviceId(), attParkAreaItemMap.get(attPointItem.getAreaId()));
        }
        return attPointAuthAreaMap;
    }

    /**
     * 获取考勤点和区域集合
     */
    private Map<String, AuthAreaItem> getAttPointAuthAreaMap(List<AttPointItem> attPointItems) {
        String areaIds = CollectionUtil.getPropertys(attPointItems, AttPointItem::getAreaId);
        List<AuthAreaItem> authAreaItems = authAreaService.getItemsByIds(areaIds);
        Map<String, AuthAreaItem> authAreaItemMap = CollectionUtil.itemListToIdMap(authAreaItems);
        Map<String, AuthAreaItem> attPointAuthAreaMap = new HashMap<>();
        for (AttPointItem attPointItem : attPointItems) {
            attPointAuthAreaMap.put(attPointItem.getDeviceId(), authAreaItemMap.get(attPointItem.getAreaId()));
        }
        return attPointAuthAreaMap;
    }

    /**
     * 推送给微信公众号消息 <br/>
     *
     * 推送频率规则: <br/>
     * 1)第一次则直接推送 <br/>
     * 2)根据考勤计算结果或排班情况,判断是否在班前或班后第一次打卡则直接推送 <br/>
     * 3)否则根据间隔时间推送<br/>
     *
     * @param attTransaction:
     * @return void
     * <AUTHOR>
     * @date 2020-11-27 11:54
     * @since 1.0.0
     */
    private void sendTransactionWxMessage(AttTransaction attTransaction) {

        String personPin = attTransaction.getPersonPin();
        Date attDatetime = attTransaction.getAttDatetime();
        String attDate = AttDateUtils.dateToStrAsShort(attDatetime);
        Date dayBeginTime = DateUtil.getDayBeginTime(attDatetime);
        Date dayEndTime = DateUtil.getDayEndTime(attDatetime);

        MessagePushCloudSendMessageItem messageItem = new MessagePushCloudSendMessageItem();
        messageItem.setTouser(personPin);
        messageItem.setSendMessageType("attTransactionWxMessage");
        messageItem.setMessageType("attSignWxMessage"); // 云端定义
        messageItem.setFirstContent(I18nUtil.i18nCode("att_sdc_wxMsg_firstData"));
        messageItem.setCommonContent1(attTransaction.getPersonName() + "(" + personPin + ")");
        messageItem.setCommonContent2(DateUtil.dateToString(attDatetime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM));
        messageItem.setCommonContent3(I18nUtil.i18nCode("att_sdc_wxMsg_stateData"));
        messageItem.setRemark(I18nUtil.i18nCode("att_sdc_wxMsg_remark"));
        // 第一次则推送并记录时间
        String lastTime = attCacheManager.getPersonTransactionTime(personPin);
        if (StringUtils.isBlank(lastTime)) {
            attCacheManager.setPersonTransactionTime(personPin, AttDateUtils.dateToStrAsLong(attDatetime));
            if (null != attCloudMessageSendService) {
                log.info("sendTransactionWxMessage name = {} attDatetime = {} => First Time",
                    messageItem.getCommonContent1(), messageItem.getCommonContent2());
                attCloudMessageSendService.asyncPushTransactionWxMsgToCloud(Arrays.asList(messageItem));
            }
            return;
        }
        // 上次打卡推送时间
        Date lastDateTime = DateUtil.stringToDate(lastTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);

        // 班前班后临界点的不用等间隔时间
        AttRecordItem attRecordItem = attRecordService.getItemByPinAndDate(personPin, attDate);
        if (Objects.nonNull(attRecordItem)) {
            String shiftTimeData = attRecordItem.getShiftTimeData();
            if (StringUtils.isNotBlank(shiftTimeData)) {
                String[] shiftTimeArr = shiftTimeData.split(";");
                for (String timeSlot : shiftTimeArr) {
                    String[] timeSlotArr = timeSlot.split("-");
                    // 班前班后临界点的不用等间隔时间
                    Date startWorkTime = DateUtil.stringToDate(timeSlotArr[0], DateUtil.DateStyle.HH_MM);
                    Date offWorkTime = DateUtil.stringToDate(timeSlotArr[1], DateUtil.DateStyle.HH_MM);
                    boolean isSend = false;
                    if ((startWorkTime.getTime()
                        - attDatetime.getTime()) < AttConstant.ATT_PUSH_TRANSACTION_MESSAGE_FREQUENCY) {
                        // 上班前打卡
                        if ((startWorkTime.getTime()
                            - lastDateTime.getTime()) > AttConstant.ATT_PUSH_TRANSACTION_MESSAGE_FREQUENCY) {
                            // 上次打卡在间隔前
                            isSend = true;
                        }
                    }
                    if ((attDatetime.getTime()
                        - offWorkTime.getTime()) < AttConstant.ATT_PUSH_TRANSACTION_MESSAGE_FREQUENCY) {
                        // 下班后打卡
                        if (lastDateTime.getTime() < offWorkTime.getTime()) {
                            // 上次打卡《 下班时间 《 当前打卡 《 下班后间隔
                            isSend = true;
                        }
                    }
                    if (isSend) {
                        log.info("sendTransactionWxMessage name = {} attDatetime = {} => In Shift Time",
                            messageItem.getCommonContent1(), messageItem.getCommonContent2());
                        attCacheManager.setPersonTransactionTime(personPin, AttDateUtils.dateToStrAsLong(attDatetime));
                        attCacheManager.setPersonTransactionTime(personPin, AttDateUtils.dateToStrAsLong(attDatetime));
                        if (null != attCloudMessageSendService) {
                            attCloudMessageSendService.asyncPushTransactionWxMsgToCloud(Arrays.asList(messageItem));
                        }
                        return;
                    }
                }
            }
        } else {
            // 获取排班信息
            Map<String, List<AttPersonSchBO>> personSchData =
                attPersonSchDataService.getPersonAllSchData(Arrays.asList(personPin), dayBeginTime, dayEndTime);
            List<AttPersonSchBO> attPersonSchBOList = personSchData.get(personPin + "=" + attDate);
            if (!CollectionUtil.isEmpty(attPersonSchBOList)) {
                AttPersonSchBO attPersonSchBO = attPersonSchBOList.get(0);
                List<AttTimeSlotBO> attTimeSlotArray = attPersonSchBO.getAttTimeSlotArray();
                if (!CollectionUtil.isEmpty(attTimeSlotArray)) {
                    List<AttTimeSlotItem> allTimeSlotItem = attTimeSlotService.getAllTimeSlotItem();
                    Map<String, AttTimeSlotItem> timeSlotItemMap = CollectionUtil.itemListToIdMap(allTimeSlotItem);
                    for (AttTimeSlotBO attTimeSlotBO : attTimeSlotArray) {
                        String attTimeSlotId = attTimeSlotBO.getAttTimeSlotId();
                        AttTimeSlotItem attTimeSlotItem = timeSlotItemMap.get(attTimeSlotId);
                        boolean isSend = false;

                        if (AttConstant.PERIODTYPE_NORMAL.equals(attTimeSlotItem.getPeriodType())) {
                            Date startWorkTime = AttDateUtils.stringToYmdHmsDate(String.format("%s %s:00",
                                attTimeSlotBO.getFirstDay(), attTimeSlotItem.getToWorkTime()));
                            Date offWorkTime = AttDateUtils.stringToYmdHmsDate(String.format("%s %s:00",
                                attTimeSlotBO.getSecondDay(), attTimeSlotItem.getOffWorkTime()));
                            if ((startWorkTime.getTime()
                                - attDatetime.getTime()) < AttConstant.ATT_PUSH_TRANSACTION_MESSAGE_FREQUENCY) {
                                // 上班前打卡
                                if ((startWorkTime.getTime()
                                    - lastDateTime.getTime()) > AttConstant.ATT_PUSH_TRANSACTION_MESSAGE_FREQUENCY) {
                                    // 上次打卡在间隔前
                                    isSend = true;
                                }
                            }
                            if ((attDatetime.getTime()
                                - offWorkTime.getTime()) < AttConstant.ATT_PUSH_TRANSACTION_MESSAGE_FREQUENCY) {
                                // 下班后打卡
                                if (lastDateTime.getTime() < offWorkTime.getTime()) {
                                    // 上次打卡《 下班时间 《 当前打卡 《 下班后间隔
                                    isSend = true;
                                }
                            }
                        }

                        if (isSend) {
                            attCacheManager.setPersonTransactionTime(personPin,
                                AttDateUtils.dateToStrAsLong(attDatetime));
                            attCacheManager.setPersonTransactionTime(personPin,
                                AttDateUtils.dateToStrAsLong(attDatetime));
                            if (null != attCloudMessageSendService) {
                                log.info("sendTransactionWxMessage name = {} attDatetime = {} => In Shift Time",
                                    messageItem.getCommonContent1(), messageItem.getCommonContent2());
                                attCloudMessageSendService.asyncPushTransactionWxMsgToCloud(Arrays.asList(messageItem));
                            }
                            return;
                        }

                    }
                }
            }
        }

        // 非上下班临界时间的大于间隔时间才发送
        if ((attDatetime.getTime() - AttDateUtils.stringToYmdHmsDate(lastTime)
            .getTime()) > AttConstant.ATT_PUSH_TRANSACTION_MESSAGE_FREQUENCY) {
            attCacheManager.setPersonTransactionTime(personPin, AttDateUtils.dateToStrAsLong(attDatetime));
            if (null != attCloudMessageSendService) {
                log.info("sendTransactionWxMessage name = {} attDatetime = {} => In Intervals Time",
                    messageItem.getCommonContent1(), messageItem.getCommonContent2());
                attCloudMessageSendService.asyncPushTransactionWxMsgToCloud(Arrays.asList(messageItem));
            }
        } else {
            log.info("sendTransactionWxMessage name = {} attDatetime = {} => Not In Intervals Time",
                messageItem.getCommonContent1(), messageItem.getCommonContent2());
        }
    }

    /**
     * 定时清理照片
     *
     * @Param [keptMonth]
     * @return
     */
    private void delPhotos(String paramValue) {
        String photoPath = systemFilePath + File.separator + AttConstant.TRANSACTION_PHOTP_PTAH + File.separator;
        File photoFile = new File(photoPath);
        if (!photoFile.isAbsolute()) { // 判断是否是绝对路径
            photoPath = ClassUtil.getRootPath() + "/" + photoPath;
        }
        baseDataCleanService.delPhoto(photoPath, paramValue);
    }

    @Override
    public void pushAttLateToAcc(String pin, Date attDate,String timeSlotToWorkTime) {

        attGetAccTransactionService.pushAttLateToAcc(pin,attDate, timeSlotToWorkTime);
    }

}