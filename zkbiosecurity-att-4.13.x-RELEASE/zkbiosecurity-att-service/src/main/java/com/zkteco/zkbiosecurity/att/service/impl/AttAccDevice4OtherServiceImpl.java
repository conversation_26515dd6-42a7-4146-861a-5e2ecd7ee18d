package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.List;

import com.zkteco.zkbiosecurity.att.dao.AttPointDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.acc.service.AccDevice4OtherService;
import com.zkteco.zkbiosecurity.att.service.AttPointService;
import com.zkteco.zkbiosecurity.att.vo.AttPointItem;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

/**
 * att实现acc设备通知接口
 * 
 * <AUTHOR>
 * @date 2021/4/8 13:54
 * @since 1.0.0
 */
@Service
public class AttAccDevice4OtherServiceImpl implements AccDevice4OtherService {

    @Autowired
    private AttPointDao attPointDao;
    @Autowired
    private AttPointService attPointService;

    @Override
    public void checkAccDeviceIsUsedByDevIds(String devIds) {
        AttPointItem condition = new AttPointItem();
        condition.setInDeviceId(devIds);
        List<AttPointItem> attPointItemList = attPointService.getByCondition(condition);
        if (!CollectionUtil.isEmpty(attPointItemList)) {
            throw ZKBusinessException
                .warnException(I18nUtil.i18nCode("common_prompt_canNotDel", I18nUtil.i18nCode("att_module")));
        }
    }

    @Override
    public void checkAccDeviceIsUsedByDevSn(String devSn) {
        List<AttPointItem> attPointItemList = attPointService.getItemsByDeviceSn(devSn);
        if (!CollectionUtil.isEmpty(attPointItemList)) {
            throw ZKBusinessException
                .warnException(I18nUtil.i18nCode("common_prompt_canNotDel", I18nUtil.i18nCode("att_module")));
        }
    }

    @Override
    public void modifyDeviceSn(String oldSn, String newSn) {
        attPointDao.updateDeviceSn(oldSn, newSn);
    }
}
