package com.zkteco.zkbiosecurity.att.dao;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.att.model.AttRecord;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 考勤计算结果、日明细表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:57
 * @since 1.0.0
 */
public interface AttRecordDao extends BaseDao<AttRecord, String> {

    @Modifying
    @Query(value = "delete AttRecord where PERS_PERSON_PIN in ?1 and ATT_DATE >= ?2 and ATT_DATE <= ?3")
    int deleteByPinAndDate(List<String> subPinList, Date startDate, Date endDate);

    List<AttRecord> findAttRecordByPersonPinInAndAttDateGreaterThanEqualAndAttDateLessThanEqual(
        Collection<String> personPins, Date startDatetime, Date endDatetime);

    List<AttRecord> findByDeptIdInAndAttDateGreaterThanEqualAndAttDateLessThanEqual(Collection<String> deptIds,
        Date startDatetime, Date endDatetime);

    /**
     * 旷工
     *
     * @param startDate endDate
     * @param num
     * @return
     * <AUTHOR>
     * @Date 2019/1/7 14:57
     */
    @Query(
        value = "SELECT count(DISTINCT t.PERS_PERSON_PIN) FROM ATT_RECORD t WHERE t.ATT_DATE >= ?1 AND t.ATT_DATE <= ?2 GROUP BY t.PERS_PERSON_PIN Having SUM(t.ABSENT_MINUTE) > ?3",
        nativeQuery = true)
    List<Object[]> countByAttDateAndAbsentMinute(Date startDate, Date endDate, int num);

    /**
     * 迟到
     *
     * @param startDate endDate
     * @param num
     * @return
     * <AUTHOR>
     * @Date 2019/1/7 14:57
     */
    @Query(
        value = "SELECT t.PERS_PERSON_PIN, count(DISTINCT t.PERS_PERSON_PIN) FROM ATT_RECORD t WHERE t.ATT_DATE >= ?1 AND t.ATT_DATE <= ?2 GROUP BY t.PERS_PERSON_PIN Having SUM(t.LATE_MINUTE_TOTAL) > ?3",
        nativeQuery = true)
    List<Object[]> countByAttDateAndLateMinuteTotal(Date startDate, Date endDate, int num);

    /**
     * 早退
     *
     * @param startDate endDate
     * @param num
     * @return
     * <AUTHOR>
     * @Date 2019/1/7 14:58
     */
    @Query(
        value = "SELECT t.PERS_PERSON_PIN, count(DISTINCT t.PERS_PERSON_PIN) FROM ATT_RECORD t WHERE t.ATT_DATE >= ?1 AND t.ATT_DATE <= ?2 GROUP BY t.PERS_PERSON_PIN Having SUM(t.EARLY_MINUTE_TOTAL) > ?3",
        nativeQuery = true)
    List<Object[]> countByAttDateAndEarlyMinuteTotal(Date startDate, Date endDate, int num);

    /**
     * 出差
     *
     * @param startDate endDate
     * @param num
     * @return
     * <AUTHOR>
     * @Date 2019/1/7 14:59
     */
    @Query(
        value = "SELECT t.PERS_PERSON_PIN, count(DISTINCT t.PERS_PERSON_PIN) FROM ATT_RECORD t WHERE t.ATT_DATE >= ?1 AND t.ATT_DATE <= ?2 GROUP BY t.PERS_PERSON_PIN Having SUM(t.TRIP_MINUTE) > ?3",
        nativeQuery = true)
    List<Object[]> countByAttDateAndTripMinute(Date startDate, Date endDate, int num);

    /**
     * 外出
     *
     * @param startDate endDate
     * @param num
     * @return
     * <AUTHOR>
     * @Date 2019/1/7 14:59
     */
    @Query(
        value = "SELECT t.PERS_PERSON_PIN, count(DISTINCT t.PERS_PERSON_PIN) FROM ATT_RECORD t WHERE t.ATT_DATE >= ?1 AND t.ATT_DATE <= ?2 GROUP BY t.PERS_PERSON_PIN Having SUM(t.OUT_MINUTE) > ?3",
        nativeQuery = true)
    List<Object[]> countByAttDateAndOutMinute(Date startDate, Date endDate, int num);

    /**
     * 请假
     *
     * @param startDate endDate
     * @param num
     * @return
     * <AUTHOR>
     * @Date 2019/1/7 15:00
     */
    @Query(
        value = "SELECT t.PERS_PERSON_PIN, count(DISTINCT t.PERS_PERSON_PIN) FROM ATT_RECORD t WHERE t.ATT_DATE >= ?1 AND t.ATT_DATE <= ?2 GROUP BY t.PERS_PERSON_PIN Having SUM(t.LEAVE_MINUTE) > ?3",
        nativeQuery = true)
    List<Object[]> countByAttDateAndLeaveMinute(Date startDate, Date endDate, int num);

    AttRecord findByAttDateAndPersonPin(Date stringToDate, String personPin);

    List<AttRecord> findAttRecordByPersonPinAndAttDateGreaterThanEqualAndAttDateLessThanEqual(String pin,
        Date startDate, Date endDate);

    /**
     * // 根据部门分组，获取有打卡的总人数（不需要过滤没排班，因为有效卡只有排班才有）
     *
     * @param startDate:
     * @param endDate:
     * @return java.util.List<java.lang.Object[]>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/4 13:36
     * @since 1.0.0
     */
    @Query(
        value = "SELECT t.AUTH_DEPT_ID AS deptId, COUNT(t.ID) AS counts FROM ATT_RECORD t WHERE t.ATT_DATE >= ?1 AND t.ATT_DATE <= ?2 AND t.CARD_VALID_COUNT > 0 GROUP BY t.AUTH_DEPT_ID",
        nativeQuery = true)
    List<Object[]> getHaveCardValidCountGroupByDept(Date startDate, Date endDate);

    /**
     * 根据部门分组，获取没有有打卡的总人数（需要过滤没排班）
     *
     * @param startDate:
     * @param endDate:
     * @return java.util.List<java.lang.Object[]>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/4 13:36
     * @since 1.0.0
     */
    @Query(
        value = "SELECT t.AUTH_DEPT_ID AS deptId, COUNT(t.ID) AS counts FROM ATT_RECORD t WHERE t.ATT_DATE >= ?1 AND t.ATT_DATE <= ?2 AND t.CARD_VALID_COUNT = 0 AND t.ATTENDANCE_STATUS != 'schAndRest' AND t.attendance_status != 'noSch' GROUP BY t.AUTH_DEPT_ID",
        nativeQuery = true)
    List<Object[]> getNoCardValidCountGroupByDept(Date startDate, Date endDate);

    /**
     * 根据部门分组，获取有排班的总人数
     *
     * @param startDate:
     * @param endDate:
     * @return java.util.List<java.lang.Object[]>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/4 13:36
     * @since 1.0.0
     */
    @Query(
        value = "SELECT t.AUTH_DEPT_ID AS deptId, COUNT(t.ID) AS counts FROM ATT_RECORD t WHERE t.ATT_DATE >= ?1 AND t.ATT_DATE <= ?2 AND t.ATTENDANCE_STATUS != 'schAndRest' AND t.attendance_status != 'noSch' GROUP BY t.AUTH_DEPT_ID",
        nativeQuery = true)
    List<Object[]> getSchCountGroupByDept(Date startDate, Date endDate);

    // 0、personPin/1、attDate/2、attendanceStatus/3、leaveDetails/4、shouldMinute/、5actualMinute
    // 6、validMinute/7、absentMinute/8、exceptionSchType
    @Query("SELECT t.personPin, t.attDate, t.attendanceStatus, t.leaveDetails, t.shouldMinute, t.actualMinute, t.validMinute, t.absentMinute, " +
            "t.exceptionSchType FROM AttRecord t WHERE t.personPin in ?1 AND t.attDate >= ?2 AND t.attDate <= ?3")
    List<Object[]> findByPinsAndAttDateForMonthDetail(List<String> pins, Date startDatetime, Date endDatetime);

    // 0、personPin/1、attDate/2、validMinute
    @Query("SELECT t.personPin, t.attDate, t.validMinute FROM AttRecord t WHERE t.personPin in ?1 AND t.attDate >= ?2 AND t.attDate <= ?3")
    List<Object[]> findByPinsAndAttDateForWorkTime(List<String> pins, Date startDatetime, Date endDatetime);

    // 0、personPin/1、attDate/2、leaveDetails/3、shouldMinute/、4actualMinute/ 5、validMinute/6、absentMinute
    // 7、leaveMinute/8、lateMinuteTotal/9、earlyMinuteTotal/10、overtimeUsualMinute
    // 11、overtimeRestMinute/12、overtimeHolidayMinute/13、OvertimeMinute
    @Query("SELECT t.personPin, t.attDate, t.leaveDetails, t.shouldMinute, t.actualMinute, t.validMinute, t.absentMinute, t.leaveMinute, " +
            "t.lateMinuteTotal, t.earlyMinuteTotal, t.overtimeUsualMinute, t.overtimeRestMinute, t.overtimeHolidayMinute, t.overtimeMinute, t.shouldMinuteEx" +
            " FROM AttRecord t WHERE t.personPin in ?1 AND t.attDate >= ?2 AND t.attDate <= ?3")
    List<Object[]> findByPinsAndAttDateForMonthStatistical(List<String> pins, Date startDatetime, Date endDatetime);

    // 0、personPin/1、attDate/2、leaveDetails/3、shouldMinute/、4actualMinute/ 5、validMinute/6、absentMinute
    // 7、leaveMinute/8、lateMinuteTotal/9、earlyMinuteTotal/10、overtimeUsualMinute
    // 11、overtimeRestMinute/12、overtimeHolidayMinute/13、OvertimeMinute
    @Query("SELECT t.deptId, t.attDate, t.leaveDetails, t.shouldMinute, t.actualMinute, t.validMinute, t.absentMinute, t.leaveMinute, " +
            "t.lateMinuteTotal, t.earlyMinuteTotal, t.overtimeUsualMinute, t.overtimeRestMinute, t.overtimeHolidayMinute, t.overtimeMinute, t.shouldMinuteEx" +
            " FROM AttRecord t WHERE t.deptId in ?1 AND t.attDate >= ?2 AND t.attDate <= ?3")
    List<Object[]> findByDeptIdsAndAttDateForMonthStatistical(List<String> deptIds, Date startDatetime, Date endDatetime);
}