package com.zkteco.zkbiosecurity.att.dao;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.att.model.AttLeave;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 对应百傲瑞达 AttLeaveDao
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-08 下午08:27
 */
public interface AttLeaveDao extends BaseDao<AttLeave, String> {

    /**
     * 根据人员编号 开始时间 结束时间 获取对应请假对象
     *
     * @param personPin
     *            人员编号
     * @param startDatetime
     *            开始时间
     * @param endDatetime
     *            结束时间
     * @return com.zkteco.zkbiosecurity.att.model.AttLeave
     */
    @Query(value = "SELECT t FROM AttLeave t WHERE t.personPin = ?1 AND (t.startDatetime < ?3 AND t.endDatetime > ?2)")
    AttLeave getByPersonPinStartDateAndEndDate(String personPin, Date startDatetime, Date endDatetime);

    /**
     * 根据人员集合查找时间范围内的所有有效请假申请
     *
     * @param personIdList
     * @param startTime
     * @param endTime
     * @return
     */
    @Query(
        value = "SELECT t FROM AttLeave t WHERE t.personId IN (?1) AND (t.startDatetime < ?3 AND t.endDatetime > ?2) AND t.flowStatus IN ('0','2')")
    List<AttLeave> getByPersonIdsAndStartEndTime(List<String> personIdList, Date startTime, Date endTime);

    /**
     * 根据人员pin查找时间范围内的所有有效请假申请
     *
     * @param pin
     * @param startTime
     * @param endTime
     * @return
     */
    @Query(
        value = "SELECT t FROM AttLeave t WHERE t.personPin IN (?1) AND (t.startDatetime < ?3 AND t.endDatetime > ?2) AND t.flowStatus IN ('0','2')")
    List<AttLeave> getByPinAndStartEndTime(String pin, Date startTime, Date endTime);

    /**
     * 通过人员ID、假种、日期、流程状态来统计请假次数
     *
     * @param personIdList
     * @param startTime
     * @param endTime
     * @return
     */
    @Query(
        value = "SELECT t.personPin,t.leaveTypeId,count(t.leaveTypeId),sum(t.leaveLong),sum(t.days) FROM AttLeave t WHERE t.personPin IN (?1) AND (t.startDatetime < ?3 AND t.endDatetime > ?2) AND t.flowStatus = '2' group by t.personPin,t.leaveTypeId")
    List<Object[]> getCountByPersonIdsAndDate(List<String> personIdList, Date startTime, Date endTime);

    /**
     * 通过人员编号、假种、日期、流程状态来查找请假列表
     *
     * @param personPin
     * @param leaveTypeId
     * @param startTime
     * @return
     */
    @Query(
        value = "SELECT t FROM AttLeave t WHERE t.personPin IN (?1) AND t.leaveTypeId = ?2 AND (t.startDatetime <= ?4) AND (t.endDatetime >= ?3) AND t.flowStatus = '2' ")
    List<AttLeave> getByPersonPinAndLeaveTypeIdAndDate(String personPin, String leaveTypeId, Date startTime,
        Date endTime);

    /**
     * 判断 假种是否有外键关联
     *
     * @param id
     * @return boolean
     * @date 2018/6/28 21:06
     */
    boolean existsByLeaveTypeId(String id);

    /**
     * 根据businessKey查找申请
     *
     * @param businessKey
     * @return
     */
    AttLeave findByBusinessKey(String businessKey);

    /**
     * 获取时间段内指定人员是否已有请假申请
     *
     * @param personIdList
     *            人员id
     * @param endDateTime
     *            结束日期时间
     * @param startDateTime
     *            开始日期时间
     * @return java.util.List<com.zkteco.zkbiosecurity.att.model.AttLeave> 存在已申请请假
     */
    @Query(
        value = "SELECT t FROM AttLeave t WHERE t.personId IN (?1) AND (t.endDatetime >= ?2 AND t.startDatetime <= ?3) AND t.flowStatus IN ('0','2')")
    List<AttLeave> getExistByPersonIdsAndStartTimeAndEndTime(List<String> personIdList, Date endDateTime,
        Date startDateTime);

    /**
     * 查找时间范围内流程结束的请假申请
     *
     * @param startDate
     * @param endDate
     * @return
     */
    @Query("SELECT t FROM AttLeave t WHERE t.flowStatus='2' AND t.endDatetime >= ?1 AND t.startDatetime <= ?2")
    List<AttLeave> findLeaveByDate(Date startDate, Date endDate);

    /**
     * 根据pin集合查找时间范围内流程结束的请假申请
     *
     * @param startDate
     * @param endDate
     * @param pins
     * @return
     */
    @Query("SELECT t FROM AttLeave t WHERE t.flowStatus='2' AND t.endDatetime >= ?1 AND t.startDatetime <= ?2 AND t.personPin IN (?3)")
    List<AttLeave> findLeaveByDateAndPins(Date startDate, Date endDate, List<String> pins);

    /**
     * 根据pin查找时间范围内流程结束的请假申请
     *
     * @param startDate
     * @param endDate
     * @param personPin
     * @return
     */
    @Query("SELECT t FROM AttLeave t WHERE t.flowStatus='2' AND t.endDatetime >= ?1 AND t.startDatetime <= ?2 AND t.personPin = ?3")
    List<AttLeave> findLeaveByDateAndPin(Date startDate, Date endDate, String personPin);

    /**
     * 根据人员编号、开始时间、结束时间、流程状态查找请假列表(flowStatus 0,2表示审批中和结束)
     *
     * @param personPin
     * @param startTime
     * @param endTime
     * @return
     */
    @Query(
        value = "SELECT t FROM AttLeave t WHERE t.personPin = ?1 AND t.endDatetime > ?2 AND t.startDatetime < ?3 AND (t.flowStatus='2' OR t.flowStatus='0')")
    List<AttLeave> getListByPinAndDateAndValidStatus(String personPin, Date startTime, Date endTime);

    /**
     * 查找所有已通过审批，且请假结束时间大于或等于指定日期的请假申请
     *
     * @param flowStatus
     * @param startDate
     *            有效开始日期
     * @return
     * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
     */
    List<AttLeave> findByFlowStatusAndEndDatetimeGreaterThanEqual(String flowStatus, Date startDate);

    /**
     * 最早请假记录
     *
     * @param beginDate
     * @param endDate
     * @param attPersPins
     * @return
     * <AUTHOR>
     * @since 2020年4月29日上午11:33:34
     */
    @Query("SELECT t.personPin,MIN(t.startDatetime) AS startDatetime,t.endDatetime, t.leaveTypeId FROM AttLeave t WHERE t.endDatetime >= ?1 AND t.startDatetime <= ?2 AND t.personPin IN ?3 group by t.leaveTypeId,t.personPin,t.endDatetime")
    List<Object[]> getItemByTimeAndPersPins(Date beginDate, Date endDate, Collection<String> attPersPins);

    @Modifying
    @Query("update AttLeave t set t.flowStatus=?2 where t.id in (?1)")
    void updateFlowStatus(List<String> ids, String flowStatus);

    @Query(
        value = "SELECT t FROM AttLeave t WHERE t.personPin IN (?1) AND t.leaveTypeId = ?2 AND (t.startDatetime <= ?4) AND (t.endDatetime >= ?3) AND t.flowStatus IN ('0','2') ")
    List<AttLeave> findByPersonPinAndLeaveTypeIdAndDate(Collection<String> personPinList, String leaveTypeId,
        Date startTime, Date endTime);

    @Query(
        value = "SELECT t FROM AttLeave t WHERE t.personPin IN (?1) AND t.leaveTypeId = ?2 AND (t.startDatetime <= ?4) AND (t.endDatetime >= ?3)")
    List<AttLeave> findByPinAndTypeIdAndDate(Collection<String> personPinList, String leaveTypeId, Date startTime,
        Date endTime);

    @Query(
            value = "SELECT SUM(t.days) FROM AttLeave t WHERE t.startDatetime >= ?1 and t.personPin = ?2")
    Float findTotalByYearAndPin(Date firstDayOfYear, String pin);

    @Query(
            value = "SELECT SUM(t.days) FROM AttLeave t WHERE t.startDatetime >= ?1 and t.endDatetime <= ?2 and t.personPin = ?3")
    Float findTotalByDateBetweenAndPin(Date startDatetime, Date endDatetime, String pin);
}