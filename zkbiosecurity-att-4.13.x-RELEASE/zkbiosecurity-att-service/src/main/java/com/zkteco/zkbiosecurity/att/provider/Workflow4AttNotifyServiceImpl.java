package com.zkteco.zkbiosecurity.att.provider;

import java.util.Date;
import java.util.List;
import java.util.Objects;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.cache.AttCacheManager;
import com.zkteco.zkbiosecurity.att.cache.AttCalculationCacheManager;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.constants.AttShiftConstant;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveItem;
import com.zkteco.zkbiosecurity.att.vo.AttOvertimeItem;
import com.zkteco.zkbiosecurity.att.vo.AttSignItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.system.service.BaseMessageService;
import com.zkteco.zkbiosecurity.system.vo.BaseMessageItem;
import com.zkteco.zkbiosecurity.workflow.service.Workflow4OtherNotifyService;
import com.zkteco.zkbiosecurity.workflow.vo.WorkflowNotifyItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 流程通知
 * 
 * <AUTHOR>
 * @date 2019/5/14
 *
 */
@Service(value = "workflow4AttNotifyServiceImpl")
@Slf4j
public class Workflow4AttNotifyServiceImpl implements Workflow4OtherNotifyService {
    @Autowired
    private AttSignService attSignService;
    @Autowired
    private AttLeaveService attLeaveService;
    @Autowired
    private AttOvertimeService attOvertimeService;

    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AttRecordService attRecordService;
    @Autowired
    private BaseMessageService baseMessageService;
    @Autowired
    private AttCacheManager attCacheManager;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttCalculationCacheManager attCalculationCacheManager;
    @Autowired
    private AttMessageCenterService attMessageCenterService;

    @Override
    public void workflowApplyEndNotify(WorkflowNotifyItem workflowNotifyItem) {
        log.info("ATT WorkflowApplyEndNotify WorkflowNotifyItem = {}", workflowNotifyItem);

        if (Objects.isNull(workflowNotifyItem)) {
            return;
        }
        String businessKey = workflowNotifyItem.getBusinessKey();
        if (StringUtils.isBlank(businessKey)) {
            return;
        }
        String processDefinitionKey = workflowNotifyItem.getProcessDefinitionKey();
        String formRecordId = MapUtils.getString(workflowNotifyItem.getTransientVariables(), "formRecordId");
        if (StringUtils.isNotBlank(formRecordId)) {
            // 更新业务表单的businessKey
            if ("sign".equalsIgnoreCase(processDefinitionKey)) {
                attSignService.updateBusinessKeyById(formRecordId, businessKey);
            } else if ("leave".equals(processDefinitionKey)) {
                attLeaveService.updateBusinessKeyById(formRecordId, businessKey);
            } else if ("overtime".equals(processDefinitionKey)) {
                attOvertimeService.updateBusinessKeyById(formRecordId, businessKey);
            } else if ("trip".equals(processDefinitionKey)) {
                attLeaveService.updateBusinessKeyById(formRecordId, businessKey);
            } else if ("out".equals(processDefinitionKey)) {
                attLeaveService.updateBusinessKeyById(formRecordId, businessKey);
            }
        }
    }

    @Override
    public void workflowApprovalCreateNotify(WorkflowNotifyItem workflowNotifyItem) {
        log.info("ATT workflowApprovalCreateNotify WorkflowNotifyItem = {}", workflowNotifyItem);

        if (Objects.isNull(workflowNotifyItem)) {
            return;
        }
        if (StringUtils.isBlank(workflowNotifyItem.getBusinessKey())) {
            return;
        }

        String processDefinitionKey = workflowNotifyItem.getProcessDefinitionKey();
        if ("sign".equalsIgnoreCase(processDefinitionKey)) {
            signApprovalCreateNotify(workflowNotifyItem);
        } else if ("leave".equals(processDefinitionKey)) {
            leaveApprovalCreateNotify(workflowNotifyItem);
        } else if ("overtime".equals(processDefinitionKey)) {
            overtimeApprovalCreateNotify(workflowNotifyItem);
        } else if ("trip".equals(processDefinitionKey)) {
            leaveApprovalCreateNotify(workflowNotifyItem);
        } else if ("out".equals(processDefinitionKey)) {
            leaveApprovalCreateNotify(workflowNotifyItem);
        }
    }

    @Override
    public void workflowApprovalEndNotify(WorkflowNotifyItem workflowNotifyItem) {
        // log.info("ATT workflowApprovalEndNotify WorkflowNotifyItem = {}", workflowNotifyItem);
    }

    @Override
    public void workflowFinishNotify(WorkflowNotifyItem workflowNotifyItem) {
        log.info("ATT workflowFinishNotify WorkflowNotifyItem = {}", workflowNotifyItem);

        if (Objects.isNull(workflowNotifyItem)) {
            return;
        }
        String processDefinitionKey = workflowNotifyItem.getProcessDefinitionKey();
        if ("sign".equals(processDefinitionKey)) {
            signFinishNotify(workflowNotifyItem);
        } else if ("leave".equals(processDefinitionKey)) {
            leaveFinishNotify(workflowNotifyItem);
        } else if ("overtime".equals(processDefinitionKey)) {
            overtimeFinishNotify(workflowNotifyItem);
        } else if ("trip".equals(processDefinitionKey)) {
            leaveFinishNotify(workflowNotifyItem);
        } else if ("out".equals(processDefinitionKey)) {
            leaveFinishNotify(workflowNotifyItem);
        }
    }

    private void signApprovalCreateNotify(WorkflowNotifyItem workflowNotifyItem) {

        AttSignItem attSignItem = attSignService.getByBusinessKey(workflowNotifyItem.getBusinessKey());
        if (Objects.isNull(attSignItem)) {
            return;
        }

        String businessId = workflowNotifyItem.getTaskId();
        attSignItem.setTaskId(businessId);
        String businessCode = "AttSign";
        String title = I18nUtil.i18nCode("att_flowable_todomsg_sign");
        String type = "TodoMsg";
        String status = "0";
        Date remindTime = new Date();

        String personPin = attSignItem.getPersonPin();
        String personName = attSignItem.getPersonName();
        // 班次
        String shiftName = attSignService.getSignShiftName(attSignItem);

        // 待办消息
        String approvalUserIds = workflowNotifyItem.getApprovalUserIds();
        if (StringUtils.isNotBlank(approvalUserIds)) {
            List<PersPersonItem> approvalPersonList =
                persPersonService.getSimpleItemsByIds(CollectionUtil.strToList(approvalUserIds));
            if (!CollectionUtil.isEmpty(approvalPersonList)) {

                JSONObject extendJson = new JSONObject();
                extendJson.put("personPin", personPin);
                extendJson.put("flowStatus", attSignItem.getFlowStatus());
                extendJson.put("personInfo",
                    StringUtils.isNotBlank(personName) ? personName : "" + "(" + personPin + ")");
                extendJson.put("photoPath", "");
                extendJson.put("todoContent1", I18nUtil.i18nCode("att_flowable_shift") + shiftName);
                extendJson.put("todoContent2", AttDateUtils.dateToStrAsMedium(attSignItem.getSignDatetime()));

                for (PersPersonItem persPersonItem : approvalPersonList) {
                    buildMessage(persPersonItem.getPin(), businessId, businessCode, type, title, status, remindTime,
                        extendJson.toJSONString());
                    // 推送补签待办消息
                    attMessageCenterService.pushSignTodoMessage(persPersonItem.getPin(), attSignItem);
                }
            }
        }
    }

    private void leaveApprovalCreateNotify(WorkflowNotifyItem workflowNotifyItem) {
        AttLeaveItem attLeaveItem = attLeaveService.getByBusinessKey(workflowNotifyItem.getBusinessKey());
        if (Objects.isNull(attLeaveItem)) {
            return;
        }

        String businessId = workflowNotifyItem.getTaskId();
        attLeaveItem.setTaskId(businessId);
        String businessCode = "AttLeave";
        String title = I18nUtil.i18nCode("att_flowable_todomsg_leave");
        if (AttConstant.FLOW_TYPE_TRIP.equals(attLeaveItem.getLeaveTypeNo())) {
            businessCode = "AttTrip";
            title = I18nUtil.i18nCode("att_flowable_todomsg_trip");
        } else if (AttConstant.FLOW_TYPE_OUT.equals(attLeaveItem.getLeaveTypeNo())) {
            businessCode = "AttOut";
            title = I18nUtil.i18nCode("att_flowable_todomsg_out");
        }
        String type = "TodoMsg";
        String status = "0";
        Date remindTime = new Date();

        String personPin = attLeaveItem.getPersonPin();
        String personName = attLeaveItem.getPersonName();
        String leaveTypeName = attLeaveItem.getLeaveTypeName();
        String startDatetime = AttDateUtils.dateToStrAsMedium(attLeaveItem.getStartDatetime());
        String endDatetime = AttDateUtils.dateToStrAsMedium(attLeaveItem.getEndDatetime());

        ZKResultMsg zkResultMsg = attLeaveService.getLeaveLongByType(attLeaveItem.getPersonPin(),
            attLeaveItem.getStartDatetime(), attLeaveItem.getEndDatetime(), attLeaveItem.getLeaveTypeNo());
        JSONObject jsonObject = (JSONObject)zkResultMsg.getData();

        // 待办消息
        String approvalUserIds = workflowNotifyItem.getApprovalUserIds();
        if (StringUtils.isNotBlank(approvalUserIds)) {
            List<PersPersonItem> approvalPersonList =
                persPersonService.getSimpleItemsByIds(CollectionUtil.strToList(approvalUserIds));
            if (!CollectionUtil.isEmpty(approvalPersonList)) {

                JSONObject extendJson = new JSONObject();
                extendJson.put("personPin", personPin);
                extendJson.put("flowStatus", AttConstant.FLOW_STATUS_CREATE);
                extendJson.put("personInfo",
                    StringUtils.isNotBlank(personName) ? personName : "" + " (" + personPin + ")");
                extendJson.put("photoPath", "");
                extendJson.put("todoContent1",
                    String.format("%s: %s%s", leaveTypeName, jsonObject.getString("timeLong"), jsonObject.get("unit")));
                extendJson.put("todoContent2", String.format("%s %s %s", startDatetime,
                    I18nUtil.i18nCode("att_flowable_datetime_to"), endDatetime));

                for (PersPersonItem persPersonItem : approvalPersonList) {
                    buildMessage(persPersonItem.getPin(), businessId, businessCode, type, title, status, remindTime,
                        extendJson.toJSONString());

                    // 推送请假待办消息
                    attMessageCenterService.pushLeaveTodoMessage(persPersonItem.getPin(), businessCode, attLeaveItem);
                }
            }
        }

    }

    private void overtimeApprovalCreateNotify(WorkflowNotifyItem workflowNotifyItem) {
        AttOvertimeItem attOvertimeItem = attOvertimeService.getByBusinessKey(workflowNotifyItem.getBusinessKey());
        if (Objects.isNull(attOvertimeItem)) {
            return;
        }

        String businessId = workflowNotifyItem.getTaskId();
        attOvertimeItem.setTaskId(businessId);
        String businessCode = "AttOvertime";
        String title = I18nUtil.i18nCode("att_flowable_todomsg_overtime");
        String type = "TodoMsg";
        String status = "0";
        Date remindTime = new Date();
        String personPin = attOvertimeItem.getPersonPin();
        String personName = attOvertimeItem.getPersonName();

        String overtimeType = AttCommonUtils.overtimeSignName(attOvertimeItem.getOvertimeSign());
        String startDatetime = AttDateUtils.dateToStrAsMedium(attOvertimeItem.getStartDatetime());
        String endDatetime = AttDateUtils.dateToStrAsMedium(attOvertimeItem.getEndDatetime());

        // 根据配置计算加班时长带单位
        ZKResultMsg zkResultMsg = attLeaveService.getLeaveLongByType(attOvertimeItem.getPersonPin(),
            attOvertimeItem.getStartDatetime(), attOvertimeItem.getEndDatetime(), AttConstant.FLOW_TYPE_OVERTIME);
        JSONObject jsonObject = (JSONObject)zkResultMsg.getData();

        // 待办消息
        String approvalUserIds = workflowNotifyItem.getApprovalUserIds();
        if (StringUtils.isNotBlank(approvalUserIds)) {
            List<PersPersonItem> approvalPersonList =
                persPersonService.getSimpleItemsByIds(CollectionUtil.strToList(approvalUserIds));
            if (!CollectionUtil.isEmpty(approvalPersonList)) {

                JSONObject extendJson = new JSONObject();
                extendJson.put("personPin", personPin);
                extendJson.put("flowStatus", attOvertimeItem.getFlowStatus());
                extendJson.put("personInfo",
                    StringUtils.isNotBlank(personName) ? personName : "" + " (" + personPin + ")");
                extendJson.put("photoPath", "");
                extendJson.put("todoContent1",
                    String.format("%s : %s%s", overtimeType, jsonObject.getString("timeLong"), jsonObject.get("unit")));
                extendJson.put("todoContent2", String.format("%s %s %s", startDatetime,
                    I18nUtil.i18nCode("att_flowable_datetime_to"), endDatetime));

                for (PersPersonItem persPersonItem : approvalPersonList) {
                    buildMessage(persPersonItem.getPin(), businessId, businessCode, type, title, status, remindTime,
                        extendJson.toJSONString());

                    // 推送加班待办消息
                    attMessageCenterService.pushOvertimeTodoMessage(persPersonItem.getPin(), attOvertimeItem);
                }
            }
        }
    }

    private void signFinishNotify(WorkflowNotifyItem workflowNotifyItem) {

        AttSignItem attSignItem = attSignService.getByBusinessKey(workflowNotifyItem.getBusinessKey());
        if (Objects.isNull(attSignItem)) {
            return;
        }
        attSignItem
            .setFlowStatus(switchFlowStatus(attSignItem.getFlowStatus(), workflowNotifyItem.getApprovalStatus()));
        attSignService.updateFlowStatus(workflowNotifyItem.getBusinessKey(), attSignItem.getFlowStatus());

        String businessCode = "AttSign";
        String title = I18nUtil.i18nCode("att_flowable_notifymsg_sign");
        String type = "NotifyMsg";
        String status = "0";
        Date remindTime = new Date();

        String personPin = attSignItem.getPersonPin();
        String personName = attSignItem.getPersonName();
        // 班次
        String shiftName = attSignService.getSignShiftName(attSignItem);

        JSONObject extendJson = new JSONObject();
        extendJson.put("personPin", personPin);
        extendJson.put("flowStatus", attSignItem.getFlowStatus());
        extendJson.put("personInfo", StringUtils.isNotBlank(personName) ? personName : "" + " (" + personPin + ")");
        extendJson.put("photoPath", "");
        extendJson.put("notifyContent1", I18nUtil.i18nCode("att_flowable_shift") + shiftName);
        extendJson.put("notifyContent2", AttDateUtils.dateToStrAsMedium(attSignItem.getSignDatetime()));

        String receiverId = personPin;
        String businessId = workflowNotifyItem.getBusinessKey();
        attSignItem.setTaskId(businessId);

        // 流程结束发送知会消息给申请人
        buildMessage(receiverId, businessId, businessCode, type, title, status, remindTime, extendJson.toJSONString());
        // 推送补签知会消息
        attMessageCenterService.pushSignNotifyMessage(attSignItem.getPersonPin(), attSignItem);

        // 流程结束发送知会消息给知会人
        String notifierUserIds = workflowNotifyItem.getNotifierUserIds();
        if (StringUtils.isNotBlank(notifierUserIds)) {
            List<PersPersonItem> notifyPersonList =
                    persPersonService.getSimpleItemsByIds(CollectionUtil.strToList(notifierUserIds));
            if (!CollectionUtil.isEmpty(notifyPersonList)) {
                for (PersPersonItem personItem : notifyPersonList) {
                    buildMessage(personItem.getPin(), businessId, businessCode, type, title, status, remindTime,
                            extendJson.toJSONString());
                    // 推送补签知会消息
                    attMessageCenterService.pushSignNotifyMessage(personItem.getPin(), attSignItem);
                }
            }
        }

        addAttRecordEvent(attSignItem.getPersonPin(), attSignItem.getSignDatetime(), attSignItem.getSignDatetime());
    }

    private void leaveFinishNotify(WorkflowNotifyItem workflowNotifyItem) {

        AttLeaveItem attLeaveItem = attLeaveService.getByBusinessKey(workflowNotifyItem.getBusinessKey());
        if (Objects.isNull(attLeaveItem)) {
            return;
        }
        attLeaveItem
            .setFlowStatus(switchFlowStatus(attLeaveItem.getFlowStatus(), workflowNotifyItem.getApprovalStatus()));
        attLeaveService.updateFlowStatus(workflowNotifyItem.getBusinessKey(), attLeaveItem.getFlowStatus());

        String personPin = attLeaveItem.getPersonPin();
        String personName = attLeaveItem.getPersonName();
        String leaveTypeName = attLeaveItem.getLeaveTypeName();
        String startDatetime = AttDateUtils.dateToStrAsMedium(attLeaveItem.getStartDatetime());
        String endDatetime = AttDateUtils.dateToStrAsMedium(attLeaveItem.getEndDatetime());

        ZKResultMsg zkResultMsg = attLeaveService.getLeaveLongByType(attLeaveItem.getPersonPin(),
            attLeaveItem.getStartDatetime(), attLeaveItem.getEndDatetime(), attLeaveItem.getLeaveTypeNo());
        JSONObject jsonObject = (JSONObject)zkResultMsg.getData();

        String businessCode = "AttLeave";
        String type = "NotifyMsg";
        String title = I18nUtil.i18nCode("att_flowable_notifymsg_leave");
        if (AttConstant.FLOW_TYPE_TRIP.equals(attLeaveItem.getLeaveTypeNo())) {
            businessCode = "AttTrip";
            title = I18nUtil.i18nCode("att_flowable_notifymsg_trip");
        } else if (AttConstant.FLOW_TYPE_OUT.equals(attLeaveItem.getLeaveTypeNo())) {
            businessCode = "AttOut";
            title = I18nUtil.i18nCode("att_flowable_notifymsg_out");
        }
        String status = "0";
        Date remindTime = new Date();
        JSONObject extendJson = new JSONObject();
        extendJson.put("personPin", personPin);
        extendJson.put("flowStatus", attLeaveItem.getFlowStatus());
        extendJson.put("personInfo", StringUtils.isNotBlank(personName) ? personName : "" + " (" + personPin + ")");
        extendJson.put("photoPath", "");
        extendJson.put("notifyContent1",
            String.format("%s: %s%s", leaveTypeName, jsonObject.getString("timeLong"), jsonObject.get("unit")));
        extendJson.put("notifyContent2", startDatetime + I18nUtil.i18nCode("att_flowable_datetime_to") + endDatetime);
        String receiverId = personPin;
        String businessId = workflowNotifyItem.getBusinessKey();
        attLeaveItem.setTaskId(businessId);

        // 流程结束发送知会消息给申请人
        buildMessage(receiverId, businessId, businessCode, type, title, status, remindTime, extendJson.toJSONString());
        // 推送请假知会消息
        attMessageCenterService.pushLeaveNotifyMessage(attLeaveItem.getPersonPin(), businessCode, attLeaveItem);
        // 流程结束发送知会消息给知会人
        String notifierUserIds = workflowNotifyItem.getNotifierUserIds();
        if (StringUtils.isNotBlank(notifierUserIds)) {
            List<PersPersonItem> notifyPersonList =
                    persPersonService.getSimpleItemsByIds(CollectionUtil.strToList(notifierUserIds));
            if (!CollectionUtil.isEmpty(notifyPersonList)) {
                for (PersPersonItem personItem : notifyPersonList) {
                    buildMessage(personItem.getPin(), businessId, businessCode, type, title, status, remindTime,
                            extendJson.toJSONString());
                    // 推送请假知会消息
                    attMessageCenterService.pushLeaveNotifyMessage(personItem.getPin(), businessCode, attLeaveItem);
                }
            }
        }

        addAttRecordEvent(attLeaveItem.getPersonPin(), attLeaveItem.getStartDatetime(), attLeaveItem.getEndDatetime());
    }

    private void overtimeFinishNotify(WorkflowNotifyItem workflowNotifyItem) {

        AttOvertimeItem attOvertimeItem = attOvertimeService.getByBusinessKey(workflowNotifyItem.getBusinessKey());
        if (Objects.isNull(attOvertimeItem)) {
            return;
        }
        // 更新流程状态
        attOvertimeItem
            .setFlowStatus(switchFlowStatus(attOvertimeItem.getFlowStatus(), workflowNotifyItem.getApprovalStatus()));
        attOvertimeService.updateFlowStatus(workflowNotifyItem.getBusinessKey(), attOvertimeItem.getFlowStatus());

        // 发送知会消息给申请人
        String applyUserId = workflowNotifyItem.getApplyUserId();
        PersPersonItem persPersonItem = persPersonService.getSimpleItemById(applyUserId);
        if (persPersonItem != null) {
            String receiverId = persPersonItem.getPin();
            String businessId = attOvertimeItem.getBusinessKey();
            attOvertimeItem.setTaskId(businessId);
            String businessCode = "AttOvertime";
            String title = I18nUtil.i18nCode("att_flowable_notifymsg_overtime");
            String type = "NotifyMsg";
            String status = "0";
            Date remindTime = new Date();
            JSONObject extendJson = new JSONObject();

            String personPin = attOvertimeItem.getPersonPin();
            String personName = attOvertimeItem.getPersonName();
            Short overtimeSign = attOvertimeItem.getOvertimeSign();
            String overtimeType = "";
            switch (overtimeSign) {
                case AttShiftConstant.OvertimeSign.NORMAL:
                    overtimeType = I18nUtil.i18nCode("att_overtime_normal");
                    break;
                case AttShiftConstant.OvertimeSign.REST:
                    overtimeType = I18nUtil.i18nCode("att_overtime_rest");
                    break;
                case AttShiftConstant.OvertimeSign.HOLIDAY:
                    overtimeType = I18nUtil.i18nCode("att_shift_holidayOt");
                    break;
                default:
            }

            String startDatetime = AttDateUtils.dateToStrAsMedium(attOvertimeItem.getStartDatetime());
            String endDatetime = AttDateUtils.dateToStrAsMedium(attOvertimeItem.getEndDatetime());

            // 根据配置计算加班时长带单位
            ZKResultMsg zkResultMsg = attLeaveService.getLeaveLongByType(attOvertimeItem.getPersonPin(),
                attOvertimeItem.getStartDatetime(), attOvertimeItem.getEndDatetime(), AttConstant.FLOW_TYPE_OVERTIME);
            JSONObject jsonObject = (JSONObject)zkResultMsg.getData();

            extendJson.put("personPin", personPin);
            extendJson.put("flowStatus", attOvertimeItem.getFlowStatus());
            extendJson.put("personInfo", StringUtils.isNotBlank(personName) ? personName : "" + " (" + personPin + ")");
            extendJson.put("photoPath", "");
            extendJson.put("notifyContent1",
                String.format("%s : %s%s", overtimeType, jsonObject.getString("timeLong"), jsonObject.get("unit")));
            extendJson.put("notifyContent2",
                String.format("%s %s %s", startDatetime, I18nUtil.i18nCode("att_flowable_datetime_to"), endDatetime));

            // 流程结束发送知会消息给申请人
            buildMessage(receiverId, businessId, businessCode, type, title, status, remindTime,
                extendJson.toJSONString());
            // 推送加班知会消息
            attMessageCenterService.pushOvertimeNotifyMessage(persPersonItem.getPin(), attOvertimeItem);
            // 流程结束发送知会消息给知会人
            String notifierUserIds = workflowNotifyItem.getNotifierUserIds();
            if (StringUtils.isNotBlank(notifierUserIds)) {
                List<PersPersonItem> notifyPersonList =
                        persPersonService.getSimpleItemsByIds(CollectionUtil.strToList(notifierUserIds));
                if (!CollectionUtil.isEmpty(notifyPersonList)) {
                    for (PersPersonItem personItem : notifyPersonList) {
                        buildMessage(personItem.getPin(), businessId, businessCode, type, title, status, remindTime,
                                extendJson.toJSONString());
                        // 推送加班知会消息
                        attMessageCenterService.pushOvertimeNotifyMessage(personItem.getPin(), attOvertimeItem);
                    }
                }
            }

            addAttRecordEvent(attOvertimeItem.getPersonPin(), attOvertimeItem.getStartDatetime(),
                attOvertimeItem.getEndDatetime());
        }
    }

    /**
     * 组装消息
     */
    private void buildMessage(String receiverId, String businessId, String businessCode, String type, String title,
        String status, Date remindTime, String extendJson) {
        BaseMessageItem baseMessageItem = new BaseMessageItem();
        baseMessageItem.setReceiverId(receiverId);
        baseMessageItem.setBusinessId(businessId);
        baseMessageItem.setBusinessCode(businessCode);
        baseMessageItem.setType(type);
        baseMessageItem.setTitle(title);
        baseMessageItem.setStatus(status);
        baseMessageItem.setRemindTime(remindTime);
        baseMessageItem.setExtendJson(extendJson);
        baseMessageService.saveAndSendToCloud(baseMessageItem);
    }

    /**
     * 转换流程状态为考勤业务表单状态
     * 
     * @param originStatus
     * @param approvalStatus
     * @return
     */
    private String switchFlowStatus(String originStatus, String approvalStatus) {
        if (null == approvalStatus) {
            return null;
        }
        String flowStatus = null;
        switch (approvalStatus) {
            case "0":
            case "true":
                // 审批直接通过
                flowStatus = AttConstant.FLOW_STATUS_COMPLETE;
                break;
            case "2":
            case "false":
                // 审批过程中被驳回
                flowStatus = AttConstant.FLOW_STATUS_REFUSE;
                break;
            case "3":
                // 审批过程中或者审批通过后，撤销成功
                flowStatus = AttConstant.FLOW_STATUS_REVOKE;
                break;
            case "4":
                if (AttConstant.FLOW_STATUS_COMPLETE.equals(originStatus)) {
                    // 审批通过后，撤销，被驳回
                    flowStatus = AttConstant.FLOW_STATUS_COMPLETE;
                } else {
                    // 审批过程中，撤销，被驳回
                    flowStatus = AttConstant.FLOW_STATUS_REFUSE;
                }
                break;
            // 异常结束
            case "5":
                flowStatus = AttConstant.FLOW_STATUS_ABNORMAL_END;
                break;
            default:
                break;
        }
        return flowStatus;
    }

    /**
     * 知会消息
     * 
     * @param notifierUserIds
     *            知会人Id集合
     * @param businessId
     * @param businessCode
     * @param type
     * @param title
     * @param status
     * @param remindTime
     * @param extendJson
     */
    private void buildNotifyMessage(String notifierUserIds, String businessId, String businessCode, String type,
        String title, String status, Date remindTime, String extendJson) {
        if (StringUtils.isNotBlank(notifierUserIds)) {
            List<PersPersonItem> notifyPersonList =
                persPersonService.getSimpleItemsByIds(CollectionUtil.strToList(notifierUserIds));
            if (!CollectionUtil.isEmpty(notifyPersonList)) {
                for (PersPersonItem personItem : notifyPersonList) {
                    buildMessage(personItem.getPin(), businessId, businessCode, type, title, status, remindTime,
                        extendJson);
                }
            }
        }
    }

    /**
     * 异常申请添加记录缓存待考勤计算
     * 
     * @param pin
     * @param start
     * @param end
     */
    private void addAttRecordEvent(String pin, Date start, Date end) {
        Date date = DateUtil.getDayBeginTime(start);
        String crossDay = attParamService.getCrossDay();
        while (date.getTime() <= end.getTime()) {
            attCalculationCacheManager.addRealTimeEvent(pin, date, crossDay);
            date = DateUtil.addDay(date, 1);
        }
    }

}
