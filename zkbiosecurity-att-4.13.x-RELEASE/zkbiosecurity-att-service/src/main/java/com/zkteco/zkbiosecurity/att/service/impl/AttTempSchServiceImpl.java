package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.model.*;
import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.bean.AttDXCalendarEventBean;
import com.zkteco.zkbiosecurity.att.bean.AttRuleParamBean;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttTimeSlotBO;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttCommonSchConstant;
import com.zkteco.zkbiosecurity.att.constants.AttShiftConstant;
import com.zkteco.zkbiosecurity.att.dao.AttGroupDao;
import com.zkteco.zkbiosecurity.att.dao.AttShiftDao;
import com.zkteco.zkbiosecurity.att.dao.AttTempSchDao;
import com.zkteco.zkbiosecurity.att.dao.AttTimeSlotDao;
import com.zkteco.zkbiosecurity.att.service.AttTempSchService;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.utils.AttShiftSchUtils;
import com.zkteco.zkbiosecurity.att.utils.ProcessBeanUtil;
import com.zkteco.zkbiosecurity.att.vo.AttTempSchItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.service.PersLeavePersonService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersLeavePersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 临时排班（人员、部门、分组）
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:19
 * @since 1.0.0
 */
@Service
public class AttTempSchServiceImpl implements AttTempSchService {

    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AttTempSchDao attTempSchDao;
    @Autowired
    private AttShiftDao attShiftDao;
    @Autowired
    AttGroupDao attGroupDao;
    @Autowired
    AttTimeSlotDao attTimeSlotDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private ProgressCache progressCache;

    @Override
    public List<AttTempSchItem> getByCondition(AttTempSchItem condition) {
        List<AttTempSchItem> list =
            (List<AttTempSchItem>)attTempSchDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
        return list;
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        buildCondition(null, (AttTempSchItem)condition);
        Pager pager = attTempSchDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        buildDeptPersGroup(pager.getData());
        return pager;
    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, AttTempSchItem condition, int pageNo, int pageSize) {
        // 分装部门条件
        buildCondition(sessionId, condition);
        Pager pager =
            attTempSchDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        // 组装人员名称，部门名称，班次
        buildDeptPersGroup(pager.getData());
        return pager;
    }

    @Override
    public List<AttTempSchItem> getItemData(String sessionId, AttTempSchItem attTempSchItem, int beginIndex,
        int endIndex) {
        // 分装部门条件
        buildCondition(sessionId, attTempSchItem);
        List<AttTempSchItem> attCycleSchItemList = attTempSchDao.getItemsDataBySql(AttTempSchItem.class,
            SQLUtil.getSqlByItem(attTempSchItem), beginIndex, endIndex, true);
        // 组装人员名称，部门名称，班次
        buildDeptPersGroup(attCycleSchItemList);
        return attCycleSchItemList;
    }

    @Override
    public Long countByDeptId(String deptId) {
        return attTempSchDao.countByDeptId(deptId);
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            List<String> idList = StrUtil.strToList(ids);
            List<AttTempSch> attTempSchList = attTempSchDao.findByIdList(idList);
            for (AttTempSch existAttTempSch : attTempSchList) {
                attTempSchDao.delete(existAttTempSch);
            }
        }
        return false;
    }

    @Override
    public AttTempSchItem getItemById(String id) {
        AttTempSchItem attTempSchItem = new AttTempSchItem();
        // AttTempSch attTempSch = attTempSchDao.findOne(id);
        AttTempSch attTempSch = attTempSchDao.findById(id).orElse(null);
        if (attTempSch != null) {
            ModelUtil.copyProperties(attTempSch, attTempSchItem);
            Set<AttTimeSlot> attTimeSlotSet = attTempSch.getAttTimeSlotSet();
            attTempSchItem.setTimeSlotIds(CollectionUtil.getModelIds(attTimeSlotSet));
            StringBuffer sb = new StringBuffer("");
            for (AttTimeSlot attTimeSlot : attTimeSlotSet) {
                sb.append(attTimeSlot.getPeriodName()).append("(").append(attTimeSlot.getToWorkTime()).append("-")
                    .append(attTimeSlot.getOffWorkTime()).append(")").append(";");
            }
            attTempSchItem.setTimeSlotName(sb.toString());
        }
        return attTempSchItem;
    }

    /**
     * 获取 构架分装
     *
     * @param items
     */
    private void buildDeptPersGroup(List<?> items) {

        List<AttTempSchItem> list = (List<AttTempSchItem>)items;

        // 根据pin获取离职人员信息
        Collection<String> personPins = CollectionUtil.getPropertyList(list, AttTempSchItem::getPersonPin, "-1");
        List<PersLeavePersonItem> persLeavePersonItemList = persLeavePersonService.getItemByPins(personPins);
        Map<String, PersLeavePersonItem> persLeavePersonItemMap =
            CollectionUtil.listToKeyMap(persLeavePersonItemList, PersLeavePersonItem::getPin);

        Collection<String> attTempSchIds = CollectionUtil.getItemIdsList(list);
        List<AttTempSch> attTempSchList = attTempSchDao.findByIdList((List<String>)attTempSchIds);

        Map<String, String> attTimeSlotMap = new HashMap<>();
        attTempSchList.forEach(attTempSch -> {
            attTimeSlotMap.put(attTempSch.getId(),
                CollectionUtil.getPropertys(attTempSch.getAttTimeSlotSet(), AttTimeSlot::getPeriodName));
        });

        list.forEach(item -> {
            if (persLeavePersonItemMap.containsKey(item.getPersonPin())) {
                PersLeavePersonItem persLeavePersonItem = persLeavePersonItemMap.get(item.getPersonPin());
                item.setPersonName(persLeavePersonItem.getName());
                item.setPersonLastName(persLeavePersonItem.getLastName());
            }
            if (attTimeSlotMap.containsKey(item.getId())) {
                item.setTimeSlotName(StringUtils.defaultIfBlank(attTimeSlotMap.get(item.getId()), ""));
            }
        });
    }

    private void buildCondition(String sessionId, AttTempSchItem attTempSchItem) {

        /*前端条件设置，如果tab显示的分组排班，那么用pin号、部门搜索无效*/
        if (AttCommonSchConstant.SCH_TYPE_GROUP.equals(attTempSchItem.getTempType())) {
            attTempSchItem.setPersonPin(null);
            attTempSchItem.setDeptId(null);
            attTempSchItem.setDeptName(null);
        }
        if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(attTempSchItem.getTempType())) {
            attTempSchItem.setPersonPin(null);
            attTempSchItem.setGroupName(null);
        }
        if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(attTempSchItem.getTempType())) {
            attTempSchItem.setGroupName(null);
        }

        if (StringUtils.isBlank(attTempSchItem.getDeptId())) {
            attTempSchItem.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, null, null,
                attTempSchItem.getDeptName()));
        } else {
            // 包含子部门
            if ("true".equals(attTempSchItem.getIsIncludeLower())) {
                attTempSchItem.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId,
                    attTempSchItem.getDeptId(), null, attTempSchItem.getDeptName()));
                attTempSchItem.setDeptId(null);
            } else {
                attTempSchItem.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, null, null,
                    attTempSchItem.getDeptName()));
            }
        }
    }

    @Override
    @Transactional
    public void handlerTransfer(List<AttTempSchItem> attTempSchItems) {
        // 大数据拆分
        List<List<AttTempSchItem>> tempSchLists = CollectionUtil.split(attTempSchItems, CollectionUtil.splitSize);
        for (List<AttTempSchItem> tempSchItemList : tempSchLists) {
            List<AttTempSch> attTempSchList = new ArrayList<>();
            for (AttTempSchItem attTempSchItem : tempSchItemList) {
                AttTempSch attTempSch = new AttTempSch();
                List<AttTempSch> existAttTempSchList = null;
                Date startDate = attTempSchItem.getStartDate();
                Date endDate = attTempSchItem.getEndDate();
                Short scheduleType = attTempSchItem.getScheduleType();
                Short tempType = attTempSchItem.getTempType();

                attTempSch.setStartDate(startDate);
                attTempSch.setEndDate(endDate);
                attTempSch.setScheduleType(scheduleType);
                attTempSch.setTempType(tempType);
                String shiftNo = attTempSchItem.getShiftNo();
                AttShift attShift = attShiftDao.findByShiftNo(shiftNo);
                if (Objects.nonNull(attShift)) {
                    attTempSch.setAttTimeSlotSet(attShift.getAttTimeSlotSet());
                }

                if (AttCommonSchConstant.SCH_TYPE_GROUP.equals(tempType)) {
                    // 分组编号去掉之后 改用分组名称查找分组对象 modified by jinxian.huang 2019-07-25
                    String groupName = attTempSchItem.getGroupName();
                    AttGroup attGroup = attGroupDao.findByGroupName(groupName);
                    attTempSch.setGroupId(attGroup.getId());
                    // 拆分原有排班并保存
                    existAttTempSchList =
                        attTempSchDao.findByGroupIdAndStartDateAndEndDate(attGroup.getId(), startDate, endDate);
                    coverAttTempSch(attTempSch, existAttTempSchList);
                    attTempSchList.add(attTempSch);
                }

                if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(tempType)) {
                    String deptCode = attTempSchItem.getDeptCode();
                    AuthDepartmentItem authDepartmentItem = authDepartmentService.getItemByCode(deptCode);
                    attTempSch.setDeptId(authDepartmentItem.getId());
                    // 拆分原有排班并保存
                    existAttTempSchList = attTempSchDao.findByDeptIdAndStartDateAndEndDate(authDepartmentItem.getId(),
                        startDate, endDate, tempType);
                    coverAttTempSch(attTempSch, existAttTempSchList);
                    attTempSchList.add(attTempSch);
                }

                if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(tempType)) {
                    String personPin = attTempSchItem.getPersonPin();
                    // 数据迁移如果存在personPin才保存这条记录 updated by jinxian.huang 2019-04-25
                    if (!StringUtils.isEmpty(personPin)) {
                        PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
                        attTempSch.setPersonId(persPersonItem.getId());
                        attTempSch.setPersonPin(personPin);
                        // 拆分原有排班并保存
                        existAttTempSchList = attTempSchDao.findByPersonIdAndStartDateAndEndDate(persPersonItem.getId(),
                            startDate, endDate);
                        coverAttTempSch(attTempSch, existAttTempSchList);
                        attTempSchList.add(attTempSch);
                    }
                }
            }
            attTempSchDao.saveAll(attTempSchList);
        }
    }

    @Override
    public List<AttTempSchItem> getTempSchList(Short schType, Date startDate, Date endDate) {
        List<AttTempSch> attTempSchList = null;
        if ((short)0 == schType) {
            attTempSchList = attTempSchDao.findByTempTypeAndStartDateAndEndDate(AttCommonSchConstant.SCH_TYPE_GROUP,
                startDate, endDate);
        } else if ((short)1 == schType) {
            attTempSchList = attTempSchDao.findByTempTypeAndStartDateAndEndDate(AttCommonSchConstant.SCH_TYPE_DEPT,
                startDate, endDate);
        } else {
            attTempSchList = attTempSchDao.findByTempTypeAndStartDateAndEndDate(AttCommonSchConstant.SCH_TYPE_PERSON,
                startDate, endDate);
        }

        List<AttTempSchItem> attTempSchItems = new ArrayList<>(attTempSchList.size());
        for (AttTempSch attTempSch : attTempSchList) {
            AttTempSchItem tempSchitem = ModelUtil.copyProperties(attTempSch, new AttTempSchItem());
            tempSchitem.setTimeSlotIds(CollectionUtil.getPropertys(attTempSch.getAttTimeSlotSet(), AttTimeSlot::getId));
            attTempSchItems.add(tempSchitem);
        }
        return attTempSchItems;
    }

    @Override
    public List<AttTempSchItem> getTempSchListByDate(Date startDate, Date endDate) {
        List<AttTempSch> attTempSchList = attTempSchDao.findByStartDateAndEndDate(startDate, endDate);
        List<AttTempSchItem> attTempSchItems = new ArrayList<>(attTempSchList.size());
        for (AttTempSch attTempSch : attTempSchList) {
            AttTempSchItem tempSchitem = ModelUtil.copyProperties(attTempSch, new AttTempSchItem());
            tempSchitem.setTimeSlotIds(CollectionUtil.getModelIds(attTempSch.getAttTimeSlotSet()));
            attTempSchItems.add(tempSchitem);
        }
        return attTempSchItems;
    }

    @Override
    public List<AttTempSchItem> getTempSchListByCondition(AttTempSchItem condition) {
        List<AttTempSch> attTempSchList = new ArrayList<>();
        if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(condition.getTempType())
                && StringUtils.isNotBlank(condition.getInPersonPin())) {
            Collection<String> personPins = CollectionUtil.strToList(condition.getInPersonPin());
            List<List<String>> personPinsGroup = CollectionUtil.split(personPins, CollectionUtil.splitSize);
            for (List<String> pins : personPinsGroup) {
                List<AttTempSch> attTempSchs = attTempSchDao.findPersonTempByPersonPinInAndStartDateAndEndDate(
                        pins, condition.getStartTime(), condition.getEndTime());
                attTempSchList.addAll(attTempSchs);
            }
        } else if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(condition.getTempType())
                && StringUtils.isNotBlank(condition.getInDeptId())) {
            attTempSchList = attTempSchDao.findDeptTempByDeptIdInAndStartDateAndEndDate(
                    CollectionUtil.strToList(condition.getInDeptId()), condition.getStartTime(), condition.getEndTime());
        } else if (AttCommonSchConstant.SCH_TYPE_GROUP.equals(condition.getTempType())
                && StringUtils.isNotBlank(condition.getInGroupId())) {
            attTempSchList = attTempSchDao.findGroupTempByGroupIdInAndStartDateAndEndDate(
                    CollectionUtil.strToList(condition.getInGroupId()), condition.getStartTime(), condition.getEndTime());
        }
        List<AttTempSchItem> attTempSchItems = new ArrayList<>(attTempSchList.size());
        for (AttTempSch attTempSch : attTempSchList) {
            AttTempSchItem tempSchitem = ModelUtil.copyProperties(attTempSch, new AttTempSchItem());
            tempSchitem.setTimeSlotIds(CollectionUtil.getModelIds(attTempSch.getAttTimeSlotSet()));
            attTempSchItems.add(tempSchitem);
        }
        return attTempSchItems;
    }

    @Override
    @Transactional
    public void saveTempSch(String addIds, AttTempSchItem item,
        List<AttDXCalendarEventBean> attDXCalendarEventBeanList) {

        // 根据工作类型赋值加班标记
        if (AttShiftConstant.SHIFT_WORKTYPE_NORMALWORK.equals(item.getWorkType())) {
            item.setOvertimeRemark(AttShiftConstant.OvertimeSign.NORMAL);
        } else if (AttShiftConstant.SHIFT_WORKTYPE_WEEKENDOT.equals(item.getWorkType())) {
            item.setOvertimeRemark(AttShiftConstant.OvertimeSign.REST);
        } else {
            item.setOvertimeRemark(AttShiftConstant.OvertimeSign.HOLIDAY);
        }

        // 拆分组装时间段，相同日期、相同时间段组装成一条记录
        List<AttDXCalendarEventBean> attDXCalendarEventBeanListNew = buildDXCalendarEvent(attDXCalendarEventBeanList);

        // 编辑的时候，找出当前编辑已存在的排班，并根据类型，获取赋值ids，当新增处理
        AttTempSch existAttTempSch = null;
        if (StringUtils.isNotBlank(item.getId())) {
            // existAttTempSch = attTempSchDao.findOne(item.getId());
            existAttTempSch = attTempSchDao.getOne(item.getId());
            if (StringUtils.isNotBlank(existAttTempSch.getGroupId())) {
                addIds = existAttTempSch.getGroupId();
            } else if (StringUtils.isNotBlank(existAttTempSch.getPersonId())) {
                addIds = existAttTempSch.getPersonId();
            } else if (StringUtils.isNotBlank(existAttTempSch.getDeptId())) {
                addIds = existAttTempSch.getDeptId();
            }
            attTempSchDao.delete(existAttTempSch);
        }

        // 新增，addIds为前端选择的人员id或者部门id或者分组id，根据TempType判断
        if (StringUtils.isNotBlank(addIds)) {
            List<AttTempSch> attTempSchList = new ArrayList<>();
            String[] idArray = addIds.split(",");
            if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(item.getTempType())) {
                List<PersPersonItem> persPersonItemList = persPersonService.getItemsByIds(Arrays.asList(idArray));
                Map<String, PersPersonItem> persPersonItemMap =
                    CollectionUtil.listToKeyMap(persPersonItemList, PersPersonItem::getId);
                for (String personId : idArray) {
                    // 人员排班
                    for (AttDXCalendarEventBean attDXCalendarEventBean : attDXCalendarEventBeanListNew) {
                        AttTempSch attTempSch = buildAttTempSch(item, attDXCalendarEventBean);
                        attTempSch.setPersonId(personId);
                        if (persPersonItemMap.containsKey(personId)) {
                            attTempSch.setPersonPin(persPersonItemMap.get(personId).getPin());
                            // attTempSch.setDeptId(persPersonItemMap.get(personId).getDeptId());
                        }
                        List<AttTempSch> existAttTempSchList = attTempSchDao.findByPersonIdAndStartDateAndEndDate(
                            personId, attDXCalendarEventBean.getStart_date(), attDXCalendarEventBean.getEnd_date());
                        coverAttTempSch(attTempSch, existAttTempSchList);
                        attTempSchList.add(attTempSch);
                    }
                }
            } else if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(item.getTempType())) {
                // 部门排班
                for (String deptId : idArray) {
                    for (AttDXCalendarEventBean attDXCalendarEventBean : attDXCalendarEventBeanListNew) {
                        AttTempSch attTempSch = buildAttTempSch(item, attDXCalendarEventBean);
                        attTempSch.setDeptId(deptId);

                        List<AttTempSch> existAttTempSchList = attTempSchDao.findByDeptIdAndStartDateAndEndDate(deptId,
                            attDXCalendarEventBean.getStart_date(), attDXCalendarEventBean.getEnd_date(),
                            item.getTempType());
                        coverAttTempSch(attTempSch, existAttTempSchList);
                        attTempSchList.add(attTempSch);
                    }
                }

            } else {
                // 分组排班
                for (String groupId : idArray) {
                    for (AttDXCalendarEventBean attDXCalendarEventBean : attDXCalendarEventBeanListNew) {
                        AttTempSch attTempSch = buildAttTempSch(item, attDXCalendarEventBean);
                        attTempSch.setGroupId(groupId);
                        List<AttTempSch> existAttTempSchList = attTempSchDao.findByGroupIdAndStartDateAndEndDate(
                            groupId, attDXCalendarEventBean.getStart_date(), attDXCalendarEventBean.getEnd_date());
                        coverAttTempSch(attTempSch, existAttTempSchList);
                        attTempSchList.add(attTempSch);
                    }
                }
            }
            attTempSchDao.saveAll(attTempSchList);
        }
    }

    /**
     * 拆分组装时间段，相同日期、相同时间段组装成一条记录
     * 
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/6/9 10:04
     * @param attDXCalendarEventBeanList
     * @return java.util.List<com.zkteco.zkbiosecurity.att.bean.AttDXCalendarEventBean>
     */
    private List<AttDXCalendarEventBean> buildDXCalendarEvent(List<AttDXCalendarEventBean> attDXCalendarEventBeanList) {

        // 根据日期组装每个日期的时间段并排序<日期-"时间段IDS">
        Map<String, String> dateTimeSlotMap = new TreeMap<String, String>(new Comparator<String>() {
            @Override
            public int compare(String obj1, String obj2) {
                return obj1.compareTo(obj2);
            }
        });
        for (AttDXCalendarEventBean item : attDXCalendarEventBeanList) {
            if (!item.is_timed()) {
                item.setEnd_date(DateUtil.addDay(item.getEnd_date(), -1));
            }
            AttDateUtils.forEachDay(item.getStart_date(), item.getEnd_date(), date -> {
                String dateStr = AttDateUtils.dateToStrAsShort(date);
                if (dateTimeSlotMap.containsKey(dateStr)) {
                    dateTimeSlotMap.put(dateStr, dateTimeSlotMap.get(dateStr) + "," + item.getAttTimeSlotIds());
                } else {
                    dateTimeSlotMap.put(dateStr, item.getAttTimeSlotIds());
                }
            });
        }

        // 根据相邻的日志的时间段来拆分
        List<AttDXCalendarEventBean> attDXCalendarEventBeanListNew = new ArrayList<>();
        AttDXCalendarEventBean attDXCalendarEvent = new AttDXCalendarEventBean();
        for (Map.Entry<String, String> entry : dateTimeSlotMap.entrySet()) {

            if (attDXCalendarEvent.getStart_date() == null) {
                attDXCalendarEvent.setStart_date(AttDateUtils.stringToYmdDate(entry.getKey()));
            }

            if (StringUtils.isBlank(attDXCalendarEvent.getAttTimeSlotIds())) {
                attDXCalendarEvent.setAttTimeSlotIds(entry.getValue());
            }

            String nextDate =
                AttDateUtils.dateToStrAsShort(DateUtil.addDay(AttDateUtils.stringToYmdDate(entry.getKey()), 1));
            // 下一天没有数据，或者与下一天的时间段id不一致，拆分
            if (!dateTimeSlotMap.containsKey(nextDate)
                || !equalsTimeSlotIds(attDXCalendarEvent.getAttTimeSlotIds(), dateTimeSlotMap.get(nextDate))) {
                attDXCalendarEvent.setEnd_date(AttDateUtils.stringToYmdDate(entry.getKey()));
                attDXCalendarEventBeanListNew.add(attDXCalendarEvent);
                attDXCalendarEvent = new AttDXCalendarEventBean();
            }
        }
        return attDXCalendarEventBeanListNew;
    }

    /**
     * 判断两个时间段IDS是否相等
     * 
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/6/9 18:28
     * @param attTimeSlotIds
     * @param attTimeSlotIdsOther
     * @return boolean
     */
    private boolean equalsTimeSlotIds(String attTimeSlotIds, String attTimeSlotIdsOther) {
        List<String> attTimeSlotIdsList = Arrays.asList(attTimeSlotIds.split(","));
        Collections.sort(attTimeSlotIdsList);
        List<String> attTimeSlotIdsOtherList = Arrays.asList(attTimeSlotIdsOther.split(","));
        Collections.sort(attTimeSlotIdsOtherList);
        return attTimeSlotIdsList.equals(attTimeSlotIdsOtherList);
    }

    /**
     * 组装临时排班信息，主要处理赋值时间段、开始时间、结束时间
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/8 14:01
     * @param item
     * @param attDXCalendarEventBean
     * @return com.zkteco.zkbiosecurity.att.model.AttTempSch
     */
    private AttTempSch buildAttTempSch(AttTempSchItem item, AttDXCalendarEventBean attDXCalendarEventBean) {
        AttTempSch attTempSch = new AttTempSch();
        ModelUtil.copyPropertiesIgnoreId(item, attTempSch);
        attTempSch.setScheduleType(AttCommonSchConstant.ScheduleType.NORMAL_SCHEDULE);
        attTempSch.setStartDate(attDXCalendarEventBean.getStart_date());

        // is_timed判断是否减一天
        Date endDate = attDXCalendarEventBean.is_timed() ? attDXCalendarEventBean.getEnd_date()
            : DateUtil.addDay(attDXCalendarEventBean.getEnd_date(), -1);

        attTempSch.setEndDate(endDate);
        Set<AttTimeSlot> attTimeSlotSet =
            attTimeSlotDao.findByIdIn(Arrays.asList(attDXCalendarEventBean.getAttTimeSlotIds().split(",")));
        attTempSch.setAttTimeSlotSet(attTimeSlotSet);
        return attTempSch;
    }

    /**
     * 遍历判断存在的临时排班内的时间段是否有交集，有交集时，需要拆分保存
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/8 14:31
     * @param editAttTempSch
     *            必须字段startDate/endDate/attTimeSlotSet
     * @param existAttTempSchList
     * @return com.zkteco.zkbiosecurity.att.model.AttTempSch
     */
    private AttTempSch coverAttTempSch(AttTempSch editAttTempSch, List<AttTempSch> existAttTempSchList) {

        if (existAttTempSchList != null && !existAttTempSchList.isEmpty()) {
            // 遍历判断存在的临时排班内的日期是否有交集，有交集时，需要拆分保存
            for (AttTempSch existAttTempSch : existAttTempSchList) {
                // 拆分临时排版，且返回拆分后的临时排班集合
                List<AttTempSch> attTempSchSaveList =
                    splitAndBuildSaveList(editAttTempSch.getStartDate(), editAttTempSch.getEndDate(), existAttTempSch);

                if (attTempSchSaveList != null && !attTempSchSaveList.isEmpty()) {
                    // 批量保存拆分的临时排班
                    attTempSchDao.saveAll(attTempSchSaveList);
                }
            }

            // 批量删除被拆分的排班或者被编辑的排班
            attTempSchDao.deleteAll(existAttTempSchList);
        }
        return editAttTempSch;
    }

    @Override
    @Transactional
    public void delTempSch(String ids, Short type, Date startDate, Date endDate) {
        if (StringUtils.isNotBlank(ids) && type != null) {

            // 根据排班类型，找出数据库中存在日期有交集的数据
            String[] idArray = ids.split(",");
            if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(type)) {
                for (String personId : idArray) {
                    // 人员排班
                    List<AttTempSch> existAttTempSchList =
                        attTempSchDao.findByPersonIdAndStartDateAndEndDate(personId, startDate, endDate);
                    coverDeleteAttTempSch(existAttTempSchList, startDate, endDate);
                }
            } else if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(type)) {
                // 部门排班
                for (String deptId : idArray) {
                    List<AttTempSch> existAttTempSchList =
                        attTempSchDao.findByDeptIdAndStartDateAndEndDate(deptId, startDate, endDate, type);
                    coverDeleteAttTempSch(existAttTempSchList, startDate, endDate);
                }

            } else {
                // 分组排班
                for (String groupId : idArray) {
                    List<AttTempSch> existAttTempSchList =
                        attTempSchDao.findByGroupIdAndStartDateAndEndDate(groupId, startDate, endDate);
                    coverDeleteAttTempSch(existAttTempSchList, startDate, endDate);
                }
            }
        }
    }

    /**
     * 对有交集的数据进行删除和拆分
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/9 16:10
     * @param existAttTempSchList
     * @param startDate
     * @param endDate
     * @return void
     */
    private void coverDeleteAttTempSch(List<AttTempSch> existAttTempSchList, Date startDate, Date endDate) {

        // 遍历判断存在的临时排班内的时间段是否有交集，有交集时，需要拆分保存
        for (AttTempSch existAttTempSch : existAttTempSchList) {
            // 找出关联的时间段，不然批量保存的时候获取不到时间段
            existAttTempSch.getAttTimeSlotSet().size();
            // 拆分临时排版，且返回拆分后的临时排班集合
            List<AttTempSch> attTempSchSaveList = splitAndBuildSaveList(startDate, endDate, existAttTempSch);

            if (attTempSchSaveList != null && !attTempSchSaveList.isEmpty()) {
                // 批量保存拆分的临时排班
                attTempSchDao.saveAll(attTempSchSaveList);

            }
        }

        // 批量删除被拆分的临时排班
        attTempSchDao.deleteAll(existAttTempSchList);
    }

    /**
     * 拆分临时排班，且返回拆分后需要新增的临时排班集合
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/9 15:43
     * @param editStartDate
     * @param editEndDate
     * @param existAttTempSchBak
     * @return void
     */
    private List<AttTempSch> splitAndBuildSaveList(Date editStartDate, Date editEndDate,
        AttTempSch existAttTempSchBak) {

        List<AttTempSch> attTempSchSaveList = new ArrayList<>();

        // 格式化时间，前端数据可能带有分钟，判断会出错
        String editAttTempSchStartDate = AttDateUtils.dateToStrAsShort(editStartDate);
        String editAttTempSchEndDate = AttDateUtils.dateToStrAsShort(editEndDate);
        String existAttTempSchStartDate = AttDateUtils.dateToStrAsShort(existAttTempSchBak.getStartDate());
        String existAttTempSchEndDate = AttDateUtils.dateToStrAsShort(existAttTempSchBak.getEndDate());

        // 编辑的临时排班包含当前存在的开始时间范围内
        if (editAttTempSchStartDate.compareTo(existAttTempSchStartDate) > 0
            && editAttTempSchEndDate.compareTo(existAttTempSchEndDate) >= 0) {
            addAttTempSchSaveList(attTempSchSaveList, existAttTempSchBak, null, DateUtil.addDay(editStartDate, -1));
        } else if (editAttTempSchStartDate.compareTo(existAttTempSchStartDate) > 0
            && editAttTempSchEndDate.compareTo(existAttTempSchEndDate) < 0) {
            // 编辑得临时排班被当前存在得包围，拆分两段
            addAttTempSchSaveList(attTempSchSaveList, existAttTempSchBak, null, DateUtil.addDay(editStartDate, -1));
            addAttTempSchSaveList(attTempSchSaveList, existAttTempSchBak, DateUtil.addDay(editEndDate, 1), null);
        } else if (editAttTempSchStartDate.compareTo(existAttTempSchStartDate) <= 0
            && editAttTempSchEndDate.compareTo(existAttTempSchEndDate) < 0) {
            // 编辑的临时排班包含当前存在的结束时间范围内
            addAttTempSchSaveList(attTempSchSaveList, existAttTempSchBak, DateUtil.addDay(editEndDate, 1), null);
        }

        return attTempSchSaveList;
    }

    /**
     * 组装需要拆分保存的临时排班信息
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/8 18:32
     * @param attTempSchSaveList
     * @param existAttTempSchBak
     * @param startDate
     *            有值更新
     * @param endDate
     *            有值更新
     * @return void
     */
    private void addAttTempSchSaveList(List<AttTempSch> attTempSchSaveList, AttTempSch existAttTempSchBak,
        Date startDate, Date endDate) {
        AttTempSch attTempSch = new AttTempSch();
        ModelUtil.copyPropertiesIgnoreId(existAttTempSchBak, attTempSch);
        if (existAttTempSchBak.getAttTimeSlotSet().size() > 0) {
            attTempSch.setAttTimeSlotSet(existAttTempSchBak.getAttTimeSlotSet());
        }
        if (startDate != null) {
            attTempSch.setStartDate(startDate);
        }
        if (endDate != null) {
            attTempSch.setEndDate(endDate);
        }
        attTempSchSaveList.add(attTempSch);
    }

    @Override
    @Transactional
    public ZKResultMsg importExcel(List<AttTempSchItem> attTempSchItemList) {

        if (attTempSchItemList.size() > 30000) {
            throw ZKBusinessException.warnException("pers_import_overData", attTempSchItemList.size());
        }

        int importAllSize = attTempSchItemList.size();
        int importSize = 0;

        int beginProgress = 20;
        // 时间段信息集合
        List<AttTimeSlot> attTimeSlotList = attTimeSlotDao.findAll();
        Map<String, AttTimeSlot> attTimeSlotMap =
            CollectionUtil.listToKeyMap(attTimeSlotList, AttTimeSlot::getPeriodName);

        List<List<AttTempSchItem>> attTempSchItemGroupList =
            CollectionUtil.split(attTempSchItemList, CollectionUtil.splitSize);

        Map<String, String> workTypeMap = new HashMap<>();
        workTypeMap.put(I18nUtil.i18nCode("att_shift_normalWork"), "normalWork");
        workTypeMap.put(I18nUtil.i18nCode("att_overtime_rest"), "weekendOt");
        workTypeMap.put(I18nUtil.i18nCode("att_shift_holidayOt"), "holidayOt");

        for (List<AttTempSchItem> attTempSchItemGroup : attTempSchItemGroupList) {

            Collection<String> pins =
                CollectionUtil.getPropertyList(attTempSchItemGroup, AttTempSchItem::getPersonPin, "-1");
            // 人员信息集合
            List<PersPersonItem> persPersonList = persPersonService.getItemsByPins(pins);
            Map<String, PersPersonItem> persPersonMap =
                CollectionUtil.listToKeyMap(persPersonList, PersPersonItem::getPin);

            // 做数据校验检查并移除异常数据以及收集关键数据，方便in查询
            Iterator<AttTempSchItem> itemIterator = attTempSchItemGroup.iterator();
            while (itemIterator.hasNext()) {
                AttTempSchItem attTempSchItem = itemIterator.next();

                // 人员编号非空校验
                if (StringUtils.isBlank(attTempSchItem.getPersonPin())) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                        I18nUtil.i18nCode("pers_import_pinNotEmpty")));
                    itemIterator.remove();
                    continue;
                }

                // 人员编号不存在
                if (persPersonMap.isEmpty() || !persPersonMap.containsKey(attTempSchItem.getPersonPin())) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                        I18nUtil.i18nCode("att_timeSlot_importNotPin", attTempSchItem.getPersonPin())));
                    itemIterator.remove();
                    continue;
                }

                // 工作类型不能为空
                if (StringUtils.isBlank(attTempSchItem.getWorkType())) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("att_personSch_workTypeNotNull", attTempSchItem.getPersonPin())));
                    itemIterator.remove();
                    continue;
                }

                // 工作类型不存在
                if (StringUtils.isNotBlank(attTempSchItem.getWorkType())) {
                    if (!workTypeMap.containsKey(attTempSchItem.getWorkType())) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("att_personSch_workTypeNotExist", attTempSchItem.getPersonPin())));
                        itemIterator.remove();
                        continue;
                    }
                }

                // 时间段不能为空
                if (attTempSchItem.getMap() == null || attTempSchItem.getMap().size() < 1) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                        I18nUtil.i18nCode("att_timeSlot_notEmpty", attTempSchItem.getPersonPin())));
                    itemIterator.remove();
                } else {
                    // 判断时间段存在、重复、交叉
                    // 遍历每个日期
                    mapFor:
                    for (Map.Entry<String, Object> entry : attTempSchItem.getMap().entrySet()) {
                        if (entry.getValue() != null) {
                            String[] timeSlotNameArray = entry.getValue().toString().split(",");
                            // 遍历每个时间段
                            List<AttTimeSlot> attTimeSlotListRepeat = new ArrayList<>();
                            timeSlotFor:
                            for (String timeSlotName : timeSlotNameArray) {
                                if (!attTimeSlotMap.containsKey(timeSlotName)) {
                                    progressCache.setProcess(
                                        ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                                            "att_timeSlot_notExist", attTempSchItem.getPersonPin(), timeSlotName)));
                                    itemIterator.remove();
                                    break mapFor;
                                } else {
                                    AttTimeSlot attTimeSlot = attTimeSlotMap.get(timeSlotName);

                                    if (AttConstant.PERIODTYPE_ELASTIC.equals(attTimeSlot.getPeriodType())) {
                                        progressCache.setProcess(
                                                ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                                                        "att_timeSlot_elasticTimePeriod", attTempSchItem.getPersonPin(), timeSlotName)));
                                        itemIterator.remove();
                                        break mapFor;
                                    }

                                    attTimeSlotListRepeat.add(attTimeSlot);
                                }
                            }

                            // 判断时间段是否有交集
                            int size = attTimeSlotListRepeat.size();
                            for (int i = 0; i < size; i++) {
                                AttTimeSlot attTimeSlot1 = attTimeSlotListRepeat.get(i);
                                for (int j = i; j < size; j++) {
                                    AttTimeSlot attTimeSlot2 = attTimeSlotListRepeat.get(j);

                                    if (j != i && attTimeSlot1.getPeriodName().equals(attTimeSlot2.getPeriodName())) {
                                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                            I18nUtil.i18nCode("att_timeSlot_importRepeat",
                                                attTempSchItem.getPersonPin(), attTimeSlot1.getPeriodName())));
                                        itemIterator.remove();
                                        break mapFor;
                                    }
                                    if (j != i
                                        && timeSlotCross(attTimeSlot1.getToWorkTime(), attTimeSlot1.getOffWorkTime(),
                                            attTimeSlot2.getToWorkTime(), attTimeSlot2.getOffWorkTime())) {
                                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                            I18nUtil.i18nCode("att_timeSlot_repeatEx", attTempSchItem.getPersonPin(),
                                                attTimeSlot1.getPeriodName(), attTimeSlot2.getPeriodName())));
                                        itemIterator.remove();
                                        break mapFor;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            for (AttTempSchItem attTempSchItem : attTempSchItemGroup) {

                if (persPersonMap.containsKey(attTempSchItem.getPersonPin())) {

                    List<AttTempSch> attTempSchList = new ArrayList<>();
                    List<AttDXCalendarEventBean> attDXCalendarEventBeanList =
                        buildAttDXCalendarEventBean(attTempSchItem.getMap(), attTimeSlotMap);

                    // 拆分组装时间段，相同日期、相同时间段组装成一条记录
                    List<AttDXCalendarEventBean> attDXCalendarEventBeanListNew = buildDXCalendarEvent(attDXCalendarEventBeanList);

                    PersPersonItem persPersonItem = persPersonMap.get(attTempSchItem.getPersonPin());
                    for (AttDXCalendarEventBean attDXCalendarEventBean : attDXCalendarEventBeanListNew) {

                        AttTempSch attTempSch = buildAttTempSch(attTempSchItem, attDXCalendarEventBean);
                        attTempSch.setPersonId(persPersonItem.getId());
                        attTempSch.setWorkType(workTypeMap.get(attTempSchItem.getWorkType()));
                        /*默认值*/
                        attTempSch.setScheduleType(AttCommonSchConstant.ScheduleType.NORMAL_SCHEDULE);
                        attTempSch.setAttendanceMode(AttShiftConstant.AttendanceMode.SHIFT_NORMAL);
                        attTempSch.setOvertimeMode(AttShiftConstant.OvertimeMode.AUTO_CALC);
                        attTempSch.setOvertimeRemark(AttShiftConstant.OvertimeSign.NORMAL);
                        attTempSch.setTempType(AttCommonSchConstant.SCH_TYPE_PERSON);
                        attTempSch.setPersonPin(attTempSchItem.getPersonPin());
                        attTempSch.setDeptId(persPersonItem.getDeptId());

                        List<AttTempSch> existAttTempSchList =
                            attTempSchDao.findByPersonIdAndStartDateAndEndDate(persPersonItem.getId(),
                                attDXCalendarEventBean.getStart_date(), attDXCalendarEventBean.getEnd_date());
                        coverAttTempSch(attTempSch, existAttTempSchList);
                        attTempSchList.add(attTempSch);
                    }
                    attTempSchDao.saveAll(attTempSchList);

                }
            }

            importSize += attTempSchItemGroup.size();
        }

        // 文件检查失败数量
        int faildCount = importAllSize - importSize;
        // 成功数据
        int saveCount = importAllSize - faildCount;
        progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
            I18nUtil.i18nCode("pers_import_result", saveCount, faildCount)));
        return new ZKResultMsg();
    }

    /**
     * 判断两个时间段是否有交集
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/15 10:17
     * @param start1
     *            时间段1的开始时间
     * @param end1
     *            时间段1的结束
     * @param start2
     *            时间段2的开始时间
     * @param end2
     *            时间段2的开始时间
     * @return boolean
     */
    private boolean timeSlotCross(String start1, String end1, String start2, String end2) {
        // 存在跨天结束时间补2，不存在跨天补1
        if (start1.compareTo(end1) >= 0) {
            end1 = "3" + end1;
        }

        if (start2.compareTo(end2) >= 0) {
            end2 = "3" + end2;
        }
        return start1.compareTo(end2) <= 0 && end1.compareTo(start2) >= 0;
    }

    /**
     * 根据导入的日期与时间段名称的map集合，拆分连续日期且时段相同的为一条记录
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/15 10:14
     * @param map
     * @param attTimeSlotMap
     * @return java.util.List<com.zkteco.zkbiosecurity.att.bean.AttDXCalendarEventBean>
     */
    private List<AttDXCalendarEventBean> buildAttDXCalendarEventBean(Map<String, Object> map,
        Map<String, AttTimeSlot> attTimeSlotMap) {
        Map<String, List<String>> mapEx = new HashMap<>();

        /*组装成时间段名称-多个日期格式的*/
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getValue() != null) {
                String[] timeSlotNameArray = entry.getValue().toString().split(",");
                for (String timeSlotName : timeSlotNameArray) {
                    List<String> stringList = null;
                    if (mapEx.containsKey(timeSlotName)) {
                        stringList = mapEx.get(timeSlotName);
                    } else {
                        stringList = new ArrayList<>();
                    }
                    stringList.add(entry.getKey());
                    mapEx.put(timeSlotName, stringList);
                }
            }
        }

        /*遍历根据不是连续的日期拆分*/
        List<AttDXCalendarEventBean> attDXCalendarEventBeanList = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : mapEx.entrySet()) {

            // 从小到大排序
            List<String> dateList = entry.getValue();
            Collections.sort(dateList, new Comparator<String>() {
                @Override
                public int compare(String o1, String o2) {
                    return o1.compareTo(o2);
                }
            });

            int size = dateList.size();
            AttDXCalendarEventBean attDXCalendarEventBean = new AttDXCalendarEventBean();

            // 是否拆分标记
            boolean flag = true;
            for (int i = 0; i < size; i++) {

                Date startDate = AttDateUtils.stringToYmdDate(dateList.get(i));
                Date nextDate = null;
                if (i == size - 1) {
                    nextDate = startDate;
                } else {
                    nextDate = AttDateUtils.stringToYmdDate(dateList.get(i + 1));
                }

                // 拆分的时候，重新设置开始时间
                if (flag) {
                    flag = false;
                    attDXCalendarEventBean.setStart_date(startDate);
                }

                // 相差一天以上，标记为拆分并重新创建一个对象
                long time = Math.abs(nextDate.getTime() - startDate.getTime());
                if (time > 86400000) {
                    // 相差一天以上，标记为拆分
                    flag = true;
                    attDXCalendarEventBean.setEnd_date(startDate);
                    attDXCalendarEventBean.setAttTimeSlotIds(attTimeSlotMap.get(entry.getKey()).getId());
                    attDXCalendarEventBeanList.add(attDXCalendarEventBean);
                    // 拆分后重新创建一个对象
                    attDXCalendarEventBean = new AttDXCalendarEventBean();
                } else {

                    // 到了最后一天也是连续的日期
                    if (i == size - 1) {
                        attDXCalendarEventBean.setEnd_date(nextDate);
                        if (attTimeSlotMap.containsKey(entry.getKey())) {
                            attDXCalendarEventBean.setAttTimeSlotIds(attTimeSlotMap.get(entry.getKey()).getId());
                            attDXCalendarEventBeanList.add(attDXCalendarEventBean);

                        }
                        break;
                    }
                }
            }
        }

        return attDXCalendarEventBeanList;
    }

    @Override
    @Transactional
    public Map<String, List<AttPersonSchBO>> getTempSch(Short tempType, String ids, Date startDate, Date endDate,
        Map<String, AttTimeSlotItem> attTimeSlotItemMap, AttRuleParamBean attRuleParamBean) {

        // 查询临时排班
        AttTempSchItem tempSchCondition = new AttTempSchItem();
        tempSchCondition.setTempType(tempType);
        if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(tempType)) {
            tempSchCondition.setInDeptId(ids);
        } else if (AttCommonSchConstant.SCH_TYPE_GROUP.equals(tempType)) {
            tempSchCondition.setInGroupId(ids);
        } else if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(tempType)) {
            tempSchCondition.setInPersonPin(ids);
        }
        tempSchCondition.setStartTime(startDate);
        tempSchCondition.setEndTime(endDate);
        List<AttTempSchItem> attTempSchItems = getTempSchListByCondition(tempSchCondition);

        Map<String, List<AttPersonSchBO>> tempSchMap = new HashMap<>();
        String crossDay = attRuleParamBean.getCrossDay();
        for (AttTempSchItem attTempSchItem : attTempSchItems) {
            Date schStartDate = attTempSchItem.getStartDate();
            Date schEndDate = attTempSchItem.getEndDate();
            // 开始日期 = max(排班开始日期，计算开始日期)
            Date start = startDate.after(schStartDate) ? startDate : schStartDate;
            // 结束日期 = min(计算结束日期，排班结束日期)
            Date end = (null == schEndDate || endDate.before(schEndDate)) ? endDate : schEndDate;

            // 循环拆分排班成按天填充<ID=日期,班次(时间段)>;
            AttDateUtils.forEachDay(start, end, date -> {
                String dateStr = AttDateUtils.dateToStrAsShort(date);

                AttPersonSchBO attPersonSchBO = new AttPersonSchBO();
                attPersonSchBO.setAttendStatus(AttCalculationConstant.AttAttendStatus.ACTUAL);
                attPersonSchBO.setAttendanceMode(attTempSchItem.getAttendanceMode());
                attPersonSchBO.setOvertimeMode(attTempSchItem.getOvertimeMode());
                attPersonSchBO.setOvertimeRemark(attTempSchItem.getOvertimeRemark());
                attPersonSchBO.setWorkType(attTempSchItem.getWorkType());
                attPersonSchBO.setShiftType(AttShiftConstant.ShiftType.REGULAR_SHIFT);
                // 设置时间段
                List<AttTimeSlotBO> attTimeSlotBOList = new ArrayList<>();
                String timeSlotIds = attTempSchItem.getTimeSlotIds();
                if (StringUtils.isNotBlank(timeSlotIds)) {
                    String[] timeSlotIdArr = timeSlotIds.split(",");
                    for (String timeSlotId : timeSlotIdArr) {
                        AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(timeSlotId);
                        if (Objects.isNull(attTimeSlotItem)) {
                            continue;
                        }
                        AttTimeSlotBO attTimeSlotBO = new AttTimeSlotBO();
                        attTimeSlotBO.setAttTimeSlotId(attTimeSlotItem.getId());
                        // 设置时段是否垮天
                        AttCommonUtils.setSetFirstAndSecondDay(attTimeSlotBO, attTimeSlotItem, dateStr, crossDay);
                        attTimeSlotBOList.add(attTimeSlotBO);
                    }
                }
                AttShiftSchUtils.sortTimeSlot(attTimeSlotBOList, attTimeSlotItemMap);
                attPersonSchBO.setAttTimeSlotArray(attTimeSlotBOList);

                // 填充<ID=日期, 班次 (时间段)>
                String key = "";
                if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(tempType)) {
                    key = attTempSchItem.getDeptId() + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                } else if (AttCommonSchConstant.SCH_TYPE_GROUP.equals(tempType)) {
                    key = attTempSchItem.getGroupId() + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                } else if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(tempType)) {
                    key = attTempSchItem.getPersonPin() + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                }
                // 可能存在多个临时排班,多个则存放一起
                List<AttPersonSchBO> tempSchBOList = tempSchMap.get(key);
                if (null == tempSchBOList) {
                    tempSchBOList = new ArrayList<>();
                }
                tempSchBOList.add(attPersonSchBO);
                tempSchMap.put(key, tempSchBOList);

            });
        }

        return tempSchMap;
    }
}