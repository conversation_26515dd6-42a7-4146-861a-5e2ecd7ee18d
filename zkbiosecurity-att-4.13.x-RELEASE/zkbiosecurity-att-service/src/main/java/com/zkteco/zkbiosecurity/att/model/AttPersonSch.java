package com.zkteco.zkbiosecurity.att.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 人员排班
 */
@Deprecated
@Entity
@Table(name = "ATT_PERSONSCH")
@Setter
@Getter
@Accessors(chain = true)
public class AttPersonSch extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 起始日期
     */
    @Column(name = "START_DATE")
    private Date startDate;

    /**
     * 结束日期
     */
    @Column(name = "END_DATE")
    private Date endDate;

    /**
     * 排班类型 0=分组,1=部门,2=人员
     */
    @Column(name = "SCHEDULE_TYPE")
    private Short scheduleType;

    /**
     * 对应排班类型组件id
     */
    @Column(name = "SCHEDULE_ID", length = 50)
    private String scheduleId;

    /**
     * 人员id
     */
    @Column(name = "PERS_PERSON_ID", length = 50)
    private String personId;
}