package com.zkteco.zkbiosecurity.att.dao;

import java.util.Collection;
import java.util.List;

import com.zkteco.zkbiosecurity.att.model.AttTiming;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 定时计算
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 17:15
 * @since 1.0.0
 */
@Deprecated
public interface AttTimingDao extends BaseDao<AttTiming, String> {

    List<AttTiming> findByIdIn(Collection<String> ids);

    /**
     * 根据时间频率和时间间隔查找AttTiming对象
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/7/22 11:03
     * @param timeCalcFrequency
     * @param timeCalcInterval
     * @return com.zkteco.zkbiosecurity.att.model.AttTiming
     */
    AttTiming findByTimeCalcFrequencyAndTimeCalcInterval(String timeCalcFrequency, String timeCalcInterval);

    /**
     * 查找按日或按天的定时器
     * 
     * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
     * @since 2019年8月26日 下午4:33:40
     * @param timeCalcFrequency
     * @param jobStatus
     *            启用/禁用
     * @return
     */
    List<AttTiming> findByTimeCalcFrequencyAndJobStatus(String timeCalcFrequency, boolean jobStatus);
}