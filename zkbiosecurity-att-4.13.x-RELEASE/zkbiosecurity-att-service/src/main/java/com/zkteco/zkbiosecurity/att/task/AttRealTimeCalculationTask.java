package com.zkteco.zkbiosecurity.att.task;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ScheduledFuture;

import com.zkteco.zkbiosecurity.core.utils.SpringUtil;
import com.zkteco.zkbiosecurity.redis.redisson.annotation.RedisLock;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.cache.AttCacheManager;
import com.zkteco.zkbiosecurity.att.cache.AttCalculationCacheManager;
import com.zkteco.zkbiosecurity.att.service.AttCalculateService;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.scheduler.ScheduleService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

import lombok.extern.slf4j.Slf4j;

/**
 * 考勤实时计算入口
 *
 * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
 * @since 2019年9月20日 下午4:16:32
 */
@Slf4j
@Component
public class AttRealTimeCalculationTask {

    private ScheduledFuture<?> scedulefuture;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private ScheduleService scheduleService;
    @Autowired
    private AttCacheManager attCacheManager;
    @Autowired
    private AttCalculateService attCalculateService;
    @Autowired
    private AttCalculationCacheManager attCalculationCacheManager;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private PersPersonService persPersonService;

    public void startTask() {
        log.info("AttRealTimeCalculationTask Init");

        try {
            // 定时计算每2分钟
            String expression = baseSysParamService.getValByName("att.realTime.calculate");
            if (StringUtils.isBlank(expression)) {
                expression = "0 0/2 * * * ?";
            }

            if (scedulefuture != null) {
                scedulefuture.cancel(true);
            }
            AttRealTimeCalculationTask attRealTimeCalculationTask = SpringUtil.getApplicationContext().getBean(AttRealTimeCalculationTask.class);
            scedulefuture = scheduleService.startScheduleTask(() -> attRealTimeCalculationTask.realTimeCalculation(), expression);

        } catch (Exception e) {
            log.error("AttRealTimeCalculationTask Init Exception", e);
        }
    }

    @RedisLock(lockName = "realTimeCalculationRedisson", waitTime = 0, expire = 600)
    public void realTimeCalculation() {
        // 是否启用实时计算
        if (!attParamService.realTimeEnable()) {
            return;
        }

        try {
            // 判断是否进行所有人员计算
            boolean isCalculateAll = false;
            String calculateAllTime = baseSysParamService.getValByName("att.realTime.calculateAllTime");
            if (StringUtils.isNotBlank(calculateAllTime)) {
                Calendar nowCalendar = Calendar.getInstance();
                Date calculateAllTimeDate =
                        DateUtil.stringToDate(calculateAllTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
                if (nowCalendar.getTime().compareTo(calculateAllTimeDate) > 0) {
                    isCalculateAll = true;
                    // 进行所有人员计算时间后推一天
                    AttCommonUtils.setCalendarHour(nowCalendar, 2);
                    baseSysParamService.saveValueByName("att.realTime.calculateAllTime",
                            AttDateUtils.dateToStrAsLong(DateUtil.addDay(nowCalendar.getTime(), 1)));
                }
            }

            // 记录所有计算异步方法
            List<CompletableFuture<Boolean>> futureAllList = new ArrayList<>();

            if (isCalculateAll) {

                log.info("AttRealTimeCalculationTask realTimeCalculation isCalculateAll");

                // 计算所有人员
                List<String> pinList = persPersonService.getAllPinList();

                // 只计算当天
                Date beginTime = DateUtil.getDayBeginTime(new Date());
                Date endDate = DateUtil.getDayEndTime(new Date());

                // 记录所有计算异步方法
                List<CompletableFuture<Boolean>> futureList =
                        attCalculateService.realTimeCalculate(pinList, beginTime, endDate);
                futureAllList.addAll(futureList);

            } else {
                // 从redis获取需要实时计算的Keys
                Set<String> realTimeEvents = attCalculationCacheManager.getAllRealTimeEvent();
                if (realTimeEvents != null && realTimeEvents.size() > 0) {

                    for (String eventKey : realTimeEvents) {
                        // log.info("AttRealTimeCalculationTask realTimeCalculation eventKey {}", eventKey);
                        // 考勤日期
                        String[] keyAry = eventKey.split(":");
                        String dateTime = keyAry[2];
                        Date calDate = AttDateUtils.stringToYmdDate(dateTime);

                        // 大于当前日期或者在考勤有效开始时间之前不进行考勤计算
                        if (System.currentTimeMillis() < calDate.getTime()
                                || calDate.getTime() < attCalculationCacheManager.getCacheStartDate().getTime()) {
                            // log.info("AttRealTimeCalculationTask realTimeCalculation time is not arriving");
                            continue;
                        }

                        // 获取Key对应的pin号集合，并删除Redis记录
                        List<String> pinList = attCalculationCacheManager.getRealTimeEventPinSet(eventKey);
                        // log.info("AttRealTimeCalculationTask realTimeCalculation pinList {}", pinList);
                        attCalculationCacheManager.delRealTimeEvent(eventKey);

                        Date beginTime = DateUtil.getDayBeginTime(calDate);
                        Date endDate = DateUtil.getDayEndTime(calDate);

                        // 记录所有计算异步方法
                        List<CompletableFuture<Boolean>> futureList =
                                attCalculateService.realTimeCalculate(pinList, beginTime, endDate);
                        futureAllList.addAll(futureList);
                    }
                }
            }

            CompletableFuture[] futures = futureAllList.toArray(new CompletableFuture[futureAllList.size()]);
            CompletableFuture.allOf(futures).join();

        } catch (Exception e) {
            log.info("AttRealTimeCalculationTask realTimeCalculation error", e);
        }
    }
}
