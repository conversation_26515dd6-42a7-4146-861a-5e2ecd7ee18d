package com.zkteco.zkbiosecurity.att.data.upgrade;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttSignAddressDao;
import com.zkteco.zkbiosecurity.att.model.AttSignAddress;
import com.zkteco.zkbiosecurity.att.utils.AttCoordinateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 4.13.0 版本升级
 *
 * <AUTHOR>
 * @date  2025-06-01 5:14
 * @since 1.0.0
 */
@Slf4j
@Component
public class AttVer4_13_0 implements UpgradeVersionManager {

    @Autowired
    private AttSignAddressDao attSignAddressDao;

    @Override
    public String getModule() {
        return BaseConstants.ATT;
    }

    @Override
    public String getVersion() {
        return "v4.13.0";
    }

    @Override
    public boolean executeUpgrade() {

        // 参数升级
        upgradeAttSignAddress();

        return true;
    }

    private void upgradeAttSignAddress() {
        List<AttSignAddress> attSignAddressList = attSignAddressDao.findAll();
        if (attSignAddressList != null) {
            for (AttSignAddress attSignAddress : attSignAddressList) {
                String latitude = attSignAddress.getLatitude();
                String longitude = attSignAddress.getLongitude();
                double[] lonLat =
                    AttCoordinateUtils.gcj02ToBd09(Double.parseDouble(longitude), Double.parseDouble(latitude));
                attSignAddress.setLongitude(Double.toString(lonLat[0]));
                attSignAddress.setLatitude(Double.toString(lonLat[1]));
                attSignAddress.setMapType(AttConstant.ATT_MAP_BAIDUMAP);
                attSignAddressDao.save(attSignAddress);
            }
        }
    }

    public static void main(String[] args) {


    }
}
