/**
 * <AUTHOR>
 * @date 2020/8/25 16:01
 */
package com.zkteco.zkbiosecurity.att.task;

import com.zkteco.zkbiosecurity.att.service.AttDayCardDetailService;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.scheduler.ScheduleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ScheduledFuture;

/**
 * 定时更新日打卡详情
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 16:01 2020/8/25
 */
@Slf4j
@Component
public class AttTimingUpdateDayCardTask {

    private ScheduledFuture<?> scedulefuture;
    @Autowired
    private ScheduleService scheduleService;

    @Autowired
    private AttDayCardDetailService attDayCardDetailService;
    @Autowired
    private AttParamService attParamService;

    public void startTask() {

        log.info("AttTimingUpdateDayCardTask Init");

        try {

            String updateDayCardDetail = attParamService.getUpdateDayCardDetail();
            if (scedulefuture != null) {
                scedulefuture.cancel(true);
            }
            scedulefuture = scheduleService.startScheduleTask(() -> attDayCardDetailService.timingUpdateDayCardDetail(),
                updateDayCardDetail);

        } catch (Exception e) {
            log.error("AttTimingUpdateDayCardTask Init Exception", e);
        }
    }
}
