package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.event.constants.EventCenterConstants;
import com.zkteco.zkbiosecurity.event.service.OtherGetEvent4EventCenterService;
import com.zkteco.zkbiosecurity.event.vo.EventCenterTypeItem;

/**
 * 事件中心获取考勤模块事件类型
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2024/10/16 14:25
 * @since 1.0.0
 */
@Service
public class AttGetEvent4EventCenterServiceImpl implements OtherGetEvent4EventCenterService {

    @Override
    public List<EventCenterTypeItem> getEventTypeItems() {
        List<EventCenterTypeItem> eventCenterTypeItemList = new ArrayList<>();
        EventCenterTypeItem eventType = new EventCenterTypeItem();
        eventType.setTypeCode("0");
        eventType.setTypeName("att_eventCenter_sign");
        eventType.setSourceModule(BaseConstants.ATT);
        eventType.setTypeLevelVaL(EventCenterConstants.EVENT_LEVEL_NORMAL);
        eventType.setLinkage(false);
        eventCenterTypeItemList.add(eventType);
        return eventCenterTypeItemList;
    }
}
