package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.cache.AttCalculationCacheManager;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.constants.AttShiftConstant;
import com.zkteco.zkbiosecurity.att.dao.AttAdjustDao;
import com.zkteco.zkbiosecurity.att.dao.AttClassDao;
import com.zkteco.zkbiosecurity.att.dao.AttShiftDao;
import com.zkteco.zkbiosecurity.att.dao.AttTimeSlotDao;
import com.zkteco.zkbiosecurity.att.model.AttShift;
import com.zkteco.zkbiosecurity.att.model.AttTimeSlot;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.service.AttShiftService;
import com.zkteco.zkbiosecurity.att.vo.AttShiftItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 班次
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:04
 * @since 1.0.0
 */
@Slf4j
@Service
public class AttShiftServiceImpl implements AttShiftService {

    @Autowired
    private AttShiftDao attShiftDao;
    @Autowired
    private AttTimeSlotDao attTimeSlotDao;
    @Autowired
    private AttAdjustDao attAdjustDao;
    @Autowired
    private AttClassDao attClassDao;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private AttCalculationCacheManager attCalculationCacheManager;
    @Autowired
    private AttParamService attParamService;

    @Override
    @Transactional
    public AttShiftItem saveItem(AttShiftItem item, String attTimeSlotIds) {

        // 根据工作类型赋值加班标记
        if (AttShiftConstant.SHIFT_WORKTYPE_NORMALWORK.equals(item.getWorkType())) {
            item.setOvertimeSign(AttShiftConstant.OvertimeSign.NORMAL);
        } else if (AttShiftConstant.SHIFT_WORKTYPE_WEEKENDOT.equals(item.getWorkType())) {
            item.setOvertimeSign(AttShiftConstant.OvertimeSign.REST);
        } else {
            item.setOvertimeSign(AttShiftConstant.OvertimeSign.HOLIDAY);
        }

        AttShift attShift = Optional.ofNullable(item).map(AttShiftItem::getId).filter(StringUtils::isNotBlank)
            .flatMap(attShiftDao::findById).filter(Objects::nonNull).orElse(new AttShift());
        // 前端可以进行选择，这边不能置空 - lgq 2024-02-26
        /*if (item.getPeriodicUnit() != 0) {
            attShift.setIsShiftWithinMonth(null);
            attShift.setStartDate(null);
            item.setIsShiftWithinMonth(null);
            item.setStartDate(null);
        }*/
        // 如果是按排班日期,则不用开始时间
        if (AttConstant.ATT_PERIODSTARTMODE_BYSCH.equals(item.getPeriodStartMode())) {
            attShift.setStartDate(null);
        }
        ModelUtil.copyPropertiesIgnoreNull(item, attShift);

        // 时间段有值保存，没有值需要去掉中间表数据
        if (StringUtils.isNotBlank(attTimeSlotIds)) {
            Set<AttTimeSlot> attTimeSlotSet = attTimeSlotDao.findByIdIn(Arrays.asList(attTimeSlotIds.split(",")));
            attShift.setAttTimeSlotSet(attTimeSlotSet);
        } else {
            Set<AttTimeSlot> attTimeSlotSet = attShift.getAttTimeSlotSet();
            attTimeSlotSet.forEach(attTimeSlot -> attTimeSlot.getAttShiftSet().remove(attShift));
        }

        attShiftDao.save(attShift);
        item.setId(attShift.getId());

        // 【实时计算】更新缓存的班次
        if (attParamService.realTimeEnable()) {
            AttShiftItem cacheItem = new AttShiftItem();
            ModelUtil.copyPropertiesIgnoreNull(attShift, cacheItem);
            attCalculationCacheManager.setShiftItem(cacheItem);
        }
        return item;
    }

    @Override
    @Transactional
    public ZKResultMsg saveTimeSlot(AttShiftItem item) {
        // AttShift attShift = attShiftDao.findOne(item.getId());
        AttShift attShift = attShiftDao.findById(item.getId()).orElse(null);
        if (attShift != null) {
            attShift.setTimeSlotDetailIds(item.getTimeSlotDetailIds());
            String attTimeSlotIds = item.getTimeSlotIds();
            if (StringUtils.isNotBlank(attTimeSlotIds)) {
                Set<AttTimeSlot> attTimeSlotSet = attTimeSlotDao.findByIdIn(Arrays.asList(attTimeSlotIds.split(",")));
                attShift.setAttTimeSlotSet(attTimeSlotSet);
            } else {
                Set<AttTimeSlot> attTimeSlotSet = attShift.getAttTimeSlotSet();
                attTimeSlotSet.forEach(attTimeSlot -> attTimeSlot.getAttShiftSet().remove(attShift));
            }
            attShiftDao.save(attShift);
            // 【实时计算】更新缓存的班次
            if (attParamService.realTimeEnable()) {
                AttShiftItem cacheItem = new AttShiftItem();
                ModelUtil.copyPropertiesIgnoreNull(attShift, cacheItem);
                attCalculationCacheManager.setShiftItem(cacheItem);
            }
        }
        return new ZKResultMsg();
    }

    @Override
    @Transactional
    public List<AttShiftItem> getByCondition(AttShiftItem condition) {
        List<AttShiftItem> list =
            (List<AttShiftItem>)attShiftDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
        buildShiftIds(list);
        return list;
    }

    /**
     * 填充班次ID集合
     *
     * @param list
     */
    private void buildShiftIds(List<AttShiftItem> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        Collection<String> ids = CollectionUtil.getPropertyList(list, AttShiftItem::getId, "-1");
        List<AttShift> attShiftList = attShiftDao.findByIdList(ids);
        Map<String, AttShift> attShiftMap = CollectionUtil.listToKeyMap(attShiftList, AttShift::getId);
        for (AttShiftItem item : list) {
            AttShift attShift = attShiftMap.get(item.getId());
            if (attShift != null) {
                item.setTimeSlotIds(CollectionUtil.getModelIds(attShift.getAttTimeSlotSet()));
            }
        }
    }

    @Override
    public List<AttShiftItem> getItemData(Class<AttShiftItem> attShiftItemClass, BaseItem condition, int beginIndex,
        int endIndex) {
        return attShiftDao.getItemsDataBySql(attShiftItemClass, SQLUtil.getSqlByItem(condition), beginIndex, endIndex,
            true);
    }

    @Override
    public Long getAllShiftCount() {
        return attShiftDao.count();
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return attShiftDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                attShiftDao.deleteById(id);

                // 【实时计算】删除缓存班次信息
                if (attParamService.realTimeEnable()) {
                    attCalculationCacheManager.delShiftItem(id);
                }
            }
        }
        return false;
    }

    @Override
    public AttShiftItem getItemById(String id) {
        List<AttShiftItem> items = getByCondition(new AttShiftItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public String getTimeSlotIds(String id) {
        AttShift attShift = attShiftDao.findById(id).orElse(new AttShift());
        StringBuilder attTimeSlotIds = new StringBuilder();
        for (AttTimeSlot attTimeSlot : attShift.getAttTimeSlotSet()) {
            attTimeSlotIds.append(attTimeSlot.getId() + ",");
        }
        if (attTimeSlotIds.length() > 1) {
            attTimeSlotIds.deleteCharAt(attTimeSlotIds.length() - 1);
        }
        return attTimeSlotIds.toString();
    }

    @Override
    public List<String> listTsd(String id) {
        List<String> tsdList = new ArrayList<String>();
        AttShift attShift = attShiftDao.findById(id).orElse(new AttShift());
        for (AttTimeSlot attTimeSlot : attShift.getAttTimeSlotSet()) {
            if (attTimeSlot.getPeriodType() == 0) {
                tsdList.add(attTimeSlot.getToWorkTime() + "-" + attTimeSlot.getOffWorkTime() + "_"
                    + attTimeSlot.getId().toString());
            } else if (attTimeSlot.getPeriodType() == 1) {
                tsdList.add(attTimeSlot.getStartSignInTime() + "-" + attTimeSlot.getEndSignOffTime() + "_"
                    + attTimeSlot.getId().toString());
            }
        }
        Collections.sort(tsdList);
        return tsdList;
    }

    @Override
    public boolean existsByShiftNo(String shiftNo) {
        return attShiftDao.existsByShiftNo(shiftNo);
    }

    @Override
    public boolean existsByShiftName(String shiftName) {
        return attShiftDao.existsByShiftName(shiftName);
    }

    @Override
    public boolean isExistFkData(String ids) {
        List<String> shiftIdList = Arrays.asList(ids.split(","));
        // 在调休补班，或者调班中使用
        if (attAdjustDao.existsByShiftIdIn(shiftIdList) || attClassDao.existsBySwapShiftIdIn(shiftIdList)) {
            return true;
        }
        // 在周期排班中使用
        Set<AttShift> attShiftList = attShiftDao.findByIdIn(shiftIdList);
        for (AttShift attShift : attShiftList) {
            if (!attShift.getAttCycleSchSet().isEmpty()) {
                return true;
            }
        }
        return false;
    }

    @Override
    @Transactional
    public void cleanShiftTimeSolt(String id, String idp) {
        AttShift attShift = null;
        if (StringUtils.isNotBlank(id)) {
            attShift = attShiftDao.findById(id).orElse(null);
        }
        if (Objects.nonNull(attShift)) {
            if (StringUtils.isNotBlank(idp)) {
                String tsdIds = attShift.getTimeSlotDetailIds();
                Collection<String> tsdIsList = CollectionUtil.strToList(tsdIds);
                Collection<String> idpList = CollectionUtil.strToList(idp);
                tsdIsList.removeAll(idpList);
                attShift.setTimeSlotDetailIds(StringUtils.join(tsdIsList, AttConstant.COMMA));
            } else {
                attShift.setTimeSlotDetailIds(AttConstant.EMPTY);
            }
            attShiftDao.save(attShift);

            // 【实时计算】 更新班次到缓存
            if (attParamService.realTimeEnable()) {
                AttShiftItem cacheItem = new AttShiftItem();
                ModelUtil.copyPropertiesIgnoreNull(attShift, cacheItem);
                attCalculationCacheManager.setShiftItem(cacheItem);
            }
        }
    }

    @Override
    @Transactional
    public void cleanByShiftIds(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            List<AttShift> attShiftList = attShiftDao.findByIdList(Arrays.asList(ids.split(",")));
            if (attShiftList != null) {
                for (AttShift attShift : attShiftList) {
                    attShift.setTimeSlotDetailIds(AttConstant.EMPTY);
                    Set<AttTimeSlot> attTimeSlotSet = attShift.getAttTimeSlotSet();
                    attTimeSlotSet.forEach(attTimeSlot -> attTimeSlot.getAttShiftSet().remove(attShift));
                }
                attShiftDao.saveAll(attShiftList);
            }
        }
    }

    @Override
    @Transactional
    public void handlerTransfer(List<AttShiftItem> attShiftItems) {
        // 因为数据量不大，不需要考虑
        for (AttShiftItem attShiftItem : attShiftItems) {
            String shiftNo = attShiftItem.getShiftNo();
            AttShift attShift = attShiftDao.findByShiftNo(shiftNo);
            if (Objects.isNull(attShift)) {
                attShift = new AttShift();
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(attShiftItem, attShift, "id");
            // 新增周期起始方式字段 默认为 0/按起始日期 by ljf 2020/3/31
            attShift.setPeriodStartMode(AttConstant.ATT_PERIODSTARTMODE_BYPERIOD);

            attShiftDao.save(attShift);
        }
    }

    @Override
    @Transactional
    public void shiftTimeSlotDataTransfer(List<AttShiftItem> attShiftItems) {

        for (AttShiftItem attShiftItem : attShiftItems) {
            String shiftNo = attShiftItem.getShiftNo();
            AttShift attShift = attShiftDao.findByShiftNo(shiftNo);

            String periodNo = attShiftItem.getPeriodNo();
            AttTimeSlot attTimeSlot = attTimeSlotDao.findByPeriodNo(periodNo);
            String attTimeSlotId = attTimeSlot.getId();

            Integer timeSlotIdInt = attShiftItem.getTimeSlotId();
            // 传进来的时间段明细
            String timeSlotDetailIds = attShiftItem.getTimeSlotDetailIds();
            if (StringUtils.isNotBlank(timeSlotDetailIds)) {
                // 有存在的，说明是在此添加的嘛
                if (StringUtils.isBlank(attShift.getTimeSlotDetailIds())) {
                    List<String> newTimeSlotIds = new ArrayList<>();
                    List<String> splitTimeSlotIds = Arrays.asList(timeSlotDetailIds.split(AttConstant.COMMA));
                    for (String timeSlitId : splitTimeSlotIds) {
                        String[] split = timeSlitId.split("_");
                        String timeSlotId = split[0];
                        Integer timeSlotIdIntTemp = Integer.valueOf(timeSlotId);
                        if (timeSlotIdInt.equals(timeSlotIdIntTemp)) {
                            String temp = attTimeSlotId + "_" + split[1];
                            newTimeSlotIds.add(temp);
                        } else {
                            newTimeSlotIds.add(timeSlitId);
                        }
                    }
                    attShift.setTimeSlotDetailIds(StringUtils.join(newTimeSlotIds, AttConstant.COMMA));
                } else {
                    String timeSlotDetailIdsSource = attShift.getTimeSlotDetailIds();
                    List<String> newTimeSlotIds = new ArrayList<>();
                    if (StringUtils.isNotBlank(timeSlotDetailIdsSource)) {
                        String[] split = timeSlotDetailIdsSource.split(AttConstant.COMMA);
                        for (String s : split) {
                            newTimeSlotIds.add(s);
                        }
                    }
                    List<String> splitTimeSlotIds = Arrays.asList(timeSlotDetailIds.split(AttConstant.COMMA));
                    for (String timeSlitId : splitTimeSlotIds) {
                        if (newTimeSlotIds.contains(timeSlitId)) {
                            newTimeSlotIds.remove(timeSlitId);
                            String[] split = timeSlitId.split("_");
                            String timeSlotId = split[0];
                            Integer timeSlotIdIntTemp = Integer.valueOf(timeSlotId);
                            if (timeSlotIdInt.equals(timeSlotIdIntTemp)) {
                                String temp = attTimeSlotId + "_" + split[1];
                                newTimeSlotIds.add(temp);
                            }
                        }
                    }
                    attShift.setTimeSlotDetailIds(StringUtils.join(newTimeSlotIds, AttConstant.COMMA));
                }
            }
            Set<AttTimeSlot> attTimeSlotSet = attShift.getAttTimeSlotSet();
            if (CollectionUtil.isEmpty(attTimeSlotSet)) {
                attTimeSlotSet = new HashSet<>();
            }
            attTimeSlotSet.add(attTimeSlot);
            attShift.setAttTimeSlotSet(attTimeSlotSet);
            attShiftDao.save(attShift);
        }
    }

    @Override
    @Transactional
    public void upgradeTo_220() {
        try {
            jdbcTemplate.execute("UPDATE att_shift SET shift_worktype = 'normalWork' WHERE shift_worktype IS NULL");
        } catch (Exception e) {
            log.info("AttShift Update Exception", e);
        }
    }

    @Override
    @Transactional
    public void upgradeTo_240() {
        List<AttShift> shiftList = attShiftDao.findAll();
        for (AttShift attShift : shiftList) {
            if (StringUtils.isBlank(attShift.getPeriodStartMode())) {
                attShift.setPeriodStartMode(AttConstant.ATT_PERIODSTARTMODE_BYPERIOD);
                attShiftDao.save(attShift);
            }
        }
    }
}