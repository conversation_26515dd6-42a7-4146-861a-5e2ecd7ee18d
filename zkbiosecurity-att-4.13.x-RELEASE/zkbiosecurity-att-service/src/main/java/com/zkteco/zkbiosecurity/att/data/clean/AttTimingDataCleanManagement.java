package com.zkteco.zkbiosecurity.att.data.clean;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.task.AttTimingDateCleanTask;
import com.zkteco.zkbiosecurity.system.service.DataCleanManager;

/**
 * 考勤数据清理管理器<更新定时器执行时间>
 * 
 * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
 * @since 2019年9月4日 下午3:04:13
 */
@Component("attReportDataCleanManagement")
public class AttTimingDataCleanManagement implements DataCleanManager {
    @Autowired
    private AttTimingDateCleanTask attTimingDateCleanTask;

    @Override
    public void handlerRuntime() {
        attTimingDateCleanTask.startTask();
    }

}
