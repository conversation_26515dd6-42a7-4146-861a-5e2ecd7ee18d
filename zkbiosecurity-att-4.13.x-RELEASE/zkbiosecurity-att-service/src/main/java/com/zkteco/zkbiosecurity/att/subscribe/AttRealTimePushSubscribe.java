package com.zkteco.zkbiosecurity.att.subscribe;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.redis.listener.RedisQueueReceiverListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;

/**
 * 考勤打卡记录和请假订阅
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 15:07 2022/1/5
 * @version v1.0
 */
@Slf4j
@Component
public class AttRealTimePushSubscribe  implements RedisQueueReceiverListener {

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Override
    public String getTopic() {
        return AttConstant.ATT_REALTIMEPUSH_TOPIC;
    }

    @Override
    public MessageListener redisQueueReceiverListener() {
        return (message, pattern) -> {
            JSONObject item = JSONObject.parseObject(message.getBody(), JSONObject.class);
            messagingTemplate.convertAndSend("/topic/attRTMonitor/getSignEventData", item.toString());
        };
    }
}

