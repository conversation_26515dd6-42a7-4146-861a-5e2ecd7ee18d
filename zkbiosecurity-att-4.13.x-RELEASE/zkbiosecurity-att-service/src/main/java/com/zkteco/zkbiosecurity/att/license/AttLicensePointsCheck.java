package com.zkteco.zkbiosecurity.att.license;

import com.zkteco.zkbiosecurity.att.service.AttPointService;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.service.AttDeviceService;
import com.zkteco.zkbiosecurity.att.service.AttLicensePointsCheckService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.license.provider.BaseLicenseProvider;
import com.zkteco.zkbiosecurity.license.vo.bean.ResultCode;

import lombok.extern.slf4j.Slf4j;

/**
 * 考勤许可点数校验
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 9:09 2021/8/25
 * @version v1.0
 */
@Slf4j
@Component
public class AttLicensePointsCheck implements AttLicensePointsCheckService {

    @Value("${system.productCode}")
    private String productCode;

    @Autowired
    private BaseLicenseProvider baseLicenseProvider;
    @Autowired
    private AttDeviceService attDeviceService;
    @Autowired
    private AttPointService attPointService;

    @Override
    public ZKResultMsg check() {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        if (isZKBioCVV6000Server() || isZKBioCVSecurityFoundation()) {
            // 盒子点数校验
            ResultCode resultCode = baseLicenseProvider.isCountOutDevTotal();
            if (!ResultCode.SUCCESS.equals(resultCode)) {
                log.warn("Att license points over, resultCode = {}", resultCode);
                zkResultMsg.setRet("fail");
                zkResultMsg.setMsg(I18nUtil.i18nCode("common_license_maxCount"));
                return zkResultMsg;
            }
        } else {
            int attLicensePoints = baseLicenseProvider.getAttModuleCount();
            log.warn("Att license points total count = {}", attLicensePoints);
            int currentDeviceCount = attDeviceService.getDeviceCount() + attPointService.getPointCount();
            log.warn("Att device and attPoint total count = {}", currentDeviceCount);
            if (currentDeviceCount + 1 > attLicensePoints) {
                log.warn(
                    "Att license points over, current points = " + currentDeviceCount + ", auth points = " + attLicensePoints);
                zkResultMsg.setRet("fail");
                zkResultMsg.setMsg(I18nUtil.i18nCode("common_dev_maxCount", currentDeviceCount));
            }
        }
        return zkResultMsg;
    }

    @Override
    public boolean update() {
        if (isZKBioCVV6000Server() || isZKBioCVSecurityFoundation()) {
            log.warn("Att update att license points");
            return baseLicenseProvider.updateModuleControlCount(ConstUtil.SYSTEM_MODULE_ATT);
        }
        return true;
    }

//    @Override
//    public ZKResultMsg checkAttPoint(String deviceModule) {
//        if (StringUtils.isBlank(deviceModule)) {
//            return ZKResultMsg.failMsg();
//        }
//        ResultCode resultCode = null;
//        if (isZKBioCVV6000Server()) {
//            resultCode = baseLicenseProvider.isCountOutDevTotal();
//        } else {
//            // 获取已经存在模块的个数
//            int count = attPointService.getCountByDeviceModule(deviceModule);
//            int sumCount = count + 1;
//            switch (deviceModule) {
//                case ConstUtil.SYSTEM_MODULE_IDENTIFICATION: // 人证
//                    resultCode = baseLicenseProvider.isCountOutPidAtt(sumCount);
//                    break;
//                case ConstUtil.SYSTEM_MODULE_INS: // 信息屏
//                    resultCode = baseLicenseProvider.isCountOutRangeInsAtt(sumCount);
//                    break;
//                case ConstUtil.SYSTEM_MODULE_ACC: // 门禁
//                    resultCode = baseLicenseProvider.isCountOutAccessControlDoor(sumCount);
//                    break;
//                case ConstUtil.SYSTEM_MODULE_PARK: // 停车
//                    resultCode = baseLicenseProvider.isCountOutParkingLprCamera(sumCount);
//                    break;
//                case ConstUtil.SYSTEM_MODULE_VMS:  // vms视频
//                    resultCode = baseLicenseProvider.isCountOutVmsAtt(sumCount);
//                    break;
//                case ConstUtil.SYSTEM_MODULE_IVS:  // ivs视频
//                    resultCode = baseLicenseProvider.isCountOutRange(ConstUtil.LICENSE_MODULE_IVSATT, sumCount);
//                    break;
//                case ConstUtil.LICENSE_MODULE_ESDC:  // 智能场景考勤点许可
//                    resultCode = baseLicenseProvider.isCountOutRange("esdcatt", sumCount);
//                    break;
//                case ConstUtil.SYSTEM_MODULE_PASSAGE:  // 通道
//                    resultCode = baseLicenseProvider.isCountOutRange(ConstUtil.LICENSE_MODULE_PSGATT, sumCount);
//                    break;
//                default:
//                    break;
//            }
//        }
//
//        if (!ResultCode.SUCCESS.equals(resultCode)) {
//            log.warn("att AttPoint license points over");
//            return ZKResultMsg.failMsg();
//        }
//
//        return ZKResultMsg.successMsg();
//    }
//
//    @Override
//    public boolean updateAttPoint(String deviceModule) {
//        if (isZKBioCVV6000Server()) {
//            String licenseModule = null;
//            switch (deviceModule) {
//                case ConstUtil.SYSTEM_MODULE_IDENTIFICATION: // 人证
//                    licenseModule = ConstUtil.LICENSE_MODULE_PIDATT;
//                    break;
//                case ConstUtil.SYSTEM_MODULE_INS: // 信息屏
//                    licenseModule = ConstUtil.LICENSE_MODULE_INSATT;
//                    break;
//                case ConstUtil.SYSTEM_MODULE_ACC: // 门禁
//                    licenseModule = ConstUtil.LICENSE_MODULE_ACCESS_CONTROL_DOOR;
//                    break;
//                case ConstUtil.SYSTEM_MODULE_PARK: // 停车
//                    licenseModule = ConstUtil.LICENSE_MODULE_PARKING_LPR_CAMERA;
//                    break;
//                case ConstUtil.SYSTEM_MODULE_VMS:  // vms视频
//                    licenseModule = ConstUtil.LICENSE_MODULE_VMSATT;
//                    break;
//                case ConstUtil.SYSTEM_MODULE_IVS:  // ivs视频
//                    licenseModule = ConstUtil.LICENSE_MODULE_IVSATT;
//                    break;
//                case ConstUtil.SYSTEM_MODULE_PASSAGE:  // 通道
//                    licenseModule = ConstUtil.LICENSE_MODULE_PSGATT;
//                    break;
//                default:
//                    break;
//            }
//            if (StringUtils.isNotBlank(licenseModule)) {
//                return baseLicenseProvider.updateModuleControlCount(licenseModule);
//            }
//            return false;
//        }
//        return true;
//    }

    /**
     * 判断是否走盒子许可
     * 1、考勤设备根据总许可点数控制
     * 2、第三方当考勤根据总许可点数控制
     *
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/25 13:43
     * @since 1.0.0
     */
    private boolean isZKBioCVV6000Server() {
        return BaseConstants.ZKBIOCV_SERVER_V6000.equals(baseLicenseProvider.getProductCode());
    }

    /**
     * 万傲瑞达基础版本许可
     * 1、考勤设备根据总许可点数控制
     * 2、第三方当考勤根据第三方当考勤许可点数控制
     *
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/6/5 14:08
     * @since 1.0.0
     */
    private boolean isZKBioCVSecurityFoundation() {
        return BaseConstants.ZKBIOCV_SECURITY_FOUNDATION.equals(baseLicenseProvider.getProductCode());
    }
}
