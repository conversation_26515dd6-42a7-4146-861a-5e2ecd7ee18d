package com.zkteco.zkbiosecurity.att.dao;

import com.zkteco.zkbiosecurity.att.model.AttTempSch;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 临时排班（人员、部门、分组）
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:19
 * @since 1.0.0
 */
public interface AttTempSchDao extends BaseDao<AttTempSch, String> {

    /**
     * 通过部门 id获取对应的临时分组数据
     *
     * @param deptId
     * @param tempType
     * @return
     */
    List<AttTempSch> findByDeptIdAndTempType(String deptId, Short tempType);

    /**
     * 通过部门 id获取对应的临时分组数据 根据开始时间升序
     *
     * @param deptId
     * @param tempType
     * @return
     */
    List<AttTempSch> findByDeptIdAndTempTypeOrderByStartDateAsc(String deptId, Short tempType);

    /**
     * 通过分组id获取对应的临时分组数据
     *
     * @param groupId
     * @return
     */
    List<AttTempSch> findByGroupId(String groupId);

    /**
     * 通过分组id获取对应的临时分组数据 根据开始时间升序
     *
     * @param groupId
     * @return
     */
    List<AttTempSch> findByGroupIdOrderByStartDateAsc(String groupId);

    /**
     * 通过人员，获取临时排班数据
     *
     * @param personId
     * @return
     */
    List<AttTempSch> findByPersonId(String personId);

    /**
     * 通过人员，获取临时排班数据 根据开始时间升序
     *
     * @param personId
     * @return
     */
    List<AttTempSch> findByPersonIdOrderByStartDateAsc(String personId);

    /**
     * 根据人员id获取对应的临时排班的集合
     *
     * @param personIds
     * @return
     */
    List<AttTempSch> findByPersonIdIn(Collection<String> personIds);

    /**
     * 根据分组ID,获取对应分组临时排班数据
     *
     * @param attGroupId
     * @param startDate
     * @param endDate
     * @return java.util.List<com.zkteco.zkbiosecurity.att.model.AttTempSch>
     * @date 2019/1/3 10:17
     */
    @Query("select t from AttTempSch t where t.groupId = ?1 And  t.startDate <= ?3 And t.endDate >= ?2 Order by startDate")
    List<AttTempSch> findByGroupIdAndStartDateAndEndDate(String attGroupId, Date startDate, Date endDate);

    /**
     * 根据部门ID,获取对应部门临时排班数据
     *
     * @param attDeptId
     *            部门ID
     * @param startDate
     *            开始时间
     * @param endDate
     *            结束时间
     * @param tempType
     *            临时排班类型
     * @return java.util.List<com.zkteco.zkbiosecurity.att.model.AttTempSch>
     * @date 2019/1/3 10:10
     */
    @Query("select t from AttTempSch t where t.deptId = ?1 And t.startDate <= ?3 And t.endDate >= ?2 AND tempType = ?4 Order by startDate")
    List<AttTempSch> findByDeptIdAndStartDateAndEndDate(String attDeptId, Date startDate, Date endDate, Short tempType);

    /**
     * 根据临时排班类型，查找有效范围内的临时排班
     *
     * @param tempType
     * @param startDate
     * @param endDate
     * @return
     */
    @Query("select t from AttTempSch t where t.tempType = ?1 And t.startDate <= ?3 And t.endDate >= ?2 Order by startDate")
    List<AttTempSch> findByTempTypeAndStartDateAndEndDate(Short tempType, Date startDate, Date endDate);

    List<AttTempSch> findByEndDateGreaterThanEqual(Date endDate);

    /**
     * 根据人员perssonId,获取对应人员临时排班数据
     *
     * @param personId
     *            人员PersonId
     * @param startDate
     *            开始时间
     * @param endDate
     *            结束时间
     * @return java.util.List<com.zkteco.zkbiosecurity.att.model.AttTempSch>
     * @date 2019/1/3 10:08
     */
    @Query("select t from AttTempSch t where t.personId = ?1 And t.startDate <= ?3 And t.endDate >= ?2 Order by startDate")
    List<AttTempSch> findByPersonIdAndStartDateAndEndDate(String personId, Date startDate, Date endDate);

    /**
     * 根据pin查询人员临时排班
     *
     * @param personPin
     * @param startDate
     * @param endDate
     * @return
     */
    @Query("select t from AttTempSch t where t.personPin = ?1 And t.startDate <= ?3 And t.endDate >= ?2 Order by startDate")
    List<AttTempSch> findByPersonPinAndStartDateAndEndDate(String personPin, Date startDate, Date endDate);

    /**
     * 判断数据库是否存在 分组外键
     *
     * @param groupId
     * @return boolean
     * @date 2018/6/29 15:55
     */
    boolean existsByGroupId(String groupId);

    void deleteByPersonIdIn(Collection<String> personIds);

    void deleteByPersonPinIn(Collection<String> pins);

    /**
     * 根据部门ID查找临时排班的数量
     *
     * @param deptId
     * @return java.lang.Long
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/27 14:15
     */
    Long countByDeptId(String deptId);

    // 先注释预留，后续大数据测试如单次查询比较慢就分次查询
    // @Query("select distinct t.groupId from AttTempSch t where t.tempType = 0 and t.startDate <= ?2 and t.endDate >=
    // ?1")
    // List<String> findGroupIdListByTempType(Date startDate, Date endDate);
    //
    // @Query("select distinct t.deptId from AttTempSch t where t.tempType = 1 and t.startDate <= ?2 and t.endDate >=
    // ?1")
    // List<String> findDeptIdIdListByTempType(Date startDate, Date endDate);
    //
    // @Query("select distinct t.personId from AttTempSch t where t.tempType = 2 and t.startDate <= ?2 and t.endDate >=
    // ?1")
    // List<String> findPersonIdListByTempType(Date startDate, Date endDate);

    @Query("select t from AttTempSch t where t.startDate <= ?2 and t.endDate >= ?1")
    List<AttTempSch> findByStartDateAndEndDate(Date startDate, Date endDate);

    @Query("select t from AttTempSch t where t.personPin in ?1 and t.startDate <= ?3 and t.endDate >= ?2 and t.tempType = 2")
    List<AttTempSch> findPersonTempByPersonPinInAndStartDateAndEndDate(Collection<String> strToList, Date startDate, Date endDate);

    @Query("select t from AttTempSch t where t.deptId in ?1 and t.startDate <= ?3 and t.endDate >= ?2 and t.tempType = 1")
    List<AttTempSch> findDeptTempByDeptIdInAndStartDateAndEndDate(Collection<String> strToList, Date startDate, Date endDate);

    @Query("select t from AttTempSch t where t.groupId in ?1 and t.startDate <= ?3 and t.endDate >= ?2 and t.tempType = 0")
    List<AttTempSch> findGroupTempByGroupIdInAndStartDateAndEndDate(Collection<String> strToList, Date startDate, Date endDate);

    List<AttTempSch> findByGroupIdIn(List<String> groupIdList);

    boolean existsByAttTimeSlotSet_IdIn(List<String> timeslotIdList);

    /**
     * 数据升级2.6.0-12.0，根据临时排班id查询对应班次id
     *
     * @param tempSchId:
     * @return java.util.List<java.lang.String>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/24 9:45
     * @since 1.0.0
     */
    @Query(value = "SELECT t.SHIFT_ID FROM ATT_TEMPSCH_SHIFT t WHERE t.TEMPSCH_ID = ?1", nativeQuery = true)
    List<String> findShiftIdByTempSchId(String tempSchId);

    /**
     * 数据升级2.6.0-12.0，根据临时排班id删除对应的记录
     *
     * @param tempSchId:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/24 9:45
     * @since 1.0.0
     */
    @Modifying
    @Transactional
    @Query(value = "DELETE FROM ATT_TEMPSCH_SHIFT WHERE TEMPSCH_ID = ?1", nativeQuery = true)
    void deleteByTempSchId(String tempSchId);

}