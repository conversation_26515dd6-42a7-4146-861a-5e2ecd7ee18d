package com.zkteco.zkbiosecurity.att.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ScheduledFuture;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.redis.redisson.annotation.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.api.vo.AttApiLaveHolidayItem;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttLeaveDao;
import com.zkteco.zkbiosecurity.att.dao.AttLeaveTypeDao;
import com.zkteco.zkbiosecurity.att.dao.AttPersonDao;
import com.zkteco.zkbiosecurity.att.model.AttLeave;
import com.zkteco.zkbiosecurity.att.model.AttLeaveType;
import com.zkteco.zkbiosecurity.att.model.AttPerson;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttAnnualLeaveReportItem;
import com.zkteco.zkbiosecurity.att.vo.AttAnnualLeaveRuleItem;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveItem;
import com.zkteco.zkbiosecurity.att.vo.AttPersonItem;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonSelectItem;
import com.zkteco.zkbiosecurity.scheduler.ScheduleService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

/**
 * 出勤异常表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/8/17 13:36
 */
@Slf4j
@Service
public class AttAnnualLeaveReportServiceImpl implements AttAnnualLeaveReportService {

    @Autowired
    private ScheduleService scheduleService;
    private ScheduledFuture<?> scedulefuture;

    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttRecordService attRecordService;
    @Autowired
    private AttPersonDao attPersonDao;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AttLeaveService attLeaveService;
    @Autowired
    private AttLeaveDao attLeaveDao;
    @Autowired
    private AttLeaveTypeDao attLeaveTypeDao;

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, AttAnnualLeaveReportItem condition, int pageNo,
        int pageSize) {

        buildCondition(sessionId, condition);
        Pager pager =
            attLeaveDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);

        List<AttAnnualLeaveReportItem> list = (List<AttAnnualLeaveReportItem>)pager.getData();
        buildItems(list);

        pager.setData(list);
        return pager;
    }

    private void buildItems(List<AttAnnualLeaveReportItem> list) {

        // 年假清零发放日期
        Date calculateDate = attParamService.getAnnualLeaveCalculateDate();
        // 年假有效截止日期
        Date validEndDate = DateUtil.addYear(calculateDate, 1);

        // 查询所有年假
        Collection<String> pinList = CollectionUtil.getPropertyList(list, AttAnnualLeaveReportItem::getPersPin, "-1");
        List<AttLeaveItem> attLeaveItemList =
            attLeaveService.getByPersonPinAndLeaveTypeIdAndDate(pinList, "L5", calculateDate, validEndDate);
        Map<String, List<AttLeaveItem>> pinLeaveMap = new HashMap<>();
        if (!CollectionUtil.isEmpty(attLeaveItemList)) {
            pinLeaveMap = attLeaveItemList.stream().collect(Collectors.groupingBy(AttLeaveItem::getPersonPin));
        }

        // 组装对象
        for (AttAnnualLeaveReportItem item : list) {

            Integer days = item.getAnnualAdjustDays() == null ? item.getAnnualLeaveDays() : item.getAnnualAdjustDays();

            // 有效期
            if (item.getAnnualValidDate() != null && days != null) {
                item.setAnnualValidDate(item.getAnnualValidDate());
                // 发放日期为2020-01-01，有效截止日期为2019-12-31，减一天
                item.setAnnualValidDateStr(attParamService.dateToLocaleString(item.getAnnualValidDate()) + " "
                    + I18nUtil.i18nCode("att_flowable_datetime_to") + " "
                    + attParamService.dateToLocaleString(DateUtil.addDay(validEndDate, -1)));
            }

            // 计算当前年假结余
            if (item.getAnnualValidDate() != null && days != null) {

                List<AttLeaveItem> leaveList = pinLeaveMap.get(item.getPersPin());
                if (leaveList != null) {
                    float count = 0;
                    for (AttLeaveItem attLeave : leaveList) {
                        if (attLeave.getStartDatetime().compareTo(item.getAnnualValidDate()) > 0) {
                            count += attLeave.getDays();
                        }
                    }
                    BigDecimal f = new BigDecimal((float)days - count);
                    float annualRemaining = f.setScale(1, BigDecimal.ROUND_HALF_UP).floatValue();
                    item.setAnnualRemaining(annualRemaining > 0 ? annualRemaining : 0);
                } else {
                    item.setAnnualRemaining((float)days);
                }
            }
        }
    }

    @Override
    public List<AttAnnualLeaveReportItem> getItemData(String sessionId, AttAnnualLeaveReportItem condition,
        int beginIndex, int endIndex) {
        buildCondition(sessionId, condition);
        List<AttAnnualLeaveReportItem> list = (List<AttAnnualLeaveReportItem>)attPersonDao
            .getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
        buildItems(list);
        return list;
    }

    private void buildCondition(String sessionId, AttAnnualLeaveReportItem condition) {
        // 封装部门条件
        if (StringUtils.isBlank(condition.getInDeptId())) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, condition.getDeptId(),
                condition.getDeptCode(), condition.getDeptName()));
            condition.setDeptId(null);
        } else if (StringUtils.isNotBlank(sessionId)) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByAuthAndIds(sessionId, condition.getInDeptId()));
            condition.setDeptId(null);
        }
    }

    @Override
    @Transactional
    public void setAnnualLeaveScheduled(String calculateMonth, String calculateDay) {
        // 启用年假
        if (attParamService.enableAnnualLeave()) {
            if (scedulefuture != null) {
                scedulefuture.cancel(true);
            }

            // 这边要大于1分钟，解决getAnnualLeaveCalculateDate这个获取开始时间的判断问题
            scedulefuture = scheduleService.startScheduleTask(() -> {
                annualLeaveScheduled();
            }, "0 1 0 " + calculateDay + " " + calculateMonth + " *");
        }
    }

    @Override
    @Transactional
    @RedisLock(lockName = "annualLeaveScheduledRedisson", waitTime = 0, expire = 600)
    public void initAnnualLeaveScheduled() {
        // 启用年假
        try {
            if (attParamService.enableAnnualLeave()) {
                String calculateMonth = attParamService.getAnnualLeaveCalculateMonth();
                String calculateDay = attParamService.getAnnualLeaveCalculateDay();
                setAnnualLeaveScheduled(calculateMonth, calculateDay);
            }
        } catch (Exception e) {
            log.error("Att initAnnualLeaveScheduled Exception", e);
        }
    }

    /**
     * 定时器执行
     * <p>
     * 1、计算人员年假总数</br>
     * 2、保存当前统计的日期，用于查询请假年假的开始时间
     * </p>
     *
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/30 9:49
     * @since 1.0.0
     */
    private void annualLeaveScheduled() {

        Date annualLeaveCalculateDate = attParamService.getAnnualLeaveCalculateDate();

        // 计算人员年假总数
        String calculateType = attParamService.getAnnualLeaveCalculateType();
        String rule = attParamService.getAnnualLeaveCalculateRule();
        List<AttAnnualLeaveRuleItem> attAnnualLeaveRuleItemList = buildRuleItem(rule);
        // 人员分批处理
        List<AttPersonItem> attPersonItemList = attPersonService.getByCondition(new AttPersonItem());
        List<List<AttPersonItem>> groupList = CollectionUtil.split(attPersonItemList, 500);
        for (List<AttPersonItem> personList : groupList) {
            for (AttPersonItem attPersonItem : personList) {
                if (attPersonItem.getHireDate() != null) {
                    Map<String, String> annualLeaveMap = calculateAnnualLeaveDays(attPersonItem.getHireDate(),
                        calculateType, attAnnualLeaveRuleItemList, annualLeaveCalculateDate);
                    attPersonItem.setAnnualLeaveDays(Integer.valueOf(annualLeaveMap.get("days")));
                    attPersonItem.setAnnualValidDate(
                        DateUtil.stringToDate(annualLeaveMap.get("validDate"), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                    attPersonItem.setWorkLong(annualLeaveMap.get("workLong"));
                    attPersonItem.setAnnualAdjustDays(null);
                } else {
                    attPersonItem.setAnnualLeaveDays(null);
                    attPersonItem.setWorkLong("");
                    attPersonItem.setAnnualValidDate(null);
                    attPersonItem.setAnnualAdjustDays(null);
                }
                attPersonService.saveItem(attPersonItem);
            }
        }
    }

    public Map<String, String> calculateAnnualLeaveDays(Date hireDate, String calculateType,
        List<AttAnnualLeaveRuleItem> attAnnualLeaveRuleItemList, Date calculateDate) {

        boolean enableCalculateType = attParamService.enableCalculateType();

        // 假设按工龄2年8个月来计算
        Map<String, Integer> workLongMap = calculateWorkLong(hireDate, calculateDate);
        // 当前计算的工龄总年数：2
        int nowYearNum = workLongMap.get("year");
        // 前计算的工龄月数：8
        int nowMonthNum = workLongMap.get("month");

        // 未满一个月无年假
        if (nowYearNum == 0 && nowMonthNum == 0) {
            Map<String, String> annualLeaveMap = new HashMap<>();
            annualLeaveMap.put("days", "0");
            annualLeaveMap.put("validDate", DateUtil.dateToString(hireDate, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
            return annualLeaveMap;
        }

        BigDecimal days = BigDecimal.ZERO;

        if (enableCalculateType) {

            if (nowMonthNum == 0) {
                AttAnnualLeaveRuleItem ruleItem = getRuleItemByYear(nowYearNum - 1, attAnnualLeaveRuleItemList);
                if (ruleItem != null) {
                    days = days.add(BigDecimal.valueOf(Integer.parseInt(ruleItem.getDays())));
                }
            } else {
                AttAnnualLeaveRuleItem ruleItem = getRuleItemByYear(nowYearNum, attAnnualLeaveRuleItemList);
                // 过滤未设置该年份周期的规则
                if (ruleItem != null) {
                    days = days.add(calculateDays(nowMonthNum, ruleItem, calculateType));
                    if (nowYearNum >= 1) {
                        // 工龄大于1年时，计算剩余月份年假
                        int nowMonthNumExt = 12 - nowMonthNum;
                        AttAnnualLeaveRuleItem ruleItemExt =
                            getRuleItemByYear(nowYearNum - 1, attAnnualLeaveRuleItemList);
                        if (ruleItemExt != null) {
                            days = days.add(calculateDays(nowMonthNumExt, ruleItemExt, calculateType));
                        }
                    }
                }
                days = days.setScale(0, AttCommonUtils.getRoundingMode(calculateType));
            }
        } else {
            AttAnnualLeaveRuleItem ruleItem = getRuleItemByYear(nowYearNum, attAnnualLeaveRuleItemList);
            if (ruleItem != null) {
                days = days.add(BigDecimal.valueOf(Integer.parseInt(ruleItem.getDays())));
            }
        }

        // 年假有效截止日期
        Date validEndDate = DateUtil.addYear(calculateDate, 1);
        Map<String, String> annualLeaveMap = new HashMap<>();
        annualLeaveMap.put("days", days.intValue() + "");
        Date validDate = getValidDate(validEndDate, hireDate, nowYearNum, attAnnualLeaveRuleItemList);
        annualLeaveMap.put("validDate", DateUtil.dateToString(validDate, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        // annualLeaveMap.put("endDate", DateUtil.dateToString(validEndDate, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        annualLeaveMap.put("workLong",
            nowYearNum + I18nUtil.i18nCode("base_cron_year") + nowMonthNum + I18nUtil.i18nCode("common_month"));
        return annualLeaveMap;
    }

    /**
     * 获取有效日期,如果年假规则配置天数为0，有效期必须是入职满整年才可以休假
     *
     * @return java.util.Date
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/1/7 11:48
     * @since 1.0.0
     */
    private Date getValidDate(Date calculateDate, Date hireDate, int year,
        List<AttAnnualLeaveRuleItem> attAnnualLeaveRuleItemList) {
        // 年假有效开始时间，有效期默认是这个季度的开始时间。
        Date validDate = DateUtil.addYear(calculateDate, -1);
        AttAnnualLeaveRuleItem ruleItem = getRuleItemByYear(year, attAnnualLeaveRuleItemList);
        if (ruleItem != null && "0".equals(ruleItem.getDays())) {
            validDate = DateUtil.setYears(hireDate, DateUtil.getYear(calculateDate));
            if (validDate.compareTo(calculateDate) >= 0) {
                validDate = DateUtil.addYear(validDate, -1);
            }
        }
        return validDate;
    }

    /**
     * 根据年份匹配年假天数发放规则
     *
     * @return com.zkteco.zkbiosecurity.att.vo.AttAnnualLeaveRuleItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/1/7 8:55
     * @since 1.0.0
     */
    private AttAnnualLeaveRuleItem getRuleItemByYear(int year,
        List<AttAnnualLeaveRuleItem> attAnnualLeaveRuleItemList) {
        for (AttAnnualLeaveRuleItem ruleItem : attAnnualLeaveRuleItemList) {
            int startYear = Integer.parseInt(ruleItem.getStartYear());
            int endYear = Integer.parseInt(ruleItem.getEndYear());
            if (year >= startYear && year < endYear) {
                return ruleItem;
            }
            /*startYear = startYear == 0 ? -1 : startYear;
            int endYear = Integer.parseInt(ruleItem.getEndYear());
            if (year > startYear && year <= endYear) {
                return ruleItem;
            }*/
        }
        return null;
    }

    /**
     * 年假按月份比例和舍入计算
     *
     * @param month:
     * @param ruleItem:
     * @return java.math.BigDecimal
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/1/7 8:59
     * @since 1.0.0
     */
    private BigDecimal calculateDays(int month, AttAnnualLeaveRuleItem ruleItem, String calculateType) {
        return BigDecimal.valueOf(month).divide(BigDecimal.valueOf(12), 2, RoundingMode.HALF_UP)
            .multiply(BigDecimal.valueOf(Integer.parseInt(ruleItem.getDays())));
    }

    /**
     * 根据入职日期计算工龄
     *
     * @param hireDate: 入职日期
     * @param calculateDate: 计算工龄传入当前日期，计算年假需要传入、年假清零发放日期
     * @return java.util.Map<java.lang.String,java.lang.Integer>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/10/9 13:46
     * @since 1.0.0
     */
    private Map<String, Integer> calculateWorkLong(Date hireDate, Date calculateDate) {

        if (hireDate == null || hireDate.compareTo(calculateDate) > 0) {
            Map<String, Integer> workLongMap = new HashMap<>();
            workLongMap.put("year", 0);
            workLongMap.put("month", 0);
            return workLongMap;
        }

        // 入职日期
        Calendar hireCalendar = Calendar.getInstance();
        hireCalendar.setTime(hireDate);
        int hireYear = hireCalendar.get(Calendar.YEAR);
        int hireMonth = hireCalendar.get(Calendar.MONTH) + 1;

        // 当前日期
        Calendar nowCalendar = Calendar.getInstance();
        nowCalendar.setTime(calculateDate);
        int nowYear = nowCalendar.get(Calendar.YEAR);
        int nowMonth = nowCalendar.get(Calendar.MONTH) + 1;

        // 入职总月份
        int allMonthNum = (nowYear - hireYear) * 12 + (nowMonth - hireMonth);

        // 当前工龄月份
        int month = allMonthNum % 12;
        // 当前工龄年份
        int year = allMonthNum / 12;

        Map<String, Integer> workLongMap = new HashMap<>();
        workLongMap.put("year", year);
        workLongMap.put("month", month);
        return workLongMap;
    }

    @Override
    public List<AttAnnualLeaveRuleItem> buildRuleItem(String attAnnualLeaveRule) {
        List<AttAnnualLeaveRuleItem> attAnnualLeaveRuleItemList = new ArrayList<>();
        for (String rule : attAnnualLeaveRule.split(",")) {
            String[] ruleArray = rule.split("-");
            if (ruleArray != null) {
                int size = ruleArray.length;
                AttAnnualLeaveRuleItem annualLeaveRuleItem = new AttAnnualLeaveRuleItem();
                annualLeaveRuleItem.setStartYear(size > 0 ? ruleArray[0] : "");
                annualLeaveRuleItem.setEndYear(size > 1 ? ruleArray[1] : "");
                annualLeaveRuleItem.setDays(size > 2 ? ruleArray[2] : "");
                attAnnualLeaveRuleItemList.add(annualLeaveRuleItem);
            }
        }
        attAnnualLeaveRuleItemList.sort((o1, o2) -> {
            if (o1.getStartYear().equals(o2.getStartYear())) {
                return Integer.valueOf(o1.getEndYear()) > Integer.valueOf(o2.getEndYear()) ? 1 : -1;
            } else {
                return Integer.valueOf(o1.getStartYear()) > Integer.valueOf(o2.getStartYear()) ? 1 : -1;
            }
        });
        return attAnnualLeaveRuleItemList;
    }

    @Override
    public void buildRuleValue(Map<String, String> params) {
        // 启用年假
        Set<String> keySet = new HashSet<>();
        for (String key : params.keySet()) {
            if (key.startsWith("attAnnualLeaveRule")) {
                String keyEx = key.split("].")[0];
                keySet.add(keyEx);
            }
        }
        if (keySet.size() > 0) {
            List<AttAnnualLeaveRuleItem> attAnnualLeaveRuleItemList = new ArrayList<>();
            for (String key : keySet) {
                String startYear = params.get(key + "].startYear");
                String endYear = params.get(key + "].endYear");
                String days = params.get(key + "].days");
                if (StringUtils.isNotBlank(startYear) && StringUtils.isNotBlank(endYear)) {
                    AttAnnualLeaveRuleItem annualLeaveRuleItem = new AttAnnualLeaveRuleItem();
                    annualLeaveRuleItem.setStartYear(startYear);
                    annualLeaveRuleItem.setEndYear(endYear);
                    annualLeaveRuleItem.setDays(StringUtils.isNoneBlank(days) ? days : "0");
                    attAnnualLeaveRuleItemList.add(annualLeaveRuleItem);
                }
            }
            if (attAnnualLeaveRuleItemList.size() > 0) {
                attAnnualLeaveRuleItemList.sort((o1, o2) -> StringUtils.compare(o1.getStartYear(), o1.getStartYear()));
                StringBuffer stringBuffer = new StringBuffer();
                for (AttAnnualLeaveRuleItem ruleItem : attAnnualLeaveRuleItemList) {
                    stringBuffer.append(ruleItem.getStartYear()).append("-").append(ruleItem.getEndYear()).append("-")
                        .append(ruleItem.getDays()).append(",");
                }
                stringBuffer.deleteCharAt(stringBuffer.length() - 1);
                params.put("att.annualLeave.rule", stringBuffer.toString());
            }
        }
    }

    @Override
    public ZKResultMsg getDetail(String personId) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        List<Map<String, String>> leaveDetailList = new ArrayList<>();
        AttPersonItem attPersonItem = attPersonService.getItemById(personId);
        if (attPersonItem != null) {

            // 年假清零发放日期
            Date calculateDate = attParamService.getAnnualLeaveCalculateDate();
            // 年假有效截止日期
            Date validEndDate = DateUtil.addYear(calculateDate, 1);

            // 查询所有年假申请
            AttLeaveType attLeaveType = attLeaveTypeDao.findByLeaveTypeNo("L5");
            List<AttLeave> attLeaveList =
                attLeaveDao.findByPersonPinAndLeaveTypeIdAndDate(CollectionUtil.strToList(attPersonItem.getPersonPin()),
                    attLeaveType.getId(), attPersonItem.getAnnualValidDate(), validEndDate);

            for (AttLeave attLeave : attLeaveList) {
                if (attLeave.getStartDatetime().compareTo(attPersonItem.getAnnualValidDate()) > 0) {
                    Map<String, String> leaveDetail = new HashMap<>();
                    leaveDetail.put("personPin", attPersonItem.getPersonPin());
                    leaveDetail.put("personName", attPersonItem.getPersonName());
                    leaveDetail.put("startTime", AttDateUtils.dateToStrAsShort(attLeave.getStartDatetime()));
                    leaveDetail.put("endTime", AttDateUtils.dateToStrAsShort(attLeave.getEndDatetime()));
                    leaveDetail.put("remarks", I18nUtil.i18nCode("att_annualLeave_useDays", attLeave.getDays()));
                    leaveDetailList.add(leaveDetail);
                }
            }

            // 调整年假
            if (attPersonItem.getAnnualAdjustDays() != null) {
                Map<String, String> adjustDetailMap = new HashMap<>();
                adjustDetailMap.put("personPin", attPersonItem.getPersonPin());
                adjustDetailMap.put("personName", attPersonItem.getPersonName());
                adjustDetailMap.put("startTime", AttDateUtils.dateToStrAsShort(attPersonItem.getAnnualValidDate()));
                adjustDetailMap.put("endTime", AttDateUtils.dateToStrAsShort(DateUtil.addDay(validEndDate, -1)));
                adjustDetailMap.put("remarks", I18nUtil.i18nCode("att_annualLeave_adjustDay")
                    + attPersonItem.getAnnualAdjustDays() + I18nUtil.i18nCode("common_days"));
                leaveDetailList.add(adjustDetailMap);
            }

            // 发放年假
            if (attPersonItem.getAnnualLeaveDays() != null) {
                Map<String, String> leaveDetailMap = new HashMap<>();
                leaveDetailMap.put("personPin", attPersonItem.getPersonPin());
                leaveDetailMap.put("personName", attPersonItem.getPersonName());
                leaveDetailMap.put("startTime", AttDateUtils.dateToStrAsShort(attPersonItem.getAnnualValidDate()));
                leaveDetailMap.put("endTime", AttDateUtils.dateToStrAsShort(DateUtil.addDay(validEndDate, -1)));
                leaveDetailMap.put("remarks",
                    I18nUtil.i18nCode("att_annualLeave_calculateDays", attPersonItem.getAnnualLeaveDays()));
                leaveDetailList.add(leaveDetailMap);
            }
        }
        resultMsg.setData(leaveDetailList);
        return resultMsg;
    }

    @Override
    public ZKResultMsg getDetailByPersonPin(String personPin) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        AttPersonItem attPerson = attPersonService.getItemByPersonPin(personPin);
        AttApiLaveHolidayItem attApiLaveHolidayItem = new AttApiLaveHolidayItem();
        if (attPerson != null) {
            attApiLaveHolidayItem.setHireDate(attPerson.getHireDate());

            // 年假清零发放日期
            Date calculateDate = attParamService.getAnnualLeaveCalculateDate();
            // 年假有效截止日期
            Date validEndDate = DateUtil.addYear(calculateDate, 1);

            // 查询所有年假申请
            AttLeaveType attLeaveType = attLeaveTypeDao.findByLeaveTypeNo("L5");
            List<AttLeave> attLeaveList =
                attLeaveDao.findByPinAndTypeIdAndDate(CollectionUtil.strToList(attPerson.getPersonPin()),
                    attLeaveType.getId(), attPerson.getAnnualValidDate(), validEndDate);
            Float useDay = 0.0F;
            Float approvedDay = 0.0F;
            for (AttLeave attLeave : attLeaveList) {
                if (attLeave.getStartDatetime().compareTo(attPerson.getAnnualValidDate()) > 0) {
                    if (AttConstant.FLOW_STATUS_COMPLETE.equals(attLeave.getFlowStatus())) {
                        useDay += attLeave.getDays();
                    } else if (AttConstant.FLOW_STATUS_CREATE.equals(attLeave.getFlowStatus())) {
                        approvedDay += attLeave.getDays();
                    }
                }
            }

            Integer annualLeaveDays = attPerson.getAnnualAdjustDays() == null ? attPerson.getAnnualLeaveDays()
                : attPerson.getAnnualAdjustDays();

            attApiLaveHolidayItem.setTotal(annualLeaveDays + ".0");
            attApiLaveHolidayItem.setApprovedDay(approvedDay + "");
            attApiLaveHolidayItem.setUseDay(useDay + "");

            Float lessDay = annualLeaveDays - useDay - approvedDay;
            attApiLaveHolidayItem.setLessDay(lessDay < 0 ? "0.0" : lessDay + "");
            attApiLaveHolidayItem.setTimeUnit("Day");
            attApiLaveHolidayItem.setTimeUnitName(I18nUtil.i18nCode("common_days"));
            attApiLaveHolidayItem.setStartDate(AttDateUtils.dateToStrAsShort(attPerson.getAnnualValidDate()));
            attApiLaveHolidayItem.setEndDate(AttDateUtils.dateToStrAsShort(DateUtil.addDay(validEndDate, -1)));
        }
        resultMsg.setData(attApiLaveHolidayItem);
        return resultMsg;
    }

    @Override
    public Pager getItemsByPage(BaseItem baseItem, int i, int i1) {
        return null;
    }

    @Override
    public boolean deleteByIds(String s) {
        return false;
    }

    @Override
    public ZKResultMsg recalculate(String ids, String type) {
        // 计算人员年假总数
        String calculateType = attParamService.getAnnualLeaveCalculateType();
        String rule = attParamService.getAnnualLeaveCalculateRule();
        List<AttAnnualLeaveRuleItem> attAnnualLeaveRuleItemList = buildRuleItem(rule);
        Date annualLeaveCalculateDate = attParamService.getAnnualLeaveCalculateDate();

        // 人员分批处理
        List<AttPersonItem> attPersonItemList = null;
        if ("person".equals(type)) {
            attPersonItemList = attPersonService.getItemByIds(ids);

        } else if ("dept".equals(type)) {
            List<String> deptIdList = attPersonService.getAllChildDeptById(ids);
            deptIdList = deptIdList.stream().distinct().collect(Collectors.toList());
            String deptIds = StringUtils.join(deptIdList.toArray(), ",");
            attPersonItemList = attPersonService.getItemByDeptIds(deptIds);
        }
        if (attPersonItemList == null) {
            return ZKResultMsg.successMsg();
        }
        List<List<AttPersonItem>> groupList = CollectionUtil.split(attPersonItemList, 500);
        for (List<AttPersonItem> personList : groupList) {
            for (AttPersonItem attPersonItem : personList) {
                if (attPersonItem.getHireDate() != null) {
                    Map<String, String> annualLeaveMap = calculateAnnualLeaveDays(attPersonItem.getHireDate(),
                        calculateType, attAnnualLeaveRuleItemList, annualLeaveCalculateDate);
                    attPersonItem.setAnnualLeaveDays(Integer.valueOf(annualLeaveMap.get("days")));
                    attPersonItem.setAnnualValidDate(
                        DateUtil.stringToDate(annualLeaveMap.get("validDate"), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                    attPersonItem.setWorkLong(annualLeaveMap.get("workLong"));
                } else {
                    attPersonItem.setAnnualLeaveDays(null);
                    attPersonItem.setWorkLong("");
                    if (attPersonItem.getAnnualAdjustDays() == null) {
                        attPersonItem.setAnnualValidDate(null);
                    }
                }

                // 不保存人事人员属性
                setNullPersPersonInfo(attPersonItem);
                attPersonService.saveItem(attPersonItem);
            }
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    public ZKResultMsg adjust(String ids, Integer adjustDays, String type) {

        List<AttPersonItem> attPersonItemList = null;
        if ("person".equals(type)) {
            attPersonItemList = attPersonService.getItemByIds(ids);
        } else if ("dept".equals(type)) {
            List<String> deptIdList = attPersonService.getAllChildDeptById(ids);
            deptIdList = deptIdList.stream().distinct().collect(Collectors.toList());
            String deptIds = StringUtils.join(deptIdList.toArray(), ",");
            attPersonItemList = attPersonService.getItemByDeptIds(deptIds);
        }
        if (attPersonItemList == null) {
            return ZKResultMsg.successMsg();
        }
        List<List<AttPersonItem>> groupList = CollectionUtil.split(attPersonItemList, 500);
        for (List<AttPersonItem> personList : groupList) {
            for (AttPersonItem attPersonItem : personList) {

                attPersonItem.setAnnualAdjustDays(adjustDays);
                // 年假清零发放日期
                if (attPersonItem.getAnnualValidDate() == null) {
                    Date calculateDate = attParamService.getAnnualLeaveCalculateDate();
                    attPersonItem.setAnnualValidDate(calculateDate);
                }

                // 不保存人事人员属性
                setNullPersPersonInfo(attPersonItem);
                attPersonService.saveItem(attPersonItem);
            }
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 人事的人员属性置空，不保存
     *
     * @param attPersonItem:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/6/25 17:50
     * @since 1.0.0
     */
    private void setNullPersPersonInfo(AttPersonItem attPersonItem) {
        attPersonItem.setDeptId(null);
        attPersonItem.setDeptCode(null);
        attPersonItem.setDeptName(null);
        attPersonItem.setHireDate(null);
        attPersonItem.setPersonName(null);
        attPersonItem.setPersonLastName(null);
    }
}
