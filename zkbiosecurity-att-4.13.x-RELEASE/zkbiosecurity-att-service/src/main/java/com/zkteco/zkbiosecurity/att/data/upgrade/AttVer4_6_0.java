package com.zkteco.zkbiosecurity.att.data.upgrade;

import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * 升级到4.6.0
 * <p>1、新增参数个人敏感信息保护-考勤照片</p>
 *
 */
@Slf4j
@Component
public class AttVer4_6_0 implements UpgradeVersionManager {

    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public String getModule() {
        return BaseConstants.ATT;
    }

    @Override
    public String getVersion() {
        return "v4.6.0";
    }

    @Override
    public boolean executeUpgrade() {

        baseSysParamService.initData(new BaseSysParamItem("att.photo.encryptProp", "true", "人敏感信息保护-考勤照片"));

        return true;
    }

}
