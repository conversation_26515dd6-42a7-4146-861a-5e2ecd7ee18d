package com.zkteco.zkbiosecurity.att.model;

import java.io.Serializable;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.Set;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 临时排班（人员、部门、分组）
 */
@Entity
@Table(name = "ATT_TEMPSCH")
@Setter
@Getter
@Accessors(chain = true)
public class AttTempSch extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 起始日期
     */
    @Column(name = "START_DATE")
    private Date startDate;

    /**
     * 结束日期
     */
    @Column(name = "END_DATE")
    private Date endDate;

    /**
     * 排班类型
     */
    @Column(name = "SCHEDULE_TYPE")
    private Short scheduleType;

    /**
     * 临时类型（分组=0、部门=1、人员=2）
     */
    @Column(name = "TEMP_TYPE")
    private Short tempType;

    /**
     * 工作类型（正常上班/周末加班/节假日加班）
     */
    @Column(name = "SHIFT_WORKTYPE")
    private String workType;

    /**
     * 考勤方式（0/按班次正常刷卡，1/一天内任刷一次有效卡，2/只计算刷卡时间，3/免刷卡）
     */
    @Column(name = "ATTENDANCE_MODE")
    private Short attendanceMode;

    /**
     * 加班方式（0/电脑自动计算，1/加班必须申请，2/必须加班否则旷工，3/时长为较小者-延后加班和加班单对比，4/不算加班）
     */
    @Column(name = "OVERTIME_MODE")
    private Short overtimeMode;

    /**
     * 加班标记（0/平时，1/休息日，2/节假日）
     */
    @Column(name = "OVERTIME_REMARK")
    private Short overtimeRemark;

    /**
     * 分组id
     */
    @Column(name = "GROUP_ID", length = 50)
    private String groupId;

    /**
     * 部门id
     */
    @Column(name = "AUTH_DEPT_ID", length = 50)
    private String deptId;

    /**
     * 人员id
     */
    @Column(name = "PERS_PERSON_ID", length = 50)
    private String personId;

    /**
     * 人员编号
     */
    @Column(name = "PERS_PERSON_PIN", length = 50)
    private String personPin;

    /**
     * 班次临时排班中间表
     */
    // @ManyToMany(targetEntity = AttShift.class, fetch = FetchType.LAZY)
    // @JoinTable(name = "ATT_TEMPSCH_SHIFT", joinColumns = @JoinColumn(name = "TEMPSCH_ID", referencedColumnName =
    // "ID"),
    // inverseJoinColumns = @JoinColumn(name = "SHIFT_ID", referencedColumnName = "ID"))
    // private Set<AttShift> attShiftSet = new LinkedHashSet<>();

    /**
     * 时间段与临时排班中间表
     */
    @ManyToMany(targetEntity = AttTimeSlot.class, fetch = FetchType.LAZY)
    @JoinTable(name = "ATT_TEMPSCH_TIMESLOT",
        joinColumns = @JoinColumn(name = "TEMPSCH_ID", referencedColumnName = "ID"),
        inverseJoinColumns = @JoinColumn(name = "TIMESLOT_ID", referencedColumnName = "ID"))
    private Set<AttTimeSlot> attTimeSlotSet = new LinkedHashSet<>();
}