package com.zkteco.zkbiosecurity.att.dao;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.zkteco.zkbiosecurity.att.model.AttDevice;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 设备
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:34
 * @since 1.0.0
 */
public interface AttDeviceDao extends BaseDao<AttDevice, String> {

    AttDevice findByDevName(String name);

    AttDevice findByDevSn(String sn);

    List<AttDevice> findByDevSnIn(Collection<String> devSnList);

    AttDevice findByIpAddress(String ipAdress);

    List<AttDevice> findByAreaId(String areaId);

    List<AttDevice> findByAreaIdAndDevSnNotIn(String areaId, Collection<String> devSn);

    /**
     * 获取可用的设备点数
     * 
     * @return
     */
    @Query("select count(id) from AttDevice ad where ad.status=:status")
    int findEnableCount(@Param("status") Boolean status);

    long countByAreaIdIn(Collection<String> areaIdList);

    List<AttDevice> findByAreaIdIn(Collection<String> areaIdList);

    Long countByAreaId(String areaId);
}