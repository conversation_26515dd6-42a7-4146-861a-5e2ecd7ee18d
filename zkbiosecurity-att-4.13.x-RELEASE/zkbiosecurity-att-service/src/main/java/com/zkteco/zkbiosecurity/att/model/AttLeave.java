package com.zkteco.zkbiosecurity.att.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 请假
 */
@Entity
@Table(name = "ATT_LEAVE")
@Setter
@Getter
@Accessors(chain = true)
public class AttLeave extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门id
     */
    @Column(name = "AUTH_DEPT_ID", length = 50)
    private String deptId;

    /**
     * 部门编号（数据库不保存部门编号，查询转化成ID查询，列表获取最新部门编号展示）
     */
    @Deprecated
    @Column(name = "AUTH_DEPT_CODE", length = 100)
    private String deptCode;

    /**
     * 部门名称（数据库不保存部门名，查询转化成ID查询，列表获取最新部门名称展示）
     */
    @Deprecated
    @Column(name = "AUTH_DEPT_NAME", length = 100)
    private String deptName;

    /**
     * 人员id
     */
    @Column(name = "PERS_PERSON_ID", length = 50)
    private String personId;

    /**
     * 人员编号
     */
    @Column(name = "PERS_PERSON_PIN", length = 50)
    private String personPin;

    /**
     * 姓名
     */
    @Deprecated
    @Column(name = "PERS_PERSON_NAME", length = 50)
    private String personName;

    /**
     * 英文（lastName）
     */
    @Deprecated
    @Column(name = "PERS_PERSON_LAST_NAME", length = 50)
    private String personLastName;

    /**
     * 假种id
     */
    @Column(name = "LEAVETYPE_ID", length = 50)
    private String leaveTypeId;

    /**
     * 请假时长（分）
     */
    @Column(name = "LEAVE_LONG")
    private Integer leaveLong;

    /**
     * 请假时长（天）
     */
    @Column(name = "DAYS")
    private Float days;

    /**
     * 开始日期时间
     */
    @Column(name = "START_DATETIME")
    private Date startDatetime;

    /**
     * 结束日期时间
     */
    @Column(name = "END_DATETIME")
    private Date endDatetime;

    /**
     * 备注
     */
    @Column(name = "REMARK")
    private String remark;

    /**
     * 请假图片保存路径
     */
    @Column(name = "LEAVE_IMAGE_PATH", length = 2000)
    private String leaveImagePath;

    @Column(name = "CLOUD_IMAGE_URL", length = 2000)
    private String cloudImageUrl;

    /**
     * 流程业务ID,全局唯一
     */
    @Column(name = "BUSINESS_KEY", length = 50)
    private String businessKey;

    /**
     * 流程状态
     */
    @Column(name = "FLOW_STATUS")
    private String flowStatus;
}
