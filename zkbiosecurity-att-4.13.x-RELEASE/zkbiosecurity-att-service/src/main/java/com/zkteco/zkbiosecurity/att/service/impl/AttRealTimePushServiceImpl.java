package com.zkteco.zkbiosecurity.att.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.service.AttRealTimePushService;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveItem;
import com.zkteco.zkbiosecurity.att.vo.AttTransactionItem;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.FileEncryptUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonCacheService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 考勤记录实时推送
 *
 * <AUTHOR>
 * @date 2020-05-27 10:37
 * @since 1.0.0
 */
@Service
public class AttRealTimePushServiceImpl implements AttRealTimePushService {

    @Autowired
    private RedisTemplate<String, JSONObject> redisTemplate;
    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private AttParamService attParamService;
    @Autowired
    private PersPersonCacheService persPersonCacheService;

    @Override
    public void pushTransaction(List<AttTransactionItem> attTransactionItemList) {

         // 推送场景
         for (AttTransactionItem attTransactionItem : attTransactionItemList) {
             String pin = attTransactionItem.getPersonPin();

             JSONObject jsonObject = new JSONObject();
             jsonObject.put("name", attTransactionItem.getPersonName());
             jsonObject.put("pin", pin);
             jsonObject.put("department", attTransactionItem.getDeptName());
             jsonObject.put("datetime", DateUtil.dateToString(attTransactionItem.getAttDatetime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));

             PersPersonCacheItem persPersonCacheItem = persPersonCacheService.getPersonCacheByPin(pin);
             if (persPersonCacheItem != null && StringUtils.isNoneBlank(persPersonCacheItem.getPhotoPath())) {
                 jsonObject.put("imageSrc", "data:image/jpg;base64," + FileEncryptUtil.getDecryptFileBase64(persPersonCacheItem.getPhotoPath()));
             }
             jsonObject.put("deptId", attTransactionItem.getDeptId());
             jsonObject.put("deviceId", attTransactionItem.getDeviceId());
             messagingTemplate.convertAndSend("/topic/noSenseAtt/realTimeSign", jsonObject);
         }

        Map<String, String> attParams = attParamService.getAttParams();
        String rollCall = attParams.get("att.realTime.rollCall");
        // 开启实时点名才进行实时推送 0 启用实时点名
        if (StringUtils.isNotBlank(rollCall) && "0".equals(rollCall)) {
            if (!CollectionUtils.isEmpty(attTransactionItemList)) {
                for (AttTransactionItem attTransaction : attTransactionItemList) {
                    // 当天数据才会推送
                    if (DateUtil.getTodayBeginTime().getTime() <= attTransaction.getAttDatetime().getTime()) {
                        JSONObject item = new JSONObject();
                        item.put("pin", attTransaction.getPersonPin());
                        item.put("deptId", attTransaction.getDeptId());
                        item.put("attDatetime", attTransaction.getAttDatetime());
                        item.put("pushType", "transaction");
                        JSONArray data = new JSONArray();
                        data.add(attTransaction.getPersonPin());
                        data.add(attTransaction.getPersonName());
                        data.add(attTransaction.getDeptCode());
                        data.add(attTransaction.getDeptName());
                        data.add(AttDateUtils.dateToStrAsLong(attTransaction.getAttDatetime()));
                        data.add(I18nUtil.i18nCode("att_realTime_signPers"));
                        item.put("data", data);
                        redisTemplate.convertAndSend(AttConstant.ATT_REALTIMEPUSH_TOPIC, item);
                    }
                }
            }
        }
    }

    @Override
    public void pushLeave(AttLeaveItem attLeave) {
        
        PersPersonCacheItem persPersonCacheItem = persPersonCacheService.getPersonCacheByPin(attLeave.getPersonPin());
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("name", persPersonCacheItem.getName());
//        jsonObject.put("pin", persPersonCacheItem.getPin());
//        jsonObject.put("department", persPersonCacheItem.getDeptName());
//        jsonObject.put("datetime", I18nUtil.i18nCode("att_leftMenu_leave"));
//        if(StringUtils.isNotBlank(persPersonCacheItem.getPhotoPath())) {
//            jsonObject.put("imageSrc", "data:image/jpg;base64," + FileEncryptUtil.getDecryptFileBase64(persPersonCacheItem.getPhotoPath()));
//        }
//        jsonObject.put("deviceId", "");
//        messagingTemplate.convertAndSend("/topic/noSenseAtt/realTimeSign", jsonObject);
        
        Map<String, String> attParams = attParamService.getAttParams();
        String rollCall = attParams.get("att.realTime.rollCall");
        // 开启实时点名才进行实时推送 0 启用实时点名
        if (StringUtils.isNotBlank(rollCall) && "0".equals(rollCall)) {
            if (Objects.nonNull(attLeave) && "2".equals(attLeave.getFlowStatus())) {
                JSONObject item = new JSONObject();
                item.put("pin", attLeave.getPersonPin());
                item.put("deptId", attLeave.getDeptId());
                item.put("startDatetime", attLeave.getStartDatetime());
                item.put("endDatetime", attLeave.getEndDatetime());
                item.put("pushType", "leave");
                JSONArray data = new JSONArray();
                data.add(persPersonCacheItem.getPin());
                data.add(persPersonCacheItem.getName());
                data.add(persPersonCacheItem.getDeptCode());
                data.add(persPersonCacheItem.getDeptName());
                data.add("");
                data.add(I18nUtil.i18nCode("att_leftMenu_leave"));
                item.put("data", data);
                redisTemplate.convertAndSend(AttConstant.ATT_REALTIMEPUSH_TOPIC, item);
            }
        }
    }
}
