package com.zkteco.zkbiosecurity.att.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 考勤点
 */
@Entity
@Table(name = "ATT_POINT")
@Setter
@Getter
@Accessors(chain = true)
public class AttPoint extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 考勤点名称
     */
    @Column(name = "POINT_NAME", length = 50)
    private String pointName;

    /**
     * 系统区域id，或停车场进出口区域id
     */
    @Column(name = "AREA_ID", length = 50)
    private String areaId;

    /**
     * 停车场的设备id或者门禁的门id或者视频的通道ID或通道的设备ID
     */
    @Column(name = "DEVICE_ID", length = 50)
    private String deviceId;

    @Column(name = "DEVICE_SN", length = 50)
    private String deviceSn;

    @Column(name = "IP_ADDRESS", length = 50)
    private String ipAddress;

    /**
     * 设备所属模块
     */
    @Column(name = "DEVICE_MODULE", length = 10)
    private String deviceModule;

    /**
     * 设备所属模块（门编号） (门禁当考勤时为门编号,VMS当考勤点时为通道编号,PSG当考勤点时为闸编号 --by ljf 2019/12/26)
     */
    @Column(name = "DOOR_NO")
    private Short doorNo;

    /**
     * 停车场的设备属性（出、入）
     */
    @Column(name = "STATUS")
    private Short status;

    /**
     * PSG的拉取记录类型(1对应正常通行记录，2对应验证记录)
     */
    @Column(name = "RECORD_TYPE")
    private Short recordType;
}