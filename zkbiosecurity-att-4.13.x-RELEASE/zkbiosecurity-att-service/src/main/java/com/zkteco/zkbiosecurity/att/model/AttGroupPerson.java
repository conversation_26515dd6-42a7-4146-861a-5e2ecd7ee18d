package com.zkteco.zkbiosecurity.att.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 分组人员
 */
@Entity
@Table(name = "ATT_GROUP_PERSON")
@Setter
@Getter
@Accessors(chain = true)
public class AttGroupPerson extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分组id
     */
    @Column(name = "GROUP_ID")
    private String groupId;

    /**
     * 人员id
     */
    @Column(name = "PERS_PERSON_ID")
    private String personId;
}
