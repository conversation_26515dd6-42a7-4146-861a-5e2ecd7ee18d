package com.zkteco.zkbiosecurity.att.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 设备参数
 */
@Entity
@Table(name = "ATT_DEVICE_OPTION")
@Setter
@Getter
@Accessors(chain = true)
public class AttDeviceOption extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**  */
    @ManyToOne
    @JoinColumn(name = "DEV_ID")
    private AttDevice attDevice;

    /**  */
    @Column(name = "OPTION_NAME", length = 50)
    private String name;

    /**  */
    @Column(name = "OPTION_VALUE", length = 150)
    private String value;

    public AttDeviceOption() {}

    public AttDeviceOption(AttDevice attDevice, String name, String value) {
        this.attDevice = attDevice;
        this.name = name;
        this.value = value;
    }
}