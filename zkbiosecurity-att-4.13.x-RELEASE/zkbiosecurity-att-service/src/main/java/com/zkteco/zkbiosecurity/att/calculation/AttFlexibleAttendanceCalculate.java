package com.zkteco.zkbiosecurity.att.calculation;

import java.util.*;

import com.zkteco.zkbiosecurity.att.constants.AppConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.bean.AttRuleParamBean;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttTimeSlotBO;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.enums.AttRuleEnum;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttRecordItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;

/**
 * 
 * 弹性班次考勤计算
 *
 * <AUTHOR>
 * @date 2020-07-01 11:10
 * @sine 1.0.0
 */
@Component
public class AttFlexibleAttendanceCalculate {

    /**
     * 弹性班次，填充考勤数据
     *
     * @date 2020/6/23 10:19
     * @param attPersonSchBO
     * @param attTimeSlotItemMap
     * @param attRuleParamBean
     * @param attRecordItem
     * @return void
     */
    public void fillElasticData(AttPersonSchBO attPersonSchBO, Map<String, AttTimeSlotItem> attTimeSlotItemMap,
        AttRuleParamBean attRuleParamBean, AttRecordItem attRecordItem) {

        String noSignIn = attRuleParamBean.getNoSignIn();
        String noSignOff = attRuleParamBean.getNoSignOff();

        List<String> timeSlotNameList = new ArrayList<>();
        // 班次时间数据
        List<String> shiftTimeDataList = new ArrayList<>();
        // 有效打卡数据
        List<String> cardValidDataList = new ArrayList<>();

        // 有效打卡次数
        int cardValidCount = 0;
        // 应上出勤分钟数
        int shouldMinuteTotal = 0;
        // 实际出勤分钟数
        int actualMinuteTotal = 0;
        // 有效出勤分钟数
        int validMinuteTotal = 0;
        // 是否有未签到
        boolean hasNoSignIn = false;
        // 是否有未签退
        boolean hasNoSignOff = false;
        List<String> cardStatusList = new ArrayList<>();
        String firstDay = null;
        String secondDay = null;

        // 遍历打卡数据，组装班次时间数据、有效打卡数据和计算应出勤分钟数
        for (AttTimeSlotBO attTimeSlotBO : attPersonSchBO.getAttTimeSlotArray()) {
            String attTimeSlotId = attTimeSlotBO.getAttTimeSlotId();
            AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotId);
            timeSlotNameList.add(attTimeSlotItem.getPeriodName());
            shiftTimeDataList
                .add(String.format("%s-%s", attTimeSlotItem.getStartSignInTime(), attTimeSlotItem.getEndSignOffTime()));

            // 应上出勤分钟数
            int shouldMinute = attTimeSlotItem.getWorkingHours() != null ? attTimeSlotItem.getWorkingHours() : 0;
            // 实际出勤分钟数
            int actualMinute = 0;
            attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);
            attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_NORMAL);
            if (Objects.isNull(firstDay)
                || (AttCalculationConstant.IsInterDay.THE_INTER_DAY.equals(attTimeSlotBO.getIsInterDay()))) {
                firstDay = attTimeSlotBO.getFirstDay();
                secondDay = attTimeSlotBO.getSecondDay();
            }

            String elasticCal = attTimeSlotItem.getElasticCal();
            // 弹性时长计算方式，两两打卡累积时长
            if (AttRuleEnum.ElasticCal.getValueOne().equals(elasticCal)) {
                List<String> elasticWorkTimeArray = attTimeSlotBO.getElasticWorkTimeArray();
                if (elasticWorkTimeArray != null && elasticWorkTimeArray.size() > 0) {
                    int elasticWorkTimeArraySize = elasticWorkTimeArray.size();
                    cardValidCount += elasticWorkTimeArraySize;

                    // 如果是打卡次数奇数，补充最后一个为缺卡
                    if (elasticWorkTimeArraySize % 2 != 0) {
                        elasticWorkTimeArray.add(elasticWorkTimeArraySize, noSignOff);
                        elasticWorkTimeArraySize++;
                        hasNoSignOff = true;
                        attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                    }
                    // 遍历两两打卡累积时长，打卡记录，组装有效打卡数据
                    for (int idx = 1; idx < elasticWorkTimeArraySize; idx += 2) {
                        String previousActualWorkTime = elasticWorkTimeArray.get(idx - 1);
                        String previousValidWorkTime = AttDateUtils.stringToHHmm(previousActualWorkTime);
                        String currentActualWorkTime = elasticWorkTimeArray.get(idx);
                        String currentValidWorkTime = currentActualWorkTime;
                        if (!noSignOff.equals(currentActualWorkTime)) {
                            currentValidWorkTime = AttDateUtils.stringToHHmm(currentActualWorkTime);
                            actualMinute += AttDateUtils.getMinuteDiff(previousActualWorkTime, currentActualWorkTime);
                        }
                        cardValidDataList.add(String.format("%s-%s", previousValidWorkTime, currentValidWorkTime));
                    }
                } else {
                    hasNoSignIn = true;
                    cardValidDataList.add(String.format("%s-%s", noSignIn, noSignOff));
                    attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                    attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                }
            } else {
                // 弹性时长计算方式，首尾打卡计算时长
                String actualToWorkTime = attTimeSlotBO.getToWorkTime();
                String actualOffWorkTime = attTimeSlotBO.getOffWorkTime();
                String validToWorkTime = actualToWorkTime;
                boolean isNoSignIn = noSignIn.equals(actualToWorkTime);
                boolean isNoSignOff = noSignOff.equals(actualOffWorkTime);
                if (isNoSignIn) {
                    hasNoSignIn = true;
                    attTimeSlotBO.setToWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                } else {
                    validToWorkTime = AttDateUtils.stringToHHmm(actualToWorkTime);
                    cardValidCount++;
                }

                String validOffWorkTime = actualOffWorkTime;
                if (isNoSignOff) {
                    hasNoSignOff = true;
                    attTimeSlotBO.setOffWorkTimeStatus(AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION);
                } else {
                    validOffWorkTime = AttDateUtils.stringToHHmm(actualOffWorkTime);
                    cardValidCount++;
                }

                cardValidDataList.add(String.format("%s-%s", validToWorkTime, validOffWorkTime));
                if (!isNoSignIn && !isNoSignOff) {
                    actualMinute = AttDateUtils.getMinuteDiff(actualToWorkTime, actualOffWorkTime);
                }
            }

            cardStatusList
                .add(String.format("%s-%s", attTimeSlotBO.getToWorkTimeStatus(), attTimeSlotBO.getOffWorkTimeStatus()));

            shouldMinuteTotal += shouldMinute;
            actualMinuteTotal += actualMinute;
            if (shouldMinute > 0) {
                validMinuteTotal += actualMinute > shouldMinute? shouldMinute : actualMinute;
            } else {
                validMinuteTotal += actualMinute;
            }
        }

        attRecordItem.setShiftNo(attPersonSchBO.getAttShiftNo());
        attRecordItem.setShiftName(attPersonSchBO.getAttShiftName());
        attRecordItem.setTimeSlotName(StringUtils.join(timeSlotNameList, ","));

        attRecordItem.setCardStatus(StringUtils.join(cardStatusList, ";"));
        attRecordItem.setCrossDay(String.format("%s_%s", firstDay, secondDay));

        attRecordItem.setShiftTimeData(StringUtils.join(shiftTimeDataList, ";"));
        if (!CollectionUtil.isEmpty(cardValidDataList) && cardValidDataList.size() > 19) {
            attRecordItem.setCardValidData(StringUtils.join(cardValidDataList.subList(0, 19), ";") + "...");
        } else {
            attRecordItem.setCardValidData(StringUtils.join(cardValidDataList, ";"));
        }
        attRecordItem.setCardValidCount(cardValidCount);

        // 应出勤
        attRecordItem.setShouldMinute(shouldMinuteTotal);
        // 实际出勤
        attRecordItem.setActualMinute(actualMinuteTotal);
        // 有效出勤
        attRecordItem.setValidMinute(validMinuteTotal);

        // 设置考勤状态结果
        Set<String> attendanceStatusSet = new HashSet<>();

        // 旷工
        if (shouldMinuteTotal > 0) {
            int absentMinute = shouldMinuteTotal - validMinuteTotal;
            attRecordItem.setAbsentMinute(absentMinute);
            if (absentMinute > 0) {
                attendanceStatusSet.add(AttCalculationConstant.AttAttendStatus.ABSENT);
            }
        }

        if (hasNoSignIn) {
            // 未签到 或 未签到且未签退 都记为未签到
            attendanceStatusSet.add(AttCalculationConstant.AttAttendStatus.NO_CHECK_IN);
        } else if (!hasNoSignIn && hasNoSignOff) {
            // 未签退
            attendanceStatusSet.add(AttCalculationConstant.AttAttendStatus.NO_CHECK_OUT);
        } else {
            // 实到
            attendanceStatusSet.add(AttCalculationConstant.AttAttendStatus.ACTUAL);
        }
        attRecordItem.setAttendanceStatus(StringUtils.join(attendanceStatusSet, ","));
    }
}
