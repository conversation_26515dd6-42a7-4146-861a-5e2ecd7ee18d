package com.zkteco.zkbiosecurity.att.data.upgrade;

import java.util.Objects;

import com.zkteco.zkbiosecurity.core.jdbc.JdbcOperateTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * 升级到4.2.0
 * <p>1、日打卡明细表的部门长度修改为100</p>
 *
 */
@Slf4j
@Component
public class AttVer4_2_0 implements UpgradeVersionManager {

    @Autowired
    private JdbcOperateTemplate jdbcOperateTemplate;

    @Override
    public String getModule() {
        return BaseConstants.ATT;
    }

    @Override
    public String getVersion() {
        return "v4.2.0";
    }

    @Override
    public boolean executeUpgrade() {

        jdbcOperateTemplate.alterTableCharLen("ATT_DAY_CARD_DETAIL", "AUTH_DEPT_NAME", "100");

        return true;
    }

}
