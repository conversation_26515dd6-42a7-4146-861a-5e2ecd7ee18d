/**
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 13:54 2019/9/17
 * @version V1.0
 */
package com.zkteco.zkbiosecurity.att.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.service.AttAnnualLeaveReportService;
import com.zkteco.zkbiosecurity.att.service.AttAutoExportService;

/**
 * 初始化定时器
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/8/24 18:21
 * @return
 */
@Component
@Order(value = 49)
public class AttInitTask implements CommandLineRunner {

    @Autowired
    private AttAutoExportService attAutoExportService;
    @Autowired
    private AttAnnualLeaveReportService attAnnualLeaveReportService;
    @Autowired
    private AttTimingDateCleanTask attTimingDateCleanTask;
    @Autowired
    private AttPointPullTransactionTask attPointPullTransactionTask;
    @Autowired
    private AttTimingUpdateDayCardTask attTimingUpdateDayCardTask;
    @Autowired
    private AttRealTimeCalculationTask attRealTimeCalculationTask;
    @Autowired
    private AttUpdateRealTimeCacheTask attUpdateRealTimeCacheTask;

    @Override
    public void run(String... strings) throws Exception {

        // 定时自动导出考勤信息到邮箱或FTP
        attAutoExportService.initAutoExport();

        // 初始化定时清空发放年假
        attAnnualLeaveReportService.initAnnualLeaveScheduled();

        // 定时清空考勤事件记录
        attTimingDateCleanTask.startTask();

        // 考勤点定时获取记录
        attPointPullTransactionTask.startTask();

        // 定时更新日打卡详情
        attTimingUpdateDayCardTask.startTask();

        // 考勤实时计算
        attRealTimeCalculationTask.startTask();

        // 更新实时计算缓存数据
        attUpdateRealTimeCacheTask.startTask();
    }
}
