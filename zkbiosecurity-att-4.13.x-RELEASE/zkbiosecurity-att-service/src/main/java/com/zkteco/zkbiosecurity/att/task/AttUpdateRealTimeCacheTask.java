/**
 * <AUTHOR>
 * @date 2020/9/1 9:18
 */
package com.zkteco.zkbiosecurity.att.task;

import java.util.Date;
import java.util.concurrent.ScheduledFuture;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.cache.AttCalculationCacheManager;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.SpringUtil;
import com.zkteco.zkbiosecurity.redis.redisson.annotation.RedisLock;
import com.zkteco.zkbiosecurity.scheduler.ScheduleService;

import lombok.extern.slf4j.Slf4j;

/**
 * 1、删除实时计算有效开始时间之前的异常申请缓存
 * 2、新增实时计算有效结束时间之前的异常申请缓存
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 9:18 2020/9/1
 */
@Slf4j
@Component
public class AttUpdateRealTimeCacheTask {

    private ScheduledFuture<?> scedulefuture;
    @Autowired
    private ScheduleService scheduleService;
    @Autowired
    private AttCalculationCacheManager attCalculationCacheManager;
    @Autowired
    private AttParamService attParamService;

    public void startTask() {
        log.info("AttUpdateRealTimeCacheTask Init");

        try {
            if (scedulefuture != null) {
                scedulefuture.cancel(true);
            }
            // 每个月1号的1点的时候执行
            AttUpdateRealTimeCacheTask attUpdateRealTimeCacheTask = SpringUtil.getApplicationContext().getBean(AttUpdateRealTimeCacheTask.class);
            scedulefuture = scheduleService.startScheduleTask(() -> attUpdateRealTimeCacheTask.updateRealTimeCache(), "0 0 1 1 * ?");
        } catch (Exception e) {
            log.error("AttUpdateRealTimeCacheTask Init Exception", e);
        }
    }

    @RedisLock(lockName = "updateRealTimeCacheRedisson", waitTime = 0, expire = 600)
    public void updateRealTimeCache() {

        // 是否启用实时计算
        if (!attParamService.realTimeEnable()) {
            return;
        }

        log.info("AttUpdateRealTimeCacheTask updateRealTimeCache start");


        // 删除实时计算有效开始时间之前的异常申请缓存
        Date cacheStartDate = attCalculationCacheManager.getCacheStartDate();
        String delBeforeMonth = DateUtil.getDate(DateUtil.addMonth(cacheStartDate, -1), DateUtil.DateStyle.YYYY_MM);

        log.info("AttUpdateRealTimeCacheTask updateRealTimeCache delBeforeMonth " + delBeforeMonth);

        // 删除过期节假日
        attCalculationCacheManager.delHolidayByDate(delBeforeMonth);

        // 删除过期补签
        attCalculationCacheManager.delSignByDate(delBeforeMonth);

        // 删除过期请假
        attCalculationCacheManager.delLeaveByDate(delBeforeMonth);

        // 删除过期加班
        attCalculationCacheManager.delOvertimeByDate(delBeforeMonth);

        // 删除过期调休
        attCalculationCacheManager.delAdjustByDate(delBeforeMonth);

        // 删除过期调班
        attCalculationCacheManager.delClassByDate(delBeforeMonth);

        // 新增实时计算有效结束时间之前的异常申请缓存
        Date addEndDate = attCalculationCacheManager.getCacheEndDate();
        Date addStartDate = DateUtil.addMonth(addEndDate, -1);

        log.info("AttUpdateRealTimeCacheTask updateRealTimeCache addDate " + addStartDate + " = > " + addEndDate);

        // 新增节假日
        attCalculationCacheManager.initHoliday(addStartDate, addEndDate);

        // 新增补签
        attCalculationCacheManager.initSignMap(addStartDate, addEndDate);

        // 新增请假
        attCalculationCacheManager.initLeaveMap(addStartDate, addEndDate);

        // 新增加班
        attCalculationCacheManager.initOvertimeMap(addStartDate, addEndDate);

        // 新增调休
        attCalculationCacheManager.initAdjustMap(addStartDate, addEndDate);

        // 新增调班
        attCalculationCacheManager.initClassMap(addStartDate, addEndDate);

        log.info("AttUpdateRealTimeCacheTask updateRealTimeCache end");
    }
}
