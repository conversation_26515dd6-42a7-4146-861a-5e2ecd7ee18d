package com.zkteco.zkbiosecurity.att.data.upgrade;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.service.AttShiftService;
import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * 升级到2.4.0
 *
 */
@Slf4j
@Component
public class AttVer2_4_0 implements UpgradeVersionManager {

    @Autowired
    private AttShiftService attShiftService;
    @Autowired
    private AuthPermissionService authPermissionService;

    @Override
    public String getModule() {
        return BaseConstants.ATT;
    }

    @Override
    public String getVersion() {
        return "v2.4.0";
    }

    @Override
    public boolean executeUpgrade() {

        //新增周期起始方式,默认为0/按起始日期
        attShiftService.upgradeTo_240();

        //添加导入U盘记录权限
        AuthPermissionItem buttonMenuItem = authPermissionService.getItemByCode("AttTransactionImportUSBRecord");
        if (Objects.isNull(buttonMenuItem)) {
            AuthPermissionItem childMenuItem = authPermissionService.getItemByCode("AttTransaction");
            if (Objects.nonNull(childMenuItem)) {
                buttonMenuItem = new AuthPermissionItem("AttTransactionImportUSBRecord", "att_op_importUSBRecord", "att:transaction:importUSBRecord", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
                buttonMenuItem.setParentId(childMenuItem.getId());
                authPermissionService.saveItem(buttonMenuItem);
            }
        }

        return true;
    }

}
