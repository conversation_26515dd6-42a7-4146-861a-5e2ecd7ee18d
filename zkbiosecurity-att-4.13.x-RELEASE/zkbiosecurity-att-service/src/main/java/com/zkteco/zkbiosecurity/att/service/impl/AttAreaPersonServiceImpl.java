package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.att.service.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.api.vo.AttApiAreaItem;
import com.zkteco.zkbiosecurity.att.api.vo.AttApiAreaPersonItem;
import com.zkteco.zkbiosecurity.att.cmd.AttDevCmdManager;
import com.zkteco.zkbiosecurity.att.constants.AttApiConstant;
import com.zkteco.zkbiosecurity.att.constants.AttCommonSchConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttAreaPersonDao;
import com.zkteco.zkbiosecurity.att.dao.AttDeviceDao;
import com.zkteco.zkbiosecurity.att.dao.AttPersonDao;
import com.zkteco.zkbiosecurity.att.model.AttAreaPerson;
import com.zkteco.zkbiosecurity.att.model.AttDevice;
import com.zkteco.zkbiosecurity.att.model.AttPerson;
import com.zkteco.zkbiosecurity.att.operate.AttSetPersonToDevOperate;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.service.AuthSessionServcie;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.service.PersBioTemplateService;
import com.zkteco.zkbiosecurity.pers.service.PersCardService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

/**
 * 区域人员
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 15:47
 * @since 1.0.0
 */
@Service
public class AttAreaPersonServiceImpl implements AttAreaPersonService {

    @Autowired
    private AttAreaPersonDao attAreaPersonDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AttDeviceDao attDeviceDao;
    @Autowired
    private AttDevCmdManager attDevCmdManager;
    @Autowired
    private AttSetPersonToDevOperate attSetPersonToDevOperate;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private PersBioTemplateService persBioTemplateService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AuthSessionServcie authSessionServcie;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttGroupService attGroupService;
    @Autowired
    private PersCardService persCardService;
    @Autowired(required = false)
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private AttPersonDao attPersonDao;
    @Autowired(required = false)
    private AttSdc4AttPersonService attSdc4AttPersonService;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttAnnualLeaveReportService attAnnualLeaveReportService;

    @Override
    @Transactional
    public AttAreaPersonItem saveItem(AttAreaPersonItem item) {
        AttAreaPerson attAreaPerson =
            Optional.ofNullable(item).map(AttAreaPersonItem::getId).filter(StringUtils::isNotBlank)
                .flatMap(attAreaPersonDao::findById).filter(Objects::nonNull).orElse(new AttAreaPerson());
        ModelUtil.copyPropertiesIgnoreNull(item, attAreaPerson);
        attAreaPersonDao.saveAndFlush(attAreaPerson);
        item.setId(attAreaPerson.getId());
        return item;
    }

    @Override
    public List<AttAreaPersonItem> getByCondition(AttAreaPersonItem condition) {
        return (List<AttAreaPersonItem>)attAreaPersonDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, AttAreaPersonItem attAreaPersonItem, int page, int size) {
        buildCondition(sessionId, attAreaPersonItem);
        Pager pager = attAreaPersonDao.getItemsBySql(attAreaPersonItem.getClass(),
            SQLUtil.getSqlByItem(attAreaPersonItem), page, size);
        return pager;
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return loadPagerByAuthUserFilter(null, (AttAreaPersonItem)condition, page, size);
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            Collection<String> idList = CollectionUtil.strToList(ids);
            List<AttAreaPerson> attAreaPersonList = attAreaPersonDao.findByIdIn(idList);
            if (attAreaPersonList != null) {
                for (AttAreaPerson attAreaPerson : attAreaPersonList) {
                    // 先取出人员，下发设备删除人员
                    PersPersonItem persPersonItem = persPersonService.getItemById(attAreaPerson.getPersonId());
                    if (persPersonItem != null) {
                        List<AttDevice> attDeviceList = attDeviceDao.findByAreaId(attAreaPerson.getAreaId());
                        for (AttDevice attDevice : attDeviceList) {
                            // 下发删除区域人员命令
                            attDevCmdManager.delUserInfo(attDevice, persPersonItem.getPin());
                        }
                    }
                    // 删除区域人员
                    attAreaPersonDao.delete(attAreaPerson);
                }
            }
        }
        return false;
    }

    @Override
    public AttAreaPersonItem getItemById(String id) {
        List<AttAreaPersonItem> items = getByCondition(new AttAreaPersonItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public ZKResultMsg syncPerToDev(String areaId, String ids) {
        ZKResultMsg res = new ZKResultMsg();
        List<AttAreaPerson> attAreaPersonList = new ArrayList<>();
        // 如果选中的id不为空，则根据选中的id获取人员id
        if (StringUtils.isBlank(areaId) && StringUtils.isNotBlank(ids)) {
            List<String> personIds =
                attAreaPersonDao.getPersonIdListByIdList(Arrays.asList(ids.split(AttConstant.COMMA)));
            attAreaPersonList = attAreaPersonDao.findByPersonIdIn(personIds);
        }
        // 区域不为空,但是人员未选中的情况
        else if (StringUtils.isBlank(ids) && StringUtils.isNotBlank(areaId)) {
            attAreaPersonList = attAreaPersonDao.findByAreaId(areaId);
        }
        // 区域与人员都不为空
        else if (StringUtils.isNotBlank(ids) && StringUtils.isNotBlank(areaId)) {
            List<String> personIds =
                attAreaPersonDao.getPersonIdListByIdList(Arrays.asList(ids.split(AttConstant.COMMA)));
            attAreaPersonList = attAreaPersonDao.findByAreaIdAndPersonIdIn(areaId, personIds);
        } else {
            // 区域或者人员必选一个
            return ZKResultMsg.failMsg("att_areaPerson_notice1");
        }
        if (CollectionUtil.isEmpty(attAreaPersonList)) {
            // 没有查询到人或者区域
            return ZKResultMsg.failMsg("att_areaPerson_notice2");
        } else {
            Collection<String> areaIdList =
                CollectionUtil.getPropertyList(attAreaPersonList, AttAreaPerson::getAreaId, AttConstant.COMM_DEF_VALUE);
            long areaCount = attDeviceDao.countByAreaIdIn(areaIdList);
            if (areaCount <= 0L) {
                // 查询区域下面没有设备
                return ZKResultMsg.failMsg("att_areaPerson_notice3");
            }
        }

        // 防止页面因为人员过多，加载过久，导致，程序执行超时，使用异步的方式，进行数据下发。
        List<AttAreaPerson> finalAttAreaPersonList = attAreaPersonList;
        CompletableFuture.runAsync(() -> {
            attSetPersonToDevOperate.sendPersonToDeviceByArea(finalAttAreaPersonList);
        });
        res.setMsg("att_areaPerson_syncToDevSuccess");
        return res;
    }

    @Override
    public ZKResultMsg addSms(String ids, String startTime, String msg, String min) {
        ZKResultMsg res = new ZKResultMsg();
        List<String> idList = Arrays.asList(ids.split(","));
        List<AttAreaPerson> attAreaPersonList = attAreaPersonDao.findByIdList(idList);
        List<String> areaIdList =
            (List<String>)CollectionUtil.getPropertyList(attAreaPersonList, AttAreaPerson::getAreaId, "-1");
        List<AttDevice> attDeviceList = attDeviceDao.findByAreaIdIn(areaIdList);
        List<String> personIdList =
            (List<String>)CollectionUtil.getPropertyList(attAreaPersonList, AttAreaPerson::getPersonId, "-1");
        Map<String, String> personIdPinMap = persPersonService.getPinsByPersonIds(personIdList);
        BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("att.sms.uid");
        int uid = Integer.parseInt(baseSysParamItem.getParamValue()) + 1;

        for (AttDevice attDevice : attDeviceList) {
            if (!attDevice.getStatus())// 过滤禁用设备
            {
                continue;
            }
            // 先把消息下发到设备
            attDevCmdManager.sendSms(attDevice, msg, "254 ", String.valueOf(uid), min, startTime);
            // 再下发人员pin号和消息对应关系
            for (AttAreaPerson attAreaPerson : attAreaPersonList) {
                if (attAreaPerson.getAreaId().equals(attDevice.getAreaId()))// 人员所属区域id要和设备区域id一致
                {
                    attDevCmdManager.sendUserSms(attDevice, personIdPinMap.get(attAreaPerson.getPersonId()),
                        String.valueOf(uid));
                }
            }
        }

        baseSysParamService.saveValueByName("att.sms.uid", String.valueOf(uid));

        return res;
    }

    @Override
    @Transactional
    public ZKResultMsg addPerson(String personIds, String areaId, String devSn) {
        ZKResultMsg zKResultMsg = new ZKResultMsg();
        if (StringUtils.isBlank(personIds)) {
            return ZKResultMsg.failMsg("att_class_personPin");
        }
        if (StringUtils.isBlank(areaId)) {
            return ZKResultMsg.failMsg("att_h5_attAreaNull");
        }
        Collection<String> personIdList = CollectionUtil.strToList(personIds);

        List<AttAreaPerson> attAreaPersonList = attAreaPersonDao.findByAreaIdAndPersonIdIn(areaId, personIdList);
        Map<String, AttAreaPerson> areaPersonMap =
            attAreaPersonList.stream().collect(Collectors.toMap(AttAreaPerson::getPersonId, Function.identity()));
        List<String> addPersonIdList =
            personIdList.stream().filter(personId -> !areaPersonMap.containsKey(personId)).collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(addPersonIdList)) {
            AttAreaPerson attAreaPerson = null;
            for (String personId : addPersonIdList) {
                attAreaPerson = new AttAreaPerson();
                attAreaPerson.setAreaId(areaId);
                attAreaPerson.setPersonId(personId);
                attAreaPersonDao.save(attAreaPerson);
            }

            // 下发人员数据到设备
            List<AttDevice> attDeviceList =
                attDeviceDao.findByAreaIdAndDevSnNotIn(areaId, CollectionUtil.strToList(devSn));
            if (!CollectionUtil.isEmpty(attDeviceList)) {
                CompletableFuture.runAsync(() -> {
                    attSetPersonToDevOperate.sendPersonToDevice(Arrays.asList(personIds.split(",")), attDeviceList);
                });
            }
            // sdc设备添加人员
            if (Objects.nonNull(attSdc4AttPersonService)) {
                // 添加人员到区域的设备
                List<PersPersonItem> personItems = persPersonService.getItemsByIds(personIds);
                List<AttSdc4AttPersonItem> attSdc4AttPersonItemList = new ArrayList<>();
                for (PersPersonItem persPersonItem : personItems) {
                    if (StringUtils.isNotBlank(persPersonItem.getPhotoPath())) {
                        AttSdc4AttPersonItem attSdc4AttPersonItem = new AttSdc4AttPersonItem();
                        attSdc4AttPersonItem.setPin(persPersonItem.getPin());
                        attSdc4AttPersonItem.setName(persPersonItem.getName());
                        attSdc4AttPersonItem.setGender(StringUtils.isBlank(persPersonItem.getGender()) ? 2
                            : ("M".equals(persPersonItem.getGender()) ? 0 : 1));
                        attSdc4AttPersonItem.setPhotoPath(FileUtils.getLocalFullPath(persPersonItem.getPhotoPath()));
                        List<String> areaIdList = new ArrayList<>();
                        areaIdList.add(areaId);
                        attSdc4AttPersonItem.setAreaIds(areaIdList);
                        attSdc4AttPersonItemList.add(attSdc4AttPersonItem);
                    }
                }
                attSdc4AttPersonService.batchAddPerson(attSdc4AttPersonItemList);
            }
        }

        // 保存考勤人员从表(选人控件一次只能选择500人)
        // List<PersPersonItem> persPersonItemList = persPersonService.getSimpleItemsByIds(personIdList);
        List<PersPersonItem> persPersonItemList = persPersonService.getItemsByIds(personIdList);
        boolean enableAnnualLeave = attParamService.enableAnnualLeave();
        String calculateType = attParamService.getAnnualLeaveCalculateType();
        String rule = attParamService.getAnnualLeaveCalculateRule();

        persPersonItemList.forEach(persPersonItem -> {
            AttPersonItem attPersonItem = attPersonService.getItemByPersonId(persPersonItem.getId());
            if (attPersonItem == null) {
                attPersonItem = new AttPersonItem();
                attPersonItem.setPersonId(persPersonItem.getId());
                attPersonItem.setPersonPin(persPersonItem.getPin());
                attPersonItem.setGroupId(AttCommonSchConstant.GroupConstant.DEFAULT_NO_GROUP_ID);
                attPersonItem.setPerDevAuth((short)0);
                attPersonItem.setIsAttendance(true);
                attPersonItem.setVerifyMode((short)0);

                // 计算年假
                if (enableAnnualLeave) {
                    // 人员更新计算总年假
                    if (attPersonItem.getHireDate() != null) {
                        List<AttAnnualLeaveRuleItem> attAnnualLeaveRuleItemList =
                                attAnnualLeaveReportService.buildRuleItem(rule);
                        Date annualLeaveCalculateDate = attParamService.getAnnualLeaveCalculateDate();
                        Map<String, String> annualLeaveMap =
                                attAnnualLeaveReportService.calculateAnnualLeaveDays(attPersonItem.getHireDate(), calculateType,
                                        attAnnualLeaveRuleItemList, annualLeaveCalculateDate);
                        attPersonItem.setAnnualLeaveDays(Integer.valueOf(annualLeaveMap.get("days")));
                        attPersonItem.setAnnualValidDate(
                                DateUtil.stringToDate(annualLeaveMap.get("validDate"), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                        attPersonItem.setWorkLong(annualLeaveMap.get("workLong"));
                    }
                }
                // 保存考勤人员
                attPersonService.saveItem(attPersonItem);
                // 人事人员编辑更新人事人员缓存的考勤附加参数
                attPersonService.updatePersonCacheExtParams(attPersonItem);
            }
        });
        return zKResultMsg;
    }

    @Override
    public void syncAttPersonToDevice(String personId, String areaId, String devSn) {
        List<AttAreaPerson> attAreaPersonList =
            attAreaPersonDao.findByAreaIdAndPersonIdIn(areaId, Arrays.asList(personId));
        Map<String, AttAreaPerson> areaPersonMap =
            attAreaPersonList.stream().collect(Collectors.toMap(AttAreaPerson::getPersonId, Function.identity()));
        if (!areaPersonMap.containsKey(personId)) {
            AttAreaPerson attAreaPerson = new AttAreaPerson();
            attAreaPerson.setAreaId(areaId);
            attAreaPerson.setPersonId(personId);
            attAreaPersonDao.save(attAreaPerson);
        }
        // 下发人员数据到设备
        List<AttDevice> attDeviceList = attDeviceDao.findByAreaIdAndDevSnNotIn(areaId, CollectionUtil.strToList(devSn));
        attSetPersonToDevOperate.sendPersonToDevice(Arrays.asList(personId), attDeviceList);
    }

    @Override
    public String getAreaIdsByPersonId(String personId) {
        List<String> list = attAreaPersonDao.getAreaIdsByPersonId(personId);
        return Optional.ofNullable(list).filter(i -> !i.isEmpty()).map(i -> StringUtils.join(i, ",")).orElse("");
    }

    @Override
    public String getRootAreaId() {
        List<String> rootAreaId = authAreaService.findRootAreaId();
        return rootAreaId.get(0);
    }

    @Override
    public List<AttAreaPersonItem> getAreaPersonByPersonIds(Collection<String> personIdList) {
        return Optional.ofNullable(personIdList).filter(i -> !i.isEmpty()).map(attAreaPersonDao::findByPersonIdIn)
            .map(list -> ModelUtil.copyListProperties(list, AttAreaPersonItem.class))
            .orElse(new ArrayList<AttAreaPersonItem>());
    }

    @Override
    public List<AttApiAreaItem> getApiAreaList(int pageNo, int pageSize) {
        List<AttApiAreaItem> attApiAreaItemList = new ArrayList<>();
        List<AuthAreaItem> authAreaItemList =
            (List<AuthAreaItem>)authAreaService.getItemsByPage(new AuthAreaItem(), pageNo - 1, pageSize).getData();
        if (!authAreaItemList.isEmpty()) {
            authAreaItemList.forEach(authAreaItem -> {
                AttApiAreaItem attApiAreaItem =
                    new AttApiAreaItem(authAreaItem.getId(), authAreaItem.getCode(), authAreaItem.getName());
                attApiAreaItemList.add(attApiAreaItem);
            });
        }
        return attApiAreaItemList;
    }

    @Override
    public Pager getApiAreaPager(int pageNo, int pageSize) {
        Pager pager = authAreaService.getItemsByPage(new AuthAreaItem(), pageNo - 1, pageSize);
        pager.setData(ModelUtil.copyListProperties(pager.getData(), AttApiAreaItem.class));
        return pager;
    }

    @Override
    public AttApiAreaItem getApiAreaByCode(String code) {
        AuthAreaItem authAreaItem = authAreaService.getItemByCode(code);
        if (authAreaItem != null) {
            AttApiAreaItem attApiAreaItem =
                new AttApiAreaItem(authAreaItem.getId(), authAreaItem.getCode(), authAreaItem.getName());
            return attApiAreaItem;
        }
        return null;
    }

    @Override
    @Transactional
    public ApiResultMessage addApiAreaPerson(AttApiAreaPersonItem areaPerson) {
        if (StringUtils.isBlank(areaPerson.getCode())) {// 区域编号为空
            return ApiResultMessage.message(AttApiConstant.BASE_AREA_CODE_NOTNULL,
                I18nUtil.i18nCode("att_api_areaCodeNotNull"));
        } else if (areaPerson.getPins() == null || areaPerson.getPins().isEmpty()) {// 人员编号集合为空
            return ApiResultMessage.message(AttApiConstant.PINS_DATA_NOT_NULL,
                I18nUtil.i18nCode("att_api_pinsNotNull"));
        } else if (areaPerson.getPins().size() > 500) {// 人员编号集合长度超过
            return ApiResultMessage.message(AttApiConstant.PINS_DATA_OVER_SIZE,
                I18nUtil.i18nCode("att_api_pinsOverSize"));
        }
        AuthAreaItem authAreaItem = authAreaService.getItemByCode(areaPerson.getCode());
        if (authAreaItem == null) {
            return ApiResultMessage.message(AttApiConstant.BASE_AREA_NO_EXIST,
                I18nUtil.i18nCode("att_api_areaNoExist"));
        }
        List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(areaPerson.getPins());
        if (persPersonItemList != null && !persPersonItemList.isEmpty()) {
            String personIds = CollectionUtil.getItemIds(persPersonItemList);
            addPerson(personIds, authAreaItem.getId(), "-1");
        }
        return ApiResultMessage.successMessage();
    }

    @Override
    @Transactional
    public ApiResultMessage delApiAreaPerson(AttApiAreaPersonItem areaPerson) {
        AuthAreaItem authAreaItem = authAreaService.getItemByCode(areaPerson.getCode());
        if (authAreaItem == null) {
            return ApiResultMessage.message(AttApiConstant.BASE_AREA_NO_EXIST,
                I18nUtil.i18nCode("att_api_areaNoExist"));
        }
        AttAreaPersonItem attAreaPersonItem = new AttAreaPersonItem();
        List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(areaPerson.getPins());
        if (!persPersonItemList.isEmpty()) {
            attAreaPersonItem.setAreaId(authAreaItem.getId());
            attAreaPersonItem.setInPersPersonId(CollectionUtil.getItemIds(persPersonItemList));
            List<AttAreaPersonItem> attAreaPersonItemList = getByCondition(attAreaPersonItem);
            if (!attAreaPersonItemList.isEmpty()) {
                deleteByIds(CollectionUtil.getItemIds(attAreaPersonItemList));
            }
        }
        return ApiResultMessage.successMessage();
    }

    @Override
    @Transactional
    public void handlerTransfer(List<AttAreaPersonItem> attAreaPersonItems) {
        List<List<AttAreaPersonItem>> splitAttAreaPersonList =
            CollectionUtil.split(attAreaPersonItems, CollectionUtil.splitSize);
        List<AttAreaPerson> attAreaPersons = attAreaPersonDao.findAll();
        Map<String, AttAreaPerson> attAreaPersonMap =
            CollectionUtil.listToKeyMap(attAreaPersons, item -> item.getAreaId() + "-" + item.getPersonId());
        List<AuthAreaItem> authAreaItemList = authAreaService.getByCondition(new AuthAreaItem());
        Map<String, AuthAreaItem> authAreaItemMap =
            CollectionUtil.listToKeyMap(authAreaItemList, AuthAreaItem::getCode);
        for (List<AttAreaPersonItem> attAreaPersonItemList : splitAttAreaPersonList) {
            Collection<String> personPinList = CollectionUtil.getPropertyList(attAreaPersonItemList,
                AttAreaPersonItem::getPin, AttConstant.COMM_DEF_VALUE);
            List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(personPinList);
            Map<String, PersPersonItem> persPersonItemMap =
                CollectionUtil.listToKeyMap(persPersonItemList, PersPersonItem::getPin);
            List<AttAreaPerson> attAreaPersonList = new ArrayList<>();

            for (AttAreaPersonItem attAreaPersonItem : attAreaPersonItemList) {
                String authAreaNo = attAreaPersonItem.getAuthAreaNo();
                AuthAreaItem authAreaItem = authAreaItemMap.get(authAreaNo);
                if (Objects.isNull(authAreaItem)) {
                    continue;
                }
                String pin = attAreaPersonItem.getPin();
                PersPersonItem persPersonItem = persPersonItemMap.get(pin);
                if (Objects.isNull(persPersonItem)) {
                    continue;
                }

                AttAreaPerson attAreaPerson = attAreaPersonMap.get(authAreaItem.getId() + "-" + persPersonItem.getId());
                if (Objects.isNull(attAreaPerson)) {
                    attAreaPerson = new AttAreaPerson();
                }
                attAreaPerson.setAreaId(authAreaItem.getId());
                attAreaPerson.setPersonId(persPersonItem.getId());
                attAreaPersonList.add(attAreaPerson);
            }
            attAreaPersonDao.saveAll(attAreaPersonList);
        }
    }

    /**
     * 部门权限过滤
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/5/13 10:18
     * @param sessionId
     * @param condition
     * @return void
     */
    private void buildCondition(String sessionId, AttAreaPersonItem condition) {
        if (StringUtils.isBlank(condition.getInDeptId())) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, condition.getDeptId(),
                condition.getDeptCode(), condition.getDeptName()));
            condition.setDeptId(null);
        } else if (StringUtils.isNotBlank(sessionId)) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByAuthAndIds(sessionId, condition.getInDeptId()));
            condition.setDeptId(null);
        }
        // 区域的处理(等于空不带条件过滤)
        if (StringUtils.isBlank(condition.getAreaId())) {
            condition.setInAreaId(
                authAreaService.getAreaIdsByIdAndCodeAndName(sessionId, null, null, condition.getAuthAreaName()));
        } else {
            // 是否包含子区域
            if ("true".equals(condition.getIsIncludeLower())) {
                condition.setInAreaId(authAreaService.getAreaIdsByIdAndCodeAndName(sessionId, condition.getAreaId(),
                    null, condition.getAuthAreaName()));
                condition.setAreaId(null);
            }
        }

    }

    @Override
    public Pager authAreaList(AttAuthAreaItem attAuthAreaItem, int pageNo, int pageSize) {
        // 前端条件过滤
        AuthAreaItem authAreaItem = new AuthAreaItem();
        ModelUtil.copyProperties(attAuthAreaItem, authAreaItem);

        // 根据人员ID过滤
        if (StringUtils.isNotBlank(attAuthAreaItem.getPersonId())) {
            // 当前人员所拥有的区域ID
            List<AttAreaPerson> attAreaPersonList = attAreaPersonDao.findByPersonId(attAuthAreaItem.getPersonId());
            // 当前登录人员所拥有的区域ID
            StringBuffer authAreaIdIn = new StringBuffer();
            if (StringUtils.isNotBlank(attAuthAreaItem.getAuthAreaIdIn())) {
                authAreaIdIn.append("-1");
                for (AttAreaPerson attAreaPerson : attAreaPersonList) {
                    if (attAuthAreaItem.getAuthAreaIdIn().contains(attAreaPerson.getAreaId())) {
                        authAreaIdIn.append(",").append(attAreaPerson.getAreaId());
                    }
                }
            } else {
                authAreaIdIn.append(CollectionUtil.getPropertys(attAreaPersonList, AttAreaPerson::getAreaId));
            }
            authAreaItem.setAuthAreaIdIn(authAreaIdIn.toString());
        }

        Pager pager = authAreaService.getItemsByPage(authAreaItem, pageNo, pageSize);
        List<AuthAreaItem> authAreaItemList = (List<AuthAreaItem>)pager.getData();
        List<AttAuthAreaItem> list = ModelUtil.copyListProperties(authAreaItemList, AttAuthAreaItem.class);
        pager.setData(list);
        return pager;
    }

    @Override
    public List<AttAreaPersonItem> loadListByAuthFilter(AttAreaPersonItem condition, int beginIndex, int endIndex,
        String sessionId) {
        buildCondition(sessionId, condition);
        List<AttAreaPersonItem> areaPersonItemList = attAreaPersonDao.getItemsDataBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition), beginIndex, endIndex, true);
        return areaPersonItemList;
    }

    @Override
    @Transactional
    public ZKResultMsg importAreaPerson(List<AttAreaPersonItem> itemList) {
        if (itemList.size() > 30000) {
            // 一次性导入只能30000，超过要分批次导入
            throw ZKBusinessException.warnException("pers_import_overData", itemList.size());
        }
        int importSize = itemList.size();
        int beginProgress = 20;
        // 已存在数量
        int existCount = 0;
        // 导入的人员要求人事已存在
        // 导入的数据中，所有pin已经存在的人员，key为pin号
        Map<String, PersPersonItem> existPersPersonMap = new HashMap<>();
        // 构建 key为人员id 方便后面取数据
        Map<String, PersPersonItem> idPersonItemMap = new HashMap<>();
        // 存在的区域 key 为区域code
        Map<String, AuthAreaItem> existAuthAreaMap = new HashMap<>();
        // 分批处理，一次处理800人
        List<List<AttAreaPersonItem>> attAreaPersonImportItemList =
            CollectionUtil.split(itemList, CollectionUtil.splitSize);
        for (List<AttAreaPersonItem> importItemList : attAreaPersonImportItemList) {
            // 取出待导入数据中人员的pin号
            Collection<String> importPins =
                CollectionUtil.getPropertyList(importItemList, AttAreaPersonItem::getPin, "-1");
            // 根据pin号查出已经存在的人员
            List<PersPersonItem> existPersonList = persPersonService.getItemsByPins(importPins);
            if (existPersonList != null && existPersonList.size() > 0) {
                existPersPersonMap.putAll(CollectionUtil.listToKeyMap(existPersonList, PersPersonItem::getPin));
                idPersonItemMap.putAll(CollectionUtil.listToKeyMap(existPersonList, PersPersonItem::getId));
            }
            Collection<String> areaNos =
                CollectionUtil.getPropertyList(itemList, AttAreaPersonItem::getAuthAreaNo, "-1");
            List<AuthAreaItem> authAreaItemList = authAreaService.getItemsByCodes(areaNos);
            if (authAreaItemList != null && authAreaItemList.size() > 0) {
                existAuthAreaMap.putAll(CollectionUtil.listToKeyMap(authAreaItemList, AuthAreaItem::getCode));
            }
        }
        Iterator<AttAreaPersonItem> itemIterator = itemList.iterator();
        // 先剔除无效数据
        while (itemIterator.hasNext()) {
            AttAreaPersonItem item = itemIterator.next();
            if (StringUtils.isBlank(item.getPin())) {
                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                    ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                        I18nUtil.i18nCode("pers_import_pinNotEmpty")))));
                itemIterator.remove();
                continue;
            }
            PersPersonItem persPersonItem = existPersPersonMap.get(item.getPin());
            if (Objects.isNull(persPersonItem)) {
                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                    ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                        I18nUtil.i18nCode("att_areaPerson_persNoExit")))));
                itemIterator.remove();
                continue;
            }
            AuthAreaItem authAreaItem = existAuthAreaMap.get(item.getAuthAreaNo());
            if (Objects.isNull(authAreaItem)) {
                progressCache
                    .setProcess(new ProcessBean(beginProgress, beginProgress, ProcessBean.createErrorContent(I18nUtil
                        .i18nCode("pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("att_api_areaNoExist")))));
                itemIterator.remove();
                continue;
            }
        }
        // 剩下的可以插入数据库,分批处理，一次处理800人
        List<List<AttAreaPersonItem>> attAreaPersonInsertList =
            CollectionUtil.split(itemList, CollectionUtil.splitSize);
        for (List<AttAreaPersonItem> insertItemList : attAreaPersonInsertList) {
            // 按区域编号分组
            Map<String, List<AttAreaPersonItem>> areaCodePersonListMap =
                insertItemList.stream().collect(Collectors.groupingBy(AttAreaPersonItem::getAuthAreaNo));
            for (Map.Entry<String, List<AttAreaPersonItem>> entry : areaCodePersonListMap.entrySet()) {
                AuthAreaItem authAreaItem = existAuthAreaMap.get(entry.getKey());
                if (Objects.nonNull(authAreaItem)) {
                    // 获取人事人员id
                    List<String> personIdList = new ArrayList<>();
                    List<AttAreaPersonItem> entryValues = entry.getValue();
                    for (AttAreaPersonItem entryValue : entryValues) {
                        PersPersonItem persPersonItem = existPersPersonMap.get(entryValue.getPin());
                        if (Objects.nonNull(persPersonItem)) {
                            personIdList.add(persPersonItem.getId());
                        }
                    }
                    // 更新persPersonLink
                    String areaId = authAreaItem.getId();
                    List<AttAreaPerson> attAreaPersonList =
                        attAreaPersonDao.findByAreaIdAndPersonIdIn(areaId, personIdList);
                    existCount += attAreaPersonList.size();
                    Map<String, AttAreaPerson> areaPersonMap = attAreaPersonList.stream()
                        .collect(Collectors.toMap(AttAreaPerson::getPersonId, Function.identity()));
                    List<String> addPersonIdList = personIdList.stream()
                        .filter(personId -> !areaPersonMap.containsKey(personId)).collect(Collectors.toList());
                    if (!CollectionUtil.isEmpty(addPersonIdList)) {
                        AttAreaPerson attAreaPerson = null;
                        for (String personId : addPersonIdList) {
                            attAreaPerson = new AttAreaPerson();
                            attAreaPerson.setAreaId(areaId);
                            attAreaPerson.setPersonId(personId);
                            attAreaPersonDao.save(attAreaPerson);
                        }
                    }
                    // 保存人员从表
                    List<AttPersonItem> attPersonItems = attPersonService.getItemByPersonIds(addPersonIdList);
                    Map<String, AttPersonItem> attPersonItemMap =
                        CollectionUtil.listToKeyMap(attPersonItems, AttPersonItem::getPersonId);
                    List<AttPerson> bathSaveAttPersonList = new ArrayList<>();
                    addPersonIdList.forEach(addPersonId -> {
                        AttPersonItem attPersonItem = attPersonItemMap.get(addPersonId);
                        if (attPersonItem == null) {
                            PersPersonItem persPersonItem = idPersonItemMap.get(addPersonId);
                            if (Objects.nonNull(persPersonItem)) {
                                AttPerson attPerson = new AttPerson();
                                ModelUtil.copyProperties(persPersonItem, attPerson);
                                attPerson.setPersonId(persPersonItem.getId());
                                attPerson.setPersonPin(persPersonItem.getPin());
                                attPerson.setPersonName(persPersonItem.getName());
                                attPerson.setPersonLastName(persPersonItem.getLastName());
                                attPerson.setGroupId(AttCommonSchConstant.GroupConstant.DEFAULT_NO_GROUP_ID);
                                attPerson.setPerDevAuth((short)0);
                                attPerson.setIsAttendance(true);
                                //  校验方式默认自动
                                if (attPerson.getVerifyMode() == null) {
                                    attPerson.setVerifyMode((short)0);
                                }
                                bathSaveAttPersonList.add(attPerson);
                            }
                        }
                    });
                    attPersonDao.saveAll(bathSaveAttPersonList);
                }
            }
        }
        // 非法数量(失败数量)
        int validCount = importSize - itemList.size();
        // 成功数量
        int successCount = importSize - validCount - existCount;
        // 失败数量
        // int faildCount = importSize - successCount;
        if (existCount > 0) {
            // 成功：%s 条，更新：%s 条，失败：%s 条。
            progressCache.setProcess(ProcessBean.createNormalSingleProcess(99,
                I18nUtil.i18nCode("pers_import_result3", successCount, existCount, validCount)));
        } else {
            // 成功：%s 条，失败：%s 条。
            progressCache.setProcess(ProcessBean.createNormalSingleProcess(99,
                I18nUtil.i18nCode("pers_import_result", itemList.size(), validCount)));
        }
        if (validCount > 0) {
            return ZKResultMsg.failMsg();
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    @Transactional
    public ZKResultMsg importBatchDel(List<AttAreaPersonItem> itemList) {
        if (itemList.size() > 30000) {
            // 一次性导入只能30000，超过要分批次导入
            throw ZKBusinessException.warnException("pers_import_overData", itemList.size());
        }
        int importSize = itemList.size();
        int beginProgress = 20;
        // 导入的数据中，所有pin已经存在的人员，key为pin号
        Map<String, PersPersonItem> existPersPersonMap = new HashMap<>();
        // 存在的区域 key 为区域code
        Map<String, AuthAreaItem> existAuthAreaMap = new HashMap<>();
        // key为人员id 方便后面取数据
        Map<String, PersPersonItem> idPersonItemMap = new HashMap<>();
        // 分批处理，一次处理800人
        List<List<AttAreaPersonItem>> attAreaPersonImportItemList =
            CollectionUtil.split(itemList, CollectionUtil.splitSize);
        for (List<AttAreaPersonItem> importItemList : attAreaPersonImportItemList) {
            // 取出待导入数据中人员的pin号
            Collection<String> importPins =
                CollectionUtil.getPropertyList(importItemList, AttAreaPersonItem::getPin, "-1");
            // 根据pin号查出已经存在的人员
            List<PersPersonItem> existPersonList = persPersonService.getItemsByPins(importPins);
            if (existPersonList != null && existPersonList.size() > 0) {
                existPersPersonMap.putAll(CollectionUtil.listToKeyMap(existPersonList, PersPersonItem::getPin));
                idPersonItemMap.putAll(CollectionUtil.listToKeyMap(existPersonList, PersPersonItem::getId));
            }
            Collection<String> areaNos =
                CollectionUtil.getPropertyList(itemList, AttAreaPersonItem::getAuthAreaNo, "-1");
            List<AuthAreaItem> authAreaItemList = authAreaService.getItemsByCodes(areaNos);
            if (authAreaItemList != null && authAreaItemList.size() > 0) {
                existAuthAreaMap.putAll(CollectionUtil.listToKeyMap(authAreaItemList, AuthAreaItem::getCode));
            }
        }
        Iterator<AttAreaPersonItem> itemIterator = itemList.iterator();
        // 先踢除异常数据
        while (itemIterator.hasNext()) {
            AttAreaPersonItem item = itemIterator.next();
            if (StringUtils.isBlank(item.getPin())) {
                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                    ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                        I18nUtil.i18nCode("pers_import_pinNotEmpty")))));
                itemIterator.remove();
                continue;
            }
            PersPersonItem persPersonItem = existPersPersonMap.get(item.getPin());
            if (Objects.isNull(persPersonItem)) {
                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                    ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                        I18nUtil.i18nCode("att_areaPerson_persNoExit")))));
                itemIterator.remove();
                continue;
            }
            AuthAreaItem authAreaItem = existAuthAreaMap.get(item.getAuthAreaNo());
            if (Objects.isNull(authAreaItem)) {
                progressCache
                    .setProcess(new ProcessBean(beginProgress, beginProgress, ProcessBean.createErrorContent(I18nUtil
                        .i18nCode("pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("att_api_areaNoExist")))));
                itemIterator.remove();
                continue;
            }
        }
        // 剩下数据,分批处理，一次处理800人
        List<List<AttAreaPersonItem>> attAreaPersonDelList = CollectionUtil.split(itemList, CollectionUtil.splitSize);
        // 要删除的区域人员
        List<AttAreaPerson> delAreaPersonList = new ArrayList<>();
        for (List<AttAreaPersonItem> delItemList : attAreaPersonDelList) {
            // 按区域编号分组
            Map<String, List<AttAreaPersonItem>> areaCodePersonListMap =
                delItemList.stream().collect(Collectors.groupingBy(AttAreaPersonItem::getAuthAreaNo));
            // 找出所有的区域人员
            for (Map.Entry<String, List<AttAreaPersonItem>> entry : areaCodePersonListMap.entrySet()) {
                AuthAreaItem authAreaItem = existAuthAreaMap.get(entry.getKey());
                if (Objects.nonNull(authAreaItem)) {
                    // 获取人事人员id
                    List<String> personIdList = new ArrayList<>();
                    List<AttAreaPersonItem> entryValues = entry.getValue();
                    for (AttAreaPersonItem entryValue : entryValues) {
                        PersPersonItem persPersonItem = existPersPersonMap.get(entryValue.getPin());
                        if (Objects.nonNull(persPersonItem)) {
                            personIdList.add(persPersonItem.getId());
                        }
                    }
                    List<AttAreaPerson> attAreaPersonList =
                        attAreaPersonDao.findByAreaIdAndPersonIdIn(authAreaItem.getId(), personIdList);
                    delAreaPersonList.addAll(attAreaPersonList);
                }
            }
        }
        // 设备不多可查询全部，避免循环查
        List<AttDevice> allDevice = attDeviceDao.findAll();
        // 区域设备map
        Map<String, List<AttDevice>> areaIdDeviceMap = allDevice.stream().filter(dev -> {
            if (StringUtils.isBlank(dev.getAreaId())) {
                return false;
            }
            return true;
        }).collect(Collectors.groupingBy(AttDevice::getAreaId));
        if (delAreaPersonList != null) {
            PersPersonItem persPersonItem = null;
            for (AttAreaPerson attAreaPerson : delAreaPersonList) {
                // 先取出人员，下发设备删除人员
                persPersonItem = idPersonItemMap.get(attAreaPerson.getPersonId());
                List<AttDevice> attDeviceList = areaIdDeviceMap.get(attAreaPerson.getAreaId());
                if (!CollectionUtil.isEmpty(attDeviceList)) {
                    for (AttDevice attDevice : attDeviceList) {
                        // 下发删除区域人员命令
                        attDevCmdManager.delUserInfo(attDevice, persPersonItem.getPin());
                    }
                }
                // 删除区域人员
                attAreaPersonDao.delete(attAreaPerson);
            }
        }
        // 成功数量
        int successCount = delAreaPersonList.size();
        // 失败数量
        int faildCount = importSize - successCount;
        // 成功：%s 条，失败：%s 条。
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(99,
            I18nUtil.i18nCode("pers_import_result", successCount, faildCount)));

        if (faildCount > 0) {
            return ZKResultMsg.failMsg();
        }
        return ZKResultMsg.successMsg();
    }
}