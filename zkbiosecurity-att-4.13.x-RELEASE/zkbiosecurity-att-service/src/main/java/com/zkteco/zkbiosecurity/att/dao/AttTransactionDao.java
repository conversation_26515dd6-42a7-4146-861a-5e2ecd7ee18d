package com.zkteco.zkbiosecurity.att.dao;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.att.model.AttTransaction;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import javax.transaction.Transactional;

/**
 * 考勤原始记录、打卡记录
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/11/23 11:10
 * @since 1.0.0
 */
public interface AttTransactionDao extends BaseDao<AttTransaction, String> {

    @Query("SELECT t.personPin, t.attDate, t.attTime FROM AttTransaction t WHERE t.personPin IN ?1 AND t.attDatetime >= ?2 AND t.attDatetime <= ?3")
    List<Object[]> listTransactionBetweenDate(List<String> personPin, Date startDatetime, Date endDatetime);

    Integer countByPersonPinAndAttDatetime(String personPin, Date attDatetime);

    /**
     * 统计日打卡总数
     *
     * @param attDate
     * @return
     * <AUTHOR>
     * @Date 2019/1/7 10:08
     */
    Integer countByAttDate(String attDate);

    /**
     * 当天各时间段考勤数
     *
     * @return
     */
    @Query(
        value = "SELECT (case WHEN t.attTime BETWEEN '00:00:00' AND '08:00:00' THEN '00:00~08:00' WHEN t.attTime BETWEEN '08:00:00' AND '12:00:00'  THEN '08:00~12:00' "
            + "WHEN t.attTime BETWEEN '12:00:00' AND '14:00:00'  THEN '12:00~14:00' WHEN t.attTime BETWEEN '14:00:00' AND '18:00:00' THEN '14:00~18:00' WHEN t.attTime BETWEEN '18:00:00' AND '23:59:59' THEN '18:00~23:59' "
            + "ELSE 'other' end),count(t.id) FROM "
            + "AttTransaction t WHERE t.attDate = ?1 GROUP BY case WHEN t.attTime BETWEEN '00:00:00' AND '08:00:00' THEN '00:00~08:00' WHEN t.attTime BETWEEN '08:00:00' AND '12:00:00'  THEN '08:00~12:00' "
            + "WHEN t.attTime BETWEEN '12:00:00' AND '14:00:00' THEN '12:00~14:00' WHEN t.attTime BETWEEN '14:00:00' AND '18:00:00' THEN '14:00~18:00' WHEN t.attTime BETWEEN '18:00:00' AND '23:59:59' THEN '18:00~23:59' "
            + "ELSE 'other' END")
    List<Object[]> getAttTodayCountData(String date);

    @Query("SELECT count(DISTINCT t.personPin) FROM AttTransaction t WHERE t.personPin IN ?1 AND t.attDatetime >= ?2 AND t.attDatetime <= ?3")
    Integer countTransactionBetweenDateAndByPersPin(List<String> personPin, Date startDatetime, Date endDatetime);

    @Query("SELECT count(t.id) FROM AttTransaction t WHERE t.attDatetime >= ?1 AND t.attDatetime <= ?2")
    Integer countAttTransactionBetween(Date startDatetime, Date endDatetime);

    /**
     * 根据人员编号，考勤时间和设备SN查找对象(用于人证比对记录删除后重新拉，需要判断记录是否已存在--去重)
     *
     * @param personPin
     * @param attDatetime
     * @param deviceSn
     * @return com.zkteco.zkbiosecurity.att.model.AttTransaction
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/6/28 15:04
     */
    List<AttTransaction> findAttTransactionByPersonPinAndAttDatetimeAndDeviceSn(String personPin, Date attDatetime,
        String deviceSn);

    /**
     * 人员最早打卡记录
     *
     * @param beginDate
     * @param endDate
     * @param attPersPins
     * @return
     * <AUTHOR>
     * @since 2020年4月29日上午11:32:46
     */
    @Query("SELECT t.personPin,MIN(t.attDatetime) AS attDatetime FROM AttTransaction t WHERE t.attDatetime >= ?1 AND t.attDatetime <= ?2 AND t.personPin IN (?3) group by t.personPin")
    List<Object[]> getItemByTimeAndPersPins(Date beginDate, Date endDate, Collection<String> attPersPins);

    /**
     * 根据创建时间查询新增的考勤记录 [人员pin,打卡日期]
     *
     * @param createTime
     * @return java.util.List<java.lang.Object [ ]>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/6/29 16:52
     */
    @Query("SELECT t.personPin, t.attDate FROM AttTransaction t WHERE t.createTime > ?1 GROUP BY t.personPin, t.attDate")
    List<Object[]> findByCreateTime(Date createTime);

    /**
     * 根据打卡日期和pin集合，查询对应的考勤记录打卡时间 [人员pin,打卡时间]
     *
     * @param attDate
     * @param pinList
     * @return java.util.List<java.lang.Object [ ]>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/6/29 16:48
     */
    @Query("SELECT t.personPin, t.attTime FROM AttTransaction t WHERE t.attDate = ?1 AND t.personPin IN (?2) ORDER BY t.attDatetime")
    List<Object[]> findByAttDateAndPersonPinIn(String attDate, Collection<String> pinList);

    /**
     * 根据设备sn获取最近记录的考勤时间
     *
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/7/14 9:14
     * @param deviceSn
     * @return: java.util.Date
     **/
    @Query("SELECT MAX(t.createTime) FROM AttTransaction t WHERE t.deviceSn = ?1")
    Date getMaxCreateTimeByDeviceSn(String deviceSn);

    /**
     * 根据设备ID获取最近记录的考勤时间
     *
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/7/16 14:32
     * @param channelId
     * @return: java.util.Date
     **/
    @Query("SELECT MAX(t.createTime) FROM AttTransaction t WHERE t.deviceId = ?1")
    Date getMaxCreateTimeByChannelId(String channelId);

    List<AttTransaction> findByAttDatetimeAndPersonPinAndMark(Date attDateTime, String personPin, String mark);

    /**
     * 根据模块获取最近记录的时间
     * 
     * @param mark
     * @return
     */
    @Query("SELECT MAX(t.createTime) FROM AttTransaction t WHERE t.mark = ?1")
    Date getMaxCreateTimeByMark(String mark);

    /**
     * 根据设备和创建时间范围获取记录数
     * 
     * @param devSn:
     * @param startDateTime:
     * @param endDateTime:
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2020-12-10 14:34
     * @since 1.0.0
     */
    Integer countByDeviceSnAndCreateTimeBetween(String devSn, Date startDateTime, Date endDateTime);

    @Query("select count(id), t.attDate from AttTransaction t where t.attDate >= ?1 and t.attDate <= ?2 GROUP BY t.attDate")
    List<Object[]> findCountAndAttDateGroupByAttDate(String startAttDate, String endAttDate);

    @Query("select count(t.id) from AttTransaction t where t.attDate >= ?1 AND t.attDate <= ?2")
    Long findCountByAttDate(String startAttDate, String endAttDate);


    Long countByMarkAndAttDatetimeBetween(String mark, Date startAttDatetime, Date endAttDatetime);

    @Query(value = "select t.personPin, t.attDatetime from AttTransaction t where t.mark =?1 and t.attDatetime >= ?2"
            + " and t.attDatetime <= ?3 order by t.createTime asc")
    List<Object[]> findByMarkAndAttDatetimeBetween(String mark, Date startDatetime, Date endDatetime, Pageable pageable);

    Long countByMarkAndAttDatetimeBetweenAndDeviceId(String mark, Date startAttDatetime, Date endAttDatetime, String deviceId);

    @Query(value = "select t.personPin, t.attDatetime from AttTransaction t where t.mark =?1 and t.attDatetime >= ?2"
            + " and t.attDatetime <= ?3 and t.deviceId = ?4 order by t.createTime asc")
    List<Object[]> findByMarkAndAttDatetimeBetweenAndDeviceId(String mark, Date startDatetime, Date endDatetime, String deviceId, Pageable pageable);

    //根据人员pin号考勤记录时间查找
    @Query(value = "SELECT id FROM att_transaction  WHERE pers_person_pin=?1 and push_wechat_flag ='1' and att_datetime >= ?2  and EXTRACT(EPOCH FROM att_datetime AT TIME ZONE 'Asia/Shanghai' AT TIME ZONE 'UTC') * 1000  = ?3 limit 1",nativeQuery = true)
    String findAttTransactionIsPush(String pin,Date attDatetime ,long attTime);


    //根据id修改push_wechat_flag为2 不需要推送
    @Modifying
    @Transactional
    @Query(value = "update att_transaction set push_wechat_flag = '2' where id = ?1",nativeQuery = true)
    void updatePushWechatFlag(String id);
}