package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.zkteco.zkbiosecurity.att.bean.AttPersPersonInfoBean;
import com.zkteco.zkbiosecurity.att.cmd.AttDevCmdManager;
import com.zkteco.zkbiosecurity.att.operate.AttSetPersonToDevOperate;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.auth.provider.AuthSessionProvider;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.bean.AttConditionBean;
import com.zkteco.zkbiosecurity.att.cache.AttCacheManager;
import com.zkteco.zkbiosecurity.att.constants.AttCommonSchConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttAreaPersonDao;
import com.zkteco.zkbiosecurity.att.dao.AttDeviceDao;
import com.zkteco.zkbiosecurity.att.dao.AttGroupDao;
import com.zkteco.zkbiosecurity.att.dao.AttPersonDao;
import com.zkteco.zkbiosecurity.att.model.AttAreaPerson;
import com.zkteco.zkbiosecurity.att.model.AttDevice;
import com.zkteco.zkbiosecurity.att.model.AttGroup;
import com.zkteco.zkbiosecurity.att.model.AttPerson;
import com.zkteco.zkbiosecurity.att.operate.AttSendPersonToDeviceOperate;
import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import com.zkteco.zkbiosecurity.att.utils.AttStrUtils;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.service.AuthSessionServcie;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.*;
import com.zkteco.zkbiosecurity.pers.vo.*;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

/**
 * 考勤人员
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:44
 * @since 1.0.0
 */
@Service
public class AttPersonServiceImpl implements AttPersonService {

    Logger log = LoggerFactory.getLogger(getClass());

    @Autowired
    private AttPersonDao attPersonDao;
    @Autowired
    private AttGroupDao attGroupDao;
    @Autowired
    private AttDeviceDao attDeviceDao;
    @Autowired
    private AttDeviceOptionService attDeviceOptionService;
    @Autowired
    private AttAreaPersonDao attAreaPersonDao;
    @Autowired
    private AttAreaPersonService attAreaPersonService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersBioTemplateService persBioTemplateService;
    @Autowired
    private PersCardService persCardService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private AttSendPersonToDeviceOperate attSendPersonToDeviceOperate;
    @Autowired
    private AuthSessionServcie authSessionServcie;
    @Autowired
    private AttCacheManager attCacheManager;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private PersPersonCacheService persPersonCacheService;
    @Autowired
    private Pers2OtherService pers2OtherService;
    @Autowired
    private AuthSessionProvider authSessionProvider;
    @Autowired
    private AttSetPersonToDevOperate attSetPersonToDevOperate;
    @Autowired
    private PersBioPhotoService persBioPhotoService;
    @Autowired
    private AttDevCmdManager attDevCmdManager;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private AttDeviceService attDeviceService;
    @Autowired
    private AttPersonService attPersonService;

    @Override
    @Transactional
    public AttPersonItem saveItem(AttPersonItem item) {
        AttPerson attPerson = Optional.ofNullable(item).map(AttPersonItem::getId).filter(StringUtils::isNotBlank)
            .flatMap(attPersonDao::findById).filter(Objects::nonNull).orElse(new AttPerson());
        ModelUtil.copyProperties(item, attPerson);
        attPersonDao.save(attPerson);
        item.setId(attPerson.getId());
        return item;
    }

    @Override
    public List<AttPersonItem> getByCondition(AttPersonItem condition) {
        return (List<AttPersonItem>)attPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return attPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public Pager loadPagerByAuthFilterByGroup(String sessionId, BaseItem condition, int page, int size) {
        AttGroupPersonItem source = (AttGroupPersonItem)condition;
        AttPersonItem attPersonItem = new AttPersonItem();
        attPersonItem.setPersonPin(source.getPersonPin());
        attPersonItem.setLikeName(source.getLikeName());
        attPersonItem.setDeptName(source.getDeptName());
        attPersonItem.setGroupId(source.getGroupId());
        buildCondition(sessionId, attPersonItem);
        Pager pager = getItemsByPage(attPersonItem, page, size);
        // 我现在就是要获取，不需要过滤的数据
        List<AttPersonItem> attPersonItemList = (List<AttPersonItem>)pager.getData();

        List<AttGroupPersonItem> attGroupPersonItemList = new ArrayList<AttGroupPersonItem>();
        if (!CollectionUtil.isEmpty(attPersonItemList)) {
            attPersonItemList.forEach(item -> {
                AttGroupPersonItem attGroupPersonItem = ModelUtil.copyProperties(item, new AttGroupPersonItem());
                attGroupPersonItem.setId(item.getPersonId());
                attGroupPersonItemList.add(attGroupPersonItem);
            });
        }
        pager.setData(attGroupPersonItemList);
        return pager;
    }

    /**
     * 封装条件
     * 
     * @param sessionId
     * @param condition
     */
    private void buildCondition(String sessionId, AttPersonItem condition) {
        // 封装部门条件
        if (StringUtils.isBlank(condition.getInDeptId())) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, condition.getDeptId(),
                condition.getDeptCode(), condition.getDeptName()));
            condition.setDeptId(null);
        } else if (StringUtils.isNotBlank(sessionId)) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByAuthAndIds(sessionId, condition.getInDeptId()));
            condition.setDeptId(null);
        }
    }

    @Override
    public Pager getAttPersonSelectByPage(String sessionId, AttPersonSelectItem condition, int pageNo, int pageSize) {
        // 设置查询条件。
        PersPersonSelectItem persPersonSelectItem = new PersPersonSelectItem();
        persPersonSelectItem.setPersPin(condition.getPersonPin());
        persPersonSelectItem.setLikeName(condition.getLikeName());
        persPersonSelectItem.setDeptId(condition.getDeptId());
        persPersonSelectItem.setIsIncludeLower(condition.getIsIncludeLower());
        // persPersonSelectItem.setDeptName(condition.getDeptName());
        if (condition.getSortName() != null) {
            String sortName = "";
            if (condition.getSortName().equals("personPin")) {
                sortName = "persPin";
            } else if (condition.getSortName().equals("personName")) {
                sortName = "persName";
            } else {
                sortName = condition.getSortName();
            }
            persPersonSelectItem.setSortName(sortName);
            persPersonSelectItem.setSortOrder(condition.getSortOrder());
        }
        if (StringUtils.isNotBlank(condition.getSelectId())) {
            persPersonSelectItem.setNotInId(condition.getSelectId());
        }
        Pager pager =
            persPersonService.findPersonSelectItemAndIsIncludeLower(sessionId, persPersonSelectItem, pageNo, pageSize);

        // 重新组装数据
        List<AttPersonSelectItem> attPersonSelectItemList = new ArrayList<>();
        List<PersPersonSelectItem> persPersonSelectItemList = (List<PersPersonSelectItem>)pager.getData();
        AttPersonSelectItem attPersonSelectItem = null;
        for (PersPersonSelectItem personSelectItem : persPersonSelectItemList) {
            attPersonSelectItem = new AttPersonSelectItem();
            attPersonSelectItem.setId(personSelectItem.getId());
            attPersonSelectItem.setPersonId(personSelectItem.getId());
            attPersonSelectItem.setPersonPin(personSelectItem.getPersPin());
            attPersonSelectItem.setPersonName(personSelectItem.getPersName());
            attPersonSelectItem.setPersonLastName(personSelectItem.getPersLastName());
            attPersonSelectItem.setDeptName(personSelectItem.getDeptName());
            attPersonSelectItem.setCardNo(personSelectItem.getCardNo());
            attPersonSelectItem.setGender(personSelectItem.getGender());
            attPersonSelectItemList.add(attPersonSelectItem);
        }
        pager.setData(attPersonSelectItemList);
        return pager;
    }

    @Override
    public Pager getItemsByPageForAttCal(String sessionId, AttManualCalculationItem condition, int pageNo,
        int pageSize) {
        // 显示人事模块的所有人员 -- add by hook.fang 2019-06-10
        PersPersonSelectItem persCondition = new PersPersonSelectItem();
        persCondition.setIsIncludeLower(condition.getIsIncludeLower());
        persCondition.setPersPin(condition.getPin()).setPersName(condition.getName())
            .setPersLastName(condition.getLastName()).setLikeName(condition.getLikeName())
            .setDeptId(condition.getDeptId()).setDeptCode(condition.getDeptCode()).setDeptName(condition.getDeptName());
        Pager pager =
            persPersonService.findPersonSelectItemAndIsIncludeLower(sessionId, persCondition, pageNo, pageSize);
        List<AttManualCalculationItem> itemList = new ArrayList<>();
        for (PersPersonSelectItem person : (List<PersPersonSelectItem>)pager.getData()) {
            AttManualCalculationItem item = new AttManualCalculationItem();
            item.setId(person.getId());
            item.setDeptId(person.getDeptId()).setDeptCode(person.getDeptCode()).setDeptName(person.getDeptName())
                .setPin(person.getPersPin()).setName(person.getPersName()).setLastName(person.getPersLastName());
            itemList.add(item);
        }
        pager.setData(itemList);
        return pager;
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                attPersonDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AttPersonItem getItemById(String id) {
        List<AttPersonItem> items = getByCondition(new AttPersonItem(id));
        return Optional.ofNullable(items).filter(i -> !i.isEmpty()).map(i -> i.get(0)).orElse(null);
    }

    @Override
    public List<AttPersonItem> getItemByIds(String ids) {
        AttPersonItem condition = new AttPersonItem();
        condition.setInId(ids);
        return getByCondition(condition);
    }

    @Override
    public List<AttPersonItem> getItemByDeptIds(String deptIds) {
        AttPersonItem condition = new AttPersonItem();
        condition.setInDeptId(deptIds);
        return getByCondition(condition);
    }

    @Override
    public AttPersonItem getItemByPersonId(String personId) {
        AttPersonItem condition = new AttPersonItem();
        condition.setPersonId(personId);
        List<AttPersonItem> itemList = getByCondition(condition);
        return Optional.ofNullable(itemList).filter(i -> !i.isEmpty()).map(i -> i.get(0)).orElse(null);
    }

    @Override
    public List<AttPersonItem> getItemByPersonIds(Collection<String> personIds) {
        List<AttPersonItem> attPersonItemList = new ArrayList<>();
        if (CollectionUtil.isEmpty(personIds)) {
            return attPersonItemList;
        }
        List<List<String>> splitPersonIds = CollectionUtil.split(personIds, CollectionUtil.splitSize);
        splitPersonIds.forEach(splitPersonId -> {
            AttPersonItem condition = new AttPersonItem();
            condition.setPersonIdIn(StringUtils.join(splitPersonId,","));
            List<AttPersonItem> itemList = getByCondition(condition);
            attPersonItemList.addAll(itemList);
        });
        return attPersonItemList;
    }

    @Override
    public AttPersonItem getItemByPersonPin(String personPin) {
        AttPersonItem condition = new AttPersonItem();
        condition.setPersonPin(personPin);
        condition.setEquals(true);
        List<AttPersonItem> items = getByCondition(condition);
        return Optional.ofNullable(items).filter(i -> !i.isEmpty()).map(i -> i.get(0)).orElse(null);
    }

    @Override
    public List<AttPersonItem> getItemByPersonPins(Collection<String> personPins) {
        List<List<String>> personPinsGroup = CollectionUtil.split(personPins, CollectionUtil.splitSize);
        List<AttPersonItem> attPersonItemList = new ArrayList<>();
        for (List<String> pins : personPinsGroup) {
            AttPersonItem condition = new AttPersonItem();
            condition.setPersonPinIn(StringUtils.join(pins, ","));
            List<AttPersonItem> attPersonItems = getByCondition(condition);
            attPersonItemList.addAll(attPersonItems);
        }
        return attPersonItemList;
    }

    @Override
    public Pager getNoExistPerson(String sessionId, AttPersonSelectItem condition, String type, int pageNo, int pageSize) {
        // 当前登录权限过滤
        if (StringUtils.isNotBlank(sessionId)) {
            SecuritySubject securitySubject = authSessionServcie.getSecuritySubject(sessionId);
            // 非超级用户进行数据权限过滤
            if (!securitySubject.getIsSuperuser() && securitySubject.getDepartmentIds() != null
                    && securitySubject.getDepartmentIds().size() > 0) {
                condition.setUserId(securitySubject.getUserId());
            }
        }
        // 是否包含下级
        if ("true".equals(condition.getIsIncludeLower()) && !StringUtils.isBlank(condition.getDeptId())) {
            List<AuthDepartmentItem> itemAndChildIds = authDepartmentService.getItemAndChildById(condition.getDeptId());
            if (itemAndChildIds.size() > 0) {
                condition.setInDeptId(CollectionUtil.getItemIds(itemAndChildIds));
                condition.setDeptId(null);
            }
        }
        return attPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
    }

    @Override
    @Transactional
    public void analysisUserData(String sn, String dataPack) {
        Map<String, List<Map<String, String>>> dataMap = new HashMap<>();
        List<Map<String, String>> dataList = null;
        Map<String, String> tableData = null;
        Set<String> pins = new HashSet<>();
        for (String data : dataPack.split("\n")) {
            String[] dataArray = data.split(" ", 2);
            if (dataArray.length == 2) {
                String table = dataArray[0].trim();
                String infoData = dataArray[1].trim();
                dataList = dataMap.get(table);
                if (CollectionUtil.isEmpty(dataList)) {
                    dataList = new ArrayList<>();
                    dataMap.put(table, dataList);
                }
                tableData = getDataMap(infoData);
                dataList.add(tableData);

                pins.add(tableData.get("PIN"));
            }
        }

        // 获取对应区域的设备
        AttDevice attDevice = attDeviceDao.findByDevSn(sn);
        List<PersPersonItem> personItems = persPersonService.getItemsByPins(pins);
        Map<String, String> pinsAndPersonIds =
            personItems.stream().collect(Collectors.toMap(PersPersonItem::getPin, PersPersonItem::getId));
        dataList = dataMap.get("USER");// 优先保存人员
        if (!CollectionUtil.isEmpty(dataList)) {
            log.info("----------------  save person info total count : " + dataList.size() + " --------- "
                + pins.toString());
            Map<String, String> batchSavePerson = batchSavePerson(attDevice, personItems, pins, dataList);
            pinsAndPersonIds.putAll(batchSavePerson);// 批量保存人员，也需要保存人员的考勤区域，权限等
        }

        // 通过人员找到区域 区域再找设备 然后根据人员匹配设备
        Map<String, Set<AttDevice>> personPinDevicesMap = getPersonDevices(personItems, attDevice);

        List<PersBioTemplateItem> bioTemplateItems = new ArrayList<>();
        Set<String> personPinFPSet = new HashSet<>();// 同步到同区域设备
        dataList = dataMap.get("FP");// 保存指纹
        if (!CollectionUtil.isEmpty(dataList)) {
            log.info(
                "----------------  save person FP total count : " + dataList.size() + " --------- " + pins.toString());
            String fpVersion = attDeviceOptionService.getValueByDevSnAndName(sn, "FPVersion");
            dataList.forEach(map -> {
                PersBioTemplateItem persBioTemplateItem = new PersBioTemplateItem();
                persBioTemplateItem.setPersonPin(map.get("PIN"));
                persBioTemplateItem.setTemplateNo(MapUtils.getShort(map, "FID"));// 手指编号
                persBioTemplateItem.setTemplateNoIndex(PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX);
                persBioTemplateItem.setTemplate(map.get("TMP"));
                persBioTemplateItem.setBioType(BaseConstants.BaseBioType.FP_BIO_TYPE);
                persBioTemplateItem.setVersion(fpVersion);// 根据设备的指纹版本值。
                persBioTemplateItem.setDuress(false);
                if (StringUtils.isNotBlank(map.get("Duress"))) {
                    persBioTemplateItem.setDuress("1".equals(map.get("Duress")));
                }
                bioTemplateItems.add(persBioTemplateItem);
                personPinFPSet.add(map.get("PIN"));
            });
        }

        dataList = dataMap.get("FACE");// 保存人脸
        List<String> personPinFACEList = new ArrayList<>();// 同步到同区域设备
        if (!CollectionUtil.isEmpty(dataList)) {
            log.info("----------------  save person Face info total count : " + dataList.size() + " --------- "
                + pins.toString());
            String faceVersion = attDeviceOptionService.getValueByDevSnAndName(sn, "FaceVersion");
            dataList.forEach(map -> {
                PersBioTemplateItem persBioTemplateItem = new PersBioTemplateItem();
                persBioTemplateItem.setPersonPin(map.get("PIN"));
                persBioTemplateItem.setTemplateNo(PersConstants.PERS_BIOTEMPLATE_DEF_NO);
                persBioTemplateItem.setTemplateNoIndex(MapUtils.getShort(map, "FID"));// 人脸的第几个模板 0-11
                persBioTemplateItem.setTemplate(map.get("TMP"));
                persBioTemplateItem.setBioType(BaseConstants.BaseBioType.FACE_BIO_TYPE);
                if (StringUtils.isNotBlank(faceVersion)) {
                    persBioTemplateItem.setVersion(faceVersion);// 根据设备的人脸版本值。
                }
                bioTemplateItems.add(persBioTemplateItem);
                personPinFACEList.add(map.get("PIN"));
            });
        }

        if (!CollectionUtil.isEmpty(bioTemplateItems)) {

            // 更新生物模板
            persBioTemplateService.updateBioTemplate(bioTemplateItems);

            for (String personPin : personPinFPSet) {
                Set<AttDevice> attDevices = personPinDevicesMap.get(personPin);
                if (CollectionUtil.isEmpty(attDevices)) {
                    continue;
                }
                // 异步处理指纹模板下发
                attSendPersonToDeviceOperate.asyncSendPersonFPToDevHandler(new ArrayList<>(attDevices),
                    Arrays.asList(personPin));
            }
            String personId = "";
            for (String personPin : personPinFACEList) {
                Set<AttDevice> attDevices = personPinDevicesMap.get(personPin);
                if (CollectionUtil.isEmpty(attDevices)) {
                    continue;
                }
                personId = pinsAndPersonIds.get(personPin);
                // 异步处理面部模板下发
                attSendPersonToDeviceOperate.asyncSendPersonFaceToDevHandler(new ArrayList<>(attDevices),
                    Arrays.asList(personId));
            }

            // 同步生物模板到其他业务模块 add by hook.fang 2020-10-30
            CompletableFuture.runAsync(() -> {
                sendBioTemplate2OtherModule(pins, bioTemplateItems, pinsAndPersonIds);
            });
        }

        dataList = dataMap.get("USERPIC");// 保存人员照片
        Set<String> personPinUserPicSet = new HashSet<>();// 同步到同区域设备
        if (!CollectionUtil.isEmpty(dataList)) {
            log.info("----------------  save person photo total count : " + dataList.size() + " --------- "
                + pins.toString());

            // 分发给其他模块的比对照片 add by hook.fang 2020-11-24
            Collection<PersUserPic2OtherItem> persUserPicItems = new ArrayList<PersUserPic2OtherItem>();

            dataList.forEach(map -> {
                persPersonService.saveUserPhoto(map.get("PIN"), map.get("Content"));

                PersUserPic2OtherItem persUserPicItem = new PersUserPic2OtherItem();
                persUserPicItem.setPin(map.get("PIN"));
                persUserPicItem.setPersonId(pinsAndPersonIds.get(map.get("PIN")));
                persUserPicItem.setContent(map.get("Content"));
                persUserPicItems.add(persUserPicItem);
                personPinUserPicSet.add(map.get("PIN"));
            });

            // 同步人员头像到相同区域的所有设备
            for (String personPin : personPinUserPicSet) {
                Set<AttDevice> attDevices = personPinDevicesMap.get(personPin);
                if (CollectionUtil.isEmpty(attDevices)) {
                    continue;
                }
                // 异步处理人员照片下发
                attSendPersonToDeviceOperate.asyncSendPersonPhotoToDevHandler(new ArrayList<>(attDevices),
                    Arrays.asList(personPin));
            }

            // 同步人员头像到其他业务模块 add by hook.fang 2020-11-25
            CompletableFuture.runAsync(() -> {
                pers2OtherService.setUserPic2OtherModule(persUserPicItems, ConstUtil.SYSTEM_MODULE_ATT);
            });
        }
        // 保存设备上传的对比照片
        dataList = dataMap.get("BIOPHOTO");
        Set<String> personPinBioPhotoSet = new HashSet<>();// 同步到同区域设备
        if (!CollectionUtil.isEmpty(dataList)) {
            log.info("----------------  save person bio photo total count : " + dataList.size() + " --------- "
                + pins.toString());

            // 分发给其他模块的比对照片 add by hook.fang 2020-11-24
            Collection<PersBioPhoto2OtherItem> persbioPhotoItems = new ArrayList<PersBioPhoto2OtherItem>();

            dataList.forEach(map -> {

                PersBioPhotoSaveItem saveItem = new PersBioPhotoSaveItem();
                saveItem.setPersonPin(map.get("PIN"));
                saveItem.setFileName(map.get("FileName"));
                saveItem.setPhotoBase64(map.get("Content"));
                saveItem.setBioType(Short.valueOf(map.get("Type")));
                persBioPhotoService.saveBioPhoto(saveItem);

                PersBioPhoto2OtherItem persBioPhotoItem = new PersBioPhoto2OtherItem();
                persBioPhotoItem.setPin(map.get("PIN"));
                persBioPhotoItem.setPersonId(pinsAndPersonIds.get(map.get("PIN")));
                persBioPhotoItem.setBioType(Short.valueOf(map.get("Type")));
                persBioPhotoItem.setContent(map.get("Content"));
                persbioPhotoItems.add(persBioPhotoItem);
                personPinBioPhotoSet.add(map.get("PIN"));
            });

            // 同步比对照片到相同区域的所有设备
            for (String personPin : personPinBioPhotoSet) {
                Set<AttDevice> attDevices = personPinDevicesMap.get(personPin);
                if (CollectionUtil.isEmpty(attDevices)) {
                    continue;
                }
                // 对比照片保存本地以后，对比照片下发
                attSetPersonToDevOperate.sendBioPhotoToDevice(attDevices, personPin);
            }

            // 同步比对照片到其他业务模块 add by hook.fang 2020-11-24
            CompletableFuture.runAsync(() -> {
                pers2OtherService.setBioPhoto2OtherModule(persbioPhotoItems, ConstUtil.SYSTEM_MODULE_ATT);
            });
        }
    }

    @Override
    @Transactional
    public void handlerBioData(String sn, String data) {
        String[] dataArr = data.split("\n");
        Set<String> pins = new HashSet<>();
        List<PersBioTemplateItem> persBioTemplateItemList = new ArrayList<>();
        PersBioTemplateItem persBioTemplateItem = null;
        for (String bioData : dataArr) {
            String[] split = bioData.split(" ", 2);
            String table = split[0];
            String infoData = split[1];
            Map<String, String> dataMap = getDataMap(infoData);
            pins.add(dataMap.get("Pin"));
            persBioTemplateItem = new PersBioTemplateItem();
            persBioTemplateItem.setPersonPin(dataMap.get("Pin"));
            persBioTemplateItem.setDuress(false);// 考勤没有胁迫数据默认 false
            if (StringUtils.isNotBlank(dataMap.get("Duress"))) {
                persBioTemplateItem.setDuress("1".equals(dataMap.get("Duress")));
            }
            persBioTemplateItem.setValidType(MapUtils.getShort(dataMap, "Valid"));
            persBioTemplateItem.setTemplateNo(MapUtils.getShort(dataMap, "No"));
            persBioTemplateItem.setTemplateNoIndex(MapUtils.getShort(dataMap, "Index"));// 生物个体模板编号
            persBioTemplateItem.setBioType(MapUtils.getShortValue(dataMap, "Type"));
            persBioTemplateItem.setTemplate(dataMap.get("Tmp"));// 模板
            String bioVer = MapUtils.getString(dataMap, "MajorVer");
            if (StringUtils.isNotBlank(dataMap.get("MinorVer"))
                && !"0".equals(MapUtils.getString(dataMap, "MinorVer"))) {
                bioVer += "." + MapUtils.getString(dataMap, "MinorVer");
            }
            persBioTemplateItem.setVersion(bioVer);// 算法版本号
            persBioTemplateItemList.add(persBioTemplateItem);
        }

        log.info("save person biodata total count : " + persBioTemplateItemList.size() + ", pins = " + pins.toString());

        // 更新生物模板
        persBioTemplateService.updateBioTemplate(persBioTemplateItemList);

        // 获取对应区域的设备
        AttDevice attDevice = attDeviceDao.findByDevSn(sn);
        List<PersPersonItem> personItems = persPersonService.getItemsByPins(pins);
        Map<String, Set<AttDevice>> personPinDevicesMap = getPersonDevices(personItems, attDevice);
        for (PersPersonItem persPersonItem : personItems) {
            Set<AttDevice> attDevices = personPinDevicesMap.get(persPersonItem.getPin());
            if (CollectionUtil.isEmpty(attDevices)) {
                continue;
            }
            // 进行生物一体化模板的下发
            attSetPersonToDevOperate.sendPersonBioDataToDevice(new ArrayList(attDevices), persPersonItem, persBioTemplateItemList);
        }

        // 同步一体化生物模板到其他业务模块 add by hook.fang 2020-10-30
        CompletableFuture.runAsync(() -> {
            Map<String, String> pinsAndPersonIds = persPersonService.getPinsAndIdsByPins(pins);
            sendBioTemplate2OtherModule(pins, persBioTemplateItemList, pinsAndPersonIds);
        });
    }

    /**
     * 同步生物模板到其他业务模块
     *
     * @param pins
     * @param itemsList
     * @param pinsAndPersonIds
     */
    private void sendBioTemplate2OtherModule(Set<String> pins, List<PersBioTemplateItem> itemsList,
        Map<String, String> pinsAndPersonIds) {
        Map<String, List<PersBioTemplateItem>> bioTemplateItemAry = itemsList.stream()
            .filter(i -> i.getTemplate().length() > 0).collect(Collectors.groupingBy(item -> item.getPersonPin()));
        pins.forEach(pin -> {
            String personId = pinsAndPersonIds.get(pin);
            Collection<PersBioTemplate2OtherItem> bioTemplateitems = new ArrayList<PersBioTemplate2OtherItem>();
            List<PersBioTemplateItem> bioTemplateItemList = bioTemplateItemAry.get(pin);
            bioTemplateItemList.forEach(item -> {
                PersBioTemplate2OtherItem bioTempItem = new PersBioTemplate2OtherItem();
                ModelUtil.copyPropertiesIgnoreNull(item, bioTempItem);
                bioTempItem.setPersonId(personId);
                bioTempItem.setPin(pin);
                if (Objects.isNull(item.getDuress())) {
                    bioTempItem.setDuress(false);
                }
                if (item.getValidType() == null) {
                    bioTempItem.setValidType(BaseConstants.BaseBioType.VALID_FLAG_ENABLE);
                }
                bioTemplateitems.add(bioTempItem);
            });
            pers2OtherService.setBioTemplate2OtherModule(bioTemplateitems, ConstUtil.SYSTEM_MODULE_ATT);
        });
    }

    /**
     * 批量保存人员，也需要保存人员的考勤区域，权限等
     * 
     * @param currentDevice
     *            当前设备的设备对象
     * @param personItems
     *            人员集合
     * @param pins
     *            人员编号
     * @param dataList
     *            数据集合
     * @return
     */
    private Map<String, String> batchSavePerson(AttDevice currentDevice, List<PersPersonItem> personItems,
        Set<String> pins, List<Map<String, String>> dataList) {
        List<PersPersonItem> persPersonItems = new ArrayList<>();
        Map<String, String> pinsAndCardMap = new HashMap<>();
        // 原始或者变更后的编号-卡号集合
        Map<String, String> newPinsAndCardMap = new HashMap<>();
        AuthDepartmentItem defautDept = authDepartmentService.getDefautDept();
        Map<String, PersPersonItem> itemMap =
            personItems.stream().collect(Collectors.toMap(PersPersonItem::getPin, Function.identity()));
        // 仅接收存在的用户信息,暂时屏蔽功能
        // String receiveExistPers = attParamService.getReceivePersonOnlyDb();
        // 获取人员对应的主卡号
        Collection<String> ids = CollectionUtil.getItemIdsList(personItems);
        PersCardItem persCardItem = new PersCardItem();
        persCardItem.setPersonIdIn(StringUtils.join(ids, ","));
        persCardItem.setCardType(PersConstants.MAIN_CARD);
        persCardItem.setCardState(PersConstants.CARD_VALID);
        List<PersCardItem> persCardItems = persCardService.getByCondition(persCardItem);
        Map<String, PersCardItem> persCardItemMap =
            CollectionUtil.listToKeyMap(persCardItems, PersCardItem::getPersonPin);

        // 已存在人员pin和id集合
        Map<String, String> pinsAndIds = new HashMap<String, String>();
        // 新增人员pin集合
        Set<String> pinAry = new HashSet<String>();
        // 更新设备上传用户信息
        dataList.forEach(dataMap -> {
            String pin = dataMap.get("PIN");
            String verify = MapUtils.getString(dataMap, "Verify", "");
            String firstName = "";
            String lastName = "";
            // 解析设备上传的人员姓名
            String name = MapUtils.getString(dataMap, "Name", null);
            if (StringUtils.isNotBlank(name)) {
                String[] splitName = StringUtils.split(name, "&");
                if (splitName.length == 1) {
                    firstName = splitName[0];
                } else {
                    firstName = splitName[0];
                    lastName = splitName[1];
                }
            }
            // 密码
            String passwd = MapUtils.getString(dataMap, "Passwd", null);
            // 主卡号（判断主卡号是否变更，是则更新）
            PersCardItem tempCardItem = persCardItemMap.get(pin);
            // 放入原始卡号
            if (tempCardItem != null) {
                newPinsAndCardMap.put(pin, tempCardItem.getCardNo());
            }
            String card = MapUtils.getString(dataMap, "Card", null);
            if (StringUtils.isNotBlank(card)
                && (Objects.isNull(tempCardItem) || !card.equals(tempCardItem.getCardNo()))) {
                String persCardHex = baseSysParamService.getValByName("pers.cardHex");
                if ("1".equals(persCardHex)) {
                    card = AttCommonUtils.convertCardNo(card, 10, 16);
                }
                pinsAndCardMap.put(pin, card);
                newPinsAndCardMap.put(pin, card);
            }

            // 判断人员信息是否存在
            PersPersonItem persPersonItem = itemMap.get(pin);
            if (Objects.isNull(persPersonItem)) {
                /*if ("0".equals(receiveExistPers)) {
                    return;
                }*/
                persPersonItem = new PersPersonItem();
                persPersonItem.setPin(pin);
                persPersonItem.setIsFrom("ATT_DEVICE_" + currentDevice.getDevSn());
                // 赋值默认部门
                persPersonItem.setDeptId(defautDept.getId());
                pinAry.add(pin);
            } else {
                // 如果设备上的人员姓名、密码、卡号未变更，则不更新
                if (((StringUtils.isNotBlank(firstName) && firstName.equals(persPersonItem.getName()))
                    || StringUtils.isBlank(firstName))
                    && ((StringUtils.isNotBlank(lastName) && lastName.equals(persPersonItem.getLastName()))
                        || StringUtils.isBlank(lastName))
                    && ((StringUtils.isNotBlank(passwd) && passwd.equals(persPersonItem.getPersonPwd()))
                        || StringUtils.isBlank(passwd))
                    && ((StringUtils.isNotBlank(card) && Objects.nonNull(tempCardItem)
                        && card.equals(tempCardItem.getCardNo())) || StringUtils.isBlank(card))) {
                    pinsAndIds.put(pin, persPersonItem.getId());
                    return;
                }
            }

            // 更新人员姓名
            if (StringUtils.isBlank(persPersonItem.getName()) && StringUtils.isNotBlank(firstName)) {
                persPersonItem.setName(firstName);
            }
            if (StringUtils.isBlank(persPersonItem.getLastName()) && StringUtils.isNotBlank(lastName)) {
                persPersonItem.setLastName(lastName);
            }
            // 更新密码
            if (StringUtils.isNotBlank(passwd)) {
                persPersonItem.setPersonPwd(passwd);
            }
            // 验证方式
            if (StringUtils.isNotBlank(verify)) {
                // 跟陈庆辉确认，如果设备设置组验证方式，软件当自动识别处理
                if ("-1".equals(verify)) {
                    verify = "0";
                }
                persPersonItem.setVerifyMode(verify);
            }
            // 异常标志
            short exceptionFlag = 0;
            if (StringUtils.isNotEmpty(passwd)) {
                if (passwd.length() > 6) {
                    exceptionFlag = 2;
                }
            }
            if (AttStrUtils.isSpecialChar((pin))) {
                exceptionFlag = 3;
            }
            persPersonItem.setExceptionFlag(exceptionFlag);
            persPersonItems.add(persPersonItem);
        });

        // 如果人员信息未变更，则直接返回，不进行后续的业务处理
        if (Objects.isNull(persPersonItems) || persPersonItems.size() == 0) {
            return pinsAndIds;
        }

        // 批量保存人员
        List<PersPersonItem> saveItems = persPersonService.batchSaveItem(persPersonItems, ConstUtil.SYSTEM_MODULE_ATT);
        // 更新集合
        for (PersPersonItem item : saveItems) {
            Iterator<PersPersonItem> iterator = personItems.iterator();
            while (iterator.hasNext()) {
                PersPersonItem next = iterator.next();
                if (item.getPin().equals(next.getPin())) {
                    iterator.remove();
                    break;
                }
            }
            personItems.add(item);
        }
        Map<String, PersPersonItem> persPersonItemMap = CollectionUtil.listToKeyMap(saveItems, PersPersonItem::getPin);

        // 保存主卡号
        if (!CollectionUtil.isEmpty(pinsAndCardMap)) {
            buildPersonCard(pinsAndCardMap, persPersonItemMap, persCardItemMap);
        }

        // 屏蔽更新考勤人员从表信息，设备上传的操作权限不更新到软件(门禁、考勤统一) add by hook.fang 2020-11-27

        if (!CollectionUtil.isEmpty(pinAry)) {// 新增
            Map<String, PersPersonItem> personMap =
                saveItems.stream().collect(Collectors.toMap(PersPersonItem::getPin, Function.identity()));
            pinAry.forEach(pin -> {
                PersPersonItem personItem = personMap.get(pin);
                if (Objects.nonNull(personItem)) {
                    AttPerson attPerson = new AttPerson();
                    attPerson.setPersonId(personItem.getId()).setIsAttendance(true).setPersonPin(pin)
                        .setGroupId(AttCommonSchConstant.GroupConstant.DEFAULT_NO_GROUP_ID).setPerDevAuth((short)0);
                    // 验证方式
                    if (StringUtils.isNotBlank(personItem.getVerifyMode())) {
                        attPerson.setVerifyMode(Short.valueOf(personItem.getVerifyMode()));
                    } else {
                        attPerson.setVerifyMode((short)0);
                    }
                    attPersonDao.save(attPerson);
                }
            });
        }

        // 人员从设备上传，赋予设备所在区域的考勤区域
        Map<String, String> pinsAndPersonIds =
            saveItems.stream().collect(Collectors.toMap(PersPersonItem::getPin, PersPersonItem::getId));
        List<AttAreaPerson> areaPerson = attAreaPersonDao.findByPersonIdIn(pinsAndPersonIds.values());
        // personId, {areaId:AttAreaPerson}
        Map<String, Map<String, AttAreaPerson>> personAreaMap = areaPerson.stream().collect(
            Collectors.groupingBy(AttAreaPerson::getPersonId, Collectors.toMap(AttAreaPerson::getAreaId, a -> a)));
        pinsAndPersonIds.forEach((pin, personId) -> {
            Map<String, AttAreaPerson> areaMap = personAreaMap.get(personId);
            if (CollectionUtil.isEmpty(areaMap)
                || (Objects.nonNull(areaMap) && !areaMap.containsKey(currentDevice.getAreaId()))) {
                String areaId = currentDevice.getAreaId();
                AttAreaPersonItem item = new AttAreaPersonItem();
                item.setPersonId(personId).setAreaId(areaId);
                attAreaPersonService.saveItem(item);
            }
        });

        // 同步人员到相同区域的所有设备
        Map<String, Set<AttDevice>> personPinDevicesMap = getPersonDevices(personItems, currentDevice);
        for (PersPersonItem persPersonItem : saveItems) {
            Set<AttDevice> attDevices = personPinDevicesMap.get(persPersonItem.getPin());
            if (CollectionUtil.isEmpty(attDevices)) {
                continue;
            }
            // 软件同步到设备的逻辑设计
            attSendPersonToDeviceOperate.asyncSendPersonToDevHandler(new ArrayList<>(attDevices),
                Arrays.asList(persPersonItem));
        }

        // 同步人员信息到第三方模块 -- add by hook.fang 2020-10-29
        Collection<PersPersonInfo2OtherItem> personInfoToOthers = new ArrayList<PersPersonInfo2OtherItem>();
        PersPersonInfo2OtherItem personInfo = null;
        for (PersPersonItem persPersonItem : saveItems) {
            personInfo = new PersPersonInfo2OtherItem();
            personInfo.setPersonId(persPersonItem.getId());
            personInfo.setPin(persPersonItem.getPin());
            personInfo.setName(persPersonItem.getName());
            personInfo.setLastName(persPersonItem.getLastName());
            personInfo.setPersonPwd(persPersonItem.getPersonPwd());
            String carNo = newPinsAndCardMap.get(persPersonItem.getPin());
            if (StringUtils.isNotBlank(carNo)) {
                personInfo.setCardNo(carNo);
            }
            personInfoToOthers.add(personInfo);
        }
        CompletableFuture.runAsync(() -> {
            pers2OtherService.setPersonInfo2OtherModule(personInfoToOthers, ConstUtil.SYSTEM_MODULE_ATT);
        });

        return pinsAndPersonIds;
    }

    /** 组装并更新人员卡表 */
    private void buildPersonCard(Map<String, String> pinsAndCardMap, Map<String, PersPersonItem> persPersonItemMap,
        Map<String, PersCardItem> persCardItemMap) {

        // 新增或编辑卡对象集合
        List<PersCardItem> persCardItemList = new ArrayList<>();
        // 判断卡号是否重复
        Collection<String> cards = pinsAndCardMap.values();
        List<PersCardItem> existPersCardItems = persCardService.getCardByCardNos(cards);

        pinsAndCardMap.forEach((pin, card) -> {
            PersCardItem item = persCardItemMap.get(pin);
            if (Objects.isNull(item)) {
                PersPersonItem personItem = persPersonItemMap.get(pin);
                item = new PersCardItem();
                item.setPersonId(personItem.getId());
                item.setPersonPin(pin);
                item.setCardType(PersConstants.MAIN_CARD);
            } else if (card.equals(item.getCardNo())) {
                // 卡号相同不做更新
                return;
            }
            item.setCardNo(card);
            item.setCardState(PersConstants.CARD_VALID);
            item.setIssueTime(DateUtil.getCurrentTime());
            persCardItemList.add(item);

            // 卡号重复，更新人员异常状态为卡号重复异常
            if (existPersCardItems != null) {
                for (PersCardItem existPersCardItem : existPersCardItems) {
                    if (card.equals(existPersCardItem.getCardNo()) && !pin.equals(existPersCardItem.getPersonPin())) {
                        persPersonService.updatePersonExceptionFlag(pin, PersConstants.PERSON_ISEXCEPTION);
                    }
                }
            }
        });

        // 批量保存或更新卡对象
        persCardService.saveCards(persCardItemList);
    }

    /**
     * 设备上传的数据通用解析，根据数据，返回Map格式的数据
     *
     * @param data
     * @return true为包含，false为不包含
     */
    private Map<String, String> getDataMap(String data) {
        Map<String, String> dataMap = new HashMap<String, String>();
        String[] attField = data.split("\t");// 制表符分隔
        for (String dataInfo : attField) {
            String[] dataIn = dataInfo.split("=", 2);
            dataMap.put(dataIn[0], dataIn[1]);
        }
        return dataMap;
    }



    @Override
    @Transactional
    public void updateAttPerson(String groupId, String personIds, String deptIds) {
        if (StringUtils.isBlank(personIds) && StringUtils.isBlank(deptIds)) {
            return;
        }
        PersPersonItem condition = new PersPersonItem();
        // 按人
        if (StringUtils.isNotBlank(personIds)) {
            condition.setInId(personIds);
        }
        // 按部门
        if (StringUtils.isNotBlank(deptIds)) {
            condition.setInDeptId(deptIds);
        }
        // 调整分组添加人员按人/按部门查询人员取人事 by ljf 2019/7/8
        List<PersPersonItem> personItemAry = persPersonService.getByCondition(condition);
        if (personItemAry != null) {
            AttGroup attGroup = attGroupDao.findById(groupId).orElse(new AttGroup());
            List<List<PersPersonItem>> splitPersonItemList =
                CollectionUtil.split(personItemAry, CollectionUtil.splitSize);
            for (List<PersPersonItem> personItemList : splitPersonItemList) {
                Collection<String> personIdList =
                    CollectionUtil.getPropertyList(personItemList, PersPersonItem::getId, "-1");
                List<String> deleteGroupIdList = new ArrayList<>();
                List<String> deletePersIdList = new ArrayList<>();
                List<AttPerson> attPersonAllList = new ArrayList<AttPerson>();
                List<AttPerson> attPersonList = attPersonDao.findByPersonIdIn(personIdList);
                Map<String, AttPerson> personItemMap =
                    CollectionUtil.listToKeyMap(attPersonList, AttPerson::getPersonId);
                personItemList.forEach(persItem -> {
                    AttPerson attPerson = personItemMap.get(persItem.getId());
                    if (!Objects.isNull(attPerson))// 已存在考勤人员从表
                    {
                        if (!AttCommonSchConstant.GroupConstant.DEFAULT_NO_GROUP_ID.equals(attPerson.getGroupId())) {
                            deleteGroupIdList.add(attPerson.getGroupId());
                            deletePersIdList.add(attPerson.getPersonId());
                        }
                    } else// 未添加到考勤人员从表
                    {
                        attPerson = new AttPerson();
                        ModelUtil.copyPropertiesIgnoreNull(persItem, attPerson);
                        attPerson.setPersonId(persItem.getId());
                        attPerson.setPersonPin(persItem.getPin());
                        attPerson.setPerDevAuth((short)0);
                        attPerson.setIsAttendance(true);
                    }
                    attPerson.setGroupId(attGroup.getId());
                    //  校验方式默认自动
                    if (attPerson.getVerifyMode() == null) {
                        attPerson.setVerifyMode((short)0);
                    }
                    attPersonAllList.add(attPerson);
                });

                attPersonDao.saveAll(attPersonAllList);

                // 分组加人更新人事人员缓存的考勤附加参数的分组ID
                updatePersonCacheExtParams(ModelUtil.copyListProperties(attPersonAllList, AttPersonItem.class));
            }
        }
    }

    @Override
    @Transactional
    public void deleteByIds(String personIds, String groupId) {
        if (StringUtils.isNotBlank(personIds)) {
            List<String> personIdsList = StrUtil.strToList(personIds);
            List<AttPerson> attPersonList = attPersonDao.findByPersonIdIn(personIdsList);
            if (!CollectionUtil.isEmpty(attPersonList)) {
                // 设置分组默认值
                attPersonList.forEach(attPerson -> {
                    attPerson.setGroupId(AttCommonSchConstant.GroupConstant.DEFAULT_NO_GROUP_ID);
                });
                attPersonDao.saveAll(attPersonList);

                // 分组删除人员更新人事人员缓存的考勤附加参数的分组ID
                updatePersonCacheExtParams(ModelUtil.copyListProperties(attPersonList, AttPersonItem.class));
            }
        }
    }

    @Override
    public String getRemoteAttPersonData(String sn, String pin) {
        PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
        if (persPersonItem != null) {
            StringBuilder cmd = new StringBuilder();
            // 人员信息相关
            String uersinfo = "DATA UPDATE USERINFO PIN=%s\tName=%s\tPasswd=%s\tCard=%s\tPri=%s\n";
            AttPerson attPerson = attPersonDao.findByPersonId(persPersonItem.getId());
            String cardNo = "";
            if (persPersonItem.getCardNos() != null) {
                cardNo = persPersonItem.getCardNos().split(";")[0];
            }
            String passWd = "";
            if (persPersonItem.getPersonPwd() != null) {
                passWd = persPersonItem.getPersonPwd();
            }
            cmd.append(String.format(uersinfo, pin, persPersonItem.getName(), passWd, cardNo,
                attPerson.getPerDevAuth().toString()));

            // 生物信息
            AttDevice attDevice = attDeviceDao.findByDevSn(sn);
            if (attDeviceOptionService.isSupportFunction(attDevice.getId(), "FingerFunOn"))// 判断设备是否支持指纹
            {
                PersBioTemplateItem persBioTemplateItem = new PersBioTemplateItem();
                persBioTemplateItem.setPersonId(persPersonItem.getId());
                persBioTemplateItem.setBioType((short)1);// 指纹
                List<PersBioTemplateItem> persBioTemplateItemList =
                    persBioTemplateService.getByCondition(persBioTemplateItem);
                if (persBioTemplateItemList != null && persBioTemplateItemList.size() > 0) {

                    for (PersBioTemplateItem bioTemplateItem : persBioTemplateItemList) {
                        cmd.append("\r\n");
                        cmd.append("DATA UPDATE FINGERTMP PIN=" + pin + "\tFID=" + bioTemplateItem.getTemplateNo()
                            + "\tSize=" + bioTemplateItem.getTemplate().length() + "\tValid="
                            + bioTemplateItem.getValidType() + "\tTMP=" + bioTemplateItem.getTemplate());
                    }
                }
            }
            if (attDeviceOptionService.isSupportFunction(attDevice.getId(), "FaceFunOn"))// 判断设备是否支持人脸
            {
                String faceVersion = attDeviceOptionService.getValueByDevSnAndName(attDevice.getDevSn(), "FaceVersion");
                if ("7".equals(faceVersion))// 设备支持7.0人脸数据
                {
                    PersBioTemplateItem persBioTemplateItem = new PersBioTemplateItem();
                    persBioTemplateItem.setPersonId(persPersonItem.getId());
                    persBioTemplateItem.setBioType((short)2);// 人脸
                    persBioTemplateItem.setVersion("7");// 7.0人脸
                    List<PersBioTemplateItem> persBioTemplateItemList =
                        persBioTemplateService.getByCondition(persBioTemplateItem);
                    if (persBioTemplateItemList != null && persBioTemplateItemList.size() > 0) {
                        for (PersBioTemplateItem bioTemplateItem : persBioTemplateItemList) {
                            cmd.append("\r\n");
                            cmd.append("DATA UPDATE FACE PIN=" + pin + "\tFID=" + bioTemplateItem.getTemplateNo()
                                + "\tSize=" + bioTemplateItem.getTemplate().length() + "\tValid="
                                + bioTemplateItem.getValidType() + "\tTMP=" + bioTemplateItem.getTemplate());
                        }
                    }
                } else if ("9".equals(faceVersion))// 设备支持9.0人脸数据
                {
                    PersBioTemplateItem persBioTemplateItem = new PersBioTemplateItem();
                    persBioTemplateItem.setPersonId(persPersonItem.getId());
                    persBioTemplateItem.setBioType((short)2);// 人脸
                    persBioTemplateItem.setVersion("9");// 9.0人脸
                    List<PersBioTemplateItem> persBioTemplateItemList =
                        persBioTemplateService.getByCondition(persBioTemplateItem);
                    if (persBioTemplateItemList != null && persBioTemplateItemList.size() > 0) {
                        for (PersBioTemplateItem bioTemplateItem : persBioTemplateItemList) {
                            cmd.append("\r\n");
                            cmd.append("DATA UPDATE BIODATA PIN=" + pin + "\tNo=0\tIndex="
                                + bioTemplateItem.getTemplateNoIndex()
                                + "\tValid=1\tDuress=0\tType=2\tMajorVer=9\tMinorVer=0\tFormat=0\tTmp="
                                + bioTemplateItem.getTemplate());
                        }
                    }
                }

            }

            return cmd.toString();
        }
        return "";
    }

    @Override
    @Transactional
    public void handlerTransfer(List<AttPersonItem> attPersonItems) {
        if (!CollectionUtil.isEmpty(attPersonItems)) {
            List<List<AttPersonItem>> splitAttPersonItemList =
                CollectionUtil.split(attPersonItems, CollectionUtil.splitSize);
            List<AttPerson> attPersons = attPersonDao.findAll();
            Map<String, AttPerson> attPersonMap = CollectionUtil.listToKeyMap(attPersons, AttPerson::getPersonPin);
            for (List<AttPersonItem> attPersonItemList : splitAttPersonItemList) {
                Collection<String> personPinList = CollectionUtil.getPropertyList(attPersonItemList,
                    AttPersonItem::getPersonPin, AttConstant.COMM_DEF_VALUE);
                List<PersPersonItem> personItemList = persPersonService.getItemsByPins(personPinList);
                Map<String, PersPersonItem> persPersonItemMap =
                    CollectionUtil.listToKeyMap(personItemList, PersPersonItem::getPin);
                List<AttPerson> attPersonList = new ArrayList<>();
                for (AttPersonItem attPersonItem : attPersonItemList) {
                    String personPin = attPersonItem.getPersonPin();
                    // AttPerson attPerson = attPersonDao.findByPersonPin(personPin);
                    AttPerson attPerson = attPersonMap.get(personPin);
                    if (Objects.isNull(attPerson)) {
                        attPerson = new AttPerson();
                    }
                    PersPersonItem persPersonItem = persPersonItemMap.get(personPin);
                    if (Objects.nonNull(persPersonItem)) {
                        attPerson.setPersonId(persPersonItem.getId());
                        attPerson.setPersonPin(persPersonItem.getPin());
                    }

                    attPerson.setIsAttendance(attPersonItem.getIsAttendance());
                    attPerson.setPerDevAuth(attPersonItem.getPerDevAuth());

                    // 分组编号去掉之后 改用分组名称查找分组对象 modified by jinxian.huang 2019-07-25
                    String groupName = attPersonItem.getGroupName();
                    if (StringUtils.isNotBlank(groupName)) {
                        AttGroup attGroup = attGroupDao.findByGroupName(groupName);
                        if (Objects.nonNull(attGroup)) {
                            String groupId = attGroup.getId();
                            attPerson.setGroupId(groupId);

                        } else {
                            attPerson.setGroupId(AttCommonSchConstant.GroupConstant.DEFAULT_NO_GROUP_ID);
                        }
                    } else {
                        attPerson.setGroupId(AttCommonSchConstant.GroupConstant.DEFAULT_NO_GROUP_ID);
                    }
                    //  校验方式默认自动
                    if (attPerson.getVerifyMode() == null) {
                        attPerson.setVerifyMode((short)0);
                    }
                    attPersonList.add(attPerson);
                }

                attPersonDao.saveAll(attPersonList);
            }
        }
    }

    @Override
    public List<String> getAllChildDeptById(String deptIds) {
        List<String> deptIdList = new ArrayList<String>();
        List<String> deptIdAry = Arrays.asList(deptIds.split(","));
        deptIdAry.forEach(id -> {
            List<AuthDepartmentItem> deptItemList = authDepartmentService.getItemAndChildById(id);
            Collection<String> idsList = CollectionUtil.getPropertyList(deptItemList, AuthDepartmentItem::getId, "-1");
            deptIdList.addAll(idsList);
        });
        return deptIdList.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public String getPinByLoginType(String sessionId) {
        SecuritySubject securitySubject = authSessionServcie.getSecuritySubject(sessionId);
        if (securitySubject != null && "PERS".equals(securitySubject.getLoginType())) {
            return securitySubject.getUserCode();
        }
        return null;
    }

    @Override
    public Pager getPersonAreaItemsByPage(String sessionId, AttPersonAreaItem condition, int page, int size) {
        PersPersonItem personItem = new PersPersonItem();
        ModelUtil.copyPropertiesIgnoreNull(condition, personItem);
        personItem.setPin(condition.getPersonPin());
        buildPersPersonCondition(sessionId, personItem);
        Pager pager = persPersonService.getItemsByPage(personItem, page, size);
        List<PersPersonItem> persPersonItemList = (List<PersPersonItem>)pager.getData();

        List<AttPersonAreaItem> attPersonAreaItemList = new ArrayList<AttPersonAreaItem>();
        if (!CollectionUtil.isEmpty(persPersonItemList)) {
            persPersonItemList.forEach(item -> {
                AttPersonAreaItem attPersonAreaItem = ModelUtil.copyProperties(item, new AttPersonAreaItem());
                attPersonAreaItem.setId(item.getId());
                attPersonAreaItem.setPersonId(item.getId());
                attPersonAreaItem.setPersonPin(item.getPin());
                attPersonAreaItem.setPersonName(item.getName());
                attPersonAreaItem.setPersonLastName(item.getLastName());
                attPersonAreaItemList.add(attPersonAreaItem);
            });
        }
        pager.setData(attPersonAreaItemList);

        return pager;
    }

    /**
     * 封装条件
     * 
     * @param sessionId
     * @param condition
     */
    private void buildPersPersonCondition(String sessionId, PersPersonItem condition) {
        // 封装部门条件
        if (StringUtils.isBlank(condition.getInDeptId())) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, condition.getDeptId(),
                condition.getDeptCode(), condition.getDeptName()));
            condition.setDeptId(null);
        } else if (StringUtils.isNotBlank(sessionId)) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByAuthAndIds(sessionId, condition.getInDeptId()));
            condition.setDeptId(null);
        }
    }

    @Override
    public List<String> filterPinList(String personIds, String deptIds, Date startDate, boolean attendanceLeaver) {
        List<String> filterPinList;
        if (StringUtils.isBlank(personIds) && StringUtils.isBlank(deptIds)) {
            // 人员和部门条件为空,则查询所有在职人员和离职人员
            List<PersPersonItem> personItems = persPersonService.getByCondition(new PersPersonItem());
            Set<String> pinSet = CollectionUtil.listToPropertySet(personItems, PersPersonItem::getPin);
            if (attendanceLeaver) {
                PersLeavePersonItem leavePersonCondition = new PersLeavePersonItem();
                // 过滤离职日期大于计算开始时间的人员
                leavePersonCondition.setBeginDate(startDate);
                List<PersLeavePersonItem> leavePersonItems =
                    persLeavePersonService.getByCondition(leavePersonCondition);
                Set<String> leavePinSet =
                    CollectionUtil.listToPropertySet(leavePersonItems, PersLeavePersonItem::getPin);
                pinSet = new HashSet<>(CollectionUtils.union(pinSet, leavePinSet));
            }
            filterPinList = new ArrayList<>(pinSet);
        } else if (StringUtils.isBlank(personIds) && StringUtils.isNotBlank(deptIds)) {
            // 只选部门,则查询部门底下人员 和 部门离职人员
            PersPersonItem personCondition = new PersPersonItem();
            personCondition.setInDeptId(deptIds);
            List<PersPersonItem> personItems = persPersonService.getByCondition(personCondition);
            Set<String> pinSet = CollectionUtil.listToPropertySet(personItems, PersPersonItem::getPin);
            if (attendanceLeaver) {
                PersLeavePersonItem leavePersonCondition = new PersLeavePersonItem();
                // 过滤离职日期大于计算开始时间的人员
                leavePersonCondition.setBeginDate(startDate);
                leavePersonCondition.setInDeptId(deptIds);
                List<PersLeavePersonItem> leavePersonItems =
                    persLeavePersonService.getByCondition(leavePersonCondition);
                Set<String> leavePinSet =
                    CollectionUtil.listToPropertySet(leavePersonItems, PersLeavePersonItem::getPin);
                pinSet = new HashSet<>(CollectionUtils.union(pinSet, leavePinSet));
            }
            filterPinList = new ArrayList<>(pinSet);
        } else {
            // 有选择人员,则只算选择的人员集合
            Map<String, String> pinsByPersonIds =
                persPersonService.getPinsByPersonIds(CollectionUtil.strToList(personIds));
            filterPinList = new ArrayList<>(pinsByPersonIds.values());
        }

        return filterPinList;
    }

    /**
     * 查询每个人所在区域的所有设备(扣除当前设备)
     *
     * @param personItems
     * @param attDevice
     * @return
     */
    private Map<String, Set<AttDevice>> getPersonDevices(List<PersPersonItem> personItems, AttDevice attDevice) {
        if (CollectionUtil.isEmpty(personItems)) {
            return new HashMap<>();
        }

        // 通过人员找到区域 建立人员跟区域关系
        AttAreaPersonItem attAreaPersonItem = new AttAreaPersonItem();
        attAreaPersonItem.setInPersPersonId(CollectionUtil.getItemIds(personItems));
        List<AttAreaPersonItem> attAreaPersonItemList = attAreaPersonService.getByCondition(attAreaPersonItem);
        if (CollectionUtil.isEmpty(attAreaPersonItemList)) {
            return new HashMap<>();
        }
        Set<String> areaIdSet = new HashSet<>();
        Map<String, Set<String>> personIdAreaIdsMap = new HashMap<>();
        for (AttAreaPersonItem item : attAreaPersonItemList) {
            String personId = item.getPersonId();
            String areaId = item.getAreaId();
            Set<String> set = personIdAreaIdsMap.get(personId);
            if (null == set) {
                set = new HashSet<>();
            }
            set.add(areaId);
            personIdAreaIdsMap.put(personId, set);
            areaIdSet.add(areaId);
        }

        if (CollectionUtil.isEmpty(areaIdSet)) {
            return new HashMap<>();
        }
        // 通过区域再找设备 建立区域设备关系
        List<AttDevice> deviceList = attDeviceDao.findByAreaIdIn(areaIdSet);
        if (CollectionUtil.isEmpty(deviceList)) {
            return new HashMap<>();
        }
        Map<String, Set<AttDevice>> areaIdDevicesMap = new HashMap<>();
        for (AttDevice device : deviceList) {
            String areaId = device.getAreaId();
            if (StringUtils.isBlank(areaId)) {
                continue;
            }
            Set<AttDevice> attDeviceSet = areaIdDevicesMap.get(areaId);
            if (null == attDeviceSet) {
                attDeviceSet = new HashSet<>();
            }
            attDeviceSet.add(device);
            areaIdDevicesMap.put(areaId, attDeviceSet);
        }

        // 通过人员找到区域 区域再找设备 然后根据人员匹配设备
        Map<String, Set<AttDevice>> personPinDevicesMap = new HashMap<>();
        Set<AttDevice> personDeviceSet = null;
        for (PersPersonItem persPersonItem : personItems) {

            Set<String> areaSet = personIdAreaIdsMap.get(persPersonItem.getId());
            if (CollectionUtil.isEmpty(areaSet)) {
                continue;
            }
            // 获取人员归属的每个区域拥有的设备集合累加起来就是人员所对应的所有设备
            personDeviceSet = new HashSet<>();
            for (String areaId : areaSet) {
                Set<AttDevice> attDevices = areaIdDevicesMap.get(areaId);
                if (CollectionUtil.isEmpty(attDevices)) {
                    continue;
                }
                personDeviceSet.addAll(attDevices);
            }
            if (CollectionUtil.isEmpty(personDeviceSet)) {
                continue;
            }
            // 当前设备不需要再下发
            if (personDeviceSet.contains(attDevice)) {
                personDeviceSet.remove(attDevice);
            }

            personPinDevicesMap.put(persPersonItem.getPin(), personDeviceSet);
        }
        return personPinDevicesMap;
    }

    @Override
    public String getPinsByLikeName(String sessionId, String likeName) {
        String pins = null;

        if (StringUtils.isBlank(likeName)) {
            return pins;
        }

        // 查询正常人员
        PersPersonItem persCondition = new PersPersonItem();
        persCondition.setLikeName(likeName);
        List<String> pings = persPersonService.getPersonPinByDeptPersAuthUserFilter(sessionId, persCondition);
        // 查询离职人员
        PersLeavePersonItem leavePersonCondition = new PersLeavePersonItem();
        leavePersonCondition.setLikeName(likeName);
        List<String> leavePins =
                persLeavePersonService.getPersonPinByDeptPersAuthUserFilter(sessionId, leavePersonCondition);
        // 合并在职离职人员pin号
        List<String> pinList = Stream.of(pings, leavePins).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        if (!CollectionUtil.isEmpty(pinList)) {
            pins = StringUtils.join(pinList, ",");
        } else {
            pins = "-1";
        }

        return pins;
    }

    @Override
    public Map<String, AttPersPersonInfoBean> getPersonInfoByPinList(Collection<String> pinList) {
        // 查询人员
        List<PersPersonCacheItem> personCacheItemList = persPersonCacheService.getPersonCacheByPins((List<String>)pinList);
        Map<String, PersPersonCacheItem> personCacheItemMap =
                CollectionUtil.listToKeyMap(personCacheItemList, PersPersonCacheItem::getPin);

        // 查询离职人员（实际上人员缓存已经存在离职人员信息了，这边保险起见也去查一次）
        List<PersLeavePersonItem> persLeavePersonItemList = persLeavePersonService.getItemByPins(pinList);
        Map<String, PersLeavePersonItem> leavePersonItemMap =
                CollectionUtil.listToKeyMap(persLeavePersonItemList, PersLeavePersonItem::getPin);
        Map<String, AttPersPersonInfoBean> persPersonInfoMap = new HashMap<>();
        for (String personPin : pinList) {
            AttPersPersonInfoBean persPersonInfoBean = new AttPersPersonInfoBean();
            PersPersonCacheItem personCacheItem = personCacheItemMap.get(personPin);
            if (Objects.isNull(personCacheItem)) {
                PersLeavePersonItem persLeavePersonItem = leavePersonItemMap.get(personPin);
                if (Objects.nonNull(persLeavePersonItem)) {
                    persPersonInfoBean.setPersonName(persLeavePersonItem.getName());
                    persPersonInfoBean.setPersonLastName(persLeavePersonItem.getLastName());
                    persPersonInfoBean.setDeptCode(persLeavePersonItem.getDeptCode());
                    persPersonInfoBean.setDeptName(persLeavePersonItem.getDeptName());
                    persPersonInfoBean.setRemark(personPin + ":" + I18nUtil.i18nCode("att_excp_leavePerson"));
                } else {
                    persPersonInfoBean.setRemark(personPin + ":" + I18nUtil.i18nCode("att_excp_notExisetPerson"));
                }
            } else {
                persPersonInfoBean.setPersonName(personCacheItem.getName());
                persPersonInfoBean.setPersonLastName(personCacheItem.getLastName());
                persPersonInfoBean.setDeptCode(personCacheItem.getDeptCode());
                persPersonInfoBean.setDeptName(personCacheItem.getDeptName());
            }
            persPersonInfoMap.put(personPin, persPersonInfoBean);
        }
        return persPersonInfoMap;
    }



    @Override
    public void getConditionBySessionId(String sessionId, AttConditionBean attConditionBean) {

        // 名字模糊搜索
        if (StringUtils.isNotBlank(attConditionBean.getLikeName())) {
            PersPersonItem persCondition = new PersPersonItem();
            persCondition.setLikeName(attConditionBean.getLikeName());
            List<String> pings = persPersonService.getPersonPinByDeptPersAuthUserFilter(sessionId, persCondition);
            // 查询离职人员
            PersLeavePersonItem leavePersonCondition = new PersLeavePersonItem();
            leavePersonCondition.setLikeName(attConditionBean.getLikeName());
            List<String> leavePins =
                persLeavePersonService.getPersonPinByDeptPersAuthUserFilter(sessionId, leavePersonCondition);
            // 合并在职离职人员pin号
            List<String> pins =
                Stream.of(pings, leavePins).flatMap(Collection::stream).distinct().collect(Collectors.toList());

            if (!CollectionUtil.isEmpty(pins)) {
                attConditionBean.setInPersonPin(StringUtils.join(pins, ","));
            } else {
                attConditionBean.setInPersonPin("-1");
            }
        }

        // 分装部门条件
        if (StringUtils.isBlank(attConditionBean.getDeptId())) {
            attConditionBean.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, null,
                attConditionBean.getDeptCode(), attConditionBean.getDeptName()));
            attConditionBean.setDeptName(null);
        } else {
            // 是否包含子部门
            if (ZKConstant.TRUE.equals(attConditionBean.getIsIncludeLower())) {
                attConditionBean.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId,
                    attConditionBean.getDeptId(), attConditionBean.getDeptCode(), attConditionBean.getDeptName()));
                attConditionBean.setDeptId(null);
                attConditionBean.setDeptName(null);
            }
        }
    }

    @Override
    public void updatePersonCacheExtParams(AttPersonItem attPersonItem) {
        if (attPersonItem != null) {
            Map<String, Map<String, Object>> personCacheExtParamsMap = new HashMap<>();
            personCacheExtParamsMap.put(attPersonItem.getPersonPin(), buildPersonCacheExtParams(attPersonItem));
            persPersonCacheService.updatePersonCacheExtParamsMap(ConstUtil.SYSTEM_MODULE_ATT, personCacheExtParamsMap);
        }
    }

    @Override
    public void updatePersonCacheExtParams(List<AttPersonItem> attPersonItemList) {
        if (attPersonItemList != null && attPersonItemList.size() > 0) {
            Map<String, Map<String, Object>> personCacheExtParamsMap = new HashMap<>();
            for (AttPersonItem attPersonItem : attPersonItemList) {
                personCacheExtParamsMap.put(attPersonItem.getPersonPin(), buildPersonCacheExtParams(attPersonItem));
            }
            persPersonCacheService.updatePersonCacheExtParamsMap(ConstUtil.SYSTEM_MODULE_ATT, personCacheExtParamsMap);
        }
    }

    @Override
    public Map<String, Object> buildPersonCacheExtParams(AttPersonItem attPersonItem) {
        Map<String, Object> extParams = new JSONObject();
        Boolean isAttendance = attPersonItem.getIsAttendance() == null ? true : attPersonItem.getIsAttendance();
        extParams.put("isAttendance", isAttendance);
        String groupId = StringUtils.isNotBlank(attPersonItem.getGroupId()) ? attPersonItem.getGroupId() : AttCommonSchConstant.GroupConstant.DEFAULT_NO_GROUP_ID;
        extParams.put("groupId", groupId);
        return extParams;
    }

    @Override
    public List<AttPersonItem> getByPinsFromPersonCache(List<String> pins) {
        List<AttPersonItem> attPersonItemList = new ArrayList<>();
        List<PersPersonCacheItem> personCacheItemList = persPersonCacheService.getPersonCacheByPins(pins);
        if (personCacheItemList != null) {
            for (PersPersonCacheItem persPersonCacheItem : personCacheItemList) {
                attPersonItemList.add(buildAttPersonItem(persPersonCacheItem));
            }

        }
        return attPersonItemList;
    }

    @Override
    public AttPersonItem getByPinFromPersonCache(String pin) {
        PersPersonCacheItem persPersonCacheItem = persPersonCacheService.getPersonCacheByPin(pin);
        if (persPersonCacheItem != null) {
            return buildAttPersonItem(persPersonCacheItem);
        }
        return null;
    }

    /**
     * 将人员缓存信息转成考勤人员信息
     *
     * @param cacheItem:
     * @return com.zkteco.zkbiosecurity.att.vo.AttPersonItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/16 14:30
     * @since 1.0.0
     */
    private AttPersonItem buildAttPersonItem(PersPersonCacheItem cacheItem) {
        AttPersonItem attPersonItem = new AttPersonItem();
        attPersonItem.setPersonId(cacheItem.getId()).setPersonPin(cacheItem.getPin()).setPersonName(cacheItem.getName())
            .setPersonLastName(cacheItem.getLastName()).setHireDate(cacheItem.getHireDate())
            .setDeptId(cacheItem.getDeptId()).setDeptCode(cacheItem.getDeptCode()).setDeptName(cacheItem.getDeptName());
        attPersonItem.setLeaveDate(cacheItem.getLeaveDate());
        Map<String, Map<String, Object>> extParams = cacheItem.getExtParams();
        if (extParams != null && extParams.containsKey(ConstUtil.SYSTEM_MODULE_ATT)) {
            Map<String, Object> attExtParams = extParams.get(ConstUtil.SYSTEM_MODULE_ATT);
            Object groupIdObj = attExtParams.get("groupId");
            String groupId = groupIdObj == null ? AttCommonSchConstant.GroupConstant.DEFAULT_NO_GROUP_ID: groupIdObj + "";
            attPersonItem.setGroupId(groupId);
            Object isAttendanceObj = attExtParams.get("isAttendance");
            Boolean isAttendance = isAttendanceObj == null ? true : Boolean.parseBoolean(isAttendanceObj + "");
            // 是否考勤（true:正常考勤/false:免打卡）,默认要考勤
            // 如果人员是导入的，然后没有添加考勤区域，然后只加了分组人员，是否考勤标识是空
            attPersonItem.setIsAttendance(isAttendance);
        }

        return attPersonItem;
    }

    @Override
    public Map<String, AttPersonItem> getPersonIdAndAttPersonItemMap(Collection<String> personIds) {

        if (CollectionUtil.isEmpty(personIds)) {
            return new HashMap<>();
        }

        List<AttPerson> attPersonList = attPersonDao.findAttPersonByPersonIdIn(personIds);
        if (CollectionUtil.isEmpty(attPersonList)) {
            return new HashMap<>();
        }

        List<AttPersonItem> attPersonItemList = ModelUtil.copyListProperties(attPersonList, AttPersonItem.class);

        return CollectionUtil.listToKeyMap(attPersonItemList, AttPersonItem::getPersonId);
    }

    @Override
    public Pager verifyModeList(String sessionId, AttPersonVerifyModeItem condition, int pageNo, int pageSize) {

        // 部门权限过滤
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }

        return attPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
    }

    @Override
    public void saveVerifyMode(AttPersonVerifyModeItem attPersonVerifyModeItem) {
        if (StringUtils.isBlank(attPersonVerifyModeItem.getAttPersonIds())) {
            return;
        }
        List<AttPersonItem> attPersonItemList = attPersonService.getItemByIds(attPersonVerifyModeItem.getAttPersonIds());
        // 更新考勤人员验证方式
        Collection<String> persPersonIdList = CollectionUtil.getPropertyList(attPersonItemList, AttPersonItem::getPersonId, "-1");
        attPersonDao.updateVerifyModeByPersonId(attPersonVerifyModeItem.getVerifyMode(), persPersonIdList);

        // 人员基础信息
        List<PersPersonItem> persPersonItemList = persPersonService.getSimpleItemsByIds(persPersonIdList);
        // 考勤人员信息
        Map<String, AttPersonItem> personIdAndAttPersonItemMap = attPersonService.getPersonIdAndAttPersonItemMap(persPersonIdList);
        // 根据人员Id查找所在设备
        Map<String, List<AttDeviceItem>> pinAndDeviceListMap = attDeviceService.getPinAndDeviceListMap(persPersonIdList);
        // 获取人员对应的主卡号
        List<PersCardItem> masterCardPersonIdList = persCardService.getMasterCardByPersonIdList(persPersonIdList);
        Map<String, PersCardItem> personIdAndCardMap =
                CollectionUtil.listToKeyMap(masterCardPersonIdList, PersCardItem::getPersonId);

        // 异步下发
        CompletableFuture.runAsync(() -> {
            for (PersPersonItem item : persPersonItemList) {
                List<AttDeviceItem> attDevices = pinAndDeviceListMap.get(item.getPin());
                if (CollectionUtil.isEmpty(attDevices)) {
                    continue;
                }
                PersPersonItem personItem = new PersPersonItem();
                personItem.setPin(item.getPin());
                personItem.setName(item.getName());
                personItem.setLastName(item.getLastName());
                personItem.setPersonPwd(item.getPersonPwd());
                PersCardItem persCardItem = personIdAndCardMap.get(item.getId());
                AttPersonItem attPersonItem = personIdAndAttPersonItemMap.get(item.getId());
                AttPersonOptItem attPersonOptItem = attDevCmdManager.buildPersonOptItem(personItem, persCardItem, attPersonItem);
                attDevices.forEach(dev -> {
                   attDevCmdManager.sendPersonToDev(dev.getDevSn(), attPersonOptItem);
                });
            }
        });
    }
}