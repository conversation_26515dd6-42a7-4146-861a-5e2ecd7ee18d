package com.zkteco.zkbiosecurity.att.license;

import com.zkteco.zkbiosecurity.att.service.AttDeviceService;
import com.zkteco.zkbiosecurity.att.service.AttPointService;
import com.zkteco.zkbiosecurity.core.guard.annotation.InmutableClassSign;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.license.vo.IModuleAuthDefault;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 许可-登录检测
 *
 * <AUTHOR>
 * @version 0.0.1
 * @since 2018年07月25日 上午10:49:47
 */

@Component
@InmutableClassSign(module = ConstUtil.SYSTEM_MODULE_ATT)
public class AttModuleAuthCheck implements IModuleAuthDefault {

    @Autowired
    private AttDeviceService attDeviceService;
    @Autowired
    private AttPointService attPointService;

    @Override
    public boolean enable() {
        return true;
    }

    @Override
    public String module() {
        return ConstUtil.SYSTEM_MODULE_ATT;
    }

    @Override
    public int controlCount() {
        return attDeviceService.getDeviceCount() + attPointService.getPointCount();
    }
}
