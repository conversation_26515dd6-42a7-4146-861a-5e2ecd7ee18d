package com.zkteco.zkbiosecurity.att.data.upgrade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.service.AttOvertimeService;
import com.zkteco.zkbiosecurity.att.service.AttShiftService;
import com.zkteco.zkbiosecurity.att.service.AttTimeSlotService;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryService;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryValueService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;
import com.zkteco.zkbiosecurity.system.vo.BaseDictionaryItem;
import com.zkteco.zkbiosecurity.system.vo.BaseDictionaryValueItem;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 升级到2.2.0
 *
 */
@Slf4j
@Component
public class AttVer2_2_0 implements UpgradeVersionManager {

    @Autowired
    private AttShiftService attShiftService;
    @Autowired
    AttTimeSlotService attTimeSlotService;
    @Autowired
    AttOvertimeService attOvertimeService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private BaseDictionaryService baseDictionaryService;
    @Autowired
    private BaseDictionaryValueService baseDictionaryValueService;

    @Override
    public String getModule() {
        return BaseConstants.ATT;
    }

    @Override
    public String getVersion() {
        return "v2.2.0";
    }

    @Override
    public boolean executeUpgrade() {

        /**
         * BEFORE_TO_WORK_MINUTES (Integer) 上班前n分钟内签到有效
         * AFTER_TO_WORK_MINUTES (Integer) 上班后n分钟内签到有效
         * BEFORE_OFF_WORK_MINUTES (Integer) 下班前n分钟内签到有效
         * AFTER_OFF_WORK_MINUTES (Integer) 下班后n分钟内签到有效
         * BEFORE_WORK_OVERTIME_MINUTES (Integer) 上班n分钟前签到记加班
         * MIN_BEFORE_OVERTIME_MINUTES (Integer) 上班前最短加班分钟数
         * AFTER_WORK_OVERTIME_MINUTES (Integer) 下班n分钟后开始记加班
         * MIN_AFTER_OVERTIME_MINUTES (Integer) 下班后最短加班分钟数
         * ENABLE_FLEXIBLE_WORK (String 10) 是否启动弹性上班(true:启用。false：不启用)--默认为false
         * */
        attTimeSlotService.upgradeTo_220();
        //ATT_OVERTIME(新增OVERTIME_LONG--加班时长)
        attOvertimeService.upgradeTo_220();
        //ATT_SHIFT：增加“工作类型”字段，必填字段，字符串类型，默认为正常上班；
        attShiftService.upgradeTo_220();

        // 参数升级
        baseSysParamService.initData(new BaseSysParamItem("att.baseRule.maxOvertimeType", "notLimited",
                "Maximum overtime type (this week/this month)"));// "加班时长限制类型：不限制/本周/本月"
        baseSysParamService.initData(new BaseSysParamItem("att.baseRule.maxOvertimeMinutes", "0",
                I18nUtil.i18nCode("att_rule_baseRuleMaxOvertimeMinutes")));// "本周/本月最大加班时长（分钟）"

        // 字典升级
        // 班次工作类型(正常工作/周末加班/节假日加班)
        BaseDictionaryItem baseDictionaryItem =
                new BaseDictionaryItem("AttShiftWorkType", "att_shift_workType", false, "att_module");
        baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
        baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("normalWork", "att_shift_normalWork", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("weekendOt", "att_overtime_rest", 1, baseDictionaryItem.getId()));
        baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("holidayOt", "att_shift_holidayOt", 2, baseDictionaryItem.getId()));

        return true;
    }

}
