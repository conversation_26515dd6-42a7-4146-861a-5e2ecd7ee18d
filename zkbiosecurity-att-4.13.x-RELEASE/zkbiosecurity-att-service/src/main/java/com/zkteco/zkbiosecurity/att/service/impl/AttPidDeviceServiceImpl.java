package com.zkteco.zkbiosecurity.att.service.impl;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.service.AttGetPidDeviceSelectService;
import com.zkteco.zkbiosecurity.att.service.AttGetPidDeviceService;
import com.zkteco.zkbiosecurity.att.service.AttPidDeviceService;
import com.zkteco.zkbiosecurity.att.service.AttPointService;
import com.zkteco.zkbiosecurity.att.vo.Att4PidDeviceSelect;
import com.zkteco.zkbiosecurity.att.vo.AttPidDeviceSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * 人证当考勤、获取人证设备信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:04
 * @since 1.0.0
 */
@Service
public class AttPidDeviceServiceImpl implements AttPidDeviceService {

    @Autowired(required = false)
    private AttGetPidDeviceSelectService attGetPidDeviceSelectService;
    @Autowired(required = false)
    private AttGetPidDeviceService attGetPidDeviceService;

    @Autowired
    private AttPointService attPointService;

    @Override
    public AttPidDeviceSelectItem getPidDeviceByDeviceId(String deviceId) {
        if (Objects.nonNull(attGetPidDeviceService)) {
            Att4PidDeviceSelect att4PidDeviceSelect = attGetPidDeviceService.getPidDeviceById(deviceId);
            if (Objects.nonNull(att4PidDeviceSelect)) {
                AttPidDeviceSelectItem attPidDeviceSelectItem = new AttPidDeviceSelectItem();
                ModelUtil.copyPropertiesIgnoreNull(att4PidDeviceSelect, attPidDeviceSelectItem);
                return attPidDeviceSelectItem;
            }
        }
        return null;
    }

    @Override
    public List<AttPidDeviceSelectItem> getPidDeviceByDeviceIds(Collection<String> deviceIds) {
        List<AttPidDeviceSelectItem> attPidDeviceSelectItemList = new ArrayList<>();
        if (Objects.nonNull(attGetPidDeviceService)) {
            List<Att4PidDeviceSelect> pidDeviceItemList = attGetPidDeviceService.getPidDeviceItemsByIds(deviceIds);
            if (!CollectionUtil.isEmpty(pidDeviceItemList)) {
                attPidDeviceSelectItemList =
                    ModelUtil.copyListProperties(pidDeviceItemList, AttPidDeviceSelectItem.class);
            }
        }
        return attPidDeviceSelectItemList;
    }

    @Override
    public Pager getSelectPidDevicePager(AttPidDeviceSelectItem conndtion, int page, int size) {
        if (Objects.nonNull(attGetPidDeviceSelectService)) {
            Att4PidDeviceSelect att4PidDeviceSelect = new Att4PidDeviceSelect();
            ModelUtil.copyPropertiesIgnoreNull(conndtion, att4PidDeviceSelect);
            List<String> deviceModules =
                attPointService.getDeviceIdsByDeviceModule(ConstUtil.SYSTEM_MODULE_IDENTIFICATION);
            String deviceModuleStr = StringUtils.join(deviceModules, AttConstant.COMMA);
            if (StringUtils.isNotBlank(deviceModuleStr)) {
                att4PidDeviceSelect.setSelectId(conndtion.getSelectId() + AttConstant.COMMA + deviceModuleStr);
            } else {
                att4PidDeviceSelect.setSelectId(conndtion.getSelectId());
            }
            Pager pager = attGetPidDeviceSelectService.getPidDeviceSelectList(att4PidDeviceSelect, page, size);
            List<Att4PidDeviceSelect> pidDeviceSelectItemList = (List<Att4PidDeviceSelect>)pager.getData();
            List<AttPidDeviceSelectItem> attPidDeviceSelectItemList =
                ModelUtil.copyListProperties(pidDeviceSelectItemList, AttPidDeviceSelectItem.class);
            pager.setData(attPidDeviceSelectItemList);
            return pager;
        } else {
            Pager pager = new Pager(page, size);
            pager.setData(new ArrayList<>());
            return pager;
        }
    }
}