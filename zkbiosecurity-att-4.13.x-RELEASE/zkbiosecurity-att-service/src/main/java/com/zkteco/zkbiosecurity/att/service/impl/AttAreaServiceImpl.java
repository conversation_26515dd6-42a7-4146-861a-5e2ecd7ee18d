package com.zkteco.zkbiosecurity.att.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.dao.AttAreaPersonDao;
import com.zkteco.zkbiosecurity.att.dao.AttDeviceDao;
import com.zkteco.zkbiosecurity.auth.provider.AuthAreaProvider;

/**
 * 判断区域是否在考勤模块使用
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/3/12 9:26
 * @since 1.0.0
 * 
 * @deprecated 已弃用 使用 {@link AttAreaExtServiceImpl} 代替
 */
@Deprecated
@Service("AttAreaService")
public class AttAreaServiceImpl implements AuthAreaProvider {

    @Autowired
    private AttDeviceDao attDeviceDao;
    @Autowired
    private AttAreaPersonDao attAreaPersonDao;

    @Override
    public String checkAreaIsUsed(String areaId) {
        Long count = attDeviceDao.countByAreaId(areaId);
        if (count > 0) {
            return "base_area_existDev";
        }
        count = attAreaPersonDao.countByAreaId(areaId);
        if (count > 0) {
            return "att_area_existPerson";
        }
        return "";
    }
}
