package com.zkteco.zkbiosecurity.att.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * 休息时间段
 */
@Entity
@Table(name = "ATT_BREAK_TIME")
@Getter
@Setter
@Accessors(chain = true)
public class AttBreakTime extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    private String name;

    /**
     * 开始时间 HH:mm
     */
    @Column(name = "START_TIME")
    private String startTime;

    /**
     * 结束时间 HH:mm
     */
    @Column(name = "END_TIME")
    private String endTime;

    /**
     * 时间段
     */
    @ManyToMany(targetEntity = AttTimeSlot.class, fetch = FetchType.LAZY)
    @JoinTable(name = "ATT_TIMESLOT_BREAKTIME",
        joinColumns = @JoinColumn(name = "BREAKTIME_ID", referencedColumnName = "ID"),
        inverseJoinColumns = @JoinColumn(name = "TIMESLOT_ID", referencedColumnName = "ID"))
    private Set<AttTimeSlot> attTimeSlotSet = new HashSet<AttTimeSlot>();
}
