package com.zkteco.zkbiosecurity.att.dao;

import java.util.List;

import com.zkteco.zkbiosecurity.att.model.AttSignAddressArea;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 *
 * <AUTHOR>
 * @date 2019/04/16
 */
public interface AttSignAddressAreaDao extends BaseDao<AttSignAddressArea, String> {

    /**
     * 按考勤签到地址Id删除
     * 
     * @param signAddressId
     */
    void deleteBySignAddressId(String signAddressId);

    /**
     * 
     * <AUTHOR>
     * @since 2019年8月8日 下午6:02:54
     * @param areaId
     * @return
     */
    AttSignAddressArea findByAreaId(String areaId);
}
