package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.att.cmd.AttDevCmdManager;
import com.zkteco.zkbiosecurity.att.dao.AttDeviceDao;
import com.zkteco.zkbiosecurity.att.service.Att4PersCardService;
import com.zkteco.zkbiosecurity.att.service.AttAreaPersonService;
import com.zkteco.zkbiosecurity.att.vo.AttAreaPersonItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersCardService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;

/**
 * 挂失、斛挂卡
 * 
 * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
 * @since 2019年6月13日 下午5:42:59
 */
@Service
public class Att4PersCardServiceImpl implements Att4PersCardService {
    @Autowired
    private PersCardService persCardService;
    @Autowired
    private AttAreaPersonService attAreaPersonService;
    @Autowired
    private AttDeviceDao attDeviceDao;
    @Autowired
    private AttDevCmdManager attDevCmdManager;
    @Autowired
    private PersPersonService persPersonService;

    @Override
    @Transactional
    public Boolean lossCard(String cardNos) {
        Map<String, String> personMap = persCardService.getPersonIdsByCardNosAndCardState(
            (List<String>)CollectionUtil.strToList(cardNos), PersConstants.CARD_LOSS);
        List<String> personIdList = new ArrayList<>(personMap.values());
        if (personIdList != null && personIdList.size() > 0) {
            updatePersonCar(personIdList);
        }
        return true;
    }

    @Override
    @Transactional
    public Boolean revertCard(String cardNos) {
        Map<String, String> personMap =
            persCardService.getPersonIdsByCardNos((List<String>)CollectionUtil.strToList(cardNos));
        List<String> personIdList = new ArrayList<>(personMap.values());
        if (personIdList != null && personIdList.size() > 0) {
            updatePersonCar(personIdList);
        }
        return true;
    }

    /**
     * 挂失、解挂卡时，同步人员基础信息到设备
     * 
     * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
     * @since 2019年6月14日 上午10:00:24
     * @param personIdList
     */
    private void updatePersonCar(List<String> personIdList) {
        List<AttAreaPersonItem> attAreaPersonItemList = attAreaPersonService.getAreaPersonByPersonIds(personIdList);
        if (attAreaPersonItemList != null && attAreaPersonItemList.size() > 0) {
            // 过滤出已存在考勤人员区域的人员ID
            for (AttAreaPersonItem item : attAreaPersonItemList) {
                attAreaPersonService.syncAttPersonToDevice(item.getPersonId(), item.getAreaId(), "-1");
            }
        }
    }
}
