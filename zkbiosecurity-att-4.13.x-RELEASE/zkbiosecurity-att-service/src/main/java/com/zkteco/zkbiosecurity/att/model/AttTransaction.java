package com.zkteco.zkbiosecurity.att.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.convert.EncryptConverter;
import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 考勤原始记录、打卡记录
 */
@Entity
@Table(name = "ATT_TRANSACTION",
    indexes = {@Index(name = "ATT_TRAN_DEPT_ID_IDX", columnList = "AUTH_DEPT_ID"),
        @Index(name = "ATT_TRAN_ATT_DATE_IDX", columnList = "ATT_DATE"),
        @Index(name = "ATT_TRAN_DEVICE_ID_IDX", columnList = "DEVICE_SN"),
        @Index(name = "ATT_TRAN_PIN_DATETIME_IDX", columnList = "PERS_PERSON_PIN,ATT_DATETIME")})
@Setter
@Getter
@Accessors(chain = true)
public class AttTransaction extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门id
     */
    @Column(name = "AUTH_DEPT_ID", length = 50)
    private String deptId;

    /**
     * 部门编号
     */
    @Column(name = "AUTH_DEPT_CODE", length = 100)
    private String deptCode;

    /**
     * 部门名称
     */
    @Column(name = "AUTH_DEPT_NAME", length = 100)
    private String deptName;

    /**
     * 人员编号
     */
    @Column(name = "PERS_PERSON_PIN", length = 50)
    private String personPin;

    /**
     * 姓名
     */
    @Column(name = "PERS_PERSON_NAME", length = 50)
    private String personName;

    /**
     * 英文（lastName）
     */
    @Column(name = "PERS_PERSON_LAST_NAME", length = 50)
    private String personLastName;

    /**
     * 区域Id
     */
    @Column(name = "AUTH_AREA_ID", length = 50)
    private String areaId;

    /**
     * 区域编号
     */
    @Column(name = "AUTH_AREA_NO", length = 50)
    private String areaNo;

    /**
     * 区域名称
     */
    @Column(name = "AUTH_AREA_NAME", length = 100)
    private String areaName;

    /**
     * 设备ID (停车场的通道ID/门禁的门ID/视频的通道ID)
     */
    @Column(name = "DEVICE_ID", length = 50)
    private String deviceId;

    /**
     * 设备序列号
     */
    @Column(name = "DEVICE_SN", length = 50)
    private String deviceSn;

    /**
     * 设备名称
     */
    @Column(name = "DEVICE_NAME", length = 50)
    private String deviceName;
    /**
     * 门编号
     */
    @Column(name = "DOOR_NO")
    private Short doorNo;

    /**
     * 考勤日期时间
     */
    @Column(name = "ATT_DATETIME")
    private Date attDatetime;

    /**
     * 考勤日期
     */
    @Column(name = "ATT_DATE", length = 30)
    private String attDate;

    /**
     * 考勤时间
     */
    @Column(name = "ATT_TIME", length = 30)
    private String attTime;

    /**
     * 考勤状态
     */
    @Column(name = "ATT_STATE", length = 20)
    private String attState;

    /**
     * 验证方式
     */
    @Column(name = "ATT_VERIFY", length = 10)
    private String attVerify;

    /**
     * 标识（0：考勤设备，1：门禁设备，2：停车设备）
     */
    @Column(name = "MARK", length = 10)
    private String mark;

    // 新增签到地点属性 add by bob.liu 20190611
    @Column(name = "ATT_PLACE", length = 250)
    @Convert(converter = EncryptConverter.class)
    private String attPlace;

    /**
     * 照片文件路径 add by ljf 2020/10/23 用来保存设备及第三方当考勤点推送过来的照片路径
     */
    @Column(name = "ATT_PHOTO_URL", length = 255)
    private String attPhotoUrl;

    /** 是否带口罩 0：表示没有佩戴口罩；1：表示有佩戴口罩 */
    @Column(name = "MASK_FLAG", length = 10)
    private String maskFlag;

    /** 体温 摄氏度℃ */
    @Column(name = "TEMPERATURE", length = 10)
    private String temperature;

    /** 经度 */
    @Column(name = "LONGITUDE", length = 20)
    private String longitude;

    /** 纬度 */
    @Column(name = "LATITUDE", length = 20)
    private String latitude;

    //推送微信 1=推送 2=不需要

    @com.zkteco.zkbiosecurity.base.annotation.Column(name = "push_wechat_flag")
    private String pushWechatFlag;

    public AttTransaction() {
        super();
    }

    public AttTransaction(String personPin, String attDate, String attTime, String deptId) {
        this.personPin = personPin;
        this.attDate = attDate;
        this.attTime = attTime;
        this.deptId = deptId;
    }
}