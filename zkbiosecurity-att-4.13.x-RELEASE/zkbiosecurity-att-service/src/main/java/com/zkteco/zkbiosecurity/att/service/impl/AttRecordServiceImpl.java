package com.zkteco.zkbiosecurity.att.service.impl;

import java.math.BigDecimal;
import java.util.*;

import com.zkteco.zkbiosecurity.att.vo.AttDayDetailReportItem;
import com.zkteco.zkbiosecurity.redis.redisson.annotation.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.api.vo.AttApiDayDetailReportItem;
import com.zkteco.zkbiosecurity.att.cache.AttCacheManager;
import com.zkteco.zkbiosecurity.att.constants.AppConstant;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttRecordDao;
import com.zkteco.zkbiosecurity.att.model.AttRecord;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 考勤计算结果、日明细表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:57
 * @since 1.0.0
 */
@Slf4j
@Service
@Transactional
public class AttRecordServiceImpl implements AttRecordService {

    @Autowired
    private AttRecordDao attRecordDao;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttTransactionService attTransactionService;
    @Autowired
    private AttRuleService attRuleService;
    @Autowired
    private AttCacheManager attCacheManager;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;
    @Autowired
    private AttSignService attSignService;

    @Override
    public AttRecordItem saveItem(AttRecordItem item) {
        AttRecord attRecord = Optional.ofNullable(item).map(AttRecordItem::getId).filter(StringUtils::isNotBlank)
            .flatMap(attRecordDao::findById).orElse(new AttRecord());
        ModelUtil.copyPropertiesIgnoreNull(item, attRecord);
        attRecordDao.save(attRecord);
        item.setId(attRecord.getId());
        return item;
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<AttRecordItem> getByCondition(AttRecordItem condition) {
        return (List<AttRecordItem>)attRecordDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return attRecordDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size, long limit) {
        StringBuffer sb = new StringBuffer(SQLUtil.getSqlByItem(condition));
        // 自定义排序
        String sql = buildOrderBySQL(sb);
        return attRecordDao.getItemsBySql(condition.getClass(), sql, page, size, limit);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                attRecordDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AttRecordItem getItemById(String id) {
        List<AttRecordItem> items = getByCondition(new AttRecordItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public List<?> getItemData(Class<?> cls, BaseItem condition, int beginIndex, int endIndex) {
        StringBuffer sb = new StringBuffer(SQLUtil.getSqlByItem(condition));
        // 自定义排序
        String sql = buildOrderBySQL(sb);
        return attRecordDao.getItemsDataBySql(cls, sql, beginIndex, endIndex, true);
    }

    @Override
    public List<AttRecordItem> getByPinsAndAttDate(Collection<String> personPins, Date startDatetime,
        Date endDatetime) {
        List<AttRecord> attRecordList =
            attRecordDao.findAttRecordByPersonPinInAndAttDateGreaterThanEqualAndAttDateLessThanEqual(personPins,
                startDatetime, endDatetime);
        List<AttRecordItem> attRecordItemList = ModelUtil.copyListProperties(attRecordList, AttRecordItem.class);
        return attRecordItemList;
    }

    @Override
    public List<AttRecordItem> getByDeptsAndAttDate(Collection<String> deptIds, Date startDatetime, Date endDatetime) {
        List<AttRecord> attRecordList = attRecordDao
            .findByDeptIdInAndAttDateGreaterThanEqualAndAttDateLessThanEqual(deptIds, startDatetime, endDatetime);
        List<AttRecordItem> attRecordItemList = ModelUtil.copyListProperties(attRecordList, AttRecordItem.class);
        return attRecordItemList;
    }

    @Override
    public Long getAllRecordCount() {
        return attRecordDao.count();
    }

    @Override
    public List<AttRecordItem> getUploadCloudRecord(int pageNo, int pageSize) {
        // Sort sort = new Sort(Sort.Direction.DESC, "createTime"); // 创建时间降序排序
        // Pageable pageable = new PageRequest(pageNo, pageSize, sort);

        // 创建时间降序排序
        Sort sort = Sort.by(Sort.Order.desc("createTime"));
        Pageable pageable = PageRequest.of(pageNo, pageSize, sort);

        Page<AttRecord> attRecordPage = attRecordDao.findAll(pageable);
        List<AttRecord> attRecordList = attRecordPage.getContent();
        List<AttRecordItem> attRecordItemList = ModelUtil.copyListProperties(attRecordList, AttRecordItem.class);
        return attRecordItemList;
    }

    /**
     * 组装默认动态表头数据
     */
    private void buildLeaveMinuteMap(List<AttDayDetailReportItem> itemList) {
        List<AttLeaveTypeItem> attLeaveTypeItems = attLeaveTypeService.getByCondition(new AttLeaveTypeItem());
        itemList.forEach(item -> {
            item.setAttDayDetailReportLeaveMinuteMap(string2map(attLeaveTypeItems, item.getLeaveDetails()));
        });
    }

    /**
     * 字符串转map集合 异常类型对应时长，格式:code-时长(分钟)，多个以逗号隔开
     * 
     * @param string
     * @return
     */
    private Map<String, Object> string2map(List<AttLeaveTypeItem> attLeaveTypeItems, String string) {
        Map<String, Object> map = new HashMap<>();
        if (StringUtils.isNotBlank(string)) {
            String[] strArr = string.split(",");
            for (String splitStr : strArr) {
                String[] splitArr = splitStr.split("-");
                map.put(splitArr[0], splitArr[1]);
            }
        }
        for (AttLeaveTypeItem attLeaveTypeItem : attLeaveTypeItems) {
            // 其他类型数据没有则填充0显示
            if (!map.containsKey(attLeaveTypeItem.getLeaveTypeNo())) {
                map.put(attLeaveTypeItem.getLeaveTypeNo(), "0");
            }
        }
        return map;
    }

    @Override
    public void handlerTransfer(List<AttRecordItem> attRecordItems) {
        List<List<AttRecordItem>> splitAttRecordItemList =
            CollectionUtil.split(attRecordItems, CollectionUtil.splitSize);
        // 获取人员集合 add by jinxian.huang 2019-08-08
        List<PersPersonItem> persPersonItemList = persPersonService.getByCondition(new PersPersonItem());
        Map<String, PersPersonItem> persPersonItemMap =
            CollectionUtil.listToKeyMap(persPersonItemList, PersPersonItem::getPin);
        for (List<AttRecordItem> attRecordItemList : splitAttRecordItemList) {
            /* Collection<String> personPinList = CollectionUtil.getPropertyList(attRecordItemList, AttRecordItem::getPersonPin, AttConstant.COMM_DEF_VALUE);
            List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(personPinList);
            Map<String, PersPersonItem> persPersonItemMap = CollectionUtil.listToKeyMap(persPersonItemList, PersPersonItem::getPin);*/
            List<AttRecord> attRecordList = new ArrayList<>();

            for (AttRecordItem attRecordItem : attRecordItemList) {
                AttRecord attRecord = new AttRecord();
                // 删除已有，保存新增的
                // attRecordDao.deleteByPinAndDate(Arrays.asList(attRecordItem.getPersonPin()),attRecordItem.getAttDate(),attRecordItem.getAttDate());
                ModelUtil.copyPropertiesIgnoreNullWithProperties(attRecordItem, attRecord, "id");
                PersPersonItem personItem = persPersonItemMap.get(attRecordItem.getPersonPin());
                if (Objects.nonNull(personItem)) {
                    attRecord.setDeptId(personItem.getDeptId());
                    attRecord.setDeptCode(personItem.getDeptCode());
                    attRecord.setDeptName(personItem.getDeptName());
                    attRecord.setPersonName(personItem.getName());
                    attRecord.setPersonLastName(personItem.getLastName());
                } else {
                    attRecord.setDeptId(null);
                }
                attRecordList.add(attRecord);
            }
            attRecordDao.saveAll(attRecordList);
        }
    }

    @Override
    public void deleteDataTransfer() {
        attRecordDao.deleteAll();
    }

    @SuppressWarnings("unchecked")
    @Override
    public AttApiDayDetailReportItem getPersonDetailReport(String pin, Date dayStartDate, Date dayEndDate) {
        AttDayDetailReportItem attDayDetailReportItem = new AttDayDetailReportItem();
        attDayDetailReportItem.setPersonPin(pin);
        attDayDetailReportItem.setStartDatetimeBegin(dayStartDate);
        attDayDetailReportItem.setStartDatetimeEnd(dayEndDate);
        attDayDetailReportItem.setEquals(true);
        List<AttDayDetailReportItem> attDayDetailReportItems = (List<AttDayDetailReportItem>)attRecordDao
            .getItemsBySql(attDayDetailReportItem.getClass(), SQLUtil.getSqlByItem(attDayDetailReportItem));
        if (!CollectionUtil.isEmpty(attDayDetailReportItems)) {
            buildLeaveMinuteMap(attDayDetailReportItems);
            // 查询一天的只有一条记录
            attDayDetailReportItem = attDayDetailReportItems.get(0);
            if (attDayDetailReportItem != null) {
                AttApiDayDetailReportItem attApiDayDetailReportItem =
                    ModelUtil.copyPropertiesIgnoreNull(attDayDetailReportItem, new AttApiDayDetailReportItem());
                String date = AttDateUtils.dateToStrAsShort(attDayDetailReportItem.getAttDate());
                // 设置考勤日期
                attApiDayDetailReportItem.setAttDate(date);
                // 设置当天打卡记录
                attApiDayDetailReportItem.setCardRecord(getDayCardRecord(pin, date));

                // 设置应出勤小时
                attApiDayDetailReportItem.setShouldHour(
                    attParamService.minutesToHourFormat(new BigDecimal(attApiDayDetailReportItem.getShouldMinute())));
                // 设置实际出勤小时
                attApiDayDetailReportItem.setActualHour(
                    attParamService.minutesToHourFormat(new BigDecimal(attApiDayDetailReportItem.getValidMinute())));
                // 设置旷工小时
                attApiDayDetailReportItem.setAbsentHour(
                    attParamService.minutesToHourFormat(new BigDecimal(attApiDayDetailReportItem.getAbsentMinute())));
                // // 设置缺卡次数
                // attApiDayDetailReportItem.setLackCardCount(AttDateUtils.getLackCardCount(
                // Optional.ofNullable(attApiDayDetailReportItem.getCardValidData()).orElse(""), attRule));

                // 考勤规则最小单位配置
                Map<String, AttLeaveTypeItem> attLeaveTypeParamsMap = attLeaveTypeService.getLeaveTypeParamsMap();
                // 考勤节假日最小单位配置
                List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getLeaveTypeItems();
                Map<String, AttLeaveTypeItem> attLeaveTypeItemMap =
                    CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getLeaveTypeNo);
                // 精确小数点位数
                int decimal = Integer.parseInt(attParamService.getDecimal());
                AttLeaveTypeItem actual = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.ACTUAL);
                AttLeaveTypeItem late = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.LATE);
                AttLeaveTypeItem early = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.EARLY);
                AttLeaveTypeItem overtime = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.OVERTIME);
                AttLeaveTypeItem absent = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.ABSENT);
                AttLeaveTypeItem noCheckIn =
                    attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.NO_CHECK_IN);
                AttLeaveTypeItem noCheckOut =
                    attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.NO_CHECK_OUT);

                AttLeaveTypeItem trip = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.TRIP);
                AttLeaveTypeItem out = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.OUT);

                Integer shouldMinute = attDayDetailReportItem.getShouldMinute();
                Integer shouldMinuteEx = Optional.ofNullable(attDayDetailReportItem.getShouldMinuteEx()).orElse(0);

                // 设置缺卡次数
                attApiDayDetailReportItem.setLackCardCount(
                    getLackCardCount(Optional.ofNullable(attApiDayDetailReportItem.getCardValidData()).orElse(""),
                        noCheckIn.getSymbol(), noCheckOut.getSymbol(), attDayDetailReportItem.getCardStatus()));

                attApiDayDetailReportItem
                    .setShould(AttCommonUtils.convertMinute(attDayDetailReportItem.getShouldMinute(), shouldMinute,
                        actual, decimal) + AttCommonUtils.getConvertUnit(actual));
                attApiDayDetailReportItem
                    .setActual(AttCommonUtils.convertMinute(attDayDetailReportItem.getActualMinute(), shouldMinute,
                        actual, decimal) + AttCommonUtils.getConvertUnit(actual));
                attApiDayDetailReportItem.setValid(
                    AttCommonUtils.convertMinute(attDayDetailReportItem.getValidMinute(), shouldMinute, actual, decimal)
                        + AttCommonUtils.getConvertUnit(actual));

                attApiDayDetailReportItem
                    .setLate(AttCommonUtils.convertMinute(attDayDetailReportItem.getLateMinuteTotal(), shouldMinute,
                        late, decimal) + AttCommonUtils.getConvertUnit(late));
                attApiDayDetailReportItem
                    .setEarly(AttCommonUtils.convertMinute(attDayDetailReportItem.getEarlyMinuteTotal(), shouldMinute,
                        early, decimal) + AttCommonUtils.getConvertUnit(early));
                attApiDayDetailReportItem
                    .setAbsent(AttCommonUtils.convertMinute(attDayDetailReportItem.getAbsentMinute(), shouldMinute,
                        absent, decimal) + AttCommonUtils.getConvertUnit(absent));

                Integer should = shouldMinute == 0 ? shouldMinuteEx : shouldMinute;
                attApiDayDetailReportItem
                    .setOvertimeUsual(AttCommonUtils.convertMinute(attDayDetailReportItem.getOvertimeUsualMinute(),
                        should, overtime, decimal) + AttCommonUtils.getConvertUnit(overtime));
                attApiDayDetailReportItem
                    .setOvertimeRest(AttCommonUtils.convertMinute(attDayDetailReportItem.getOvertimeRestMinute(),
                        should, overtime, decimal) + AttCommonUtils.getConvertUnit(overtime));
                attApiDayDetailReportItem
                    .setOvertimeHoliday(AttCommonUtils.convertMinute(attDayDetailReportItem.getOvertimeHolidayMinute(),
                        should, overtime, decimal) + AttCommonUtils.getConvertUnit(overtime));
                attApiDayDetailReportItem.setOvertime(
                    AttCommonUtils.convertMinute(attDayDetailReportItem.getOvertimeMinute(), should, overtime, decimal)
                        + AttCommonUtils.getConvertUnit(overtime));

                // 请假不分类别 统一按天
                // 默认天数转化
                AttLeaveTypeItem defaultLeaveTypeDay = new AttLeaveTypeItem();
                defaultLeaveTypeDay.setConvertUnit(AttConstant.ATT_CONVERT_UNIT_DAY);
                defaultLeaveTypeDay.setConvertType(AttConstant.ATT_CONVERT_CARRY);
                defaultLeaveTypeDay.setConvertCount(0.1);
                attApiDayDetailReportItem.setLeave(AttCommonUtils.convertMinute(attDayDetailReportItem.getLeaveMinute(),
                    shouldMinute, defaultLeaveTypeDay, decimal) + AttCommonUtils.getConvertUnit(defaultLeaveTypeDay));

                attApiDayDetailReportItem.setTrip(
                    AttCommonUtils.convertMinute(attDayDetailReportItem.getTripMinute(), shouldMinute, trip, decimal)
                        + AttCommonUtils.getConvertUnit(trip));
                attApiDayDetailReportItem.setOut(
                    AttCommonUtils.convertMinute(attDayDetailReportItem.getOutMinute(), shouldMinute, out, decimal)
                        + AttCommonUtils.getConvertUnit(out));

                List<AttSignItem> attSignItemList = attSignService.getItemByPinAndDate(pin, dayStartDate, dayEndDate);
                attApiDayDetailReportItem.setSignCount(attSignItemList.size());

                return attApiDayDetailReportItem;
            }
        }
        return null;
    }

    /**
     * 漏卡判断
     * 
     * @param cardValue:
     * @param noSignIn:
     * @param noSignOff:
     * @param cardStatus:
     * @return int
     * <AUTHOR>
     * @date 2020-12-22 17:03
     * @since 1.0.0
     */
    private int getLackCardCount(String cardValue, String noSignIn, String noSignOff, String cardStatus) {
        int lackCount = 0;
        if (StringUtils.isNotBlank(cardValue) && StringUtils.isNotBlank(cardStatus)) {
            String[] cardStatusArr = cardStatus.split(";");
            String[] cardTime = cardValue.split(";");
            if (cardStatusArr.length > 0) {
                for (int i = 0, len = cardStatusArr.length; i < len; i++) {
                    String[] cardTimeStatus = cardStatusArr[i].split("-", 2);
                    String timeSlot = cardTime[i];
                    String startWork = timeSlot.split("-", 2)[0];
                    String endWork = timeSlot.split("-", 2)[1];
                    if (AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION.equals(cardTimeStatus[0])
                        && noSignIn.equals(startWork)) {
                        lackCount++;
                    }
                    if (AppConstant.ATT_CALENDAR_CARD_STATUS_EXCEPTION.equals(cardTimeStatus[1])
                        && noSignOff.equals(endWork)) {
                        lackCount++;
                    }
                }
            }
        }
        return lackCount;
    }

    /**
     * 获取人员当天打卡记录
     * 
     * @auther lambert.li
     * @date 2019/4/12 11:02
     * @param pin
     * @param date
     * @return
     */
    private String getDayCardRecord(String pin, String date) {
        String dayCardRecord = null;
        AttTransactionItem attTransactionItem = new AttTransactionItem();
        attTransactionItem.setPersonPin(pin);
        attTransactionItem.setAttDate(date);
        attTransactionItem.setEquals(true);
        List<AttTransactionItem> attTransactionItems = attTransactionService.getByCondition(attTransactionItem);
        if (!CollectionUtil.isEmpty(attTransactionItems)) {
            return CollectionUtil.getPropertys(attTransactionItems, AttTransactionItem::getAttTime);
        }
        return dayCardRecord;
    }

    @Override
    public void delete(List<String> pins, Date startDate, Date endDate) {
        List<List<String>> split = CollectionUtil.split(pins, CollectionUtil.splitSize);
        for (List<String> list : split) {
            attRecordDao.deleteByPinAndDate(list, startDate, endDate);
        }
    }

    @Override
    public void save(List<AttRecordItem> recordItemList) {
        List<AttRecord> attRecords = ModelUtil.copyListProperties(recordItemList, AttRecord.class);
        attRecordDao.saveAll(attRecords);
    }

    @Override
    public AttRecordItem getItemByPinAndDate(String personPin, String attDate) {
        AttRecordItem condition = new AttRecordItem();
        condition.setEquals(true);
        condition.setPersonPin(personPin);
        condition.setStartDate(AttDateUtils.getMinOfDay(AttDateUtils.stringToYmdDate(attDate)));
        condition.setEndDate(AttDateUtils.getMaxOfDay(AttDateUtils.stringToYmdDate(attDate)));
        List<AttRecordItem> attRecordItems = getByCondition(condition);
        if (!CollectionUtil.isEmpty(attRecordItems)) {
            return attRecordItems.get(0);
        }
        return null;
    }

    @Override
    @Transactional
    @RedisLock(lockName = "attDelAndSaveRecordRedisson", waitTime = 600, expire = 600)
    public void delAndSaveRecord(List<String> pinList, Date startDate, Date endDate,
        List<AttRecordItem> recordItemList) {
        log.info("delAndSaveRecord pinList = {}", pinList);
        // 删除旧记录
        delete(pinList, startDate, endDate);
        // 保存新记录
        save(recordItemList);
    }

    @Override
    public String buildOrderBySQL(StringBuffer sb) {
        String sql = sb.toString();
        String[] arraySql = sql.split("ORDER BY");
        if (arraySql.length > 1) {
            String orderSql = arraySql[1];
            if (!orderSql.contains("t.ATT_DATE")) {
                sql += ", t.ATT_DATE ASC";
            }
        }
        return sql;
    }
}