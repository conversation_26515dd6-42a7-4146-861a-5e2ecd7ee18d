//package com.zkteco.zkbiosecurity.att.license;
//
//import com.zkteco.zkbiosecurity.att.service.AttPointService;
//import com.zkteco.zkbiosecurity.core.guard.annotation.InmutableClassSign;
//import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
//import com.zkteco.zkbiosecurity.license.vo.IModuleAuthDefault;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
// * @version V1.0
// * @date Created In 15:41 2019/1/16
// */
//@Component
//@InmutableClassSign(module = ConstUtil.SYSTEM_MODULE_ATT)
//public class PidForAttModuleAuthCheck implements IModuleAuthDefault {
//
//    @Autowired
//    private AttPointService attPointService;
//
//    @Override
//    public int controlCount() {
//        return attPointService.getCountByDeviceModule(ConstUtil.SYSTEM_MODULE_IDENTIFICATION);
//    }
//
//    @Override
//    public boolean enable() {
//        return true;
//    }
//
//    @Override
//    public String module() {
//        return ConstUtil.LICENSE_MODULE_PIDATT;
//    }
//}
