package com.zkteco.zkbiosecurity.att.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

/**
 * 设备操作日志
 */
@Setter
@Getter
@Accessors(chain = true)
@Entity
@Table(name = "ATT_DEVICE_OP_LOG", indexes = {@Index(name = "ATT_DEVICE_OP_LOG_CRT_IDX", columnList = "CREATE_TIME"),
    @Index(name = "ATT_DEVICE_OP_LOG_UPT_IDX", columnList = "UPDATE_TIME")})
public class AttDeviceOpLog extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /** 设备SN */
    @Column(name = "DEV_SN", length = 50)
    private String devSn;

    /** 操作时间 */
    @Column(name = "OP_TIME")
    private Date opTime;

    /** 操作代码 */
    @Column(name = "OP_TYPE", length = 50)
    private String opType;

    /** 操作内容 */
    @Column(name = "OP_CONTENT", length = 100)
    private String opContent;

    /** 操作人员pin */
    @Column(name = "OPERATOR_PIN", length = 50)
    private String operator;

    /** 操作人员姓名 */
    @Column(name = "OPERATOR_NAME", length = 50)
    private String operatorName;

    /** 操作对象值 */
    @Column(name = "OP_WHO_VALUE", length = 50)
    private String opWhoValue;

    /** 操作对象内容 */
    @Column(name = "OP_WHO_CONTENT", length = 100)
    private String opWhoContent;

    /** 操作对象1 */
    @Column(name = "OP_VALUE1", length = 50)
    private String opValue1;

    /** 操作对象1描述 */
    @Column(name = "OP_VALUE_CONTENT1", length = 100)
    private String opValueContent1;

    /** 操作对象2 */
    @Column(name = "OP_VALUE2", length = 50)
    private String opValue2;

    /** 操作对象2描述 */
    @Column(name = "OP_VALUE_CONTENT2", length = 100)
    private String opValueContent2;

    /** 操作对象3 */
    @Column(name = "OP_VALUE3", length = 50)
    private String opValue3;

    /** 操作对象3描述 */
    @Column(name = "OP_VALUE_CONTENT3", length = 100)
    private String opValueContent3;

    /**
     * 区域Id
     */
    @Column(name = "AUTH_AREA_ID", length = 50)
    private String areaId;

    /** 表与表关系 **/
    public AttDeviceOpLog() {
        super();
    }

    public AttDeviceOpLog(String id) {
        super();
        this.id = id;
    }
}