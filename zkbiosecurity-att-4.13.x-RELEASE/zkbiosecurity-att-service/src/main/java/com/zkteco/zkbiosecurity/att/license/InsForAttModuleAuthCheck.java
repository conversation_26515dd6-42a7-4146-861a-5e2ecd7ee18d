//package com.zkteco.zkbiosecurity.att.license;
//
//import com.zkteco.zkbiosecurity.att.service.AttPointService;
//import com.zkteco.zkbiosecurity.core.guard.annotation.InmutableClassSign;
//import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
//import com.zkteco.zkbiosecurity.license.vo.IModuleAuthDefault;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
///**
// * 信息屏当考勤许可 - 登录检测
// *
// * <AUTHOR>
// * @version v1.0
// */
//@Component
//@InmutableClassSign(module = ConstUtil.SYSTEM_MODULE_INS)
//public class InsForAttModuleAuthCheck implements IModuleAuthDefault {
//
//    @Autowired
//    private AttPointService attPointService;
//
//    @Override
//    public int controlCount() {
//        return attPointService.getCountByDeviceModule(ConstUtil.SYSTEM_MODULE_INS);
//    }
//
//    @Override
//    public boolean enable() {
//        return true;
//    }
//
//    @Override
//    public String module() {
//        return ConstUtil.LICENSE_MODULE_INSATT;
//    }
//}
