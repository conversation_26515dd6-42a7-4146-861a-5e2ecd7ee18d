package com.zkteco.zkbiosecurity.att.data.upgrade;

import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * 升级到4.11.0
 *
 */
@Slf4j
@Component
public class AttVer4_11_0 implements UpgradeVersionManager {

    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public String getModule() {
        return BaseConstants.ATT;
    }

    @Override
    public String getVersion() {
        return "v4.11.0";
    }

    @Override
    public boolean executeUpgrade() {

        // 菜单升级
        upgradeMenu();

        return true;
    }

    // 菜单升级
    private void upgradeMenu() {
        // 一级菜单:考勤管理
        AuthPermissionItem attAttendanceDevice = authPermissionService.getItemByCode("AttAttendanceDevice");
        if (attAttendanceDevice != null) {
            /* ------------- 二级菜单:人员验证方式 -------------*/
            AuthPermissionItem childMenuItem = new AuthPermissionItem("AttPersonVerifyMode", "att_personVerifyMode", "att:personVerifyMode",
                    AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 0);
            childMenuItem.setParentId(attAttendanceDevice.getId());
            childMenuItem.setActionLink("attPerson.do?verifyModeIndex");
            childMenuItem = authPermissionService.saveItem(childMenuItem);
            // 刷新
            AuthPermissionItem buttonMenuItem = new AuthPermissionItem("AttPersonVerifyModeRefresh", "common_op_refresh", "att:personVerifyMode:refresh",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
            buttonMenuItem.setParentId(childMenuItem.getId());
            authPermissionService.saveItem(buttonMenuItem);
            // 验证方式设置
            buttonMenuItem = new AuthPermissionItem("AttPersonVerifyModeSetting", "att_personVerifyMode_setting",
                    "att:personVerifyMode:setting", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
            buttonMenuItem.setParentId(childMenuItem.getId());
            authPermissionService.saveItem(buttonMenuItem);
        }

        // 二级菜单:人员排班
        AuthPermissionItem attPersonSch = authPermissionService.getItemByCode("AttPersonSch");
        if (attPersonSch != null) {
            // 下载人员临时排班模版
            AuthPermissionItem buttonMenuItem = new AuthPermissionItem("AttPersonSchExportTemplate", "att_personSch_exportTemplate",
                    "att:personsch:exportTemplate", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
            buttonMenuItem.setParentId(attPersonSch.getId());
            authPermissionService.saveItem(buttonMenuItem);
            // 导入人员临时排班
            buttonMenuItem = new AuthPermissionItem("AttPersonSchImportCycSch", "att_personSch_importCycSch", "att:personsch:importCycSch",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 9);
            buttonMenuItem.setParentId(attPersonSch.getId());
            authPermissionService.saveItem(buttonMenuItem);
            // 下载人员临时排班模版
            buttonMenuItem = new AuthPermissionItem("AttPersonSchExportCycSchTemplate", "att_personSch_exportCycSchTemplate",
                    "att:personsch:exportCycSchTemplate", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 10);
            buttonMenuItem.setParentId(attPersonSch.getId());
            authPermissionService.saveItem(buttonMenuItem);
        }

        // 二级菜单:设备管理
        AuthPermissionItem attDevice = authPermissionService.getItemByCode("AttDevice");
        if (attDevice != null) {
            // 设置考勤状态
            AuthPermissionItem attSetShortcutKey = new AuthPermissionItem("AttSetShortcutKey", "att_cardStatus_setting",
                    "att:device:setShortcutKey", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 22);
            attSetShortcutKey.setParentId(attDevice.getId());
            authPermissionService.saveItem(attSetShortcutKey);
        }
        //初始化打卡状态参数，数据格式：快捷键IDkeyId,考勤状态statusCode,状态名称showName
        baseSysParamService.initData(new BaseSysParamItem("att.cardStatus.f1", "1,0," + I18nUtil.i18nCode("att_cardStatus_signIn" ), "签到"));
        baseSysParamService.initData(new BaseSysParamItem("att.cardStatus.f2", "2,1," + I18nUtil.i18nCode("att_cardStatus_signOut" ), "签退"));
        baseSysParamService.initData(new BaseSysParamItem("att.cardStatus.f3", "3,2," + I18nUtil.i18nCode("att_cardStatus_out" ), "外出"));
        baseSysParamService.initData(new BaseSysParamItem("att.cardStatus.f4", "4,3," + I18nUtil.i18nCode("att_cardStatus_outReturn" ), "外出返回"));
        baseSysParamService.initData(new BaseSysParamItem("att.cardStatus.f5", "5,4," + I18nUtil.i18nCode("att_cardStatus_overtime_signIn" ), "加班签到"));
        baseSysParamService.initData(new BaseSysParamItem("att.cardStatus.f6", "6,5," + I18nUtil.i18nCode("att_cardStatus_overtime_signOut"), "加班签退"));
    }
}
