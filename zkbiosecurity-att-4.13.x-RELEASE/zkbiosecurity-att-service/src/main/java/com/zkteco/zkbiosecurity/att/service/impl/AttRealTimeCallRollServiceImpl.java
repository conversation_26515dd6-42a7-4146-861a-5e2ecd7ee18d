package com.zkteco.zkbiosecurity.att.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.service.AttLeaveService;
import com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService;
import com.zkteco.zkbiosecurity.att.service.AttRealTimeCallRollService;
import com.zkteco.zkbiosecurity.att.service.AttTransactionService;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveItem;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.att.vo.AttRealTimeCallRollItem;
import com.zkteco.zkbiosecurity.att.vo.AttTransactionItem;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 实时点名
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:55
 * @since 1.0.0
 */
@Service
public class AttRealTimeCallRollServiceImpl implements AttRealTimeCallRollService {

    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AttTransactionService attTransactionService;
    @Autowired
    private AttLeaveService attLeaveService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;

    private static final String EXPORT = "export";
    private static final String LIST = "list";

    @Override
    public Map<String, JSONArray> getAttRealTimeCallRollList(AttRealTimeCallRollItem condition) {
        // 返回值map
        HashMap<String, JSONArray> returnMap = new HashMap<String, JSONArray>();
        // 已签到集合
        JSONArray persSignJsonList = new JSONArray();
        // 未签到集合
        JSONArray persNoSignJsonList = new JSONArray();
        if (condition.getDeptId() != null) {
            PersPersonItem persPersonItem = new PersPersonItem();
            persPersonItem.setInDeptId(condition.getDeptId());
            // 获取部门下所有考勤人员
            List<PersPersonItem> personAllList = persPersonService.getByCondition(persPersonItem);
            // 限制800人
            List<List<PersPersonItem>> persSplitList = CollectionUtil.split(personAllList, CollectionUtil.splitSize);
            for (List<PersPersonItem> persList : persSplitList) {
                // 部门下全部考勤人员集合
                Collection<String> persPins = CollectionUtil.getPropertyList(persList, PersPersonItem::getPin, "-1");
                // 部门下考勤人员的最早打卡记录
                List<AttTransactionItem> persSignList =
                    attTransactionService.getItemListByTimeAndPersPins(DateUtil.stringToDate(condition.getBeginDate()),
                        DateUtil.stringToDate(condition.getEndDate()), persPins);
                Map<String, AttTransactionItem> persSignListMap =
                    CollectionUtil.listToKeyMap(persSignList, AttTransactionItem::getPersonPin);
                // 部门下考勤人员的最早请假记录
                List<AttLeaveItem> persLeaveList =
                    attLeaveService.getItemListByTimeAndPersPins(DateUtil.stringToDate(condition.getBeginDate()),
                        DateUtil.stringToDate(condition.getEndDate()), persPins);
                Map<String, AttLeaveItem> persLeaveListMap =
                    CollectionUtil.listToKeyMap(persLeaveList, AttLeaveItem::getPersonPin);
                // 部门下考勤人员请假类型
                Collection<String> leaveTypeIdList =
                    CollectionUtil.getPropertyList(persLeaveList, AttLeaveItem::getLeaveTypeId, "-1");
                List<AttLeaveTypeItem> attLeaveTypeList = attLeaveTypeService.getLeaveTypeByIds(leaveTypeIdList);
                Map<String, AttLeaveTypeItem> persLeaveTypeListMap =
                    CollectionUtil.listToKeyMap(attLeaveTypeList, AttLeaveTypeItem::getId);
                for (PersPersonItem item : persList) {
                    JSONObject returnItem = new JSONObject();
                    returnItem.put("id", item.getPin());
                    JSONArray attRealTimeCallRollJson = new JSONArray();
                    // 标识
                    attRealTimeCallRollJson.add(item.getPin());
                    attRealTimeCallRollJson.add(item.getName());
                    attRealTimeCallRollJson.add(item.getDeptCode());
                    attRealTimeCallRollJson.add(item.getDeptName());

                    AttTransactionItem persSignItem = persSignListMap.get(item.getPin());
                    AttLeaveItem persLeaveItem = persLeaveListMap.get(item.getPin());

                    // 有请假
                    if (persLeaveListMap.containsKey(item.getPin())) {
                        // 有打卡
                        if (persSignListMap.containsKey(item.getPin())) {
                            Date signDate = persSignItem.getAttDatetime();
                            Date leaveStartDate = persLeaveItem.getStartDatetime();
                            if (signDate.getTime() > leaveStartDate.getTime()) {
                                attRealTimeCallRollJson.add("");
                                attRealTimeCallRollJson
                                    .add(persLeaveTypeListMap.get(persLeaveItem.getLeaveTypeId()).getLeaveTypeName());
                            } else {
                                attRealTimeCallRollJson
                                    .add(AttDateUtils.dateToStrAsLong(persSignItem.getAttDatetime()));
                                attRealTimeCallRollJson.add(I18nUtil.i18nCode("att_realTime_signPers"));
                            }
                        } else {
                            attRealTimeCallRollJson.add("");
                            attRealTimeCallRollJson
                                .add(persLeaveTypeListMap.get(persLeaveItem.getLeaveTypeId()).getLeaveTypeName());
                        }
                        returnItem.put("data", attRealTimeCallRollJson);
                        persSignJsonList.add(returnItem);
                        // 没有请假
                    } else {
                        if (persSignListMap.containsKey(item.getPin())) {
                            attRealTimeCallRollJson.add(AttDateUtils.dateToStrAsLong(persSignItem.getAttDatetime()));
                            attRealTimeCallRollJson.add(I18nUtil.i18nCode("att_realTime_signPers"));
                            returnItem.put("data", attRealTimeCallRollJson);
                            persSignJsonList.add(returnItem);
                        } else {
                            attRealTimeCallRollJson.add(" ");
                            attRealTimeCallRollJson.add(I18nUtil.i18nCode("att_rule_noSignIn"));
                            returnItem.put("data", attRealTimeCallRollJson);
                            persNoSignJsonList.add(returnItem);
                        }
                    }
                }
            }
        }
        returnMap.put("sign", persSignJsonList);
        returnMap.put("noSign", persNoSignJsonList);
        return returnMap;
    }

    @Override
    public Pager getItemByCondition(AttRealTimeCallRollItem condition, int pageNo, int pageSize) {
        Pager pager = new Pager();
        List<AttRealTimeCallRollItem> returnList = getItemListByStatus(condition, pageNo, pageSize, LIST);
        pager.setPage(pageNo);
        pager.setSize(pageSize);
        pager.setData(returnList);
        pager.setTotal(returnList.size());
        return pager;
    }

    @Override
    public List<AttRealTimeCallRollItem> getExportItemByCondition(AttRealTimeCallRollItem condition, int beginIndex,
        int endIndex) {
        List<AttRealTimeCallRollItem> returnList = getItemListByStatus(condition, beginIndex, endIndex, EXPORT);
        return returnList;
    }

    private List<AttRealTimeCallRollItem> getItemListByStatus(AttRealTimeCallRollItem condition, int beginNo, int endNo,
        String status) {
        List<AttRealTimeCallRollItem> returnList = new ArrayList<>();
        PersPersonItem persPersonItem = new PersPersonItem();
        if (condition.getDeptId() != null) {
            List<AuthDepartmentItem> deptItemList = authDepartmentService.getItemAndChildById(condition.getDeptId());
            Collection<String> idsList = CollectionUtil.getPropertyList(deptItemList, AuthDepartmentItem::getId, "-1");
            String deptIds = String.join(",", idsList);
            persPersonItem.setInDeptId(deptIds);
        }
        List<PersPersonItem> personAllList = persPersonService.getByCondition(persPersonItem);
        if (personAllList.size() > 0) {
            int startIndex = 0;
            int endIndex = 29999;
            if (EXPORT.equals(status)) {
                startIndex = beginNo * endNo;
                endIndex = startIndex + endNo;
            } else if (LIST.equals(status)) {
                startIndex = beginNo;
                endIndex = endNo;
            }
            // 分批处理
            List<List<PersPersonItem>> persSplitList = CollectionUtil.split(personAllList, CollectionUtil.splitSize);
            for (List<PersPersonItem> personItemList : persSplitList) {
                // 部门下全部考勤人员集合
                Collection<String> persPins =
                    CollectionUtil.getPropertyList(personItemList, PersPersonItem::getPin, "-1");
                // 部门下考勤人员的最早打卡记录
                List<AttTransactionItem> persSignList =
                    attTransactionService.getItemListByTimeAndPersPins(DateUtil.stringToDate(condition.getBeginDate()),
                        DateUtil.stringToDate(condition.getEndDate()), persPins);
                Map<String, AttTransactionItem> persSignListMap =
                    CollectionUtil.listToKeyMap(persSignList, AttTransactionItem::getPersonPin);
                // 部门下考勤人员的最早请假记录
                List<AttLeaveItem> persLeaveList =
                    attLeaveService.getItemListByTimeAndPersPins(DateUtil.stringToDate(condition.getBeginDate()),
                        DateUtil.stringToDate(condition.getEndDate()), persPins);
                Map<String, AttLeaveItem> persLeaveListMap =
                    CollectionUtil.listToKeyMap(persLeaveList, AttLeaveItem::getPersonPin);
                // 部门下考勤人员请假类型
                Collection<String> leaveTypeIdList =
                    CollectionUtil.getPropertyList(persLeaveList, AttLeaveItem::getLeaveTypeId, "-1");
                List<AttLeaveTypeItem> attLeaveTypeList = attLeaveTypeService.getLeaveTypeByIds(leaveTypeIdList);
                Map<String, AttLeaveTypeItem> persLeaveTypeListMap =
                    CollectionUtil.listToKeyMap(attLeaveTypeList, AttLeaveTypeItem::getId);
                for (int i = startIndex; i < endIndex && i < personItemList.size(); i++) {
                    AttRealTimeCallRollItem attRealTimeCallRollItem = buildStatusReturrn(personItemList.get(i),
                        persSignListMap, persLeaveListMap, persLeaveTypeListMap);
                    if (condition.getRealTimeStatus() != null
                        && attRealTimeCallRollItem.getRealTimeStatus().equals(condition.getRealTimeStatus())) {
                        returnList.add(attRealTimeCallRollItem);
                    } else if (condition.getRealTimeStatus() == null) {
                        returnList.add(attRealTimeCallRollItem);
                    }
                    if (returnList.size() == endIndex || returnList.size() == personItemList.size()) {
                        break;
                    }
                }
            }
        }
        return returnList;
    }

    private AttRealTimeCallRollItem buildStatusReturrn(PersPersonItem personItem,
        Map<String, AttTransactionItem> persSignListMap, Map<String, AttLeaveItem> persLeaveListMap,
        Map<String, AttLeaveTypeItem> persLeaveTypeListMap) {
        // 设置返回值
        AttRealTimeCallRollItem attRealTimeCallRollItem = new AttRealTimeCallRollItem();
        attRealTimeCallRollItem.setId(personItem.getId());
        attRealTimeCallRollItem.setDeptName(personItem.getDeptName());
        attRealTimeCallRollItem.setPersonPin(personItem.getPin());
        attRealTimeCallRollItem.setPersonName(personItem.getName());
        // 有签到
        if (persSignListMap.containsKey(personItem.getPin())) {
            AttTransactionItem attPersSignItem = persSignListMap.get(personItem.getPin());
            Date signDate = attPersSignItem.getAttDatetime();
            attRealTimeCallRollItem.setRealTimeStatus(AttConstant.ROLLCALL_SIGNIN);
            // 有请假
            if (persLeaveListMap.containsKey(personItem.getPin())) {
                AttLeaveItem attPersLeaveItem = persLeaveListMap.get(personItem.getPin());
                Date leaveStartDate = attPersLeaveItem.getStartDatetime();
                if (signDate.getTime() >= leaveStartDate.getTime()) {
                    attRealTimeCallRollItem.setExceptionName(
                        persLeaveTypeListMap.get(attPersLeaveItem.getLeaveTypeId()).getLeaveTypeName());
                    attRealTimeCallRollItem.setAttDatetime(leaveStartDate);
                    attRealTimeCallRollItem.setRealTimeStatus(AttConstant.ROLLCALL_LEAVE);
                } else {
                    attRealTimeCallRollItem.setExceptionName(I18nUtil.i18nCode("att_realTime_signPers"));
                    attRealTimeCallRollItem.setAttDatetime(signDate);
                }
                // 没请假
            } else {
                attRealTimeCallRollItem.setExceptionName(I18nUtil.i18nCode("att_realTime_signPers"));
                attRealTimeCallRollItem.setAttDatetime(signDate);
            }
        } else {
            if (persLeaveListMap.containsKey(personItem.getPin())) {
                AttLeaveItem attPersLeaveItem = persLeaveListMap.get(personItem.getPin());
                Date leaveStartDate = attPersLeaveItem.getStartDatetime();
                attRealTimeCallRollItem
                    .setExceptionName(persLeaveTypeListMap.get(attPersLeaveItem.getLeaveTypeId()).getLeaveTypeName());
                attRealTimeCallRollItem.setAttDatetime(leaveStartDate);
                attRealTimeCallRollItem.setRealTimeStatus(AttConstant.ROLLCALL_LEAVE);
            } else {
                attRealTimeCallRollItem.setExceptionName(I18nUtil.i18nCode("att_rule_noSignIn"));
                attRealTimeCallRollItem.setRealTimeStatus(AttConstant.ROLLCALL_NOSIGNIN);
            }
        }
        return attRealTimeCallRollItem;
    }
}
