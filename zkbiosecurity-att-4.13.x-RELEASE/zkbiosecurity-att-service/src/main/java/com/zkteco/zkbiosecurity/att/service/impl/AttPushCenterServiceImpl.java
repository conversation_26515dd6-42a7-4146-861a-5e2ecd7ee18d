package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.zkteco.zkbiosecurity.att.dao.AttTransactionDao;
import com.zkteco.zkbiosecurity.att.service.AttTransactionService;
import com.zkteco.zkbiosecurity.att.vo.AttTransactionItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.push.enums.PushCenter4OtherEnum;
import com.zkteco.zkbiosecurity.push.service.PushCenter4OtherService;
import com.zkteco.zkbiosecurity.push.service.PushCenterNotifyOtherService;
import com.zkteco.zkbiosecurity.push.vo.PushCenter4OtherItem;

/**
 * 推送中心，通知模块进行重新推送
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 16:26 2021/9/3
 * @version v1.0
 */
@Service
public class AttPushCenterServiceImpl implements PushCenterNotifyOtherService {

    @Autowired
    private AttTransactionService attTransactionService;
    @Autowired(required = false)
    private PushCenter4OtherService pushCenter4OtherService;
    @Autowired
    private AttTransactionDao attTransactionDao;

    @Override
    public boolean notifyPushData(Date startTime, Date endTime, PushCenter4OtherEnum pushCenter4OtherEnum) {
        // 是否由本模块处理
        boolean isHandle = false;
        if (Objects.nonNull(pushCenter4OtherService)) {
            if (PushCenter4OtherEnum.ATT_TRANSACTION.equals(pushCenter4OtherEnum)
                || PushCenter4OtherEnum.ATT_TRANSACTION_IMAGE.equals(pushCenter4OtherEnum)) {
                isHandle = true;
                PushCenter4OtherItem pushCenter4OtherItem = new PushCenter4OtherItem();
                AttTransactionItem condition = new AttTransactionItem();
                condition.setBeginDate(startTime);
                condition.setEndDate(endTime);
                condition.setMark(ConstUtil.SYSTEM_MODULE_ATT);
                // 分页处理
                int beginIndex = 0;
                while (true) {
                    int pageSize = CollectionUtil.splitSize;
                    int endIndex = beginIndex + (pageSize - 1);
                    List<AttTransactionItem> attTransactionItemList = attTransactionDao.getItemsDataBySql(
                        AttTransactionItem.class, SQLUtil.getSqlByItem(condition), beginIndex, endIndex, true);
                    if (attTransactionItemList != null && attTransactionItemList.size() > 0) {
                        List<String> attTransList = new ArrayList<>();
                        List<String> attPhotoUrlList = new ArrayList<>();
                        for (AttTransactionItem attTrans : attTransactionItemList) {
                            attTransList.add(JSON.toJSONString(attTrans));
                            attPhotoUrlList.add(attTrans.getAttPhotoUrl());
                        }
                        if (PushCenter4OtherEnum.ATT_TRANSACTION.equals(pushCenter4OtherEnum)) {
                            pushCenter4OtherItem.setPushCenter4OtherEnum(PushCenter4OtherEnum.ATT_TRANSACTION);
                            pushCenter4OtherItem.setContentList(attTransList);
                        } else {
                            pushCenter4OtherItem.setPushCenter4OtherEnum(PushCenter4OtherEnum.ATT_TRANSACTION_IMAGE);
                            pushCenter4OtherItem.setContentList(attPhotoUrlList);
                        }
                        pushCenter4OtherService.pushData(pushCenter4OtherItem);
                        if (attTransactionItemList.size() < pageSize) {
                            break;
                        }
                    } else {
                        break;
                    }
                    beginIndex = endIndex + 1;
                }
            }
        }
        return isHandle;
    }
}
