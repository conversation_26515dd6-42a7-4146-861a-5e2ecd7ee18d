package com.zkteco.zkbiosecurity.att.service.impl;

import java.io.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ScheduledFuture;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.dao.AttPersonDao;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.guard.foldex.utils.FoldexUtil;
import com.zkteco.zkbiosecurity.redis.redisson.annotation.RedisLock;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.zkteco.zkbiosecurity.att.constants.AttAutoExportConstant;
import com.zkteco.zkbiosecurity.att.dao.AttAutoExportDao;
import com.zkteco.zkbiosecurity.att.dao.AttTransactionDao;
import com.zkteco.zkbiosecurity.att.model.AttAutoExport;
import com.zkteco.zkbiosecurity.att.utils.*;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.scheduler.ScheduleService;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryValueService;
import com.zkteco.zkbiosecurity.system.service.BaseMailService;
import com.zkteco.zkbiosecurity.system.service.BaseSendMailService;

import lombok.extern.slf4j.Slf4j;

/**
 * 报表推送
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 16:33
 * @since 1.0.0
 */
@Service
@Slf4j
public class AttAutoExportServiceImpl implements AttAutoExportService {

    private ScheduledFuture<?> scedulefuture;
    private Map<String, ScheduledFuture<?>> scheduledFutureMap = new HashMap<>();

    @Autowired
    private AttAutoExportDao attAutoExportDao;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired(required = false)
    private BaseMailService baseMailService;
    @Autowired
    private ScheduleService scheduleService;
    @Autowired
    private AttTransactionService attTransactionService;
    @Autowired(required = false)
    private BaseSendMailService baseSendMailService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AttTransactionDao attTransactionDao;
    @Autowired
    private AttPointService attPointService;
    @Autowired
    private AttDayCardDetailService attDayCardDetailService;
    @Autowired
    private BaseDictionaryValueService baseDictionaryValueService;
    @Autowired
    private AttPersonDao attPersonDao;
    @Autowired
    private AttDayDetailReportService attDayDetailReportService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;
    @Autowired
    private AttMonthDetailReportService attMonthDetailReportService;
    @Autowired
    private AttMonthStatisticalReportService attMonthStatisticalReportService;

    @Override
    @Transactional
    public AttAutoExportItem saveItem(AttAutoExportItem item) {
        AttAutoExport attAutoExport = null;
        if (item.getId() != null) {
            // 编辑
            attAutoExport = attAutoExportDao.findById(item.getId()).orElse(new AttAutoExport());
        }
        if (attAutoExport == null) {
            // 新增
            attAutoExport = new AttAutoExport();
        }
        // 新增才默认启动,编辑不修改状态
        if (StringUtils.isBlank(item.getJobStatus())) {
            if (StringUtils.isNotBlank(attAutoExport.getJobStatus())) {
                item.setJobStatus(attAutoExport.getJobStatus());
            } else {
                item.setJobStatus(AttAutoExportConstant.JOBSTATUS_ENABLE);
            }
        }

        item.setFileTimeFormat("HHmmss");
        // 格式化时间发送频率格式(按日)
        if (AttAutoExportConstant.TIMESENDFREQUENCY_DAY.equals(item.getTimeSendFrequency())) {
            formatTimeCalcInterval(item);
        }
        // 发送方式(邮件发送方式和ftp发送方式 如果是邮箱方式就把ftp对应的字段值设为空 反之亦然)
        if (AttAutoExportConstant.SENDFORMAT_MAIL.equals(item.getSendFormat())) {
            item.setFtpUrl(null);
            item.setFtpPort(null);
            item.setFtpUsername(null);
            item.setFtpPassword(null);
            // 邮件发送类型 按人员 按部门 按区域
            if (AttAutoExportConstant.EMAILTYPE_DEPT.equals(item.getEmailType())) {
                AuthDepartmentItem authDepartmentItem = authDepartmentService.getItemById(item.getDeptId());
                item.setEmailRecipients(authDepartmentItem.getName());
            } else if (AttAutoExportConstant.EMAILTYPE_AREA.equals(item.getEmailType())) {
                AuthAreaItem authAreaItem = authAreaService.getItemById(item.getAreaId());
                item.setEmailRecipients(authAreaItem.getName());
            }
        } else if (AttAutoExportConstant.SENDFORMAT_FTP.equals(item.getSendFormat())
            || AttAutoExportConstant.SENDFORMAT_SFTP.equals(item.getSendFormat())) {// ftp发送方式
            item.setEmailType(null);
            item.setEmailRecipients(null);
            item.setDeptId(null);
            item.setAreaId(null);
            item.setEmailSubject(null);
            item.setEmailContent(null);

        }
        // 转换时间通配符,并创建定时任务
        item.setJobName(String.valueOf(System.currentTimeMillis()));
        item.setJobCron(formatCron(item.getTimeSendFrequency(), item.getTimeSendInterval()));
        ModelUtil.copyProperties(item, attAutoExport);
        attAutoExportDao.save(attAutoExport);
        item.setId(attAutoExport.getId());

        // 只有启用状态才添加到定时作业去执行
        if (AttAutoExportConstant.JOBSTATUS_ENABLE.equals(attAutoExport.getJobStatus())) {
            scedulefuture = scheduledFutureMap.get(item.getId());
            if (scedulefuture != null) {
                // 停止定时任务
                scedulefuture.cancel(true);
                // 从定时任务Map中移除该任务
                scheduledFutureMap.remove(item.getId());
            }
            scedulefuture = scheduleService.startScheduleTask(() -> autoExport(item.getId()),
                formatCron(item.getTimeSendFrequency(), item.getTimeSendInterval()));
            scheduledFutureMap.put(item.getId(), scedulefuture);
        }

        return item;
    }

    private void autoExport(String id) {
        log.info("Start automatically exporting reports......");
        AttAutoExport attAutoExport = attAutoExportDao.findById(id).get();
        SimpleDateFormat sdfDate = new SimpleDateFormat(attAutoExport.getFileDateFormat());
        SimpleDateFormat sdfTime = new SimpleDateFormat(attAutoExport.getFileTimeFormat());
        Date date = new Date();
        String fileType = attAutoExport.getFileType();
        // 根据文件类型，拼接文件后缀
        String suffix = ".txt";
        if (AttAutoExportConstant.FILETYPE_EXCEL.equals(fileType)) {
            suffix = ".xls";
        }
        // 获取文件名
        String fileName = attAutoExport.getFileName() + sdfDate.format(date) + sdfTime.format(date) + suffix;
        // 保存的文件路径
        String filePath = attFilePath() + AttDateUtils.dateToStrAsShort(new Date()) + FileUtils.separator;
        File file = new File(filePath);
        // 如果文件夹不存在则创建
        if (!file.exists() && !file.isDirectory()) {
            file.mkdirs();
        }
        // 文件全路径
        String fileAllPath = filePath + fileName;
        String reportType = attAutoExport.getReportType();
        String timeSendFrequency = attAutoExport.getTimeSendFrequency();
        String timeSendInterval = attAutoExport.getTimeSendInterval();
        // 第一步，根据报表设置和时间设置，抓取报表的内容，然后生成文件
        calcDateCreateFile(date, fileName, filePath, reportType, fileType, timeSendFrequency, attAutoExport,
            timeSendInterval);
        // 判断发送方式进行第二步操作
        if (AttAutoExportConstant.SENDFORMAT_MAIL.equals(attAutoExport.getSendFormat())) {
            // 第二步，根据邮件设置，抓取接收人的邮箱，然后批量发送邮件
            sendMail(attAutoExport, fileName, fileAllPath);
        } else if (AttAutoExportConstant.SENDFORMAT_FTP.equals(attAutoExport.getSendFormat())) {
            // 第二步，发送到ftp文件上
            sendToFtp(attAutoExport, fileName, fileAllPath);
        } else if (AttAutoExportConstant.SENDFORMAT_SFTP.equals(attAutoExport.getSendFormat())) {
            // 第二步，发送到sftp文件上
            sendToSFTP(attAutoExport, fileAllPath);
        }

    }

    @Override
    public List<AttAutoExportItem> getByCondition(AttAutoExportItem condition) {
        return (List<AttAutoExportItem>)attAutoExportDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return attAutoExportDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                attAutoExportDao.deleteById(id);
                scedulefuture = scheduledFutureMap.get(id);
                if (scedulefuture != null) {
                    // 停止定时任务
                    scedulefuture.cancel(true);
                    // 从定时任务Map中移除该任务
                    scheduledFutureMap.remove(id);
                }
            }
        }
        return false;
    }

    @Override
    public AttAutoExportItem getItemById(String id) {
        List<AttAutoExportItem> items = getByCondition(new AttAutoExportItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    // 格式化时间发送频率格式
    private void formatTimeCalcInterval(AttAutoExportItem item) {
        String[] timeCalcIntervalArray = item.getTimeSendInterval().split(";");
        String[] timeTempArray = null;
        StringBuffer timeCalcInterval = new StringBuffer();
        for (String timeTemp : timeCalcIntervalArray) {
            timeTempArray = timeTemp.split(":");
            if ("-1".equals(timeTempArray[0]) || "-1".equals(timeTempArray[1])) {
                continue;
            } else {
                timeCalcInterval.append(timeTemp + ";");
            }
        }
        item.setTimeSendInterval(timeCalcInterval.toString());
    }

    // 转换时间通配符
    private String formatCron(String timeSendFrequency, String timeSendInterval) {
        StringBuffer time = null;
        // 按日
        if (AttAutoExportConstant.TIMESENDFREQUENCY_DAY.equals(timeSendFrequency)) {
            // e.g.:每天14:30执行（0 30 14 * * ?）
            String[] timeSendIntervalArray = timeSendInterval.split(";");
            time = new StringBuffer("0 ");
            String[] timeTempArray = null;
            for (String timeTemp : timeSendIntervalArray) {
                timeTempArray = timeTemp.split(":");
                time.append(String.format("%d", Integer.parseInt(timeTempArray[1])) + " ");
                break;
            }

            List<String> hourList = new ArrayList<>();
            for (String timeTemp : timeSendIntervalArray) {
                timeTempArray = timeTemp.split(":");
                hourList.add(String.format("%d", Integer.parseInt(timeTempArray[0])));
            }
            time.append(StringUtils.join(hourList, ",")).append(" * * ?");
            return time.toString();
        } else if (AttAutoExportConstant.TIMESENDFREQUENCY_MONTH.equals(timeSendFrequency)) {
            time = new StringBuffer();
            if (AttAutoExportConstant.TIMEINTERVAL_LASTDAYOFMONTH.equals(timeSendInterval)) {
                // e.g.: 每月最后一天23:59:59点执行 （59 59 23 L * ?）
                // 因为CronTrigger不支持L，所以改成每个月的1号0点0分0秒
                // 配置《每月最后一天》，执行时间为每月的第一天的0点0分0秒，所有这边要往前推迟一秒，才是每月最后一天的59秒！！！
                time.append("0 0 0 1 * ?");
                return time.toString();
            } else if (AttAutoExportConstant.TIMEINTERVAL_FIRSTDAYOFMONTH.equals(timeSendInterval)) {
                // e.g. 每个月1号0点发送 （0 0 0 1 * ?）
                time.append("0 0 0 1 * ?");
                return time.toString();
            } else {
                time.append("0 0 0 ");
                time.append(timeSendInterval);
                time.append(" * ?");
                return time.toString();
            }
        } else {
            return "";
        }
    }

    @Override
    @RedisLock(lockName = "attAutoExportRedisson", waitTime = 0, expire = 600)
    @Transactional
    public void initAutoExport() {
        // 启用
        try {
            String jobStatus = AttAutoExportConstant.JOBSTATUS_ENABLE;
            List<AttAutoExport> attAutoExportList = attAutoExportDao.findByJobStatus(jobStatus);

            for (AttAutoExport attAutoExport : attAutoExportList) {
                scedulefuture = scheduleService.startScheduleTask(() -> autoExport(attAutoExport.getId()),
                    attAutoExport.getJobCron());
                scheduledFutureMap.put(attAutoExport.getId(), scedulefuture);
            }
        } catch (Exception e) {
            log.error("Att initAutoExport Exception", e);
        }
    }

    @Override
    @Transactional
    public void enable(String ids) {
        List<AttAutoExport> attAutoExportList = attAutoExportDao.findByIdIn(Arrays.asList(ids.split(",")));
        for (AttAutoExport attAutoExport : attAutoExportList) {
            // 启用
            attAutoExport.setJobStatus(AttAutoExportConstant.JOBSTATUS_ENABLE);
            scedulefuture = scheduledFutureMap.get(attAutoExport.getId());
            if (scedulefuture != null) {
                // 停止定时任务
                scedulefuture.cancel(true);
                // 从定时任务Map中移除该任务
                scheduledFutureMap.remove(attAutoExport.getId());
            }
            scedulefuture =
                scheduleService.startScheduleTask(() -> autoExport(attAutoExport.getId()), attAutoExport.getJobCron());
            scheduledFutureMap.put(attAutoExport.getId(), scedulefuture);
            attAutoExportDao.save(attAutoExport);
        }
    }

    @Override
    @Transactional
    public void disable(String ids) {
        List<AttAutoExport> attAutoExportList = attAutoExportDao.findByIdIn(Arrays.asList(ids.split(",")));
        for (AttAutoExport attAutoExport : attAutoExportList) {
            scedulefuture = scheduledFutureMap.get(attAutoExport.getId());
            if (scedulefuture != null) {
                // 停止定时任务
                scedulefuture.cancel(true);
                // 从定时任务Map中移除该任务
                scheduledFutureMap.remove(attAutoExport.getId());
            }
            // 禁用
            attAutoExport.setJobStatus(AttAutoExportConstant.JOBSTATUS_DISABLE);
            attAutoExportDao.save(attAutoExport);
        }
    }

    @Override
    public boolean completeMailInfo() {
        return baseMailService.completeMailInfo();
    }

    @Override
    public boolean ftpTest(String sendFormat, String ftpUrl, int ftpPort, String ftpUsername, String ftpPassword) {

        if (AttAutoExportConstant.SENDFORMAT_FTP.equals(sendFormat)) {
            boolean result = false;
            FTPClient ftp = new FTPClient();
            // 设置5S超时,解决错误配置端口号导致的超时无响应问题 add by bob.liu at 20191011
            ftp.setConnectTimeout(5 * 1000);
            int reply;
            try {
                ftp.connect(ftpUrl, ftpPort);
                result = ftp.login(ftpUsername, ftpPassword);
                ftp.setFileType(FTPClient.BINARY_FILE_TYPE);
                reply = ftp.getReplyCode();
                if (!FTPReply.isPositiveCompletion(reply)) {
                    ftp.disconnect();
                }
            } catch (IOException e) {
                log.error("exception = ", e);
            }
            return result;
        } else {
            return AttSFTPUtils.testConnection(ftpUrl, ftpPort, ftpUsername, ftpPassword);
        }
    }

    /**
     * 计算开始结束时间并创建文件
     *
     * @param date
     * @param fileName
     * @param filePath
     * @param reportType
     * @param fileType
     * @param timeSendFrequency
     * @param attAutoExport
     * @param timeSendInterval
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/4/22 10:21
     */
    private void calcDateCreateFile(Date date, String fileName, String filePath, String reportType, String fileType,
        String timeSendFrequency, AttAutoExport attAutoExport, String timeSendInterval) {
        // 计算开始结束时间
        Date startDate = null;
        Date endDate = null;
        if (AttAutoExportConstant.TIMESENDFREQUENCY_DAY.equals(timeSendFrequency)) {
            // 以天为频率，一天内只支持设置三个时间点发送当天的报表记录
            // 获取当天0点
            startDate = AttDateUtils.getMinOfDay(date);
            endDate = date;
        } else if (AttAutoExportConstant.TIMESENDFREQUENCY_MONTH.equals(timeSendFrequency)) {
            if (AttAutoExportConstant.TIMEINTERVAL_LASTDAYOFMONTH.equals(timeSendInterval)) {
                // 默认发送当月的报表 获取本月第一天的0:0:0
                // 配置《每月最后一天》，执行时间为每月的第一天的0点0分0秒，所有这边要往前推迟一秒，才是每月最后一天的59秒！！！
                Date dateBefore = DateUtil.addMinutes(date, -1);
                startDate = AttDateUtils.getMonthOfFirstDay(AttDateUtils.getMinOfDay(dateBefore));
                // 获取本月最后一天的23:59:59
                endDate = AttDateUtils.getMonthOfLastDay(AttDateUtils.getMaxOfDay(dateBefore));
            } else if (AttAutoExportConstant.TIMEINTERVAL_FIRSTDAYOFMONTH.equals(timeSendInterval)) {
                // 默认发送上个月的报表 获取上个月第一天的0:0:0
                startDate = AttDateUtils.getLastMonthOfFirstDay(AttDateUtils.getMinOfDay(date));
                // 获取上个月最后一天的23:59:59
                endDate = AttDateUtils.getLastMonthOfLastDay(AttDateUtils.getMaxOfDay(date));
            } else {
                // 默认在每个月的具体日期发送从上月的的这天到这个月这天的记录 获取上个月这一天的0:0:0
                startDate = AttDateUtils.getLastMonthOfDay(AttDateUtils.getMinOfDay(date));
                endDate = date;
            }
        }

        if (AttAutoExportConstant.FILETYPE_TXT.equals(fileType)) {
            // 文件类型为TXT
            if (AttAutoExportConstant.REPORTTYPE_TRANSACTION.equals(reportType)) {
                // 创建原始记录表TXT
                createTransaction(fileName, filePath, attAutoExport, startDate, endDate);
            } else if (AttAutoExportConstant.REPORTTYPE_DAYCARDDETAILREPORT.equals(reportType)) {
                // 创建日打卡详情表TXT
                createDayCardDetailReport(fileName, filePath, attAutoExport, startDate, endDate);
            } else if (AttAutoExportConstant.REPORTTYPE_DAYDETAILREPORT.equals(reportType)) {
                // 创建日明细表TXT
                createDayDetailReport(fileName, filePath, attAutoExport, startDate, endDate);
            } else if (AttAutoExportConstant.REPORTTYPE_ABNORMAL_REPORT.equals(reportType)) {
                // 出勤异常表TXT
                createAbnormalReportTxt(fileName, filePath, attAutoExport, startDate, endDate);
            } else if (AttAutoExportConstant.REPORTTYPE_MONTHDETAIL_REPORT.equals(reportType)) {
                // 月考勤状态表TXT
                createMonthDetailReportTxt(fileName, filePath, attAutoExport, startDate, endDate);
            } else if (AttAutoExportConstant.REPORTTYPE_MONTHSTATISTICAL_REPORT.equals(reportType)) {
                // 人员汇总表TXT
                createMonthStatisticalReportTxt(fileName, filePath, attAutoExport, startDate, endDate);
            }
        }
        // 发送excel文件
        else if (AttAutoExportConstant.FILETYPE_EXCEL.equals(fileType)) {
            if (AttAutoExportConstant.REPORTTYPE_TRANSACTION.equals(reportType)) {
                // 创建原始记录表Excel文件
                createTransactionExcel(fileName, filePath, attAutoExport, startDate, endDate);
            } else if (AttAutoExportConstant.REPORTTYPE_DAYCARDDETAILREPORT.equals(reportType)) {
                // 创建日打卡详情表Excel文件
                createDayCardDetailReportExcel(fileName, filePath, attAutoExport, startDate, endDate);
            } else if (AttAutoExportConstant.REPORTTYPE_DAYDETAILREPORT.equals(reportType)) {
                // 创建日明细表Excel文件
                createDayDetailReportExcel(fileName, filePath, attAutoExport, startDate, endDate);
            } else if (AttAutoExportConstant.REPORTTYPE_ABNORMAL_REPORT.equals(reportType)) {
                // 创建出勤异常表Excel文件
                createAbnormalReportExcel(fileName, filePath, attAutoExport, startDate, endDate);
            } else if (AttAutoExportConstant.REPORTTYPE_MONTHDETAIL_REPORT.equals(reportType)) {
                // 创建月考勤状态表TExcel文件
                createMonthDetailReportExcel(fileName, filePath, attAutoExport, startDate, endDate);
            } else if (AttAutoExportConstant.REPORTTYPE_MONTHSTATISTICAL_REPORT.equals(reportType)) {
                // 创建人员汇总表Excel文件
                createMonthStatisticalReportExcel(fileName, filePath, attAutoExport, startDate, endDate);
            }
        }
    }

    /**
     * 创建日明细表TXT
     *
     * @param fileName:
     * @param filePath:
     * @param attAutoExport:
     * @param startDate:
     * @param endDate:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/3/3 9:47
     * @since 1.0.0
     */
    private void createDayDetailReport(String fileName, String filePath, AttAutoExport attAutoExport, Date startDate,
        Date endDate) {
        try {

            // 考勤规则单位配置
            List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getByCondition(new AttLeaveTypeItem());
            Map<String, AttLeaveTypeItem> attLeaveTypeNoMap =
                CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getLeaveTypeNo);

            String actualUnit =
                AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.ACTUAL));
            String lateUnit =
                AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.LATE));
            String earlyUnit =
                AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.EARLY));
            String overtimeUnit =
                AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.OVERTIME));
            String absentUnit =
                AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.ABSENT));

            AttDayDetailReportItem condition = new AttDayDetailReportItem();
            condition.setStartDatetimeBegin(startDate);
            condition.setStartDatetimeEnd(endDate);
            List<AttDayDetailReportItem> attDayDetailReportItemList =
                attDayDetailReportService.getByCondition(condition);
            /*if (attDayDetailReportItemList == null || attDayDetailReportItemList.size() == 0) {
                log.error("createDayDetailReportExcel data is empty");
                return;
            }*/

            List<Map<String, Object>> data = new ArrayList<>();
            Map<String, Object> filterData = new HashMap<>();
            String dateFormat = attAutoExport.getFileDateFormat();
            for (AttDayDetailReportItem attDayDetailReportItem : attDayDetailReportItemList) {
                attDayDetailReportItem.setAttDateStr(DateUtil
                    .stringToString(AttDateUtils.dateToStrAsShort(attDayDetailReportItem.getAttDate()), dateFormat));
                Map<String, Object> dataMap = BeanToMapUtil.convertBean(attDayDetailReportItem);
                data.add(dataMap);
            }

            StringBuffer dataFormat = new StringBuffer();
            dataFormat.append(I18nUtil.i18nCode("pers_dept_deptNo") + ":{deptCode} ")
                .append(I18nUtil.i18nCode("pers_dept_deptName") + ":{deptName} ")
                .append(I18nUtil.i18nCode("att_person_pin") + ":{personPin} ")
                .append(I18nUtil.i18nCode("att_person_name") + ":{personName} ")
                .append(I18nUtil.i18nCode("att_person_lastName") + ":{personLastName} ")
                .append(I18nUtil.i18nCode("att_statistical_attDate") + ":{attDateStr} ")
                .append(I18nUtil.i18nCode("att_shift_timeSlotDetail") + ":{timeSlotName} ")
                .append(I18nUtil.i18nCode("att_statistical_shiftTimeData") + ":{shiftTimeData} ")
                .append(I18nUtil.i18nCode("att_statistical_should") + ":{shouldConvert}" + actualUnit + " ")
                .append(I18nUtil.i18nCode("att_statistical_actual") + ":{actualConvert}" + actualUnit + " ")
                .append(I18nUtil.i18nCode("att_statistical_valid") + ":{validConvert}" + actualUnit + " ")
                .append(I18nUtil.i18nCode("att_common_late") + ":{lateMinuteTotalConvert}" + lateUnit + " ")
                .append(I18nUtil.i18nCode("att_common_early") + ":{earlyMinuteTotalConvert}" + earlyUnit + " ")
                .append(I18nUtil.i18nCode("att_common_overtime") + ":{overtimeMinuteConvert}" + overtimeUnit + " ")
                .append(I18nUtil.i18nCode("att_common_absent") + ":{absentConvert}" + absentUnit + " ")
                .append("{attDayDetailReportLeaveMinuteMap}").append(System.getProperty("line.separator"));
            AttTxtUtils.exportTxtFile(data, dataFormat.toString(), filePath, fileName, filterData,
                attLeaveTypeItemList);

        } catch (Exception e) {
            log.error("createDayDetailReport exception = ", e);
        }
    }

    /**
     * 出勤异常表TXT
     *
     * @param fileName:
     * @param filePath:
     * @param attAutoExport:
     * @param startDate:
     * @param endDate:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2025-03-31 10:23
     * @since 1.0.0
     */
    private void createAbnormalReportTxt(String fileName, String filePath, AttAutoExport attAutoExport, Date startDate,
        Date endDate) {
        try {
            // 考勤规则单位配置
            List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getByCondition(new AttLeaveTypeItem());
            Map<String, AttLeaveTypeItem> attLeaveTypeNoMap =
                CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getLeaveTypeNo);
            String lateUnit =
                AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.LATE));
            String earlyUnit =
                AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.EARLY));
            String absentUnit =
                AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.ABSENT));

            AttDayDetailReportItem condition = new AttDayDetailReportItem();
            condition.setStartDatetimeBegin(startDate);
            condition.setStartDatetimeEnd(endDate);
            List<AttDayDetailReportItem> attDayDetailReportItemList =
                    attDayDetailReportService.getAbnormalReportItemData(null, condition, 0, 200000);
//            if (attDayDetailReportItemList == null || attDayDetailReportItemList.size() == 0) {
//                log.error("createAbnormalReportTxt data is empty");
//                return;
//            }

            List<Map<String, Object>> data = new ArrayList<>();
            Map<String, Object> filterData = new HashMap<>();
            String dateFormat = attAutoExport.getFileDateFormat();
            for (AttDayDetailReportItem attDayDetailReportItem : attDayDetailReportItemList) {
                attDayDetailReportItem.setAttDateStr(DateUtil
                    .stringToString(AttDateUtils.dateToStrAsShort(attDayDetailReportItem.getAttDate()), dateFormat));
                Map<String, Object> dataMap = BeanToMapUtil.convertBean(attDayDetailReportItem);
                data.add(dataMap);
            }

            StringBuffer dataFormat = new StringBuffer();
            dataFormat.append(I18nUtil.i18nCode("att_person_pin") + ":{personPin} ")
                .append(I18nUtil.i18nCode("att_person_name") + ":{personName} ")
                .append(I18nUtil.i18nCode("att_person_lastName") + ":{personLastName} ")
                .append(I18nUtil.i18nCode("pers_dept_deptName") + ":{deptName} ")
                .append(I18nUtil.i18nCode("att_statistical_attDate") + ":{attDateStr} ")
                .append(I18nUtil.i18nCode("att_shift_timeSlotDetail") + ":{timeSlotName} ")
                .append(I18nUtil.i18nCode("att_statistical_shiftTimeData") + ":{shiftTimeData} ")
                .append(I18nUtil.i18nCode("att_common_late") + ":{lateMinuteTotalConvert}" + lateUnit + " ")
                .append(I18nUtil.i18nCode("att_common_early") + ":{earlyMinuteTotalConvert}" + earlyUnit + " ")
                .append(I18nUtil.i18nCode("att_common_absent") + ":{absentConvert}" + absentUnit)
                .append(System.getProperty("line.separator"));
            AttTxtUtils.exportTxtFile(data, dataFormat.toString(), filePath, fileName, filterData,
                attLeaveTypeItemList);

        } catch (Exception e) {
            log.error("createAbnormalReportTxt exception = ", e);
        }
    }

    private void createMonthDetailReportTxt(String fileName, String filePath, AttAutoExport attAutoExport,
        Date startDate, Date endDate) {
        try {

            AttMonthDetailReportItem condition = new AttMonthDetailReportItem();
            condition.setMonthStart(startDate);
            condition.setMonthEnd(endDate);
            List<AttMonthDetailReportItem> attMonthDetailReportItemList =
                attMonthDetailReportService.getMonthDetailReportItemData(null, condition, 0, 200000, false);
            /*if (attMonthDetailReportItemList == null || attMonthDetailReportItemList.size() == 0) {
                log.error("createMonthDetailReportTxt data is empty");
                return;
            }*/

            List<Map<String, Object>> data = new ArrayList<>();
            Map<String, Object> filterData = new HashMap<>();
            for (AttMonthDetailReportItem item : attMonthDetailReportItemList) {
                Map<String, Object> dataMap = BeanToMapUtil.convertBean(item);
                data.add(dataMap);
            }

            StringBuffer dataFormat = new StringBuffer();
            dataFormat.append(I18nUtil.i18nCode("att_person_pin") + ":{pin} ")
                .append(I18nUtil.i18nCode("att_person_name") + ":{name} ")
                .append(I18nUtil.i18nCode("att_person_lastName") + ":{lastName} ")
                .append(I18nUtil.i18nCode("pers_dept_deptName") + ":{deptName} ").append("{monthDetailMap}")
                .append(System.getProperty("line.separator"));
            AttTxtUtils.exportTxtFile(data, dataFormat.toString(), filePath, fileName, filterData, null);

        } catch (Exception e) {
            log.error("createMonthDetailReportTxt exception = ", e);
        }
    }

    private void createMonthStatisticalReportTxt(String fileName, String filePath, AttAutoExport attAutoExport,
        Date startDate, Date endDate) {
        try {

            // 考勤规则单位配置
            List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getByCondition(new AttLeaveTypeItem());
            Map<String, AttLeaveTypeItem> attLeaveTypeNoMap =
                CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getLeaveTypeNo);

            String actualUnit =
                AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.ACTUAL));
            String lateUnit =
                AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.LATE));
            String earlyUnit =
                AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.EARLY));
            String overtimeUnit =
                AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.OVERTIME));
            String absentUnit =
                AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.ABSENT));

            AttMonthStatisticalReportItem condition = new AttMonthStatisticalReportItem();
            condition.setMonthStart(startDate);
            condition.setMonthEnd(endDate);
            List<AttMonthStatisticalReportItem> attMonthStatisticalReportItemList =
                attMonthStatisticalReportService.getMonthStatisticalReportItemData(null, condition, 0, 200000);
            /*if (attMonthStatisticalReportItemList == null || attMonthStatisticalReportItemList.size() == 0) {
                log.error("createMonthStatisticalReportTxt data is empty");
                return;
            }*/

            List<Map<String, Object>> data = new ArrayList<>();
            Map<String, Object> filterData = new HashMap<>();
            String dateFormat = attAutoExport.getFileDateFormat();
            for (AttMonthStatisticalReportItem item : attMonthStatisticalReportItemList) {
                Map<String, Object> dataMap = BeanToMapUtil.convertBean(item);
                data.add(dataMap);
            }

            StringBuffer dataFormat = new StringBuffer();
            dataFormat.append(I18nUtil.i18nCode("att_person_pin") + ":{pin} ")
                .append(I18nUtil.i18nCode("att_person_name") + ":{name} ")
                .append(I18nUtil.i18nCode("att_person_lastName") + ":{lastName} ")
                .append(I18nUtil.i18nCode("pers_dept_deptName") + ":{deptName} ")
                .append(I18nUtil.i18nCode("att_statistical_should") + ":{shouldHour}" + actualUnit + " ")
                .append(I18nUtil.i18nCode("att_statistical_actual") + ":{actualHour}" + actualUnit + " ")
                .append(I18nUtil.i18nCode("att_statistical_valid") + ":{validHour}" + actualUnit + " ")
                .append(I18nUtil.i18nCode("att_common_late") + ":{lateMinute}" + lateUnit + " ")
                .append(I18nUtil.i18nCode("att_common_early") + ":{earlyMinute}" + earlyUnit + " ")
                .append(I18nUtil.i18nCode("att_common_overtime") + ":{overtimeHour}" + overtimeUnit + " ")
                .append(I18nUtil.i18nCode("att_common_absent") + ":{absentHour}" + absentUnit + " ")
                .append("{attMonthDetailReportLeaveHourMap}").append(System.getProperty("line.separator"));
            AttTxtUtils.exportTxtFile(data, dataFormat.toString(), filePath, fileName, filterData,
                attLeaveTypeItemList);

        } catch (Exception e) {
            log.error("createMonthStatisticalReportTxt exception = ", e);
        }
    }

    /**
     * 创建日明细表Excel文件
     *
     * @param fileName:
     * @param filePath:
     * @param attAutoExport:
     * @param startDate:
     * @param endDate:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/3/3 11:24
     * @since 1.0.0
     */
    private void createDayDetailReportExcel(String fileName, String filePath, AttAutoExport attAutoExport,
        Date startDate, Date endDate) {

        // 考勤规则单位配置
        List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getByCondition(new AttLeaveTypeItem());

        AttDayDetailReportItem condition = new AttDayDetailReportItem();
        condition.setStartDatetimeBegin(startDate);
        condition.setStartDatetimeEnd(endDate);
        List<AttDayDetailReportItem> attDayDetailReportItemList = attDayDetailReportService.getByCondition(condition);
        if (attDayDetailReportItemList == null || attDayDetailReportItemList.size() == 0) {
            log.error("createDayDetailReportExcel data is empty");
            return;
        }

        // 数据处理
        String dateFormat = attAutoExport.getFileDateFormat();
        for (AttDayDetailReportItem attDayDetailReportItem : attDayDetailReportItemList) {
            attDayDetailReportItem.setAttDateStr(DateUtil
                .stringToString(AttDateUtils.dateToStrAsShort(attDayDetailReportItem.getAttDate()), dateFormat));
        }

        OutputStream out = null;
        try {
            String dataFormat = StringEscapeUtils.unescapeJava(attAutoExport.getFileContentFormat());
            out = new FileOutputStream(new File(filePath + fileName));
            AttExcelUtils.writeDayDetail(attDayDetailReportItemList, dataFormat, fileName, out, startDate, endDate,
                attLeaveTypeItemList);
        } catch (FileNotFoundException e) {
            log.error("createDayDetailReportExcel exception = ", e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("exception = ", e);
                }
            }
        }
    }

    /**
     * 创建出勤异常表Excel文件
     *
     * @param fileName:
     * @param filePath:
     * @param attAutoExport:
     * @param startDate:
     * @param endDate:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/3/3 11:24
     * @since 1.0.0
     */
    private void createAbnormalReportExcel(String fileName, String filePath, AttAutoExport attAutoExport,
        Date startDate, Date endDate) {
        // 考勤规则单位配置
        List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getByCondition(new AttLeaveTypeItem());
        AttDayDetailReportItem condition = new AttDayDetailReportItem();
        condition.setStartDatetimeBegin(startDate);
        condition.setStartDatetimeEnd(endDate);
        List<AttDayDetailReportItem> attDayDetailReportItemList =
            attDayDetailReportService.getAbnormalReportItemData(null, condition, 0, 200000);
        /*if (attDayDetailReportItemList == null || attDayDetailReportItemList.size() == 0) {
            log.error("createAbnormalReportExcel data is empty");
            return;
        }*/
        // 数据处理
        String dateFormat = attAutoExport.getFileDateFormat();
        for (AttDayDetailReportItem attDayDetailReportItem : attDayDetailReportItemList) {
            attDayDetailReportItem.setAttDateStr(DateUtil
                .stringToString(AttDateUtils.dateToStrAsShort(attDayDetailReportItem.getAttDate()), dateFormat));
        }
        OutputStream out = null;
        try {
            String dataFormat = StringEscapeUtils.unescapeJava(attAutoExport.getFileContentFormat());
            out = new FileOutputStream(new File(filePath + fileName));
            AttExcelUtils.writeAbnormal(attDayDetailReportItemList, dataFormat, fileName, out, startDate, endDate,
                attLeaveTypeItemList);
        } catch (FileNotFoundException e) {
            log.error("createAbnormalReportExcel exception = ", e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("exception = ", e);
                }
            }
        }
    }

    /**
     * 创建日明细表Excel文件
     *
     * @param fileName:
     * @param filePath:
     * @param attAutoExport:
     * @param startDate:
     * @param endDate:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/3/3 11:24
     * @since 1.0.0
     */
    private void createMonthDetailReportExcel(String fileName, String filePath, AttAutoExport attAutoExport,
        Date startDate, Date endDate) {

        AttMonthDetailReportItem condition = new AttMonthDetailReportItem();
        condition.setMonthStart(startDate);
        condition.setMonthEnd(endDate);
        List<AttMonthDetailReportItem> attMonthDetailReportItemList =
            attMonthDetailReportService.getMonthDetailReportItemData(null, condition, 0, 200000, false);
        /*if (attMonthDetailReportItemList == null || attMonthDetailReportItemList.size() == 0) {
            log.error("createMonthDetailReportTxt data is empty");
            return;
        }*/

        OutputStream out = null;
        try {
            String dataFormat = StringEscapeUtils.unescapeJava(attAutoExport.getFileContentFormat());
            out = new FileOutputStream(new File(filePath + fileName));
            AttExcelUtils.writeMonthDetail(attMonthDetailReportItemList, dataFormat, fileName, out, startDate, endDate);
        } catch (FileNotFoundException e) {
            log.error("createDayDetailReportExcel exception = ", e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("exception = ", e);
                }
            }
        }
    }

    /**
     * 创建日明细表Excel文件
     *
     * @param fileName:
     * @param filePath:
     * @param attAutoExport:
     * @param startDate:
     * @param endDate:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/3/3 11:24
     * @since 1.0.0
     */
    private void createMonthStatisticalReportExcel(String fileName, String filePath, AttAutoExport attAutoExport,
        Date startDate, Date endDate) {

        // 考勤规则单位配置
        List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getByCondition(new AttLeaveTypeItem());

        AttMonthStatisticalReportItem condition = new AttMonthStatisticalReportItem();
        condition.setMonthStart(startDate);
        condition.setMonthEnd(endDate);
        List<AttMonthStatisticalReportItem> attMonthStatisticalReportItemList =
            attMonthStatisticalReportService.getMonthStatisticalReportItemData(null, condition, 0, 200000);
        /*if (attMonthStatisticalReportItemList == null || attMonthStatisticalReportItemList.size() == 0) {
            log.error("createMonthStatisticalReportExcel data is empty");
            return;
        }*/

        // 数据处理
        String dateFormat = attAutoExport.getFileDateFormat();

        OutputStream out = null;
        try {
            String dataFormat = StringEscapeUtils.unescapeJava(attAutoExport.getFileContentFormat());
            out = new FileOutputStream(new File(filePath + fileName));
            AttExcelUtils.writeMonthStatistical(attMonthStatisticalReportItemList, dataFormat, fileName, out, startDate,
                endDate, attLeaveTypeItemList);
        } catch (FileNotFoundException e) {
            log.error("createDayDetailReportExcel exception = ", e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("exception = ", e);
                }
            }
        }
    }

    /**
     * 创建原始记录表TXT
     *
     * @param fileName
     * @param filePath
     * @param attAutoExport
     * @param startDate
     * @param endDate
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/4/22 10:20
     */
    private void createTransaction(String fileName, String filePath, AttAutoExport attAutoExport, Date startDate,
        Date endDate) {
        try {

            AttTransactionItem attTransactionItem = new AttTransactionItem();
            attTransactionItem.setBeginDate(startDate);
            attTransactionItem.setEndDate(endDate);
            List<AttTransactionItem> attTransactionList = attTransactionService.getByCondition(attTransactionItem);
            /*if (attTransactionList == null || attTransactionList.size() == 0) {
                log.error("createTransaction data is empty");
                return;
            }*/

            // 数据处理
            List<Map<String, Object>> data = new ArrayList<>();
            Map<String, Object> dataMap = null;
            Map<String, Object> filterData = new HashMap<>();
            String dateFormat = attAutoExport.getFileDateFormat(); // 日期格式
            String timeFormat = attAutoExport.getFileTimeFormat(); // 时间格式
            String dateTime = "";
            // 获取门禁考勤点(特点：AttPoint表的deviceId对应的是设备某个门的ID)
            Map<String, List<AttPointItem>> accTransMapByDoorNo = getAccOrVmsPoints(attTransactionList, "acc");
            // 获取VMS考勤点(特点：AttPoint表的deviceId对应的是设备某个通道的ID)
            Map<String, List<AttPointItem>> vmsTransMapByDoorNo = getAccOrVmsPoints(attTransactionList, "vms");
            // 获取其他模块考勤点(特点：AttPoint表的deviceId对应的就是设备的ID)
            List<AttPointItem> otherPointItems = null;
            List<AttTransactionItem> otherPointItemList = attTransactionList.stream()
                .filter(i -> !i.getMark().equals("att") && !i.getMark().equals("acc") && !i.getMark().equals("vms"))
                .collect(Collectors.toList());
            if (Objects.nonNull(otherPointItemList) && otherPointItemList.size() > 0) {
                String deviceIds = CollectionUtil.getPropertys(otherPointItemList, AttTransactionItem::getDeviceId);
                AttPointItem attPointItemCondition = new AttPointItem();
                attPointItemCondition.setInDeviceId(deviceIds);
                otherPointItems = attPointService.getByCondition(attPointItemCondition);
            }
            Map<String, AttPointItem> otherPointItemMap =
                CollectionUtil.listToKeyMap(otherPointItems, AttPointItem::getDeviceId);
            Map<String, String> markFormatMap = AttCommonUtils.getFormatMap(AttTransactionItem.class, "mark");
            for (AttTransactionItem item : attTransactionList) {
                dataMap = BeanToMapUtil.convertBean(item);
                // 日期和时间格式根据自动导出的配置转换
                dateTime = dataMap.get("attDatetime").toString();
                SimpleDateFormat dateTimeSdf = new SimpleDateFormat(dateFormat + " " + timeFormat);
                dateTime = dateTimeSdf.format(DateUtil.stringToDate(dateTime));
                dataMap.put("attDatetime", dateTime);
                dataMap.put("attDate", dateTime.split(" ")[0]);
                dataMap.put("attTime", dateTime.split(" ")[1]);
                dataMap.put("pointName", "");
                // 数据来源改成国际化
                if (StringUtils.isNotBlank(item.getMark())) {
                    dataMap.put("mark", MapUtils.getString(markFormatMap, item.getMark(), ""));
                }

                if (("acc".equals(item.getMark()) && Objects.nonNull(accTransMapByDoorNo))
                    || "vms".equals(item.getMark()) && Objects.nonNull(vmsTransMapByDoorNo)) {
                    List<AttPointItem> pointItems = null;
                    if ("acc".equals(item.getMark())) {
                        pointItems = accTransMapByDoorNo.get(item.getDeviceSn() + "_" + item.getDoorNo());
                    } else {
                        pointItems = vmsTransMapByDoorNo.get(item.getDeviceSn() + "_" + item.getDoorNo());
                    }
                    if (Objects.nonNull(pointItems) && pointItems.size() > 0) {
                        dataMap.put("pointName", pointItems.get(0).getPointName());
                    }
                } else if (!"att".equals(item.getMark()) && Objects.nonNull(otherPointItemMap)
                    && otherPointItemMap.containsKey(item.getDeviceId())) {
                    dataMap.put("pointName", otherPointItemMap.get(item.getDeviceId()).getPointName());
                }

                data.add(dataMap);
            }

            // 导出格式去掉可编辑减少因字段的增加导致长保存超过255的问题
            StringBuffer dataFormat = new StringBuffer();
            dataFormat.append(I18nUtil.i18nCode("pers_dept_deptNo") + ":{deptCode} ")
                .append(I18nUtil.i18nCode("pers_dept_deptName") + ":{deptName} ")
                .append(I18nUtil.i18nCode("att_person_pin") + ":{personPin} ")
                .append(I18nUtil.i18nCode("att_person_name") + ":{personName} ")
                .append(I18nUtil.i18nCode("att_person_lastName") + ":{personLastName} ")
                .append(I18nUtil.i18nCode("common_dev_sn") + ":{deviceSn} ")
                .append(I18nUtil.i18nCode("common_dev_name") + ":{deviceName} ")
                .append(I18nUtil.i18nCode("att_attPoint_name") + ":{pointName} ")
                .append(I18nUtil.i18nCode("pers_person_attArea") + ":{areaName} ")
                .append(I18nUtil.i18nCode("att_statistical_attDatetime") + ":{attDatetime} ")
                // .append(I18nUtil.i18nCode("common_verifyMode_entiy") + ":{attVerify} ")
                .append(I18nUtil.i18nCode("att_statistical_dataSources") + ":{mark} ")
                // 换行处理
                .append(System.getProperty("line.separator"));
            AttTxtUtils.exportTxtFile(data, dataFormat.toString(), filePath, fileName, filterData, null);

        } catch (Exception e) {
            log.error("exception = ", e);
        }
    }

    /**
     * 创建原始记录表Excel
     *
     * @param fileName
     * @param filePath
     * @param attAutoExport
     * @param startDate
     * @param endDate
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2019/6/6 16:50
     */
    private void createTransactionExcel(String fileName, String filePath, AttAutoExport attAutoExport, Date startDate,
        Date endDate) {

        AttTransactionItem attTransactionItem = new AttTransactionItem();
        attTransactionItem.setBeginDate(startDate);
        attTransactionItem.setEndDate(endDate);
        List<AttTransactionItem> attTransactionList = attTransactionService.getByCondition(attTransactionItem);
        /*if (attTransactionList == null || attTransactionList.size() == 0) {
            log.error("createTransactionExcel data is empty");
            return;
        }*/

        // 获取门禁考勤点(特点：AttPoint表的deviceId对应的是设备某个门的ID)
        Map<String, List<AttPointItem>> accTransMapByDoorNo = getAccOrVmsPoints(attTransactionList, "acc");
        // 获取VMS考勤点(特点：AttPoint表的deviceId对应的是设备某个通道的ID)
        Map<String, List<AttPointItem>> vmsTransMapByDoorNo = getAccOrVmsPoints(attTransactionList, "vms");
        // 获取其他模块考勤点(特点：AttPoint表的deviceId对应的就是设备的ID)
        List<AttPointItem> otherPointItems = null;
        List<AttTransactionItem> otherPointItemList = attTransactionList.stream()
            .filter(i -> !i.getMark().equals("att") && !i.getMark().equals("acc") && !i.getMark().equals("vms"))
            .collect(Collectors.toList());
        if (Objects.nonNull(otherPointItemList) && otherPointItemList.size() > 0) {
            String deviceIds = CollectionUtil.getPropertys(otherPointItemList, AttTransactionItem::getDeviceId);
            AttPointItem attPointItemCondition = new AttPointItem();
            attPointItemCondition.setInDeviceId(deviceIds);
            otherPointItems = attPointService.getByCondition(attPointItemCondition);
        }
        Map<String, AttPointItem> otherPointItemMap =
            CollectionUtil.listToKeyMap(otherPointItems, AttPointItem::getDeviceId);

        Map<String, String> markFormatMap = AttCommonUtils.getFormatMap(AttTransactionItem.class, "mark");

        attTransactionList.forEach(item -> {
            item.setPointName("");
            if (("acc".equals(item.getMark()) && Objects.nonNull(accTransMapByDoorNo))
                || "vms".equals(item.getMark()) && Objects.nonNull(vmsTransMapByDoorNo)) {
                List<AttPointItem> pointItems = null;
                if ("acc".equals(item.getMark())) {
                    pointItems = accTransMapByDoorNo.get(item.getDeviceSn() + "_" + item.getDoorNo());
                } else {
                    pointItems = vmsTransMapByDoorNo.get(item.getDeviceSn() + "_" + item.getDoorNo());
                }
                if (Objects.nonNull(pointItems) && pointItems.size() > 0) {
                    item.setPointName(pointItems.get(0).getPointName());
                }
            } else if (!"att".equals(item.getMark()) && Objects.nonNull(otherPointItemMap)
                && otherPointItemMap.containsKey(item.getDeviceId())) {
                item.setPointName(otherPointItemMap.get(item.getDeviceId()).getPointName());
            }
            // 数据来源改成国际化
            if (StringUtils.isNotBlank(item.getMark())) {
                item.setMark(MapUtils.getString(markFormatMap, item.getMark(), ""));
            }
        });

        OutputStream out = null;
        try {
            String dateTimeFormat = attAutoExport.getFileDateFormat() + " " + attAutoExport.getFileTimeFormat();
            out = new FileOutputStream(new File(filePath + fileName));
            AttExcelUtils.write(attTransactionList, dateTimeFormat, fileName, out, startDate, endDate);

        } catch (FileNotFoundException e) {
            log.error("exception = ", e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("exception = ", e);
                }
            }
        }
    }

    /**
     * 用于门禁和VMS模块获取对应考勤点
     *
     * @param attTransactionList
     * @param moduleType 模块标识
     * @return
     * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
     * @since 2020年2月11日 下午17:18:44
     */
    private Map<String, List<AttPointItem>> getAccOrVmsPoints(List<AttTransactionItem> attTransactionList,
        String moduleType) {
        Map<String, List<AttPointItem>> attTransMapByDoorNo = null;
        List<AttTransactionItem> attTransItemList =
            attTransactionList.stream().filter(i -> i.getMark().equals(moduleType)).collect(Collectors.toList());
        if (Objects.nonNull(attTransItemList) && attTransItemList.size() > 0) {
            String deviceSns = CollectionUtil.getPropertys(attTransItemList, AttTransactionItem::getDeviceSn);
            AttPointItem attPointItemCondition = new AttPointItem();
            attPointItemCondition.setInDeviceSn(deviceSns);
            List<AttPointItem> attPointItems = attPointService.getByCondition(attPointItemCondition);
            attTransMapByDoorNo = attPointItems.stream()
                .collect(Collectors.groupingBy(point -> point.getDeviceSn() + "_" + point.getDoorNo()));
        }
        return attTransMapByDoorNo;
    }

    /**
     * 创建日打卡详情表TXT
     *
     * @param fileName
     * @param filePath
     * @param attAutoExport
     * @param startDate
     * @param endDate
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/4/22 10:21
     */
    private void createDayCardDetailReport(String fileName, String filePath, AttAutoExport attAutoExport,
        Date startDate, Date endDate) {

        AttDayCardDetailItem condition = new AttDayCardDetailItem();
        condition.setBeginDate(AttDateUtils.dateToStrAsShort(startDate));
        condition.setEndDate(AttDateUtils.dateToStrAsShort(endDate));
        List<AttDayCardDetailItem> attDayCardDetailItemList = attDayCardDetailService.getByCondition(condition);
        /*if (attDayCardDetailItemList == null || attDayCardDetailItemList.size() == 0) {
            log.error("createDayCardDetailReport data is empty");
            return;
        }*/

        try {

            List<Map<String, Object>> data = new ArrayList<>();
            Map<String, Object> filterData = new HashMap<>();
            String dateFormat = attAutoExport.getFileDateFormat();
            String timeFormat = attAutoExport.getFileTimeFormat();
            for (AttDayCardDetailItem attDayCardDetailItem : attDayCardDetailItemList) {
                attDayCardDetailItem.setAttDate(DateUtil.stringToString(attDayCardDetailItem.getAttDate(), dateFormat));
                attDayCardDetailItem
                    .setEarliestTime(DateUtil.stringToString(attDayCardDetailItem.getEarliestTime(), timeFormat));
                attDayCardDetailItem
                    .setLatestTime(DateUtil.stringToString(attDayCardDetailItem.getLatestTime(), timeFormat));
                String attTimes = attDayCardDetailItem.getAttTimes();
                StringBuffer attTimeStrBuf = new StringBuffer();
                for (String attTime : attTimes.split(";")) {
                    attTimeStrBuf.append(DateUtil.stringToString(attTime, timeFormat)).append(";");
                }
                attDayCardDetailItem.setAttTimes(attTimeStrBuf.toString());
                Map<String, Object> dataMap = BeanToMapUtil.convertBean(attDayCardDetailItem);
                data.add(dataMap);
            }

            StringBuffer dataFormat = new StringBuffer();
            dataFormat.append(I18nUtil.i18nCode("pers_dept_deptNo") + ":{deptCode} ")
                .append(I18nUtil.i18nCode("pers_dept_deptName") + ":{deptName} ")
                .append(I18nUtil.i18nCode("att_person_pin") + ":{personPin} ")
                .append(I18nUtil.i18nCode("att_person_name") + ":{personName} ")
                .append(I18nUtil.i18nCode("att_person_lastName") + ":{personLastName} ")
                .append(I18nUtil.i18nCode("att_statistical_cardDate") + ":{attDate} ")
                .append(I18nUtil.i18nCode("att_statistical_earliestTime") + ":{earliestTime} ")
                .append(I18nUtil.i18nCode("att_statistical_latestTime") + ":{latestTime} ")
                .append(I18nUtil.i18nCode("att_statistical_cardTime") + ":{attTimes} ")
                .append(System.getProperty("line.separator"));
            AttTxtUtils.exportTxtFile(data, dataFormat.toString(), filePath, fileName, filterData, null);

        } catch (Exception e) {
            log.error("createDayCardDetailReport exception = ", e);
        }
    }

    /**
     * 创建日打卡详情表Excel
     *
     * @param fileName
     * @param filePath
     * @param attAutoExport
     * @param startDate
     * @param endDate
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2019/6/6 16:50
     */
    private void createDayCardDetailReportExcel(String fileName, String filePath, AttAutoExport attAutoExport,
        Date startDate, Date endDate) {

        AttDayCardDetailItem condition = new AttDayCardDetailItem();
        condition.setBeginDate(AttDateUtils.dateToStrAsShort(startDate));
        condition.setEndDate(AttDateUtils.dateToStrAsShort(endDate));
        List<AttDayCardDetailItem> attDayCardDetailItemList = attDayCardDetailService.getByCondition(condition);
        /*if (attDayCardDetailItemList == null || attDayCardDetailItemList.size() == 0) {
            log.error("createDayCardDetailReport data is empty");
            return;
        }*/

        String dateFormat = attAutoExport.getFileDateFormat();
        String timeFormat = attAutoExport.getFileTimeFormat();

        // 数据处理
        for (AttDayCardDetailItem attDayCardDetailItem : attDayCardDetailItemList) {
            attDayCardDetailItem.setAttDate(DateUtil.stringToString(attDayCardDetailItem.getAttDate(), dateFormat));
            attDayCardDetailItem
                .setEarliestTime(DateUtil.stringToString(attDayCardDetailItem.getEarliestTime(), timeFormat));
            attDayCardDetailItem
                .setLatestTime(DateUtil.stringToString(attDayCardDetailItem.getLatestTime(), timeFormat));
            String attTimes = attDayCardDetailItem.getAttTimes();
            StringBuffer attTimeStrBuf = new StringBuffer();
            for (String attTime : attTimes.split(";")) {
                attTimeStrBuf.append(DateUtil.stringToString(attTime, timeFormat)).append(";");
            }
            attDayCardDetailItem.setAttTimes(attTimeStrBuf.toString());
        }

        OutputStream out = null;
        try {
            String dataFormat = StringEscapeUtils.unescapeJava(attAutoExport.getFileContentFormat());
            out = new FileOutputStream(new File(filePath + fileName));
            AttExcelUtils.writeDayCardDetail(attDayCardDetailItemList, dataFormat, fileName, out, startDate, endDate);
        } catch (FileNotFoundException e) {
            log.error("createDayCardDetailReportExcel exception = ", e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("createDayCardDetailReportExcel exception = ", e);
                }
            }
        }
    }

    /**
     * 发送邮件
     *
     * @param attAutoExport
     * @param fileName
     * @param fileAllPath
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/4/22 20:30
     */
    private void sendMail(AttAutoExport attAutoExport, String fileName, String fileAllPath) {
        Collection<String> list = null;
        String emailType = attAutoExport.getEmailType();
        if (AttAutoExportConstant.EMAILTYPE_PERSON.equals(emailType)) {
            list = CollectionUtil.strToList(attAutoExport.getEmailRecipients());
        } else if (AttAutoExportConstant.EMAILTYPE_DEPT.equals(emailType)) {
            String deptIds = attAutoExport.getDeptId();
            list = persPersonService.getEmailsByDeptIds(CollectionUtil.strToList(deptIds));
        } else if (AttAutoExportConstant.EMAILTYPE_AREA.equals(emailType)) {
            String areaIds = attAutoExport.getAreaId();
            list = attPersonDao.getEmailsByAreaIdIn(CollectionUtil.strToList(areaIds));
        }

        if (!CollectionUtil.isEmpty(list)) {
            // 调整群发为拆分每个人单独发送
            for (String email : list) {
                if (StringUtils.isBlank(email)) {
                    continue;
                }

                email = FoldexUtil.decryptByRandomSey(email);

                String emailContent = attAutoExport.getEmailContent() == null ? "" : attAutoExport.getEmailContent();
                if (StringUtils.isNotBlank(fileAllPath) && !CollectionUtils.isEmpty(list)) {
                    baseSendMailService.sendSampleHtmlMail(email, attAutoExport.getEmailSubject(), emailContent,
                        fileAllPath);
                }
            }
        }
    }

    /**
     * 发送到ftp上
     *
     * @param attAutoExport
     * @param fileName
     * @param fileAllPath
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/5/13 20:28
     */
    private void sendToFtp(AttAutoExport attAutoExport, String fileName, String fileAllPath) {
        // 设置ftp文件名
        String getFtpPath = "att/" + AttDateUtils.dateToStrAsShort(new Date()) + "/";
        File attachmentsFile = new File(fileAllPath);
        try {
            FTPClient ftpClient = AttFTPUtils.connect(getFtpPath, attAutoExport.getFtpUrl(), attAutoExport.getFtpPort(),
                attAutoExport.getFtpUsername(), attAutoExport.getFtpPassword());
            AttFTPUtils.upload(attachmentsFile, ftpClient);
            log.info("Ftp upload file is successful!");
        } catch (Exception e) {
            log.error("exception = ", e);
        }
        if (attachmentsFile.exists()) {
            attachmentsFile.delete();
        }
    }

    /**
     * 发送文件到SFTP上
     *
     * @param attAutoExport 自动导出相关实体
     * @param fileFullPath 要上传的文件路径
     * <AUTHOR>
     * @date 2024/10/24 11:18
     */
    private void sendToSFTP(AttAutoExport attAutoExport, String fileFullPath) {
        // 设置sftp文件名
        String remoteDirectory = "att/" + AttDateUtils.dateToStrAsShort(new Date()) + "/";
        File attachmentFile = new File(fileFullPath);
        try {
            boolean isSuccess = AttSFTPUtils.uploadFileToServer(attAutoExport.getFtpUrl(), attAutoExport.getFtpPort(),
                attAutoExport.getFtpUsername(), attAutoExport.getFtpPassword(), remoteDirectory, attachmentFile);
            if (isSuccess) {
                log.info("SFTP upload file success!");
            } else {
                log.info("SFTP upload file fail!");
            }
        } catch (Exception e) {
            log.error("send file to SFTP fail", e);
        }
        if (attachmentFile.exists()) {
            attachmentFile.delete();
        }
    }

    /**
     * 文件默认保存路径
     *
     * @param
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/6/3 9:18
     */
    private String attFilePath() {
        return ClassUtil.getRootPath() + FileUtils.separator + FileUtils.systemFilePath + FileUtils.separator + "att"
            + FileUtils.separator;
    }

    /**
     * 每天10点定时删除考勤记录文件(删除前一天的文件)
     *
     * @param
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/6/1 12:41
     */
    @Scheduled(cron = "0 0 10 * * ?")
    public boolean deleteOldFile() {
        // 设置时间格式
        String dateStyle = DateUtil.DateStyle.YYYY_MM_DD.getValue();
        SimpleDateFormat dft = new SimpleDateFormat(dateStyle);
        Date nowDate = new Date();
        Calendar date = Calendar.getInstance();
        date.setTime(nowDate);
        // 获取前一天的时间
        date.set(Calendar.DATE, date.get(Calendar.DATE) - 1);
        try {
            Date dayBefore = dft.parse(dft.format(date.getTime()));
            String attFilePath = attFilePath() + DateUtil.dateToString(dayBefore, dateStyle) + "\\";
            File file = new File(attFilePath);
            if (file == null || !file.exists()) {
                return false;
            }
            // 如果是文件
            if (file.isFile()) {
                return file.delete();
            } else if (file.isDirectory()) {
                File[] childFiles = file.listFiles();
                // 文件夹没有内容,删除文件夹
                if (childFiles == null || childFiles.length == 0) {
                    return file.delete();
                }
                // 删除文件夹内容
                boolean reslut = true;
                for (File files : childFiles) {
                    reslut = reslut && files.delete();
                }
                // 删除文件夹
                return reslut && file.delete();
            }
        } catch (ParseException e) {
            log.error("exception = ", e);
        }
        return false;
    }
}