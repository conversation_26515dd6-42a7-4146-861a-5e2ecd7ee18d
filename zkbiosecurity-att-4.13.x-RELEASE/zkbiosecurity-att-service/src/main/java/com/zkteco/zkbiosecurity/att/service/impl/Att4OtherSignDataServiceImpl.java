package com.zkteco.zkbiosecurity.att.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.service.Att4OtherSignDataService;
import com.zkteco.zkbiosecurity.att.service.AttLeaveService;
import com.zkteco.zkbiosecurity.att.service.AttTransactionService;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveItem;
import com.zkteco.zkbiosecurity.att.vo.AttSignPersonItem;
import com.zkteco.zkbiosecurity.att.vo.AttTransactionItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.FileEncryptUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 签到数据
 *
 * <AUTHOR>
 * @date 2024/6/13 16:37
 * @since 1.0.0
 */
@Service
public class Att4OtherSignDataServiceImpl implements Att4OtherSignDataService {

    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AttTransactionService attTransactionService;
    @Autowired
    private AttLeaveService attLeaveService;


    @Override
    public JSONObject getAttSignDataLst(String depIds, Date startTime, Date endTime) {
        JSONObject result = new JSONObject();
        // 已签到人员
        List<AttSignPersonItem> signPersonList = new ArrayList<>();
        // 未签到人员
        List<AttSignPersonItem> notSignPersonList = new ArrayList<>();
        if (depIds != null) {
            PersPersonItem persPersonItem = new PersPersonItem();
            persPersonItem.setInDeptId(depIds);
            // 获取部门下所有考勤人员
            List<PersPersonItem> personAllList = persPersonService.getByCondition(persPersonItem);
            // 限制800人
            List<List<PersPersonItem>> persSplitList = CollectionUtil.split(personAllList, CollectionUtil.splitSize);
            for (List<PersPersonItem> persList : persSplitList) {
                // 部门下全部考勤人员集合
                Collection<String> persPins = CollectionUtil.getPropertyList(persList, PersPersonItem::getPin, "-1");
                // 部门下考勤人员的最早打卡记录
                List<AttTransactionItem> persSignList = attTransactionService.getItemListByTimeAndPersPins(startTime, endTime, persPins);
                Map<String, AttTransactionItem> persSignListMap = CollectionUtil.listToKeyMap(persSignList, AttTransactionItem::getPersonPin);
                // 部门下考勤人员的最早请假记录
                List<AttLeaveItem> persLeaveList = attLeaveService.getItemListByTimeAndPersPins(startTime, endTime, persPins);
                Map<String, AttLeaveItem> persLeaveListMap = CollectionUtil.listToKeyMap(persLeaveList, AttLeaveItem::getPersonPin);
                for (PersPersonItem item : persList) {
                    AttSignPersonItem attSignPersonItem = new AttSignPersonItem();
                    attSignPersonItem.setId(item.getId());
                    attSignPersonItem.setPin(item.getPin());
                    attSignPersonItem.setName(item.getName());
                    attSignPersonItem.setDepId(item.getDeptId());
                    attSignPersonItem.setDepName(item.getDeptName());
                    attSignPersonItem.setPhoto(item.getPhotoPath());
                    if (persSignListMap.containsKey(item.getPin()) || persLeaveListMap.containsKey(item.getPin())) {
                        attSignPersonItem.setSignStatus(true);
                        signPersonList.add(attSignPersonItem);
                    } else {
                        attSignPersonItem.setSignStatus(false);
                        notSignPersonList.add(attSignPersonItem);
                    }
                }
            }
        }
        result.put("signPersons", signPersonList);
        result.put("notSignPersons", notSignPersonList);
        int signCount = signPersonList.size();
        result.put("signCount", signCount);
        int notSignCount = notSignPersonList.size();
        result.put("notSignCount", notSignCount);
        result.put("totalCount", signCount + notSignCount);
        return result;
    }

    @Override
    public JSONArray getTodaySignTransaction(Integer count, String depIds, Date startTime, Date endTime) {
        JSONArray jsonArray = new JSONArray();
        if (depIds != null) {
            PersPersonItem persPersonItem = new PersPersonItem();
            persPersonItem.setInDeptId(depIds);
            // 获取部门下所有考勤人员
            List<PersPersonItem> personAllList = persPersonService.getByCondition(persPersonItem);
            // 限制800人
            List<List<PersPersonItem>> persSplitList = CollectionUtil.split(personAllList, CollectionUtil.splitSize);
            for (List<PersPersonItem> persList : persSplitList) {
                // 部门下全部考勤人员集合
                Collection<String> persPins = CollectionUtil.getPropertyList(persList, PersPersonItem::getPin, "-1");
                // 部门下考勤人员的最早打卡记录
                List<AttTransactionItem> persSignList = attTransactionService.getItemListByTimeAndPersPins(startTime, endTime, persPins);
                Map<String, AttTransactionItem> persSignListMap = CollectionUtil.listToKeyMap(persSignList, AttTransactionItem::getPersonPin);
                // 部门下考勤人员的最早请假记录
//                List<AttLeaveItem> persLeaveList = attLeaveService.getItemListByTimeAndPersPins(startTime, endTime, persPins);
//                Map<String, AttLeaveItem> persLeaveListMap = CollectionUtil.listToKeyMap(persLeaveList, AttLeaveItem::getPersonPin);
                for (PersPersonItem item : persList) {
                    if (persSignListMap.containsKey(item.getPin())) {
                        AttTransactionItem transactionItem = persSignListMap.get(item.getPin());
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("name", item.getName());
                        jsonObject.put("pin", item.getPin());
                        jsonObject.put("department", item.getDeptName());
                        jsonObject.put("datetime", DateUtil.dateToString(transactionItem.getAttDatetime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                        if (StringUtils.isNotBlank(item.getPhotoPath())) {
                            jsonObject.put("imageSrc", "data:image/jpg;base64," + FileEncryptUtil.getDecryptFileBase64(item.getPhotoPath()));
                        }
                        // jsonObject.put("deviceId", transactionItem.getDeviceId());
                        jsonArray.add(jsonObject);
                    } 
//                    else if (persLeaveListMap.containsKey(item.getPin())) {
//                        JSONObject jsonObject = new JSONObject();
//                        jsonObject.put("name", item.getName());
//                        jsonObject.put("pin", item.getPin());
//                        jsonObject.put("department", item.getDeptName());
//                        jsonObject.put("datetime", I18nUtil.i18nCode("att_leftMenu_leave"));
//                        if (StringUtils.isNotBlank(item.getPhotoPath())) {
//                            jsonObject.put("imageSrc", "data:image/jpg;base64," + FileEncryptUtil.getDecryptFileBase64(item.getPhotoPath()));
//                        }
//                        // jsonObject.put("deviceId", datum.getDeviceId());
//                        jsonArray.add(jsonObject);
//                    }
                }
            }
        }
        return jsonArray;
    }
}
