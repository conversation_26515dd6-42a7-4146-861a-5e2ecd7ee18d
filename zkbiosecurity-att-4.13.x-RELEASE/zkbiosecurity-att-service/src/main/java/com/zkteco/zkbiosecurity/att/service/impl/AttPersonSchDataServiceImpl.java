package com.zkteco.zkbiosecurity.att.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.att.api.vo.AppAttWorkTimeItem;
import com.zkteco.zkbiosecurity.att.bean.AttRuleParamBean;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttTimeSlotBO;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttCommonSchConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.constants.AttShiftConstant;
import com.zkteco.zkbiosecurity.att.enums.AttRuleEnum;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.utils.AttShiftSchUtils;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.pers.service.PersLeavePersonService;

/**
 * 考勤计算人员排班数据组装
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:39
 * @since 1.0.0
 */
@Service
public class AttPersonSchDataServiceImpl implements AttPersonSchDataService {

    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private AttTempSchService attTempSchService;
    @Autowired
    private AttAdjustService attAdjustService;
    @Autowired
    private AttClassService attClassService;
    @Autowired
    private AttHolidayService attHolidayService;
    @Autowired
    private AttShiftService attShiftService;
    @Autowired
    private AttTimeSlotService attTimeSlotService;
    @Autowired
    private AttCycleSchService attCycleSchService;
    @Autowired
    private AttRecordService attRecordService;

    private List<AttPersonSchBO> deepClone(List<AttPersonSchBO> attPersonSchDataMapValue) {
        List<AttPersonSchBO> newAttPersonSchDataMapValue = new ArrayList<>();
        attPersonSchDataMapValue.forEach(v -> {
            AttPersonSchBO newR = ModelUtil.copyProperties(v, new AttPersonSchBO());
            if (v.getAttTimeSlotArray() != null) {
                newR.setAttTimeSlotArray(ModelUtil.copyListProperties(v.getAttTimeSlotArray(), AttTimeSlotBO.class));
            } else {
                newR.setAttTimeSlotArray(new ArrayList<>());
            }
            newAttPersonSchDataMapValue.add(newR);
        });
        return newAttPersonSchDataMapValue;
    }

    @Override
    public Map<String, List<AttPersonSchBO>> getPersonSchData(List<String> pins, Date startDate, Date endDate) {

        // 获取人员信息
        List<AttPersonItem> attPersonItems = attPersonService.getByPinsFromPersonCache(pins);

        // 获取全部班次集合
        List<AttShiftItem> attShiftItems = attShiftService.getByCondition(new AttShiftItem());
        Map<String, AttShiftItem> attShiftItemMap = CollectionUtil.listToKeyMap(attShiftItems, AttShiftItem::getId);

        // 获取所有时间段集合
        List<AttTimeSlotItem> attTimeSlotItems = attTimeSlotService.getByCondition(new AttTimeSlotItem());
        Map<String, AttTimeSlotItem> attTimeSlotItemMap =
            CollectionUtil.listToKeyMap(attTimeSlotItems, AttTimeSlotItem::getId);

        AttRuleParamBean attRuleParamBean = attParamService.getRuleParam();

        return buildPersonSchData(attPersonItems, startDate, endDate, attShiftItemMap, attTimeSlotItemMap,
            attRuleParamBean);
    }

    @Override
    public Map<String, List<AttPersonSchBO>> buildPersonSchData(List<String> pins, Date startDate, Date endDate) {
        List<AttPersonItem> attPersonItems = attPersonService.getItemByPersonPins(pins);
        List<AttShiftItem> attShiftItems = attShiftService.getByCondition(new AttShiftItem());
        Map<String, AttShiftItem> attShiftItemMap =
            attShiftItems.stream().collect(Collectors.toMap(AttShiftItem::getId, Function.identity()));
        List<AttTimeSlotItem> attTimeSlotItems = attTimeSlotService.getAllTimeSlotItem();
        Map<String, AttTimeSlotItem> attTimeSlotItemMap =
            attTimeSlotItems.stream().collect(Collectors.toMap(AttTimeSlotItem::getId, Function.identity()));
        AttRuleParamBean attRuleParamBean = attParamService.getRuleParam();

        return buildPersonSchData(attPersonItems, startDate, endDate, attShiftItemMap, attTimeSlotItemMap,
            attRuleParamBean);
    }

    @Override
    public Map<String, List<AttPersonSchBO>> buildPersonSchData(List<AttPersonItem> attPersonItems, Date startDate,
        Date endDate, Map<String, AttShiftItem> attShiftItemMap, Map<String, AttTimeSlotItem> attTimeSlotItemMap,
        AttRuleParamBean attRuleParamBean) {

        // 查询部门排班
        String deptIds = CollectionUtil.getPropertys(attPersonItems, AttPersonItem::getDeptId);
        // 部门排班集合 （部门ID=日期,班次）
        Map<String, List<AttPersonSchBO>> deptCycleSchMap =
            attCycleSchService.getCycleSch(AttCommonSchConstant.SCH_TYPE_DEPT, deptIds, startDate, endDate,
                attShiftItemMap, attTimeSlotItemMap, attRuleParamBean);
        Map<String, List<AttPersonSchBO>> deptTempSchMap = attTempSchService.getTempSch(
            AttCommonSchConstant.SCH_TYPE_DEPT, deptIds, startDate, endDate, attTimeSlotItemMap, attRuleParamBean);

        // 查询分组排班（分组ID=日期,班次）
        String groupIds = CollectionUtil.getPropertys(attPersonItems, AttPersonItem::getGroupId);
        Map<String, List<AttPersonSchBO>> groupCycleSchMap =
            attCycleSchService.getCycleSch(AttCommonSchConstant.SCH_TYPE_GROUP, groupIds, startDate, endDate,
                attShiftItemMap, attTimeSlotItemMap, attRuleParamBean);
        Map<String, List<AttPersonSchBO>> groupTempSchMap = attTempSchService.getTempSch(
            AttCommonSchConstant.SCH_TYPE_GROUP, groupIds, startDate, endDate, attTimeSlotItemMap, attRuleParamBean);

        // 查询人员排班（PIN=日期,班次）
        String personPins = CollectionUtil.getPropertys(attPersonItems, AttPersonItem::getPersonPin);
        Map<String, List<AttPersonSchBO>> personCycleSchMap =
            attCycleSchService.getCycleSch(AttCommonSchConstant.SCH_TYPE_PERSON, personPins, startDate, endDate,
                attShiftItemMap, attTimeSlotItemMap, attRuleParamBean);
        Map<String, List<AttPersonSchBO>> personTempSchMap = attTempSchService.getTempSch(
            AttCommonSchConstant.SCH_TYPE_PERSON, personPins, startDate, endDate, attTimeSlotItemMap, attRuleParamBean);

        // 根据人员排班/分组排班/部门排班优先级比较得到人/天/班排班情况
        Map<String, List<AttPersonSchBO>> schDataMap = new HashMap<>();
        for (AttPersonItem attPersonItem : attPersonItems) {

            Date hireDate = attPersonItem.getHireDate();
            Date leaveDate = attPersonItem.getLeaveDate();

            // 开始日期 = max(入职日期，计算开始日期)
            Date start = (null == hireDate || startDate.after(hireDate)) ? startDate : hireDate;
            // 结束日期 = min(离职日期, 计算结束日期)
            Date end = (null == leaveDate || endDate.before(leaveDate)) ? endDate : leaveDate;

            String personPin = attPersonItem.getPersonPin();
            String groupId = attPersonItem.getGroupId();
            String deptId = attPersonItem.getDeptId();

            // 循环天 填充 <部门=日期,班次(时间段)>;
            AttDateUtils.forEachDay(start, end, date -> {
                String dateStr = AttDateUtils.dateToStrAsShort(date);

                // 人员排班:有人员排班则优先就取人员排班返回即可
                String personSchKey = personPin + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                // 人员临时排班
                List<AttPersonSchBO> attPersonSchBOList = personTempSchMap.get(personSchKey);
                if (null != attPersonSchBOList) {
                    schDataMap.put(personSchKey, deepClone(attPersonSchBOList));
                    return;
                }
                // 人员周期排班
                attPersonSchBOList = personCycleSchMap.get(personSchKey);
                if (null != attPersonSchBOList) {
                    schDataMap.put(personSchKey, deepClone(attPersonSchBOList));
                    return;
                }

                // 分组排班
                String groupSchKey = groupId + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                // 分组临时排班
                attPersonSchBOList = groupTempSchMap.get(groupSchKey);
                if (null != attPersonSchBOList) {
                    schDataMap.put(personSchKey, deepClone(attPersonSchBOList));
                    return;
                }
                // 分组周期排班
                attPersonSchBOList = groupCycleSchMap.get(groupSchKey);
                if (null != attPersonSchBOList) {
                    schDataMap.put(personSchKey, deepClone(attPersonSchBOList));
                    return;
                }

                // 部门排班
                String deptSchKey = deptId + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                // 部门临时排班
                attPersonSchBOList = deptTempSchMap.get(deptSchKey);
                if (null != attPersonSchBOList) {
                    schDataMap.put(personSchKey, deepClone(attPersonSchBOList));
                    return;
                }
                // 部门周期排班
                attPersonSchBOList = deptCycleSchMap.get(deptSchKey);
                if (null != attPersonSchBOList) {
                    schDataMap.put(personSchKey, deepClone(attPersonSchBOList));
                    return;
                }

                // 填充未排班
                attPersonSchBOList = new ArrayList<>();
                AttPersonSchBO attPersonSchBO = new AttPersonSchBO();
                attPersonSchBO.setAttendStatus(AttCalculationConstant.AttAttendStatus.NO_SCHEDULING);
                attPersonSchBOList.add(attPersonSchBO);
                schDataMap.put(personSchKey, deepClone(attPersonSchBOList));

            });
        }
        return schDataMap;
    }

    @Override
    public Map<String, List<AttPersonSchBO>> buildPersonSchData2(List<AttPersonItem> attPersonItems, Date startDate,
        Date endDate, Map<String, AttShiftItem> attShiftItemMap, Map<String, AttTimeSlotItem> attTimeSlotItemMap,
        AttRuleParamBean attRuleParamBean, List<AttCycleSchItem> attCycleSchItemList,
        List<AttTempSchItem> attTempSchItemList) {

        // 部门排班集合 （部门ID=日期,班次）
        Map<String, List<AttPersonSchBO>> deptCycleSchMap = new HashMap<>();
        Map<String, List<AttPersonSchBO>> deptTempSchMap = new HashMap<>();
        // 查询分组排班（分组ID=日期,班次）
        Map<String, List<AttPersonSchBO>> groupCycleSchMap = new HashMap<>();
        Map<String, List<AttPersonSchBO>> groupTempSchMap = new HashMap<>();
        // 查询人员排班（PIN=日期,班次）
        Map<String, List<AttPersonSchBO>> personCycleSchMap = new HashMap<>();
        Map<String, List<AttPersonSchBO>> personTempSchMap = new HashMap<>();

        String crossDay = attRuleParamBean.getCrossDay();

        // 查询填充周期排班
        fillCycSchMap(startDate, endDate, attShiftItemMap, attTimeSlotItemMap, crossDay, deptCycleSchMap,
            groupCycleSchMap, personCycleSchMap, attCycleSchItemList);

        // 查询填充临时排班
        fillTempSchMap(startDate, endDate, attTimeSlotItemMap, crossDay, deptTempSchMap, groupTempSchMap,
            personTempSchMap, attTempSchItemList);

        // 根据人员排班/分组排班/部门排班优先级比较得到人/天/班排班情况
        Map<String, List<AttPersonSchBO>> schDataMap = new HashMap<>();
        for (AttPersonItem attPersonItem : attPersonItems) {

            Date hireDate = attPersonItem.getHireDate();
            Date leaveDate = attPersonItem.getLeaveDate();

            // 开始日期 = max(入职日期，计算开始日期)
            Date start = (null == hireDate || startDate.after(hireDate)) ? startDate : hireDate;
            // 结束日期 = min(离职日期, 计算结束日期)
            Date end = (null == leaveDate || endDate.before(leaveDate)) ? endDate : leaveDate;

            String personPin = attPersonItem.getPersonPin();
            String groupId = attPersonItem.getGroupId();
            String deptId = attPersonItem.getDeptId();

            // 循环天 填充 <部门=日期,班次(时间段)>;
            AttDateUtils.forEachDay(start, end, date -> {
                String dateStr = AttDateUtils.dateToStrAsShort(date);

                // 人员排班:有人员排班则优先就取人员排班返回即可
                String personSchKey = personPin + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                // 人员临时排班
                List<AttPersonSchBO> attPersonSchBOList = personTempSchMap.get(personSchKey);
                if (null != attPersonSchBOList) {
                    schDataMap.put(personSchKey, deepClone(attPersonSchBOList));
                    return;
                }
                // 人员周期排班
                attPersonSchBOList = personCycleSchMap.get(personSchKey);
                if (null != attPersonSchBOList) {
                    schDataMap.put(personSchKey, deepClone(attPersonSchBOList));
                    return;
                }

                // 分组排班
                String groupSchKey = groupId + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                // 分组临时排班
                attPersonSchBOList = groupTempSchMap.get(groupSchKey);
                if (null != attPersonSchBOList) {
                    schDataMap.put(personSchKey, deepClone(attPersonSchBOList));
                    return;
                }
                // 分组周期排班
                attPersonSchBOList = groupCycleSchMap.get(groupSchKey);
                if (null != attPersonSchBOList) {
                    schDataMap.put(personSchKey, deepClone(attPersonSchBOList));
                    return;
                }

                // 部门排班
                String deptSchKey = deptId + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                // 部门临时排班
                attPersonSchBOList = deptTempSchMap.get(deptSchKey);
                if (null != attPersonSchBOList) {
                    schDataMap.put(personSchKey, deepClone(attPersonSchBOList));
                    return;
                }
                // 部门周期排班
                attPersonSchBOList = deptCycleSchMap.get(deptSchKey);
                if (null != attPersonSchBOList) {
                    schDataMap.put(personSchKey, deepClone(attPersonSchBOList));
                    return;
                }

                // 填充未排班
                attPersonSchBOList = new ArrayList<>();
                AttPersonSchBO attPersonSchBO = new AttPersonSchBO();
                attPersonSchBO.setAttendStatus(AttCalculationConstant.AttAttendStatus.NO_SCHEDULING);
                attPersonSchBOList.add(attPersonSchBO);
                schDataMap.put(personSchKey, attPersonSchBOList);

            });
        }
        return schDataMap;
    }

    /**
     * 查询填充周期排班
     * 
     * @param startDate
     * @param endDate
     * @param attShiftItemMap
     * @param attTimeSlotItemMap
     * @param crossDay
     * @param deptCycleSchMap
     * @param groupCycleSchMap
     * @param personCycleSchMap
     * @param attCycleSchItemList
     */
    private void fillCycSchMap(Date startDate, Date endDate, Map<String, AttShiftItem> attShiftItemMap,
        Map<String, AttTimeSlotItem> attTimeSlotItemMap, String crossDay,
        Map<String, List<AttPersonSchBO>> deptCycleSchMap, Map<String, List<AttPersonSchBO>> groupCycleSchMap,
        Map<String, List<AttPersonSchBO>> personCycleSchMap, List<AttCycleSchItem> attCycleSchItemList) {

        // 循环周期排班 填充 <ID_日期,班次(时间段)>;
        for (AttCycleSchItem attCycleSchItem : attCycleSchItemList) {
            Date schStartDate = attCycleSchItem.getStartDate();
            Date schEndDate = attCycleSchItem.getEndDate();
            // 开始日期 = max(排班开始日期，计算开始日期)
            Date start = startDate.after(schStartDate) ? startDate : schStartDate;
            // 结束日期 = min(计算结束日期，排班结束日期)
            Date end = (null == schEndDate || endDate.before(schEndDate)) ? endDate : schEndDate;
            // 循环天 拆分排班为天填充 <ID=日期,班次(时间段)>;
            AttDateUtils.forEachDay(start, end, date -> {

                String dateStr = AttDateUtils.dateToStrAsShort(date);
                List<AttPersonSchBO> attPersonSchBOList = new ArrayList<>();
                String shiftIds = attCycleSchItem.getShiftIds();

                String[] shiftIdArr = shiftIds.split(",");
                // 多个班次则是智能找班
                for (String shiftId : shiftIdArr) {
                    AttShiftItem attShiftItem = attShiftItemMap.get(shiftId);
                    AttPersonSchBO attPersonSchBO = new AttPersonSchBO();
                    attPersonSchBO.setAttendStatus(AttCalculationConstant.AttAttendStatus.ACTUAL);
                    attPersonSchBO.setAttShiftId(shiftId);
                    attPersonSchBO.setAttShiftName(attShiftItem.getShiftName());
                    attPersonSchBO.setAttendanceMode(attShiftItem.getAttendanceMode());
                    attPersonSchBO.setOvertimeMode(attShiftItem.getOvertimeMode());
                    attPersonSchBO.setOvertimeRemark(attShiftItem.getOvertimeSign());
                    attPersonSchBO.setWorkType(attShiftItem.getWorkType());
                    attPersonSchBO.setShiftType(attShiftItem.getShiftType());
                    // 设置班次时间段
                    List<String> timeSlotIdList =
                        AttShiftSchUtils.getTimeSlotByShiftAndDate(attShiftItem, date, attCycleSchItem.getStartDate());
                    List<AttTimeSlotBO> attTimeSlotBOList = new ArrayList<>();
                    if (CollectionUtil.isEmpty(timeSlotIdList)) {
                        attPersonSchBO.setAttendStatus(AttCalculationConstant.AttAttendStatus.SCHEDULING_AND_REST);
                    }
                    for (String timeSlotId : timeSlotIdList) {
                        AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(timeSlotId);
                        AttTimeSlotBO attTimeSlotBO = new AttTimeSlotBO();
                        attTimeSlotBO.setAttTimeSlotId(timeSlotId);
                        // 设置时段是否垮天
                        AttCommonUtils.setSetFirstAndSecondDay(attTimeSlotBO, attTimeSlotItem, dateStr, crossDay);
                        attTimeSlotBOList.add(attTimeSlotBO);
                    }
                    AttShiftSchUtils.sortTimeSlot(attTimeSlotBOList, attTimeSlotItemMap);
                    attPersonSchBO.setAttTimeSlotArray(attTimeSlotBOList);
                    attPersonSchBOList.add(attPersonSchBO);
                }

                // 填充<ID=日期, 班次 (时间段)>
                String key = "";
                if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(attCycleSchItem.getCycleType())) {
                    key = attCycleSchItem.getDeptId() + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                    deptCycleSchMap.put(key, attPersonSchBOList);
                } else if (AttCommonSchConstant.SCH_TYPE_GROUP.equals(attCycleSchItem.getCycleType())) {
                    key = attCycleSchItem.getGroupId() + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                    groupCycleSchMap.put(key, attPersonSchBOList);
                } else if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(attCycleSchItem.getCycleType())) {
                    key = attCycleSchItem.getPersonPin() + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                    personCycleSchMap.put(key, attPersonSchBOList);
                }

            });
        }

    }

    /**
     * 查询填充临时排班
     * 
     * @param startDate
     * @param endDate
     * @param attTimeSlotItemMap
     * @param crossDay
     * @param deptTempSchMap
     * @param groupTempSchMap
     * @param personTempSchMap
     * @param attTempSchItemList
     */
    private void fillTempSchMap(Date startDate, Date endDate, Map<String, AttTimeSlotItem> attTimeSlotItemMap,
        String crossDay, Map<String, List<AttPersonSchBO>> deptTempSchMap,
        Map<String, List<AttPersonSchBO>> groupTempSchMap, Map<String, List<AttPersonSchBO>> personTempSchMap,
        List<AttTempSchItem> attTempSchItemList) {

        for (AttTempSchItem attTempSchItem : attTempSchItemList) {
            Date schStartDate = attTempSchItem.getStartDate();
            Date schEndDate = attTempSchItem.getEndDate();
            // 开始日期 = max(排班开始日期，计算开始日期)
            Date start = startDate.after(schStartDate) ? startDate : schStartDate;
            // 结束日期 = min(计算结束日期，排班结束日期)
            Date end = (null == schEndDate || endDate.before(schEndDate)) ? endDate : schEndDate;

            // 循环拆分排班成按天填充<ID=日期,班次(时间段)>;
            AttDateUtils.forEachDay(start, end, date -> {
                String dateStr = AttDateUtils.dateToStrAsShort(date);

                AttPersonSchBO attPersonSchBO = new AttPersonSchBO();
                attPersonSchBO.setAttendStatus(AttCalculationConstant.AttAttendStatus.ACTUAL);
                attPersonSchBO.setAttendanceMode(attTempSchItem.getAttendanceMode());
                attPersonSchBO.setOvertimeMode(attTempSchItem.getOvertimeMode());
                attPersonSchBO.setOvertimeRemark(attTempSchItem.getOvertimeRemark());
                attPersonSchBO.setWorkType(attTempSchItem.getWorkType());
                attPersonSchBO.setShiftType(AttShiftConstant.ShiftType.REGULAR_SHIFT);
                // 设置时间段
                List<AttTimeSlotBO> attTimeSlotBOList = new ArrayList<>();
                String timeSlotIds = attTempSchItem.getTimeSlotIds();
                if (StringUtils.isNotBlank(timeSlotIds)) {
                    String[] timeSlotIdArr = timeSlotIds.split(",");
                    for (String timeSlotId : timeSlotIdArr) {
                        AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(timeSlotId);
                        AttTimeSlotBO attTimeSlotBO = new AttTimeSlotBO();
                        attTimeSlotBO.setAttTimeSlotId(attTimeSlotItem.getId());
                        // 设置时段是否垮天
                        AttCommonUtils.setSetFirstAndSecondDay(attTimeSlotBO, attTimeSlotItem, dateStr, crossDay);
                        attTimeSlotBOList.add(attTimeSlotBO);
                    }
                }
                AttShiftSchUtils.sortTimeSlot(attTimeSlotBOList, attTimeSlotItemMap);
                attPersonSchBO.setAttTimeSlotArray(attTimeSlotBOList);
                List<AttPersonSchBO> attPersonSchBOList = new ArrayList<>();
                attPersonSchBOList.add(attPersonSchBO);

                // 填充<ID=日期, 班次 (时间段)>
                String key = "";
                if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(attTempSchItem.getTempType())) {
                    key = attTempSchItem.getDeptId() + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                    deptTempSchMap.put(key, attPersonSchBOList);
                } else if (AttCommonSchConstant.SCH_TYPE_GROUP.equals(attTempSchItem.getTempType())) {
                    key = attTempSchItem.getGroupId() + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                    groupTempSchMap.put(key, attPersonSchBOList);
                } else if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(attTempSchItem.getTempType())) {
                    key = attTempSchItem.getPersonPin() + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                    personTempSchMap.put(key, attPersonSchBOList);
                }

            });
        }
    }

    @Override
    public Map<String, List<AttPersonSchBO>> getPersonAllSchData(List<String> pins, Date startDate, Date endDate) {

        Map<String, List<AttPersonSchBO>> schDataMap = getPersonSchData(pins, startDate, endDate);

        // 获取调休补班（PIN_日期,调休补班）
        Map<String, List<AttPersonSchBO>> attAdjustMap = attAdjustService.getAdjustMap(pins, startDate, endDate);

        // 获取调班（PIN_日期,调班）
        Map<String, List<AttPersonSchBO>> attClassMap = attClassService.getClassMap(pins, startDate, endDate);

        // 获取所有节假日集合
        Set<String> holidaySet = attHolidayService.getHolidayDateSet(startDate, endDate);

        buildPersonSchExData(schDataMap, attAdjustMap, attClassMap, holidaySet);
        return schDataMap;
    }

    @Override
    public void buildPersonSchExData(Map<String, List<AttPersonSchBO>> schDataMap,
        Map<String, List<AttPersonSchBO>> attAdjustMap, Map<String, List<AttPersonSchBO>> attClassMap,
        Set<String> holidaySet) {

        for (Map.Entry<String, List<AttPersonSchBO>> entry : schDataMap.entrySet()) {
            String personSchKey = entry.getKey();
            // 填充调休补班
            if (Objects.nonNull(attAdjustMap) && attAdjustMap.containsKey(personSchKey)) {
                List<AttPersonSchBO> attPersonSchBOList = attAdjustMap.get(personSchKey);
                schDataMap.put(personSchKey, deepClone(attPersonSchBOList));
            }
            // 填充调班
            if (Objects.nonNull(attClassMap) && attClassMap.containsKey(personSchKey)) {
                List<AttPersonSchBO> attPersonSchBOList = attClassMap.get(personSchKey);
                schDataMap.put(personSchKey, deepClone(attPersonSchBOList));
            }
            // 节假日，如果班次工作类型不是节假日加班，替换班次为空
            String personSchKeyDate = personSchKey.split(AttCalculationConstant.KEY_CONNECTOR)[1];
            if (Objects.nonNull(holidaySet) && holidaySet.contains(personSchKeyDate)) {
                List<AttPersonSchBO> attPersonSchBOList = schDataMap.get(personSchKey);
                for (AttPersonSchBO attPersonSchBO : attPersonSchBOList) {
                    if (!AttShiftConstant.SHIFT_WORKTYPE_HOLIDAYOT.equals(attPersonSchBO.getWorkType())) {
                        attPersonSchBO.setAttendStatus(AttCalculationConstant.AttAttendStatus.HOLIDAY);
                        attPersonSchBO.setAttTimeSlotArray(new ArrayList<>());
                        attPersonSchBO.setAttShiftId(null);
                    }
                }
                schDataMap.put(personSchKey, deepClone(attPersonSchBOList));
            }
        }
    }

    @Override
    public ZKResultMsg getTodayWorkTime(String pin, String attDate) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        Date starDate = DateUtil.getDayBeginTime(AttDateUtils.stringToYmdDate(attDate));
        Date endDate = DateUtil.getDayEndTime(AttDateUtils.stringToYmdDate(attDate));

        AppAttWorkTimeItem appAttWorkTimeItem = null;

        // 查询考勤计算结果
        AttRecordItem condition = new AttRecordItem();
        condition.setEquals(true);
        condition.setPersonPin(pin);
        condition.setAttDate(starDate);
        List<AttRecordItem> recordItems = attRecordService.getByCondition(condition);
        AttRecordItem attRecordItem = null;
        if (!CollectionUtil.isEmpty(recordItems)) {
            attRecordItem = recordItems.get(0);
        }

        if (null != attRecordItem) {

            String shiftTimeData = attRecordItem.getShiftTimeData();
            if (StringUtils.isNotBlank(shiftTimeData)) {
                String[] timeSlot = shiftTimeData.split(";");
                String timeSlot0 = timeSlot[0];
                String[] timeSlotTime = timeSlot0.split("-");
                String startWorkTime = timeSlotTime[0];
                String endWorkTime =
                    timeSlot.length == 1 ? timeSlotTime[1] : (timeSlot[timeSlot.length - 1].split("-")[1]);

                String firstDay = attDate;
                String secondDay = attDate;
                if (startWorkTime.compareTo(endWorkTime) > 0) {
                    // 跨天
                    AttRuleParamBean attRuleParamBean = attParamService.getRuleParam();
                    String crossDay = attRuleParamBean.getCrossDay();
                    if (AttRuleEnum.CrossDay.getValueOne().equals(crossDay)) {
                        // 跨天算第一天
                        secondDay =
                            AttDateUtils.dateToStrAsShort(DateUtil.addDay(AttDateUtils.stringToYmdDate(attDate), 1));
                    } else {
                        // 跨天算第二天
                        firstDay =
                            AttDateUtils.dateToStrAsShort(DateUtil.addDay(AttDateUtils.stringToYmdDate(attDate), -1));
                    }
                }

                startWorkTime = String.format("%s %s:00", firstDay, startWorkTime);
                endWorkTime = String.format("%s %s:00", secondDay, endWorkTime);

                appAttWorkTimeItem = new AppAttWorkTimeItem();
                appAttWorkTimeItem.setStartWorkTime(startWorkTime);
                appAttWorkTimeItem.setEndWorkTime(endWorkTime);
                if (Objects.nonNull(attRecordItem.getShouldMinute()) && attRecordItem.getShouldMinute() > 0) {
                    appAttWorkTimeItem.setTimeLong(
                            attParamService.minutesToHourFormat(BigDecimal.valueOf(attRecordItem.getShouldMinute())));
                } else {
                    appAttWorkTimeItem.setTimeLong("0");
                }
                appAttWorkTimeItem.setStartWorkStatus("0");
                appAttWorkTimeItem.setEndWorkStatus("0");
            }

        } else {

            // 获取人员当日排班
            Map<String, List<AttPersonSchBO>> personSchData =
                getPersonAllSchData(Collections.singletonList(pin), starDate, endDate);

            List<AttTimeSlotItem> allTimeSlotItem = attTimeSlotService.getAllTimeSlotItem();
            Map<String, AttTimeSlotItem> attTimeSlotItemMap = CollectionUtil.itemListToIdMap(allTimeSlotItem);

            List<AttPersonSchBO> attPersonSchBOList = personSchData.get(pin + "=" + attDate);
            if (!CollectionUtil.isEmpty(attPersonSchBOList)) {
                // 获取排班时段
                AttPersonSchBO attPersonSchBO = attPersonSchBOList.get(0);
                List<AttTimeSlotBO> attTimeSlotArray = attPersonSchBO.getAttTimeSlotArray();
                if (!CollectionUtil.isEmpty(attTimeSlotArray)) {
                    String startWorkTime = null;
                    String endWorkTime = null;
                    BigDecimal timeLong = new BigDecimal(0);
                    for (AttTimeSlotBO attTimeSlotBO : attTimeSlotArray) {
                        AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());
                        if (StringUtils.isBlank(startWorkTime)) {
                            startWorkTime =
                                String.format("%s %s:00", attTimeSlotBO.getFirstDay(), attTimeSlotItem.getToWorkTime());
                            if (AttShiftConstant.ShiftType.FLEXIBLE_SHIFT == attTimeSlotItem.getPeriodType()) {
                                startWorkTime = String.format("%s %s:00", attTimeSlotBO.getFirstDay(),
                                    attTimeSlotItem.getStartSignInTime());
                            }
                        }
                        endWorkTime =
                            String.format("%s %s:00", attTimeSlotBO.getSecondDay(), attTimeSlotItem.getOffWorkTime());
                        if (AttShiftConstant.ShiftType.FLEXIBLE_SHIFT == attTimeSlotItem.getPeriodType()) {
                            endWorkTime = String.format("%s %s:00", attTimeSlotBO.getSecondDay(),
                                attTimeSlotItem.getEndSignOffTime());
                        }
                        Short workingHours = attTimeSlotItem.getWorkingHours();
                        timeLong = timeLong.add(BigDecimal.valueOf(workingHours));
                    }
                    appAttWorkTimeItem = new AppAttWorkTimeItem();
                    appAttWorkTimeItem.setStartWorkTime(startWorkTime);
                    appAttWorkTimeItem.setEndWorkTime(endWorkTime);
                    appAttWorkTimeItem.setTimeLong(attParamService.minutesToHourFormat(timeLong));
                    appAttWorkTimeItem.setStartWorkStatus("0");
                    appAttWorkTimeItem.setEndWorkStatus("0");
                }
            }
        }

        // 人员未排班返回缺省值
        if (Objects.isNull(appAttWorkTimeItem)) {
            appAttWorkTimeItem = new AppAttWorkTimeItem();
            appAttWorkTimeItem.setStartWorkTime(AttDateUtils.dateToStrAsLong(starDate));
            appAttWorkTimeItem.setEndWorkTime(AttDateUtils.dateToStrAsLong(endDate));
            appAttWorkTimeItem.setTimeLong("0");
            appAttWorkTimeItem.setStartWorkStatus("0");
            appAttWorkTimeItem.setEndWorkStatus("0");
        }
        zkResultMsg.setData(appAttWorkTimeItem);
        return zkResultMsg;
    }

}
