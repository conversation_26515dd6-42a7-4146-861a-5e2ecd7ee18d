package com.zkteco.zkbiosecurity.att.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.cache.AttCacheManager;
import com.zkteco.zkbiosecurity.att.dao.AttPointDao;
import com.zkteco.zkbiosecurity.att.service.AttPoint4OtherService;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;

/**
 * 考勤考勤点对接其他模块接口
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 14:44 2021/9/8
 * @version v1.0
 */
@Service
public class AttPoint4OtherServiceImpl implements AttPoint4OtherService {

    @Autowired
    private AttPointDao attPointDao;
    @Autowired
    private AttCacheManager attCacheManager;

    @Override
    public boolean isUsedByDeviceId(String module, String deviceId) {
        if (ConstUtil.SYSTEM_MODULE_PARK.equals(module)) {
            String parkPullTransactionIn = attCacheManager.getPointPullTransaction(module + ":in");
            boolean isUsed = isUsed(parkPullTransactionIn, deviceId);
            if (!isUsed) {
                String parkPullTransactionInOut = attCacheManager.getPointPullTransaction(module + ":out");
                isUsed = isUsed(parkPullTransactionInOut, deviceId);
            }
            return isUsed;
        } else {
            String pointPullTransaction = attCacheManager.getPointPullTransaction(module);
            return isUsed(pointPullTransaction, deviceId);
        }
    }

    /**
     * 判断是否存在
     */
    private boolean isUsed(String pointPullTransaction, String deviceId) {
        if (StringUtils.isNotBlank(pointPullTransaction)) {
            JSONObject jsonObject = JSONObject.parseObject(pointPullTransaction);
            if (jsonObject.containsKey("deviceIds")) {
                String deviceIds = jsonObject.getString("deviceIds");
                if (StringUtils.isNotBlank(deviceIds)) {
                    return deviceIds.contains(deviceId);
                }
            }
        }
        return false;
    }

    @Override
    public boolean isUsedByDeviceSn(String deviceSn) {
        if (StringUtils.isNotBlank(deviceSn)) {
            return attPointDao.existsByDeviceSn(deviceSn);
        }
        return false;
    }
}
