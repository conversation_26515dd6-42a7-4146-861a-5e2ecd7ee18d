package com.zkteco.zkbiosecurity.att.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 外出
 */
@Deprecated
@Entity
@Table(name = "ATT_OUT")
@Setter
@Getter
@Accessors(chain = true)
public class AttOut extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门id
     */
    @Column(name = "AUTH_DEPT_ID", length = 50)
    private String deptId;

    /**
     * 人员id
     */
    @Column(name = "PERS_PERSON_ID", length = 50)
    private String personId;

    /**
     * 人员编号
     */
    @Column(name = "PERS_PERSON_PIN", length = 50)
    private String personPin;

    /**
     * 外出时长（分）
     */
    @Column(name = "OUT_LONG")
    private Integer outLong;

    /**
     * 开始日期时间
     */
    @Column(name = "START_DATETIME")
    private Date startDatetime;

    /**
     * 结束日期时间
     */
    @Column(name = "END_DATETIME")
    private Date endDatetime;

    /**
     * 备注
     */
    @Column(name = "REMARK")
    private String remark;

    /**
     * 流程业务ID,全局唯一
     */
    @Column(name = "BUSINESS_KEY", length = 50)
    private String businessKey;

    @Column(name = "FLOW_STATUS")
    private String flowStatus;

}
