package com.zkteco.zkbiosecurity.att.service.impl;

import java.math.BigDecimal;
import java.util.*;

import com.zkteco.zkbiosecurity.att.bean.AttPersPersonInfoBean;
import com.zkteco.zkbiosecurity.att.constants.AttShiftConstant;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.cache.AttCalculationCacheManager;
import com.zkteco.zkbiosecurity.att.calc.bo.AttOvertimeBO;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttOvertimeDao;
import com.zkteco.zkbiosecurity.att.model.AttOvertime;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttOvertimeItem;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.service.PersLeavePersonService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 加班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:30
 * @since 1.0.0
 */
@Service
public class AttOvertimeServiceImpl implements AttOvertimeService {

    @Autowired
    private AttOvertimeDao attOvertimeDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private AttCalculationCacheManager attCalculationCacheManager;
    @Autowired
    private AttLeaveService attLeaveService;
    @Autowired
    private AttWorkflowService attWorkflowService;
    @Autowired
    private AttMessageCenterService attMessageCenterService;
    @Autowired
    private AuthUserService authUserService;

    @Override
    @Transactional
    public AttOvertimeItem saveItem(AttOvertimeItem item, String personIds, String sessionId) {

        // 是否有审批权限
        boolean hasApproval = attLeaveService.checkPermission(sessionId, "att:overtime:approval");

        List<PersPersonItem> persPersonList = persPersonService.getSimpleItemsByIds(StrUtil.strToList(personIds));
        List<AttOvertime> attOvertimeList = new ArrayList<>();
        for (PersPersonItem persPerson : persPersonList) {
            AttOvertime attOvertime = new AttOvertime();
            ModelUtil.copyPropertiesIgnoreNull(item, attOvertime);
            attOvertime.setPersonId(persPerson.getId());
            attOvertime.setPersonPin(persPerson.getPin());
            attOvertime.setDeptId(persPerson.getDeptId());

            // 如果有审批权限，直接标记状态为已完成
            attOvertime.setFlowStatus(hasApproval ? AttConstant.FLOW_STATUS_COMPLETE : AttConstant.FLOW_STATUS_CREATE);
            attOvertimeList.add(attOvertime);
        }
        attOvertimeDao.saveAll(attOvertimeList);

        // 【实时计算】保存加班记录到缓存，新增实时计算事件
        if (hasApproval) {
            String crossDay = attParamService.getCrossDay();
            for (AttOvertime attOvertime : attOvertimeList) {
                setOvertimeAddEventCache(attOvertime, crossDay);
            }
        }

        return item;
    }

    @Override
    public List<AttOvertimeItem> getByCondition(AttOvertimeItem condition) {
        return (List<AttOvertimeItem>)attOvertimeDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        Pager pager = attOvertimeDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        buildItem((List<AttOvertimeItem>)pager.getData());
        return pager;
    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, AttOvertimeItem condition, int pageNo, int pageSize) {
        buildCondition(sessionId, condition);
        Pager pager =
            attOvertimeDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        buildItem((List<AttOvertimeItem>)pager.getData());
        return pager;
    }

    private void buildCondition(String sessionId, AttOvertimeItem condition) {

        // 使用员工自助登录的用户,只有自己数据的权限。若登录类型为pers,则返回pin加入查询sql
        String persperPin = attPersonService.getPinByLoginType(sessionId);
        if (persperPin != null) {
            condition.setPersonPinEq(persperPin);
        } else {
            // 部门权限过滤
            String userId = authUserService.getUserIdBySessionId(sessionId);
            if (StringUtils.isNotBlank(userId)) {
                condition.setUserId(userId);
            }
            // 前端是否包含上下级
            if (ZKConstant.TRUE.equals(condition.getIsIncludeLower())) {
                if (StringUtils.isBlank(condition.getInDeptId())) {
                    if (StringUtils.isNotBlank(condition.getDeptId())) {
                        List<String> deptIdList = authDepartmentService.getChildDeptIdsByDeptIds(condition.getDeptId());
                        condition.setInDeptId(StrUtil.collectionToStr(deptIdList));
                        condition.setDeptId(null);
                    }
                }
            }
            // 人员姓名模糊查询（包含离职人员）
            String pins = attPersonService.getPinsByLikeName(sessionId, condition.getLikeName());
            if (StringUtils.isNotBlank(pins)) {
                condition.setInPersonPin(pins);
            }
        }
    }

    /**
     * 数据填充 离职人员姓名填充
     */
    private void buildItem(List<AttOvertimeItem> itemList) {
        List<List<AttOvertimeItem>> splitItemList = CollectionUtil.split(itemList, CollectionUtil.splitSize);
        for (List<AttOvertimeItem> splitItems : splitItemList) {

            Collection<String> personPinList = CollectionUtil.getPropertyList(splitItems, AttOvertimeItem::getPersonPin, AttConstant.COMM_DEF_VALUE);
            Map<String, AttPersPersonInfoBean> persPersonInfoMap = attPersonService.getPersonInfoByPinList(personPinList);

            for (AttOvertimeItem item : splitItems) {
                String personPin = item.getPersonPin();
                AttPersPersonInfoBean persPersonInfoBean = persPersonInfoMap.get(personPin);
                if (Objects.nonNull(persPersonInfoBean)) {
                    item.setPersonName(persPersonInfoBean.getPersonName());
                    item.setPersonLastName(persPersonInfoBean.getPersonLastName());
                }
                if (Objects.nonNull(item.getOvertimeLong())) {
                    float overTimeLongHour = ((float)item.getOvertimeLong()) / 60;
                    double result =
                            new BigDecimal(overTimeLongHour).setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
                    item.setOverTimeHour(String.valueOf(result));
                }
            }
        }
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            List<AttOvertime> attOvertimeList = attOvertimeDao.findByIdList(CollectionUtil.strToList(ids));
            String crossDay = attParamService.getCrossDay();
            for (AttOvertime attOvertime : attOvertimeList) {
                attOvertimeDao.delete(attOvertime);
                if (StringUtils.isNotBlank(attOvertime.getBusinessKey())) {
                    attWorkflowService.deleteProcessInstance(attOvertime.getBusinessKey());
                }
                // 【实时计算】删除缓存的加班,新增实时计算事件
                if (AttConstant.FLOW_STATUS_COMPLETE.equals(attOvertime.getFlowStatus())) {
                    delOvertimeAddEventCache(attOvertime, crossDay);
                }
            }
        }
        return false;
    }

    @Override
    public AttOvertimeItem getItemById(String id) {
        List<AttOvertimeItem> items = getByCondition(new AttOvertimeItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public List<AttOvertimeItem> getItemData(String sessionId, AttOvertimeItem condition, int beginIndex,
        int endIndex) {
        buildCondition(sessionId, condition);
        List<AttOvertimeItem> itemList = attOvertimeDao.getItemsDataBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition), beginIndex, endIndex, true);
        buildItem(itemList);
        return itemList;
    }

    @Override
    public boolean existOvertime(List<String> personIdList, Date startTime, Date endTime) {
        List<AttOvertime> attOvertimeList =
            attOvertimeDao.getByPersonIdsAndStartEndTime(personIdList, startTime, endTime);
        if (attOvertimeList != null && attOvertimeList.size() > 0) {
            return true;
        }
        return false;
    }

    @Override
    public boolean existOvertime(String pin, Date startTime, Date endTime) {
        List<AttOvertime> attOvertimeList = attOvertimeDao.getByPinAndStartEndTime(pin, startTime, endTime);
        return attOvertimeList != null && attOvertimeList.size() > 0;
    }

    @Override
    @Transactional
    public void handlerTransfer(List<AttOvertimeItem> attOvertimeItems) {
        // 数据量大的时候 分批处理 jinxian.huang 2019-07-31
        List<List<AttOvertimeItem>> splitAttOvertimeItemList =
            CollectionUtil.split(attOvertimeItems, CollectionUtil.splitSize);
        for (List<AttOvertimeItem> splitAttOvertimeItems : splitAttOvertimeItemList) {
            // 找出pin号 根据pin号把人员信息补充完整 modified by jinxian.huang 2019-07-29
            Collection<String> pinList = CollectionUtil.getPropertyList(splitAttOvertimeItems,
                AttOvertimeItem::getPersonPin, AttConstant.COMM_DEF_VALUE);
            List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(pinList);
            Map<String, PersPersonItem> persPersonItemMap =
                CollectionUtil.listToKeyMap(persPersonItemList, PersPersonItem::getPin);
            List<AttOvertime> attOvertimes = new ArrayList<>();
            for (AttOvertimeItem attOvertimeItem : splitAttOvertimeItems) {
                String personPin = attOvertimeItem.getPersonPin();
                Date startDatetime = attOvertimeItem.getStartDatetime();
                Date endDatetime = attOvertimeItem.getEndDatetime();
                AttOvertime attOvertime =
                    attOvertimeDao.getByPersonPinAndStartEndTime(personPin, startDatetime, endDatetime);
                if (Objects.isNull(attOvertime)) {
                    attOvertime = new AttOvertime();
                    if (persPersonItemMap.containsKey(personPin)) {
                        PersPersonItem persPersonItem = persPersonItemMap.get(personPin);
                        if (Objects.nonNull(persPersonItem)) {
                            attOvertime.setDeptId(persPersonItem.getDeptId());
                            attOvertime.setPersonId(persPersonItem.getId());
                        }
                    }
                    // 流程状态默认设置为2——已完成
                    attOvertime.setFlowStatus("2");
                    attOvertime.setCreateTime(attOvertimeItem.getOperateDatetime());
                }
                ModelUtil.copyPropertiesIgnoreNullWithProperties(attOvertimeItem, attOvertime, "id");
                attOvertimes.add(attOvertime);
            }
            attOvertimeDao.saveAll(attOvertimes);
        }
    }

    @Override
    @Transactional
    public AttOvertimeItem saveItem(AttOvertimeItem attOvertimeItem) {
        PersPersonItem persPerson = persPersonService.getItemById(attOvertimeItem.getPersonId());
        AttOvertime attOvertime = new AttOvertime();
        ModelUtil.copyProperties(attOvertimeItem, attOvertime);
        attOvertime.setPersonId(persPerson.getId());
        attOvertime.setPersonPin(persPerson.getPin());
        attOvertime.setDeptId(persPerson.getDeptId());
        attOvertimeDao.save(attOvertime);
        return attOvertimeItem.setId(attOvertime.getId());
    }

    @Override
    @Transactional
    public void updateFlowStatus(String businessKey, String status) {
        AttOvertime attOvertime = attOvertimeDao.findByBusinessKey(businessKey);

        // 【实时计算】 根据异常状态判断是否需要更新缓存和进行考勤实时计算
        boolean isCalculation = attCalculationCacheManager.judgeStatusCalculation(attOvertime.getFlowStatus(), status);

        attOvertime.setFlowStatus(status);
        attOvertimeDao.save(attOvertime);

        // 【实时计算】保存\删除缓存的加班、新增实时计算事件
        if (isCalculation) {
            String crossDay = attParamService.getCrossDay();
            if (AttConstant.FLOW_STATUS_COMPLETE.equals(attOvertime.getFlowStatus())) {
                // 保存请假记录到缓存，新增实时计算事件
                setOvertimeAddEventCache(attOvertime, crossDay);
            } else {
                // 删除缓存加班记录，新增实时计算事件
                delOvertimeAddEventCache(attOvertime, crossDay);
            }
        }
    }

    @Override
    public AttOvertimeItem getByBusinessKey(String businessKey) {
        AttOvertime attOvertime = attOvertimeDao.findByBusinessKey(businessKey);
        if (attOvertime != null) {
            AttOvertimeItem attOvertimeItem = ModelUtil.copyPropertiesIgnoreNull(attOvertime, new AttOvertimeItem());
            PersPersonItem persPersonItem = persPersonService.getItemById(attOvertime.getPersonId());
            attOvertimeItem.setOperateDatetime(attOvertime.getCreateTime());
            if (persPersonItem != null) {
                // 设置人员信息
                attOvertimeItem.setPersonPin(persPersonItem.getPin());
                attOvertimeItem.setPersonName(persPersonItem.getName());
                attOvertimeItem.setPersonLastName(persPersonItem.getLastName());
                // 分钟转小时,用作前端展示 add by bob.liu 20190813
                String hours = attParamService.minutesToHourFormat(new BigDecimal(attOvertimeItem.getOvertimeLong()));
                attOvertimeItem.setOverTimeHour(hours);
            }
            return attOvertimeItem;
        }
        return null;
    }

    @Override
    @Transactional
    public void updateBusinessKeyById(String id, String businessKey) {
        AttOvertime attOvertime = attOvertimeDao.findById(id).orElse(null);
        if (attOvertime != null) {
            attOvertime.setBusinessKey(businessKey);
        }
    }

    @Override
    @Transactional
    public void upgradeTo_220() {
        List<AttOvertime> attOverTimeList = attOvertimeDao.findAll();
        List<List<AttOvertime>> splitAttOvertimeList = CollectionUtil.split(attOverTimeList, CollectionUtil.splitSize);
        for (List<AttOvertime> attOvertimes : splitAttOvertimeList) {
            if (!CollectionUtil.isEmpty(attOvertimes)) {
                for (AttOvertime attOvertime : attOvertimes) {
                    long overTimeLong =
                        (attOvertime.getEndDatetime().getTime() - attOvertime.getStartDatetime().getTime()) / 1000 / 60;
                    attOvertime.setOvertimeLong((int)overTimeLong);
                }
                attOvertimeDao.saveAll(attOvertimes);
            }
        }
    }

    @Override
    @Transactional
    public ZKResultMsg importExcel(List<AttOvertimeItem> itemList, String sessionId) {

        if (itemList.size() > 30000) {
            // 一次性导入只能30000，超过要分批次导入
            throw ZKBusinessException.warnException("att_import_overData", itemList.size());
        }
        int importSize = itemList.size();
        int beginProgress = 20;
        // 导入的人员要求人事已存在
        Map<String, PersPersonItem> existPersPersonMap = new HashMap<>();
        // 取出待导入数据中人员的pin号
        Collection<String> importPins = CollectionUtil.getPropertyList(itemList, AttOvertimeItem::getPersonPin, "-1");
        // 分批处理，一次处理800人
        List<List<String>> pinsList = CollectionUtil.split(importPins, CollectionUtil.splitSize);
        for (List<String> pins : pinsList) {
            // 根据pin号查出人事人员
            List<PersPersonItem> existPersonList = persPersonService.getItemsByPins(pins);
            if (existPersonList != null && existPersonList.size() > 0) {
                existPersPersonMap.putAll(CollectionUtil.listToKeyMap(existPersonList, PersPersonItem::getPin));
            }
        }
        /*// 获取考勤规则
        Map<String, String> attParams = attRuleService.getAttParams();
        String shortOvertimeMinutes = attParams.get("att.baseRule.shortestOvertimeMinutes");*/
        Iterator<AttOvertimeItem> itemIterator = itemList.iterator();
        // 先剔除无效数据
        while (itemIterator.hasNext()) {
            AttOvertimeItem item = itemIterator.next();
            // 人员编号校验
            if (StringUtils.isBlank(item.getPersonPin())) {
                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                    ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                        I18nUtil.i18nCode("pers_import_pinNotEmpty")))));
                itemIterator.remove();
                continue;
            }
            PersPersonItem persPersonItem = existPersPersonMap.get(item.getPersonPin());
            if (Objects.isNull(persPersonItem)) {
                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                    ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                        I18nUtil.i18nCode("att_areaPerson_persNoExit")))));
                itemIterator.remove();
                continue;
            }
            // 开始时间校验
            if (Objects.isNull(item.getStartDatetime())) {
                progressCache
                    .setProcess(new ProcessBean(beginProgress, beginProgress, ProcessBean.createErrorContent(I18nUtil
                        .i18nCode("pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("att_leave_startNotNull")))));
                itemIterator.remove();
                continue;
            }
            // 结束时间校验
            if (Objects.isNull(item.getEndDatetime())) {
                progressCache
                    .setProcess(new ProcessBean(beginProgress, beginProgress, ProcessBean.createErrorContent(I18nUtil
                        .i18nCode("pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("att_leave_endNotNull")))));
                itemIterator.remove();
                continue;
            }
            // 结束时间不能小于等于开始时间
            if (item.getEndDatetime().getTime() <= item.getStartDatetime().getTime()) {
                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                    ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                        I18nUtil.i18nCode("att_leave_endNoLessAndEqualStart")))));
                itemIterator.remove();
                continue;
            }

            // 请假时间重复判断
            if (attLeaveService.existLeave(item.getPersonPin(), item.getStartDatetime(), item.getEndDatetime())) {
                progressCache
                    .setProcess(new ProcessBean(beginProgress, beginProgress, ProcessBean.createErrorContent(I18nUtil
                        .i18nCode("pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("att_apply_leaveTips")))));
                itemIterator.remove();
                continue;
            }

            // 加班时间重复判断
            if (existOvertime(item.getPersonPin(), item.getStartDatetime(), item.getEndDatetime())) {
                progressCache
                    .setProcess(new ProcessBean(beginProgress, beginProgress, ProcessBean.createErrorContent(I18nUtil
                        .i18nCode("pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("att_apply_overtimeTips")))));
                itemIterator.remove();
                continue;
            }
        }

        // 是否有审批权限
        boolean hasApproval = authPermissionService.checkPermission(sessionId, "att:overtime:approval");

        // 剩下的可以插入数据库,分批处理，一次处理800条
        List<List<AttOvertimeItem>> attOvertimeInsertList = CollectionUtil.split(itemList, CollectionUtil.splitSize);
        for (List<AttOvertimeItem> insertItemList : attOvertimeInsertList) {
            // 保存入库
            List<AttOvertime> attOvertimeList = new ArrayList<>();
            for (AttOvertimeItem item : insertItemList) {
                PersPersonItem persPersonItem = existPersPersonMap.get(item.getPersonPin());
                if (Objects.nonNull(persPersonItem)) {
                    AttOvertime attOvertime = new AttOvertime();
                    ModelUtil.copyPropertiesWithIgnore(item, attOvertime, "id");
                    // 加班时长
                    long second = (item.getEndDatetime().getTime() - item.getStartDatetime().getTime()) / 1000;
                    int attOverLongMinute = (int)(second / 60);
                    attOvertime.setOvertimeLong(attOverLongMinute);
                    attOvertime.setDeptId(persPersonItem.getDeptId());
                    attOvertime.setPersonId(persPersonItem.getId());
                    attOvertime.setOvertimeSign(AttShiftConstant.OvertimeSign.NORMAL);
                    // 如果有审批权限，直接标记状态为已完成
                    attOvertime
                        .setFlowStatus(hasApproval ? AttConstant.FLOW_STATUS_COMPLETE : AttConstant.FLOW_STATUS_CREATE);

                    attOvertimeList.add(attOvertime);
                }
            }
            attOvertimeDao.saveAll(attOvertimeList);

            // 【实时计算】保存加班记录到缓存，新增实时计算事件
            if (hasApproval) {
                String crossDay = attParamService.getCrossDay();
                for (AttOvertime attOvertime : attOvertimeList) {
                    setOvertimeAddEventCache(attOvertime, crossDay);
                }
            }
        }
        // 失败数量
        int faildCount = importSize - itemList.size();
        // 成功：%s 条，失败：%s 条。
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(99,
            I18nUtil.i18nCode("pers_import_result", itemList.size(), faildCount)));
        if (faildCount > 0) {
            return ZKResultMsg.failMsg();
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    @Transactional
    public void approval(String ids) {
        if (StringUtils.isNotBlank(ids)) {

            // 【实时计算】保存加班记录到缓存，新增实时计算事件
            List<AttOvertime> attOvertimeList = attOvertimeDao.findByIdList(CollectionUtil.strToList(ids));
            String crossDay = attParamService.getCrossDay();
            for (AttOvertime attOvertime : attOvertimeList) {
                if (!AttConstant.FLOW_STATUS_COMPLETE.equals(attOvertime.getFlowStatus())) {
                    setOvertimeAddEventCache(attOvertime, crossDay);
                }
            }

            attOvertimeDao.updateFlowStatus(Arrays.asList(ids.split(",")), AttConstant.FLOW_STATUS_COMPLETE);
        }
    }

    @Override
    @Transactional
    public void refuse(String ids) {
        if (StringUtils.isNotBlank(ids)) {

            // 【实时计算】删除缓存的加班,新增实时计算事件
            List<AttOvertime> attOvertimeList = attOvertimeDao.findByIdList(CollectionUtil.strToList(ids));
            String crossDay = attParamService.getCrossDay();
            for (AttOvertime attOvertime : attOvertimeList) {
                if (AttConstant.FLOW_STATUS_COMPLETE.equals(attOvertime.getFlowStatus())) {
                    delOvertimeAddEventCache(attOvertime, crossDay);
                }
            }

            attOvertimeDao.updateFlowStatus(Arrays.asList(ids.split(",")), AttConstant.FLOW_STATUS_REFUSE);
        }
    }

    @Override
    public Map<String, List<AttOvertimeBO>> getOvertimeMap(List<String> pins, Date startDate, Date endDate) {

        // 查询记录
        List<AttOvertime> attOvertimeList = new ArrayList<>();
        if (CollectionUtil.isEmpty(pins)) {
            attOvertimeList = attOvertimeDao.findOvertimeByDate(startDate, endDate);
        } else if (pins.size() == 1) {
            attOvertimeList = attOvertimeDao.findOvertimeByDateAndPin(startDate, endDate, pins.get(0));
        } else {
            List<List<String>> splitPinList = CollectionUtil.split(pins, CollectionUtil.splitSize);
            for (List<String> subPinList : splitPinList) {
                List<AttOvertime> sunOvertimeList =
                    attOvertimeDao.findOvertimeByDateAndPins(startDate, endDate, subPinList);
                attOvertimeList.addAll(sunOvertimeList);
            }
        }

        // 按 map(pin_date, List<AttOvertimeBO>) 填充
        Map<String, List<AttOvertimeBO>> attOvertimeMap = new HashMap<>();
        for (AttOvertime attOvertime : attOvertimeList) {
            buildOvertimeMap(attOvertime, attOvertimeMap);
        }

        return attOvertimeMap;
    }

    /**
     * 组装加班记录
     */
    private void buildOvertimeMap(AttOvertime attOvertime, Map<String, List<AttOvertimeBO>> attOvertimeMap) {
        // 加班申请不允许跨天申请,所以不用拆分天
        AttOvertimeBO attOvertimeBO = new AttOvertimeBO();
        attOvertimeBO.setId(attOvertime.getId());
        attOvertimeBO.setStartDateTime(AttDateUtils.dateToStrAsLong(attOvertime.getStartDatetime()));
        attOvertimeBO.setEndDateTime(AttDateUtils.dateToStrAsLong(attOvertime.getEndDatetime()));
        attOvertimeBO.setOvertimeSign(attOvertime.getOvertimeSign());
        String key = attOvertime.getPersonPin() + AttCalculationConstant.KEY_CONNECTOR
            + AttDateUtils.dateToStrAsShort(attOvertime.getStartDatetime());
        List<AttOvertimeBO> attOvertimeBOList = attOvertimeMap.get(key);
        if (null == attOvertimeBOList) {
            attOvertimeBOList = new ArrayList<>();
        }
        attOvertimeBOList.add(attOvertimeBO);
        attOvertimeMap.put(key, attOvertimeBOList);
    }

    /**
     * 【实时计算】保存加班记录到缓存，新增实时计算事件
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/31 11:29
     * @param attOvertime
     * @return void
     */
    private void setOvertimeAddEventCache(AttOvertime attOvertime, String crossDay) {

        // 发送消息
        AttOvertimeItem attOvertimeItem = new AttOvertimeItem();
        ModelUtil.copyProperties(attOvertime, attOvertimeItem);
        attMessageCenterService.pushOvertimeMessage(attOvertimeItem);

        if (attParamService.realTimeEnable()) {
            Map<String, List<AttOvertimeBO>> attOvertimeMap = new HashMap<>();
            buildOvertimeMap(attOvertime, attOvertimeMap);
            AttDateUtils.forEachDay(attOvertime.getStartDatetime(), attOvertime.getEndDatetime(), date -> {
                // 保存在时间范围内的数据
                if (attCalculationCacheManager.judgeCacheDate(date)) {
                    String key = attOvertime.getPersonPin() + "=" + AttDateUtils.dateToStrAsShort(date);
                    List<AttOvertimeBO> attOvertimeBOList = attOvertimeMap.get(key);
                    if (attOvertimeBOList != null && attOvertimeBOList.size() > 0) {
                        attCalculationCacheManager.setOvertime(key, attOvertimeBOList, true);
                        // 新增实时计算事件
                        attCalculationCacheManager.addRealTimeEvent(attOvertime.getPersonPin(), date, crossDay);
                    }
                }
            });
        }
    }

    /**
     * 【实时计算】删除缓存加班记录，新增实时计算事件
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/31 11:29
     * @param attOvertime
     * @return void
     */
    private void delOvertimeAddEventCache(AttOvertime attOvertime, String crossDay) {
        if (attParamService.realTimeEnable()) {
            AttDateUtils.forEachDay(attOvertime.getStartDatetime(), attOvertime.getEndDatetime(), date -> {
                if (attCalculationCacheManager.judgeCacheDate(date)) {
                    // 删除缓存加班记录
                    attCalculationCacheManager.delOvertime(attOvertime.getPersonPin(), attOvertime.getId(), date);
                    // 新增实时计算事件
                    attCalculationCacheManager.addRealTimeEvent(attOvertime.getPersonPin(), date, crossDay);
                }
            });
        }
    }

    @Override
    public boolean hasStaffApply(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            List<AttOvertime> list = attOvertimeDao.findByIdList(CollectionUtil.strToList(ids));
            if (!CollectionUtil.isEmpty(list)) {
                for (AttOvertime attOvertime : list) {
                    if (StringUtils.isNotBlank(attOvertime.getBusinessKey())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
}