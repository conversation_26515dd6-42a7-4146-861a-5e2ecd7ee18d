package com.zkteco.zkbiosecurity.att.service.impl;

import java.text.MessageFormat;
import java.util.*;

import com.zkteco.zkbiosecurity.att.bean.AttPersPersonInfoBean;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.cache.AttCalculationCacheManager;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttAdjustDao;
import com.zkteco.zkbiosecurity.att.dao.AttShiftDao;
import com.zkteco.zkbiosecurity.att.model.AttAdjust;
import com.zkteco.zkbiosecurity.att.model.AttShift;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.utils.AttShiftSchUtils;
import com.zkteco.zkbiosecurity.att.vo.AttAdjustItem;
import com.zkteco.zkbiosecurity.att.vo.AttShiftItem;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.service.PersLeavePersonService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersLeavePersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 调休补班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 16:07
 * @since 1.0.0
 */
@Service
public class AttAdjustServiceImpl implements AttAdjustService {

    @Autowired
    private AttAdjustDao attAdjustDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AttShiftDao attShiftDao;
    @Autowired
    private AttShiftService attShiftService;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private AttPersonSchDataService attPersonSchDataService;
    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private AttCalculationCacheManager attCalculationCacheManager;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttMessageCenterService attMessageCenterService;
    @Autowired
    private AuthUserService authUserService;

    @Override
    @Transactional
    public ZKResultMsg saveItem(AttAdjustItem item, String personIds, String sessionId) {

        // 查询人员信息
        List<PersPersonItem> persPersonList = persPersonService.getSimpleItemsByIds(StrUtil.strToList(personIds));

        // 根据排班判断是否进行申请
        String ret = allowApply(persPersonList, item.getAdjustType(), item.getAdjustDate(), item.getShiftId());
        if (StringUtils.isNotBlank(ret)) {
            return ZKResultMsg.failMsg(ret);
        }

        // 查询班次信息
        List<AttShift> attShifts = attShiftDao.findAll();
        Map<String, AttShift> attShiftMap = CollectionUtil.listToKeyMap(attShifts, AttShift::getId);

        // 是否有审批权限
        boolean hasApproval = authPermissionService.checkPermission(sessionId, "att:adjust:approval");

        // 遍历赋值并批量保存
        List<AttAdjust> attAdjustList = new ArrayList<>();
        for (PersPersonItem persPerson : persPersonList) {
            AttAdjust attAdjust = new AttAdjust();
            ModelUtil.copyPropertiesIgnoreNull(item, attAdjust);
            attAdjust.setPersonId(persPerson.getId());
            attAdjust.setPersonPin(persPerson.getPin());
            attAdjust.setDeptId(persPerson.getDeptId());

            if (AttConstant.ADJUST_TYPE_CLASSES == item.getAdjustType()) {
                attAdjust.setShiftId(item.getShiftId());
            }

            // 如果有审批权限，直接标记状态为已完成
            attAdjust.setFlowStatus(hasApproval ? AttConstant.FLOW_STATUS_COMPLETE : AttConstant.FLOW_STATUS_CREATE);
            attAdjustList.add(attAdjust);
        }
        attAdjustDao.saveAll(attAdjustList);

        // 【实时计算】保存调休记录到缓存，新增实时计算事件
        if (hasApproval) {
            String crossDay = attParamService.getCrossDay();
            for (AttAdjust attAdjust : attAdjustList) {
                setAdjustAddEventCache(attAdjust, attShiftMap, crossDay);
            }
        }

        return ZKResultMsg.successMsg();
    }

    /**
     * 根据排班判断是否进行申请
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/10 14:20
     * @param persPersonList
     * @param type
     * @param date
     * @return java.lang.String
     */
    private String allowApply(List<PersPersonItem> persPersonList, short type, Date date, String shiftId) {

        String pins = CollectionUtil.getPropertys(persPersonList, PersPersonItem::getPin);

        // 找出所有班次
        List<AttShiftItem> attShiftItemList = attShiftService.getByCondition(new AttShiftItem());
        Map<String, AttShiftItem> attShiftItemMap = CollectionUtil.listToKeyMap(attShiftItemList, AttShiftItem::getId);

        // 根据日期和pin号找出当前的排班信息
        Map<String, List<AttPersonSchBO>> attPersonSchMap =
            attPersonSchDataService.getPersonSchData(Arrays.asList(pins.split(",")), date, date);

        // 遍历判断是否可以进行调休补班操作
        for (PersPersonItem persPersonItem : persPersonList) {

            // 排班状态
            String isWork = AttCalculationConstant.AttAttendStatus.NO_SCHEDULING;
            String dateStr = AttDateUtils.dateToStrAsShort(date);
            String key = persPersonItem.getPin() + AttCalculationConstant.KEY_CONNECTOR + dateStr;
            List<AttPersonSchBO> attPersonSchBOList = attPersonSchMap.get(key);
            if (attPersonSchBOList != null && attPersonSchBOList.size() > 0) {
                for (AttPersonSchBO attPersonSchBO : attPersonSchBOList) {

                    // 判断是否排班
                    if (!AttCalculationConstant.AttAttendStatus.NO_SCHEDULING
                        .equals(attPersonSchBO.getAttendStatus())) {
                        if (!CollectionUtil.isEmpty(attPersonSchBO.getAttTimeSlotArray())) {
                            // 如果多个排班中只要一个符合时间段不为空，就返回(应到/实到)标记
                            isWork = AttCalculationConstant.AttAttendStatus.ACTUAL;
                            break;
                        } else {
                            // 有排班，但是班次没有选择该日期（排班且休息）
                            isWork = AttCalculationConstant.AttAttendStatus.SCHEDULING_AND_REST;
                        }
                    }
                }
            }

            String personNameAndPin = persPersonItem.getName() + "(" + persPersonItem.getPin() + ")";
            if (type == AttConstant.ADJUST_TYPE_OFF) {
                // 调休
                if (!AttCalculationConstant.AttAttendStatus.ACTUAL.equals(isWork)) {
                    // 不上班日期不允许调休申请
                    return MessageFormat.format(I18nUtil.i18nCode("att_approve_offDayConflicts"), personNameAndPin,
                        dateStr);
                }
            } else {
                // 补班
                if (AttCalculationConstant.AttAttendStatus.NO_SCHEDULING.equals(isWork)) {
                    // 未排班不允许补班申请！
                    return MessageFormat.format(I18nUtil.i18nCode("att_approve_shiftNoSch"), personNameAndPin, dateStr);
                } else if (AttCalculationConstant.AttAttendStatus.ACTUAL.equals(isWork)) {
                    // 已有排班且在上班日期中不允许补班申请！！
                    return MessageFormat.format(I18nUtil.i18nCode("att_approve_shiftConflicts"), personNameAndPin,
                        dateStr);
                } else {
                    AttShiftItem attShiftItem = attShiftItemMap.get(shiftId);
                    List<String> timeSlotDetailIds =
                        AttShiftSchUtils.getTimeSlotByShiftAndDate(attShiftItem, date, date);
                    if (CollectionUtil.isEmpty(timeSlotDetailIds)) {
                        // 2020-07-10不在调整班次"班次名称"的上班日期中不允许进行补班申请!
                        return MessageFormat.format(I18nUtil.i18nCode("att_shift_notExistShiftWorkDate"),
                            attShiftItem.getShiftName(), dateStr);
                    }
                }
            }
        }
        return null;
    }

    @Override
    public List<AttAdjustItem> getByCondition(AttAdjustItem condition) {
        return (List<AttAdjustItem>)attAdjustDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        Pager pager = attAdjustDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        buildItem((List<AttAdjustItem>)pager.getData());
        return pager;
    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, AttAdjustItem condition, int pageNo, int pageSize) {
        // 屏蔽补班功能
        condition.setAdjustType(AttConstant.ADJUST_TYPE_OFF);
        buildCondition(sessionId, condition);
        Pager pager =
            attAdjustDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        buildItem((List<AttAdjustItem>)pager.getData());
        return pager;
    }

    private void buildCondition(String sessionId, AttAdjustItem condition) {

        // 使用员工自助登录的用户,只有自己数据的权限。若登录类型为pers,则返回pin加入查询sql
        String persperPin = attPersonService.getPinByLoginType(sessionId);
        if (persperPin != null) {
            condition.setPersonPinEq(persperPin);
        } else {
            // 部门权限过滤
            String userId = authUserService.getUserIdBySessionId(sessionId);
            if (StringUtils.isNotBlank(userId)) {
                condition.setUserId(userId);
            }
            // 前端是否包含上下级
            if (ZKConstant.TRUE.equals(condition.getIsIncludeLower())) {
                if (StringUtils.isBlank(condition.getInDeptId())) {
                    if (StringUtils.isNotBlank(condition.getDeptId())) {
                        List<String> deptIdList = authDepartmentService.getChildDeptIdsByDeptIds(condition.getDeptId());
                        condition.setInDeptId(StrUtil.collectionToStr(deptIdList));
                        condition.setDeptId(null);
                    }
                }
            }
            // 人员姓名模糊查询（包含离职人员）
            String pins = attPersonService.getPinsByLikeName(sessionId, condition.getLikeName());
            if (StringUtils.isNotBlank(pins)) {
                condition.setInPersonPin(pins);
            }
        }
    }

    /**
     * 数据填充：离职人员姓名填充
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/7/2 15:08
     * @param itemList
     * @return void
     */
    private void buildItem(List<AttAdjustItem> itemList) {
        List<List<AttAdjustItem>> splitItemList = CollectionUtil.split(itemList, CollectionUtil.splitSize);
        for (List<AttAdjustItem> splitItems : splitItemList) {

            Collection<String> personPinList =
                    CollectionUtil.getPropertyList(splitItems, AttAdjustItem::getPersonPin, AttConstant.COMM_DEF_VALUE);
            Map<String, AttPersPersonInfoBean> persPersonInfoMap = attPersonService.getPersonInfoByPinList(personPinList);

            for (AttAdjustItem item : splitItems) {
                String personPin = item.getPersonPin();
                AttPersPersonInfoBean persLeavePersonItem = persPersonInfoMap.get(personPin);
                if (Objects.nonNull(persLeavePersonItem)) {
                    item.setPersonName(persLeavePersonItem.getPersonName());
                    item.setPersonLastName(persLeavePersonItem.getPersonLastName());
                }
            }
        }
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            List<AttAdjust> attAdjustList = attAdjustDao.findByIdList(CollectionUtil.strToList(ids));
            String crossDay = attParamService.getCrossDay();
            for (AttAdjust attAdjust : attAdjustList) {
                attAdjustDao.delete(attAdjust);
                // 【实时计算】删除缓存的调休,新增实时计算事件
                if (AttConstant.FLOW_STATUS_COMPLETE.equals(attAdjust.getFlowStatus())) {
                    delAdjustAddEventCache(attAdjust, crossDay);
                }
            }
        }
        return false;
    }

    @Override
    public AttAdjustItem getItemById(String id) {
        List<AttAdjustItem> items = getByCondition(new AttAdjustItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public List<AttAdjustItem> getItemData(String sessionId, Class<AttAdjustItem> attAdjustItemClass, AttAdjustItem condition,
        int beginIndex, int endIndex) {
        buildCondition(sessionId, condition);
        List<AttAdjustItem> itemList = attAdjustDao.getItemsDataBySql(attAdjustItemClass, SQLUtil.getSqlByItem(condition),
            beginIndex, endIndex, true);
        buildItem(itemList);
        return itemList;
    }

    @Override
    public boolean existAdjust(List<String> personIdList, Date startTime, Date endTime) {
        startTime = DateUtil.getDayBeginTime(startTime);
        endTime = DateUtil.getDayEndTime(endTime);
        List<AttAdjust> attAdjustList = attAdjustDao.getByPersonIdAndTime(personIdList, startTime, endTime);
        if (attAdjustList != null && attAdjustList.size() > 0) {
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public void handlerTransfer(List<AttAdjustItem> attAdjustItems) {
        // 数据量大的时候 分批处理 jinxian.huang 2019-07-31
        List<List<AttAdjustItem>> splitAttAdjustItemList =
            CollectionUtil.split(attAdjustItems, CollectionUtil.splitSize);
        for (List<AttAdjustItem> splitAdjustItemList : splitAttAdjustItemList) {
            // 找出pin号 根据pin号把人员信息补充完整 modified by jinxian.huang 2019-07-29
            Collection<String> pinList =
                CollectionUtil.getPropertyList(splitAdjustItemList, AttAdjustItem::getPersonPin, "-1");
            List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(pinList);
            Map<String, PersPersonItem> persPersonItemMap =
                CollectionUtil.listToKeyMap(persPersonItemList, PersPersonItem::getPin);
            List<AttAdjust> attAdjustList = new ArrayList<>();
            for (AttAdjustItem attAdjustItem : splitAdjustItemList) {

                String personPin = attAdjustItem.getPersonPin();
                Date adjustDate = attAdjustItem.getAdjustDate();
                AttAdjust attAdjust = attAdjustDao.getByPersonPinAndTime(personPin, adjustDate, adjustDate);
                if (Objects.isNull(attAdjust)) {
                    attAdjust = new AttAdjust();
                    if (persPersonItemMap.containsKey(personPin)) {
                        PersPersonItem persPersonItem = persPersonItemMap.get(personPin);
                        if (Objects.nonNull(persPersonItem)) {
                            attAdjust.setDeptId(persPersonItem.getDeptId());
                            attAdjust.setPersonId(persPersonItem.getId());
                        }
                    }
                    attAdjust.setCreateTime(attAdjustItem.getOperateDatetime());
                    // 流程状态默认设置为2——已完成
                    attAdjust.setFlowStatus("2");
                }
                ModelUtil.copyPropertiesIgnoreNullWithProperties(attAdjustItem, attAdjust, "id", "shiftId");
                String shiftNo = attAdjustItem.getShiftNo();
                AttShift attShift = null;
                if (StringUtils.isNotBlank(shiftNo)) {
                    attShift = attShiftDao.findByShiftNo(shiftNo);
                }
                if (Objects.nonNull(attShift)) {
                    attAdjust.setShiftId(attShift.getId());
                }
                attAdjustList.add(attAdjust);
            }
            attAdjustDao.saveAll(attAdjustList);
        }

    }

    @Override
    @Transactional
    public AttAdjustItem saveItem(AttAdjustItem item) {
        AttAdjust attAdjust = new AttAdjust();
        ModelUtil.copyPropertiesIgnoreNull(item, attAdjust);

        PersPersonItem personItem = persPersonService.getItemById(attAdjust.getPersonId());
        if (Objects.nonNull(personItem)) {
            attAdjust.setPersonPin(personItem.getPin());
        }

        attAdjustDao.save(attAdjust);
        return item;
    }

    @Override
    @Transactional
    public void updateFlowStatus(String businessKey, String status) {
        AttAdjust attAdjust = attAdjustDao.findByBusinessKey(businessKey);
        if (Objects.nonNull(attAdjust)) {

            // 【实时计算】 根据异常状态判断是否需要更新缓存和进行考勤实时计算
            boolean isCalculation =
                attCalculationCacheManager.judgeStatusCalculation(attAdjust.getFlowStatus(), status);

            attAdjust.setFlowStatus(status);
            attAdjustDao.save(attAdjust);

            // 【实时计算】保存\删除缓存的补签、新增实时计算事件
            if (isCalculation) {
                if (attCalculationCacheManager.judgeCacheDate(attAdjust.getAdjustDate())) {

                    List<AttShift> attShifts = attShiftDao.findAll();
                    Map<String, AttShift> attShiftMap = CollectionUtil.listToKeyMap(attShifts, AttShift::getId);
                    String crossDay = attParamService.getCrossDay();
                    if (AttConstant.FLOW_STATUS_COMPLETE.equals(attAdjust.getFlowStatus())) {
                        // 【实时计算】保存调休记录到缓存，新增实时计算事件
                        setAdjustAddEventCache(attAdjust, attShiftMap, crossDay);
                    } else {
                        // 【实时计算】删除缓存的补签，新增实时计算事件
                        delAdjustAddEventCache(attAdjust, crossDay);
                    }
                }
            }
        }
    }

    @Override
    public AttAdjustItem getByBusinessKey(String businessKey) {
        AttAdjust attAdjust = attAdjustDao.findByBusinessKey(businessKey);
        if (Objects.isNull(attAdjust)) {
            return null;
        }
        AttAdjustItem attAdjustItem = new AttAdjustItem();
        ModelUtil.copyProperties(attAdjust, attAdjustItem);
        return attAdjustItem;
    }

    @Override
    @Transactional
    public ZKResultMsg importExcel(List<AttAdjustItem> itemList, String sessionId) {

        if (itemList.size() > 30000) {
            // 一次性导入只能30000，超过要分批次导入
            throw ZKBusinessException.warnException("att_import_overData", itemList.size());
        }
        int importSize = itemList.size();
        int beginProgress = 20;
        // 导入的人员要求人事已存在
        Map<String, PersPersonItem> existPersPersonMap = new HashMap<>();
        // 取出待导入数据中人员的pin号
        Collection<String> importPins = CollectionUtil.getPropertyList(itemList, AttAdjustItem::getPersonPin, "-1");
        // 分批处理，一次处理800人
        List<List<String>> pinsList = CollectionUtil.split(importPins, CollectionUtil.splitSize);
        for (List<String> pins : pinsList) {
            // 根据pin号查出人事人员
            List<PersPersonItem> existPersonList = persPersonService.getItemsByPins(pins);
            if (existPersonList != null && existPersonList.size() > 0) {
                existPersPersonMap.putAll(CollectionUtil.listToKeyMap(existPersonList, PersPersonItem::getPin));
            }
        }
        Iterator<AttAdjustItem> itemIterator = itemList.iterator();
        // 先剔除无效数据
        while (itemIterator.hasNext()) {
            AttAdjustItem item = itemIterator.next();
            // 人员编号校验
            if (StringUtils.isBlank(item.getPersonPin())) {
                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                    ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                        I18nUtil.i18nCode("pers_import_pinNotEmpty")))));
                itemIterator.remove();
                continue;
            }
            PersPersonItem persPersonItem = existPersPersonMap.get(item.getPersonPin());
            if (Objects.isNull(persPersonItem)) {
                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                    ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                        I18nUtil.i18nCode("att_areaPerson_persNoExit")))));
                itemIterator.remove();
                continue;
            }
            // 时间空校验
            if (Objects.isNull(item.getAdjustDate())) {
                progressCache
                    .setProcess(new ProcessBean(beginProgress, beginProgress, ProcessBean.createErrorContent(I18nUtil
                        .i18nCode("pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("att_leave_startNotNull")))));
                itemIterator.remove();
                continue;
            }
        }

        // 查询班次信息
        List<AttShift> attShifts = attShiftDao.findAll();
        Map<String, AttShift> attShiftMap = CollectionUtil.listToKeyMap(attShifts, AttShift::getId);

        // 是否有审批权限
        boolean hasApproval = authPermissionService.checkPermission(sessionId, "att:adjust:approval");

        // 剩下的可以插入数据库,分批处理，一次处理800条
        List<List<AttAdjustItem>> attOvertimeInsertList = CollectionUtil.split(itemList, CollectionUtil.splitSize);
        for (List<AttAdjustItem> insertItemList : attOvertimeInsertList) {
            // 保存入库
            List<AttAdjust> attAdjustList = new ArrayList<>();
            for (AttAdjustItem item : insertItemList) {
                PersPersonItem persPersonItem = existPersPersonMap.get(item.getPersonPin());
                if (Objects.nonNull(persPersonItem)) {
                    AttAdjust attAdjust = new AttAdjust();
                    ModelUtil.copyPropertiesWithIgnore(item, attAdjust, "id");
                    attAdjust.setDeptId(persPersonItem.getDeptId());
                    attAdjust.setPersonId(persPersonItem.getId());
                    // 导入现在只有调休,表格没有类型列,解析出来的对象类型是空的,类型直接默认调休
                    attAdjust.setAdjustType(AttConstant.ADJUST_TYPE_OFF);
                    // 如果有审批权限，直接标记状态为已完成
                    attAdjust
                        .setFlowStatus(hasApproval ? AttConstant.FLOW_STATUS_COMPLETE : AttConstant.FLOW_STATUS_CREATE);

                    attAdjustList.add(attAdjust);
                }
            }
            attAdjustDao.saveAll(attAdjustList);

            // 【实时计算】保存调休记录到缓存，新增实时计算事件
            if (hasApproval) {
                String crossDay = attParamService.getCrossDay();
                for (AttAdjust attAdjust : attAdjustList) {
                    setAdjustAddEventCache(attAdjust, attShiftMap, crossDay);
                }
            }
        }
        // 失败数量
        int faildCount = importSize - itemList.size();
        // 成功：%s 条，失败：%s 条。
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(99,
            I18nUtil.i18nCode("pers_import_result", itemList.size(), faildCount)));
        if (faildCount > 0) {
            return ZKResultMsg.failMsg();
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    @Transactional
    public void approval(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            // 【实时计算】保存调休记录到缓存，新增实时计算事件
            List<AttAdjust> attAdjustList = attAdjustDao.findByIdList(CollectionUtil.strToList(ids));
            String crossDay = attParamService.getCrossDay();
            List<AttShift> attShifts = attShiftDao.findAll();
            Map<String, AttShift> attShiftMap = CollectionUtil.listToKeyMap(attShifts, AttShift::getId);
            for (AttAdjust attAdjust : attAdjustList) {
                if (!AttConstant.FLOW_STATUS_COMPLETE.equals(attAdjust.getFlowStatus())) {
                    setAdjustAddEventCache(attAdjust, attShiftMap, crossDay);
                }
            }
            attAdjustDao.updateFlowStatus(Arrays.asList(ids.split(",")), AttConstant.FLOW_STATUS_COMPLETE);
        }
    }

    @Override
    @Transactional
    public void refuse(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            // 【实时计算】删除缓存的调休,新增实时计算事件
            List<AttAdjust> attAdjustList = attAdjustDao.findByIdList(CollectionUtil.strToList(ids));
            String crossDay = attParamService.getCrossDay();
            for (AttAdjust attAdjust : attAdjustList) {
                if (AttConstant.FLOW_STATUS_COMPLETE.equals(attAdjust.getFlowStatus())) {
                    delAdjustAddEventCache(attAdjust, crossDay);
                }
            }
            attAdjustDao.updateFlowStatus(Arrays.asList(ids.split(",")), AttConstant.FLOW_STATUS_REFUSE);
        }
    }

    @Override
    public Map<String, List<AttPersonSchBO>> getAdjustMap(List<String> pins, Date startDate, Date endDate) {

        // 查询调休补班
        List<AttAdjust> attAdjustList = new ArrayList<>();
        if (CollectionUtil.isEmpty(pins)) {
            attAdjustList = attAdjustDao.findAdjusByDate(startDate, endDate);
        } else if (pins.size() == 1) {
            attAdjustList = attAdjustDao.findAdjusByDateAndPin(startDate, endDate, pins.get(0));
        } else {
            List<List<String>> splitPinList = CollectionUtil.split(pins, CollectionUtil.splitSize);
            for (List<String> subPinList : splitPinList) {
                List<AttAdjust> subAdjustList = attAdjustDao.findAdjusByDateAndPins(startDate, endDate, subPinList);
                attAdjustList.addAll(subAdjustList);
            }
        }

        // 查询班次信息
        Map<String, AttShift> attShiftMap = new HashMap<>();
        Collection<String> shiftIds = CollectionUtil.getPropertyList(attAdjustList, AttAdjust::getShiftId, "-1");
        if (!CollectionUtil.isEmpty(shiftIds)) {
            List<AttShift> attShifts = attShiftDao.findByIdList(shiftIds);
            attShiftMap = CollectionUtil.listToKeyMap(attShifts, AttShift::getId);
        }

        Map<String, List<AttPersonSchBO>> attAdjustMap = new HashMap<>();
        for (AttAdjust attAdjust : attAdjustList) {
            buildAdjustMap(attAdjust, attShiftMap, attAdjustMap);
        }

        return attAdjustMap;
    }

    /**
     * 组装调休记录
     */
    private void buildAdjustMap(AttAdjust attAdjust, Map<String, AttShift> attShiftMap,
        Map<String, List<AttPersonSchBO>> attAdjustMap) {
        String pin = attAdjust.getPersonPin();
        String adjustDate = AttDateUtils.dateToStrAsShort(attAdjust.getAdjustDate());
        Short adjustType = attAdjust.getAdjustType();

        AttPersonSchBO attPersonSchBO = new AttPersonSchBO();
        attPersonSchBO.setId(attAdjust.getId());
        attPersonSchBO.setAttendStatus(AttCalculationConstant.AttAttendStatus.TUNE_OFF);
        if (AttConstant.ADJUST_TYPE_CLASSES == adjustType) {
            // 补班 填充班次时间段信息 (zktime之后没补班功能,屏蔽掉)
            return;
            /* 
            AttShift attShift = attShiftMap.get(attAdjust.getShiftId());
            AttShiftItem attShiftItem = ModelUtil.copyProperties(attShift, new AttShiftItem());
            List<String> timeSlotIdList = AttShiftSchUtils.getTimeSlotByShiftAndDate(attShiftItem,
                attAdjust.getAdjustDate(), attAdjust.getAdjustDate());
            List<AttTimeSlotBO> timeSlotBOList = new ArrayList<>();
            for (String timeSlotId : timeSlotIdList) {
                AttTimeSlotBO attTimeSlotBO = new AttTimeSlotBO();
                attTimeSlotBO.setAttTimeSlotId(timeSlotId);
                timeSlotBOList.add(attTimeSlotBO);
            }
            attPersonSchBO.setAttendStatus(AttCalculationConstant.AttAttendStatus.SUPPLEMENT_CLASS);
            attPersonSchBO.setAttShiftId(attAdjust.getShiftId());
            attPersonSchBO.setAttTimeSlotArray(timeSlotBOList);
            */
        } else {
            attPersonSchBO.setAttTimeSlotArray(new ArrayList<>());
        }

        String key = pin + AttCalculationConstant.KEY_CONNECTOR + adjustDate;
        List<AttPersonSchBO> attPersonSchBOList = new ArrayList<>();
        attPersonSchBOList.add(attPersonSchBO);
        attAdjustMap.put(key, attPersonSchBOList);
    }

    /**
     * 【实时计算】保存调休记录到缓存，新增实时计算事件
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/31 11:47
     * @param attAdjust
     * @param crossDay
     * @return void
     */
    private void setAdjustAddEventCache(AttAdjust attAdjust, Map<String, AttShift> attShiftMap, String crossDay) {

        // 发送消息
        AttAdjustItem attAdjustItem = new AttAdjustItem();
        ModelUtil.copyProperties(attAdjust, attAdjustItem);
        attMessageCenterService.pushAdjustMessage(attAdjustItem);

        if (attParamService.realTimeEnable()) {
            if (attCalculationCacheManager.judgeCacheDate(attAdjust.getAdjustDate())) {
                Map<String, List<AttPersonSchBO>> attAdjustMap = new HashMap<>();
                buildAdjustMap(attAdjust, attShiftMap, attAdjustMap);
                for (Map.Entry<String, List<AttPersonSchBO>> entry : attAdjustMap.entrySet()) {
                    attCalculationCacheManager.setAdjust(entry.getKey(), entry.getValue(), true);
                }
                // 新增实时计算事件
                attCalculationCacheManager.addRealTimeEvent(attAdjust.getPersonPin(), attAdjust.getAdjustDate(),
                    crossDay);
            }
        }
    }

    /**
     * 【实时计算】删除缓存的补签，新增实时计算事件
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/31 18:31
     * @param attAdjust
     * @param crossDay
     * @return void
     */
    private void delAdjustAddEventCache(AttAdjust attAdjust, String crossDay) {
        if (attParamService.realTimeEnable()) {
            if (attCalculationCacheManager.judgeCacheDate(attAdjust.getAdjustDate())) {
                // 删除缓存的补签
                attCalculationCacheManager.delAdjust(attAdjust.getPersonPin(), attAdjust.getId(),
                    attAdjust.getAdjustDate());
                // 新增实时计算事件
                attCalculationCacheManager.addRealTimeEvent(attAdjust.getPersonPin(), attAdjust.getAdjustDate(),
                    crossDay);
            }
        }
    }
}