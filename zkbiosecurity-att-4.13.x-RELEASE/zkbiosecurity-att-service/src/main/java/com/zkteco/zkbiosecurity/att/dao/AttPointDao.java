/**
 * File Name: AttPoint Created by GenerationTools on 2018-02-08 下午08:27 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.att.dao;

import com.zkteco.zkbiosecurity.att.model.AttPoint;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;

/**
 * 对应百傲瑞达 AttPointDao
 * 
 * <AUTHOR>
 * @date: 2018-02-08 下午08:27
 * @version v1.0
 */
public interface AttPointDao extends BaseDao<AttPoint, String> {

    List<AttPoint> findByDeviceModule(String deviceModule);

    AttPoint findByPointName(String pointName);

    AttPoint findByDeviceSnAndDoorNo(String deviceSn, Short doorNo);

    AttPoint findByDeviceId(String deviceId);

    @Query(value = "select count(id) from AttPoint where deviceModule = ?1")
    int getCountByDeviceModule(String deviceModule);

    AttPoint findByDeviceSn(String devSn);

    /**
     * 根据设备SN 查询考勤点
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2019/1/8 11:37
     * @param devSn
     * @return java.util.List<com.zkteco.zkbiosecurity.att.model.AttPoint>
     */
    @Query(value = "select t from AttPoint t where t.deviceSn = ?1")
    List<AttPoint> getAttPointByDeviceSn(String devSn);

    long countByDeviceIdIn(Collection<String> channelIds);

    boolean existsByDeviceId(String deviceId);

    boolean existsByDeviceSn(String deviceSn);

    @Modifying
    @Query(value = "UPDATE AttPoint SET deviceSn=?2 WHERE deviceSn=?1 ")
    void updateDeviceSn(String oldSn, String newSn);
}