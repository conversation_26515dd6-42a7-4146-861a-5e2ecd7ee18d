package com.zkteco.zkbiosecurity.att.data.upgrade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * 升级到4.10.0
 *
 */
@Slf4j
@Component
public class AttVer4_10_0 implements UpgradeVersionManager {

    @Autowired
    private AuthPermissionService authPermissionService;

    @Override
    public String getModule() {
        return BaseConstants.ATT;
    }

    @Override
    public String getVersion() {
        return "v4.10.0";
    }

    @Override
    public boolean executeUpgrade() {

        // 菜单升级
        upgradeMenu();

        return true;
    }

    // 菜单升级
    private void upgradeMenu() {
        // 考勤模块
        AuthPermissionItem systemMenuItem = authPermissionService.getItemByCode("Att");
        if (systemMenuItem != null) {
            /* ------------- 一级菜单:考勤自定义报表 -------------*/
            AuthPermissionItem parentMenuItem = new AuthPermissionItem("AttCustomReportManage", "att_customReport",
                    "att:customReport:manage", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 10);
            parentMenuItem.setParentId(systemMenuItem.getId());
            parentMenuItem.setActionLink("attCustomReport.do");
            parentMenuItem.setImg("att_statistics_report.png");
            parentMenuItem.setImgHover("att_statistics_report_over.png");
            parentMenuItem = authPermissionService.saveItem(parentMenuItem);

            /* ------------- 二级菜单:考勤自定义报表-------------*/
            AuthPermissionItem childMenuItem = new AuthPermissionItem("AttCustomReport", "att_customReport",
                    "att:customReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 1);
            childMenuItem.setParentId(parentMenuItem.getId());
            childMenuItem.setActionLink("attCustomReport.do");
            childMenuItem = authPermissionService.saveItem(childMenuItem);
            // 刷新
            AuthPermissionItem buttonMenuItem = new AuthPermissionItem("AttCustomReportRefresh", "common_op_refresh",
                    "att:customReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
            buttonMenuItem.setParentId(childMenuItem.getId());
            authPermissionService.saveItem(buttonMenuItem);
            // 新增
            buttonMenuItem = new AuthPermissionItem("AttCustomReportAdd", "common_op_new", "att:customReport:add",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
            buttonMenuItem.setParentId(childMenuItem.getId());
            authPermissionService.saveItem(buttonMenuItem);
            // 删除
            buttonMenuItem = new AuthPermissionItem("AttCustomReportDel", "common_op_del", "att:customReport:del",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
            buttonMenuItem.setParentId(childMenuItem.getId());
            authPermissionService.saveItem(buttonMenuItem);
            // 导出
            buttonMenuItem = new AuthPermissionItem("AttCustomReportExport", "common_op_export", "att:customReport:export",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
            buttonMenuItem.setParentId(childMenuItem.getId());
            authPermissionService.saveItem(buttonMenuItem);
        }
    }
}
