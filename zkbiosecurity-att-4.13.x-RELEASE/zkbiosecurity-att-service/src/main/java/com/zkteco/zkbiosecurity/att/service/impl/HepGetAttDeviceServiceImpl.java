package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.att.service.AttDeviceOptionService;
import com.zkteco.zkbiosecurity.hep.service.HepGetAttDeviceService;

/**
 * 防疫获取考勤防疫设备
 * 
 * <AUTHOR>
 * @date 2020/12/10 13:49
 * @since 1.0.0
 */
@Service
public class HepGetAttDeviceServiceImpl implements HepGetAttDeviceService {

    @Autowired
    private AttDeviceOptionService attDeviceOptionService;

    @Override
    public List<String> hepGetAttDevSnList() {
        List<String> functions = Arrays.asList("MaskDetectionFunOn", "IRTempDetectionFunOn");
        List<String> list = attDeviceOptionService.getSupportFunctionsDevSn(functions);
        return list;
    }
}
