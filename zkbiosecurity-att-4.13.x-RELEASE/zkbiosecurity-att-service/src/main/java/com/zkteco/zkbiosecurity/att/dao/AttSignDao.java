/**
 * File Name: AttSign Created by GenerationTools on 2018-02-08 下午08:27 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.att.dao;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.att.model.AttSign;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 对应百傲瑞达 AttSignDao
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-08 下午08:27
 */
public interface AttSignDao extends BaseDao<AttSign, String> {

    /**
     * 根据人员编号,和签到时间 获取补签记录
     *
     * @param personPin
     *            人员编号
     * @param signDatetime
     *            补签时间
     * @return com.zkteco.zkbiosecurity.att.model.AttSign
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/25 9:44
     */
    List<AttSign> findByPersonPinAndSignDatetime(String personPin, Date signDatetime);

    AttSign findByBusinessKey(String businessKey);

    @Query("SELECT t FROM AttSign t WHERE t.flowStatus='2' AND t.signDatetime >= ?1 AND t.signDatetime <= ?2")
    List<AttSign> findSignByDate(Date startDate, Date endDate);

    @Query("SELECT t FROM AttSign t WHERE t.flowStatus='2' AND t.signDatetime >= ?1 AND t.signDatetime <= ?2 AND t.personPin = ?3")
    List<AttSign> findSignByDateAndPin(Date startDate, Date endDate, String personPin);

    /**
     * 查询时间范围内人员集合的补签记录
     *
     * @param startDate
     * @param endDate
     * @param pins
     * @return
     */
    @Query("SELECT t FROM AttSign t WHERE t.flowStatus='2' AND t.signDatetime >= ?1 AND t.signDatetime <= ?2 AND t.personPin IN (?3)")
    List<AttSign> findSignByDateAndPins(Date startDate, Date endDate, List<String> pins);

    @Query("SELECT t.personId,t.personPin,count(t.id) FROM AttSign t WHERE t.signDatetime >= ?1 AND t.signDatetime <= ?2 AND t.personId in (?3) group by t.personId,t.personPin")
    List<Object[]> getTeamSignTimes(Date startDateTime, Date endDateTime, Collection<String> personIds);

    /**
     * 查找所有已通过审批，且补签时间大于或等于指定日期的补签申请
     *
     * @param flowStatus
     * @param startDate
     * @return
     * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
     */
    List<AttSign> findByFlowStatusAndSignDatetimeGreaterThanEqual(String flowStatus, Date startDate);

    @Modifying
    @Query("update AttSign t set t.flowStatus=?2 where t.id in (?1)")
    void updateFlowStatus(List<String> asList, String flowStatusComplete);
}