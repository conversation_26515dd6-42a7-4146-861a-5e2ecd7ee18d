package com.zkteco.zkbiosecurity.att.service.impl;

import java.math.BigDecimal;
import java.util.*;

import javax.transaction.Transactional;

import com.zkteco.zkbiosecurity.pers.service.PersPersonCacheService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem;
import com.zkteco.zkbiosecurity.system.service.BaseMessageService;
import com.zkteco.zkbiosecurity.system.vo.BaseMessageItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.api.vo.*;
import com.zkteco.zkbiosecurity.att.bean.AttCalculationLeaveLong;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttTimeSlotBO;
import com.zkteco.zkbiosecurity.att.constants.AttApiConstant;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.constants.AttShiftConstant;
import com.zkteco.zkbiosecurity.att.dao.*;
import com.zkteco.zkbiosecurity.att.enums.AttRuleEnum;
import com.zkteco.zkbiosecurity.att.model.AttLeave;
import com.zkteco.zkbiosecurity.att.model.AttOut;
import com.zkteco.zkbiosecurity.att.model.AttOvertime;
import com.zkteco.zkbiosecurity.att.model.AttTrip;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.workflow.service.Workflow4OtherFlowableService;
import com.zkteco.zkbiosecurity.workflow.vo.WorkflowApplyInfoItem;
import com.zkteco.zkbiosecurity.workflow.vo.WorkflowCompleteInfoItem;
import com.zkteco.zkbiosecurity.workflow.vo.WorkflowProcessInfoItem;
import com.zkteco.zkbiosecurity.workflow.vo.WorkflowTaskInfoItem;

/**
 * 移动端考勤申请（工作流）
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 16:50
 * @since 1.0.0
 */
@Slf4j
@Service
public class AttWorkflowServiceImpl implements AttWorkflowService {

    @Autowired
    private AttRecordService attRecordService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AttSignService attSignService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;
    @Autowired
    private AttLeaveService attLeaveService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AttRecordDao attRecordDao;
    @Autowired
    private AttOvertimeService attOvertimeService;
    @Autowired(required = false)
    private Workflow4OtherFlowableService workflowService;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttCalculateService attCalculateService;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttTripDao attTripDao;
    @Autowired
    private AttLeaveDao attLeaveDao;
    @Autowired
    private AttOutDao attOutDao;
    @Autowired
    private AttOvertimeDao attOvertimeDao;
    @Autowired
    private AttPersonSchService attPersonSchService;
    @Autowired
    private AttPersonSchDataService attPersonSchDataService;
    @Autowired
    private AttTimeSlotService attTimeSlotService;
    @Autowired
    private AttShiftService attShiftService;
    @Autowired
    private BaseMessageService baseMessageService;
    @Autowired
    private PersPersonCacheService persPersonCacheService;

    @Override
    public Integer getPersonMonthSignCount(String personPin, String month) {

        int year = Integer.parseInt(month.split("-")[0]);
        int monthInt = Integer.parseInt(month.split("-")[1]);
        Date firstDayOfMonth = AttDateUtils.getFirstDayOfMonth(year, monthInt);
        Date lastDayOfMonth = AttDateUtils.getLastDayOfMonth(year, monthInt);

        AttSignItem condition = new AttSignItem();
        condition.setEquals(true);
        condition.setPersonPin(personPin);
        condition.setStartTime(firstDayOfMonth);
        condition.setEndTime(lastDayOfMonth);
        condition.setFlowStatusIn(AttConstant.FLOW_STATUS_COMPLETE + "," + AttConstant.FLOW_STATUS_CREATE);

        List<AttSignItem> attSignItems = attSignService.getByCondition(condition);

        Integer count = CollectionUtil.isEmpty(attSignItems) ? 0 : attSignItems.size();

        return count;
    }

    @Override
    @Transactional
    public void saveSignItem(List<AttCloudSignItem> attSignItemList) {
        if (!CollectionUtil.isEmpty(attSignItemList)) {
            for (AttCloudSignItem attCloudSignItem : attSignItemList) {
                String personPin = attCloudSignItem.getPersonPin();
                PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
                // 判断补签时间不能为空 by ljf 2019/9/27
                if (StringUtils.isBlank(attCloudSignItem.getSignDatetime())
                    || StringUtils.isBlank(attCloudSignItem.getSignDatetime().replaceAll("-|;", ""))) {
                    throw ZKBusinessException.errorException("att_sign_signTimeNotNull");
                }

                String personId = persPersonItem.getId();
                String deptId = persPersonItem.getDeptId();

                // notifierPerIds 知会人 personPins转成personIds传给流程
                String notifierPins = attCloudSignItem.getNotifierPins();
                String notifierPerIds = null;
                if (StringUtils.isNotBlank(notifierPins)) {
                    List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(notifierPins);
                    notifierPerIds = CollectionUtil.getPropertys(persPersonItemList, PersPersonItem::getId);
                }

                String signDate = attCloudSignItem.getSignDate();
                String signDatetime = attCloudSignItem.getSignDatetime();
                String[] signDateTimeArr = signDatetime.split("-|;");

                for (String signTime : signDateTimeArr) {
                    if (StringUtils.isBlank(signTime)) {
                        continue;
                    }
                    AttSignItem attSignItem = new AttSignItem();
                    attSignItem.setPersonId(personId);
                    attSignItem.setPersonPin(personPin);
                    attSignItem.setDeptId(deptId);
                    attSignItem.setSignDatetime(
                        DateUtil.stringToDate(signDate + " " + signTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM));
                    attSignItem.setRemark(attCloudSignItem.getRemark());
                    attSignItem.setNotifierPerIds(notifierPerIds);
                    attSignItem.setAfterSignRecord(signTime);
                    attSignItem.setFlowStatus(AttConstant.FLOW_STATUS_CREATE);
                    attSignItem.setAttState(attCloudSignItem.getAttState());
                    attSignItem = attSignService.saveItem(attSignItem);

                    WorkflowApplyInfoItem workflowApplyInfoItem = new WorkflowApplyInfoItem();
                    workflowApplyInfoItem.setApplyUserId(personId);
                    workflowApplyInfoItem.setPin(personPin);
                    workflowApplyInfoItem.setApplyName(persPersonItem.getName());
                    workflowApplyInfoItem
                        .setApplyStartDatetime(AttDateUtils.dateToStrAsLong(attSignItem.getSignDatetime()));
                    workflowApplyInfoItem.setFlowType(AttConstant.FLOW_TYPE_SIGN);
                    workflowApplyInfoItem.setFlowTypeName(I18nUtil.i18nCode("wf_leftMenu_sign"));
                    workflowApplyInfoItem.setFormRecordId(attSignItem.getId());
                    workflowApplyInfoItem.setNotifierUserIds(notifierPerIds);
                    workflowService.start(workflowApplyInfoItem);
                }
            }
        }
    }

    @Override
    @Transactional
    public void saveLeaveItem(List<AttLeaveItem> attLeaveItemList) {
        if (!CollectionUtil.isEmpty(attLeaveItemList)) {
            for (AttLeaveItem item : attLeaveItemList) {

                checkApplyStartDateIsOverdue(item.getStartDatetime());

                // apply 申请人 personPin转成personId
                String personPin = item.getPersonPin();
                PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
                String personId = persPersonItem.getId();
                item.setPersonId(personId);
                item.setPersonPin(personPin);
                // notifierPerIds 知会人 personPins转成personIds传给流程
                String notifierPins = item.getNotifierPins();
                if (StringUtils.isNotBlank(notifierPins)) {
                    List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(notifierPins);
                    String notifierPerIds = CollectionUtil.getPropertys(persPersonItemList, PersPersonItem::getId);
                    item.setNotifierPerIds(notifierPerIds);
                }

                // 请假类型编号转成请假类型Id
                String leaveTypeNo = item.getLeaveTypeNo();
                String leaveTypeName = "";
                AttLeaveTypeItem condition = new AttLeaveTypeItem();
                condition.setLeaveTypeNo(leaveTypeNo);
                List<AttLeaveTypeItem> attLeaveTypeItems = attLeaveTypeService.getByCondition(condition);
                if (!CollectionUtil.isEmpty(attLeaveTypeItems)) {
                    AttLeaveTypeItem attLeaveTypeItem = attLeaveTypeItems.get(0);
                    String leaveTypeId = attLeaveTypeItem.getId();
                    leaveTypeName = attLeaveTypeItem.getLeaveTypeName();
                    item.setLeaveTypeId(leaveTypeId);
                }

                String flowType = AttCalculationConstant.AttAttendStatus.LEAVE;

                // 业务模块先保存表单数据在提交流程 by ljf 2019/5/13
                item.setFlowStatus(AttConstant.FLOW_STATUS_CREATE);
                AttLeaveItem attLeaveItem = attLeaveService.saveItem(personId, item, null);

                WorkflowApplyInfoItem workflowApplyInfoItem = new WorkflowApplyInfoItem();
                workflowApplyInfoItem.setApplyUserId(personId);
                workflowApplyInfoItem.setPin(personPin);
                workflowApplyInfoItem.setApplyName(persPersonItem.getName());
                workflowApplyInfoItem.setFlowType(flowType);
                workflowApplyInfoItem.setFlowTypeName(leaveTypeName);
                workflowApplyInfoItem
                    .setApplyStartDatetime(AttDateUtils.dateToStrAsLong(attLeaveItem.getStartDatetime()));
                workflowApplyInfoItem.setApplyEndDatetime(AttDateUtils.dateToStrAsLong(attLeaveItem.getEndDatetime()));
                workflowApplyInfoItem.setFormRecordId(attLeaveItem.getId());
                workflowApplyInfoItem.setNotifierUserIds(item.getNotifierPerIds());
                // 调整请假时长单位为小时 by ljf 2021/1/14
                workflowApplyInfoItem.setLeaveLong(attLeaveItem.getLeaveLongHour());
                workflowService.start(workflowApplyInfoItem);
            }
        }
    }

    @Override
    @Transactional
    public void saveOvertimeItem(List<AttOvertimeItem> attOvertimeItemList) {
        if (!CollectionUtil.isEmpty(attOvertimeItemList)) {
            for (AttOvertimeItem item : attOvertimeItemList) {

                checkApplyStartDateIsOverdue(item.getStartDatetime());

                // apply 申请人 personPin转成personId
                String startTime = AttDateUtils.dateToStrAsLong(item.getStartDatetime());
                String endTime = AttDateUtils.dateToStrAsLong(item.getEndDatetime());
                String personPin = item.getPersonPin();
                PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
                String personId = persPersonItem.getId();
                item.setPersonId(personId);
                int timeLong = AttDateUtils.getMinuteDiff(startTime, endTime);
                if (timeLong == 0) {
                    throw ZKBusinessException.errorException(I18nUtil.i18nCode("att_apply_DurationIsZero"));
                }
                item.setOvertimeLong(timeLong);

                // notifierPerIds 知会人 personPins转成personIds传给流程
                String notifierPins = item.getNotifierPins();
                if (StringUtils.isNotBlank(notifierPins)) {
                    List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(notifierPins);
                    String notifierPerIds = CollectionUtil.getPropertys(persPersonItemList, PersPersonItem::getId);
                    item.setNotifierPerIds(notifierPerIds);
                }

                // 业务模块先保存表单数据在提交流程 by ljf 2019/5/13
                item.setFlowStatus(AttConstant.FLOW_STATUS_CREATE);
                AttOvertimeItem attOvertimeItem = attOvertimeService.saveItem(item);

                WorkflowApplyInfoItem workflowApplyInfoItem = new WorkflowApplyInfoItem();
                workflowApplyInfoItem.setApplyUserId(personId);
                workflowApplyInfoItem.setPin(personPin);
                workflowApplyInfoItem.setApplyName(persPersonItem.getName());
                workflowApplyInfoItem.setFlowType(AttConstant.FLOW_TYPE_OVERTIME);
                workflowApplyInfoItem.setFlowTypeName(AttCommonUtils.overtimeSignName(item.getOvertimeSign()));
                workflowApplyInfoItem.setApplyStartDatetime(AttDateUtils.dateToStrAsLong(item.getStartDatetime()));
                workflowApplyInfoItem.setApplyEndDatetime(AttDateUtils.dateToStrAsLong(item.getEndDatetime()));
                workflowApplyInfoItem.setFormRecordId(attOvertimeItem.getId());
                workflowApplyInfoItem.setNotifierUserIds(item.getNotifierPerIds());
                workflowService.start(workflowApplyInfoItem);
            }
        }
    }

    @Override
    @Transactional
    public void saveTripItem(List<AttTripItem> attTripItemList) {
        if (!CollectionUtil.isEmpty(attTripItemList)) {

            // 获取假种配置
            AttLeaveTypeItem attLeaveTypeItem =
                attLeaveTypeService.getItemByLeaveTypeNo(AttCalculationConstant.AttAttendStatus.TRIP);

            for (AttTripItem item : attTripItemList) {

                checkApplyStartDateIsOverdue(item.getStartDatetime());

                String personPin = item.getPersonPin();
                PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
                String personId = persPersonItem.getId();
                item.setPersonId(personId);

                Integer tripLong = attLeaveService.calLeaveTimeMinutes(item.getPersonId(), item.getStartDatetime(),
                    item.getEndDatetime(), attLeaveTypeItem);
                if (tripLong == 0) {
                    throw ZKBusinessException.errorException("att_apply_DurationIsZero");
                }
                item.setLeaveLong(tripLong);

                // notifierPerIds 知会人 personPins转成personIds传给流程
                String notifierPins = item.getNotifierPins();
                if (StringUtils.isNotBlank(notifierPins)) {
                    List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(notifierPins);
                    String notifierPerIds = CollectionUtil.getPropertys(persPersonItemList, PersPersonItem::getId);
                    item.setNotifierPerIds(notifierPerIds);
                }

                // 业务模块先保存表单数据在提交流程 by ljf 2019/5/13
                item.setFlowStatus(AttConstant.FLOW_STATUS_CREATE);
                // AttTripItem attTripItem = attTripService.saveItem(item);

                AttLeaveItem leaveItem = new AttLeaveItem();

                leaveItem.setDeptId(item.getDeptId());
                leaveItem.setPersonId(item.getPersonId());
                leaveItem.setPersonPin(item.getPersonPin());
                leaveItem.setLeaveTypeId(attLeaveTypeItem.getId());
                leaveItem.setLeaveTypeNo(attLeaveTypeItem.getLeaveTypeNo());
                leaveItem.setLeaveLong(item.getLeaveLong());
                leaveItem.setStartDatetime(item.getStartDatetime());
                leaveItem.setEndDatetime(item.getEndDatetime());
                leaveItem.setRemark(item.getRemark());
                leaveItem.setFlowStatus(item.getFlowStatus());

                // 小程序照片保存到本地，防止临时链接过期请假文件无法查看
                String photoUrlList = item.getPhotoUrlList();
                String leaveImagePath = attLeaveService.saveCloudImage(photoUrlList);
                leaveItem.setLeaveImagePath(leaveImagePath);
                leaveItem.setCloudImageUrl(photoUrlList);

                leaveItem = attLeaveService.saveItem(leaveItem.getPersonId(), leaveItem, null);

                WorkflowApplyInfoItem workflowApplyInfoItem = new WorkflowApplyInfoItem();
                workflowApplyInfoItem.setApplyUserId(personId);
                workflowApplyInfoItem.setPin(personPin);
                workflowApplyInfoItem.setApplyName(persPersonItem.getName());
                workflowApplyInfoItem.setFlowType(AttConstant.FLOW_TYPE_TRIP);
                workflowApplyInfoItem.setFlowTypeName(I18nUtil.i18nCode("wf_leftMenu_trip"));
                workflowApplyInfoItem.setApplyStartDatetime(AttDateUtils.dateToStrAsLong(item.getStartDatetime()));
                workflowApplyInfoItem.setApplyEndDatetime(AttDateUtils.dateToStrAsLong(item.getEndDatetime()));
                workflowApplyInfoItem.setFormRecordId(leaveItem.getId());
                workflowApplyInfoItem.setNotifierUserIds(item.getNotifierPerIds());
                workflowService.start(workflowApplyInfoItem);
            }

        }
    }

    @Override
    @Transactional
    public void saveOutItem(List<AttOutItem> attOutItemList) {
        if (!CollectionUtil.isEmpty(attOutItemList)) {

            // 获取假种配置
            AttLeaveTypeItem attLeaveTypeItem =
                attLeaveTypeService.getItemByLeaveTypeNo(AttCalculationConstant.AttAttendStatus.OUT);

            for (AttOutItem item : attOutItemList) {

                checkApplyStartDateIsOverdue(item.getStartDatetime());

                String personPin = item.getPersonPin();
                PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
                String personId = persPersonItem.getId();
                item.setPersonId(personId);

                Integer outLong = attLeaveService.calLeaveTimeMinutes(item.getPersonId(), item.getStartDatetime(),
                    item.getEndDatetime(), attLeaveTypeItem);
                if (outLong == 0) {
                    throw ZKBusinessException.errorException(I18nUtil.i18nCode("att_apply_DurationIsZero"));
                }
                item.setLeaveLong(outLong);

                // notifierPerIds 知会人 personPins转成personIds传给流程
                String notifierPins = item.getNotifierPins();
                if (StringUtils.isNotBlank(notifierPins)) {
                    List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(notifierPins);
                    String notifierPerIds = CollectionUtil.getPropertys(persPersonItemList, PersPersonItem::getId);
                    item.setNotifierPerIds(notifierPerIds);
                }

                // 业务模块先保存表单数据在提交流程 by ljf 2019/5/13
                item.setFlowStatus(AttConstant.FLOW_STATUS_CREATE);
                // AttOutItem attOutItem = attOutService.saveItem(item);

                AttLeaveItem leaveItem = new AttLeaveItem();

                leaveItem.setDeptId(item.getDeptId());
                leaveItem.setPersonId(item.getPersonId());
                leaveItem.setPersonPin(item.getPersonPin());
                leaveItem.setLeaveTypeId(attLeaveTypeItem.getId());
                leaveItem.setLeaveTypeNo(attLeaveTypeItem.getLeaveTypeNo());
                leaveItem.setLeaveLong(item.getLeaveLong());
                leaveItem.setStartDatetime(item.getStartDatetime());
                leaveItem.setEndDatetime(item.getEndDatetime());
                leaveItem.setRemark(item.getRemark());
                leaveItem.setFlowStatus(item.getFlowStatus());

                // 小程序照片保存到本地，防止临时链接过期请假文件无法查看
                String photoUrlList = item.getPhotoUrlList();
                String leaveImagePath = attLeaveService.saveCloudImage(photoUrlList);
                leaveItem.setLeaveImagePath(leaveImagePath);
                leaveItem.setCloudImageUrl(photoUrlList);

                leaveItem = attLeaveService.saveItem(leaveItem.getPersonId(), leaveItem, null);

                WorkflowApplyInfoItem workflowApplyInfoItem = new WorkflowApplyInfoItem();
                workflowApplyInfoItem.setApplyUserId(personId);
                workflowApplyInfoItem.setPin(personPin);
                workflowApplyInfoItem.setApplyName(persPersonItem.getName());
                workflowApplyInfoItem.setFlowType(AttConstant.FLOW_TYPE_OUT);
                workflowApplyInfoItem.setFlowTypeName(I18nUtil.i18nCode("wf_leftMenu_out"));
                workflowApplyInfoItem.setApplyStartDatetime(AttDateUtils.dateToStrAsLong(item.getStartDatetime()));
                workflowApplyInfoItem.setApplyEndDatetime(AttDateUtils.dateToStrAsLong(item.getEndDatetime()));
                workflowApplyInfoItem.setFormRecordId(leaveItem.getId());
                workflowApplyInfoItem.setNotifierUserIds(item.getNotifierPerIds());
                workflowService.start(workflowApplyInfoItem);
            }

        }
    }

    @Override
    public List<AttApiApplyPersonItem> getFirstApprove(String personPin, String flowType) {

        PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
        String personId = persPersonItem.getId();

        WorkflowApplyInfoItem workflowApplyInfoItem = new WorkflowApplyInfoItem();
        workflowApplyInfoItem.setApplyUserId(personId);
        workflowApplyInfoItem.setFlowType(flowType);

        // 流程获得环节审批人
        List<String> approvePersonIds = workflowService.getWorkflowApprove(workflowApplyInfoItem);

        List<AttApiApplyPersonItem> list = buildApplyPersonItem(approvePersonIds);

        return list;
    }

    @Override
    public List<AttApiApplyPersonItem> getLeaveApprove(String personPin, String leaveTypeNo, String startTime,
        String endTime) {

        PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
        String personId = persPersonItem.getId();

        // 计算人员请假时长
        AttLeaveItem attLeaveItem = new AttLeaveItem();
        attLeaveItem.setPersonPin(personPin);
        attLeaveItem.setStartDatetime(DateUtil.stringToDate(startTime));
        attLeaveItem.setEndDatetime(DateUtil.stringToDate(endTime));

        // 请假申请节点表达式按小时判断
        AttCalculationLeaveLong attCalculationLeaveLong = attLeaveService.calculationLeaveLong(
            attLeaveItem.getPersonPin(), attLeaveItem.getStartDatetime(), attLeaveItem.getEndDatetime(), leaveTypeNo);
        Float hour = attCalculationLeaveLong.getHour();

        WorkflowApplyInfoItem workflowApplyInfoItem = new WorkflowApplyInfoItem();
        workflowApplyInfoItem.setApplyUserId(personId);
        workflowApplyInfoItem.setFlowType(AttConstant.FLOW_TYPE_LEAVE);
        workflowApplyInfoItem.setLeaveLong(String.valueOf(hour));
        List<String> approvePersonIds = workflowService.getWorkflowApprove(workflowApplyInfoItem);

        List<AttApiApplyPersonItem> list = buildApplyPersonItem(approvePersonIds);

        return list;
    }

    @Override
    public List<AttApiApplyPersonItem> getFirstNotifier(String personPin, String flowType) {

        PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
        String personId = persPersonItem.getId();

        WorkflowApplyInfoItem workflowApplyInfoItem = new WorkflowApplyInfoItem();
        workflowApplyInfoItem.setApplyUserId(personId);
        workflowApplyInfoItem.setFlowType(flowType);

        String personIds = workflowService.getFirstNotifier(workflowApplyInfoItem);

        List<AttApiApplyPersonItem> list = new ArrayList<AttApiApplyPersonItem>();
        if (StringUtils.isNotBlank(personIds)) {
            list = buildApplyPersonItem(CollectionUtil.strToList(personIds));
        }

        return list;
    }

    /**
     * 审批人知会人 转换类型
     *
     * @param personIds
     * @return
     */
    private List<AttApiApplyPersonItem> buildApplyPersonItem(Collection<String> personIds) {
        List<AttApiApplyPersonItem> list = new ArrayList<AttApiApplyPersonItem>();
        if (!CollectionUtil.isEmpty(personIds)) {
            // 调整为按顺序单个查询人员信息，审批要按顺序
            int ind = 1;
            for (String personId : personIds) {
                PersPersonItem personItem = persPersonService.getItemById(personId);
                if (Objects.isNull(personItem)) {
                    continue;
                }
                AttApiApplyPersonItem attApiApplyPersonItem = new AttApiApplyPersonItem();
                attApiApplyPersonItem.setId(personItem.getId());
                attApiApplyPersonItem.setPin(personItem.getPin());
                attApiApplyPersonItem.setName(AttCommonUtils.buildName(personItem.getName(), personItem.getLastName()));
                attApiApplyPersonItem.setSortNo(ind++);
                attApiApplyPersonItem.setAvatarUrl(personItem.getPhotoPath());
                list.add(attApiApplyPersonItem);
            }

        }
        return list;
    }

    @Override
    public Pager findApplyTask(String personPin, String flowType, Integer taskStatus, Integer pageNo, Integer pageSize,
        String filter) {
        PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
        String personId = persPersonItem.getId();

        // 我的申请增加 区分 0 待审批 / 2 已处理 by ljf 2019/03/01
        // 流程是按 1流程未结束,2流程结束
        Integer flowStatus = null;
        if (Objects.nonNull(taskStatus)) {
            switch (taskStatus) {
                case 0:
                    flowStatus = 1;
                    break;
                case 2:
                    flowStatus = 2;
                    break;
                default:
                    break;
            }
        }

        WorkflowProcessInfoItem condition = new WorkflowProcessInfoItem();
        condition.setApplyUserId(personId);
        condition.setFlowType(flowType);
        condition.setFlowStatus(flowStatus);
        condition.setFilter(filter);

        Pager pager = workflowService.findProcessInstance(condition, pageNo, pageSize);
        if (Objects.nonNull(pager)) {
            List<WorkflowProcessInfoItem> data = (List<WorkflowProcessInfoItem>)pager.getData();
            List<AttApiApplyTaskItem> dataList = new ArrayList<AttApiApplyTaskItem>();
            if (!CollectionUtil.isEmpty(data)) {
                for (WorkflowProcessInfoItem workflowProcessInfoItem : data) {

                    String processFlowType = workflowProcessInfoItem.getFlowType();
                    String businessKey = workflowProcessInfoItem.getBusinessKey();
                    AttApiApplyTaskItem attApiApplyTaskItem = new AttApiApplyTaskItem();

                    buildItemFormData(processFlowType, businessKey, attApiApplyTaskItem);

                    if (AttConstant.FLOW_STATUS_CREATE.equals(String.valueOf(taskStatus))) {
                        attApiApplyTaskItem.setAuditStatus(AttApiConstant.AUDIT_STATUS_UNAPPROVAL);
                    }
                    attApiApplyTaskItem.setTaskId(businessKey);
                    // 审批人
                    if (StringUtils.isNotBlank(workflowProcessInfoItem.getApprovalUserId())) {
                        PersPersonItem personItem =
                            persPersonService.getSimpleItemById(workflowProcessInfoItem.getApprovalUserId());
                        if (Objects.nonNull(personItem)) {
                            attApiApplyTaskItem.setAuditNote(personItem.getName());
                        }
                    }
                    dataList.add(attApiApplyTaskItem);
                }
            }
            pager.setData(dataList);
        }

        return pager;
    }

    /**
     * 填充表单数据
     *
     * @param processFlowType
     * @param businessKey
     * @param attApiApplyTaskItem
     */
    private void buildItemFormData(String processFlowType, String businessKey,
        AttApiApplyTaskItem attApiApplyTaskItem) {
        if ("sign".equals(processFlowType)) {
            attApiApplyTaskItem.setFlowType(AttConstant.FLOW_TYPE_SIGN);
            buildSignApplyTaskItem(attApiApplyTaskItem, businessKey);
        } else if ("leave".equals(processFlowType)) {
            attApiApplyTaskItem.setFlowType(AttConstant.FLOW_TYPE_LEAVE);
            buildLeaveApplyTaskItem(attApiApplyTaskItem, businessKey);
        } else if ("overtime".equals(processFlowType)) {
            attApiApplyTaskItem.setFlowType(AttConstant.FLOW_TYPE_OVERTIME);
            buildOvertimeApplyTaskItem(attApiApplyTaskItem, businessKey);
        } else if ("trip".equals(processFlowType)) {
            attApiApplyTaskItem.setFlowType(AttConstant.FLOW_TYPE_TRIP);
            buildLeaveApplyTaskItem(attApiApplyTaskItem, businessKey);
        } else if ("out".equals(processFlowType)) {
            attApiApplyTaskItem.setFlowType(AttConstant.FLOW_TYPE_OUT);
            buildLeaveApplyTaskItem(attApiApplyTaskItem, businessKey);
        }
    }

    /**
     * 填充补签数据
     *
     * @param attApiApplyTaskItem
     * @param businessKey
     */
    private void buildSignApplyTaskItem(AttApiApplyTaskItem attApiApplyTaskItem, String businessKey) {

        AttSignItem attSignItem = attSignService.getByBusinessKey(businessKey);
        if (Objects.isNull(attSignItem)) {
            return;
        }

        // 班次名称
        String shiftName = attSignService.getSignShiftName(attSignItem);

        // 获取月补签次数统计
        Integer monthSignCount = 0;
        AttSignItem attSignItemCondition = new AttSignItem();
        attSignItemCondition.setEquals(true);
        attSignItemCondition.setPersonPin(attSignItem.getPersonPin());
        attSignItemCondition
            .setStartSignTime(AttDateUtils.getMinOfDay(AttDateUtils.getMonthOfFirstDay(attSignItem.getSignDatetime())));
        attSignItemCondition
            .setEndSignTime(AttDateUtils.getMaxOfDay(AttDateUtils.getMonthOfLastDay(attSignItem.getSignDatetime())));
        attSignItemCondition.setFlowStatusIn(AttConstant.FLOW_STATUS_CREATE + "," + AttConstant.FLOW_STATUS_COMPLETE);
        List<AttSignItem> attSignItems = attSignService.getByCondition(attSignItemCondition);
        if (CollectionUtils.isNotEmpty(attSignItems)) {
            monthSignCount = attSignItems.size();
        }

        PersPersonCacheItem personCacheItem = persPersonCacheService.getPersonCacheByPin(attSignItem.getPersonPin());
        if (personCacheItem != null) {
            attApiApplyTaskItem.setPhotoPath(personCacheItem.getPhotoPath());
        }

        // 填充补签信息
        attApiApplyTaskItem.setShiftName(shiftName);
        attApiApplyTaskItem.setBusinessType(I18nUtil.i18nCode("att_api_sign"));
        attApiApplyTaskItem.setSignTime(AttDateUtils.dateToStrAsLong(attSignItem.getSignDatetime()));
        attApiApplyTaskItem.setShiftCardValid(attSignItem.getBeforeSignRecord());
        attApiApplyTaskItem.setSignCardValid(attSignItem.getAfterSignRecord());
        attApiApplyTaskItem.setPin(attSignItem.getPersonPin());
        attApiApplyTaskItem
            .setName(AttCommonUtils.buildName(attSignItem.getPersonName(), attSignItem.getPersonLastName()));
        attApiApplyTaskItem.setApplyTime(AttDateUtils.dateToStrAsLong(attSignItem.getOperateDatetime()));
        attApiApplyTaskItem.setRemark(attSignItem.getRemark());
        attApiApplyTaskItem.setAuditStatus(AttCommonUtils.buildFlowStatus2AuditStatus(attSignItem.getFlowStatus()));
        attApiApplyTaskItem.setMonthSignCount(monthSignCount);
    }

    /**
     * 填充请假数据
     *
     * @param attApiApplyTaskItem
     * @param businessKey
     */
    private void buildLeaveApplyTaskItem(AttApiApplyTaskItem attApiApplyTaskItem, String businessKey) {
        AttLeaveItem attLeaveItem = attLeaveService.getByBusinessKey(businessKey);
        if (Objects.isNull(attLeaveItem)) {
            return;
        }

        PersPersonCacheItem personCacheItem = persPersonCacheService.getPersonCacheByPin(attLeaveItem.getPersonPin());
        if (personCacheItem != null) {
            attApiApplyTaskItem.setPhotoPath(personCacheItem.getPhotoPath());
        }

        AttLeaveTypeItem attLeaveTypeItem = attLeaveTypeService.getItemById(attLeaveItem.getLeaveTypeId());
        attApiApplyTaskItem.setBusinessType(attLeaveTypeItem.getLeaveTypeName());
        attApiApplyTaskItem.setPin(attLeaveItem.getPersonPin());
        attApiApplyTaskItem
            .setName(AttCommonUtils.buildName(attLeaveItem.getPersonName(), attLeaveItem.getPersonLastName()));
        attApiApplyTaskItem.setApplyTime(AttDateUtils.dateToStrAsLong(attLeaveItem.getOperateDatetime()));
        attApiApplyTaskItem.setStartTime(AttDateUtils.dateToStrAsLong(attLeaveItem.getStartDatetime()));
        attApiApplyTaskItem.setEndTime(AttDateUtils.dateToStrAsLong(attLeaveItem.getEndDatetime()));
        attApiApplyTaskItem.setRemark(attLeaveItem.getRemark());
        attApiApplyTaskItem.setAuditStatus(AttCommonUtils.buildFlowStatus2AuditStatus(attLeaveItem.getFlowStatus()));
        attApiApplyTaskItem.setTimeLong(buildTimeLong(attLeaveTypeItem, attLeaveItem));

        // 兼容处理新增的CloudImageUrl字段，保存云平台下发的文件路径
        String photoUrlList = StringUtils.isNotBlank(attLeaveItem.getCloudImageUrl()) ? attLeaveItem.getCloudImageUrl()
            : attLeaveItem.getLeaveImagePath();
        attApiApplyTaskItem.setPhotoUrlList(photoUrlList);
    }

    /**
     * 时长转换
     *
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/12/15 10:50
     * @since 1.0.0
     */
    private String buildTimeLong(AttLeaveTypeItem attLeaveTypeItem, AttLeaveItem attLeaveItem) {
        String timeLong = "";
        switch (attLeaveTypeItem.getConvertUnit()) {
            case AttConstant.ATT_CONVERT_UNIT_MINUTE:
                timeLong = attLeaveItem.getLeaveLong() + I18nUtil.i18nCode("common_minutes");
                break;
            case AttConstant.ATT_CONVERT_UNIT_HOUR:
                timeLong = attLeaveItem.getLeaveLongHour() + I18nUtil.i18nCode("common_hours");
                break;
            case AttConstant.ATT_CONVERT_UNIT_DAY:
                timeLong = attLeaveItem.getDays() + I18nUtil.i18nCode("common_days");
                break;
            default:
        }
        return timeLong;
    }

    /**
     * 填充加班数据
     *
     * @param attApiApplyTaskItem
     * @param businessKey
     */
    private void buildOvertimeApplyTaskItem(AttApiApplyTaskItem attApiApplyTaskItem, String businessKey) {
        AttOvertimeItem attOvertimeItem = attOvertimeService.getByBusinessKey(businessKey);
        if (Objects.isNull(attOvertimeItem)) {
            return;
        }

        // 加班类型名称
        String overtimeType = "";
        switch (attOvertimeItem.getOvertimeSign()) {
            case AttShiftConstant.OvertimeSign.NORMAL:
                overtimeType = I18nUtil.i18nCode("att_overtime_normal");
                break;
            case AttShiftConstant.OvertimeSign.REST:
                overtimeType = I18nUtil.i18nCode("att_overtime_rest");
                break;
            case AttShiftConstant.OvertimeSign.HOLIDAY:
                overtimeType = I18nUtil.i18nCode("att_shift_holidayOt");
                break;
            default:
        }

        PersPersonCacheItem personCacheItem =
            persPersonCacheService.getPersonCacheByPin(attOvertimeItem.getPersonPin());
        if (personCacheItem != null) {
            attApiApplyTaskItem.setPhotoPath(personCacheItem.getPhotoPath());
        }

        attApiApplyTaskItem.setBusinessType(overtimeType);
        attApiApplyTaskItem.setPin(attOvertimeItem.getPersonPin());
        attApiApplyTaskItem
            .setName(AttCommonUtils.buildName(attOvertimeItem.getPersonName(), attOvertimeItem.getPersonLastName()));
        attApiApplyTaskItem.setApplyTime(AttDateUtils.dateToStrAsLong(attOvertimeItem.getOperateDatetime()));
        attApiApplyTaskItem.setStartTime(AttDateUtils.dateToStrAsLong(attOvertimeItem.getStartDatetime()));
        attApiApplyTaskItem.setEndTime(AttDateUtils.dateToStrAsLong(attOvertimeItem.getEndDatetime()));
        attApiApplyTaskItem.setRemark(attOvertimeItem.getRemark());
        attApiApplyTaskItem.setAuditStatus(AttCommonUtils.buildFlowStatus2AuditStatus(attOvertimeItem.getFlowStatus()));
        // 根据配置计算加班时长带单位
        ZKResultMsg zkResultMsg = attLeaveService.getLeaveLongByType(attApiApplyTaskItem.getPin(),
            attOvertimeItem.getStartDatetime(), attOvertimeItem.getEndDatetime(), AttConstant.FLOW_TYPE_OVERTIME);
        JSONObject jsonObject = (JSONObject)zkResultMsg.getData();
        attApiApplyTaskItem.setTimeLong(jsonObject.getString("timeLong") + jsonObject.get("unit"));
    }

    @Override
    public AttApiApplyTaskDetailItem findByBusinessKey(String businessKey) {
        if (Objects.isNull(workflowService)) {
            return null;
        }
        WorkflowProcessInfoItem workflowProcessInfoItem = workflowService.findProcessByBusinessKey(businessKey);
        AttApiApplyTaskDetailItem attApiApplyTaskDetailItem = new AttApiApplyTaskDetailItem();
        if (Objects.nonNull(workflowProcessInfoItem)) {
            // 填充表单数据
            buildItemFormData(workflowProcessInfoItem.getFlowType(), workflowProcessInfoItem.getBusinessKey(),
                attApiApplyTaskDetailItem);
            // 填充知会人审批人信息
            buildPersonItem(attApiApplyTaskDetailItem, workflowProcessInfoItem);
            // 审批人
            if (StringUtils.isNotBlank(workflowProcessInfoItem.getApprovalUserId())) {
                PersPersonItem personItem =
                    persPersonService.getSimpleItemById(workflowProcessInfoItem.getApprovalUserId());
                if (Objects.nonNull(personItem)) {
                    attApiApplyTaskDetailItem.setAuditNote(personItem.getName());
                }
                attApiApplyTaskDetailItem.setAuditStatus(AttApiConstant.AUDIT_STATUS_UNAPPROVAL);
            }
        }
        return attApiApplyTaskDetailItem;
    }

    @Override
    public AttApiApplyTaskDetailItem findByTaskId(String taskId) {

        WorkflowProcessInfoItem workflowProcessInfoItem = workflowService.findHistoricTaskByTaskId(taskId);
        AttApiApplyTaskDetailItem attApiApplyTaskDetailItem = new AttApiApplyTaskDetailItem();
        if (Objects.nonNull(workflowProcessInfoItem)) {
            // 填充表单数据
            buildItemFormData(workflowProcessInfoItem.getFlowType(), workflowProcessInfoItem.getBusinessKey(),
                attApiApplyTaskDetailItem);

            // 填充知会人审批人信息
            buildPersonItem(attApiApplyTaskDetailItem, workflowProcessInfoItem);

            // 填充状态
            String auditStatus = buildTaskStatus2AuditStatus(workflowProcessInfoItem.getTaskStatus());
            attApiApplyTaskDetailItem.setAuditStatus(auditStatus);
            if ("1".equals(auditStatus)) {
                attApiApplyTaskDetailItem.setTaskId(workflowProcessInfoItem.getTaskId());
            } else {
                attApiApplyTaskDetailItem.setTaskId(null);
            }
            // 审批人
            if (StringUtils.isNotBlank(workflowProcessInfoItem.getApprovalUserId())) {
                PersPersonItem personItem =
                    persPersonService.getSimpleItemById(workflowProcessInfoItem.getApprovalUserId());
                if (Objects.nonNull(personItem)) {
                    attApiApplyTaskDetailItem.setAuditNote(personItem.getName());
                }
            }
        }
        return attApiApplyTaskDetailItem;
    }

    /**
     * 填充审批人知会人信息
     *
     * @param attApiApplyTaskDetailItem
     * @param workflowProcessInfoItem
     */
    private void buildPersonItem(AttApiApplyTaskDetailItem attApiApplyTaskDetailItem,
        WorkflowProcessInfoItem workflowProcessInfoItem) {
        // 填充审批记录
        List<AttApiApplyPersonItem> approvePersons = new ArrayList<AttApiApplyPersonItem>();
        List<WorkflowTaskInfoItem> taskInfoItemList = workflowProcessInfoItem.getTaskInfoItemList();
        if (!CollectionUtil.isEmpty(taskInfoItemList)) {
            int sortNo = 1;
            for (WorkflowTaskInfoItem workflowTaskInfoItem : taskInfoItemList) {
                AttApiApplyPersonItem item = new AttApiApplyPersonItem();
                String approvalUserId = workflowTaskInfoItem.getApprovalUserId();
                PersPersonItem personItem = persPersonService.getSimpleItemById(approvalUserId);
                if (Objects.nonNull(personItem)) {
                    item.setPin(personItem.getPin());
                    item.setName(AttCommonUtils.buildName(personItem.getName(), personItem.getLastName()));
                    item.setAvatarUrl(personItem.getPhotoPath());
                }
                item.setAuditResult(buildTaskStatus2AuditStatus(workflowTaskInfoItem.getTaskStatus()));
                item.setAuditTime(AttDateUtils.dateToStrAsLong(workflowTaskInfoItem.getApprovalDateTime()));
                item.setSortNo(sortNo++);
                item.setRemark(workflowTaskInfoItem.getComment());
                approvePersons.add(item);
            }
        }
        attApiApplyTaskDetailItem.setApprovePersons(approvePersons);

        // 填充知会人信息
        List<AttApiApplyPersonItem> notifierPersons = new ArrayList<AttApiApplyPersonItem>();
        String notifierUserIds = workflowProcessInfoItem.getNotifierUserIds();
        if (StringUtils.isNotBlank(notifierUserIds)) {
            List<PersPersonItem> personItems =
                persPersonService.getSimpleItemsByIds(CollectionUtil.strToList(notifierUserIds));
            if (!CollectionUtil.isEmpty(personItems)) {
                int sortNo = 1;
                for (PersPersonItem personItem : personItems) {
                    AttApiApplyPersonItem item = new AttApiApplyPersonItem();
                    item.setPin(personItem.getPin());
                    item.setName(AttCommonUtils.buildName(personItem.getName(), personItem.getLastName()));
                    item.setAvatarUrl(personItem.getPhotoPath());
                    item.setSortNo(sortNo++);
                    notifierPersons.add(item);
                }
            }
        }
        attApiApplyTaskDetailItem.setNotifierPersons(notifierPersons);
    }

    /**
     * 任务状态转换
     *
     * @param taskStatus
     * @return
     */
    private String buildTaskStatus2AuditStatus(String taskStatus) {
        // 审批结果：0 审批通过 1 未审批 2 已驳回 3撤销
        String auditStatus = "";
        if ("true".equalsIgnoreCase(taskStatus) || "0".equals(taskStatus)) {
            auditStatus = AttApiConstant.AUDIT_STATUS_APPROVAL;
        } else if ("false".equalsIgnoreCase(taskStatus) || "2".equals(taskStatus)) {
            auditStatus = AttApiConstant.AUDIT_STATUS_REJECT;
        } else if ("3".equals(taskStatus)) {
            auditStatus = AttApiConstant.AUDIT_STATUS_REVOKE;
        } else if (null == taskStatus || "null".equalsIgnoreCase(taskStatus) || "1".equals(taskStatus)) {
            auditStatus = AttApiConstant.AUDIT_STATUS_UNAPPROVAL;
        }
        return auditStatus;
    }

    @Override
    public Pager findPersonalTask(String personPin, String flowType, Integer pageNo, Integer pageSize, String filter) {
        PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
        String personId = persPersonItem.getId();

        WorkflowProcessInfoItem condition = new WorkflowProcessInfoItem();
        condition.setFlowType(flowType);
        condition.setApprovalUserId(personId);
        condition.setFilter(filter);

        Pager pager = workflowService.findRunTimeTaskInstance(condition, pageNo, pageSize);

        if (Objects.nonNull(pager)) {
            List<WorkflowProcessInfoItem> data = (List<WorkflowProcessInfoItem>)pager.getData();
            List<String> dataList = new ArrayList<>();
            if (!CollectionUtil.isEmpty(data)) {
                data.forEach(workflowProcessInfoItem -> {

                    String processFlowType = workflowProcessInfoItem.getFlowType();
                    String businessKey = workflowProcessInfoItem.getBusinessKey();
                    String approvalUserId = workflowProcessInfoItem.getApprovalUserId();

                    AttApiApplyTaskItem attApiApplyTaskItem = new AttApiApplyTaskItem();
                    // 填充表单数据
                    buildItemFormData(processFlowType, businessKey, attApiApplyTaskItem);
                    // 流程taskId
                    attApiApplyTaskItem.setTaskId(workflowProcessInfoItem.getTaskId());
                    // 任务状态
                    attApiApplyTaskItem
                        .setAuditStatus(buildTaskStatus2AuditStatus(workflowProcessInfoItem.getTaskStatus()));
                    // 审批人
                    if (StringUtils.isNotBlank(approvalUserId)) {
                        PersPersonItem personItem = persPersonService.getSimpleItemById(approvalUserId);
                        if (Objects.nonNull(personItem)) {
                            attApiApplyTaskItem.setAuditNote(personItem.getName());
                        }
                    }

                    dataList.add(JSONObject.toJSONString(attApiApplyTaskItem));
                });
            }
            pager.setData(dataList);
        }

        return pager;
    }

    @Override
    public Pager findApprovedTask(String personPin, String flowType, Integer pageNo, Integer pageSize, String filter) {
        PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
        String personId = persPersonItem.getId();

        WorkflowProcessInfoItem condition = new WorkflowProcessInfoItem();
        condition.setFlowType(flowType);
        condition.setApprovalUserId(personId);
        condition.setFilter(filter);

        Pager pager = workflowService.finHistoryTaskInstance(condition, pageNo, pageSize);

        if (Objects.nonNull(pager)) {
            List<WorkflowProcessInfoItem> data = (List<WorkflowProcessInfoItem>)pager.getData();
            List<String> dataList = new ArrayList<>();
            if (!CollectionUtil.isEmpty(data)) {
                data.forEach(workflowProcessInfoItem -> {
                    AttApiApplyTaskItem attApiApplyTaskItem = new AttApiApplyTaskItem();
                    // 填充表单数据
                    buildItemFormData(workflowProcessInfoItem.getFlowType(), workflowProcessInfoItem.getBusinessKey(),
                        attApiApplyTaskItem);
                    // 流程businessKey
                    attApiApplyTaskItem.setTaskId(workflowProcessInfoItem.getBusinessKey());
                    // 任务状态
                    attApiApplyTaskItem
                        .setAuditStatus(buildTaskStatus2AuditStatus(workflowProcessInfoItem.getTaskStatus()));
                    // 审批人
                    if (StringUtils.isNotBlank(workflowProcessInfoItem.getApprovalUserId())) {
                        PersPersonItem personItem =
                            persPersonService.getSimpleItemById(workflowProcessInfoItem.getApprovalUserId());
                        if (Objects.nonNull(personItem)) {
                            attApiApplyTaskItem.setAuditNote(personItem.getName());
                        }
                        if (Objects.nonNull(workflowProcessInfoItem.getAuditTime())) {
                            attApiApplyTaskItem
                                .setAuditTime(AttDateUtils.dateToStrAsLong(workflowProcessInfoItem.getAuditTime()));
                        }
                    }
                    dataList.add(JSONObject.toJSONString(attApiApplyTaskItem));
                });
            }
            pager.setData(dataList);
        }
        return pager;
    }

    @Override
    @Transactional
    public void completeTask(String personPin, String taskId, String approve, String comment, String notifierPins) {
        PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
        String personId = persPersonItem.getId();
        // notifierPerIds 知会人 personPins转成personIds传给流程
        String notifierPerIds = null;
        if (StringUtils.isNotBlank(notifierPins)) {
            List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(notifierPins);
            if (CollectionUtils.isNotEmpty(persPersonItemList)) {
                notifierPerIds = CollectionUtil.getItemIds(persPersonItemList);
            }
        }

        WorkflowCompleteInfoItem workflowCompleteInfoItem = new WorkflowCompleteInfoItem();
        workflowCompleteInfoItem.setTaskId(taskId);
        workflowCompleteInfoItem.setApprovalUserId(personId);
        workflowCompleteInfoItem.setApproval(approve);
        workflowCompleteInfoItem.setComment(comment);
        workflowCompleteInfoItem.setNotifierUserIds(notifierPerIds);

        // 先通知云端设置代办消息为已读
        sendTodoMsgReadByTaskId(taskId);

        workflowService.complete(workflowCompleteInfoItem);
    }

    @Override
    public Pager findPersonalHisTask(AttFlowTaskItem condition, int pageNo, int pageSize) {

        WorkflowProcessInfoItem workflowProcessInfoItem = new WorkflowProcessInfoItem();
        workflowProcessInfoItem.setFlowType(condition.getFlowType());
        workflowProcessInfoItem.setApplyUserId(condition.getPersonId());
        workflowProcessInfoItem.setApprovalUserId(condition.getLoginUserId());

        Pager pager = workflowService.finHistoryTaskInstance(workflowProcessInfoItem, pageNo, pageSize);
        List<AttFlowTaskItem> dataList = new ArrayList<>();
        if (Objects.nonNull(pager)) {
            List<WorkflowProcessInfoItem> data = (List<WorkflowProcessInfoItem>)pager.getData();
            if (!CollectionUtil.isEmpty(data)) {
                data.forEach(processInfoItem -> {
                    AttFlowTaskItem item = new AttFlowTaskItem();
                    item.setId(processInfoItem.getBusinessKey());
                    PersPersonItem personItem = persPersonService.getItemById(processInfoItem.getApplyUserId());
                    if (Objects.nonNull(personItem)) {
                        item.setPersonId(personItem.getId());
                        item.setPersonPin(personItem.getPin());
                        item.setPersonName(personItem.getName());
                        item.setDeptCode(personItem.getDeptCode());
                        item.setDeptName(personItem.getDeptName());
                    }
                    item.setBusinessKey(processInfoItem.getBusinessKey());
                    PersPersonItem approvalUser =
                        persPersonService.getSimpleItemById(processInfoItem.getApprovalUserId());
                    if (Objects.nonNull(approvalUser)) {
                        item.setOperateUser(approvalUser.getName() + "(" + approvalUser.getPin() + ")");
                    }
                    item.setHandleButton(false);
                    item.setTaskStatus(processInfoItem.getFlowStatus());
                    item.setFlowType(processInfoItem.getFlowType());
                    dataList.add(item);
                });
            }
        }
        pager.setData(dataList);
        return pager;
    }

    @Override
    public Pager findMyApplyList(AttFlowTaskItem condition, int pageNo, int pageSize) {
        WorkflowProcessInfoItem workflowProcessInfoItem = new WorkflowProcessInfoItem();
        workflowProcessInfoItem.setApplyUserId(condition.getPersonId());
        workflowProcessInfoItem.setFlowStatus(condition.getTaskStatus());
        workflowProcessInfoItem.setFlowType(condition.getFlowType());
        Pager pager = workflowService.findProcessInstance(workflowProcessInfoItem, pageNo, pageSize);
        if (Objects.nonNull(pager)) {
            List<WorkflowProcessInfoItem> data = (List<WorkflowProcessInfoItem>)pager.getData();
            if (!CollectionUtil.isEmpty(data)) {
                List<AttFlowTaskItem> dataList = new ArrayList<>();
                String personIds = CollectionUtil.getPropertys(data, WorkflowProcessInfoItem::getApplyUserId);
                List<PersPersonItem> personItems = persPersonService.getItemsByIds(personIds);
                Map<String, PersPersonItem> persPersonItemMap =
                    CollectionUtil.listToKeyMap(personItems, PersPersonItem::getId);
                data.forEach(item -> {
                    AttFlowTaskItem attFlowTaskItem = new AttFlowTaskItem();
                    attFlowTaskItem.setId(item.getBusinessKey());
                    attFlowTaskItem.setBusinessKey(item.getBusinessKey());
                    if (persPersonItemMap.containsKey(item.getApplyUserId())) {
                        PersPersonItem personItem = persPersonItemMap.get(item.getApplyUserId());
                        attFlowTaskItem.setPersonPin(personItem.getPin());
                        attFlowTaskItem.setPersonName(personItem.getName());
                        attFlowTaskItem.setDeptCode(personItem.getDeptCode());
                        attFlowTaskItem.setDeptName(personItem.getDeptName());
                    }
                    if (StringUtils.isNotBlank(item.getApprovalUserId())) {
                        PersPersonItem personItem = persPersonService.getSimpleItemById(item.getApprovalUserId());
                        if (Objects.nonNull(personItem)) {
                            attFlowTaskItem.setOperateUser(personItem.getName() + "(" + personItem.getPin() + ")");
                        }
                    }
                    attFlowTaskItem.setFlowType(item.getFlowType());
                    attFlowTaskItem.setTaskStatus(item.getFlowStatus());
                    attFlowTaskItem.setHandleButton(false);
                    dataList.add(attFlowTaskItem);
                });
                pager.setData(dataList);
            }
        }
        return pager;
    }

    @Override
    public Pager findMyApproveList(AttFlowTaskItem condition, int pageNo, int pageSize) {
        WorkflowProcessInfoItem workflowProcessInfoItem = new WorkflowProcessInfoItem();
        workflowProcessInfoItem.setApplyUserId(condition.getPersonId());
        workflowProcessInfoItem.setApprovalUserId(condition.getLoginUserId());
        workflowProcessInfoItem.setFlowType(condition.getFlowType());
        Pager pager = workflowService.findRunTimeTaskInstance(workflowProcessInfoItem, pageNo, pageSize);
        if (Objects.nonNull(pager)) {
            List<WorkflowProcessInfoItem> data = (List<WorkflowProcessInfoItem>)pager.getData();
            if (!CollectionUtil.isEmpty(data)) {
                List<AttFlowTaskItem> dataList = new ArrayList<>();
                String personIds = CollectionUtil.getPropertys(data, WorkflowProcessInfoItem::getApplyUserId);
                List<PersPersonItem> personItems = persPersonService.getItemsByIds(personIds);
                Map<String, PersPersonItem> persPersonItemMap =
                    CollectionUtil.listToKeyMap(personItems, PersPersonItem::getId);
                data.forEach(item -> {
                    AttFlowTaskItem attFlowTaskItem = new AttFlowTaskItem();
                    attFlowTaskItem.setId(item.getTaskId());
                    attFlowTaskItem.setTaskId(item.getTaskId());
                    attFlowTaskItem.setBusinessKey(item.getBusinessKey());
                    if (persPersonItemMap.containsKey(item.getApplyUserId())) {
                        PersPersonItem personItem = persPersonItemMap.get(item.getApplyUserId());
                        attFlowTaskItem.setPersonPin(personItem.getPin());
                        attFlowTaskItem.setPersonName(personItem.getName());
                        attFlowTaskItem.setDeptCode(personItem.getDeptCode());
                        attFlowTaskItem.setDeptName(personItem.getDeptName());
                    }
                    if (StringUtils.isNotBlank(item.getApprovalUserId())) {
                        PersPersonItem personItem = persPersonService.getSimpleItemById(item.getApprovalUserId());
                        if (Objects.nonNull(personItem)) {
                            attFlowTaskItem.setOperateUser(personItem.getName() + "(" + personItem.getPin() + ")");
                        }
                    }
                    attFlowTaskItem.setFlowType(item.getFlowType());
                    attFlowTaskItem.setTaskStatus(item.getFlowStatus());
                    dataList.add(attFlowTaskItem);
                });
                pager.setData(dataList);
            }
        }
        return pager;
    }

    @Override
    public Map approve(String taskId) {
        WorkflowProcessInfoItem workflowProcessInfoItem = workflowService.findHistoricTaskByTaskId(taskId);
        if (Objects.nonNull(workflowProcessInfoItem)) {
            Map<String, Object> map = new HashMap<>(16);
            map.put("item",
                getItemByFlowType(workflowProcessInfoItem.getFlowType(), workflowProcessInfoItem.getBusinessKey()));
            map.put("task", workflowProcessInfoItem);
            map.put("flowType", workflowProcessInfoItem.getFlowType());
            return map;
        }
        return null;
    }

    @Override
    public Map detail(String businessKey) {
        WorkflowProcessInfoItem workflowProcessInfoItem = workflowService.findProcessByBusinessKey(businessKey);
        if (Objects.nonNull(workflowProcessInfoItem)) {
            Map<String, Object> map = new HashMap<>(16);
            map.put("item",
                getItemByFlowType(workflowProcessInfoItem.getFlowType(), workflowProcessInfoItem.getBusinessKey()));
            map.put("task", workflowProcessInfoItem);
            map.put("flowType", workflowProcessInfoItem.getFlowType());
            return map;
        }
        return null;
    }

    private BaseItem getItemByFlowType(String flowType, String businessKey) {
        if ("sign".equals(flowType)) {
            return attSignService.getByBusinessKey(businessKey);
        } else if ("leave".equals(flowType)) {
            return attLeaveService.getByBusinessKey(businessKey);
        } else if ("overtime".equals(flowType)) {
            return attOvertimeService.getByBusinessKey(businessKey);
        } else if ("trip".equals(flowType)) {
            return attLeaveService.getByBusinessKey(businessKey);
        } else if ("out".equals(flowType)) {
            return attLeaveService.getByBusinessKey(businessKey);
        }
        return null;
    }

    @Override
    public Map<String, String> apply(Object o, String personId, String flowType) {
        return null;
    }

    @Override
    @Transactional
    public void complete(String personId, String taskId, String pass, String notifierPerIds) {

        WorkflowCompleteInfoItem workflowCompleteInfoItem = new WorkflowCompleteInfoItem();
        workflowCompleteInfoItem.setTaskId(taskId);
        workflowCompleteInfoItem.setApprovalUserId(personId);
        workflowCompleteInfoItem.setApproval(pass);
        workflowCompleteInfoItem.setComment("");
        workflowCompleteInfoItem.setNotifierUserIds(notifierPerIds);

        workflowService.complete(workflowCompleteInfoItem);
    }

    @Override
    @Transactional
    public ZKResultMsg revokeProcess(String businessKey, String personPin, String revokeReason) {
        PersPersonItem personItem = persPersonService.getItemByPin(personPin);
        if (Objects.nonNull(personItem)) {
            String personId = personItem.getId();

            // 先通知云端设置代办消息为已读
            sendTodoMsgReadByBusinessKey(businessKey);

            ZKResultMsg resultMsg = workflowService.revokeProcess(businessKey, null, personId, revokeReason);
            if (resultMsg != null && resultMsg.getData() != null) {
                JSONObject data = new JSONObject();
                Map<String, String> dataMap = (Map<String, String>)resultMsg.getData();
                String approvePersonId = MapUtils.getString(dataMap, "personId");
                String approvePersonPin = "";
                // 返回云端下一级审批人信息，设置消息已读
                data.put("taskId", MapUtils.getString(dataMap, "taskId"));
                if (StringUtils.isNotBlank(approvePersonId)) {
                    personItem = persPersonService.getItemById(approvePersonId);
                    if (personItem != null) {
                        approvePersonPin = personItem.getPin();
                    }
                }
                data.put("pin", approvePersonPin);
                resultMsg.setData(data);
            }
            return resultMsg;
        }
        return ZKResultMsg.failMsg("att_excp_notExisetPerson");
    }

    @Override
    public AttApiSignDayValidCardItem getSignDayScheduleAndRecords(String personPin, String attDate) {
        AttApiSignDayValidCardItem attApiSignDayValidCardItem = new AttApiSignDayValidCardItem();
        PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
        String deptId = persPersonItem.getDeptId();
        String groupId = null;
        AttPersonItem attPersonItem = attPersonService.getItemByPersonPin(personPin);
        if (Objects.nonNull(attPersonItem)) {
            groupId = attPersonItem.getGroupId();
        }

        Date startDate = DateUtil.getDayBeginTime(AttDateUtils.stringToYmdDate(attDate));
        Date endDate = DateUtil.getDayEndTime(AttDateUtils.stringToYmdDate(attDate));
        // TODO
        // attCalculateService.calculate(personId, null, AttDateUtils.dateToStrAsLong(startDate),
        // AttDateUtils.dateToStrAsLong(endDate));
        List<AttRecordItem> attRecordItems =
            attRecordService.getByPinsAndAttDate(CollectionUtil.strToList(personPin), startDate, endDate);

        if (!CollectionUtil.isEmpty(attRecordItems)) {
            AttRecordItem attRecordItem = attRecordItems.get(0);
            attApiSignDayValidCardItem.setPersonPin(attRecordItem.getPersonPin());
            attApiSignDayValidCardItem.setDate(AttDateUtils.dateToStrAsShort(attRecordItem.getAttDate()));
            attApiSignDayValidCardItem.setShiftName(attRecordItem.getShiftName());
            List<AttApiValidCardItem> appAttValidCardItems = new ArrayList<>(16);
            String shiftTimeData = attRecordItem.getShiftTimeData();
            String cardValidData = attRecordItem.getCardValidData();

            if (StringUtils.isNotBlank(shiftTimeData)) {
                String[] shiftTimes = shiftTimeData.split(";");
                String[] cardValidTimes = null;
                if (null != cardValidData) {
                    cardValidTimes = cardValidData.split(";");
                }
                if (null != shiftTimes) {
                    String noSignIn = attParamService.getNoSignIn();
                    String noSignOff = attParamService.getNoSignOff();
                    String crossDay = attParamService.getCrossDay();
                    String firstDay = attDate;
                    String secondDay = attDate;

                    for (int i = 0, len = shiftTimes.length; i < len; i++) {
                        String[] shiftStartEndTime = shiftTimes[i].split("-", 2);
                        AttApiValidCardItem attApiValidCardItem = new AttApiValidCardItem();
                        attApiValidCardItem.setShouldStartWorkTime(shiftStartEndTime[0]);
                        attApiValidCardItem.setShouldEndWorkTime(shiftStartEndTime[1]);

                        boolean isCrossDay = StringUtils.compare(shiftStartEndTime[0], shiftStartEndTime[1]) > 0;
                        if (isCrossDay) {
                            if (AttRuleEnum.CrossDay.getValueOne().equals(crossDay)) {
                                secondDay = AttDateUtils
                                    .dateToStrAsShort(DateUtil.addDay(AttDateUtils.stringToYmdDate(attDate), 1));
                            } else if (AttRuleEnum.CrossDay.getValueTwo().equals(crossDay)) {
                                firstDay = AttDateUtils
                                    .dateToStrAsShort(DateUtil.addDay(AttDateUtils.stringToYmdDate(attDate), -1));
                            }
                        }

                        if (null != cardValidTimes && cardValidTimes.length > i) {
                            String[] validStartEndTime = cardValidTimes[i].split("-", 2);
                            String startWorkTime =
                                StringUtils.equals(noSignIn, validStartEndTime[0]) ? null : validStartEndTime[0];
                            String endWorkTime =
                                StringUtils.equals(noSignOff, validStartEndTime[1]) ? null : validStartEndTime[1];
                            String startWorkStatus = "0";
                            String endWorkStatus = "0";
                            if (null != startWorkTime) {
                                // 迟到
                                if (StringUtils.compare(startWorkTime, shiftStartEndTime[0]) > 0) {
                                    startWorkStatus = "1";
                                }
                            } else {
                                // 未打卡
                                startWorkTime = "W";

                                Date shiftStartDateTime = DateUtil.stringToDate(firstDay + " " + shiftStartEndTime[0],
                                    DateUtil.DateStyle.YYYY_MM_DD_HH_MM);
                                if (System.currentTimeMillis() > shiftStartDateTime.getTime()) {
                                    startWorkTime = "L";
                                    startWorkStatus = "1";
                                }
                            }
                            if (null != endWorkTime) {
                                // 早退
                                if (StringUtils.compare(endWorkTime, shiftStartEndTime[1]) < 0) {
                                    endWorkStatus = "1";
                                }
                            } else {
                                endWorkTime = "W";
                                Date shiftEndDateTime = DateUtil.stringToDate(secondDay + " " + shiftStartEndTime[1],
                                    DateUtil.DateStyle.YYYY_MM_DD_HH_MM);
                                if (System.currentTimeMillis() > shiftEndDateTime.getTime()) {
                                    endWorkTime = "L";
                                    endWorkStatus = "1";
                                }
                            }
                            attApiValidCardItem.setStartWorkTime(startWorkTime);
                            attApiValidCardItem.setEndWorkTime(endWorkTime);
                            attApiValidCardItem.setStartWorkStatus(startWorkStatus);
                            attApiValidCardItem.setEndWorkStatus(endWorkStatus);
                        }
                        appAttValidCardItems.add(attApiValidCardItem);
                    }
                }
            }
            attApiSignDayValidCardItem.setAppAttValidCardItems(appAttValidCardItems);
        }
        return attApiSignDayValidCardItem;
    }

    @Override
    public AttApiSignDayValidCardItem getSignDaySchedule(String personPin, String attDateStr) {

        // 获取人员排班详情
        Date attDate = DateUtil.getDayBeginTime(AttDateUtils.stringToYmdDate(attDateStr));
        Map<String, List<AttPersonSchBO>> personSchDataMap =
            attPersonSchDataService.getPersonAllSchData(Arrays.asList(personPin), attDate, attDate);

        // 时间段集合
        List<AttTimeSlotItem> attTimeSlotItemList = attTimeSlotService.getByCondition(new AttTimeSlotItem());
        Map<String, AttTimeSlotItem> attTimeSlotItemMap = CollectionUtil.itemListToIdMap(attTimeSlotItemList);

        // 组装排班详情
        AttApiSignDayValidCardItem attApiSignDayValidCardItem = new AttApiSignDayValidCardItem();
        attApiSignDayValidCardItem.setPersonPin(personPin);
        attApiSignDayValidCardItem.setDate(AttDateUtils.dateToStrAsShort(attDate));
        List<AttApiValidCardItem> attApiValidCardItemList = new ArrayList<>();
        for (Map.Entry<String, List<AttPersonSchBO>> entry : personSchDataMap.entrySet()) {
            List<AttPersonSchBO> attPersonSchBOList = entry.getValue();
            if (!CollectionUtil.isEmpty(attPersonSchBOList)) {
                AttPersonSchBO attPersonSchBO = attPersonSchBOList.get(0);
                attApiSignDayValidCardItem.setShiftName(attPersonSchBO.getAttShiftName());
                List<AttTimeSlotBO> attTimeSlotBOList = attPersonSchBO.getAttTimeSlotArray();
                if (CollectionUtil.isEmpty(attTimeSlotBOList)) {
                    continue;
                }
                StringBuffer timeSlots = new StringBuffer();
                for (AttTimeSlotBO attTimeSlotBO : attTimeSlotBOList) {
                    AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());
                    AttApiValidCardItem attApiValidCardItem = new AttApiValidCardItem();
                    if (AttConstant.PERIODTYPE_NORMAL.equals(attTimeSlotItem.getPeriodType())) {
                        attApiValidCardItem.setShouldStartWorkTime(attTimeSlotItem.getToWorkTime());
                        attApiValidCardItem.setShouldEndWorkTime(attTimeSlotItem.getOffWorkTime());
                    } else {
                        attApiValidCardItem.setShouldStartWorkTime(attTimeSlotItem.getStartSignInTime());
                        attApiValidCardItem.setShouldEndWorkTime(attTimeSlotItem.getEndSignOffTime());
                    }
                    timeSlots.append(attTimeSlotItem.getPeriodName());
                    attApiValidCardItemList.add(attApiValidCardItem);
                }
                attApiSignDayValidCardItem.setTimeSlots(timeSlots.toString());
            }
        }
        attApiSignDayValidCardItem.setAppAttValidCardItems(attApiValidCardItemList);
        return attApiSignDayValidCardItem;
    }

    @Override
    @Transactional
    public ZKResultMsg deleteProcessInstance(String businessKey) {
        if (Objects.isNull(workflowService)) {
            return new ZKResultMsg();
        }
        if (StringUtils.isNotBlank(businessKey)) {

            // 先通知云端设置代办消息为已读
            sendTodoMsgReadByBusinessKey(businessKey);

            return workflowService.deleteProcessInstance(businessKey);
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * // 先通知云端设置代办消息为已读
     *
     * @param businessKey:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/3/6 17:22
     * @since 1.0.0
     */
    private void sendTodoMsgReadByBusinessKey(String businessKey) {

        String taskId = workflowService.getRuntimeProcessTaskId(businessKey);
        if (StringUtils.isBlank(taskId)) {
            return;
        }

        WorkflowProcessInfoItem workflowProcessInfoItem = workflowService.findHistoricTaskByTaskId(taskId);
        if (workflowProcessInfoItem == null) {
            return;
        }

        String approvalUserId = workflowProcessInfoItem.getApprovalUserId();
        if (StringUtils.isBlank(approvalUserId)) {
            return;
        }

        PersPersonItem persPersonItem = persPersonService.getItemById(approvalUserId);
        if (persPersonItem == null) {
            return;
        }

        BaseMessageItem baseMessageItem = new BaseMessageItem();
        baseMessageItem.setReceiverId(persPersonItem.getPin());
        baseMessageItem.setBusinessId(taskId);
        log.info("Notify the cloud to set the agent message as read, ReceiverId = {}, BusinessId = {}",
            persPersonItem.getPin(), businessKey);
        baseMessageService.sendSaveTodoMsgRead(baseMessageItem);
    }

    private void sendTodoMsgReadByTaskId(String taskId) {

        WorkflowProcessInfoItem workflowProcessInfoItem = workflowService.findHistoricTaskByTaskId(taskId);
        if (workflowProcessInfoItem == null) {
            return;
        }

        String approvalUserId = workflowProcessInfoItem.getApprovalUserId();
        if (StringUtils.isBlank(approvalUserId)) {
            return;
        }

        PersPersonItem persPersonItem = persPersonService.getItemById(approvalUserId);
        if (persPersonItem == null) {
            return;
        }

        BaseMessageItem baseMessageItem = new BaseMessageItem();
        baseMessageItem.setReceiverId(persPersonItem.getPin());
        baseMessageItem.setBusinessId(taskId);
        log.info("Notify the cloud to set the agent message as read, ReceiverId = {}, TaskId = {}",
            persPersonItem.getPin(), taskId);
        baseMessageService.sendSaveTodoMsgRead(baseMessageItem);
    }

    @Override
    public String calcApplyTimeLong(String personPin, String startTime, String endTime) {
        PersPersonItem personItem = persPersonService.getItemByPin(personPin);
        Date startDateTime = DateUtil.stringToDate(startTime);
        Date endDateTime = DateUtil.stringToDate(endTime);

        // TODO 获取假种配置
        AttLeaveTypeItem attLeaveTypeItem = new AttLeaveTypeItem();
        attLeaveTypeItem.setConvertCount(1.0);
        attLeaveTypeItem.setConvertType(AttConstant.ATT_CONVERT_ABORT);
        attLeaveTypeItem.setConvertUnit(AttConstant.ATT_CONVERT_UNIT_MINUTE);

        int timeMinutes =
            attLeaveService.calLeaveTimeMinutes(personItem.getId(), startDateTime, endDateTime, attLeaveTypeItem);
        String leaveHours = attParamService.minutesToHourFormat(new BigDecimal(timeMinutes));
        return leaveHours;
    }

    @Override
    public ZKResultMsg isExistApply(String personPin, Date startTime, Date endTime) {
        ZKResultMsg rest = new ZKResultMsg();
        // 判断人员是否存在
        AttPersonItem attPersonItem = attPersonService.getItemByPersonPin(personPin);
        if (attPersonItem == null) {
            rest.setRet("fail");
            rest.setMsg("att_h5_pers_personNoExist");
            return rest;
        }
        // 判断是否存在请假
        List<AttLeave> attLeaveList = attLeaveDao.getListByPinAndDateAndValidStatus(personPin, startTime, endTime);
        if (attLeaveList.size() > 0) {
            rest.setRet("fail");
            rest.setMsg("att_apply_leaveTips");
            return rest;
        }
        // 判断是否存在出差
        List<AttTrip> attTripList = attTripDao.getListByPinAndDateAndValidStatus(personPin, startTime, endTime);
        if (attTripList.size() > 0) {
            rest.setRet("fail");
            rest.setMsg("att_apply_tripTips");
            return rest;
        }
        // 判断是否存在外出
        List<AttOut> attOutList = attOutDao.getListByPinAndDateAndValidStatus(personPin, startTime, endTime);
        if (attOutList.size() > 0) {
            rest.setRet("fail");
            rest.setMsg("att_apply_outTips");
            return rest;
        }
        // 判断是否存在加班
        List<AttOvertime> attOvertimeList =
            attOvertimeDao.getListByPinAndDateAndValidStatus(personPin, startTime, endTime);
        if (attOvertimeList.size() > 0) {
            rest.setRet("fail");
            rest.setMsg("att_apply_overtimeTips");
            return rest;
        }
        return rest;
    }

    /**
     * 判断申请时间是否超过上上个月
     */
    private void checkApplyStartDateIsOverdue(Date startDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -2);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date theFirstDayOfLastMonth = DateUtil.getDayBeginTime(calendar.getTime());
        if (theFirstDayOfLastMonth.compareTo(startDate) > 0) {
            throw new ZKBusinessException(I18nUtil.i18nCode("att_apply_overLastMonth"));
        }
    }

    @Override
    public ZKResultMsg checkApplyAndWorkTimeValid(String personPin, String flowType, String startTime, String endTime) {

        Date startDatetime = DateUtil.stringToDate(startTime);
        Date endDatetime = DateUtil.stringToDate(endTime);

        Map<String, List<AttPersonSchBO>> personSchData =
            attPersonSchDataService.getPersonSchData(Collections.singletonList(personPin), startDatetime, endDatetime);
        // 人员申请时间内没有排班 不允许申请
        if (CollectionUtil.isEmpty(personSchData)) {
            return ZKResultMsg.failMsg(I18nUtil.i18nCode("att_self_thisTimeNoSch"));
        }

        List<AttTimeSlotItem> allTimeSlotItem = attTimeSlotService.getAllTimeSlotItem();
        Map<String, AttTimeSlotItem> attTimeSlotItemMap = CollectionUtil.itemListToIdMap(allTimeSlotItem);

        // 是否有排班时间段
        boolean hasShiftTimeSlot = false;
        for (Map.Entry<String, List<AttPersonSchBO>> entry : personSchData.entrySet()) {
            List<AttPersonSchBO> attPersonSchBOList = entry.getValue();
            if (CollectionUtil.isEmpty(attPersonSchBOList)) {
                continue;
            }
            AttPersonSchBO attPersonSchBO = attPersonSchBOList.get(0);
            List<AttTimeSlotBO> attTimeSlotArray = attPersonSchBO.getAttTimeSlotArray();
            if (CollectionUtil.isEmpty(attTimeSlotArray)) {
                continue;
            }
            hasShiftTimeSlot = true;

            for (AttTimeSlotBO attTimeSlotBO : attTimeSlotArray) {

                AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());

                // 排班时间是否有交集
                String toWorkTime =
                    String.format("%s %s:00", attTimeSlotBO.getFirstDay(), attTimeSlotItem.getToWorkTime());
                String offWorkTime =
                    String.format("%s %s:00", attTimeSlotBO.getSecondDay(), attTimeSlotItem.getOffWorkTime());

                // 判断申请时间和排班时间是否有交集
                if (StringUtils.compare(endTime, toWorkTime) > 0 && StringUtils.compare(offWorkTime, startTime) > 0) {
                    // 请假/外出/出差 申请时间和上班时间有交集 允许申请
                    if (AttConstant.FLOW_TYPE_LEAVE.equals(flowType) || AttConstant.FLOW_TYPE_OUT.equals(flowType)
                        || AttConstant.FLOW_TYPE_TRIP.equals(flowType)) {
                        return ZKResultMsg.successMsg();
                    }
                    // 加班 申请时间和上班时间有交集 不允许申请
                    else if (AttConstant.FLOW_TYPE_OVERTIME.equals(flowType)) {
                        return ZKResultMsg.failMsg(I18nUtil.i18nCode("att_self_applyAndWorkTimeOverlap"));
                    }
                }

            }

        }
        // 没有排上班时间段 不允许申请
        if (!hasShiftTimeSlot) {
            return ZKResultMsg.failMsg(I18nUtil.i18nCode("att_self_thisTimeNoSch"));
        }

        return ZKResultMsg.successMsg();
    }
}
