package com.zkteco.zkbiosecurity.att.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.*;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.bean.AttRuleParamBean;
import com.zkteco.zkbiosecurity.att.cache.AttCacheManager;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.enums.AttRuleEnum;
import com.zkteco.zkbiosecurity.att.service.AttCloudMessageSendService;
import com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 考勤规则
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:34
 * @since 1.0.0
 */
@Slf4j
@Service
public class AttParamServiceImpl implements AttParamService {

    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AttCloudMessageSendService attCloudMessageSendService;
    @Autowired
    private AttCacheManager attCacheManager;
    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;

    @Value("${system.customDateFormat:false}")
    private Boolean customDateFormat;

    @Override
    @Transactional
    public void saveItem(Map<String, String> params) {
        baseSysParamService.saveParams(params);
    }

    @Override
    public Map<String, String> getAttParams() {
        return baseSysParamService.getParamsByModule("att");
    }

    @Override
    public String getFindSchSort() {
        // 默认取全局规则
        String schSort = baseSysParamService.getValByName(AttRuleEnum.FindSchSort.getKey());
        return schSort;
    }

    @Override
    public String getCrossDay(String groupId, String deptId, boolean querySql) {
        String crossDay = baseSysParamService.getValByName(AttRuleEnum.CrossDay.getKey());
        return crossDay;
    }

    @Override
    public String getAutoAddDevice() {
        String autoAdd = baseSysParamService.getValByName(AttRuleEnum.AutoAdd.getKey());
        return autoAdd;
    }

    @Override
    public String getReceivePersonOnlyDb() {
        String receivePersonOnlyDb = baseSysParamService.getValByName(AttRuleEnum.ReceivePersonOnlyDb.getKey());
        if (StringUtils.isBlank(receivePersonOnlyDb)) {
            receivePersonOnlyDb = "0";
        }
        return receivePersonOnlyDb;
    }

    @Override
    public String getNoSignIn() {
        AttLeaveTypeItem attLeaveTypeItem =
            attLeaveTypeService.getItemByLeaveTypeNo(AttCalculationConstant.AttAttendStatus.NO_CHECK_IN);
        if (attLeaveTypeItem != null) {
            return attLeaveTypeItem.getSymbol();
        }
        return I18nUtil.i18nCode("att_other_noSignIn");
    }

    @Override
    public String getNoSignOff() {
        AttLeaveTypeItem attLeaveTypeItem =
            attLeaveTypeService.getItemByLeaveTypeNo(AttCalculationConstant.AttAttendStatus.NO_CHECK_OUT);
        if (attLeaveTypeItem != null) {
            return attLeaveTypeItem.getSymbol();
        }
        return I18nUtil.i18nCode("att_other_noSignOff");
    }

    @Override
    public String getDecimal() {
        return getParamValueByName(AttRuleEnum.Decimal.getKey());
    }

    /**
     * 根据名称获取参数
     *
     * @param name
     * @return
     */
    private String getParamValueByName(String name) {
        return baseSysParamService.getValByName(name);
    }

    @Override
    public String dayFormat(String dayStr) {
        if (StringUtils.isBlank(dayStr)) {
            dayStr = "0.00";
        }
        DecimalFormat decimalFormat = getDecimalFormat();
        return decimalFormat.format(BigDecimal.valueOf(Double.parseDouble(dayStr)));
    }

    @Override
    public DecimalFormat getDecimalFormat() {
        Locale enlocale = new Locale("en", "US");
        DecimalFormat decimalFormat = (DecimalFormat)NumberFormat.getNumberInstance(enlocale);
        Integer countConvertDecimal = Integer.valueOf(getDecimal());
        switch (countConvertDecimal) {
            case 0:
                // 保留0位小数
                decimalFormat.applyPattern("0");
                break;
            case 1:
                // 保留1位小数
                decimalFormat.applyPattern("0.0");
                break;
            case 2:
                // 保留2位小数
                decimalFormat.applyPattern("0.00");
                break;
            default:
        }
        decimalFormat.setRoundingMode(RoundingMode.HALF_UP);

        return decimalFormat;
    }

    @Override
    public String minutesToHourFormat(BigDecimal minutesTime) {
        Integer countConvertDecimal = Integer.valueOf(getDecimal());
        BigDecimal leaveHour =
            minutesTime.divide(BigDecimal.valueOf(60), countConvertDecimal, BigDecimal.ROUND_HALF_UP);
        return leaveHour.toString();
    }

    @Override
    public String minutesToHourFormat(String minutesTime, String decimal) {
        if (StringUtils.isBlank(minutesTime) || "0".equals(minutesTime)) {
            return "0";
        }
        Integer countConvertDecimal = Integer.valueOf(decimal);
        BigDecimal minutes = new BigDecimal(minutesTime);
        BigDecimal leaveHour =
                minutes.divide(BigDecimal.valueOf(60), countConvertDecimal, BigDecimal.ROUND_HALF_UP);
        return leaveHour.toString();
    }

    @Override
    public boolean singleAttSystem() {
        int systemMenus = authPermissionService.getSystemMenus().size();
        AuthPermissionItem authAttDev = authPermissionService.getItemByCode("AttDev");
        if (authAttDev != null && ZKConstant.TRUE.equals(authAttDev.getAvailable())) {
            // 需要排除系统菜单《设备》《报表》
            systemMenus = systemMenus - 2;
        }
        // 如果系统模块菜单小于4，就是单考勤系统
        return systemMenus < 4;
    }

    @Override
    public AttRuleParamBean getRuleParam() {
        AttRuleParamBean attRuleParamBean = new AttRuleParamBean();
        Map<String, String> attParams = getAttParams();
        attRuleParamBean.setCrossDay(
            MapUtils.getString(attParams, AttRuleEnum.CrossDay.getKey(), AttRuleEnum.CrossDay.getValueOne()));
        attRuleParamBean.setSmartFindClass(MapUtils.getString(attParams, AttRuleEnum.SmartFindClass.getKey(),
            AttRuleEnum.SmartFindClass.getValueOne()));
        attRuleParamBean.setLateAndEarlyAsAbsent(
            MapUtils.getString(attParams, AttRuleEnum.LateAndEarly.getKey(), AttRuleEnum.LateAndEarly.getValueOne()));
        attRuleParamBean.setOvertime(
            MapUtils.getString(attParams, AttRuleEnum.CountOvertime.getKey(), AttRuleEnum.CountOvertime.getValueOne()));
        attRuleParamBean.setNoSignIn(getNoSignIn());
        attRuleParamBean.setNoSignOff(getNoSignOff());
        attRuleParamBean
            .setDecimal(MapUtils.getString(attParams, AttRuleEnum.Decimal.getKey(), AttRuleEnum.Decimal.getValueOne()));
        attRuleParamBean.setNoSignInAsType(MapUtils.getString(attParams, AttRuleEnum.NoSignInCountType.getKey(),
            AttRuleEnum.NoSignInCountType.getValueOne()));
        attRuleParamBean
            .setNoSignInAsLateMinute(MapUtils.getString(attParams, AttRuleEnum.noSignInCountLateMinute.getKey(), "0"));
        attRuleParamBean.setNoSignOutAsType(MapUtils.getString(attParams, AttRuleEnum.noSignOffCountType.getKey(),
            AttRuleEnum.noSignOffCountType.getValueOne()));
        attRuleParamBean.setNoSignOutAsEarlyMinute(
            MapUtils.getString(attParams, AttRuleEnum.noSignOffCountEarlyMinute.getKey(), "0"));
        /*attRuleParamBean.setAbsentDay(
            MapUtils.getString(attParams, AttRuleEnum.AbsentDay.getKey(), AttRuleEnum.AbsentDay.getValueOne()));
        attRuleParamBean
            .setDay(MapUtils.getString(attParams, AttRuleEnum.Day.getKey(), AttRuleEnum.AbsentDay.getValueOne()));
        attRuleParamBean
            .setHour(MapUtils.getString(attParams, AttRuleEnum.Hour.getKey(), AttRuleEnum.Hour.getValueOne()));
        attRuleParamBean.setOneDay(MapUtils.getString(attParams, AttRuleEnum.OneDay.getKey(), "80"));
        attRuleParamBean.setHalfAnDay(MapUtils.getString(attParams, AttRuleEnum.HalfAnDay.getKey(), "20"));*/
        attRuleParamBean
            .setSignIn(MapUtils.getString(attParams, AttRuleEnum.SignIn.getKey(), AttRuleEnum.SignIn.getValueOne()));
        attRuleParamBean
            .setSignOut(MapUtils.getString(attParams, AttRuleEnum.SignOut.getKey(), AttRuleEnum.SignOut.getValueOne()));

        return attRuleParamBean;
    }

    @Override
    public String getUpdateDayCardDetail() {
        String updateDayCardDetail = baseSysParamService.getValByName("att.report.updateDayCardDetail");
        return StringUtils.isNotBlank(updateDayCardDetail) ? updateDayCardDetail : "0 */10 * * * ?";
    }

    @Override
    public String getCrossDay() {
        return baseSysParamService.getValByName("att.baseRule.crossDay");
    }

    @Override
    public boolean realTimeEnable() {
        String enable = baseSysParamService.getValByName("att.realTime.enable");
        return "0".equals(enable);
    }

    @Override
    public String getAnnualLeaveCalculateType() {
        String calculateType = baseSysParamService.getValByName("att.annualLeave.calculateType");
        return StringUtils.isNoneBlank(calculateType) ? calculateType : "rounding";
    }

    @Override
    public String getAnnualLeaveCalculateRule() {
        String rule = baseSysParamService.getValByName("att.annualLeave.rule");
        return StringUtils.isNoneBlank(rule) ? rule : "0-1-1,1-2-2,2-999-3";
    }

    @Override
    public Date getAnnualLeaveCalculateStartDate() {
        return null;
    }

    @Override
    public Date getAnnualLeaveCalculateDate() {
        Calendar calculateDate = Calendar.getInstance();
        calculateDate.set(Calendar.MONTH, Integer.parseInt(getAnnualLeaveCalculateMonth()) - 1);
        calculateDate.set(Calendar.DAY_OF_MONTH, Integer.parseInt(getAnnualLeaveCalculateDay()));
        calculateDate.set(Calendar.HOUR_OF_DAY, 0);
        calculateDate.set(Calendar.MINUTE, 0);
        calculateDate.set(Calendar.MILLISECOND, 0);
        Calendar nowDate = Calendar.getInstance();
        if (nowDate.compareTo(calculateDate) < 0) {
            calculateDate.add(Calendar.YEAR, -1);
        }
        return calculateDate.getTime();
    }

    @Override
    public String getAnnualLeaveCalculateMonth() {
        String calculateMonth = baseSysParamService.getValByName("att.annualLeave.calculateMonth");
        return StringUtils.isNotBlank(calculateMonth) ? calculateMonth : "1";
    }

    @Override
    public String getAnnualLeaveCalculateDay() {
        String calculateDay = baseSysParamService.getValByName("att.annualLeave.calculateDay");
        return StringUtils.isNotBlank(calculateDay) ? calculateDay : "1";
    }

    @Override
    public boolean enableAnnualLeave() {
        String enable = baseSysParamService.getValByName("att.annualLeave.enable");
        return "0".equals(enable);
    }

    @Override
    public boolean enableCalculateType() {
        String enable = baseSysParamService.getValByName("att.annualLeave.enableCalculateType");
        return StringUtils.isNotBlank(enable) && "true".equals(enable);
    }

    @Override
    public String dateToLocaleString(Date date) {
        String dateStyle = DateUtil.DateStyle.YYYY_MM_DD.getValue();
        if (customDateFormat) {
            try {
                BaseSysParamItem dateItem = baseSysParamService.findByParamName("system.fmt.date");
                if (dateItem != null && StringUtils.isNoneBlank(dateItem.getParamValue())) {
                    String dateFmt = dateItem.getParamValue();
                    DateUtil.CustomDateStyle d = DateUtil.CustomDateStyle.valueOf(dateFmt);
                    dateStyle = d.getJavaFmt();
                }
            } catch (Exception e) {
                log.error("Init custom date format failed", e);
            }
        }
        return DateUtil.dateToLocaleString(date, dateStyle);
    }

    @Override
    public String dateTimeToLocaleString(Date date) {
        String dateStyle = DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS.getValue();;
        if (customDateFormat) {
            try {
                BaseSysParamItem dateItem = baseSysParamService.findByParamName("system.fmt.date");
                BaseSysParamItem timeItem = baseSysParamService.findByParamName("system.fmt.time");
                if (dateItem != null && timeItem != null) {
                    String dateFmt = dateItem.getParamValue();
                    String timeFmt = timeItem.getParamValue();
                    if (StringUtils.isNoneBlank(dateFmt, timeFmt)) {
                        DateUtil.CustomDateStyle d = DateUtil.CustomDateStyle.valueOf(dateFmt);
                        DateUtil.CustomDateStyle t = DateUtil.CustomDateStyle.valueOf(timeFmt);
                        dateStyle = d.getJavaFmt() + " " + t.getJavaFmt();
                    }
                }

            } catch (Exception e) {
                log.error("Init custom date format failed", e);
            }
        }
        return DateUtil.dateToLocaleString(date, dateStyle);
    }

    @Override
    public Map<String, String> getOvertimeSetting() {
        Map<String,String> overtimeParamsMap = new HashMap<>();

        BaseSysParamItem condition = new BaseSysParamItem();
        condition.setParamName("att.overtimeLevel.");
        List<BaseSysParamItem> baseSysParamItemList = baseSysParamService.getByCondition(condition);
        for (BaseSysParamItem item : baseSysParamItemList) {
            overtimeParamsMap.put(item.getParamName(), item.getParamValue());
        }

        return overtimeParamsMap;
    }

    @Override
    public boolean overtimeLevelEnable() {
        String enable = baseSysParamService.getValByName("att.overtimeLevel.enable");
        return StringUtils.isNotBlank(enable) && "0".equals(enable);
    }
}