package com.zkteco.zkbiosecurity.att.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 定时计算
 */
@Entity
@Table(name = "ATT_TIMING")
@Setter
@Getter
@Accessors(chain = true)
@Deprecated
public class AttTiming extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 时间计算频率（时、日、周、月）
     */
    @Column(name = "TIME_CALC_FREQUENCY", length = 1)
    private String timeCalcFrequency;

    /**
     * 时间计算间隔
     */
    @Column(name = "TIME_CALC_INTERVAL", length = 100)
    private String timeCalcInterval;

    /**
     * 任务名称
     */
    @Column(name = "JOB_NAME", length = 50)
    private String jobName;

    /**
     * 任务执行类
     */
    @Column(name = "JOB_CLASS", length = 50)
    private String jobClass;

    /**
     * 任务Cron表达式
     */
    @Column(name = "JOB_CRON", length = 50)
    private String jobCron;

    /**
     * 任务状态（启动、禁用），默认启动
     */
    @Column(name = "JOB_STATUS")
    private Boolean jobStatus;
}