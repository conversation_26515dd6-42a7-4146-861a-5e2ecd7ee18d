package com.zkteco.zkbiosecurity.att.service.impl;

import com.zkteco.zkbiosecurity.att.service.AttGetInsDeviceSelectService;
import com.zkteco.zkbiosecurity.att.service.AttGetInsDeviceService;
import com.zkteco.zkbiosecurity.att.service.AttInsDeviceService;
import com.zkteco.zkbiosecurity.att.service.AttPointService;
import com.zkteco.zkbiosecurity.att.vo.Att4InsDeviceSelect;
import com.zkteco.zkbiosecurity.att.vo.AttInsDeviceSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * 信息屏当考勤、获取信息屏设备信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:04
 * @since 1.0.0
 */
@Service
public class AttInsDeviceServiceImpl implements AttInsDeviceService {

    @Autowired(required = false)
    private AttGetInsDeviceSelectService attGetInsDeviceSelectService;
    @Autowired(required = false)
    private AttGetInsDeviceService attGetInsDeviceService;

    @Autowired
    private AttPointService attPointService;

    @Override
    public AttInsDeviceSelectItem getInsDeviceByDeviceId(String deviceId) {
        if (Objects.nonNull(attGetInsDeviceService)) {
            Att4InsDeviceSelect att4InsDeviceSelect = attGetInsDeviceService.getInsDeviceById(deviceId);
            if (Objects.nonNull(att4InsDeviceSelect)) {
                AttInsDeviceSelectItem target = new AttInsDeviceSelectItem();
                ModelUtil.copyPropertiesIgnoreNull(att4InsDeviceSelect, target);
                return target;
            }
        }
        return null;
    }

    @Override
    public List<AttInsDeviceSelectItem> getInsDeviceByDeviceIds(Collection<String> deviceIds) {
        List<AttInsDeviceSelectItem> attInsDeviceSelectItemList = new ArrayList<>();
        if (Objects.nonNull(attGetInsDeviceService)) {
            List<Att4InsDeviceSelect> insDeviceItemList = attGetInsDeviceService.getInsDeviceItemsByIds(deviceIds);
            if (!CollectionUtil.isEmpty(insDeviceItemList)) {
                attInsDeviceSelectItemList =
                    ModelUtil.copyListProperties(insDeviceItemList, AttInsDeviceSelectItem.class);
            }
        }
        return attInsDeviceSelectItemList;
    }

    @Override
    public Pager getSelectInsDevicePager(AttInsDeviceSelectItem condition, int page, int size) {
        if (Objects.nonNull(attGetInsDeviceSelectService)) {
            AttInsDeviceSelectItem attInsDeviceSelectItem = (AttInsDeviceSelectItem)condition;
            Att4InsDeviceSelect insDeviceSelectItem =
                ModelUtil.copyPropertiesIgnoreNull(condition, new Att4InsDeviceSelect());
            List<String> deviceModules = attPointService.getDeviceIdsByDeviceModule(ConstUtil.SYSTEM_MODULE_INS);
            String deviceModuleStr = StringUtils.join(deviceModules, ",");
            if (StringUtils.isNotBlank(deviceModuleStr)) {
                insDeviceSelectItem.setSelectId(attInsDeviceSelectItem.getSelectId() + "," + deviceModuleStr);
            } else {
                insDeviceSelectItem.setSelectId(attInsDeviceSelectItem.getSelectId());
            }
            Pager pager = attGetInsDeviceSelectService.getInsDeviceSelectList(insDeviceSelectItem, page, size);
            List<AttInsDeviceSelectItem> insDeviceSelectItemList =
                ModelUtil.copyListProperties(pager.getData(), AttInsDeviceSelectItem.class);
            pager.setPage(page);
            pager.setSize(size);
            pager.setData(insDeviceSelectItemList);
            return pager;
        } else {
            Pager pager = new Pager();
            pager.setPage(0);
            pager.setSize(0);
            pager.setData(new ArrayList<>());
            return pager;
        }
    }
}