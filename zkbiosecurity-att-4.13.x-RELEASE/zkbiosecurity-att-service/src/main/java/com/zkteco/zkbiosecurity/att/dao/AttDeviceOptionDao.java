package com.zkteco.zkbiosecurity.att.dao;

import com.zkteco.zkbiosecurity.att.model.AttDeviceOption;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;

/**
 * 设备参数
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:28
 * @since 1.0.0
 */
public interface AttDeviceOptionDao extends BaseDao<AttDeviceOption, String> {

    List<AttDeviceOption> findByAttDevice_Id(String devId);

    List<AttDeviceOption> findByAttDevice_DevSn(String devSn);

    AttDeviceOption findByAttDevice_IdAndName(String devId, String optionName);

    @Query(value = "select t.value from AttDeviceOption t where t.attDevice.devSn = ?1 and t.name = ?2 ")
    String getValueByDevSnAndName(String devSn, String name);

    void deleteByAttDevice_Id(String devId);

    /**
     * 查找指定设备的指定属性集合
     * 
     * @param devIds
     * @param opNames
     * @return
     */
    @Query(
        value = "select new AttDeviceOption(t.attDevice, t.name, t.value) from AttDeviceOption t where t.attDevice.id in (?1) and t.name in (?2)")
    List<AttDeviceOption> findByDevIdInAndNameIn(List<String> devIds, List<String> opNames);

    /**
     * 获取支持功能(or)的设备SN集合
     * 
     * @param functions:
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2020-12-10 14:18
     * @since 1.0.0
     */
    @Query(value = "select distinct t.attDevice.devSn from AttDeviceOption t where t.name in (?1) and value = '1'")
    List<String> findSupportFunctionsDevSn(List<String> functions);

    @Query(value = "select count(t.id) from AttDeviceOption t where t.attDevice.devSn = ?1 and t.name in (?2) and t.value = '1'")
    Long isSupportOrFunctionsBySn(String sn, List<String> functions);
}