package com.zkteco.zkbiosecurity.att.service.impl;

import com.zkteco.zkbiosecurity.att.dao.AttPersonDao;
import com.zkteco.zkbiosecurity.att.model.AttPerson;
import com.zkteco.zkbiosecurity.att.service.AttPersonService;
import com.zkteco.zkbiosecurity.att.vo.AttPersonItem;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonCacheExtParamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 人员信息缓存附加参数获取
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/9/14 14:16
 * @since 1.0.0
 */
@Service
public class AttPersonCacheExtParamsServiceImpl implements PersPersonCacheExtParamsService {

    @Autowired
    private AttPersonDao attPersonDao;
    @Autowired
    private AttPersonService attPersonService;

    @Override
    public String getModule() {
        return ConstUtil.SYSTEM_MODULE_ATT;
    }

    @Override
    public Map<String, Map<String, Object>> getExtParamsMapByPins(Collection<String> pinList) {
        List<AttPerson> attPersonList = attPersonDao.findByPersonPinIn(pinList);
        if (attPersonList != null && attPersonList.size() > 0) {
            List<AttPersonItem> attPersonItemList = ModelUtil.copyListProperties(attPersonList, AttPersonItem.class);
            Map<String, Map<String, Object>> personCacheExtParamsMap = new HashMap<>();
            for (AttPersonItem attPersonItem : attPersonItemList) {
                personCacheExtParamsMap.put(attPersonItem.getPersonPin(),
                    attPersonService.buildPersonCacheExtParams(attPersonItem));
            }
            return personCacheExtParamsMap;
        }
        return null;
    }
}
