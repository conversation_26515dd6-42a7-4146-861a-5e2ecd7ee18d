package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.att.bean.AttPersPersonInfoBean;
import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.bean.AttConditionBean;
import com.zkteco.zkbiosecurity.att.cache.AttCalculationCacheManager;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttTimeSlotBO;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttClassDao;
import com.zkteco.zkbiosecurity.att.dao.AttShiftDao;
import com.zkteco.zkbiosecurity.att.enums.AttRuleEnum;
import com.zkteco.zkbiosecurity.att.model.AttClass;
import com.zkteco.zkbiosecurity.att.model.AttShift;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.utils.AttShiftSchUtils;
import com.zkteco.zkbiosecurity.att.vo.AttClassItem;
import com.zkteco.zkbiosecurity.att.vo.AttShiftItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.service.PersLeavePersonService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersLeavePersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 调班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:08
 * @since 1.0.0
 */
@Service
public class AttClassServiceImpl implements AttClassService {

    @Autowired
    private AttClassDao attClassDao;
    @Autowired
    private AttShiftDao attShiftDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private AttPersonSchService attPersonSchService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private AttPersonSchDataService attPersonSchDataService;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttCalculationCacheManager attCalculationCacheManager;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttTimeSlotService attTimeSlotService;
    @Autowired
    private AttMessageCenterService attMessageCenterService;
    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired(required = false)
    private AuthUserService authUserService;
    @Autowired(required = false)
    private AuthDepartmentService authDepartmentService;

    @Override
    @Transactional
    public ZKResultMsg saveItem(AttClassItem item, String sessionId) {
        String personPin = item.getAdjustPersonPin();

        // 是否有审批权限
        boolean hasApproval = authPermissionService.checkPermission(sessionId, "att:class:approval");

        // SameDay((short) 0, "个人同日期调班"),
        String isWork = attPersonSchService.isWorkd(personPin, item.getAdjustDate());
        if (isWork.equals(AttCalculationConstant.AttAttendStatus.NO_SCHEDULING)) {
            return ZKResultMsg.failMsg("att_approve_classConflicts");
        }

        // DiffDay((short) 1, "个人不同日期调班"),
        if (item.getAdjustType() == 1) {
            String isSwapDataWork = attPersonSchService.isWorkd(personPin, item.getSwapDate());
            if (isSwapDataWork.equals(AttCalculationConstant.AttAttendStatus.NO_SCHEDULING)) {
                return ZKResultMsg.failMsg("att_approve_classConflicts");
            }
        }
        // TwoPerson((short) 2, "两人对调");
        if (item.getAdjustType() == 2) {

            PersPersonItem simplePersonItem = persPersonService.getSimpleItemById(item.getSwapPersonId());
            if (!Objects.nonNull(simplePersonItem)) {
                return ZKResultMsg.failMsg("att_class_isNotExisetSwapPersonPin");
            }
            String isSwapWork = attPersonSchService.isWorkd(item.getSwapPersonPin(), item.getSwapDate());
            if (isSwapWork.equals(AttCalculationConstant.AttAttendStatus.NO_SCHEDULING)) {
                return ZKResultMsg.failMsg("att_approve_classConflicts");
            }
        }

        if (item.getAdjustType() == 1 || item.getAdjustType() == 2) {
            Long swapDateLong = item.getSwapDate().getTime();
            Long adjustDateLong = item.getAdjustDate().getTime();
            // 一个月毫秒数
            Long monthMSLong = 2592000000L;
            if (Math.abs(swapDateLong - adjustDateLong) > monthMSLong) {
                return ZKResultMsg.failMsg("att_class_outTime");
            }
        }

        if (item.getAdjustType() == 0) {
            // 将对调日期和对调人员信息置空
            item.setSwapDate(null);
        } else if (item.getAdjustType() == 1) {
            // 当类型为个人不同日期调班，将对调人员信息置空
            item.setSwapPersonId(null);
            item.setSwapPersonName(null);
            item.setSwapPersonLastName(null);
            item.setSwapPersonPin(null);
            item.setSwapDeptId(null);
            item.setSwapDeptName(null);
            item.setSwapDeptCode(null);
        }

        AttClass attClass = new AttClass();
        ModelUtil.copyPropertiesIgnoreNull(item, attClass);

        attClassDao.save(attClass);

        // 【实时计算】1.添加人员到缓存;2.添加异常信息到缓存;3.更新补签事件到缓存
        if (hasApproval) {
            String crossDay = attParamService.getCrossDay();
            setClassAddEventCache(attClass, crossDay);
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 【实时计算】保存调班记录到缓存，新增实时计算事件
     *
     * @param attClass:
     * @param crossDay:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/19 11:40
     * @since 1.0.0
     */
    private void setClassAddEventCache(AttClass attClass, String crossDay) {
        // 发送消息
        AttClassItem attClassItem = new AttClassItem();
        ModelUtil.copyProperties(attClass, attClassItem);
        attMessageCenterService.pushClassMessage(attClassItem);

        if (attParamService.realTimeEnable()) {
            if (attCalculationCacheManager.judgeCacheDate(attClass.getAdjustDate()) || (null != attClass.getSwapDate()
                    && attCalculationCacheManager.judgeCacheDate(attClass.getSwapDate()))) {
                Map<String, List<AttPersonSchBO>> listMap = buildAttClassMap(Collections.singletonList(attClass));
                for (Map.Entry<String, List<AttPersonSchBO>> entry : listMap.entrySet()) {
                    attCalculationCacheManager.setClass(entry.getKey(), entry.getValue(), true);
                }
                // 新增实时计算事件
                attCalculationCacheManager.addRealTimeEvent(attClass.getAdjustPersonPin(), attClass.getAdjustDate(),
                        crossDay);
                if (attClass.getAdjustType() == 1) {
                    attCalculationCacheManager.addRealTimeEvent(attClass.getAdjustPersonPin(), attClass.getSwapDate(),
                            crossDay);
                } else if (attClass.getAdjustType() == 2) {
                    attCalculationCacheManager.addRealTimeEvent(attClass.getSwapPersonPin(), attClass.getSwapDate(),
                            crossDay);
                }
            }
        }
    }

    /**
     * 【实时计算】删除缓存的调班，新增实时计算事件
     *
     * @param attClass:
     * @param crossDay:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/19 11:40
     * @since 1.0.0
     */
    private void delClassAddEventCache(AttClass attClass, String crossDay) {
        if (attParamService.realTimeEnable()) {
            if (attCalculationCacheManager.judgeCacheDate(attClass.getAdjustDate())) {
                // 删除缓存的补签
                attCalculationCacheManager.delClass(attClass.getAdjustPersonPin(), attClass.getId(),
                        attClass.getAdjustDate());
                // 新增实时计算事件
                attCalculationCacheManager.addRealTimeEvent(attClass.getAdjustPersonPin(), attClass.getAdjustDate(),
                        crossDay);
            }
        }
    }

    @Override
    public List<AttClassItem> getByCondition(AttClassItem condition) {
        return (List<AttClassItem>)attClassDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        Pager pager = attClassDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        return pager;
    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, AttClassItem condition, int pageNo, int pageSize) {
        buildCondition(sessionId, condition);
        Pager pager =
            attClassDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        buildItem((List<AttClassItem>)pager.getData());
        return pager;
    }

    private void buildCondition(String sessionId, AttClassItem condition) {

        // 使用员工自助登录的用户,只有自己数据的权限。若登录类型为pers,则返回pin加入查询sql
        String persperPin = attPersonService.getPinByLoginType(sessionId);
        if (persperPin != null) {
            condition.setAdjustPersonPin(persperPin);
            condition.equals(true);
        } else {
            // 部门权限过滤
            String userId = authUserService.getUserIdBySessionId(sessionId);
            if (StringUtils.isNotBlank(userId)) {
                condition.setUserId(userId);
            }

            // 前端部门树点击查询
            condition.setAdjustDeptId(condition.getDeptId());
            // 前端是否包含上下级
            if (ZKConstant.TRUE.equals(condition.getIsIncludeLower())) {
                if (StringUtils.isBlank(condition.getInAdjustDeptId())) {
                    if (StringUtils.isNotBlank(condition.getAdjustDeptId())) {
                        List<String> deptIdList = authDepartmentService.getChildDeptIdsByDeptIds(condition.getAdjustDeptId());
                        condition.setInAdjustDeptId(StrUtil.collectionToStr(deptIdList));
                        condition.setAdjustDeptId(null);
                    }
                }
            }
            // 人员姓名模糊查询（包含离职人员）
            String pins = attPersonService.getPinsByLikeName(sessionId, condition.getLikeName());
            if (StringUtils.isNotBlank(pins)) {
                condition.setInAdjustPersonPin(pins);
            }
        }
    }

    /**
     * 填充栅格信息
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/7/2 15:08
     * @param itemList
     * @return void
     */
    private void buildItem(List<AttClassItem> itemList) {


        List<List<AttClassItem>> splitItemList = CollectionUtil.split(itemList, CollectionUtil.splitSize);
        for (List<AttClassItem> splitItems : splitItemList) {

            // 调整人员信息
            Collection<String> personAdjustPinList = CollectionUtil.getPropertyList(splitItems,
                    AttClassItem::getAdjustPersonPin, AttConstant.COMM_DEF_VALUE);
            Map<String, AttPersPersonInfoBean> adjustPersPersonInfoMap = attPersonService.getPersonInfoByPinList(personAdjustPinList);


            // 对调人员信息
            Collection<String> personSwapPinList = splitItems.stream().map(AttClassItem::getSwapPersonPin)
                    .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            if (CollectionUtil.isEmpty(personSwapPinList)) {
                personSwapPinList.add(AttConstant.COMM_DEF_VALUE);
            }
            Map<String, AttPersPersonInfoBean> swapPersPersonInfoMap = attPersonService.getPersonInfoByPinList(personSwapPinList);

            for (AttClassItem item : splitItems) {

                // 调整人员信息
                String personPin = item.getAdjustPersonPin();
                AttPersPersonInfoBean adjustPersPersonInfo = adjustPersPersonInfoMap.get(personPin);
                if (Objects.nonNull(adjustPersPersonInfo)) {
                    item.setAdjustPersonName(adjustPersPersonInfo.getPersonName());
                    item.setAdjustPersonLastName(adjustPersPersonInfo.getPersonLastName());
                }

                // 对调人员信息
                if (item.getAdjustType() == AttConstant.CLASS_TYPE_PEOPLEMOVE) {
                    String personSwapPin = item.getSwapPersonPin();
                    AttPersPersonInfoBean persSwapPersonItem = swapPersPersonInfoMap.get(personSwapPin);
                    if (Objects.nonNull(persSwapPersonItem)) {
                        item.setSwapPersonName(persSwapPersonItem.getPersonName());
                        item.setSwapPersonLastName(persSwapPersonItem.getPersonLastName());
                        item.setSwapDeptName(persSwapPersonItem.getDeptName());
                        item.setSwapDeptCode(persSwapPersonItem.getDeptCode());
                    }
                }
            }
        }
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            List<AttClass> attAdjustList = attClassDao.findByIdList(CollectionUtil.strToList(ids));
            for (AttClass attClass : attAdjustList) {
                attClassDao.delete(attClass);
                // 删除实时计算缓存
                deleteAttClassCache(attClass);
            }
        }
        return false;
    }

    /**
     * 删除实时计算缓存
     * 
     * @param attClass:
     * @return void
     * <AUTHOR>
     * @date 2021-02-20 16:23
     * @since 1.0.0
     */
    private void deleteAttClassCache(AttClass attClass) {
        if (attParamService.realTimeEnable()) {
            if (attCalculationCacheManager.judgeCacheDate(attClass.getAdjustDate()) || (null != attClass.getSwapDate()
                && attCalculationCacheManager.judgeCacheDate(attClass.getSwapDate()))) {

                String crossDay = attParamService.getCrossDay();
                attCalculationCacheManager.delClass(attClass.getAdjustPersonPin(), attClass.getId(),
                    attClass.getAdjustDate());
                // 新增实时计算事件
                attCalculationCacheManager.addRealTimeEvent(attClass.getAdjustPersonPin(), attClass.getAdjustDate(),
                    crossDay);
                if (attClass.getAdjustType() == 1) {
                    attCalculationCacheManager.delClass(attClass.getAdjustPersonPin(), attClass.getId(),
                        attClass.getSwapDate());
                    attCalculationCacheManager.addRealTimeEvent(attClass.getAdjustPersonPin(), attClass.getSwapDate(),
                        crossDay);
                } else if (attClass.getAdjustType() == 2) {
                    attCalculationCacheManager.delClass(attClass.getSwapPersonPin(), attClass.getId(),
                        attClass.getSwapDate());
                    attCalculationCacheManager.addRealTimeEvent(attClass.getSwapPersonPin(), attClass.getSwapDate(),
                        crossDay);
                }
            }
        }
    }

    @Override
    public AttClassItem getItemById(String id) {
        List<AttClassItem> items = getByCondition(new AttClassItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public List<AttClassItem> getItemData(String sessionId, AttClassItem condition, int beginIndex, int endIndex) {
        buildCondition(sessionId, condition);
        List<AttClassItem> itemList = attClassDao.getItemsDataBySql(condition.getClass(), SQLUtil.getSqlByItem(condition),
            beginIndex, endIndex, true);
        buildItem(itemList);
        return itemList;
    }

    @Override
    public boolean existClass(List<String> personIdList, Date startTime, Date endTime) {
        startTime = DateUtil.getDayBeginTime(startTime);
        endTime = DateUtil.getDayEndTime(endTime);
        List<AttClass> attClassList = attClassDao.getByPersonIdAndTime(personIdList, startTime, endTime);
        if (attClassList != null && attClassList.size() > 0) {
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public void handlerTransfer(List<AttClassItem> attClassItems) {
        // 数据量大的时候 分批处理 jinxian.huang 2019-07-31
        List<List<AttClassItem>> splitAttClassItemList = CollectionUtil.split(attClassItems, CollectionUtil.splitSize);
        for (List<AttClassItem> splitAttClassItems : splitAttClassItemList) {
            // 找出pin号 根据pin号把人员信息补充完整 modified by jinxian.huang 2019-07-29
            // 调整人员编号集合
            Collection<String> adjustPinList =
                CollectionUtil.getPropertyList(splitAttClassItems, AttClassItem::getAdjustPersonPin, "-1");
            List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(adjustPinList);
            Map<String, PersPersonItem> persPersonItemMap =
                CollectionUtil.listToKeyMap(persPersonItemList, PersPersonItem::getPin);
            // 对调人员编号集合
            Collection<String> swapPinList =
                CollectionUtil.getPropertyList(splitAttClassItems, AttClassItem::getSwapPersonPin, "-1");
            List<PersPersonItem> persPersonList = persPersonService.getItemsByPins(swapPinList);
            Map<String, PersPersonItem> persPersonMap =
                CollectionUtil.listToKeyMap(persPersonList, PersPersonItem::getPin);
            for (AttClassItem attClassItem : splitAttClassItems) {
                String adjustPersonPin = attClassItem.getAdjustPersonPin();
                Date adjustDate = attClassItem.getAdjustDate();
                AttClass attClass = attClassDao.getByPersonPinAndTime(adjustPersonPin, adjustDate, adjustDate);
                if (Objects.isNull(attClass)) {
                    attClass = new AttClass();
                    if (persPersonItemMap.containsKey(adjustPersonPin)) {
                        PersPersonItem persPersonItem = persPersonItemMap.get(adjustPersonPin);
                        if (Objects.nonNull(persPersonItem)) {
                            attClass.setAdjustDeptId(persPersonItem.getDeptId());
                            attClass.setAdjustPersonId(persPersonItem.getId());
                        }
                    }
                    attClass.setCreateTime(attClassItem.getOperateDatetime());
                    // 流程状态默认设置为2——已完成
                    attClass.setFlowStatus("2");
                }
                Short adjustType = attClassItem.getAdjustType();
                ModelUtil.copyPropertiesIgnoreNullWithProperties(attClassItem, attClass, "id");
                if (AttConstant.CLASS_TYPE_SAMETIMEMOVE == (adjustType)) {
                    attClass.setSwapDate(null);
                    attClass.setCreateTime(attClassItem.getOperateDatetime());
                    String shiftNo = attClassItem.getShiftNo();
                    AttShift attShift = attShiftDao.findByShiftNo(shiftNo);
                    attClass.setSwapShiftId(attShift.getId());
                    attClassDao.save(attClass);
                }

                if (AttConstant.CLASS_TYPE_DIFFTIMEMOVE == (adjustType)) {
                    attClass.setSwapPersonPin(null);
                    attClass.setCreateTime(attClassItem.getOperateDatetime());
                    attClassDao.save(attClass);
                }

                if (AttConstant.CLASS_TYPE_PEOPLEMOVE == (adjustType)) {
                    String swapPersonPin = attClassItem.getSwapPersonPin();
                    if (persPersonItemMap.containsKey(swapPersonPin)) {
                        PersPersonItem persPersonItem = persPersonMap.get(swapPersonPin);
                        if (Objects.nonNull(persPersonItem)) {
                            attClass.setSwapDeptId(persPersonItem.getDeptId());
                            attClass.setSwapPersonId(persPersonItem.getId());
                        }
                    }
                    attClass.setCreateTime(attClassItem.getOperateDatetime());
                    attClassDao.save(attClass);
                }
            }
        }
    }

    @Override
    @Transactional
    public void updateFlowStatus(String businessKey, String status) {
        AttClass attClass = attClassDao.findByBusinessKey(businessKey);
        if (Objects.nonNull(attClass)) {
            attClass.setFlowStatus(status);
            attClassDao.save(attClass);

            // 【实时计算】 根据异常状态判断是否需要更新缓存和进行考勤实时计算
            boolean isCalculation =
                    attCalculationCacheManager.judgeStatusCalculation(attClass.getFlowStatus(), status);

            // 【实时计算】保存\删除缓存的补签、新增实时计算事件
            if (isCalculation) {
                if (attCalculationCacheManager.judgeCacheDate(attClass.getAdjustDate())) {

                    List<AttShift> attShifts = attShiftDao.findAll();
                    Map<String, AttShift> attShiftMap = CollectionUtil.listToKeyMap(attShifts, AttShift::getId);
                    String crossDay = attParamService.getCrossDay();
                    if (AttConstant.FLOW_STATUS_COMPLETE.equals(attClass.getFlowStatus())) {
                        // 【实时计算】保存调班记录到缓存，新增实时计算事件
                        setClassAddEventCache(attClass, crossDay);
                    } else {
                        // 【实时计算】删除缓存的调班，新增实时计算事件
                        delClassAddEventCache(attClass, crossDay);
                    }
                }
            }
        }
    }

    @Override
    public AttClassItem getByBusinessKey(String businessKey) {
        AttClass attClass = attClassDao.findByBusinessKey(businessKey);
        if (Objects.isNull(attClass)) {
            return null;
        }
        AttClassItem attClassItem = new AttClassItem();
        ModelUtil.copyProperties(attClass, attClassItem);
        return attClassItem;
    }

    @Override
    @Transactional
    public ZKResultMsg importExcel(List<AttClassItem> itemList, String sessionId) {
        if (itemList.size() > 30000) {
            // 一次性导入只能30000，超过要分批次导入
            throw ZKBusinessException.warnException("att_import_overData", itemList.size());
        }
        int importSize = itemList.size();
        int beginProgress = 20;
        // 导入的人员要求人事已存在
        Map<String, PersPersonItem> existPersPersonMap = new HashMap<>();
        // 取出待导入数据中人员的pin号
        List<String> importPins = new ArrayList<>();
        // 调整人pin号
        Collection<String> adjustPersonPins =
            CollectionUtil.getPropertyList(itemList, AttClassItem::getAdjustPersonPin, "-1");
        importPins.addAll(adjustPersonPins);
        // 对调人员编号
        Collection<String> SwapPersonPins =
            CollectionUtil.getPropertyList(itemList, AttClassItem::getSwapPersonPin, "-1");
        importPins.addAll(SwapPersonPins);
        // 分批处理，一次处理800人
        List<List<String>> pinsList = CollectionUtil.split(importPins, CollectionUtil.splitSize);
        for (List<String> pins : pinsList) {
            // 根据pin号查出人事人员
            List<PersPersonItem> existPersonList = persPersonService.getItemsByPins(pins);
            if (existPersonList != null && existPersonList.size() > 0) {
                existPersPersonMap.putAll(CollectionUtil.listToKeyMap(existPersonList, PersPersonItem::getPin));
            }
        }
        // 查找所有的班次
        List<AttShift> allShift = attShiftDao.findAll();
        Map<String, AttShift> nameAttShiftMap = CollectionUtil.listToKeyMap(allShift, AttShift::getShiftName);

        Iterator<AttClassItem> itemIterator = itemList.iterator();
        // 先剔除无效数据
        while (itemIterator.hasNext()) {
            AttClassItem item = itemIterator.next();
            // 人员编号校验
            if (StringUtils.isBlank(item.getAdjustPersonPin())) {
                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                    ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                        I18nUtil.i18nCode("pers_import_pinNotEmpty")))));
                itemIterator.remove();
                continue;
            }
            PersPersonItem adjustPersonItem = existPersPersonMap.get(item.getAdjustPersonPin());
            // 人员不存在
            if (Objects.isNull(adjustPersonItem)) {
                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                    ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                        I18nUtil.i18nCode("att_areaPerson_persNoExit")))));
                itemIterator.remove();
                continue;
            }
            // 调整日期校验
            if (Objects.isNull(item.getAdjustDate())) {
                progressCache
                    .setProcess(new ProcessBean(beginProgress, beginProgress, ProcessBean.createErrorContent(I18nUtil
                        .i18nCode("pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("att_leave_startNotNull")))));
                itemIterator.remove();
                continue;
            }
            // 个人同日期调班,调整班次校验
            if (item.getAdjustType() == 0) {
                if (Objects.isNull(item.getSwapShiftName())) {
                    progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                        ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                            I18nUtil.i18nCode("att_class_shiftNameNoNull")))));
                    itemIterator.remove();
                    continue;
                }
                // 班次不存在
                Set<String> shiftNames = nameAttShiftMap.keySet();
                if (!shiftNames.contains(item.getSwapShiftName())) {
                    progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                        ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                            I18nUtil.i18nCode("att_class_shiftNameNoExsist")))));
                    itemIterator.remove();
                    continue;
                }
            }
            // 个人不同日期调或两人对调时进行对调日期校验
            if (item.getAdjustType() == 1 || item.getAdjustType() == 2) {
                if (Objects.isNull(item.getSwapDate())) {
                    progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                        ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                            I18nUtil.i18nCode("att_leave_startNotNull")))));
                    itemIterator.remove();
                    continue;
                }
                // 调整日期和对调日期相隔不能超过一个月
                Long swapDateLong = item.getSwapDate().getTime();
                Long adjustDateLong = item.getAdjustDate().getTime();
                // 个人不同日期调,日期不能一样
                if (item.getAdjustType() == 1) {
                    if ((swapDateLong - adjustDateLong) == 0) {
                        progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                            ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                                I18nUtil.i18nCode("att_class_dateNoSame")))));
                        itemIterator.remove();
                        continue;
                    }
                }
                // 一个月毫秒数
                Long monthMSLong = 2592000000L;
                if (Math.abs(swapDateLong - adjustDateLong) > monthMSLong) {
                    progressCache.setProcess(
                        new ProcessBean(beginProgress, beginProgress, ProcessBean.createErrorContent(I18nUtil
                            .i18nCode("pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("att_class_outTime")))));
                    itemIterator.remove();
                    continue;
                }
            }
            // 两人对调时对对调人员校验
            if (item.getAdjustType() == 2) {
                PersPersonItem swapPersonItem = existPersPersonMap.get(item.getSwapPersonPin());
                // 对调人员不存在
                if (StringUtils.isBlank(item.getSwapPersonPin()) || Objects.isNull(swapPersonItem)) {
                    progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                        ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                            I18nUtil.i18nCode("att_class_swapPersonNoExisist")))));
                    itemIterator.remove();
                    continue;
                }
                // 调整对调人员不能相同
                if (item.getSwapPersonPin().equals(item.getAdjustPersonPin())) {
                    progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                        ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                            I18nUtil.i18nCode("att_class_personNoSame")))));
                    itemIterator.remove();
                    continue;
                }
            }
        }

        // 是否有审批权限
        boolean hasApproval = authPermissionService.checkPermission(sessionId, "att:class:approval");

        // 剩下的可以插入数据库,分批处理，一次处理800条
        List<List<AttClassItem>> attOvertimeInsertList = CollectionUtil.split(itemList, CollectionUtil.splitSize);
        for (List<AttClassItem> insertItemList : attOvertimeInsertList) {
            // 保存入库
            List<AttClass> attClassList = new ArrayList<>();
            for (AttClassItem item : insertItemList) {
                PersPersonItem adjustPersonItem = existPersPersonMap.get(item.getAdjustPersonPin());
                PersPersonItem swapPersonItem = existPersPersonMap.get(item.getSwapPersonPin());
                if (Objects.nonNull(adjustPersonItem)) {
                    AttClass attClass = new AttClass();
                    ModelUtil.copyPropertiesWithIgnore(item, attClass, "id");
                    attClass.setFlowStatus(hasApproval ? AttConstant.FLOW_STATUS_COMPLETE : AttConstant.FLOW_STATUS_CREATE);
                    attClass.setAdjustDeptId(adjustPersonItem.getDeptId());
                    attClass.setAdjustPersonId(adjustPersonItem.getId());
                    if (Objects.nonNull(swapPersonItem)) {
                        attClass.setSwapPersonId(swapPersonItem.getId());
                        attClass.setSwapDeptId(swapPersonItem.getDeptId());
                    }
                    // 个人同日期设置班次
                    if (item.getAdjustType() == 0 && StringUtils.isNotBlank(item.getSwapShiftName())) {
                        AttShift attShift = nameAttShiftMap.get(item.getSwapShiftName());
                        if (Objects.nonNull(attShift)) {
                            item.setSwapShiftId(attShift.getId());
                            attClass.setSwapShiftId(attShift.getId());
                        }
                    }
                    attClassList.add(attClass);
                }
            }
            attClassDao.saveAll(attClassList);

            // 【实时计算】保存调休记录到缓存，新增实时计算事件
            if (hasApproval) {
                String crossDay = attParamService.getCrossDay();
                for (AttClass attClass : attClassList) {
                    setClassAddEventCache(attClass, crossDay);
                }
            }
        }

        // 失败数量
        int faildCount = importSize - itemList.size();
        // 成功：%s 条，失败：%s 条。
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(99,
            I18nUtil.i18nCode("pers_import_result", itemList.size(), faildCount)));
        if (faildCount > 0) {
            return ZKResultMsg.failMsg();
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    @Transactional
    public void approval(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            List<AttClass> attClassList = attClassDao.findByIdList(CollectionUtil.strToList(ids));
            String crossDay = attParamService.getCrossDay();
            for (AttClass attClass : attClassList) {
                if (!AttConstant.FLOW_STATUS_COMPLETE.equals(attClass.getFlowStatus())) {
                    setClassAddEventCache(attClass, crossDay);
                }
            }
            attClassDao.updateFlowStatus(Arrays.asList(ids.split(",")), AttConstant.FLOW_STATUS_COMPLETE);
        }
    }

    @Override
    @Transactional
    public void refuse(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            List<AttClass> attClassList = attClassDao.findByIdList(CollectionUtil.strToList(ids));
            String crossDay = attParamService.getCrossDay();
            for (AttClass attClass : attClassList) {
                if (AttConstant.FLOW_STATUS_COMPLETE.equals(attClass.getFlowStatus())) {
                    delClassAddEventCache(attClass, crossDay);
                }
            }
            attClassDao.updateFlowStatus(Arrays.asList(ids.split(",")), AttConstant.FLOW_STATUS_REFUSE);
        }
    }

    @Override
    public boolean existApply(String adjustPersonId, Date adjustDate, String swapPersonId, Date swapDate) {
        List<String> flowStatusList = new ArrayList<String>() {
            {
                add(AttConstant.FLOW_STATUS_CREATE);
                add(AttConstant.FLOW_STATUS_COMPLETE);
            }
        };
        // 调整人员当天是否有其他申请
        List<AttClass> attClassList = attClassDao.findByAdjustPersonIdAndAdjustDateAndFlowStatusIn(adjustPersonId,
            DateUtil.getDayBeginTime(adjustDate), flowStatusList);
        if (attClassList != null && attClassList.size() > 0) {
            return true;
        }
        // 调整人员当天是否跟其他人调班
        attClassList = attClassDao.findBySwapPersonIdAndSwapDateAndFlowStatusIn(adjustPersonId,
            DateUtil.getDayBeginTime(adjustDate), flowStatusList);
        if (attClassList != null && attClassList.size() > 0) {
            return true;
        }
        if (StringUtils.isNotBlank(swapPersonId) && swapDate != null) {
            // 对调人员当天是否有其他申请
            List<AttClass> attClassSwapList = attClassDao.findByAdjustPersonIdAndAdjustDateAndFlowStatusIn(swapPersonId,
                DateUtil.getDayBeginTime(swapDate), flowStatusList);
            if (attClassSwapList != null && attClassSwapList.size() > 0) {
                return true;
            }
            // 对调人员当天是否跟其他人调班
            attClassSwapList = attClassDao.findBySwapPersonIdAndSwapDateAndFlowStatusIn(swapPersonId,
                DateUtil.getDayBeginTime(swapDate), flowStatusList);
            if (attClassSwapList != null && attClassSwapList.size() > 0) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Map<String, List<AttPersonSchBO>> getClassMap(List<String> pins, Date startDate, Date endDate) {

        // 查找调班记录
        List<AttClass> attClassList = new ArrayList<>();
        if (CollectionUtil.isEmpty(pins)) {
            attClassList = attClassDao.findClassBetweenDate(startDate, endDate);
        } else if (pins.size() == 1) {
            attClassList = attClassDao.findClassByPinAndDate(pins.get(0), startDate, endDate);
        } else {
            List<List<String>> splitPinList = CollectionUtil.split(pins, CollectionUtil.splitSize);
            for (List<String> subPinList : splitPinList) {
                List<AttClass> subClassList = attClassDao.findClassByPinsAndDate(subPinList, startDate, endDate);
                attClassList.addAll(subClassList);
            }
        }

        return buildAttClassMap(attClassList);
    }

    /**
     * 组装调班记录
     * 
     * @param attClassList:
     * @return java.util.Map<java.lang.String,java.util.List<com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO>>
     * <AUTHOR>
     * @date 2021-02-20 16:07
     * @since 1.0.0
     */
    private Map<String, List<AttPersonSchBO>> buildAttClassMap(List<AttClass> attClassList) {

        Collection<String> shiftIds = CollectionUtil.getPropertyList(attClassList, AttClass::getSwapShiftId, "-1");
        List<AttShift> attShifts = attShiftDao.findByIdList(shiftIds);
        Map<String, AttShift> attShiftMap = CollectionUtil.listToKeyMap(attShifts, AttShift::getId);

        // 获取所有时间段集合
        List<AttTimeSlotItem> attTimeSlotItems = attTimeSlotService.getByCondition(new AttTimeSlotItem());
        Map<String, AttTimeSlotItem> attTimeSlotItemMap =
            CollectionUtil.listToKeyMap(attTimeSlotItems, AttTimeSlotItem::getId);

        String crossDay = attParamService.getCrossDay();

        Map<String, List<AttPersonSchBO>> attClassMap = new HashMap<>();
        for (AttClass attClass : attClassList) {
            Date adjustDate = attClass.getAdjustDate();

            if (AttConstant.CLASS_TYPE_SAMETIMEMOVE == attClass.getAdjustType()) {

                String shiftId = attClass.getSwapShiftId();
                AttShift attShift = attShiftMap.get(shiftId);
                AttShiftItem attShiftItem = ModelUtil.copyProperties(attShift, new AttShiftItem());
                List<String> timeSlotIdList =
                    AttShiftSchUtils.getTimeSlotByShiftAndDate(attShiftItem, adjustDate, adjustDate);
                List<AttTimeSlotBO> timeSlotBOList = new ArrayList<>();
                String dateStr = AttDateUtils.dateToStrAsShort(adjustDate);
                for (String timeSlotId : timeSlotIdList) {
                    AttTimeSlotBO attTimeSlotBO = new AttTimeSlotBO();

                    AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(timeSlotId);
                    attTimeSlotBO.setAttTimeSlotId(timeSlotId);
                    // 设置时段是否垮天
                    AttCommonUtils.setSetFirstAndSecondDay(attTimeSlotBO, attTimeSlotItem, dateStr, crossDay);
                    timeSlotBOList.add(attTimeSlotBO);
                }

                List<AttPersonSchBO> attPersonSchBOList = new ArrayList<>();
                AttPersonSchBO attPersonSchBO = new AttPersonSchBO();
                attPersonSchBO.setId(attClass.getId());
                attPersonSchBO.setAttendStatus(AttCalculationConstant.AttAttendStatus.SAME_DATE_CLASS);
                attPersonSchBO.setAttShiftId(shiftId);
                attPersonSchBO.setAttShiftName(attShiftItem.getShiftName());
                attPersonSchBO.setAttendanceMode(attShiftItem.getAttendanceMode());
                attPersonSchBO.setOvertimeMode(attShiftItem.getOvertimeMode());
                attPersonSchBO.setOvertimeRemark(attShiftItem.getOvertimeSign());
                attPersonSchBO.setWorkType(attShiftItem.getWorkType());
                attPersonSchBO.setShiftType(attShiftItem.getShiftType());
                AttShiftSchUtils.sortTimeSlot(timeSlotBOList, attTimeSlotItemMap);
                attPersonSchBO.setAttTimeSlotArray(timeSlotBOList);
                attPersonSchBOList.add(attPersonSchBO);

                String key = attClass.getAdjustPersonPin() + AttCalculationConstant.KEY_CONNECTOR
                    + AttDateUtils.dateToStrAsShort(adjustDate);
                attClassMap.put(key, attPersonSchBOList);

            } else if (AttConstant.CLASS_TYPE_DIFFTIMEMOVE == attClass.getAdjustType()) {

                // 调整天的班次
                Map<String, List<AttPersonSchBO>> personSchDataMap = attPersonSchDataService
                    .getPersonSchData(Arrays.asList(attClass.getAdjustPersonPin()), adjustDate, adjustDate);
                String key = attClass.getAdjustPersonPin() + AttCalculationConstant.KEY_CONNECTOR
                    + AttDateUtils.dateToStrAsShort(adjustDate);
                List<AttPersonSchBO> attPersonSchBOList = personSchDataMap.get(key);
                if (!CollectionUtil.isEmpty(attPersonSchBOList)) {
                    attPersonSchBOList.parallelStream().forEach(bo -> {
                        bo.setId(attClass.getId());
                    });
                }

                // 对调天的班次
                Date swapDate = attClass.getSwapDate();
                Map<String, List<AttPersonSchBO>> personSchDataMap2 = attPersonSchDataService
                    .getPersonSchData(Arrays.asList(attClass.getAdjustPersonPin()), swapDate, swapDate);
                String key2 = attClass.getAdjustPersonPin() + AttCalculationConstant.KEY_CONNECTOR
                    + AttDateUtils.dateToStrAsShort(swapDate);
                List<AttPersonSchBO> attPersonSchBOList2 = personSchDataMap2.get(key2);
                if (!CollectionUtil.isEmpty(attPersonSchBOList2)) {
                    attPersonSchBOList2.parallelStream().forEach(bo -> {
                        bo.setId(attClass.getId());
                    });
                }

                // 调整对应排班的日期
                for (AttPersonSchBO attPersonSchBO: attPersonSchBOList) {
                    List<AttTimeSlotBO>  attTimeSlotBOList = attPersonSchBO.getAttTimeSlotArray();
                    for (AttTimeSlotBO attTimeSlotBO : attTimeSlotBOList) {
                        AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());
                        AttCommonUtils.setSetFirstAndSecondDay(attTimeSlotBO, attTimeSlotItem, AttDateUtils.dateToStrAsShort(swapDate), crossDay);
                    }
                }
                for (AttPersonSchBO attPersonSchBO2: attPersonSchBOList2) {
                    List<AttTimeSlotBO>  attTimeSlotBOList = attPersonSchBO2.getAttTimeSlotArray();
                    for (AttTimeSlotBO attTimeSlotBO : attTimeSlotBOList) {
                        AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(attTimeSlotBO.getAttTimeSlotId());
                        AttCommonUtils.setSetFirstAndSecondDay(attTimeSlotBO, attTimeSlotItem, AttDateUtils.dateToStrAsShort(adjustDate), crossDay);
                    }
                }

                // 调整天填充对调天的班次, 对调天填充调整天的班次
                attClassMap.put(key, attPersonSchBOList2);
                attClassMap.put(key2, attPersonSchBOList);

            } else if (AttConstant.CLASS_TYPE_PEOPLEMOVE == attClass.getAdjustType()) {
                // 调整人那天的班次
                Map<String, List<AttPersonSchBO>> personSchDataMap = attPersonSchDataService
                    .getPersonSchData(Arrays.asList(attClass.getAdjustPersonPin()), adjustDate, adjustDate);
                String key = attClass.getAdjustPersonPin() + AttCalculationConstant.KEY_CONNECTOR
                    + AttDateUtils.dateToStrAsShort(adjustDate);
                List<AttPersonSchBO> attPersonSchBOList = personSchDataMap.get(key);
                if (!CollectionUtil.isEmpty(attPersonSchBOList)) {
                    attPersonSchBOList.parallelStream().forEach(bo -> {
                        bo.setId(attClass.getId());
                    });
                }

                // 对调人那天的班次
                Date swapDate = attClass.getSwapDate();
                Map<String, List<AttPersonSchBO>> personSchDataMap2 = attPersonSchDataService
                    .getPersonSchData(Arrays.asList(attClass.getSwapPersonPin()), swapDate, swapDate);
                String key2 = attClass.getSwapPersonPin() + AttCalculationConstant.KEY_CONNECTOR
                    + AttDateUtils.dateToStrAsShort(swapDate);
                List<AttPersonSchBO> attPersonSchBOList2 = personSchDataMap2.get(key2);
                if (!CollectionUtil.isEmpty(attPersonSchBOList2)) {
                    attPersonSchBOList2.parallelStream().forEach(bo -> {
                        bo.setId(attClass.getId());
                    });
                }

                // 调整人那天填充对调人那天的班次, 对调人那天填充调整人那天的班次
                attClassMap.put(key, attPersonSchBOList2);
                attClassMap.put(key2, attPersonSchBOList);
            }

        }

        return attClassMap;
    }
}