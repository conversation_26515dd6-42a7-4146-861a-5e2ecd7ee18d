package com.zkteco.zkbiosecurity.att.data.upgrade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 升级到2.3.0
 * 
 */
@Slf4j
@Component
public class AttVer2_3_0 implements UpgradeVersionManager {

    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;


    @Override
    public String getModule() {
        return BaseConstants.ATT;
    }

    @Override
    public String getVersion() {
        return "v2.3.0";
    }

    @Override
    public boolean executeUpgrade() {

        // 增加未签到未签退规则参数初始化
        baseSysParamService.initData(new BaseSysParamItem("att.baseRule.noSignInCountType", "absent", I18nUtil.i18nCode("att_rule_noSignInCountType")));
        baseSysParamService.initData(new BaseSysParamItem("att.baseRule.noSignInCountLateMinute", "30", I18nUtil.i18nCode("att_rule_noSignInCountLateMinute")));
        baseSysParamService.initData(new BaseSysParamItem("att.baseRule.noSignOffCountType", "absent", I18nUtil.i18nCode("att_rule_noSignOffCountType")));
        baseSysParamService.initData(new BaseSysParamItem("att.baseRule.noSignOffCountEarlyMinute", "60", I18nUtil.i18nCode("att_rule_noSignOffCountEarlyMinute")));
        baseSysParamService.initData(new BaseSysParamItem("att.other.noCheckInIncomplete", "├", I18nUtil.i18nCode("att_rule_noCheckInIncomplete")));
        baseSysParamService.initData(new BaseSysParamItem("att.other.noCheckOutIncomplete", "┤", I18nUtil.i18nCode("att_rule_noCheckOutIncomplete")));

        // 考勤初始化假种的key改为国际化语言；
        attLeaveTypeService.upgradeTo_232();

        return true;
    }

}
