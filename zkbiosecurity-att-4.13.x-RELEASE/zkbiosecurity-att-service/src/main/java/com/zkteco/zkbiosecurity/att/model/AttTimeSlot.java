package com.zkteco.zkbiosecurity.att.model;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 时间段
 */
@Entity
@Table(name = "ATT_TIMESLOT")
@Setter
@Getter
@Accessors(chain = true)
public class AttTimeSlot extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 时段类型（0：正常时间段，1：弹性时间段）
     */
    @Column(name = "PERIOD_TYPE")
    private Short periodType;

    /**
     * 时段编号
     */
    @Column(name = "PERIOD_NO", length = 50)
    @Deprecated
    private String periodNo;

    /**
     * 时段名称
     */
    @Column(name = "PERIOD_NAME", length = 50)
    private String periodName;

    /**
     * 开始签到时间
     */
    @Column(name = "START_SIGN_IN_TIME", length = 20)
    private String startSignInTime;

    /**
     * 上班时间
     */
    @Column(name = "TO_WORK_TIME", length = 20)
    private String toWorkTime;

    /**
     * 结束签到时间
     */
    @Column(name = "END_SIGN_IN_TIME", length = 20)
    @Deprecated
    private String endSignInTime;

    /**
     * 允许迟到分钟数
     */
    @Column(name = "ALLOW_LATE_MINUTES")
    private Short allowLateMinutes;

    /**
     * 必须签到（false：否/0，true：是/1）
     */
    @Column(name = "IS_MUST_SIGN_IN")
    private Boolean isMustSignIn;

    /**
     * 开始签退时间
     */
    @Column(name = "START_SIGN_OFF_TIME", length = 20)
    @Deprecated
    private String startSignOffTime;

    /**
     * 下班时间
     */
    @Column(name = "OFF_WORK_TIME", length = 20)
    private String offWorkTime;

    /**
     * 结束签退时间
     */
    @Column(name = "END_SIGN_OFF_TIME", length = 20)
    private String endSignOffTime;

    /**
     * 允许早退分钟数
     */
    @Column(name = "ALLOW_EARLY_MINUTES")
    private Short allowEarlyMinutes;

    /**
     * 必须签退（false：否/0，true：是/1）
     */
    @Column(name = "IS_MUST_SIGN_OFF")
    private Boolean isMustSignOff;

    /**
     * 工作时长（分钟）
     */
    @Column(name = "WORKING_HOURS")
    private Short workingHours;

    /**
     * 是否段间扣除（false：否/0，true：是/1）
     */
    @Column(name = "IS_SEGMENT_DEDUCTION")
    private Boolean isSegmentDeduction;

    /**
     * 段间扣除
     */
    @Column(name = "INTER_SEGMENT_DEDUCTION")
    private Short interSegmentDeduction;

    /**
     * 开始段间时间
     */
    @Column(name = "START_SEGMENT_TIME", length = 20)
    @Deprecated
    private String startSegmentTime;

    /**
     * 结束段间时间
     */
    @Column(name = "END_SEGMENT_TIME", length = 20)
    @Deprecated
    private String endSegmentTime;

    /**
     * 记为工作日数
     */
    @Column(name = "MARK_WORKING_DAYS", length = 20)
    private String markWorkingDays;

    /**
     * 延时是否计加班（false：否/0，true：是/1）
     */
    @Column(name = "IS_DELAY_COUNT_OVERTIME")
    private Boolean isDelayCountOvertime;

    /**
     * 起记加班时间
     */
    @Column(name = "START_OVERTIME", length = 20)
    private String startOvertime;

    /**
     * 提前是否计加班（false：否/0，true：是/1）
     */
    @Column(name = "IS_ADVANCE_COUNT_OVERTIME")
    private Boolean isAdvanceCountOvertime;

    /**
     * 签到早于时间
     */
    @Column(name = "SIGN_IN_ADVANCE_TIME", length = 20)
    @Deprecated
    private String signInAdvanceTime;

    /**
     * 延后是否计加班（false：否/0，true：是/1）
     */
    @Column(name = "IS_POSTPONE_COUNT_OVERTIME")
    private Boolean isPostponeCountOvertime;

    /**
     * 签退晚于时间
     */
    @Deprecated
    @Column(name = "SIGN_OUT_POSPONE_TIME", length = 20)
    private String signOutPosponeTime;

    /**
     * 是否计加班（false：否/0，true：是/1）
     */
    @Column(name = "IS_COUNT_OVERTIME")
    private Boolean isCountOvertime;

    /**
     * 弹性时长计算方式 0:两两打卡累积时长 1:首尾打卡计算时长
     */
    @Column(name = "ELASTIC_CAL", length = 4)
    private String elasticCal;

    /**
     * 上班前n分钟内签到有效
     */
    @Column(name = "BEFORE_TO_WORK_MINUTES")
    private Integer beforeToWorkMinutes;

    /**
     * 上班后n分钟内签到有效
     */
    @Column(name = "AFTER_TO_WORK_MINUTES")
    private Integer afterToWorkMinutes;

    /**
     * 下班前n分钟内签到有效
     */
    @Column(name = "BEFORE_OFF_WORK_MINUTES")
    private Integer beforeOffWorkMinutes;

    /**
     * 下班前n分钟内签到有效
     */
    @Column(name = "AFTER_OFF_WORK_MINUTES")
    private Integer afterOffWorkMinutes;

    /**
     * 上班n分钟前签到记加班
     */
    @Column(name = "BEFORE_WORK_OVERTIME_MINUTES")
    private Integer beforeWorkOvertimeMinutes;

    /**
     * 上班前最短加班分钟数
     */
    @Column(name = "MIN_BEFORE_OVERTIME_MINUTES")
    private Integer minBeforeOvertimeMinutes;

    /**
     * 上班前最大加班分钟数
     */
    @Column(name = "MAX_BEFORE_OVERTIME_MINUTES")
    private Integer maxBeforeOvertimeMinutes;

    /**
     * 下班n分钟后开始记加班
     */
    @Column(name = "AFTER_WORK_OVERTIME_MINUTES")
    private Integer afterWorkOvertimeMinutes;

    /**
     * 下班后最短加班分钟数
     */
    @Column(name = "MIN_AFTER_OVERTIME_MINUTES")
    private Integer minAfterOvertimeMinutes;

    /**
     * 下班后最大加班分钟数
     */
    @Column(name = "MAX_AFTER_OVERTIME_MINUTES")
    private Integer maxAfterOvertimeMinutes;

    /**
     * 是否启动工作时长(计划用于弹性时间段--打卡满工作时长即可)
     */
    @Column(name = "ENABLE_WORKING_HOURS")
    private String enableWorkingHours;

    /**
     * 是否启动弹性上班(true:启用。false：不启用)
     */
    @Column(name = "ENABLE_FLEXIBLE_WORK", length = 10)
    private String enableFlexibleWork;

    /**
     * 可提前上班分钟数
     */
    @Column(name = "ADVANCE_WORK_MINUTES")
    private Integer advanceWorkMinutes;

    /**
     * 可延后上班分钟数
     */
    @Column(name = "DELAYED_WORK_MINUTES")
    private Integer delayedWorkMinutes;

    /**
     * 时间段休息时间段中间表
     */
    @ManyToMany(targetEntity = AttBreakTime.class, fetch = FetchType.EAGER)
    @JoinTable(name = "ATT_TIMESLOT_BREAKTIME",
        joinColumns = @JoinColumn(name = "TIMESLOT_ID", referencedColumnName = "ID"),
        inverseJoinColumns = @JoinColumn(name = "BREAKTIME_ID", referencedColumnName = "ID"))
    private Set<AttBreakTime> attBreakTimeSet = new HashSet<AttBreakTime>();

    /**
     * 班次时间段中间表
     */
    @ManyToMany(targetEntity = AttShift.class, fetch = FetchType.LAZY)
    @JoinTable(name = "ATT_SHIFT_TIMESLOT",
        joinColumns = @JoinColumn(name = "TIMESLOT_ID", referencedColumnName = "ID"),
        inverseJoinColumns = @JoinColumn(name = "SHIFT_ID", referencedColumnName = "ID"))
    private Set<AttShift> attShiftSet = new HashSet<AttShift>();
}