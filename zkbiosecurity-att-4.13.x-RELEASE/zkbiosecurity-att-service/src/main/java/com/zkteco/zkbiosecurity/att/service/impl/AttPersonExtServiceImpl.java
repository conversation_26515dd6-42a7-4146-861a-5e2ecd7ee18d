package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.att.dao.AttAreaPersonDao;
import com.zkteco.zkbiosecurity.att.dao.AttCycleSchDao;
import com.zkteco.zkbiosecurity.att.dao.AttDeviceDao;
import com.zkteco.zkbiosecurity.att.dao.AttTempSchDao;
import com.zkteco.zkbiosecurity.att.model.AttAreaPerson;
import com.zkteco.zkbiosecurity.att.model.AttDevice;
import com.zkteco.zkbiosecurity.att.service.AttAreaPersonService;
import com.zkteco.zkbiosecurity.att.service.AttDeviceOptionService;
import com.zkteco.zkbiosecurity.att.service.AttDeviceService;
import com.zkteco.zkbiosecurity.att.service.AttPersonService;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.cmd.AttDevCmdManager;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonExtService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.*;

/**
 *
 * 实现人事调用其他业务模块的实现类
 *
 * <AUTHOR>
 * @date 2020-11-23 17:33
 * @sine 1.0.0
 */
@Component
public class AttPersonExtServiceImpl implements PersPersonExtService {

    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AttDevCmdManager attDevCmdManager;
    @Autowired
    private AttDeviceService attDeviceService;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttDeviceOptionService attDeviceOptionService;
    @Autowired
    private AttTempSchDao attTempSchDao;
    @Autowired
    private AttCycleSchDao attCycleSchDao;
    @Autowired
    private AttAreaPersonDao attAreaPersonDao;
    @Autowired
    private AttAreaPersonService attAreaPersonService;
    @Autowired
    private AttDeviceDao attDeviceDao;

    @Override
    public void deletePersonBioTemplate(Collection<String> personIds, Collection<Short> bioTemplateTypes) {

        // 根据人员Id查找所在设备
        Map<String, List<AttDeviceItem>> pinAndDeviceListMap = attDeviceService.getPinAndDeviceListMap(personIds);

        // 循环人员，下发删除模板命令到对应设备
        List<String> pinList = persPersonService.getPinsByIds((List<String>)personIds);
        for (String pin : pinList) {
            List<AttDeviceItem> attDeviceList = pinAndDeviceListMap.get(pin);
            if (CollectionUtil.isEmpty(attDeviceList)) {
                continue;
            }

            List<List<AttDeviceItem>> attDeviceItemListGroup = CollectionUtil.split(attDeviceList, CollectionUtil.splitSize);
            for (List<AttDeviceItem> attDeviceItems : attDeviceItemListGroup) {
                Collection<String> devIdList =
                    CollectionUtil.getPropertyList(attDeviceItems, AttDeviceItem::getId, AttConstant.COMM_DEF_VALUE);

                // 获取设备的参数
                Map<String, List<AttDeviceOptionItem>> devOptionMap = attDeviceOptionService.getDeviceIdAndDeviceOptionMap(devIdList);
                if (CollectionUtil.isEmpty(devOptionMap)) {
                    continue;
                }

                attDeviceItems.forEach(attDeviceItem -> {
                    List<AttDeviceOptionItem> devOptions = devOptionMap.get(attDeviceItem.getId());
                    bioTemplateTypes.forEach(type -> {
                        attDevCmdManager.delBioTemplate(attDeviceItem, devOptions, pin, type, false);
                        // 可见光人脸还需同时删除比对照片
                        if (BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE.equals(type)) {
                            attDevCmdManager.delBioPhoto(attDeviceItem, devOptions, pin, false);
                        }
                    });
                });
            }
        }
    }

    @Override
    public void setOtherPersonInfo2Dev(Collection<PersPersonInfo2OtherItem> items, String module) {
        // 过滤本模块自己推送的数据
        if (ConstUtil.SYSTEM_MODULE_ATT.equals(module)) {
            return;
        }
        Collection<String> personIds =
            CollectionUtil.getPropertyList(items, PersPersonInfo2OtherItem::getPersonId, AttConstant.COMM_DEF_VALUE);
        // 根据人员Id查找所在设备
        Map<String, List<AttDeviceItem>> pinAndDeviceListMap = attDeviceService.getPinAndDeviceListMap(personIds);
        // 获取考勤人员授权信息
        Map<String, AttPersonItem> personIdAndAttPersonItemMap = attPersonService.getPersonIdAndAttPersonItemMap(personIds);
        // 异步下发
        CompletableFuture.runAsync(() -> {
            for (PersPersonInfo2OtherItem item : items) {
                List<AttDeviceItem> attDevices = pinAndDeviceListMap.get(item.getPin());
                if (CollectionUtil.isEmpty(attDevices)) {
                    continue;
                }
                // 这边转成对应的PersPersonItem、PersCardItem命令的统一组装！
                PersPersonItem personItem = new PersPersonItem();
                personItem.setPin(item.getPin());
                personItem.setName(item.getName());
                personItem.setLastName(item.getLastName());
                personItem.setPersonPwd(item.getPersonPwd());
                PersCardItem persCardItem = new PersCardItem();
                persCardItem.setCardNo(item.getCardNo());
                AttPersonItem attPersonItem = personIdAndAttPersonItemMap.get(item.getPersonId());
                AttPersonOptItem attPersonOptItem = attDevCmdManager.buildPersonOptItem(personItem, persCardItem, attPersonItem);
                attDevices.forEach(dev -> {
                    attDevCmdManager.sendPersonToDev(dev.getDevSn(), attPersonOptItem);
                });
            }
        });
    }

    @Override
    public void setOtherBioTemplate2Dev(Collection<PersBioTemplate2OtherItem> items, String module) {
        // 过滤本模块自己推送的数据
        if (ConstUtil.SYSTEM_MODULE_ATT.equals(module)) {
            return;
        }

        Collection<String> personIds =
            CollectionUtil.getPropertyList(items, PersBioTemplate2OtherItem::getPersonId, AttConstant.COMM_DEF_VALUE);

        // 根据人员Id查找所在设备
        Map<String, List<AttDeviceItem>> pinAndDeviceListMap = attDeviceService.getPinAndDeviceListMap(personIds);

        CompletableFuture.runAsync(() -> {
            for (PersBioTemplate2OtherItem item : items) {
                AttBioTemplateItem attBioTemplate = new AttBioTemplateItem();
                ModelUtil.copyPropertiesIgnoreNull(item, attBioTemplate);
                attBioTemplate.setPersonPin(item.getPin());
                // 人员对应的设备集合
                List<AttDeviceItem> attDevices = pinAndDeviceListMap.get(item.getPin());

                Collection<String> devIdList =
                    CollectionUtil.getPropertyList(attDevices, AttDeviceItem::getId, AttConstant.COMM_DEF_VALUE);
                // 获取设备的参数
                Map<String, List<AttDeviceOptionItem>> devOptionMap = attDeviceOptionService.getDeviceIdAndDeviceOptionMap(devIdList);

                attDevices.forEach(dev -> {
                    List<AttDeviceOptionItem> devOptions = devOptionMap.get(dev.getId());
                    // 下发生物模板到设备
                    attDevCmdManager.setBioTemplate2Dev(dev, devOptions, attBioTemplate, false);
                });
            }
        });
    }

    @Override
    public void setOtherBioPhoto2Dev(Collection<PersBioPhoto2OtherItem> items, String module) {
        // 过滤本模块自己推送的数据
        if (ConstUtil.SYSTEM_MODULE_ATT.equals(module)) {
            return;
        }
        // 根据人员Id查找人员区域对应的设备集合
        Collection<String> personIds =
            CollectionUtil.getPropertyList(items, PersBioPhoto2OtherItem::getPersonId, AttConstant.COMM_DEF_VALUE);

        Map<String, List<AttDeviceItem>> personDevicesMap = attDeviceService.getPinAndDeviceListMap(personIds);

        CompletableFuture.runAsync(() -> {
            for (PersBioPhoto2OtherItem item : items) {
                // vo转换
                AttBioTemplateItem attBioTemplate = new AttBioTemplateItem();
                ModelUtil.copyPropertiesIgnoreNull(item, attBioTemplate);
                attBioTemplate.setPersonPin(item.getPin());
                // 临时用模板属性，pro版本再重新整理
                attBioTemplate.setTemplate(item.getContent());

                // 人员对应的设备集合
                List<AttDeviceItem> attDevices = personDevicesMap.get(item.getPin());

                Collection<String> devIdList =
                    CollectionUtil.getPropertyList(attDevices, AttDeviceItem::getId, AttConstant.COMM_DEF_VALUE);
                // 获取设备的参数
                Map<String, List<AttDeviceOptionItem>> devOptionMap = attDeviceOptionService.getDeviceIdAndDeviceOptionMap(devIdList);
                attDevices.forEach(dev -> {
                    List<AttDeviceOptionItem> devOptions = devOptionMap.get(dev.getId());
                    // 下发比对照片到设备
                    attDevCmdManager.setBioPhoto2Dev(dev, devOptions, attBioTemplate, false);
                });
            }
        });
    }

    @Override
    public void setOtherUserPic2Dev(Collection<PersUserPic2OtherItem> items, String module) {
        // 过滤本模块自己推送的数据
        if (ConstUtil.SYSTEM_MODULE_ATT.equals(module)) {
            return;
        }
        // 根据人员Id查找人员区域对应的设备集合
        Collection<String> personIds =
            CollectionUtil.getPropertyList(items, PersUserPic2OtherItem::getPersonId, AttConstant.COMM_DEF_VALUE);

        Map<String, List<AttDeviceItem>> personDevicesMap = attDeviceService.getPinAndDeviceListMap(personIds);

        CompletableFuture.runAsync(() -> {
            for (PersUserPic2OtherItem item : items) {
                // vo转换
                AttBioTemplateItem attBioTemplate = new AttBioTemplateItem();
                ModelUtil.copyPropertiesIgnoreNull(item, attBioTemplate);
                attBioTemplate.setPersonPin(item.getPin());
                // 临时用模板属性，pro版本再重新整理
                attBioTemplate.setTemplate(item.getContent());

                // 人员对应的设备集合
                List<AttDeviceItem> attDevices = personDevicesMap.get(item.getPin());
                Collection<String> devIdList =
                    CollectionUtil.getPropertyList(attDevices, AttDeviceItem::getId, AttConstant.COMM_DEF_VALUE);
                // 获取设备的参数
                Map<String, List<AttDeviceOptionItem>> devOptionMap = attDeviceOptionService.getDeviceIdAndDeviceOptionMap(devIdList);
                attDevices.forEach(dev -> {
                    List<AttDeviceOptionItem> devOptions = devOptionMap.get(dev.getId());
                    // 下发人员头像到设备
                    attDevCmdManager.setUserPic2Dev(dev, devOptions, attBioTemplate, false);
                });
            }
        });
    }


    @Override
    public void issuedCard(List<PersCardItem> persCardItems) {
        if (CollectionUtil.isEmpty(persCardItems)) {
            return;
        }
        Collection<String> personIds =
                CollectionUtil.getPropertyList(persCardItems, PersCardItem::getPersonId, AttConstant.COMM_DEF_VALUE);
        // 根据人员Id查找所在设备
        Map<String, List<AttDeviceItem>> personDevicesMap =  attDeviceService.getPinAndDeviceListMap(personIds);
        // 获取考勤人员授权信息
        Map<String, AttPersonItem> personIdAndAttPersonItemMap = attPersonService.getPersonIdAndAttPersonItemMap(personIds);
        // 查询人员信息
        List<PersPersonItem> personItemList = persPersonService.getSimpleItemsByIds(new ArrayList<>(personIds));
        Map<String, PersPersonItem> personItemMap = CollectionUtil.listToKeyMap(personItemList, PersPersonItem::getId);
        // 异步下发
        CompletableFuture.runAsync(() -> {
            for (PersCardItem persCardItem : persCardItems) {
                List<AttDeviceItem> attDevices = personDevicesMap.get(persCardItem.getPersonPin());
                if (CollectionUtil.isEmpty(attDevices)) {
                    continue;
                }
                PersPersonItem personItem = personItemMap.get(persCardItem.getPersonId());
                AttPersonItem attPerson = personIdAndAttPersonItemMap.get(persCardItem.getPersonId());
                AttPersonOptItem attPersonOptItem = attDevCmdManager.buildPersonOptItem(personItem, persCardItem, attPerson);
                attDevices.forEach(dev -> {
                    attDevCmdManager.sendPersonToDev(dev.getDevSn(), attPersonOptItem);
                });
            }
        });
    }

    @Override
    public void pushLeavePersonDel(List<PersPushLeavePersonItem> persPushLeavePersonItems) {
        if (persPushLeavePersonItems == null || persPushLeavePersonItems.size() == 0) {
            return;
        }

        // 离职人员删除，删除对应的人员排班
        Collection<String> pins =  CollectionUtil.getPropertyList(persPushLeavePersonItems, PersPushLeavePersonItem::getPin, AttConstant.COMM_DEF_VALUE);
        attTempSchDao.deleteByPersonPinIn(pins);
        attCycleSchDao.deleteByPersonPinIn(pins);
    }


    @Override
    public void enabledCredential(String personIds) {
        if (StringUtils.isBlank(personIds)) {
            return;
        }

        List<AttAreaPerson> areaPersonList = attAreaPersonDao.findByPersonIdIn(StrUtil.strToList(personIds));
        if (!CollectionUtils.isEmpty(areaPersonList)) {
            for (AttAreaPerson attAreaPerson : areaPersonList) {
                attAreaPersonService.syncAttPersonToDevice(attAreaPerson.getPersonId(), attAreaPerson.getAreaId(), "-1");
            }
        }

    }

    @Override
    public void disableCredential(String personIds) {

        if (StringUtils.isBlank(personIds)) {
            return;
        }

        // 人员ID和编号集合
        List<String> personIdList = StrUtil.strToList(personIds);
        List<PersPersonItem> persPersonItemList = persPersonService.getSimpleItemsByIds(personIdList);
        Map<String, String> personIdPinMap =
                persPersonItemList.stream().collect(Collectors.toMap(PersPersonItem::getId, PersPersonItem::getPin));

        // 人员ID和人员区域集合
        List<Object[]> objectList = attAreaPersonDao.getAreaIdsByPersonIds(personIdList);
        // key 为 areaID Object[] 下标：0：persionId 下标：1 areaid
        Map<String, List<Object[]>> areaIdPersonMap =
                objectList.stream().collect(Collectors.groupingBy((Object[] o) -> o[1].toString()));

        // 下发删除设备人员信息
        Set<String> areaIdSet = areaIdPersonMap.keySet();
        if (areaIdSet != null && areaIdSet.size() > 0) {
            List<List<String>> areaIdListGroup = CollectionUtil.split(areaIdPersonMap.keySet(), 500);
            for (List<String> areaIdList : areaIdListGroup) {
                List<AttDevice> attDeviceList = attDeviceDao.findByAreaIdIn(areaIdList);
                attDeviceList.forEach(device -> {
                    List<Object[]> objects = areaIdPersonMap.get(device.getAreaId());
                    if (!CollectionUtil.isEmpty(objects)) {
                        objects.forEach((Object[] o) -> {
                            attDevCmdManager.delUserInfo(device, personIdPinMap.get(o[0].toString()));
                        });
                    }
                });
            }
        }
    }
}