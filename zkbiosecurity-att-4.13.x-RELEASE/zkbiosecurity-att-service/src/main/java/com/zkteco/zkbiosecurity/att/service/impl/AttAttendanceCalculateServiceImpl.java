package com.zkteco.zkbiosecurity.att.service.impl;

import com.zkteco.zkbiosecurity.att.bean.AttPersonAttendanceBean;
import com.zkteco.zkbiosecurity.att.bean.AttRuleParamBean;
import com.zkteco.zkbiosecurity.att.calc.bo.AttLeaveBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttOvertimeBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.calculation.AttFlexibleAttendanceCalculate;
import com.zkteco.zkbiosecurity.att.calculation.AttIntelligentPersonSch;
import com.zkteco.zkbiosecurity.att.calculation.AttNormalAttendanceCalculate;
import com.zkteco.zkbiosecurity.att.calculation.AttOtherAttendanceCalculate;
import com.zkteco.zkbiosecurity.att.constants.AttShiftConstant;
import com.zkteco.zkbiosecurity.att.service.AttAttendanceCalculateService;
import com.zkteco.zkbiosecurity.att.vo.AttRecordItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 考勤计算入口，针对每人每天的考勤计算
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/2 9:09
 * @since 1.0.0
 */
@Service
public class AttAttendanceCalculateServiceImpl implements AttAttendanceCalculateService {

    @Autowired
    private AttIntelligentPersonSch attIntelligentPersonSch;
    @Autowired
    private AttOtherAttendanceCalculate attOtherAttendanceCalculate;
    @Autowired
    private AttNormalAttendanceCalculate attNormalAttendanceCalculate;
    @Autowired
    private AttFlexibleAttendanceCalculate attFlexibleAttendanceCalculate;

    @Override
    public AttRecordItem calculate(AttPersonAttendanceBean attPersonAttendanceBean) {
        // 人天考勤计算参数
        String pinAndDate = attPersonAttendanceBean.getPinAndDate();
        List<AttPersonSchBO> personSchDataMap = attPersonAttendanceBean.getPersonSchDataMap();
        Map<String, List<String>> transactionDataMap = attPersonAttendanceBean.getTransactionDataMap();
        AttRuleParamBean attRuleParamBean = attPersonAttendanceBean.getAttRuleParamBean();
        Map<String, AttTimeSlotItem> attTimeSlotItemMap = attPersonAttendanceBean.getAttTimeSlotItemMap();
        List<AttLeaveBO> attLeaveBOList = attPersonAttendanceBean.getAttLeaveBOList();
        List<AttOvertimeBO> attOvertimeBOList = attPersonAttendanceBean.getAttOvertimeBOList();


        AttRecordItem attRecordItem = initAttRecordItem();

        if(StringUtils.isNotBlank(pinAndDate)){
            attRecordItem.setPersonPin(pinAndDate.split("=")[0]);
        }

        // 非正常考勤计算流程（未排班、排班无时间段<排班且休息>、调休、节假日<班次非节假日加班>），直接填充attRecordItem对象
        boolean otherRes = attOtherAttendanceCalculate.otherAttendance(attRecordItem, personSchDataMap);
        if (otherRes) {
            return attRecordItem;
        }

        // 填充排班卡点数据，选择智能找班班次，并返回人/天最终的排班对象
        AttPersonSchBO attPersonSchBO = attIntelligentPersonSch.fillPersSchCardData(pinAndDate, personSchDataMap,
            transactionDataMap, attRuleParamBean, attTimeSlotItemMap, attLeaveBOList);

        // 班次再过滤非正常考勤（处理智能找班）
        personSchDataMap.clear();
        personSchDataMap.add(attPersonSchBO);
        if (attOtherAttendanceCalculate.otherAttendance(attRecordItem, personSchDataMap)) {
            return attRecordItem;
        }

        // 根据班次类型（0:规律班次，1:弹性班次）和人员是否免打卡走不同流程
        Boolean isAttendance = attPersonAttendanceBean.getIsAttendance();
        Short shiftType = attPersonSchBO.getShiftType();
        if (AttShiftConstant.ShiftType.REGULAR_SHIFT == shiftType) {
            if (isAttendance) {
                // 规律班次(正常考勤)
                attNormalAttendanceCalculate.analyseRegularShift(attPersonSchBO, attLeaveBOList, attOvertimeBOList,
                    attTimeSlotItemMap, attRuleParamBean, attRecordItem);
            } else {
                // 规律班次(免打卡)
                attNormalAttendanceCalculate.fillFreeCardData(attPersonSchBO, attTimeSlotItemMap, attRuleParamBean,
                    attRecordItem);
            }
        } else {
            // 弹性班次，填充考勤数据（无法计算请假、加班时长、迟到、早退，状态只有未签到、未签退、实到）
            attFlexibleAttendanceCalculate.fillElasticData(attPersonSchBO, attTimeSlotItemMap, attRuleParamBean,
                attRecordItem);
        }

        return attRecordItem;
    }

    /**
     * 初始化AttRecordItem默认值
     *
     * @return com.zkteco.zkbiosecurity.att.vo.AttRecordItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/12/11 11:12
     * @since 1.0.0
     */
    private AttRecordItem initAttRecordItem() {
        AttRecordItem attRecordItem = new AttRecordItem();
        attRecordItem.setShiftNo("");
        attRecordItem.setShiftName("");
        attRecordItem.setTimeSlotName("");
        attRecordItem.setShiftTimeData("");
        attRecordItem.setCardValidData("");
        attRecordItem.setCardValidCount(0);
        attRecordItem.setShouldMinute(0);
        attRecordItem.setShouldMinuteEx(0);
        attRecordItem.setActualMinute(0);
        attRecordItem.setValidMinute(0);
        attRecordItem.setLateCountData("");
        attRecordItem.setLateCountTotal(0);
        attRecordItem.setLateMinuteData("");
        attRecordItem.setLateMinuteTotal(0);
        attRecordItem.setEarlyCountData("");
        attRecordItem.setEarlyCountTotal(0);
        attRecordItem.setEarlyMinuteData("");
        attRecordItem.setEarlyMinuteTotal(0);
        attRecordItem.setAbsentMinute(0);
        attRecordItem.setOvertimeUsualMinute(0);
        attRecordItem.setOvertimeRestMinute(0);
        attRecordItem.setOvertimeHolidayMinute(0);
        attRecordItem.setOvertimeMinute(0);
        attRecordItem.setLeaveMinute(0);
        attRecordItem.setTripMinute(0);
        attRecordItem.setOutMinute(0);
        attRecordItem.setShouldDays(BigDecimal.ZERO);
        attRecordItem.setActualDays(BigDecimal.ZERO);
        attRecordItem.setValidDays(BigDecimal.ZERO);
        attRecordItem.setAbsentDays(BigDecimal.ZERO);
        attRecordItem.setLeaveDays(BigDecimal.ZERO);
        attRecordItem.setTripDays(BigDecimal.ZERO);
        attRecordItem.setOutDays(BigDecimal.ZERO);
        return attRecordItem;
    }
}
