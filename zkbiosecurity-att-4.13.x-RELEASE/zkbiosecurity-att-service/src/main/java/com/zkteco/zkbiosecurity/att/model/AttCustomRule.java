package com.zkteco.zkbiosecurity.att.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 考勤规则
 */
@Entity
@Table(name = "ATT_CUSTOM_RULE")
@Setter
@Getter
@Accessors(chain = true)
public class AttCustomRule extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 规则名称
     */
    @Column(name = "RULE_NAME")
    private String ruleName;

    /**
     * 规则类型: 0:分组规则; 1:部门规则
     */
    @Column(name = "RULE_TYPE", length = 4)
    private String ruleType;

    /**
     * 上班签到取卡记录原则
     */
    @Column(name = "SIGN_IN")
    private String signIn;

    /**
     * 下班签退取卡记录原则
     */
    @Column(name = "SIGN_OUT")
    private String signOut;

    /**
     * 迟到且早退算旷工
     */
    @Column(name = "LATE_AND_EARLY")
    private String lateAndEarly;

    /**
     * 班次时间段跨天时，考勤计算结果
     */
    @Column(name = "CROSS_DAY")
    private String crossDay;

    /**
     * 是否统计加班
     */
    @Column(name = "COUNT_OVERTIME")
    private String countOvertime;

    /**
     * 最短加班时长（分钟）
     */
    @Column(name = "SHORTEST_OVERTIME_MINUTES")
    private String shortestOvertimeMinutes;

    /**
     * 最大加班时长类型（不限制/本周/本月）
     */
    @Column(name = "MAXOVERTIME_TYPE")
    private String maxOvertimeType;

    /**
     * 最大加班时长（分钟）
     */
    @Column(name = "MAXOVERTIME_MINUTES")
    private String maxOvertimeMinutes;

    /**
     * 智能找班原则
     */
    @Column(name = "SMART_FIND_CLASS")
    private String smartFindClass;

    /**
     * 休息时段是否打卡
     */
    @Column(name = "SIGN_BREAK_TIME")
    private String signBreakTime;

}
