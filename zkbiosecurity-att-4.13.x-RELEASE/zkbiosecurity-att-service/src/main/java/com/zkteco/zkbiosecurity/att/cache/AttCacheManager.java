package com.zkteco.zkbiosecurity.att.cache;

import java.util.*;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.vo.AttDeviceItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;

/**
 * 考勤模块缓存管理
 *
 * <AUTHOR> href:"mailto:<EMAIL>">max.zheng</a>
 * @version v1.0
 */
@Component
public class AttCacheManager {

    @Resource(name = "redisTemplate")
    private RedisTemplate<String, AttDeviceItem> redisTemplate;

    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;

    /** 设备授权使用，过期自动删除 */
    public final String AUTHDEVICE_INFO_KEY = "att:authDevice:";

    /** 设备信息 */
    public final String DEVICE_INFO_KEY = "att:devInfo:";
    /** 是否初始化缓存，防止没有设备，redis查询不到数据，再次查库 */
    public final String DEVICE_INFO_INIT_KEY = "att:devInfo:init";

    /** ADMS心跳存在key */
    public static final String ADMS_DEV_HEARTBEAT = "adms:heartbeat:";
    /** ADMS设备信息缓存key */
    public static final String ADMS_DEVICE_INFO = "adms:devInfo:";

    /** 考勤前 */
    private static final String ATT_SIGN_KEY = "att:sign:";

    /**
     * 缓存设备信息
     */
    public void putDeviceInfo(AttDeviceItem item) {
        redisTemplate.opsForValue().set(DEVICE_INFO_KEY + item.getDevSn(), item);
    }

    /**
     * 获取缓存设备信息
     */
    public AttDeviceItem getDeviceInfo(String sn) {
        return redisTemplate.opsForValue().get(DEVICE_INFO_KEY + sn);
    }

    /**
     * 删除缓存设备信息
     */
    public void delDeviceInfo(String sn) {
        redisTemplate.delete(DEVICE_INFO_KEY + sn);
    }

    /**
     * 设备信息缓存key集合
     */
    public Set<String> getDeviceInfoKeySet() {
        return stringRedisTemplate.keys(DEVICE_INFO_KEY + "*");
    }

    /**
     * 根据key获取设备缓存集合
     */
    public AttDeviceItem getAttDeviceItemByKey(String key) {
        return  redisTemplate.opsForValue().get(key);
    }

    /**
     * 缓存临时授权设备信息
     */
    public void putAuthDeviceInfo(AttDeviceItem item) {
        redisTemplate.opsForValue().set(AUTHDEVICE_INFO_KEY + item.getDevSn(), item);
        redisTemplate.expire(AUTHDEVICE_INFO_KEY + item.getDevSn(), 600, TimeUnit.SECONDS);
    }

    /**
     * 获取临时缓存授权设备信息
     */
    public AttDeviceItem getAuthDeviceInfo(String sn) {
        return redisTemplate.opsForValue().get(AUTHDEVICE_INFO_KEY + sn);
    }

    /**
     * 加载所有待处理的OperLog信息key
     *
     * @return
     */
    public Set<String> loadOperLogKeyList() {
        return stringRedisTemplate.keys("adms:att:operlog:*");
    }

    /**
     * 加载考勤记录key list
     *
     * @return
     */
    public Set<String> loadAttLogKeyList() {
        return stringRedisTemplate.keys("adms:att:attlog:*");
    }

    /**
     * 加载考勤照片key list
     *
     * @return
     */
    public Set<String> loadAttPhotoKeyList() {
        return stringRedisTemplate.keys("adms:att:attphoto:*");
    }

    /**
     * 加载考勤照片key list
     *
     * @return
     */
    public Set<String> loadAttBioDataKeyList() {
        return stringRedisTemplate.keys("adms:att:biodata:*");
    }

    /**
     * 获取list左边第一个元素
     *
     * @param key
     * @return
     */
    public String getLeftFirstValue(String key) {
        return stringRedisTemplate.opsForList().leftPop(key);
    }

    /**
     * 清除上传人员相关数据
     *
     * @param sn
     */
    public void cleanOperLog(String sn) {
        // 人员、指纹、面部
        stringRedisTemplate.delete("adms:att:operlog:" + sn);
        // 模版一体化数据
        stringRedisTemplate.delete("adms:att:biodata:" + sn);
    }

    public void cleanBiodata(String sn) {
        stringRedisTemplate.delete("adms:att:biodata:" + sn);
    }

    /**
     * 加载所有待处理的OpLog信息key
     *
     * @return
     */
    public Set<String> loadOpLogKeyList() {
        return stringRedisTemplate.keys("adms:att:oplog:*");
    }

    /**
     * @param key
     * @return
     * @Description: 判断key是否存在
     */
    public boolean exists(String key) {
        return stringRedisTemplate.hasKey(key);
    }

    /**
     * @param prex
     * @return
     * @Description: key模糊删除
     */
    public void deleteByPrex(String prex) {
        Set<String> keys = redisTemplate.keys(prex);
        if (Objects.nonNull(keys) && keys.size() > 0) {
            redisTemplate.delete(keys);
        }
    }

    /**
     * 检查给定的元素是否在变量中
     *
     * @param key
     * @param hashKey
     * @return
     */
    @Deprecated
    public boolean isMember(String key, String hashKey) {
        boolean ret = false;
        if (exists(key)) {
            Object obj = stringRedisTemplate.opsForHash().get(key, hashKey);
            if (Objects.nonNull(obj)) {
                ret = true;
            }
        }
        return ret;
    }

    /**
     * 判断key是否存在(存在：true、不存在：false)
     *
     * @return
     */
    public boolean hasKey(String key) {
        if (stringRedisTemplate.hasKey(key)) {
            return true;
        }
        return false;
    }

    /**
     * 缓存考勤点对应拉取记录的时间戳
     *
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/7/15 16:18
     * @param key
     * @param lastTransactionTime
     * @return: void
     **/
    public void setPointPullTransaction(String key, String lastTransactionTime) {
        stringRedisTemplate.opsForValue().set(AttConstant.ATT_ATTENDPOINT + key, lastTransactionTime);
    }

    /**
     * 删除考勤点标记
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/7/15 16:18
     * @param key
     * @return: void
     **/
    public void delPointPullTransaction(String key) {
        stringRedisTemplate.delete(AttConstant.ATT_ATTENDPOINT + key);
    }

    /**
     * 获取正在拉取考勤记录的标记
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/7/15 16:17
     * @param key
     * @return: java.lang.String
     **/
    public String getPullTransactionFlag(String key) {
        return stringRedisTemplate.opsForValue().get(AttConstant.ATT_ATTENDPOINT_TRANSACTION + key);
    }

    /**
     * 获取考勤点最近一次拉取记录的时间戳
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/7/15 16:17
     * @param key
     * @return: java.lang.String
     **/
    public String getPointPullTransaction(String key) {
        return stringRedisTemplate.opsForValue().get(AttConstant.ATT_ATTENDPOINT + key);
    }

    /**
     * 设置正在拉取考勤记录的标记
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/7/15 16:17
     * @param key
     * @return: void
     **/
    public void setPullTransactionFlag(String key) {
        stringRedisTemplate.opsForValue().set(AttConstant.ATT_ATTENDPOINT_TRANSACTION + key, "1");
    }

    /**
     * 删除正在拉取考勤记录的标记
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/7/15 16:20
     * @param key
     * @return: void
     **/
    public void delPullTransactionFlag(String key) {
        stringRedisTemplate.delete(AttConstant.ATT_ATTENDPOINT_TRANSACTION + key);
    }

    /**
     * 清空拉取考勤记录的标记
     */
    public void deleteAllPullTransactionFlag() {
        deleteByPrex(AttConstant.ATT_ATTENDPOINT_TRANSACTION + "*");
    }

    /**
     * 获取所有缓存的考勤点
     *
     * @return
     */
    public Map<String, JSONObject> getAllPointPullTransaction() {
        Set<String> keySet = stringRedisTemplate.keys(AttConstant.ATT_ATTENDPOINT + "*");
        Map<String, JSONObject> attPointItems = new HashMap<>(16);
        if (!CollectionUtil.isEmpty(keySet)) {
            for (String key : keySet) {
                String module = key.replaceAll(AttConstant.ATT_ATTENDPOINT, "");
                JSONObject jsonObject = JSONObject.parseObject(stringRedisTemplate.opsForValue().get(key));
                attPointItems.put(module, jsonObject);
            }
        }
        return attPointItems;
    }

    /**
     * 设置人员打卡记录时间
     * 
     * @param pin:
     * @param datetime:
     * @return void
     * <AUTHOR>
     * @throws //
     *             @date 2020-11-26 15:00
     * @since 1.0.0
     */
    public void setPersonTransactionTime(String pin, String datetime) {
        stringRedisTemplate.opsForHash().put(AttConstant.ATT_PERSON_TRANSACTION_MAP, pin, datetime);
    }

    /**
     * 获取人员打卡记录时间
     * 
     * @param pin:
     * @return java.lang.String
     * <AUTHOR>
     * @throws //
     *             @date 2020-11-26 15:00
     * @since 1.0.0
     */
    public String getPersonTransactionTime(String pin) {
        Object object = stringRedisTemplate.opsForHash().get(AttConstant.ATT_PERSON_TRANSACTION_MAP, pin);
        if (null != object) {
            return object.toString();
        }
        return null;
    }

    /**
     * 获取adms设备心跳（判断在线离线）
     *
     * @param sn:
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/18 17:22
     * @since 1.0.0
     */
    public boolean isOnline(String sn) {
        return stringRedisTemplate.hasKey(ADMS_DEV_HEARTBEAT + sn);
    }

    /**
     * 获取adms设备信息
     *
     * @param sn:
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/18 17:22
     * @since 1.0.0
     */
    public String getAdmsDeviceInfo(String sn) {
        return stringRedisTemplate.opsForValue().get(ADMS_DEVICE_INFO + sn);
    }

    /**
     * 设置人员签到客户端信息
     * <AUTHOR>
     * @date 2020/1/14 10:38
     * @param clientId
     * @param personId
     * @return
     */
    public void setPersonSignClient(String clientId, String personId) {
        stringRedisTemplate.opsForValue().set(ATT_SIGN_KEY + clientId, personId);
        long endTime = DateUtil.getDayEndTime(new Date()).getTime();
        //设置当天结束过期
        stringRedisTemplate.expire(ATT_SIGN_KEY + clientId, endTime - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
    }

    /**
     * 判断人员是否能够签到
     * <AUTHOR>
     * @date 2020/1/14 10:38
     * @param clientId 签到设备唯一标识
     * @param personId 人员ID
     * @return
     */
    public boolean checkPersonSignClient(String clientId, String personId) {
        if (stringRedisTemplate.hasKey(ATT_SIGN_KEY + clientId)) {
            String oldPersonId = stringRedisTemplate.opsForValue().get(ATT_SIGN_KEY + clientId);
            if (!personId.equals(oldPersonId)) {
                return false;
            }
        }
        return true;
    }
}
