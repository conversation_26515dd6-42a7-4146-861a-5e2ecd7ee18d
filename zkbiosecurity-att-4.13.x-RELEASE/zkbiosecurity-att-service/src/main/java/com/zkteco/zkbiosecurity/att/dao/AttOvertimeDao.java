package com.zkteco.zkbiosecurity.att.dao;

import com.zkteco.zkbiosecurity.att.model.AttOvertime;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

/**
 * 加班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:30
 * @since 1.0.0
 */
public interface AttOvertimeDao extends BaseDao<AttOvertime, String> {

    /**
     * 根据人员集合查找时间范围内的所有有效申请
     *
     * @param personIdList
     * @param startTime
     * @param endTime
     * @return
     */
    @Query(
        value = "SELECT t FROM AttOvertime t WHERE t.personId IN (?1) AND (t.startDatetime < ?3 AND t.endDatetime > ?2) AND t.flowStatus IN ('0','2')")
    List<AttOvertime> getByPersonIdsAndStartEndTime(List<String> personIdList, Date startTime, Date endTime);

    /**
     * 根据人员pin查找时间范围内的所有有效申请
     *
     * @param pin
     * @param startTime
     * @param endTime
     * @return
     */
    @Query(
        value = "SELECT t FROM AttOvertime t WHERE t.personPin IN (?1) AND (t.startDatetime < ?3 AND t.endDatetime > ?2) AND t.flowStatus IN ('0','2')")
    List<AttOvertime> getByPinAndStartEndTime(String pin, Date startTime, Date endTime);

    /**
     * 根据人员编号 开始时间 结束时间 获取对应加班对象
     *
     * @param personPin
     *            人员编号
     * @param startDatetime
     *            开始时间
     * @param endDatetime
     *            结束时间
     * @return com.zkteco.zkbiosecurity.att.model.AttTrip
     * @date 2018/12/28 19:52
     */
    @Query(
        value = "SELECT t FROM AttOvertime t WHERE t.personPin = ?1 AND (t.startDatetime < ?3 AND t.endDatetime > ?2)")
    AttOvertime getByPersonPinAndStartEndTime(String personPin, Date startDatetime, Date endDatetime);

    AttOvertime findByBusinessKey(String businessKey);

    @Query("SELECT t FROM AttOvertime t WHERE t.flowStatus='2' AND t.endDatetime >= ?1 AND t.startDatetime <= ?2")
    List<AttOvertime> findOvertimeByDate(Date startDate, Date endDate);

    @Query("SELECT t FROM AttOvertime t WHERE t.flowStatus='2' AND t.endDatetime >= ?1 AND t.startDatetime <= ?2 AND t.personPin = ?3")
    List<AttOvertime> findOvertimeByDateAndPin(Date startDate, Date endDate, String personPin);

    /**
     * 查询人员集合时间范围内的申请记录
     *
     * @param startDate
     * @param endDate
     * @param pins
     * @return
     */
    @Query("SELECT t FROM AttOvertime t WHERE t.flowStatus='2' AND t.endDatetime >= ?1 AND t.startDatetime <= ?2 AND t.personPin IN (?3)")
    List<AttOvertime> findOvertimeByDateAndPins(Date startDate, Date endDate, List<String> pins);

    /**
     * 根据人员编号、开始时间、结束时间、流程状态查找外出列表(flowStatus 0,2表示审批中和结束)
     *
     * @param personPin
     * @param startTime
     * @param endTime
     * @return
     */
    @Query(
        value = "SELECT t FROM AttOvertime t WHERE t.personPin = ?1 AND t.endDatetime > ?2 AND t.startDatetime < ?3 AND (t.flowStatus='2' OR t.flowStatus='0')")
    List<AttOvertime> getListByPinAndDateAndValidStatus(String personPin, Date startTime, Date endTime);

    /**
     * 查找所有已通过审批，且加班申请结束时间大于或等于指定日期的加班申请
     *
     * @param flowStatus
     * @param startDate
     *            有效开始日期
     * @return
     * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
     */
    List<AttOvertime> findByFlowStatusAndEndDatetimeGreaterThanEqual(String flowStatus, Date startDate);

    @Modifying
    @Query("update AttOvertime t set t.flowStatus=?2 where t.id in (?1)")
    void updateFlowStatus(List<String> asList, String flowStatusComplete);
}