package com.zkteco.zkbiosecurity.att.dao;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import com.zkteco.zkbiosecurity.att.model.AttGroup;

/**
 * 分组
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:43
 * @since 1.0.0
 */
public interface AttGroupDao extends BaseDao<AttGroup, String> {

    /**
     * 判断是否已存在分组名称
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/9/30 9:21
     * @param groupName
     * @return boolean
     */
    boolean existsByGroupName(String groupName);

    /**
     * 根据分组名称查询对应的分组对象
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/7/25 16:11
     * @param groupName
     * @return com.zkteco.zkbiosecurity.att.model.AttGroup
     */
    AttGroup findByGroupName(String groupName);

}