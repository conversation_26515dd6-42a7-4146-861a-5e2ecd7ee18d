package com.zkteco.zkbiosecurity.att.dao;

import java.util.Collection;
import java.util.List;

import com.zkteco.zkbiosecurity.att.model.AttAutoExport;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 报表推送
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 16:38
 * @since 1.0.0
 */
public interface AttAutoExportDao extends BaseDao<AttAutoExport, String> {

    List<AttAutoExport> findByJobStatus(String jobStatus);

    List<AttAutoExport> findByIdIn(Collection<String> ids);

}