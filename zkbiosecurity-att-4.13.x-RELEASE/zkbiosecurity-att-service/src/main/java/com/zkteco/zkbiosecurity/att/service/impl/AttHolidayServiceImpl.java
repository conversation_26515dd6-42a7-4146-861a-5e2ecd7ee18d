package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;

import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.cache.AttCalculationCacheManager;
import com.zkteco.zkbiosecurity.att.dao.AttHolidayDao;
import com.zkteco.zkbiosecurity.att.model.AttHoliday;
import com.zkteco.zkbiosecurity.att.service.AttHolidayService;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttHolidayItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.*;

/**
 * 节假日
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 9:50
 * @since 1.0.0
 */
@Service
public class AttHolidayServiceImpl implements AttHolidayService {

    @Autowired
    private AttHolidayDao attHolidayDao;
    @Autowired
    private AttCalculationCacheManager attCalculationCacheManager;
    @Autowired
    private AttParamService attParamService;

    @Override
    @Transactional
    public AttHolidayItem saveItem(AttHolidayItem item) {

        // 时间段重叠校验
        boolean isExists = attHolidayDao.existsByIdNotAndEndDatetimeGreaterThanEqualAndStartDatetimeLessThanEqual(StringUtils.isNotBlank(item.getId())?item.getId(): "-1",
                item.getStartDatetime(), DateUtil.addDay(item.getStartDatetime(), item.getDayNumber() - 1));
        if (isExists) {
            // 该时间内已有节假日
            throw  new ZKBusinessException("att_holiday_validDate_msg");
        }

        item.setEndDatetime(DateUtil.addDay(item.getStartDatetime(), item.getDayNumber() - 1));
        AttHoliday attHoliday;
        if (StringUtils.isNotBlank(item.getId())) {
            attHoliday = attHolidayDao.getOne(item.getId());

            // 【实时计算】删除缓存中的节假日
            delHolidayCache(attHoliday);

        } else {
            attHoliday = new AttHoliday();
        }

        ModelUtil.copyPropertiesIgnoreNull(item, attHoliday);
        attHolidayDao.save(attHoliday);
        item.setId(attHoliday.getId());

        // 【实时计算】新增节假日到缓存
        setHolidayCache(attHoliday);

        return item;
    }

    @Override
    public List<AttHolidayItem> getByCondition(AttHolidayItem condition) {
        return (List<AttHolidayItem>)attHolidayDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return attHolidayDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            List<AttHoliday> attHolidayList = attHolidayDao.findByIdList(CollectionUtil.strToList(ids));
            for (AttHoliday attHoliday : attHolidayList) {
                attHolidayDao.delete(attHoliday);

                // 【实时计算】删除缓存中的节假日
                delHolidayCache(attHoliday);
            }
        }
        return false;
    }

    @Override
    public AttHolidayItem getItemById(String id) {
        List<AttHolidayItem> items = getByCondition(new AttHolidayItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public boolean existsByHolidayName(String holidayName) {
        return attHolidayDao.existsByHolidayName(holidayName);
    }

    @Override
    public Pager loadPagerByFilter(String sessionId, AttHolidayItem condition, int pageNo, int pageSize) {
        return getItemsByPage(condition, pageNo, pageSize);
    }

    @Override
    @Transactional
    public void handlerTransfer(List<AttHolidayItem> holidayItems) {
        // 节假日的数据不会太多，不需要批量处理
        List<AttHoliday> attHolidayList = new ArrayList<>();
        for (AttHolidayItem attHolidayItem : holidayItems) {
            // 根据假期名称找出对象 added by jinxian.huang 2019-05-24
            String holidayName = attHolidayItem.getHolidayName();
            AttHoliday attHoliday = attHolidayDao.findByHolidayName(holidayName);
            if (Objects.isNull(attHoliday)) {
                attHoliday = new AttHoliday();
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(attHolidayItem, attHoliday, "id");
            attHolidayList.add(attHoliday);
        }
        attHolidayDao.saveAll(attHolidayList);
    }

    @Deprecated
    @Override
    public Object getItemData(Class<AttHolidayItem> attHolidayItemClass, AttHolidayItem attHolidayItem, int beginIndex,
        int endIndex) {
        return attHolidayDao.getItemsDataBySql(attHolidayItemClass, SQLUtil.getSqlByItem(attHolidayItem), beginIndex,
            endIndex, true);
    }

    @Override
    public Set<String> getHolidayDateSet(Date startDatetime, Date endDateTime) {
        Set<String> holidaySet = new HashSet<>();
        List<AttHoliday> attHolidayList = attHolidayDao.findHolidayByStartTimeAndEndTime(startDatetime, endDateTime);
        for (AttHoliday attHoliday : attHolidayList) {
            AttDateUtils.forEachDay(attHoliday.getStartDatetime(), attHoliday.getEndDatetime(), date -> {
                holidaySet.add(DateUtil.dateToString(date, DateUtil.DateStyle.YYYY_MM_DD));
            });
        }
        return holidaySet;
    }

    /**
     * 【实时计算】删除缓存节假日
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/1 14:02
     * @param attHoliday
     * @return void
     */
    private void delHolidayCache(AttHoliday attHoliday) {
        if (attParamService.realTimeEnable()) {
            AttDateUtils.forEachDay(attHoliday.getStartDatetime(), attHoliday.getEndDatetime(), date -> {
                if (attCalculationCacheManager.judgeCacheDate(date)) {
                    attCalculationCacheManager.delHoliday(AttDateUtils.dateToStrAsShort(date));
                }
            });
        }
    }

    /**
     * 【实时计算】新增节假日到缓存
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/1 14:03
     * @param attHoliday
     * @return void
     */
    private void setHolidayCache(AttHoliday attHoliday) {
        if (attParamService.realTimeEnable()) {
            AttDateUtils.forEachDay(attHoliday.getStartDatetime(), attHoliday.getEndDatetime(), date -> {
                // 保存在时间范围内的数据
                if (attCalculationCacheManager.judgeCacheDate(date)) {
                    attCalculationCacheManager.setHoliday(AttDateUtils.dateToStrAsShort(date));
                }
            });
        }
    }
}