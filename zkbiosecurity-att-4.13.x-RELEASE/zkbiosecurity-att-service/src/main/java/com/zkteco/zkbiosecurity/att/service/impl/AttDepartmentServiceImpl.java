package com.zkteco.zkbiosecurity.att.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.service.AttCycleSchService;
import com.zkteco.zkbiosecurity.att.service.AttTempSchService;
import com.zkteco.zkbiosecurity.att.vo.AttCycleSchItem;
import com.zkteco.zkbiosecurity.auth.provider.AuthDepartmentProvider;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;

/**
 * 判断部门是否在考勤模块使用
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/3/12 9:48
 * @since 1.0.0
 *
 * @deprecated 已弃用 使用 {@link AttDepartmentExtServiceImpl} 代替
 *
 */
@Deprecated
@Service("attDepartmentService")
public class AttDepartmentServiceImpl implements AuthDepartmentProvider {

    @Autowired
    private AttCycleSchService attCycleSchService;
    @Autowired
    private AttTempSchService attTempSchService;

    @Override
    public void editAuthDepartment(AuthDepartmentItem authDepartmentItem) {
        // 无需处理
    }

    @Override
    @Transactional
    public void deleteAuthDepartment(String deptId) {
        // 判断部门是否存在排班情况
        AttCycleSchItem attCycleSchItem = new AttCycleSchItem();
        attCycleSchItem.setDeptId(deptId);
        if (!CollectionUtil.isEmpty(attCycleSchService.getByCondition(attCycleSchItem))) {
            throw ZKBusinessException.warnException("att_deptSch_existsDept");
        }
        // 判断部门是否存在临时排班情况
        if (attTempSchService.countByDeptId(deptId) > 0) {
            throw ZKBusinessException.warnException("att_tempSch_existsDept");
        }
    }
}
