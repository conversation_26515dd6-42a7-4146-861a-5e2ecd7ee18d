package com.zkteco.zkbiosecurity.att.dao;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.att.model.AttTrip;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 出差
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 17:14
 * @since 1.0.0
 */
@Deprecated
public interface AttTripDao extends BaseDao<AttTrip, String> {

    AttTrip findByBusinessKey(String businessKey);

    /**
     * 根据人员编号、开始时间、结束时间、流程状态查找出差列表(flowStatus 0,2表示审批中和结束)
     * 
     * @param personPin
     * @param startTime
     * @param endTime
     * @return
     */
    @Query(
        value = "SELECT t FROM AttTrip t WHERE t.personPin = ?1 AND t.endDatetime > ?2 AND t.startDatetime < ?3 AND (t.flowStatus='2' OR t.flowStatus='0')")
    List<AttTrip> getListByPinAndDateAndValidStatus(String personPin, Date startTime, Date endTime);

    /**
     * 查找所有已通过审批，且出差结束时间大于或等于指定日期的出差申请
     * 
     * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
     * @param flowStatus
     * @param startDate
     *            有效开始日期
     * @return
     */
    List<AttTrip> findByFlowStatusAndEndDatetimeGreaterThanEqual(String flowStatus, Date startDate);
}