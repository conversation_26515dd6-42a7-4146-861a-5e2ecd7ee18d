package com.zkteco.zkbiosecurity.att.service.impl;

import com.zkteco.zkbiosecurity.att.service.AttDeviceService;
import com.zkteco.zkbiosecurity.dashboard.service.Dashboard4OtherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 面板统计设备数量
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 18:26 2021/9/13
 * @version v1.0
 */
@Service
public class AttDashboardExtServiceImpl implements Dashboard4OtherService {

    @Autowired
    private AttDeviceService attDeviceService;

    @Override
    public long getDeviceCount() {
        return attDeviceService.getAllDeviceCount();
    }
}

