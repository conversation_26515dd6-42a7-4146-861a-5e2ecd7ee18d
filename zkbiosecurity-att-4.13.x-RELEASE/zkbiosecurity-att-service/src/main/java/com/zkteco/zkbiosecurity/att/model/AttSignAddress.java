package com.zkteco.zkbiosecurity.att.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * 签到地址
 */
@Entity
@Table(name = "ATT_SIGN_ADDRESS")
@Setter
@Getter
@Accessors(chain = true)
public class AttSignAddress extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 地址
     */
    @Column(name = "ADDRESS")
    private String address;

    /**
     * 经度
     */
    @Column(name = "LONGITUDE")
    private String longitude;

    /**
     * 纬度
     */
    @Column(name = "LATITUDE")
    private String latitude;

    /**
     * 范围距离 (m:米)
     */
    @Column(name = "VALID_RANGE")
    private Integer validRange;

    /** 地图类型 */
    @Column(name = "MAP_TYPE", length = 20)
    private String mapType;

    /** 地图密钥 */
    @Column(name = "MAP_KEY")
    private String mapKey;


}
