package com.zkteco.zkbiosecurity.att.dao;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.att.model.AttOut;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 外出
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 17:16
 * @since 1.0.0
 */
@Deprecated
public interface AttOutDao extends BaseDao<AttOut, String> {

    AttOut findByBusinessKey(String businessKey);

    /**
     * 根据人员编号、开始时间、结束时间、流程状态查找外出列表(flowStatus 0,2表示审批中和结束)
     * 
     * @param personPin
     * @param startTime
     * @param endTime
     * @return
     */
    @Query(
        value = "SELECT t FROM AttOut t WHERE t.personPin = ?1 AND t.endDatetime > ?2 AND t.startDatetime < ?3 AND (t.flowStatus='2' OR t.flowStatus='0')")
    List<AttOut> getListByPinAndDateAndValidStatus(String personPin, Date startTime, Date endTime);

    /**
     * 查找所有已通过审批，且外出结束时间大于或等于指定日期的外出申请
     * 
     * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
     * @param flowStatus
     * @param startDate
     *            有效开始日期
     * @return
     */
    List<AttOut> findByFlowStatusAndEndDatetimeGreaterThanEqual(String flowStatus, Date startDate);
}