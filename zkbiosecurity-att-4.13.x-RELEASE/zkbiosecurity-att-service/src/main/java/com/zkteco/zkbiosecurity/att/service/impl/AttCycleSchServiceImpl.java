package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import com.zkteco.zkbiosecurity.att.utils.ProcessBeanUtil;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.service.PersLeavePersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersLeavePersonItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.att.bean.AttRuleParamBean;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttTimeSlotBO;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttCommonSchConstant;
import com.zkteco.zkbiosecurity.att.dao.AttCycleSchDao;
import com.zkteco.zkbiosecurity.att.dao.AttShiftDao;
import com.zkteco.zkbiosecurity.att.model.AttCycleSch;
import com.zkteco.zkbiosecurity.att.model.AttShift;
import com.zkteco.zkbiosecurity.att.service.AttCycleSchService;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.utils.AttShiftSchUtils;
import com.zkteco.zkbiosecurity.att.vo.AttCycleSchItem;
import com.zkteco.zkbiosecurity.att.vo.AttShiftItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

import javax.transaction.Transactional;

/**
 * 周期排班（分组/部门/人员）
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/5/6 10:52
 */
@Service
public class AttCycleSchServiceImpl implements AttCycleSchService {

    @Autowired
    private AttCycleSchDao attCycleSchDao;
    @Autowired
    private AttShiftDao attShiftDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private ProgressCache progressCache;

    @Override
    @Transactional
    public AttCycleSchItem saveItem(String addIds, AttCycleSchItem item) {

        // 编辑的时候，找出当前编辑已存在的排班，并根据类型，获取赋值ids，当新增处理
        AttCycleSch existAttCycleSch = null;
        if (StringUtils.isNotBlank(item.getId())) {
            // existAttCycleSch = attCycleSchDao.findOne(item.getId());
            existAttCycleSch = attCycleSchDao.getOne(item.getId());
            if (StringUtils.isNotBlank(existAttCycleSch.getGroupId())) {
                addIds = existAttCycleSch.getGroupId();
            } else if (StringUtils.isNotBlank(existAttCycleSch.getPersonId())) {
                addIds = existAttCycleSch.getPersonId();
            } else if (StringUtils.isNotBlank(existAttCycleSch.getDeptId())) {
                addIds = existAttCycleSch.getDeptId();
            }
            attCycleSchDao.delete(existAttCycleSch);
        }

        // addIds为人员id或者部门id或者分组id，根据TempType判断
        if (StringUtils.isNotBlank(addIds)) {

            List<AttCycleSch> attCycleSchList = new ArrayList<>();
            String[] idArray = addIds.split(",");

            // 批量找出当前的新增排班与数据库存在交集的排班集合，组装成Map格式，key为对应类型的ID，防止在for语句中遍历查询数据库
            Map<String, List<AttCycleSch>> existAttCycleSchListMap = buildExistAttCycleSchListMap(
                Arrays.asList(idArray), item.getStartDate(), item.getEndDate(), item.getCycleType());

            if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(item.getCycleType())) {
                // 人员排班
                // 找出当前新增的人员信息集合，组装成Map格式，防止在for语句中遍历查询数据库
                List<PersPersonItem> persPersonItemList = persPersonService.getSimpleItemsByIds(Arrays.asList(idArray));
                Map<String, PersPersonItem> persPersonItemMap =
                    CollectionUtil.listToKeyMap(persPersonItemList, PersPersonItem::getId);

                for (String personId : idArray) {
                    AttCycleSch attCycleSch = buildAttCycleSch(item);
                    attCycleSch.setPersonId(personId);
                    if (persPersonItemMap.containsKey(personId)) {
                        attCycleSch.setPersonPin(persPersonItemMap.get(personId).getPin());
                        // attCycleSch.setDeptId(persPersonItemMap.get(personId).getDeptId());
                    }
                    if (existAttCycleSchListMap.containsKey(personId)) {
                        coverAttCycleSch(existAttCycleSchListMap.get(personId), attCycleSch.getStartDate(),
                            attCycleSch.getEndDate());
                    }
                    attCycleSchList.add(attCycleSch);

                }
            } else if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(item.getCycleType())) {
                // 部门排班
                for (String deptId : idArray) {
                    AttCycleSch attCycleSch = buildAttCycleSch(item);
                    attCycleSch.setDeptId(deptId);
                    if (existAttCycleSchListMap.containsKey(deptId)) {
                        coverAttCycleSch(existAttCycleSchListMap.get(deptId), attCycleSch.getStartDate(),
                            attCycleSch.getEndDate());
                    }
                    attCycleSchList.add(attCycleSch);
                }

            } else {
                // 分组排班
                for (String groupId : idArray) {
                    AttCycleSch attCycleSch = buildAttCycleSch(item);
                    attCycleSch.setGroupId(groupId);
                    if (existAttCycleSchListMap.containsKey(groupId)) {
                        coverAttCycleSch(existAttCycleSchListMap.get(groupId), attCycleSch.getStartDate(),
                            attCycleSch.getEndDate());
                    }
                    attCycleSchList.add(attCycleSch);
                }
            }
            attCycleSchDao.saveAll(attCycleSchList);

        }
        return item;
    }

    /**
     * 根据ID和日期找出存在交集的周期排班集合
     * 
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/15 15:55
     * @param idList
     * @param startDate
     * @param endDate
     * @param cycleType
     * @return java.util.Map<java.lang.String,java.util.List<com.zkteco.zkbiosecurity.att.model.AttCycleSch>>
     */
    private Map<String, List<AttCycleSch>> buildExistAttCycleSchListMap(List<String> idList, Date startDate,
        Date endDate, Short cycleType) {
        Map<String, List<AttCycleSch>> existAttCycleSchListMap = new HashMap<>();
        if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(cycleType)) {
            List<AttCycleSch> existAllAttCycleSchList =
                attCycleSchDao.findByPersonIdInAndStartDateAndEndDate(idList, startDate, endDate);
            if (existAllAttCycleSchList != null && !existAllAttCycleSchList.isEmpty()) {
                existAttCycleSchListMap =
                    existAllAttCycleSchList.stream().collect(Collectors.groupingBy(t -> t.getPersonId()));
            }
        } else if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(cycleType)) {
            List<AttCycleSch> existAllAttCycleSchList =
                attCycleSchDao.findByDeptIdInAndStartDateAndEndDate(idList, startDate, endDate);
            if (existAllAttCycleSchList != null && !existAllAttCycleSchList.isEmpty()) {
                existAttCycleSchListMap =
                    existAllAttCycleSchList.stream().collect(Collectors.groupingBy(t -> t.getDeptId()));
            }
        } else {
            List<AttCycleSch> existAllAttCycleSchList =
                attCycleSchDao.findByGroupIdInAndStartDateAndEndDate(idList, startDate, endDate);
            if (existAllAttCycleSchList != null && !existAllAttCycleSchList.isEmpty()) {
                existAttCycleSchListMap =
                    existAllAttCycleSchList.stream().collect(Collectors.groupingBy(t -> t.getGroupId()));
            }
        }
        return existAttCycleSchListMap;
    }

    /**
     * 组装周期排班信息，主要处理拷贝对象和赋值班次
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/8 14:01
     * @param item
     * @return com.zkteco.zkbiosecurity.att.model.AttCycleSch
     */
    private AttCycleSch buildAttCycleSch(AttCycleSchItem item) {
        AttCycleSch attCycleSch = new AttCycleSch();
        ModelUtil.copyPropertiesIgnoreId(item, attCycleSch);
        Set<AttShift> attShiftSet = attShiftDao.findByIdIn(Arrays.asList(item.getShiftIds().split(",")));
        attCycleSch.setAttShiftSet(attShiftSet);
        return attCycleSch;
    }

    @Override
    public AttCycleSchItem getItemById(String id) {
        AttCycleSchItem attCycleSchItem = new AttCycleSchItem();
        AttCycleSch attCycleSch = attCycleSchDao.findById(id).orElse(null);
        if (attCycleSch != null) {
            ModelUtil.copyProperties(attCycleSch, attCycleSchItem);
            attCycleSchItem.setShiftIds(CollectionUtil.getModelIds(attCycleSch.getAttShiftSet()));
        }
        return attCycleSchItem;
    }

    @Override
    public List<AttCycleSchItem> getByCondition(AttCycleSchItem condition) {
        List<AttCycleSchItem> list =
            (List<AttCycleSchItem>)attCycleSchDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
        return list;
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return attCycleSchDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            List<String> idList = StrUtil.strToList(ids);
            List<AttCycleSch> attCycleSchList = attCycleSchDao.findByIdList(idList);
            for (AttCycleSch existAttCycleSch : attCycleSchList) {
                attCycleSchDao.delete(existAttCycleSch);

            }
        }
        return false;
    }

    @Override
    @Transactional
    public void delCycleSch(String ids, Short type, Date startDate, Date endDate) {
        if (StringUtils.isNotBlank(ids) && type != null) {

            // 根据排班类型，找出数据库中存在日期有交集的数据
            String[] idArray = ids.split(",");

            // 找出当前删除日期内与数据库存在交集的排班
            Map<String, List<AttCycleSch>> existAttCycleSchListMap =
                buildExistAttCycleSchListMap(Arrays.asList(idArray), startDate, endDate, type);

            if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(type)) {
                // 人员排班
                for (String personId : idArray) {
                    if (existAttCycleSchListMap.containsKey(personId)) {
                        coverAttCycleSch(existAttCycleSchListMap.get(personId), startDate, endDate);
                    }
                }
            } else if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(type)) {
                // 部门排班
                for (String deptId : idArray) {
                    if (existAttCycleSchListMap.containsKey(deptId)) {
                        coverAttCycleSch(existAttCycleSchListMap.get(deptId), startDate, endDate);
                    }
                }

            } else {
                // 分组排班
                for (String groupId : idArray) {
                    if (existAttCycleSchListMap.containsKey(groupId)) {
                        coverAttCycleSch(existAttCycleSchListMap.get(groupId), startDate, endDate);
                    }

                }
            }
        }
    }

    /**
     * 对有交集的周期排班数据进行批量删除和批量拆分保存
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/9 16:10
     * @param existAttCycleSchList
     * @param startDate
     * @param endDate
     * @return void
     */
    private void coverAttCycleSch(List<AttCycleSch> existAttCycleSchList, Date startDate, Date endDate) {
        if (existAttCycleSchList != null && !existAttCycleSchList.isEmpty()) {
            // 遍历判断存在的周期排班内的日期是否有交集，有交集时，需要拆分保存
            for (AttCycleSch existAttCycleSch : existAttCycleSchList) {
                // 拆分周期排版，且返回拆分后的周期排班集合
                List<AttCycleSch> attCycleSchSaveList = splitAndBuildSaveList(startDate, endDate, existAttCycleSch);

                if (attCycleSchSaveList != null && !attCycleSchSaveList.isEmpty()) {
                    // 批量保存拆分的周期排班
                    attCycleSchDao.saveAll(attCycleSchSaveList);
                }
            }

            // 批量删除被拆分的排班或者被编辑的排班
            attCycleSchDao.deleteAll(existAttCycleSchList);
        }
    }

    /**
     * 拆分周期排班，且返回拆分后需要新增的周期排班集合
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/9 15:43
     * @param editStartDate
     * @param editEndDate
     * @param existAttCycleSchBak
     * @return void
     */
    private List<AttCycleSch> splitAndBuildSaveList(Date editStartDate, Date editEndDate,
        AttCycleSch existAttCycleSchBak) {

        List<AttCycleSch> attCycleSchSaveList = new ArrayList<>();

        // 格式化时间，前端数据可能带有分钟，判断会出错
        String editAttCycleSchStartDate = AttDateUtils.dateToStrAsShort(editStartDate);
        String editAttCycleSchEndDate = AttDateUtils.dateToStrAsShort(editEndDate);
        String existAttCycleSchStartDate = AttDateUtils.dateToStrAsShort(existAttCycleSchBak.getStartDate());
        String existAttCycleSchEndDate = AttDateUtils.dateToStrAsShort(existAttCycleSchBak.getEndDate());

        // 编辑的周期排班包含当前存在的开始时间有交集
        if (editAttCycleSchStartDate.compareTo(existAttCycleSchStartDate) > 0
            && editAttCycleSchEndDate.compareTo(existAttCycleSchEndDate) >= 0) {
            addAttCycleSchSaveList(attCycleSchSaveList, existAttCycleSchBak, null, DateUtil.addDay(editStartDate, -1));
        } else if (editAttCycleSchStartDate.compareTo(existAttCycleSchStartDate) > 0
            && editAttCycleSchEndDate.compareTo(existAttCycleSchEndDate) < 0) {
            // 编辑的周期排班被当前存在得包围，拆分两段
            addAttCycleSchSaveList(attCycleSchSaveList, existAttCycleSchBak, null, DateUtil.addDay(editStartDate, -1));
            addAttCycleSchSaveList(attCycleSchSaveList, existAttCycleSchBak, DateUtil.addDay(editEndDate, 1), null);
        } else if (editAttCycleSchStartDate.compareTo(existAttCycleSchStartDate) <= 0
            && editAttCycleSchEndDate.compareTo(existAttCycleSchEndDate) < 0) {
            // 编辑的周期排班包含当前存在的结束时间有交集
            addAttCycleSchSaveList(attCycleSchSaveList, existAttCycleSchBak, DateUtil.addDay(editEndDate, 1), null);
        }

        return attCycleSchSaveList;
    }

    /**
     * 批量组装需要拆分保存的临时周期排班信息
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/8 18:32
     * @param attCycleSchSaveList
     * @param existAttCycleSchBak
     * @param startDate 有值更新
     * @param endDate 有值更新
     * @return void
     */
    private void addAttCycleSchSaveList(List<AttCycleSch> attCycleSchSaveList, AttCycleSch existAttCycleSchBak,
        Date startDate, Date endDate) {
        AttCycleSch attCycleSch = new AttCycleSch();
        ModelUtil.copyPropertiesIgnoreId(existAttCycleSchBak, attCycleSch);
        if (existAttCycleSchBak.getAttShiftSet().size() > 0) {
            attCycleSch.setAttShiftSet(existAttCycleSchBak.getAttShiftSet());
        }
        if (startDate != null) {
            attCycleSch.setStartDate(startDate);
        }
        if (endDate != null) {
            attCycleSch.setEndDate(endDate);
        }
        attCycleSchSaveList.add(attCycleSch);
    }

    @Override
    public List<AttCycleSchItem> getCycleSchList(Short schType, Date startDate, Date endDate) {
        List<AttCycleSch> attCycleSchList = null;
        if ((short)0 == schType) {
            attCycleSchList = attCycleSchDao.findByCycleTypeAndStartDateAndEndDate(AttCommonSchConstant.SCH_TYPE_GROUP,
                startDate, endDate);
        } else if ((short)1 == schType) {
            attCycleSchList = attCycleSchDao.findByCycleTypeAndStartDateAndEndDate(AttCommonSchConstant.SCH_TYPE_DEPT,
                startDate, endDate);
        } else {
            attCycleSchList = attCycleSchDao.findByCycleTypeAndStartDateAndEndDate(AttCommonSchConstant.SCH_TYPE_PERSON,
                startDate, endDate);
        }

        List<AttCycleSchItem> attCycleSchItems = new ArrayList<>(attCycleSchList.size());
        for (AttCycleSch attCycleSch : attCycleSchList) {
            AttCycleSchItem cycleSchitem = ModelUtil.copyProperties(attCycleSch, new AttCycleSchItem());
            cycleSchitem.setShiftIds(CollectionUtil.getPropertys(attCycleSch.getAttShiftSet(), AttShift::getId));
            attCycleSchItems.add(cycleSchitem);
        }
        return attCycleSchItems;
    }

    @Override
    public List<AttCycleSchItem> getCycleSchListByDate(Date startDate, Date endDate) {
        List<AttCycleSch> attCycleSchList = attCycleSchDao.findByStartDateAndEndDate(startDate, endDate);
        List<AttCycleSchItem> attCycleSchItems = new ArrayList<>(attCycleSchList.size());
        for (AttCycleSch attCycleSch : attCycleSchList) {
            AttCycleSchItem cycleSchitem = ModelUtil.copyProperties(attCycleSch, new AttCycleSchItem());
            cycleSchitem.setShiftIds(CollectionUtil.getModelIds(attCycleSch.getAttShiftSet()));
            attCycleSchItems.add(cycleSchitem);
        }
        return attCycleSchItems;
    }

    @Override
    public List<AttCycleSchItem> getCycleSchListByCondition(AttCycleSchItem condition) {
        List<AttCycleSch> attCycleSchList = new ArrayList<>();
        if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(condition.getCycleType())
            && StringUtils.isNotBlank(condition.getInPersonPin())) {
            Collection<String> personPins = CollectionUtil.strToList(condition.getInPersonPin());
            List<List<String>> personPinsGroup = CollectionUtil.split(personPins, CollectionUtil.splitSize);
            for (List<String> pins : personPinsGroup) {
                List<AttCycleSch> attCycleSchs = attCycleSchDao.findPersonCycleByPersonPinInAndStartDateAndEndDate(pins,
                    condition.getStartTime(), condition.getEndTime());
                attCycleSchList.addAll(attCycleSchs);
            }
        } else if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(condition.getCycleType())
            && StringUtils.isNotBlank(condition.getInDeptId())) {
            attCycleSchList = attCycleSchDao.findDeptCycleByDeptIdInAndStartDateAndEndDate(
                CollectionUtil.strToList(condition.getInDeptId()), condition.getStartTime(), condition.getEndTime());
        } else if (AttCommonSchConstant.SCH_TYPE_GROUP.equals(condition.getCycleType())
            && StringUtils.isNotBlank(condition.getInGroupId())) {
            attCycleSchList = attCycleSchDao.findGroupCycleByGroupIdInAndStartDateAndEndDate(
                CollectionUtil.strToList(condition.getInGroupId()), condition.getStartTime(), condition.getEndTime());
        }
        List<AttCycleSchItem> attCycleSchItems = new ArrayList<>(attCycleSchList.size());
        for (AttCycleSch attCycleSch : attCycleSchList) {
            AttCycleSchItem cycleSchitem = ModelUtil.copyProperties(attCycleSch, new AttCycleSchItem());
            cycleSchitem.setShiftIds(CollectionUtil.getModelIds(attCycleSch.getAttShiftSet()));
            attCycleSchItems.add(cycleSchitem);
        }
        return attCycleSchItems;
    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, AttCycleSchItem condition, int pageNo, int pageSize) {
        // 组装部门条件
        buildCondition(sessionId, condition);
        Pager pager =
            attCycleSchDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        // 组装人员名称，部门名称，班次
        buildDeptPersGroup(pager.getData());
        return pager;
    }

    @Override
    public List<AttCycleSchItem> getItemData(String sessionId, AttCycleSchItem attCycleSchItem, int beginIndex,
        int endIndex) {
        // 组装部门条件
        buildCondition(sessionId, attCycleSchItem);
        List<AttCycleSchItem> attCycleSchItemList = attCycleSchDao.getItemsDataBySql(AttCycleSchItem.class,
            SQLUtil.getSqlByItem(attCycleSchItem), beginIndex, endIndex, true);
        // 组装人员名称，部门名称，班次
        buildDeptPersGroup(attCycleSchItemList);
        return attCycleSchItemList;
    }

    /**
     * 组装部门条件
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/27 11:39
     * @param sessionId
     * @param attCycleSchItem
     * @return void
     */
    private void buildCondition(String sessionId, AttCycleSchItem attCycleSchItem) {

        /*前端条件设置，如果tab显示的分组排班，那么用pin号、部门搜索无效*/
        if (AttCommonSchConstant.SCH_TYPE_GROUP.equals(attCycleSchItem.getCycleType())) {
            attCycleSchItem.setPersonPin(null);
            attCycleSchItem.setDeptId(null);
            attCycleSchItem.setDeptName(null);
        }
        if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(attCycleSchItem.getCycleType())) {
            attCycleSchItem.setPersonPin(null);
            attCycleSchItem.setGroupName(null);
        }
        if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(attCycleSchItem.getCycleType())) {
            attCycleSchItem.setGroupName(null);
        }

        if (StringUtils.isBlank(attCycleSchItem.getDeptId())) {
            attCycleSchItem.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, null, null,
                attCycleSchItem.getDeptName()));
        } else {
            // 包含子部门
            if ("true".equals(attCycleSchItem.getIsIncludeLower())) {
                attCycleSchItem.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId,
                    attCycleSchItem.getDeptId(), null, attCycleSchItem.getDeptName()));
                attCycleSchItem.setDeptId(null);
            } else {
                attCycleSchItem.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, null, null,
                    attCycleSchItem.getDeptName()));
            }
        }
    }

    /**
     * 组装人员名称，部门名称，班次
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/27 14:04
     * @param items
     * @return void
     */
    private void buildDeptPersGroup(List<?> items) {
        List<AttCycleSchItem> list = (List<AttCycleSchItem>)items;

        // 根据pin获取离职人员信息
        Collection<String> personPins = CollectionUtil.getPropertyList(list, AttCycleSchItem::getPersonPin, "-1");
        List<PersLeavePersonItem> persLeavePersonItemList = persLeavePersonService.getItemByPins(personPins);
        Map<String, PersLeavePersonItem> persLeavePersonItemMap =
            CollectionUtil.listToKeyMap(persLeavePersonItemList, PersLeavePersonItem::getPin);

        Collection<String> attCycleSchIds = CollectionUtil.getItemIdsList(list);
        List<AttCycleSch> attCycleSchList = attCycleSchDao.findByIdList(attCycleSchIds);

        Map<String, String> shiftNameMap = new HashMap<String, String>();
        attCycleSchList.forEach(attCycleSch -> {
            shiftNameMap.put(attCycleSch.getId(),
                CollectionUtil.getPropertys(attCycleSch.getAttShiftSet(), AttShift::getShiftName));
        });

        for (AttCycleSchItem item : list) {

            if (persLeavePersonItemMap.containsKey(item.getPersonPin())) {
                PersLeavePersonItem persLeavePersonItem = persLeavePersonItemMap.get(item.getPersonPin());
                item.setPersonName(persLeavePersonItem.getName());
                item.setPersonLastName(persLeavePersonItem.getLastName());
            }

            if (shiftNameMap.containsKey(item.getId())) {
                item.setShiftName(shiftNameMap.get(item.getId()));
            }
        }
    }

    @Override
    @Transactional
    public Map<String, List<AttPersonSchBO>> getCycleSch(Short cycleType, String ids, Date startDate, Date endDate,
        Map<String, AttShiftItem> attShiftItemMap, Map<String, AttTimeSlotItem> attTimeSlotItemMap,
        AttRuleParamBean attRuleParamBean) {

        // 查询部门周期排班
        AttCycleSchItem cycleSchCondition = new AttCycleSchItem();
        cycleSchCondition.setCycleType(cycleType);
        if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(cycleType)) {
            cycleSchCondition.setInDeptId(ids);
        } else if (AttCommonSchConstant.SCH_TYPE_GROUP.equals(cycleType)) {
            cycleSchCondition.setInGroupId(ids);
        } else if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(cycleType)) {
            cycleSchCondition.setInPersonPin(ids);
        }
        cycleSchCondition.setStartTime(startDate);
        cycleSchCondition.setEndTime(endDate);
        List<AttCycleSchItem> attCycleSchItems = getCycleSchListByCondition(cycleSchCondition);

        Map<String, List<AttPersonSchBO>> cycleSchMap = new HashMap<>();
        // 循环周期排班 填充 <ID_日期,班次(时间段)>;
        for (AttCycleSchItem attCycleSchItem : attCycleSchItems) {
            Date schStartDate = attCycleSchItem.getStartDate();
            Date schEndDate = attCycleSchItem.getEndDate();
            // 开始日期 = max(排班开始日期，计算开始日期)
            Date start = startDate.after(schStartDate) ? startDate : schStartDate;
            // 结束日期 = min(计算结束日期，排班结束日期)
            Date end = (null == schEndDate || endDate.before(schEndDate)) ? endDate : schEndDate;
            // 循环天 拆分排班为天填充 <ID=日期,班次(时间段)>;
            AttDateUtils.forEachDay(start, end, date -> {

                String dateStr = AttDateUtils.dateToStrAsShort(date);
                List<AttPersonSchBO> attPersonSchBOList = new ArrayList<>();
                String shiftIds = attCycleSchItem.getShiftIds();

                String[] shiftIdArr = shiftIds.split(",");
                // 多个班次则是智能找班
                for (String shiftId : shiftIdArr) {
                    AttShiftItem attShiftItem = attShiftItemMap.get(shiftId);
                    AttPersonSchBO attPersonSchBO = new AttPersonSchBO();
                    attPersonSchBO.setAttendStatus(AttCalculationConstant.AttAttendStatus.ACTUAL);
                    attPersonSchBO.setAttShiftId(shiftId);
                    attPersonSchBO.setAttShiftName(attShiftItem.getShiftName());
                    attPersonSchBO.setAttendanceMode(attShiftItem.getAttendanceMode());
                    attPersonSchBO.setOvertimeMode(attShiftItem.getOvertimeMode());
                    attPersonSchBO.setOvertimeRemark(attShiftItem.getOvertimeSign());
                    attPersonSchBO.setWorkType(attShiftItem.getWorkType());
                    attPersonSchBO.setShiftType(attShiftItem.getShiftType());
                    // 设置班次时间段
                    List<String> timeSlotIdList =
                        AttShiftSchUtils.getTimeSlotByShiftAndDate(attShiftItem, date, attCycleSchItem.getStartDate());
                    List<AttTimeSlotBO> attTimeSlotBOList = new ArrayList<>();
                    if (CollectionUtil.isEmpty(timeSlotIdList)) {
                        attPersonSchBO.setAttendStatus(AttCalculationConstant.AttAttendStatus.SCHEDULING_AND_REST);
                    }
                    for (String timeSlotId : timeSlotIdList) {
                        AttTimeSlotItem attTimeSlotItem = attTimeSlotItemMap.get(timeSlotId);
                        AttTimeSlotBO attTimeSlotBO = new AttTimeSlotBO();
                        attTimeSlotBO.setAttTimeSlotId(timeSlotId);
                        // 设置时段是否垮天
                        AttCommonUtils.setSetFirstAndSecondDay(attTimeSlotBO, attTimeSlotItem, dateStr,
                            attRuleParamBean.getCrossDay());
                        attTimeSlotBOList.add(attTimeSlotBO);
                    }
                    AttShiftSchUtils.sortTimeSlot(attTimeSlotBOList, attTimeSlotItemMap);
                    attPersonSchBO.setAttTimeSlotArray(attTimeSlotBOList);
                    attPersonSchBOList.add(attPersonSchBO);
                }

                // 填充<ID=日期, 班次 (时间段)>
                String key = "";
                if (AttCommonSchConstant.SCH_TYPE_DEPT.equals(cycleType)) {
                    key = attCycleSchItem.getDeptId() + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                } else if (AttCommonSchConstant.SCH_TYPE_GROUP.equals(cycleType)) {
                    key = attCycleSchItem.getGroupId() + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                } else if (AttCommonSchConstant.SCH_TYPE_PERSON.equals(cycleType)) {
                    key = attCycleSchItem.getPersonPin() + AttCalculationConstant.KEY_CONNECTOR + dateStr;
                }
                cycleSchMap.put(key, attPersonSchBOList);

            });
        }

        return cycleSchMap;
    }

    @Override
    public ZKResultMsg importItem(List<AttCycleSchItem> attCycleSchItemList) {

        if (attCycleSchItemList.size() > 30000) {
            throw ZKBusinessException.warnException("pers_import_overData", attCycleSchItemList.size());
        }

        int importAllSize = attCycleSchItemList.size();
        int importSize = 0;

        int beginProgress = 20;
        // 时间段信息集合
        List<AttShift> attShiftList = attShiftDao.findAll();
        Map<String, AttShift> attShiftMap = CollectionUtil.listToKeyMap(attShiftList, AttShift::getShiftName);

        List<List<AttCycleSchItem>> attCycleSchItemGroupList =
            CollectionUtil.split(attCycleSchItemList, CollectionUtil.splitSize);

        for (List<AttCycleSchItem> attCycleSchItemGroup : attCycleSchItemGroupList) {

            Collection<String> pins =
                CollectionUtil.getPropertyList(attCycleSchItemGroup, AttCycleSchItem::getPersonPin, "-1");
            // 人员信息集合
            List<PersPersonItem> persPersonList = persPersonService.getItemsByPins(pins);
            Map<String, PersPersonItem> persPersonMap =
                CollectionUtil.listToKeyMap(persPersonList, PersPersonItem::getPin);

            // 做数据校验检查并移除异常数据以及收集关键数据，方便in查询
            Iterator<AttCycleSchItem> itemIterator = attCycleSchItemGroup.iterator();
            while (itemIterator.hasNext()) {
                AttCycleSchItem item = itemIterator.next();

                // 人员编号非空校验
                if (StringUtils.isBlank(item.getPersonPin())) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                        .i18nCode("pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("pers_import_pinNotEmpty"))));
                    itemIterator.remove();
                    continue;
                }

                // 人员不存在
                if (persPersonMap.isEmpty() || !persPersonMap.containsKey(item.getPersonPin())) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                        .i18nCode("pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("pers_api_personNotExist"))));
                    itemIterator.remove();
                    continue;
                }

                // 开始时间不能为空
                if (Objects.isNull(item.getStartDate())) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                        .i18nCode("pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("att_leave_startNotNull"))));
                    itemIterator.remove();
                    continue;
                }

                // 结束时间不能为空
                if (Objects.isNull(item.getEndDate())) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                        .i18nCode("pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("att_leave_endNotNull"))));
                    itemIterator.remove();
                    continue;
                }

                // 开始时间不能大于等于结束时间
                if (item.getStartDate().compareTo(item.getEndDate()) > 0) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                            .i18nCode("pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("common_dsTime_timeValid4"))));
                    itemIterator.remove();
                    continue;
                }

                // 排班类型名称
                if (Objects.isNull(item.getScheduleType())) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                        "pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("att_personSch_scheduleTypeNotNull"))));
                    itemIterator.remove();
                    continue;
                }

                String shiftName = item.getShiftName();
                // 班次不能为空
                if (StringUtils.isBlank(shiftName)) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                        "pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("att_personSch_shiftNotNull"))));
                    itemIterator.remove();
                    continue;
                }

                short scheduleType = item.getScheduleType();
                String[] shiftNameArray = shiftName.split(",");
                if (shiftNameArray.length > 1 && AttCommonSchConstant.ScheduleType.NORMAL_SCHEDULE == scheduleType) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                        "pers_import_fail", item.getRowNum(), I18nUtil.i18nCode("att_personSch_onlyAllowOneShift"))));
                    itemIterator.remove();
                    continue;
                }
                String shiftIds = "";
                for (String name : shiftNameArray) {
                    // 工作类型不存在
                    AttShift attShift = attShiftMap.get(name);
                    if (attShift == null) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                                I18nUtil.i18nCode("att_personSch_shiftNotExist"))));
                        itemIterator.remove();
                        continue;
                    } else {
                        if (StringUtils.isNoneBlank(shiftIds)) {
                            shiftIds += ",";
                        }
                        shiftIds += attShift.getId();
                    }
                }
                item.setShiftIds(shiftIds);

            }

            for (AttCycleSchItem item : attCycleSchItemGroup) {
                item.setCycleType(AttCommonSchConstant.SCH_TYPE_PERSON);
                PersPersonItem personItem = persPersonMap.get(item.getPersonPin());
                saveItem(personItem.getId(), item);
            }
            importSize += attCycleSchItemGroup.size();
        }

        // 文件检查失败数量
        int faildCount = importAllSize - importSize;
        // 成功数据
        int saveCount = importAllSize - faildCount;
        progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
            I18nUtil.i18nCode("pers_import_result", saveCount, faildCount)));
        return new ZKResultMsg();
    }
}
