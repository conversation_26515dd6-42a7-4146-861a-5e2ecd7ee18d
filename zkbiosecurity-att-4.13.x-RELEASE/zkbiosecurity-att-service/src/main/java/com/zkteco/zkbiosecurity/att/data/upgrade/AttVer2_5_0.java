package com.zkteco.zkbiosecurity.att.data.upgrade;

import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 升级到2.5.0
 *
 */
@Slf4j
@Component
public class AttVer2_5_0 implements UpgradeVersionManager {

    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public String getModule() {
        return BaseConstants.ATT;
    }

    @Override
    public String getVersion() {
        return "v2.5.0";
    }

    @Override
    public boolean executeUpgrade() {

        // 添加导出考勤照片按钮权限
        AuthPermissionItem buttonMenuItem = authPermissionService.getItemByCode("AttTransactionExportAttPhoto");
        if (Objects.isNull(buttonMenuItem)) {
            AuthPermissionItem childMenuItem = authPermissionService.getItemByCode("AttTransaction");
            if (Objects.nonNull(childMenuItem)) {
                buttonMenuItem =
                    new AuthPermissionItem("AttTransactionExportAttPhoto", "att_transaction_exportAttPhoto",
                        "att:transaction:exportAttPhoto", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 9);
                buttonMenuItem.setParentId(childMenuItem.getId());
                authPermissionService.saveItem(buttonMenuItem);
            }
        }

        // 添加考勤点定时获取记录频率参数
        if (StringUtils.isBlank(baseSysParamService.getValByName("att.point.pullTransaction"))) {
            baseSysParamService
                .initData(new BaseSysParamItem("att.point.pullTransaction", "0 0/10 * * * ?", "考勤点定时获取记录频率"));
        }

        return true;
    }

}
