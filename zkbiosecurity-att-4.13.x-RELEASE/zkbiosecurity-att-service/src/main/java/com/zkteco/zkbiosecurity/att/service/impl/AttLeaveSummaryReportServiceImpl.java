package com.zkteco.zkbiosecurity.att.service.impl;

import java.math.BigDecimal;
import java.util.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.bean.AttConditionBean;
import com.zkteco.zkbiosecurity.att.bean.AttLeaveTypeSummary;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttLeaveDao;
import com.zkteco.zkbiosecurity.att.model.AttLeave;
import com.zkteco.zkbiosecurity.att.service.AttLeaveSummaryReportService;
import com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.service.AttPersonService;
import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveSummaryReportItem;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.pers.service.PersLeavePersonService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersLeavePersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 请假汇总表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/8/18 11:42
 */
@Service
public class AttLeaveSummaryReportServiceImpl implements AttLeaveSummaryReportService {

    @Autowired
    private AttLeaveDao attLeaveDao;
    @Autowired(required = false)
    private PersPersonService persPersonService;
    @Autowired(required = false)
    PersLeavePersonService persLeavePersonService;
    @Autowired(required = false)
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;
    @Autowired
    private AttParamService attParamService;

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return null;
    }

    @Override
    public boolean deleteByIds(String ids) {
        return false;
    }

    @Override
    public List<AttLeaveSummaryReportItem> getItemData(String sessionId,
        AttLeaveSummaryReportItem attLeaveSummaryReportItem, int beginIndex, int endIndex) {

        // 过滤条件组装
        buildCondition(sessionId, attLeaveSummaryReportItem);

        // 根据部门ID + pin + 请假类型ID查出唯一记录（未按人员统计数量）
        List<AttLeaveSummaryReportItem> items = attLeaveDao.getItemsDataBySql(AttLeaveSummaryReportItem.class,
            SQLUtil.getSqlByItem(attLeaveSummaryReportItem), beginIndex, endIndex, true);

        // 数据组装
        buildLeaveType(items, attLeaveSummaryReportItem.getStartApplyDateTime(),
            attLeaveSummaryReportItem.getEndApplyDateTime(), true);

        return items;
    }

    @Override
    public ZKResultMsg getLeaveDetail(String personPin, String leaveTypeId, Date startTime, Date endTime) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        List<AttLeave> attLeaveList =
            attLeaveDao.getByPersonPinAndLeaveTypeIdAndDate(personPin, leaveTypeId, startTime, endTime);
        String personName = "";
        String lastName = "";
        PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
        if (persPersonItem != null) {
            personName = persPersonItem.getName();
            lastName = persPersonItem.getLastName();
        } else {
            PersLeavePersonItem persLeavePersonItem = persLeavePersonService.getItemByPin(personPin);
            if (persLeavePersonItem != null) {
                personName = persLeavePersonItem.getName();
                lastName = persLeavePersonItem.getLastName();
            }
        }
        AttLeaveTypeItem attLeaveType = attLeaveTypeService.getItemById(leaveTypeId);
        List<Map<String, String>> leaveDetailList = new ArrayList<>();
        Map<String, String> leaveDetailMap = null;
        for (AttLeave attLeave : attLeaveList) {
            leaveDetailMap = new HashMap<>();
            leaveDetailMap.put("personPin", personPin);
            leaveDetailMap.put("personName", personName);
            leaveDetailMap.put("lastName", lastName);
            leaveDetailMap.put("leaveType", attLeaveType.getLeaveTypeName());
            leaveDetailMap.put("startTime", AttDateUtils.dateToStrAsLong(attLeave.getStartDatetime()));
            leaveDetailMap.put("endTime", AttDateUtils.dateToStrAsLong(attLeave.getEndDatetime()));
            leaveDetailList.add(leaveDetailMap);
        }
        resultMsg.setData(leaveDetailList);
        return resultMsg;
    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, AttLeaveSummaryReportItem condition, int pageNo,
        int pageSize) {

        // 过滤条件组装
        buildCondition(sessionId, condition);

        Pager pager =
            attLeaveDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);

        // 数据组装
        buildLeaveType((List<AttLeaveSummaryReportItem>)pager.getData(), condition.getStartApplyDateTime(),
            condition.getEndApplyDateTime(), false);

        return pager;
    }

    private void buildLeaveType(List<AttLeaveSummaryReportItem> items, Date startTime, Date endTime, boolean export) {

        Collection<String> personPins =
            CollectionUtil.getPropertyList(items, AttLeaveSummaryReportItem::getPersonPin, "-1");

        // 组装请假集合<pin,<leaveTypeId,AttLeaveTypeSummary>>
        Map<String, Map<String, AttLeaveTypeSummary>> personLeaveSumMap =
            getPersonLeaveSumMap(personPins, startTime, endTime);

        // 假种
        List<AttLeaveTypeItem> attLeaveTypeList = attLeaveTypeService.getLeaveTypeItems();
        // 精确小数点位数
        int decimal = Integer.parseInt(attParamService.getDecimal());

        // 人员
        List<String> personPinList =
            (List<String>)CollectionUtil.getPropertyList(items, AttLeaveSummaryReportItem::getPersonPin, "-1");
        List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(personPinList);
        Map<String, PersPersonItem> persPersonItemMap =
            CollectionUtil.listToKeyMap(persPersonItemList, PersPersonItem::getPin);

        for (AttLeaveSummaryReportItem item : items) {
            item.setId(item.getPersonPin());

            // 查询人员名称
            PersPersonItem persPersonItem = persPersonItemMap.get(item.getPersonPin());
            if (Objects.nonNull(persPersonItem)) {
                item.setPersonName(persPersonItem.getName());
                item.setPersonLastName(persPersonItem.getLastName());
                item.setDeptCode(persPersonItem.getDeptCode());
                item.setDeptName(persPersonItem.getDeptName());
            }

            Map<String, AttLeaveTypeSummary> leaveTypeSumMap = personLeaveSumMap.get(item.getPersonPin());
            Map<String, Object> leaveTypeMap = new LinkedHashMap<>();
            for (AttLeaveTypeItem attLeaveType : attLeaveTypeList) {
                String leaveTypeName = attLeaveType.getLeaveTypeName();
                String leaveTypeNameUnit = attLeaveTypeService.getConvertUnit(attLeaveType);
                if (leaveTypeSumMap != null && leaveTypeSumMap.containsKey(attLeaveType.getId())) {
                    AttLeaveTypeSummary attLeaveTypeSummary = leaveTypeSumMap.get(attLeaveType.getId());
                    if (export) {
                        leaveTypeMap.put(leaveTypeName + I18nUtil.i18nCode("att_statistical_numberOfTimes"),
                            attLeaveTypeSummary.getCount());
                    } else {
                        leaveTypeMap.put(leaveTypeName + I18nUtil.i18nCode("att_statistical_numberOfTimes"),
                            item.getPersonPin() + "_" + attLeaveType.getId() + "_" + attLeaveTypeSummary.getCount());
                    }
                    if (AttConstant.ATT_CONVERT_UNIT_DAY.equals(attLeaveType.getConvertUnit())) {
                        BigDecimal days = new BigDecimal(attLeaveTypeSummary.getDays());
                        leaveTypeMap.put(leaveTypeNameUnit,
                            days.setScale(decimal, AttCommonUtils.getRoundingMode(attLeaveType.getConvertType())) + "");
                    } else {
                        leaveTypeMap.put(leaveTypeNameUnit,
                            AttCommonUtils.convertMinute(attLeaveTypeSummary.getLeaveLong(), 0, attLeaveType, decimal));
                    }
                } else {
                    leaveTypeMap.put(leaveTypeName + I18nUtil.i18nCode("att_statistical_numberOfTimes"), "0");
                    leaveTypeMap.put(leaveTypeNameUnit, BigDecimal.ZERO.setScale(decimal) + "");
                }
            }
            item.setLeaveTypeMap(leaveTypeMap);
        }
    }

    /**
     * 组装请假集合<pin,<leaveTypeId,AttLeaveTypeSummary>>
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/18 14:15
     * @param personPins
     * @param startTime
     * @param endTime
     * @return java.util.Map<java.lang.String,java.util.Map<java.lang.String,java.lang.String>>
     */
    private Map<String, Map<String, AttLeaveTypeSummary>> getPersonLeaveSumMap(Collection<String> personPins,
        Date startTime, Date endTime) {
        Map<String, Map<String, AttLeaveTypeSummary>> personLeaveSumMap = new HashMap();
        List<List<String>> personPinsGroup = CollectionUtil.split(personPins, CollectionUtil.splitSize);
        for (List<String> pins : personPinsGroup) {
            List<Object[]> objectArayList = attLeaveDao.getCountByPersonIdsAndDate(pins, startTime, endTime);
            for (Object[] objectAray : objectArayList) {
                String personPin = objectAray[0].toString();
                String leaveTypeId = objectAray[1].toString();
                Map<String, AttLeaveTypeSummary> leaveTypeSumMap;

                AttLeaveTypeSummary attLeaveTypeSummary = new AttLeaveTypeSummary();
                attLeaveTypeSummary.setCount(Integer.valueOf(objectAray[2].toString()));
                if (Objects.isNull(objectAray[3])) {
                    attLeaveTypeSummary.setLeaveLong(0);
                } else {
                    attLeaveTypeSummary.setLeaveLong(Integer.valueOf(objectAray[3].toString()));
                }
                if (Objects.isNull(objectAray[4])) {
                    attLeaveTypeSummary.setDays("0");
                } else {
                    attLeaveTypeSummary.setDays(objectAray[4].toString());
                }
                if (personLeaveSumMap.containsKey(personPin)) {
                    leaveTypeSumMap = personLeaveSumMap.get(personPin);
                    leaveTypeSumMap.put(leaveTypeId, attLeaveTypeSummary);
                } else {
                    leaveTypeSumMap = new HashMap<>();
                    leaveTypeSumMap.put(leaveTypeId, attLeaveTypeSummary);
                }
                personLeaveSumMap.put(personPin, leaveTypeSumMap);
            }
        }
        return personLeaveSumMap;
    }

    /**
     * 条件组装，根据用户权限
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/18 16:02
     * @param sessionId
     * @param condition
     */
    private void buildCondition(String sessionId, AttLeaveSummaryReportItem condition) {
        // 使用员工自助登录的用户,只有自己数据的权限。若登录类型为pers,则返回pin加入查询sql。 add by bob.liu 20190708
        String persperPin = attPersonService.getPinByLoginType(sessionId);
        if (persperPin != null) {
            condition.setEquals(true);
            condition.setPersonPin(persperPin);
        } else {

            // 组装部门权限和姓名模糊搜索条件
            AttConditionBean attConditionBean = new AttConditionBean(ZKConstant.TRUE, condition.getLikeName(),
                condition.getDeptId(), condition.getDeptCode(), condition.getDeptName());
            attPersonService.getConditionBySessionId(sessionId, attConditionBean);
            condition.setInPersonPin(attConditionBean.getInPersonPin());
            condition.setDeptId(attConditionBean.getDeptId());
            condition.setInDeptId(attConditionBean.getInDeptId());
        }
        condition.setFlowStatus(AttConstant.FLOW_STATUS_COMPLETE);
    }
}