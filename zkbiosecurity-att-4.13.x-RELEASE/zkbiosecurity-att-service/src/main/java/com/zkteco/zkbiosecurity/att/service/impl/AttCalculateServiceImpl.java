package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;
import java.util.concurrent.CompletableFuture;

import com.zkteco.zkbiosecurity.att.bean.AttCalculationParamsBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.bean.AttPersonAttendanceBean;
import com.zkteco.zkbiosecurity.att.bean.AttRuleParamBean;
import com.zkteco.zkbiosecurity.att.cache.AttCacheManager;
import com.zkteco.zkbiosecurity.att.cache.AttCalculationCacheManager;
import com.zkteco.zkbiosecurity.att.calc.bo.AttLeaveBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttOvertimeBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 考勤计算
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:14
 * @since 1.0.0
 */
@Slf4j
@Service
public class AttCalculateServiceImpl implements AttCalculateService {

    @Autowired
    private AttRecordService attRecordService;
    @Autowired
    private AttPersonSchDataService attPersonSchDataService;
    @Autowired
    private AttCacheManager attCacheManager;
    @Autowired
    private AttTransactionService attTransactionService;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttAttendanceCalculateService attAttendanceCalculateService;
    @Autowired
    private AttCycleSchService attCycleSchService;
    @Autowired
    private AttTempSchService attTempSchService;
    @Autowired
    private AttCalculationCacheManager attCalculationCacheManager;

    @Async
    @Transactional
    @Override
    public CompletableFuture calculateBatchPerson(AttCalculationParamsBean params) {

        try {

            AttRuleParamBean attRuleParamBean = params.getAttRuleParamBean();
            Set<String> holidaySet = params.getAttHolidaySet();
            Map<String, AttShiftItem> attShiftItemMap = params.getAttShiftItemMap();
            Map<String, AttTimeSlotItem> attTimeSlotItemMap = params.getAttTimeSlotItemMap();

            // 从人事缓存中获取人员信息
            List<AttPersonItem> attPersonItemList = attPersonService.getByPinsFromPersonCache(params.getPins());
            Map<String, AttPersonItem> attPersonItemMap =
                    CollectionUtil.listToKeyMap(attPersonItemList, AttPersonItem::getPersonPin);

            // 根据人+时间段查找时间范围内的原始考勤记录
            Date dateBefore = DateUtil.getDateBefore(params.getStartDate(), 2);
            Date dateAfter = DateUtil.getDateAfter(params.getEndDate(), 2);
            Map<String, List<String>> attTransactionMap =
                    attTransactionService.getTransactionMap(dateBefore, dateAfter, params.getPins());

            // 查询时间范围内的周期排班
            List<AttCycleSchItem> attCycleSchItemList = attCycleSchService.getCycleSchListByDate(params.getStartDate(), params.getEndDate());

            // 查询时间范围内的临时排班
            List<AttTempSchItem> attTempSchItemList = attTempSchService.getTempSchListByDate(params.getStartDate(), params.getEndDate());

            // 获取人员排班情况
            Map<String, List<AttPersonSchBO>> schDataMap =
                    attPersonSchDataService.buildPersonSchData2(attPersonItemList, params.getStartDate(), params.getEndDate(), attShiftItemMap,
                            attTimeSlotItemMap, attRuleParamBean, attCycleSchItemList, attTempSchItemList);
            attPersonSchDataService.buildPersonSchExData(schDataMap, params.getAttAdjustMap(), params.getAttClassMap(), holidaySet);

            // 循环人/天/班
            List<AttRecordItem> recordItemList = new ArrayList<>();
            for (Map.Entry<String, List<AttPersonSchBO>> entry : schDataMap.entrySet()) {
                String pinAndDate = entry.getKey();
                List<AttPersonSchBO> attPersonSchBOList = entry.getValue();

                String[] pinAndDateSplit = pinAndDate.split(AttCalculationConstant.KEY_CONNECTOR);
                String pin = pinAndDateSplit[0];
                String date = pinAndDateSplit[1];

                // 取单个人的原始打卡记录(已包含补签记录)、请假/出差/外出/加班信息 by ljf 2020/9/29
                Map<String, List<String>> perTransactionMap = new HashMap<>();
                List<AttLeaveBO> attLeaveBOList = new ArrayList<>();
                List<AttOvertimeBO> attOvertimeBOList = new ArrayList<>();
                // 取前后各一天共三天 给班次时段跨天或取卡范围跨天计算时判断
                String pinAndBeforeDay = pin + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.getBeforeDay(date);
                String pinAndNextDay = pin + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.getNextDay(date);
                for (String key : new String[] {pinAndBeforeDay, pinAndDate, pinAndNextDay}) {
                    List<String> perTranList = attTransactionMap.get(key);
                    if (null == perTranList) {
                        perTranList = new ArrayList<>();
                    }
                    perTransactionMap.put(key, perTranList);
                    if (params.getAttLeaveMap().containsKey(key)) {
                        attLeaveBOList.addAll(params.getAttLeaveMap().get(key));
                    }
                    if (params.getAttOvertimeMap().containsKey(key)) {
                        attOvertimeBOList.addAll(params.getAttOvertimeMap().get(key));
                    }
                }

                AttPersonItem attPersonItem = attPersonItemMap.get(pin);
                // 是否考勤（true:正常考勤/false:免打卡）,默认要考勤
                boolean isAttendance =
                        Objects.nonNull(attPersonItem.getIsAttendance()) ? attPersonItem.getIsAttendance() : true;

                AttPersonAttendanceBean attPersonAttendanceBean = new AttPersonAttendanceBean();
                attPersonAttendanceBean.setPinAndDate(pinAndDate);
                attPersonAttendanceBean.setIsAttendance(isAttendance);
                attPersonAttendanceBean.setAttRuleParamBean(attRuleParamBean);
                attPersonAttendanceBean.setAttTimeSlotItemMap(attTimeSlotItemMap);
                attPersonAttendanceBean.setPersonSchDataMap(attPersonSchBOList);
                attPersonAttendanceBean.setTransactionDataMap(perTransactionMap);
                attPersonAttendanceBean.setAttLeaveBOList(attLeaveBOList);
                attPersonAttendanceBean.setAttOvertimeBOList(attOvertimeBOList);

                // 考勤计算
                AttRecordItem attRecordItem = attAttendanceCalculateService.calculate(attPersonAttendanceBean);

                // 填充考勤记录的人员信息
                attRecordItem.setPersonPin(attPersonItem.getPersonPin());
                attRecordItem.setPersonName(attPersonItem.getPersonName());
                attRecordItem.setPersonLastName(attPersonItem.getPersonLastName());
                attRecordItem.setDeptId(attPersonItem.getDeptId());
                attRecordItem.setDeptCode(attPersonItem.getDeptCode());
                attRecordItem.setDeptName(attPersonItem.getDeptName());
                // 填充日期星期信息
                attRecordItem.setAttDate(AttDateUtils.stringToYmdDate(date));
                attRecordItem.setWeek(AttDateUtils.getWeekOfDate(attRecordItem.getAttDate()));
                recordItemList.add(attRecordItem);
            }

            // 删除旧记录，保存新记录
            attRecordService.delAndSaveRecord(params.getPins(), params.getStartDate(), params.getEndDate(), recordItemList);

        } catch (Exception e) {
            log.error("calculateBatchPerson Exception {}", e);
        }
        return CompletableFuture.completedFuture(null);
    }

    @Override
    @Transactional
    public List<CompletableFuture<Boolean>> realTimeCalculate(List<String> pinList, Date calculateBeginDate,
        Date calculateEndDate) {

        List<CompletableFuture<Boolean>> futureList = new ArrayList<>();

        // 从缓存中获取基础信息
        AttRuleParamBean attRuleParamBean = attCalculationCacheManager.getRuleParamBean();
        Set<String> holidaySet = attCalculationCacheManager.getHolidaySet();
        Map<String, AttTimeSlotItem> attTimeSlotItemMap = attCalculationCacheManager.getTimeSlotItemMap();
        Map<String, AttShiftItem> attShiftItemMap = attCalculationCacheManager.getShiftItemMap();

        // 先查数据库获取周期排班、临时排班
        // 查询时间范围内的周期排班
        List<AttCycleSchItem> attCycleSchItemList = attCycleSchService.getCycleSchListByDate(calculateBeginDate, calculateEndDate);

        // 查询时间范围内的临时排班
        List<AttTempSchItem> attTempSchItemList = attTempSchService.getTempSchListByDate(calculateBeginDate, calculateEndDate);

        int personTotal = pinList.size();
        for (int fromIndex = 0; fromIndex < personTotal; fromIndex = fromIndex + AttCalculationConstant.NUMBER) {

            int toIndex = Math.min(personTotal, fromIndex + AttCalculationConstant.NUMBER);
            List<String> pins = pinList.subList(fromIndex, toIndex);

            // 从缓存中获取异常信息(往前和往后多查一天)
            Map<String, List<AttLeaveBO>> attLeaveMap = attCalculationCacheManager.getLeaveMap(pins,
                DateUtil.getDateBefore(calculateBeginDate, 1), DateUtil.getDateAfter(calculateEndDate, 1));
            Map<String, List<AttOvertimeBO>> attOvertimeMap = attCalculationCacheManager.getOvertimeMap(pins,
                DateUtil.getDateBefore(calculateBeginDate, 1), DateUtil.getDateAfter(calculateEndDate, 1));
            Map<String, List<AttPersonSchBO>> attAdjustMap = attCalculationCacheManager.getAdjustMap(pins,
                DateUtil.getDateBefore(calculateBeginDate, 1), DateUtil.getDateAfter(calculateEndDate, 1));
            Map<String, List<AttPersonSchBO>> attClassMap = attCalculationCacheManager.getClassMap(pins,
                DateUtil.getDateBefore(calculateBeginDate, 1), DateUtil.getDateAfter(calculateEndDate, 1));

            // 从人事缓存中获取人员信息
            List<AttPersonItem> attPersonItemList = attPersonService.getByPinsFromPersonCache(pins);
            Map<String, AttPersonItem> attPersonItemMap =
                CollectionUtil.listToKeyMap(attPersonItemList, AttPersonItem::getPersonPin);

            // 组装排班详情
            Map<String, List<AttPersonSchBO>> schDataMap =
                attPersonSchDataService.buildPersonSchData2(attPersonItemList, calculateBeginDate, calculateEndDate,
                    attShiftItemMap, attTimeSlotItemMap, attRuleParamBean, attCycleSchItemList, attTempSchItemList);
            // 组装异常详情
            attPersonSchDataService.buildPersonSchExData(schDataMap, attAdjustMap, attClassMap, holidaySet);

            // 根据人+时间段查找对应的原始考勤记录(往前和往后多查一天)
            Map<String, List<String>> attTransactionMap = attTransactionService.getTransactionMap(
                DateUtil.getDateBefore(calculateBeginDate, 1), DateUtil.getDateAfter(calculateEndDate, 1), pins);

            // 循环人/天/班
            List<AttRecordItem> recordItemList = new ArrayList<>();
            for (Map.Entry<String, List<AttPersonSchBO>> entry : schDataMap.entrySet()) {
                String pinAndDate = entry.getKey();
                List<AttPersonSchBO> attPersonSchBOList = entry.getValue();

                String[] pinAndDateSplit = pinAndDate.split(AttCalculationConstant.KEY_CONNECTOR);
                String pin = pinAndDateSplit[0];
                String date = pinAndDateSplit[1];

                // 取单个人的原始打卡记录(已包含补签记录) by ljf 2020/9/29
                // 请假/出差/外出/加班信息
                Map<String, List<String>> perTransactionMap = new HashMap<>();
                List<AttLeaveBO> attLeaveBOList = new ArrayList<>();
                List<AttOvertimeBO> attOvertimeBOList = new ArrayList<>();
                // 取前后各一天共三天 给班次时段跨天或取卡范围跨天计算时判断
                String pinAndBeforeDay = pin + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.getBeforeDay(date);
                String pinAndNextDay = pin + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.getNextDay(date);

                for (String key : new String[] {pinAndBeforeDay, pinAndDate, pinAndNextDay}) {
                    List<String> perTranList = attTransactionMap.get(key);
                    if (null == perTranList) {
                        perTranList = new ArrayList<>();
                    }
                    perTransactionMap.put(key, perTranList);
                    if (attLeaveMap.containsKey(key)) {
                        attLeaveBOList.addAll(attLeaveMap.get(key));
                    }
                    if (attOvertimeMap.containsKey(key)) {
                        attOvertimeBOList.addAll(attOvertimeMap.get(key));
                    }
                }

                AttPersonItem attPersonItem = attPersonItemMap.get(pin);
                // 是否考勤（true:正常考勤/false:免打卡）,默认要考勤
                boolean isAttendance =
                    Objects.nonNull(attPersonItem.getIsAttendance()) ? attPersonItem.getIsAttendance() : true;

                AttPersonAttendanceBean attPersonAttendanceBean = new AttPersonAttendanceBean();
                attPersonAttendanceBean.setPinAndDate(pinAndDate);
                attPersonAttendanceBean.setIsAttendance(isAttendance);
                attPersonAttendanceBean.setAttRuleParamBean(attRuleParamBean);
                attPersonAttendanceBean.setAttTimeSlotItemMap(attTimeSlotItemMap);
                attPersonAttendanceBean.setPersonSchDataMap(attPersonSchBOList);
                attPersonAttendanceBean.setTransactionDataMap(perTransactionMap);
                attPersonAttendanceBean.setAttLeaveBOList(attLeaveBOList);
                attPersonAttendanceBean.setAttOvertimeBOList(attOvertimeBOList);

                // 考勤计算
                AttRecordItem attRecordItem = attAttendanceCalculateService.calculate(attPersonAttendanceBean);

                // 填充考勤记录的人员信息
                attRecordItem.setPersonPin(attPersonItem.getPersonPin());
                attRecordItem.setPersonName(attPersonItem.getPersonName());
                attRecordItem.setPersonLastName(attPersonItem.getPersonLastName());
                attRecordItem.setDeptId(attPersonItem.getDeptId());
                attRecordItem.setDeptCode(attPersonItem.getDeptCode());
                attRecordItem.setDeptName(attPersonItem.getDeptName());
                // 填充日期星期信息
                attRecordItem.setAttDate(AttDateUtils.stringToYmdDate(date));
                attRecordItem.setWeek(AttDateUtils.getWeekOfDate(attRecordItem.getAttDate()));
                recordItemList.add(attRecordItem);
            }

            // 删除旧记录，保存新记录
            attRecordService.delAndSaveRecord(pins, calculateBeginDate, calculateEndDate, recordItemList);

            futureList.add(CompletableFuture.completedFuture(null));
        }

        return futureList;
    }
}
