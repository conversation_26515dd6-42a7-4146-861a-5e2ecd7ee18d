package com.zkteco.zkbiosecurity.att.utils;

import java.lang.reflect.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.*;

import com.zkteco.zkbiosecurity.att.calc.bo.AttTimeSlotBO;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.enums.AttRuleEnum;
import com.zkteco.zkbiosecurity.att.vo.AttBioTemplateItem;
import com.zkteco.zkbiosecurity.att.vo.AttDayDetailReportItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import com.zkteco.zkbiosecurity.att.constants.AttApiConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.constants.AttShiftConstant;
import com.zkteco.zkbiosecurity.att.model.AttShift;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.core.config.DataSourceConfig;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * @version V1.0
 * @date Created In 15:15 2018/8/14
 */
@Slf4j
public class AttCommonUtils {

    /**
     * 进制卡号转换
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/6/23 17:55
     * @param cardNo
     * @param beforeRadix
     * @param afterRadix
     * @return java.lang.String
     */
    public static String convertCardNo(String cardNo, int beforeRadix, int afterRadix) {
        cardNo = new BigInteger(cardNo, beforeRadix).toString(afterRadix);// 卡格式为16进制
        return cardNo;
    }

    /**
     * 去除首位逗号、或者两个逗号保留一个逗号
     *
     * <AUTHOR> href=\"<EMAIL>\">liangaoqi</a>
     * @since 2017年10月16日 上午9:53:45
     * @param str
     * @return
     */
    public static String removeExComma(String str) {
        if (StringUtils.isNotBlank(str)) {
            return str.replaceAll("(?:^,+)|(?:,+$)|(,),+", "$1");
        }
        return null;
    }

    /**
     * 流程状态转换
     * 
     * @param flowStatus
     * @return
     */
    public static String buildFlowStatus2AuditStatus(String flowStatus) {
        if (StringUtils.isBlank(flowStatus)) {
            return "";
        }
        String auditStatus = "";
        switch (flowStatus) {
            case AttConstant.FLOW_STATUS_CREATE:
                auditStatus = AttApiConstant.AUDIT_STATUS_UNAPPROVAL;
                break;
            case AttConstant.FLOW_STATUS_REFUSE:
                auditStatus = AttApiConstant.AUDIT_STATUS_REJECT;
                break;
            case AttConstant.FLOW_STATUS_REVOKE:
                auditStatus = AttApiConstant.AUDIT_STATUS_REVOKE;
                break;
            case AttConstant.FLOW_STATUS_COMPLETE:
                auditStatus = AttApiConstant.AUDIT_STATUS_APPROVAL;
                break;
            case AttConstant.FLOW_STATUS_ABNORMAL_END:
                auditStatus = AttApiConstant.AUDIT_STATUS_ABNORMAL_END;
                break;
            default:
                auditStatus = "";
        }
        return auditStatus;
    }

    /**
     * 获取指定日期所在周的第一天或最后一天
     * 
     * <AUTHOR> href="mailto:<EMAIL>">方武略</a>
     * @since 2019年9月17日 下午6:45:00
     * @param curDay
     * @param retType firstDay:本周第一天；secondDay：本周最后一天
     * @return
     */
    public static Date getWeekFirstOrEndDay(Date curDay, String retType) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(curDay);
        int day_of_week = 0;
        if (cal.get(Calendar.DAY_OF_WEEK) == 1) {
            day_of_week = -6;
        } else {
            day_of_week = 2 - cal.get(Calendar.DAY_OF_WEEK);
        }
        cal.add(Calendar.DAY_OF_WEEK, day_of_week);

        if ("firstDay".equals(retType)) {
            return cal.getTime();
        } else {
            cal.add(Calendar.DAY_OF_WEEK, 6);
            return cal.getTime();
        }
    }

    /** 判断两个日期是否同一天 */
    public static boolean isTheSameDay(Date date, Date otherDate) {
        String dateStr = AttDateUtils.dateToStrAsShort(date);
        String otherDateStr = AttDateUtils.dateToStrAsShort(otherDate);
        if (dateStr.equals(otherDateStr)) {
            return true;
        }
        return false;
    }

    /**
     * 计算周期单位为天的模
     */
    public static int countDayMod(AttShift attShift, Long attDateLong, Date schStartDate) {
        String periodStartMode = attShift.getPeriodStartMode();
        if (StringUtils.isBlank(periodStartMode)) {
            periodStartMode = AttConstant.ATT_PERIODSTARTMODE_BYPERIOD;
        }
        Date startDate = null;
        switch (periodStartMode) {
            // 按周期起始日期
            case AttConstant.ATT_PERIODSTARTMODE_BYPERIOD:
                startDate = attShift.getStartDate();
                break;
            // 按排班开始日期
            case AttConstant.ATT_PERIODSTARTMODE_BYSCH:
                startDate = schStartDate;
                break;
            default:
                break;
        }

        // 当前日期与班次的开始日期相差天数
        if (startDate == null) {
            return 0;
        }
        int diffDay = (int)((attDateLong - startDate.getTime()) / 86400000);
        // 月内循环
        if (attShift.getIsShiftWithinMonth()) {
            // 取得2个日期相差几月
            int diffMonth = AttDateUtils.getMonth(startDate.getTime(), attDateLong);
            if (diffMonth > 0) {
                // 取得该天为当月第几天
                diffDay = AttDateUtils.getDayOfMonth(attDateLong) - 1;
            }
        }
        // 除以周期数，取模
        // 修改天数差为负数时用绝对值取余为取模 在起始日期以前的也按周期循环 by ljf 2019/8/12
        int modDay = Math.floorMod(diffDay, attShift.getPeriodNumber());
        return modDay;
    }

    /**
     * 计算周期单位为月的模
     */
    public static int countMonthMod(AttShift attShift, Long attDateLong, Long startDateLong) {
        // 当前日期是当月的第几天
        int dayOfMonth = AttDateUtils.getDayOfMonth(attDateLong) - 1;
        // 修改当前日期与班次的开始日期相差月数，改为取当前日期是当年第几个月
        int diffMonth = DateUtil.getMonth(new Date(attDateLong)) + 1;
        // 除以周期数，取模
        int modMonth = diffMonth % attShift.getPeriodNumber();
        return diffMonth == 0 ? dayOfMonth : (dayOfMonth + 31 * modMonth);
    }

    /**
     * 将分钟转化成小时或者工作日（返回字符串）
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/4 9:13
     * @param minute
     * @param shouldMinute
     * @param attLeaveTypeItem
     * @param decimal
     * @return java.lang.String
     */
    public static String convertMinute(Integer minute, Integer shouldMinute, AttLeaveTypeItem attLeaveTypeItem,
        int decimal) {
        return convertMinuteToBD(minute, shouldMinute, attLeaveTypeItem, decimal) + "";
    }

    /**
     * 将分钟转化成小时或者工作日（返回BigDecimal类型）
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/4 9:13
     * @param minute
     * @param shouldMinute
     * @param attLeaveTypeItem
     * @param decimal
     * @return java.lang.String
     */
    public static BigDecimal convertMinuteToBD(Integer minute, Integer shouldMinute, AttLeaveTypeItem attLeaveTypeItem,
        int decimal) {
        if (minute == null || minute == 0) {
            return BigDecimal.ZERO.setScale(decimal);
        }

        BigDecimal bigDecimal = BigDecimal.valueOf(minute);
        // 最小单位-单位
        String unit = attLeaveTypeItem.getConvertUnit();
        // 最小单位-数值
        BigDecimal count = BigDecimal.valueOf(attLeaveTypeItem.getConvertCount());

        RoundingMode roundingMode = getRoundingMode(attLeaveTypeItem.getConvertType());

        if (AttConstant.ATT_CONVERT_UNIT_MINUTE.equals(unit)) {
            BigDecimal ret = bigDecimal.divide(count, 0, roundingMode);
            return ret.multiply(count).setScale(decimal);
        } else if (AttConstant.ATT_CONVERT_UNIT_HOUR.equals(unit)) {
            BigDecimal ret = bigDecimal.divide(BigDecimal.valueOf(60).multiply(count), 0, roundingMode);
            return ret.multiply(count).setScale(decimal);
        } else {

            if (shouldMinute == null || shouldMinute == 0) {
                return BigDecimal.ZERO.setScale(decimal);
            }

            BigDecimal ret = bigDecimal.divide(BigDecimal.valueOf(shouldMinute).multiply(count), 0, roundingMode);
            return ret.multiply(count).setScale(decimal);
        }
    }

    /**
     * 获取舍入控制
     * 
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/4 17:20
     * @param roundingMode
     * @return java.math.RoundingMode
     */
    public static RoundingMode getRoundingMode(String roundingMode) {

        if (AttConstant.ATT_CONVERT_ABORT.equals(roundingMode)) {
            // 向下（舍弃）
            return RoundingMode.DOWN;
        } else if (AttConstant.ATT_CONVERT_CARRY.equals(roundingMode)) {
            // 向上（进位）
            return RoundingMode.UP;
        }
        // 四舍五入
        return RoundingMode.HALF_UP;
    }

    /**
     * 动态修改表头lable
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/4 9:14
     * @param clazz
     * @param fieldName
     * @param Label
     * @return void
     */
    public static void modifyItemLabelByFieldName(Class clazz, String fieldName, String Label) {
        try {
            Field field = clazz.getDeclaredField(fieldName);
            GridColumn gridColumn = field.getAnnotation(GridColumn.class);
            InvocationHandler invocationHandler = Proxy.getInvocationHandler(gridColumn);
            Field value = invocationHandler.getClass().getDeclaredField("memberValues");
            value.setAccessible(true);
            Map<String, Object> memberValues = (Map<String, Object>)value.get(invocationHandler);
            memberValues.put("label", Label);
        } catch (Exception e) {
            log.error("exception = ", e);
        }
    }

    /**
     * 动态修改表头SecHeader
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/4 9:14
     * @param clazz
     * @param fieldName
     * @param secHeader
     * @return void
     */
    public static void modifyItemSecHeaderByFieldName(Class clazz, String fieldName, String secHeader) {
        try {
            Field field = clazz.getDeclaredField(fieldName);
            GridColumn gridColumn = field.getAnnotation(GridColumn.class);
            InvocationHandler invocationHandler = Proxy.getInvocationHandler(gridColumn);
            Field value = invocationHandler.getClass().getDeclaredField("memberValues");
            value.setAccessible(true);
            Map<String, Object> memberValues = (Map<String, Object>)value.get(invocationHandler);
            memberValues.put("secHeader", secHeader);
        } catch (Exception e) {
            log.error("exception = ", e);
        }
    }

    /**
     * 字符串转Integer如果空字符串返回0
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/4 18:56
     * @param str
     * @return java.lang.Integer
     */
    public static int strToInt(String str) {
        if (StringUtils.isNotBlank(str)) {
            return Integer.valueOf(str).intValue();
        }
        return 0;
    }

    /**
     * 设置整点时间
     *
     * @param calendar:
     * @param hour:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/17 11:27
     * @since 1.0.0
     */
    public static void setCalendarHour(Calendar calendar, int hour) {
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
    }

    /**
     * 获取item Format集合
     *
     * @param cls:
     * @param column:
     * @return java.util.Map<java.lang.String,java.lang.String>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/12/1 11:34
     * @since 1.0.0
     */
    public static Map<String, String> getFormatMap(Class cls, String column) {
        Map<String, String> map = new HashMap<>();
        try {
            String format = cls.getDeclaredField(column).getAnnotation(GridColumn.class).format();
            String[] formatArray = format.split(",");
            for (String fa : formatArray) {
                String[] array = fa.split("=", 2);
                if (array.length == 2) {
                    map.put(array[0], I18nUtil.i18nCode(array[1]));
                }
            }
        } catch (Exception e) {
            return map;
        }
        return map;
    }

    /**
     * 获取最小单位名称
     *
     * @param attLeaveTypeItem:
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020-11-20 17:42
     * @since 1.0.0
     */
    public static String getConvertUnit(AttLeaveTypeItem attLeaveTypeItem) {
        if (Objects.isNull(attLeaveTypeItem) || StringUtils.isBlank(attLeaveTypeItem.getConvertUnit())) {
            return "";
        }
        String convertUnit = "";
        switch (attLeaveTypeItem.getConvertUnit()) {
            case AttConstant.ATT_CONVERT_UNIT_MINUTE:
                convertUnit = I18nUtil.i18nCode("common_minutes");
                break;
            case AttConstant.ATT_CONVERT_UNIT_HOUR:
                convertUnit = I18nUtil.i18nCode("common_hour");
                break;
            case AttConstant.ATT_CONVERT_UNIT_DAY:
                convertUnit = I18nUtil.i18nCode("common_days");
                break;
            default:
        }
        return convertUnit;
    }

    /**
     * 加班类型名称
     *
     * @param overtimeSign:
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020-12-10 20:31
     * @since 1.0.0
     */
    public static String overtimeSignName(Short overtimeSign) {
        if (Objects.isNull(overtimeSign)) {
            return "";
        }
        String overtimeType = "";
        switch (overtimeSign) {
            case AttShiftConstant.OvertimeSign.NORMAL:
                overtimeType = I18nUtil.i18nCode("att_overtime_normal");
                break;
            case AttShiftConstant.OvertimeSign.REST:
                overtimeType = I18nUtil.i18nCode("att_overtime_rest");
                break;
            case AttShiftConstant.OvertimeSign.HOLIDAY:
                overtimeType = I18nUtil.i18nCode("att_shift_holidayOt");
                break;
            default:
        }
        return overtimeType;
    }

    /**
     * 考勤记录表，根据日期进行行转列查询语句组装
     *
     * @param itemSql:
     * @param startDate:
     * @param endDate:
     * @param filedMap:
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/12/24 10:32
     * @since 1.0.0
     */
    public static String buildRecordRowToColumnSql(String itemSql, Date startDate, Date endDate,
        Map<String, Object> filedMap) {

        // 遍历天数
        List<String> days = AttDateUtils.getBetweenDate(AttDateUtils.dateToStrAsShort(startDate),
            AttDateUtils.dateToStrAsShort(endDate));

        if (days.size() > 124) {
            throw new ZKBusinessException("att_customReport_queryMaxRange");
        }

        System.err.println();

        // 数据库兼容处理
        String dataSource = DataSourceConfig.getDbType();
        StringBuffer sql = new StringBuffer();
        if (ZKConstant.ORACLE.equals(dataSource)) {
            for (String day : days) {

                for (Map.Entry<String, Object> filedEntry : filedMap.entrySet()) {
                    String key = filedEntry.getKey();
                    Object val = filedEntry.getValue();
                    sql.append(", MAX ( CASE WHEN TO_CHAR(T.ATT_DATE,'yyyy-mm-dd') = '");
                    sql.append(day);
                    sql.append(
                        "' THEN T." + key + " ELSE " + val + " END ) AS \"" + key + "_" + day.substring(5, 10) + "\"");
                }
            }
        } else {
            for (String day : days) {
                for (Map.Entry<String, Object> filedEntry : filedMap.entrySet()) {
                    String key = filedEntry.getKey();
                    Object val = filedEntry.getValue();
                    sql.append(", MAX ( CASE WHEN T.ATT_DATE = '");
                    sql.append(day);
                    sql.append(
                        "' THEN T." + key + " ELSE " + val + " END ) AS \"" + key + "_" + day.substring(5, 10) + "\"");
                }
            }
        }

        // 拼凑语句
        StringBuffer resultSql = new StringBuffer(itemSql);
        int fromIndex = resultSql.indexOf("FROM");
        resultSql.insert(fromIndex, sql);
        return resultSql.toString();
    }

    public static void buildLeaveDetail(String leaveDetails, Integer shouldMinute, int decimal,
        Map<String, AttLeaveTypeItem> leaveTypeItemMap, Map<String, Object> leaveDetailMap) {
        Map<String, Object> map = new HashMap<>();
        String[] leaveDetailArray = leaveDetails.split(",");
        for (String leaveDetail : leaveDetailArray) {
            if (StringUtils.isNotBlank(leaveDetail)) {
                String[] leaveDetailArr = leaveDetail.split("-");
                String leaveTypeNo = leaveDetailArr[0];
                int num = AttCommonUtils.strToInt(leaveDetailArr[1]);
                if (map.containsKey(leaveTypeNo)) {
                    BigDecimal nowNum = BigDecimal.valueOf(Double.valueOf(map.get(leaveTypeNo) + ""));
                    String addRet =
                        AttCommonUtils.convertMinute(num, shouldMinute, leaveTypeItemMap.get(leaveTypeNo), decimal);
                    BigDecimal addNum = BigDecimal.valueOf(Double.valueOf(addRet));
                    map.put(leaveTypeNo, nowNum.add(addNum).setScale(decimal) + "");
                } else {
                    map.put(leaveTypeNo,
                        AttCommonUtils.convertMinute(num, shouldMinute, leaveTypeItemMap.get(leaveTypeNo), decimal));
                }
            }
        }
        String defaultValue = BigDecimal.ZERO.setScale(decimal) + "";
        for (Map.Entry<String, AttLeaveTypeItem> entry : leaveTypeItemMap.entrySet()) {
            AttLeaveTypeItem attLeaveTypeItem = entry.getValue();
            Object value = MapUtils.getObject(map, attLeaveTypeItem.getLeaveTypeNo(), defaultValue);
            if (leaveDetailMap.containsKey(attLeaveTypeItem.getLeaveTypeName())) {
                BigDecimal nowNum = BigDecimal.valueOf(Double.valueOf(value + ""));
                BigDecimal addNum =
                    BigDecimal.valueOf(Double.valueOf(leaveDetailMap.get(attLeaveTypeItem.getLeaveTypeName()) + ""));
                leaveDetailMap.put(attLeaveTypeItem.getLeaveTypeName(), nowNum.add(addNum).setScale(decimal) + "");
            } else {
                leaveDetailMap.put(attLeaveTypeItem.getLeaveTypeName(), value);
            }
        }
    }

    /**
     * 根据原始pin创建数字
     *
     * @param pin
     * @return
     */
    public static Long createNumberPin(String pin) {
        StringBuffer targetPin = new StringBuffer();
        String originPin = pin.toUpperCase();
        boolean containLetter = pin.matches(".*[a-zA-Z]+.*");
        if (containLetter) {
            targetPin.append(2);
        } else {
            targetPin.append(1);
        }
        for (int i = 0; i < originPin.length(); i++) {
            char c = originPin.charAt(i);
            // 将英文字母转换为1-26
            if (c > 64 && c < 91) {
                targetPin.append((int)c);
            } else {
                if (containLetter) {
                    targetPin.append(0);
                }
                targetPin.append(c);
            }
        }
        if (containLetter) {
            while (targetPin.length() < 19) {
                targetPin.append(0);
            }
        }
        return Long.parseLong(targetPin.substring(0, targetPin.length() >= 19 ? 19 : targetPin.length()));
    }

    public static Integer getIntegerValue(Object[] objects, int index) {
        if (objects.length > index) {
            String value = objects[index] + "";
            return Integer.valueOf(value);
        }
        return 0;
    }

    public static String getStringValue(Object[] objects, int index) {
        if (objects.length > index) {
            return Objects.isNull(objects[index]) ? "" : objects[index] + "";
        }
        return "";
    }

    /**
     * 组装考勤抓拍图片路径（全路径，包含图片名称）
     *
     * @param sn:
     * @param attDate:
     * @param attDatetime:
     * @param personPin:
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/11 9:49
     * @since 1.0.0
     */
    public static String buildAttPhotoUrl(String sn, String attDate, Date attDatetime, String personPin) {
        String filePath = buildAttPhotoFilePath(sn, attDate);
        return filePath + DateUtil.dateToString(attDatetime, DateUtil.DateStyle.YYYYMMDDHHMMSS) + "-" + personPin
            + ".jpg";
    }

    /**
     * 组装考勤抓拍图片路径
     *
     * @param sn:
     * @param attDate:
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/11 9:49
     * @since 1.0.0
     */
    public static String buildAttPhotoFilePath(String sn, String attDate) {
        return AttConstant.TRANSACTION_PHOTP_PTAH + sn + "/" + attDate + "/";
    }

    public static <T> boolean haveHighVersion(Map<String, T> map, String startKey, String version) {
        if (map == null) {
            return false;
        }
        if (StringUtils.isBlank(startKey)) {
            return false;
        }
        if (StringUtils.isBlank(version)) {
            return false;
        }

        for (Map.Entry<String, T> entry : map.entrySet()) {
            if (entry.getKey().startsWith(startKey)) {
                if (entry.getKey().compareTo(startKey + version) > 0) {
                    return true;
                }
            }
        }
        return false;
    }

    public static <T> String getClassFieldValue(T t, String methodName) {
        try {
            String value = null;
            Method method = t.getClass().getMethod(methodName);
            if (method != null) {
                Object o = method.invoke(t);
                if (Objects.nonNull(o)) {
                    value = o + "";
                }
            }
            return value;
        } catch (Exception e) {
            return null;
        }
    }

    public static <T> void setClassFieldValue(T t, String methodName, Object value, Class clazz) {
        try {
            Method method = t.getClass().getMethod(methodName, clazz);
            if (method != null) {
                method.invoke(t, value);
            }
        } catch (Exception e) {
            log.error("error {}", methodName, e);
        }
    }

    /**
     * 设置时段是否垮天
     * 
     * @param attTimeSlotBO:
     * @param attTimeSlotItem:
     * @param dateStr:
     * @param crossDay:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/2/24 16:37
     * @since 1.0.0
     */
    public static void setSetFirstAndSecondDay(AttTimeSlotBO attTimeSlotBO, AttTimeSlotItem attTimeSlotItem,
        String dateStr, String crossDay) {
        String timeSlotStart = "";
        String timeSlotEnd = "";
        if (AttConstant.PERIODTYPE_NORMAL.equals(attTimeSlotItem.getPeriodType())) {
            timeSlotStart = attTimeSlotItem.getToWorkTime();
            timeSlotEnd = attTimeSlotItem.getOffWorkTime();
        } else if (AttConstant.PERIODTYPE_ELASTIC.equals(attTimeSlotItem.getPeriodType())) {
            // 弹性时间段只有开始签到和结束签退时间 by ljf 2020/8/10
            timeSlotStart = attTimeSlotItem.getStartSignInTime();
            timeSlotEnd = attTimeSlotItem.getEndSignOffTime();
        }
        String isInterDay = timeSlotStart.compareTo(timeSlotEnd) > 0 ? AttCalculationConstant.IsInterDay.THE_INTER_DAY
            : AttCalculationConstant.IsInterDay.THE_SAME_DAY;
        attTimeSlotBO.setIsInterDay(isInterDay);
        if (AttCalculationConstant.IsInterDay.THE_SAME_DAY.equals(isInterDay)) {
            attTimeSlotBO.setFirstDay(dateStr);
            attTimeSlotBO.setSecondDay(dateStr);
        } else {
            // 跨天
            if (AttRuleEnum.CrossDay.getValueOne().equals(crossDay)) {
                // 跨天算第一天
                String secondDay =
                    AttDateUtils.dateToStrAsShort(DateUtil.addDay(AttDateUtils.stringToYmdDate(dateStr), 1));
                attTimeSlotBO.setFirstDay(dateStr);
                attTimeSlotBO.setSecondDay(secondDay);
            } else {
                // 跨天算第二天
                String firstDay =
                    AttDateUtils.dateToStrAsShort(DateUtil.addDay(AttDateUtils.stringToYmdDate(dateStr), -1));
                attTimeSlotBO.setFirstDay(firstDay);
                attTimeSlotBO.setSecondDay(dateStr);
            }
        }
    }

    public static String getUnit(AttLeaveTypeItem attLeaveTypeItem) {
        if (attLeaveTypeItem == null) {
            return "";
        }
        String convertUnit = attLeaveTypeItem.getConvertUnit();
        String convertUnitName = I18nUtil.i18nCode("common_minute");
        if (AttConstant.ATT_CONVERT_UNIT_HOUR.equals(convertUnit)) {
            convertUnitName = I18nUtil.i18nCode("common_hour");
        } else if (AttConstant.ATT_CONVERT_UNIT_DAY.equals(convertUnit)) {
            convertUnitName = I18nUtil.i18nCode("common_day");
        }
        return convertUnitName;
    }

    public static int getBeginIndex(int pageNo, int pageSize) {
        return pageNo * pageSize;
    }

    public static int getPageSize(int beginIndex, int endIndex) {
        return endIndex - beginIndex + 1;
    }

    public static String convertOverTimeOTString(AttDayDetailReportItem item, Integer shouldMinute,
        AttLeaveTypeItem overtimeType, int decimal, Map<String, String> overtimeParamsMap, String otType) {
        return convertOverTimeOT(item, shouldMinute, overtimeType, decimal, overtimeParamsMap, otType) + "";
    }

    public static BigDecimal convertOverTimeOT(AttDayDetailReportItem item, Integer shouldMinute,
        AttLeaveTypeItem attLeaveTypeItem, int decimal, Map<String, String> overtimeParamsMap, String otType) {

        BigDecimal overtimeUsualConvert = new BigDecimal(item.getOvertimeUsualConvert());
        BigDecimal overtimeHoliday = new BigDecimal(item.getOvertimeHolidayConvert());
        BigDecimal overtimeRest = new BigDecimal(item.getOvertimeRestConvert());

        String convertUnit = attLeaveTypeItem.getConvertUnit();
        if (AttConstant.ATT_CONVERT_UNIT_MINUTE.equals(convertUnit)) {
            overtimeUsualConvert = overtimeUsualConvert.divide(BigDecimal.valueOf(60), decimal, RoundingMode.HALF_UP);
            overtimeHoliday = overtimeHoliday.divide(BigDecimal.valueOf(60), decimal, RoundingMode.HALF_UP);
            overtimeRest = overtimeRest.divide(BigDecimal.valueOf(60), decimal, RoundingMode.HALF_UP);
        } else if (AttConstant.ATT_CONVERT_UNIT_DAY.equals(convertUnit)) {
            overtimeUsualConvert = overtimeUsualConvert.multiply(BigDecimal.valueOf(shouldMinute))
                .divide(BigDecimal.valueOf(60), decimal, RoundingMode.HALF_UP);
            overtimeHoliday = overtimeHoliday.multiply(BigDecimal.valueOf(shouldMinute)).divide(BigDecimal.valueOf(60),
                decimal, RoundingMode.HALF_UP);
            overtimeRest = overtimeRest.multiply(BigDecimal.valueOf(shouldMinute)).divide(BigDecimal.valueOf(60),
                decimal, RoundingMode.HALF_UP);
        }

        BigDecimal result = BigDecimal.ZERO;
        if ("OT1".equals(otType)) {
            String normalOT1 = overtimeParamsMap.getOrDefault("att.overtimeLevel.normalOT1", "0-0");
            String restOT1 = overtimeParamsMap.getOrDefault("att.overtimeLevel.restOT1", "0-0");
            String holidayOT1 = overtimeParamsMap.getOrDefault("att.overtimeLevel.holidayOT1", "0-0");
            BigDecimal ot1 = calcOverTimeLeave(overtimeUsualConvert, normalOT1)
                .add(calcOverTimeLeave(overtimeRest, restOT1)).add(calcOverTimeLeave(overtimeHoliday, holidayOT1));
            result = ot1;

        } else if ("OT2".equals(otType)) {
            String normalOT2 = overtimeParamsMap.getOrDefault("att.overtimeLevel.normalOT2", "0-0");
            String restOT2 = overtimeParamsMap.getOrDefault("att.overtimeLevel.restOT2", "0-0");
            String holidayOT2 = overtimeParamsMap.getOrDefault("att.overtimeLevel.holidayOT2", "0-0");
            BigDecimal ot2 = calcOverTimeLeave(overtimeUsualConvert, normalOT2)
                .add(calcOverTimeLeave(overtimeRest, restOT2)).add(calcOverTimeLeave(overtimeHoliday, holidayOT2));
            result = ot2;

        } else if ("OT3".equals(otType)) {
            String normalOT3 = overtimeParamsMap.getOrDefault("att.overtimeLevel.normalOT3", "0-0");
            String restOT3 = overtimeParamsMap.getOrDefault("att.overtimeLevel.restOT3", "0-0");
            String holidayOT3 = overtimeParamsMap.getOrDefault("att.overtimeLevel.holidayOT3", "0-0");
            BigDecimal ot3 = calcOverTimeLeave(overtimeUsualConvert, normalOT3)
                .add(calcOverTimeLeave(overtimeRest, restOT3)).add(calcOverTimeLeave(overtimeHoliday, holidayOT3));
            result = ot3;
        }
        return result.setScale(decimal, RoundingMode.HALF_UP);
    }

    private static BigDecimal calcOverTimeLeave(BigDecimal ot, String levelParams) {
        BigDecimal bigDecimal = new BigDecimal(0);
        if (StringUtils.isNotBlank(levelParams)) {
            String[] levelParamArray = levelParams.split("-");
            if (levelParamArray.length == 2 && StringUtils.isNoneBlank(levelParamArray[0])
                && StringUtils.isNoneBlank(levelParamArray[1])) {
                BigDecimal start = new BigDecimal(levelParamArray[0]);
                BigDecimal end = new BigDecimal(levelParamArray[1]);
                if (ot.compareTo(start) > 0) {
                    if (ot.compareTo(end) <= 0) {
                        bigDecimal = ot.subtract(start);
                    } else {
                        bigDecimal = end.subtract(start);
                    }
                }
            }
        }
        return bigDecimal;
    }

    public static String buildName(String name, String lastName) {
        name = StringUtils.isNotBlank(name) ? name.trim() : "";
        lastName = StringUtils.isNotBlank(lastName) ? lastName.trim() : "";

        if (name.isEmpty()) {
            return lastName;
        } else if (lastName.isEmpty()) {
            return name;
        } else {
            return name + " " + lastName;
        }
    }

    public static void main(String[] args) {
        Map<String, AttBioTemplateItem> bioTemplateFPMap = new HashMap<>();
        bioTemplateFPMap.put("8888_1_10", new AttBioTemplateItem());
        bioTemplateFPMap.put("8888_1_12", new AttBioTemplateItem());
        bioTemplateFPMap.put("8888_2_10", new AttBioTemplateItem());
        bioTemplateFPMap.put("8888_2_12", new AttBioTemplateItem());
        bioTemplateFPMap.put("8888_3_10", new AttBioTemplateItem());
        System.out.println(AttCommonUtils.haveHighVersion(bioTemplateFPMap, "8888_2_", "10"));
    }
}
