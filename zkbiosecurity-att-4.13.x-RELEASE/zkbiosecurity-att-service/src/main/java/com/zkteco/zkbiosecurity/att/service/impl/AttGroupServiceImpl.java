package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;

import com.zkteco.zkbiosecurity.auth.service.AuthSessionServcie;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.constants.AttCommonSchConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.*;
import com.zkteco.zkbiosecurity.att.model.AttCycleSch;
import com.zkteco.zkbiosecurity.att.model.AttGroup;
import com.zkteco.zkbiosecurity.att.model.AttPerson;
import com.zkteco.zkbiosecurity.att.model.AttTempSch;
import com.zkteco.zkbiosecurity.att.service.AttCycleSchService;
import com.zkteco.zkbiosecurity.att.service.AttGroupService;
import com.zkteco.zkbiosecurity.att.service.AttPersonService;
import com.zkteco.zkbiosecurity.att.service.AttTempSchService;
import com.zkteco.zkbiosecurity.att.vo.AttGroupItem;
import com.zkteco.zkbiosecurity.att.vo.AttGroupPersonSelectItem;
import com.zkteco.zkbiosecurity.att.vo.AttPersonItem;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;

/**
 * 分组
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 18:03
 * @since 1.0.0
 */
@Service
public class AttGroupServiceImpl implements AttGroupService {

    @Autowired
    private AttGroupDao attGroupDao;
    @Autowired
    private AttPersonDao attPersonDao;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AttTempSchDao attTempSchDao;
    @Autowired
    private AttGroupSchDao attGroupSchDao;
    @Autowired
    private AttCycleSchDao attCycleSchDao;
    @Autowired
    private AttTempSchService attTempSchService;
    @Autowired
    private AttCycleSchService attCycleSchService;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AuthSessionServcie authSessionServcie;

    @Override
    @Transactional
    public AttGroupItem saveItem(AttGroupItem item) {
        AttGroup attGroup = Optional.ofNullable(item).map(AttGroupItem::getId).filter(StringUtils::isNotBlank)
            .flatMap(attGroupDao::findById).filter(Objects::nonNull).orElse(new AttGroup());
        ModelUtil.copyPropertiesIgnoreNull(item, attGroup);
        attGroupDao.save(attGroup);
        item.setId(attGroup.getId());
        return item;
    }

    @Override
    public List<AttGroupItem> getByCondition(AttGroupItem condition) {
        return (List<AttGroupItem>)attGroupDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return attGroupDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                attGroupDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AttGroupItem getItemById(String id) {
        List<AttGroupItem> items = getByCondition(new AttGroupItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public boolean existsByGroupName(String groupName) {
        return attGroupDao.existsByGroupName(groupName);
    }

    @Override
    @Transactional
    public boolean deleteByGroup(String ids) {
        // 删除att_person 关联的group_id字段进行删除
        if (StringUtils.isNotBlank(ids)) {
            List<String> groupIds = Arrays.asList(ids.split(AttConstant.COMMA));
            List<AttPerson> attPersonList = attPersonDao.findByGroupIdIn(groupIds);
            List<String> deleteGroupIdList = new ArrayList<>();
            List<String> deletePersIdList = new ArrayList<>();
            for (AttPerson attPerson : attPersonList) {
                deleteGroupIdList.add(attPerson.getGroupId());
                deletePersIdList.add(attPerson.getPersonId());
                attPerson.setGroupId(AttCommonSchConstant.GroupConstant.DEFAULT_NO_GROUP_ID);
            }
            attPersonDao.saveAll(attPersonList);

            // 删除分组更新人事人员缓存的考勤附加参数的分组ID
            attPersonService
                .updatePersonCacheExtParams(ModelUtil.copyListProperties(attPersonList, AttPersonItem.class));

            for (String id : groupIds) {
                attGroupDao.deleteById(id);
            }
            // 删除分组同时，找出对应的排班也删除（删除对应排班也会将redis的排班信息删除）
            List<AttCycleSch> attCycleSchList = attCycleSchDao.findByGroupIdIn(groupIds);
            if (attCycleSchList != null) {
                attCycleSchService.deleteByIds(CollectionUtil.getModelIds(attCycleSchList));
            }
            List<AttTempSch> attTempSchList = attTempSchDao.findByGroupIdIn(groupIds);
            if (attTempSchList != null) {
                attTempSchService.deleteByIds(CollectionUtil.getModelIds(attTempSchList));
            }
        }
        return false;
    }

    @Override
    @Transactional
    public void handlerTransfer(List<AttGroupItem> attGroupItems) {
        // 数据量不多，不需要分页
        List<AttGroup> attGroupList = new ArrayList<>();
        for (AttGroupItem attGroupItem : attGroupItems) {
            // String groupNo = attGroupItem.getGroupNo();
            String groupName = attGroupItem.getGroupName();
            AttGroup attGroup = attGroupDao.findByGroupName(groupName);
            if (Objects.isNull(attGroup)) {
                attGroup = new AttGroup();
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(attGroupItem, attGroup, "id");
            attGroupList.add(attGroup);
        }
        attGroupDao.saveAll(attGroupList);
    }

    @Override
    public Pager getNoExistPerson(String sessionId, AttGroupPersonSelectItem condition, int pageNo, int pageSize) {
        // 当前登录权限过滤
        if (StringUtils.isNotBlank(sessionId)) {
            SecuritySubject securitySubject = authSessionServcie.getSecuritySubject(sessionId);
            // 非超级用户进行数据权限过滤
            if (!securitySubject.getIsSuperuser() && securitySubject.getDepartmentIds() != null
                    && securitySubject.getDepartmentIds().size() > 0) {
                condition.setUserId(securitySubject.getUserId());
            }
        }
        // 是否包含下级
        if ("true".equals(condition.getIsIncludeLower()) && !StringUtils.isBlank(condition.getDeptId())) {
            List<AuthDepartmentItem> itemAndChildIds = authDepartmentService.getItemAndChildById(condition.getDeptId());
            if (itemAndChildIds.size() > 0) {
                condition.setInDeptId(CollectionUtil.getItemIds(itemAndChildIds));
                condition.setDeptId(null);
            }
        }
        return attGroupDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
    }

    @Override
    public void addCondition(String sessionId, AttPersonItem condition) {
        if (StringUtils.isBlank(condition.getInDeptId())) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, condition.getDeptId(),
                condition.getDeptCode(), condition.getDeptName()));
            condition.setDeptId(null);
        } else if (StringUtils.isNotBlank(sessionId)) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByAuthAndIds(sessionId, condition.getInDeptId()));
            condition.setDeptId(null);
        }
    }

    @Override
    public List<AttGroupItem> getItemsByIds(String ids) {
        List<AttGroupItem> list = new ArrayList<>();
        if (StringUtils.isNotBlank(ids)) {
            List<AttGroup> attGroupList = attGroupDao.findByIdList(CollectionUtil.strToList(ids));
            if (!CollectionUtil.isEmpty(attGroupList)) {
                list = ModelUtil.copyListProperties(attGroupList, AttGroupItem.class);
            }
        }
        return list;
    }
}