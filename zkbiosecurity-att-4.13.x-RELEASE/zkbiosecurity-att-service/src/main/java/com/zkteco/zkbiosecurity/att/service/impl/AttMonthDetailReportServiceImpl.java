package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.att.bean.AttConditionBean;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.dao.AttRecordDao;
import com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService;
import com.zkteco.zkbiosecurity.att.service.AttMonthDetailReportService;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.service.AttPersonService;
import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.att.vo.AttMonthDetailReportItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.dao.ext.SqlParser;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.pers.service.PersLeavePersonService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonCacheService;
import com.zkteco.zkbiosecurity.pers.vo.PersLeavePersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 考勤月报表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:22
 * @since 1.0.0
 */
@Slf4j
@Service
public class AttMonthDetailReportServiceImpl implements AttMonthDetailReportService {

    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;
    @Autowired
    private PersPersonCacheService persPersonCacheService;
    @Autowired
    private AttRecordDao attRecordDao;

    @Override
    public void modifyItemLabel() {

    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, AttMonthDetailReportItem condition, int pageNo,
        int pageSize) {
        Pager pager = new Pager(pageNo, pageSize);
        List<AttMonthDetailReportItem> items = queryReportItem(sessionId, condition,
            AttCommonUtils.getBeginIndex(pageNo, pageSize), pageSize, "MonthDetailReport", true);
        String countSql = new SqlParser().getSmartCountSql(SQLUtil.getSqlByItem(condition));
        List<Object[]> countList = attRecordDao.getArrayData(countSql);
        if (countList != null && countList.size() > 0 && Objects.nonNull(countList.get(0)[0])) {
            pager.setTotal(Integer.valueOf(countList.get(0)[0] + ""));
        }
        pager.setData(items);
        return pager;
    }

    @Override
    public List<AttMonthDetailReportItem> getMonthDetailReportItemData(String sessionId,
        AttMonthDetailReportItem condition, int beginIndex, int endIndex, boolean showColor) {
        return queryReportItem(sessionId, condition, beginIndex, AttCommonUtils.getPageSize(beginIndex, endIndex),
            "MonthDetailReport", showColor);
    }

    @Override
    public Pager loadMonthWorkTimeReportPager(String sessionId, AttMonthDetailReportItem condition, int pageNo,
        int pageSize) {
        Pager pager = new Pager(pageNo, pageSize);
        List<AttMonthDetailReportItem> items = queryReportItem(sessionId, condition,
            AttCommonUtils.getBeginIndex(pageNo, pageSize), pageSize, "MonthWorkTimeReport", false);
        String countSql = new SqlParser().getSmartCountSql(SQLUtil.getSqlByItem(condition));
        List<Object[]> countList = attRecordDao.getArrayData(countSql);
        if (countList != null && countList.size() > 0 && Objects.nonNull(countList.get(0)[0])) {
            pager.setTotal(Integer.valueOf(countList.get(0)[0] + ""));
        }
        pager.setData(items);
        return pager;
    }

    @Override
    public List<AttMonthDetailReportItem> getMonthWorkTimeReportItemData(String sessionId,
        AttMonthDetailReportItem condition, int beginIndex, int endIndex) {
        return queryReportItem(sessionId, condition, beginIndex, AttCommonUtils.getPageSize(beginIndex, endIndex),
            "MonthWorkTimeReport", false);
    }

    @Override
    public Pager loadMonthCardReportPager(String sessionId, AttMonthDetailReportItem condition, int pageNo,
        int pageSize) {
        Pager pager = new Pager(pageNo, pageSize);
        List<AttMonthDetailReportItem> items = queryReportItem(sessionId, condition,
            AttCommonUtils.getBeginIndex(pageNo, pageSize), pageSize, "MonthCardReport", false);
        String countSql = new SqlParser().getSmartCountSql(SQLUtil.getSqlByItem(condition));
        List<Object[]> countList = attRecordDao.getArrayData(countSql);
        if (countList != null && countList.size() > 0 && Objects.nonNull(countList.get(0)[0])) {
            pager.setTotal(Integer.valueOf(countList.get(0)[0] + ""));
        }
        pager.setData(items);
        return pager;
    }

    @Override
    public List<AttMonthDetailReportItem> getMonthCardReportItemData(String sessionId,
        AttMonthDetailReportItem condition, int beginIndex, int endIndex) {
        return queryReportItem(sessionId, condition, beginIndex, AttCommonUtils.getPageSize(beginIndex, endIndex),
            "MonthCardReport", false);
    }

    @Override
    public Pager loadMonthOvertimeReportPager(String sessionId, AttMonthDetailReportItem condition, int pageNo,
        int pageSize) {
        Pager pager = new Pager(pageNo, pageSize);
        List<AttMonthDetailReportItem> items = queryReportItem(sessionId, condition,
            AttCommonUtils.getBeginIndex(pageNo, pageSize), pageSize, "MonthOvertimeReport", false);
        String countSql = new SqlParser().getSmartCountSql(SQLUtil.getSqlByItem(condition));
        List<Object[]> countList = attRecordDao.getArrayData(countSql);
        if (countList != null && countList.size() > 0 && Objects.nonNull(countList.get(0)[0])) {
            pager.setTotal(Integer.valueOf(countList.get(0)[0] + ""));
        }
        pager.setData(items);
        return pager;
    }

    @Override
    public List<AttMonthDetailReportItem> getMonthOvertimeReportItemData(String sessionId,
        AttMonthDetailReportItem condition, int beginIndex, int endIndex) {
        return queryReportItem(sessionId, condition, beginIndex, AttCommonUtils.getPageSize(beginIndex, endIndex),
            "MonthOvertimeReport", false);
    }

    /**
     * 大数据航专列统计查询 "MonthOvertimeReport","MonthWorkTimeReport","MonthCardReport","MonthDetailReport"
     */
    private List<AttMonthDetailReportItem> queryReportItem(String sessionId, AttMonthDetailReportItem condition,
        int beginIndex, int pageSize, String report, boolean showColor) {
        // 组装条件过滤
        if (StringUtils.isNotBlank(sessionId)) {
            buildCondition(sessionId, condition);
        }

        // 大数据行转列查询
        Map<String, Object> filedMap = new LinkedHashMap();
        if ("MonthDetailReport".equals(report)) {
            filedMap.put("ATTENDANCE_STATUS", "''");
            filedMap.put("EXCEPTION_SCH_TYPE", null);
        } else if ("MonthWorkTimeReport".equals(report)) {
            filedMap.put("VALID_MINUTE", 0);
        } else if ("MonthCardReport".equals(report)) {
            filedMap.put("CARD_VALID_DATA", "''");
        } else if ("MonthOvertimeReport".equals(report)) {
            filedMap.put("OVERTIME_MINUTE", 0);
        }

        String sql = AttCommonUtils.buildRecordRowToColumnSql(SQLUtil.getSqlByItem(condition),
            condition.getMonthStart(), condition.getMonthEnd(), filedMap);
        List<Object[]> objectsList = attRecordDao.createSqlQueryPage(sql, beginIndex, pageSize);

        // 大数据统计组装
        List<AttMonthDetailReportItem> items = buildReportItem(condition, objectsList, filedMap.size(), report, showColor);

        return items;
    }

    /**
     * 大数据统计组装 "MonthDetailReport","MonthWorkTimeReport","MonthCardReport","MonthOvertimeReport"
     */
    private List<AttMonthDetailReportItem> buildReportItem(AttMonthDetailReportItem condition,
        List<Object[]> objectsList, int filedNum, String report, boolean showColor) {

        // 旧状态、兼容处理
        Map<String, AttLeaveTypeItem> attSymbolsMap = getAttSymbolsJson();
        Map<String, AttLeaveTypeItem> oldAttSymbolsMap = getOldRemark(attSymbolsMap);

        // 查询的时间区间
        List<String> days = AttDateUtils.getBetweenDate(AttDateUtils.dateToStrAsShort(condition.getMonthStart()),
            AttDateUtils.dateToStrAsShort(condition.getMonthEnd()));

        List<AttMonthDetailReportItem> items = new ArrayList<>();
        for (Object[] objects : objectsList) {
            String pin = AttCommonUtils.getStringValue(objects, 0);
            AttMonthDetailReportItem item = new AttMonthDetailReportItem();
            item.setPin(pin);

            Map<String, Object> monthDetailMap = new LinkedHashMap<>();

            // 遍历天
            for (int i = 0; i < days.size(); i++) {
                String day = days.get(i);
                String monthDetail = AttCommonUtils.getStringValue(objects, i * filedNum + 1);

                if ("MonthDetailReport".equals(report)) {
                    String exceptionSchType = AttCommonUtils.getStringValue(objects, i * filedNum + 2);
                    String statusRemark =
                        getStatusRemark(monthDetail, attSymbolsMap, exceptionSchType, oldAttSymbolsMap, report, showColor);
                    monthDetailMap.put(day, statusRemark);
                } else {
                    monthDetailMap.put(day, monthDetail);
                }
            }

            // 考勤状态、工作时长、月打卡、月加班
            item.setMonthDetailMap(monthDetailMap);
            items.add(item);
        }

        buildDeptNamePersonName(items);

        return items;
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        Pager pager = new Pager(page, size);
        return pager;
    }

    @Override
    public boolean deleteByIds(String arg0) {
        return false;
    }

    @Override
    public Map<String, String> getAttSymbolsI18nJson() {
        List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getByCondition(new AttLeaveTypeItem());
        Collections.sort(attLeaveTypeItemList, new Comparator<AttLeaveTypeItem>() {
            @Override
            public int compare(AttLeaveTypeItem o1, AttLeaveTypeItem o2) {
                if (o2.getLeaveTypeNo().startsWith("L")) {
                    return -1;
                }
                return 1;
            }
        });
        Map<String, String> symbolsI18nJson = new LinkedHashMap<>();
        for (AttLeaveTypeItem attLeaveTypeItem : attLeaveTypeItemList) {
            symbolsI18nJson.put(attLeaveTypeItem.getLeaveTypeName(), attLeaveTypeItem.getSymbol());
        }
        return symbolsI18nJson;
    }

    private Map<String, AttLeaveTypeItem> getAttSymbolsJson() {
        List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getByCondition(new AttLeaveTypeItem());
        return CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getLeaveTypeNo);
    }

    /**
     * 根据状态标识获取对应的标识
     *
     * @param attendanceStatus : 新多状态
     * @param attSymbolsMap : 状态符号
     * @param oldStatus : 旧单状态
     * @param oldRemarkMap
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021-06-23 16:57
     * @since 1.0.0
     */
    private String getStatusRemark(String attendanceStatus, Map<String, AttLeaveTypeItem> attSymbolsMap,
        String oldStatus, Map<String, AttLeaveTypeItem> oldRemarkMap, String report, boolean showColor) {
        StringBuffer remark = new StringBuffer("");
        if (StringUtils.isBlank(attendanceStatus)) {
            if (oldRemarkMap.containsKey(oldStatus)) {
                AttLeaveTypeItem leaveTypeItem = oldRemarkMap.get(oldStatus);
                if (leaveTypeItem != null) {
                    if (StringUtils.isNotBlank(leaveTypeItem.getColor()) && showColor) {
                        return "<span style=\"color:" + leaveTypeItem.getColor() + "\">"
                                + leaveTypeItem.getSymbol() + "</span>";
                    } else {
                        return leaveTypeItem.getSymbol();
                    }
                }
            }
            return "";
        } else {
            for (String status : attendanceStatus.split(",")) {
                if (attSymbolsMap.containsKey(status)) {
                    AttLeaveTypeItem leaveTypeItem = attSymbolsMap.get(status);
                    if (leaveTypeItem != null) {
                        if (StringUtils.isNotBlank(leaveTypeItem.getColor()) && showColor) {
                            remark.append("<span style=\"color:" + leaveTypeItem.getColor() + "\">"
                                    + leaveTypeItem.getSymbol() + "</span>").append(",");
                        } else {
                            remark.append(leaveTypeItem.getSymbol()).append(",");
                        }
                    }
                }
            }
            return StringUtils.isBlank(remark) ? "" : remark.toString().substring(0, remark.length() - 1);
        }
    }

    /**
     * 获取旧考勤单状态集合
     *
     * @param symbolsMap:
     * @return java.util.Map<java.lang.String,java.lang.String>
     * <AUTHOR>
     * @date 2021-06-23 18:36
     * @since 1.0.0
     */
    private Map<String, AttLeaveTypeItem> getOldRemark(Map<String, AttLeaveTypeItem> symbolsMap) {
        Map<String, AttLeaveTypeItem> oldSymbolsMap = new HashMap<>();
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.HOLIDAY,
            AttCalculationConstant.AttAttendStatus.HOLIDAY);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.PEOPLE_SWAP_CLASS,
            AttCalculationConstant.AttAttendStatus.PERSON_SWAP_CLASS);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.DIFFERENCE_DATE_CLASS,
            AttCalculationConstant.AttAttendStatus.DIFF_DATE_CLASS);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.SAME_DATE_CLASS,
            AttCalculationConstant.AttAttendStatus.SAME_DATE_CLASS);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.NO_SCHEDULING,
            AttCalculationConstant.AttAttendStatus.NO_SCHEDULING);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.SCHEDULING_AND_REST,
            AttCalculationConstant.AttAttendStatus.SCHEDULING_AND_REST);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.CLASSES,
            AttCalculationConstant.AttAttendStatus.TUNE_OFF);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.OFF,
            AttCalculationConstant.AttAttendStatus.TUNE_OFF);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.ABSENT,
            AttCalculationConstant.AttAttendStatus.ABSENT);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.OVERTIME,
            AttCalculationConstant.AttAttendStatus.OVERTIME);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.OUT,
            AttCalculationConstant.AttAttendStatus.OUT);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.TRIP,
            AttCalculationConstant.AttAttendStatus.TRIP);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.LEAVE,
            AttCalculationConstant.AttAttendStatus.LEAVE);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.NO_SIGN_OFF,
            AttCalculationConstant.AttAttendStatus.NO_CHECK_OUT);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.NO_SIGN_IN,
            AttCalculationConstant.AttAttendStatus.NO_CHECK_IN);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.EARLY,
            AttCalculationConstant.AttAttendStatus.EARLY);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.LATE,
            AttCalculationConstant.AttAttendStatus.LATE);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.ARRIVE,
            AttCalculationConstant.AttAttendStatus.ACTUAL);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.NO_CHECK_IN_INCOMPLETE,
            AttCalculationConstant.AttAttendStatus.NO_CHECK_IN_INCOMPLETE);
        buildOldRemark(oldSymbolsMap, symbolsMap, AttCalculationConstant.AttExceptionSchType.NO_CHECK_OUT_INCOMPLETE,
            AttCalculationConstant.AttAttendStatus.NO_CHECK_OUT_INCOMPLETE);
        return oldSymbolsMap;
    }

    private void buildOldRemark(Map<String, AttLeaveTypeItem> exceptionRemarkMap,
        Map<String, AttLeaveTypeItem> attSymbolsMap, String oldStatus, String status) {
        if (attSymbolsMap.containsKey(status)) {
            exceptionRemarkMap.put(oldStatus, attSymbolsMap.get(status));
        }
    }

    /**
     * 组装人员姓名、部门名称信息
     */
    private void buildDeptNamePersonName(List<AttMonthDetailReportItem> itemList) {

        Collection<String> personPinList =
            CollectionUtil.getPropertyList(itemList, AttMonthDetailReportItem::getPin, "-1");

        // 根据pin号查找对应的人员列表
        List<PersPersonCacheItem> persPersonItemList =
            persPersonCacheService.getPersonCacheByPins((List<String>)personPinList);
        Map<String, PersPersonCacheItem> persPersonItemMap =
            CollectionUtil.listToKeyMap(persPersonItemList, PersPersonCacheItem::getPin);

        // 根据pin号查找对应的离职人员列表
        List<List<String>> personPinsGroup = CollectionUtil.split(personPinList, CollectionUtil.splitSize);
        List<PersLeavePersonItem> persLeavePersonItemList = new ArrayList<>();
        for (List<String> pins : personPinsGroup) {
            List<PersLeavePersonItem> persLeavePersonItems = persLeavePersonService.getItemByPins(pins);
            persLeavePersonItemList.addAll(persLeavePersonItems);
        }
        Map<String, PersLeavePersonItem> leavePersonItemMap =
            CollectionUtil.listToKeyMap(persLeavePersonItemList, PersLeavePersonItem::getPin);

        itemList.forEach(item -> {
            String personPin = item.getPin();
            if (persPersonItemMap.containsKey(personPin)) {
                PersPersonCacheItem persPersonItem = persPersonItemMap.get(personPin);
                item.setName(persPersonItem.getName());
                item.setLastName(persPersonItem.getLastName());
                item.setDeptName(persPersonItem.getDeptName());
                item.setDeptCode(persPersonItem.getDeptCode());
            } else {
                if (leavePersonItemMap.containsKey(personPin)) {
                    PersLeavePersonItem persLeavePersonItem = leavePersonItemMap.get(personPin);
                    item.setName(persLeavePersonItem.getName());
                    item.setLastName(persLeavePersonItem.getLastName());
                    item.setDeptName(persLeavePersonItem.getDeptName());
                    item.setDeptCode(persLeavePersonItem.getDeptCode());
                }
            }
        });
    }

    /**
     * 组装条件过滤
     */
    private void buildCondition(String sessionId, AttMonthDetailReportItem condition) {
        // 人员部门过滤条件组装
        String persperPin = attPersonService.getPinByLoginType(sessionId);
        if (persperPin != null) {
            condition.setEquals(true);
            condition.setPin(persperPin);
        } else {
            // 组装部门权限和姓名模糊搜索条件
            AttConditionBean attConditionBean = new AttConditionBean(ZKConstant.TRUE, condition.getLikeName(),
                condition.getDeptId(), condition.getDeptCode(), condition.getDeptName());
            attPersonService.getConditionBySessionId(sessionId, attConditionBean);
            condition.setInPersonPin(attConditionBean.getInPersonPin());
            condition.setDeptId(attConditionBean.getDeptId());
            condition.setInDeptId(attConditionBean.getInDeptId());
        }
    }
}
