package com.zkteco.zkbiosecurity.att.data.upgrade;

import java.util.ArrayList;
import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * 升级到4.8.0
 *
 */
@Slf4j
@Component
public class AttVer4_8_0 implements UpgradeVersionManager {

    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public String getModule() {
        return BaseConstants.ATT;
    }

    @Override
    public String getVersion() {
        return "v4.8.0";
    }

    @Override
    public boolean executeUpgrade() {

        // 菜单升级
        upgradeMenu();

        // 参数升级
        upgradeParam();

        return true;
    }

    private void upgradeParam() {

        String attMessageNotificationValue = baseSysParamService.getValByName("attMessageNotification");
        if (StringUtils.isBlank(attMessageNotificationValue)) {
            JSONObject notificationValues = new JSONObject();
            notificationValues.put("Email", "1");
            notificationValues.put("SMS", "1");
            notificationValues.put("Whatsapp", "1");
            notificationValues.put("Line", "1");
            BaseSysParamItem attMessageNotification = new BaseSysParamItem("attMessageNotification", notificationValues.toString(), "base_dataClean_attTrans");
            baseSysParamService.initData(attMessageNotification);
        }

        baseSysParamService.initData(new BaseSysParamItem("att.annualLeave.enableCalculateType", "false", "是否启用按工龄月份比例时长计算"));
    }

    private void upgradeMenu() {

        // 考勤模块
        AuthPermissionItem systemMenuItem = authPermissionService.getItemByCode("Att");
        if (systemMenuItem == null) {
            return;
        }

        // **********************新增《考勤日报表》**********************
        AuthPermissionItem attDailyReport = new AuthPermissionItem("AttDailyReport", "att_leftMenu_dailyReport", "att:daily:report",
                AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 7);
        attDailyReport.setParentId(systemMenuItem.getId());
        attDailyReport.setActionLink("attDayDetailReport.do");
        attDailyReport.setImg("att_report.png");
        attDailyReport.setImgHover("att_report_over.png");
        authPermissionService.saveItem(attDailyReport);

        // 《日明细表》移到《考勤日报表》下面
        AuthPermissionItem attDayDetailReport = authPermissionService.getItemByCode("AttDayDetailReport");
        if (attDayDetailReport != null) {
            attDayDetailReport.setParentId(attDailyReport.getId());
            attDayDetailReport.setOrderNo(1);
            authPermissionService.saveItem(attDayDetailReport);
        }

        // 《工作时长表》移到《考勤日报表》下面，并修改请求地址
        AuthPermissionItem attWorkTimeReport = authPermissionService.getItemByCode("AttWorkTimeReport");
        if (attWorkTimeReport != null) {
            attWorkTimeReport.setActionLink("attDayDetailReport.do?indexWorkTimeReport");
            attWorkTimeReport.setParentId(attDailyReport.getId());
            attWorkTimeReport.setOrderNo(2);
            authPermissionService.saveItem(attWorkTimeReport);
        }

        // 补签记录表设置为无效
        setUnavailableByCode("AttSignReport");

        // 新增《加班报表》
        AuthPermissionItem childMenuItem = new AuthPermissionItem("AttOvertimeReport", "att_leftMenu_overtimeReport", "att:overtimeReport",
                AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 3);
        childMenuItem.setParentId(attDailyReport.getId());
        childMenuItem.setActionLink("attDayDetailReport.do?indexOvertimeReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        AuthPermissionItem buttonMenuItem = new AuthPermissionItem("AttOvertimeReportRefresh", "common_op_refresh", "att:overtimeReport:refresh",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        buttonMenuItem = new AuthPermissionItem("AttOvertimeReportExport", "common_op_export", "att:overtimeReport:export",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        // 《请假报表》移到《考勤日报表》下面，并修改请求地址
        AuthPermissionItem attLeaveReport = authPermissionService.getItemByCode("AttLeaveReport");
        if (attLeaveReport != null) {
            attLeaveReport.setActionLink("attDayDetailReport.do?indexLeaveReport");
            attLeaveReport.setParentId(attDailyReport.getId());
            attLeaveReport.setOrderNo(4);
            authPermissionService.saveItem(attLeaveReport);
        }

        // 《出勤异常表》移到《考勤日报表》下面，并修改请求地址
        AuthPermissionItem AttAbnormalReport = authPermissionService.getItemByCode("AttAbnormalReport");
        if (AttAbnormalReport != null) {
            AttAbnormalReport.setActionLink("attDayDetailReport.do?indexAbnormalReport");
            AttAbnormalReport.setParentId(attDailyReport.getId());
            AttAbnormalReport.setOrderNo(5);
            authPermissionService.saveItem(AttAbnormalReport);
        }

        /* ------------- 二级菜单:迟到报表------------*/
        childMenuItem = new AuthPermissionItem("AttLateReport", "att_leftMenu_lateReport", "att:lateReport",
                AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 6);
        childMenuItem.setParentId(attDailyReport.getId());
        childMenuItem.setActionLink("attDayDetailReport.do?indexLateReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        buttonMenuItem = new AuthPermissionItem("AttLateReportRefresh", "common_op_refresh", "att:lateReport:refresh",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        buttonMenuItem = new AuthPermissionItem("AttLateReportExport", "common_op_export", "att:lateReport:export",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:早退报表------------*/
        childMenuItem = new AuthPermissionItem("AttEarlyReport", "att_leftMenu_earlyReport", "att:earlyReport",
                AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 7);
        childMenuItem.setParentId(attDailyReport.getId());
        childMenuItem.setActionLink("attDayDetailReport.do?indexEarlyReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        buttonMenuItem = new AuthPermissionItem("AttEarlyReportRefresh", "common_op_refresh", "att:earlyReport:refresh",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        buttonMenuItem = new AuthPermissionItem("AttEarlyReportExport", "common_op_export", "att:earlyReport:export",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:缺勤报表------------*/
        childMenuItem = new AuthPermissionItem("AttAbsentReport", "att_leftMenu_absentReport", "att:absentReport",
                AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 8);
        childMenuItem.setParentId(attDailyReport.getId());
        childMenuItem.setActionLink("attDayDetailReport.do?indexAbsentReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        buttonMenuItem = new AuthPermissionItem("AttAbsentReportRefresh", "common_op_refresh", "att:absentReport:refresh",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        buttonMenuItem = new AuthPermissionItem("AttAbsentReportExport", "common_op_export", "att:absentReport:export",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);


        // **********************新增《考勤月报表》**********************
        AuthPermissionItem attMonthReport = new AuthPermissionItem("AttMonthReport", "att_leftMenu_monthReport", "att:daily:report",
                AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 8);
        attMonthReport.setParentId(systemMenuItem.getId());
        attMonthReport.setActionLink("attMonthDetailReport.do");
        attMonthReport.setImg("att_report.png");
        attMonthReport.setImgHover("att_report_over.png");
        authPermissionService.saveItem(attMonthReport);

        /* ------------- 二级菜单:月考勤状态表-------------*/
        childMenuItem = new AuthPermissionItem("AttMonthDetailReport", "att_leftMenu_monthDetailReport",
                "att:monthDetailReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 1);
        childMenuItem.setParentId(attMonthReport.getId());
        childMenuItem.setActionLink("attMonthDetailReport.do");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttMonthDetailReportRefresh", "common_op_refresh",
                "att:monthDetailReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttMonthDetailReportExport", "common_op_export",
                "att:monthDetailReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:月工作时长报表-------------*/
        childMenuItem = new AuthPermissionItem("AttMonthWorkTimeReport", "att_leftMenu_monthWorkTimeReport",
                "att:monthWorkTimeReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
        childMenuItem.setParentId(attMonthReport.getId());
        childMenuItem.setActionLink("attMonthDetailReport.do?indexMonthWorkTimeReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttMonthWorkTimeReportRefresh", "common_op_refresh",
                "att:monthWorkTimeReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttMonthWorkTimeReportExport", "common_op_export",
                "att:monthWorkTimeReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:月打卡表-------------*/
        childMenuItem = new AuthPermissionItem("AttMonthCardReport", "att_leftMenu_monthCardReport",
                "att:monthCardReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 3);
        childMenuItem.setParentId(attMonthReport.getId());
        childMenuItem.setActionLink("attMonthDetailReport.do?indexMonthCardReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttMonthCardReportRefresh", "common_op_refresh",
                "att:monthCardReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttMonthCardReportExport", "common_op_export",
                "att:monthCardReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        /* ------------- 二级菜单:月加班表-------------*/
        childMenuItem = new AuthPermissionItem("AttMonthOvertimeReport", "att_leftMenu_monthOvertimeReport",
                "att:monthOvertimeReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 3);
        childMenuItem.setParentId(attMonthReport.getId());
        childMenuItem.setActionLink("attMonthDetailReport.do?indexMonthOvertimeReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttMonthOvertimeReportRefresh", "common_op_refresh",
                "att:monthOvertimeReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttMonthOvertimeReportExport", "common_op_export",
                "att:monthOvertimeReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        // **********************《考勤汇总表》**********************
        AuthPermissionItem attStatisticalReport = authPermissionService.getItemByCode("AttStatisticalReport");

        // 修改排序《人员汇总表》排序1
        updateOrderNo("AttMonthStatisticalReport", 1);

        // 新增《人员加班汇总表》
        childMenuItem = new AuthPermissionItem("AttOvertimeSummaryReport", "att_leftMenu_overtimeSummaryReport",
                "att:overtimeSummaryReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
        childMenuItem.setParentId(attStatisticalReport.getId());
        childMenuItem.setActionLink("attMonthStatisticalReport.do?indexOvertimeSummaryReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttOvertimeSummaryReportRefresh", "common_op_refresh",
                "att:overtimeSummaryReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttOvertimeSummaryReportExport", "common_op_export",
                "att:overtimeSummaryReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        // 修改请假汇总路径
        AuthPermissionItem attLeaveSummaryReport = authPermissionService.getItemByCode("AttLeaveSummaryReport");
        if (attLeaveSummaryReport != null) {
            attLeaveSummaryReport.setActionLink("attMonthStatisticalReport.do?indexLeaveSummaryReport");
            authPermissionService.saveItem(attLeaveSummaryReport);
        }

        // 修改排序《部门汇总表》排序5
        updateOrderNo("AttDeptStatisticalReport", 5);

        // 新增《部门加班汇总表》
        childMenuItem = new AuthPermissionItem("AttDeptOvertimeSummaryReport", "att_leftMenu_deptOvertimeSummaryReport",
                "att:deptOvertimeSummaryReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 6);
        childMenuItem.setParentId(attStatisticalReport.getId());
        childMenuItem.setActionLink("attDeptStatisticalReport.do?indexDeptOvertimeSummaryReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttDeptOvertimeSummaryReportRefresh", "common_op_refresh",
                "att:deptOvertimeSummaryReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttDeptOvertimeSummaryReportExport", "common_op_export",
                "att:deptOvertimeSummaryReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);

        // 新增《部门请假汇总表》
        childMenuItem = new AuthPermissionItem("AttDeptLeaveSummaryReport", "att_leftMenu_deptLeaveSummaryReport",
                "att:deptLeaveSummaryReport", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 7);
        childMenuItem.setParentId(attStatisticalReport.getId());
        childMenuItem.setActionLink("attDeptStatisticalReport.do?indexDeptLeaveSummaryReport");
        childMenuItem = authPermissionService.saveItem(childMenuItem);
        // 刷新
        buttonMenuItem = new AuthPermissionItem("AttDeptLeaveSummaryReportRefresh", "common_op_refresh",
                "att:deptLeaveSummaryReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);
        // 导出
        buttonMenuItem = new AuthPermissionItem("AttDeptLeaveSummaryReportExport", "common_op_export",
                "att:deptLeaveSummaryReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        buttonMenuItem.setParentId(childMenuItem.getId());
        authPermissionService.saveItem(buttonMenuItem);


        // 《年假结余表》移到《考勤汇总报表 》下面
        AuthPermissionItem attAnnualLeaveReport = authPermissionService.getItemByCode("AttAnnualLeaveReport");
        if (attAnnualLeaveReport != null) {
            attAnnualLeaveReport.setParentId(attStatisticalReport.getId());
            attAnnualLeaveReport.setOrderNo(10);
            authPermissionService.saveItem(attAnnualLeaveReport);
        }

        // 修改排序《签到点名表》排序11
        updateOrderNo("AttSignCallRollReport", 11);

        // 修改排序《考勤汇总报表》排序9
        updateOrderNo("AttStatisticalReport", 9);
    }

    private void updateOrderNo(String code, int orderNo) {
        AuthPermissionItem authPermissionItem = authPermissionService.getItemByCode(code);
        if (authPermissionItem != null) {
            authPermissionItem.setOrderNo(orderNo);
            authPermissionService.saveItem(authPermissionItem);
        }
    }

    private void setUnavailableByCode(String code) {
        AuthPermissionItem menu = authPermissionService.getItemByCode(code);
        if (menu != null && ZKConstant.TRUE.equals(menu.getAvailable())) {
            menu.setAvailable(ZKConstant.FALSE);
            authPermissionService.saveItem(menu);
        }
    }
}
