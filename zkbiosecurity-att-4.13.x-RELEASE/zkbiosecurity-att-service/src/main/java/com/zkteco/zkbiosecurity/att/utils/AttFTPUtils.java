package com.zkteco.zkbiosecurity.att.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;

/**
 * ftp工具类
 * 
 * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
 * @date 2019/5/13 16:38
 */
@Slf4j
public class AttFTPUtils {

    private static String LOCAL_CHARSET = "GBK";
    private static String SERVER_CHARSET = "ISO-8859-1";

    /**
     * @param remote
     *            上传到ftp服务器哪个路径下
     * @param addr
     *            地址
     * @param port
     *            端口号
     * @param username
     *            用户名
     * @param password
     *            密码
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/5/13 16:38
     */
    public static FTPClient connect(String remote, String addr, int port, String username, String password)
        throws Exception {
        boolean result = false;
        FTPClient ftp = new FTPClient();
        int reply;
        ftp.connect(addr, port);
        ftp.login(username, password);
        ftp.setFileType(FTPClient.BINARY_FILE_TYPE);

        // 开启服务器对UTF-8的支持，如果服务器支持就用UTF-8编码，否则就使用本地编码（GBK）.
        if (FTPReply.isPositiveCompletion(ftp.sendCommand("OPTS UTF8", "ON"))) {
            LOCAL_CHARSET = "UTF-8";
        }
        ftp.setControlEncoding(LOCAL_CHARSET);

        reply = ftp.getReplyCode();
        if (!FTPReply.isPositiveCompletion(reply)) {
            ftp.disconnect();
            return ftp;
        }

        // 如果远程目录不存在，则递归创建远程服务器目录
        String directory = remote + "/";
        if (!directory.equalsIgnoreCase("/") && !changeWorkingDirectory(directory, ftp)) {
            int start = 0;
            int end = 0;
            if (directory.startsWith("/")) {
                start = 1;
            } else {
                start = 0;
            }
            end = directory.indexOf("/", start);
            StringBuilder path = new StringBuilder();
            while (true) {
                String subDirectory = new String(remote.substring(start, end).getBytes(LOCAL_CHARSET), SERVER_CHARSET);
                path.append("/" + subDirectory);
                if (!existFile(path.toString(), ftp)) {
                    if (makeDirectory(subDirectory, ftp)) {
                        changeWorkingDirectory(subDirectory, ftp);
                    } else {
                        changeWorkingDirectory(subDirectory, ftp);
                    }
                } else {
                    changeWorkingDirectory(subDirectory, ftp);
                }
                start = end + 1;
                end = directory.indexOf("/", start);
                // 检查所有目录是否创建完毕
                if (end <= start) {
                    break;
                }
            }
        }

        result = true;
        return ftp;
    }

    /**
     * 创建目录
     *
     * @param dir
     * @param ftp
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/5/13 16:38
     */
    public static boolean makeDirectory(String dir, FTPClient ftp) {
        boolean flag = true;
        try {
            flag = ftp.makeDirectory(dir);
        } catch (Exception e) {
            log.error("exception = ", e);
        }
        return flag;
    }

    /**
     * 判断ftp服务器文件是否存在
     *
     * @param path
     * @param ftp
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/5/13 16:38
     */
    public static boolean existFile(String path, FTPClient ftp) throws IOException {
        boolean flag = false;
        FTPFile[] ftpFileArr = ftp.listFiles(path);
        if (ftpFileArr.length > 0) {
            flag = true;
        }
        return flag;
    }

    /**
     * 改变目录路径
     *
     * @param directory
     * @param ftp
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/5/13 16:38
     */
    public static boolean changeWorkingDirectory(String directory, FTPClient ftp) {
        boolean flag = true;
        try {
            flag = ftp.changeWorkingDirectory(directory);
        } catch (IOException ioe) {
            ioe.printStackTrace();
        }
        return flag;
    }

    /**
     * 上传的文件或文件夹
     *
     * @param file
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/5/13 16:38
     */
    public static void upload(File file, FTPClient ftp) throws Exception {
        if (file.isDirectory()) {
            ftp.makeDirectory(file.getName());
            ftp.changeWorkingDirectory(file.getName());
            String[] files = file.list();
            if (files != null) {
                for (int i = 0; i < files.length; i++) {
                    File file1 = new File(file.getPath() + "\\" + files[i]);
                    if (file1.isDirectory()) {
                        upload(file1, ftp);
                        ftp.changeToParentDirectory();
                    } else {
                        File file2 = new File(file.getPath() + "\\" + files[i]);
                        if (file2 != null && file2.length() != 0)// 判空
                        {
                            FileInputStream fileInputStream = null;
                            try {
                                fileInputStream = new FileInputStream(file2);
                                ftp.storeFile(new String(file2.getName().getBytes(LOCAL_CHARSET), SERVER_CHARSET),
                                    fileInputStream);
                            } finally {
                                if (null != fileInputStream) {
                                    try {
                                        fileInputStream.close();
                                    } catch (IOException e) {
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } else {
            File file2 = new File(file.getPath());
            if (file2 != null && file2.length() != 0)// 判空
            {
                FileInputStream fileInputStream = null;
                try {
                    fileInputStream = new FileInputStream(file2);
                    ftp.storeFile(new String(file2.getName().getBytes(LOCAL_CHARSET), SERVER_CHARSET), fileInputStream);
                } finally {
                    if (null != fileInputStream) {
                        try {
                            fileInputStream.close();
                        } catch (IOException e) {
                        }
                    }
                }

            }
        }
    }
}
