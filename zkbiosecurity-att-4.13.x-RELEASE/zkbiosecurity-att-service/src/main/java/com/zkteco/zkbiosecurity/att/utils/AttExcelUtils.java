package com.zkteco.zkbiosecurity.att.utils;

import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.LocaleMessageSourceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 生成excel文件
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 14:04 2019/6/6
 */
@Slf4j
public class AttExcelUtils {

    public static void write(List<AttTransactionItem> attTransactionList, String dateTimeFormat, String fileName,
        OutputStream outputStream, Date startDate, Date endDate) {

        // 初始化工作簿为 HSSFWorkbook
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 当前工作表索引
        int sheetIndex = 0;
        // 当前行索引
        int rowIndex = 0;
        // 每个工作表最大行数
        int maxRowsPerSheet = 65535;
        // 创建第一个工作表
        String sheetName = fileName;
        if (sheetName.length() > 25) {
            sheetName = fileName.substring(0, 25);
        }
        HSSFSheet sheet = workbook.createSheet(sheetName + "_" + sheetIndex);

        // 定义表头
        List<String> columnList = new ArrayList<>();
        columnList.add(I18nUtil.i18nCode("pers_dept_deptNo"));
        columnList.add(I18nUtil.i18nCode("pers_dept_deptName"));
        columnList.add(I18nUtil.i18nCode("att_person_pin"));
        columnList.add(I18nUtil.i18nCode("att_person_name"));
        String language = LocaleMessageSourceUtil.language;
        if (!language.equals("zh_CN")) {
            columnList.add(I18nUtil.i18nCode("att_person_lastName"));
        }
        columnList.add(I18nUtil.i18nCode("common_dev_sn"));
        columnList.add(I18nUtil.i18nCode("common_dev_name"));
        columnList.add(I18nUtil.i18nCode("att_attPoint_name"));
        columnList.add(I18nUtil.i18nCode("pers_person_attArea"));
        columnList.add(I18nUtil.i18nCode("att_statistical_attDatetime"));
        // columnList.add(I18nUtil.i18nCode("common_verifyMode_entiy"));
        columnList.add(I18nUtil.i18nCode("att_statistical_dataSources"));

        // 设置标题
        HSSFRow row = sheet.createRow(rowIndex++);
        String cellValue = I18nUtil.i18nCode("att_leftMenu_transaction");
        setTitle(row, cellValue, columnList.size());

        // 起止时间
        row = sheet.createRow(rowIndex++);
        cellValue = I18nUtil.i18nCode("att_autoExport_startEndTime") + ": " + AttDateUtils.dateToStrAsLong(startDate)
            + " - " + AttDateUtils.dateToStrAsLong(endDate);
        setTime(row, cellValue, columnList.size());

        // 创建时间
        row = sheet.createRow(rowIndex++);
        cellValue = I18nUtil.i18nCode("pers_person_createTime") + ": " + AttDateUtils.dateToStrAsLong(new Date());
        setTime(row, cellValue, columnList.size());

        // 设置列名（部门编号 部门名称 人员编号 姓名 姓氏(海外) 设备序列号 设备名称 考勤点名称 考勤区域 考勤日期时间 验证方式 数据来源）
        row = sheet.createRow(rowIndex++);
        setColumn(row, columnList);

        // 固定标题、表头不滚动
        sheet.createFreezePane(columnList.size(), rowIndex);

        // 创建数据行（（部门编号 部门名称 人员编号 姓名 姓氏(海外) 设备序列号 设备名称 考勤点名称 考勤区域 考勤日期时间 验证方式 数据来源）
        for (int i = 0, size = attTransactionList.size(); i < size; i++) {

            if (rowIndex >= maxRowsPerSheet) { // 超过最大行数时创建新工作表
                sheetIndex++;
                sheet = workbook.createSheet(sheetName + "_" + sheetIndex);
                rowIndex = 0;
                // 重新创建表头
                row = sheet.createRow(rowIndex++);
                setColumn(row, columnList);
                // 冻结窗格
                sheet.createFreezePane(columnList.size(), rowIndex);
            }

            row = sheet.createRow(rowIndex++);
            row.setHeight((short)400);
            AttTransactionItem attTransactionItem = attTransactionList.get(i);
            List<String> rowData = new ArrayList<>();
            rowData.add(attTransactionItem.getDeptCode());
            rowData.add(attTransactionItem.getDeptName());
            rowData.add(attTransactionItem.getPersonPin());
            rowData.add(attTransactionItem.getPersonName());
            if (!language.equals("zh_CN")) {
                rowData.add(attTransactionItem.getPersonLastName());
            }
            rowData.add(attTransactionItem.getDeviceSn());
            rowData.add(attTransactionItem.getDeviceName());
            rowData.add(attTransactionItem.getPointName());
            rowData.add(attTransactionItem.getAreaName());
            rowData.add(DateUtil.dateToString(attTransactionItem.getAttDatetime(), dateTimeFormat));
            // rowData.add(attTransactionItem.getAttVerify());
            rowData.add(attTransactionItem.getMark());
            for (int cellIndex = 0; cellIndex < rowData.size(); cellIndex++) {
                HSSFCell cell = row.createCell(cellIndex);
                cell.setCellStyle(getDataCellStyle(workbook));
                cell.setCellValue(rowData.get(cellIndex));
            }
        }

        try {
            workbook.write(outputStream);
            workbook.close();
        } catch (IOException e) {
            log.error("exception = ", e);
        }
    }

    public static void writeDayCardDetail(List<AttDayCardDetailItem> attDayCardDetailItemList, String dataFormat,
        String fileName, OutputStream outputStream, Date startDate, Date endDate) {

        // 初始化工作簿为 HSSFWorkbook
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 当前工作表索引
        int sheetIndex = 0;
        // 当前行索引
        int rowIndex = 0;
        // 每个工作表最大行数
        int maxRowsPerSheet = 65535;
        // 创建第一个工作表
        String sheetName = fileName;
        if (sheetName.length() > 25) {
            sheetName = fileName.substring(0, 25);
        }
        HSSFSheet sheet = workbook.createSheet(sheetName + "_" + sheetIndex);

        // 定义表头（部门编号 部门名称 人员编号 姓名 姓氏(海外) 打卡日期 最早时间 最晚时间 打卡时间）
        List<String> columnList = new ArrayList<>();
        columnList.add(I18nUtil.i18nCode("pers_dept_deptNo"));
        columnList.add(I18nUtil.i18nCode("pers_dept_deptName"));
        columnList.add(I18nUtil.i18nCode("att_person_pin"));
        columnList.add(I18nUtil.i18nCode("att_person_name"));
        String language = LocaleMessageSourceUtil.language;
        if (!language.equals("zh_CN")) {
            columnList.add(I18nUtil.i18nCode("att_person_lastName"));
        }
        columnList.add(I18nUtil.i18nCode("att_statistical_cardDate"));
        columnList.add(I18nUtil.i18nCode("att_statistical_earliestTime"));
        columnList.add(I18nUtil.i18nCode("att_statistical_latestTime"));
        columnList.add(I18nUtil.i18nCode("att_statistical_cardTime"));

        // 设置标题
        HSSFRow row = sheet.createRow(rowIndex++);
        String cellValue = I18nUtil.i18nCode("att_leftMenu_dayCardDetailReport");
        setTitle(row, cellValue, columnList.size());

        // 起止时间
        row = sheet.createRow(rowIndex++);
        cellValue = I18nUtil.i18nCode("att_autoExport_startEndTime") + ": " + AttDateUtils.dateToStrAsLong(startDate)
            + " - " + AttDateUtils.dateToStrAsLong(endDate);
        setTime(row, cellValue, columnList.size());

        // 创建时间
        row = sheet.createRow(rowIndex++);
        cellValue = I18nUtil.i18nCode("pers_person_createTime") + ": " + AttDateUtils.dateToStrAsLong(new Date());
        setTime(row, cellValue, columnList.size());

        // 设置列名（部门编号 部门名称 人员编号 姓名 姓氏(海外) 设备序列号 设备名称 考勤点名称 考勤区域 考勤日期时间）
        row = sheet.createRow(rowIndex++);
        setColumn(row, columnList);

        // 固定标题、表头不滚动
        sheet.createFreezePane(columnList.size(), rowIndex);

        // 创建数据行（（部门编号 部门名称 人员编号 姓名 姓氏(海外) 打卡日期 最早时间 最晚时间 打卡时间）
        for (int i = 0, size = attDayCardDetailItemList.size(); i < size; i++) {

            if (rowIndex >= maxRowsPerSheet) { // 超过最大行数时创建新工作表
                sheetIndex++;
                sheet = workbook.createSheet(sheetName + "_" + sheetIndex);
                rowIndex = 0;
                // 重新创建表头
                row = sheet.createRow(rowIndex++);
                setColumn(row, columnList);
                // 冻结窗格
                sheet.createFreezePane(columnList.size(), rowIndex);
            }

            row = sheet.createRow(rowIndex++);
            row.setHeight((short)400);
            AttDayCardDetailItem AttDayCardDetailItem = attDayCardDetailItemList.get(i);
            List<String> rowData = new ArrayList<>();
            rowData.add(AttDayCardDetailItem.getDeptCode());
            rowData.add(AttDayCardDetailItem.getDeptName());
            rowData.add(AttDayCardDetailItem.getPersonPin());
            rowData.add(AttDayCardDetailItem.getPersonName());
            if (!language.equals("zh_CN")) {
                rowData.add(AttDayCardDetailItem.getPersonLastName());
            }
            rowData.add(AttDayCardDetailItem.getAttDate());
            rowData.add(AttDayCardDetailItem.getEarliestTime());
            rowData.add(AttDayCardDetailItem.getLatestTime());
            rowData.add(AttDayCardDetailItem.getAttTimes());
            for (int cellIndex = 0; cellIndex < rowData.size(); cellIndex++) {
                HSSFCell cell = row.createCell(cellIndex);
                cell.setCellValue(rowData.get(cellIndex));
            }
        }

        try {
            workbook.write(outputStream);
            workbook.close();
        } catch (IOException e) {
            log.error("exception = ", e);
        }
    }

    public static void writeDayDetail(List<AttDayDetailReportItem> attDayDetailReportItemList, String dataFormat,
        String fileName, OutputStream outputStream, Date startDate, Date endDate,
        List<AttLeaveTypeItem> attLeaveTypeItemList) {

        // 考勤规则单位配置
        Map<String, AttLeaveTypeItem> attLeaveTypeNoMap =
            CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getLeaveTypeNo);
        String actualUnit =
            AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.ACTUAL));
        String lateUnit = AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.LATE));
        String earlyUnit = AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.EARLY));
        String overtimeUnit =
            AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.OVERTIME));
        String absentUnit =
            AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.ABSENT));

        // 初始化工作簿为 HSSFWorkbook
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 当前工作表索引
        int sheetIndex = 0;
        // 当前行索引
        int rowIndex = 0;
        // 每个工作表最大行数
        int maxRowsPerSheet = 65535;
        // 创建第一个工作表
        String sheetName = fileName;
        if (sheetName.length() > 25) {
            sheetName = fileName.substring(0, 25);
        }
        HSSFSheet sheet = workbook.createSheet(sheetName + "_" + sheetIndex);

        List<String> columnList = new ArrayList<>();
        columnList.add(I18nUtil.i18nCode("pers_dept_deptNo"));
        columnList.add(I18nUtil.i18nCode("pers_dept_deptName"));
        columnList.add(I18nUtil.i18nCode("att_person_pin"));
        columnList.add(I18nUtil.i18nCode("att_person_name"));
        String language = LocaleMessageSourceUtil.language;
        if (!language.equals("zh_CN")) {
            columnList.add(I18nUtil.i18nCode("att_person_lastName"));
        }
        columnList.add(I18nUtil.i18nCode("att_statistical_attDate"));
        columnList.add(I18nUtil.i18nCode("att_shift_timeSlotDetail"));
        columnList.add(I18nUtil.i18nCode("att_statistical_should") + "(" + actualUnit + ")");
        columnList.add(I18nUtil.i18nCode("att_statistical_actual") + "(" + actualUnit + ")");
        columnList.add(I18nUtil.i18nCode("att_statistical_valid") + "(" + actualUnit + ")");

        columnList.add(I18nUtil.i18nCode("att_common_late") + "(" + lateUnit + ")");
        columnList.add(I18nUtil.i18nCode("att_common_early") + "(" + earlyUnit + ")");
        columnList.add(I18nUtil.i18nCode("att_common_overtime") + "(" + overtimeUnit + ")");
        columnList.add(I18nUtil.i18nCode("att_common_absent") + "(" + absentUnit + ")");

        // 请假动态表头
        if (attDayDetailReportItemList != null && attDayDetailReportItemList.size() > 0) {
            Map<String, Object> attDayDetailReportLeaveMinuteMap =
                attDayDetailReportItemList.get(0).getAttDayDetailReportLeaveMinuteMap();
            Map<String, AttLeaveTypeItem> attLeaveTypeNameMap =
                CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getLeaveTypeName);
            for (Map.Entry<String, Object> leaveEntry : attDayDetailReportLeaveMinuteMap.entrySet()) {
                String unit = AttCommonUtils.getUnit(attLeaveTypeNameMap.get(leaveEntry.getKey()));
                columnList.add(leaveEntry.getKey() + "(" + unit + ")");
            }
        }

        // 设置标题
        HSSFRow row = sheet.createRow(rowIndex++);
        String cellValue = I18nUtil.i18nCode("att_leftMenu_dayDetailReport");
        setTitle(row, cellValue, columnList.size());

        // 起止时间
        row = sheet.createRow(rowIndex++);
        cellValue = I18nUtil.i18nCode("att_autoExport_startEndTime") + ": " + AttDateUtils.dateToStrAsLong(startDate)
            + " - " + AttDateUtils.dateToStrAsLong(endDate);
        setTime(row, cellValue, columnList.size());

        // 创建时间
        row = sheet.createRow(rowIndex++);
        cellValue = I18nUtil.i18nCode("pers_person_createTime") + ": " + AttDateUtils.dateToStrAsLong(new Date());
        setTime(row, cellValue, columnList.size());

        // 设置列名（部门编号 部门名称 人员编号 姓名 姓氏(海外) 设备序列号 设备名称 考勤点名称 考勤区域 考勤日期时间）
        row = sheet.createRow(rowIndex++);
        setColumn(row, columnList);

        // 固定标题、表头不滚动
        sheet.createFreezePane(columnList.size(), rowIndex);

        // 创建数据行（（部门编号 部门名称 人员编号 姓名 姓氏(海外) 打卡日期 最早时间 最晚时间 打卡时间）
        for (int i = 0, size = attDayDetailReportItemList.size(); i < size; i++) {

            if (rowIndex >= maxRowsPerSheet) { // 超过最大行数时创建新工作表
                sheetIndex++;
                sheet = workbook.createSheet(sheetName + "_" + sheetIndex);
                rowIndex = 0;
                // 重新创建表头
                row = sheet.createRow(rowIndex++);
                setColumn(row, columnList);
                // 冻结窗格
                sheet.createFreezePane(columnList.size(), rowIndex);
            }

            row = sheet.createRow(rowIndex++);
            row.setHeight((short)400);
            AttDayDetailReportItem attDayDetailReportItem = attDayDetailReportItemList.get(i);
            List<String> rowData = new ArrayList<>();
            rowData.add(attDayDetailReportItem.getDeptCode());
            rowData.add(attDayDetailReportItem.getDeptName());
            rowData.add(attDayDetailReportItem.getPersonPin());
            rowData.add(attDayDetailReportItem.getPersonName());
            if (!language.equals("zh_CN")) {
                rowData.add(attDayDetailReportItem.getPersonLastName());
            }
            rowData.add(attDayDetailReportItem.getAttDateStr());
            rowData.add(attDayDetailReportItem.getShiftName());
            rowData.add(attDayDetailReportItem.getShouldConvert());
            rowData.add(attDayDetailReportItem.getActualConvert());
            rowData.add(attDayDetailReportItem.getValidConvert());
            rowData.add(attDayDetailReportItem.getLateMinuteTotalConvert());
            rowData.add(attDayDetailReportItem.getEarlyMinuteTotalConvert());
            rowData.add(attDayDetailReportItem.getOvertimeMinuteConvert());
            rowData.add(attDayDetailReportItem.getAbsentConvert());
            Map<String, Object> attDayDetailReportLeaveMinuteMap =
                attDayDetailReportItem.getAttDayDetailReportLeaveMinuteMap();
            for (Map.Entry<String, Object> leaveEntry : attDayDetailReportLeaveMinuteMap.entrySet()) {
                rowData.add(leaveEntry.getValue() + "");
            }

            for (int cellIndex = 0; cellIndex < rowData.size(); cellIndex++) {
                HSSFCell cell = row.createCell(cellIndex);
                cell.setCellValue(rowData.get(cellIndex));
            }
        }

        try {
            workbook.write(outputStream);
            workbook.close();
        } catch (IOException e) {
            log.error("exception = ", e);
        }
    }

    public static void writeAbnormal(List<AttDayDetailReportItem> attDayDetailReportItemList, String dataFormat,
        String fileName, OutputStream outputStream, Date startDate, Date endDate,
        List<AttLeaveTypeItem> attLeaveTypeItemList) {
        // 考勤规则单位配置
        Map<String, AttLeaveTypeItem> attLeaveTypeNoMap =
            CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getLeaveTypeNo);
        String lateUnit = AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.LATE));
        String earlyUnit = AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.EARLY));
        String absentUnit =
            AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.ABSENT));

        // 初始化工作簿为 HSSFWorkbook
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 当前工作表索引
        int sheetIndex = 0;
        // 当前行索引
        int rowIndex = 0;
        // 每个工作表最大行数
        int maxRowsPerSheet = 65535;
        // 创建第一个工作表
        String sheetName = fileName;
        if (sheetName.length() > 25) {
            sheetName = fileName.substring(0, 25);
        }
        HSSFSheet sheet = workbook.createSheet(sheetName + "_" + sheetIndex);

        List<String> columnList = new ArrayList<>();
        columnList.add(I18nUtil.i18nCode("att_person_pin"));
        columnList.add(I18nUtil.i18nCode("att_person_name"));
        String language = LocaleMessageSourceUtil.language;
        if (!language.equals("zh_CN")) {
            columnList.add(I18nUtil.i18nCode("att_person_lastName"));
        }
        columnList.add(I18nUtil.i18nCode("pers_dept_deptName"));
        columnList.add(I18nUtil.i18nCode("att_statistical_attDate"));
        columnList.add(I18nUtil.i18nCode("att_shift_timeSlotDetail"));
        columnList.add(I18nUtil.i18nCode("att_common_late") + "(" + lateUnit + ")");
        columnList.add(I18nUtil.i18nCode("att_common_early") + "(" + earlyUnit + ")");
        columnList.add(I18nUtil.i18nCode("att_common_absent") + "(" + absentUnit + ")");

        // 设置标题
        HSSFRow row = sheet.createRow(rowIndex++);
        String cellValue = I18nUtil.i18nCode("att_leftMenu_abnormal");
        setTitle(row, cellValue, columnList.size());

        // 起止时间
        row = sheet.createRow(rowIndex++);
        cellValue = I18nUtil.i18nCode("att_autoExport_startEndTime") + ": " + AttDateUtils.dateToStrAsLong(startDate)
            + " - " + AttDateUtils.dateToStrAsLong(endDate);
        setTime(row, cellValue, columnList.size());

        // 创建时间
        row = sheet.createRow(rowIndex++);
        cellValue = I18nUtil.i18nCode("pers_person_createTime") + ": " + AttDateUtils.dateToStrAsLong(new Date());
        setTime(row, cellValue, columnList.size());

        // 设置列名（部门编号 部门名称 人员编号 姓名 姓氏(海外) 设备序列号 设备名称 考勤点名称 考勤区域 考勤日期时间）
        row = sheet.createRow(rowIndex++);
        setColumn(row, columnList);

        // 固定标题、表头不滚动
        sheet.createFreezePane(columnList.size(), rowIndex);
        // 创建数据行（（部门编号 部门名称 人员编号 姓名 姓氏(海外) 打卡日期 最早时间 最晚时间 打卡时间）
        for (int i = 0, size = attDayDetailReportItemList.size(); i < size; i++) {
            if (rowIndex >= maxRowsPerSheet) { // 超过最大行数时创建新工作表
                sheetIndex++;
                sheet = workbook.createSheet(sheetName + "_" + sheetIndex);
                rowIndex = 0;
                // 重新创建表头
                row = sheet.createRow(rowIndex++);
                setColumn(row, columnList);
                // 冻结窗格
                sheet.createFreezePane(columnList.size(), rowIndex);
            }

            row = sheet.createRow(rowIndex++);
            row.setHeight((short)400);
            AttDayDetailReportItem attDayDetailReportItem = attDayDetailReportItemList.get(i);
            List<String> rowData = new ArrayList<>();

            rowData.add(attDayDetailReportItem.getPersonPin());
            rowData.add(attDayDetailReportItem.getPersonName());
            if (!language.equals("zh_CN")) {
                rowData.add(attDayDetailReportItem.getPersonLastName());
            }
            rowData.add(attDayDetailReportItem.getDeptName());
            rowData.add(attDayDetailReportItem.getAttDateStr());
            rowData.add(attDayDetailReportItem.getShiftName());
            rowData.add(attDayDetailReportItem.getLateMinuteTotalConvert());
            rowData.add(attDayDetailReportItem.getEarlyMinuteTotalConvert());
            rowData.add(attDayDetailReportItem.getAbsentConvert());
            for (int cellIndex = 0; cellIndex < rowData.size(); cellIndex++) {
                HSSFCell cell = row.createCell(cellIndex);
                cell.setCellValue(rowData.get(cellIndex));
            }
        }
        try {
            workbook.write(outputStream);
            workbook.close();
        } catch (IOException e) {
            log.error("exception = ", e);
        }
    }

    public static void writeMonthDetail(List<AttMonthDetailReportItem> attMonthDetailReportItemList, String dataFormat,
        String fileName, OutputStream outputStream, Date startDate, Date endDate) {

        // 初始化工作簿为 HSSFWorkbook
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 当前工作表索引
        int sheetIndex = 0;
        // 当前行索引
        int rowIndex = 0;
        // 每个工作表最大行数
        int maxRowsPerSheet = 65535;
        // 创建第一个工作表
        String sheetName = fileName;
        if (sheetName.length() > 25) {
            sheetName = fileName.substring(0, 25);
        }
        HSSFSheet sheet = workbook.createSheet(sheetName + "_" + sheetIndex);

        List<String> columnList = new ArrayList<>();
        columnList.add(I18nUtil.i18nCode("att_person_pin"));
        columnList.add(I18nUtil.i18nCode("att_person_name"));
        String language = LocaleMessageSourceUtil.language;
        if (!language.equals("zh_CN")) {
            columnList.add(I18nUtil.i18nCode("att_person_lastName"));
        }
        columnList.add(I18nUtil.i18nCode("pers_dept_deptName"));

        // 请假动态表头
        if (attMonthDetailReportItemList != null && attMonthDetailReportItemList.size() > 0) {
            Map<String, Object> monthDetailMap = attMonthDetailReportItemList.get(0).getMonthDetailMap();
            for (Map.Entry<String, Object> entry : monthDetailMap.entrySet()) {
                columnList.add(entry.getKey());
            }
        }

        // 设置标题
        HSSFRow row = sheet.createRow(rowIndex++);
        String cellValue = I18nUtil.i18nCode("att_leftMenu_monthDetailReport");
        setTitle(row, cellValue, columnList.size());
        // 起止时间
        row = sheet.createRow(rowIndex++);
        cellValue = I18nUtil.i18nCode("att_autoExport_startEndTime") + ": " + AttDateUtils.dateToStrAsLong(startDate)
            + " - " + AttDateUtils.dateToStrAsLong(endDate);
        setTime(row, cellValue, columnList.size());
        // 创建时间
        row = sheet.createRow(rowIndex++);
        cellValue = I18nUtil.i18nCode("pers_person_createTime") + ": " + AttDateUtils.dateToStrAsLong(new Date());
        setTime(row, cellValue, columnList.size());

        // 设置列名
        row = sheet.createRow(rowIndex++);
        setColumn(row, columnList);

        // 固定标题、表头不滚动
        sheet.createFreezePane(columnList.size(), rowIndex);

        // 创建数据行
        for (int i = 0, size = attMonthDetailReportItemList.size(); i < size; i++) {

            if (rowIndex >= maxRowsPerSheet) { // 超过最大行数时创建新工作表
                sheetIndex++;
                sheet = workbook.createSheet(sheetName + "_" + sheetIndex);
                rowIndex = 0;
                // 重新创建表头
                row = sheet.createRow(rowIndex++);
                setColumn(row, columnList);
                // 冻结窗格
                sheet.createFreezePane(columnList.size(), rowIndex);
            }

            row = sheet.createRow(rowIndex++);
            row.setHeight((short)400);
            AttMonthDetailReportItem item = attMonthDetailReportItemList.get(i);
            List<String> rowData = new ArrayList<>();

            rowData.add(item.getPin());
            rowData.add(item.getName());
            if (!language.equals("zh_CN")) {
                rowData.add(item.getLastName());
            }
            rowData.add(item.getDeptName());

            Map<String, Object> monthDetailMap = item.getMonthDetailMap();
            for (Map.Entry<String, Object> leaveEntry : monthDetailMap.entrySet()) {
                rowData.add(leaveEntry.getValue() + "");
            }

            for (int cellIndex = 0; cellIndex < rowData.size(); cellIndex++) {
                HSSFCell cell = row.createCell(cellIndex);
                cell.setCellValue(rowData.get(cellIndex));
            }
        }

        try {
            workbook.write(outputStream);
            workbook.close();
        } catch (IOException e) {
            log.error("exception = ", e);
        }
    }

    public static void writeMonthStatistical(List<AttMonthStatisticalReportItem> monthStatisticalReportList,
        String dataFormat, String fileName, OutputStream outputStream, Date startDate, Date endDate,
        List<AttLeaveTypeItem> attLeaveTypeItemList) {

        // 考勤规则单位配置
        Map<String, AttLeaveTypeItem> attLeaveTypeNoMap =
            CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getLeaveTypeNo);
        String actualUnit =
            AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.ACTUAL));
        String lateUnit = AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.LATE));
        String earlyUnit = AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.EARLY));
        String overtimeUnit =
            AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.OVERTIME));
        String absentUnit =
            AttCommonUtils.getUnit(attLeaveTypeNoMap.get(AttCalculationConstant.AttAttendStatus.ABSENT));

        // 初始化工作簿为 HSSFWorkbook
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 当前工作表索引
        int sheetIndex = 0;
        // 当前行索引
        int rowIndex = 0;
        // 每个工作表最大行数
        int maxRowsPerSheet = 65535;
        // 创建第一个工作表
        String sheetName = fileName;
        if (sheetName.length() > 25) {
            sheetName = fileName.substring(0, 25);
        }
        HSSFSheet sheet = workbook.createSheet(sheetName + "_" + sheetIndex);

        List<String> columnList = new ArrayList<>();
        columnList.add(I18nUtil.i18nCode("att_person_pin"));
        columnList.add(I18nUtil.i18nCode("att_person_name"));
        String language = LocaleMessageSourceUtil.language;
        if (!language.equals("zh_CN")) {
            columnList.add(I18nUtil.i18nCode("att_person_lastName"));
        }
        columnList.add(I18nUtil.i18nCode("pers_dept_deptName"));
        columnList.add(I18nUtil.i18nCode("att_statistical_should") + "(" + actualUnit + ")");
        columnList.add(I18nUtil.i18nCode("att_statistical_actual") + "(" + actualUnit + ")");
        columnList.add(I18nUtil.i18nCode("att_statistical_valid") + "(" + actualUnit + ")");

        columnList.add(I18nUtil.i18nCode("att_common_late") + "(" + lateUnit + ")");
        columnList.add(I18nUtil.i18nCode("att_common_early") + "(" + earlyUnit + ")");
        columnList.add(I18nUtil.i18nCode("att_common_overtime") + "(" + overtimeUnit + ")");
        columnList.add(I18nUtil.i18nCode("att_common_absent") + "(" + absentUnit + ")");

        // 请假动态表头
        if (monthStatisticalReportList != null && monthStatisticalReportList.size() > 0) {
            Map<String, Object> attMonthDetailReportLeaveHourMap =
                monthStatisticalReportList.get(0).getAttMonthDetailReportLeaveHourMap();
            Map<String, AttLeaveTypeItem> attLeaveTypeNameMap =
                CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getLeaveTypeName);
            for (Map.Entry<String, Object> leaveEntry : attMonthDetailReportLeaveHourMap.entrySet()) {
                String unit = AttCommonUtils.getUnit(attLeaveTypeNameMap.get(leaveEntry.getKey()));
                columnList.add(leaveEntry.getKey() + "(" + unit + ")");
            }
        }

        // 设置标题
        HSSFRow row = sheet.createRow(rowIndex++);
        String cellValue = I18nUtil.i18nCode("att_leftMenu_monthStatisticalReport");
        setTitle(row, cellValue, columnList.size());

        // 起止时间
        row = sheet.createRow(rowIndex++);
        cellValue = I18nUtil.i18nCode("att_autoExport_startEndTime") + ": " + AttDateUtils.dateToStrAsLong(startDate)
            + " - " + AttDateUtils.dateToStrAsLong(endDate);
        setTime(row, cellValue, columnList.size());

        // 创建时间
        row = sheet.createRow(rowIndex++);
        cellValue = I18nUtil.i18nCode("pers_person_createTime") + ": " + AttDateUtils.dateToStrAsLong(new Date());
        setTime(row, cellValue, columnList.size());

        // 设置列名（部门编号 部门名称 人员编号 姓名 姓氏(海外) 设备序列号 设备名称 考勤点名称 考勤区域 考勤日期时间）
        row = sheet.createRow(rowIndex++);
        setColumn(row, columnList);

        // 固定标题、表头不滚动
        sheet.createFreezePane(columnList.size(), rowIndex);

        // 创建数据行（（部门编号 部门名称 人员编号 姓名 姓氏(海外) 打卡日期 最早时间 最晚时间 打卡时间）
        for (int i = 0, size = monthStatisticalReportList.size(); i < size; i++) {

            if (rowIndex >= maxRowsPerSheet) { // 超过最大行数时创建新工作表
                sheetIndex++;
                sheet = workbook.createSheet(sheetName + "_" + sheetIndex);
                rowIndex = 0;
                // 重新创建表头
                row = sheet.createRow(rowIndex++);
                setColumn(row, columnList);
                // 冻结窗格
                sheet.createFreezePane(columnList.size(), rowIndex);
            }

            row = sheet.createRow(rowIndex++);
            row.setHeight((short)400);
            AttMonthStatisticalReportItem item = monthStatisticalReportList.get(i);
            List<String> rowData = new ArrayList<>();

            rowData.add(item.getPin());
            rowData.add(item.getName());
            if (!language.equals("zh_CN")) {
                rowData.add(item.getLastName());
            }
            rowData.add(item.getDeptName());
            rowData.add(item.getShouldHour());
            rowData.add(item.getActualHour());
            rowData.add(item.getValidHour());
            rowData.add(item.getLateMinute());
            rowData.add(item.getEarlyMinute());
            rowData.add(item.getOvertimeHour());
            rowData.add(item.getAbsentHour());
            Map<String, Object> attMonthDetailReportLeaveHourMap = item.getAttMonthDetailReportLeaveHourMap();
            for (Map.Entry<String, Object> leaveEntry : attMonthDetailReportLeaveHourMap.entrySet()) {
                rowData.add(leaveEntry.getValue() + "");
            }

            for (int cellIndex = 0; cellIndex < rowData.size(); cellIndex++) {
                HSSFCell cell = row.createCell(cellIndex);
                cell.setCellValue(rowData.get(cellIndex));
            }
        }

        try {
            workbook.write(outputStream);
            workbook.close();
        } catch (IOException e) {
            log.error("exception = ", e);
        }
    }

    /**
     * 设置标题样式，填充数据
     *
     * @param row:
     * @param cellValue:
     * @param cellSize:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/25 15:56
     * @since 1.0.0
     */
    private static void setTitle(HSSFRow row, String cellValue, int cellSize) {
        row.setHeight((short)800);

        HSSFSheet sheet = row.getSheet();
        HSSFWorkbook workbook = sheet.getWorkbook();
        CellStyle cellStyle = workbook.createCellStyle();
        Font headFont = workbook.createFont();
        headFont.setFontName("ARIAL");
        headFont.setFontHeightInPoints((short)18);
        headFont.setBold(true);
        cellStyle.setFont(headFont);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        HSSFCell cell = row.createCell(0);
        cell.setCellStyle(cellStyle);
        cell.setCellValue(cellValue);
        sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(), row.getRowNum(), 0, cellSize - 1));
    }

    /**
     * 设置起止时间和创建时间样式，填充数据
     *
     * @param row:
     * @param cellValue:
     * @param cellSize:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/25 15:57
     * @since 1.0.0
     */
    private static void setTime(HSSFRow row, String cellValue, int cellSize) {

        row.setHeight((short)500);

        HSSFSheet sheet = row.getSheet();
        HSSFWorkbook workbook = sheet.getWorkbook();

        CellStyle cellStyle = workbook.createCellStyle();
        Font headFont = workbook.createFont();
        headFont.setFontName("ARIAL");
        headFont.setFontHeightInPoints((short)15);
        // headFont.setBold(true);
        cellStyle.setFont(headFont);
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        HSSFCell cell = row.createCell(0);
        cell.setCellStyle(cellStyle);
        cell.setCellValue(cellValue);
        sheet.addMergedRegion(new CellRangeAddress(row.getRowNum(), row.getRowNum(), 0, cellSize - 1));
    }

    /**
     * 设置表头样式，填充数据
     *
     * @param row:
     * @param rowTitle:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/25 15:57
     * @since 1.0.0
     */
    private static void setColumn(HSSFRow row, List<String> rowTitle) {
        row.setHeight((short)500);

        HSSFSheet sheet = row.getSheet();
        HSSFWorkbook workbook = sheet.getWorkbook();

        CellStyle cellStyle = workbook.createCellStyle();
        Font headFont = workbook.createFont();
        headFont.setFontName("ARIAL");
        headFont.setFontHeightInPoints((short)15);
        headFont.setBold(true);
        cellStyle.setFont(headFont);
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setFillBackgroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        for (int cellIndex = 0; cellIndex < rowTitle.size(); cellIndex++) {
            sheet.setColumnWidth(cellIndex, 6000);
            HSSFCell cell = row.createCell(cellIndex);
            cell.setCellStyle(cellStyle);
            cell.setCellValue(rowTitle.get(cellIndex));
        }
    }

    /**
     * 获取数据格式
     *
     * @param workbook:
     * @return org.apache.poi.ss.usermodel.CellStyle
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/25 15:57
     * @since 1.0.0
     */
    private static CellStyle getDataCellStyle(Workbook workbook) {
        CellStyle dataStyle = workbook.createCellStyle();
        Font headFont = workbook.createFont();
        headFont.setFontName("ARIAL");
        headFont.setFontHeightInPoints((short)12);
        dataStyle.setFont(headFont);
        dataStyle.setWrapText(true);
        dataStyle.setAlignment(HorizontalAlignment.LEFT);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        return dataStyle;
    }

}
