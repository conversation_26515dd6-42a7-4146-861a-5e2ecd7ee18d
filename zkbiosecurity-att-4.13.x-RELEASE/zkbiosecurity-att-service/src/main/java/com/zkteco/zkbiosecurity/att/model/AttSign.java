package com.zkteco.zkbiosecurity.att.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * 补签
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 15:52
 * @since 1.0.0
 */
@Entity
@Table(name = "ATT_SIGN")
@Setter
@Getter
@Accessors(chain = true)
public class AttSign extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 部门id
     */
    @Column(name = "AUTH_DEPT_ID", length = 50)
    private String deptId;

    /**
     * 人员id
     */
    @Column(name = "PERS_PERSON_ID", length = 50)
    private String personId;

    /**
     * 人员编号
     */
    @Column(name = "PERS_PERSON_PIN", length = 30)
    private String personPin;

    /**
     * 补签日期时间
     */
    @Column(name = "SIGN_DATETIME")
    private Date signDatetime;

    /**
     * 流程业务ID,全局唯一
     */
    @Column(name = "BUSINESS_KEY", length = 50)
    private String businessKey;

    /**
     * 备注
     */
    @Column(name = "REMARK")
    private String remark;

    @Column(name = "FLOW_STATUS")
    private String flowStatus;

    /**
     * 记录补签异常打卡
     */
    @Column(name = "BEFORE_SIGN_RECORD")
    private String beforeSignRecord;

    /**
     * 记录补签时间计算后结果
     */
    @Column(name = "AFTER_SIGN_RECORD")
    private String afterSignRecord;

    @Column(name = "ATT_STATE")
    private String attState;

}
