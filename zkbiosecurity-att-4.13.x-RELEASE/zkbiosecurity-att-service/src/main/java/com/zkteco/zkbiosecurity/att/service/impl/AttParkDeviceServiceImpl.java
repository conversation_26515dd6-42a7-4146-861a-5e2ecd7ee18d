package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.vo.Att4ParkDeviceSelect;
import com.zkteco.zkbiosecurity.att.vo.Att4ParkEntranceArea;
import com.zkteco.zkbiosecurity.att.vo.AttParkAreaItem;
import com.zkteco.zkbiosecurity.att.vo.AttParkDeviceSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;


/**
 * 停车场当考勤、获取停车场设备、区域信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:04
 * @since 1.0.0
 */
@Service
public class AttParkDeviceServiceImpl implements AttParkDeviceService {

    @Autowired(required = false)
    private AttGetParkDeviceSelectService attGetParkDeviceSelectService;
    @Autowired(required = false)
    private AttGetParkDeviceService attGetParkDeviceService;
    @Autowired(required = false)
    private AttGetParkDeviceAreaService attGetParkDeviceAreaService;

    @Autowired
    private AttPointService attPointService;

    @Override
    public AttParkDeviceSelectItem getParkDeviceById(String deviceId) {
        if (Objects.nonNull(attGetParkDeviceService)) {
            Att4ParkDeviceSelect att4ParkDeviceSelect = attGetParkDeviceService.getParkDeviceById(deviceId);
            if (Objects.nonNull(att4ParkDeviceSelect)) {
                AttParkDeviceSelectItem attParkDeviceSelectItem = new AttParkDeviceSelectItem();
                ModelUtil.copyProperties(att4ParkDeviceSelect, attParkDeviceSelectItem);
                return attParkDeviceSelectItem;
            }
        }
        return null;
    }

    @Override
    public List<AttParkDeviceSelectItem> getParkDeviceByIds(Collection<String> deviceIds) {
        List<AttParkDeviceSelectItem> attParkDeviceSelectItemList = new ArrayList<>();
        if (Objects.nonNull(attGetParkDeviceAreaService)) {
            List<Att4ParkDeviceSelect> att4ParkDeviceSelectList =
                attGetParkDeviceService.getParkDeviceItemsByIds(deviceIds);
            if (!CollectionUtil.isEmpty(att4ParkDeviceSelectList)) {
                attParkDeviceSelectItemList =
                    ModelUtil.copyListProperties(att4ParkDeviceSelectList, AttParkDeviceSelectItem.class);
            }
        }
        return attParkDeviceSelectItemList;
    }

    @Override
    public AttParkAreaItem getParkAreaByAreaId(String areaId) {
        if (Objects.nonNull(attGetParkDeviceAreaService)) {
            Att4ParkEntranceArea att4ParkEntranceArea = attGetParkDeviceAreaService.getParkEntranceAreaById(areaId);
            if (Objects.nonNull(att4ParkEntranceArea)) {
                AttParkAreaItem attParkAreaItem = new AttParkAreaItem();
                ModelUtil.copyProperties(att4ParkEntranceArea, attParkAreaItem);
                return attParkAreaItem;
            }
        }
        return null;
    }

    @Override
    public List<AttParkAreaItem> getParkAreaByAreaIds(Collection<String> areaIds) {
        List<AttParkAreaItem> attParkAreaItemList = new ArrayList<>();
        if (Objects.nonNull(attGetParkDeviceAreaService)) {
            List<Att4ParkEntranceArea> att4ParkEntranceAreaList =
                attGetParkDeviceAreaService.getParkEntranceAreaItemsByIds(areaIds);
            if (!CollectionUtil.isEmpty(att4ParkEntranceAreaList)) {
                attParkAreaItemList = ModelUtil.copyListProperties(att4ParkEntranceAreaList, AttParkAreaItem.class);
            }
        }
        return attParkAreaItemList;
    }

    @Override
    public List<AttParkAreaItem> getParkAreaByAreaAll() {
        List<AttParkAreaItem> attParkAreaItemList = new ArrayList<>();
        if (Objects.nonNull(attGetParkDeviceAreaService)) {
            List<Att4ParkEntranceArea> parkEntranceArea = attGetParkDeviceAreaService.getAllParkEntranceArea();
            if (!CollectionUtil.isEmpty(parkEntranceArea)) {
                attParkAreaItemList = ModelUtil.copyListProperties(parkEntranceArea, AttParkAreaItem.class);
            }
        }
        return attParkAreaItemList;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Pager getSelectParkDevicePager(AttParkDeviceSelectItem condition, int page, int size) {
        if (Objects.nonNull(attGetParkDeviceSelectService)) {
            Att4ParkDeviceSelect parkDeviceSelectItem =
                ModelUtil.copyPropertiesIgnoreNull(condition, new Att4ParkDeviceSelect());
            List<String> deviceModules = attPointService.getDeviceIdsByDeviceModule(ConstUtil.SYSTEM_MODULE_PARK);
            String deviceModuleStr = StringUtils.join(deviceModules, ",");
            if (StringUtils.isNotBlank(deviceModuleStr)) {
                parkDeviceSelectItem.setSelectId(condition.getSelectId() + "," + deviceModuleStr);
            } else {
                parkDeviceSelectItem.setSelectId(condition.getSelectId());
            }
            Pager pager = attGetParkDeviceSelectService.getAccDeviceSelectList(parkDeviceSelectItem, page, size);
            List<Att4ParkDeviceSelect> parkDeviceItemList = (List<Att4ParkDeviceSelect>)pager.getData();

            List<AttParkDeviceSelectItem> itemList = new ArrayList<>();
            parkDeviceItemList.forEach(parkDeviceItem -> {
                AttParkDeviceSelectItem item = new AttParkDeviceSelectItem();
                // 前端双列表需要用ID
                item.setId(parkDeviceItem.getChannelId());
                item.setName(parkDeviceItem.getName());
                item.setIpAddress(parkDeviceItem.getIpAddress());
                item.setStatus(parkDeviceItem.getChannelState());
                item.setChannelId(parkDeviceItem.getChannelId());
                item.setChannelName(parkDeviceItem.getChannelName());
                itemList.add(item);
            });
            pager.setData(itemList);
            return pager;
        } else {
            Pager pager = new Pager();
            pager.setData(new ArrayList<>());
            return pager;
        }
    }
}