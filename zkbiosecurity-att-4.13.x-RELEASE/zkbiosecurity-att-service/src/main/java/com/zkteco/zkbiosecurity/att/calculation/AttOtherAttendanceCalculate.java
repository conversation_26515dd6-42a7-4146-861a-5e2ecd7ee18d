package com.zkteco.zkbiosecurity.att.calculation;

import java.util.List;

import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.vo.AttRecordItem;

/**
 * 非正常考勤业务流程
 * <p>
 * 未排班/排班且休息/节假日/调休
 * </p>
 *
 * <AUTHOR>
 * @date 2020-06-22 19:34
 * @sine 1.0.0
 */
@Component
public class AttOtherAttendanceCalculate {

    /**
     * 非正常考勤业务流程
     * <p>
     * 未排班/排班且休息/节假日/调休
     * </p>
     * 
     * @param attRecordItem
     * @param personSchDataMap
     */
    public boolean otherAttendance(AttRecordItem attRecordItem, List<AttPersonSchBO> personSchDataMap) {
        boolean otherRes = false;
        // 排班对象大于1个，是智能排班，不属于非正常考勤
        if (personSchDataMap.size() < 2) {
            AttPersonSchBO attPersonSchBO = personSchDataMap.get(0);
            String attendStatus = attPersonSchBO.getAttendStatus();
            switch (attendStatus) {
                case AttCalculationConstant.AttAttendStatus.NO_SCHEDULING:
                    noScheduling(attRecordItem, AttCalculationConstant.AttAttendStatus.NO_SCHEDULING);
                    otherRes = true;
                    break;
                case AttCalculationConstant.AttAttendStatus.SCHEDULING_AND_REST:
                    schedulingAndRest(attRecordItem, attPersonSchBO);
                    otherRes = true;
                    break;
                case AttCalculationConstant.AttAttendStatus.HOLIDAY:
                    noScheduling(attRecordItem, AttCalculationConstant.AttAttendStatus.HOLIDAY);
                    otherRes = true;
                    break;
                case AttCalculationConstant.AttAttendStatus.TUNE_OFF:
                    noScheduling(attRecordItem, AttCalculationConstant.AttAttendStatus.TUNE_OFF);
                    otherRes = true;
                    break;
                default:
                    break;
            }
        }
        return otherRes;
    }

    /** 未排班 */
    private void noScheduling(AttRecordItem attRecordItem, String attendStatus) {
        attRecordItem.setAttendanceStatus(attendStatus);
        attRecordItem.setShiftNo("");
        attRecordItem.setShiftName("");
        attRecordItem.setShiftTimeData("");
        attRecordItem.setCardValidData("");
        attRecordItem.setCardValidCount(0);

        attRecordItem.setShouldMinute(0);
        attRecordItem.setShouldMinuteEx(0);
        attRecordItem.setActualMinute(0);
        attRecordItem.setValidMinute(0);

        // attRecordItem.setShouldDays(BigDecimal.ZERO);
        // attRecordItem.setActualDays(BigDecimal.ZERO);
        // attRecordItem.setValidDays(BigDecimal.ZERO);

    }

    /** 排班且休息 */
    private void schedulingAndRest(AttRecordItem attRecordItem, AttPersonSchBO attPersonSchBO) {
        attRecordItem.setShiftNo(attPersonSchBO.getAttShiftNo());
        attRecordItem.setShiftName(attPersonSchBO.getAttShiftName());
        attRecordItem.setAttendanceStatus(AttCalculationConstant.AttAttendStatus.SCHEDULING_AND_REST);
        attRecordItem.setShiftTimeData("");
        attRecordItem.setCardValidData("");
        attRecordItem.setCardValidCount(0);

        attRecordItem.setShouldMinute(0);
        attRecordItem.setShouldMinuteEx(0);
        attRecordItem.setActualMinute(0);
        attRecordItem.setValidMinute(0);

        // attRecordItem.setShouldDays(BigDecimal.ZERO);
        // attRecordItem.setActualDays(BigDecimal.ZERO);
        // attRecordItem.setValidDays(BigDecimal.ZERO);

    }
}
