package com.zkteco.zkbiosecurity.att.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * 调班
 */
@Entity
@Table(name = "ATT_CLASS")
@Setter
@Getter
@Accessors(chain = true)
public class AttClass extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 调整人员部门id
     */
    @Column(name = "AUTH_ADJUSTDEPT_ID", length = 50)
    private String adjustDeptId;

    /**
     * 调整人员id
     */
    @Column(name = "PERS_ADJUSTPERSON_ID", length = 50)
    private String adjustPersonId;

    /**
     * 调整人员编号
     */
    @Column(name = "PERS_ADJUSTPERSON_PIN", length = 50)
    private String adjustPersonPin;

    /**
     * 对调人员部门id
     */
    @Column(name = "AUTH_SWAPDEPT_ID", length = 50)
    private String swapDeptId;

    /**
     * 对调人员id
     */
    @Column(name = "PERS_SWAPPERSON_ID", length = 50)
    private String swapPersonId;

    /**
     * 对调人员编号
     */
    @Column(name = "PERS_SWAPPERSON_PIN", length = 50)
    private String swapPersonPin;

    /**
     * 调整类型
     */
    @Column(name = "ADJUST_TYPE")
    private Short adjustType;

    /**
     * 调整日期
     */
    @Column(name = "ADJUST_DATE")
    private Date adjustDate;

    /**
     * 对调日期
     */
    @Column(name = "SWAP_DATE")
    private Date swapDate;

    /**
     * 备注
     */
    @Column(name = "REMARK")
    private String remark;

    /**
     * 对调班次id
     */
    @Column(name = "SWAPSHIFT_ID")
    private String swapShiftId;

    /**
     * 流程业务ID,全局唯一
     */
    @Column(name = "BUSINESS_KEY", length = 50)
    private String businessKey;

    @Column(name = "FLOW_STATUS")
    private String flowStatus;

}