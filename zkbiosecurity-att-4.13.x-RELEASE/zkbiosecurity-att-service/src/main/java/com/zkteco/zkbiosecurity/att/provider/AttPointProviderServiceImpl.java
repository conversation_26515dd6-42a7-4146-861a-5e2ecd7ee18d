package com.zkteco.zkbiosecurity.att.provider;

import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.service.AttPointService;
import com.zkteco.zkbiosecurity.att.vo.AttPointItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.ins.service.InsGetAttPointService;
import com.zkteco.zkbiosecurity.park.service.ParkGetAttPointService;
import com.zkteco.zkbiosecurity.pid.service.Pid4AttPointService;
import com.zkteco.zkbiosecurity.vms.service.VmsGetAttPointService;

/**
 * 其它模块，调用考勤，判断是否是考勤点。
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
 * @version V1.0
 * @date Created In 11:57 2018/12/6
 */
@Deprecated
@Service
@Transactional
public class AttPointProviderServiceImpl implements ParkGetAttPointService,
    InsGetAttPointService, Pid4AttPointService, VmsGetAttPointService {

    @Autowired
    private AttPointService attPointService;

    @Override
    public boolean isUsedByAttPoint(String devId) {
        AttPointItem attPointItem = attPointService.getItemByDeviceId(devId);
        if (Objects.nonNull(attPointItem)) {
            return true;
        }
        return false;
    }

    @Override
    public boolean isUsedByAttPoint(String module, String deviceId) {
        AttPointItem attPointItem = attPointService.getItemByDeviceId(deviceId);
        if (Objects.nonNull(attPointItem)) {
            return true;
        }
        return false;
    }

    @Override
    public boolean isPidDeviceUsedByAttPoint(String deviceSn) {
        AttPointItem attPointItem = attPointService.getItemByDeviceSn(deviceSn);
        if (Objects.nonNull(attPointItem)) {
            return true;
        }
        return false;
    }

    @Override
    public boolean isVmsDeviceUsedByAttPoint(String deviceSns) {
        AttPointItem condition = new AttPointItem();
        condition.setInDeviceSn(deviceSns);
        List<AttPointItem> attPointItemList = attPointService.getByCondition(condition);
        if (!CollectionUtil.isEmpty(attPointItemList)) {
            return true;
        }
        return false;
    }
}
