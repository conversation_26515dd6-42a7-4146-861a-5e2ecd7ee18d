package com.zkteco.zkbiosecurity.att.data.upgrade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * 升级到4.9.0
 *
 */
@Slf4j
@Component
public class AttVer4_9_0 implements UpgradeVersionManager {

    @Autowired
    private AuthPermissionService authPermissionService;

    @Override
    public String getModule() {
        return BaseConstants.ATT;
    }

    @Override
    public String getVersion() {
        return "v4.9.0";
    }

    @Override
    public boolean executeUpgrade() {
        AuthPermissionItem attRule = authPermissionService.getItemByCode("AttRule");
        if (attRule != null) {

            // 实时点名设置
            AuthPermissionItem attRuleRealTimeSet =
                new AuthPermissionItem("AttRuleRealTimeSet", "att_realTime_realTimeSet", "att:rule:realTimeSet",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
            attRuleRealTimeSet.setParentId(attRule.getId());
            authPermissionService.saveItem(attRuleRealTimeSet);


            // 员工自助设置
            AuthPermissionItem attRuleEmployeeSet = new AuthPermissionItem("AttRuleEmployeeSet", "auth_login_personLogin", "att:rule:employeeSet",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
            attRuleEmployeeSet.setParentId(attRule.getId());
            authPermissionService.saveItem(attRuleEmployeeSet);
        }

        return true;
    }
}
