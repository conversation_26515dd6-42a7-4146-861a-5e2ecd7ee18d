package com.zkteco.zkbiosecurity.att.service.impl;

import com.zkteco.zkbiosecurity.att.dao.AttBreakTimeDao;
import com.zkteco.zkbiosecurity.att.dao.AttTimeSlotDao;
import com.zkteco.zkbiosecurity.att.model.AttBreakTime;
import com.zkteco.zkbiosecurity.att.model.AttTimeSlot;
import com.zkteco.zkbiosecurity.att.service.AttBreakTimeService;
import com.zkteco.zkbiosecurity.att.vo.AttBreakTimeItem;
import com.zkteco.zkbiosecurity.att.vo.AttBreakTimeSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 休息时间段
 *
 * <AUTHOR>
 * @date 2019/5/30
 */
@Service
public class AttBreakTimeServiceImpl implements AttBreakTimeService {

    @Autowired
    private AttBreakTimeDao attBreakTimeDao;
    @Autowired
    private AttTimeSlotDao attTimeSlotDao;

    @Override
    @Transactional
    public AttBreakTimeItem saveItem(AttBreakTimeItem item) {
        AttBreakTime attBreakTime =
            Optional.ofNullable(item).map(AttBreakTimeItem::getId).filter(StringUtils::isNotBlank)
                .flatMap(attBreakTimeDao::findById).filter(Objects::nonNull).orElse(new AttBreakTime());
        ModelUtil.copyProperties(item, attBreakTime);
        attBreakTimeDao.save(attBreakTime);
        item.setId(attBreakTime.getId());
        return item;
    }

    @Override
    public AttBreakTimeItem getItemById(String id) {
        List<AttBreakTimeItem> items = getByCondition(new AttBreakTimeItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public List<AttBreakTimeItem> getByCondition(AttBreakTimeItem condition) {
        return (List<AttBreakTimeItem>)attBreakTimeDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int pageNo, int pageSize) {
        return attBreakTimeDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            // @TODO 判断休息时间段是否已被引用 不能删除
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                attBreakTimeDao.deleteById(id);
            }
        }
        return true;
    }

    @Override
    public Pager selectList(String sessionId, AttBreakTimeSelectItem condition, int pageNo, int pageSize) {

        AttBreakTimeItem attBreakTimeItem = new AttBreakTimeItem();
        attBreakTimeItem.setName(condition.getName());
        attBreakTimeItem.setNotInId(condition.getSelectId());

        AttTimeSlot attTimeSlot = attTimeSlotDao.getOne(condition.getLinkId());
        // 过滤已选择的休息时间段
        String attBreakTimeIds = CollectionUtil.getPropertys(attTimeSlot.getAttBreakTimeSet(), AttBreakTime::getId);
        if (StringUtils.isNotBlank(attBreakTimeIds)) {
            if (StringUtils.isNotBlank(attBreakTimeItem.getNotInId())) {
                attBreakTimeItem.setNotInId(attBreakTimeItem.getNotInId() + "," + attBreakTimeIds);
            } else {
                attBreakTimeItem.setNotInId(attBreakTimeIds);
            }
        }

        StringBuffer sql = new StringBuffer();

        /**
         * 休息时间段的开始时间要大于上班时间，结束时间要小于下班时间
         *
         * 1. 时间段是不跨天的: 休息时间段也是不跨天的, 则休息时间段的开始时间要大于上班时间，结束时间要小于下班时间 e.g.:(09:00-18:00) (12:00-13:00) 2. 时间段是跨天的: 2.1.
         * 休息时间段不跨天,当天的 则休息时间段的开始时间要大于上班时间 e.g.:(20:00-08:00) (22:00-23:00) 2.2. 休息时间段不跨天,隔天的
         * 则休息时间段的开始时间要小于上班时间，结束时间要小于下班时间 e.g.:(20:00-08:00) (02:00-03:00) 2.3. 休息时间段跨天的, 则休息时间段的开始时间要大于上班时间，结束时间要小于下班时间
         * e.g.:(20:00-08:00) (22:00-02:00)
         */
        sql.append(String.format(
            " (('%s' < '%s' AND t.start_time < t.end_time AND t.start_time > '%s' AND t.end_time < '%s') ",
            attTimeSlot.getToWorkTime(), attTimeSlot.getOffWorkTime(), attTimeSlot.getToWorkTime(),
            attTimeSlot.getOffWorkTime()));
        sql.append(" OR ");
        sql.append(String.format(" ('%s' > '%s' AND ( ", attTimeSlot.getToWorkTime(), attTimeSlot.getOffWorkTime()));
        sql.append(String.format(
            " (t.start_time < t.end_time AND ((t.start_time > '%s') OR (t.start_time < '%s' AND t.end_time < '%s'))) ",
            attTimeSlot.getToWorkTime(), attTimeSlot.getToWorkTime(), attTimeSlot.getOffWorkTime()));
        sql.append(" OR ");
        sql.append(String.format(" (t.start_time > t.end_time AND t.start_time > '%s' AND t.end_time < '%s') ",
            attTimeSlot.getToWorkTime(), attTimeSlot.getOffWorkTime()));
        sql.append(" ))) ");
        attBreakTimeItem.setTimeBetween(sql.toString());

        Pager pager = getItemsByPage(attBreakTimeItem, pageNo, pageSize);
        List<AttBreakTimeItem> data = (List<AttBreakTimeItem>)pager.getData();
        if (!CollectionUtil.isEmpty(data)) {
            List<AttBreakTimeSelectItem> dataList = new ArrayList<AttBreakTimeSelectItem>();
            data.forEach(item -> {
                AttBreakTimeSelectItem selectItem = new AttBreakTimeSelectItem();
                selectItem.setId(item.getId());
                selectItem.setName(item.getName());
                selectItem.setStartTime(item.getStartTime());
                selectItem.setEndTime(item.getEndTime());
                dataList.add(selectItem);
            });
            pager.setData(dataList);
        }

        return pager;
    }

    @Override
    public boolean isExistFkData(String ids) {
        boolean exitsTimeSlotId = false;
        if (StringUtils.isNotBlank(ids)) {
            List<String> idList = StrUtil.strToList(ids);
            exitsTimeSlotId = attTimeSlotDao.existsByAttBreakTimeSet_IdIn(idList);
        }
        return exitsTimeSlotId;
    }

    @Override
    public boolean existsByName(String name) {
        return attBreakTimeDao.existsByName(name);
    }

    @Override
    public AttBreakTimeItem getItemByStartTimeAndEndTime(String startTime, String endTime) {
        AttBreakTime attBreakTime = attBreakTimeDao.findByStartTimeAndEndTime(startTime, endTime);
        AttBreakTimeItem attBreakTimeItem = null;
        if (Objects.nonNull(attBreakTime)) {
            attBreakTimeItem = new AttBreakTimeItem();
            ModelUtil.copyProperties(attBreakTime, attBreakTimeItem);
        }
        return attBreakTimeItem;
    }
}
