package com.zkteco.zkbiosecurity.att.data.upgrade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.jdbc.JdbcOperateTemplate;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * 升级到4.0.0
 *
 */
@Slf4j
@Component
public class AttVer4_0_0 implements UpgradeVersionManager {

    @Autowired
    private JdbcOperateTemplate jdbcOperateTemplate;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public String getModule() {
        return BaseConstants.ATT;
    }

    @Override
    public String getVersion() {
        return "v4.0.0";
    }

    @Override
    public boolean executeUpgrade() {

        // 涉及加密字段长度调整
        jdbcOperateTemplate.alterTableCharLen("ATT_AUTOEXPORT", "FTP_PASSWORD", "250");
        jdbcOperateTemplate.alterTableCharLen("ATT_AUTOEXPORT", "EMAIL_RECIPIENTS", "500");
        jdbcOperateTemplate.alterTableCharLen("ATT_TRANSACTION", "ATT_PLACE", "250");

        return true;
    }

}
