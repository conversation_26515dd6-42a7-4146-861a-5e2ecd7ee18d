package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.utils.AttCoordinateUtils;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.dao.AttSignAddressAreaDao;
import com.zkteco.zkbiosecurity.att.dao.AttSignAddressDao;
import com.zkteco.zkbiosecurity.att.model.AttSignAddress;
import com.zkteco.zkbiosecurity.att.model.AttSignAddressArea;
import com.zkteco.zkbiosecurity.att.service.AttAreaPersonService;
import com.zkteco.zkbiosecurity.att.service.AttSignAddressService;
import com.zkteco.zkbiosecurity.att.vo.AttSignAddressAreaItem;
import com.zkteco.zkbiosecurity.att.vo.AttSignAddressItem;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 签到地址
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:09
 * @since 1.0.0
 */
@Slf4j
@Service
public class AttSignAddressServiceImpl implements AttSignAddressService {

    @Autowired
    private AttSignAddressDao attSignAddressDao;
    @Autowired
    private AttSignAddressAreaDao attSignAddressAreaDao;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private AttAreaPersonService attAreaPersonService;
    @Autowired
    private PersPersonService persPersonService;

    @Override
    @Transactional
    public AttSignAddressItem saveItem(AttSignAddressItem item) {

        if (StringUtils.isNotBlank(item.getAreaIds())) {
            String[] areaIdArray = item.getAreaIds().split(",");
            for (String areaId : areaIdArray) {
                AttSignAddressArea attSignAddressArea = attSignAddressAreaDao.findByAreaId(areaId);
                if (attSignAddressArea != null && !attSignAddressArea.getSignAddressId().equals(item.getId())) {
                    throw new ZKBusinessException("att_self_attSignAddressAreaIsExist");
                }
            }
        } else {
            throw new ZKBusinessException("att_h5_attAreaNull");
        }

        AttSignAddress attSignAddress =
            Optional.ofNullable(item).map(AttSignAddressItem::getId).filter(StringUtils::isNotBlank)
                .flatMap(attSignAddressDao::findById).filter(Objects::nonNull).orElse(new AttSignAddress());
        ModelUtil.copyProperties(item, attSignAddress);
        attSignAddressDao.save(attSignAddress);
        item.setId(attSignAddress.getId());

        // 保存考勤签到地点和区域关联关系
        String signAddressId = attSignAddress.getId();
        String areaIds = item.getAreaIds();
        Set<String> newAreaIds = new HashSet<>();
        if (StringUtils.isNotBlank(areaIds)) {
            newAreaIds = new HashSet<>(Arrays.asList(StringUtils.split(areaIds, ",")));
        }
        AttSignAddressAreaItem condition = new AttSignAddressAreaItem();
        condition.setSignAddressId(signAddressId);
        List<AttSignAddressAreaItem> list = (List<AttSignAddressAreaItem>)attSignAddressAreaDao
            .getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
        Map<String, AttSignAddressAreaItem> signAddressAreaItemMap =
            CollectionUtil.listToKeyMap(list, AttSignAddressAreaItem::getAreaId);
        Set<String> oldAreaIds = CollectionUtil.listToPropertySet(list, AttSignAddressAreaItem::getAreaId);
        // 新增
        for (String newAreaId : newAreaIds) {
            if (!oldAreaIds.contains(newAreaId)) {
                AttSignAddressArea attSignAddressArea = new AttSignAddressArea();
                attSignAddressArea.setSignAddressId(signAddressId);
                attSignAddressArea.setAreaId(newAreaId);
                attSignAddressAreaDao.save(attSignAddressArea);
            }
        }
        // 删除
        for (String oldAreaId : oldAreaIds) {
            if (!newAreaIds.contains(oldAreaId)) {
                AttSignAddressAreaItem attSignAddressAreaItem = signAddressAreaItemMap.get(oldAreaId);
                attSignAddressAreaDao.deleteById(attSignAddressAreaItem.getId());
            }
        }
        return item;
    }

    @Override
    public AttSignAddressItem getItemById(String id) {
        List<AttSignAddressItem> list = getByCondition(new AttSignAddressItem(id));
        return (null != list && !list.isEmpty()) ? list.get(0) : null;
    }

    @Override
    public List<AttSignAddressItem> getByCondition(AttSignAddressItem condition) {
        List<AttSignAddressItem> data = (List<AttSignAddressItem>)attSignAddressDao.getItemsBySql(condition.getClass(),
                SQLUtil.getSqlByItem(condition));
        for (AttSignAddressItem item : data) {
            buildAreaInfo(item);
        }
        return data;
    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, AttSignAddressItem condition, int pageNo, int pageSize) {
        Pager pager =
            attSignAddressDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        List<AttSignAddressItem> data = (List<AttSignAddressItem>)pager.getData();
        for (AttSignAddressItem item : data) {
            buildAreaInfo(item);
        }
        return pager;
    }

    @Override
    public Pager getItemsByPage(BaseItem baseItem, int i, int i1) {
        return null;
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                // 先删除关联考勤地址区域的关系
                attSignAddressAreaDao.deleteBySignAddressId(id);
                attSignAddressDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public List<AttSignAddressItem> getPersonSignAddress(String personPin) {

        // 根据区域查询
        PersPersonItem personItem = persPersonService.getItemByPin(personPin);
        if (Objects.isNull(personItem)) {
            return new ArrayList<>();
        }

        String areaIds = attAreaPersonService.getAreaIdsByPersonId(personItem.getId());
        if (StringUtils.isBlank(areaIds)) {
            return new ArrayList<>();
        }

        AttSignAddressAreaItem attSignAddressAreaItem = new AttSignAddressAreaItem();
        attSignAddressAreaItem.setInAreaId(areaIds);
        List<AttSignAddressAreaItem> attSignAddressAreaItems = (List<AttSignAddressAreaItem>)attSignAddressAreaDao
            .getItemsBySql(attSignAddressAreaItem.getClass(), SQLUtil.getSqlByItem(attSignAddressAreaItem));
        String signAddressIds =
            CollectionUtil.getPropertys(attSignAddressAreaItems, AttSignAddressAreaItem::getSignAddressId);
        if (StringUtils.isBlank(signAddressIds)) {
            return new ArrayList<>();
        }

        AttSignAddressItem condition = new AttSignAddressItem();
        condition.setInId(signAddressIds);
        return getByCondition(condition);
    }

    @Override
    public AttSignAddressItem getAttSignAddressItemById(String id) {
        AttSignAddressItem item = getItemById(id);
        buildAreaInfo(item);
        return item;
    }

    @Override
    public ZKResultMsg checkSignInRange(String pin, String longitude, String latitude, String mapType) {

        if (StringUtils.isBlank(mapType)) {
            return ZKResultMsg.failMsg("mapType cannot be empty ");
        }
        if (!"WGS84,GCJ02,BD09".contains(mapType)) {
            return ZKResultMsg.failMsg("mapType not supported");
        }

        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        JSONObject result = new JSONObject();
        List<AttSignAddressItem> signAddressList = getPersonSignAddress(pin);
        if (signAddressList == null || signAddressList.size() == 0) {
            result.put("status", -1);
            resultMsg.setData(result);
            return resultMsg;
        }
        log.info("AttSignAddress APP mapType = {}, longitude = {}, latitude = {}", mapType, longitude, latitude);
        boolean isInSignAddress = false;
        for (AttSignAddressItem signAddressItem : signAddressList) {
            String signLongitude = signAddressItem.getLongitude();
            String signLatitude = signAddressItem.getLatitude();
            String signMapType = signAddressItem.getMapType();
            int validRange = signAddressItem.getValidRange();

            if (AttConstant.ATT_MAP_BAIDUMAP.equals(signMapType)) {
                signMapType = "BD09";
            } else if (AttConstant.ATT_MAP_GAODEMAP.equals(signMapType)) {
                signMapType = "GCJ02";
            } else {
                signMapType = "WGS84";
            }
            log.info("AttSignAddress WEB signMapType = {}, signLongitude = {}, signLatitude = {}", signMapType, signLongitude, signLatitude);
            int distance = AttCoordinateUtils.distanceMeters(Double.parseDouble(longitude), Double.parseDouble(latitude),
                    mapType, Double.parseDouble(signLongitude), Double.parseDouble(signLatitude), signMapType);
            log.info("AttSignAddress distance = {}", distance);
            if (distance < validRange) {
                isInSignAddress = true;
                break;
            }
        }
        if (isInSignAddress) {
            result.put("status", 0);
        } else {
            result.put("status", 1);
        }
        resultMsg.setData(result);
        return resultMsg;
    }

    public void buildAreaInfo(AttSignAddressItem item) {
        AttSignAddressAreaItem attSignAddressAreaItem = new AttSignAddressAreaItem();
        attSignAddressAreaItem.setSignAddressId(item.getId());
        List<AttSignAddressAreaItem> attSignAddressAreaItems = (List<AttSignAddressAreaItem>)attSignAddressAreaDao
                .getItemsBySql(attSignAddressAreaItem.getClass(), SQLUtil.getSqlByItem(attSignAddressAreaItem));
        String areaIds = CollectionUtil.getPropertys(attSignAddressAreaItems, AttSignAddressAreaItem::getAreaId);
        item.setAreaIds(areaIds);
        if (StringUtils.isNotBlank(areaIds)) {
            List<AuthAreaItem> authAreaItems = authAreaService.getItemsByIds(areaIds);
            String areaNames = CollectionUtil.getPropertys(authAreaItems, AuthAreaItem::getName);
            item.setAreaNames(areaNames);
        }
    }
}
