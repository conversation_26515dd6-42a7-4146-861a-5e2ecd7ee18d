package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;

import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.vo.AttClassItem;
import com.zkteco.zkbiosecurity.pers.service.PersLeavePersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersLeavePersonItem;
import com.zkteco.zkbiosecurity.redis.redisson.annotation.RedisLock;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.dao.AttDayCardDetailDao;
import com.zkteco.zkbiosecurity.att.dao.AttTransactionDao;
import com.zkteco.zkbiosecurity.att.model.AttDayCardDetail;
import com.zkteco.zkbiosecurity.att.service.AttDayCardDetailService;
import com.zkteco.zkbiosecurity.att.service.AttPersonService;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttDayCardDetailItem;
import com.zkteco.zkbiosecurity.att.vo.AttDayCardDetailReportItem;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 日打卡详情
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 18:27 2020/6/28
 */
@Slf4j
@Service
public class AttDayCardDetailServiceImpl implements AttDayCardDetailService {

    @Autowired
    private AttDayCardDetailDao attDayCardDetailDao;
    @Autowired
    private AttTransactionDao attTransactionDao;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private AttParamService attParamService;

    @Override
    public AttDayCardDetailItem getItemById(String id) {
        List<AttDayCardDetailItem> items = getByCondition(new AttDayCardDetailItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public List<AttDayCardDetailItem> getByCondition(AttDayCardDetailItem condition) {
        List<AttDayCardDetailItem> attDayCardDetailItemList = (List<AttDayCardDetailItem>)attDayCardDetailDao
            .getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
        buildItems(attDayCardDetailItemList);
        return attDayCardDetailItemList;
    }

    @Override
    public List<AttDayCardDetailItem> getByCondition(AttDayCardDetailItem condition, int beginIndex, int endIndex) {
        List<AttDayCardDetailItem> attDayCardDetailItemList = (List<AttDayCardDetailItem>)attDayCardDetailDao
            .getItemsDataBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), beginIndex, endIndex, true);
        buildItems(attDayCardDetailItemList);
        return attDayCardDetailItemList;
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        Pager pager = attDayCardDetailDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        buildItems((List<AttDayCardDetailItem>)pager.getData());
        return pager;
    }

    @Override
    public Pager getItemsByPageByAuthFilter(String sessionId, AttDayCardDetailItem condition, int pageNo,
        int pageSize) {
        buildCondition(sessionId, condition);
        // 自定义排序
        String sql = buildOrderBySQL(condition);
        Pager pager = attDayCardDetailDao.getItemsBySql(condition.getClass(), sql, pageNo, pageSize);
        buildItems((List<AttDayCardDetailItem>)pager.getData());
        return pager;
    }

    /**
     * 组装部门查询条件
     *
     * @param sessionId
     * @param condition
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/6/30 15:22
     */
    private void buildCondition(String sessionId, AttDayCardDetailItem condition) {

        // 判断是否为人工自助
        String persperPin = attPersonService.getPinByLoginType(sessionId);
        if (persperPin != null) {
            condition.setPersonPin(persperPin);
            condition.setEquals(true);
            return;
        }
        // 部门查询条件组装
        if (StringUtils.isBlank(condition.getDeptId())) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, null, null, null));
        } else {
            // 包含子部门
            if ("true".equals(condition.getIsIncludeLower())) {
                condition.setInDeptId(
                    authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, condition.getDeptId(), null, null));
                condition.setDeptId(null);
            }
        }
    }

    private void buildItems(List<AttDayCardDetailItem> attDayCardDetailItemList) {
        for (AttDayCardDetailItem attDayCardDetailItem: attDayCardDetailItemList ) {
            attDayCardDetailItem.setAttDate(attParamService.dateToLocaleString(DateUtil.stringToDate(attDayCardDetailItem.getAttDate())));
        }
    }

    @Override
    public List<AttDayCardDetailItem> exportItemListByAuthFilter(String sessionId,
        AttDayCardDetailItem attDayCardDetailItem, int beginIndex, int endIndex) {
        buildCondition(sessionId, attDayCardDetailItem);
        String sql = buildOrderBySQL(attDayCardDetailItem);
        List<AttDayCardDetailItem> attDayCardDetailItemList = attDayCardDetailDao.getItemsDataBySql( AttDayCardDetailItem.class, sql, beginIndex, endIndex, true);
        return attDayCardDetailItemList;
    }

    /**
     * 自定义排序
     *
     * @param condition:
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/7/11 10:20
     * @since 1.0.0
     */
    private String buildOrderBySQL(AttDayCardDetailItem condition) {
        StringBuffer sql = new StringBuffer(SQLUtil.getSqlByItem(condition));
        if (sql.indexOf("ORDER BY t.ATT_DATE") < 0) {
            sql.append(", t.ATT_DATE ASC");
        }
        return sql.toString();
    }

    @Override
    public boolean deleteByIds(String ids) {
        return false;
    }

    private List<AttDayCardDetailReportItem> getDayCardDetailReportByPage(AttDayCardDetailReportItem condition,
        int beginIndex, int endIndex) {
        return attDayCardDetailDao.getItemsDataBySql(AttDayCardDetailReportItem.class, SQLUtil.getSqlByItem(condition),
            beginIndex, endIndex, true);
    }

    private List<AttDayCardDetailReportItem> getDayCardDetailReportByCondition(AttDayCardDetailReportItem condition) {
        return (List<AttDayCardDetailReportItem>)attDayCardDetailDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    /**
     * 根据创建时间查询考勤打开记录，并更新统计（当天更换部门，统计时，取最新的部门）
     *
     * @param
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/6/28 17:23
     */
    @Override
    @RedisLock(lockName = "timingUpdateDayCardDetailRedisson", waitTime = 0, expire = 600)
    @Transactional
    public void timingUpdateDayCardDetail() {

        // 最新的开始创建时间
        String newStartCreateTime = AttDateUtils.dateToStrAsLong(new Date());

        // 获取统计更新的最后日期
        BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("AttUpdateDayCardStartCreateTime");
        String startCreateTime = baseSysParamItem.getParamValue();
        // log.info("timingUpdateDayCardDetail startCreateTime = " + startCreateTime);
        if (StringUtils.isBlank(startCreateTime)) {
            int beginIndex = 0;
            while (true) {
                int endIndex = beginIndex + (CollectionUtil.splitSize - 1);
                log.info("beginIndex = " + beginIndex + " >> " + "endIndex = " + endIndex);
                AttDayCardDetailReportItem condition = new AttDayCardDetailReportItem();
                // 首次只统计之前的三个月数据
                condition.setAttDateBegin(AttDateUtils.dateToStrAsShort(DateUtil.addMonth(new Date(), -3)));
                List<AttDayCardDetailReportItem> attDayCardDetailItemList =
                    getDayCardDetailReportByPage(condition, beginIndex, endIndex);
                if (attDayCardDetailItemList != null && attDayCardDetailItemList.size() > 0) {
                    deleteAndSaveDayCardDetailList(attDayCardDetailItemList);
                    if (attDayCardDetailItemList.size() < CollectionUtil.splitSize) {
                        log.info("updateDayCardDetail break !");
                        break;
                    }
                } else {
                    log.info("updateDayCardDetail break !");
                    break;
                }
                beginIndex = endIndex + 1;
            }
        } else {

            // 之前已统计
            // 先查询新纪录，组装成单个考勤日期-多个pin号集合，方便批量查询统计数据
            List<Object[]> newAttTransactionArrayList = attTransactionDao
                .findByCreateTime(DateUtil.stringToDate(startCreateTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
            Map<String, Set<String>> attDayCardDetailMap = new HashMap<>();
            for (Object[] attTransactionArray : newAttTransactionArrayList) {
                String pin = attTransactionArray[0] + "";
                String attDate = attTransactionArray[1] + "";
                Set<String> pinSet;
                if (attDayCardDetailMap.containsKey(attDate)) {
                    pinSet = attDayCardDetailMap.get(attDate);
                } else {
                    pinSet = new HashSet<>();
                }
                pinSet.add(pin);
                attDayCardDetailMap.put(attDate, pinSet);
            }

            // 根据考勤日期遍历处理数据
            for (Map.Entry<String, Set<String>> entry : attDayCardDetailMap.entrySet()) {
                // 分批批量处理数据
                String attDate = entry.getKey();
                List<List<String>> pinLists = CollectionUtil.split(entry.getValue(), CollectionUtil.splitSize);
                for (List<String> pinList : pinLists) {
                    List<AttDayCardDetailReportItem> attDayCardDetailItemList = getDayCardDetailReportByCondition(
                        new AttDayCardDetailReportItem(attDate, String.join(",", pinList)));
                    deleteAndSaveDayCardDetailList(attDayCardDetailItemList);
                }
            }
        }

        // 更新最新的开始创建时间
        if (StringUtils.isBlank(baseSysParamItem.getId())) {
            baseSysParamItem = new BaseSysParamItem("AttUpdateDayCardStartCreateTime", newStartCreateTime,
                "AttUpdateDayCardStartCreateTime", true);
        } else {
            baseSysParamItem.setParamValue(newStartCreateTime);
        }
        baseSysParamService.saveItem(baseSysParamItem);
    }

    /**
     * 批量删除旧的统计数据和保存新的统计数据
     *
     * @param attDayCardDetailReportItemList
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/6/29 16:19
     */
    private void deleteAndSaveDayCardDetailList(List<AttDayCardDetailReportItem> attDayCardDetailReportItemList) {
        if (attDayCardDetailReportItemList != null && attDayCardDetailReportItemList.size() > 0) {

            // 组装考勤日期-多条打卡记录集合，方便批量查询与删除数据
            Map<String, List<AttDayCardDetail>> attDayCardDetailListMap = new HashMap<>();

            // 根据pin号查询部门
            String personPins =
                CollectionUtil.getPropertys(attDayCardDetailReportItemList, AttDayCardDetailReportItem::getPersonPin);

            List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(CollectionUtil.strToList(personPins));
            Map<String, PersPersonItem> persPersonItemMap =
                    CollectionUtil.listToKeyMap(persPersonItemList, PersPersonItem::getPin);

            List<PersLeavePersonItem> persLeavePersonItemList = persLeavePersonService.getItemByPins(CollectionUtil.strToList(personPins));
            Map<String, PersLeavePersonItem> leavePersonItemMap =
                    CollectionUtil.listToKeyMap(persLeavePersonItemList, PersLeavePersonItem::getPin);


            // 遍历赋值信息，赋值部门ID，组装考勤日期-多条打卡记录集合
            for (AttDayCardDetailReportItem attDayCardDetailReportItem : attDayCardDetailReportItemList) {

                // 赋值基础信息
                AttDayCardDetail attDayCardDetail = new AttDayCardDetail();
                attDayCardDetail.setPersonPin(attDayCardDetailReportItem.getPersonPin())
                    .setAttDate(attDayCardDetailReportItem.getAttDate())
                    .setCardCount(attDayCardDetailReportItem.getCardCount())
                    .setEarliestTime(attDayCardDetailReportItem.getEarliestTime())
                    .setLatestTime(attDayCardDetailReportItem.getLatestTime());

                // 赋值部门ID
                if (persPersonItemMap.containsKey(attDayCardDetailReportItem.getPersonPin())) {
                    PersPersonItem persPersonItem = persPersonItemMap.get(attDayCardDetailReportItem.getPersonPin());
                    attDayCardDetail.setDeptId(persPersonItem.getDeptId());
                    attDayCardDetail.setDeptCode(persPersonItem.getDeptCode());
                    attDayCardDetail.setDeptName(persPersonItem.getDeptName());
                    attDayCardDetail.setPersonName(persPersonItem.getName());
                    attDayCardDetail.setPersonLastName(persPersonItem.getLastName());
                } else if (leavePersonItemMap.containsKey(attDayCardDetailReportItem.getPersonPin())) {
                    PersLeavePersonItem persLeavePersonItem = leavePersonItemMap.get(attDayCardDetailReportItem.getPersonPin());
                    attDayCardDetail.setDeptId(persLeavePersonItem.getDeptId());
                    attDayCardDetail.setDeptCode(persLeavePersonItem.getDeptCode());
                    attDayCardDetail.setDeptName(persLeavePersonItem.getDeptName());
                    attDayCardDetail.setPersonName(persLeavePersonItem.getName());
                    attDayCardDetail.setPersonLastName(persLeavePersonItem.getLastName());
                }

                // 组装日期-多个打卡记录
                String attDate = attDayCardDetail.getAttDate();
                List<AttDayCardDetail> attDayCardDetailList;
                if (attDayCardDetailListMap.containsKey(attDate)) {
                    attDayCardDetailList = attDayCardDetailListMap.get(attDate);
                } else {
                    attDayCardDetailList = new ArrayList<>();
                }
                attDayCardDetailList.add(attDayCardDetail);
                attDayCardDetailListMap.put(attDate, attDayCardDetailList);
            }

            // 根据考勤日期遍历处理数据
            for (Map.Entry<String, List<AttDayCardDetail>> entry : attDayCardDetailListMap.entrySet()) {

                // 分批批量处理数据
                String attDate = entry.getKey();
                List<List<AttDayCardDetail>> attDayCardDetailListGroup =
                    CollectionUtil.split(entry.getValue(), CollectionUtil.splitSize);
                for (List<AttDayCardDetail> attDayCardDetailList : attDayCardDetailListGroup) {

                    // 组装打卡时间集合、赋值打卡时间集合
                    Collection<String> pinList =
                        CollectionUtil.getPropertyList(attDayCardDetailList, AttDayCardDetail::getPersonPin, "-1");
                    buildCardTimes(attDate, pinList, attDayCardDetailList);

                    // 量删除之前统计的数据
                    attDayCardDetailDao.deleteByAttDateAndPersonPinIn(attDate, pinList);
                    // 批量保存新的统计数据
                    attDayCardDetailDao.saveAll(attDayCardDetailList);
                }

            }
        }
    }

    /**
     * 组装打卡时间集合、赋值打卡时间集合
     *
     * @param
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/2 10:35
     */
    private void buildCardTimes(String attDate, Collection<String> pinList,
        List<AttDayCardDetail> attDayCardDetailList) {
        // 组装打卡时间集合、赋值打卡时间集合
        List<Object[]> attTimeArrayList = attTransactionDao.findByAttDateAndPersonPinIn(attDate, pinList);
        Map<String, List<String>> attTimesMap = new HashMap<>();
        for (Object[] attTimeArray : attTimeArrayList) {
            String pin = attTimeArray[0] + "";
            String attTime = attTimeArray[1] + "";
            List<String> attTimeList;
            if (attTimesMap.containsKey(pin)) {
                attTimeList = attTimesMap.get(pin);
            } else {
                attTimeList = new ArrayList<>();
            }
            attTimeList.add(attTime);
            attTimesMap.put(pin, attTimeList);
        }
        for (AttDayCardDetail attDayCardDetail : attDayCardDetailList) {
            String pin = attDayCardDetail.getPersonPin();
            if (attTimesMap.containsKey(pin)) {
                List<String> attTimeList = attTimesMap.get(pin);
                String attTimes = "";
                if (!CollectionUtil.isEmpty(attTimeList) && attTimeList.size() > 25) {
                    attTimes = StringUtils.join(attTimeList.subList(0, 25), ";") + "...";
                } else {
                    attTimes = StringUtils.join(attTimeList, ";");
                }
                attDayCardDetail.setAttTimes(attTimes);
            }
        }
    }

    @Override
    @Transactional
    public void dataClean(String keptDate) {
        jdbcTemplate.execute("DELETE FROM ATT_DAY_CARD_DETAIL WHERE ATT_DATE < '" + keptDate + "'");
    }
}