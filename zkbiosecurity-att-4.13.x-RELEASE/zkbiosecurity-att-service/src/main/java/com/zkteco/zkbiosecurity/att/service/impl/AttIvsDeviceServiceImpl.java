package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.service.AttGetIvsDeviceService;
import com.zkteco.zkbiosecurity.att.service.AttIvsDeviceService;
import com.zkteco.zkbiosecurity.att.service.AttPointService;
import com.zkteco.zkbiosecurity.att.vo.Att4IvsChannelSelectItem;
import com.zkteco.zkbiosecurity.att.vo.Att4IvsParentDeviceItem;
import com.zkteco.zkbiosecurity.att.vo.AttIvsDeviceSelectItem;
import com.zkteco.zkbiosecurity.att.vo.AttIvsParentDeviceItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;

/**
 * 智能视频当考勤、获取智能视频设备信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:04
 * @since 1.0.0
 */
@Service
public class AttIvsDeviceServiceImpl implements AttIvsDeviceService {

    @Autowired(required = false)
    private AttGetIvsDeviceService attGetIvsDeviceService;

    @Autowired
    private AttPointService attPointService;

    @Override
    public AttIvsDeviceSelectItem getIvsDeviceByDeviceId(String deviceId) {
        if (Objects.nonNull(attGetIvsDeviceService)) {
            Att4IvsChannelSelectItem att4IvsChannelSelectItem = attGetIvsDeviceService.getIvsChannelById(deviceId);
            if (Objects.nonNull(att4IvsChannelSelectItem)) {
                AttIvsDeviceSelectItem deviceSelectItem = new AttIvsDeviceSelectItem();
                ModelUtil.copyPropertiesIgnoreNull(att4IvsChannelSelectItem, deviceSelectItem);

                return deviceSelectItem;
            }
        }
        return null;
    }

    @Override
    public List<AttIvsDeviceSelectItem> getIvsDeviceByDeviceIds(Collection<String> deviceIds) {
        List<AttIvsDeviceSelectItem> selectItemList = new ArrayList<>();
        if (Objects.nonNull(attGetIvsDeviceService)) {
            List<Att4IvsChannelSelectItem> ivsChannelSelectItemList =
                attGetIvsDeviceService.getIvsChannelByIds(deviceIds);
            if (!CollectionUtil.isEmpty(ivsChannelSelectItemList)) {
                selectItemList = ModelUtil.copyListProperties(ivsChannelSelectItemList, AttIvsDeviceSelectItem.class);
            }
        }
        return selectItemList;
    }

    @Override
    public Pager getSelectIvsDevicePager(AttIvsDeviceSelectItem condition, int page, int size) {
        if (Objects.nonNull(attGetIvsDeviceService)) {
            Att4IvsChannelSelectItem att4IvsChannelSelectItem = new Att4IvsChannelSelectItem();
            ModelUtil.copyPropertiesIgnoreNull(condition, att4IvsChannelSelectItem);
            List<String> deviceModules = attPointService.getDeviceIdsByDeviceModule(ConstUtil.SYSTEM_MODULE_IVS);
            String deviceModuleStr = StringUtils.join(deviceModules, AttConstant.COMMA);
            if (StringUtils.isNotBlank(deviceModuleStr)) {
                att4IvsChannelSelectItem.setSelectId(condition.getSelectId() + AttConstant.COMMA + deviceModuleStr);
            } else {
                att4IvsChannelSelectItem.setSelectId(condition.getSelectId());
            }
            Pager pager = attGetIvsDeviceService.getIvsFaceChannelSelectItemList(att4IvsChannelSelectItem, page, size);
            List<Att4IvsChannelSelectItem> ivsChannelSelectItemList = (List<Att4IvsChannelSelectItem>)pager.getData();
            List<AttIvsDeviceSelectItem> attIvsDeviceSelectItemList =
                ModelUtil.copyListProperties(ivsChannelSelectItemList, AttIvsDeviceSelectItem.class);
            pager.setData(attIvsDeviceSelectItemList);
            return pager;
        } else {
            Pager pager = new Pager(page, size);
            pager.setData(new ArrayList<>());
            return pager;
        }
    }

    @Override
    public List<AttIvsParentDeviceItem> getDeviceTree(String sessionId) {
        if (Objects.isNull(attGetIvsDeviceService)) {
            return new ArrayList<>();
        }
        List<Att4IvsParentDeviceItem> att4IvsParentDeviceItems = attGetIvsDeviceService.getDeviceTree(sessionId);
        List<AttIvsParentDeviceItem> attIvsParentDeviceItems =
            ModelUtil.copyListProperties(att4IvsParentDeviceItems, AttIvsParentDeviceItem.class);
        return attIvsParentDeviceItems;
    }
}