package com.zkteco.zkbiosecurity.att.service.impl;

import com.zkteco.zkbiosecurity.att.service.AttTransactionService;
import com.zkteco.zkbiosecurity.att.vo.AttTransactionItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.hep.service.HepGetAttTransactionService;
import com.zkteco.zkbiosecurity.hep.vo.Hep4OtherTransactionItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 
 * 防疫获取考勤防疫事件记录
 * 
 * <AUTHOR>
 * @date 2020/12/10 14:21
 * @since 1.0.0
 */
@Service
public class HepGetAttTransactionServiceImpl implements HepGetAttTransactionService {

    @Autowired
    private AttTransactionService attTransactionService;

    @Override
    public Integer countAttTransactionByCreateTime(String devSn, Date startDateTime, Date endDateTime) {
        return attTransactionService.countAttTransactionByCreateTime(devSn, startDateTime, endDateTime);
    }

    @Override
    public Pager hepGetAttTransactionList(String devSn, Date startDateTime, Date endDateTime, int page, int size) {

        AttTransactionItem condition = new AttTransactionItem();
        condition.setEquals(true);
        condition.setDeviceSn(devSn);
        condition.setStartCreateTime(startDateTime);
        condition.setEndCreateTime(endDateTime);
        Pager pager = attTransactionService.getItemsByPage(condition, page, size);
        List<AttTransactionItem> attTransactionItemList = (List<AttTransactionItem>)pager.getData();

        List<Hep4OtherTransactionItem> list = new ArrayList<>();
        Hep4OtherTransactionItem hep4OtherTransactionItem = null;
        if (!CollectionUtil.isEmpty(attTransactionItemList)) {
            for (AttTransactionItem attTransactionItem : attTransactionItemList) {

                hep4OtherTransactionItem = new Hep4OtherTransactionItem();

                hep4OtherTransactionItem.setEventTime(attTransactionItem.getAttDatetime());
                hep4OtherTransactionItem.setDevId(attTransactionItem.getDeptId());
                hep4OtherTransactionItem.setDevSn(attTransactionItem.getDeviceSn());
                hep4OtherTransactionItem.setDevAlias(attTransactionItem.getDeviceName());
                hep4OtherTransactionItem.setDeptId(attTransactionItem.getDeptId());
                hep4OtherTransactionItem.setDeptCode(attTransactionItem.getDeptCode());
                hep4OtherTransactionItem.setDeptName(attTransactionItem.getDeptName());
                hep4OtherTransactionItem.setPin(attTransactionItem.getPersonPin());
                hep4OtherTransactionItem.setName(attTransactionItem.getPersonName());
                hep4OtherTransactionItem.setLastName(attTransactionItem.getPersonLastName());
                hep4OtherTransactionItem.setAreaCode(attTransactionItem.getAreaNo());
                hep4OtherTransactionItem.setAreaName(attTransactionItem.getAreaName());
                hep4OtherTransactionItem.setMaskFlag(attTransactionItem.getMaskFlag());
                hep4OtherTransactionItem.setTemperature(attTransactionItem.getTemperature());
                hep4OtherTransactionItem.setIsFrom(ConstUtil.SYSTEM_MODULE_ATT);
                hep4OtherTransactionItem.setAreaId(attTransactionItem.getAreaId());
                // 增加事件点名称和照片路径 (考勤设备记录和照片是分开上传或设备未开启图片上传,实时推送的未能获取到路径)
                hep4OtherTransactionItem.setEventPointName(attTransactionItem.getDeviceName());
                hep4OtherTransactionItem.setVidLinkageHandle(attTransactionItem.getAttPhotoUrl());

                list.add(hep4OtherTransactionItem);
            }
            pager.setData(list);
        }
        return pager;
    }
}
