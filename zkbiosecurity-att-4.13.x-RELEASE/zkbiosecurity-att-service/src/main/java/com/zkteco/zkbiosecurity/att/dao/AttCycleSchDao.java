package com.zkteco.zkbiosecurity.att.dao;

import com.zkteco.zkbiosecurity.att.model.AttCycleSch;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 周期排班（分组/部门/人员）
 * 
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 10:31 2020/5/6
 */
public interface AttCycleSchDao extends BaseDao<AttCycleSch, String> {

    @Query("select t from AttCycleSch t where t.personId in ?1 and t.startDate <= ?3 and t.endDate >= ?2 Order by startDate")
    List<AttCycleSch> findByPersonIdInAndStartDateAndEndDate(List<String> personIdList, Date startDate, Date endDate);

    @Query("select t from AttCycleSch t where t.deptId in ?1 and t.startDate <= ?3 and t.endDate >= ?2 and t.cycleType = 1 Order by startDate")
    List<AttCycleSch> findByDeptIdInAndStartDateAndEndDate(List<String> deptIdList, Date startDate, Date endDate);

    @Query("select t from AttCycleSch t where t.groupId in ?1 and  t.startDate <= ?3 and t.endDate >= ?2 Order by startDate")
    List<AttCycleSch> findByGroupIdInAndStartDateAndEndDate(List<String> groupIdList, Date startDate, Date endDate);

    @Query("select t from AttCycleSch t where t.cycleType = ?1 and  t.startDate <= ?3 and t.endDate >= ?2 Order by startDate")
    List<AttCycleSch> findByCycleTypeAndStartDateAndEndDate(Short cycleType, Date startDate, Date endDate);

    @Query("select t from AttCycleSch t where t.startDate <= ?2 and t.endDate >= ?1")
    List<AttCycleSch> findByStartDateAndEndDate(Date startDate, Date endDate);

    @Query("select t from AttCycleSch t where t.personPin in ?1 and t.startDate <= ?3 and t.endDate >= ?2 and t.cycleType = 2")
    List<AttCycleSch> findPersonCycleByPersonPinInAndStartDateAndEndDate(Collection<String> personPinList, Date startDate, Date endDate);

    @Query("select t from AttCycleSch t where t.deptId in ?1 and t.startDate <= ?3 and t.endDate >= ?2 and t.cycleType = 1")
    List<AttCycleSch> findDeptCycleByDeptIdInAndStartDateAndEndDate(Collection<String> deptIdList, Date startDate, Date endDate);

    @Query("select t from AttCycleSch t where t.groupId in ?1 and t.startDate <= ?3 and t.endDate >= ?2 and t.cycleType = 0")
    List<AttCycleSch> findGroupCycleByGroupIdInAndStartDateAndEndDate(Collection<String> groupIdList, Date startDate, Date endDate);

    List<AttCycleSch> findByGroupIdIn(List<String> groupIdList);

    Long countByDeptId(String deptId);

    void deleteByPersonIdIn(Collection<String> personIdList);

    void deleteByPersonPinIn(Collection<String> pins);
}