package com.zkteco.zkbiosecurity.att.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.att.dao.AttCycleSchDao;
import com.zkteco.zkbiosecurity.att.dao.AttTempSchDao;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartment4OtherService;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

/**
 * 判断部门是否在考勤模块使用
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/3/12 9:48
 * @since 1.0.0
 */
@Service
public class AttDepartmentExtServiceImpl implements AuthDepartment4OtherService {

    @Autowired
    private AttCycleSchDao attCycleSchDao;
    @Autowired
    private AttTempSchDao attTempSchDao;

    @Override
    public void checkDepartmentIsUsed(String deptId) {
        // 判断部门是否存在排班情况
        if (attCycleSchDao.countByDeptId(deptId) > 0) {
            // att_deptSch_existsDept
            throw ZKBusinessException
                .warnException(I18nUtil.i18nCode("auth_dept_isUsed", I18nUtil.i18nCode("att_module")));
        }
        // 判断部门是否存在临时排班情况
        if (attTempSchDao.countByDeptId(deptId) > 0) {
            // att_tempSch_existsDept
            throw ZKBusinessException
                .warnException(I18nUtil.i18nCode("auth_dept_isUsed", I18nUtil.i18nCode("att_module")));
        }

    }
}
