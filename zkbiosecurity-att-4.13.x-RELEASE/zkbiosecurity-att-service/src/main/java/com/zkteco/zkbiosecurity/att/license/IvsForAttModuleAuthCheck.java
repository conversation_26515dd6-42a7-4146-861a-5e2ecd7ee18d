//package com.zkteco.zkbiosecurity.att.license;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import com.zkteco.zkbiosecurity.att.service.AttPointService;
//import com.zkteco.zkbiosecurity.core.guard.annotation.InmutableClassSign;
//import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
//import com.zkteco.zkbiosecurity.license.vo.IModuleAuthDefault;
//
///**
// * ivs视频当考勤许可-登录检测
// *
// * <AUTHOR>
// * @date 2021-02-04 14:02
// * @since 1.0.0
// */
//@Component
//@InmutableClassSign(module = ConstUtil.SYSTEM_MODULE_ATT)
//public class IvsForAttModuleAuthCheck implements IModuleAuthDefault {
//
//    @Autowired
//    private AttPointService attPointService;
//
//    @Override
//    public boolean enable() {
//        return true;
//    }
//
//    @Override
//    public String module() {
//        return ConstUtil.LICENSE_MODULE_IVSATT;
//    }
//
//    @Override
//    public int controlCount() {
//        return attPointService.getCountByDeviceModule(ConstUtil.SYSTEM_MODULE_IVS);
//    }
//}
