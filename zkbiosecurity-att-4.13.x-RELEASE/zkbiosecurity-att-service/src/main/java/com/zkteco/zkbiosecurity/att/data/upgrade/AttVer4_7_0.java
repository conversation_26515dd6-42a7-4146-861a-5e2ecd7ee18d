package com.zkteco.zkbiosecurity.att.data.upgrade;

import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.service.AttLeaveTypeService;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 升级到4.7.0
 * <p>升级假种排序初始化值</p>
 *
 */
@Slf4j
@Component
public class AttVer4_7_0 implements UpgradeVersionManager {

    @Autowired
    private AttLeaveTypeService attLeaveTypeService;

    @Override
    public String getModule() {
        return BaseConstants.ATT;
    }

    @Override
    public String getVersion() {
        return "v4.7.0";
    }

    @Override
    public boolean executeUpgrade() {

        List<AttLeaveTypeItem> attLeaveTypeItemList = new ArrayList<>();
        AttLeaveTypeItem leaveThing = new AttLeaveTypeItem("L1", 1);
        attLeaveTypeItemList.add(leaveThing);

        AttLeaveTypeItem leaveAnnual = new AttLeaveTypeItem("L5", 2);
        attLeaveTypeItemList.add(leaveAnnual);

        AttLeaveTypeItem leaveSick = new AttLeaveTypeItem("L4", 3);
        attLeaveTypeItemList.add(leaveSick);

        AttLeaveTypeItem leaveMarriage =
                new AttLeaveTypeItem("L2", 4);
        attLeaveTypeItemList.add(leaveMarriage);

        AttLeaveTypeItem leaveBirth = new AttLeaveTypeItem("L3", 5);
        attLeaveTypeItemList.add(leaveBirth);

        AttLeaveTypeItem leaveNursing =
                new AttLeaveTypeItem("L8", 6);
        attLeaveTypeItemList.add(leaveNursing);

        AttLeaveTypeItem leaveHome = new AttLeaveTypeItem("L7", 7);
        attLeaveTypeItemList.add(leaveHome);

        AttLeaveTypeItem leaveFuneral =
                new AttLeaveTypeItem("L6", 8);
        attLeaveTypeItemList.add(leaveFuneral);

        AttLeaveTypeItem trip =
                new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.TRIP, 9);
        attLeaveTypeItemList.add(trip);

        AttLeaveTypeItem out =
                new AttLeaveTypeItem(AttCalculationConstant.AttAttendStatus.OUT, 10);
        attLeaveTypeItemList.add(out);

        attLeaveTypeService.updateSortNoByLeaveTypeNo(attLeaveTypeItemList);

        return true;
    }

}
