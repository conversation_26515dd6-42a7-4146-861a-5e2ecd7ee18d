package com.zkteco.zkbiosecurity.att.enums;

import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

/**
 * 调整类型-枚举
 * <p>
 * 调整类型（0/个人同日期调班，1/个人不同日期调班, 2/两人对调）
 * 
 * <AUTHOR>
 * @version 0.0.1
 * @since 2017年8月29日 13:43:45
 */
public enum AttClassTypeEnum {
    /**
     * exceptionType值含义
     */
    SameDate("0", I18nUtil.i18nCode("att_class_sameTimeMoveShift")),
    DiffDate("1", I18nUtil.i18nCode("att_class_differenceTimeMoveShift")),
    TwoSwap("2", I18nUtil.i18nCode("att_class_twoPeopleMove"));

    /** 值 */
    private String value;

    /** 描述 */
    private String desc;

    AttClassTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

}
