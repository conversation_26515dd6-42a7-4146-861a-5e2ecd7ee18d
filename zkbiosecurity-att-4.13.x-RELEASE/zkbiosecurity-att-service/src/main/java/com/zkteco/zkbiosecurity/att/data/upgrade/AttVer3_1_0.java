package com.zkteco.zkbiosecurity.att.data.upgrade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 16:40 2020/4/21
 */
@Slf4j
@Component
public class AttVer3_1_0 implements UpgradeVersionManager {

    @Autowired
    private AuthPermissionService authPermissionService;

    @Override
    public String getVersion() {
        return "v3.1.0";
    }

    @Override
    public String getModule() {
        return BaseConstants.ATT;
    }

    @Override
    public boolean executeUpgrade() {

        // 开启调班菜单
        enableAttClassMenu();

        return true;
    }

    /**
     * 开启调班菜单 如果已存在则开启,不存在则新建
     * 
     * @return void
     * <AUTHOR>
     * @date 2021-02-20 17:34
     * @since 1.0.0
     */
    private void enableAttClassMenu() {
        AuthPermissionItem menu = authPermissionService.getItemByCode("AttClass");

        if (menu == null) {
            AuthPermissionItem parentMenuItem = authPermissionService.getItemByCode("AttExceptionManagement");
            if (null == parentMenuItem) {
                return;
            }
            AuthPermissionItem childMenuItem;
            AuthPermissionItem buttonMenuItem;
            childMenuItem = new AuthPermissionItem("AttClass", "att_leftMenu_class", "att:class",
                AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 7);
            childMenuItem.setParentId(parentMenuItem.getId());
            childMenuItem.setActionLink("attClass.do");
            childMenuItem = authPermissionService.saveItem(childMenuItem);
            // 刷新
            buttonMenuItem = new AuthPermissionItem("AttClassRefresh", "common_op_refresh", "att:class:refresh",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
            buttonMenuItem.setParentId(childMenuItem.getId());
            buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
            // 新增
            buttonMenuItem = new AuthPermissionItem("AttClassAdd", "common_op_new", "att:class:add",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
            buttonMenuItem.setParentId(childMenuItem.getId());
            buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
            // 删除
            buttonMenuItem = new AuthPermissionItem("AttClassDel", "common_op_del", "att:class:del",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
            buttonMenuItem.setParentId(childMenuItem.getId());
            buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
            // 导出
            buttonMenuItem = new AuthPermissionItem("AttClassExport", "common_op_export", "att:class:export",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
            buttonMenuItem.setParentId(childMenuItem.getId());
            buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);
            // 导入
            buttonMenuItem = new AuthPermissionItem("AttClassImport", "common_op_import", "att:class:import",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
            buttonMenuItem.setParentId(childMenuItem.getId());
            buttonMenuItem = authPermissionService.saveItem(buttonMenuItem);

        } else if (!ZKConstant.TRUE.equals(menu.getAvailable())) {
            menu.setAvailable(ZKConstant.TRUE);
            authPermissionService.saveItem(menu);
        }
    }
}
