package com.zkteco.zkbiosecurity.att.model;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 班次
 */
@Entity
@Table(name = "ATT_SHIFT")
@Setter
@Getter
@Accessors(chain = true)
public class AttShift extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 班次类型(0=规律、1=弹性)
     */
    @Column(name = "SHIFT_TYPE")
    private Short shiftType;

    /**
     * 班次编号
     */
    @Column(name = "SHIFT_NO", length = 50)
    private String shiftNo;

    /**
     * 班次名称
     */
    @Column(name = "SHIFT_NAME", length = 50)
    private String shiftName;

    /**
     * 颜色
     */
    @Column(name = "SHIFT_COLOR")
    private String shiftColor;

    /**
     * 周期单位（0/天，1/周，2/月）
     */
    @Column(name = "PERIODIC_UNIT")
    private Short periodicUnit;

    /**
     * 周期数
     */
    @Column(name = "PERIOD_NUMBER")
    private Short periodNumber;

    /**
     * 周期起始方式(0/按起始日期, 1/按排班日期)
     */
    @Column(name = "PERIOD_START_MODE")
    private String periodStartMode;

    /**
     * 起始日期
     */
    @Column(name = "START_DATE")
    private Date startDate;

    /**
     * 是否月内轮班（false：否/0，true：是/1）
     */
    @Column(name = "IS_SHIFT_WITHIN_MONTH")
    private Boolean isShiftWithinMonth;

    /**
     * 时间段明细ids(SqlServer nvarchar类型字段最大容纳的是4000，目前就暂定4000定长（其他三种数据库都支持）)
     */
    @Column(name = "TIME_SLOT_DETAIL_IDS")
    @Lob
    private String timeSlotDetailIds;

    /**
     * 工作类型（正常上班/周末加班/节假日加班）
     */
    @Column(name = "SHIFT_WORKTYPE")
    private String workType;

    /**
     * 考勤方式（0/按班次正常刷卡，1/一天内任刷一次有效卡，2/只计算刷卡时间，3/免刷卡）
     */
    @Column(name = "ATTENDANCE_MODE")
    private Short attendanceMode;

    /**
     * 加班方式（0/电脑自动计算，1/加班必须申请，2/必须加班否则旷工，3/时长为较小者-延后加班和加班单对比，4/不算加班）
     */
    @Column(name = "OVERTIME_MODE")
    private Short overtimeMode;

    /**
     * 加班标记（0/平时，1/休息日，2/节假日）
     */
    @Column(name = "OVERTIME_SIGN")
    private Short overtimeSign;

    /**
     * 时间段
     */
    @ManyToMany(targetEntity = AttTimeSlot.class, fetch = FetchType.LAZY)
    @JoinTable(name = "ATT_SHIFT_TIMESLOT", joinColumns = @JoinColumn(name = "SHIFT_ID", referencedColumnName = "ID"),
        inverseJoinColumns = @JoinColumn(name = "TIMESLOT_ID", referencedColumnName = "ID"))
    private Set<AttTimeSlot> attTimeSlotSet = new HashSet<AttTimeSlot>();

    /**
     * 周期排班
     */
    @ManyToMany(targetEntity = AttCycleSch.class, fetch = FetchType.LAZY)
    @JoinTable(name = "ATT_CYCLESCH_SHIFT", joinColumns = @JoinColumn(name = "SHIFT_ID", referencedColumnName = "ID"),
        inverseJoinColumns = @JoinColumn(name = "CYCLESCH_ID", referencedColumnName = "ID"))
    private Set<AttCycleSch> attCycleSchSet = new HashSet<AttCycleSch>();

    /**
     * 分组排班
     */
    @ManyToMany(targetEntity = AttGroupSch.class, fetch = FetchType.LAZY)
    @JoinTable(name = "ATT_GROUPSCH_SHIFT", joinColumns = @JoinColumn(name = "SHIFT_ID", referencedColumnName = "ID"),
        inverseJoinColumns = @JoinColumn(name = "GROUPSCH_ID", referencedColumnName = "ID"))
    private Set<AttGroupSch> attGroupSchSet = new HashSet<AttGroupSch>();

    /**
     * 部门排班
     */
    @ManyToMany(targetEntity = AttDeptSch.class, fetch = FetchType.LAZY)
    @JoinTable(name = "ATT_DEPTSCH_SHIFT", joinColumns = @JoinColumn(name = "SHIFT_ID", referencedColumnName = "ID"),
        inverseJoinColumns = @JoinColumn(name = "DEPTSCH_ID", referencedColumnName = "ID"))
    private Set<AttDeptSch> attDeptSchSet = new HashSet<AttDeptSch>();

    /**
     * 人员排班
     */
    @ManyToMany(targetEntity = AttPersonSch.class, fetch = FetchType.LAZY)
    @JoinTable(name = "ATT_PERSONSCH_SHIFT", joinColumns = @JoinColumn(name = "SHIFT_ID", referencedColumnName = "ID"),
        inverseJoinColumns = @JoinColumn(name = "PERSONSCH_ID", referencedColumnName = "ID"))
    private Set<AttPersonSch> attPersonSchSet = new HashSet<AttPersonSch>();

    // /**
    // * 临时排班
    // */
    // @ManyToMany(targetEntity = AttTempSch.class, fetch = FetchType.LAZY)
    // @JoinTable(name = "ATT_TEMPSCH_SHIFT", joinColumns = @JoinColumn(name = "SHIFT_ID", referencedColumnName = "ID"),
    // inverseJoinColumns = @JoinColumn(name = "TEMPSCH_ID", referencedColumnName = "ID"))
    // private Set<AttTempSch> attTempSchSet = new HashSet<AttTempSch>();
}