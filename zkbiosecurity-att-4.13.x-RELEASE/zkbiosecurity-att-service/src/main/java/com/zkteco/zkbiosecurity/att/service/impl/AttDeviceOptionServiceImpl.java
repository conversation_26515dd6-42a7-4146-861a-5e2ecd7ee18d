package com.zkteco.zkbiosecurity.att.service.impl;

import com.zkteco.zkbiosecurity.att.constants.AttDeviceConstant;
import com.zkteco.zkbiosecurity.att.dao.AttDeviceDao;
import com.zkteco.zkbiosecurity.att.dao.AttDeviceOptionDao;
import com.zkteco.zkbiosecurity.att.model.AttDevice;
import com.zkteco.zkbiosecurity.att.model.AttDeviceOption;
import com.zkteco.zkbiosecurity.att.service.AttDeviceOptionService;
import com.zkteco.zkbiosecurity.att.vo.AttDeviceItem;
import com.zkteco.zkbiosecurity.att.vo.AttDeviceOptionItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 设备参数
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:28
 * @since 1.0.0
 */
@Service
public class AttDeviceOptionServiceImpl implements AttDeviceOptionService {

    @Autowired
    private AttDeviceOptionDao attDeviceOptionDao;
    @Autowired
    private AttDeviceDao attDeviceDao;

    @Override
    @Transactional
    public AttDeviceOptionItem saveItem(AttDeviceOptionItem item) {
        AttDeviceOption attDeviceOption = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> attDeviceOptionDao.findById(id)).orElse(new AttDeviceOption());
        ModelUtil.copyPropertiesIgnoreNull(item, attDeviceOption);
        attDeviceOptionDao.save(attDeviceOption);
        item.setId(attDeviceOption.getId());
        return item;
    }

    @Override
    public List<AttDeviceOptionItem> getByCondition(AttDeviceOptionItem condition) {
        return (List<AttDeviceOptionItem>)attDeviceOptionDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return attDeviceOptionDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                attDeviceOptionDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AttDeviceOptionItem getItemById(String id) {
        List<AttDeviceOptionItem> items = getByCondition(new AttDeviceOptionItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    /**
     * 更新设备参数信息
     *
     * @param attDevice
     * @param key
     * @param value
     */
    @Override
    @Transactional
    public void upadateDeviceOption(AttDeviceItem attDevice, String key, String value) {
        AttDeviceOption attDeviceOption = attDeviceOptionDao.findByAttDevice_IdAndName(attDevice.getId(), key);
        if (attDeviceOption == null) {
            attDeviceOption = new AttDeviceOption();
            attDeviceOption.setAttDevice(attDeviceDao.findById(attDevice.getId()).get());
        }
        attDeviceOption.setName(key);
        attDeviceOption.setValue(value);
        if (attDeviceOption.getId() == null || !(value + "").equals(attDeviceOption.getValue())) {
            // 避免不停更新数据库，做了value比较判断，有修改才更新 add by max
            attDeviceOptionDao.save(attDeviceOption);
        }
    }

    @Override
    @Transactional
    public void updateDeviceOption(String devSn, Map<String, String> optionsMap) {
        List<AttDeviceOption> optionList = attDeviceOptionDao.findByAttDevice_DevSn(devSn);
        AttDevice attDevice = attDeviceDao.findByDevSn(devSn);
        if (CollectionUtil.isEmpty(optionList)) {// 全部新增
            optionsMap.forEach((k, v) -> {
                if (!"SN".equals(k)) {
                    attDeviceOptionDao.save(new AttDeviceOption(attDevice, k, v));
                }
            });
        } else {
            Map<String, AttDeviceOption> keyOptions =
                optionList.stream().collect(Collectors.toMap(AttDeviceOption::getName, Function.identity()));
            optionsMap.forEach((k, v) -> {
                if (!"SN".equals(k)) {
                    AttDeviceOption attDeviceOption = keyOptions.get(k);
                    if (Objects.nonNull(attDeviceOption)) {
                        if (StringUtils.isNotBlank(attDeviceOption.getValue())) {
                            if (!attDeviceOption.getValue().equals(v)) {// 值不同，编辑
                                attDeviceOption.setValue(v);
                                attDeviceOptionDao.save(attDeviceOption);
                            }
                        } else if (StringUtils.isNotBlank(v)) {
                            attDeviceOption.setValue(v);
                            attDeviceOptionDao.save(attDeviceOption);
                        }
                    } else {// 新增
                        attDeviceOptionDao.save(new AttDeviceOption(attDevice, k, v));
                    }
                }
            });
        }
    }

    @Override
    public Map<String, String> getOptionMapByDevId(String devId) {
        Map<String, String> optionMap = new HashMap<>();
        List<AttDeviceOption> optionList = attDeviceOptionDao.findByAttDevice_Id(devId);
        for (AttDeviceOption attDeviceOption : optionList) {
            optionMap.put(attDeviceOption.getName(), attDeviceOption.getValue());
        }
        return optionMap;
    }

    @Override
    public boolean isSupportFunction(String devId, String devOption) {
        AttDeviceOption attDeviceOption = attDeviceOptionDao.findByAttDevice_IdAndName(devId, devOption);
        if (Objects.nonNull(attDeviceOption)) {
            if ("1".equals(attDeviceOption.getValue())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean isSupportFunctionBySn(String deviceSn, String devOption) {
        String value = attDeviceOptionDao.getValueByDevSnAndName(deviceSn, devOption);
        return "1".equals(value);
    }

    @Override
    public boolean isSupportFunList(String devSn, int index) {
        String value = attDeviceOptionDao.getValueByDevSnAndName(devSn, "AttSupportFunList");
        if (StringUtils.isNotBlank(value) && value.length() > index) {
            return value.charAt(index) == '1';
        }
        return false;
    }

    @Override
    public String getValueByDevSnAndName(String devSn, String name) {
        return attDeviceOptionDao.getValueByDevSnAndName(devSn, name);
    }

    @Override
    @Transactional
    public void deleteByDevId(String devId) {
        attDeviceOptionDao.deleteByAttDevice_Id(devId);
    }

    @Override
    public List<String> getSupportFunctionsDevSn(List<String> functions) {
        List<String> list = attDeviceOptionDao.findSupportFunctionsDevSn(functions);
        return list;
    }

    @Override
    public boolean isSupportMaskOrTempBySn(String sn) {
        List<String> functions = new ArrayList<String>(){{add("MaskDetectionFunOn"); add("IRTempDetectionFunOn");}};
        Long count = attDeviceOptionDao.isSupportOrFunctionsBySn(sn, functions);
        return count > 0;
    }

    @Override
    @Transactional
    public Map<String, List<AttDeviceOptionItem>> getDeviceIdAndDeviceOptionMap(Collection<String> devIds) {
        if (CollectionUtil.isEmpty(devIds)) {
            return new HashMap<>();
        }

        List<AttDeviceOption> devOptionList = attDeviceOptionDao.findByDevIdInAndNameIn(new ArrayList<>(devIds),
                StrUtil.strToList(AttDeviceConstant.DEVOPTIONS));
        if (CollectionUtil.isEmpty(devOptionList)) {
            return new HashMap<>();
        }

        Map<String, List<AttDeviceOptionItem>> deviceIdAndDeviceOptionMap = new HashMap<>();
        for (AttDeviceOption attDeviceOption: devOptionList) {
            String devId = attDeviceOption.getAttDevice().getId();
            AttDeviceOptionItem attDeviceOptionItem = new AttDeviceOptionItem();
            ModelUtil.copyProperties(attDeviceOption, attDeviceOptionItem);

            List<AttDeviceOptionItem> attDeviceOptionItemList = null;
            if (deviceIdAndDeviceOptionMap.containsKey(devId)) {
                attDeviceOptionItemList = deviceIdAndDeviceOptionMap.get(devId);
            } else {
                attDeviceOptionItemList = new ArrayList<>();
            }
            attDeviceOptionItemList.add(attDeviceOptionItem);
            deviceIdAndDeviceOptionMap.put(devId, attDeviceOptionItemList);
        }

        return deviceIdAndDeviceOptionMap;
    }
}