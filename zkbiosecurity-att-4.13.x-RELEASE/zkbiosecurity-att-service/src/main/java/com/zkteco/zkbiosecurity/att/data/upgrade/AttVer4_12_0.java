package com.zkteco.zkbiosecurity.att.data.upgrade;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.system.constants.BaseDataConstants;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2024/7/1 14:29
 * @since 1.0.0
 */
@Slf4j
@Component
public class AttVer4_12_0 implements UpgradeVersionManager {

    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AuthPermissionService authPermissionService;

    @Override
    public String getModule() {
        return BaseConstants.ATT;
    }

    @Override
    public String getVersion() {
        return "v4.12.0";
    }

    @Override
    public boolean executeUpgrade() {

        // 菜单升级
        upgradeMenu();

        // 参数升级
        upgradeParam();

        return true;
    }

    private void upgradeMenu() {
        AuthPermissionItem systemMenuItem = authPermissionService.getItemByCode("Att");
        if (systemMenuItem != null) {
            /** --------------------------- 敏感信息保护控制 --------------------------- */
            AuthPermissionItem parentMenuItem = new AuthPermissionItem("AttEncryptProp", "common_param_infoProtection",
                "att:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 999);
            parentMenuItem.setParentId(systemMenuItem.getId());
            parentMenuItem = authPermissionService.saveItem(parentMenuItem);
            AuthPermissionItem childMenuItem = new AuthPermissionItem("AttPinEncryptProp", "pers_person_pin",
                "att:pin:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 1);
            childMenuItem.setParentId(parentMenuItem.getId());
            authPermissionService.saveItem(childMenuItem);
            childMenuItem = new AuthPermissionItem("AttNameEncryptProp", "pers_person_wholeName",
                "att:name:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 2);
            childMenuItem.setParentId(parentMenuItem.getId());
            authPermissionService.saveItem(childMenuItem);
            childMenuItem = new AuthPermissionItem("AttCapturePhotoEncryptProp", "pers_capture_catchPhoto",
                "att:capturePhoto:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 3);
            childMenuItem.setParentId(parentMenuItem.getId());
            authPermissionService.saveItem(childMenuItem);
        }

        AuthPermissionItem attRule = authPermissionService.getItemByCode("AttRule");
        if (attRule != null) {
            // 加班等级设置
            AuthPermissionItem buttonMenuItem = new AuthPermissionItem("AttRuleOverTimeLevelSet", "att_param_overTimeSetting", "att:rule:overTimeLevelSet",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
            buttonMenuItem.setParentId(attRule.getId());
            authPermissionService.saveItem(buttonMenuItem);
        }
    }

    private void upgradeParam() {

        BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("attReportDataClean");
        if (StringUtils.isNotBlank(baseSysParamItem.getId())) {
            String paramValue = baseSysParamItem.getParamValue();
            JSONObject json = JSON.parseObject(paramValue);
            json.put("keptPhoto", "15");
            json.put("keptPhotoType", BaseDataConstants.DATA_CLEAN_KEPTMONTH);
            baseSysParamItem.setParamValue(json.toString());
            baseSysParamService.saveItem(baseSysParamItem);
        }

        // 是否启用加班等级计算（0启用、1禁用、默认为禁用）
        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.enable", "1", "是否启用加班等级计算"));

        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.normalOT1", "0-1", "平时加班OT1"));
        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.normalOT2", "1-2", "平时加班OT2"));
        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.normalOT3", "2-3", "平时加班OT3"));

        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.restOT1", "0-4", "休息日加班OT1"));
        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.restOT2", "4-6", "休息日加班OT2"));
        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.restOT3", "6-8", "休息日加班OT3"));

        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.holidayOT1", "0-4", "节假日加班OT1"));
        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.holidayOT2", "4-6", "节假日加班OT2"));
        baseSysParamService.initData(new BaseSysParamItem("att.overtimeLevel.holidayOT3", "6-8", "节假日加班OT3"));
    }
}
