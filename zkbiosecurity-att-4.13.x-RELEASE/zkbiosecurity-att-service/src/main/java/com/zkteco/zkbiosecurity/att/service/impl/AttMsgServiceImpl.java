package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.api.msg.vo.AppNotifyMsgMessageItem;
import com.zkteco.zkbiosecurity.att.api.msg.vo.AppTodoMsgMessageItem;
import com.zkteco.zkbiosecurity.att.service.AttMsgService;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.auth.constants.AppConstant;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKPageResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.system.app.constants.BaseAppConstants;
import com.zkteco.zkbiosecurity.system.service.BaseMessageService;
import com.zkteco.zkbiosecurity.system.vo.BaseMessageItem;

/**
 * 云端消息推送
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:29
 * @since 1.0.0
 */
@Service
public class AttMsgServiceImpl implements AttMsgService {

    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private BaseMessageService baseMessageService;

    public Pager getMessagePager(PersPersonItem persPersonItem, String type, int page, int pageSize,
        String businessCodes, String filter) {
        String queryCode = getQueryCodeByType(type, businessCodes);
        BaseMessageItem baseMessageItem = new BaseMessageItem();
        baseMessageItem.setType(type);
        baseMessageItem.setEquals(true);
        baseMessageItem.setSortOrder("desc");
        baseMessageItem.setSortName("remindTime");
        if (StringUtils.isNotBlank(queryCode)) {
            baseMessageItem.setBusinessCodeIn(queryCode);
        }
        if (BaseAppConstants.MESSAGE_TODO_MSG.equals(type)) {
            // 待办消息只取出未读消息
            baseMessageItem.setStatus("0");
        }

        baseMessageItem.setReceiverId(persPersonItem.getPin());
        Pager pager = baseMessageService.getItemsByFilter(baseMessageItem, filter, page, pageSize);
        return pager;
    }

    @Override
    public ZKPageResultMsg getTodoMsg(String customerId, int page, int pageSize, String businessCodes, String filter) {
        ZKPageResultMsg resultMsg = ZKPageResultMsg.successMsg();
        PersPersonItem persPersonItem = persPersonService.getItemById(customerId);
        if (persPersonItem == null) {
            // 人员不存在
            throw ZKBusinessException.errorException("auth_login_userNotExist");
        }
        Pager pager = getMessagePager(persPersonItem, BaseAppConstants.MESSAGE_TODO_MSG, page - 1, pageSize,
            businessCodes, filter);
        List<BaseMessageItem> baseMessageItemList = (List<BaseMessageItem>)pager.getData();
        List<AppTodoMsgMessageItem> appTodoMessageItems = new ArrayList<AppTodoMsgMessageItem>();
        if (baseMessageItemList != null && !baseMessageItemList.isEmpty()) {
            // SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); //设置格式
            for (BaseMessageItem messageItem : baseMessageItemList) {
                AppTodoMsgMessageItem appTodoMessageItem = new AppTodoMsgMessageItem();
                ModelUtil.copyPropertiesIgnoreNull(messageItem, appTodoMessageItem);
                if (messageItem.getRemindTime() != null) {
                    String timeText =
                        AttDateUtils.dateToStrAsLong(messageItem.getRemindTime());
                    appTodoMessageItem.setRemindTime(timeText);
                }
                JSONObject jsonObject = JSONObject.parseObject(messageItem.getExtendJson());
                // 姓名（人员编号）
                appTodoMessageItem.setPersonInfo(jsonObject.getString("personInfo"));
                // 待办消息扩展内容1
                appTodoMessageItem.setTodoContent1(jsonObject.getString("todoContent1"));
                // 待办消息扩展内容2
                appTodoMessageItem.setTodoContent2(jsonObject.getString("todoContent2"));
                String personPin = jsonObject.getString("personPin");
                // modify xjing.huang 待办消息为访客审批代表 设置访客头像
                if ("VisReservation".equals(messageItem.getBusinessCode())) {
                    appTodoMessageItem.setPhotoPath(jsonObject.getString("photoPath"));
                } else if (StringUtils.isNotBlank(personPin)) {
                    // 根据申请人pin获取申请人头像
                    PersPersonItem tempPerson = persPersonService.getItemByPin(personPin);
                    if (tempPerson != null) {
                        appTodoMessageItem.setPhotoPath(tempPerson.getPhotoPath());
                    }
                }
                appTodoMessageItems.add(appTodoMessageItem);
            }
        }
        resultMsg.setData(appTodoMessageItems);
        resultMsg.setPage(page);
        resultMsg.setPageSize(pageSize);
        resultMsg.setTotal(pager.getTotal());
        return resultMsg;
    }

    @Override
    public ZKPageResultMsg getNotifyMsg(String customerId, int page, int pageSize, String businessCodes,
        String filter) {
        ZKPageResultMsg resultMsg = ZKPageResultMsg.successMsg();
        PersPersonItem persPersonItem = persPersonService.getItemById(customerId);
        if (persPersonItem == null) {
            // 人员不存在
            throw ZKBusinessException.errorException("auth_login_userNotExist");
        }
        Pager pager = getMessagePager(persPersonItem, BaseAppConstants.MESSAGE_NOTIFY_MSG, page - 1, pageSize,
            businessCodes, filter);
        List<BaseMessageItem> baseMessageItemList = (List<BaseMessageItem>)pager.getData();
        List<AppNotifyMsgMessageItem> appNotifyMsgMessageItems = new ArrayList<AppNotifyMsgMessageItem>();
        if (baseMessageItemList != null && !baseMessageItemList.isEmpty()) {
            // SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); //设置格式
            for (BaseMessageItem messageItem : baseMessageItemList) {
                AppNotifyMsgMessageItem appNotifyMsgMessageItem = new AppNotifyMsgMessageItem();
                ModelUtil.copyPropertiesIgnoreNull(messageItem, appNotifyMsgMessageItem);
                if (messageItem.getRemindTime() != null) {
                    String timeText =
                        AttDateUtils.dateToStrAsLong(messageItem.getRemindTime());
                    appNotifyMsgMessageItem.setRemindTime(timeText);
                }
                JSONObject jsonObject = JSONObject.parseObject(messageItem.getExtendJson());
                // 姓名（人员编号）
                appNotifyMsgMessageItem.setPersonInfo(jsonObject.getString("personInfo"));
                // 知会消息扩展内容1
                appNotifyMsgMessageItem.setNotifyContent1(jsonObject.getString("notifyContent1"));
                // 知会消息扩展内容2
                appNotifyMsgMessageItem.setNotifyContent2(jsonObject.getString("notifyContent2"));
                String personPin = jsonObject.getString("personPin");
                if (StringUtils.isNotBlank(personPin)) {
                    // 根据申请人pin获取申请人头像
                    PersPersonItem tempPerson = persPersonService.getItemByPin(personPin);
                    if (tempPerson != null) {
                        appNotifyMsgMessageItem.setPhotoPath(tempPerson.getPhotoPath());
                    }
                }
                appNotifyMsgMessageItems.add(appNotifyMsgMessageItem);
            }
        }
        resultMsg.setData(appNotifyMsgMessageItems);
        resultMsg.setPage(page);
        resultMsg.setPageSize(pageSize);
        resultMsg.setTotal(pager.getTotal());
        return resultMsg;
    }

    @Override
    public ZKPageResultMsg changeMsgStatus(String messageIds) {
        ZKPageResultMsg resultMsg = ZKPageResultMsg.successMsg();
        if (StringUtils.isNotBlank(messageIds)) {
            BaseMessageItem baseMessageItem;
            for (String messageId : CollectionUtil.strToList(messageIds)) {
                baseMessageItem = baseMessageService.getItemById(messageId);
                if (baseMessageItem != null
                    && ("0".equals(baseMessageItem.getStatus()) || "1".equals(baseMessageItem.getStatus()))) {
                    baseMessageItem.setStatus("2");
                    baseMessageService.saveItem(baseMessageItem);
                }
            }
        }
        return resultMsg;
    }

    // 获取代办，知会，访客动态消息
    public JSONObject getTodeMsgorVistendencty(String msgType, String pin, String businessCodes) {
        // SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String queryCode = getQueryCodeByType(msgType, businessCodes);
        BaseMessageItem baseMessageItem = new BaseMessageItem();
        baseMessageItem.setType(msgType);
        baseMessageItem.setReceiverId(pin);
        baseMessageItem.setStatus("0");// 未读
        baseMessageItem.setSortOrder("desc");
        baseMessageItem.setSortName("remindTime");
        if (StringUtils.isNotBlank(queryCode)) {
            baseMessageItem.setBusinessCodeIn(queryCode);
        }
        baseMessageItem.setEquals(true);
        List<BaseMessageItem> baseMessageItemList = baseMessageService.getByCondition(baseMessageItem);
        JSONObject json = new JSONObject();
        if (CollectionUtil.isEmpty(baseMessageItemList)) {
            json.put("count", "0");
            json.put("lastContent", "");
            json.put("lastDate", "");
        } else {
            json.put("count", baseMessageItemList.size() + "");
            json.put("lastContent", StringUtils.isNotBlank(baseMessageItemList.get(0).getTitle())
                ? baseMessageItemList.get(0).getTitle() : "");
            if ("VisTendency".equals(msgType)) {
                json.put("lastContentId", baseMessageItemList.get(0).getId());
            }
            if (baseMessageItemList.get(0).getRemindTime() != null) {
                Date todoMsgdate = baseMessageItemList.get(0).getRemindTime();
                String LasttodoMsgdate = AttDateUtils.dateToStrAsLong(todoMsgdate);// sdf.format(todoMsgdate);
                json.put("lastDate", LasttodoMsgdate);
            } else {
                json.put("lastDate", "");
            }
        }
        return json;
    }

    // 获取知会信息
    public JSONObject getNotifyMsg(String msgType, String pin, String businessCodes) {
        // SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String queryCode = getQueryCodeByType(msgType, businessCodes);
        BaseMessageItem baseMessageItem = new BaseMessageItem();
        baseMessageItem.setType(msgType);
        baseMessageItem.setReceiverId(pin);
        baseMessageItem.setSortOrder("desc");
        baseMessageItem.setStatus("0");// 未读
        baseMessageItem.setSortName("remindTime");
        if (StringUtils.isNotBlank(queryCode)) {
            baseMessageItem.setBusinessCodeIn(queryCode);
        }
        baseMessageItem.setEquals(true);
        List<BaseMessageItem> baseMessageItemList = baseMessageService.getByCondition(baseMessageItem);
        JSONObject json = new JSONObject();
        if (CollectionUtil.isEmpty(baseMessageItemList)) {
            json.put("count", "0");
            json.put("lastContent", "");
            json.put("lastDate", "");
        } else {
            json.put("count", baseMessageItemList.size() + "");

            String msgId = CollectionUtil.getPropertys(baseMessageItemList, BaseMessageItem::getId);
            json.put("notifyMsgIds", msgId);

            json.put("lastContent", StringUtils.isNotBlank(baseMessageItemList.get(0).getTitle())
                ? baseMessageItemList.get(0).getTitle() : "");
            if ("VisTendency".equals(msgType)) {
                json.put("lastContentId", baseMessageItemList.get(0).getId());
            }
            if (baseMessageItemList.get(0).getRemindTime() != null) {
                Date todoMsgdate = baseMessageItemList.get(0).getRemindTime();
                String LasttodoMsgdate = AttDateUtils.dateToStrAsLong(todoMsgdate);// sdf.format(todoMsgdate);
                json.put("lastDate", LasttodoMsgdate);
            } else {
                json.put("lastDate", "");
            }
        }
        return json;
    }

    @Override
    public ZKPageResultMsg statisticalMsgCount(String customerId, String businessCodes) {
        ZKPageResultMsg resultMsg = ZKPageResultMsg.successMsg();
        JSONObject json = new JSONObject();
        PersPersonItem persPersonItem = persPersonService.getItemById(customerId);
        if (persPersonItem == null) {
            // 人员不存在
            throw ZKBusinessException.errorException("auth_login_userNotExist");
        }
        String pin = persPersonItem.getPin();
        // 待办消息
        JSONObject todoMsgjson = getTodeMsgorVistendencty("TodoMsg", pin, businessCodes);
        // 知会消息
        JSONObject notifyMsgjson = getNotifyMsg("NotifyMsg", pin, businessCodes);
        json.put("todoMsg", todoMsgjson);
        json.put("notifyMsg", notifyMsgjson);
        resultMsg.setData(json);
        return resultMsg;
    }

    private String getQueryCodeByType(String type, String businessCodes) {
        String businessCode = null;
        if (StringUtils.isNotBlank(businessCodes)) {
            if (StringUtils.isNotBlank(type)) {
                List<String> defaultCodeList = AppConstant.BUSINESS_CODE_MAP.get(type);
                if (!CollectionUtil.isEmpty(defaultCodeList)) {
                    List<String> queryList = (List<String>)CollectionUtil.strToList(businessCodes);
                    queryList.retainAll(defaultCodeList);
                    return StringUtils.join(queryList, ",");
                }
            }
        }
        return businessCode;
    }
}
