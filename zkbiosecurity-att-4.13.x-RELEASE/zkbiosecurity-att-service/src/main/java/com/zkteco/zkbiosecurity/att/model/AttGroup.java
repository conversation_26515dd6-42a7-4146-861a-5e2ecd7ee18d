package com.zkteco.zkbiosecurity.att.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 分组
 */
@Entity
@Table(name = "ATT_GROUP")
@Setter
@Getter
@Accessors(chain = true)
public class AttGroup extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分组编号(此字段暂时不用)
     */
    @Deprecated
    @Column(name = "GROUP_NO", length = 5)
    private String groupNo;

    /**
     * 分组名称
     */
    @Column(name = "GROUP_NAME", length = 30)
    private String groupName;

    /**
     * 备注
     */
    @Column(name = "REMARK")
    private String remark;

}