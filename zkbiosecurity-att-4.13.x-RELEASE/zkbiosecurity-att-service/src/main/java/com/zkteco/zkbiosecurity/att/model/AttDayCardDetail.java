package com.zkteco.zkbiosecurity.att.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 日打卡详情
 */
@Entity
@Table(name = "ATT_DAY_CARD_DETAIL",
    indexes = {@Index(name = "ATT_DAY_CARD_PERSON_PIN_IDX", columnList = "PERS_PERSON_PIN"),
        @Index(name = "ATT_DAY_CARD_DEPT_ID_IDX", columnList = "AUTH_DEPT_ID"),
        @Index(name = "ATT_DAY_CARD_ATT_DATE_IDX", columnList = "ATT_DATE")})
@Setter
@Getter
@Accessors(chain = true)
public class AttDayCardDetail extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 人员编号
     */
    @Column(name = "PERS_PERSON_PIN", length = 50)
    private String personPin;

    /**
     * 姓名
     */
    @Column(name = "PERS_PERSON_NAME", length = 50)
    private String personName;

    /**
     * 英文（lastName）
     */
    @Column(name = "PERS_PERSON_LASTNAME", length = 50)
    private String personLastName;

    /**
     * 部门ID
     */
    @Column(name = "AUTH_DEPT_ID", length = 50)
    private String deptId;

    /**
     * 部门编号
     */
    @Column(name = "AUTH_DEPT_CODE", length = 50)
    private String deptCode;

    /**
     * 部门名称
     */
    @Column(name = "AUTH_DEPT_NAME", length = 100)
    private String deptName;

    /**
     * 打卡日期
     */
    @Column(name = "ATT_DATE", length = 50)
    private String attDate;

    /**
     * 打卡次数
     */
    @Column(name = "CARD_COUNT")
    private Integer cardCount;

    /**
     * 最早时间
     */
    @Column(name = "EARLIEST_TIME", length = 20)
    private String earliestTime;

    /**
     * 最晚时间
     */
    @Column(name = "LATEST_TIME", length = 20)
    private String latestTime;

    /**
     * 打卡时间集合
     */
    @Column(name = "ATT_TIMES")
    private String attTimes;
}
