package com.zkteco.zkbiosecurity.att.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.zkteco.zkbiosecurity.att.dao.AttRecordDao;
import com.zkteco.zkbiosecurity.att.vo.AttCycleSchItem;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttCommonUtils;
import com.zkteco.zkbiosecurity.att.vo.AttDayDetailReportItem;
import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;

/**
 * 考勤日报表
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 9:24 2020/8/4
 */
@Service
public class AttDayDetailReportServiceImpl implements AttDayDetailReportService {

    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttRecordService attRecordService;
    @Autowired
    private AttRecordDao attRecordDao;

    @Override
    public void modifyItemLabel(Class clazz) {

        // 考勤规则最小单位配置
        Map<String, AttLeaveTypeItem> attLeaveTypeItemMap = attLeaveTypeService.getLeaveTypeParamsMap();

        // 应到/实到
        AttLeaveTypeItem actual = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.ACTUAL);
        if (actual != null) {
            // VO对应的字段名称
            String fieldName = "shouldConvert";
            String label = attLeaveTypeService.getConvertUnit(actual);
            AttCommonUtils.modifyItemLabelByFieldName(clazz, fieldName, label);
        }

        // 加班
        AttLeaveTypeItem overtime = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.OVERTIME);
        if (overtime != null) {
            // VO对应的字段名称
            String fieldName = "overtimeUsualConvert";
            String label = attLeaveTypeService.getConvertUnit(overtime);
            AttCommonUtils.modifyItemLabelByFieldName(clazz, fieldName, label);
        }

        // 旷工
        AttLeaveTypeItem absent = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.ABSENT);
        if (absent != null) {
            // VO对应的字段名称
            String fieldName = "absentConvert";
            String label = attLeaveTypeService.getConvertUnit(absent);
            AttCommonUtils.modifyItemLabelByFieldName(clazz, fieldName, label);
        }

        // 迟到
        AttLeaveTypeItem late = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.LATE);
        if (late != null) {
            // VO对应的字段名称
            String fieldName = "lateCountTotal";
            String label = attLeaveTypeService.getConvertUnit(late);
            AttCommonUtils.modifyItemLabelByFieldName(clazz, fieldName, label);
        }

        // 早退
        AttLeaveTypeItem early = attLeaveTypeItemMap.get(AttCalculationConstant.AttAttendStatus.EARLY);
        if (early != null) {
            // VO对应的字段名称
            String fieldName = "earlyCountTotal";
            String label = attLeaveTypeService.getConvertUnit(early);
            AttCommonUtils.modifyItemLabelByFieldName(clazz, fieldName, label);
        }
    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, AttDayDetailReportItem condition, int pageNo,
        int pageSize, long limitCount) {
        buildCondition(sessionId, condition);
        Pager pager = attRecordService.getItemsByPage(condition, pageNo, pageSize, limitCount);
        List<AttDayDetailReportItem> items = (List<AttDayDetailReportItem>)pager.getData();
        buildItems(items);
        return pager;

    }

    @Override
    public List<AttDayDetailReportItem> getDayDetailReportItemData(String sessionId,
        AttDayDetailReportItem condition, int beginIndex, int endIndex) {
        buildCondition(sessionId, condition);
        List<AttDayDetailReportItem> items = (List<AttDayDetailReportItem>)attRecordService
            .getItemData(condition.getClass(), condition, beginIndex, endIndex);
        buildItems(items);
        return items;
    }

    @Override
    public Pager loadWorkTimePagerByAuthUserFilter(String sessionId, AttDayDetailReportItem condition,
        int pageNo, int pageSize, long limitCount) {
        buildCondition(sessionId, condition);
        Pager pager = attRecordService.getItemsByPage(condition, pageNo, pageSize, limitCount);
        List<AttDayDetailReportItem> items = (List<AttDayDetailReportItem>)pager.getData();
        buildItems(items);
        return pager;
    }

    @Override
    public List<AttDayDetailReportItem> getWorkTimeReportItemData(String sessionId,
                                                                  AttDayDetailReportItem condition, int beginIndex, int endIndex) {
        buildCondition(sessionId, condition);
        List<AttDayDetailReportItem> items = (List<AttDayDetailReportItem>)attRecordService
                .getItemData(condition.getClass(), condition, beginIndex, endIndex);
        buildItems(items);
        return items;
    }

    @Override
    public Pager loadOvertimePagerByAuthUserFilter(String sessionId, AttDayDetailReportItem condition, int pageNo, int pageSize, long limitCount) {
        buildCondition(sessionId, condition);
        StringBuffer sb = new StringBuffer(SQLUtil.getSqlByItem(condition));
        sb.insert(sb.indexOf("ORDER BY"), " AND t.OVERTIME_MINUTE > 0 ");
        String sql = attRecordService.buildOrderBySQL(sb);
        Pager pager = attRecordDao.getItemsBySql(condition.getClass(), sql, pageNo, pageSize, limitCount);
        List<AttDayDetailReportItem> items = (List<AttDayDetailReportItem>)pager.getData();
        buildItems(items);
        return pager;
    }

    @Override
    public List<AttDayDetailReportItem> getOvertimeReportItemData(String sessionId, AttDayDetailReportItem condition, int beginIndex, int endIndex) {
        buildCondition(sessionId, condition);
        StringBuffer sb = new StringBuffer(SQLUtil.getSqlByItem(condition));
        sb.insert(sb.indexOf("ORDER BY"), " AND t.OVERTIME_MINUTE > 0 ");
        String sql = attRecordService.buildOrderBySQL(sb);
        List<AttDayDetailReportItem> items = attRecordDao.getItemsDataBySql(condition.getClass(), sql, beginIndex, endIndex, true);
        buildItems(items);
        return items;
    }

    @Override
    public Pager loadLeavePagerByAuthUserFilter(String sessionId, AttDayDetailReportItem condition, int pageNo, int pageSize, long limitCount) {
        buildCondition(sessionId, condition);
        StringBuffer sb = new StringBuffer(SQLUtil.getSqlByItem(condition));
        sb.insert(sb.indexOf("ORDER BY"), " AND t.LEAVE_MINUTE > 0 ");
        String sql = attRecordService.buildOrderBySQL(sb);
        Pager pager = attRecordDao.getItemsBySql(condition.getClass(), sql, pageNo, pageSize, limitCount);
        List<AttDayDetailReportItem> items = (List<AttDayDetailReportItem>)pager.getData();
        buildItems(items);
        return pager;
    }

    @Override
    public List<AttDayDetailReportItem> getLeaveReportItemData(String sessionId, AttDayDetailReportItem condition, int beginIndex, int endIndex) {
        buildCondition(sessionId, condition);
        StringBuffer sb = new StringBuffer(SQLUtil.getSqlByItem(condition));
        sb.insert(sb.indexOf("ORDER BY"), " AND t.LEAVE_MINUTE > 0 ");
        String sql = attRecordService.buildOrderBySQL(sb);
        List<AttDayDetailReportItem> items = attRecordDao.getItemsDataBySql(condition.getClass(), sql, beginIndex, endIndex, true);
        buildItems(items);
        return items;
    }

    @Override
    public Pager loadAbnormalReportPagerByAuthUserFilter(String sessionId, AttDayDetailReportItem condition, int pageNo, int pageSize, long limitCount) {
        buildCondition(sessionId, condition);
        StringBuffer sb = new StringBuffer(SQLUtil.getSqlByItem(condition));
        sb.insert(sb.indexOf("ORDER BY"), " AND (t.EARLY_MINUTE_TOTAL > 0 OR t.LATE_COUNT_TOTAL > 0 OR t.ABSENT_MINUTE > 0) ");
        String sql = attRecordService.buildOrderBySQL(sb);
        Pager pager = attRecordDao.getItemsBySql(condition.getClass(), sql, pageNo, pageSize, limitCount);
        List<AttDayDetailReportItem> items = (List<AttDayDetailReportItem>)pager.getData();
        buildItems(items);
        return pager;
    }

    @Override
    public List<AttDayDetailReportItem> getAbnormalReportItemData(String sessionId, AttDayDetailReportItem condition, int beginIndex, int endIndex) {
        buildCondition(sessionId, condition);
        StringBuffer sb = new StringBuffer(SQLUtil.getSqlByItem(condition));
        sb.insert(sb.indexOf("ORDER BY"), " AND (t.EARLY_MINUTE_TOTAL > 0 OR t.LATE_COUNT_TOTAL > 0 OR t.ABSENT_MINUTE > 0) ");
        String sql = attRecordService.buildOrderBySQL(sb);
        List<AttDayDetailReportItem> items = attRecordDao.getItemsDataBySql(condition.getClass(), sql, beginIndex, endIndex, true);
        buildItems(items);
        return items;
    }

    @Override
    public Pager loadLateReportPagerByAuthUserFilter(String sessionId, AttDayDetailReportItem condition, int pageNo, int pageSize, long limitCount) {
        buildCondition(sessionId, condition);
        StringBuffer sb = new StringBuffer(SQLUtil.getSqlByItem(condition));
        sb.insert(sb.indexOf("ORDER BY"), " AND t.LATE_COUNT_TOTAL > 0 ");
        String sql = attRecordService.buildOrderBySQL(sb);
        Pager pager = attRecordDao.getItemsBySql(condition.getClass(), sql, pageNo, pageSize, limitCount);
        List<AttDayDetailReportItem> items = (List<AttDayDetailReportItem>)pager.getData();
        buildItems(items);
        return pager;
    }

    @Override
    public List<AttDayDetailReportItem> getLateReportItemData(String sessionId, AttDayDetailReportItem condition, int beginIndex, int endIndex) {
        buildCondition(sessionId, condition);
        StringBuffer sb = new StringBuffer(SQLUtil.getSqlByItem(condition));
        sb.insert(sb.indexOf("ORDER BY"), " AND t.LATE_COUNT_TOTAL > 0 ");
        String sql = attRecordService.buildOrderBySQL(sb);
        List<AttDayDetailReportItem> items = attRecordDao.getItemsDataBySql(condition.getClass(), sql, beginIndex, endIndex, true);
        buildItems(items);
        return items;
    }

    @Override
    public Pager loadEarlyReportPagerByAuthUserFilter(String sessionId, AttDayDetailReportItem condition, int pageNo, int pageSize, long limitCount) {
        buildCondition(sessionId, condition);
        StringBuffer sb = new StringBuffer(SQLUtil.getSqlByItem(condition));
        sb.insert(sb.indexOf("ORDER BY"), " AND t.EARLY_MINUTE_TOTAL > 0 ");
        String sql = attRecordService.buildOrderBySQL(sb);
        Pager pager = attRecordDao.getItemsBySql(condition.getClass(), sql, pageNo, pageSize, limitCount);
        List<AttDayDetailReportItem> items = (List<AttDayDetailReportItem>)pager.getData();
        buildItems(items);
        return pager;
    }

    @Override
    public List<AttDayDetailReportItem> getEarlyReportItemData(String sessionId, AttDayDetailReportItem condition, int beginIndex, int endIndex) {
        buildCondition(sessionId, condition);
        StringBuffer sb = new StringBuffer(SQLUtil.getSqlByItem(condition));
        sb.insert(sb.indexOf("ORDER BY"), " AND t.EARLY_MINUTE_TOTAL > 0 ");
        String sql = attRecordService.buildOrderBySQL(sb);
        List<AttDayDetailReportItem> items = attRecordDao.getItemsDataBySql(condition.getClass(), sql, beginIndex, endIndex, true);
        buildItems(items);
        return items;
    }

    @Override
    public Pager loadAbsentReportPagerByAuthUserFilter(String sessionId, AttDayDetailReportItem condition, int pageNo, int pageSize, long limitCount) {
        buildCondition(sessionId, condition);
        StringBuffer sb = new StringBuffer(SQLUtil.getSqlByItem(condition));
        sb.insert(sb.indexOf("ORDER BY"), " AND t.ABSENT_MINUTE > 0 ");
        String sql = attRecordService.buildOrderBySQL(sb);
        Pager pager = attRecordDao.getItemsBySql(condition.getClass(), sql, pageNo, pageSize, limitCount);
        List<AttDayDetailReportItem> items = (List<AttDayDetailReportItem>)pager.getData();
        buildItems(items);
        return pager;
    }

    @Override
    public List<AttDayDetailReportItem> getAbsentReportItemData(String sessionId, AttDayDetailReportItem condition, int beginIndex, int endIndex) {
        buildCondition(sessionId, condition);
        StringBuffer sb = new StringBuffer(SQLUtil.getSqlByItem(condition));
        sb.insert(sb.indexOf("ORDER BY"), " AND t.ABSENT_MINUTE > 0 ");
        String sql = attRecordService.buildOrderBySQL(sb);
        List<AttDayDetailReportItem> items = attRecordDao.getItemsDataBySql(condition.getClass(), sql, beginIndex, endIndex, true);
        buildItems(items);
        return items;
    }

    private void buildItems(List<AttDayDetailReportItem> items) {

        // 考勤加班等级
        Map<String, String> overtimeParamsMap = attParamService.getOvertimeSetting();

        // 考勤规则最小单位配置
        Map<String, AttLeaveTypeItem> attLeaveTypeParamsMap = attLeaveTypeService.getLeaveTypeParamsMap();

        // 考勤节假日最小单位配置
        List<AttLeaveTypeItem> attLeaveTypeItemList = attLeaveTypeService.getLeaveTypeItems();
        Map<String, AttLeaveTypeItem> attLeaveTypeItemMap =
            CollectionUtil.listToKeyMap(attLeaveTypeItemList, AttLeaveTypeItem::getLeaveTypeNo);

        // 精确小数点位数
        int decimal = Integer.parseInt(attParamService.getDecimal());

        for (AttDayDetailReportItem item : items) {

            Integer shouldMinute = item.getShouldMinute();
            Integer shouldMinuteEx = Optional.ofNullable(item.getShouldMinuteEx()).orElse(0);

            // 请假
            item.setAttDayDetailReportLeaveMinuteMap(
                leaveFormat(attLeaveTypeItemMap, item.getLeaveDetails(), decimal, shouldMinute));

            // 迟到
            AttLeaveTypeItem late = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.LATE);
            buildLateMinuteData(item, late, decimal);

            // 早退
            AttLeaveTypeItem early = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.EARLY);
            buildEarlyMinuteData(item, early, decimal);

            // 出勤（单位）
            // 应该
            AttLeaveTypeItem actual = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.ACTUAL);
            item.setShouldConvert(AttCommonUtils.convertMinute(shouldMinute, shouldMinute, actual, decimal));
            // 实际
            item.setActualConvert(AttCommonUtils.convertMinute(item.getActualMinute(), shouldMinute, actual, decimal));
            // 有效
            item.setValidConvert(AttCommonUtils.convertMinute(item.getValidMinute(), shouldMinute, actual, decimal));

            Integer should = shouldMinute == 0 ? shouldMinuteEx : shouldMinute;
            // 加班（单位）
            // 平时
            AttLeaveTypeItem overtimeType = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.OVERTIME);
            item.setOvertimeUsualConvert(
                AttCommonUtils.convertMinute(item.getOvertimeUsualMinute(), should, overtimeType, decimal));
            // 休息
            item.setOvertimeRestConvert(
                AttCommonUtils.convertMinute(item.getOvertimeRestMinute(), should, overtimeType, decimal));
            // 节日
            item.setOvertimeHolidayConvert(
                AttCommonUtils.convertMinute(item.getOvertimeHolidayMinute(), should, overtimeType, decimal));
            // 合计
            item.setOvertimeMinuteConvert(
                AttCommonUtils.convertMinute(item.getOvertimeMinute(), should, overtimeType, decimal));

            // 加班等级
            item.setOverTimeOT1(AttCommonUtils.convertOverTimeOTString(item, should, overtimeType, decimal, overtimeParamsMap, "OT1"));
            item.setOverTimeOT2(AttCommonUtils.convertOverTimeOTString(item, should, overtimeType, decimal, overtimeParamsMap, "OT2"));
            item.setOverTimeOT3(AttCommonUtils.convertOverTimeOTString(item, should, overtimeType, decimal, overtimeParamsMap, "OT3"));

            // 旷工
            AttLeaveTypeItem absent = attLeaveTypeParamsMap.get(AttCalculationConstant.AttAttendStatus.ABSENT);
            item.setAbsentConvert(AttCommonUtils.convertMinute(item.getAbsentMinute(), shouldMinute, absent, decimal));
        }
    }

    /**
     * 迟到数据组装
     */
    private void buildLateMinuteData(AttDayDetailReportItem attDayDetailReportItem, AttLeaveTypeItem late,
        int decimal) {

        String lateMinuteData = attDayDetailReportItem.getLateMinuteData();
        if (StringUtils.isNotBlank(lateMinuteData)) {
            String[] lateMinuteDataAtt = lateMinuteData.split(";");
            StringBuffer lateMinuteDataBuf = new StringBuffer();
            BigDecimal bigDecimal = BigDecimal.ZERO;

            RoundingMode roundingMode = AttCommonUtils.getRoundingMode(late.getConvertType());
            for (String lateMinute : lateMinuteDataAtt) {
                String ret = AttCommonUtils.convertMinute(Integer.valueOf(lateMinute),
                    attDayDetailReportItem.getShouldMinute(), late, decimal);
                lateMinuteDataBuf.append(ret).append(";");
                bigDecimal = bigDecimal.add(BigDecimal.valueOf(Double.valueOf(ret)));
            }
            attDayDetailReportItem
                .setLateMinuteData(lateMinuteDataBuf.deleteCharAt(lateMinuteDataBuf.length() - 1).toString());
            attDayDetailReportItem.setLateMinuteTotalConvert(bigDecimal.setScale(decimal, roundingMode).toString());

        } else {
            attDayDetailReportItem.setLateMinuteData(BigDecimal.ZERO.setScale(decimal) + "");
            attDayDetailReportItem.setLateMinuteTotalConvert(BigDecimal.ZERO.setScale(decimal) + "");
        }
    }

    /**
     * 早退数据组装
     */
    private void buildEarlyMinuteData(AttDayDetailReportItem attDayDetailReportItem, AttLeaveTypeItem early,
        int decimal) {
        String earlyMinuteData = attDayDetailReportItem.getEarlyMinuteData();
        if (StringUtils.isNotBlank(earlyMinuteData)) {
            String[] earlyMinuteDataArry = earlyMinuteData.split(";");
            StringBuffer earlyMinuteDataBuf = new StringBuffer();
            BigDecimal bigDecimal = BigDecimal.ZERO;
            RoundingMode roundingMode = AttCommonUtils.getRoundingMode(early.getConvertType());
            for (String earlyMinute : earlyMinuteDataArry) {
                String ret = AttCommonUtils.convertMinute(Integer.valueOf(earlyMinute),
                    attDayDetailReportItem.getShouldMinute(), early, decimal);
                earlyMinuteDataBuf.append(ret).append(";");
                bigDecimal = bigDecimal.add(BigDecimal.valueOf(Double.valueOf(ret)));
            }
            attDayDetailReportItem
                .setEarlyMinuteData(earlyMinuteDataBuf.deleteCharAt(earlyMinuteDataBuf.length() - 1).toString());
            attDayDetailReportItem.setEarlyMinuteTotalConvert(bigDecimal.setScale(decimal, roundingMode).toString());
        } else {
            attDayDetailReportItem.setEarlyMinuteData(BigDecimal.ZERO.setScale(decimal) + "");
            attDayDetailReportItem.setEarlyMinuteTotalConvert(BigDecimal.ZERO.setScale(decimal) + "");
        }
    }

    /**
     * 请假时长组装
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/8/4 9:37
     * @param attLeaveTypeItemMap
     * @param leaveDetails
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    private Map<String, Object> leaveFormat(Map<String, AttLeaveTypeItem> attLeaveTypeItemMap, String leaveDetails,
        int decimal, Integer shouldMinute) {

        Map<String, Object> map = new HashMap<>();
        if (StringUtils.isNotBlank(leaveDetails)) {
            String[] leaveDetailsArray = leaveDetails.split(",");
            for (String leaveDetail : leaveDetailsArray) {
                String[] splitArr = leaveDetail.split("-");
                String leaveTypeNo = splitArr[0] + "";
                Integer minute = Integer.valueOf(splitArr[1] + "");
                map.put(leaveTypeNo,
                    AttCommonUtils.convertMinute(minute, shouldMinute, attLeaveTypeItemMap.get(leaveTypeNo), decimal));
            }
        }

        Map<String, Object> result = new HashMap<>();
        String defaultValue = BigDecimal.ZERO.setScale(decimal) + "";
        for (Map.Entry<String, AttLeaveTypeItem> entry : attLeaveTypeItemMap.entrySet()) {
            AttLeaveTypeItem attLeaveTypeItem = entry.getValue();
            Object value = MapUtils.getObject(map, attLeaveTypeItem.getLeaveTypeNo(), defaultValue);
            result.put(attLeaveTypeItem.getLeaveTypeName(), value);
        }
        return result;
    }


    /**
     * 组装条件过滤
     */
    private <T> void buildCondition(String sessionId, T t) {
        String pin = attPersonService.getPinByLoginType(sessionId);
        if (StringUtils.isNotBlank(pin)) {
            AttCommonUtils.setClassFieldValue(t, "setEquals", true, Boolean.class);
            AttCommonUtils.setClassFieldValue(t, "setPersonPin", pin, String.class);
        } else {
            String inDeptId = AttCommonUtils.getClassFieldValue(t, "getInDeptId");
            String deptId = AttCommonUtils.getClassFieldValue(t, "getDeptId");
            if (StringUtils.isBlank(inDeptId)) {
                String deptCode = AttCommonUtils.getClassFieldValue(t, "getDeptCode");
                String deptName = AttCommonUtils.getClassFieldValue(t, "getDeptName");
                String inDeptIds =
                        authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, deptId, deptCode, deptName);
                AttCommonUtils.setClassFieldValue(t, "setInDeptId", inDeptIds, String.class);
                AttCommonUtils.setClassFieldValue(t, "setDeptId", null, String.class);
            } else if (StringUtils.isNotBlank(sessionId)) {
                String inDeptIds = authDepartmentService.getDeptIdsByAuthAndIds(sessionId, inDeptId);
                AttCommonUtils.setClassFieldValue(t, "setInDeptId", inDeptIds, String.class);
                AttCommonUtils.setClassFieldValue(t, "setDeptId", null, String.class);
            }
        }
    }

    @Override
    public List<AttDayDetailReportItem> getByCondition(AttDayDetailReportItem condition) {
        List<AttDayDetailReportItem> list =
                (List<AttDayDetailReportItem>)attRecordDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
        buildItems(list);
        return list;
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int pageNo, int pageSize) {
        Pager pager = attRecordService.getItemsByPage(condition, pageNo, pageSize);
        List<AttDayDetailReportItem> items = (List<AttDayDetailReportItem>)pager.getData();
        buildItems(items);
        return pager;
    }

    @Override
    public boolean deleteByIds(String s) {
        return false;
    }

}
