package com.zkteco.zkbiosecurity.att.dao;

import com.zkteco.zkbiosecurity.att.model.AttAreaPerson;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;

/**
 * 区域人员
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 15:53
 * @since 1.0.0
 */
public interface AttAreaPersonDao extends BaseDao<AttAreaPerson, String> {

    /**
     * 通过区域ID，查找该区域下存在多少人
     * 
     * @param areaId
     * @return
     */
    Long countByAreaId(String areaId);

    /**
     * 根据区域id，还有人员id删除 区域人员id数据
     *
     * @param personId:
     * @param areaId:
     * @return int
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/3/31 15:53
     * @since 1.0.0
     */
    int deleteByPersonIdAndAreaId(String personId, String areaId);

    /**
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/7 10:39
     * @param personId
     * @return java.util.List<java.lang.String>
     */
    @Query("SELECT t.areaId FROM AttAreaPerson t WHERE t.personId = ?1")
    List<String> getAreaIdsByPersonId(String personId);

    @Query(value = "SELECT t.personId FROM AttAreaPerson t WHERE t.areaId = ?1 ")
    List<String> getPersonIdListByAreaId(String areaId);

    @Query(value = "SELECT t.personId FROM AttAreaPerson t WHERE t.id in(?1) ")
    List<String> getPersonIdListByIdList(List<String> idList);

    @Query("SELECT t.areaId FROM AttAreaPerson t WHERE t.id in (?1)")
    List<String> getAreaIdsByIds(List<String> idList);

    void deleteByPersonIdIn(Collection<String> personIdList);

    @Query("SELECT t.personId,t.areaId FROM AttAreaPerson t WHERE t.personId IN (?1) GROUP BY t.personId,t.areaId ORDER BY t.personId")
    List<Object[]> getAreaIdsByPersonIds(List<String> personIdList);

    /**
     *
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/8/6 14:07
     * @param personIds
     * @return java.util.List<com.zkteco.zkbiosecurity.att.model.AttAreaPerson>
     */
    List<AttAreaPerson> findByPersonIdIn(Collection<String> personIds);

    List<AttAreaPerson> findByAreaIdAndPersonIdIn(String areaId, Collection<String> personIds);

    List<AttAreaPerson> findByAreaId(String areaId);

    List<AttAreaPerson> findByIdIn(Collection<String> ids);

    @Query("SELECT t.areaId FROM AttAreaPerson t GROUP BY t.areaId")
    List<String> getAllAreaIds();

    List<AttAreaPerson> findByPersonId(String personId);

    List<AttAreaPerson> findByPersonIdAndAreaIdIn(String personId, List<String> asList);
}