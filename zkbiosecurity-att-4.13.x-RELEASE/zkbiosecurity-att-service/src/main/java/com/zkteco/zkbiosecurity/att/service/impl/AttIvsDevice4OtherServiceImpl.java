package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.Collection;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.att.dao.AttPointDao;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.ivs.service.IvsDevice4OtherService;

/**
 * 智能视频设备删除通知
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 15:17 2021/9/8
 * @version v1.0
 */
@Service
public class AttIvsDevice4OtherServiceImpl implements IvsDevice4OtherService {

    @Autowired
    private AttPointDao attPointDao;

    @Override
    public void deleteDevice(Collection<String> channelIds) {
        if (channelIds != null && channelIds.size() > 0) {
            long count = attPointDao.countByDeviceIdIn(channelIds);
            if (count > 0) {
                throw ZKBusinessException
                    .warnException(I18nUtil.i18nCode("common_prompt_canNotDel", I18nUtil.i18nCode("att_module")));
            }
        }
    }
}
