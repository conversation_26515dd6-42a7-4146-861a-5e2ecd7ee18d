package com.zkteco.zkbiosecurity.att.service.impl;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.api.vo.*;
import com.zkteco.zkbiosecurity.att.constants.AttApiConstant;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.constants.AttShiftConstant;
import com.zkteco.zkbiosecurity.att.enums.AttRuleEnum;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.service.PersPositionService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPositionItem;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

@Slf4j
@Service
public class AttTeamWorkFlowServiceImpl implements AttTeamWorkFlowService {

    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AttLeaveService attLeaveService;
    @Autowired
    private AttOvertimeService attOvertimeService;
    @Autowired
    private AttSignService attSignService;
    @Autowired
    AuthDepartmentService authDepartmentService;
    @Autowired
    private PersPositionService persPositionService;
    @Autowired
    private AttRecordService attRecordService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AttTransactionService attTransactionService;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttLeaveTypeService attLeaveTypeService;

    @Override
    public Pager findTeamLeaveTask(String startDate, String endDate, String personPin, Integer pageNo,
        Integer pageSize) {

        List<PersPersonItem> teamMember = getTeamMember(personPin, null);
        String personIds = CollectionUtil.getItemIds(teamMember);

        AttLeaveItem condition = new AttLeaveItem();
        // @TODO 时间格式转换
        // condition.setStartDatetime();
        // condition.setEndDatetime();
        condition.setInPersonId(personIds);

        Pager pager = attLeaveService.getItemsByPage(condition, pageNo, pageSize);

        return pager;
    }

    @Override
    public Pager findTeamOvertimeTask(String month, String personPin, Integer pageNo, Integer pageSize) {

        int year = Integer.parseInt(month.split("-")[0]);
        int monthInt = Integer.parseInt(month.split("-")[1]);
        Date firstDayOfMonth = AttDateUtils.getFirstDayOfMonth(year, monthInt);
        Date lastDayOfMonth = AttDateUtils.getLastDayOfMonth(year, monthInt);

        List<PersPersonItem> teamMember = getTeamMember(personPin, null);
        String personIds = CollectionUtil.getItemIds(teamMember);

        AttLeaveItem condition = new AttLeaveItem();
        condition.setStartDatetime(firstDayOfMonth);
        condition.setEndDatetime(lastDayOfMonth);
        condition.setInPersonId(personIds);

        Pager pager = attLeaveService.getItemsByPage(condition, pageNo, pageSize);

        return pager;
    }

    @Override
    public String getTeamOvertimeHours(String month, String personPin) {
        int year = Integer.parseInt(month.split("-")[0]);
        int monthInt = Integer.parseInt(month.split("-")[1]);
        Date firstDayOfMonth = AttDateUtils.getFirstDayOfMonth(year, monthInt);
        Date lastDayOfMonth = AttDateUtils.getLastDayOfMonth(year, monthInt);

        List<PersPersonItem> teamMember = getTeamMember(personPin, null);
        String personIds = CollectionUtil.getItemIds(teamMember);

        AttOvertimeItem condition = new AttOvertimeItem();
        condition.setStartDatetime(firstDayOfMonth);
        condition.setEndDatetime(lastDayOfMonth);
        condition.setInPersonId(personIds);

        List<AttOvertimeItem> attOvertimeItemList = attOvertimeService.getByCondition(condition);

        BigDecimal bigDecimal = BigDecimal.ZERO;
        for (AttOvertimeItem attOvertimeItem : attOvertimeItemList) {
            // 分钟数
            long diffTime = (attOvertimeItem.getEndDatetime().getTime() - attOvertimeItem.getStartDatetime().getTime())
                / (60 * 1000);
            bigDecimal = bigDecimal.add(BigDecimal.valueOf(diffTime));
        }
        DecimalFormat df = attParamService.getDecimalFormat();
        return df.format(bigDecimal);

    }

    @Override
    public Pager findPersonOvertimeTask(String month, String personPin, Integer pageNo, Integer pageSize) {

        int year = Integer.parseInt(month.split("-")[0]);
        int monthInt = Integer.parseInt(month.split("-")[1]);
        Date firstDayOfMonth = AttDateUtils.getFirstDayOfMonth(year, monthInt);
        Date lastDayOfMonth = AttDateUtils.getLastDayOfMonth(year, monthInt);

        AttOvertimeItem condition = new AttOvertimeItem();
        condition.setStartDatetime(firstDayOfMonth);
        condition.setEndDatetime(lastDayOfMonth);
        condition.setPersonPin(personPin);

        Pager pager = attOvertimeService.getItemsByPage(condition, pageNo, pageSize);

        return pager;
    }

    @Override
    public List<Map<String, Object>> getTeamSignTimes(String month, String personPin) {
        List<PersPersonItem> teamMember = getTeamMember(personPin, null);
        String personIds = CollectionUtil.getItemIds(teamMember);

        int year = Integer.parseInt(month.split("-")[0]);
        int monthInt = Integer.parseInt(month.split("-")[1]);
        Date firstDayOfMonth = AttDateUtils.getFirstDayOfMonth(year, monthInt);
        Date lastDayOfMonth = AttDateUtils.getLastDayOfMonth(year, monthInt);
        List<Map<String, Object>> list = attSignService.getTeamSignTimes(firstDayOfMonth, lastDayOfMonth, personIds);
        return list;
    }

    @Override
    public Pager findPersonSignTask(String month, String personPin, Integer pageNo, Integer pageSize) {

        int year = Integer.parseInt(month.split("-")[0]);
        int monthInt = Integer.parseInt(month.split("-")[1]);
        Date firstDayOfMonth = AttDateUtils.getFirstDayOfMonth(year, monthInt);
        Date lastDayOfMonth = AttDateUtils.getLastDayOfMonth(year, monthInt);

        AttSignItem condition = new AttSignItem();
        condition.setStartTime(firstDayOfMonth);
        condition.setEndTime(lastDayOfMonth);
        condition.setPersonPin(personPin);

        Pager pager = attSignService.getItemsByPage(condition, pageNo, pageSize);

        return pager;
    }

    /**
     * 获取我的团队成员
     *
     * @param personPin
     * @return
     */
    private List<PersPersonItem> getTeamMember(String personPin, String deptCode) {
        PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
        String positionId = persPersonItem.getPositionId();
        if (StringUtils.isBlank(positionId)) {
            // 如果没有配置职位返回自己
            return Collections.singletonList(persPersonItem);
        }
        PersPersonItem condition = new PersPersonItem();
        // 底下部门集合
        if (StringUtils.isBlank(deptCode)) {
            // 没有传部门,则取该人员拥有权限的全部部门
            List<AuthDepartmentItem> departmentItems =
                authDepartmentService.getItemAndChildById(persPersonItem.getDeptId());
            String deptIds = CollectionUtil.getItemIds(departmentItems);
            condition.setInDeptId(deptIds);
        } else {
            /* condition.setDeptCode(deptCode); */
            // 指定部门,则只查该部门
            AuthDepartmentItem departmentItem = new AuthDepartmentItem();
            departmentItem.setEquals(true);
            departmentItem.setCode(deptCode);
            List<AuthDepartmentItem> departmentItemList = authDepartmentService.getByCondition(departmentItem);
            String deptIds = CollectionUtil.getPropertys(departmentItemList, AuthDepartmentItem::getId);
            condition.setInDeptId(deptIds);
        }
        // 底下职位集合, 不包含同级
        String positionIds = persPositionService.getPositionIdsByParentId(positionId);
        Set<String> positionSet = new HashSet(Arrays.asList(positionIds.split(",")));
        positionSet.remove(positionId);
        if (CollectionUtil.isEmpty(positionSet)) {
            return Collections.singletonList(persPersonItem);
        }
        positionIds = StringUtils.join(positionSet, ",");
        condition.setInPositionId(positionIds);
        List<PersPersonItem> list = persPersonService.getByCondition(condition);
        if (null == list) {
            list = new ArrayList();
        }
        list.add(persPersonItem);

        return list;
    }

    private String getTeamMemberPins(String personPin, String deptCode) {
        // 去掉重复代码 mod by wumeijin 20190827
        List<PersPersonItem> persPersonItemList = getTeamMember(personPin, deptCode);
        String personPins = CollectionUtil.getPropertys(persPersonItemList, PersPersonItem::getPin);
        return personPins;
    }

    @Override
    public List<String> getTeamMemberPins(String personPin) {
        List<PersPersonItem> teamMember = getTeamMember(personPin, null);
        Collection<String> teamMemberPins = CollectionUtil.getPropertyList(teamMember, PersPersonItem::getPin, "-1");
        return new ArrayList<>(teamMemberPins);
    }

    @Override
    public AttApiTeamAttendanceStatusItem getTeamAttendanceStatusItem(String personPin, String deptCode,
        String attDate) {
        // 1.根据 personPin 和 deptCode 查询团队成员集合
        String personPins = getTeamMemberPins(personPin, deptCode);

        // 2. 按人员集合 日期 查询记录
        Date date = AttDateUtils.stringToYmdDate(attDate);
        Date dayBeginTime = DateUtil.getDayBeginTime(date);
        Date dayEndTime = DateUtil.getDayEndTime(date);

        AttRecordItem condition = new AttRecordItem();
        condition.setInPersonPin(personPins);
        condition.setStartDate(dayBeginTime);
        condition.setEndDate(dayEndTime);

        List<AttRecordItem> attRecordItemList = attRecordService.getByCondition(condition);

        // 3.汇总统计状态
        Integer actualSign = Integer.valueOf(0);
        Integer earlyCount = Integer.valueOf(0);
        Integer lateCount = Integer.valueOf(0);
        Integer leakageCount = Integer.valueOf(0);
        Integer leaveCount = Integer.valueOf(0);
        Integer normalCount = Integer.valueOf(0);
        Integer outCount = Integer.valueOf(0);
        Integer shouldSign = Integer.valueOf(0);
        Integer tripCount = Integer.valueOf(0);

        List<AttLeaveTypeItem> leaveTypeItems = attLeaveTypeService.listLeaveTypeFilterTripAndOut();
        Set<String> leaveTypeNoSet = CollectionUtil.listToPropertySet(leaveTypeItems, AttLeaveTypeItem::getLeaveTypeNo);
        Set<String> intersection = new HashSet<>();
        for (AttRecordItem attRecordItem : attRecordItemList) {

            // 应到
            if ((Objects.nonNull(attRecordItem.getShouldMinute()) && attRecordItem.getShouldMinute() > 0)
                    || !AttCalculationConstant.AttAttendStatus.NO_SCHEDULING.equals(attRecordItem.getAttendanceStatus())) {
                shouldSign++;
            }

            // 实到 : 有打卡记录（含补签） by ljf 2019/9/20
            AttTransactionItem attTransactionItem = new AttTransactionItem();
            attTransactionItem.setEquals(true);
            attTransactionItem.setPersonPin(attRecordItem.getPersonPin());
            attTransactionItem.setBeginDate(dayBeginTime);
            attTransactionItem.setEndDate(dayEndTime);
            List<AttTransactionItem> attTransactionItems = attTransactionService.getByCondition(attTransactionItem);
            if (!CollectionUtil.isEmpty(attTransactionItems)) {
                actualSign++;
            }

            String attendanceStatus = Optional.ofNullable(attRecordItem.getAttendanceStatus()).orElse("");
            Set<String> attendanceStatusSet = new HashSet<>(Arrays.asList(attendanceStatus.split(",")));
            intersection.clear();
            intersection.addAll(attendanceStatusSet);
            intersection.retainAll(leaveTypeNoSet);

            // 状态统计调整,多状态允许重复统计,即一个人有多种状态,每个状态都+1
            if (attendanceStatusSet.contains(AttCalculationConstant.AttAttendStatus.ACTUAL)) {
                // 正常
                normalCount++;
            }
            if (!intersection.isEmpty()) {
                // 休假
                leaveCount++;
            }
            if (attendanceStatusSet.contains(AttCalculationConstant.AttAttendStatus.TRIP)) {
                // 出差
                tripCount++;
            }
            if (attendanceStatusSet.contains(AttCalculationConstant.AttAttendStatus.OUT)) {
                // 外出
                outCount++;
            }
            if (attendanceStatusSet.contains(AttCalculationConstant.AttAttendStatus.LATE)) {
                // 迟到
                lateCount++;
            }
            if (attendanceStatusSet.contains(AttCalculationConstant.AttAttendStatus.EARLY)) {
                // 早退
                earlyCount++;
            }
            if (attendanceStatusSet.contains(AttCalculationConstant.AttAttendStatus.ABSENT)
                || attendanceStatusSet.contains(AttCalculationConstant.AttAttendStatus.NO_CHECK_IN)
                || attendanceStatusSet.contains(AttCalculationConstant.AttAttendStatus.NO_CHECK_OUT)) {
                // 漏卡
                leakageCount++;
            }
        }

        AttApiTeamAttendanceStatusItem statusItem = new AttApiTeamAttendanceStatusItem();
        statusItem.setActualSign(actualSign);
        statusItem.setEarlyCount(earlyCount);
        statusItem.setLateCount(lateCount);
        statusItem.setLeakageCount(leakageCount);
        statusItem.setLeaveCount(leaveCount);
        statusItem.setNormalCount(normalCount);
        statusItem.setOutCount(outCount);
        statusItem.setShouldSign(shouldSign);
        statusItem.setTripCount(tripCount);

        return statusItem;
    }

    @Override
    public Pager getTeamAttendancePersonItem(String personPin, String deptCode, String attDate, Integer pageNo,
        Integer pageSize) {
        // 1.根据 personPin 和 deptCode 查询团队成员集合
        String personPins = getTeamMemberPins(personPin, deptCode);

        // 2. 按人员集合 日期 查询记录
        AttRecordItem condition = new AttRecordItem();
        condition.setInPersonPin(personPins);
        try {
            Date date = AttDateUtils.stringToYmdDate(attDate);
            // condition.setAttDate(date);
            condition.setStartDate(AttDateUtils.getMinOfDay(date));
            condition.setEndDate(AttDateUtils.getMaxOfDay(date));
        } catch (Exception e) {
            log.error("exception = ", e);
        }

        Pager pager = attRecordService.getItemsByPage(condition, pageNo, pageSize);
        // 转换返回类型
        List<AttRecordItem> attRecordItemList = (List<AttRecordItem>)pager.getData();

        List<AttApiTeamAttendancePersonItem> attendanceItems = new ArrayList<>();

        for (AttRecordItem attRecordItem : attRecordItemList) {
            AttApiTeamAttendancePersonItem item = new AttApiTeamAttendancePersonItem();
            item.setPersonPin(attRecordItem.getPersonPin());
            item.setPersonName(attRecordItem.getPersonName());
            item.setShiftName(attRecordItem.getShiftName());
            item.setCardValidData(attRecordItem.getCardValidData());

            // 设置职位名称
            PersPersonItem persPersonItem = persPersonService.getItemByPin(attRecordItem.getPersonPin());
            if (Objects.nonNull(persPersonItem.getPositionId())) {
                PersPositionItem persPositionItem = persPositionService.getItemById(persPersonItem.getPositionId());
                if (Objects.nonNull(persPositionItem)) {
                    item.setPositionName(persPositionItem.getName());
                }
            }

            String attResultType = attendanceStatusToAttResultType(attRecordItem.getAttendanceStatus());
            item.setAttResultType(attResultType);
            attendanceItems.add(item);
        }

        pager.setData(attendanceItems);

        return pager;

    }

    /**
     * 考勤状态值转化为api接口状态值
     *
     * @param attendanceStatus:
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/7/9 15:19
     * @since 1.0.0
     */
    private String attendanceStatusToAttResultType(String attendanceStatus) {
        StringBuffer attResultTypeSb = new StringBuffer();
        String[] statusArray = attendanceStatus.split(",");
        for (String status : statusArray) {
            int resultType = -1;
            switch (status) {
                // 实到
                case AttCalculationConstant.AttAttendStatus.ACTUAL:
                    resultType = AttApiConstant.ATT_RESULT_TYPE_ARRIVE;
                    break;
                // 迟到
                case AttCalculationConstant.AttAttendStatus.LATE:
                    resultType = AttApiConstant.ATT_RESULT_TYPE_LATE;
                    break;
                // 早退
                case AttCalculationConstant.AttAttendStatus.EARLY:
                    resultType = AttApiConstant.ATT_RESULT_TYPE_EARLY;
                    break;
                // 旷工
                case AttCalculationConstant.AttAttendStatus.ABSENT:
                    resultType = AttApiConstant.ATT_RESULT_TYPE_ABSENT;
                    break;
                // 休假
                case AttCalculationConstant.AttAttendStatus.LEAVE:
                    resultType = AttApiConstant.ATT_RESULT_TYPE_LEAVE;
                    break;
                // 加班
                case AttCalculationConstant.AttAttendStatus.OVERTIME:
                    resultType = AttApiConstant.ATT_RESULT_TYPE_OVERTIME;
                    break;
                // 出差
                case AttCalculationConstant.AttAttendStatus.TRIP:
                    resultType = AttApiConstant.ATT_RESULT_TYPE_TRIP;
                    break;
                // 外出
                case AttCalculationConstant.AttAttendStatus.OUT:
                    resultType = AttApiConstant.ATT_RESULT_TYPE_OUT;
                    break;
                // 未排班
                case AttCalculationConstant.AttAttendStatus.NO_SCHEDULING:
                    resultType = AttApiConstant.ATT_RESULT_TYPE_NO_SCHEDULING;
                    break;
                // 休息（含排班且休息，调休，节假日）
                case AttCalculationConstant.AttAttendStatus.SCHEDULING_AND_REST:
                case AttCalculationConstant.AttAttendStatus.TUNE_OFF:
                case AttCalculationConstant.AttAttendStatus.HOLIDAY:
                    resultType = AttApiConstant.ATT_RESULT_TYPE_REST;
                    break;
                // 调班,补班
                case AttCalculationConstant.AttAttendStatus.SAME_DATE_CLASS:
                case AttCalculationConstant.AttAttendStatus.DIFF_DATE_CLASS:
                case AttCalculationConstant.AttAttendStatus.PERSON_SWAP_CLASS:
                    resultType = AttApiConstant.ATT_RESULT_ADJUST_CLASS;
                    break;
                // 未签退、未签到都是漏卡
                case AttCalculationConstant.AttAttendStatus.NO_CHECK_IN:
                case AttCalculationConstant.AttAttendStatus.NO_CHECK_OUT:
                    resultType = AttApiConstant.ATT_RESULT_LACK_CARD;
                    break;
            }
            if (resultType != -1 && attResultTypeSb.indexOf(resultType + "") == -1) {
                attResultTypeSb.append(resultType).append(",");
            }
        }
        String attResultType = attResultTypeSb.deleteCharAt(attResultTypeSb.length() - 1).toString();
        return attResultType;
    }

    @Override
    public Pager getTeamLeakagePersonItem(String personPin, String attDate, String attType, Integer pageNo,
        Integer pageSize) {

        //
        String memberPins = getTeamMemberPins(personPin, "");

        AttRecordItem condition = new AttRecordItem();
        if ("arrive".equals(attType)) {
            // 正常
            condition.setAttendanceStatus(AttCalculationConstant.AttAttendStatus.ACTUAL);
        } else if ("leakage".equals(attType)) {
            // 漏卡
            condition.setLeakageStatus("(t.ATTENDANCE_STATUS LIKE '%absent%' OR t.ATTENDANCE_STATUS LIKE '%noCheck%')");
        } else if ("late".equals(attType)) {
            // 迟到
            condition.setAttendanceStatus(AttCalculationConstant.AttAttendStatus.LATE);
        } else if ("early".equals(attType)) {
            // 早退
            condition.setAttendanceStatus(AttCalculationConstant.AttAttendStatus.EARLY);
        } else if ("leave".equals(attType)) {
            // 请假
            condition.setAttendanceStatus(AttCalculationConstant.AttAttendStatus.LEAVE);
        } else if ("trip".equals(attType)) {
            // 出差
            condition.setAttendanceStatus(AttCalculationConstant.AttAttendStatus.TRIP);
        } else if ("out".equals(attType)) {
            // 外出
            condition.setAttendanceStatusSingle(AttCalculationConstant.AttAttendStatus.OUT);
        }

        condition.setInPersonPin(memberPins);
        try {
            Date date = AttDateUtils.stringToYmdDate(attDate);
            // condition.setAttDate(date);
            condition.setStartDate(AttDateUtils.getMinOfDay(date));
            condition.setEndDate(AttDateUtils.getMaxOfDay(date));
        } catch (Exception e) {
            log.error("exception = ", e);
        }

        Pager pager = attRecordService.getItemsByPage(condition, pageNo, pageSize);

        List<AttApiTeamLeakagePersonItem> leakagePersonItemList = new ArrayList<>();
        List<AttRecordItem> attRecordItemList = (List<AttRecordItem>)pager.getData();
        for (AttRecordItem attRecordItem : attRecordItemList) {
            AttApiTeamLeakagePersonItem item = new AttApiTeamLeakagePersonItem();
            item.setPersonName(attRecordItem.getPersonName());
            item.setPersonPin(attRecordItem.getPersonPin());
            item.setShiftName(attRecordItem.getShiftName());

            // 设置职位名称
            PersPersonItem persPersonItem = persPersonService.getItemByPin(attRecordItem.getPersonPin());
            if (Objects.nonNull(persPersonItem.getPositionId())) {
                PersPositionItem persPositionItem = persPositionService.getItemById(persPersonItem.getPositionId());
                if (Objects.nonNull(persPositionItem)) {
                    item.setPositionName(persPositionItem.getName());
                }
            }

            leakagePersonItemList.add(item);
        }
        pager.setData(leakagePersonItemList);
        return pager;
    }

    @Override
    public AttApiTeamAbnormalItem getTeamAbnormalItem(String personPin, String month, String type) {

        List<PersPersonItem> teamMember = getTeamMember(personPin, null);
        String personPins = CollectionUtil.getPropertys(teamMember, PersPersonItem::getPin);

        int year = Integer.parseInt(month.split("-")[0]);
        int monthInt = Integer.parseInt(month.split("-")[1]);
        Date startApplyDateTime = AttDateUtils.getFirstDayOfMonth(year, monthInt);
        Date endApplyDateTime = AttDateUtils.getLastDayOfMonth(year, monthInt);
        Map<String, String> params = baseSysParamService.getParamsByModule("att");
        Set<String> abnormalPersonPins = new HashSet<>();
        BigDecimal abnormalMinuteCount = new BigDecimal(0);

        AttLeaveItem attLeaveCondition = new AttLeaveItem();
        attLeaveCondition.setInPersonPin(personPins);
        attLeaveCondition.setFlowStatus(AttConstant.FLOW_STATUS_COMPLETE);
        attLeaveCondition.setStartApplyDateTime(startApplyDateTime);
        attLeaveCondition.setEndApplyDateTime(endApplyDateTime);
        if (AttApiConstant.LEAVE.equals(type)) {

            // 请假过滤出差和外出
            attLeaveCondition.setNotInLeaveTypeNo(
                    AttCalculationConstant.AttAttendStatus.TRIP + "," + AttCalculationConstant.AttAttendStatus.OUT);
            List<AttLeaveItem> attLeaveItemList = attLeaveService.getByCondition(attLeaveCondition);
            for (AttLeaveItem item : attLeaveItemList) {
                int timeLong = calcExceptionTimeLongByDate(item.getPersonId(), item.getStartDatetime(),
                    item.getEndDatetime(), startApplyDateTime, endApplyDateTime, item.getLeaveLong(), true);
                String timeFormat = attParamService.minutesToHourFormat(new BigDecimal(timeLong));
                abnormalMinuteCount = abnormalMinuteCount.add(new BigDecimal(timeFormat));
                abnormalPersonPins.add(item.getPersonPin());
            }
        } else if (AttApiConstant.TRIP.equals(type)) {
            // 出差
            attLeaveCondition.setLeaveTypeNo(AttCalculationConstant.AttAttendStatus.TRIP);
            List<AttLeaveItem> attLeaveItemList = attLeaveService.getByCondition(attLeaveCondition);
            for (AttLeaveItem item : attLeaveItemList) {
                int timeLong = calcExceptionTimeLongByDate(item.getPersonId(), item.getStartDatetime(),
                        item.getEndDatetime(), startApplyDateTime, endApplyDateTime, item.getLeaveLong(), true);
                String timeFormat = attParamService.minutesToHourFormat(new BigDecimal(timeLong));
                abnormalMinuteCount = abnormalMinuteCount.add(new BigDecimal(timeFormat));
                abnormalPersonPins.add(item.getPersonPin());
            }

        }  else if (AttApiConstant.OUT.equals(type)) {
            // 外出
            attLeaveCondition.setLeaveTypeNo(AttCalculationConstant.AttAttendStatus.OUT);
            List<AttLeaveItem> attLeaveItemList = attLeaveService.getByCondition(attLeaveCondition);
            for (AttLeaveItem item : attLeaveItemList) {
                int timeLong = calcExceptionTimeLongByDate(item.getPersonId(), item.getStartDatetime(),
                        item.getEndDatetime(), startApplyDateTime, endApplyDateTime, item.getLeaveLong(), true);
                String timeFormat = attParamService.minutesToHourFormat(new BigDecimal(timeLong));
                abnormalMinuteCount = abnormalMinuteCount.add(new BigDecimal(timeFormat));
                abnormalPersonPins.add(item.getPersonPin());
            }
        } else if (AttApiConstant.OVERTIME.equals(type)) {
            AttOvertimeItem condition = new AttOvertimeItem();
            condition.setInPersonPin(personPins);
            condition.setFlowStatus(AttConstant.FLOW_STATUS_COMPLETE);
            condition.setStartApplyDateTime(startApplyDateTime);
            condition.setEndApplyDateTime(endApplyDateTime);
            List<AttOvertimeItem> list = attOvertimeService.getByCondition(condition);
            for (AttOvertimeItem item : list) {
                int timeLong = calcExceptionTimeLongByDate(item.getPersonId(), item.getStartDatetime(),
                    item.getEndDatetime(), startApplyDateTime, endApplyDateTime, item.getOvertimeLong(), false);
                String timeFormat = attParamService.minutesToHourFormat(new BigDecimal(timeLong));
                abnormalMinuteCount = abnormalMinuteCount.add(new BigDecimal(timeFormat));
                abnormalPersonPins.add(item.getPersonPin());
            }
        } else if (AttApiConstant.SIGN.equals(type)) {
            AttSignItem condition = new AttSignItem();
            condition.setInPersonPin(personPins);
            condition.setFlowStatus(AttConstant.FLOW_STATUS_COMPLETE);
            condition.setStartSignTime(startApplyDateTime);
            condition.setEndSignTime(endApplyDateTime);
            List<AttSignItem> list = attSignService.getByCondition(condition);
            for (AttSignItem item : list) {
                // 补签为次数
                abnormalMinuteCount = abnormalMinuteCount.add(BigDecimal.ONE);
                abnormalPersonPins.add(item.getPersonPin());
            }
        }

        int teamCount = teamMember.size();
        int abnormalCount = abnormalPersonPins.size();

        AttApiTeamAbnormalItem item = new AttApiTeamAbnormalItem();
        item.setTeamCount(teamCount);
        item.setAbnormalCount(abnormalCount);
        if (AttApiConstant.SIGN.equals(type)) {
            // 补签为次数
            item.setTimeLong(String.valueOf(abnormalMinuteCount.setScale(0, BigDecimal.ROUND_DOWN)));
        } else {
            // 分钟转成小时
            Integer countConvertDecimal = Integer.valueOf(params.get(AttRuleEnum.Decimal.getKey()));
            item.setTimeLong(String.valueOf(abnormalMinuteCount.setScale(countConvertDecimal)));
        }

        return item;
    }

    @Override
    public Pager getTeamLeavePersonItem(String personPin, String month, Integer pageNo, Integer pageSize) {
        List<PersPersonItem> teamMember = getTeamMember(personPin, null);
        String personPins = CollectionUtil.getPropertys(teamMember, PersPersonItem::getPin);

        AttLeaveItem condition = new AttLeaveItem();
        condition.setInPersonPin(personPins);
        int year = Integer.parseInt(month.split("-")[0]);
        int monthInt = Integer.parseInt(month.split("-")[1]);
        Date monthStartDate = AttDateUtils.getFirstDayOfMonth(year, monthInt);
        Date monthEndDate = AttDateUtils.getLastDayOfMonth(year, monthInt);
        condition.setStartApplyDateTime(monthStartDate);
        condition.setEndApplyDateTime(monthEndDate);
        condition.setFlowStatus(AttConstant.FLOW_STATUS_COMPLETE);
        // 请假过滤出差和外出
        condition.setNotInLeaveTypeNo(
                AttCalculationConstant.AttAttendStatus.TRIP + "," + AttCalculationConstant.AttAttendStatus.OUT);

        Pager pager = attLeaveService.getItemsByPage(condition, pageNo, pageSize);

        List<AttLeaveItem> attLeaveItemList = (List<AttLeaveItem>)pager.getData();
        List<AttApiTeamLeavePersonItem> itemList = new ArrayList<>();

        // 获取假种配置
        Map<String, AttLeaveTypeItem> attLeaveTypeItemMap = attLeaveTypeService.getLeaveTypeMap();

        for (AttLeaveItem attLeaveItem : attLeaveItemList) {
            Date startDate = attLeaveItem.getStartDatetime();
            Date endDate = attLeaveItem.getEndDatetime();
            boolean isSameMonth = true;
            if (AttDateUtils.getMonth(startDate.getTime(), monthStartDate.getTime()) != 0) {
                startDate = monthStartDate;
                isSameMonth = false;
            }
            if (AttDateUtils.getMonth(endDate.getTime(), monthEndDate.getTime()) != 0) {
                endDate = monthEndDate;
                isSameMonth = false;
            }
            AttApiTeamLeavePersonItem item = new AttApiTeamLeavePersonItem();
            item.setPersonPin(attLeaveItem.getPersonPin());
            item.setPersonName(attLeaveItem.getPersonName());
            item.setStartDatetime(AttDateUtils.dateToStrAsMedium(startDate));
            item.setEndDatetime(AttDateUtils.dateToStrAsMedium(endDate));
            item.setLeaveTypeName(attLeaveItem.getLeaveTypeName());
            if (isSameMonth) {
                // 同一月直接设置时长
                item.setLeaveLong(attParamService.minutesToHourFormat(new BigDecimal(attLeaveItem.getLeaveLong())));
            } else {
                // 跨月根据时间重新计算
                int timeLong = attLeaveService.calLeaveTimeMinutes(attLeaveItem.getPersonId(), startDate, endDate,
                    attLeaveTypeItemMap.get(attLeaveItem.getLeaveTypeId()));
                item.setLeaveLong(attParamService.minutesToHourFormat(new BigDecimal(timeLong)));
            }
            item.setApplyTime(AttDateUtils.dateToStrAsLong(attLeaveItem.getOperateDatetime()));
            item.setTaskId(attLeaveItem.getBusinessKey());
            // 设置申请id,用以admin用户提交的申请查询详情 add by bob.liu at 20191018
            item.setApplyId(attLeaveItem.getId());
            itemList.add(item);
        }

        pager.setData(itemList);

        return pager;
    }

    @Override
    public Pager getTeamOvertimePersonItem(String personPin, String month, Integer pageNo, Integer pageSize) {

        List<PersPersonItem> teamMember = getTeamMember(personPin, null);
        String teamMemberPins = CollectionUtil.getPropertys(teamMember, PersPersonItem::getPin);
        Map<String, PersPersonItem> persPersonItemMap =
            teamMember.stream().collect(Collectors.toMap(PersPersonItem::getPin, a -> a));

        AttOvertimeItem condition = new AttOvertimeItem();
        int year = Integer.parseInt(month.split("-")[0]);
        int monthInt = Integer.parseInt(month.split("-")[1]);
        Date monthStartDate = AttDateUtils.getFirstDayOfMonth(year, monthInt);
        Date monthEndDate = AttDateUtils.getLastDayOfMonth(year, monthInt);
        condition.setStartApplyDateTime(AttDateUtils.getFirstDayOfMonth(year, monthInt));
        condition.setEndApplyDateTime(AttDateUtils.getLastDayOfMonth(year, monthInt));
        condition.setInPersonPin(teamMemberPins);
        condition.setFlowStatus(AttConstant.FLOW_STATUS_COMPLETE);

        Pager pager = attOvertimeService.getItemsByPage(condition, pageNo, pageSize);

        List<AttApiTeamOverTimePersonItem> itemList = new ArrayList<>();
        List<AttOvertimeItem> attOvertimeItemList = (List<AttOvertimeItem>)pager.getData();
        for (AttOvertimeItem attOvertimeItem : attOvertimeItemList) {
            Date startDate = attOvertimeItem.getStartDatetime();
            Date endDate = attOvertimeItem.getEndDatetime();
            boolean isSameMonth = true;
            if (AttDateUtils.getMonth(startDate.getTime(), monthStartDate.getTime()) != 0) {
                startDate = monthStartDate;
                isSameMonth = false;
            }
            if (AttDateUtils.getMonth(endDate.getTime(), monthEndDate.getTime()) != 0) {
                endDate = monthEndDate;
                isSameMonth = false;
            }
            AttApiTeamOverTimePersonItem item = new AttApiTeamOverTimePersonItem();
            item.setPersonPin(attOvertimeItem.getPersonPin());
            // 设置姓名
            if (persPersonItemMap.containsKey(attOvertimeItem.getPersonPin())) {
                item.setPersonName(persPersonItemMap.get(attOvertimeItem.getPersonPin()).getName());
            }
            item.setOverTimeTypeName(
                AttShiftConstant.ATT_OVERTIME_TYPE.get(String.valueOf(attOvertimeItem.getOvertimeSign())));

            String overtime = AttDateUtils.dateToStrAsMedium(startDate) + "-" + AttDateUtils.dateToStrAsMedium(endDate);
            item.setOverTime(overtime);
            if (isSameMonth) {
                item.setOverTimeLong(
                    attParamService.minutesToHourFormat(new BigDecimal(attOvertimeItem.getOvertimeLong())));
            } else {
                long timeLong = 0;
                long diff = endDate.getTime() - startDate.getTime();
                if (diff > 0) {
                    timeLong = diff / (1000 * 60);
                }
                item.setOverTimeLong(attParamService.minutesToHourFormat(new BigDecimal(timeLong)));
            }

            item.setApplyTime(AttDateUtils.dateToStrAsLong(attOvertimeItem.getCreateTime()));
            item.setTaskId(attOvertimeItem.getBusinessKey());
            itemList.add(item);
        }
        pager.setData(itemList);

        return pager;
    }

    @Override
    public Pager getTeamOverTimeByTypePersonItem(String personPin, String month, Integer pageNo, Integer pageSize) {

        List<PersPersonItem> teamMember = getTeamMember(personPin, null);
        String personPins = CollectionUtil.getPropertys(teamMember, PersPersonItem::getPin);
        Map<String, PersPersonItem> persPersonItemMap =
            teamMember.stream().collect(Collectors.toMap(PersPersonItem::getPin, a -> a));

        int year = Integer.parseInt(month.split("-")[0]);
        int monthInt = Integer.parseInt(month.split("-")[1]);
        Date monthStartDate = AttDateUtils.getFirstDayOfMonth(year, monthInt);
        Date monthEndDate = AttDateUtils.getLastDayOfMonth(year, monthInt);

        AttTeamOverTimeByTypePersonItem condition = new AttTeamOverTimeByTypePersonItem();
        condition.setStartDate(monthStartDate);
        condition.setEndDate(monthEndDate);
        condition.setInPersonPin(personPins);
        condition.setFlowStatus(AttConstant.FLOW_STATUS_COMPLETE);
        Pager pager = attOvertimeService.getItemsByPage(condition, pageNo, pageSize);

        // 查询月份中团队成员所有加班记录，用于计算加班工时
        AttOvertimeItem attOvertimeItem = new AttOvertimeItem();
        attOvertimeItem.setStartApplyDateTime(monthStartDate);
        attOvertimeItem.setEndApplyDateTime(monthEndDate);
        attOvertimeItem.setInPersonPin(personPins);
        attOvertimeItem.setFlowStatus(AttConstant.FLOW_STATUS_COMPLETE);
        List<AttOvertimeItem> attOvertimeItems = attOvertimeService.getByCondition(attOvertimeItem);
        Map<String, List<AttOvertimeItem>> attOverMap =
            attOvertimeItems.stream().collect(Collectors.groupingBy(AttOvertimeItem::getPersonPin));

        List<AttApiTeamOverTimeByTypePersonItem> itemList = new ArrayList<>();
        List<AttTeamOverTimeByTypePersonItem> data = (List<AttTeamOverTimeByTypePersonItem>)pager.getData();
        for (AttTeamOverTimeByTypePersonItem tempItem : data) {
            AttApiTeamOverTimeByTypePersonItem item = new AttApiTeamOverTimeByTypePersonItem();
            item.setPersonPin(tempItem.getPersonPin());
            // 设置姓名
            if (persPersonItemMap.containsKey(tempItem.getPersonPin())) {
                item.setPersonName(persPersonItemMap.get(tempItem.getPersonPin()).getName());
            }
            item.setOverTimeTypeName(
                AttShiftConstant.ATT_OVERTIME_TYPE.get(String.valueOf(tempItem.getOvertimeSign())));
            item.setOverTimeCount(tempItem.getOverTimeCount());
            List<AttOvertimeItem> attOvertimeItemList = attOverMap.get(tempItem.getPersonPin());
            float timeLong = 0;
            if (!CollectionUtil.isEmpty(attOvertimeItemList)) {
                for (AttOvertimeItem overtimeItem : attOvertimeItemList) {
                    if (tempItem.getOvertimeSign().equals(overtimeItem.getOvertimeSign())) {
                        int time = calcExceptionTimeLongByDate(overtimeItem.getPersonId(),
                            overtimeItem.getStartDatetime(), overtimeItem.getEndDatetime(), monthStartDate,
                            monthEndDate, overtimeItem.getOvertimeLong(), false);
                        timeLong += Float.parseFloat(attParamService.minutesToHourFormat(new BigDecimal(time)));
                    }
                }
            } else {
                // 加班时长
                if (Objects.isNull(tempItem.getOverTimeLong())) {
                    timeLong = 0;
                }
            }
            item.setOverTimeLong(String.valueOf(timeLong));
            itemList.add(item);
        }

        pager.setData(itemList);

        return pager;
    }

    @Override
    public Pager getTeamSignMonthItem(String personPin, String month, Integer pageNo, Integer pageSize) {

        List<PersPersonItem> teamMember = getTeamMember(personPin, null);
        String personPins = CollectionUtil.getPropertys(teamMember, PersPersonItem::getPin);
        Map<String, PersPersonItem> persPersonItemMap =
            teamMember.stream().collect(Collectors.toMap(PersPersonItem::getPin, a -> a));

        AttTeamSignMonthItem condition = new AttTeamSignMonthItem();
        int year = Integer.parseInt(month.split("-")[0]);
        int monthInt = Integer.parseInt(month.split("-")[1]);
        condition.setStartDate(AttDateUtils.getFirstDayOfMonth(year, monthInt));
        condition.setEndDate(AttDateUtils.getLastDayOfMonth(year, monthInt));
        condition.setInPersonPin(personPins);
        condition.setFlowStatus(AttConstant.FLOW_STATUS_COMPLETE);

        Pager pager = attSignService.getItemsByPage(condition, pageNo, pageSize);

        List<AttTeamSignMonthItem> attTeamSignMonthItemList = (List<AttTeamSignMonthItem>)pager.getData();
        List<AttApiTeamSignMonthItem> monthItemList = new ArrayList<>();
        for (AttTeamSignMonthItem attTeamSignMonthItem : attTeamSignMonthItemList) {
            AttApiTeamSignMonthItem item = new AttApiTeamSignMonthItem();
            item.setPersonPin(attTeamSignMonthItem.getPersonPin());
            // 设置姓名
            if (persPersonItemMap.containsKey(attTeamSignMonthItem.getPersonPin())) {
                item.setPersonName(persPersonItemMap.get(attTeamSignMonthItem.getPersonPin()).getName());
            }
            item.setCount(attTeamSignMonthItem.getCount());
            monthItemList.add(item);
        }
        pager.setData(monthItemList);

        return pager;
    }

    @Override
    public Integer getTeamSignCount(String personPin, String month) {

        String teamMemberPins = getTeamMemberPins(personPin, "");

        AttSignItem condition = new AttSignItem();
        int year = Integer.parseInt(month.split("-")[0]);
        int monthInt = Integer.parseInt(month.split("-")[1]);
        condition.setStartTime(AttDateUtils.getFirstDayOfMonth(year, monthInt));
        condition.setEndTime(AttDateUtils.getLastDayOfMonth(year, monthInt));
        condition.setInPersonPin(teamMemberPins);
        condition.setFlowStatus(AttConstant.FLOW_STATUS_COMPLETE);

        List<AttSignItem> attSignItemList = attSignService.getByCondition(condition);

        if (CollectionUtil.isEmpty(attSignItemList)) {
            return 0;
        }

        return attSignItemList.size();
    }

    @Override
    public AttApiTeamSignPersonItem getTeamSignPersonItem(String personPin, String month) {

        AttSignItem condition = new AttSignItem();
        int year = Integer.parseInt(month.split("-")[0]);
        int monthInt = Integer.parseInt(month.split("-")[1]);
        condition.setStartSignTime(AttDateUtils.getFirstDayOfMonth(year, monthInt));
        condition.setEndSignTime(AttDateUtils.getLastDayOfMonth(year, monthInt));
        condition.setEquals(true);
        condition.setPersonPin(personPin);

        // 获取该pin号成员的当月考勤
        AttRecordItem attRecordItem = new AttRecordItem();
        attRecordItem.setPersonPin(personPin);
        attRecordItem.setStartDate(AttDateUtils.getFirstDayOfMonth(year, monthInt));
        attRecordItem.setEndDate(AttDateUtils.getLastDayOfMonth(year, monthInt));
        condition.setFlowStatus(AttConstant.FLOW_STATUS_COMPLETE);
        List<AttRecordItem> attRecordItemList = attRecordService.getByCondition(attRecordItem);
        Map<Date, AttRecordItem> attRecordItemMap =
            CollectionUtil.listToKeyMap(attRecordItemList, AttRecordItem::getAttDate);

        // 获取该PIN号人员的补签记录
        List<AttSignItem> attSignItemList = attSignService.getByCondition(condition);
        List<AttApiTeamSignPersonDetailItem> itemList = new ArrayList<>();

        for (AttSignItem attSignItem : attSignItemList) {

            AttApiTeamSignPersonDetailItem detailItem = new AttApiTeamSignPersonDetailItem();

            detailItem.setApplyTime(AttDateUtils.dateToStrAsLong(attSignItem.getOperateDatetime()));
            detailItem
                .setSignDate(DateUtil.dateToString(attSignItem.getSignDatetime(), DateUtil.DateStyle.YYYY_MM_DD_CN));
            detailItem.setSignDay(DateUtil.getWeek(attSignItem.getSignDatetime()).getChineseName());
            detailItem.setRemaker(attSignItem.getRemark());

            detailItem.setBeforeSignTime(attSignItem.getBeforeSignRecord());
            detailItem.setSignTime(attSignItem.getAfterSignRecord());
            // 添加标识,是否是管理员直接添加的补签申请 add by bob.liu 20191115
            if ("admin".equals(attSignItem.getCreatName())) {
                detailItem.setAdminAdd(true);
            } else {
                detailItem.setAdminAdd(false);
            }
            // 班次信息
            String shiftName = "";
            if (attRecordItemMap.containsKey(AttDateUtils.getMinOfDay(attSignItem.getSignDatetime()))) {
                shiftName =
                    attRecordItemMap.get(AttDateUtils.getMinOfDay(attSignItem.getSignDatetime())).getShiftName();
            }
            detailItem.setShiftName(shiftName);

            itemList.add(detailItem);
        }

        AttApiTeamSignPersonItem item = new AttApiTeamSignPersonItem();
        item.setCount(itemList.size());
        item.setSignDetails(itemList);

        return item;
    }

    @Override
    public Pager getTeamTripPersonItem(String personPin, String month, Integer pageNo, Integer pageSize) {
        List<PersPersonItem> teamMember = getTeamMember(personPin, null);
        String personPins = CollectionUtil.getPropertys(teamMember, PersPersonItem::getPin);

        AttLeaveItem condition = new AttLeaveItem();
        condition.setInPersonPin(personPins);
        int year = Integer.parseInt(month.split("-")[0]);
        int monthInt = Integer.parseInt(month.split("-")[1]);
        Date monthStartDate = AttDateUtils.getFirstDayOfMonth(year, monthInt);
        Date monthEndDate = AttDateUtils.getLastDayOfMonth(year, monthInt);
        condition.setStartApplyDateTime(AttDateUtils.getFirstDayOfMonth(year, monthInt));
        condition.setEndApplyDateTime(AttDateUtils.getLastDayOfMonth(year, monthInt));
        condition.setFlowStatus(AttConstant.FLOW_STATUS_COMPLETE);
        condition.setLeaveTypeNo(AttCalculationConstant.AttAttendStatus.TRIP);

        Pager pager = attLeaveService.getItemsByPage(condition, pageNo, pageSize);

        List<AttLeaveItem> attLeaveItemList = (List<AttLeaveItem>)pager.getData();
        List<AttApiTeamTripPersonItem> itemList = new ArrayList<>();

        // 获取假种配置
        AttLeaveTypeItem attLeaveTypeItem =
            attLeaveTypeService.getItemByLeaveTypeNo(AttCalculationConstant.AttAttendStatus.TRIP);

        for (AttLeaveItem attLeaveItem : attLeaveItemList) {
            Date startDate = attLeaveItem.getStartDatetime();
            Date endDate = attLeaveItem.getEndDatetime();
            boolean isSameMonth = true;
            if (AttDateUtils.getMonth(startDate.getTime(), monthStartDate.getTime()) != 0) {
                startDate = monthStartDate;
                isSameMonth = false;
            }
            if (AttDateUtils.getMonth(endDate.getTime(), monthEndDate.getTime()) != 0) {
                endDate = monthEndDate;
                isSameMonth = false;
            }
            AttApiTeamTripPersonItem item = new AttApiTeamTripPersonItem();
            item.setPersonPin(attLeaveItem.getPersonPin());
            item.setPersonName(attLeaveItem.getPersonName());
            item.setStartDatetime(AttDateUtils.dateToStrAsMedium(startDate));
            item.setEndDatetime(AttDateUtils.dateToStrAsMedium(endDate));
            if (isSameMonth) {
                item.setTimeLong(attParamService.minutesToHourFormat(new BigDecimal(attLeaveItem.getLeaveLong())));
            } else {
                int timeLong = attLeaveService.calLeaveTimeMinutes(attLeaveItem.getPersonId(), startDate, endDate,
                    attLeaveTypeItem);
                item.setTimeLong(attParamService.minutesToHourFormat(new BigDecimal(timeLong)));
            }
            item.setTaskId(attLeaveItem.getBusinessKey());
            item.setApplyTime(AttDateUtils.dateToStrAsMedium(attLeaveItem.getOperateDatetime()));
            // 设置申请id,用以admin用户提交的申请查询详情 add by bob.liu at 20191018
            item.setApplyId(attLeaveItem.getId());
            itemList.add(item);
        }

        pager.setData(itemList);

        return pager;
    }

    @Override
    public Pager getTeamOutPersonItem(String personPin, String month, Integer pageNo, Integer pageSize) {
        List<PersPersonItem> teamMember = getTeamMember(personPin, null);
        String personPins = CollectionUtil.getPropertys(teamMember, PersPersonItem::getPin);

        AttLeaveItem condition = new AttLeaveItem();
        condition.setInPersonPin(personPins);
        int year = Integer.parseInt(month.split("-")[0]);
        int monthInt = Integer.parseInt(month.split("-")[1]);
        Date monthStartDate = AttDateUtils.getFirstDayOfMonth(year, monthInt);
        Date monthEndDate = AttDateUtils.getLastDayOfMonth(year, monthInt);
        condition.setStartApplyDateTime(AttDateUtils.getFirstDayOfMonth(year, monthInt));
        condition.setEndApplyDateTime(AttDateUtils.getLastDayOfMonth(year, monthInt));
        condition.setFlowStatus(AttConstant.FLOW_STATUS_COMPLETE);
        condition.setLeaveTypeNo(AttCalculationConstant.AttAttendStatus.OUT);

        Pager pager = attLeaveService.getItemsByPage(condition, pageNo, pageSize);

        List<AttLeaveItem> attLeaveItemList = (List<AttLeaveItem>)pager.getData();
        List<AttApiTeamOutPersonItem> itemList = new ArrayList<>();

        // 获取假种配置
        AttLeaveTypeItem attLeaveTypeItem =
            attLeaveTypeService.getItemByLeaveTypeNo(AttCalculationConstant.AttAttendStatus.OUT);

        for (AttLeaveItem attLeaveItem : attLeaveItemList) {
            Date startDate = attLeaveItem.getStartDatetime();
            Date endDate = attLeaveItem.getEndDatetime();
            boolean isSameMonth = true;
            if (AttDateUtils.getMonth(startDate.getTime(), monthStartDate.getTime()) != 0) {
                startDate = monthStartDate;
                isSameMonth = false;
            }
            if (AttDateUtils.getMonth(endDate.getTime(), monthEndDate.getTime()) != 0) {
                endDate = monthEndDate;
                isSameMonth = false;
            }
            AttApiTeamOutPersonItem item = new AttApiTeamOutPersonItem();
            item.setPersonPin(attLeaveItem.getPersonPin());
            item.setPersonName(attLeaveItem.getPersonName());
            item.setStartDatetime(AttDateUtils.dateToStrAsMedium(startDate));
            item.setEndDatetime(AttDateUtils.dateToStrAsMedium(endDate));
            item.setApplyTime(AttDateUtils.dateToStrAsLong(attLeaveItem.getOperateDatetime()));
            if (isSameMonth) {
                item.setTimeLong(attParamService.minutesToHourFormat(new BigDecimal(attLeaveItem.getLeaveLong())));
            } else {
                int timeLong =
                    attLeaveService.calLeaveTimeMinutes(attLeaveItem.getPersonId(), startDate, endDate, attLeaveTypeItem);
                item.setTimeLong(attParamService.minutesToHourFormat(new BigDecimal(timeLong)));
            }
            item.setTaskId(attLeaveItem.getBusinessKey());
            // 设置申请id,用以admin用户提交的申请查询详情 add by bob.liu at 20191018
            item.setApplyId(attLeaveItem.getId());
            itemList.add(item);
        }

        pager.setData(itemList);

        return pager;
    }

    @Override
    public Pager getTeamAbnormalPersonItem(String personPin, String deptCode, String attDate, String attType,
        Integer pageNo, Integer pageSize) {
        String memberPins = getTeamMemberPins(personPin, deptCode);
        AttRecordItem condition = new AttRecordItem();
        if (AttApiConstant.ARRIVE.equals(attType)) {
            condition.setAttendanceStatus(AttCalculationConstant.AttAttendStatus.ACTUAL);
        } else if (AttApiConstant.LEAKAGE.equals(attType)) {
            condition.setAttendanceStatus(AttCalculationConstant.AttAttendStatus.ABSENT);
        } else if (AttApiConstant.LATE.equals(attType)) {
            condition.setAttendanceStatus(AttCalculationConstant.AttAttendStatus.LATE);
        } else if (AttApiConstant.EARLY.equals(attType)) {
            condition.setAttendanceStatus(AttCalculationConstant.AttAttendStatus.EARLY);
        } else if (AttApiConstant.LEAVE.equals(attType)) {
            condition.setAttendanceStatus(AttCalculationConstant.AttAttendStatus.LEAVE);
        } else if (AttApiConstant.TRIP.equals(attType)) {
            condition.setAttendanceStatus(AttCalculationConstant.AttAttendStatus.TRIP);
        } else if (AttApiConstant.OUT.equals(attType)) {
            condition.setAttendanceStatus(AttCalculationConstant.AttAttendStatus.TRIP);
        } else {
            // 未知类型
            condition.setAttendanceStatus("-1");
        }

        condition.setInPersonPin(memberPins);
        try {
            Date date = AttDateUtils.stringToYmdDate(attDate);
            condition.setStartDate(AttDateUtils.getMinOfDay(date));
            condition.setEndDate(AttDateUtils.getMaxOfDay(date));
        } catch (Exception e) {
            log.error("exception = ", e);
        }

        Pager pager = attRecordService.getItemsByPage(condition, pageNo, pageSize);

        List<AttApiTeamAbnormalPersonItem> attApiTeamAbnormalPersonItemList =
            new ArrayList<AttApiTeamAbnormalPersonItem>();

        List<AttRecordItem> attRecordItemList = (List<AttRecordItem>)pager.getData();
        String personPins = CollectionUtil.getPropertys(attRecordItemList, AttRecordItem::getPersonPin);

        List<PersPersonItem> personItems = persPersonService.getItemsByPins(personPins);
        Map<String, PersPersonItem> personItemMap = CollectionUtil.listToKeyMap(personItems, PersPersonItem::getPin);
        String positionIds = CollectionUtil.getPropertys(personItems, PersPersonItem::getPositionId);

        List<PersPositionItem> positionItems = persPositionService.getItemByIds(positionIds);
        Map<String, PersPositionItem> positionItemMap =
            CollectionUtil.listToKeyMap(positionItems, PersPositionItem::getId);

        for (AttRecordItem attRecordItem : attRecordItemList) {
            AttApiTeamAbnormalPersonItem item = new AttApiTeamAbnormalPersonItem();
            item.setPersonName(attRecordItem.getPersonName());
            item.setPersonPin(attRecordItem.getPersonPin());
            item.setShiftName(attRecordItem.getShiftName());
            // 设置职位名称
            PersPersonItem persPersonItem = personItemMap.get(attRecordItem.getPersonPin());
            if (Objects.nonNull(persPersonItem.getPositionId())) {
                PersPositionItem persPositionItem = positionItemMap.get(persPersonItem.getPositionId());
                if (Objects.nonNull(persPositionItem)) {
                    item.setPositionName(persPositionItem.getName());
                }
            }
            attApiTeamAbnormalPersonItemList.add(item);
        }
        pager.setData(attApiTeamAbnormalPersonItemList);

        return pager;
    }

    /**
     * 根据月份时间统计异常申请时长
     * 
     * @auther lambert.li
     * @date 2019/9/5 16:22
     * @param applyStartDate
     *            申请开始时间
     * @param applyEndDate
     *            申请结束时间
     * @param monthStartDate
     *            月份开始时间
     * @param monthEndDate
     *            月份结束时间
     * @param applyTimeLong
     *            申请时长
     * @param isCalcByShift
     *            是否根据班次时长计算
     * @return
     */
    private int calcExceptionTimeLongByDate(String personId, Date applyStartDate, Date applyEndDate,
        Date monthStartDate, Date monthEndDate, int applyTimeLong, boolean isCalcByShift) {
        int timeLong = applyTimeLong;
        Date startDate = applyStartDate;
        Date endDate = applyEndDate;
        boolean isSameMonth = true;
        if (AttDateUtils.getMonth(applyStartDate.getTime(), monthStartDate.getTime()) != 0) {
            startDate = monthStartDate;
            isSameMonth = false;
        }
        if (AttDateUtils.getMonth(applyEndDate.getTime(), monthEndDate.getTime()) != 0) {
            endDate = monthEndDate;
            isSameMonth = false;
        }
        if (!isSameMonth) {
            // 跨月数据根据时间段计算结果
            if (isCalcByShift) {
                AttLeaveTypeItem attLeaveTypeItem = new AttLeaveTypeItem();
                attLeaveTypeItem.setConvertCount(1.0);
                attLeaveTypeItem.setConvertType(AttConstant.ATT_CONVERT_ABORT);
                attLeaveTypeItem.setConvertUnit(AttConstant.ATT_CONVERT_UNIT_MINUTE);
                // 根据排班时长进行计算
                Integer leaveMinute =
                    attLeaveService.calLeaveTimeMinutes(personId, startDate, endDate, attLeaveTypeItem);
                return leaveMinute != null ? leaveMinute.intValue() : 0;
            } else {
                // 直接计算两个时间差
                long minutes = 0;
                long diff = endDate.getTime() - startDate.getTime();
                if (diff > 0) {
                    minutes = diff / (1000 * 60);
                }
                return (int)minutes;
            }
        }
        return timeLong;
    }
}
