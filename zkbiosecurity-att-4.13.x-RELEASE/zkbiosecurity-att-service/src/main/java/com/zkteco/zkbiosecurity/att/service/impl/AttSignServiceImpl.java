package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.*;

import com.zkteco.zkbiosecurity.att.bean.AttPersPersonInfoBean;
import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.api.vo.AttApiSignDayValidCardItem;
import com.zkteco.zkbiosecurity.att.cache.AttCalculationCacheManager;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.dao.AttRecordDao;
import com.zkteco.zkbiosecurity.att.dao.AttSignDao;
import com.zkteco.zkbiosecurity.att.model.AttSign;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.utils.DateUtil.DateStyle;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.service.PersLeavePersonService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersLeavePersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 补签
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 15:52
 * @since 1.0.0
 */
@Service
public class AttSignServiceImpl implements AttSignService {

    @Autowired
    private AttSignDao attSignDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AttPersonService attPersonService;
    @Autowired
    private AttWorkflowService attWorkflowService;
    @Autowired
    private AttRecordDao attRecordDao;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private AttTransactionService attTransactionService;
    @Autowired
    private AttRealTimePushService attRealTimePushService;
    @Autowired
    private AttCalculationCacheManager attCalculationCacheManager;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttRecordService attRecordService;
    @Autowired
    private AttPersonSchDataService attPersonSchDataService;
    @Autowired
    private AttMessageCenterService attMessageCenterService;
    @Autowired
    private AttLeaveService attLeaveService;
    @Autowired
    private AuthUserService authUserService;

    @Override
    @Transactional
    public AttSignItem saveItem(AttSignItem item, String personIds, String sessionId) {

        // 是否有审批权限
        boolean hasApproval = attLeaveService.checkPermission(sessionId, "att:sign:approval");

        List<PersPersonItem> persPersonList = persPersonService.getSimpleItemsByIds(StrUtil.strToList(personIds));
        List<AttSign> attSignList = new ArrayList<>();
        for (PersPersonItem persPerson : persPersonList) {
            AttSign attSign = new AttSign();
            attSign.setPersonId(persPerson.getId()).setPersonPin(persPerson.getPin()).setDeptId(persPerson.getDeptId());
            attSign.setSignDatetime(item.getSignDatetime()).setRemark(item.getRemark());
            attSign.setAfterSignRecord(DateUtil.dateToString(item.getSignDatetime(), DateStyle.HH_MM));
            attSign.setAttState(item.getAttState());
            // 如果有审批权限，直接标记状态为已完成
            attSign.setFlowStatus(hasApproval ? AttConstant.FLOW_STATUS_COMPLETE : AttConstant.FLOW_STATUS_CREATE);
            attSignList.add(attSign);
        }
        attSignDao.save(attSignList);

        if (hasApproval) {
            String crossDay = attParamService.getCrossDay();
            for (AttSign attSign : attSignList) {
                // 保存补签记录到原始记录表
                saveToTransaction(attSign);
                // 【实时计算】保存补签记录到缓存,新增实时计算事件
                setSignAndAddEventCache(attSign, crossDay);
            }
        }

        return item;
    }

    @Override
    @Transactional
    public AttSignItem saveItem(AttSignItem item) {
        AttSign attSign = Optional.ofNullable(item).map(AttSignItem::getId).filter(StringUtils::isNotBlank)
            .flatMap(attSignDao::findById).filter(Objects::nonNull).orElse(new AttSign());
        PersPersonItem persPersonItem = persPersonService.getItemById(item.getPersonId());
        ModelUtil.copyPropertiesIgnoreNull(item, attSign);
        attSign.setPersonPin(persPersonItem.getPin());
        attSignDao.save(attSign);
        item.setId(attSign.getId());
        return item;
    }

    @Override
    public List<AttSignItem> getByCondition(AttSignItem condition) {
        return (List<AttSignItem>)attSignDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        Pager pager = attSignDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        buildItem((List<AttSignItem>)pager.getData());
        return pager;
    }

    @Override
    public Pager loadPagerByAuthFilter(String sessionId, AttSignItem condition, int pageNo, int pageSize) {
        buildCondition(sessionId, condition);
        Pager pager = attSignDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        buildItem((List<AttSignItem>)pager.getData());
        return pager;
    }

    private void buildCondition(String sessionId, AttSignItem condition) {

        // 使用员工自助登录的用户,只有自己数据的权限。若登录类型为pers,则返回pin加入查询sql
        String persperPin = attPersonService.getPinByLoginType(sessionId);
        if (persperPin != null) {
            condition.setPersonPinEq(persperPin);
        } else {
            // 部门权限过滤
            String userId = authUserService.getUserIdBySessionId(sessionId);
            if (StringUtils.isNotBlank(userId)) {
                condition.setUserId(userId);
            }
            // 前端是否包含上下级
            if (ZKConstant.TRUE.equals(condition.getIsIncludeLower())) {
                if (StringUtils.isBlank(condition.getInDeptId())) {
                    if (StringUtils.isNotBlank(condition.getDeptId())) {
                        List<String> deptIdList = authDepartmentService.getChildDeptIdsByDeptIds(condition.getDeptId());
                        condition.setInDeptId(StrUtil.collectionToStr(deptIdList));
                        condition.setDeptId(null);
                    }
                }
            }
            // 人员姓名模糊查询（包含离职人员）
            String pins = attPersonService.getPinsByLikeName(sessionId, condition.getLikeName());
            if (StringUtils.isNotBlank(pins)) {
                condition.setInPersonPin(pins);
            }
        }
    }

    /**
     * 数据填充：离职人员姓名填充
     *
     * @param itemList
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/7/2 15:08
     */
    private void buildItem(List<AttSignItem> itemList) {
        List<List<AttSignItem>> splitItemList = CollectionUtil.split(itemList, CollectionUtil.splitSize);
        for (List<AttSignItem> splitItems : splitItemList) {

            Collection<String> personPinList =
                CollectionUtil.getPropertyList(splitItems, AttSignItem::getPersonPin, AttConstant.COMM_DEF_VALUE);
            Map<String, AttPersPersonInfoBean> persPersonInfoMap =
                attPersonService.getPersonInfoByPinList(personPinList);

            for (AttSignItem item : splitItems) {
                String personPin = item.getPersonPin();
                AttPersPersonInfoBean persPersonInfoBean = persPersonInfoMap.get(personPin);
                if (Objects.nonNull(persPersonInfoBean)) {
                    item.setPersonName(persPersonInfoBean.getPersonName());
                    item.setPersonLastName(persPersonInfoBean.getPersonLastName());
                }
            }
        }
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            List<AttSign> attSignList = attSignDao.findByIdList(CollectionUtil.strToList(ids));
            String crossDay = attParamService.getCrossDay();
            for (AttSign attSign : attSignList) {
                attSignDao.delete(attSign);
                if (StringUtils.isNotBlank(attSign.getBusinessKey())) {
                    attWorkflowService.deleteProcessInstance(attSign.getBusinessKey());
                }
                if (AttConstant.FLOW_STATUS_COMPLETE.equals(attSign.getFlowStatus())) {
                    // 删除原始记录表的保存补签记录
                    attTransactionService.deleteByAttDatetimeAndPinAndmark(attSign.getSignDatetime(),
                        attSign.getPersonPin(), "att-sign");
                    // 【实时计算】删除缓存的补签
                    delLeaveAndAddEventCache(attSign, crossDay);
                }
            }
        }
        return false;
    }

    @Override
    public AttSignItem getItemById(String id) {
        List<AttSignItem> items = getByCondition(new AttSignItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public List<AttSignItem> getItemData(String sessionId, AttSignItem condition, int beginIndex, int endIndex) {
        buildCondition(sessionId, condition);
        List<AttSignItem> itemList = attSignDao.getItemsDataBySql(condition.getClass(), SQLUtil.getSqlByItem(condition),
            beginIndex, endIndex, true);
        buildItem(itemList);
        return itemList;
    }

    @Override
    @Transactional
    public void handlerTransfer(List<AttSignItem> attSignItems) {
        // 数据量大的时候 分批处理 jinxian.huang 2019-07-31
        List<List<AttSignItem>> splitAttSignItemList = CollectionUtil.split(attSignItems, CollectionUtil.splitSize);
        for (List<AttSignItem> splitAttSignItems : splitAttSignItemList) {
            // 找出pin号 根据pin号把人员信息补充完整 modified by jinxian.huang 2019-07-29
            Collection<String> pinList =
                CollectionUtil.getPropertyList(splitAttSignItems, AttSignItem::getPersonPin, "-1");
            List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(pinList);
            Map<String, PersPersonItem> persPersonItemMap =
                CollectionUtil.listToKeyMap(persPersonItemList, PersPersonItem::getPin);
            List<AttSign> attSignList = new ArrayList<>();
            for (AttSignItem attSignItem : splitAttSignItems) {
                String personPin = attSignItem.getPersonPin();
                Date signDatetime = attSignItem.getSignDatetime();
                List<AttSign> personSignList = attSignDao.findByPersonPinAndSignDatetime(personPin, signDatetime);
                if (CollectionUtil.isEmpty(personSignList)) {
                    AttSign attSign = new AttSign();
                    if (persPersonItemMap.containsKey(personPin)) {
                        PersPersonItem persPersonItem = persPersonItemMap.get(personPin);
                        if (Objects.nonNull(persPersonItem)) {
                            attSign.setDeptId(persPersonItem.getDeptId());
                            attSign.setPersonId(persPersonItem.getId());
                        }
                    }
                    attSign.setCreateTime(attSignItem.getOperateDatetime());
                    // 流程状态默认设置为2——已完成
                    attSign.setFlowStatus("2");
                    ModelUtil.copyPropertiesIgnoreNullWithProperties(attSignItem, attSign, "id");
                    attSignList.add(attSign);
                } else {
                    // 如果按pin和补签时间已存在则用更新的
                    for (AttSign attSign : personSignList) {
                        ModelUtil.copyPropertiesIgnoreNullWithProperties(attSignItem, attSign, "id");
                        attSignList.add(attSign);
                    }
                }
            }
            attSignDao.saveAll(attSignList);
        }
    }

    @Override
    @Transactional
    public void updateFlowStatus(String businessKey, String status) {
        AttSign attSign = attSignDao.findByBusinessKey(businessKey);

        // 【实时计算】 根据异常状态判断是否需要更新缓存和进行考勤实时计算
        boolean isCalculation = attCalculationCacheManager.judgeStatusCalculation(attSign.getFlowStatus(), status);

        attSign.setFlowStatus(status);
        attSignDao.save(attSign);

        // 【实时计算】保存\删除缓存的补签、新增实时计算事件
        if (isCalculation) {
            String crossDay = attParamService.getCrossDay();
            if (AttConstant.FLOW_STATUS_COMPLETE.equals(attSign.getFlowStatus())) {
                // 保存补签记录到原始记录表
                saveToTransaction(attSign);
                // 保存补签记录到缓存,新增实时计算事件
                setSignAndAddEventCache(attSign, crossDay);
            } else {
                // 删除原始记录表的保存补签记录
                attTransactionService.deleteByAttDatetimeAndPinAndmark(attSign.getSignDatetime(),
                    attSign.getPersonPin(), "att-sign");
                // 删除缓存的补签,新增实时计算事件
                delLeaveAndAddEventCache(attSign, crossDay);
            }
        }
    }

    /**
     * 保存完成的补签记录到原始记录表
     *
     * @param attSign
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/23 16:50
     */
    private void saveToTransaction(AttSign attSign) {
        PersPersonItem persPersonItem = persPersonService.getItemById(attSign.getPersonId());
        AttTransactionItem attTransactionItem = new AttTransactionItem();
        attTransactionItem.setMark("att-sign");
        String attDate = AttDateUtils.dateToStrAsShort(attSign.getSignDatetime());
        String attTime = DateUtil.dateToString(attSign.getSignDatetime(), DateUtil.DateStyle.HH_MM_SS);
        attTransactionItem.setAttDatetime(attSign.getSignDatetime()).setAttDate(attDate).setAttTime(attTime);
        attTransactionItem.setPersonPin(attSign.getPersonPin()).setDeptId(attSign.getDeptId());
        attTransactionItem.setPersonName(persPersonItem.getName()).setPersonLastName(persPersonItem.getLastName());
        attTransactionItem.setDeptCode(persPersonItem.getDeptCode()).setDeptName(persPersonItem.getDeptName());
        attTransactionItem.setAttState(attSign.getAttState());
        attTransactionService.saveItem(attTransactionItem);
        // 实时点名推送
        attRealTimePushService.pushTransaction(Collections.singletonList(attTransactionItem));
    }

    @Override
    public AttSignItem getByBusinessKey(String businessKey) {
        AttSign attSign = attSignDao.findByBusinessKey(businessKey);
        if (attSign != null) {
            AttSignItem attSignItem = ModelUtil.copyPropertiesIgnoreNull(attSign, new AttSignItem());
            attSignItem.setOperateDatetime(attSign.getCreateTime());
            PersPersonItem personItem = persPersonService.getSimpleItemById(attSign.getPersonId());
            if (Objects.nonNull(personItem)) {
                attSignItem.setPersonPin(personItem.getPin());
                attSignItem.setPersonName(personItem.getName());
                attSignItem.setPersonLastName(personItem.getLastName());
            }

            return attSignItem;
        }
        return null;
    }

    @Override
    @Transactional
    public void updateBusinessKeyById(String id, String businessKey) {
        AttSign attSign = attSignDao.findById(id).orElse(null);
        if (attSign != null) {
            attSign.setBusinessKey(businessKey);
        }
    }

    @Override
    public List<Map<String, Object>> getTeamSignTimes(Date startDateTime, Date endDateTime, String personIds) {

        Collection<String> ids = CollectionUtil.strToList(personIds);
        List<Object[]> list = attSignDao.getTeamSignTimes(startDateTime, endDateTime, ids);
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<Map<String, Object>>();
        }
        List<PersPersonItem> persPersonItemList = persPersonService.getSimpleItemsByIds(ids);
        Map<String, PersPersonItem> persPersonItemMap = CollectionUtil.itemListToIdMap(persPersonItemList);
        List<Map<String, Object>> signTimesList = new ArrayList<>();
        list.forEach(objects -> {
            if (objects != null && objects.length >= 3) {
                PersPersonItem persPersonItem = persPersonItemMap.get(objects[0]);
                if (persPersonItem != null) {
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("personPin", objects[1]);
                    map.put("personName", persPersonItem.getName());
                    map.put("times", objects[2]);
                    signTimesList.add(map);
                }
            }
        });

        return signTimesList;
    }

    @Override
    public ZKResultMsg getSignDayScheduleAndRecords(String personId, String date) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        PersPersonItem persPersonItem = persPersonService.getItemById(personId);
        if (persPersonItem != null) {
            AttApiSignDayValidCardItem signDayScheduleAndRecords =
                attWorkflowService.getSignDayScheduleAndRecords(persPersonItem.getPin(), date);
            // 时间段为空表示未排班
            if (StringUtils.isBlank(signDayScheduleAndRecords.getTimeSlots())) {
                zkResultMsg.setRet("fail");
            } else {
                zkResultMsg.setData(signDayScheduleAndRecords);
            }
        }
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg getSignDaySchedule(String personId, String date) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        PersPersonItem persPersonItem = persPersonService.getItemById(personId);
        if (persPersonItem != null) {
            AttApiSignDayValidCardItem signDayScheduleAndRecords =
                attWorkflowService.getSignDaySchedule(persPersonItem.getPin(), date);
            // 时间段为空表示未排班
            if (StringUtils.isBlank(signDayScheduleAndRecords.getTimeSlots())) {
                zkResultMsg.setRet("fail");
            } else {
                zkResultMsg.setData(signDayScheduleAndRecords);
            }
        }
        return zkResultMsg;
    }

    @Override
    @Transactional
    public ZKResultMsg importExcel(List<AttSignItem> itemList, String sessionId) {

        // 是否有审批权限
        boolean hasApproval = authPermissionService.checkPermission(sessionId, "att:sign:approval");

        if (itemList.size() > 30000) {
            // 一次性导入只能30000，超过要分批次导入
            throw ZKBusinessException.warnException("att_import_overData", itemList.size());
        }
        int importSize = itemList.size();
        int beginProgress = 20;
        // 导入的人员要求人事已存在
        Map<String, PersPersonItem> existPersPersonMap = new HashMap<>();
        // 取出待导入数据中人员的pin号
        Collection<String> importPins = CollectionUtil.getPropertyList(itemList, AttSignItem::getPersonPin, "-1");
        // 分批处理，一次处理800人
        List<List<String>> pinsList = CollectionUtil.split(importPins, CollectionUtil.splitSize);
        for (List<String> pins : pinsList) {
            // 根据pin号查出人事人员
            List<PersPersonItem> existPersonList = persPersonService.getItemsByPins(pins);
            if (existPersonList != null && existPersonList.size() > 0) {
                existPersPersonMap.putAll(CollectionUtil.listToKeyMap(existPersonList, PersPersonItem::getPin));
            }
        }
        Iterator<AttSignItem> itemIterator = itemList.iterator();
        // 先剔除无效数据
        while (itemIterator.hasNext()) {
            AttSignItem item = itemIterator.next();
            // 人员编号校验
            if (StringUtils.isBlank(item.getPersonPin())) {
                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                    ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                        I18nUtil.i18nCode("pers_import_pinNotEmpty")))));
                itemIterator.remove();
                continue;
            }
            PersPersonItem persPersonItem = existPersPersonMap.get(item.getPersonPin());
            if (Objects.isNull(persPersonItem)) {
                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                    ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                        I18nUtil.i18nCode("att_areaPerson_persNoExit")))));
                itemIterator.remove();
                continue;
            }
            // 时间校验
            if (Objects.isNull(item.getSignDatetime())) {
                progressCache.setProcess(new ProcessBean(beginProgress, beginProgress,
                    ProcessBean.createErrorContent(I18nUtil.i18nCode("pers_import_fail", item.getRowNum(),
                        I18nUtil.i18nCode("att_sign_signTimeNotNull")))));
                itemIterator.remove();
                continue;
            }
        }
        // 剩下的可以插入数据库,分批处理，一次处理800条
        List<List<AttSignItem>> attSignInsertList = CollectionUtil.split(itemList, CollectionUtil.splitSize);
        for (List<AttSignItem> insertItemList : attSignInsertList) {
            // 保存入库
            List<AttSign> attSignList = new ArrayList<>();
            for (AttSignItem item : insertItemList) {
                PersPersonItem persPersonItem = existPersPersonMap.get(item.getPersonPin());
                if (Objects.nonNull(persPersonItem)) {
                    AttSign attSign = new AttSign();
                    ModelUtil.copyPropertiesWithIgnore(item, attSign, "id");
                    attSign.setDeptId(persPersonItem.getDeptId());
                    attSign.setPersonId(persPersonItem.getId());
                    attSign.setAfterSignRecord(DateUtil.dateToString(item.getSignDatetime(), DateStyle.HH_MM));
                    // 如果有审批权限，直接标记状态为已完成
                    attSign
                        .setFlowStatus(hasApproval ? AttConstant.FLOW_STATUS_COMPLETE : AttConstant.FLOW_STATUS_CREATE);

                    attSignList.add(attSign);

                    if (hasApproval) {

                    }
                }
            }
            attSignDao.saveAll(attSignList);

            if (hasApproval) {
                String crossDay = attParamService.getCrossDay();
                for (AttSign attSign : attSignList) {
                    // 保存补签记录到原始记录表
                    saveToTransaction(attSign);
                    // 【实时计算】保存补签记录到缓存,新增实时计算事件
                    setSignAndAddEventCache(attSign, crossDay);
                }
            }
        }
        // 失败数量
        int faildCount = importSize - itemList.size();
        // 成功：%s 条，失败：%s 条。
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(99,
            I18nUtil.i18nCode("pers_import_result", itemList.size(), faildCount)));
        if (faildCount > 0) {
            return ZKResultMsg.failMsg();
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    @Transactional
    public void approval(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            List<AttSign> attSignList = attSignDao.findByIdList(CollectionUtil.strToList(ids));
            String crossDay = attParamService.getCrossDay();
            for (AttSign attSign : attSignList) {
                if (!AttConstant.FLOW_STATUS_COMPLETE.equals(attSign.getFlowStatus())) {
                    // 保存补签记录到原始记录表
                    saveToTransaction(attSign);
                    // 【实时计算】保存补签记录到缓存,新增实时计算事件
                    setSignAndAddEventCache(attSign, crossDay);
                }
            }
            attSignDao.updateFlowStatus(Arrays.asList(ids.split(",")), AttConstant.FLOW_STATUS_COMPLETE);
        }
    }

    @Override
    @Transactional
    public void refuse(String ids) {
        if (StringUtils.isNotBlank(ids)) {

            List<AttSign> attSignList = attSignDao.findByIdList(CollectionUtil.strToList(ids));
            String crossDay = attParamService.getCrossDay();
            for (AttSign attSign : attSignList) {
                if (AttConstant.FLOW_STATUS_COMPLETE.equals(attSign.getFlowStatus())) {
                    // 删除原始记录表的保存补签记录
                    attTransactionService.deleteByAttDatetimeAndPinAndmark(attSign.getSignDatetime(),
                        attSign.getPersonPin(), "att-sign");
                    // 【实时计算】删除缓存的补签,新增实时计算事件
                    delLeaveAndAddEventCache(attSign, crossDay);
                }
            }
            attSignDao.updateFlowStatus(Arrays.asList(ids.split(",")), AttConstant.FLOW_STATUS_REFUSE);
        }
    }

    @Override
    public Map<String, List<String>> getSignMap(List<String> pins, Date startDate, Date endDate) {
        List<AttSign> attSignList = new ArrayList<>();

        if (CollectionUtil.isEmpty(pins)) {
            attSignList = attSignDao.findSignByDate(startDate, endDate);
        } else if (pins.size() == 1) {
            attSignList = attSignDao.findSignByDateAndPin(startDate, endDate, pins.get(0));
        } else {
            List<List<String>> splitPinList = CollectionUtil.split(pins, CollectionUtil.splitSize);
            for (List<String> subPinList : splitPinList) {
                List<AttSign> subSignList = attSignDao.findSignByDateAndPins(startDate, endDate, subPinList);
                attSignList.addAll(subSignList);
            }
        }

        Map<String, List<String>> attSignMap = new HashMap<>();
        for (AttSign attSign : attSignList) {
            String key = attSign.getPersonPin() + AttCalculationConstant.KEY_CONNECTOR
                + AttDateUtils.dateToStrAsShort(attSign.getSignDatetime());
            List<String> signList = attSignMap.get(key);
            if (null == signList) {
                signList = new ArrayList<>();
            }
            signList.add(AttDateUtils.dateToStrAsLong(attSign.getSignDatetime()));
            attSignMap.put(key, signList);
        }
        return attSignMap;
    }

    /**
     * 【实时计算】保存补签记录到缓存,新增实时计算事件
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/1 15:49
     * @param attSign
     * @param crossDay
     * @return void
     */
    private void setSignAndAddEventCache(AttSign attSign, String crossDay) {

        // 发送消息
        AttSignItem attSignItem = new AttSignItem();
        ModelUtil.copyProperties(attSign, attSignItem);
        attMessageCenterService.pushSignMessage(attSignItem);

        if (attParamService.realTimeEnable()) {
            if (attCalculationCacheManager.judgeCacheDate(attSign.getSignDatetime())) {
                // 保存补签记录到缓存
                String key = attSign.getPersonPin() + AttCalculationConstant.KEY_CONNECTOR
                    + AttDateUtils.dateToStrAsShort(attSign.getSignDatetime());
                attCalculationCacheManager.setSign(key, attSign.getSignDatetime());
                // 新增实时计算事件
                attCalculationCacheManager.addRealTimeEvent(attSign.getPersonPin(), attSign.getSignDatetime(),
                    crossDay);
            }
        }
    }

    /**
     * 【实时计算】删除缓存补签记录,新增实时计算事件
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/1 15:58
     * @param attSign
     * @param crossDay
     * @return void
     */
    private void delLeaveAndAddEventCache(AttSign attSign, String crossDay) {
        if (attParamService.realTimeEnable()) {
            if (attCalculationCacheManager.judgeCacheDate(attSign.getSignDatetime())) {
                String key = attSign.getPersonPin() + AttCalculationConstant.KEY_CONNECTOR
                    + AttDateUtils.dateToStrAsShort(attSign.getSignDatetime());
                // 删除缓存补签记录
                attCalculationCacheManager.delSign(key, attSign.getSignDatetime());

                // 新增实时计算事件
                attCalculationCacheManager.addRealTimeEvent(attSign.getPersonPin(), attSign.getSignDatetime(),
                    crossDay);
            }
        }
    }

    @Override
    public boolean hasStaffApply(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            List<AttSign> list = attSignDao.findByIdList(CollectionUtil.strToList(ids));
            if (!CollectionUtil.isEmpty(list)) {
                for (AttSign attSign : list) {
                    if (StringUtils.isNotBlank(attSign.getBusinessKey())) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    @Override
    public List<AttSignItem> getItemByPinAndDate(String pin, Date startDate, Date endDate) {
        AttSignItem condition = new AttSignItem();
        condition.setEquals(true);
        condition.setPersonPin(pin);
        condition.setStartSignTime(startDate);
        condition.setEndSignTime(endDate);
        condition.setFlowStatusIn(AttConstant.FLOW_STATUS_COMPLETE + "," + AttConstant.FLOW_STATUS_CREATE);
        List<AttSignItem> list = getByCondition(condition);
        return list;
    }

    @Override
    public String getSignShiftName(AttSignItem attSignItem) {
        AttRecordItem attRecordItem = attRecordService.getItemByPinAndDate(attSignItem.getPersonPin(),
            AttDateUtils.dateToStrAsShort(attSignItem.getSignDatetime()));
        String shiftName = I18nUtil.i18nCode("att_leftMenu_tempSch");
        if (Objects.nonNull(attRecordItem)) {
            if (StringUtils.isNotBlank(attRecordItem.getShiftName())) {
                shiftName = attRecordItem.getShiftName();
            } else if (StringUtils.isBlank(attRecordItem.getTimeSlotName())) {
                shiftName = I18nUtil.i18nCode("att_schedule_noSchDetail");
            }
        } else {
            // 没有考勤结果则获取下排班情况
            Map<String,
                List<AttPersonSchBO>> personSchDataMap = attPersonSchDataService.buildPersonSchData(
                    Arrays.asList(attSignItem.getPersonPin()), AttDateUtils.getMinOfDay(attSignItem.getSignDatetime()),
                    AttDateUtils.getMinOfDay(attSignItem.getSignDatetime()));
            List<AttPersonSchBO> attPersonSchBOList = personSchDataMap
                .get(attSignItem.getPersonPin() + "=" + AttDateUtils.dateToStrAsShort(attSignItem.getSignDatetime()));
            if (CollectionUtil.isEmpty(attPersonSchBOList)) {
                shiftName = I18nUtil.i18nCode("att_schedule_noSchDetail");
            } else {
                AttPersonSchBO attPersonSchBO = attPersonSchBOList.get(0);
                if (AttCalculationConstant.AttAttendStatus.NO_SCHEDULING.equals(attPersonSchBO.getAttendStatus())) {
                    shiftName = I18nUtil.i18nCode("att_schedule_noSchDetail");
                } else if (StringUtils.isNotBlank(attPersonSchBO.getAttShiftName())) {
                    shiftName = attPersonSchBO.getAttShiftName();
                }
            }
        }
        return shiftName;
    }
}