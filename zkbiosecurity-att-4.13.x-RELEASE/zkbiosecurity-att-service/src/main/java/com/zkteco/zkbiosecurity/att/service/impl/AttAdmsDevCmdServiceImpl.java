package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.List;

import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.adms.service.AdmsDevCmdService;
import com.zkteco.zkbiosecurity.adms.vo.AdmsDevCmdItem;
import com.zkteco.zkbiosecurity.att.service.AttAdmsDevCmdService;
import com.zkteco.zkbiosecurity.att.vo.AttAdmsDevCmdItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;

@Service
public class AttAdmsDevCmdServiceImpl implements AttAdmsDevCmdService {

    @Autowired(required = false)
    private AdmsDevCmdService admsDevCmdService;

    @Override
    public Pager getAttAdmsDevCmdList(AttAdmsDevCmdItem codition, int pageNo, int pageSize) {
        AdmsDevCmdItem queryItem = buildQueryItem(codition);
        Pager pager = admsDevCmdService.getItemListByPage(queryItem, pageNo, pageSize);
        List<AdmsDevCmdItem> admsDevCmdItemList = (List<AdmsDevCmdItem>)pager.getData();
        List<AttAdmsDevCmdItem> itemList = buildReturnItem(admsDevCmdItemList);
        pager.setData(itemList);
        return pager;
    }

    private AdmsDevCmdItem buildQueryItem(AttAdmsDevCmdItem codition) {
        AdmsDevCmdItem queryItem = new AdmsDevCmdItem();
        queryItem.setAppName(ConstUtil.SYSTEM_MODULE_ATT);
        queryItem.setSubmitStartTime(codition.getSubmitStartTime());
        queryItem.setSubmitEndTime(codition.getSubmitEndTime());
        queryItem.setSn(codition.getSn());
        queryItem.setReturnValue(codition.getReturnValue());
        queryItem.setReturnStartTime(codition.getReturnStartTime());
        queryItem.setReturnEndTime(codition.getReturnEndTime());
        queryItem.setContent(codition.getContent());
        queryItem.setSortName(codition.getSortName());
        queryItem.setSortOrder(codition.getSortOrder());
        return queryItem;
    }

    private List<AttAdmsDevCmdItem> buildReturnItem(List<AdmsDevCmdItem> admsDevCmdItemList) {
        List<AttAdmsDevCmdItem> itemList = ModelUtil.copyListProperties(admsDevCmdItemList, AttAdmsDevCmdItem.class);
        return itemList;
    }

    @Override
    public List<AttAdmsDevCmdItem> export(AttAdmsDevCmdItem codition, int beginIndex, int endIndex) {
        AdmsDevCmdItem queryItem = buildQueryItem(codition);
        List<AdmsDevCmdItem> admsDevCmdItemList = admsDevCmdService.getExportItemList(queryItem, beginIndex, endIndex);
        List<AttAdmsDevCmdItem> itemList = buildReturnItem(admsDevCmdItemList);
        return itemList;
    }

    @Override
    @Transactional
    public ZKResultMsg cleanAttCmd(String ids) {
        admsDevCmdService.deleteByIds(ids);
        return ZKResultMsg.successMsg();
    }
}
