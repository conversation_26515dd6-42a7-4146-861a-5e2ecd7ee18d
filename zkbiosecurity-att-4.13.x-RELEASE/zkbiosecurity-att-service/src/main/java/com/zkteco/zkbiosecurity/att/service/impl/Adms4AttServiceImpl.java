package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.adms.bean.Adms2AttDevUploadInfo;
import com.zkteco.zkbiosecurity.adms.service.Adms4AttService;
import com.zkteco.zkbiosecurity.att.service.AttDeviceOptionService;
import com.zkteco.zkbiosecurity.att.service.AttDeviceService;
import com.zkteco.zkbiosecurity.att.service.AttPersonService;
import com.zkteco.zkbiosecurity.att.vo.AttDeviceItem;

import lombok.extern.slf4j.Slf4j;

/**
 * PUSH协议通讯回调实现
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 15:55
 * @since 1.0.0
 */
@Slf4j
@Service
public class Adms4AttServiceImpl implements Adms4AttService {

    @Autowired
    private AttDeviceService attDeviceService;
    @Autowired
    private AttDeviceOptionService attDeviceOptionService;
    @Autowired
    private AttPersonService attPersonService;

    @Override
    @Transactional
    public Map<String, String> addDevice(Map<String, String> optionMap) {
        log.info("add att device from adms sn = {}", optionMap.get("SN"));
        Map<String, String> returnMap = attDeviceService.addDevice(optionMap);
        return returnMap;
    }

    @Override
    @Transactional
    public void uploadDevInfo(Adms2AttDevUploadInfo devUploadInfo) {
        AttDeviceItem attDeviceItem = new AttDeviceItem();
        attDeviceItem.setDevSn(devUploadInfo.getSn());
        attDeviceItem.setFaceVersion(devUploadInfo.getFaceVersion());
        attDeviceItem.setFpVersion(devUploadInfo.getFpVersion());
        attDeviceItem.setFwVersion(devUploadInfo.getFwVersion());
        attDeviceItem.setIpAddress(devUploadInfo.getIpAddress());
        attDeviceItem.setFaceCount(devUploadInfo.getFaceCount());
        attDeviceItem.setFpCount(devUploadInfo.getFpCount());
        attDeviceItem.setPersonCount(devUploadInfo.getUsercCount());
        // 记录数
        attDeviceItem.setRecordCount(devUploadInfo.getTransactionCount());
        attDeviceItem.setAttSupportFunList(devUploadInfo.getAttSupportFunList());
        attDeviceService.updateUploadInfo(attDeviceItem);
    }

    @Override
    @Transactional
    public boolean updateDeviceOptions(Map<String, String> optionMap) {
        return attDeviceService.updateDeviceOptions(optionMap);
    }

    @Override
    public String remoteAtt(String sn, String pin) {
        return attPersonService.getRemoteAttPersonData(sn, pin);
    }

    @Override
    public void disableDev(String sn) {
        AttDeviceItem item = attDeviceService.getItemBySn(sn);
        if (Objects.nonNull(item)) {
            attDeviceService.disable(item.getId());
        }
    }
}
