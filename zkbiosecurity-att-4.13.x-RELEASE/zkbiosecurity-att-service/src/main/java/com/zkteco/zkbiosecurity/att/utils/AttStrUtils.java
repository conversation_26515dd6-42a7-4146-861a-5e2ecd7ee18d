/**
 * <AUTHOR>
 *
 */
package com.zkteco.zkbiosecurity.att.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AttStrUtils {

    /**
     * 判断是否含有特殊字符
     *
     * @param str
     * @return true为包含，false为不包含
     */
    public static boolean isSpecialChar(String str) {
        String regEx = "[ _`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。，、？]|\n|\r|\t";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        return m.find();
    }

    /**
     * 
     * String 转 Integer List
     * <p>
     * 
     * @since 2015年1月28日 下午5:20:41
     * @param str
     *            字符串
     * @return List
     */
    public static List<String> stringToList(String str) {
        String[] idsArr = str.split(",");
        return toList(idsArr);
    }

    /**
     * 
     * 字符数组转String List
     * <p>
     * 
     * @since 2015年1月28日 下午5:21:32
     * @param strArr
     *            字符串数组
     * @return List
     */
    public static List<String> toList(String[] strArr) {
        List<String> ids = new ArrayList<>();
        for (String id : strArr) {
            ids.add(id);
        }
        return ids;
    }

}