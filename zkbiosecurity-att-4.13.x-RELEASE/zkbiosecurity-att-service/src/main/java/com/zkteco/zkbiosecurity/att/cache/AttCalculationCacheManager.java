package com.zkteco.zkbiosecurity.att.cache;

import java.util.*;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.att.bean.AttRuleParamBean;
import com.zkteco.zkbiosecurity.att.calc.bo.AttLeaveBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttOvertimeBO;
import com.zkteco.zkbiosecurity.att.calc.bo.AttPersonSchBO;
import com.zkteco.zkbiosecurity.att.constants.AttCalculationConstant;
import com.zkteco.zkbiosecurity.att.constants.AttConstant;
import com.zkteco.zkbiosecurity.att.enums.AttRuleEnum;
import com.zkteco.zkbiosecurity.att.service.*;
import com.zkteco.zkbiosecurity.att.utils.AttDateUtils;
import com.zkteco.zkbiosecurity.att.vo.AttShiftItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 实时计算缓存管理
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/8/26 14:30
 */
@Slf4j
@Component
public class AttCalculationCacheManager {

    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;

    @Lazy
    @Autowired
    private AttParamService attParamService;
    @Lazy
    @Autowired
    private AttHolidayService attHolidayService;
    @Lazy
    @Autowired
    private AttTimeSlotService attTimeSlotService;
    @Lazy
    @Autowired
    private AttShiftService attShiftService;
    @Lazy
    @Autowired
    private AttSignService attSignService;
    @Lazy
    @Autowired
    private AttLeaveService attLeaveService;
    @Lazy
    @Autowired
    private AttOvertimeService attOvertimeService;
    @Lazy
    @Autowired
    private AttAdjustService attAdjustService;
    @Lazy
    @Autowired
    private AttClassService attClassService;

    /** 时间范围，前几个月和后几个月 */
    public static final Short ATT_EVENTTIME_BEFORE = -1;
    public static final Short ATT_EVENTTIME_AFTER = 1;
    /** 事件标记 （格式：att:realTimeEvent:日期 = pin集合)） */
    public static final String ATT_REALTIMEEVENT = "att:realTimeEvent:";
    /** 规则参数 */
    public static final String ATT_BASE_RULEPARAM = "att:base:ruleParamData";
    /** 节假日 */
    public static final String ATT_BASE_HOLIDAYDATA = "att:base:holidayData";
    /** 时间段 */
    public static final String ATT_BASE_TIMESLOTDATA = "att:base:timeSlotData";
    /** 班次 */
    public static final String ATT_BASE_SHIFTDATA = "att:base:shiftData";
    /** 补签 */
    public static final String ATT_EXCEPTION_SIGNDATA = "att:exception:signData";
    /** 请假 */
    public static final String ATT_EXCEPTION_LEAVEDATA = "att:exception:leaveData";
    /** 加班 */
    public static final String ATT_EXCEPTION_OVERTIMEDATA = "att:exception:overTimeData";
    /** 调休/补班 */
    public static final String ATT_EXCEPTION_ADJUSTDATA = "att:exception:adjustData";
    /** 调班 */
    public static final String ATT_EXCEPTION_CLASSDATA = "att:exception:classData";
    /** 通用排班对象--分组(临时)/部门(临时)/人员临时排班 */
    public static final String ATT_BASE_COMMSCHDATA = "att:base:commSchData";

    /**
     * 缓存中数据的开始时间，考勤有效开始时间
     */
    public Date getCacheStartDate() {
        return AttDateUtils.getFirstDayWithMonthsAgo(DateUtil.getTodayBeginTime(), ATT_EVENTTIME_BEFORE);
    }

    /**
     * 缓存中数据的结束时间，考勤有效结束时间
     */
    public Date getCacheEndDate() {
        return AttDateUtils.getLastDayWithMonthsAfter(DateUtil.getTodayEndTime(), ATT_EVENTTIME_AFTER);
    }

    /**
     * 判断日期是否在考勤有效结束时间范围内
     */
    public boolean judgeCacheDate(Date date) {
        return date.compareTo(getCacheStartDate()) >= 0 && date.compareTo(getCacheEndDate()) <= 0;
    }

    /**
     * 根据异常状态判断是否需要更新缓存和进行考勤实时计算
     *
     * 状态从完成 改成 其他状态，删除异常记录缓存，新增实时计算事件
     *
     * 状态从其他状态 改成完成，新增异常记录缓存，新增实时计算事件
     *
     */
    public boolean judgeStatusCalculation(String oldStatus, String newStatus) {
        if (AttConstant.FLOW_STATUS_COMPLETE.equals(oldStatus)) {
            return !AttConstant.FLOW_STATUS_COMPLETE.equals(newStatus);
        } else {
            return AttConstant.FLOW_STATUS_COMPLETE.equals(newStatus);
        }
    }

    /* -------------------------- 考勤事件 --------------------------*/
    /**
     * 获取所有实时计算事件key
     */
    public Set<String> getAllRealTimeEvent() {
        return stringRedisTemplate.keys(ATT_REALTIMEEVENT + "*");
    }

    /**
     * 根据key获取实时计算对应的pin集合
     */
    public List<String> getRealTimeEventPinSet(String key) {
        if (exists(key)) {
            return new ArrayList<>(stringRedisTemplate.opsForSet().members(key));
        } else {
            return Collections.EMPTY_LIST;
        }
    }

    /**
     * 新增实时计算事件
     *
     * 跨天排班，根据跨天算第一天或第二天会多保存一天的事件，防止漏算
     */
    public void addRealTimeEvent(String pin, Date datetime, String crossDay) {
        if (attParamService.realTimeEnable()) {
            // 当天实时计算事件
            if (judgeCacheDate(datetime)) {
                String key = ATT_REALTIMEEVENT + AttDateUtils.dateToStrAsShort(datetime);
                stringRedisTemplate.opsForSet().add(key, pin);
            }

            // 跨天排班，根据跨天算第一天或第二天会多保存一天的事件，防止漏算
            if (AttRuleEnum.CrossDay.getValueOne().equals(crossDay)) {
                // 前一天
                Date dateBefore = DateUtil.getDateBefore(datetime, 1);
                if (judgeCacheDate(dateBefore)) {
                    stringRedisTemplate.opsForSet().add(ATT_REALTIMEEVENT + AttDateUtils.dateToStrAsShort(dateBefore),
                        pin);
                }

            } else {
                // 往后一天
                Date dateAfter = DateUtil.getDateAfter(datetime, 1);
                if (judgeCacheDate(dateAfter)) {
                    stringRedisTemplate.opsForSet().add(ATT_REALTIMEEVENT + AttDateUtils.dateToStrAsShort(dateAfter),
                        pin);
                }
            }
        }
    }

    /**
     * 刪除实时事件
     */
    public void delRealTimeEvent(String key) {
        stringRedisTemplate.delete(key);
    }
    /* -------------------------- 考勤事件 --------------------------*/

    /* -------------------------- 规则参数 --------------------------*/
    /**
     * 编辑规则参数保存到缓存
     */
    public AttRuleParamBean setRuleParamBean() {
        AttRuleParamBean attRuleParamBean = attParamService.getRuleParam();
        stringRedisTemplate.opsForValue().set(ATT_BASE_RULEPARAM, JSONObject.toJSONString(attRuleParamBean));
        return attRuleParamBean;
    }

    /**
     * 获取缓存的规则参数，如果不存在，查询数据库并保存到缓存中
     */
    public AttRuleParamBean getRuleParamBean() {
        String ruleParamBeanStr = stringRedisTemplate.opsForValue().get(ATT_BASE_RULEPARAM);
        if (StringUtils.isBlank(ruleParamBeanStr)) {
            return setRuleParamBean();
        } else {
            return JSONObject.parseObject(ruleParamBeanStr, AttRuleParamBean.class);
        }
    }
    /* -------------------------- 规则参数 --------------------------*/

    /* -------------------------- 节假日 --------------------------*/
    /**
     * 初始化节假日保存到缓存
     */
    public Set<String> initHoliday(Date cacheStartDate, Date cacheEndDate) {
        Set<String> holidaySet = attHolidayService.getHolidayDateSet(cacheStartDate, cacheEndDate);
        if (holidaySet != null && holidaySet.size() > 0) {
            for (String holidayDate : holidaySet) {
                setHoliday(holidayDate);
            }
        } else {
            // 防止未检测到key再次查询
            setHoliday("init");
        }
        return holidaySet;
    }

    /**
     * 新增节假日到缓存
     */
    public void setHoliday(String holidayDate) {
        stringRedisTemplate.opsForSet().add(ATT_BASE_HOLIDAYDATA, holidayDate);
    }

    /**
     * 删除缓存中的节假日
     */
    public void delHoliday(String holidayDate) {
        stringRedisTemplate.opsForSet().remove(ATT_BASE_HOLIDAYDATA, holidayDate);
    }

    /**
     * 删除缓存中的节假日
     */
    public void delHolidayByDate(String beforeMonth) {
        Set<String> keySet = stringRedisTemplate.opsForSet().members(ATT_BASE_HOLIDAYDATA);
        if (Objects.nonNull(keySet)) {
            keySet.forEach(key -> {
                if (key.contains(beforeMonth)) {
                    delHoliday(key);
                }
            });
        }
    }

    /**
     * 获取缓存节假日，如果不存在，查询数据库并保存到缓存中
     */
    public Set<String> getHolidaySet() {
        Set<String> holidaySet;
        if ((exists(ATT_BASE_HOLIDAYDATA))) {
            holidaySet = stringRedisTemplate.opsForSet().members(ATT_BASE_HOLIDAYDATA);
        } else {
            holidaySet = initHoliday(getCacheStartDate(), getCacheEndDate());
        }
        return holidaySet == null ? Collections.emptySet() : holidaySet;
    }
    /* -------------------------- 节假日 --------------------------*/

    /* -------------------------- 时间段 --------------------------*/
    /**
     * 更新缓存时间段
     */
    public void setTimeSlotItem(AttTimeSlotItem attTimeSlotItem) {
        stringRedisTemplate.opsForHash().put(ATT_BASE_TIMESLOTDATA, attTimeSlotItem.getId(),
            JSONObject.toJSONString(attTimeSlotItem));
    }

    /**
     * 删除缓存中的时间段
     */
    public void delTimeSlotItem(String id) {
        stringRedisTemplate.opsForHash().delete(ATT_BASE_TIMESLOTDATA, id);
    }

    /**
     * 获取缓存时间段，如果不存在，查询数据库并保存到缓存中
     */
    public Map<String, AttTimeSlotItem> getTimeSlotItemMap() {
        Map<String, AttTimeSlotItem> timeSlotItemMap = new HashMap<>();
        if (exists(ATT_BASE_TIMESLOTDATA)) {
            Map<Object, Object> shiftItemObjectMap = stringRedisTemplate.opsForHash().entries(ATT_BASE_TIMESLOTDATA);
            if (shiftItemObjectMap != null) {
                for (Map.Entry<Object, Object> entry : shiftItemObjectMap.entrySet()) {
                    String key = (String)entry.getKey();
                    Object value = entry.getValue();
                    if (Objects.nonNull(value)) {
                        AttTimeSlotItem attTimeSlotItem =
                            JSONObject.parseObject(value.toString(), AttTimeSlotItem.class);
                        timeSlotItemMap.put(key, attTimeSlotItem);
                    }
                }
            }
        } else {
            List<AttTimeSlotItem> attTimeSlotItemList = attTimeSlotService.getAllTimeSlotItem();
            timeSlotItemMap = CollectionUtil.listToKeyMap(attTimeSlotItemList, AttTimeSlotItem::getId);
            for (AttTimeSlotItem attTimeSlotItem : attTimeSlotItemList) {
                setTimeSlotItem(attTimeSlotItem);
            }
        }
        return timeSlotItemMap;
    }
    /* -------------------------- 时间段 --------------------------*/

    /* -------------------------- 班次 --------------------------*/
    /**
     * 更新缓存的班次
     */
    public void setShiftItem(AttShiftItem attShiftItem) {
        stringRedisTemplate.opsForHash().put(ATT_BASE_SHIFTDATA, attShiftItem.getId(),
            JSONObject.toJSONString(attShiftItem));
    }

    /**
     * 删除缓存的班次
     */
    public void delShiftItem(String id) {
        stringRedisTemplate.opsForHash().delete(ATT_BASE_SHIFTDATA, id);
    }

    /**
     * 获取缓存班次，如果不存在，查询数据库并保存到缓存中
     */
    public Map<String, AttShiftItem> getShiftItemMap() {
        Map<String, AttShiftItem> shiftItemMap = new HashMap<>();
        if (exists(ATT_BASE_SHIFTDATA)) {
            Map<Object, Object> shiftItemObjectMap = stringRedisTemplate.opsForHash().entries(ATT_BASE_SHIFTDATA);
            if (shiftItemObjectMap != null) {
                for (Map.Entry<Object, Object> entry : shiftItemObjectMap.entrySet()) {
                    String key = (String)entry.getKey();
                    Object value = entry.getValue();
                    if (Objects.nonNull(value)) {
                        AttShiftItem attShiftItem = JSONObject.parseObject(value.toString(), AttShiftItem.class);
                        shiftItemMap.put(key, attShiftItem);
                    }
                }
            }
        } else {
            List<AttShiftItem> attShiftItemList = attShiftService.getByCondition(new AttShiftItem());
            shiftItemMap = CollectionUtil.listToKeyMap(attShiftItemList, AttShiftItem::getId);
            for (AttShiftItem attShiftItem : attShiftItemList) {
                setShiftItem(attShiftItem);
            }
        }
        return shiftItemMap;
    }
    /* -------------------------- 班次 --------------------------*/

    /* -------------------------- 补签 --------------------------*/
    /**
     * 根据考勤有效时间范围初始化补签
     */
    public Map<String, List<String>> initSignMap(Date cacheStartDate, Date cacheEndDate) {
        Map<String, List<String>> signMap = attSignService.getSignMap(null, cacheStartDate, cacheEndDate);
        if (signMap != null && signMap.size() > 0) {
            for (Map.Entry<String, List<String>> entry : signMap.entrySet()) {
                for (String signTime : entry.getValue()) {
                    setSign(entry.getKey(), DateUtil.stringToDate(signTime));
                }
            }
        } else {
            // 防止未检测到key再次查询
            stringRedisTemplate.opsForHash().put(ATT_EXCEPTION_SIGNDATA, "init",
                AttDateUtils.dateToStrAsLong(new Date()));
        }
        return signMap;
    }

    /**
     * 更新缓存补签
     */
    public void setSign(String hashKey, Date signDateTime) {
        String signTime = AttDateUtils.dateToStrAsLong(signDateTime);
        List<String> signTimeList = getSign(hashKey);
        if (signTimeList != null && signTimeList.size() > 0) {
            signTimeList.add(signTime);
            stringRedisTemplate.opsForHash().put(ATT_EXCEPTION_SIGNDATA, hashKey, String.join(",", signTimeList));

        } else {
            stringRedisTemplate.opsForHash().put(ATT_EXCEPTION_SIGNDATA, hashKey, signTime);
        }
    }

    /**
     * 删除缓存的补签
     */
    public void delSign(String hashKey, Date signDatetime) {
        String signTime = AttDateUtils.dateToStrAsLong(signDatetime);
        List<String> signTimeList = getSign(hashKey);
        if (signTimeList != null && signTimeList.size() > 0) {
            Iterator<String> signTimeIterator = signTimeList.iterator();
            while (signTimeIterator.hasNext()) {
                String time = signTimeIterator.next();
                if (signTime.equals(time)) {
                    signTimeIterator.remove();
                    // 补签多个相同卡点，只删除一个
                    break;
                }
            }
            if (signTimeList.size() == 0) {
                stringRedisTemplate.opsForHash().delete(ATT_EXCEPTION_SIGNDATA, hashKey);
            } else {
                stringRedisTemplate.opsForHash().put(ATT_EXCEPTION_SIGNDATA, hashKey, String.join(",", signTimeList));
            }
        }
    }

    /**
     * 删除缓存的补签
     */
    public void delSignByDate(String beforeMonth) {
        delExceptionByType(beforeMonth, ATT_EXCEPTION_SIGNDATA);
    }

    /**
     * 根据pin=date，获取缓存中的补签记录
     *
     * @return List<补签日期>
     */
    public List<String> getSign(String hashKey) {
        Object signTimeObj = stringRedisTemplate.opsForHash().get(ATT_EXCEPTION_SIGNDATA, hashKey);
        if (signTimeObj != null) {
            List<String> signTimeList = new ArrayList<>();
            signTimeList.addAll(Arrays.asList(signTimeObj.toString().split(",")));
            return signTimeList;
        }
        return null;
    }

    /**
     * 根据pins获取缓存中的补签记录，如果记录不存在，查选数据库，并保存到缓存中
     *
     * @return Map<pin=date, List<补签日期>
     *
     */
    public Map<String, List<String>> getSignMap(List<String> pins, Date calculateBeginDate, Date calculateEndDate) {
        Map<String, List<String>> attSignMap = new HashMap<>();
        if (exists(ATT_EXCEPTION_SIGNDATA)) {
            // 缓存存在，根据pin=date取出组装成 Map<pin=date, List<补签日期>>
            AttDateUtils.forEachDay(calculateBeginDate, calculateEndDate, date -> {
                for (String pin : pins) {
                    String key = pin + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.dateToStrAsShort(date);
                    List<String> signTimeList = getSign(key);
                    if (signTimeList != null) {
                        attSignMap.put(key, signTimeList);
                    }
                }
            });

        } else {
            // 缓存不存在，根据考勤有效时间范围内初始化补签
            Map<String, List<String>> signMap = initSignMap(getCacheStartDate(), getCacheEndDate());
            if (signMap != null && signMap.size() > 0) {
                AttDateUtils.forEachDay(calculateBeginDate, calculateEndDate, date -> {
                    for (String pin : pins) {
                        String key = pin + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.dateToStrAsShort(date);
                        List<String> signTimeList = signMap.get(key);
                        if (signTimeList != null) {
                            attSignMap.put(key, signTimeList);
                        }
                    }
                });
            }
        }
        return attSignMap;
    }
    /* -------------------------- 补签 --------------------------*/

    /* -------------------------- 请假 --------------------------*/
    /**
     * 根据考勤有效时间范围初始化请假
     */
    public Map<String, List<AttLeaveBO>> initLeaveMap(Date cacheStartDate, Date cacheEndDate) {
        Map<String, List<AttLeaveBO>> leaveMap = attLeaveService.getLeaveMap(null, cacheStartDate, cacheEndDate);
        if (leaveMap != null && leaveMap.size() > 0) {
            for (Map.Entry<String, List<AttLeaveBO>> entry : leaveMap.entrySet()) {
                setLeave(entry.getKey(), entry.getValue(), false);
            }
        } else {
            // 防止未检测到key再次查询
            setLeave("init", new ArrayList<>(), false);
        }
        return leaveMap;
    }

    /**
     * 保存请假记录到缓存 isAdd 新否新增
     */
    public void setLeave(String hashKey, List<AttLeaveBO> attLeaveBOList, boolean isAdd) {
        if (isAdd) {
            // 新增需要判断是否已经存在异常申请，存在则累加异常申请缓存数据
            List<AttLeaveBO> existAttLeaveBOList = getLeave(hashKey);
            if (existAttLeaveBOList != null && existAttLeaveBOList.size() > 0) {
                attLeaveBOList.addAll(existAttLeaveBOList);
            }
        }
        stringRedisTemplate.opsForHash().put(ATT_EXCEPTION_LEAVEDATA, hashKey, JSONObject.toJSONString(attLeaveBOList));
    }

    /**
     * 删除缓存请假记录
     */
    public void delLeave(String pin, String id, Date date) {
        String hashKey = pin + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.dateToStrAsShort(date);
        List<AttLeaveBO> existAttLeaveBOList = getLeave(hashKey);
        if (existAttLeaveBOList != null && existAttLeaveBOList.size() > 0) {
            // 存在多条申请，需要根据id判断删除哪一条申请
            Iterator<AttLeaveBO> existAttLeaveBOIterator = existAttLeaveBOList.iterator();
            while (existAttLeaveBOIterator.hasNext()) {
                AttLeaveBO existAttLeaveBO = existAttLeaveBOIterator.next();
                if (id.equals(existAttLeaveBO.getId())) {
                    existAttLeaveBOIterator.remove();
                }
            }
            if (existAttLeaveBOList.size() > 0) {
                setLeave(hashKey, existAttLeaveBOList, false);
            } else {
                stringRedisTemplate.opsForHash().delete(ATT_EXCEPTION_LEAVEDATA, hashKey);
            }
        }
    }

    /**
     * 删除缓存请假记录
     */
    public void delLeaveByDate(String beforeMonth) {
        delExceptionByType(beforeMonth, ATT_EXCEPTION_LEAVEDATA);
    }

    /**
     * 根据pin=date，获取缓存中的请假记录
     */
    public List<AttLeaveBO> getLeave(String hashKey) {
        Object signTimeObj = stringRedisTemplate.opsForHash().get(ATT_EXCEPTION_LEAVEDATA, hashKey);
        if (signTimeObj != null) {
            return JSONArray.parseArray(signTimeObj.toString(), AttLeaveBO.class);
        }
        return null;
    }

    /**
     * 根据pins获取缓存中的请假记录，如果记录不存在，查选数据库，并保存到缓存中
     */
    public Map<String, List<AttLeaveBO>> getLeaveMap(List<String> pins, Date calculateBeginDate,
        Date calculateEndDate) {
        Map<String, List<AttLeaveBO>> attLeaveMap = new HashMap<>();
        if (exists(ATT_EXCEPTION_LEAVEDATA)) {
            // 缓存存在，根据pin=date取出数据
            AttDateUtils.forEachDay(calculateBeginDate, calculateEndDate, date -> {
                for (String pin : pins) {
                    String key = pin + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.dateToStrAsShort(date);
                    List<AttLeaveBO> attLeaveBOList = getLeave(key);
                    if (attLeaveBOList != null) {
                        attLeaveMap.put(key, attLeaveBOList);
                    }

                }
            });
        } else {
            // 缓存不存在，根据考勤有效时间范围初始化请假
            Map<String, List<AttLeaveBO>> leaveMap = initLeaveMap(getCacheStartDate(), getCacheEndDate());
            if (leaveMap != null && leaveMap.size() > 0) {
                AttDateUtils.forEachDay(calculateBeginDate, calculateEndDate, date -> {
                    for (String pin : pins) {
                        String key = pin + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.dateToStrAsShort(date);
                        List<AttLeaveBO> attLeaveBOList = leaveMap.get(key);
                        if (attLeaveBOList != null) {
                            attLeaveMap.put(key, attLeaveBOList);
                        }
                    }
                });
            }
        }
        return attLeaveMap;
    }
    /* -------------------------- 请假 --------------------------*/

    /* -------------------------- 加班 --------------------------*/
    /**
     * 根据考勤有效时间范围初始化请假
     */
    public Map<String, List<AttOvertimeBO>> initOvertimeMap(Date cacheStartDate, Date cacheEndDate) {
        Map<String, List<AttOvertimeBO>> overtimeMap =
            attOvertimeService.getOvertimeMap(null, cacheStartDate, cacheEndDate);
        if (overtimeMap != null && overtimeMap.size() > 0) {
            for (Map.Entry<String, List<AttOvertimeBO>> entry : overtimeMap.entrySet()) {
                setOvertime(entry.getKey(), entry.getValue(), false);
            }
        } else {
            // 防止未检测到key再次查询
            setOvertime("init", new ArrayList<>(), false);
        }
        return overtimeMap;
    }

    /**
     * 保存加班记录到缓存，格式为 Map<pin=date, List<AttOvertimeBO>>
     */
    public void setOvertime(String hashKey, List<AttOvertimeBO> attOvertimeBOList, boolean isAdd) {
        if (isAdd) {
            // 新增需要判断是否已经存在异常申请，存在则累加异常申请缓存数据
            List<AttOvertimeBO> existAttOvertimeBOList = getOvertime(hashKey);
            if (existAttOvertimeBOList != null && existAttOvertimeBOList.size() > 0) {
                attOvertimeBOList.addAll(existAttOvertimeBOList);
            }
        }
        stringRedisTemplate.opsForHash().put(ATT_EXCEPTION_OVERTIMEDATA, hashKey,
            JSONObject.toJSONString(attOvertimeBOList));
    }

    /**
     * 删除缓存加班记录
     */
    public void delOvertime(String pin, String id, Date date) {
        String hashKey = pin + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.dateToStrAsShort(date);
        List<AttOvertimeBO> existAttOvertimeBOList = getOvertime(hashKey);
        if (existAttOvertimeBOList != null && existAttOvertimeBOList.size() > 0) {
            // 存在多条申请，需要根据id判断删除哪一条申请
            Iterator<AttOvertimeBO> existAttOvertimeBOIterator = existAttOvertimeBOList.iterator();
            while (existAttOvertimeBOIterator.hasNext()) {
                AttOvertimeBO existAttOvertimeBO = existAttOvertimeBOIterator.next();
                if (id.equals(existAttOvertimeBO.getId())) {
                    existAttOvertimeBOIterator.remove();
                }
            }
            if (existAttOvertimeBOList.size() > 0) {
                setOvertime(hashKey, existAttOvertimeBOList, false);
            } else {
                stringRedisTemplate.opsForHash().delete(ATT_EXCEPTION_OVERTIMEDATA, hashKey);
            }
        }
    }

    /**
     * 删除缓存加班记录
     */
    public void delOvertimeByDate(String beforeMonth) {
        delExceptionByType(beforeMonth, ATT_EXCEPTION_OVERTIMEDATA);
    }

    /**
     * 根据pin=date，获取缓存中的加班记录
     */
    public List<AttOvertimeBO> getOvertime(String hashKey) {
        Object signTimeObj = stringRedisTemplate.opsForHash().get(ATT_EXCEPTION_OVERTIMEDATA, hashKey);
        if (signTimeObj != null) {
            return JSONArray.parseArray(signTimeObj.toString(), AttOvertimeBO.class);
        }
        return null;
    }

    /**
     * 根据pins获取缓存中的加班记录，如果记录不存在，查选数据库，并保存到缓存中
     */
    public Map<String, List<AttOvertimeBO>> getOvertimeMap(List<String> pins, Date calculateBeginDate,
        Date calculateEndDate) {
        Map<String, List<AttOvertimeBO>> attOvertimeMap = new HashMap<>();
        if (exists(ATT_EXCEPTION_OVERTIMEDATA)) {
            // 缓存存在，根据pin=date取出数据
            AttDateUtils.forEachDay(calculateBeginDate, calculateEndDate, date -> {
                for (String pin : pins) {
                    String attDateStr = AttDateUtils.dateToStrAsShort(date);
                    String key = pin + AttCalculationConstant.KEY_CONNECTOR + attDateStr;
                    List<AttOvertimeBO> attOvertimeBOList = getOvertime(key);
                    if (attOvertimeBOList != null) {
                        attOvertimeMap.put(key, attOvertimeBOList);
                    }

                }
            });
        } else {
            // 缓存不存在，根据考勤有效时间范围初始化请假
            Map<String, List<AttOvertimeBO>> overtimeMap = initOvertimeMap(getCacheStartDate(), getCacheEndDate());
            if (overtimeMap != null && overtimeMap.size() > 0) {
                AttDateUtils.forEachDay(calculateBeginDate, calculateEndDate, date -> {
                    for (String pin : pins) {
                        String attDateStr = AttDateUtils.dateToStrAsShort(date);
                        String key = pin + AttCalculationConstant.KEY_CONNECTOR + attDateStr;
                        List<AttOvertimeBO> attOvertimeBOList = overtimeMap.get(key);
                        if (attOvertimeBOList != null) {
                            attOvertimeMap.put(key, attOvertimeBOList);
                        }
                    }
                });
            }
        }
        return attOvertimeMap;
    }
    /* -------------------------- 加班 --------------------------*/

    /* -------------------------- 调休 --------------------------*/
    /**
     * 根据考勤有效时间范围初始化调休
     */
    public Map<String, List<AttPersonSchBO>> initAdjustMap(Date cacheStartDate, Date cacheEndDate) {
        Map<String, List<AttPersonSchBO>> adjustMap = attAdjustService.getAdjustMap(null, cacheStartDate, cacheEndDate);
        if (adjustMap != null && adjustMap.size() > 0) {
            for (Map.Entry<String, List<AttPersonSchBO>> entry : adjustMap.entrySet()) {
                setAdjust(entry.getKey(), entry.getValue(), false);
            }
        } else {
            // 防止未检测到key再次查询
            setAdjust("init", new ArrayList<>(), false);
        }
        return adjustMap;
    }

    /**
     * 保存调休录到缓存，格式为 Map<pin=date, List<AttPersonSchBO>>
     */
    public void setAdjust(String hashKey, List<AttPersonSchBO> attPersonSchBOList, boolean isAdd) {
        if (isAdd) {
            // 新增需要判断是否已经存在异常申请，存在则累加异常申请缓存数据
            List<AttPersonSchBO> existAttPersonSchBOList = getAdjust(hashKey);
            if (existAttPersonSchBOList != null && existAttPersonSchBOList.size() > 0) {
                attPersonSchBOList.addAll(existAttPersonSchBOList);
            }
        }
        stringRedisTemplate.opsForHash().put(ATT_EXCEPTION_ADJUSTDATA, hashKey,
            JSONObject.toJSONString(attPersonSchBOList));
    }

    /**
     * 删除缓存调休
     */
    public void delAdjust(String pin, String id, Date date) {
        String hashKey = pin + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.dateToStrAsShort(date);
        List<AttPersonSchBO> existAttPersonSchBOList = getAdjust(hashKey);
        if (existAttPersonSchBOList != null && existAttPersonSchBOList.size() > 0) {
            // 存在多条申请，需要根据id判断删除哪一条申请
            Iterator<AttPersonSchBO> existAttPersonSchBOIterator = existAttPersonSchBOList.iterator();
            while (existAttPersonSchBOIterator.hasNext()) {
                AttPersonSchBO existAttPersonSchBO = existAttPersonSchBOIterator.next();
                if (id.equals(existAttPersonSchBO.getId())) {
                    existAttPersonSchBOIterator.remove();
                }
            }
            if (existAttPersonSchBOList.size() > 0) {
                setAdjust(hashKey, existAttPersonSchBOList, false);
            } else {
                stringRedisTemplate.opsForHash().delete(ATT_EXCEPTION_ADJUSTDATA, hashKey);
            }
        }
    }

    /**
     * 根据起止时间删除缓存调休
     */
    public void delAdjustByDate(String beforeMonth) {
        delExceptionByType(beforeMonth, ATT_EXCEPTION_ADJUSTDATA);
    }

    /**
     * 根据pin=date，获取缓存中的调休记录
     */
    public List<AttPersonSchBO> getAdjust(String hashKey) {
        Object signTimeObj = stringRedisTemplate.opsForHash().get(ATT_EXCEPTION_ADJUSTDATA, hashKey);
        if (signTimeObj != null) {
            return JSONArray.parseArray(signTimeObj.toString(), AttPersonSchBO.class);
        }
        return null;
    }

    /**
     * 根据pins获取缓存中的调休记录，如果记录不存在，查选数据库，并保存到缓存中
     */
    public Map<String, List<AttPersonSchBO>> getAdjustMap(List<String> pins, Date calculateBeginDate,
        Date calculateEndDate) {
        Map<String, List<AttPersonSchBO>> attAdjustMap = new HashMap<>();
        if (exists(ATT_EXCEPTION_ADJUSTDATA)) {
            // 缓存存在，根据pin=date取出数据
            AttDateUtils.forEachDay(calculateBeginDate, calculateEndDate, date -> {
                for (String pin : pins) {
                    String key = pin + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.dateToStrAsShort(date);
                    List<AttPersonSchBO> attPersonSchBOList = getAdjust(key);
                    if (attPersonSchBOList != null) {
                        attAdjustMap.put(key, attPersonSchBOList);
                    }

                }
            });
        } else {
            // 缓存不存在，根据考勤有效时间范围初始化调休
            Map<String, List<AttPersonSchBO>> adjustMap = initAdjustMap(getCacheStartDate(), getCacheEndDate());
            if (adjustMap != null && adjustMap.size() > 0) {
                AttDateUtils.forEachDay(calculateBeginDate, calculateEndDate, date -> {
                    for (String pin : pins) {
                        String key = pin + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.dateToStrAsShort(date);
                        List<AttPersonSchBO> attPersonSchBOList = adjustMap.get(key);
                        if (attPersonSchBOList != null) {
                            attAdjustMap.put(key, attPersonSchBOList);
                        }
                    }
                });
            }
        }
        return attAdjustMap;
    }
    /* -------------------------- 调休 --------------------------*/

    /* -------------------------- 调班 --------------------------*/
    /**
     * 根据考勤有效时间范围初始化调班
     */
    public Map<String, List<AttPersonSchBO>> initClassMap(Date cacheStartDate, Date cacheEndDate) {
        Map<String, List<AttPersonSchBO>> classMap = attClassService.getClassMap(null, cacheStartDate, cacheEndDate);
        if (classMap != null && classMap.size() > 0) {
            for (Map.Entry<String, List<AttPersonSchBO>> entry : classMap.entrySet()) {
                setClass(entry.getKey(), entry.getValue(), false);
            }
        } else {
            // 防止未检测到key再次查询
            setClass("init", new ArrayList<>(), false);
        }
        return classMap;
    }

    /**
     * 保存调班记录到缓存，格式为 Map<pin=date, List<AttPersonSchBO>>
     */
    public void setClass(String hashKey, List<AttPersonSchBO> attPersonSchBOList, boolean isAdd) {
        if (isAdd) {
            // 新增需要判断是否已经存在异常申请，存在则累加异常申请缓存数据
            List<AttPersonSchBO> existAttPersonSchBOList = getClass(hashKey);
            if (existAttPersonSchBOList != null && existAttPersonSchBOList.size() > 0) {
                attPersonSchBOList.addAll(existAttPersonSchBOList);
            }
        }
        stringRedisTemplate.opsForHash().put(ATT_EXCEPTION_CLASSDATA, hashKey,
            JSONObject.toJSONString(attPersonSchBOList));
    }

    /**
     * 删除缓存调班
     */
    public void delClass(String pin, String id, Date date) {
        String hashKey = pin + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.dateToStrAsShort(date);
        List<AttPersonSchBO> existAttPersonSchBOList = getClass(hashKey);
        if (existAttPersonSchBOList != null && existAttPersonSchBOList.size() > 0) {
            // 存在多条申请，需要根据id判断删除哪一条申请
            Iterator<AttPersonSchBO> existAttPersonSchBOIterator = existAttPersonSchBOList.iterator();
            while (existAttPersonSchBOIterator.hasNext()) {
                AttPersonSchBO existAttPersonSchBO = existAttPersonSchBOIterator.next();
                if (id.equals(existAttPersonSchBO.getId())) {
                    existAttPersonSchBOIterator.remove();
                }
            }
            if (existAttPersonSchBOList.size() > 0) {
                setClass(hashKey, existAttPersonSchBOList, false);
            } else {
                stringRedisTemplate.opsForHash().delete(ATT_EXCEPTION_CLASSDATA, hashKey);
            }
        }
    }

    /**
     * 根据起止时间删除缓存调班
     */
    public void delClassByDate(String beforeMonth) {
        delExceptionByType(beforeMonth, ATT_EXCEPTION_CLASSDATA);
    }

    /**
     * 根据pin=date，获取缓存中的调班记录
     */
    public List<AttPersonSchBO> getClass(String hashKey) {
        Object signTimeObj = stringRedisTemplate.opsForHash().get(ATT_EXCEPTION_CLASSDATA, hashKey);
        if (signTimeObj != null) {
            return JSONArray.parseArray(signTimeObj.toString(), AttPersonSchBO.class);
        }
        return null;
    }

    /**
     * 根据pins获取缓存中的调班记录，如果记录不存在，查选数据库，并保存到缓存中
     */
    public Map<String, List<AttPersonSchBO>> getClassMap(List<String> pins, Date calculateBeginDate,
        Date calculateEndDate) {
        Map<String, List<AttPersonSchBO>> attClassMap = new HashMap<>();
        if (exists(ATT_EXCEPTION_CLASSDATA)) {
            // 缓存存在，根据pin=date取出数据
            AttDateUtils.forEachDay(calculateBeginDate, calculateEndDate, date -> {
                for (String pin : pins) {
                    String key = pin + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.dateToStrAsShort(date);
                    List<AttPersonSchBO> attPersonSchBOList = getClass(key);
                    if (attPersonSchBOList != null) {
                        attClassMap.put(key, attPersonSchBOList);
                    }

                }
            });
        } else {
            // 缓存不存在，查询指定月份的所有调班记录
            Map<String, List<AttPersonSchBO>> classMap = initClassMap(getCacheStartDate(), getCacheEndDate());
            if (classMap != null && classMap.size() > 0) {
                AttDateUtils.forEachDay(calculateBeginDate, calculateEndDate, date -> {
                    for (String pin : pins) {
                        String key = pin + AttCalculationConstant.KEY_CONNECTOR + AttDateUtils.dateToStrAsShort(date);
                        List<AttPersonSchBO> attPersonSchBOList = classMap.get(key);
                        if (attPersonSchBOList != null) {
                            attClassMap.put(key, attPersonSchBOList);
                        }
                    }
                });
            }
        }
        return attClassMap;
    }
    /* -------------------------- 调班 --------------------------*/

    /**
     * 判断key是否存在
     */
    public boolean exists(String key) {
        return stringRedisTemplate.hasKey(key);
    }

    /**
     * 根据异常类型和月份模糊删除缓存
     * 
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/1 11:51
     * @param beforeMonth
     * @param keyType
     * @return void
     */
    private void delExceptionByType(String beforeMonth, String keyType) {
        Set<Object> keySet = stringRedisTemplate.opsForHash().keys(keyType);
        if (Objects.nonNull(keySet)) {
            keySet.forEach(key -> {
                if (key.toString().contains(beforeMonth)) {
                    stringRedisTemplate.opsForHash().delete(keyType, key);
                }
            });
        }
    }
}
