package com.zkteco.zkbiosecurity.att.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 自定义规则 使用对象(分组,部门)
 */
@Entity
@Table(name = "ATT_CUSTOM_RULE_ORG")
@Setter
@Getter
@Accessors(chain = true)
public class AttCustomRuleOrg extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 规则ID
     */
    @Column(name = "RULE_ID")
    private String ruleId;

    /**
     * 部门或分组ID
     */
    @Column(name = "ORG_ID")
    private String orgId;

}
