package com.zkteco.zkbiosecurity.att.license;

import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.service.AttPointService;
import com.zkteco.zkbiosecurity.auth.provider.AuthSessionProvider;
import com.zkteco.zkbiosecurity.auth.service.AuthLicenseMenuManager;
import com.zkteco.zkbiosecurity.auth.vo.AuthLicenseMenuItem;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.SpringContextUtil;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.ModuleInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 非员工登录隐藏流程任务菜单
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 11:12 2020/6/11
 */
@Component
public class AttLicenseMenuManagerImpl implements AuthLicenseMenuManager {

    @Value("${system.language:zh_CN}")
    private String language;
    @Value("${system.zkcloud.productCode:}")
    private String zkCloudProductCode;

    @Autowired
    private AuthSessionProvider authSessionProvider;
    @Autowired
    private ModuleInfoService moduleInfoService;
    @Autowired
    private AttParamService attParamService;
    @Autowired
    private AttPointService attPointService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public List<AuthLicenseMenuItem> handlerSystemMenus(String sessionId) {
        List<AuthLicenseMenuItem> licenseMenuItems = new ArrayList<>();

        // 员工自助显示流程任务、出差、外出
        licenseMenuStaff(licenseMenuItems, sessionId);

        // 如果存在第三方当考勤模块，显示考勤点菜单，否则不显示
        licenseMenuPoint(licenseMenuItems);

        // 启用年假才显示年假报表
        licenseMenuAnnualReport(licenseMenuItems);

        if ("ZKTime".equals(zkCloudProductCode)) {
            // 单考勤zktime隐藏系统管理音频文件和数据迁移菜单
            zkTimeHideMenu(licenseMenuItems);
        }

        /*if (!"zh_CN".equals(language)) {
            // 海外隐藏移动端签到地址
            hideSignAddressMenu(licenseMenuItems);
        }*/

        // 沒有工作流，隐藏工作流菜单
        try {
            SpringContextUtil.getBean("workflowInit");
        } catch (Exception e) {
            hideWorkflowMenu(licenseMenuItems);
        }

        return licenseMenuItems;
    }

    /**
     * 沒有工作流，隐藏工作流菜单
     *
     * @param licenseMenuItems:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/12/1 14:19
     * @since 1.0.0
     */
    private void hideWorkflowMenu(List<AuthLicenseMenuItem> licenseMenuItems) {
        // 流程设置
        AuthLicenseMenuItem flow = new AuthLicenseMenuItem();
        flow.setModule(ConstUtil.SYSTEM_MODULE_ATT);
        flow.setPermission("wf:flow");
        flow.setLicenseMenu(false);
        licenseMenuItems.add(flow);
    }

    /**
     * zktime隐藏系统管理音频和数据迁移菜单
     *
     * @param licenseMenuItems:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/1/4 10:23
     * @since 1.0.0
     */
    private void zkTimeHideMenu(List<AuthLicenseMenuItem> licenseMenuItems) {
        // 音频文件
        AuthLicenseMenuItem mediaFile = new AuthLicenseMenuItem();
        mediaFile.setModule(ConstUtil.SYSTEM_MODULE_BASE);
        mediaFile.setPermission("system:mediaFile");
        mediaFile.setLicenseMenu(false);
        licenseMenuItems.add(mediaFile);
        // 数据迁移
        AuthLicenseMenuItem dataMigration = new AuthLicenseMenuItem();
        dataMigration.setModule(ConstUtil.SYSTEM_MODULE_BASE);
        dataMigration.setPermission("system:dataMigration");
        dataMigration.setLicenseMenu(false);
        licenseMenuItems.add(dataMigration);
    }

    /**
     * 隐藏移动端签到地址
     *
     * @param licenseMenuItems:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/1/4 20:38
     * @since 1.0.0
     */
    private void hideSignAddressMenu(List<AuthLicenseMenuItem> licenseMenuItems) {
        // 移动端签到地址
        AuthLicenseMenuItem signAddress = new AuthLicenseMenuItem();
        signAddress.setModule(ConstUtil.SYSTEM_MODULE_BASE);
        signAddress.setPermission("att:signAddress");
        signAddress.setLicenseMenu(false);
        licenseMenuItems.add(signAddress);
    }

    /**
     * 员工自助显示流程任务、出差、外出
     *
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/10/13 9:37
     * @since 1.0.0
     */
    private void licenseMenuStaff(List<AuthLicenseMenuItem> licenseMenuItems, String sessionId) {
        SecuritySubject securitySubject = authSessionProvider.getSecuritySubject(sessionId);
        boolean showMenu = securitySubject.getStaff() == null ? false : securitySubject.getStaff();
        // 流程任务
        AuthLicenseMenuItem wfTask = new AuthLicenseMenuItem();
        wfTask.setModule(ConstUtil.SYSTEM_MODULE_ATT);
        wfTask.setPermission("wf:task");
        wfTask.setLicenseMenu(showMenu);
        licenseMenuItems.add(wfTask);
        // 出差
        AuthLicenseMenuItem attTrip = new AuthLicenseMenuItem();
        attTrip.setModule(ConstUtil.SYSTEM_MODULE_ATT);
        attTrip.setPermission("att:trip");
        attTrip.setLicenseMenu(showMenu);
        licenseMenuItems.add(attTrip);
        // 外出
        AuthLicenseMenuItem attOut = new AuthLicenseMenuItem();
        attOut.setModule(ConstUtil.SYSTEM_MODULE_ATT);
        attOut.setPermission("att:out");
        attOut.setLicenseMenu(showMenu);
        licenseMenuItems.add(attOut);
    }

    /**
     * 如果存在第三方当考勤模块，显示考勤点菜单，否则不显示
     *
     * @param licenseMenuItems:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/10/13 9:35
     * @since 1.0.0
     */
    private void licenseMenuPoint(List<AuthLicenseMenuItem> licenseMenuItems) {
        AuthLicenseMenuItem point = new AuthLicenseMenuItem();
        point.setModule(ConstUtil.SYSTEM_MODULE_ATT);
        point.setPermission("att:point");
        point.setLicenseMenu(attPointService.existOtherAttModule());
        licenseMenuItems.add(point);
    }

    /**
     * 启用年假才显示年假报表
     *
     * @param licenseMenuItems:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/10/23 11:11
     * @since 1.0.0
     */
    private void  licenseMenuAnnualReport(List<AuthLicenseMenuItem> licenseMenuItems) {
        AuthLicenseMenuItem annualLeaveReport = new AuthLicenseMenuItem();
        annualLeaveReport.setModule(ConstUtil.SYSTEM_MODULE_ATT);
        annualLeaveReport.setPermission("att:annualLeaveReport");
        annualLeaveReport.setLicenseMenu(attParamService.enableAnnualLeave());
        licenseMenuItems.add(annualLeaveReport);
    }
}