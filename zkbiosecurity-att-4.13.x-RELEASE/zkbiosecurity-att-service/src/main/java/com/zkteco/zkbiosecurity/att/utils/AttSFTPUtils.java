package com.zkteco.zkbiosecurity.att.utils;

import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;

/**
 * SFTP工具类
 *
 * <AUTHOR>
 * @date 2024/10/23 15:14
 */
@Slf4j
public class AttSFTPUtils {

    /**
     * 创建SFTP连接
     *
     * @param host SFTP服务器地址
     * @param port 端口
     * @param username 用户名
     * @param password 密码
     * @return SFTP连接
     * <AUTHOR>
     * @date 2024/10/24 10:58
     */
    public static ChannelSftp createSftp(String host, int port, String username, String password) throws JSchException {
        JSch jsch = new JSch();
        log.info("Try to connect sftp[" + username + "@" + host + "]");
        Session session = createSession(jsch, host, username, port);
        session.setPassword(password);
        session.setConfig("StrictHostKeyChecking", "no");
        // 默认情况下，JSch库本身并没有会话超时时间。
        // 为了避免长时间无活动连接占用资源或因网络问题导致连接挂起而不被释放，通常建议设置会话超时，（单位：毫秒）
        session.setTimeout(60000);
        session.connect();
        log.info("Session connected to {}.", host);
        Channel channel = session.openChannel("sftp");
        channel.connect();
        log.info("Channel created to {}.", host);
        return (ChannelSftp) channel;
    }

    /**
     * 创建 Session
     *
     * @param jsch jsch
     * @param host SFTP服务器地址
     * @param username 用户名
     * @param port 密码
     * @return Session
     * <AUTHOR>
     * @date 2024/10/24 11:00
     */
    public static Session createSession(JSch jsch, String host, String username, Integer port) throws JSchException {
        Session session;
        if (port <= 0) {
            session = jsch.getSession(username, host);
        } else {
            session = jsch.getSession(username, host, port);
        }
        if (session == null) {
            throw new RuntimeException(host + "session is null");
        }
        return session;
    }

    /**
     * 关闭连接
     *
     * @param sftp ChannelSftp
     * <AUTHOR>
     * @date 2024/10/24 11:01
     */
    public static void disconnect(ChannelSftp sftp) {
        try {
            if (sftp != null) {
                if (sftp.isConnected()) {
                    sftp.disconnect();
                } else if (sftp.isClosed()) {
                    log.error("sftp connection already closed");
                }
                if (sftp.getSession() != null) {
                    sftp.getSession().disconnect();
                }
            }
        } catch (JSchException e) {
            log.error("sftp disconnect fail，reason：{}", e.getMessage(), e);
        }
    }

    /**
     * 上传文件
     *
     * @param channelSftp ChannelSftp
     * @param attachmentsFile 要上传的文件
     * @param sftpPath 要放入SFTP服务器的目录
     * @return 上传是否成功
     * <AUTHOR>
     * @date 2024/10/24 11:09
     */
    public static boolean upload(ChannelSftp channelSftp, File attachmentsFile, String sftpPath) {
        boolean isSuccess = false;
        // 校验文件是否存在
        if (attachmentsFile == null || !attachmentsFile.exists() || !attachmentsFile.isFile()) {
            log.error("The file does not exist or is not a valid file. File path: {}", attachmentsFile == null ? "null" : attachmentsFile.getAbsolutePath());
            return isSuccess;
        }

        try (InputStream in = Files.newInputStream(attachmentsFile.toPath())) {
            // 进入sftp文件目录
            channelSftp.cd(sftpPath);
            // 上传文件
            channelSftp.put(in, attachmentsFile.getName());
            isSuccess = true;
        } catch (Exception e) {
            log.error("upload SFTP file fail", e);
        }
        return isSuccess;
    }

    /**
     * 判断SFTP服务器是否存在指定目录
     *
     * @param channelSftp ChannelSftp
     * @param remoteDirectory 远程SFTP服务器目录
     * @return 是否存在
     * <AUTHOR>
     * @date 2024/10/24 11:14
     */
    public static boolean isDirectoryExist(ChannelSftp channelSftp, String remoteDirectory) {
        boolean isExist = true;
        try {
            channelSftp.stat(remoteDirectory);
        } catch (Exception e) {
            log.info("remote directory is not exist: " + remoteDirectory);
            isExist = false;
        }
        return isExist;
    }

    /**
     * 上传文件到SFTP服务器，入口方法
     *
     * @param host SFTP服务器地址
     * @param port 端口
     * @param username 用户名
     * @param password 密码
     * @param remoteDirectory 要上传的SFTP目的端服务器目录
     * @param attachmentFile 要上传的文件
     * @return 是否上传成功
     * <AUTHOR>
     * @date 2024/10/24 11:15
     */
    public static boolean uploadFileToServer(String host, int port, String username, String password, String remoteDirectory, File attachmentFile) {
        boolean isSuccess = false;
        // 相关参数不能为空
        if (StringUtils.isAnyBlank(host, username, password, remoteDirectory)) {
            log.error("upload File To SFTP Server fail, please check the parameter");
            return isSuccess;
        }
        ChannelSftp channelSftp = null;
        try {
            // 连接SFTP服务器
            channelSftp = createSftp(host, port, username, password);
            // 判断远程目录是否存在
            if (!isDirectoryExist(channelSftp, remoteDirectory)) {
                // 创建远程目录
                String[] dirArr = remoteDirectory.split("/");
                String path = "";
                for (String s : dirArr) {
                    path = path + "/" + s;
                    log.info("upload File To SFTP Server path = {}", path);
                    if (!isDirectoryExist(channelSftp, path)) {
                        channelSftp.mkdir(path);
                        channelSftp.cd(path);
                    } else {
                        channelSftp.cd(path);
                    }
                }
            }
            // 上传文件
            isSuccess = upload(channelSftp, attachmentFile, "/" + remoteDirectory);
        } catch (Exception e) {
            log.error("upload File To SFTP Server fail", e);
        } finally {
            // 关闭连接
            if (channelSftp != null) {
                disconnect(channelSftp);
            }
        }
        return isSuccess;
    }

    /**
     * 测试SFTP连接
     *
     * @param host SFTP服务器地址
     * @param port 端口
     * @param username 用户名
     * @param password 密码
     * @return 测试结果
     * <AUTHOR>
     * @date 2024/10/24 16:29
     */
    public static boolean testConnection(String host, int port, String username, String password) {
        boolean isSuccess = false;
        JSch jsch = new JSch();
        Session session = null;
        Channel channel = null;
        ChannelSftp sftpChannel = null;

        try {
            // 建立Session连接
            session = jsch.getSession(username, host, port);
            session.setConfig("StrictHostKeyChecking", "no");
            session.setPassword(password);
            // 默认情况下，JSch库本身并没有会话超时时间。
            // 为了避免长时间无活动连接占用资源或因网络问题导致连接挂起而不被释放，通常建议设置会话超时，（单位：毫秒）
            session.setTimeout(5 * 1000);
            session.connect();

            // 开启SFTP通道
            channel = session.openChannel("sftp");
            channel.connect();

            // 转换为SFTP通道
            sftpChannel = (ChannelSftp) channel;

            isSuccess = true;
            log.info("SFTP connection success [" + username + "@" + host + "]");
        } catch (JSchException e) {
            log.error("SFTP connection fail", e);
        } finally {
            // 关闭通道和Session
            if (sftpChannel != null) {
                sftpChannel.exit();
            }
            if (channel != null) {
                channel.disconnect();
            }
            if (session != null) {
                session.disconnect();
            }
        }
        return isSuccess;
    }
}
