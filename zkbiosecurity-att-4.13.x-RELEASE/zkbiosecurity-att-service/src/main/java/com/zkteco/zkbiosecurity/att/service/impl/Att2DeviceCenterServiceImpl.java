package com.zkteco.zkbiosecurity.att.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.adms.service.AdmsDeviceService;
import com.zkteco.zkbiosecurity.att.cache.AttCacheManager;
import com.zkteco.zkbiosecurity.att.service.AttDeviceService;
import com.zkteco.zkbiosecurity.att.vo.AttDeviceItem;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.device.service.Other2DeviceCenterService;
import com.zkteco.zkbiosecurity.device.vo.DeviceInfo4OtherItem;
import com.zkteco.zkbiosecurity.device.vo.DeviceInfoStatus4OtherItem;

/**
 * 设备中心实现类
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 13:54 2021/9/7
 * @version v1.0
 */
@Service
public class Att2DeviceCenterServiceImpl implements Other2DeviceCenterService {

    @Autowired
    private AttCacheManager attCacheManager;
    @Autowired
    private AttDeviceService attDeviceService;
    @Autowired(required = false)
    private AdmsDeviceService admsDeviceService;

    @Override
    public String getServiceModule() {
        return ConstUtil.SYSTEM_MODULE_ATT;
    }

    @Override
    public List<DeviceInfo4OtherItem> getDeviceFromModule() {
        List<DeviceInfo4OtherItem> deviceInfo4OtherItemList = new ArrayList<>();
        Set<String> keys = attCacheManager.getDeviceInfoKeySet();
        if (keys != null && keys.size() > 0) {
            for (String key : keys) {
                // 初始化标识，跳过
                if (attCacheManager.DEVICE_INFO_INIT_KEY.equals(key)) {
                    continue;
                }
                AttDeviceItem attDeviceItem = attCacheManager.getAttDeviceItemByKey(key);
                DeviceInfo4OtherItem deviceInfo4OtherItem = buildDeviceInfo4OtherItem(attDeviceItem);
                deviceInfo4OtherItemList.add(deviceInfo4OtherItem);
            }
        } else { // redis不存在，查询数据库，并更新缓存设备信息
            List<AttDeviceItem> attDeviceItemList = attDeviceService.getAllItems();
            if (Objects.nonNull(attDeviceItemList) && attDeviceItemList.size() > 0) {
                for (AttDeviceItem attDeviceItem : attDeviceItemList) {
                    // 更新缓存设备信息
                    AttDeviceItem deviceItem = attDeviceService.updateCacheDeviceInfo(attDeviceItem);
                    DeviceInfo4OtherItem deviceInfo4OtherItem = buildDeviceInfo4OtherItem(deviceItem);
                    deviceInfo4OtherItemList.add(deviceInfo4OtherItem);
                }
            } else { // 数据没有数据，保存初始化标识
                AttDeviceItem attDeviceItem = new AttDeviceItem();
                attDeviceItem.setDevSn("init");
                attCacheManager.putDeviceInfo(attDeviceItem);
            }
        }
        return deviceInfo4OtherItemList;
    }

    private DeviceInfo4OtherItem buildDeviceInfo4OtherItem(AttDeviceItem attDeviceItem) {
        DeviceInfo4OtherItem deviceInfo4OtherItem = new DeviceInfo4OtherItem();
        deviceInfo4OtherItem.setDeviceSn(attDeviceItem.getDevSn());
        deviceInfo4OtherItem.setDeviceName(attDeviceItem.getDevName());
        deviceInfo4OtherItem.setDeviceAlias(attDeviceItem.getDevName());
        deviceInfo4OtherItem.setDeviceModelName(attDeviceItem.getDevModel());
        deviceInfo4OtherItem.setDeviceVersion(attDeviceItem.getFwVersion());
        deviceInfo4OtherItem.setDeviceIp(attDeviceItem.getIpAddress());
        deviceInfo4OtherItem.setSourceModule(ConstUtil.SYSTEM_MODULE_ATT);
        deviceInfo4OtherItem.setStatus(admsDeviceService.getDevStatus(attDeviceItem.getDevSn()));
        deviceInfo4OtherItem.setReserve(attDeviceItem.getId());
        // 考勤没有保存设备端口
        // deviceInfo4OtherItem.setDevicePort();
        deviceInfo4OtherItem.setAreaId(attDeviceItem.getAreaId());
        deviceInfo4OtherItem.setAreaName(attDeviceItem.getAuthAreaName());
        return deviceInfo4OtherItem;
    }

    @Override
    public List<DeviceInfoStatus4OtherItem> getDeviceStatusFromModule() {
        List<DeviceInfoStatus4OtherItem> deviceInfoStatus4OtherItemList = new ArrayList<>();
        Set<String> keys = attCacheManager.getDeviceInfoKeySet();
        if (keys != null && keys.size() > 0) {
            for (String key : keys) {
                // 初始化标识，跳过
                if (attCacheManager.DEVICE_INFO_INIT_KEY.equals(key)) {
                    continue;
                }
                AttDeviceItem attDeviceItem = attCacheManager.getAttDeviceItemByKey(key);
                DeviceInfoStatus4OtherItem deviceInfoStatus4OtherItem = buildDeviceInfoStatus4OtherItem(attDeviceItem);
                deviceInfoStatus4OtherItemList.add(deviceInfoStatus4OtherItem);
            }
        } else { // redis不存在，查询数据库，并更新缓存设备信息
            List<AttDeviceItem> attDeviceItemList = attDeviceService.getAllItems();
            if (Objects.nonNull(attDeviceItemList) && attDeviceItemList.size() > 0) {
                for (AttDeviceItem attDeviceItem : attDeviceItemList) {
                    // 更新缓存设备信息
                    AttDeviceItem deviceItem = attDeviceService.updateCacheDeviceInfo(attDeviceItem);
                    DeviceInfoStatus4OtherItem deviceInfoStatus4OtherItem = buildDeviceInfoStatus4OtherItem(deviceItem);
                    deviceInfoStatus4OtherItemList.add(deviceInfoStatus4OtherItem);
                }
            } else { // 数据没有数据，保存初始化标识
                AttDeviceItem attDeviceItem = new AttDeviceItem();
                attDeviceItem.setDevSn("init");
                attCacheManager.putDeviceInfo(attDeviceItem);
            }
        }
        return deviceInfoStatus4OtherItemList;
    }

    private DeviceInfoStatus4OtherItem buildDeviceInfoStatus4OtherItem(AttDeviceItem attDeviceItem) {
        DeviceInfoStatus4OtherItem deviceInfoStatus4OtherItem = new DeviceInfoStatus4OtherItem();
        deviceInfoStatus4OtherItem.setId(attDeviceItem.getId());
        deviceInfoStatus4OtherItem.setStatus(admsDeviceService.getDevStatus(attDeviceItem.getDevSn()));
        return deviceInfoStatus4OtherItem;
    }
}
