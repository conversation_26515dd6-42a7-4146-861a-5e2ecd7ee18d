package com.zkteco.zkbiosecurity.att.service.impl;

import com.zkteco.zkbiosecurity.att.cache.AttCalculationCacheManager;
import com.zkteco.zkbiosecurity.att.service.AttCloudMessageSendService;
import com.zkteco.zkbiosecurity.att.service.AttParamService;
import com.zkteco.zkbiosecurity.att.service.AttRuleService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 考勤规则
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:00
 * @since 1.0.0
 */
@Service
public class AttRuleServiceImpl implements AttRuleService {

    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AttCloudMessageSendService attCloudMessageSendService;
    @Autowired
    private AttCalculationCacheManager attCalculationCacheManager;
    @Autowired
    private AttParamService attParamService;

    @Override
    @Transactional
    public void saveItem(Map<String, String> params) {

        baseSysParamService.saveParams(params);

        // 【实时计算】判断是否启用实时计算保存到缓存
        if (attParamService.realTimeEnable()) {
            attCalculationCacheManager.setRuleParamBean();
        }
    }

    @Override
    public Map<String, String> getAttParams() {
        return baseSysParamService.getParamsByModule("att");
    }

}