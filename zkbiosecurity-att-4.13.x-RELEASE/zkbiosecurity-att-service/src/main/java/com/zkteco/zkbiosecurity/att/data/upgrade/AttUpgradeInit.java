package com.zkteco.zkbiosecurity.att.data.upgrade;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.utils.VersionUtil;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.BaseUpgradeVerHandlerService;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 升级初始化
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 16:00 2020/4/21
 *
 */
@Slf4j
@Component
@Order(1701)
public class AttUpgradeInit implements CommandLineRunner {

    @Autowired
    private BaseUpgradeVerHandlerService baseUpgradeVerHandlerService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public void run(String... strings) throws Exception {
        try {
            upgrade();
        } catch (Exception e) {
            log.error("AttUpgradeInit Exception {}", e);
        }
    }

    void upgrade() {

        // 需要升级的考勤目标版本，取git上面的tag版本
        String curVersion = VersionUtil.getReleaseGitTags(BaseConstants.ATT);

        if (StringUtils.isNotBlank(curVersion)) {
            // 考勤当前版本
            BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("AttUpgradeVersion");
            String curDbVersion = baseSysParamItem.getParamValue();

            if (StringUtils.isBlank(curDbVersion)) {
                curDbVersion = "v2.1.0";
            }

            baseUpgradeVerHandlerService.upgrade(curDbVersion, BaseConstants.ATT);
            if (!curVersion.equals(curDbVersion)) {
                // 升级后更新考勤当前版本为目标版本
                if (StringUtils.isBlank(baseSysParamItem.getId())) {
                    // 没有则初始化版本信息
                    baseSysParamItem =
                        new BaseSysParamItem("AttUpgradeVersion", curVersion, "Att Upgrade Version", true);
                } else {
                    // 存在则更新为新版本
                    baseSysParamItem.setParamValue(curVersion);
                }
                baseSysParamService.saveItem(baseSysParamItem);
            }
        }

    }
}
