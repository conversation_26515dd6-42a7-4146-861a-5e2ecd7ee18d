package com.zkteco.zkbiosecurity.att.remote;

import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

/**
 * 考勤工作流
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 18:09
 * @since 1.0.0
 */
@RequestMapping(value = "/attFlowable.do")
public interface AttFlowableRemote {

    @RequestMapping
    ModelAndView index();

    /**
     * 页面跳转-我的申请
     *
     * @return
     */
    @RequestMapping(params = "myApply")
    ModelAndView myApply();

    /**
     * 页面跳转-待审批任务
     *
     * @return
     */
    @RequestMapping(params = "pendingTask")
    ModelAndView pendingTask();

    /**
     * 页面跳转-已审批任务
     * 
     * @return
     */
    @RequestMapping(params = "approvedTask")
    ModelAndView approvedTask();

    /**
     * 我的申请列表
     * 
     * @param condition
     * @return
     */
    @RequestMapping(params = "myApplyList")
    @ResponseBody
    DxGrid myApplyList(AttProcessInfoItem condition);

    /**
     * 待审批任务列表
     * 
     * @param condition
     * @return
     */
    @RequestMapping(params = "pendingTaskList")
    @ResponseBody
    DxGrid pendingTaskList(AttProcessInfoItem condition);

    /**
     * 已审批任务列表
     * 
     * @param condition
     * @return
     */
    @RequestMapping(params = "approvedTaskList")
    @ResponseBody
    DxGrid approvedTaskList(AttProcessInfoItem condition);

    /**
     * 启动流程-补签
     * 
     * @param attSignItem
     * @return
     */
    @RequestMapping(params = "startSign")
    @ResponseBody
    ZKResultMsg startSign(AttSignItem attSignItem);

    /**
     * 启动流程-请假
     * 
     * @param attLeaveItem
     * @return
     */
    @RequestMapping(params = "startLeave",consumes = "multipart/form-data")
    @ResponseBody
    ZKResultMsg startLeave(AttLeaveItem attLeaveItem, @RequestParam(value = "leaveImage", required = false) MultipartFile[] files);

    /**
     * 启动流程-加班
     * 
     * @param attOvertimeItem
     * @return
     */
    @RequestMapping(params = "startOvertime")
    @ResponseBody
    ZKResultMsg startOvertime(AttOvertimeItem attOvertimeItem);

    /**
     * 启动流程-外出
     * 
     * @param attOutItem
     * @return
     */
    @RequestMapping(params = "startOut",consumes = "multipart/form-data")
    @ResponseBody
    ZKResultMsg startOut(AttOutItem attOutItem, @RequestParam(value = "leaveImage", required = false) MultipartFile[] files);

    /**
     * 启动流程-出差
     * 
     * @param attTripItem
     * @return
     */
    @RequestMapping(params = "startTrip",consumes = "multipart/form-data")
    @ResponseBody
    ZKResultMsg startTrip(AttTripItem attTripItem, @RequestParam(value = "leaveImage", required = false) MultipartFile[] files);

    /**
     * 启动流程-调休
     * 
     * @param attClassItem
     * @return
     */
    @RequestMapping(params = "startClass")
    @ResponseBody
    ZKResultMsg startClass(AttClassItem attClassItem);

    /**
     * 启动流程-调班
     * 
     * @param attAdjustItem
     * @return
     */
    @RequestMapping(params = "startAdjust")
    @ResponseBody
    ZKResultMsg startAdjust(AttAdjustItem attAdjustItem);

    /**
     * 请假单图片上传
     * 
     * <AUTHOR>
     * @since 2019年8月7日 下午3:32:00
     * @param file
     * @return
     */
    @RequestMapping(params = "attachment")
    @ResponseBody
    ZKResultMsg attachment(@RequestParam(value = "leaveImage") MultipartFile file);

    /**
     * 审批动作
     * 
     * <AUTHOR>
     * @since 2019年8月13日 下午1:44:48
     * @param taskId
     * @param pass
     * @param comment
     * @return
     */
    @RequestMapping(params = "complete")
    @ResponseBody
    ZKResultMsg complete(String taskId, String pass, String comment, String notifierPerIds);

    /**
     * 撤销动作
     * 
     * @param businessKey
     * @param revokeReason
     * @return ZKResultMsg
     * <AUTHOR>
     */
    @RequestMapping(params = "cancelApply")
    @ResponseBody
    public ZKResultMsg cancelApply(String businessKey, String revokeReason);

    /**
     * 是否可以打开流程任务菜单
     * 
     * @return
     */
    @RequestMapping(params = "isCanOpenFlowable")
    @ResponseBody
    public ZKResultMsg isCanOpenFlowable();
}