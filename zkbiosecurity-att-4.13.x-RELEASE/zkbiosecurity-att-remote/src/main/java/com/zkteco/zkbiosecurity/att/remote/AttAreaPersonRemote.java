package com.zkteco.zkbiosecurity.att.remote;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttAreaPersonItem;
import com.zkteco.zkbiosecurity.att.vo.AttAuthAreaItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 区域人员
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 15:44
 * @since 1.0.0
 */
@RequestMapping(value = "/attAreaPerson.do")
public interface AttAreaPersonRemote {

    /**
     * 人员区域根据人员获取区域列表
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2019/6/10 10:08
     * @param
     * @return org.springframework.web.servlet.ModelAndView
     */
    @RequestMapping(params = "authAreaList")
    @ResponseBody
    DxGrid authAreaList(AttAuthAreaItem authAreaItem);

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttAreaPersonItem condition);

    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);

    /**
     * 对私短消息页面跳转
     *
     * @param ids:
     * @return org.springframework.web.servlet.ModelAndView
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/3/31 15:43
     * @since 1.0.0
     */
    @RequestMapping(params = "setUserSms")
    ModelAndView setUserSms(String ids);

    /**
     * 区域添加人员
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/3/31 15:43
     * @since 1.0.0
     */
    @RequestMapping(params = "addPerson")
    @ResponseBody
    ZKResultMsg addPerson();

    /**
     * 对私短消息
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:21
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "addSms")
    @ResponseBody
    ZKResultMsg addSms(String ids);

    /**
     * 同步软件数据到设备
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:22
     * @param areaId
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "syncPerToDev")
    @ResponseBody
    ZKResultMsg syncPerToDev(String areaId, String ids);

    /**
     * 导出区域人员数据
     *
     * @param request:
     * @param response:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/3/31 15:43
     * @since 1.0.0
     */
    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response);

    /**
     * 下载导出区域人员模板
     * 
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/8/4 17:22
     * @param request
     * @param response
     * @return void
     */
    @RequestMapping(params = "importTemplate")
    void importTemplate(HttpServletRequest request, HttpServletResponse response);

    /**
     * 导入区域人员
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/4/20
     * @param upload
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    @RequestMapping(params = "import")
    @ResponseBody
    ZKResultMsg importExcel(@RequestParam("upload") MultipartFile upload) throws IOException;

    /**
     * 导入批量删除
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/4/20
     * @param upload
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    @RequestMapping(params = "importBatchDel")
    @ResponseBody
    ZKResultMsg importBatchDel(@RequestParam("upload") MultipartFile upload) throws IOException;
}