package com.zkteco.zkbiosecurity.att.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttMonthDetailReportItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;

import java.util.Map;

/**
 * 考勤月报表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:22
 * @since 1.0.0
 */
@RequestMapping(value = "/attMonthDetailReport.do")
public interface AttMonthDetailReportRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttMonthDetailReportItem condition);

    @RequestMapping(params = "export")
    void export();

    @RequestMapping(params = "indexMonthWorkTimeReport")
    ModelAndView indexMonthWorkTimeReport();

    @RequestMapping(params = "listMonthWorkTimeReport")
    @ResponseBody
    DxGrid listMonthWorkTimeReport(AttMonthDetailReportItem condition);

    @RequestMapping(params = "exportMonthWorkTimeReport")
    void exportMonthWorkTimeReport();

    @RequestMapping(params = "indexMonthCardReport")
    ModelAndView indexMonthCardReport();

    @RequestMapping(params = "listMonthCardReport")
    @ResponseBody
    DxGrid listMonthCardReport(AttMonthDetailReportItem condition);

    @RequestMapping(params = "exportMonthCardReport")
    void exportMonthCardReport();

    @RequestMapping(params = "indexMonthOvertimeReport")
    ModelAndView indexMonthOvertimeReport();

    @RequestMapping(params = "listMonthOvertimeReport")
    @ResponseBody
    DxGrid listMonthOvertimeReport(AttMonthDetailReportItem condition);

    @RequestMapping(params = "exportMonthOvertimeReport")
    void exportMonthOvertimeReport();

    @RequestMapping(params = "getAttSymbolsJson")
    @ResponseBody
    Map<String, String> getAttSymbolsJson();
}
