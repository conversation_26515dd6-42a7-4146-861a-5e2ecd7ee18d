package com.zkteco.zkbiosecurity.att.remote;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttDeviceOpLogItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;

/**
 * 设备操作日志
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:26
 * @since 1.0.0
 */
@RequestMapping(value = "/attDeviceOpLog.do")
public interface AttDeviceOpLogRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttDeviceOpLogItem codition);

    @RequestMapping(params = "export")
    @ResponseBody
    public void export(HttpServletRequest req, HttpServletResponse resp);
}