package com.zkteco.zkbiosecurity.att.remote;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.zkteco.zkbiosecurity.att.vo.AttAnnualLeaveReportItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.bean.DxGrid;

import java.util.Date;

/**
 * 年假结余表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/8/17 9:45
 */
@RequestMapping(value = "/attAnnualLeaveReport.do")
public interface AttAnnualLeaveReportRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttAnnualLeaveReportItem condition);

    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response);

    /**
     * 年假详情
     *
     * @param personId:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/10/10 14:20
     * @since 1.0.0
     */
    @RequestMapping(params = "getDetail")
    @ResponseBody
    ZKResultMsg getDetail(String personId);

    /**
     * 重新计算年假
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/1/3 11:15
     * @since 1.0.0
     */
    @RequestMapping(params = "recalculate")
    @ResponseBody
    ZKResultMsg recalculate(@RequestParam(value = "ids") String ids, @RequestParam(value = "type") String type);

    /**
     * 调整年假
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/1/3 11:15
     * @since 1.0.0
     */
    @RequestMapping(params = "adjustView")
    ModelAndView adjustView(@RequestParam(value = "ids") String ids, @RequestParam(value = "type") String type);

    /**
     * 调整年假
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/1/3 11:15
     * @since 1.0.0
     */
    @RequestMapping(params = "adjust")
    @ResponseBody
    ZKResultMsg adjust(@RequestParam(value = "ids") String ids, @RequestParam(value = "adjustDays") Integer adjustDays, @RequestParam(value = "type") String type);
}
