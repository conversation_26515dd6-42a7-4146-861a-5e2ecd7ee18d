/**
 * File Name: AttLeaveType Created by GenerationTools on 2018-02-23 下午03:52 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.att.remote;

import com.zkteco.zkbiosecurity.att.vo.AttBreakTimeItem;
import com.zkteco.zkbiosecurity.att.vo.AttBreakTimeSelectItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/**
 * 休息时间段
 *
 * <AUTHOR>
 * @date 2019/5/30
 */
@RequestMapping(value = "/attBreakTime.do")
public interface AttBreakTimeRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AttBreakTimeItem item);

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttBreakTimeItem condition);

    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);

    /**
     * 选择休息时间段
     * 
     * @param condition
     * @return
     */
    @RequestMapping(params = "selectList")
    @ResponseBody
    DxGrid selectList(AttBreakTimeSelectItem condition);

    /**
     * 判断名称是否有效
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/7/31 15:17
     * @param name
     * @return java.lang.String
     */
    @ResponseBody
    @RequestMapping(params = "validName")
    String validName(@RequestParam(value = "name") String name);

    /**
     * 判断时间是否重复
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/7/31 15:59
     * @param id
     * @param startTime
     * @param endTime
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @ResponseBody
    @RequestMapping(params = "isExistBreakTime")
    ZKResultMsg isExistBreakTime(@RequestParam(value = "id", required = false) String id,
        @RequestParam(value = "startTime") String startTime, @RequestParam(value = "endTime") String endTime);
}