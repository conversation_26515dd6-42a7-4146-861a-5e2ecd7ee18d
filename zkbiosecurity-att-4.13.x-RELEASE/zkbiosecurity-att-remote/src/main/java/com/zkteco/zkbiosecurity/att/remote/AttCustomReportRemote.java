package com.zkteco.zkbiosecurity.att.remote;


import com.zkteco.zkbiosecurity.att.vo.AttCustomReportItem;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 考勤自定义报表
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 10:25 2023/7/18
 * @version v1.0
 */
@RequestMapping(value = "/attCustomReport.do")
public interface AttCustomReportRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AttCustomReportItem item);

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttCustomReportItem condition);

    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);


    @RequestMapping(params = "getCustomReportField")
    @ResponseBody
    ZKResultMsg getCustomReportField(@RequestParam(value = "id", required = false) String id, @RequestParam(value = "type", required = false) String type);

    @RequestMapping(params = "toCustomReport")
    ModelAndView toCustomReport(String id);
}