package com.zkteco.zkbiosecurity.att.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttMonthStatisticalReportItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;

/**
 * 人员汇总表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:25
 * @since 1.0.0
 */
@RequestMapping(value = "/attMonthStatisticalReport.do")
public interface AttMonthStatisticalReportRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttMonthStatisticalReportItem condition);

    @RequestMapping(params = "export")
    void export();

    @RequestMapping(params = "indexOvertimeSummaryReport")
    ModelAndView indexOvertimeSummaryReport();

    @RequestMapping(params = "listOvertimeSummaryReport")
    @ResponseBody
    DxGrid listOvertimeSummaryReport(AttMonthStatisticalReportItem condition);

    @RequestMapping(params = "exportOvertimeSummaryReport")
    void exportOvertimeSummaryReport();

    @RequestMapping(params = "indexLeaveSummaryReport")
    ModelAndView indexLeaveSummaryReport();

    @RequestMapping(params = "listLeaveSummaryReport")
    @ResponseBody
    DxGrid listLeaveSummaryReport(AttMonthStatisticalReportItem condition);

    @RequestMapping(params = "exportLeaveSummaryReport")
    void exportLeaveSummaryReport();
}
