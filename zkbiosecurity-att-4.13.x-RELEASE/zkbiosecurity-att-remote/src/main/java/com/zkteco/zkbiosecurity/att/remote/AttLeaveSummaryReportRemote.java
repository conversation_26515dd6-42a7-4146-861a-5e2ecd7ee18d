package com.zkteco.zkbiosecurity.att.remote;

import com.zkteco.zkbiosecurity.att.vo.AttLeaveSummaryReportItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

/**
 * 请假汇总表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/8/18 11:42
 */
@RequestMapping(value = "/attLeaveSummaryReport.do")
public interface AttLeaveSummaryReportRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttLeaveSummaryReportItem condition);

    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response);

    @RequestMapping(params = "getLeaveDetail")
    @ResponseBody
    ZKResultMsg getLeaveDetail(String personPin, String leaveTypeId, Date startTime, Date endTime);
}
