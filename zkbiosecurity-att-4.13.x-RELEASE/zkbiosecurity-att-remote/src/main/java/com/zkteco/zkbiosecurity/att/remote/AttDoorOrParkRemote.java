package com.zkteco.zkbiosecurity.att.remote;

import com.zkteco.zkbiosecurity.att.vo.*;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 考勤点添加门或停车设备
 *
 * @author: verber
 * @date: 2018-07-02 下午02:15
 */
@RequestMapping(value = "/attDoorOrPark.do")
public interface AttDoorOrParkRemote {

    /**
     * 考勤点：添加门控件
     * 
     * @param condition
     * @return
     */
    @RequestMapping(params = "selectDoorList")
    @ResponseBody
    DxGrid selectDoorList(AttSelectDoorItem condition);

    /**
     * 考勤点：添加停车设备控件
     * 
     * @param condition
     * @return
     */
    @RequestMapping(params = "selectParkDeviceList")
    @ResponseBody
    DxGrid selectParkDeviceList(AttParkDeviceSelectItem condition);

    /**
     * 考勤点：添加大屏机设备控件
     * 
     * @param condition
     * @return
     */
    @RequestMapping(params = "selectInsDeviceList")
    @ResponseBody
    DxGrid selectInsDeviceList(AttInsDeviceSelectItem condition);

    /**
     * 考勤点：添加人证设备控件
     * 
     * @param condition
     * @return
     */
    @RequestMapping(params = "selectPidDeviceList")
    @ResponseBody
    DxGrid selectPidDeviceList(AttPidDeviceSelectItem condition);

    /**
     * 考勤点：添加VMS设备控件
     * 
     * @param condition
     * @return
     */
    @RequestMapping(params = "selectVmsDeviceList")
    @ResponseBody
    DxGrid selectVmsDeviceList(AttVmsDeviceSelectItem condition);

    /**
     * 考勤点：添加ivs设备通道控件
     *
     * @param condition
     * @return
     */
    @RequestMapping(params = "selectIvsDeviceList")
    @ResponseBody
    DxGrid selectIvsDeviceList(AttIvsDeviceSelectItem condition);

    /**
     * 视频模块主设备树
     *
     * @return
     * <AUTHOR>
     * @date 2021-02-04 16:54
     * @since 1.0.0
     */
    @RequestMapping(params = "ivsParentDeviceTree")
    @ResponseBody
    TreeItem ivsParentDeviceTree();

    /**
     * 考勤点：添加PSG设备控件
     * 
     * @param condition
     * @return
     */
    @RequestMapping(params = "selectPsgDeviceList")
    @ResponseBody
    DxGrid selectPsgGateList(AttPsgDeviceSelectItem condition);

}