package com.zkteco.zkbiosecurity.att.remote;

import java.io.IOException;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttTransactionItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 考勤原始记录、打卡记录
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/11/23 11:10
 * @since 1.0.0
 */
@RequestMapping(value = "/attTransaction.do")
public interface AttTransactionRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AttTransactionItem item);

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttTransactionItem condition);

    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);

    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response);

    /**
     * 获取考勤照片
     *
     * @author: verber
     * @date: 2018-07-02 下午03:52
     * @param pin
     * @param sn
     * @param attDate
     * @return
     */
    @RequestMapping(params = "getPhoto")
    @ResponseBody
    ZKResultMsg getPhoto(@RequestParam(value = "pin") String pin, @RequestParam(value = "sn") String sn,
        @RequestParam(value = "attDate") String attDate);

    /**
     * 从U盘导入记录
     * 
     * <AUTHOR>
     * @version 2019年6月10日 下午2:26:53
     * @return
     */
    @RequestMapping(params = "importUSBRecord")
    @ResponseBody
    ZKResultMsg importUSBRecord(@RequestParam("upload") MultipartFile upload) throws IOException;

    /**
     * 选择的导入文件名称校验
     * 
     * <AUTHOR>
     * @version 2019年6月11日 上午10:42:12
     * @param fileName
     * @return
     */
    @RequestMapping(params = "validFileName")
    @ResponseBody
    Boolean isValidFileName(@RequestParam("fileName") String fileName);

    /**
     * 导出考勤照片
     * 
     * @throws Exception
     */
    @RequestMapping(params = "exportAttPhoto")
    void exportAttPhoto() throws Exception;

    /**
     * 手动同步第三方考勤记录
     * 
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/10/21 11:13
     * @return void
     */
    @RequestMapping(params = "syncAttRecord")
    @ResponseBody
    ZKResultMsg syncAttRecord(String attPointIds, Date startDatetime, Date endDatetime);

    /**
     * 获取加密文件图片
     * 
     * @param path:
     * @return void
     * <AUTHOR>
     * @date 2021-05-27 9:57
     * @since 1.0.0
     */
    @RequestMapping(params = "getDecryptFileBase64")
    @ResponseBody
    void getDecryptFileBase64(String path);

    /**
     * 查看详情
     *
     * @return com.zkteco.zkbiosecurity.base.bean.DxGrid
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/5/9 18:05
     * @since 1.0.0
     */
    @RequestMapping(params = "getDayCardDetailView")
    @ResponseBody
    DxGrid getDayCardDetailView(String attDayCardDetailId, String attDate);
}