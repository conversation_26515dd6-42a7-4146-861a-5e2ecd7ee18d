/*
 * File Name: AttManualCalculationRemote.java Created by y<PERSON><PERSON>.zou on 2018年3月19日 上午10:06:29. Copyright:Copyright ?
 * 1985-2017 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.att.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttManualCalculationItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 考勤计算
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:16
 * @since 1.0.0
 */
@RequestMapping(value = "/attManualCalculation.do")
public interface AttManualCalculationRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttManualCalculationItem condition);

    @RequestMapping(params = "calculating")
    @ResponseBody
    ZKResultMsg calculating();

    @RequestMapping(params = "calculate")
    @ResponseBody
    void calculate(@RequestParam(value = "personIds") String personIds, @RequestParam(value = "deptIds", required = false) String deptIds,
        @RequestParam(value = "startDateStr") String startDateStr, @RequestParam(value = "endDateStr") String endDateStr,
        @RequestParam(value = "pins", required = false) String pins,
        @RequestParam(value = "totalLeaver") String totalLeaver,
        @RequestParam(value = "isIncludeLower") String isIncludeLower);

    /**
     *
     * <AUTHOR> href="mailto:<EMAIL>">jinjie.you</a>
     * @date 2018/8/9 14:33
     * @param deptIds
     *            部门id串，多个id由“,”分隔
     * @return 部门下的所有人员pin的拼接字符串
     */
    @RequestMapping(params = "getDeptPins")
    @ResponseBody
    String getDeptPins(String deptIds);

}
