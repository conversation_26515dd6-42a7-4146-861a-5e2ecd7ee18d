package com.zkteco.zkbiosecurity.att.remote;

import com.zkteco.zkbiosecurity.att.vo.AttDayCardDetailItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 日打卡详情
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 18:27 2020/6/28
 */
@RequestMapping(value = "/attDayCardDetailReport.do")
public interface AttDayCardDetailReportRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttDayCardDetailItem condition);

    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response);

}
