package com.zkteco.zkbiosecurity.att.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttRealTimeCallRollItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 实时点名
 * 
 * <AUTHOR>
 * @date 2020年4月16日 下午5:59:03
 * @version V1.0
 */
@RequestMapping(value = "/attRealTimeCallRoll.do")
public interface AttRealTimeCallRollRemote {

    @RequestMapping
    ModelAndView index();

    /**
     * 实时点名：已签到/未签到 数据获取
     * 
     * <AUTHOR>
     * @since 2020年4月17日上午9:27:35
     * @param condition
     * @return
     */
    @RequestMapping(params = "getAttRealTimeCallRollList")
    @ResponseBody
    ZKResultMsg getAttRealTimeCallRollList(AttRealTimeCallRollItem condition);

}
