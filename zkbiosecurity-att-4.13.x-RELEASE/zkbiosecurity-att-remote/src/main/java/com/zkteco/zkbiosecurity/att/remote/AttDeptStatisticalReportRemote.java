package com.zkteco.zkbiosecurity.att.remote;

import com.zkteco.zkbiosecurity.att.vo.AttDeptStatisticalReportItem;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.bean.DxGrid;

/**
 * 部门汇总表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:24
 * @since 1.0.0
 */
@RequestMapping(value = "/attDeptStatisticalReport.do")
public interface AttDeptStatisticalReportRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttDeptStatisticalReportItem condition);

    @RequestMapping(params = "export")
    void export();

    @RequestMapping(params = "indexDeptOvertimeSummaryReport")
    ModelAndView indexDeptOvertimeSummaryReport();

    @RequestMapping(params = "listDeptOvertimeSummaryReport")
    @ResponseBody
    DxGrid listDeptOvertimeSummaryReport(AttDeptStatisticalReportItem condition);

    @RequestMapping(params = "exportDeptOvertimeSummaryReport")
    void exportDeptOvertimeSummaryReport();

    @RequestMapping(params = "indexDeptLeaveSummaryReport")
    ModelAndView indexDeptLeaveSummaryReport();

    @RequestMapping(params = "listDeptLeaveSummaryReport")
    @ResponseBody
    DxGrid listDeptLeaveSummaryReport(AttDeptStatisticalReportItem condition);

    @RequestMapping(params = "exportDeptLeaveSummaryReport")
    void exportDeptLeaveSummaryReport();
}
