package com.zkteco.zkbiosecurity.att.remote;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;

/**
 * 考勤规则
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:34
 * @since 1.0.0
 */
@RequestMapping(value = "/attParam.do")
public interface AttParamRemote {

    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(@RequestParam Map<String, String> params);

}