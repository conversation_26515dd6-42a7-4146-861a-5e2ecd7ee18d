package com.zkteco.zkbiosecurity.att.remote;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.zkteco.zkbiosecurity.att.vo.AttEsdcChannelSelectItem;
import com.zkteco.zkbiosecurity.att.vo.AttPointItem;
import com.zkteco.zkbiosecurity.att.vo.AttPointSelectItem;
import com.zkteco.zkbiosecurity.att.vo.AttSelectPointItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/**
 * 考勤点
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:50
 * @since 1.0.0
 */
@RequestMapping(value = "/attPoint.do")
public interface AttPointRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AttPointItem item);

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttPointItem condition);

    /**
     * 考勤点选择控件列表数据获取
     *
     * @author: verber
     * @date: 2018-07-18 下午03:52
     * @param condition
     * @return
     */
    @RequestMapping(params = "selectAttPointList")
    @ResponseBody
    DxGrid selectAttPointList(AttPointSelectItem condition);

    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);

    /**
     * 判断名称是否存在
     *
     * @author: verber
     * @date: 2018-07-04 下午03:52
     * @param pointName
     * @return
     */
    @RequestMapping(params = "isExist")
    @ResponseBody
    boolean isExist(@RequestParam(value = "pointName") String pointName);

    /**
     * 判断授权点数是否超过
     *
     * @author: verber
     * @date: 2018-07-05 下午03:52
     * @param deviceModule
     * @return
     */
    @RequestMapping(params = "checkLicenseCount")
    @ResponseBody
    ZKResultMsg checkLicenseCount(@RequestParam(value = "deviceModule") String deviceModule);

    /**
     * 获取停车场进出口区域
     *
     * @author: verber
     * @date: 2018-07-06 11:52
     * @return
     */
    @RequestMapping(params = "getAllParkEntranceArea")
    @ResponseBody
    TreeItem getAllParkEntranceArea();

    /**
     * 批量导出
     * 
     * @param req
     * @param resp
     */
    @RequestMapping(params = "export")
    @ResponseBody
    public void export(HttpServletRequest req, HttpServletResponse resp);

    /**
     * 获取存在考勤点的模块，返回拉下组件格式列表
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/7/16 15:38
     * @since 1.0.0
     */
    @RequestMapping(params = "getPointModule")
    @ResponseBody
    ZKResultMsg getPointModule();

    /**
     * 取esdc通道双列表数据
     *
     * @param condition:
     * @return com.zkteco.zkbiosecurity.base.bean.DxGrid
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/8/5 14:53
     * @since 1.0.0
     */
    @RequestMapping(params = "esdcChannelList")
    @ResponseBody
    DxGrid esdcChannelList(AttEsdcChannelSelectItem condition);
}