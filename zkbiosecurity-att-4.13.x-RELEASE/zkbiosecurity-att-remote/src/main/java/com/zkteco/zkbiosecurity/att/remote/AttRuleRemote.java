package com.zkteco.zkbiosecurity.att.remote;

import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;

/**
 * 考勤规则
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:00
 * @since 1.0.0
 */
@RequestMapping(value = "/attRule.do")
public interface AttRuleRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(@RequestParam Map<String, String> params, AttLeaveTypeItem attLeaveTypeItem);

    @RequestMapping(params = "getParams")
    @ResponseBody
    ZKResultMsg getParams();


    /**
     * 获取考勤状态下发
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/4/26 10:42
     * @since 1.0.0
     */
    @RequestMapping(params = "getCardStatus")
    @ResponseBody
    ZKResultMsg getCardStatus();
}