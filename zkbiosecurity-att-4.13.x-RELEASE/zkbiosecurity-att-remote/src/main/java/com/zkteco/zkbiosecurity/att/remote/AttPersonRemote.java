package com.zkteco.zkbiosecurity.att.remote;

import com.zkteco.zkbiosecurity.att.vo.AttPersonVerifyModeItem;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttPersonAreaItem;
import com.zkteco.zkbiosecurity.att.vo.AttPersonSelectItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 考勤人员
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 16:56
 * @since 1.0.0
 */
@RequestMapping(value = "/attPerson.do")
public interface AttPersonRemote {

    /**
     * 人事=>人员编辑页面，嵌入的考勤页面
     * 
     * @author: GenerationTools
     * @date: 2018-02-23 下午03:52
     * @param personId
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "personId", required = false) String personId);

    /**
     * 人员区域列表数据获取
     *
     * @author: GenerationTools
     * @date: 2018-02-23 下午03:52
     * @param condition
     * @return
     */
    @RequestMapping(params = "attPersonList")
    @ResponseBody
    DxGrid attPersonList(AttPersonAreaItem condition);

    /**
     * 选人控件列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param condition
     * @return
     */
    @RequestMapping(params = "selectList")
    @ResponseBody
    DxGrid selectList(AttPersonSelectItem condition);

    /**
     * 区域添加人选人控件列表数据获取(从PersPerson获取人员)
     *
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param condition
     * @return
     */
    @RequestMapping(params = "attAreaSelectPerson")
    @ResponseBody
    DxGrid attAreaSelectPerson(AttPersonSelectItem condition);

    /**
     * 根据人员ID获取人员信息
     * <p>
     * 调班界面用到
     * </p>
     * 
     * @author: GenerationTools
     * @date: 2018-02-23 下午03:52
     * @param ids
     * @return
     */
    @RequestMapping(params = "getPersonInfo")
    @ResponseBody
    ZKResultMsg getPersonInfo(String ids);

    /**
     * 人员验证方式界面跳转
     * 
     * @return org.springframework.web.servlet.ModelAndView
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/1/6 17:19
     * @since 1.0.0
     */
    @RequestMapping(params = "verifyModeIndex")
    ModelAndView verifyModeIndex();

    /**
     * 获取人员验证方式列表数据
     * 
     * @param condition: 
     * @return com.zkteco.zkbiosecurity.base.bean.DxGrid
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/1/6 17:19
     * @since 1.0.0
     */
    @RequestMapping(params = "verifyModeList")
    @ResponseBody
    DxGrid verifyModeList(AttPersonVerifyModeItem condition);

    /**
     * 验证方式设置界面跳转
     *
     * @param ids:
     * @return org.springframework.web.servlet.ModelAndView
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/1/6 17:19
     * @since 1.0.0
     */
    @RequestMapping(params = "editVerifyMode")
    ModelAndView editVerifyMode(@RequestParam(value = "ids", required = false) String ids);

    /**
     * 保存考勤人员验证方式
     *
     * @param attPersonVerifyModeItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/1/6 17:20
     * @since 1.0.0
     */
    @RequestMapping(params = "saveVerifyMode")
    @ResponseBody
    ZKResultMsg saveVerifyMode(AttPersonVerifyModeItem attPersonVerifyModeItem);
}