package com.zkteco.zkbiosecurity.att.remote;

import java.io.IOException;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttTempSchItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 临时排班（人员、部门、分组）
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:19
 * @since 1.0.0
 */
@RequestMapping(value = "/attTempSch.do")
public interface AttTempSchRemote {

    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttTempSchItem condition);

    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);

    /**
     * 保存临时排班
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/5/7 14:23
     * @param item
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "saveTempSch")
    @ResponseBody
    ZKResultMsg saveTempSch(AttTempSchItem item);

    /**
     * 清除临时排班
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/4/26 14:02
     * @param type
     *            分组0/部门1/人员2
     * @param ids
     *            分组ID/部门ID/人员ID
     * @param startDate
     *            搜索开始时间
     * @param endDate
     *            搜索结束时间
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "delTempSch")
    @ResponseBody
    ZKResultMsg delTempSch(@RequestParam(value = "ids") String ids, @RequestParam(value = "type") Short type,
        @RequestParam(value = "startDate") Date startDate, @RequestParam(value = "endDate") Date endDate);

    /**
     * 导出临时排班模版
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/4/26 14:11
     * @return void
     */
    @RequestMapping(params = "exportTemplate")
    void exportTemplate(HttpServletRequest request, HttpServletResponse response);

    /**
     * 导入临时排班
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/4/26 14:17
     * @param upload
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "import")
    @ResponseBody
    ZKResultMsg importExcel(@RequestParam("upload") MultipartFile upload) throws IOException;

    /**
     * 导出临时排班模版
     *
     * @param request:
     * @param response:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/1/18 17:48
     * @since 1.0.0
     */
    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response);
}