package com.zkteco.zkbiosecurity.att.remote;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.zkteco.zkbiosecurity.att.vo.AttShortcutKeyInfoItem;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttDeviceItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/attDevice.do")
public interface AttDeviceRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    @RequestMapping(params = "auth")
    @ResponseBody
    ZKResultMsg auth(AttDeviceItem item);

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttDeviceItem condition);

    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);

    /**
     * 搜索设备
     * 
     * <AUTHOR>
     * @since 2018/3/22 9:01
     * @return
     */
    @RequestMapping(params = "searchDev")
    @ResponseBody
    ZKResultMsg searchDev(@RequestParam(value = "nowTime") Long nowTime);

    /**
     * 获取所有设备Ip和sn
     * 
     * <AUTHOR>
     * @since 2018/3/22 9:36
     * @return
     */
    @RequestMapping(params = "getAllIPSn")
    @ResponseBody
    ZKResultMsg getAllIPSn();

    /**
     * 查看设备参数
     * 
     * @author: verber
     * @date: 2018-05-18 11:53:23
     * @param devId
     * @return
     */
    @RequestMapping(params = "queryDeviceOption")
    @ResponseBody
    ZKResultMsg queryDeviceOption(@RequestParam(value = "devId") String devId);

    /**
     * 启用
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:24
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "enable")
    @ResponseBody
    ZKResultMsg enable(@RequestParam(value = "ids") String ids);

    /**
     * 禁用
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:26
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "disable")
    @ResponseBody
    ZKResultMsg disable(@RequestParam(value = "ids") String ids);

    /**
     * 同步软件数据到设备
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 1 11:27
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "syncDev")
    @ResponseBody
    ZKResultMsg syncDev(@RequestParam(value = "ids") String ids, @RequestParam(value = "isSyncClearData", required = false) String isSyncClearData);

    /**
     * 考勤数据校对页面跳转
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:28
     * @param ids
     * @return org.springframework.web.servlet.ModelAndView
     */
    @RequestMapping(params = "setTime")
    ModelAndView setTime(@RequestParam(value = "ids") String ids);

    /**
     * 考勤数据校对
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:34
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "verify")
    @ResponseBody
    ZKResultMsg verify(@RequestParam(value = "ids") String ids);

    /**
     * 获取指定人员数据页面跳转
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:34
     * @param ids
     * @return org.springframework.web.servlet.ModelAndView
     */
    @RequestMapping(params = "fillPin")
    ModelAndView fillPin(@RequestParam(value = "ids") String ids);

    /**
     * 获取指定人员数据
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:35
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "getPersonInfo")
    @ResponseBody
    ZKResultMsg getPersonInfo(@RequestParam(value = "ids") String ids);

    /**
     * 重新上传数据页面跳转
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:35
     * @param ids
     * @return org.springframework.web.servlet.ModelAndView
     */
    @RequestMapping(params = "setStamp")
    ModelAndView setStamp(@RequestParam(value = "ids") String ids);

    /**
     * 重新上传数据
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:35
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "reUpload")
    @ResponseBody
    ZKResultMsg reUpload(@RequestParam(value = "ids") String ids);

    /**
     * 清除设备命令
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:35
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "deleteCmd")
    @ResponseBody
    ZKResultMsg deleteCmd(@RequestParam(value = "ids") String ids);

    /**
     * 对公短消息页面跳转
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:35
     * @param ids
     * @return org.springframework.web.servlet.ModelAndView
     */
    @RequestMapping(params = "setSms")
    ModelAndView setSms(@RequestParam(value = "ids") String ids);

    /**
     * 对公短消息
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:35
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "addSms")
    @ResponseBody
    ZKResultMsg addSms(@RequestParam(value = "ids") String ids);

    /**
     * 清除考勤照片
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:35
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "clearAttPic")
    @ResponseBody
    ZKResultMsg clearAttPic(@RequestParam(value = "ids") String ids);

    /**
     * 清除考勤记录
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:35
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "clearAttLog")
    @ResponseBody
    ZKResultMsg clearAttLog(@RequestParam(value = "ids") String ids);

    /**
     * 清除设备人员
     * 
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/8/7 15:58
     * @param ids
     * @return
     */
    @RequestMapping(params = "clearAttPers")
    @ResponseBody
    ZKResultMsg clearAttPers(@RequestParam(value = "ids") String ids);

    /**
     * 重启设备
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:35
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "reboot")
    @ResponseBody
    ZKResultMsg reboot(@RequestParam(value = "ids") String ids);

    /**
     * 获取设备参数
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:35
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "getDevOpt")
    @ResponseBody
    ZKResultMsg getDevOpt(@RequestParam(value = "ids") String ids);

    /**
     * 设备名称验证
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:36
     * @param
     * @return java.lang.String
     */
    @RequestMapping(params = "devNameVaild")
    @ResponseBody
    String devNameVaild();

    /**
     * 设备序列号验证
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:36
     * @param
     * @return java.lang.String
     */
    @RequestMapping(params = "devSnVaild")
    @ResponseBody
    String devSnVaild();

    /**
     * 设备Ip验证
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:36
     * @param
     * @return java.lang.String
     */
    @RequestMapping(params = "devIpVaild")
    @ResponseBody
    String devIpVaild();

    /**
     * 设备Ip验证
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/5 11:36
     * @param
     * @return java.lang.String
     */
    @RequestMapping(params = "checkDeviceStatus")
    @ResponseBody
    ZKResultMsg checkDeviceStatus(@RequestParam(value = "ids") String ids);

    /**
     * 验证设备是否是登记机
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/10/31 15:33
     * @param ids
     *            只能上传一台设备
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "checkExitIsRegDevice")
    @ResponseBody
    ZKResultMsg checkExitIsRegDevice(@RequestParam(value = "ids") String ids);

    /**
     * 获取搜索设备信息
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年11月30日 上午9:36:04
     * @return
     */
    @RequestMapping(params = "getSearchDevInfo")
    @ResponseBody
    ModelAndView getSearchDevInfo();

    /**
     * 设备授权区域
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/6/5 9:44
     * @param authAreaId
     * @param deviceIds
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    @RequestMapping(params = "authArea")
    @ResponseBody
    ZKResultMsg authArea(@RequestParam(value = "authAreaId") String authAreaId,
        @RequestParam(value = "deviceIds") String deviceIds);

    /**
     * 导出设备
     *
     * @param request
     * @param response
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/6/2 16:00
     * @return: void
     **/
    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response);

    /**
     * 升级固件页面跳转
     * 
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/6/30 15:28
     * 
     * @return
     */
    @RequestMapping(params = "getDevInfoWithUpgradeFirmware")
    @ResponseBody
    ModelAndView getDevInfoWithUpgradeFirmware(@RequestParam(value = "ids") String ids);

    /**
     * 设备升级固件
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/7/2 10:18
     * @param devIds
     * @param devFile
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    @RequestMapping(params = "upgradeFirmware")
    @ResponseBody
    ZKResultMsg upgradeFirmware(@RequestParam(value = "devIds") String devIds,
        @RequestParam(value = "devFile") MultipartFile devFile);

    /**
     * 下拉选择查询
     *
     * @return
     */
    @RequestMapping(params = "tree")
    @ResponseBody
    TreeItem tree();


    /**
     * 设置考勤状态键，界面跳转
     *
     * @param ids:
     * @return org.springframework.web.servlet.ModelAndView
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/4/25 19:48
     * @since 1.0.0
     */
    @RequestMapping(params = "opAttSetShortcutKey")
    ModelAndView opAttSetShortcutKey(String ids);

    /**
     * 设置考勤状态键
     *
     * @param attShortcutKeyInfoItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/4/25 20:01
     * @since 1.0.0
     */
    @RequestMapping(params = "setShortcutKey")
    @ResponseBody
    ZKResultMsg setShortcutKey(AttShortcutKeyInfoItem attShortcutKeyInfoItem);

    /**
     * 方法描述
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/4/26 10:42
     * @since 1.0.0
     */
    @RequestMapping(params = "getShortcutKeyName")
    @ResponseBody
    ZKResultMsg getShortcutKeyName();
}