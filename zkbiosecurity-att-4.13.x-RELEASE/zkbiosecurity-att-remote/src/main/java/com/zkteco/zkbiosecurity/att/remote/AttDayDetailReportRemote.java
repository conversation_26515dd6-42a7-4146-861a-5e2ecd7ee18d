package com.zkteco.zkbiosecurity.att.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttDayDetailReportItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;

/**
 * 考勤日报表
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:22
 * @since 1.0.0
 */
@RequestMapping(value = "/attDayDetailReport.do")
public interface AttDayDetailReportRemote {

    /** 日明细表 */
    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttDayDetailReportItem condition);

    @RequestMapping(params = "export")
    void export();

    /** 工作时长表 */
    @RequestMapping(params = "indexWorkTimeReport")
    ModelAndView indexWorkTimeReport();

    @RequestMapping(params = "listWorkTimeReport")
    @ResponseBody
    DxGrid listWorkTimeReport(AttDayDetailReportItem condition);

    @RequestMapping(params = "exportWorkTimeReport")
    void exportWorkTimeReport();

    /** 加班报表 */
    @RequestMapping(params = "indexOvertimeReport")
    ModelAndView indexOvertimeReport();

    @RequestMapping(params = "listOvertimeReport")
    @ResponseBody
    DxGrid listOvertimeReport(AttDayDetailReportItem condition);

    @RequestMapping(params = "exportOvertimeReport")
    void exportOvertimeReport();

    /** 请假报表 */
    @RequestMapping(params = "indexLeaveReport")
    ModelAndView indexLeaveReport();

    @RequestMapping(params = "listLeaveReport")
    @ResponseBody
    DxGrid listLeaveReport(AttDayDetailReportItem condition);

    @RequestMapping(params = "exportLeaveReport")
    void exportLeaveReport();

    /** 出勤异常表 */
    @RequestMapping(params = "indexAbnormalReport")
    ModelAndView indexAbnormalReport();

    @RequestMapping(params = "listAbnormalReport")
    @ResponseBody
    DxGrid listAbnormalReport(AttDayDetailReportItem condition);

    @RequestMapping(params = "exportAbnormalReport")
    void exportAbnormalReport();

    /** 迟到报表 */
    @RequestMapping(params = "indexLateReport")
    ModelAndView indexLateReport();

    @RequestMapping(params = "listLateReport")
    @ResponseBody
    DxGrid listLateReport(AttDayDetailReportItem condition);

    @RequestMapping(params = "exportLateReport")
    void exportLateReport();

    /** 早退报表 */
    @RequestMapping(params = "indexEarlyReport")
    ModelAndView indexEarlyReport();

    @RequestMapping(params = "listEarlyReport")
    @ResponseBody
    DxGrid listEarlyReport(AttDayDetailReportItem condition);

    @RequestMapping(params = "exportEarlyReport")
    void exportEarlyReport();

    /** 缺勤报表 */
    @RequestMapping(params = "indexAbsentReport")
    ModelAndView indexAbsentReport();

    @RequestMapping(params = "listAbsentReport")
    @ResponseBody
    DxGrid listAbsentReport(AttDayDetailReportItem condition);

    @RequestMapping(params = "exportAbsentReport")
    void exportAbsentReport();
}
