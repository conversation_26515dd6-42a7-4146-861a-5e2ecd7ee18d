package com.zkteco.zkbiosecurity.att.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttLeaveTypeItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 假种
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:13
 * @since 1.0.0
 */
@RequestMapping(value = "/attLeaveType.do")
public interface AttLeaveTypeRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AttLeaveTypeItem item);

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttLeaveTypeItem condition);

    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);

    /**
     * 判断假种名称 是否有效/允许
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/6/28 9:48
     * @param leaveTypeName
     * @return java.lang.String
     */
    @ResponseBody
    @RequestMapping(params = "isLeaveTypeName")
    ZKResultMsg isLeaveTypeName(@RequestParam(value = "leaveTypeName") String leaveTypeName);

    /**
     * 获取JSON格式的全部假种数据
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/1 10:17
     * @since 1.0.0
     */
    @ResponseBody
    @RequestMapping(params = "listJsonLeaveType")
    ZKResultMsg listJsonLeaveType();

    /**
     * 获取除出差和外出的所有假种
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/13 16:50
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    @ResponseBody
    @RequestMapping(params = "listJsonLeaveTypeFilterTripAndOut")
    ZKResultMsg listJsonLeaveTypeFilterTripAndOut();
}