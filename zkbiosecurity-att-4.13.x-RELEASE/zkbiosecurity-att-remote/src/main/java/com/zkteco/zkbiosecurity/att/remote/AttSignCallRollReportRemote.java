package com.zkteco.zkbiosecurity.att.remote;

import com.zkteco.zkbiosecurity.att.vo.AttRealTimeCallRollItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @version v1.0
 * @ClassName: AttSignCallRollReportRemote
 * @Author: bob.liu
 * @Date: 2020/5/12 10:18
 */
@RequestMapping(value = "/attSignCallRollReport.do")
public interface AttSignCallRollReportRemote {

    /**
     * 默认页面跳转
     *
     * @return
     * @author: bob.liu
     * @date: 2020/5/12 10:18
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 列表数据获取
     *
     * @return
     * @author: bob.liu
     * @date: 2020/5/12 10:31
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttRealTimeCallRollItem condition);

    /**
     * 导出
     *
     * @return
     * @author: bob.liu
     * @date: 2020/5/12 14:58
     */
    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response);
}
