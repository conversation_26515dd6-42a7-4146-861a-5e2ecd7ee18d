package com.zkteco.zkbiosecurity.att.remote;

import java.io.IOException;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttCycleSchItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/attCycleSch.do")
public interface AttCycleSchRemote {

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttCycleSchItem condition);

    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AttCycleSchItem item);

    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);

    /**
     * 清除周期排班
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/4/26 14:02
     * @param type
     *            分组0/部门1/人员2
     * @param ids
     *            分组ID/部门ID/人员ID
     * @param startDate
     *            搜索开始时间
     * @param endDate
     *            搜索结束时间
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "delCycleSch")
    @ResponseBody
    ZKResultMsg delCycleSch(@RequestParam(value = "ids") String ids, @RequestParam(value = "type") Short type,
        @RequestParam(value = "startDate") Date startDate, @RequestParam(value = "endDate") Date endDate);

    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response);

    /**
     * 导出周期排班模版
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/4/26 14:11
     * @return void
     */
    @RequestMapping(params = "exportTemplate")
    void exportTemplate(HttpServletRequest request, HttpServletResponse response);

    /**
     * 导入周期排班
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/4/26 14:17
     * @param upload
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "import")
    @ResponseBody
    ZKResultMsg importExcel(@RequestParam("upload") MultipartFile upload) throws IOException;
}