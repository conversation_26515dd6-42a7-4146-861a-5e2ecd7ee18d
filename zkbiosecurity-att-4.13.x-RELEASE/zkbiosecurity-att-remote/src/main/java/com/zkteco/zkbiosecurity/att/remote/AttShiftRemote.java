package com.zkteco.zkbiosecurity.att.remote;

import com.zkteco.zkbiosecurity.att.vo.AttShiftItem;
import com.zkteco.zkbiosecurity.att.vo.AttShiftSchItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;

/**
 * 班次
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:04
 * @since 1.0.0
 */
@RequestMapping(value = "/attShift.do")
public interface AttShiftRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

   /**
    * 班次添加时间段
    *
    * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
    * @date 2020/7/29 10:57
    * @param id
    * @return org.springframework.web.servlet.ModelAndView
    */
    @RequestMapping(params = "addTimeSlot")
    ModelAndView addTimeSlot(@RequestParam(value = "id", required = false) String id);

    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AttShiftItem item);

    /**
     * 班次设置时间段
     *
     * @param item:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/2/5 15:45
     * @since 1.0.0
     */
    @RequestMapping(params = "saveTimeSlot")
    @ResponseBody
    ZKResultMsg saveTimeSlot(AttShiftItem item);

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttShiftItem condition);

    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);


    @RequestMapping(params = "clean")
    @ResponseBody
    ZKResultMsg clean(@RequestParam(value = "id") String id);

    @RequestMapping(params = "cleanByIds")
    @ResponseBody
    ZKResultMsg cleanByIds(@RequestParam(value = "id") String id, @RequestParam(value = "idp") String idp);

    @RequestMapping(params = "getAttTimeSlotJson")
    @ResponseBody
    Map<String, Object> getAttTimeSlotJson();

    /**
     * 获取班次
     *
     * @author: hongyi.zeng
     * @date: 2018年3月23日 10:36:36
     * @return
     */
    @RequestMapping(params = "getShiftList")
    @ResponseBody
    ZKResultMsg getShiftList();

    @ResponseBody
    @RequestMapping(params = "validNo")
    String validNo(@RequestParam(value = "shiftNo") String shiftNo);

    @ResponseBody
    @RequestMapping(params = "validName")
    String validName(@RequestParam(value = "shiftName") String shiftName);

    /**
     * 获取所有排班
     *
     * @return com.zkteco.zkbiosecurity.base.bean.DxGrid
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/1 16:10
     * @since 1.0.0
     */
    @RequestMapping(params = "allList")
    @ResponseBody
    DxGrid allList(AttShiftSchItem condition);
}