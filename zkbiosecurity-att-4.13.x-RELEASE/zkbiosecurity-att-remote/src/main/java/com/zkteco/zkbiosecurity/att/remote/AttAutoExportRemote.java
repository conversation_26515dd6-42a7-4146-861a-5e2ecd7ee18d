package com.zkteco.zkbiosecurity.att.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttAutoExportItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 报表推送
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 16:32
 * @since 1.0.0
 */
@RequestMapping(value = "/attAutoExport.do")
public interface AttAutoExportRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AttAutoExportItem item);

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttAutoExportItem condition);

    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);

    @RequestMapping(params = "enable")
    @ResponseBody
    ZKResultMsg enable(@RequestParam(value = "ids") String ids);

    @RequestMapping(params = "disable")
    @ResponseBody
    ZKResultMsg disable(@RequestParam(value = "ids") String ids);

    /**
     * ftp测试连接是否成功
     *
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/4/16 9:03
     * @return void
     */
    @RequestMapping(params = "ftpTest")
    @ResponseBody
    ZKResultMsg ftpTest(@RequestParam(value = "sendFormat") String sendFormat, @RequestParam(value = "ftpUrl") String ftpUrl,
        @RequestParam(value = "ftpPort") int ftpPort, @RequestParam(value = "ftpUsername") String ftpUsername,
        @RequestParam(value = "ftpPassword") String ftpPassword);
}