package com.zkteco.zkbiosecurity.att.remote;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.zkteco.zkbiosecurity.att.vo.AttAllPersonSchItem;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttPersonSchItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 人员排班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:39
 * @since 1.0.0
 */
@RequestMapping(value = "/attPersonSch.do")
public interface AttPersonSchRemote {

    @RequestMapping
    ModelAndView index();

    @Deprecated
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    @Deprecated
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AttPersonSchItem item);

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttAllPersonSchItem condition);

    @Deprecated
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);

    /**
     * 根据人员id、日期获取排班列表Json
     * 
     */
    @RequestMapping(params = "getAttShiftSchJsonByDate")
    @ResponseBody
    ZKResultMsg getAttShiftSchJsonByDate();

    /**
     * 人员排班查询列表页面跳转
     * 
     * @return
     */
    @Deprecated
    @RequestMapping(params = "attSelectPersonContent")
    ModelAndView attSelectPersonContent();

    /**
     * 人员排班-周期排班界面跳转
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/4/21 10:36
     * @param type
     *            分组0/部门1/人员2
     * @param ids
     * @return org.springframework.web.servlet.ModelAndView
     */
    @RequestMapping(params = "cycleSch")
    ModelAndView cycleSch(@RequestParam Short type, @RequestParam String ids);

    /**
     * 人员排班-临时排班界面跳转
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/4/21 10:36
     * @param type
     *            分组0/部门1/人员2
     * @param ids
     * @return org.springframework.web.servlet.ModelAndView
     */
    @RequestMapping(params = "tempSch")
    ModelAndView tempSch(@RequestParam Short type, @RequestParam String ids);

    /**
     * 显示排班详情界面跳转
     *
     * @author: GenerationTools
     * @date: 2018-02-23 下午03:52
     * @param id
     * @return
     */
    @RequestMapping(params = "info")
    ModelAndView info(@RequestParam(value = "id", required = false) String id);

    /**
     * 导出人员排班
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/4/26 14:11
     * @param request
     * @param response
     * @return void
     */
    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response);
}