package com.zkteco.zkbiosecurity.att.remote;

import com.zkteco.zkbiosecurity.att.vo.AttClassItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 调班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 17:06
 * @since 1.0.0
 */
@RequestMapping(value = "/attClass.do")
public interface AttClassRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AttClassItem item);

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttClassItem condition);

    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);

    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response);

    /**
     * 导入调班
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/15 15:53
     * @param upload
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    @RequestMapping(params = "import")
    @ResponseBody
    ZKResultMsg importExcel(@RequestParam("upload") MultipartFile upload) throws IOException;

    /**
     * 下载导入模版
     *
     * @param request:
     * @param response:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/9 9:13
     * @since 1.0.0
     */
    @RequestMapping(params = "importTemplate")
    void importTemplate(HttpServletRequest request, HttpServletResponse response);

    /**
     * 审批（通过）
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/14 9:17
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "approval")
    @ResponseBody
    ZKResultMsg approval(@RequestParam(value = "ids") String ids);

    /**
     * 审批（拒绝）
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/14 9:17
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "refuse")
    @ResponseBody
    ZKResultMsg refuse(@RequestParam(value = "ids") String ids);
}