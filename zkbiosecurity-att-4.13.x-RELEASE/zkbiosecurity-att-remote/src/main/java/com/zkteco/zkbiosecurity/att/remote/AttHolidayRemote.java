package com.zkteco.zkbiosecurity.att.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttHolidayItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.Date;

/**
 * 节假日
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 9:53
 * @since 1.0.0
 */
@RequestMapping(value = "/attHoliday.do")
public interface AttHolidayRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AttHolidayItem item);

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttHolidayItem condition);

    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);

    /**
     * 判断名称是否有效/允许
     * 
     * @param holidayName
     * @return
     */
    @ResponseBody
    @RequestMapping(params = "validName")
    String validName(@RequestParam(value = "holidayName") String holidayName);
}