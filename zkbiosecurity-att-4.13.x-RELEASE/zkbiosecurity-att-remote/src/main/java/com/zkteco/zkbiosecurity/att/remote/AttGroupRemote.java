package com.zkteco.zkbiosecurity.att.remote;

import com.zkteco.zkbiosecurity.att.vo.AttGroupPersonSelectItem;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttGroupItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 分组
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 18:03
 * @since 1.0.0
 */
@RequestMapping(value = "/attGroup.do")
public interface AttGroupRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AttGroupItem item);

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttGroupItem condition);

    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);

    @ResponseBody
    @RequestMapping(params = "validName")
    String validName(@RequestParam(value = "groupName") String groupName);

    /**
     * 选人控件列表数据获取
     *
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param condition
     * @return
     */
    @RequestMapping(params = "selectList")
    @ResponseBody
    DxGrid selectList(AttGroupPersonSelectItem condition);

    @RequestMapping(params = "tree")
    @ResponseBody
    TreeItem tree();

}