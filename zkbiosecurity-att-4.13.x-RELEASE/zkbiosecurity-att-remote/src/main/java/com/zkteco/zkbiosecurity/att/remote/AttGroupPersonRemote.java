package com.zkteco.zkbiosecurity.att.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttGroupPersonItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 分组人员
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/3/31 18:12
 * @since 1.0.0
 */
@RequestMapping(value = "/attGroupPerson.do")
public interface AttGroupPersonRemote {


    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttGroupPersonItem condition);

    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save();

    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids, @RequestParam(value = "groupId") String groupId);

}
