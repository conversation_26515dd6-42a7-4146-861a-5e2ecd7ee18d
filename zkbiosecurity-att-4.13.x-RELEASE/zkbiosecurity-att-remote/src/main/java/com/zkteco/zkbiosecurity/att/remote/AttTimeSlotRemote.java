package com.zkteco.zkbiosecurity.att.remote;

import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotBreakTimeItem;
import com.zkteco.zkbiosecurity.att.vo.AttTimeSlotItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/**
 * 时间段
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 11:52
 * @since 1.0.0
 */
@RequestMapping(value = "/attTimeSlot.do")
public interface AttTimeSlotRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-02-23 下午03:52
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-02-23 下午03:52
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-02-23 下午03:52
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AttTimeSlotItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-02-23 下午03:52
     * @param condition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttTimeSlotItem condition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-02-23 下午03:52
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);

    // @RequestMapping(params = "export")
    // void export(HttpServletRequest request, HttpServletResponse response);

    // @ResponseBody
    // @RequestMapping(params = "validNo")
    // @Deprecated
    // String validNo(@RequestParam(value = "periodNo") String periodNo);

    @ResponseBody
    @RequestMapping(params = "validName")
    String validName(@RequestParam(value = "periodName") String periodName);

    /**
     * 时间段的休息时间段
     * 
     * @param condition
     * @return
     */
    @RequestMapping(params = "breakTimeList")
    @ResponseBody
    DxGrid breakTimeList(AttTimeSlotBreakTimeItem condition);

    /**
     * 添加休息时间段
     * 
     * @param timeSlotId
     * @param breakTimeIds
     * @return
     */
    @ResponseBody
    @RequestMapping(params = "addBreakTime")
    ZKResultMsg addBreakTime(@RequestParam(value = "timeSlotId") String timeSlotId,
        @RequestParam(value = "breakTimeIds") String breakTimeIds);

    /**
     * 删除休息时间段
     * 
     * @param timeSlotId
     * @param breakTimeIds
     * @return
     */
    @RequestMapping(params = "delBreakTime")
    @ResponseBody
    ZKResultMsg delBreakTime(@RequestParam(value = "timeSlotId") String timeSlotId,
        @RequestParam(value = "breakTimeIds") String breakTimeIds);

    /**
     * 计算间段时长
     * 
     * @param timeSlotId
     * @return
     */
    @RequestMapping(params = "countSegmentTime")
    @ResponseBody
    ZKResultMsg countSegmentTime(@RequestParam(value = "timeSlotId") String timeSlotId,
                                 @RequestParam(value = "breakTimeArray") String breakTimeArray);

    /**
     * 编辑的时候，判断上班时间是否小于所选择的最小休息时间段。 判断下班的时间是否大于所选择的最大休息时间段。
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2019/6/19 11:14
     * @param timeSlotId
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "timeJudgment")
    @ResponseBody
    ZKResultMsg timeJudgment(@RequestParam(value = "timeSlotId") String timeSlotId,
        @RequestParam(value = "toWorkTime") String toWorkTime, @RequestParam(value = "offWorkTime") String offWorkTime);

    /**
     ** <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @Description 判断休息时间段是否在工作时间范围内
     * @date 2020/5/7
     **/
    @RequestMapping(params = "breakTimeIsInWorkTime")
    @ResponseBody
    ZKResultMsg breakTimeIsInWorkTime(@RequestParam(value = "breakTimeListStr") String breakTimeListStr,
        @RequestParam(value = "toWorkTime") String toWorkTime, @RequestParam(value = "offWorkTime") String offWorkTime);

    /**
     * 获取正常时间段数据
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/4/22 13:48
     * @param
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "getNormalList")
    @ResponseBody
    ZKResultMsg getNormalList();

    /**
     * 获取正常时间段树数据，用于前端拖拽
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/4/28 15:24
     * @param
     * @return com.zkteco.zkbiosecurity.base.bean.TreeItem
     */
    @RequestMapping(params = "getTree")
    @ResponseBody
    TreeItem getTree();

    /**
     * 获取所有时间段集合
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/2/5 9:37
     * @since 1.0.0
     */
    @RequestMapping(params = "getAllList")
    @ResponseBody
    ZKResultMsg getAllList();
}