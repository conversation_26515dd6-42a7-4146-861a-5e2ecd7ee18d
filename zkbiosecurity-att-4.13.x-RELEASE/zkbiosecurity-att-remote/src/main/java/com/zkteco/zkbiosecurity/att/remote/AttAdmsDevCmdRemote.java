package com.zkteco.zkbiosecurity.att.remote;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttAdmsDevCmdItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 服务器下发命令
 * 
 * <AUTHOR>
 * @date 2020年4月16日 下午5:58:56
 * @version V1.0
 */
@RequestMapping(value = "/attAdmsDevCmd.do")
public interface AttAdmsDevCmdRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttAdmsDevCmdItem codition);

    /**
     * 导出服务器下发考勤命令
     * 
     * <AUTHOR>
     * @since 2020年4月20日下午5:48:43
     * @param request
     * @param response
     */
    @RequestMapping(params = "export")
    @ResponseBody
    void export(HttpServletRequest request, HttpServletResponse response);

    /**
     * 清空当前查出的考勤命令表
     * 
     * <AUTHOR>
     * @since 2020年4月21日上午8:59:23
     * @return
     */
    @RequestMapping(params = "clean")
    @ResponseBody
    ZKResultMsg clean(@RequestParam("ids") String ids);
}
