package com.zkteco.zkbiosecurity.att.remote;

import com.zkteco.zkbiosecurity.att.vo.AttOvertimeItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 加班
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 10:30
 * @since 1.0.0
 */
@RequestMapping(value = "/attOvertime.do")
public interface AttOvertimeRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AttOvertimeItem item);

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttOvertimeItem condition);

    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);

    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response);

    /**
     * 下载导出模版
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/15 9:19
     * @param request
     * @param response
     * @return void
     */
    @RequestMapping(params = "importTemplate")
    void importTemplate(HttpServletRequest request, HttpServletResponse response);

    /**
     * 导入加班
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/15 14:16
     * @param upload
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    @RequestMapping(params = "import")
    @ResponseBody
    ZKResultMsg importExcel(@RequestParam("upload") MultipartFile upload) throws IOException;

    /**
     * 审批（通过）
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/14 9:17
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "approval")
    @ResponseBody
    ZKResultMsg approval(@RequestParam(value = "ids") String ids);

    /**
     * 审批（拒绝）
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/14 9:17
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "refuse")
    @ResponseBody
    ZKResultMsg refuse(@RequestParam(value = "ids") String ids);
}