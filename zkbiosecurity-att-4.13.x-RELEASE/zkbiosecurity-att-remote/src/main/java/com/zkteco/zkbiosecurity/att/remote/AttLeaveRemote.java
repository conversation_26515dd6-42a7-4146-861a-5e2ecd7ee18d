package com.zkteco.zkbiosecurity.att.remote;

import java.io.IOException;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.att.vo.AttLeaveItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 请假
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 9:56
 * @since 1.0.0
 */
@RequestMapping(value = "/attLeave.do")
public interface AttLeaveRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    @RequestMapping(params = "viewLeavePhoto")
    ModelAndView viewLeavePhoto(@RequestParam(value = "key", required = false) String key);

    @RequestMapping(params = "save", consumes = "multipart/form-data")
    @ResponseBody
    ZKResultMsg save(AttLeaveItem item, @RequestParam(value = "leaveImage", required = false) MultipartFile[] files);

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AttLeaveItem condition);

    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);

    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response);

    /**
     * 展示请假图片
     */
    @RequestMapping(params = "showImage")
    @ResponseBody
    ModelAndView showImage(@RequestParam(value = "id") String id);

    /**
     * 获取请假时间范围的时长(分总、小时)
     */
    @RequestMapping(params = "calLeaveTime")
    @ResponseBody
    ZKResultMsg calLeaveTime();

    /**
     * 判断人员在相同时间内是否有申请
     *
     * @author: verber
     * @date: 2018-06-25 下午03:52
     * @param personIds
     * @param startTime
     * @param endTime
     * @param exceptionType
     *            异常类型
     * @return
     */
    @RequestMapping(params = "existApply")
    @ResponseBody
    ZKResultMsg existApply(@RequestParam(value = "personIds") String personIds,
        @RequestParam(value = "startTime") Date startTime, @RequestParam(value = "endTime") Date endTime,
        @RequestParam(value = "exceptionType") String exceptionType,
        @RequestParam(value = "leaveTypeId", required = false) String leaveTypeId);

    /**
     * 判断人员在相同时间内是否有申请调班
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/6/12 18:08
     * @param adjustPersonId
     * @param adjustDate
     * @param swapPersonId
     * @param swapDate
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "existAdjustOrClassApply")
    @ResponseBody
    ZKResultMsg existAdjustOrClassApply(@RequestParam(value = "adjustPersonId") String adjustPersonId,
        @RequestParam(value = "adjustDate") Date adjustDate,
        @RequestParam(value = "swapPersonId", required = false) String swapPersonId,
        @RequestParam(value = "swapDate", required = false) Date swapDate);

    /**
     * 下载导出模版
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/15 9:19
     * @param request
     * @param response
     * @return void
     */
    @RequestMapping(params = "importTemplate")
    void importTemplate(HttpServletRequest request, HttpServletResponse response);

    /**
     * 导入请假(包含出差、外出)
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/14 18:57
     * @param upload
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    @RequestMapping(params = "import")
    @ResponseBody
    ZKResultMsg importExcel(@RequestParam("upload") MultipartFile upload) throws IOException;

    /**
     * 审批（通过
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/14 9:17
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "approval")
    @ResponseBody
    ZKResultMsg approval(@RequestParam(value = "ids") String ids);

    /**
     * 审批（拒绝）
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/7/14 9:17
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "refuse")
    @ResponseBody
    ZKResultMsg refuse(@RequestParam(value = "ids") String ids);

    /**
     * 获取请假图片
     * 
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/1 9:57
     * @since 1.0.0
     */
    @RequestMapping(params = "getLeaveImage")
    @ResponseBody
    String getLeaveImage();

    @RequestMapping(params = "getImageUrl")
    @ResponseBody
    ZKResultMsg getImageUrl(@RequestParam(value = "id") String id);

}