package com.zkteco.zkbiosecurity.att.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 异常申请、查看（员工自助）
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/4/1 15:54
 * @since 1.0.0
 */
@RequestMapping(value = "/attWfSelfApply.do")
public interface AttWfApplyRemote {

    @RequestMapping(params = "startApply")
    ModelAndView apply(String flowType);

    @RequestMapping(params = "approve")
    ModelAndView approve(String taskId);

    @RequestMapping(params = "detail")
    ModelAndView detail(String businessKey);

    @RequestMapping(params = "cancelApply")
    ModelAndView cancelApply(String businessKey);

    /**
     * 根据流程类型验证申请人是否有设置流程
     * 
     * @param flowType
     * @return
     */
    @RequestMapping(params = "validApplyFlow")
    @ResponseBody
    ZKResultMsg validApplyFlow(String flowType);
}
