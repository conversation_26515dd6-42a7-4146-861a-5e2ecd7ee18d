#系统名称 越南语
att_systemName=Hệ thống chấm công 1.0
#=====================================================================
#左侧菜单
att_module=Chấm công
#左侧菜单-考勤设备
att_leftMenu_attendanceDevice=Quản lý thiết bị
att_leftMenu_device=Thiết bị chấm công
att_leftMenu_point=Điểm chấm công
att_leftMenu_sign_address=Địa chỉ đăng nhập di động
att_leftMenu_adms_devCmd=Máy chủ đã ra lệnh
#左侧菜单-基础信息
att_leftMenu_basicInformation=Thông tin cơ bản
att_leftMenu_rule=Quy tắc
att_leftMenu_base_rule=Quy tắc tham dự
att_leftMenu_department_rule=Nội quy
att_leftMenu_holiday=Ngày nghỉ
att_leftMenu_leaveType=Loại nghỉ
att_leftMenu_timingCalculation=Tính toán thời gian
att_leftMenu_autoExport=Báo cáo đẩy
att_leftMenu_param=Cài đặt tham số
#左侧菜单-班次管理
att_leftMenu_shiftManagement=Ca
att_leftMenu_timeSlot=Thời gian biểu
att_leftMenu_shift=Ca
#左侧菜单-排班管理
att_leftMenu_scheduleManagement=Lịch trình
att_leftMenu_group=Nhóm
att_leftMenu_groupPerson=Nhóm người
att_leftMenu_groupSch=Lịch trình nhóm
att_leftMenu_deptSch=Lịch trình theo bộ phận
att_leftMenu_personSch=Lịch trình cá nhân
att_leftMenu_tempSch=Lịch trình tạm thời
att_leftMenu_nonSch=Nhân sự không theo lịch trình
#左侧菜单-异常管理
att_leftMenu_exceptionManagement=Quản lý ngoại lệ đi học
att_leftMenu_sign=Nhật ký bổ sung
att_leftMenu_leave=Nghỉ phép
att_leftMenu_trip=Chuyến công tác
att_leftMenu_out=Ra ngoài
att_leftMenu_overtime=Làm thêm giờ
att_leftMenu_adjust=Điều chỉnh và bổ sung
att_leftMenu_class=Điều chỉnh ca
#左侧菜单-统计报表
att_leftMenu_statisticalReport=Báo cáo Thống kê Điểm danh
att_leftMenu_manualCalculation=Tính toán thủ công
att_leftMenu_transaction=Sự kiện
att_leftMenu_dayCardDetailReport=Chấm công hàng ngày
att_leftMenu_leaveSummaryReport=Tóm tắt nghỉ
att_leftMenu_dayDetailReport=Báo cáo ngày
att_leftMenu_monthDetailReport=Báo cáo tháng chi tiết
att_leftMenu_monthStatisticalReport=Báo cáo thống kê nhân viên hàng tháng
att_leftMenu_deptStatisticalReport=Báo cáo thống kê hàng tháng
att_leftMenu_yearStatisticalReport=Báo cáo hàng năm (theo người)
att_leftMenu_attSignCallRollReport=Đăng nhập điểm danh
att_leftMenu_workTimeReport=Báo cáo thời gian làm việc
#左侧菜单-设备操作日志
att_leftMenu_device_op_log=Nhật ký vận hành thiết bị
#左侧菜单-实时点名
att_leftMenu_realTime_callRoll=Điểm danh
#=====================================================================
#公共
att_common_person=Nhân sự
att_common_pin=ID
att_common_group=Nhóm
att_common_dept=Bộ phận
att_common_symbol=Ký hiệu
att_common_deptNo=Số của bộ phận
att_common_deptName=Tên bộ phận
att_common_groupNo=Số nhóm
att_common_groupName=Tên nhóm
att_common_operateTime=Thời gian hoạt động
att_common_operationFailed=Hoạt động thất bại
att_common_id=ID
att_common_deptId=ID bộ phận
att_common_groupId=ID nhóm
att_common_deviceId=ID thiết bị
att_person_pin=ID nhân sự
att_person_name=Tên
att_person_lastName=Họ
att_person_internalCard=Số thẻ
att_person_attendanceMode=Thời gian và chế độ tham dự
att_person_normalAttendance=Tham dự bình thường
att_person_noPunchCard=Không có thẻ đục lỗ
att_common_attendance=Chấm công
att_common_attendance_hour=Chấm công (giờ)
att_common_attendance_day=Chấm công (ngày)
att_common_late=Muộn
att_common_early=Sớm
att_common_overtime=Thêm giờ
att_common_exception=Ngoại lệ
att_common_absent=Nghỉ
att_common_leave=Nghỉ phép
att_common_trip=Công tác
att_common_out=Ra ngoài
att_common_staff=Nhân viên
att_common_superadmin=Super admin
att_common_msg=Nội dung SMS
att_common_min=Thời lượng tin nhắn (phút)
att_common_letterNumber=Chỉ có thể nhập số hoặc chữ cái!
att_common_relationDataCanNotDel=Các dữ liệu liên quan không thể bị xóa.
att_common_relationDataCanNotEdit=Các dữ liệu liên quan không thể bị sửa đổi.
att_common_needSelectOneArea=Vui lòng chọn một khu vực!
att_common_neesSelectPerson=Vui lòng chọn một người!
att_common_nameNoSpace=Tên không thể chứa dấu cách!
att_common_digitsValid=Bạn chỉ có thể nhập các số không quá hai số thập phân!
att_common_numValid=Chỉ nhập số!
#=====================================================================
#工作面板
att_dashboard_worker=Người mê làm việc
att_dashboard_today=Chấm công hôm nay
att_dashboard_todayCount=Số liệu thống kê chấm công ngày hôm nay
att_dashboard_exceptionCount=Thống kê bất thường (tháng này)
att_dashboard_lastWeek=Tuần trước
att_dashboard_lastMonth=Tháng trước
att_dashboard_perpsonNumber=Tổng số nhân sự
att_dashboard_actualNumber=Nhân sự thực tế
att_dashboard_notArrivedNumber=Nhân viên vắng mặt
att_dashboard_attHour=Thời gian làm việc
#区域
#设备
att_op_syncDev=Đồng bộ hóa dữ liệu phần mềm với thiết bị
att_op_account=Kiểm tra dữ liệu chấm công
att_op_check=Tải lại dữ liệu
att_op_deleteCmd=Xóa lệnh thiết bị
att_op_dataSms=Tin nhắn công cộng
att_op_clearAttPic=Xóa ảnh chấm công
att_op_clearAttLog=Xóa các giao dịch chấm công
att_device_waitCmdCount=Các lệnh được thực thi
att_device_status=Kích hoạt trạng thái
att_device_register=Đăng ký thiết bị
att_device_isRegister=Thiết bị đăng ký
att_device_existNotRegDevice=Thiết bị máy không đăng ký, dữ liệu không thể có được!
att_device_fwVersion=Phiên bản Firmware
att_device_transInterval=Thời lượng làm mới (phút)
att_device_cmdCount=Số lượng lệnh tối đa để giao tiếp với máy chủ.
att_device_delay=Thời gian ghi lại yêu cầu (giây)
att_device_timeZone=Bảng thời gian
att_device_operationLog=Nhật ký hoạt động
att_device_registeredFingerprint=Đăng ký vân tay
att_device_registeredUser=Đăng ký nhân sự
att_device_fingerprintImage=Ảnh vân tay
att_device_editUser=Chỉnh sửa nhân sự
att_device_modifyFingerprint=Chỉnh sửa vân tay
att_device_faceRegistration=Đăng ký khuôn mặt
att_device_userPhotos=Ảnh nhân sự
att_device_attLog=Có tải lên bản ghi chấm công không?
att_device_operLog=Có tải lên thông tin Nhân sự không?
att_device_attPhoto=Có tải lên ảnh chấm công?
att_device_isOnLine=Trạng thái trực tuyến
att_device_InputPin=Nhập số người
att_device_getPin=Lấy dữ liệu nhân sự được chỉ định
att_device_separatedPin=Nhiều nhân sự, cách nhau bằng dấu phẩy
att_device_authDevice=Thiết bị ủy quyền
att_device_disabled=Các thiết bị sau đây bị vô hiệu hóa và không thể được vận hành!
att_device_autoAdd=Thiết bị mới được thêm tự động
att_device_receivePersonOnlyDb=Chỉ nhận dữ liệu của nhân viên có trong cơ sở dữ liệu
att_devMenu_control=Kiểm soát thiết bị
att_devMenu_viewOrGetInfo=Xem và lấy thông tin
att_devMenu_clearData=Xóa dữ liệu thiết bị
att_device_disabledOrOffline=Thiết bị không được kích hoạt hoặc không trực tuyến và không thể được vận hành!
att_device_areaStatus=Trạng thái khu vực thiết bị
att_device_areaCommon=Vùng là bình thường
att_device_areaEmpty=Khu vực trống
att_device_isRegDev=Múi giờ hoặc sửa đổi trạng thái đăng ký yêu cầu thiết bị khởi động lại để có hiệu lực!
att_device_canUpgrad=Các thiết bị sau có thể được nâng cấp
att_device_offline=Các thiết bị sau đây ngoại tuyến và không thể được vận hành!
att_device_oldProtocol=Giao thức cũ
att_device_newProtocol=Giao thức mới
att_device_noMoreToven=Gói nâng cấp chương trình cơ sở giao thức cũ không thể vượt quá 20M
att_device_transferFilesTip=Firmware được phát hiện thành công, truyền tệp
att_op_clearAttPers=Nhân viên thiết bị rõ ràng
#区域人员
att_op_forZoneAddPers=Thiết lập nhân sự khu vực
att_op_dataUserSms=Tin nhắn cá nhân
att_op_syncPers=Đồng bộ hóa lại với thiết bị
att_areaPerson_choiceArea=Vui lòng chọn khu vực!
att_areaPerson_byAreaPerson=Theo vùng
att_areaPerson_setByAreaPerson=Đặt theo nhân viên khu vực
att_areaPerson_importBatchDel=Nhập hàng loạt xóa
att_areaPerson_syncToDevSuccess=Hoạt động thành công! Xin vui lòng chờ lệnh được gửi.
att_areaPerson_personId=ID nhân sự
att_areaPerson_areaId=ID khu vực
att_area_existPerson=Có người trong khu vực!
att_areaPerson_notice1=Khu vực hoặc nhân sự không thể để trống cùng một lúc!
att_areaPerson_notice2=Không có người hoặc khu vực đã được truy vấn!
att_areaPerson_notice3=Không tìm thấy thiết bị nào trong khu vực!
att_areaPerson_addArea=Thêm vùng
att_areaPerson_delArea=Xóa khu vực
att_areaPerson_persNoExit=Người không tồn tại
att_areaPerson_importTip1=Vui lòng đảm bảo rằng người đã nhập đã tồn tại trong mô-đun nhân sự
att_areaPerson_importTip2=Các nhà nhập khẩu hàng loạt sẽ không được tự động gửi đến thiết bị và cần được đồng bộ hóa thủ công
att_areaPerson_addAreaPerson=Thêm nhân viên khu vực
att_areaPerson_delAreaPerson=Xóa nhân viên khu vực
att_areaPerson_importDelAreaPerson=Nhập và xóa nhân viên khu vực
att_areaPerson_importAreaPerson=Nhân viên khu vực nhập khẩu
#考勤点
att_attPoint_name=Tên điểm chấm công
att_attPoint_list=Danh sách điểm chấm công
att_attPoint_deviceModule=Thiết bị
att_attPoint_acc=Kiểm soát truy cập
att_attPoint_park=Đỗ xe
att_attPoint_ins=FaceKiosk
att_attPoint_pid=Giấy chứng nhận cá nhân
att_attPoint_vms=Video
att_attPoint_psg=lối đi
att_attPoint_doorList=Danh sách cửa
att_attPoint_deviceList=Danh sách thiết bị
att_attPoint_channelList=Danh sách kênh
att_attPoint_gateList=Danh sách cổng
att_attPoint_recordTypeList=Kéo loại bản ghi
att_attPoint_door=Vui lòng chọn cửa tương ứng.
att_attPoint_device=Vui lòng chọn thiết bị tương ứng.
att_attPoint_gate=Vui lòng chọn cổng tương ứng
att_attPoint_normalPassRecord=Hồ sơ vượt qua bình thường
att_attPoint_verificationRecord=Hồ sơ xác minh
att_person_attSet=Thiết lập chấm công
att_attPoint_point=Vui lòng chọn điểm chấm công.
att_attPoint_count=Không đủ điểm giấy phép chấm công được ủy quyền; lỗi hệ thống!
att_attPoint_notSelect=Mô-đun không được cấu hình
att_attPoint_accInsufficientPoints=Điểm chấm công không đầy đủ cho bản ghi kiểm soát truy cập!
att_attPoint_parkInsufficientPoints=Điểm chấm công không đầy đủ cho bản ghi bãi đậu xe!
att_attPoint_insInsufficientPoints=Điểm giấy phép chấm công của FaceKiosk không đủ
att_attPoint_pidInsufficientPoints=Chứng chỉ nhân sự không đủ để chấm công!
att_attPoint_doorOrParkDeviceName=Tên cửa hoặc tên thiết bị đỗ xe
att_attPoint_vmsInsufficientPoints=Video khi điểm tham dự là không đủ!
att_attPoint_psgInsufficientPoints=Kênh không đủ điểm tham dự!
att_attPoint_delDevFail=Xóa thiết bị thất bại, thiết bị đã được sử dụng
att_attPoint_pullingRecord=Điểm tham dự là nhận hồ sơ thường xuyên, xin vui lòng chờ đợi!
att_attPoint_lastTransactionTime=Thời gian kéo dữ liệu cuối cùng
att_attPoint_masterDevice=Thiết bị chính
att_attPoint_channelName=Tên kênh
att_attPoint_cameraName=Tên máy ảnh
att_attPoint_cameraIP=camera ip
att_attPoint_channelIP=ip kênh
att_attPoint_gateNumber=Số cổng
att_attPoint_gateName=Tên cổng
#APP考勤签到地址
att_signAddress_address=Địa chỉ
att_signAddress_longitude=Kinh độ
att_signAddress_latitude=Vĩ độ
att_signAddress_range=Phạm vi hiệu quả
att_signAddress_rangeUnit=Đơn vị là mét (m)
#=====================================================================
#规则
att_rule_baseRuleSet=Thiết lập quy tắc cơ bản
att_rule_countConvertSet=Cài đặt tính toán
att_rule_otherSet=Các thiết lập khác
att_rule_baseRuleSignIn=Quy tắc chấm vào
att_rule_baseRuleSignOut=Quy tắc chấm ra
att_rule_earliestPrinciple=Quy tắc đến sớm
att_rule_theLatestPrinciple=Quy tắc đến muộn
att_rule_principleOfProximity=Quy tắc tiệm cận
att_rule_baseRuleShortestMinutes=Khoảng thời gian tối thiểu phải lớn hơn (tối thiểu 10 phút)
att_rule_baseRuleLongestMinutes=Khoảng thời gian tối đa phải nhỏ hơn (tối đa 1440 phút)
att_rule_baseRuleLateAndEarly=Muộn và nghỉ sớm được tính là vắng mặt
att_rule_baseRuleCountOvertime=Thống kê làm thêm
att_rule_baseRuleFindSchSort=Tìm kiếm bản ghi ca
att_rule_groupGreaterThanDepartment=Nhóm->Bộ phận
att_rule_departmentGreaterThanGroup=Bộ phận->Nhóm
att_rule_baseRuleSmartFindClass=Quy tắc thay đổi kết hợp thông minh
att_rule_timeLongest=Thời gian làm việc dài nhất
att_rule_exceptionLeast=Bất thường ít nhất
att_rule_baseRuleCrossDay=Kết quả tính toán chấm công cho ca làm việc chéo
att_rule_firstDay=Ngày đầu
att_rule_secondDay=Ngày thứ hai
att_rule_baseRuleShortestOvertimeMinutes=Thời gian làm thêm ngắn nhất (phút)
att_rule_baseRuleMaxOvertimeMinutes=Làm thêm giờ tối đa (phút)
att_rule_baseRuleElasticCal=Tính toán thời lượng linh hoạt
att_rule_baseRuleTwoPunch=Thời gian tích lũy cho mỗi cặp chấm
att_rule_baseRuleStartEnd=Tính toán thời gian chấm đầu và cuối
att_rule_countConvertHour=Quy tắc chuyển đổi giờ
att_rule_formulaHour=Công thức: Giờ=Phút / 60
att_rule_countConvertDay=Quy tắc chuyển đổi ngày
att_rule_formulaDay=Công thức: Ngày = Số phút / Số phút làm việc mỗi ngày
att_rule_inFormulaShallPrevail=Lấy kết quả tính theo công thức làm tiêu chuẩn;
att_rule_remainderHour=Phần còn lại lớn hơn hoặc bằng
att_rule_oneHour=Tính như một giờ;
att_rule_halfAnHour=Tính là nửa giờ, nếu không thì bỏ qua;
att_rule_remainderDay=Đơn vị lớn hơn hoặc bằng số phút làm việc
att_rule_oneDay=%,được tính là một ngày;
att_rule_halfAnDay=%,tính là nửa ngày, nếu không thì bỏ qua;
att_rule_countConvertAbsentDay=Quy tắc chuyển đổi ngày vắng mặt
att_rule_markWorkingDays=Tính theo ngày làm việc
att_rule_countConvertDecimal=Các chữ số chính xác của dấu thập phân
att_rule_otherSymbol=Cài đặt biểu tượng kết quả chấm công trong báo cáo
att_rule_arrive=Dự kiến / thực tế
att_rule_noSignIn=Không chấm vào
att_rule_noSignOff=Không chấm ra
att_rule_off=Điều chỉnh phần còn lại
att_rule_class=Bổ sung chấm công
att_rule_shortLessLong=Thời gian biểu chấm công không thể dài hơn thời gian biểu chấm công dài nhất.
att_rule_symbolsWarning=Bạn phải cấu hình biểu tượng trong báo cáo chấm công!
att_rule_reportSettingSet=Thiết lập xuất khẩu báo cáo
att_rule_shortDateFormat=Định dạng ngày
att_rule_shortTimeFormat=Định dạng thời gian
att_rule_baseRuleSignBreakTime=Còn lại là đồng hồ
att_leftMenu_custom_rule=Quy tắc tùy chỉnh
att_custom_rule_already_exist={0} Có một quy tắc tùy chỉnh!
att_add_group_custom_rule=Thêm các quy tắc nhóm
att_custom_rule_type=Loại quy tắc
att_rule_type_group=Quy tắc nhóm
att_rule_type_dept=Quy tắc
att_custom_rule_orgNames=Sử dụng các đối tượng
att_rult_maxOverTimeType1=Không giới hạn
att_rult_maxOverTimeType2=Tuần này
att_rult_maxOverTimeType3=Tháng này
att_rule_countConvertDayRemark1=Ví dụ: Thời gian làm việc hiệu quả là 500 phút và thời gian làm việc là 480 phút mỗi ngày. Kết quả là 500/480=1.04 và số thập phân cuối cùng được giữ ở mức 1.0.
att_rule_countConvertDayRemark2=Ví dụ: Thời gian làm việc hiệu quả là 500 phút và thời gian làm việc là 480 phút mỗi ngày. Kết quả là 500/480=1.04, 1.04>0.8.
att_rule_countConvertDayRemark3=Ví dụ: Thời gian làm việc hiệu quả là 300 phút và thời gian làm việc là 480 phút mỗi ngày. Kết quả là 300/480=0.625, 0.2 <0.625<0.8 trong nửa ngày.
att_rule_countConvertDayRemark4=Điểm chuẩn chuyển đổi ngày: Ghi là ngày làm việc trong khoảng thời gian không hoạt động;
att_rule_countConvertDayRemark5=Nó được ghi là số ngày làm việc: nó được giới hạn trong việc tính số ngày hoàn thành và miễn là hoàn thành trong mỗi giai đoạn, thời gian hoàn thành được tính theo số ngày làm việc của thời kỳ;
att_rule_baseRuleSmartFindRemark1=Thời gian dài nhất: Theo điểm thẻ trong ngày, hãy tính giờ làm việc tương ứng với mỗi ca trong ngày và tìm ca làm việc của thời gian làm việc dài nhất trong ngày;
att_rule_baseRuleSmartFindRemark2=Tối thiểu bất thường: Tính số lần bất thường tương ứng với mỗi ca trong ngày theo điểm thẻ trong ngày và tìm ca làm việc với số lượng bất thường ít nhất trong ngày để tính thời gian làm việc;
att_rule_baseRuleHourValidator=Phút phán xét nửa giờ không thể lớn hơn hoặc bằng 1 giờ!
att_rule_baseRuleDayValidator=Thời gian phán xét trong nửa ngày không thể lớn hơn hoặc bằng thời gian phán xét của một ngày!
att_rule_overtimeWarning=Thời lượng làm thêm giờ tối đa không thể nhỏ hơn thời lượng làm thêm tối thiểu duy nhất tối thiểu!
att_rule_noSignInCountType=Không đăng ký
att_rule_absent=QueQin
att_rule_earlyLeave=Đi sớm
att_rule_noSignOffCountType=Không kiểm tra trở lại
att_rule_minutes=phút
att_rule_noSignInCountLateMinute=Không đăng ký là số phút trễ
att_rule_noSignOffCountEarlyMinute=Không kiểm tra trở lại để nghỉ sớm phút
att_rule_incomplete=Không đầy đủ
att_rule_noCheckInIncomplete=Chưa đăng nhập là không đầy đủ
att_rule_noCheckOutIncomplete=Không đăng nhập lại là không đầy đủ
att_rule_lateMinuteWarning=Chưa đăng ký sẽ được ghi lại vì số phút muộn phải lớn hơn 0 và nhỏ hơn thời gian tham dự dài nhất
att_rule_earlyMinuteWarning=Không được chọn cho số phút khởi hành sớm phải lớn hơn 0 và nhỏ hơn thời gian tham dự dài nhất
att_rule_baseRuleNoSignInCountLateMinuteRemark=Khi không đăng ký được tính là muộn, nếu không đăng ký, nó được tính là trễ trong N phút
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=Khi không đăng xuất được tính là khởi hành sớm, nếu không đăng xuất, nó sẽ được tính là khởi hành sớm N phút
#节假日
att_holiday_placeholderNo=Nên bắt đầu với H, chẳng hạn như H01.
att_holiday_placeholderName=Bạn nên đặt tên bằng [Năm] + [Tên ngày lễ], vd. [Ngày lao động 2017].
att_holiday_dayNumber=Số ngày
att_holiday_validDate_msg=Ngày lễ trong thời gian này
#假种
att_leaveType_leaveThing=Nghỉ phép
att_leaveType_leaveMarriage=Nghỉ kết hôn
att_leaveType_leaveBirth=Nghỉ thai sản
att_leaveType_leaveSick=Nghỉ ốm
att_leaveType_leaveAnnual=Nghỉ thường niên
att_leaveType_leaveFuneral=Nghỉ tang
att_leaveType_leaveHome=Nghỉ phép ở nhà
att_leaveType_leaveNursing=Nghỉ cho con bú
att_leaveType_isDeductWorkLong=Có trừ giờ làm việc không?
att_leaveType_placeholderNo=Nên bắt đầu với L, chẳng hạn như L1.
att_leaveType_placeholderName=Khuyến cáo để kết thúc với nghỉ, ví dụ: Nghỉ kết hôn
#定时计算
att_timingcalc_timeCalcFrequency=Khoảng thời gian tính toán
att_timingcalc_timeCalcInterval=Thời gian tính toán
att_timingcalc_timeSet=Cài đặt thời gian tính toán
att_timingcalc_timeSelect=Hãy chọn thời gian!
att_timingcalc_optionTip=Ít nhất một tính toán tham dự theo lịch trình hàng ngày phải được giữ lại
#自动导出报表
att_autoExport_reportType=Loại báo cáo
att_autoExport_fileType=Loại tệp
att_autoExport_fileName=Tên tệp
att_autoExport_fileDateFormat=Định dạng ngày tháng
att_autoExport_fileTimeFormat=Định dạng thời gian
att_autoExport_fileContentFormat=Định dạng nội dung
att_autoExport_fileContentFormatTxt=Ví dụ {Tên phòng} 00 {ID nhân sự} 01 {Tên nhân sự} 02 {Thời gian chấm công} 03
att_autoExport_timeSendFrequency=Tần suất gửi
att_autoExport_timeSendInterval=Khoảng thời gian gửi
att_autoExport_emailType=Loại nhận thư
att_autoExport_emailRecipients=Nhận thư
att_autoExport_emailAddress=Địa chỉ thư điện tử
att_autoExport_emailExample=Ví dụ: <EMAIL>,<EMAIL>
att_autoExport_emailSubject=Tiêu đề thư
att_autoExport_emailContent=Nội dung thư
att_autoExport_field=Trường
att_autoExport_fieldName=Tên trường
att_autoExport_fieldCode=Số trường
att_autoExport_reportSet=Cài đặt báo cáo
att_autoExport_timeSet=Cài đặt thời gian gửi thư
att_autoExport_emailSet=Cài đặt thư
att_autoExport_emailSetAlert=Hãy điền địa chỉ email của bạn.
att_autoExport_emailTypeSet=Cài đặt người nhận
att_autoExport_byDay=Theo ngày
att_autoExport_byMonth=Theo tháng
att_autoExport_byPersonSet=Đặt theo nhân sự
att_autoExport_byDeptSet=Đặt theo bộ phận
att_autoExport_byAreaSet=Đặt theo khu vực
att_autoExport_emailSubjectSet=Cài đặt tiêu đề
att_autoExport_emailContentSet=Thiết lập nội dung
att_autoExport_timePointAlert=Vui lòng chọn thời điểm gửi chính xác.
att_autoExport_lastDayofMonth=Ngày cuối tháng
att_autoExport_firstDayofMonth=Ngày đầu tháng
att_autoExport_dayofMonthCheck=Ngày cụ thể
att_autoExport_dayofMonthCheckAlert=Vui lòng chọn ngày cụ thể.
att_autoExport_chooseDeptAlert=Vui lòng chọn Bộ phận!
att_autoExport_sendFormatSet=Cài đặt chế độ gửi
att_autoExport_sendFormat=Chế độ gửi
att_autoExport_mailFormat=Phương thức gửi hộp thư
att_autoExport_ftpFormat=Phương thức gửi FTP
att_autoExport_sftpFormat=Phương thức gửi SFTP
att_autoExport_ftpUrl=Địa chỉ máy chủ FTP
att_autoExport_ftpPort=Cổng máy chủ FTP
att_autoExport_ftpTimeSet=Cài đặt thời gian gửi FTP
att_autoExport_ftpParamSet=Cài đặt tham số FTP
att_autoExport_ftpUsername=Tên người dùng FTP
att_autoExport_ftpPassword=Mật khẩu FTP
att_autoExport_correctFtpParam=Vui lòng điền chính xác các tham số ftp
att_autoExport_correctFtpTestParam=Vui lòng kiểm tra kết nối để đảm bảo rằng giao tiếp là bình thường
att_autoExport_inputFtpUrl=Vui lòng nhập địa chỉ máy chủ
att_autoExport_inputFtpPort=Vui lòng nhập cổng máy chủ
att_autoExport_ftpSuccess=Kết nối thành công
att_autoExport_ftpFail=Vui lòng kiểm tra nếu phần cài đặt tham số không chính xác.
att_autoExport_validFtp=Vui lòng nhập địa chỉ máy chủ hợp lệ
att_autoExport_validPort=Vui lòng nhập một cổng máy chủ hợp lệ
att_autoExport_selectExcelTip=Loại tệp chọn EXCEL, định dạng nội dung là tất cả các trường!
#=====================================================================
#时间段
att_timeSlot_periodType=Kiểu Thời khóa biểu
att_timeSlot_normalTime=Thời khóa biểu bình thường
att_timeSlot_elasticTime=Thời khóa biểu linh hoạt
att_timeSlot_startSignInTime=Thời gian bắt đầu chấm vào
att_timeSlot_toWorkTime=Thời gian chấm vào
att_timeSlot_endSignInTime=Kết thúc thời gian chấm vào
att_timeSlot_allowLateMinutes=Cho phép trễ (phút)
att_timeSlot_isMustSignIn=Phải chấm vào
att_timeSlot_startSignOffTime=Thời gian bắt đầu chấm ra
att_timeSlot_offWorkTime=Thời gian chấm ra
att_timeSlot_endSignOffTime=Thời gian kết thúc chấm ra
att_timeSlot_allowEarlyMinutes=Cho phép nghỉ sớm (phút)
att_timeSlot_isMustSignOff=Phải chấm ra
att_timeSlot_workingHours=Thời gian làm việc (phút)
att_timeSlot_isSegmentDeduction=Thời gian nghỉ tự động
att_timeSlot_startSegmentTime=Thời gian bắt đầu
att_timeSlot_endSegmentTime=Thời gian kết thúc
att_timeSlot_interSegmentDeduction=Thời gian khấu trừ (phút)
att_timeSlot_markWorkingDays=Ngày làm việc
att_timeSlot_isAdvanceCountOvertime=Tự động OT (Chấm vào sớm)
att_timeSlot_signInAdvanceTime=Thời gian kết thúc tự động OT (Chấm vào)
att_timeSlot_isPostponeCountOvertime=Tự động OT (Trì hoãn chấm ra)
att_timeSlot_signOutPosponeTime=Thời gian bắt đầu tự động OT (Chấm ra)
att_timeSlot_isCountOvertime=Được tính là làm thêm giờ
att_timeSlot_timeSlotLong=Giờ làm việc phải đáp ứng khoảng thời gian chấm công được xác định bởi các quy tắc:
att_timeSlot_alertStartSignInTime=Thời gian bắt đầu chấm phải nhỏ hơn thời gian chấm vào.
att_timeSlot_alertEndSignInTime=Thời gian kết thúc chấm vào phải lớn hơn thời gian chấm vào.
att_timeSlot_alertStartSignInAndEndSignIn=Thời gian bắt đầu chấm ra phải nhỏ hơn thời gian chấm ra.
att_timeSlot_alertStartSignOffTime=Thời gian bắt đầu làm thêm giờ không thể nhỏ hơn thời gian chấm ra.
att_timeSlot_alertEndSignOffTime=Thời gian bắt đầu làm thêm giờ không thể lớn hơn thời gian kết thúc chấm ra.
att_timeSlot_alertStartUnequalEnd=Ngày làm việc không thể nhỏ hơn 0.
att_timeSlot_alertStartSegmentTime=Thời gian khấu trừ không thể nhỏ hơn 0.
att_timeSlot_alertStartAndEndTime=Thời gian bắt đầu chấm ra không thể bằng thời gian kết thúc chấm vào
att_timeSlot_alertEndAndoffWorkTime=Số giờ không thể lớn hơn 23.
att_timeSlot_alertToWorkLesserAndEndTimeLesser=Số phút không thể lớn hơn 59.
att_timeSlot_alertLessSignInAdvanceTime=Thời gian chấm vào sớm nên ít hơn thời gian bắt đầu công việc
att_timeSlot_alertMoreSignInAdvanceTime=Số phút làm thêm giờ trước khi 'đi làm' ít hơn số phút trước khi 'làm việc'
att_timeSlot_alertMoreSignOutPosponeTime=mô tả bài đăng 'ngoài giờ làm việc' ít hơn số phút 'sau khi làm việc'
att_timeSlot_alertLessSignOutPosponeTime=Thời gian chấm vào trễ nên lớn hơn thời gian kết thúc công việc
att_timeSlot_time=Vui lòng nhập định dạng thời gian chính xác.
att_timeSlot_alertMarkWorkingDays=Hãy nhớ rằng số ngày làm việc không thể để trống!
att_timeSlot_placeholderNo=Nên bắt đầu với T, chẳng hạn như T01.
att_timeSlot_placeholderName=Nên bắt đầu bằng T hoặc kết thúc bằng thời gian biểu.
att_timeSlot_beforeToWork=Trước khi làm việc
att_timeSlot_afterToWork=Sau khi làm việc
att_timeSlot_beforeOffWork=Trước khi làm việc
att_timeSlot_afterOffWork=Sau khi làm việc
att_timeSlot_minutesSignInValid=Kiểm tra trong vòng vài phút
att_timeSlot_toWork=Làm việc
att_timeSlot_offWork=Đi làm
att_timeSlot_minutesSignInAsOvertime=Đăng ký làm thêm giờ vài phút trước
att_timeSlot_minutesSignOutAsOvertime=Bắt đầu làm thêm giờ sau một phút
att_timeSlot_minOvertimeMinutes=Số phút làm thêm giờ ngắn nhất
att_timeSlot_enableWorkingHours=Mở giờ làm việc không?
att_timeSlot_eidtTimeSlot=Chỉnh sửa thời gian
att_timeSlot_browseBreakTime=Duyệt thời gian nghỉ ngơi
att_timeSlot_addBreakTime=Thêm phần giờ nghỉ
att_timeSlot_enableFlexibleWork=Cho phép làm việc linh hoạt
att_timeSlot_advanceWorkMinutes=Có thể đi làm trước
att_timeSlot_delayedWorkMinutes=Có thể hoãn công việc
att_timeSlot_advanceWorkMinutesValidMsg1=Số phút trước khi đi làm lớn hơn số phút Có thể đi làm trước
att_timeSlot_advanceWorkMinutesValidMsg2=Số phút Có thể đi làm trước ít hơn số phút trước khi đi làm
att_timeSlot_advanceWorkMinutesValidMsg3=Số phút có thể làm việc trước ít hơn hoặc bằng số phút trước khi đăng nhập để làm thêm giờ.
att_timeSlot_advanceWorkMinutesValidMsg4=Số phút có thể đăng nhập để làm thêm giờ lớn hơn hoặc bằng số phút trước khi làm việc trước.
att_timeSlot_delayedWorkMinutesValidMsg1=Số phút sau khi làm việc lớn hơn số phút có thể bị trì hoãn để làm việc.
att_timeSlot_delayedWorkMinutesValidMsg2=Số phút có thể hoãn lại từ công việc ít hơn số phút sau sau khi làm việc
att_timeSlot_delayedWorkMinutesValidMsg3=Số phút có thể được lên lịch để làm việc nhỏ hơn hoặc bằng số phút sau khi làm việc, đăng xuất và bắt đầu làm thêm giờ
att_timeSlot_delayedWorkMinutesValidMsg4=Số phút sau khi đăng xuất và bắt đầu làm việc sau khi làm việc nên lớn hơn hoặc bằng số phút có thể bị hoãn để làm việc.
att_timeSlot_allowLateMinutesValidMsg1=Số phút được phép trễ là ít hơn số phút sau khi làm việc
att_timeSlot_allowLateMinutesValidMsg2=Số phút sau sau khi làm việc lớn hơn số phút cho phép phút bị trễ
att_timeSlot_allowEarlyMinutesValidMsg1=Cho phép phút sớm ít hơn phút trước khi làm việc
att_timeSlot_allowEarlyMinutesValidMsg2=Số phút trước trước khi làm việc lớn hơn số phút được phép còn lại sớm
att_timeSlot_timeOverlap=Thời gian nghỉ chồng chéo, vui lòng sửa đổi khoảng thời gian nghỉ!
att_timeSlot_atLeastOne=Ít nhất 1 khoảng thời gian nghỉ ngơi!
att_timeSlot_mostThree=Lên đến 3 kỳ nghỉ!
att_timeSlot_canNotEqual=Thời gian bắt đầu của thời gian nghỉ ngơi không thể bằng thời gian kết thúc!
att_timeSlot_shoudInWorkTime=Hãy chắc chắn rằng thời gian nghỉ ngơi là trong giờ làm việc!
att_timeSlot_repeatBreakTime=Lặp lại thời gian nghỉ ngơi!
att_timeSlot_toWorkLe=Thời gian làm việc nhỏ hơn thời gian bắt đầu tối thiểu của khoảng thời gian nghỉ ngơi đã chọn:
att_timeSlot_offWorkGe=Giờ làm việc lớn hơn thời gian kết thúc tối đa của khoảng thời gian nghỉ ngơi đã chọn:
att_timeSlot_crossDays_toWork=Thời gian bắt đầu tối thiểu cho khoảng thời gian nghỉ là trong khoảng thời gian:
att_timeSlot_crossDays_offWork=Thời gian kết thúc tối đa của khoảng thời gian nghỉ là trong khoảng thời gian:
att_timeSlot_allowLateMinutesRemark=Từ thời gian làm việc đến thẻ phút trễ cho phép để tính thẻ làm việc bình thường
att_timeSlot_allowEarlyMinutesRemark=Bắt đầu sớm từ thời gian nghỉ việc với số phút được phép về sớm, thẻ nghỉ việc bình thường
att_timeSlot_isSegmentDeductionRemark=Xóa khoảng thời gian nghỉ ngơi trong khoảng thời gian
att_timeSlot_attEnableFlexibleWorkRemark1=Công việc linh hoạt không được phép đặt số lần khởi hành muộn, sớm
att_timeSlot_afterToWorkRemark=phút Sau giờ làm việc bằng với số phút Trì hoãn để làm việc
att_timeSlot_beforeOffWorkRemark=Phút Trước khi làm việc bằng phút có thể đi làm
att_timeSlot_attEnableFlexibleWorkRemark2=Số phút sau sau khi làm việc lớn hơn hoặc bằng giờ nghỉ thời gian làm việc bị trì hoãn
att_timeSlot_attEnableFlexibleWorkRemark3=Phút Bạn có thể làm việc trước, phải nhỏ hơn hoặc bằng phút N làm việc để làm thêm giờ
att_timeSlot_attEnableFlexibleWorkRemark4=Phút Trì hoãn để làm việc, phải nhỏ hơn hoặc bằng N phút nghỉ việc
att_timeSlot_attBeforeToWorkAsOvertimeRemark=Ví dụ: 9:00 lớp, đăng nhập để làm thêm 60 phút trước khi làm việc, sau đó đăng ký trước 8 giờ đến 8 giờ làm việc ngoài giờ
att_timeSlot_attAfterOffWorkAsOvertimeRemark=Ví dụ: Sau 18 giờ làm việc, sau 60 phút làm việc, đánh dấu rút tiền và làm thêm giờ, sau đó bắt đầu làm thêm giờ từ 19 giờ đến giờ chấm ra.
att_timeSlot_longTimeValidRemark=Thời gian đánh dấu hiệu quả của nhóm sau khi làm việc và thời gian đánh dấu hiệu quả của trước khi làm việc, không bị chồng chéo trong khoảng thời gian!
att_timeSlot_advanceWorkMinutesValidMsg5=Số phút hợp lệ trước khi nhận phòng phải lớn hơn số phút có thể làm việc trước
att_timeSlot_advanceWorkMinutesValidMsg6=Số phút bạn có thể đi làm trước ít hơn số phút hợp lệ trước khi nhận phòng
att_timeSlot_delayedWorkMinutesValidMsg5=Số phút hợp lệ sau khi đăng ký lớn hơn số phút có thể bị hoãn để làm việc
att_timeSlot_delayedWorkMinutesValidMsg6=Số phút có thể bị hoãn để làm việc ít hơn số phút hợp lệ sau khi đăng nhập
att_timeSlot_advanceWorkMinutesValidMsg7=Thời gian nhận phòng trước khi làm việc không được trùng lặp với thời gian trả phòng sau giờ làm việc ngày hôm trước
att_timeSlot_delayedWorkMinutesValidMsg7=Thời gian trả phòng sau giờ làm việc không được trùng với thời gian nhận phòng trước giờ làm việc vào ngày hôm sau
att_timeSlot_maxOvertimeMinutes=Giới hạn số giờ làm thêm tối đa
#班次
att_shift_basicSet=Loại lịch trình
att_shift_advancedSet=Tên lịch trình
att_shift_type=Loại ca
att_shift_name=Tên ca
att_shift_regularShift=Ca thường xuyên
att_shift_flexibleShift=Ca linh hoạt
att_shift_color=Màu
att_shift_periodicUnit=Đơn vị
att_shift_periodNumber=Chu kỳ
att_shift_startDate=Ngày bắt đầu
att_shift_startDate_firstDay=Ngày bắt đầu chu kỳ
att_shift_isShiftWithinMonth=Chu kỳ ca trong một tháng
att_shift_attendanceMode=Chế độ chấm công
att_shift_shiftNormal=Chấm thẻ theo ca thường
att_shift_oneDayOneCard=Chấm một lần bất cứ lúc nào trong ngày
att_shift_onlyBrushTime=Chỉ tính thời gian chấm thẻ
att_shift_notBrushCard=Chấm tự do
att_shift_overtimeMode=Chế độ làm thêm
att_shift_autoCalc=Máy tính tự động
att_shift_mustApply=Làm thêm giờ phải đăng ký
att_shift_mustOvertime=Phải làm thêm giờ hoặc nghỉ việc
att_shift_timeSmaller=Thời gian ngắn hơn giữa tính toán tự động và nhận ngoài giờ
att_shift_notOvertime=Không được tính là làm thêm giờ
att_shift_overtimeSign=Loại làm thêm giờ
att_shift_normal=Ngày bình thường
att_shift_restday=Ngày nghỉ
att_shift_timeSlotDetail=Chi tiết thời gian biểu
att_shift_doubleDeleteTimeSlot=Bấm đúp vào chu kỳ thời gian; bạn có thể xóa khoảng thời gian
att_shift_addTimeSlot=Thêm thời gian biểu
att_shift_cleanTimeSlot=Xóa thời gian biểu
att_shift_NO=No.
att_shift_notAll=Bỏ chọn tất cả
att_shift_notTime=Nếu hộp kiểm chi tiết thời gian biểu không thể được chọn, nó chỉ ra rằng có sự trùng lặp trong thời gian biểu.
att_shift_notExistTime=Thời gian biểu này không tồn tại.
att_shift_cleanAllTimeSlot=Bạn có chắc chắn muốn xóa thời gian biểu cho ca đã chọn không?
att_shift_pleaseCheckBox=Vui lòng chọn hộp kiểm tra ở bên trái giống với thời gian hiển thị hiện tại ở bên phải.
att_shift_pleaseUnit=Vui lòng điền vào các đơn vị chu kỳ và số lượng chu kỳ.
att_shift_pleaseAllDetailTimeSlot=Vui lòng chọn chi tiết thời gian biểu.
att_shift_placeholderNo=Nên bắt đầu với S, chẳng hạn như S0.
att_shift_placeholderName=Nên bắt đầu bằng S hoặc kết thúc bằng ca.
att_shift_workType=Loại công việc
att_shift_normalWork=Làm việc bình thường
att_shift_holidayOt=Nghỉ lễ ngoài giờ
att_shift_attShiftStartDateRemark=Ví dụ: Ngày bắt đầu của chu kỳ là Số 22, với thời gian là ba ngày, sau đó Số 22/23/24 thuộc Lớp A / B / C và Số 19/20/21 là trên lớp A / Lớp B / lớp C, trước và sau ngày và v.v.
att_shift_isShiftWithinMonthRemark1=Ca trong tháng, chu kỳ chỉ chu kỳ đến ngày cuối cùng của mỗi tháng, không được lên lịch liên tục trong tháng;
att_shift_isShiftWithinMonthRemark2=Ca làm việc không theo tháng, chu kỳ được chuyển sang ngày cuối cùng của mỗi tháng, nếu một chu kỳ chưa kết thúc, tiếp tục sang tháng tiếp theo, v.v.
att_shift_workTypeRemark1=Lưu ý: Khi loại công việc được chọn là làm thêm vào ngày nghỉ, việc tham dự sẽ không được tính vào ngày nghỉ.
att_shift_workTypeRemark2=Làm thêm giờ vào cuối tuần, mặc định đánh dấu giờ làm thêm vào ngày nghỉ và máy tính sẽ tự động tính thời gian làm thêm. Không yêu cầu phải làm thêm giờ.
att_shift_workTypeRemark3=Nghỉ lễ ngoài giờ, đánh dấu làm thêm giờ vào ngày nghỉ và máy tính tự động tính toán thời gian làm thêm, không yêu cầu phải làm thêm giờ và giờ làm việc trong ngày được ghi là giờ làm thêm;
att_shift_attendanceModeRemark1=Ngoại trừ thường vuốt theo ca, nó không được coi là sớm hoặc trễ quá giờ, ví dụ:
att_shift_attendanceModeRemark2=1.Không cần đăng ký hoặc sử dụng thẻ hợp lệ một lần trong ngày, không tính thời gian làm thêm giờ;
att_shift_attendanceModeRemark3=2.Loại công việc: công việc bình thường, chế độ chấm công: quẹt thẻ miễn phí, sau đó thời gian thay đổi ngày được coi là thời gian làm việc hiệu quả;
att_shift_apseStartMode=loại bắt đầu giai đoạn
att_shift_apseStartModeByPeriod=ngày bắt đầu
att_shift_apseStartModeBySch=Ngày bắt đầu thay đổi
att_shift_addTimeSlotToShift=Có thêm khoảng thời gian của ca này không
#=====================================================================
#分组
att_group_editGroup=Chỉnh sửa nhân sự cho nhóm
att_group_browseGroupPerson=Duyệt nhân sự nhóm
att_group_list=Danh sách nhóm
att_group_placeholderNo=Nên bắt đầu với G, chẳng hạn như G1
att_group_placeholderName=Nên bắt đầu bằng G hoặc kết thúc bằng nhóm.
att_widget_deptHint=Lưu ý: Nhập tất cả nhân sự theo bộ phận đã chọn
att_widget_searchType=Truy vấn có điều kiện
att_widget_noPerson=Không chọn ai
#分组排班
#部门排班
att_deptSch_existsDept=Có ca của bộ phận, không được phép xóa bộ phận
#人员排班
att_personSch_view=Xem lịch trình nhân sự
#临时排班
att_schedule_type=Loại lịch trình
att_schedule_tempType=Loại tạm thời
att_schedule_normal=Lịch trình bình thường
att_schedule_intelligent=Tìm lớp thông minh
att_tempSch_scheduleType=Loại lịch trình
att_tempSch_startDate=Ngày bắt đầu
att_tempSch_endDate=Ngày kết thúc
att_tempSch_attendanceMode=Phương pháp chấm công
att_tempSch_overtimeMode=Chế độ làm thêm giờ
att_tempSch_overtimeRemark=Đánh dấu giờ làm thêm
att_tempSch_existsDept=Có ca tạm thời cho bộ phận, không được xóa bộ phận
att_schedult_opAddTempSch=Ca tạm thời mới
att_schedule_cleanEndDate=Thời gian kết thúc trống
att_schedule_selectOne=Lịch trình bình thường chỉ có thể chọn một ca!
att_schedule_selectPerson=Vui lòng chọn nhân sự trước!
att_schedule_selectDept=Vui lòng chọn bộ phận trước!
att_schedule_selectGroup=Vui lòng chọn nhóm trước!
att_schedule_selectOneGroup=Chỉ có một nhóm có thể được chọn!
att_schedule_arrange=Vui lòng chọn một ca!
att_schedule_leave=Nghỉ phép
att_schedule_trip=Công tác
att_schedule_out=Ra ngoài
att_schedule_off=Nghỉ
att_schedule_makeUpClass=Bổ sung
att_schedule_class=Chỉnh sửa
att_schedule_holiday=Ngày lễ
att_schedule_offDetail=Chỉnh sửa ngày nghỉ
att_schedule_makeUpClassDetail=Bổ sung chấm công
att_schedule_classDetail=Điều chỉnh ca
att_schedule_holidayDetail=Ngày lễ
att_schedule_noSchDetail=Không theo lịch trình
att_schedule_normalDetail=Bình thường
att_schedule_normalSchInfo=Ca giữa: Không có ca chéo
att_schedule_multipleInterSchInfo=Ca được phân tách bằng dấu phẩy nếu nhiều ca làm việc trong nhiều ngày
att_schedule_inderSchFirstDayInfo=Chuyển qua phần bù lưới: ca làm việc chéo được ghi lại như ngày đầu tiên
att_schedule_inderSchSecondDayInfo=Chuyển qua lưới chuyển tiếp bù: ca làm việc chéo được ghi lại như ngày thứ hai
att_schedule_timeConflict=Xung đột với khoảng thời gian thay đổi hiện tại, không được phép lưu!
#=====================================================================
att_excp_notExisetPerson=Người không tồn tại!
att_excp_leavePerson=Nhân viên nghỉ việc!
#补签单
att_sign_signTime=Thời gian chấm
att_sign_signDate=Ngày chấm
#请假
att_leave_arilName=Loại nghỉ phép
att_leave_image=Để lại hình ảnh yêu cầu
att_leave_imageShow=Không có hình ảnh
att_leave_imageType=Mẹo sai: Định dạng hình ảnh không chính xác, hỗ trợ định dạng: JPEG, GIF, PNG!
att_leave_imageSize=Mẹo sai: Ảnh đã chọn quá lớn, kích thước tối đa của ảnh là 4MB!
att_leave_leaveLongDay=Thời lượng (ngày)
att_leave_leaveLongHour=Thời lượng (giờ)
att_leave_leaveLongMinute=Thời lượng (phút)
att_leave_endNoLessAndEqualStart=Thời gian kết thúc không thể nhỏ hơn hoặc bằng thời gian bắt đầu
att_leave_typeNameNoExsists=Tên lớp giả không tồn tại
att_leave_startNotNull=Thời gian bắt đầu không thể để trống
att_leave_endNotNull=Thời gian kết thúc không thể để trống
att_leave_typeNameConflict=Tên loại giả xung đột với tên trạng thái tham dự
#出差
att_trip_tripLongDay=Độ dài của chuyến đi(ngày)
att_trip_tripLongMinute=Độ dài của chuyến đi(phút)
att_trip_tripLongHour=Thời gian đi lại (giờ)
#外出
att_out_outLongDay=Thời gian ra(ngày)
att_out_outLongMinute=Thời gian ra(phút)
att_out_outLongHour=Thời gian sử dụng (thời gian)
#加班
att_overtime_type=Loại OT
att_overtime_normal=OT bình thường
att_overtime_rest=OT theo tuần
att_overtime_overtimeLong=Thời lượng làm thêm giờ(phút)
att_overtime_overtimeHour=Giờ làm thêm (giờ)
att_overtime_notice=Thời gian ứng dụng ngoài giờ không được phép quá một ngày!
att_overtime_minutesNotice=Thời gian nộp đơn ngoài giờ không thể ít hơn thời gian làm thêm tối thiểu!
#调休补班
att_adjust_type=Loại điều chỉnh
att_adjust_adjustDate=Ngày điều chỉnh
att_adjust_shiftName=Bổ sung ca chấm công
att_adjust_selectClass=Vui lòng chọn tên ca cần bổ sung chấm công.
att_shift_notExistShiftWorkDate={1} lập lịch thay đổi tại {0} và phần còn lại không được phép áp dụng cho ca trang điểm!
att_adjust_shiftPeriodStartMode=Ca làm việc được chọn cho ca trang điểm, nếu ngày bắt đầu là theo ca, mặc định là 0
att_adjust_shiftNameNoNull=Ca trang điểm không thể để trống
att_adjust_shiftNameNoExsist=Thay đổi trang điểm không tồn tại
#调班
att_class_type=Loại điều chỉnh
att_class_sameTimeMoveShift=Điều chỉnh ca cá nhân trong cùng một ngày
att_class_differenceTimeMoveShift=Điều chỉnh ca cá nhân trong những ngày khác
att_class_twoPeopleMove=Trao đổi hai người
att_class_moveDate=Ngày điều chỉnh
att_class_shiftName=Tên lịch trình ban đầu
att_class_moveShiftName=Ca điều chỉnh mới không thể để trống.
att_class_movePersonPin=Điều chỉnh ID nhân sự
att_class_movePersonName=Điều chỉnh tên nhân sự
att_class_movePersonLastName=Điều chỉnh Họ nhân sự
att_class_moveDeptName=Điều chỉnh tên bộ phận
att_class_personPin=ID nhân sự
att_class_shiftNameNoNull=Ca điều chỉnh mới không thể để trống.
att_class_personPinNoNull=ID nhân sự của người mới không thể để trống!
att_class_isNotExisetSwapPersonPin=Người điều chỉnh mới không tồn tại, vui lòng thêm lại!
att_class_personNoSame=Bạn không thể điều chỉnh cho cùng một người, vui lòng thử lại.
att_class_outTime=Ngày điều chỉnh và ngày chuyển không thể quá một tháng
att_class_shiftNameNoExsist=Sự thay đổi điều chỉnh không tồn tại
att_class_swapPersonNoExisist=Người mai mối không tồn tại
att_class_dateNoSame=Các cá nhân được chuyển vào các ngày khác nhau, ngày không thể giống nhau
#=====================================================================
#节点
att_node_name=Nút
att_node_type=Loại nút
att_node_leader=Lệnh dẫn
att_node_leaderNode=Nút dẫn lệnh trực tiếp
att_node_person=Người được chỉ định
att_node_position=Vị trí chỉ định
att_node_choose=Chọn vị trí
att_node_personNoNull=Nhân viên không thể để trống
att_node_posiitonNoNull=Vị trí không thể để trống
att_node_placeholderNo=Nên bắt đầu với N, chẳng hạn như N01.
att_node_placeholderName=Bạn nên bắt đầu bằng một vị trí hoặc tên, kết thúc bằng một nút, chẳng hạn như Nút quản lý.
att_node_searchPerson=Tiêu chí tìm kiếm đầu vào
att_node_positionIsExist=Vị trí đã tồn tại trong dữ liệu nút, vui lòng chọn lại vị trí.
#流程
att_flow_type=Loại lưu lượng
att_flow_rule=Quy tắc lưu lượng
att_flow_rule0=Ít hơn hoặc bằng 1 ngày
att_flow_rule1=Hơn 1 ngày và ít hơn hoặc bằng 3 ngày
att_flow_rule2=Hơn 3 ngày và ít hơn hoặc bằng 7 ngày
att_flow_rule3=Hơn 7 ngày
att_flow_node=Nút phê duyệt
att_flow_start=Bắt đầu lưu lượng
att_flow_end=Kết thúc lưu lượng
att_flow_addNode=Thêm nút
att_flow_placeholderNo=Nên bắt đầu với F, chẳng hạn như F01.
att_flow_placeholderName=Nên bắt đầu bằng loại, kết thúc bằng luồng, ví dụ: Luồng nghỉ phép.
att_flow_tips=Lưu ý: Thứ tự phê duyệt của các nút là từ trên xuống dưới và bạn có thể kéo sắp xếp sau khi chọn.
#申请
att_apply_personPin=Áp dụng ID nhân sự
att_apply_type=Loại ngoại lệ
att_apply_flowStatus=Lưu lượng trạng thái tổng thể
att_apply_start=Bắt đầu một ứng dụng
att_apply_flowing=Đang chờ xử lý
att_apply_pass=Thông qua
att_apply_over=Kết thúc
att_apply_refuse=Từ chối
att_apply_revoke=Hủy bỏ
att_apply_except=Ngoại lệ
att_apply_view=Xem chi tiết
att_apply_leaveTips=Người có yêu cầu nghỉ phép trong khoảng thời gian này!
att_apply_tripTips=Nhân sự có chuyến công tác trong khoảng thời gian này!
att_apply_outTips=Nhân sự đã nộp đơn ra ngoài trong khoảng thời gian này!
att_apply_overtimeTips=Nhân sự làm thêm giờ trong khoảng thời gian này!
att_apply_adjustTips=Trong thời gian này, các nhân viên phải đăng ký cho buổi diễn tập!
att_apply_classTips=Nhân sự có một ca trong khoảng thời gian này!
#审批
att_approve_wait=Chờ phê duyệt
att_approve_refuse=Không được thông qua
att_approve_reason=Lý do
att_approve_personPin=ID người phê duyệt
att_approve_personName=Tên người phê duyệt
att_approve_person=Người phê duyệt
att_approve_isPass=Có chấp thuận?
att_approve_status=Trạng thái nút hiện tại
att_approve_tips=Điểm thời gian đã tồn tại trong luồng và không thể lặp lại.
att_approve_tips2=Nút luồng chưa được cấu hình, vui lòng liên hệ với quản trị viên để cấu hình.
att_approve_offDayConflicts={0} không được lên lịch hoặc lên lịch vào {1} và phần còn lại không được phép.
att_approve_shiftConflicts={0} đã có sự thay đổi trong {1} và không cho phép áp dụng cho ca làm việc trong ngày làm việc!
att_approve_shiftNoSch={0} Không cho phép ứng dụng thay đổi khi sự thay đổi không được lên lịch vào {1}!
att_approve_classConflicts=Không có ứng dụng thay đổi được cho phép vào ngày đột xuất
att_approve_selectTime=Thời gian lựa chọn sẽ xác định quá trình theo các quy tắc
att_approve_withoutPermissionApproval=Có một quy trình làm việc chưa được phép phê duyệt, vui lòng kiểm tra!
#=====================================================================
#考勤计算
att_op_calculation=Tính toán chấm công
att_op_calculation_notice=Dữ liệu chấm công đã được tính toán trong nền, vui lòng thử lại sau!
att_op_calculation_leave=Bao gồm cả nhân viên từ chức
att_statistical_choosePersonOrDept=Vui lòng chọn Bộ phận hoặc Nhân sự!
att_statistical_sureCalculation=Bạn có chắc chắn muốn thực hiện tính toán chấm công?
att_statistical_filter=Điều kiện lọc đã sẵn sàng!
att_statistical_initData=Khởi tạo cơ sở dữ liệu hoàn tất!
att_statistical_exception=Khởi tạo dữ liệu ngoại lệ hoàn tất!
att_statistical_error=Tính toán vắng mặt thất bại!
att_statistical_begin=bắt đầu tính toán!
att_statistical_end=Kết thúc tính toán!
att_statistical_noticeTime=Tham dự phạm vi thời gian tùy chọn: hai tháng đầu tiên cho đến ngày!
att_statistical_remarkHoliday=H
att_statistical_remarkClass=C
att_statistical_remarkNoSch=NS
att_statistical_remarkRest=R
#原始记录表
att_op_importAccRecord=Nhập bản ghi kiểm soát truy cập
att_op_importParkRecord=Nhập bản ghi đỗ xe
att_op_importInsRecord=Nhập bản ghi facekiosk
att_op_importPidRecord=Nhập bản ghi chứng nhận nhân sự
att_op_importVmsRecord=Nhập dữ liệu video
att_op_importUSBRecord=Nhập bản ghi đĩa U
att_transaction_noAccModule=Không có mô-đun kiểm soát truy cập!
att_transaction_noParkModule=Không có mô-đun đỗ xe!
att_transaction_noInsModule=Không có mô-đun facekiosk!
att_transaction_noPidModule=Không có một mô-đun chứng nhận nhân sự!
att_transaction_exportRecord=Xuất hồ sơ gốc
att_transaction_exportAttPhoto=Xuất ảnh tham dự
att_transaction_fileIsTooLarge=Tệp đã xuất quá lớn, vui lòng thu hẹp phạm vi ngày
att_transaction_exportDate=Ngày xuất
att_statistical_attDatetime=thời gian tham dự
att_statistical_attPhoto=Ảnh chấm công
att_statistical_attDetail=Chi tiết chấm công
att_statistical_acc=Thiết bị kiểm soát truy cập
att_statistical_att=Thiết bị chấm công
att_statistical_park=LPR Camera
att_statistical_faceRecognition=Thiết bị nhận dạng khuôn mặt
att_statistical_app=Thiết bị di động
att_statistical_vms=Thiết bị video
att_statistical_psg=Thiết bị kênh
att_statistical_dataSources=Nguồn dữ liệu
att_transaction_SyncRecord=Đồng bộ hóa hồ sơ tham dự
#日打卡详情表
att_statistical_dayCardDetail=Chi tiết đăng ký
att_statistical_cardDate=Ngày ghi
att_statistical_cardNumber=Thời gian ghi
att_statistical_earliestTime=Thời gian sớm nhất
att_statistical_latestTime=Thời gian trễ nhất
att_statistical_cardTime=Thời gian chấm
#请假汇总表
att_statistical_leaveDetail=Chi tiết nghỉ phép
#日明细报表
att_statistical_attDate=Ngày chấm công
att_statistical_week=Tuần
att_statistical_shiftInfo=Thông tin ca
att_statistical_shiftTimeData=Thời gian làm việc/nghỉ
att_statistical_cardValidData=Thời gian chấm
att_statistical_cardValidCount=Số lượng chấm
att_statistical_lateCount=Đếm muộn
att_statistical_lateMinute=Phút muộn
att_statistical_earlyCount=Đếm sớm
att_statistical_earlyMinute=Phút đến sớm
att_statistical_countData=Dữ liệu thời gian
att_statistical_minuteData=Dữ liệu phút
att_statistical_attendance_minute=Chấm công (phút)
att_statistical_overtime_minute=Làm thêm giờ (phút)
att_statistical_unusual_minute=Bất thường (phút)
#月明细报表
att_monthdetail_should_hour=Nên (thời gian)
att_monthdetail_actual_hour=Thực tế (thời gian)
att_monthdetail_valid_hour=Hợp lệ (thời gian)
att_monthdetail_absent_hour=Hoàn thành (giờ)
att_monthdetail_leave_hour=Nghỉ phép (thời gian)
att_monthdetail_trip_hour=Công tác (giờ)
att_monthdetail_out_hour=Ra ngoài (giờ)
att_monthdetail_should_day=Nên (ngày)
att_monthdetail_actual_day=Thực tế (ngày)
att_monthdetail_valid_day=Hợp lệ (ngày)
att_monthdetail_absent_day=Hoàn thành (ngày)
att_monthdetail_leave_day=Nghỉ phép(ngày)
att_monthdetail_trip_day=Công tác (ngày)
att_monthdetail_out_day=Ra ngoài(ngày)
#月统计报表
att_statistical_late_minute=Thời lượng (phút)
att_statistical_early_minute=Thời lượng (phút)
#部门统计报表
#年度统计报表
att_statistical_should=Nên
att_statistical_actual=Thực tế
att_statistical_valid=Có hiệu lực
att_statistical_numberOfTimes=thời gian
att_statistical_usually=Các ngày trong tuần
att_statistical_rest=Ngày cuối tuần
att_statistical_holiday=Ngày lễ
att_statistical_total=Toàn bộ
att_statistical_month=Thống kê tháng
att_statistical_year=Thống kê năm
att_statistical_attendance_hour=Có mặt(giờ)
att_statistical_attendance_day=Có mặt(ngày)
att_statistical_overtime_hour=Tăng ca(giờ)
att_statistical_unusual_hour=Ngoại lệ (thời gian)
att_statistical_unusual_day=Bất thường (ngày)
#考勤设备参数
att_deviceOption_query=Xem thông số thiết bị
att_deviceOption_noOption=Không có thông tin tham số, vui lòng lấy thông số thiết bị trước
att_deviceOption_name=Tên tham số
att_deviceOption_value=Giá trị tham số
att_deviceOption_UserCount=Số người dùng
att_deviceOption_MaxUserCount=Số lượng người dùng tối đa
att_deviceOption_FaceCount=Số mẫu khuôn mặt hiện tại
att_deviceOption_MaxFaceCount=Số lượng mẫu mặt tối đa
att_deviceOption_FacePhotoCount=Số lượng hình ảnh khuôn mặt hiện tại
att_deviceOption_MaxFacePhotoCount=Số lượng hình ảnh khuôn mặt tối đa
att_deviceOption_FingerCount=Số mẫu vân tay hiện tại
att_deviceOption_MaxFingerCount=Số lượng mẫu vân tay tối đa
att_deviceOption_FingerPhotoCount=Số lượng hình ảnh dấu vân tay hiện tại
att_deviceOption_MaxFingerPhotoCount=Số lượng hình ảnh vân tay tối đa
att_deviceOption_FvCount=Số mẫu vân tay hiện tại
att_deviceOption_MaxFvCount=Số lượng mẫu vân tay tối đa
att_deviceOption_FvPhotoCount=Số lượng hình ảnh tĩnh mạch ngón tay hiện tại
att_deviceOption_MaxFvPhotoCount=Số lượng hình ảnh tĩnh mạch ngón tay
att_deviceOption_PvCount=Số mẫu cọ hiện tại
att_deviceOption_MaxPvCount=Số lượng mẫu cọ tối đa
att_deviceOption_PvPhotoCount=Hình ảnh lòng bàn tay hiện tại
att_deviceOption_MaxPvPhotoCount=Số lượng hình ảnh lòng bàn tay tối đa
att_deviceOption_TransactionCount=Số lượng bản ghi hiện tại
att_deviceOption_MaxAttLogCount=Số lượng bản ghi tối đa
att_deviceOption_UserPhotoCount=Ảnh người dùng hiện tại
att_deviceOption_MaxUserPhotoCount=Số lượng ảnh người dùng tối đa
att_deviceOption_FaceVersion=Phiên bản thuật toán nhận dạng khuôn mặt
att_deviceOption_FPVersion=Phiên bản thuật toán nhận dạng vân tay
att_deviceOption_FvVersion=Phiên bản thuật toán nhận dạng ven ngón tay
att_deviceOption_PvVersion=Phiên bản thuật toán nhận dạng lòng bàn tay
att_deviceOption_FWVersion=Phiên bản Firmware
att_deviceOption_PushVersion=Phiên bản Push
#=====================================================================
#API
att_api_areaCodeNotNull=Số khu vực không thể để trống
att_api_pinsNotNull=Dữ liệu pin không được phép để trống
att_api_pinsOverSize=Độ dài dữ liệu pin không được phép vượt quá 500
att_api_areaNoExist=Khu vực không tồn tại
att_api_sign=Bổ sung
#=====================================================================
#休息时间段
att_leftMenu_breakTime=Thời gian nghỉ
att_breakTime_startTime=Thời gian bắt đầu
att_breakTime_endTime=Thời gian kết thúc
#=====================================================================
#导入U盘记录
att_import_uploadFileSuccess=Tải tệp thành công, bắt đầu phân tích dữ liệu tệp, vui lòng đợi ...
att_import_resolutionComplete=Sau khi phân tích xong, bắt đầu cập nhật cơ sở dữ liệu.
att_import_snNoExist=Thiết bị tham dự tương ứng với tệp nhập không tồn tại. Vui lòng chọn lại tệp.
att_import_fileName_msg=Yêu cầu định dạng tên tệp đã nhập: Số sê-ri của thiết bị bắt đầu bằng và được phân tách bằng dấu gạch dưới "_", ví dụ: "3517171600001_attlog.dat".
att_import_notSupportFormat=Định dạng này không được hỗ trợ tại thời điểm này!
att_import_selectC CorrFile=Vui lòng chọn tệp định dạng chính xác!
att_import_fileFormat=Định dạng tệp
att_import_targetFile=Tệp mục tiêu
att_import_startRow=Số lượng hàng ở đầu tiêu đề
att_import_startRowNote=Dòng đầu tiên của định dạng dữ liệu là nhập dữ liệu, vui lòng kiểm tra tệp trước khi nhập.
att_import_d006iter=dải phân cách
#=====================================================================
#设备操作日志
att_device_op_log_op_type=Mã hoạt động
att_device_op_log_dev_sn=Số sê-ri thiết bị
att_device_op_log_op_content=Nội dung hoạt động
att_device_op_log_operator_pin=Số nhà quản trị
att_device_op_log_operator_name=Tên nhà quản trị
att_device_op_log_op_time=Thời gian hoạt động
att_device_op_log_op_who_value=Giá trị đối tượng hoạt động
att_device_op_log_op_who_content=Mô tả đối tượng hoạt động
att_device_op_log_op_value1=Đối tượng hoạt động 2
att_device_op_log_op_value_content1=Mô tả đối tượng hoạt động 2
att_device_op_log_op_value2=Đối tượng hoạt động 3
att_device_op_log_op_value_content2=Mô tả đối tượng hoạt động 3
att_device_op_log_op_value3=Đối tượng hoạt động 4
att_device_op_log_op_value_content3=Mô tả đối tượng hoạt động 4
#操作日志的操作类型
att_device_op_log_opType_0=Bật nguồn
att_device_op_log_opType_1=Tắt máy
att_device_op_log_opType_2=Xác minh thất bại
att_device_op_log_opType_3=Báo động
att_device_op_log_opType_4=Vào menu
att_device_op_log_opType_5=Thay đổi cài đặt
att_device_op_log_opType_6=Ghi dấu vân tay
att_device_op_log_opType_7=Đăng ký mật khẩu
att_device_op_log_opType_8=Đăng ký thẻ HID
att_device_op_log_opType_9=Xóa người dùng
att_device_op_log_opType_10=Xóa dấu vân tay
att_device_op_log_opType_11=Xóa mật khẩu
att_device_op_log_opType_12=Xóa thẻ RF
att_device_op_log_opType_13=Xóa dữ liệu
att_device_op_log_opType_14=Tạo thẻ MF
att_device_op_log_opType_15=Đăng ký thẻ MF
att_device_op_log_opType_16=Đăng ký thẻ MF
att_device_op_log_opType_17=Xóa đăng ký thẻ MF
att_device_op_log_opType_18=Xóa nội dung thẻ MF
att_device_op_log_opType_19=Di chuyển dữ liệu đăng ký vào thẻ
att_device_op_log_opType_20=Sao chép dữ liệu từ thẻ vào máy
att_device_op_log_opType_21=Đặt thời gian
att_device_op_log_opType_22=Cài đặt gốc
att_device_op_log_opType_23=Xóa hồ sơ nhập và xuất
att_device_op_log_opType_24=Xóa đặc quyền quản trị viên
att_device_op_log_opType_25=Sửa đổi cài đặt nhóm kiểm soát truy cập
att_device_op_log_opType_26=Sửa đổi cài đặt truy cập của người dùng
att_device_op_log_opType_27=Sửa đổi khoảng thời gian truy cập
att_device_op_log_opType_28=Sửa đổi cài đặt mở khóa kết hợp
att_device_op_log_opType_29=Mở khóa
att_device_op_log_opType_30=Đăng ký người dùng mới
att_device_op_log_opType_31=Thay đổi thuộc tính dấu vân tay
att_device_op_log_opType_32=Chuông báo động
att_device_op_log_opType_34=Cản lại lại sự xâm nhập chưa xác minh
att_device_op_log_opType_35=Xóa ảnh tham dự
att_device_op_log_opType_36=Sửa đổi thông tin người dùng
att_device_op_log_opType_37=Ngày lễ
att_device_op_log_opType_38=Khôi phục dữ liệu
att_device_op_log_opType_39=Sao lưu dữ liệu
att_device_op_log_opType_40=Tải lên đĩa U
att_device_op_log_opType_41=Tải xuống đĩa U
att_device_op_log_opType_42=Mã hóa bản ghi tham dự đĩa U
att_device_op_log_opType_43=Xóa bản ghi sau khi tải xuống USB thành công
att_device_op_log_opType_53=Công tắc đi ra
att_device_op_log_opType_54=Cảm biến cửa
att_device_op_log_opType_55=Báo động
att_device_op_log_opType_56=Khôi phục tham số
att_device_op_log_opType_68=Ảnh người dùng đã đăng ký
att_device_op_log_opType_69=Sửa đổi ảnh người dùng
att_device_op_log_opType_70=Sửa đổi tên người dùng
att_device_op_log_opType_71=Sửa đổi quyền của người dùng
att_device_op_log_opType_76=Sửa đổi IP cài đặt mạng
att_device_op_log_opType_77=Sửa đổi mặt nạ mạng
att_device_op_log_opType_78=Sửa đổi cổng cài đặt mạng
att_device_op_log_opType_79=Sửa đổi cài đặt mạng DNS
att_device_op_log_opType_80=Sửa đổi mật khẩu thiết lập kết nối
att_device_op_log_opType_81=Sửa đổi ID thiết bị kết nối
att_device_op_log_opType_82=Sửa đổi địa chỉ máy chủ đám mây
att_device_op_log_opType_83=Sửa đổi cổng máy chủ đám mây
att_device_op_log_opType_87=Sửa đổi cài đặt hồ sơ kiểm soát truy cập
att_device_op_log_opType_88=Sửa đổi tham số khuôn mặt
att_device_op_log_opType_89=Sửa đổi tham số vân tay
att_device_op_log_opType_90=Sửa đổi các dấu tham số tĩnh mạch ngón tay
att_device_op_log_opType_91=Sửa đổi tham số vân bàn tay
att_device_op_log_opType_92=Nâng cấp U disk
att_device_op_log_opType_100=Sửa đổi thông tin thẻ RF
att_device_op_log_opType_101=Đăng ký khuôn mặt
att_device_op_log_opType_102=Sửa đổi quyền của nhân viên
att_device_op_log_opType_103=Xóa quyền nhân sự
att_device_op_log_opType_104=Thêm quyền của nhân viên
att_device_op_log_opType_105=Xóa dữ liệu đăng nhập
att_device_op_log_opType_106=Xóa mặt
att_device_op_log_opType_107=Xóa ảnh nhân viên
att_device_op_log_opType_108=Sửa đổi tham số
att_device_op_log_opType_109=Chọn WIFI SSID
att_device_op_log_opType_110=bật proxy
att_device_op_log_opType_111=sửa đổi proxyip
att_device_op_log_opType_112=sửa đổi cổng proxy
att_device_op_log_opType_113=Sửa đổi mật khẩu cá nhân
att_device_op_log_opType_114=Sửa đổi thông tin khuôn mặt
att_device_op_log_opType_115=Sửa đổi mật khẩu của người quản trị
att_device_op_log_opType_116=Tiếp tục cài đặt kiểm soát truy cập
att_device_op_log_opType_117=lỗi nhập mật khẩu của người quản trị
att_device_op_log_opType_118=khóa mật khẩu của người quản trị
att_device_op_log_opType_120=Sửa đổi độ dài dữ liệu thẻ hợp pháp
att_device_op_log_opType_121=Đăng ký tĩnh mạch ngón tay
att_device_op_log_opType_122=Sửa đổi vân tay
att_device_op_log_opType_123=Xóa tĩnh mạch ngón tay
att_device_op_log_opType_124=Đăng ký in lòng bàn tay
att_device_op_log_opType_125=Sửa đổi bản in
att_device_op_log_opType_126=Xóa in lòng bàn tay
#操作对象描述
att_device_op_log_content_pin=ID người dùng:
att_device_op_log_content_alarm=Báo động:
att_device_op_log_content_alarm_reason=Lý do báo động:
att_device_op_log_content_update_no=Sửa đổi số mục:
att_device_op_log_content_update_value=Sửa đổi giá trị:
att_device_op_log_content_finger_no=Số vân tay:
att_device_op_log_content_finger_size=Độ dài mẫu vân tay:
#=====================================================================
#工作流
att_flowable_datetime_to=Đến
att_flowable_todomsg_leave=Cho phép nghỉ
att_flowable_todomsg_sign=Ghi dấu lần đăng nhập
att_flowable_todomsg_overtime=Phê duyệt làm thêm giờ
att_flowable_notifymsg_leave=Thông báo rời ứng dụng
att_flowable_notifymsg_sign=Đăng nhập vào lúc
att_flowable_notifymsg_overtime=Thông báo làm ngoài giờ
att_flowable_shift=Ca:
att_flowable_hour=Giờ
att_flowable_todomsg_trip=Phê duyệt công tác
att_flowable_notifymsg_trip=Chuyến công tác
att_flowable_todomsg_out=Phê duyệt đi ra ngoài
att_flowable_notifymsg_out=Thông báo đi ra ngoài
att_flow_apply=Áp dụng
att_flow_applyTime=Thời gian nộp đơn
att_flow_approveTime=Thời gian giải quyết
att_flow_operateUser=Người đánh giá
att_flow_approve=Phê duyệt
att_flow_approveComment=Chú thích
att_flow_approvePass=Kết quả phê duyệt
att_flow_status_processing=Phê duyệt
#=====================================================================
#biotime
att_h5_pers_personIdNull=Id nhân viên không thể để trống
att_h5_attPlaceNull=Địa điểm nhận phòng không thể để trống
att_h5_attAreaNull=Khu vực tham dự không thể để trống
att_h5_pers_personNoExist=Số nhân viên không tồn tại
att_h5_signRemarkNull=Nhận xét không thể để trống
att_h5_common_pageNull=Lỗi số phân trang
att_h5_taskIdNotNull=Id nút tác vụ không thể để trống
att_h5_auditResultNotNull=Kết quả phê duyệt không thể để trống
att_h5_latLongitudeNull=Kinh độ và vĩ độ không thể để trống
att_h5_pers_personIsNull=Id nhân viên không tồn tại
att_h5_pers_personIsNotInArea=Người chưa đặt khu vực
att_h5_mapApiConnectionsError=Lỗi kết nối bản đồ API
att_h5_googleMap=Bản đồ Google
att_h5_gaodeMap=Bản đồ Gaode
att_h5_defaultMap=Bản đồ mặc định
att_h5_shiftTime=Thời gian ca kíp
att_h5_signTimes=Thời gian bổ sung
att_h5_enterKeyWords=Vui lòng nhập một từ khóa:
att_h5_mapSet=Cài đặt bản đồ tham dự
att_h5_setMapApiAddress=Đặt tham số bản đồ
att_h5_MapSetWarning=Chuyển đổi bản đồ sẽ khiến địa chỉ đăng nhập thiết bị đầu cuối di động đã nhập không tương ứng với vĩ độ và kinh độ, vui lòng sửa đổi một cách thận trọng!
att_h5_mapSelect=Lựa chọn bản đồ
att_h5_persNoHire=Nhân viên chưa gia nhập công ty tại thời điểm này.
att_slef_apiKey=API-KEY
att_self_apiJsKey=API-JS-KEY
att_self_noComputeAtt=Chưa tính toán sự tham dự trong ngày
att_self_noSignRecord=Không có bản ghi chấm công ngày
att_self_imageUploadError=Tải hình ảnh không thành công
att_self_attSignAddressAreaIsExist=Hiện đã có điểm đăng ký tham dự trong khu vực
att_self_signRuleIsError=Thời gian bổ sung hiện tại không nằm trong thời gian bổ sung cho phép.
att_self_signAcrossDay=Không thể đăng ký lịch trình ngày
att_self_todaySignIsExist=Đã có bản vá được thêm vào ngày hôm nay!
att_self_signSetting=Cài đặt bản vá
att_self_allowSign=Cho phép vá thêm
att_self_allowSignSuffix=ngày, hồ sơ tham dự trong
att_self_onlyThisMonth=Chỉ trong tháng này
att_self_allowAcrossMonth=Cho phép chéo tháng
att_self_thisTimeNoSch=Không có sự thay đổi trong khoảng thời gian hiện tại!
att_self_revokeReason=Lý do thu hồi:
att_self_revokeHint=Vui lòng nhập lý do hủy trong vòng 20 từ để xem xét
att_self_persSelfLogin=Nhân viên tự đăng nhập
att_self_isOpenSelfLogin=Bao giờ thì cho nhân viên tự đăng nhập
att_self_applyAndWorkTimeOverlap=Thời gian đăng ký và giờ làm việc chồng chéo nhau
att_apply_DurationIsZero=Thời gian không cho phép áp dụng
att_sign_mapWarn=Tải bản đồ không thành công, vui lòng kiểm tra kết nối mạng và bản đồ giá trị KEY
att_admin_applyWarn=Các hoạt động không thành công, có những người không được lên lịch hoặc thời gian nộp đơn không nằm trong phạm vi của lịch trình!({0})
att_self_getPhotoFailed=Hình ảnh không tồn tại
att_self_view=Xem
# 二维码
att_param_qrCodeUrl=Mã QR
att_param_qrCodeUrlHref=Địa chỉ máy chủ: cổng
att_param_appAttQrCode=Mã QR tham dự di động
att_param_timingFrequency=Khoảng thời gian: 5-59 phút hoặc 1-24 giờ
att_sign_signTimeNotNull=Thời gian thay thế không phải là trống rỗng
att_apply_overLastMonth=Ứng dụng bắt đầu từ tháng trước
att_apply_withoutDetail=Không có chi tiết quá trình
att_flowable_noAuth=Vui lòng sử dụng tài khoản quản trị viên siêu hạng để xem
att_apply_overtimeOverMaxTimeLong=Thời gian làm thêm giờ là nhiều hơn thời gian làm thêm giờ tối đa
# 考勤设置参数符号
att_other_arrive=√
att_other_late=L
att_other_early=E
att_other_absent=□
att_other_noSignIn=[
att_other_noSignOff=]
att_other_leave=∆
att_other_overtime=OT
att_other_off=○
att_other_classes=●
att_other_trip=T
att_other_out=G
att_other_incomplete=├
att_other_outcomplete=┤
# 服务器下发命令
att_devCmd_submitTime=Gửi thời gian
att_devCmd_returnedResult=Kết quả trả về
att_devCmd_returnTime=Thời gian trở về
att_devCmd_content=Nội dung lệnh
att_devCmd_clearCmd=Xóa danh sách lệnh
# 实时点名
att_realTime_selectDept=Vui lòng chọn một bộ phận
att_realTime_noSignPers=Người chưa đăng ký
att_realTime_signPers=Đã đăng ký
att_realTime_signMonitor=Giám sát đăng nhập
att_realTime_signDateTime=Thời gian nhận phòng
att_realTime_realTimeSet=Cài đặt cuộc gọi cuộn thời gian thực
att_realTime_openRealTime=Bật cuộc gọi cuộn thời gian thực
att_realTime_rollCallEnd=Điểm danh theo thời gian thực kết thúc
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=Lên lịch
att_personSch_cycleSch=Lập lịch trình chu kỳ
att_personSch_cleanCycleSch=Lịch trình chu trình sạch
att_personSch_cleanTempSch=Xóa lịch trình tạm thời
att_personSch_personCycleSch=lịch trình chu kỳ người
att_personSch_deptCycleSch=Lịch trình chu kỳ của bộ phận
att_personSch_groupCycleSch=Lập lịch chu kỳ nhóm
att_personSch_personTempSch=Lên lịch nhân sự tạm thời
att_personSch_deptTempSch=Lịch trình tạm thời của bộ phận
att_personSch_groupTempSch=Lịch biểu tạm thời của nhóm
att_personSch_checkGroupFirst=Vui lòng kiểm tra nhóm bên trái hoặc những người trong danh sách bên phải để hoạt động!
att_personSch_sureDeleteGroup=Bạn có chắc chắn xóa {0} và lịch trình tương ứng với nhóm không?
att_personSch_sch=Lịch trình
att_personSch_delSch=Xóa lịch biểu
#考勤计算
att_statistical_sureAllCalculate=Bạn có chắc chắn thực hiện tính toán chuyên cần cho tất cả nhân viên không?
# 异常管理
att_exception_downTemplate=Tải xuống và nhập mẫu
att_exception_signImportTemplate=Đăng nhập Mẫu
att_exception_leaveImportTemplate=Để lại mẫu nhập
att_exception_overtimeImportTemplate=Mẫu nhập ngoài giờ
att_exception_adjustImportTemplate=Điều chỉnh Mẫu Nhập
att_exception_cellDefault=Trường không bắt buộc
att_exception_cellRequired=Trường bắt buộc
att_exception_cellDateTime=Trường bắt buộc, định dạng thời gian là yyyy-MM-dd HH: mm: ss, chẳng hạn như: 2020-07-07 08:30:00
att_exception_cellLeaveTypeName=Các trường bắt buộc, chẳng hạn như: 'nghỉ việc riêng', 'nghỉ kết hôn', 'nghỉ thai sản', 'nghỉ ốm', 'nghỉ hàng năm', 'nghỉ mất sức', 'nghỉ gia đình', 'nghỉ cho con bú', 'đi công tác', 'đi chơi' Tên kiêu ngạo
att_exception_cellOvertimeSign=Trường bắt buộc, chẳng hạn như: 'làm thêm giờ bình thường', 'làm thêm vào ngày nghỉ', 'làm thêm vào ngày lễ'
att_exception_cellAdjustType=Trường bắt buộc, chẳng hạn như: 'chuyển trường', 'lớp trang điểm'
att_exception_cellAdjustDate=Trường bắt buộc, định dạng thời gian là yyyy-MM-dd, chẳng hạn như: 2020-07-07
att_exception_cellShiftName=Trường bắt buộc khi loại điều chỉnh là Trang điểm
att_exception_refuse=Từ chối
att_exception_end=Kết thúc bất thường
att_exception_delete=Xóa
att_exception_stop=Tạm dừng
# 时间段
att_timeSlot_normalTimeAdd=Thêm khoảng thời gian bình thường
att_timeSlot_elasticTimeAdd=Thêm khoảng thời gian linh hoạt
# 班次
att_shift_addRegularShift=Thêm ca làm việc thường xuyên
att_shift_addFlexibleShift=Thêm dịch chuyển linh hoạt
# 参数设置
att_param_notLeaveSetting=Cài đặt tính toán không sai
att_param_smallestUnit=Đơn vị tối thiểu
att_param_workDay=Ngày làm việc
att_param_roundingControl=Điều khiển làm tròn
att_param_abort=Xuống (loại bỏ)
att_param_rounding=làm tròn
att_param_carry=Lên (mang)
att_param_reportSymbol=Biểu tượng hiển thị báo cáo
att_param_convertCountValid=Vui lòng nhập một số và chỉ cho phép một chữ số thập phân
att_other_leaveThing=Thing
att_other_leaveMarriage=Hôn nhân
att_other_leaveBirth=sản phẩm
att_other_leaveSick=Ốm
att_other_leaveAnnual=năm
att_other_leaveFuneral=Tang lễ
att_other_leaveHome=Khám phá
att_other_leaveNursing=Điều dưỡng
att_other_leavetrip=chênh lệch
att_other_leaveout=other
att_common_schAndRest=Lên lịch và nghỉ ngơi
att_common_timeLongs=Độ dài thời gian
att_personSch_checkDeptOrPersFirst=Vui lòng kiểm tra bộ phận bên trái hoặc người trong danh sách bên phải!
att_personSch_checkCalendarFirst=Vui lòng chọn ngày bạn cần lên lịch trước!
att_personSch_cleanCheck=Xóa kiểm tra
att_personSch_delTimeSlot=Xóa khoảng thời gian đã chọn
att_personSch_repeatTimeSlotNoAdd=Không có khoảng thời gian lặp đi lặp lại sẽ được thêm vào!
att_personSch_showSchInfo=Hiển thị chi tiết lịch trình
att_personSch_sureToCycleSch=Bạn có chắc chắn lên lịch {0} định kỳ không?
att_personSch_sureToTempSch=Bạn có chắc chắn tạm thời lên lịch {0} không?
att_personSch_sureToCycleSchDeptOrGroup=Bạn có chắc chắn lên lịch cho tất cả các nhân viên theo {0} định kỳ không?
att_personSch_sureToTempSchDeptOrGroup=Bạn có chắc chắn tạm thời lên lịch cho tất cả các nhân viên dưới {0} không?
att_personSch_sureCleanCycleSch=Bạn có chắc chắn muốn xóa {0} từ {1} đến {2}?
att_personSch_sureCleanTempSch=Bạn có chắc chắn muốn xóa {0} sự thay đổi tạm thời từ {1} sang {2}?
att_personSch_sureCleanCycleSchDeptOrGroup=Bạn có chắc chắn muốn xóa lịch trình định kỳ từ {1} đến {2} cho tất cả mọi người dưới {0}?
att_personSch_sureCleanTempSchDeptOrGroup=Bạn có chắc chắn muốn xóa lịch trình tạm thời từ {1} đến {2} cho tất cả mọi người dưới {0}?
att_personSch_today=Hôm nay
att_personSch_timeSoltName=Tên khoảng thời gian
att_personSch_export=Lập lịch trình nhân sự
att_personSch_exportTemplate=Xuất mẫu thay đổi nhân sự tạm thời
att_personSch_import=Nhập lịch trình nhân sự tạm thời
att_personSch_tempSchTemplate=Mẫu lập lịch trình nhân sự tạm thời
att_personSch_tempSchTemplateTip=Vui lòng chọn thời gian bắt đầu và thời gian kết thúc để lên lịch, tải xuống mẫu lịch trong ngày đó
att_personSch_opTip=Hướng dẫn vận hành
att_personSch_opTip1=1, bạn có thể sử dụng chuột để kéo khe thời gian đến một ngày trong điều khiển lịch để lập lịch.
att_personSch_opTip2=2. Trong điều khiển lịch, bấm đúp vào một ngày để lên lịch.
att_personSch_opTip3=3. Trong điều khiển lịch, nhấn và giữ chuột để chọn nhiều ngày để lên lịch.
att_personSch_schRules=Lịch quy tắc
att_personSch_schRules1=1, lập lịch định kỳ: Ghi đè lên lịch trình trước đó sau cùng một ngày, bất kể không có giao lộ.
att_personSch_schRules2=2. Lập lịch tạm thời: Có một giao lộ vào cùng một ngày và lịch trình tạm thời trước đó được ghi đè sau. Nếu không có giao lộ, chúng sẽ tồn tại cùng một lúc.
att_personSch_schRules3=3. Thời gian và tạm thời: Nếu có một giao lộ vào cùng một ngày, thời gian sẽ được bảo hiểm tạm thời và nếu không có giao lộ, chúng cũng sẽ tồn tại đồng thời.
att_personSch_schStatus=Trạng thái lịch biểu
#左侧菜单-排班管理
att_leftMenu_schDetails=Chi tiết lịch trình
att_leftMenu_detailReport=Báo cáo chi tiết về người tham dự
att_leftMenu_signReport=Bảng chi tiết đăng ký
att_leftMenu_leaveReport=Để lại biểu mẫu chi tiết
att_leftMenu_abnormal=Bảng điểm danh bất thường
att_leftMenu_yearLeaveSumReport=Nghỉ phép hàng nămSumReport
att_leave_maxFileCount=Bạn chỉ có thể thêm tối đa 4 ảnh
#时间段
att_timeSlot_add=Đặt thời gian
att_timeSlot_select=Vui lòng chọn khoảng thời gian!
att_timeSlot_repeat=Khoảng thời gian "{0}" lặp lại!
att_timeSlot_overlapping=Khoảng thời gian "{0}" trùng lặp với thời gian đi lại của "{1}"!
att_timeSlot_addFirst=Vui lòng đặt khoảng thời gian đầu tiên!
att_timeSlot_notEmpty=Khoảng thời gian tương ứng với số nhân viên {0} không thể để trống!
att_timeSlot_notExist=Khoảng thời gian "{1}" tương ứng với số nhân viên {0} không tồn tại!
att_timeSlot_repeatEx=Khoảng thời gian "{1}" tương ứng với số nhân viên {0} trùng với thời gian đi lại của "{2}"
att_timeSlot_importRepeat=Khoảng thời gian "{1}" tương ứng với số nhân viên {0} được lặp lại
att_timeSlot_importNotPin=Không có người nào có số {0} trong hệ thống!
att_timeSlot_elasticTimePeriod=Số người {0}, không thể nhập khoảng thời gian đàn hồi'{1}'!
#导入
att_import_overData=Số lượng nhập khẩu hiện tại là {0}, vượt quá giới hạn 30.000, vui lòng nhập theo lô!
att_import_existIllegalType={0} đã nhập có loại bất hợp pháp!
#验证方式
att_verifyMode_0=nhận dạng tự động
att_verifyMode_1=Chỉ dấu vân tay
att_verifyMode_2=Xác minh ID công việc
att_verifyMode_3=Chỉ mật khẩu
att_verifyMode_4=Chỉ thẻ
att_verifyMode_5=Dấu vân tay hoặc mật khẩu
att_verifyMode_6=Dấu vân tay hoặc thẻ
att_verifyMode_7=Thẻ hoặc mật khẩu
att_verifyMode_8=ID công việc cộng với dấu vân tay
att_verifyMode_9=Dấu vân tay cộng mật khẩu
att_verifyMode_10=Thẻ cộng với dấu vân tay
att_verifyMode_11=Thẻ cộng mật khẩu
att_verifyMode_12=Dấu vân tay và mật khẩu cộng với thẻ
att_verifyMode_13=ID công việc cộng với mật khẩu vân tay cộng với mật khẩu
att_verifyMode_14=(Gōng hào jiā zhǐwén) huò (kǎ jiā zhǐwén)
att_verifyMode_15=Mặt
att_verifyMode_16=Mặt cộng dấu vân tay
att_verifyMode_17=Mật khẩu khuôn mặt cộng
att_verifyMode_18=Thẻ mặt cộng
att_verifyMode_19=Face plus dấu vân tay cộng với thẻ
att_verifyMode_20=Face plus vân tay cộng mật khẩu
att_verifyMode_21=tĩnh mạch ngón tay
att_verifyMode_22=Tĩnh mạch cộng với mật khẩu
att_verifyMode_23=Thẻ vân tay cộng với thẻ
att_verifyMode_24=Tĩnh mạch cộng với mật khẩu cộng với thẻ
att_verifyMode_25=In cọ
att_verifyMode_26=Thẻ in cộng với thẻ
att_verifyMode_27=In và mặt cọ
att_verifyMode_28=In dấu vân tay và dấu vân tay
att_verifyMode_29=In cọ cộng với dấu vân tay cộng với khuôn mặt
# 工作流
att_flow_schedule=Tiến trình kiểm tra
att_flow_schedulePass=(Đạt)
att_flow_scheduleNot=(Không được chấp thuận)
att_flow_scheduleReject=(Bị từ chối)
# 工作时长表
att_workTimeReport_total=Tổng số giờ làm việc
# 自动导出报表
att_autoExport_startEndTime=Thời gian bắt đầu và kết thúc
# 年假
att_annualLeave_setting=Cài đặt số dư ngày nghỉ hàng năm
att_annualLeave_settingTip1=Để sử dụng chức năng cân đối ngày nghỉ hàng năm, bạn cần đặt thời gian vào cho từng nhân viên; khi không đặt thời gian vào, số ngày nghỉ hàng năm còn lại của bảng cân đối nghỉ hàng năm của nhân viên sẽ được hiển thị là trống.
att_annualLeave_settingTip2=Nếu ngày hiện tại lớn hơn ngày phát hành thanh toán bù trừ, sửa đổi này sẽ có hiệu lực vào năm sau; nếu ngày hiện tại nhỏ hơn ngày phát hành thanh toán bù trừ, khi đến ngày phát hành thanh toán bù trừ, nó sẽ bị xóa và phép năm sẽ được cấp lại.
att_annualLeave_calculate=Ngày nghỉ phép hàng năm và ngày cấp phép
att_annualLeave_workTimeCalculate=Tính toán theo tỷ lệ thời gian làm việc
att_annualLeave_rule=Quy tắc về thời gian nghỉ phép hàng năm
att_annualLeave_ruleCountOver=Đã đạt đến giới hạn số lượng thiết lập tối đa
att_annualLeave_years=Những năm cuối cấp
att_annualLeave_eachYear=Hàng năm
att_annualLeave_have=Có
att_annualLeave_days=Số ngày nghỉ phép hàng năm
att_annualLeave_totalDays=Tổng số ngày nghỉ hàng năm
att_annualLeave_remainingDays=Nghỉ phép hàng năm còn lại
att_annualLeave_consosystem=Việc thiết lập quy tắc nghỉ phép hàng năm phải là những năm liên tiếp
# 年假结余表
att_annualLeave_report=Bảng Cân đối Nghỉ phép Hàng năm
att_annualLeave_validDate=Ngày hợp lệ
att_annualLeave_useDays=Sử dụng {0} ngày
att_annualLeave_calculateDays=Phát hành {0} ngày
att_annualLeave_notEnough=Không đủ ngày nghỉ hàng năm trong {0}!
att_annualLeave_notValidDate={0} không nằm trong phạm vi nghỉ phép năm hợp lệ!
att_annualLeave_notDays={0} không có ngày nghỉ hàng năm!
att_annualLeave_tip1=Zhang San đã tham gia vào ngày 1 tháng 9 năm ngoái
att_annualLeave_tip2=Cài đặt số dư ngày nghỉ hàng năm
att_annualLeave_tip3=Ngày thanh toán bù trừ và phát hành là ngày 1 tháng 1 hàng năm; nó được tính bằng cách làm tròn theo tỷ lệ làm việc; nếu thời gian phục vụ nhỏ hơn hoặc bằng 1 thì sẽ có 3 ngày phép năm, và nếu thời gian làm việc nhỏ hơn hoặc bằng 1 thì có 5 ngày phép năm
att_annualLeave_tip4=Phép tính hưởng ngày nghỉ hàng năm
att_annualLeave_tip5=Năm ngoái 09-01 ~ 12-31 hưởng 4 / 12x3=1,0 ngày
att_annualLeave_tip6=Năm nay 01-01 ~ 12-31 hưởng 4,0 ngày (năm nay 01-01 ~ 08-31 hưởng 8 / 12x3=2,0 ngày   năm nay 09-01 ~ 12-31 hưởng 4 / 12x5≈2,0 ngày)
# att SDC
att_sdc_name=Thiết bị video
att_sdc_wxMsg_firstData=Xin chào, bạn có một thông báo đăng ký tham dự
att_sdc_wxMsg_stateData=Không có cảm giác đăng ký tham dự thành công
att_sdc_wxMsg_remark=Nhắc nhở: Kết quả tham dự cuối cùng tùy thuộc vào trang chi tiết đăng ký.
# 时间段
att_timeSlot_conflict=Khoảng thời gian xung đột với các khoảng thời gian khác trong ngày
att_timeSlot_selectFirst=Vui lòng chọn khoảng thời gian
# 事件中心
att_eventCenter_sign=Đăng nhập điểm danh
#异常管理
att_exception_classImportTemplate=Mẫu Nhập Lớp
att_exception_cellClassAdjustType=Trường bắt buộc, chẳng hạn như: "{0}", "{1}", "{2}"
att_exception_swapDateDate=trường không bắt buộc, định dạng thời gian là yyyy-MM-dd, chẳng hạn như: 2020-07-07
#消息中心
att_message_leave=Thông báo tham dự {0}
att_message_leaveContent={0} đã gửi {1}, {2} thời gian là {3} ~ {4}
att_message_leaveTime=Thời gian Rời khỏi
att_message_overtime=Thông báo về việc đi học và làm thêm giờ
att_message_overtimeContent={0} đã nộp thêm giờ và thời gian làm thêm là {1} ~ {2}
att_message_overtimeTime=Thời gian làm thêm
att_message_sign=Thông báo đăng ký tham dự
att_message_signContent={0} đã gửi một ký hiệu phụ và thời gian ký bổ sung là {1}
att_message_adjust=Thông báo về việc điều chỉnh tham gia
att_message_adjustContent={0} đã gửi điều chỉnh và ngày điều chỉnh là {1}
att_message_class=Điểm danh và thông báo thay đổi
att_message_classContent=Nội dung Lớp học
att_message_classContent0={0} đã gửi một ca làm việc, ngày ca làm việc là {1} và ca làm việc là {2}
att_message_classContent1={0} đã gửi một ca làm việc, ngày thay đổi là {1} và ngày thay đổi là {2}
att_message_classContent2={0} ({1}) và {2} ({3}) hoán đổi lớp
#推送中心
att_pushCenter_transaction=Bản ghi điểm danh
# 时间段
att_timeSlot_workTimeNotEqual=Thời gian làm việc không thể bằng thời gian nghỉ làm
att_timeSlot_signTimeNotEqual=Thời gian đăng nhập bắt đầu không thể bằng thời gian đăng xuất kết thúc
# 北向接口A
att_api_notNull={0} không được để trống!
att_api_startDateGeEndDate=Thời gian bắt đầu không được lớn hơn hoặc bằng thời gian kết thúc!
att_api_leaveTypeNotExist=Loài giả không tồn tại!
att_api_imageLengthNot2000=Độ dài của địa chỉ hình ảnh không được vượt quá 2000!
# 20221230新增国际化
att_personSch_workTypeNotNull=Không thể rỗng được loại công việc tương ứng với số nhân sự
att_personSch_workTypeNotExist=Không có loại công việc tương ứng với số nhân sự
att_annualLeave_recalculate=Tính toán lại
# 20230530新增国际化
att_leftMenu_dailyReport=Báo cáo chấm công hàng ngày
att_leftMenu_overtimeReport=Báo cáo tăng ca
att_leftMenu_lateReport=Báo cáo đi trễ
att_leftMenu_earlyReport=Báo cáo về sớm
att_leftMenu_absentReport=Báo cáo vắng mặt
att_leftMenu_monthReport=Báo cáo chấm công hàng tháng
att_leftMenu_monthWorkTimeReport=Thời gian làm việc trong tháng
att_leftMenu_monthCardReport=Danh sách chấm công hàng tháng
att_leftMenu_monthOvertimeReport=Báo cáo tăng ca hàng tháng
att_leftMenu_overtimeSummaryReport=Tổng giờ tăng ca nhân viên
att_leftMenu_deptOvertimeSummaryReport=Tổng giờ tăng ca phòng ban
att_leftMenu_deptLeaveSummaryReport=Tổng giờ nghỉ phòng ban
att_annualLeave_calculateDay=Nghỉ phép hàng năm
att_annualLeave_adjustDay=Ngày được thêm vào
att_annualLeave_sureSelectDept=Bạn có chắc chắn muốn thực hiện {0} thao tác cho phòng ban được chọn?
att_annualLeave_sureSelectPerson=Bạn có chắc chắn muốn thực hiện {0} thao tác cho nhân viên được chọn?
att_annualLeave_calculateTip1=Khi quy đổi thời gian công tác: thời gian công tác của phép năm được tính theo tháng. Nếu thời gian công tác là 10 năm 3 tháng thì được tính là 10 năm 3 tháng;
att_annualLeave_calculateTip2=Khi không quy đổi theo thời gian công tác: thời gian công tác để hưởng chế độ nghỉ phép năm được tính đến năm gần nhất. Nếu thời gian công tác là 10 năm 3 tháng thì tính là 10 năm;
att_rule_isInCompleteTip=Nếu không có thông tin đăng nhập hoặc không có thông tin đăng xuất nào chưa hoàn tất, thì mức độ ưu tiên là cao nhất, sau đó không tính đến việc đến muộn, nghỉ sớm, vắng mặt và tính hợp lệ
att_rule_absentTip=Nếu việc không đăng ký vào hoặc đăng ký ra được ghi nhận là vắng mặt, thời gian vắng mặt bằng thời gian làm việc trừ đi thời gian nghỉ muộn và nghỉ sớm
att_timeSlot_elasticTip1=Khi bằng 0, thời gian có hiệu lực bằng thời gian thực tế và không có tình trạng vắng mặt
att_timeSlot_elasticTip2=Nếu thời gian thực tế lớn hơn thời gian làm việc thì thời gian có hiệu lực bằng thời gian làm việc và không có tình trạng vắng mặt
att_timeSlot_elasticTip3=Nếu thời gian thực tế ít hơn thời gian làm việc thì thời gian có hiệu lực bằng thời gian thực tế, thời gian vắng mặt bằng thời gian làm việc trừ đi thời gian thực tế
att_timeSlot_maxWorkingHours=Giờ làm việc không thể vượt quá
# 20231030
att_customReport=Báo cáo tham dự tùy chỉnh
att_customReport_byDayDetail=Chi tiết theo ngày
att_customReport_byPerson=Tóm tắt theo nhân sự
att_customReport_byDept=Tóm tắt theo bộ phận
att_customReport_queryMaxRange=Thời gian truy vấn tối đa không quá 4 tháng
# 20240630新增国际化
att_personSch_shiftWorkTypeTip1=1, loại công việc là làm thêm giờ vào ngày làm việc bình thường/ngày nghỉ, ưu tiên xếp hàng thấp hơn ngày lễ
att_personSch_shiftWorkTypeTip2=2, loại công việc là ngày lễ làm thêm giờ, ưu tiên xếp hàng cao hơn ngày lễ
att_personVerifyMode=Cách xác minh nhân sự
att_personVerifyMode_setting=Thiết lập phương thức xác minh
att_personSch_importCycSch=Nhập lịch trình nhân sự
att_personSch_cycSchTemplate=Mẫu sắp xếp chu kỳ nhân sự
att_personSch_exportCycSchTemplate=Tải về mẫu sắp xếp chu kỳ nhân sự
att_personSch_scheduleTypeNotNull=Loại thay đổi không thể trống hoặc không tồn tại!
att_personSch_shiftNotNull=Ca trực không thể để trống!
att_personSch_shiftNotExist=Sự thay đổi không tồn tại!
att_personSch_onlyAllowOneShift=Trung đội bình thường chỉ cho phép trung đội một ca!
att_shift_attShiftStartDateRemark2=Tuần mà ngày bắt đầu chu kỳ là tuần đầu tiên; Tháng mà ngày bắt đầu chu kỳ là tháng 1.
#打卡状态
att_cardStatus_setting=Cài đặt trạng thái danh dự
att_cardStatus_name=Tên
att_cardStatus_value=Giá trị
att_cardStatus_alias=Biệt tê
att_cardStatus_every_day=Mỗi ngày
att_cardStatus_by_week=Theo tuần
att_cardStatus_autoState=Trạng thái tự động
att_cardStatus_attState=Trạng thái danh dự
att_cardStatus_signIn=Đăng ký
att_cardStatus_signOut=Khởi hành
att_cardStatus_out=ra khỏi
att_cardStatus_outReturn=Trở lại
att_cardStatus_overtime_signIn=Đăng ký ca làm thêm
att_cardStatus_overtime_signOut=Khởi hành ca làm thêm
# 20241030新增国际化
att_leaveType_enableMaxDays=Bật khoảng hạn hàng năm
att_leaveType_maxDays=Khoảng hạn hàng năm (ngày)
att_leaveType_applyMaxDays=Đơn xin không thể vượt quá {0} ngày trong năm này
att_param_overTimeSetting=Cài đặt cấp độ làm thêm
att_param_overTimeLevel=Cấp độ làm thêm (giờ)
att_param_overTimeLevelEnable=Bật tính toán cấp độ làm thêm
att_param_reportColor=Màu Cáo Báo
# APP
att_app_signClientTip=Máy của bạn này đã đăng nhập bằng người khác hôm nay
att_app_noSignAddress=Khoảng có vùng quét dấu, vui lòng liên hệ quản trị viên để thiết lập
att_app_notInSignAddress=Chưa đến điểm quét dấu, không thể quét được
att_app_attendance=Kiểm tra của tôi
att_app_apply=Đơn xin kiểm tra
att_app_approve=Quyến tác nhân của tôi
# 20250530
att_node_leaderNodeExist=Nút duyệt của người lãnh đạo trực tiếp đã tồn tại
att_signAddress_init=Khởi tạo bản đồ
att_signAddress_initTips=Vui lòng nhập khóa bản đồ và khởi tạo bản đồ để chọn địa chỉ