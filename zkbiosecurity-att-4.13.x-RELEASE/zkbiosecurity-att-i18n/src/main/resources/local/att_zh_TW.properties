#系统名称 台湾
att_systemName=Att6.0考勤管理系統
#=====================================================================
#左侧菜单
att_module=考勤
#左侧菜单-考勤设备
att_leftMenu_attendanceDevice=設備管理
att_leftMenu_device=考勤設備
att_leftMenu_point=考勤點
att_leftMenu_sign_address=移動端簽到地址
att_leftMenu_adms_devCmd=服務器下發命令
#左侧菜单-基础信息
att_leftMenu_basicInformation=基礎信息
att_leftMenu_rule=規則
att_leftMenu_base_rule=考勤規則
att_leftMenu_department_rule=部門規則
att_leftMenu_holiday=節假日
att_leftMenu_leaveType=假類
att_leftMenu_timingCalculation=定時計算
att_leftMenu_autoExport=報表推送
att_leftMenu_param=參數設置
#左侧菜单-班次管理
att_leftMenu_shiftManagement=班次管理
att_leftMenu_timeSlot=時間段
att_leftMenu_shift=班次
#左侧菜单-排班管理
att_leftMenu_scheduleManagement=排班管理
att_leftMenu_group=分組
att_leftMenu_groupPerson=分組人員
att_leftMenu_groupSch=分組排班
att_leftMenu_deptSch=部門排班
att_leftMenu_personSch=人員排班
att_leftMenu_tempSch=臨時排班
att_leftMenu_nonSch=未排班人員
#左侧菜单-异常管理
att_leftMenu_exceptionManagement=考勤異常管理
att_leftMenu_sign=補簽單
att_leftMenu_leave=請假
att_leftMenu_trip=出差
att_leftMenu_out=外出
att_leftMenu_overtime=加班
att_leftMenu_adjust=調休補班
att_leftMenu_class=調班
#左侧菜单-统计报表
att_leftMenu_statisticalReport=考勤匯總報表
att_leftMenu_manualCalculation=考勤計算
att_leftMenu_transaction=原始記錄表
att_leftMenu_dayCardDetailReport=打卡詳情表
att_leftMenu_leaveSummaryReport=請假匯總表
att_leftMenu_dayDetailReport=日明細報表
att_leftMenu_monthDetailReport=月考勤狀態錶
att_leftMenu_monthStatisticalReport=人員請假匯總表
att_leftMenu_deptStatisticalReport=部門匯總表
att_leftMenu_yearStatisticalReport=年度統計報表(按人)
att_leftMenu_attSignCallRollReport=簽到點名表
att_leftMenu_workTimeReport=工作時長表
#左側菜單-設備操作日誌
att_leftMenu_device_op_log=設備操作日誌
#左侧菜单-实时点名
att_leftMenu_realTime_callRoll=實時點名
#=====================================================================
#公共
att_common_person=人員
att_common_pin=編號
att_common_group=分組
att_common_dept=部門
att_common_symbol=符號
att_common_deptNo=部門編號
att_common_deptName=部門名稱
att_common_groupNo=分組編號
att_common_groupName=分組名稱
att_common_operateTime=操作時間
att_common_operationFailed=操作失敗
att_common_id=ID
att_common_deptId=部門ID
att_common_groupId=分組ID
att_common_deviceId=設備ID
att_person_pin=人員編號
att_person_name=姓名
att_person_lastName=姓氏
att_person_internalCard=卡號
att_person_attendanceMode=考勤模式
att_person_normalAttendance=正常考勤
att_person_noPunchCard=免打卡
att_common_attendance=出勤
att_common_attendance_hour=出勤（時）
att_common_attendance_day=出勤（日）
att_common_late=遲到
att_common_early=早退
att_common_overtime=加班
att_common_exception=異常
att_common_absent=曠工
att_common_leave=請假
att_common_trip=出差
att_common_out=外出
att_common_staff=普通員工
att_common_superadmin=超級管理員
att_common_msg=短消息內容
att_common_min=短消息持續時間(分)
att_common_letterNumber=只能輸入數字或字母！
att_common_relationDataCanNotDel=存在關聯的數據不能刪除！
att_common_relationDataCanNotEdit=存在外鍵關聯的數據不能修改！
att_common_needSelectOneArea=請選擇一個區域！
att_common_neesSelectPerson=請選擇人員！
att_common_nameNoSpace=名稱不能包括空格！
att_common_digitsValid=只能輸入不超過兩位小數的數字！
att_common_numValid=只能輸入數字！
#=====================================================================
#工作面板
att_dashboard_worker=工作狂人
att_dashboard_today=今日考勤
att_dashboard_todayCount=本日考勤分段統計
att_dashboard_exceptionCount=異常統計(本月)
att_dashboard_lastWeek=上周
att_dashboard_lastMonth=上月
att_dashboard_perpsonNumber=總人數
att_dashboard_actualNumber=實到
att_dashboard_notArrivedNumber=未到
att_dashboard_attHour=工作時長
#区域
#设备
att_op_syncDev=同步軟件數據到設備
att_op_account=考勤數據校對
att_op_check=重新上傳數據
att_op_deleteCmd=清除設備命令
att_op_dataSms=對公短信息
att_op_clearAttPic=清除考勤照片
att_op_clearAttLog=清除考勤記錄
att_device_waitCmdCount=待執行命令數
att_device_status=啟用狀態
att_device_register=登記機
att_device_isRegister=是否登記機
att_device_existNotRegDevice=非登記機設備，數據無法獲取！
att_device_fwVersion=固件版本號
att_device_transInterval=刷新間隔時間(分鐘)
att_device_cmdCount=和服務器通訊的最大命令個數
att_device_delay=查詢記錄時間(秒)
att_device_timeZone=時區
att_device_operationLog=操作日誌
att_device_registeredFingerprint=登記指紋
att_device_registeredUser=登記用戶
att_device_fingerprintImage=指紋圖片
att_device_editUser=編輯用戶
att_device_modifyFingerprint=修改指紋
att_device_faceRegistration=人臉登記
att_device_userPhotos=用戶照片
att_device_attLog=是否上傳考勤記錄
att_device_operLog=是否上傳人員信息
att_device_attPhoto=是否上傳考勤照片
att_device_isOnLine=是否在線
att_device_InputPin=輸入人員編號
att_device_getPin=獲取指定人員數據
att_device_separatedPin=多個人員編號，用逗號隔開
att_device_authDevice=授權設備
att_device_disabled=以下設備處於禁用狀態，無法操作！
att_device_autoAdd=新增設備自動添加
att_device_receivePersonOnlyDb=僅接收數據庫中存在的人員數據
att_devMenu_control=設備控制
att_devMenu_viewOrGetInfo=查看和獲取信息
att_devMenu_clearData=清除設備數據
att_device_disabledOrOffline=設備未啟用或者不在線，無法操作！
att_device_areaStatus=設備區域狀態
att_device_areaCommon=區域正常
att_device_areaEmpty=區域為空
att_device_isRegDev=時區或登記機狀態修改，需設備重啟後生效!
att_device_canUpgrade=以下設備可升級
att_device_offline=以下設備處於離線狀態，無法操作！
att_device_oldProtocol=舊協議
att_device_newProtocol=新協議
att_device_noMoreTwenty=舊協議設備固件升級包不能超過20M
att_device_transferFilesTip=固件檢測成功，傳文件
att_op_clearAttPers=清除設備人員
#区域人员
att_op_forZoneAddPers=區域添加人員
att_op_dataUserSms=對私短消息
att_op_syncPers=重新同步到設備
att_areaPerson_choiceArea=請選擇區域！
att_areaPerson_byAreaPerson=按區域人員
att_areaPerson_setByAreaPerson=按區域人員設置
att_areaPerson_importBatchDel=導入批量刪除
att_areaPerson_syncToDevSuccess=操作成功！請等待指令下發完成。
att_areaPerson_personId=人員ID
att_areaPerson_areaId=區域ID
att_area_existPerson=區域存在人員！
att_areaPerson_notice1=區域或者人員不能同時為空！
att_areaPerson_notice2=未查詢到人員或者區域！
att_areaPerson_notice3=區域下面未找到設備！
att_areaPerson_addArea=添加區域
att_areaPerson_delArea=刪除區域
att_areaPerson_persNoExit=人員不存在
att_areaPerson_importTip1=請確保導入的人員已在人事模塊中存在
att_areaPerson_importTip2=批量導入人員不會自動下發到設備，需要手動同步
att_areaPerson_addAreaPerson=添加區域人員
att_areaPerson_delAreaPerson=刪除區域人員
att_areaPerson_importDelAreaPerson=導入刪除區域人員
att_areaPerson_importAreaPerson=導入區域人員
#考勤点
att_attPoint_name=考勤點名稱
att_attPoint_list=考勤點列表
att_attPoint_deviceModule=設備模塊
att_attPoint_acc=門禁
att_attPoint_park=停車場
att_attPoint_ins=信息屏
att_attPoint_pid=人證
att_attPoint_vms=視頻
att_attPoint_psg=通道
att_attPoint_doorList=門列表
att_attPoint_deviceList=設備列表
att_attPoint_channelList=通道列表
att_attPoint_gateList=閘列表
att_attPoint_recordTypeList=拉取記錄類型
att_attPoint_door=請選擇對應的門
att_attPoint_device=請選擇對應的設備
att_attPoint_gate=請選擇對應的閘
att_attPoint_normalPassRecord=正常通行記錄
att_attPoint_verificationRecord=驗證記錄
att_person_attSet=考勤設置
att_attPoint_point=請選擇考勤點
att_attPoint_count=考勤點數不足，操作失敗
att_attPoint_notSelect=沒有配置相應的模塊
att_attPoint_accInsufficientPoints=門禁記錄當考勤點數不足！
att_attPoint_parkInsufficientPoints=停車場記錄當考勤點數不足！
att_attPoint_insInsufficientPoints=信息屏當考勤點數不足！
att_attPoint_pidInsufficientPoints=人證當考勤點數不足！
att_attPoint_doorOrParkDeviceName=門名稱或停車設備名稱
att_attPoint_vmsInsufficientPoints=視頻當考勤點數不足！
att_attPoint_psgInsufficientPoints=通道當考勤點數不足！
att_attPoint_delDevFail=刪除設備失敗，設備已被當考勤點使用！
att_attPoint_pullingRecord=該考勤點正在定時獲取記錄,請稍後！
att_attPoint_lastTransactionTime=最近數據拉取時間
att_attPoint_masterDevice=主設備
att_attPoint_channelName=通道名稱
att_attPoint_cameraName=攝像機名稱
att_attPoint_cameraIP=攝像機ip
att_attPoint_channelIP=通道ip
att_attPoint_gateNumber=閘編號
att_attPoint_gateName=閘名稱
#APP考勤签到地址
att_signAddress_address=地址
att_signAddress_longitude=經度
att_signAddress_latitude=緯度
att_signAddress_range=有效範圍
att_signAddress_rangeUnit=單位為米(m)
#=====================================================================
#规则
att_rule_baseRuleSet=基礎規則設置
att_rule_countConvertSet=計算轉換設置
att_rule_otherSet=其它設置
att_rule_baseRuleSignIn=上班簽到取卡記錄原則
att_rule_baseRuleSignOut=下班簽退取卡記錄原則
att_rule_earliestPrinciple=最早
att_rule_theLatestPrinciple=最晚
att_rule_principleOfProximity=就近
att_rule_baseRuleShortestMinutes=最短的考勤時段應大於(最小值10分鐘)
att_rule_baseRuleLongestMinutes=最長的考勤時段應小於(最大值1440分鐘)
att_rule_baseRuleLateAndEarly=遲到且早退算曠工
att_rule_baseRuleCountOvertime=是否統計加班
att_rule_baseRuleFindSchSort=查找排班記錄順序
att_rule_groupGreaterThanDepartment=分組->部門
att_rule_departmentGreaterThanGroup=部門->分組
att_rule_baseRuleSmartFindClass=智能找班原則
att_rule_timeLongest=時長最長
att_rule_exceptionLeast=異常最少
att_rule_baseRuleCrossDay=班次時間段跨天時，考勤計算結果
att_rule_firstDay=第一日
att_rule_secondDay=第二日
att_rule_baseRuleShortestOvertimeMinutes=單次最短加班時長(分鐘)
att_rule_baseRuleMaxOvertimeMinutes=最大加班時長（分鐘）
att_rule_baseRuleElasticCal=彈性時長計算方式
att_rule_baseRuleTwoPunch=兩兩打卡累積時長
att_rule_baseRuleStartEnd=首尾打卡計算時長
att_rule_countConvertHour=小時轉換基準
att_rule_formulaHour=公式：小時數 = 分鐘數 / 60
att_rule_countConvertDay=天數轉換基準
att_rule_formulaDay=公式：天數 = 分鐘數 / 每天應工作的分鐘數
att_rule_inFormulaShallPrevail=以公式計算結果為準；
att_rule_remainderHour=餘數大於或等於
att_rule_oneHour=記為一小時；
att_rule_halfAnHour=記為半小時，否則忽略不計；
att_rule_remainderDay=商數大於或等於應工作的分鐘數
att_rule_oneDay=%，記為一天；
att_rule_halfAnDay=%，記為半天，否則忽略不計；
att_rule_countConvertAbsentDay=曠工天數轉換基準
att_rule_markWorkingDays=記為工作日數為準
att_rule_countConvertDecimal=小數點精確位數
att_rule_otherSymbol=設置報表中考勤結果的表示符號
att_rule_arrive=應到/實到
att_rule_noSignIn=未簽到
att_rule_noSignOff=未簽退
att_rule_off=調休
att_rule_class=補班
att_rule_shortLessLong=最短的考勤時間段不能大於最長的考勤時間段！
att_rule_symbolsWarning=設置報表中的考勤符號不能為空！
att_rule_reportSettingSet=報表導出設置
att_rule_shortDateFormat=日期格式
att_rule_shortTimeFormat=時間格式
att_rule_baseRuleSignBreakTime=休息時段是否打卡
att_leftMenu_custom_rule=自定義規則
att_custom_rule_already_exist={0}已存在自定義規則!
att_add_group_custom_rule=添加分組規則
att_custom_rule_type=規則類型
att_rule_type_group=分組規則
att_rule_type_dept=部門規則
att_custom_rule_orgNames=使用對象
att_rult_maxOverTimeType1=不限制
att_rult_maxOverTimeType2=本週
att_rult_maxOverTimeType3=本月
att_rule_countConvertDayRemark1=例：有效工作時長為500分鐘，每天應工作時長為480分鐘，則結果為500/480=1.04，保留最後一位小數為1.0
att_rule_countConvertDayRemark2=例：有效工作時長為500分鐘，每天應工作時長為480分鐘，則結果為500/480=1.04，1.04>0.8算一天
att_rule_countConvertDayRemark3=例：有效工作時長為300分鐘，每天應工作時長為480分鐘，則結果為300/480=0.625，0.2<0.625<0.8算半天
att_rule_countConvertDayRemark4=天數轉換基準：則時間段內的記為工作日數不起作用；
att_rule_countConvertDayRemark5=記為工作日數為準：只限於曠工天數計算，且每個時段只要有曠工，就按時段的工作日數來計算曠工時長；
att_rule_baseRuleSmartFindRemark1=時長最長：按當天的卡點，計算當天每個班次對應的上班時長，找到當天最長的上班有效時長的那個班次；
att_rule_baseRuleSmartFindRemark2=異常最少：按當天的卡點，計算當天每個班次對應的異常次數，找到當天異常最少的班次來計算工作時長；
att_rule_baseRuleHourValidator=半小時的判斷分鐘數不能大於或等於1小時的判斷分鐘數;
att_rule_baseRuleDayValidator=半天的判斷時長不能大於或等於1天的判斷時長;
att_rule_overtimeWarning=最大加班時長不能小於最小單次最短加班時長!
att_rule_noSignInCountType=未簽到記爲
att_rule_absent=缺勤
att_rule_earlyLeave=早退
att_rule_noSignOffCountType=未簽退記爲
att_rule_minutes=分鐘
att_rule_noSignInCountLateMinute=未簽到記爲遲到分鐘數
att_rule_noSignOffCountEarlyMinute=未簽退記爲早退分鐘數
att_rule_incomplete=不完整
att_rule_noCheckInIncomplete=未簽到不完整
att_rule_noCheckOutIncomplete=未簽退不完整
att_rule_lateMinuteWarning=未簽到記為遲到分鐘數應大於0且小於最長的考勤時段時長
att_rule_earlyMinuteWarning=未簽退記為早退分鐘數應大於0且小於最長的考勤時段時長
att_rule_baseRuleNoSignInCountLateMinuteRemark=未簽到記為遲到時,如果未簽到則記為遲到N分鐘
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=未簽退記為早退時,如果未簽退則記為早退N分鐘
#节假日
att_holiday_placeholderNo=建議以 H 開頭，如H1
att_holiday_placeholderName=建議以 年份 + 節假日名，如2017五一
att_holiday_dayNumber=天數
att_holiday_validDate_msg=該時間內已有節假日
#假种
att_leaveType_leaveThing=事假
att_leaveType_leaveMarriage=婚假
att_leaveType_leaveBirth=產假
att_leaveType_leaveSick=病假
att_leaveType_leaveAnnual=年假
att_leaveType_leaveFuneral=喪假
att_leaveType_leaveHome=探親假
att_leaveType_leaveNursing=哺乳假
att_leaveType_isDeductWorkLong=扣上班時長
att_leaveType_placeholderNo=建議以 L 開頭，如 L1
att_leaveType_placeholderName=建議以 假 結尾，如 婚假
#定时计算
att_timingcalc_timeCalcFrequency=計算間隔
att_timingcalc_timeCalcInterval=定時計算時間
att_timingcalc_timeSet=定時計算時間設置
att_timingcalc_timeSelect=請選擇時間！
att_timingcalc_optionTip=至少要保留一個有效的按日定時考勤計算
#自动导出报表
att_autoExport_reportType=報表類型
att_autoExport_fileType=文件類型
att_autoExport_fileName=文件名稱
att_autoExport_fileDateFormat=日期格式
att_autoExport_fileTimeFormat=時間格式
att_autoExport_fileContentFormat=內容格式
att_autoExport_fileContentFormatTxt=示例：{deptName}00{personPin}01{personName}02{attDatetime}03
att_autoExport_timeSendFrequency=發送頻率
att_autoExport_timeSendInterval=時間發送間隔
att_autoExport_emailType=郵件接收方類型
att_autoExport_emailRecipients=郵件接收人
att_autoExport_emailAddress=郵箱地址
att_autoExport_emailExample=示例：<EMAIL>,<EMAIL>
att_autoExport_emailSubject=郵件標題
att_autoExport_emailContent=郵件正文
att_autoExport_field=字段
att_autoExport_fieldName=字段名稱
att_autoExport_fieldCode=字段編碼
att_autoExport_reportSet=報表設置
att_autoExport_timeSet=郵件發送時間設置
att_autoExport_emailSet=郵件設置
att_autoExport_emailSetAlert=請輸入郵箱地址
att_autoExport_emailTypeSet=接收人設置
att_autoExport_byDay=按日
att_autoExport_byMonth=按月
att_autoExport_byPersonSet=按人員設置
att_autoExport_byDeptSet=按部門設置
att_autoExport_byAreaSet=按區域設置
att_autoExport_emailSubjectSet=標題設置
att_autoExport_emailContentSet=正文設置
att_autoExport_timePointAlert=請選取正確的發送時間點
att_autoExport_lastDayofMonth=每月最後一天
att_autoExport_firstDayofMonth=每月第一天
att_autoExport_dayofMonthCheck=具體日期
att_autoExport_dayofMonthCheckAlert=請選擇具體日期
att_autoExport_chooseDeptAlert=請選擇部門!
att_autoExport_sendFormatSet=發送方式設置
att_autoExport_sendFormat=發送方式
att_autoExport_mailFormat=郵箱發送方式
att_autoExport_ftpFormat=ftp發送方式
att_autoExport_sftpFormat=sftp發送方式
att_autoExport_ftpUrl=ftp服務器地址
att_autoExport_ftpPort=ftp服務器端口
att_autoExport_ftpTimeSet=ftp發送時間設置
att_autoExport_ftpParamSet=ftp參數設置
att_autoExport_ftpUsername=ftp用戶名
att_autoExport_ftpPassword=ftp密碼
att_autoExport_correctFtpParam=請正確填寫ftp參數
att_autoExport_correctFtpTestParam=請測試連接以確保通信正常
att_autoExport_inputFtpUrl=請輸入服務器地址
att_autoExport_inputFtpPort=請輸入服務器端口
att_autoExport_ftpSuccess=連接成功
att_autoExport_ftpFail=請檢查參數設置是否有誤
att_autoExport_validFtp=請輸入合法的服務器地址
att_autoExport_validPort=請輸入合法的服務器端口
att_autoExport_selectExcelTip=文件類型選擇EXCEL，內容格式為所有字段！
#=====================================================================
#时间段
att_timeSlot_periodType=時段類型
att_timeSlot_normalTime=正常時間段
att_timeSlot_elasticTime=彈性時間段
att_timeSlot_startSignInTime=開始簽到時間
att_timeSlot_toWorkTime=上班時間
att_timeSlot_endSignInTime=結束簽到時間
att_timeSlot_allowLateMinutes=允許遲到分鐘數
att_timeSlot_isMustSignIn=必須簽到
att_timeSlot_startSignOffTime=開始簽退時間
att_timeSlot_offWorkTime=下班時間
att_timeSlot_endSignOffTime=結束簽退時間
att_timeSlot_allowEarlyMinutes=允許早退分鐘數
att_timeSlot_isMustSignOff=必須簽退
att_timeSlot_workingHours=工作時長（分鐘）
att_timeSlot_isSegmentDeduction=是否段間扣除
att_timeSlot_startSegmentTime=開始段間時間
att_timeSlot_endSegmentTime=結束段間時間
att_timeSlot_interSegmentDeduction=段間扣除(分鐘)
att_timeSlot_markWorkingDays=記為工作日數
att_timeSlot_isAdvanceCountOvertime=提前是否計加班
att_timeSlot_signInAdvanceTime=簽到早於時間
att_timeSlot_isPostponeCountOvertime=延後是否計加班
att_timeSlot_signOutPosponeTime=簽退晚於時間
att_timeSlot_isCountOvertime=是否計加班
att_timeSlot_timeSlotLong=工作時長必需符合規則定義的考勤區間：
att_timeSlot_alertStartSignInTime=開始簽到時間要小於上班時間
att_timeSlot_alertEndSignInTime=上班時間要小於結束簽到時間
att_timeSlot_alertStartSignInAndEndSignIn=結束簽到時間要小於開始簽退時間
att_timeSlot_alertStartSignOffTime=開始簽退時間要小於下班時間
att_timeSlot_alertEndSignOffTime=下班時間要小於結束簽退時間
att_timeSlot_alertStartUnequalEnd=開始段間時間不能等於結束段間時間
att_timeSlot_alertStartSegmentTime=開始段間時間要小於結束段間時間
att_timeSlot_alertStartAndEndTime=上班時間要小於開始段間時間
att_timeSlot_alertEndAndoffWorkTime=結束段間時間要小於下班時間
att_timeSlot_alertToWorkLesserAndEndTimeLesser=上班時間要小於開始段間時間並且結束段間時間要小於下班時間
att_timeSlot_alertLessSignInAdvanceTime=簽到早於時間要小於上班時間
att_timeSlot_alertMoreSignInAdvanceTime=上班前記加班分鐘數要小於上班前分鐘數
att_timeSlot_alertMoreSignOutPosponeTime=下班後記加班分鐘數要小於下班後分鐘數
att_timeSlot_alertLessSignOutPosponeTime=簽退晚於時間要大於下班時間
att_timeSlot_time=請輸入正確時間格式
att_timeSlot_alertMarkWorkingDays=記為工作日數不能為空！
att_timeSlot_placeholderNo=建議以 T 開頭，如 T01
att_timeSlot_placeholderName=建議以 T 開頭或 時間段 結尾
att_timeSlot_beforeToWork=上班前
att_timeSlot_afterToWork=上班後
att_timeSlot_beforeOffWork=下班前
att_timeSlot_afterOffWork=下班後
att_timeSlot_minutesSignInValid=分鐘內簽到有效
att_timeSlot_toWork=上班
att_timeSlot_offWork=下班
att_timeSlot_minutesSignInAsOvertime=分鐘前簽到記加班
att_timeSlot_minutesSignOutAsOvertime=分鐘後開始記加班
att_timeSlot_minOvertimeMinutes=最短加班分鐘數
att_timeSlot_enableWorkingHours=是否啓用工作時間
att_timeSlot_eidtTimeSlot=編輯時間段
att_timeSlot_browseBreakTime=瀏覽休息時間段
att_timeSlot_addBreakTime=添加休息時間段
att_timeSlot_enableFlexibleWork=啟用彈性上班
att_timeSlot_advanceWorkMinutes=可提前上班
att_timeSlot_delayedWorkMinutes=可延後上班
att_timeSlot_advanceWorkMinutesValidMsg1=上班前的分鐘數要大於可提前上班的分鐘數
att_timeSlot_advanceWorkMinutesValidMsg2=可提前上班的分鐘數要小於上班前的分鐘數
att_timeSlot_advanceWorkMinutesValidMsg3=可提前上班的分鐘數要小於等於上班前簽到記加班的分鐘數
att_timeSlot_advanceWorkMinutesValidMsg4=上班前簽到記加班的分鐘數要大於等於可提前上班的分鐘數
att_timeSlot_delayedWorkMinutesValidMsg1=下班後的分鐘數要大於可延後上班的分鐘數
att_timeSlot_delayedWorkMinutesValidMsg2=可延後上班的分鐘數要小於下班後的分鐘數
att_timeSlot_delayedWorkMinutesValidMsg3=可延後上班的分鐘數要小於等於下班後簽退開始記加班的分鐘數
att_timeSlot_delayedWorkMinutesValidMsg4=下班後簽退開始記加班的分鐘數要大於等於可延後上班的分鐘數
att_timeSlot_allowLateMinutesValidMsg1=允許遲到分鐘數要小於上班後的分鐘數
att_timeSlot_allowLateMinutesValidMsg2=上班後的分鐘數要大於允許遲到分鐘數的分鐘數
att_timeSlot_allowEarlyMinutesValidMsg1=允許早退分鐘數要小於下班前的分鐘數
att_timeSlot_allowEarlyMinutesValidMsg2=下班前的分鐘要大於允許早退分鐘數的分鐘數
att_timeSlot_timeOverlap=休息時間重疊，請修改休息時間段！
att_timeSlot_atLeastOne=至少1條休息時間段！
att_timeSlot_mostThree=最多3條休息時間段！
att_timeSlot_canNotEqual=休息時間段開始時間不能等於結束時間！
att_timeSlot_shoudInWorkTime=請確保休息時間段在上班時間範圍內！
att_timeSlot_repeatBreakTime=重複休息時間段！
att_timeSlot_toWorkLe=上班時間要小於所選的休息時間段的最小開始時間：
att_timeSlot_offWorkGe=下班時間要大於所選的休息時間段的最大結束時間：
att_timeSlot_crossDays_toWork=休息時間段的最小開始時間要在時間段內:
att_timeSlot_crossDays_offWork=休息時間段的最大結束時間要在時間段內:
att_timeSlot_allowLateMinutesRemark=從上班時間開始到允許遲到分鐘數內打卡算正常上班卡
att_timeSlot_allowEarlyMinutesRemark=從下班時間開始提前簽退在允許早退分鐘數內，算正常下班卡
att_timeSlot_isSegmentDeductionRemark=是否扣除時間段內的休息時間段
att_timeSlot_attEnableFlexibleWorkRemark1=彈性上班不允許設置遲到、早退分鐘數
att_timeSlot_afterToWorkRemark=上班後分鐘數等於可延後上班分鐘數
att_timeSlot_beforeOffWorkRemark=下班前分鐘數等於可提前上班分鐘數
att_timeSlot_attEnableFlexibleWorkRemark2=下班後的分鐘數要大於或等於下班時間+延後上班時間
att_timeSlot_attEnableFlexibleWorkRemark3=可提前上班分鐘數，必須小於等於上班N分鐘記加班分鐘數
att_timeSlot_attEnableFlexibleWorkRemark4=可延後上班分鐘數，必須小於等於下班N分鐘記加班分鐘數
att_timeSlot_attBeforeToWorkAsOvertimeRemark=例：9點的班，上班60分鐘前簽到記加班，則8點前簽到至8點算加班
att_timeSlot_attAfterOffWorkAsOvertimeRemark=例：18點下班，下班60分鐘後簽退記加班，則從19點開始到簽退時間算加班
att_timeSlot_longTimeValidRemark=上班後的有效簽卡時長與下班前的有效簽卡時長，在時間段內不能重疊！
att_timeSlot_advanceWorkMinutesValidMsg5=下班前簽到有效分鐘數要大於可提前上​​班分鐘數
att_timeSlot_advanceWorkMinutesValidMsg6=可提前上班分鐘數要小於下班前簽到有效分鐘數
att_timeSlot_delayedWorkMinutesValidMsg5=上班後簽到有效分鐘數要大於可延後上班分鐘數
att_timeSlot_delayedWorkMinutesValidMsg6=可延後上班分鐘數要小於上班後簽到有效分鐘數
att_timeSlot_advanceWorkMinutesValidMsg7=上班前開始簽到時間不能和前一天下班後結束簽退時間重疊
att_timeSlot_delayedWorkMinutesValidMsg7=下班後結束簽退時間不能和後一天上班前開始簽到時間重疊
att_timeSlot_maxOvertimeMinutes=限制最大加班時長
#班次
att_shift_basicSet=基本設置
att_shift_advancedSet=高級設置
att_shift_type=班次類型
att_shift_name=班次名稱
att_shift_regularShift=規律班次
att_shift_flexibleShift=彈性班次
att_shift_color=顏色
att_shift_periodicUnit=單位
att_shift_periodNumber=週期
att_shift_startDate=起始日期
att_shift_startDate_firstDay=週期起始日期
att_shift_isShiftWithinMonth=是否月內輪班
att_shift_attendanceMode=考勤方式
att_shift_shiftNormal=按班次正常刷卡
att_shift_oneDayOneCard=一天內任刷一次有效卡
att_shift_onlyBrushTime=只計算刷卡時間
att_shift_notBrushCard=免刷卡
att_shift_overtimeMode=加班模式
att_shift_autoCalc=電腦自動計算
att_shift_mustApply=加班必須申請
att_shift_mustOvertime=必須加班否則曠工
att_shift_timeSmaller=時長為較小者
att_shift_notOvertime=不算加班
att_shift_overtimeSign=加班標記
att_shift_normal=平時
att_shift_restday=休息日
att_shift_timeSlotDetail=時間段明細
att_shift_doubleDeleteTimeSlot=雙擊班次時段，可以刪除該時段
att_shift_addTimeSlot=增加時間段
att_shift_cleanTimeSlot=清空時間段
att_shift_NO=第
att_shift_notAll=全不選
att_shift_notTime=時間段明細複選框不可勾選，表示時間段存在重疊。
att_shift_notExistTime=沒有存在該時間段！
att_shift_cleanAllTimeSlot=確定清空所選班次的時間段？
att_shift_pleaseCheckBox=請先勾選左側列表中與右側當前顯示時間一致的複選框
att_shift_pleaseUnit=請填寫週期單位和週期數
att_shift_pleaseAllDetailTimeSlot=請選擇時間段明細
att_shift_placeholderNo=建議以 S 開頭，如 S01
att_shift_placeholderName=建議以 S 開頭或 班次 結尾
att_shift_workType=工作類型
att_shift_normalWork=正常工作
att_shift_holidayOt=節假日加班
att_shift_attShiftStartDateRemark=例：週期的起始日期是22號，以三天為一個週期，則22號/23號/24號分別上A班/B班/C班，19號/20號/21號分別上A班/B班/C班，前後日期以此類推。
att_shift_isShiftWithinMonthRemark1=月內輪班，則周期只循環到每個月的最後一天，不跨月連續排班；
att_shift_isShiftWithinMonthRemark2=非月內輪班，則周期循環到每個月的最後一天時，如果一個週期未結束，則繼續排到下個月，以此類推；
att_shift_workTypeRemark1=注意：工作類型選擇為休息日加班時，遇節假日當天不計算考勤。
att_shift_workTypeRemark2=週末加班，加班標記默認為休息日且電腦自動計算加班，不需要加班申請，當天的工作時長記為加班時長，遇節假日不計算考勤；
att_shift_workTypeRemark3=節假日加班，加班標記默認為節假日且電腦自動計算加班，不需要加班申請，當天的工作時長記為加班時長；
att_shift_attendanceModeRemark1=除按班次正常刷卡外，都不算提前或延後的加班時長，例：
att_shift_attendanceModeRemark2=1.免打卡或者一天內任打一次有效卡，不統計加班；
att_shift_attendanceModeRemark3=2.工作類型：正常工作，考勤方式：免刷卡，則當天的排班時段視為有效工作時長；
att_shift_periodStartMode=週期起始類型
att_shift_periodStartModeByPeriod=週期起始日期
att_shift_periodStartModeBySch=排班開始日期
att_shift_addTimeSlotToShift=是否添加該班次的時間段
#=====================================================================
#分组
att_group_editGroup=為分組編輯人員
att_group_browseGroupPerson=瀏覽分組的人員
att_group_list=分組列表
att_group_placeholderNo=建議以 G 開頭，如G1
att_group_placeholderName=建議以 G 開頭或 分組 結尾
att_widget_deptHint=註：導入所選部門下所有人員
att_widget_searchType=條件查詢
att_widget_noPerson=沒有選擇任何人
#分组排班
#部门排班
att_deptSch_existsDept=該部門存在部門排班，不允許刪除該部門。
#人员排班
att_personSch_view=查看人員排班
#临时排班
att_schedule_type=排班類型
att_schedule_tempType=臨時類型
att_schedule_normal=普通排班
att_schedule_intelligent=智能找班
att_tempSch_scheduleType=排班類型
att_tempSch_startDate=起始日期
att_tempSch_endDate=結束日期
att_tempSch_attendanceMode=考勤方式
att_tempSch_overtimeMode=加班方式
att_tempSch_overtimeRemark=加班標記
att_tempSch_existsDept=該部門存在部門臨時排班，不允許刪除該部門。
att_schedult_opAddTempSch=新增臨時排班
att_schedule_cleanEndDate=清空結束時間
att_schedule_selectOne=普通排班 只能選擇一個班次！
att_schedule_selectPerson=請先選擇人員！
att_schedule_selectDept=請先選擇部門！
att_schedule_selectGroup=請先選擇分組！
att_schedule_selectOneGroup=只能選擇一個分組！
att_schedule_arrange=請選擇班次！
att_schedule_leave=假
att_schedule_trip=差
att_schedule_out=外
att_schedule_off=休
att_schedule_makeUpClass=補
att_schedule_class=調
att_schedule_holiday=節
att_schedule_offDetail=調休
att_schedule_makeUpClassDetail=補班
att_schedule_classDetail=調班
att_schedule_holidayDetail=節假日
att_schedule_noSchDetail=未排班
att_schedule_normalDetail=正常
att_schedule_normalSchInfo=班次居中：未跨天班次
att_schedule_multipleInterSchInfo=班次逗號分隔：多個跨天班次
att_schedule_inderSchFirstDayInfo=班次跨格向後偏移：跨天班次記為第一日
att_schedule_inderSchSecondDayInfo=班次跨格向前偏移：跨天班次記為第二日
att_schedule_timeConflict=與現有的排班時間段衝突，不允許保存！
#=====================================================================
att_excp_notExisetPerson=人員不存在！
att_excp_leavePerson=人員離職！
#补签单
att_sign_signTime=簽卡時間
att_sign_signDate=簽卡日期
#请假
att_leave_arilName=假類名稱
att_leave_image=請假單照片
att_leave_imageShow=暫無圖片
att_leave_imageType=錯誤提示:格式不正確,支持的圖片格式為：JPEG、GIF、PNG!
att_leave_imageSize=錯誤提示:所選擇的圖片太大，圖片大小最多支持4M!
att_leave_leaveLongDay=時長(天)
att_leave_leaveLongHour=時長(時)
att_leave_leaveLongMinute=時長(分)
att_leave_endNoLessAndEqualStart=結束時間不能小於等於開始時間
att_leave_typeNameNoExsists=假類名稱不存在
att_leave_startNotNull=開始時間不能為空
att_leave_endNotNull=結束時間不能為空
att_leave_typeNameConflict=假種名稱與考勤狀態名稱衝突
#出差
att_trip_tripLongDay=出差時長(天)
att_trip_tripLongMinute=出差時長(分)
att_trip_tripLongHour=出差時長(時)
#外出
att_out_outLongDay=外出時長(天)
att_out_outLongMinute=外出時長(分)
att_out_outLongHour=外出時長(時)
#加班
att_overtime_type=加班類型
att_overtime_normal=平時加班
att_overtime_rest=休息日加班
att_overtime_overtimeLong=加班時長(分)
att_overtime_overtimeHour=加班時長(時)
att_overtime_notice=加班申請時間不允許超過一天！
att_overtime_minutesNotice=加班申請時間不能小於最短加班時長！
#调休补班
att_adjust_type=調整類型
att_adjust_adjustDate=調整日期
att_adjust_shiftName=補班班次
att_adjust_selectClass=請選擇補班的班次名稱！
att_shift_notExistShiftWorkDate={1}在{0}排班且休息不允許進行補班申請!
att_adjust_shiftPeriodStartMode=補班選擇的班次，如果起始日期是按排班，則默認取第0個
att_adjust_shiftNameNoNull=補班班次不能為空
att_adjust_shiftNameNoExsist=補班班次不存在
#调班
att_class_type=調班類型
att_class_sameTimeMoveShift=個人同日期調班
att_class_differenceTimeMoveShift=個人不同日期調班
att_class_twoPeopleMove=兩人對調
att_class_moveDate=對調日期
att_class_shiftName=調整班次名稱
att_class_moveShiftName=對調班次名稱
att_class_movePersonPin=對調人員編號
att_class_movePersonName=對調人員姓名
att_class_movePersonLastName=對調人員姓氏
att_class_moveDeptName=對調人員部門名稱
att_class_personPin=人員編號不能為空
att_class_shiftNameNoNull=調整班次不能為空
att_class_personPinNoNull=對調人員編號不能為空！
att_class_isNotExisetSwapPersonPin=對調人員編號不存在，請重新新增！
att_class_personNoSame=調整人員和對調人員不能相同，請重新新增！
att_class_outTime=調整日期和對調日期相隔不能超過一個月!
att_class_shiftNameNoExsist=調整班次不存在
att_class_swapPersonNoExisist=對調人員不存在
att_class_dateNoSame=個人不同日期調班,日期不能一樣
#=====================================================================
#节点
att_node_name=節點
att_node_type=節點類型
att_node_leader=直屬領導
att_node_leaderNode=直屬領導節點
att_node_person=指定人
att_node_position=指定職位
att_node_choose=選擇職位
att_node_personNoNull=人員不為空
att_node_posiitonNoNull=職位不能為空
att_node_placeholderNo=建議以 N 開頭，如 N01
att_node_placeholderName=建議以職位或姓名開頭，以節點結尾，如主管節點
att_node_searchPerson=輸入查詢條件
att_node_positionIsExist=該職位已經存在於節點數據中，請重新選擇職位
#流程
att_flow_type=流程類型
att_flow_rule=流程規則
att_flow_rule0=小於等於1天
att_flow_rule1=大於1天，並小於等於3天
att_flow_rule2=大於3天，並小於等於7天
att_flow_rule3=大於7天
att_flow_node=審批節點
att_flow_start=開始流程
att_flow_end=結束流程
att_flow_addNode=新增節點
att_flow_placeholderNo=建議以 F 開頭，如 F01
att_flow_placeholderName=建議類型開頭，以流程結尾，如請假流程
att_flow_tips=注意：節點的審批順序是從上到下的，選擇後可以拖動排序。
#申請
att_apply_personPin=申請人員編號
att_apply_type=異常類型
att_apply_flowStatus=流程總體狀態
att_apply_start=發起申請
att_apply_flowing=流程中
att_apply_pass=通過
att_apply_over=結束
att_apply_refuse=駁回
att_apply_revoke=撤銷
att_apply_except=異常
att_apply_view=查看詳情
att_apply_leaveTips=人員在該時間段內有請假申請！
att_apply_tripTips=人員在該時間段內有出差申請！
att_apply_outTips=人員在該時間段內有外出申請！
att_apply_overtimeTips=人員在該時間段內有加班申請！
att_apply_adjustTips=人員在該時間段內有調休補班申請！
att_apply_classTips=人員在該時間段內有調班申請！
#审批
att_approve_wait=待審批
att_approve_refuse=不通過
att_approve_reason=理由
att_approve_personPin=審批人員編號
att_approve_personName=審批人員姓名
att_approve_person=審批人員
att_approve_isPass=是否通過審批
att_approve_status=當前節點狀態
att_approve_tips=該時間點已經存在流程中的記錄，無法重複申請
att_approve_tips2=還未配置流程節點，請管理員配置
att_approve_offDayConflicts={0}在{1}未排班或者排班且休息不允許調休申請！
att_approve_shiftConflicts={0}在{1}已有排班且在上班日期中不允許補班申請！
att_approve_shiftNoSch={0}在{1}未排班不允許補班申請！
att_approve_classConflicts=未排班日期不允許進行調班申請
att_approve_selectTime=選擇時間後將根據規則判斷流程
att_approve_withoutPermissionApproval=存在無權限審批的工作流，請核對！
#=====================================================================
#考勤计算
att_op_calculation=考勤計算
att_op_calculation_notice=已有考勤數據在後台計算，請稍後重試！
att_op_calculation_leave=包含離職人員
att_statistical_choosePersonOrDept=請選擇部門或者人員！
att_statistical_sureCalculation=你確定要執行考勤計算？
att_statistical_filter=過濾條件準備完畢！
att_statistical_initData=初始化基礎數據完畢！
att_statistical_exception=初始化異常數據完畢！
att_statistical_error=考勤計算失敗！
att_statistical_begin=開始計算!
att_statistical_end=結束計算!
att_statistical_noticeTime=考勤可選時間範圍：前兩個月至當天！
att_statistical_remarkHoliday=節
att_statistical_remarkClass=班
att_statistical_remarkNoSch=未
att_statistical_remarkRest=休
#原始记录表
att_op_importAccRecord=導入門禁記錄
att_op_importParkRecord=導入停車記錄
att_op_importInsRecord=導入信息屏記錄
att_op_importPidRecord=導入人證記錄
att_op_importVmsRecord=導入視頻記錄
att_op_importUSBRecord=導入U盤記錄
att_transaction_noAccModule=沒有門禁模塊！
att_transaction_noParkModule=沒有停車模塊！
att_transaction_noInsModule=沒有信息屏模塊！
att_transaction_noPidModule=沒有人證模塊！
att_transaction_exportRecord=導出原始記錄
att_transaction_exportAttPhoto=導出考勤照片
att_transaction_fileIsTooLarge=導出的文件太大，請縮小日期範圍
att_transaction_exportDate=導出日期
att_statistical_attDatetime=考勤時間
att_statistical_attPhoto=考勤照片
att_statistical_attDetail=考勤明細
att_statistical_acc=門禁設備
att_statistical_att=考勤設備
att_statistical_park=停車場設備
att_statistical_faceRecognition=人臉識別設備
att_statistical_app=手機設備
att_statistical_vms=視頻設備
att_statistical_psg=通道設備
att_statistical_dataSources=數據來源
att_transaction_SyncRecord=同步考勤點記錄
#日打卡详情表
att_statistical_dayCardDetail=打卡詳情
att_statistical_cardDate=打卡日期
att_statistical_cardNumber=打卡次數
att_statistical_earliestTime=最早時間
att_statistical_latestTime=最晚時間
att_statistical_cardTime=打卡時間
#请假汇总表
att_statistical_leaveDetail=請假詳情
#日明细报表
att_statistical_attDate=考勤日期
att_statistical_week=星期
att_statistical_shiftInfo=班次信息
att_statistical_shiftTimeData=上下班時間
att_statistical_cardValidData=打卡數據
att_statistical_cardValidCount=打卡次數
att_statistical_lateCount=遲到次數
att_statistical_lateMinute=遲到分鐘數
att_statistical_earlyCount=早退次數
att_statistical_earlyMinute=早退分鐘數
att_statistical_countData=次數數據
att_statistical_minuteData=分鐘數數據
att_statistical_attendance_minute=出勤(分)
att_statistical_overtime_minute=加班(分)
att_statistical_unusual_minute=異常(分)
#月明细报表
att_monthdetail_should_hour=應該(時)
att_monthdetail_actual_hour=實際(時)
att_monthdetail_valid_hour=有效(時)
att_monthdetail_absent_hour=矌工(時)
att_monthdetail_leave_hour=請假(時)
att_monthdetail_trip_hour=出差(時)
att_monthdetail_out_hour=外出(時)
att_monthdetail_should_day=應該(日)
att_monthdetail_actual_day=實際(日)
att_monthdetail_valid_day=有效(日)
att_monthdetail_absent_day=曠工(日)
att_monthdetail_leave_day=請假(日)
att_monthdetail_trip_day=出差(日)
att_monthdetail_out_day=外出(日)
#月统计报表
att_statistical_late_minute=時長(分)
att_statistical_early_minute=時長(分)
#部门统计报表
#年度统计报表
att_statistical_should=應該
att_statistical_actual=實際
att_statistical_valid=有效
att_statistical_numberOfTimes=次數
att_statistical_usually=平時
att_statistical_rest=休日
att_statistical_holiday=節日
att_statistical_total=合計
att_statistical_month=統計月份
att_statistical_year=統計年份
att_statistical_attendance_hour=出勤(時)
att_statistical_attendance_day=出勤(日)
att_statistical_overtime_hour=加班(時)
att_statistical_unusual_hour=異常(時)
att_statistical_unusual_day=異常(日)
#考勤设备参数
att_deviceOption_query=查看設備參數
att_deviceOption_noOption=沒有參數信息，請先獲取設備參數
att_deviceOption_name=參數名稱
att_deviceOption_value=參數值
att_deviceOption_UserCount=用戶數
att_deviceOption_MaxUserCount=最大用戶數
att_deviceOption_FaceCount=當前面部模板數
att_deviceOption_MaxFaceCount=最大面部模板數
att_deviceOption_FacePhotoCount=当前面部圖像数
att_deviceOption_MaxFacePhotoCount=最大面部圖像数
att_deviceOption_FingerCount=當前指紋模板數
att_deviceOption_MaxFingerCount=最大指紋模板數
att_deviceOption_FingerPhotoCount=当前指纹圖像数
att_deviceOption_MaxFingerPhotoCount=最大指纹圖像数
att_deviceOption_FvCount=當前指靜脈模板數
att_deviceOption_MaxFvCount=最大指靜脈模板數
att_deviceOption_FvPhotoCount=当前指静脉圖像数
att_deviceOption_MaxFvPhotoCount=最大指静脉圖像数
att_deviceOption_PvCount=當前手掌模板數
att_deviceOption_MaxPvCount=最大手掌模板數
att_deviceOption_PvPhotoCount=当前手掌圖像数
att_deviceOption_MaxPvPhotoCount=最大手掌圖像数
att_deviceOption_TransactionCount=當前記錄數
att_deviceOption_MaxAttLogCount=最大記錄數
att_deviceOption_UserPhotoCount=當前用戶照片數
att_deviceOption_MaxUserPhotoCount=最大用戶照片數
att_deviceOption_FaceVersion=人臉識別算法版本
att_deviceOption_FPVersion=指紋識別算法版本
att_deviceOption_FvVersion=指靜脈識別算法版本
att_deviceOption_PvVersion=手掌識別算法版本
att_deviceOption_FWVersion=固件版本
att_deviceOption_PushVersion=Push版本
#=====================================================================
#API
att_api_areaCodeNotNull=區域編號不能為空
att_api_pinsNotNull=pins數據不允許為空
att_api_pinsOverSize=pins數據長度不允許超過500
att_api_areaNoExist=區域不存在
att_api_sign=補簽
#=====================================================================
#休息时间段
att_leftMenu_breakTime=休息時間段
att_breakTime_startTime=開始時間
att_breakTime_endTime=結束時間
#=====================================================================
#导入U盘记录
att_import_uploadFileSuccess=上傳文件成功，開始解析文件數據,請稍候...
att_import_resolutionComplete=解析完畢，開始更新數據庫。
att_import_snNoExist=導入文件對應的考勤設備不存在，請重新選擇文件。
att_import_fileName_msg=導入的文件名格式要求：設備序列號開頭並用下劃線"_"分隔，例如："3517171600001_attlog.dat"。
att_import_notSupportFormat=暫時不支持此種格式！
att_import_selectCorrectFile=請選擇格式正確的文件！
att_import_fileFormat=文件格式
att_import_targetFile=目標文件
att_import_startRow=表頭起始行數
att_import_startRowNote=數據格式第壹行是導入數據，請核對文件後再導入。
att_import_delimiter=分隔符
#=====================================================================
#设备操作日志
att_device_op_log_op_type=操作代碼
att_device_op_log_dev_sn=設備序列號
att_device_op_log_op_content=操作內容
att_device_op_log_operator_pin=操作人員編號
att_device_op_log_operator_name=操作人員姓名
att_device_op_log_op_time=操作時間
att_device_op_log_op_who_value=操作對象值
att_device_op_log_op_who_content=操作對象描述
att_device_op_log_op_value1=操作對象2
att_device_op_log_op_value_content1=操作對象描述2
att_device_op_log_op_value2=操作對象3
att_device_op_log_op_value_content2=操作對象描述3
att_device_op_log_op_value3=操作對象4
att_device_op_log_op_value_content3=操作對象描述4
#操作日志的操作类型
att_device_op_log_opType_0=開機
att_device_op_log_opType_1=關機
att_device_op_log_opType_2=驗證失敗
att_device_op_log_opType_3=報警
att_device_op_log_opType_4=進入菜單
att_device_op_log_opType_5=更改設置
att_device_op_log_opType_6=登記指紋
att_device_op_log_opType_7=登記密碼
att_device_op_log_opType_8=登記HID卡
att_device_op_log_opType_9=刪除用戶
att_device_op_log_opType_10=刪除指紋
att_device_op_log_opType_11=刪除密碼
att_device_op_log_opType_12=刪除射頻卡
att_device_op_log_opType_13=清除數據
att_device_op_log_opType_14=創建MF卡
att_device_op_log_opType_15=登記MF卡
att_device_op_log_opType_16=註冊MF卡
att_device_op_log_opType_17=刪除MF卡註冊
att_device_op_log_opType_18=清除MF卡內容
att_device_op_log_opType_19=把登記數據移到卡中
att_device_op_log_opType_20=把卡中的數據復制到機器中
att_device_op_log_opType_21=設置時間
att_device_op_log_opType_22=出廠設置
att_device_op_log_opType_23=刪除進出記錄
att_device_op_log_opType_24=清除管理員權限
att_device_op_log_opType_25=修改門禁組設置
att_device_op_log_opType_26=修改用戶門禁設置
att_device_op_log_opType_27=修改門禁時間段
att_device_op_log_opType_28=修改開鎖組合設置
att_device_op_log_opType_29=開鎖
att_device_op_log_opType_30=登記新用戶
att_device_op_log_opType_31=更改指紋屬性
att_device_op_log_opType_32=脅迫報警
att_device_op_log_opType_34=反潛
att_device_op_log_opType_35=刪除考勤照片
att_device_op_log_opType_36=修改用戶其他信息
att_device_op_log_opType_37=節假日
att_device_op_log_opType_38=還原數據
att_device_op_log_opType_39=備份數據
att_device_op_log_opType_40=U盤上傳
att_device_op_log_opType_41=U盤下載
att_device_op_log_opType_42=U盤考勤記錄加密
att_device_op_log_opType_43=U盤下載成功後刪除記錄
att_device_op_log_opType_53=出門開關
att_device_op_log_opType_54=門磁
att_device_op_log_opType_55=報警
att_device_op_log_opType_56=恢復參數
att_device_op_log_opType_68=註冊用戶照片
att_device_op_log_opType_69=修改用戶照片
att_device_op_log_opType_70=修改用戶姓名
att_device_op_log_opType_71=修改用戶權限
att_device_op_log_opType_76=修改網絡設置IP
att_device_op_log_opType_77=修改網絡設置掩碼
att_device_op_log_opType_78=修改網絡設置網關
att_device_op_log_opType_79=修改網絡設置DNS
att_device_op_log_opType_80=修改連接設置密碼
att_device_op_log_opType_81=修改連接設置設備ID
att_device_op_log_opType_82=修改雲服務器地址
att_device_op_log_opType_83=修改雲服務器端口
att_device_op_log_opType_87=修改門禁記錄設置
att_device_op_log_opType_88=修改人臉參數標誌
att_device_op_log_opType_89=修改指紋參數標誌
att_device_op_log_opType_90=修改指靜脈參數標誌
att_device_op_log_opType_91=修改掌紋參數標誌
att_device_op_log_opType_92=u盤升級標誌
att_device_op_log_opType_100=修改RF卡信息
att_device_op_log_opType_101=登記人臉
att_device_op_log_opType_102=修改人員權限
att_device_op_log_opType_103=刪除人員權限
att_device_op_log_opType_104=增加人員權限
att_device_op_log_opType_105=刪除門禁記錄
att_device_op_log_opType_106=刪除人臉
att_device_op_log_opType_107=刪除人員照片
att_device_op_log_opType_108=修改參數
att_device_op_log_opType_109=選擇WIFISSID
att_device_op_log_opType_110=proxy使能
att_device_op_log_opType_111=proxyip修改
att_device_op_log_opType_112=proxy端口修改
att_device_op_log_opType_113=修改人員密碼
att_device_op_log_opType_114=修改人臉信息
att_device_op_log_opType_115=修改operator的密碼
att_device_op_log_opType_116=恢復門禁設置
att_device_op_log_opType_117=operator密碼輸入錯誤
att_device_op_log_opType_118=operator密碼鎖定
att_device_op_log_opType_120=修改Legic卡數據長度
att_device_op_log_opType_121=登記指靜脈
att_device_op_log_opType_122=修改指靜脈
att_device_op_log_opType_123=刪除指靜脈
att_device_op_log_opType_124=登記掌紋
att_device_op_log_opType_125=修改掌紋
att_device_op_log_opType_126=刪除掌紋
#操作对象描述
att_device_op_log_content_pin=用戶工號：
att_device_op_log_content_alarm=報警：
att_device_op_log_content_alarm_reason=報警原因：
att_device_op_log_content_update_no=修改項序號：
att_device_op_log_content_update_value=修改項值：
att_device_op_log_content_finger_no=指紋序號：
att_device_op_log_content_finger_size=指紋模板長度：
#=====================================================================
#工作流
att_flowable_datetime_to=至
att_flowable_todomsg_leave=請假審批
att_flowable_todomsg_sign=補簽審批
att_flowable_todomsg_overtime=加班審批
att_flowable_notifymsg_leave=請假知會
att_flowable_notifymsg_sign=補簽知會
att_flowable_notifymsg_overtime=加班知會
att_flowable_shift=班次:
att_flowable_hour=小時
att_flowable_todomsg_trip=出差審批
att_flowable_notifymsg_trip=出差知會
att_flowable_todomsg_out=外出審批
att_flowable_notifymsg_out=外出知會
att_flow_apply=申請
att_flow_applyTime=申請時間
att_flow_approveTime=審批時間
att_flow_operateUser=審核人
att_flow_approve=審批
att_flow_approveComment=批註
att_flow_approvePass=審批結果
att_flow_status_processing=審批中
#=====================================================================
#biotime
att_h5_pers_personIdNull=員工id不能爲空
att_h5_attPlaceNull=簽到地點不能爲空
att_h5_attAreaNull=考勤區域不能為空
att_h5_pers_personNoExist=員工編號不存在
att_h5_signRemarkNull=備注不能爲空
att_h5_common_pageNull=分頁參數錯誤
att_h5_taskIdNotNull=任務節點id不能爲空
att_h5_auditResultNotNull=審批結果不能爲空
att_h5_latLongitudeNull=經緯度不能爲空
att_h5_pers_personIsNull=員工id不存在
att_h5_pers_personIsNotInArea=該人員未設置所處區域
att_h5_mapApiConnectionsError=地圖api連接錯誤
att_h5_googleMap=谷歌地圖
att_h5_gaodeMap=高德地圖
att_h5_defaultMap=默認地圖
att_h5_shiftTime=排班時間
att_h5_signTimes=補籤時間
att_h5_enterKeyWords=請輸入關鍵字：
att_h5_mapSet=考勤地圖設置
att_h5_setMapApiAddress=設置地圖參數
att_h5_MapSetWarning=切換地圖會導致錄入的移動端簽到地址經緯度無法對應,請謹慎修改!
att_h5_mapSelect=地圖選擇
att_h5_persNoHire=該員工此時還未入職
att_slef_apiKey=API-KEY
att_self_apiJsKey=API-JS-KEY
att_self_noComputeAtt=當日考勤還未核算
att_self_noSignRecord=當日無打卡記錄
att_self_imageUploadError=圖片上傳失敗
att_self_attSignAddressAreaIsExist=該區域已有簽到點
att_self_signRuleIsError=當前補籤時間不在允許補籤時間範圍內
att_self_signAcrossDay=跨天排班不可補簽！
att_self_todaySignIsExist=今日已有補簽！
att_self_signSetting=補簽設置
att_self_allowSign=允許補簽:
att_self_allowSignSuffix=天，以內的考勤記錄
att_self_onlyThisMonth=僅限本月
att_self_allowAcrossMonth=允許跨月
att_self_thisTimeNoSch=當前時間段沒有排班！
att_self_revokeReason=撤銷理由:
att_self_revokeHint=請輸入20字以內的撤銷原由,以便審核
att_self_persSelfLogin=員工自助登錄
att_self_isOpenSelfLogin=是否啟動員工自助登錄入口
att_self_applyAndWorkTimeOverlap=申請時間和上班時間有重疊
att_apply_DurationIsZero=申請時長爲0,不允許申請
att_sign_mapWarn=地圖加載失敗,請檢查網絡連接和地圖KEY值
att_admin_applyWarn=操作失敗，存在人員未排班或申請時間不在排班範圍內！({0})
att_self_getPhotoFailed=圖片不存在
att_self_view=查看
# 二维码
att_param_qrCodeUrl=二維碼Url
att_param_qrCodeUrlHref=服務器地址:端口
att_param_appAttQrCode=移動端考勤二維碼
att_param_timingFrequency=時間間隔：5-59分鐘或1-24小時
att_sign_signTimeNotNull=補籤時間不能爲空
att_apply_overLastMonth=申請開始時間超過了上上個月
att_apply_withoutDetail=沒有流程詳情
att_flowable_noAuth=請使用超級管理員帳戶查看
att_apply_overtimeOverMaxTimeLong=加班時長大於最大加班時長
# 考勤设置参数符号
att_other_arrive=√
att_other_late=L
att_other_early=E
att_other_absent=□
att_other_noSignIn=[
att_other_noSignOff=]
att_other_leave=∆
att_other_overtime=+
att_other_off=○
att_other_classes=●
att_other_trip=T
att_other_out=G
att_other_incomplete=├
att_other_outcomplete=┤
# 服务器下发命令
att_devCmd_submitTime=提交時間
att_devCmd_returnedResult=返回結果
att_devCmd_returnTime=返回時間
att_devCmd_content=命令內容
att_devCmd_clearCmd=清空命令表
# 实时点名
att_realTime_selectDept=請選擇部門
att_realTime_noSignPers=未簽到人員
att_realTime_signPers=已簽到
att_realTime_signMonitor=簽到監控
att_realTime_signDateTime=簽到時間
att_realTime_realTimeSet=實時點名設置
att_realTime_openRealTime=啟用實時點名
att_realTime_rollCallEnd=實時點名結束
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=已排班
att_personSch_cycleSch=週期排班
att_personSch_cleanCycleSch=清除週期排班
att_personSch_cleanTempSch=清除臨時排班
att_personSch_personCycleSch=人員周期排班
att_personSch_deptCycleSch=部門週期排班
att_personSch_groupCycleSch=分組週期排班
att_personSch_personTempSch=人員臨時排班
att_personSch_deptTempSch=部門臨時排班
att_personSch_groupTempSch=分組臨時排班
att_personSch_checkGroupFirst=請勾選左側的分組或者右側列表的人員進行操作！
att_personSch_sureDeleteGroup=確​​認刪除{0}以及分組對應的排班嗎？
att_personSch_sch=排班
att_personSch_delSch=刪除排班
# 考勤计算
att_statistical_sureAllCalculate=確認對所有人員進行考勤計算嗎？
# 异常管理
att_exception_downTemplate=下載導入模版
att_exception_signImportTemplate=補簽單導入模版
att_exception_leaveImportTemplate=請假導入模版
att_exception_overtimeImportTemplate=加班導入模版
att_exception_adjustImportTemplate=調休導入模版
att_exception_cellDefault=非必填字段
att_exception_cellRequired=必填字段
att_exception_cellDateTime=必填字段，時間格式為yyyy-MM-dd HH:mm:ss，如：2020-07-07 08:30:00
att_exception_cellLeaveTypeName=必填字段，如：'事假'、'婚假'、'產假'、'病假'、'年假'、'喪假'、'探親假'、'哺乳假'、'出差'、'外出'等假種名稱
att_exception_cellOvertimeSign=必填字段，如：'平時加班'、'休息日加班'、'節假日加班'
att_exception_cellAdjustType=必填字段，如：'調休'、'補班'
att_exception_cellAdjustDate=必填字段，時間格式為yyyy-MM-dd，如：2020-07-07
att_exception_cellShiftName=當調整類型為補班時，必填字段
att_exception_refuse=拒絕
att_exception_end=異常結束
att_exception_delete=刪除
att_exception_stop=暫停
# 时间段
att_timeSlot_normalTimeAdd=新增正常時間段
att_timeSlot_elasticTimeAdd=新增彈性時間段
# 班次
att_shift_addRegularShift=新增規律班次
att_shift_addFlexibleShift=新增彈性班次
# 参数设置
att_param_notLeaveSetting=非假類計算設置
att_param_smallestUnit=最小單位
att_param_workDay=工作日
att_param_roundingControl=舍入控制
att_param_abort=向下（捨棄）
att_param_rounding=四捨五入
att_param_carry=向上（進位）
att_param_reportSymbol=報表展示符號
att_param_convertCountValid=請輸入數字且只允許一位小數
att_other_leaveThing=事
att_other_leaveMarriage=婚
att_other_leaveBirth=產
att_other_leaveSick=病
att_other_leaveAnnual=年
att_other_leaveFuneral=喪
att_other_leaveHome=探
att_other_leaveNursing=哺
att_other_leavetrip=差
att_other_leaveout=外
att_common_schAndRest=排班且休息
att_common_timeLongs=時長
att_personSch_checkDeptOrPersFirst=請先勾選要操作的左側的部門或者右側列表的人員！
att_personSch_checkCalendarFirst=請先選中需要排班的日期！
att_personSch_cleanCheck=清空選中
att_personSch_delTimeSlot=清除選中時間段
att_personSch_repeatTimeSlotNoAdd=存在重複的時間段不進行添加！
att_personSch_showSchInfo=顯示排班詳情
att_personSch_sureToCycleSch=確認對{0}進行週期排班嗎？
att_personSch_sureToTempSch=確認對{0}進行臨時排班嗎？
att_personSch_sureToCycleSchDeptOrGroup=確認對{0}下的所有人員進行週期排班嗎？
att_personSch_sureToTempSchDeptOrGroup=確認對{0}下的所有人員進行臨時排班嗎？
att_personSch_sureCleanCycleSch=確認清除{0}從{1}到{2}的周期排班嗎？
att_personSch_sureCleanTempSch=確認清除{0}從{1}到{2}的臨時排班嗎？
att_personSch_sureCleanCycleSchDeptOrGroup=確認清除{0}下的所有人員從{1}到{2}的周期排班嗎？
att_personSch_sureCleanTempSchDeptOrGroup=確認清除{0}下的所有人員從{1}到{2}的臨時排班嗎？
att_personSch_today=今天
att_personSch_timeSoltName=時間段名稱
att_personSch_export=導出人員排班
att_personSch_exportTemplate=導出人員臨時排班模版
att_personSch_import=導入人員臨時排班
att_personSch_tempSchTemplate=人員臨時排班模版
att_personSch_tempSchTemplateTip=請選擇要排班的開始時間與結束時間，下載該日期內的排班模版
att_personSch_opTip=操作說明
att_personSch_opTip1=1、可以用鼠標將時段拖到日曆控件內的單個日期進行排班。
att_personSch_opTip2=2、可以在日曆控件內，雙擊單個日期進行排班。
att_personSch_opTip3=3、可以在日曆控件內，按住鼠標移動選擇多個日期進行排班。
att_personSch_schRules=排班規則
att_personSch_schRules1=1、週期排班：相同日期後面覆蓋前面的排班，不管有不交集。
att_personSch_schRules2=2、臨時排班：相同日期有交集，後面覆蓋前面的臨時排班，如果沒有交集則同時存在。
att_personSch_schRules3=3、週期與臨時：相同日期有交集則臨時覆蓋週期，沒交集則同時存在；如果週期排班類型是智能找班，則直接由臨時排班覆蓋週期。
att_personSch_schStatus=排班狀態
#左侧菜单-排班管理
att_leftMenu_schDetails=排班詳情
att_leftMenu_detailReport=考勤記錄報表
att_leftMenu_signReport=補簽詳情表
att_leftMenu_leaveReport=請假報表
att_leftMenu_abnormal=出勤異常表
att_leftMenu_yearLeaveSumReport=年度請假匯總表
att_leave_maxFileCount=最多只能添加4張照片
#时间段
att_timeSlot_add=設置時間段
att_timeSlot_select=請選擇時間段！
att_timeSlot_repeat=時間段“{0}”重複！
att_timeSlot_overlapping=時間段“{0}”與“{1}”的上下班時間重疊！
att_timeSlot_addFirst=請先設置時間段！
att_timeSlot_notEmpty=人員編號{0}對應的時間段不能為空！
att_timeSlot_notExist=人員編號{0}對應的時間段“{1}”不存在！
att_timeSlot_repeatEx=人員編號{0}對應的時間段“{1}”與“{2}”上下班時間重疊
att_timeSlot_importRepeat=人員編號{0}對應的時間段“{1}”重複
att_timeSlot_importNotPin=系統中不存在編號為{0}的人員！
att_timeSlot_elasticTimePeriod=人員編號{0}，不能導入彈性時間段“{1}”！
#导入
att_import_overData=當前導入條數為{0}，超過限制的30000條，請分批導入！
att_import_existIllegalType=導入的{0}存在非法類型！
#验证方式
att_verifyMode_0=自動識別
att_verifyMode_1=僅指紋
att_verifyMode_2=工號驗證
att_verifyMode_3=僅密碼
att_verifyMode_4=僅卡
att_verifyMode_5=指紋或密碼
att_verifyMode_6=指紋或卡
att_verifyMode_7=卡或密碼
att_verifyMode_8=工號加指紋
att_verifyMode_9=指紋加密碼
att_verifyMode_10=卡加指紋
att_verifyMode_11=卡加密碼
att_verifyMode_12=指紋加密碼加卡
att_verifyMode_13=工號加指紋加密碼
att_verifyMode_14=(工號加指紋)或(卡加指紋)
att_verifyMode_15=人臉
att_verifyMode_16=人臉加指紋
att_verifyMode_17=人臉加密碼
att_verifyMode_18=人臉加卡
att_verifyMode_19=人臉加指紋加卡
att_verifyMode_20=人臉加指紋加密碼
att_verifyMode_21=指靜脈
att_verifyMode_22=指靜脈加密碼
att_verifyMode_23=指靜脈加卡
att_verifyMode_24=指靜脈加密碼加卡
att_verifyMode_25=掌紋
att_verifyMode_26=掌紋加卡
att_verifyMode_27=掌紋加面部
att_verifyMode_28=掌紋加指紋
att_verifyMode_29=掌紋加指紋加面部
# 工作流
att_flow_schedule=審核進度
att_flow_schedulePass=（通過）
att_flow_scheduleNot=（未審批）
att_flow_scheduleReject=（駁回）
# 工作时长表
att_workTimeReport_total=工作時長總和
# 自动导出报表
att_autoExport_startEndTime=起止時間
# 年假
att_annualLeave_setting=年休假結餘設置
att_annualLeave_settingTip1=使用年假結餘功能需要對每個人員設置入職時間；未設置入職時間時，人員年假結餘表的剩餘年假顯示為空。
att_annualLeave_settingTip2=若當前日期大於清零發放日期，則本次修改內容次年生效；若當前日期小於清零發放日期，則到清零發放日期時，會進行清零並重新發放年假。
att_annualLeave_calculate=年休假清零發放日期
att_annualLeave_workTimeCalculate=按工齡折算
att_annualLeave_rule=年假時長規則
att_annualLeave_ruleCountOver=已達到最大設置條數限制
att_annualLeave_years=工齡
att_annualLeave_eachYear=每年
att_annualLeave_have=有
att_annualLeave_days=天年假
att_annualLeave_totalDays=總年假
att_annualLeave_remainingDays=剩餘年假
att_annualLeave_consecutive=年假規則設置必須為連續年份
# 年假结余表
att_annualLeave_report=年假結餘表
att_annualLeave_validDate=有效期
att_annualLeave_useDays=使用{0}天
att_annualLeave_calculateDays=發放{0}天
att_annualLeave_notEnough={0}年假不足！
att_annualLeave_notValidDate={0}不在年假的有效範圍內！
att_annualLeave_notDays={0}沒有年假！
att_annualLeave_tip1=張三去年9月1號入職
att_annualLeave_tip2=年假結餘設置
att_annualLeave_tip3=清零發放日期為每年1月1號；按上班比例四捨五入計算；工齡≤1時有3天年假，1年<工齡≤3年時，有5天年假
att_annualLeave_tip4=年假享有計算
att_annualLeave_tip5=去年09-01~12-31享有4/12x3=1.0天
att_annualLeave_tip6=今年01-01~12-31享有4.0天（今年01-01~08-31享有8/12x3=2.0天 今年09-01~12-31享有4/12x5≈2.0天）
# att SDC
att_sdc_name=視頻設備
att_sdc_wxMsg_firstData=您好，您有一條考勤打卡通知
att_sdc_wxMsg_stateData=無感考勤打卡成功
att_sdc_wxMsg_remark=溫馨提示：最終考勤結果以打卡詳情頁為準。
# 时间段
att_timeSlot_conflict=該在時間段與當天的其他時間段衝突
att_timeSlot_selectFirst=請選擇時間段
# 事件中心
att_eventCenter_sign=考勤簽到
#异常管理
att_exception_classImportTemplate=調班導入模版
att_exception_cellClassAdjustType=必填字段，如："{0}"、"{1}"、"{2}"
att_exception_swapDateDate=非必填字段，時間格式為yyyy-MM-dd，如：2020-07-07
#消息中心
att_message_leave=考勤{0}通知
att_message_leaveContent={0}提交了{1}，{2}時間為{3}~{4}
att_message_leaveTime=請假時間
att_message_overtime=考勤加班通知
att_message_overtimeContent={0}提交了加班，加班時間為{1}~{2}
att_message_overtimeTime=加班時間
att_message_sign=考勤補簽通知
att_message_signContent={0}提交了補簽，補籤時間為{1}
att_message_adjust=考勤調休通知
att_message_adjustContent={0}提交了調休，調休日期為{1}
att_message_class=考勤調班通知
att_message_classContent=調班詳情
att_message_classContent0={0}提交了調班，調班日期為{1}，班次為{2}
att_message_classContent1={0}提交了調班，調班日期為{1}，對調日期為{2}
att_message_classContent2={0}（{1}）與{2}（{3}）對調調班
#推送中心
att_pushCenter_transaction=考勤記錄
# 时间段
att_timeSlot_workTimeNotEqual=上班時間不能等於下班時間
att_timeSlot_signTimeNotEqual=開始簽到時間不能等於結束簽退時間
# 北向接口A
att_api_notNull={0}不能為空！
att_api_startDateGeEndDate=開始時間不能大於等於結束時間！
att_api_leaveTypeNotExist=假種不存在！
att_api_imageLengthNot2000=圖片地址長度不能超過2000！
# 20221230新增国际化
att_personSch_workTypeNotNull=人員編號{0}對應的工作類型不能為空！
att_personSch_workTypeNotExist=人員編號{0}對應的工作類型不存在！
att_annualLeave_recalculate=重新計算
# 20230530新增国际化
att_leftMenu_dailyReport=考勤日報表
att_leftMenu_overtimeReport=加班報表
att_leftMenu_lateReport=遲到報表
att_leftMenu_earlyReport=早退報表
att_leftMenu_absentReport=缺勤報表
att_leftMenu_monthReport=考勤月報表
att_leftMenu_monthWorkTimeReport=月工作時長錶
att_leftMenu_monthCardReport=月打卡錶
att_leftMenu_monthOvertimeReport=月加班錶
att_leftMenu_overtimeSummaryReport=人員加班匯總表
att_leftMenu_deptOvertimeSummaryReport=部門加班匯總表
att_leftMenu_deptLeaveSummaryReport=部門請假匯總表
att_annualLeave_calculateDay=年假天數
att_annualLeave_adjustDay=調整天數
att_annualLeave_sureSelectDept=你確定要對所選部門執行{0}操作嗎？
att_annualLeave_sureSelectPerson=你確定要對所選人員執行{0}操作嗎？
att_annualLeave_calculateTip1=按工齡折算時：年假計算工齡精確到月份，如工齡為10年3個月，則取10年3個月計算；
att_annualLeave_calculateTip2=不按工齡折算時：年假計算工齡精確到年，如工齡為10年3個月，則取10年計算；
att_rule_isInCompleteTip=未簽到或未簽退記為不完整時優先順序最高，則不計遲到、早退、曠工、有效
att_rule_absentTip=未簽到或未簽退記為曠工時，則曠工時長等於工作時長减遲到早退時長
att_timeSlot_elasticTip1=為0時，有效時長等於實際時長，無曠工
att_timeSlot_elasticTip2=如果實際時長大於工作時長，有效時長等於工作時長，無曠工
att_timeSlot_elasticTip3=如果實際時長小於工作時長，有效時長等於實際時長，曠工等於工作時長减實際時長
att_timeSlot_maxWorkingHours=工作時長不能大於
# 20231030
att_customReport=考勤自定義報表
att_customReport_byDayDetail=按日明細
att_customReport_byPerson=按人員匯總
att_customReport_byDept=按部門匯總
att_customReport_queryMaxRange=査詢時間範圍最大不能超過四個月
# 20240630新增国际化
att_personSch_shiftWorkTypeTip1=1、工作類型為正常工作/休息日加班時，排班優先順序低於節假日
att_personSch_shiftWorkTypeTip2=2、工作類型為節假日加班時，排班優先順序高於節假日
att_personVerifyMode=人員驗證方式
att_personVerifyMode_setting=驗證方式設定
att_personSch_importCycSch=導入人員週期排班
att_personSch_cycSchTemplate=人員週期排班模版
att_personSch_exportCycSchTemplate=下載人員週期排班模版
att_personSch_scheduleTypeNotNull=班次類型不能為空或不存在！
att_personSch_shiftNotNull=班次不能為空！
att_personSch_shiftNotExist=班次不存在！
att_personSch_onlyAllowOneShift=普通排班只允許排一個班次！
att_shift_attShiftStartDateRemark2=週期起始日期所在的周為第一周； 週期起始日期所在的月份為第一月。
#打卡狀態
att_cardStatus_setting=考勤狀態設置
att_cardStatus_name=名稱
att_cardStatus_value=值
att_cardStatus_alias=別名
att_cardStatus_every_day=每天
att_cardStatus_by_week=按星期
att_cardStatus_autoState=自動切換
att_cardStatus_attState=考勤狀態
att_cardStatus_signIn=簽到
att_cardStatus_signOut=簽退
att_cardStatus_out=外出
att_cardStatus_outReturn=外出返回
att_cardStatus_overtime_signIn=加班簽到
att_cardStatus_overtime_signOut=加班簽退
# 20241030新增国际化
att_leaveType_enableMaxDays=啟用年度限制
att_leaveType_maxDays=年度限制(天)
att_leaveType_applyMaxDays=年度申請不能超過{0}天
att_param_overTimeSetting=加班等級設置
att_param_overTimeLevel=加班等級(時)
att_param_overTimeLevelEnable=是否啟用加班等級計算
att_param_reportColor=報表展示顏色
# APP
att_app_signClientTip=此設備今天已被其他人簽到
att_app_noSignAddress=未設置打卡範圍，請聯繫管理員進行設置
att_app_notInSignAddress=未到達簽到地點，無法簽到
att_app_attendance=我的考勤情況
att_app_apply=考勤申請表
att_app_approve=我的審批事項
# 20250530
att_node_leaderNodeExist=直屬領導審批節點已存在
att_signAddress_init=初始化地圖
att_signAddress_initTips=請輸入地圖密鑰並初始化地圖以選擇地址