#系统名称
att_systemName=Système de Présence 6.0
#=====================================================================
#左侧菜单
att_module=Présence
#左侧菜单-考勤设备
att_leftMenu_attendanceDevice=Gestion des appareils
att_leftMenu_device=Dispositif d'assistance
att_leftMenu_point=Point de présence
att_leftMenu_sign_address=Adresse de connexion avec mobile
att_leftMenu_adms_devCmd=Le serveur a émis une commande
#左侧菜单-基础信息
att_leftMenu_basicInformation=Informations de base
att_leftMenu_rule=Règles
att_leftMenu_base_rule=Règle basique
att_leftMenu_department_rule=Règles départementales
att_leftMenu_holiday=Vacances
att_leftMenu_leaveType=Type de congé
att_leftMenu_timingCalculation=Calcul chronométré
att_leftMenu_autoExport=Envoi de rapport
att_leftMenu_param=Réglage des paramètres
#左侧菜单-班次管理
att_leftMenu_shiftManagement=Rotation
att_leftMenu_timeSlot=Calendrier
att_leftMenu_shift=Rotation
#左侧菜单-排班管理
att_leftMenu_scheduleManagement=Programme
att_leftMenu_group=Groupe
att_leftMenu_groupPerson=Groupe personnel
att_leftMenu_groupSch=Programme de Groupe
att_leftMenu_deptSch=Programme de Département
att_leftMenu_personSch=Programme de Personnel
att_leftMenu_tempSch=Programme Temporaire
att_leftMenu_nonSch=Personnel non programmé
#左侧菜单-异常管理
att_leftMenu_exceptionManagement=Gestion des exceptions de présence
att_leftMenu_sign=Reçu joint
att_leftMenu_leave=Congé
att_leftMenu_trip=Voyage d'affaires
att_leftMenu_out=Sortir
att_leftMenu_overtime=Heures supplémentaires
att_leftMenu_adjust=Ajuster et joindre
att_leftMenu_class=Ajuster la rotation
#左侧菜单-统计报表
att_leftMenu_statisticalReport=Rapport des statistiques de fréquentation
att_leftMenu_manualCalculation=Calcul manuel
att_leftMenu_transaction=Transactions
att_leftMenu_dayCardDetailReport=Présence journalière
att_leftMenu_leaveSummaryReport=Récapitulatif des Congés
att_leftMenu_dayDetailReport=Rapport Journalier
att_leftMenu_monthDetailReport=Rapport mensuel détaillé
att_leftMenu_monthStatisticalReport=Rapport mensuel des du personnel
att_leftMenu_deptStatisticalReport=Rapport ministériel mensuel
att_leftMenu_yearStatisticalReport=Rapport annuel
att_leftMenu_attSignCallRollReport=Connectez-vous dans la table d'appel nominal
att_leftMenu_workTimeReport=Rapport sur le temps de travail
#左侧菜单-设备操作日志
att_leftMenu_device_op_log=Journal d'opération de l'appareil
#左侧菜单-实时点名
att_leftMenu_realTime_callRoll=Appel
#=====================================================================
#公共
att_common_person=Personnel
att_common_pin=Identité
att_common_group=Groupe
att_common_dept=Département
att_common_symbol=symbole
att_common_deptNo=Numéro de département
att_common_deptName=Nom du département
att_common_groupNo=Numéro de groupe
att_common_groupName=Nom de groupe
att_common_operateTime=Temps d'opération
att_common_operationFailed=Opération échouée
att_common_id=Identité
att_common_deptId=ID du Département
att_common_groupId=ID du Groupe
att_common_deviceId=ID du Périphérique
att_person_pin=ID du Personnel
att_person_name=Prénom
att_person_lastName=Nom de famille
att_person_internalCard=Numéro de carte
att_person_attendanceMode=Mode heure et présence
att_person_normalAttendance=Fréquentation normale
att_person_noPunchCard=Pas de carte perforée
att_common_attendance=Présence
att_common_attendance_hour=Présence (heures)
att_common_attendance_day=Présence (jour)
att_common_late=En retard
att_common_early=En avance
att_common_overtime=Heures supplémentaires
att_common_exception=Exception
att_common_absent=Absent
att_common_leave=Congé
att_common_trip=Voyage d'affaires
att_common_out=Sortie
att_common_staff=Employé
att_common_superadmin=Super utilisateur
att_common_msg=Contenu SMS
att_common_min=Durée du message court (minutes)
att_common_letterNumber=Il ne peut saisir que des chiffres ou des lettres!
att_common_relationDataCanNotDel=Les données associées ne peuvent pas être supprimées.
att_common_relationDataCanNotEdit=The associated data can not be modified.
att_common_needSelectOneArea=Veuillez sélectionner une zone!
att_common_neesSelectPerson=Veuillez sélectionner une personne!
att_common_nameNoSpace=Le nom ne peut pas contenir d'espaces!
att_common_digitsValid=Il ne peut saisir que des nombres ne dépassant pas deux décimales!
att_common_numValid=Entrez uniquement des chiffres!
#=====================================================================
#工作面板
att_dashboard_worker=Bourreau de travail
att_dashboard_today=Présence d'aujourd'hui
att_dashboard_todayCount=Statistiques segmentées sur la présence d'aujourd'hui
att_dashboard_exceptionCount=Statistiques anormales (Ce mois)
att_dashboard_lastWeek=Semaine passée
att_dashboard_lastMonth=Mois passé
att_dashboard_perpsonNumber=Personnel Total
att_dashboard_actualNumber=Personnel Actuel
att_dashboard_notArrivedNumber=Personnel Absent
att_dashboard_attHour=Temps de travail
#区域
#设备
att_op_syncDev=Synchroniser les données du logiciel avec le périphérique
att_op_account=Vérification des données de présence
att_op_check=Importer à nouveau les données
att_op_deleteCmd=Effacer les commandes du périphérique
att_op_dataSms=Message public
att_op_clearAttPic=Effacer les photos de présence
att_op_clearAttLog=Effacer les transactions de présence
att_device_waitCmdCount=Commandes à exécuter
att_device_status=Activer l'état
att_device_register=Enregistrer machine
att_device_isRegister=Périphérique d'inscription
att_device_existNotRegDevice=Équipement de machine non enregistré, les données ne peuvent pas être obtenues!
att_device_fwVersion=Version du Firmware
att_device_transInterval=Durée de rafraîchissement(mins)
att_device_cmdCount=Le nombre maximum de commandes pour communiquer avec le serveur.
att_device_delay=Temps d'enregistrement de l'information (secondes)
att_device_timeZone=Calendrier
att_device_operationLog=Journaux des opérations
att_device_registeredFingerprint=Inscrire empreinte digitale
att_device_registeredUser=Inscrire personnel
att_device_fingerprintImage=Image d'empreinte digitale
att_device_editUser=Modifier le personnel
att_device_modifyFingerprint=Modifier empreinte digitale
att_device_faceRegistration=Inscription faciale
att_device_userPhotos=Photo du personnel
att_device_attLog=Indique s'il faut importer les fiches de présence
att_device_operLog=Indique s'il faut importer les informations du personnel
att_device_attPhoto=Indique s'il faut importer les photos de présence
att_device_isOnLine=Statut en ligne
att_device_InputPin=Entrez le numéro de la personne
att_device_getPin=Obtenir les données de personnel spécifiées
att_device_separatedPin=Numéros de personnel multiples, séparés par des virgules
att_device_authDevice=Appareil autorisé
att_device_disabled=Les appareils suivants sont désactivés et ne peuvent pas être utilisés!
att_device_autoAdd=De nouveaux équipements sont ajoutés automatiquement
att_device_receivePersonOnlyDb=Recevoir uniquement les données du personnel présent dans la base de données
att_devMenu_control=Contrôle du périphérique
att_devMenu_viewOrGetInfo=Afficher et obtenir des informations
att_devMenu_clearData=Effacer les données de l'appareil
att_device_disabledOrOffline=Le périphérique n'est pas activé ou n'est pas en ligne, ne peut être utilisé!
att_device_areaStatus=Statut de la zone du périphérique
att_device_areaCommon=La région est normale
att_device_areaEmpty=La zone est vide
att_device_isRegDev=La modification du fuseau horaire ou de l'état du bureau d'enregistrement nécessite que l'appareil redémarre pour prendre effet!
att_device_canUpgrade=Les appareils suivants peuvent être mis à niveau
att_device_offline=Les appareils suivants sont hors ligne et ne peuvent pas être utilisés!
att_device_oldProtocol=Ancien protocole
att_device_newProtocol=Nouveau protocole
att_device_noMoreTwenty=L'ancien package de mise à niveau du micrologiciel du périphérique de protocole ne peut pas dépasser 20 Mo
att_device_transferFilesTip=Firmware détecté avec succès, transfert de fichiers
att_op_clearAttPers=Personnel de l'équipement clair
#区域人员
att_op_forZoneAddPers=Paramètre de Personnel de zone
att_op_dataUserSms=Message privé
att_op_syncPers=Re-synchroniser avec le périphérique
att_areaPerson_choiceArea=Veuillez sélectionner la zone!
att_areaPerson_byAreaPerson=Personne par zone
att_areaPerson_setByAreaPerson=Défini par le personnel de la zone
att_areaPerson_importBatchDel=Importer la suppression par lots
att_areaPerson_syncToDevSuccess=Opération réussie! Veuillez attendre l'envoi de la commande.
att_areaPerson_personId=ID du Personnel
att_areaPerson_areaId=ID de la Zone
att_area_existPerson=Il y a des gens dans la zone!
att_areaPerson_notice1=La zone ou le personnel ne peuvent pas être vide au même moment!
att_areaPerson_notice2=Aucune personne ou zone n'a été interrogée!
att_areaPerson_notice3=Aucun appareil trouvé sous la zone!
att_areaPerson_addArea=Ajouter zone
att_areaPerson_delArea=Supprimer zone
att_areaPerson_persNoExit=La personne n'existe pas
att_areaPerson_importTip1=Veuillez vous assurer que la personne importée existe déjà dans le module personnel
att_areaPerson_importTip2=Le personnel d'importation par lots ne sera pas automatiquement livré sur l'appareil, une synchronisation manuelle est requise
att_areaPerson_addAreaPerson=Ajouter du personnel de zone
att_areaPerson_delAreaPerson=Supprimer le personnel de la zone
att_areaPerson_importDelAreaPerson=Importer et supprimer le personnel de la zone
att_areaPerson_importAreaPerson=Importer le personnel de la zone
#考勤点
att_attPoint_name=Nom du point de présence
att_attPoint_list=Liste du point de présence
att_attPoint_deviceModule=Module du périphérique
att_attPoint_acc=Contrôle d'accès
att_attPoint_park=Parking
att_attPoint_ins=FaceKiosk
att_attPoint_pid=Certificat personnel
att_attPoint_vms=Vidéo
att_attPoint_psg=Rayon
att_attPoint_doorList=Liste des portes
att_attPoint_deviceList=Liste des périphériques
att_attPoint_channelList=Liste des canaux
att_attPoint_gateList=Liste des portes
att_attPoint_recordTypeList=Type d'enregistrement d'extraction
att_attPoint_door=Veuillez sélectionner la porte correspondante.
att_attPoint_device=Veuillez sélectionner le périphérique correspondant.
att_attPoint_gate=Veuillez sélectionner le portail correspondant
att_attPoint_normalPassRecord=Dossier de passage normal
att_attPoint_verificationRecord=Enregistrement de vérification
att_person_attSet=Paramètre de présence
att_attPoint_point=Veuillez sélectionner le point de présence.
att_attPoint_count=Points de licence de présence autorisés insuffisants; l'opération a échoué!
att_attPoint_notSelect=Le module n'est pas configuré
att_attPoint_accInsufficientPoints=Points de présence inadéquats pour les dossiers de contrôle d'accès!
att_attPoint_parkInsufficientPoints=Points de présence inadéquats pour les dossiers de parking!
att_attPoint_insInsufficientPoints=Le point de licence de présence de faceKiosk est insuffisant！
att_attPoint_pidInsufficientPoints=Le certificat du personnel ne suffit pas pour la présence!
att_attPoint_doorOrParkDeviceName=Nom de porte ou nom de périphérique de stationnement
att_attPoint_vmsInsufficientPoints=Vidéo lorsque les points de présence sont insuffisants!
att_attPoint_psgInsufficientPoints=La chaîne a des points de fréquentation insuffisants !
att_attPoint_delDevFail=La suppression du périphérique a échoué, le périphérique a été utilisé comme périphérique de présence!
att_attPoint_pullingRecord=Le point de présence reçoit régulièrement des records, veuillez patienter!
att_attPoint_lastTransactionTime=Heure de la dernière extraction des données
att_attPoint_masterDevice=Périphérique maître
att_attPoint_channelName=Nom du canal
att_attPoint_cameraName=Nom de la caméra
att_attPoint_cameraIP=IP de la caméra
att_attPoint_channelIP=adresse IP du canal
att_attPoint_gateNumber=Numéro de porte
att_attPoint_gateName=Nom de la porte
#APP考勤签到地址
att_signAddress_address=Adresse
att_signAddress_longitude=Longitude
att_signAddress_latitude=Latitude
att_signAddress_range=Intervalle Effectif
att_signAddress_rangeUnit=L'unité est en Mètres(m)
#=====================================================================
#规则
att_rule_baseRuleSet=Paramètre des règles de base
att_rule_countConvertSet=Paramètre de calcul
att_rule_otherSet=Autre paramètre
att_rule_baseRuleSignIn=Règle d'enregistrement
att_rule_baseRuleSignOut=Règle de départ
att_rule_earliestPrinciple=La première règle
att_rule_theLatestPrinciple=La dernière règle
att_rule_principleOfProximity=La règle de proximité
att_rule_baseRuleShortestMinutes=La période minimale doit être supérieure à (minimum 10 minutes)
att_rule_baseRuleLongestMinutes=La période maximale doit être inférieure à (maximum 1440 minutes)
att_rule_baseRuleLateAndEarly=Retard et Congé anticipé compté comme Absent.
att_rule_baseRuleCountOvertime=Statistiques des heures supplémentaires
att_rule_baseRuleFindSchSort=Rechercher enregistrement de rotation
att_rule_groupGreaterThanDepartment=Groupe->Département
att_rule_departmentGreaterThanGroup=Département->Groupe
att_rule_baseRuleSmartFindClass=Règle de rotation de correspondance intelligente
att_rule_timeLongest=Durée de travail la plus longue
att_rule_exceptionLeast=Moins anormal
att_rule_baseRuleCrossDay=Résultat du calcul de présence pour la rotation de jours
att_rule_firstDay=Premier jour
att_rule_secondDay=Deuxième jour
att_rule_baseRuleShortestOvertimeMinutes=Durée minimale des heures supplémentaires (minutes)
att_rule_baseRuleMaxOvertimeMinutes=Heures supplémentaires maximales (minutes)
att_rule_baseRuleElasticCal=Calcul de durée flexible
att_rule_baseRuleTwoPunch=Temps cumulé pour chaque deux pointages
att_rule_baseRuleStartEnd=Calcul du temps de pointage de tête et de queue
att_rule_countConvertHour=Règle de conversion d'heures
att_rule_formulaHour=Formule: Heures = Minutes / 60
att_rule_countConvertDay=Règle de conversion de jours
att_rule_formulaDay=Formule：Jours = Minutes / Nombre de minutes de travail par jour
att_rule_inFormulaShallPrevail=Prendre le résultat calculé par la formule comme standard;
att_rule_remainderHour=Le reste est supérieur ou égal à
att_rule_oneHour=Calculer comme une heure, sinon calculer comme une demi-heure ou ignorer;
att_rule_halfAnHour=Calculer comme une demi-heure, sinon ignorer;
att_rule_remainderDay=Le quotient est supérieur ou égal aux minutes de travail
att_rule_oneDay=%,calculer comme un jour, sinon calculer comme une demi-journée ou ignorer;
att_rule_halfAnDay=%,calculer comme une demi-journée, sinon ignorer;
att_rule_countConvertAbsentDay=Règle de conversion des jours d'absence
att_rule_markWorkingDays=Calculer comme jours ouvrables
att_rule_countConvertDecimal=Chiffres exacts du point décimal
att_rule_otherSymbol=The attendance result symbol setting in the report
att_rule_arrive=Attendu/réel
att_rule_noSignIn=Pas d'enregistrement
att_rule_noSignOff=Pas de départ
att_rule_off=Ajuster le repos
att_rule_class=Joindre la présence
att_rule_shortLessLong=Le calendrier de présence ne peut pas être plus long que la durée du calendrier de présence la plus longue.
att_rule_symbolsWarning=Vous devez configurer le symbole dans le rapport de présence!
att_rule_reportSettingSet=Paramètres d'exportation de rapport
att_rule_shortDateFormat=Format de date
att_rule_shortTimeFormat=Format d'heure
att_rule_baseRuleSignBreakTime=S'il faut pointer pendant la pause
att_leftMenu_custom_rule=Règle personnalisée
att_custom_rule_already_exist={0} règles personnalisées existent déjà!
att_add_group_custom_rule=Ajouter des règles de regroupement
att_custom_rule_type=Type de règle
att_rule_type_group=Règles de regroupement
att_rule_type_dept=Règles du département
att_custom_rule_orgNames=Utilisation de l'objet
att_rult_maxOverTimeType1=Pas de limite
att_rult_maxOverTimeType2=Cette Semaine
att_rult_maxOverTimeType3=Ce Mois
att_rule_countConvertDayRemark1=Exemple:Le temps de travail effectif est de 500 minutes et le temps de travail est de 480 minutes par jour. Le résultat est 500/480 = 1,25 et la dernière décimale est maintenue à 1,2
att_rule_countConvertDayRemark2=Exemple:Le temps de travail effectif est de 500 minutes et le temps de travail est de 480 minutes par jour. Le résultat est 500/480=1.25, 1.25>0.8.
att_rule_countConvertDayRemark3=Exemple:Le temps de travail effectif est de 300 minutes et le temps de travail est de 480 minutes par jour. Le résultat est 300/480=0.625, 0.2<0.625<0.8 pour une demi-journée.
att_rule_countConvertDayRemark4=Référence de conversion de jour: l'enregistrement en tant que jours ouvrables dans la période ne fonctionne pas;
att_rule_countConvertDayRemark5=Il est enregistré comme le nombre de jours ouvrables: il est limité au calcul du nombre de jours de réalisation, et tant qu'il y a réalisation dans chaque période, la durée de réalisation est calculée en fonction du nombre de jours ouvrables de la période;
att_rule_baseRuleSmartFindRemark1=Le temps le plus long: Selon le point de la carte du jour, calculez les heures de travail correspondant à chaque rotation de la journée, et trouvez la rotation la plus longue de la journée;
att_rule_baseRuleSmartFindRemark2=Minimum anormal: Calculez le nombre d'heures anormales correspondant à chaque rotation en fonction du point de carte du jour, et trouvez la rotation avec le moins d'anomalies du jour pour calculer le temps de travail;
att_rule_baseRuleHourValidator=La minute de jugement d'une demi-heure ne peut pas être supérieure ou égale à 1 heure!
att_rule_baseRuleDayValidator=La période de jugement d'une demi-journée ne peut pas être supérieure ou égale à la période de jugement d'un jour!
att_rule_overtimeWarning=La durée maximale des heures supplémentaires ne peut être inférieure à la durée minimale unique des heures supplémentaires!
att_rule_noSignInCountType=Enregistrement manquant compte comme
att_rule_absent=Absent
att_rule_earlyLeave=Congé anticipé
att_rule_noSignOffCountType=Check-Out manquant compte comme
att_rule_minutes=Minutes
att_rule_noSignInCountLateMinute=Enregistrement manquant compte comme minutes de retard
att_rule_noSignOffCountEarlyMinute=Check-Out manquant compte comme minutes parti plus tôt
att_rule_incomplete=Incomplet
att_rule_noCheckInIncomplete=Incomplet et pas d'enregistrement
att_rule_noCheckOutIncomplete=Incomplet et pas de check-out
att_rule_lateMinuteWarning=Ne pas se connecter car les dernières minutes doivent être supérieures à 0 et inférieures à la période la plus longue
att_rule_earlyMinuteWarning=Le paiement non signé est compté car les minutes de congé anticipé doivent être supérieures à 0 et inférieures à la durée la plus longue
att_rule_baseRuleNoSignInCountLateMinuteRemark=Lorsqu'il n'est pas archivé est compté comme en retard, s'il n'est pas enregistré, il est compté comme en retard pendant N minutes
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=Si la non-signature est comptée comme départ anticipé, si elle n'est pas déconnectée, elle sera comptée comme départ anticipé N minutes
#节假日
att_holiday_placeholderNo=Il est recommandé de commencer avec H, tel que H01.
att_holiday_placeholderName=Il est recommandé de nommer avec [Year]+[Holiday Name] eg. [2017 Labour Day].
att_holiday_dayNumber=Nombre de jours
att_holiday_validDate_msg=Vacances pendant cette période
#假种
att_leaveType_leaveThing=Congé occasionnel
att_leaveType_leaveMarriage=Congé de mariage
att_leaveType_leaveBirth=Congé maternité
att_leaveType_leaveSick=Congé de maladie
att_leaveType_leaveAnnual=Congé annuel
att_leaveType_leaveFuneral=Congé de deuil
att_leaveType_leaveHome=Congé familial
att_leaveType_leaveNursing=Congé d'allaitement
att_leaveType_isDeductWorkLong=Durée du travail
att_leaveType_placeholderNo=Il est recommandé de commencer par L, tel que L1.
att_leaveType_placeholderName=Il est recommandé de terminer avec "holiday",eg.Marriage Holiday.
#定时计算
att_timingcalc_timeCalcFrequency=Fréquence de calcul
att_timingcalc_timeCalcInterval=Temps de calcul chronométré
att_timingcalc_timeSet=Réglage du temps de calcul chronométré
att_timingcalc_timeSelect=Veuillez choisir le temps!
att_timingcalc_optionTip=Au moins un calcul de présence planifié quotidien valide doit être conservé
#自动导出报表
att_autoExport_reportType=Type de Rapport
att_autoExport_fileType=Type de Fichier
att_autoExport_fileName=Nom de Fichier
att_autoExport_fileDateFormat=Format de date
att_autoExport_fileTimeFormat=Format de l'heure
att_autoExport_fileContentFormat=Format du contenu
att_autoExport_fileContentFormatTxt=Exemple：{deptName}00{personPin}01{personName}02{attDatetime}03
att_autoExport_timeSendFrequency=Fréquence d'envoi
att_autoExport_timeSendInterval=Intervalle d'envoi
att_autoExport_emailType=Type de destinataire de courrier
att_autoExport_emailRecipients=Destinataire de courrier
att_autoExport_emailAddress=Adresse mail
att_autoExport_emailExample=Exemple: 123 @ foxmail.com, 456 @ foxmail.com
att_autoExport_emailSubject=Titre du courrier
att_autoExport_emailContent=Corps du courrier
att_autoExport_field=Champ
att_autoExport_fieldName=Nom de champ
att_autoExport_fieldCode=Numéro de champ
att_autoExport_reportSet=Réglage du rapport
att_autoExport_timeSet=Réglage du délai de livraison du courrier
att_autoExport_emailSet=Paramètre de courrier
att_autoExport_emailSetAlert=Veuillez entrer votre adresse e-mail.
att_autoExport_emailTypeSet=Réglage du récepteur
att_autoExport_byDay=Par Jour
att_autoExport_byMonth=Par Mois
att_autoExport_byPersonSet=Définir par le personnel
att_autoExport_byDeptSet=Définir par département
att_autoExport_byAreaSet=Définir par Zone
att_autoExport_emailSubjectSet=Réglage du titre
att_autoExport_emailContentSet=Réglage du corps
att_autoExport_timePointAlert=Veuillez sélectionner l'heure exacte d'envoi.
att_autoExport_lastDayofMonth=Dernier jour du mois
att_autoExport_firstDayofMonth=Premier jour du mois
att_autoExport_dayofMonthCheck=Date spécifique
att_autoExport_dayofMonthCheckAlert=Veuillez sélectionner la date spécifique.
att_autoExport_chooseDeptAlert=Veuillez sélectionner le département!
att_autoExport_sendFormatSet=Réglage du mode d'envoi
att_autoExport_sendFormat=Mode d'envoi
att_autoExport_mailFormat=Méthode de livraison de boîte aux lettres
att_autoExport_ftpFormat=Méthode d'envoi FTP
att_autoExport_sftpFormat=Méthode d'envoi SFTP
att_autoExport_ftpUrl=Adresse du serveur FTP
att_autoExport_ftpPort=Port du serveur FTP
att_autoExport_ftpTimeSet=Réglage de l'heure d'envoi FTP
att_autoExport_ftpParamSet=Réglage des paramètres FTP
att_autoExport_ftpUsername=Nom d'utilisateur FTP
att_autoExport_ftpPassword=Mot de passe FTP
att_autoExport_correctFtpParam=Veuillez remplir correctement les paramètres ftp
att_autoExport_correctFtpTestParam=Veuillez tester la connexion pour vous assurer que la communication est normale
att_autoExport_inputFtpUrl=Veuillez saisir l'adresse du serveur
att_autoExport_inputFtpPort=Veuillez entrer le port du serveur
att_autoExport_ftpSuccess=Connexion réussie
att_autoExport_ftpFail=Veuillez vérifier si les réglages des paramètres sont incorrects.
att_autoExport_validFtp=Veuillez entrer une adresse de serveur valide
att_autoExport_validPort=Veuillez entrer un port de serveur valide
att_autoExport_selectExcelTip=Type de fichier sélectionnez EXCEL, le format de contenu est tous les champs!
#=====================================================================
#时间段
att_timeSlot_periodType=Type de calendrier
att_timeSlot_normalTime=Calendrier normal
att_timeSlot_elasticTime=Calendrier flexible
att_timeSlot_startSignInTime=Heure de début d'enregistrement
att_timeSlot_toWorkTime=Heure d'enregistrement
att_timeSlot_endSignInTime=Heure de fin d'enregistrement
att_timeSlot_allowLateMinutes=Autoriser retard(minutes)
att_timeSlot_isMustSignIn=Doit s'enregistrer
att_timeSlot_startSignOffTime=Heure de début de départ
att_timeSlot_offWorkTime=Heure de départ
att_timeSlot_endSignOffTime=Heure de fin de départ
att_timeSlot_allowEarlyMinutes=Autoriser congé anticipé(minutes)
att_timeSlot_isMustSignOff=Doit Partir
att_timeSlot_workingHours=Heures de travail (minutes)
att_timeSlot_isSegmentDeduction=Déduction automatique de temps de pause
att_timeSlot_startSegmentTime=Heure de début
att_timeSlot_endSegmentTime=Heure de fin
att_timeSlot_interSegmentDeduction=Temps déduit(minutes)
att_timeSlot_markWorkingDays=Jour de travail
att_timeSlot_isAdvanceCountOvertime=Heures supplémentaires Automatique(Enregistrement en avance)
att_timeSlot_signInAdvanceTime=Heure de fin des heures supplémentaires Automatique(Enregistrement)
att_timeSlot_isPostponeCountOvertime=Heures supplémentaires Automatique(Délai de départ)
att_timeSlot_signOutPosponeTime=Heure de fin des heures supplémentaires Automatique(Départ)
att_timeSlot_isCountOvertime=Calculé comme heures supplémentaires
att_timeSlot_timeSlotLong=Les heures de travail doivent respecter l'intervalle de présence défini par les règles:
att_timeSlot_alertStartSignInTime=L'heure de début de l'enregistrement doit être inférieure à l'heure de l'enregistrement.
att_timeSlot_alertEndSignInTime=L'heure de fin d'enregistrement doit être supérieure à l'heure d'enregistrement.
att_timeSlot_alertStartSignInAndEndSignIn=L'heure de début de Check-Out doit être inférieure à l'heure de Check-Out.
att_timeSlot_alertStartSignOffTime=L'heure de début des heures supplémentaires ne peut pas être inférieure à l'heure de Check-Out.
att_timeSlot_alertEndSignOffTime=L'heure de début des heures supplémentaires ne peut pas être supérieure à l'heure de fin de Check-Out.
att_timeSlot_alertStartUnequalEnd=Les jours ouvrables ne peuvent pas être inférieurs à 0.
att_timeSlot_alertStartSegmentTime=Le temps déduit ne peut pas être inférieur à 0.
att_timeSlot_alertStartAndEndTime=L'heure de début du Check-Out ne peut pas être égale à l'heure de fin de l'enregistrement.
att_timeSlot_alertEndAndoffWorkTime=Les heures ne peuvent pas être supérieures à 23.
att_timeSlot_alertToWorkLesserAndEndTimeLesser=Les minutes ne peuvent pas être supérieures à 59.
att_timeSlot_alertLessSignInAdvanceTime=L'enregistrement anticipé doit être inférieur à l'heure de début du travail
att_timeSlot_alertMoreSignInAdvanceTime=Le nombre de minutes supplémentaires avant 'Aller au travail' est inférieur au nombre de minutes avant 'travail'
att_timeSlot_alertMoreSignOutPosponeTime='hors travail' les minutes supplémentaires de post-scriptum sont inférieures aux minutes 'après le travail'
att_timeSlot_alertLessSignOutPosponeTime=L'heure de Check-Out tardif doit être supérieure à l'heure de fin du travail
att_timeSlot_time=Veuillez saisir le format d'heure correct.
att_timeSlot_alertMarkWorkingDays=La journée de travail ne peut pas être vide!
att_timeSlot_placeholderNo=Il est recommandé de commencer par T, comme T01.
att_timeSlot_placeholderName=Il est recommandé de commencer par T ou terminer avec timetable.
att_timeSlot_beforeToWork=Avant d'aller travailler
att_timeSlot_afterToWork=Après le travail
att_timeSlot_beforeOffWork=Avant d'être hors service
att_timeSlot_afterOffWork=Après travail
att_timeSlot_minutesSignInValid=L'enregistrement est valide en quelques minutes
att_timeSlot_toWork=En service
att_timeSlot_offWork=Hors service
att_timeSlot_minutesSignInAsOvertime=Connexion il y a quelques minutes pour les heures supplémentaires
att_timeSlot_minutesSignOutAsOvertime=Commencez à compter les minutes supplémentaires  plus tard
att_timeSlot_minOvertimeMinutes=Minutes supplémentaires minimum
att_timeSlot_enableWorkingHours=S'il faut activer les heures de travail
att_timeSlot_eidtTimeSlot=Modifier l'heure
att_timeSlot_browseBreakTime=Parcourir les périodes de repos
att_timeSlot_addBreakTime=Ajouter pauses
att_timeSlot_enableFlexibleWork=Activer travail flexible
att_timeSlot_advanceWorkMinutes=Peut aller travailler à l'avance
att_timeSlot_delayedWorkMinutes=Peut reporter le travail
att_timeSlot_advanceWorkMinutesValidMsg1=Le nombre de minutes avant d'aller au travail est supérieur au nombre de minutes pouvant aller au travail à l'avance
att_timeSlot_advanceWorkMinutesValidMsg2=Le nombre de minutes pouvant aller au travail à l'avance est inférieur au nombre de minutes avant d'aller travailler
att_timeSlot_advanceWorkMinutesValidMsg3=Le nombre de minutes pouvant être effectuées à l'avance est inférieur ou égal au nombre de minutes avant connexion pour effectuer des heures supplémentaires.
att_timeSlot_advanceWorkMinutesValidMsg4=Le nombre de minutes qui peut être avant de vous connecter pour faire des heures supplémentaires est supérieur ou égal au nombre de minutes effectuées à l'avance.
att_timeSlot_delayedWorkMinutesValidMsg1=Le nombre de minutes après le travail est supérieur au nombre de minutes qui peuvent être reportées au travail
att_timeSlot_delayedWorkMinutesValidMsg2=Le nombre de minutes pouvant être retardé au travail est inférieur au nombre de minutes après le travail
att_timeSlot_delayedWorkMinutesValidMsg3=Le nombre de minutes qui peuvent être programmées pour travailler est inférieur ou égal au nombre de minutes après le travail, la fermeture de session et le début des heures supplémentaires
att_timeSlot_delayedWorkMinutesValidMsg4=Le nombre de minutes qui peuvent être après le travail, la fermeture de session et le début des heures supplémentaires est supérieur ou égal au nombre de minutes après l'heure prévue de travailler
att_timeSlot_allowLateMinutesValidMsg1=Le nombre de minutes de retard autorisé est inférieur au nombre de minutes après le travail
att_timeSlot_allowLateMinutesValidMsg2=Le nombre de minutes après le travail est supérieur au nombre de minutes de retard autorisé
att_timeSlot_allowEarlyMinutesValidMsg1=Minutes en avance autorisées est inférieur à minutes avant le travail
att_timeSlot_allowEarlyMinutesValidMsg2=Le nombre de minutes avant le travail est supérieur au nombre de minutes de départ anticipé autorisé
att_timeSlot_timeOverlap={0} chevauche avec {1} temps, veuillez modifier la période sélectionnée!
att_timeSlot_atLeastOne=Au moins 1 période de repos!
att_timeSlot_mostThree=Maximum 3 périodes de repos!
att_timeSlot_canNotEqual=L'heure de début de la période de repos ne peut pas être égale à l'heure de fin!
att_timeSlot_shoudInWorkTime=Veuillez vous assurer que la période de repos est dans les heures de travail!
att_timeSlot_repeatBreakTime=Répétez la période de pause!
att_timeSlot_toWorkLe=Le temps de travail est inférieur à l'heure de début minimale de la période de repos sélectionnée:
att_timeSlot_offWorkGe=Les heures creuses sont supérieures à l'heure de fin maximale de la période de repos sélectionnée:
att_timeSlot_crossDays_toWork=L'heure de début minimale pour la période de pause se situe dans la période de temps:
att_timeSlot_crossDays_offWork=L'heure de fin maximale de la période de repos se situe dans la période de temps:
att_timeSlot_allowLateMinutesRemark=Du temps de travail à la carte de fin de minutes autorisée pour calculer la carte de travail normale
att_timeSlot_allowEarlyMinutesRemark=En partant tôt du temps libre en nombre de minutes allouées pour partir plus tôt, la carte normale de repos
att_timeSlot_isSegmentDeductionRemark=Suppression de la période de repos dans la période de temps
att_timeSlot_attEnableFlexibleWorkRemark1=Le travail flexible n'est pas autorisé à définir le nombre de départs tardifs et anticipés
att_timeSlot_afterToWorkRemark=Après minutes de travail est égal à Reporté aux minutes de travail
att_timeSlot_beforeOffWorkRemark=Minutes avant travail sont égales à minutes peut aller au travail
att_timeSlot_attEnableFlexibleWorkRemark2=Le nombre de minutes après le travail est supérieur ou égal aux heures creuses + heures de travail retardées
att_timeSlot_attEnableFlexibleWorkRemark3=minutes Vous pouvez travailler à l'avance, doit être inférieur ou égal à N minutes de travail pour faire des heures supplémentaires
att_timeSlot_attEnableFlexibleWorkRemark4=Reporté aux minutes de travail, doit être inférieur ou égal à N minutes de congé
att_timeSlot_attBeforeToWorkAsOvertimeRemark=Eeample:9:00 classe,Connexion pour faire des heures supplémentaires 60 minutes avant le travail, puis enregistrement avant 8 heures à 8 heures heures supplémentaires.
att_timeSlot_attAfterOffWorkAsOvertimeRemark=Exemple:Après 18 heures hors service, après 60 minutes de travail, signez le retrait et effectuez les heures supplémentaires, puis commencez les heures supplémentaires de 19 heures à l'heure de départ.
att_timeSlot_longTimeValidRemark=L'heure de signature effective après le travail et l'heure de signature effective avant le travail ne peuvent pas se chevaucher dans la période de temps!
att_timeSlot_advanceWorkMinutesValidMsg5=Le nombre de minutes valides avant l'enregistrement est supérieur au nombre de minutes pouvant être travaillées à l'avance
att_timeSlot_advanceWorkMinutesValidMsg6=Les minutes pour travailler à l'avance doivent être inférieures aux minutes valides pour se connecter avant le travail
att_timeSlot_delayedWorkMinutesValidMsg5=Le nombre de minutes valides après l'enregistrement est supérieur au nombre de minutes pouvant être reportées au travail
att_timeSlot_delayedWorkMinutesValidMsg6=Le nombre de minutes pouvant être reporté au travail doit être inférieur aux minutes valides après la connexion.
att_timeSlot_advanceWorkMinutesValidMsg7=L'heure d'arrivée avant le travail ne peut pas chevaucher l'heure de départ après le travail la veille
att_timeSlot_delayedWorkMinutesValidMsg7=L'heure de départ après le travail ne peut pas chevaucher l'heure d'arrivée avant le travail le lendemain
att_timeSlot_maxOvertimeMinutes=Limiter le maximum d'heures supplémentaires
#班次
att_shift_basicSet=Type de Programme
att_shift_advancedSet=Nom du Programme
att_shift_type=Type de Rotation
att_shift_name=Nom de Rotation
att_shift_regularShift=Rotation Régulière
att_shift_flexibleShift=Rotation flexible
att_shift_color=Couleur
att_shift_periodicUnit=Unité
att_shift_periodNumber=Cycle
att_shift_startDate=Date de début
att_shift_startDate_firstDay=Date de début du cycle
att_shift_isShiftWithinMonth=Cycle de rotation en un mois
att_shift_attendanceMode=Mode de présence
att_shift_shiftNormal=Carte de pointage selon la rotation normale
att_shift_oneDayOneCard=Pointer une fois à n'importe quelle heure dans la journée
att_shift_onlyBrushTime=Calculer uniquement la carte de temps de pointage
att_shift_notBrushCard=Pointage libre
att_shift_overtimeMode=Mode d'heures supplémentaires
att_shift_autoCalc=Calcul automatique par ordinateur
att_shift_mustApply=Les heures supplémentaires doivent s'appliquer
att_shift_mustOvertime=Doit faire des heures supplémentaires ou s'absenter
att_shift_timeSmaller=Durée plus courte entre le calcul automatique et la réception des heures supplémentaires
att_shift_notOvertime=Pas calculé comme heures supplémentaires
att_shift_overtimeSign=Type d'heures supplémentaires
att_shift_normal=Journée normale
att_shift_restday=Jour de repos
att_shift_timeSlotDetail=Détails du calendrier
att_shift_doubleDeleteTimeSlot=Double-cliquez sur la période de rotation; vous pouvez supprimer la période
att_shift_addTimeSlot=Ajouter Calendrier
att_shift_cleanTimeSlot=Effacer Calendrier
att_shift_NO=NON
att_shift_notAll=Tout déselectionner
att_shift_notTime=Si la case à cocher Détails du calendrier ne peut pas être cochée, cela indique qu'il y a un chevauchement dans le calendrier.
att_shift_notExistTime=Ce calendrier n'existe pas.
att_shift_cleanAllTimeSlot=Êtes-vous sûr de vouloir effacer le calendrier pour la rotation sélectionnée?
att_shift_pleaseCheckBox=Veuillez cocher la case sur le côté gauche qui est la même que l'heure d'affichage actuelle sur le côté droit.
att_shift_pleaseUnit=Veuillez remplir les unités de cycle et le nombre de cycles.
att_shift_pleaseAllDetailTimeSlot=Veuillez sélectionner les détails du calendrier.
att_shift_placeholderNo=Il est recommandé de commencer par S, tel que S0.
att_shift_placeholderName=Il est recommandé de commencer par S ou finir avec "shift".
att_shift_workType=Type de travail
att_shift_normalWork=Travail normal
att_shift_holidayOt=HS vacances
att_shift_attShiftStartDateRemark=Exemple: La date de début du cycle est le No. 22, avec une période de trois jours, puis No. 22/23/24 est en classe A/B/C, et No. 19/20/21 est en classe A. /B classe / C classe, avant et après la date et ainsi de suite.
att_shift_isShiftWithinMonthRemark1=Rotation au cours du mois, le cycle se déplace uniquement jusqu'au dernier jour de chaque mois, non programmé consécutivement sur le mois;
att_shift_isShiftWithinMonthRemark2=Rotation non mensuelle, le cycle est parcouru au dernier jour de chaque mois, si un cycle n'est pas terminé, continuez au mois suivant, etc.
att_shift_workTypeRemark1=Remarque: Lorsque le type de travail est sélectionné comme heures supplémentaires un jour de repos, la fréquentation ne sera pas calculée le jour d'un jour férié.
att_shift_workTypeRemark2=Week-end HS, la marque d'heures supplémentaires par défaut est le jour de repos et l'ordinateur calcule automatiquement les heures supplémentaires. Aucune demande d'heures supplémentaires n'est requise. Les heures de travail de la journée sont enregistrées comme heures supplémentaires et la présence n'est pas comptabilisée pendant les vacances.
att_shift_workTypeRemark3=HS vacances, la marque d'heures supplémentaires par défaut est les jours fériés et l'ordinateur calcule automatiquement les heures supplémentaires, aucune application d'heures supplémentaires n'est requise et les heures de travail de la journée sont enregistrées comme heures supplémentaires;
att_shift_attendanceModeRemark1=À l'exception du passage normal par rotation, il n'est pas considéré comme des heures supplémentaires anticipées ou retardées, par exemple:
att_shift_attendanceModeRemark2=1.Aucun enregistrement n'est requis ou une carte valide est utilisée une fois par jour, aucune heure supplémentaire n'est comptée ;
att_shift_attendanceModeRemark3=2.Type de travail: travail normal, mode de présence: glissement de carte gratuit, puis la rotation de jour est considérée comme du temps de travail effectif;
att_shift_periodStartMode=Type de début de cycle
att_shift_periodStartModeByPeriod=Date de début de la période
att_shift_periodStartModeBySch=Date de début de l'équipe
att_shift_addTimeSlotToShift=S'il faut ajouter le créneau horaire de ce quart de travail
#=====================================================================
#分组
att_group_editGroup=Modifier le personnel pour le groupe
att_group_browseGroupPerson=Parcourir le personnel de groupe
att_group_list=Liste de Groupe
att_group_placeholderNo=Il est recommandé de commencer par G, tel que G1
att_group_placeholderName=Il est recommandé de commencer par G ou finir avec "group".
att_widget_deptHint=Remarque: Importer tout le personnel sous le département sélectionné
att_widget_searchType=Requête conditionnelle
att_widget_noPerson=N'a choisi personne
#分组排班
#部门排班
att_deptSch_existsDept=Il y a une rotation départementale dans le département et il n'est pas autorisé de supprimer le département.
#人员排班
att_personSch_view=Voir la programmation du personnel
#临时排班
att_schedule_type=Type de Programme
att_schedule_tempType=Type temporaire
att_schedule_normal=Programme normal
att_schedule_intelligent=Recherche de classe intelligente
att_tempSch_scheduleType=Type de programme
att_tempSch_startDate=Date de début
att_tempSch_endDate=Date de fin
att_tempSch_attendanceMode=Méthode de présence
att_tempSch_overtimeMode=Mode d'heures supplémentaires
att_tempSch_overtimeRemark=Marque d'heures supplémentaires
att_tempSch_existsDept=Il y a une rotation départementale temporaire dans le département, et il n'est pas autorisé de supprimer le département.
att_schedult_opAddTempSch=Nouvelle Rotation Temporaire
att_schedule_cleanEndDate=Heure de fin vide
att_schedule_selectOne=Le programme normal ne peut choisir qu'une seule rotation!
att_schedule_selectPerson=Veuillez d'abord sélectionner le personnel!
att_schedule_selectDept=Veuillez d'abord sélectionner le département!
att_schedule_selectGroup=Veuillez d'abord sélectionner le groupe!
att_schedule_selectOneGroup=Un seul groupe peut être sélectionné!
att_schedule_arrange=Veuiller choisir une rotation!
att_schedule_leave=Congé
att_schedule_trip=Voyage
att_schedule_out=Sortie
att_schedule_off=Repos
att_schedule_makeUpClass=joindre
att_schedule_class=Ajuster
att_schedule_holiday=Vacances
att_schedule_offDetail=Ajuster Repos
att_schedule_makeUpClassDetail=Joindre la présence
att_schedule_classDetail=Ajuster la Rotation
att_schedule_holidayDetail=Vacances
att_schedule_noSchDetail=Pas programmé
att_schedule_normalDetail=Normal
att_schedule_normalSchInfo=Centre de rotation: Pas de rotation de jours
att_schedule_multipleInterSchInfo=Rotation séparée par des virgules: Rotations de jours multiple
att_schedule_inderSchFirstDayInfo=Rotation à travers le décalage arrière de la grille: La rotation de jours est enregistrée comme le premier jour
att_schedule_inderSchSecondDayInfo=Rotation à travers le décalage avant de la grille: La rotation de jours est enregistrée comme le deuxième jour
att_schedule_timeConflict=Conflit avec la période de rotation existante, non autorisé à enregistrer!
#=====================================================================
att_excp_notExisetPerson=La Personne n'existe pas!
att_excp_leavePerson=Licenciement du personnel!
#补签单
att_sign_signTime=Heure de Pointage
att_sign_signDate=Date de Pointage
#请假
att_leave_arilName=Type de Congé
att_leave_image=Demande de photo de congé
att_leave_imageShow=Pas de Photos
att_leave_imageType=Astuce d'erreur: le format d'image n'est pas correct, supporte le format: JPEG, GIF, PNG!
att_leave_imageSize=Astuce d'erreur: L'image sélectionnée est trop grande, la taille maximale de l'image est 4MB!
att_leave_leaveLongDay=Durée (jours)
att_leave_leaveLongHour=Durée (heure)
att_leave_leaveLongMinute=Durée (minutes)
att_leave_endNoLessAndEqualStart=L'heure de fin ne peut pas être inférieure ou égale à l'heure de début
att_leave_typeNameNoExsists=Les faux noms de classe n'existent pas
att_leave_startNotNull=L'heure de début ne peut pas être vide
att_leave_endNotNull=L'heure de fin ne peut pas être vide
att_leave_typeNameConflict=Le faux nom de type entre en conflit avec le nom du statut de participation
#出差
att_trip_tripLongDay=Durée du voyage(jour)
att_trip_tripLongMinute=Durée du voyage(minutes)
att_trip_tripLongHour=Temps de voyage (heures)
#外出
att_out_outLongDay=Durée de sortie(jour)
att_out_outLongMinute=Durée de sortie(minutes)
att_out_outLongHour=Temps passé (temps)
#加班
att_overtime_type=Type d'heures supplémentaires
att_overtime_normal=Heures supplémentaires Normale
att_overtime_rest=Heures supplémentaires en Weekend
att_overtime_overtimeLong=Durée des heures supplémentaires(minutes)
att_overtime_overtimeHour=heures supplémentaires (heures)
att_overtime_notice=Le temps d'application des heures supplémentaires n'est pas autorisé pendant plus d'une journée!
att_overtime_minutesNotice=Le temps d'application des heures supplémentaires ne peut être inférieur au temps minimum d'heures supplémentaires!
#调休补班
att_adjust_type=Type d'ajustement
att_adjust_adjustDate=Date d'Ajustement
att_adjust_shiftName=Joindre la Rotation de Présence
att_adjust_selectClass=Veuillez sélectionner le nom de la rotation qui nécessite une présence jointe.
att_shift_notExistShiftWorkDate={1} planification de poste à {0} et le repos n'est pas autorisé à postuler pour un poste de rattrapage!
att_adjust_shiftPeriodStartMode=L'équipe sélectionnée pour l'équipe de rattrapage, si la date de début est par équipe, la valeur par défaut est le 0
att_adjust_shiftNameNoNull=Le changement d'équipe ne peut pas être vide
att_adjust_shiftNameNoExsist=Aucun décalage de décalage n'existe
#调班
att_class_type=Type d'Ajustement
att_class_sameTimeMoveShift=Ajustez la rotation personnelle dans la même journée
att_class_differenceTimeMoveShift=Ajuster la rotation personnelle sur les autres jours
att_class_twoPeopleMove=Deux personnes échangent
att_class_moveDate=Ajuster la Date
att_class_shiftName=Nom du Programme Original
att_class_moveShiftName=La nouvelle rotation ajustée ne peut pas être vide.
att_class_movePersonPin=Ajuster ID Personnel
att_class_movePersonName=Ajuster Nom du Personnel
att_class_movePersonLastName=Ajuster Nom de famille du Personnel
att_class_moveDeptName=Ajuster Nom du Département
att_class_personPin=ID Personnel
att_class_shiftNameNoNull=La nouvelle rotation ajustée ne peut pas être vide.
att_class_personPinNoNull=L'ID personnel de la nouvelle personne ne peut pas être vide!
att_class_isNotExisetSwapPersonPin=La nouvelle personne Ajustée n'existe pas, veuillez rajouter!
att_class_personNoSame=Vous ne pouvez pas ajuster pour la même personne, veuillez réessayer.
att_class_outTime=La date d'ajustement et la date de transfert ne peuvent pas dépasser un mois！
att_class_shiftNameNoExsist=Le décalage d'ajustement n'existe pas
att_class_swapPersonNoExisist=La personne de swap n'existe pas
att_class_dateNoSame=Changements personnels à différentes dates, les dates ne peuvent pas être les mêmes
#=====================================================================
#节点
att_node_name=Nœud
att_node_type=Type de nœud
att_node_leader=Leader direct
att_node_leaderNode=Noeud leader direct
att_node_person=Personne désignée
att_node_position=Attribuer position
att_node_choose=Sélectionnez position
att_node_personNoNull=Personnel ne peut pas être vide!
att_node_posiitonNoNull=Position ne peut pas être vide
att_node_placeholderNo=Il est recommandé de commencer par N, tel que N01.
att_node_placeholderName=Il est recommandé de commencer par une position ou un nom, en terminant par un nœud, tel que le nœud Manager.
att_node_searchPerson=Entrer les critères de recherche
att_node_positionIsExist=La position existe déjà dans les données du nœud, veuillez sélectionner à nouveau la position.
#流程
att_flow_type=Type de Flux
att_flow_rule=Règle de flux
att_flow_rule0=Inférieur ou égal à 1 jour
att_flow_rule1=Plus d'un jour et inférieur ou égal à 3 jours
att_flow_rule2=Plus de 3 jours et inférieur ou égal à 7 jours
att_flow_rule3=Plus de 7 jours
att_flow_node=Noeud d'approbation
att_flow_start=Début du Flux
att_flow_end=Fin du Flux
att_flow_addNode=Ajouter Noeud
att_flow_placeholderNo=Il est recommandé de commencer par F, tel que F01.
att_flow_placeholderName=Il est recommandé de commencer par type, de terminer par "flow", eg.Leave Flow.
att_flow_tips=Remarque: L'ordre d'approbation des nœuds est de haut en bas, et vous pouvez faire glisser le tri après avoir sélectionné.
#申请
att_apply_personPin=Application de l'ID du Personnel
att_apply_type=Type d'Exception
att_apply_flowStatus=Statut global du flux
att_apply_start=Lancer une application
att_apply_flowing=en attente
att_apply_pass=Accepté
att_apply_over=Fin
att_apply_refuse=Rejeté
att_apply_revoke=Revoke
att_apply_except=Exception
att_apply_view=Voir détails
att_apply_leaveTips=La personne a une demande de congé pendant cette période!
att_apply_tripTips=Le personnel a une demande de voyage d'affaires pendant cette période!
att_apply_outTips=Le personnel a fait une demande de sortie pendant cette période!
att_apply_overtimeTips=Le personnel a des demandes d'heures supplémentaires pendant cette période!
att_apply_adjustTips=Pendant cette période, le personnel peut faire la demande d'une répétition!
att_apply_classTips=Le personnel a une demande de rotation pendant cette période!
#审批
att_approve_wait=Validation en attente
att_approve_refuse=Pas Accepté
att_approve_reason=Raison
att_approve_personPin=ID de l'approbateur
att_approve_personName=Nom de l'approbateur
att_approve_person=Approbateur
att_approve_isPass=Approuver ou non?
att_approve_status=État actuel du nœud
att_approve_tips=Le point de temps existe déjà dans le flux et ne peut être répété.
att_approve_tips2=Le nœud de flux n'a pas été configuré, veuillez contacter l'administrateur pour la configuration.
att_approve_offDayConflicts={0} n'est pas planifié ou planifié le {1} et le reste n'est pas autorisé.
att_approve_shiftConflicts={0} a déjà un quart dans {1} et ne permet pas de postuler pour des quarts le jour ouvrable!
att_approve_shiftNoSch={0} Aucune application de quart n'est autorisée lorsque le quart n'est pas planifié le {1}!
att_approve_classConflicts=La date de programmation est une date de non-rotation et ne peut pas être ajoutée.
att_approve_selectTime=Le temps de sélection déterminera le processus selon les règles
att_approve_withoutPermissionApproval=Il y a un workflow sans autorisation pour approbation, veuillez vérifier!
#=====================================================================
#考勤计算
att_op_calculation=Calcul de présence
att_op_calculation_notice=Les données de présence ont été calculées en background, veuillez réessayer plus tard!
att_op_calculation_leave=Y compris le personnel démissionnaire
att_statistical_choosePersonOrDept=Veuillez sélectionner département ou personnel!
att_statistical_sureCalculation=Êtes-vous sûr de vouloir effectuer les calculs de présence?
att_statistical_filter=La condition de filtration est prête!
att_statistical_initData=Initialisation de la base de données terminée!
att_statistical_exception=Initialisation des données d'exception terminée!
att_statistical_error=Échec du calcul d'absence!
att_statistical_begin=commencer à calculer!
att_statistical_end=Fin du calcul!
att_statistical_noticeTime=Période facultative de présence: des deux premiers mois au jour!
att_statistical_remarkHoliday=V
att_statistical_remarkClass=C
att_statistical_remarkNoSch=NP
att_statistical_remarkRest=R
#原始记录表
att_op_importAccRecord=Importer le dossier de contrôle d'accès
att_op_importParkRecord=Importer le dossier de Parking
att_op_importInsRecord=Importer le dossier faceKiosk
att_op_importPidRecord=Importer le dossier certificats de personnel
att_op_importVmsRecord=Importer record vidéo
att_op_importUSBRecord=Importer record Clé USB
att_transaction_noAccModule=Pas de module de contrôle d'accès!
att_transaction_noParkModule=Pas de module Parking!
att_transaction_noInsModule=Pas de module faceKiosk!
att_transaction_noPidModule=Pas de module de certificat de personnel!
att_transaction_exportRecord=Exporter les enregistrements originaux
att_transaction_exportAttPhoto=Exporter les photos de présence
att_transaction_exportDate=Date d'exportation
att_transaction_fileIsTooLarge=Le fichier exporté est trop volumineux, veuillez réduire la plage de dates
att_statistical_attDatetime=Temps de présence
att_statistical_attPhoto=Photo de présence
att_statistical_attDetail=Détails de présence
att_statistical_acc=Périphérique de contrôle d'accès
att_statistical_att=Périphérique de gestion de présence
att_statistical_park=Caméra LPR
att_statistical_faceRecognition=Périphérique de reconnaissance faciale
att_statistical_app=Périphériques Mobiles
att_statistical_vms=Périphériques vidéo
att_statistical_psg=Équipement de canal
att_statistical_dataSources=Sources de données
att_transaction_SyncRecord=Synchroniser les enregistrements de présence
#日打卡详情表
att_statistical_dayCardDetail=Détails de l'enregistrement
att_statistical_cardDate=Date d'enregistrement
att_statistical_cardNumber=Nombre d'enregistrement
att_statistical_earliestTime=Le temps le plus tôt
att_statistical_latestTime=Le temps le plus tard
att_statistical_cardTime=Temps de pointage
#请假汇总表
att_statistical_leaveDetail=Détails de Congé
#日明细报表
att_statistical_attDate=Date de présence
att_statistical_week=Semaine
att_statistical_shiftInfo=Information de Rotation
att_statistical_shiftTimeData=Travail en marche/arrêt
att_statistical_cardValidData=Temps de pointage
att_statistical_cardValidCount=Comptage de pointage
att_statistical_lateCount=Comptage des retards
att_statistical_lateMinute=Minute de retard
att_statistical_earlyCount=Comptage des avances
att_statistical_earlyMinute=Minute en avance
att_statistical_countData=Nombre de données
att_statistical_minuteData=Minutes de Données
att_statistical_attendance_minute=Présence (minutes)
att_statistical_overtime_minute=Heures supplémentaires (minutes)
att_statistical_unusual_minute=Anormal (minutes)
#月明细报表
att_monthdetail_should_hour=Doit (temps)
att_monthdetail_actual_hour=Réel (temps)
att_monthdetail_valid_hour=Valide (temps)
att_monthdetail_absent_hour=Achèvement (temps)
att_monthdetail_leave_hour=Congé (temps)
att_monthdetail_trip_hour=Voyage d'affaires (heure)
att_monthdetail_out_hour=Sortir (heure)
att_monthdetail_should_day=Doit (jour)
att_monthdetail_actual_day=Réel (jour)
att_monthdetail_valid_day=Effectif (jour)
att_monthdetail_absent_day=Achèvement (jour)
att_monthdetail_leave_day=Congé (jour)
att_monthdetail_trip_day=Voyage d'affaires (jour)
att_monthdetail_out_day=Sortir (jour)
#月统计报表
att_statistical_late_minute=Durée (minutes)
att_statistical_early_minute=Durée (minutes)
#部门统计报表
#年度统计报表
att_statistical_should=Doit
att_statistical_actual=Réel
att_statistical_valid=Valide
att_statistical_numberOfTimes=Nombre de fois
att_statistical_usually=Jour de travail
att_statistical_rest=Weekend
att_statistical_holiday=Vacances
att_statistical_total=Total
att_statistical_month=Statistiques du mois
att_statistical_year=Statistiques de l'année
att_statistical_attendance_hour=Présence (heure)
att_statistical_attendance_day=Présence (Jour)
att_statistical_overtime_hour=Heures supplémentaires (heure)
att_statistical_unusual_hour=Exception (heure)
att_statistical_unusual_day=Anormal (Jour)
#考勤设备参数
att_deviceOption_query=Afficher les paramètres du périphérique
att_deviceOption_noOption=Aucune information sur les paramètres, veuillez d'abord obtenir les paramètres de l'appareil
att_deviceOption_name=Nom du paramètre
att_deviceOption_value=Valeur du paramètre
att_deviceOption_UserCount=Numéro d'utilisateur
att_deviceOption_MaxUserCount=Nombre maximal d'utilisateurs
att_deviceOption_FaceCount=Nombre de modèles de visage actuels
att_deviceOption_MaxFaceCount=Nombre maximal de modèles de visage
att_deviceOption_FacePhotoCount=Nombre actuel d'images faciales
att_deviceOption_MaxFacePhotoCount=Nombre maximum d'images faciales
att_deviceOption_FingerCount=Numéro d'empreinte digitale actuel
att_deviceOption_MaxFingerCount=Nombre maximal d'empreintes digitales
att_deviceOption_FingerPhotoCount=Nombre d'images d'empreintes digitales actuelles
att_deviceOption_MaxFingerPhotoCount=Nombre maximal d'images d'empreintes digitales
att_deviceOption_FvCount=Nombre actuel de veine du doigt
att_deviceOption_MaxFvCount=Nombre maximal de modèles de veines de doigts
att_deviceOption_FvPhotoCount=Nombre d'images actuelles de veines de doigts
att_deviceOption_MaxFvPhotoCount=Nombre d'images de veines de doigts
att_deviceOption_PvCount=Nombre de modèles de paumes actuels
att_deviceOption_MaxPvCount=Nombre maximum de veine de paume
att_deviceOption_PvPhotoCount=Images de paumes actuelles
att_deviceOption_MaxPvPhotoCount=Nombre maximal d'images de paumes
att_deviceOption_TransactionCount=Nombre actuel d'enregistrements
att_deviceOption_MaxAttLogCount=Nombre maximum d'enregistrements
att_deviceOption_UserPhotoCount=Photos des utilisateurs actuels
att_deviceOption_MaxUserPhotoCount=Nombre maximum de photos d'utilisateurs
att_deviceOption_FaceVersion=Version de l'algorithme de reconnaissance faciale
att_deviceOption_FPVersion=Version de l'algorithme de reconnaissance d'empreintes digitales
att_deviceOption_FvVersion=Version de l'algorithme de reconnaissance de veines de doigts
att_deviceOption_PvVersion=Version de l'algorithme de reconnaissance de Paume
att_deviceOption_FWVersion=Version du Firmware
att_deviceOption_PushVersion=Version du Push
#=====================================================================
#API
att_api_areaCodeNotNull=Le numéro de zone ne peut pas être vide
att_api_pinsNotNull=Les données de pin ne doivent pas être vides
att_api_pinsOverSize=La longueur des données de pin ne doit pas dépasser 500
att_api_areaNoExist=La zone n'existe pas
att_api_sign=Supplément
#=====================================================================
#休息时间段
att_leftMenu_breakTime=Pause
att_breakTime_startTime=Heure de début
att_breakTime_endTime=Heure de Fin
#=====================================================================
#导入U盘记录
att_import_uploadFileSuccess=Téléchargez le fichier avec succès, lancez l'analyse des données du fichier, veuillez patienter...
att_import_resolutionComplete=Une fois l'analyse terminée, commencez à mettre à jour la base de données.
att_import_snNoExist=Le périphérique d'assistance correspondant au fichier importé n'existe pas. Veuillez sélectionner à nouveau le fichier.
att_import_fileName_msg=Exigences de format de nom de fichier importé: le numéro de série de l'appareil commence par et est séparé par un trait de soulignement "_", par exemple："3517171600001_attlog.dat"。
att_import_notSupportFormat=Ce format n'est pas pris en charge pour le moment!
att_import_selectCorrectFile=Veuillez sélectionner le format de fichier correct!
att_import_fileFormat=format de fichier
att_import_targetFile=Fichier cible
att_import_startRow=Nombre de lignes au début de l'en-tête
att_import_startRowNote=La première ligne du format de données est d'importer des données, veuillez vérifier le fichier avant d'importer.
att_import_delimiter=Séparateur
#=====================================================================
#设备操作日志
att_device_op_log_op_type=Code d'opération
att_device_op_log_dev_sn=Numéro de série de l'appareil
att_device_op_log_op_content=Contenu opérationnel
att_device_op_log_operator_pin=Numéro d'opérateur
att_device_op_log_operator_name=Nom d'opérateur
att_device_op_log_op_time=Temps d'opération
att_device_op_log_op_who_value=Valeur de l'objet d'opération
att_device_op_log_op_who_content=Description de l'objet d'opération
att_device_op_log_op_value1=Objet d'opération 2
att_device_op_log_op_value_content1=Description de l'objet d'opération 2
att_device_op_log_op_value2=Objet d'opération 3
att_device_op_log_op_value_content2=Description de l'objet d'opération 3
att_device_op_log_op_value3=Objet d'opération 4
att_device_op_log_op_value_content3=Description de l'objet d'opération 4
#操作日志的操作类型
att_device_op_log_opType_0=Allumer
att_device_op_log_opType_1=Eteindre
att_device_op_log_opType_2=Échec de la vérification
att_device_op_log_opType_3=Alarme
att_device_op_log_opType_4=Entrer dans le menu
att_device_op_log_opType_5=Modifier les paramètres
att_device_op_log_opType_6=Enregistrer empreinte digitale
att_device_op_log_opType_7=Enregistrer mot de passe
att_device_op_log_opType_8=Enregistrer carte HID
att_device_op_log_opType_9=Supprimer utilisateur
att_device_op_log_opType_10=Supprimer empreinte digitale
att_device_op_log_opType_11=Supprimer mot de passe
att_device_op_log_opType_12=Supprimer carte RF
att_device_op_log_opType_13=Effacer les données
att_device_op_log_opType_14=Créer carte MF
att_device_op_log_opType_15=Enregistrer carte MF
att_device_op_log_opType_16=Enregistrer carte MF
att_device_op_log_opType_17=Supprimer enregistrement carte MF
att_device_op_log_opType_18=Effacer contenu carte MF
att_device_op_log_opType_19=Déplacer les données d'enregistrement vers la carte
att_device_op_log_opType_20=Copiez les données de la carte vers la machine
att_device_op_log_opType_21=Définir le temps
att_device_op_log_opType_22=Réglages d'usine
att_device_op_log_opType_23=Supprimer les enregistrements d'entrée et de sortie
att_device_op_log_opType_24=Effacer les privilèges d'administrateur
att_device_op_log_opType_25=Modifier les paramètres du groupe de contrôle d'accès
att_device_op_log_opType_26=Modifier les paramètres d'accès utilisateur
att_device_op_log_opType_27=Modifier la période de temps d'accès
att_device_op_log_opType_28=Modifier les paramètres de combinaison de déverrouillage
att_device_op_log_opType_29=Déverrouiller
att_device_op_log_opType_30=Enregistrer les nouveaux utilisateurs
att_device_op_log_opType_31=Modifier les attributs d'empreinte digitale
att_device_op_log_opType_32=Alarme sous contrainte
att_device_op_log_opType_34=Anti-passback
att_device_op_log_opType_35=Supprimer les photos de présence
att_device_op_log_opType_36=Modifier les informations utilisateur
att_device_op_log_opType_37=Vacances
att_device_op_log_opType_38=Restaurer les données
att_device_op_log_opType_39=Sauvegarde des données
att_device_op_log_opType_40=Télécharger vers clé USB
att_device_op_log_opType_41=Télécharger de clé USB
att_device_op_log_opType_42=Chiffrement des enregistrements de présence sur clé USB
att_device_op_log_opType_43=Supprimer enregistrement après téléchargement réussi de la clé USB
att_device_op_log_opType_53=Commutateur sortant
att_device_op_log_opType_54=Capteur de porte
att_device_op_log_opType_55=Alarme
att_device_op_log_opType_56=Restaurer les paramètres
att_device_op_log_opType_68=Photo d'utilisateur enregistré
att_device_op_log_opType_69=Modifier photos d'utilisateurs
att_device_op_log_opType_70=Modifier nom d'utilisateur
att_device_op_log_opType_71=Modifier les autorisations des utilisateurs
att_device_op_log_opType_76=Modifier les paramètres réseau IP
att_device_op_log_opType_77=Modifier le masque des paramètres réseau
att_device_op_log_opType_78=Modifier paramètres de réseau gateway
att_device_op_log_opType_79=Modifier les paramètres réseau DNS
att_device_op_log_opType_80=Modifier le mot de passe de configuration de la connexion
att_device_op_log_opType_81=Modifier les paramètres de connexion ID de l'appareil
att_device_op_log_opType_82=Modifier l'adresse du serveur cloud
att_device_op_log_opType_83=Modifier le port du serveur cloud
att_device_op_log_opType_87=Modifier les paramètres d'enregistrement du contrôle d'accès
att_device_op_log_opType_88=Modifier l'indicateur de paramètre de visage
att_device_op_log_opType_89=Modifier l'indicateur de paramètre d'empreinte digitale
att_device_op_log_opType_90=Modifier l'indicateur de paramètre de veine du doigt
att_device_op_log_opType_91=Modifier l'indicateur de paramètre d'empreinte de paume
att_device_op_log_opType_92=Indicateur de mise à niveau de clé USB
att_device_op_log_opType_100=Modifier informations de carte RF
att_device_op_log_opType_101=Enregistrer visage
att_device_op_log_opType_102=Modifier les autorisations du personnel
att_device_op_log_opType_103=Supprimer les autorisations du personnel
att_device_op_log_opType_104=Ajouter les autorisations du personnel
att_device_op_log_opType_105=Supprimer l'enregistrement de contrôle d'accès
att_device_op_log_opType_106=Supprimer visage
att_device_op_log_opType_107=Supprimer les photos du personnel
att_device_op_log_opType_108=Modifier les paramètres
att_device_op_log_opType_109=Sélectionner WIFI SSID
att_device_op_log_opType_110=activer le proxy
att_device_op_log_opType_111=modification proxyip
att_device_op_log_opType_112=modification port proxy
att_device_op_log_opType_113=Modifier mot de passe de la personne
att_device_op_log_opType_114=Modifier les informations de visage
att_device_op_log_opType_115=Modifier le mot de passe de l'opérateur
att_device_op_log_opType_116=Reprendre les paramètres de contrôle d'accès
att_device_op_log_opType_117=erreur de saisie du mot de passe de l'opérateur
att_device_op_log_opType_118=verrouillage du mot de passe de l'opérateur
att_device_op_log_opType_120=Modifier la longueur des données de la carte Legic
att_device_op_log_opType_121=Enregistrer la veine du doigt
att_device_op_log_opType_122=Modifier la veine du doigt
att_device_op_log_opType_123=Supprimer la veine du doigt
att_device_op_log_opType_124=Enregistrer l'empreinte palmaire
att_device_op_log_opType_125=Modifier l'impression palmaire
att_device_op_log_opType_126=Supprimer l'empreinte palmaire
#操作对象描述
att_device_op_log_content_pin=ID utilisateur:
att_device_op_log_content_alarm=Alarme:
att_device_op_log_content_alarm_reason=Raison de l'alarme:
att_device_op_log_content_update_no=Modifier le numéro d'article:
att_device_op_log_content_update_value=Modifier valeur:
att_device_op_log_content_finger_no=Numéro d'empreinte digitale:
att_device_op_log_content_finger_size=Longueur du modèle d'empreinte digitale:
#=====================================================================
#工作流
att_flowable_datetime_to=à
att_flowable_todomsg_leave=Appobation de congés
att_flowable_todomsg_sign=Ajouter l'approbation du journal
att_flowable_todomsg_overtime=Approbation des heures supplémentaires
att_flowable_notifymsg_leave=Notification d'application de congé
att_flowable_notifymsg_sign=Ajouter notification de journal
att_flowable_notifymsg_overtime=Notification des heures supplémentaires
att_flowable_shift=Rotation:
att_flowable_hour=heure
att_flowable_todomsg_trip=Approbation de voyage d'affaires
att_flowable_notifymsg_trip=voyage d'affaires
att_flowable_todomsg_out=Approbation de sortie
att_flowable_notifymsg_out=Notification de sortie
att_flow_apply=Application
att_flow_applyTime=temps d'application
att_flow_approveTime=Temps de traitement
att_flow_operateUser=Critique
att_flow_approve=Approbation
att_flow_approveComment=annotation
att_flow_approvePass=Résultats d'approbation
att_flow_status_processing=Approval
#=====================================================================
#biotime
att_h5_pers_personIdNull=ID d'employé ne peut pas être vide
att_h5_attPlaceNull=Le lieu d'enregistrement ne peut pas être vide
att_h5_attAreaNull=La zone de présence ne peut pas être vide
att_h5_pers_personNoExist=Le numéro d'employé n'existe pas
att_h5_signRemarkNull=Les remarques ne peuvent pas être vides
att_h5_common_pageNull=Erreur de paramètre de pagination
att_h5_taskIdNotNull=L'ID du nœud de tâche ne peut pas être vide
att_h5_auditResultNotNull=Le résultat d'approbation ne peut pas être vide
att_h5_latLongitudeNull=La longitude et la latitude ne peuvent pas être vides
att_h5_pers_personIsNull=L'ID d'employé n'existe pas
att_h5_pers_personIsNotInArea=La personne n'a pas défini la zone
att_h5_mapApiConnectionsError=Erreur de connexion de l'API de la carte
att_h5_googleMap=Google Map
att_h5_gaodeMap=Gaode Map
att_h5_defaultMap=Carte par défaut
att_h5_shiftTime=Temps de rotation
att_h5_signTimes=Temps de réapprovisionnement
att_h5_enterKeyWords=Veuillez entrer les mots clés:
att_h5_mapSet=Paramètres de la carte de présence
att_h5_setMapApiAddress=Définir le paramètre de la carte
att_h5_MapSetWarning=Si vous changez de carte, l'adresse de connexion du terminal mobile saisie ne correspondra pas à la latitude et à la longitude, veuillez modifier avec prudence!
att_h5_mapSelect=Sélection de la carte
att_h5_persNoHire=L'employé n'a pas encore rejoint l'entreprise pour le moment.
att_slef_apiKey=API-KEY
att_self_apiJsKey=API-JS-KEY
att_self_noComputeAtt=La présence de la journée n'a pas encore été comptabilisée.
att_self_noSignRecord=Pas d'enregistrement de pointage dans la journée
att_self_imageUploadError=Échec du téléchargement de l'image
att_self_attSignAddressAreaIsExist=Il y a déjà des points d'enregistrement dans la zone
att_self_signRuleIsError=Le temps de réapprovisionnement actuel n'est pas dans le délai de réapprovisionnement autorisé.
att_self_signAcrossDay=Le programme de journée transversale ne peut pas être signé!
att_self_todaySignIsExist=Il y a déjà des correctifs ajoutés aujourd'hui!
att_self_signSetting=Réglage de signe
att_self_allowSign=Autoriser le signe:
att_self_allowSignSuffix=jours, record de présence dans
att_self_onlyThisMonth=Seulement ce mois-ci
att_self_allowAcrossMonth=Autoriser mois transversal
att_self_thisTimeNoSch=Il n'y a pas de rotation dans la période actuelle!
att_self_revokeReason=Motif de révocation:
att_self_revokeHint=Veuillez entrer le motif de l'annulation dans les 20 mots pour révision
att_self_persSelfLogin=Connexion en libre-service pour les employés
att_self_isOpenSelfLogin=S'il faut démarrer l'entrée de connexion en libre-service des employés
att_self_applyAndWorkTimeOverlap=Temps d'application et temps de travail se chevauchent
att_apply_DurationIsZero=La durée d'application est 0, aucune application n'est autorisée
att_sign_mapWarn=Échec du chargement de la carte, veuillez vérifier la connexion réseau et la valeur KEY de la carte
att_admin_applyWarn=L'opération a échoué, il y a des gens qui ne sont pas programmés ou le temps d'application n'est pas dans la portée du programme!({0})
att_self_getPhotoFailed=L'image n'existe pas
att_self_view=Voir
# 二维码
att_param_qrCodeUrl=Url de code QR
att_param_qrCodeUrlHref=Adresse du serveur: port
att_param_appAttQrCode=Code QR de présence sur mobile
att_param_timingFrequency=intervalle de Temps : 5-59 minutes ou 1-24 heures
att_sign_signTimeNotNull=L'heure du journal d'ajout ne peut pas être vide
att_apply_overLastMonth=Les applications ont commencé plus longtemps que le mois dernier
att_apply_withoutDetail=Pas de détails sur le processus
att_flowable_noAuth=Veuillez utiliser le compte super administrateur pour voir
att_apply_overtimeOverMaxTimeLong=Les heures supplémentaires sont supérieures aux heures supplémentaires maximales
# 考勤设置参数符号
att_other_arrive=√
att_other_late=En Retard
att_other_early=En avance
att_other_absent=Kuang
att_other_noSignIn=[Manque de
att_other_noSignOff=Manque de]
att_other_leave=Faux
att_other_overtime=Ajouter
att_other_off=Ajustable
att_other_classes=Remplir
att_other_trip=Faible
att_other_out=À l'extérieur
att_other_incomplete=Incomplet
att_other_outcomplete=缺
# 服务器下发命令
att_devCmd_submitTime=Soumettre l'heure
att_devCmd_returnedResult=Retourne le résultat
att_devCmd_returnTime=Heure de retour
att_devCmd_content=Contenu de la commande
att_devCmd_clearCmd=Effacer la liste de commandes
# 实时点名
att_realTime_selectDept=Veuillez sélectionner le département
att_realTime_noSignPers=Pas de personnel de connexion
att_realTime_signPers=Connecté
att_realTime_signMonitor=Moniteur de connexion
att_realTime_signDateTime=Heure d'enregistrement
att_realTime_realTimeSet=Paramètre d'appel en temps réel
att_realTime_openRealTime=Activer l'appel en temps réel
att_realTime_rollCallEnd=L'appel en temps réel se termine
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=Planifié
att_personSch_cycleSch=Planification des cycles
att_personSch_cleanCycleSch=Programme de cycle de nettoyage
att_personSch_cleanTempSch=Effacer la planification temporaire
att_personSch_personCycleSch=programme de cycle de personne
att_personSch_deptCycleSch=Calendrier du cycle de service
att_personSch_groupCycleSch=Planification du cycle de groupe
att_personSch_personTempSch=Planification du personnel temporaire
att_personSch_deptTempSch=Horaire temporaire du service
att_personSch_groupTempSch=Programme temporaire de groupe
att_personSch_checkGroupFirst=Veuillez cocher le groupe de gauche ou les personnes dans la liste de droite pour opérer!
att_personSch_sureDeleteGroup=Êtes-vous sûr de vouloir supprimer {0} et le calendrier correspondant au groupe?
att_personSch_sch=Calendrier
att_personSch_delSch=Supprimer le programme
#考勤计算
att_statistical_sureAllCalculate=Êtes-vous sûr d'effectuer le calcul de présence pour tout le personnel?
# 异常管理
att_exception_downTemplate=Télécharger le modèle d'importation
att_exception_signImportTemplate=Modèle d'importation de signe
att_exception_leaveImportTemplate=Quitter le modèle d'importation
att_exception_overtimeImportTemplate=Modèle d'importation des heures supplémentaires
att_exception_adjustImportTemplate=Ajuster le modèle d'importation
att_exception_cellDefault=Champ non obligatoire
att_exception_cellRequired=Champ obligatoire
att_exception_cellDateTime=Champ obligatoire, le format de l'heure est aaaa-MM-jj HH: mm: ss, tel que: 2020-07-07 08:30:00
att_exception_cellLeaveTypeName=Champ obligatoire, tel que: 'congé personnel', 'congé de mariage', 'congé de maternité', 'congé de maladie', 'congé annuel', 'congé de deuil', 'congé familial', 'congé d'allaitement', 'voyage d'affaires', 'sortie' Nom arrogant
att_exception_cellOvertimeSign=Champ obligatoire, tel que: 'heures supplémentaires normales', 'heures supplémentaires les jours de repos', 'heures supplémentaires les jours fériés'
att_exception_cellAdjustType=Champ obligatoire, tel que: 'transfer off', 'make up class'
att_exception_cellAdjustDate=Champ obligatoire, le format de l'heure est aaaa-MM-jj, tel que: 2020-07-07
att_exception_cellShiftName=Champ obligatoire lorsque le type d'ajustement est shift shift
att_exception_refuse=Refuser
att_exception_end=Fin anormale
att_exception_delete=Supprimer
att_exception_stop=Pause
# 时间段
att_timeSlot_normalTimeAdd=Ajouter un créneau horaire normal
att_timeSlot_elasticTimeAdd=Ajouter un créneau horaire élastique
# 班次
att_shift_addRegularShift=Ajouter un quart de travail normal
att_shift_addFlexibleShift=Ajouter un décalage flexible
# 参数设置
att_param_notLeaveSetting=Paramètre de calcul non faux
att_param_smallestUnit=Unité minimale
att_param_workDay=Jour de travail
att_param_roundingControl=Contrôle d'arrondi
att_param_abort=Down (rejeter)
att_param_rounding=arrondi
att_param_carry=Vers le haut (report)
att_param_reportSymbol=Symbole d'affichage du rapport
att_param_convertCountValid=Veuillez saisir un nombre et une seule décimale est autorisée
att_other_leaveThing=Chose
att_other_leaveMarriage=Mariage
att_other_leaveBirth=produit
att_other_leaveSick=Malade
att_other_leaveAnnual=année
att_other_leaveFuneral=Funérailles
att_other_leaveHome=Explorer
att_other_leaveNursing=Soins infirmiers
att_other_leavetrip=différence
att_other_leaveout=autre
att_common_schAndRest=Planification et repos
att_common_timeLongs=Durée
att_personSch_checkDeptOrPersFirst=Veuillez vérifier le département à gauche ou la personne dans la liste à droite!
att_personSch_checkCalendarFirst=Veuillez sélectionner la date que vous devez planifier en premier!
att_personSch_cleanCheck=Effacer le chèque
att_personSch_delTimeSlot=Effacer la période sélectionnée
att_personSch_repeatTimeSlotNoAdd=Ne pas ajouter quand il y a une période de temps répétée!
att_personSch_showSchInfo=Afficher les détails du planning
att_personSch_sureToCycleSch=Êtes-vous sûr de planifier {0} périodiquement?
att_personSch_sureToTempSch=Êtes-vous sûr de planifier temporairement {0}?
att_personSch_sureToCycleSchDeptOrGroup=Êtes-vous sûr de planifier périodiquement tout le personnel sous {0}?
att_personSch_sureToTempSchDeptOrGroup=Êtes-vous sûr de planifier temporairement tout le personnel sous {0}?
att_personSch_sureCleanCycleSch=Êtes-vous sûr de vouloir supprimer {0} de {1} à {2}?
att_personSch_sureCleanTempSch=Êtes-vous sûr de vouloir effacer {0} le décalage temporaire de {1} à {2}?
att_personSch_sureCleanCycleSchDeptOrGroup=Êtes-vous sûr de vouloir effacer le calendrier périodique de {1} à {2} pour toutes les personnes de moins de {0}?
att_personSch_sureCleanTempSchDeptOrGroup=Êtes-vous sûr de vouloir effacer le programme temporaire de {1} à {2} pour toutes les personnes de moins de {0}?
att_personSch_today=Aujourd'hui
att_personSch_timeSoltName=Nom de la période
att_personSch_export=Exporter la planification du personnel
att_personSch_exportTemplate=Exporter le modèle de quart de travail temporaire du personnel
att_personSch_import=Importer la planification temporaire du personnel
att_personSch_tempSchTemplate=Modèle de planification du personnel temporaire
att_personSch_tempSchTemplateTip=Veuillez sélectionner l'heure de début et l'heure de fin à planifier, téléchargez le modèle de calendrier dans cette date
att_personSch_opTip=Instructions d'opération
att_personSch_opTip1=1.vous pouvez utiliser la souris pour faire glisser le créneau horaire vers une seule date dans le contrôle du calendrier pour la planification.
att_personSch_opTip2=2.Dans le contrôle calendrier, double-cliquez sur une seule date à planifier.
att_personSch_opTip3=3.Dans le contrôle du calendrier, appuyez et maintenez la souris pour sélectionner plusieurs dates pour la planification.
att_personSch_schRules=Règles de planification
att_personSch_schRules1=1.Ordonnancement périodique: écrase l'ordonnancement précédent après la même date, qu'il n'y ait pas d'intersection.
att_personSch_schRules2=2.Ordonnancement temporaire: Il y a une intersection à la même date et l'ordonnancement temporaire précédent est écrasé plus tard. S'il n'y a pas d'intersection, elles existeront également.
att_personSch_schRules3=3.Période et Temporaire: S'il y a une intersection à la même date, la période sera temporairement couverte, et s'il n'y a pas d'intersection, elles existeront également simultanément.
att_personSch_schStatus=Statut de planification
#左侧菜单-排班管理
att_leftMenu_schDetails=Détails du planning
att_leftMenu_detailReport=Rapport détaillé de présence
att_leftMenu_signReport=Tableau des détails d'inscription
att_leftMenu_leaveReport=Quitter le formulaire de détails
att_leftMenu_abnormal=Table de présence anormale
att_leftMenu_yearLeaveSumReport=Congé annuelSumReport
att_leave_maxFileCount=Vous ne pouvez ajouter que 4 photos au maximum
#时间段
att_timeSlot_add=Définir le créneau horaire
att_timeSlot_select=Veuillez sélectionner une période de temps!
att_timeSlot_repeat=La période de temps "{0}" se répète!
att_timeSlot_overlapping=La période de temps "{0}" chevauche le temps de trajet de "{1}"!
att_timeSlot_addFirst=Veuillez d'abord définir la période de temps!
att_timeSlot_notEmpty=La période de temps correspondant au matricule {0} ne peut pas être vide!
att_timeSlot_notExist=La période "{1}" correspondant au matricule {0} n'existe pas!
att_timeSlot_repeatEx=La période de temps "{1}" correspondant au matricule {0} chevauche le temps de trajet de "{2}"
att_timeSlot_importRepeat=La période "{1}" correspondant au matricule {0} est répétée
att_timeSlot_importNotPin=Il n'y a personne avec le numéro {0} dans le système!
att_timeSlot_elasticTimePeriod=人员编号{0}，不能导入弹性时间段"{1}"!
#导入
att_import_overData=Le nombre actuel d'importations est {0}, dépassant la limite de 30 000, veuillez importer par lots!
att_import_existIllegalType={0} importé a un type illégal!
#验证方式
att_verifyMode_0=reconnaissance automatique
att_verifyMode_1=Empreinte digitale uniquement
att_verifyMode_2=Vérification du numéro de poste
att_verifyMode_3=Mot de passe uniquement
att_verifyMode_4=Carte uniquement
att_verifyMode_5=Empreinte digitale ou mot de passe
att_verifyMode_6=Empreinte digitale ou carte
att_verifyMode_7=Carte ou mot de passe
att_verifyMode_8=Numéro de travail plus empreinte digitale
att_verifyMode_9=Empreinte digitale et mot de passe
att_verifyMode_10=Carte plus empreinte digitale
att_verifyMode_11=Carte plus mot de passe
att_verifyMode_12=Empreinte digitale plus mot de passe plus carte
att_verifyMode_13=Identifiant professionnel plus empreinte digitale et mot de passe
att_verifyMode_14=(Numéro professionnel plus empreinte digitale) ou (carte plus empreinte digitale)
att_verifyMode_15=visage humain
att_verifyMode_16=Visage et empreinte digitale
att_verifyMode_17=Mot de passe Face plus
att_verifyMode_18=Carte Face Plus
att_verifyMode_19=Face plus carte d'empreinte digitale plus
att_verifyMode_20=Face plus empreinte digitale plus mot de passe
att_verifyMode_21=Veine du doigt
att_verifyMode_22=Veine du doigt et code
att_verifyMode_23=Carte veine plus doigt
att_verifyMode_24=Veine du doigt plus code plus carte
att_verifyMode_25=Imprimé palmier
att_verifyMode_26=Carte plus imprimé palmier
att_verifyMode_27=Impressions de paume et visage
att_verifyMode_28=Empreinte digitale et empreinte digitale
att_verifyMode_29=Impression de la paume, empreinte digitale et visage
# 工作流
att_flow_schedule=Progression de l'audit
att_flow_schedulePass=(Réussi)
att_flow_scheduleNot=(Non approuvé)
att_flow_scheduleReject=(Rejeté)
# 工作时长表
att_workTimeReport_total=Heures de travail totales
# 自动导出报表
att_autoExport_startEndTime=Heure de début et de fin
# 年假
att_annualLeave_setting=Réglage du solde des congés annuels
att_annualLeave_settingTip1=Pour utiliser la fonction d'équilibre des congés annuels, vous devez définir l'heure d'entrée pour chaque employé; lorsque l'heure d'entrée n'est pas définie, les congés annuels restants du tableau d'équilibre des congés annuels du personnel sont vides.
att_annualLeave_settingTip2=Si la date actuelle est supérieure à la date d'émission de compensation, cette modification prendra effet l'année suivante; si la date actuelle est inférieure à la date d'émission de compensation, lorsque la date d'émission de compensation est atteinte, elle sera effacée et le congé annuel sera réémis.
att_annualLeave_calculate=Date de compensation et d'émission des congés annuels
att_annualLeave_workTimeCalculate=Calculer en fonction du ratio du temps de travail
att_annualLeave_rule=Règle du temps de congé annuel
att_annualLeave_ruleCountOver=La limite de nombre maximum de set a été atteinte
att_annualLeave_years=Années seniors
att_annualLeave_eachYear=Chaque année
att_annualLeave_have=Oui
att_annualLeave_days=Jours de congé annuel
att_annualLeave_totalDays=Congé annuel total
att_annualLeave_remainingDays=Congé annuel restant
att_annualLeave_consecutive=Le paramètre de règle de congé annuel doit être des années consécutives
# 年假结余表
att_annualLeave_report=Bilan des congés annuels
att_annualLeave_validDate=Date valide
att_annualLeave_useDays=Utiliser {0} jours
att_annualLeave_calculateDays=Libération {0} jours
att_annualLeave_notEnough=Congé annuel insuffisant en {0}!
att_annualLeave_notValidDate={0} n'est pas dans la plage valide de congé annuel!
att_annualLeave_notDays={0} n'a pas de congé annuel!
att_annualLeave_tip1=Zhang San a rejoint le 1er septembre de l'année dernière
att_annualLeave_tip2=Réglage du solde des congés annuels
att_annualLeave_tip3=La date de compensation et d'émission est le 1er janvier de chaque année; elle est calculée en arrondissant selon le ratio de travail; si l'ancienneté est inférieure ou égale à 1, il y aura 3 jours de congé annuel, et si l'ancienneté est inférieure ou égale à 1, il y a 5 jours de congé annuel
att_annualLeave_tip4=Calcul des congés annuels
att_annualLeave_tip5=L'année dernière 09-01 ~ 12-31 profitez de 4 / 12x3=1.0 jours
att_annualLeave_tip6=Cette année 01-01 ~ 12-31 profitez de 4.0 jours (cette année 01-01 ~ 08-31 profitez de 8 / 12x3=2.0 jours   cette année 09-01 ~ 12-31 profitez de 4 / 12x5≈2.0 jours)
# att SDC
att_sdc_name=Équipement vidéo
att_sdc_wxMsg_firstData=Bonjour, vous avez un avis d'enregistrement de présence
att_sdc_wxMsg_stateData=Aucun sentiment de réussite de l'enregistrement de présence
att_sdc_wxMsg_remark=Rappel: le résultat final de la participation est soumis à la page des détails de l'enregistrement.
# 时间段
att_timeSlot_conflict=Le créneau horaire est en conflit avec d'autres créneaux horaires de la journée
att_timeSlot_selectFirst=Veuillez sélectionner le créneau horaire
# 事件中心
att_eventCenter_sign=Connexion de présence
#异常管理
att_exception_classImportTemplate=Modèle d'importation de classe
att_exception_cellClassAdjustType=Champ obligatoire, tel que: "{0}", "{1}", "{2}"
att_exception_swapDateDate=champ non obligatoire, le format de l'heure est aaaa-MM-jj, par exemple2020-07-07
#消息中心
att_message_leave=Avis de présence {0}
att_message_leaveContent={0} soumis {1}, {2} l'heure est {3}~{4}
att_message_leaveTime=Temps de congé
att_message_overtime=Avis de présence et heures supplémentaires
att_message_overtimeContent={0} heures supplémentaires soumises, et les heures supplémentaires sont de {1}~{2}
att_message_overtimeTime=Heures supplémentaires
att_message_sign=Panneau d'avis de présence
att_message_signContent={0} a soumis un signe supplémentaire, et l'heure du signe supplémentaire est {1}
att_message_adjust=Avis d'ajustement de présence
att_message_adjustContent={0} a soumis un ajustement, et la date d'ajustement est le {1}
att_message_class=Notification de présence et d'équipe
att_message_classContent=Contenu de la classe
att_message_classContent0={0} a soumis une équipe, la date de l'équipe est {1} et l'équipe est {2}
att_message_classContent1={0} a soumis une équipe, la date de l'équipe est {1} et la date de l'équipe est {2}
att_message_classContent2={0} ({1}) et {2} ({3}) classes d'échange
#推送中心
att_pushCenter_transaction=Enregistrement de présence
# 时间段
att_timeSlot_workTimeNotEqual=Le temps de travail ne peut pas être égal au temps de repos
att_timeSlot_signTimeNotEqual=L'heure de début de connexion ne peut pas être égale à l'heure de fin de déconnexion
# 北向接口A
att_api_notNull={0} ne peut pas être vide!
att_api_startDateGeEndDate=L'heure de début ne peut pas être supérieure ou égale à l'heure de fin !
att_api_leaveTypeNotExist=La fausse espèce n'existe pas !
att_api_imageLengthNot2000=La longueur de l'adresse de l'image ne peut pas dépasser 2000!
# 20221230新增国际化
att_personSch_workTypeNotNull=Le type de travail correspondant au numéro de personne {0} ne peut pas être vide!
att_personSch_workTypeNotExist=Le type de travail correspondant au numéro de personne {0} n'existe pas!
att_annualLeave_recalculate=Recalculer
# 20230530新增国际化
att_leftMenu_dailyReport=Rapport quotidien de présence
att_leftMenu_overtimeReport=Rapport d'heures supplémentaires
att_leftMenu_lateReport=Rapport tardif
att_leftMenu_earlyReport=Quitter le rapport anticipé
att_leftMenu_absentReport=Rapport d'absence
att_leftMenu_monthReport=Rapport mensuel de présence
att_leftMenu_monthWorkTimeReport=Rapport mensuel sur le temps de travail
att_leftMenu_monthCardReport=Rapport de carte mensuel
att_leftMenu_monthOvertimeReport=Rapport mensuel des heures supplémentaires
att_leftMenu_overtimeSummaryReport=Rapport récapitulatif des heures supplémentaires du personnel
att_leftMenu_deptOvertimeSummaryReport=Rapport récapitulatif des heures supplémentaires du service
att_leftMenu_deptLeaveSummaryReport=Rapport récapitulatif des congés du service
att_annualLeave_calculateDay=Nombre de jours de congé annuel
att_annualLeave_adjustDay=Ajuster les jours
att_annualLeave_sureSelectDept=Êtes-vous sûr de vouloir effectuer l'opération {0} sur le département sélectionné?
att_annualLeave_sureSelectPerson=Êtes-vous sûr de vouloir effectuer l'opération {0} sur la personne sélectionnée?
att_annualLeave_calculateTip1=Lors du calcul selon l'ancienneté: le calcul du congé annuel est précis au mois près, si l'ancienneté est de 10ans et 3 mois, alors 10 ans et 3 mois seront utilisés pour le calcul;
att_annualLeave_calculateTip2=Lorsque la conversion n'est pas basée sur l'ancienneté: le calcul du congé annuel est précis à l'année près, si l'ancienneté est de 10 ans et 3 mois, alors 10 ans seront utilisés pour le calcul;
att_rule_isInCompleteTip=La priorité est la plus élevée lorsqu'aucune connexion ou aucune déconnexion n'est enregistrée comme incomplète, et en cas de retard, de congé anticipé, d'absentéisme et valide
att_rule_absentTip=Lorsqu'aucune connexion ou aucune déconnexion n'est enregistrée comme absentéisme, la durée de l'absentéisme est égale à la durée des heures de travail moins la durée du congé tardif ou anticipé
att_timeSlot_elasticTip1=0, le temps effectif est égal au temps réel, pas d'absentéisme
att_timeSlot_elasticTip2=Si le temps réel est supérieur au temps de travail, le temps effectif est égal au temps de travail, pas d'absentéisme
att_timeSlot_elasticTip3=Si la durée réelle est inférieure à la durée de travail, la durée effective est égale à la durée réelle, et l'absentéisme est égal à la durée de travail moins la durée réelle
att_timeSlot_maxWorkingHours=Les heures de travail ne peuvent pas être supérieures à
# 20231030
att_customReport=Rapport personnalisé de fréquentation
att_customReport_byDayDetail=Détail par jour
att_customReport_byPerson=Résumé par personne
att_customReport_byDept=Résumé par département
att_customReport_queryMaxRange=L'intervalle maximum de la requête est de quatre mois.
# 20240630新增国际化
att_personSch_shiftWorkTypeTip1=1, lorsque le type de travail est le travail normal / jours de repos heures supplémentaires, la priorité de travail est inférieure aux jours fériés
att_personSch_shiftWorkTypeTip2=2, lorsque le type de travail est des heures supplémentaires pendant les vacances, la priorité de la classe est plus élevée que les vacances
att_personVerifyMode=Moyens de vérification du personnel
att_personVerifyMode_setting=Configuration de la méthode de vérification
att_personSch_importCycSch=Importer des horaires de cycle du personnel
att_personSch_cycSchTemplate=Modèle de roulement du cycle du personnel
att_personSch_exportCycSchTemplate=Télécharger le modèle staff cycle
att_personSch_scheduleTypeNotNull=Le type de quart ne peut pas être vide ou inexistant!
att_personSch_shiftNotNull=Les quarts ne peuvent pas être vides!
att_personSch_shiftNotExist=Les quarts n'existent pas!
att_personSch_onlyAllowOneShift=Une seule rangée est autorisée pour les quarts de travail ordinaires!
att_shift_attShiftStartDateRemark2=La semaine où la date de début du cycle est la première; Le mois dans lequel la date de début du cycle est le mois de janvier.
#打卡状态
att_cardStatus_setting=Réglage du statut d'assistance
att_cardStatus_name=Nom
att_cardStatus_value=Valeur
att_cardStatus_alias=Alias
att_cardStatus_every_day=Tout le jour
att_cardStatus_by_week=Par semaine
att_cardStatus_autoState=État automatique
att_cardStatus_attState=État d'assistance
att_cardStatus_signIn=Entrée
att_cardStatus_signOut=Sortie
att_cardStatus_out=dehors
att_cardStatus_outReturn=Retour après la sortie
att_cardStatus_overtime_signIn=Entrée en temps supplémentaire
att_cardStatus_overtime_signOut=Sortie en temps supplémentaire
# 20241030新增国际化
att_leaveType_enableMaxDays=Activer la limite annuelle
att_leaveType_maxDays=Limite annuelle (jours)
att_leaveType_applyMaxDays=Les demandes ne peuvent pas excéder {0} jours pour cette année
att_param_overTimeSetting=Paramètres du niveau de travail supplémentaire
att_param_overTimeLevel=Niveau de travail supplémentaire (heures)
att_param_overTimeLevelEnable=Activer le calcul du niveau de travail supplémentaire
att_param_reportColor=Couleur du rapport
# APP
att_app_signClientTip=Cet appareil a déjà été enregistré par quelqu'un d'autre aujourd'hui
att_app_noSignAddress=Zone de pointage non configurée, veuillez contacter l'administrateur pour la configurer
att_app_notInSignAddress=Vous n'êtes pas arrivé à l'endroit prévu pour le pointage, impossible de pointeur
att_app_attendance=Mon suivi d'assiduite
att_app_apply=Demande d'assiduite
att_app_approve=Mes validations
# 20250530
att_node_leaderNodeExist=Le nœud d'approbation par le supérieur direct existe déjà
att_signAddress_init=Initialisation de la carte
att_signAddress_initTips=Veuillez entrer la clé de la carte et initialiser la carte pour sélectionner une adresse