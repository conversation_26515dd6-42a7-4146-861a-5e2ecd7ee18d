#Numele sistemului 罗马尼亚语
att_systemName=Sistem de management al prezenței Att6.0
#================================================== ===================
#Meniu stânga
att_module=Prezență
#Meniu stânga-Dispozitiv prezență și timp
att_leftMenu_attendanceDevice=Administrarea dispozitivului
att_leftMenu_device=Dispozitiv de asistență
att_leftMenu_point=Punctul de prezență
att_leftMenu_sign_address=Adresa de check-in mobil
att_leftMenu_adms_devCmd=Serverul a emis o comandă
#Meniu stânga-informații de bază
att_leftMenu_basicInformation=Informații de bază
att_leftMenu_rule=Regulă
att_leftMenu_base_rule=Reguli de bază
att_leftMenu_department_rule=Reguli departament
att_leftMenu_holiday=Vacanță
att_leftMenu_leaveType=Prefăcătorie
att_leftMenu_timingCalculation=Calcularea sincronizării
att_leftMenu_autoExport=Export automat
att_leftMenu_param=Setările parametrilor
#Meniu stânga-Gestionare schimburi 
att_leftMenu_shiftManagement=Gestionare schimburi
att_leftMenu_timeSlot=Perioadă
att_leftMenu_shift=Schimb
#Meniu stânga-Gestionare schimburi
att_leftMenu_scheduleManagement=Gestionare schimburi
att_leftMenu_group=Grupare
att_leftMenu_groupPerson=Persoanele grupate
att_leftMenu_groupSch=Schimb de grup
att_leftMenu_deptSch=Planificarea departamentului
att_leftMenu_personSch=Planificarea personalului
att_leftMenu_tempSch=Schimb temporar
att_leftMenu_nonSch=Persoană neînlocuită
#Meniu stânga-Gestionare excepții
att_leftMenu_exceptionManagement=Managementul excepțiilor de prezență
att_leftMenu_sign=Aprobare
att_leftMenu_leave=Părăsire
att_leftMenu_trip=Voiaj
att_leftMenu_out=Ieșire
att_leftMenu_overtime=Ore suplimentare
att_leftMenu_adjust=Ajustarea la odihnă
att_leftMenu_class=Schimb
#Meniu stânga-raport statistic
att_leftMenu_statisticalReport=Raport de statistici de prezență
att_leftMenu_manualCalculation=Calcul manual
att_leftMenu_transaction=Formular original de înregistrare
att_leftMenu_dayCardDetailReport=Tabelul zilnic cu detalii despre glisare carduri
att_leftMenu_leaveSummaryReport=Tabel sumar de plecări
att_leftMenu_dayDetailReport=Raport zilnic
att_leftMenu_monthDetailReport=Raport lunar
att_leftMenu_monthStatisticalReport=Raport statistică lunară (per persoană)
att_leftMenu_deptStatisticalReport=Raport statistic al departamentului (per departament)
att_leftMenu_yearStatisticalReport=Raport statistic anual (per persoană)
att_leftMenu_attSignCallRollReport=Conectare apel apel
att_leftMenu_workTimeReport=Raport despre timpul de lucru
#Meniu stânga-Jurnalul de operare al dispozitivului
att_leftMenu_device_op_log=Jurnalul de operare al dispozitivului
#左侧菜单-实时点名
att_leftMenu_realTime_callRoll=Apel nominal
#================================================== ===================
#Comun
att_common_person=Personal
att_common_pin=Numerotare
att_common_group=Grupare
att_common_dept=Departament
att_common_symbol=Simbol
att_common_deptNo=Numărul departamentului
att_common_deptName=Numele departamentului
att_common_groupNo=Număr de grup
att_common_groupName=Numele grupului
att_common_operateTime=Timpul de funcționare
att_common_operationFailed=Operațiunea nu a reușit
att_common_id=Id
att_common_deptId=ID departament
att_common_groupId=ID grup
att_common_deviceId=ID-ul dispozitivului
att_person_pin=Numărul de angajat
att_person_name=Numele complet
att_person_lastName=Numele de familie
att_person_internalCard=Număr de card
att_person_attendanceMode=Modul timp și prezență
att_person_normalAttendance=Prezență normală
att_person_noPunchCard=Fără carte de perforare
att_common_attendance=Prezență
att_common_attendance_hour=Prezență (ore)
att_common_attendance_day=Prezență (zi)
att_common_late=A întârziat
att_common_early=Plecare devreme
att_common_overtime=Ore suplimentare
att_common_exception=Anormal
att_common_absent=Absență
att_common_leave=Părăsire
att_common_trip=Voiaj
att_common_out=Ieșire
att_common_staff=Angajat general
att_common_superadmin=Super administrator
att_common_msg=Conținut SMS
att_common_min=Durata scurtă a mesajului (minute)
att_common_letterNumber=Introduceți doar numere sau litere!
att_common_relationDataCanNotDel=Datele asociate nu pot fi șterse!
att_common_relationDataCanNotEdit=Datele asociate nu pot fi modificate!
att_common_needSelectOneArea=Vă rugăm să selectați o zonă!
att_common_neesSelectPerson=Vă rugăm să selectați o persoană!
att_common_nameNoSpace=Numele nu poate conține spații!
att_common_digitsValid=Introduceți doar numere cu până la două zecimale!
att_common_numValid=Introduceți doar numere!
#================================================== ===================
#Panoul de lucru
att_dashboard_worker=Obsedat de muncă
att_dashboard_today=Prezența de astăzi
att_dashboard_todayCount=Statisticile segmentului de prezență de astăzi
att_dashboard_exceptionCount=Statistici anormale (luna aceasta)
att_dashboard_lastWeek=Săptămâna trecută
att_dashboard_lastMonth=Luna trecută
att_dashboard_perpsonNumber=Persoane totale
att_dashboard_actualNumber=Sosit
att_dashboard_notArrivedNumber=Nu a ajuns
att_dashboard_attHour=Timp de lucru
#Zonă
#Dispozitiv
att_op_syncDev=Sincronizați datele software cu dispozitivul
att_op_account=Corectarea datelor de prezență
att_op_check=Reîncărcați datele
att_op_deleteCmd=Ștergeți comanda dispozitivului
att_op_dataSms=Mesaj oficial scurt 
att_op_clearAttPic=Ștergeți fotografiile de prezență
att_op_clearAttLog=Evidență clară de prezență
att_device_waitCmdCount=Numărul de comenzi care trebuie executate
att_device_status=Stare activată
att_device_register=Dispozitiv de check-in
att_device_isRegister=Dacă înregistrați dispozitivul
att_device_existNotRegDevice=Pentru dispozitivele neînregistrate, datele nu pot fi obținute!
att_device_fwVersion=Numărul versiunii firmware-ului
att_device_transInterval=Interval de actualizare (minute)
att_device_cmdCount=Numărul maxim de comenzi pentru a comunica cu serverul
att_device_delay=Timpul înregistrării solicitării (secunde)
att_device_timeZone=Fus orar
att_device_operationLog=Jurnalul de operații
att_device_registeredFingerprint=Înscrieți amprentă
att_device_registeredUser=Înregistrare utilizator
att_device_fingerprintImage=Poză cu amprenta
att_device_editUser=Editare utilizator
att_device_modifyFingerprint=Modificați amprenta
att_device_faceRegistration=Înregistrarea feței
att_device_userPhotos=Fotografii utilizator
att_device_attLog=Dacă încărcați înregistrări de prezență
att_device_operLog=Dacă încărcați informații despre personal
att_device_attPhoto=Dacă încărcați fotografii de prezență
att_device_isOnLine=Dacă este online
att_device_InputPin=Introduceți numărul de angajat
att_device_getPin=Obțineți date specificate despre persoană 
att_device_separatedPin=Numere multiple de angajat, separate prin virgule
att_device_authDevice=Dispozitiv autorizat
att_device_disabled=Următoarele dispozitive sunt dezactivate și nu pot fi operate!
att_device_autoAdd=Echipamentul nou este adăugat automat
att_device_receivePersonOnlyDb=Primirea doar a datelor personalului prezent în baza de date
att_devMenu_control=Controlul dispozitivului
att_devMenu_viewOrGetInfo=Vizualizați și obțineți informații
att_devMenu_clearData=Ștergeți datele dispozitivului
att_device_disabledOrOffline=Dispozitivul nu este activat sau nu este online și nu poate fi operat!
att_device_areaStatus=Starea zonei dispozitivului
att_device_areaCommon=Regiunea este normală
att_device_areaEmpty=Zona este goală
att_device_isRegDev=Fusul orar sau modificarea stării registratorului necesită repornirea dispozitivului pentru a intra în vigoare!
att_device_canUpgrade=Următoarele dispozitive pot fi actualizate
att_device_offline=Următoarele dispozitive sunt offline și nu pot fi operate!
att_device_oldProtocol=Protocol vechi
att_device_newProtocol=Protocol nou
att_device_noMoreTwenty=Pachetul vechi de actualizare firmware a dispozitivului de protocol nu poate depăși 20M
att_device_transferFilesTip=Firmware-ul detectat cu succes, transferați fișiere
att_op_clearAttPers=Șterge personalul echipamentului
#Personalul din zonă
att_op_forZoneAddPers=Adăugați persoane din zonă
att_op_dataUserSms=Mesaj privat
att_op_syncPers=Resincronizați dispozitivul
att_areaPerson_choiceArea=Vă rugăm să selectați o zonă!
att_areaPerson_byAreaPerson=După regiune
att_areaPerson_setByAreaPerson=Setați după personalul din zonă
att_areaPerson_importBatchDel=Importare ștergere în bloc
att_areaPerson_syncToDevSuccess=Operațiune cu reușit ! Vă rugăm să așteptați finalizarea comenzii.
att_areaPerson_personId=ID persoană
att_areaPerson_areaId=ID zonă
att_area_existPerson=Sunt persoane în zonă!
att_areaPerson_notice1=Zona sau personalul nu pot fi goale în același timp!
att_areaPerson_notice2=Nu a fost găsită nicio persoană sau zonă!
att_areaPerson_notice3=Nu s-a găsit niciun dispozitiv în zonă!
att_areaPerson_addArea=Adăugați zonă
att_areaPerson_delArea=Ștergeți zonă
att_areaPerson_persNoExit=Persoana nu există
att_areaPerson_importTip1=Vă rugăm să vă asigurați că persoana importată există deja în modulul de personal
att_areaPerson_importTip2=Importatorii de loturi nu vor fi livrați automat pe dispozitiv și trebuie sincronizați manual
att_areaPerson_addAreaPerson=Adăugați personal regional
att_areaPerson_delAreaPerson=Șterge personalul zonei
att_areaPerson_importDelAreaPerson=Importați și ștergeți personalul din zonă
att_areaPerson_importAreaPerson=Personalul zonei de import
#Punctul de prezență
att_attPoint_name=Numele punctului de prezență
att_attPoint_list=Listă timpi de prezență
att_attPoint_deviceModule=Modulul dispozitivului
att_attPoint_acc=Controlul accesului
att_attPoint_park=Parcare
att_attPoint_ins=Ecran informativ
att_attPoint_pid=Martor
att_attPoint_vms=Video
att_attPoint_psg=Interval
att_attPoint_doorList=Lista ușilor
att_attPoint_deviceList=Listă dispozitive
att_attPoint_channelList=Lista de canale
att_attPoint_gateList=Lista poartă
att_attPoint_recordTypeList=Trageți tipul de înregistrare
att_attPoint_door=Vă rugăm să selectați ușa corespunzătoare
att_attPoint_device=Vă rugăm să selectați dispozitivul corespunzător
att_attPoint_gate=Vă rugăm să selectați poarta corespunzătoare
att_attPoint_normalPassRecord=Record normal de trecere
att_attPoint_verificationRecord=Înregistrare verificare
att_person_attSet=Setări de prezență
att_attPoint_point=Vă rugăm să selectați un punct de prezență
att_attPoint_count=Puncte de prezență insuficiente, operațiunea a eșuat
att_attPoint_notSelect=Nu este configurat niciun modul corespunzător
att_attPoint_accInsufficientPoints=Înregistrarea controlului de acces nu este suficientă la punctele de participare!
att_attPoint_parkInsufficientPoints=Înregistrările de prezență parcare când punctele de prezență sunt insuficiente!
att_attPoint_insInsufficientPoints=Ecranul informativ are puncte de prezență insuficiente!
att_attPoint_pidInsufficientPoints=Cardul de identitate are puncte de prezență insuficiente!
att_attPoint_doorOrParkDeviceName=Numele ușii sau numele dispozitivului de parcare
att_attPoint_vmsInsufficientPoints=Filmarea are puncte de prezență insuficiente!
att_attPoint_psgInsufficientPoints=Canalul are puncte de participare insuficiente!
att_attPoint_delDevFail=Nu s-a reușit ștergerea dispozitivului, dispozitivul a fost utilizat ca punct de participare!
att_attPoint_pullingRecord=Punctul de participare obține înregistrări în mod regulat, vă rugăm să așteptați!
att_attPoint_lastTransactionTime=Timpul de extragere a ultimelor date
att_attPoint_masterDevice=Dispozitiv principal
att_attPoint_channelName=Numele canalului
att_attPoint_cameraName=Numele camerei
att_attPoint_cameraIP=IP camera
att_attPoint_channelIP=IP canal
att_attPoint_gateNumber=Număr poartă
att_attPoint_gateName=Numele porții
#Adresa de check-in prezență APP
att_signAddress_address=Adresă
att_signAddress_longitude=Longitudine
att_signAddress_latitude=Latitudine
att_signAddress_range=Raza de acțiune efectivă
att_signAddress_rangeUnit=Unitatea este metrul (m)
#================================================== ===================
#Regulă
att_rule_baseRuleSet=Setări reguli de bază
att_rule_countConvertSet=Setări de conversie calcule
att_rule_otherSet=Altele setări
att_rule_baseRuleSignIn=Reguli pentru înregistrarea cardurilor la locul de muncă
att_rule_baseRuleSignOut=Reguli pentru predarea și scoaterea cardurilor după plecarea de la serviciu
att_rule_earliestPrinciple=Cel mai devreme
att_rule_theLatestPrinciple=Cele mai nou
att_rule_principleOfProximity=Din apropiere
att_rule_baseRuleShortestMinutes=Cea mai scurtă perioadă de prezență trebuie să fie mai mare de (minim 10 minute)
att_rule_baseRuleLongestMinutes=Cea mai lungă perioadă de prezență trebuie să fie mai mică de (maxim 1440 minute)
att_rule_baseRuleLateAndEarly=Întârzierea și plecarea mai devreme sunt considerate absenteism
att_rule_baseRuleCountOvertime=Ore suplimentare statistice
att_rule_baseRuleFindSchSort=Găsiți comanda înregistrării de schimburi
att_rule_groupGreaterThanDepartment=Grupare->departament
att_rule_departmentGreaterThanGroup=Departament->grup
att_rule_baseRuleSmartFindClass=Principiul căutării inteligente de schimburi
att_rule_timeLongest=Cel mai lung timp
att_rule_exceptionLeast=Cel mai puțin anormal
att_rule_baseRuleCrossDay=Rezultatul calculului prezenței atunci când timpul schimbului se întinde pe zile
att_rule_firstDay=Prima zi
att_rule_secondDay=A doua zi
att_rule_baseRuleShortestOvertimeMinutes=Durata minimă unică a orelor suplimentare (minute)
att_rule_baseRuleMaxOvertimeMinutes=Durata maximă a orelor suplimentare (minute)
att_rule_baseRuleElasticCal=Metoda de calcul a duratei elastice
att_rule_baseRuleTwoPunch=Timpul cumulativ
att_rule_baseRuleStartEnd=Contorul de timp pentru primul și ultimul card glisat
att_rule_countConvertHour=Referință conversie Oră
att_rule_formulaHour=Formula: Oră = minute/60
att_rule_countConvertDay=Referință conversie zile
att_rule_formulaDay=Formula: Zile = Minute/Minute de lucru pe zi
att_rule_inFormulaShallPrevail=Sub rezerva rezultatelor formulei de calculul;
att_rule_remainderHour=Restul este mai mare sau egal cu
att_rule_oneHour=Socotit ca o oră;
att_rule_halfAnHour=Socotit ca o jumătate de oră, altfel ignorați;
att_rule_remainderDay=Coeficientul este mai mare sau egal cu numărul de minute care ar trebui să fie lucrate
att_rule_oneDay=%, Socotit ca o zi;
att_rule_halfAnDay=%, Înregistrat ca jumătate de zi, altfel ignorat;
att_rule_countConvertAbsentDay=Referință pentru conversia zilelor de absenteism
att_rule_markWorkingDays=Socotite ca zile lucrătoare
att_rule_countConvertDecimal=Locul zecimalei
att_rule_otherSymbol=Setați simbolul rezultatului prezenței în raport
att_rule_arrive=Datorat/real
att_rule_noSignIn=Fără checke in
att_rule_noSignOff=Fără checke out
att_rule_off=Reglare
att_rule_class=Recuperare
att_rule_shortLessLong=Cea mai scurtă perioadă de prezență nu poate fi mai mare decât cea mai lungă perioadă de prezență!
att_rule_symbolsWarning=Setați simbolul de prezență în raport. Nu poate fi gol!
att_rule_reportSettingSet=Setările de export pentru raport
att_rule_shortDateFormat=Formatul datei
att_rule_shortTimeFormat=Formatul orei
att_rule_baseRuleSignBreakTime=Dacă se glisează cardul în pauză
att_leftMenu_custom_rule=Reguli personalizate
att_custom_rule_already_exist={0} reguli personalizate deja există!
att_add_group_custom_rule=Adăugați reguli de grupare
att_custom_rule_type=Tip de regulă
att_rule_type_group=Reguli de grupare
att_rule_type_dept=Reguli departament
att_custom_rule_orgNames=Aplicare obiect
att_rult_maxOverTimeType1=Nu este limitat
att_rult_maxOverTimeType2=Săptămâna aceasta
att_rult_maxOverTimeType3=Luna aceasta
att_rule_countConvertDayRemark1=Exemplu: Timpul efectiv de lucru este de 500 de minute, iar timpul de lucru trebuie să fie de 480 de minute pe zi, rezultatul este de 500/480 = 1,04, iar ultima zecimală este de 1,0.
att_rule_countConvertDayRemark2=Exemplu: Timpul efectiv de lucru este de 500 minute, iar timpul de lucru trebuie să fie de 480 de minute pe zi, atunci rezultatul este 500/480 = 1.04, 1.04> 0.8 este calculat ca o zi
att_rule_countConvertDayRemark3=Exemplu: Timpul efectiv de lucru este de 300 minute, iar timpul de lucru trebuie să fie de 480 minute pe zi, rezultatul este de 300/480 = 0,625, 0,2<0,625<0,8 este calculat ca jumătate de zi
att_rule_countConvertDayRemark4=Rreferința pentru conversia numărului de zile: zilele lucrătoare înregistrate în Perioadă de timp nu vor funcționa;
att_rule_countConvertDayRemark5=Înregistrarea ca număr de zile lucrătoare va predomina: limitat doar de numărul de zile de absenteism și, atât timp cât există absenteism în fiecare perioadă, durata absenteismului este calculată în funcție de numărul de zile lucrătoare din perioadă.
att_rule_baseRuleSmartFindRemark1=Cel mai lung timp: în funcție de punctul cardului zilei, calculați timpul de lucru corespunzător fiecărui schimb al zilei și găsiți tura cu cel mai lung timp efectiv al zilei;
att_rule_baseRuleSmartFindRemark2=Cele mai puține anomalii: calculați numărul de anomalii corespunzătoare fiecărui schimb al zilei în funcție de punctul cardului zilei, găsiți turația cu cele mai puține anomalii pentru a calcula timpul de lucru
att_rule_baseRuleHourValidator=Minutele considerate jumătăți de oră nu pot fi mai multe sau egale cu minutele considerate o oră!
att_rule_baseRuleDayValidator=Durata considerată jumătate de zi nu poate fi mai mare sau egală cu durata considerată o zi!
att_rule_overtimeWarning=Durata maximă de ore suplimentare nu poate fi mai mică decât durata minimă a unei perioade suplimentare minime!
att_rule_noSignInCountType=Nu este înregistrat ca
att_rule_absent=Absență
att_rule_earlyLeave=Plecare devreme
att_rule_noSignOffCountType=Neînregistrarea la plecare este socotită ca
att_rule_minutes=Minut
att_rule_noSignInCountLateMinute=Neînregistrarea socotită ca minute întârziate
att_rule_noSignOffCountEarlyMinute=Restituirile neatribuite sunt socotite ca minute de plecare anticipată
att_rule_incomplete=Incomplet
att_rule_noCheckInIncomplete=Nu este semnat incomplet
att_rule_noCheckOutIncomplete=Neînregistrarea la plecare este incompletă
att_rule_lateMinuteWarning=Neînregistrarea este socotită deoarece numărul de minute întârziate ar trebui să fie mai mare de 0 și mai mic decât cea mai lungă perioadă de timp
att_rule_earlyMinuteWarning=Ieșirile ne-semnate sunt socotite, deoarece minutele de plecare anticipată trebuie să fie mai mari de 0 și mai mici decât cea mai lungă durată
att_rule_baseRuleNoSignInCountLateMinuteRemark=Atunci când nu se face check-in este numărat ca întârziat, dacă nu se face check-in, acesta va fi considerat ca întârziere pentru N minute
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=Atunci când nu vă deconectați este înregistrat ca plecare anticipată, dacă nu deconectați, este înregistrat ca plecând devreme pentru N minute
#Vacanță
att_holiday_placeholderNo=Se recomandă să începeți cu H, cum ar fi H1
att_holiday_placeholderName=Se recomandă utilizarea anului + a numelui vacanței, ca de exemplu 1 Mai Ziua Muncii, 2017
att_holiday_dayNumber=Zile
att_holiday_validDate_msg=Există deja sărbători în această perioadă
#Prefăcătorie
att_leaveType_leaveThing=Concediu
att_leaveType_leaveMarriage=Concediu de nuntă
att_leaveType_leaveBirth=Concediu de maternitate
att_leaveType_leaveSick=Concediu medical
att_leaveType_leaveAnnual=Concediu de odihnă anual
att_leaveType_leaveFuneral=Concediu funerar
att_leaveType_leaveHome=Concediu în familie
att_leaveType_leaveNursing=Concediu de alăptare
att_leaveType_isDeductWorkLong=Reducerea orelor de lucru
att_leaveType_placeholderNo=Se recomandă să începeți cu L, cum ar fi L1
att_leaveType_placeholderName=Se recomandă încheierea cu concediu, cum ar fi concediul pentru căsătorie
#Calcularea sincronizării
att_timingcalc_timeCalcFrequency=Intervalul de calcul
att_timingcalc_timeCalcInterval=Sincronizarea de calcul a timpului
att_timingcalc_timeSet=Sincronizarea de calcul a setării timpului
att_timingcalc_timeSelect=Vă rugăm să selectați timpul
att_timingcalc_optionTip=Trebuie să fie păstrat cel puțin un calcul efectiv de prezență zilnic
#Exportați rapoarte automat
att_autoExport_reportType=Tip de raport
att_autoExport_fileType=Tip fișier
att_autoExport_fileName=Numele fișier
att_autoExport_fileDateFormat=Formatul datei
att_autoExport_fileTimeFormat=Formatul orei
att_autoExport_fileContentFormat=Formatul conținutului
att_autoExport_fileContentFormatTxt=Exemplu: {deptname}00{personpin}01{personname}02{attdatetime}03\r\n
att_autoExport_timeSendFrequency=Frecvența de trimitere
att_autoExport_timeSendInterval=Interval de timp
att_autoExport_emailType=Tipul destinatarului de e-mail
att_autoExport_emailRecipients=Destinatar de e-mail
att_autoExport_emailAddress=Adresa de email
att_autoExport_emailExample=Exemplu: <EMAIL>, <EMAIL>
att_autoExport_emailSubject=Titlu e-mail
att_autoExport_emailContent=Conținutul mesajului
att_autoExport_field=Câmp
att_autoExport_fieldName=Numele câmpului
att_autoExport_fieldCode=Codare câmp
att_autoExport_reportSet=Setări raport
att_autoExport_timeSet=Setarea timpului de trimitere e-mail
att_autoExport_emailSet=Setări e-mail
att_autoExport_emailSetAlert=Vă rugăm să introduceți adresa de e-mail
att_autoExport_emailTypeSet=Setările destinatarului
att_autoExport_byDay=Zilnic
att_autoExport_byMonth=Lunar
att_autoExport_byPersonSet=Setat de persoană
att_autoExport_byDeptSet=Setat prin departament
att_autoExport_byAreaSet=Pe zonă
att_autoExport_emailSubjectSet=Setare titlu
att_autoExport_emailContentSet=Setări text
att_autoExport_timePointAlert=Vă rugăm să selectați termenul corect de livrare
att_autoExport_lastDayofMonth=Ultima zi din fiecare lună
att_autoExport_firstDayofMonth=Prima zi a fiecărei luni
att_autoExport_dayofMonthCheck=O anumită dată
att_autoExport_dayofMonthCheckAlert=Vă rugăm să selectați o anumită dată
att_autoExport_chooseDeptAlert=Vă rugăm să selectați un departament!
att_autoExport_sendFormatSet=Setări metodă de trimitere
att_autoExport_sendFormat=Metoda de trimitere
att_autoExport_mailFormat=Metoda de trimitere e-mail
att_autoExport_ftpFormat=Metoda de trimitere ftp
att_autoExport_sftpFormat=Metoda de trimitere sftp
att_autoExport_ftpUrl=Adresa serverului ftp
att_autoExport_ftpPort=Portul serverului FTP
att_autoExport_ftpTimeSet=Setarea timpului de trimitere ftp
att_autoExport_ftpParamSet=Setarea parametrilor ftp
att_autoExport_ftpUsername=Numele de utilizator ftp
att_autoExport_ftpPassword=Parola ftp
att_autoExport_correctFtpParam=Vă rugăm să completați parametrii ftp corect
att_autoExport_correctFtpTestParam=Vă rugăm să testați conexiunea pentru a vă asigura că comunicarea este normală
att_autoExport_inputFtpUrl=Vă rugăm să introduceți adresa serverului
att_autoExport_inputFtpPort=Vă rugăm să introduceți portul serverului
att_autoExport_ftpSuccess=Conexiunea a reușit
att_autoExport_ftpFail=Verificați dacă setarea parametrului este greșită
att_autoExport_validFtp=Vă rugăm să introduceți o adresă de server validă
att_autoExport_validPort=Vă rugăm să introduceți un port server valabil
att_autoExport_selectExcelTip=Selectați EXCEL ca tip de fișier, iar formatul conținutului este toate câmpurile!
#================================================== ===================
#Perioadă
att_timeSlot_periodType=Tipul perioadei
att_timeSlot_normalTime=Perioadă normală 
att_timeSlot_elasticTime=Perioadă flexibilă 
att_timeSlot_startSignInTime=Oră check in
att_timeSlot_toWorkTime=Ore de lucru
att_timeSlot_endSignInTime=Ora de încheiere check-in 
att_timeSlot_allowLateMinutes=Minute întârziere
att_timeSlot_isMustSignIn=Trebuie să vă conectați
att_timeSlot_startSignOffTime=Oră check out
att_timeSlot_offWorkTime=Ore oprire
att_timeSlot_endSignOffTime=Ora de încheiere check-out
att_timeSlot_allowEarlyMinutes=Se permit minute de plecare timpurie
att_timeSlot_isMustSignOff=Trebuie să vă deconectați
att_timeSlot_workingHours=Ore de lucru (minute)
att_timeSlot_isSegmentDeduction=Dacă se deduce între segmente
att_timeSlot_startSegmentTime=Timpul de începere
att_timeSlot_endSegmentTime=Interval de timp de sfârșit
att_timeSlot_interSegmentDeduction=Inter-deducere (minute)
att_timeSlot_markWorkingDays=Socotite ca zile lucrătoare
att_timeSlot_isAdvanceCountOvertime=Dacă se lucrează ore suplimentare în avans
att_timeSlot_signInAdvanceTime=Conectarea mai devreme decât ora
att_timeSlot_isPostponeCountOvertime=Dacă se amâna orele suplimentare
att_timeSlot_signOutPosponeTime=Check out mai târziu decât ora
att_timeSlot_isCountOvertime=Dacă lucrează ore suplimentare
att_timeSlot_timeSlotLong=Programul de lucru trebuie să îndeplinească timpul și intervalul de prezență definit de reguli:
att_timeSlot_alertStartSignInTime=Ora de check-in ar trebui să fie mai mică decât timpul de lucru
att_timeSlot_alertEndSignInTime=Timpul de lucru ar trebui să fie mai mic decât ora de check-in
att_timeSlot_alertStartSignInAndEndSignIn=Ora de check-in finală ar trebui să fie mai mică decât ora de pornire
att_timeSlot_alertStartSignOffTime=Ora de check-out ar trebui să fie mai mică decât ora finală
att_timeSlot_alertEndSignOffTime=Ora liberă este mai mică decât ora finală
att_timeSlot_alertStartUnequalEnd=Durata intervalului de început nu poate fi egală cu ora intervalului final
att_timeSlot_alertStartSegmentTime=Timpul dintre segmentele de început trebuie să fie mai mic decât timpul dintre segmentele finale
att_timeSlot_alertStartAndEndTime=Orele de lucru trebuie să fie mai mici decât ora de început
att_timeSlot_alertEndAndoffWorkTime=Timpul dintre perioadele de sfârșit trebuie să fie mai mic decât timpul liber
att_timeSlot_alertToWorkLesserAndEndTimeLesser=Timpul de lucru trebuie să fie mai mic decât timpul de început, iar timpul de încheiere trebuie să fie mai mic decât timpul
att_timeSlot_alertLessSignInAdvanceTime=Check-in-ul mai devreme decât ora este mai mică decât timpul de lucru
att_timeSlot_alertMoreSignInAdvanceTime=Numărul de minute suplimentare înainte de conectare trebuie să fie mai mic decât numărul de minute înainte de lucru
att_timeSlot_alertMoreSignOutPosponeTime=Numărul de minute suplimentare de la plecare este mai mic decât numărul de minute după plecarea de la muncă
att_timeSlot_alertLessSignOutPosponeTime=Check-out mai târziu decât ora este mai mare decât timpul liber
att_timeSlot_time=Vă rugăm să introduceți formatul de timp corect
att_timeSlot_alertMarkWorkingDays=Numără zile lucrătoare nu poate fi gol!
att_timeSlot_placeholderNo=Se recomandă să începeți cu T, cum ar fi T01
att_timeSlot_placeholderName=Se recomandă să începeți cu T sau cu sfârșitul perioadei de timp
att_timeSlot_beforeToWork=Înainte de a merge la serviciu
att_timeSlot_afterToWork=După serviciu
att_timeSlot_beforeOffWork=Înainte de a pleca de la serviciu
att_timeSlot_afterOffWork=După ce pleacă de la serviciu
att_timeSlot_minutesSignInValid=Conectare valabilă în câteva minute
att_timeSlot_toWork=Mergeți la muncă
att_timeSlot_offWork=In afara programului
att_timeSlot_minutesSignInAsOvertime=Conectare minute pentru a vă aminti orele suplimentare
att_timeSlot_minutesSignOutAsOvertime=Începeți orele suplimentare în minute
att_timeSlot_minOvertimeMinutes=Minimul de minute suplimentare
att_timeSlot_enableWorkingHours=Dacă activați orele de lucru
att_timeSlot_eidtTimeSlot=Editați Perioadă de timp
att_timeSlot_browseBreakTime=Căutați pauze
att_timeSlot_addBreakTime=Adăugați o pauză
att_timeSlot_enableFlexibleWork=Activați lucrul flexibil
att_timeSlot_advanceWorkMinutes=Poate lucra devreme
att_timeSlot_delayedWorkMinutes=Poate amâna lucrul
att_timeSlot_advanceWorkMinutesValidMsg1=Numărul de minute înainte de a merge la serviciu este mai mare decât minutele pe care le puteți lucra în avans
att_timeSlot_advanceWorkMinutesValidMsg2=Numărul de minute care pot fi lucrate în avans este mai mic decât numărul de minute înainte de lucru
att_timeSlot_advanceWorkMinutesValidMsg3=Numărul de minute pe care le puteți lucra în avans trebuie să fie mai mic sau egal cu numărul de minute pe care le puteți înregistra înainte de lucru pentru a înregistra ore suplimentare
att_timeSlot_advanceWorkMinutesValidMsg4=Numărul de minute pentru înregistrare înainte de lucru pentru înregistrarea orelor suplimentare trebuie să fie mai mare sau egal cu numărul de minute care pot fi lucrate în avans
att_timeSlot_delayedWorkMinutesValidMsg1=Numărul de minute după ieșirea de la muncă este mai mare decât numărul de minute care pot fi întârziate
att_timeSlot_delayedWorkMinutesValidMsg2=Numărul de minute care pot fi amânate la lucru este mai mic decât numărul de minute de la ieșirea de la lucru
att_timeSlot_delayedWorkMinutesValidMsg3=Numărul de minute care pot fi amânate la lucru trebuie să fie mai mic sau egal cu numărul de minute de la plecare și începerea orelor suplimentare
att_timeSlot_delayedWorkMinutesValidMsg4=Numărul de minute suplimentare după terminarea lucrului trebuie să fie mai mare sau egal cu numărul de minute care pot fi întârziate
att_timeSlot_allowLateMinutesValidMsg1=Permiteți minute întârziate să fie mai mici decât minutele după lucru
att_timeSlot_allowLateMinutesValidMsg2=Numărul de minute după lucru este mai mare decât minutele permise de întârziere
att_timeSlot_allowEarlyMinutesValidMsg1=Permiteți minute de plecare mai devreme să fie mai puține decât minutele înainte de plecarea de la lucru
att_timeSlot_allowEarlyMinutesValidMsg2=Minutele înainte de plecarea de la lucru trebuie să fie mai mari decât minutele permise de plecare mai devreme
att_timeSlot_timeOverlap={0} se suprapune cu timpul de {1}, vă rugăm să modificați Perioadă de timp selectată!
att_timeSlot_atLeastOne=Cel puțin 1 perioadă de odihnă!
att_timeSlot_mostThree=Până la 3 perioade de odihnă!
att_timeSlot_canNotEqual=Ora de începere a perioadei de odihnă nu poate fi egală cu ora de sfârșit!
att_timeSlot_shoudInWorkTime=Vă rugăm să vă asigurați că perioada de odihnă este în orele de lucru!
att_timeSlot_repeatBreakTime=Repetați perioada de odihnă!
att_timeSlot_toWorkLe=Orele de lucru sunt mai puține decât ora minimă de pornire a perioadei de odihnă selectate:
att_timeSlot_offWorkGe=Perioadă de oprire este mai mare decât timpul maxim de încheiere a perioadei de odihnă selectate:
att_timeSlot_crossDays_toWork=Timpul minim de început al perioadei de odihnă ar trebui să se încadreze în Perioadă: 
att_timeSlot_crossDays_offWork=Timpul maxim de terminare al perioadei de odihnă ar trebui să se încadreze în Perioadă: 
att_timeSlot_allowLateMinutesRemark=De la începutul orelor de lucru până la minutele de întârziere permise sunt considerate ca o cartelă normală de lucru
att_timeSlot_allowEarlyMinutesRemark=Plecarea timpurie de la începutul programului de plecare la orele de lucru în minutele permise de concediu timpuriu, socotită ca o cartelă normală de plecare
att_timeSlot_isSegmentDeductionRemark=Dacă se deduce Perioadă de odihnă în cadrul perioadei respective
att_timeSlot_attEnableFlexibleWorkRemark1=La munca flexibilă nu este permisă stabilirea numărului de minute de întârziere și plecare înainte de termen
att_timeSlot_afterToWorkRemark=Minutele după program sunt egale cu minutele care nu pot fi amânate
att_timeSlot_beforeOffWorkRemark=Numărul de minute înainte de terminarea programului este egal cu numărul de minute înainte de începerea programului de lucru
att_timeSlot_attEnableFlexibleWorkRemark2=Minutele efectuate după programul de lucru ar trebui să fie mai mari sau egale cu timpul ieșirii de la lucru + timpul amânat
att_timeSlot_attEnableFlexibleWorkRemark3=Minutele lucrate în avans, trebuie să fie mai mici sau egale cu N minute de lucru pentru a înregistra minute suplimentare
att_timeSlot_attEnableFlexibleWorkRemark4=Numărul de minute care poate fi amânat pentru a lucra trebuie să fie mai mic sau egal cu N minute după ce ați plecat de la muncă pentru a înregistra minute suplimentare
att_timeSlot_attBeforeToWorkAsOvertimeRemark=Exemplu: Pentru o tură de la ora 9, faceți check-in-ul pentru ore suplimentare cu 60 de minute înainte de începerea programului, apoi check-in înainte de ora 8 până la 8 pentru se consideră timp suplimentar
att_timeSlot_attAfterOffWorkAsOvertimeRemark=Exemplu: plecați de la muncă la ora 18, ieșiți după 60 de minute după muncă, apoi lucrați timp suplimentar de la 19:00 pentru a verifica timpul
att_timeSlot_longTimeValidRemark=Timpul valabil de semnare a cartelei după muncă și timpul valid de semnare a cartelei înainte de a pleca de la serviciu nu se pot suprapune în perioadă!
att_timeSlot_advanceWorkMinutesValidMsg5=Numărul efectiv de minute înainte de check-in este mai mare decât numărul de minute efectuate înainte de programul de lucru
att_timeSlot_advanceWorkMinutesValidMsg6=Numărul minutelor lucrate înainte de programul de lucru ar trebui să fie mai mic decât numărul valid de minute înainte de a părăsi locul de muncă
att_timeSlot_delayedWorkMinutesValidMsg5=Numărul efectiv de minute pentru a vă conecta după muncă este mai mare decât numărul de minute care poate fi amânat
att_timeSlot_delayedWorkMinutesValidMsg6=Numărul de minute care poate fi amânat la lucru este mai mic decât numărul valid de minute după lucrare
att_timeSlot_advanceWorkMinutesValidMsg7=Ora de check-in înainte de serviciu nu se poate suprapune cu ora de check-out după ieșirea de la serviciu cu o zi înainte
att_timeSlot_delayedWorkMinutesValidMsg7=Ora de check-out după muncă nu se poate suprapune cu ora de check-in înainte de lucru a doua zi
att_timeSlot_maxOvertimeMinutes=Limitați maximul orelor suplimentare
#Schimb
att_shift_basicSet=Setări de bază
att_shift_advancedSet=Setări avansate
att_shift_type=Tipul schimbului de lucru
att_shift_name=Numele schimbului de lucru
att_shift_regularShift=Schimb de lucru regular
att_shift_flexibleShift=Schimb de lucru flexibil
att_shift_color=Culoare
att_shift_periodicUnit=Unitate
att_shift_periodNumber=Ciclu
att_shift_startDate=Dată start
att_shift_startDate_firstDay=Dată start ciclu
att_shift_isShiftWithinMonth=Dacă se lucrează într-o lună
att_shift_attendanceMode=Timp și prezență
att_shift_shiftNormal=Glisați cartela în mod normal după schimbul de lucru
att_shift_oneDayOneCard=Glisați cartela validă o dată pe zi
att_shift_onlyBrushTime=Numără numai timpul cartelei      
att_shift_notBrushCard=Fără glisare
att_shift_overtimeMode=Timp suplimentar
att_shift_autoCalc=Calculare automată computerizată
att_shift_mustApply=Se aplică timp suplimentar
att_shift_mustOvertime=Trebuie timp suplimentar sau absență
att_shift_timeSmaller=Durata este mai mică
att_shift_notOvertime=Nu se consideră timp suplimentar
att_shift_overtimeSign=Notă timp suplimentar
att_shift_normal=De obicei
att_shift_restday=Zi liberă
att_shift_timeSlotDetail=Detalii perioadă timp
att_shift_doubleDeleteTimeSlot=Faceți dublu click pe Perioadă schimbului de lucru pentru a-l șterge
att_shift_addTimeSlot=Mărește Perioadă
att_shift_cleanTimeSlot=Șterge Perioadă
att_shift_NO=Prima
att_shift_notAll=Deselectare tot
att_shift_notTime=Căsuța care marchează detaliile perioadei nu poate fi bifată, ceea ce indică faptul că există perioade suprapuse
att_shift_notExistTime=Nu există această perioadă!
att_shift_cleanAllTimeSlot=Confirmați Perioadă pentru a șterge schimbul de lucru selectat?
att_shift_pleaseCheckBox=Vă rugăm să bifați căsuța din lista din stânga care se potrivește cu ora curentă de afișare din dreapta
att_shift_pleaseUnit=Rugăm completați unitatea de ciclu și numărul ciclului
att_shift_pleaseAllDetailTimeSlot=Selectați detaliile perioadei
att_shift_placeholderNo=Este recomandat să înceapă cu S, de exemplu S01
att_shift_placeholderName=Este recomandat să începeți sau să terminați schimbul de lucru cu litera S 
att_shift_workType=Tipul postului
att_shift_normalWork=Muncă normală
att_shift_holidayOt=Timp suplimentar în zile libere
att_shift_attShiftStartDateRemark=Exemplu: data de început a ciclului este No.22, cu trei zile ca ciclu, apoi No.22/No.23/24 merg la Clasa A/B/C și No.19/20/21 merg la Clasa A, respectiv B/C, și așa mai departe.
att_shift_isShiftWithinMonthRemark1=Schimburile într-o lună, ciclul se termină pînă în ultima zi a fiecărei luni și nu se planifică continuu pe parcursul lunilor;
att_shift_isShiftWithinMonthRemark2=Pentru schimburi nelunare, când ciclul nu se termină până în ultima zi a fiecărei luni, dacă un ciclu nu s-a încheiat, continuați până la luna următoare, etc.
att_shift_workTypeRemark1=Muncă normală, notele timpului suplimentar nu sunt considerate ca zile libere, excepție fac zilele libere. Dacă timpul suplimentar este marcate ca zi liberă, durata orelor suplimentare se înregistrează ca zile libere (se exclud orele normale de lucru);
att_shift_workTypeRemark2=Orele suplimentare la sfârșit de săptămână, orele suplimentare sunt implicite în ziua de odihnă, iar calculatorul calculează automat orele suplimentare. Nu este necesară o solicitare de ore suplimentare. Orele de lucru ale zilei sunt înregistrate ca ore suplimentare, iar prezența nu este luată în calcul la zilele libere;
att_shift_workTypeRemark3=Orele suplimentare în zilele libere, marcajul orei prestabilite este implicit în concedii, iar computerul calculează automat orele suplimentare, nu este necesară o aplicare de ore suplimentare, iar orele de lucru ale zilei sunt înregistrate ca ore suplimentare;
att_shift_attendanceModeRemark1=Cu excepția cardului de credit obișnuit prin schimb, nu se consideră ca ore suplimentare lucrate în avans sau cu întârziere, de exemplu:
att_shift_attendanceModeRemark2=1. Nu este necesară înregistrarea sau se folosește un card valid o dată pe zi, nu se iau în calcul orele suplimentare;
att_shift_attendanceModeRemark3=2. Tip de muncă: muncă normală, metoda prezenței: fără glisare cartelă, timpul de lucru pe zi se consideră muncă efectuată;
att_shift_periodStartMode=Tipul de pornire al ciclului
att_shift_periodStartModeByPeriod=Dată start ciclu
att_shift_periodStartModeBySch=Planificare data început
att_shift_addTimeSlotToShift=Dacă se adaugă intervalul de timp al acestei schimbări
#================================================== ===================
#Grupare
att_group_editGroup=Editare grup
att_group_browseGroupPerson=Căutați persoane din grup
att_group_list=Lista grupuri
att_group_placeholderNo=Este recomandat să începeți cu G, cum ar fi G1
att_group_placeholderName=Este recomandat să începeți sau să terminați un grup cu G 
att_widget_deptHint=Notă: importați tot personalul departamentului selectat
att_widget_searchType=Interogare condiționată
att_widget_noPerson=Nu este selectat nimeni
#Schimb de grup
#Planificarea departamentului
att_deptSch_existsDept=Este un schimb de lucru în acest departament și nu se poate șterge acest departament.
#Planificarea personalului
att_personSch_view=Vizualizează schimbul de lucru al personalului
#Schimb temporar
att_schedule_type=Tipul schimbului de lucru
att_schedule_tempType=Tipul temporar
att_schedule_normal=Programare ordinară
att_schedule_intelligent=Programare inteligentă
att_tempSch_scheduleType=Tipul schimbului de lucru
att_tempSch_startDate=Dată start
att_tempSch_endDate=Dată sfârșit
att_tempSch_attendanceMode=Timp și prezență
att_tempSch_overtimeMode=Timp suplimentar
att_tempSch_overtimeRemark=Notă timp suplimentar
att_tempSch_existsDept=Este o programare temporară a departamentului în acest departament, și nu este permis ștergerea departamentului.
att_schedult_opAddTempSch=Adaugă programare temporară
att_schedule_cleanEndDate=Șterge timpul de încheiere
att_schedule_selectOne=Schimbul de lucru normal poate să aleagă numai un singur schimb!
att_schedule_selectPerson=Rugăm, selectați personalul mai întâi!
att_schedule_selectDept=Rugăm, selectați departamentul mai întâi!
att_schedule_selectGroup=Rugăm, selectați grupa mai întâi!
att_schedule_selectOneGroup=Se poate selecta numai un singur grup!
att_schedule_arrange=Rugăm, selectați un schimb!
att_schedule_leave=Concediu
att_schedule_trip=Excursie
att_schedule_out=Afară
att_schedule_off=Hugh
att_schedule_makeUpClass=Compensare
att_schedule_class=Ajustare
att_schedule_holiday=Festival
att_schedule_offDetail=Reglare
att_schedule_makeUpClassDetail=Recuperare
att_schedule_classDetail=Schimb
att_schedule_holidayDetail=Vacanță
att_schedule_noSchDetail=Nu este programată
att_schedule_normalDetail=Normal
att_schedule_normalSchInfo=Schimb de lucru centru: cu schimburi fără trecere de la o zi la alta
att_schedule_multipleInterSchInfo=Schimburi separate prin virgulă: schimburi cu trecere de la o zi la alta
att_schedule_inderSchFirstDayInfo=Schimburi înapoi de la o zi la alta: ziua schimbului este considerată prima zi de lucru
att_schedule_inderSchSecondDayInfo=Schimburi înainte de la o zi la alta: ziua schimbului este considerată a doua zi de lucru
att_schedule_timeConflict=Conflict cu Perioadă schimbului de lucru existent, nu se poate salva!
#================================================== ===================
att_excp_notExisetPerson=Persoana nu există!
att_excp_leavePerson=Demisie personal!
#Aprobare
att_sign_signTime=Timpul semnării
att_sign_signDate=Data semnării
#Părăsire
att_leave_arilName=Numele clasă falsă
att_leave_image=Lasă fotografie
att_leave_imageShow=Fără fotografie
att_leave_imageType=Eroare: format incorect. Format imagine acceptat: JPEG, GIF, PNG!
att_leave_imageSize=Eroare: mărimea imaginii este prea mare, mărimea maximă acceptată 4M!
att_leave_leaveLongDay=Data plecării (zile)
att_leave_leaveLongHour=Data plecării (ore)
att_leave_leaveLongMinute=Data plecării (minute)
att_leave_endNoLessAndEqualStart=Ora de încheiere nu poate fi mai mică sau egală cu ora de începere
att_leave_typeNameNoExsists=Numele clasei false nu există
att_leave_startNotNull=Ora de început nu poate fi goală
att_leave_endNotNull=Ora de încheiere nu poate fi goală
att_leave_typeNameConflict=Numele de tip fals intră în conflict cu numele stării prezenței
#Voiaj
att_trip_tripLongDay=Timp călătorie (zile) 
att_trip_tripLongMinute=Timp călătorie (minute)
att_trip_tripLongHour=Timp călătorie (ore)
#Ieșire
att_out_outLongDay=Pauză (zile)
att_out_outLongMinute=Pauză (minute)
att_out_outLongHour=Pauză (Oră)
#Ore suplimentare
att_overtime_type=Tipul orelor suplimentare
att_overtime_normal=Ore suplimentare obișnuite
att_overtime_rest=Ore suplimentare pe zi de odihnă
att_overtime_overtimeLong=Durata timpului suplimentar (minute)
att_overtime_overtimeHour=Durata timpului suplimentar (ore)
att_overtime_notice=Cererea pentru timp suplimentar nu poate depăși o zi!
att_overtime_minutesNotice=Cererea timpului suplimentar nu poate fi mai mică decât cea mai scurtă durată!
#Ajustarea la odihnă
att_adjust_type=Tipul ajustării
att_adjust_adjustDate=Data ajustării
att_adjust_shiftName=Numărul schimbului de remediere
att_adjust_selectClass=Selectați numele schimbului pentru a putea edita!
att_shift_notExistShiftWorkDate=Data ajustării nu este pe ziua zilei de lucru când orele suplimentare au fost efectuate și nu poate fi adăugată
att_adjust_shiftPeriodStartMode=Schimbul selectat pentru Remediere schimb. Dacă data începerii este programată, în mod implicit este 0.
att_adjust_shiftNameNoNull=Schimbările nu pot fi goale
att_adjust_shiftNameNoExsist=Schimbarea de schimb nu există
#Schimb
att_class_type=Tipul schimbului de lucru
att_class_sameTimeMoveShift=Ajustare individuală în aceeași zi
att_class_differenceTimeMoveShift=Ajustare individuală în zile diferite
att_class_twoPeopleMove=Schimbare două persoane
att_class_moveDate=Data schimbării
att_class_shiftName=Ajustare Numele schimb de lucru
att_class_moveShiftName=Numele schimbare
att_class_movePersonPin=Schimbare numărul de angajat
att_class_movePersonName=Numelele persoanei Schimbate
att_class_movePersonLastName=Comutarea proNumelelui personal al persoanei
att_class_moveDeptName=Numelele departamentului Schimbat
att_class_personPin=Numărul de angajat nu poate fi gol
att_class_shiftNameNoNull=Ajustare schimb de lucru nu poate fi gol
att_class_personPinNoNull=Numărul de angajat schimbate nu poate fi gol!
att_class_isNotExisetSwapPersonPin=Numărul de angajat schimbate nu există, adăugați din nou!
att_class_personNoSame=Ajustarea și persoanele schimbate nu pot fi aceeași, rugăm adăugați din nou!
att_class_outTime=Data ajustării și data schimbării nu pot fi la o distanță mai mari decât o lună!
att_class_shiftNameNoExsist=Schimbarea ajustării nu există
att_class_swapPersonNoExisist=Matchmakerul nu există
att_class_dateNoSame=Persoanele sunt transferate la date diferite, datele nu pot fi aceleași
#================================================== ===================
#Nodul
att_node_name=Nodul
att_node_type=Tip nodul
att_node_leader=Superior direct
att_node_leaderNode=Nodul superior direct
att_node_person=Persoana desemnată
att_node_position=Poziția desemnată
att_node_choose=Alegeți o poziție
att_node_personNoNull=Personalul nu este completat
att_node_posiitonNoNull=Poziția nu poate să fie goală
att_node_placeholderNo=Este recomandat să începeți cu N, cum ar fi N01
att_node_placeholderName=Este recomandat să începeți cu o poziție sau Numelele și să terminați cu un nodul, cum ar fi un nodul supervizor
att_node_searchPerson=Introduceți condițiile de căutare
att_node_positionIsExist=Poziția deja există în datele de nodul, reselectați poziția
#Fluxul procesului
att_flow_type=Tipul fluxului procesului
att_flow_rule=Regulile fluxului procesului
att_flow_rule0=1 zi sau mai puțin
att_flow_rule1=Mai mult de o zi, dar mai puțin sau egal cu 3 zile
att_flow_rule2=Mai mult de o 3 zi, dar mai puțin sau egal cu 7 zile
att_flow_rule3=Mai mult de 7 zile
att_flow_node=Nodul aprobare
att_flow_start=Inițiați procesul
att_flow_end=Terminați procesul
att_flow_addNode=Adaugă nodul
att_flow_placeholderNo=Este recomandat să începeți cu F, cum ar fi F01
att_flow_placeholderName=Începutul tipului sugerat și sfârșitul procesului, cum ar fi procesul de plecare
att_flow_tips=Notă: Secvența de aprobare a nodurilor este de sus în jos și puteți trage pentru a sorta după selecție.
#Aplicație
att_apply_personPin=Numărul de angajat aplicantului
att_apply_type=Tip excepție
att_apply_flowStatus=Statusul general al fluxului procesului
att_apply_start=Inițiere aplicație
att_apply_flowing=În fluxul procesului
att_apply_pass=Trece
att_apply_over=Sfârși
att_apply_refuse=Refuză
att_apply_revoke=Revocă
att_apply_except=Anormal
att_apply_view=Vezi detaliile
att_apply_leaveTips=În acest interval au fost învoiri din partea personalului!
att_apply_tripTips=În acest interval a existat personal care a aplicat pentru călătorie de business
att_apply_outTips=Persoana are o aplicație pentru a ieși în acest interval!
att_apply_overtimeTips=Există personal care a aplicat pentru ore suplimentare în acest interval!
att_apply_adjustTips=Există personal care a aplicat pentru ajustare și remediere în acest interval!
att_apply_classTips=Există personal care a aplicat pentru ajustare schimb de lucru în acest interval!
#Aprobă
att_approve_wait=Așteptare aprobare
att_approve_refuse=Eșuată
att_approve_reason=Cauză
att_approve_personPin=Aprobă numărul de angajat
att_approve_personName=Aprobă Numele angajat
att_approve_person=Aprobă angajat
att_approve_isPass=Aprobat sau nu
att_approve_status=Status nodul curent
att_approve_tips=În acest moment, există deja o înregistrare în proces, nu este posibil să se aplice în mod repetat
att_approve_tips2=Nodulul procesului nu a fost configurat, rugați administratorul să configureze
att_approve_offDayConflicts=Aplicația pentru ajustare învoiri nu este permisă în zilele nelucrătoare
att_approve_shiftConflicts=Aplicația pentru ajustare schimb de lucru nu este permisă în zilele lucrătoare
att_approve_shiftNoSch=Aplicația pentru remediere schimb de lucru nu este permisă pe datele neprogramate
att_approve_classConflicts=Cererile de ajustate nu sunt permise pe date neprogramate
att_approve_selectTime=După selectarea timpului, procesul va fi judecat în funcție de reguli
att_approve_withoutPermissionApproval=Există un flux de lucru fără permisiunea pentru aprobare, vă rugăm să verificați!
#================================================== ===================
#Calculare prezență
att_op_calculation=Calculare prezență
att_op_calculation_notice=Datele de prezență sunt deja calculate în fundal, încercați mai târziu din nou!
att_op_calculation_leave=Inclusiv angajații demisionați
att_statistical_choosePersonOrDept=Rugăm, selectați departamentul sau persoana!
att_statistical_sureCalculation=Doriți să procesați calcularea prezenței?
att_statistical_filter=Condițiile de filtrare sunt gata!
att_statistical_initData=Inițializare date de bază finalizată!
att_statistical_exception=Inițializarea datelor anormale finalizată!
att_statistical_error=Calcularea prezenței eșuată!
att_statistical_begin=Începe calcularea!
att_statistical_end=Termină calcularea!
att_statistical_noticeTime=Interval prezență selectabil: primele două luni la zi!
att_statistical_remarkHoliday=Festival
att_statistical_remarkClass=Schimb
att_statistical_remarkNoSch=Nu
att_statistical_remarkRest=Hugh
#Formular original de înregistrare
att_op_importAccRecord=Import înregistrări acces control
att_op_importParkRecord=Import înregistrări parcare
att_op_importInsRecord=Import înregistrări ecran info
att_op_importPidRecord=Import înregistrări martori
att_op_importVmsRecord=Import înregistrări video
att_op_importUSBRecord=Import înregistrări U disk
att_transaction_noAccModule=Nu există modul acces control
att_transaction_noParkModule=Nu există modul parcare
att_transaction_noInsModule=Nu există modul ecran info!
att_transaction_noPidModule=Nu există modul martori!
att_transaction_exportRecord=Exportă înregistrările originale
att_transaction_exportAttPhoto=Exportați fotografiile de prezență
att_transaction_fileIsTooLarge=Fișierul exportat este prea mare, vă rugăm să restrângeți intervalul de date
att_transaction_exportDate=Data exportului
att_statistical_attDatetime=Dată și timp prezență
att_statistical_attPhoto=Fotografie prezență
att_statistical_attDetail=Detalii prezență
att_statistical_acc=Dispozitiv de control acces
att_statistical_att=Dispozitiv de prezență și timp
att_statistical_park=Dispozitiv parcare
att_statistical_faceRecognition=Dispozitiv de recunoaștere a feței
att_statistical_app=Dispozitiv telefon mobil
att_statistical_vms=Dispozitiv video
att_statistical_psg=Echipament de canal
att_statistical_dataSources=Surse date
att_transaction_SyncRecord=Sincronizați înregistrările de prezență
#Tabelul zilnic cu detalii despre glisare carduri
att_statistical_dayCardDetail=Introduceți detalii
att_statistical_cardDate=Dată glisare cartelă
att_statistical_cardNumber=Orele glisare cartelă
att_statistical_earliestTime=Prima glisare
att_statistical_latestTime=Ultima glisare
att_statistical_cardTime=Timp glisare cartelă
#Tabel sumar de plecări
att_statistical_leaveDetail=Detalii plecare
#Raport zilnic
att_statistical_attDate=Dată prezență
att_statistical_week=Săptămână
att_statistical_shiftInfo=Informații schimb de lucru
att_statistical_shiftTimeData=Timp de navetă
att_statistical_cardValidData=Date glisare cartelă
att_statistical_cardValidCount=Orele glisare cartelă
att_statistical_lateCount=Perioade întârziere
att_statistical_lateMinute=Minute întârziere
att_statistical_earlyCount=Număr plecare timpurie
att_statistical_earlyMinute=Minute plecare timpurie
att_statistical_countData=Date perioade
att_statistical_minuteData=Date minute
att_statistical_attendance_minute=Prezență (minute)
att_statistical_overtime_minute=Timp suplimentar (minute)
att_statistical_unusual_minute=Anormal (minute)
#Raport lunar
att_monthdetail_should_hour=Ar trebui
att_monthdetail_actual_hour=Actual
att_monthdetail_valid_hour=Valid (oră)
att_monthdetail_absent_hour=Absență (ore)
att_monthdetail_leave_hour=Plecare (timp)
att_monthdetail_trip_hour=Călătorie business (ore)
att_monthdetail_out_hour=Ieșire
att_monthdetail_should_day=Ar trebui (zi)
att_monthdetail_actual_day=Actual (zi)
att_monthdetail_valid_day=Efectiv (zi)
att_monthdetail_absent_day=Absență (zi)
att_monthdetail_leave_day=Plecare (zi)
att_monthdetail_trip_day=Călătorie business (zi)
att_monthdetail_out_day=Ieșire (zi)
#Raport lunar statistic
att_statistical_late_minute=Durată (minute)
att_statistical_early_minute=Durată (minute)
#Raport statistic departament
#Raport statistic anual
att_statistical_should=Ar trebui
att_statistical_actual=Actual
att_statistical_valid=Efectiv
att_statistical_numberOfTimes=Dăți
att_statistical_usually=De obicei
att_statistical_rest=Zile libere
att_statistical_holiday=Festival
att_statistical_total=Total
att_statistical_month=Lună statistică
att_statistical_year=An statistic
att_statistical_attendance_hour=Prezență (ore)
att_statistical_attendance_day=Prezență (zi)
att_statistical_overtime_hour=Timp suplimentar
att_statistical_unusual_hour=Anormal
att_statistical_unusual_day=Anormal (zi)
#Parametrii dispozitiv timp și prezență
att_deviceOption_query=Vizualizează parametrii dispozitiv
att_deviceOption_noOption=Nu sunt informații parametru, mai întâi obțineți parametrii dispozitivului
att_deviceOption_name=Numelele parametru
att_deviceOption_value=Valoare parametru
att_deviceOption_UserCount=Utilizatori curenți
att_deviceOption_MaxUserCount=Număr maxim utilizator
att_deviceOption_FaceCount=Număr curent șabloane faciale
att_deviceOption_MaxFaceCount=Număr maxim șabloane faciale
att_deviceOption_FacePhotoCount=Număr curent imagini faciale
att_deviceOption_MaxFacePhotoCount=Număr maxim imagini faciale
att_deviceOption_FingerCount=Număr curent șabloane amprente
att_deviceOption_MaxFingerCount=Număr maxim șabloane amprente
att_deviceOption_FingerPhotoCount=Număr curent imagini amprente
att_deviceOption_MaxFingerPhotoCount=Număr maxim imagini amprente
att_deviceOption_FvCount=Număr curent șabloane vene degete
att_deviceOption_MaxFvCount=Număr maxim șabloane vene degete
att_deviceOption_FvPhotoCount=Număr curent imagini vene degete
att_deviceOption_MaxFvPhotoCount=Număr maxim imagini vene degete
att_deviceOption_PvCount=Număr curent șabloane palmă
att_deviceOption_MaxPvCount=Număr maxim șabloane palmă
att_deviceOption_PvPhotoCount=Număr curent imagini palmă
att_deviceOption_MaxPvPhotoCount=Număr maxim imagini palmă
att_deviceOption_TransactionCount=Înregistrări curente
att_deviceOption_MaxAttLogCount=Număr maxim înregistrări
att_deviceOption_UserPhotoCount=Fotografii utilizatori curenți
att_deviceOption_MaxUserPhotoCount=Număr maxim fotografii utilizatori
att_deviceOption_FaceVersion=Versiune algoritm recunoaștere față
att_deviceOption_FPVersion=Versiune algoritm recunoaștere amprentă
att_deviceOption_FvVersion=Versiune algoritm recunoaștere vene degete
att_deviceOption_PvVersion=Versiune algoritm recunoaștere palmă
att_deviceOption_FWVersion=Versiunea softului
att_deviceOption_PushVersion=Versiune push
#================================================== ===================
#Api
att_api_areaCodeNotNull=Numărul zonei nu poate fi gol
att_api_pinsNotNull=Datele pin nu poate fi goale
att_api_pinsOverSize=Lungimea datelor pin nu poate să depășească 500
att_api_areaNoExist=Zona nu există
att_api_sign=Semnarea remedierii
#================================================== ===================
#Perioadă odihnă
att_leftMenu_breakTime=Perioadă odihnă
att_breakTime_startTime=Timp început
att_breakTime_endTime=Timp sfârșit
#================================================== ===================
#Import înregistrări U disk
att_import_uploadFileSuccess=Fișier încărcat cu reușit  și datele au fost analizate. Rugăm așteptați...
att_import_resolutionComplete=După finalizarea analizei, baza de date este actualizată.
att_import_snNoExist=Dispozitivul de prezență corespunzător fișierului importat nu există. Vă rugăm să selectați din nou fișierul.
att_import_fileName_msg=Cerințe format fișier importat: începutul numărului de serie al dispozitivului separat de o linie de subliniere "_", de exemplu: "3517171600001_attlog.dat".
att_import_notSupportFormat=Acest format nu este suportat!
att_import_selectCorrectFile=Rugăm selectați un format de fișier corect!
att_import_fileFormat=Format fișier
att_import_targetFile=Fișier țintă
att_import_startRow=Numărul rândului de început al antetului tabelului
att_import_startRowNote=Prima linie a formatului de date este de a importa date. Verificați fișierul înainte de a importa.
att_import_delimiter=Delimitator
#================================================== ===================
#Jurnalul de operare al dispozitivului
att_device_op_log_op_type=Cod operare
att_device_op_log_dev_sn=Numărul de serie al dispozitivului
att_device_op_log_op_content=Conținut operare
att_device_op_log_operator_pin=Numărul de angajat operator
att_device_op_log_operator_name=Numelele operator
att_device_op_log_op_time=Timpul de funcționare
att_device_op_log_op_who_value=Valoarea obiectului de operare
att_device_op_log_op_who_content=Descrierea obiectului de operare
att_device_op_log_op_value1=Obiect operare 2
att_device_op_log_op_value_content1=Descrierea obiectului de operare 2
att_device_op_log_op_value2=Obiect operare 3
att_device_op_log_op_value_content2=Descrierea obiectului de operare 3
att_device_op_log_op_value3=Obiect operare 4
att_device_op_log_op_value_content3=Descrierea obiectului de operare 4
#Tipul de operare al jurnalului de operare
att_device_op_log_opType_0=Pornește
att_device_op_log_opType_1=Închide
att_device_op_log_opType_2=Verificare eșuată
att_device_op_log_opType_3=Sunați la politie
att_device_op_log_opType_4=Introduce meniul
att_device_op_log_opType_5=Schimbare setări
att_device_op_log_opType_6=Înscrieți amprentă
att_device_op_log_opType_7=Înregistrare parolă
att_device_op_log_opType_8=Înregistrare cartelă HID
att_device_op_log_opType_9=Șterge utilizatori
att_device_op_log_opType_10=Șterge amprentă
att_device_op_log_opType_11=Șterge parolă
att_device_op_log_opType_12=Șterge cartelă RF
att_device_op_log_opType_13=Șterge date
att_device_op_log_opType_14=Șterge cartelă MF
att_device_op_log_opType_15=Înregistrează cartelă MF
att_device_op_log_opType_16=Înregistrează cartelă MF
att_device_op_log_opType_17=Șterge înregistrare cartelă MF
att_device_op_log_opType_18=Șterge conținut cartelă MF
att_device_op_log_opType_19=Mutare date înregistrare pe cartelă
att_device_op_log_opType_20=Copiază datele cartelei pe dispozitiv
att_device_op_log_opType_21=Setare timp
att_device_op_log_opType_22=Setări de fabrică
att_device_op_log_opType_23=Șterge înregistrările de intrare și ieșire
att_device_op_log_opType_24=Șterge drepturi administrator
att_device_op_log_opType_25=Modifică setările grupului control acces
att_device_op_log_opType_26=Modifică setările utilizatorului controlului acces
att_device_op_log_opType_27=Modifică Perioadă controlului acces
att_device_op_log_opType_28=Modifică setările combinației de deblocare
att_device_op_log_opType_29=Deblocare
att_device_op_log_opType_30=Înregistrează utilizator nou
att_device_op_log_opType_31=Schimbă atribute amprentă
att_device_op_log_opType_32=Alarmă de protecție
att_device_op_log_opType_34=Anti-deschidere multiplă
att_device_op_log_opType_35=Șterge fotografie prezență
att_device_op_log_opType_36=Modifică alte informații utilizator
att_device_op_log_opType_37=Vacanță
att_device_op_log_opType_38=Recuperarea datelor
att_device_op_log_opType_39=Salvare rezervă date
att_device_op_log_opType_40=Încărcare U disk
att_device_op_log_opType_41=Descărcare U disk
att_device_op_log_opType_42=Criptare înregistrare prezență pe U disc
att_device_op_log_opType_43=Șterge înregistrarea după ce a fost descărcată cu reușit  a discului U
att_device_op_log_opType_53=Comutator de ieșire
att_device_op_log_opType_54=Senzor pentru ușă
att_device_op_log_opType_55=Sunați la politie
att_device_op_log_opType_56=Parametrii recuperare
att_device_op_log_opType_68=Înregistrare fotografii utilizatori
att_device_op_log_opType_69=Editează fotografie utilizator
att_device_op_log_opType_70=Editează Numelele utilizator
att_device_op_log_opType_71=Modifică niveluri utilizator
att_device_op_log_opType_76=Modifică IP setări rețea 
att_device_op_log_opType_77=Modifică mască setări rețea
att_device_op_log_opType_78=Modifică portal setări rețea
att_device_op_log_opType_79=Modifică DNS setări rețea
att_device_op_log_opType_80=Schimbă parola setării conexiunii
att_device_op_log_opType_81=Modifică ID - ul setării conexiunii
att_device_op_log_opType_82=Modifică adresa serverului cloud
att_device_op_log_opType_83=Modifică port server cloud
att_device_op_log_opType_87=Modifică setări înregistrare control acces
att_device_op_log_opType_88=Modifică pavilion parametrii față
att_device_op_log_opType_89=Modifică pavilion parametrii amprentă
att_device_op_log_opType_90=Modifică pavilion parametrii vene degete
att_device_op_log_opType_91=Modifică pavilion parametrii palmă
att_device_op_log_opType_92=Logo actualizare u disk
att_device_op_log_opType_100=Modifică informații cartelă RF
att_device_op_log_opType_101=Înregistrează față
att_device_op_log_opType_102=Modifică niveluri personal
att_device_op_log_opType_103=Șterge niveluri personal
att_device_op_log_opType_104=Crește niveluri personal
att_device_op_log_opType_105=Șterge înregistrare control acces
att_device_op_log_opType_106=Șterge față
att_device_op_log_opType_107=Șterge fotografie persoană
att_device_op_log_opType_108=Schimbă parametrii
att_device_op_log_opType_109=Alege WIFISSID
att_device_op_log_opType_110=Deblochează proxy
att_device_op_log_opType_111=Modifică proxyip
att_device_op_log_opType_112=Modificare port proxy
att_device_op_log_opType_113=Schimbare parolă personal
att_device_op_log_opType_114=Modifică informații față
att_device_op_log_opType_115=Schimbă parolă operator
att_device_op_log_opType_116=Recuperează setări control acces
att_device_op_log_opType_117=Eroare input parolă operator
att_device_op_log_opType_118=Blocare parolă operator
att_device_op_log_opType_120=Modificați lungimea datelor legale ale cardului
att_device_op_log_opType_121=Înregistrează vena degetului
att_device_op_log_opType_122=Modificați vena degetului
att_device_op_log_opType_123=Șterge Vena Degetului
att_device_op_log_opType_124=Înregistrează Palm
att_device_op_log_opType_125=Modifică Palm
att_device_op_log_opType_126=Șterge Palm
#Descrierea obiectului de operare
att_device_op_log_content_pin=Număr utilizator personal:
att_device_op_log_content_alarm=Alarmă:
att_device_op_log_content_alarm_reason=Cauza alarmei:
att_device_op_log_content_update_no=Modifică numărul de serie al articolului:
att_device_op_log_content_update_value=Modifică valoarea articolului:
att_device_op_log_content_finger_no=Numărul de serie al amprentei:
att_device_op_log_content_finger_size=Lungimea șablonului amprentei:
#================================================== ===================
#Fluxul de lucru
att_flowable_datetime_to=La
att_flowable_todomsg_leave=Aprobă învoire
att_flowable_todomsg_sign=Remediere semnare aprobare
att_flowable_todomsg_overtime=Aprobă timp suplimentar
att_flowable_notifymsg_leave=Notificare învoire
att_flowable_notifymsg_sign=Notificare pentru semnare remediere
att_flowable_notifymsg_overtime=Notificare muncă suplimentară
att_flowable_shift=Schimb:
att_flowable_hour=Oră
att_flowable_todomsg_trip=Aprobă călătorie business
att_flowable_notifymsg_trip=Notificare călătorie Business
att_flowable_todomsg_out=Aprobare ieșire
att_flowable_notifymsg_out=Notificare ieșire
att_flow_apply=Aplicație
att_flow_applyTime=Timp de aplicare
att_flow_approveTime=Timp procesare
att_flow_operateUser=Aprobă personal
att_flow_approve=Aprobă
att_flow_approveComment=Adnotare
att_flow_approvePass=Rezultate aprobare
att_flow_status_processing=În curs de aprobare
#================================================== ===================
#Biotimp
att_h5_pers_personIdNull=Angajatul nu poate fi gol
att_h5_attPlaceNull=Locație check-in nu poate fi gol
att_h5_attAreaNull=Zonă prezență nu poate fie goală
att_h5_pers_personNoExist=Număr angajat inexistent
att_h5_signRemarkNull=Observațiile nu poate fi goale
att_h5_common_pageNull=Eroare parametru paginare
att_h5_taskIdNotNull=Nodul sarcină nu poate fi gol
att_h5_auditResultNotNull=Rezultat aprobare nu poate fi gol
att_h5_latLongitudeNull=Latitudinea și longitudinea nu pot fi goale
att_h5_pers_personIsNull=Id angajat inexistent 
att_h5_pers_personIsNotInArea=Persoana nu stabilește zona
att_h5_mapApiConnectionsError=Eroare conexiune hartă api
att_h5_googleMap=Hartă google
att_h5_gaodeMap=Hartă gaode
att_h5_defaultMap=Hartă implicită
att_h5_shiftTime=Schimbarea timpului de programare
att_h5_signTimes=Timpul semnării de remediere
att_h5_enterKeyWords=Rugăm introduceți cuvinte cheie:
att_h5_mapSet=Setări hartă prezență
att_h5_setMapApiAddress=Setare parametrii hartă
att_h5_MapSetWarning=Comutarea hărții va duce la imposibilitatea de a corespunde longitudinea și latitudinea adresei înregistrate a terminalului mobil, vă rugăm să o modificați cu atenție!
att_h5_mapSelect=Selectare hartă
att_h5_persNoHire=Angajatul nu s-a alăturat încă
att_slef_apiKey=Api-key
att_self_apiJsKey=Api-js-key
att_self_noComputeAtt=Prezența nu a fost calculată
att_self_noSignRecord=Nu s-au efectuat glisări cartelă azi
att_self_imageUploadError=Încărcarea imaginii a eșuat
att_self_attSignAddressAreaIsExist=Puncte check-in în această zonă
att_self_signRuleIsError=Ora actuală de semnare a remedierii nu se încadrează în termenul admis de reînnoire
att_self_signAcrossDay=Programarea schimbului de la o zi la alta nu se poate avea remediere semnată!
att_self_todaySignIsExist=Astăzi există semnare remediere!
att_self_signSetting=Setări semnare remediere
att_self_allowSign=Permite semnare remediere:
att_self_allowSignSuffix=Înregistrare prezență între zile
att_self_onlyThisMonth=Numai această lună
att_self_allowAcrossMonth=Permite lună încrucișată
att_self_thisTimeNoSch=Nu există schimburi programate în Perioadă curentă!
att_self_revokeReason=Cauzele anulării:
att_self_revokeHint=Rugăm introduceți cauzele anulării în 20 de caractere pentru verificare
att_self_persSelfLogin=Autoînregistrare angajat
att_self_isOpenSelfLogin=Dacă se permite autoînregistrare angajat
att_self_applyAndWorkTimeOverlap=Timpul de aplicare și timpul de lucru se suprapun
att_apply_DurationIsZero=Durata aplicării este 0, nu este permisă aplicarea
att_sign_mapWarn=Nu s-a încărcat harta, verificați conexiunea de rețea și valoarea CHEIE a hărții
att_admin_applyWarn=Operare eșuată, există persoane care nu sunt programate sau timpul de aplicare nu se încadrează în domeniul de programare! ({0})
att_self_getPhotoFailed=Imaginea nu există
att_self_view=Vizualizare
# Cod QR
att_param_qrCodeUrl=Url cod QR
att_param_qrCodeUrlHref=Adresă server: port
att_param_appAttQrCode=Cod QR prezență mobilă
att_param_timingFrequency=Interval timp: 5-59 minute sau 1-24 Oră
att_sign_signTimeNotNull=Timpul de reînnoire nu poate fi gol
att_apply_overLastMonth=Timpul de aplicare a depășit ultima lună
att_apply_withoutDetail=Nu există detalii proces
att_flowable_noAuth=Rugăm folosiți cont administrator super pentru vizualizare
att_apply_overtimeOverMaxTimeLong=Durata timpului suplimentar este mai mare decât durata timpului suplimentar maxim
# Simbol setare parametru prezență
att_other_arrive=√
att_other_late=L
att_other_early=E
att_other_absent=□
att_other_noSignIn=[
att_other_noSignOff=]
att_other_leave=∆
att_other_overtime=OT
att_other_off=○
att_other_classes=●
att_other_trip=T
att_other_out=G
att_other_incomplete=├
att_other_outcomplete=┤
# 服务器下发命令
att_devCmd_submitTime=Timp de trimitere
att_devCmd_returnedResult=Returnează rezultatul
att_devCmd_returnTime=ora returnării
att_devCmd_content=Conținutul comenzii
att_devCmd_clearCmd=Ștergeți lista de comenzi
# 实时点名
att_realTime_selectDept=Vă rugăm să selectați un departament
att_realTime_noSignPers=Persoană neînregistrată
att_realTime_signPers=S-a înregistrat
att_realTime_signMonitor=Monitorizarea conectării
att_realTime_signDateTime=Ora de check-in
att_realTime_realTimeSet=Setare apel în timp real
att_realTime_openRealTime=Activează apel în timp real
att_realTime_rollCallEnd=Apelul nominal în timp real se încheie
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=Programat
att_personSch_cycleSch=Programarea ciclului
att_personSch_cleanCycleSch=Programul curat al ciclului
att_personSch_cleanTempSch=Șterge programul temporar
att_personSch_personCycleSch=Programul ciclului persoanei
att_personSch_deptCycleSch=Programul ciclului departamentului
att_personSch_groupCycleSch=Programarea ciclului de grup
att_personSch_personTempSch=Program temporar de persoană
att_personSch_deptTempSch=Program temporar al departamentului
att_personSch_groupTempSch=Programare temporară de grup
att_personSch_checkGroupFirst=Vă rugăm să verificați grupul din stânga sau persoana din lista dreaptă pentru a opera!
att_personSch_sureDeleteGroup=Sunteți sigur că ștergeți {0} și programul corespunzător grupului?
att_personSch_sch=Program
att_personSch_delSch=Șterge programul
#考勤计算
att_statistical_sureAllCalculate=Sunteți sigur că efectuați calculul prezenței pentru tot personalul?
#异常管理
att_exception_downTemplate=Descarcă șablonul de import
att_exception_signImportTemplate=Șablon de import jurnal adăugat
att_exception_leaveImportTemplate=Părăsește șablonul de import
att_exception_overtimeImportTemplate=Șablon de import ore suplimentare
att_exception_adjustImportTemplate=Ajustează șablonul de import
att_exception_cellDefault=Câmpul nu este obligatoriu
att_exception_cellRequired=Câmp obligatoriu
att_exception_cellDateTime=Câmp obligatoriu, formatul orei este aaaa-LL-zz HH: mm: ss, cum ar fi: 07-07-2020 08:30:00
att_exception_cellLeaveTypeName=Câmp obligatoriu, de exemplu: „Concediu ocazional”, „Concediu pentru căsătorie”, „Concediu de maternitate”, „Concediu de boală”, „Concediu anual”, „Concediu pentru doliu”, „Concediu la domiciliu”, „Concediu pentru alăptare”, „Afacere Trip ',' Going Out 'și așa mai departe.
att_exception_cellOvertimeSign=Câmp obligatoriu, pentru eaxmple: 'OT normal', 'OT zi de odihnă', 'OT vacanță'
att_exception_cellAdjustType=Câmp obligatoriu, de exemplu: „Ajustare repaus”, „Așezați prezența”
att_exception_cellAdjustDate=Câmp obligatoriu, formatul orei este aaaa-LL-zz, de exemplu: 07-07-2020
att_exception_cellShiftName=Câmp obligatoriu când tipul de ajustare este „Adaugă prezență”
att_exception_refuse=Refuză
att_exception_end=Sfârșit anormal
att_exception_delete=Șterge
att_exception_stop=Pauză
#时间段
att_timeSlot_normalTimeAdd=Adăugați un interval de timp normal
att_timeSlot_elasticTimeAdd=Adăugați un interval de timp flexibil
#班次
att_shift_addRegularShift=Adăugați o schimbare regulată
att_shift_addFlexibleShift=Adăugați o schimbare flexibilă
#参数设置
att_param_notLeaveSetting=Setare de calcul fără concediu
att_param_smallestUnit=Unitate minimă
att_param_workDay=Ziua de lucru
att_param_roundingControl=Controlul rotunjirii
att_param_abort=Jos (aruncați)
att_param_rounding=rotunjire
att_param_carry=Sus (carry)
att_param_reportSymbol=Simbol de afișare a raportului
att_param_convertCountValid=Vă rugăm să introduceți un număr și este permisă doar o zecimală
att_other_leaveThing=Casual
att_other_leaveMarriage=Căsătorie
att_other_leaveBirth=Maternitate
att_other_leaveSick=Bolnav
att_other_leaveAnnual=Anual
att_other_leaveFuneral=Doliu
att_other_leaveHome=Acasă
att_other_leaveNursing=Alăptarea
att_other_leavetrip=Afaceri
att_other_leaveout=Afară
att_common_schAndRest=Programează și odihnește
att_common_timeLongs=Durata timpului
att_personSch_checkDeptOrPersFirst=Vă rugăm să verificați grupul din stânga sau persoana din lista corectă pentru a opera!
att_personSch_checkCalendarFirst=Vă rugăm să selectați data pe care trebuie să o programați mai întâi!
att_personSch_cleanCheck=Șterge verificarea
att_personSch_delTimeSlot=Șterge perioada de timp selectată
att_personSch_repeatTimeSlotNoAdd=Nu se va adăuga nicio perioadă de timp repetitivă!
att_personSch_showSchInfo=Afișează detaliile programului
att_personSch_sureToCycleSch=Sunteți sigur că programați {0} ciclic?
att_personSch_sureToTempSch=Sunteți sigur că programați {0} temporar?
att_personSch_sureToCycleSchDeptOrGroup=Sunteți sigur că programați tot personalul în {0} ciclic?
att_personSch_sureToTempSchDeptOrGroup=Sunteți sigur că programați tot personalul din {0} temporar?
att_personSch_sureCleanCycleSch=Sigur doriți să ștergeți programul ciclului pentru {0} de la {1} la {2}?
att_personSch_sureCleanTempSch=Sigur doriți să ștergeți schimbarea temporară pentru {0} de la {1} la {2}?
att_personSch_sureCleanCycleSchDeptOrGroup=Sunteți sigur că doriți să ștergeți programul ciclului pentru tot personalul din {0} de la {1} la {2}?
att_personSch_sureCleanTempSchDeptOrGroup=Sunteți sigur că doriți să ștergeți programul temporar pentru tot personalul din {0} de la {1} la {2}?
att_personSch_today=Astăzi
att_personSch_timeSoltName=Numele perioadei de timp
att_personSch_export=Exportarea programării personalului
att_personSch_exportTemplate=Exportați șablonul de schimb temporar al personalului
att_personSch_import=Importarea programării temporare a personalului
att_personSch_tempSchTemplate=Șablon de planificare temporară a personalului
att_personSch_tempSchTemplateTip=Vă rugăm să selectați ora de începere și ora de încheiere a programului, descărcați șablonul de programare în acea dată
att_personSch_opTip=Instrucțiuni de operare
att_personSch_opTip1=1. Puteți trage intervalul de timp într-o singură dată în controlul calendarului pentru a programa.
att_personSch_opTip2=2. În controlul calendarului, faceți dublu clic pe o singură dată pentru a programa.
att_personSch_opTip3=3. În controlul calendarului, țineți apăsat mouse-ul pentru a selecta mai multe date de programat.
att_personSch_schRules=Reguli de programare
att_personSch_schRules1=1. Programarea ciclului: în aceeași zi, programul ulterior va suprascrie programul anterior, indiferent dacă există suprapunere.
att_personSch_schRules2=2. Programare temporară: dacă există o suprapunere în aceeași zi, programul temporar anterior va fi suprascris. Dacă nu există suprapunere, acestea vor exista simultan.
att_personSch_schRules3=3. Cicl și temporar: dacă există o suprapunere în aceeași zi, programul ciclului va suprascrie programul temporar și, dacă nu există o suprapunere, acestea vor exista și simultan.
att_personSch_schStatus=Starea programării
#左侧菜单-排班管理
att_leftMenu_schDetails=Detalii program
att_leftMenu_detailReport=Raport detaliu prezență
att_leftMenu_signReport=Detalii jurnal adăugat
att_leftMenu_leaveReport=Lăsați detalii
att_leftMenu_abnormal=Raport de participare anormal
att_leftMenu_yearLeaveSumReport=Raport sumar concediu anual
att_leave_maxFileCount=Puteți adăuga doar 4 fotografii cel mult
#时间段
att_timeSlot_add=Setați intervalul de timp
att_timeSlot_select=Vă rugăm să selectați o perioadă de timp!
att_timeSlot_repeat=Perioada de timp „{0}” se repetă!
att_timeSlot_overlapping=Perioada de timp „{0}” se suprapune cu timpul de lucru al „{1}”!
att_timeSlot_addFirst=Vă rugăm să setați mai întâi perioada de timp!
att_timeSlot_notEmpty=Perioada de timp corespunzătoare numărului de personal {0} nu poate fi goală!
att_timeSlot_notExist=Perioada de timp „{1}” corespunzătoare numărului de personal {0} nu există!
att_timeSlot_repeatEx=Perioada de timp „{1}” corespunzătoare numărului de personal {0} se suprapune cu timpul de lucru al „{2}”
att_timeSlot_importRepeat=Perioada de timp „{1}” corespunzătoare numărului de personal {0} se repetă
att_timeSlot_importNotPin=Nu există personal cu numărul {0} în sistem!
att_timeSlot_elasticTimePeriod=Numărul personal {0}, nu poate importa perioada de timp flexibilă "{1}"!
#导入
att_import_overData=Numărul actual de importuri este de {0}, depășind limita de 30.000, vă rugăm să importați în loturi!
att_import_existIllegalType={0} importat are un tip ilegal!
#验证方式
att_verifyMode_0=Recunoaștere automată
att_verifyMode_1=Doar amprentă digitală
att_verifyMode_2=Numai Pin
att_verifyMode_3=Numai parola
att_verifyMode_4=Numai card
att_verifyMode_5=Amprentă digitală sau parolă
att_verifyMode_6=Amprentă digitală sau card
att_verifyMode_7=Card sau parolă
att_verifyMode_8=Pin și amprentă
att_verifyMode_9=Amprentă digitală și parolă
att_verifyMode_10=Card și amprentă digitală
att_verifyMode_11=Card și parolă
att_verifyMode_12=Amprentă digitală și parolă și card
att_verifyMode_13=Pin și amprentă digitală și parolă
att_verifyMode_14=(Pin și amprentă digitală) sau (Card și amprentă digitală)
att_verifyMode_15=Față
att_verifyMode_16=Față și amprentă
att_verifyMode_17=Față și parolă
att_verifyMode_18=Față și card
att_verifyMode_19=Față și amprentă digitală și card
att_verifyMode_20=Față și amprentă și parolă
att_verifyMode_21=Vena Degetului
att_verifyMode_22=Vena degetului și parola
att_verifyMode_23=Vena degetelor și cardul
att_verifyMode_24=Vena degetului și parola și cardul
att_verifyMode_25=Palmprint
att_verifyMode_26=Amprentă și card
att_verifyMode_27=Amprentă și față
att_verifyMode_28=Amprentă palmară și amprentă digitală
att_verifyMode_29=Amprentă palmă și amprentă digitală și față
# 工作流
att_flow_schedule=Revizuirea progresului
att_flow_schedulePass=(Trecut)
att_flow_scheduleNot=(Verificați)
att_flow_scheduleReject=(Respins)
# 工作时长表
att_workTimeReport_total=Total ore de lucru
# 自动导出报表
att_autoExport_startEndTime=Ora de început și sfârșit
# 年假
att_annualLeave_setting=Setarea soldului concediului anual
att_annualLeave_settingTip1=Pentru a utiliza funcția de concediu anual de concediu, trebuie să setați ora de intrare pentru fiecare angajat; când ora de intrare nu este setată, concediul anual rămas al angajatului în raportul anual al bilanțului concediului este afișat ca fiind gol.
att_annualLeave_settingTip2=Dacă data curentă este mai mare decât data emiterii compensării, această modificare va intra în vigoare în anul următor; dacă data curentă este mai mică decât data emiterii compensării, când se ajunge la data emiterii compensării, aceasta va fi compensată și concediul anual va fi reeditat.
att_annualLeave_calculate=Data anuală de compensare și eliberare a concediului
att_annualLeave_workTimeCalculate=Calculați în funcție de raportul timpului de lucru
att_annualLeave_rule=Regula de concediu anual
att_annualLeave_ruleCountOver=A fost atinsă limita maximă a numărului stabilit
att_annualLeave_years=Senior ani
att_annualLeave_eachYear=În fiecare an
att_annualLeave_have=Are
att_annualLeave_days=Zile de concediu anual
att_annualLeave_totalDays=Total concediu anual
att_annualLeave_remainingDays=Concediu anual rămas
att_annualLeave_consecutive=Setarea regulii anuale de concediu trebuie să fie ani consecutivi
# 年假结余表
att_annualLeave_report=Bilanțul anual al concediului
att_annualLeave_validDate=Data validă
att_annualLeave_useDays=Folosiți {0} zile
att_annualLeave_calculateDays=Lansare {0} zile
att_annualLeave_notEnough=Concediu anual insuficient de {0}!
att_annualLeave_notValidDate={0} nu se încadrează în intervalul valid de concediu anual!
att_annualLeave_notDays={0} nu are concediu anual!
att_annualLeave_tip1=Zhang San s-a alăturat pe 1 septembrie anul trecut
att_annualLeave_tip2=Setarea anuală a soldului concediului
att_annualLeave_tip3=Data compensării și emiterii este 1 ianuarie a fiecărui an; se calculează prin rotunjire în funcție de raportul de lucru; dacă durata serviciului este mai mică sau egală cu 1, vor exista 3 zile de concediu anual și, dacă durata serviciului este mai mică sau egală cu 1, există 5 zile de concediu anual
att_annualLeave_tip4=Calcularea concediului anual
att_annualLeave_tip5=Anul trecut 09-01 ~ 12-31 bucură-te de 4 / 12x3 = 1,0 zile
att_annualLeave_tip6=Anul acesta 01-01 ~ 12-31 bucură-te de 4,0 zile (anul acesta 01-01 ~ 08-31 bucură-te de 8 / 12x3 = 2,0 zile + anul acesta 01-01 ~ 12-31 bucură-te de 4 / 12x5≈2,0 zile)
# att SDC
att_sdc_name=Echipament video
att_sdc_wxMsg_firstData=Bună ziua, aveți o notificare de check-in de prezență
att_sdc_wxMsg_stateData=Niciun sentiment de participare la succes
att_sdc_wxMsg_remark=Memento: Rezultatul final al prezenței este supus paginii cu detalii despre check-in.
# 时间段
att_timeSlot_conflict=Intervalul de timp intră în conflict cu alte intervale de timp ale zilei
att_timeSlot_selectFirst=Vă rugăm să selectați intervalul de timp
# 事件中心
att_eventCenter_sign=Conectare la prezență
#异常管理
att_exception_classImportTemplate=Șablon de import de clasă
att_exception_cellClassAdjustType=Câmp obligatoriu, cum ar fi: "{0}", "{1}", "{2}"
att_exception_swapDateDate=câmp neobligator, formatul orei este aaaa-LL-zz, cum ar fi: 07-07-2020
#消息中心
att_message_leave=Notificare de participare {0}
att_message_leaveContent={0} trimis {1}, {2} timpul este {3} ~ {4}
att_message_leaveTime=Lasă timpul
att_message_overtime=Notificare de participare și ore suplimentare
att_message_overtimeContent={0} a trimis ore suplimentare, iar timpul suplimentar este de {1} ~ {2}
att_message_overtimeTime=Timpul de ore suplimentare
att_message_sign=Semnul aviz de participare
att_message_signContent={0} a trimis un semn suplimentar, iar timpul de semnare suplimentar este {1}
att_message_adjust=Notificare privind ajustarea prezenței
att_message_adjustContent={0} a trimis o ajustare, iar data ajustării este {1}
att_message_class=Notificare de prezență și schimbare
att_message_classContent=Conținutul clasei
att_message_classContent0={0} a trimis o schimbare, data schimbării este {1}, iar schimbarea este {2}
att_message_classContent1={0} a trimis o schimbare, data schimbării este {1}, iar data schimbării este {2}
att_message_classContent2={0} ({1}) și {2} ({3}) cursuri de schimb
#推送中心
att_pushCenter_transaction=Înregistrare de prezență
# 时间段
att_timeSlot_workTimeNotEqual=Timpul de lucru nu poate fi egal cu ieșirea din timpul de lucru
att_timeSlot_signTimeNotEqual=Ora de începere a conectării nu poate fi egală cu ora de terminare a deconectării
# 北向接口A
att_api_notNull={0} nu poate fi gol!
att_api_startDateGeEndDate=Ora de început nu poate fi mai mare sau egală cu ora de încheiere!
att_api_leaveTypeNotExist=Specia falsă nu există!
att_api_imageLengthNot2000=Lungimea adresei imaginii nu poate depăşi 2000!
# 20221230新增国际化
att_personSch_workTypeNotNull=Tipul de lucru corespunzător numărului de personal {0} nu poate fi gol!
att_personSch_workTypeNotExist=Tipul de lucru corespunzător numărului de personal {0} nu există!
att_annualLeave_recalculate=Recalculează
# 20230530新增国际化
att_leftMenu_dailyReport=Raport zilnic de prezență
att_leftMenu_overtimeReport=Raportul orelor suplimentare
att_leftMenu_lateReport=Raport întârziat
att_leftMenu_earlyReport=Părăsiți raportul devreme
att_leftMenu_absentReport=Raport de absent
att_leftMenu_monthReport=Raport lunar privind prezența
att_leftMenu_monthWorkTimeReport=Raport lunar privind timpul de lucru
att_leftMenu_monthCardReport=Raport lunar de card
att_leftMenu_monthOvertimeReport=Raport lunar pentru ore suplimentare
att_leftMenu_overtimeSummaryReport=Raport rezumat al personalului
att_leftMenu_deptOvertimeSummaryReport=Raport de rezumat al departamentului de ore suplimentare
att_leftMenu_deptLeaveSummaryReport=Raport de rezumat al concediului de departament
att_annualLeave_calculateDay=Numărul de zile de concediu anual
att_annualLeave_adjustDay=Ajustați zilele
att_annualLeave_sureSelectDept=Sunteţi sigur că doriţi să efectuaţi operaţia {0} pe departamentul selectat?
att_annualLeave_sureSelectPerson=Sunteţi sigur că doriţi să efectuaţi operaţia {0} asupra persoanei selectate?
att_annualLeave_calculateTip1=Când se calculează în funcție de vechimea în muncă: calculul concediului anual este exact lunar, dacă vechimea în muncă este de 10 ani și 3 luni, atunci se vor folosi 10 ani și 3 luni pentru calcul;
att_annualLeave_calculateTip2=Când conversia nu se bazează pe vechimea în muncă: calculul concediului anual este exact pentru anul, dacă vechimea în muncă este de 10 ani și 3 luni, atunci 10 ani vor fi utilizați pentru calcul;
att_rule_isInCompleteTip=Prioritatea este cea mai mare atunci când nicio conectare sau nicio deconectare nu este înregistrată ca incompletă, iar întârzierea, concediul anticipat, absenteismul și valabilitatea
att_rule_absentTip=Atunci când nu este înregistrată nicio conectare sau nicio deconectare ca absenteism, durata absenteismului este egală cu durata orelor de lucru minus durata concediului târziu până la devreme
att_timeSlot_elasticTip1=0, timpul efectiv este egal cu timpul real, fără absenteism
att_timeSlot_elasticTip2=Dacă timpul efectiv este mai mare decât timpul de lucru, timpul efectiv este egal cu timpul de lucru, fără absenteism
att_timeSlot_elasticTip3=Dacă durata efectivă este mai mică decât durata de lucru, durata efectivă este egală cu durata reală, iar absenteismul este egal cu durata de lucru minus durata reală
att_timeSlot_maxWorkingHours=Orele de lucru nu pot fi mai mari decât
# 20231030
att_customReport=Raport personalizat de prezență
att_customReport_byDayDetail=Detalii pe zile
att_customReport_byPerson=Rezumat pe persoană
att_customReport_byDept=Rezumat pe departamente
att_customReport_queryMaxRange=Intervalul maxim de interogare este de patru luni
# 20240630新增国际化
att_personSch_shiftWorkTypeTip1=(1) Atunci când lucrează ore suplimentare în zilele normale de muncă/odihnă, prioritatea programării este mai mică decât cea a sărbătorilor
att_personSch_shiftWorkTypeTip2=(2) Atunci când tipul de muncă este orele suplimentare în timpul vacanțelor, prioritatea programării este mai mare decât cea a vacanțelor
att_personVerifyMode=Metoda de verificare a personalului
att_personVerifyMode_setting=Setările metodei de verificare
att_personSch_importCycSch=Programarea ciclului de import al personalului
att_personSch_cycSchTemplate=Șablon de programare a ciclului personal
att_personSch_exportCycSchTemplate=Descărcați șablonul de programare a ciclului de personal
att_personSch_scheduleTypeNotNull=Tipul Shift nu poate fi gol sau nu există!
att_personSch_shiftNotNull=Schimbul nu poate fi gol!
att_personSch_shiftNotExist=Schimbarea nu există!
att_personSch_onlyAllowOneShift=Programarea regulată permite programarea unei singure ture!
att_shift_attShiftStartDateRemark2=Săptămâna în care se află data de începere a ciclului este prima săptămână; Luna în care se află data de începere a ciclului este prima lună.
#打卡状态
att_cardStatus_setting=Configurarea statusului de prezență
att_cardStatus_name=Nume
att_cardStatus_value=Valoare
att_cardStatus_alias=Pseudobune
att_cardStatus_every_day=Zilueta
att_cardStatus_by_week=Luna pe ziua de sâmbătă
att_cardStatus_autoState=Stare automatizată
att_cardStatus_attState=Stare de prezență
att_cardStatus_signIn=Conectare
att_cardStatus_signOut=Descentare
att_cardStatus_out=extern
att_cardStatus_outReturn=Revenire după externare
att_cardStatus_overtime_signIn=Conectare la muncă additională
att_cardStatus_overtime_signOut=Descentare la muncă additională
# 20241030新增国际化
att_leaveType_enableMaxDays=Activarea limitări anuale
att_leaveType_maxDays=Limit anunaț (zile)
att_leaveType_applyMaxDays=Aplicarea solicitanței anuale nu poate depăși {0} zile
att_param_overTimeSetting=Setarea nivelurilor suprapozelor
att_param_overTimeLevel=Nivelul suprapoză (ore)
att_param_overTimeLevelEnable=Activați calcularea nivelurilor suprapozelor?
att_param_reportColor=Culoarea raportului de prezentare
# APP
att_app_signClientTip=Acest dispozitiv a fost deja check-in de altcineva acum
att_app_noSignAddress=Zona de verificare nedefinita, vă rugăm să contactați administratorul pentru a o seta
att_app_notInSignAddress=Nu ai ajuns în locația de verificare, nu se poate face verificarea
att_app_attendance=Prezenta mea
att_app_apply=Cerere de prezență
att_app_approve=Aprobarea mea
# 20250530
att_node_leaderNodeExist=Nodul de autorizare a liderului direct există deja
att_signAddress_init=Initializare hartă
att_signAddress_initTips=Introdu codul hartei și inițiază-o pentru a alege o adresă