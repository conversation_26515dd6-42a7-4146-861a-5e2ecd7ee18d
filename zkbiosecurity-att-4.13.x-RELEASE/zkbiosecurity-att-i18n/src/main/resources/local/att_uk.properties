#系统名称 乌克兰语
att_systemName=Attendance System 1.0
#=====================================================================
#左侧菜单
att_module=Відвідуваність
#左侧菜单-考勤设备
att_leftMenu_attendanceDevice=Керування пристроями
att_leftMenu_device=Пристрій
att_leftMenu_point=Точки обліку
att_leftMenu_sign_address=Адреса входу з мобільного
att_leftMenu_adms_devCmd=Команда сервера
#左侧菜单-基础信息
att_leftMenu_basicInformation=Основна інформація
att_leftMenu_rule=Правило
att_leftMenu_base_rule=Правила відвідуваності
att_leftMenu_department_rule=Правила по відділу
att_leftMenu_holiday=Свята
att_leftMenu_leaveType=Тип відпустки
att_leftMenu_timingCalculation=Автоматичний звіт
att_leftMenu_autoExport=Повідомлення push
att_leftMenu_param=Налаштування параметрів
#左侧菜单-班次管理
att_leftMenu_shiftManagement=Робоча зміна
att_leftMenu_timeSlot=Розклад
att_leftMenu_shift=Зміна
#左侧菜单-排班管理
att_leftMenu_scheduleManagement=Графік
att_leftMenu_group=Група
att_leftMenu_groupPerson=Груповий графік
att_leftMenu_groupSch=Графік групи
att_leftMenu_deptSch=Графік відділу
att_leftMenu_personSch=Графік співробітника
att_leftMenu_tempSch=Тимчасовий графік
att_leftMenu_nonSch=Співробітники поза графіком
#左侧菜单-异常管理
att_leftMenu_exceptionManagement=Виправдувальні
att_leftMenu_sign=Реєстрація приходу
att_leftMenu_leave=Відпустка
att_leftMenu_trip=Відрядження
att_leftMenu_out=Вихід
att_leftMenu_overtime=Понаднормові
att_leftMenu_adjust=Коригування та додавання
att_leftMenu_class=Коригування зміни
#左侧菜单-统计报表
att_leftMenu_statisticalReport=Генератор звітів
att_leftMenu_manualCalculation=Розрахунок вручну
att_leftMenu_transaction=Події
att_leftMenu_dayCardDetailReport=Вівідування за день
att_leftMenu_leaveSummaryReport=Звіт по відпусткам
att_leftMenu_dayDetailReport=Звіт за день
att_leftMenu_monthDetailReport=Детальній звіт за місяць
att_leftMenu_monthStatisticalReport=Статист. звіт за місяць
att_leftMenu_deptStatisticalReport=Звіт по відділу
att_leftMenu_yearStatisticalReport=Звіт за рік
att_leftMenu_attSignCallRollReport=Звіт про ненормальну роботу
att_leftMenu_workTimeReport=Звіт про робочий час
#左侧菜单-设备操作日志
att_leftMenu_device_op_log=Журнал роботи пристрою
#左侧菜单-实时点名
att_leftMenu_realTime_callRoll=Збір
#=====================================================================
#公共
att_common_person=Пресонал
att_common_pin=ID
att_common_group=Група
att_common_dept=Віділ
att_common_symbol=Символ
att_common_deptNo=Номер відділу
att_common_deptName=Ім'я департмента
att_common_groupNo=Номер групи
att_common_groupName=Ім'я групи
att_common_operateTime=Час роботи
att_common_operationFailed=Помилка операції
att_common_id=ID
att_common_deptId=ID відділу
att_common_groupId=ID групи
att_common_deviceId=ID пристрою
att_person_pin=ID співробітника
att_person_name=Ім'я
att_person_lastName=Прізвище
att_person_internalCard=Номер картки
att_person_attendanceMode=Режим часу та відвідуваності
att_person_normalAttendance=Нормальне відвідування
att_person_noPunchCard=Без картки доступа
att_common_attendance=Відвідування
att_common_attendance_hour=Відвідування (часи)
att_common_attendance_day=Відвідування (дні)
att_common_late=Запізнення
att_common_early=Ранній прихід
att_common_overtime=Наднормові
att_common_exception=Виправдувальні
att_common_absent=Відсутність
att_common_leave=Відпустка
att_common_trip=відрядження
att_common_out=Вихід
att_common_staff=Співробітник
att_common_superadmin=Суперадмін
att_common_msg=SMS 
att_common_min=Час дії короткого повідомлення (хвилини)
att_common_letterNumber=Можна вводити лише цифри або літери!
att_common_relationDataCanNotDel=Зв’язані дані не можна видалити.
att_common_relationDataCanNotEdit=Зв’язані дані не можна змінити.
att_common_needSelectOneArea=Виберіть область!
att_common_neesSelectPerson=Виберіть особу!
att_common_nameNoSpace=Ім'я не може містити пробіли!
att_common_digitsValid=Можна вводити лише цифри, що складають не більше двох десяткових знаків!
att_common_numValid=Можна вводити лише цифри!
#=====================================================================
#工作面板
att_dashboard_worker=Workaholic
att_dashboard_today=Сьогоднішня відвідуваність
att_dashboard_todayCount=Сьогоднішня сегментована статистика відвідуваності
att_dashboard_exceptionCount=Ненормальна статистика (цього місяця)
att_dashboard_lastWeek=Останнього тижня
att_dashboard_lastMonth=Останній місяць
att_dashboard_perpsonNumber=Всього особова
att_dashboard_actualNumber=Фактична особова
att_dashboard_notArrivedNumber=Відсутність особова
att_dashboard_attHour=Робочий час
#区域
#设备
att_op_syncDev=Синхронізувати дані програмного забезпечення з пристроєм
att_op_account=Перевірка даних про відвідуваність
att_op_check=Завантажити дані ще раз
att_op_deleteCmd=Очистити команди пристрою
att_op_dataSms=Публічне повідомлення
att_op_clearAttPic=Очистіть фотографії відвідувачів
att_op_clearAttLog=Очистити події відвідуваності
att_device_waitCmdCount=Команди, які потрібно виконати
att_device_status=Увімкнути стан
att_device_register=Реєстраційна машина
att_device_isRegister=Пристрій для реєстрації
att_device_existNotRegDevice=Незареєстроване машинне обладнання, дані отримати не можна!
att_device_fwVersion=Версія прошивки
att_device_transInterval=Оновити тривалість (хв.)
att_device_cmdCount=Максимальна кількість команд для зв'язку з сервером.
att_device_delay=Запитати час запиту (секунди)
att_device_timeZone=Часовий пояс
att_device_operationLog=Журнал операцій
att_device_registeredFingerprint=Зареєструвати відбиток пальця
att_device_registeredUser=Зареєструвати особу
att_device_fingerprintImage=Зображення відбитків пальців
att_device_editUser=Редагувати персонал
att_device_modifyFingerprint=Змінити відбиток пальця
att_device_faceRegistration=Зареєструвати обличчя 
att_device_userPhotos=Фото співробітника
att_device_attLog=Чи потрібно завантажувати записи відвідуваності
att_device_operLog=Чи потрібно завантажувати особисту інформацію
att_device_attPhoto=Чи потрібно завантажувати фотографії відвідувачів
att_device_isOnLine=Онлайн статус
att_device_InputPin=Введіть номер особи
att_device_getPin=Отримайте вказані дані про персонал
att_device_separatedPin=Кілька штатних номерів, розділені комами
att_device_authDevice=Авторизований пристрій
att_device_disabled=Наступні пристрої відключені і ними не можна керувати!
att_device_autoAdd=Нове обладнання додається автоматично
att_device_receivePersonOnlyDb=Отримання лише даних про персонал, присутній у базі даних
att_devMenu_control=Керування пристроєм
att_devMenu_viewOrGetInfo=Перегляд та отримання інформації
att_devMenu_clearData=Очистити дані пристрою
att_device_disabledOrOffline=Пристрій не ввімкнено або офлайн, ним не можна керувати!
att_device_areaStatus=Стан зони пристрою
att_device_areaCommon=Область нормальна
att_device_areaEmpty=Область порожня
att_device_isRegDev=Зміна часового поясу або стану реєстратора вимагає перезапуску пристрою, щоб набути чинності!
att_device_canUpgrade=Наступні пристрої можна оновити
att_device_offline=Наступні пристрої перебувають у режимі офлайн і ними не можна керувати!
att_device_oldProtocol=Старий протокол
att_device_newProtocol=Новий протокол
att_device_noMoreTwenty=Старий пакет оновлення мікропрограмного забезпечення пристрою не може перевищувати 20 млн
att_device_transferFilesTip=Прошивку виявлено успішно, передайте файли
att_op_clearAttPers=Очистити персонал обладнання
#区域人员
att_op_forZoneAddPers=Налаштування зони персоналу
att_op_dataUserSms=Особисте повідомлення
att_op_syncPers=Повторно синхронізуйте з пристроєм
att_areaPerson_choiceArea=Будь ласка, виберіть область!
att_areaPerson_byAreaPerson=За регіонами
att_areaPerson_setByAreaPerson=Встановлення за персоналом району
att_areaPerson_importBatchDel=Імпортувати масове видалення
att_areaPerson_syncToDevSuccess=Операція вдала! Зачекайте, поки команда буде надіслана.
att_areaPerson_personId=ID сівробітника
att_areaPerson_areaId=ID області
att_area_existPerson=В області є люди!
att_areaPerson_notice1=Площа або персонал не можуть бути порожніми одночасно!
att_areaPerson_notice2=Про людей чи області не запитували!
att_areaPerson_notice3=У цьому районі не знайдено жодного пристрою!
att_areaPerson_addArea=Додати область
att_areaPerson_delArea=Видалити область
att_areaPerson_persNoExit=Особи не існує
att_areaPerson_importTip1=Переконайтесь, що імпортована особа вже існує в модулі персоналу
att_areaPerson_importTip2=Партія імпортованих не буде автоматично доставлена на пристрій, їх потрібно синхронізувати вручну
att_areaPerson_addAreaPerson=Додати область
att_areaPerson_delAreaPerson=Видалити область 
att_areaPerson_importDelAreaPerson=Імпорт та видалення персоналу області
att_areaPerson_importAreaPerson=Імпорт персоналу
#考勤点
att_attPoint_name=Назва точки відвідування
att_attPoint_list=Список точок відвідування
att_attPoint_deviceModule=Пристрій
att_attPoint_acc=Контроль доступу
att_attPoint_park=Паркінг
att_attPoint_ins=FaceKiosk
att_attPoint_pid=Особистий сертифікат
att_attPoint_vms=Відео
att_attPoint_psg=проходу
att_attPoint_doorList=Список дверей
att_attPoint_deviceList=Список пристроїв
att_attPoint_channelList=Список каналів
att_attPoint_gateList=Список воріт
att_attPoint_recordTypeList=Витягнути тип запису
att_attPoint_door=Будь ласка, виберіть відповідні двері.
att_attPoint_device=Будь ласка, виберіть відповідні пристрої.
att_attPoint_gate=Будь ласка, виберіть відповідні ворота
att_attPoint_normalPassRecord=Звичайний запис про проходження
att_attPoint_verificationRecord=Запис перевірки 
att_person_attSet=Налаштування відвідування
att_attPoint_point=Будь ласка, виберіть пункт відвідування.
att_attPoint_count=Недостатня кількість дозволених пунктів відвідуваності; операція не вдалася!
att_attPoint_notSelect=Модуль не налаштовано
att_attPoint_accInsufficientPoints=Невідповідні пункти відвідування для записів контролю доступу!
att_attPoint_parkInsufficientPoints=Невідповідні точки відвідування для записів паркування!
att_attPoint_insInsufficientPoints=Ліцензія недостатня!
att_attPoint_pidInsufficientPoints=Сертифіката персоналу недостатньо для відвідування!
att_attPoint_doorOrParkDeviceName=Назва дверей або назва пристрою паркування
att_attPoint_vmsInsufficientPoints=Відео, коли балів відвідуваності недостатньо!
att_attPoint_psgInsufficientPoints=Балки відвідуваності, коли канал недостатній!
att_attPoint_delDevFail=Не вдалося видалити пристрій, пристрій використовувався!
att_attPoint_pullingRecord=Пункт відвідуваності отримує записи, будь ласка, зачекайте!
att_attPoint_lastTransactionTime=Час отримання останніх даних
att_attPoint_masterDevice=Головний пристрій
att_attPoint_channelName=Назва каналу
att_attPoint_cameraName=Назва камери
att_attPoint_cameraIP=IP-адреса камери
att_attPoint_channelIP=IP каналу
att_attPoint_gateNumber=Номер воріт
att_attPoint_gateName=назва воріт
#APP考勤签到地址
att_signAddress_address=Адреса
att_signAddress_longitude=Довгота
att_signAddress_latitude=Широта
att_signAddress_range=Ефективний діапазон
att_signAddress_rangeUnit=Одиниця вимірювання (м)
#=====================================================================
#规则
att_rule_baseRuleSet=Основне правило
att_rule_countConvertSet=Налаштування розрахунку
att_rule_otherSet=Інші налаштування
att_rule_baseRuleSignIn=Правила обліку приходу
att_rule_baseRuleSignOut=Правила обліку уходу
att_rule_earliestPrinciple=Найбільш рання подія
att_rule_theLatestPrinciple=Найбільш пізня подія
att_rule_principleOfProximity=Правило сусідства
att_rule_baseRuleShortestMinutes=Мінімальний період часу повинен бути більше (мінімум 10 хвилин)
att_rule_baseRuleLongestMinutes=Максимальний період часу повинен бути менше (максимум 1440 хвилин)
att_rule_baseRuleLateAndEarly=Запізнення та ранній ухід вважати відсутністю
att_rule_baseRuleCountOvertime=Статистика наднормової
att_rule_baseRuleFindSchSort=Пошук запису змін
att_rule_groupGreaterThanDepartment=Група->Відділ
att_rule_departmentGreaterThanGroup=Відділ->Група
att_rule_baseRuleSmartFindClass=Правило інтелектуального підрахунку змін
att_rule_timeLongest=Максимальна тривалість роботи
att_rule_exceptionLeast=Менше порушень
att_rule_baseRuleCrossDay=Розрахунок обліку реєстрації при переході зміни на наступну добу
att_rule_firstDay=Перший день
att_rule_secondDay=Другий день
att_rule_baseRuleShortestOvertimeMinutes=Мінімальний час наднормових(хв.)
att_rule_baseRuleMaxOvertimeMinutes=Максимум наднормових(хв.)
att_rule_baseRuleElasticCal=Гнучкий розрахунок тривалості
att_rule_baseRuleTwoPunch=Сукупний час за кожні два прохода
att_rule_baseRuleStartEnd=Calculation of head and tail punch time
att_rule_countConvertHour=Правило перетворення годин
att_rule_formulaHour=Формула: Години = Хвилини / 60
att_rule_countConvertDay=Правило перетворення днів
att_rule_formulaDay=Формула：Дні = Хвилини / Кількість хвилин, які потрібно відпрацювати за день
att_rule_inFormulaShallPrevail=Взяти за стандарт результат, розрахований за формулою;
att_rule_remainderHour=Залишок більше або дорівнює
att_rule_oneHour=Записати як одну годину;
att_rule_halfAnHour=Обчислюється як півгодини, інакше ігнорується;
att_rule_remainderDay=Коефіцієнт більше або дорівнює робочим хвилинам
att_rule_oneDay=%,обчислюється як один день;
att_rule_halfAnDay=%,обчислюється як півдня, інакше ігнорується;
att_rule_countConvertAbsentDay=Правило конвертації в дні відсутності
att_rule_markWorkingDays=Розраховується як робочі дні
att_rule_countConvertDecimal=Точні цифри після коми
att_rule_otherSymbol=Символи реєстрації обліку в звіті
att_rule_arrive=Очікуваний/Дійсний
att_rule_noSignIn=Без відмітки входу
att_rule_noSignOff=Без відмітки виходу
att_rule_off=Вірегулюйте останні
att_rule_class=Додати відвідування
att_rule_shortLessLong=Графік відвідуваності не може бути довшим за графік відвідуваності.
att_rule_symbolsWarning=Ви повинні налаштувати символ у звіті про відвідуваність!
att_rule_reportSettingSet=Налаштуваня експорту звіту
att_rule_shortDateFormat=Формат дати
att_rule_shortTimeFormat=Формат часу
att_rule_baseRuleSignBreakTime=Чи рахувати час перерви?
att_leftMenu_custom_rule=Спеціальне правило
att_custom_rule_already_exist={0} Спеціальні правила вже існують!
att_add_group_custom_rule=Додайте правила групування
att_custom_rule_type=Тип правил
att_rule_type_group=Правила по групах
att_rule_type_dept=Правила по віділам
att_custom_rule_orgNames=Використання об'єкта
att_rult_maxOverTimeType1=Без ліміту
att_rult_maxOverTimeType2=Ця неділя
att_rult_maxOverTimeType3=Цей місяць
att_rule_countConvertDayRemark1=Приклад:Дійсний робочий час становить 500 хвилин, а необхідний робочий час - 480 хвилин на день. Результат - 500/480 = 1,04, останній десятковий знак зберігається на рівні 1,0
att_rule_countConvertDayRemark2=Приклад:Дійсний робочий час становить 500 хвилин, а необхідний робочий час - 480 хвилин на день. Результат - 500/480=1.04, 1.04>0.8.
att_rule_countConvertDayRemark3=Приклад:Дійсний робочий час становить 300 хвилин, а необхідний робочий час - 480 хвилин на день. Результат - 300/480=0.625, 0.2<0.625<0.8 для половини дня.
att_rule_countConvertDayRemark4=Рівень перерахунку дня: у проміжок часу "Рахувати як робочі дні" не працює;
att_rule_countConvertDayRemark5=Записується як кількість робочих днів: обмежена підрахунком закінчення кількості днів, і, в кожному періоді є закінчення, тривалість закінчення розраховується відповідно до кількості робочих днів в періоді;
att_rule_baseRuleSmartFindRemark1=Найтриваліше: відповідно до карти відвідуваності дня, розраховується робочий час, відповідне кожній зміні в день, і знаходить зміну найдовшого робочого часу дня;
att_rule_baseRuleSmartFindRemark2=Мінімум відхилень: розраховує кількість відхилень часу, відповідних кожній зміні в день, відповідно до карти відвідуваності дня і знаходить зміну з найменшою кількістю відхилень в день для розрахунку робочого часу;
att_rule_baseRuleHourValidator=Оцінка півгодинної хвилини не може бути більше обо дорівнювати 1 годині!
att_rule_baseRuleDayValidator=Період оцінки в півдня не може бути більше або дорівнює періоду оцінки в один день!
att_rule_overtimeWarning=Максимальна тривалість СУ роботи не може бути менше мінімальної тривалості СУ роботи!
att_rule_noSignInCountType=Відсутність відмітки входу вважати
att_rule_absent=Вісутність
att_rule_earlyLeave=Ранній ухід
att_rule_noSignOffCountType=Вісутніть відмітки виходу вважати
att_rule_minutes=Хвилини
att_rule_noSignInCountLateMinute=Відсутність відмітки входу зарахувати як хвилини запізнення
att_rule_noSignOffCountEarlyMinute=Відсутність відмітки виходу зарахувати як хвилини ранньго уходу
att_rule_incomplete=Неповна
att_rule_noCheckInIncomplete=Неповна і відсутня відмітка входу
att_rule_noCheckOutIncomplete=Неповна і відсутня відмітка виходу
att_rule_lateMinuteWarning=Зарахування хвилин запізнення за відсутність відмітки входу повинно бути більше 0 і менше найдовшого періоду присутності
att_rule_earlyMinuteWarning=Зарахування хвилин раннього уходу за відсутність відмітки виходу повинно бути більше 0 і менше найдовшого періоду присутності
att_rule_baseRuleNoSignInCountLateMinuteRemark=Якщо відсутність відмітки входу зараховується як запізнення, то при відсутності відмітки буде нараховано запізнення на N хвилин
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=Якщо відсутність відмітки виходу зараховується як ранній ухід, то при відсутності відмітки буде нараховано достроковий вихід на N хвилин
#节假日
att_holiday_placeholderNo=Радимо називати з H, наприклад, H01.
att_holiday_placeholderName=Радимо називати в форматі [Рік] + [Назв. свята] наприклад. [2017 День праці].
att_holiday_dayNumber=Кількість днів
att_holiday_validDate_msg=Відпустка в цей час
#假种
att_leaveType_leaveThing=Позапланова відпустка
att_leaveType_leaveMarriage=Відпустка у зв'язку з вступом у шлюб
att_leaveType_leaveBirth=Відпустка у зв'язку з вагітністю та пологами
att_leaveType_leaveSick=Відпустка через хворобу
att_leaveType_leaveAnnual=Щорічна трудова відпустка
att_leaveType_leaveFuneral=Відпустка у зв'язку зі смертю члена сім'ї
att_leaveType_leaveHome=Отпуск для поездки домой
att_leaveType_leaveNursing=Відпустка по догляду за дитиною
att_leaveType_isDeductWorkLong=Зняти робочі години
att_leaveType_placeholderNo=Рекомендується починати з L, наприклад, L1.
att_leaveType_placeholderName=Рекомендується закінчувати «святом», наприклад, День весілля.
#定时计算
att_timingcalc_timeCalcFrequency=Інтервал обчислення
att_timingcalc_timeCalcInterval=Плановий розрахунок часу
att_timingcalc_timeSet=Встановлення часу планового обчислення
att_timingcalc_timeSelect=Будь ласка, виберіть час
att_timingcalc_optionTip=Принаймні один дійсний щоденний розрахунок відвідуваності повинен бути збережений
#自动导出报表
att_autoExport_reportType=Тип звіту
att_autoExport_fileType=Тип файлу
att_autoExport_fileName=Ім'я файлу
att_autoExport_fileDateFormat=Формат дати
att_autoExport_fileTimeFormat=Формат часу
att_autoExport_fileContentFormat=Формат вмісту
att_autoExport_fileContentFormatTxt=Приклад：{deptName}00{personPin}01{personName}02{attDatetime}03
att_autoExport_timeSendFrequency=Частота надсилання
att_autoExport_timeSendInterval=Інтервал надсилання
att_autoExport_emailType=Тип отримувача
att_autoExport_emailRecipients=Отримувач
att_autoExport_emailAddress=Адреса
att_autoExport_emailExample=Приклад:<EMAIL>,<EMAIL>
att_autoExport_emailSubject=Заголовок листа
att_autoExport_emailContent=Зміст листа
att_autoExport_field=Поле
att_autoExport_fieldName=Назва поля
att_autoExport_fieldCode=Номер поля
att_autoExport_reportSet=Налаштування звіту
att_autoExport_timeSet=Встановлення часу доставки пошти
att_autoExport_emailSet=Налаштування пошти
att_autoExport_emailSetAlert=Введіть свою адресу електронної пошти.
att_autoExport_emailTypeSet=Налаштування приймача
att_autoExport_byDay=По дням
att_autoExport_byMonth=По місяцям
att_autoExport_byPersonSet=Встановлено по працівнику
att_autoExport_byDeptSet=Встановлено по відділу
att_autoExport_byAreaSet=Встановлено по зоні
att_autoExport_emailSubjectSet=Встановлення заголовка
att_autoExport_emailContentSet=Налаштування змісту листа
att_autoExport_timePointAlert=Виберіть правильну точку часу відправлення.
att_autoExport_lastDayofMonth=Останній день місяця
att_autoExport_firstDayofMonth=Перший день місяця
att_autoExport_dayofMonthCheck=Конкретна дата
att_autoExport_dayofMonthCheckAlert=Виберіть конкретну дату.
att_autoExport_chooseDeptAlert=Будь ласка, виберіть відділ!
att_autoExport_sendFormatSet=Налаштування режиму надсилання
att_autoExport_sendFormat=Режим надсилання
att_autoExport_mailFormat=Метод доставки
att_autoExport_ftpFormat=Метод надсилання FTP
att_autoExport_sftpFormat=Метод надсилання SFTP
att_autoExport_ftpUrl=Адреса сервера FTP
att_autoExport_ftpPort=Порт FTP-сервера
att_autoExport_ftpTimeSet=Установка часу надсилання FTP
att_autoExport_ftpParamSet=Налаштування параметрів FTP
att_autoExport_ftpUsername=Ім'я користувача FTP
att_autoExport_ftpPassword=Пароль FTP
att_autoExport_correctFtpParam=Будь ласка, правильно заповніть параметри ftp
att_autoExport_correctFtpTestParam=Перевірте з'єднання, щоб переконатися, що зв'язок нормальний
att_autoExport_inputFtpUrl=Введіть адресу сервера
att_autoExport_inputFtpPort=Введіть порт сервера
att_autoExport_ftpSuccess=З’єднання вдало
att_autoExport_ftpFail=Перевірте, чи правильні налаштування параметра.
att_autoExport_validFtp=Введіть дійсну адресу сервера
att_autoExport_validPort=Введіть дійсний порт сервера
att_autoExport_selectExcelTip=Тип файлу виберіть EXCEL, формат вмісту - усі поля!
#=====================================================================
#时间段
att_timeSlot_periodType=Тип розкладу
att_timeSlot_normalTime=Звичайний розклад
att_timeSlot_elasticTime=Гнучкий графік
att_timeSlot_startSignInTime=Час початку обліку приходу
att_timeSlot_toWorkTime=Час приходу
att_timeSlot_endSignInTime=Час кінця обліку приходу
att_timeSlot_allowLateMinutes=Дозволити запізнення(хвилин)
att_timeSlot_isMustSignIn=Необходно відмічати прихід
att_timeSlot_startSignOffTime=Час початку обліку уходу
att_timeSlot_offWorkTime=Час уходу
att_timeSlot_endSignOffTime=Час кінця обліку уходу
att_timeSlot_allowEarlyMinutes=Дозволити достроковий уход(хвилин)
att_timeSlot_isMustSignOff=Необхідно відмічати уход
att_timeSlot_workingHours=Час роботи (хвилин)
att_timeSlot_isSegmentDeduction=Автовідрахування часу перерви
att_timeSlot_startSegmentTime=Час початку
att_timeSlot_endSegmentTime=Час кінця
att_timeSlot_interSegmentDeduction=Відрахуваний час(хвилин)
att_timeSlot_markWorkingDays=Робочий день
att_timeSlot_isAdvanceCountOvertime=Автоматичні наднормові(Ранній прихід)
att_timeSlot_signInAdvanceTime=Час закінчення автоматичних наднормових(Прихід)
att_timeSlot_isPostponeCountOvertime=Автоматичні наднормові(Затримка виходу)
att_timeSlot_signOutPosponeTime=Час закінчення автоматичних наднормових(Вихід)
att_timeSlot_isCountOvertime=Зараховано як наднормові
att_timeSlot_timeSlotLong=Робочий час повинен відповідати інтервалу відвідування, визначеному правилами:
att_timeSlot_alertStartSignInTime=Час початку реєстрації прихода повинен бути меншим за час реєстрації прихода.
att_timeSlot_alertEndSignInTime=Час закінчення реєстрації уходу повинен бути більше часу реєстрації прихода.
att_timeSlot_alertStartSignInAndEndSignIn=Початок реєстрації уходу повинен бути меншим за час уходу.
att_timeSlot_alertStartSignOffTime=Час початку понаднормових не може бути меншим за час уходу.
att_timeSlot_alertEndSignOffTime=Час початку понаднормових не може перевищувати час закінчення реєстрації уходу.
att_timeSlot_alertStartUnequalEnd=Робочі дні не можуть бути менше 0.
att_timeSlot_alertStartSegmentTime=Час, що вираховується, не може бути менше 0.
att_timeSlot_alertStartAndEndTime=Час початку реєстрації уходу не може дорівнювати часу закінчення реєстрації приходу.
att_timeSlot_alertEndAndoffWorkTime=Години не можуть перевищувати 23.
att_timeSlot_alertToWorkLesserAndEndTimeLesser=Хвилини не можуть перевищувати 59.
att_timeSlot_alertLessSignInAdvanceTime=Час раннього приходу повинен бути меншим за час початку роботи
att_timeSlot_alertMoreSignInAdvanceTime=Кількість хвилин надурочних робіт перед "виходом на роботу" менше, ніж кількість хвилин перед "роботою"
att_timeSlot_alertMoreSignOutPosponeTime=Приписки понаднормових "поза роботою" менше, ніж хвилини "після роботи"
att_timeSlot_alertLessSignOutPosponeTime=Час пізньої реєстрації виходу повинен бути більшим, ніж час закінчення роботи
att_timeSlot_time=Будь ласка, введіть правильний формат часу.
att_timeSlot_alertMarkWorkingDays=Робочий день не може бути порожнім!
att_timeSlot_placeholderNo=Рекомендується починати з Т, наприклад Т01.
att_timeSlot_placeholderName=Рекомендується починати з Т або закінчувати з timetable.
att_timeSlot_beforeToWork=Перед виходом на роботу
att_timeSlot_afterToWork=Після роботи
att_timeSlot_beforeOffWork=Перед відходом від служби
att_timeSlot_afterOffWork=Після роботи
att_timeSlot_minutesSignInValid=Реєстрація входу дійсна протягом декількох хвилин
att_timeSlot_toWork=На роботі
att_timeSlot_offWork=Не на роботі
att_timeSlot_minutesSignInAsOvertime=Передчасніий вхід рахувати як нанормові
att_timeSlot_minutesSignOutAsOvertime=Почати рахувати наднормові 
att_timeSlot_minOvertimeMinutes=Мінімум понаднормових хвилин
att_timeSlot_enableWorkingHours=Чи включати робочий час
att_timeSlot_eidtTimeSlot=Редагувати час
att_timeSlot_browseBreakTime=Перегляньте періоди відпочинку
att_timeSlot_addBreakTime=Додати перерви
att_timeSlot_enableFlexibleWork=Гнучка робота
att_timeSlot_advanceWorkMinutes=Можна прийти заздалегідь
att_timeSlot_delayedWorkMinutes=Затримання на роботі
att_timeSlot_advanceWorkMinutesValidMsg1=Кідькість хвилин до роботи вище ніж кількість хвилин, на які можна прибути заздалегідь
att_timeSlot_advanceWorkMinutesValidMsg2=Кількість хвилин, на які можна прибути заздалегідь нижча, ніж кількість хвилин до роботи
att_timeSlot_advanceWorkMinutesValidMsg3=Кількість відпрацьованих годин менша або дорівнює кількості хвилин до вступу в понаднормову роботу.
att_timeSlot_advanceWorkMinutesValidMsg4=Кількість хвилин, яка може залишитися до входу в понаднормову роботу, більша або дорівнює кількості відпрацьованих хвилин.
att_timeSlot_delayedWorkMinutesValidMsg1=Кількість хвилин після закінчення роботи перевищує кількість хвилин, на які можна затриматись
att_timeSlot_delayedWorkMinutesValidMsg2=Кількість хвилин, на які можно затриматись, менша за кількість хвилин після закінчення роботи
att_timeSlot_delayedWorkMinutesValidMsg3=Кількість хвилин, на які можно затриматись, менше або дорівнює кількості хвилин після закінчення звичайної роботи, та початку роботи понаднормово
att_timeSlot_delayedWorkMinutesValidMsg4=Кількість хвилин, які можна працювати після закінчення звичайної роботи, і початку наднормових, вище або дорівнює кількості хвилин після запланованої роботи
att_timeSlot_allowLateMinutesValidMsg1=Кількість хвилин, на які дозволяється запізнюватися, менша за кількість хвилин після роботи
att_timeSlot_allowLateMinutesValidMsg2=Кількість хвилин після закінчення роботи перевищує кількість хвилин, дозволених на запізнення
att_timeSlot_allowEarlyMinutesValidMsg1=Дозволити ранніх хвлин менше, ніж хвилин до роботи
att_timeSlot_allowEarlyMinutesValidMsg2=Кількість хвилин до роботи вище, ніж кількість хвилин , на які дозволяється піти раніше
att_timeSlot_timeOverlap=Час відпочинку перекривається, будь ласка, змініть період відпочинку!
att_timeSlot_atLeastOne=Принаймні 1 період відпочинку!
att_timeSlot_mostThree=До 3 періодів відпочинку!
att_timeSlot_canNotEqual=Час початку періоду відпочинку не може бути рівним часу закінчення!
att_timeSlot_shoudInWorkTime=Будь ласка, переконайтесь, що період відпочинку в межах робочого часу!
att_timeSlot_repeatBreakTime=Повторіть період відпочинку!
att_timeSlot_toWorkLe=Час роботи менше мінімального часу початку вибраного періоду відпочинку:
att_timeSlot_offWorkGe=Час поза робочим часом перевищує максимальний час закінчення вибраного періоду відпочинку:
att_timeSlot_crossDays_toWork=Мінімальний час початку періоду перерви в межах періоду часу:
att_timeSlot_crossDays_offWork=Максимальний час закінчення періоду відпочинку знаходиться в межах періоду часу:
att_timeSlot_allowLateMinutesRemark=Від робочого часу до дозволеної картки пізніх хвилин для розрахунку нормальної робочої картки
att_timeSlot_allowEarlyMinutesRemark=Починаючи з раннього часу поза робочим часом, у кількості хвилин, на які дозволено піти достроково, звичайна картка поза робочим часом
att_timeSlot_isSegmentDeductionRemark=Видалення періоду відпочинку в періоді часу
att_timeSlot_attEnableFlexibleWorkRemark1=Гнучка робота не дозволяє встановлювати кількість пізніх, дочасних виїздів
att_timeSlot_afterToWorkRemark=Час після роботи дорівнює відкладеним хвилинам
att_timeSlot_beforeOffWorkRemark=Час до роботи дорівнює часу можливого початку
att_timeSlot_attEnableFlexibleWorkRemark2=Кількість хвилин після закінчення робочого часу більше або дорівнює неробочому часу + затримка робочого часу
att_timeSlot_attEnableFlexibleWorkRemark3=Ви можете працювати заздалегідь, має бути менше або дорівнює робочим N хвилинам для працювання понаднормово
att_timeSlot_attEnableFlexibleWorkRemark4=Відкладені на робочі хвилини повинні бути менше або дорівнювати N хвилинам без роботи
att_timeSlot_attBeforeToWorkAsOvertimeRemark=Початок з 9:00, зареєструйтесь за час до роботи, щоб почати працювати надномово, і увійдіть до восьмої години, для наднормових о 8:00
att_timeSlot_attAfterOffWorkAsOvertimeRemark=Приклад: Після 18 години, після 60 хвилин роботи, підпишіть відмову і понаднормово працюйте, а потім почніть понаднормово з 19 години до часу виїзду.
att_timeSlot_longTimeValidRemark=Час підписання після закінчення роботи та фактичний час підписання до початку роботи не можуть збігатися в періоді часу!
att_timeSlot_advanceWorkMinutesValidMsg5=Кількість дійсних хвилин до реєстрації більше, ніж кількість хвилин, які можна відпрацювати заздалегідь
att_timeSlot_advanceWorkMinutesValidMsg6=Хвилини до роботи заздалегідь повинні бути менше дійсних хвилин для входу до роботи
att_timeSlot_delayedWorkMinutesValidMsg5=Кількість хвилин після реєстрації перевищує кількість хвилин, які можна перенести
att_timeSlot_delayedWorkMinutesValidMsg6=Кількість хвилин, які можна відкласти, повинна бути менше хвилин після входу
att_timeSlot_advanceWorkMinutesValidMsg7=Час входу перед роботою не може збігатися з часом виходу після виходу з роботи напередодні
att_timeSlot_delayedWorkMinutesValidMsg7=Час виходу після роботи не може збігатися з часом входу до роботи наступного дня
att_timeSlot_maxOvertimeMinutes=Обмеження максимума надурочних годин
#班次
att_shift_basicSet=Тип розкладу
att_shift_advancedSet=Назва розкладу
att_shift_type=Тип зміни
att_shift_name=Назва зміни
att_shift_regularShift=Звичайна зміна
att_shift_flexibleShift=Гнучка зміна
att_shift_color=Колір
att_shift_periodicUnit=Номер
att_shift_periodNumber=Цикл
att_shift_startDate=Дата початку
att_shift_startDate_firstDay=Дата початку цикла
att_shift_isShiftWithinMonth=Цикл зміни за один місяць
att_shift_attendanceMode=Режим реєстрації обліку
att_shift_shiftNormal=Реєстрація обліку відповідно до звичайної зміни
att_shift_oneDayOneCard=Реєстрація обліку один раз на день в будь-який час
att_shift_onlyBrushTime=Рахувати тільки час реєстрації
att_shift_notBrushCard=Вільна реєстрація
att_shift_overtimeMode=Режим наднормових
att_shift_autoCalc=Автоматичний комп'ютерний розрахунок
att_shift_mustApply=Наднормові потрібно підтверджувати
att_shift_mustOvertime=Необхідно працювати понаднормово або приймати відсутність
att_shift_timeSmaller=Найменший час між автоматичним розрахунком та отриманням понаднормового листа
att_shift_notOvertime=Не рахувати як понаднормові
att_shift_overtimeSign=Тип понаднормових
att_shift_normal=Звичайний день
att_shift_restday=День відпочинку
att_shift_timeSlotDetail=Подробиці розкладу
att_shift_doubleDeleteTimeSlot=Двічі клацніть період зміни; Ви можете видалити період часу
att_shift_addTimeSlot=Додати розклад
att_shift_cleanTimeSlot=Очистити розклад
att_shift_NO=Ні.
att_shift_notAll=Скасувати вибір усіх
att_shift_notTime=Якщо прапорець щодо деталей розкладу неможливо встановити, це вказує на те, що розклад руху перекривається.
att_shift_notExistTime=Цього розкладу не існує.
att_shift_cleanAllTimeSlot=Ви впевнені, що хочете очистити розклад для обраної зміни?
att_shift_pleaseCheckBox=Установіть прапорець на лівій стороні, який збігається з поточним часом відображення на правій стороні.
att_shift_pleaseUnit=Будь ласка, заповніть одиниці циклу та кількість циклів.
att_shift_pleaseAllDetailTimeSlot=Виберіть деталі розкладу.
att_shift_placeholderNo=Рекомендується починати з S, наприклад S0.
att_shift_placeholderName=Рекомендується починати з S або закінчувати зміною.
att_shift_workType=Тип роботи
att_shift_normalWork=Звичайна робота
att_shift_holidayOt=Наднормові у свято
att_shift_attShiftStartDateRemark=Приклад: Датою початку циклу є № 22, з періодом три дні, потім № 22/23/24 - для класу A/B/C та № 19/20/21 - для класу A. /B класу / C класу, до і після дати і так далі.
att_shift_isShiftWithinMonthRemark1=Робоча зміна протягом місяця, цикл лише переходить до останнього дня кожного місяця, не послідовно планується протягом місяця;
att_shift_isShiftWithinMonthRemark2=Немісячна робоча зміна, циклується до останнього дня кожного місяця, якщо один цикл не закінчився, продовжує до наступного місяця тощо;
att_shift_workTypeRemark1=Примітка: Якщо тип роботи обраний понаднормово в день відпочинку, відвідуваність не обчислюється в день відпустки.
att_shift_workTypeRemark2=У вихідні дні, понаднормовий час за замовчуванням відзначається днем відпочинку, і комп’ютер автоматично обчислює надурочний час. Заявка на понаднормові роботи не потрібна. Робочий час дня фіксується як понаднормовий час, а відвідуваність не враховується під час свят.
att_shift_workTypeRemark3=Понаднормові під час свят за замовчуванням є вихідним, і комп’ютер автоматично обчислює надурочні роботи, додаткова заявка не потрібна, і робочий час дня фіксується як понаднормовий час;
att_shift_attendanceModeRemark1=За винятком звичайного проведення зміни, не вважається ранньою або відстроченою понаднормовою роботою, наприклад:
att_shift_attendanceModeRemark2=1.Тип роботи: Понаднормові роботи у вихідні дні, режим відвідування:При проведенні карткою, зміна часу дня вважається тривалістю понаднормових;
att_shift_attendanceModeRemark3=2.Тип роботи: звичайна робота, режим відвідування:При проведенні карткою, денна зміна вважається робочим часом;
att_shift_periodStartMode=Тип запуску періоду
att_shift_periodStartModeByPeriod=Дата початку періоду
att_shift_periodStartModeBySch=Дата початку зміни
att_shift_addTimeSlotToShift=Чи потрібно додавати часовий інтервал зміни
#=====================================================================
#分组
att_group_editGroup=Редагувати персонал для групи
att_group_browseGroupPerson=Перегляньте персонал групи
att_group_list=Список груп
att_group_placeholderNo=Рекомендується починати з G, наприклад G1
att_group_placeholderName=Рекомендується починати з G або закінчувати групою.
att_widget_deptHint=Примітка: Імпортуйте весь персонал у вибраний відділ
att_widget_searchType=Умовний запит
att_widget_noPerson=Нікого не вибирано
#分组排班
#部门排班
att_deptSch_existsDept=У відділі діє відомча зміна, і видаляти не дозволяється.
#人员排班
att_personSch_view=Переглянути персональний розклад
#临时排班
att_schedule_type=Тип розкладу
att_schedule_tempType=Тимчасовий тип
att_schedule_normal=Звичайний графік
att_schedule_intelligent=Інтелектуальний пошук класу
att_tempSch_scheduleType=Тип планування
att_tempSch_startDate=Дата початку
att_tempSch_endDate=Дата кінця
att_tempSch_attendanceMode=Режим відвудування
att_tempSch_overtimeMode=Режим наднормових
att_tempSch_overtimeRemark=Відмітка наднормових
att_tempSch_existsDept=У відділі діє відомча тимчасова зміна, і видаляти не дозволяється.
att_schedult_opAddTempSch=Нова тимчасова зміна
att_schedule_cleanEndDate=Порожній час закінчення
att_schedule_selectOne=Для звичайного графіка можна вибрати лише одну зміну!
att_schedule_selectPerson=Будь ласка, спочатку виберіть співробітника!
att_schedule_selectDept=Будь ласка, спочату виберіть відділ!
att_schedule_selectGroup=Будь ласка, спочатку видеріть групу!
att_schedule_selectOneGroup=Можна вибрати лише одну групу!
att_schedule_arrange=Будь ласка, оберіть зміну!
att_schedule_leave=Відпустка
att_schedule_trip=Подорож
att_schedule_out=Із
att_schedule_off=Відпочинок
att_schedule_makeUpClass=Додати
att_schedule_class=Налаштувати
att_schedule_holiday=Свято
att_schedule_offDetail=Відрегулювати відпочинок
att_schedule_makeUpClassDetail=Додати відвідуваність
att_schedule_classDetail=Відрегулювати зміну
att_schedule_holidayDetail=Свято
att_schedule_noSchDetail=Не заплпновано
att_schedule_normalDetail=Нормальний
att_schedule_normalSchInfo=Центр зміни: Без зміни між днями
att_schedule_multipleInterSchInfo=Робоча зміна, відокремлена комами: кілька змін у різні дні
att_schedule_inderSchFirstDayInfo=Робоча зміна по всій сітці в зворотному напрямку: зміна між днями реєструється як перший день
att_schedule_inderSchSecondDayInfo=Зсув через зміщення вперед по сітці: Зсув у різні дні фіксується як другий день
att_schedule_timeConflict=Конфлікт із існуючим періодом зміни, не дозволяється зберігати!
#=====================================================================
att_excp_notExisetPerson=Особи не існує!
att_excp_leavePerson=Звільнення персоналу!
#补签单
att_sign_signTime=Час проходу
att_sign_signDate=Дата проходу
#请假
att_leave_arilName=Тип відпустки
att_leave_image=Залишити фото заявки
att_leave_imageShow=Без фото
att_leave_imageType=Порада: Формат зображення неправильний, підтримуються тільки формати JPEG, GIF, PNG!
att_leave_imageSize=Порада: Вибране зображення завелике, максимальний розмір зображення - 4 Мб!
att_leave_leaveLongDay=Тривалість (дні)
att_leave_leaveLongHour=Тривалість (годин)
att_leave_leaveLongMinute=Тривалість (хв.)
att_leave_endNoLessAndEqualStart=Час закінчення не може бути меншим або рівним часу початку
att_leave_typeNameNoExsists=Підроблена назва класу не існує
att_leave_startNotNull=Час початку не може бути порожнім
att_leave_endNotNull=Час кінця не може бути порожнім
att_leave_typeNameConflict=Ім'я типу конфліктує з ім'ям статусу відвідуваності
#出差
att_trip_tripLongDay=Тривалість поїздки (днів)
att_trip_tripLongMinute=Тривалість поїздки (хв.)
att_trip_tripLongHour=Час у дорозі (години)
#外出
att_out_outLongDay=Тривалість виходу (день)
att_out_outLongMinute=Тривалість виходу (хвилин)
att_out_outLongHour=Витрачений час (час)
#加班
att_overtime_type=Тип наднормових
att_overtime_normal=Звичайні наднормові
att_overtime_rest=Наднормові вихідного дня
att_overtime_overtimeLong=Тривалість надурочного часу (хв.)
att_overtime_overtimeHour=Надурочні години (години)
att_overtime_notice=Подання заявок на наднормову роботу більше, ніж на один день, не допускається
att_overtime_minutesNotice=Подання заявок на наднормову роботу менше, ніж мінімальний час наднормової роботи, не допускається
#调休补班
att_adjust_type=Налаштування типу
att_adjust_adjustDate=Налаштування дати
att_adjust_shiftName=Додати зміну
att_adjust_selectClass=Виберіть назву зміни, яка потребує додаткової відвідуваності.
att_shift_notExistShiftWorkDate={1} планування зміни на {0} та відпочинок не дозволяється застосовувати для редагування зміни!
att_adjust_shiftPeriodStartMode=Зміна, обрана для редагування, якщо датою початку є зміна, типовим значенням є 0-а
att_adjust_shiftNameNoNull=Поля складання розкладу не можуть бути порожніми
att_adjust_shiftNameNoExsist=Складання розкладу не існує
#调班
att_class_type=Налаштування типу
att_class_sameTimeMoveShift=Налаштувати особисту зміну в той же день
att_class_differenceTimeMoveShift=Налаштувати особисту зміну в інші дні
att_class_twoPeopleMove=Обмін між двома особами
att_class_moveDate=Налаштування дати
att_class_shiftName=Оригінальна назва розкладу
att_class_moveShiftName=Нова відрегульована зміна не може бути порожньою.
att_class_movePersonPin=Налаштувати ідентифікатор співробітника
att_class_movePersonName=Налаштувати ім'я співробітника
att_class_movePersonLastName=Налаштувати прізвище співробітника
att_class_moveDeptName=Встановити назву відділа
att_class_personPin=Особисте ID
att_class_shiftNameNoNull=Нова відрегульована зміна не може бути порожньою.
att_class_personPinNoNull=Особистий ідентифікатор нової особи не може бути порожнім!
att_class_isNotExisetSwapPersonPin=Нова налаштована особа не існує, будь ласка, додайте знову!
att_class_personNoSame=Ви не можете налаштувати для тієї самої людини, будь ласка, спробуйте ще раз.
att_class_outTime=Дата коригування та дата переказу не можуть бути більше одного місяця!
att_class_shiftNameNoExsist=Налаштування робочої зміни не існує
att_class_swapPersonNoExisist=The matchmaker does not exist
att_class_dateNoSame=Особи переносяться на різні дати, дати не можуть бути однаковими
#=====================================================================
#节点
att_node_name=Вузол
att_node_type=Тип вузла
att_node_leader=Прямий керівник
att_node_leaderNode=Вузол прямого керівника
att_node_person=Призначена особа
att_node_position=Призначити посаду
att_node_choose=Вибрати позицію
att_node_personNoNull=Персонал не може бути порожнім!
att_node_posiitonNoNull=Позиція не може бути порожньою
att_node_placeholderNo=Рекомендується починати з N, наприклад N01.
att_node_placeholderName=Рекомендується починати з посади або імені, закінчуючи вузлом, наприклад, Вузол менеджера.
att_node_searchPerson=Введіть критерії пошуку
att_node_positionIsExist=Позиція вже існує у даних вузла, будь ласка, виберіть позицію ще раз.
#流程
att_flow_type=Тип потоку
att_flow_rule=Правило потоку
att_flow_rule0=Менше або дорівнює 1 добі
att_flow_rule1=Більше 1 дня і менше або дорівнює 3 дням
att_flow_rule2=Більше 3 днів і менше або дорівнює 7 дням
att_flow_rule3=Більше 7 днів
att_flow_node=Вузол затвердження
att_flow_start=Почати потік
att_flow_end=Кінець потоку
att_flow_addNode=Додати вузол
att_flow_placeholderNo=Рекомендується починати з F, наприклад F01.
att_flow_placeholderName=Рекомендується починати з типу, закінчувати потоком, наприклад, залишити потік.
att_flow_tips=Примітка: Порядок затвердження вузлів знаходиться зверху вниз, і ви можете перетягнути сортування після вибору.
#申请
att_apply_personPin=Ідентифікатор заявки персонала
att_apply_type=Тип винятку
att_apply_flowStatus=Загальний стан потоку
att_apply_start=Ініціювання програми
att_apply_flowing=В очікуванні
att_apply_pass=Пройшов
att_apply_over=Кінець
att_apply_refuse=Відхилено
att_apply_revoke=Відкликати
att_apply_except=Виняток
att_apply_view=Докладніше
att_apply_leaveTips=Особа має запит на відпустку протягом цього періоду часу!
att_apply_tripTips=Протягом цього періоду персонал має заявку на відрядження!
att_apply_outTips=Протягом цього періоду особа подала заявку на вихід!
att_apply_overtimeTips=Протягом цього періоду персонал має заявки на понаднормову роботу!
att_apply_adjustTips=Протягом цього періоду персонал може подати заявку на репетицію!
att_apply_classTips=Протягом цього періоду персонал має заявку на зміну!
#审批
att_approve_wait=Очікує підтвердження
att_approve_refuse=Не пройдено
att_approve_reason=Причина
att_approve_personPin=Ідентифікатор затверджувача
att_approve_personName=Ім’я затверджувача
att_approve_person=Затверджувач
att_approve_isPass=Затверджувати?
att_approve_status=Поточний стан вузла
att_approve_tips=Точка часу вже існує в потоці і не може бути повторена.
att_approve_tips2=Вузол потоку не налаштований, зверніться до адміністратора для налаштування.
att_approve_offDayConflicts={0} не заплановано або заплановано на {1}, а решта не дозволена.
att_approve_shiftConflicts={0} вже має зміну в {1} і не дозволяє подавати заявки на зміни робочого дня!
att_approve_shiftNoSch={0} Жодна заявка не допускається, коли зміна не запланована {1}!
att_approve_classConflicts=No shift application is allowed on the unscheduled date
att_approve_selectTime=Вибір часу визначатиме процес згідно з правилами
att_approve_withoutPermissionApproval=Є робочий процес без дозволу на затвердження, будь ласка, перевірте!
#=====================================================================
#考勤计算
att_op_calculation=Розрахунок відвідуваності
att_op_calculation_notice=Дані про відвідуваність були розраховані у фоновому режимі, повторіть спробу пізніше!
att_op_calculation_leave=У тому числі звільнений персонал
att_statistical_choosePersonOrDept=Будь ласка, виберіть Відділ або Персонал!
att_statistical_sureCalculation=Ви впевнені, що хочете провести розрахунки відвідуваності?
att_statistical_filter=Умова фільтрації готова!
att_statistical_initData=Ініціалізація бази даних завершена!
att_statistical_exception=Ініціалізація даних винятків завершена!
att_statistical_error=Не вдалося розрахувати відсутність!
att_statistical_begin=почалося обчислення!
att_statistical_end=Кінець обчислення!
att_statistical_noticeTime=Час опціонального відвідування: перші два місяці!
att_statistical_remarkHoliday=H
att_statistical_remarkClass=C
att_statistical_remarkNoSch=NS
att_statistical_remarkRest=R
#原始记录表
att_op_importAccRecord=Імпортувати запис контролю доступу
att_op_importParkRecord=Імпорт паркувальних записів
att_op_importInsRecord=Імпортувати записи faceKiosk
att_op_importPidRecord=Імпортуйте записи персональних сертифікатів
att_op_importVmsRecord=Імпортувати відеозапис
att_op_importUSBRecord=Імпортувати запис диска U
att_transaction_noAccModule=Немає модуля контролю доступу!
att_transaction_noParkModule=Немає модуля паркування!
att_transaction_noInsModule=Немає модуля faceKiosk!
att_transaction_noPidModule=Немає модуля особистих сертифікатів!
att_transaction_exportRecord=Експортувати оригінальні записи
att_transaction_exportAttPhoto=Експортувати фотографії відвідувачів
att_transaction_fileIsTooLarge=Експортований файл завеликий. Будь ласка, звужте діапазон дат
att_transaction_exportDate=Дата експорту
att_statistical_attDatetime=Час відвідуваності
att_statistical_attPhoto=Фото відвідувачів
att_statistical_attDetail=Подробиці відвідуваності
att_statistical_acc=Пристрій контролю доступу
att_statistical_att=Пристрій відвідування робочого часу
att_statistical_park=Камера LPR
att_statistical_faceRecognition=Пристрій розпізнавання обличчя
att_statistical_app=Мобільні пристрої
att_statistical_vms=Відеопристрої
att_statistical_psg=Пристрій каналу
att_statistical_dataSources=Джерела даних
att_transaction_SyncRecord=Синхронізуйте записи відвідуваності
#日打卡详情表
att_statistical_dayCardDetail=Деталі входу
att_statistical_cardDate=Дата запису
att_statistical_cardNumber=Запис часу
att_statistical_earliestTime=Найраніший час
att_statistical_latestTime=Пізніший час
att_statistical_cardTime=Час проходу
#请假汇总表
att_statistical_leaveDetail=Деталі відпустки
#日明细报表
att_statistical_attDate=Дата відвідування
att_statistical_week=Тиждень
att_statistical_shiftInfo=Інформація зміни
att_statistical_shiftTimeData=Час початку/кінця роботи
att_statistical_cardValidData=Час проходу
att_statistical_cardValidCount=Підрахунок кількості проходів
att_statistical_lateCount=Кількість запізнень
att_statistical_lateMinute=Кількість хвилин запізнень
att_statistical_earlyCount=Кількість ранніх уходів
att_statistical_earlyMinute=Кількість хвилин ранніх уходів
att_statistical_countData=Дані часу
att_statistical_minuteData=Дані по хвилинах
att_statistical_attendance_minute=Відвідуваність (хвилини)
att_statistical_overtime_minute=Понаднормовий час (хвилини)
att_statistical_unusual_minute=Ненормально(хвилини)
#月明细报表
att_monthdetail_should_hour=Необхідний (час)
att_monthdetail_actual_hour=Фактичний (час)
att_monthdetail_valid_hour=Дійсний (час)
att_monthdetail_absent_hour=Відсутність (час)
att_monthdetail_leave_hour=Віпустка (час)
att_monthdetail_trip_hour=Відрядження (По годинам)
att_monthdetail_out_hour=Вихід(По годинам)
att_monthdetail_should_day=Необхідний (дні)
att_monthdetail_actual_day=Фактичний (дні)
att_monthdetail_valid_day=Дійсний (дні)
att_monthdetail_absent_day=Відсутність (дні)
att_monthdetail_leave_day=Віпустка (дні)
att_monthdetail_trip_day=Відрядження (дні)
att_monthdetail_out_day=Вихід(дні)
#月统计报表
att_statistical_late_minute=Тривалість (хвилин)
att_statistical_early_minute=Тривалість (хвилин)
#部门统计报表
#年度统计报表
att_statistical_should=Необхідний
att_statistical_actual=Фактичний
att_statistical_valid=Дійсний
att_statistical_numberOfTimes=Кількість разів
att_statistical_usually=робочий день
att_statistical_rest=Робочий день
att_statistical_holiday=Свято
att_statistical_total=Вього
att_statistical_month=Статистика місяця
att_statistical_year=Статистика за рік
att_statistical_attendance_hour=Відвідуваність (години)
att_statistical_attendance_day=Відвідуваність (день)
att_statistical_overtime_hour=Надурочний час (години)
att_statistical_unusual_hour=Виняток (години)
att_statistical_unusual_day=Ненормально (дні)
#考勤设备参数
att_deviceOption_query=Переглянути параметри пристрою
att_deviceOption_noOption=Немає інформації про параметри, будь ласка, спочатку отримайте параметри пристрою
att_deviceOption_name=Назва параметра
att_deviceOption_value=Значення параметра
att_deviceOption_UserCount=Номер користувача
att_deviceOption_MaxUserCount=Максимальна кількість користувачів
att_deviceOption_FaceCount=Кількість поточних шаблонів для обличчя
att_deviceOption_MaxFaceCount=Максимальна кількість шаблонів для обличчя
att_deviceOption_FacePhotoCount=Поточна кількість зображень обличчя
att_deviceOption_MaxFacePhotoCount=Максимальна кількість зображень обличчя
att_deviceOption_FingerCount=Кількість поточних шаблонів відбитків пальців
att_deviceOption_MaxFingerCount=Максимальна кількість шаблонів відбитків пальців
att_deviceOption_FingerPhotoCount=Кількість поточних зображень відбитків пальців
att_deviceOption_MaxFingerPhotoCount=Максимальна кількість зображень з відбитками пальців
att_deviceOption_FvCount=Кількість поточних шаблонів вен пальців
att_deviceOption_MaxFvCount=Максимальна кількість шаблонів вен пальців
att_deviceOption_FvPhotoCount=Кількість поточних зображень вен пальців
att_deviceOption_MaxFvPhotoCount=Кількість зображень вен пальців
att_deviceOption_PvCount=Кількість поточних шаблонів долонь
att_deviceOption_MaxPvCount=Максимальна кількість шаблонів долонь
att_deviceOption_PvPhotoCount=Поточні зображення долонь
att_deviceOption_MaxPvPhotoCount=Максимальна кількість зображень долоні
att_deviceOption_TransactionCount=Поточна кількість записів
att_deviceOption_MaxAttLogCount=Максимальна кількість записів
att_deviceOption_UserPhotoCount=Фотографії поточного користувача
att_deviceOption_MaxUserPhotoCount=Максимальна кількість фотографій користувачів
att_deviceOption_FaceVersion=Версія алгоритму розпізнавання облич
att_deviceOption_FPVersion=Версія алгоритму розпізнавання відбитків пальців
att_deviceOption_FvVersion=Версія алгоритму розпізнавання вен пальців
att_deviceOption_PvVersion=Версія алгоритму розпізнавання долонь
att_deviceOption_FWVersion=Версія прошивки
att_deviceOption_PushVersion=Push-версія
#=====================================================================
#API
att_api_areaCodeNotNull=Номер області не може бути порожнім
att_api_pinsNotNull=Дані піна не можуть бути порожніми
att_api_pinsOverSize=Довжина даних піна не може перевищувати 500
att_api_areaNoExist=Зона не існує
att_api_sign=Додаток
#=====================================================================
#休息时间段
att_leftMenu_breakTime=Перерва
att_breakTime_startTime=Час початку
att_breakTime_endTime=Час кінця
#=====================================================================
#导入U盘记录
att_import_uploadFileSuccess=Завантаження файла успішно, початок аналізу даних, зачекайте ...
att_import_resolutionComplete=Після завершення аналізу починається оновлення бази даних.
att_import_snNoExist=Пристрій відвідуваності, що відповідає імпортованому файлу, не існує. Будь ласка, виберіть файл ще раз.
att_import_fileName_msg=Вимоги до формату імені імпортованого файлу: починається з серійного номера пристрою і відокремлюється підкресленням "_", наприклад: "3517171600001_attlog.dat".
att_import_notSupportFormat=Цей формат наразі не підтримується!
att_import_selectCorrectFile=Виберіть файл правильного формату!
att_import_fileFormat=Формат файлу
att_import_targetFile=Цільовий файл
att_import_startRow=Кількість рядків на початку заголовка
att_import_startRowNote=Перший рядок формату даних - це імпорт даних, перевірте файл перед імпортом.
att_import_delimiter=Сепаратор
#=====================================================================
#设备操作日志
att_device_op_log_op_type=Код операції
att_device_op_log_dev_sn=Серійний номер пристрою
att_device_op_log_op_content=Оперативний зміст
att_device_op_log_operator_pin=Номер оператора
att_device_op_log_operator_name=Ім'я оператора
att_device_op_log_op_time=Час роботи
att_device_op_log_op_who_value=Значення об'єкта операції
att_device_op_log_op_who_content=Опис об'єкта операції
att_device_op_log_op_value1=Об'єкт операції 2
att_device_op_log_op_value_content1=Опис об'єкта операції 2
att_device_op_log_op_value2=Об'єкт операції 3
att_device_op_log_op_value_content2=Опис об'єкта операції 3
att_device_op_log_op_value3=Об'єкт операції 4
att_device_op_log_op_value_content3=Об'єкт операції 4
#操作日志的操作类型
att_device_op_log_opType_0=Увімкнути
att_device_op_log_opType_1=Вимкнути
att_device_op_log_opType_2=Помилка перевірки
att_device_op_log_opType_3=Сигнал
att_device_op_log_opType_4=Увійти в меню
att_device_op_log_opType_5=Змінити налаштування
att_device_op_log_opType_6=Зареєструвати відбиток пальця
att_device_op_log_opType_7=Зареєструвати пароль
att_device_op_log_opType_8=Зареєструвати HID-карту
att_device_op_log_opType_9=Видалити користувача
att_device_op_log_opType_10=Видалити відбиток пальця
att_device_op_log_opType_11=Видалити пароль
att_device_op_log_opType_12=Видалити RF-карту
att_device_op_log_opType_13=Очистити дані
att_device_op_log_opType_14=Створити картку MF
att_device_op_log_opType_15=Зареєструвати картку MF
att_device_op_log_opType_16=Зареєструвати картку MF
att_device_op_log_opType_17=Видалити реєстрацію картки MF
att_device_op_log_opType_18=Очистити вміст картки MF
att_device_op_log_opType_19=Перемістити реєстраційні дані на картку
att_device_op_log_opType_20=Скопіювати дані з картки на пристрій
att_device_op_log_opType_21=Встановити час
att_device_op_log_opType_22=Заводські налаштування
att_device_op_log_opType_23=Видалити записи входу та виходу
att_device_op_log_opType_24=Очистити права адміністратора
att_device_op_log_opType_25=Змінити налаштування групи контролю доступу
att_device_op_log_opType_26=Змінити налаштування доступу користувачів
att_device_op_log_opType_27=Змініть період часу доступу
att_device_op_log_opType_28=Змінити налаштування комбінації розблокування
att_device_op_log_opType_29=Розблокувати
att_device_op_log_opType_30=Зареєструйте нових користувачів
att_device_op_log_opType_31=Зміна атрибутів відбитків пальців
att_device_op_log_opType_32=Сигнал примусу
att_device_op_log_opType_34=Заборона подвійного проходу
att_device_op_log_opType_35=Видалити фотографії відвідувачів
att_device_op_log_opType_36=Змінення інформації про користувача
att_device_op_log_opType_37=Свято
att_device_op_log_opType_38=Відновити дані
att_device_op_log_opType_39=Резервне копіювання даних
att_device_op_log_opType_40=U завантаження диска
att_device_op_log_opType_41=U завантаження диска
att_device_op_log_opType_42=U шифрування запису відвідуваності диска
att_device_op_log_opType_43=Видалити запис після успішного завантаження USB-диска
att_device_op_log_opType_53=Вихідний комутатор
att_device_op_log_opType_54=Дверний датчик
att_device_op_log_opType_55=Сигнал
att_device_op_log_opType_56=Відновити параметри
att_device_op_log_opType_68=Фотографія зареєстрованого користувача
att_device_op_log_opType_69=Змінити фотографії користувачів
att_device_op_log_opType_70=Змінити ім’я користувача
att_device_op_log_opType_71=Змінити дозволів користувача
att_device_op_log_opType_76=Змінити мережевих налаштувань IP
att_device_op_log_opType_77=Змінити маску мережевих налаштувань
att_device_op_log_opType_78=Змінити шлюз мережевих налаштувань
att_device_op_log_opType_79=Змінити мережевих налаштувань DNS
att_device_op_log_opType_80=Змінити пароль налаштування підключення
att_device_op_log_opType_81=Змінити ідентифікатор пристрою налаштувань з’єднання
att_device_op_log_opType_82=Змінити адресу хмарного сервера
att_device_op_log_opType_83=Змінити порт хмарного сервера
att_device_op_log_opType_87=Змінити налаштування записів контролю доступу
att_device_op_log_opType_88=Змінити параметр обличчя
att_device_op_log_opType_89=Змінити прапорець параметра відбитків пальців
att_device_op_log_opType_90=Змінити прапорець параметра вен пальця
att_device_op_log_opType_91=Змінити прапорець параметра відбитка долоні
att_device_op_log_opType_92=U прапорець оновлення диска 
att_device_op_log_opType_100=Змінення інформації про RF-карту
att_device_op_log_opType_101=Зареєструвати обличчя
att_device_op_log_opType_102=Змінити дозволи персоналу
att_device_op_log_opType_103=Видалити дозволи персоналу
att_device_op_log_opType_104=Додати дозволи персоналу
att_device_op_log_opType_105=Видалити запис контролю доступу
att_device_op_log_opType_106=Видалити обличчя
att_device_op_log_opType_107=Видалити фотографії персоналу
att_device_op_log_opType_108=Змінити параметри
att_device_op_log_opType_109=Вибрати WIFI SSID
att_device_op_log_opType_110=увімкнути проксі
att_device_op_log_opType_111=модифікація proxyip
att_device_op_log_opType_112=модифікація порту проксі
att_device_op_log_opType_113=Змінити пароль особи
att_device_op_log_opType_114=Змінити інформацію про обличчя
att_device_op_log_opType_115=Змінити пароль оператора
att_device_op_log_opType_116=Відновіть налаштування контролю доступу
att_device_op_log_opType_117=Помилка введення пароля оператора
att_device_op_log_opType_118=Блокування пароля оператора 
att_device_op_log_opType_120=Змінити довжину даних Legic Card
att_device_op_log_opType_121=Зареєструвати вени пльця
att_device_op_log_opType_122=Модифікування вен пальця
att_device_op_log_opType_123=Видалити вени пальця
att_device_op_log_opType_124=Зареєструвати долоню
att_device_op_log_opType_125=Модифікувати долоню
att_device_op_log_opType_126=Видалити долоню
#操作对象描述
att_device_op_log_content_pin=Ідентифікатор користувача:
att_device_op_log_content_alarm=Тривога:
att_device_op_log_content_alarm_reason=Причина тривоги:
att_device_op_log_content_update_no=Змінити номер товару:
att_device_op_log_content_update_value=Змінити значення:
att_device_op_log_content_finger_no=Номер відбитка пальця:
att_device_op_log_content_finger_size=Довжина шаблону відбитків пальців:
#=====================================================================
#工作流
att_flowable_datetime_to=до
att_flowable_todomsg_leave=Схвалення відпуски
att_flowable_todomsg_sign=Додати журнал затвердження
att_flowable_todomsg_overtime=Схвалення наднормової
att_flowable_notifymsg_leave=Сповіщення про заявку на відпустку
att_flowable_notifymsg_sign=Додати журнал про повідомлення
att_flowable_notifymsg_overtime=Повідомлення про понаднормову роботу
att_flowable_shift=Зміна:
att_flowable_hour=час
att_flowable_todomsg_trip=Схвалення відрядження
att_flowable_notifymsg_trip=Повідомлення про відрядження
att_flowable_todomsg_out=Схвалення виходу
att_flowable_notifymsg_out=Повідомлення про вихід
att_flow_apply=Заявка
att_flow_applyTime=час заявки
att_flow_approveTime=Час обробки
att_flow_operateUser=Рецензент
att_flow_approve=Схвалення
att_flow_approveComment=анотація
att_flow_approvePass=Результати затвердження
att_flow_status_processing=Схвалення
#=====================================================================
#biotime
att_h5_pers_personIdNull=Ідентифікатор працівника не може бути порожнім
att_h5_attPlaceNull=Місце входа не може бути порожнім
att_h5_attAreaNull=Зона відвідування не може бути порожня
att_h5_pers_personNoExist=Номер працівника не існує
att_h5_signRemarkNull=Зауваження не можуть бути порожніми
att_h5_common_pageNull=Помилка параметра нумерування
att_h5_taskIdNotNull=Ідентифікатор вузла завдання не може бути порожнім
att_h5_auditResultNotNull=Результат схвалення не може бути порожнім
att_h5_latLongitudeNull=Довгота і широта не можуть бути порожніми
att_h5_pers_personIsNull=Ідентифікатор працівника не існує
att_h5_pers_personIsNotInArea=Зона не прив'язана до особи
att_h5_mapApiConnectionsError=Помилка підключення API карти
att_h5_googleMap=Google карти
att_h5_gaodeMap=Gaode карти
att_h5_defaultMap=Карта за замовчуванням
att_h5_shiftTime=Час зміни
att_h5_signTimes=Час поповнення
att_h5_enterKeyWords=Введіть ключові слова:
att_h5_mapSet=Налаштування карти відвідуваності
att_h5_setMapApiAddress=Встановити параметр карти
att_h5_MapSetWarning=Перемикання карти призведе до того, що введена адреса для входу в мобільний термінал не відповідатиме широті та довготі. Будь ласка, змінюйте з обережністю!
att_h5_mapSelect=Вибір карти
att_h5_persNoHire=На даний момент працівник ще не приєднався до компанії.
att_slef_apiKey=API-KEY
att_self_apiJsKey=API-JS-KEY
att_self_noComputeAtt=Відвідуваність дня ще не врахована.
att_self_noSignRecord=Немає записів проходу за день
att_self_imageUploadError=Помилка завантаження зображення
att_self_attSignAddressAreaIsExist=У районі вже є пункти реєстрації
att_self_signRuleIsError=Поточний час поповнення не входить у допустимий час поповнення.
att_self_signAcrossDay=Міжденний графік не можна підписати!
att_self_todaySignIsExist=Сьогодні вже додано виправлення!
att_self_signSetting=Налаштування підпису
att_self_allowSign=Allow sign:
att_self_allowSignSuffix=днів, запис відвідуваності протягом
att_self_onlyThisMonth=Тільки цей місяць
att_self_allowAcrossMonth=Дозволити перехресні місяці
att_self_thisTimeNoSch=Поточний період часу не зміщується!
att_self_revokeReason=Причина відкликання:
att_self_revokeHint=Будь ласка, введіть причину скасування протягом 20 слів для перевірки
att_self_persSelfLogin=Логін самообслуговування співробітника
att_self_isOpenSelfLogin=Починати запис про вхід для самообслуговування співробітників
att_self_applyAndWorkTimeOverlap=Час застосування та робочий час перекриваються
att_apply_DurationIsZero=Тривалість заявки 0, заявка не допускається
att_sign_mapWarn=Помилка завантаження карти. Перевірте мережеве з’єднання та значення КЛЮЧУ
att_admin_applyWarn=Операція не вдалася, є люди без розкладу або час подання заявки не входить у рамки розкладу! ({0})
att_self_getPhotoFailed=Фото не існує
att_self_view=Переглянути
# 二维码
att_param_qrCodeUrl=Url QR-коду
att_param_qrCodeUrlHref=Адреса сервера: порт
att_param_appAttQrCode=QR-код для зчитування відвідування з мобільних пристроїв
att_param_timingFrequency=Інтервал часу: 5-59 хвилин або 1-24 години
att_sign_signTimeNotNull=Час журналу додавання не може бути порожнім
att_apply_overLastMonth=Заявки розпочались раніше, ніж минулого місяця
att_apply_withoutDetail=Немає деталей процесу
att_flowable_noAuth=Будь ласка, використовуйте обліковий запис супер адміністратора для перегляду
att_apply_overtimeOverMaxTimeLong=Надурочні години перевищують максимальні понаднормові години
# 考勤设置参数符号
att_other_arrive=√
att_other_late=L
att_other_early=E
att_other_absent=□
att_other_noSignIn=[
att_other_noSignOff=]
att_other_leave=∆
att_other_overtime=OT
att_other_off=○
att_other_classes=●
att_other_trip=T
att_other_out=G
att_other_incomplete=├
att_other_outcomplete=┤
# 服务器下发命令
att_devCmd_submitTime=Час підтвердження
att_devCmd_returnedResult=Результат
att_devCmd_returnTime=час повернення
att_devCmd_content=Зміст команди
att_devCmd_clearCmd=Очистити список команд
# 实时点名
att_realTime_selectDept=Виберіть відділ
att_realTime_noSignPers=Незареєстрована особа
att_realTime_signPers=Зареєстрований
att_realTime_signMonitor=Моніторинг входу
att_realTime_signDateTime=Час входу
att_realTime_realTimeSet=Налаштування поіменного дзвінка в реальному часі
att_realTime_openRealTime=Увімкніть поіменний дзвінок у режимі реального часу
att_realTime_rollCallEnd=Перекличка в режимі реального часу закінчується
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=За розкладом
att_personSch_cycleSch=Планування циклу
att_personSch_cleanCycleSch=Очистити графік циклу
att_personSch_cleanTempSch=Очистити тимчасовий графік
att_personSch_personCycleSch=Персональний графік циклу
att_personSch_deptCycleSch=Графік циклу відділа
att_personSch_groupCycleSch=Графік циклу групи
att_personSch_personTempSch=Тимчасовий графік персоналу
att_personSch_deptTempSch=Тимчасовий графік роботи відділу
att_personSch_groupTempSch=Тимчасовий графік групи
att_personSch_checkGroupFirst=Будь ласка, перевірте групу ліворуч або людей у списку праворуч для роботи!
att_personSch_sureDeleteGroup=Дійсно видалити {0} та розклад, що відповідає групі?
att_personSch_sch=Розклад
att_personSch_delSch=Видалити розклад
#考勤计算
att_statistical_sureAllCalculate=Ви обов'язково зробите розрахунок відвідуваності для всього персоналу?
#异常管理
att_exception_downTemplate=Шаблон іморту завантаження
att_exception_signImportTemplate=Шаблон імпорту підтвердження
att_exception_leaveImportTemplate=Шаблон імпорту відпустки
att_exception_overtimeImportTemplate=Шаблон імпорту понаднормових
att_exception_adjustImportTemplate=Налаштувати шаблон імпорту
att_exception_cellDefault=Не обов’язкове поле
att_exception_cellRequired=Обов’язкове поле
att_exception_cellDateTime=Обов’язкове поле, формат часу рррр-ММ-дд чч:мм:сс, наприклад: 2020-07-07 08:30:00
att_exception_cellLeaveTypeName=Обов’язкові поля, такі як: „особиста відпустка”, „Відпустка у зв'язку з вступом у шлюб”, „відпустка по вагітності та пологах”, „відпустка через хворобу”, „щорічна трудова відпустка”, „відпустка у зв'язку зі смертю члена сім'ї”, „відпустка для поїздки додому”, „відпустка по догляду за дитиною”, „відрядження” , "going out" Arrogant name
att_exception_cellOvertimeSign=Обов’язкові поля, такі як: „звичайний понаднормовий час”, „надурочний час у дні відпочинку”, „надурочний час у святкові дні”
att_exception_cellAdjustType=Обов’язкове поле, таке як: 'перенесення вимкнено', 'складання класу'
att_exception_cellAdjustDate=Обов’язкове поле, формат часу - рррр-MM-дд, наприклад: 2020-07-07
att_exception_cellShiftName=Обов’язкове поле, коли тип регулювання - зсув
att_exception_refuse=Відмовити
att_exception_end=Ненормальний кінець
att_exception_delete=Видалити
att_exception_stop=Пауза
#时间段
att_timeSlot_normalTimeAdd=Додайте звичайний часовий інтервал
att_timeSlot_elasticTimeAdd=Додайте гнучкий часовий інтервал
#班次
att_shift_addRegularShift=Додати звичайну зміну
att_shift_addFlexibleShift=Додати гнучку зміну
#参数设置
att_param_notLeaveSetting=Налаштування розрахунку без відпустки
att_param_smallestUnit=Мінімальна одиниця
att_param_workDay=Робочий день
att_param_roundingControl=Налаштування округлення
att_param_abort=Вниз (відмова)
att_param_rounding=округлення
att_param_carry=Вгору (нести)
att_param_reportSymbol=Символ відображення звіту
att_param_convertCountValid=Будь ласка, введіть число, допускається лише один десятковий знак
att_other_leaveThing=Річ
att_other_leaveMarriage=Шлюб
att_other_leaveBirth=продукт
att_other_leaveSick=Хворий
att_other_leaveAnnual=рік
att_other_leaveFuneral=Похорони
att_other_leaveHome=Досліджувати
att_other_leaveNursing=Догляд
att_other_leavetrip=різниця
att_other_leaveout=інший
att_common_schAndRest=Розклад і відпочинок
att_common_timeLongs=Тривалість часу
att_personSch_checkDeptOrPersFirst=Будь ласка, перевірте відділ ліворуч або особу зі списку праворуч!
att_personSch_checkCalendarFirst=Будь ласка, спершу виберіть дату, яку потрібно запланувати!
att_personSch_cleanCheck=Очистити чек
att_personSch_delTimeSlot=Очистити вибраний період часу
att_personSch_repeatTimeSlotNoAdd=Жоден повторюваний проміжок часу не буде доданий!
att_personSch_showSchInfo=Показати деталі розкладу
att_personSch_sureToCycleSch=Ви впевнені, що хочете запланувати {0} періодично?
att_personSch_sureToTempSch=Дійсно тимчасово запланувати {0}?
att_personSch_sureToCycleSchDeptOrGroup=Дійсно запланувати весь персонал до {0} періодично?
att_personSch_sureToTempSchDeptOrGroup=Дійсно тимчасово запланувати весь персонал під {0}?
att_personSch_sureCleanCycleSch=Ви впевнені, що хочете очистити {0} від {1} до {2}?
att_personSch_sureCleanTempSch=Ви впевнені, що хочете очистити {0} тимчасовий перехід з {1} на {2}?
att_personSch_sureCleanCycleSchDeptOrGroup=Ви впевнені, що хочете очистити періодичний графік від {1} до {2} для всіх людей до {0}?
att_personSch_sureCleanTempSchDeptOrGroup=Ви впевнені, що хочете очистити тимчасовий графік від {1} до {2} для всіх людей до {0}?
att_personSch_today=Сьогодні
att_personSch_timeSoltName=Назва періоду часу
att_personSch_export=Планування експорту персоналу
att_personSch_exportTemplate=Експорт персоналу тимчасової зміни шаблону
att_personSch_import=Імпорт персоналу тимчасового планування
att_personSch_tempSchTemplate=Тимчасовий шаблон планування персоналу
att_personSch_tempSchTemplateTip=Будь ласка, виберіть час початку та час закінчення для планування, завантажте шаблон розкладу протягом цієї дати
att_personSch_opTip=Інструкція з експлуатації
att_personSch_opTip1=1.за допомогою миші можна перетягнути часовий інтервал до однієї дати в елементі керування календарем для планування.
att_personSch_opTip2=2.У елементі керування календарем двічі клацніть одну дату для планування.
att_personSch_opTip3=3.У елементі керування календарем натисніть і утримуйте мишу, щоб вибрати кілька дат для планування.
att_personSch_schRules=Правила розкладу
att_personSch_schRules1=1.Періодичне планування: перезаписати попередній графік після тієї ж дати, незалежно від наявності перетину.
att_personSch_schRules2=2.Тимчасове планування: перехрестя відбувається тієї ж дати, і попереднє тимчасове планування замінюється пізніше. Якщо перетину немає, вони існуватимуть одночасно.
att_personSch_schRules3=3.Період і тимчасовий: якщо перехрестя є тієї ж дати, період буде тимчасово покритий, а якщо перехрестя немає, вони також існуватимуть одночасно.
att_personSch_schStatus=Статус розкладу
#左侧菜单-排班管理
att_leftMenu_schDetails=Деталі розкладу
att_leftMenu_detailReport=Детальний звіт
att_leftMenu_signReport=Sign-up Details
att_leftMenu_leaveReport=Форма деталей відпустки
att_leftMenu_abnormal=Звіт ненормального відвідування
att_leftMenu_yearLeaveSumReport=Щорічний сумарний звіт відпусток
att_leave_maxFileCount=Ви можете додати щонайбільше 4 фотографії
#时间段
att_timeSlot_add=Встановити часовий інтервал
att_timeSlot_select=Виберіть період часу!
att_timeSlot_repeat=Період часу "{0}" повторюється!
att_timeSlot_overlapping=Період часу "{0}" збігається з часом прихода на роботу "{1}"!
att_timeSlot_addFirst=Будь ласка, спочатку встановіть період часу!
att_timeSlot_notEmpty=Період часу, що відповідає номеру персоналу {0}, не може бути порожнім!
att_timeSlot_notExist=Період часу "{1}", що відповідає номеру персоналу {0}, не існує!
att_timeSlot_repeatEx=Період часу "{1}", що відповідає номеру персоналу {0}, перекривається з часом приїзду "{2}"
att_timeSlot_importRepeat=Період часу "{1}", що відповідає номеру персоналу {0}, повторюється
att_timeSlot_importNotPin=У системі немає людини з номером {0}!
att_timeSlot_elasticTimePeriod=Номер персоналу {0}, неможливо імпортувати гнучкий період часу «{1}».
#导入
att_import_overData=Поточна кількість імпорту - {0}, що перевищує межу в 30000, будь ласка, імпортуйте партіями!
att_import_existIllegalType=Імпортний {0} має недійсний тип!
#验证方式
att_verifyMode_0=Автоматичне розпізнавання
att_verifyMode_1=Тільки відбиток пальця
att_verifyMode_2=Перевірка номера роботи
att_verifyMode_3=Тільки пароль
att_verifyMode_4=Лише картка
att_verifyMode_5=Відбиток пальця або пароль
att_verifyMode_6=Відбиток пальця або картки
att_verifyMode_7=Картка або пароль
att_verifyMode_8=Номер роботи плюс відбиток пальця
att_verifyMode_9=Відбиток пальця плюс пароль
att_verifyMode_10=Картка плюс відбитки пальців
att_verifyMode_11=Картка плюс пароль
att_verifyMode_12=Відбиток пальця плюс пароль плюс картка
att_verifyMode_13=Ідентифікатор роботи плюс відбиток пальця плюс пароль
att_verifyMode_14=(Робочий номер плюс відбиток) або (картка плюс відбиток)
att_verifyMode_15=Обличчя
att_verifyMode_16=Обличчя та відбиток
att_verifyMode_17=Обличчя та пароль
att_verifyMode_18=Обличчя та картка
att_verifyMode_19=Обличчя плюс відбиток пальця плюс картка
att_verifyMode_20=Обличчя плюс відбиток пальця плюс пароль
att_verifyMode_21=Вени пальця
att_verifyMode_22=Вени пальця плюс код
att_verifyMode_23=Вени пальця плюс картка
att_verifyMode_24=Вени пальця плюс код плюс картка
att_verifyMode_25=Принт долоні
att_verifyMode_26=Принт долоні плюс картка
att_verifyMode_27=Принт долоні та обличчя
att_verifyMode_28=Принт долоні та відбиток пальця
att_verifyMode_29=Принт долоні та відбиток пальця плюс обличчя
# 工作流
att_flow_schedule=Audit progress
att_flow_schedulePass=(Пройдено)
att_flow_scheduleNot=(Не схвалено)
att_flow_scheduleReject=(Відхилено)
# 工作时长表
att_workTimeReport_total=Всього робочих годин
# 自动导出报表
att_autoExport_startEndTime=Час початку та кінця
# 年假
att_annualLeave_setting=Встановлення балансу щорічних відпусток
att_annualLeave_settingTip1=Для використання функції залишку щорічних відпусток потрібно встановити час входу для кожного працівника; коли час в'їзду не встановлений, залишкова щорічна відпустка у таблиці річних відпусток персоналу відображається як порожня.
att_annualLeave_settingTip2=Якщо поточна дата перевищує дату випуску очищення, ця зміна набере чинності наступного року; якщо поточна дата менша за дату очищення, коли буде досягнута дата очищення, вона буде очищена та щорічна відпустка буде перевидана.
att_annualLeave_calculate=Дата звільнення та видачі щорічної відпустки
att_annualLeave_workTimeCalculate=Обчисліть за співвідношенням робочого часу
att_annualLeave_rule=Правило щорічної відпустки
att_annualLeave_ruleCountOver=Досягнуто максимально встановленого обмеження кількості
att_annualLeave_years=Вищестоячі роки
att_annualLeave_eachYear=Кожен рік
att_annualLeave_have=Так
att_annualLeave_days=Дні щорічної відпустки
att_annualLeave_totalDays=Загальна щорічна відпустка
att_annualLeave_remainingDays=Залишилось щорічної відпустки
att_annualLeave_consecutive=Встановлення правила щорічної відпустки повинно бути роком поспіль
# 年假结余表
att_annualLeave_report=Річний баланс відпусток
att_annualLeave_validDate=Дійсна дата
att_annualLeave_useDays=Використовувати {0} днів
att_annualLeave_calculateDays=Випустити {0} днів
att_annualLeave_notEnough=Недостатня щорічна відпустка за {0}!
att_annualLeave_notValidDate={0} не входить у діапазон щорічної відпустки!
att_annualLeave_notDays={0} не має щорічної відпустки!
att_annualLeave_tip1=Чжан Сан приєднався 1 вересня минулого року
att_annualLeave_tip2=Встановлення балансу щорічних відпусток
att_annualLeave_tip3=Дата клірингу та випуску - 1 січня кожного року; вона обчислюється округленням відповідно до робочого коефіцієнта; якщо стаж роботи менше або дорівнює 1, буде три дні щорічної відпустки, а якщо стаж роботи менше або дорівнює 3 рокам, буде 5 днів щорічної відпустки
att_annualLeave_tip4=Щорічна відпустка користується розрахунком
att_annualLeave_tip5=Last year 09-01~12-31 enjoy 4/12x3=1.0 days
att_annualLeave_tip6=This year 01-01~12-31 enjoy 4.0 days (this year 01-01~08-31 enjoy 8/12x3=2.0 days   this year 09-01~12-31 enjoy 4/12x5≈2.0 days)
# att SDC
att_sdc_name=Відеоапаратура
att_sdc_wxMsg_firstData=Привіт, у Вас є повідомлення про реєстрацію
att_sdc_wxMsg_stateData=No sense of attendance check-in success
att_sdc_wxMsg_remark=Нагадування: Остаточний результат відвідування залежить від сторінки з деталями реєстрації.
# 时间段
att_timeSlot_conflict=Часовий інтервал конфліктує з іншими часовими інтервалами дня
att_timeSlot_selectFirst=Виберіть часовий інтервал
# 事件中心
att_eventCenter_sign=考勤签到
#异常管理
att_exception_classImportTemplate=Шаблон імпорту зміни Shift
att_exception_cellClassAdjustType=Потрібні поля, наприклад: "{0}"、"{1}"、"{2}"
att_exception_swapDateDate=Необов’ язкове поле, формат часу є yyyy-MM-DD, наприклад:2020-07-07
#消息中心
att_message_leave=повідомлення про присутність {0}
att_message_leaveContent={0} надіслано {1}, {2} на {3} ~ {4}
att_message_leaveTime=час виходу
att_message_overtime=attendance overtime notice
att_message_overtimeContent={0} submitted overtime with {1} ~ {2}
att_message_overtimeTime=overtime
att_message_sign=додаткове повідомлення про підпис участі
att_message_signContent={0} надіслав додатковий підпис на {1}
att_message_adjust=час відпуску
att_message_adjustContent={0} submitted compensatory leave on {1}
att_message_class=повідомлення про налаштування зміни участі
att_message_classContent=shift adjustment details
att_message_classContent0={0} надіслано налаштування зміни. Дата налаштування зміни {1} і зміна {2}
att_message_classContent1={0} надіслано налаштування зміни. Дата налаштування зміни {1} і дата перенесення {2}
att_message_classContent2={0} ({1}) і {2} ({3}) зміни
#推送中心
att_pushCenter_transaction=Запис учасників
# 时间段
att_timeSlot_workTimeNotEqual=Работний час не може бути дорівнюючим часу без роботи
att_timeSlot_signTimeNotEqual=Початковий знак у часі не може дорівнювати часу виходу кінцевого знаку
# 北向接口A
att_api_notNull={0} не може бути порожнім!
att_api_startDateGeEndDate=Час початку не може бути більший або рівний кінцевому часу!
att_api_leaveTypeNotExist=Неправильних насінь не існує!
att_api_imageLengthNot2000=Довжина адреси зображення не може перевищити 2000!
# 20221230新增国际化
att_personSch_workTypeNotNull=Тип роботи, що відповідає номеру персоналу {0} не може бути порожнім!
att_personSch_workTypeNotExist=Тип роботи, що відповідає номеру персоналу {0} не існує!
att_annualLeave_recalculate=Переобчислити
# 20230530新增国际化
att_leftMenu_dailyReport=Щоденний звіт про відвідуваність
att_leftMenu_overtimeReport=Звіт про понаднормову роботу
att_leftMenu_lateReport=Запізнілий звіт
att_leftMenu_earlyReport=Залишити ранній звіт
att_leftMenu_absentReport=Звіт про відсутність
att_leftMenu_monthReport=Щомісячний звіт про відвідуваність
att_leftMenu_monthWorkTimeReport=Місячний звіт про робочий час
att_leftMenu_monthCardReport=Щомісячний звіт про картку
att_leftMenu_monthOvertimeReport=Щомісячний звіт про понаднормову роботу
att_leftMenu_overtimeSummaryReport=Підсумковий звіт про понаднормову роботу персоналу
att_leftMenu_deptOvertimeSummaryReport=Підсумковий звіт про понаднормову роботу
att_leftMenu_deptLeaveSummaryReport=Підсумковий звіт про відпустку відділу
att_annualLeave_calculateDay=Кількість днів щорічної відпустки
att_annualLeave_adjustDay=Налаштувати дні
att_annualLeave_sureSelectDept=Ви впевнені, що бажаєте виконати операцію {0} для вибраного відділу?
att_annualLeave_sureSelectPerson=Ви впевнені, що бажаєте виконати операцію {0} з вибраною особою?
att_annualLeave_calculateTip1=При розрахунку за стажем: розрахунок щорічної відпустки здійснюється з точністю до місяця, якщо стаж становить 10 років і 3 місяці, то для розрахунку буде використано 10 років і 3 місяці;
att_annualLeave_calculateTip2=Якщо конвертація не базується на стажі роботи: розрахунок щорічної відпустки точний до року, якщо стаж становить 10 років і 3 місяці, тоді для розрахунку буде використано 10 років;
att_rule_isInCompleteTip=Пріоритет є найвищим, коли жоден вхід або вихід не реєструється як неповний, а запізнення, рання відпустка, відсутність і дійсні
att_rule_absentTip=Якщо відсутність входу або виходу реєструється як прогул, тривалість прогулу дорівнює тривалості робочого часу мінус тривалість пізньої або ранньої відпустки
att_timeSlot_elasticTip1=0, ефективний час дорівнює фактичному, відсутність прогулів
att_timeSlot_elasticTip2=Якщо фактичний час перевищує робочий час, ефективний час дорівнює робочому часу, відсутність прогулів
att_timeSlot_elasticTip3=Якщо фактична тривалість менша за тривалість роботи, ефективна тривалість дорівнює фактичній тривалості, а відсутність на роботі дорівнює тривалості роботи мінус фактична тривалість
att_timeSlot_maxWorkingHours=Робочі години не можуть перевищувати ніж
# 20231030
att_customReport=Кастомний звіт про відвідуваність
att_customReport_byDayDetail=Звіт по днях
att_customReport_byPerson=Зведення по персоналіях
att_customReport_byDept=Зведення по відділу
att_customReport_queryMaxRange=Максимальний діапазон запиту - чотири місяці
# 20240630新增国际化
att_personSch_shiftWorkTypeTip1=1. Під час роботи надгодин на звичайних робочих/відпочинкових днях приоритет планування менший за приоритет відпустків
att_personSch_shiftWorkTypeTip2=2. Коли тип роботи є надгодинним за відпустками, пріоритет планування вищий за тип відпустків
att_personVerifyMode=Метод перевірки персоналу
att_personVerifyMode_setting=Параметри методу перевірки
att_personSch_importCycSch=Kişisel döngü programlamasını indir
att_personSch_cycSchTemplate=Шаблон планування персонального циклу
att_personSch_exportCycSchTemplate=Звантажити шаблон планування циклу персоналу
att_personSch_scheduleTypeNotNull=Тип Shift не може бути порожнім або не існує!
att_personSch_shiftNotNull=Зміна не може бути порожньою!
att_personSch_shiftNotExist=Зміна не існує!
att_personSch_onlyAllowOneShift=Регулярне планування дозволяє запланувати лише одну зміну!
att_shift_attShiftStartDateRemark2=Период начала цикла начинается в первую неделю;Месяц, в котором начинается цикл, - первый месяц.
#打卡状态
att_cardStatus_setting=Настройки статусу обіднання
att_cardStatus_name=Ім'я
att_cardStatus_value=Значення
att_cardStatus_alias=Псевдоним
att_cardStatus_every_day=Щоденно
att_cardStatus_by_week=За тиждень
att_cardStatus_autoState=Автоматичний статус
att_cardStatus_attState=Статус обіднання
att_cardStatus_signIn=Підпис
att_cardStatus_signOut=Випис
att_cardStatus_out=поза межами
att_cardStatus_outReturn=Повернення
att_cardStatus_overtime_signIn=Підпис на продолження
att_cardStatus_overtime_signOut=Випис з продолження
# 20241030新增国际化
att_leaveType_enableMaxDays=Включение годового ограничения
att_leaveType_maxDays=Годовое ограничение (дней)
att_leaveType_applyMaxDays=Заявление на год не должно превышать {0} дней
att_param_overTimeSetting=Настройка уровней сверхурочного времени
att_param_overTimeLevel=Уровень сверхурочного времени (часов)
att_param_overTimeLevelEnable=Включить расчет уровней сверхурочного времени
att_param_reportColor=Цвет отчета
# APP
att_app_signClientTip=Цей пристрій вже був зареєстрований іншим користувачем сьогодні
att_app_noSignAddress=Область реєстрації не встановлена, будьте такі любезні обратися до адміністратора для встановлення
att_app_notInSignAddress=Не доведено місце реєстрації, неможливо провести реєстрацію
att_app_attendance=Моє присутність
att_app_apply=Заява на присутність
att_app_approve=Моє підтвердження
# 20250530
att_node_leaderNodeExist=Версія схвильного підписання вже створена
att_signAddress_init=Ініціалізація карти
att_signAddress_initTips=Введіть ключ карти та починайте встановлення карти для використання адресу