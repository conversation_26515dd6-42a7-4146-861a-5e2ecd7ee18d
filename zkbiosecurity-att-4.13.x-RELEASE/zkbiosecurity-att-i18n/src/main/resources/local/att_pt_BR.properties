#系统名称
att_systemName=Sistema de Presença 1.0
#=====================================================================
#左侧菜单
att_module=Presença
#左侧菜单-考勤设备
att_leftMenu_attendanceDevice=Gerenciamento de dispositivos
att_leftMenu_device=Dispositivo de atendimento
att_leftMenu_point=Ponto de Presença
att_leftMenu_sign_address=Endereço de entrada para celular
att_leftMenu_adms_devCmd=Comando emitido pelo servidor
#左侧菜单-基础信息
att_leftMenu_basicInformation=Informações Básica
att_leftMenu_rule=Regra
att_leftMenu_base_rule=Regra básica
att_leftMenu_department_rule=Regras departamentais
att_leftMenu_holiday=Feriado
att_leftMenu_leaveType=Tipo de Licença
att_leftMenu_timingCalculation=Cálculo Cronometrado
att_leftMenu_autoExport=Relatório Automático
att_leftMenu_param=Configuração de parâmetro
#左侧菜单-班次管理
att_leftMenu_shiftManagement=Turno
att_leftMenu_timeSlot=Calendário
att_leftMenu_shift=Turno
#左侧菜单-排班管理
att_leftMenu_scheduleManagement=Cronograma
att_leftMenu_group=Grupo
att_leftMenu_groupPerson=Agrupador
att_leftMenu_groupSch=Cronograma do Grupo
att_leftMenu_deptSch=Cronograma do Departamento
att_leftMenu_personSch=Cronograma Pessoal
att_leftMenu_tempSch=Cronograma Temporário
att_leftMenu_nonSch=Pessoal Imprevisto
#左侧菜单-异常管理
att_leftMenu_exceptionManagement=Gerenciamento de exceção de comparecimento
att_leftMenu_sign=Recibo Anexado
att_leftMenu_leave=Licença
att_leftMenu_trip=Viagem de Negócios
att_leftMenu_out=Saída
att_leftMenu_overtime=Hora Extra
att_leftMenu_adjust=Ajustar e Anexar
att_leftMenu_class=Ajustar Turno
#左侧菜单-统计报表
att_leftMenu_statisticalReport=Relatório de estatísticas de atendimento
att_leftMenu_manualCalculation=Cálculo Manual
att_leftMenu_transaction=Transações
att_leftMenu_dayCardDetailReport=Presença Diária
att_leftMenu_leaveSummaryReport=Resumo da Licença
att_leftMenu_dayDetailReport=Relatório Diário
att_leftMenu_monthDetailReport=Informações do Relatório Mensal
att_leftMenu_monthStatisticalReport=Relatório de Estatísticas Mensal
att_leftMenu_deptStatisticalReport=Relatório Departamental
att_leftMenu_yearStatisticalReport=Relatório Anual
att_leftMenu_attSignCallRollReport=Tabela de implementação de login
att_leftMenu_workTimeReport=Relatório de tempo de trabalho
#左侧菜单-设备操作日志
att_leftMenu_device_op_log=Log de operação do dispositivo
#左侧菜单-实时点名
att_leftMenu_realTime_callRoll=Real Time CallRoll
#=====================================================================
#公共
att_common_person=Pessoal
att_common_pin=ID
att_common_group=Grupo
att_common_dept=Departamento
att_common_symbol=Símbolo
att_common_deptNo=Número de Departamento
att_common_deptName=Nome do Departamento
att_common_groupNo=Número do Grupo
att_common_groupName=Nome do Grupo
att_common_operateTime=Tempo de Operação
att_common_operationFailed=A operação Falhou
att_common_id=ID
att_common_deptId=ID do Departamento
att_common_groupId=ID do Grupo
att_common_deviceId=ID do Dispositivo
att_person_pin=ID Pessoal
att_person_name=Nome
att_person_lastName=Sobrenome
att_person_internalCard=Número do Cartão
att_person_attendanceMode=modo de atendimento
att_person_normalAttendance=atendimento normal
att_person_noPunchCard=PunchCard não requerido
att_common_attendance=Presença
att_common_attendance_hour=Presença (horas)
att_common_attendance_day=Presença (dia)
att_common_late=Atrasado
att_common_early=Adiantado
att_common_overtime=Hora Extra
att_common_exception=Exceção
att_common_absent=Ausente
att_common_leave=Licença
att_common_trip=Viagem de Negócios
att_common_out=Saída
att_common_staff=Colaborador
att_common_superadmin=Superusuário
att_common_msg=Conteúdo SMS
att_common_min=Duração da Mensagem Curta (minutos)
att_common_letterNumber=Só é possível inserir números ou letras!
att_common_relationDataCanNotDel=Os dados associados não podem ser excluídos.
att_common_relationDataCanNotEdit=Os dados associados não podem ser modificados.
att_common_needSelectOneArea=Por favor selecione uma área!
att_common_neesSelectPerson=Por favor selecione uma pessoa!
att_common_nameNoSpace=O nome não pode conter espaços!
att_common_digitsValid=Só pode digitar números com mais de duas casas decimais!
att_common_numValid=Digite apenas números!
#=====================================================================
#工作面板
att_dashboard_worker=Viciado em Trabalho
att_dashboard_today=Presença do Dia
att_dashboard_todayCount=Estatísticas Segmentadas de Presença do Dia
att_dashboard_exceptionCount=Estatísticas Anormais (este mês)
att_dashboard_lastWeek=Semana Passada
att_dashboard_lastMonth=Mês Passado
att_dashboard_perpsonNumber=Total de Pessoas
att_dashboard_actualNumber=Pessoal Efetivo
att_dashboard_notArrivedNumber=Pessoal Ausente
att_dashboard_attHour=Tempo de Trabalho
#区域
#设备
att_op_syncDev=Sincronizar dados do software com o dispositivo
att_op_account=Verificação de Dados de Presença
att_op_check=Carregar Dados Novamente
att_op_deleteCmd=Comandos de Limpeza do Dispositivo
att_op_dataSms=Mensagem Pública
att_op_clearAttPic=Limpar as Fotos de Presença
att_op_clearAttLog=Limpar as Transações de Presença
att_device_waitCmdCount=Comandos a serem executados
att_device_status=Ativar Status
att_device_register=Dispositivo de Registro
att_device_isRegister=Dispositivo de Cadastro
att_device_existNotRegDevice=Equipamento de Dispositivo Não Registrado, os dados não podem ser obtidos!
att_device_fwVersion=Versão do Firmware
att_device_transInterval=Duração da Atualização (min)
att_device_cmdCount=Número máximo de comandos para comunicação com o servidor.
att_device_delay=Tempo de Busca do Registro (segundos)
att_device_timeZone=Calendário
att_device_operationLog=Logs de Operação
att_device_registeredFingerprint=Registrar Impressão Digital
att_device_registeredUser=Registrar Pessoa
att_device_fingerprintImage=Imagem da Impressão Digital
att_device_editUser=Editar Pessoal
att_device_modifyFingerprint=Modificar Impressão Digital
att_device_faceRegistration=Registrar Face
att_device_userPhotos=Foto Pessoal
att_device_attLog=Enviar registros de presença 
att_device_operLog=Enviar Informações Pessoais
att_device_attPhoto=Enviar fotos de presença
att_device_isOnLine=Status Online
att_device_InputPin=Insira o número da pessoa
att_device_getPin=Obter os dados pessoais especificados
att_device_separatedPin=Múltiplos números pessoais, separados por vírgulas
att_device_authDevice=Dispositivo Autorizado
att_device_disabled=Os seguintes dispositivos estão desativados e não podem ser operados!
att_device_autoAdd=Adicionar novo dispositivo automaticamente
att_device_receivePersonOnlyDb=Receber apenas dados pessoais no banco de dados
att_devMenu_control=Controle do Dispositivo
att_devMenu_viewOrGetInfo=Visualizar e Obter Informações
att_devMenu_clearData=Limpar Dados do dispositivo
att_device_disabledOrOffline=O dispositivo não está ativado ou não está online e não pode ser operado!
att_device_areaStatus=Status da área do dispositivo
att_device_areaCommon=Área é normal
att_device_areaEmpty=Área está vazia
att_device_isRegDev=A modificação do fuso horário ou do status da máquina de registro entrará em vigor após reiniciar o dispositivo!
att_device_canUpgrade=Os seguintes dispositivos podem ser atualizados
att_device_offline=Os seguintes dispositivos estão offline e não podem ser operados!
att_device_oldProtocol=Protocolo antigo
att_device_newProtocol=Novo protocolo
att_device_noMoreTwenty=O pacote de atualização do firmware do dispositivo de protocolo antigo não pode exceder 20M
att_device_transferFilesTip=Teste de firmware com sucesso, transfira arquivos
att_op_clearAttPers=Limpar equipe de equipamentos
#区域人员
att_op_forZoneAddPers=Configuração de Área de Pessoal
att_op_dataUserSms=Mensagem Privada
att_op_syncPers=Sincronizar novamente com o dispositivo
att_areaPerson_choiceArea=Por favor, selecione a área!
att_areaPerson_byAreaPerson=Por área
att_areaPerson_setByAreaPerson=Definir Por área
att_areaPerson_importBatchDel=Importar lote deletar
att_areaPerson_syncToDevSuccess=Operação bem-sucedida! Por favor, aguarde o envio do comando.
att_areaPerson_personId=ID Pessoal
att_areaPerson_areaId=ID da Área
att_area_existPerson=Há pessoas na área!
att_areaPerson_notice1=A área ou o pessoal não podem estar vazios ao mesmo tempo!
att_areaPerson_notice2=Nenhuma pessoa ou área foi buscada!
att_areaPerson_notice3=Nenhum dispositivo encontrado na área!
att_areaPerson_addArea=Adicionar área
att_areaPerson_delArea=Excluir área
att_areaPerson_persNoExit=Pessoa não existe
att_areaPerson_importTip1=Certifique-se de que a pessoa importada já exista no módulo de pessoal
att_areaPerson_importTip2=Os importadores de lote não serão entregues automaticamente ao dispositivo e precisam ser sincronizados manualmente
att_areaPerson_addAreaPerson=Adicionar pessoa da área
att_areaPerson_delAreaPerson=Excluir pessoal da área
att_areaPerson_importDelAreaPerson=Importar e excluir pessoal da área
att_areaPerson_importAreaPerson=Importar pessoal da area
#考勤点
att_attPoint_name=Nome do Ponto de Presença
att_attPoint_list=Lista de Pontos de Presença
att_attPoint_deviceModule=Módulo do Dispositivo
att_attPoint_acc=Controle de Acesso
att_attPoint_park=Estacionamento
att_attPoint_ins=FaceKiosk
att_attPoint_pid=Certificado Pessoal
att_attPoint_vms=Vídeo
att_attPoint_psg=Corredor
att_attPoint_doorList=Lista de Portas
att_attPoint_deviceList=Lista de Dispositivos
att_attPoint_channelList=Lista de canais
att_attPoint_gateList=Lista de portões
att_attPoint_recordTypeList=Puxar tipo de registro
att_attPoint_door=Por favor, selecione a porta correspondente.
att_attPoint_device=Por favor, selecione o dispositivo correspondente.
att_attPoint_gate=Selecione o portão correspondente
att_attPoint_normalPassRecord=Registro de aprovação normal
att_attPoint_verificationRecord=Registro de verificação
att_person_attSet=Configuração de Presença
att_attPoint_point=Por favor, selecione o ponto de presença.
att_attPoint_count=Pontos de presença autorizados insuficientes na licença; A operação falhou!
att_attPoint_notSelect=O módulo não está configurado
att_attPoint_accInsufficientPoints=Pontos de presença inadequados para registros de controle de acesso!
att_attPoint_parkInsufficientPoints=Pontos de presença inadequados para registros de estacionamentos!
att_attPoint_insInsufficientPoints=A licença para pontos de presença do faceKiosk é insuficiente!
att_attPoint_pidInsufficientPoints=O Certificado Pessoal não é suficiente para a presença!
att_attPoint_doorOrParkDeviceName=Nome da porta ou nome do dispositivo de estacionamento
att_attPoint_vmsInsufficientPoints=Vídeo quando os pontos de presença são insuficientes!
att_attPoint_psgInsufficientPoints=O canal tem pontos de atendimento insuficientes!
att_attPoint_delDevFail=A exclusão do dispositivo falhou, o dispositivo foi usado como presença!
att_attPoint_pullingRecord=O ponto de presença está recebendo registros regularmente, aguarde!
att_attPoint_lastTransactionTime=Última hora de extração de dados
att_attPoint_masterDevice=Dispositivo mestre
att_attPoint_channelName=nome do canal
att_attPoint_cameraName=Nome da câmera
att_attPoint_cameraIP=ip da câmera
att_attPoint_channelIP=canal ip
att_attPoint_gateNumber=Número do portão
att_attPoint_gateName=Nome do portão
#APP考勤签到地址
att_signAddress_address=Endereço
att_signAddress_longitude=Longitude
att_signAddress_latitude=Latitude
att_signAddress_range=Alcance efetivo
att_signAddress_rangeUnit=A unidade em metros (m)
#=====================================================================
#规则
att_rule_baseRuleSet=Configuração de Regra Básica
att_rule_countConvertSet=Configuração de Cálculo
att_rule_otherSet=Outras Configurações
att_rule_baseRuleSignIn=Regra de entrada
att_rule_baseRuleSignOut=Regra de saída
att_rule_earliestPrinciple=Regra Mais Antiga
att_rule_theLatestPrinciple=Regra Mais Recente
att_rule_principleOfProximity=Regra de Proximidade
att_rule_baseRuleShortestMinutes=O período mínimo deve ser maior que (mínimo 10 minutos)
att_rule_baseRuleLongestMinutes=O período máximo deve ser menor que (máximo de 1.440 minutos)
att_rule_baseRuleLateAndEarly=Licença Tardia e Antecipada Contadas como Ausentes
att_rule_baseRuleCountOvertime=Estatísticas de Horas Extras
att_rule_baseRuleFindSchSort=Registro de Busca de Turno 
att_rule_groupGreaterThanDepartment=Grupo -> Departamento
att_rule_departmentGreaterThanGroup=Departamento -> Grupo
att_rule_baseRuleSmartFindClass=Regra de Correspondência de Turno Inteligente
att_rule_timeLongest=Maior Duração de Trabalho
att_rule_exceptionLeast=Menos Anormal
att_rule_baseRuleCrossDay=Resultado do cálculo de presença para o turno cruzado do dia
att_rule_firstDay=Primeiro Dia
att_rule_secondDay=Segundo Dia
att_rule_baseRuleShortestOvertimeMinutes=Duração Mínima de Horas Extras (minutos)
att_rule_baseRuleMaxOvertimeMinutes=Horas extras máximas (minutos)
att_rule_baseRuleElasticCal=Cálculo de Duração Flexível
att_rule_baseRuleTwoPunch=Tempo acumulado para a cada duas batidas de cartão
att_rule_baseRuleStartEnd=Cálculo do tempo início e fim do horário de batida do cartão
att_rule_countConvertHour=Regra de Conversão de Horas
att_rule_formulaHour=Fórmula: Horas 
att_rule_countConvertDay=Regra de Conversão de Dias
att_rule_formulaDay=Fórmula: Dias 
att_rule_inFormulaShallPrevail=Use o resultado calculado pela fórmula como padrão;
att_rule_remainderHour=O resto é maior ou igual a
att_rule_oneHour=Calculado como uma hora, caso contrário, calcule como meia hora ou ignore;
att_rule_halfAnHour=Calculado como meia hora, caso contrário ignore;
att_rule_remainderDay=O quociente é maior ou igual aos minutos de trabalho
att_rule_oneDay=%, calculado como um dia; caso contrário, calcule como meio dia ou ignore;
att_rule_halfAnDay=%, calculado como meio dia, caso contrário ignore;
att_rule_countConvertAbsentDay=Regra de conversão de dias ausentes
att_rule_markWorkingDays=Calculado como dias úteis
att_rule_countConvertDecimal=Dígitos exatos da casa decimal
att_rule_otherSymbol=Configuração do símbolo do resultado da presença no relatório
att_rule_arrive=Esperado/Real
att_rule_noSignIn=Sem Check-in
att_rule_noSignOff=Sem Check-out
att_rule_off=Ajustar Descanso
att_rule_class=Anexar Presença
att_rule_shortLessLong=O horário de presença não pode exceder a duração mais longa do horário de presença.
att_rule_symbolsWarning=Você deve configurar o símbolo no relatório de presença!
att_rule_reportSettingSet=Configurações de exportação de relatório
att_rule_shortDateFormat=Formato de data
att_rule_shortTimeFormat=Formato da hora
att_rule_baseRuleSignBreakTime=Se o relógio deve aparecer durante o intervalo
att_leftMenu_custom_rule=Regra personalizada
att_custom_rule_already_exist={0} Regras personalizadas já existem!
att_add_group_custom_rule=Adicionar regras de agrupamento
att_custom_rule_type=Tipo de regra
att_rule_type_group=Regras de agrupamento
att_rule_type_dept=Regras de departamento
att_custom_rule_orgNames=Usando o objeto
att_rult_maxOverTimeType1=Sem limite
att_rult_maxOverTimeType2=Esta semana
att_rult_maxOverTimeType3=Este mês
att_rule_countConvertDayRemark1=Exemplo: o tempo de trabalho efetivo é de 500 minutos e o tempo de trabalho é de 480 minutos por dia. O resultado é 500/480
att_rule_countConvertDayRemark2=Exemplo: o tempo de trabalho efetivo é de 500 minutos e o tempo de trabalho é de 480 minutos por dia. O resultado é 500/480
att_rule_countConvertDayRemark3=Exemplo: o tempo de trabalho efetivo é de 300 minutos e o tempo de trabalho é de 480 minutos por dia. O resultado é 300/480
att_rule_countConvertDayRemark4=Referência de conversão de dias: registre como os dias úteis no período não funcionam;
att_rule_countConvertDayRemark5=É registrado como o número de dias úteis: é limitado ao cálculo do número de dias de conclusão e, enquanto houver conclusão em cada período, a duração da conclusão é calculada de acordo com o número de dias úteis do período;
att_rule_baseRuleSmartFindRemark1=O tempo mais longo: de acordo com o ponto do cartão do dia, calcule as horas de trabalho correspondentes a ada turno do dia e encontre o turno do tempo de trabalho mais longo do dia;
att_rule_baseRuleSmartFindRemark2=Mínimo anormal: calcule o número de horários anormais correspondentes a cada turno do dia, de acordo com o ponto do cartão do dia, e encontre o turno com o menor número de anormalidades do dia para calcular o tempo de trabalho;
att_rule_baseRuleHourValidator=O minuto de julgamento de meia hora não pode ser maior ou igual a 1 hora!
att_rule_baseRuleDayValidator=O período de julgamento de meio dia não pode ser maior ou igual ao período de julgamento de um dia!
att_rule_overtimeWarning=A duração máxima de horas extras não pode ser menor que a duração mínima mínima de horas extras!
att_rule_noSignInCountType=Contagem de check-in ausente como
att_rule_absent=Ausente
att_rule_earlyLeave=Licença antecipada
att_rule_noSignOffCountType=Contagem de check-out ausente como
att_rule_minutes=Minutos
att_rule_noSignInCountLateMinute=Falta o número de check-in com minutos de atraso
att_rule_noSignOffCountEarlyMinute=Contagem de check-out ausente, pois os minutos saem mais cedo
att_rule_incomplete=Incompleto
att_rule_noCheckInIncomplete=Incompleto e sem check-in
att_rule_noCheckOutIncomplete=Incompleto e sem check-out
att_rule_lateMinuteWarning=Não fazer login como minutos atrasados ​​deve ser maior que 0 e menor que o período mais longo
att_rule_earlyMinuteWarning=O checkout não assinado é contado, pois os minutos de licença antecipada devem ser maiores que 0 e menores que a duração mais longa
att_rule_baseRuleNoSignInCountLateMinuteRemark=Quando não o check-in é contado como atrasado, se não o check-in é contado como atrasado por N minutos
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=Quando não assinar é registrado como saída antecipada, se não assinar, será registrado como saída antecipada N minutos#节假日
#节假日
att_holiday_placeholderNo=É recomendável começar com H, por exemplo H01.
att_holiday_placeholderName=Recomenda-se nomear com [Ano] + [Nome do Feriado], por exemplo. [Dia do Trabalho de 2017].
att_holiday_dayNumber=Número de dias
att_holiday_validDate_msg=Feriados durante este período
#假种
att_leaveType_leaveThing=Licença Casual
att_leaveType_leaveMarriage=Licença de Casamento
att_leaveType_leaveBirth=Licença Maternidade
att_leaveType_leaveSick=Atestado Médico
att_leaveType_leaveAnnual=Licença Anual
att_leaveType_leaveFuneral=Licença de Luto
att_leaveType_leaveHome=Licença de Mudança
att_leaveType_leaveNursing=Licença Amamentação
att_leaveType_isDeductWorkLong=Anexar ao horário de trabalho
att_leaveType_placeholderNo=É recomendável começar com L, por exemplo L1.
att_leaveType_placeholderName=É recomendável terminar com "feriado", por exemplo, Feriado de Casamento.
#定时计算
att_timingcalc_timeCalcFrequency=Frequência de Cálculo
att_timingcalc_timeCalcInterval=Horário de Cálculo Programado
att_timingcalc_timeSet=Configuração de Horário de Cálculo Programado
att_timingcalc_timeSelect=Por favor, escolha a hora!
att_timingcalc_optionTip=Pelo menos um cálculo de presença programada diária válido deve ser retido
#自动导出报表
att_autoExport_reportType=Tipo de Relatório
att_autoExport_fileType=Tipo de Arquivo
att_autoExport_fileName=Nome do Arquivo
att_autoExport_fileDateFormat=Formato de Data
att_autoExport_fileTimeFormat=Formato da Hora
att_autoExport_fileContentFormat=Formato de Conteúdo
att_autoExport_fileContentFormatTxt=Exemplo: {deptName} 00 {personPin} 01 {personName} 02 {attDatetime} 03
att_autoExport_timeSendFrequency=Frequência de Envio
att_autoExport_timeSendInterval=Intervalo de Tempo de Envio
att_autoExport_emailType=Tipo de E-mail Recebedor
att_autoExport_emailRecipients=E-mail Recebedor
att_autoExport_emailAddress=Endereço de E-mail
att_autoExport_emailExample=Examplo:<EMAIL>,<EMAIL>
att_autoExport_emailSubject=Título do E-mail
att_autoExport_emailContent=Corpo do E-mail
att_autoExport_field=Campo
att_autoExport_fieldName=Nome do Campo
att_autoExport_fieldCode=Número do Campo
att_autoExport_reportSet=Configuração do Relatório
att_autoExport_timeSet=Configuração do Tempo de Entrega do E-mail
att_autoExport_emailSet=Configuração do E-mail
att_autoExport_emailSetAlert=Por favor, indique o seu endereço de e-mail.
att_autoExport_emailTypeSet=Configuração do recebedor
att_autoExport_byDay=Por Dia
att_autoExport_byMonth=Por Mês
att_autoExport_byPersonSet=Definido pela Pessoa
att_autoExport_byDeptSet=Definido por Departamento
att_autoExport_byAreaSet=Definido por Área
att_autoExport_emailSubjectSet=Configuração do Título
att_autoExport_emailContentSet=Configuração do Corpo
att_autoExport_timePointAlert=Selecione o momento de envio correto.
att_autoExport_lastDayofMonth=Último dia do mês
att_autoExport_firstDayofMonth=Primeiro dia do mês
att_autoExport_dayofMonthCheck=Data Específica
att_autoExport_dayofMonthCheckAlert=Por favor, selecione a data específica.
att_autoExport_chooseDeptAlert=Por favor, selecione o Departamento!
att_autoExport_sendFormatSet=Configuração do modo de envio
att_autoExport_sendFormat=Modo de Envio
att_autoExport_mailFormat=Método de Entrega da Caixa de Correio
att_autoExport_ftpFormat=Método de Envio FTP
att_autoExport_sftpFormat=Método de Envio SFTP
att_autoExport_ftpUrl=Endereço do servidor FTP
att_autoExport_ftpPort=Porta do servidor FTP
att_autoExport_ftpTimeSet=Configuração do tempo de envio do FTP
att_autoExport_ftpParamSet=Configuração de parâmetros FTP
att_autoExport_ftpUsername=Nome de usuário do FTP
att_autoExport_ftpPassword=Senha FTP
att_autoExport_correctFtpParam=Por favor, preencha os parâmetros ftp corretamente
att_autoExport_correctFtpTestParam=Teste a conexão para garantir que a comunicação esteja normal
att_autoExport_inputFtpUrl=Digite o endereço do servidor
att_autoExport_inputFtpPort=Digite a porta do servidor
att_autoExport_ftpSuccess=Conexão bem-sucedida
att_autoExport_ftpFail=Verifique se as configurações do parâmetro estão incorretas.
att_autoExport_validFtp=Digite um endereço de servidor válido
att_autoExport_validPort=Digite uma porta de servidor válida
att_autoExport_selectExcelTip=Tipo de arquivo selecione EXCEL, o formato do conteúdo é todos os campos!
#=====================================================================
#时间段
att_timeSlot_periodType=Tipo de Horário
att_timeSlot_normalTime=Horário normal
att_timeSlot_elasticTime=Horário Flexível
att_timeSlot_startSignInTime=Hora de Início do Check-In
att_timeSlot_toWorkTime=Hora de Check-in
att_timeSlot_endSignInTime=Hora de Fim do Check-In
att_timeSlot_allowLateMinutes=Permitir atraso (minutos)
att_timeSlot_isMustSignIn=Deve Fazer o Check-In
att_timeSlot_startSignOffTime=Hora de Início do Check-Out
att_timeSlot_offWorkTime=Hora do Check-Out
att_timeSlot_endSignOffTime=Hora de Fim do Check-Out
att_timeSlot_allowEarlyMinutes=Permitir Saída Antecipada (minutos)
att_timeSlot_isMustSignOff=Deve Fazer o Check-out
att_timeSlot_workingHours=Tempo de Trabalho (minutos)
att_timeSlot_isSegmentDeduction=Dedução Automática de Tempo de Intervalo
att_timeSlot_startSegmentTime=Hora de Início
att_timeSlot_endSegmentTime=Hora de Fim
att_timeSlot_interSegmentDeduction=Tempo Deduzido (minutos)
att_timeSlot_markWorkingDays=Dia Útil
att_timeSlot_isAdvanceCountOvertime=Hora Extra Automática (Check-In Adiantado)
att_timeSlot_signInAdvanceTime=Hora de Fim de Hora Extra automática (Check-In)
att_timeSlot_isPostponeCountOvertime=Hora Extra Automática (Check-Out Atrasado)
att_timeSlot_signOutPosponeTime=Hora de Início da Hora Extra Automática (Check-Out)
att_timeSlot_isCountOvertime=Calculado como Hora Extra
att_timeSlot_timeSlotLong=O horário de trabalho deve atender ao intervalo de presença definido pelas regras:
att_timeSlot_alertStartSignInTime=A Hora de Início do Check-In deve ser menor que a hora do Check-In.
att_timeSlot_alertEndSignInTime=A Hora de Fim do Check-In deve ser maior que a hora do Check-In.
att_timeSlot_alertStartSignInAndEndSignIn=A Hora de Início do Check-Out deve ser menor que a hora do Check-Out.
att_timeSlot_alertStartSignOffTime=A hora de início da hora extra não pode ser menor que a hora do check-out.
att_timeSlot_alertEndSignOffTime=A hora de início da hora extra não pode ser maior que a hora de fim do check-out.
att_timeSlot_alertStartUnequalEnd=Os dias úteis não podem ser menores que 0.
att_timeSlot_alertStartSegmentTime=O tempo deduzido não pode ser menor que 0.
att_timeSlot_alertStartAndEndTime=A hora de inicio de check-out não pode ser igual a hora de fim de check-in.
att_timeSlot_alertEndAndoffWorkTime=As horas não podem ser maiores que 23.
att_timeSlot_alertToWorkLesserAndEndTimeLesser=Os minutos não podem ser maiores que 59.
att_timeSlot_alertLessSignInAdvanceTime=A Hora de Check-In antecipado deve ser menor que a hora de início do trabalho
att_timeSlot_alertMoreSignInAdvanceTime=A Hora de Check-In antecipado deve ser maior que a hora de início do Check-In
att_timeSlot_alertMoreSignOutPosponeTime=A Hora de Check-Out atrasado deve ser menor que a hora de fim do Check-Out
att_timeSlot_alertLessSignOutPosponeTime=A Hora de Check-Out atrasado deve ser maior que a hora de fim do do trabalho
att_timeSlot_time=Insira o formato correto de tempo.
att_timeSlot_alertMarkWorkingDays=O dia de trabalho não pode estar vazio!
att_timeSlot_placeholderNo=É recomendável começar com T, por exemplo T01.
att_timeSlot_placeholderName=Recomenda-se começar com T ou terminar com "horário".
att_timeSlot_beforeToWork=Antes de ir trabalhar
att_timeSlot_afterToWork=Depois do trabalho
att_timeSlot_beforeOffWork=Antes de sair de serviço
att_timeSlot_afterOffWork=Depois do trabalho
att_timeSlot_minutesSignInValid=O check-in é válido em minutos
att_timeSlot_toWork=Em serviço
att_timeSlot_offWork=Fora de serviço
att_timeSlot_minutesSignInAsOvertime=Faça login minutos atrás para horas extras
att_timeSlot_minutesSignOutAsOvertime=Comece a contar horas extras minutos depois
att_timeSlot_minOvertimeMinutes=Minutos mínimos de horas extras
att_timeSlot_enableWorkingHours=Se deve habilitar o horário de trabalho
att_timeSlot_eidtTimeSlot=Hora da edição
att_timeSlot_browseBreakTime=Procurar períodos de descanso
att_timeSlot_addBreakTime=Adicionar quebras
att_timeSlot_enableFlexibleWork=Permitir trabalho flexível
att_timeSlot_advanceWorkMinutes=Pode ir trabalhar antecipadamente
att_timeSlot_delayedWorkMinutes=Pode adiar o trabalho
att_timeSlot_advanceWorkMinutesValidMsg1=O número de minutos antes de ir para o trabalho é maior que o número de minutos que pode ir para o trabalho com antecedência
att_timeSlot_advanceWorkMinutesValidMsg2=O número de minutos que você pode trabalhar com antecedência é menor que o número de minutos antes de ir para o trabalho
att_timeSlot_advanceWorkMinutesValidMsg3=O número de minutos que podem ser trabalhados com antecedência é menor ou igual ao número de minutos antes de entrar para trabalhar em horas extras.
att_timeSlot_advanceWorkMinutesValidMsg4=O número de minutos que pode ter antes de entrar para fazer horas extras é maior ou igual ao número de minutos trabalhados com antecedência.
att_timeSlot_delayedWorkMinutesValidMsg1=O número de minutos após o trabalho é maior que o número de minutos que podem ser estendidos para o horário de trabalho
att_timeSlot_delayedWorkMinutesValidMsg2=O número de minutos para o atraso do trabalho é menor que o número de minutos após o trabalho
att_timeSlot_delayedWorkMinutesValidMsg3=O número de minutos que podem ser agendados para trabalhar é menor ou igual ao número de minutos após o término do trabalho, terminando a sessão e começando a trabalhar horas extras
att_timeSlot_delayedWorkMinutesValidMsg4=O número de minutos que podem ser posteriores ao trabalho do início e fim da hora extra é maior ou igual ao número de minutos após o horário programado para o trabalho
att_timeSlot_allowLateMinutesValidMsg1=O número de minutos de atraso é menor que o número de minutos após o trabalho
att_timeSlot_allowLateMinutesValidMsg2=O número de minutos após o trabalho é maior que o número de minutos que os minutos podem chegar atrasados
att_timeSlot_allowEarlyMinutesValidMsg1=Permissão dos minutos iniciais é menor que minutos antes do trabalho
att_timeSlot_allowEarlyMinutesValidMsg2=O número de minutos antes do trabalho é maior que o número de minutos permitidos
att_timeSlot_timeOverlap={0} se sobrepõe ao tempo {1}, modifique o período selecionado!
att_timeSlot_atLeastOne=Pelo menos 1 intervalo de tempo de descanso!
att_timeSlot_mostThree=No máximo três intervalos de tempo de descanso!
att_timeSlot_canNotEqual=A hora de início do período de descanso não pode ser igual à hora de fim!
att_timeSlot_shoudInWorkTime=Certifique-se de que o período de descanso está dentro do horário de trabalho!
att_timeSlot_repeatBreakTime=Repetir o intervalo de tempo!
att_timeSlot_toWorkLe=O tempo de trabalho é menor que o tempo mínimo de início do período de descanso selecionado:
att_timeSlot_offWorkGe=As horas de folga são maiores que o tempo final máximo do período de descanso selecionado:
att_timeSlot_crossDays_toWork=O horário mínimo de início do período de intervalo está dentro do período de tempo:
att_timeSlot_crossDays_offWork=O horário final máximo do período de descanso é dentro do período:
att_timeSlot_allowLateMinutesRemark=Do horário de trabalho aos minutos tardios permitidos para calcular o trabalho normal
att_timeSlot_allowEarlyMinutesRemark=Começando cedo a partir do horário de folga no número de minutos permitido para sair mais cedo
att_timeSlot_isSegmentDeductionRemark=Excluindo o período de descanso no período
att_timeSlot_attEnableFlexibleWorkRemark1=O trabalho flexível não é permitido para definir o número de saídas tardias e antecipadas
att_timeSlot_afterToWorkRemark=Após minutos de trabalho é igual a diferido para minutos de trabalho
att_timeSlot_beforeOffWorkRemark=Antes que minutos de trabalho iguais possam ir para minutos de trabalho
att_timeSlot_attEnableFlexibleWorkRemark2=O número de minutos após o trabalho é maior ou igual a horas fora do expediente + horas de trabalho atrasadas
att_timeSlot_attEnableFlexibleWorkRemark3=Você pode trabalhar com minutos antecipados, deve ser menor ou igual a N minutos úteis para trabalhar minutos extras
att_timeSlot_attEnableFlexibleWorkRemark4=Adiamentos para minutos de trabalho, deve ser menor ou igual a N minutos fora do trabalho
att_timeSlot_attBeforeToWorkAsOvertimeRemark=Exemplo: aula 9:00, para fazer horas extras entre 60 minutos antes do trabalho e depois faça check-in antes das 8:00 às 20:00 para horas extras
att_timeSlot_attAfterOffWorkAsOvertimeRemark=Exemplo: Após 18 horas de trabalho, após 60 minutos de trabalho, assine a retirada e faça horas extras e inicie as horas extras entre as 19 e as horas de check-out.
att_timeSlot_longTimeValidRemark=O tempo efetivo de assinatura após o trabalho e o tempo efetivo de assinatura não podem se sobrepor no período de tempo!
att_timeSlot_advanceWorkMinutesValidMsg5=O número de minutos válidos antes da entrada é maior que o número de minutos que podem ser trabalhados com antecedência
att_timeSlot_advanceWorkMinutesValidMsg6=Os minutos para trabalhar com antecedência devem ser inferiores a minutos válidos para entrar antes do trabalho
att_timeSlot_delayedWorkMinutesValidMsg5=O número de minutos válidos após a entrada é maior que o número de minutos que podem ser adiados para o trabalho
att_timeSlot_delayedWorkMinutesValidMsg6=O número de minutos que podem ser adiados para o trabalho deve ser menor que os minutos válidos após a entrada
att_timeSlot_advanceWorkMinutesValidMsg7=O horário de check-in antes do trabalho não pode se sobrepor ao horário de check-out após o trabalho no dia anterior
att_timeSlot_delayedWorkMinutesValidMsg7=O horário de check-out após o trabalho não pode se sobrepor ao horário de check-in antes do trabalho no dia seguinte
att_timeSlot_maxOvertimeMinutes=Limitar as horas extras máximas
#班次
att_shift_basicSet=Tipo de Cronograma
att_shift_advancedSet=Nome do Cronograma
att_shift_type=Tipo de Turno
att_shift_name=Nome do Turno
att_shift_regularShift=Turno Regular
att_shift_flexibleShift=Turno Flexível
att_shift_color=Cor
att_shift_periodicUnit=Unidade
att_shift_periodNumber=Ciclo
att_shift_startDate=Data de Início
att_shift_startDate_firstDay=Data de Início do Ciclo
att_shift_isShiftWithinMonth=Ciclo do Turno em Um Mês
att_shift_attendanceMode=Modo de Presença
att_shift_shiftNormal=Cartão de Ponto de Acordo com o Turno Normal
att_shift_oneDayOneCard=Bater o ponto uma vez a qualquer Hora do Dia
att_shift_onlyBrushTime=Calcular Apenas o Tempo do Cartão de Ponto
att_shift_notBrushCard=Ponto Liberado
att_shift_overtimeMode=Modo de Hora Extra
att_shift_autoCalc=Cálculo Automático pelo Computador
att_shift_mustApply=Horas Extras Devem Ser Aplicadas
att_shift_mustOvertime=Deve fazer horas extras ou ter uma Ausência
att_shift_timeSmaller=Menor Duração Entre o Cálculo Automático e o Recebimento de Hora Extra
att_shift_notOvertime=Não Calculado Como Hora Extra
att_shift_overtimeSign=Tipo de hora extra
att_shift_normal=Dia Normal
att_shift_restday=Dia de Folga
att_shift_timeSlotDetail=Informações do Cronograma
att_shift_doubleDeleteTimeSlot=Clique duas vezes no período do turno; você pode excluir o período
att_shift_addTimeSlot=Adicionar Cronograma
att_shift_cleanTimeSlot=Limpar Cronograma
att_shift_NO=NÃO
att_shift_notAll=Desmarcar Tudo
att_shift_notTime=Se a caixa de seleção de informação do cronograma não puder ser marcada, isso indica que há uma sobreposição no cronograma.
att_shift_notExistTime=Este cronograma não existe.
att_shift_cleanAllTimeSlot=Tem certeza de que deseja limpar o cronograma do turno selecionado?
att_shift_pleaseCheckBox=Marque a caixa de seleção no lado esquerdo que é a mesma que a hora atual exibida no lado direito.
att_shift_pleaseUnit=Preencha as unidades de ciclo e o número de ciclos.
att_shift_pleaseAllDetailTimeSlot=Selecione as informações do cronograma.
att_shift_placeholderNo=É recomendável começar com S, por exemplo S0.
att_shift_placeholderName=É recomendável começar com S ou terminar com "turno".
att_shift_workType=Tipo de trabalho
att_shift_normalWork=Trabalho normal
att_shift_holidayOt=Feriado
att_shift_attShiftStartDateRemark=Exemplo: a data de início do ciclo é o número 22, com um período de três dias, o número 22/23/24 está na classe A / B / C e o número 19/20/21 está na classe A. / Classe B / classe C, antes e depois da data e assim por diante.
att_shift_isShiftWithinMonthRemark1=Mudar dentro do mês, o ciclo circula apenas até o último dia de cada mês, não agendado consecutivamente ao longo do mês;
att_shift_isShiftWithinMonthRemark2=Turno não mensal, o ciclo é alternado para o último dia de cada mês, se um ciclo não terminar, continue para o próximo mês e assim por diante;
att_shift_workTypeRemark1=Nota: Quando o tipo de trabalho é selecionado como horas extras em um dia de descanso, a participação não será calculada no dia de um feriado.
att_shift_workTypeRemark2=Fim de semana de hora extra, marca de horas extras padrão para o dia de descanso e o computador calcula automaticamente as horas extras. Nenhuma aplicação de horas extras é necessária. As horas de trabalho do dia são registradas como horas extras e a participação não é contada durante os feriados.
att_shift_workTypeRemark3=Feriado de hora extra,  marcação de horas extras é padronizada como feriados e o computador calcula automaticamente as horas extras, nenhuma aplicação de horas extras é necessário e as horas de trabalho do dia são registradas como horas extras;
att_shift_attendanceModeRemark1=Exceto no caso de passar normalmente por turno, não é considerado horas extras adiantado ou atrasado, por exemplo:
att_shift_attendanceModeRemark2=1. Nenhum check-in é necessário ou um cartão válido é usado uma vez por dia, nenhuma hora extra é contada;
att_shift_attendanceModeRemark3=2.Tipo de trabalho: trabalho normal, modo de atendimento: furto gratuito de cartão, então o horário do turno diurno é considerado como horário de trabalho efetivo;
att_shift_periodStartMode=Tipo de início do ciclo
att_shift_periodStartModeByPeriod=Data de início do período
att_shift_periodStartModeBySch=Mudar data de início
att_shift_addTimeSlotToShift=Se deseja adicionar o intervalo de tempo deste turno
#=====================================================================
#分组
att_group_editGroup=Editar pessoal para o grupo
att_group_browseGroupPerson=Navegar pelo Pessoal do Grupo
att_group_list=Lista de Grupos
att_group_placeholderNo=É recomendável começar com G, por exemplo G1
att_group_placeholderName=É recomendável começar com G ou terminar com grupo.
att_widget_deptHint=Observação: Importar todo o pessoal do departamento selecionado
att_widget_searchType=Condições de Busca
att_widget_noPerson=Ninguém foi escolhido
#分组排班
#部门排班
att_deptSch_existsDept=Há uma mudança departamental no departamento e não é permitido excluir o departamento.
#人员排班
att_personSch_view=Exibir Agendamento de Pessoal
#临时排班
att_schedule_type=Tipo de Cronograma
att_schedule_tempType=Tipo de Temporário
att_schedule_normal=Cronograma Normal
att_schedule_intelligent=Cronograma Inteligente
att_tempSch_scheduleType=Tipo de Cronograma
att_tempSch_startDate=Data de Início
att_tempSch_endDate=Data de Fim
att_tempSch_attendanceMode=Método de Presença
att_tempSch_overtimeMode=Modo de Hora Extra
att_tempSch_overtimeRemark=Marcar de Ho
att_tempSch_existsDept=Há um turno temporário do departamento no departamento e não é permitido excluir o departamento.
att_schedult_opAddTempSch=Novo Turno Temporário
att_schedule_cleanEndDate=Hora de Fim vazia
att_schedule_selectOne=O cronograma normal só pode escolher um turno!
att_schedule_selectPerson=Selecione o pessoal primeiro!
att_schedule_selectDept=Selecione o departamento primeiro!
att_schedule_selectGroup=Selecione o grupo primeiro!
att_schedule_selectOneGroup=Apenas um grupo pode ser selecionado!
att_schedule_arrange=Escolha um turno!
att_schedule_leave=Licença
att_schedule_trip=Viagem
att_schedule_out=Saída
att_schedule_off=Folga
att_schedule_makeUpClass=Anexar
att_schedule_class=Ajustar
att_schedule_holiday=Feriado
att_schedule_offDetail=Ajustar Descanso
att_schedule_makeUpClassDetail=Anexar Presença
att_schedule_classDetail=Ajustar Turno
att_schedule_holidayDetail=Feriado
att_schedule_noSchDetail=Não agendado
att_schedule_normalDetail=Normal
att_schedule_normalSchInfo=Centro do Turno: Sem Turno Entre Dias
att_schedule_multipleInterSchInfo=Turno separado por vírgula: múltiplos turnos entre dias
att_schedule_inderSchFirstDayInfo=Deslocar o turno para trás da grade: O turno entre dias é registrado como o primeiro dia
att_schedule_inderSchSecondDayInfo=Deslocar o turno para frente da grade: O turno entre dias é registrado como o segundo dia
att_schedule_timeConflict=Conflito com o período de turno existente, não é permitido salvar!
#=====================================================================
att_excp_notExisetPerson=A pessoa não existe!
att_excp_leavePerson=Demissão do colaborador!
#补签单
att_sign_signTime=Hora de Batida do Cartão
att_sign_signDate=Data da marcação
#请假
att_leave_arilName=Tipo de Licença
att_leave_image=Foto de Requerimento de Licença
att_leave_imageShow=Sem Imagens
att_leave_imageType=Dica do Erro: O formato da imagem não está correto, formato suportado: JPEG, GIF, PNG!
att_leave_imageSize=Dica do Erro: A imagem selecionada é muito grande, o tamanho máximo da imagem é de 4 MB!
att_leave_leaveLongDay=Duração da licença (dia)
att_leave_leaveLongHour=Hora de sair (horas)
att_leave_leaveLongMinute=Duração da licença
att_leave_endNoLessAndEqualStart=O horário de término não pode ser menor ou igual ao horário de início
att_leave_typeNameNoExsists=O nome falso da classe não existe
att_leave_startNotNull=A hora de início não pode estar vazia
att_leave_endNotNull=O horário de término não pode estar vazio
att_leave_typeNameConflict=O nome do tipo falso está em conflito com o nome do status de atendimento
#出差
att_trip_tripLongDay=Duração da viagem (dia)
att_trip_tripLongMinute=Duração da viagem (minutos)
att_trip_tripLongHour=Tempo de viagem (horas)
#外出
att_out_outLongDay=Duração da saída (dia)
att_out_outLongMinute=Duração da saída (minutos)
att_out_outLongHour=Tempo gasto (tempo)
#加班
att_overtime_type=Tipo Hora Extra
att_overtime_normal=Hora Extra Normal
att_overtime_rest=Hora Extra de Fim de Semana
att_overtime_overtimeLong=Duração das horas extras (minutos)
att_overtime_overtimeHour=Horas extras (horas)
att_overtime_notice=A hora de aplicação da hora extra não é permitida por mais de um dia!
att_overtime_minutesNotice=A hora de aplicação das hora extra não pode ser inferior ao tempo mínimo de hora extra!
#调休补班
att_adjust_type=Ajustar Tipo
att_adjust_adjustDate=Ajustar Data
att_adjust_shiftName=Anexar Turno de Presença
att_adjust_selectClass=Selecione o nome do turno que necessita de presença em anexo.
att_shift_notExistShiftWorkDate=A data do ajuste não está no dia útil do turno de selecionado e não pode ser adicionada
att_adjust_shiftPeriodStartMode=O turno selecionado para turno de compensação, se a data de início for por turno, o padrão é o 0º
att_adjust_shiftNameNoNull=Turnos anexados não podem estar vazios
att_adjust_shiftNameNoExsist=O turno anexado não existe
#调班
att_class_type=Ajustar Tipo
att_class_sameTimeMoveShift=Ajustar o turno pessoal no mesmo dia
att_class_differenceTimeMoveShift=Ajustar o turno pessoal em outros dias
att_class_twoPeopleMove=Troca de duas pessoas
att_class_moveDate=Ajustar Data
att_class_shiftName=Nome do Cronograma Original
att_class_moveShiftName=O novo turno ajustado não pode estar vazio.
att_class_movePersonPin=Ajustar ID Pessoal
att_class_movePersonName=Ajustar Nome da Pessoa
att_class_movePersonLastName=Ajustar Sobrenome da Pessoa
att_class_moveDeptName=Ajustar Nome do Departamento
att_class_personPin=ID Pessoal
att_class_shiftNameNoNull=O novo turno ajustado não pode estar vazio.
att_class_personPinNoNull=O ID Pessoal da nova pessoa não pode estar vazio!
att_class_isNotExisetSwapPersonPin=O Ajuste da nova pessoa não existe, adicione novamente!
att_class_personNoSame=Você não pode ajustar para a mesma pessoa, tente novamente.
att_class_outTime=A data do ajuste e a data da transferência não podem ser superiores a um mês!
att_class_shiftNameNoExsist=A mudança de ajuste não existe
att_class_swapPersonNoExisist=A pessoa que troca não existe
att_class_dateNoSame=Turnos pessoais em datas diferentes, as datas não podem ser as mesmas
#=====================================================================
#节点
att_node_name=Nó
att_node_type=Tipo de Nó
att_node_leader=Líder Direto
att_node_leaderNode=Nó do Líder Direto
att_node_person=Pessoa Designada
att_node_position=Atribuir Cargo
att_node_choose=Selecionar Cargo
att_node_personNoNull=A pessoa não pode estar vazia!
att_node_posiitonNoNull=O cargo não pode estar vazio
att_node_placeholderNo=É recomendável começar com N, por exemplo N01.
att_node_placeholderName=É recomendável começar com um cargo ou nome, terminando com um nó, por exemplo o Nó do Gerente.
att_node_searchPerson=Insira os Critérios de Pesquisa
att_node_positionIsExist=O cargo já existe nos dados do nó, selecione o cargo novamente.
#流程
att_flow_type=Tipo de Fluxo
att_flow_rule=Regra de Fluxo
att_flow_rule0=Menor ou igual a 1 dia
att_flow_rule1=Mais de 1 dia e menor ou igual a 3 dias
att_flow_rule2=Mais de 3 dias e menor ou igual a 7 dias
att_flow_rule3=Mais de 7 dias
att_flow_node=Nó de Aprovação
att_flow_start=Fluxo Inicial
att_flow_end=Fluxo Final
att_flow_addNode=Adicionar Nó
att_flow_placeholderNo=É recomendável começar com F, por exemplo F01.
att_flow_placeholderName=É recomendável começar com o tipo, terminar com "fluxo", por exemplo, Fluxo de Licença.
att_flow_tips=Observação: A ordem de aprovação dos nós é de cima para baixo e você pode arrastar a classificação após a seleção.
#申请
att_apply_personPin=Solicitação de ID Pessoal
att_apply_type=Tipo de Exceção
att_apply_flowStatus=Status Geral do Fluxo
att_apply_start=Iniciando uma solicitação
att_apply_flowing=Pendente
att_apply_pass=Aprovada
att_apply_over=Fim
att_apply_refuse=Rejeitada
att_apply_revoke=Revogar
att_apply_except=Exceção
att_apply_view=Ver Informações
att_apply_leaveTips=A pessoa tem um pedido de licença durante esse período!
att_apply_tripTips=A pessoa tem uma solicitação de viagem de negócios durante esse período!
att_apply_outTips=A pessoa solicitou uma saída durante esse período!
att_apply_overtimeTips=A pessoa tem solicitações de horas extras durante esse período!
att_apply_adjustTips=Durante esse período, a pessoa pode solicitar um ensaio!
att_apply_classTips=A pessoa tem uma solicitação de turno durante esse período!
#审批
att_approve_wait=Aprovação Pendente
att_approve_refuse=Não Aprovada
att_approve_reason=Motivo
att_approve_personPin=ID do Aprovador
att_approve_personName=Nome do Aprovador
att_approve_person=Aprovador
att_approve_isPass=Aprovar?
att_approve_status=Status Atual do Nó
att_approve_tips=O ponto de tempo já existe no fluxo e não pode ser repetido.
att_approve_tips2=O nó do fluxo não foi configurado, entre em contato com o administrador para configuração.
att_approve_offDayConflicts=Conflito no ajuste da data de folga e dispensa; não é possível adicionar.
att_approve_shiftConflicts=Conflito da data de presença anexada e a data do trabalho; não é possível adicionar.
att_approve_shiftNoSch=Nenhum aplicativo de reagendamento é permitido na data agendada.
att_approve_classConflicts=A data do cronograma é uma data sem turno e não pode ser adicionada.
att_approve_selectTime=O tempo de seleção determinará o processo de acordo com as regras
att_approve_withoutPermissionApproval=Existe um fluxo de trabalho sem permissão para aprovação, verifique!
#=====================================================================
#考勤计算
att_op_calculation=Cálculo de Presença
att_op_calculation_notice=Os dados de presença foram calculados em segundo plano, tente novamente mais tarde!
att_op_calculation_leave=Incluindo pessoal renunciado
att_statistical_choosePersonOrDept=Por favor, selecione Departamento ou Pessoa!
att_statistical_sureCalculation=Tem certeza de que deseja realizar os cálculos de presença?
att_statistical_filter=A condição de filtração está pronta!
att_statistical_initData=Inicialização do banco de dados concluída!
att_statistical_exception=Inicialização de dados de exceção concluída!
att_statistical_error=Falha no cálculo de ausência!
att_statistical_begin=Iniciando cálculo!
att_statistical_end=Fim do cálculo!
att_statistical_noticeTime=Período de atendimento opcional: os dois primeiros meses do dia!
att_statistical_remarkHoliday=H
att_statistical_remarkClass=C
att_statistical_remarkNoSch=NS
att_statistical_remarkRest=R
#原始记录表
att_op_importAccRecord=Importar registro de controle de acesso
att_op_importParkRecord=Importar registro de estacionamento
att_op_importInsRecord=Importar registros do faceKiosk
att_op_importPidRecord=Importar os registros de Certificado Pessoal
att_op_importVmsRecord=Importar registro de vídeo
att_op_importUSBRecord=Importar registro de disco U
att_transaction_noAccModule=Módulo de controle de acesso ausente!
att_transaction_noParkModule=Módulo de estacionamento ausente!
att_transaction_noInsModule=Módulo de faceKiosk ausente!
att_transaction_noPidModule=Módulo de Certificado Pessoal ausente!
att_transaction_exportRecord=Exportar registros originais
att_transaction_exportAttPhoto=Exportar fotos de presença
att_transaction_fileIsTooLarge=O arquivo exportado é muito grande. Limite o período
att_transaction_exportDate=Data de exportação
att_statistical_attDatetime=Data de Presença
att_statistical_attPhoto=Foto de Presença
att_statistical_attDetail=Informações de Presença
att_statistical_acc=Dispositivo de Controle de Acesso
att_statistical_att=Dispositivo de Tempo de Presença
att_statistical_park=Câmera LPR
att_statistical_faceRecognition=Dispositivo de Reconhecimento Facial
att_statistical_app=Dispositivos Móveis
att_statistical_vms=Dispositivos de vídeo
att_statistical_psg=Equipamento de canal
att_statistical_dataSources=Fontes de Dados
att_transaction_SyncRecord=Sincronizar registros de presença
#日打卡详情表
att_statistical_dayCardDetail=Detalhes de check-in
att_statistical_cardDate=Data de Gravação
att_statistical_cardNumber=Horas Registradas
att_statistical_earliestTime=Hora Inicial
att_statistical_latestTime=Horário Final
att_statistical_cardTime=Hora de Batida do Cartão
#请假汇总表
att_statistical_leaveDetail=Informações da Licença
#日明细报表
att_statistical_attDate=Data de Presença
att_statistical_week=Semana
att_statistical_shiftInfo=Informações do Turno
att_statistical_shiftTimeData=Hora de Trabalho/Descanso
att_statistical_cardValidData=Hora de Batida do Cartão
att_statistical_cardValidCount=Contagem de Batida de Cartão
att_statistical_lateCount=Contagem Final
att_statistical_lateMinute=Minuto Final
att_statistical_earlyCount=Contagem Inicial
att_statistical_earlyMinute=Minuto Inicial
att_statistical_countData=Dados de Hora
att_statistical_minuteData=Dados de Minutos
att_statistical_attendance_minute=Presença (minutos)
att_statistical_overtime_minute=Horas Extras (minutos)
att_statistical_unusual_minute=Anormal (minutos)
#月明细报表
att_monthdetail_should_hour=Deve (hora)
att_monthdetail_actual_hour=Real (hora)
att_monthdetail_valid_hour=Válido (hora)
att_monthdetail_absent_hour=Conclusão (hora)
att_monthdetail_leave_hour=Licença (hora)
att_monthdetail_trip_hour=Viagem de Negócios (a cada hora)
att_monthdetail_out_hour=Saída (a cada hora)
att_monthdetail_should_day=Deve (hora)
att_monthdetail_actual_day=Real (dia)
att_monthdetail_valid_day=Efetivo (dia)
att_monthdetail_absent_day=Conclusão (dia)
att_monthdetail_leave_day=Licença (dia)
att_monthdetail_trip_day=Viagem de Negócios (dia)
att_monthdetail_out_day=Saída (dia)
#月统计报表
att_statistical_late_minute=Duração (minutos)
att_statistical_early_minute=Duração (minutos)
#部门统计报表
#年度统计报表
att_statistical_should=Deve
att_statistical_actual=Real
att_statistical_valid=Válido
att_statistical_numberOfTimes=Horários
att_statistical_usually=Dia Útil
att_statistical_rest=Fim de semana
att_statistical_holiday=Feriado
att_statistical_total=Total
att_statistical_month=Estatísticas do Mês
att_statistical_year=Estatísticas do Ano
att_statistical_attendance_hour=Presença (hora)
att_statistical_attendance_day=Presença (dia)
att_statistical_overtime_hour=Hora Extra (hora)
att_statistical_unusual_hour=Exceção (hora)
att_statistical_unusual_day=Anormal (dia)
#考勤设备参数
att_deviceOption_query=Visualizar Parâmetros do Dispositivo
att_deviceOption_noOption=Nenhuma informação de parâmetro, por favor, obtenha os parâmetros do dispositivo primeiro
att_deviceOption_name=Nome do Parâmetro
att_deviceOption_value=Valor do Parâmetro
att_deviceOption_UserCount=Número Atual de Usuários
att_deviceOption_MaxUserCount=Número Máximo de Usuários
att_deviceOption_FaceCount=Número Atual de Faces
att_deviceOption_MaxFaceCount=Número Máximo de Faces
att_deviceOption_FacePhotoCount=Número atual de imagens faciais
att_deviceOption_MaxFacePhotoCount=Número máximo de imagens faciais
att_deviceOption_FingerCount=Número Atual de Impressões Digitais
att_deviceOption_MaxFingerCount=Número Máximo de Impressões Digitais
att_deviceOption_FingerPhotoCount=Número de imagens de impressões digitais atuais
att_deviceOption_MaxFingerPhotoCount=Número máximo de imagens de impressão digital
att_deviceOption_FvCount=Número Atual de Veias do Dedo
att_deviceOption_MaxFvCount=Número Máximo de Veias do Dedo
att_deviceOption_FvPhotoCount=Número de imagens atuais das veias dos dedos
att_deviceOption_MaxFvPhotoCount=Número de imagens das veias dos dedos
att_deviceOption_PvCount=Número Atual de Palmas
att_deviceOption_MaxPvCount=Número Máximo de Palmas
att_deviceOption_PvPhotoCount=Imagens atuais da palma
att_deviceOption_MaxPvPhotoCount=Número máximo de imagens de palmas
att_deviceOption_TransactionCount=Número Atual de Registros
att_deviceOption_MaxAttLogCount=Número Máximo de Registros
att_deviceOption_UserPhotoCount=Fotos Atuais dos Usuários
att_deviceOption_MaxUserPhotoCount=Número Máximo de Fotos dos Usuários
att_deviceOption_FaceVersion=Versão do Algoritmo de Reconhecimento Facial
att_deviceOption_FPVersion=Versão do Algoritmo de Reconhecimento de Impressão Digital
att_deviceOption_FvVersion=Versão do Algoritmo de Reconhecimento de Veias do Dedo
att_deviceOption_PvVersion=Versão do Algoritmo de Reconhecimento de Palma
att_deviceOption_FWVersion=Versão do Firmware
att_deviceOption_PushVersion=Versão do Push
#=====================================================================
#API
att_api_areaCodeNotNull=O número da área não pode estar vazio
att_api_pinsNotNull=Os dados do pin não pode estar vazio
att_api_pinsOverSize=O tamanho dos dados do pin não pode exceder 500
att_api_areaNoExist=A área não existe
att_api_sign=Suplemento
#=====================================================================
#休息时间段
att_leftMenu_breakTime=Intervalo
att_breakTime_startTime=Hora de início
att_breakTime_endTime=Hora de fim
#=====================================================================
#导入U盘记录
att_import_uploadFileSuccess=Carregamento do arquivo com sucesso, inicie a análise dos dados, aguarde ...
att_import_resolutionComplete=Após a conclusão da análise, comece a atualizar o banco de dados.
att_import_snNoExist=O dispositivo de presença correspondente ao arquivo importado não existe. Por favor, selecione o arquivo novamente.
att_import_fileName_msg=Requisitos de formato do nome do arquivo importado: o número de série do dispositivo começa com e é separado por um sublinhado "_", por exemplo: "3517171600001_attlog.dat"。
att_import_notSupportFormat=This format is not supported for the time being!
att_import_selectCorrectFile=Por favor, selecione o arquivo de formato correto!
att_import_fileFormat=formato de arquivo
att_import_targetFile=Arquivo de destino
att_import_startRow=Número de linhas no início do cabeçalho
att_import_startRowNote=A primeira linha do formato de dados é importar dados, verifique o arquivo antes de importar.
att_import_delimiter=Separador
#=====================================================================
#设备操作日志
att_device_op_log_op_type=Código de operação
att_device_op_log_dev_sn=Número de série do dispositivo
att_device_op_log_op_content=Conteúdo operacional
att_device_op_log_operator_pin=Número do operador
att_device_op_log_operator_name=Nome do operador
att_device_op_log_op_time=Tempo de operação
att_device_op_log_op_who_value=Valor do objeto de operação
att_device_op_log_op_who_content=Descrição do objeto de operação
att_device_op_log_op_value1=Objeto de operação 2
att_device_op_log_op_value_content1=Descrição do objeto de operação 2
att_device_op_log_op_value2=Objeto de operação 3
att_device_op_log_op_value_content2=Descrição do objeto de operação 3
att_device_op_log_op_value3=Objeto de operação 4
att_device_op_log_op_value_content3=Descrição do objeto de operação 4
#操作日志的操作类型
att_device_op_log_opType_0=Ligar
att_device_op_log_opType_1=Desligar
att_device_op_log_opType_2=Falha na verificação
att_device_op_log_opType_3=Alarme
att_device_op_log_opType_4=Entrar no menu
att_device_op_log_opType_5=Mudar configurações
att_device_op_log_opType_6=Registrar impressão digital
att_device_op_log_opType_7=Registrar senha
att_device_op_log_opType_8=Registrar cartão HID
att_device_op_log_opType_9=Deletar usuário
att_device_op_log_opType_10=Excluir impressão digital
att_device_op_log_opType_11=Excluir senha
att_device_op_log_opType_12=Excluir cartão RF
att_device_op_log_opType_13=Apagar os dados
att_device_op_log_opType_14=Criar cartão MF
att_device_op_log_opType_15=Registrar cartão MF
att_device_op_log_opType_16=Registrar cartão MF
att_device_op_log_opType_17=Excluir registro do cartão MF
att_device_op_log_opType_18=Limpar o conteúdo do cartão MF
att_device_op_log_opType_19=Mover dados de registro para o cartão
att_device_op_log_opType_20=Copie os dados do cartão para a máquina
att_device_op_log_opType_21=Definir tempo
att_device_op_log_opType_22=Configurações de fábrica
att_device_op_log_opType_23=Excluir registros de entrada e saída
att_device_op_log_opType_24=Limpar privilégios de administrador
att_device_op_log_opType_25=Modificar configurações do grupo de controle de acesso
att_device_op_log_opType_26=Modificar configurações de acesso do usuário
att_device_op_log_opType_27=Modifique o período de acesso
att_device_op_log_opType_28=Modificar configurações de combinação de desbloqueio
att_device_op_log_opType_29=Desbloquear
att_device_op_log_opType_30=Registrar novos usuários
att_device_op_log_opType_31=Alterar atributos de impressão digital
att_device_op_log_opType_32=Alarme de coação
att_device_op_log_opType_34=Anti-passback
att_device_op_log_opType_35=Excluir fotos de presença
att_device_op_log_opType_36=Modificar informações do usuário
att_device_op_log_opType_37=Feriado
att_device_op_log_opType_38=Restaurar dados
att_device_op_log_opType_39=Dados de backup
att_device_op_log_opType_40=Upload de disco em U
att_device_op_log_opType_41=Download do disco em U
att_device_op_log_opType_42=Criptografia de registro de presença em disco U
att_device_op_log_opType_43=Excluir registro após o download bem-sucedido do disco USB
att_device_op_log_opType_53=Interruptor de saída
att_device_op_log_opType_54=Sensor da porta
att_device_op_log_opType_55=Alarme
att_device_op_log_opType_56=Restaurar parâmetros
att_device_op_log_opType_68=Foto de usuário registrado
att_device_op_log_opType_69=Modificar fotos do usuário
att_device_op_log_opType_70=Modificar nome de usuário
att_device_op_log_opType_71=Modificar permissões de usuário
att_device_op_log_opType_76=Modificar IP das configurações de rede
att_device_op_log_opType_77=Modificar máscara de configurações de rede
att_device_op_log_opType_78=Modificar gateway de configurações de rede
att_device_op_log_opType_79=Modificar configurações de rede DNS
att_device_op_log_opType_80=Modifique a senha de configuração da conexão
att_device_op_log_opType_81=Modificar ID do dispositivo das configurações de conexão
att_device_op_log_opType_82=Modificar endereço do servidor em nuvem
att_device_op_log_opType_83=Modificar porta do servidor em nuvem
att_device_op_log_opType_87=Modificar configurações de registro de controle de acesso
att_device_op_log_opType_88=Modificar sinalizador de parâmetro de face
att_device_op_log_opType_89=Modifique o sinalizador de parâmetro de impressão digital
att_device_op_log_opType_90=Modifique o sinalizador de parâmetro da veia do dedo
att_device_op_log_opType_91=Modificar sinalizador de parâmetro palmprint
att_device_op_log_opType_92=Sinalizador de atualização de disco USB
att_device_op_log_opType_100=Modificar informações do cartão RF
att_device_op_log_opType_101=Registrar face
att_device_op_log_opType_102=Modificar permissões da equipe
att_device_op_log_opType_103=Excluir permissões pessoais
att_device_op_log_opType_104=Adicionar permissões de equipe
att_device_op_log_opType_105=Excluir registro de controle de acesso
att_device_op_log_opType_106=Excluir face
att_device_op_log_opType_107=Excluir fotos pessoais
att_device_op_log_opType_108=Modificar parâmetros
att_device_op_log_opType_109=Selecione WIFI SSID
att_device_op_log_opType_110=Habilitação de proxy
att_device_op_log_opType_111=Modificação de proxyip
att_device_op_log_opType_112=Modificação de porta proxy
att_device_op_log_opType_113=Modifique a senha da pessoa
att_device_op_log_opType_114=Modificar informações de face
att_device_op_log_opType_115=Modifique a senha do operador
att_device_op_log_opType_116=Retomar as configurações de controle de acesso
att_device_op_log_opType_117=Erro de entrada da senha do operador
att_device_op_log_opType_118=Bloqueio de senha do operador
att_device_op_log_opType_120=Modificar o comprimento dos dados do cartão Legic
att_device_op_log_opType_121=Registre a veia do dedo
att_device_op_log_opType_122=Modifique a veia do dedo
att_device_op_log_opType_123=Excluir veia dos dedos
att_device_op_log_opType_124=Registrar palm print
att_device_op_log_opType_125=Modificar a palma da mão
att_device_op_log_opType_126=Excluir impressão em palma
#操作对象描述
att_device_op_log_content_pin=ID do usuário:
att_device_op_log_content_alarm=Alarme:
att_device_op_log_content_alarm_reason=Motivo do alarme:
att_device_op_log_content_update_no=Modifique o número do item:
att_device_op_log_content_update_value=Modifique o valor:
att_device_op_log_content_finger_no=Número da impressão digital:
att_device_op_log_content_finger_size=Tamanho do modelo de impressão digital:
#=====================================================================
#工作流
att_flowable_datetime_to=Para
att_flowable_todomsg_leave=Deixar aprovação
att_flowable_todomsg_sign=Anexar aprovação de log
att_flowable_todomsg_overtime=Aprovação de horas extras
att_flowable_notifymsg_leave=Sair da notificação do aplicativo
att_flowable_notifymsg_sign=Anexar notificação de log
att_flowable_notifymsg_overtime=Notificação de horas extras
att_flowable_shift=Mudança:
att_flowable_hour=Hora
att_flowable_todomsg_trip=Aprovação de viagem de negócios
att_flowable_notifymsg_trip=Viagem de negócios
att_flowable_todomsg_out=Sair da aprovação
att_flowable_notifymsg_out=Sair da notificação
att_flow_apply=Inscrição
att_flow_applyTime=tempo de aplicação
att_flow_approveTime=Tempo de processamento
att_flow_operateUser=Revisor
att_flow_approve=Aprovação
att_flow_approveComment=Anotação
att_flow_approvePass=Resultados da aprovação
att_flow_status_processing=Aprovação
#=====================================================================
#biotime
att_h5_pers_personIdNull=O ID do funcionário não pode estar vazio
att_h5_attPlaceNull=O local do check-in não pode estar vazio
att_h5_attAreaNull=A área de frequencia não pode estar vazia
att_h5_pers_personNoExist=O número do funcionário não existe
att_h5_signRemarkNull=Comentários não podem estar vazios
att_h5_common_pageNull=Erro no parâmetro de paginação
att_h5_taskIdNotNull=O ID da tarefa não pode estar vazio
att_h5_auditResultNotNull=O resultado da aprovação não pode estar vazio
att_h5_latLongitudeNull=Longitude e latitude não podem estar vazias
att_h5_pers_personIsNull=O ID do funcionário não existe
att_h5_pers_personIsNotInArea=A pessoa não definiu a área
att_h5_mapApiConnectionsError=Erro de conexão da API do mapa
att_h5_googleMap=Google Map
att_h5_gaodeMap=Gaode Map
att_h5_defaultMap=Mapa padrão
att_h5_shiftTime=Tempo de troca
att_h5_signTimes=Tempo de reabastecimento
att_h5_enterKeyWords=Digite as palavras-chave:
att_h5_mapSet=Configurações do mapa de presença
att_h5_setMapApiAddress=Definir parâmetro do mapa
att_h5_MapSetWarning=A mudança do mapa fará com que o endereço de entrada do terminal móvel não corresponda à latitude e longitude; modifique com cuidado!
att_h5_mapSelect=Seleção de mapa
att_h5_persNoHire=O funcionário ainda não entrou na empresa no momento.
att_slef_apiKey=API-KEY
att_self_apiJsKey=API-JS-KEY
att_self_noComputeAtt=O comparecimento do dia ainda não foi contabilizado.
att_self_noSignRecord=Nenhum registro de marcação no dia
att_self_imageUploadError=Falha no upload da imagem
att_self_attSignAddressAreaIsExist=Já existem pontos de check-in na área
att_self_signRuleIsError=O tempo de reabastecimento atual não está dentro do tempo de reabastecimento permitido.
att_self_signAcrossDay=A programação de dias cruzados não pode ser assinada!
att_self_todaySignIsExist=Já existem patches adicionados hoje!
att_self_signSetting=Configuração de sinal
att_self_allowSign=Permitir sinal:
att_self_allowSignSuffix=Dias, registro de Presenças dentro de
att_self_onlyThisMonth=Só este mês
att_self_allowAcrossMonth=Permitir mês cruzado
att_self_thisTimeNoSch=Não há mudança no período atual!
att_self_revokeReason=Motivo da revogação:
att_self_revokeHint=Digite o motivo do cancelamento em 20 palavras para revisão
att_self_persSelfLogin=Login de autoatendimento do funcionário
att_self_isOpenSelfLogin=Se deve iniciar a entrada de login de autoatendimento dos funcionários
att_self_applyAndWorkTimeOverlap=O tempo de aplicação e o tempo de trabalho se sobrepõem
att_apply_DurationIsZero=A duração do aplicativo é 0, nenhum aplicativo é permitido
att_sign_mapWarn=O carregamento do mapa falhou. Verifique a conexão de rede e mapeie o valor KEY
att_admin_applyWarn=A operação falhou, há pessoas que não estão agendadas ou o horário do aplicativo não está dentro do escopo da programação! ({0})
att_self_getPhotoFailed=A imagem não existe
att_self_view=Visão
# 二维码
att_param_qrCodeUrl=URL do código QR
att_param_qrCodeUrlHref=Endereço do servidor: porta
att_param_appAttQrCode=Código QR do atendimento móvel
att_param_timingFrequency=Intervalo de tempo: 5-59 minutos ou 1-24 horas
att_sign_signTimeNotNull=O tempo do log de acréscimo não pode estar vazio
att_apply_overLastMonth=As inscrições começaram há mais de um mês
att_apply_withoutDetail=Sem detalhes do processo
att_flowable_noAuth=Use a conta de super administrador para visualizar
att_apply_overtimeOverMaxTimeLong=As horas extras são maiores que as horas extras máximas
# 考勤设置参数符号
att_other_arrive=√
att_other_late=Sair mais Tarde
att_other_early=Sair mais Cedo
att_other_absent=Ausente do trabalho
att_other_noSignIn=[não logou
att_other_noSignOff= falha ]
att_other_leave=Falso Sair
att_other_overtime=Hora Extra
att_other_off=Anexar periodo off
att_other_classes=Cartões Adicionais
att_other_trip=Viagem de Negócios
att_other_out=Lado de fora
att_other_incomplete=Ausência
att_other_outcomplete=Ausência
# 服务器下发命令
att_devCmd_submitTime=Tempo de envio
att_devCmd_returnedResult=Resultado retornado
att_devCmd_returnTime=Tempo de retorno
att_devCmd_content=Conteúdo do comando
att_devCmd_clearCmd=Limpar lista de comandos
# 实时点名
att_realTime_selectDept=Selecione um departamento
att_realTime_noSignPers=Não conectado
att_realTime_signPers=Conectado
att_realTime_signMonitor=Monitor de login
att_realTime_signDateTime=Hora de login
att_realTime_realTimeSet=Configuração da lista de chamada em tempo real
att_realTime_openRealTime=Habilitar chamada em tempo real
att_realTime_rollCallEnd=Fim da lista de chamada em tempo real
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=Programado
att_personSch_cycleSch=programação do ciclo
att_personSch_cleanCycleSch=Limpar programação do ciclo
att_personSch_cleanTempSch=Limpar agendamento temporário
att_personSch_personCycleSch=programação do ciclo da pessoa
att_personSch_deptCycleSch=Programação do ciclo do departamento
att_personSch_groupCycleSch=Programação do ciclo do grupo
att_personSch_personTempSch=agenda temporária de pessoal
att_personSch_deptTempSch=Agendamento temporário do departamento
att_personSch_groupTempSch=Agendamento temporário de grupo
att_personSch_checkGroupFirst=Verifique o grupo à esquerda ou as pessoas na lista à direita para operar!
att_personSch_sureDeleteGroup=Tem certeza que deseja deletar {0} e a programação correspondente ao grupo?
att_personSch_sch=Agenda
att_personSch_delSch=Excluir programação
#考勤计算
att_statistically_sureAllCalculate=Tem certeza de realizar o cálculo de presença para todo o pessoal?
#异常管理
att_exception_downTemplate=Baixar e importar modelo
att_exception_signImportTemplate=Modelo de importação de assinatura
att_exception_leaveImportTemplate=Sair do modelo de importação
att_exception_overtimeImportTemplate=Modelo de importação de horas extras
att_exception_adjustImportTemplate=Ajustar modelo de importação
att_exception_cellDefault=Campo não obrigatório
att_exception_cellRequired=Campo obrigatório
att_exception_cellDateTime=Campo obrigatório, o formato da hora é aaaa-MM-dd HH: mm: ss, como: 2020-07-07 08:30:00
att_exception_cellLeaveTypeName=Campo obrigatório, como: 'licença pessoal', 'licença casamento', 'licença maternidade', 'licença médica', 'licença anual', 'licença por luto', 'licença família', 'licença para amamentação', 'viagem de negócios', 'sair' Nome arrogante
att_exception_cellOvertimeSign=Campo obrigatório, como: 'horas extras normais', 'horas extras em dias de descanso', 'horas extras em feriados'
att_exception_cellAdjustType=Campo obrigatório, como: 'transferência off', 'make up class'
att_exception_cellAdjustDate=Campo obrigatório, o formato da hora é aaaa-MM-dd, como: 2020-07-07
att_exception_cellShiftName=Campo obrigatório quando o tipo de ajuste for ajuste de deslocamento
att_exception_refuse=Recusar
att_exception_end=Fim anormal
att_exception_delete=Excluir
att_exception_stop=Pause
#时间段
att_timeSlot_normalTimeAdd=Adicionar horário normal
att_timeSlot_elasticTimeAdd=Adicionar intervalo de tempo flexível
#班次
att_shift_addRegularShift=Adicionar turno regular
att_shift_addFlexibleShift=Adicionar turno flexível
#参数设置
att_param_notLeaveSetting=Configuração de cálculo não falsa
att_param_smallestUnit=Unidade mínima
att_param_workDay=dia de trabalho
att_param_roundingControl=controle de arredondamento
att_param_abort=Down (abandonar)
att_param_rounding=arredondamento
att_param_carry=Up (transportar)
att_param_reportSymbol=Símbolo de exibição do relatório
att_param_convertCountValid=Insira um número e permita apenas uma casa decimal
att_other_leaveThing=Pessoal
att_other_leaveMarriage=Casamento
att_other_leaveBirth=Maternidade
att_other_leaveSick=Doente
att_other_leaveAnnual=Anual
att_other_leaveFuneral=Funeral
att_other_leaveHome=Família
att_other_leaveNursing=Enfermagem
att_other_leavetrip=Negócios
att_other_leaveout=Fora
att_common_schAndRest=Dia de descanso
att_common_timeLongs=Duração
att_personSch_checkDeptOrPersFirst=Verifique o departamento à esquerda ou o pessoal da lista à direita para operar!
att_personSch_checkCalendarFirst=Selecione a data a ser agendada primeiro!
att_personSch_cleanCheck=Limpar a seleção
att_personSch_delTimeSlot=Limpar o intervalo de tempo selecionado
att_personSch_repeatTimeSlotNoAdd=Não adicione quando houver um intervalo de tempo repetitivo!
att_personSch_showSchInfo=Mostrar detalhes da programação
att_personSch_sureToCycleSch=Tem certeza de agendar {0} periodicamente?
att_personSch_sureToTempSch=Tem certeza que deseja agendar temporariamente {0}?
att_personSch_sureToCycleSchDeptOrGroup=Tem certeza de agendar todo o pessoal em {0}?
att_personSch_sureToTempSchDeptOrGroup=Tem certeza de agendar temporariamente todo o pessoal em {0}?
att_personSch_sureCleanCycleSch=Tem certeza de que deseja limpar a programação periódica de {0} de {1} a {2}?
att_personSch_sureCleanTempSch=Tem certeza de que deseja limpar a programação temporária de {0} de {1} a {2}?
att_personSch_sureCleanCycleSchDeptOrGroup=Tem certeza de limpar a programação do ciclo de {1} para {2} para todo o pessoal em {0}?
att_personSch_sureCleanTempSchDeptOrGroup=Tem certeza de que deseja limpar a programação temporária de todos os funcionários em {0} de {1} a {2}?
att_personSch_today=Hoje
att_personSch_timeSoltName=Nome do período de tempo
att_personSch_export=Exportar programação de pessoal
att_personSch_exportTemplate=Baixar modelo de agendamento temporário de equipe
att_personSch_import=Cronograma temporário de pessoal importado
att_personSch_tempSchTemplate=modelo de agendamento de equipe temporária
att_personSch_tempSchTemplateTip=Por favor, selecione o horário de início e término da programação e baixe o modelo de programação para essa data
att_personSch_opTip=Instruções de operação
att_personSch_opTip1=1, você pode usar o mouse para arrastar o período de tempo para uma única data no controle de calendário para agendamento.
att_personSch_opTip2=2, você pode clicar duas vezes em uma única data para agendar turnos no controle do calendário.
att_personSch_opTip3=3. No controle de calendário, mantenha o mouse pressionado e mova para selecionar várias datas para agendamento.
att_personSch_schRules=Regras de programação
att_personSch_schRules1=1. Programação periódica: cobre a programação anterior após a mesma data, independentemente de haver um cruzamento.
att_personSch_schRules2=2. Horário temporário: a mesma data tem um cruzamento, este último cobrirá o horário temporário anterior, se não houver cruzamento, ele existirá no mesmo horário.
att_personSch_schRules3=3. Período e temporário: Se a mesma data tiver uma interseção, o período será coberto temporariamente e, se não houver interseção, ele existirá ao mesmo tempo; se o tipo de turno periódico for inteligente, o período será coberto diretamente pelo turno temporário.
att_personSch_schStatus=Status da programação
#左侧菜单-排班管理
att_leftMenu_schDetails=Detalhes da programação
att_leftMenu_detailReport=Relatório de detalhes de comparecimento
att_leftMenu_signReport=Tabela de detalhes de inscrição
att_leftMenu_leaveReport=Formulário de Detalhes de Licença
att_leftMenu_abnormal=Tabela de presença anormal
att_leftMenu_yearLeaveSumReport=Relatório de licença anual
att_leave_maxFileCount=Você só pode adicionar 4 fotos no máximo
#时间段
att_timeSlot_add=Definir horário
att_timeSlot_select=Selecione um intervalo de tempo!
att_timeSlot_repeat=O intervalo de tempo "{0}" é repetido!
att_timeSlot_overlapping=O intervalo de tempo "{0}" se sobrepõe ao tempo de deslocamento "{1}"!
att_timeSlot_addFirst=Defina o intervalo de tempo primeiro!
att_timeSlot_notEmpty=O intervalo de tempo correspondente ao número pessoal {0} não pode estar vazio!
att_timeSlot_notExist=O período de tempo "{1}" correspondente ao número pessoal {0} não existe!
att_timeSlot_repeatEx=O período de tempo "{1}" e "{2}" correspondente ao número pessoal {0} se sobrepõe
att_timeSlot_importRepeat=Período de tempo "{1}" correspondente ao número pessoal {0} é repetido
att_timeSlot_importNotPin=Não existe pessoa com o número {0} no sistema!
att_timeSlot_elasticTimePeriod=Número de pessoal {0}, não é possível importar período de tempo flexível '{1}'!
#导入
att_import_overData=O número atual de itens importados é {0}, que excede o limite de 30.000, importe em lotes!
att_import_existIllegalType=O {0} importado tem um tipo ilegal!
#验证方式
att_verifyMode_0=Identificação automática
att_verifyMode_1=Apenas impressão digital
att_verifyMode_2=Verificação de ID de trabalho
att_verifyMode_3=Somente senha
att_verifyMode_4=Cartão apenas
att_verifyMode_5=Impressão digital ou senha
att_verifyMode_6=Impressão digital ou cartão
att_verifyMode_7=Cartão ou senha
att_verifyMode_8=ID de trabalho mais impressão digital
att_verifyMode_9=Impressão digital mais senha
att_verifyMode_10=Cartão mais impressão digital
att_verifyMode_11=Cartão mais senha
att_verifyMode_12=Impressão digital mais senha mais cartão
att_verifyMode_13=ID de trabalho mais impressão digital e senha
att_verifyMode_14=(ID de trabalho mais impressão digital) ou (cartão mais impressão digital)
att_verifyMode_15=Face
att_verifyMode_16=Rosto mais impressão digital
att_verifyMode_17=Rosto mais senha
att_verifyMode_18=Face mais cartão
att_verifyMode_19=Rosto mais impressão digital e cartão
att_verifyMode_20=Rosto mais impressão digital e senha
att_verifyMode_21=Veia do dedo
att_verifyMode_22=Veia do dedo mais senha
att_verifyMode_23=Veia do dedo mais cartão
att_verifyMode_24=Veia do dedo mais senha mais cartão
att_verifyMode_25=Palm print
att_verifyMode_26=Palm print plus card
att_verifyMode_27=Impressões palmares e rosto
att_verifyMode_28=Impressão da palma e impressão digital
att_verifyMode_29=Impressão da palma mais impressão digital mais rosto
# 工作流
att_flow_schedule=Progresso da auditoria
att_flow_schedulePass=(aprovado)
att_flow_scheduleNot=(não aprovado)
att_flow_scheduleReject=(rejeitado)
# 工作时长表
att_workTimeReport_total=Tempo total de trabalho
# 自动导出报表
att_autoExport_startEndTime=Horário de início e término
# 年假
att_annualLeave_setting=Configuração de saldo de férias anuais
att_annualLeave_settingTip1=Para usar a função de saldo de férias anuais, você precisa definir a hora de entrada para cada funcionário; quando a hora de entrada não for definida, as férias anuais restantes da tabela de saldo de férias anuais da equipe são exibidas como vazias.
att_annualLeave_settingTip2=Se a data atual for superior à data de emissão da compensação, esta modificação entrará em vigor no ano seguinte; se a data atual for inferior à data de emissão da compensação, quando a data de emissão da compensação for atingida, ela será compensada e as férias anuais serão reemitidas.
att_annualLeave_calculate=Licença anual de compensação e data de emissão
att_annualLeave_workTimeCalculate=Calcular de acordo com a proporção do tempo de trabalho
att_annualLeave_rule=Regra de férias anuais
att_annualLeave_ruleCountOver=O limite de número máximo definido foi atingido
att_annualLeave_years=Anos seniores
att_annualLeave_eachYear=Todos os anos
att_annualLeave_have=Sim
att_annualLeave_days=Dias de férias anuais
att_annualLeave_totalDays=Total de férias anuais
att_annualLeave_remainingDays=Férias anuais restantes
att_annualLeave_consecutive=A configuração da regra de férias anuais deve ser anos consecutivos
# 年假结余表
att_annualLeave_report=Balanço anual de férias
att_annualLeave_validDate=Data válida
att_annualLeave_useDays=Use {0} dias
att_annualLeave_calculateDays=Lançamento {0} dias
att_annualLeave_notEnough=Férias anuais insuficientes em {0}!
att_annualLeave_notValidDate={0} não está dentro da faixa válida de férias anuais!
att_annualLeave_notDays={0} não tem férias anuais!
att_annualLeave_tip1=Zhang San ingressou em 1º de setembro do ano passado
att_annualLeave_tip2=Configuração de saldo de férias anuais
att_annualLeave_tip3=A data de compensação e emissão é 1º de janeiro de cada ano; é calculada por arredondamento de acordo com a proporção de trabalho; se o tempo de serviço for menor ou igual a 1, haverá 3 dias de férias anuais, e se o tempo de serviço for menor ou igual a 3 anos, haverá 5 dias de férias anuais
att_annualLeave_tip4=Cálculo de férias anuais
att_annualLeave_tip5=Ano passado 09-01 ~ 12-31 aproveite 4/12 x 3=1,0 dias
att_annualLeave_tip6=Este ano 01-01 ~ 12-31 aproveite 4,0 dias (este ano 01-01 ~ 08-31 aproveite 8 / 12x3=2,0 dias   este ano 09-01 ~ 12-31 aproveite 4 / 12x5≈2,0 dias)
# att SDC
att_sdc_name=Equipamento de vídeo
att_sdc_wxMsg_firstData=Olá, você tem um aviso de check-in de presença
att_sdc_wxMsg_stateData=Sem sensação de sucesso no check-in de presença
att_sdc_wxMsg_remark=Lembrete: O resultado final do comparecimento está sujeito à página de detalhes do check-in.
# 时间段
att_timeSlot_conflict=O intervalo de tempo está em conflito com outros intervalos de tempo do dia
att_timeSlot_selectFirst=Selecione o intervalo de tempo
# 事件中心
att_eventCenter_sign=Login de atendimento
#异常管理
att_exception_classImportTemplate=Modelo de importação de classe
att_exception_cellClassAdjustType=Campo obrigatório, como: "{0}", "{1}", "{2}"
att_exception_swapDateDate=campo não obrigatório, o formato da hora é aaaa-MM-dd, como: 2020-07-07
#消息中心
att_message_leave=Aviso de atendimento {0}
att_message_leaveContent={0} enviado {1}, {2} hora é {3} ~ {4}
att_message_leaveTime=Tempo de licença
att_message_overtime=Aviso de comparecimento e horas extras
att_message_overtimeContent={0} hora extra enviada, e a hora extra é {1} ~ {2}
att_message_overtimeTime=Horário extra
att_message_sign=Sinal de aviso de presença
att_message_signContent={0} enviou um sinal suplementar, e o tempo do sinal suplementar é {1}
att_message_adjust=Aviso de ajuste de atendimento
att_message_adjustContent={0} enviou um ajuste, e a data do ajuste é {1}
att_message_class=Notificação de presença e turno
att_message_classContent=Conteúdo da aula
att_message_classContent0={0} enviou um turno, a data do turno é {1} e o turno é {2}
att_message_classContent1={0} enviou um turno, a data do turno é {1} e a data do turno é {2}
att_message_classContent2={0} ({1}) e {2} ({3}) classes de troca
#推送中心
att_pushCenter_transaction=registro de comparecimento
# 时间段
att_timeSlot_workTimeNotEqual=O tempo de trabalho não pode ser igual ao tempo de folga
att_timeSlot_signTimeNotEqual=A hora de início de sessão não pode ser igual à hora de fim de sessão
# 北向接口A
att_api_notNull={0} não pode estar vazio!
att_api_startDateGeEndDate=O horário de início não pode ser maior ou igual ao horário de término!
att_api_leaveTypeNotExist=A espécie falsa não existe!
att_api_imageLengthNot2000=O comprimento do endereço da imagem não pode exceder 2.000!
# 20221230新增国际化
att_personSch_workTypeNotNull=O tipo de trabalho correspondente ao número de pessoal {0} não pode estar vazio!
att_personSch_workTypeNotExist=O tipo de trabalho correspondente ao número de pessoal {0} não existe!
att_annualLeave_recalculate=Recalcular
# 20230530新增国际化
att_leftMenu_dailyReport=Relatório Diário de Presença
att_leftMenu_overtimeReport=Relatório de horas extras
att_leftMenu_lateReport=Relatório atrasado
att_leftMenu_earlyReport=Sair do Relatório Antecipado
att_leftMenu_absentReport=Relatório de Ausência
att_leftMenu_monthReport=Relatório Mensal de Presença
att_leftMenu_monthWorkTimeReport=Relatório Mensal de Tempo de Trabalho
att_leftMenu_monthCardReport=Relatório mensal do cartão
att_leftMenu_monthOvertimeReport=Relatório Mensal de Horas Extras
att_leftMenu_overtimeSummaryReport=Relatório resumido de horas extras da equipe
att_leftMenu_deptOvertimeSummaryReport=Relatório de resumo de horas extras do departamento
att_leftMenu_deptLeaveSummaryReport=Relatório de resumo de licença do departamento
att_annualLeave_calculateDay=Número de dias de férias anuais
att_annualLeave_adjustDay=Ajustar dias
att_annualLeave_sureSelectDept=Tem certeza que deseja realizar {0} operação no departamento selecionado?
att_annualLeave_sureSelectPerson=Tem certeza que deseja realizar {0} operação na pessoa selecionada?
att_annualLeave_calculateTip1=Ao calcular de acordo com o tempo de serviço: o cálculo das férias anuais é preciso para o mês, se o tempo de serviço for 10 anos e 3 meses, então 10 anos e 3 meses serão usados ​​para o cálculo;
att_annualLeave_calculateTip2=Quando a conversão não é baseada no tempo de serviço: o cálculo das férias anuais é preciso para o ano, se o tempo de serviço for 10 anos e 3 meses, então 10 anos serão usados ​​para o cálculo;
att_rule_isInCompleteTip=A prioridade é a mais alta quando nenhuma entrada ou saída é registrada como incompleta e atraso, saída antecipada, absenteísmo e válido
att_rule_absentTip=Quando nenhuma entrada ou saída é registrada como absenteísmo, a duração do absenteísmo é igual à duração das horas de trabalho menos a duração das licenças atrasadas e antecipadas
att_timeSlot_elasticTip1=0, o tempo efetivo é igual ao tempo real, sem absenteísmo
att_timeSlot_elasticTip2=Se o tempo real for maior que o horário de trabalho, o tempo efetivo é igual ao horário de trabalho, sem absenteísmo
att_timeSlot_elasticTip3=Se a duração real for menor que a duração do trabalho, a duração efetiva é igual à duração real e o absenteísmo é igual à duração do trabalho menos a duração real
att_timeSlot_maxWorkingHours=As horas de trabalho não podem ser superiores a
# 20231030
att_customReport=Relatório personalizado de assiduidade
att_customReport_byDayDetail=Detalhe por dia
att_customReport_byPerson=Resumo por pessoa
att_customReport_byDept=Resumo por Departamento
att_customReport_queryMaxRange=O intervalo máximo da consulta é de quatro meses.
# 20240630新增国际化
att_personSch_shiftWorkTypeTip1=1. Ao trabalhar horas extras em dias normais de trabalho / descanso, a prioridade de agendamento é menor do que a de feriados
att_personSch_shiftWorkTypeTip2=2. Quando o tipo de trabalho é horas extras durante feriados, a prioridade de agendamento é maior do que a de feriados
att_personVerifyMode=Método de verificação do pessoal
att_personVerifyMode_setting=Configuração do método de verificação
att_personSch_importCycSch=Programação do ciclo de pessoal de importação
att_personSch_cycSchTemplate=Modelo de programação do ciclo do pessoal
att_personSch_exportCycSchTemplate=Transferir o modelo de programação do ciclo de pessoal
att_personSch_scheduleTypeNotNull=O tipo Shift não pode estar vazio ou não existe!
att_personSch_shiftNotNull=O turno não pode estar vazio!
att_personSch_shiftNotExist=A mudança não existe!
att_personSch_onlyAllowOneShift=O agendamento regular só permite agendar um turno!
att_shift_attShiftStartDateRemark2=A semana em que a data de início do ciclo está localizada é a primeira semana; O mês em que a data de início do ciclo está localizada é o primeiro mês.
#打卡状态
att_cardStatus_setting=Configuração do status de frequência
att_cardStatus_name=Nome
att_cardStatus_value=Valor
att_cardStatus_alias=Alias
att_cardStatus_every_day=Todos os dias
att_cardStatus_by_week=Por semana
att_cardStatus_autoState=Estado automático
att_cardStatus_attState=Estado de frequência
att_cardStatus_signIn=Entrada
att_cardStatus_signOut=Saída
att_cardStatus_out=fora
att_cardStatus_outReturn=Retorno após a saída
att_cardStatus_overtime_signIn=Entrada de extra tempo
att_cardStatus_overtime_signOut=Saída de extra tempo
# 20241030新增国际化
att_leaveType_enableMaxDays=Ativar limite anual
att_leaveType_maxDays=Limite anual (dias)
att_leaveType_applyMaxDays=A solicitação não pode exceder {0} dias neste ano
att_param_overTimeSetting=Configuração do Nível de Horas Extras
att_param_overTimeLevel=Nível de Horas Extras (horas)
att_param_overTimeLevelEnable=Ativar Cálculo do Nível de Horas Extras
att_param_reportColor=Cor do relatório
# APP
att_app_signClientTip=Este dispositivo já foi registado por outra pessoa hoje
att_app_noSignAddress=Área de ponto não configurada, por favor contacte o administrador para configurar
att_app_notInSignAddress=Não chegou ao local de ponto, impossível fazer check-in
att_app_attendance=Minha frequência
att_app_apply=Pedido de frequência
att_app_approve=Minhas aprovações
# 20250530
att_node_leaderNodeExist=Nó de aprovação do líder imediato já existe
att_signAddress_init=Inicialização do mapa
att_signAddress_initTips=Digite a chave do mapa e inicie a configuração do mapa para selecionar um endereço