#系统名称 日语
att_systemName=勤怠管理システム6.0
#=====================================================================
#左侧菜单
att_module=勤怠管理
#左侧菜单-考勤设备
att_leftMenu_attendanceDevice=デバイス管理
att_leftMenu_device=出席デバイス
att_leftMenu_point=打刻ポイント
att_leftMenu_sign_address=モバイルサインインアドレス
att_leftMenu_adms_devCmd=サーバーによって発行されたコマンド
#左侧菜单-基础信息
att_leftMenu_basicInformation=基本情報
att_leftMenu_rule=ルール
att_leftMenu_base_rule=基本ルール
att_leftMenu_department_rule=部署ルール
att_leftMenu_holiday=休日
att_leftMenu_leaveType=休暇タイプ
att_leftMenu_timingCalculation=定時計算
att_leftMenu_autoExport=自動レポート
att_leftMenu_param=参数设置
#左侧菜单-班次管理
att_leftMenu_shiftManagement=シフト
att_leftMenu_timeSlot=タイムゾーン
att_leftMenu_shift=シフト
#左侧菜单-排班管理
att_leftMenu_scheduleManagement=シフト管理
att_leftMenu_group=グループ
att_leftMenu_groupPerson=グループユーザー
att_leftMenu_groupSch=グループシフト
att_leftMenu_deptSch=部署シフト
att_leftMenu_personSch=ユーザーシフト
att_leftMenu_tempSch=一時シフト
att_leftMenu_nonSch=シフト外ユーザー
#左侧菜单-异常管理
att_leftMenu_exceptionManagement=出席例外管理
att_leftMenu_sign=サインイン追加
att_leftMenu_leave=休暇
att_leftMenu_trip=出張
att_leftMenu_out=外出
att_leftMenu_overtime=残業
att_leftMenu_adjust=调休补班
att_leftMenu_class=シフト調整
#左侧菜单-统计报表
att_leftMenu_statisticalReport=出席統計レポート
att_leftMenu_manualCalculation=手動計算
att_leftMenu_transaction=トランザクション
att_leftMenu_dayCardDetailReport=デイリーアテンダンス
att_leftMenu_leaveSummaryReport=休暇サマリ
att_leftMenu_dayDetailReport=デイリーレポート
att_leftMenu_monthDetailReport=マンスリーレポート
att_leftMenu_monthStatisticalReport=月次統計レポート
att_leftMenu_deptStatisticalReport=部署別レポート
att_leftMenu_yearStatisticalReport=年次レポート
att_leftMenu_attSignCallRollReport=作業異常レポート
att_leftMenu_workTimeReport=作業時間レポート
#左侧菜单-设备操作日志
att_leftMenu_device_op_log=デバイス操作ログ
#左侧菜单-实时点名
att_leftMenu_realTime_callRoll=リアルタイムCallRoll
#=====================================================================
#公共
att_common_person=ユーザー
att_common_pin=ID
att_common_group=グループ
att_common_dept=部署
att_common_symbol=記号
att_common_deptNo=部署No.
att_common_deptName=部署名
att_common_groupNo=グループNo.
att_common_groupName=グループ名
att_common_operateTime=操作時間
att_common_operationFailed=操作失敗
att_common_id=ID
att_common_deptId=部署ID
att_common_groupId=グループID
att_common_deviceId=デバイスID
att_person_pin=ユーザーNo.
att_person_name=名
att_person_lastName=苗字
att_person_internalCard=カード番号
att_person_attendanceMode=出席モード
att_person_normalAttendance=通常の出席
att_person_noPunchCard=パンチカードなし
att_common_attendance=出勤
att_common_attendance_hour=出勤（時）
att_common_attendance_day=出勤（日）
att_common_late=遅刻
att_common_early=早退
att_common_overtime=残業
att_common_exception=例外
att_common_absent=欠勤
att_common_leave=休暇
att_common_trip=出張
att_common_out=外出
att_common_staff=ユーザー
att_common_superadmin=スーパー管理者
att_common_msg=SMSコンテンツ
att_common_min=SMS保留時間（分）
att_common_letterNumber=数字または文字のみを入力してください！
att_common_relationDataCanNotDel=関連データは削除できません！
att_common_relationDataCanNotEdit=関連するデータは変更できません！
att_common_needSelectOneArea=エリアを選択してください！
att_common_neesSelectPerson=ユーザーを選択してください！
att_common_nameNoSpace=名前にスペースを含めることはできません！
att_common_digitsValid=小数点以下2桁以下の数字のみを入力できます！
att_common_numValid=数字のみ入力できます！
#=====================================================================
#工作面板
att_dashboard_worker=ワーカホリック
att_dashboard_today=今日の出社
att_dashboard_todayCount=今日の出社セグメント統計
att_dashboard_exceptionCount=例外統計（今月）
att_dashboard_lastWeek=先週
att_dashboard_lastMonth=先月
att_dashboard_perpsonNumber=総数
att_dashboard_actualNumber=チェックイン済み
att_dashboard_notArrivedNumber=未チェックイン
att_dashboard_attHour=作業時間
#区域
#设备
att_op_syncDev=ソフトウェアデータをデバイスに同期
att_op_account=勤怠データの校正
att_op_check=データ再アップロード
att_op_deleteCmd=デバイスコマンドをクリア
att_op_dataSms=パブリックメッセージ
att_op_clearAttPic=アテンダンスフォトをクリア
att_op_clearAttLog=アクセス履歴をクリア
att_device_waitCmdCount=実行コマンド
att_device_status=状態有効
att_device_register=登録機
att_device_isRegister=登録機ですか？
att_device_existNotRegDevice=登録機ではないので、データは利用できません！
att_device_fwVersion=ファームウェアバージョン
att_device_transInterval=更新間隔（分）
att_device_cmdCount=サーバと通信するコマンドの最大数
att_device_delay=記録時間クエリ（秒）
att_device_timeZone=タイムゾーン
att_device_operationLog=操作ログ
att_device_registeredFingerprint=指紋登録
att_device_registeredUser=ユーザー登録
att_device_fingerprintImage=指紋写真
att_device_editUser=ユーザー編集
att_device_modifyFingerprint=指紋変更
att_device_faceRegistration=顔登録
att_device_userPhotos=ユーザー写真
att_device_attLog=アクセスレコードをアップロードしますか？
att_device_operLog=ユーザー情報をアップロードしますか？
att_device_attPhoto=アテンダンスフォトをアップロードしますか？
att_device_isOnLine=オンラインかどうか
att_device_InputPin=ユーザーNo.入力
att_device_getPin=特定ユーザーデータ取得
att_device_separatedPin=コンマで区切られた複数のユーザーNo.
att_device_authDevice=許可されたデバイス
att_device_disabled=次のデバイスは無効になっており、操作できません！
att_device_autoAdd=新しいデバイスを自動的に追加します
att_device_receivePersonOnlyDb=データベース内の個人データのみを受信する
att_devMenu_control=デバイス管理
att_devMenu_viewOrGetInfo=情報の表示と取得
att_devMenu_clearData=デバイスデータクリア
att_device_disabledOrOffline=デバイスが有効になっていないかオフラインになっており、操作できません！
att_device_areaStatus=デバイスエリアのステータス
att_device_areaCommon=エリアは正常です
att_device_areaEmpty=エリアは空です
att_device_isRegDev=タイムゾーンまたは登録マシンのステータスの変更は、デバイスの再起動後に有効になります。
att_device_canUpgrade=次のデバイスをアップグレードできます
att_device_offline=次のデバイスはオフラインであり、操作できません。
att_device_oldProtocol=古いプロトコル
att_device_newProtocol=新しいプロトコル
att_device_noMoreTwenty=古いプロトコルデバイスのファームウェアアップグレードパッケージは20Mを超えることはできません
att_device_transferFilesTip=ファームウェアテストが成功しました、ファイルを転送します
att_op_clearAttPers=機器担当者をクリアする
#区域人员
att_op_forZoneAddPers=エリアユーザー設定
att_op_dataUserSms=個人SMS
att_op_syncPers=デバイスに再同期
att_areaPerson_choiceArea=エリアを選択してください！
att_areaPerson_byAreaPerson=エリアスタッフ別
att_areaPerson_setByAreaPerson=地域スタッフによる設定
att_areaPerson_importBatchDel=バッチ削除のインポート
att_areaPerson_syncToDevSuccess=操作が成功しました！ コマンドが発行されるまでお待ちください。
att_areaPerson_personId=ユーザーID
att_areaPerson_areaId=エリアID
att_area_existPerson=ユーザーがいます！
att_areaPerson_notice1=エリアまたはユーザーを同時に空にすることはできません！
att_areaPerson_notice2=ユーザーまたはエリアが見つかりませんでした！
att_areaPerson_notice3=デバイスがありません！
att_areaPerson_addArea=エリア追加
att_areaPerson_delArea=エリア削除
att_areaPerson_persNoExit=人は存在しません
att_areaPerson_importTip1=インポートされた人が人事モジュールにすでに存在することを確認してください
att_areaPerson_importTip2=バッチインポーターはデバイスに自動的に配信されないため、手動で同期する必要があります
att_areaPerson_addAreaPerson=エリアの人を追加
att_areaPerson_delAreaPerson=エリア担当者を削除
att_areaPerson_importDelAreaPerson=エリア担当者のインポートと削除
att_areaPerson_importAreaPerson=エリアパーソンのインポート
#考勤点
att_attPoint_name=打刻ポイント名
att_attPoint_list=打刻ポイントリスト
att_attPoint_deviceModule=デバイスモジュール
att_attPoint_acc=入退室
att_attPoint_park=駐車
att_attPoint_ins=広告
att_attPoint_pid=身分証明書
att_attPoint_vms=ビデオ
att_attPoint_psg=通路側
att_attPoint_doorList=ドアリスト
att_attPoint_deviceList=デバイスリスト
att_attPoint_channelList=チャネルリスト
att_attPoint_gateList=ゲートリスト
att_attPoint_recordTypeList=プルレコードタイプ
att_attPoint_door=対応するドアを選択してください。
att_attPoint_device=対応するデバイスを選択してください。
att_attPoint_gate=対応するゲートを選択してください
att_attPoint_normalPassRecord=通常のパスレコード
att_attPoint_verificationRecord=検証記録
att_person_attSet=勤怠設定
att_attPoint_point=打刻ポイントを選択してください。
att_attPoint_count=打刻ポイントが足りませんので、失敗しました。
att_attPoint_notSelect=モジュールが設置されていません。
att_attPoint_accInsufficientPoints=打刻ポイント不足！
att_attPoint_parkInsufficientPoints=打刻ポイント不足！
att_attPoint_insInsufficientPoints=打刻ポイント不足！
att_attPoint_pidInsufficientPoints=打刻ポイント不足！
att_attPoint_doorOrParkDeviceName=ドアまたは駐車デバイス名
att_attPoint_vmsInsufficientPoints=打刻ポイント不足！
att_attPoint_psgInsufficientPoints=チャンネルの出席ポイントが不足しています！
att_attPoint_delDevFail=デバイスは打刻ポイントに使用されていて、削除できません！
att_attPoint_pullingRecord=出席ポイントは定期的にレコードを取得しています。お待ちください！
att_attPoint_lastTransactionTime=最後のデータプル時間
att_attPoint_masterDevice=マスターデバイス
att_attPoint_channelName=チャネル名
att_attPoint_cameraName=カメラ名
att_attPoint_cameraIP=カメラIP
att_attPoint_channelIP=チャネルIP
att_attPoint_gateNumber=ゲート番号
att_attPoint_gateName=ゲート名
#APP考勤签到地址
att_signAddress_address=アドレス
att_signAddress_longitude=経度
att_signAddress_latitude=緯度
att_signAddress_range=有効範囲
att_signAddress_rangeUnit=単位：メートル
#=====================================================================
#规则
att_rule_baseRuleSet=基本ルール設定
att_rule_countConvertSet=計算設定
att_rule_otherSet=他設定
att_rule_baseRuleSignIn=チェックインルール
att_rule_baseRuleSignOut=チェックアウトルール
att_rule_earliestPrinciple=初期原則
att_rule_theLatestPrinciple=後期原則
att_rule_principleOfProximity=最も近い原則
att_rule_baseRuleShortestMinutes=最小期間は（最小10分）より長くする必要があります。
att_rule_baseRuleLongestMinutes=最大期間は（最大1440分）未満にする必要があります。
att_rule_baseRuleLateAndEarly=遅刻/早退を欠勤とする
att_rule_baseRuleCountOvertime=残業統計
att_rule_baseRuleFindSchSort=シフトレコード検索
att_rule_groupGreaterThanDepartment=グループ->部署
att_rule_departmentGreaterThanGroup=部署->グループ
att_rule_baseRuleSmartFindClass=インテリジェントマッチングシフトルール
att_rule_timeLongest=最長作業時間
att_rule_exceptionLeast=最低限の例外
att_rule_baseRuleCrossDay=日を跨ぐシフトの勤怠計算結果
att_rule_firstDay=1日目
att_rule_secondDay=2日目
att_rule_baseRuleShortestOvertimeMinutes=最小残業時間（分）
att_rule_baseRuleMaxOvertimeMinutes=最大残業時間（分）
att_rule_baseRuleElasticCal=フレックス期間計算
att_rule_baseRuleTwoPunch=2回翳すごとの累積時間
att_rule_baseRuleStartEnd=最初と最後のカードを翳した時間の計算
att_rule_countConvertHour=時間変換ルール
att_rule_formulaHour=式：時間=分/60
att_rule_countConvertDay=日変換ルール
att_rule_formulaDay=式：日=分/1日に仕事をする分数
att_rule_inFormulaShallPrevail=式で計算された結果を基準として取得。
att_rule_remainderHour=余りが
att_rule_oneHour=分以上ならば、1時間とする。下回る場合30分とするか、無視する。
att_rule_halfAnHour=分以上ならば、30分とする。下回る場合は無視する。
att_rule_remainderDay=商数が次の作業分数以上
att_rule_oneDay={}、1日として計算する。そうでない場合は半日として計算するか無視する。
att_rule_halfAnDay=%、半日として計算され、それ以外は無視する。
att_rule_countConvertAbsentDay=不在日数変換ルール
att_rule_markWorkingDays=出勤日として計算
att_rule_countConvertDecimal=小数点の正確な桁
att_rule_otherSymbol=レポートのアクセス結果シンボル設定
att_rule_arrive=予定/実際
att_rule_noSignIn=チェックインなし
att_rule_noSignOff=チェックアウトなし
att_rule_off=休暇の調整
att_rule_class=出勤日の追加
att_rule_shortLessLong=出席時間表の時間は、最長出席時間表の時間を超えることはできません。
att_rule_symbolsWarning=アクセスレポートにシンボルを設定する必要があります。
att_rule_reportSettingSet=レポート導出設定
att_rule_shortDateFormat=日付フォーマット
att_rule_shortTimeFormat=時間フォーマット
att_rule_baseRuleSignBreakTime=休憩中に打刻するかどうか
att_leftMenu_custom_rule=カスタムルール
att_custom_rule_already_exist={0}にカスタムルールがあります!
att_add_group_custom_rule=グループルール追加
att_custom_rule_type=ルールタイプ
att_rule_type_group=グループルール
att_rule_type_dept=部署ルール
att_custom_rule_orgNames=使用オブジェクト
att_rult_maxOverTimeType1=制限がありません
att_rult_maxOverTimeType2=本週
att_rult_maxOverTimeType3=本月
att_rule_countConvertDayRemark1=例：実効労働時間は500分であり、毎日の労働時間は480分である必要があり、結果は500/480 = 1.25で、最後の小数は1.2のままです。
att_rule_countConvertDayRemark2=例：実効労働時間は500分であり、毎日の労働時間は480分である必要があり、結果は500/480 = 1.25であり、1.25>0.8は1日として計算されます。
att_rule_countConvertDayRemark3=例：実効労働時間は300分、1日の労働時間は480分で、結果は300/480 = 0.625、0.2<0.625 <0.8は半日と見なされます。
att_rule_countConvertDayRemark4=日換算のベンチマーク：期間内の「稼働日として記録」が機能しません。
att_rule_countConvertDayRemark5=就業日として記録：欠勤日数に制限され、各期間に欠勤がある限り、欠勤の長さはその期間の就業日数に従って計算されます。
att_rule_baseRuleSmartFindRemark1=最長時間：その日のカードに従って、その日の各シフトに対応する勤務時間を計算し、その日の有効な勤務時間が最も長いシフトを見つけます。
att_rule_baseRuleSmartFindRemark2=最小の異常：その日のカードポイントに従って、その日の各シフトに対応する異常の数を計算し、その日の最小の異常を伴うシフトを見つけて勤務時間を計算します。
att_rule_baseRuleHourValidator=半時間の判定時間は、1時間の判定時間以上にすることはできません！
att_rule_baseRuleDayValidator=半日の判定時間は、1日の判定時間以上にすることはできません！
att_rule_overtimeWarning=最大残業時間は、最小残業時間より短くすることはできません！
att_rule_noSignInCountType=チェックイン無しとは：
att_rule_absent=欠勤
att_rule_earlyLeave=早退
att_rule_noSignOffCountType=チェックアウト無しとは：
att_rule_minutes=分
att_rule_noSignInCountLateMinute=チェックイン無しとは遅刻分です。
att_rule_noSignOffCountEarlyMinute=チェックアウト無しとは早退分です。
att_rule_incomplete=不完全
att_rule_noCheckInIncomplete=チェックイン無しで不完全です。
att_rule_noCheckOutIncomplete=チェックアウト無しで不完全です。
att_rule_lateMinuteWarning=遅くチェックインされなかった分数は、0より大きく、最長の出席期間未満である必要があります
att_rule_earlyMinuteWarning=早期出発の分は0より大きく、最長の出席期間より短い必要があるため、サインオフは記録されません
att_rule_baseRuleNoSignInCountLateMinuteRemark=チェックインしない場合は遅延としてカウントされ、チェックインしない場合はN分間遅延としてカウントされます。
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=サインオフしない場合は早期出発として記録され、サインオフしない場合は早期出発として記録されますN分#节假日
#节假日
att_holiday_placeholderNo=「H1」などの「H」で開始することをお勧めします。
att_holiday_placeholderName=「年」+「祭日名」などで名前を付けることをお勧めします。例: 「2017勤労感謝の日」。
att_holiday_dayNumber=日数
att_holiday_validDate_msg=この時期は祝日があります
#假种
att_leaveType_leaveThing=臨時休暇
att_leaveType_leaveMarriage=結婚休暇
att_leaveType_leaveBirth=産休
att_leaveType_leaveSick=病欠
att_leaveType_leaveAnnual=有給休暇
att_leaveType_leaveFuneral=忌引休暇
att_leaveType_leaveHome=帰省休暇
att_leaveType_leaveNursing=育児休暇
att_leaveType_isDeductWorkLong=労働時間を差し引くかどうか
att_leaveType_placeholderNo=「L1」などの「L」で開始することをお勧めします。
att_leaveType_placeholderName=「結婚式休暇」などの「休暇」で終わることをお勧めします。
#定时计算
att_timingcalc_timeCalcFrequency=計算間隔
att_timingcalc_timeCalcInterval=タイミング計算時間
att_timingcalc_timeSet=タイミング計算時間設定
att_timingcalc_timeSelect=時間を選択してください。
att_timingcalc_optionTip=少なくとも1つの有効な毎日の出勤計算を保持します。
#自动导出报表
att_autoExport_reportType=レポートタイプ
att_autoExport_fileType=ファイルタイプ
att_autoExport_fileName=ファイル名
att_autoExport_fileDateFormat=日付フォーマット
att_autoExport_fileTimeFormat=時間フォーマット
att_autoExport_fileContentFormat=コンテンツフォーマット
att_autoExport_fileContentFormatTxt=例：「部署名」 00 「ユーザーPin」 01 「ユーザー名」 02 「アクセス日時」 03
att_autoExport_timeSendFrequency=送信頻度
att_autoExport_timeSendInterval=時間送信間隔
att_autoExport_emailType=メール受信者タイプ
att_autoExport_emailRecipients=メール受信者
att_autoExport_emailAddress=メールアドレス
att_autoExport_emailExample=例：123 @ foxmail.com、456 @ foxmail.com
att_autoExport_emailSubject=メール件名
att_autoExport_emailContent=メール本文
att_autoExport_field=フィールド
att_autoExport_fieldName=フィールド名
att_autoExport_fieldCode=フィールドNo.
att_autoExport_reportSet=レポート設定
att_autoExport_timeSet=メール送信時間設定
att_autoExport_emailSet=メール設定
att_autoExport_emailSetAlert=メールアドレスを入力してください
att_autoExport_emailTypeSet=受信者設定
att_autoExport_byDay=毎日
att_autoExport_byMonth=毎月
att_autoExport_byPersonSet=ユーザーから設定
att_autoExport_byDeptSet=部署から設定
att_autoExport_byAreaSet=エリアから設定
att_autoExport_emailSubjectSet=件名
att_autoExport_emailContentSet=本文
att_autoExport_timePointAlert=正しい送信時刻を選択してください。
att_autoExport_lastDayofMonth=月末
att_autoExport_firstDayofMonth=月初
att_autoExport_dayofMonthCheck=特定の日付
att_autoExport_dayofMonthCheckAlert=特定の日付を選択してください。
att_autoExport_chooseDeptAlert=部署を選択してください！
att_autoExport_sendFormatSet=送信方法設定
att_autoExport_sendFormat=送信方法
att_autoExport_mailFormat=メール送信
att_autoExport_ftpFormat=FTP送信
att_autoExport_sftpFormat=SFTP送信
att_autoExport_ftpUrl=FTPサーバーIP
att_autoExport_ftpPort=FTPサーバーポート
att_autoExport_ftpTimeSet=FTP送信時間設定
att_autoExport_ftpParamSet=FTPパラメーター設定
att_autoExport_ftpUsername=FTPユーザー名
att_autoExport_ftpPassword=FTPパスワード
att_autoExport_correctFtpParam=正しいFTPパラメーターを入力してください。
att_autoExport_correctFtpTestParam=接続をテストして、通信が正常であることを確認してください。
att_autoExport_inputFtpUrl=サーバーIPを入力してください。
att_autoExport_inputFtpPort=サーバーポートを入力してください。
att_autoExport_ftpSuccess=正常に接続しました。
att_autoExport_ftpFail=パラメーターの設定が正しいかどうかを確認してください。
att_autoExport_validFtp=有効なサーバーIPを入力してください。
att_autoExport_validPort=有効なサーバーポートを入力してください。
att_autoExport_selectExcelTip=ファイルタイプとしてEXCELを選択すると、コンテンツ形式はすべてのフィールドになります！
#=====================================================================
#时间段
att_timeSlot_periodType=タイムテーブルタイプ
att_timeSlot_normalTime=標準タイムテーブル
att_timeSlot_elasticTime=フレックスタイムテーブル
att_timeSlot_startSignInTime=チェックイン開始時間
att_timeSlot_toWorkTime=チェックイン時間
att_timeSlot_endSignInTime=チェックイン終了時刻
att_timeSlot_allowLateMinutes=遅刻(分)を許可する
att_timeSlot_isMustSignIn=チェックイン必須
att_timeSlot_startSignOffTime=チェックアウト開始時間
att_timeSlot_offWorkTime=チェックアウト時間
att_timeSlot_endSignOffTime=チェックアウト終了時間
att_timeSlot_allowEarlyMinutes=早退(分)を許可する
att_timeSlot_isMustSignOff=チェックアウト必須
att_timeSlot_workingHours=勤怠時間(分)
att_timeSlot_isSegmentDeduction=パラグラフ間を差し引くかどうか
att_timeSlot_startSegmentTime=セグメント間の開始時間
att_timeSlot_endSegmentTime=終了セグメント間の時間
att_timeSlot_interSegmentDeduction=控除時間(分)
att_timeSlot_markWorkingDays=出勤日として計算
att_timeSlot_isAdvanceCountOvertime=自動残業（チェックイン遅延）
att_timeSlot_signInAdvanceTime=自動残業終了時間（チェックイン）
att_timeSlot_isPostponeCountOvertime=自動残業（チェックアウト遅延）
att_timeSlot_signOutPosponeTime=自動残業開始時間（チェックアウト）
att_timeSlot_isCountOvertime=残業として計算
att_timeSlot_timeSlotLong=タイムテーブルの範囲は次のとおりです：
att_timeSlot_alertStartSignInTime=チェックイン開始時間は、チェックイン時間より早くなければなりません。
att_timeSlot_alertEndSignInTime=チェックイン終了時間は、チェックイン時間より遅くなければなりません。
att_timeSlot_alertStartSignInAndEndSignIn=チェックアウト開始時間は、チェックアウト時間より早くなければなりません。
att_timeSlot_alertStartSignOffTime=残業開始時間は、チェックアウト時間より早くすることはできません。
att_timeSlot_alertEndSignOffTime=残業開始時間は、終了チェックアウト時間より遅くすることはできません。
att_timeSlot_alertStartUnequalEnd=就業日数を0より小さくすることはできません。
att_timeSlot_alertStartSegmentTime=控除された時間は0より小さくなることはできません。
att_timeSlot_alertStartAndEndTime=開始チェックアウト時間は終了チェックイン時間に同じにすることはできません。
att_timeSlot_alertEndAndoffWorkTime=時間は23時間を超えることはできません。
att_timeSlot_alertToWorkLesserAndEndTimeLesser=59分を超えることはできません。
att_timeSlot_alertLessSignInAdvanceTime=アーリーチェックイン時間は作業開始時間より短くなければなりません
att_timeSlot_alertMoreSignInAdvanceTime=アーリーチェックイン時間はチェックイン開始時間より遅くなければなりません
att_timeSlot_alertMoreSignOutPosponeTime=レイトチェックアウト時間は、チェックアウト終了時間より短くなければなりません。
att_timeSlot_alertLessSignOutPosponeTime=レイトチェックアウト時間は作業終了時間より遅くなければなりません
att_timeSlot_time=正しい時刻形式を入力してください。
att_timeSlot_alertMarkWorkingDays=就業日数を空にすることはできません！
att_timeSlot_placeholderNo=「T01」など、「T」で始めることをお勧めします。
att_timeSlot_placeholderName=「T」で開始するか、「期間」で終了することをお勧めします。
att_timeSlot_beforeToWork=出勤前
att_timeSlot_afterToWork=出勤後
att_timeSlot_beforeOffWork=退勤前
att_timeSlot_afterOffWork=退勤後
att_timeSlot_minutesSignInValid=数分内にチェックイン有効
att_timeSlot_toWork=出勤
att_timeSlot_offWork=退勤
att_timeSlot_minutesSignInAsOvertime=数分前にチェックインは残業として計算
att_timeSlot_minutesSignOutAsOvertime=数分後にチェックアウトは残業として計算
att_timeSlot_minOvertimeMinutes=最小残業時間
att_timeSlot_enableWorkingHours=労働時間を有効にするかどうか
att_timeSlot_eidtTimeSlot=期間を編集
att_timeSlot_browseBreakTime=休憩期間閲覧
att_timeSlot_addBreakTime=休憩期間追加
att_timeSlot_enableFlexibleWork=フレックスタイム有効
att_timeSlot_advanceWorkMinutes=早い出勤可能
att_timeSlot_delayedWorkMinutes=遅い出勤可能
att_timeSlot_advanceWorkMinutesValidMsg1=「出勤前」の分は「早い出勤可能」よりも大きい
att_timeSlot_advanceWorkMinutesValidMsg2=「早い出勤可能」の分は「出勤前」よりも小さい
att_timeSlot_advanceWorkMinutesValidMsg3=「早い出勤可能」の分は「数分前にチェックインは残業として計算」よりも小さい
att_timeSlot_advanceWorkMinutesValidMsg4=「数分前にチェックインは残業として計算」の分は「早い出勤可能」よりも大きい
att_timeSlot_delayedWorkMinutesValidMsg1=作業後の分数が、延期できる分数よりも大きい
att_timeSlot_delayedWorkMinutesValidMsg2=「遅い出勤可能」の分は「退勤後」よりも小さい
att_timeSlot_delayedWorkMinutesValidMsg3=「遅い出勤可能」の分は「数分後にチェックアウトは残業として計算」よりも小さい
att_timeSlot_delayedWorkMinutesValidMsg4=「数分後にチェックアウトは残業として計算」の分は「遅い出勤可能」よりも大きい
att_timeSlot_allowLateMinutesValidMsg1=「遅刻可能」の分は「出勤後」よりも小さい
att_timeSlot_allowLateMinutesValidMsg2=「出勤後」の分は「遅刻可能」よりも大きい
att_timeSlot_allowEarlyMinutesValidMsg1=「早退可能」の分は「退勤前」よりも小さい
att_timeSlot_allowEarlyMinutesValidMsg2=「退勤前」の分は「早退可能」よりも大きい
att_timeSlot_timeOverlap={0}と{1}の時間が重複しています。選択した期間を変更してください！
att_timeSlot_atLeastOne=少なくとも1つの休憩タイムスロット！
att_timeSlot_mostThree=最大3つの休憩タイムスロット！
att_timeSlot_canNotEqual=休止期間の開始時刻を終了時刻と等しくすることはできません！
att_timeSlot_shoudInWorkTime=休憩時間が営業時間内であることを確認してください。
att_timeSlot_repeatBreakTime=休憩時間を繰り返します！
att_timeSlot_toWorkLe=出勤時間は選択した休憩時間の最小開始時間よりも早い：
att_timeSlot_offWorkGe=退勤時間は選択した休憩時間の最大終了時間よりも遅い：
att_timeSlot_crossDays_toWork=休憩時間の最小開始時間は期間内にしてください：
att_timeSlot_crossDays_offWork=休憩時間の最大終了時間は期間内にしてください：
att_timeSlot_allowLateMinutesRemark=「出勤時間」から「遅刻可能」までにチェックインできます。
att_timeSlot_allowEarlyMinutesRemark=「退勤時間」から「早退可能」までにチェックアウトできます。
att_timeSlot_isSegmentDeductionRemark=期間内の残りの期間を差し引くかどうか
att_timeSlot_attEnableFlexibleWorkRemark1=フレックスタイムで遅刻・早退可能分を設定できません。
att_timeSlot_afterToWorkRemark=「出勤後」の分は「遅い出勤可能」と同じです。
att_timeSlot_beforeOffWorkRemark=「退勤前」の分は「早い出勤可能」と同じです。
att_timeSlot_attEnableFlexibleWorkRemark2=「退勤後」の分は「退勤時間」＋「遅い出勤」の分より大きい
att_timeSlot_attEnableFlexibleWorkRemark3=「早い出勤可能」の分は「数分前にチェックインは残業として計算」より小さい
att_timeSlot_attEnableFlexibleWorkRemark4=「遅い出勤可能」の分は「数分後にチェックアウトは残業として計算」より小さい
att_timeSlot_attBeforeToWorkAsOvertimeRemark=例：9時のシフトの場合は、出勤の60分前に残業をチェックインし、8時までに残業をチェックインします
att_timeSlot_attAfterOffWorkAsOvertimeRemark=例：18:00に仕事を辞め、60分後にサインオフして残業し、その後19:00からチェックアウト時間まで残業します
att_timeSlot_longTimeValidRemark=作業後の有効なカード署名時間と作業前の有効なカード署名時間は、期間内で重複することはできません。
att_timeSlot_advanceWorkMinutesValidMsg5=チェックイン前の有効な分数は、事前に作業できる分数よりも大きくする必要があります
att_timeSlot_advanceWorkMinutesValidMsg6=事前に作業する時間は、作業する前にサインインする有効な時間より短くなければなりません
att_timeSlot_delayedWorkMinutesValidMsg5=チェックイン後の有効な分数が、仕事を延期できる分数を超えています
att_timeSlot_delayedWorkMinutesValidMsg6=仕事を延期できる分数は、仕事後にサインインする有効な分数よりも短くする必要があります
att_timeSlot_advanceWorkMinutesValidMsg7=作業前のチェックイン時間は、前日の作業後のチェックアウト時間と重複することはできません。
att_timeSlot_delayedWorkMinutesValidMsg7=作業後のチェックアウト時間は、翌日の作業前のチェックイン時間と重複することはできません。
att_timeSlot_maxOvertimeMinutes=最大残業時間を制限する
#班次
att_shift_basicSet=基本設定
att_shift_advancedSet=詳細設定
att_shift_type=シフトタイプ
att_shift_name=シフト名
att_shift_regularShift=レギュラーシフト
att_shift_flexibleShift=フレックスシフト
att_shift_color=色
att_shift_periodicUnit=単位
att_shift_periodNumber=サイクル
att_shift_startDate=開始日
att_shift_startDate_firstDay=サイクル開始日
att_shift_isShiftWithinMonth=1ヶ月のサイクルシフト
att_shift_attendanceMode=アクセスモード
att_shift_shiftNormal=レギュラーシフトに従ってアクセス
att_shift_oneDayOneCard=1日のうち任意の時間に1度アクセス
att_shift_onlyBrushTime=アクセスした時間のみを計算
att_shift_notBrushCard=アクセス不要
att_shift_overtimeMode=残業モード
att_shift_autoCalc=コンピュータ自動計算
att_shift_mustApply=残業を適用する
att_shift_mustOvertime=残業しないと欠勤として計算
att_shift_timeSmaller=自動計算と残業レシート時間の短い方
att_shift_notOvertime=残業として計算しない
att_shift_overtimeSign=残業タイプ
att_shift_normal=通常営業日
att_shift_restday=休暇日
att_shift_timeSlotDetail=タイムテーブル詳細
att_shift_doubleDeleteTimeSlot=シフト期間をダブルクリックすると、期間を削除することができます
att_shift_addTimeSlot=タイムテーブル追加
att_shift_cleanTimeSlot=タイムテーブルクリア
att_shift_NO=第
att_shift_notAll=全選択しません
att_shift_notTime=タイムテーブルの詳細チェックボックスをチェックできない場合は、タイムテーブルに重複があることを示します。
att_shift_notExistTime=このタイムテーブルは存在しません。
att_shift_cleanAllTimeSlot=選択したシフトのタイムテーブルをクリアしてもよろしいですか？
att_shift_pleaseCheckBox=まず、チェックボックスをオンにしてください。
att_shift_pleaseUnit=サイクル単位とサイクル数を入力してください。
att_shift_pleaseAllDetailTimeSlot=タイムテーブルの詳細を選択してください。
att_shift_placeholderNo=「S01」などの「S」で開始することをお勧めします。
att_shift_placeholderName=「S」で開始または「シフト」で終了のをお勧めします。
att_shift_workType=仕事タイプ
att_shift_normalWork=通常仕事
att_shift_holidayOt=休日残業
att_shift_attShiftStartDateRemark=例：サイクルの開始日は22で、3日をサイクルとして、22/23/24はクラスA / B / Cに、19/20/21はクラスAに移動します / Bクラス/ Cクラスなど。
att_shift_isShiftWithinMonthRemark1=月内にシフトすると、サイクルは各月の最後の日にのみサイクルし、月をまたいで連続的にシフトすることはありません。
att_shift_isShiftWithinMonthRemark2=月以外シフトでは、サイクルは各月の最終日まで繰り返され、1つのサイクルが完了しない場合は、次の月に続きます。
att_shift_workTypeRemark1=注：休憩日の残業として作業タイプが選択されている場合、休日の当日の出席は計算されません。
att_shift_workTypeRemark2=週末残業：残業マークはデフォルトで休業日となり、コンピュータは残業を自動的に計算します。残業申請は不要です。その日の勤務時間は残業時間として記録されます。
att_shift_workTypeRemark3=休日残業、残業マークは休日にデフォルト設定され、コンピュータは残業を自動的に計算し、残業申請は不要で、その日の労働時間は残業時間として記録されます。
att_shift_attendanceModeRemark1=「シフトによるカードのスワイプ」を除き、残業時間は早朝または遅刻としてカウントされません。次に例を示します。
att_shift_attendanceModeRemark2=1.チェックインは不要であるか、有効なカードが1日に1回使用され、残業はカウントされません。
att_shift_attendanceModeRemark3=2.仕事種類：通常仕事、勤怠方法：クレジットカードなし、その日のスケジュールは有効な勤務時間と見なされます。
att_shift_periodStartMode=期間開始モード
att_shift_periodStartModeByPeriod=期間の開始日
att_shift_periodStartModeBySch=シフト開始日
att_shift_addTimeSlotToShift=このシフトのタイムスロットを追加するかどうか
#=====================================================================
#分组
att_group_editGroup=グループユーザー編集
att_group_browseGroupPerson=グループユーザー閲覧
att_group_list=グループリスト
att_group_placeholderNo=「G1」などの「G」で開始することをお勧めします
att_group_placeholderName=「G」で開始または「グループ」で終了のをお勧めします。
att_widget_deptHint=注：選択した部門のすべてのユーザーを導入します
att_widget_searchType=条件付きクエリ
att_widget_noPerson=誰も選択されていません
#分组排班
#部门排班
att_deptSch_existsDept=シフトがあるので、削除できません。
#人员排班
att_personSch_view=ユーザーシフト閲覧
#临时排班
att_schedule_type=シフトタイプ
att_schedule_tempType=一時シフト
att_schedule_normal=通常シフト
att_schedule_intelligent=スマートシフト
att_tempSch_scheduleType=シフトタイプ
att_tempSch_startDate=開始日付
att_tempSch_endDate=終了日付
att_tempSch_attendanceMode=勤怠方法
att_tempSch_overtimeMode=残業モード
att_tempSch_overtimeRemark=残業マーク
att_tempSch_existsDept=シフトがあるので、削除できません。
att_schedult_opAddTempSch=一時シフト追加
att_schedule_cleanEndDate=終了時間クリア
att_schedule_selectOne=選択できるシフトは1つだけです！
att_schedule_selectPerson=ユーザーを選択してください！
att_schedule_selectDept=部署を選択してください！
att_schedule_selectGroup=グループを選択してください！
att_schedule_selectOneGroup=選択できるシフトは1つだけです！
att_schedule_arrange=シフトを選択してください！
att_schedule_leave=休暇
att_schedule_trip=出張
att_schedule_out=外出
att_schedule_off=休息
att_schedule_makeUpClass=追加
att_schedule_class=調整
att_schedule_holiday=休日
att_schedule_offDetail=休息調整
att_schedule_makeUpClassDetail=アクセス調整
att_schedule_classDetail=シフト調整
att_schedule_holidayDetail=休日
att_schedule_noSchDetail=シフトなし
att_schedule_normalDetail=ノーマル
att_schedule_normalSchInfo=シフトセンター：日跨ぎシフトなし
att_schedule_multipleInterSchInfo=コンマ区切りのシフト：複数の日跨ぎシフト
att_schedule_inderSchFirstDayInfo=シフトアクロスグリッド後方オフセット：日跨ぎシフトは初日として記録する。
att_schedule_inderSchSecondDayInfo=シフトアクロスグリッド前方オフセット：日跨ぎシフトは２日目として記録する。
att_schedule_timeConflict=重複です、保存できません！
#=====================================================================
att_excp_notExisetPerson=ユーザーがいません！
att_excp_leavePerson=ユーザーがいません！
#补签单
att_sign_signTime=チェックイン時間
att_sign_signDate=チェックイン日付
#请假
att_leave_arilName=休暇タイプ
att_leave_image=休暇申請書類
att_leave_imageShow=写真無し
att_leave_imageType=ヒント：画像形式が正しくありません。サポートしている形式：JPEG、GIF、PNG
att_leave_imageSize=ヒント：選択した画像が大きすぎます。画像の最大サイズは4MBです。
att_leave_leaveLongDay=休暇時間（日）
att_leave_leaveLongHour=休暇期間（時）
att_leave_leaveLongMinute=休暇時間（分）
att_leave_endNoLessAndEqualStart=終了時刻を開始時刻以下にすることはできません
att_leave_typeNameNoExsists=偽のクラス名は存在しません
att_leave_startNotNull=開始時刻を空にすることはできません
att_leave_endNotNull=終了時刻を空にすることはできません
att_leave_typeNameConflict=偽のタイプ名が出席ステータス名と競合しています
#出差
att_trip_tripLongDay=出張時間（日）
att_trip_tripLongMinute=出張時間（時）
att_trip_tripLongHour=出張時間（分）
#外出
att_out_outLongDay=外出時間（日）
att_out_outLongMinute=外出時間（分）
att_out_outLongHour=外出時間（時）
#加班
att_overtime_type=残業タイプ
att_overtime_normal=通常残業
att_overtime_rest=週末残業
att_overtime_overtimeLong=残業の長さ（分）
att_overtime_overtimeHour=残業時間（時）
att_overtime_notice=申し込む残業時間は１日以下にしてください！
att_overtime_minutesNotice=申し込む残業時間は最小残業時間より長くにしてください！
#调休补班
att_adjust_type=調整タイプ
att_adjust_adjustDate=調整日
att_adjust_shiftName=アクセスシフト追加
att_adjust_selectClass=ユーザーを追加する必要があるシフト名を選択してください！
att_shift_notExistShiftWorkDate=追加できません！
att_adjust_shiftPeriodStartMode=シフトによって選択されたシフト。開始日がシフトに従っている場合、デフォルトは0番目です。
att_adjust_shiftNameNoNull=メイクアップシフトを空にすることはできません
att_adjust_shiftNameNoExsist=メイクアップシフトは存在しません
#调班
att_class_type=調整タイプ
att_class_sameTimeMoveShift=同じ日にシフトを調整
att_class_differenceTimeMoveShift=他の日にシフトを調整
att_class_twoPeopleMove=２人のシフトを交換
att_class_moveDate=調整日
att_class_shiftName=元のスケジュール名
att_class_moveShiftName=新しい調整済みのシフトは空にできません。
att_class_movePersonPin=調整ユーザーID
att_class_movePersonName=調整ユーザー姓
att_class_movePersonLastName=調整ユーザー名
att_class_moveDeptName=調整部署名
att_class_personPin=ユーザーIDは空にしないでください！
att_class_shiftNameNoNull=調整シフトが空にしないでください！
att_class_personPinNoNull=調整ユーザーIDが空にしないでください！
att_class_isNotExisetSwapPersonPin=ユーザーIDがありません！
att_class_personNoSame=ユーザーが同じにすることができません！
att_class_outTime=間隔は１が月以下にしてください！
att_class_shiftNameNoExsist=調整シフトは存在しません
att_class_swapPersonNoExisist=スワップ担当者は存在しません
att_class_dateNoSame=個人は異なる日に転送され、日付を同じにすることはできません
#=====================================================================
#节点
att_node_name=ノード
att_node_type=ノードタイプ
att_node_leader=ダイレクトリーダー
att_node_leaderNode=ダイレクトリーダーノード
att_node_person=指定されたユーザー
att_node_position=指定されたポジション
att_node_choose=選択ポジション
att_node_personNoNull=ユーザーが空にしないでください！
att_node_posiitonNoNull=ポジションが空にしないでください
att_node_placeholderNo=「N01」などの「N」から始めることをお勧めします。
att_node_placeholderName=「管理者ノード」などのポジションから始めて、ノードで終了することをお勧めします。
att_node_searchPerson=検索条件入力
att_node_positionIsExist=重複です、再選択してください！
#流程
att_flow_type=フロータイプ
att_flow_rule=フロールール
att_flow_rule0=1日以内
att_flow_rule1=1日以上3日以内
att_flow_rule2=3日以上7日以下
att_flow_rule3=7日以上
att_flow_node=認証ノード
att_flow_start=開始フロー
att_flow_end=終了フロー
att_flow_addNode=ノード追加
att_flow_placeholderNo=Fで始めることをお勧めします。
att_flow_placeholderName=Leave Flowのように、タイプで始めて、”フロー”で終えることをお勧めします。
att_flow_tips=ノードの認証順序は上から下です。選択した後にソートをドラッグできます。
#申请
att_apply_personPin=申請ユーザーID
att_apply_type=例外タイプ
att_apply_flowStatus=フローの全ステータス
att_apply_start=申請開始
att_apply_flowing=処理中
att_apply_pass=承認
att_apply_over=終了
att_apply_refuse=拒否
att_apply_revoke=キャンセル
att_apply_except=例外
att_apply_view=詳細表示
att_apply_leaveTips=申請が重複です！
att_apply_tripTips=申請が重複です！
att_apply_outTips=申請が重複です！
att_apply_overtimeTips=申請が重複です！
att_apply_adjustTips=申請が重複です！
att_apply_classTips=申請が重複です！
#审批
att_approve_wait=処理中
att_approve_refuse=拒否
att_approve_reason=理由
att_approve_personPin=認証ID
att_approve_personName=認証名
att_approve_person=認証者
att_approve_isPass=認証するか？
att_approve_status=現在のノードステータス
att_approve_tips=この時点で、その人はすでに例外記録を持っており、要求を繰り返すことはできません
att_approve_tips2=フローノードが設定されていません。設定については管理者にお問い合わせください。
att_approve_offDayConflicts=休息日の調整と休暇の調整が競合しています。 追加できません。
att_approve_shiftConflicts=添付のアクセス日と作業日が矛盾しています。 追加できません。
att_approve_shiftNoSch=シフト無し日付に、残業の申請ができません。
att_approve_classConflicts=スケジュール日は非シフト日です。追加できません。
att_approve_selectTime=選択時間はルールに従ってプロセスを決定します
att_approve_withoutPermissionApproval=承認の許可のないワークフローがあります。確認してください！
#=====================================================================
#考勤计算
att_op_calculation=勤怠計算
att_op_calculation_notice=勤怠データは既にバックグラウンドで計算されています。しばらくしてからもう一度お試しください！
att_op_calculation_leave=辞任ユーザー含む
att_statistical_choosePersonOrDept=部署またはユーザーを選択してください！
att_statistical_sureCalculation=勤怠計算をしますか？
att_statistical_filter=フィルターの準備ができています！
att_statistical_initData=基本データの初期化が完了しました！
att_statistical_exception=異常なデータの初期化が完了しました！
att_statistical_error=勤怠計算に失敗しました！
att_statistical_begin=計算開始!
att_statistical_end=計算完了!
att_statistical_noticeTime=勤怠選択可能な時間範囲：前の2ヶ月！
att_statistical_remarkHoliday=H
att_statistical_remarkClass=C
att_statistical_remarkNoSch=NS
att_statistical_remarkRest=R
#原始记录表
att_op_importAccRecord=アクセスレコード導入
att_op_importParkRecord=駐車レコード導入
att_op_importInsRecord=Insレコード導入
att_op_importPidRecord=Pidレコード導入
att_op_importVmsRecord=ビデオレコード導入
att_op_importUSBRecord=USBレコード導入
att_transaction_noAccModule=アクセスモジュールがありません！
att_transaction_noParkModule=駐車モジュールがありません！
att_transaction_noInsModule=Insモジュールがありません！
att_transaction_noPidModule=Pidモジュールがありません！
att_transaction_exportRecord=元のレコードをエクスポートする
att_transaction_exportAttPhoto=出席写真をエクスポート
att_transaction_fileIsTooLarge=エクスポートされたファイルが大きすぎます。日付範囲を狭めてください
att_transaction_exportDate=エクスポート日
att_statistical_attDatetime=勤怠日時
att_statistical_attPhoto=勤怠写真
att_statistical_attDetail=勤怠詳細
att_statistical_acc=アクセスデバイス
att_statistical_att=勤怠デバイス
att_statistical_park=駐車デバイス
att_statistical_faceRecognition=顔認証デバイス
att_statistical_app=携帯電話デバイス
att_statistical_vms=ビデオデバイス
att_statistical_psg=チャネル機器
att_statistical_dataSources=データソース
att_transaction_SyncRecord=出席記録を同期する
#日打卡详情表
att_statistical_dayCardDetail=チェックインの詳細
att_statistical_cardDate=打刻日時
att_statistical_cardNumber=打刻回数
att_statistical_earliestTime=最も早い時間
att_statistical_latestTime=最も遅い時間
att_statistical_cardTime=打刻時間
#请假汇总表
att_statistical_leaveDetail=休暇詳細
#日明细报表
att_statistical_attDate=勤怠日時
att_statistical_week=週
att_statistical_shiftInfo=シフト情報
att_statistical_shiftTimeData=シフト時間
att_statistical_cardValidData=打刻データ
att_statistical_cardValidCount=打刻回数
att_statistical_lateCount=遅刻回数
att_statistical_lateMinute=遅刻分数
att_statistical_earlyCount=早退回数
att_statistical_earlyMinute=早退分数
att_statistical_countData=回数データ
att_statistical_minuteData=分数データ
att_statistical_attendance_minute=出勤（分）
att_statistical_overtime_minute=加班（分）
att_statistical_unusual_minute=异常（分）
#月明细报表
att_monthdetail_should_hour=予定（時）
att_monthdetail_actual_hour=実際（時）
att_monthdetail_valid_hour=有効（時）
att_monthdetail_absent_hour=欠勤（時）
att_monthdetail_leave_hour=休暇（時）
att_monthdetail_trip_hour=出張（時）
att_monthdetail_out_hour=外出（時）
att_monthdetail_should_day=予定（日）
att_monthdetail_actual_day=実際（日）
att_monthdetail_valid_day=有効（日）
att_monthdetail_absent_day=欠勤（日）
att_monthdetail_leave_day=休暇（日）
att_monthdetail_trip_day=出張（日）
att_monthdetail_out_day=外出（日）
#月统计报表
att_statistical_late_minute=遅刻分数
att_statistical_early_minute=早退分数
#部门统计报表
#年度统计报表
att_statistical_should=予定
att_statistical_actual=実際
att_statistical_valid=有効
att_statistical_numberOfTimes=回数
att_statistical_usually=平日
att_statistical_rest=休暇
att_statistical_holiday=休日
att_statistical_total=合計
att_statistical_month=統計月
att_statistical_year=統計年
att_statistical_attendance_hour=出勤（時）
att_statistical_attendance_day=出勤（日）
att_statistical_overtime_hour=残業（時）
att_statistical_unusual_hour=例外（時）
att_statistical_unusual_day=例外（日）
#考勤设备参数
att_deviceOption_query=デバイスパラメータ表示
att_deviceOption_noOption=パラメータ情報がありません、まずデバイスパラメータを取得してください。
att_deviceOption_name=パラメータ名
att_deviceOption_value=パラメータ値
att_deviceOption_UserCount=現在ユーザー数
att_deviceOption_MaxUserCount=最大ユーザー数
att_deviceOption_FaceCount=現在顔数
att_deviceOption_MaxFaceCount=最大顔数
att_deviceOption_FacePhotoCount=現在の顔画像の数
att_deviceOption_MaxFacePhotoCount=顔の画像の最大数
att_deviceOption_FingerCount=現在指紋数
att_deviceOption_MaxFingerCount=最大指紋数
att_deviceOption_FingerPhotoCount=現在の指紋画像数
att_deviceOption_MaxFingerPhotoCount=指紋画像の最大数
att_deviceOption_FvCount=現在指静脈数
att_deviceOption_MaxFvCount=最大指静脈数
att_deviceOption_FvPhotoCount=指静脈画像の現在の数
att_deviceOption_MaxFvPhotoCount=指静脈画像の最大数
att_deviceOption_PvCount=現在手のひら数
att_deviceOption_MaxPvCount=最大手のひら数
att_deviceOption_PvPhotoCount=手のひら画像の現在の数
att_deviceOption_MaxPvPhotoCount=手のひらの画像の最大数
att_deviceOption_TransactionCount=現在履歴数
att_deviceOption_MaxAttLogCount=最大履歴数
att_deviceOption_UserPhotoCount=現在ユーザー写真数
att_deviceOption_MaxUserPhotoCount=最大ユーザー写真数
att_deviceOption_FaceVersion=顔アルゴリズムバージョン
att_deviceOption_FPVersion=指紋アルゴリズムバージョン
att_deviceOption_FvVersion=指静脈アルゴリズムバージョン
att_deviceOption_PvVersion=手のひらアルゴリズムバージョン
att_deviceOption_FWVersion=ファームウェアバージョン
att_deviceOption_PushVersion=Pushバージョン
#=====================================================================
#API
att_api_areaCodeNotNull=エリア番号は空にできません
att_api_pinsNotNull=pinsデータを空にすることはできません
att_api_pinsOverSize=pinsデータ長は500を超えることはできません
att_api_areaNoExist=エリアがありません
att_api_sign=打刻追加
#=====================================================================
#休息时间段
att_leftMenu_breakTime=休憩期間
att_breakTime_startTime=開始時間
att_breakTime_endTime=終了時間
#=====================================================================
#导入U盘记录
att_import_uploadFileSuccess=ファイルのアップロードが成功し、ファイルデータが解析されました。お待ちください。
att_import_resolutionComplete=解析が完了すると、データベースが更新されます。
att_import_snNoExist=インポートされたファイルに対応する勤怠デバイスがありません。ファイルをもう一度選択してください。
att_import_fileName_msg=インポートされたファイル名のフォーマットの要件：デバイスのシリアル番号の先頭は、アンダースコア「_」で区切られています（例：「3517171600001_attlog.dat」）。
att_import_notSupportFormat=このフォーマットはまだサポートされていません！
att_import_selectCorrectFile=正しいフォーマットのファイルを選択してください！
att_import_fileFormat=ファイルフォーマット
att_import_targetFile=対象ファイル
att_import_startRow=ヘッダー行数
att_import_startRowNote=データフォーマットの最初の行はデータをインポートするためのものです。インポートする前にファイルを確認してください。
att_import_delimiter=区切り文字
#=====================================================================
#设备操作日志
att_device_op_log_op_type=操作コード
att_device_op_log_dev_sn=デバイスSN
att_device_op_log_op_content=操作内容
att_device_op_log_operator_pin=操作ユーザーID
att_device_op_log_operator_name=操作ユーザー名前
att_device_op_log_op_time=操作時間
att_device_op_log_op_who_value=操作対象値
att_device_op_log_op_who_content=操作対象説明
att_device_op_log_op_value1=操作対象2
att_device_op_log_op_value_content1=操作対象説明2
att_device_op_log_op_value2=操作対象3
att_device_op_log_op_value_content2=操作対象説明3
att_device_op_log_op_value3=操作対象4
att_device_op_log_op_value_content3=操作対象説明4
#操作日志的操作类型
att_device_op_log_opType_0=ブーツ
att_device_op_log_opType_1=シャットダウン
att_device_op_log_opType_2=認証失敗
att_device_op_log_opType_3=アラーム
att_device_op_log_opType_4=メニューに入る
att_device_op_log_opType_5=設定変更
att_device_op_log_opType_6=指紋登録
att_device_op_log_opType_7=パスワード登録
att_device_op_log_opType_8=HIDカード登録
att_device_op_log_opType_9=ユーザー削除
att_device_op_log_opType_10=指紋削除
att_device_op_log_opType_11=パスワード削除
att_device_op_log_opType_12=カード削除
att_device_op_log_opType_13=データクリア
att_device_op_log_opType_14=MFカード作成
att_device_op_log_opType_15=MFカード登録
att_device_op_log_opType_16=MFカード登録
att_device_op_log_opType_17=MFカード登録削除
att_device_op_log_opType_18=MFカード内容クリア
att_device_op_log_opType_19=登録データをカードに移動します
att_device_op_log_opType_20=カード内のデータをデバイスにコピーします
att_device_op_log_opType_21=時間設定
att_device_op_log_opType_22=出荷設定
att_device_op_log_opType_23=入場/退場履歴削除
att_device_op_log_opType_24=管理者権限クリア
att_device_op_log_opType_25=アクセスグループ設定変更
att_device_op_log_opType_26=ユーザーアクセス設定変更
att_device_op_log_opType_27=アクセスタイムゾン設定変更
att_device_op_log_opType_28=組合わせ設定変更
att_device_op_log_opType_29=解錠
att_device_op_log_opType_30=ユーザー新規
att_device_op_log_opType_31=指紋属性変更
att_device_op_log_opType_32=強迫警報
att_device_op_log_opType_34=アンチパスバック
att_device_op_log_opType_35=勤怠写真削除
att_device_op_log_opType_36=他のユーザー情報変更
att_device_op_log_opType_37=休日
att_device_op_log_opType_38=データリアルストア
att_device_op_log_opType_39=データバックアップ
att_device_op_log_opType_40=Uディスクアップロード
att_device_op_log_opType_41=Uディスクダウンロード
att_device_op_log_opType_42=Uディスク勤怠履歴暗号化
att_device_op_log_opType_43=Uディスクのダウンロードに成功したら履歴を削除
att_device_op_log_opType_53=退室ボタン
att_device_op_log_opType_54=ゲートセンサー
att_device_op_log_opType_55=アラーム
att_device_op_log_opType_56=パラメータ復元
att_device_op_log_opType_68=ユーザー写真登録
att_device_op_log_opType_69=ユーザー写真変更
att_device_op_log_opType_70=ユーザー名変更
att_device_op_log_opType_71=ユーザー権限変更
att_device_op_log_opType_76=IP変更
att_device_op_log_opType_77=マスク変更
att_device_op_log_opType_78=ゲートウェイ変更
att_device_op_log_opType_79=DNS変更
att_device_op_log_opType_80=接続パスワード変更
att_device_op_log_opType_81=デバイスID変更
att_device_op_log_opType_82=クラウドサーバーアドレス変更
att_device_op_log_opType_83=修改云クラウドサーバーポート変更
att_device_op_log_opType_87=アクセス履歴設定変更
att_device_op_log_opType_88=顔パラメータロゴ変更
att_device_op_log_opType_89=指紋パラメータロゴ変更
att_device_op_log_opType_90=指静脈パラメータロゴ変更
att_device_op_log_opType_91=手のひらパラメータロゴ変更
att_device_op_log_opType_92=uディスクアップグレードロゴ
att_device_op_log_opType_100=RFカード情報変更
att_device_op_log_opType_101=顔登録
att_device_op_log_opType_102=ユーザー権限変更
att_device_op_log_opType_103=ユーザー権限削除
att_device_op_log_opType_104=ユーザー権限追加
att_device_op_log_opType_105=アクセス履歴削除
att_device_op_log_opType_106=顔削除
att_device_op_log_opType_107=ユーザー写真削除
att_device_op_log_opType_108=パラメータ変更
att_device_op_log_opType_109=WIFISSID選択
att_device_op_log_opType_110=proxy有効
att_device_op_log_opType_111=proxyIP変更
att_device_op_log_opType_112=proxyポート変更
att_device_op_log_opType_113=ユーザーパスワード変更
att_device_op_log_opType_114=顔情報変更
att_device_op_log_opType_115=管理者パスワード変更
att_device_op_log_opType_116=アクセス設定復元
att_device_op_log_opType_117=管理者パスワード間違います。
att_device_op_log_opType_118=管理者パスワードロックダウンします。
att_device_op_log_opType_120=レギックカードのデータ長を変更
att_device_op_log_opType_121=指静脈を登録
att_device_op_log_opType_122=指静脈を変更する
att_device_op_log_opType_123=指静脈を削除
att_device_op_log_opType_124=palmprintを登録
att_device_op_log_opType_125=掌紋を修正する
att_device_op_log_opType_126=掌紋を削除
#操作对象描述
att_device_op_log_content_pin=ユーザーID：
att_device_op_log_content_alarm=アラーム：
att_device_op_log_content_alarm_reason=アラーム理由：
att_device_op_log_content_update_no=変更イベント番号：
att_device_op_log_content_update_value=変更イベント値：
att_device_op_log_content_finger_no=指紋番号：
att_device_op_log_content_finger_size=指紋タンプレート長さ：
#=====================================================================
#工作流
att_flowable_datetime_to=へ
att_flowable_todomsg_leave=休暇監査
att_flowable_todomsg_sign=打刻追加監査
att_flowable_todomsg_overtime=残業監査
att_flowable_notifymsg_leave=休暇通知
att_flowable_notifymsg_sign=打刻追加通知
att_flowable_notifymsg_overtime=残業通知
att_flowable_shift=シフト:
att_flowable_hour=時
att_flowable_todomsg_trip=出張監査
att_flowable_notifymsg_trip=出張通知
att_flowable_todomsg_out=外出監査
att_flowable_notifymsg_out=外出通知
att_flow_apply=申し込む
att_flow_applyTime=申し込む時間
att_flow_approveTime=監査時間
att_flow_operateUser=監査人
att_flow_approve=監査
att_flow_approveComment=付注
att_flow_approvePass=監査結果
att_flow_status_processing=監査中
#=====================================================================
#biotime
att_h5_pers_personIdNull=ユーザーIDが空にできません
att_h5_attPlaceNull=打刻ポイントが空にできません
att_h5_attAreaNull=勤怠エリアが空にできません
att_h5_pers_personNoExist=ユーザーIDがありません
att_h5_signRemarkNull=備考が空にできません
att_h5_common_pageNull=ページネーションパラメータエラー
att_h5_taskIdNotNull=タスクノードIDは空にできません
att_h5_auditResultNotNull=監査結果は空にできません
att_h5_latLongitudeNull=緯度と経度は空にできません
att_h5_pers_personIsNull=ユーザーIDがありません
att_h5_pers_personIsNotInArea=エリアを設置しません。
att_h5_mapApiConnectionsError=マップAPI接続エラー
att_h5_googleMap=グーグルマップ
att_h5_gaodeMap=GaoDeマップ
att_h5_defaultMap=デフォルトマップ
att_h5_shiftTime=シフト時間
att_h5_signTimes=打刻追加時間
att_h5_enterKeyWords=キーワードを入力してください：
att_h5_mapSet=勤怠マップ設置
att_h5_setMapApiAddress=マップパラメータ設定
att_h5_MapSetWarning=マップを切り替えると、登録されている携帯端末のチェックインアドレスの経度と緯度が一致しなくなりますので、慎重に修正してください！
att_h5_mapSelect=マップ選択
att_h5_persNoHire=ユーザーはまだ入社していません
att_slef_apiKey=API-KEY
att_self_apiJsKey=API-JS-KEY
att_self_noComputeAtt=当日勤怠は計算されていません
att_self_noSignRecord=当日に打刻がありません
att_self_imageUploadError=画像のアップロードに失敗しました
att_self_attSignAddressAreaIsExist=打刻ポイント重複です
att_self_signRuleIsError=タイムアウトです
att_self_signAcrossDay=クロスデーシフトには打刻追加できません！
att_self_todaySignIsExist=打刻追加しました！
att_self_signSetting=打刻追加設定
att_self_allowSign=許可された打刻追加:
att_self_allowSignSuffix={0}日内の勤怠履歴
att_self_onlyThisMonth=本月のみ
att_self_allowAcrossMonth=クロス月できます
att_self_thisTimeNoSch=シフトがありません！
att_self_revokeReason=取消理由:
att_self_revokeHint=監査のため、取消理由を20文字以内で入力してください
att_self_persSelfLogin=セルフサービスログイン
att_self_isOpenSelfLogin=セルフサービスログイン有効するかどうか
att_self_applyAndWorkTimeOverlap=時間が重複です
att_apply_DurationIsZero=申請期間は0、申請は許可されていません
att_sign_mapWarn=マップを読み込めませんでした。ネットワーク接続を確認し、KEY値をマップしてください
att_admin_applyWarn=操作が失敗しました、シフトされていない人がいる、またはアプリケーションの時間がシフトの範囲外です！ （{0}）
att_self_getPhotoFailed=画像は存在しません
att_self_view=見る
# 二维码
att_param_qrCodeUrl=QRコードのURL
att_param_qrCodeUrlHref=サーバーアドレス：ポート
att_param_appAttQrCode=モバイル勤怠QRコード
att_param_timingFrequency=時間間隔：5-59分または1-24時間
att_sign_signTimeNotNull=打刻追加時間を空にすることはできません
att_apply_overLastMonth=アプリケーションの開始時間が先月を超えました
att_apply_withoutDetail=プロセスの詳細なし
att_flowable_noAuth=表示するには特権管理者アカウントを使用してください
att_apply_overtimeOverMaxTimeLong=残業期間が最大残業期間より大きい
# 考勤设置参数符号
att_other_arrive=√
att_other_late=遅刻
att_other_early=早退
att_other_absent=欠勤
att_other_noSignIn=[チェックイン無し
att_other_noSignOff=チェックアウト無し]
att_other_leave=休暇
att_other_overtime=残業
att_other_off=振替
att_other_classes=代償
att_other_trip=出張
att_other_out=外出
att_other_incomplete=無し
att_other_outcomplete=完了していません
# 服务器下发命令
att_devCmd_submitTime=送信時間
att_devCmd_returnedResult=返された結果
att_devCmd_returnTime=戻り時間
att_devCmd_content=コマンドコンテンツ
att_devCmd_clearCmd=コマンドリストをクリアする
# 实时点名
att_realTime_selectDept=部門を選択してください
att_realTime_noSignPers=サインインしていません
att_realTime_signPers=サインイン
att_realTime_signMonitor=サインインモニター
att_realTime_signDateTime=サインイン時間
att_realTime_realTimeSet=リアルタイムロールコール設定
att_realTime_openRealTime=リアルタイムロールコールを有効にする
att_realTime_rollCallEnd=リアルタイムロールコール終了
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=Scheduled
att_personSch_cycleSch=サイクルスケジュール
att_personSch_cleanCycleSch=クリーンサイクルスケジュール
att_personSch_cleanTempSch=一時的なスケジュールをクリーンアップ
att_personSch_personCycleSch=人のサイクルスケジュール
att_personSch_deptCycleSch=部門サイクルスケジュール
att_personSch_groupCycleSch=グループサイクルスケジュール
att_personSch_personTempSch=人員の一時的なスケジュール
att_personSch_deptTempSch=部門の一時的なスケジュール
att_personSch_groupTempSch=グループの一時的なスケジュール
att_personSch_checkGroupFirst=左側のグループまたは右側のリストの人々をチェックして操作してください！
att_personSch_sureDeleteGroup={0}とグループに対応するスケジュールを削除してもよろしいですか？
att_personSch_sch=スケジュール
att_personSch_delSch=スケジュールの削除
#考勤计算
att_statistical_sureAllCalculate=すべての担当者の出席計算を実行してもよろしいですか？
#异常管理
att_exception_downTemplate=テンプレートをダウンロードしてインポートする
att_exception_signImportTemplate=署名インポートテンプレート
att_exception_leaveImportTemplate=インポートテンプレートを残す
att_exception_overtimeImportTemplate=残業インポートテンプレート
att_exception_adjustImportTemplate=インポートテンプレートの調整
att_exception_cellDefault=必須フィールドではありません
att_exception_cellRequired=必須フィールド
att_exception_cellDateTime=必須フィールド、時間形式はyyyy-MM-dd HH：mm：ss、例：2020-07-07 08:30:00
att_exception_cellLeaveTypeName=必須フィールド：「個人休暇」、「結婚休暇」、「出産休暇」、「病気休暇」、「年次休暇」、「離婚休暇」、「家族休暇」、「授乳休暇」、「出張」、「外出」傲慢な名前
att_exception_cellOvertimeSign=「通常の残業」、「休憩日の残業」、「休日の残業」などの必須フィールド
att_exception_cellAdjustType=「転送オフ」、「クラスの構成」などの必須フィールド
att_exception_cellAdjustDate=必須フィールド、時間形式はyyyy-MM-dd（例：2020-07-07）
att_exception_cellShiftName=調整タイプがシフトシフトの場合の必須フィールド
att_exception_refuse=拒否
att_exception_end=異常終了
att_exception_delete=削除
att_exception_stop=一時停止
#时间段
att_timeSlot_normalTimeAdd=通常のタイムスロットを追加
att_timeSlot_elasticTimeAdd=エラスティックタイムスロットを追加
#班次
att_shift_addRegularShift=通常のシフトを追加
att_shift_addFlexibleShift=フレキシブルシフトを追加
#参数设置
att_param_notLeaveSetting=偽以外の計算設定
att_param_smallestUnit=最小単位
att_param_workDay=workday
att_param_roundingControl=丸め制御
att_param_abort=ダウン（放棄）
att_param_rounding=rounding
att_param_carry=Up（キャリー）
att_param_reportSymbol=レポート表示シンボル
att_param_convertCountValid=数値を入力し、小数点以下1桁のみを許可してください
att_other_leaveThing=Thing
att_other_leaveMarriage=Marriage
att_other_leaveBirth=product
att_other_leaveSick=Sick
att_other_leaveAnnual=year
att_other_leaveFuneral=Funeral
att_other_leaveHome=探索
att_other_leaveNursing=看護
att_other_leavetrip=bad
att_other_leaveout=other
att_common_schAndRest=スケジュールと休憩
att_common_timeLongs=時間の長さ
att_personSch_checkDeptOrPersFirst=左側の部門または右側のリストの担当者を確認して操作してください！
att_personSch_checkCalendarFirst=最初にスケジュールする日付を選択してください！
att_personSch_cleanCheck=チェックをクリアします
att_personSch_delTimeSlot=選択したタイムスロットをクリアします
att_personSch_repeatTimeSlotNoAdd=繰り返しのタイムスロットがある場合は追加しないでください！
att_personSch_showSchInfo=スケジュールの詳細を表示
att_personSch_sureToCycleSch={0}を定期的にスケジュールしますか？
att_personSch_sureToTempSch=一時的に{0}をスケジュールしてもよろしいですか？
att_personSch_sureToCycleSchDeptOrGroup={0}未満のすべての担当者をスケジュールしてもよろしいですか？
att_personSch_sureToTempSchDeptOrGroup={0}の下のすべての担当者を一時的にスケジュールしますか？
att_personSch_sureCleanCycleSch={0}の定期的なスケジュールを{1}から{2}にクリアしてもよろしいですか？
att_personSch_sureCleanTempSch={0}の一時的なスケジュールを{1}から{2}にクリアしてもよろしいですか？
att_personSch_sureCleanCycleSchDeptOrGroup={0}未満のすべての担当者のサイクルスケジュールを{1}から{2}にクリアしてもよろしいですか？
att_personSch_sureCleanTempSchDeptOrGroup={0}の下のすべての担当者の一時的なスケジュールを{1}から{2}にクリアしてもよろしいですか？
att_personSch_today=今日
att_personSch_timeSoltName=期間名
att_personSch_export=人事スケジュールのエクスポート
att_personSch_exportTemplate=臨時スタッフスケジューリングテンプレートをダウンロードする
att_personSch_import=インポートされた担当者の一時的なスケジュール
att_personSch_tempSchTemplate=臨時スタッフスケジューリングテンプレート
att_personSch_tempSchTemplateTip=スケジュールの開始時刻と終了時刻を選択し、その日付のスケジュールテンプレートをダウンロードしてください
att_personSch_opTip=操作手順
att_personSch_opTip1=1の場合、マウスを使用して、スケジュールのためにカレンダーコントロールの単一の日付に期間をドラッグできます。
att_personSch_opTip2=2の場合、単一の日付をダブルクリックして、カレンダーコントロールでシフトをスケジュールできます。
att_personSch_opTip3=3。カレンダーコントロールで、マウスを押したまま移動して、スケジュールする複数の日付を選択します。
att_personSch_schRules=スケジュールルール
att_personSch_schRules1=1.定期的なスケジュール：交差点があるかどうかに関係なく、同じ日付以降の前のスケジュールをカバーします。
att_personSch_schRules2=2。一時的なスケジュール：同じ日付に交差点があり、後者は前の一時的なスケジュールをカバーします。交差点がない場合は、同時に存在します。
att_personSch_schRules3=3.期間と一時：同じ日付に交差がある場合、期間は一時的にカバーされ、交差がない場合、同時に存在します。定期シフトタイプがインテリジェントシフトの場合、期間は一時シフトによって直接カバーされます。
att_personSch_schStatus=スケジュールステータス
#左侧菜单-排班管理
att_leftMenu_schDetails=スケジュールの詳細
att_leftMenu_detailReport=出席詳細レポート
att_leftMenu_signReport=サインアップ詳細テーブル
att_leftMenu_leaveReport=詳細フォームを残す
att_leftMenu_abnormal=異常な出席テーブル
att_leftMenu_yearLeaveSumReport=年次LeaveSumReport
att_leave_maxFileCount=追加できる写真は最大4枚までです
#时间段
att_timeSlot_add=タイムスロットを設定する
att_timeSlot_select=タイムスロットを選択してください！
att_timeSlot_repeat=タイムスロット "{0}"が繰り返されます！
att_timeSlot_overlapping=タイムスロット "{0}"は "{1}"の通勤時間と重複しています！
att_timeSlot_addFirst=最初にタイムスロットを設定してください！
att_timeSlot_notEmpty=人員番号{0}に対応するタイムスロットを空にすることはできません！
att_timeSlot_notExist=人員番号{0}に対応する期間 "{1}"は存在しません！
att_timeSlot_repeatEx=人員番号{0}に対応する期間「{1}」と「{2}」が重複している
att_timeSlot_importRepeat=人員番号{0}に対応する期間「{1}」が繰り返されます
att_timeSlot_importNotPin=システムに{0}という番号の人はいません！
att_timeSlot_elasticTimePeriod=個人番号{0}、フレックス期間'{1}'をインポートできません！
#导入
att_import_overData=現在インポートされているアイテムの数は{0}で、制限の30,000を超えています。バッチでインポートしてください！
att_import_existIllegalType=インポートされた{0}のタイプが不正です！
#验证方式
att_verifyMode_0=自動識別
att_verifyMode_1=指紋のみ
att_verifyMode_2=作業IDの検証
att_verifyMode_3=パスワードのみ
att_verifyMode_4=カードのみ
att_verifyMode_5=指紋またはパスワード
att_verifyMode_6=指紋またはカード
att_verifyMode_7=カードまたはパスワード
att_verifyMode_8=作業IDと指紋
att_verifyMode_9=指紋とパスワード
att_verifyMode_10=カードと指紋
att_verifyMode_11=カードとパスワード
att_verifyMode_12=指紋とパスワードとカード
att_verifyMode_13=作業IDと指紋とパスワード
att_verifyMode_14=（作業IDと指紋）または（カードと指紋）
att_verifyMode_15=顔
att_verifyMode_16=顔と指紋
att_verifyMode_17=顔とパスワード
att_verifyMode_18=フェイスプラスカード
att_verifyMode_19=顔と指紋とカード
att_verifyMode_20=顔と指紋とパスワード
att_verifyMode_21=指静脈
att_verifyMode_22=指静脈とパスワード
att_verifyMode_23=指の静脈とカード
att_verifyMode_24=指の静脈とパスワードとカード
att_verifyMode_25=パームプリント
att_verifyMode_26=パームプリントとカード
att_verifyMode_27=手のひらのプリントと顔
att_verifyMode_28=手のひらの印刷と指紋
att_verifyMode_29=掌紋と指紋と顔
# 工作流
att_flow_schedule=進行状況の監査
att_flow_schedulePass=（合格）
att_flow_scheduleNot=（承認されていません）
att_flow_scheduleReject=（拒否）
# 工作时长表
att_workTimeReport_total=総作業時間
# 自动导出报表
att_autoExport_startEndTime=開始時間と終了時間
# 年假
att_annualLeave_setting=年間休暇残高設定
att_annualLeave_settingTip1=年次休暇残高機能を使用するには、各従業員の入場時間を設定する必要があります。入場時間が設定されていない場合、スタッフの年次休暇残高テーブルの残りの年次休暇は空として表示されます。
att_annualLeave_settingTip2=現在の日付が清算発行日よりも大きい場合、この変更は翌年に有効になります。現在の日付が清算発行日よりも小さい場合、清算発行日に達すると、それは清算され、年次休暇が再発行されます。
att_annualLeave_calculate=年間休暇の清算および発行日
att_annualLeave_workTimeCalculate=作業時間の比率に従って計算します
att_annualLeave_rule=年間休暇時間ルール
att_annualLeave_ruleCountOver=設定された最大数の制限に達しました
att_annualLeave_years=シニア年
att_annualLeave_eachYear=毎年
att_annualLeave_have=はい
att_annualLeave_days=年次休暇の日数
att_annualLeave_totalDays=年間休暇の合計
att_annualLeave_remainingDays=残りの年間休暇
att_annualLeave_consecutive=年次休暇ルールの設定は連続した年でなければなりません
# 年假结余表
att_annualLeave_report=年次休暇バランスシート
att_annualLeave_validDate=有効な日付
att_annualLeave_useDays={0}日を使用
att_annualLeave_calculateDays=リリース{0}日
att_annualLeave_notEnough={0}の年間休暇が不十分です！
att_annualLeave_notValidDate={0}が年間休暇の有効範囲内にありません！
att_annualLeave_notDays={0}には年次休暇がありません！
att_annualLeave_tip1=ZhangSanは昨年9月1日に参加しました
att_annualLeave_tip2=年間休暇残高設定
att_annualLeave_tip3=清算・発行日は毎年1月1日です。稼働率に応じて四捨五入して計算します。勤続年数が1以下の場合は3日、勤続年数が3年以下の場合は5日となります。
att_annualLeave_tip4=年間休暇は計算を楽しむ
att_annualLeave_tip5=昨年09-01〜12-31 4 / 12x3=1。0日をお楽しみください
att_annualLeave_tip6=今年01-01〜12-31は4。0日をお楽しみください（今年01-01〜08-31は8 / 12x3=2。0日をお楽しみいただけます 今年09-01〜12-31は4/12x5≈2。0日をお楽しみいただけます）
# att SDC
att_sdc_name=ビデオ機器
att_sdc_wxMsg_firstData=こんにちは、出席チェックイン通知があります
att_sdc_wxMsg_stateData=出席チェックインの成功感がない
att_sdc_wxMsg_remark=注意：最終的な出席結果は、チェックインの詳細ページの対象となります。
# 时间段
att_timeSlot_conflict=タイムスロットがその日のその他のタイムスロットと競合しています
att_timeSlot_selectFirst=タイムスロットを選択してください
# 事件中心
att_eventCenter_sign=出席サインイン
#异常管理
att_exception_classImportTemplate=クラスインポートテンプレート
att_exception_cellClassAdjustType=必須フィールド： "{0}"、 "{1}"、 "{2}"
att_exception_swapDateDate=必須ではないフィールド。時間形式はyyyy-MM-ddです（例：2020-07-07）。
#消息中心
att_message_leave=出席の通知{0}
att_message_leaveContent={0}送信済み{1}、{2}時間は{3}〜{4}です
att_message_leaveTime=休暇
att_message_overtime=出席と残業の通知
att_message_overtimeContent={0}は残業を提出し、残業時間は{1}〜{2}です。
att_message_overtimeTime=残業時間
att_message_sign=出席サインの通知
att_message_signContent={0}は補足サインを送信し、補足サインの時間は{1}です。
att_message_adjust=出席調整の通知
att_message_adjustContent={0}が調整を送信し、調整日は{1}です
att_message_class=出席およびシフト通知
att_message_classContent=クラスコンテンツ
att_message_classContent0={0}はシフトを送信し、シフトの日付は{1}、シフトは{2}です。
att_message_classContent1={0}はシフトを送信し、シフト日は{1}、シフト日は{2}です。
att_message_classContent2={0}（{1}）および{2}（{3}）スワッピングクラス
#推送中心
att_pushCenter_transaction=出席レコード
# 时间段
att_timeSlot_workTimeNotEqual=作業時間は作業時間を降りるのと同じにすることはできません
att_timeSlot_signTimeNotEqual=サインインの開始時間をサインアウトの終了時間と等しくすることはできません
# 北向接口A
att_api_notNull={0}を空にすることはできません！
att_api_startDateGeEndDate=開始時刻は終了時刻以上にすることはできません！
att_api_leaveTypeNotExist=偽の種は存在しません！
att_api_imageLengthNot2000=画像アドレスの長さは2000を超えることはできません！
# 20221230新增国际化
att_personSch_workTypeNotNull=個人番号{0}に対応する職種は空にできません！
att_personSch_workTypeNotExist=個人番号{0}に対応する職種は存在しません！
att_annualLeave_recalculate=再計算
# 20230530新增国际化
att_leftMenu_dailyReport=出席日報
att_leftMenu_overtimeReport=残業レポート
att_leftMenu_lateReport=後期レポート
att_leftMenu_earlyReport=早期レポートを残す
att_leftMenu_absentReport=欠席レポート
att_leftMenu_monthReport=出席月次レポート
att_leftMenu_monthWorkTimeReport=月間作業時間レポート
att_leftMenu_monthCardReport=毎月のカード レポート
att_leftMenu_monthOvertimeReport=毎月の残業レポート
att_leftMenu_overtimeSummaryReport=スタッフの残業概要レポート
att_leftMenu_deptOvertimeSummaryReport=部門残業概要レポート
att_leftMenu_deptLeaveSummaryReport=部門休暇概要レポート
att_annualLeave_calculateDay=年次有給休暇日数
att_annualLeave_adjustDay=日数を調整
att_annualLeave_sureSelectDept=選択した部門で {0} 操作を実行してもよろしいですか?
att_annualLeave_sureSelectPerson=選択した人物に対して {0} 操作を実行してもよろしいですか?
att_annualLeave_calculateTip1=勤続年数に応じて計算する場合: 年次休暇の計算は月単位で正確に行われます。勤続年数が 10 年 3 か月の場合、10 年 3 か月が計算に使用されます。
att_annualLeave_calculateTip2=換算が勤続年数に基づいていない場合: 年次休暇の計算は年に対して正確です。勤続年数が 10 年 3 か月の場合、10 年が計算に使用されます。
att_rule_isInCompleteTip=サインインもサインアウトも未完了として記録され、遅刻、早退、欠勤、および有効な場合、優先度が最も高くなります。
att_rule_absentTip=サインインもサインアウトも欠勤として記録されていない場合、欠勤の長さは勤務時間の長さから遅刻から早退の長さを引いたものに等しい
att_timeSlot_elasticTip1=0、有効時間は実際の時間と同じ、欠勤なし
att_timeSlot_elasticTip2=実際の時間が勤務時間よりも長い場合、有効時間は勤務時間と等しくなり、欠勤はありません
att_timeSlot_elasticTip3=実際の期間が勤務期間よりも短い場合、有効期間は実際の期間に等しく、欠勤は勤務期間から実際の期間を差し引いたものに等しくなります
att_timeSlot_maxWorkingHours=労働時間を超えることはできません
# 20231030
att_customReport=出欠カスタムレポート
att_customReport_byDayDetail=日別詳細
att_customReport_byPerson=個人別サマリー
att_customReport_byDept=部署別サマリー
att_customReport_queryMaxRange=クエリの最大範囲は4ヶ月です。
# 20240630新增国际化
att_personSch_shiftWorkTypeTip1=1、勤務タイプが通常勤務/休日出勤の場合、休日より優先順位が低い
att_personSch_shiftWorkTypeTip2=2、勤務タイプが休日出勤の場合、休日より優先順位が高い
att_personVerifyMode=ユーザ認証方式
att_personVerifyMode_setting=認証方式の設定
att_personSch_importCycSch=サイクル・シフトのインポート
att_personSch_cycSchTemplate=サイクルシフトパターン
att_personSch_exportCycSchTemplate=ユーザサイクルシフトテンプレートのダウンロード
att_personSch_scheduleTypeNotNull=シフトタイプは空または存在しないことはできません！
att_personSch_shiftNotNull=シフトを空にすることはできません！
att_personSch_shiftNotExist=シフトは存在しません！
att_personSch_onlyAllowOneShift=通常のシフトは1つのシフトしか許可されていません！
att_shift_attShiftStartDateRemark2=サイクル開始日の週は第1週であり、サイクル開始日の月は第1月です。
#打卡状态
att_cardStatus_setting=勤怠状態の設定
att_cardStatus_name=名前
att_cardStatus_value=価値
att_cardStatus_alias=エイリアス
att_cardStatus_every_day=每日
att_cardStatus_by_week=週による
att_cardStatus_autoState=自動状態
att_cardStatus_attState=勤怠状態
att_cardStatus_signIn=シグンイン
att_cardStatus_signOut=シグンアウト
att_cardStatus_out=外出
att_cardStatus_outReturn=外出後の戻り
att_cardStatus_overtime_signIn=残業のサインイン
att_cardStatus_overtime_signOut=残業のサインアウト
# 20241030新增国际化
att_leaveType_enableMaxDays=年度制限の有効化
att_leaveType_maxDays=年間制限（日）
att_leaveType_applyMaxDays=年間申請は{0}日を超えることができません
att_param_overTimeSetting=残業等級設定
att_param_overTimeLevel=残業等級（時間）
att_param_overTimeLevelEnable=残業等級計算を有効にするかどうか
att_param_reportColor=レポート表示色
# APP
att_app_signClientTip=このデバイスは今日、誰かがすでにチェックインしました
att_app_noSignAddress=チェックイン範囲が設定されていません、管理者に連絡して設定してください
att_app_notInSignAddress=チェックイン地点に到着していません、チェックイン出来ません
att_app_attendance=私の出勤状況
att_app_apply=出勤申請
att_app_approve=私の承認待ち
# 20250530
att_node_leaderNodeExist=直属上司の承認ノードが既に存在します
att_signAddress_init=地図の初期化
att_signAddress_initTips=地図キーを入力し、住所を選択するために地図を初期化してください