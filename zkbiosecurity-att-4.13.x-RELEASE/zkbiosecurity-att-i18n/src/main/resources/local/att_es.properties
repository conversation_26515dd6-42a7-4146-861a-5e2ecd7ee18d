#系统名称 西班牙语
att_systemName=Att6.0 Sistema de Gestión de Asistencia
#=====================================================================
#左侧菜单
att_module=Asistencia
#左侧菜单-考勤设备
att_leftMenu_attendanceDevice=Gestión de dispositivos
att_leftMenu_device=Dispositivo de asistencia
att_leftMenu_point=Puntos de Asistencia
att_leftMenu_sign_address=Dirección De Inicio De Sesión Móvil
att_leftMenu_adms_devCmd=El servidor emitió un comando
#左侧菜单-基础信息
att_leftMenu_basicInformation=Información básica
att_leftMenu_rule=Regla
att_leftMenu_base_rule=Reglas de asistencia
att_leftMenu_department_rule=Reglas por departamento
att_leftMenu_holiday=Día Festivo
att_leftMenu_leaveType=Motivo de Salida
att_leftMenu_timingCalculation=Cálculo temporizado
att_leftMenu_autoExport=Informe de envío
att_leftMenu_param=Ajuste de parámetros
#左侧菜单-班次管理
att_leftMenu_shiftManagement=Turno
att_leftMenu_timeSlot=Horario
att_leftMenu_shift=Turno
#左侧菜单-排班管理
att_leftMenu_scheduleManagement=Horario
att_leftMenu_group=Grupo
att_leftMenu_groupPerson=Grupo de personal
att_leftMenu_groupSch=Horario de Grupo
att_leftMenu_deptSch=Horario de Departamento
att_leftMenu_personSch=Horario de Personal
att_leftMenu_tempSch=Horario Temporal
att_leftMenu_nonSch=Personal no Programado
#左侧菜单-异常管理
att_leftMenu_exceptionManagement=Gestión de excepciones de asistencia
att_leftMenu_sign=Adjuntar Asistencia
att_leftMenu_leave=Permiso de Salida
att_leftMenu_trip=Viaje de negocios
att_leftMenu_out=Salida
att_leftMenu_overtime=Tiempo Extra
att_leftMenu_adjust=Ajustar y Anexar
att_leftMenu_class=Ajustar Turno
#左侧菜单-统计报表
att_leftMenu_statisticalReport=Informe de estadísticas de asistencia
att_leftMenu_manualCalculation=Cálculo Manual
att_leftMenu_transaction=Eventos
att_leftMenu_dayCardDetailReport=Asistencia Diaria
att_leftMenu_leaveSummaryReport=Reporte de Salidas
att_leftMenu_dayDetailReport=Reporte por Día
att_leftMenu_monthDetailReport=Reporte Mensual
att_leftMenu_monthStatisticalReport=Estadísticas mensuales de personal
att_leftMenu_deptStatisticalReport=Estadísticos mensuales por departamentos
att_leftMenu_yearStatisticalReport=Reporte Anual
att_leftMenu_attSignCallRollReport=Iniciar sesión
att_leftMenu_workTimeReport=Informe de tiempo trabajado
#左侧菜单-设备操作日志
att_leftMenu_device_op_log=Registros del dispositivo
#左侧菜单-实时点名
att_leftMenu_realTime_callRoll=Pasar lista en tiempo real
#=====================================================================
#公共
att_common_person=Personal
att_common_pin=Identificación
att_common_group=Grupo
att_common_dept=Departamento
att_common_symbol=Símbolo
att_common_deptNo=Número departamento
att_common_deptName=Nombre de Departamento
att_common_groupNo=Número de grupo
att_common_groupName=Nombre del grupo
att_common_operateTime=Tiempo de operacion
att_common_operationFailed=Operación fallida
att_common_id=Identificación
att_common_deptId=ID de departamento
att_common_groupId=Identificación del grupo
att_common_deviceId=ID del dispositivo
att_person_pin=ID de Usuario
att_person_name=Nombre
att_person_lastName=Apellido
att_person_internalCard=Número de tarjeta
att_person_attendanceMode=Tiempo y modo de asistencia
att_person_normalAttendance=Asistencia normal
att_person_noPunchCard=Sin marcación
att_common_attendance=Asistencia
att_common_attendance_hour=Asistencia (horas)
att_common_attendance_day=Asistencia (días)
att_common_late=Tarde
att_common_early=Temprano
att_common_overtime=A través del tiempo
att_common_exception=Excepción
att_common_absent=Ausente
att_common_leave=Salir
att_common_trip=Viaje de negocios
att_common_out=Fuera
att_common_staff=Empleado
att_common_superadmin=Super usuario
att_common_msg=Contenido SMS
att_common_min=Mensajes cortos Duración (minutos)
att_common_letterNumber=Sólo puede introducir números o letras!
att_common_relationDataCanNotDel=Los datos asociados no se puede eliminar.
att_common_relationDataCanNotEdit=Los datos asociados no se puede modificar.
att_common_needSelectOneArea=Por favor, seleccione un área!
att_common_neesSelectPerson=Por favor, seleccione una persona!
att_common_nameNoSpace=El nombre no puede contener espacios!
att_common_digitsValid=Sólo se pueden introducir números con hasta dos decimales.
att_common_numValid=Sólo introducir números!
#=====================================================================
#工作面板
att_dashboard_worker=Trabajador obsesivo
att_dashboard_today=La asistencia de hoy
att_dashboard_todayCount=Estadísticas de asistencias del día
att_dashboard_exceptionCount=Estadística de excepciones (este mes)
att_dashboard_lastWeek=La semana pasada
att_dashboard_lastMonth=El mes pasado
att_dashboard_perpsonNumber=Total Personal
att_dashboard_actualNumber=Real
att_dashboard_notArrivedNumber=Ausentismo del Personal
att_dashboard_attHour=Tiempo de trabajo
#区域
#设备
att_op_syncDev=Sincronizar datos de software en el dispositivo
att_op_account=Ajustes de datos de asistencia
att_op_check=Cargar datos de nuevo
att_op_deleteCmd=Borrar comandos del dispositivo
att_op_dataSms=Mensaje público
att_op_clearAttPic=Desactive las fotos de asistencia
att_op_clearAttLog=Despejar las operaciones de asistencia
att_device_waitCmdCount=Los comandos a ejecutar
att_device_status=Habilitar estado
att_device_register=Dispositivo de registro
att_device_isRegister=dispositivo de inscripción
att_device_existNotRegDevice=Dispositivo no registrado, los datos no se pueden obtener!
att_device_fwVersion=Versión de firmware
att_device_transInterval=Intervalo de actualización (en minutos)
att_device_cmdCount=El número máximo de comandos para comunicarse con el servidor.
att_device_delay=Tiempo de registro (segundos)
att_device_timeZone=Zona horaria
att_device_operationLog=Registros de las operaciones
att_device_registeredFingerprint=Registro de huellas dactilares
att_device_registeredUser=Registro de Personal
att_device_fingerprintImage=imagen de huellas dactilares
att_device_editUser=Editar Personal
att_device_modifyFingerprint=Modificar la huella digital
att_device_faceRegistration=Enrolamiento facial
att_device_userPhotos=Foto Personal
att_device_attLog=Ya sea para cargar los registros de asistencia
att_device_operLog=Cargar información sobre el personal
att_device_attPhoto=Subir fotos de asistencia
att_device_isOnLine=Estado en línea
att_device_InputPin=Introduzca el número de personas
att_device_getPin=Obtener los datos de personal especificados
att_device_separatedPin=números de personal múltiples, separados por comas
att_device_authDevice=Dispositivo autorizado
att_device_disabled=Los siguientes dispositivos están desactivados y no pueden ser operados!
att_device_autoAdd=Se agrega nuevo equipo automáticamente
att_device_receivePersonOnlyDb=Recibir sólo los datos de las personas que existen en la base de datos
att_devMenu_control=Control del dispositivo
att_devMenu_viewOrGetInfo=Ver u obtener información
att_devMenu_clearData=Borrar datos del dispositivo
att_device_disabledOrOffline=El dispositivo no está habilitado o no está en línea, no se puede utilizar!
att_device_areaStatus=Estado del área del dispositivo
att_device_areaCommon=La región es normal
att_device_areaEmpty=El área está vacía
att_device_isRegDev=La modificación de la zona horaria o del estado del registrador requiere que el dispositivo se reinicie para que surta efecto.
att_device_canUpgrade=Los siguientes dispositivos se pueden actualizar
att_device_offline=¡Los siguientes dispositivos están fuera de línea y no pueden ser operados!
att_device_oldProtocol=Protocolo antiguo
att_device_newProtocol=Nuevo protocolo
att_device_noMoreTwenty=El paquete de actualización del firmware del dispositivo del protocolo anterior no puede exceder los 20M
att_device_transferFilesTip=Firmware detectado con éxito, transfiere archivos
att_op_clearAttPers=Borrar el personal de los equipos
#区域人员
att_op_forZoneAddPers=Ajuste Personal Area
att_op_dataUserSms=Mensaje privado
att_op_syncPers=Volver a sincronizar con el dispositivo
att_areaPerson_choiceArea=Por favor, seleccione la zona!
att_areaPerson_byAreaPerson=Por región
att_areaPerson_setByAreaPerson=Establecido por el personal del área
att_areaPerson_importBatchDel=Importar eliminación masiva
att_areaPerson_syncToDevSuccess=¡Operación exitosa! Por favor, espere a que el comando se enviará.
att_areaPerson_personId=ID de Usuario
att_areaPerson_areaId=ID de la zona
att_area_existPerson=Hay gente en la zona!
att_areaPerson_notice1=Ninguna Área o persona puede estar vacía al mismo tiempo.
att_areaPerson_notice2=¡No se registró a ninguna persona ni ninguna zona!
att_areaPerson_notice3=No se encontraron dispositivos de la zona!
att_areaPerson_addArea=Añadir Área
att_areaPerson_delArea=Eliminar área
att_areaPerson_persNoExit=Persona no existe
att_areaPerson_importTip1=Asegúrese de que la persona importada ya existe en el módulo de personal
att_areaPerson_importTip2=La importación por lotes de personas no se envía automáticamente al dispositivo y debe sincronizarse manualmente.
att_areaPerson_addAreaPerson=Agregar personal por área
att_areaPerson_delAreaPerson=Eliminar personal del área
att_areaPerson_importDelAreaPerson=Importar y eliminar personal del área
att_areaPerson_importAreaPerson=Importar personal de área
#考勤点
att_attPoint_name=Nombre de punto de asistencia
att_attPoint_list=Lista de puntos de asistencia
att_attPoint_deviceModule=Módulo dispositivo
att_attPoint_acc=Control de acceso
att_attPoint_park=Estacionamiento
att_attPoint_ins=FaceKiosk
att_attPoint_pid=Certificado personal
att_attPoint_vms=Vídeo
att_attPoint_psg=Pasillo
att_attPoint_doorList=Lista de puerta
att_attPoint_deviceList=Lista de dispositivos
att_attPoint_channelList=Lista de canales
att_attPoint_gateList=Lista de puertas
att_attPoint_recordTypeList=Tipo de registro de extracción
att_attPoint_door=Por favor, seleccione la puerta correspondiente.
att_attPoint_device=Por favor, seleccione el dispositivo correspondiente.
att_attPoint_gate=Seleccione la puerta correspondiente
att_attPoint_normalPassRecord=Registro de pase normal
att_attPoint_verificationRecord=Registro de verificación
att_person_attSet=Ajuste de asistencia
att_attPoint_point=Por favor, seleccione el punto de asistencia.
att_attPoint_count=Puntos de asistencia insuficientes, operación fallida
att_attPoint_notSelect=El módulo no está configurado
att_attPoint_accInsufficientPoints=puntos de asistencia inadecuados para los registros de control de acceso!
att_attPoint_parkInsufficientPoints=Puntos de asistencia inadecuados para los registros de Parqueadero!
att_attPoint_insInsufficientPoints=El permiso por puntos asistencia de faceKiosk es insuficiente!
att_attPoint_pidInsufficientPoints=No hay suficientes personas que pasen como puntos de asistencia
att_attPoint_doorOrParkDeviceName=Nombre del dispositivo de parqueadero
att_attPoint_vmsInsufficientPoints=Puntos de asistencia insuficientes con cámaras de reconocimiento facial
att_attPoint_psgInsufficientPoints=¡El canal tiene puntos de asistencia insuficientes!
att_attPoint_delDevFail=No se ha podido eliminar el dispositivo, ¡el dispositivo ya se está utilizando como punto de asistencia!
att_attPoint_pullingRecord=El punto de asistencia está recibiendo registros regularmente, ¡por favor espere!
att_attPoint_lastTransactionTime=Hora de la última extracción de datos
att_attPoint_masterDevice=Dispositivo maestro
att_attPoint_channelName=Nombre del canal
att_attPoint_cameraName=Nombre de la cámara
att_attPoint_cameraIP=Cámara IP
att_attPoint_channelIP=ip del canal
att_attPoint_gateNumber=Número de puerta
att_attPoint_gateName=Nombre de la puerta
#APP考勤签到地址
att_signAddress_address=Dirección
att_signAddress_longitude=Longitud
att_signAddress_latitude=Latitud
att_signAddress_range=Alcance efectivo
att_signAddress_rangeUnit=La unidad de medida es (m)
#=====================================================================
#规则
att_rule_baseRuleSet=Ajuste de reglas básicas
att_rule_countConvertSet=Ajuste del cálculo
att_rule_otherSet=Otro ajuste
att_rule_baseRuleSignIn=Regla de Entrada Regla
att_rule_baseRuleSignOut=Regla de Salida Regla
att_rule_earliestPrinciple=Regla de llegada temprana
att_rule_theLatestPrinciple=La regla más reciente
att_rule_principleOfProximity=La regla de proximidad
att_rule_baseRuleShortestMinutes=El período de tiempo mínimo debe ser mayor que (mínimo 10 minutos)
att_rule_baseRuleLongestMinutes=El período de tiempo máximo debe ser inferior a (máximo 1.440 minutos)
att_rule_baseRuleLateAndEarly=Retardos y Salida Temprana contar como Falta
att_rule_baseRuleCountOvertime=Estadísticas de tiempo extra
att_rule_baseRuleFindSchSort=Buscador de Registro de Turno
att_rule_groupGreaterThanDepartment=Grupo-> Departamento
att_rule_departmentGreaterThanGroup=Departmento-> Grupo
att_rule_baseRuleSmartFindClass=Ajuste de turnos automáticos
att_rule_timeLongest=Duración más larga de Trabajo
att_rule_exceptionLeast=Excepciones mínimas
att_rule_baseRuleCrossDay=Cálculo de Asistencia para Turno de Cruce de Día
att_rule_firstDay=Primer día
att_rule_secondDay=Segundo día
att_rule_baseRuleShortestOvertimeMinutes=Tiempo Extra más corto (minutos)
att_rule_baseRuleMaxOvertimeMinutes=Máximo Horas Extra (minutos)
att_rule_baseRuleElasticCal=Horarios flexibles (calculo)
att_rule_baseRuleTwoPunch=Acumular horas de dos en dos
att_rule_baseRuleStartEnd=Marcaciones del primer y último registro
att_rule_countConvertHour=Regla de conversión hora
att_rule_formulaHour=Formula: Hours = Minutes / 60
att_rule_countConvertDay=Regla de conversión días
att_rule_formulaDay=Fórmula: Número de días = Número de minutos / Número de minutos a trabajar por día
att_rule_inFormulaShallPrevail=Tome el resultado calculado por la fórmula como el estándar;
att_rule_remainderHour=El resto es mayor que o igual a
att_rule_oneHour=Registrado como una hora.
att_rule_halfAnHour=Se registra como media hora, de lo contrario se ignora.
att_rule_remainderDay=Cociente es mayor o igual a los minutos de trabajo
att_rule_oneDay=%, Calculado como un día,
att_rule_halfAnDay=%, Calculado como media-día, ignorada de otro modo;
att_rule_countConvertAbsentDay=Base para la conversión de los días de ausencia del trabajo
att_rule_markWorkingDays=Calculado como el día de trabajo
att_rule_countConvertDecimal=Número exacto de cifras decimales
att_rule_otherSymbol=Establezca los símbolos siglas para representar los resultados de la asistencia en los informes
att_rule_arrive=Esperado / real
att_rule_noSignIn=Sin Registro
att_rule_noSignOff=Sin Salida
att_rule_off=Descanso
att_rule_class=Anexar Asistencia
att_rule_shortLessLong=El periodo de asistencia más corto no puede ser mayor que el periodo de asistencia más largo.
att_rule_symbolsWarning=Configure símbolos o iniciales de asistencia en el informe para que no esté vacío.
att_rule_reportSettingSet=Ajustes de exportación de informe
att_rule_shortDateFormat=Formato de fecha
att_rule_shortTimeFormat=Formato de tiempo
att_rule_baseRuleSignBreakTime=Si hay que marcar a la entrada o a la salida durante las pausas
att_leftMenu_custom_rule=regla personalizada
att_custom_rule_already_exist=Ya existen {0} reglas personalizadas!
att_add_group_custom_rule=Añadir reglas de agrupación
att_custom_rule_type=tipo de regla
att_rule_type_group=La agrupación de reglas
att_rule_type_dept=Reglas del departamento
att_custom_rule_orgNames=Uso de objetos
att_rult_maxOverTimeType1=Sin límite
att_rult_maxOverTimeType2=Esta semana
att_rult_maxOverTimeType3=Este mes
att_rule_countConvertDayRemark1=Ejemplo: Si el tiempo de trabajo efectivo es de 500 minutos y el tiempo de trabajo diario es de 480 minutos, el resultado es 500/480 = 1,04, conservando el último decimal como 1,0.
att_rule_countConvertDayRemark2=Ejemplo: el tiempo de trabajo efectivo es de 500 minutos, se deben trabajar 480 minutos al día, el resultado es 500/480 = 1,04, 1,04>0,8 cuenta como un día
att_rule_countConvertDayRemark3=Ejemplo: Si el tiempo de trabajo efectivo es de 300 minutos y el tiempo de trabajo diario es de 480 minutos, el resultado es 300/480 = 0,625, 0,2 < 0,625 < 0,8 cuenta como media jornada.
att_rule_countConvertDayRemark4=Base de conversión de días: el número de días registrados como días laborables en el periodo de tiempo no funciona.
att_rule_countConvertDayRemark5=Prevalecerá el número de días laborables: Los cálculos se limitará únicamente a los días de absentismo y la duración del absentismo se calculará sobre la base del número de días laborables del período siempre que haya absentismo en cada período.
att_rule_baseRuleSmartFindRemark1=Horas más largas: calcular las horas de trabajo correspondientes a cada turno del día, según los registros diarios, y hallar el turno con las horas de trabajo válidas más largas del día.
att_rule_baseRuleSmartFindRemark2=Mínimo anormal: calcular el número de anormalidades correspondientes a cada turno del día, según los registros diarios, y encontrar el turno con menos anormalidades del día para calcular las horas de trabajo.
att_rule_baseRuleHourValidator=La sentencia minutos de media hora no puede ser mayor que o igual a 1 hora!
att_rule_baseRuleDayValidator=La duración de una sentencia de medio día no puede ser superior o igual a la de una sentencia de 1 día.
att_rule_overtimeWarning=La duración máxima de las horas extra no puede ser menor que la duración mínima de las horas extra!
att_rule_noSignInCountType=No se registrará como
att_rule_absent=Absentismo
att_rule_earlyLeave=Salir temprano
att_rule_noSignOffCountType=No se registran cancelaciones como
att_rule_minutes=minutos
att_rule_noSignInCountLateMinute=La falta de registro de ingreso se cuenta como minutos de retraso
att_rule_noSignOffCountEarlyMinute=La falta de registro de salida se contabiliza como minutos de salida anticipada.
att_rule_incomplete=incompleto
att_rule_noCheckInIncomplete=Incompleto sin registrarse
att_rule_noCheckOutIncomplete=Devoluciones incompletas sin registrar
att_rule_lateMinuteWarning=El número de minutos no registrados como tarde debe ser superior a 0 e inferior a la duración máxima del período de asistencia.
att_rule_earlyMinuteWarning=El número de minutos no registrados como anticipados debe ser superior a 0 e inferior a la duración máxima del período de asistencia.
att_rule_baseRuleNoSignInCountLateMinuteRemark=Cuando no se registra se cuenta como tarde, si no se registra, se cuenta como tarde durante N minutos
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=Si no marca la salida, se le calculara que se marcha N minutos antes.
#节假日
att_holiday_placeholderNo=Se recomienda comenzar con H, tales como H01.
att_holiday_placeholderName=Se recomienda a nombre de [año] + [Nombre de vacaciones], por ejemplo. [2017 Día del Trabajo].
att_holiday_dayNumber=Número de días
att_holiday_validDate_msg=Vacaciones durante este tiempo
#假种
att_leaveType_leaveThing=Salida Ocasional
att_leaveType_leaveMarriage=Matrimonio
att_leaveType_leaveBirth=Maternidad
att_leaveType_leaveSick=Incapacidad medica
att_leaveType_leaveAnnual=Vacaciones Anuales
att_leaveType_leaveFuneral=Funeral
att_leaveType_leaveHome=Personal
att_leaveType_leaveNursing=Lactancia
att_leaveType_isDeductWorkLong=Descontar Horas de Trabajo
att_leaveType_placeholderNo=Se recomienda comenzar con L, tales como L1.
att_leaveType_placeholderName=Sugerencias que terminan en permiso, p. ej. permiso de matrimonio
#定时计算
att_timingcalc_timeCalcFrequency=intervalo de cálculo
att_timingcalc_timeCalcInterval=Tiempo de cálculo temporizado
att_timingcalc_timeSet=Ajuste del tiempo a cálcular
att_timingcalc_timeSelect=Por favor, seleccione el tiempo
att_timingcalc_optionTip=Al menos un cálculo de la asistencia programada diario válido debe ser retenido
#自动导出报表
att_autoExport_reportType=Tipo de informe
att_autoExport_fileType=Tipo de archivo
att_autoExport_fileName=Nombre del archivo
att_autoExport_fileDateFormat=Formato de fecha
att_autoExport_fileTimeFormat=Formato de tiempo
att_autoExport_fileContentFormat=Formato del contenido
att_autoExport_fileContentFormatTxt=Ejemplo: {} 00 {DEPTNAME personPin} {01} a PERSONNAME 02} {03 attDatetime
att_autoExport_timeSendFrequency=Enviar Frecuencia
att_autoExport_timeSendInterval=Intervalo de envío
att_autoExport_emailType=Tipo de receptor electrónico
att_autoExport_emailRecipients=Destinatario de correo
att_autoExport_emailAddress=Correo Electronico
att_autoExport_emailExample=Ejemplo: 123 @ foxmail.com, 456 @ foxmail.com
att_autoExport_emailSubject=Título del correo
att_autoExport_emailContent=Cuerpo del correo
att_autoExport_field=Campo
att_autoExport_fieldName=Nombre del campo
att_autoExport_fieldCode=Número de campo
att_autoExport_reportSet=La configuración de informes
att_autoExport_timeSet=Ajuste de la hora de entrega del correo
att_autoExport_emailSet=Ajuste de correo electrónico
att_autoExport_emailSetAlert=Por favor, introduzca su dirección de correo electrónico.
att_autoExport_emailTypeSet=Configuración del destinatario
att_autoExport_byDay=Por día
att_autoExport_byMonth=Por mes
att_autoExport_byPersonSet=Por Personal
att_autoExport_byDeptSet=Por Departamento
att_autoExport_byAreaSet=Por Área
att_autoExport_emailSubjectSet=Ajuste del título
att_autoExport_emailContentSet=Ajuste corporal
att_autoExport_timePointAlert=Seleccione la hora correcta de envío
att_autoExport_lastDayofMonth=Último día del mes
att_autoExport_firstDayofMonth=Primer día del mes
att_autoExport_dayofMonthCheck=Fecha específica
att_autoExport_dayofMonthCheckAlert=Por favor seleccione la fecha específica.
att_autoExport_chooseDeptAlert=Por favor, seleccione el Departamento!
att_autoExport_sendFormatSet=Configuración del método de envío
att_autoExport_sendFormat=Modo envío
att_autoExport_mailFormat=Método E-mail
att_autoExport_ftpFormat=Método FTP
att_autoExport_sftpFormat=Método SFTP
att_autoExport_ftpUrl=Dirección del servidor FTP
att_autoExport_ftpPort=Puerto del servidor FTP
att_autoExport_ftpTimeSet=Enviar FTP Ajuste de la hora
att_autoExport_ftpParamSet=Parámetros de la ruta FTP
att_autoExport_ftpUsername=Nombre de usuario FTP
att_autoExport_ftpPassword=Contraseña FTP
att_autoExport_correctFtpParam=Por favor rellene los parámetros FTP correctamente
att_autoExport_correctFtpTestParam=Por favor, probar la conexión para asegurarse de que la comunicación es normal
att_autoExport_inputFtpUrl=Por favor, introduzca la dirección del servidor
att_autoExport_inputFtpPort=Por favor, introduzca el puerto del servidor
att_autoExport_ftpSuccess=La conexión se realizó correctamente
att_autoExport_ftpFail=Por favor, compruebe si la configuración de parámetros son incorrectos.
att_autoExport_validFtp=Por favor, introduzca una dirección de servidor válida
att_autoExport_validPort=Por favor, introduzca un puerto de servidor válido
att_autoExport_selectExcelTip=Elija EXCEL como tipo de archivo y valide el formato del contenido en todos los campos.
#=====================================================================
#时间段
att_timeSlot_periodType=Tipo de Horario
att_timeSlot_normalTime=Normal
att_timeSlot_elasticTime=Horarios flexibles
att_timeSlot_startSignInTime=Check-In Hora de inicio
att_timeSlot_toWorkTime=Hora de entrada
att_timeSlot_endSignInTime=Check-In Hora de finalización
att_timeSlot_allowLateMinutes=Tolerancia Retardo (min)
att_timeSlot_isMustSignIn=Debe Registrar Entrada
att_timeSlot_startSignOffTime=Hora de salida de inicio
att_timeSlot_offWorkTime=Hora de salida
att_timeSlot_endSignOffTime=Fin de la hora de salida
att_timeSlot_allowEarlyMinutes=Salida Temprana (min)
att_timeSlot_isMustSignOff=Debe Registrar Salida
att_timeSlot_workingHours=Tiempo de trabajo (minutos)
att_timeSlot_isSegmentDeduction=Auto-Descontar Descanso
att_timeSlot_startSegmentTime=Hora de inicio
att_timeSlot_endSegmentTime=Hora de finalización
att_timeSlot_interSegmentDeduction=Deducido Tiempo (minutos)
att_timeSlot_markWorkingDays=Jornada laboral
att_timeSlot_isAdvanceCountOvertime=¿Cuentan las horas extras por adelantado?
att_timeSlot_signInAdvanceTime=Auto OT Hora de finalización (Check-in)
att_timeSlot_isPostponeCountOvertime=¿El aplazamiento cuenta como horas extras?
att_timeSlot_signOutPosponeTime=Auto OT Hora de inicio (Check-Out)
att_timeSlot_isCountOvertime=Calculado como horas extras
att_timeSlot_timeSlotLong=Las horas de trabajo deben cumplir con el intervalo definido por la asistencia a las reglas:
att_timeSlot_alertStartSignInTime=El registro de ingreso debe ser menor que el tiempo programado.
att_timeSlot_alertEndSignInTime=El tiempo de Check-in final debe ser mayor que el tiempo de llegada.
att_timeSlot_alertStartSignInAndEndSignIn=La hora de salida antelada debe ser menor que la hora de salida.
att_timeSlot_alertStartSignOffTime=La hora de inicio de las horas extraordinarias no puede ser menor que la hora de salida.
att_timeSlot_alertEndSignOffTime=La hora de inicio de las horas extras no puede ser mayor que la hora de salida final.
att_timeSlot_alertStartUnequalEnd=Las jornadas de trabajo no puede ser menor que 0.
att_timeSlot_alertStartSegmentTime=El tiempo deducido no puede ser menor que 0.
att_timeSlot_alertStartAndEndTime=La hora de inicio debe ser inferior al inicio del intervalo
att_timeSlot_alertEndAndoffWorkTime=Las horas no puede ser mayor que 23.
att_timeSlot_alertToWorkLesserAndEndTimeLesser=Los minutos no puede ser mayor que 59.
att_timeSlot_alertLessSignInAdvanceTime=Regístrese antes de la hora de entrada a laborar
att_timeSlot_alertMoreSignInAdvanceTime=El número de minutos de tiempo extra antes de 'ir a trabajar' es menor que el número de minutos antes de 'trabajo'
att_timeSlot_alertMoreSignOutPosponeTime=fuera del trabajo' posdata minutos de tiempo extra es menor que 'después de minutos de trabajo'
att_timeSlot_alertLessSignOutPosponeTime=El registro de salida debe ser mayor que la hora de finalización del turno
att_timeSlot_time=Por favor, introduzca el formato de hora correcta.
att_timeSlot_alertMarkWorkingDays=La jornada de trabajo no puede estar vacía!
att_timeSlot_placeholderNo=Se recomienda comenzar con T, tales como T01.
att_timeSlot_placeholderName=Se recomienda comenzar con T o terminar con calendario.
att_timeSlot_beforeToWork=Inicio de Entrada
att_timeSlot_afterToWork=Fin de Entrada
att_timeSlot_beforeOffWork=Inicio de Salida
att_timeSlot_afterOffWork=Fin de salida
att_timeSlot_minutesSignInValid=Registro en mínutos
att_timeSlot_toWork=En Turno
att_timeSlot_offWork=Fuera de Turno
att_timeSlot_minutesSignInAsOvertime=Registro de minutos para marcar horas extra antes de iniciar
att_timeSlot_minutesSignOutAsOvertime=Comienzo del registro para calcular las horas extra en minutos
att_timeSlot_minOvertimeMinutes=Minutos mínimos de horas extra
att_timeSlot_enableWorkingHours=Si se habilita el horario laboral
att_timeSlot_eidtTimeSlot=Editar periodo de tiempo
att_timeSlot_browseBreakTime=Períodos de descanso o fuera de horario
att_timeSlot_addBreakTime=Añadir una franja horaria de descanso
att_timeSlot_enableFlexibleWork=Habilitar trabajo flexible
att_timeSlot_advanceWorkMinutes=Puede ir a trabajar con antelación
att_timeSlot_delayedWorkMinutes=Puede posponer el trabajo
att_timeSlot_advanceWorkMinutesValidMsg1=El número de minutos antes de ir a trabajar es mayor que el número de minutos que pueden ir a trabajar con antelación
att_timeSlot_advanceWorkMinutesValidMsg2=El número de minutos que pueden ir a trabajar con antelación es menor que el número de minutos antes de ir a trabajar
att_timeSlot_advanceWorkMinutesValidMsg3=El número de minutos que pueden ser trabajó con antelación es menor o igual al número de minutos antes de iniciar sesión en tiempo extra de trabajo.
att_timeSlot_advanceWorkMinutesValidMsg4=El número de minutos que pueden ser antes de iniciar sesión a las horas extraordinarias de trabajo es mayor o igual que el número de minutos trabajado de antemano.
att_timeSlot_delayedWorkMinutesValidMsg1=El número de minutos después del trabajo debe ser superior al número de minutos que se pueden retrasar
att_timeSlot_delayedWorkMinutesValidMsg2=El número de minutos que se puede retrasar al trabajo es menor que el número de minutos después del trabajo
att_timeSlot_delayedWorkMinutesValidMsg3=El número de minutos que puede ser programado para trabajar es menor o igual al número de minutos después de después del trabajo, firmando y comenzando a las horas extraordinarias de trabajo
att_timeSlot_delayedWorkMinutesValidMsg4=El número de minutos que puede ser después del trabajo, firmando y comenzando a las horas extraordinarias de trabajo es mayor que o igual al número de minutos después de programado para trabajar
att_timeSlot_allowLateMinutesValidMsg1=El número de minutos permitido llegar tarde es menor que el número de minutos después del trabajo
att_timeSlot_allowLateMinutesValidMsg2=El número de minutos después de que después del trabajo es mayor que el número de minutos permitidos minutos a llegar tarde
att_timeSlot_allowEarlyMinutesValidMsg1=Permitir primeros minutos es menor que minutos antes del trabajo
att_timeSlot_allowEarlyMinutesValidMsg2=El número de minutos antes de que antes del trabajo es mayor que el número de minutos permitidos minutos fueron temprano
att_timeSlot_timeOverlap=Superposición del tiempo de descanso, modifique el período de tiempo de descanso!
att_timeSlot_atLeastOne=¡Al menos 1 período de descanso!
att_timeSlot_mostThree=¡Hasta 3 períodos de descanso!
att_timeSlot_canNotEqual=¡La hora de inicio del período de descanso no puede ser igual a la hora de finalización!
att_timeSlot_shoudInWorkTime=¡Asegúrese de que el período de descanso esté dentro de las horas de trabajo!
att_timeSlot_repeatBreakTime=Repita el período de descanso!
att_timeSlot_toWorkLe=La hora de inicio debe ser inferior a la hora mínima de inicio del periodo de descanso seleccionado.
att_timeSlot_offWorkGe=El tiempo de baja debe ser superior a la hora máxima de finalización del periodo seleccionado.
att_timeSlot_crossDays_toWork=La hora de inicio mínimo para el período de descanso se encuentra dentro del período de tiempo:
att_timeSlot_crossDays_offWork=La hora máxima de finalización del periodo de descanso debe estar dentro del periodo de tiempo:
att_timeSlot_allowLateMinutesRemark=Marcar desde el inicio de la jornada laboral hasta el número de minutos permitido por retraso cuenta como un turno de trabajo normal
att_timeSlot_allowEarlyMinutesRemark=Registrar la salida anticipada desde el final del turno está dentro del número permitido de minutos de salida temprana y cuenta como un registro de turno normal.
att_timeSlot_isSegmentDeductionRemark=Eliminación del periodo de descanso en el período de tiempo
att_timeSlot_attEnableFlexibleWorkRemark1=Los horarios de trabajo flexibles no permiten un número fijo de minutos de llegadas tarde y salidas temprano.
att_timeSlot_afterToWorkRemark=El número de minutos después del trabajo es igual al número de minutos que se pueden aplazar
att_timeSlot_beforeOffWorkRemark=El número de minutos antes del trabajo es igual al número de minutos disponibles para empezar temprano
att_timeSlot_attEnableFlexibleWorkRemark2=El número de minutos de horas extra debe ser mayor o igual que la hora después del trabajo + la hora de marcación de llegada 
att_timeSlot_attEnableFlexibleWorkRemark3=El número de minutos que puede empezar a trabajar con antelación debe ser inferior o igual al número de minutos de tiempo extra registrados en N minutos de trabajo
att_timeSlot_attEnableFlexibleWorkRemark4=El número de minutos que se pueden aplazar debe ser inferior o igual al número de minutos de tiempo extra registrados al final de la jornada.
att_timeSlot_attBeforeToWorkAsOvertimeRemark=Ejemplo: 9: 00 clase, accede a las horas extraordinarias de trabajo 60 minutos antes del trabajo, a continuación, comprobar antes de las horas extraordinarias de trabajo 8:00-8:00
att_timeSlot_attAfterOffWorkAsOvertimeRemark=Ejemplo: Si sale del trabajo a las 18:00 y registra su salida 60 minutos después de finalizar el turno como horas extras, se calcularan como horas extras desde las 19:00 hasta la hora en que registre su salida.
att_timeSlot_longTimeValidRemark=El número de horas de los registros después del trabajo y el número de horas de registro válida de la tarjeta antes del trabajo no pueden solaparse dentro del periodo de tiempo.
att_timeSlot_advanceWorkMinutesValidMsg5=El número de minutos válidos para marcar antes de que finalice el turno debe ser superior al número de minutos disponibles para empezar antes.
att_timeSlot_advanceWorkMinutesValidMsg6=El número de minutos que puedes empezar antes es menor que el número de minutos que tienes para registrarte antes del final de la jornada
att_timeSlot_delayedWorkMinutesValidMsg5=El número de minutos válidos después del check-in es mayor que el número de minutos que se pueden posponer para trabajar.
att_timeSlot_delayedWorkMinutesValidMsg6=La cantidad de minutos que pueden posponerse para trabajar debe ser inferior a los minutos válidos después de iniciar sesión
att_timeSlot_advanceWorkMinutesValidMsg7=La hora de entrada antes del trabajo no puede coincidir con la hora de salida del trabajo del día anterior.
att_timeSlot_delayedWorkMinutesValidMsg7=La hora de salida del turno no puede coincidir con la hora de entrada antes del siguiente día de trabajo.
att_timeSlot_maxOvertimeMinutes=Limite el máximo de horas extra
#班次
att_shift_basicSet=Tipo de horario
att_shift_advancedSet=Nombre de programación
att_shift_type=Tipo de Turno
att_shift_name=cambio de nombre
att_shift_regularShift=Turno regular
att_shift_flexibleShift=Turnos flexibles
att_shift_color=Color
att_shift_periodicUnit=Unidad
att_shift_periodNumber=Ciclo
att_shift_startDate=Fecha de inicio
att_shift_startDate_firstDay=Ciclo Fecha de Inicio
att_shift_isShiftWithinMonth=Turnos disponibles durante el mes
att_shift_attendanceMode=Modo de asistencia
att_shift_shiftNormal=Registro regular de entrada y salida según el turno
att_shift_oneDayOneCard=Un registro válido por día
att_shift_onlyBrushTime=Sólo se calcula la hora de entrada
att_shift_notBrushCard=Sin marcación
att_shift_overtimeMode=Modo de tiempo extra
att_shift_autoCalc=Cálculo automático por ordenador
att_shift_mustApply=Las horas extraordinarias deben aplicar
att_shift_mustOvertime=Deben trabajar horas extras o marcar como Ausencia
att_shift_timeSmaller=Duración más corta entre Auto-Cálculo y Recibo de horas extras
att_shift_notOvertime=No calculado como horas extras
att_shift_overtimeSign=Tipo horas extras
att_shift_normal=Día normal
att_shift_restday=Día de descanso
att_shift_timeSlotDetail=Detalles de los Horarios
att_shift_doubleDeleteTimeSlot=Haga doble clic en el período de desplazamiento; puede eliminar el período de tiempo
att_shift_addTimeSlot=Agregar Horarios
att_shift_cleanTimeSlot=Limpiar Horario
att_shift_NO=No.
att_shift_notAll=Deselecciona todo
att_shift_notTime=Si la casilla de verificación detalle horario no se puede comprobar, indica que existe una superposición en el calendario.
att_shift_notExistTime=Este calendario no existe.
att_shift_cleanAllTimeSlot=¿Está seguro de que desea borrar el calendario para el cambio seleccionado?
att_shift_pleaseCheckBox=Por favor, seleccione la casilla de verificación en el lado izquierdo que es el mismo que el tiempo de visualización actual en el lado derecho.
att_shift_pleaseUnit=Por favor, introduzca la unidad y el número de ciclos
att_shift_pleaseAllDetailTimeSlot=Por favor, seleccione los detalles de horarios.
att_shift_placeholderNo=Se recomienda comenzar con S, tales como S0.
att_shift_placeholderName=Se recomienda comenzar con S o terminar con cambio.
att_shift_workType=Tipo de trabajo
att_shift_normalWork=Marcaciones normales
att_shift_holidayOt=Tiempo Extra Día Festivo
att_shift_attShiftStartDateRemark=Ejemplo: La fecha de inicio del ciclo es No. 22, con un período de tres días, a continuación, No. 22/23/24 está en clase A / B / C, y No. 19/20/21 está en Clase A. / clase B / clase C, antes y después de la fecha y así sucesivamente.
att_shift_isShiftWithinMonthRemark1=turnos intramensuales, entonces el ciclo sólo recorrerá el último día de cada mes y no se programará consecutivamente a lo largo de los meses.
att_shift_isShiftWithinMonthRemark2=Turnos no mensuales: el ciclo se completa un ciclo hasta el último día de cada mes, si un ciclo no ha terminado, continúe con el siguiente mes, y así sucesivamente;
att_shift_workTypeRemark1=Nota: Cuando se selecciona el tipo de trabajo como horas extra en un día de descanso, la asistencia no se calculará el día de un día festivo.
att_shift_workTypeRemark2=Fin de semana OT, por defecto marca de tiempo extra para descansar día y el ordenador calcula automáticamente las horas extraordinarias. No se requiere la aplicación de horas extras. Las horas de trabajo del día se registran como las horas extraordinarias, y la asistencia no se cuenta durante las vacaciones.
att_shift_workTypeRemark3=OT vacaciones, marca de tiempo extra por defecto a los días festivos y el ordenador calcula automáticamente las horas extraordinarias, sin necesidad de tramitar las horas extraordinarias y las horas de trabajo del día se registran como las horas extraordinarias;
att_shift_attendanceModeRemark1=Todas no cuentan como horas extraordinarias tempranas o tardías, excepto las de entrada y salida normales de los turnos, por ejemplo.
att_shift_attendanceModeRemark2=1. No marcar o marcar válidamente una sola vez al día, no se contabiliza las horas extras.
att_shift_attendanceModeRemark3=2. Tipo de trabajo: trabajo normal, modo de asistencia: sin fichar, entonces el periodo programado del día se considera como horas válidas de trabajo.
att_shift_periodStartMode=tipo de inicio de período
att_shift_periodStartModeByPeriod=fecha de inicio del período
att_shift_periodStartModeBySch=Fecha de inicio del turno
att_shift_addTimeSlotToShift=Agregar una franja horaria para este turno
#=====================================================================
#分组
att_group_editGroup=Editar personal para el Grupo
att_group_browseGroupPerson=Navegar por el Grupo de Personal
att_group_list=Lista de grupos
att_group_placeholderNo=Se recomienda comenzar con G, tales como G1
att_group_placeholderName=Se recomienda comenzar con G o terminar con grupo.
att_widget_deptHint=Nota: Importar todo el personal del departamento seleccionado
att_widget_searchType=Búsqueda condicional
att_widget_noPerson=No selecciono personal
#分组排班
#部门排班
att_deptSch_existsDept=Existe una programación departamental para este departamento y no se puede borrar el departamento.
#人员排班
att_personSch_view=Ver Programación de Personal
#临时排班
att_schedule_type=Tipo de horario
att_schedule_tempType=Tipo temporal
att_schedule_normal=Horario normal
att_schedule_intelligent=Búsqueda inteligente de clases
att_tempSch_scheduleType=Tipo de programación
att_tempSch_startDate=Fecha de inicio
att_tempSch_endDate=Fecha final
att_tempSch_attendanceMode=Modo de asistencia
att_tempSch_overtimeMode=Modo de tiempo extra
att_tempSch_overtimeRemark=Marcador de tiempo extra
att_tempSch_existsDept=Existe una programación departamental temporal para este departamento y no se puede eliminar
att_schedult_opAddTempSch=Nuevo Turno Temporal
att_schedule_cleanEndDate=Hora de finalización vacía
att_schedule_selectOne=El horario normal sólo puede elegir un turno!
att_schedule_selectPerson=Seleccione primero a la persona.
att_schedule_selectDept=Por favor, seleccione el departamento primero!
att_schedule_selectGroup=Por favor, seleccione el grupo primero!
att_schedule_selectOneGroup=Sólo un grupo puede ser seleccionado!
att_schedule_arrange=Seleccione un turno
att_schedule_leave=Salir
att_schedule_trip=Viaje
att_schedule_out=Ausencia
att_schedule_off=Descanso
att_schedule_makeUpClass=Adjuntar
att_schedule_class=Ajustar
att_schedule_holiday=Vacaciones
att_schedule_offDetail=Transferencia de vacaciones
att_schedule_makeUpClassDetail= Anexar La asistencia
att_schedule_classDetail=utilice cambio
att_schedule_holidayDetail=Vacaciones
att_schedule_noSchDetail=No programado
att_schedule_normalDetail=Normal
att_schedule_normalSchInfo=Frecuencia: frecuencia diaria no cruzada
att_schedule_multipleInterSchInfo=Turnos separados por comas: varios turnos Inter diarios
att_schedule_inderSchFirstDayInfo=Desplazamiento hacia atrás en la parrilla: los desplazamientos entre días se registran como el primer día
att_schedule_inderSchSecondDayInfo=Desplazamiento hacia delante entre registros: los desplazamientos entre días se registran como el segundo día
att_schedule_timeConflict=Entra en conflicto con las franjas horarias existentes y no permite guardarlas.
#=====================================================================
att_excp_notExisetPerson=Persona no existe!
att_excp_leavePerson=Falla del personal!
#补签单
att_sign_signTime=Hora de Registro
att_sign_signDate=Fecha de Registro
#请假
att_leave_arilName=Motivo de Salida
att_leave_image=Solicitud de vacaciones
att_leave_imageShow=Sin fotos
att_leave_imageType=Error: Formato incorrecto, los formatos de imagen admitidos son: JPEG, GIF, PNG.
att_leave_imageSize=Error:¡La imagen seleccionada es demasiado grande, el tamaño de imagen se admite hasta 4M!
att_leave_leaveLongDay=Duración (días)
att_leave_leaveLongHour=Duración (hora)
att_leave_leaveLongMinute=Duración (minutos)
att_leave_endNoLessAndEqualStart=La hora de finalización no puede ser menor o igual que la hora de inicio
att_leave_typeNameNoExsists=El nombre falso de la clase no existe
att_leave_startNotNull=La hora de inicio no puede estar vacía
att_leave_endNotNull=La hora de finalización no puede estar vacía
att_leave_typeNameConflict=El nombre entra en conflicto con el nombre del estado de asistencia
#出差
att_trip_tripLongDay=Duración del viaje (días)
att_trip_tripLongMinute=Duración del viaje (minutos)
att_trip_tripLongHour=El tiempo de viaje (horas)
#外出
att_out_outLongDay=Duración de las salidas (días)
att_out_outLongMinute=Duración de la salida (minutos)
att_out_outLongHour=Duración de las salidas (en horas)
#加班
att_overtime_type=Tipo Tiempo Extra
att_overtime_normal=Tiempo Extra Normal
att_overtime_rest=Tiempo Extra Fin de Semana
att_overtime_overtimeLong=Cantidad de Horas Extra (min)
att_overtime_overtimeHour=Las horas extras (horas)
att_overtime_notice=tiempo de aplicación de las horas extraordinarias no está permitido para más de un día!
att_overtime_minutesNotice=Las solicitudes de horas extraordinarias no pueden ser inferiores al mínimo de horas extraordinarias.
#调休补班
att_adjust_type=Tipo de Ajuste
att_adjust_adjustDate=Ajustar Fecha
att_adjust_shiftName=Anexar o recuperar turnos
att_adjust_selectClass=Por favor, seleccione el nombre del turno que desea validar
att_shift_notExistShiftWorkDate={1} programación de turnos en {0} y el resto no tiene permitido solicitar turnos de recuperación.
att_adjust_shiftPeriodStartMode=El turno seleccionado para el turno de recuperación, si la fecha de inicio es por turno, el valor predeterminado es el 0
att_adjust_shiftNameNoNull=Los turnos de recuperación no pueden estar vacíos
att_adjust_shiftNameNoExsist=No existen turnos de reposición
#调班
att_class_type=Tipo de Ajuste
att_class_sameTimeMoveShift=Ajustar el cambio de personal en el mismo día
att_class_differenceTimeMoveShift=Turnos individuales en fechas diferentes
att_class_twoPeopleMove=intercambian dos personas
att_class_moveDate=Ajustar Fecha
att_class_shiftName=Nombre Original del Horario
att_class_moveShiftName=El nuevo cambio ajustado no puede estar vacío.
att_class_movePersonPin=Ajuste Personal ID
att_class_movePersonName=Ajuste Nombre Personal
att_class_movePersonLastName=Ajuste Personal Apellido
att_class_moveDeptName=Ajuste Nombre de departamento
att_class_personPin=ID de Usuario
att_class_shiftNameNoNull=El nuevo cambio ajustado no puede estar vacío.
att_class_personPinNoNull=La ID de Usuario de la nueva persona no puede estar vacía!
att_class_isNotExisetSwapPersonPin=El número de personal no existe, por favor, añádalo de nuevo.
att_class_personNoSame=No se puede ajustar para la misma persona, por favor intente de nuevo.
att_class_outTime=La fecha de ajuste y la fecha de alineación no pueden estar separadas por más de un mes.
att_class_shiftNameNoExsist=No existe ajuste de turno
att_class_swapPersonNoExisist=El emparejador no existe
att_class_dateNoSame=Turnos individuales en fechas diferentes, no en las mismas fechas
#=====================================================================
#节点
att_node_name=Nodo
att_node_type=Tipo de nodo
att_node_leader=Líder directa
att_node_leaderNode=Nodo líder directa
att_node_person=Persona designada
att_node_position=Asignar Posición
att_node_choose=Seleccione Posición
att_node_personNoNull=El personal no puede estar vacío!
att_node_posiitonNoNull=Posición no puede estar vacía
att_node_placeholderNo=Se recomienda comenzar con N, tales como N01.
att_node_placeholderName=Se recomienda empezar con un cargo o nombre y terminar con un nodo, por ejemplo, nodo supervisor
att_node_searchPerson=Introduzca sus criterios de búsqueda
att_node_positionIsExist=La posición ya existe en los datos del nodo, por favor seleccione la posición de nuevo.
#流程
att_flow_type=Tipo de flujo
att_flow_rule=Regla del flujo
att_flow_rule0=Menor o igual a 1 día
att_flow_rule1=Más de 1 día y menos de o igual a 3 días
att_flow_rule2=Más de 3 días y de menos de o igual a 7 días
att_flow_rule3=Más de 7 días
att_flow_node=Nodos de aprobación
att_flow_start=Flujo de inicio
att_flow_end=Finalizar el proceso
att_flow_addNode=Añadir nodo
att_flow_placeholderNo=Se recomienda comenzar con F, tales como F01.
att_flow_placeholderName=Tipo sugerido para empezar y terminar con un proceso, por ejemplo, proceso de tiempo extra
att_flow_tips=Nota: El orden de aprobación de los nodos es de arriba hacia abajo, y se puede arrastrar la clase después de la selección.
#申请
att_apply_personPin=Número de solicitante
att_apply_type=Tipo de excepción
att_apply_flowStatus=Estado general del proceso
att_apply_start=El inicio de una aplicación
att_apply_flowing=Pendiente
att_apply_pass=pasado
att_apply_over=Fin
att_apply_refuse=Rechazado
att_apply_revoke=Revocar
att_apply_except=Excepción
att_apply_view=Ver detalles
att_apply_leaveTips=La persona tiene una solicitud de ausencia durante este período de tiempo!
att_apply_tripTips=El personal tiene solicitudes de viaje durante ese periodo.
att_apply_outTips=¡El personal tiene una solicitud de permiso durante ese período de tiempo!
att_apply_overtimeTips=El personal tiene solicitudes de horas extras durante ese periodo de tiempo.
att_apply_adjustTips=¡El personal tiene una solicitud de reasignación para cubrir turnos durante ese periodo de tiempo!
att_apply_classTips=El personal tiene una aplicación de turno durante este período de tiempo!
#审批
att_approve_wait=Aprobación pendiente
att_approve_refuse=No pasó
att_approve_reason=Razón
att_approve_personPin=aprobador ID
att_approve_personName=Nombre aprobador
att_approve_person=Aprobador
att_approve_isPass=Si aprueba o no?
att_approve_status=Estado del nodo actual
att_approve_tips=Un registro ya existe en el proceso en ese momento y no puede repetirse
att_approve_tips2=El nodo de flujo no se ha configurado, por favor, póngase en contacto con el administrador.
att_approve_offDayConflicts={0} no está programado en {1} y esto genera conflicto
att_approve_shiftConflicts={0} ya tiene un turno en {1} y no permite solicitar turnos en la jornada laboral.
att_approve_shiftNoSch={0} ¡No se permite la aplicación de turnos cuando el turno no está programado para {1}!
att_approve_classConflicts=Ninguna solicitud de cambio está permitido en la fecha programada
att_approve_selectTime=Después de seleccionar la hora, el proceso se juzgará de acuerdo con las normas
att_approve_withoutPermissionApproval=Hay un flujo de trabajo sin permiso para aprobar, ¡verifique!
#=====================================================================
#考勤计算
att_op_calculation=Cálculo de asistencia
att_op_calculation_notice=Los datos de asistencia ya se están calculando en segundo plano, inténtelo más tarde.
att_op_calculation_leave=Incluido el personal resignados
att_statistical_choosePersonOrDept=Por favor, seleccione Departamento o personal!
att_statistical_sureCalculation=¿Está seguro que desea realizar cálculos de asistencia?
att_statistical_filter=¡Las condiciones del filtro están listas!
att_statistical_initData=Inicialización de la base de datos completa!
att_statistical_exception=Inicialización de datos de excepción completa!
att_statistical_error=El cálculo de asistencia fallido
att_statistical_begin=Empezar a calcular!
att_statistical_end=Terminar el cálculo!
att_statistical_noticeTime=Rango de tiempo opcional de asistencia: ¡los primeros dos meses del día!
att_statistical_remarkHoliday=H
att_statistical_remarkClass=do
att_statistical_remarkNoSch=NS
att_statistical_remarkRest=R
#原始记录表
att_op_importAccRecord=Importación registro de control de acceso
att_op_importParkRecord=registro de importación de estacionamiento
att_op_importInsRecord=Registros de importación faceKiosk
att_op_importPidRecord=Importación de registros de personal
att_op_importVmsRecord=Importación de grabaciones de vídeo
att_op_importUSBRecord=Importar registros de memoria USB
att_transaction_noAccModule=Sin módulo de control de acceso!
att_transaction_noParkModule=Sin módulo de estacionamiento!
att_transaction_noInsModule=Sin módulo de faceKiosk!
att_transaction_noPidModule=Módulo certificados personales no!
att_transaction_exportRecord=Exportar registros originales
att_transaction_exportAttPhoto=Exportar fotos de asistencia
att_transaction_fileIsTooLarge=El archivo exportado es demasiado grande, reduzca el intervalo de fechas o filtre los valores
att_transaction_exportDate=Fecha de exportación
att_statistical_attDatetime=Tiempo de asistencia
att_statistical_attPhoto=La asistencia de fotos
att_statistical_attDetail=Detalles de asistencia
att_statistical_acc=Dispositivo de control de acceso
att_statistical_att=Dispositivo de asistencia
att_statistical_park=Cámara LPR
att_statistical_faceRecognition=dispositivo de reconocimiento facial
att_statistical_app=Dispositivos móviles
att_statistical_vms=Equipo de vídeo
att_statistical_psg=Equipo de canal
att_statistical_dataSources=Fuentes de datos
att_transaction_SyncRecord=Sincronizar registros de asistencia
#日打卡详情表
att_statistical_dayCardDetail=Detalles del check-in
att_statistical_cardDate=Fecha de entrada en vigor
att_statistical_cardNumber=Número de veces registradas
att_statistical_earliestTime=Hora mas temprana
att_statistical_latestTime=última Hora
att_statistical_cardTime=Hora de Registro
#请假汇总表
att_statistical_leaveDetail=Detalles de la ausencia
#日明细报表
att_statistical_attDate=Fecha de Asistencia
att_statistical_week=Semana
att_statistical_shiftInfo=Información de turno
att_statistical_shiftTimeData=Horas de inicio y fin
att_statistical_cardValidData=Hora de Registro
att_statistical_cardValidCount=Número de veces registradas
att_statistical_lateCount=Número de llegadas tardías
att_statistical_lateMinute=Minuto finales
att_statistical_earlyCount=Número de salidas tempranas
att_statistical_earlyMinute=Salida anticipada (minutos)
att_statistical_countData=Los tiempos de datos
att_statistical_minuteData=Datos en minutos
att_statistical_attendance_minute=Asistencia (minutos)
att_statistical_overtime_minute=Horas extras (minutos)
att_statistical_unusual_minute=Anormales (minutos)
#月明细报表
att_monthdetail_should_hour=Debe (tiempo)
att_monthdetail_actual_hour=Tiempo actual)
att_monthdetail_valid_hour=Tiempo valido)
att_monthdetail_absent_hour=Tiempo de ausentismo
att_monthdetail_leave_hour=Permiso de ausencia (horas)
att_monthdetail_trip_hour=Viaje de negocios (por hora)
att_monthdetail_out_hour=Salir (cada hora)
att_monthdetail_should_day=Debe (días)
att_monthdetail_actual_day=Día actual
att_monthdetail_valid_day=Dia efectivo)
att_monthdetail_absent_day=Absentismo laboral (días)
att_monthdetail_leave_day=Día de descanso)
att_monthdetail_trip_day=Viaje de negocios (días)
att_monthdetail_out_day=Marcación externa (días)
#月统计报表
att_statistical_late_minute=Duración (minutos)
att_statistical_early_minute=Duración (minutos)
#部门统计报表
#年度统计报表
att_statistical_should=Debería
att_statistical_actual=Real
att_statistical_valid=Válido
att_statistical_numberOfTimes=Veces
att_statistical_usually=Día laborable
att_statistical_rest=Fin de semana
att_statistical_holiday=Vacaciones
att_statistical_total=Total
att_statistical_month=Estadísticas del Mes
att_statistical_year=Estadísticas de Año
att_statistical_attendance_hour=Asistencia (horas)
att_statistical_attendance_day=Asistencia (días)
att_statistical_overtime_hour=Las horas extraordinarias (horas)
att_statistical_unusual_hour=Excepción (horas)
att_statistical_unusual_day=Anormal (días)
#考勤设备参数
att_deviceOption_query=Ver los parámetros del equipo
att_deviceOption_noOption=No hay información de parámetros, por favor obtener los parámetros del dispositivo primero
att_deviceOption_name=Nombre del parámetro
att_deviceOption_value=Valores de los parámetros
att_deviceOption_UserCount=Número de usuario
att_deviceOption_MaxUserCount=Número máximo de usuarios
att_deviceOption_FaceCount=Número de plantillas faciales actuales
att_deviceOption_MaxFaceCount=Número máximo de plantillas faciales
att_deviceOption_FacePhotoCount=Número actual de imágenes faciales
att_deviceOption_MaxFacePhotoCount=Número máximo de imágenes faciales
att_deviceOption_FingerCount=Número de plantillas de huellas digitales actuales
att_deviceOption_MaxFingerCount=Número máximo de plantillas de huellas digitales
att_deviceOption_FingerPhotoCount=Número de imágenes de huellas digitales actuales
att_deviceOption_MaxFingerPhotoCount=Número máximo de imágenes de huellas digitales
att_deviceOption_FvCount=Número de plantillas de venas de dedo actuales
att_deviceOption_MaxFvCount=Número máximo de plantillas de venas de los dedos
att_deviceOption_FvPhotoCount=Número de imágenes actuales de la vena del dedo
att_deviceOption_MaxFvPhotoCount=Número de imágenes de venas de los dedos
att_deviceOption_PvCount=Número de plantillas de palma actuales
att_deviceOption_MaxPvCount=Número máximo de plantillas de palma
att_deviceOption_PvPhotoCount=Imágenes actuales de la palma
att_deviceOption_MaxPvPhotoCount=Número máximo de imágenes de palma
att_deviceOption_TransactionCount=Número actual de registros
att_deviceOption_MaxAttLogCount=Número máximo de registros
att_deviceOption_UserPhotoCount=Fotos actuales de los usuarios
att_deviceOption_MaxUserPhotoCount=Número máximo de fotos de los usuarios
att_deviceOption_FaceVersion=Versión del algoritmo de reconocimiento facial
att_deviceOption_FPVersion=Reconocimiento de la huella Versión del algoritmo
att_deviceOption_FvVersion=Venas del dedo algoritmo de reconocimiento Versión
att_deviceOption_PvVersion=Versión del algoritmo de reconocimiento de la palma de la mano
att_deviceOption_FWVersion=Versión de firmware
att_deviceOption_PushVersion=Versión PUSH (comunicación)
#=====================================================================
#API
att_api_areaCodeNotNull=Número de área no puede estar vacía
att_api_pinsNotNull=Pin de datos no se le permite estar vacío
att_api_pinsOverSize=longitud de datos Pin no se le permite exceder 500
att_api_areaNoExist=No existe el area
att_api_sign=Suplemento
#=====================================================================
#休息时间段
att_leftMenu_breakTime=Descanso
att_breakTime_startTime=Hora de inicio
att_breakTime_endTime=Hora de finalización
#=====================================================================
#导入U盘记录
att_import_uploadFileSuccess=Archivo cargado con éxito, comenzando a analizar los datos del archivo, por favor espere...
att_import_resolutionComplete=Después de terminado el análisis, iniciar la actualización de la base de datos.
att_import_snNoExist=El dispositivo de tiempo y asistencia correspondiente al archivo importado no existe, vuelva a seleccionar el archivo.
att_import_fileName_msg=El formato del nombre del archivo importado requiere que el número de serie del dispositivo empiece por un guión bajo 
att_import_notSupportFormat=Este formato no es compatible
att_import_selectCorrectFile=Por favor, seleccione el archivo de formato correcto!
att_import_fileFormat=Formato de archivo
att_import_targetFile=Archivo de destino
att_import_startRow=Número de filas iniciales de la tabla
att_import_startRowNote=La primera línea del formato de datos son los datos importados, por favor compruebe el archivo antes de importar.
att_import_delimiter=Separador
#=====================================================================
#设备操作日志
att_device_op_log_op_type=código de operación
att_device_op_log_dev_sn=Número de serie del equipo
att_device_op_log_op_content=Contenido de la operación
att_device_op_log_operator_pin=Número de operador
att_device_op_log_operator_name=Nombre del operador
att_device_op_log_op_time=Tiempo de operación
att_device_op_log_op_who_value=valor de objeto Operación
att_device_op_log_op_who_content=Descripción de la operación
att_device_op_log_op_value1=objeto Operación 2
att_device_op_log_op_value_content1=Operación de descripción del objeto 2
att_device_op_log_op_value2=objeto Operación 3
att_device_op_log_op_value_content2=Operación de descripción del objeto 3
att_device_op_log_op_value3=objeto Operación 4
att_device_op_log_op_value_content3=Operación de descripción del objeto 4
#操作日志的操作类型
att_device_op_log_opType_0=Encendido
att_device_op_log_opType_1=Apagar
att_device_op_log_opType_2=Fallo en la verificación
att_device_op_log_opType_3=Alarma
att_device_op_log_opType_4=Entrar en el menú
att_device_op_log_opType_5=Cambiar ajustes
att_device_op_log_opType_6=huella digital inscribirse
att_device_op_log_opType_7=Registro contraseña
att_device_op_log_opType_8=tarjeta de registro HID
att_device_op_log_opType_9=Borrar usuario
att_device_op_log_opType_10=Borrar la huella digital
att_device_op_log_opType_11=Eliminar la contraseña
att_device_op_log_opType_12=RF tarjeta de borrado
att_device_op_log_opType_13=Borrar datos
att_device_op_log_opType_14=Crear tarjeta de MF
att_device_op_log_opType_15=tarjeta de registro MF
att_device_op_log_opType_16=tarjeta de registro MF
att_device_op_log_opType_17=Borrar registro de la tarjeta MF
att_device_op_log_opType_18=contenido de la tarjeta MF
att_device_op_log_opType_19=Mover los datos de registro a la tarjeta
att_device_op_log_opType_20=Copiar los datos de registro en el dispositivo
att_device_op_log_opType_21=Fijar tiempo
att_device_op_log_opType_22=Ajustes de fábrica
att_device_op_log_opType_23=Borrar registros de entrada y salida
att_device_op_log_opType_24=privilegios de administrador claras
att_device_op_log_opType_25=la configuración del grupo de control de acceso Modificar
att_device_op_log_opType_26=Modificar la configuración de acceso de usuario
att_device_op_log_opType_27=Modificar el período de tiempo de acceso
att_device_op_log_opType_28=Modificar la configuración de combinación de desbloqueo
att_device_op_log_opType_29=Desbloquear
att_device_op_log_opType_30=Registrar nuevos usuarios
att_device_op_log_opType_31=atributos de huellas dactilares de cambio
att_device_op_log_opType_32=Alarma de coacción
att_device_op_log_opType_34=Antipassback
att_device_op_log_opType_35=Eliminar fotos de asistencia
att_device_op_log_opType_36=Modificar información del usuario
att_device_op_log_opType_37=Vacaciones
att_device_op_log_opType_38=Restaurar los datos
att_device_op_log_opType_39=Copia de seguridad
att_device_op_log_opType_40=Cargar a USB
att_device_op_log_opType_41=descarga de USB
att_device_op_log_opType_42=cifrado de registro de asistencia del disco de U
att_device_op_log_opType_43=Eliminar registro después de la descarga de disco USB con éxito
att_device_op_log_opType_53=interruptor de salida
att_device_op_log_opType_54=sensor de la puerta
att_device_op_log_opType_55=Alarma
att_device_op_log_opType_56=Restaurar parámetros
att_device_op_log_opType_68=Usuario registrado foto
att_device_op_log_opType_69=Modificar fotos de los usuarios
att_device_op_log_opType_70=Modificar el nombre de usuario
att_device_op_log_opType_71= Modificar permisos de usuario
att_device_op_log_opType_76=red de modificar la configuración de IP
att_device_op_log_opType_77=Modificar la configuración de red enmascaran
att_device_op_log_opType_78=Modificar la configuración de red de puerta de enlace
att_device_op_log_opType_79=Modificar la configuración de red DNS
att_device_op_log_opType_80=Modificar la contraseña de configuración de conexión
att_device_op_log_opType_81=Modificar la configuración de conexión del dispositivo de identificación
att_device_op_log_opType_82=Modificar la dirección del servidor de nube
att_device_op_log_opType_83=Modificar el puerto del servidor en nube
att_device_op_log_opType_87=Ajustes del registro de control de acceso
att_device_op_log_opType_88=Modificar bandera parámetro de rostro
att_device_op_log_opType_89=Modificación de los indicadores de parámetros de huellas dactilares
att_device_op_log_opType_90=Modificar el parámetro flag venas de los dedos
att_device_op_log_opType_91=Modificar bandera parámetro de palma
att_device_op_log_opType_92=Actualizar logo desde USB
att_device_op_log_opType_100=Modificar información de la tarjeta RF
att_device_op_log_opType_101=Registro de rostro
att_device_op_log_opType_102=Modificar los permisos del personal
att_device_op_log_opType_103=Eliminar permisos de personal
att_device_op_log_opType_104=Añadir permisos del personal
att_device_op_log_opType_105=Eliminar registros de control de acceso
att_device_op_log_opType_106=Eliminar el rostro
att_device_op_log_opType_107=Eliminar fotos de personal 
att_device_op_log_opType_108=Modificar parámetros
att_device_op_log_opType_109=Seleccione WIFI SSID
att_device_op_log_opType_110=Proxy activado
att_device_op_log_opType_111=modificación proxyip
att_device_op_log_opType_112=modificación de puerto del servidor proxy
att_device_op_log_opType_113=Modificar la contraseña de la persona
att_device_op_log_opType_114=Modificar información de la cara
att_device_op_log_opType_115=Modificar la contraseña del operador
att_device_op_log_opType_116=la configuración de control de acceso del curriculum vitae
att_device_op_log_opType_117=error de entrada de contraseña operador
att_device_op_log_opType_118=cerradura de la contraseña del operador
att_device_op_log_opType_120=Modificar longitud de datos de tarjeta legica
att_device_op_log_opType_121=Registrar vena de dedo
att_device_op_log_opType_122=Modificar vena del dedo
att_device_op_log_opType_123=Eliminar vena del dedo
att_device_op_log_opType_124=Registrar la plantilla de la palma
att_device_op_log_opType_125=Modificar impresión de palma
att_device_op_log_opType_126=Eliminar impresión de palma
#操作对象描述
att_device_op_log_content_pin=ID de usuario:
att_device_op_log_content_alarm=Alarma:
att_device_op_log_content_alarm_reason=Alarma razón:
att_device_op_log_content_update_no=Modificar número de artículo:
att_device_op_log_content_update_value=Modificar el valor:
att_device_op_log_content_finger_no=Número de huellas digitales:
att_device_op_log_content_finger_size=longitud plantilla de huellas dactilares:
#=====================================================================
#工作流
att_flowable_datetime_to=a
att_flowable_todomsg_leave=Aprobación de la licencia
att_flowable_todomsg_sign=Aprobación de registros anexados
att_flowable_todomsg_overtime=autorización de horas extras
att_flowable_notifymsg_leave=Notificación de solicitud de permiso
att_flowable_notifymsg_sign=la notificación de registro de datos anexados
att_flowable_notifymsg_overtime=notificación de las horas extraordinarias
att_flowable_shift=Cambio:
att_flowable_hour=hora
att_flowable_todomsg_trip=aprobación viaje de negocios
att_flowable_notifymsg_trip=Viaje de negocios
att_flowable_todomsg_out=Aprobación de salida
att_flowable_notifymsg_out=Notificación de salidas
att_flow_apply=Solicitud
att_flow_applyTime=Período de solicitud
att_flow_approveTime=Tiempo de aprobación
att_flow_operateUser=Crítico
att_flow_approve=Aprobación
att_flow_approveComment=Anotaciones
att_flow_approvePass=Los resultados de aprobación
att_flow_status_processing=Aprobación
#=====================================================================
#biotime
att_h5_pers_personIdNull=Identificación del empleado no puede estar vacía
att_h5_attPlaceNull=Registro de entrada ubicación no puede estar vacía
att_h5_attAreaNull=área de asistencia no puede estar vacía
att_h5_pers_personNoExist=número de empleado no existe
att_h5_signRemarkNull=Observaciones no puede estar vacía
att_h5_common_pageNull=Parámetros de paginación incorrectos
att_h5_taskIdNotNull=El id del nodo de tarea no puede estar vacío
att_h5_auditResultNotNull=Resultado la aprobación no puede estar vacía
att_h5_latLongitudeNull=Longitud y latitud no puede estar vacía
att_h5_pers_personIsNull=Identificación del empleado no existe
att_h5_pers_personIsNotInArea=La persona no ha determinado la zona
att_h5_mapApiConnectionsError=Mapa error de conexión API
att_h5_googleMap=Mapa de Google
att_h5_gaodeMap=Gaode Map
att_h5_defaultMap=mapa predeterminado
att_h5_shiftTime=Programación de tiempo
att_h5_signTimes=tiempo de reposición
att_h5_enterKeyWords=Por favor, introduzca las palabras clave:
att_h5_mapSet=configuración del mapa de asistencia
att_h5_setMapApiAddress=Ajustar el parámetro mapa
att_h5_MapSetWarning=Al cambiar el mapa, la dirección de inicio de sesión del terminal móvil ingresada no se corresponde con la latitud y longitud, ¡modifíquela con precaución!
att_h5_mapSelect=selección de mapa
att_h5_persNoHire=El empleado aún no estaba contratado en ese momento
att_slef_apiKey=CLAVE API
att_self_apiJsKey=API-JS-KEY
att_self_noComputeAtt=La asistencia del día no ha tenido en cuenta todavía.
att_self_noSignRecord=No se registra ninguna marcación del día
att_self_imageUploadError=Carga de imágenes falló
att_self_attSignAddressAreaIsExist=Ya hay registro de entrada en puntos en la zona
att_self_signRuleIsError=El tiempo de reposición actual no está dentro del tiempo de reposición permitida.
att_self_signAcrossDay=La programación entre días no se puede recuperar.
att_self_todaySignIsExist=¡Ya hay un sustituto programado hoy!
att_self_signSetting=ajuste de signo
att_self_allowSign=Permitir signo:
att_self_allowSignSuffix=Registros de asistencia en (días)
att_self_onlyThisMonth=Sólo este mes
att_self_allowAcrossMonth=Permitido por meses
att_self_thisTimeNoSch=No hay ningún cambio en el actual período de tiempo!
att_self_revokeReason=Motivo de la revocación:
att_self_revokeHint=Por favor, introduzca el motivo de cancelación dentro de 20 palabras para su revisión
att_self_persSelfLogin=Inicio de sesión automático del personal
att_self_isOpenSelfLogin=Activar o no el portal de acceso de autoservicio del personal
att_self_applyAndWorkTimeOverlap=Hay solapamiento entre el horario de solicitud y el horario de trabajo
att_apply_DurationIsZero=La duración de la solicitud es 0 y no se permiten solicitudes
att_sign_mapWarn=El mapa no se ha podido cargar, compruebe su conexión a Internet y el valor de la CLAVE del mapa.
att_admin_applyWarn=¡La operación falló, hay personas que no están programadas o el tiempo de aplicación no está dentro del alcance de la programación!({0})
att_self_getPhotoFailed=La imagen no existe
att_self_view=Vista
# 二维码
att_param_qrCodeUrl=código QR Url
att_param_qrCodeUrlHref=Dirección del servidor: puerto
att_param_appAttQrCode=Código QR de asistencia móvil
att_param_timingFrequency=Intervalo de tiempo: 5-59 minutos o 1-24 horas
att_sign_signTimeNotNull=El tiempo de registro de datos anexados no puede estar vacía
att_apply_overLastMonth=Se inician más solicitudes que el mes pasado
att_apply_withoutDetail=No hay detalles del proceso
att_flowable_noAuth=Utilice una cuenta de super administrador para ver
att_apply_overtimeOverMaxTimeLong=La duración de las horas extraordinarias es mayor que la duración máxima
# 考勤设置参数符号
att_other_arrive=√
att_other_late=Retardo
att_other_early=Te-pr-no
att_other_absent=□
att_other_noSignIn=[
att_other_noSignOff=]
att_other_leave=∆
att_other_overtime=TE
att_other_off=○
att_other_classes=●
att_other_trip=T
att_other_out=G
att_other_incomplete=├
att_other_outcomplete=┤
# 服务器下发命令
att_devCmd_submitTime=Tiempo de envío
att_devCmd_returnedResult=Resultado devuelto
att_devCmd_returnTime=Hora de regreso
att_devCmd_content=Contenido del comando
att_devCmd_clearCmd=Borrar lista de comandos
# 实时点名
att_realTime_selectDept=Por favor seleccione un departamento
att_realTime_noSignPers=Persona no registrada
att_realTime_signPers=Registrado
att_realTime_signMonitor=Monitoreo de inicio de sesión
att_realTime_signDateTime=Hora de entrada
att_realTime_realTimeSet=Configuración de pasar lista en tiempo real
att_realTime_openRealTime=Habilitar pase de lista en tiempo real
att_realTime_rollCallEnd=Fin del paso de lista en tiempo real
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=Programado
att_personSch_cycleSch=Programación de ciclos
att_personSch_cleanCycleSch=Limpiar programación de los turnos
att_personSch_cleanTempSch=Borrar horario temporal
att_personSch_personCycleSch=Programación del ciclo del personal
att_personSch_deptCycleSch=Programación de ciclo del departamento
att_personSch_groupCycleSch=Programación del ciclo del grupo
att_personSch_personTempSch=Programación temporal del personal
att_personSch_deptTempSch=Lista de turnos temporal por departamento
att_personSch_groupTempSch=Programa temporal del grupo
att_personSch_checkGroupFirst=¡Por favor, marque el grupo de la izquierda o las personas de la lista de la derecha para operar!
att_personSch_sureDeleteGroup=¿Está seguro de eliminar {0} y la programación correspondiente al grupo?
att_personSch_sch=Horario
att_personSch_delSch=Eliminar horario
#考勤计算
att_statistical_sureAllCalculate=¿Está seguro de realizar el cálculo de asistencia para todo el personal?
#异常管理
att_exception_downTemplate=Descargar e importar plantilla
att_exception_signImportTemplate=Plantilla de importación de solicitud de permiso
att_exception_leaveImportTemplate=Plantilla de importación de extras
att_exception_overtimeImportTemplate=Plantilla de importación de horas extra
att_exception_adjustImportTemplate=Ajustar plantilla de importación
att_exception_cellDefault=Campos no requeridos
att_exception_cellRequired=Campo obligatorio
att_exception_cellDateTime=Campo obligatorio, el formato de hora es aaaa-MM-dd HH: mm: ss, como: 2023-07-07 08:30:00
att_exception_cellLeaveTypeName=Campo obligatorio, como: 'licencia personal', 'licencia por matrimonio', 'licencia por maternidad', 'licencia por enfermedad', 'licencia anual', 'licencia por duelo', 'licencia familiar', 'licencia por lactancia', 'viaje de negocios', 'Ausencia especial"
att_exception_cellOvertimeSign=Campo obligatorio, como: 'horas extra normales', 'horas extra en días de descanso', 'horas extra en días festivos'
att_exception_cellAdjustType=Campos obligatorios, por ejemplo, "traslado", "trabajo compensatorio"
att_exception_cellAdjustDate=Campo obligatorio, el formato de hora es aaaa-MM-dd, como: 2020-07-07
att_exception_cellShiftName=Campo obligatorio cuando el Tipo de Ajuste es Recuperar Turno
att_exception_refuse=Rechazar
att_exception_end=Fin de la excepción
att_exception_delete=Eliminar
att_exception_stop=Pausa
#时间段
att_timeSlot_normalTimeAdd=Agregar intervalo de tiempo normal
att_timeSlot_elasticTimeAdd=Nuevo horario flexible
#班次
att_shift_addRegularShift=Agregar turno regular
att_shift_addFlexibleShift=Agregar turno flexible
#参数设置
att_param_notLeaveSetting=Configuración de cálculo no falsa
att_param_smallestUnit=Unidad mínima
att_param_workDay=Día laboral
att_param_roundingControl=Control de redondeo
att_param_abort=Abajo (descartar)
att_param_rounding=Redondeo
att_param_carry=Arriba (llevar)
att_param_reportSymbol=Símbolo o iniciales de visualización del informe
att_param_convertCountValid=Ingrese un número y solo se permite un decimal
att_other_leaveThing=Objeto
att_other_leaveMarriage=Matrimonio
att_other_leaveBirth=producto
att_other_leaveSick=Enfermo
att_other_leaveAnnual=año
att_other_leaveFuneral=Funeral
att_other_leaveHome=Explorar
att_other_leaveNursing=Enfermería
att_other_leavetrip=Diferencia
att_other_leaveout=otro
att_common_schAndRest=Programar y descansar
att_common_timeLongs=Duración del tiempo
att_personSch_checkDeptOrPersFirst=Para ello, marque los departamentos de la izquierda o las personas de la lista de la derecha.
att_personSch_checkCalendarFirst=¡Seleccione la fecha que necesita programar primero!
att_personSch_cleanCheck=Borrar cheque
att_personSch_delTimeSlot=Borrar el período de tiempo seleccionado
att_personSch_repeatTimeSlotNoAdd=Las franjas horarias en las que existen duplicados no se añaden.
att_personSch_showSchInfo=Mostrar detalles del horario
att_personSch_sureToCycleSch=¿Está seguro de programar {0} periódicamente?
att_personSch_sureToTempSch=¿Confirmar programación temporal para {0}?
att_personSch_sureToCycleSchDeptOrGroup=¿Está seguro de programar periódicamente todo el personal en {0}?
att_personSch_sureToTempSchDeptOrGroup=¿Está seguro de programar temporalmente todo el personal en {0}?
att_personSch_sureCleanCycleSch=¿Está seguro de que desea borrar {0} de {1} a {2}?
att_personSch_sureCleanTempSch=¿Está seguro de que desea borrar {0} el cambio temporal de {1} a {2}?
att_personSch_sureCleanCycleSchDeptOrGroup=¿Confirmar para borrar todo el personal seleccionado {0} de {1} a {2} para la programación de ciclos?
att_personSch_sureCleanTempSchDeptOrGroup=¿Está seguro de que desea borrar el horario temporal de {1} a {2} para todas las personas menores de {0}?
att_personSch_today=Del día
att_personSch_timeSoltName=Nombre del período de tiempo
att_personSch_export=Exportar los horarios del personal
att_personSch_exportTemplate=Exportar plantilla de turno temporal de personal
att_personSch_import=Ajustes de programación temporal del personal
att_personSch_tempSchTemplate=Plantilla de programación de personal temporal
att_personSch_tempSchTemplateTip=Seleccione la hora de inicio y la hora de finalización para programar, descargue la plantilla de programación dentro de esa fecha
att_personSch_opTip=Instrucciones de operación
att_personSch_opTip1=1. Las franjas horarias se pueden programar arrastrándolas con el ratón a una sola fecha dentro del control de calendario.
att_personSch_opTip2=2. Puede hacer doble clic en fechas individuales para programarlas dentro del control de calendario.
att_personSch_opTip3=3. Puede seleccionar varias fechas para programar dentro del control de calendario manteniendo pulsado el ratón para desplazarse.
att_personSch_schRules=Reglas de programación
att_personSch_schRules1=1. Programación cíclica: se siguen las mismas fechas sobrescribiendo la programación anterior, independientemente de que no haya intersección.
att_personSch_schRules2=2. Programación temporal: si se cruzan las mismas fechas, esta última anula la programación temporal anterior, o ambas si no hay intersección.
att_personSch_schRules3=3. Ciclo y temporal: si se cruzan las mismas fechas, el temporal anula al ciclo; si no se cruzan, existen los dos; si la clase de programación del ciclo es la búsqueda inteligente de turnos, el ciclo es anulado directamente por la programación temporal.
att_personSch_schStatus=Programar estado
#左侧菜单-排班管理
att_leftMenu_schDetails=Detalles del horario
att_leftMenu_detailReport=Informe de detalle de asistencia
att_leftMenu_signReport=Tabla de detalles de registros
att_leftMenu_leaveReport=Reporte de ausencias
att_leftMenu_abnormal=Formulario de excepciones de asistencia
att_leftMenu_yearLeaveSumReport=Informe de vacaciones anual
att_leave_maxFileCount=Solo puede agregar 4 fotos como máximo
#时间段
att_timeSlot_add=Establecer intervalo de tiempo
att_timeSlot_select=¡Seleccione un período de tiempo!
att_timeSlot_repeat=¡Se repite el período de tiempo "{0}"!
att_timeSlot_overlapping=¡El período de tiempo "{0}" se superpone con el tiempo de viaje de "{1}"!
att_timeSlot_addFirst=¡Por favor, configure el período de tiempo primero!
att_timeSlot_notEmpty=¡El período de tiempo correspondiente al número de personal {0} no puede estar vacío!
att_timeSlot_notExist=¡El período de tiempo "{1}" correspondiente al número de personal {0} no existe!
att_timeSlot_repeatEx=El período de tiempo "{1}" correspondiente al número de personal {0} se superpone con el tiempo de desplazamiento de "{2}"
att_timeSlot_importRepeat=Se repite el período de tiempo "{1}" correspondiente al número de personal {0}
att_timeSlot_importNotPin=¡No hay ninguna persona con el número {0} en el sistema!
att_timeSlot_elasticTimePeriod=¡ el número de personal {0}, no se puede importar el período de tiempo flexible "{1}"!
#导入
att_import_overData=El número actual de importaciones es {0}, excediendo el límite de 30,000, ¡por favor importe por lotes!
att_import_existIllegalType=¡El {0} importado tiene un tipo ilegal!
#验证方式
att_verifyMode_0=reconocimiento automático
att_verifyMode_1=Solo huella digital
att_verifyMode_2=Verificación de número de trabajo
att_verifyMode_3=Solo contraseña
att_verifyMode_4=Solo tarjeta
att_verifyMode_5=Huella digital o contraseña
att_verifyMode_6=Huella digital o tarjeta
att_verifyMode_7=Tarjeta o contraseña
att_verifyMode_8=Número de trabajo más huella digital
att_verifyMode_9=Huella digital más contraseña
att_verifyMode_10=Tarjeta más huella digital
att_verifyMode_11=Tarjeta más contraseña
att_verifyMode_12=Huella digital más contraseña más tarjeta
att_verifyMode_13=ID de trabajo más huella digital más contraseña
att_verifyMode_14=(Número de trabajo más huella digital) o (tarjeta más huella digital)
att_verifyMode_15=Rostro
att_verifyMode_16=Cara y huella digital
att_verifyMode_17=Cara más contraseña
att_verifyMode_18=Cara más tarjeta
att_verifyMode_19=Cara más huella digital más tarjeta
att_verifyMode_20=Cara más huella digital más contraseña
att_verifyMode_21=Vena del dedo
att_verifyMode_22=Vena del dedo más código
att_verifyMode_23=Vena del dedo más tarjeta
att_verifyMode_24=Vena del dedo más código más tarjeta
att_verifyMode_25=PLantilla de la palma
att_verifyMode_26=Tarjeta más Palma
att_verifyMode_27=Plantillas de palma y rostro
att_verifyMode_28=Huella digital y huella digital
att_verifyMode_29=Impresión de palma más huella digital más rostro
# 工作流
att_flow_schedule=Progreso de la auditoría
att_flow_schedulePass=(Aprobado)
att_flow_scheduleNot=(No aprobado)
att_flow_scheduleReject=(Rechazado)
# 工作时长表
att_workTimeReport_total=Total de horas trabajadas
# 自动导出报表
att_autoExport_startEndTime=Hora de inicio y finalización
# 年假
att_annualLeave_setting=Configuración del saldo de las vacaciones anuales
att_annualLeave_settingTip1=Para utilizar la función de saldo de vacaciones anuales, debe establecer la hora de entrada para cada empleado; cuando no se establece la hora de entrada, las vacaciones anuales restantes de la tabla de saldo de vacaciones anuales del personal se muestran como vacías.
att_annualLeave_settingTip2=Si la fecha actual es mayor que la fecha de emisión de compensación, esta modificación entrará en vigencia el año siguiente; si la fecha actual es menor que la fecha de emisión de compensación, cuando se alcance la fecha de emisión de compensación, se borrará y se volverán a emitir las vacaciones anuales.
att_annualLeave_calculate=Fecha de expedición de la liquidación de vacaciones anuales
att_annualLeave_workTimeCalculate=Calcular según la relación de tiempo de trabajo
att_annualLeave_rule=Regla de tiempo de licencia anual
att_annualLeave_ruleCountOver=Se ha alcanzado el número máximo de registros fijado
att_annualLeave_years=Últimos años
att_annualLeave_eachYear=Cada año
att_annualLeave_have=Sí
att_annualLeave_days=Días de vacaciones anuales
att_annualLeave_totalDays=Total de vacaciones anuales
att_annualLeave_remainingDays=Vacaciones anuales restantes
att_annualLeave_consecutive=La configuración de la regla de vacaciones anuales debe ser de años consecutivos
# 年假结余表
att_annualLeave_report=Balance de vacaciones anuales
att_annualLeave_validDate=Fecha válida
att_annualLeave_useDays=Usar {0} días
att_annualLeave_calculateDays=Lanzamiento {0} días
att_annualLeave_notEnough=¡Vacaciones anuales insuficientes en {0}!
att_annualLeave_notValidDate={0} no está dentro del rango válido de vacaciones anuales.
att_annualLeave_notDays=¡{0} no tiene vacaciones anuales!
att_annualLeave_tip1=Nombre de empleado se unió el 1 de septiembre del año pasado
att_annualLeave_tip2=Configuración del saldo de las vacaciones anuales
att_annualLeave_tip3=La fecha de liquidación y emisión es el 1 de enero de cada año; se calcula redondeando según el ratio de trabajo; si el tiempo de servicio es menor o igual a 1, habrá 3 días de vacaciones anuales, y si el tiempo de servicio es menor o igual a 3 años, habrá 5 días de vacaciones anuales
att_annualLeave_tip4=Cálculo de disfrute de vacaciones anuales
att_annualLeave_tip5=El año pasado 09-01 ~ 12-31 disfruta 4 / 12x3=1.0 días
att_annualLeave_tip6=Este año 01-01 ~ 12-31 disfruta 4.0 días (este año 01-01 ~ 08-31 disfruta 8 / 12x3=2.0 días   este año 09-01 ~ 12-31 disfruta 4 / 12x5≈2.0 días)
# att SDC
att_sdc_name=Cámara de reconocimiento facial
att_sdc_wxMsg_firstData=Hola, tienes un aviso de registro de asistencia
att_sdc_wxMsg_stateData=Marcación correcta sin estado en el equipo
att_sdc_wxMsg_remark=Nota: Los resultados finales de asistencia se basan en la página de detalles de marcaciones.
# 时间段
att_timeSlot_conflict=La franja horaria entra en conflicto con otras franjas horarias del día
att_timeSlot_selectFirst=Seleccione el intervalo de tiempo
# 事件中心
att_eventCenter_sign=Registro de asistencia
#异常管理
att_exception_classImportTemplate=Plantillas de importación de turnos
att_exception_cellClassAdjustType=Campo obligatorio, como: "{0}", "{1}", "{2}"
att_exception_swapDateDate=campo no obligatorio, el formato de hora es aaaa-MM-dd, por ejemplo: 2020-07-07
#消息中心
att_message_leave=Aviso de asistencia {0}
att_message_leaveContent={0} enviado {1}, {2} el tiempo es {3} ~ {4}
att_message_leaveTime=Hora de salida
att_message_overtime=Aviso de asistencia y horas extra
att_message_overtimeContent={0} enviado horas extra, y el tiempo extra es {1} ~ {2}
att_message_overtimeTime=Tiempo extra
att_message_sign=Señal de aviso de asistencia
att_message_signContent={0} envió una señal complementaria, y la hora de la señal complementaria es {1}
att_message_adjust=Aviso de ajuste de asistencia
att_message_adjustContent={0} envió un ajuste y la fecha de ajuste es {1}
att_message_class=Notificación de asistencia y turno
att_message_classContent=Detalles del turno
att_message_classContent0={0} envió un turno, la fecha del turno es {1} y el turno es {2}
att_message_classContent1={0} envió un turno, la fecha del turno es {1} y la fecha del turno es {2}
att_message_classContent2={0} ({1}) y {2} ({3}) clases de intercambio
#推送中心
att_pushCenter_transaction=Registro de asistencia
# 时间段
att_timeSlot_workTimeNotEqual=El tiempo de trabajo no puede ser igual al tiempo libre
att_timeSlot_signTimeNotEqual=La hora de inicio no puede ser igual a la hora final
# 北向接口A
att_api_notNull=¡{0} no puede estar vacío!
att_api_startDateGeEndDate=¡La hora de inicio no puede ser mayor o igual que la hora de finalización!
att_api_leaveTypeNotExist=Las especies ficticias no existen.
att_api_imageLengthNot2000=Las direcciones de las imágenes no deben superar los 2000 caracteres.
# 20221230新增国际化
att_personSch_workTypeNotNull=¡El tipo de trabajo correspondiente al número de personal {0} no puede estar vacío!
att_personSch_workTypeNotExist=¡El tipo de trabajo correspondiente al número de personal {0} no existe!
att_annualLeave_recalculate=Volver a calcular
# 20230530新增国际化
att_leftMenu_dailyReport=Informe diario de asistencia
att_leftMenu_overtimeReport=Informe de tiempo extra
att_leftMenu_lateReport=Informe tardío
att_leftMenu_earlyReport=Salir del informe anticipado
att_leftMenu_absentReport=Informe de ausencia
att_leftMenu_monthReport=Informe mensual de asistencia
att_leftMenu_monthWorkTimeReport=Informe de tiempo de trabajo mensual
att_leftMenu_monthCardReport=Informe de tarjeta mensual
att_leftMenu_monthOvertimeReport=Informe mensual de horas extra
att_leftMenu_overtimeSummaryReport=Informe de resumen de horas extra del personal
att_leftMenu_deptOvertimeSummaryReport=Informe de resumen de horas extra del departamento
att_leftMenu_deptLeaveSummaryReport=Informe de resumen de baja del departamento
att_annualLeave_calculateDay=Número de días de vacaciones anuales
att_annualLeave_adjustDay=Ajustar días
att_annualLeave_sureSelectDept=¿Está seguro de que desea realizar la operación {0} en el departamento seleccionado?
att_annualLeave_sureSelectPerson=¿Está seguro de que desea realizar la operación {0} en la persona seleccionada?
att_annualLeave_calculateTip1=Al calcular según la duración del servicio: el cálculo de las vacaciones anuales es exacto al mes, si la duración del servicio es de 10 años y 3 meses, entonces se utilizarán 10 años y 3 meses para el cálculo;
att_annualLeave_calculateTip2=Cuando la conversión no se basa en la duración del servicio: el cálculo de las vacaciones anuales es exacto al año, si la duración del servicio es de 10 años y 3 meses, se utilizarán 10 años para el cálculo;
att_rule_isInCompleteTip=La prioridad es la más alta cuando ningún inicio de sesión o cierre de sesión se registra como incompleto, y retrasos, salidas anticipadas, ausentismo y validez
att_rule_absentTip=Cuando no se registra como ausentismo ningún registro de entrada o de salida, la duración del ausentismo es igual a la duración de las horas de trabajo menos la duración de la licencia tardía a temprana
att_timeSlot_elasticTip1=0, el tiempo efectivo es igual al tiempo real, sin ausentismo
att_timeSlot_elasticTip2=Si el tiempo real es mayor que el tiempo de trabajo, el tiempo efectivo es igual al tiempo de trabajo, sin ausentismo
att_timeSlot_elasticTip3=Si la duración real es menor que la duración laboral, la duración efectiva es igual a la duración real y el ausentismo es igual a la duración laboral menos la duración real
att_timeSlot_maxWorkingHours=El horario laboral no puede ser superior a
# 20231030
att_customReport=Informe personalizado de asistencia
att_customReport_byDayDetail=Detalhe por dia
att_customReport_byPerson=Resumo por pessoa
att_customReport_byDept=Resumo por Departamento
att_customReport_queryMaxRange=O intervalo máximo da consulta é de quatro meses
# 20240630新增国际化
att_personSch_shiftWorkTypeTip1=1. cuando el tipo de trabajo es trabajar horas extras durante el día normal de trabajo / descanso, la prioridad de programación temporal es menor que la de los días festivos.
att_personSch_shiftWorkTypeTip2=2. cuando el tipo de trabajo es trabajar horas extras durante las vacaciones, la prioridad de la programación temporal es mayor que la de las vacaciones.
att_personVerifyMode=Método de verificación del personal
att_personVerifyMode_setting=Configuración del método de verificación
att_personSch_importCycSch=Importar la programación del ciclo de personal
att_personSch_cycSchTemplate=Plantilla de programación del ciclo de personal
att_personSch_exportCycSchTemplate=Descargar la plantilla de programación del ciclo de personal
att_personSch_scheduleTypeNotNull=¡¡ el tipo de turno no puede estar vacío o no existe!
att_personSch_shiftNotNull=¡¡ el turno no puede estar vacío!
att_personSch_shiftNotExist=¡¡ el turno no existe!
att_personSch_onlyAllowOneShift=¡¡ el horario ordinario solo permite un turno!
att_shift_attShiftStartDateRemark2=La semana en la que se encuentra la fecha de inicio del ciclo es la primera semana; El mes en el que se encuentra la fecha de inicio del ciclo es el primer mes.
#打卡状态
att_cardStatus_setting=Configuración del estado de asistencia
att_cardStatus_name=Nombre
att_cardStatus_value=Valor
att_cardStatus_alias=Alias
att_cardStatus_every_day=Cada día
att_cardStatus_by_week=Por semana
att_cardStatus_autoState=Estado automático
att_cardStatus_attState=Estado de asistencia
att_cardStatus_signIn=Registro de entrada
att_cardStatus_signOut=Registro de salida
att_cardStatus_out=fuera
att_cardStatus_outReturn=Regresar después de salir
att_cardStatus_overtime_signIn=Registro de entrada de extratiempo
att_cardStatus_overtime_signOut=Registro de salida de extratiempo
# 20241030新增国际化
att_leaveType_enableMaxDays=Habilitar límite anual
att_leaveType_maxDays=Límite anual (días)
att_leaveType_applyMaxDays=La solicitud no puede exceder {0} días en este año
att_param_overTimeSetting=Configuración de nivel de horas extra
att_param_overTimeLevel=Nivel de horas extra (horas)
att_param_overTimeLevelEnable=Habilitar cálculo de nivel de horas extra
att_param_reportColor=Color del informe
# APP
att_app_signClientTip=Este dispositivo ya ha sido registrado por otra persona hoy
att_app_noSignAddress=Área de verificación no configurada, póngase en contacto con el administrador para configurarla
att_app_notInSignAddress=No ha llegado a la ubicación de verificación, no se puede realizar la verificación
att_app_attendance=Mi asistencia
att_app_apply=Solicitud de asistencia
att_app_approve=Mis aprobaciones
# 20250530
att_node_leaderNodeExist=Ya existe un nodo de aprobación del líder directo
att_signAddress_init=Inicializando mapa
att_signAddress_initTips=Ingrese la clave de mapa y inicialice el mapa para seleccionar una dirección