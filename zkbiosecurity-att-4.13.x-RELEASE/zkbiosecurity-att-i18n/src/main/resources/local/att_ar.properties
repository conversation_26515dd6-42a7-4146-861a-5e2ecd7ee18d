#系统名称 阿拉伯语
att_systemName=نظام الحضور 1.0
#=====================================================================
#左侧菜单
att_module=الحضور
#左侧菜单-考勤设备
att_leftMenu_attendanceDevice=إدارة الجهاز
att_leftMenu_device=جهاز حضور
att_leftMenu_point=نقطة الحضور
att_leftMenu_sign_address=تسجيل الوصول للجوال
att_leftMenu_adms_devCmd=أصدر الخادم أمرًا
#左侧菜单-基础信息
att_leftMenu_basicInformation=معلومات اساسية
att_leftMenu_rule=قاعدة
att_leftMenu_base_rule=قواعد الحضور
att_leftMenu_department_rule=قسم الإدارة
att_leftMenu_holiday=يوم الاجازة
att_leftMenu_leaveType=نوع الإجازة
att_leftMenu_timingCalculation=حساب توقيت
att_leftMenu_autoExport=إرسال تقرير
att_leftMenu_param=ضبط القياسات
#左侧菜单-班次管理
att_leftMenu_shiftManagement=تحول
att_leftMenu_timeSlot=الجدول الزمني
att_leftMenu_shift=تحول
#左侧菜单-排班管理
att_leftMenu_scheduleManagement=جدول
att_leftMenu_group=مجموعة
att_leftMenu_groupPerson=شخص المجموعة
att_leftMenu_groupSch=جدول المجموعة
att_leftMenu_deptSch=جدول القسم
att_leftMenu_personSch=جدول الموظفين
att_leftMenu_tempSch=جدول مؤقت
att_leftMenu_nonSch=غير المجدولة الموظفين
#左侧菜单-异常管理
att_leftMenu_exceptionManagement=إدارة استثناءات الحضور
att_leftMenu_sign=سجل اليدوي
att_leftMenu_leave=غادر
att_leftMenu_trip=رحلة عمل
att_leftMenu_out=خارج
att_leftMenu_overtime=متأخر، بعد فوات الوقت
att_leftMenu_adjust=ضبط وإلحاق
att_leftMenu_class=ضبط التحول
#左侧菜单-统计报表
att_leftMenu_statisticalReport=تقرير إحصائيات الحضور
att_leftMenu_manualCalculation=حساب يدوي
att_leftMenu_transaction=المعاملات
att_leftMenu_dayCardDetailReport=الحضور اليومي
att_leftMenu_leaveSummaryReport=ترك ملخص
att_leftMenu_dayDetailReport=تقرير يومي
att_leftMenu_monthDetailReport=التقرير الشهري
att_leftMenu_monthStatisticalReport=تقرير إحصاءات الموظفين الشهري
att_leftMenu_deptStatisticalReport=التقرير الشهري للإحصائيات الإدارية
att_leftMenu_yearStatisticalReport=التقرير السنوي (حسب الشخص)
att_leftMenu_attSignCallRollReport=تسجيل الدخول بنداء الأسماء
att_leftMenu_workTimeReport=تقرير وقت العمل
#左侧菜单-设备操作日志
att_leftMenu_device_op_log=سجل تشغيل الجهاز
#左侧菜单-实时点名
att_leftMenu_realTime_callRoll=نداء المكالمة
#=====================================================================
#公共
att_common_person=شؤون الموظفين
att_common_pin=هوية شخصية
att_common_group=مجموعة
att_common_dept=قسم
att_common_symbol=رمز
att_common_deptNo=رقم القسم
att_common_deptName=اسم القسم
att_common_groupNo=رقم المجموعة
att_common_groupName=أسم المجموعة
att_common_operateTime=وقت العملية
att_common_operationFailed=فشلت العملية
att_common_id=هوية شخصية
att_common_deptId=معرف القسم
att_common_groupId=معرف مجموعة
att_common_deviceId=معرف الجهاز
att_person_pin=معرف الموظفين
att_person_name=الاسم الاول
att_person_lastName=الكنية
att_person_internalCard=رقم البطاقة
att_person_attendanceMode=وضع الوقت والحضور
att_person_normalAttendance=الحضور العادي
att_person_noPunchCard=لا بطاقة لكمة
att_common_attendance=الحضور
att_common_attendance_hour=الحضور (ساعات)
att_common_attendance_day=الحضور (اليوم)
att_common_late=متأخر
att_common_early=مبكرا
att_common_overtime=متأخر، بعد فوات الوقت
att_common_exception=استثناء
att_common_absent=غائب
att_common_leave=المغادرة باكرا
att_common_trip=رحلة عمل
att_common_out=خارج
att_common_staff=موظف
att_common_superadmin=الخارق
att_common_msg=محتوى الرسائل القصيرة
att_common_min=مدة الرسالة القصيرة (بالدقائق)
att_common_letterNumber=يمكن فقط إدخال الأرقام أو الحروف!
att_common_relationDataCanNotDel=لا يمكن حذف البيانات المرتبطة.
att_common_relationDataCanNotEdit=البيانات المرتبطة لا يمكن تعديلها.
att_common_needSelectOneArea=يرجى اختيار المنطقة!
att_common_neesSelectPerson=يرجى اختيار شخص!
att_common_nameNoSpace=لا يمكن أن يحتوي الاسم على مساحة!
att_common_digitsValid=يمكن فقط إدخال أرقام لا تزيد عن رقمين عشريين!
att_common_numValid=فقط أدخل الأرقام!
#=====================================================================
#工作面板
att_dashboard_worker=مخلص لعمله
att_dashboard_today=حضور اليوم
att_dashboard_todayCount=إحصائيات الحضور اليوم المقسمة
att_dashboard_exceptionCount=إحصائيات غير طبيعية (هذا الشهر)
att_dashboard_lastWeek=الاسبوع الماضى
att_dashboard_lastMonth=الشهر الماضي
att_dashboard_perpsonNumber=مجموع الموظفين
att_dashboard_actualNumber=الموظفين الفعليين
att_dashboard_notArrivedNumber=أفراد غائبين
att_dashboard_attHour=وقت العمل
#区域
#设备
att_op_syncDev=مزامنة بيانات البرنامج مع الجهاز
att_op_account=فحص بيانات الحضور
att_op_check=تحميل البيانات مرة أخرى
att_op_deleteCmd=مسح أوامر الجهاز
att_op_dataSms=رسالة عامة
att_op_clearAttPic=مسح صور الحضور
att_op_clearAttLog=مسح المعاملات الحضور
att_device_waitCmdCount=أوامر ليتم تنفيذها
att_device_status=تمكين الدولة
att_device_register=جهاز التسجيل
att_device_isRegister=سواء لتعيين كجهاز التسجيل؟
att_device_existNotRegDevice=جهاز تسجيل غير مسجل ، لا يمكن الحصول على البيانات!
att_device_fwVersion=نسخة برنامج ثابت
att_device_transInterval=تحديث الفاصل (بالدقائق)
att_device_cmdCount=الحد الأقصى لعدد الأوامر للتواصل مع الخادم.
att_device_delay=وقت سجل الاستفسار (بالثواني)
att_device_timeZone=وحدة زمنية
att_device_operationLog=سجلات العملية
att_device_registeredFingerprint=تسجيل بصمة
att_device_registeredUser=تسجيل الموظفين
att_device_fingerprintImage=بصمة الصورة
att_device_editUser=تحرير الموظفين
att_device_modifyFingerprint=تعديل بصمة
att_device_faceRegistration=تسجيل الوجه
att_device_userPhotos=صور الموظفين
att_device_attLog=سواء لتحميل سجلات الحضور
att_device_operLog=سواء لتحميل معلومات الموظفين
att_device_attPhoto=سواء لتحميل الصور الحضور
att_device_isOnLine=الموجودين
att_device_InputPin=أدخل رقم الشخص
att_device_getPin=الحصول على بيانات الموظفين المحددة
att_device_separatedPin=أرقام الموظفين متعددة ، مفصولة بفواصل
att_device_authDevice=جهاز معتمد
att_device_disabled=الأجهزة التالية معطلة ولا يمكن تشغيلها!
att_device_autoAdd=تتم إضافة معدات جديدة تلقائيًا
att_device_receivePersonOnlyDb=استقبال بيانات الموظفين الموجودة في قاعدة البيانات فقط
att_devMenu_control=جهاز التحكم
att_devMenu_viewOrGetInfo=عرض واحصل على معلومات
att_devMenu_clearData=مسح بيانات الجهاز
att_device_disabledOrOffline=الجهاز غير مفعل أو متصل بالإنترنت ، لا يمكن تشغيله!
att_device_areaStatus=حالة منطقة الجهاز
att_device_areaCommon=المنطقة طبيعية
att_device_areaEmpty=المنطقة فارغة
att_device_isRegDev=يتطلب تعديل المنطقة الزمنية أو حالة المسجل إعادة تشغيل الجهاز ليصبح ساري المفعول!
att_device_canUpgrade=يمكن ترقية الأجهزة التالية
att_device_offline=الأجهزة التالية غير متصلة ولا يمكن تشغيلها!
att_device_oldProtocol=بروتوكول قديم
att_device_newProtocol=بروتوكول جديد
att_device_noMoreTwenty=لا يمكن أن تتجاوز حزمة ترقية البرامج الثابتة لجهاز البروتوكول القديم 20 مليونًا
att_device_transferFilesTip=تم اكتشاف البرامج الثابتة بنجاح ، قم بنقل الملفات
att_op_clearAttPers=مسح أفراد المعدات
#区域人员
att_op_forZoneAddPers=إعداد أفراد المنطقة
att_op_dataUserSms=رسالة خاصة
att_op_syncPers=إعادة المزامنة إلى الجهاز
att_areaPerson_choiceArea=يرجى اختيار المنطقة!
att_areaPerson_byAreaPerson=حسب المنطقة
att_areaPerson_setByAreaPerson=تم ضبطه بواسطة أفراد المنطقة
att_areaPerson_importBatchDel=استيراد حذف مجمع
att_areaPerson_syncToDevSuccess=عملية ناجحة! يرجى الانتظار حتى يتم إرسال الأمر.
att_areaPerson_personId=معرف الموظفين
att_areaPerson_areaId=معرف المنطقة
att_area_existPerson=هناك شخص في المنطقة!
att_areaPerson_notice1=لا يمكن أن تكون المنطقة أو الأفراد فارغين في نفس الوقت!
att_areaPerson_notice2=لم يتم الاستعلام عن أي شخص أو مناطق!
att_areaPerson_notice3=لا توجد أجهزة في المنطقة!
att_areaPerson_addArea=إضافة المنطقة
att_areaPerson_delArea=حذف المنطقة
att_areaPerson_persNoExit=الشخص غير موجود
att_areaPerson_importTip1=يرجى التأكد من وجود الشخص المستورد بالفعل في وحدة شؤون الموظفين
att_areaPerson_importTip2=لن يتم تسليم مستوردي المجموعة تلقائيًا إلى الجهاز ويجب مزامنتها يدويًا
att_areaPerson_addAreaPerson=إضافة موظفين إقليميين
att_areaPerson_delAreaPerson=حذف أفراد المنطقة
att_areaPerson_importDelAreaPerson=استيراد وحذف أفراد المنطقة
att_areaPerson_importAreaPerson=استيراد أفراد المنطقة
#考勤点
att_attPoint_name=اسم نقطة الحضور
att_attPoint_list=قائمة نقاط الحضور
att_attPoint_deviceModule=وحدة الجهاز
att_attPoint_acc=صلاحية التحكم صلاحية الدخول
att_attPoint_park=موقف سيارات
att_attPoint_ins=FaceKiosk
att_attPoint_pid=شهادة شخصية
att_attPoint_vms=فيديو
att_attPoint_psg=ممر
att_attPoint_doorList=قائمة الباب
att_attPoint_deviceList=قائمة الأجهزة
att_attPoint_channelList=قائمة القنوات
att_attPoint_gateList=قائمة البوابة
att_attPoint_recordTypeList=سحب نوع السجل
att_attPoint_door=يرجى اختيار الباب المقابل.
att_attPoint_device=يرجى اختيار الجهاز المقابل.
att_attPoint_gate=الرجاء تحديد البوابة المقابلة
att_attPoint_normalPassRecord=سجل النجاح العادي
att_attPoint_verificationRecord=سجل التحقق
att_person_attSet=إعداد الحضور
att_attPoint_point=يرجى اختيار نقطة الحضور.
att_attPoint_count=نقاط ترخيص الحضور غير كافية ؛ فشلت العملية!
att_attPoint_notSelect=لم يتم تكوين الوحدة
att_attPoint_accInsufficientPoints=نقاط حضور غير كافية لجهاز التحكم في الوصول!
att_attPoint_parkInsufficientPoints=نقاط الحضور غير كافية لجهاز وقوف السيارات!
att_attPoint_insInsufficientPoints=نقطة ترخيص الحضور لـ faceKiosk غير كافية！
att_attPoint_pidInsufficientPoints=شهادة الموظفين ليست كافية للحضور!
att_attPoint_doorOrParkDeviceName=اسم الباب أو اسم جهاز وقوف السيارات
att_attPoint_vmsInsufficientPoints=الفيديو عندما تكون نقاط الحضور غير كافية!
att_attPoint_psgInsufficientPoints=القناة ليس لديها نقاط حضور كافية!
att_attPoint_delDevFail=فشل في حذف الجهاز ، تم استخدام الجهاز كنقطة حضور!
att_attPoint_pullingRecord=نقطة الحضور هي الحصول على السجلات بانتظام ، يرجى الانتظار!
att_attPoint_lastTransactionTime=وقت سحب البيانات الأخير
att_attPoint_masterDevice=الجهاز الرئيسي
att_attPoint_channelName=اسم القناة
att_attPoint_cameraName=اسم الكاميرا
att_attPoint_cameraIP=عنوان IP للكاميرا
att_attPoint_channelIP=قناة IP
att_attPoint_gateNumber=رقم البوابة
att_attPoint_gateName=اسم البوابة
#APP考勤签到地址
att_signAddress_address=عنوان
att_signAddress_longitude=خط الطول
att_signAddress_latitude=خط العرض
att_signAddress_range=مجال صحيح
att_signAddress_rangeUnit=الوحدة متر (م)
#=====================================================================
#规则
att_rule_baseRuleSet=إعداد القاعدة الأساسية
att_rule_countConvertSet=إعداد الحساب
att_rule_otherSet=إعدادات أخرى
att_rule_baseRuleSignIn=تحقق في القاعدة
att_rule_baseRuleSignOut=المغادرة المغادرة القاعدة
att_rule_earliestPrinciple=القاعدة الأولى
att_rule_theLatestPrinciple=القاعدة الأخيرة
att_rule_principleOfProximity=القرب القاعدة
att_rule_baseRuleShortestMinutes=يجب أن تكون المنطقة الزمنية الدنيا أكبر من (10 دقائق على الأقل)
att_rule_baseRuleLongestMinutes=يجب أن تكون المنطقة الزمنية القصوى أقل من (1440 دقيقة كحد أقصى)
att_rule_baseRuleLateAndEarly=إجازة متأخرة ومبكرة تعتبر غائبة
att_rule_baseRuleCountOvertime=إحصائيات العمل الإضافي
att_rule_baseRuleFindSchSort=البحث عن سجل التحول
att_rule_groupGreaterThanDepartment=المجموعة-> القسم
att_rule_departmentGreaterThanGroup=قسم-> المجموعة
att_rule_baseRuleSmartFindClass=مطابقة ذكي حكم التحول
att_rule_timeLongest=أطول مدة العمل
att_rule_exceptionLeast=أقل استثناء
att_rule_baseRuleCrossDay=نتيجة حساب الحضور للتحول عبر اليوم
att_rule_firstDay=اليوم الأول
att_rule_secondDay=ثاني يوم
att_rule_baseRuleShortestOvertimeMinutes=أقصر وقت إضافي (بالدقائق)
att_rule_baseRuleMaxOvertimeMinutes=الوقت الإضافي الأقصى (بالدقائق)
att_rule_baseRuleElasticCal=حساب المدة المرنة
att_rule_baseRuleTwoPunch=الوقت التراكمي لكل اثنين من اللكمات
att_rule_baseRuleStartEnd=حساب الرأس والذيل لكمة الوقت
att_rule_countConvertHour=قاعدة تحويل الساعة
att_rule_formulaHour=الصيغة: ساعات
att_rule_countConvertDay=أيام تحويل القاعدة
att_rule_formulaDay=الصيغة: يوم
att_rule_inFormulaShallPrevail=تأخذ النتيجة المحسوبة بواسطة الصيغة كمعيار؛
att_rule_remainderHour=الباقي أكبر من أو يساوي
att_rule_oneHour=تحسب ساعة واحدة ؛
att_rule_halfAnHour=تحسب على أنها نصف ساعة ، يتم تجاهلها بطريقة أخرى ؛
att_rule_remainderDay=أكبر من أو يساوي دقائق العمل
att_rule_oneDay=٪ ، وتحسب على أنها يوم واحد ؛
att_rule_halfAnDay=٪ ، محسوبة على أنها نصف يوم ، يتم تجاهلها ؛
att_rule_countConvertAbsentDay=معيار تحويل اليوم
att_rule_markWorkingDays=تحسب أيام العمل
att_rule_countConvertDecimal=الأرقام الدقيقة للفاصلة العشرية
att_rule_otherSymbol=إعداد رمز نتيجة الحضور في التقرير
att_rule_arrive=يتوقع / الفعلية
att_rule_noSignIn=لا تحقق في
att_rule_noSignOff=لا تحقق المغادرة
att_rule_off=ضبط الراحة
att_rule_class=إلحاق الحضور
att_rule_shortLessLong=لا يمكن أن يكون الجدول الزمني للحضور أطول من أطول فترة زمنية لجدول الحضور.
att_rule_symbolsWarning=يجب عليك تكوين الرمز في تقرير الحضور!
att_rule_reportSettingSet=الإبلاغ عن إعدادات التصدير
att_rule_shortDateFormat=صيغة التاريخ
att_rule_shortTimeFormat=تنسيق الوقت
att_rule_baseRuleSignBreakTime=ما إذا كان في الاختيار خلال فترة الاستراحة
att_leftMenu_custom_rule=قاعدة مخصصة
att_custom_rule_already_exist={0} القواعد المخصصة موجودة بالفعل!
att_add_group_custom_rule=إضافة قواعد التجميع
att_custom_rule_type=نوع القاعدة
att_rule_type_group=قواعد التجميع
att_rule_type_dept=قواعد القسم
att_custom_rule_orgNames=باستخدام الكائن
att_rult_maxOverTimeType1=لا حدود
att_rult_maxOverTimeType2=هذا الاسبوع
att_rult_maxOverTimeType3=هذا الشهر
att_rule_countConvertDayRemark1=مثال: وقت العمل الفعال هو 500 دقيقة ، ويجب أن يكون وقت العمل 480 دقيقة في اليوم ، وتكون النتيجة 500/480 = 1.04 ، والعشرية الأخيرة 1.0
att_rule_countConvertDayRemark2=مثال: وقت العمل الفعال هو 500 دقيقة ، ويجب أن يكون وقت العمل 480 دقيقة في اليوم ، ثم تكون النتيجة 500/480 = 1.04 ، 1.04> 0.8 يتم حسابها في يوم واحد
att_rule_countConvertDayRemark3=مثال: وقت العمل الفعلي هو 300 دقيقة ، ووقت العمل هو 480 دقيقة يوميًا. والنتيجة هي مثال: وقت العمل الفعال هو 300 دقيقة ، ويجب أن يكون وقت العمل 480 دقيقة في اليوم ، وتكون النتيجة 300/480 = 0.625 ، 0.2 <0.625 <0.8 يتم حسابها كنصف يوم
att_rule_countConvertDayRemark4=معيار تحويل اليوم: السجل كأيام عمل في الفترة الزمنية لا يعمل ؛
att_rule_countConvertDayRemark5=يتم تسجيله على أنه عدد أيام العمل: فهو يقتصر على حساب عدد أيام الإكمال ، وطالما كان هناك إتمام في كل فترة ، يتم حساب طول الإكمال وفقًا لعدد أيام العمل في فترة؛
att_rule_baseRuleSmartFindRemark1=أطول وقت: وفقًا لنقطة بطاقة اليوم ، احسب ساعات العمل المقابلة لكل نوبة في اليوم ، وابحث عن التحول في أطول وقت عمل في اليوم ؛
att_rule_baseRuleSmartFindRemark2=الحد الأدنى غير الطبيعي: احسب عدد الأوقات غير الطبيعية المقابلة لكل نوبة في اليوم وفقًا لنقطة بطاقة اليوم ، وابحث عن التحول مع أقل عدد من الحالات الشاذة في اليوم لحساب وقت العمل ؛
att_rule_baseRuleHourValidator=لا يمكن أن تكون مدة الحكم لمدة نصف ساعة أكبر من أو تساوي ساعة واحدة!
att_rule_baseRuleDayValidator=فترة الحكم لنصف يوم لا يمكن أن تكون أكبر من أو تساوي فترة الحكم ليوم واحد!
att_rule_overtimeWarning=لا يمكن أن يكون الحد الأقصى لمدة العمل الإضافي أقل من الحد الأدنى لمدة العمل الإضافي الحد الأدنى!
att_rule_noSignInCountType=لم يتم التحقق منه في
att_rule_absent=غياب
att_rule_earlyLeave=مغادرة مبكرة
att_rule_noSignOffCountType=غير موقعة مرة أخرى
att_rule_minutes=الدقائق
att_rule_noSignInCountLateMinute=سيتم احتساب فشل تسجيل الوصول على أنه دقائق متأخرة
att_rule_noSignOffCountEarlyMinute=يتم حساب الإجازة غير الموقعة باعتبارها عدد الدقائق المتبقية في وقت مبكر
att_rule_incomplete=غير مكتمل
att_rule_noCheckInIncomplete=لم يتم تسجيل الدخول كـ غير مكتمل
att_rule_noCheckOutIncomplete=لم يتم التوقيع مرة أخرى على أنه غير مكتمل
att_rule_lateMinuteWarning=يجب ألا يزيد عدد عمليات تسجيل الدخول عن الدقائق المتأخرة عن 0 وأقل من أطول فترة حضور
att_rule_earlyMinuteWarning=المبالغ المردودة غير الموقعة هي أن دقائق الإجازة المبكرة يجب أن تكون أكبر من 0 وأقل من أطول فترة حضور
att_rule_baseRuleNoSignInCountLateMinuteRemark=عندما لا يتم تسجيل الوصول في وقت متأخر ، إذا لم يتم تسجيل الوصول ، فإنه يتم حسابه على أنه متأخر لمدة N دقيقة
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=عندما لا يتم تسجيل الخروج كمغادرة مبكرة ، إذا لم يتم تسجيل الخروج ، فسيتم احتسابه كمغادرة مبكرة N دقيقة
#节假日
att_holiday_placeholderNo=يوصى ببدء التشغيل بـ H ، مثل H01.
att_holiday_placeholderName=اسم عطلة + العام المقترح ، مثل 2017 مايو
att_holiday_dayNumber=عدد الأيام
att_holiday_validDate_msg=العطل خلال هذا الوقت
#假种
att_leaveType_leaveThing=مغادرة عادية
att_leaveType_leaveMarriage=إجازة الزواج
att_leaveType_leaveBirth=إجازة الأمومة
att_leaveType_leaveSick=أجازة مرضية
att_leaveType_leaveAnnual=اجازة سنويه
att_leaveType_leaveFuneral=إجازة الفجيعة
att_leaveType_leaveHome=مغادرة المنزل
att_leaveType_leaveNursing=إجازة الرضاعة الطبيعية
att_leaveType_isDeductWorkLong=سواء لاستبدال ساعات العمل
att_leaveType_placeholderNo=يوصى ببدء التشغيل بـ L ، مثل L1.
att_leaveType_placeholderName=من المستحسن أن تنتهي بـ عطلة ، على سبيل المثال. عطلة الزواج.
#定时计算
att_timingcalc_timeCalcFrequency=حساب الفاصل
att_timingcalc_timeCalcInterval=توقيت حساب الوقت
att_timingcalc_timeSet=توقيت إعداد حساب الوقت
att_timingcalc_timeSelect=يرجى اختيار الوقت
att_timingcalc_optionTip=يجب الاحتفاظ بحساب واحد صحيح على الأقل يوميًا
#自动导出报表
att_autoExport_reportType=نوع التقرير
att_autoExport_fileType=نوع الملف
att_autoExport_fileName=اسم الملف
att_autoExport_fileDateFormat=صيغة التاريخ
att_autoExport_fileTimeFormat=تنسيق الوقت
att_autoExport_fileContentFormat=تنسيق المحتوى
att_autoExport_fileContentFormatTxt=مثال: {مثال: {deptName} 00 {personPin} 01 {personName} 02 {attDatetime} 03 \r \n
att_autoExport_timeSendFrequency=إرسال التردد
att_autoExport_timeSendInterval=وقت إرسال الفاصل
att_autoExport_emailType=نوع استقبال البريد
att_autoExport_emailRecipients=استقبال البريد
att_autoExport_emailAddress=عنوان البريد
att_autoExport_emailExample=مثال: 123 @ foxmail.com، 456 @ foxmail.com
att_autoExport_emailSubject=عنوان البريد
att_autoExport_emailContent=هيئة البريد
att_autoExport_field=حقل
att_autoExport_fieldName=اسم الحقل
att_autoExport_fieldCode=رقم الحقل
att_autoExport_reportSet=إعداد تقرير
att_autoExport_timeSet=إعداد وقت تسليم البريد
att_autoExport_emailSet=إعداد البريد
att_autoExport_emailSetAlert=الرجاء إدخال عنوان البريد الإلكتروني الخاص بك.
att_autoExport_emailTypeSet=إعداد المتلقي
att_autoExport_byDay=في اليوم
att_autoExport_byMonth=شهريا
att_autoExport_byPersonSet=تعيين من قبل الموظفين
att_autoExport_byDeptSet=تعيين من قبل الإدارة
att_autoExport_byAreaSet=تعيين حسب المنطقة
att_autoExport_emailSubjectSet=إعداد العنوان
att_autoExport_emailContentSet=إعداد الجسم
att_autoExport_timePointAlert=يرجى تحديد نقطة إرسال الوقت الصحيحة.
att_autoExport_lastDayofMonth=آخر يوم في الشهر
att_autoExport_firstDayofMonth=اليوم الأول من الشهر
att_autoExport_dayofMonthCheck=موعد محدد
att_autoExport_dayofMonthCheckAlert=يرجى تحديد التاريخ المحدد.
att_autoExport_chooseDeptAlert=يرجى اختيار القسم!
att_autoExport_sendFormatSet=إرسال وضع الإعداد
att_autoExport_sendFormat=إرسال الوضع
att_autoExport_mailFormat=طريقة تسليم صندوق البريد
att_autoExport_ftpFormat=إرسال الطريقة عبر FTP
att_autoExport_sftpFormat=إرسال الطريقة عبر SFTP
att_autoExport_ftpUrl=عنوان خادم FTP
att_autoExport_ftpPort=منفذ خادم FTP
att_autoExport_ftpTimeSet=إعداد وقت إرسال FTP
att_autoExport_ftpParamSet=إعداد معلمة FTP
att_autoExport_ftpUsername=اسم مستخدم FTP
att_autoExport_ftpPassword=كلمة مرور FTP
att_autoExport_correctFtpParam=يرجى ملء المعلمات بروتوكول نقل الملفات بشكل صحيح
att_autoExport_correctFtpTestParam=يرجى اختبار الاتصال للتأكد من أن اتصال بروتوكول نقل الملفات أمر طبيعي
att_autoExport_inputFtpUrl=يرجى إدخال عنوان خادم بروتوكول نقل الملفات
att_autoExport_inputFtpPort=الرجاء إدخال منفذ خادم بروتوكول نقل الملفات
att_autoExport_ftpSuccess=نجح الاتصال
att_autoExport_ftpFail=يرجى التحقق مما إذا كانت إعدادات معلمة غير صحيحة.
att_autoExport_validFtp=الرجاء إدخال عنوان خادم صالح
att_autoExport_validPort=الرجاء إدخال منفذ خادم صالح
att_autoExport_selectExcelTip=نوع الملف حدد EXCEL ، تنسيق المحتوى هو كل الحقول!
#=====================================================================
#时间段
att_timeSlot_periodType=نوع الجدول الزمني
att_timeSlot_normalTime=الجدول الزمني العادي
att_timeSlot_elasticTime=جدول زمني مرن
att_timeSlot_startSignInTime=تحقق في وقت البدء
att_timeSlot_toWorkTime=تحقق في الوقت
att_timeSlot_endSignInTime=تحقق في نهاية الوقت
att_timeSlot_allowLateMinutes=السماح متأخرًا (بالدقائق)
att_timeSlot_isMustSignIn=يجب تسجيل الوصول
att_timeSlot_startSignOffTime=وقت المغادرة المغادرة
att_timeSlot_offWorkTime=تحقق من الوقت
att_timeSlot_endSignOffTime=وقت المغادرة المغادرة
att_timeSlot_allowEarlyMinutes=السماح بالإجازة المبكرة (بالدقائق)
att_timeSlot_isMustSignOff=يجب إنهاء إجراءات المغادرة
att_timeSlot_workingHours=وقت العمل (بالدقائق)
att_timeSlot_isSegmentDeduction=خصم السيارات استراحة الوقت
att_timeSlot_startSegmentTime=وقت البدء
att_timeSlot_endSegmentTime=وقت النهاية
att_timeSlot_interSegmentDeduction=الوقت المخصوم (بالدقائق)
att_timeSlot_markWorkingDays=يوم عمل
att_timeSlot_isAdvanceCountOvertime=أوت أوت (تسجيل الوصول المبكر)
att_timeSlot_signInAdvanceTime=وقت انتهاء OT التلقائي (تسجيل الوصول)
att_timeSlot_isPostponeCountOvertime=أوت أوت (تأخير المغادرة)
att_timeSlot_signOutPosponeTime=وقت بدء التشغيل التلقائي (تسجيل المغادرة)
att_timeSlot_isCountOvertime=يحسب العمل الإضافي؟
att_timeSlot_timeSlotLong=يجب أن تفي ساعات العمل بفترة الحضور المحددة في القواعد:
att_timeSlot_alertStartSignInTime=يجب أن يكون وقت بدء تسجيل الوصول أقل من وقت تسجيل الوصول.
att_timeSlot_alertEndSignInTime=يجب أن يكون وقت إنهاء تسجيل الوصول أكبر من وقت تسجيل الوصول.
att_timeSlot_alertStartSignInAndEndSignIn=يجب أن يكون وقت بدء تسجيل المغادرة أقل من وقت تسجيل المغادرة.
att_timeSlot_alertStartSignOffTime=لا يمكن أن يكون وقت بدء العمل الإضافي أقل من وقت تسجيل المغادرة.
att_timeSlot_alertEndSignOffTime=لا يمكن أن يكون وقت بدء العمل الإضافي أكبر من وقت إنهاء إجراءات المغادرة.
att_timeSlot_alertStartUnequalEnd=لا يمكن أن تكون أيام العمل أصغر من 0.
att_timeSlot_alertStartSegmentTime=لا يمكن أن يكون الوقت المستقطع أصغر من 0.
att_timeSlot_alertStartAndEndTime=لا يمكن أن يكون وقت بدء التسجيل مساويا لوقت تسجيل الوصول النهائي.
att_timeSlot_alertEndAndoffWorkTime=لا يمكن أن تكون الساعات أكبر من 23 ساعة.
att_timeSlot_alertToWorkLesserAndEndTimeLesser=لا يمكن أن تكون الدقائق أكبر من 59.
att_timeSlot_alertLessSignInAdvanceTime=يجب أن يكون وقت تسجيل الوصول المبكر أقل من وقت بدء العمل
att_timeSlot_alertMoreSignInAdvanceTime=عدد دقائق العمل الإضافي قبل الذهاب إلى العمل أقل من عدد الدقائق قبل العمل
att_timeSlot_alertMoreSignOutPosponeTime=دقائق العمل الإضافي خارج العمل هي أقل من دقائق بعد العمل
att_timeSlot_alertLessSignOutPosponeTime=يجب أن يكون وقت تسجيل المغادرة المتأخر أكبر من وقت انتهاء العمل
att_timeSlot_time=يرجى إدخال تنسيق الوقت الصحيح.
att_timeSlot_alertMarkWorkingDays=يوم العمل لا يمكن أن يكون فارغا!
att_timeSlot_placeholderNo=يوصى بالبدء بحرف T ، مثل T01.
att_timeSlot_placeholderName=يوصى بأن تبدأ بـ T أو تنتهي بـ جدول زمني.
att_timeSlot_beforeToWork=قبل العمل
att_timeSlot_afterToWork=بعد العمل
att_timeSlot_beforeOffWork=قبل الخروج من الخدمة
att_timeSlot_afterOffWork=بعد العمل
att_timeSlot_minutesSignInValid=تسجيل الوصول صالح في غضون دقائق
att_timeSlot_toWork=في الخدمة
att_timeSlot_offWork=خارج الخدمه
att_timeSlot_minutesSignInAsOvertime=تسجيل الدخول قبل دقائق للعمل الإضافي
att_timeSlot_minutesSignOutAsOvertime=ابدأ في حساب الوقت الإضافي بعد دقائق
att_timeSlot_minOvertimeMinutes=الحد الأدنى لوقت العمل الإضافي
att_timeSlot_enableWorkingHours=سواء لتمكين ساعات العمل؟
att_timeSlot_eidtTimeSlot=تحرير المنطقة الزمنية
att_timeSlot_browseBreakTime=تصفح كسر وقت zome
att_timeSlot_addBreakTime=إضافة استراحة المنطقة الزمنية
att_timeSlot_enableFlexibleWork=تمكين العمل المرن
att_timeSlot_advanceWorkMinutes=يمكن أن تذهب إلى العمل مقدما
att_timeSlot_delayedWorkMinutes=يمكن تأجيله للعمل
att_timeSlot_advanceWorkMinutesValidMsg1=عدد الدقائق قبل الذهاب إلى العمل أكبر من عدد الدقائق التي يمكن أن تذهب للعمل مسبقًا
att_timeSlot_advanceWorkMinutesValidMsg2=عدد الدقائق التي يمكن أن تذهب للعمل مسبقًا أقل من عدد الدقائق قبل الذهاب إلى العمل
att_timeSlot_advanceWorkMinutesValidMsg3=عدد الدقائق التي يمكن العمل مسبقًا أقل من أو يساوي عدد الدقائق قبل تسجيل الدخول للعمل الإضافي.
att_timeSlot_advanceWorkMinutesValidMsg4=عدد الدقائق التي يمكن أن تكون قبل تسجيل الدخول للعمل الإضافي أكبر من أو يساوي عدد الدقائق تم العمل مسبقًا.
att_timeSlot_delayedWorkMinutesValidMsg1=عدد الدقائق بعد بعد العمل أكبر من عدد الدقائق التي يمكن تأجيلها إلى العمل
att_timeSlot_delayedWorkMinutesValidMsg2=عدد الدقائق لـ يمكن تأجيله إلى العمل أقل من عدد الدقائق بعد بعد العمل
att_timeSlot_delayedWorkMinutesValidMsg3=عدد الدقائق التي يمكن جدولتها للعمل أقل من أو تساوي عدد الدقائق بعد بعد العمل ، والتوقيع وبدء العمل الإضافي
att_timeSlot_delayedWorkMinutesValidMsg4=عدد الدقائق التي يمكن أن تكون بعد العمل والتوقيع وبدء العمل الإضافي أكبر من أو يساوي عدد الدقائق بعد المجدولة للعمل
att_timeSlot_allowLateMinutesValidMsg1=عدد الدقائق المسموح بتأخيرها أقل من عدد الدقائق بعد العمل
att_timeSlot_allowLateMinutesValidMsg2=عدد الدقائق بعد بعد العمل أكبر من عدد الدقائق المسموح بها الدقائق المتأخرة
att_timeSlot_allowEarlyMinutesValidMsg1=السماح بالدقائق المبكرة أقل من دقائق قبل العمل
att_timeSlot_allowEarlyMinutesValidMsg2=عدد الدقائق قبل قبل العمل أكبر من عدد الدقائق المسموح بها الدقائق المتبقية مبكرًا
att_timeSlot_timeOverlap=يتداخل وقت الراحة ، يرجى تعديل فترة الراحة المتبقية!
att_timeSlot_atLeastOne=فترة راحة واحدة على الأقل!
att_timeSlot_mostThree=حتى 3 فترات راحة!
att_timeSlot_canNotEqual=لا يمكن أن يكون وقت البدء لفترة الراحة مساويًا لوقت الانتهاء!
att_timeSlot_shoudInWorkTime=يرجى التأكد من أن فترة الراحة في غضون ساعات العمل!
att_timeSlot_repeatBreakTime=كرر فترة الراحة!
att_timeSlot_toWorkLe=وقت العمل أقل من الحد الأدنى لوقت البدء لفترة الراحة المحددة:
att_timeSlot_offWorkGe=ساعات الإيقاف أكبر من الحد الأقصى لوقت النهاية لفترة الراحة المحددة:
att_timeSlot_crossDays_toWork=الحد الأدنى لوقت البدء لفترة الاستراحة هو ضمن الفترة الزمنية:
att_timeSlot_crossDays_offWork=الحد الأقصى لوقت نهاية فترة الراحة هو خلال الفترة الزمنية:
att_timeSlot_allowLateMinutesRemark=من وقت العمل إلى بطاقة الدقائق المتأخرة المسموح بها لحساب بطاقة العمل العادية
att_timeSlot_allowEarlyMinutesRemark=تبدأ في وقت مبكر من وقت خارج الخدمة في عدد الدقائق المسموح للمغادرة في وقت مبكر ، وبطاقة خارج الخدمة العادية
att_timeSlot_isSegmentDeductionRemark=حذف فترة الراحة في الفترة الزمنية
att_timeSlot_attEnableFlexibleWorkRemark1=لا يُسمح للعمل المرن بتعيين عدد مرات المغادرة المتأخرة والمبكرة
att_timeSlot_afterToWorkRemark=بعد العمل ، تكون الدقائق مساوية لدقائق مؤجلة للعمل
att_timeSlot_beforeOffWorkRemark=قبل دقائق العمل يساوي يمكن أن تذهب إلى العمل دقيقة
att_timeSlot_attEnableFlexibleWorkRemark2=عدد الدقائق بعد بعد العمل أكبر من أو يساوي خارج ساعات العمل + ساعات العمل المتأخرة
att_timeSlot_attEnableFlexibleWorkRemark3=يمكنك العمل في دقائق مقدما ، ويجب أن تكون أقل من أو تساوي دقائق العمل في دقائق للعمل الإضافي
att_timeSlot_attEnableFlexibleWorkRemark4=مؤجلة للعمل دقائق ، يجب أن يكون أقل من أو يساوي N دقيقة من العمل
att_timeSlot_attBeforeToWorkAsOvertimeRemark=مثال: فئة 9: 00 ، سجّل الدخول للعمل الإضافي قبل 60 دقيقة من العمل ، ثم سجل الدخول قبل الساعة 8 صباحًا إلى الساعة 8 ساعات العمل الإضافي
att_timeSlot_attAfterOffWorkAsOvertimeRemark=مثال: بعد الساعة 18:00 من العمل ، وبعد 60 دقيقة من العمل ، قم بالتوقيع على الانسحاب والعمل الإضافي ، ثم ابدأ العمل الإضافي من الساعة 19 حتى وقت تسجيل المغادرة.
att_timeSlot_longTimeValidRemark=لا يمكن أن يتداخل وقت التوقيع الفعلي لـ بعد العمل ووقت التوقيع الفعلي لـ قبل العمل في الفترة الزمنية!
att_timeSlot_advanceWorkMinutesValidMsg5=عدد الدقائق الصالحة قبل تسجيل الوصول أكبر من عدد الدقائق التي يمكن العمل بها مسبقًا
att_timeSlot_advanceWorkMinutesValidMsg6=يجب أن تكون دقائق العمل مسبقًا أقل من دقائق صالحة لتسجيل الدخول قبل العمل
att_timeSlot_delayedWorkMinutesValidMsg5=عدد الدقائق الصالحة بعد تسجيل الوصول أكبر من عدد الدقائق التي يمكن تأجيلها للعمل
att_timeSlot_delayedWorkMinutesValidMsg6=يجب أن يكون عدد الدقائق التي يمكن تأجيلها للعمل أقل من الدقائق الصحيحة بعد تسجيل الدخول
att_timeSlot_advanceWorkMinutesValidMsg7=لا يمكن أن يتداخل وقت تسجيل الوصول قبل العمل مع وقت المغادرة بعد العمل في اليوم السابق
att_timeSlot_delayedWorkMinutesValidMsg7=لا يمكن أن يتداخل وقت المغادرة بعد العمل مع وقت تسجيل الوصول قبل العمل في اليوم التالي
att_timeSlot_maxOvertimeMinutes=الحد الأقصى لساعات العمل الإضافي
#班次
att_shift_basicSet=نوع الجدول
att_shift_advancedSet=اسم الجدول
att_shift_type=تحول نوع
att_shift_name=تحول الاسم
att_shift_regularShift=التحول العادي
att_shift_flexibleShift=التحول المرن
att_shift_color=اللون
att_shift_periodicUnit=وحدة
att_shift_periodNumber=دورة
att_shift_startDate=تاريخ البدء
att_shift_startDate_firstDay=تاريخ بدء الدورة
att_shift_isShiftWithinMonth=دورة التحول في شهر واحد
att_shift_attendanceMode=وضع الحضور
att_shift_shiftNormal=بطاقة ممغنطة وفقًا للتحول العادي
att_shift_oneDayOneCard=اسحب مرة واحدة في أي وقت في اليوم
att_shift_onlyBrushTime=فقط حساب وقت بطاقة السحب
att_shift_notBrushCard=انتقد مجانا
att_shift_overtimeMode=وضع العمل الإضافي
att_shift_autoCalc=حساب الكمبيوتر التلقائي
att_shift_mustApply=يجب تطبيق العمل الإضافي
att_shift_mustOvertime=يجب العمل الإضافي أو الغياب
att_shift_timeSmaller=مدة أقصر بين الحساب التلقائي واستلام العمل الإضافي
att_shift_notOvertime=لا تحسب كعمل إضافي
att_shift_overtimeSign=نوع العمل الإضافي
att_shift_normal=يوم عادي
att_shift_restday=يوم الراحة
att_shift_timeSlotDetail=تفاصيل الجدول الزمني
att_shift_doubleDeleteTimeSlot=انقر نقرا مزدوجا فوق فترة التحول. يمكنك حذف الفترة الزمنية
att_shift_addTimeSlot=إضافة جدول زمني
att_shift_cleanTimeSlot=واضح الجدول الزمني
att_shift_NO=لا.
att_shift_notAll=إلغاء تحديد الكل
att_shift_notTime=إذا تعذر تحديد خانة الاختيار تفاصيل الجدول الزمني ، فهذا يشير إلى وجود تداخل في الجدول الزمني.
att_shift_notExistTime=هذا الجدول الزمني غير موجود.
att_shift_cleanAllTimeSlot=هل أنت متأكد من أنك تريد مسح الجدول الزمني للتحول المحدد؟
att_shift_pleaseCheckBox=يرجى تحديد مربع الاختيار الموجود على الجانب الأيسر وهو نفس وقت العرض الحالي على الجانب الأيمن.
att_shift_pleaseUnit=يرجى ملء وحدات الدورة وعدد الدورات.
att_shift_pleaseAllDetailTimeSlot=يرجى تحديد تفاصيل الجدول الزمني.
att_shift_placeholderNo=يوصى بالبدء بـ S ، مثل S0.
att_shift_placeholderName=أقترح أن تبدأ بـ S أو نهاية التحول
att_shift_workType=نوع العمل
att_shift_normalWork=العمل العادي
att_shift_holidayOt=عطلة العمل الإضافي في أيام العطلات
att_shift_attShiftStartDateRemark=مثال: تاريخ بدء الدورة هو الرقم 22 ، مع فترة ثلاثة أيام ، ثم الرقم 22/23/24 في الفئة A / B / C ، والرقم 19/20/21 في الفئة A. / فئة B / فئة C ، قبل وبعد التاريخ وهلم جرا.
att_shift_isShiftWithinMonthRemark1=الانتقال خلال الشهر ، لا تدور الدورة إلا في اليوم الأخير من كل شهر ، وليس من المقرر تحديدها على مدار الشهر ؛
att_shift_isShiftWithinMonthRemark2=نوبة شهرية ، يتم تدوير الدورة إلى اليوم الأخير من كل شهر ، إذا لم تنته دورة واحدة ، تابع إلى الشهر التالي ، وهكذا ؛
att_shift_workTypeRemark1=ملاحظة: عند تحديد نوع العمل كوقت إضافي في يوم راحة ، لن يتم احتساب الحضور في يوم العطلة.
att_shift_workTypeRemark2=عطلة نهاية الأسبوع OT ، يتم تعيين علامة العمل الإضافي إلى يوم الراحة ويقوم الكمبيوتر تلقائيًا بحساب الوقت الإضافي. لا يلزم تطبيق العمل الإضافي. يتم تسجيل ساعات العمل في اليوم كساعات عمل إضافي ، ولا يتم احتساب الحضور خلال العطلات.
att_shift_workTypeRemark3=Holiday OT ، والوقت الإضافي لعلامة العمل الإضافي هو أيام العطلات ، ويقوم الكمبيوتر تلقائيًا بحساب الوقت الإضافي ، ولا يلزم تطبيق العمل الإضافي ، ويتم تسجيل ساعات العمل في اليوم كساعات عمل إضافي ؛
att_shift_attendanceModeRemark1=باستثناء الضرب عادةً عن طريق الإزاحة ، لا يُعتبر الوقت الإضافي مبكرًا أو متأخرًا ، على سبيل المثال:
att_shift_attendanceModeRemark2=1. لا يلزم إجراء عملية ثقب أو ثقب بطاقة صالحة مرة واحدة في اليوم ، ولا يتم احتساب وقت إضافي ؛
att_shift_attendanceModeRemark3=2. نوع العمل: العمل العادي ، وضع الحضور: انتقد بطاقة مجانية ، ثم يعتبر وقت التحول اليوم فعالة وقت العمل ؛
att_shift_periodStartMode=نوع بداية الفترة
att_shift_periodStartModeByPeriod=تاريخ بدء الفترة
att_shift_periodStartModeBySch=تاريخ بدء التحول
att_shift_addTimeSlotToShift=ما إذا كان سيتم إضافة الفاصل الزمني لهذا التحول
#=====================================================================
#分组
att_group_editGroup=تحرير الموظفين للمجموعة
att_group_browseGroupPerson=استعرض أفراد المجموعة
att_group_list=قائمة المجموعة
att_group_placeholderNo=من المستحسن أن تبدأ بـ G ، مثل G1
att_group_placeholderName=من المستحسن أن تبدأ بـ G أو تنتهي بـ group.
att_widget_deptHint=ملاحظة: استيراد جميع الموظفين تحت الإدارة المختارة
att_widget_searchType=الاستعلام الشرطي
att_widget_noPerson=لم تختر أحدا
#分组排班
#部门排班
att_deptSch_existsDept=يوجد تحول في القسم ولا يُسمح بحذف القسم.
#人员排班
att_personSch_view=عرض جدولة الموظفين
#临时排班
att_schedule_type=نوع الجدول
att_schedule_tempType=نوع مؤقت
att_schedule_normal=جدول عادي
att_schedule_intelligent=اكتشاف ذكي للفئة
att_tempSch_scheduleType=نوع الجدولة
att_tempSch_startDate=تاريخ البدء
att_tempSch_endDate=تاريخ الانتهاء
att_tempSch_attendanceMode=وضع الحضور
att_tempSch_overtimeMode=وضع العمل الإضافي
att_tempSch_overtimeRemark=العمل الإضافي مارك
att_tempSch_existsDept=يوجد تحول مؤقت في القسم ، ولا يُسمح بحذف القسم.
att_schedult_opAddTempSch=التحول المؤقت الجديد
att_schedule_cleanEndDate=وقت نهاية فارغ
att_schedule_selectOne=يمكن للجدول الزمني العادي فقط اختيار نوبة واحدة!
att_schedule_selectPerson=يرجى اختيار الموظفين أولا!
att_schedule_selectDept=يرجى اختيار القسم أولا!
att_schedule_selectGroup=يرجى اختيار المجموعة أولا!
att_schedule_selectOneGroup=يمكن اختيار مجموعة واحدة فقط!
att_schedule_arrange=يرجى اختيار التحول!
att_schedule_leave=غادر
att_schedule_trip=رحلة عمل
att_schedule_out=خارج
att_schedule_off=راحة
att_schedule_makeUpClass=ألحق
att_schedule_class=يعدل
att_schedule_holiday=يوم الاجازة
att_schedule_offDetail=ضبط الراحة
att_schedule_makeUpClassDetail=إلحاق الحضور
att_schedule_classDetail=ضبط التحول
att_schedule_holidayDetail=يوم الاجازة
att_schedule_normalDetail=عادي
att_schedule_noSchDetail=غير مجدولة
att_schedule_normalSchInfo=نوبات مركزية: لا نوبة عمل متقاطعة
att_schedule_multipleInterSchInfo=إزاحة مفصولة بفواصل: نوبات متعددة لعدة أيام
att_schedule_inderSchFirstDayInfo=التبديل عبر إزاحة الشبكة للخلف: يتم تسجيل إزاحة التبادل على مدار اليوم في اليوم الأول
att_schedule_inderSchSecondDayInfo=إزاحة الشبكة عبر الإزاحة للأمام: يتم تسجيل إزاحة الأيام المتقاطعة في اليوم الثاني
att_schedule_timeConflict=تعارض مع فترة التحول الحالية ، لا يسمح للحفظ!
#=====================================================================
att_excp_notExisetPerson=شخص لا وجود له!
att_excp_leavePerson=تفكيك الموظفين!
#补签单
att_sign_signTime=لكمة الوقت
att_sign_signDate=لكمة التاريخ
#请假
att_leave_arilName=نوع الإجازة
att_leave_image=ترك طلب الصورة
att_leave_imageShow=لا صور
att_leave_imageType=نصيحة خاطئة: تنسيق الصورة غير صحيح ، ودعم التنسيق: JPEG ، GIF ، PNG!
att_leave_imageSize=نصيحة خاطئة: الصورة المحددة كبيرة جدًا ، والحد الأقصى لحجم الصورة 4 ميغابايت!
att_leave_leaveLongDay=المدة (بالأيام)
att_leave_leaveLongHour=المدة (ساعة)
att_leave_leaveLongMinute=المدة (بالدقائق)
att_leave_endNoLessAndEqualStart=لا يمكن أن يكون وقت الانتهاء أقل من أو يساوي وقت البدء
att_leave_typeNameNoExsists=اسم الفئة المزيفة غير موجود
att_leave_startNotNull=لا يمكن أن يكون وقت البدء فارغًا
att_leave_endNotNull=لا يمكن أن يكون وقت الانتهاء فارغًا
att_leave_typeNameConflict=يتعارض اسم النوع المزيف مع اسم حالة الحضور
#出差
att_trip_tripLongDay=طول رحلة العمل (يوم)
att_trip_tripLongMinute=مدة رحلة العمل (بالدقائق)
att_trip_tripLongHour=وقت رحلة العمل (ساعات)
#外出
att_out_outLongDay=طول الخروج (يوم)
att_out_outLongMinute=طول الخروج (بالدقائق)
att_out_outLongHour=الوقت المستغرق (الوقت)
#加班
att_overtime_type=نوع العمل الإضافي
att_overtime_normal=عادة العمل الإضافي
att_overtime_rest=عطلة نهاية الاسبوع العمل الإضافي في أيام الراحة
att_overtime_overtimeLong=طول العمل الإضافي (بالدقائق)
att_overtime_overtimeHour=ساعات العمل الإضافي (ساعات)
att_overtime_notice=وقت العمل الإضافي غير مسموح به لأكثر من يوم واحد!
att_overtime_minutesNotice=لا يمكن أن يكون وقت طلب العمل الإضافي أقل من الحد الأدنى لوقت العمل الإضافي!
#调休补班
att_adjust_type=ضبط النوع
att_adjust_adjustDate=ضبط التاريخ
att_adjust_shiftName=إلحاق الحضور التحول
att_adjust_selectClass=يرجى تحديد اسم التحول الذي يحتاج إلى إلحاق الحضور
att_shift_notExistShiftWorkDate={1} جدولة التحول في {0} والباقي غير مسموح له بالتقدم للحصول على نوبة التجميل!
att_adjust_shiftPeriodStartMode=التحول المحدد لورشة التجميل ، إذا كان تاريخ البدء عن طريق التحول ، الافتراضي هو 0
att_adjust_shiftNameNoNull=لا يمكن أن تكون نوبات المكياج فارغة
att_adjust_shiftNameNoExsist=تحول الماكياج غير موجود
#调班
att_class_type=ضبط النوع
att_class_sameTimeMoveShift=ضبط التحول الشخصية في نفس اليوم
att_class_differenceTimeMoveShift=ضبط التحول الشخصية في أيام أخرى
att_class_twoPeopleMove=تبادل شخصين
att_class_moveDate=ضبط التاريخ
att_class_shiftName=ضبط اسم التحول
att_class_moveShiftName=اسم التحول العكسي
att_class_movePersonPin=ضبط معرف الموظفين
att_class_movePersonName=ضبط اسم الموظفين
att_class_movePersonLastName=ضبط الموظفين اسم العائلة
att_class_moveDeptName=ضبط اسم القسم
att_class_personPin=لا يمكن أن يكون معرف الموظفين فارغًا
att_class_shiftNameNoNull=لا يمكن أن يكون التحول المعدل الجديد فارغًا.
att_class_personPinNoNull=لا يمكن أن يكون معرف الموظفين للشخص الجديد فارغًا!
att_class_isNotExisetSwapPersonPin=الشخص الضبط الجديد غير موجود ، يرجى إعادة إضافته!
att_class_personNoSame=لا يمكنك ضبط نفس الشخص ، يرجى المحاولة مرة أخرى.
att_class_outTime=لا يمكن أن يكون تاريخ التعديل وتاريخ النقل أكثر من شهر واحد
att_class_shiftNameNoExsist=تحول التعديل غير موجود
att_class_swapPersonNoExisist=الخاطبة غير موجودة
att_class_dateNoSame=يتم نقل الأفراد في تواريخ مختلفة ، لا يمكن أن تكون التواريخ هي نفسها
#=====================================================================
#节点
att_node_name=العقدة
att_node_type=نوع العقدة
att_node_leader=القائد المباشر
att_node_leaderNode=عقدة الزعيم المباشر
att_node_person=الشخص المعين
att_node_position=تعيين الموقف
att_node_choose=اختر الوظيفة
att_node_personNoNull=الموظفين لا يمكن أن تكون فارغة!
att_node_posiitonNoNull=الموضع لا يمكن أن يكون فارغا
att_node_placeholderNo=يوصى بالبدء بـ N ، مثل N01.
att_node_placeholderName=يوصى بالبدء بموضع أو اسم ، ينتهي بعقدة ، مثل مدير العقدة.
att_node_searchPerson=معايير البحث المدخلات
att_node_positionIsExist=الموضع موجود بالفعل في بيانات العقدة ، يرجى تحديد الموضع مرة أخرى.
#流程
att_flow_type=نوع التدفق
att_flow_rule=قاعدة التدفق
att_flow_rule0=أقل من أو يساوي 1 يوم
att_flow_rule1=أكثر من يوم واحد وأقل من أو يساوي 3 أيام
att_flow_rule2=أكثر من 3 أيام وأقل من أو تساوي 7 أيام
att_flow_rule3=أكثر من 7 أيام
att_flow_node=عقدة الموافقة
att_flow_start=بدء التدفق
att_flow_end=نهاية التدفق
att_flow_addNode=إضافة العقدة
att_flow_placeholderNo=يوصى ببدء التشغيل بـ F ، مثل F01.
att_flow_placeholderName=يوصى ببدء الكتابة بالنهاية ، مع التدفق ، على سبيل المثال ، تدفق التدفق.
att_flow_tips=ملاحظة: ترتيب الموافقة على العقد هو من أعلى إلى أسفل ، ويمكنك سحب الترتيب بعد التحديد.
#申请
att_apply_personPin=معرف مقدم الطلب
att_apply_type=نوع الاستثناء
att_apply_flowStatus=الوضع الكلي للتدفق
att_apply_start=بدء تطبيق
att_apply_flowing=قيد الانتظار
att_apply_pass=تم الاجتياز بنجاح
att_apply_over=النهاية
att_apply_refuse=مرفوض
att_apply_revoke=سحب او إبطال
att_apply_except=استثناء
att_apply_view=عرض التفاصيل
att_apply_leaveTips=الشخص لديه طلب إجازة خلال هذه الفترة الزمنية!
att_apply_tripTips=الموظفين لديهم طلب رحلة عمل خلال هذه الفترة الزمنية!
att_apply_outTips=تقدم الأفراد بطلب الخروج خلال هذه الفترة الزمنية!
att_apply_overtimeTips=الموظفين لديهم تطبيقات العمل الإضافي خلال هذه الفترة الزمنية!
att_apply_adjustTips=خلال هذه الفترة الزمنية ، يمكن للموظفين التقدم للحصول على بروفة!
att_apply_classTips=الموظفين لديهم تطبيق التحول خلال هذه الفترة الزمنية!
#审批
att_approve_wait=ما زال يحتاج بتصدير
att_approve_refuse=لم يمر
att_approve_reason=السبب
att_approve_personPin=معرف الموافقة
att_approve_personName=اسم الموافقة
att_approve_person=الموافق
att_approve_isPass=سواء للموافقة؟
att_approve_status=حالة العقدة الحالية
att_approve_tips=النقطة الزمنية موجودة بالفعل في التدفق ولا يمكن تكرارها.
att_approve_tips2=لم يتم تكوين عقدة التدفق ، يرجى الاتصال بالمسؤول عن التكوين.
att_approve_offDayConflicts={0} غير مجدول أو مجدول في {1} والباقي غير مسموح به.
att_approve_shiftConflicts={0} لديه بالفعل نوبة في {1} ولا يسمح بالتقدم للحصول على نوبات في يوم العمل!
att_approve_shiftNoSch={0} لا يُسمح بتطبيق shift عندما لا يكون الجدول الزمني للعمل على {1}!
att_approve_classConflicts=لا يسمح بتغيير التطبيق في التاريخ غير المجدول
att_approve_selectTime=سيحدد وقت الاختيار العملية وفقًا للقواعد
att_approve_withoutPermissionApproval=يوجد سير عمل بدون إذن بالموافقة ، يرجى التحقق!
#=====================================================================
#考勤计算
att_op_calculation=حساب الحضور
att_op_calculation_notice=تم حساب بيانات الحضور في الخلفية ، يرجى إعادة المحاولة لاحقًا!
att_op_calculation_leave=بما في ذلك الموظفين المستقيل
att_statistical_choosePersonOrDept=يرجى اختيار الإدارة أو الموظفين!
att_statistical_sureCalculation=هل أنت متأكد من رغبتك في إجراء حسابات الحضور؟
att_statistical_filter=حالة الترشيح جاهزة!
att_statistical_initData=تهيئة قاعدة البيانات كاملة!
att_statistical_exception=تهيئة بيانات الاستثناء كاملة!
att_statistical_error=فشل حساب الغياب!
att_statistical_begin=البدء في حساب!
att_statistical_end=إنهاء الحساب!
att_statistical_noticeTime=الحضور نطاق زمني اختياري: أول شهرين إلى اليوم!
att_statistical_remarkHoliday=H
att_statistical_remarkClass=C
att_statistical_remarkNoSch=NS
att_statistical_remarkRest=R
#原始记录表
att_op_importAccRecord=استيراد سجل التحكم في الوصول
att_op_importParkRecord=استيراد سجل وقوف السيارات
att_op_importInsRecord=استيراد سجلات faceKiosk
att_op_importPidRecord=استيراد سجلات الشهادة الشخصية
att_op_importVmsRecord=استيراد سجل الفيديو
att_op_importUSBRecord=استيراد سجل يو القرص
att_transaction_noAccModule=لا وحدة التحكم في الوصول!
att_transaction_noParkModule=لا وحدة وقوف السيارات!
att_transaction_noInsModule=لا وحدة faceKiosk!
att_transaction_noPidModule=لا توجد وحدة شهادة شخصية!
att_transaction_exportRecord=تصدير السجلات الأصلية
att_transaction_exportAttPhoto=تصدير صور الحضور
att_transaction_exportDate=تاريخ التصدير
att_transaction_fileIsTooLarge=الملف الذي تم تصديره كبير جدًا ، يُرجى تضييق النطاق الزمني
att_statistical_attDatetime=وقت الحضور
att_statistical_attPhoto=صور الحضور
att_statistical_attDetail=تفاصيل الحضور
att_statistical_acc=جهاز التحكم في الوصول
att_statistical_att=جهاز وقت الحضور
att_statistical_park=كاميرا LPR
att_statistical_faceRecognition=جهاز التعرف على الوجه
att_statistical_app=أجهزة محمولة
att_statistical_vms=معدات الفيديو
att_statistical_psg=معدات القناة
att_statistical_dataSources=مصادر البيانات
att_transaction_SyncRecord=مزامنة سجلات الحضور
#日打卡详情表
att_statistical_dayCardDetail=تفاصيل تسجيل الوصول
att_statistical_cardDate=تاريخ التسجيل
att_statistical_cardNumber=سجل مرات
att_statistical_earliestTime=أقرب وقت
att_statistical_latestTime=آخر مرة
att_statistical_cardTime=انتقد بطاقة الوقت
#请假汇总表
att_statistical_leaveDetail=ترك التفاصيل
#日明细报表
att_statistical_attDate=تاريخ الحضور
att_statistical_week=أسبوع
att_statistical_shiftInfo=التحول المعلومات
att_statistical_shiftTimeData=العمل على / قبالة الوقت
att_statistical_cardValidData=انتقد بطاقة الوقت
att_statistical_cardValidCount=انتقد عدد البطاقات
att_statistical_lateCount=العد المتأخر
att_statistical_lateMinute=الدقائق المتأخرة
att_statistical_earlyCount=العد المبكر
att_statistical_earlyMinute=الدقائق (الدقائق) المبكرة
att_statistical_countData=تايمز البيانات
att_statistical_minuteData=دقائق البيانات
att_statistical_attendance_minute=الحضور (دقائق)
att_statistical_overtime_minute=العمل الإضافي (بالدقائق)
att_statistical_unusual_minute=غير طبيعي (بالدقائق)
#月明细报表
att_monthdetail_should_hour=يجب (الوقت)
att_monthdetail_actual_hour=الوقت الفعلي)
att_monthdetail_valid_hour=وقت الصلاحية)
att_monthdetail_absent_hour=الكفر (الوقت)
att_monthdetail_leave_hour=إجازة (الوقت)
att_monthdetail_trip_hour=رحلة عمل (كل ساعة)
att_monthdetail_out_hour=الخروج (بالساعة)
att_monthdetail_should_day=يجب (اليوم)
att_monthdetail_actual_day=فعلي (اليوم)
att_monthdetail_valid_day=يوم مؤثر)
att_monthdetail_absent_day=الانتهاء (اليوم)
att_monthdetail_leave_day=ترك يوم)
att_monthdetail_trip_day=رحلة عمل (يوم)
att_monthdetail_out_day=الخروج (يوم)
#月统计报表
att_statistical_late_minute=المدة (بالدقائق)
att_statistical_early_minute=المدة (بالدقائق)
#部门统计报表
#年度统计报表
att_statistical_should=ينبغي
att_statistical_actual=فعلي
att_statistical_valid=صالح
att_statistical_numberOfTimes=مرات
att_statistical_usually=يوم من أيام الأسبوع
att_statistical_rest=عطلة نهاية الاسبوع
att_statistical_holiday=يوم الاجازة
att_statistical_total=مجموع
att_statistical_month=إحصائيات الشهر
att_statistical_year=إحصائيات السنة
att_statistical_attendance_hour=الحضور (ساعة)
att_statistical_attendance_day=الحضور (اليوم)
att_statistical_overtime_hour=العمل الإضافي (ساعة)
att_statistical_unusual_hour=استثناء (ساعة)
att_statistical_unusual_day=غير طبيعي (اليوم)
#考勤设备参数
att_deviceOption_query=عرض معلمات الجهاز
att_deviceOption_noOption=لا توجد معلومات المعلمة ، يرجى الحصول على المعلمات الجهاز أولا
att_deviceOption_name=اسم المعلمة
att_deviceOption_value=قيمة المعلمة
att_deviceOption_UserCount=رقم المستخدم
att_deviceOption_MaxUserCount=الحد الأقصى لعدد المستخدمين
att_deviceOption_FaceCount=عدد قوالب الوجه الحالية
att_deviceOption_MaxFaceCount=الحد الأقصى لعدد قوالب الوجه
att_deviceOption_FacePhotoCount=العدد الحالي لصور الوجه
att_deviceOption_MaxFacePhotoCount=الحد الأقصى لعدد صور الوجه
att_deviceOption_FingerCount=عدد قوالب بصمات الأصابع الحالية
att_deviceOption_MaxFingerCount=الحد الأقصى لعدد قوالب بصمات الأصابع
att_deviceOption_FingerPhotoCount=عدد الصور بصمة الحالية
att_deviceOption_MaxFingerPhotoCount=الحد الأقصى لعدد صور بصمات الأصابع
att_deviceOption_FvCount=عدد قوالب الوريد الاصبع الحالية
att_deviceOption_MaxFvCount=الحد الأقصى لعدد قوالب الوريد الاصبع
att_deviceOption_FvPhotoCount=عدد الصور الوريد الاصبع الحالية
att_deviceOption_MaxFvPhotoCount=عدد صور الوريد الاصبع
att_deviceOption_PvCount=عدد قوالب النخيل الحالية
att_deviceOption_MaxPvCount=الحد الأقصى لعدد قوالب النخيل
att_deviceOption_PvPhotoCount=صور النخيل الحالية
att_deviceOption_MaxPvPhotoCount=الحد الأقصى لعدد صور النخيل
att_deviceOption_TransactionCount=العدد الحالي للسجلات
att_deviceOption_MaxAttLogCount=الحد الأقصى لعدد السجلات
att_deviceOption_UserPhotoCount=صور المستخدم الحالي
att_deviceOption_MaxUserPhotoCount=الحد الأقصى لعدد صور المستخدم
att_deviceOption_FaceVersion=خوارزمية التعرف على الوجوه
att_deviceOption_FPVersion=بصمة التعرف على خوارزمية الإصدار
att_deviceOption_FvVersion=إصبع الوريد التعرف على خوارزمية الإصدار
att_deviceOption_PvVersion=النخيل الاعتراف خوارزمية الإصدار
att_deviceOption_FWVersion=نسخة برنامج ثابت
att_deviceOption_PushVersion=ادفع النسخة
#=====================================================================
#API
att_api_areaCodeNotNull=لا يمكن أن يكون رقم المنطقة فارغًا
att_api_pinsNotNull=لا يُسمح أن تكون بيانات الرقم السري فارغة
att_api_pinsOverSize=طول بيانات رقم التعريف الشخصي غير مسموح به يتجاوز 500
att_api_areaNoExist=المنطقة غير موجودة
att_api_sign=تكملة
#=====================================================================
#休息时间段
att_leftMenu_breakTime=فترة إستراحة
att_breakTime_startTime=وقت البدء
att_breakTime_endTime=وقت النهاية
#=====================================================================
#导入U盘记录
att_import_uploadFileSuccess=حمّل الملف بنجاح ، وابدأ تحليل بيانات الملف ، يرجى الانتظار ...
att_import_resolutionComplete=بعد اكتمال التحليل ، ابدأ في تحديث قاعدة البيانات.
att_import_snNoExist=جهاز الحضور المطابق للملف المستورد غير موجود. يرجى تحديد الملف مرة أخرى.
att_import_fileName_msg=متطلبات تنسيق اسم الملف الذي تم استيراده: يبدأ الرقم التسلسلي للجهاز بـ "_" ويتم فصله بتسطير أسفل السطر "، على سبيل المثال" 3517171600001_attlog.dat "。
att_import_notSupportFormat=هذا التنسيق غير مدعوم في الوقت الحالي!
att_import_selectCorrectFile=يرجى تحديد ملف التنسيق الصحيح!
att_import_fileFormat=تنسيق الملف
att_import_targetFile=الملف الهدف
att_import_startRow=عدد الصفوف في بداية الرأس
att_import_startRowNote=السطر الأول من تنسيق البيانات هو استيراد البيانات ، يرجى التحقق من الملف قبل الاستيراد.
att_import_delimiter=الفاصل
#=====================================================================
#设备操作日志
att_device_op_log_op_type=رمز العملية
att_device_op_log_dev_sn=الرقم التسلسلي للجهاز
att_device_op_log_op_content=المحتوى التشغيلي
att_device_op_log_operator_pin=رقم المشغل
att_device_op_log_operator_name=اسم المشغل
att_device_op_log_op_time=وقت العملية
att_device_op_log_op_who_value=قيمة كائن العملية
att_device_op_log_op_who_content=وصف كائن العملية
att_device_op_log_op_value1=كائن التشغيل 2
att_device_op_log_op_value_content1=وصف كائن العملية 2
att_device_op_log_op_value2=كائن التشغيل 3
att_device_op_log_op_value_content2=وصف كائن العملية 3
att_device_op_log_op_value3=كائن التشغيل 4
att_device_op_log_op_value_content3=وصف كائن العملية 4
#操作日志的操作类型
att_device_op_log_opType_0=تشغيل
att_device_op_log_opType_1=اغلق
att_device_op_log_opType_2=فشل التحقق
att_device_op_log_opType_3=إنذار
att_device_op_log_opType_4=أدخل القائمة
att_device_op_log_opType_5=تغيير الاعدادات
att_device_op_log_opType_6=تسجيل بصمة
att_device_op_log_opType_7=تسجيل كلمة المرور
att_device_op_log_opType_8=تسجيل بطاقة HID
att_device_op_log_opType_9=مسح المستخدم
att_device_op_log_opType_10=حذف بصمة
att_device_op_log_opType_11=حذف كلمة المرور
att_device_op_log_opType_12=حذف بطاقة RF
att_device_op_log_opType_13=امسح البيانات
att_device_op_log_opType_14=إنشاء بطاقة MF
att_device_op_log_opType_15=تسجيل بطاقة MF
att_device_op_log_opType_16=تسجيل بطاقة MF
att_device_op_log_opType_17=حذف تسجيل بطاقة MF
att_device_op_log_opType_18=مسح محتوى بطاقة MF
att_device_op_log_opType_19=نقل بيانات التسجيل إلى البطاقة
att_device_op_log_opType_20=انسخ البيانات من البطاقة إلى الجهاز
att_device_op_log_opType_21=ضبط الوقت
att_device_op_log_opType_22=اعدادات المصنع
att_device_op_log_opType_23=حذف سجلات الدخول والخروج
att_device_op_log_opType_24=مسح امتيازات المسؤول
att_device_op_log_opType_25=تعديل إعدادات مجموعة التحكم في الوصول
att_device_op_log_opType_26=تعديل إعدادات وصول المستخدم
att_device_op_log_opType_27=تعديل فترة وصول الوقت
att_device_op_log_opType_28=تعديل فتح إعدادات الجمع
att_device_op_log_opType_29=الغاء القفل
att_device_op_log_opType_30=تسجيل المستخدمين الجدد
att_device_op_log_opType_31=تغيير سمات بصمات الأصابع
att_device_op_log_opType_32=إنذار الإكراه
att_device_op_log_opType_34=مضادة للغواصات
att_device_op_log_opType_35=حذف صور الحضور
att_device_op_log_opType_36=تعديل معلومات المستخدم
att_device_op_log_opType_37=يوم الاجازة
att_device_op_log_opType_38=إستعادة البيانات
att_device_op_log_opType_39=نسخ إحتياطي للبيانات
att_device_op_log_opType_40=يو القرص تحميل
att_device_op_log_opType_41=يو القرص التحميل
att_device_op_log_opType_42=يو سجل تشفير الحضور سجل
att_device_op_log_opType_43=حذف السجل بعد نجاح تنزيل قرص USB
att_device_op_log_opType_53=التبديل المنتهية ولايته
att_device_op_log_opType_54=استشعار الباب
att_device_op_log_opType_55=إنذار
att_device_op_log_opType_56=استعادة المعلمات
att_device_op_log_opType_68=صورة المستخدم المسجل
att_device_op_log_opType_69=تعديل صور المستخدم
att_device_op_log_opType_70=تعديل اسم المستخدم
att_device_op_log_opType_71=تعديل أذونات المستخدم
att_device_op_log_opType_76=تعديل إعدادات الشبكة IP
att_device_op_log_opType_77=تعديل قناع إعدادات الشبكة
att_device_op_log_opType_78=تعديل بوابة إعدادات الشبكة
att_device_op_log_opType_79=تعديل إعدادات الشبكة DNS
att_device_op_log_opType_80=تعديل كلمة مرور إعداد الاتصال
att_device_op_log_opType_81=تعديل معرف الجهاز إعدادات الاتصال
att_device_op_log_opType_82=تعديل عنوان خادم السحاب
att_device_op_log_opType_83=تعديل منفذ خادم السحاب
att_device_op_log_opType_87=تعديل إعدادات سجل التحكم في الوصول
att_device_op_log_opType_88=تعديل وجه المعلمة
att_device_op_log_opType_89=تعديل علامة المعلمة بصمة
att_device_op_log_opType_90=تعديل علامة المعلمة الوريد الإصبع
att_device_op_log_opType_91=تعديل علامة معلمة palmprint
att_device_op_log_opType_92=يو القرص ترقية العلم
att_device_op_log_opType_100=تعديل معلومات بطاقة RF
att_device_op_log_opType_101=تسجيل الوجه
att_device_op_log_opType_102=تعديل أذونات الموظفين
att_device_op_log_opType_103=حذف أذونات الموظفين
att_device_op_log_opType_104=إضافة أذونات الموظفين
att_device_op_log_opType_105=حذف سجل التحكم في الوصول
att_device_op_log_opType_106=حذف الوجه
att_device_op_log_opType_107=حذف صور الموظفين
att_device_op_log_opType_108=تعديل المعلمات
att_device_op_log_opType_109=حدد WIFI SSID
att_device_op_log_opType_110=تمكين الوكيل
att_device_op_log_opType_111=تعديل الوكيل
att_device_op_log_opType_112=تعديل منفذ الوكيل
att_device_op_log_opType_113=تعديل كلمة مرور الشخص
att_device_op_log_opType_114=تعديل معلومات الوجه
att_device_op_log_opType_115=تعديل كلمة مرور المشغل
att_device_op_log_opType_116=استئناف إعدادات التحكم في الوصول
att_device_op_log_opType_117=خطأ إدخال كلمة مرور المشغل
att_device_op_log_opType_118=قفل كلمة مرور المشغل
att_device_op_log_opType_120=تعديل طول بيانات البطاقة القانونية
att_device_op_log_opType_121=تسجيل وريد الإصبع
att_device_op_log_opType_122=تعديل وريد الإصبع
att_device_op_log_opType_123=حذف وريد الإصبع
att_device_op_log_opType_124=تسجيل بصمة اليد
att_device_op_log_opType_125=تعديل بصمة اليد
att_device_op_log_opType_126=حذف بصمة اليد
#操作对象描述
att_device_op_log_content_pin=هوية المستخدم:
att_device_op_log_content_alarm=إنذار:
att_device_op_log_content_alarm_reason=سبب التنبيه:
att_device_op_log_content_update_no=تعديل رقم العنصر:
att_device_op_log_content_update_value=تعديل القيمة:
att_device_op_log_content_finger_no=رقم البصمة:
att_device_op_log_content_finger_size=طول قالب بصمة:
#=====================================================================
#工作流
att_flowable_datetime_to=إلى
att_flowable_todomsg_leave=اترك الموافقة
att_flowable_todomsg_sign=إلحاق موافقة السجل
att_flowable_todomsg_overtime=موافقة العمل الإضافي
att_flowable_notifymsg_leave=اترك إشعار التطبيق
att_flowable_notifymsg_sign=إلحاق إعلام السجل
att_flowable_notifymsg_overtime=إشعار العمل الإضافي
att_flowable_shift=تحول:
att_flowable_hour=ساعة
att_flowable_todomsg_trip=موافقة رحلة العمل
att_flowable_notifymsg_trip=رحلة عمل
att_flowable_todomsg_out=الخروج الموافقة
att_flowable_notifymsg_out=إخطار الخروج
att_flow_apply=تطبيق
att_flow_applyTime=وقت التطبيق
att_flow_approveTime=وقت المعالجة
att_flow_operateUser=مراجع
att_flow_approve=موافقة
att_flow_approveComment=حاشية. ملاحظة
att_flow_approvePass=نتائج الموافقة
att_flow_status_processing=قيد المراجعة
#=====================================================================
#biotime
att_h5_pers_personIdNull=لا يمكن أن يكون معرف الموظف فارغًا
att_h5_attPlaceNull=لا يمكن أن يكون موقع تسجيل الوصول فارغًا
att_h5_attAreaNull=لا يمكن أن تكون منطقة الحضور فارغة
att_h5_pers_personNoExist=رقم الموظف غير موجود
att_h5_signRemarkNull=لا يمكن أن تكون الملاحظات فارغة
att_h5_common_pageNull=خطأ معلمة الترحيل
att_h5_taskIdNotNull=لا يمكن أن يكون معرف عقدة المهمة فارغًا
att_h5_auditResultNotNull=لا يمكن أن تكون نتيجة الموافقة فارغة
att_h5_latLongitudeNull=لا يمكن أن يكون خط الطول وخط العرض فارغين
att_h5_pers_personIsNull=معرف الموظف غير موجود
att_h5_pers_personIsNotInArea=الشخص لم يحدد المنطقة
att_h5_mapApiConnectionsError=خريطة خطأ اتصال api
att_h5_googleMap=خرائط جوجل
att_h5_gaodeMap=خريطة Gaode
att_h5_defaultMap=الخريطة الافتراضية
att_h5_shiftTime=وقت التناوب
att_h5_signTimes=وقت التجديد
att_h5_enterKeyWords=الرجاء إدخال الكلمات الأساسية:
att_h5_mapSet=إعدادات خريطة الحضور
att_h5_setMapApiAddress=تعيين خريطة المعلمة
att_h5_MapSetWarning=سيؤدي تبديل الخريطة إلى فشل عنوان تسجيل الدخول إلى محطة الهاتف المحمول التي تم إدخالها في التقابل مع خطوط الطول والعرض ، يرجى التعديل بحذر!
att_h5_mapSelect=اختيار الخريطة
att_h5_persNoHire=لم ينضم الموظف بعد إلى الشركة في هذا الوقت.
att_slef_apiKey=API-KEY
att_self_apiJsKey=API-JS-KEY
att_self_noComputeAtt=لم يتم حساب حضور اليوم لحد الآن.
att_self_noSignRecord=لا سجل انتقاد في اليوم
att_self_imageUploadError=فشل تحميل الصورة
att_self_attSignAddressAreaIsExist=هناك بالفعل نقاط تسجيل الوصول في المنطقة
att_self_signRuleIsError=وقت التجديد الحالي ليس ضمن وقت التجديد المسموح به.
att_self_signAcrossDay=الجدول الزمني لليوم لا يمكن توقيعه!
att_self_todaySignIsExist=هناك بالفعل بقع المضافة اليوم!
att_self_signSetting=إلحاق الإعداد
att_self_allowSign=السماح بالتذييل:
att_self_allowSignSuffix=أيام ، سجل الحضور داخل
att_self_onlyThisMonth=فقط هذا الشهر
att_self_allowAcrossMonth=السماح عبر الشهر
att_self_thisTimeNoSch=لا يوجد تحول في الفترة الزمنية الحالية!
att_self_revokeReason=سبب الإلغاء:
att_self_revokeHint=الرجاء إدخال سبب الإلغاء خلال 20 كلمة للمراجعة
att_self_persSelfLogin=تسجيل دخول الخدمة الذاتية للموظفين
att_self_isOpenSelfLogin=ما إذا كان لبدء إدخال تسجيل دخول الخدمة الذاتية الموظف
att_self_applyAndWorkTimeOverlap=وقت التطبيق ووقت العمل تتداخل
att_apply_DurationIsZero=مدة التطبيق هي 0 ، لا يسمح لأي تطبيق
att_sign_mapWarn=أخفق تحميل الخريطة ، يرجى التحقق من اتصال الشبكة وتعيين قيمة المفتاح
att_admin_applyWarn=فشلت العملية ، فهناك أشخاص لم يتم جدولتهم أو أن وقت التطبيق ليس ضمن نطاق الجدول الزمني! ({0})
att_self_getPhotoFailed=الصورة غير موجودة
att_self_view=رأي
# 二维码
att_param_qrCodeUrl=رمز الاستجابة السريعة Url
att_param_qrCodeUrlHref=عنوان الخادم: منفذ
att_param_appAttQrCode=الحضور المحمول رمز الاستجابة السريعة
att_param_timingFrequency=الفاصل الزمني: 5-59 دقيقة أو 1-24 ساعة
att_sign_signTimeNotNull=لا يمكن أن يكون وقت سجل الإلحاق فارغًا
att_apply_overLastMonth=بدأت التطبيقات لفترة أطول من الشهر الماضي
att_apply_withoutDetail=لا تفاصيل العملية
att_flowable_noAuth=يرجى استخدام حساب المسؤول السوبر للعرض
att_apply_overtimeOverMaxTimeLong=ساعات العمل الإضافي أكبر من الحد الأقصى لساعات العمل الإضافي
# 考勤设置参数符号
att_other_arrive=√
att_other_late=L
att_other_early=E
att_other_absent=□
att_other_noSignIn=[
att_other_noSignOff=]
att_other_leave=∆
att_other_overtime=OT
att_other_off=○
att_other_classes=●
att_other_trip=T
att_other_out=G
att_other_incomplete=├
att_other_outcomplete=┤
# 服务器下发命令
att_devCmd_submitTime=أرسل الوقت
att_devCmd_returnedResult=نتيجة الإرجاع
att_devCmd_returnTime=وقت العودة
att_devCmd_content=محتوى الأمر
att_devCmd_clearCmd=مسح قائمة الأوامر
# 实时点名
att_realTime_selectDept=يرجى تحديد قسم
att_realTime_noSignPers=شخص غير مسجل
att_realTime_signPers=تم تسجيل الوصول
att_realTime_signMonitor=مراقبة تسجيل الدخول
att_realTime_signDateTime=وقت الوصول
att_realTime_realTimeSet=إعداد مكالمات في الوقت الحقيقي
att_realTime_openRealTime=تفعيل مكالمات الرد في الوقت الفعلي
att_realTime_rollCallEnd=ينتهي استدعاء الأسماء في الوقت الفعلي
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=مجدولة
att_personSch_cycleSch=جدولة الدورة
att_personSch_cleanCycleSch=جدول الدورات النظيفة
att_personSch_cleanTempSch=امسح الجدول الزمني المؤقت
att_personSch_personCycleSch=جدول دورة الشخص
att_personSch_deptCycleSch=جدول دورة القسم
att_personSch_groupCycleSch=جدولة دورة المجموعة
att_personSch_personTempSch=جدولة الموظفين المؤقتين
att_personSch_deptTempSch=الجدول الزمني المؤقت للقسم
att_personSch_groupTempSch=جدول مؤقت للمجموعة
att_personSch_checkGroupFirst=يرجى التحقق من المجموعة الموجودة على اليسار أو الأشخاص الموجودين في القائمة على اليمين للعمل!
att_personSch_sureDeleteGroup=هل أنت متأكد من حذف {0} والجدول المقابل للمجموعة؟
att_personSch_sch=الجدول الزمني
att_personSch_delSch=حذف الجدول
#考勤计算
att_statistical_sureAllCalculate=هل أنت متأكد من إجراء حساب الحضور لجميع الموظفين؟
#异常管理
att_exception_downTemplate=تنزيل واستيراد النموذج
att_exception_signImportTemplate=توقيع قالب الاستيراد
att_exception_leaveImportTemplate=ترك قالب الاستيراد
att_exception_overtimeImportTemplate=قالب استيراد الوقت الإضافي
att_exception_adjustImportTemplate=ضبط قالب الاستيراد
att_exception_cellDefault=حقل غير مطلوب
att_exception_cellRequired=حقل مطلوب
att_exception_cellDateTime=حقل مطلوب ، تنسيق الوقت هو yyyy-MM-dd HH: mm: ss ، مثل: 2020-07-07 08:30:00
att_exception_cellLeaveTypeName=حقل مطلوب ، مثل: "إجازة شخصية" ، "إجازة زواج" ، "إجازة أمومة" ، "إجازة مرضية" ، "إجازة سنوية" ، "إجازة وفاة" ، "إجازة عائلية" ، "إجازة رضاعة طبيعية" ، "رحلة عمل" ، "خروج" اسم متعجرف
att_exception_cellOvertimeSign=حقل مطلوب ، مثل: "العمل الإضافي العادي" ، "العمل الإضافي في أيام الراحة" ، "العمل الإضافي في أيام العطلات"
att_exception_cellAdjustType=حقل مطلوب ، مثل: "تحويل" ، "تكوين فئة"
att_exception_cellAdjustDate=حقل مطلوب ، تنسيق الوقت هو yyyy-MM-dd ، مثل: 2020-07-07
att_exception_cellShiftName=حقل مطلوب عندما يكون نوع التعديل هو التحول
att_exception_refuse=رفض
att_exception_end=نهاية غير طبيعية
att_exception_delete=حذف
att_exception_stop=وقفة
#时间段
att_timeSlot_normalTimeAdd=إضافة فترة زمنية عادية
att_timeSlot_elasticTimeAdd=أضف فترة زمنية مرنة
# 班次
att_shift_addRegularShift=إضافة وردية عادية
att_shift_addFlexibleShift=إضافة وردية مرنة
#参数设置
att_param_notLeaveSetting=إعداد حساب غير خاطئ
att_param_smallestUnit=الحد الأدنى للوحدة
att_param_workDay=يوم عمل
att_param_roundingControl=التحكم بالتقريب
att_param_abort=أسفل (تجاهل)
att_param_rounding=التقريب
att_param_carry=لأعلى (حمل)
att_param_reportSymbol=رمز عرض التقرير
att_param_convertCountValid=الرجاء إدخال رقم ولا يُسمح إلا بمكان عشري واحد
att_other_leaveThing=شيء
att_other_leaveMarriage=زواج
att_other_leaveBirth=المنتج
att_other_leaveSick=مريض
att_other_leaveAnnual=سنة
att_other_leaveFuneral=جنازة
att_other_leaveHome=استكشاف
att_other_leaveNursing=تمريض
att_other_leavetrip=فرق
att_other_leaveout=آخر
att_common_schAndRest=الجدول الزمني والراحة
att_common_timeLongs=طول الوقت
att_personSch_checkDeptOrPersFirst=يرجى مراجعة القسم الموجود على اليسار أو الشخص الموجود في القائمة على اليمين!
att_personSch_checkCalendarFirst=يرجى تحديد التاريخ الذي تريد جدولته أولاً!
att_personSch_cleanCheck=مسح الشيك
att_personSch_delTimeSlot=مسح الفترة الزمنية المحددة
att_personSch_repeatTimeSlotNoAdd=لن تتم إضافة فترة زمنية متكررة
att_personSch_showSchInfo=إظهار تفاصيل الجدول
att_personSch_sureToCycleSch=هل أنت متأكد من جدولة {0} بشكل دوري؟
att_personSch_sureToTempSch=هل أنت متأكد من جدولة {0} مؤقتًا؟
att_personSch_sureToCycleSchDeptOrGroup=هل أنت متأكد من جدولة جميع الموظفين تحت {0} بشكل دوري؟
att_personSch_sureToTempSchDeptOrGroup=هل أنت متأكد من جدولة جميع الأفراد مؤقتًا تحت {0}؟
att_personSch_sureCleanCycleSch=هل تريد بالتأكيد محو {0} من {1} إلى {2}؟
att_personSch_sureCleanTempSch=هل أنت متأكد من أنك تريد مسح {0} التحول المؤقت من {1} إلى {2}؟
att_personSch_sureCleanCycleSchDeptOrGroup=هل أنت متأكد من أنك تريد مسح الجدول الدوري من {1} إلى {2} لجميع الأشخاص الأقل من {0}؟
att_personSch_sureCleanTempSchDeptOrGroup=هل أنت متأكد من أنك تريد مسح الجدول المؤقت من {1} إلى {2} لجميع الأشخاص الأقل من {0}؟
att_personSch_today=اليوم
att_personSch_timeSoltName=اسم الفترة الزمنية
att_personSch_export=جدولة أفراد التصدير
att_personSch_exportTemplate=قالب النقل المؤقت لموظفي التصدير
att_personSch_import=جدولة مؤقتة لاستيراد الأفراد
att_personSch_tempSchTemplate=قالب جدولة الموظفين المؤقتين
att_personSch_tempSchTemplateTip=يرجى تحديد وقت البدء ووقت الانتهاء للجدولة ، قم بتنزيل قالب الجدول الزمني في ذلك التاريخ
att_personSch_opTip=تعليمات التشغيل
att_personSch_opTip1=1 ، يمكنك استخدام الماوس لسحب الفتحة الزمنية إلى تاريخ واحد في عنصر تحكم التقويم للجدولة.
att_personSch_opTip2=2. في عنصر تحكم التقويم ، انقر نقرًا مزدوجًا فوق تاريخ واحد للجدولة.
att_personSch_opTip3=3. في عنصر تحكم التقويم ، اضغط مع الاستمرار على الماوس لتحديد تواريخ متعددة للجدولة.
att_personSch_schRules=قواعد الجدول
att_personSch_schRules1=1 ، الجدولة الدورية: استبدال الجدولة السابقة بعد نفس التاريخ ، بغض النظر عما إذا لم يكن هناك تقاطع.
att_personSch_schRules2=2. الجدولة المؤقتة: هناك تقاطع في نفس التاريخ ، والجدولة المؤقتة السابقة سيتم استبدالها لاحقًا. إذا لم يكن هناك تقاطع ، فستكون موجودة في نفس الوقت.
att_personSch_schRules3=3. الفترة والمؤقت: إذا كان هناك تقاطع في نفس التاريخ ، فسيتم تغطية الفترة مؤقتًا ، وإذا لم يكن هناك تقاطع ، فستكون موجودة أيضًا في وقت واحد.
att_personSch_schStatus=حالة الجدول
# 左侧菜单-排班管理
att_leftMenu_schDetails=تفاصيل الجدول
att_leftMenu_detailReport=تقرير تفاصيل الحضور
att_leftMenu_signReport=جدول تفاصيل الاشتراك
att_leftMenu_leaveReport=نموذج تفاصيل المغادرة
att_leftMenu_abnormal=جدول حضور غير طبيعي
att_leftMenu_yearLeaveSumReport=تقرير الإجازة السنوية
att_leave_maxFileCount=يمكنك فقط إضافة 4 صور على الأكثر
#时间段
att_timeSlot_add=ضبط الفترة الزمنية
att_timeSlot_select=يرجى تحديد فترة زمنية!
att_timeSlot_repeat=الفترة الزمنية "{0}" تتكرر!
att_timeSlot_overlapping=تتداخل الفترة الزمنية "{0}" مع وقت التنقل "{1}"!
att_timeSlot_addFirst=يرجى تحديد الفترة الزمنية أولاً!
att_timeSlot_notEmpty=الفترة الزمنية المقابلة لرقم الموظفين {0} لا يمكن أن تكون فارغة!
att_timeSlot_notExist=الفترة الزمنية "{1}" المطابقة لرقم الموظفين {0} غير موجودة!
att_timeSlot_repeatEx=تتداخل الفترة الزمنية "{1}" المطابقة لرقم الموظفين {0} مع وقت التنقل "{2}"
att_timeSlot_importRepeat=يتم تكرار الفترة الزمنية "{1}" المطابقة لرقم الموظفين {0}
att_timeSlot_importNotPin=لا يوجد شخص برقم {0} في النظام!
att_timeSlot_elasticTimePeriod=عدد الموظفين{0}، لا يمكن استيراد فترة مرنة "{1}" !
#导入
att_import_overData=العدد الحالي للواردات هو {0} ، وهو يتجاوز الحد الأقصى البالغ 30،000 ، يرجى الاستيراد على دفعات!
att_import_existIllegalType={0} المستورد له نوع غير قانوني!
#验证方式
att_verifyMode_0=التعرف التلقائي
att_verifyMode_1=بصمة الإصبع فقط
att_verifyMode_2=التحقق من رقم الوظيفة
att_verifyMode_3=كلمة المرور فقط
att_verifyMode_4=البطاقة فقط
att_verifyMode_5=بصمة أو كلمة مرور
att_verifyMode_6=بصمة أو بطاقة
att_verifyMode_7=البطاقة أو كلمة المرور
att_verifyMode_8=رقم الوظيفة بالإضافة إلى بصمة الإصبع
att_verifyMode_9=بصمة الإصبع بالإضافة إلى كلمة المرور
att_verifyMode_10=بطاقة بالإضافة إلى بصمة الإصبع
att_verifyMode_11=بطاقة بالإضافة إلى كلمة المرور
att_verifyMode_12=بصمة الإصبع بالإضافة إلى كلمة المرور بالإضافة إلى البطاقة
att_verifyMode_13=معرف العمل بالإضافة إلى بصمة الإصبع بالإضافة إلى كلمة المرور
att_verifyMode_14=(رقم العمل بالإضافة إلى بصمة الإصبع) أو (البطاقة بالإضافة إلى بصمة الإصبع)
att_verifyMode_15=الوجه الإنساني
att_verifyMode_16=الوجه والبصمة
att_verifyMode_17=Face Plus كلمة المرور
att_verifyMode_18=بطاقة وجه زائد
att_verifyMode_19=الوجه بالإضافة إلى بطاقة بصمة بالإضافة إلى
att_verifyMode_20=الوجه بالإضافة إلى بصمة الإصبع بالإضافة إلى كلمة المرور
att_verifyMode_21=وريد الإصبع
att_verifyMode_22=إصبع الوريد بالإضافة إلى الرمز
att_verifyMode_23=الوريد الإصبع بالإضافة إلى البطاقة
att_verifyMode_24=إصبع الوريد بالإضافة إلى الرمز بالإضافة إلى البطاقة
att_verifyMode_25=طباعة الكف
att_verifyMode_26=بطاقة النخيل المطبوعة
att_verifyMode_27=طبعات النخيل والوجه
att_verifyMode_28=البصمة والبصمة
att_verifyMode_29=بصمة الكف بالإضافة إلى بصمة الإصبع بالإضافة إلى الوجه
# 工作流
att_flow_schedule=تقدم التدقيق
att_flow_schedulePass=(ناجح)
att_flow_scheduleNot=(غير معتمد)
att_flow_scheduleReject=(مرفوض)
# 工作时长表
att_workTimeReport_total=إجمالي ساعات العمل
# 自动导出报表
att_autoExport_startEndTime=وقت البدء والانتهاء
# 年假
att_annualLeave_setting=إعداد رصيد الإجازة السنوية
att_annualLeave_settingTip1=لاستخدام وظيفة رصيد الإجازة السنوية ، تحتاج إلى تعيين وقت الدخول لكل موظف ؛ عندما لا يتم تعيين وقت الدخول ، يتم عرض الإجازة السنوية المتبقية من جدول رصيد الإجازة السنوية للموظفين فارغة
att_annualLeave_settingTip2=إذا كان التاريخ الحالي أكبر من تاريخ إصدار المقاصة ، فسيكون هذا التعديل ساري المفعول في العام التالي ؛ إذا كان التاريخ الحالي أقل من تاريخ إصدار المقاصة ، عند الوصول إلى تاريخ إصدار المقاصة ، فسيتم مسحه وإعادة إصدار الإجازة السنوية
att_annualLeave_calculate=تاريخ المقاصة والإصدار للإجازة السنوية
att_annualLeave_workTimeCalculate=احسب وفقًا لنسبة وقت العمل
att_annualLeave_rule=قاعدة وقت الإجازة السنوية
att_annualLeave_ruleCountOver=تم الوصول إلى الحد الأقصى لعدد معين
att_annualLeave_years=سنوات التخرج
att_annualLeave_eachYear=كل عام
att_annualLeave_have=نعم
att_annualLeave_days=أيام الإجازة السنوية
att_annualLeave_totalDays=إجمالي الإجازة السنوية
att_annualLeave_remainingDays=الإجازة السنوية المتبقية
att_annualLeave_consecutive=يجب أن يكون إعداد قاعدة الإجازة السنوية سنوات متتالية
# 年假结余表
att_annualLeave_report=الميزانية العمومية للإجازة السنوية
att_annualLeave_validDate=تاريخ صالح
att_annualLeave_useDays=استخدم {0} يومًا
att_annualLeave_calculateDays=الإصدار {0} يوم
att_annualLeave_notEnough=إجازة سنوية غير كافية في {0}!
att_annualLeave_notValidDate={0} ليس ضمن النطاق الصحيح للإجازة السنوية!
att_annualLeave_notDays={0} ليس لديه إجازة سنوية!
att_annualLeave_tip1=انضم Zhang San في الأول من سبتمبر من العام الماضي
att_annualLeave_tip2=إعداد رصيد الإجازة السنوية
att_annualLeave_tip3=تاريخ المقاصة والإصدار هو 1 يناير من كل عام ؛ يتم حسابه وفقًا لنسبة العمل ؛ 3 أيام من الإجازة السنوية إذا كانت خبرة العمل أقل من أو تساوي 1 ، و 5 أيام من الإجازة السنوية إذا كانت سنة واحدة أقل من أو تساوي 3 سنوات
att_annualLeave_tip4=استمتع بالإجازة السنوية
att_annualLeave_tip5=العام الماضي 09-01 ~ 12-31 استمتع 4 / 12x3=1.0 يوم
att_annualLeave_tip6=هذا العام 01-01 ~ 12-31 استمتع بـ 4.0 أيام (هذا العام 01-01 ~ 08-31 استمتع بـ 8 / 12x3=2.0 يومًا   هذا العام 09-01 ~ 12-31 استمتع 4 / 12x5≈2.0 يوم)
# att SDC
att_sdc_name=معدات الفيديو
att_sdc_wxMsg_firstData=مرحبًا ، لديك إشعار تسجيل الوصول
att_sdc_wxMsg_stateData=لا معنى للحضور وتسجيل الوصول بنجاح
att_sdc_wxMsg_remark=تذكير: تخضع النتيجة النهائية للحضور لصفحة تفاصيل تسجيل الوصول.
# 时间段
att_timeSlot_conflict=تتعارض خانة الوقت مع فترات زمنية أخرى من اليوم
att_timeSlot_selectFirst=يرجى تحديد الفترة الزمنية
# 事件中心
att_eventCenter_sign=تسجيل الدخول الحضور
#异常管理
att_exception_classImportTemplate=قالب استيراد الفئة
att_exception_cellClassAdjustType=حقل مطلوب ، مثل: "{0}" ، "{1}" ، "{2}"
att_exception_swapDateDate=حقل غير مطلوب ، تنسيق الوقت هو yyyy-MM-dd
#消息中心
att_message_leave=إشعار الحضور {0}
att_message_leaveContent={0} تم الإرسال {1} ، {2} الوقت {3} ~ {4}
att_message_leaveTime=وقت المغادرة
att_message_overtime=إشعار الحضور والعمل الإضافي
att_message_overtimeContent={0} تم إرسال العمل الإضافي ، ووقت العمل الإضافي هو {1} ~ {2}
att_message_overtimeTime=الوقت الإضافي
att_message_sign=علامة إشعار الحضور
att_message_signContent={0} أرسل علامة تكميلية ، ووقت التوقيع الإضافي هو {1}
att_message_adjust=إشعار بتعديل الحضور
att_message_adjustContent={0} أرسل تعديلًا ، وتاريخ التعديل هو {1}
att_message_class=إخطار الحضور والتحول
att_message_classContent=محتوى الفصل الدراسي
att_message_classContent0={0} تم إرسال وردية ، وتاريخ التحول هو {1} ، والتحول هو {2}
att_message_classContent1={0} تم إرسال وردية ، وتاريخ التحول هو {1} ، وتاريخ التحول هو {2}
att_message_classContent2={0} ({1}) و {2} ({3}) تبديل الفئات
#推送中心
att_pushCenter_transaction=سجل الحضور
# 时间段
att_timeSlot_workTimeNotEqual=لا يمكن أن يكون وقت العمل مساويًا للتوقف عن العمل
att_timeSlot_signTimeNotEqual=لا يمكن أن يكون وقت بدء تسجيل الدخول مساويًا لوقت تسجيل الخروج النهائي
# 北向接口A
att_api_notNull=لا يمكن ترك حقل {0} فارغًا!
att_api_startDateGeEndDate=لا يمكن أن يكون وقت البدء أكبر من أو يساوي وقت الانتهاء!
att_api_leaveTypeNotExist=الأنواع المزيفة غير موجودة!
att_api_imageLengthNot2000=طول عنوان الصورة لا يمكن أن يتجاوز 2000!
# 20221230新增国际化
att_personSch_workTypeNotNull=نوع العمل الذي يتوافق مع عدد الموظفين { 0 } لا يمكن أن تكون فارغة !
att_personSch_workTypeNotExist=نوع العمل الذي يتوافق مع عدد الموظفين { 0 } لا يمكن أن تكون فارغة !
att_annualLeave_recalculate=إعادة حساب
# 20230530新增国际化
att_leftMenu_dailyReport=تقرير الحضور اليومي
att_leftMenu_overtimeReport=تقرير العمل الإضافي
att_leftMenu_lateReport=تقرير متأخر
att_leftMenu_earlyReport=اترك التقرير المبكر
att_leftMenu_absentReport=تقرير غائب
att_leftMenu_monthReport=تقرير الحضور الشهري
att_leftMenu_monthWorkTimeReport=تقرير وقت العمل الشهري
att_leftMenu_monthCardReport=تقرير البطاقة الشهري
att_leftMenu_monthOvertimeReport=تقرير العمل الإضافي الشهري
att_leftMenu_overtimeSummaryReport=تقرير ملخص العمل الإضافي للموظفين
att_leftMenu_deptOvertimeSummaryReport=تقرير ملخص العمل الإضافي للقسم
att_leftMenu_deptLeaveSummaryReport=تقرير ملخص إجازة القسم
att_annualLeave_calculateDay=عدد أيام الإجازة السنوية
att_annualLeave_adjustDay=ضبط الأيام
att_annualLeave_sureSelectDept=هل أنت متأكد من أنك تريد إجراء عملية {0} على القسم المحدد؟
att_annualLeave_sureSelectPerson=هل أنت متأكد من أنك تريد إجراء عملية {0} على الشخص المحدد؟
att_annualLeave_calculateTip1=عند الحساب وفقًا لطول الخدمة: حساب الإجازة السنوية دقيق للشهر ، إذا كانت مدة الخدمة 10 سنوات و 3 أشهر ، فسيتم استخدام 10 سنوات و 3 أشهر للحساب ؛
att_annualLeave_calculateTip2=عندما لا يعتمد التحويل على طول الخدمة: حساب الإجازة السنوية يكون دقيقًا للسنة ، إذا كانت مدة الخدمة 10 سنوات و 3 أشهر ، فسيتم استخدام 10 سنوات للحساب ؛
att_rule_isInCompleteTip=الأولوية هي الأعلى عندما لا يتم تسجيل الدخول أو عدم تسجيل الخروج على أنه غير مكتمل ، والتأخير ، والإجازة المبكرة ، والتغيب ، وصلاحية
att_rule_absentTip=عند عدم تسجيل الدخول أو عدم تسجيل الخروج على أنه حالة تغيب ، فإن طول التغيب يساوي طول ساعات العمل مطروحًا منه طول الإجازة المتأخرة إلى الإجازة المبكرة
att_timeSlot_elasticTip1=0 ، الوقت الفعلي يساوي الوقت الفعلي ، لا يوجد غياب
att_timeSlot_elasticTip2=إذا كان الوقت الفعلي أطول من وقت العمل ، فإن الوقت الفعلي يساوي وقت العمل ، ولا يوجد غياب
att_timeSlot_elasticTip3=إذا كانت المدة الفعلية أقل من مدة العمل ، فإن المدة الفعلية تساوي المدة الفعلية ، والغياب يساوي مدة العمل مطروحًا منها المدة الفعلية
att_timeSlot_maxWorkingHours=لا يمكن أن تكون ساعات العمل أكبر من
# 20231030
att_customReport=تقرير مخصص الحضور
att_customReport_byDayDetail=التفاصيل اليومية
att_customReport_byPerson=ملخص الموظفين
att_customReport_byDept=ملخص حسب الإدارة
att_customReport_queryMaxRange=أقصى فترة الاستعلام لا تتجاوز أربعة أشهر
# 20240630
att_personSch_shiftWorkTypeTip1=1 ، نوع العمل هو العمل العادي / يوم راحة العمل الإضافي ، جدولة الأولوية أقل من عطلة
att_personSch_shiftWorkTypeTip2=2 ، نوع العمل هو عطلة العمل الإضافي ، جدولة أولوية أعلى من عطلة
att_personVerifyMode=طريقة التحقق من الموظفين
att_personVerifyMode_setting=وضع التحقق
att_personSch_importCycSch=مقدمة دورة الموظفين جدولة
att_personSch_cycSchTemplate=دورة الموظفين جدولة قالب
att_personSch_exportCycSchTemplate=تحميل قوالب جدولة دورة الموظفين
att_personSch_scheduleTypeNotNull=نوع التحول لا يمكن أن تكون فارغة أو غير موجودة !
att_personSch_shiftNotNull=التحول لا يمكن أن تكون فارغة !
att_personSch_shiftNotExist=التحول لا وجود لها !
att_personSch_onlyAllowOneShift=الجدول العادي يسمح فقط جدول واحد !
att_shift_attShiftStartDateRemark2=الأسبوع الذي يبدأ تاريخ الدورة هو الأسبوع الأول .الشهر الذي تبدأ الدورة في الشهر الأول
#打卡状态
att_cardStatus_setting=إعدادت حالة المواقف التسجيلية
att_cardStatus_name=اسم
att_cardStatus_value=قيمة
att_cardStatus_alias=بنوء آخر
att_cardStatus_every_day=كل يوم
att_cardStatus_by_week=باستخدام الأسبوع
att_cardStatus_autoState=حالة تلقائية
att_cardStatus_attState=حالة المواقف التسجيلية
att_cardStatus_signIn=تسجيل دخول
att_cardStatus_signOut=تسجيل خروج
att_cardStatus_out=خارج
att_cardStatus_outReturn=عودة من الخارج
att_cardStatus_overtime_signIn=تسجيل دخول الصبح
att_cardStatus_overtime_signOut=تسجيل خروج الصب
# 20241030新增国际化
att_leaveType_enableMaxDays=تمكين الحد السنوي
att_leaveType_maxDays=الحد السنوي (أيام)
att_leaveType_applyMaxDays=لا يمكن طلب الإجازة في هذا العام أكثر من {0} أيام
att_param_overTimeSetting=إعداد مستوى العمل الإضافي
att_param_overTimeLevel=مستوى العمل الإضافي (ساعات)
att_param_overTimeLevelEnable=هل تمكن من حساب مستوى العمل الإضافي
att_param_reportColor=لون التقرير
# APP
att_app_signClientTip=هذا الجهاز تم تسجيل الدخول بواسطة شخص آخر اليوم
att_app_noSignAddress=لم يتم تعيين نطاق التحقق من البصمة، يرجى الاتصال بمدير النظام لضبط الإعدادات
att_app_notInSignAddress=لم تتم الوصول إلى موقع التحقق من البصمة، لا يمكن التحقق
att_app_attendance=حضوري
att_app_apply=طلب تسجيل حضور
att_app_approve=قائمة التصديق الخاصة بي
# 20250530
att_node_leaderNodeExist=يوجد بالفعل خطوة مصادقة من قبل القائد المباشر
att_signAddress_init=تمهيد الخريطة
att_signAddress_initTips=يرجى إدخال مفتاح الخريطة وبدء عملية التهيئة لاختيار العنوان