#系统名称 英语
att_systemName=Attendance System 1.0
#=====================================================================
#左侧菜单
att_module=Attendance
#左侧菜单-考勤设备
att_leftMenu_attendanceDevice=Attendance Management
att_leftMenu_device=Attendance Device
att_leftMenu_point=Attendance Point
att_leftMenu_sign_address=Mobile Check-in Range
att_leftMenu_adms_devCmd=Device Command
#左侧菜单-基础信息
att_leftMenu_basicInformation=Basic Information
att_leftMenu_rule=Rule
att_leftMenu_base_rule=Attendance Rules
att_leftMenu_department_rule=Departmental Rules
att_leftMenu_holiday=Holiday
att_leftMenu_leaveType=Leave Type
att_leftMenu_timingCalculation=Timed Calculation
att_leftMenu_autoExport=Automatic Report
att_leftMenu_param=Parameter Setting
#左侧菜单-班次管理
att_leftMenu_shiftManagement=Shift
att_leftMenu_timeSlot=Timetable
att_leftMenu_shift=Shift
#左侧菜单-排班管理
att_leftMenu_scheduleManagement=Schedule
att_leftMenu_group=Group
att_leftMenu_groupPerson=Personnel Group 
att_leftMenu_groupSch=Group Schedule
att_leftMenu_deptSch=Department Schedule
att_leftMenu_personSch=Personnel Schedule
att_leftMenu_tempSch=Temporary Schedule
att_leftMenu_nonSch=Unscheduled Personnel
#左侧菜单-异常管理
att_leftMenu_exceptionManagement=Exception
att_leftMenu_sign=Manual Check - in
att_leftMenu_leave=Leave
att_leftMenu_trip=Business Trip
att_leftMenu_out=Outing
att_leftMenu_overtime=Overtime
att_leftMenu_adjust=Leave Adjustment
att_leftMenu_class=Shift Adjustment
#左侧菜单-统计报表
att_leftMenu_statisticalReport=Calculate Report
att_leftMenu_manualCalculation=Manual Calculate
att_leftMenu_transaction=Transactions
att_leftMenu_dayCardDetailReport=Daily Attendance
att_leftMenu_leaveSummaryReport=Leave Summary
att_leftMenu_dayDetailReport=Daily Report
att_leftMenu_monthDetailReport=Monthly Detail Report
att_leftMenu_monthStatisticalReport=Monthly Staff Report
att_leftMenu_deptStatisticalReport=Monthly Departmental Report
att_leftMenu_yearStatisticalReport=Annual Report(By Person)
att_leftMenu_attSignCallRollReport=Roll Call Report
att_leftMenu_workTimeReport=Work Time Report
#左侧菜单-设备操作日志
att_leftMenu_device_op_log=Device Operation Log
#左侧菜单-实时点名
att_leftMenu_realTime_callRoll=Roll Call
#=====================================================================
#公共
att_common_person=Personnel
att_common_pin=ID
att_common_group=Group
att_common_dept=Department
att_common_symbol=Symbol
att_common_deptNo=Department ID
att_common_deptName=Department Name
att_common_groupNo=Group ID
att_common_groupName=Group Name
att_common_operateTime=Operation Time
att_common_operationFailed=Operation Failed
att_common_id=ID
att_common_deptId=Department ID
att_common_groupId=Group ID
att_common_deviceId=Device ID
att_person_pin=Personnel ID
att_person_name=First Name
att_person_lastName=Last Name
att_person_internalCard=Card Number
att_person_attendanceMode=Attendance Mode
att_person_normalAttendance=Normal Attendance
att_person_noPunchCard=No Punch Required
att_common_attendance=Attendance
att_common_attendance_hour=Attendance (Hours)
att_common_attendance_day=Attendance (Days)
att_common_late=Late
att_common_early=Early
att_common_overtime=Overtime
att_common_exception=Exception
att_common_absent=Absent
att_common_leave=Leave
att_common_trip=Business Trip
att_common_out=Outing
att_common_staff=Employee
att_common_superadmin=Superuser
att_common_msg=SMS Content
att_common_min=SMS Duration (Minutes)
att_common_letterNumber=Only enter numbers or letters.
att_common_relationDataCanNotDel=The associated data can not be deleted.
att_common_relationDataCanNotEdit=The associated data can not be modified.
att_common_needSelectOneArea=Please select an area.
att_common_neesSelectPerson=Please select a person.
att_common_nameNoSpace=The name cannot contain spaces!
att_common_digitsValid=Only enter numbers with no more than two decimal places.
att_common_numValid=Only enter numbers!
#=====================================================================
#工作面板
att_dashboard_worker=Workaholic
att_dashboard_today=Today's Attendance
att_dashboard_todayCount=Today's Attendance Segmented Statistics
att_dashboard_exceptionCount=Exception Summary (this month)
att_dashboard_lastWeek=Last Week
att_dashboard_lastMonth=Last Month
att_dashboard_perpsonNumber=Total Personnel
att_dashboard_actualNumber=Actual Personnel
att_dashboard_notArrivedNumber=Absent Personnel
att_dashboard_attHour=Work Time
#区域
#设备
att_op_syncDev=Synchronize Software Data to Device
att_op_account=Attendance Data Checking
att_op_check=Re-Upload Data
att_op_deleteCmd=Clear unexecuted device commands
att_op_dataSms=Public Message
att_op_clearAttPic=Clear the attendance photos
att_op_clearAttLog=Clear the attendance transactions
att_device_waitCmdCount=Commands to be Executed
att_device_status=Enable State
att_device_register=Entrollment Device
att_device_isRegister=Entrollment Device
att_device_existNotRegDevice=Data cannot be obtained from Non-Entrollment Device!
att_device_fwVersion=Firmware Version
att_device_transInterval=Refresh Duration(Minutes)
att_device_cmdCount=The maximum number of commands to communicate with the server.
att_device_delay=Inquiry record time (Seconds)
att_device_timeZone=Time Zone
att_device_operationLog=Operation Logs
att_device_registeredFingerprint=Enroll Fingerprint
att_device_registeredUser=Enroll Personnel
att_device_fingerprintImage=Fingerprint Image
att_device_editUser=Edit Personnel
att_device_modifyFingerprint=Modify Fingerprint
att_device_faceRegistration=Facial Enrollment
att_device_userPhotos=Personnel Photo
att_device_attLog=Whether to upload attendance records
att_device_operLog=Whether to upload Personnel information
att_device_attPhoto=Whether to upload attendance photos
att_device_isOnLine=Online Status
att_device_InputPin=Enter person number
att_device_getPin=Get the specified personnel data
att_device_separatedPin=Multiple personnel numbers, separated by commas
att_device_authDevice=Authorized Device
att_device_disabled=The following devices are disabled and cannot be operated!
att_device_autoAdd=New equipment is added automatically
att_device_receivePersonOnlyDb=Only receive personnel data that exists in the database!
att_devMenu_control=Device Control
att_devMenu_viewOrGetInfo=View and Get Information
att_devMenu_clearData=Clear Device Data
att_device_disabledOrOffline=The device is not enabled or offline,cannot be operated!
att_device_areaStatus=Device Area Status
att_device_areaCommon=The area is normal
att_device_areaEmpty=The area is empty
att_device_isRegDev=Time zone or registration machine status modification will take effect after restarting the device! 
att_device_canUpgrade=The following devices can be upgraded
att_device_offline=The following devices are offline and cannot be operated!
att_device_oldProtocol=Old Protocol
att_device_newProtocol=New Protocol
att_device_noMoreTwenty=The firmware upgrade package of the old protocol device cannot exceed 20M.
att_device_transferFilesTip=Firmware detected successfully, transfering files
att_op_clearAttPers=Clear equipment personnel
#区域人员
att_op_forZoneAddPers=Regional Personnel
att_op_dataUserSms=Private Message
att_op_syncPers=Re-synchronize to Device
att_areaPerson_choiceArea=Please select the area!
att_areaPerson_byAreaPerson=By Area
att_areaPerson_setByAreaPerson=Set By Area Personnel
att_areaPerson_importBatchDel=Import Bulk Delete
att_areaPerson_syncToDevSuccess=Operation succeed! Please wait for the command to be sent.
att_areaPerson_personId=Personnel ID
att_areaPerson_areaId=Area ID
att_area_existPerson=There are people in the area!
att_areaPerson_notice1=The area or personnel cannot be empty at the same time!
att_areaPerson_notice2=No people or areas have been found!
att_areaPerson_notice3=No devices found in the area!
att_areaPerson_addArea=Add Area
att_areaPerson_delArea=Delete Area
att_areaPerson_persNoExit=Person does not exist
att_areaPerson_importTip1=Please make sure that the imported person already exists in the personnel module
att_areaPerson_importTip2=Batch imported person will not be automatically delivered to the device and need to be manually synchronized
att_areaPerson_addAreaPerson=Add Area Personnel
att_areaPerson_delAreaPerson=Delete Area Personnel
att_areaPerson_importDelAreaPerson=Import and Delete Area Personnel
att_areaPerson_importAreaPerson=Import Area Personnel
#考勤点
att_attPoint_name=Attendance Point Name
att_attPoint_list=Attendance Point List
att_attPoint_deviceModule=Device Module
att_attPoint_acc=Access Control
att_attPoint_park=Parking
att_attPoint_ins=FaceKiosk
att_attPoint_pid=Personal Certificate
att_attPoint_vms=Video Surveillance
att_attPoint_psg=Entrance Control
att_attPoint_doorList=Door List
att_attPoint_deviceList=Device List
att_attPoint_channelList=Channel List
att_attPoint_gateList=Gate List
att_attPoint_recordTypeList=Pull Record Type
att_attPoint_door=Please select the corresponding door.
att_attPoint_device=Please select the corresponding device.
att_attPoint_gate=Please select the corresponding gate.
att_attPoint_normalPassRecord=Normal Pass Record
att_attPoint_verificationRecord=Verification Record
att_person_attSet=Attendance Setting
att_attPoint_point=Please select the attendance point.
att_attPoint_count=Insufficient attendance points license; operation failed!
att_attPoint_notSelect=The module is not configured
att_attPoint_accInsufficientPoints=Insufficient attendance points license for access control records!
att_attPoint_parkInsufficientPoints=Insufficient attendance points license for car park records!
att_attPoint_insInsufficientPoints=Insufficient attendance points license for Facekiosk！
att_attPoint_pidInsufficientPoints=Insufficient attendance points license for personnel certificate!
att_attPoint_doorOrParkDeviceName=Door Name or Parking Device name
att_attPoint_vmsInsufficientPoints=Insufficient attendance points license for video!
att_attPoint_psgInsufficientPoints=Insufficient attendance points license for the channel!
att_attPoint_delDevFail=Delete the device failed, the device has been used as attendance!
att_attPoint_pullingRecord=The attendance point is getting records regularly, please wait!
att_attPoint_lastTransactionTime=Last Data Pull Time
att_attPoint_masterDevice=Master Device
att_attPoint_channelName=Channel Name
att_attPoint_cameraName=Camera Name
att_attPoint_cameraIP=Camera IP
att_attPoint_channelIP=Channel IP
att_attPoint_gateNumber=Gate Number
att_attPoint_gateName=Gate Name
#APP考勤签到地址
att_signAddress_address=Address
att_signAddress_longitude=Longitude
att_signAddress_latitude=Latitude
att_signAddress_range=Effective Range
att_signAddress_rangeUnit=The unit is meters(m)
#=====================================================================
#规则
att_rule_baseRuleSet=Basic Rule Setting
att_rule_countConvertSet=Calculation Setting
att_rule_otherSet=Other Setting
att_rule_baseRuleSignIn=Check-In Rule
att_rule_baseRuleSignOut=Check-Out Rule
att_rule_earliestPrinciple=The Earliest Rule
att_rule_theLatestPrinciple=The Latest Rule
att_rule_principleOfProximity=The Proximity Rule
att_rule_baseRuleShortestMinutes=The minimum time period should be greater than (minimum 10 minutes)
att_rule_baseRuleLongestMinutes=The maximum time period should be less than (maximum 1,440 minutes)
att_rule_baseRuleLateAndEarly=Late and Early Leave Counted as Absent
att_rule_baseRuleCountOvertime=Overtime Statistics
att_rule_baseRuleFindSchSort=Search Shift Record
att_rule_groupGreaterThanDepartment=Group->Department
att_rule_departmentGreaterThanGroup=Department->Group
att_rule_baseRuleSmartFindClass=Intelligent Matching Shift Rule
att_rule_timeLongest=Longest Working Duration
att_rule_exceptionLeast=Least Abnormal
att_rule_baseRuleCrossDay=Attendance calculation result for cross-day shift
att_rule_firstDay=First Day
att_rule_secondDay=Second Day
att_rule_baseRuleShortestOvertimeMinutes=Shortest Overtime (Minutes)
att_rule_baseRuleMaxOvertimeMinutes=Maximum Overtime (Minutes)
att_rule_baseRuleElasticCal=Flexible Duration Calculation
att_rule_baseRuleTwoPunch=Cumulative time for every two punches
att_rule_baseRuleStartEnd=Calculation of the first and the last punch time
att_rule_countConvertHour=Hour Conversion Rule
att_rule_formulaHour=Formula: Hours = Minutes / 60
att_rule_countConvertDay=Days Conversion Rule
att_rule_formulaDay=Formula：Days = Minutes / Number of minutes to work per day
att_rule_inFormulaShallPrevail=Take the result calculated by the formula as the standard;
att_rule_remainderHour=The remainder is greater than or equal to
att_rule_oneHour=Recorded as one hour;
att_rule_halfAnHour=Calculated as half an hour, otherwise ignored;
att_rule_remainderDay=Quotient is greater than or equal to the work minutes
att_rule_oneDay=%,calculated as one day;
att_rule_halfAnDay=%,calculated as half-day, otherwise ignored;
att_rule_countConvertAbsentDay=Day conversion benchmark
att_rule_markWorkingDays=Calculated as the work days
att_rule_countConvertDecimal=Exact digits of the decimal point
att_rule_otherSymbol=The attendance result symbol setting in the report
att_rule_arrive=Expected/Actual
att_rule_noSignIn=No Check-In
att_rule_noSignOff=No Check-Out
att_rule_off=Adjust Rest
att_rule_class=Append Attendance
att_rule_shortLessLong=The attendance timetable cannot be longer than the longest attendance timetable duration.
att_rule_symbolsWarning=You must configure the symbol in the attendance report!
att_rule_reportSettingSet=Report Export Settings
att_rule_shortDateFormat=Date Format
att_rule_shortTimeFormat=Time Format
att_rule_baseRuleSignBreakTime=Whether to clock in during the break
att_leftMenu_custom_rule=Custom Rule
att_custom_rule_already_exist={0} Custom rules already exist!
att_add_group_custom_rule=Add Grouping Rules
att_custom_rule_type=Rule Type
att_rule_type_group=Grouping Rules
att_rule_type_dept=Department Rules
att_custom_rule_orgNames=Using the object
att_rult_maxOverTimeType1=No Limit
att_rult_maxOverTimeType2=This Week
att_rult_maxOverTimeType3=This Month
att_rule_countConvertDayRemark1=Example:The effective working time is 500 minutes, and the working time is 480 minutes per day. The result is 500/480=1.04, and the last decimal is kept at 1.0
att_rule_countConvertDayRemark2=Example:The effective working time is 500 minutes, and the working time is 480 minutes per day. The result is 500/480=1.04, 1.04>0.8.
att_rule_countConvertDayRemark3=Example:The effective working time is 300 minutes, and the working time is 480 minutes per day. The result is 300/480=0.625, 0.2<0.625<0.8 for half a day.
att_rule_countConvertDayRemark4=Day conversion benchmark: Record as working days in the time period does not work;
att_rule_countConvertDayRemark5=It is recorded as the number of working days: it is limited to the calculation of the number of days of completion, and as long as there is completion in each period, the length of completion is calculated according to the number of working days of the period;
att_rule_baseRuleSmartFindRemark1=The longest time:According to the card point of the day, calculate the working hours corresponding to each shift on the day, and find the shift of the longest working time of the day;
att_rule_baseRuleSmartFindRemark2=Abnormal minimum:Calculate the number of abnormal times corresponding to each shift on the day according to the card point of the day, and find the shift with the least number of abnormalities on the day to calculate the working time;
att_rule_baseRuleHourValidator=The half-hour judgment minute cannot be greater than or equal to 1 hour!
att_rule_baseRuleDayValidator=The judgment period of half a day cannot be greater than or equal to the judgment period of one day!
att_rule_overtimeWarning=The maximum overtime duration cannot be less than the minimum single minimum overtime duration!
att_rule_noSignInCountType=Missing Check-In count as
att_rule_absent=Absent
att_rule_earlyLeave=Early Leave
att_rule_noSignOffCountType=Missing Check-Out count as
att_rule_minutes=Minutes
att_rule_noSignInCountLateMinute=Missing Check-In count as minutes late
att_rule_noSignOffCountEarlyMinute=Missing Check-Out count as minutes left early
att_rule_incomplete=Incomplete
att_rule_noCheckInIncomplete=Incomplete and No Check-in
att_rule_noCheckOutIncomplete=Incomplete and No Check-out
att_rule_lateMinuteWarning=The late minutes when missing Check-In should be greater than 0 and less than the longest attendance period
att_rule_earlyMinuteWarning=The early minutes when missing Check-Out should be greater than 0 and less than the longest attendance period
att_rule_baseRuleNoSignInCountLateMinuteRemark=When missing Check-In is counted as late, if no Check-In, it will be counted as being late for N minutes
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=When missing Check-Out is counted as early leave, if no Check-Out, it will be counted as leaving early for N minutes
#节假日
att_holiday_placeholderNo=eg. H01.
att_holiday_placeholderName=eg. [2017 Labour Day].
att_holiday_dayNumber=Number of Days
att_holiday_validDate_msg=Holidays during this time
#假种
att_leaveType_leaveThing=Personal Leave
att_leaveType_leaveMarriage=Marriage Leave
att_leaveType_leaveBirth=Maternity Leave
att_leaveType_leaveSick=Sick Leave
att_leaveType_leaveAnnual=Annual Leave
att_leaveType_leaveFuneral=Bereavement Leave
att_leaveType_leaveHome=Home Leave
att_leaveType_leaveNursing=Breastfeeding Leave
att_leaveType_isDeductWorkLong=Deduct working hours
att_leaveType_placeholderNo=eg. L1.
att_leaveType_placeholderName=eg. Marriage Holiday.
#定时计算
att_timingcalc_timeCalcFrequency=Calculation Interval
att_timingcalc_timeCalcInterval=Timed Calculation Time
att_timingcalc_timeSet=Timed Calculation Time Setting
att_timingcalc_timeSelect=Please Choose Time
att_timingcalc_optionTip=At least one valid daily scheduled attendance calculation must be retained
#自动导出报表
att_autoExport_reportType=Report Type
att_autoExport_fileType=File Type
att_autoExport_fileName=File Name
att_autoExport_fileDateFormat=Date Format
att_autoExport_fileTimeFormat=Time Format
att_autoExport_fileContentFormat=Content Format
att_autoExport_fileContentFormatTxt=Example：{deptName}00{personPin}01{personName}02{attDatetime}03
att_autoExport_timeSendFrequency=Send Frequency
att_autoExport_timeSendInterval=Time Send Interval
att_autoExport_emailType=Mail Receiver Type
att_autoExport_emailRecipients=Mail Receiver
att_autoExport_emailAddress=Mail Address
att_autoExport_emailExample=Example:<EMAIL>,<EMAIL>
att_autoExport_emailSubject=Mail Title
att_autoExport_emailContent=Mail Body
att_autoExport_field=Field
att_autoExport_fieldName=Field Name
att_autoExport_fieldCode=Field Number
att_autoExport_reportSet=Report Setting
att_autoExport_timeSet=Mail Delivery Time Setting
att_autoExport_emailSet=Mail Setting
att_autoExport_emailSetAlert=Please enter your email address.
att_autoExport_emailTypeSet=Receiver Setting
att_autoExport_byDay=By Day
att_autoExport_byMonth=By Month
att_autoExport_byPersonSet=Set by Personnel
att_autoExport_byDeptSet=Set by Department
att_autoExport_byAreaSet=Set by Area
att_autoExport_emailSubjectSet=Title Setting
att_autoExport_emailContentSet=Body Setting
att_autoExport_timePointAlert=Please select the correct send time point.
att_autoExport_lastDayofMonth=Last day of the month
att_autoExport_firstDayofMonth=First day of the month
att_autoExport_dayofMonthCheck=Specific Date
att_autoExport_dayofMonthCheckAlert=Please select the specific date.
att_autoExport_chooseDeptAlert=Please select the Department!
att_autoExport_sendFormatSet=Send Mode Setting
att_autoExport_sendFormat=Send Mode
att_autoExport_mailFormat=Mailbox Delivery Method
att_autoExport_ftpFormat=FTP Send Method
att_autoExport_sftpFormat=SFTP Send Method
att_autoExport_ftpUrl=FTP Server Address
att_autoExport_ftpPort=FTP Server Port
att_autoExport_ftpTimeSet=FTP Send Time Setting
att_autoExport_ftpParamSet=FTP Parameter Setting
att_autoExport_ftpUsername=FTP Username
att_autoExport_ftpPassword=FTP Password
att_autoExport_correctFtpParam=Please fill in the FTP parameters correctly
att_autoExport_correctFtpTestParam=Please test the connection to ensure that the communication is normal
att_autoExport_inputFtpUrl=Please enter the server address
att_autoExport_inputFtpPort=Please enter the server port
att_autoExport_ftpSuccess=Connection succeeded
att_autoExport_ftpFail=Please check if the parameter settings are incorrect.
att_autoExport_validFtp=Please enter a valid server address
att_autoExport_validPort=Please enter a valid server port
att_autoExport_selectExcelTip=Select EXCEL as the file type, and the content format is all fields!
#=====================================================================
#时间段
att_timeSlot_periodType=Timetable Type
att_timeSlot_normalTime=Normal Timetable
att_timeSlot_elasticTime=Flexible Timetable
att_timeSlot_startSignInTime=Check-In Start Time
att_timeSlot_toWorkTime=Check-In Time
att_timeSlot_endSignInTime=Check-In End Time
att_timeSlot_allowLateMinutes=Allow Late(Minutes)
att_timeSlot_isMustSignIn=Must Check-In
att_timeSlot_startSignOffTime=Check-Out Start Time
att_timeSlot_offWorkTime=Check-Out Time
att_timeSlot_endSignOffTime=Check-Out End Time
att_timeSlot_allowEarlyMinutes=Allow Early Leave(Minutes)
att_timeSlot_isMustSignOff=Must Check-Out
att_timeSlot_workingHours=Work Time (Minutes)
att_timeSlot_isSegmentDeduction=Auto Deduct Break Time
att_timeSlot_startSegmentTime=Start Time
att_timeSlot_endSegmentTime=End Time
att_timeSlot_interSegmentDeduction=Deducted Time(Minutes)
att_timeSlot_markWorkingDays=Workday
att_timeSlot_isAdvanceCountOvertime=Auto OT(Check-In Early)
att_timeSlot_signInAdvanceTime=Auto OT End Time(Check-In)
att_timeSlot_isPostponeCountOvertime=Auto OT(Check-Out Delay)
att_timeSlot_signOutPosponeTime=Auto OT Start Time(Check-Out)
att_timeSlot_isCountOvertime=Calculated as Overtime
att_timeSlot_timeSlotLong=The working hours must meet the attendance interval defined by the rules:
att_timeSlot_alertStartSignInTime=The Check-In Start time must be less than Check-In time.
att_timeSlot_alertEndSignInTime=The Check-In End time must be greater than Check-In time.
att_timeSlot_alertStartSignInAndEndSignIn=The Check-Out Start time must be less than Check-Out time.
att_timeSlot_alertStartSignOffTime=The overtime start  time cannot be less than check-out time.
att_timeSlot_alertEndSignOffTime=The overtime start  time cannot be larger than end check-out time.
att_timeSlot_alertStartUnequalEnd=The working days can not be smaller than 0.
att_timeSlot_alertStartSegmentTime=The deducted time cannot be smaller than 0.
att_timeSlot_alertStartAndEndTime=Start checkout time cannot equal end check-in time.
att_timeSlot_alertEndAndoffWorkTime=The hours cannot be greater than 23.
att_timeSlot_alertToWorkLesserAndEndTimeLesser=The minutes cannot be greater than 59.
att_timeSlot_alertLessSignInAdvanceTime=Early Check-In time should be less than work start time.
att_timeSlot_alertMoreSignInAdvanceTime=The number of minutes of overtime before 'going to work' is less than the number of minutes before 'working'.
att_timeSlot_alertMoreSignOutPosponeTime='off work' postscript overtime minutes is less than 'after work' minutes.
att_timeSlot_alertLessSignOutPosponeTime=Late Check-Out time should be greater than work end time.
att_timeSlot_time=Please enter the correct time format.
att_timeSlot_alertMarkWorkingDays=The working day can not be empty!
att_timeSlot_placeholderNo=It is recommended to start with T, such as T01.
att_timeSlot_placeholderName=It is recommended to start with T or end with timetable.
att_timeSlot_beforeToWork=Before going to Work
att_timeSlot_afterToWork=After Work
att_timeSlot_beforeOffWork=Before Going Off Duty
att_timeSlot_afterOffWork=After Work
att_timeSlot_minutesSignInValid=Check-In is valid within minutes
att_timeSlot_toWork=On Duty
att_timeSlot_offWork=Off Duty
att_timeSlot_minutesSignInAsOvertime=Check-In Minutes ago for Overtime
att_timeSlot_minutesSignOutAsOvertime=Start counting overtime minutes later
att_timeSlot_minOvertimeMinutes=Minimum Overtime Minutes
att_timeSlot_enableWorkingHours=Whether to enable working hours
att_timeSlot_eidtTimeSlot=Edit Time
att_timeSlot_browseBreakTime=Browse Rest Periods
att_timeSlot_addBreakTime=Add Breaks
att_timeSlot_enableFlexibleWork=Enable Flexible Work
att_timeSlot_advanceWorkMinutes=Can go to work in advance.
att_timeSlot_delayedWorkMinutes=Can postpone work.
att_timeSlot_advanceWorkMinutesValidMsg1=The number of minutes before going to work is greater than the number of minutes that can go to work in advance.
att_timeSlot_advanceWorkMinutesValidMsg2=The number of minutes that can go to work in advance is less than the number of minutes before going to work.
att_timeSlot_advanceWorkMinutesValidMsg3=The number of minutes that can be worked in advance is less than or equal to the number of minutes before signing in to work overtime.
att_timeSlot_advanceWorkMinutesValidMsg4=The number of minutes that can be before signing in to work overtime is greater than or equal to the number of minutes worked in advance.
att_timeSlot_delayedWorkMinutesValidMsg1=The number of minutes after after work is greater than the number of minutes that can be postponed to work.
att_timeSlot_delayedWorkMinutesValidMsg2=The number of minutes for can be delayed to work is less than the number of minutes after after work.
att_timeSlot_delayedWorkMinutesValidMsg3=The number of minutes that can be scheduled to work is less than or equal to the number of minutes after after work, signing off and starting to work overtime
att_timeSlot_delayedWorkMinutesValidMsg4=The number of minutes that can be after work, signing off and starting to work overtime is greater than or equal to the number of minutes after scheduled to work
att_timeSlot_allowLateMinutesValidMsg1=The number of minutes allowed to be late is less than the number of minutes after work.
att_timeSlot_allowLateMinutesValidMsg2=The number of minutes after after work is greater than the number of minutes allowed minutes to be late.
att_timeSlot_allowEarlyMinutesValidMsg1=Allow early minutes is less than minutes before work.
att_timeSlot_allowEarlyMinutesValidMsg2=The number of minutes before before work is greater than the number of minutes allowed minutes left early.
att_timeSlot_timeOverlap=The rest time overlaps, please modify the rest time period!
att_timeSlot_atLeastOne=At least 1 rest period!
att_timeSlot_mostThree=Up to 3 rest periods!
att_timeSlot_canNotEqual=The start time of the rest period cannot be equal to the end time!
att_timeSlot_shoudInWorkTime=Please make sure that the rest period is within the working hours!
att_timeSlot_repeatBreakTime=Repeat rest period!
att_timeSlot_toWorkLe=Working time is less than the minimum start time of the selected rest period:
att_timeSlot_offWorkGe=The off-hours are greater than the maximum end time of the selected rest period:
att_timeSlot_crossDays_toWork=The minimum start time for the break period is within the time period:
att_timeSlot_crossDays_offWork=The maximum end time of the rest period is within the time period:
att_timeSlot_allowLateMinutesRemark=From the working time to the allowed late minutes card to calculate the normal work card
att_timeSlot_allowEarlyMinutesRemark=Starting early from the off-duty time in the number of minutes allowed to leave early,the normal off-duty card
att_timeSlot_isSegmentDeductionRemark=Deleting the rest period in the time period
att_timeSlot_attEnableFlexibleWorkRemark1=Flexible work is not allowed to set the number of late,early departures
att_timeSlot_afterToWorkRemark=After work minutes is equal to Deferred to work minutes
att_timeSlot_beforeOffWorkRemark=Before work minutes equals can go to work minutes
att_timeSlot_attEnableFlexibleWorkRemark2=The number of minutes after after work is greater than or equal to off-hours + delayed work hours
att_timeSlot_attEnableFlexibleWorkRemark3=You can work in advance minutes,must be less than or equal to Working N minutes to work overtime minutes
att_timeSlot_attEnableFlexibleWorkRemark4=Deferred to work minutes,must be less than or equal to N minutes off work
att_timeSlot_attBeforeToWorkAsOvertimeRemark=Example:9:00 class,sign in to work overtime 60 minutes before work,then check in before 8 o'clock to 8 o'clock work overtime
att_timeSlot_attAfterOffWorkAsOvertimeRemark=Example:After 18 o'clock off work,after 60 minutes of work, sign the withdrawal and work overtime,then start overtime from 19 o'clock to the check-out time.
att_timeSlot_longTimeValidRemark=The effective signing time of after work and the effective signing time of before work cannot overlap in the time period!
att_timeSlot_advanceWorkMinutesValidMsg5=The number of valid minutes before check-in is greater than the number of minutes that can be worked in advance
att_timeSlot_advanceWorkMinutesValidMsg6=Minutes to work in advance must be less than valid minutes to sign in before work
att_timeSlot_delayedWorkMinutesValidMsg5=The number of valid minutes after check-in is greater than the number of minutes that can be postponed to work
att_timeSlot_delayedWorkMinutesValidMsg6=The number of minutes that can be postponed to work should be less than the valid minutes after signing in
att_timeSlot_advanceWorkMinutesValidMsg7=The check-in start time cannot overlap with the check-out end time of the last day
att_timeSlot_delayedWorkMinutesValidMsg7=The check-out end time cannot overlap with the check-in start time of the next day
att_timeSlot_maxOvertimeMinutes=Limit the maximum overtime hours
#班次
att_shift_basicSet=Basic Setting
att_shift_advancedSet=Advanced Setting
att_shift_type=Shift Type
att_shift_name=Shift Name
att_shift_regularShift=Regular Shift
att_shift_flexibleShift=Flexible Shift
att_shift_color=Color
att_shift_periodicUnit=Unit
att_shift_periodNumber=Cycle
att_shift_startDate=Start Date
att_shift_startDate_firstDay=Cycle Start Date
att_shift_isShiftWithinMonth=Cycle Shift in one Month
att_shift_attendanceMode=Attendance Mode
att_shift_shiftNormal=Punch According to Normal Shift
att_shift_oneDayOneCard=Punch Once at any Time in a Day
att_shift_onlyBrushTime=Only Calculate the Punch Time
att_shift_notBrushCard=No Punch Required
att_shift_overtimeMode=Overtime Mode
att_shift_autoCalc=Computer Automatic Calculation
att_shift_mustApply=Overtime must be applied for
att_shift_mustOvertime=Must work overtime or take Absence
att_shift_timeSmaller=Shorter Duration Between Auto-Calculation and Overtime Receipt
att_shift_notOvertime=Not Calculated as Overtime
att_shift_overtimeSign=Overtime Type
att_shift_normal=Normal Day
att_shift_restday=Rest Day
att_shift_timeSlotDetail=Timetable Details
att_shift_doubleDeleteTimeSlot=Double-click the shift period; you can delete the time period
att_shift_addTimeSlot=Add Timetable
att_shift_cleanTimeSlot=Clear Timetable
att_shift_NO=No.
att_shift_notAll=Unselect All
att_shift_notTime=If the timetable detail check box cannot be checked, it indicates that there is an overlap in the timetable.
att_shift_notExistTime=This timetable does not exist.
att_shift_cleanAllTimeSlot=Are you sure to clear the timetable for the selected shift?
att_shift_pleaseCheckBox=Please select the check box on the left side that is the same as the current display time on the right side.
att_shift_pleaseUnit=Please fill in the cycle units and the number of cycles.
att_shift_pleaseAllDetailTimeSlot=Please select the timetable details.
att_shift_placeholderNo=It is recommended to start with S, such as S01.
att_shift_placeholderName=It is recommended to start with S or end with shift.
att_shift_workType=Work Type
att_shift_normalWork=Normal Work
att_shift_holidayOt=Holiday OT
att_shift_attShiftStartDateRemark=Example: The starting date of the cycle is No. 22, with a period of three days, then No. 22/23/24 is on Class A/B/C, and No. 19/20/21 is on Class A. /B class / C class, before and after the date and so on.
att_shift_isShiftWithinMonthRemark1=Cycle Shift in one Month, the cycle only cycles to the last day of each month, not consecutively scheduled across the month;
att_shift_isShiftWithinMonthRemark2=Not Cycle Shift in one Month, the cycle is cycled to the last day of each month, if one cycle is not over, continue to the next month, and so on;
att_shift_workTypeRemark1=Note: When the work type is selected as overtime on a rest day, attendance will not be calculated on the day of a holiday.
att_shift_workTypeRemark2=Weekend OT,overtime mark defaults to rest day and the computer automatically calculates overtime. No overtime application is required. The working hours of the day are recorded as overtime hours, and attendance is not counted during holidays.
att_shift_workTypeRemark3=Holiday OT,overtime mark defaults to holidays and the computer automatically calculates overtime, no overtime application is required, and the working hours of the day are recorded as overtime hours;
att_shift_attendanceModeRemark1=Except for normally swiping by shift, it is not considered to be early or delayed overtime, for example:
att_shift_attendanceModeRemark2=1.No punch is required or a valid card is punched once a day, no overtime is counted;
att_shift_attendanceModeRemark3=2.Work type: normal work, attendance mode: free card swipe, then the day shift time is regarded as effective working time;
att_shift_periodStartMode=Period Start Type
att_shift_periodStartModeByPeriod=Period Start Date
att_shift_periodStartModeBySch=Shift Start Date
att_shift_addTimeSlotToShift=Whether to add the timetable of this shift
#=====================================================================
#分组
att_group_editGroup=Edit Personnel for Group
att_group_browseGroupPerson=Browse the Group Personnel
att_group_list=Group List
att_group_placeholderNo=It is recommended to start with G, such as G1
att_group_placeholderName=It is recommended to start with G or end with group.
att_widget_deptHint=Note: Import all personnel under the selected department
att_widget_searchType=Conditional Query
att_widget_noPerson=No one was selected
#分组排班
#部门排班
att_deptSch_existsDept=There is a departmental shift in the department and it is not allowed to delete the department.
#人员排班
att_personSch_view=View Personnel Scheduling
#临时排班
att_schedule_type=Schedule Type
att_schedule_tempType=Temporary Type
att_schedule_normal=Normal Schedule
att_schedule_intelligent=Smart Schedule
att_tempSch_scheduleType=Scheduling Type
att_tempSch_startDate=Start Date
att_tempSch_endDate=End Date
att_tempSch_attendanceMode=Attendance Mode
att_tempSch_overtimeMode=Overtime Mode
att_tempSch_overtimeRemark=Overtime Mark
att_tempSch_existsDept=There is a departmental temporary shift in the department, and it is not allowed to delete the department.
att_schedult_opAddTempSch=New Temporary Shift
att_schedule_cleanEndDate=Empty End Time
att_schedule_selectOne=The normal schedule can only choose one shift!
att_schedule_selectPerson=Please select personnel first!
att_schedule_selectDept=Please select department first!
att_schedule_selectGroup=Please select group first!
att_schedule_selectOneGroup=Only one group can be selected!
att_schedule_arrange=Please choose a shift!
att_schedule_leave=Leave
att_schedule_trip=Trip
att_schedule_out=Out
att_schedule_off=Rest
att_schedule_makeUpClass=Append
att_schedule_class=Adjust
att_schedule_holiday=Holiday
att_schedule_offDetail=Adjust Rest
att_schedule_makeUpClassDetail=Append Attendance
att_schedule_classDetail=Adjust Shift
att_schedule_holidayDetail=Holiday
att_schedule_noSchDetail=Not Scheduled
att_schedule_normalDetail=Normal
att_schedule_normalSchInfo=Shift Center: No Cross-day Shift
att_schedule_multipleInterSchInfo=Shift comma-separated: multiple cross-day shifts
att_schedule_inderSchFirstDayInfo=Shift across the grid backward offset: The cross-day shift is recorded as the first day
att_schedule_inderSchSecondDayInfo=Shift across the grid forward offset: The cross-day shift is recorded as the second day
att_schedule_timeConflict=Conflict with the existing shift time period, not allowed to save!
#=====================================================================
att_excp_notExisetPerson=Person do not exist!
att_excp_leavePerson=The staff has resigned!
#补签单
att_sign_signTime=Punch Time
att_sign_signDate=Punch Date
#请假
att_leave_arilName=Leave Type
att_leave_image=Leave Requisition Photo
att_leave_imageShow=No Pictures
att_leave_imageType=Wrong Tip: Picture format is not correct, support the format: JPEG, GIF, PNG!
att_leave_imageSize=Wrong Tip: The selected picture is too large, the maximum size of the picture is 4MB!
att_leave_leaveLongDay=Leave Duration (Days)
att_leave_leaveLongHour=Leave Duration (Hours)
att_leave_leaveLongMinute=Leave Duration (Minutes)
att_leave_endNoLessAndEqualStart=End time cannot be less than or equal to start time
att_leave_typeNameNoExsists=Holiday name does not exist
att_leave_startNotNull=Start time cannot be empty
att_leave_endNotNull=End time cannot be empty
att_leave_typeNameConflict=The holiday name conflicts with the attendance status name
#出差
att_trip_tripLongDay=Trip Duration(Days)
att_trip_tripLongMinute=Trip Duration(Minutes)
att_trip_tripLongHour=Trip Duration(Hours)
#外出
att_out_outLongDay=Out Duration(Days)
att_out_outLongMinute=Out Duration(Minutes)
att_out_outLongHour=Out Duration(Hours)
#加班
att_overtime_type=OT Type
att_overtime_normal=Normal OT
att_overtime_rest=Rest Day OT
att_overtime_overtimeLong=Overtime Duration(Minutes)
att_overtime_overtimeHour=Overtime Duration(Hours)
att_overtime_notice=Overtime application time is not allowed for more than one day!
att_overtime_minutesNotice=The overtime application time cannot be less than the minimum overtime time!
#调休补班
att_adjust_type=Adjust Type
att_adjust_adjustDate=Adjust Date
att_adjust_shiftName=Appended Shift
att_adjust_selectClass=Please select the shift name which needs appended attendance.
att_shift_notExistShiftWorkDate={1} shift scheduling at {0} and rest is not allowed to apply for make-up shift!
att_adjust_shiftPeriodStartMode=The shift selected for make-up shift, if the start date is by shift, the default is the 0th
att_adjust_shiftNameNoNull=Appended shifts cannot be empty
att_adjust_shiftNameNoExsist=Appended shift does not exist
#调班
att_class_type=Adjust Type
att_class_sameTimeMoveShift=Adjust the personal shift in the same day
att_class_differenceTimeMoveShift=Adjust the personal shift in other days
att_class_twoPeopleMove=Two people exchange
att_class_moveDate=Adjust Date
att_class_shiftName=Adjust Shift Name
att_class_moveShiftName=The new adjusted shift cannot be empty.
att_class_movePersonPin=Adjust Personnel ID
att_class_movePersonName=Adjust Personnel Name
att_class_movePersonLastName=Adjust Personnel Last Name
att_class_moveDeptName=Adjust Department Name
att_class_personPin=Personnel ID
att_class_shiftNameNoNull=The new adjusted shift cannot be empty.
att_class_personPinNoNull=The Personnel ID of the new  person can not be empty!
att_class_isNotExisetSwapPersonPin=The new Adjust person does not exist, please re-add!
att_class_personNoSame=You cannot adjust for the same person, please try again.
att_class_outTime=The adjust date and the transfer date cannot be more than one month！
att_class_shiftNameNoExsist=Adjust shift does not exist
att_class_swapPersonNoExisist=The exchange person does not exist
att_class_dateNoSame=Individuals are transferred on different dates, the dates cannot be the same
#=====================================================================
#节点
att_node_name=Node
att_node_type=Node Type
att_node_leader=Direct Leader
att_node_leaderNode=Direct Leader Node
att_node_person=Designated Person
att_node_position=Assign Position
att_node_choose=Select Position
att_node_personNoNull=Personnel can not be empty!
att_node_posiitonNoNull=Position cannot be empty
att_node_placeholderNo=It is recommended to start with N, such as N01.
att_node_placeholderName=It is recommended to start with a position or name, ending with a node, such as Manager Node.
att_node_searchPerson=Input Search Criteria
att_node_positionIsExist=The position already exists in the node data, please select the position again.
#流程
att_flow_type=Flow Type
att_flow_rule=Flow Rule
att_flow_rule0=Less than or equal to 1 day
att_flow_rule1=More than 1 day and less than or equal to 3 days
att_flow_rule2=More than 3 days and less than or equal to 7 days
att_flow_rule3=More than 7 days
att_flow_node=Approval Node
att_flow_start=Start Flow
att_flow_end=End Flow
att_flow_addNode=Add Node
att_flow_placeholderNo=It is recommended to start with F, such as F01.
att_flow_placeholderName=It is recommended to start with type, end with flow, eg.Leave Flow.
att_flow_tips=Note: The approval order of the nodes is from top to bottom, and you can drag the sort after selecting.
#申请
att_apply_personPin=Applicant ID
att_apply_type=Exception Type
att_apply_flowStatus=Flow Status
att_apply_start=Initiating an application
att_apply_flowing=Pending
att_apply_pass=Passed
att_apply_over=End
att_apply_refuse=Rejected
att_apply_revoke=Revoke
att_apply_except=Exception
att_apply_view=View Details
att_apply_leaveTips=The person has a leave request during this time period!
att_apply_tripTips=Personnel have a business trip application during this time period!
att_apply_outTips=Personnel have applied for out during this time period!
att_apply_overtimeTips=Personnel have overtime applications during this time period!
att_apply_adjustTips=During this period of time, the personnel can apply for a rehearsal!
att_apply_classTips=Personnel have a shift application during this time period!
#审批
att_approve_wait=Pending Approval
att_approve_refuse=Not Passed
att_approve_reason=Reason
att_approve_personPin=Approver ID
att_approve_personName=Approver Name
att_approve_person=Approver
att_approve_isPass=Whether to approve?
att_approve_status=Current Node Status
att_approve_tips=The time point already exists in the flow and cannot be repeated.
att_approve_tips2=The flow node has not been configured, please contact the administrator for configuration.
att_approve_offDayConflicts={0} is not scheduled or scheduled on {1} and the rest is not allowed.
att_approve_shiftConflicts={0} already has a shift in {1} and does not allow to apply for shifts on the working day!
att_approve_shiftNoSch={0} No shift application is allowed when the shift is not scheduled on {1}!
att_approve_classConflicts=No shift application is allowed on the unscheduled date
att_approve_selectTime=Selection time will determine the process according to the rules
att_approve_withoutPermissionApproval=There is a workflow without permission for approval, please check!
#=====================================================================
#考勤计算
att_op_calculation=Attendance Calculation
att_op_calculation_notice=Attendance data has been calculated in the background, please try again later!
att_op_calculation_leave=Including resigned personnel
att_statistical_choosePersonOrDept=Please select Department or Personnel!
att_statistical_sureCalculation=Are you sure to perform attendance calculations?
att_statistical_filter=The filtration condition is ready!
att_statistical_initData=Initialization of database has completed!
att_statistical_exception=Initialization of exception data has completed!
att_statistical_error=Absence calculation failed!
att_statistical_begin=Start calculating!
att_statistical_end=End the calculation!
att_statistical_noticeTime=Attendance optional time range: the first two months to the day!
att_statistical_remarkHoliday=H
att_statistical_remarkClass=C
att_statistical_remarkNoSch=NS
att_statistical_remarkRest=R
#原始记录表
att_op_importAccRecord=Import Access Control Records
att_op_importParkRecord=Import Parking Records
att_op_importInsRecord=Import FaceKiosk Records
att_op_importPidRecord=Import Personal Certificate Records
att_op_importVmsRecord=Import Video Records
att_op_importUSBRecord=Import U Disk Records
att_transaction_noAccModule=No Access Control Module!
att_transaction_noParkModule=No Parking Module!
att_transaction_noInsModule=No FaceKiosk Module!
att_transaction_noPidModule=No Personal Certificate Module!
att_transaction_exportRecord=Export Original Records
att_transaction_exportAttPhoto=Export Attendance Photos
att_transaction_fileIsTooLarge=The exported file is too large, please narrow the date range
att_transaction_exportDate=Export Date
att_statistical_attDatetime=Attendance time
att_statistical_attPhoto=Attendance Photo
att_statistical_attDetail=Attendance Details
att_statistical_acc=Access Control Device
att_statistical_att=Time Attendance Device
att_statistical_park=LPR Camera
att_statistical_faceRecognition=Face Recognition Device
att_statistical_app=Mobile Devices
att_statistical_vms=Video Devices
att_statistical_psg=Channel Devices
att_statistical_dataSources=Data Sources
att_transaction_SyncRecord=Synchronize attendance records
#日打卡详情表
att_statistical_dayCardDetail=Check-in Details
att_statistical_cardDate=Record Date
att_statistical_cardNumber=Record Counts
att_statistical_earliestTime=Earliest Time
att_statistical_latestTime=Latest Time
att_statistical_cardTime=Punch Time
#请假汇总表
att_statistical_leaveDetail=Leave Details
#日明细报表
att_statistical_attDate=Attendance Date
att_statistical_week=Week
att_statistical_shiftInfo=Shift Information
att_statistical_shiftTimeData=Work Time
att_statistical_cardValidData=Punch Data
att_statistical_cardValidCount=Punch Counts
att_statistical_lateCount=Late Counts
att_statistical_lateMinute=Late Minutes
att_statistical_earlyCount=Early Counts
att_statistical_earlyMinute=Early Minutes
att_statistical_countData=Counts Data
att_statistical_minuteData=Minutes Data
att_statistical_attendance_minute=Attendance (Minutes)
att_statistical_overtime_minute=Overtime (Minutes)
att_statistical_unusual_minute=Exception (Minutes)
#月明细报表
att_monthdetail_should_hour=Should (Hours)
att_monthdetail_actual_hour=Actual (Hours)
att_monthdetail_valid_hour=Valid (Hours)
att_monthdetail_absent_hour=Absent (Hours)
att_monthdetail_leave_hour=Leave (Hours)
att_monthdetail_trip_hour=Business Trip (Hours)
att_monthdetail_out_hour=Go Out (Hours)
att_monthdetail_should_day=Should (Days)
att_monthdetail_actual_day=Actual (Days)
att_monthdetail_valid_day=Valid (Days)
att_monthdetail_absent_day=Absent (Days)
att_monthdetail_leave_day=Leave (Days)
att_monthdetail_trip_day=Business Trip (Days)
att_monthdetail_out_day=Go Out (Days)
#月统计报表
att_statistical_late_minute=Duration (Minutes)
att_statistical_early_minute=Duration (Minutes)
#部门统计报表
#年度统计报表
att_statistical_should=Should
att_statistical_actual=Actual
att_statistical_valid=Valid
att_statistical_numberOfTimes=Counts
att_statistical_usually=Weekday
att_statistical_rest=Weekend
att_statistical_holiday=Holiday
att_statistical_total=Total
att_statistical_month=Statistics of Month
att_statistical_year=Statistics of Year
att_statistical_attendance_hour=Attendance (Hours)
att_statistical_attendance_day=Attendance (Days)
att_statistical_overtime_hour=Overtime (Hours)
att_statistical_unusual_hour=Exception (Hours)
att_statistical_unusual_day=Abnormal (Days)
#考勤设备参数
att_deviceOption_query=View Device Parameters
att_deviceOption_noOption=No parameter information, please get the device parameters first
att_deviceOption_name=Parameter Name
att_deviceOption_value=Parameter Value
att_deviceOption_UserCount=Current
att_deviceOption_MaxUserCount=Maximum Users
att_deviceOption_FaceCount=Current
att_deviceOption_MaxFaceCount=Maximum Facial Templates
att_deviceOption_FacePhotoCount=Current
att_deviceOption_MaxFacePhotoCount=Maximum Facial Images
att_deviceOption_FingerCount=Current
att_deviceOption_MaxFingerCount=Maximum Fingerprint Templates
att_deviceOption_FingerPhotoCount=Current
att_deviceOption_MaxFingerPhotoCount=Maximum Fingerprint Images
att_deviceOption_FvCount=Current
att_deviceOption_MaxFvCount=Maximum Finger Vein Templates
att_deviceOption_FvPhotoCount=Current
att_deviceOption_MaxFvPhotoCount=Maximum Finger Vein Images
att_deviceOption_PvCount=Current
att_deviceOption_MaxPvCount=Maximum Palm Templates
att_deviceOption_PvPhotoCount=Current
att_deviceOption_MaxPvPhotoCount=Maximum Palm Images
att_deviceOption_TransactionCount=Current
att_deviceOption_MaxAttLogCount=Maximum Attendance Records
att_deviceOption_UserPhotoCount=Current
att_deviceOption_MaxUserPhotoCount=Maximum User Photos
att_deviceOption_FaceVersion=Face Recognition Algorithm Version
att_deviceOption_FPVersion=Fingerprint Recognition Algorithm Version
att_deviceOption_FvVersion=Finger Vein Recognition Algorithm Version
att_deviceOption_PvVersion=Palm Recognition Algorithm Version
att_deviceOption_FWVersion=Firmware Version
att_deviceOption_PushVersion=Push Version
#=====================================================================
#API
att_api_areaCodeNotNull=Area number cannot be empty
att_api_pinsNotNull=Pin data is not allowed to be empty
att_api_pinsOverSize=Pin data length is not allowed to exceed 500
att_api_areaNoExist=Area does not exist
att_api_sign=Manual Check Ins
#=====================================================================
#休息时间段
att_leftMenu_breakTime=Rest Timetable
att_breakTime_startTime=Start Time
att_breakTime_endTime=End Time
#=====================================================================
#导入U盘记录
att_import_uploadFileSuccess=Upload the file successfully, start analysis the file data, please wait...
att_import_resolutionComplete=After the analysis is complete, start updating the database.
att_import_snNoExist=The attendance device corresponding to the imported file does not exist. Please select the file again.
att_import_fileName_msg=Imported file name format requirements: Begin with device serial number and separated by an underscore "_", for example："3517171600001_attlog.dat".
att_import_notSupportFormat=This format is not supported at the moment.
att_import_selectCorrectFile=Please select the correct format file!
att_import_fileFormat=File Format
att_import_targetFile=Target File
att_import_startRow=Number of rows at the beginning of the header
att_import_startRowNote=The first line of the data format is to import data, please check the file before importing.
att_import_delimiter=Separator
#=====================================================================
#设备操作日志
att_device_op_log_op_type=Operation Code
att_device_op_log_dev_sn=Device SN
att_device_op_log_op_content=Operational Content
att_device_op_log_operator_pin=Operator Number
att_device_op_log_operator_name=Operator Name
att_device_op_log_op_time=Operation Time
att_device_op_log_op_who_value=Operation Object Value
att_device_op_log_op_who_content=Operation Object Description
att_device_op_log_op_value1=Operation Object 2
att_device_op_log_op_value_content1=Operation Object Description 2
att_device_op_log_op_value2=Operation Object 3
att_device_op_log_op_value_content2=Operation Object Description 3
att_device_op_log_op_value3=Operation Object 4
att_device_op_log_op_value_content3=Operation Object Description 4
#操作日志的操作类型
att_device_op_log_opType_0=Power on
att_device_op_log_opType_1=Shutdown
att_device_op_log_opType_2=Verification is failed.
att_device_op_log_opType_3=Alarm
att_device_op_log_opType_4=Enter Menu
att_device_op_log_opType_5=Change Settings
att_device_op_log_opType_6=Enroll Fingerprint
att_device_op_log_opType_7=Register Password
att_device_op_log_opType_8=Register HID Card
att_device_op_log_opType_9=Delete User
att_device_op_log_opType_10=Delete Fingerprint
att_device_op_log_opType_11=Delete Password
att_device_op_log_opType_12=Delete RF card
att_device_op_log_opType_13=Clear Data
att_device_op_log_opType_14=Create MF Card
att_device_op_log_opType_15=Register MF Card
att_device_op_log_opType_16=Register MF Card
att_device_op_log_opType_17=Delete MF Card Registration
att_device_op_log_opType_18=Clear MF Card Content
att_device_op_log_opType_19=Move registration data to the card.
att_device_op_log_opType_20=Copy the data from the card to the machine.
att_device_op_log_opType_21=Set Time
att_device_op_log_opType_22=Factory Settings
att_device_op_log_opType_23=Delete Entry and Exit Records
att_device_op_log_opType_24=Clear Administrator Privileges
att_device_op_log_opType_25=Modify Access Control Group Settings
att_device_op_log_opType_26=Modify User Access Settings
att_device_op_log_opType_27=Modify Access Time Period
att_device_op_log_opType_28=Modify Unlock Combination Settings
att_device_op_log_opType_29=Unlock
att_device_op_log_opType_30=Register New Users
att_device_op_log_opType_31=Change Fingerprint Attributes
att_device_op_log_opType_32=Duress Alarm
att_device_op_log_opType_34=Anti-Passback
att_device_op_log_opType_35=Delete Attendance Photos
att_device_op_log_opType_36=Modify User Information
att_device_op_log_opType_37=Holiday
att_device_op_log_opType_38=Restore Data
att_device_op_log_opType_39=Backup Data
att_device_op_log_opType_40=U Disk Upload
att_device_op_log_opType_41=U Disk Download
att_device_op_log_opType_42=U disk Attendance Record Encryption
att_device_op_log_opType_43=Delete record after successful USB disk download.
att_device_op_log_opType_53=Outgoing Switch
att_device_op_log_opType_54=Door Sensor
att_device_op_log_opType_55=Alarm
att_device_op_log_opType_56=Restore Parameters
att_device_op_log_opType_68=Registered User Photo
att_device_op_log_opType_69=Modify User Photos
att_device_op_log_opType_70=Modify User Name
att_device_op_log_opType_71=Modify User Permissions
att_device_op_log_opType_76=Modify Network Settings IP
att_device_op_log_opType_77=Modify Network Settings Mask
att_device_op_log_opType_78=Modify Network Settings Gateway
att_device_op_log_opType_79=Modify Network Settings DNS
att_device_op_log_opType_80=Modify Connection Setup Password
att_device_op_log_opType_81=Modify Connection Settings Device ID
att_device_op_log_opType_82=Modify Cloud Server Address
att_device_op_log_opType_83=Modify Cloud Server Port
att_device_op_log_opType_87=Modify Access Control Record Settings
att_device_op_log_opType_88=Modify Face Parameter Flag
att_device_op_log_opType_89=Modify the Fingerprint Parameter Flag
att_device_op_log_opType_90=Modify the Finger Vein Parameter Flag
att_device_op_log_opType_91=Modify Palmprint Parameter Flag
att_device_op_log_opType_92=U-Disk Upgrade Flag
att_device_op_log_opType_100=Modify RF Card Information
att_device_op_log_opType_101=Register Face
att_device_op_log_opType_102=Modify Staff Permissions
att_device_op_log_opType_103=Delete Personnel Permissions
att_device_op_log_opType_104=Add Staff Permissions
att_device_op_log_opType_105=Delete Access Control Record
att_device_op_log_opType_106=Delete Face
att_device_op_log_opType_107=Delete Personnel Photos
att_device_op_log_opType_108=Modify Parameters
att_device_op_log_opType_109=Select WIFI SSID
att_device_op_log_opType_110=Proxy Enable
att_device_op_log_opType_111=Proxy IP Modification
att_device_op_log_opType_112=Proxy Port Modification
att_device_op_log_opType_113=Modify the Person Password
att_device_op_log_opType_114=Modify Face Information
att_device_op_log_opType_115=Modify the password of the operator.
att_device_op_log_opType_116=Resume Access Control Settings
att_device_op_log_opType_117=Operator Password Input Error
att_device_op_log_opType_118=Operator Password Lock
att_device_op_log_opType_120=Modify Legic Card Data Length
att_device_op_log_opType_121=Register Finger Vein
att_device_op_log_opType_122=Modify Finger Vein
att_device_op_log_opType_123=Delete Finger Vein
att_device_op_log_opType_124=Register Palm
att_device_op_log_opType_125=Modify Palm
att_device_op_log_opType_126=Delete Palm
#操作对象描述
att_device_op_log_content_pin=User ID:
att_device_op_log_content_alarm=Alarm:
att_device_op_log_content_alarm_reason=Alarm Reason:
att_device_op_log_content_update_no=Modify Item Number:
att_device_op_log_content_update_value=Modify Value:
att_device_op_log_content_finger_no=Fingerprint Number:
att_device_op_log_content_finger_size=Fingerprint Template Length:
#=====================================================================
#工作流
att_flowable_datetime_to=to
att_flowable_todomsg_leave=Leave Approval
att_flowable_todomsg_sign=Manual Check In Approval
att_flowable_todomsg_overtime=Overtime Approval
att_flowable_notifymsg_leave=Leave Application Notification
att_flowable_notifymsg_sign=Manual Check In Notification 
att_flowable_notifymsg_overtime=Overtime Notification
att_flowable_shift=Shift:
att_flowable_hour=Hour
att_flowable_todomsg_trip=Business Trip Approval
att_flowable_notifymsg_trip=Business Trip
att_flowable_todomsg_out=Outgoing Approval
att_flowable_notifymsg_out=Outing Notification
att_flow_apply=Application
att_flow_applyTime=Application Time
att_flow_approveTime=Processing Time
att_flow_operateUser=Reviewer
att_flow_approve=Approval
att_flow_approveComment=Annotation
att_flow_approvePass=Approval Results
att_flow_status_processing=Approval
#=====================================================================
#biotime
att_h5_pers_personIdNull=Employee ID cannot be empty
att_h5_attPlaceNull=Check-in location cannot be empty
att_h5_attAreaNull=Attendance area cannot be empty
att_h5_pers_personNoExist=Employee number does not exist
att_h5_signRemarkNull=Remarks cannot be empty
att_h5_common_pageNull=Paging parameter error
att_h5_taskIdNotNull=Task node id cannot be empty
att_h5_auditResultNotNull=Approval result cannot be empty
att_h5_latLongitudeNull=Longitude and latitude cannot be empty
att_h5_pers_personIsNull=Employee ID does not exist
att_h5_pers_personIsNotInArea=The person has not set the area
att_h5_mapApiConnectionsError=Map API connection error
att_h5_googleMap=Google Map
att_h5_gaodeMap=Amap
att_h5_defaultMap=Default Map
att_h5_shiftTime=Shift Time
att_h5_signTimes=Replenishment Time
att_h5_enterKeyWords=Please enter key words:
att_h5_mapSet=Attendance Map Settings
att_h5_setMapApiAddress=Set Map Parameter
att_h5_MapSetWarning=Switching the map will cause the entered mobile terminal sign-in address to fail to correspond to the latitude and longitude, please modify with caution!
att_h5_mapSelect=Map Selection
att_h5_persNoHire=The employee has not yet joined the company at this time.
att_slef_apiKey=API-KEY
att_self_apiJsKey=API-JS-KEY
att_self_noComputeAtt=The attendance of the day has not been accounted for yet.
att_self_noSignRecord=No punch record on the day
att_self_imageUploadError=Image upload failed
att_self_attSignAddressAreaIsExist=There are already attendance points in the area
att_self_signRuleIsError=The current time is not within the allowable appended log time.
att_self_signAcrossDay=Cross day schedule can not Manual Check In!
att_self_todaySignIsExist=There are appended logs today!
att_self_signSetting=Appended Log Setting
att_self_allowSign=Allow Append:
att_self_allowSignSuffix=days,Attendance record within
att_self_onlyThisMonth=Only This Month
att_self_allowAcrossMonth=Allow Cross Month
att_self_thisTimeNoSch=There is no shift in the current time period!
att_self_revokeReason=Reason for Revocation:
att_self_revokeHint=Please enter the reason for revocation within 20 words for review
att_self_persSelfLogin=Employee Self-Service Login
att_self_isOpenSelfLogin=Whether to enable employee self-service login
att_self_applyAndWorkTimeOverlap=Application time and working time overlap
att_apply_DurationIsZero=Application duration is 0, application is not allowed
att_sign_mapWarn=Map loading failed, please check network connection and map KEY value
att_admin_applyWarn=Operation failed, there are people who are un-scheduled or the application time is not within the schedule scope!({0})
att_self_getPhotoFailed=Picture does not exist
att_self_view=View
# 二维码
att_param_qrCodeUrl=QR Code URL
att_param_qrCodeUrlHref=Server Address: Port
att_param_appAttQrCode=Mobile Attendance QR Code
att_param_timingFrequency=Time interval: 5-59 minutes or 1-24 hours
att_sign_signTimeNotNull=The appended log time cannot be empty
att_apply_overLastMonth=Applications began longer than last month
att_apply_withoutDetail=No process details
att_flowable_noAuth=Please view with super admin account
att_apply_overtimeOverMaxTimeLong=The overtime hours are greater than the maximum overtime hours
# 考勤设置参数符号
att_other_arrive=√
att_other_late=L
att_other_early=E
att_other_absent=□
att_other_noSignIn=[
att_other_noSignOff=]
att_other_leave=∆
att_other_overtime=OT
att_other_off=○
att_other_classes=●
att_other_trip=T
att_other_out=G
att_other_incomplete=├
att_other_outcomplete=┤
# 服务器下发命令
att_devCmd_submitTime=Submit Time
att_devCmd_returnedResult=Return Result
att_devCmd_returnTime=Time of Return
att_devCmd_content=Command Content
att_devCmd_clearCmd=Clear Command List
# 实时点名
att_realTime_selectDept=Please select a department
att_realTime_noSignPers=No Check-in Person
att_realTime_signPers=Checked In
att_realTime_signMonitor=Check-In Monitoring
att_realTime_signDateTime=Check In Time
att_realTime_realTimeSet=Real Time Roll Call Setting
att_realTime_openRealTime=Enable Real Time Roll Call
att_realTime_rollCallEnd=Real-time roll call ends
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=Scheduled
att_personSch_cycleSch=Cycle Schedule
att_personSch_cleanCycleSch=Clear Cycle Schedule
att_personSch_cleanTempSch=Clear Temporary Schedule
att_personSch_personCycleSch=Personnel Cycle Schedule
att_personSch_deptCycleSch=Department Cycle Schedule
att_personSch_groupCycleSch=Group Cycle Schedule
att_personSch_personTempSch=Personnel Temporary Schedule
att_personSch_deptTempSch=Department Temporary Schedule
att_personSch_groupTempSch=Group Temporary Schedule
att_personSch_checkGroupFirst=Please check the group on the left or the people in the list on the right to operate!
att_personSch_sureDeleteGroup=Are you sure to delete {0} and the schedule corresponding to the group?
att_personSch_sch=Schedule
att_personSch_delSch=Delete Schedule
#考勤计算
att_statistical_sureAllCalculate=Are you sure to perform attendance calculation for all personnel?
#异常管理
att_exception_downTemplate=Download Import Template
att_exception_signImportTemplate=Appended Log Import Template
att_exception_leaveImportTemplate=Leave Import Template
att_exception_overtimeImportTemplate=Overtime Import Template
att_exception_adjustImportTemplate=Adjustment Import Template
att_exception_cellDefault=Not required field
att_exception_cellRequired=Required field
att_exception_cellDateTime=Required field, time format is yyyy-MM-dd HH:mm:ss, such as: 2020-07-07 08:30:00
att_exception_cellLeaveTypeName=Required field, such as:'personal leave','marriage leave','maternity leave','sick leave','annual leave','bereavement leave','family leave','breastfeeding leave','business trip','going out' Arrogant name
att_exception_cellOvertimeSign=Required field, such as:'normal overtime','overtime on rest days','overtime on holidays'
att_exception_cellAdjustType=Required field, such as:'transfer off','make up class'
att_exception_cellAdjustDate=Required field, time format is yyyy-MM-dd, such as: 2020-07-07
att_exception_cellShiftName=Required field when the adjustment type is shift adjustment
att_exception_refuse=Refuse
att_exception_end=Abnormal End
att_exception_delete=Delete
att_exception_stop=Pause
#时间段
att_timeSlot_normalTimeAdd=Add Normal Timetable
att_timeSlot_elasticTimeAdd=Add Flexible Timetable
#班次
att_shift_addRegularShift=Add Regular Shift
att_shift_addFlexibleShift=Add Fexible Shift
#参数设置
att_param_notLeaveSetting=Non-Leave Calculation Setting
att_param_smallestUnit=Minimum Unit
att_param_workDay=Work Day
att_param_roundingControl=Rounding Control
att_param_abort=Down (Discard)
att_param_rounding=Rounding
att_param_carry=Up (Carry)
att_param_reportSymbol=Report Display Symbol
att_param_convertCountValid=Please enter a number and only one decimal place is allowed
att_other_leaveThing=Personnal
att_other_leaveMarriage=Marriage
att_other_leaveBirth=Maternity
att_other_leaveSick=Sick
att_other_leaveAnnual=Annual
att_other_leaveFuneral=Funeral
att_other_leaveHome=Family
att_other_leaveNursing=Nursing
att_other_leavetrip=Business
att_other_leaveout=Outing
att_common_schAndRest=Rest Day
att_common_timeLongs=Duration
att_personSch_checkDeptOrPersFirst=Please check the department on the left or the person in the list on the right!
att_personSch_checkCalendarFirst=Please select the date you need to schedule first!
att_personSch_cleanCheck=Clear Selected
att_personSch_delTimeSlot=Clear Selected Timetable
att_personSch_repeatTimeSlotNoAdd=No repetitive time period will be added!
att_personSch_showSchInfo=Show Schedule Details
att_personSch_sureToCycleSch=Are you sure to cycle schedule {0} ?
att_personSch_sureToTempSch=Are you sure to temporarily schedule {0}?
att_personSch_sureToCycleSchDeptOrGroup=Are you sure to cycle schedule all personnel under {0}?
att_personSch_sureToTempSchDeptOrGroup=Are you sure to temporary schedule all personnel under {0}?
att_personSch_sureCleanCycleSch=Are you sure to clear cycle schedule {0} from {1} to {2}?
att_personSch_sureCleanTempSch=Are you sure to clear {0} the temporary schedule from {1} to {2}?
att_personSch_sureCleanCycleSchDeptOrGroup=Are you sure to clear the cycle schedule from {1} to {2} for all personnel under {0}?
att_personSch_sureCleanTempSchDeptOrGroup=Are you sure to clear the temporary schedule from {1} to {2} for all personnel under {0}?
att_personSch_today=Today
att_personSch_timeSoltName=Timetable Name
att_personSch_export=Export Personnel Schedule
att_personSch_exportTemplate=Export Personnel Temporary Schedule Template
att_personSch_import=Import Personnel Temporary Schedule
att_personSch_tempSchTemplate=Personnel Temporary Schedule Template
att_personSch_tempSchTemplateTip=Please select the schedule start time and end time, download the schedule template within that range
att_personSch_opTip=Operation Instructions
att_personSch_opTip1=1. You can drag the timetable to a single date in the calendar control to schedule.
att_personSch_opTip2=2. In the calendar control, double-click a single date to schedule.
att_personSch_opTip3=3. In the calendar control, press and hold the mouse to select multiple dates to schedule.
att_personSch_schRules=Schedule Rules
att_personSch_schRules1=1. Cycle Schedule: Overwrite the previous schedule after the same date, regardless of whether there is no intersection.
att_personSch_schRules2=2. Temporary Schedule: There is an intersection on the same date, and the previous temporary schedule is overwritten later. If there is no intersection, they will exist at the same time.
att_personSch_schRules3=3. Cycle and Temporary: If there is an intersection on the same date, the cycle schedule will be temporarily overwritten, and if there is no intersection, they will also both exist;If the cycle schedule type is smart schedule, the cycle schedule will be directly overwritten by temporary schedule.
att_personSch_schStatus=Schedule Status
#左侧菜单-排班管理
att_leftMenu_schDetails=Schedule Details
att_leftMenu_detailReport=Attendance Detail Report
att_leftMenu_signReport=Appended Log Details
att_leftMenu_leaveReport=Leave Details
att_leftMenu_abnormal=Exception Report
att_leftMenu_yearLeaveSumReport=Annual Leave Summary
att_leave_maxFileCount=You can add up to 4 photos.
#时间段
att_timeSlot_add=Set Timetable
att_timeSlot_select=Please select a time table!
att_timeSlot_repeat=Timetable "{0}" repeats!
att_timeSlot_overlapping=The timetable "{0}" overlaps with the commuting time of "{1}"!
att_timeSlot_addFirst=Please set the time table first!
att_timeSlot_notEmpty=The timetable corresponding to personnel ID {0} cannot be empty!
att_timeSlot_notExist=The timetable "{1}" corresponding to the personnel ID {0} does not exist!
att_timeSlot_repeatEx=The timetable "{1}" corresponding to the personnel ID {0} overlaps with the commuting time of "{2}"
att_timeSlot_importRepeat=The timetable "{1}" corresponding to personnel ID {0} is repeated
att_timeSlot_importNotPin=There is no person with personnel ID {0} in the system!
att_timeSlot_elasticTimePeriod=Personnel number {0}, cannot import flexible time period "{1}"!
#导入
att_import_overData=The current number of imports is {0}, exceeding the limit of 30,000, please import seperated!
att_import_existIllegalType=The imported {0} has an illegal type!
#验证方式
att_verifyMode_0=Automatic Recognition
att_verifyMode_1=Fingerprint
att_verifyMode_2=PIN
att_verifyMode_3=Password
att_verifyMode_4=Card
att_verifyMode_5=Fingerprint/Password
att_verifyMode_6=Fingerprint/Card
att_verifyMode_7=Card/Password
att_verifyMode_8=PIN+Fingerprint
att_verifyMode_9=Fingerprint+Password
att_verifyMode_10=Card+Fingerprint
att_verifyMode_11=Card+Password
att_verifyMode_12=Fingerprint+Password+Card
att_verifyMode_13=PIN+Fingerprint+Password
att_verifyMode_14=(PIN+Fingerprint) / (Card+Fingerprint)
att_verifyMode_15=Face
att_verifyMode_16=Face+Fingerprint
att_verifyMode_17=Face+Password
att_verifyMode_18=Face+Card
att_verifyMode_19=Face+Fingerprint+Card
att_verifyMode_20=Face+Fingerprint+Password
att_verifyMode_21=Finger Vein
att_verifyMode_22=Finger Vein+Password
att_verifyMode_23=Finger vein+Card
att_verifyMode_24=Finger Vein+Password+Card
att_verifyMode_25=Palm
att_verifyMode_26=Palm+Card
att_verifyMode_27=Palm+face
att_verifyMode_28=Palm+Fingerprint
att_verifyMode_29=Palm+Fingerprint+Face
# 工作流
att_flow_schedule=Audit Progress
att_flow_schedulePass=(Passed)
att_flow_scheduleNot=(Not Approved)
att_flow_scheduleReject=(Rejected)
# 工作时长表
att_workTimeReport_total=Total Work Hours
# 自动导出报表
att_autoExport_startEndTime=Start and End Time
# 年假
att_annualLeave_setting=Annual Leave Balance Setting
att_annualLeave_settingTip1=To use the annual leave balance function, you need to set the hire date for each employee; when the hire date is not set, the remaining annual leave of the staff's annual leave balance table is displayed as empty.
att_annualLeave_settingTip2=If the current date is greater than the clearing issue date, this modification will take effect the following year; if the current date is less than the clearing issue date, when the clearing issue date is reached, it will be cleared and the annual leave will be reissued.
att_annualLeave_calculate=Annual Leave Clearing and Issuing Date
att_annualLeave_workTimeCalculate=Calculate According to Work Time Ratio
att_annualLeave_rule=Annual Leave Rule
att_annualLeave_ruleCountOver=The maximum set number limit has been reached
att_annualLeave_years=Working Years
att_annualLeave_eachYear=Every year
att_annualLeave_have=Yes
att_annualLeave_days=Days of Annual Leave
att_annualLeave_totalDays=Total Annual Leave
att_annualLeave_remainingDays=Remaining Annual Leave
att_annualLeave_consecutive=The annual leave rule setting must be consecutive years
# 年假结余表
att_annualLeave_report=Annual Leave Balance Sheet
att_annualLeave_validDate=Valid Date
att_annualLeave_useDays=Use {0} days
att_annualLeave_calculateDays=Release {0} days
att_annualLeave_notEnough=Insufficient annual leave for {0}!
att_annualLeave_notValidDate={0} is not within the valid range of annual leave!
att_annualLeave_notDays={0} has no annual leave!
att_annualLeave_tip1=Zhang San joined on September 1st last year
att_annualLeave_tip2=Annual Leave Balance Setting
att_annualLeave_tip3=The clearing and issuing date is January 1st of each year; it is calculated by rounding off according to the working ratio; if the length of service is less than or equal to 1, there will be 3 days of annual leave, and if the length of service is less than or equal to 3 years, there will be 5 days of annual leave
att_annualLeave_tip4=Annual Leave Calculation
att_annualLeave_tip5=Last year 09-01~12-31 enjoy 4/12x3=1.0 days
att_annualLeave_tip6=This year 01-01~12-31 enjoy 4.0 days (this year 01-01~08-31 enjoy 8/12x3=2.0 days   this year 09-01~12-31 enjoy 4/12x5≈2.0 days)
# att SDC
att_sdc_name=Video Channel
att_sdc_wxMsg_firstData=Hello, you have an attendance Check-In notice
att_sdc_wxMsg_stateData=Check-In success
att_sdc_wxMsg_remark=Reminder: The final attendance result is subject to the check-in details page.
# 时间段
att_timeSlot_conflict=The timetable conflicts with other timetable of the day
att_timeSlot_selectFirst=Please select the timetable
# 事件中心
att_eventCenter_sign=Attendance Check-In
#异常管理
att_exception_classImportTemplate=Shift Adjustment Import Template
att_exception_cellClassAdjustType=Required field, such as: "{0}", "{1}", "{2}"
att_exception_swapDateDate=non-required field, the time format is yyyy-MM-dd, such as: 2020-07-07
#消息中心
att_message_leave=Notice of Attendance {0}
att_message_leaveContent={0} submitted {1}, {2} time is {3}~{4}
att_message_leaveTime=Leave Time
att_message_overtime=Notice of Attendance and OverTime
att_message_overtimeContent={0} submitted overtime, and the overtime time is {1}~{2}
att_message_overtimeTime=OverTime
att_message_sign=Notice of Appended Log
att_message_signContent={0} submitted a supplementary sign, and the supplementary sign time is {1}
att_message_adjust=Notice of Rest Adjustment
att_message_adjustContent={0} submitted an adjustment, and the adjustment date is {1}
att_message_class=Notice of Shift Adjustment
att_message_classContent=Shift Adjustment Detail
att_message_classContent0={0} submitted a shift, the shift date is {1}, and the shift is {2}
att_message_classContent1={0} submitted a shift, the shift date is {1}, and the shift date is {2}
att_message_classContent2={0} ({1}) and {2} ({3}) exchanged shifts.
#推送中心
att_pushCenter_transaction=Attendance Record
# 时间段
att_timeSlot_workTimeNotEqual=Check-In time cannot be equal to Check-Out time
att_timeSlot_signTimeNotEqual=Start Check-In time cannot be equal to end Check-Out time
# 北向接口A
att_api_notNull={0} cannot be empty!
att_api_startDateGeEndDate=The start time cannot be greater than or equal to the end time!
att_api_leaveTypeNotExist=The fake species does not exist!
att_api_imageLengthNot2000=The length of the image address cannot exceed 2000!
# 20221230新增国际化
att_personSch_workTypeNotNull=The work type corresponding to personnel number {0} cannot be empty!
att_personSch_workTypeNotExist=The work type corresponding to personnel number {0} does not exist!
att_annualLeave_recalculate=Recalculate
# 20230530新增国际化
att_leftMenu_dailyReport=Daily Attendance Report
att_leftMenu_overtimeReport=Overtime Report
att_leftMenu_lateReport=Late Report
att_leftMenu_earlyReport=Early Leave Report
att_leftMenu_absentReport=Absence Report
att_leftMenu_monthReport=Monthly Attendance Report
att_leftMenu_monthWorkTimeReport=Monthly work time table
att_leftMenu_monthCardReport=Monthly Punch List
att_leftMenu_monthOvertimeReport=Monthly Overtime Report
att_leftMenu_overtimeSummaryReport=Employee Overtime Summary
att_leftMenu_deptOvertimeSummaryReport=Department Overtime Summary
att_leftMenu_deptLeaveSummaryReport=Department Leave Summary
att_annualLeave_calculateDay=Annual leave days
att_annualLeave_adjustDay=Adjust Days
att_annualLeave_sureSelectDept=Are you sure you want to perform {0} operation on the selected department?
att_annualLeave_sureSelectPerson=Are you sure you want to perform {0} operation on the selected person?
att_annualLeave_calculateTip1=When converting by length of service: the length of service of annual leave is calculated to the month. If the length of service is 10 years and 3 months, it is calculated as 10 years and 3 months;
att_annualLeave_calculateTip2=When not converted according to the length of service: the length of service for annual leave is calculated to the nearest year. If the length of service is 10 years and 3 months, it is calculated as 10 years;
att_rule_isInCompleteTip=If no sign-in or no sign-out record is incomplete, the priority is the highest, then no late arrival, early leave, absenteeism and validity will be counted
att_rule_absentTip=If the absence of sign-in or sign-out is recorded as absenteeism, the length of absenteeism is equal to the length of working hours minus the length of late and early leave
att_timeSlot_elasticTip1=When it is 0, the effective duration is equal to the actual duration, and there is no absenteeism
att_timeSlot_elasticTip2=If the actual duration is greater than the working duration, the effective duration is equal to the working duration, and there is no absenteeism
att_timeSlot_elasticTip3=If the actual duration is less than the working duration, the effective duration is equal to the actual duration, and absenteeism is equal to the working duration minus the actual duration
att_timeSlot_maxWorkingHours=Working hours cannot be greater than
# 20231030
att_customReport=Attendance custom report
att_customReport_byDayDetail=By Day Detail
att_customReport_byPerson=Summary by Person
att_customReport_byDept=Summary by Dept
att_customReport_queryMaxRange=Query Maximum Range is four months
# 20240630
att_personSch_shiftWorkTypeTip1=1. When working overtime on normal work/rest days, the priority of scheduling is lower than that of holidays
att_personSch_shiftWorkTypeTip2=2. When the work type is overtime during holidays, the priority of scheduling is higher than that of holidays
att_personVerifyMode=Personnel verification method
att_personVerifyMode_setting=Verification method settings
att_personSch_importCycSch=Import personnel cycle scheduling
att_personSch_cycSchTemplate=Personnel cycle scheduling template
att_personSch_exportCycSchTemplate=Download personnel cycle scheduling template
att_personSch_scheduleTypeNotNull=Shift type cannot be empty or does not exist!
att_personSch_shiftNotNull=The shift cannot be empty!
att_personSch_shiftNotExist=The shift does not exist!
att_personSch_onlyAllowOneShift=Regular scheduling only allows one shift to be scheduled!
att_shift_attShiftStartDateRemark2=The week in which the start date of the cycle is located is the first week; The month in which the start date of the cycle is located is the first month.
#打卡状态
att_cardStatus_setting=Attendance Status Setting
att_cardStatus_name=Name
att_cardStatus_value=Value
att_cardStatus_alias=Alias
att_cardStatus_every_day=Every Day
att_cardStatus_by_week=By Week
att_cardStatus_autoState=Auto State
att_cardStatus_attState=Attendance State
att_cardStatus_signIn=Check In
att_cardStatus_signOut=Check Out
att_cardStatus_out=Break Out
att_cardStatus_outReturn=Break In
att_cardStatus_overtime_signIn=Overtime In
att_cardStatus_overtime_signOut=Overtime Out
# 20241030新增国际化
att_leaveType_enableMaxDays=Enable Annual Limit
att_leaveType_maxDays=Annual Limit (days)
att_leaveType_applyMaxDays=Application cannot exceed {0} days in this year
att_param_overTimeSetting=Overtime Level Setting
att_param_overTimeLevel=Overtime Level (hours)
att_param_overTimeLevelEnable=Enable Overtime Level Calculation
att_param_reportColor=Report color
# APP
att_app_signClientTip=This mobile phone has been bound to a personnel ID for attendance verification, and it is not possible to clock in for other IDs using this device. 
att_app_noSignAddress=No check-in area configured, please contact administrator to set it up
att_app_notInSignAddress=Not arrived at check-in location, unable to check in
att_app_attendance=My Attendance
att_app_apply=Attendance Application
att_app_approve=My Approvals
# 20250530
att_node_leaderNodeExist=Direct supervisor approval node already exists
att_signAddress_init=Initialize map
att_signAddress_initTips=Please enter the map key and initialize the map to select an address