#系统名称
att_systemName=Att6.0考勤管理系统
#=====================================================================
#左侧菜单
att_module=考勤
#左侧菜单-考勤设备
att_leftMenu_attendanceDevice=考勤管理
att_leftMenu_device=考勤设备
att_leftMenu_point=考勤点
att_leftMenu_sign_address=移动端签到地址
att_leftMenu_adms_devCmd=服务器下发命令
#左侧菜单-基础信息
att_leftMenu_basicInformation=基础信息
att_leftMenu_rule=规则
att_leftMenu_base_rule=考勤规则
att_leftMenu_department_rule=部门规则
att_leftMenu_holiday=节假日
att_leftMenu_leaveType=假种
att_leftMenu_timingCalculation=定时计算
att_leftMenu_autoExport=报表推送
att_leftMenu_param=参数设置
#左侧菜单-班次管理
att_leftMenu_shiftManagement=班次管理
att_leftMenu_timeSlot=时间段
att_leftMenu_shift=班次
#左侧菜单-排班管理
att_leftMenu_scheduleManagement=排班管理
att_leftMenu_group=分组
att_leftMenu_groupPerson=分组人员
att_leftMenu_groupSch=分组排班
att_leftMenu_deptSch=部门排班
att_leftMenu_personSch=人员排班
att_leftMenu_tempSch=临时排班
att_leftMenu_nonSch=未排班人员
#左侧菜单-异常管理
att_leftMenu_exceptionManagement=考勤异常管理
att_leftMenu_sign=补签单
att_leftMenu_leave=请假
att_leftMenu_trip=出差
att_leftMenu_out=外出
att_leftMenu_overtime=加班
att_leftMenu_adjust=调休补班
att_leftMenu_class=调班
#左侧菜单-统计报表
att_leftMenu_statisticalReport=考勤汇总报表
att_leftMenu_manualCalculation=考勤计算
att_leftMenu_transaction=原始记录表
att_leftMenu_dayCardDetailReport=打卡详情表
att_leftMenu_leaveSummaryReport=人员请假汇总表
att_leftMenu_dayDetailReport=日明细表
att_leftMenu_monthDetailReport=月考勤状态表
att_leftMenu_monthStatisticalReport=人员汇总表
att_leftMenu_deptStatisticalReport=部门汇总表
att_leftMenu_yearStatisticalReport=年度统计表(按人)
att_leftMenu_attSignCallRollReport=签到点名表
att_leftMenu_workTimeReport=工作时长表
#左侧菜单-设备操作日志
att_leftMenu_device_op_log=设备操作日志
#左侧菜单-实时点名
att_leftMenu_realTime_callRoll=实时点名
#=====================================================================
#公共
att_common_person=人员
att_common_pin=编号
att_common_group=分组
att_common_dept=部门
att_common_symbol=符号
att_common_deptNo=部门编号
att_common_deptName=部门名称
att_common_groupNo=分组编号
att_common_groupName=分组名称
att_common_operateTime=操作时间
att_common_operationFailed=操作失败
att_common_id=ID
att_common_deptId=部门ID
att_common_groupId=分组ID
att_common_deviceId=设备ID
att_person_pin=人员编号
att_person_name=姓名
att_person_lastName=姓氏
att_person_internalCard=卡号
att_person_attendanceMode=考勤模式
att_person_normalAttendance=正常考勤
att_person_noPunchCard=免打卡
att_common_attendance=出勤
att_common_attendance_hour=出勤（时）
att_common_attendance_day=出勤（日）
att_common_late=迟到
att_common_early=早退
att_common_overtime=加班
att_common_exception=异常
att_common_absent=旷工
att_common_leave=请假
att_common_trip=出差
att_common_out=外出
att_common_staff=普通员工
att_common_superadmin=超级管理员
att_common_msg=短消息内容
att_common_min=短消息持续时间（分）
att_common_letterNumber=只能输入数字或字母！
att_common_relationDataCanNotDel=存在关联的数据不能删除！
att_common_relationDataCanNotEdit=存在关联的数据不能修改！
att_common_needSelectOneArea=请选择一个区域！
att_common_neesSelectPerson=请选择人员！
att_common_nameNoSpace=名称不能包含空格！
att_common_digitsValid=只能输入不超过两位小数的数字！
att_common_numValid=只能输入数字！
#=====================================================================
#工作面板
att_dashboard_worker=工作狂人
att_dashboard_today=今日考勤
att_dashboard_todayCount=本日考勤分段统计
att_dashboard_exceptionCount=异常统计(本月)
att_dashboard_lastWeek=上周
att_dashboard_lastMonth=上月
att_dashboard_perpsonNumber=总人数
att_dashboard_actualNumber=实到
att_dashboard_notArrivedNumber=未到
att_dashboard_attHour=工作时长
#区域
#设备
att_op_syncDev=同步软件数据到设备
att_op_account=考勤数据校对
att_op_check=重新上传数据
att_op_deleteCmd=清除设备命令
att_op_dataSms=对公短消息
att_op_clearAttPic=清除考勤照片
att_op_clearAttLog=清除考勤记录
att_device_waitCmdCount=待执行命令数
att_device_status=启用状态
att_device_register=登记机
att_device_isRegister=是否登记机
att_device_existNotRegDevice=非登记机设备，数据无法获取！
att_device_fwVersion=固件版本号
att_device_transInterval=刷新间隔时间（分钟）
att_device_cmdCount=和服务器通讯的最大命令个数
att_device_delay=查询记录时间（秒）
att_device_timeZone=时区
att_device_operationLog=操作日志
att_device_registeredFingerprint=登记指纹
att_device_registeredUser=登记用户
att_device_fingerprintImage=指纹图片
att_device_editUser=编辑用户
att_device_modifyFingerprint=修改指纹
att_device_faceRegistration=人脸登记
att_device_userPhotos=用户照片
att_device_attLog=是否上传考勤记录
att_device_operLog=是否上传人员信息
att_device_attPhoto=是否上传考勤照片
att_device_isOnLine=是否在线
att_device_InputPin=输入人员编号
att_device_getPin=获取指定人员数据
att_device_separatedPin=多个人员编号，用逗号隔开
att_device_authDevice=授权设备
att_device_disabled=以下设备处于禁用状态，无法操作！
att_device_autoAdd=新增设备自动添加
att_device_receivePersonOnlyDb=仅接收数据库中存在的人员数据
att_devMenu_control=设备控制
att_devMenu_viewOrGetInfo=查看和获取信息
att_devMenu_clearData=清除设备数据
att_device_disabledOrOffline=设备未启用或者不在线，无法操作！
att_device_areaStatus=设备区域状态
att_device_areaCommon=区域正常
att_device_areaEmpty=区域为空
att_device_isRegDev=时区或登记机状态修改，需设备重启后生效!
att_device_canUpgrade=以下设备可升级
att_device_offline=以下设备处于离线状态，无法操作！
att_device_oldProtocol=旧协议
att_device_newProtocol=新协议
att_device_noMoreTwenty=旧协议设备固件升级包不能超过20M
att_device_transferFilesTip=固件检测成功，传文件
att_op_clearAttPers=清除设备人员
#区域人员
att_op_forZoneAddPers=区域添加人员
att_op_dataUserSms=对私短消息
att_op_syncPers=重新同步到设备
att_areaPerson_choiceArea=请选择区域！
att_areaPerson_byAreaPerson=按区域设置人员
att_areaPerson_setByAreaPerson=按区域人员设置
att_areaPerson_importBatchDel=导入批量删除
att_areaPerson_syncToDevSuccess=操作成功！请等待命令下发完成。
att_areaPerson_personId=人员ID
att_areaPerson_areaId=区域ID
att_area_existPerson=区域存在人员！
att_areaPerson_notice1=区域或者人员不能同时为空！
att_areaPerson_notice2=未查询到人员或者区域！
att_areaPerson_notice3=区域下面未找到设备！
att_areaPerson_addArea=添加区域
att_areaPerson_delArea=删除区域
att_areaPerson_persNoExit=人员不存在
att_areaPerson_importTip1=请确保导入的人员已在人事模块中存在
att_areaPerson_importTip2=批量导入人员不会自动下发到设备，需要手动同步
att_areaPerson_addAreaPerson=添加区域人员
att_areaPerson_delAreaPerson=删除区域人员
att_areaPerson_importDelAreaPerson=导入删除区域人员
att_areaPerson_importAreaPerson=导入区域人员
#考勤点
att_attPoint_name=考勤点名称
att_attPoint_list=考勤点列表
att_attPoint_deviceModule=设备模块
att_attPoint_acc=门禁
att_attPoint_park=停车场
att_attPoint_ins=信息屏
att_attPoint_pid=人证
att_attPoint_vms=视频
att_attPoint_psg=通道
att_attPoint_doorList=门列表
att_attPoint_deviceList=设备列表
att_attPoint_channelList=通道列表
att_attPoint_gateList=闸列表
att_attPoint_recordTypeList=拉取记录类型
att_attPoint_door=请选择对应的门
att_attPoint_device=请选择对应的设备
att_attPoint_gate=请选择对应的闸
att_attPoint_normalPassRecord=正常通行记录
att_attPoint_verificationRecord=验证记录
att_person_attSet=考勤设置
att_attPoint_point=请选择考勤点
att_attPoint_count=考勤点数不足，操作失败
att_attPoint_notSelect=没有配置相应的模块
att_attPoint_accInsufficientPoints=门禁记录当考勤点数不足！
att_attPoint_parkInsufficientPoints=停车场记录当考勤点数不足！
att_attPoint_insInsufficientPoints=信息屏当考勤点数不足！
att_attPoint_pidInsufficientPoints=人证当考勤点数不足！
att_attPoint_doorOrParkDeviceName=门名称或停车设备名称
att_attPoint_vmsInsufficientPoints=视频当考勤点数不足！
att_attPoint_psgInsufficientPoints=通道当考勤点数不足！
att_attPoint_delDevFail=删除设备失败，设备已被当考勤点使用！
att_attPoint_pullingRecord=该考勤点正在定时获取记录,请稍后！
att_attPoint_lastTransactionTime=最近数据拉取时间
att_attPoint_masterDevice=主设备
att_attPoint_channelName=通道名称
att_attPoint_cameraName=摄像机名称
att_attPoint_cameraIP=摄像机ip
att_attPoint_channelIP=通道ip
att_attPoint_gateNumber=闸编号
att_attPoint_gateName=闸名称
#APP考勤签到地址
att_signAddress_address=地址
att_signAddress_longitude=经度
att_signAddress_latitude=纬度
att_signAddress_range=有效范围
att_signAddress_rangeUnit=单位为米(m)
#=====================================================================
#规则
att_rule_baseRuleSet=基础规则设置
att_rule_countConvertSet=计算转换设置
att_rule_otherSet=其它设置
att_rule_baseRuleSignIn=上班签到取卡规则
att_rule_baseRuleSignOut=下班签退取卡规则
att_rule_earliestPrinciple=最早
att_rule_theLatestPrinciple=最晚
att_rule_principleOfProximity=就近
att_rule_baseRuleShortestMinutes=最短的考勤时段应大于（最小值10分钟）
att_rule_baseRuleLongestMinutes=最长的考勤时段应小于（最大值1440分钟）
att_rule_baseRuleLateAndEarly=迟到且早退算旷工
att_rule_baseRuleCountOvertime=统计加班
att_rule_baseRuleFindSchSort=查找排班记录顺序
att_rule_groupGreaterThanDepartment=分组->部门
att_rule_departmentGreaterThanGroup=部门->分组
att_rule_baseRuleSmartFindClass=智能找班原则
att_rule_timeLongest=时长最长
att_rule_exceptionLeast=异常最少
att_rule_baseRuleCrossDay=班次时间段跨天时，考勤计算结果
att_rule_firstDay=第一日
att_rule_secondDay=第二日
att_rule_baseRuleShortestOvertimeMinutes=单次最短加班时长（分钟）
att_rule_baseRuleMaxOvertimeMinutes=最大加班时长（分钟）
att_rule_baseRuleElasticCal=弹性时长计算方式
att_rule_baseRuleTwoPunch=两两打卡累积时长
att_rule_baseRuleStartEnd=首尾打卡计算时长
att_rule_countConvertHour=小时转换基准
att_rule_formulaHour=公式：小时数 = 分钟数 / 60
att_rule_countConvertDay=天数转换基准
att_rule_formulaDay=公式：天数 = 分钟数 / 每天应工作的分钟数
att_rule_inFormulaShallPrevail=以公式计算结果为准；
att_rule_remainderHour=余数大于或等于
att_rule_oneHour=记为一小时；
att_rule_halfAnHour=记为半小时，否则忽略不计；
att_rule_remainderDay=商数大于或等于应工作的分钟数
att_rule_oneDay=%，记为一天；
att_rule_halfAnDay=%，记为半天，否则忽略不计；
att_rule_countConvertAbsentDay=旷工天数转换基准
att_rule_markWorkingDays=记为工作日数为准
att_rule_countConvertDecimal=小数点精确位数
att_rule_otherSymbol=设置报表中考勤结果的表示符号
att_rule_arrive=应到/实到
att_rule_noSignIn=未签到
att_rule_noSignOff=未签退
att_rule_off=调休
att_rule_class=补班
att_rule_shortLessLong=最短的考勤时间段不能大于最长的考勤时间段！
att_rule_symbolsWarning=设置报表中的考勤符号不能为空！
att_rule_reportSettingSet=报表导出设置
att_rule_shortDateFormat=日期格式
att_rule_shortTimeFormat=时间格式
att_rule_baseRuleSignBreakTime=休息时段是否打卡
att_leftMenu_custom_rule=自定义规则
att_custom_rule_already_exist={0}已存在自定义规则!
att_add_group_custom_rule=添加分组规则
att_custom_rule_type=规则类型
att_rule_type_group=分组规则
att_rule_type_dept=部门规则
att_custom_rule_orgNames=使用对象
att_rult_maxOverTimeType1=不限制
att_rult_maxOverTimeType2=本周
att_rult_maxOverTimeType3=本月
att_rule_countConvertDayRemark1=例：有效工作时长为500分钟，每天应工作时长为480分钟，则结果为500/480=1.04，保留最后一位小数为1.0
att_rule_countConvertDayRemark2=例：有效工作时长为500分钟，每天应工作时长为480分钟，则结果为500/480=1.04，1.04>0.8算一天
att_rule_countConvertDayRemark3=例：有效工作时长为300分钟，每天应工作时长为480分钟，则结果为300/480=0.625，0.2<0.625<0.8算半天
att_rule_countConvertDayRemark4=天数转换基准：则时间段内的记为工作日数不起作用；
att_rule_countConvertDayRemark5=记为工作日数为准：只限于旷工天数计算，且每个时段只要有旷工，就按时段的工作日数来计算旷工时长；
att_rule_baseRuleSmartFindRemark1=时长最长：按当天的卡点，计算当天每个班次对应的上班时长，找到当天最长的上班有效时长的那个班次；
att_rule_baseRuleSmartFindRemark2=异常最少：按当天的卡点，计算当天每个班次对应的异常次数，找到当天异常最少的班次来计算工作时长；
att_rule_baseRuleHourValidator=半小时的判断分钟数不能大于或等于1小时的判断分钟数！
att_rule_baseRuleDayValidator=半天的判断时长不能大于或等于1天的判断时长！
att_rule_overtimeWarning=最大加班时长不能小于最小单次最短加班时长!
att_rule_noSignInCountType=未签到记为
att_rule_absent=缺勤
att_rule_earlyLeave=早退
att_rule_noSignOffCountType=未签退记为
att_rule_minutes=分钟
att_rule_noSignInCountLateMinute=未签到记为迟到分钟数
att_rule_noSignOffCountEarlyMinute=未签退记为早退分钟数
att_rule_incomplete=不完整
att_rule_noCheckInIncomplete=未签到不完整
att_rule_noCheckOutIncomplete=未签退不完整
att_rule_lateMinuteWarning=未签到记为迟到分钟数应大于0且小于最长的考勤时段时长
att_rule_earlyMinuteWarning=未签退记为早退分钟数应大于0且小于最长的考勤时段时长
att_rule_baseRuleNoSignInCountLateMinuteRemark=未签到记为迟到时，如果未签到则记为迟到N分钟
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=未签退记为早退时，如果未签退则记为早退N分钟
#节假日
att_holiday_placeholderNo=建议以 H 开头，如H1
att_holiday_placeholderName=建议以 年份 + 节假日名，如2017五一
att_holiday_dayNumber=天数
att_holiday_validDate_msg=该时间内已有节假日
#假种
att_leaveType_leaveThing=事假
att_leaveType_leaveMarriage=婚假
att_leaveType_leaveBirth=产假
att_leaveType_leaveSick=病假
att_leaveType_leaveAnnual=年假
att_leaveType_leaveFuneral=丧假
att_leaveType_leaveHome=探亲假
att_leaveType_leaveNursing=哺乳假
att_leaveType_isDeductWorkLong=扣上班时长
att_leaveType_placeholderNo=建议以 L 开头，如 L1
att_leaveType_placeholderName=建议以 假 结尾，如 婚假
#定时计算
att_timingcalc_timeCalcFrequency=计算间隔
att_timingcalc_timeCalcInterval=定时计算时间
att_timingcalc_timeSet=定时计算时间设置
att_timingcalc_timeSelect=请选择时间
att_timingcalc_optionTip=至少要保留一个有效的按日定时考勤计算
#自动导出报表
att_autoExport_reportType=报表类型
att_autoExport_fileType=文件类型
att_autoExport_fileName=文件名称
att_autoExport_fileDateFormat=日期格式
att_autoExport_fileTimeFormat=时间格式
att_autoExport_fileContentFormat=内容格式
att_autoExport_fileContentFormatTxt=示例：{deptName}00{personPin}01{personName}02{attDatetime}03\r\n
att_autoExport_timeSendFrequency=发送频率
att_autoExport_timeSendInterval=时间发送间隔
att_autoExport_emailType=邮件接收方类型
att_autoExport_emailRecipients=邮件接收人
att_autoExport_emailAddress=邮箱地址
att_autoExport_emailExample=示例：<EMAIL>,<EMAIL>
att_autoExport_emailSubject=邮件标题
att_autoExport_emailContent=邮件正文
att_autoExport_field=字段
att_autoExport_fieldName=字段名称
att_autoExport_fieldCode=字段编码
att_autoExport_reportSet=报表设置
att_autoExport_timeSet=邮件发送时间设置
att_autoExport_emailSet=邮件设置
att_autoExport_emailSetAlert=请输入邮箱地址
att_autoExport_emailTypeSet=接收人设置
att_autoExport_byDay=按日
att_autoExport_byMonth=按月
att_autoExport_byPersonSet=按人员设置
att_autoExport_byDeptSet=按部门设置
att_autoExport_byAreaSet=按区域设置
att_autoExport_emailSubjectSet=标题设置
att_autoExport_emailContentSet=正文设置
att_autoExport_timePointAlert=请选择正确的发送时间点
att_autoExport_lastDayofMonth=每月最后一天
att_autoExport_firstDayofMonth=每月第一天
att_autoExport_dayofMonthCheck=具体日期
att_autoExport_dayofMonthCheckAlert=请选择具体日期
att_autoExport_chooseDeptAlert=请选择部门!
att_autoExport_sendFormatSet=发送方式设置
att_autoExport_sendFormat=发送方式
att_autoExport_mailFormat=邮箱发送方式
att_autoExport_ftpFormat=ftp发送方式
att_autoExport_sftpFormat=sftp发送方式
att_autoExport_ftpUrl=ftp服务器地址
att_autoExport_ftpPort=ftp服务器端口
att_autoExport_ftpTimeSet=ftp发送时间设置
att_autoExport_ftpParamSet=ftp参数设置
att_autoExport_ftpUsername=ftp用户名
att_autoExport_ftpPassword=ftp密码
att_autoExport_correctFtpParam=请正确填写ftp参数
att_autoExport_correctFtpTestParam=请测试连接,保证通讯正常
att_autoExport_inputFtpUrl=请输入服务器地址
att_autoExport_inputFtpPort=请输入服务器端口
att_autoExport_ftpSuccess=连接成功
att_autoExport_ftpFail=请检查参数设置是否有误
att_autoExport_validFtp=请输入合法的服务器地址
att_autoExport_validPort=请输入合法的服务器端口
att_autoExport_selectExcelTip=文件类型选择EXCEL，内容格式为所有字段！
#=====================================================================
#时间段
att_timeSlot_periodType=时段类型
att_timeSlot_normalTime=正常时间段
att_timeSlot_elasticTime=弹性时间段
att_timeSlot_startSignInTime=开始签到时间
att_timeSlot_toWorkTime=上班时间
att_timeSlot_endSignInTime=结束签到时间
att_timeSlot_allowLateMinutes=允许迟到分钟数
att_timeSlot_isMustSignIn=必须签到
att_timeSlot_startSignOffTime=开始签退时间
att_timeSlot_offWorkTime=下班时间
att_timeSlot_endSignOffTime=结束签退时间
att_timeSlot_allowEarlyMinutes=允许早退分钟数
att_timeSlot_isMustSignOff=必须签退
att_timeSlot_workingHours=工作时长（分钟）
att_timeSlot_isSegmentDeduction=是否段间扣除
att_timeSlot_startSegmentTime=开始段间时间
att_timeSlot_endSegmentTime=结束段间时间
att_timeSlot_interSegmentDeduction=段间扣除（分钟）
att_timeSlot_markWorkingDays=记为工作日数
att_timeSlot_isAdvanceCountOvertime=提前是否计加班
att_timeSlot_signInAdvanceTime=签到早于时间
att_timeSlot_isPostponeCountOvertime=延后是否计加班
att_timeSlot_signOutPosponeTime=签退晚于时间
att_timeSlot_isCountOvertime=是否计加班
att_timeSlot_timeSlotLong=工作时长必需符合规则定义的考勤区间：
att_timeSlot_alertStartSignInTime=开始签到时间要小于上班时间
att_timeSlot_alertEndSignInTime=上班时间要小于结束签到时间
att_timeSlot_alertStartSignInAndEndSignIn=结束签到时间要小于开始签退时间
att_timeSlot_alertStartSignOffTime=开始签退时间要小于下班时间
att_timeSlot_alertEndSignOffTime=下班时间要小于结束签退时间
att_timeSlot_alertStartUnequalEnd=开始段间时间不能等于结束段间时间
att_timeSlot_alertStartSegmentTime=开始段间时间要小于结束段间时间
att_timeSlot_alertStartAndEndTime=上班时间要小于开始段间时间
att_timeSlot_alertEndAndoffWorkTime=结束段间时间要小于下班时间
att_timeSlot_alertToWorkLesserAndEndTimeLesser=上班时间要小于开始段间时间并且结束段间时间要小于下班时间
att_timeSlot_alertLessSignInAdvanceTime=签到早于时间要小于上班时间
att_timeSlot_alertMoreSignInAdvanceTime=上班前签到记加班分钟数要小于上班前分钟数
att_timeSlot_alertMoreSignOutPosponeTime=下班后签到记加班分钟数要小于下班后分钟数
att_timeSlot_alertLessSignOutPosponeTime=签退晚于时间要大于下班时间
att_timeSlot_time=请输入正确时间格式
att_timeSlot_alertMarkWorkingDays=记为工作日数不能为空！
att_timeSlot_placeholderNo=建议以 T 开头，如 T01
att_timeSlot_placeholderName=建议以 T 开头或 时间段 结尾
att_timeSlot_beforeToWork=上班前
att_timeSlot_afterToWork=上班后
att_timeSlot_beforeOffWork=下班前
att_timeSlot_afterOffWork=下班后
att_timeSlot_minutesSignInValid=分钟内签到有效
att_timeSlot_toWork=上班
att_timeSlot_offWork=下班
att_timeSlot_minutesSignInAsOvertime=分钟前签到记加班
att_timeSlot_minutesSignOutAsOvertime=分钟后开始记加班
att_timeSlot_minOvertimeMinutes=最短加班分钟数
att_timeSlot_enableWorkingHours=是否启用工作时间
att_timeSlot_eidtTimeSlot=编辑时间段
att_timeSlot_browseBreakTime=浏览休息时间段
att_timeSlot_addBreakTime=添加休息时间段
att_timeSlot_enableFlexibleWork=启用弹性上班
att_timeSlot_advanceWorkMinutes=可提前上班
att_timeSlot_delayedWorkMinutes=可延后上班
att_timeSlot_advanceWorkMinutesValidMsg1=上班前的分钟数要大于可提前上班的分钟数
att_timeSlot_advanceWorkMinutesValidMsg2=可提前上班的分钟数要小于上班前的分钟数
att_timeSlot_advanceWorkMinutesValidMsg3=可提前上班的分钟数要小于等于上班前签到记加班的分钟数
att_timeSlot_advanceWorkMinutesValidMsg4=上班前签到记加班的分钟数要大于等于可提前上班的分钟数
att_timeSlot_delayedWorkMinutesValidMsg1=下班后的分钟数要大于可延后上班的分钟数
att_timeSlot_delayedWorkMinutesValidMsg2=可延后上班的分钟数要小于下班后的分钟数
att_timeSlot_delayedWorkMinutesValidMsg3=可延后上班的分钟数要小于等于下班后签退开始记加班的分钟数
att_timeSlot_delayedWorkMinutesValidMsg4=下班后签退开始记加班的分钟数要大于等于可延后上班的分钟数
att_timeSlot_allowLateMinutesValidMsg1=允许迟到分钟数要小于上班后的分钟数
att_timeSlot_allowLateMinutesValidMsg2=上班后的分钟数要大于允许迟到分钟数的分钟数
att_timeSlot_allowEarlyMinutesValidMsg1=允许早退分钟数要小于下班前的分钟数
att_timeSlot_allowEarlyMinutesValidMsg2=下班前的分钟要大于允许早退分钟数的分钟数
att_timeSlot_timeOverlap=休息时间重叠，请修改休息时间段！
att_timeSlot_atLeastOne=至少1条休息时间段！
att_timeSlot_mostThree=最多3条休息时间段！
att_timeSlot_canNotEqual=休息时间段开始时间不能等于结束时间！
att_timeSlot_shoudInWorkTime=请确保休息时间段在上班时间范围内！
att_timeSlot_repeatBreakTime=重复休息时间段！
att_timeSlot_toWorkLe=上班时间要小于所选的休息时间段的最小开始时间：
att_timeSlot_offWorkGe=下班时间要大于所选的休息时间段的最大结束时间：
att_timeSlot_crossDays_toWork=休息时间段的最小开始时间要在时间段内:
att_timeSlot_crossDays_offWork=休息时间段的最大结束时间要在时间段内:
att_timeSlot_allowLateMinutesRemark=从上班时间开始到允许迟到分钟数内打卡算正常上班卡
att_timeSlot_allowEarlyMinutesRemark=从下班时间开始提前签退在允许早退分钟数内，算正常下班卡
att_timeSlot_isSegmentDeductionRemark=是否扣除时间段内的休息时间段
att_timeSlot_attEnableFlexibleWorkRemark1=弹性上班不允许设置迟到、早退分钟数
att_timeSlot_afterToWorkRemark=上班后分钟数等于可延后上班分钟数
att_timeSlot_beforeOffWorkRemark=下班前分钟数等于可提前上班分钟数
att_timeSlot_attEnableFlexibleWorkRemark2=下班后的分钟数要大于或等于下班时间+延后上班时间
att_timeSlot_attEnableFlexibleWorkRemark3=可提前上班分钟数，必须小于等于上班N分钟记加班分钟数
att_timeSlot_attEnableFlexibleWorkRemark4=可延后上班分钟数，必须小于等于下班N分钟记加班分钟数
att_timeSlot_attBeforeToWorkAsOvertimeRemark=例：9点的班，上班60分钟前签到记加班，则8点前签到至8点算加班
att_timeSlot_attAfterOffWorkAsOvertimeRemark=例：18点下班，下班60分钟后签退记加班，则从19点开始到签退时间算加班
att_timeSlot_longTimeValidRemark=上班后的有效签卡时长与下班前的有效签卡时长，在时间段内不能重叠！
att_timeSlot_advanceWorkMinutesValidMsg5=下班前签到有效分钟数要大于可提前上班分钟数
att_timeSlot_advanceWorkMinutesValidMsg6=可提前上班分钟数要小于下班前签到有效分钟数
att_timeSlot_delayedWorkMinutesValidMsg5=上班后签到有效分钟数要大于可延后上班分钟数
att_timeSlot_delayedWorkMinutesValidMsg6=可延后上班分钟数要小于上班后签到有效分钟数
att_timeSlot_advanceWorkMinutesValidMsg7=上班前开始签到时间不能和前一天下班后结束签退时间重叠
att_timeSlot_delayedWorkMinutesValidMsg7=下班后结束签退时间不能和后一天上班前开始签到时间重叠
att_timeSlot_maxOvertimeMinutes=限制最大加班时长
#班次
att_shift_basicSet=基础设置
att_shift_advancedSet=高级设置
att_shift_type=班次类型
att_shift_name=班次名称
att_shift_regularShift=规律班次
att_shift_flexibleShift=弹性班次
att_shift_color=颜色
att_shift_periodicUnit=单位
att_shift_periodNumber=周期
att_shift_startDate=起始日期
att_shift_startDate_firstDay=周期起始日期
att_shift_isShiftWithinMonth=是否月内轮班
att_shift_attendanceMode=考勤方式
att_shift_shiftNormal=按班次正常打卡
att_shift_oneDayOneCard=一天内任打一次有效卡
att_shift_onlyBrushTime=只计算打卡时间
att_shift_notBrushCard=免打卡
att_shift_overtimeMode=加班方式
att_shift_autoCalc=电脑自动计算
att_shift_mustApply=加班必须申请
att_shift_mustOvertime=必须加班否则旷工
att_shift_timeSmaller=时长为较小者
att_shift_notOvertime=不算加班
att_shift_overtimeSign=加班标记
att_shift_normal=平时
att_shift_restday=休息日
att_shift_timeSlotDetail=时间段明细
att_shift_doubleDeleteTimeSlot=双击班次时段，可以删除该时段
att_shift_addTimeSlot=增加时间段
att_shift_cleanTimeSlot=清空时间段
att_shift_NO=第
att_shift_notAll=全不选
att_shift_notTime=时间段明细复选框不可勾选，表示时间段存在重叠。
att_shift_notExistTime=没有存在该时间段！
att_shift_cleanAllTimeSlot=确定清空所选班次的时间段？
att_shift_pleaseCheckBox=请先勾选左侧列表中与右侧当前显示时间一致的复选框
att_shift_pleaseUnit=请填写周期单位和周期数
att_shift_pleaseAllDetailTimeSlot=请选择时间段明细
att_shift_placeholderNo=建议以 S 开头，如 S01
att_shift_placeholderName=建议以 S 开头或 班次 结尾
att_shift_workType=工作类型
att_shift_normalWork=正常工作
att_shift_holidayOt=节假日加班
att_shift_attShiftStartDateRemark=例：周期的起始日期是22号，以三天为一个周期，则22号/23号/24号分别上A班/B班/C班，19号/20号/21号分别上A班/B班/C班，前后日期以此类推。
att_shift_isShiftWithinMonthRemark1=月内轮班，则周期只循环到每个月的最后一天，不跨月连续排班；
att_shift_isShiftWithinMonthRemark2=非月内轮班，则周期循环到每个月的最后一天时，如果一个周期未结束，则继续排到下个月，以此类推；
att_shift_workTypeRemark1=注意：工作类型选择为休息日加班时，遇节假日当天不计算考勤。
att_shift_workTypeRemark2=周末加班，加班标记默认为休息日且电脑自动计算加班，不需要加班申请，当天的工作时长记为加班时长，遇节假日不计算考勤；
att_shift_workTypeRemark3=节假日加班，加班标记默认为节假日且电脑自动计算加班，不需要加班申请，当天的工作时长记为加班时长；
att_shift_attendanceModeRemark1=除按班次正常打卡外，都不算提前或延后的加班时长，例：
att_shift_attendanceModeRemark2=1.免打卡或者一天内任打一次有效卡，不统计加班；
att_shift_attendanceModeRemark3=2.工作类型：正常工作，考勤方式：免打卡，则当天的排班时段视为有效工作时长；
att_shift_periodStartMode=周期起始类型
att_shift_periodStartModeByPeriod=周期起始日期
att_shift_periodStartModeBySch=排班开始日期
att_shift_addTimeSlotToShift=是否添加该班次的时间段
#=====================================================================
#分组
att_group_editGroup=为分组编辑人员
att_group_browseGroupPerson=浏览分组的人员
att_group_list=分组列表
att_group_placeholderNo=建议以 G 开头，如G1
att_group_placeholderName=建议以 G 开头或 分组 结尾
att_widget_deptHint=注：导入所选部门下所有人员
att_widget_searchType=条件查询
att_widget_noPerson=没有选择任何人
#分组排班
#部门排班
att_deptSch_existsDept=该部门存在部门排班，不允许删除该部门。
#人员排班
att_personSch_view=查看人员排班
#临时排班
att_schedule_type=排班类型
att_schedule_tempType=临时类型
att_schedule_normal=普通排班
att_schedule_intelligent=智能找班
att_tempSch_scheduleType=排班类型
att_tempSch_startDate=起始日期
att_tempSch_endDate=结束日期
att_tempSch_attendanceMode=考勤方式
att_tempSch_overtimeMode=加班方式
att_tempSch_overtimeRemark=加班标记
att_tempSch_existsDept=该部门存在部门临时排班，不允许删除该部门。
att_schedult_opAddTempSch=新增临时排班
att_schedule_cleanEndDate=清空结束时间
att_schedule_selectOne=普通排班 只能选择一个班次！
att_schedule_selectPerson=请先选择人员！
att_schedule_selectDept=请先选择部门！
att_schedule_selectGroup=请先选择分组！
att_schedule_selectOneGroup=只能选择一个分组！
att_schedule_arrange=请选择班次！
att_schedule_leave=假
att_schedule_trip=差
att_schedule_out=外
att_schedule_off=休
att_schedule_makeUpClass=补
att_schedule_class=调
att_schedule_holiday=节
att_schedule_offDetail=调休
att_schedule_makeUpClassDetail=补班
att_schedule_classDetail=调班
att_schedule_holidayDetail=节假日
att_schedule_noSchDetail=未排班
att_schedule_normalDetail=正常
att_schedule_normalSchInfo=班次居中：未跨天班次
att_schedule_multipleInterSchInfo=班次逗号分隔：多个跨天班次
att_schedule_inderSchFirstDayInfo=班次跨格向后偏移：跨天班次记为第一日
att_schedule_inderSchSecondDayInfo=班次跨格向前偏移：跨天班次记为第二日
att_schedule_timeConflict=与现有的排班时间段冲突，不允许保存！
#=====================================================================
att_excp_notExisetPerson=人员不存在！
att_excp_leavePerson=人员离职！
#补签单
att_sign_signTime=签卡时间
att_sign_signDate=签卡日期
#请假
att_leave_arilName=假类名称
att_leave_image=请假单照片
att_leave_imageShow=暂无图片
att_leave_imageType=错误提示:格式不正确,支持的图片格式为：JPEG、GIF、PNG!
att_leave_imageSize=错误提示:所选择的图片太大，图片大小最多支持4M!
att_leave_leaveLongDay=时长(天)
att_leave_leaveLongHour=时长(时)
att_leave_leaveLongMinute=时长(分)
att_leave_endNoLessAndEqualStart=结束时间不能小于等于开始时间
att_leave_typeNameNoExsists=假类名称不存在
att_leave_startNotNull=开始时间不能为空
att_leave_endNotNull=结束时间不能为空
att_leave_typeNameConflict=假种名称与考勤状态名称冲突
#出差
att_trip_tripLongDay=出差时长(天)
att_trip_tripLongMinute=出差时长(分)
att_trip_tripLongHour=出差时长(时)
#外出
att_out_outLongDay=外出时长(天)
att_out_outLongMinute=外出时长(分)
att_out_outLongHour=外出时长(时)
#加班
att_overtime_type=加班类型
att_overtime_normal=平时加班
att_overtime_rest=休息日加班
att_overtime_overtimeLong=加班时长(分)
att_overtime_overtimeHour=加班时长(时)
att_overtime_notice=加班申请时间不允许超过一天！
att_overtime_minutesNotice=加班申请时间不能小于最短加班时长！
#调休补班
att_adjust_type=调整类型
att_adjust_adjustDate=调整日期
att_adjust_shiftName=补班班次
att_adjust_selectClass=请选择补班的班次名称！
att_shift_notExistShiftWorkDate={1}在{0}排班且休息不允许进行补班申请!
att_adjust_shiftPeriodStartMode=补班选择的班次，如果起始日期是按排班，则默认取第0个
att_adjust_shiftNameNoNull=补班班次不能为空
att_adjust_shiftNameNoExsist=补班班次不存在
#调班
att_class_type=调班类型
att_class_sameTimeMoveShift=个人同日期调班
att_class_differenceTimeMoveShift=个人不同日期调班
att_class_twoPeopleMove=两人对调
att_class_moveDate=对调日期
att_class_shiftName=调整班次名称
att_class_moveShiftName=对调班次名称
att_class_movePersonPin=对调人员编号
att_class_movePersonName=对调人员姓名
att_class_movePersonLastName=对调人员姓氏
att_class_moveDeptName=对调人员部门名称
att_class_personPin=人员编号不能为空
att_class_shiftNameNoNull=调整班次不能为空
att_class_personPinNoNull=对调人员编号不能为空！
att_class_isNotExisetSwapPersonPin=对调人员编号不存在，请重新添加！
att_class_personNoSame=调整人员和对调人员不能相同，请重新添加！
att_class_outTime=调整日期和对调日期相隔不能超过一个月!
att_class_shiftNameNoExsist=调整班次不存在
att_class_swapPersonNoExisist=对调人员不存在
att_class_dateNoSame=个人不同日期调班,日期不能一样
#=====================================================================
#节点
att_node_name=节点
att_node_type=节点类型
att_node_leader=直属领导
att_node_leaderNode=直属领导节点
att_node_person=指定人
att_node_position=指定职位
att_node_choose=选择职位
att_node_personNoNull=人员不为空
att_node_posiitonNoNull=职位不能为空
att_node_placeholderNo=建议以 N 开头，如 N01
att_node_placeholderName=建议以职位或姓名开头，以节点结尾，如主管节点
att_node_searchPerson=输入查询条件
att_node_positionIsExist=该职位已经存在于节点数据中，请重新选择职位
#流程
att_flow_type=流程类型
att_flow_rule=流程规则
att_flow_rule0=小于等于1天
att_flow_rule1=大于1天，并小于等于3天
att_flow_rule2=大于3天，并小于等于7天
att_flow_rule3=大于7天
att_flow_node=审批节点
att_flow_start=开始流程
att_flow_end=结束流程
att_flow_addNode=添加节点
att_flow_placeholderNo=建议以 F 开头，如 F01
att_flow_placeholderName=建议类型开头，以流程结尾，如请假流程
att_flow_tips=注意：节点的审批顺序是从上到下的，选择后可以拖动排序。
#申请
att_apply_personPin=申请人员编号
att_apply_type=异常类型
att_apply_flowStatus=流程总体状态
att_apply_start=发起申请
att_apply_flowing=流程中
att_apply_pass=通过
att_apply_over=结束
att_apply_refuse=驳回
att_apply_revoke=撤销
att_apply_except=异常
att_apply_view=查看详情
att_apply_leaveTips=人员在该时间段内有请假申请！
att_apply_tripTips=人员在该时间段内有出差申请！
att_apply_outTips=人员在该时间段内有外出申请！
att_apply_overtimeTips=人员在该时间段内有加班申请！
att_apply_adjustTips=人员在该时间段内有调休补班申请！
att_apply_classTips=人员在该时间段内有调班申请！
#审批
att_approve_wait=待审批
att_approve_refuse=不通过
att_approve_reason=理由
att_approve_personPin=审批人员编号
att_approve_personName=审批人员姓名
att_approve_person=审批人员
att_approve_isPass=是否通过审批
att_approve_status=当前节点状态
att_approve_tips=该时间点已经存在流程中的记录，无法重复申请
att_approve_tips2=还未配置流程节点，请管理员配置
att_approve_offDayConflicts={0}在{1}未排班或者排班且休息不允许调休申请！
att_approve_shiftConflicts={0}在{1}已有排班且在上班日期中不允许补班申请！
att_approve_shiftNoSch={0}在{1}未排班不允许补班申请！
att_approve_classConflicts=未排班日期不允许进行调班申请！
att_approve_selectTime=选择时间后将根据规则判断流程
att_approve_withoutPermissionApproval=存在无权限审批的工作流，请核对！
#=====================================================================
#考勤计算
att_op_calculation=考勤计算
att_op_calculation_notice=已有考勤数据在后台计算，请稍后再试！
att_op_calculation_leave=包含离职人员
att_statistical_choosePersonOrDept=请选择部门或者人员！
att_statistical_sureCalculation=你确定要执行考勤计算？
att_statistical_filter=过滤条件准备完毕！
att_statistical_initData=初始化基础数据完毕！
att_statistical_exception=初始化异常数据完毕！
att_statistical_error=考勤计算失败！
att_statistical_begin=开始计算!
att_statistical_end=结束计算!
att_statistical_noticeTime=考勤可选时间范围：前两个月至当天！
att_statistical_remarkHoliday=节
att_statistical_remarkClass=班
att_statistical_remarkNoSch=未
att_statistical_remarkRest=休
#原始记录表
att_op_importAccRecord=导入门禁记录
att_op_importParkRecord=导入停车记录
att_op_importInsRecord=导入信息屏记录
att_op_importPidRecord=导入人证记录
att_op_importVmsRecord=导入视频记录
att_op_importUSBRecord=导入U盘记录
att_transaction_noAccModule=没有门禁模块！
att_transaction_noParkModule=没有停车模块！
att_transaction_noInsModule=没有信息屏模块！
att_transaction_noPidModule=没有人证模块！
att_transaction_exportRecord=导出原始记录
att_transaction_exportAttPhoto=导出考勤照片
att_transaction_fileIsTooLarge=导出的文件太大，请缩小日期范围
att_transaction_exportDate=导出日期
att_statistical_attDatetime=考勤时间
att_statistical_attPhoto=考勤照片
att_statistical_attDetail=考勤明细
att_statistical_acc=门禁设备
att_statistical_att=考勤设备
att_statistical_park=停车场设备
att_statistical_faceRecognition=人脸识别设备
att_statistical_app=手机设备
att_statistical_vms=视频设备
att_statistical_psg=通道设备
att_statistical_dataSources=数据来源
att_transaction_SyncRecord=同步考勤点记录
#日打卡详情表
att_statistical_dayCardDetail=打卡详情
att_statistical_cardDate=打卡日期
att_statistical_cardNumber=打卡次数
att_statistical_earliestTime=最早时间
att_statistical_latestTime=最晚时间
att_statistical_cardTime=打卡时间
#请假汇总表
att_statistical_leaveDetail=请假详情
#日明细报表
att_statistical_attDate=考勤日期
att_statistical_week=星期
att_statistical_shiftInfo=班次信息
att_statistical_shiftTimeData=上下班时间
att_statistical_cardValidData=打卡数据
att_statistical_cardValidCount=打卡次数
att_statistical_lateCount=迟到次数
att_statistical_lateMinute=迟到分钟数
att_statistical_earlyCount=早退次数
att_statistical_earlyMinute=早退分钟数
att_statistical_countData=次数数据
att_statistical_minuteData=分钟数数据
att_statistical_attendance_minute=出勤（分）
att_statistical_overtime_minute=加班（分）
att_statistical_unusual_minute=异常（分）
#月明细报表
att_monthdetail_should_hour=应该(时)
att_monthdetail_actual_hour=实际(时)
att_monthdetail_valid_hour=有效(时)
att_monthdetail_absent_hour=旷工(时)
att_monthdetail_leave_hour=请假(时)
att_monthdetail_trip_hour=出差(时)
att_monthdetail_out_hour=外出(时)
att_monthdetail_should_day=应该(日)
att_monthdetail_actual_day=实际(日)
att_monthdetail_valid_day=有效(日)
att_monthdetail_absent_day=旷工(日)
att_monthdetail_leave_day=请假(日)
att_monthdetail_trip_day=出差(日)
att_monthdetail_out_day=外出(日)
#月统计报表
att_statistical_late_minute=时长(分)
att_statistical_early_minute=时长(分)
#部门统计报表
#年度统计报表
att_statistical_should=应该
att_statistical_actual=实际
att_statistical_valid=有效
att_statistical_numberOfTimes=次数
att_statistical_usually=平时
att_statistical_rest=休日
att_statistical_holiday=节日
att_statistical_total=合计
att_statistical_month=统计月份
att_statistical_year=统计年份
att_statistical_attendance_hour=出勤(时)
att_statistical_attendance_day=出勤(日)
att_statistical_overtime_hour=加班(时)
att_statistical_unusual_hour=异常(时)
att_statistical_unusual_day=异常(日)
#考勤设备参数
att_deviceOption_query=查看设备参数
att_deviceOption_noOption=没有参数信息，请先获取设备参数
att_deviceOption_name=参数名称
att_deviceOption_value=参数值
att_deviceOption_UserCount=用户数
att_deviceOption_MaxUserCount=最大用户数
att_deviceOption_FaceCount=当前面部模板数
att_deviceOption_MaxFaceCount=最大面部模板数
att_deviceOption_FacePhotoCount=当前面部图像数
att_deviceOption_MaxFacePhotoCount=最大面部图像数
att_deviceOption_FingerCount=当前指纹模板数
att_deviceOption_MaxFingerCount=最大指纹模板数
att_deviceOption_FingerPhotoCount=当前指纹图像数
att_deviceOption_MaxFingerPhotoCount=最大指纹图像数
att_deviceOption_FvCount=当前指静脉模板数
att_deviceOption_MaxFvCount=最大指静脉模板数
att_deviceOption_FvPhotoCount=当前指静脉图像数
att_deviceOption_MaxFvPhotoCount=最大指静脉图像数
att_deviceOption_PvCount=当前手掌模板数
att_deviceOption_MaxPvCount=最大手掌模板数
att_deviceOption_PvPhotoCount=当前手掌图像数
att_deviceOption_MaxPvPhotoCount=最大手掌图像数
att_deviceOption_TransactionCount=当前记录数
att_deviceOption_MaxAttLogCount=最大记录数
att_deviceOption_UserPhotoCount=当前用户照片数
att_deviceOption_MaxUserPhotoCount=最大用户照片数
att_deviceOption_FaceVersion=人脸识别算法版本
att_deviceOption_FPVersion=指纹识别算法版本
att_deviceOption_FvVersion=指静脉识别算法版本
att_deviceOption_PvVersion=手掌识别算法版本
att_deviceOption_FWVersion=固件版本
att_deviceOption_PushVersion=Push版本
#=====================================================================
#API
att_api_areaCodeNotNull=区域编号不能为空
att_api_pinsNotNull=pins数据不允许为空
att_api_pinsOverSize=pins数据长度不允许超过500
att_api_areaNoExist=区域不存在
att_api_sign=补签
#=====================================================================
#休息时间段
att_leftMenu_breakTime=休息时间段
att_breakTime_startTime=开始时间
att_breakTime_endTime=结束时间
#=====================================================================
#导入U盘记录
att_import_uploadFileSuccess=上传文件成功，开始解析文件数据,请稍候...
att_import_resolutionComplete=解析完毕，开始更新数据库。
att_import_snNoExist=导入文件对应的考勤设备不存在，请重新选择文件。
att_import_fileName_msg=导入的文件名格式要求：设备序列号开头并用下划线"_"分隔，例如："3517171600001_attlog.dat"。
att_import_notSupportFormat=暂时不支持此种格式！
att_import_selectCorrectFile=请选择格式正确的文件！
att_import_fileFormat=文件格式
att_import_targetFile=目标文件
att_import_startRow=表头起始行数
att_import_startRowNote=数据格式第一行是导入数据，请核对文件后再导入。
att_import_delimiter=分隔符
#=====================================================================
#设备操作日志
att_device_op_log_op_type=操作代码
att_device_op_log_dev_sn=设备序列号
att_device_op_log_op_content=操作内容
att_device_op_log_operator_pin=操作人员编号
att_device_op_log_operator_name=操作人员姓名
att_device_op_log_op_time=操作时间
att_device_op_log_op_who_value=操作对象值
att_device_op_log_op_who_content=操作对象描述
att_device_op_log_op_value1=操作对象2
att_device_op_log_op_value_content1=操作对象描述2
att_device_op_log_op_value2=操作对象3
att_device_op_log_op_value_content2=操作对象描述3
att_device_op_log_op_value3=操作对象4
att_device_op_log_op_value_content3=操作对象描述4
#操作日志的操作类型
att_device_op_log_opType_0=开机
att_device_op_log_opType_1=关机
att_device_op_log_opType_2=验证失败
att_device_op_log_opType_3=报警
att_device_op_log_opType_4=进入菜单
att_device_op_log_opType_5=更改设置
att_device_op_log_opType_6=登记指纹
att_device_op_log_opType_7=登记密码
att_device_op_log_opType_8=登记HID卡
att_device_op_log_opType_9=删除用户
att_device_op_log_opType_10=删除指纹
att_device_op_log_opType_11=删除密码
att_device_op_log_opType_12=删除射频卡
att_device_op_log_opType_13=清除数据
att_device_op_log_opType_14=创建MF卡
att_device_op_log_opType_15=登记MF卡
att_device_op_log_opType_16=注册MF卡
att_device_op_log_opType_17=删除MF卡注册
att_device_op_log_opType_18=清除MF卡内容
att_device_op_log_opType_19=把登记数据移到卡中
att_device_op_log_opType_20=把卡中的数据复制到机器中
att_device_op_log_opType_21=设置时间
att_device_op_log_opType_22=出厂设置
att_device_op_log_opType_23=删除进出记录
att_device_op_log_opType_24=清除管理员权限
att_device_op_log_opType_25=修改门禁组设置
att_device_op_log_opType_26=修改用户门禁设置
att_device_op_log_opType_27=修改门禁时间段
att_device_op_log_opType_28=修改开锁组合设置
att_device_op_log_opType_29=开锁
att_device_op_log_opType_30=登记新用户
att_device_op_log_opType_31=更改指纹属性
att_device_op_log_opType_32=胁迫报警
att_device_op_log_opType_34=反潜
att_device_op_log_opType_35=删除考勤照片
att_device_op_log_opType_36=修改用户其他信息
att_device_op_log_opType_37=节假日
att_device_op_log_opType_38=还原数据
att_device_op_log_opType_39=备份数据
att_device_op_log_opType_40=U盘上传
att_device_op_log_opType_41=U盘下载
att_device_op_log_opType_42=U盘考勤记录加密
att_device_op_log_opType_43=U盘下载成功后删除记录
att_device_op_log_opType_53=出门开关
att_device_op_log_opType_54=门磁
att_device_op_log_opType_55=报警
att_device_op_log_opType_56=恢复参数
att_device_op_log_opType_68=注册用户照片
att_device_op_log_opType_69=修改用户照片
att_device_op_log_opType_70=修改用户姓名
att_device_op_log_opType_71=修改用户权限
att_device_op_log_opType_76=修改网络设置IP
att_device_op_log_opType_77=修改网络设置掩码
att_device_op_log_opType_78=修改网络设置网关
att_device_op_log_opType_79=修改网络设置DNS
att_device_op_log_opType_80=修改连接设置密码
att_device_op_log_opType_81=修改连接设置设备ID
att_device_op_log_opType_82=修改云服务器地址
att_device_op_log_opType_83=修改云服务器端口
att_device_op_log_opType_87=修改门禁记录设置
att_device_op_log_opType_88=修改人脸参数标志
att_device_op_log_opType_89=修改指纹参数标志
att_device_op_log_opType_90=修改指静脉参数标志
att_device_op_log_opType_91=修改掌纹参数标志
att_device_op_log_opType_92=u盘升级标志
att_device_op_log_opType_100=修改RF卡信息
att_device_op_log_opType_101=登记人脸
att_device_op_log_opType_102=修改人员权限
att_device_op_log_opType_103=删除人员权限
att_device_op_log_opType_104=增加人员权限
att_device_op_log_opType_105=删除门禁记录
att_device_op_log_opType_106=删除人脸
att_device_op_log_opType_107=删除人员照片
att_device_op_log_opType_108=修改参数
att_device_op_log_opType_109=选择WIFISSID
att_device_op_log_opType_110=proxy使能
att_device_op_log_opType_111=proxyip修改
att_device_op_log_opType_112=proxy端口修改
att_device_op_log_opType_113=修改人员密码
att_device_op_log_opType_114=修改人脸信息
att_device_op_log_opType_115=修改operator的密码
att_device_op_log_opType_116=恢复门禁设置
att_device_op_log_opType_117=operator密码输入错误
att_device_op_log_opType_118=operator密码锁定
att_device_op_log_opType_120=修改Legic卡数据长度
att_device_op_log_opType_121=登记指静脉
att_device_op_log_opType_122=修改指静脉
att_device_op_log_opType_123=删除指静脉
att_device_op_log_opType_124=登记掌纹
att_device_op_log_opType_125=修改掌纹
att_device_op_log_opType_126=删除掌纹
#操作对象描述
att_device_op_log_content_pin=用户工号：
att_device_op_log_content_alarm=报警：
att_device_op_log_content_alarm_reason=报警原因：
att_device_op_log_content_update_no=修改项序号：
att_device_op_log_content_update_value=修改项值：
att_device_op_log_content_finger_no=指纹序号：
att_device_op_log_content_finger_size=指纹模板长度：
#=====================================================================
#工作流
att_flowable_datetime_to=至
att_flowable_todomsg_leave=请假审批
att_flowable_todomsg_sign=补签审批
att_flowable_todomsg_overtime=加班审批
att_flowable_notifymsg_leave=请假知会
att_flowable_notifymsg_sign=补签知会
att_flowable_notifymsg_overtime=加班知会
att_flowable_shift=班次:
att_flowable_hour=小时
att_flowable_todomsg_trip=出差审批
att_flowable_notifymsg_trip=出差知会
att_flowable_todomsg_out=外出审批
att_flowable_notifymsg_out=外出知会
att_flow_apply=申请
att_flow_applyTime=申请时间
att_flow_approveTime=审批时间
att_flow_operateUser=审核人
att_flow_approve=审批
att_flow_approveComment=批注
att_flow_approvePass=审批结果
att_flow_status_processing=审批中
#=====================================================================
#biotime
att_h5_pers_personIdNull=员工id不能为空
att_h5_attPlaceNull=签到地点不能为空
att_h5_attAreaNull=考勤区域不能为空
att_h5_pers_personNoExist=员工编号不存在
att_h5_signRemarkNull=备注不能为空
att_h5_common_pageNull=分页参数错误
att_h5_taskIdNotNull=任务节点id不能为空
att_h5_auditResultNotNull=审批结果不能为空
att_h5_latLongitudeNull=经纬度不能为空
att_h5_pers_personIsNull=员工id不存在
att_h5_pers_personIsNotInArea=该人员未设置所处区域
att_h5_mapApiConnectionsError=地图api连接错误
att_h5_googleMap=谷歌地图
att_h5_gaodeMap=高德地图
att_h5_defaultMap=默认地图
att_h5_shiftTime=排班时间
att_h5_signTimes=补签时间
att_h5_enterKeyWords=请输入关键字：
att_h5_mapSet=考勤地图设置
att_h5_setMapApiAddress=设置地图参数
att_h5_MapSetWarning=切换地图会导致录入的移动端签到地址经纬度无法对应,请谨慎修改!
att_h5_mapSelect=地图选择
att_h5_persNoHire=该员工此时还未入职
att_slef_apiKey=API-KEY
att_self_apiJsKey=API-JS-KEY
att_self_noComputeAtt=当日考勤还未核算
att_self_noSignRecord=当日无打卡记录
att_self_imageUploadError=图片上传失败
att_self_attSignAddressAreaIsExist=该区域已有签到点
att_self_signRuleIsError=当前补签时间不在允许补签时间范围内
att_self_signAcrossDay=跨天排班不可补签！
att_self_todaySignIsExist=今日已有补签！
att_self_signSetting=补签设置
att_self_allowSign=允许补签:
att_self_allowSignSuffix=天，以内的考勤记录
att_self_onlyThisMonth=仅限本月
att_self_allowAcrossMonth=允许跨月
att_self_thisTimeNoSch=当前时间段没有排班！
att_self_revokeReason=撤销理由:
att_self_revokeHint=请输入20字以内的撤销原由,以便审核
att_self_persSelfLogin=员工自助登录
att_self_isOpenSelfLogin=是否启动员工自助登录入口
att_self_applyAndWorkTimeOverlap=申请时间和上班时间有重叠
att_apply_DurationIsZero=申请时长为0，不允许申请
att_sign_mapWarn=地图加载失败,请检查网络连接和地图KEY值
att_admin_applyWarn=操作失败，存在人员未排班或申请时间不在排班范围内！({0})
att_self_getPhotoFailed=图片不存在
att_self_view=查看
# 二维码
att_param_qrCodeUrl=二维码Url
att_param_qrCodeUrlHref=服务器地址:端口
att_param_appAttQrCode=移动端考勤二维码
att_param_timingFrequency=时间间隔：5-59分钟或1-24小时
att_sign_signTimeNotNull=补签时间不能为空
att_apply_overLastMonth=申请开始时间超过了上上个月
att_apply_withoutDetail=没有流程详情
att_flowable_noAuth=请使用超级管理员帐户查看
att_apply_overtimeOverMaxTimeLong=加班时长大于最大加班时长
# 考勤设置参数符号
att_other_arrive=√
att_other_late=迟
att_other_early=早
att_other_absent=旷
att_other_noSignIn=[缺
att_other_noSignOff=缺]
att_other_leave=假
att_other_overtime=加
att_other_off=调休
att_other_classes=补
att_other_trip=差
att_other_out=外
att_other_incomplete=缺
att_other_outcomplete=缺
# 服务器下发命令
att_devCmd_submitTime=提交时间
att_devCmd_returnedResult=返回结果
att_devCmd_returnTime=返回时间
att_devCmd_content=命令内容
att_devCmd_clearCmd=清空命令表
# 实时点名
att_realTime_selectDept=请选择部门
att_realTime_noSignPers=未签到人员
att_realTime_signPers=已签到
att_realTime_signMonitor=签到监控
att_realTime_signDateTime=签到时间
att_realTime_realTimeSet=实时点名设置
att_realTime_openRealTime=启用实时点名
att_realTime_rollCallEnd=实时点名结束
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=已排班
att_personSch_cycleSch=周期排班
att_personSch_cleanCycleSch=清除周期排班
att_personSch_cleanTempSch=清除临时排班
att_personSch_personCycleSch=人员周期排班
att_personSch_deptCycleSch=部门周期排班
att_personSch_groupCycleSch=分组周期排班
att_personSch_personTempSch=人员临时排班
att_personSch_deptTempSch=部门临时排班
att_personSch_groupTempSch=分组临时排班
att_personSch_checkGroupFirst=请勾选左侧的分组或者右侧列表的人员进行操作！
att_personSch_sureDeleteGroup=确认刪除{0}以及分组对应的排班吗？
att_personSch_sch=排班
att_personSch_delSch=刪除排班
#考勤计算
att_statistical_sureAllCalculate=确认对所有人员进行考勤计算吗？
#异常管理
att_exception_downTemplate=下载导入模版
att_exception_signImportTemplate=补签单导入模版
att_exception_leaveImportTemplate=请假导入模版
att_exception_overtimeImportTemplate=加班导入模版
att_exception_adjustImportTemplate=调休导入模版
att_exception_cellDefault=非必填字段
att_exception_cellRequired=必填字段
att_exception_cellDateTime=必填字段，时间格式为yyyy-MM-dd HH:mm:ss，如：2020-07-07 08:30:00
att_exception_cellLeaveTypeName=必填字段，如：'事假'、'婚假'、'产假'、'病假'、'年假'、'丧假'、'探亲假'、'哺乳假'、'出差'、'外出'等假种名称
att_exception_cellOvertimeSign=必填字段，如：'平时加班'、'休息日加班'、'节假日加班'
att_exception_cellAdjustType=必填字段，如：'调休'、'补班'
att_exception_cellAdjustDate=必填字段，时间格式为yyyy-MM-dd，如：2020-07-07
att_exception_cellShiftName=当调整类型为补班时，必填字段
att_exception_refuse=拒绝
att_exception_end=异常结束
att_exception_delete=删除
att_exception_stop=暂停
#时间段
att_timeSlot_normalTimeAdd=新增正常时间段
att_timeSlot_elasticTimeAdd=新增弹性时间段
#班次
att_shift_addRegularShift=新增规律班次
att_shift_addFlexibleShift=新增弹性班次
#参数设置
att_param_notLeaveSetting=非假类计算设置
att_param_smallestUnit=最小单位
att_param_workDay=工作日
att_param_roundingControl=舍入控制
att_param_abort=向下（舍弃）
att_param_rounding=四舍五入
att_param_carry=向上（进位）
att_param_reportSymbol=报表展示符号
att_param_convertCountValid=请输入数字且只允许一位小数
att_other_leaveThing=事
att_other_leaveMarriage=婚
att_other_leaveBirth=产
att_other_leaveSick=病
att_other_leaveAnnual=年
att_other_leaveFuneral=丧
att_other_leaveHome=探
att_other_leaveNursing=哺
att_other_leavetrip=差
att_other_leaveout=外
att_common_schAndRest=排班且休息
att_common_timeLongs=时长
att_personSch_checkDeptOrPersFirst=请勾选左侧的部门或者右侧列表的人员进行操作！
att_personSch_checkCalendarFirst=请先选中需要排班的日期！
att_personSch_cleanCheck=清空选中
att_personSch_delTimeSlot=清除选中时间段
att_personSch_repeatTimeSlotNoAdd=存在重复的时间段不进行添加！
att_personSch_showSchInfo=显示排班详情
att_personSch_sureToCycleSch=确认对{0}进行周期排班吗？
att_personSch_sureToTempSch=确认对{0}进行临时排班吗？
att_personSch_sureToCycleSchDeptOrGroup=确认对{0}下的所有人员进行周期排班吗？
att_personSch_sureToTempSchDeptOrGroup=确认对{0}下的所有人员进行临时排班吗？
att_personSch_sureCleanCycleSch=确认清除{0}从{1}到{2}的周期排班吗？
att_personSch_sureCleanTempSch=确认清除{0}从{1}到{2}的临时排班吗？
att_personSch_sureCleanCycleSchDeptOrGroup=确认清除{0}下的所有人员从{1}到{2}的周期排班吗？
att_personSch_sureCleanTempSchDeptOrGroup=确认清除{0}下的所有人员从{1}到{2}的临时排班吗？
att_personSch_today=今天
att_personSch_timeSoltName=时间段名称
att_personSch_export=导出人员排班
att_personSch_exportTemplate=下载人员临时排班模版
att_personSch_import=导入人员临时排班
att_personSch_tempSchTemplate=人员临时排班模版
att_personSch_tempSchTemplateTip=请选择要排班的开始时间与结束时间，下载该日期内的排班模版
att_personSch_opTip=操作说明
att_personSch_opTip1=1、可以用鼠标将时段拖到日历控件内的单个日期进行排班。
att_personSch_opTip2=2、可以在日历控件内，双击单个日期进行排班。
att_personSch_opTip3=3、可以在日历控件内，按住鼠标移动选择多个日期进行排班。
att_personSch_schRules=排班规则
att_personSch_schRules1=1、周期排班：相同日期后面覆盖前面的排班，不管有不交集。
att_personSch_schRules2=2、临时排班：相同日期有交集，后面覆盖前面的临时排班，如果没有交集则同时存在。
att_personSch_schRules3=3、周期与临时：相同日期有交集则临时覆盖周期，没交集则同时存在；如果周期排班类型是智能找班，则直接由临时排班覆盖周期。
att_personSch_schStatus=排班状态
#左侧菜单-排班管理
att_leftMenu_schDetails=排班详情
att_leftMenu_detailReport=考勤记录报表
att_leftMenu_signReport=补签详情表
att_leftMenu_leaveReport=请假报表
att_leftMenu_abnormal=出勤异常表
att_leftMenu_yearLeaveSumReport=年度请假汇总表
att_leave_maxFileCount=最多只能添加4张图片
#时间段
att_timeSlot_add=设置时间段
att_timeSlot_select=请选择时间段！
att_timeSlot_repeat=时间段“{0}”重复！
att_timeSlot_overlapping=时间段“{0}”与“{1}”的上下班时间重叠！
att_timeSlot_addFirst=请先设置时间段！
att_timeSlot_notEmpty=人员编号{0}对应的时间段不能为空！
att_timeSlot_notExist=人员编号{0}对应的时间段“{1}”不存在！
att_timeSlot_repeatEx=人员编号{0}对应的时间段“{1}”与“{2}”上下班时间重叠
att_timeSlot_importRepeat=人员编号{0}对应的时间段“{1}”重复
att_timeSlot_importNotPin=系统中不存在编号为{0}的人员！
att_timeSlot_elasticTimePeriod=人员编号{0}，不能导入弹性时间段“{1}”！
#导入
att_import_overData=当前导入条数为{0}，超过限制的30000条，请分批导入！
att_import_existIllegalType=导入的{0}存在非法类型！
#验证方式
att_verifyMode_0=自动识别
att_verifyMode_1=仅指纹
att_verifyMode_2=工号验证
att_verifyMode_3=仅密码
att_verifyMode_4=仅卡
att_verifyMode_5=指纹或密码
att_verifyMode_6=指纹或卡
att_verifyMode_7=卡或密码
att_verifyMode_8=工号加指纹
att_verifyMode_9=指纹加密码
att_verifyMode_10=卡加指纹
att_verifyMode_11=卡加密码
att_verifyMode_12=指纹加密码加卡
att_verifyMode_13=工号加指纹加密码
att_verifyMode_14=(工号加指纹)或(卡加指纹)
att_verifyMode_15=人脸
att_verifyMode_16=人脸加指纹
att_verifyMode_17=人脸加密码
att_verifyMode_18=人脸加卡
att_verifyMode_19=人脸加指纹加卡
att_verifyMode_20=人脸加指纹加密码
att_verifyMode_21=指静脉
att_verifyMode_22=指静脉加密码
att_verifyMode_23=指静脉加卡
att_verifyMode_24=指静脉加密码加卡
att_verifyMode_25=掌纹
att_verifyMode_26=掌纹加卡
att_verifyMode_27=掌纹加面部
att_verifyMode_28=掌纹加指纹
att_verifyMode_29=掌纹加指纹加面部
# 工作流
att_flow_schedule=审核进度
att_flow_schedulePass=（通过）
att_flow_scheduleNot=（未审批）
att_flow_scheduleReject=（驳回）
# 工作时长表
att_workTimeReport_total=工作时长总和
# 自动导出报表
att_autoExport_startEndTime=起止时间
# 年假
att_annualLeave_setting=年假结余设置
att_annualLeave_settingTip1=使用年假结余功能需要对每个人员设置入职时间；未设置入职时间时，人员年假结余表的剩余年假显示为空。
att_annualLeave_settingTip2=若当前日期大于清零发放日期，则本次修改内容次年生效；若当前日期小于清零发放日期，则到清零发放日期时，会进行清零并重新发放年假。
att_annualLeave_calculate=年假清零发放日期
att_annualLeave_workTimeCalculate=按工龄折算
att_annualLeave_rule=年假时长规则
att_annualLeave_ruleCountOver=已达到最大设置条数限制
att_annualLeave_years=工龄
att_annualLeave_eachYear=每年
att_annualLeave_have=有
att_annualLeave_days=天年假
att_annualLeave_totalDays=总年假
att_annualLeave_remainingDays=剩余年假
att_annualLeave_consecutive=年假规则设置必须为连续年份
# 年假结余表
att_annualLeave_report=年假结余表
att_annualLeave_validDate=有效期
att_annualLeave_useDays=使用{0}天
att_annualLeave_calculateDays=发放{0}天
att_annualLeave_notEnough={0}年假不足！
att_annualLeave_notValidDate={0}不在年假的有效范围内！
att_annualLeave_notDays={0}没有年假！
att_annualLeave_tip1=张三去年9月1号入职
att_annualLeave_tip2=年假结余设置
att_annualLeave_tip3=清零发放日期为每年1月1号；按上班比例四舍五入计算；工龄≤1时有3天年假，1年<工龄≤3年时，有5天年假
att_annualLeave_tip4=年假享有计算
att_annualLeave_tip5=去年09-01~12-31享有4/12x3=1.0天
att_annualLeave_tip6=今年01-01~12-31享有4.0天（今年01-01~08-31享有8/12x3=2.0天+今年09-01~12-31享有4/12x5≈2.0天）
# att SDC
att_sdc_name=视频设备
att_sdc_wxMsg_firstData=您好，您有一条考勤打卡通知
att_sdc_wxMsg_stateData=无感考勤打卡成功
att_sdc_wxMsg_remark=温馨提示：最终考勤结果以打卡详情页为准。
# 时间段
att_timeSlot_conflict=该在时间段与当天的其他时间段冲突
att_timeSlot_selectFirst=请选择时间段
# 事件中心
att_eventCenter_sign=考勤签到
#异常管理
att_exception_classImportTemplate=调班导入模版
att_exception_cellClassAdjustType=必填字段，如："{0}"、"{1}"、"{2}"
att_exception_swapDateDate=非必填字段，时间格式为yyyy-MM-dd，如：2020-07-07
#消息中心
att_message_leave=考勤{0}通知
att_message_leaveContent={0}提交了{1}，{2}时间为{3}~{4}
att_message_leaveTime=请假时间
att_message_overtime=考勤加班通知
att_message_overtimeContent={0}提交了加班，加班时间为{1}~{2}
att_message_overtimeTime=加班时间
att_message_sign=考勤补签通知
att_message_signContent={0}提交了补签，补签时间为{1}
att_message_adjust=考勤调休通知
att_message_adjustContent={0}提交了调休，调休日期为{1}
att_message_class=考勤调班通知
att_message_classContent=调班详情
att_message_classContent0={0}提交了调班，调班日期为{1}，班次为{2}
att_message_classContent1={0}提交了调班，调班日期为{1}，对调日期为{2}
att_message_classContent2={0}（{1}）与{2}（{3}）对调调班
#推送中心
att_pushCenter_transaction=考勤记录
# 时间段
att_timeSlot_workTimeNotEqual=上班时间不能等于下班时间
att_timeSlot_signTimeNotEqual=开始签到时间不能等于结束签退时间
# 北向接口A
att_api_notNull={0}不能为空！
att_api_startDateGeEndDate=开始时间不能大于等于结束时间！
att_api_leaveTypeNotExist=假种不存在！
att_api_imageLengthNot2000=图片地址长度不能超过2000！
# 20221230新增国际化
att_personSch_workTypeNotNull=人员编号{0}对应的工作类型不能为空！
att_personSch_workTypeNotExist=人员编号{0}对应的工作类型不存在！
att_annualLeave_recalculate=重新计算
# 20230530新增国际化
att_leftMenu_dailyReport=考勤日报表
att_leftMenu_overtimeReport=加班报表
att_leftMenu_lateReport=迟到报表
att_leftMenu_earlyReport=早退报表
att_leftMenu_absentReport=缺勤报表
att_leftMenu_monthReport=考勤月报表
att_leftMenu_monthWorkTimeReport=月工作时长表
att_leftMenu_monthCardReport=月打卡表
att_leftMenu_monthOvertimeReport=月加班表
att_leftMenu_overtimeSummaryReport=人员加班汇总表
att_leftMenu_deptOvertimeSummaryReport=部门加班汇总表
att_leftMenu_deptLeaveSummaryReport=部门请假汇总表
att_annualLeave_calculateDay=年假天数
att_annualLeave_adjustDay=调整天数
att_annualLeave_sureSelectDept=你确定要对所选部门执行{0}操作吗？
att_annualLeave_sureSelectPerson=你确定要对所选人员执行{0}操作吗？
att_annualLeave_calculateTip1=按工龄折算时：年假计算工龄精确到月份，如工龄为10年3个月，则取10年3个月计算；
att_annualLeave_calculateTip2=不按工龄折算时：年假计算工龄精确到年，如工龄为10年3个月，则取10年计算；
att_rule_isInCompleteTip=未签到或未签退记为不完整时优先级最高，则不计迟到、早退、旷工、有效
att_rule_absentTip=未签到或未签退记为旷工时，则旷工时长等于工作时长减迟到早退时长
att_timeSlot_elasticTip1=为0时，有效时长等于实际时长，无旷工
att_timeSlot_elasticTip2=如果实际时长大于工作时长，有效时长等于工作时长，无旷工
att_timeSlot_elasticTip3=如果实际时长小于工作时长，有效时长等于实际时长，旷工等于工作时长减实际时长
att_timeSlot_maxWorkingHours=工作时长不能大于
# 20231030新增国际化
att_customReport=考勤自定义报表
att_customReport_byDayDetail=按日明细
att_customReport_byPerson=按人员汇总
att_customReport_byDept=按部门汇总
att_customReport_queryMaxRange=查询时间范围最大不能超过四个月
# 20240630新增国际化
att_personSch_shiftWorkTypeTip1=1、工作类型为正常工作/休息日加班时，排班优先级低于节假日
att_personSch_shiftWorkTypeTip2=2、工作类型为节假日加班时，排班优先级高于节假日
att_personVerifyMode=人员验证方式
att_personVerifyMode_setting=验证方式设置
att_personSch_importCycSch=导入人员周期排班
att_personSch_cycSchTemplate=人员周期排班模版
att_personSch_exportCycSchTemplate=下载人员周期排班模版
att_personSch_scheduleTypeNotNull=班次类型不能为空或不存在！
att_personSch_shiftNotNull=班次不能为空！
att_personSch_shiftNotExist=班次不存在！
att_personSch_onlyAllowOneShift=普通排班只允许排一个班次！
att_shift_attShiftStartDateRemark2=周期起始日期所在的周为第一周；周期起始日期所在的月份为第一月。
#打卡状态
att_cardStatus_setting=考勤状态设置
att_cardStatus_name=名称
att_cardStatus_value=值
att_cardStatus_alias=别名
att_cardStatus_every_day=每天
att_cardStatus_by_week=按星期
att_cardStatus_autoState=自动切换
att_cardStatus_attState=考勤状态
att_cardStatus_signIn=签到
att_cardStatus_signOut=签退
att_cardStatus_out=外出
att_cardStatus_outReturn=外出返回
att_cardStatus_overtime_signIn=加班签到
att_cardStatus_overtime_signOut=加班签退
# 20241030新增国际化
att_leaveType_enableMaxDays=启用年度限制
att_leaveType_maxDays=年度限制(天)
att_leaveType_applyMaxDays=年度申请不能超过{0}天
att_param_overTimeSetting=加班等级设置
att_param_overTimeLevel=加班等级(时)
att_param_overTimeLevelEnable=是否启用加班等级计算
att_param_reportColor=报表展示颜色
# APP
att_app_signClientTip=该设备今天已被其他人签到
att_app_noSignAddress=未配置打卡范围，请联系管理员设置
att_app_notInSignAddress=未抵达签到点，无法签到
att_app_attendance=我的考勤
att_app_apply=考勤申请
att_app_approve=我的审核
# 20250530
att_node_leaderNodeExist=已存在直属领导审批节点
att_signAddress_init=初始化地图
att_signAddress_initTips=请输入地图密钥，并初始化地图进行地址选取