#系统名称 印尼语
att_systemName=Attendance System 1.0
#=====================================================================
#左侧菜单
att_module=Kehadiran
#左侧菜单-考勤设备
att_leftMenu_attendanceDevice=Manajemen Perangkat
att_leftMenu_device=Perangkat kehadiran
att_leftMenu_point=Point Kehadiran
att_leftMenu_sign_address=Alamat Masuk Seluler
att_leftMenu_adms_devCmd=Server mengeluarkan perintah
#左侧菜单-基础信息
att_leftMenu_basicInformation=Informasi dasar
att_leftMenu_rule=Aturan
att_leftMenu_base_rule=Aturan kehadiran
att_leftMenu_department_rule=Aturan Departemen
att_leftMenu_holiday=Liburan
att_leftMenu_leaveType=Tipe cuti
att_leftMenu_timingCalculation=Perhitungan waktu
att_leftMenu_autoExport=Laporkan dorongan
att_leftMenu_param=Pengaturan Parameter
#左侧菜单-班次管理
att_leftMenu_shiftManagement=Shift
att_leftMenu_timeSlot=Jadwal
att_leftMenu_shift=Shift
#左侧菜单-排班管理
att_leftMenu_scheduleManagement=Susunan acara
att_leftMenu_group=Grup
att_leftMenu_groupPerson=Grup
att_leftMenu_groupSch=Susunan acara grup
att_leftMenu_deptSch=Susunan acara department
att_leftMenu_personSch=Jadwal personil
att_leftMenu_tempSch=Sementara
att_leftMenu_nonSch=Personil tidak terjadwal
#左侧菜单-异常管理
att_leftMenu_exceptionManagement=Manajemen pengecualian kehadiran
att_leftMenu_sign=Kwitansi Tambah
att_leftMenu_leave=Pergi
att_leftMenu_trip=Perjalanan Bisnis
att_leftMenu_out=Keluar
att_leftMenu_overtime=Lembur
att_leftMenu_adjust=Sesuaikan dan Tambahkan
att_leftMenu_class=Sesuaikan Shift
#左侧菜单-统计报表
att_leftMenu_statisticalReport=Laporan Statistik Kehadiran
att_leftMenu_manualCalculation=Perhitungan Manual
att_leftMenu_transaction=Transaksi
att_leftMenu_dayCardDetailReport=Absen harian
att_leftMenu_leaveSummaryReport=Tinggalkan Ringkasan
att_leftMenu_dayDetailReport=Laporan Harian
att_leftMenu_monthDetailReport=Laporan Detail Bulanan
att_leftMenu_monthStatisticalReport=Laporan Statistik Staf Bulanan
att_leftMenu_deptStatisticalReport=Laporan Statistik Departemen Bulanan
att_leftMenu_yearStatisticalReport=Laporan Tahunan
att_leftMenu_attSignCallRollReport=Panggilan masuk masuk
att_leftMenu_workTimeReport=Laporan Waktu Kerja
#左侧菜单-设备操作日志
att_leftMenu_device_op_log=Log Operasi Perangkat
#左侧菜单-实时点名
att_leftMenu_realTime_callRoll=Gulung panggilan
#=====================================================================
#公共
att_common_person=Personil
att_common_pin=ID
att_common_group=Grup
att_common_dept=Departemen
att_common_symbol=Simbol
att_common_deptNo=Nomor Departemen
att_common_deptName=Nama Departemen
att_common_groupNo=Nomor Grup
att_common_groupName=Nama Grup
att_common_operateTime=Waktu Operasi
att_common_operationFailed=Operasi Gagal
att_common_id=ID
att_common_deptId=ID Departemen
att_common_groupId=ID grup
att_common_deviceId=ID perangkat
att_person_pin=jumlah personil
att_person_name=Nama
att_person_lastName=Nama belakang
att_person_internalCard=nomor kartu
att_person_attendanceMode=Mode waktu dan kehadiran
att_person_normalAttendance=Kehadiran normal
att_person_noPunchCard=Tidak ada kartu punch
att_common_attendance=Kehadiran
att_common_attendance_hour=Kehadiran (jam)
att_common_attendance_day=Kehadiran (hari)
att_common_late=Terlambat
att_common_early=Awal
att_common_overtime=Lembur
att_common_exception=Pengecualian
att_common_absent=Tidak ada
att_common_leave=Pergi
att_common_trip=Perjalanan Bisnis
att_common_out=Keluar
att_common_staff=Karyawan
att_common_superadmin=Pengguna super
att_common_msg=Konten SMS
att_common_min=Durasi Pesan Singkat (menit)
att_common_letterNumber=Hanya dapat memasukkan angka atau huruf!
att_common_relationDataCanNotDel=Data terkait tidak dapat dihapus.
att_common_relationDataCanNotEdit=Data terkait tidak dapat dimodifikasi.
att_common_needSelectOneArea=Silakan pilih satu wilayah!
att_common_neesSelectPerson=Silakan pilih seseorang!
att_common_nameNoSpace=Nama tidak boleh mengandung spasi!
att_common_digitsValid=Anda hanya dapat memasukkan angka yang tidak lebih dari dua desimal!
att_common_numValid=Masukkan nomor saja!
#=====================================================================
#工作面板
att_dashboard_worker=Pekerja
att_dashboard_today=Kehadiran Hari Ini
att_dashboard_todayCount=Statistik Segmentasi Kehadiran Hari Ini
att_dashboard_exceptionCount=Statistik Abnormal (bulan ini)
att_dashboard_lastWeek=Minggu Terakhir
att_dashboard_lastMonth=Bulan Terakhir
att_dashboard_perpsonNumber=Jumlah Personil
att_dashboard_actualNumber=Personil Aktual
att_dashboard_notArrivedNumber=Personil Absen
att_dashboard_attHour=Waktu Kerja
#区域
#设备
att_op_syncDev=Sinkronisasi data perangkat lunak ke perangkat
att_op_account=Memeriksa Data Kehadiran
att_op_check=Unggah Data lagi
att_op_deleteCmd=Hapus perintah Perangkat
att_op_dataSms=Pesan Publik
att_op_clearAttPic=Bersihkan foto kehadiran
att_op_clearAttLog=Bersihkan transaksi kehadiran
att_device_waitCmdCount=Perintah untuk dieksekusi
att_device_status=Aktifkan Status
att_device_register=Mesin register
att_device_isRegister=Perangkat Pendaftaran
att_device_existNotRegDevice=Peralatan mesin yang tidak terdaftar, data tidak dapat diperoleh!
att_device_fwVersion=Versi Firmware
att_device_transInterval=Refresh Durasi (Menit)
att_device_cmdCount=Jumlah maksimum perintah untuk berkomunikasi dengan server.
att_device_delay=Waktu catatan penyelidikan (detik)
att_device_timeZone=Jadwal
att_device_operationLog=Log Operasi
att_device_registeredFingerprint=Daftarkan Sidik Jari
att_device_registeredUser=Daftarkan Personil
att_device_fingerprintImage=Gambar Sidik Jari
att_device_editUser=Edit Personil
att_device_modifyFingerprint=Ubah Sidik Jari
att_device_faceRegistration=Pendaftaran Wajah
att_device_userPhotos=Foto Personil
att_device_attLog=Apakah akan mengunggah catatan kehadiran
att_device_operLog=Apakah akan mengunggah informasi Personel
att_device_attPhoto=Apakah akan mengunggah foto yang hadir
att_device_isOnLine=Status Online
att_device_InputPin=Masukkan nomor orang
att_device_getPin=Dapatkan data personil yang ditentukan
att_device_separatedPin=Beberapa nomor personil, dipisahkan dengan koma
att_device_authDevice=Perangkat resmi
att_device_disabled=Perangkat berikut dinonaktifkan dan tidak dapat dioperasikan!
att_device_autoAdd=Peralatan baru ditambahkan secara otomatis
att_device_receivePersonOnlyDb=Hanya menerima data personel yang ada dalam database
att_devMenu_control=Kontrol Perangkat
att_devMenu_viewOrGetInfo=Lihat dan dapatkan informasi
att_devMenu_clearData=Hapus data perangkat
att_device_disabledOrOffline=Perangkat tidak diaktifkan atau tidak online dan tidak dapat dioperasikan!
att_device_areaStatus=Status area perangkat
att_device_areaCommon=Wilayah normal
att_device_areaEmpty=Area kosong
att_device_isRegDev=Modifikasi zona waktu atau registrar mengharuskan perangkat untuk memulai ulang agar berlaku!
att_device_canUpgrade=Perangkat berikut dapat ditingkatkan
att_device_offline=Perangkat berikut sedang offline dan tidak dapat dioperasikan!
att_device_oldProtocol=Protokol lama
att_device_newProtocol=Protokol baru
att_device_noMoreTwenty=Paket peningkatan firmware perangkat protokol lama tidak boleh lebih dari 20 juta
att_device_transferFilesTip=Firmware berhasil terdeteksi, mentransfer file
att_op_clearAttPers=Bersihkan personel peralatan
#区域人员
att_op_forZoneAddPers=Pengaturan Personil Area
att_op_dataUserSms=Pesan Pribadi
att_op_syncPers=Sinkronisasi ulang ke perangkat
att_areaPerson_choiceArea=Silakan pilih area!
att_areaPerson_byAreaPerson=Menurut wilayah
att_areaPerson_setByAreaPerson=Ditetapkan oleh personel area
att_areaPerson_importBatchDel=Impor penghapusan massal
att_areaPerson_syncToDevSuccess=Operasi yang sukses! Harap tunggu perintah untuk dikirim.
att_areaPerson_personId=ID personil
att_areaPerson_areaId=ID area
att_area_existPerson=Ada orang di area ini!
att_areaPerson_notice1=Area atau personil tidak boleh kosong pada saat yang sama!
att_areaPerson_notice2=Tidak ada orang atau area yang ditanyakan!
att_areaPerson_notice3=Tidak ada perangkat yang ditemukan di bawah area ini!
att_areaPerson_addArea=Tambahkan zona
att_areaPerson_delArea=Hapus area
att_areaPerson_persNoExit=Orang tidak ada
att_areaPerson_importTip1=Pastikan orang yang diimpor sudah ada dalam modul personalia
att_areaPerson_importTip2=Importir batch tidak akan secara otomatis dikirim ke perangkat dan perlu disinkronkan secara manual
att_areaPerson_addAreaPerson=Tambahkan staf regional
att_areaPerson_delAreaPerson=Hapus personel area
att_areaPerson_importDelAreaPerson=Impor dan hapus personel area
att_areaPerson_importAreaPerson=Personil bidang impor
#考勤点
att_attPoint_name=Nama Titik Kehadiran
att_attPoint_list=Daftar Titik Kehadiran
att_attPoint_deviceModule=Modul Perangkat
att_attPoint_acc=Kontrol Akses
att_attPoint_park=Parkir
att_attPoint_ins=Kios Wajah
att_attPoint_pid=Sertifikat pribadi
att_attPoint_vms=Video
att_attPoint_psg=Lorong
att_attPoint_doorList=Daftar Pintu
att_attPoint_deviceList=Daftar Perangkat
att_attPoint_channelList=Daftar saluran
att_attPoint_gateList=Daftar gerbang
att_attPoint_recordTypeList=Tarik jenis rekaman
att_attPoint_door=Silakan pilih pintu yang sesuai.
att_attPoint_device=Silakan pilih perangkat yang sesuai.
att_attPoint_gate=Silakan pilih gerbang yang sesuai
att_attPoint_normalPassRecord=Catatan lulus normal
att_attPoint_verificationRecord=Catatan verifikasi
att_person_attSet=Pengaturan kehadiran
att_attPoint_point=Silakan pilih titik kehadiran.
att_attPoint_count=Poin lisensi kehadiran tidak memadai yang diotorisasi; operasi gagal!
att_attPoint_notSelect=Modul ini tidak dikonfigurasi
att_attPoint_accInsufficientPoints=Titik kehadiran yang tidak memadai untuk catatan kontrol akses!
att_attPoint_parkInsufficientPoints=Poin absensi yang tidak memadai untuk catatan parkir mobil !!
att_attPoint_insInsufficientPoints=Kios wajah ketika jumlah titik kehadiran tidak mencukupi！
att_attPoint_pidInsufficientPoints=Kartu tidak cukup untuk hadir!
att_attPoint_doorOrParkDeviceName=Nama pintu atau nama peralatan parkir
att_attPoint_vmsInsufficientPoints=Video saat titik kehadiran tidak mencukupi!
att_attPoint_psgInsufficientPoints=Saluran memiliki poin kehadiran yang tidak mencukupi!
att_attPoint_delDevFail=Menghapus perangkat gagal, perangkat telah digunakan sebagai waktu
att_attPoint_pullingRecord=Titik kehadiran mendapatkan catatan secara teratur, harap tunggu!
att_attPoint_lastTransactionTime=Waktu pengambilan data terakhir
att_attPoint_masterDevice=Perangkat Master
att_attPoint_channelName=Nama saluran
att_attPoint_cameraName=Nama kamera
att_attPoint_cameraIP=ip kamera
att_attPoint_channelIP=ip saluran
att_attPoint_gateNumber=Nomor gerbang
att_attPoint_gateName=Nama gerbang
#APP考勤签到地址
att_signAddress_address=Alamat
att_signAddress_longitude=Bujur
att_signAddress_latitude=Lintang
att_signAddress_range=Rentang Efektif
att_signAddress_rangeUnit=Unit ini Meter(m)
#=====================================================================
#规则
att_rule_baseRuleSet=Pengaturan Aturan Dasar
att_rule_countConvertSet=Pengaturan Perhitungan
att_rule_otherSet=Pengaturan Lainnya
att_rule_baseRuleSignIn=Aturan Check-In
att_rule_baseRuleSignOut=Aturan Check-Out
att_rule_earliestPrinciple=Aturan Paling Awal
att_rule_theLatestPrinciple=Aturan Terbaru
att_rule_principleOfProximity=Aturan Proximity
att_rule_baseRuleShortestMinutes=Periode waktu minimum harus lebih besar dari (minimum 10 menit)
att_rule_baseRuleLongestMinutes=Periode waktu maksimum harus kurang dari (maksimum 1440 menit)
att_rule_baseRuleLateAndEarly=Cuti Terlambat dan Awal Dihitung sebagai Absen
att_rule_baseRuleCountOvertime=Statistik lembur
att_rule_baseRuleFindSchSort=Cari Catatan Pergeseran
att_rule_groupGreaterThanDepartment=Grup-> Departemen
att_rule_departmentGreaterThanGroup=Departemen-> Grup
att_rule_baseRuleSmartFindClass=Aturan Shift Pencocokan Cerdas
att_rule_timeLongest=Durasi Kerja Terpanjang
att_rule_exceptionLeast=Abnormal Paling Rendah
att_rule_baseRuleCrossDay=Hasil perhitungan kehadiran untuk shift lintas hari
att_rule_firstDay=Hari Pertama
att_rule_secondDay=Hari Kedua
att_rule_baseRuleShortestOvertimeMinutes=Lembur terpendek tunggal (menit)
att_rule_baseRuleMaxOvertimeMinutes=Jam lembur maksimum (menit)
att_rule_baseRuleElasticCal=Perhitungan Durasi Fleksibel
att_rule_baseRuleTwoPunch=Waktu kumulatif untuk setiap dua pukulan
att_rule_baseRuleStartEnd=Perhitungan waktu punch kepala dan ekor
att_rule_countConvertHour=Aturan Konversi Jam
att_rule_formulaHour=Formula: Jam=Menit/60
att_rule_countConvertDay=Aturan Konversi Hari
att_rule_formulaDay=Formula ： Hari=Menit/Jumlah menit untuk bekerja per hari
att_rule_inFormulaShallPrevail=Ambil hasil yang dihitung dengan rumus sebagai standar;
att_rule_remainderHour=Sisanya lebih besar dari atau sama dengan
att_rule_oneHour=Dihitung sebagai jam;
att_rule_halfAnHour=Dihitung setengah jam, jika tidak dihiraukan;
att_rule_remainderDay=Quotient lebih besar dari atau sama dengan menit kerja
att_rule_oneDay=%, dihitung sebagai satu hari;
att_rule_halfAnDay=%, dihitung setengah hari, jika tidak diabaikan;
att_rule_countConvertAbsentDay=Aturan konversi absen hari
att_rule_markWorkingDays=Dihitung sebagai hari kerja
att_rule_countConvertDecimal=Digit pasti dari titik desimal
att_rule_otherSymbol=Pengaturan simbol hasil kehadiran dalam laporan
att_rule_arrive=Diharapkan/Sebenarnya
att_rule_noSignIn=Tidak Ada Check-In
att_rule_noSignOff=Tidak Ada Check-Out
att_rule_off=Sesuaikan Istirahat
att_rule_class=Tambahkan Kehadiran
att_rule_shortLessLong=Jadwal kehadiran tidak boleh lebih dari durasi jadwal kehadiran terlama.
att_rule_symbolsWarning=Anda harus mengonfigurasi simbol dalam laporan kehadiran!
att_rule_reportSettingSet=Pengaturan ekspor laporan
att_rule_shortDateFormat=Format tanggal
att_rule_shortTimeFormat=Format waktu
att_rule_baseRuleSignBreakTime=Tidak ada waktu istirahat
att_leftMenu_custom_rule=Aturan khusus
att_custom_rule_already_exist={0} Aturan khusus sudah ada!
att_add_group_custom_rule=Tambahkan aturan pengelompokan
att_custom_rule_type=Tipe aturan
att_rule_type_group=Aturan dalam kelompok
att_rule_type_dept=Aturan departemen
att_custom_rule_orgNames=Gunakan benda
att_rult_maxOverTimeType1=Tidak ada batasan
att_rult_maxOverTimeType2=Minggu ini
att_rult_maxOverTimeType3=Bulan ini
att_rule_countConvertDayRemark1=Contoh:Waktu kerja efektif adalah 500 menit, dan waktu kerja adalah 480 menit per hari,hasilnya adalah 500/480=1.04, dan desimal terakhir disimpan pada 1.0.
att_rule_countConvertDayRemark2=Contoh:Waktu kerja efektif adalah 500 menit, dan waktu kerja adalah 480 menit per hari,hasilnya adalah 500/480=1.04, 1.04>0.8.
att_rule_countConvertDayRemark3=Contoh:Waktu kerja efektif adalah 300 menit, dan waktu kerja adalah 480 menit per hari,hasilnya adalah 300/480=0.625, 0.2<0.625<0.8 selama setengah hari.
att_rule_countConvertDayRemark4=Tolok ukur konversi hari: Rekam sebagai hari kerja dalam periode waktu tidak berfungsi;
att_rule_countConvertDayRemark5=Ini dicatat sebagai jumlah hari kerja: terbatas pada perhitungan jumlah hari penyelesaian, dan selama ada penyelesaian pada setiap periode, lama penyelesaian dihitung berdasarkan jumlah hari kerja periode tersebut;
att_rule_baseRuleSmartFindRemark1=Waktu terlama: Menurut titik kartu pada hari itu, hitung jam kerja yang sesuai dengan setiap shift pada hari itu,dan temukan pergeseran waktu kerja terlama pada hari itu;
att_rule_baseRuleSmartFindRemark2=Abnormal minimum: Hitung jumlah waktu abnormal yang sesuai dengan setiap shift pada hari itu sesuai dengan titik kartu pada hari itu,dan temukan shift dengan jumlah kelainan paling sedikit pada hari itu untuk menghitung waktu kerja;
att_rule_baseRuleHourValidator=Menit penghakiman setengah jam tidak bisa lebih dari atau sama dengan 1 jam!
att_rule_baseRuleDayValidator=Masa penghakiman setengah hari tidak bisa lebih besar dari atau sama dengan masa penghakiman satu hari!
att_rule_overtimeWarning=Durasi lembur maksimum tidak boleh kurang dari durasi lembur minimum minimum tunggal!
att_rule_noSignInCountType=Tak terdaftar sebagai
att_rule_absent=absen
att_rule_earlyLeave=Pergi lebih awal
att_rule_noSignOffCountType=Tak ditanda tangani sebagai jalan keluar
att_rule_minutes=menit
att_rule_noSignInCountLateMinute=Tak tercatat telat 1 menit
att_rule_noSignOffCountEarlyMinute=Tak ditandai mundur lebih awal
att_rule_incomplete=Tidak lengkap
att_rule_noCheckInIncomplete=Tidak masuk sebagai tidak lengkap
att_rule_noCheckOutIncomplete=Tidak masuk kembali karena tidak lengkap
att_rule_lateMinuteWarning=Tidak terdaftar akan dicatat karena menit terlambat harus lebih besar dari 0 dan kurang dari periode kehadiran terlama
att_rule_earlyMinuteWarning=Tidak dicentang untuk menit keberangkatan awal harus lebih besar dari 0 dan kurang dari periode kehadiran terlama
att_rule_baseRuleNoSignInCountLateMinuteRemark=Ketika tidak check-in dihitung sebagai terlambat, jika tidak check-in, itu akan dianggap terlambat selama N menit
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=Ketika tidak penandatanganan dicatat sebagai keberangkatan awal, jika tidak penandatanganan, itu dicatat sebagai berangkat lebih awal selama N menit
#节假日
att_holiday_placeholderNo=Dianjurkan untuk memulai dengan H, seperti H01.
att_holiday_placeholderName=Disarankan untuk memberi nama dengan [Tahun]+[Nama Liburan] mis. [2017 Hari Buruh].
att_holiday_dayNumber=Jumlah Hari
att_holiday_validDate_msg=Liburan selama ini
#假种
att_leaveType_leaveThing=Cuti Kasual
att_leaveType_leaveMarriage=Cuti Menikah
att_leaveType_leaveBirth=Cuti Bersalin
att_leaveType_leaveSick=Cuti Sakit
att_leaveType_leaveAnnual=Cuti Tahunan
att_leaveType_leaveFuneral=Cuti Berkabung
att_leaveType_leaveHome=Home Leave
att_leaveType_leaveNursing=Cuti Menyusui
att_leaveType_isDeductWorkLong=Lepaskan jam kerja
att_leaveType_placeholderNo=Disarankan untuk memulai dengan L, seperti L1.
att_leaveType_placeholderName=Dianjurkan untuk mengakhiri dengan liburan, mis. Liburan Kereta.
#定时计算
att_timingcalc_timeCalcFrequency=Interval perhitungan
att_timingcalc_timeCalcInterval=Waktu Perhitungan Berwaktu
att_timingcalc_timeSet=Pengaturan Waktu Penghitungan Waktu
att_timingcalc_timeSelect=Silakan pilih waktu!
att_timingcalc_optionTip=Setidaknya satu perhitungan jadwal kehadiran harian yang valid harus dipertahankan
#自动导出报表
att_autoExport_reportType=Jenis Laporan
att_autoExport_fileType=Jenis File
att_autoExport_fileName=Nama File
att_autoExport_fileDateFormat=Format Tanggal
att_autoExport_fileTimeFormat=Format Waktu
att_autoExport_fileContentFormat=Format Konten
att_autoExport_fileContentFormatTxt=Contoh ： {deptName} 00 {personPin} 01 {personName} 02 {attDatetime} 03
att_autoExport_timeSendFrequency=Kirim Frekuensi
att_autoExport_timeSendInterval=Interval Pengiriman Waktu
att_autoExport_emailType=Jenis Penerima Email
att_autoExport_emailRecipients=Penerima Email
att_autoExport_emailAddress=Alamat Surat
att_autoExport_emailExample=Contoh: <EMAIL>,<EMAIL>
att_autoExport_emailSubject=Judul Surat
att_autoExport_emailContent=Badan Mail
att_autoExport_field=Bidang
att_autoExport_fieldName=Nama Bidang
att_autoExport_fieldCode=Nomor Bidang
att_autoExport_reportSet=Pengaturan Laporan
att_autoExport_timeSet=Pengaturan Waktu Pengiriman Email
att_autoExport_emailSet=Pengaturan Email
att_autoExport_emailSetAlert=Silakan masukkan alamat email Anda.
att_autoExport_emailTypeSet=Pengaturan Penerima
att_autoExport_byDay=Pada Hari
att_autoExport_byMonth=Menurut Bulan
att_autoExport_byPersonSet=Diatur oleh Personil
att_autoExport_byDeptSet=Diatur oleh Departemen
att_autoExport_byAreaSet=Diatur berdasarkan Area
att_autoExport_emailSubjectSet=Pengaturan Judul
att_autoExport_emailContentSet=Pengaturan Tubuh
att_autoExport_timePointAlert=Silakan pilih titik pengiriman yang benar.
att_autoExport_lastDayofMonth=Hari terakhir dalam sebulan
att_autoExport_firstDayofMonth=Hari pertama di bulan itu
att_autoExport_dayofMonthCheck=Tanggal Tertentu
att_autoExport_dayofMonthCheckAlert=Silakan pilih tanggal tertentu.
att_autoExport_chooseDeptAlert=Silakan pilih Departemen!
att_autoExport_sendFormatSet=Pengaturan Mode Kirim
att_autoExport_sendFormat=Mode Kirim
att_autoExport_mailFormat=Metode Pengiriman Kotak Surat
att_autoExport_ftpFormat=Metode Pengiriman FTP
att_autoExport_sftpFormat=Metode Pengiriman SFTP
att_autoExport_ftpUrl=Alamat Server FTP
att_autoExport_ftpPort=Port Server FTP
att_autoExport_ftpTimeSet=Pengaturan Waktu Kirim FTP
att_autoExport_ftpParamSet=Pengaturan Parameter FTP
att_autoExport_ftpUsername=FTP Nama Pengguna
att_autoExport_ftpPassword=Kata Sandi FTP
att_autoExport_correctFtpParam=Silakan isi parameter ftp dengan benar
att_autoExport_correctFtpTestParam=Silakan uji koneksi untuk memastikan komunikasi normal
att_autoExport_inputFtpUrl=Silakan masukkan alamat server
att_autoExport_inputFtpPort=Silakan masukkan port server
att_autoExport_ftpSuccess=Koneksi berhasil
att_autoExport_ftpFail=Periksa apakah pengaturan parameter salah.
att_autoExport_validFtp=Silakan masukkan alamat server yang valid
att_autoExport_validPort=Silakan masukkan port server yang valid
att_autoExport_selectExcelTip=Jenis file pilih EXCEL, format konten adalah semua bidang!
#=====================================================================
#时间段
att_timeSlot_periodType=Jenis Jadwal
att_timeSlot_normalTime=Jadwal Normal
att_timeSlot_elasticTime=Jadwal Fleksibel
att_timeSlot_startSignInTime=Waktu Mulai Masuk
att_timeSlot_toWorkTime=Waktu Check-In
att_timeSlot_endSignInTime=Waktu Akhir Waktu Masuk
att_timeSlot_allowLateMinutes=Izinkan Terlambat (menit)
att_timeSlot_isMustSignIn=Harus Masuk
att_timeSlot_startSignOffTime=Waktu Mulai Check-Out
att_timeSlot_offWorkTime=Waktu Keluar
att_timeSlot_endSignOffTime=Waktu Akhir Check-Out
att_timeSlot_allowEarlyMinutes=Izinkan Cuti Dini (menit)
att_timeSlot_isMustSignOff=Harus Keluar
att_timeSlot_workingHours=Waktu Kerja (menit)
att_timeSlot_isSegmentDeduction=Waktu Istirahat Pengurangan Otomatis
att_timeSlot_startSegmentTime=Waktu Mulai
att_timeSlot_endSegmentTime=Waktu Berakhir
att_timeSlot_interSegmentDeduction=Waktu Khusus (menit)
att_timeSlot_markWorkingDays=Hari Kerja
att_timeSlot_isAdvanceCountOvertime=OT Otomatis (Check-In Dini)
att_timeSlot_signInAdvanceTime=Waktu Akhir OT Otomatis (Check-In)
att_timeSlot_isPostponeCountOvertime=OT Otomatis (Keterlambatan Check-Out)
att_timeSlot_signOutPosponeTime=Waktu Mulai OT Otomatis (Check-Out)
att_timeSlot_isCountOvertime=Dihitung sebagai lembur
att_timeSlot_timeSlotLong=Rentang Jadwal adalah:
att_timeSlot_alertStartSignInTime=Waktu Mulai Check-In harus kurang dari waktu Check-In.
att_timeSlot_alertEndSignInTime=Waktu Akhir Check-In harus lebih besar dari waktu Check-In.
att_timeSlot_alertStartSignInAndEndSignIn=Waktu Mulai Check-Out harus kurang dari waktu Check-Out.
att_timeSlot_alertStartSignOffTime=Waktu mulai lembur tidak boleh kurang dari waktu check-out.
att_timeSlot_alertEndSignOffTime=Waktu mulai lembur tidak boleh lebih besar dari waktu check-out akhir.
att_timeSlot_alertStartUnequalEnd=Hari kerja tidak boleh lebih kecil dari 0.
att_timeSlot_alertStartSegmentTime=Waktu yang dikurangkan tidak boleh lebih kecil dari 0.
att_timeSlot_alertStartAndEndTime=Mulai waktu checkout tidak dapat menyamai waktu check-in akhir.
att_timeSlot_alertEndAndoffWorkTime=Jam tidak boleh lebih dari 23.
att_timeSlot_alertToWorkLesserAndEndTimeLesser=Menit tidak bisa lebih dari 59.
att_timeSlot_alertLessSignInAdvanceTime=Waktu Check-In Awal harus kurang dari waktu mulai bekerja
att_timeSlot_alertMoreSignInAdvanceTime=Jumlah menit lembur sebelum 'pergi bekerja' kurang dari jumlah menit sebelum 'bekerja'
att_timeSlot_alertMoreSignOutPosponeTime=menit kerja lembur postscript 'off work' kurang dari menit 'after work'
att_timeSlot_alertLessSignOutPosponeTime=Waktu Check-Out yang terlambat harus lebih besar daripada waktu akhir kerja
att_timeSlot_time=Silakan masukkan format waktu yang benar.
att_timeSlot_alertMarkWorkingDays=Ingatlah bahwa jumlah hari kerja tidak boleh kosong!
att_timeSlot_placeholderNo=Disarankan untuk memulai dengan T, seperti T01.
att_timeSlot_placeholderName=Disarankan untuk memulai dengan T atau diakhiri dengan jadwal.
att_timeSlot_beforeToWork=Sebelum berangkat kerja.
att_timeSlot_afterToWork=Setelah bekerja.
att_timeSlot_beforeOffWork=Setelah jam kerja
att_timeSlot_afterOffWork=Setelah bekerja.
att_timeSlot_minutesSignInValid=Tanda tangani dalam semenit
att_timeSlot_toWork=Kantor.
att_timeSlot_offWork=kerja
att_timeSlot_minutesSignInAsOvertime=Tanda tangan lembur menit lalu
att_timeSlot_minutesSignOutAsOvertime=Mulai menghitung lembur dalam menit
att_timeSlot_minOvertimeMinutes=Jumlah menit lembur terpendek
att_timeSlot_enableWorkingHours=Waktu kerja diaktifkan atau tidak
att_timeSlot_eidtTimeSlot=Sunting periode
att_timeSlot_browseBreakTime=Jelajahi waktu istirahat
att_timeSlot_addBreakTime=Tambah waktu istirahat
att_timeSlot_enableFlexibleWork=Aktifkan kerja yang fleksibel
att_timeSlot_advanceWorkMinutes=Dapat bekerja di muka
att_timeSlot_delayedWorkMinutes=Dapat menunda pekerjaan
att_timeSlot_advanceWorkMinutesValidMsg1=Jumlah menit sebelum bekerja lebih besar dari jumlah menit yang dapat dikerjakan sebelumnya
att_timeSlot_advanceWorkMinutesValidMsg2=Jumlah menit yang dapat dikerjakan di muka kurang dari jumlah menit sebelum di kantor
att_timeSlot_advanceWorkMinutesValidMsg3=Jumlah menit yang dapat dikerjakan sebelumnya kurang dari atau sama dengan jumlah menit sebelum masuk untuk bekerja lembur.
att_timeSlot_advanceWorkMinutesValidMsg4=Jumlah menit yang dapat sebelum masuk untuk bekerja lembur lebih besar dari atau sama dengan jumlah menit dikerjakan sebelumnya.
att_timeSlot_delayedWorkMinutesValidMsg1=Jumlah menit setelah setelah bekerja lebih besar dari jumlah menit yang dapat ditunda untuk bekerja.
att_timeSlot_delayedWorkMinutesValidMsg2=Jumlah menit untuk dapat ditunda untuk bekerja kurang dari jumlah menit setelah setelah bekerja
att_timeSlot_delayedWorkMinutesValidMsg3=Jumlah menit yang dapat dijadwalkan untuk bekerja kurang dari atau sama dengan jumlah menit setelah setelah bekerja, keluar dan mulai bekerja lembur
att_timeSlot_delayedWorkMinutesValidMsg4=Jumlah menit yang dapat setelah bekerja, keluar dan mulai bekerja lembur lebih besar dari atau sama dengan jumlah menit setelah dijadwalkan untuk bekerja
att_timeSlot_allowLateMinutesValidMsg1=Izinkan menit terlambat kurang dari jumlah menit setelah bekerja
att_timeSlot_allowLateMinutesValidMsg2=Jumlah menit setelah setelah bekerja lebih besar dari jumlah menit yang diizinkan menit terlambat
att_timeSlot_allowEarlyMinutesValidMsg1=Izinkan menit awal kurang dari menit sebelum bekerja
att_timeSlot_allowEarlyMinutesValidMsg2=Jumlah menit sebelum sebelum kerja lebih besar dari jumlah menit yang diizinkan menit tersisa lebih awal
att_timeSlot_timeOverlap=Waktu istirahat tumpang tindih, harap modifikasi periode waktu istirahat!
att_timeSlot_atLeastOne=Setidaknya 1 periode istirahat!
att_timeSlot_mostThree=Hingga 3 periode istirahat!
att_timeSlot_canNotEqual=Waktu mulai dari periode istirahat tidak dapat sama dengan waktu akhir!
att_timeSlot_shoudInWorkTime=Pastikan periode istirahat dalam jam kerja!
att_timeSlot_repeatBreakTime=Ulangi periode istirahat!
att_timeSlot_toWorkLe=Waktu kerja kurang dari waktu mulai minimum periode istirahat yang dipilih:
att_timeSlot_offWorkGe=Waktu tidak aktif lebih besar dari waktu akhir maksimum periode istirahat yang dipilih:
att_timeSlot_crossDays_toWork=Waktu mulai minimum untuk periode istirahat adalah dalam periode waktu:
att_timeSlot_crossDays_offWork=Waktu akhir maksimum periode istirahat adalah dalam periode waktu:
att_timeSlot_allowLateMinutesRemark=Dari kartu waktu kerja ke kartu menit terlambat diizinkan untuk menghitung kartu kerja normal
att_timeSlot_allowEarlyMinutesRemark=Mulai lebih awal dari waktu tidak bertugas dalam jumlah menit yang diizinkan untuk berangkat lebih awal, kartu tidak bertugas yang normal
att_timeSlot_isSegmentDeductionRemark=Menghapus periode istirahat dalam periode waktu
att_timeSlot_attEnableFlexibleWorkRemark1=Kerja fleksibel tidak diizinkan untuk mengatur jumlah keberangkatan awal, awal
att_timeSlot_afterToWorkRemark=After work menit sama dengan menit Deferred to work
att_timeSlot_beforeOffWorkRemark=Sebelum bekerja menit sama dengan bisa mulai bekerja menit
att_timeSlot_attEnableFlexibleWorkRemark2=Jumlah menit setelah setelah bekerja lebih besar dari atau sama dengan di luar jam + jam kerja yang tertunda
att_timeSlot_attEnableFlexibleWorkRemark3=Anda dapat bekerja di muka menit,harus kurang dari atau sama dengan Bekerja N menit untuk bekerja lembur menit
att_timeSlot_attEnableFlexibleWorkRemark4=Ditunda untuk bekerja menit,harus kurang dari atau sama dengan N menit libur kerja
att_timeSlot_attBeforeToWorkAsOvertimeRemark=Contoh:kelas 9:00, masuk kerja lembur 60 menit sebelum kerja,lalu check in sebelum jam 8 sampai 8 jam kerja lembur
att_timeSlot_attAfterOffWorkAsOvertimeRemark=Contoh:Setelah jam kerja 18 jam,setelah 60 menit kerja,tandatangani penarikan dan lembur kerja, kemudian mulai lembur dari pukul 19 hingga waktu check-out.
att_timeSlot_longTimeValidRemark=Waktu penandatanganan yang efektif setelah bekerja dan waktu penandatanganan yang efektif sebelum bekerja tidak tumpang tindih dalam periode waktu!
att_timeSlot_advanceWorkMinutesValidMsg5=Jumlah menit yang valid sebelum check-in lebih besar dari jumlah menit yang dapat dikerjakan sebelumnya
att_timeSlot_advanceWorkMinutesValidMsg6=Menit untuk bekerja di muka harus kurang dari menit yang valid untuk masuk sebelum bekerja
att_timeSlot_delayedWorkMinutesValidMsg5=Jumlah menit yang valid setelah check-in lebih besar dari jumlah menit yang dapat ditunda untuk bekerja
att_timeSlot_delayedWorkMinutesValidMsg6=Jumlah menit yang dapat ditunda untuk bekerja harus kurang dari menit yang valid setelah masuk
att_timeSlot_advanceWorkMinutesValidMsg7=Waktu check-in sebelum bekerja tidak boleh tumpang tindih dengan waktu check-out setelah bekerja sehari sebelumnya
att_timeSlot_delayedWorkMinutesValidMsg7=Waktu check-out setelah bekerja tidak boleh tumpang tindih dengan waktu check-in sebelum bekerja keesokan harinya
att_timeSlot_maxOvertimeMinutes=Batasi jam lembur maksimum
#班次
att_shift_basicSet=Jenis Jadwal
att_shift_advancedSet=Jadwalkan Nama
att_shift_type=Jenis Pergeseran
att_shift_name=Ubah Nama
att_shift_regularShift=Shift Biasa
att_shift_flexibleShift=Pergeseran Fleksibel
att_shift_color=Warna
att_shift_periodicUnit=Unit
att_shift_periodNumber=Siklus
att_shift_startDate=Tanggal Mulai
att_shift_startDate_firstDay=Siklus tanggal mulai
att_shift_isShiftWithinMonth=Siklus Pergeseran dalam satu Bulan
att_shift_attendanceMode=Mode Kehadiran
att_shift_shiftNormal=Punch Card Menurut Normal Shift
att_shift_oneDayOneCard=Punch sekali setiap saat dalam sehari
att_shift_onlyBrushTime=Hanya Hitung Waktu Kartu Punch
att_shift_notBrushCard=Punch Gratis
att_shift_overtimeMode=Mode Lembur
att_shift_autoCalc=Perhitungan Otomatis Komputer
att_shift_mustApply=Lembur Harus Berlaku
att_shift_mustOvertime=Harus bekerja lembur atau absen
att_shift_timeSmaller=Durasi Lebih Pendek Antara Perhitungan Otomatis dan Penerimaan Lembur
att_shift_notOvertime=Tidak Dihitung sebagai Lembur
att_shift_overtimeSign=Jenis Lembur
att_shift_normal=Hari Normal
att_shift_restday=Hari Istirahat
att_shift_timeSlotDetail=Detail Jadwal
att_shift_doubleDeleteTimeSlot=Klik dua kali periode shift; Anda dapat menghapus periode waktu
att_shift_addTimeSlot=Tambahkan Jadwal
att_shift_cleanTimeSlot=Kosongkan Jadwal
att_shift_NO=TIDAK
att_shift_notAll=Batalkan Semua Pilihan
att_shift_notTime=Jika kotak centang rincian waktu tidak dapat diperiksa, ini menunjukkan bahwa ada tumpang tindih dalam jadwal.
att_shift_notExistTime=Jadwal ini tidak ada.
att_shift_cleanAllTimeSlot=Apakah Anda yakin ingin menghapus jadwal untuk shift yang dipilih?
att_shift_pleaseCheckBox=Silakan pilih kotak centang di sisi kiri yang sama dengan waktu tampilan saat ini di sisi kanan.
att_shift_pleaseUnit=Silakan isi unit siklus dan jumlah siklus.
att_shift_pleaseAllDetailTimeSlot=Silakan pilih detail jadwal waktu.
att_shift_placeholderNo=Disarankan untuk memulai dengan S, seperti S0.
att_shift_placeholderName=Disarankan untuk memulai dengan S atau diakhiri dengan shift.
att_shift_workType=Jenis pekerjaan
att_shift_normalWork=Pekerjaan normal
att_shift_holidayOt=lembur di hari libur
att_shift_attShiftStartDateRemark=Contoh: Tanggal awal siklus adalah No. 22, dengan jangka waktu tiga hari, maka No. 22/23/24 berada di Kelas A / B / C, dan No. 19/20/21 di Kelas A. / B class / C class, sebelum dan sesudah tanggal dan sebagainya.
att_shift_isShiftWithinMonthRemark1=Bergeser dalam satu bulan, siklus hanya siklus ke hari terakhir setiap bulan, tidak dijadwalkan secara berurutan sepanjang bulan;
att_shift_isShiftWithinMonthRemark2=Pergeseran non-bulan, siklus didaur ulang ke hari terakhir setiap bulan, jika satu siklus belum berakhir, lanjutkan ke bulan berikutnya, dan seterusnya;
att_shift_workTypeRemark1=Catatan: Jika jenis pekerjaan dipilih sebagai jam lembur pada hari istirahat, kehadiran tidak akan dihitung pada hari libur.
att_shift_workTypeRemark2=Lembur di akhir pekan, tanda lembur default untuk hari istirahat dan komputer secara otomatis menghitung lembur. Tidak ada aplikasi lembur yang diperlukan. Jam kerja dalam sehari dicatat sebagai jam lembur, dan kehadiran tidak dihitung selama liburan.
att_shift_workTypeRemark3=lembur di hari libur, tanda lembur default untuk hari libur dan komputer secara otomatis menghitung lembur, tidak ada aplikasi lembur yang diperlukan, dan jam kerja hari itu dicatat sebagai jam lembur;
att_shift_attendanceModeRemark1=Kecuali untuk biasanya digesek oleh shift, itu tidak dianggap sebagai lembur awal atau lembur, misalnya:
att_shift_attendanceModeRemark2=1.Tidak diperlukan check-in atau kartu yang valid digunakan sekali sehari, tidak ada lembur yang dihitung;
att_shift_attendanceModeRemark3=2.Jenis pekerjaan: kerja normal, mode kehadiran: gesek kartu gratis, maka waktu shift hari dianggap sebagai waktu kerja yang efektif;
att_shift_PeriodStartMode=jenis mulai periode
att_shift_PeriodStartModeByPeriod=tanggal mulai periode
att_shift_PeriodStartModeBySch=Ubah tanggal mulai
att_shift_addTimeSlotToShift=Apakah akan menambahkan slot waktu untuk shift ini
#=====================================================================
#分组
att_group_editGroup=Edit Personil untuk Grup
att_group_browseGroupPerson=Jelajahi Personil Grup
att_group_list=Daftar Grup
att_group_placeholderNo=Disarankan untuk memulai dengan G, seperti G1
att_group_placeholderName=Disarankan untuk memulai dengan G atau diakhiri dengan grup.
att_widget_deptHint=Catatan: Impor semua personil di bawah departemen yang dipilih
att_widget_searchType=Permintaan bersyarat
att_widget_noPerson=Tidak memilih siapa pun
#分组排班
#部门排班
att_deptSch_existsDept=Ada perubahan departemen di departemen dan tidak diizinkan untuk menghapus departemen.
#人员排班
att_personSch_view=Lihat penjadwalan staf
#临时排班
att_schedule_type=Jenis Jadwal
att_schedule_tempType=Jenis Sementara
att_schedule_normal=Jadwal Normal
att_schedule_intelligent=Penemuan kelas cerdas
att_tempSch_scheduleType=Jenis penjadwalan
att_tempSch_startDate=Tanggal mulai
att_tempSch_endDate=Tanggal akhir
att_tempSch_attendanceMode=Metode kehadiran
att_tempSch_overtimeMode=Mode lembur
att_tempSch_overtimeRemark=Tanda lembur
att_tempSch_existsDept=Ada pergeseran sementara departemen di departemen, dan tidak diizinkan untuk menghapus departemen.
att_schedult_opAddTempSch=Pergeseran Sementara Baru
att_schedule_cleanEndDate=Empty End time
att_schedule_selectOne=Jadwal normal hanya dapat memilih satu shift!
att_schedule_selectPerson=Silakan pilih personil terlebih dahulu!
att_schedule_selectDept=Silakan pilih departemen terlebih dahulu!
att_schedule_selectGroup=Silakan pilih grup terlebih dahulu!
att_schedule_selectOneGroup=Hanya satu grup yang dapat dipilih!
att_schedule_arrange=Silakan pilih shift!
att_schedule_leave=Pergi
att_schedule_trip=Perjalanan
att_schedule_out=Keluar
att_schedule_off=Istirahat
att_schedule_makeUpClass=Tambah
att_schedule_class=Sesuaikan
att_schedule_holiday=Liburan
att_schedule_offDetail=Sesuaikan Istirahat
att_schedule_makeUpClassDetail=Tambahkan Kehadiran
att_schedule_classDetail=Sesuaikan Shift
att_schedule_holidayDetail=Liburan
att_schedule_noSchDetail=Tidak dijadwalkan
att_schedule_normalDetail=Normal
att_schedule_normalSchInfo=Shift Center: Tidak Ada Cross-day Shift
att_schedule_multipleInterSchInfo=Bergeser dipisah koma: bergiliran banyak lintas hari
att_schedule_inderSchFirstDayInfo=Perpindahan mundur kisi mundur: Pergeseran lintas-hari dicatat sebagai hari pertama
att_schedule_inderSchSecondDayInfo=Bergeser melintasi offset maju kotak: Pergeseran lintas hari dicatat sebagai hari kedua
att_schedule_timeConflict=Konflik dengan periode waktu shift yang ada, tidak diizinkan untuk menyimpan!
#=====================================================================
att_excp_notExisetPerson=Orang tidak ada!
att_excp_leavePerson=Cuti staf!
#补签单
att_sign_signTime=Waktu Pukulan
att_sign_signDate=Tanggal Pukulan
#请假
att_leave_arilName=Jenis Cuti
att_leave_image=Tinggalkan Foto Permintaan
att_leave_imageShow=Tidak Ada Gambar
att_leave_imageType=Tip Salah: Format gambar tidak benar, mendukung format: JPEG, GIF, PNG!
att_leave_imageSize=Tip Salah: Gambar yang dipilih terlalu besar, ukuran maksimum gambar adalah 4MB!
att_leave_leaveLongDay=Durasi (hari)
att_leave_leaveLongHour=Durasi (jam)
att_leave_leaveLongMinute=Durasi (menit)
att_leave_endNoLessAndEqualStart=Waktu akhir tidak boleh kurang dari atau sama dengan waktu mulai
att_leave_typeNameNoExsists=Nama kelas palsu tidak ada
att_leave_startNotNull=Waktu mulai tidak boleh kosong
att_leave_endNotNull=Waktu akhir tidak boleh kosong
att_leave_typeNameConflict=Nama tipe palsu bertentangan dengan nama status kehadiran
#出差
att_trip_tripLongDay=Durasi Perjalanan(hari)
att_trip_tripLongMinute=Durasi Perjalanan(menit)
att_trip_tripLongHour=Waktu perjalanan (jam)
#外出
att_out_outLongDay=Durasi Keluar(hari)
att_out_outLongMinute=Durasi Keluar(menit)
att_out_outLongHour=Waktu yang dihabiskan (waktu)
#加班
att_overtime_type=Jenis PL
att_overtime_normal=OT normal
att_overtime_rest=PL Akhir Pekan
att_overtime_overtimeLong=Durasi Lembur(menit)
att_overtime_overtimeHour=Jam lembur (jam)
att_overtime_notice=Waktu aplikasi lembur tidak diperbolehkan selama lebih dari satu hari!
att_overtime_minutesNotice=Waktu aplikasi lembur tidak boleh kurang dari waktu lembur minimum!
#调休补班
att_adjust_type=Sesuaikan Jenis
att_adjust_adjustDate=Sesuaikan Tanggal
att_adjust_shiftName=Tambahkan Pergeseran Kehadiran
att_adjust_selectClass=Silakan pilih nama shift yang perlu ditambahkan.
att_shift_notExistShiftWorkDate={1} penjadwalan shift pada {0} dan sisanya tidak diizinkan untuk mengajukan perubahan make-up!
att_adjust_shiftPeriodStartMode=Shift yang dipilih untuk make-up shift, jika tanggal mulai adalah shift, defaultnya adalah 0
att_adjust_shiftNameNoNull=Pergeseran make-up tidak boleh kosong
att_adjust_shiftNameNoExsist=Pergeseran make-up tidak ada
#调班
att_class_type=Sesuaikan Jenis
att_class_sameTimeMoveShift=Sesuaikan shift pribadi di hari yang sama
att_class_differenceTimeMoveShift=Sesuaikan shift pribadi di hari lain
att_class_twoPeopleMove=Pertukaran dua orang
att_class_moveDate=Sesuaikan Tanggal
att_class_shiftName=Nama Jadwal Asli
att_class_moveShiftName=Shift yang baru disesuaikan tidak boleh kosong.
att_class_movePersonPin=Sesuaikan Personil ID
att_class_movePersonName=Sesuaikan Nama Personel
att_class_movePersonLastName=Sesuaikan Nama Belakang Personil
att_class_moveDeptName=Sesuaikan Nama Departemen
att_class_personPin=ID Personel
att_class_shiftNameNoNull=Shift yang baru disesuaikan tidak boleh kosong.
att_class_personPinNoNull=ID Personil orang baru tidak boleh kosong!
att_class_isNotExisetSwapPersonPin=Penyesuaian orang baru tidak ada, harap tambahkan kembali!
att_class_personNoSame=Anda tidak dapat menyesuaikan untuk orang yang sama, silakan coba lagi.
att_class_outTime=Tanggal penyesuaian dan tanggal transfer tidak boleh lebih dari satu bulan！
att_class_shiftNameNoExsist=Pergeseran penyesuaian tidak ada
att_class_swapPersonNoExisist=Matchmaker tidak ada
att_class_dateNoSame=Individu ditransfer pada tanggal yang berbeda, tanggal tidak bisa sama
#=====================================================================
#节点
att_node_name=Node
att_node_type=Jenis Node
att_node_leader=Pemimpin Langsung
att_node_leaderNode=Node Pemimpin Langsung
att_node_person=Orang Yang Ditunjuk
att_node_position=Tetapkan Posisi
att_node_choose=Pilih Posisi
att_node_personNoNull=Personil tidak kosong
att_node_posiitonNoNull=Posisi tidak boleh kosong
att_node_placeholderNo=Disarankan untuk memulai dengan N, seperti N01.
att_node_placeholderName=Disarankan untuk memulai dengan posisi atau nama, diakhiri dengan simpul, seperti Manajer Node.
att_node_searchPerson=Masukan Kriteria Pencarian
att_node_positionIsExist=Posisi sudah ada dalam data node, silakan pilih posisi lagi.
#流程
att_flow_type=Jenis Aliran
att_flow_rule=Aturan Aliran
att_flow_rule0=Kurang dari atau sama dengan 1 hari
att_flow_rule1=Lebih dari 1 hari dan kurang dari atau sama dengan 3 hari
att_flow_rule2=Lebih dari 3 hari dan kurang dari atau sama dengan 7 hari
att_flow_rule3=Lebih dari 7 hari
att_flow_node=Node Persetujuan
att_flow_start=Mulai Aliran
att_flow_end=Alur Akhir
att_flow_addNode=Tambahkan Node
att_flow_placeholderNo=Disarankan untuk memulai dengan F, seperti F01.
att_flow_placeholderName=Dianjurkan untuk memulai dengan tipe, diakhiri dengan aliran, mis. Tinggalkan Aliran.
att_flow_tips=Catatan: Urutan persetujuan node adalah dari atas ke bawah, dan Anda dapat menyeret pengurutan setelah memilih.
#申请
att_apply_personPin=ID Personil Pelamar
att_apply_type=Jenis Pengecualian
att_apply_flowStatus=Alirkan Status Keseluruhan
att_apply_start=Memulai aplikasi
att_apply_flowing=Tertunda
att_apply_pass=Lulus
att_apply_over=Akhir
att_apply_refuse=Ditolak
att_apply_revoke=Batalkan
att_apply_except=Pengecualian
att_apply_view=Lihat Detail
att_apply_leaveTips=Orang tersebut memiliki permintaan cuti selama periode waktu ini!
att_apply_tripTips=Personil memiliki aplikasi perjalanan bisnis selama periode waktu ini!
att_apply_outTips=Personil telah mendaftar keluar selama periode waktu ini!
att_apply_overtimeTips=Personil memiliki aplikasi lembur selama periode waktu ini!
att_apply_adjustTips=Selama periode waktu ini, personel harus mendaftar untuk latihan!
att_apply_classTips=Personil memiliki aplikasi shift selama periode waktu ini!
#审批
att_approve_wait=Persetujuan Menunggu Keputusan
att_approve_refuse=Tidak Lulus
att_approve_reason=Alasan
att_approve_personPin=Persetujuan ID
att_approve_personName=Nama Penerima
att_approve_person=Setuju
att_approve_isPass=Apakah akan menyetujui?
att_approve_status=Status Node Saat Ini
att_approve_tips=Titik waktu sudah ada dalam aliran dan tidak dapat diulang.
att_approve_tips2=Aliran node belum dikonfigurasikan, silakan hubungi administrator untuk konfigurasi.
att_approve_offDayConflicts={0} tidak dijadwalkan atau dijadwalkan pada {1} dan sisanya tidak diperbolehkan.
att_approve_shiftConflicts={0} sudah memiliki pergeseran dalam {1} dan tidak memungkinkan untuk mengajukan shift pada hari kerja!
att_approve_shiftNoSch={0} Tidak ada aplikasi shift yang diizinkan pada {1} tanpa penjadwalan!
att_approve_classConflicts=Aplikasi shift tidak diizinkan pada tanggal yang tidak dijadwalkan
att_approve_selectTime=Waktu pemilihan akan menentukan proses sesuai dengan aturan
att_approve_withoutPermissionApproval=Ada alur kerja tanpa izin untuk disetujui, harap periksa!
#=====================================================================
#考勤计算
att_op_calculation=Perhitungan Kehadiran
att_op_calculation_notice=Data kehadiran telah dihitung di latar belakang, silakan coba lagi nanti!
att_op_calculation_leave=Termasuk personil yang mengundurkan diri
att_statistical_choosePersonOrDept=Silakan pilih Departemen atau Personil!
att_statistical_sureCalculation=Apakah Anda yakin ingin melakukan perhitungan kehadiran?
att_statistical_filter=Kondisi filtrasi siap!
att_statistical_initData=Inisialisasi database selesai!
att_statistical_exception=Inisialisasi data pengecualian lengkap!
att_statistical_error=Perhitungan absensi gagal!
att_statistical_begin=mulai menghitung!
att_statistical_end=Akhiri perhitungan!
att_statistical_noticeTime=Kehadiran rentang waktu opsional: dua bulan pertama hingga hari itu!
att_statistical_remarkHoliday=H
att_statistical_remarkClass=C
att_statistical_remarkNoSch=NS
att_statistical_remarkRest=R
#原始记录表
att_op_importAccRecord=Impor catatan kontrol akses
att_op_importParkRecord=Impor catatan parkir
att_op_importInsRecord=Impor catatan kios wajah
att_op_importPidRecord=Impor catatan saksi
att_op_importVmsRecord=Catatan video impor
att_op_importUSBRecord=Impor catatan disk U
att_transaction_noAccModule=Tidak ada modul kontrol akses!
att_transaction_noParkModule=Tidak ada modul parkir!
att_transaction_noInsModule=Tidak ada modul kios wajah!
att_transaction_noPidModule=Tidak ada modul kartu!
att_transaction_exportRecord=Ekspor catatan asli
att_transaction_exportAttPhoto=Mengekspor foto kehadiran
att_transaction_exportDate=Tanggal ekspor
att_transaction_fileIsTooLarge=File yang diekspor terlalu besar, persempit rentang tanggal
att_statistical_attDatetime=Waktu kehadiran
att_statistical_attPhoto=Foto Kehadiran
att_statistical_attDetail=Detail Kehadiran
att_statistical_acc=Perangkat Kontrol Akses
att_statistical_att=Perangkat Kehadiran Waktu
att_statistical_park=Kamera LPR
att_statistical_faceRecognition=Perangkat pengenalan wajah
att_statistical_app=Perangkat ponsel
att_statistical_vms=Perlengkapan video
att_statistical_psg=Peralatan saluran
att_statistical_dataSources=Sumber Data
att_transaction_SyncRecord=Sinkronkan catatan kehadiran
#日打卡详情表
att_statistical_dayCardDetail=Detail check-in
att_statistical_cardDate=Tanggal Rekam
att_statistical_cardNumber=Rekam Waktu
att_statistical_earliestTime=Waktu Terlama
att_statistical_latestTime=Waktu Terbaru
att_statistical_cardTime=Waktu Pukulan
#请假汇总表
att_statistical_leaveDetail=Tinggalkan Detail
#日明细报表
att_statistical_attDate=Tanggal Kehadiran
att_statistical_week=Minggu
att_statistical_shiftInfo=Pergeseran Informasi
att_statistical_shiftTimeData=Bekerja Waktu Nyala/Mati
att_statistical_cardValidData=Waktu Punch
att_statistical_cardValidCount=Hitungan Punch
att_statistical_lateCount=Hitungan Akhir
att_statistical_lateMinute=Menit Terlambat
att_statistical_earlyCount=Penghitungan Awal
att_statistical_earlyMinute=Menit Awal
att_statistical_countData=Data Waktu
att_statistical_minuteData=Data menit
att_statistical_attendance_minute=Kehadiran (menit)
att_statistical_overtime_minute=Waktu tambahan (menit)
att_statistical_unusual_minute=Abnormal (menit)
#月明细报表
att_monthdetail_should_hour=Haruskah (waktu)
att_monthdetail_actual_hour=Aktual (waktu)
att_monthdetail_valid_hour=Valid (waktu)
att_monthdetail_absent_hour=Penyelesaian (waktu)
att_monthdetail_leave_hour=Tinggalkan (waktu)
att_monthdetail_trip_hour=Perjalanan bisnis (jam)
att_monthdetail_out_hour=Keluar (setiap jam)
att_monthdetail_should_day=Seharusnya (hari)
att_monthdetail_actual_day=Sebenarnya (hari)
att_monthdetail_valid_day=Efektif (hari)
att_monthdetail_absent_day=Penyelesaian (hari)
att_monthdetail_leave_day=Tinggalkan (hari)
att_monthdetail_trip_day=Perjalanan bisnis (hari)
att_monthdetail_out_day=Keluar (hari)
#月统计报表
att_statistical_late_minute=Durasi (menit)
att_statistical_early_minute=Durasi (menit)
#部门统计报表
#年度统计报表
att_statistical_should=Haruskah
att_statistical_actual=Aktual
att_statistical_valid=Valid
att_statistical_numberOfTimes=Waktu
att_statistical_usually=Hari Kerja
att_statistical_rest=Akhir Pekan
att_statistical_holiday=Liburan
att_statistical_total=Total
att_statistical_month=Statistik Bulan
att_statistical_year=Statistik Tahun
att_statistical_attendance_hour=Kehadiran (jam)
att_statistical_attendance_day=Kehadiran (hari)
att_statistical_overtime_hour=Jam lembur (setiap jam)
att_statistical_unusual_hour=Pengecualian (waktu)
att_statistical_unusual_day=Abnormal (hari)
#考勤设备参数
att_deviceOption_query=Lihat parameter perangkat
att_deviceOption_noOption=Tidak ada informasi parameter, dapatkan parameter perangkat terlebih dahulu
att_deviceOption_name=nama parameter
att_deviceOption_value=Nilai parameter
att_deviceOption_UserCount=Nomor pengguna
att_deviceOption_MaxUserCount=Jumlah maksimum pengguna
att_deviceOption_FaceCount=Jumlah templat wajah saat ini
att_deviceOption_MaxFaceCount=Jumlah maksimum templat wajah
att_deviceOption_FacePhotoCount=Jumlah gambar wajah saat ini
att_deviceOption_MaxFacePhotoCount=Jumlah maksimum gambar wajah
att_deviceOption_FingerCount=Jumlah templat sidik jari saat ini
att_deviceOption_MaxFingerCount=Jumlah maksimum templat sidik jari
att_deviceOption_FingerPhotoCount=Jumlah gambar sidik jari saat ini
att_deviceOption_MaxFingerPhotoCount=Jumlah maksimum gambar sidik jari
att_deviceOption_FvCount=Jumlah templat urat jari saat ini
att_deviceOption_MaxFvCount=Jumlah maksimum templat urat jari
att_deviceOption_FvPhotoCount=Jumlah gambar jari saat ini
att_deviceOption_MaxFvPhotoCount=Jumlah gambar urat jari
att_deviceOption_PvCount=Jumlah templat telapak tangan saat ini
att_deviceOption_MaxPvCount=Jumlah maksimum templat telapak tangan
att_deviceOption_PvPhotoCount=Gambar telapak tangan saat ini
att_deviceOption_MaxPvPhotoCount=Jumlah maksimum gambar telapak tangan
att_deviceOption_TransactionCount=Jumlah catatan saat ini
att_deviceOption_MaxAttLogCount=Jumlah maksimum catatan
att_deviceOption_UserPhotoCount=Foto pengguna saat ini
att_deviceOption_MaxUserPhotoCount=Jumlah maksimum foto pengguna
att_deviceOption_FaceVersion=Versi algoritma pengenalan wajah
att_deviceOption_FPVersion=Versi algoritma pengenalan sidik jari
att_deviceOption_FvVersion=Versi algoritma pengenalan vena jari
att_deviceOption_PvVersion=Versi algoritma pengenalan sawit
att_deviceOption_FWVersion=Versi firmware
att_deviceOption_PushVersion=Versi push
#=====================================================================
#API
att_api_areaCodeNotNull=Nomor area tidak boleh kosong
att_api_pinsNotNull=Data pin tidak boleh kosong
att_api_pinsOverSize=Panjang data pin tidak boleh melebihi 500
att_api_areaNoExist=Area tidak ada
att_api_sign=Suplemen
#=====================================================================
#休息时间段
att_leftMenu_breakTime=Waktu istirahat
att_breakTime_startTime=Waktu mulai
att_breakTime_endTime=Ketika jam
# =============================================================== =====================
#导入U盘记录
att_import_uploadFileSuccess=Berhasil mengunggah file, mulai parsing data file, harap tunggu ...
att_import_resolutionComplete=Selesaikan dan mulai perbarui database.
att_import_snNoExist=Perangkat absensi yang sesuai dengan file impor tidak ada. Silakan pilih file itu lagi.
att_import_fileName_msg=Persyaratan format nama file yang diimpor: Nomor seri perangkat dimulai dengan dan dipisahkan dengan garis bawah "_", misalnya: "3517171600001_attlog.dat".
att_import_notSupportFormat=Format ini tidak didukung saat ini!
att_import_selectCorrectFile=Silakan pilih file format yang benar!
att_import_fileFormat=Format file
att_import_targetFile=File target
att_import_startRow=Jumlah baris di awal header
att_import_startRowNote=Baris pertama dari format data adalah untuk mengimpor data, silakan periksa file sebelum mengimpor.
att_import_delimiter=pemisah
# =============================================================== =====================
#设备操作日志
att_device_op_log_op_type=Kode operasi
att_device_op_log_dev_sn=Nomor seri perangkat
att_device_op_log_op_content=Konten operasional
att_device_op_log_operator_pin=Nomor operator
att_device_op_log_operator_name=Nama operator
att_device_op_log_op_time=Waktu operasi
att_device_op_log_op_who_value=Nilai objek operasi
att_device_op_log_op_who_content=Deskripsi objek operasi
att_device_op_log_op_value1=Objek operasi 2
att_device_op_log_op_value_content1=Deskripsi objek operasi 2
att_device_op_log_op_value2=Objek operasi 3
att_device_op_log_op_value_content2=Deskripsi objek operasi 3
att_device_op_log_op_value3=Objek operasi 4
att_device_op_log_op_value_content3=Deskripsi objek operasi 4
#操作日志的操作类型
att_device_op_log_opType_0=Nyalakan
att_device_op_log_opType_1=Matikan
att_device_op_log_opType_2=Verifikasi gagal
att_device_op_log_opType_3=Alarm
att_device_op_log_opType_4=Masuk ke menu
att_device_op_log_opType_5=Ubah pengaturan
att_device_op_log_opType_6=Daftarkan sidik jari
att_device_op_log_opType_7=Daftarkan kata sandi
att_device_op_log_opType_8=Daftarkan kartu HID
att_device_op_log_opType_9=Hapus pengguna
att_device_op_log_opType_10=Hapus sidik jari
att_device_op_log_opType_11=Hapus kata sandi
att_device_op_log_opType_12=Hapus kartu RF
att_device_op_log_opType_13=Hapus data
att_device_op_log_opType_14=Buat kartu MF
att_device_op_log_opType_15=Daftarkan kartu MF
att_device_op_log_opType_16=Daftarkan kartu MF
att_device_op_log_opType_17=Hapus pendaftaran kartu MF
att_device_op_log_opType_18=Hapus konten kartu MF
att_device_op_log_opType_19=Pindahkan data pendaftaran ke kartu
att_device_op_log_opType_20=Salin data dari kartu ke mesin
att_device_op_log_opType_21=Tetapkan waktu
att_device_op_log_opType_22=Pengaturan pabrik
att_device_op_log_opType_23=Hapus entri dan keluar dari catatan
att_device_op_log_opType_24=Hapus hak administrator
att_device_op_log_opType_25=Ubah pengaturan grup kontrol akses
att_device_op_log_opType_26=Ubah pengaturan akses pengguna
att_device_op_log_opType_27=Ubah periode waktu akses
att_device_op_log_opType_28=Ubah pengaturan kombinasi buka kunci
att_device_op_log_opType_29=Buka kunci
att_device_op_log_opType_30=Daftarkan pengguna baru
att_device_op_log_opType_31=Ubah atribut sidik jari
att_device_op_log_opType_32=Alarm kejahatan
att_device_op_log_opType_34=Anti-kapal selam
att_device_op_log_opType_35=Hapus foto kehadiran
att_device_op_log_opType_36=Ubah informasi pengguna
att_device_op_log_opType_37=Liburan
att_device_op_log_opType_38=Pulihkan data
att_device_op_log_opType_39=Cadangkan data
att_device_op_log_opType_40=Unggah U disk
att_device_op_log_opType_41=Unduhan diska
att_device_op_log_opType_42=U enkripsi catatan kehadiran disk
att_device_op_log_opType_43=Hapus catatan setelah unduhan cakram USB berhasil
att_device_op_log_opType_53=Switch keluar
att_device_op_log_opType_54=Gerbang magnet
att_device_op_log_opType_55=Alarm
att_device_op_log_opType_56=Pulihkan parameter
att_device_op_log_opType_68=Foto pengguna terdaftar
att_device_op_log_opType_69=Ubah foto pengguna
att_device_op_log_opType_70=Ubah nama pengguna
att_device_op_log_opType_71=Ubah izin pengguna
att_device_op_log_opType_76=Ubah IP pengaturan jaringan
att_device_op_log_opType_77=Ubah topeng pengaturan jaringan
att_device_op_log_opType_78=Ubah gateway pengaturan jaringan
att_device_op_log_opType_79=Ubah pengaturan jaringan DNS
att_device_op_log_opType_80=Ubah kata sandi pengaturan koneksi
att_device_op_log_opType_81=Ubah ID perangkat pengaturan koneksi
att_device_op_log_opType_82=Ubah alamat server cloud
att_device_op_log_opType_83=Ubah port server cloud
att_device_op_log_opType_87=Ubah pengaturan catatan kontrol akses
att_device_op_log_opType_88=Ubah flag parameter wajah
att_device_op_log_opType_89=Ubah flag parameter sidik jari
att_device_op_log_opType_90=Ubah flag parameter urat jari
att_device_op_log_opType_91=Ubah flag parameter palmprint
att_device_op_log_opType_92=u flag peningkatan disk
att_device_op_log_opType_100=Ubah informasi kartu RF
att_device_op_log_opType_101=Daftarkan wajah
att_device_op_log_opType_102=Ubah izin staf
att_device_op_log_opType_103=Hapus izin personel
att_device_op_log_opType_104=Tambahkan izin staf
att_device_op_log_opType_105=Hapus catatan kontrol akses
att_device_op_log_opType_106=Hapus wajah
att_device_op_log_opType_107=Hapus foto orang
att_device_op_log_opType_108=Ubah parameter
att_device_op_log_opType_109=Pilih WIFISSID
att_device_op_log_opType_110=proxy aktifkan
att_device_op_log_opType_111=modifikasi proxyip
att_device_op_log_opType_112=modifikasi port proxy
att_device_op_log_opType_113=Ubah kata sandi orang
att_device_op_log_opType_114=Ubah informasi wajah
att_device_op_log_opType_115=Ubah kata sandi operator
att_device_op_log_opType_116=Lanjutkan pengaturan kontrol akses
att_device_op_log_opType_117=kesalahan input kata sandi operator
att_device_op_log_opType_118=kunci kata sandi operator
att_device_op_log_opType_120=Ubah Panjang Data Kartu Legic
att_device_op_log_opType_121=Daftarkan jari vena
att_device_op_log_opType_122=Ubah vena jari
att_device_op_log_opType_123=Hapus vena jari
att_device_op_log_opType_124=Daftarkan palm print
att_device_op_log_opType_125=Ubah telapak tangan
att_device_op_log_opType_126=Hapus cetak telapak tangan
# 操作 对象 描述
att_device_op_log_content_pin=ID Pengguna:
att_device_op_log_content_alarm=Alarm:
att_device_op_log_content_alarm_reason=Alasan alarm:
att_device_op_log_content_update_no=Ubah nomor item:
att_device_op_log_content_update_value=Ubah nilai:
att_device_op_log_content_finger_no=Nomor sidik jari:
att_device_op_log_content_finger_size=Panjang templat sidik jari:
#=====================================================================
#工作流
att_flowable_datetime_to=Untuk
att_flowable_todomsg_leave=Tinggalkan persetujuan
att_flowable_todomsg_sign=Persetujuan tambahan
att_flowable_todomsg_overtime=Persetujuan lembur
att_flowable_notifymsg_leave=Mohon informasikan
att_flowable_notifymsg_sign=Tanda tambahan
att_flowable_notifymsg_overtime=Pemberitahuan lembur
att_flowable_shift=Shift:
att_flowable_hour=Jam
att_flowable_todomsg_trip=Persetujuan perjalanan
att_flowable_notifymsg_trip=Perjalanan bisnis
att_flowable_todomsg_out=Persetujuan keluar
att_flowable_notifymsg_out=Pergilah untuk tahu
att_flow_apply=Terapkan
att_flow_applyTime=Waktu aplikasi
att_flow_approveTime=Waktu persetujuan
att_flow_operateUser=Peninjau
att_flow_approve=Persetujuan
att_flow_approveComment=Komentar
att_flow_approvePass=Hasil persetujuan
att_flow_status_processing=Persetujuan
#=====================================================================
#biotime
att_h5_pers_personIdNull=Id karyawan tidak boleh kosong
att_h5_attPlaceNull=Lokasi check-in tidak boleh kosong
att_h5_attAreaNull=Area kehadiran tidak boleh kosong
att_h5_pers_personNoExist=Jumlah karyawan tidak ada
att_h5_signRemarkNull=Komentar tidak boleh kosong
att_h5_common_pageNull=Kesalahan parameter paging
att_h5_taskIdNotNull=Id simpul tugas tidak boleh kosong
att_h5_auditResultNotNull=Hasil persetujuan tidak boleh kosong
att_h5_latLongitudeNull=Bujur dan lintang tidak boleh kosong
att_h5_pers_personIsNull=Id karyawan tidak ada
att_h5_pers_personIsNotInArea=Orang tersebut belum mengatur area
att_h5_mapApiConnectionsError=Kesalahan koneksi api peta
att_h5_googleMap=Peta Google
att_h5_gaodeMap=Peta Gaode
att_h5_defaultMap=Peta default
att_h5_shiftTime=Pergeseran waktu
att_h5_signTimes=Waktu pengisian
att_h5_enterKeyWords=Silakan masukkan kata kunci:
att_h5_mapSet=Pengaturan peta kehadiran
att_h5_setMapApiAddress=Tetapkan parameter peta
att_h5_MapSetWarning=Mengalihkan peta akan menyebabkan alamat masuk terminal seluler yang dimasukkan gagal sesuai dengan garis lintang dan bujur, harap modifikasi dengan hati-hati!
att_h5_mapSelect=Pemilihan peta
att_h5_persNoHire=Karyawan belum bergabung dengan perusahaan saat ini.
att_slef_apiKey=API-KEY
att_self_apiJsKey=API-JS-KEY
att_self_noComputeAtt=Kehadiran hari itu belum diperhitungkan.
att_self_noSignRecord=Tidak ada catatan pukulan pada hari itu
att_self_imageUploadError=Pengunggahan gambar gagal
att_self_attSignAddressAreaIsExist=Sudah ada titik masuk di area ini
att_self_signRuleIsError=Waktu pengisian saat ini tidak dalam waktu pengisian yang diizinkan.
att_self_signAcrossDay=Tak bisa mengisi tugas di hari lain!
att_self_todaySignIsExist=Sudah ada tambalan yang ditambahkan hari ini!
att_self_signSetting=Pengaturan tambalan
att_self_allowSign=Izinkan untuk tambalan:
att_self_allowSignSuffix=hari, Catatan kehadiran dalam
att_self_onlyThisMonth=Hanya bulan ini
att_self_allowAcrossMonth=Bolehkan lintas bulan
att_self_thisTimeNoSch=Tidak ada perubahan dalam periode waktu saat ini!
att_self_revokeReason=Alasan pencabutan:
att_self_revokeHint=Silakan masukkan alasan pembatalan dalam 20 kata untuk ditinjau
att_self_persSelfLogin=Login layanan mandiri karyawan
att_self_isOpenSelfLogin=Apakah akan memulai entri masuk layanan mandiri karyawan
att_self_applyAndWorkTimeOverlap=Ada tumpang tindih antara waktu aplikasi dan jam kerja
att_apply_DurationIsZero=Zero tak diijinkan untuk pemohon
att_sign_mapWarn=Gagal memuat peta, harap periksa koneksi jaringan dan nilai peta KUNCI
att_admin_applyWarn=Operasi gagal, ada orang yang tidak dijadwalkan atau waktu aplikasi tidak dalam lingkup jadwal!({0})
att_self_getPhotoFailed=Gambar tidak ada
att_self_view=Lihat
# 二维码
att_param_qrCodeUrl=Url kode QR
att_param_qrCodeUrlHref=Alamat server: port
att_param_appAttQrCode=Kode QR kehadiran seluler
att_param_timingFrequency=Interval waktu: 5-59 menit atau 1-24 jam
att_sign_signTimeNotNull=Waktu bebas tidak boleh habis
att_apply_overLastMonth=Aplikasi telah dimulai lebih dari bulan lalu
att_apply_withoutDetail=Tidak ada proses.
att_flowable_noAuth=Silakan gunakan akun administrator super untuk melihat
att_apply_overtimeOverMaxTimeLong=Lembur yang panjang lebih besar dari lembur maksimum
# 考勤设置参数符号
att_other_arrive=√
att_other_late=L
att_other_early=E
att_other_absent=□
att_other_noSignIn=[
att_other_noSignOff=]
att_other_leave=∆
att_other_overtime=OT
att_other_off=○
att_other_classes=●
att_other_trip=T
att_other_out=G
att_other_incomplete=├
att_other_outcomplete=┤
# 服务器下发命令
att_devCmd_submitTime=Kirimkan waktu
att_devCmd_returnedResult=Hasil kembali
att_devCmd_returnTime=Hapus daftar perintah
att_devCmd_content=Konten perintah
att_devCmd_clearCmd=Bersihkan barang
# 实时点名
att_realTime_selectDept=Silakan pilih departemen
att_realTime_noSignPers=Orang yang tidak terdaftar
att_realTime_signPers=Sudah masuk
att_realTime_signMonitor=Pemantauan masuk
att_realTime_signDateTime=Waktu check-in
att_realTime_realTimeSet=Pengaturan panggilan roll waktu-nyata
att_realTime_openRealTime=Aktifkan panggilan roll waktu-nyata
att_realTime_rollCallEnd=Roll call waktu nyata berakhir
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=Dijadwalkan
att_personSch_cycleSch=Penjadwalan siklus
att_personSch_cleanCycleSch=Bersihkan jadwal siklus
att_personSch_cleanTempSch=Hapus jadwal sementara
att_personSch_personCycleSch=jadwal siklus orang
att_personSch_deptCycleSch=Jadwal siklus departemen
att_personSch_groupCycleSch=Penjadwalan siklus grup
att_personSch_personTempSch=Penjadwalan personel sementara
att_personSch_deptTempSch=Jadwal sementara departemen
att_personSch_groupTempSch=Jadwal sementara grup
att_personSch_checkGroupFirst=Silakan periksa grup di kiri atau orang-orang di daftar di kanan untuk beroperasi!
att_personSch_sureDeleteGroup=Anda yakin akan menghapus {0} dan jadwal yang sesuai dengan grup?
att_personSch_sch=Jadwal
att_personSch_delSch=Hapus jadwal
#考勤计算
att_statistical_sureAllCalculate=Apakah Anda yakin akan melakukan penghitungan kehadiran untuk semua personel?
# 异常管理
att_exception_downTemplate=Unduh dan impor templat
att_exception_signImportTemplate=Masuk Template Impor
att_exception_leaveImportTemplate=Tinggalkan Template Impor
att_exception_overtimeImportTemplate=Templat Impor Lembur
att_exception_adjustImportTemplate=Sesuaikan Template Impor
att_exception_cellDefault=Bidang tidak wajib diisi
att_exception_cellRequired=Bidang wajib diisi
att_exception_cellDateTime=Kolom wajib diisi, format waktu adalah yyyy-MM-hh HH: mm: ss, seperti: 2020-07-07 08:30:00
att_exception_cellLeaveTypeName=Bidang wajib diisi, seperti: 'cuti pribadi', 'cuti nikah', 'cuti melahirkan', 'cuti sakit', 'cuti tahunan', 'cuti berkabung', 'cuti keluarga', 'cuti menyusui', 'perjalanan bisnis', 'pacaran' Nama yang sombong
att_exception_cellOvertimeSign=Bidang wajib diisi, seperti: 'lembur normal', 'lembur pada hari istirahat', 'lembur pada hari libur'
att_exception_cellAdjustType=Bidang wajib diisi, seperti: 'transfer off', 'make up class'
att_exception_cellAdjustDate=Kolom wajib diisi, format waktu adalah yyyy-MM-dd, seperti: 2020-07-07
att_exception_cellShiftName=Bidang wajib diisi jika jenis penyesuaian adalah shift shift
att_exception_refuse=Tolak
att_exception_end=Akhir yang tidak normal
att_exception_delete=Hapus
att_exception_stop=Jeda
# 时间段
att_timeSlot_normalTimeAdd=Tambahkan slot waktu normal
att_timeSlot_elasticTimeAdd=Tambahkan slot waktu elastis
# 班次
att_shift_addRegularShift=Tambahkan shift reguler
att_shift_addFlexibleShift=Tambahkan shift fleksibel
# 参数设置
att_param_notLeaveSetting=Pengaturan kalkulasi tidak salah
att_param_smallestUnit=Satuan minimum
att_param_workDay=Hari kerja
att_param_roundingControl=Kontrol Pembulatan
att_param_abort=Bawah (buang)
att_param_rounding=pembulatan
att_param_carry=Naik (bawa)
att_param_reportSymbol=Simbol tampilan laporan
att_param_convertCountValid=Harap masukkan angka dan hanya diperbolehkan satu tempat desimal
att_other_leaveThing=Hal
att_other_leaveMarriage=Pernikahan
att_other_leaveBirth=produk
att_other_leaveSick=Sakit
att_other_leaveAnnual=tahun
att_other_leaveFuneral=Pemakaman
att_other_leaveHome=Jelajahi
att_other_leaveNursing=Perawatan
att_other_leavetrip=perbedaan
att_other_leaveout=lainnya
att_common_schAndRest=Jadwal dan istirahat
att_common_timeLongs=Durasi waktu
att_personSch_checkDeptOrPersFirst=Silakan periksa departemen di sebelah kiri atau orang di daftar di sebelah kanan!
att_personSch_checkCalendarFirst=Silakan pilih tanggal yang Anda harus jadwalkan dulu!
att_personSch_cleanCheck=Hapus centang
att_personSch_delTimeSlot=Hapus periode waktu yang dipilih
att_personSch_repeatTimeSlotNoAdd=Tidak ada periode waktu berulang yang akan ditambahkan!
att_personSch_showSchInfo=Tampilkan detail jadwal
att_personSch_sureToCycleSch=Apakah Anda yakin untuk menjadwalkan {0} secara berkala?
att_personSch_sureToTempSch=Apakah Anda yakin untuk menjadwalkan {0} sementara?
att_personSch_sureToCycleSchDeptOrGroup=Apakah Anda yakin untuk menjadwalkan semua personel di bawah {0} secara berkala?
att_personSch_sureToTempSchDeptOrGroup=Apakah Anda yakin untuk sementara waktu menjadwalkan semua personil di bawah {0}?
att_personSch_sureCleanCycleSch=Apakah Anda yakin ingin menghapus {0} dari {1} hingga {2}?
att_personSch_sureCleanTempSch=Apakah Anda yakin ingin menghapus {0} pergeseran sementara dari {1} ke {2}?
att_personSch_sureCleanCycleSchDeptOrGroup=Apakah Anda yakin ingin menghapus jadwal periodik dari {1} hingga {2} untuk semua orang di bawah {0}?
att_personSch_sureCleanTempSchDeptOrGroup=Apakah Anda yakin ingin menghapus jadwal sementara dari {1} hingga {2} untuk semua orang di bawah {0}?
att_personSch_today=Hari ini
att_personSch_timeSoltName=Nama periode waktu
att_personSch_export=Penjadwalan personel ekspor
att_personSch_exportTemplate=Templat shift sementara personel ekspor
att_personSch_import=Impor penjadwalan sementara personel
att_personSch_tempSchTemplate=Templat penjadwalan personel sementara
att_personSch_tempSchTemplateTip=Silakan pilih waktu mulai dan waktu selesai untuk menjadwalkan, unduh templat jadwal dalam tanggal tersebut
att_personSch_opTip=Instruksi pengoperasian
att_personSch_opTip1=1.Anda dapat menggunakan mouse untuk menyeret slot waktu ke satu tanggal di kontrol kalender untuk penjadwalan.
att_personSch_opTip2=2.Dalam kontrol kalender, klik dua kali satu tanggal untuk menjadwalkan.
att_personSch_opTip3=3.Dalam kontrol kalender, tekan dan tahan mouse untuk memilih beberapa tanggal untuk penjadwalan.
att_personSch_schRules=Jadwalkan aturan
att_personSch_schRules1=1.penjadwalan berkala: Timpa penjadwalan sebelumnya setelah tanggal yang sama, terlepas dari apakah tidak ada persimpangan.
att_personSch_schRules2=2.Penjadwalan sementara: Ada persimpangan pada tanggal yang sama, dan penjadwalan sementara sebelumnya ditimpa kemudian. Jika tidak ada persimpangan, mereka juga akan ada.
att_personSch_schRules3=3.Periode dan Sementara: Jika ada persimpangan pada tanggal yang sama, periode akan ditutup sementara, dan jika tidak ada persimpangan, mereka juga akan ada secara bersamaan.
att_personSch_schStatus=Status Jadwal
#左侧菜单-排班管理
att_leftMenu_schDetails=Detail jadwal
att_leftMenu_detailReport=Laporan detail kehadiran
att_leftMenu_signReport=Tabel detail pendaftaran
att_leftMenu_leaveReport=Keluar dari Formulir Detail
att_leftMenu_abnormal=Tabel kehadiran tidak normal
att_leftMenu_yearLeaveSumReport=Laporan Cuti Tahunan
att_leave_maxFileCount=Anda hanya dapat menambahkan paling banyak 4 foto
#时间段
att_timeSlot_add=Atur slot waktu
att_timeSlot_select=Silakan pilih periode waktu!
att_timeSlot_repeat=Periode waktu "{0}" berulang!
att_timeSlot_overlapping=Periode waktu "{0}" tumpang tindih dengan waktu perjalanan "{1}"!
att_timeSlot_addFirst=Silakan tentukan periode waktunya dulu!
att_timeSlot_notEmpty=Periode waktu yang terkait dengan jumlah personil {0} tidak boleh kosong!
att_timeSlot_notExist=Periode waktu "{1}" yang sesuai dengan nomor personil {0} tidak ada!
att_timeSlot_repeatEx=Periode waktu "{1}" sesuai dengan jumlah personel {0} tumpang tindih dengan waktu perjalanan "{2}"
att_timeSlot_importRepeat=Periode waktu "{1}" yang sesuai dengan jumlah personel {0} diulang
att_timeSlot_importNotPin=Tidak ada orang dengan angka {0} di sistem!
att_timeSlot_elasticTimePeriod=Nomor personil {0}, tidak dapat mengimport periode waktu fleksibel '{1}'!
#导入
att_import_overData=Jumlah impor saat ini adalah {0}, melebihi batas 30.000, harap impor dalam batch!
att_import_existIllegalType={0} yang diimpor memiliki jenis ilegal!
#验证方式
att_verifyMode_0=pengenalan otomatis
att_verifyMode_1=Sidik jari saja
att_verifyMode_2=Verifikasi nomor pekerjaan
att_verifyMode_3=Hanya kata sandi
att_verifyMode_4=Hanya kartu
att_verifyMode_5=Sidik jari atau kata sandi
att_verifyMode_6=Sidik jari atau kartu
att_verifyMode_7=Kartu atau kata sandi
att_verifyMode_8=Nomor pekerjaan ditambah sidik jari
att_verifyMode_9=Sidik jari plus kata sandi
att_verifyMode_10=Kartu plus sidik jari
att_verifyMode_11=Kartu plus kata sandi
att_verifyMode_12=Sidik jari plus kata sandi plus kartu
att_verifyMode_13=ID kerja ditambah sidik jari plus kata sandi
att_verifyMode_14=(Nomor kerja ditambah sidik jari) atau (kartu ditambah sidik jari)
att_verifyMode_15=wajah manusia
att_verifyMode_16=Wajah dan sidik jari
att_verifyMode_17=Kata sandi plus wajah
att_verifyMode_18=Kartu plus wajah
att_verifyMode_19=Wajah plus sidik jari plus kartu
att_verifyMode_20=Wajah ditambah sidik jari plus kata sandi
att_verifyMode_21=Jari vena
att_verifyMode_22=Vena jari plus kode
att_verifyMode_23=Vena jari plus kartu
att_verifyMode_24=Vena jari plus kode plus kartu
att_verifyMode_25=Cetak telapak tangan
att_verifyMode_26=Cetak telapak tangan plus kartu
att_verifyMode_27=Telapak tangan dan wajah
att_verifyMode_28=Sidik jari dan sidik jari
att_verifyMode_29=Cetak telapak tangan plus sidik jari plus wajah
# 工作流
att_flow_schedule=Kemajuan audit
att_flow_schedulePass=(Lulus)
att_flow_scheduleNot=(Tidak disetujui)
att_flow_scheduleReject=(Ditolak)
# 工作时长表
att_workTimeReport_total=Total jam kerja
# 自动导出报表
att_autoExport_startEndTime=Waktu mulai dan berakhir
# 年假
att_annualLeave_setting=Pengaturan saldo cuti tahunan
att_annualLeave_settingTip1=Untuk menggunakan fungsi saldo cuti tahunan, Anda perlu menyetel waktu on-boarding untuk setiap karyawan; ketika waktu on-boarding tidak disetel, sisa cuti tahunan dari tabel saldo cuti tahunan karyawan ditampilkan kosong.
att_annualLeave_settingTip2=Jika tanggal sekarang lebih besar dari tanggal penerbitan kliring, modifikasi ini akan berlaku tahun berikutnya; jika tanggal saat ini kurang dari tanggal penerbitan kliring, saat tanggal penerbitan kliring tercapai, maka akan dihapus dan cuti tahunan akan diterbitkan kembali.
att_annualLeave_calculate=Tanggal pembukaan dan penerbitan cuti tahunan
att_annualLeave_workTimeCalculate=Hitung menurut rasio waktu kerja
att_annualLeave_rule=Aturan Waktu Cuti Tahunan
att_annualLeave_ruleCountOver=Batas jumlah set maksimum telah tercapai
att_annualLeave_years=Tahun senior
att_annualLeave_eachYear=Setiap tahun
att_annualLeave_have=Ya
att_annualLeave_days=Hari cuti tahunan
att_annualLeave_totalDays=Total cuti tahunan
att_annualLeave_remainingDays=Sisa cuti tahunan
att_annualLeave_consecutive=Pengaturan aturan cuti tahunan harus tahun berturut-turut
# 年假结余表
att_annualLeave_report=Neraca Cuti Tahunan
att_annualLeave_validDate=Tanggal Berlaku
att_annualLeave_useDays=Gunakan {0} hari
att_annualLeave_calculateDays=Rilis {0} hari
att_annualLeave_notEnough={0} Cuti tahunan tidak cukup!
att_annualLeave_notValidDate={0} tidak termasuk dalam rentang cuti tahunan yang valid!
att_annualLeave_notDays={0} tidak memiliki cuti tahunan!
att_annualLeave_tip1=Zhang San bergabung pada 1 September tahun lalu
att_annualLeave_tip2=Pengaturan saldo cuti tahunan
att_annualLeave_tip3=Tanggal kliring dan penerbitan adalah 1 Januari setiap tahun; dihitung dengan pembulatan sesuai rasio kerja; jika masa kerja kurang dari atau sama dengan 1, akan ada 3 hari cuti tahunan, dan jika masa kerja kurang dari atau sama dengan 3 tahun, akan ada 5 hari cuti tahunan
att_annualLeave_tip4=Perhitungan menikmati cuti tahunan
att_annualLeave_tip5=Tahun lalu 09-01 ~ 12-31 nikmati 4 / 12x3=1,0 hari
att_annualLeave_tip6=Tahun ini 01-01 ~ 12-31 nikmati 4.0 hari (tahun ini 01-01 ~ 08-31 nikmati 8 / 12x3=2.0 hari   tahun ini 09-01 ~ 12-31 nikmati 4 / 12x5≈2.0 hari)
# att SDC
att_sdc_name=Perlengkapan video
att_sdc_wxMsg_firstData=Halo, Anda memiliki pemberitahuan check-in kehadiran
att_sdc_wxMsg_stateData=Tidak ada rasa kehadiran check-in sukses
att_sdc_wxMsg_remark=Pengingat: Hasil akhir kehadiran tergantung pada halaman detail check-in.
# 时间段
att_timeSlot_conflict=Slot waktu bentrok dengan slot waktu lain hari itu
att_timeSlot_selectFirst=Silakan pilih slot waktu
# 事件中心
att_eventCenter_sign=Kehadiran masuk
#异常管理
att_exception_classImportTemplate=Template Impor Kelas
att_exception_cellClassAdjustType=Bidang wajib diisi, seperti: "{0}", "{1}", "{2}"
att_exception_swapDateDate=bidang yang tidak wajib diisi, format waktunya yyyy-MM-dd, seperti: 07-07-2020
#消息中心
att_message_leave=Bewara ngeunaan kahadiran {0}
att_message_leaveContent={0} dikintunkeun {1}, {2} waktosna nyaéta {3} ~ {4}
att_message_leaveTime=Ninggalkeun Waktos
att_message_overtime=Béja kahadiran sareng lembur
att_message_overtimeContent={0} dikintunkeun ka lembur, sareng waktosna lembur nyaéta {1} ~ {2}
att_message_overtimeTime=Waktu lembur
att_message_sign=Bewara tanda kahadiran
att_message_signContent={0} ngalebetkeun tanda tambahan, sareng waktos tanda tambahan nyaéta {1}
att_message_adjust=Bewara ngeunaan pangaluyuan kahadiran
att_message_adjustContent={0} ngalebetkeun pangaluyuan, sareng tanggal pangaturan nyaéta {1}
att_message_class=Kahadiran sareng pergeseran béja
att_message_classContent=Eusi Kelas
att_message_classContent0={0} dikintunkeun pergeseran, tanggal shift nyaéta {1}, sareng pergeseran na nyaéta {2}
att_message_classContent1={0} ngirimkeun shift, tanggal shift nyaéta {1}, sareng tanggal shift nyaéta {2}
att_message_classContent2={0} ({1}) sareng {2} ({3}) kelas swap
#推送中心
att_pushCenter_transaction=Catatan kehadiran
# 时间段
att_timeSlot_workTimeNotEqual=Waktu kerja tidak boleh sama dengan waktu pulang kerja
att_timeSlot_signTimeNotEqual=Waktu masuk awal tidak boleh sama dengan waktu keluar akhir
# 北向接口A
att_api_notNull={0} tidak boleh kosong!
att_api_startDateGeEndDate=Waktu mulai tidak boleh lebih besar dari atau sama dengan waktu berakhir!
att_api_leaveTypeNotExist=Spesies palsu tidak ada!
att_api_imageLengthNot2000=Panjang alamat gambar tidak boleh melebihi 2000!
# 20221230新增国际化
att_personSch_workTypeNotNull=Jenis pekerjaan yang cocok dengan nomor persunal {0} tidak dapat kosong!
att_personSch_workTypeNotExist=Jenis kerja yang cocok dengan nomor persunal {0} tidak ada!
att_annualLeave_recalculate=Kalkulasi ulang
# 20230530新增国际化
att_leftMenu_dailyReport=Laporan Harian Kehadiran
att_leftMenu_overtimeReport=Laporan lembur
att_leftMenu_lateReport=Laporan Terlambat
att_leftMenu_earlyReport=Keluarkan Laporan Lebih Awal
att_leftMenu_absentReport=Laporan Absen
att_leftMenu_monthReport=Laporan Bulanan Kehadiran
att_leftMenu_monthWorkTimeReport=Laporan Waktu Kerja Bulanan
att_leftMenu_monthCardReport=Laporan kartu bulanan
att_leftMenu_monthOvertimeReport=Laporan Lembur Bulanan
att_leftMenu_overtimeSummaryReport=Laporan ringkasan lembur staf
att_leftMenu_deptOvertimeSummaryReport=Laporan Ringkasan Lembur Dept
att_leftMenu_deptLeaveSummaryReport=Dept Tinggalkan Laporan Ringkasan
att_annualLeave_calculateDay=Jumlah hari cuti tahunan
att_annualLeave_adjustDay=Sesuaikan Hari
att_annualLeave_sureSelectDept=Apakah Anda yakin ingin melakukan operasi {0} pada departemen yang dipilih?
att_annualLeave_sureSelectPerson=Apakah Anda yakin ingin melakukan operasi {0} pada orang yang dipilih?
att_annualLeave_calculateTip1=Saat menghitung berdasarkan lama masa kerja: perhitungan cuti tahunan adalah akurat per bulan, jika masa kerja adalah 10 tahun 3 bulan, maka 10 tahun 3 bulan akan digunakan untuk perhitungan;
att_annualLeave_calculateTip2=Bila konversi tidak didasarkan pada masa kerja: perhitungan cuti tahunan akurat untuk tahun, jika masa kerja adalah 10 tahun dan 3 bulan, maka 10 tahun akan digunakan untuk perhitungan;
att_rule_isInCompleteTip=Prioritas adalah yang tertinggi ketika tidak masuk atau tidak keluar dicatat sebagai tidak lengkap, dan keterlambatan, cuti awal, ketidakhadiran, dan valid
att_rule_absentTip=Bila tidak masuk atau tidak keluar dicatat sebagai ketidakhadiran, lama absen sama dengan panjang jam kerja dikurangi lama cuti terlambat hingga awal
att_timeSlot_elasticTip1=0, waktu efektif sama dengan waktu aktual, tidak ada absensi
att_timeSlot_elasticTip2=Jika waktu aktual lebih lama dari waktu kerja, maka waktu efektif sama dengan waktu kerja, tidak ada absensi
att_timeSlot_elasticTip3=Jika durasi aktual kurang dari durasi kerja, durasi efektif sama dengan durasi aktual, dan ketidakhadiran sama dengan durasi kerja dikurangi durasi aktual
att_timeSlot_maxWorkingHours=Jam kerja tidak boleh lebih dari
# 20231030
att_customReport=Laporan khusus kehadiran
att_customReport_byDayDetail=Berdasarkan Detail Hari
att_customReport_byPerson=Ringkasan berdasarkan Orang
att_customReport_byDept=Ringkasan berdasarkan Departemen
att_customReport_queryMaxRange=Kisaran Maksimum Kueri adalah empat bulan
# 20240630新增国际化
att_personSch_shiftWorkTypeTip1=1. Ketika bekerja overtime pada hari kerja/istirahat biasa, prioritas jadwal lebih rendah dari pada hari libur
att_personSch_shiftWorkTypeTip2=2. Ketika tipe pekerjaan adalah waktu tambahan selama liburan, prioritas jadwal lebih tinggi dari pada liburan
att_personVerifyMode=Metode verifikasi pribadi
att_personVerifyMode_setting=Pengaturan metode pengesahan
att_personSch_importCycSch=Impor jadwal siklus pribadi
att_personSch_cycSchTemplate=Templat jadwal siklus pribadi
att_personSch_exportCycSchTemplate=Muat turun templat jadwal siklus pribadi
att_personSch_scheduleTypeNotNull=Tipe Shift tidak dapat kosong atau tidak ada!
att_personSch_shiftNotNull=Tukar tidak bisa kosong!
att_personSch_shiftNotExist=Tukar tidak ada!
att_personSch_onlyAllowOneShift=Scheduling biasa hanya memungkinkan satu shift untuk dijadwalkan!
att_shift_attShiftStartDateRemark2=Minggu dimana tanggal awal siklus ditempatkan adalah minggu pertama; Bulan dimana tanggal awal siklus ditempatkan adalah bulan pertama.
#打卡状态
att_cardStatus_setting=Pengaturan Status Kehadiran
att_cardStatus_name=Nama
att_cardStatus_value=Nilai
att_cardStatus_alias=Apel
att_cardStatus_every_day=Hari ini
att_cardStatus_by_week=Mingguan
att_cardStatus_autoState=State Otomatis
att_cardStatus_attState=Status Kehadiran
att_cardStatus_signIn=Masuk
att_cardStatus_signOut=Keluar
att_cardStatus_out=keluar
att_cardStatus_outReturn=Kembali setelah Keluar
att_cardStatus_overtime_signIn=Masuk Jam Kerja Tambahan
att_cardStatus_overtime_signOut=Keluar Jam Kerja Tambahan
# 20241030新增国际化
att_leaveType_enableMaxDays=Memperbolehkan Batasan Tahunan
att_leaveType_maxDays=Batasan Tahunan (Hari)
att_leaveType_applyMaxDays=Permohonan Tahunan Tidak Boleh Melebihi {0} Hari
att_param_overTimeSetting=Pengaturan Level Jam Kerja Lembur
att_param_overTimeLevel=Level Jam Kerja Lembur (Jam)
att_param_overTimeLevelEnable=Apakah Memasang kalkulasi level jam lembur
att_param_reportColor=Warna Tampilan Laporan
# APP
att_app_signClientTip=Perangkat ini sudah dicatat oleh orang lain hari ini
att_app_noSignAddress=Area peninjauan tanda tangan belum diatur, harap hubungi admin untuk pengaturan
att_app_notInSignAddress=Belum tiba di tempat peninjauan tanda tangan, tidak dapat meninjau
att_app_attendance=Presensi saya
att_app_apply=Permohonan presensi
att_app_approve=Verifikasi saya
# 20250530
att_node_leaderNodeExist=Node persetujuan pemimpin langsung sudah ada
att_signAddress_init=Inisialisasi peta
att_signAddress_initTips=Masukkan kunci peta dan inisialisasikan peta untuk memilih alamat