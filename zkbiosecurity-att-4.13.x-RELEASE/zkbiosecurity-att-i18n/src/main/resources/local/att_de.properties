#系统名称 德语
att_systemName=Att6.0 Managementsystem für Zeiterfassung
#=====================================================================
#左侧菜单
att_module=Zeiterfassung
#左侧菜单-考勤设备
att_leftMenu_attendanceDevice=Geräteverwaltung
att_leftMenu_device=Anwesenheitsgerät
att_leftMenu_point=Zeiterfassungspunkt
att_leftMenu_sign_address=Mobile Check-in-Adresse
att_leftMenu_adms_devCmd=Der Server hat einen Befehl ausgegeben
#左侧菜单-基础信息
att_leftMenu_basicInformation=Grundinformation
att_leftMenu_rule=Regeln
att_leftMenu_base_rule=Grundregeln
att_leftMenu_department_rule=Abteilungsregeln
att_leftMenu_holiday=Feiertage
att_leftMenu_leaveType=Typ
att_leftMenu_timingCalculation=Zeitliche Berechnung
att_leftMenu_autoExport=Automatischer Export
att_leftMenu_param=Parametereinstellung
#左侧菜单-班次管理
att_leftMenu_shiftManagement=Schichtmanagement
att_leftMenu_timeSlot=Zeitraum
att_leftMenu_shift=Schicht
#左侧菜单-排班管理
att_leftMenu_scheduleManagement=Schichtplanverwaltung
att_leftMenu_group=Gruppe
att_leftMenu_groupPerson=Gruppierte Menschen
att_leftMenu_groupSch=Schichtplangruppierung
att_leftMenu_deptSch=Abteilungsplan
att_leftMenu_personSch=Personalplan
att_leftMenu_tempSch=Vorübergehender Plan
att_leftMenu_nonSch=Nicht geplante Personen
#左侧菜单-异常管理
att_leftMenu_exceptionManagement=Anwesenheitsausnahmeverwaltung
att_leftMenu_sign=Ergänzendes Anmeldeblatt
att_leftMenu_leave=Beurlaubung
att_leftMenu_trip=Reise
att_leftMenu_out=Hinausgehen
att_leftMenu_overtime=Überstunde
att_leftMenu_adjust=Anpassung der Pausenzeit an die Arbeit
att_leftMenu_class=Einstellung
#左侧菜单-统计报表
att_leftMenu_statisticalReport=Anwesenheitsstatistikbericht
att_leftMenu_manualCalculation=Manuelle Berechnung
att_leftMenu_transaction=Formular für Originalaufzeichnung
att_leftMenu_dayCardDetailReport=Tägliche Stanzdetailtabelle
att_leftMenu_leaveSummaryReport=Übersichtstabelle für Beurlaubung
att_leftMenu_dayDetailReport=Tagesbericht
att_leftMenu_monthDetailReport=Monatlicher Bericht
att_leftMenu_monthStatisticalReport=Monatlicher statistischer Bericht (nach Person)
att_leftMenu_deptStatisticalReport=Statistischer Bericht der Abteilung (nach Abteilung)
att_leftMenu_yearStatisticalReport=Jährlicher statistischer Bericht (nach Person)
att_leftMenu_attSignCallRollReport=Appell anmelden
att_leftMenu_workTimeReport=Arbeitszeitbericht
#左侧菜单-设备操作日志
att_leftMenu_device_op_log=Gerätebetriebsprotokoll
#左侧菜单-实时点名
att_leftMenu_realTime_callRoll=Appell
#=====================================================================
#公共
att_common_person=Personal
att_common_pin=Nummer
att_common_group=Gruppe
att_common_dept=Abteilung
att_common_symbol=Symbol
att_common_deptNo=Abteilungsnummer
att_common_deptName=Abteilungsname
att_common_groupNo=Gruppennummer
att_common_groupName=Gruppenname
att_common_operateTime=Betriebszeit
att_common_operationFailed=Operation fehlgeschlagen
att_common_id=ID
att_common_deptId=Abteilung-ID
att_common_groupId=Gruppe-ID
att_common_deviceId=Gerät-ID
att_person_pin=Personal Nummer
att_person_name=Name
att_person_lastName=Familienname
att_person_internalCard=Kartennummer
att_person_attendanceMode=Zeit- und Anwesenheitsmodus
att_person_normalAttendance=Normale Anwesenheit
att_person_noPunchCard=Keine Lochkarte
att_common_attendance=Anwesenheit
att_common_attendance_hour=Anwesenheit (Stunde)
att_common_attendance_day=Anwesenheit (Tag)
att_common_late=Spät
att_common_early=Früher gehen
att_common_overtime=Überstunde
att_common_exception=Abnormal
att_common_absent=Fehlzeit
att_common_leave=Beurlaubung
att_common_trip=Reise
att_common_out=Hinausgehen
att_common_staff=Allgemeiner Mitarbeiter
att_common_superadmin=Super Administrator
att_common_msg=SMS-Inhalt
att_common_min=SMS-Dauer (Minute)
att_common_letterNumber=Geben Sie nur Zahlen oder Buchstaben ein!
att_common_relationDataCanNotDel=Die zugehörigen Daten können nicht gelöscht werden!
att_common_relationDataCanNotEdit=Die zugehörigen Daten können nicht geändert werden!
att_common_needSelectOneArea=Bitte wählen Sie einen Bereich!
att_common_neesSelectPerson=Bitte wählen Sie eine Person!
att_common_nameNoSpace=Der Name darf keine Leerzeichen enthalten!
att_common_digitsValid=Geben Sie nur Zahlen mit bis zu zwei Dezimalstellen ein!
att_common_numValid=Geben Sie nur Zahlen ein!
#=====================================================================
#工作面板
att_dashboard_worker=Workaholic
att_dashboard_today=Heutige Zeiterfassung
att_dashboard_todayCount=Segmentstatistik heutiger Zeiterfassung
att_dashboard_exceptionCount=Abnormale Statistiken (Dieser Monat)
att_dashboard_lastWeek=Letzte Woche
att_dashboard_lastMonth=Letzter Monat
att_dashboard_perpsonNumber=Gesamt Personen
att_dashboard_actualNumber=Tatsächliche Anzahl der Beschäftigten
att_dashboard_notArrivedNumber=Anzahl der Unterbeschäftigten
att_dashboard_attHour=Arbeitszeit
#区域
#设备
att_op_syncDev=Synchronisieren Sie die Softwaredaten mit dem Gerät
att_op_account=Anwesenheitsdaten Korrekturlesen
att_op_check=Laden Sie die Daten erneut hoch
att_op_deleteCmd=Gerätebefehl löschen
att_op_dataSms=Die öffentliche Kurznachricht
att_op_clearAttPic=Anwesenheitsfoto löschen
att_op_clearAttLog=Anwesenheitsliste löschen
att_device_waitCmdCount=Anzahl der auszuführenden Befehle
att_device_status=Aktivierter Status
att_device_register=Registrierte Geräte
att_device_isRegister=Ob registrierte Geräte
att_device_existNotRegDevice=Für nicht registrierte Geräte können keine Daten abgerufen werden!
att_device_fwVersion=Firmware-Versionsnummer
att_device_transInterval=Aktualisierungsintervall (Minute)
att_device_cmdCount=Die maximale Anzahl von Befehlen für die Kommunikation mit dem Server
att_device_delay=Abfrage der Aufzeichnungszeit (Sekunde)
att_device_timeZone=Zeitzone
att_device_operationLog=Betriebsprotokoll
att_device_registeredFingerprint=Fingerabdruck registrieren
att_device_registeredUser=Nutzer registrieren
att_device_fingerprintImage=Fingerabdruckbild
att_device_editUser=Benutzer bearbeiten
att_device_modifyFingerprint=Fingerabdruck ändern
att_device_faceRegistration=Gesichts registrieren
att_device_userPhotos=Benutzerfotos
att_device_attLog=Ob Anwesenheitsliste hochgeladen werden soll
att_device_operLog=Ob Personalinformation hochgeladen werden soll
att_device_attPhoto=Ob Anwesenheitsfoto hochgeladen werden soll
att_device_isOnLine=Ob online
att_device_InputPin=Geben Sie die Personalnummer ein
att_device_getPin=Erhalten Sie bestimmte Personendaten
att_device_separatedPin=Mehrere durch Kommas getrennte Personalnummern
att_device_authDevice=Autorisierte Geräte
att_device_disabled=Die folgende Geräte sind deaktiviert und können nicht bedient werden!
att_device_autoAdd=Neue Ausrüstung wird automatisch hinzugefügt
att_device_receivePersonOnlyDb=Empfängt nur die Daten des in der Datenbank vorhandenen Personals
att_devMenu_control=Gerätesteuerung
att_devMenu_viewOrGetInfo=Information anzeigen und abrufen
att_devMenu_clearData=Gerätedaten löschen
att_device_disabledOrOffline=Das Gerät ist nicht aktiviert oder nicht online und kann nicht betrieben werden!
att_device_areaStatus=Status des Gerätebereichs
att_device_areaCommon=Region ist normal
att_device_areaEmpty=Der Bereich ist leer
att_device_isRegDev=Die Änderung der Zeitzone oder des Registrarstatus erfordert einen Neustart des Geräts, um wirksam zu werden!
att_device_canUpgrade=Die folgenden Geräte können aktualisiert werden
att_device_offline=Die folgenden Geräte sind offline und können nicht betrieben werden!
att_device_oldProtocol=Altes Protokoll
att_device_newProtocol=Neues Protokoll
att_device_noMoreTwenty=Das Firmware-Upgrade-Paket für alte Protokollgeräte darf 20 Millionen nicht überschreiten
att_device_transferFilesTip=Firmware erfolgreich erkannt, Dateien übertragen
att_op_clearAttPers=Gerätepersonal löschen
#区域人员
att_op_forZoneAddPers=Fügen Sie Personen in den Bereich hinzu
att_op_dataUserSms=Private Kurznachricht
att_op_syncPers=Erneut mit dem Gerät synchronisieren
att_areaPerson_choiceArea=Bitte wählen Sie einen Bereich!
att_areaPerson_byAreaPerson=Nach Region
att_areaPerson_setByAreaPerson=Nach Bereichspersonal festlegen
att_areaPerson_importBatchDel=Massenlöschung importieren
att_areaPerson_syncToDevSuccess=Erfolgreiche Operation! Bitte warten Sie, bis die Befehlsübermittlung abgeschlossen ist
att_areaPerson_personId=Person-ID
att_areaPerson_areaId=Bereich-ID
att_area_existPerson=Es gibt Leute in dem Bereich!
att_areaPerson_notice1=Der Bereich oder das Personal kann nicht gleichzeitig leer sein!
att_areaPerson_notice2=Personal oder Bereich nicht gefunden!
att_areaPerson_notice3=Kein Gerät unter dem Bereich gefunden!
att_areaPerson_addArea=Bereich hinzufügen
att_areaPerson_delArea=Bereich löschen
att_areaPerson_persNoExit=Person existiert nicht
att_areaPerson_importTip1=Stellen Sie sicher, dass die importierte Person bereits im Personalmodul vorhanden ist
att_areaPerson_importTip2=Die Stapelimporteure werden nicht automatisch an das Gerät geliefert und müssen manuell synchronisiert werden
att_areaPerson_addAreaPerson=Regionales Personal hinzufügen
att_areaPerson_delAreaPerson=Bereichspersonal löschen
att_areaPerson_importDelAreaPerson=Bereichspersonal importieren und löschen
att_areaPerson_importAreaPerson=Personal im Importbereich
#考勤点
att_attPoint_name=Name des Anwesenheitspunkts
att_attPoint_list=Zeiterfassungsliste
att_attPoint_deviceModule=Ausstattungsmodul
att_attPoint_acc=Zugangskontrolle
att_attPoint_park=Parkplatz
att_attPoint_ins=Informationsbildschirm
att_attPoint_pid=Zeuge
att_attPoint_vms=Video
att_attPoint_psg=Gang
att_attPoint_doorList=Torliste
att_attPoint_deviceList=Geräteliste
att_attPoint_channelList=Kanalliste
att_attPoint_gateList=Torliste
att_attPoint_recordTypeList=Datensatztyp ziehen
att_attPoint_door=Bitte wählen Sie die entsprechende Tür aus
att_attPoint_device=Bitte wählen Sie das entsprechende Gerät aus
att_attPoint_gate=Bitte wählen Sie das entsprechende Tor aus
att_attPoint_normalPassRecord=Normaler Pass-Datensatz
att_attPoint_verificationRecord=Verifizierungsdatensatz
att_person_attSet=Einstellung für Zeiterfassung
att_attPoint_point=Bitte wählen Sie einen Anwesenheitspunkt
att_attPoint_count=Unzureichende Anwesenheitspunkte, Operation fehlgeschlagen
att_attPoint_notSelect=Es ist kein entsprechendes Modul konfiguriert
att_attPoint_accInsufficientPoints=Wenn die Anwesenheitspunkte nicht ausreichen sind, wird der Zugriffskontrolldatensatz ausgeführt!
att_attPoint_parkInsufficientPoints=Wenn die Anwesenheitspunkte nicht ausreichen sind, wird die  Parkplatzaufzeichnung ausgeführt!
att_attPoint_insInsufficientPoints=Wenn die Anwesenheitspunkte nicht ausreichen sind, wird der Informationsbildschirm ausgeführt!
att_attPoint_pidInsufficientPoints=Wenn die Anwesenheitspunkte nicht ausreichen sind, wird die Zeuge ausgeführt!
att_attPoint_doorOrParkDeviceName=Name der Tür oder der Parkausrüstung
att_attPoint_vmsInsufficientPoints=Wenn die Anwesenheitspunkte nicht ausreichen sind, wird das Video ausgeführt!
att_attPoint_psgInsufficientPoints=Der Kanal hat nicht genügend Anwesenheitspunkte!
att_attPoint_delDevFail=Fehler beim Löschen des Geräts, das Gerät wurde als Anwesenheitspunkt verwendet!
att_attPoint_pullingRecord=Die Anwesenheitsstelle erhält regelmäßig Aufzeichnungen, bitte warten Sie!
att_attPoint_lastTransactionTime=Letzte Datenabrufzeit
att_attPoint_masterDevice=Master-Gerät
att_attPoint_channelName=Kanalname
att_attPoint_cameraName=Kameraname
att_attPoint_cameraIP=Kamera-IP
att_attPoint_channelIP=Kanal-IP
att_attPoint_gateNumber=Gate-Nummer
att_attPoint_gateName=Torname
#APP考勤签到地址
att_signAddress_address=Adresse
att_signAddress_longitude=Längengrad
att_signAddress_latitude=Breitengrad
att_signAddress_range=Reichweite
att_signAddress_rangeUnit=Einheit ist Meter (m)
#=====================================================================
#规则
att_rule_baseRuleSet=Einstellung für Grundregeln
att_rule_countConvertSet=Einstellung für Umrechnung
att_rule_otherSet=Andere Einstellung
att_rule_baseRuleSignIn=Regeln für die Anmeldung von Karten bei der Arbeit
att_rule_baseRuleSignOut=Regeln für die Abmeldung von Karten nach der Arbeit
att_rule_earliestPrinciple=Am frühesten
att_rule_theLatestPrinciple=Am spätesten
att_rule_principleOfProximity=In der Nähe
att_rule_baseRuleShortestMinutes=Die kürzeste Anwesenheitszeit soll länger als (mindestens 10 Minuten) sein.
att_rule_baseRuleLongestMinutes=Die längste Anwesenheitszeit soll weniger als (maximal 1440 Minuten) sein.
att_rule_baseRuleLateAndEarly=Verspätung und Früher Gehen gelten als Fehlzeiten
att_rule_baseRuleCountOvertime=Statistische Überstunde
att_rule_baseRuleFindSchSort=Reihenfolge für Schichtaufzeichnung suchen
att_rule_groupGreaterThanDepartment=Gruppe -> Abteilung
att_rule_departmentGreaterThanGroup=Abteilung -> Gruppe
att_rule_baseRuleSmartFindClass=Das Prinzip der intelligenten Arbeitssuche
att_rule_timeLongest=Längste Zeit
att_rule_exceptionLeast=Wenigste Ausnahme
att_rule_baseRuleCrossDay=Ergebnisse der Anwesenheitsberechnung für Schichtzeiträume über Tage
att_rule_firstDay=Erster Tag
att_rule_secondDay=Zweiter Tag
att_rule_baseRuleShortestOvertimeMinutes=Einzelne Mindestdauer für Überstunde (Minute)
att_rule_baseRuleMaxOvertimeMinutes=Maximale Überstundendauer (Minute)
att_rule_baseRuleElasticCal=Berechnungsmethode der elastischen Dauer
att_rule_baseRuleTwoPunch=Kumulative Zeit von zwei Schlägen
att_rule_baseRuleStartEnd=Zählzeit für ersten und letzten Schlag
att_rule_countConvertHour=Benchmark für Stundenumrechnung
att_rule_formulaHour=Formel: Stunden = Minuten / 60
att_rule_countConvertDay=Benchmark für Tagenumrechnung
att_rule_formulaDay=Formel: Tage = Minuten / Arbeit Minuten pro Tag
att_rule_inFormulaShallPrevail=Vorbehaltlich der Ergebnisse der Formelberechnung;
att_rule_remainderHour=Der Rest ist größer oder gleich
att_rule_oneHour=Als eine Stunde gezählt;
att_rule_halfAnHour=Als eine halbe Stunde gezählt, sonst ignorieren;
att_rule_remainderDay=Der Quotient ist größer oder gleich der Anzahl der Arbeit Minuten
att_rule_oneDay=%,  als ein Tag gezählt;
att_rule_halfAnDay=%,  als halben Tag gezählt, sonst ignorieren;
att_rule_countConvertAbsentDay=Benchmark für Umrechnung von Abwesenheitstagen
att_rule_markWorkingDays=Als Arbeitstage gezählt
att_rule_countConvertDecimal=Dezimalstelle
att_rule_otherSymbol=Das Symbol für das Anwesenheitsergebnis im Bericht einstellen
att_rule_arrive=Soll/Ist
att_rule_noSignIn=Nicht eingecheckt
att_rule_noSignOff=Nicht ausgecheckt
att_rule_off=Urlaub anpassen
att_rule_class=Ergänzung zur Arbeit
att_rule_shortLessLong=Die kürzeste Anwesenheitszeit kann nicht länger als die längste Anwesenheitszeit sein!
att_rule_symbolsWarning=Das Anwesenheitssymbol im Bericht darf nicht leer sein!
att_rule_reportSettingSet=Export einstellen
att_rule_shortDateFormat=Datumsformat
att_rule_shortTimeFormat=Zeitformat
att_rule_baseRuleSignBreakTime=Ob man die Pause einlegt
att_leftMenu_custom_rule=Benutzerdefinierte Regeln
att_custom_rule_already_exist={0}Benutzerdefinierte Regeln existieren bereits!
att_add_group_custom_rule=Gruppierungsregeln hinzufügen
att_custom_rule_type=Regeltyp
att_rule_type_group=Gruppierungsregeln
att_rule_type_dept=Abteilungsregeln
att_custom_rule_orgNames=Benutzerzielgruppe
att_rult_maxOverTimeType1=Unbegrenzt
att_rult_maxOverTimeType2=Diese Woche
att_rult_maxOverTimeType3=Dieser Monat
att_rule_countConvertDayRemark1=Beispiel: Die effektive Arbeitszeit beträgt 500 Minuten, die Arbeitszeit soll 480 Minuten pro Tag betragen, das Ergebnis ist 500/480 = 1,04 und die letzte Dezimalstelle ist 1,0
att_rule_countConvertDayRemark2=Beispiel: Die effektive Arbeitszeit beträgt 500 Minuten, die Arbeitszeit soll 480 Minuten pro Tag betragen, das Ergebnis ist 500/480 = 1,04, 1,04> 0,8 wird als ein Tag berechnet
att_rule_countConvertDayRemark3=Beispiel: Die effektive Arbeitszeit beträgt 300 Minuten, die Arbeitszeit soll 480 Minuten pro Tag betragen, das Ergebnis ist 300/480 = 0,625, 0,2 <0,625 <0,8 wird als halber Tag berechnet
att_rule_countConvertDayRemark4=Benchmark für die Umrechnung der Anzahl der Tage: Die im Zeitraum erfasste Arbeitstage funktionieren nicht;
att_rule_countConvertDayRemark5=Als Arbeitstage gezählt: Nur begrenzt auf die Anzahl der Abwesenheitstage, solange in jedem Zeitraum Fehlzeiten vorliegen, wird die Abwesenheitsdauer anhand der Anzahl der Arbeitstage im Zeitraum berechnet;
att_rule_baseRuleSmartFindRemark1=Die längste Zeit: Berechnen Sie gemäß dem Kartenpunkt des Tages die Arbeitszeit für jede Schicht des Tages und ermitteln Sie die Schicht mit der längsten effektiven Zeit des Tages;
att_rule_baseRuleSmartFindRemark2=Die geringste Ausnahme: Berechnen Sie anhand der Kartenpunkte des Tages die Anzahl der Ausnahme, die jeder Schicht des Tages entsprechen, finden Sie die Schicht mit den wenigsten Ausnahmen am Tag, um die Arbeitszeit zu berechnen;
att_rule_baseRuleHourValidator=Die halbstündigen Beurteilungsminuten dürfen nicht größer oder gleich den einstündigen Beurteilungsminuten sein!
att_rule_baseRuleDayValidator=Die halbtägige Beurteilungsdauer darf nicht größer oder gleich der 1-tägigen Beurteilungsdauer sein!
att_rule_overtimeWarning=Die maximale Überstundendauer darf nicht geringer als die minimale einzelne Überstundendauer sein!
att_rule_noSignInCountType=Nicht Eingecheckt als
att_rule_absent=Abwesenheit
att_rule_earlyLeave=Früher gehen
att_rule_noSignOffCountType=Nicht Ausgecheckt als
att_rule_minutes=Minute
att_rule_noSignInCountLateMinute=Nicht Eingecheckt als späte Minuten gezeichnet
att_rule_noSignOffCountEarlyMinute=Nicht Ausgecheckt als frühzeitige Minuten gezeichnet
att_rule_incomplete=Unvollständig
att_rule_noCheckInIncomplete=Nicht Eingecheckt ist unvollständig
att_rule_noCheckOutIncomplete=Nicht Ausgecheckt ist unvollständig
att_rule_lateMinuteWarning=Nicht Eingecheckt als späte Minuten sollen größer als 0 und kleiner als der längste Zeitraum sein
att_rule_earlyMinuteWarning=Nicht Ausgecheckt als frühzeitige Minuten sollen größer als 0 und kleiner als der längste Zeitraum sein
att_rule_baseRuleNoSignInCountLateMinuteRemark=Wenn das Nicht-Einchecken als spät gezählt wird, wird das Nicht-Einchecken für N Minuten als verspätet gezählt
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=Wenn keine Abmeldung als vorzeitiger Abflug aufgezeichnet wird, wird als nicht vorzeitiger Abflug N Minuten lang als vorzeitiger Abflug aufgezeichnet
#节假日
att_holiday_placeholderNo=Es wird empfohlen, mit H zu beginnen, z. B. mit H1
att_holiday_placeholderName=Es wird empfohlen, den Namen Jahr + Feiertag zu verwenden, z. B. 1. Mai 2017
att_holiday_dayNumber=Tage
att_holiday_validDate_msg=In dieser Zeit gibt es bereits Feiertage
#假种
att_leaveType_leaveThing=Diensturlaub
att_leaveType_leaveMarriage=Hochzeitsurlaub
att_leaveType_leaveBirth=Mutterschaftsurlaub
att_leaveType_leaveSick=Krankenstand
att_leaveType_leaveAnnual=Jahresurlaub
att_leaveType_leaveFuneral=Beerdigung Abschied
att_leaveType_leaveHome=Familienurlaub
att_leaveType_leaveNursing=Stillurlaub
att_leaveType_isDeductWorkLong=Arbeitszeiten abziehen
att_leaveType_placeholderNo=Es wird empfohlen, mit L zu beginnen, z. B. L1
att_leaveType_placeholderName=Es wird empfohlen, mit einem Urlaubsnamen zu enden, z. B. Hochzeitsurlaub
#定时计算
att_timingcalc_timeCalcFrequency=Berechnungsintervall
att_timingcalc_timeCalcInterval=Berechnungszeit
att_timingcalc_timeSet=Einstellung der Berechnungszeit
att_timingcalc_timeSelect=Bitte wählen Sie eine Zeit
att_timingcalc_optionTip=Es muss mindestens eine gültige Berechnung der täglichen Anwesenheitszeit aufbewahrt werden
#自动导出报表
att_autoExport_reportType=Berichtstyp
att_autoExport_fileType=Dateityp
att_autoExport_fileName=Dateiname
att_autoExport_fileDateFormat=Datumsformat
att_autoExport_fileTimeFormat=Zeitformat
att_autoExport_fileContentFormat=Inhaltsformat
att_autoExport_fileContentFormatTxt=Beispiel: {deptName}00{personPin}01{personName}02{attDatetime}03\r\n
att_autoExport_timeSendFrequency=Sendefrequenz
att_autoExport_timeSendInterval=Zeitintervall
att_autoExport_emailType=Typ des E-Mail-Empfängers
att_autoExport_emailRecipients=Mail-Empfänger
att_autoExport_emailAddress=E-Mail-Addresse
att_autoExport_emailExample=Beispiel: <EMAIL>,<EMAIL>
att_autoExport_emailSubject=Mail-Titel
att_autoExport_emailContent=Mail-Text
att_autoExport_field=Feld
att_autoExport_fieldName=Feldname
att_autoExport_fieldCode=Feldcodierung
att_autoExport_reportSet=Berichtseinstellung
att_autoExport_timeSet=Einstellung der Sendezeit für E-Mail
att_autoExport_emailSet=Email Einstellung
att_autoExport_emailSetAlert=Bitte geben Sie die E-Mail-Adresse ein
att_autoExport_emailTypeSet=Empfängereinstellung
att_autoExport_byDay=Täglich
att_autoExport_byMonth=Monatlich
att_autoExport_byPersonSet=Nach Person einstellen
att_autoExport_byDeptSet=Nach Abteilung einstellen
att_autoExport_byAreaSet=Nach Bereich einstellen
att_autoExport_emailSubjectSet=Titeleinstellung
att_autoExport_emailContentSet=Texteinstellung
att_autoExport_timePointAlert=Bitte wählen Sie die richtige Sendezeit
att_autoExport_lastDayofMonth=Der letzte Tag jedes Monats
att_autoExport_firstDayofMonth=1. Tag eines jedes Monats
att_autoExport_dayofMonthCheck=Bestimmtes Datum
att_autoExport_dayofMonthCheckAlert=Bitte wählen Sie ein bestimmtes Datum
att_autoExport_chooseDeptAlert=Bitte wählen Sie eine Abteilung!
att_autoExport_sendFormatSet=Sendemethode einstellen
att_autoExport_sendFormat=Sendemethode
att_autoExport_mailFormat=E-Mail-Sendemethode
att_autoExport_ftpFormat=ftp Sendemethode
att_autoExport_sftpFormat=sftp Sendemethode
att_autoExport_ftpUrl=ftp Serveradresse
att_autoExport_ftpPort=ftp Server Port
att_autoExport_ftpTimeSet=ftp Einstellung der Sendezeit
att_autoExport_ftpParamSet=ftp Parametereinstellung
att_autoExport_ftpUsername=ftp Benutzername
att_autoExport_ftpPassword=ftp Passwort
att_autoExport_correctFtpParam=Bitte geben Sie die ftp-Parameter korrekt ein
att_autoExport_correctFtpTestParam=Bitte testen Sie die Verbindung, um sicherzustellen, dass die Kommunikation normal ist
att_autoExport_inputFtpUrl=Bitte geben Sie die Serveradresse ein
att_autoExport_inputFtpPort=Bitte geben Sie den Server-Port ein
att_autoExport_ftpSuccess=Erfolgreiche Verbindung
att_autoExport_ftpFail=Bitte überprüfen Sie, ob die Parametereinstellung falsch ist
att_autoExport_validFtp=Bitte geben Sie eine gültige Serveradresse ein
att_autoExport_validPort=Bitte geben Sie einen gültigen Server-Port ein
att_autoExport_selectExcelTip=Wählen Sie EXCEL als Dateityp und das Inhaltsformat besteht aus allen Feldern!
#=====================================================================
#时间段
att_timeSlot_periodType=Zeitraumtyp
att_timeSlot_normalTime=Normaler Zeitraum
att_timeSlot_elasticTime=Flexibler Zeitraum
att_timeSlot_startSignInTime=Check-in Zeit starten
att_timeSlot_toWorkTime=Arbeitszeit
att_timeSlot_endSignInTime=Check-in Zeit beenden
att_timeSlot_allowLateMinutes=Minuten dürfen zu spät sein
att_timeSlot_isMustSignIn=Muss sich anmelden
att_timeSlot_startSignOffTime=Abreisezeit starten
att_timeSlot_offWorkTime=Off Stunden
att_timeSlot_endSignOffTime=Abreisezeit beenden
att_timeSlot_allowEarlyMinutes=Minuten dürfen vorzeitig abreisen
att_timeSlot_isMustSignOff=Muss mich abmelden
att_timeSlot_workingHours=Arbeitszeit (Minute)
att_timeSlot_isSegmentDeduction=Zwischenabzug
att_timeSlot_startSegmentTime=Die Zwischenzeit starten
att_timeSlot_endSegmentTime=Die Zwischenzeit beenden
att_timeSlot_interSegmentDeduction=Zwischenabzug (Minute)
att_timeSlot_markWorkingDays=Als Arbeitstage gezählt
att_timeSlot_isAdvanceCountOvertime=Ob Sie im Voraus Überstunden machen sollen
att_timeSlot_signInAdvanceTime=Frühe Check-in-Zeit
att_timeSlot_isPostponeCountOvertime=Ob Sie die Überstunden verschieben sollen
att_timeSlot_signOutPosponeTime=Späte Check-out-Zeit
att_timeSlot_isCountOvertime=Ob Sie die Überstunden zählen sollen
att_timeSlot_timeSlotLong=Die Arbeitszeit muss dem in den Regeln festgelegten Zeit- und Anwesenheitsintervall entsprechen:
att_timeSlot_alertStartSignInTime=Die Check-in-Zeit soll kürzer als die Arbeitszeit sein
att_timeSlot_alertEndSignInTime=Die Arbeitszeit soll kürzer als die End-Check-in-Zeit sein
att_timeSlot_alertStartSignInAndEndSignIn=Die End-Check-In-Zeit soll kürzer als die Start-Check-Out-Zeit sein
att_timeSlot_alertStartSignOffTime=Die Start-Check-out-Zeit soll kürzer als die Ausschaltzeit sein
att_timeSlot_alertEndSignOffTime=Die Ausschaltzeit ist kürzer als die End-Check-Out-Zeit
att_timeSlot_alertStartUnequalEnd=Die Startintervallzeit kann nicht gleich der Endintervallzeit sein
att_timeSlot_alertStartSegmentTime=Die Startintervallzeit ist kürzer als die Endintervallzeit sein
att_timeSlot_alertStartAndEndTime=Die Arbeitszeit soll kürzer als die Startintervallzeit sein
att_timeSlot_alertEndAndoffWorkTime=Die Endintervallzeit soll kürzer als die Ausschaltzeit sein
att_timeSlot_alertToWorkLesserAndEndTimeLesser=Die Arbeitszeit soll kürzer als die Startintervallzeit und die Endintervallzeit kürzer als die Ausschaltzeit sein
att_timeSlot_alertLessSignInAdvanceTime=Die frühe Check-in-Zeit soll kürzer als die Arbeitszeit sein
att_timeSlot_alertMoreSignInAdvanceTime=Die Anzahl der Überstunden vor der Anmeldung soll geringer als die Anzahl der Minuten vor der Arbeit sein
att_timeSlot_alertMoreSignOutPosponeTime=Die Anzahl der Überstunden nach der Abmeldung soll geringer als die Anzahl der Minuten nach der Arbeit sein
att_timeSlot_alertLessSignOutPosponeTime=Die späte Check-out-Zeit soll länger als die Ausschaltzeit sein
att_timeSlot_time=Bitte geben Sie das richtige Zeitformat ein
att_timeSlot_alertMarkWorkingDays=Die Anzahl der Arbeitstage darf nicht leer sein!
att_timeSlot_placeholderNo=Es wird empfohlen, mit T zu beginnen, z. B. T01
att_timeSlot_placeholderName=Es wird empfohlen, mit T zu beginnen oder mit einem Zeitraum zu enden
att_timeSlot_beforeToWork=Vor der Arbeit
att_timeSlot_afterToWork=Nach der Arbeit
att_timeSlot_beforeOffWork=Vor dem Feierabend
att_timeSlot_afterOffWork=Nach dem Feierabend
att_timeSlot_minutesSignInValid=Anmeldung gültig innerhalb von Minuten
att_timeSlot_toWork=Geh zur Arbeit
att_timeSlot_offWork=Feierabend
att_timeSlot_minutesSignInAsOvertime=Anmeldung als Überstunde vor Minuten
att_timeSlot_minutesSignOutAsOvertime=Überstunde nach Minuten
att_timeSlot_minOvertimeMinutes=Minimale Überstunden
att_timeSlot_enableWorkingHours=Gibt an, ob die Arbeitszeit aktiviert werden soll
att_timeSlot_eidtTimeSlot=Zeitraum bearbeiten
att_timeSlot_browseBreakTime=Pausen durchsuchen
att_timeSlot_addBreakTime=Eine Pause hinzufügen
att_timeSlot_enableFlexibleWork=Flexible Arbeit aktivieren
att_timeSlot_advanceWorkMinutes=Kann früh arbeiten
att_timeSlot_delayedWorkMinutes=Kann spät arbeiten
att_timeSlot_advanceWorkMinutesValidMsg1=Die Anzahl der Minuten vor der Arbeit ist größer als die Anzahl der Minuten, die Sie im Voraus zur Arbeit gehen können
att_timeSlot_advanceWorkMinutesValidMsg2=Die Anzahl der Minuten, die Sie im Voraus zur Arbeit gehen können, ist geringer als die Anzahl der Minuten vor der Arbeit
att_timeSlot_advanceWorkMinutesValidMsg3=Die Anzahl der Minuten, die Sie im Voraus zur Arbeit gehen können, muss geringer oder gleich der Anzahl der Minuten sein, die Sie vor der Arbeit als Überstunde einchecken können
att_timeSlot_advanceWorkMinutesValidMsg4=Die Anzahl der Minuten, die Sie vor der Arbeit als Überstunde einchecken können, muss größer oder gleich der Anzahl der Minuten sein, die Sie im Voraus zur Arbeit gehen können
att_timeSlot_delayedWorkMinutesValidMsg1=Die Anzahl der Minuten nach der Arbeit ist größer als die Anzahl der Minuten, die verzögert werden können
att_timeSlot_delayedWorkMinutesValidMsg2=Die Anzahl der Minuten, die verzögert werden können, ist geringer als die Anzahl der Minuten nach der Arbeit
att_timeSlot_delayedWorkMinutesValidMsg3=Die Anzahl der Minuten, die verzögert werden können, ist geringer oder gleich der Anzahl der Minuten, die nach der Abmeldung als Überstunden gezählt werden können
att_timeSlot_delayedWorkMinutesValidMsg4=Die Anzahl der Minuten, die nach der Abmeldung als Überstunden gezählt werden können, soll größer oder gleich der Anzahl der Minuten sein, die verzögert werden können
att_timeSlot_allowLateMinutesValidMsg1=Die Anzahl der Minuten, die zu spät sein dürfen, ist weniger als die Anzahl der Minuten nach der Arbeit
att_timeSlot_allowLateMinutesValidMsg2=Die Anzahl der Minuten nach der Arbeit ist größer als die Anzahl der Minuten, die zu spät sein dürfen
att_timeSlot_allowEarlyMinutesValidMsg1=Die Anzahl der Minuten, die früher gehen dürfen, ist weniger als die Anzahl der Minuten vor dem Feierabend
att_timeSlot_allowEarlyMinutesValidMsg2=Die Anzahl der Minuten vor dem Feierabend soll länger als die Anzahl der Minuten, die früher gehen dürfen, sein
att_timeSlot_timeOverlap={0}überschneidet mit{1}Zeit, bitte ändern Sie den ausgewählten Zeitraum!
att_timeSlot_atLeastOne=Mindestens 1 Ruhezeit!
att_timeSlot_mostThree=Bis zu 3 Ruhezeiten!
att_timeSlot_canNotEqual=Die Startzeit der Ruhezeit darf nicht gleich der Endzeit sein!
att_timeSlot_shoudInWorkTime=Bitte stellen Sie sicher, dass die Ruhezeit innerhalb der Arbeitszeit liegt!
att_timeSlot_repeatBreakTime=Wiederholen Sie die Ruhezeit!
att_timeSlot_toWorkLe=Die Arbeitszeit soll kürzer als die Mindeststartzeit der ausgewählten Ruhezeit sein:
att_timeSlot_offWorkGe=Die Feierabendzeit soll länger als die Mindeststartzeit der ausgewählten Ruhezeit sein:
att_timeSlot_crossDays_toWork=Die Mindeststartzeit der Ruhezeit soll innerhalb des Zeitraums liegen:
att_timeSlot_crossDays_offWork=Die maximale Endzeit der Ruhezeit soll innerhalb des Zeitraums liegen:
att_timeSlot_allowLateMinutesRemark=Schlag vom Beginn der Arbeitszeit bis zu den Minuten, die zu spät sein dürfen, gilt als normalen Arbeitsschlag
att_timeSlot_allowEarlyMinutesRemark=Eine frühzeitige Abmeldung innerhalb der Minuten, die bis zum Ende der Arbeitszeit erfolgen dürfen, gilt als normale Freizeit
att_timeSlot_isSegmentDeductionRemark=Gibt an, ob die Ruhezeit innerhalb des Zeitraums abgezogen werden soll
att_timeSlot_attEnableFlexibleWorkRemark1=Bei flexibler Arbeit darf die Anzahl der späten und frühen Minuten nicht eingestellt werden
att_timeSlot_afterToWorkRemark=Die Anzahl der Minuten nach der Arbeit entsprecht der Anzahl der Minuten, die verzögert werden können
att_timeSlot_beforeOffWorkRemark=Die Anzahl der Minuten vor dem Feierabend entspricht der Anzahl der Minuten, die Sie im Voraus zur Arbeit gehen können
att_timeSlot_attEnableFlexibleWorkRemark2=Die Anzahl der Minuten nach dem Feierabend ist größer oder gleich der Anzahl der Feierabendzeit + der verzögerten Arbeitszeit
att_timeSlot_attEnableFlexibleWorkRemark3=Die Anzahl der Minuten, die Sie im Voraus zur Arbeit gehen können, müssen kleiner oder gleich Anzahl der in N Arbeitsminuten aufgezeichneten Überstundenminuten sein
att_timeSlot_attEnableFlexibleWorkRemark4=Die Anzahl der Minuten, die verzögert werden können, müssen kleiner oder gleich Anzahl der in N Feierabendminuten aufgezeichneten Überstundenminuten sein
att_timeSlot_attBeforeToWorkAsOvertimeRemark=Beispiel: Für eine Schicht von 9 Uhr, melden Sie sich 60 Minuten vor der Arbeit an und zählen dies als Überstunden, melden Sie sich dann vor 8 bis 8 Uhr an, als Überstunden zu zählen
att_timeSlot_attAfterOffWorkAsOvertimeRemark=Beispiel: Für einen Feierabend von 18 Uhr, melden Sie sich 60 Minuten nach dem Feierabend ab und zählen dies als Überstunden, melden Sie sich dann von 19 Uhr bis Abmeldungszeit ab, als Überstunden zu zählen
att_timeSlot_longTimeValidRemark=Die gültige Kartensignaturzeit nach der Arbeit und die gültige Kartensignaturzeit vor der Arbeit dürfen sich innerhalb des Zeitraums nicht überschneiden!
att_timeSlot_advanceWorkMinutesValidMsg5=Die gültige Anzahl der Minuten vor der Arbeit ist größer als die Anzahl der Minuten, die Sie im Voraus zur Arbeit gehen können
att_timeSlot_advanceWorkMinutesValidMsg6=Die Anzahl der Minuten, die Sie im Voraus zur Arbeit gehen können, soll geringer als die gültige Anzahl der Minuten vor der Arbeit, sein
att_timeSlot_delayedWorkMinutesValidMsg5=Die gültige Anzahl der Minuten nach der Arbeit ist größer als die Anzahl der Minuten, die verzögert werden können
att_timeSlot_delayedWorkMinutesValidMsg6=Die Anzahl der Minuten, die verzögert werden können, soll geringer als die gültige Anzahl der Minuten nach der Arbeit, sein
att_timeSlot_advanceWorkMinutesValidMsg7=Die Check-in-Zeit vor der Arbeit kann sich nicht mit der Check-out-Zeit nach dem Aussteigen am Vortag überschneiden
att_timeSlot_delayedWorkMinutesValidMsg7=Die Check-out-Zeit nach der Arbeit darf sich nicht mit der Check-in-Zeit vor der Arbeit am nächsten Tag überschneiden
att_timeSlot_maxOvertimeMinutes=Begrenzen Sie die maximalen Überstunden
#班次
att_shift_basicSet=Grundeinstellung
att_shift_advancedSet=Erweiterte Einstellung
att_shift_type=Schichttyp
att_shift_name=Schichtname
att_shift_regularShift=Regelmäßige Schicht
att_shift_flexibleShift=Flexible Schicht
att_shift_color=Farbe
att_shift_periodicUnit=Einheit
att_shift_periodNumber=Periode
att_shift_startDate=Anfangsdatum
att_shift_startDate_firstDay=Anfangsdatum der Periode
att_shift_isShiftWithinMonth=Ob innerhalb eines Monats gearbeitet werden soll
att_shift_attendanceMode=Zeiterfassungsart
att_shift_shiftNormal=Schlag nach normaler Schicht
att_shift_oneDayOneCard=Wischen Sie einmal am Tag über eine gültige Karte
att_shift_onlyBrushTime=Nur die Kartenzeit zählen      
att_shift_notBrushCard=Wischfrei
att_shift_overtimeMode=Art der Überstunde
att_shift_autoCalc=Automatische Computerberechnung
att_shift_mustApply=Überstunde muss beantragen
att_shift_mustOvertime=Überstunde muss gemacht werden ansonsten als Fehlzeit
att_shift_timeSmaller=Die Dauer ist kleiner
att_shift_notOvertime=Nicht als Überstunde gezählt
att_shift_overtimeSign=Überstundenmarke
att_shift_normal=Täglich
att_shift_restday=Freier Tag
att_shift_timeSlotDetail=Details zum Zeitraum
att_shift_doubleDeleteTimeSlot=Doppelklicken Sie auf die Schichtzeitraum, um den zeitraum zu löschen
att_shift_addTimeSlot=Zeitraum hinzufügen
att_shift_cleanTimeSlot=Zeitraum leeren
att_shift_NO=Nr.
att_shift_notAll=Alles wiederufen
att_shift_notTime=Wenn das Kontrollkästchen Details zum Zeitraum kann nicht aktiviert werden, bedeutet es, dass die Zeiträume überschneiden.
att_shift_notExistTime=Es gibt keinen solchen Zeitraum!
att_shift_cleanAllTimeSlot=Bestätigen Sie den Zeitraum zum Leeren der ausgewählten Schicht?
att_shift_pleaseCheckBox=Bitte aktivieren Sie das Kontrollkästchen in der linken Liste, das der aktuellen Anzeigezeit auf der rechten Seite entspricht
att_shift_pleaseUnit=Bitte geben Sie die Zykluseinheit und die Zykluszahl ein
att_shift_pleaseAllDetailTimeSlot=Bitte wählen Sie Zeitraum Details aus
att_shift_placeholderNo=Es wird empfohlen, mit S zu beginnen, z. B. S01
att_shift_placeholderName=Es wird empfohlen, mit S zu beginnen oder mit einer Schicht zu enden 
att_shift_workType=Auftragstyp
att_shift_normalWork=Normale Arbeit
att_shift_holidayOt=Überstunden an Feiertagen
att_shift_attShiftStartDateRemark=Beispiel: Das Startdatum des Zyklus ist Nr. 22 mit drei Tagen als Zyklus, dann gehen Nr. 22/23/24 in Schicht A/B/C und Nr. 19/20/21 in Schicht A/B/C und so weiter.
att_shift_isShiftWithinMonthRemark1=Wenn die Schicht innerhalb eines Monats liegt, wird der Zyklus nur bis zum letzten Tag jedes Monats durchgeführt und nicht über Monate hinweg kontinuierlich geplant.
att_shift_isShiftWithinMonthRemark2=Wenn die Schicht nicht innerhalb des Monats liegt, wird der Zyklus bis zum letzten Tag jedes Monats fortgesetzt, und wenn der Zyklus nicht beendet ist, wird er weiterhin bis zum nächsten Monat geplant und so weiter.
att_shift_workTypeRemark1=Während der normalen Arbeit, außer an Feiertagen, wird die Überstundenmarke im Falle von Feiertagen nicht gezählt. Wenn die Überstunden als Feiertag markiert sind, ist die Überstundendauer die Urlaubsdauer (ohnormale Arbeitszeiten);
att_shift_workTypeRemark2=Wenn Sie an Wochenenden Überstunden leisten, werden standardmäßig Überstunden am Ruhetag angezeigt, und der Computer berechnet automatisch Überstunden, die Überstunden müssen nicht beantragt werden, die Arbeitszeit des Tages wird als Überstunden markiert, und die Anwesenheit wird an Feiertagen nicht gezählt;
att_shift_workTypeRemark3=Wenn Sie an Feiertagen Überstunden leisten, werden standardmäßig Überstunden am Feiertag angezeigt, und der Computer berechnet automatisch Überstunden, die Überstunden müssen nicht beantragt werden, und die Arbeitszeit des Tages wird als Überstunden markiert;
att_shift_attendanceModeRemark1=Mit Ausnahme des normalen Kartenlesens per Schicht wird die frühe oder verspätete Überstunde nicht gezählt, zum Beispiel:
att_shift_attendanceModeRemark2=1.Es ist kein Check-in erforderlich oder eine gültige Karte wird einmal am Tag verwendet, es werden keine Überstunden gezählt;
att_shift_attendanceModeRemark3=2.Arbeitstyp: normale Arbeit, Anwesenheitsmethode: kein Kartenlesen, dann wird die Schichtzeit des Tages als effektive Arbeitszeit angesehen;
att_shift_periodStartMode=Zyklusstarttyp
att_shift_periodStartModeByPeriod=Anfangsdatum der Periode
att_shift_periodStartModeBySch=Startdatum des Schichtplans
att_shift_addTimeSlotToShift=Gibt an, ob das Zeitfenster dieser Schicht hinzugefügt werden soll
#=====================================================================
#分组
att_group_editGroup=Bearbeiten Sie das Personal für jede Gruppe
att_group_browseGroupPerson=Durchsuchen Sie Gruppenmitglieder
att_group_list=Gruppenliste
att_group_placeholderNo=Es wird empfohlen, mit G zu beginnen, z. B. G1
att_group_placeholderName=Es wird empfohlen, mit G zu beginnen oder mit Gruppe zu enden
att_widget_deptHint=Hinweis: Importieren Sie alle Mitarbeiter der ausgewählten Abteilung
att_widget_searchType=Bedingte Abfrage
att_widget_noPerson=Niemand ausgewählt
#分组排班
#部门排班
att_deptSch_existsDept=In dieser Abteilung gibt es einen Abteilungsplan, und diese Abteilung darf nicht gelöscht werden.
#人员排班
att_personSch_view=Mitarbeiterplan anzeigen
#临时排班
att_schedule_type=Schichttyp
att_schedule_tempType=Temporärer Typ
att_schedule_normal=Gewöhnlicher Plan
att_schedule_intelligent=Intelligenter Plan
att_tempSch_scheduleType=Schichttyp
att_tempSch_startDate=Anfangsdatum
att_tempSch_endDate=Enddatum
att_tempSch_attendanceMode=Zeiterfassungsart
att_tempSch_overtimeMode=Art der Überstunde
att_tempSch_overtimeRemark=Überstundenmarke
att_tempSch_existsDept=In dieser Abteilung gibt es eine temporäre Planung, und diese Abteilung darf nicht gelöscht werden.
att_schedult_opAddTempSch=Temporäre Planung hinzufügen
att_schedule_cleanEndDate=Endzeit leeren
att_schedule_selectOne=In gewöhnlichem Plan kann nur eine Schicht wählen!
att_schedule_selectPerson=Bitte zuerst Personal auswählen!
att_schedule_selectDept=Bitte zuerst die Abteilung auswählen!
att_schedule_selectGroup=Bitte zuerst die Gruppe auswählen!
att_schedule_selectOneGroup=Es kann nur eine Gruppe ausgewählt werden!
att_schedule_arrange=Bitte wählen Sie eine Schicht!
att_schedule_leave=Urlaub
att_schedule_trip=Reise
att_schedule_out=Hinausgehen
att_schedule_off=Freizeit
att_schedule_makeUpClass=Ergänzung zur Arbeit
att_schedule_class=Einstellung
att_schedule_holiday=Feiertag
att_schedule_offDetail=Urlaub anpassen
att_schedule_makeUpClassDetail=Ergänzung zur Arbeit
att_schedule_classDetail=Einstellung
att_schedule_holidayDetail=Feiertage
att_schedule_noSchDetail=Nicht geplant
att_schedule_normalDetail=Normal
att_schedule_normalSchInfo=Die Schicht ist in der Mitte: Die Schicht überschreitet keine Tage
att_schedule_multipleInterSchInfo=Durch Kommas getrennte Schichten: mehrere Tagesschichten
att_schedule_inderSchFirstDayInfo=Rückwärtsverschiebung von Schichten über Gitter: Tagesübergreifende Schichten werden als erster Tag gezählt
att_schedule_inderSchSecondDayInfo=Vorwärtsverschiebung von Schichten über Gitter: Tagesübergreifende Schichten werden als zweiter Tag gezählt
att_schedule_timeConflict=Konflikt mit dem bestehenden Planungszeitraum, darf nicht gespeichert werden!
#=====================================================================
att_excp_notExisetPerson=Die Person existiert nicht!
att_excp_leavePerson=Rücktritt der Person!
#补签单
att_sign_signTime=Unterzeichnungszeit
att_sign_signDate=Unterzeichnungsdatum
#请假
att_leave_arilName=Name des Urlaubstyps
att_leave_image=Foto der Feiertagsliste
att_leave_imageShow=Kein bild
att_leave_imageType=Fehlermeldung: Das Format ist falsch, die unterstützte Bildformate sind: JPEG, GIF, PNG!
att_leave_imageSize=Fehlermeldung: Das ausgewählte Bild ist zu groß, die Bildgröße unterstützt bis zu 4M!
att_leave_leaveLongDay=Urlaubszeit (Tag)
att_leave_leaveLongHour=Urlaubszeit (Stunde)
att_leave_leaveLongMinute=Urlaubszeit (Minute)
att_leave_endNoLessAndEqualStart=Die Endzeit darf nicht kleiner oder gleich der Startzeit sein
att_leave_typeNameNoExsists=Falscher Klassenname existiert nicht
att_leave_startNotNull=Die Startzeit darf nicht leer sein
att_leave_endNotNull=Die Endzeit darf nicht leer sein
att_leave_typeNameConflict=Der Name des gefälschten Typs steht in Konflikt mit dem Namen des Anwesenheitsstatus
#出差
att_trip_tripLongDay=Reisezeit (Tag)
att_trip_tripLongMinute=Reisezeit (Minute)
att_trip_tripLongHour=Reisezeit (Stunde)
#外出
att_out_outLongDay=Auszeit (Tag)
att_out_outLongMinute=Auszeit (Minute)
att_out_outLongHour=Auszeit (Stunde)
#加班
att_overtime_type=Überstundentyp
att_overtime_normal=Normale Überstunde
att_overtime_rest=Überstunde an Ruhetagen
att_overtime_overtimeLong=Überstundendauer (Minute)
att_overtime_overtimeHour=Überstundendauer (Stunde)
att_overtime_notice=Die Beantragungszeit für Überstunde darf einen Tag nicht überschreiten!
att_overtime_minutesNotice=Die Beantragungszeit für Überstunde kann nicht kürzer als die kürzeste Überstundendauer sein!
#调休补班
att_adjust_type=Einstellungstyp
att_adjust_adjustDate=Einstellungsdatum
att_adjust_shiftName=Ergänzungsschicht
att_adjust_selectClass=Bitte wählen Sie den Namen der Ergänzungsschicht!
att_shift_notExistShiftWorkDate=Das Einstellungsdatum liegt nicht im Arbeitsdatum der Ergänzungsschicht und kann nicht hinzugefügt werden
att_adjust_shiftPeriodStartMode=Für die Ergänzungsschicht ausgewählte Schicht, wenn das Startdatum geplant ist, ist die Standardeinstellung die 0.
att_adjust_shiftNameNoNull=Make-up-Schichten dürfen nicht leer sein
att_adjust_shiftNameNoExsist=Make-up-Schicht existiert nicht
#调班
att_class_type=Einstellungstyp
att_class_sameTimeMoveShift=Einstellung am selben Tag für eine Person
att_class_differenceTimeMoveShift=Einstellung am unterschiedlichen Tag für eine Person
att_class_twoPeopleMove=Die beide tauschen
att_class_moveDate=Datum tauschen
att_class_shiftName=Name der Einstellungsschicht
att_class_moveShiftName=Name der getauschten Schicht
att_class_movePersonPin=Nummer der getauschten Person
att_class_movePersonName=Name der getauschten Person
att_class_movePersonLastName=Familienname der getauschten Person
att_class_moveDeptName=Abteilungsname der getauschten Person
att_class_personPin=Die Personalnummer darf nicht leer sein
att_class_shiftNameNoNull=Einstellungsschicht darf nicht leer sein
att_class_personPinNoNull=Nummer der getauschten Person darf nicht leer sein!
att_class_isNotExisetSwapPersonPin=Die Nummer der getauschten Person existiert nicht, bitte nochmal hinzufügen!
att_class_personNoSame=Die Einstellungsperson und die getauschte Person können nicht identisch sein, bitte fügen Sie ihn erneut hinzu!
att_class_outTime=Das Einstellungsdatum und das getauschte Datum dürfen nicht mehr als einen Monat voneinander entfernt sein!
att_class_shiftNameNoExsist=Anpassungsverschiebung existiert nicht
att_class_swapPersonNoExisist=Der Matchmaker existiert nicht
att_class_dateNoSame=Personen werden an unterschiedlichen Daten übertragen, die Daten können nicht gleich sein
#=====================================================================
#节点
att_node_name=Knoten
att_node_type=Knotentyp
att_node_leader=Direkte Führung
att_node_leaderNode=Knoten für direkte Führung
att_node_person=Ernannte Person
att_node_position=Bestimmte Position
att_node_choose=Wählen Sie eine Position
att_node_personNoNull=Das Personal darf nicht leer sein
att_node_posiitonNoNull=Die Position kann nicht leer sein
att_node_placeholderNo=Es wird empfohlen, mit N zu beginnen, z. B. N01
att_node_placeholderName=Es wird empfohlen, mit einer Position oder einem Namen zu beginnen und mit einem Knoten zu enden, z. B. einem Supervisor-Knoten
att_node_searchPerson=Suchkriterien eingeben
att_node_positionIsExist=Die Position ist bereits in den Knotendaten vorhanden, bitte wählen Sie die Position erneut aus
#流程
att_flow_type=Prozessart
att_flow_rule=Prozessregeln
att_flow_rule0=Weniger als oder gleich 1 Tag
att_flow_rule1=Größer als 1 Tag und kleiner oder gleich 3 Tagen
att_flow_rule2=Größer als 3 Tag und kleiner oder gleich 7 Tagen
att_flow_rule3=Größer als 7 Tage
att_flow_node=Knoten prüfen
att_flow_start=Prozess starten
att_flow_end=Prozess beenden
att_flow_addNode=Knoten hinzufügen
att_flow_placeholderNo=Es wird empfohlen, mit F zu beginnen, z. B. F01
att_flow_placeholderName=Es wird empfohlen, mit dem Typ zu beginnen und mit dem Prozess zu enden, z. B. Urlaubsprozess
att_flow_tips=Hinweis: Die Genehmigungssequenz der Knoten verläuft von oben nach unten, und Sie können nach der Auswahl ziehen, um sie zu sortieren.
#申请
att_apply_personPin=Bewerber-Nr.
att_apply_type=Ausnahmetyp
att_apply_flowStatus=Gesamtprozessstatus
att_apply_start=Anwendung einleiten
att_apply_flowing=Während des Prozesses
att_apply_pass=Bestanden
att_apply_over=Beenden
att_apply_refuse=Ablehnen
att_apply_revoke=Widerrufen
att_apply_except=Abnormal
att_apply_view=Details anzeigen
att_apply_leaveTips=Das Personal hat innerhalb dieser Frist Urlaub beantragt!
att_apply_tripTips=Das Personal hat innerhalb dieser Frist Geschäftsreise beantragt!
att_apply_outTips=Das Personal hat innerhalb dieser Frist Hinausgehen beantragt!
att_apply_overtimeTips=Das Personal hat innerhalb dieser Frist Überstunde beantragt!
att_apply_adjustTips=Das Personal hat innerhalb dieser Frist Anpassung der Pausenzeit an die Arbeit beantragt!
att_apply_classTips=Das Personal hat innerhalb dieser Frist Einstellung beantragt!
#审批
att_approve_wait=Zur Genehmigung ausstehend
att_approve_refuse=Nicht bestanden
att_approve_reason=Grund
att_approve_personPin=Nr. des Genehmigers
att_approve_personName=Name des Genehmigers
att_approve_person=Genehmiger
att_approve_isPass=Ob genehmigt
att_approve_status=Aktueller Knotenstatus
att_approve_tips=Zu diesem Zeitpunkt befindet sich bereits ein Datensatz im Prozess, Sie können sich nicht erneut bewerben
att_approve_tips2=Der Prozessknoten wurde nicht konfiguriert, bitte vom Administrator konfigurieren
att_approve_offDayConflicts=Der Antrag auf Übertragung des Urlaubs ist an arbeitsfreien Tagen nicht zulässig
att_approve_shiftConflicts=Ergänzung zur Arbeit ist am Arbeitstag nicht erlaubt
att_approve_shiftNoSch=Ergänzung zur Arbeit ist an nicht geplanten Terminen nicht zulässig
att_approve_classConflicts=Der Antrag auf Einstellung ist an nicht geplanten Terminen nicht zulässig
att_approve_selectTime=Nach Auswahl der Zeit wird der Prozess gemäß den Regeln beurteilt
att_approve_withoutPermissionApproval=Es gibt einen Workflow ohne Genehmigung zur Genehmigung, bitte überprüfen!
#=====================================================================
#考勤计算
att_op_calculation=Anwesenheitsberechnung
att_op_calculation_notice=Die Anwesenheitsdaten werden bereits im Hintergrund berechnet, bitte versuchen Sie es später noch einmal!
att_op_calculation_leave=Einschließlich rücktritter Mitarbeiter
att_statistical_choosePersonOrDept=Bitte wählen Sie eine Abteilung oder Person!
att_statistical_sureCalculation=Sind Sie sicher, dass Sie eine Anwesenheitsberechnung durchführen möchten?
att_statistical_filter=Die Filterbedingungen sind fertig!
att_statistical_initData=Grundinitialisierung abgeschlossen!
att_statistical_exception=Die Initialisierung abnormaler Daten ist abgeschlossen!
att_statistical_error=Anwesenheitsberechnung fehlgeschlagen!
att_statistical_begin=Berechnung starten
att_statistical_end=Berechnung beednen!
att_statistical_noticeTime=Wählbarer Zeitbereich: Die erste zwei Monate bis zum selben Tag!
att_statistical_remarkHoliday=Feiertag
att_statistical_remarkClass=Schicht
att_statistical_remarkNoSch=Ende
att_statistical_remarkRest=Freizeit
#原始记录表
att_op_importAccRecord=Zugriffssteuerungsdatensatz importieren
att_op_importParkRecord=Parkdatensatz importieren
att_op_importInsRecord=Info-Bildschirmdatensatz importieren
att_op_importPidRecord=Zeugendatensatz importieren
att_op_importVmsRecord=Videodatensatz importieren
att_op_importUSBRecord=U-Datensatz importieren
att_transaction_noAccModule=Kein Zugangskontrollmodul!
att_transaction_noParkModule=Kein Parkmodul!
att_transaction_noInsModule=Kein Informationsbildschirmmodul!
att_transaction_noPidModule=Kein Zeugenmodul!
att_transaction_exportRecord=Originaldatensätze exportieren
att_transaction_exportAttPhoto=Anwesenheitsfotos exportieren
att_transaction_fileIsTooLarge=Die exportierte Datei ist zu groß. Bitte beschränken Sie den Datumsbereich
att_transaction_exportDate=Exportdatum
att_statistical_attDatetime=Datum und Uhrzeit der Anwesenheit
att_statistical_attPhoto=Anwesenheitsfoto
att_statistical_attDetail=Anwesenheitsdetails
att_statistical_acc=Zugangskontrollausrüstung
att_statistical_att=Zeiterfassungsgerät
att_statistical_park=Parkausrüstung
att_statistical_faceRecognition=Gesichtserkennungsgerät
att_statistical_app=Mobiltelefonausrüstung
att_statistical_vms=Videogerät
att_statistical_psg=Kanalausstattung
att_statistical_dataSources=Datenquelle
att_transaction_SyncRecord=Anwesenheitslisten synchronisieren
#日打卡详情表
att_statistical_dayCardDetail=Details einfügen
att_statistical_cardDate=Schlagdatum
att_statistical_cardNumber=Schlaganzahl
att_statistical_earliestTime=Früheste Zeit
att_statistical_latestTime=Späteste Zeit
att_statistical_cardTime=Schlagzeit
#请假汇总表
att_statistical_leaveDetail=Beurlaubung Details
#日明细报表
att_statistical_attDate=Datum der Anwesenheit
att_statistical_week=Woche
att_statistical_shiftInfo=Schichtinformation
att_statistical_shiftTimeData=Pendelzeit
att_statistical_cardValidData=Schlagdaten
att_statistical_cardValidCount=Schlaganzahl
att_statistical_lateCount=Anzahl der Verspätung
att_statistical_lateMinute=Minuten der Verspätung
att_statistical_earlyCount=Anzahl frühes Gehens
att_statistical_earlyMinute=Minuten frühes Gehens
att_statistical_countData=Frequenzdaten
att_statistical_minuteData=Minuten Daten
att_statistical_attendance_minute=Anwesenheit (Minute)
att_statistical_overtime_minute=Überstunde (Minute)
att_statistical_unusual_minute=Abnormal (Minute)
#月明细报表
att_monthdetail_should_hour=Soll (Stunde)
att_monthdetail_actual_hour=Ist (Stunde)
att_monthdetail_valid_hour=Gültig (Stunde)
att_monthdetail_absent_hour=Fehlzeit (Stunde)
att_monthdetail_leave_hour=Beurlaubung (Stunde)
att_monthdetail_trip_hour=Dienstreise (Stunde)
att_monthdetail_out_hour=Auszeit (Stunde)
att_monthdetail_should_day=Soll (Tag)
att_monthdetail_actual_day=Ist (Tag)
att_monthdetail_valid_day=Gültig (Tag)
att_monthdetail_absent_day=Fehlzeit (Tag)
att_monthdetail_leave_day=Beurlaubung (Tag)
att_monthdetail_trip_day=Dienstreise (Tag)
att_monthdetail_out_day=Hinausgehen (Tag)
#月统计报表
att_statistical_late_minute=Dauer (Minute)
att_statistical_early_minute=Dauer (Minute)
#部门统计报表
#年度统计报表
att_statistical_should=Soll
att_statistical_actual=Ist
att_statistical_valid=Gültig
att_statistical_numberOfTimes=Anzahl
att_statistical_usually=Täglich
att_statistical_rest=Freie Tage
att_statistical_holiday=Festival
att_statistical_total=Total
att_statistical_month=Statistischer Monat
att_statistical_year=Statistisches Jahr
att_statistical_attendance_hour=Anwesenheit (Stunde)
att_statistical_attendance_day=Anwesenheit (Tag)
att_statistical_overtime_hour=Überstunde (Stunde)
att_statistical_unusual_hour=Abnormal (Stunde)
att_statistical_unusual_day=Abnormal (Tag)
#考勤设备参数
att_deviceOption_query=Geräteparameter anzeigen
att_deviceOption_noOption=Keine Parameterinformation, bitte zuerst die Geräteparameter abrufen
att_deviceOption_name=Name des Parameters
att_deviceOption_value=Parameterwert
att_deviceOption_UserCount=Aktuelle Anzahl des Benutzers
att_deviceOption_MaxUserCount=Maximale Anzahl des Benutzers
att_deviceOption_FaceCount=Aktuelle Anzahl der Gesichtsvorlagen
att_deviceOption_MaxFaceCount=Maximale Anzahl der Gesichtsvorlagen
att_deviceOption_FacePhotoCount=Aktuelle Anzahl der Gesichtsbilder
att_deviceOption_MaxFacePhotoCount=Maximale Anzahl der Gesichtsbilder
att_deviceOption_FingerCount=Aktuelle Anzahl der Fingerabdruckvorlagen
att_deviceOption_MaxFingerCount=Maximale Anzahl der Fingerabdruckvorlagen
att_deviceOption_FingerPhotoCount=Aktuelle Anzahl der Fingerabdruckbilder
att_deviceOption_MaxFingerPhotoCount=Maximale Anzahl der Fingerabdruckbilder
att_deviceOption_FvCount=Aktuelle Anzahl der Vorlagen für Fingervenen
att_deviceOption_MaxFvCount=Maximale Anzahl der Vorlagen für Fingervenen
att_deviceOption_FvPhotoCount=Aktuelle Anzahl der Fingervenenbilder
att_deviceOption_MaxFvPhotoCount=Maximale Anzahl der Fingervenenbilder
att_deviceOption_PvCount=Aktuelle Anzahl der Handflächenvorlagen
att_deviceOption_MaxPvCount=Maximale Anzahl der Handflächenvorlagen
att_deviceOption_PvPhotoCount=Aktuelle Anzahl der Handflächenbilder
att_deviceOption_MaxPvPhotoCount=Maximale Anzahl der Handflächenbilder
att_deviceOption_TransactionCount=Aktuelle Anzahl der Datensätze
att_deviceOption_MaxAttLogCount=Maximale Anzahl der Datensätze
att_deviceOption_UserPhotoCount=Aktuelle Anzahl der Benutzerfotos
att_deviceOption_MaxUserPhotoCount=Maximale Anzahl der Benutzerfotos
att_deviceOption_FaceVersion=Version des Algorithmus zur Erkennung von Gesichts
att_deviceOption_FPVersion=Version des Algorithmus zur Erkennung von Fingerabdrücken
att_deviceOption_FvVersion=Version des Algorithmus zur Erkennung von Fingervenen
att_deviceOption_PvVersion=Version des Algorithmus zur Erkennung von Handflächen
att_deviceOption_FWVersion=Firmware-Version
att_deviceOption_PushVersion=Push-Version
#=====================================================================
#API
att_api_areaCodeNotNull=Die Bereichsnummer darf nicht leer sein
att_api_pinsNotNull=PIN-Daten dürfen nicht leer sein
att_api_pinsOverSize=Die Länge der Pin-Daten darf 500 nicht überschreiten
att_api_areaNoExist=Bereich existiert nicht
att_api_sign=Ergänzende Unterschrift
#=====================================================================
#休息时间段
att_leftMenu_breakTime=Ruhezeit
att_breakTime_startTime=Startzeit
att_breakTime_endTime=Endzeit
#=====================================================================
#导入U盘记录
att_import_uploadFileSuccess=Der Datei-Upload war erfolgreich und die Dateidaten wurden analysiert. Bitte warten ...
att_import_resolutionComplete=Nach Abschluss der Analyse wird die Datenbank aktualisiert.
att_import_snNoExist=Das der importierten Datei entsprechende Anwesenheitsgerät ist nicht vorhanden. Wählen Sie die Datei erneut aus.
att_import_fileName_msg=Anforderungen an das Format des importierten Dateinamens: Der Anfang der Seriennummer des Geräts wird durch einen Unterstrich "_" getrennt, z. B. "3517171600001_attlog.dat".
att_import_notSupportFormat=Dieses Format wird noch nicht unterstützt!
att_import_selectCorrectFile=Bitte wählen Sie die richtige Formatdatei!
att_import_fileFormat=Dateiformat
att_import_targetFile=Zieldatei
att_import_startRow=Kopfzeilennummer
att_import_startRowNote=In der ersten Zeile des Datenformats werden Daten importiert. Überprüfen Sie die Datei vor dem Import.
att_import_delimiter=Trennzeichen
#=====================================================================
#设备操作日志
att_device_op_log_op_type=Betriebscode
att_device_op_log_dev_sn=Seriennummer
att_device_op_log_op_content=Betriebsinhalt
att_device_op_log_operator_pin=Bediener-Nr.
att_device_op_log_operator_name=Name des Bedieners
att_device_op_log_op_time=Betriebszeit
att_device_op_log_op_who_value=Operationsobjektwert
att_device_op_log_op_who_content=Beschreibung des Operationsobjekts
att_device_op_log_op_value1=Operationsobjekt 2
att_device_op_log_op_value_content1=Beschreibung des Operationsobjekts 2
att_device_op_log_op_value2=Operationsobjekt 3
att_device_op_log_op_value_content2=Beschreibung des Operationsobjekts 3
att_device_op_log_op_value3=Operationsobjekt 4
att_device_op_log_op_value_content3=Beschreibung des Operationsobjekts 4
#操作日志的操作类型
att_device_op_log_opType_0=Einschalten
att_device_op_log_opType_1=Ausschalten
att_device_op_log_opType_2=Überprüfung ist fehlgeschlagen
att_device_op_log_opType_3=Alarm
att_device_op_log_opType_4=Rufen Sie das Menü auf
att_device_op_log_opType_5=Einstellung ändern
att_device_op_log_opType_6=Fingerabdruck registrieren
att_device_op_log_opType_7=Passwort registrieren
att_device_op_log_opType_8=HID-Karte registrieren
att_device_op_log_opType_9=Benutzer löschen
att_device_op_log_opType_10=Fingerabdruck löschen
att_device_op_log_opType_11=Passwort löschen
att_device_op_log_opType_12=RF-Karte löschen
att_device_op_log_opType_13=Daten löschen
att_device_op_log_opType_14=MF-Karte erstellen
att_device_op_log_opType_15=MF-Karte registrieren
att_device_op_log_opType_16=MF-Karte eintragen
att_device_op_log_opType_17=MF-Kartenregistrierung löschen
att_device_op_log_opType_18=Inhalt der MF-Karte löschen
att_device_op_log_opType_19=Die Registrierungsdaten auf die Karte verschieben
att_device_op_log_opType_20=Die Daten auf der Karte auf das Gerät kopieren
att_device_op_log_opType_21=Zeit einstellen
att_device_op_log_opType_22=Werkseinstellung
att_device_op_log_opType_23=Eintrags- und Ausgangsdatensätze löschen
att_device_op_log_opType_24=Administratorrechte löschen
att_device_op_log_opType_25=Die Einstellung der Zugriffssteuerungsgruppe ändern
att_device_op_log_opType_26=Die Einstellungen für die Benutzerzugriffskontrolle ändern
att_device_op_log_opType_27=Den Zeitraum für die Zugriffskontrolle ändern
att_device_op_log_opType_28=Die Einstellung für die Entsperrkombination ändern
att_device_op_log_opType_29=Entsperr
att_device_op_log_opType_30=Einen neuen Benutzer registrieren
att_device_op_log_opType_31=Die Fingerabdruckeigenschaft ändern
att_device_op_log_opType_32=Zwangsalarm
att_device_op_log_opType_34=Anti-Sneak
att_device_op_log_opType_35=Anwesenheitsfoto löschen
att_device_op_log_opType_36=Andere Benutzerinformation ändern
att_device_op_log_opType_37=Feiertage
att_device_op_log_opType_38=Daten wiederherstellen
att_device_op_log_opType_39=Daten sichern
att_device_op_log_opType_40=Hochladen von U-Diskette
att_device_op_log_opType_41=Unterladen von U-Diskette
att_device_op_log_opType_42=Verschlüsselung des Anwesenheitsdatensatzes von U-Diskette
att_device_op_log_opType_43=Datensatz nach erfolgreichem Unterladen der U-Diskette löschen
att_device_op_log_opType_53=Ausgangsschalter
att_device_op_log_opType_54=Türmagnet
att_device_op_log_opType_55=Alarm
att_device_op_log_opType_56=Wiederherstellungsparameter
att_device_op_log_opType_68=Benutzerfotos registrieren
att_device_op_log_opType_69=Benutzerfotos ändern
att_device_op_log_opType_70=Benutzername ändern
att_device_op_log_opType_71=Benutzerberechtigung ändern
att_device_op_log_opType_76=Die IP-Adresse der Netzwerkeinstellung ändern
att_device_op_log_opType_77=Die Maske der Netzwerkeinstellung ändern
att_device_op_log_opType_78=Das Gateway der Netzwerkeinstellung ändern
att_device_op_log_opType_79=Den DNS der Netzwerkeinstellung ändern
att_device_op_log_opType_80=Das Kennwort der Verbindungseinstellung ändern
att_device_op_log_opType_81=Die Geräte-ID der Verbindungseinstellung ändern
att_device_op_log_opType_82=Die Adresse des Cloud-Servers ändern
att_device_op_log_opType_83=Den Port des Cloud-Servers ändern
att_device_op_log_opType_87=Die Einstellung für den Zugriffskontrolldatensatz ändern
att_device_op_log_opType_88=Das Zeichen des Gesichtsparameters ändern
att_device_op_log_opType_89=Das Zeichen des Fingerabdrucks ändern
att_device_op_log_opType_90=Das Zeichen der Fingervene ändern
att_device_op_log_opType_91=Das Zeichen des Palmprint-Parameters ändern
att_device_op_log_opType_92=Zeichen für U-Upgrade
att_device_op_log_opType_100=Die RF-Karteninformation ändern
att_device_op_log_opType_101=Gesicht registrieren
att_device_op_log_opType_102=Die Personalberechtigung ändern
att_device_op_log_opType_103=Die Personalberechtigung löschen
att_device_op_log_opType_104=Die Personalberechtigung hinzufügen
att_device_op_log_opType_105=Zugriffssteuerungsdatensatz löschen
att_device_op_log_opType_106=Gesicht löschen
att_device_op_log_opType_107=Personenfoto löschen
att_device_op_log_opType_108=Parameter ändern
att_device_op_log_opType_109=WIFISSID wählen
att_device_op_log_opType_110=Proxy aktivieren
att_device_op_log_opType_111=Proxyip ändern
att_device_op_log_opType_112=Proxy-Port ändern
att_device_op_log_opType_113=Das Personalpasswort ändern
att_device_op_log_opType_114=Gesichtsinformation ändern
att_device_op_log_opType_115=Bedienerkennwort ändern
att_device_op_log_opType_116=Die Einstellung für die Zugriffssteuerung wiederherstellen
att_device_op_log_opType_117=Fehler bei der Eingabe des Bedienerkennworts
att_device_op_log_opType_118=Bedienerkennwortsperre
att_device_op_log_opType_120=Ändern der Länge der Legic Card-Daten
att_device_op_log_opType_121=Fingervene registrieren
att_device_op_log_opType_122=Fingervene ändern
att_device_op_log_opType_123=Fingervene löschen
att_device_op_log_opType_124=Palm registrieren
att_device_op_log_opType_125=Palm ändern
att_device_op_log_opType_126=Palm löschen
#操作对象描述
att_device_op_log_content_pin=Auftragsnummer für Benutzer:
att_device_op_log_content_alarm=Alarm:
att_device_op_log_content_alarm_reason=Alarmursache:
att_device_op_log_content_update_no=Die Seriennummer des Artikels ändern:
att_device_op_log_content_update_value=Artikelwert ändern:
att_device_op_log_content_finger_no=Seriennummer des Fingerabdrucks:
att_device_op_log_content_finger_size=Länge der Fingerabdruckvorlage:
#=====================================================================
#工作流
att_flowable_datetime_to=Bis
att_flowable_todomsg_leave=Beurlaubung genehmigen
att_flowable_todomsg_sign=Ergänzende Unterschrift genehmigen
att_flowable_todomsg_overtime=Überstunde genehmigen
att_flowable_notifymsg_leave=Beurlaubung ankündigen
att_flowable_notifymsg_sign=Ergänzende Unterschrif ankündigen
att_flowable_notifymsg_overtime=Überstunde ankündigen
att_flowable_shift=Schicht:
att_flowable_hour=Stunde
att_flowable_todomsg_trip=Dienstreise genehmigen
att_flowable_notifymsg_trip=Dienstreise ankündigen
att_flowable_todomsg_out=Hinausgehen genehmigen
att_flowable_notifymsg_out=Hinausgehen ankündigen
att_flow_apply=Anwendung
att_flow_applyTime=Anwendungszeit
att_flow_approveTime=Genehmigungszeit
att_flow_operateUser=Prüfer
att_flow_approve=Genehmigen
att_flow_approveComment=Anmerkung
att_flow_approvePass=Genehmigungsergebnisse
att_flow_status_processing=In Prüfung
#=====================================================================
#biotime
att_h5_pers_personIdNull=Die Mitarbeiter-ID darf nicht leer sein
att_h5_attPlaceNull=Der Check-in-Ort darf nicht leer sein
att_h5_attAreaNull=Der Anwesenheitsbereich darf nicht leer sein
att_h5_pers_personNoExist=Mitarbeiternummer existiert nicht
att_h5_signRemarkNull=Die Bemerkung darf nicht leer sein
att_h5_common_pageNull=Paging-Parameter Fehler
att_h5_taskIdNotNull=Die Taskknoten-ID darf nicht leer sein
att_h5_auditResultNotNull=Das Genehmigungsergebnis darf nicht leer sein
att_h5_latLongitudeNull=Breite und Länge dürfen nicht leer sein
att_h5_pers_personIsNull=Mitarbeiter-ID existiert nicht
att_h5_pers_personIsNotInArea=Für diese Person ist kein Bereich eingestellt
att_h5_mapApiConnectionsError=Verbindungsfehler für Map API
att_h5_googleMap=Google Karte
att_h5_gaodeMap=Gaode Karte
att_h5_defaultMap=Standardkarte
att_h5_shiftTime=Planungszeit
att_h5_signTimes=Zeit für ergänzende Unterschrift
att_h5_enterKeyWords=Bitte geben Sie Schlüsselwörter ein:
att_h5_mapSet=Einstellung für Anwesenheitskarte
att_h5_setMapApiAddress=Kartenparameter einstellen
att_h5_MapSetWarning=Das Wechseln der Karte führt dazu, dass der Längen- und Breitengrad der registrierten Adresse des mobilen Endgeräts nicht übereinstimmt. Bitte ändern Sie sie sorgfältig!
att_h5_mapSelect=Kartenauswahl
att_h5_persNoHire=Der Mitarbeiter ist noch nicht beigetreten
att_slef_apiKey=API-KEY
att_self_apiJsKey=API-JS-KEY
att_self_noComputeAtt=Die Anwesenheit wurden nicht berechnet
att_self_noSignRecord=Kein Schlagrekord am Tag
att_self_imageUploadError=Bild-Hochladen fehlgeschlagen
att_self_attSignAddressAreaIsExist=Check-in-Punkte in diesem Bereich
att_self_signRuleIsError=Die aktuelle Zeit für ergänzende Unterschrift liegt nicht innerhalb der zulässigen Verlängerungszeit
att_self_signAcrossDay=Tagesübergreifende Planung kann nicht neu signiert werden!
att_self_todaySignIsExist=Heute gibt es eine ergänzende Unterschrift!
att_self_signSetting=Einstellung für ergänzende Unterschrift
att_self_allowSign=Ergänzende Unterschrift zulassen:
att_self_allowSignSuffix=Anwesenheitsliste innerhalb Tage
att_self_onlyThisMonth=Nur diesen Monat
att_self_allowAcrossMonth=Kreuzmonat erlauben
att_self_thisTimeNoSch=Im aktuellen Zeitraum gibt es keine Zeitpläne!
att_self_revokeReason=Grund für den Widerruf:
att_self_revokeHint=Bitte geben Sie den Grund für den Widerruf innerhalb von 20 Zeichen zur Überprüfung ein
att_self_persSelfLogin=Self-Service-Login für Mitarbeiter
att_self_isOpenSelfLogin=Ob der Self-Service-Login-Zugang für Mitarbeiter aktiviert werden soll
att_self_applyAndWorkTimeOverlap=Bewerbungszeit und Arbeitszeit überschneiden sich
att_apply_DurationIsZero=Bewerbungsdauer ist 0, Bewerbung ist nicht erlaubt
att_sign_mapWarn=Die Karte konnte nicht geladen werden. Überprüfen Sie die Netzwerkverbindung und den KEY-Wert der Karte
att_admin_applyWarn=Betrieb fehlgeschlagen, es gibt Personen, die nicht geplant sind oder deren Anwendungszeit nicht im Rahmen der Planung liegt! ({0})
att_self_getPhotoFailed=Bild existiert nicht
att_self_view=Ansehen
# 二维码
att_param_qrCodeUrl=QR-Code URL
att_param_qrCodeUrlHref=Serveradresse: Port
att_param_appAttQrCode=Anwesenheit QR-Code für mobile Geräte
att_param_timingFrequency=Zeitintervall: 5-59 Minuten oder 1-24 Stunden
att_sign_signTimeNotNull=Zeit für ergänzende Unterschrift darf nicht leer sein
att_apply_overLastMonth=Die Startzeit der Anwendung wurde im letzten Monat überschritten
att_apply_withoutDetail=Keine Prozessdetails
att_flowable_noAuth=Bitte verwenden Sie zum Anzeigen das Superadministratorkonto
att_apply_overtimeOverMaxTimeLong=Die Überstundendauer ist größer als die maximale Überstundendauer
# 考勤设置参数符号
att_other_arrive=√
att_other_late=L
att_other_early=E
att_other_absent=□
att_other_noSignIn=[
att_other_noSignOff=]
att_other_leave=∆
att_other_overtime=OT
att_other_off=○
att_other_classes=●
att_other_trip=T
att_other_out=G
att_other_incomplete=├
att_other_outcomplete=┤
# 服务器下发命令
att_devCmd_submitTime=Sendezeit
att_devCmd_returnedResult=Ergebnis zurückgeben
att_devCmd_returnTime=Zeitpunkt der Rückkehr
att_devCmd_content=Befehlsinhalt
att_devCmd_clearCmd=Befehlsliste löschen
# 实时点名
att_realTime_selectDept=Bitte wählen Sie eine Abteilung aus
att_realTime_noSignPers=Nicht registrierte Person
att_realTime_signPers=Eingecheckt
att_realTime_signMonitor=Anmeldeüberwachung
att_realTime_signDateTime=Eincheckzeit
att_realTime_realTimeSet=Echtzeit-Appelleinstellung
att_realTime_openRealTime=Echtzeit-Appell aktivieren
att_realTime_rollCallEnd=Echtzeit-Appell wird beendet
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=Geplant
att_personSch_cycleSch=Zyklusplanung
att_personSch_cleanCycleSch=Zyklusplan bereinigen
att_personSch_cleanTempSch=Temporären Zeitplan löschen
att_personSch_personCycleSch=Zeitplan für den Personenzyklus
att_personSch_deptCycleSch=Abteilungszyklusplan
att_personSch_groupCycleSch=Gruppenzyklusplanung
att_personSch_personTempSch=Temporärer Zeitplan für Personen
att_personSch_deptTempSch=Temporärer Zeitplan der Abteilung
att_personSch_groupTempSch=Temporärer Gruppenzeitplan
att_personSch_checkGroupFirst=Bitte überprüfen Sie die Gruppe links oder die Person rechts, um zu operieren!
att_personSch_sureDeleteGroup=Sind Sie sicher, {0} und den der Gruppe entsprechenden Zeitplan zu löschen?
att_personSch_sch=Zeitplan
att_personSch_delSch=Zeitplan löschen
#考勤计算
att_statistical_sureAllCalculate=Sind Sie sicher, dass Sie für alle Mitarbeiter eine Anwesenheitsberechnung durchführen?
#异常管理
att_exception_downTemplate=Importvorlage herunterladen
att_exception_signImportTemplate=Angehängte Protokollimportvorlage
att_exception_leaveImportTemplate=Importvorlage verlassen
att_exception_overtimeImportTemplate=Importvorlage für Überstunden
att_exception_adjustImportTemplate=Importvorlage anpassen
att_exception_cellDefault=Nicht erforderliches Feld
att_exception_cellRequired=Erforderliches Feld
att_exception_cellDateTime=Erforderliches Feld, Zeitformat ist JJJJ-MM-TT HH: MM: SS, z. B.: 2020-07-07 08:30:00
att_exception_cellLeaveTypeName=Erforderliches Feld, zum Beispiel: "Freizeiturlaub", "Eheurlaub", "Mutterschaftsurlaub", "Krankheitsurlaub", "Jahresurlaub", "Trauerurlaub", "Heimaturlaub", "Stillurlaub", "Geschäft" Trip ',' Ausgehen 'und so weiter.
att_exception_cellOvertimeSign=Erforderliches Feld, zum Beispiel: 'Normal OT', 'Rest Day OT', 'Holiday OT'
att_exception_cellAdjustType=Erforderliches Feld, zum Beispiel: 'Rest anpassen', 'Anwesenheit anhängen'
att_exception_cellAdjustDate=Erforderliches Feld, Zeitformat ist JJJJ-MM-TT, zum Beispiel: 2020-07-07
att_exception_cellShiftName=Erforderliches Feld, wenn der Anpassungstyp "Attendance anhängen" lautet.
att_exception_refuse=Ablehnen
att_exception_end=Abnormales Ende
att_exception_delete=Löschen
att_exception_stop=Pause
#时间段
att_timeSlot_normalTimeAdd=Normales Zeitfenster hinzufügen
att_timeSlot_elasticTimeAdd=Flexibles Zeitfenster hinzufügen
#班次
att_shift_addRegularShift=Regelmäßige Schicht hinzufügen
att_shift_addFlexibleShift=Flexible Schicht hinzufügen
#参数设置
att_param_notLeaveSetting=Berechnungseinstellung ohne Urlaub
att_param_smallestUnit=Minimale Einheit
att_param_workDay=Arbeitstag
att_param_roundingControl=Rundungssteuerung
att_param_abort=Down (verwerfen)
att_param_rounding=Rundung
att_param_carry=Up (Carry)
att_param_reportSymbol=Berichtsanzeigesymbol
att_param_convertCountValid=Bitte geben Sie eine Zahl ein und es ist nur eine Dezimalstelle zulässig
att_other_leaveThing=Lässig
att_other_leaveMarriage=Ehe
att_other_leaveBirth=Mutterschaft
att_other_leaveSick=Krank
att_other_leaveAnnual=Jährlich
att_other_leaveFuneral=Trauerfall
att_other_leaveHome=Home
att_other_leaveNursing=Stillen
att_other_leavetrip=Geschäft
att_other_leaveout=Out
att_common_schAndRest=Planen und Ausruhen
att_common_timeLongs=Zeitdauer
att_personSch_checkDeptOrPersFirst=Bitte überprüfen Sie die Gruppe auf der linken Seite oder die Person auf der rechten Liste, um zu operieren!
att_personSch_checkCalendarFirst=Bitte wählen Sie zuerst das Datum aus, das Sie planen möchten!
att_personSch_cleanCheck=Prüfung löschen
att_personSch_delTimeSlot=Ausgewählten Zeitraum löschen
att_personSch_repeatTimeSlotNoAdd=Es wird kein sich wiederholender Zeitraum hinzugefügt!
att_personSch_showSchInfo=Zeitplandetails anzeigen
att_personSch_sureToCycleSch=Sind Sie sicher, dass Sie {0} zyklisch planen?
att_personSch_sureToTempSch=Sind Sie sicher, dass Sie {0} vorübergehend planen?
att_personSch_sureToCycleSchDeptOrGroup=Sind Sie sicher, dass Sie alle Mitarbeiter in {0} zyklisch einplanen?
att_personSch_sureToTempSchDeptOrGroup=Sind Sie sicher, dass Sie alle Mitarbeiter vorübergehend in {0} einplanen?
att_personSch_sureCleanCycleSch=Möchten Sie den Zykluszeitplan für {0} wirklich von {1} bis {2} löschen?
att_personSch_sureCleanTempSch=Möchten Sie die temporäre Verschiebung für {0} von {1} nach {2} wirklich löschen?
att_personSch_sureCleanCycleSchDeptOrGroup=Möchten Sie den Zykluszeitplan für alle Mitarbeiter in {0} von {1} bis {2} löschen?
att_personSch_sureCleanTempSchDeptOrGroup=Möchten Sie wirklich den temporären Zeitplan für alle Mitarbeiter in {0} von {1} bis {2} löschen?
att_personSch_today=Heute
att_personSch_timeSoltName=Zeitraumname
att_personSch_export=Personalplanung exportieren
att_personSch_exportTemplate=Temporäre Schichtvorlage für Personal exportieren
att_personSch_import=Temporäre Personalplanung importieren
att_personSch_tempSchTemplate=Temporäre Personalplanungsvorlage
att_personSch_tempSchTemplateTip=Bitte wählen Sie die Start- und Endzeit des Zeitplans aus und laden Sie die Zeitplanvorlage innerhalb dieses Datums herunter
att_personSch_opTip=Bedienungsanleitung
att_personSch_opTip1=1.Sie können das Zeitfenster in ein einzelnes Datum im Kalendersteuerelement ziehen, um einen Zeitplan zu erstellen.
att_personSch_opTip2=2. Doppelklicken Sie im Kalendersteuerelement auf ein einzelnes Datum, um es zu planen.
att_personSch_opTip3=3. Halten Sie im Kalendersteuerelement die Maus gedrückt, um mehrere Termine für den Zeitplan auszuwählen.
att_personSch_schRules=Zeitplanregeln
att_personSch_schRules1=1. Zykluszeitplan: Innerhalb desselben Tages überschreibt der spätere Zeitplan den vorherigen Zeitplan, unabhängig davon, ob es Überlappungen gibt.
att_personSch_schRules2=2. Temporärer Zeitplan: Wenn es innerhalb desselben Tages zu einer Überlappung kommt, wird der vorherige temporäre Zeitplan überschrieben. Wenn es keine Überlappung gibt, existieren sie gleichzeitig.
att_personSch_schRules3=3. Zyklus und Temporär: Wenn es am selben Tag eine Überlappung gibt, überschreibt der Zyklusplan den temporären Zeitplan, und wenn es keine Überlappung gibt, sind sie auch gleichzeitig vorhanden.
att_personSch_schStatus=Zeitplanstatus
#左侧菜单-排班管理
att_leftMenu_schDetails=Zeitplandetails
att_leftMenu_detailReport=Anwesenheitsdetailbericht
att_leftMenu_signReport=Angehängte Protokolldetails
att_leftMenu_leaveReport=Details hinterlassen
att_leftMenu_abnormal=Abnormaler Anwesenheitsbericht
att_leftMenu_yearLeaveSumReport=Jahresurlaubssummenbericht
att_leave_maxFileCount=Sie können höchstens 4 Fotos hinzufügen
#时间段
att_timeSlot_add=Zeitfenster festlegen
att_timeSlot_select=Bitte wählen Sie einen Zeitraum!
att_timeSlot_repeat=Zeitraum "{0}" wiederholt sich!
att_timeSlot_overlapping=Der Zeitraum "{0}" überschneidet sich mit der Arbeitszeit von "{1}"!
att_timeSlot_addFirst=Bitte stellen Sie zuerst den Zeitraum ein!
att_timeSlot_notEmpty=Der der Personalnummer {0} entsprechende Zeitraum darf nicht leer sein!
att_timeSlot_notExist=Der der Personalnummer {0} entsprechende Zeitraum "{1}" existiert nicht!
att_timeSlot_repeatEx=Der Zeitraum "{1}", der der Personalnummer {0} entspricht, überschneidet sich mit der Arbeitszeit von "{2}".
att_timeSlot_importRepeat=Der Zeitraum "{1}", der der Personalnummer {0} entspricht, wird wiederholt
att_timeSlot_importNotPin=Es ist kein Personal mit der Nummer {0} im System!
att_timeSlot_elasticTimePeriod=Personalnummer {0}, kann keinen flexiblen Zeitraum "{1}" importieren!
#导入
att_import_overData=Die aktuelle Anzahl der Importe ist {0} und überschreitet das Limit von 30.000. Bitte importieren Sie in Chargen!
att_import_existIllegalType=Die importierte {0} hat einen unzulässigen Typ!
#验证方式
att_verifyMode_0=Automatische Erkennung
att_verifyMode_1=Nur Fingerabdruck
att_verifyMode_2=Nur Pin
att_verifyMode_3=Nur Passwort
att_verifyMode_4=Nur Karte
att_verifyMode_5=Fingerabdruck oder Passwort
att_verifyMode_6=Fingerabdruck oder Karte
att_verifyMode_7=Karte oder Passwort
att_verifyMode_8=Pin und Fingerabdruck
att_verifyMode_9=Fingerabdruck und Passwort
att_verifyMode_10=Karte und Fingerabdruck
att_verifyMode_11=Karte und Passwort
att_verifyMode_12=Fingerabdruck und Passwort und Karte
att_verifyMode_13=PIN und Fingerabdruck und Passwort
att_verifyMode_14=(Pin und Fingerabdruck) oder (Karte und Fingerabdruck)
att_verifyMode_15=Gesicht
att_verifyMode_16=Gesicht und Fingerabdruck
att_verifyMode_17=Gesicht und Passwort
att_verifyMode_18=Gesicht und Karte
att_verifyMode_19=Gesicht und Fingerabdruck und Karte
att_verifyMode_20=Gesicht und Fingerabdruck und Passwort
att_verifyMode_21=Fingervene
att_verifyMode_22=Fingervene und Passwort
att_verifyMode_23=Fingervene und Karte
att_verifyMode_24=Fingervene und Passwort und Karte
att_verifyMode_25=Palmprint
att_verifyMode_26=Palmprint und Karte
att_verifyMode_27=Palmprint und Gesicht
att_verifyMode_28=Handabdruck und Fingerabdruck
att_verifyMode_29=Handabdruck und Fingerabdruck und Gesicht
# 工作流
att_flow_schedule=Fortschritt überprüfen
att_flow_schedulePass=(bestanden)
att_flow_scheduleNot=(wird überprüft)
att_flow_scheduleReject=(Abgelehnt)
# 工作时长表
att_workTimeReport_total=Gesamtarbeitszeit
# 自动导出报表
att_autoExport_startEndTime=Start- und Endzeit
# 年假
att_annualLeave_setting=Einstellung des Jahresurlaubsguthaben
att_annualLeave_settingTip1=Um die Jahresurlaubsbilanzfunktion verwenden zu können, müssen Sie die Eintrittszeit für jeden Mitarbeiter festlegen. Wenn die Eintrittszeit nicht festgelegt ist, wird der verbleibende Jahresurlaub des Mitarbeiters im Jahresurlaubsbilanzbericht als leer angezeigt.
att_annualLeave_settingTip2=Wenn das aktuelle Datum größer als das Ausstellungsdatum des Clearings ist, wird diese Änderung im folgenden Jahr wirksam. Wenn das aktuelle Datum unter dem Ausstellungsdatum des Clearings liegt und das Ausstellungsdatum des Clearings erreicht ist, wird es gelöscht und der Jahresurlaub wird erneut ausgestellt.
att_annualLeave_calculate=Clearing- und Ausstellungsdatum des Jahresurlaubs
att_annualLeave_workTimeCalculate=Berechnet nach Arbeitszeitverhältnis
att_annualLeave_rule=Jahresurlaubszeitregel
att_annualLeave_ruleCountOver=Das maximal festgelegte Nummernlimit wurde erreicht
att_annualLeave_years=Ältere Jahre
att_annualLeave_eachYear=Jedes Jahr
att_annualLeave_have=Has
att_annualLeave_days=Tage des Jahresurlaubs
att_annualLeave_totalDays=Gesamtjahresurlaub
att_annualLeave_remainingDays=Verbleibender Jahresurlaub
att_annualLeave_consecutive=Die Einstellung der Jahresurlaubsregel muss aufeinanderfolgende Jahre sein
# 年假结余表
att_annualLeave_report=Jahresurlaubsbilanz
att_annualLeave_validDate=Gültiges Datum
att_annualLeave_useDays=Verwenden Sie {0} Tage
att_annualLeave_calculateDays={0} Tage freigeben
att_annualLeave_notEnough=Unzureichender Jahresurlaub von {0}!
att_annualLeave_notValidDate={0} liegt nicht im gültigen Bereich des Jahresurlaubs!
att_annualLeave_notDays={0} hat keinen Jahresurlaub!
att_annualLeave_tip1=Zhang San ist am 1. September letzten Jahres beigetreten
att_annualLeave_tip2=Einstellung des Jahresurlaubssaldos
att_annualLeave_tip3=Das Clearing- und Ausstellungsdatum ist der 1. Januar eines jeden Jahres. Es wird durch Abrunden gemäß dem Arbeitsverhältnis berechnet. Wenn die Dienstzeit ≤ 1 ist, gibt es 3 Tage Jahresurlaub, und wenn die Dienstzeit ≤ 1 ist, gibt es 5 Tage Jahresurlaub.
att_annualLeave_tip4=Berechnung des Jahresurlaubs
att_annualLeave_tip5=Letztes Jahr 09-01 ~ 12-31 genießen Sie 4 / 12x3 = 1,0 Tage
att_annualLeave_tip6=Dieses Jahr 01-01 ~ 12-31 genießen 4,0 Tage (dieses Jahr 01-01 ~ 08-31 genießen 8 / 12x3 = 2,0 Tage + dieses Jahr 09-01 ~ 12-31 genießen 4 / 12x5≈2.0 Tage)
# att SDC
att_sdc_name=Videogerät
att_sdc_wxMsg_firstData=Hallo, Sie haben eine Benachrichtigung zum Einchecken
att_sdc_wxMsg_stateData=Kein Erfolg beim Einchecken
att_sdc_wxMsg_remark=Erinnerung: Das endgültige Anwesenheitsergebnis unterliegt der Check-in-Detailseite.
# 时间段
att_timeSlot_conflict=Das Zeitfenster steht in Konflikt mit anderen Zeitfenstern des Tages
att_timeSlot_selectFirst=Bitte wählen Sie das Zeitfenster aus
# 事件中心
att_eventCenter_sign=Anwesenheitsanmeldung
#异常管理
att_exception_classImportTemplate=Klassenimportvorlage
att_exception_cellClassAdjustType=Erforderliches Feld, wie: "{0}", "{1}", "{2}"
att_exception_swapDateDate=nicht erforderliches Feld, das Zeitformat ist JJJJ-MM-TT, zum Beispiel: 2020-07-07
#消息中心
att_message_leave=Bekanntmachung der Teilnahme {0}
att_message_leaveContent={0} übermittelt {1}, {2} Zeit ist {3}~{4}
att_message_leaveTime=Zeit verlassen
att_message_overtime=Ankündigung von Anwesenheit und Überstunden
att_message_overtimeContent={0} hat Überstunden eingereicht und die Überstunden betragen {1}~{2}
att_message_overtimeTime=Überstundenzeit
att_message_sign=Bekanntmachung des Anwesenheitszeichens
att_message_signContent={0} hat ein zusätzliches Zeichen eingereicht und die zusätzliche Zeichenzeit beträgt {1}
att_message_adjust=Mitteilung zur Anwesenheitsanpassung
att_message_adjustContent={0} hat eine Anpassung eingereicht und das Anpassungsdatum ist {1}
att_message_class=Anwesenheits- und Schichtbenachrichtigung
att_message_classContent=Klasseninhalt
att_message_classContent0={0} hat eine Schicht eingereicht, das Schichtdatum ist {1} und die Schicht ist {2}
att_message_classContent1={0} hat eine Schicht eingereicht, das Schichtdatum ist {1} und das Schichtdatum ist {2}
att_message_classContent2={0} ({1}) und {2} ({3}) tauschen Klassen
#推送中心
att_pushCenter_transaction=Anwesenheitsdatensatz
# 时间段
att_timeSlot_workTimeNotEqual=Arbeitszeit kann nicht gleich Arbeitszeit sein
att_timeSlot_signTimeNotEqual=Start-Anmeldezeit kann nicht gleich End-Abmeldezeit sein
# 北向接口A
att_api_notNull={0} darf nicht leer sein!
att_api_startDateGeEndDate=Die Startzeit kann nicht größer oder gleich der Endzeit sein!
att_api_leaveTypeNotExist=Die falsche Spezies existiert nicht!
att_api_imageLengthNot2000=Die Länge der Bildadresse darf 2000 nicht überschreiten!
# 20221230新增国际化
att_personSch_workTypeNotNull=Der Arbeitstyp, der Personalnummer {0} entspricht, darf nicht leer sein!
att_personSch_workTypeNotExist=Die der Personalnummer {0} entsprechende Arbeitsart existiert nicht!
att_annualLeave_recalculate=Neuberechnen
# 20230530新增国际化
att_leftMenu_dailyReport=Täglicher Anwesenheitsbericht
att_leftMenu_overtimeReport=Überstundenbericht
att_leftMenu_lateReport=Verspätete Meldung
att_leftMenu_earlyReport=Frühzeitigen Bericht hinterlassen
att_leftMenu_absentReport=Abwesenheitsbericht
att_leftMenu_monthReport=Monatlicher Anwesenheitsbericht
att_leftMenu_monthWorkTimeReport=Monatlicher Arbeitszeitbericht
att_leftMenu_monthCardReport=Monatlicher Kartenbericht
att_leftMenu_monthOvertimeReport=Monatlicher Überstundenbericht
att_leftMenu_overtimeSummaryReport=Zusammenfassungsbericht über die Überstunden des Personals
att_leftMenu_deptOvertimeSummaryReport=Überstundenübersichtsbericht der Abteilung
att_leftMenu_deptLeaveSummaryReport=Zusammenfassungsbericht zum Urlaub
att_annualLeave_calculateDay=Anzahl Jahresurlaubstage
att_annualLeave_adjustDay=Tage anpassen
att_annualLeave_sureSelectDept=Sind Sie sicher, dass Sie den Vorgang {0} für die ausgewählte Abteilung ausführen möchten?
att_annualLeave_sureSelectPerson=Sind Sie sicher, dass Sie die Operation {0} an der ausgewählten Person durchführen möchten?
att_annualLeave_calculateTip1=Bei Berechnung nach Betriebszugehörigkeit: Die Berechnung des Jahresurlaubs erfolgt monatsgenau, bei einer Betriebszugehörigkeit von 10 Jahren und 3 Monaten werden 10 Jahre und 3 Monate zur Berechnung herangezogen;
att_annualLeave_calculateTip2=Wenn die Umrechnung nicht nach Betriebszugehörigkeit erfolgt: Die Berechnung des Jahresurlaubs ist auf das Jahr genau, wenn die Betriebszugehörigkeit 10 Jahre und 3 Monate beträgt, dann werden 10 Jahre zur Berechnung verwendet;
att_rule_isInCompleteTip=Die Priorität ist am höchsten, wenn keine Anmeldung oder keine Abmeldung als unvollständig, Verspätung, vorzeitiges Verlassen, Fehlzeiten und gültig erfasst wird
att_rule_absentTip=Wenn keine An- oder Abmeldung als Abwesenheit erfasst wird, entspricht die Dauer der Abwesenheit der Arbeitszeit abzüglich der Dauer der verspäteten bis vorzeitigen Abwesenheit
att_timeSlot_elasticTip1=0, die effektive Zeit ist gleich der tatsächlichen Zeit, keine Fehlzeiten
att_timeSlot_elasticTip2=Wenn die tatsächliche Zeit länger als die Arbeitszeit ist, ist die effektive Zeit gleich der Arbeitszeit, keine Fehlzeiten
att_timeSlot_elasticTip3=Wenn die tatsächliche Dauer kleiner als die Arbeitszeit ist, entspricht die effektive Dauer der tatsächlichen Dauer und die Fehlzeiten entsprechen der Arbeitszeit minus der tatsächlichen Dauer
att_timeSlot_maxWorkingHours=Die Arbeitszeit darf nicht größer sein als
# 20231030
att_customReport=Benutzerdefinierter Anwesenheitsbericht
att_customReport_byDayDetail=Detail nach Tag
att_customReport_byPerson=Zusammenfassung nach Person
att_customReport_byDept=Zusammenfassung nach Abt.
att_customReport_queryMaxRange=Der maximale Abfragebereich beträgt vier Monate.
# 20240630新增国际化
att_personSch_shiftWorkTypeTip1=1. Bei Überstunden an normalen Arbeits-/Ruhetagen ist die Priorität der Planung niedriger als die der Feiertage
att_personSch_shiftWorkTypeTip2=2. Wenn die Art der Arbeit Überstunden während der Ferien ist, ist die Priorität der Planung höher als die der Ferien
att_personVerifyMode=Verfahren zur Überprüfung des Personals
att_personVerifyMode_setting=Einstellungen der Verifizierungsmethode
att_personSch_importCycSch=Personalzyklusplanung importieren
att_personSch_cycSchTemplate=Vorlage für die Planung des Personalzyklus
att_personSch_exportCycSchTemplate=Vorlage für Personalzyklusplanung herunterladen
att_personSch_scheduleTypeNotNull=Shift-Typ kann nicht leer sein oder existiert nicht!
att_personSch_shiftNotNull=Die Schicht darf nicht leer sein!
att_personSch_shiftNotExist=Die Verschiebung existiert nicht!
att_personSch_onlyAllowOneShift=Bei regelmäßiger Terminplanung kann nur eine Schicht eingeplant werden!
att_shift_attShiftStartDateRemark2=Die Woche, in der sich das Startdatum des Zyklus befindet, ist die erste Woche; Der Monat, in dem sich das Startdatum des Zyklus befindet, ist der erste Monat.
#打卡状态
att_cardStatus_setting=Einstellung der Ausprägungsstatus
att_cardStatus_name=Name
att_cardStatus_value=Wert
att_cardStatus_alias=Alias
att_cardStatus_every_day=Tages
att_cardStatus_by_week=Wochentlich
att_cardStatus_autoState=Automatische Zustand
att_cardStatus_attState=Ausprägungsstatus
att_cardStatus_signIn=Einchecken
att_cardStatus_signOut=Auschecken
att_cardStatus_out=ausgehen
att_cardStatus_outReturn=Rückkehr aus dem Ausland
att_cardStatus_overtime_signIn=Überstundenauschecken
att_cardStatus_overtime_signOut=Überstunden einchecken
# 20241030新增国际化
att_leaveType_enableMaxDays=Jahresbegrenzung aktivieren
att_leaveType_maxDays=Jahresbegrenzung (Tage)
att_leaveType_applyMaxDays=Antrag auf Urlaub darf im Jahr nicht mehr als {0} Tage betragen
att_param_overTimeSetting=Überstundenstufe einstellen
att_param_overTimeLevel=Überstundenstufe (Stunden)
att_param_overTimeLevelEnable=Überstundenrechner aktivieren
att_param_reportColor=Farbe des Berichts
# APP
att_app_signClientTip=Dieses Gerät wurde heute von jemandem anders eingecheckt
att_app_noSignAddress=Kein Abschlagsbereich eingerichtet. Bitte kontaktieren Sie den Administrator, um die Einstellungen zu konfigurieren.
att_app_notInSignAddress=Sie sind nicht am vorgesehenen Ort angekommen, das Check-In ist nicht möglich.
att_app_attendance=Mein Arbeitszeitkonto
att_app_apply=Arbeitszeitantrag
att_app_approve=Meine Genehmigungen
# 20250530
att_node_leaderNodeExist=Es besteht bereits eine Genehmigungsstufe durch den direkten Vorgesetzten
att_signAddress_init=Karteninitialisierung
att_signAddress_initTips=Geben Sie den Kartenschlüssel ein und initialisieren Sie die Karte, um eine Adresse auszuwählen