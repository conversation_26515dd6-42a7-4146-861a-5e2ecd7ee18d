#系统名称 土耳其语
att_systemName=Zaman Kontrol Sistemi 1.0
#=====================================================================
#左侧菜单
att_module=Zaman Kontrol
#左侧菜单-考勤设备
att_leftMenu_attendanceDevice=Zaman Kontrol Cihazı
att_leftMenu_device=Cihaz
att_leftMenu_point=Zaman Kontrol Noktası
att_leftMenu_sign_address=Mobil Oturum Açma Adresi
att_leftMenu_adms_devCmd=Sunucu tarafından verilen komut
#左侧菜单-基础信息
att_leftMenu_basicInformation=Basit Bilgilendirme
att_leftMenu_rule=Kural
att_leftMenu_base_rule=Temel kural
att_leftMenu_department_rule=De<PERSON><PERSON>
att_leftMenu_holiday=Tatil
att_leftMenu_leaveType=İzin Türü
att_leftMenu_timingCalculation=Zamanlı Hesaplama
att_leftMenu_autoExport=Otomatik Rapor
att_leftMenu_param=Parametre Ayarı
#左侧菜单-班次管理
att_leftMenu_shiftManagement=Vardiya
att_leftMenu_timeSlot=Zaman Tablosu
att_leftMenu_shift=Vardiya
#左侧菜单-排班管理
att_leftMenu_scheduleManagement=Planlama
att_leftMenu_group=Grup
att_leftMenu_groupPerson=Gruplandırılanlar
att_leftMenu_groupSch=Grup Takvimi
att_leftMenu_deptSch=Departman Planlama
att_leftMenu_personSch=Personel Planlama
att_leftMenu_tempSch=Geçici Planlama
att_leftMenu_nonSch=Planlanmamış Personel
#左侧菜单-异常管理
att_leftMenu_exceptionManagement=Katılım istisna yönetimi
att_leftMenu_sign=Kayıt Ekleme
att_leftMenu_leave=İzin
att_leftMenu_trip=İş gezisi
att_leftMenu_out=Dışarı
att_leftMenu_overtime=Fazla Mesai
att_leftMenu_adjust=Ayarla ve Ekle
att_leftMenu_class=Vardiyayı Ayarla
#左侧菜单-统计报表
att_leftMenu_statisticalReport=Katılım İstatistikleri Raporu
att_leftMenu_manualCalculation=Manuel Hesaplama
att_leftMenu_transaction=Kayıtlar
att_leftMenu_dayCardDetailReport=Günlük Zaman Kontrol
att_leftMenu_leaveSummaryReport=İzin Özeti
att_leftMenu_dayDetailReport=Günlük rapor
att_leftMenu_monthDetailReport=Aylık Detay Raporu
att_leftMenu_monthStatisticalReport=Aylık İstatistik Raporu (Kişiye Göre)
att_leftMenu_deptStatisticalReport=Departman Raporu (Departman Tarafından)
att_leftMenu_yearStatisticalReport=Faaliyet Raporu (Kişiye Göre)
att_leftMenu_attSignCallRollReport=Oturum açma sunum tablosu
att_leftMenu_workTimeReport=Çalışma Zaman Raporu
#左侧菜单-设备操作日志
att_leftMenu_device_op_log=Cihaz İşlem Günlüğü
#左侧菜单-实时点名
att_leftMenu_realTime_callRoll=Gerçek Zamanlı CallRoll
#=====================================================================
#公共
att_common_person=Personel
att_common_pin=ID
att_common_group=Grup
att_common_dept=Departman
att_common_symbol=Sembol
att_common_deptNo=Departman Numarası
att_common_deptName=Departman İsmi
att_common_groupNo=Grup numarası
att_common_groupName=Grup ismi
att_common_operateTime=Operasyon zamanı
att_common_operationFailed=Operasyon başarısız
att_common_id=ID
att_common_deptId=Departman ID
att_common_groupId=Grup ID
att_common_deviceId=Cihaz ID
att_person_pin=Personel ID
att_person_name=İsim
att_person_lastName=Soyadı
att_person_internalCard=Kart numarası
att_person_attendanceMode=katılım modu
att_person_normalAttendance=Normal katılım
att_person_noPunchCard=PunchCard Yok
att_common_attendance=Zaman Kontrol
att_common_attendance_hour=Zaman Kontrol(saat)
att_common_attendance_day=Zaman Kontrol (Gün)
att_common_late=Geç
att_common_early=Erken
att_common_overtime=Fazla Mesai
att_common_exception=İstisna
att_common_absent=Devamsızlık
att_common_leave=İzin
att_common_trip=İş gezisi
att_common_out=Dışarı
att_common_staff=Personel
att_common_superadmin=Süper Kullanıcı
att_common_msg=SMS İçeriği
att_common_min=Kısa Mesaj Bekleme Süresi (Dakikalar)
att_common_letterNumber=Sadece rakam veya harf girebilir!
att_common_relationDataCanNotDel=İlişkili veriler silinemez.
att_common_relationDataCanNotEdit=İlişkili veriler değiştirilemez.
att_common_needSelectOneArea=Lütfen bir alan seçin!
att_common_neesSelectPerson=Lütfen bir kişi seçin!
att_common_nameNoSpace=İsim boşluk içeremez!
att_common_digitsValid=Sadece iki ondalık basamaktan fazla olmayan sayıları girebilir!
att_common_numValid=Sadece rakam girin!
#=====================================================================
#工作面板
att_dashboard_worker=işkolik kimse  isim:     işkolik kimse (workaholic)
att_dashboard_today=Bugünün Zaman Kontrolü
att_dashboard_todayCount=Bugünkü Zaman Kontrol Bölümlü İstatistikler
att_dashboard_exceptionCount=Anormal İstatistikler (bu ay)
att_dashboard_lastWeek=Geçen hafta
att_dashboard_lastMonth=Geçen Ay
att_dashboard_perpsonNumber=Toplam Personel
att_dashboard_actualNumber=Fiili Personel
att_dashboard_notArrivedNumber=Personel Yok
att_dashboard_attHour=Çalışma zamanı
#区域
#设备
att_op_syncDev=Yazılım Verilerini Cihaza Senkronize Etme
att_op_account=Zaman Kontrol Veri Kontrolü
att_op_check=Verileri tekrar yükle
att_op_deleteCmd=Cihaz Komutlarını Temizle
att_op_dataSms=Genel Mesaj
att_op_clearAttPic=Zaman Kontrol Fotoğraflarını Temizle
att_op_clearAttLog=Zaman Kontrol İşlemlerini Temizle
att_device_waitCmdCount=Yürütülecek Komutlar
att_device_status=Durumu Etkinleştir
att_device_register=Kayıt Makinesi
att_device_isRegister=Kayıt Cihazı
att_device_existNotRegDevice=Kayıtlı olmayan makine ekipmanı, veri alınamıyor!
att_device_fwVersion=Firmware Versiyon
att_device_transInterval=Yenileme Süresi (dk)
att_device_cmdCount=Sunucuyla iletişim kurmak için maksimum komut sayısı
att_device_delay=Sorgu kayıt süresi (saniye)
att_device_timeZone=Zaman Dilimi
att_device_operationLog=İşlem Kayıtları
att_device_registeredFingerprint=Parmak İzini Kaydet
att_device_registeredUser=Personel Kaydı
att_device_fingerprintImage=Parmak İzi Resmi
att_device_editUser=Personel Düzenle
att_device_modifyFingerprint=Parmak İzini Değiştir
att_device_faceRegistration=Yüz Kaydı
att_device_userPhotos=Personel Resmi
att_device_attLog=Zaman kontrol kayıtlarının yüklenip yüklenmeyeceği
att_device_operLog=Personel bilgilerinin yüklenip yüklenmeyeceği
att_device_attPhoto=Katılım fotoğraflarının yüklenip yüklenmeyeceği
att_device_isOnLine=Çevrimiçi durum
att_device_InputPin=Kişi numarasını girin
att_device_getPin=Belirtilen personel verilerini alın
att_device_separatedPin=Virgülle ayrılmış birden fazla personel sayısı
att_device_authDevice=Yetkili Cihaz
att_device_disabled=Aşağıdaki cihazlar devre dışıdır ve çalıştırılamaz!
att_device_autoAdd=Yeni cihazı otomatik olarak ekle
att_device_receivePersonOnlyDb=Yalnızca veritabanında bulunan kişi verilerini alın
att_devMenu_control=Cihaz Kontrolü
att_devMenu_viewOrGetInfo=Bilgileri Görüntüleme ve Alma
att_devMenu_clearData=Cihaz Verilerini Temizle
att_device_disabledOrOffline=Cihaz etkin değil veya çevrimiçi değil, çalıştırılamıyor!
att_device_areaStatus=Cihaz alanı durumu
att_device_areaCommon=Alan normal
att_device_areaEmpty=Alan boş
att_device_isRegDev=Saat dilimi veya kayıt makinesi durumu değişikliği, cihazı yeniden başlattıktan sonra etkinleşecek!
att_device_canUpgrade=Aşağıdaki cihazlar yükseltilebilir
att_device_offline=Aşağıdaki cihazlar çevrimdışı ve çalıştırılamaz!
att_device_oldProtocol=Eski protokol
att_device_newProtocol=Yeni protokol
att_device_noMoreTwenty=Eski protokol aygıtının ürün yazılımı yükseltme paketi 20M'yi geçemez
att_device_transferFilesTip=Firmware testi başarılı, dosyaları aktarın
att_op_clearAttPers=Ekipman personelini temizle
#区域人员
att_op_forZoneAddPers=Bölge Personel Ayarı
att_op_dataUserSms=Özel mesaj
att_op_syncPers=Cihaza yeniden senkronize et
att_areaPerson_choiceArea=Lütfen alanı seçin!
att_areaPerson_byAreaPerson=Personeli alana göre ayarla
att_areaPerson_setByAreaPerson=Alana Göre Ayarla
att_areaPerson_importBatchDel=Toplu silmeyi içe aktar
att_areaPerson_syncToDevSuccess=Başarılı operasyon! Lütfen komutun gönderilmesini bekleyin.
att_areaPerson_personId=Personel ID
att_areaPerson_areaId=Alan ID
att_area_existPerson=Bölgede insanlar var!
att_areaPerson_notice1=Alan veya personel aynı anda boş olamaz!
att_areaPerson_notice2=Hiçbir insan veya bölge sorgulanmadı!
att_areaPerson_notice3=Bölgede cihaz bulunamadı!
att_areaPerson_addArea=Alan ekle
att_areaPerson_delArea=Alanı sil
att_areaPerson_persNoExit=Kişi mevcut değil
att_areaPerson_importTip1=Lütfen aktarılan kişinin personel modülünde zaten mevcut olduğundan emin olun
att_areaPerson_importTip2=Toplu ithalatçılar cihaza otomatik olarak teslim edilmeyecek ve manuel olarak senkronize edilmeleri gerekecek
att_areaPerson_addAreaPerson=Alan kişisi ekle
att_areaPerson_delAreaPerson=Alan personelini sil
att_areaPerson_importDelAreaPerson=Alan personelini içe aktarın ve silin
att_areaPerson_importAreaPerson=AreaPerson'u İçe Aktar
#考勤点
att_attPoint_name=Zaman Kontrol Noktası İsmi
att_attPoint_list=Zaman Kontrol Nokta Listesi
att_attPoint_deviceModule=Cihaz Modülü
att_attPoint_acc=Giriş kontrolu
att_attPoint_park=Otopark
att_attPoint_ins=FaceKiosk
att_attPoint_pid=Personel Sertifikası
att_attPoint_vms=Video
att_attPoint_psg=Koridor
att_attPoint_doorList=Kapı Listesi
att_attPoint_deviceList=Cİhaz Listesi
att_attPoint_channelList=Kanal Listesi
att_attPoint_gateList=Kapı listesi
att_attPoint_recordTypeList=Kayıt türünü çekin
att_attPoint_door=Lütfen ilgili kapıyı seçiniz.
att_attPoint_device=Lütfen ilgili cihazı seçin.
att_attPoint_gate=Lütfen ilgili kapıyı seçin
att_attPoint_normalPassRecord=Normal geçiş kaydı
att_attPoint_verificationRecord=Doğrulama kaydı
att_person_attSet=Zaman Kontrol Ayarları
att_attPoint_point=Lütfen devam noktasını seçin.
att_attPoint_count=Yeterli Zaman Kontrol lisansı puanı; operasyon başarısız!
att_attPoint_notSelect=Modül yapılandırılmamış
att_attPoint_accInsufficientPoints=Erişim kontrol kayıtları için yetersiz katılım noktaları!
att_attPoint_parkInsufficientPoints=Otopark kayıtları için yetersiz katılım noktaları!
att_attPoint_insInsufficientPoints=FaceKiosk'un zaman kontrol lisans noktası yetersiz！
att_attPoint_pidInsufficientPoints=Personel Sertifikası Zaman Kontrol için yeterli değildir!
att_attPoint_doorOrParkDeviceName=Kapı adı veya park cihazı adı
att_attPoint_vmsInsufficientPoints=Videoda yetersiz katılım puanı var!
att_attPoint_psgInsufficientPoints=Kanalda yetersiz katılım puanı var!
att_attPoint_delDevFail=Cihazı silme başarısız oldu, cihaz katılım olarak kullanıldı!
att_attPoint_pullingRecord=Katılım noktası düzenli olarak kayıt alıyor, lütfen bekleyin!
att_attPoint_lastTransactionTime=Son veri çekme süresi
att_attPoint_masterDevice=Ana Cihaz
att_attPoint_channelName=Kanal adı
att_attPoint_cameraName=Kamera adı
att_attPoint_cameraIP=kamera ipi
att_attPoint_channelIP=kanal ipi
att_attPoint_gateNumber=Kapı numarası
att_attPoint_gateName=Kapı adı
#APP考勤签到地址
att_signAddress_address=Adres
att_signAddress_longitude=Boylam
att_signAddress_latitude=Enlem
att_signAddress_range=Etkili menzil
att_signAddress_rangeUnit=Birim Metre (m)
#=====================================================================
#规则
att_rule_baseRuleSet=Temel Kural Ayarı
att_rule_countConvertSet=Hesaplama Ayarı
att_rule_otherSet=Diğer Ayarlar
att_rule_baseRuleSignIn=Giriş Kuralı
att_rule_baseRuleSignOut=Çıkış Kuralı
att_rule_earliestPrinciple=en erken Kuralı
att_rule_theLatestPrinciple=Son Kural
att_rule_principleOfProximity=Proximity Kuralı
att_rule_baseRuleShortestMinutes=Minimum süre (en az 10 dakika) olmalıdır
att_rule_baseRuleLongestMinutes=Maksimum süre (azami 1.440 dakika)
att_rule_baseRuleLateAndEarly=Geç ve Erken İzin Yok Sayılır
att_rule_baseRuleCountOvertime=Fazla Mesai İstatistikleri
att_rule_baseRuleFindSchSort=Vardiya Kaydı Ara
att_rule_groupGreaterThanDepartment=Grubu -  Bölüm
att_rule_departmentGreaterThanGroup=Departman-> Grup
att_rule_baseRuleSmartFindClass=Akıllı iş arama prensibi
att_rule_timeLongest=En Uzun Çalışma Süresi
att_rule_exceptionLeast=En Az Anormal
att_rule_baseRuleCrossDay=Gün içi vardiya için devam hesaplama sonucu
att_rule_firstDay=İlk gün
att_rule_secondDay=İkinci gün
att_rule_baseRuleShortestOvertimeMinutes=Tek en kısa fazla mesai (dakika)
att_rule_baseRuleMaxOvertimeMinutes=Maksimum yükleme (Dakikalar)
att_rule_baseRuleElasticCal=Esnek Bekleme Hesabı
att_rule_baseRuleTwoPunch=Her iki okutma için kümülatif zaman
att_rule_baseRuleStartEnd=İlk ve son okutma için sayım süresi
att_rule_countConvertHour=Saat Dönüşüm Kuralı
att_rule_formulaHour=Formül: Saat = Dakika / 60
att_rule_countConvertDay=Gün Dönüşüm Kuralı
att_rule_formulaDay=Formül ： Gün = Dakika / Gün içinde çalışıdan dakika
att_rule_inFormulaShallPrevail=Formül tarafından hesaplanan sonucu standart olarak alın;
att_rule_remainderHour=Kalan değer şuna eşit veya daha büyük
att_rule_oneHour=Bir saat olarak kaydedildi;
att_rule_halfAnHour=Yarım saat olarak hesaplanır, aksi takdirde yoksayılır;
att_rule_remainderDay=Bölüm, çalışma dakikalarından büyük veya ona eşittir
att_rule_oneDay=%, bir gün olarak hesaplanır;
att_rule_halfAnDay=%,yarım gün olarak hesaplanan 1; aksi takdirde yoksayılır;
att_rule_countConvertAbsentDay=Gün dönüşüm karşılaştırması
att_rule_markWorkingDays=İş günü olarak hesaplanır
att_rule_countConvertDecimal=Ondalık noktasının tam rakamları
att_rule_otherSymbol=Rapordaki zaman kontrol sonucu sembol ayarı
att_rule_arrive=Beklenen / Fiili
att_rule_noSignIn=Giriş Yok
att_rule_noSignOff=Çıkış Yok
att_rule_off=Dinlenmeyi Ayarla
att_rule_class=Katılım Ekle
att_rule_shortLessLong=Zaman Kontrol Zaman dilimi, en uzun zaman kontrol zaman diliminden uzun olamaz
att_rule_symbolsWarning=Zaman Kontrol raporunda sembolü yapılandırmalısınız!
att_rule_reportSettingSet=Rapor dışa aktarma ayarları
att_rule_shortDateFormat=Tarih Formatı
att_rule_shortTimeFormat=Zaman formatı
att_rule_baseRuleSignBreakTime=Mola sırasında okutma 
att_leftMenu_custom_rule=Özel Kural
att_custom_rule_already_exist={0} Özel kurallar zaten var!
att_add_group_custom_rule=Gruplama kuralları ekle
att_custom_rule_type=Kural türü
att_rule_type_group=Grup kuralları
att_rule_type_dept=Departman Kuralı
att_custom_rule_orgNames=Nesneyi kullanma
att_rult_maxOverTimeType1=Limit Yok
att_rult_maxOverTimeType2=Bu Hafta
att_rult_maxOverTimeType3=Bu Ay
att_rule_countConvertDayRemark1=Örnek: Etkili çalışma süresi 500 dakikadır ve çalışma süresi günde 480 dakikadır. Sonuç 500/480 = 1.25'dir ve son ondalık sayı 1.2'de tutulur
att_rule_countConvertDayRemark2=Örnek: Etkili çalışma süresi 500 dakikadır ve çalışma süresi günde 480 dakikadır. Sonuç 500/480 = 1.25, 1.25> 0.8'dir.
att_rule_countConvertDayRemark3=Örnek: Etkili çalışma süresi 300 dakikadır ve çalışma süresi günde 480 dakikadır. Sonuç, yarım gün için 300/480 = 0.625, 0.2 <0.625 <0.8'dir.
att_rule_countConvertDayRemark4=Gün dönüşüm karşılaştırması: Zaman periyodunda çalışma günü olarak çalışmaz;
att_rule_countConvertDayRemark5=İş günü sayısı olarak kaydedilir: tamamlanma günlerinin sayısının hesaplanmasıyla sınırlıdır ve her dönemde tamamlanma süresi olduğu sürece, tamamlanma süresi iş günlerinin sayısına göre hesaplanır. dönemi;
att_rule_baseRuleSmartFindRemark1=En uzun zaman: Günün kart noktasına göre, günün her vardiyasına karşılık gelen çalışma saatlerini hesaplayın ve günün en uzun çalışma süresinin kaymasını bulun;
att_rule_baseRuleSmartFindRemark2=Anormal minimum: Günün her vardiyasına karşılık gelen anormal sürelerin sayısını günün kart noktasına göre hesaplayın ve çalışma süresini hesaplamak için günü en az sayıda anormallikle kaydırmayı bulun;
att_rule_baseRuleHourValidator=Yarım saatlik karar dakikası 1 saate eşit veya daha fazla olamaz!
att_rule_baseRuleDayValidator=Yarım günlük karar süresi, bir günlük karar süresinden büyük veya ona eşit olamaz!
att_rule_overtimeWarning=Maksimum fazla mesai süresi minimum tek minimum fazla mesai süresinden az olamaz!
att_rule_noSignInCountType=Check-In sayısı eksik
att_rule_absent=Devamsızlık
att_rule_earlyLeave=Erken Çıkma
att_rule_noSignOffCountType=Eksik Giirş sayımı
att_rule_minutes=Dakikalar
att_rule_noSignInCountLateMinute=Dakika cinsinden Geç Giriş sayısı
att_rule_noSignOffCountEarlyMinute=Erken kalan dakika olarak Eksik Çıkış sayımı
att_rule_incomplete=Tamamlanmamış
att_rule_noCheckInIncomplete=Eksik ve Giriş Yok
att_rule_noCheckOutIncomplete=Eksik ve Çıkış Yok
att_rule_lateMinuteWarning=Geç Kalma dakikası 0'dan büyük ve en uzun katılım süresinden kısa olmamalı
att_rule_earlyMinuteWarning=Erken Çıkma dakikası 0'dan büyük ve en uzun katılım süresinden kısa olmamalı
att_rule_baseRuleNoSignInCountLateMinuteRemark=Giriş yapılmadığında geç sayılır, giriş yapılmazsa N dakika gecikmiş sayılır
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=Çıkış yapılmadığında erken ayrılma olarak sayılır, çıkış yapılmazsa erken ayrılma N dakikası olarak sayılır
#节假日
att_holiday_placeholderNo=H01 gibi H ile başlamanız önerilir.
att_holiday_placeholderName=Örneğin [Yıl] + [Tatil Adı] ile adlandırılması önerilir. [2017 İşçi Bayramı].
att_holiday_dayNumber=Gün sayısı
att_holiday_validDate_msg=Holidays during this time
#假种
att_leaveType_leaveThing=Günlük İzin
att_leaveType_leaveMarriage=Evlilik İzni
att_leaveType_leaveBirth=Doğum İzni
att_leaveType_leaveSick=Hastalık İzni
att_leaveType_leaveAnnual=Yıllık izin
att_leaveType_leaveFuneral=Yas İzni
att_leaveType_leaveHome=Aile izni
att_leaveType_leaveNursing=Emzirme İzni
att_leaveType_isDeductWorkLong=Çalışma saatlerini bırak
att_leaveType_placeholderNo=L1 gibi L ile başlamanız önerilir.
att_leaveType_placeholderName=Tatil ile sona erdirilmesi önerilir, örneğin Evlilik Tatili.
#定时计算
att_timingcalc_timeCalcFrequency=Hesaplama aralığı
att_timingcalc_timeCalcInterval=Zamanlı Hesaplama Süresi
att_timingcalc_timeSet=Zamanlı Hesaplama Süresi Ayarı
att_timingcalc_timeSelect=Lütfen Zaman Seçiniz
att_timingcalc_optionTip=En az bir geçerli günlük programlanmış katılım hesaplaması saklanmalıdır
#自动导出报表
att_autoExport_reportType=Rapor türü
att_autoExport_fileType=Dosya tipi
att_autoExport_fileName=Dosya Adı
att_autoExport_fileDateFormat=Tarih formatı
att_autoExport_fileTimeFormat=Zaman formatı
att_autoExport_fileContentFormat=İçerik Biçimi
att_autoExport_fileContentFormatTxt=Örnek: {deptName} 00 {personPin} 01 {PERSONNAME} 02 {attDatetime} 03
att_autoExport_timeSendFrequency=Frekans Gönder
att_autoExport_timeSendInterval=Zaman Gönderme Aralığı
att_autoExport_emailType=Posta Alıcı Türü
att_autoExport_emailRecipients=Posta Alıcısı
att_autoExport_emailAddress=Mail Adresi
att_autoExport_emailExample=Örnek: <EMAIL>, <EMAIL>
att_autoExport_emailSubject=Mail Unvanı
att_autoExport_emailContent=Posta Gövdesi
att_autoExport_field=Alan
att_autoExport_fieldName=Alan adı
att_autoExport_fieldCode=Alan Numarası
att_autoExport_reportSet=Rapor Ayarlatı
att_autoExport_timeSet=Posta Teslim Süresi Ayarı
att_autoExport_emailSet=Posta Ayarı
att_autoExport_emailSetAlert=Lütfen e-mail adresinizi giriniz.
att_autoExport_emailTypeSet=Alıcı Ayarı
att_autoExport_byDay=Güne Göre
att_autoExport_byMonth=Ayda
att_autoExport_byPersonSet=Kişiye göre ayarla
att_autoExport_byDeptSet=Departmana göre ayarla
att_autoExport_byAreaSet=Bölgeye Göre Ayarla
att_autoExport_emailSubjectSet=Başlık Ayarı
att_autoExport_emailContentSet=Gövde Ayarı
att_autoExport_timePointAlert=Lütfen doğru gönderme zaman noktasını seçin.
att_autoExport_lastDayofMonth=Ayın son günü
att_autoExport_firstDayofMonth=Ayın ilk günü
att_autoExport_dayofMonthCheck=Belirli tarih
att_autoExport_dayofMonthCheckAlert=Lütfen belirli bir tarih seçin.
att_autoExport_chooseDeptAlert=Lütfen Departman seçin!
att_autoExport_sendFormatSet=Gönderim Mod Ayarları
att_autoExport_sendFormat=Gönderme Modu
att_autoExport_mailFormat=Posta Kutusu Dağıtım Yöntemi
att_autoExport_ftpFormat=FTP Gönderme Yöntemi
att_autoExport_sftpFormat=SFTP Gönderme Yöntemi
att_autoExport_ftpUrl=FTP Sunucusu Adresi
att_autoExport_ftpPort=FTP Sunucu Portu
att_autoExport_ftpTimeSet=FTP Gönderme Süresi Ayarı
att_autoExport_ftpParamSet=FTP Parametre Ayarı
att_autoExport_ftpUsername=FTP Kullanıcı Adı
att_autoExport_ftpPassword=FTP Şifre
att_autoExport_correctFtpParam=Lütfen ftp parametrelerini doğru doldurunuz
att_autoExport_correctFtpTestParam=iletişiminin normal olduğundan emin olmak için lütfen bağlantıyı test edin
att_autoExport_inputFtpUrl=Lütfen sunucusu adresini girin
att_autoExport_inputFtpPort=Lütfen sunucu portunu girin
att_autoExport_ftpSuccess=Bağlantı başarılı
att_autoExport_ftpFail=Lütfen parametre ayarlarının yanlış olup olmadığını kontrol edin.
att_autoExport_validFtp=Lütfen geçerli bir sunucu adresi girin
att_autoExport_validPort=Lütfen geçerli bir sunucu bağlantı noktası girin
att_autoExport_selectExcelTip=Dosya türü olarak EXCEL'i seçin, içerik formatı tüm alanlardır!
#=====================================================================
#时间段
att_timeSlot_periodType=Zaman Dilimi Tipi
att_timeSlot_normalTime=Normal Zaman Tablosu
att_timeSlot_elasticTime=Esnek Zaman Dilimi
att_timeSlot_startSignInTime=Giriş Başlangıç ​​Saati
att_timeSlot_toWorkTime=Giriş Zamanı
att_timeSlot_endSignInTime=Giriş Bitiş Saati
att_timeSlot_allowLateMinutes=Geç Gelme İzini (Dakikalar)
att_timeSlot_isMustSignIn=Griş Yapılmalıdır
att_timeSlot_startSignOffTime=Çıkış Başlangıç ​​Saati
att_timeSlot_offWorkTime=Çıkış Zamanı
att_timeSlot_endSignOffTime=Çıkış Bitiş Saati
att_timeSlot_allowEarlyMinutes=Erken Çıkma İzni (Dakikalar)
att_timeSlot_isMustSignOff=Çıkış Olmalı
att_timeSlot_workingHours=Çalışma Süresi (Dakikalar)
att_timeSlot_isSegmentDeduction=Otomatik Kesme Molası Süresi
att_timeSlot_startSegmentTime=Başlangıç ​​saati
att_timeSlot_endSegmentTime=Bitiş zamanı
att_timeSlot_interSegmentDeduction=Kesinti Süresi (dakika)
att_timeSlot_markWorkingDays=Çalışma Günü
att_timeSlot_isAdvanceCountOvertime=Otomatik FM (Erken Giriş)
att_timeSlot_signInAdvanceTime=Otomatik FM Bitiş Zamanı (Giriş)
att_timeSlot_isPostponeCountOvertime=Otomatik FM (Giriş Gecikmesi)
att_timeSlot_signOutPosponeTime=Otomatik FM Başlangıç ​​Zamanı (Çıkış)
att_timeSlot_isCountOvertime=Fazla Mesai Olarak Hesaplandı
att_timeSlot_timeSlotLong=Çalışma saatleri, kurallarla tanımlanan katılım aralığını karşılamalıdır:
att_timeSlot_alertStartSignInTime=Giriş Başlangıç ​​zamanı Giriş zamanından az olmalıdır.
att_timeSlot_alertEndSignInTime=Giriş Bitiş zamanı Giriş zamanından büyük olmalıdır.
att_timeSlot_alertStartSignInAndEndSignIn=Çıkış Başlangıç ​​zamanı Çıkış zamanından az olmalıdır.
att_timeSlot_alertStartSignOffTime=Fazla mesai başlangıç ​​zamanı Çıkış zamanından az olamaz.
att_timeSlot_alertEndSignOffTime=Fazla mesai başlangıç ​​zamanı bitiş kontrol zamanından daha büyük olamaz.
att_timeSlot_alertStartUnequalEnd=Çalışma günleri 0'dan küçük olamaz.
att_timeSlot_alertStartSegmentTime=Kesinti süresi 0'dan küçük olamaz.
att_timeSlot_alertStartAndEndTime=Başlangıç ​​çıkış zamanı bitiş kontrol zamanına eşit olamaz.
att_timeSlot_alertEndAndoffWorkTime=Saatler 23'ten fazla olamaz.
att_timeSlot_alertToWorkLesserAndEndTimeLesser=Dakikalar 59'dan fazla olamaz.
att_timeSlot_alertLessSignInAdvanceTime=Erken Giriş zamanı iş başlangıç ​​zamanından az olmalıdır
att_timeSlot_alertMoreSignInAdvanceTime='Çalışmaya' başlamadan önceki dakika mesai sayısı, 'çalışmaya' başlamadan önceki dakika sayısından az
att_timeSlot_alertMoreSignOutPosponeTime=İşten sonra oturumu kapattıktan sonraki dakika fazla mesai dakikası, işten sonraki dakika sayısından az
att_timeSlot_alertLessSignOutPosponeTime=Geç çıkış süresi iş bitiş saatinden fazla olmalıdır
att_timeSlot_time=Lütfen doğru saat biçimini girin.
att_timeSlot_alertMarkWorkingDays=Çalışma günü boş olamaz!
att_timeSlot_placeholderNo=T01 gibi T ile başlamanız önerilir.
att_timeSlot_placeholderName=T ile başlamanız veya zaman çizelgesi ile bitmeniz önerilir.
att_timeSlot_beforeToWork=İşe gitmeden önce
att_timeSlot_afterToWork=Çalışma sonrası
att_timeSlot_beforeOffWork=Görevden ayrılmadan önce
att_timeSlot_afterOffWork=Çalışma Sonrası
att_timeSlot_minutesSignInValid=Giriş birkaç dakika içinde geçerlidir
att_timeSlot_toWork=Görevde
att_timeSlot_offWork=Mesai Dışı
att_timeSlot_minutesSignInAsOvertime=Fazla mesai için dakika önce oturum açın
att_timeSlot_minutesSignOutAsOvertime=Birkaç dakika sonra fazla mesai saymaya başlayın
att_timeSlot_minOvertimeMinutes=Minimum fazla mesai dakikaları
att_timeSlot_enableWorkingHours=Çalışma saatlerinin etkinleştirilip etkinleştirilmeyeceği
att_timeSlot_eidtTimeSlot=Zamanı düzenle
att_timeSlot_browseBreakTime=Dinlenme sürelerine göz atın
att_timeSlot_addBreakTime=Mola ekle
att_timeSlot_enableFlexibleWork=Esnek çalışma aktif
att_timeSlot_advanceWorkMinutes=Erken çalışabilir
att_timeSlot_delayedWorkMinutes=Çalışmayı ertele
att_timeSlot_advanceWorkMinutesValidMsg1=Çalışmaya başlamadan önce geçen dakika sayısı, önceden işe gidebilecek dakika sayısından fazla
att_timeSlot_advanceWorkMinutesValidMsg2=Önceden işe gidebilecek dakika sayısı, işe gitmeden önceki dakika sayısından az
att_timeSlot_advanceWorkMinutesValidMsg3=Önceden çalışılabilecek dakika sayısı, fazla mesai için oturum açmadan önceki dakika sayısından az veya ona eşittir.
att_timeSlot_advanceWorkMinutesValidMsg4=Fazla mesai için oturum açmadan önce geçebilecek dakika sayısı önceden çalışılan dakika sayısından fazla veya buna eşittir.
att_timeSlot_delayedWorkMinutesValidMsg1=İşten sonraki dakika sayısı, işe ertelenebilecek dakika sayısından fazla
att_timeSlot_delayedWorkMinutesValidMsg2=Çalışması için ertelenebilecek dakika sayısı işten sonraki dakika sayısından az
att_timeSlot_delayedWorkMinutesValidMsg3=Çalışması planlanan dakika sayısı, işten sonraki dakika sayısından az veya eşittir, oturumu kapatır ve fazla mesai yapmaya başlar
att_timeSlot_delayedWorkMinutesValidMsg4=İşten sonra geçebilecek, oturumu kapatan ve fazla mesai yapmaya başlayabilecek dakika sayısı, çalışması planlanan dakika sayısından fazla veya bu süreye eşit
att_timeSlot_allowLateMinutesValidMsg1=Geç kalmasına izin verilen dakika sayısı işten sonraki dakika sayısından az
att_timeSlot_allowLateMinutesValidMsg2=İşten sonraki dakika sayısı, geç kalmasına izin verilen dakika sayısından fazla
att_timeSlot_allowEarlyMinutesValidMsg1=Erkan çıkma dakikası, Çalışma dakikalarından az ise izin ver
att_timeSlot_allowEarlyMinutesValidMsg2=Çalışmadan önceki dakika sayısı, erken bırakılan dakika sayısından fazla
att_timeSlot_timeOverlap={0}, {1} zamanla çakışıyor, lütfen seçilen zaman aralığını değiştirin!
att_timeSlot_atLeastOne=En az 1 dinlenme zamanı aralığı!
att_timeSlot_mostThree=En fazla üç dinlenme zaman aralığı!
att_timeSlot_canNotEqual=Dinlenme süresinin başlangıç ​​zamanı bitiş zamanına eşit olamaz!
att_timeSlot_shoudInWorkTime=Lütfen dinlenme süresinin çalışma saatleri içinde olduğundan emin olun!
att_timeSlot_repeatBreakTime=Ara süresi periyodunu tekrarlayın!
att_timeSlot_toWorkLe=Çalışma süresi, seçilen dinlenme süresinin minimum başlangıç ​​zamanından daha az:
att_timeSlot_offWorkGe=Mesai saatleri, seçilen dinlenme süresinin maksimum bitiş süresinden daha büyük:
att_timeSlot_crossDays_toWork=Dinlenme süresinin minimum başlangıç ​​zamanı, süre içinde olmalıdır:
att_timeSlot_crossDays_offWork=Dinlenme süresinin maksimum bitiş zamanı, süre içinde:
att_timeSlot_allowLateMinutesRemark=Çalışma saatlerinin başlangıcından geç kalmasına izin verilen zamana kadar, okutulan kart normal çalışma olarak sayılır
att_timeSlot_allowEarlyMinutesRemark=Normal izinli karttan erken ayrılmasına izin verilen dakika sayısına göre görev dışı zamanından erken başlayarak
att_timeSlot_isSegmentDeductionRemark=Zaman periyodunda dinlenme süresini silme
att_timeSlot_attEnableFlexibleWorkRemark1=Esnek çalışmaların geç, erken kalkış sayısını ayarlamasına izin verilmez
att_timeSlot_afterToWorkRemark=Çalışma dakikalarından sonra Çalışma dakikalarına ertelendi
att_timeSlot_beforeOffWorkRemark=Çalışmadan önceki dakika sayısı, çalışmadan önceki dakika sayısına eşittir
att_timeSlot_attEnableFlexibleWorkRemark2=İşten sonraki dakika sayısı, mesai saatleri + gecikmeli çalışma saatlerinden büyük veya bu süreye eşittir
att_timeSlot_attEnableFlexibleWorkRemark3=Mesai dakikaları içinde çalışabilirsiniz, fazla mesai dakikaları çalışmak için Çalışma N dakikalarına eşit veya daha az olmalıdır
att_timeSlot_attEnableFlexibleWorkRemark4=Çalışma dakikalarına ertelenmiştir, çalışma dışı N dakikaya eşit veya daha az olmalıdır
att_timeSlot_attBeforeToWorkAsOvertimeRemark=Örnek: 9: 00 sınıfı, çalışmadan 60 dakika önce fazla mesai yapmak için oturum açın, daha sonra saat 8'den 8'e kadar fazla mesai yapın
att_timeSlot_attAfterOffWorkAsOvertimeRemark=Örnek: İşten 18: 00'den sonra, 60 dakikalık bir çalışmadan sonra, para çekme ve fazla mesaiyi imzalayın, daha sonra fazla mesaiye saat 19'dan çıkış saatine kadar başlayın.
att_timeSlot_longTimeValidRemark=İşten sonra geçerli kart imzalama süresi ve işten önce geçerli kart imzalama süresi, süre içinde çakışamaz!
att_timeSlot_advanceWorkMinutesValidMsg5=Giriş öncesi etkili dakika sayısı, önceden çalışabileceğiniz dakika sayısından fazla
att_timeSlot_advanceWorkMinutesValidMsg6=Önceden işe gidebileceğiniz dakika sayısı, işten ayrılmadan önce geçerli dakika sayısından az olmalıdır
att_timeSlot_delayedWorkMinutesValidMsg5=Check-in sonrası geçerli dakika sayısı, işe ertelenebilecek dakika sayısından fazla
att_timeSlot_delayedWorkMinutesValidMsg6=Çalışmaya ertelenebilecek dakika sayısı, oturum açtıktan sonraki geçerli dakikalardan az olmalıdır
att_timeSlot_advanceWorkMinutesValidMsg7=İşten önceki giriş zamanı, önceki gün işten ayrıldıktan sonraki çıkış zamanıyla çakışamaz
att_timeSlot_delayedWorkMinutesValidMsg7=İşten sonraki çıkış zamanı, ertesi gün işe başlamadan önceki giriş zamanıyla çakışamaz
att_timeSlot_maxOvertimeMinutes=Maksimum fazla mesai saatlerini sınırlayın
#班次
att_shift_basicSet=Zamanlama türü
att_shift_advancedSet=Zamanlama Adı
att_shift_type=Vardita Türü
att_shift_name=Vardiya Adı
att_shift_regularShift=Düzenli vardiya
att_shift_flexibleShift=Esnek Vardiya
att_shift_color=Renk
att_shift_periodicUnit=Birim
att_shift_periodNumber=Döngü
att_shift_startDate=Başlangıç ​​tarihi
att_shift_startDate_firstDay=Başlangıç Tarih Döngüsü
att_shift_isShiftWithinMonth=Biir Aylık Vardiya Döngüsü
att_shift_attendanceMode=Zaman Kontrol Modu
att_shift_shiftNormal=Normal Vardiyaya Göre Delikli Kart
att_shift_oneDayOneCard=Günün herhangi bir zamanında bir kez okutma yapın
att_shift_onlyBrushTime=Sadece Kart Okutma Zamanlarını Hesapla
att_shift_notBrushCard=Serbest Okutma
att_shift_overtimeMode=Fazla Mesai Modu
att_shift_autoCalc=Bilgisayar Otomatik Hesaplama
att_shift_mustApply=Fazla Mesai Uygulanmalı
att_shift_mustOvertime=Fazla mesai yapmalı veya Devamsızlık almalı
att_shift_timeSmaller=Otomatik Hesaplama ile Fazla Mesai Arasındaki Süre Kısa
att_shift_notOvertime=Fazla Mesai Olarak Hesaplanmadı
att_shift_overtimeSign=Fazla Mesai Türü
att_shift_normal=Normal Gün
att_shift_restday=Dinlenme Günü
att_shift_timeSlotDetail=Zaman Tablosu Detayları
att_shift_doubleDeleteTimeSlot=Vardiya dönemini çift tıklayın; zaman aralığını silebilirsiniz
att_shift_addTimeSlot=Zaman Çizelgesi Ekle
att_shift_cleanTimeSlot=Tarifeyi Temizle
att_shift_NO=Num.
att_shift_notAll=Tümünün Seçimini Kaldır
att_shift_notTime=Zaman çizelgesi detayı onay kutusu işaretlenemezse, zaman çizelgesinde bir çakışma olduğunu gösterir.
att_shift_notExistTime=Bu zaman çizelgesi mevcut değil.
att_shift_cleanAllTimeSlot=Seçilen vardiya için zaman çizelgesini silmek istediğinizden emin misiniz?
att_shift_pleaseCheckBox=Lütfen sol taraftaki, sağ taraftaki geçerli görüntüleme zamanıyla aynı onay kutusunu seçin.
att_shift_pleaseUnit=Lütfen döngü birimlerini ve döngü sayısını doldurun.
att_shift_pleaseAllDetailTimeSlot=Lütfen zaman tablosu detaylarını seçin
att_shift_placeholderNo=S0 gibi S ile başlamanız önerilir.
att_shift_placeholderName=S ile başlamanız veya vardiya ile sonlandırmanız önerilir.
att_shift_workType=Çalışma tipi
att_shift_normalWork=Normal Çalışma
att_shift_holidayOt=Tatil FM
att_shift_attShiftStartDateRemark=Örnek: Döngünün başlangıç ​​tarihi üç günlük bir süre ile No. 22'dir, daha sonra 22/23/24 Sınıf A / B / C'de ve No. 19/20/21 Sınıf A'dadır. / B sınıfı / C sınıfı, tarihten önce ve sonra vb.
att_shift_isShiftWithinMonthRemark1=Ay içinde vardiya, döngü her ayın son gününe döner, ay boyunca ardışık olarak planlanmaz;
att_shift_isShiftWithinMonthRemark2=Aylık olmayan vardiyada, döngü her ayın son gününe çevrilir, eğer bir döngü bitmezse, bir sonraki aya devam edin, vb.
att_shift_workTypeRemark1=Normal Çalışma, fazla mesai işareti, tatiller hariç, tatillere katılım sayılmaz. Fazla mesai tatil olarak işaretlenirse, fazla mesai saatleri tatil olarak kaydedilir (normal çalışma saatleri hariç);
att_shift_workTypeRemark2=Hafta sonu FM, fazla mesai işareti varsayılan olarak dinlenme gününü kullanır ve bilgisayar fazla mesaiyi otomatik olarak hesaplar. Fazla mesai uygulaması gerekmez. Günün çalışma saatleri fazla mesai saatleri olarak kaydedilir ve tatillere devam sayılmaz.
att_shift_workTypeRemark3=Tatil FM, fazla mesai işareti varsayılan olarak tatil zamanıdır ve bilgisayar fazla mesaiyi otomatik olarak hesaplar, fazla mesai uygulaması gerekmez ve günün çalışma saatleri fazla mesai saatleri olarak kaydedilir;
att_shift_attendanceModeRemark1=Normalde vardiya ile hızlıca kaydırma dışında, erken veya gecikmeli fazla mesai olarak kabul edilmez, örneğin:
att_shift_attendanceModeRemark2=1. Check-in gerekli değildir veya günde bir kez geçerli bir kart kullanılır, fazla mesai sayılmaz;
att_shift_attendanceModeRemark3=2.İş türü: normal çalışma, katılım modu: ücretsiz kart tokatlamak, daha sonra gün vardiya süresi etkili çalışma süresi olarak kabul edilir;
att_shift_periodStartMode=Periyot Başlangıç Tipi
att_shift_periodStartModeByPeriod=Periyot Başlangıç Tarihi
att_shift_periodStartModeBySch=Vardiya Başlangıç Tarihi
att_shift_addTimeSlotToShift=Bu vardiyanın zaman aralığının eklenip eklenmeyeceği
#=====================================================================
#分组
att_group_editGroup=Personeli Grup için Düzenle
att_group_browseGroupPerson=Grup Personeline Göz Atın
att_group_list=Grup Listesi
att_group_placeholderNo=G1 gibi G ile başlamanız önerilir.
att_group_placeholderName=G ile başlamanız veya grupla sonlandırmanız önerilir.
att_widget_deptHint=Not: Seçilen departman altındaki tüm personeli içe aktarın
att_widget_searchType=Koşullu Sorgu
att_widget_noPerson=Hiç kimse seçilmedi
#分组排班
#部门排班
att_deptSch_existsDept=Bölümde bölüm değişimi vardır ve bölümün silinmesine izin verilmez.
#人员排班
att_personSch_view=Personel Planlamasını Görüntüle
#临时排班
att_schedule_type=Zamanlama türü
att_schedule_tempType=Geçici Tip
att_schedule_normal=Normal Program
att_schedule_intelligent=Akıllı Vardiya
att_tempSch_scheduleType=Zamanlama Türü
att_tempSch_startDate=Başlangıç Tarihi
att_tempSch_endDate=Bitiş tarihi
att_tempSch_attendanceMode=Zaman Kontrol Modu
att_tempSch_overtimeMode=Fazla Mesai Modu
att_tempSch_overtimeRemark=Fazla Mesai İşareti
att_tempSch_existsDept=Bu departmanda bölümün geçici bir planlaması vardır ve bu bölümün silinmesine izin verilmez.
att_schedult_opAddTempSch=Yeni Geçici Vardiya
att_schedule_cleanEndDate=Boş Bitiş zamanı
att_schedule_selectOne=Normal program sadece bir vardiya seçebilir!
att_schedule_selectPerson=Lütfen önce personeli seçin!
att_schedule_selectDept=Lütfen önce departmanı seçin!
att_schedule_selectGroup=Lütfen önce grubu seçin!
att_schedule_selectOneGroup=Sadece bir grup seçilebilir!
att_schedule_arrange=Lütfen bir vardiya seçin!
att_schedule_leave=İzin
att_schedule_trip=Fark
att_schedule_out=Dışarı
att_schedule_off=Dinlenme
att_schedule_makeUpClass=Ekleme
att_schedule_class=Ayarla
att_schedule_holiday=Tatil
att_schedule_offDetail=Dinlenmeyi Ayarla
att_schedule_makeUpClassDetail=Zaman Kontrol Ekle
att_schedule_classDetail=Vardiyayı Ayarla
att_schedule_holidayDetail=Tatil
att_schedule_noSchDetail=Planlanmadı
att_schedule_normalDetail=Normal
att_schedule_normalSchInfo=Vites Merkezi: Günler Arası Vardiya Yok
att_schedule_multipleInterSchInfo=Virgülle ayrılmış vardiya: birden çok gün arası vardiya
att_schedule_inderSchFirstDayInfo=Vardiyalar arasında geriye doğru kaydırma: Gün içi vardiyalar ilk gün olarak sayılır
att_schedule_inderSchSecondDayInfo=Izgara ileri ofseti boyunca kaydırma: Günler arası kaydırma ikinci gün olarak kaydedilir
att_schedule_timeConflict=Mevcut vardiya süresi ile çakışma var, kaydetmesine izin verilmiyor!
#=====================================================================
att_excp_notExisetPerson=Kişi yok!
att_excp_leavePerson=Personel dismission!
#补签单
att_sign_signTime=Okutma Zamanı
att_sign_signDate=Okutma Tarihi
#请假
att_leave_arilName=İzin Tipi
att_leave_image=Çıkış Fotoğraf Talebi
att_leave_imageShow=Resim yok
att_leave_imageType=Yanlış İpucu: Görüntü formatı doğru değil, formatı destekleyin: JPEG, GIF, PNG!
att_leave_imageSize=Yanlış İpucu: Seçilen resim çok büyük, resmin maksimum boyutu 4 MB!
att_leave_leaveLongDay=İzin Süresi (gün)
att_leave_leaveLongHour=Ayrılma süresi (saat)
att_leave_leaveLongMinute=İzin Süresi (dakika)
att_leave_endNoLessAndEqualStart=Bitiş zamanı, başlangıç ​​zamanından küçük veya ona eşit olamaz
att_leave_typeNameNoExsists=Sahte sınıf adı mevcut değil
att_leave_startNotNull=Başlangıç ​​zamanı boş olamaz
att_leave_endNotNull=Bitiş zamanı boş olamaz
att_leave_typeNameConflict=Yanlış tür adı, katılım durumu adıyla çakışıyor
#出差
att_trip_tripLongDay=Yolculuk Süresi (gün)
att_trip_tripLongMinute=Yolculuk Uzunluğu (dakika)
att_trip_tripLongHour=Seyahat süresi (saat)
#外出
att_out_outLongDay=Çıkış Uzunluğu (gün)
att_out_outLongMinute=Çıkış Uzunluğu (Dakikalar)
att_out_outLongHour=Harcanan zaman (zaman)
#加班
att_overtime_type=FM Tipi
att_overtime_normal=Normal FM
att_overtime_rest=Dinlenme Günü FM
att_overtime_overtimeLong=Fazla Mesai Süresi (dakika)
att_overtime_overtimeHour=Fazla mesai saatleri (saat)
att_overtime_notice=Fazla mesai süresine bir günden fazla izin verilmez!
att_overtime_minutesNotice=Fazla mesai uygulama süresi asgari fazla mesai süresinden az olamaz!
#调休补班
att_adjust_type=Türü Ayarla
att_adjust_adjustDate=Tarihi Ayarla
att_adjust_shiftName=Zaman Kontrol Vardiyası Ekle
att_adjust_selectClass=Lütfen ekte bulunması gereken vardiya adını seçin.
att_shift_notExistShiftWorkDate=Ayarlama tarihi, telafi vardiyasının iş gününde değildir ve eklenemez
att_adjust_shiftPeriodStartMode=Makyaj vardiyası tarafından seçilen vardiya, başlangıç ​​tarihi vardiyaya göre ise varsayılan olarak 0. vardiya alınacaktır
att_adjust_shiftNameNoNull=Makyaj vardiyası boş olamaz
att_adjust_shiftNameNoExsist=Makyaj vardiyası mevcut değil
#调班
att_class_type=Vardiya tipi
att_class_sameTimeMoveShift=Kişisel vardiyayı aynı gün içinde ayarlayın
att_class_differenceTimeMoveShift=Kişisel vardiyayı diğer günlerde ayarlayın
att_class_twoPeopleMove=İki kişi değişimi
att_class_moveDate=Tarihi Ayarla
att_class_shiftName=Orijinal Program Adı
att_class_moveShiftName=Ayarlanan yeni vardiya boş olamaz.
att_class_movePersonPin=Personel ID Ayarla
att_class_movePersonName=Personel Adını Ayarla
att_class_movePersonLastName=Personel Soyadını Ayarla
att_class_moveDeptName=Departman Adını Ayarla
att_class_personPin=Personel ID
att_class_shiftNameNoNull=Ayarlanan yeni vardiya boş olamaz.
att_class_personPinNoNull=Yeni kişinin Personel Kimliği boş olamaz!
att_class_isNotExisetSwapPersonPin=Yeni Ayarlama kişisi mevcut değil, lütfen tekrar ekleyin!
att_class_personNoSame=Aynı kişiye ayarlayamazsınız, lütfen tekrar deneyin.
att_class_outTime=Ayarlama tarihi ve aktarım tarihi bir aydan fazla olamaz！
att_class_shiftNameNoExsist=Ayarlanan vardiya mevcut değil
att_class_swapPersonNoExisist=SwapPersonNoExisist mevcut değil
att_class_dateNoSame=Bireyler farklı tarihlerde sınıf değiştirir, tarihler aynı olamaz
#=====================================================================
#节点
att_node_name=Düğüm
att_node_type=Düğüm Tipi
att_node_leader=Doğrudan Lider
att_node_leaderNode=Doğrudan Lider Düğümü
att_node_person=Tayin Edilen Kişi
att_node_position=Pozisyon Ata
att_node_choose=Konum Seçin
att_node_personNoNull=Personel boş bırakılamaz!
att_node_posiitonNoNull=Konum boş olamaz
att_node_placeholderNo=N01 gibi N ile başlamanız önerilir.
att_node_placeholderName=Yönetici Düğümü gibi bir düğümle biten bir konum veya adla başlamanız önerilir.
att_node_searchPerson=Girdi Arama Kriterleri
att_node_positionIsExist=Konum düğüm verilerinde zaten var, lütfen konumu tekrar seçin.
#流程
att_flow_type=Akış Tipi
att_flow_rule=Akış Kuralı
att_flow_rule0=1 günden az veya 1 güne eşit
att_flow_rule1=1 günden fazla ve 3 günden az veya eşit
att_flow_rule2=3 günden fazla ve 7 günden az veya eşit
att_flow_rule3=7 günden fazla
att_flow_node=Onay Düğümü
att_flow_start=Akışı Başlat
att_flow_end=Son Akış
att_flow_addNode=Düğüm Ekle
att_flow_placeholderNo=F01 gibi F ile başlamanız önerilir.
att_flow_placeholderName=Öneri türünün başlangıcı ve izin süreci gibi sürecin sonu
att_flow_tips=Not: Düğümlerin onay sırası yukarıdan aşağıya doğrudur ve seçtikten sonra sıralamayı sürükleyebilirsiniz.
#申请
att_apply_personPin=Başvuru Personeli ID
att_apply_type=İstisna Türü
att_apply_flowStatus=Akış Genel Durumu
att_apply_start=Uygulama başlatma
att_apply_flowing=Bekliyor
att_apply_pass=geçti
att_apply_over=Son
att_apply_refuse=Reddedildi
att_apply_revoke=İptal Et
att_apply_except=İstisna
att_apply_view=Detayları İncele
att_apply_leaveTips=Kişinin bu süre zarfında izin talebi vardır!
att_apply_tripTips=Personel bu süre zarfında bir iş gezisi başvurusu var!
att_apply_outTips=Personelin dışarı çıkmak için izin isteği var !
att_apply_overtimeTips=Personelin bu süre zarfında fazla mesai başvurusu var!
att_apply_adjustTips=Bekleme süresinde personel dinlenme talebinde bulundu!
att_apply_classTips=Personelin bu süre zarfında vardiya uygulaması var!
#审批
att_approve_wait=Onay bekleyen
att_approve_refuse=Geçmedi
att_approve_reason=Sebep
att_approve_personPin=Onaylayan ID
att_approve_personName=Onaylayan Adı
att_approve_person=Onaylayıcı
att_approve_isPass=Onaylamak ister misiniz?
att_approve_status=Geçerli Düğüm Durumu
att_approve_tips=Zaman noktası akışta zaten var ve tekrarlanamıyor.
att_approve_tips2=İşlem düğümü yapılandırılmadı, lütfen yapılandırmak için operatöre başvurun
att_approve_offDayConflicts=Çalışma günlerinde izin transferi başvurusuna izin verilmez
att_approve_shiftConflicts=Çalışma gününde makyaj uygulamasına izin verilmez
att_approve_shiftNoSch=Planlanan tarihte yeniden zamanlama uygulamasına izin verilmez.
att_approve_classConflicts=Planlanmamış tarihte vardiya uygulamasına izin verilmez
att_approve_selectTime=Seçim süresi süreci kurallara göre belirleyecektir
att_approve_withoutPermissionApproval=Onaylama izni olmayan bir iş akışı var, lütfen kontrol edin!
#=====================================================================
#考勤计算
att_op_calculation=Zaman Kontrol Hesaplaması
att_op_calculation_notice=Katılım verileri arka planda hesaplandı, lütfen daha sonra tekrar deneyin!
att_op_calculation_leave=İstifa eden personel dahil
att_statistical_choosePersonOrDept=Lütfen Departman veya Personel seçin!
att_statistical_sureCalculation=Zaman Kontrol hesaplamaları yapmak istediğinizden emin misiniz?
att_statistical_filter=Filtrasyon koşulu hazır!
att_statistical_initData=Veritabanının başlatılması tamamlandı!
att_statistical_exception=İstisna verilerinin başlatılması tamamlandı!
att_statistical_error=Devamsızlık hesaplaması başarısız oldu!
att_statistical_begin=hesaplamaya başlayın!
att_statistical_end=Hesaplamayı bitirin!
att_statistical_noticeTime=Zaman Kontrol isteğe bağlı zaman aralığı: İlk iki ay ile aynı gün!
att_statistical_remarkHoliday=S
att_statistical_remarkClass=S
att_statistical_remarkNoSch=Henüz değil
att_statistical_remarkRest=Geri
#原始记录表
att_op_importAccRecord=Erişim kontrol kaydını içe aktar
att_op_importParkRecord=Park kaydını içe aktar
att_op_importInsRecord=FaceKiosk kayıtlarını içe aktarma
att_op_importPidRecord=Kişisel Sertifika kayıtlarını içeri aktarma
att_op_importVmsRecord=Video Kaydını içe aktar
att_op_importUSBRecord=U disk kaydını içe aktar
att_transaction_noAccModule=Erişim kontrol modülü yok!
att_transaction_noParkModule=Park modülü yok!
att_transaction_noInsModule=FaceKiosk modülü yok!
att_transaction_noPidModule=Kişisel Sertifika modülü yok!
att_transaction_exportRecord=Orjinal Kayıt Çıktısı
att_transaction_exportAttPhoto=Katılım Resimlerini Çıkart
att_transaction_fileIsTooLarge=Çıkartılan dosya çok büyük, Tarih aralığını kısaltınız
att_transaction_exportDate=Çıkartma Tarihi
att_statistical_attDatetime=Katılım Tarihi
att_statistical_attPhoto=Zaman Kontrol Resmi
att_statistical_attDetail=Zaman Kontrol Detayları
att_statistical_acc=Geçiş Kontrol Cİhazı
att_statistical_att=Zaman Kontrol Cihazı
att_statistical_park=LPR Kamera
att_statistical_faceRecognition=Yüz tanıma cihazı
att_statistical_app=Mobil cihazlar
att_statistical_vms=Video Cihazları
att_statistical_psg=Kanal ekipmanı
att_statistical_dataSources=Veri kaynakları
att_transaction_SyncRecord=Katılım kaydını eşitle
#日打卡详情表
att_statistical_dayCardDetail=Giriş ayrıntıları
att_statistical_cardDate=Kayıt Tarihi
att_statistical_cardNumber=Kayıt Süreleri
att_statistical_earliestTime=En erken zaman
att_statistical_latestTime=En son zaman
att_statistical_cardTime=Okutma Saati
#请假汇总表
att_statistical_leaveDetail=Ayrıntıları Bırakın
#日明细报表
att_statistical_attDate=Zaman Kontrol Tarihi
att_statistical_week=Hafta
att_statistical_shiftInfo=Vardiya Bilgileri
att_statistical_shiftTimeData=Çalışma açma / kapama süresi
att_statistical_cardValidData=Okutma Zamanı
att_statistical_cardValidCount=Okutma sayısı
att_statistical_lateCount=Geç Gelme Sayısı
att_statistical_lateMinute=Geç Dakika
att_statistical_earlyCount=Erken Sayım
att_statistical_earlyMinute=Erken Dakika
att_statistical_countData=Frekans verileri
att_statistical_minuteData=Dakika Verileri
att_statistical_attendance_minute=Katılım (dakika)
att_statistical_overtime_minute=Fazla mesai (Dakika)
att_statistical_unusual_minute=Anormal (dakika)
#月明细报表
att_monthdetail_should_hour=Gerekir (zaman)
att_monthdetail_actual_hour=Asıl (Saat)
att_monthdetail_valid_hour=Geçerli (Saat)
att_monthdetail_absent_hour=Tamamlanma (zaman)
att_monthdetail_leave_hour=İzin (Saat) 
att_monthdetail_trip_hour=İş seyahati (saat)
att_monthdetail_out_hour=Dışarı Çık (saatlik)
att_monthdetail_should_day=Gerekir (gün)
att_monthdetail_actual_day=Gerçek (gün)
att_monthdetail_valid_day=Etkili (gün)
att_monthdetail_absent_day=Tamamlanma (gün)
att_monthdetail_leave_day=İzin (Gün)
att_monthdetail_trip_day=Resim yok
att_monthdetail_out_day=Dışarıda (Gün)
#月统计报表
att_statistical_late_minute=Bekleme (Dakika)
att_statistical_early_minute=Bekleme (Dakika)
#部门统计报表
#年度统计报表
att_statistical_should=meli
att_statistical_actual=Geçerli
att_statistical_valid=Geçerli
att_statistical_numberOfTimes=Saatler
att_statistical_usually=Hafta Günü
att_statistical_rest=Haftasonu
att_statistical_holiday=Tatil
att_statistical_total=Toplam
att_statistical_month=Aylık İstatistikler
att_statistical_year=Yılın İstatistikleri
att_statistical_attendance_hour=Zaman Kontrol (saat)
att_statistical_attendance_day=Zaman Kontrol (Gün)
att_statistical_overtime_hour=Fazla mesai (saat)
att_statistical_unusual_hour=İstisna (saat)
att_statistical_unusual_day=Anormal (gün)
#考勤设备参数
att_deviceOption_query=Cihaz Parametrelerini Görüntüleyin
att_deviceOption_noOption=Parametre bilgisi yok, lütfen önce cihaz parametrelerini alın
att_deviceOption_name=Parametre adı
att_deviceOption_value=Parametre Değeri
att_deviceOption_UserCount=Mevcut Kullanıcı Sayısı
att_deviceOption_MaxUserCount=Maksimum Kullanıcı Sayısı
att_deviceOption_FaceCount=Geçerli Yüz Sayısı
att_deviceOption_MaxFaceCount=Maksimum Yüz Sayısı
att_deviceOption_FacePhotoCount=Current number of facial images
att_deviceOption_MaxFacePhotoCount=Maximum number of facial images
att_deviceOption_FingerCount=Number of current fingerprint templates
att_deviceOption_MaxFingerCount=Maximum number of fingerprint templates
att_deviceOption_FingerPhotoCount=Geçerli parmak izi görüntü sayısı
att_deviceOption_MaxFingerPhotoCount=Maksimum parmak izi görüntüsü sayısı
att_deviceOption_FvCount=Mevcut Parmak Damar Sayısı
att_deviceOption_MaxFvCount=Maksimum Parmak Damar Sayısı
att_deviceOption_FvPhotoCount=Number of current finger vein images
att_deviceOption_MaxFvPhotoCount=Number of finger vein images
att_deviceOption_PvCount=Geçerli Avuç içi Numarası
att_deviceOption_MaxPvCount=Maksimum Avuç İçi Numarası
att_deviceOption_PvPhotoCount=Current palm images
att_deviceOption_MaxPvPhotoCount=Maximum number of palm images
att_deviceOption_TransactionCount=Mevcut Kayıt Sayısı
att_deviceOption_MaxAttLogCount=Maksimum Kayıt Sayısı
att_deviceOption_UserPhotoCount=Mevcut Kullanıcı Fotoğrafları
att_deviceOption_MaxUserPhotoCount=Maksimum Kullanıcı Fotoğrafı Sayısı
att_deviceOption_FaceVersion=Yüz Tanıma Algoritması Sürümü
att_deviceOption_FPVersion=Parmak İzi Tanıma Algoritması Sürümü
att_deviceOption_FvVersion=Parmak Damar Tanıma Algoritması Sürümü
att_deviceOption_PvVersion=Avuç Tanıma Algoritması Sürümü
att_deviceOption_FWVersion=Firmware Versiyon
att_deviceOption_PushVersion=Push Sürümü
#=====================================================================
#API
att_api_areaCodeNotNull=Alan numarası boş olamaz
att_api_pinsNotNull=Pin verilerinin boş olmasına izin verilmiyor
att_api_pinsOverSize=Pin veri uzunluğunun 500'ü geçmesine izin verilmiyor
att_api_areaNoExist=Alan mevcut değil
att_api_sign=Ek
#=====================================================================
#休息时间段
att_leftMenu_breakTime=Mola Zamanı
att_breakTime_startTime=Başlangıç Saati
att_breakTime_endTime=Bitiş Zamanı
#=====================================================================
#导入U盘记录
att_import_uploadFileSuccess=Dosya yükleme başarılı oldu ve dosya verileri ayrıştırıldı. Lütfen bekleyin ...
att_import_resolutionComplete=Analiz tamamlandıktan sonra veritabanını güncellemeye başlayın.
att_import_snNoExist=İçe aktarılan dosyaya karşılık gelen zaman kontrol cihazı mevcut değil. Lütfen dosyayı tekrar seçin.
att_import_fileName_msg=İçe aktarılan dosya adı formatı gereksinimleri: cihaz seri numarası ile başlar ve bir alt çizgiyle ayrılır
att_import_notSupportFormat=Bu biçim şimdilik desteklenmiyor!
att_import_selectCorrectFile=Lütfen doğru format dosyasını seçin!
att_import_fileFormat=Dosya Formatı
att_import_targetFile=Hedef Dosya
att_import_startRow=Başlığın başındaki satır sayısı
att_import_startRowNote=Veri formatının ilk satırı veri almaktır, lütfen içe aktarmadan önce dosyayı kontrol edin.
att_import_delimiter=Ayırıcı
#=====================================================================
#设备操作日志
att_device_op_log_op_type=İşlem kodu
att_device_op_log_dev_sn=Cihazseri numarası
att_device_op_log_op_content=Operasyonel içerik
att_device_op_log_operator_pin=Operatör numarası
att_device_op_log_operator_name=Operatör ismi
att_device_op_log_op_time=İşlem zamanı
att_device_op_log_op_who_value=İşlem nesnesi değeri
att_device_op_log_op_who_content=İşlem nesnesi açıklaması
att_device_op_log_op_value1=İşlem nesnesi 2
att_device_op_log_op_value_content1=İşlem nesnesi açıklaması 2
att_device_op_log_op_value2=İşlem nesnesi 3
att_device_op_log_op_value_content2=İşlem nesnesi açıklaması 3
att_device_op_log_op_value3=İşlem nesnesi 4
att_device_op_log_op_value_content3=İşlem nesnesi açıklaması 4
#操作日志的操作类型
att_device_op_log_opType_0=Açık
att_device_op_log_opType_1=Kapat
att_device_op_log_opType_2=Doğrulama başarısız oldu
att_device_op_log_opType_3=Alarm
att_device_op_log_opType_4=Menüye girin
att_device_op_log_opType_5=Ayarları değiştir
att_device_op_log_opType_6=Parmak izini kaydetme
att_device_op_log_opType_7=Kayıt şifresi
att_device_op_log_opType_8=HID kartını kaydet
att_device_op_log_opType_9=Kullanıcı Sil
att_device_op_log_opType_10=Parmak izini sil
att_device_op_log_opType_11=Parolayı sil
att_device_op_log_opType_12=RF kartını sil
att_device_op_log_opType_13=Veri Sil
att_device_op_log_opType_14=MF kartı oluştur
att_device_op_log_opType_15=MF kartını kaydet
att_device_op_log_opType_16=MF kartını kaydet
att_device_op_log_opType_17=MF kartı kaydını sil
att_device_op_log_opType_18=MF kart içeriğini temizle
att_device_op_log_opType_19=Kayıt verilerini karta taşıyın
att_device_op_log_opType_20=Verileri karttan makineye kopyalayın
att_device_op_log_opType_21=Zamanı ayarla
att_device_op_log_opType_22=Fabrika ayarları
att_device_op_log_opType_23=Giriş ve çıkış kayıtlarını silme
att_device_op_log_opType_24=Yönetici ayrıcalıklarını temizle
att_device_op_log_opType_25=Erişim kontrol grubu ayarlarını değiştirme
att_device_op_log_opType_26=Kullanıcı erişim ayarlarını değiştirme
att_device_op_log_opType_27=Erişim süresini değiştirme
att_device_op_log_opType_28=Kilit açma kombinasyonu ayarlarını değiştirme
att_device_op_log_opType_29=Kilidi Aç
att_device_op_log_opType_30=Yeni kullanıcılar kaydedin
att_device_op_log_opType_31=Parmak izi özelliklerini değiştirme
att_device_op_log_opType_32=Zorlama alarmı
att_device_op_log_opType_34=Anti-passback
att_device_op_log_opType_35=Zaman Kontrol fotoğraflarını sil
att_device_op_log_opType_36=Kullanıcı bilgilerini değiştirme
att_device_op_log_opType_37=Tatil
att_device_op_log_opType_38=Verileri geri yükle
att_device_op_log_opType_39=Verileri yedekle
att_device_op_log_opType_40=U disk yükleme
att_device_op_log_opType_41=U disk indirme
att_device_op_log_opType_42=U disk katılım kaydı şifrelemesi
att_device_op_log_opType_43=Başarılı bir USB disk indirildikten sonra kaydı sil
att_device_op_log_opType_53=Giden anahtar
att_device_op_log_opType_54=Kapı sensörü
att_device_op_log_opType_55=Alarm
att_device_op_log_opType_56=Parametreleri geri yükle
att_device_op_log_opType_68=Kayıtlı kullanıcı fotoğrafı
att_device_op_log_opType_69=Kullanıcı fotoğraflarını değiştirme
att_device_op_log_opType_70=Kullanıcı adını değiştir
att_device_op_log_opType_71=Kullanıcı izinlerini değiştirme
att_device_op_log_opType_76=Ağ ayarları IP'sini değiştirme
att_device_op_log_opType_77=Ağ ayarları maskesini değiştirme
att_device_op_log_opType_78=Ağ ayarları ağ geçidini değiştirme
att_device_op_log_opType_79=Ağ ayarları DNS'sini değiştirme
att_device_op_log_opType_80=Bağlantı kurulum şifresini değiştirme
att_device_op_log_opType_81=Bağlantı ayarları cihaz kimliğini değiştirme
att_device_op_log_opType_82=Bulut sunucusu adresini değiştirme
att_device_op_log_opType_83=Bulut sunucusu bağlantı noktasını değiştirme
att_device_op_log_opType_87=Erişim kontrolü kayıt ayarlarını değiştirme
att_device_op_log_opType_88=Yüz parametresi bayrağını değiştirme
att_device_op_log_opType_89=Parmak izi parametre bayrağını değiştirme
att_device_op_log_opType_90=Parmak damar parametresi bayrağını değiştirme
att_device_op_log_opType_91=Avuç izi parametre bayrağını değiştirme
att_device_op_log_opType_92=u disk yükseltme bayrağı
att_device_op_log_opType_100=RF kart bilgilerini değiştirme
att_device_op_log_opType_101=Yüzü kaydet
att_device_op_log_opType_102=Personel izinlerini değiştirme
att_device_op_log_opType_103=Personel izinlerini sil
att_device_op_log_opType_104=Personel izinleri ekleyin
att_device_op_log_opType_105=Erişim kontrol kaydını sil
att_device_op_log_opType_106=Yüzü sil
att_device_op_log_opType_107=Personel fotoğraflarını silme
att_device_op_log_opType_108=Parametreleri değiştirme
att_device_op_log_opType_109=WIFI SSID'yi seçin
att_device_op_log_opType_110=proxy etkinleştir
att_device_op_log_opType_111=Proxy IP değişikliği
att_device_op_log_opType_112=proxy bağlantı noktası değişikliği
att_device_op_log_opType_113=Kişi şifresini değiştirme
att_device_op_log_opType_114=Yüz bilgilerini değiştirme
att_device_op_log_opType_115=Operatörün şifresini değiştirme
att_device_op_log_opType_116=Erişim kontrol ayarlarını sürdür
att_device_op_log_opType_117=operatör şifresi giriş hatası
att_device_op_log_opType_118=operatör şifre kilidi
att_device_op_log_opType_120=Legic Kart Veri Uzunluğu Düzenle
att_device_op_log_opType_121=Parmak Damar Kaydı
att_device_op_log_opType_122=Parmak Damar Düzenle
att_device_op_log_opType_123=Parmak Damar Sil
att_device_op_log_opType_124=Avuç İçi Kaydı
att_device_op_log_opType_125=Avuç İçi Düzenle
att_device_op_log_opType_126=Avuç İçi Sil
#操作对象描述
att_device_op_log_content_pin=Kull ID :
att_device_op_log_content_alarm=Alarm:
att_device_op_log_content_alarm_reason=Alarm nedeni:
att_device_op_log_content_update_no=Öğe numarasını değiştirin:
att_device_op_log_content_update_value=Değeri değiştir:
att_device_op_log_content_finger_no=Parmakizi Num.
att_device_op_log_content_finger_size=Parmak izi şablon uzunluğu:
#=====================================================================
#工作流
att_flowable_datetime_to=den
att_flowable_todomsg_leave=İzin Onayı
att_flowable_todomsg_sign=Günlük onayını ekle
att_flowable_todomsg_overtime=Fazla mesai onayı
att_flowable_notifymsg_leave=Uygulama bildirimini bırakın
att_flowable_notifymsg_sign=Kayıt bildirimi ekle
att_flowable_notifymsg_overtime=Fazla mesai bildirimi
att_flowable_shift=Vardiya:
att_flowable_hour=Saat
att_flowable_todomsg_trip=İş gezisi onayı
att_flowable_notifymsg_trip=İş Gezisi
att_flowable_todomsg_out=Onay al
att_flowable_notifymsg_out=Dışarı bildirim
att_flow_apply=Uygulama
att_flow_applyTime=uygulama zamanı
att_flow_approveTime=İşlem süresi
att_flow_operateUser=Eleştirmen
att_flow_approve=Onay
att_flow_approveComment=Not
att_flow_approvePass=Onay sonuçları
att_flow_status_processing=Onay
#=====================================================================
#biotime
att_h5_pers_personIdNull=Çalışan kimliği boş olamaz
att_h5_attPlaceNull=Giriş yeri boş olamaz
att_h5_attAreaNull=Zaman Kontrol alanı boş bırakılamaz
att_h5_pers_personNoExist=Personel numarası mevcut değil
att_h5_signRemarkNull=Açıklamalar boş olamaz
att_h5_common_pageNull=Sayfalama parametre hatası
att_h5_taskIdNotNull=Görev düğümü kimliği boş olamaz
att_h5_auditResultNotNull=Onay sonucu boş olamaz
att_h5_latLongitudeNull=Boylam ve enlem boş olamaz
att_h5_pers_personIsNull=Çalışan ıd mevcut değil
att_h5_pers_personIsNotInArea=Kişi alanı ayarlamadı
att_h5_mapApiConnectionsError=Harita API bağlantısı hatası
att_h5_googleMap=Google Map
att_h5_gaodeMap=Gaode Haritası
att_h5_defaultMap=Varsayılan Harita
att_h5_shiftTime=Vardiya Saati
att_h5_signTimes=İkmal süresi
att_h5_enterKeyWords=Lütfen anahtar kelimeleri girin:
att_h5_mapSet=Katılım haritası ayarları
att_h5_setMapApiAddress=Harita parametresini ayarla
att_h5_MapSetWarning=Haritayı değiştirmek, girilen mobil terminal oturum açma adresinin enlem ve boylam ile uyuşmamasına neden olur, lütfen dikkatli bir şekilde değiştirin!
att_h5_mapSelect=Harita seçimi
att_h5_persNoHire=Çalışan henüz şirkete katılmamıştır.
att_slef_apiKey=API-KEY
att_self_apiJsKey=API-JS-KEY
att_self_noComputeAtt=Günün Zaman Kontorlü henüz açıklanmamıştır.
att_self_noSignRecord=Gün içinde okutma kaydı yok
att_self_imageUploadError=Resim yüklenemedi
att_self_attSignAddressAreaIsExist=Bölgede zaten Giriş noktaları var
att_self_signRuleIsError=Mevcut ikmal süresi, izin verilen ikmal süresi içinde değildir.
att_self_signAcrossDay=Çapraz gün programı imzalanamıyor!
att_self_todaySignIsExist=Bugün zaten eklenmiş ekler var!
att_self_signSetting=İşaret ayarı
att_self_allowSign=İşarete izin ver:
att_self_allowSignSuffix=gün içinde katılım kaydı
att_self_onlyThisMonth=Sadece bu ay
att_self_allowAcrossMonth=Çapraz aya izin ver
att_self_thisTimeNoSch=Mevcut zaman diliminde değişiklik yok!
att_self_revokeReason=İptal nedeni:
att_self_revokeHint=Lütfen incelenmek üzere 20 karakter içinde iptal nedenini girin
att_self_persSelfLogin=Çalışan self servis girişi
att_self_isOpenSelfLogin=Çalışan self servis giriş girişinin başlatılıp başlatılmayacağı
att_self_applyAndWorkTimeOverlap=Uygulama süresi ve çalışma süresi çakışması
att_apply_DurationIsZero=Başvuru süresi 0, uygulamaya izin verilmiyor
att_sign_mapWarn=Harita yüklenemedi, lütfen ağ bağlantısını kontrol edin ve KEY değerini eşleyin
att_admin_applyWarn=İşlem başarısız oldu, planlanmamış kişiler var veya başvuru süresi program kapsamında değil! ({0})
att_self_getPhotoFailed=Resim mevcut değil
att_self_view=Görüntüle
# 二维码
att_param_qrCodeUrl=QR kodu URL'si
att_param_qrCodeUrlHref=Sunucu adresi: bağlantı noktası
att_param_appAttQrCode=Mobil katılım QR kodu
att_param_timingFrequency=Zaman aralığı: 5-59 dakika veya 1-24 saat
att_sign_signTimeNotNull=Ekleme günlüğü süresi boş olamaz
att_apply_overLastMonth=Başvurular geçen aydan daha uzun süre başladı
att_apply_withoutDetail=İşlem ayrıntısı yok
att_flowable_noAuth=Görüntülemek için lütfen süper yönetici hesabı kullanın
att_apply_overtimeOverMaxTimeLong=Fazla mesai saatleri azami mesai saatlerinden fazla
# 考勤设置参数符号
att_other_arrive=√
att_other_late=Geç
att_other_early=Erken
att_other_absent=Çöl
att_other_noSignIn=[Eksik
att_other_noSignOff=Eksiklik
att_other_leave=Yanlış
att_other_overtime=Ekle
att_other_off=Ayarlanabilir
att_other_classes=Dolgu
att_other_trip=Zayıf
att_other_out=Dışarıda
att_other_incomplete=Tamamlanmamış
att_other_outcomplete=Tamamlanmadı
# 服务器下发命令
att_devCmd_submitTime=Gönderme zamanı
att_devCmd_returnedResult=ReturnedResult
att_devCmd_returnTime=Dönüş Zamanı
att_devCmd_content=Komut içeriği
att_devCmd_clearCmd=Komut listesini temizle
# 实时点名
att_realTime_selectDept=Lütfen bir bölüm seçin
att_realTime_noSignPers=Oturum açılmadı
att_realTime_signPers=Oturum açıldı
att_realTime_signMonitor=Oturum açma monitörü
att_realTime_signDateTime=Oturum açma zamanı
att_realTime_realTimeSet=Gerçek zamanlı yoklama ayarı
att_realTime_openRealTime=Gerçek zamanlı yoklamayı etkinleştir
att_realTime_rollCallEnd=Gerçek zamanlı yoklama çağrısı sonlandırma
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=Planlandı
att_personSch_cycleSch=Periyodik program
att_personSch_cleanCycleSch=Temiz döngü programı
att_personSch_cleanTempSch=Geçici programı temizle
att_personSch_personCycleSch=kişi döngüsü programı
att_personSch_deptCycleSch=Departman döngüsü programı
att_personSch_groupCycleSch=Grup döngüsü planlama
att_personSch_personTempSch=Geçici personel planlaması
att_personSch_deptTempSch=Departman geçici programı
att_personSch_groupTempSch=Grup geçici programı
att_personSch_checkGroupFirst=Lütfen çalışmak için soldaki grubu veya sağdaki listedeki kişiyi kontrol edin!
att_personSch_sureDeleteGroup={0} ve gruba karşılık gelen programı silmek istediğinizden emin misiniz?
att_personSch_sch=Program
att_personSch_delSch=Planı sil
#考勤计算
att_statistical_sureAllCalculate=Tüm personel için katılım hesaplaması yapmak istediğinizden emin misiniz?
#异常管理
att_exception_downTemplate=İçe aktarma şablonunu indirin
att_exception_signImportTemplate=İçe Aktarma Şablonunu İmzala
att_exception_leaveImportTemplate=İçe Aktarma Şablonundan Ayrıl
att_exception_overtimeImportTemplate=Fazla Mesai İçe Aktarma Şablonu
att_exception_adjustImportTemplate=İçe Aktarma Şablonunu Ayarla
att_exception_cellDefault=gerekli olmayan alan
att_exception_cellRequired=Gerekli alan
att_exception_cellDateTime=Gerekli alan, saat biçimi yyyy-MM-gg SS: dd: ss şeklindedir, örneğin: 2020-07-07 08:30:00
att_exception_cellLeaveTypeName='kişisel izin', 'evlilik izni', 'doğum izni', 'hastalık izni', 'yıllık izin', 'ölüm izni', 'aile izni', 'emzirme izni', 'iş gibi zorunlu alan gezi ',' dışarı çıkma 'Küstah adı
att_exception_cellOvertimeSign=Gerekli alan, örneğin: 'normal fazla mesai', 'dinlenme günlerinde fazla mesai', 'tatillerde fazla mesai'
att_exception_cellAdjustType='transfer kapalı', 'makyaj sınıfı' gibi gerekli alan
att_exception_cellAdjustDate=Gerekli alan, saat biçimi yyyy-MM-dd şeklindedir, örneğin: 2020-07-07
att_exception_cellShiftName=Ayar türü shift shift olduğunda gerekli alan
att_exception_refuse=Reddet
att_exception_end=Anormal son
att_exception_delete=Sil
att_exception_stop=Duraklat
#时间段
att_timeSlot_normalTimeAdd=Normal zaman aralığı ekle
att_timeSlot_elasticTimeAdd=Esnek zaman aralığı ekle
#班次
att_shift_addRegularShift=Normal vardiya ekle
att_shift_addFlexibleShift=Esnek vardiya ekle
#参数设置
att_param_notLeaveSetting=Yanlış olmayan hesaplama ayarı
att_param_smallestUnit=Minimum birim
att_param_workDay=iş günü
att_param_roundingControl=Yuvarlama Kontrolü
att_param_abort=Aşağı (terk)
att_param_rounding=yuvarlama
att_param_carry=yukarı (taşıma)
att_param_reportSymbol=Rapor görüntüleme simgesi
att_param_convertCountValid=Lütfen bir sayı girin ve yalnızca bir ondalık basamağa izin verilir
att_other_leaveThing=Şey
att_other_leaveMarriage=Evlilik
att_other_leaveBirth=ürün
att_other_leaveSick=Hasta
att_other_leaveAnnual=year
att_other_leaveFuneral=Cenaze
att_other_leaveHome=Keşfedin
att_other_leaveNursing=Hemşirelik
att_other_leavetrip=Farklı
att_other_leaveout=diğer
att_common_schAndRest=Planla ve dinlen
att_common_timeLongs=Zaman uzunluğu
att_personSch_checkDeptOrPersFirst=Lütfen çalışmak için soldaki departmanı veya sağdaki listedeki personeli kontrol edin!
att_personSch_checkCalendarFirst=Lütfen önce planlanması gereken tarihi seçin!
att_personSch_cleanCheck=Kontrolü temizle
att_personSch_delTimeSlot=Seçili zaman aralığını temizle
att_personSch_repeatTimeSlotNoAdd=Tekrar eden bir zaman dilimi olduğunda eklemeyin!
att_personSch_showSchInfo=Plan ayrıntılarını göster
att_personSch_sureToCycleSch=Periyodik olarak {0} planlamak istediğinizden emin misiniz?
att_personSch_sureToTempSch=Geçici olarak {0} planlayacağınızdan emin misiniz?
att_personSch_sureToCycleSchDeptOrGroup={0} altındaki tüm personeli planlamak istediğinizden emin misiniz?
att_personSch_sureToTempSchDeptOrGroup=Tüm personeli {0} altında geçici olarak planlayacağınızdan emin misiniz?
att_personSch_sureCleanCycleSch={0} için {1} ile {2} arasındaki periyodik programı temizlediğinizden emin misiniz?
att_personSch_sureCleanTempSch={0} için {1} ile {2} arasındaki geçici programı temizlediğinizden emin misiniz?
att_personSch_sureCleanCycleSchDeptOrGroup={0} altındaki tüm personelin döngü programını {1} ile {2} arasında temizlediğinizden emin misiniz?
att_personSch_sureCleanTempSchDeptOrGroup={0} altındaki tüm personelin geçici programını {1} ile {2} arasında temizlediğinizden emin misiniz?
att_personSch_today=Bugün
att_personSch_timeSoltName=Zaman dönemi adı
att_personSch_export=Personel programını dışa aktar
att_personSch_exportTemplate=Geçici personel planlama şablonunu indirin
att_personSch_import=İçe aktarılan personelin geçici programı
att_personSch_tempSchTemplate=geçici personel planlama şablonu
att_personSch_tempSchTemplateTip=Program şablonunu o tarih içinde indirmek için lütfen programın başlangıç ​​ve bitiş zamanını seçin
att_personSch_opTip=İşlem talimatları
att_personSch_opTip1=1, vardiyaları programlamak için zaman periyodunu takvim kontrolündeki tek bir tarihe sürüklemek için fareyi kullanabilirsiniz.
att_personSch_opTip2=2, takvim denetiminde vardiyaları planlamak için tek bir tarihi çift tıklayabilirsiniz.
att_personSch_opTip3=3. Takvim denetiminde, fareyi basılı tutun ve planlama için birden çok tarih seçmek üzere hareket ettirin.
att_personSch_schRules=Kuralları planla
att_personSch_schRules1=1. Periyodik program: bir kesişme olup olmadığına bakılmaksızın aynı tarihten sonraki önceki programı kapsar.
att_personSch_schRules2=2. Geçici program: aynı tarihin bir kesişme noktası vardır, ikincisi önceki geçici programı kapsayacaktır, kesişme yoksa, aynı zamanda var olacaktır.
att_personSch_schRules3=3. Dönem ve Geçici: Aynı tarihin bir kesişme noktası varsa, dönem geçici olarak kapsanacak ve kesişme yoksa dönem aynı anda var olacaktır; periyodik vardiya tipi akıllı vites değiştirme ise, dönem doğrudan geçici vardiya tarafından karşılanacaktır.
att_personSch_schStatus=Plan durumu
#左侧菜单-排班管理
att_leftMenu_schDetails=Plan ayrıntıları
att_leftMenu_detailReport=Katılım ayrıntı raporu
att_leftMenu_signReport=Kayıt ayrıntıları tablosu
att_leftMenu_leaveReport=Ayrıntı Formundan Ayrıl
att_leftMenu_abnormal=Anormal katılım tablosu
att_leftMenu_yearLeaveSumReport=Yıllık İzin Toplamı Raporu
att_leave_maxFileCount=En fazla 4 fotoğraf ekleyebilirsiniz
#时间段
att_timeSlot_add=Zaman aralığını ayarla
att_timeSlot_select=Lütfen bir zaman aralığı seçin!
att_timeSlot_repeat="{0}" zaman aralığı tekrarlanıyor!
att_timeSlot_overlapping="{0}" zaman aralığı, "{1}" işe gidip gelme süresi ile çakışıyor!
att_timeSlot_addFirst=Lütfen önce zaman aralığını ayarlayın!
att_timeSlot_notEmpty={0} personel numarasına karşılık gelen zaman aralığı boş olamaz!
att_timeSlot_notExist={0} personel numarasına karşılık gelen "{1}" süresi mevcut değil!
att_timeSlot_repeatEx=Personel numarası {0} çakışmasına karşılık gelen "{1}" ve "{2}" dönemi
att_timeSlot_importRepeat={0} personel numarasına karşılık gelen "{1}" dönemi tekrarlanır
att_timeSlot_importNotPin=Sistemde {0} numaralı kişi yok!
att_timeSlot_elasticTimePeriod=หมายเลขบุคลากร {0} ไม่สามารถนำเข้าช่วงเวลายืดหยุ่น "{1}"!
#导入
att_import_overData=Şu anki içe aktarılan öğe sayısı {0}, bu da 30.000 sınırını aşıyor, lütfen gruplar halinde içe aktarın!
att_import_existIllegalType=İçe aktarılan {0} geçersiz bir türe sahip!
#验证方式
att_verifyMode_0=Otomatik tanımlama
att_verifyMode_1=Yalnızca parmak izi
att_verifyMode_2=Çalışma Kimliği doğrulaması
att_verifyMode_3=Yalnızca şifre
att_verifyMode_4=Yalnızca kart
att_verifyMode_5=Parmak izi veya şifre
att_verifyMode_6=Parmak izi veya kart
att_verifyMode_7=Kart veya şifre
att_verifyMode_8=İş Kimliği artı parmak izi
att_verifyMode_9=Parmak izi artı şifre
att_verifyMode_10=Kart artı parmak izi
att_verifyMode_11=Kart artı şifre
att_verifyMode_12=Parmak izi artı şifre artı kart
att_verifyMode_13=İş Kimliği artı parmak izi artı şifre
att_verifyMode_14=(iş kimliği artı parmak izi) veya (kart artı parmak izi)
att_verifyMode_15=Yüz
att_verifyMode_16=Yüz artı parmak izi
att_verifyMode_17=Yüz artı şifre
att_verifyMode_18=Yüz artı kartı
att_verifyMode_19=Yüz artı parmak izi artı kart
att_verifyMode_20=Yüz artı parmak izi artı şifre
att_verifyMode_21=parmak damarı
att_verifyMode_22=Parmak damarı artı şifre
att_verifyMode_23=Parmak damar artı kartı
att_verifyMode_24=Parmak damarı artı şifre artı kart
att_verifyMode_25=Palmiye baskısı
att_verifyMode_26=Palmiye baskı artı kartı
att_verifyMode_27=Palm baskılar ve yüz
att_verifyMode_28=Avuç içi izi ve parmak izi
att_verifyMode_29=Avuç içi izi artı parmak izi artı yüz
# 工作流
att_flow_schedule=Denetim ilerlemesi
att_flow_schedulePass=(Geçti)
att_flow_scheduleNot=(Onaylanmadı)
att_flow_scheduleReject=(Reddedildi)
# 工作时长表
att_workTimeReport_total=Toplam Çalışma Zamanı
# 自动导出报表
att_autoExport_startEndTime=Başlangıç ​​ve bitiş zamanı
# 年假
att_annualLeave_setting=Yıllık İzin Bakiyesi Ayarı
att_annualLeave_settingTip1=Yıllık izin bakiyesi işlevini kullanmak için, her çalışanın işe alma süresini ayarlamanız gerekir; işe alma süresi ayarlanmadığında, çalışanın yıllık izin bakiye tablosunun kalan yıllık izni boş olarak görüntülenir.
att_annualLeave_settingTip2=Mevcut tarih, takas düzenleme tarihinden daha büyükse, bu değişiklik bir sonraki yıl yürürlüğe girecektir; mevcut tarih, takas düzenleme tarihinden küçükse, takas gerçekleştirilecek ve yıllık izin takas sırasında yeniden verilecektir. çıkış tarihine ulaşıldı.
att_annualLeave_calculate=Yıllık izin takas tarihi
att_annualLeave_workTimeCalculate=Çalışma süresi oranına göre hesapla
att_annualLeave_rule=Yıllık İzin Süresi Kuralı
att_annualLeave_ruleCountOver=Ayarlanan maksimum sayı sınırına ulaşıldı
att_annualLeave_years=Kıdemli yıllar
att_annualLeave_eachYear=Her yıl
att_annualLeave_have=Evet
att_annualLeave_days=Yıllık izin günleri
att_annualLeave_totalDays=Toplam yıllık izin
att_annualLeave_remainingDays=Kalan yıllık izin
att_annualLeave_consecutive=Yıllık izin kuralı ayarı ardışık yıllar olmalıdır
# 年假结余表
att_annualLeave_report=Yıllık İzin Bilançosu
att_annualLeave_validDate=Geçerli Tarih
att_annualLeave_useDays={0} gün kullanın
att_annualLeave_calculateDays=Yayın {0} gün
att_annualLeave_notEnough={0} için yetersiz yıllık izin!
att_annualLeave_notValidDate={0} geçerli yıllık izin aralığında değil!
att_annualLeave_notDays={0} yıllık izne sahip değil!
att_annualLeave_tip1=Zhang San geçen yıl 1 Eylül'de katıldı
att_annualLeave_tip2=Yıllık izin bakiyesi ayarı
att_annualLeave_tip3=Takas ve tanzim tarihi her yıl 1 Ocak, çalışma oranına göre hesaplanır; çalışma deneyimi 1 veya daha az ise 3 gün yıllık izin, 1 yıl ise 5 gün yıllık izin 3 yıldan az veya eşit
att_annualLeave_tip4=Yıllık izin hakkı hesaplaması
att_annualLeave_tip5=Geçen yıl 09-01 ~ 12-31 4 / 12x3 = 1.0 gün keyfini çıkarın
att_annualLeave_tip6=Bu yıl 01-01 ~ 12-31 4.0 gün keyfini çıkarın (bu yıl 01-01 ~ 08-31 8 / 12x3 = 2.0 gün keyfini çıkarın + bu yıl 09-01 ~ 12-31 4 / 12x5≈2.0 günün tadını çıkarın)
# att SDC
att_sdc_name=Video ekipmanı
att_sdc_wxMsg_firstData=Merhaba, bir katılım kontrolü bildiriminiz var
att_sdc_wxMsg_stateData=Hissetmeden katılımda başarılı saat
att_sdc_wxMsg_remark=Hatırlatma: Nihai katılım sonucu, kayıt ayrıntıları sayfasına tabidir.
# 时间段
att_timeSlot_conflict=Zaman dilimi, günün diğer zaman aralıklarıyla çakışıyor
att_timeSlot_selectFirst=Lütfen zaman aralığını seçin
# 事件中心
att_eventCenter_sign=Katılım oturumu açma
#异常管理
att_exception_classImportTemplate=Sınıf İçe Aktarma Şablonu
att_exception_cellClassAdjustType=Gerekli alan, örneğin: "{0}", "{1}", "{2}"
att_exception_swapDateDate=zorunlu olmayan alan, saat biçimi yyyy-AA-gg şeklindedir, örneğin: 2020-07-07
#消息中心
att_message_leave=Katılım bildirimi {0}
att_message_leaveContent={0} gönderildi {1}, {2} saat {3}~{4}
att_message_leaveTime=Zaman Ayrıl
att_message_overtime=Katılım ve fazla mesai bildirimi
att_message_overtimeContent={0} fazla mesai gönderdi ve fazla mesai süresi {1}~{2}
att_message_overtimeTime=Fazla mesai
att_message_sign=Katılım bildirimi işareti
att_message_signContent={0} ek bir işaret gönderdi ve ek imza zamanı {1}
att_message_adjust=Katılım düzeltme bildirimi
att_message_adjustContent={0} bir düzenleme gönderdi ve düzenleme tarihi {1}
att_message_class=Katılım ve vardiya bildirimi
att_message_classContent=Sınıf İçeriği
att_message_classContent0={0} bir vardiya gönderdi, vardiya tarihi {1} ve vardiya {2}
att_message_classContent1={0} bir vardiya gönderdi, vardiya tarihi {1} ve vardiya tarihi {2}
att_message_classContent2={0} ({1}) ve {2} ({3}) sınıf değiştirme
#推送中心
att_pushCenter_transaction=Katılım kaydı
# 时间段
att_timeSlot_workTimeNotEqual=Çalışma zamanı, işten çıkış zamanına eşit olamaz
att_timeSlot_signTimeNotEqual=Başlangıç oturum açma zamanı, bitiş oturum kapatma zamanına eşit olamaz
# 北向接口A
att_api_notNull={0} boş olamaz!
att_api_startDateGeEndDate=Başlangıç zamanı bitiş zamanından büyük veya ona eşit olamaz!
att_api_leaveTypeNotExist=Sahte tür mevcut değil!
att_api_imageLengthNot2000=Görüntü adresinin uzunluğu 2000'i aşamaz!
# 20221230新增国际化
att_personSch_workTypeNotNull={0} kişilik numarasına uygun çalışma tipi boş olamaz!
att_personSch_workTypeNotExist={0} kişilik numarasına uygun çalışma tipi yok!
att_annualLeave_recalculate=Yeniden hesapla
# 20230530新增国际化
att_leftMenu_dailyReport=Günlük Katılım Raporu
att_leftMenu_overtimeReport=Fazla Mesai Raporu
att_leftMenu_lateReport=Geç Rapor
att_leftMenu_earlyReport=Raporu Erken Bırak
att_leftMenu_absentReport=Yok Raporu
att_leftMenu_monthReport=Aylık Katılım Raporu
att_leftMenu_monthWorkTimeReport=Aylık Çalışma Süresi Raporu
att_leftMenu_monthCardReport=Aylık kart raporu
att_leftMenu_monthOvertimeReport=Aylık Fazla Mesai Raporu
att_leftMenu_overtimeSummaryReport=Personel fazla mesai özet raporu
att_leftMenu_deptOvertimeSummaryReport=Bölüm Fazla Mesai Özet Raporu
att_leftMenu_deptLeaveSummaryReport=Bölüm İzin Özet Raporu
att_annualLeave_calculateDay=Yıllık izin gün sayısı
att_annualLeave_adjustDay=Günleri Ayarla
att_annualLeave_sureSelectDept=Seçilen bölümde {0} işlemi yapmak istediğinizden emin misiniz?
att_annualLeave_sureSelectPerson=Seçilen kişi üzerinde {0} işlemi yapmak istediğinizden emin misiniz?
att_annualLeave_calculateTip1=Hizmet süresine göre hesaplanırken: Yıllık izin hesabı ay bazında doğrudur, kıdem 10 yıl 3 ay ise 10 yıl 3 ay hesaplanır;
att_annualLeave_calculateTip2=Dönüşüm hizmet süresine göre yapılmadığında: yıllık izin hesaplaması yıla göre doğrudur, hizmet süresi 10 yıl 3 ay ise hesaplama için 10 yıl kullanılır;
att_rule_isInCompleteTip=Öncelik, oturum açmama veya oturum kapatmama eksik olarak kaydedildiğinde ve gecikme, erken izin, devamsızlık ve geçerli olduğunda en yüksektir.
att_rule_absentTip=Giriş yapılmaması veya çıkış yapılmaması devamsızlık olarak kaydedildiğinde, devamsızlık süresi çalışma saatlerinin uzunluğundan geç ve erken izin süresinin uzunluğuna eşittir
att_timeSlot_elasticTip1=0, geçerli süre gerçek süreye eşittir, devamsızlık yok
att_timeSlot_elasticTip2=Gerçek süre çalışma süresinden uzunsa, geçerli süre çalışma süresine eşittir, devamsızlık yapılmaz
att_timeSlot_elasticTip3=Gerçek süre çalışma süresinden azsa, etkin süre gerçek süreye eşittir ve devamsızlık, çalışma süresi eksi gerçek süreye eşittir
att_timeSlot_maxWorkingHours=Çalışma saatleri şundan fazla olamaz:
# 20231030
att_customReport=Devamlılık özel raporu
att_customReport_byDayDetail=Güne Göre Detay
att_customReport_byPerson=Kişiye Göre Özet
att_customReport_byDept=Dept'e göre özet
att_customReport_queryMaxRange=Sorgu Maksimum Aralığı dört aydır
# 20240630新增国际化
att_personSch_shiftWorkTypeTip1=1. Normal çalışma/dinlenme günleri üzerinde çalıştığımızda, programlama önceliği tatilden daha az.
att_personSch_shiftWorkTypeTip2=2. Çalışma türü tatil boyunca zaman geçirirse, zamanlama önceliği tatilden daha yüksektir.
att_personVerifyMode=Kişisel doğrulama yöntemi
att_personVerifyMode_setting=Kontrol yöntemi ayarları
att_personSch_importCycSch=Kişisel döngü programlamasını indir
att_personSch_cycSchTemplate=Kişisel bisiklet programlama şablonu
att_personSch_exportCycSchTemplate=Kişisel döngü programlama şablonu indir
att_personSch_scheduleTypeNotNull=Shift tipi boş olamaz ya da yok!
att_personSch_shiftNotNull=Vardiya boş olamaz!
att_personSch_shiftNotExist=Vardiya yok!
att_personSch_onlyAllowOneShift=Düzenli planlama sadece bir değişiklik planlanmasına izin verir!
att_shift_attShiftStartDateRemark2=Bu döngün başlangıç tarihinin ilk haftadır; Dönüşün başlangıç tarihi ilk ay.
#打卡状态
att_cardStatus_setting=Çalışma Durumu Ayarları
att_cardStatus_name=Ad
att_cardStatus_value=Değer
att_cardStatus_alias=Farklı Ad
att_cardStatus_every_day=Her Gün
att_cardStatus_by_week=Haftanın Özelinde
att_cardStatus_autoState=Otomatik Durum
att_cardStatus_attState=Çalışma Durumu
att_cardStatus_signIn=Giriş
att_cardStatus_signOut=Çıkış
att_cardStatus_out=dışarı çıkma
att_cardStatus_outReturn=Dışlama Dönüşüm
att_cardStatus_overtime_signIn=Gecelik Girişi
att_cardStatus_overtime_signOut=Gecelik Çıkışı
# 20241030新增国际化
att_leaveType_enableMaxDays=Yıllık Sınırlamayı Etkinleştir
att_leaveType_maxDays=Yıllık Sınır(Gün)
att_leaveType_applyMaxDays=Yıllık Başvuru {0} Günden Fazla Olamaz
att_param_overTimeSetting=Eski Saatleri Ayarlama
att_param_overTimeLevel=Eski Saatleri (Saat)
att_param_overTimeLevelEnable=Eski Saat Düzeylerini Hesaplamak İçin Etkinleştirilsin mi?
att_param_reportColor=Tablo Görünümün Rengini
# APP
att_app_signClientTip=Bu cihaz zaten başkası tarafından bugün kaydedilmiştir
att_app_noSignAddress=İşaretleme alanı ayarlanmadı, lütfen yöneticiyle iletişime geçin ayarlamak için
att_app_notInSignAddress=İşaretleme noktasına ulaşılmadı, işaretleme yapılamaz
att_app_attendance=Çalışma İznim
att_app_apply=İzin Başvurması
att_app_approve=Onaylarımı
# 20250530
att_node_leaderNodeExist=Doğrudan yönetici onayı düğümü zaten mevcuttur
att_signAddress_init=Harita başlatma
att_signAddress_initTips=Lütfen harita anahtarı girin ve adres seçmek için haritayı başlatın