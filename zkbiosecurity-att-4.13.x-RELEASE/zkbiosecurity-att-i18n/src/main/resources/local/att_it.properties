#系统名称 意大利
att_systemName=Attendance System 1.0
#=====================================================================
#左侧菜单
att_module=Presenza
#左侧菜单-考勤设备
att_leftMenu_attendanceDevice=Gestione del dispositivo
att_leftMenu_device=Dispositivo di presenza
att_leftMenu_point=Punto presenze
att_leftMenu_sign_address=Indirizzo sign-in Mobile
att_leftMenu_adms_devCmd=Il server ha emesso un comando
#左侧菜单-基础信息
att_leftMenu_basicInformation=Informazioni Base
att_leftMenu_rule=Regole
att_leftMenu_base_rule=Regole Base
att_leftMenu_department_rule=Regole dipartimento
att_leftMenu_holiday=Vacanze
att_leftMenu_leaveType=Lascia tipo
att_leftMenu_timingCalculation=Tempo di calcolo
att_leftMenu_autoExport=Rapporto automatico
att_leftMenu_param=Impostazione parametri
#左侧菜单-班次管理
att_leftMenu_shiftManagement=Turno
att_leftMenu_timeSlot=Timetable
att_leftMenu_shift=Turno
#左侧菜单-排班管理
att_leftMenu_scheduleManagement=Schedule
att_leftMenu_group=Gruppi 
att_leftMenu_groupPerson=Grouper
att_leftMenu_groupSch=Imposta gruppo
att_leftMenu_deptSch=Imposta dipartimento
att_leftMenu_personSch=Imposta personale
att_leftMenu_tempSch=Imposta temporaneo
att_leftMenu_nonSch=Non programmato
#左侧菜单-异常管理
att_leftMenu_exceptionManagement=Gestione delle eccezioni di presenza
att_leftMenu_sign=Registro allegato
att_leftMenu_leave=Esci
att_leftMenu_trip=Viaggio di lavoro
att_leftMenu_out=Out
att_leftMenu_overtime=Overtime
att_leftMenu_adjust=Modifica e allega
att_leftMenu_class=Modifica Turno
#左侧菜单-统计报表
att_leftMenu_statisticalReport=Report statistiche presenze
att_leftMenu_manualCalculation=Calcolo Manuale
att_leftMenu_transaction=Transazioni
att_leftMenu_dayCardDetailReport=Frequenza giornaliera
att_leftMenu_leaveSummaryReport=Lascia riassunto
att_leftMenu_dayDetailReport=Riassunto gionaliero
att_leftMenu_monthDetailReport=Dettagli riassunto mensile
att_leftMenu_monthStatisticalReport=statistica riassunti mensili(Da persona)
att_leftMenu_deptStatisticalReport=Riassunti dipartimento(Da dipartimento)
att_leftMenu_yearStatisticalReport=Riassunto annuo(Da persona)
att_leftMenu_attSignCallRollReport=Accedi all'appello
att_leftMenu_workTimeReport=Rapporto sul tempo di lavoro
#左侧菜单-设备操作日志
att_leftMenu_device_op_log=Registro operazioni dispositivo
#左侧菜单-实时点名
att_leftMenu_realTime_callRoll=Roll call
#=====================================================================
#公共
att_common_person=Personale
att_common_pin=ID
att_common_group=Grouppo
att_common_dept=Dipartimento
att_common_symbol=Simbolo
att_common_deptNo=Dipartimento number
att_common_deptName=Nome dipartimento
att_common_groupNo=Numero Gruppo
att_common_groupName=Nome gruppo
att_common_operateTime=Tempo operazione
att_common_operationFailed=Operazione fallita
att_common_id=ID
att_common_deptId=ID dipartimento
att_common_groupId=ID gruppo ID
att_common_deviceId=ID dispositivo
att_person_pin=ID personale
att_person_name=Nome
att_person_lastName=Cognome
att_person_internalCard=Numero Carta
att_person_attendanceMode=Modalità tempo e presenza
att_person_normalAttendance=Frequenza normale
att_person_noPunchCard=Nessuna scheda perforata
att_common_attendance=Presenze
att_common_attendance_hour=Presenze (ora)
att_common_attendance_day=Presenze (giorno)
att_common_late=RItardo
att_common_early=Anticipo
att_common_overtime=Overtime
att_common_exception=Eccezione
att_common_absent=Assenza
att_common_leave=Uscita
att_common_trip=Viaggio di lavoro
att_common_out=Out
att_common_staff=Impiegato 
att_common_superadmin=Superutente
att_common_msg=Contenuto SMS
att_common_min=Durata short message (minuti)
att_common_letterNumber=è possibile inserire solo lettere e numeri!
att_common_relationDataCanNotDel=I dati associati non possono essere cancellati.
att_common_relationDataCanNotEdit=I dati associati non possono essere modificati.
att_common_needSelectOneArea=Selezionare un area,Grazie.
att_common_neesSelectPerson=Seleziona la persona, Grazie.
att_common_nameNoSpace=Questo nome non deve contenere spazi.
att_common_digitsValid=Puó solo contenere numero non piú di due decimali.
att_common_numValid=Solo inserire numeri!
#=====================================================================
#工作面板
att_dashboard_worker=Maniaco del lavoro
att_dashboard_today=Presenze oggi
att_dashboard_todayCount=Statistiche segmenti presenze oggi
att_dashboard_exceptionCount=Statistiche anormalie (questo mese)
att_dashboard_lastWeek=Ultima settimana
att_dashboard_lastMonth=Ultimo mese
att_dashboard_perpsonNumber=Totale personale
att_dashboard_actualNumber=Personale attuale
att_dashboard_notArrivedNumber=Personale assente
att_dashboard_attHour=Tempo lavoro
#区域
#设备
att_op_syncDev=Sincronizza dati software del dispositivo
att_op_account=Controllo dati presenze
att_op_check=Caricare dati nuovamente
att_op_deleteCmd=Cancellare comandi dispositivo
att_op_dataSms=Messaggio pubblico 
att_op_clearAttPic=Cancellare foto presenze
att_op_clearAttLog=Cancella presenze transazione
att_device_waitCmdCount=Comando da eseguire
att_device_status=Abilita stato
att_device_register=Registrazione macchina
att_device_isRegister=Registramento dispositivo
att_device_existNotRegDevice=Equipaggiamento macchina non registrato, dati non ottenibili!
att_device_fwVersion=Versione Firmware
att_device_transInterval=Durata aggiornamento(min)
att_device_cmdCount=Numero massimo di comandi a comunicare col server.
att_device_delay=Tempo di registrazione richiesta (secondi)
att_device_timeZone=Fuso orario
att_device_operationLog=Registro operazioni
att_device_registeredFingerprint=Registrare impronta
att_device_registeredUser=Registrare personale
att_device_fingerprintImage=Immagine impronta
att_device_editUser=Modifica personale
att_device_modifyFingerprint=Modifica impronta
att_device_faceRegistration=Registra Volto
att_device_userPhotos=Foto personale
att_device_attLog=Caricare resgistri di presenza
att_device_operLog=Caricare informazioni personale
att_device_attPhoto=Caricare foto presenze
att_device_isOnLine=Stato Online
att_device_InputPin=Immettere numero persona
att_device_getPin=Ottenere specifiche dati personali
att_device_separatedPin=Numeri personale mutipli, separatti da virgola
att_device_authDevice=Dispositivo autorizzato
att_device_disabled=I seguenti dispositivi sono disabilitati, non possono essere utilizzati.
att_device_autoAdd=La nuova attrezzatura viene aggiunta automaticamente
att_device_receivePersonOnlyDb=Ricevere solo i dati del personale presente nel database
att_devMenu_control=Controllo dispositivo
att_devMenu_viewOrGetInfo=Visualizza e gurarda info
att_devMenu_clearData=Cancelllare dati dispositivo
att_device_disabledOrOffline=Il dispositivo non è autorizzato o non è online,non può essere utilizzato.
att_device_areaStatus=Stato dell'area del dispositivo
att_device_areaCommon=La regione è normale
att_device_areaEmpty=L'area è vuota
att_device_isRegDev=La modifica del fuso orario o dello stato del registrar richiede il riavvio del dispositivo per avere effetto!
att_device_canUpgrade=È possibile aggiornare i seguenti dispositivi
att_device_offline=I seguenti dispositivi sono offline e non possono essere utilizzati!
att_device_oldProtocol=Vecchio protocollo
att_device_newProtocol=Nuovo protocollo
att_device_noMoreTwenty=Il pacchetto di aggiornamento del firmware del dispositivo vecchio protocollo non può superare i 20M
att_device_transferFilesTip=Firmware rilevato correttamente, trasferire file
att_op_clearAttPers=Chiaro personale dell'attrezzatura
#区域人员
att_op_forZoneAddPers=Area impostazioni personale
att_op_dataUserSms=Messaggio privato
att_op_syncPers=Re-sincornizzato al dispositivo
att_areaPerson_choiceArea=Selezionare area, Grazie!
att_areaPerson_byAreaPerson=Per regione
att_areaPerson_setByAreaPerson=Impostato dal personale di area
att_areaPerson_importBatchDel=Importa eliminazione in blocco
att_areaPerson_syncToDevSuccess=Operazione riuscita! aspetare che il comando sia inviato, Grazie.
att_areaPerson_personId=ID personale
att_areaPerson_areaId=Area ID
att_area_existPerson=Ci sono persone in questa area!
att_areaPerson_notice1=Area e personale non possono essere vuoti allo stesso tempo!
att_areaPerson_notice2=Nessuna persona o area è stata richiesta!
att_areaPerson_notice3=Nessun dispositivo trovate in area!
att_areaPerson_addArea=Add area
att_areaPerson_delArea=Cancella area
att_areaPerson_persNoExit=La persona non esiste
att_areaPerson_importTip1=Assicurati che la persona importata esista già nel modulo del personale
att_areaPerson_importTip2=Gli importatori batch non verranno consegnati automaticamente al dispositivo e dovranno essere sincronizzati manualmente
att_areaPerson_addAreaPerson=Aggiungi personale regionale
att_areaPerson_delAreaPerson=Elimina personale di area
att_areaPerson_importDelAreaPerson=Importa ed elimina personale di area
att_areaPerson_importAreaPerson=Personale dell'area di importazione
#考勤点
att_attPoint_name=Nome punto presenza
att_attPoint_list=Lista punti presenza
att_attPoint_deviceModule=Modulo dispositivo
att_attPoint_acc=Controllo accessi
att_attPoint_park=Parcheggio
att_attPoint_ins=FaceKiosk
att_attPoint_pid=Certificato personale
att_attPoint_vms=Video
att_attPoint_psg=Corridoio
att_attPoint_doorList=Lista porte
att_attPoint_deviceList=Lista dipositivi
att_attPoint_channelList=Elenco dei canali
att_attPoint_gateList=Elenco dei cancelli
att_attPoint_recordTypeList=Tirare il tipo di record
att_attPoint_door=Selezionare la porta corrispondente, Grazie.
att_attPoint_device=Selezionare il dispositivo corrispondente, Grazie.
att_attPoint_gate=Si prega di selezionare il cancello corrispondente
att_attPoint_normalPassRecord=Record di passaggio normale
att_attPoint_verificationRecord=Registro di verifica
att_person_attSet=Impostazioni presenze
att_attPoint_point=Selezionare il punto di presenza, Grazie.
att_attPoint_count=Insufficiente punto di licenza di presenza autorizzato ; operazione fallita!
att_attPoint_notSelect=Il modulo non è configurato
att_attPoint_accInsufficientPoints=Inadeguati punti presenza per registrazione controllo accessi!
att_attPoint_parkInsufficientPoints=Inadeguati punti presenza per registrazione parco auto!
att_attPoint_insInsufficientPoints=I punti licenza presenze di faceKiosk sono insufficienti！
att_attPoint_pidInsufficientPoints=Il certificato personale non è sufficinete alle presenze!
att_attPoint_doorOrParkDeviceName=Nome porta o Nome dispositivo parcheggio
att_attPoint_vmsInsufficientPoints=Video quando i punti presenza sono insufficienti!
att_attPoint_psgInsufficientPoints=Il canale ha punti di partecipazione insufficienti!
att_attPoint_delDevFail=Fallita cancellazione dispositivo, il dispositivo è stato utilizzato come presenza!
att_attPoint_pullingRecord=Il punto di presenza sta ottenendo regolarmente record, per favore aspetta!
att_attPoint_lastTransactionTime=Ultimo tempo di estrazione dei dati
att_attPoint_masterDevice=Dispositivo principale
att_attPoint_channelName=Nome canale
att_attPoint_cameraName=Nome della telecamera
att_attPoint_cameraIP=ip telecamera
att_attPoint_channelIP=ip canale
att_attPoint_gateNumber=Numero del cancello
att_attPoint_gateName=Nome del cancello
#APP考勤签到地址
att_signAddress_address=Indirizzo
att_signAddress_longitude=Longitudine
att_signAddress_latitude=Latitudine
att_signAddress_range=Rango effettivo
att_signAddress_rangeUnit=Lunità di misura è in metri(m)
#=====================================================================
#规则
att_rule_baseRuleSet=Impostazioni regole base
att_rule_countConvertSet=Impostazioni di calcolo
att_rule_otherSet=Altre Impostazioni
att_rule_baseRuleSignIn=Regole Check-In
att_rule_baseRuleSignOut=Regole Check-Out 
att_rule_earliestPrinciple=Prima regola
att_rule_theLatestPrinciple=Ultima regola
att_rule_principleOfProximity=Regole di prossimità
att_rule_baseRuleShortestMinutes=Il tempo minimo dovrebbe essere maggiore di (minimo 10 minuti)
att_rule_baseRuleLongestMinutes=Il tempo massimo dovrebbe essere minore di (max 1,440 minuti)
att_rule_baseRuleLateAndEarly=Late and Early Leave Counted as Absent
att_rule_baseRuleCountOvertime=Statistiche straordinario
att_rule_baseRuleFindSchSort=Ricerca registro turni
att_rule_groupGreaterThanDepartment=Gruppi->Dipartimento
att_rule_departmentGreaterThanGroup=Dipartimento->Gruppi
att_rule_baseRuleSmartFindClass=Regole intelligenti adattamento turni
att_rule_timeLongest=Durata lavorativa più lunga
att_rule_exceptionLeast=Meno anormale
att_rule_baseRuleCrossDay=Risultato calcolo delle presenze su turni di più giornate
att_rule_firstDay=Primo giorno
att_rule_secondDay=Secondo giorno
att_rule_baseRuleShortestOvertimeMinutes=Straodrinari brevi (minuti)
att_rule_baseRuleMaxOvertimeMinutes=Straordinari massime (minuti)
att_rule_baseRuleElasticCal=Calcolo felssibile della durata
att_rule_baseRuleTwoPunch=Tempo accumulato per ogni due timbrature
att_rule_baseRuleStartEnd=Calcolo di testa e coda tempo timbratura
att_rule_countConvertHour=Regole conversione orario
att_rule_formulaHour=Formula: Ore = Minuti / 60
att_rule_countConvertDay=Regole conversione giorni
att_rule_formulaDay=Formula：Days = Minuti / Numero di minuti per giornata lavorativa
att_rule_inFormulaShallPrevail=Prendi i risultati calcolati dalla formula come standard;
att_rule_remainderHour=Il reminder è maggiore o uguale a
att_rule_oneHour=Registrato come un ora;
att_rule_halfAnHour=Calcolato come mezza ora, altrimenti ignorato;
att_rule_remainderDay=Il quoziente è maggiore o uguale ai minuti lavorati
att_rule_oneDay=%,calcolato come un giorno;
att_rule_halfAnDay=%,calcolato come mezza giornata, altrimenti ignorato;
att_rule_countConvertAbsentDay=Analisi giorno di conversione
att_rule_markWorkingDays=Calcolati come giorni lavorativi
att_rule_countConvertDecimal=Cifra esatta del punto decimale
att_rule_otherSymbol=L'impostazione del simbolo del risultato di presenza nel rapporto
att_rule_arrive=Previsto/Attuale
att_rule_noSignIn=No Check-In
att_rule_noSignOff=No Check-Out
att_rule_off=Regola reset
att_rule_class=Aggiungi presenze
att_rule_shortLessLong=L'orario delle presenze non può essere più lungo della durata dell'orario delle presenze più lunghe.
att_rule_symbolsWarning=è necessario configurare il simbolo nel rapporto delle presenze!
att_rule_reportSettingSet=Report impostazione di esportazione
att_rule_shortDateFormat=Formato data
att_rule_shortTimeFormat=Formato tempo
att_rule_baseRuleSignBreakTime=Timbrare durante la pausa
att_leftMenu_custom_rule=Personalizza regole
att_custom_rule_already_exist={0} Esistono già regole personalizzate!
att_add_group_custom_rule=Add raggruppamento regole
att_custom_rule_type=Tipe regole
att_rule_type_group=Raggruppamento regole
att_rule_type_dept=Regole di dipartimento
att_custom_rule_orgNames=Usando l'oggetto
att_rult_maxOverTimeType1=No limiti
att_rult_maxOverTimeType2=Questo weekend
att_rult_maxOverTimeType3=Questo mese
att_rule_countConvertDayRemark1=Esempio:L'orario di lavoro effettivo è di 500 minuti, e l'orario di lavoro è di 480 minuti al giorno. Il risultato è 500/480=1.25, e l'ultimo decimale rimane 1.2
att_rule_countConvertDayRemark2=Esempio:L'orario di lavoro effettivo è di 500 minuti, e l'orario di lavoro è di 480 minuti al giorno. Il risultato è 500/480=1.25, 1.25>0.8.
att_rule_countConvertDayRemark3=Esempio:L'orario di lavoro effettivo è di 300 minuti, e l'orario di lavoro è di 480 minuti al giorno. Il risultato è 300/480=0.625, 0.2<0.625<0.8 for half a day.
att_rule_countConvertDayRemark4=Benchmark conversione giornaliera: Registrare come giorni lavorativi nel periodo di tempo non funziona;
att_rule_countConvertDayRemark5=è registrato come numero di giorni lavorativi: è limitato al calcolo del numero di giorni di completamento, e fino a quando c'è il completamento in ogni periodo, la durata del completamento è calcolata in base al numero di giorni lavorativi del periodo;
att_rule_baseRuleSmartFindRemark1=Il tempo prolungato:In base alla carta del giorno, calcola l'orario di lavoro corrispondente ad ogni turno giornata, e trova il turno della giornata più lunga di lavoro;
att_rule_baseRuleSmartFindRemark2=Minimo anormale: Calcolate il numero di tempi anormali rapporato ad ogni turno del giorno accordato con il punto carta del giorno,e trova il turnocon l'ultimo numero di anomalie del giorno per calcorare l'orario di lavoro;
att_rule_baseRuleHourValidator=Il minuto di giudizio su mezz'ora non può essere maggiore o uguale al periodo di giudizio di un giorno!
att_rule_baseRuleDayValidator=Il periodo di giudizio di mezza giornata non può essere maggiore o uguale al periodo di giudizio di un giorno!
att_rule_overtimeWarning=La durata massima degli straordinari non può essere inferiore alla durata minima singola degli straordinari!
att_rule_noSignInCountType=Conta Ceck-Out mancante con pochi minuti di ritardo
att_rule_absent=Assente
att_rule_earlyLeave=Uscita anticipata
att_rule_noSignOffCountType=Conta ceck-out mancante
att_rule_minutes=Minuti
att_rule_noSignInCountLateMinute=Conta Ceck-In mancante con pochi minuti di ritardo
att_rule_noSignOffCountEarlyMinute=Conta Check-Out mancante con pochi minuti di uscita anticipata
att_rule_incomplete=Incompleto
att_rule_noCheckInIncomplete=Incompleto e No Check-in
att_rule_noCheckOutIncomplete=Incompleto e No Check-out
att_rule_lateMinuteWarning=Il conteggio mancante del check-in in quanto i minuti in ritardo dovrebbero essere maggiori di 0 e inferiori al periodo di presenza più lungo
att_rule_earlyMinuteWarning=Il conteggio dei check-out mancanti in quanto i primi minuti dovrebbero essere maggiori di 0 e inferiori al periodo di presenza più lungo
att_rule_baseRuleNoSignInCountLateMinuteRemark=Quando il check-in non viene effettuato come in ritardo, se non il check-in viene considerato in ritardo per N minuti
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=Quando la mancata firma viene registrata come partenza anticipata, se non la firma viene registrata come partenza anticipata per N minuti
#节假日
att_holiday_placeholderNo=Si raccomanda di partire con H, tale a H01.
att_holiday_placeholderName=si consiglia di nominare con [Anno]+[Nome vacanze] eg. [2017 Giorno lavortivo].
att_holiday_dayNumber=Numero di gionri
att_holiday_validDate_msg=Vacanze in questo periodo
#假种
att_leaveType_leaveThing=Congedo occasionale
att_leaveType_leaveMarriage=Congedo matrimoniale
att_leaveType_leaveBirth=Congedo maternità
att_leaveType_leaveSick=Congedo malattia
att_leaveType_leaveAnnual=Ferie annuali
att_leaveType_leaveFuneral=Congedo per lutto
att_leaveType_leaveHome=Congedo a casa
att_leaveType_leaveNursing=Congedo per allattamento
att_leaveType_isDeductWorkLong=Se rimuovere ore di lavoro
att_leaveType_placeholderNo=It è raccomandato cominciare con L, tale a L1.
att_leaveType_placeholderName=è consigliato terminare con le vacanze,eg.Vacanze per matrimonio.
#定时计算
att_timingcalc_timeCalcFrequency=Calcolo intervalli
att_timingcalc_timeCalcInterval=Tempo di calcolo del tempo
att_timingcalc_timeSet=Impostazioni tempo del calcolo tempo
att_timingcalc_timeSelect=Selezionare tempo, prego
att_timingcalc_optionTip=è necessario conservare almeno un calcolo valido giornaliero programmato, delle presenze
#自动导出报表
att_autoExport_reportType=Tipo di rapporto
att_autoExport_fileType=Tipo di File
att_autoExport_fileName=Nome file
att_autoExport_fileDateFormat=Formato data
att_autoExport_fileTimeFormat=Formato tempo
att_autoExport_fileContentFormat=Formato contenuti
att_autoExport_fileContentFormatTxt=Esempio：{deptName}00{personPin}01{personName}02{attDatetime}03
att_autoExport_timeSendFrequency=Invia frequenza
att_autoExport_timeSendInterval=Intervallo di invio
att_autoExport_emailType=Tipo di destinatario mail
att_autoExport_emailRecipients=Destinatario mail
att_autoExport_emailAddress=Indirizzo mail
att_autoExport_emailExample=Esempio:<EMAIL>,<EMAIL>
att_autoExport_emailSubject=Titolo mail
att_autoExport_emailContent=Corpo mail
att_autoExport_field=Campo
att_autoExport_fieldName=Nome Campo
att_autoExport_fieldCode=Numero Campo
att_autoExport_reportSet=Impostazioni report
att_autoExport_timeSet=Impostazioni tempo di invio mail
att_autoExport_emailSet=Impostazioni Mail
att_autoExport_emailSetAlert=Prego, inserire la sua mail.
att_autoExport_emailTypeSet=Impostazioni destinatario
att_autoExport_byDay=Per giono
att_autoExport_byMonth=Per mese
att_autoExport_byPersonSet=Impostato da personale
att_autoExport_byDeptSet=Impostato da dipartimento
att_autoExport_byAreaSet=Impostato da Area
att_autoExport_emailSubjectSet=Impostazioni titolo
att_autoExport_emailContentSet=Impostazioni corpo
att_autoExport_timePointAlert=Seleziona il punto temporale di invio corretto.
att_autoExport_lastDayofMonth=Ultimo giorno del mese
att_autoExport_firstDayofMonth=Primo giorno del mese
att_autoExport_dayofMonthCheck=Data specifica
att_autoExport_dayofMonthCheckAlert=Selezionare data specifica,Grazie.
att_autoExport_chooseDeptAlert=Prego selezionare il dipartimento!
att_autoExport_sendFormatSet=Impostazione modalità invio
att_autoExport_sendFormat=Modo invio
att_autoExport_mailFormat=Metodo di consegna cassetta postale
att_autoExport_ftpFormat=Metodo invio FTP
att_autoExport_sftpFormat=Metodo invio SFTP
att_autoExport_ftpUrl=Indirizzo Server FTP
att_autoExport_ftpPort=Porta server FTP
att_autoExport_ftpTimeSet=Impostazioni tempo di invio FTP
att_autoExport_ftpParamSet=Impostazioni parametri FTP
att_autoExport_ftpUsername=FTP Username
att_autoExport_ftpPassword=FTP Password
att_autoExport_correctFtpParam=Si prega di compilare correttamente i parametri FTP
att_autoExport_correctFtpTestParam=Verificare la connessione per assicurarsi che la comunicazione sia normale
att_autoExport_inputFtpUrl=Prego inserire indirizzo
att_autoExport_inputFtpPort=Si prega di compilare correttamente i parametri
att_autoExport_ftpSuccess=Connessione stabilita
att_autoExport_ftpFail=Verificare che le impostazioni del parametro non siano corrette.
att_autoExport_validFtp=Prego inserire indirizzo valido server
att_autoExport_validPort=Prego inserire porta valida del server
att_autoExport_selectExcelTip=File type select EXCEL, the content format is all fields!
#=====================================================================
#时间段
att_timeSlot_periodType=Tipo orario	
att_timeSlot_normalTime=Orario normale
att_timeSlot_elasticTime=Orario flessibile
att_timeSlot_startSignInTime=Check-In inizio ora
att_timeSlot_toWorkTime=Check-In Ora
att_timeSlot_endSignInTime=Check-In Fine ora
att_timeSlot_allowLateMinutes=Ritardo consentito(minuti)
att_timeSlot_isMustSignIn=Effettuare Check-In
att_timeSlot_startSignOffTime=Check-Out Ore di inizio
att_timeSlot_offWorkTime=Check-Out Ora
att_timeSlot_endSignOffTime=Check-Out Fine ora
att_timeSlot_allowEarlyMinutes=Autorizza uscita anticipata(minuti)
att_timeSlot_isMustSignOff=Effettuare Check-Out
att_timeSlot_workingHours=Ore lavorate (minuti)
att_timeSlot_isSegmentDeduction=Auto rilevatore pausa
att_timeSlot_startSegmentTime=Ora di inizio
att_timeSlot_endSegmentTime=Fine ora
att_timeSlot_interSegmentDeduction=Tempo dedotto(minutei)
att_timeSlot_markWorkingDays=Giorno lavorativo
att_timeSlot_isAdvanceCountOvertime=Auto OT(Check-In anticipo)
att_timeSlot_signInAdvanceTime=Auto OT Fine ora(Check-In)
att_timeSlot_isPostponeCountOvertime=Auto OT(Check-Out Ritardo)
att_timeSlot_signOutPosponeTime=Auto OT Ora di inizio(Check-Out)
att_timeSlot_isCountOvertime=Calcolato come straordinario
att_timeSlot_timeSlotLong=L'orario di lavoro deve rispettare l'intervallo di frequenza definito dalle regole:
att_timeSlot_alertStartSignInTime=L'ora di inizio del check-in deve essere inferiore all'ora del check-in.
att_timeSlot_alertEndSignInTime=L'ora di fine del check-in deve essere maggiore del tempo del check-in.
att_timeSlot_alertStartSignInAndEndSignIn=L'ora di inizio del check-out deve essere inferiore all'ora di check-out.
att_timeSlot_alertStartSignOffTime=L'ora di inizio degli straordinari non può essere inferiore all'ora di check-out.
att_timeSlot_alertEndSignOffTime=L'ora di inizio degli straordinari non può essere maggiore dell'orario di fine del check-out.
att_timeSlot_alertStartUnequalEnd=I giorni lavorativi non possono essere inferiori a 0.
att_timeSlot_alertStartSegmentTime=Il tempo dedotto non può essere inferiore a 0.
att_timeSlot_alertStartAndEndTime=L'ora di inizio del checkout non può eguagliare il tempo di check-in finale.
att_timeSlot_alertEndAndoffWorkTime=Le ore non possono essere maggiori di 23.
att_timeSlot_alertToWorkLesserAndEndTimeLesser=I minuti non possono essere maggiori di 59.
att_timeSlot_alertLessSignInAdvanceTime=L'orario di check-in anticipato deve essere inferiore all'ora di inizio del lavoro
att_timeSlot_alertMoreSignInAdvanceTime=Il numero di minuti di straordinario prima di "andare al lavoro" è inferiore al numero di minuti prima di "lavorare"
att_timeSlot_alertMoreSignOutPosponeTime=i minuti di lavoro straordinario post lavoro "fuori lavoro" sono inferiori ai minuti "dopo lavoro"
att_timeSlot_alertLessSignOutPosponeTime=Il check-out posticipato dovrebbe essere superiore all'orario di fine lavoro
att_timeSlot_time=Prego inserire il formato corretto orario
att_timeSlot_alertMarkWorkingDays=La giornata lavorativa non puo' essere vuota!
att_timeSlot_placeholderNo=Si raccomanda di cominciare con T, come T01.
att_timeSlot_placeholderName=Si raccomanda di cominciare con T o terminare con orario.
att_timeSlot_beforeToWork=Prima di andare a lavoro
att_timeSlot_afterToWork=Dopo lavoro
att_timeSlot_beforeOffWork=Prima di andare fuori servizio
att_timeSlot_afterOffWork=Dopo lavoro
att_timeSlot_minutesSignInValid=Il check-in sarà valido entro pochi minuti
att_timeSlot_toWork=In servizio
att_timeSlot_offWork=Fuori servizio
att_timeSlot_minutesSignInAsOvertime=Entrato minuti fa per straordinari
att_timeSlot_minutesSignOutAsOvertime=Inizia a contare gli straordinari minuti
att_timeSlot_minOvertimeMinutes=Minimo minuti di straordinario
att_timeSlot_enableWorkingHours=Abilitare orario di lavoro
att_timeSlot_eidtTimeSlot=Modifica orario
att_timeSlot_browseBreakTime=Sfoglia i periodi di riposo
att_timeSlot_addBreakTime=Add pause
att_timeSlot_enableFlexibleWork=Attiva lavoro flessibile
att_timeSlot_advanceWorkMinutes=Può andare a lavorare in anticipo
att_timeSlot_delayedWorkMinutes=Può rinviare il lavoro
att_timeSlot_advanceWorkMinutesValidMsg1=Il numero di minuti prima di andare al lavoro è maggiore del numero di minuti che possono lavorare in anticipo
att_timeSlot_advanceWorkMinutesValidMsg2=Il numero di minuti che possono lavorare in anticipo è inferiore al numero di minuti prima di andare al lavoro
att_timeSlot_advanceWorkMinutesValidMsg3=Il numero di minuti che è possibile lavorare in anticipo è inferiore o uguale al numero di minuti prima di accedere per fare gli straordinari.
att_timeSlot_advanceWorkMinutesValidMsg4=Il numero di minuti che possono essere prima di accedere al lavoro straordinario è maggiore o uguale al numero di minuti lavorati in anticipo.
att_timeSlot_delayedWorkMinutesValidMsg1=Il numero di minuti dopo il lavoro è maggiore del numero di minuti che possono essere rimandati al lavoro
att_timeSlot_delayedWorkMinutesValidMsg2=Il numero di minuti di un possibile ritardo è inferiore al numero di minuti dopo il lavoro
att_timeSlot_delayedWorkMinutesValidMsg3=Il numero di minuti che possono essere programmati per lavorare è inferiore o uguale al numero di miniuti dopo il lavoro, la disconnessione e l'inizio degli straordinari
att_timeSlot_delayedWorkMinutesValidMsg4=Il numero di minuti che possono essere dopo il lavoro, la disconnessione e l'inizio del lavoro straordinario è maggiore o uguale al numero di minuti dopo il lavoro programmato
att_timeSlot_allowLateMinutesValidMsg1=Il numero di minuti di ritardo è inferiore al numero di minuti dopo il lavoro
att_timeSlot_allowLateMinutesValidMsg2=Il numero di minuti dopo il lavoro è maggiore del numero di minuti consentiti per il ritardo
att_timeSlot_allowEarlyMinutesValidMsg1=Autorizzare minuti anticipati è inferiore ai minuti prima del lavoro
att_timeSlot_allowEarlyMinutesValidMsg2=Il numero di minuti prima del lavoro è maggiore del numero di minuti consentiti rimasti in anticipo
att_timeSlot_timeOverlap={0} sovrapposizione con {1} tempo, prego modificare il peridio selezionato!
att_timeSlot_atLeastOne=Almeno 1 periodo di riposo!
att_timeSlot_mostThree=Fino a 3 periodi di riposo!
att_timeSlot_canNotEqual=L'ora di inizio del periodo di riposo non può essere uguale all'ora di fine!
att_timeSlot_shoudInWorkTime=Assicurati che il periodo di riposo sia entro l'orario di lavoro!
att_timeSlot_repeatBreakTime=Ripeti il periodo di riposo!
att_timeSlot_toWorkLe=L'orario di lavoro è inferiore all'orario minimo di inizio del periodo di riposo selezionato:
att_timeSlot_offWorkGe=Le ore di spegnimento sono maggiori del tempo massimo di fine del periodo di riposo selezionato:
att_timeSlot_crossDays_toWork=Il tempo minimo di inizio per il periodo di pausa è compreso nel periodo di tempo:
att_timeSlot_crossDays_offWork=Il tempo massimo di fine del periodo di riposo è compreso nel periodo di tempo:
att_timeSlot_allowLateMinutesRemark=Dall'orario di lavoro alla tessera dei minuti consentiti per calcolare la normale carta di lavoro
att_timeSlot_allowEarlyMinutesRemark=A partire presto dal tempo di fuori servizio nel numero di minuti consentiti per uscita anticipata, la normale carta fuori servizio
att_timeSlot_isSegmentDeductionRemark=Eliminazione del periodo di riposo nel periodo di tempo
att_timeSlot_attEnableFlexibleWorkRemark1=Il lavoro flessibile non è autorizzato a stabilire il numero di partenze anticipate
att_timeSlot_afterToWorkRemark=Dopo i minuti di lavoro è uguale al distinguersi ai minuti di lavoro
att_timeSlot_beforeOffWorkRemark=Prima che i minuti di lavoro uguali possano andare a minuti di lavoro
att_timeSlot_attEnableFlexibleWorkRemark2=Il numero di minuti dopo il lavoro è maggiore o uguale alle ore di inattività + ore di lavoro in ritardo
att_timeSlot_attEnableFlexibleWorkRemark3=È possibile lavorare in anticipo, deve essere inferiore o uguale a N minuti di lavoro per eseguire i minuti di lavoro straordinario
att_timeSlot_attEnableFlexibleWorkRemark4=Rinviato ai minuti di lavoro, deve essere inferiore o uguale a N minuti di lavoro
att_timeSlot_attBeforeToWorkAsOvertimeRemark=Esempio:9:00 classe,accedere per fare gli straordinari 60 minuti prima del lavoro, quindi fare il check-in prima delle 8 alle 8 in punto per gli straordinari
att_timeSlot_attAfterOffWorkAsOvertimeRemark=Esempio:Dopo le 18 in punto di lavoro, dopo 60 minuti di lavoro, firmare il ritiro e fare gli straordinari, quindi iniziare gli straordinari dalle 19 in poi all'ora del check-out.
att_timeSlot_longTimeValidRemark=Il tempo di marcatura effettiva dopo il lavoro e il tempo di marcatura effettiva di prima del lavoro non possono sovrapporsi nel periodo di tempo!
att_timeSlot_advanceWorkMinutesValidMsg5=Il numero di minuti validi prima del check-in è maggiore del numero di minuti che è possibile lavorare in anticipo
att_timeSlot_advanceWorkMinutesValidMsg6=I minuti per lavorare in anticipo devono essere meno dei minuti validi per accedere prima del lavoro
att_timeSlot_delayedWorkMinutesValidMsg5=Il numero di minuti validi dopo il check-in è maggiore del numero di minuti che possono essere posticipati per lavorare
att_timeSlot_delayedWorkMinutesValidMsg6=Il numero di minuti che possono essere posticipati per lavorare dovrebbe essere inferiore ai minuti validi dopo l'accesso
att_timeSlot_advanceWorkMinutesValidMsg7=L'orario di check-in prima del lavoro non può sovrapporsi all'orario di check-out dopo essere uscito dal lavoro il giorno prima
att_timeSlot_delayedWorkMinutesValidMsg7=L'orario di check-out dopo il lavoro non può sovrapporsi all'orario di check-in prima del lavoro il giorno successivo
att_timeSlot_maxOvertimeMinutes=Limita il numero massimo di ore di straordinario
#班次
att_shift_basicSet=Tipo di programma
att_shift_advancedSet=Nome programma
att_shift_type=Tipo di turno
att_shift_name=Nome turno
att_shift_regularShift=Turno regolare
att_shift_flexibleShift=Turno flessibile
att_shift_color=Colore
att_shift_periodicUnit=Unità
att_shift_periodNumber=Ciclo
att_shift_startDate=Data di inizio
att_shift_startDate_firstDay=Data di inizio ciclo
att_shift_isShiftWithinMonth=Turno ciclo in un mese
att_shift_attendanceMode=Modlità presenze
att_shift_shiftNormal=Timbra carta accordato col turno normale
att_shift_oneDayOneCard=Timbra una volta in qualsiasi momento del giorno
att_shift_onlyBrushTime=Calcola solo il tempo di timbro della carta
att_shift_notBrushCard=Tibratura libera
att_shift_overtimeMode=Modalità straordinari
att_shift_autoCalc=Calcolo automatico computer
att_shift_mustApply=Gli straordinari devono essere applicati
att_shift_mustOvertime=Deve fare straordinari o prendere assenza
att_shift_timeSmaller=Durata più breve tra il calcolo automatico e la ricevuta degli straordinari
att_shift_notOvertime=Non calcolato come straordinario
att_shift_overtimeSign=Tipo di straordinario
att_shift_normal=Giorno normale
att_shift_restday=Giorno di riposo
att_shift_timeSlotDetail=Dettagli orari
att_shift_doubleDeleteTimeSlot=Doppio-click per periodo di turno; puoi cancellare il periodo di tempo
att_shift_addTimeSlot=Add Orario
att_shift_cleanTimeSlot=Cancella orario
att_shift_NO=No.
att_shift_notAll=Deselezionare TUTTO
att_shift_notTime=Se la casella di controllo dei dettagli dell'orario non può essere selezionata, indica che vi è una sovrapposizione nell'orario.
att_shift_notExistTime=Questi orari non esistono.
att_shift_cleanAllTimeSlot=Sei sicuro di voler cancellare l'orario per il turno selezionato?
att_shift_pleaseCheckBox=Prego selezionare la casella di controllo sul lato sinistro che corrisponde al tempo di visualizzazione corrente sul lato destro.
att_shift_pleaseUnit=Si prega di compilare le unità del ciclo e il numero di cicli.
att_shift_pleaseAllDetailTimeSlot=Prego selezionare dettagli orari.
att_shift_placeholderNo=Si consiglia di usare S, come S0.
att_shift_placeholderName=Si consiglia di cominciare con S o finire col turno.
att_shift_workType=Tipo lavoro
att_shift_normalWork=Lavoro normale
att_shift_holidayOt=Vacanze OT
att_shift_attShiftStartDateRemark=Example: La giornata iniziale del ciclo No. 22, con un periodo di 3 giorni, poi No. 22/23/24 è in Classe A/B/C, e No. 19/20/21 è in Classe A. /B classe / C classe, prima e dopo la data e proseguire.
att_shift_isShiftWithinMonthRemark1=Spostato all'interno del mese, il ciclo scorre solo fino all'ultimo giorno di ogni mese, non programmato consecutivamente durante il mese;
att_shift_isShiftWithinMonthRemark2=Non-month shSpostamento non mensile, il ciclo viene sequenziato fino all'ultimo giorno di ogni mese, se un ciclo non è terminato, continua con il mese successivo e così via;
att_shift_workTypeRemark1=Lavoro normale,straordinario marcato, ad eccezione delle festività, la frequenza non viene conteggiata per le ferie. Se gli straordinari sono contrassegnati come festivi, le ore di straordinario sono registrate come festività (escluse le normali ore di lavoro);
att_shift_workTypeRemark2=Weekend OT,la timbratura degli straordinari viene impostata automaticamente sul giorno di riposo e il computer calcola automaticamente gli straordinari. Non è richiesta un'applicazione per tali. Le ore lavorative del giorno sono registrate come ore di straordinario e la frequenza non viene conteggiata durante le vacanze.
att_shift_workTypeRemark3=Holiday OT,la timbratura degli straordinari è impostata automaticamente sui giorni festivi e il computer calcola automaticamente gli straordinari, non è necessaria l'applicazione di straordinari e le ore lavorative del giorno vengono registrate come ore di straordinario;
att_shift_attendanceModeRemark1=Tranne che per lo scorrimento normale per turno, non è considerato un lavoro straordinario anticipato o ritardato, ad esempio:
att_shift_attendanceModeRemark2=1. Non è richiesto il check-in o viene utilizzata una carta valida una volta al giorno, non vengono conteggiati gli straordinari;
att_shift_attendanceModeRemark3=2.Work type:lavoro normale, modalità di presenza: scorrimento della carta libero, quindi l'orario del turno di giorno è considerato orario di lavoro effettivo;
att_shift_periodStartMode=Tipo di inizio periodo
att_shift_periodStartModeByPeriod=Data inizio periodo
att_shift_periodStartModeBySch=Data di inizio del turno
att_shift_addTimeSlotToShift=Se aggiungere la fascia oraria di questo turno
#=====================================================================
#分组
att_group_editGroup=Modifica gruppo personale
att_group_browseGroupPerson=Sfoglia il gruppo personale
att_group_list=Lista gruppo
att_group_placeholderNo=Si consiglia di cominciare con G, come G1
att_group_placeholderName=Si consiglia di cominciare con G o finire col gruppo.
att_widget_deptHint=Note: Importare tutto il personale nel dipartimento selezionato
att_widget_searchType=Richiesta condizionale
att_widget_noPerson=Nessuna scelta
#分组排班
#部门排班
att_deptSch_existsDept=C'è un turno dipartimentale nel dipartimento e non è consentito eliminare il dipartimento.
#人员排班
att_personSch_view=Visualizza programmazione del personale
#临时排班
att_schedule_type=Tipo di programma
att_schedule_tempType=Tipo temporaneo
att_schedule_normal=Programmazione normale
att_schedule_intelligent=Programmazione Smart
att_tempSch_scheduleType=Tipo di programmazione
att_tempSch_startDate=Data di inizio
att_tempSch_endDate=Data di fine
att_tempSch_attendanceMode=Modalità presenze
att_tempSch_overtimeMode=Modalità straordinari
att_tempSch_overtimeRemark=Marcatura straordinari
att_tempSch_existsDept=C'è un turno temporaneo dipartimentale nel dipartimento e non è consentito eliminare il dipartimento.
att_schedult_opAddTempSch=Nuovo turno temporaneo
att_schedule_cleanEndDate=Ora di fine vuota
att_schedule_selectOne=La programmazione normale può solo selezionare un turno!
att_schedule_selectPerson=Selezionare Personale come prima cosa, Grazie!
att_schedule_selectDept=Selezionare dipartimento come prima cosa, Grazie!
att_schedule_selectGroup=Selezionare gruppo come prima cosa!
att_schedule_selectOneGroup=Solo un gruppo puo' essere selezionato!
att_schedule_arrange=Prego scegliere un turno!
att_schedule_leave=Partenza
att_schedule_trip=Viaggio
att_schedule_out=Uscita
att_schedule_off=Riposo
att_schedule_makeUpClass=Aggiungi
att_schedule_class=Modifica
att_schedule_holiday=Vacanze
att_schedule_offDetail=Modifica riposo
att_schedule_makeUpClassDetail=Aggiungi
att_schedule_classDetail=Modifica turno
att_schedule_holidayDetail=Vacanze
att_schedule_noSchDetail=Non programmate
att_schedule_normalDetail=Normale
att_schedule_normalSchInfo=Shift Center: Nessun turno di più giorni
att_schedule_multipleInterSchInfo=Turni separati da virgola: più turni di più giorni
att_schedule_inderSchFirstDayInfo=Spostamento attraverso l'offset all'indietro della griglia: lo spostamento tra giorni viene registrato come primo giorno
att_schedule_inderSchSecondDayInfo=Spostamento attraverso l'offset in avanti della griglia: lo spostamento tra i giorni viene registrato come secondo giorno
att_schedule_timeConflict=Conflitto con il periodo turno esistente, non consentito salvare!
#=====================================================================
att_excp_notExisetPerson=La persona non esiste!
att_excp_leavePerson=Dimissioni Staff!
#补签单
att_sign_signTime=Orario timbratura
att_sign_signDate=Data timbratura
#请假
att_leave_arilName=Lascia tipo
att_leave_image=Lascia foto richiesta
att_leave_imageShow=No Foto
att_leave_imageType=Wrong Tip: Formato foto corretto, supporta formati: JPEG, GIF, PNG!
att_leave_imageSize=Wrong Tip: Il formato foto è troppo grande, taglia massima per file foto 4MB!
att_leave_leaveLongDay=Durata partenza(giorni)
att_leave_leaveLongHour=Orario partenza (ore)
att_leave_leaveLongMinute=Durata partenza(minuti)
att_leave_endNoLessAndEqualStart=L'ora di fine non può essere inferiore o uguale all'ora di inizio
att_leave_typeNameNoExsists=Il nome della classe falso non esiste
att_leave_startNotNull=L'ora di inizio non può essere vuota
att_leave_endNotNull=L'ora di fine non può essere vuota
att_leave_typeNameConflict=Il nome del tipo falso è in conflitto con il nome dello stato di presenza
#出差
att_trip_tripLongDay=Durata del viaggio(giorno)
att_trip_tripLongMinute=Durata del viaggio(minuti)
att_trip_tripLongHour=Durata del viaggio (ore)
#外出
att_out_outLongDay=Durata assenza(day)
att_out_outLongMinute=tempo assenza(minuti)
att_out_outLongHour=Tempo utilizzato (tempo)
#加班
att_overtime_type=Tipo di OT
att_overtime_normal=Normale OT
att_overtime_rest=Giorno di riposo OT
att_overtime_overtimeLong=Durata straordinari(Minuti)
att_overtime_overtimeHour=Ore Straordinario (Ore)
att_overtime_notice=Il tempo di applicazione per gli straordinari non è consentito per più di un giorno!
att_overtime_minutesNotice=Il tempo di applicazione degli straordinari non può essere inferiore al tempo minimo di straordinari!
#调休补班
att_adjust_type=Modifica tipo
att_adjust_adjustDate=Modifica data
att_adjust_shiftName=Turni presenze allegate
att_adjust_selectClass=Seleziona il nome del turno che richiede la presenza allegata,Grazie.
att_shift_notExistShiftWorkDate=La data di rettifica non è nel giorno lavorativo del turno e non può essere aggiunta
att_adjust_shiftPeriodStartMode=Il turno selezionato per il turno di trucco, se la data di inizio è per turno, l'impostazione predefinita è 0
att_adjust_shiftNameNoNull=I turni di trucco non possono essere vuoti
att_adjust_shiftNameNoExsist=Il turno di trucco non esiste
#调班
att_class_type=Tipo di modifica
att_class_sameTimeMoveShift=Modifica turno personale in questa giornata
att_class_differenceTimeMoveShift=Modifica turno personale in altre date
att_class_twoPeopleMove=Cambia due persone
att_class_moveDate=Modifica Data
att_class_shiftName=Nome originario del programma
att_class_moveShiftName=Il nuovo turno modificato non puo' essere privo di contenuti.
att_class_movePersonPin=Modifica ID del personale
att_class_movePersonName=Modifica nome del personale
att_class_movePersonLastName=Modifica cognome del personale
att_class_moveDeptName=Modifica Nome dipartimento
att_class_personPin=ID personale
att_class_shiftNameNoNull=Il nuovo turno modificato non puo' essere privo di contenuti.
att_class_personPinNoNull=ID personale della nuova persona non può essere vuoto!
att_class_isNotExisetSwapPersonPin=La nuova persona modificata non esiste, prego re-agg!
att_class_personNoSame=Non puoi modificare per la stessa persona, provare nuovamente Grazie.
att_class_outTime=La modifica della data e il trasferimento non possono essere più lunghe di un mese！
att_class_shiftNameNoExsist=Il turno di regolazione non esiste
att_class_swapPersonNoExisist=Il sensale non esiste
att_class_dateNoSame=Le persone vengono trasferite in date diverse, le date non possono essere le stesse
#=====================================================================
#节点
att_node_name=Nodo
att_node_type=Tippo Nodo
att_node_leader=Leader diretto
att_node_leaderNode=Leader diretto nodo
att_node_person=Persona Designata
att_node_position=Assegna Posizione
att_node_choose=Seleziona posizione
att_node_personNoNull=Personale non può essere vuoto!
att_node_posiitonNoNull=La posizione non pu?o essere vuota
att_node_placeholderNo=Si consiglia di cominciare con N, come N01.
att_node_placeholderName=Si consiglia di cominciare con una posizione o nome, finire con un nodo, come Nodo manager.
att_node_searchPerson=Immetti criteri di ricerca
att_node_positionIsExist=La posizione esiste già nel dato nodo, selezionare nuovamente la posizione Grazie.
#流程
att_flow_type=Tipo di flusso
att_flow_rule=Regole flusso
att_flow_rule0=Meno o uagule a 1 giorno
att_flow_rule1=Più di 1 giorni e meno o uguale a 3 giorni
att_flow_rule2=Più di 3 giorni e meno o uguale a 7 giorni 
att_flow_rule3=Più di 7 giorni
att_flow_node=Nodo di approvazione
att_flow_start=Inizio Flusso
att_flow_end=Fine flusso
att_flow_addNode=Add Nodo
att_flow_placeholderNo=It is recommended to start with F, such as F01.
att_flow_placeholderName=Si consiglia di cominciare col tipo e finire col flusso, eg.Lascia flusso.
att_flow_tips=Note: L'ordine di approvazione dei nodi va dall'alto verso il basso ed è possibile trascinare l'ordine dopo averlo selezionato.
#申请
att_apply_personPin=Applicazione ID personale
att_apply_type=Tipo di eccezione
att_apply_flowStatus=Stato generale del flusso
att_apply_start=Avvio di una applicazione
att_apply_flowing=In attesa
att_apply_pass=Passati
att_apply_over=fine
att_apply_refuse=Rifiutati
att_apply_revoke=Revoche
att_apply_except=Eccezioni
att_apply_view=Visualizza dettagli
att_apply_leaveTips=La persona ha richiesto un uscita durante questa fascia oraria!
att_apply_tripTips=Il personale ha richiesto un viaggio di lavoro per questa fascia oraria!
att_apply_outTips=Il personale ha fatto domanda per questa fascia oraria!
att_apply_overtimeTips=Il personale richiesto straordinario durante questa fascia oraria!
att_apply_adjustTips=Durante questa fascia oraria, il personale puo' candidarsi per una prova!
att_apply_classTips=Il personale a turni applicati durante quest fascia oraria!
#审批
att_approve_wait=In attesa di approvazione
att_approve_refuse=Non passato
att_approve_reason=Ragione
att_approve_personPin=ID approvatore
att_approve_personName=Nome approvatore
att_approve_person=Approvatore
att_approve_isPass=Approvare?
att_approve_status=Stato corrente del nodo
att_approve_tips=Il punto temporale esiste già nel flusso e non può essere ripetuto.
att_approve_tips2=Il nodo di flusso non è stato configurato, contattare l'amministratore per la configurazione.
att_approve_offDayConflicts=Nessuna domanda di remissione è consentita durante il giorno lavorativo.
att_approve_shiftConflicts=Nessuna domanda di rifornimento è consentita il giorno lavorativo.
att_approve_shiftNoSch=Non è consentita la riprogrammazione dell'applicazione nella data pianificata.
att_approve_classConflicts=Nessuna domanda di turno è consentita nella data non programmata
att_approve_selectTime=Il tempo selezionato determinerà il processo secondo le regole
att_approve_withoutPermissionApproval=Esiste un flusso di lavoro senza autorizzazione per l'approvazione, controlla!
#=====================================================================
#考勤计算
att_op_calculation=Calcolo delle presenze
att_op_calculation_notice=I dati sulle presenze sono stati calcolati in background, prego riprovare in seguito!
att_op_calculation_leave=Incluso il personale dimesso
att_statistical_choosePersonOrDept=Selezionare dipartimento o personale, Grazie!
att_statistical_sureCalculation=Sei sicuro di voler eseguire calcoli delle presenze?
att_statistical_filter=la condizione di filtraggio è pronto!
att_statistical_initData=Inizializzazione del database completata!
att_statistical_exception=Inizializzazione dati straordinari completata!
att_statistical_error=Calcolo assenza fallito!
att_statistical_begin=Inizio calcolo!
att_statistical_end=Fine calcolo!
att_statistical_noticeTime=Intervallo di tempo facoltativo di partecipazione: i primi due mesi al giorno!
att_statistical_remarkHoliday=H
att_statistical_remarkClass=C
att_statistical_remarkNoSch=NS
att_statistical_remarkRest=R
#原始记录表
att_op_importAccRecord=Import registri access control
att_op_importParkRecord=Importa registri parcheggio
att_op_importInsRecord=Importa registri faceKiosk
att_op_importPidRecord=Importa registri certificati pernali
att_op_importVmsRecord=Importa registri video 
att_op_importUSBRecord=Importa registri in U Disk
att_transaction_noAccModule=Nessun modulo Access control!
att_transaction_noParkModule=Nessun modulo parcheggio!
att_transaction_noInsModule=Nessun modulo faceKiosk!
att_transaction_noPidModule=Nessun modulo di certificato personale!
att_transaction_exportRecord=Esporta record originali
att_transaction_exportAttPhoto=Esporta le foto delle presenze
att_transaction_fileIsTooLarge=Il file esportato è troppo grande, restringere l'intervallo di date
att_transaction_exportDate=Data di esportazione
att_statistical_attDatetime=Date presenze
att_statistical_attPhoto=Dettagli foto
att_statistical_attDetail=Dettagli presenze
att_statistical_acc=Accesso controllo dispositivo 
att_statistical_att=Dispositivo time attendance
att_statistical_park=LPR Camera
att_statistical_faceRecognition=Dispositivo riconoscimento facciale
att_statistical_app=Dispositivo mobile
att_statistical_vms=Video Dispositivo
att_statistical_psg=Attrezzatura del canale
att_statistical_dataSources=Origine dei dati
att_transaction_SyncRecord=Sincronizza i record di presenza
#日打卡详情表
att_statistical_dayCardDetail=Inserire i dettagli
att_statistical_cardDate=Dati registrati
att_statistical_cardNumber=Volte registrate
att_statistical_earliestTime=Prima volta
att_statistical_latestTime=Ultima volta
att_statistical_cardTime=Orario timbratura
#请假汇总表
att_statistical_leaveDetail=Lascia dettagli
#日明细报表
att_statistical_attDate=Data presenza
att_statistical_week=Settimana
att_statistical_shiftInfo=Informazione turni
att_statistical_shiftTimeData=Tempo di accensione/spegnimeto
att_statistical_cardValidData=Tempo timbratura
att_statistical_cardValidCount=Conteggio timbrature
att_statistical_lateCount=Conteggio tardivo
att_statistical_lateMinute=Minuto in ritardo
att_statistical_earlyCount=Conteggio anticipato
att_statistical_earlyMinute=Minuto in anticipo
att_statistical_countData=Dati tempo
att_statistical_minuteData=Dati minuti
att_statistical_attendance_minute=Presenza (minuti)
att_statistical_overtime_minute=Straordinario (minuti)
att_statistical_unusual_minute=Anormale (minuti)
#月明细报表
att_monthdetail_should_hour=Should (ora)
att_monthdetail_actual_hour=Actual (ora)
att_monthdetail_valid_hour=Valido (ora)
att_monthdetail_absent_hour=Completamento (ora)
att_monthdetail_leave_hour=Partenza (ora)
att_monthdetail_trip_hour=Viaggio lavoro (ogni ora)
att_monthdetail_out_hour=Uscita (ogni ora)
att_monthdetail_should_day=Dovrebbe (giorno)
att_monthdetail_actual_day=Attuale (giorno)
att_monthdetail_valid_day=Effettivo (giorno)
att_monthdetail_absent_day=Completamento (giorno)
att_monthdetail_leave_day=Partenza (giorno)
att_monthdetail_trip_day=Viaggio lavoro (giorno)
att_monthdetail_out_day=Uscita (giorno)
#月统计报表
att_statistical_late_minute=Durata (minuti)
att_statistical_early_minute=Durata (minuti)
#部门统计报表
#年度统计报表
att_statistical_should=Dovrebbe
att_statistical_actual=Attuale
att_statistical_valid=Valido
att_statistical_numberOfTimes=Volte
att_statistical_usually=Feriale
att_statistical_rest=Weekend
att_statistical_holiday=Vacanza
att_statistical_total=Totale
att_statistical_month=Statistiche del Mese
att_statistical_year=Statistiche Anno
att_statistical_attendance_hour=Presenza (ora)
att_statistical_attendance_day=Presenza (giorno)
att_statistical_overtime_hour=Straordinario (ora)
att_statistical_unusual_hour=Eccezione (ora)
att_statistical_unusual_day=Anormale (giorno)
#考勤设备参数
att_deviceOption_query=Visualizza parametri dispositivo
att_deviceOption_noOption=Nessuna informazione sui paramatri, si prega di ottenere prima i parametri del dispositivo
att_deviceOption_name=Nome parametri
att_deviceOption_value=Valori parametri
att_deviceOption_UserCount=Numero attuale utenti
att_deviceOption_MaxUserCount=Numero massimo utenti
att_deviceOption_FaceCount=Numero modelli attuali volto
att_deviceOption_MaxFaceCount=Numero massimo modelli volto
att_deviceOption_FacePhotoCount=Numero attuale delle immagini volto
att_deviceOption_MaxFacePhotoCount=Numero massimo immagini volto
att_deviceOption_FingerCount=Numero modelli impronte digitali attuali
att_deviceOption_MaxFingerCount=Numero massimo modelli impronte digitali
att_deviceOption_FingerPhotoCount=Numero immagini impronte digitali 
att_deviceOption_MaxFingerPhotoCount=Numero massimo immagini impronte digitali
att_deviceOption_FvCount=Numero attuale modelli vena del dito
att_deviceOption_MaxFvCount=Numero massimo modelli vena del dito
att_deviceOption_FvPhotoCount=Numero attuale modelli vena del dito 
att_deviceOption_MaxFvPhotoCount=Numero immagine vena del dito
att_deviceOption_PvCount=Numero corretto modelli palmo
att_deviceOption_MaxPvCount=Numeso massimo modelli palmo
att_deviceOption_PvPhotoCount=Immagine palmo attuale
att_deviceOption_MaxPvPhotoCount=Numero massimo di immagini palmo
att_deviceOption_TransactionCount=Numero attuale di registri
att_deviceOption_MaxAttLogCount=Numero massimo di registrazioni
att_deviceOption_UserPhotoCount=Foto utente attuale
att_deviceOption_MaxUserPhotoCount=Numero massimo di foto utenti
att_deviceOption_FaceVersion=Versione dell'algoritmo del riconoscimento volto
att_deviceOption_FPVersion=Versione dell'algoritmo del riconoscimento improtna digitale
att_deviceOption_FvVersion=Versione dell'algoritmo del riconoscimento venda del dito
att_deviceOption_PvVersion=Versione dell'algoritmo del riconoscimento palmo
att_deviceOption_FWVersion=Versione Firmware
att_deviceOption_PushVersion=Versione Push
#=====================================================================
#API
att_api_areaCodeNotNull=Il numero di area non puo' essere vuoto
att_api_pinsNotNull=La lunghezza dei dati del pin non può superare 500
att_api_pinsOverSize=Pin data length is not allowed to exceed 500
att_api_areaNoExist=Area non esistente
att_api_sign=Supplementi
#=====================================================================
#休息时间段
att_leftMenu_breakTime=Pausa
att_breakTime_startTime=Inizio
att_breakTime_endTime=Fine
#=====================================================================
#导入U盘记录
att_import_uploadFileSuccess=File caricato con successo, avvia analisi file dati, attendere prego...
att_import_resolutionComplete=Dopo aver completato l'analisi, cominciare aggiornamento database.
att_import_snNoExist=Il dispositivo di presenza corrispondente al file importato non esiste. Seleziona di nuovo il file.
att_import_fileName_msg=Requisiti nome del file importato:il numero seriale del dispositvo inizia con e è separato da un underscore "_", per esempio："3517171600001_attlog.dat"。
att_import_notSupportFormat=Questo formato non è attualmente supportato!
att_import_selectCorrectFile=Selezionare il formato corretto del file, Grazie!
att_import_fileFormat=Formato del file
att_import_targetFile=File di destinazione
att_import_startRow=Numero di righe all'inizio dell'intestazione
att_import_startRowNote=La prima line dei formati dati è da importare in dati, controllare il file prima di importarlo, Grazie.
att_import_delimiter=Separatore
#=====================================================================
#设备操作日志
att_device_op_log_op_type=Codice operazione
att_device_op_log_dev_sn=Numero seriale dispositivo
att_device_op_log_op_content=Contenuto operato
att_device_op_log_operator_pin=Numero operatore
att_device_op_log_operator_name=Nome operatore
att_device_op_log_op_time=Tempo in funzione
att_device_op_log_op_who_value=Valori operazione oggetto
att_device_op_log_op_who_content=Operazione descrizione oggetto
att_device_op_log_op_value1=Operazione oggetto 2
att_device_op_log_op_value_content1=Operazione descrizione oggetto 2
att_device_op_log_op_value2=Operazione oggetto 3
att_device_op_log_op_value_content2=Operazione descrizione oggetto 3
att_device_op_log_op_value3=Operazione oggetto 4
att_device_op_log_op_value_content3=Operazione descrizione oggetto 4
#操作日志的操作类型
att_device_op_log_opType_0=Acceso
att_device_op_log_opType_1=Spegnimento
att_device_op_log_opType_2=Verificazione fallita
att_device_op_log_opType_3=Allarme
att_device_op_log_opType_4=Entra nel menu
att_device_op_log_opType_5=Cambia impostazionie
att_device_op_log_opType_6=Registrare impornta digitale
att_device_op_log_opType_7=Registra password
att_device_op_log_opType_8=Registra HID card
att_device_op_log_opType_9=Elimina Utente
att_device_op_log_opType_10=Cancella impronta
att_device_op_log_opType_11=Cancella password
att_device_op_log_opType_12=Cancella RF carta
att_device_op_log_opType_13=Elimina dati
att_device_op_log_opType_14=Crea MF carta
att_device_op_log_opType_15=Registrati MF carta
att_device_op_log_opType_16=Registrati MF carta
att_device_op_log_opType_17=Elimina MF registri carta
att_device_op_log_opType_18=Cancella MF contenuti carta
att_device_op_log_opType_19=Sposta dati registrati sulla carta
att_device_op_log_opType_20=Copia i dati dalla carta alla macchina
att_device_op_log_opType_21=Imposta orario
att_device_op_log_opType_22=Impostazioni di fabbrica
att_device_op_log_opType_23=Cancella registri entrata e uscita
att_device_op_log_opType_24=Cancella privilegi Admin
att_device_op_log_opType_25=Modifica impostazioni controllo accessi gruppi
att_device_op_log_opType_26=Modifica impostazioni acesso utente
att_device_op_log_opType_27=Modifica periodo di tempo acessi
att_device_op_log_opType_28=Modifica impostazioni combinazioni di sblocco
att_device_op_log_opType_29=Sblocca
att_device_op_log_opType_30=Registri nuovi utenti
att_device_op_log_opType_31=Cambia attributi impronte digitali
att_device_op_log_opType_32=Allarme forzato
att_device_op_log_opType_34=Anti-passback
att_device_op_log_opType_35=Elimina le foto presenti
att_device_op_log_opType_36=Modifica informazioni utente
att_device_op_log_opType_37=Vacanze
att_device_op_log_opType_38=Ripristina dati
att_device_op_log_opType_39=Dati di Backup
att_device_op_log_opType_40=Carica su disco U
att_device_op_log_opType_41=Download disco U
att_device_op_log_opType_42=Crittografia record di frequenza disco su U
att_device_op_log_opType_43=Elimina registri dopo averli caricati correttamente su disco USB
att_device_op_log_opType_53=Interruttore uscita
att_device_op_log_opType_54=Sensore porta
att_device_op_log_opType_55=Allarm
att_device_op_log_opType_56=Ripristina parametri
att_device_op_log_opType_68=Foto utente registrate
att_device_op_log_opType_69=Modifica foto utente
att_device_op_log_opType_70=Modifica nome utente
att_device_op_log_opType_71=Modifica permessi utente
att_device_op_log_opType_76=Modifica impostazioni di rete IP
att_device_op_log_opType_77=Modifica impostazioni di rete mask
att_device_op_log_opType_78=Modifica impostazioni di rete gateway
att_device_op_log_opType_79=Modifica impostazioni di rete DNS
att_device_op_log_opType_80=Modifica parametri flag impornta palmo
att_device_op_log_opType_81=Modifica impostazioni connessione ID dispositivo
att_device_op_log_opType_82=Modifica indirizzo server del cloud
att_device_op_log_opType_83=Modifica porta server del cloud
att_device_op_log_opType_87=Modifica impostazioni registro acesscontrol
att_device_op_log_opType_88=Modifica parametri flag volto
att_device_op_log_opType_89=Modifica parametri flag impronte digitali
att_device_op_log_opType_90=Modica falg parametri vena del dito
att_device_op_log_opType_91=Modifica flag parametri impronta del palmo
att_device_op_log_opType_92=Flad di aggionamento del disco
att_device_op_log_opType_100=Modifica RF informazioni carta
att_device_op_log_opType_101=Volti registrati
att_device_op_log_opType_102=Modifica permessi staff
att_device_op_log_opType_103=Cancella permessi personale
att_device_op_log_opType_104=Add permessi staff
att_device_op_log_opType_105=Cancella registri di access control
att_device_op_log_opType_106=Cancella volto
att_device_op_log_opType_107=Cancella foto personale
att_device_op_log_opType_108=Modifica parametri
att_device_op_log_opType_109=Seleziona WIFI SSID
att_device_op_log_opType_110=proxy abilitato
att_device_op_log_opType_111=Modifica ip proxy
att_device_op_log_opType_112=Modifica porta proxy
att_device_op_log_opType_113=Modifica password persona
att_device_op_log_opType_114=Modifica informazioni facciali
att_device_op_log_opType_115=Modifica password operatore
att_device_op_log_opType_116=Riprendi le impostazioni di Access control
att_device_op_log_opType_117=Password operatore errata
att_device_op_log_opType_118=Password operatore bloccata
att_device_op_log_opType_120=Modifica la lunghezza dei dati della carta Legic
att_device_op_log_opType_121=Registra la vena del dito
att_device_op_log_opType_122=Modifica la vena del dito
att_device_op_log_opType_123=Elimina la vena del dito
att_device_op_log_opType_124=Registra Palm
att_device_op_log_opType_125=Modifica Palm
att_device_op_log_opType_126=Elimina Palm
#操作对象描述
att_device_op_log_content_pin=ID Utente:
att_device_op_log_content_alarm=Allarme:
att_device_op_log_content_alarm_reason=Ragione allarme:
att_device_op_log_content_update_no=Modifica numero articolo:
att_device_op_log_content_update_value=Modifica valori:
att_device_op_log_content_finger_no=Numero impronta:
att_device_op_log_content_finger_size=Lunghezza modello impronte digitali:
#=====================================================================
#工作流
att_flowable_datetime_to=per
att_flowable_todomsg_leave=Uscita approvata
att_flowable_todomsg_sign=aggingi log approvato
att_flowable_todomsg_overtime=Straordinario approvato
att_flowable_notifymsg_leave=Lascia notifiche applicazioni
att_flowable_notifymsg_sign=Aggiungi registro notifiche 
att_flowable_notifymsg_overtime=Notifiche straordinari
att_flowable_shift=Cambio:
att_flowable_hour=Ora
att_flowable_todomsg_trip=Viaggio di lavoro approvato
att_flowable_notifymsg_trip=Viaggio di lavoro 
att_flowable_todomsg_out=Esci da approvazioni
att_flowable_notifymsg_out=Esci da notifica
att_flow_apply=Applicazione
att_flow_applyTime=Tempo applicazione
att_flow_approveTime=Tempo di processo
att_flow_operateUser=Recensore
att_flow_approve=Approvazioni
att_flow_approveComment=annotazioni
att_flow_approvePass=Risultati approvati
att_flow_status_processing=Approvazione
#=====================================================================
#biotime
att_h5_pers_personIdNull=ID dipendenti non puo' essere vuoto
att_h5_attPlaceNull=Le impostazioni check-in non possono esse vuote
att_h5_attAreaNull=Area presenze non puo' essere vuota
att_h5_pers_personNoExist=Numero dipendente non esistente
att_h5_signRemarkNull=Le osservazioni non possono essere vuote
att_h5_common_pageNull=Errore parametri paging
att_h5_taskIdNotNull=ID Task node non puo' essere vuoto
att_h5_auditResultNotNull=Risultati approvati non possono essere vuoti
att_h5_latLongitudeNull=Longitudine e latitudine non possono essere vuoti
att_h5_pers_personIsNull=ID dipendente non esistente
att_h5_pers_personIsNotInArea=La persona non ha impostatto l'area
att_h5_mapApiConnectionsError=Errore di connessione api map
att_h5_googleMap=Google Map
att_h5_gaodeMap=Gaode Map
att_h5_defaultMap=Mappa di default
att_h5_shiftTime=Tempo cambio
att_h5_signTimes=Tempo di rifornimento
att_h5_enterKeyWords=Prego immettere parola chiave:
att_h5_mapSet=Impostazione mappa presenze
att_h5_setMapApiAddress=Imposta parametri mappa
att_h5_MapSetWarning=Il cambio della mappa farà sì che l'indirizzo di accesso al terminale mobile inserito non corrisponda alla latitudine e alla longitudine, si prega di modificare con cautela!
att_h5_mapSelect=Seleziona mappa
att_h5_persNoHire=L'impiegato non è ancora in azienda in questo momento.
att_slef_apiKey=API-KEY
att_self_apiJsKey=API-JS-KEY
att_self_noComputeAtt=la presenza della giornata non è ancora stata presa in considerazione.
att_self_noSignRecord=Nessuna timbratura registrata in questa giornata
att_self_imageUploadError=Caricamento immagine fallito
att_self_attSignAddressAreaIsExist=Sono già presenti punti check-in in questa area
att_self_signRuleIsError=Il tempo di rifornimento attuale non è compreso nel tempo di rifornimento consentito.
att_self_signAcrossDay=Il programma della giornata non può essere firmato!
att_self_todaySignIsExist=Sono già presenti patch aggiunte oggi!
att_self_signSetting=Impostazione firma
att_self_allowSign=Consenti firma:
att_self_allowSignSuffix=Giorni,record di presenze
att_self_onlyThisMonth=Solo questo mese
att_self_allowAcrossMonth=Consentri tra un mese
att_self_thisTimeNoSch=Non vi è alcun turno su questa fascia oraria!
att_self_revokeReason=Ragione per la revoca:
att_self_revokeHint=Inserisci il motivo della cancellazione in 20 parole per la revisione
att_self_persSelfLogin=Accesso impegato self-service
att_self_isOpenSelfLogin=Se avviare la voce di accesso self-service dei dipendenti
att_self_applyAndWorkTimeOverlap=Il tempo di applicazione e l'orario di lavoro si sovrappongono
att_apply_DurationIsZero=La durata dell'applicazione è 0, nessuna applicazione è consentita
att_sign_mapWarn=Caricamento mappa fallito, controllare connessiona a internet e i valori map KEY
att_admin_applyWarn=Operazione fallita, ci sono persone non in programma o che il tempo di applicazione non rientra nel programma!({0})
att_self_getPhotoFailed=Foto non esistente
att_self_view=Visualizza
# 二维码
att_param_qrCodeUrl=QR code Url
att_param_qrCodeUrlHref=Indirizzo server: porta
att_param_appAttQrCode=Assistenza Mobile QR code
att_param_timingFrequency=Time interval: 5-59 minuti o 1-24 ore
att_sign_signTimeNotNull=Il registro di accodamento non puo' essere vuoto
att_apply_overLastMonth=Applicazioni sono più lunghe rispetto al mese scorso
att_apply_withoutDetail=Nessun dettaglio di processo
att_flowable_noAuth=Si prega di utilizzare l'account super amministratore per visualizzare
att_apply_overtimeOverMaxTimeLong=Le ore di straordinario sono maggiori delle ore di straordinario
# 考勤设置参数符号
att_other_arrive=√
att_other_late=L
att_other_early=E
att_other_absent=□
att_other_noSignIn=[
att_other_noSignOff=]
att_other_leave=∆
att_other_overtime=OT
att_other_off=○
att_other_classes=●
att_other_trip=T
att_other_out=G
att_other_incomplete=├
att_other_outcomplete=┤
# 服务器下发命令
att_devCmd_submitTime=Invia ora
att_devCmd_returnedResult=Restituisci il risultato
att_devCmd_returnTime=ora di ritorno
att_devCmd_content=Contenuto del comando
att_devCmd_clearCmd=Cancella elenco comandi
# 实时点名
att_realTime_selectDept=Seleziona un dipartimento
att_realTime_noSignPers=Persona non registrata
att_realTime_signPers=Check-in
att_realTime_signMonitor=Monitoraggio dell'accesso
att_realTime_signDateTime=Orario di check-in
att_realTime_realTimeSet=Impostazioni Roll Call in tempo reale
att_realTime_openRealTime=Abilita chiamata in tempo reale
att_realTime_rollCallEnd=L'appello in tempo reale termina
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=Pianificato
att_personSch_cycleSch=Pianificazione del ciclo
att_personSch_cleanCycleSch=Programma ciclo pulito
att_personSch_cleanTempSch=Cancella pianificazione temporanea
att_personSch_personCycleSch=Pianificazione del ciclo della persona
att_personSch_deptCycleSch=Pianificazione del ciclo del reparto
att_personSch_groupCycleSch=Pianificazione del ciclo di gruppo
att_personSch_personTempSch=Pianificazione temporanea persona
att_personSch_deptTempSch=Pianificazione temporanea del reparto
att_personSch_groupTempSch=Pianificazione temporanea del gruppo
att_personSch_checkGroupFirst=Controlla il gruppo a sinistra o la persona nell'elenco a destra per operare!
att_personSch_sureDeleteGroup=Sei sicuro di eliminare {0} e la pianificazione corrispondente al gruppo?
att_personSch_sch=Pianificazione
att_personSch_delSch=Elimina pianificazione
#考勤计算
att_statistical_sureAllCalculate=Sei sicuro di eseguire il calcolo delle presenze per tutto il personale?
#异常管理
att_exception_downTemplate=Scarica modello di importazione
att_exception_signImportTemplate=Modello di importazione registro aggiunto
att_exception_leaveImportTemplate=Lascia modello di importazione
att_exception_overtimeImportTemplate=Modello di importazione straordinario
att_exception_adjustImportTemplate=Modifica modello di importazione
att_exception_cellDefault=Campo non obbligatorio
att_exception_cellRequired=Campo obbligatorio
att_exception_cellDateTime=Campo obbligatorio, il formato dell'ora è aaaa-MM-gg HH: mm: ss, ad esempio: 2020-07-07 08:30:00
att_exception_cellLeaveTypeName=Campo obbligatorio, ad esempio: "Congedo occasionale", "Congedo matrimoniale", "Congedo di maternità", "Congedo per malattia", "Congedo annuale", "Congedo per lutto", "Congedo in casa", "Congedo per allattamento", "Affari Trip ',' Going Out 'e così via.
att_exception_cellOvertimeSign=Campo obbligatorio, per ogni esempio: "OT normale", "OT giorno di riposo", "OT festivo"
att_exception_cellAdjustType=Campo obbligatorio, ad esempio: 'Regola riposo', 'Aggiungi presenze'
att_exception_cellAdjustDate=Campo obbligatorio, il formato dell'ora è yyyy-MM-dd, ad esempio: 2020-07-07
att_exception_cellShiftName=Campo obbligatorio quando il tipo di rettifica è "Aggiungi partecipazione"
att_exception_refuse=Rifiuta
att_exception_end=Fine anomala
att_exception_delete=Elimina
att_exception_stop=Pausa
#时间段
att_timeSlot_normalTimeAdd=Aggiungi normale fascia oraria
att_timeSlot_elasticTimeAdd=Aggiungi fascia oraria flessibile
#班次
att_shift_addRegularShift=Aggiungi turno regolare
att_shift_addFlexibleShift=Aggiungi turno flessibile
#参数设置
att_param_notLeaveSetting=Impostazione del calcolo non abbandono
att_param_smallestUnit=Unità minima
att_param_workDay=Giornata lavorativa
att_param_roundingControl=Controllo arrotondamento
att_param_abort=Giù (scarta)
att_param_rounding=arrotondamento
att_param_carry=Su (trasporta)
att_param_reportSymbol=Simbolo di visualizzazione del report
att_param_convertCountValid=Immettere un numero ed è consentita una sola cifra decimale
att_other_leaveThing=Casual
att_other_leaveMarriage=Matrimonio
att_other_leaveBirth=Maternità
att_other_leaveSick=Sick
att_other_leaveAnnual=Annuale
att_other_leaveFuneral=Lutto
att_other_leaveHome=Home
att_other_leaveNursing=Allattamento al seno
att_other_leavetrip=Affari
att_other_leaveout=Fuori
att_common_schAndRest=Programma e riposa
att_common_timeLongs=Durata del tempo
att_personSch_checkDeptOrPersFirst=Per favore controlla il gruppo a sinistra o la persona sulla lista di destra per operare!
att_personSch_checkCalendarFirst=Seleziona prima la data che devi programmare!
att_personSch_cleanCheck=Cancella controllo
att_personSch_delTimeSlot=Cancella il periodo di tempo selezionato
att_personSch_repeatTimeSlotNoAdd=Non verrà aggiunto alcun periodo di tempo ripetitivo!
att_personSch_showSchInfo=Mostra i dettagli della pianificazione
att_personSch_sureToCycleSch=Sei sicuro di programmare {0} ciclicamente?
att_personSch_sureToTempSch=Sei sicuro di programmare {0} temporaneamente?
att_personSch_sureToCycleSchDeptOrGroup=Sei sicuro di programmare tutto il personale in {0} ciclicamente?
att_personSch_sureToTempSchDeptOrGroup=Sei sicuro di programmare temporaneamente tutto il personale in {0}?
att_personSch_sureCleanCycleSch=Sei sicuro di voler cancellare la pianificazione del ciclo per {0} da {1} a {2}?
att_personSch_sureCleanTempSch=Sei sicuro di voler cancellare il turno temporaneo per {0} da {1} a {2}?
att_personSch_sureCleanCycleSchDeptOrGroup=Sei sicuro di voler cancellare la pianificazione del ciclo per tutto il personale in {0} da {1} a {2}?
att_personSch_sureCleanTempSchDeptOrGroup=Sei sicuro di voler cancellare la pianificazione temporanea per tutto il personale in {0} da {1} a {2}?
att_personSch_today=Oggi
att_personSch_timeSoltName=Nome periodo di tempo
att_personSch_export=Esporta la pianificazione del personale
att_personSch_exportTemplate=Esporta modello di turno temporaneo del personale
att_personSch_import=Importa pianificazione temporanea del personale
att_personSch_tempSchTemplate=Modello di pianificazione del personale temporaneo
att_personSch_tempSchTemplateTip=Seleziona l'ora di inizio e l'ora di fine della pianificazione, scarica il modello di pianificazione entro quella data
att_personSch_opTip=Istruzioni per l'uso
att_personSch_opTip1=1. È possibile trascinare l'intervallo di tempo in una singola data nel controllo del calendario per pianificare.
att_personSch_opTip2=2.Nel controllo del calendario, fare doppio clic su una singola data da pianificare.
att_personSch_opTip3=3.Nel controllo del calendario, tenere premuto il mouse per selezionare più date da pianificare.
att_personSch_schRules=Regole di pianificazione
att_personSch_schRules1=1.Pianificazione ciclo: entro lo stesso giorno, la pianificazione successiva sovrascriverà la pianificazione precedente, indipendentemente dal fatto che vi sia una sovrapposizione.
att_personSch_schRules2=2. Pianificazione temporanea: se si verifica una sovrapposizione nello stesso giorno, la pianificazione temporanea precedente verrà sovrascritta. Se non ci sono sovrapposizioni, esisteranno simultaneamente.
att_personSch_schRules3=3.Ciclo e Temporaneo: se si verifica una sovrapposizione nello stesso giorno, la pianificazione del ciclo sovrascriverà la pianificazione temporanea e, se non vi è alcuna sovrapposizione, esisteranno anche simultaneamente.
att_personSch_schStatus=Stato pianificazione
#左侧菜单-排班管理
att_leftMenu_schDetails=Dettagli pianificazione
att_leftMenu_detailReport=Report dettagli presenze
att_leftMenu_signReport=Dettagli registro aggiunti
att_leftMenu_leaveReport=Lascia i dettagli
att_leftMenu_abnormal=Rapporto di presenza anormale
att_leftMenu_yearLeaveSumReport=SumReport ferie annuali
att_leave_maxFileCount=Puoi aggiungere solo 4 foto al massimo
#时间段
att_timeSlot_add=Imposta fascia oraria
att_timeSlot_select=Seleziona un periodo di tempo!
att_timeSlot_repeat=Il periodo di tempo "{0}" si ripete!
att_timeSlot_overlapping=Il periodo di tempo "{0}" si sovrappone all'orario di lavoro di "{1}"!
att_timeSlot_addFirst=Imposta prima il periodo di tempo!
att_timeSlot_notEmpty=Il periodo di tempo corrispondente al numero di personale {0} non può essere vuoto!
att_timeSlot_notExist=Il periodo di tempo "{1}" corrispondente al numero di personale {0} non esiste!
att_timeSlot_repeatEx=Il periodo di tempo "{1}" corrispondente al numero del personale {0} si sovrappone all'orario di lavoro di "{2}"
att_timeSlot_importRepeat=Il periodo di tempo "{1}" corrispondente al numero di personale {0} viene ripetuto
att_timeSlot_importNotPin=Non c'è personale con numero {0} nel sistema!
att_timeSlot_elasticTimePeriod=Numero del personale {0}, non è possibile importare il periodo di tempo flessibile "{1}"!
#导入
att_import_overData=Il numero corrente di importazioni è {0}, supera il limite di 30.000, importare in batch!
att_import_existIllegalType=Il {0} importato ha un tipo illegale!
#验证方式
att_verifyMode_0=Riconoscimento automatico
att_verifyMode_1=Solo impronta digitale
att_verifyMode_2=Solo Pin
att_verifyMode_3=Solo password
att_verifyMode_4=Solo carta
att_verifyMode_5=Impronta digitale o password
att_verifyMode_6=Impronta digitale o scheda
att_verifyMode_7=Carta o password
att_verifyMode_8=Pin e impronta digitale
att_verifyMode_9=Impronta digitale e password
att_verifyMode_10=Carta e impronta digitale
att_verifyMode_11=Carta e password
att_verifyMode_12=Impronta digitale, password e scheda
att_verifyMode_13=Pin, impronta digitale e password
att_verifyMode_14=(Pin e impronta digitale) o (Carta e impronta digitale)
att_verifyMode_15=Faccia
att_verifyMode_16=Viso e impronta digitale
att_verifyMode_17=Viso e password
att_verifyMode_18=Face and Card
att_verifyMode_19=Viso, impronta digitale e scheda
att_verifyMode_20=Viso, impronta digitale e password
att_verifyMode_21=Vena del dito
att_verifyMode_22=Vena del dito e password
att_verifyMode_23=Vena del dito e carta
att_verifyMode_24=Vena del dito, password e scheda
att_verifyMode_25=Palmprint
att_verifyMode_26=Palmprint e scheda
att_verifyMode_27=Impronta palmare e viso
att_verifyMode_28=Impronta palmare e impronta digitale
att_verifyMode_29=Impronta palmare, impronta digitale e viso
# 工作流
att_flow_schedule=Controlla i progressi
att_flow_schedulePass=(superato)
att_flow_scheduleNot=(Stai recensendo)
att_flow_scheduleReject=(Rifiutato)
# 工作时长表
att_workTimeReport_total=Ore lavorative totali
# 自动导出报表
att_autoExport_startEndTime=Ora di inizio e di fine
# 年假
att_annualLeave_setting=Impostazione del saldo delle ferie annuali
att_annualLeave_settingTip1=Per utilizzare la funzione di saldo delle ferie annuali, è necessario impostare l'ora di ingresso per ogni dipendente; quando l'ora di ingresso non è impostata, le ferie annuali rimanenti del dipendente nel report del saldo delle ferie annuali vengono visualizzate come vuote.
att_annualLeave_settingTip2=Se la data corrente è maggiore della data di emissione della cancellazione, questa modifica avrà effetto l'anno successivo; se la data corrente è inferiore alla data di rilascio della compensazione, quando viene raggiunta la data di emissione della compensazione, verrà cancellata e le ferie annuali verranno ristampate.
att_annualLeave_calculate=Data di rilascio e rilascio delle ferie annuali
att_annualLeave_workTimeCalculate=Calcola in base al rapporto tempo di lavoro
att_annualLeave_rule=Regola del tempo di congedo annuale
att_annualLeave_ruleCountOver=È stato raggiunto il limite massimo di numero impostato
att_annualLeave_years=Anni senior
att_annualLeave_eachYear=Ogni anno
att_annualLeave_have=Ha
att_annualLeave_days=Giorni di ferie annuali
att_annualLeave_totalDays=ferie annuali totali
att_annualLeave_remainingDays=Ferie annuali rimanenti
att_annualLeave_consecutive=L'impostazione della regola delle ferie annuali deve essere di anni consecutivi
# 年假结余表
att_annualLeave_report=Bilancio delle ferie annuali
att_annualLeave_validDate=Data valida
att_annualLeave_useDays=Utilizza {0} giorni
att_annualLeave_calculateDays=Rilascio {0} giorni
att_annualLeave_notEnough=Ferie annuali insufficienti di {0}!
att_annualLeave_notValidDate={0} non rientra nell'intervallo valido delle ferie annuali!
att_annualLeave_notDays={0} non ha ferie annuali!
att_annualLeave_tip1=Zhang San si è unito al 1 ° settembre dello scorso anno
att_annualLeave_tip2=Impostazione del saldo delle ferie annuali
att_annualLeave_tip3=La data di liquidazione ed emissione è il 1 ° gennaio di ogni anno; è calcolata arrotondando in base al rapporto di lavoro; se l'anzianità è minore o uguale a 1, ci saranno 3 giorni di ferie annuali, e se l'anzianità è inferiore o uguale a 3 anni, ci saranno 5 giorni di ferie annuali
att_annualLeave_tip4=Calcolo del godimento delle ferie annuali
att_annualLeave_tip5=L'anno scorso 09-01 ~ 12-31 goditi 4 / 12x3 = 1.0 giorni
att_annualLeave_tip6=Quest'anno 01-01 ~ 12-31 goditi 4.0 giorni (quest'anno 01-01 ~ 08-31 goditi 8 / 12x3 = 2.0 giorni + quest'anno 09-01 ~ 12-31 goditi 4 / 12x5≈2.0 giorni)
# att SDC
att_sdc_name=Attrezzatura video
att_sdc_wxMsg_firstData=Salve, hai un avviso di registrazione delle presenze
att_sdc_wxMsg_stateData=Nessun senso di presenza nel check-in riuscito
att_sdc_wxMsg_remark=Promemoria: il risultato finale della partecipazione è soggetto alla pagina dei dettagli del check-in.
# 时间段
att_timeSlot_conflict=La fascia oraria è in conflitto con altre fasce orarie della giornata
att_timeSlot_selectFirst=Seleziona la fascia oraria
# 事件中心
att_eventCenter_sign=Accesso presenze
#异常管理
att_exception_classImportTemplate=Modello di importazione della classe
att_exception_cellClassAdjustType=Campo obbligatorio, come: "{0}", "{1}", "{2}"
att_exception_swapDateDate=campo non obbligatorio, il formato dell'ora è aaaa-MM-gg, ad esempio: 07-07-2020
#消息中心
att_message_leave=Avviso di partecipazione {0}
att_message_leaveContent={0} inviato {1}, {2} l'ora è {3}~{4}
att_message_leaveTime=Tempo di uscita
att_message_overtime=Avviso di presenza e lavoro straordinario
att_message_overtimeContent={0} ha inviato straordinari e il tempo straordinario è {1}~{2}
att_message_overtimeTime=Tempo straordinario
att_message_sign=Segnale di avviso di presenza
att_message_signContent={0} ha inviato un segno supplementare e l'ora del segno supplementare è {1}
att_message_adjust=Avviso di adeguamento delle presenze
att_message_adjustContent={0} ha inviato una rettifica e la data della rettifica è {1}
att_message_class=Notifica presenze e turni
att_message_classContent=Contenuto della classe
att_message_classContent0={0} ha inviato un turno, la data del turno è {1} e il turno è {2}
att_message_classContent1={0} ha inviato un turno, la data del turno è {1} e la data del turno è {2}
att_message_classContent2={0} ({1}) e {2} ({3}) scambiano classi
#推送中心
att_pushCenter_transaction=Registro presenze
# 时间段
att_timeSlot_workTimeNotEqual=Il tempo di lavoro non può essere uguale all'orario di lavoro
att_timeSlot_signTimeNotEqual=L'ora di inizio accesso non può essere uguale all'ora di fine disconnessione
# 北向接口A
att_api_notNull={0} non può essere vuoto!
att_api_startDateGeEndDate=L'ora di inizio non può essere maggiore o uguale all'ora di fine!
att_api_leaveTypeNotExist=La specie falsa non esiste!
att_api_imageLengthNot2000=La lunghezza dell'indirizzo dell'immagine non può superare 2000!
# 20221230新增国际化
att_personSch_workTypeNotNull=Il tipo di lavoro corrispondente al numero di personale {0} non può essere vuoto!
att_personSch_workTypeNotExist=Il tipo di lavoro corrispondente al numero di personale {0} non esiste!
att_annualLeave_recalculate=Ricalcola
# 20230530新增国际化
att_leftMenu_dailyReport=Rapporto giornaliero presenze
att_leftMenu_overtimeReport=Resoconto Straordinari
att_leftMenu_lateReport=Rapporto in ritardo
att_leftMenu_earlyReport=Esci dal rapporto in anticipo
att_leftMenu_absentReport=Rapporto assente
att_leftMenu_monthReport=Rapporto mensile presenze
att_leftMenu_monthWorkTimeReport=Rapporto mensile sull'orario di lavoro
att_leftMenu_monthCardReport=Rapporto carte mensile
att_leftMenu_monthOvertimeReport=Rapporto Mensile Straordinari
att_leftMenu_overtimeSummaryReport=Rapporto di riepilogo straordinari del personale
att_leftMenu_deptOvertimeSummaryReport=Report Riepilogo Straordinari Reparto
att_leftMenu_deptLeaveSummaryReport=Rapporto di riepilogo ferie reparto
att_annualLeave_calculateDay=Numero di giorni di ferie annuali
att_annualLeave_adjustDay=Regola giorni
att_annualLeave_sureSelectDept=Sei sicuro di voler eseguire {0} operazione sul reparto selezionato?
att_annualLeave_sureSelectPerson=Sei sicuro di voler eseguire l'operazione {0} sulla persona selezionata?
att_annualLeave_calculateTip1=Nel calcolo in base all'anzianità di servizio: il calcolo delle ferie annuali è accurato al mese, se l'anzianità di servizio è di 10 anni e 3 mesi, per il calcolo verranno utilizzati 10 anni e 3 mesi;
att_annualLeave_calculateTip2=Quando la conversione non è basata sull'anzianità di servizio: il calcolo delle ferie annuali è accurato all'anno, se l'anzianità di servizio è di 10 anni e 3 mesi, verranno utilizzati 10 anni per il calcolo;
att_rule_isInCompleteTip=La priorità è la più alta quando nessun accesso o nessuna uscita è registrato come incompleto e ritardo, congedo anticipato, assenteismo e valido
att_rule_absentTip=Quando nessun accesso o nessuna uscita viene registrato come assenteismo, la durata dell'assenteismo è uguale alla durata dell'orario di lavoro meno la durata del congedo tardivo o anticipato
att_timeSlot_elasticTip1=0, il tempo effettivo è uguale al tempo effettivo, no assenteismo
att_timeSlot_elasticTip2=Se l'orario effettivo è più lungo dell'orario di lavoro, l'orario effettivo è uguale all'orario di lavoro, no assenteismo
att_timeSlot_elasticTip3=Se la durata effettiva è inferiore alla durata lavorativa, la durata effettiva è uguale alla durata effettiva e l'assenteismo è uguale alla durata lavorativa meno la durata effettiva
att_timeSlot_maxWorkingHours=L'orario di lavoro non può essere maggiore di
# 20231030
att_customReport=Rapporto personalizzato sulle presenze
att_customReport_byDayDetail=Dettaglio per giorno
att_customReport_byPerson=Riepilogo per persona
att_customReport_byDept=Riepilogo per reparto.
att_customReport_queryMaxRange=L'intervallo massimo di interrogazione è di quattro mesi.
# 20240630新增国际化
att_personSch_shiftWorkTypeTip1=1. Quando si lavora straordinario nei normali giorni di lavoro/riposo, la priorità di programmazione è inferiore a quella delle vacanze
att_personSch_shiftWorkTypeTip2=2. Quando il tipo di lavoro è straordinario durante le vacanze, la priorità di programmazione è superiore a quella delle vacanze
att_personVerifyMode=Metodo di verifica del personale
att_personVerifyMode_setting=Impostazioni del metodo di verifica
att_personSch_importCycSch=Importa la pianificazione del ciclo del personale
att_personSch_cycSchTemplate=Modello di programmazione del ciclo del personale
att_personSch_exportCycSchTemplate=Scarica il modello di pianificazione del ciclo del personale
att_personSch_scheduleTypeNotNull=Il tipo di spostamento non può essere vuoto o non esiste!
att_personSch_shiftNotNull=Il turno non può essere vuoto!
att_personSch_shiftNotExist=Il cambiamento non esiste!
att_personSch_onlyAllowOneShift=La programmazione regolare permette di programmare un solo turno!
att_shift_attShiftStartDateRemark2=La settimana in cui si trova la data di inizio del ciclo è la prima settimana; Il mese in cui si trova la data di inizio del ciclo è il primo mese.
#打卡状态
att_cardStatus_setting=Impostazione dello stato di assenza
att_cardStatus_name=Nome
att_cardStatus_value=Valore
att_cardStatus_alias=Alias
att_cardStatus_every_day=Ogni giorno
att_cardStatus_by_week=Per settimana
att_cardStatus_autoState=Stato automatico
att_cardStatus_attState=Stato di assenza
att_cardStatus_signIn=Accedere
att_cardStatus_signOut=Esci
att_cardStatus_out=uscita
att_cardStatus_outReturn=Ritorno dopo l'uscita
att_cardStatus_overtime_signIn=Accedere in tempo di overtime
att_cardStatus_overtime_signOut=Uscire in tempo di overtime
# 20241030新增国际化
att_leaveType_enableMaxDays=Abilita la soglia annuale
att_leaveType_maxDays=Limite annuale (giorni)
att_leaveType_applyMaxDays=La richiesta annuale non può superare {0} giorni
att_param_overTimeSetting=Configurazione livello straordinari
att_param_overTimeLevel=Livello di straordinario (ore)
att_param_overTimeLevelEnable=Abilita il calcolo dei livelli di straordinario
att_param_reportColor=Colore del report
# APP
att_app_signClientTip=Questo dispositivo è già stato registrato da qualcun altro oggi
att_app_noSignAddress=Area di controllo accesso non impostata, contattare l'amministratore per la configurazione
att_app_notInSignAddress=Non è stata raggiunta la posizione di controllo accesso, impossibile effettuare il controllo
att_app_attendance=La mia presenza
att_app_apply=Domanda di presenza
att_app_approve=Le mie approvazioni
# 20250530
att_node_leaderNodeExist=Nodo di approvazione del diretto superiore già esistente
att_signAddress_init=Inizializzazione mappa
att_signAddress_initTips=Inserire la chiave della mappa e inizializzarla per selezionare un indirizzo