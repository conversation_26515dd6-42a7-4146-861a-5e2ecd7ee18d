#系统名称 俄语
att_systemName=Система учёта рабочего времени 1.0
#=====================================================================
#左侧菜单
att_module=Посещаемость
#左侧菜单-考勤设备
att_leftMenu_attendanceDevice=Управление устройством
att_leftMenu_device=устройство посещаемости
att_leftMenu_point=Точки учёта
att_leftMenu_sign_address=Адрес прихода в приложении
att_leftMenu_adms_devCmd=Сервер выдал команду
#左侧菜单-基础信息
att_leftMenu_basicInformation=Основная информация
att_leftMenu_rule=Правила
att_leftMenu_base_rule=Правила посещения
att_leftMenu_department_rule=Правила отдела
att_leftMenu_holiday=Праздники
att_leftMenu_leaveType=Вид отпуска
att_leftMenu_timingCalculation=Расчёт времени
att_leftMenu_autoExport=Сообщить о толчке
att_leftMenu_param=Настройка параметров
#左侧菜单-班次管理
att_leftMenu_shiftManagement=Рабочая смена
att_leftMenu_timeSlot=Расписание
att_leftMenu_shift=Смена
#左侧菜单-排班管理
att_leftMenu_scheduleManagement=График
att_leftMenu_group=Группа
att_leftMenu_groupPerson=Участник
att_leftMenu_groupSch=Групповой график
att_leftMenu_deptSch=График отдела
att_leftMenu_personSch=График сотрудника
att_leftMenu_tempSch=Временный график
att_leftMenu_nonSch=Сотрудники вне графика
#左侧菜单-异常管理
att_leftMenu_exceptionManagement=Управление исключениями посещаемости
att_leftMenu_sign=Регистрация прихода
att_leftMenu_leave=Отпуск
att_leftMenu_trip=Командировка
att_leftMenu_out=Выход
att_leftMenu_overtime=Сверхурочная работа
att_leftMenu_adjust=Корректировка и добавление
att_leftMenu_class=Корректировка смены
#左侧菜单-统计报表
att_leftMenu_statisticalReport=Отчет статистики посещаемости
att_leftMenu_manualCalculation=Рассчёт вручную
att_leftMenu_transaction=События
att_leftMenu_dayCardDetailReport=Посещения за день
att_leftMenu_leaveSummaryReport=Отчёт по отпускам
att_leftMenu_dayDetailReport=Отчёт за день
att_leftMenu_monthDetailReport=Подробный отчёт за месяц
att_leftMenu_monthStatisticalReport=Ежемесячный отчет о персонала
att_leftMenu_deptStatisticalReport=Ежемесячный ведомственный отчет
att_leftMenu_yearStatisticalReport=Отчёт за год
att_leftMenu_attSignCallRollReport=Войти перекличка
att_leftMenu_workTimeReport=Отчет о рабочем времени
#左侧菜单-设备操作日志
att_leftMenu_device_op_log=Журнал операций устройства
#左侧菜单-实时点名
att_leftMenu_realTime_callRoll=Перекличка
#=====================================================================
#公共
att_common_person=Сотрудник
att_common_pin=ID
att_common_group=Группа
att_common_dept=Отдел
att_common_symbol=Символ
att_common_deptNo=Номер отдела
att_common_deptName=Название отдела
att_common_groupNo=Номер группы
att_common_groupName=Имя группы
att_common_operateTime=Время операции
att_common_operationFailed=Сбой операции
att_common_id=ID
att_common_deptId=ID отдела
att_common_groupId=ID группы
att_common_deviceId=ID устройства
att_person_pin=ID сотрудника
att_person_name=Имя
att_person_lastName=Фамилия
att_person_internalCard=Номер карты
att_person_attendanceMode=Время и режим посещаемости
att_person_normalAttendance=Нормальная посещаемость
att_person_noPunchCard=Нет перфокарты
att_common_attendance=Посещаемость
att_common_attendance_hour=Посещаемость (часы)
att_common_attendance_day=Посещаемость (дни)
att_common_late=Опоздание
att_common_early=Ранний уход
att_common_overtime=Сверхурочная работа
att_common_exception=Исключение
att_common_absent=Отсутствие
att_common_leave=Отпуск
att_common_trip=Командировка
att_common_out=Выход
att_common_staff=Работник
att_common_superadmin=Суперпользователь
att_common_msg=Содержание SMS
att_common_min=Длительность сообщения(минуты)
att_common_letterNumber=Можно вводить только цифры или буквы!
att_common_relationDataCanNotDel=Связанные данные не могут быть удалены.
att_common_relationDataCanNotEdit=Связанные данные не могут быть изменены.
att_common_needSelectOneArea=Пожалуйста, выберите зону!
att_common_neesSelectPerson=Пожалуйста, выберите сотрудника!
att_common_nameNoSpace=Имя не может содержать пробелы!
att_common_digitsValid=Можно вводить только цифры, до двух десятичных знаков после запятой!
att_common_numValid=Вводить только цифры!
#=====================================================================
#工作面板
att_dashboard_worker=Трудоголик
att_dashboard_today=Посещаемость сегодня
att_dashboard_todayCount=Статистика посещаемости за сегодня
att_dashboard_exceptionCount=Статистика нарушений (в этом месяце)
att_dashboard_lastWeek=Прошлая неделя
att_dashboard_lastMonth=Прошлый месяц
att_dashboard_perpsonNumber=Всего
att_dashboard_actualNumber=Присутствующие
att_dashboard_notArrivedNumber=Отсутствующие
att_dashboard_attHour=Рабочее время
#区域
#设备
att_op_syncDev=Синхронизация данных ПО с устройством
att_op_account=Проверка данных учёта регистрации
att_op_check=Загрузить данные снова
att_op_deleteCmd=Очистить команды устройства
att_op_dataSms=Публичные сообщения
att_op_clearAttPic=Очистить фотографии регистрации учёта
att_op_clearAttLog=Очистить события регистрации учёта
att_device_waitCmdCount=Команды для выполнения
att_device_status=Включенное состояние
att_device_register=Устройство регистрации
att_device_isRegister=Устройство регистрации
att_device_existNotRegDevice=Не устройство регистрации, нельзя получить данные!
att_device_fwVersion=Версия прошивки
att_device_transInterval=Период обновления (мин.)
att_device_cmdCount=Максимальное количество команд для связи с сервером.
att_device_delay=Запрос времени записи (секунды)
att_device_timeZone=Расписание
att_device_operationLog=Журнал событий
att_device_registeredFingerprint=Зарегистрировать от. пальца
att_device_registeredUser=Зарегистрировать сотрудника
att_device_fingerprintImage=Изображение от. пальца
att_device_editUser=Редактировать сотрудника
att_device_modifyFingerprint=Изменить от. пальца
att_device_faceRegistration=Зарегистрировать лицо
att_device_userPhotos=Фото сотрудника
att_device_attLog=Нужно ли загружать записи регистрации учёта
att_device_operLog=Нужно ли загружать информацию о сотрудниках
att_device_attPhoto=Нужно ли загружать фотографии регистрации учёта
att_device_isOnLine=Состояние в сети
att_device_InputPin=Введите номер сотрудника
att_device_getPin=Получить данные указанного сотрудника
att_device_separatedPin=Несколько номеров сотрудников, разделенных запятыми
att_device_authDevice=Авторизованные устройства
att_device_disabled=Следующие устройства отключены и не могут работать!
att_device_autoAdd=Новое оборудование добавляется автоматически
att_device_receivePersonOnlyDb=Получение только данных персонала, присутствующего в базе данных
att_devMenu_control=Управление устройством
att_devMenu_viewOrGetInfo=Просмотр и получение информации
att_devMenu_clearData=Очистить данные устройства
att_device_disabledOrOffline=Устройство выключено или вне сети, нельзя управлять!
att_device_areaStatus=Состояние области устройства
att_device_areaCommon=Регион нормальный
att_device_areaEmpty=Область пуста
att_device_isRegDev=Изменение часового пояса или состояния регистратора требует перезагрузки устройства для вступления в силу!
att_device_canUpgrade=Следующие устройства могут быть обновлены
att_device_offline=Следующие устройства находятся в автономном режиме и не могут работать!
att_device_oldProtocol=Старый протокол
att_device_newProtocol=Новый протокол
att_device_noMoreTwenty=Старый пакет обновления прошивки устройства протокола не может превышать 20M
att_device_transferFilesTip=Прошивка обнаружена успешно, передача файлов
att_op_clearAttPers=Очистить персонал оборудования
#区域人员
att_op_forZoneAddPers=Настройка зоны сотрудников
att_op_dataUserSms=Личное сообщение
att_op_syncPers=Ресинхронизация с устройством
att_areaPerson_choiceArea=Пожалуйста, выберите зону!
att_areaPerson_byAreaPerson=По регионам
att_areaPerson_setByAreaPerson=Установлено местным персоналом
att_areaPerson_importBatchDel=Импорт массового удаления
att_areaPerson_syncToDevSuccess=Успешная операция! Пожалуйста, дождитесь отправки команды.
att_areaPerson_personId=ID сотрудника
att_areaPerson_areaId=ID зоны
att_area_existPerson=В зоне есть сотрудники!
att_areaPerson_notice1=Зона или сотрудники не могут быть пустыми одновременно!
att_areaPerson_notice2=Нет запрошенных сотрудников или зон!
att_areaPerson_notice3=Не найдены устройства внутри зоны!
att_areaPerson_addArea=Добавить зону
att_areaPerson_delArea=Удалить зону
att_areaPerson_persNoExit=Человек не существует
att_areaPerson_importTip1=Пожалуйста, убедитесь, что импортированный человек уже существует в модуле персонала
att_areaPerson_importTip2=Импортеры партий не будут автоматически доставлены на устройство и должны быть синхронизированы вручную
att_areaPerson_addAreaPerson=Добавить региональных сотрудников
att_areaPerson_delAreaPerson=Удалить персонал области
att_areaPerson_importDelAreaPerson=Импорт и удаление персонала области
att_areaPerson_importAreaPerson=Ввоз персонала
#考勤点
att_attPoint_name=Название точки учёта
att_attPoint_list=Список точек учёта
att_attPoint_deviceModule=Модуль устройства
att_attPoint_acc=Доступ
att_attPoint_park=Автостоянка
att_attPoint_ins=Экран информации
att_attPoint_pid=Документ сотрудника
att_attPoint_vms=виде
att_attPoint_psg=ряд
att_attPoint_doorList=Список дверей
att_attPoint_deviceList=Список устройств
att_attPoint_channelList=Список каналов
att_attPoint_gateList=Список ворот
att_attPoint_recordTypeList=Тип записи по запросу
att_attPoint_door=Пожалуйста, выберите соответствующую дверь.
att_attPoint_device=Пожалуйста, выберите соответствующее устройство.
att_attPoint_gate=Пожалуйста, выберите соответствующие ворота
att_attPoint_normalPassRecord=Нормальный проходной рекорд
att_attPoint_verificationRecord=Контрольная запись
att_person_attSet=Настройки учёта регистрации
att_attPoint_point=Пожалуйста, выберите точку регистрации учёта.
att_attPoint_count=Недостаточно лицензий авторизованных точек учёта; операция не удалась!
att_attPoint_notSelect=Модуль не настроен
att_attPoint_accInsufficientPoints=Неправильные точки регистрации учёта для событий контроля доступа!
att_attPoint_parkInsufficientPoints=Неправильные точки регистрации учёта для событий автостоянки!
att_attPoint_insInsufficientPoints=Не хватает точек учёта регистрации информационного экрана!
att_attPoint_pidInsufficientPoints=Карты недостаточно для учёта регистрации!
att_attPoint_doorOrParkDeviceName=Название двери или оборудования автостоянки
att_attPoint_vmsInsufficientPoints=Видео при недостаточной посещаемости!
att_attPoint_psgInsufficientPoints=На канале недостаточно очков посещаемости!
att_attPoint_delDevFail=Не удалось удалить устройство, оно использовалось в это время
att_attPoint_pullingRecord=Точка посещаемости регулярно получает записи, пожалуйста, подождите!
att_attPoint_lastTransactionTime=Время последнего получения данных
att_attPoint_masterDevice=Главное устройство
att_attPoint_channelName=Название канала
att_attPoint_cameraName=Название камеры
att_attPoint_cameraIP=ip камеры
att_attPoint_channelIP=IP-адрес канала
att_attPoint_gateNumber=Номер ворот
att_attPoint_gateName=Название ворот
#APP考勤签到地址
att_signAddress_address=Адрес
att_signAddress_longitude=Долгота
att_signAddress_latitude=Широта
att_signAddress_range=Диапазон действия
att_signAddress_rangeUnit=Ед. измерения в метрах (м)
#=====================================================================
#规则
att_rule_baseRuleSet=Настройки основных правил
att_rule_countConvertSet=Настройки расчёта
att_rule_otherSet=Другие настройки
att_rule_baseRuleSignIn=Правила учёта прихода
att_rule_baseRuleSignOut=Правила учёта ухода
att_rule_earliestPrinciple=Наиболее раннее событие
att_rule_theLatestPrinciple=Наиболее позднее событие
att_rule_principleOfProximity=Наиболее близкое событие
att_rule_baseRuleShortestMinutes=Мин. период времени должен быть больше (минимум 10 минут)
att_rule_baseRuleLongestMinutes=Макс. период времени должен быть меньше (максимум 1440 минут)
att_rule_baseRuleLateAndEarly=Поздний и ранний уход считать отсутствием
att_rule_baseRuleCountOvertime=Статистика сверхурочной работы
att_rule_baseRuleFindSchSort=Поиск записей смены
att_rule_groupGreaterThanDepartment=Группа -> Отдел
att_rule_departmentGreaterThanGroup=Отдел -> Группа
att_rule_baseRuleSmartFindClass=Правило интеллектуального подсчёта смен
att_rule_timeLongest=Максимальная длительность работы
att_rule_exceptionLeast=Меньше нарушений
att_rule_baseRuleCrossDay=Расчёт учёта регистрации при переходе смены на следующие сутки
att_rule_firstDay=Первый день
att_rule_secondDay=Второй день
att_rule_baseRuleShortestOvertimeMinutes=Мин. сверхурочное время(минуты)
att_rule_baseRuleMaxOvertimeMinutes=Макс. сверхурочное время (минуты)
att_rule_baseRuleElasticCal=Гибкий расчёт времени
att_rule_baseRuleTwoPunch=Подсчёт между каждыми двумя считываниями
att_rule_baseRuleStartEnd=Подсчёт между первым и последним считыванием
att_rule_countConvertHour=Правило преобразования времени
att_rule_formulaHour=Формула: Часы=
att_rule_countConvertDay=Правило преобразования дней
att_rule_formulaDay=Формула：Дни =
att_rule_inFormulaShallPrevail=Забирать результат, рассчитанный по формуле, в качестве стандарта;
att_rule_remainderHour=Остаток больше или равен
att_rule_oneHour=Рассчитывается как час, в противном случае рассчитывается как полчаса или игнорируется;
att_rule_halfAnHour=Рассчитывается как полчаса, в противном случае игнорируется;
att_rule_remainderDay=Коэффициент больше или равен рабочим минутам
att_rule_oneDay=%, рассчитывается как один день, в противном случае рассчитывается как полдня или игнорируется;
att_rule_halfAnDay=%, рассчитывается как полдня, в противном случае игнорируется;
att_rule_countConvertAbsentDay=Правило конвертации в дни отсутствия
att_rule_markWorkingDays=Рассчитывается как рабочие дни
att_rule_countConvertDecimal=Точные цифры после запятой
att_rule_otherSymbol=Настройка символов результата регистрации учёта в отчёте
att_rule_arrive=Ожидаемый / Фактический
att_rule_noSignIn=Нет учёта прихода
att_rule_noSignOff=Нет учёта ухода
att_rule_off=Скоррек. отдых
att_rule_class=Добав. учёт регистрации
att_rule_shortLessLong=Расписание рабочего времени не может быть длиннее, чем самое продолжительное расписание доступа.
att_rule_symbolsWarning=Вы должны настроить символ в отчете учёта рабочего времени!
att_rule_reportSettingSet=Настройки экспорта отчётов
att_rule_shortDateFormat=Формат даты
att_rule_shortTimeFormat=Формат времени
att_rule_baseRuleSignBreakTime=Независимо от часов в перерыве
att_leftMenu_custom_rule=Пользовательское правило
att_custom_rule_already_exist=Пользовательские правила {0} уже существуют!
att_add_group_custom_rule=Добавьте групповые правила
att_custom_rule_type=Тип правила
att_rule_type_group=Групповые правила
att_rule_type_dept=Правила отдела
att_custom_rule_orgNames=Использовать объект
att_rult_maxOverTimeType1=Без ограничений
att_rult_maxOverTimeType2=Эта неделя
att_rult_maxOverTimeType3=Этот месяц
att_rule_countConvertDayRemark1=Пример: эффективное рабочее время составляет 500 минут, а рабочее время должно составлять 480 минут в день, результат равен 500/480 = 1,04, а последнее десятичное число равно 1,0
att_rule_countConvertDayRemark2=Пример: эффективное рабочее время составляет 500 минут, а рабочее время должно составлять 480 минут в день, тогда результат 500/480 = 1,04, 1,04> 0,8 рассчитывается как один день
att_rule_countConvertDayRemark3=Пример: эффективное рабочее время составляет 300 минут, а рабочее время должно составлять 480 минут в день, Тогда результат 300/480 = 0,625, 0,2 <0,625 <0,8 - полдня
att_rule_countConvertDayRemark4=Уровень пересчёта дня: в период времени «Считать как рабочие дни» не работает;
att_rule_countConvertDayRemark5=Записывается как количество рабочих дней: ограничено подсчетом окончания количества дней, и, в каждом периоде есть окончание, продолжительность окончания рассчитывается в соответствии с количеством рабочих дней в периоде;
att_rule_baseRuleSmartFindRemark1=Самое продолжительное время: в соответствии с картой посещаемости дня, рассчитывается рабочее время, соответствующее каждой смене в день, и находит смену самого длинного рабочего времени дня;
att_rule_baseRuleSmartFindRemark2=Минимум отклонений: рассчитывает количество отклонений времени, соответствующих каждой смене в день, в соответствии с картой посещаемости дня и находит смену с наименьшим количеством отклонений в день для расчета рабочего времени;
att_rule_baseRuleHourValidator=Оценка минуты получаса не может быть больше или равна 1 часу!
att_rule_baseRuleDayValidator=Период оценки в полдня не может быть больше или равен периоду оценки в один день!
att_rule_overtimeWarning=Максимальная продолжительность сверхурочной работы не может быть меньше минимальной минимальной продолжительности сверхурочной работы!
att_rule_noSignInCountType=Незарегистрированная регистрация
att_rule_absent=отсутств
att_rule_earlyLeave=Уйти раньше
att_rule_noSignOffCountType=Не подписано
att_rule_minutes=минут
att_rule_noSignInCountLateMinute=Незарегистрированный вход записывается как запоздалый
att_rule_noSignOffCountEarlyMinute=Неподписанное отступление-это отсчет времени до вылета
att_rule_incomplete=Неполный.
att_rule_noCheckInIncomplete=Не вошли как неполные
att_rule_noCheckOutIncomplete=Не подписан как неполный
att_rule_lateMinuteWarning=Незарегистрированные будут записываться, так как поздние минуты должны быть больше 0 и меньше самого длинного периода посещаемости
att_rule_earlyMinuteWarning=Не отмечен для минут раннего отъезда должен быть больше 0 и меньше, чем самый длинный период посещаемости
att_rule_baseRuleNoSignInCountLateMinuteRemark=Если не зарегистрирован, считается как поздно, если не зарегистрирован, это считается как поздно для N минут
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=Когда не прекращение подписывается, записывается как досрочный отъезд, если не прекращается, оно записывается как преждевременный отъезд на N минут
#节假日
att_holiday_placeholderNo=Рекомендуется начинать с Пр, например, с Пр01.
att_holiday_placeholderName=Советуем называть в формате [Год] + [Назв. праздника] например. [2017 День труда].
att_holiday_dayNumber=Количество дней
att_holiday_validDate_msg=Праздники в это время
#假种
att_leaveType_leaveThing=Внеплановый отпуск
att_leaveType_leaveMarriage=Отпуск в связи с вступлением в брак
att_leaveType_leaveBirth=Отпуск по беременности и родам
att_leaveType_leaveSick=Отпуск по болезни
att_leaveType_leaveAnnual=Ежегодный трудовой отпуск
att_leaveType_leaveFuneral=Отпуск в связи со смертью члена семьи
att_leaveType_leaveHome=Отпуск для поездки домой
att_leaveType_leaveNursing=Отпуск по уходу за ребёнком
att_leaveType_isDeductWorkLong=Задержка рабочего времени
att_leaveType_placeholderNo=Рекомендуется начинать с У, например, с У1.
att_leaveType_placeholderName=Рекомендуется заканчивать «праздником», например, День свадьбы.
#定时计算
att_timingcalc_timeCalcFrequency=Частота расчета
att_timingcalc_timeCalcInterval=Время расчёта
att_timingcalc_timeSet=Настройки времени расчёта
att_timingcalc_timeSelect=Пожалуйста, укажите время!
att_timingcalc_optionTip=Должен быть сохранен, по крайней мере, один действительный ежедневный расчет посещаемости.
#自动导出报表
att_autoExport_reportType=Тип отчёта
att_autoExport_fileType=Тип файла
att_autoExport_fileName=Имя файла
att_autoExport_fileDateFormat=Формат даты
att_autoExport_fileTimeFormat=Формат времени
att_autoExport_fileContentFormat=Формат данных отчета
att_autoExport_fileContentFormatTxt=Пример: {НазвОтдл}00{IDсотруд}01{Имясотрд}02{учётвремядата}03
att_autoExport_timeSendFrequency=Периодичность отправки
att_autoExport_timeSendInterval=Интервал отправки
att_autoExport_emailType=Тип получателя почты
att_autoExport_emailRecipients=Получатель почты
att_autoExport_emailAddress=Эл. адрес почты
att_autoExport_emailExample=Пример:<EMAIL>,<EMAIL>
att_autoExport_emailSubject=Заголовок письма
att_autoExport_emailContent=Тело письма
att_autoExport_field=Поля
att_autoExport_fieldName=Название поля
att_autoExport_fieldCode=Номер поля
att_autoExport_reportSet=Настройки отчета
att_autoExport_timeSet=Параметры доставки почты
att_autoExport_emailSet=Настройки почты
att_autoExport_emailSetAlert=Пожалуйста, введите ваш адрес эл. почты
att_autoExport_emailTypeSet=Настройки получателя
att_autoExport_byDay=Каждый день
att_autoExport_byMonth=Каждый месяц
att_autoExport_byPersonSet=По сотруднику
att_autoExport_byDeptSet=По отделу
att_autoExport_byAreaSet=По зоне
att_autoExport_emailSubjectSet=Заголовок
att_autoExport_emailContentSet=Содержание письма
att_autoExport_timePointAlert=Пожалуйста, выберите правильный момент отправки.
att_autoExport_lastDayofMonth=Последний день месяца
att_autoExport_firstDayofMonth=Первый день месяца
att_autoExport_dayofMonthCheck=Конкретная дата
att_autoExport_dayofMonthCheckAlert=Пожалуйста, выберите конкретную дату.
att_autoExport_chooseDeptAlert=Пожалуйста, выберите отдел!
att_autoExport_sendFormatSet=Настройка режима отправки
att_autoExport_sendFormat=Режим отправки
att_autoExport_mailFormat=Почт. ящик спос. доставки
att_autoExport_ftpFormat=Метод отправки по FTP
att_autoExport_sftpFormat=Метод отправки по SFTP
att_autoExport_ftpUrl=Адрес FTP-сервера
att_autoExport_ftpPort=Порт FTP-сервера
att_autoExport_ftpTimeSet=Настройка времени отп. по FTP
att_autoExport_ftpParamSet=Настройка параметров FTP
att_autoExport_ftpUsername=Имя пользователя FTP
att_autoExport_ftpPassword=Пароль FTP
att_autoExport_correctFtpParam=Пожалуйста, заполните параметры FTP правильно
att_autoExport_correctFtpTestParam=Пожалуйста, проверьте соединение, чтобы убедиться, что связь с нормальная
att_autoExport_inputFtpUrl=Пожалуйста, введите адрес сервера
att_autoExport_inputFtpPort=Пожалуйста, введите порт сервера
att_autoExport_ftpSuccess=Соединение установлено
att_autoExport_ftpFail=Пожалуйста, проверьте правильность настроек параметров.
att_autoExport_validFtp=Пожалуйста, введите действительный адрес сервера
att_autoExport_validPort=Пожалуйста, введите действительный порт сервера
att_autoExport_selectExcelTip=Выберите тип файла EXCEL, формат содержимого - все поля!
#=====================================================================
#时间段
att_timeSlot_periodType=Тип расписания
att_timeSlot_normalTime=Нормальное расписание
att_timeSlot_elasticTime=Плавающее расписание
att_timeSlot_startSignInTime=Начало времени учёта прихода
att_timeSlot_toWorkTime=Время учёта прихода
att_timeSlot_endSignInTime=Окончание времени учёта прихода
att_timeSlot_allowLateMinutes=Разрешить опоздание(минуты)
att_timeSlot_isMustSignIn=Необходимо отмечать приход
att_timeSlot_startSignOffTime=Начало времени учёта ухода
att_timeSlot_offWorkTime=Время учёта ухода
att_timeSlot_endSignOffTime=Окончание времени учёта ухода
att_timeSlot_allowEarlyMinutes=Разрешить ранний уход(минуты)
att_timeSlot_isMustSignOff=Необходимо отмечать уход
att_timeSlot_workingHours=Рабочее время(минуты)
att_timeSlot_isSegmentDeduction=Авто вычитать время перерыва
att_timeSlot_startSegmentTime=Время начала
att_timeSlot_endSegmentTime=Время окончания
att_timeSlot_interSegmentDeduction=Вычтенное время (минуты)
att_timeSlot_markWorkingDays=Рабочий день
att_timeSlot_isAdvanceCountOvertime=Авто сверхурочные (учёт раннего прихода)
att_timeSlot_signInAdvanceTime=Авто сверхурочные время окончания (учёт прихода)
att_timeSlot_isPostponeCountOvertime=Авто сверхурочные (учёт позднего ухода)
att_timeSlot_signOutPosponeTime=Авто сверхурочные время начала (учёт ухода)
att_timeSlot_isCountOvertime=Рассчитано как сверхурочные
att_timeSlot_timeSlotLong=Диапазон расписания:
att_timeSlot_alertStartSignInTime=Время начала учёта прихода должно быть меньше времени учёта прихода.
att_timeSlot_alertEndSignInTime=Время окончания учёта прихода должно быть больше времени учёта прихода.
att_timeSlot_alertStartSignInAndEndSignIn=Время начала учёта ухода должно быть меньше времени учёта ухода.
att_timeSlot_alertStartSignOffTime=Время начала сверхурочной работы не может быть меньше времени учёта ухода.
att_timeSlot_alertEndSignOffTime=Время начала сверхурочной работы не может быть больше времени окончания учёта ухода.
att_timeSlot_alertStartUnequalEnd=Рабочих дней не может быть меньше 0.
att_timeSlot_alertStartSegmentTime=Вычитаемое время не может быть меньше 0.
att_timeSlot_alertStartAndEndTime=Время начала учёта ухода не может быть равно времени окончания учёта прихода.
att_timeSlot_alertEndAndoffWorkTime=Часы не могут быть больше 23.
att_timeSlot_alertToWorkLesserAndEndTimeLesser=Минуты не могут быть больше 59.
att_timeSlot_alertLessSignInAdvanceTime=Время учёта раннего прихода должно быть меньше времени начала работы
att_timeSlot_alertMoreSignInAdvanceTime=Время учёта раннего прихода должно быть больше времени начала учёта прихода
att_timeSlot_alertMoreSignOutPosponeTime=Время учёта позднего ухода должно быть меньше времени окончания учёта ухода
att_timeSlot_alertLessSignOutPosponeTime=Время учёта позднего ухода должно быть больше времени окончания работы
att_timeSlot_time=Пожалуйста, введите правильный формат времени.
att_timeSlot_alertMarkWorkingDays=Помните, что количество рабочих дней не может быть пустым!
att_timeSlot_placeholderNo=Рекомендуется начинать с Р, например, Р01.
att_timeSlot_placeholderName=Рекомендуется начинать с буквы Р, или заканчивать «расписанием».
att_timeSlot_beforeToWork=перед приходом на работу
att_timeSlot_afterToWork=после работы
att_timeSlot_beforeOffWork=перед уходом с работы
att_timeSlot_afterOffWork=после работы
att_timeSlot_minutesSignInValid=Приход действителен в течение минут
att_timeSlot_toWork=на работе
att_timeSlot_offWork=вне работы
att_timeSlot_minutesSignInAsOvertime=Вход для сверхурочного времени (минуты)
att_timeSlot_minutesSignOutAsOvertime=Начать считать сверхурочные после (минуты)
att_timeSlot_minOvertimeMinutes=Минимальные сверхурочные минуты
att_timeSlot_enableWorkingHours=Внезависимости вкл. рабочие часы
att_timeSlot_eidtTimeSlot=Изм. время
att_timeSlot_browseBreakTime=Просмотр периодов отдыха
att_timeSlot_addBreakTime=Доб. перерывы
att_timeSlot_enableFlexibleWork=Вкл. плавающую работу
att_timeSlot_advanceWorkMinutes=Можно быть на работе заранее
att_timeSlot_delayedWorkMinutes=Можно отложить работу
att_timeSlot_advanceWorkMinutesValidMsg1=Значение минут «перед приходом на работу» больше, чем значение минут, которое «можно быть на работе заранее»
att_timeSlot_advanceWorkMinutesValidMsg2=Значение минут «можно быть на работе заранее», меньше, чем значение минут «перед приходом на работу»
att_timeSlot_advanceWorkMinutesValidMsg3=Значение минут, которые могут быть «обработаны заранее», меньше или равно значению минут «вход для сверхурочного времени».
att_timeSlot_advanceWorkMinutesValidMsg4=Значение минут, которое может быть «вход для сверхурочного времени», больше или равно значению минут, «обработаны заранее».
att_timeSlot_delayedWorkMinutesValidMsg1=Значение минут после «после работы» больше, чем значение минут, которое можно отложить работу
att_timeSlot_delayedWorkMinutesValidMsg2=Значение минут для «можно отложить работу» меньше, чем количество минут после «после работы»
att_timeSlot_delayedWorkMinutesValidMsg3=Значение минут, которое можно «запланированная работа», меньше или равно количеству минут после «после работы», «выхода из системы» и «вход для сверхурочного времени»
att_timeSlot_delayedWorkMinutesValidMsg4=Значение минут, которое может быть «после работы», «выхода из системы» и «вход для сверхурочного времени», больше или равно значению минут после «запланированная работа»
att_timeSlot_allowLateMinutesValidMsg1=Значение минут, на которые можно опоздать, меньше количества минут после работы.
att_timeSlot_allowLateMinutesValidMsg2=Значение минут после «после работы» больше, чем значение минут, допустимых для «опоздания»
att_timeSlot_allowEarlyMinutesValidMsg1=Разрешенные ранние минуты меньше, чем минут до работы
att_timeSlot_allowEarlyMinutesValidMsg2=Значение минут до «перед началом работы» больше, чем количество минут до «оставленных минут»
att_timeSlot_timeOverlap=Время отдыха перекрывается, пожалуйста, измените период отдыха!
att_timeSlot_atLeastOne=Минимум 1 период отдыха!
att_timeSlot_mostThree=До 3-х периодов отдыха!
att_timeSlot_canNotEqual=Время начала периода отдыха не может быть равно времени окончания!
att_timeSlot_shoudInWorkTime=Пожалуйста, убедитесь, что период отдыха в рабочее время!
att_timeSlot_repeatBreakTime=Повторите период отдыха!
att_timeSlot_toWorkLe=Рабочее время меньше минимального времени начала выбранного периода отдыха:
att_timeSlot_offWorkGe=Нерабочее время больше максимального времени окончания выбранного периода отдыха:
att_timeSlot_crossDays_toWork=Минимальное время начала перерыва находится в пределах периода времени:
att_timeSlot_crossDays_offWork=Максимальное время окончания отдыха находится в пределах периода времени:
att_timeSlot_allowLateMinutesRemark=От «рабочего времени» до «разрешенной поздней минуты» карты для расчета карты нормальной работы
att_timeSlot_allowEarlyMinutesRemark=Начиная рано из «нерабочего времени» по количеству минут, оставленных досрочно, обычная карта вне работы
att_timeSlot_isSegmentDeductionRemark=Удаление периода отдыха в период времени
att_timeSlot_attEnableFlexibleWorkRemark1=Плавающая работа не позволяет устанавливать количество поздних, ранних выходов
att_timeSlot_afterToWorkRemark=Минуты после работы равны минутам «Отложено на работу»
att_timeSlot_beforeOffWorkRemark=До работы минуты равны можно идти на работу минут
att_timeSlot_attEnableFlexibleWorkRemark2=Количество минут после «после работы» больше или равно «нерабочим часам» + «часы с задержкой»
att_timeSlot_attEnableFlexibleWorkRemark3=Вы можете работать заранее, минуты должны быть меньше или равны «Рабочим N минутам сверхурочной работы».
att_timeSlot_attEnableFlexibleWorkRemark4=Отложено на рабочие минуты, должно быть меньше или равно «N минутам без работы»
att_timeSlot_attBeforeToWorkAsOvertimeRemark=Пример: класс 9: 00, вход на работу сверхурочно за 60 минут до работы, тогда отметьтесь до 8:00 сверхурочно.
att_timeSlot_attAfterOffWorkAsOvertimeRemark=Пример: после 18 часов, после 60 минут работы, подпишите снятие и сверхурочную работу, затем начните сверхурочно с 19 часов до времени ухода.
att_timeSlot_longTimeValidRemark=Эффективное время подписания «после работы» и эффективное время подписания «до работы» не может пересекаться во времени!
att_timeSlot_advanceWorkMinutesValidMsg5=Количество действительных минут до регистрации должно быть больше, чем количество минут, которое можно обработать заранее
att_timeSlot_advanceWorkMinutesValidMsg6=Количество минут, которое вы можете отправиться на работу заранее, меньше количества действительных минут до регистрации
att_timeSlot_delayedWorkMinutesValidMsg5=Количество действительных минут после регистрации больше, чем количество минут, которое можно отложить на работу
att_timeSlot_delayedWorkMinutesValidMsg6=Количество минут, которые можно отложить на работу, меньше количества действительных минут после входа в систему для работы
att_timeSlot_advanceWorkMinutesValidMsg7=Время заезда до работы не может совпадать с временем выезда после работы накануне
att_timeSlot_delayedWorkMinutesValidMsg7=Время выезда после работы не может совпадать со временем выезда до работы на следующий день
att_timeSlot_maxOvertimeMinutes=Ограничить максимальное количество сверхурочных часов
#班次
att_shift_basicSet=Тип графика
att_shift_advancedSet=Название графика
att_shift_type=Тип смены
att_shift_name=Название смены
att_shift_regularShift=Обычная смена
att_shift_flexibleShift=Плавающая смена
att_shift_color=Цвет
att_shift_periodicUnit=Тип
att_shift_periodNumber=Цикл
att_shift_startDate=Дата начала
att_shift_startDate_firstDay=Дата начала цикла
att_shift_isShiftWithinMonth=Цикл смены за один месяц
att_shift_attendanceMode=Режим регистрации учёта
att_shift_shiftNormal=Регистрация учёта в соответствии с обычной сменой
att_shift_oneDayOneCard=Регистрация учёта один раз в день в любое время
att_shift_onlyBrushTime=Рассчитывать только время регистрации
att_shift_notBrushCard=Свободная регистрация
att_shift_overtimeMode=Сверхурочный режим
att_shift_autoCalc=Автоматический компьютерный расчет
att_shift_mustApply=Сверхурочная работа должна подтверждаться
att_shift_mustOvertime=Обязан работать сверхурочно или прогул
att_shift_timeSmaller=Наиболее короткое время между авто расчетом и получением сверхурочного листа
att_shift_notOvertime=Не рассчитывать, как сверхурочные
att_shift_overtimeSign=Тип сверхурочной работы
att_shift_normal=Обычный день
att_shift_restday=Выходной день
att_shift_timeSlotDetail=Детали расписания
att_shift_doubleDeleteTimeSlot=Дважды щелкните на период смены; вы можете удалить период времени
att_shift_addTimeSlot=Добавить расписание
att_shift_cleanTimeSlot=Очистить расписание
att_shift_NO=Нет
att_shift_notAll=Снять всё
att_shift_notTime=Если флажок подробностей о расписании не может быть выбран, это означает, что в расписании есть наложения
att_shift_notExistTime=Это расписание не существует.
att_shift_cleanAllTimeSlot=Вы уверены, что хотите очистить расписание для выбранной смены?
att_shift_pleaseCheckBox=Пожалуйста, установите флажок слева, который совпадает с текущим отображения справа.
att_shift_pleaseUnit=Пожалуйста, заполните единицы и количество циклов.
att_shift_pleaseAllDetailTimeSlot=Пожалуйста, выберите детали расписания.
att_shift_placeholderNo=Рекомендуется начинать с С, например, с С0.
att_shift_placeholderName=Рекомендуется начинать с С или заканчивать на «Смена».
att_shift_workType=Тип работы
att_shift_normalWork=Обычная работа
att_shift_holidayOt=СУ праздники
att_shift_attShiftStartDateRemark=Пример: начальная дата цикла - № 22 с периодом в три дня, затем № 22/23/24 для класса A / B / C, а № 19/20/21 для класса A. / B класс / C класс, до и после даты и так далее.
att_shift_isShiftWithinMonthRemark1=Смена внутри месяца, цикл только до последнего дня каждого месяца, не запланированная переходить в другой месяц;
att_shift_isShiftWithinMonthRemark2=Немесячная смена, циклически повторяется до последнего дня каждого месяца, если цикл не завершен, продолжается в следующем месяце и т. д .;
att_shift_workTypeRemark1=Примечание: если в качестве типа работы выбрана сверхурочная работа в выходной день, посещаемость не будет рассчитываться в выходной день.
att_shift_workTypeRemark2=СУ выходные, отмечены сверхурочной работы по умолчанию соответствует выходному дню, и ПО автоматически рассчитывает сверхурочную работу. Не требуется применять сверхурочные. Рабочее время дня учитывается как сверхурочные часы, а посещаемость не учитывается в праздничные дни.
att_shift_workTypeRemark3=СУ выходные, отметка сверхурочного времени по умолчанию соответствует праздничным дням, и ПО автоматически рассчитывает сверхурочное время, применение сверхурочного времени не требуется, а рабочее время дня записывается как сверхурочное время;
att_shift_attendanceModeRemark1=За исключением «обычного смещения по смене», сверхурочные не считаются ранними или отложенными, например:
att_shift_attendanceModeRemark2=1. Регистрация не требуется, или действующая карта используется один раз в день, сверхурочные часы не учитываются;
att_shift_attendanceModeRemark3=2. Тип работы: обычная работа, режим посещаемости: свободное считывание карт, тогда время дневной смены считается действительным рабочим временем;
att_shift_periodStartMode=тип начала периода
att_shift_periodStartModeByPeriod=дата начала периода
att_shift_periodStartModeBySch=Дата начала смены
att_shift_addTimeSlotToShift=Следует ли добавлять временной интервал этой смены
#=====================================================================
#分组
att_group_editGroup=Редактировать сотрудников для группы
att_group_browseGroupPerson=Просмотреть сотрудников группы
att_group_list=Список группы
att_group_placeholderNo=Рекомендуется начинать с Г, например, с Г1
att_group_placeholderName=Рекомендуется начинать с Г или заканчивать на «группа».
att_widget_deptHint=Примечание: импортировать всех сотрудников в выбранный отдел
att_widget_searchType=Условия запроса
att_widget_noPerson=Никто не был выбран
#分组排班
#部门排班
att_deptSch_existsDept=В отделе есть смены, и отдел не может быть удален.
#人员排班
att_personSch_view=Посмотреть расписание сотрудника
#临时排班
att_schedule_type=Тип графика
att_schedule_tempType=Временный тип
att_schedule_normal=Обычный график
att_schedule_intelligent=Интеллектуальный поиск класса
att_tempSch_scheduleType=Тип расписания
att_tempSch_startDate=Время начала
att_tempSch_endDate=Время окончания
att_tempSch_attendanceMode=Метод учёта регистрации
att_tempSch_overtimeMode=Сверхурочный режим
att_tempSch_overtimeRemark=Отметка о сверхурочных
att_tempSch_existsDept=В отделе есть временная смена, и отдел нельзя удалить.
att_schedult_opAddTempSch=Новая временная смена
att_schedule_cleanEndDate=Время окончания очистки
att_schedule_selectOne=Обычный график может добавить только одну смену!
att_schedule_selectPerson=Пожалуйста, сначала выберите сотрудника!
att_schedule_selectDept=Пожалуйста, сначала выберите отдел!
att_schedule_selectGroup=Пожалуйста, сначала выберите группу!
att_schedule_selectOneGroup=Можно выбрать только одну группу!
att_schedule_arrange=Пожалуйста, выберите смену!
att_schedule_leave=Отпуск
att_schedule_trip=Поездка
att_schedule_out=Выход
att_schedule_off=Отдых
att_schedule_makeUpClass=Добавление
att_schedule_class=Корректировка
att_schedule_holiday=Праздник
att_schedule_offDetail=Скоррек. отдых
att_schedule_makeUpClassDetail=Добав. уч. регистрации
att_schedule_classDetail=Скоррек. смену
att_schedule_holidayDetail=Праздник
att_schedule_noSchDetail=Не запланировано
att_schedule_normalDetail=Обычный
att_schedule_normalSchInfo=Центр смены: без перехода на сл. сутки
att_schedule_multipleInterSchInfo=Смены через запятую: несколько смен с переходами на сл. сутки
att_schedule_inderSchFirstDayInfo=Смена со смещением назад: переход смены между днями записывается как первый день
att_schedule_inderSchSecondDayInfo=Смена со смещением вперед: переход смены между днями записывается как второй день
att_schedule_timeConflict=Конфликт с существующим периодом смены, нельзя сохранить!
#=====================================================================
att_excp_notExisetPerson=Сотрудник не существует!
att_excp_leavePerson=Сотрудник в отпуске!
#补签单
att_sign_signTime=Время регистрации учёта
att_sign_signDate=Дата считывания
#请假
att_leave_arilName=Тип отпуска
att_leave_image=Фото на квитанции
att_leave_imageShow=Нет изображений
att_leave_imageType=Совет по ошибке: неправильный формат изображения, поддерживаются: JPEG, GIF, PNG!
att_leave_imageSize=Совет по ошибке: Выбранное изображение слишком велико, максимальный размер 4 Мб!
att_leave_leaveLongDay=Продолжительность (дни)
att_leave_leaveLongHour=Продолжительность (час)
att_leave_leaveLongMinute=Продолжительность (минуты)
att_leave_endNoLessAndEqualStart=Время окончания не может быть меньше или равно времени начала
att_leave_typeNameNoExsists=Поддельное имя класса не существует
att_leave_startNotNull=Время начала не может быть пустым
att_leave_endNotNull=Время окончания не может быть пустым
att_leave_typeNameConflict=Имя поддельного типа конфликтует с именем статуса посещаемости
#出差
att_trip_tripLongDay=Продолжительность поездки (день)
att_trip_tripLongMinute=Продолжительность поездки (минуты)
att_trip_tripLongHour=Время в пути (часы)
#外出
att_out_outLongDay=Продолжительность выхода (день)
att_out_outLongMinute=Продолжительность выхода (минуты)
att_out_outLongHour=Потраченное время (время)
#加班
att_overtime_type=Тип сверхурочной работы
att_overtime_normal=Обычная СУ
att_overtime_rest=СУ на выходных
att_overtime_overtimeLong=Продолжительность СУ работы (минуты)
att_overtime_overtimeHour=Сверхурочные часы (часы)
att_overtime_notice=Заявка на сверхурочную работу не допускается более чем на один день!
att_overtime_minutesNotice=Время подачи сверхурочной работы не может быть меньше минимальной продолжительности сверхурочной работы!
#调休补班
att_adjust_type=Тип корректировки
att_adjust_adjustDate=Дата корректировки
att_adjust_shiftName=Добавить смену регистрации учёта
att_adjust_selectClass=Пожалуйста, выберите смену, которой требуется корректировка регистрации учёта.
att_shift_notExistShiftWorkDate={1} планирование смены на {0}, а остальные не могут подать заявку на косметическую смену!
att_adjust_shiftPeriodStartMode=Сдвиг, выбранный для макияжа, если дата начала смещения, по умолчанию 0
att_adjust_shiftNameNoNull=Смена макияжа не может быть пустой
att_adjust_shiftNameNoExsist=Смена макияжа не существует
#调班
att_class_type=Тип корректировки
att_class_sameTimeMoveShift=Откорректируйте смену сотрудника в этот день
att_class_differenceTimeMoveShift=Откорректируйте смену сотрудника в другие дни
att_class_twoPeopleMove=Обмен двух сотрудников
att_class_moveDate=Дата корректировки
att_class_shiftName=Исходное название графика
att_class_moveShiftName=Новая скорректированная смена не может быть пустой.
att_class_movePersonPin=Скорректируйте ID сотрудника
att_class_movePersonName=Скорректируйте имя сотрудника
att_class_movePersonLastName=Скорректируйте фамилию сотрудника
att_class_moveDeptName=Скорректируйте название отдела
att_class_personPin=ID сотрудника
att_class_shiftNameNoNull=Новая скорректированная смена не может быть пустой.
att_class_personPinNoNull=ID нового сотрудника не может быть пустым!
att_class_isNotExisetSwapPersonPin=Новый скорректированный сотрудник не существует, пожалуйста, добавьте его заново!
att_class_personNoSame=Вы не можете скорректировать для того же сотрудника, пожалуйста, попробуйте еще раз.
att_class_outTime=Дата корректировки и дата перевода не может быть более одного месяца！
att_class_shiftNameNoExsist=Корректировка сдвига не существует
att_class_swapPersonNoExisist=Сваха не существует
att_class_dateNoSame=Физические лица переводятся в разные даты, даты не могут быть одинаковыми
#=====================================================================
#节点
att_node_name=Звено
att_node_type=Тип звена
att_node_leader=Главный узел
att_node_leaderNode=Звено главного узла
att_node_person=Назначенный сотрудник
att_node_position=Назначенная должность
att_node_choose=Выберите должность
att_node_personNoNull=Сотрудники не пусты
att_node_posiitonNoNull=Должность не может быть пустой
att_node_placeholderNo=Рекомендуется начинать с З, например, З01.
att_node_placeholderName=Рекомендуется начинать с должности или имени, добавляя звено, например, как звено менеджера.
att_node_searchPerson=Входные критерии поиска
att_node_positionIsExist=Должность уже существует в данных звена, пожалуйста, выберите должность снова.
#流程
att_flow_type=Тип раб. процесса
att_flow_rule=Правила раб. процесса
att_flow_rule0=Меньше или равно 1 дню
att_flow_rule1=Больше 1 дня и меньше или равно 3 дням
att_flow_rule2=Больше 3 дней и меньше или равно 7 дням
att_flow_rule3=Более 7 дней
att_flow_node=Звенья одобрения
att_flow_start=Начало раб. процесса
att_flow_end=Окончание раб. процесса
att_flow_addNode=Добавить звено
att_flow_placeholderNo=Рекомендуется начинать с П, например, П01.
att_flow_placeholderName=Рекомендуется начинать с типа или названия, добавляя процесс, например, как одобрение отпуска.
att_flow_tips=Примечание. Порядок одобрения звеньев сверху вниз, вы можете отсортировать порядок на ваш выбор.
#申请
att_apply_personPin=ID заявителя
att_apply_type=Тип исключения
att_apply_flowStatus=Общее состояние раб. процесса
att_apply_start=Инициирование заявления
att_apply_flowing=В ожидании
att_apply_pass=Одобрено
att_apply_over=Завершено
att_apply_refuse=Отклонено
att_apply_revoke=Отозвано
att_apply_except=Оправдательные документы
att_apply_view=Просмотреть детали
att_apply_leaveTips=У сотрудника есть заявление на отпуск в этот период!
att_apply_tripTips=У сотрудника есть заявление на командировку в этот период!
att_apply_outTips=Сотрудник подал заявку на выход в течение этого периода времени!
att_apply_overtimeTips=У сотрудников есть заявления на сверхурочную работу в этот период!
att_apply_adjustTips=В этот период у сотрудника есть поданное заявление на изменение!
att_apply_classTips=У сотрудника есть заявление на смену в этот период!
#审批
att_approve_wait=В ожидании одобрения
att_approve_refuse=Не пройдено
att_approve_reason=Причина
att_approve_personPin=ID ответственного
att_approve_personName=Имя ответственного
att_approve_person=Ответственный
att_approve_isPass=Одобрить?
att_approve_status=Текущее состояние звена
att_approve_tips=Время контроля уже существует в раб. процессе и не может быть повторено.
att_approve_tips2=Звено раб. процесса не было настроено, пожалуйста, свяжитесь с администратором для конфигурации.
att_approve_offDayConflicts={0} не запланировано или не запланировано на {1}, а остальное не разрешено.
att_approve_shiftConflicts={0} уже имеет смену в {1} и не позволяет подавать заявку на смену в рабочий день!
att_approve_shiftNoSch={0} Приложение смены не допускается, если смена не запланирована в {1}!
att_approve_classConflicts=Дата графика не является датой смены и не может быть добавлена.
att_approve_selectTime=Время выбора определит процесс по правилам
att_approve_withoutPermissionApproval=Существует рабочий процесс без разрешения на утверждение, пожалуйста, проверьте!
#=====================================================================
#考勤计算
att_op_calculation=Расчёт учёта регистрации
att_op_calculation_notice=Данные учёта рабочего времени были рассчитаны в фоновом режиме, повторите попытку позже!
att_op_calculation_leave=В том числе персонал в отставке
att_statistical_choosePersonOrDept=Пожалуйста, выберите отдел или сотрудника!
att_statistical_sureCalculation=Вы уверены, что хотите выполнить расчёт учёта регистрации?
att_statistical_filter=Фильтр готов!
att_statistical_initData=Инициализация базы данных завершена!
att_statistical_exception=Инициализация данных о нарушениях завершена!
att_statistical_error=Расчёт об отсутствии не удался!
att_statistical_begin=Расчёт начат!
att_statistical_end=Окончание расчёта!
att_statistical_noticeTime=Рассчитать записи учёта регистрации за этот период, по умолчанию один месяц
att_statistical_remarkHoliday=В
att_statistical_remarkClass=Я
att_statistical_remarkNoSch=НН
att_statistical_remarkRest=ДО
#原始记录表
att_op_importAccRecord=Импорт записи учёта регистрации из модуля контроля доступа
att_op_importParkRecord=Импорт записи учёта регистрации из модуля автостоянки
att_op_importInsRecord=Импорт записей информационного экрана
att_op_importPidRecord=Импорт учётных данных сотрудников
att_op_importVmsRecord=Импорт видео записей
att_op_importUSBRecord=Импорт записи диска U
att_transaction_noAccModule=Нет модуля доступа!
att_transaction_noParkModule=Нет модуля автостоянки!
att_transaction_noInsModule=Нет модуля информационного экрана!
att_transaction_noPidModule=Ни одного модуля карт!
att_transaction_exportRecord=Экспорт оригинальных записей
att_transaction_exportAttPhoto=Экспорт фотографий посещаемости
att_transaction_fileIsTooLarge=Экспортированный файл слишком большой, пожалуйста, сузьте диапазон дат
att_transaction_exportDate=Дата экспорта
att_statistical_attDatetime=время посещения
att_statistical_attPhoto=Фото учёта регистрации
att_statistical_attDetail=Детали учёта регистрации
att_statistical_acc=Устройство контроля доступа
att_statistical_att=Устройство учёта регистрации
att_statistical_park=Видеокамера LPR
att_statistical_faceRecognition=Устройство распознавания лиц
att_statistical_app=Мобильные устройства
att_statistical_vms=видеооборудование
att_statistical_psg=Канальное оборудование
att_statistical_dataSources=Источники данных
att_transaction_SyncRecord=Синхронизировать записи посещаемости
#日打卡详情表
att_statistical_dayCardDetail=Детали регистрации
att_statistical_cardDate=Дата записи
att_statistical_cardNumber=Записано раз
att_statistical_earliestTime=Самое раннее время
att_statistical_latestTime=Самое позднее время
att_statistical_cardTime=Время считывания
#请假汇总表
att_statistical_leaveDetail=Детали отпуска
#日明细报表
att_statistical_attDate=Дата учёта регистрации
att_statistical_week=Неделя
att_statistical_shiftInfo=Информация смены
att_statistical_shiftTimeData=Время работы вкл/выкл
att_statistical_cardValidData=Время считывания
att_statistical_cardValidCount=Количество считывания
att_statistical_lateCount=Количество опозданий
att_statistical_lateMinute=Опоздание минут
att_statistical_earlyCount=Количество ранних уходов
att_statistical_earlyMinute=Ранний уход минут
att_statistical_countData=Раз данные
att_statistical_minuteData=Минут данные
att_statistical_attendance_minute=Учёт рабочего времени (Минуты)
att_statistical_overtime_minute=Сверхурочные (Минуты)
att_statistical_unusual_minute=Отклонения (Минуты)
#月明细报表
att_monthdetail_should_hour=Должно(Часы)
att_monthdetail_actual_hour=Фактически(Часы)
att_monthdetail_valid_hour=Действующие(Часы)
att_monthdetail_absent_hour=Отсутствие(Часы)
att_monthdetail_leave_hour=Отпуск (Время)
att_monthdetail_trip_hour=Командировка (Почасовая)
att_monthdetail_out_hour=Выход (Почасовой)
att_monthdetail_should_day=Должно(Дни)
att_monthdetail_actual_day=Фактически(Дни)
att_monthdetail_valid_day=Действующие(Дни)
att_monthdetail_absent_day=Отсутствие(Дни)
att_monthdetail_leave_day=Отпуск (Дни)
att_monthdetail_trip_day=Командировка (Дни)
att_monthdetail_out_day=Выход (Дни)
#月统计报表
att_statistical_late_minute=Продолжительность (Минуты)
att_statistical_early_minute=Продолжительность (Минуты)
#部门统计报表
#年度统计报表
att_statistical_should=Должно
att_statistical_actual=Фактически
att_statistical_valid=Действующие
att_statistical_numberOfTimes=Раз
att_statistical_usually=Рабочие дни
att_statistical_rest=Выходные
att_statistical_holiday=Праздники
att_statistical_total=Всего
att_statistical_month=Месячная статистика
att_statistical_year=Годовая статистика
att_statistical_attendance_hour=Учёт рабочего времени (Часы)
att_statistical_attendance_day=Учёт рабочего времени (Дни)
att_statistical_overtime_hour=Сверхурочные (Часы)
att_statistical_unusual_hour=Нарушения (Время)
att_statistical_unusual_day=Отклонения (Дни)
#考勤设备参数
att_deviceOption_query=Просмотр параметров устройства
att_deviceOption_noOption=Нет информации о параметрах, пожалуйста, сначала получите параметры устройства
att_deviceOption_name=Название параметра
att_deviceOption_value=Значение параметра
att_deviceOption_UserCount=Номер пользователя
att_deviceOption_MaxUserCount=Максимальное количество пользователей
att_deviceOption_FaceCount=Текущего количество шаблонов лиц
att_deviceOption_MaxFaceCount=Максимальное количество шаблонов лиц
att_deviceOption_FacePhotoCount=Текущее количество изображений лица
att_deviceOption_MaxFacePhotoCount=Максимальное количество изображений лица
att_deviceOption_FingerCount=Текущее количество отпечатков пальца
att_deviceOption_MaxFingerCount=Максимальное количество отпечатков пальца
att_deviceOption_FingerPhotoCount=Количество текущих изображений отпечатков пальцев
att_deviceOption_MaxFingerPhotoCount=Максимальное количество изображений отпечатков пальцев
att_deviceOption_FvCount=Текущее количество рисунка вен пальца
att_deviceOption_MaxFvCount=Текущее количество рисунка вен пальца
att_deviceOption_FvPhotoCount=Количество текущих изображений вен пальцев
att_deviceOption_MaxFvPhotoCount=Количество изображений вен вен
att_deviceOption_PvCount=Текущее количество рисунка вен ладони
att_deviceOption_MaxPvCount=Текущее количество рисунка вен ладони
att_deviceOption_PvPhotoCount=Текущие изображения пальм
att_deviceOption_MaxPvPhotoCount=Максимальное количество изображений ладони
att_deviceOption_TransactionCount=Текущее количество записей
att_deviceOption_MaxAttLogCount=Максимальное количество записей
att_deviceOption_UserPhotoCount=Текущее количество фото
att_deviceOption_MaxUserPhotoCount=Максимальное количество фото
att_deviceOption_FaceVersion=Версия алгоритма распознавания лиц
att_deviceOption_FPVersion=Версия алгоритма распознавания отпечатка пальца
att_deviceOption_FvVersion=Версия алгоритма распознавания вен пальца
att_deviceOption_PvVersion=Версия алгоритма распознавания вен ладони
att_deviceOption_FWVersion=Версия прошивки
att_deviceOption_PushVersion=Версия-Push
#=====================================================================
#API
att_api_areaCodeNotNull=Номер зоны не может быть пустым
att_api_pinsNotNull=Pin-данные не могут быть пустыми
att_api_pinsOverSize=Длина данных Pin не может превышать 500
att_api_areaNoExist=Зона не существует
att_api_sign=Приложение
#=====================================================================
#休息时间段
att_leftMenu_breakTime=Перерыв
att_breakTime_startTime=Время начала
att_breakTime_endTime=Время окончания
#=====================================================================
#导入U盘记录
att_import_uploadFileSuccess=Файл загуржен успешно, начинается анализ данных файла, пожалуйста, подождите ...
att_import_resolutionComplete=После завершения анализа начнётся обновление базы данных.
att_import_snNoExist=Устройства учета регистрации, соответствующему импортированному файлу, не существует. Пожалуйста, выберите файл снова.
att_import_fileName_msg=Требования к формату имени импортируемого файла:  начинается с серийного номера устройства и подчеркивания «_», например ： «3517171600001_attlog.dat»。
att_import_notSupportFormat=Этот формат пока не поддерживается!
att_import_selectCorrectFile=Пожалуйста, выберите правильный формат файла!
att_import_fileFormat=формат файла
att_import_targetFile=Конечный файл
att_import_startRow=Количество строк в начале заголовка
att_import_startRowNote=Первая строка формата данных предназначена для импорта данных, пожалуйста, проверьте файл перед импортом.
att_import_delimiter=Разделитель
#=====================================================================
#设备操作日志
att_device_op_log_op_type=Код операции
att_device_op_log_dev_sn=Серийный номер устройства
att_device_op_log_op_content=Содержание операции
att_device_op_log_operator_pin=Номер операции
att_device_op_log_operator_name=Имя оператора
att_device_op_log_op_time=Время операции
att_device_op_log_op_who_value=Значение объекта операции
att_device_op_log_op_who_content=Описание объекта операции
att_device_op_log_op_value1=Объект операции 2
att_device_op_log_op_value_content1=Описание объекта операции 2
att_device_op_log_op_value2=Объект операции 3
att_device_op_log_op_value_content2=Описание объекта операции 3
att_device_op_log_op_value3=Объект операции 4
att_device_op_log_op_value_content3=Описание объекта операции 4
#操作日志的操作类型
att_device_op_log_opType_0=Включение
att_device_op_log_opType_1=Выключение
att_device_op_log_opType_2=Проверка не удалась
att_device_op_log_opType_3=Тревога
att_device_op_log_opType_4=Вход в меню
att_device_op_log_opType_5=Изменение настроек
att_device_op_log_opType_6=Регистрация от. пальца
att_device_op_log_opType_7=Регистрация пароля
att_device_op_log_opType_8=Регистрация карты HID
att_device_op_log_opType_9=Удаление пользователя
att_device_op_log_opType_10=Удаление от. пальца
att_device_op_log_opType_11=Удаление пароля
att_device_op_log_opType_12=Удаление карты RF
att_device_op_log_opType_13=Очистка данных
att_device_op_log_opType_14=Создание карты MF
att_device_op_log_opType_15=Регистрация карты MF
att_device_op_log_opType_16=Регистрация карты MF
att_device_op_log_opType_17=Удаление регистрации карты MF
att_device_op_log_opType_18=Очистка данные MF карты
att_device_op_log_opType_19=Перемещение регистрационных данных на карту
att_device_op_log_opType_20=Копирование данных с карты на ПК
att_device_op_log_opType_21=Установка времени
att_device_op_log_opType_22=Заводские настройки
att_device_op_log_opType_23=Удаление записей входа и выхода
att_device_op_log_opType_24=Удаление прав администратора
att_device_op_log_opType_25=Изменение настроек группы контроля доступа
att_device_op_log_opType_26=Изменение настроек доступа пользователя
att_device_op_log_opType_27=Изменение периода времени доступа
att_device_op_log_opType_28=Изменение настроек комбинации разблокировки
att_device_op_log_opType_29=Разблокировка
att_device_op_log_opType_30=Регистрация новых пользователей
att_device_op_log_opType_31=Изменение атрибутов от. пальца
att_device_op_log_opType_32=Тревога принуждения
att_device_op_log_opType_34=Запрет дв. прохода
att_device_op_log_opType_35=Удаление фото уч. регистрации
att_device_op_log_opType_36=Изм. информации пользователя
att_device_op_log_opType_37=Праздник
att_device_op_log_opType_38=Восстановление данных
att_device_op_log_opType_39=Резервирование данные
att_device_op_log_opType_40=Загрузка на диск U
att_device_op_log_opType_41=Скачивание с диска U
att_device_op_log_opType_42=Шифрование записей уч. регистрации U диска
att_device_op_log_opType_43=Удаление записей после успешной загрузки с USB-диска
att_device_op_log_opType_53=Переключение выхода
att_device_op_log_opType_54=Датчик двери
att_device_op_log_opType_55=Тревога
att_device_op_log_opType_56=Восстановление параметры
att_device_op_log_opType_68=Регистрация фото пользователя
att_device_op_log_opType_69=Изменение фотографии пользователя
att_device_op_log_opType_70=Изменение имени пользователя
att_device_op_log_opType_71=Изменение разрешения пользователя
att_device_op_log_opType_76=Изменение настроек сети IP
att_device_op_log_opType_77=Изменение маски настроек сети
att_device_op_log_opType_78=Изменение сетевых настроек шлюза
att_device_op_log_opType_79=Изменение настройки сети DNS
att_device_op_log_opType_80=Изменение пароля настройки соединения
att_device_op_log_opType_81=Изменение настройки подключения устройства ID
att_device_op_log_opType_82=Изменение адреса облачного сервера
att_device_op_log_opType_83=Изменение порта облачного сервера
att_device_op_log_opType_87=Изменение настроек записей контроля доступа
att_device_op_log_opType_88=Изменение флага параметра лица
att_device_op_log_opType_89=Изменение флага параметра от. пальца
att_device_op_log_opType_90=Изменение флага параметра вен пальца
att_device_op_log_opType_91=Изменение флага параметра ладони
att_device_op_log_opType_92=Флаг обновления U диска
att_device_op_log_opType_100=Изменение информацию RF-карты
att_device_op_log_opType_101=Регистрация лица
att_device_op_log_opType_102=Изменение доступа персонала
att_device_op_log_opType_103=Удаление прав персонала
att_device_op_log_opType_104=Добавление прав персонала
att_device_op_log_opType_105=Удаление записей контроля доступа
att_device_op_log_opType_106=Удаление лица
att_device_op_log_opType_107=Удаление фотографий персонала
att_device_op_log_opType_108=Изменение параметров
att_device_op_log_opType_109=Выбор WIFI SSID
att_device_op_log_opType_110=Включение Proxy
att_device_op_log_opType_111=Изменение Proxy
att_device_op_log_opType_112=Изменение порта-Proxy
att_device_op_log_opType_113=Изменение пароля пользователя
att_device_op_log_opType_114=Изменение информации о лице
att_device_op_log_opType_115=Изменение пароля оператора
att_device_op_log_opType_116=Возобновление настроек контроля доступа
att_device_op_log_opType_117=Ошибка ввода пароля оператора
att_device_op_log_opType_118=Блокировка пароля оператора
att_device_op_log_opType_120=Изменить длину данных карты Legic
att_device_op_log_opType_121=Зарегистрировать вену пальцев
att_device_op_log_opType_122=Изменить вену пальцев
att_device_op_log_opType_123=Удалить вену пальцев
att_device_op_log_opType_124=Зарегистрировать печать пальмы
att_device_op_log_opType_125=Изменить отпечаток ладони
att_device_op_log_opType_126=Удалить отпечаток ладони
#操作对象描述
att_device_op_log_content_pin=Пользователь ID:
att_device_op_log_content_alarm=Тревога:
att_device_op_log_content_alarm_reason=Причина тревоги:
att_device_op_log_content_update_no=Изменение номера элемента:
att_device_op_log_content_update_value=Изменение значения:
att_device_op_log_content_finger_no=Номер от. пальца:
att_device_op_log_content_finger_size=Длина шаблона от. пальцев:
#=====================================================================
#工作流
att_flowable_datetime_to=до
att_flowable_todomsg_leave=Одобрение отпуска
att_flowable_todomsg_sign=Добавить в журнал одобрений
att_flowable_todomsg_overtime=Одобрение сверхурочных
att_flowable_notifymsg_leave=Уведомление заявления об отпуске
att_flowable_notifymsg_sign=Добавить в журнал одобрений
att_flowable_notifymsg_overtime=Уведомление о сверхурочных
att_flowable_shift=Смена:
att_flowable_hour=час
att_flowable_todomsg_trip=Одобрение командировки
att_flowable_notifymsg_trip=Командировка
att_flowable_todomsg_out=Одобрение ухода с работы
att_flowable_notifymsg_out=Уведомление об уходе с работы
att_flow_apply=Заявление
att_flow_applyTime=Время заявления
att_flow_approveTime=Время обработки
att_flow_operateUser=Рецензент
att_flow_approve=Утверждение
att_flow_approveComment=Аннотация
att_flow_approvePass=Результаты одобрения
att_flow_status_processing=Утверждение
#=====================================================================
#biotime
att_h5_pers_personIdNull=ID сотрудника не может быть пустым
att_h5_attPlaceNull=Местопол. точки уч. регистрации не может быть пустым
att_h5_attAreaNull=Зона учета посещамости не может быть пустой
att_h5_pers_personNoExist=Номер сотрудника не существует
att_h5_signRemarkNull=Примечания не могут быть пустыми
att_h5_common_pageNull=Ошибка параметра подкачки
att_h5_taskIdNotNull=ID задания узла не может быть пустым
att_h5_auditResultNotNull=Результат одобрения не может быть пустым
att_h5_latLongitudeNull=Долгота и широта не могут быть пустыми
att_h5_pers_personIsNull=ID сотрудника не существует
att_h5_pers_personIsNotInArea=Сотруднику не уснановлена зона
att_h5_mapApiConnectionsError=Ошибка подключения API карты
att_h5_googleMap=Карты Google
att_h5_gaodeMap=Карты Gaode
att_h5_defaultMap=Карта по умолчанию
att_h5_shiftTime=Время смены
att_h5_signTimes=Время пополнения
att_h5_enterKeyWords=Пожалуйста, введите ключевые слова:
att_h5_mapSet=Настройки карты посещаемости
att_h5_setMapApiAddress=Установить параметр эл. карты
att_h5_MapSetWarning=Изменение эл. карты приведет к тому, что широта и долгота зарегистрированного адреса APP могут не совпадать. Пожалуйста, будьте осторожны, изменяя её!
att_h5_mapSelect=Выбор эл. карты
att_h5_persNoHire=В это время сотрудник еще не присоединился к компании.
att_slef_apiKey=Ключ-API
att_self_apiJsKey=Ключ-JS-API
att_self_noComputeAtt=Посещаемость дня еще не была учтена.
att_self_noSignRecord=Нет записей регистрации в этот день
att_self_imageUploadError=Не удалось загрузить изображение
att_self_attSignAddressAreaIsExist=В этой зоне уже есть точки регистрации
att_self_signRuleIsError=Текущее время пополнения не соответствует допустимому времени пополнения.
att_self_signAcrossDay=График со сменой суток нельзя отметиться!
att_self_todaySignIsExist=Сегодня уже были добавлены пути!
att_self_signSetting=Настройки входа
att_self_allowSign=Разрешить вход:
att_self_allowSignSuffix=дней, записи уч. регистрации в течение
att_self_onlyThisMonth=Только в этом месяце
att_self_allowAcrossMonth=Разрешить переход месяца
att_self_thisTimeNoSch=В текущем периоде времени нет смены!
att_self_revokeReason=Причина отзыва:
att_self_revokeHint=Пожалуйста, для обзора укажите причину отмены до 20 слов
att_self_persSelfLogin=Самообслуживание сотрудника
att_self_isOpenSelfLogin=Нужно ли начинать вход в систему самообслуживания сотрудников
att_self_applyAndWorkTimeOverlap=Время подачи заявок и время работы совпадают
att_apply_DurationIsZero=Срок подачи заявок — 0
att_sign_mapWarn=Ошибка загрузки карты, проверьте сетевое соединение и значение KEY карты
att_admin_applyWarn=Операция не удалась, есть люди, которые не запланированы или время приложения не входит в рамки расписания!({0})
att_self_getPhotoFailed=Картинка не существует
att_self_view=вид
# 二维码
att_param_qrCodeUrl=Url QR-кода
att_param_qrCodeUrlHref=Адрес сервера: порт
att_param_appAttQrCode=QR-код мобильного уч. регистрации
att_param_timingFrequency=Временной интервал: 5-59 минут или 1-24 часа
att_sign_signTimeNotNull=Время добавления журнала не может быть пустым
att_apply_overLastMonth=Заявление начались раньше, чем в прошлом месяце
att_apply_withoutDetail=Нет деталей процесса
att_flowable_noAuth=Пожалуйста, используйте учетную запись супер администратора для просмотра
att_apply_overtimeOverMaxTimeLong=Сверхурочные больше, чем максимальные
# 考勤设置参数符号
att_other_arrive=√
att_other_late=L
att_other_early=E
att_other_absent=□
att_other_noSignIn=[
att_other_noSignOff=]
att_other_leave=∆
att_other_overtime=OT
att_other_off=○
att_other_classes=●
att_other_trip=T
att_other_out=G
att_other_incomplete=├
att_other_outcomplete=┤
# 服务器下发命令
att_devCmd_submitTime=Отправить время
att_devCmd_returnedResult=Вернуть результат
att_devCmd_returnTime=Время возврата
att_devCmd_content=Содержание команды
att_devCmd_clearCmd=Очистить список команд
# 实时点名
att_realTime_selectDept=Пожалуйста, выберите отдел
att_realTime_noSignPers=Незарегистрированный человек
att_realTime_signPers=Проверено в
att_realTime_signMonitor=Вход в систему мониторинга
att_realTime_signDateTime=Время заезда
att_realTime_realTimeSet=Настройка переклички в реальном времени
att_realTime_openRealTime=Включить перекличку в реальном времени
att_realTime_rollCallEnd=Заканчивается перекличка в реальном времени
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=Запланировано
att_personSch_cycleSch=Циклическое планирование
att_personSch_cleanCycleSch=Расписание чистого цикла
att_personSch_cleanTempSch=Очистить временное расписание
att_personSch_personCycleSch=график цикла человека
att_personSch_deptCycleSch=График цикла отдела
att_personSch_groupCycleSch=Планирование цикла группы
att_personSch_personTempSch=Временное расписание персонала
att_personSch_deptTempSch=Временное расписание отдела
att_personSch_groupTempSch=Временное расписание группы
att_personSch_checkGroupFirst=Пожалуйста, проверьте группу слева или людей в списке справа, чтобы действовать!
att_personSch_sureDeleteGroup=Вы действительно хотите удалить {0} и расписание, соответствующее группе?
att_personSch_sch=Расписание
att_personSch_delSch=Удалить расписание
#考勤计算
att_statistical_sureAllCalculate=Вы уверены, что выполните расчет явки для всего персонала?
# 异常管理
att_exception_downTemplate=Загрузить и импортировать шаблон
att_exception_signImportTemplate=Подписать шаблон импорта
att_exception_leaveImportTemplate=Оставить шаблон импорта
att_exception_overtimeImportTemplate=Шаблон импорта сверхурочных
att_exception_adjustImportTemplate=Настроить шаблон импорта
att_exception_cellDefault=Необязательное поле
att_exception_cellRequired=Обязательное поле
att_exception_cellDateTime=Обязательное поле, формат времени: гггг-ММ-дд ЧЧ: мм: сс, например: 2020-07-07 08:30:00
att_exception_cellLeaveTypeName=Обязательное поле, например: «личный отпуск», «отпуск по браку», «отпуск по беременности и родам», «отпуск по болезни», «ежегодный отпуск», «отпуск в связи с тяжелой утратой», «семейный отпуск», «отпуск по кормлению грудью», «командировка», «выезд» Высокомерное имя
att_exception_cellOvertimeSign=Обязательное поле, например: «сверхурочная работа», «сверхурочная работа в выходные дни», «сверхурочная работа в праздничные дни»
att_exception_cellAdjustType=Обязательное поле, например: «перевод выключен», «класс макияжа»
att_exception_cellAdjustDate=Обязательное поле, формат времени - гггг-ММ-дд, например: 2020-07-07
att_exception_cellShiftName=Обязательное поле, если тип корректировки сдвиг сдвиг
att_exception_refuse=Отказываюсь
att_exception_end=Ненормальный конец
att_exception_delete=Удалить
att_exception_stop=Пауза
# 时间段
att_timeSlot_normalTimeAdd=Добавить обычный временной интервал
att_timeSlot_elasticTimeAdd=Добавить эластичный временной интервал
# 班次
att_shift_addRegularShift=Добавить обычную смену
att_shift_addFlexibleShift=Добавить гибкую смену
# 参数设置
att_param_notLeaveSetting=Настройка расчета без ложных значений
att_param_smallestUnit=Минимальная единица
att_param_workDay=Рабочий день
att_param_roundingControl=Контроль округления
att_param_abort=Вниз (сбросить)
att_param_rounding=округление
att_param_carry=Вверх (переносить)
att_param_reportSymbol=символ отображения отчета
att_param_convertCountValid=Пожалуйста, введите число, допускается использование только одного десятичного знака
att_other_leaveThing=Вещь
att_other_leaveMarriage=Брак
att_other_leaveBirth=продукт
att_other_leaveSick=Больной
att_other_leaveAnnual=год
att_other_leaveFuneral=Похороны
att_other_leaveHome=Исследовать
att_other_leaveNursing=Nursing
att_other_leavetrip=разница
att_other_leaveout=другие
att_common_schAndRest=Расписание и отдых
att_common_timeLongs=Продолжительность времени
att_personSch_checkDeptOrPersFirst=Пожалуйста, проверьте отдел слева или человека в списке справа!
att_personSch_checkCalendarFirst=Пожалуйста, выберите дату, которую вы должны запланировать в первую очередь!
att_personSch_cleanCheck=Очистить чек
att_personSch_delTimeSlot=Очистить выбранный период времени
att_personSch_repeatTimeSlotNoAdd=Повторный период времени не будет добавлен!
att_personSch_showSchInfo=Показать детали расписания
att_personSch_sureToCycleSch=Вы обязательно планируете {0} периодически?
att_personSch_sureToTempSch=Вы уверены, что временно запланировали {0}?
att_personSch_sureToCycleSchDeptOrGroup=Вы уверены, что будете периодически планировать весь персонал в {0}?
att_personSch_sureToTempSchDeptOrGroup=Вы уверены, что временно запланировали весь персонал под {0}?
att_personSch_sureCleanCycleSch=Вы уверены, что хотите очистить {0} от {1} до {2}?
att_personSch_sureCleanTempSch=Вы уверены, что хотите очистить {0} временный сдвиг с {1} на {2}?
att_personSch_sureCleanCycleSchDeptOrGroup=Вы уверены, что хотите очистить периодическое расписание от {1} до {2} для всех людей с {0}?
att_personSch_sureCleanTempSchDeptOrGroup=Вы уверены, что хотите очистить временное расписание от {1} до {2} для всех людей с {0}?
att_personSch_today=сегодня
att_personSch_timeSoltName=Имя периода времени
att_personSch_export=Экспорт кадрового расписания
att_personSch_exportTemplate=Экспорт шаблона временной смены персонала
att_personSch_import=Импорт временного планирования персонала
att_personSch_tempSchTemplate=Временный шаблон планирования персонала
att_personSch_tempSchTemplateTip=Пожалуйста, выберите время начала и время окончания расписания, загрузите шаблон расписания в эту дату
att_personSch_opTip=Инструкция по эксплуатации
att_personSch_opTip1=1.вы можете использовать мышь, чтобы перетащить временной интервал к одной дате в элементе управления календаря для планирования.
att_personSch_opTip2=2.В элементе управления календаря дважды щелкните одну дату, чтобы запланировать.
att_personSch_opTip3=3.В элементе управления календаря нажмите и удерживайте кнопку мыши, чтобы выбрать несколько дат для планирования.
att_personSch_schRules=Расписание правил
att_personSch_schRules1=1.периодическое планирование: переписать предыдущее планирование после той же даты, независимо от того, нет ли пересечения.
att_personSch_schRules2=2.Временное планирование: в ту же дату есть пересечение, а предыдущее временное планирование перезаписывается позже. Если пересечения нет, они будут существовать в то же время.
att_personSch_schRules3=3.Период и Временный: Если в ту же дату будет пересечение, период будет временно покрыт, и если пересечения нет, они будут существовать в одно и то же время. Если тип периодического планирования интеллектуально ищет сдвиг, временное планирование будет непосредственно охватывать период.
att_personSch_schStatus=Состояние расписания
#左侧菜单-排班管理
att_leftMenu_schDetails=Подробности расписания
att_leftMenu_detailReport=Подробный отчет о посещаемости
att_leftMenu_signReport=Таблица деталей регистрации
att_leftMenu_leaveReport=Оставить форму подробностей
att_leftMenu_abnormal=Аномальная таблица посещаемости
att_leftMenu_yearLeaveSumReport=Годовой отчет LeaveSumReport
att_leave_maxFileCount=Вы можете добавить не более 4 фотографий
#时间段
att_timeSlot_add=Установить временной интервал
att_timeSlot_select=Пожалуйста, выберите период времени!
att_timeSlot_repeat=Период времени "{0}" повторяется!
att_timeSlot_overlapping=Период времени "{0}" перекрывается с временем коммутации "{1}"!
att_timeSlot_addFirst=Пожалуйста, сначала установите период времени!
att_timeSlot_notEmpty=Период времени, соответствующий табельному номеру {0}, не может быть пустым!
att_timeSlot_notExist=Период времени "{1}", соответствующий табельному номеру {0}, не существует!
att_timeSlot_repeatEx=Период времени, соответствующий табельному номеру {0}, "{1}" и "{2}" перекрываются с переключением
att_timeSlot_importRepeat=Период времени "{1}", соответствующий табельному номеру {0}, повторяется
att_timeSlot_importNotPin=В системе нет человека с номером {0}!
att_timeSlot_elasticTimePeriod=Номер персонала {0}, не может импортировать эластичный период времени "{1}"!
#导入
att_import_overData=Текущее количество импортов: {0}, превышающее ограничение в 30 000, пожалуйста, импортируйте пакетами!
att_import_existIllegalType=Импортированный {0} имеет недопустимый тип!
#验证方式
att_verifyMode_0=автоматическое распознавание
att_verifyMode_1=Только отпечаток пальца
att_verifyMode_2=Проверка номера вакансии
att_verifyMode_3=Только пароль
att_verifyMode_4=Только карта
att_verifyMode_5=Отпечаток пальца или пароль
att_verifyMode_6=Отпечаток пальца или карточка
att_verifyMode_7=Карта или пароль
att_verifyMode_8=Номер задания плюс отпечаток пальца
att_verifyMode_9=Отпечаток пальца плюс пароль
att_verifyMode_10=Карта плюс отпечаток пальца
att_verifyMode_11=Карта плюс пароль
att_verifyMode_12=Отпечаток пальца плюс пароль плюс карта
att_verifyMode_13=Рабочий идентификатор плюс отпечаток пальца плюс пароль
att_verifyMode_14=(Рабочий номер плюс отпечаток пальца) или (карта плюс отпечаток пальца)
att_verifyMode_15=человеческое лицо
att_verifyMode_16=Лицо и отпечаток пальца
att_verifyMode_17=Лицо плюс пароль
att_verifyMode_18=Лицо плюс карта
att_verifyMode_19=Лицо плюс отпечаток пальца плюс карта
att_verifyMode_20=Лицо плюс отпечаток пальца плюс пароль
att_verifyMode_21=Пальцевая вена
att_verifyMode_22=Палец вены плюс код
att_verifyMode_23=Карта пальца вены плюс
att_verifyMode_24=Палец вены плюс код плюс карта
att_verifyMode_25=Отпечаток ладони
att_verifyMode_26=Карманная карта plus plus
att_verifyMode_27=Отпечатки ладоней и лицо
att_verifyMode_28=Отпечаток пальца и отпечаток пальца
att_verifyMode_29=Отпечаток ладони плюс отпечаток пальца плюс лицо
# 工作流
att_flow_schedule=Аудит прогресса
att_flow_schedulePass=(Пройдено)
att_flow_scheduleNot=(Не утверждено)
att_flow_scheduleReject=(отклонено)
# 工作时长表
att_workTimeReport_total=Общее количество рабочих часов
# 自动导出报表
att_autoExport_startEndTime=Время начала и окончания
# 年假
att_annualLeave_setting=Настройка баланса ежегодного отпуска
att_annualLeave_settingTip1=Чтобы использовать функцию баланса годового отпуска, вам необходимо установить время входа для каждого сотрудника; когда время входа не установлено, оставшийся годовой отпуск таблицы баланса годового отпуска персонала отображается как пустой.
att_annualLeave_settingTip2=Если текущая дата больше, чем дата выдачи клиринга, это изменение вступит в силу в следующем году; если текущая дата меньше даты выдачи клиринга, когда дата выдачи клиринга будет достигнута, она будет очищена, и ежегодный отпуск будет повторно оформлен.
att_annualLeave_calculate=Дата оформления и выдачи ежегодного отпуска
att_annualLeave_workTimeCalculate=Рассчитать в соответствии с соотношением рабочего времени
att_annualLeave_rule=Правило ежегодного отпуска
att_annualLeave_ruleCountOver=Достигнут максимальный установленный предел количества
att_annualLeave_years=Старшие годы
att_annualLeave_eachYear=Каждый год
att_annualLeave_have=Да
att_annualLeave_days=Дни ежегодного отпуска
att_annualLeave_totalDays=Общий годовой отпуск
att_annualLeave_remainingDays=Оставшийся ежегодный отпуск
att_annualLeave_consecutive=Правило ежегодного отпуска должно составлять несколько лет подряд
# 年假结余表
att_annualLeave_report=Годовой баланс отпуска
att_annualLeave_validDate=Действительная дата
att_annualLeave_useDays=Использовать {0} дней
att_annualLeave_calculateDays=Выпуск {0} дней
att_annualLeave_notEnough=Недостаточный ежегодный отпуск в {0}!
att_annualLeave_notValidDate={0} находится вне допустимого диапазона ежегодного отпуска!
att_annualLeave_notDays={0} не имеет ежегодного отпуска!
att_annualLeave_tip1=Чжан Сан присоединился 1 сентября прошлого года
att_annualLeave_tip2=Настройка баланса ежегодного отпуска
att_annualLeave_tip3=Дата клиринга и выдачи - 1 января каждого года; она рассчитывается путем округления в соответствии с рабочим соотношением; если стаж работы меньше или равен 1, будет 3 дня ежегодного отпуска, а если стаж работы меньше или равен 3 годам, будет 5 дней ежегодного отпуска
att_annualLeave_tip4=Расчет ежегодного отпуска
att_annualLeave_tip5=В прошлом году 09-01 ~ 12-31 наслаждались 4 / 12x3=1.0 дней
att_annualLeave_tip6=В этом году 01-01 ~ 12-31 наслаждайтесь 4,0 дня (в этом году 01-01 ~ 08-31 наслаждайтесь 8 / 12x3=2,0 дня   в этом году 09-01 ~ 12-31 наслаждайтесь 4 / 12x5≈2,0 дня)
# att SDC
att_sdc_name=Видеооборудование
att_sdc_wxMsg_firstData=Здравствуйте, у вас есть уведомление о прибытии
att_sdc_wxMsg_stateData=Нет ощущения присутствия при успешной регистрации
att_sdc_wxMsg_remark=Напоминание: окончательный результат посещаемости указан на странице сведений о регистрации.
# 时间段
att_timeSlot_conflict=Временной интервал конфликтует с другими временными интервалами дня
att_timeSlot_selectFirst=Пожалуйста, выберите временной интервал
# 事件中心
att_eventCenter_sign=Вход посещаемости
#异常管理
att_exception_classImportTemplate=Шаблон импорта класса
att_exception_cellClassAdjustType=Обязательное поле, например: «{0}», «{1}», «{2}»
att_exception_swapDateDate=необязательное поле, формат времени - гггг-мм-дд, например: 2020-07-07
#消息中心
att_message_leave=регистрация {0} уведомление
att_message_leaveContent={0} отправлено {1}, {2} время {3} ~ {4}
att_message_leaveTime=Время выхода
att_message_overtime=Уведомление о посещаемости и сверхурочной работе
att_message_overtimeContent={0} отправлено сверхурочное время, а сверхурочное время составляет {1} ~ {2}
att_message_overtimeTime=Сверхурочное время
att_message_sign=Уведомление о посещаемости знак
att_message_signContent={0} отправил дополнительный знак, и время дополнительного подписания: {1}
att_message_adjust=Уведомление о корректировке посещаемости
att_message_adjustContent={0} отправил корректировку, и дата корректировки: {1}
att_message_class=Уведомление о посещаемости и смене
att_message_classContent=Содержание класса
att_message_classContent0={0} отправил смену, дата смены - {1}, а смена - {2}
att_message_classContent1={0} отправил смену, дата смены - {1}, дата смены - {2}
att_message_classContent2={0} ({1}) и {2} ({3}) замена классов
#推送中心
att_pushCenter_transaction=Запись посещаемости
# 时间段
att_timeSlot_workTimeNotEqual=Рабочее время не может быть равно времени выхода из работы
att_timeSlot_signTimeNotEqual=Время начала входа не может быть равно времени окончания выхода
# 北向接口A
att_api_notNull={0} не может быть пустым!
att_api_startDateGeEndDate=Время начала не может быть больше или равно времени окончания!
att_api_leaveTypeNotExist=Поддельный вид не существует!
att_api_imageLengthNot2000=Длина адреса изображения не может превышать 2000!
# 20221230新增国际化
att_personSch_workTypeNotNull=номер сотрудника {0} соответствует типу работы, который не может быть пустым!
att_personSch_workTypeNotExist=личный номер {0} соответствующий тип работы не существует!
att_annualLeave_recalculate=Перерасчет
# 20230530新增国际化
att_leftMenu_dailyReport=Ежедневный отчет о посещаемости
att_leftMenu_overtimeReport=Отчет о сверхурочной работе
att_leftMenu_lateReport=Поздний отчет
att_leftMenu_earlyReport=Оставить ранний отчет
att_leftMenu_absentReport=Отчет об отсутствии
att_leftMenu_monthReport=Ежемесячный отчет о посещаемости
att_leftMenu_monthWorkTimeReport=Ежемесячный отчет о рабочем времени
att_leftMenu_monthCardReport=Ежемесячный отчет по карте
att_leftMenu_monthOvertimeReport=Ежемесячный отчет о сверхурочной работе
att_leftMenu_overtimeSummaryReport=Сводный отчет о сверхурочной работе персонала
att_leftMenu_deptOvertimeSummaryReport=Отчет о сверхурочной работе отдела
att_leftMenu_deptLeaveSummaryReport=Сводный отчет об отпуске отдела
att_annualLeave_calculateDay=Количество дней ежегодного отпуска
att_annualLeave_adjustDay=Настройка дней
att_annualLeave_sureSelectDept=Вы уверены, что хотите выполнить операцию {0} в выбранном отделе?
att_annualLeave_sureSelectPerson=Вы уверены, что хотите выполнить операцию {0} для выбранного человека?
att_annualLeave_calculateTip1=При расчете по стажу: расчет ежегодного отпуска производится с точностью до месяца, если стаж составляет 10 лет и 3 месяца, то для расчета будет использоваться 10 лет и 3 месяца;
att_annualLeave_calculateTip2=Когда преобразование не основано на стаже работы: расчет ежегодного отпуска осуществляется с точностью до года, если стаж работы составляет 10 лет и 3 месяца, то для расчета будут использоваться 10 лет;
att_rule_isInCompleteTip=Приоритет имеет наивысший уровень, когда ни один вход или выход не записываются как незавершенные, а также опоздание, ранний уход, прогул и действительный
att_rule_absentTip=Когда отсутствие регистрации или выхода из системы регистрируется как прогул, продолжительность прогула равна продолжительности рабочего времени минус продолжительность позднего или раннего отпуска
att_timeSlot_elasticTip1=0, эффективное время равно фактическому времени, прогулов нет
att_timeSlot_elasticTip2=Если фактическое время больше рабочего времени, эффективное время равно рабочему времени, без прогулов
att_timeSlot_elasticTip3=Если фактическая продолжительность меньше рабочей продолжительности, эффективная продолжительность равна фактической продолжительности, а прогулы равны рабочей продолжительности минус фактическая продолжительность
att_timeSlot_maxWorkingHours=Рабочие часы не могут превышать
# 20231030
att_customReport=Настроенный отчет по посещаемости
att_customReport_byDayDetail=Подробно по дням
att_customReport_byPerson=Сводка по персоналу
att_customReport_byDept=Сводка по отделам.
att_customReport_queryMaxRange=Максимальный диапазон запроса составляет четыре месяца.
# 20240630新增国际化
att_personSch_shiftWorkTypeTip1=1, при сверхурочной работе в обычный рабочий / выходной день приоритет ниже, чем в праздничные дни
att_personSch_shiftWorkTypeTip2=2, при сверхурочной работе в праздничные дни приоритет выше, чем в праздничные дни
att_personVerifyMode=Способы проверки персонала
att_personVerifyMode_setting=Параметры режима проверки
att_personSch_importCycSch=Импорт циклов
att_personSch_cycSchTemplate=Модуль цикла
att_personSch_exportCycSchTemplate=Загрузить шаблон цикла
att_personSch_scheduleTypeNotNull=Тип смены не может быть пустым или несуществующим!
att_personSch_shiftNotNull=Класс не может быть пустым!
att_personSch_shiftNotExist=Класс не существует!
att_personSch_onlyAllowOneShift=Обычная смена допускается только на одну смену!
att_shift_attShiftStartDateRemark2=Тиждень, у який розташована дата початку циклу, є першим тиждень; Місяць, у якому розташована дата початку циклу, є першим місяцем.
#打卡状态
att_cardStatus_setting=Настройка статуса приема-контроля
att_cardStatus_name=Имя
att_cardStatus_value=Значение
att_cardStatus_alias=Псевдоним
att_cardStatus_every_day=Всегдай
att_cardStatus_by_week=По неделямй
att_cardStatus_autoState=Автоматический статус
att_cardStatus_attState=Статус приема-контроля
att_cardStatus_signIn=Входить
att_cardStatus_signOut=Выходить
att_cardStatus_out=внешний
att_cardStatus_outReturn=Возвращаться после выхода
att_cardStatus_overtime_signIn=Входить на смену во время пребывания
att_cardStatus_overtime_signOut=Выходить со смены во время пребывания
# 20241030新增国际化
att_leaveType_enableMaxDays=Включить годовой лимит
att_leaveType_maxDays=Годовой лимит (дни)
att_leaveType_applyMaxDays=Заявка не может превышать {0} дней в этом году
att_param_overTimeSetting=Настройка уровня сверхурочного рабочего времени
att_param_overTimeLevel=Уровень сверхурочного рабочего времени (часы)
att_param_overTimeLevelEnable=Включить расчет уровня сверхурочного рабочего времени
att_param_reportColor=Цвет отчета
# APP
att_app_signClientTip=Этот устройство уже было зарегистрировано через другого пользователя сегодня
att_app_noSignAddress=Район сканирования не настроен, пожалуйста обратитесь к администратору для настройки
att_app_notInSignAddress=Не достигнуто место сканирования, невозможно зарегистрироваться
att_app_attendance=Моя посещаемость
att_app_apply=Заявка на посещаемость
att_app_approve=Мои одобрения
# 20250530
att_node_leaderNodeExist=Узел одобрения непосредственного руководителя уже существует
att_signAddress_init=Инициализация карты
att_signAddress_initTips=Введите ключ карты и инициализируйте карту для выбора адреса