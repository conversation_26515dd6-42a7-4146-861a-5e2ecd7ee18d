#Nazwa systemu 波兰
att_systemName=System zarządzania obecnością w pracy Att 6.0
#=====================================================================
#Menu w lewo
att_module=Frekwencja
#Menu w lewo-urządzenie kontroli obecności w pracy
att_leftMenu_attendanceDevice=Gestione del dispositivo
att_leftMenu_device=Urządzenie kontrolne
att_leftMenu_point=Punkt kontroli obecności w pracy
att_leftMenu_sign_address=Adres do logowania mobilnego
att_leftMenu_adms_devCmd=Serwer wydał polecenie
#Menu w lewo-podstawowa informacja
att_leftMenu_basicInformation=Podstawowa informacja
att_leftMenu_rule=Zasada
att_leftMenu_base_rule=Podstawowa zasada
att_leftMenu_department_rule=Zasada działowa 
att_leftMenu_holiday=Święta
att_leftMenu_leaveType=Typ urlop
att_leftMenu_timingCalculation=Rozliczenie w ustalonym czasie
att_leftMenu_autoExport=Eksportowanie automatyczne
att_leftMenu_param=Ustawienia parametrów
#Menu w lewo-zarządzanie zmianami
att_leftMenu_shiftManagement=Zarządzanie zmianami
att_leftMenu_timeSlot=Przedział czasu
att_leftMenu_shift=Zmiana
#Menu w lewo-zarządzanie zmiany
att_leftMenu_scheduleManagement=Zarządzanie zmianami
att_leftMenu_group=Grupowanie
att_leftMenu_groupPerson=Personel do pogrupowania
att_leftMenu_groupSch=Zmiany w grupach
att_leftMenu_deptSch=Zmiany w działu
att_leftMenu_personSch=Zmiany personelu
att_leftMenu_tempSch=Zmiany tymczasowe
att_leftMenu_nonSch=Personel wolny od zmiany
#Menu w lewo-zarządzanie anomalią
att_leftMenu_exceptionManagement=Zarządzanie wyjątkami dotyczącymi obecności
att_leftMenu_sign=Formularz do dodawania zalogowania
att_leftMenu_leave=Złożenie wniosku o nieobecność
att_leftMenu_trip=Delegacja
att_leftMenu_out=Praca poza biurem
att_leftMenu_overtime=Praca nadliczbowa
att_leftMenu_adjust=Odczytywania w celu uzupełnienia pracy
att_leftMenu_class=Zmiana rozkładu zmiany
#Menu w lewo-Raport statystyczny
att_leftMenu_statisticalReport=Raport statystyk frekwencji
att_leftMenu_manualCalculation=Rozliczenie ręczne
att_leftMenu_transaction=Raport oryginalnego wpisów
att_leftMenu_dayCardDetailReport=Tabela szczegółowa logowania dziennego
att_leftMenu_leaveSummaryReport=Raport nieobecności
att_leftMenu_dayDetailReport=Raport dzienny logowania
att_leftMenu_monthDetailReport=Raport miesięczny logowania
att_leftMenu_monthStatisticalReport=Raport statystyczny miesięczny (według personelu)
att_leftMenu_deptStatisticalReport=Raport statystyczny udziałowy (według udziału)
att_leftMenu_yearStatisticalReport=Raport statystyczny roczny (według personelu)
att_leftMenu_attSignCallRollReport=Zarejestruj się w liście
att_leftMenu_workTimeReport=Raport czasu pracy
#Menu w lewo-Dziennik obsługiwania urządzenia
att_leftMenu_device_op_log=Dziennik obsługiwania urządzenia
# 左侧 菜单 - 实时 点名
att_leftMenu_realTime_callRoll=Zadzwoń
#=====================================================================
#Wspólny 
att_common_person=Personel 
att_common_pin=Numer
att_common_group=Grupowanie
att_common_dept=Dział
att_common_symbol=Znak
att_common_deptNo=Numer działu
att_common_deptName=Nazwa działu
att_common_groupNo=Numer grupy
att_common_groupName=Nazwa grupy
att_common_operateTime=Czas obsługiwania
att_common_operationFailed=Awaria obsługiwania
att_common_id=ID
att_common_deptId=ID działu
att_common_groupId=ID grupy
att_common_deviceId=ID urządzenia
att_person_pin=Numer personelu
att_person_name=Imię i nazwisko 
att_person_lastName=Nazwisko
att_person_internalCard=Numer karty
att_person_attendanceMode=Tryb czasu i obecności
att_person_normalAttendance=Zwykła obecność
att_person_noPunchCard=Brak karty dziurkowanej
att_common_attendance=Obecność
att_common_attendance_hour=Obecność(ilość godzin)
att_common_attendance_day=Obecność (ilość dni)
att_common_late=Spóźnienie do pracy
att_common_early=Wyjście z pracy wcześnie 
att_common_overtime=Praca nadliczbowa
att_common_exception=Anomalia
att_common_absent=Nieobecny
att_common_leave=Złożenie wniosku o nieobecność
att_common_trip=Delegacja
att_common_out=Praca poza biurem
att_common_staff=Zwykły pracownik 
att_common_superadmin=Super administrator
att_common_msg=Treść wiadomości
att_common_min=Czas wyświetlenia wiadomości
att_common_letterNumber=Należy tylko i wyłącznie wprowadzić cyfry i litery! 
att_common_relationDataCanNotDel=Nie można usunąć powiązanych danych. 
att_common_relationDataCanNotEdit=Nie można modyfikować danych powiązanych.
att_common_needSelectOneArea=Należy wybrać jedną strefę!
att_common_neesSelectPerson=Należy wybrać personelu.
att_common_nameNoSpace=Nie wolno zawierać spacji w nazwach.
att_common_digitsValid=Należy tylko wprowadzić liczby nie więcej niż dwa miejsca po przecinku!
att_common_numValid=Należy tylko wprowadzić cyfry!
#=====================================================================
#Panel operacyjny
att_dashboard_worker=Pracoholik
att_dashboard_today=Kontrola obecności w pracy na dzień dzisiejszy 
att_dashboard_todayCount=Statystyka sekcjonowana kontroli obecności na dzień dzisiejszy
att_dashboard_exceptionCount=Statystyki anomalii (w obecnym miesiącu)
att_dashboard_lastWeek=Ostatnie tydzień
att_dashboard_lastMonth=Ostatnie miesiąc
att_dashboard_perpsonNumber=Suma personelu
att_dashboard_actualNumber=Obecny
att_dashboard_notArrivedNumber=Nieobecny
att_dashboard_attHour=Ilość godzin pracy
#Strefa
#Urządzenie
att_op_syncDev=Synchronizowanie danych oprogramowania do urządzenia
att_op_account=Kalibracja danych kontroli obecności w pracy
att_op_check=Wrzucenie danych ponownie
att_op_deleteCmd=Skasowanie poleceń urządzenia
att_op_dataSms=Wiadomość publiczna
att_op_clearAttPic=Skasowanie zdjęć kontroli obecności w pracy
att_op_clearAttLog=Skasowanie zapisów kontroli obecności w pracy
att_device_waitCmdCount=Ilość poleceń do wykonania
att_device_status=W stanie włączonym
att_device_register=Urządzenie rejestrujące
att_device_isRegister=Czy jest urządzeniem do zapisu
att_device_existNotRegDevice=Nie jest urządzeniem do zapisu, brak możliwości pobrania danych!
att_device_fwVersion=Wersja oprogramowania układowego
att_device_transInterval=Odstęp czasu między odświeżaniami
att_device_cmdCount=Maksymalna ilość poleceń komunikacji z serwerem
att_device_delay=Czas na wyszukiwanie zapisów (sekund)
att_device_timeZone=Strefa czasowa
att_device_operationLog=Zapis obsługiwania
att_device_registeredFingerprint=Odciski palców zapisywane
att_device_registeredUser=Użytkownik zarejestrowany
att_device_fingerprintImage=Zdjęcia odcisków palców
att_device_editUser=Edycja użytkownika
att_device_modifyFingerprint=Zmiana odcisków palców
att_device_faceRegistration=Zapis twarzy
att_device_userPhotos=Zdjęcie użytkownika
att_device_attLog=Czy wrzucić zapisy kontroli obecności w pracy do urządzenia
att_device_operLog=Czy wrzucić informację o personelu do urządzenia
att_device_attPhoto=Czy wrzucić zdjęcie kontroli obecności w pracy do urządzenia
att_device_isOnLine=Czy jest podłączone
att_device_InputPin=Wprowadzenie numeru personelu
att_device_getPin=Pobranie danych wybranego personelu
att_device_separatedPin=W przypadku wielu numerów użytkownika, należy podzielić za pomocą odcinka. 
att_device_authDevice=Autoryzacja urządzenia
att_device_disabled=Następujące urządzenia są wyłączone i nie mogą być obsługiwane!
att_device_autoAdd=Nowy sprzęt jest dodawany automatycznie
att_device_receivePersonOnlyDb=Odbieranie tylko danych personelu obecnego w bazie danych
att_devMenu_control=Kontrola sprzętu
att_devMenu_viewOrGetInfo=Przeglądanie i uzyskiwanie dostępu do informacji
att_devMenu_clearData=Skasowanie danych urządzenia
att_device_disabledOrOffline=Urządzenie nie jest włączony lub podłączone, nie można go obsługiwać.
att_device_areaStatus=Stan obszaru urządzeń
att_device_areaCommon=Region jest normalny
att_device_areaEmpty=Obszar jest pusty
att_device_isRegDev=Zmiana strefy czasowej lub stanu rejestratora wymaga ponownego uruchomienia urządzenia, aby odniosła skutek!
att_device_canUpgrade=Następujące urządzenia można zaktualizować
att_device_offline=Następujące urządzenia są w trybie offline i nie można ich obsługiwać!
att_device_oldProtocol=Stary protokół
att_device_newProtocol=Nowy protokół
att_device_noMoreTwenty=Pakiet aktualizacji oprogramowania sprzętowego starego protokołu nie może przekraczać 20M
att_device_transferFilesTip=Oprogramowanie układowe wykryte pomyślnie, prześlij pliki
att_op_clearAttPers=Oczyść personel sprzętu
#Personel w strefie
att_op_forZoneAddPers=Dodanie personelu do strefy
att_op_dataUserSms=Wiadomość prywatna
att_op_syncPers=Synchronizacja do urządzenia ponownie
att_areaPerson_choiceArea=Należy wybrać strefę!
att_areaPerson_byAreaPerson=Według regionu
att_areaPerson_setByAreaPerson=Ustawione przez personel obszaru
att_areaPerson_importBatchDel=Importuj usuwanie zbiorcze
att_areaPerson_syncToDevSuccess=Operacja z sukcesem! Należy poczekać aż wysłanie poleceń się kończy. 
att_areaPerson_personId=ID personelu
att_areaPerson_areaId=ID strefy
att_area_existPerson=Istnieje personel w strefie!
att_areaPerson_notice1=Strefy lub personel nie mogą być puste w tym samym czasie!
att_areaPerson_notice2=Nie ma ani personelu, ani strefy wyszukiwanych!
att_areaPerson_notice3=Brak urządzenia w strefie!
att_areaPerson_addArea=Dodanie strefy
att_areaPerson_delArea=Usunięcie strefy
att_areaPerson_persNoExit=Osoba nie istnieje
att_areaPerson_importTip1=Upewnij się, że zaimportowana osoba już istnieje w module personelu
att_areaPerson_importTip2=Importerzy partii nie zostaną automatycznie dostarczeni do urządzenia i muszą zostać ręcznie zsynchronizowani
att_areaPerson_addAreaPerson=Dodaj personel regionalny
att_areaPerson_delAreaPerson=Usuń personel obszaru
att_areaPerson_importDelAreaPerson=Importuj i usuń personel obszaru
att_areaPerson_importAreaPerson=Importuj personel obszaru
#Punkt kontroli obecności w pracy
att_attPoint_name=Nazwa punktu kontroli obecności w pracy
att_attPoint_list=Lista punktów kontroli obecności w pracy
att_attPoint_deviceModule=Moduł urządzenia
att_attPoint_acc=Kontrola dostępu
att_attPoint_park=Parking
att_attPoint_ins=Ekran informacyjny
att_attPoint_pid=Dopasowanie dokumentu do personelu
att_attPoint_vms=Wideo
att_attPoint_psg=Nawa
att_attPoint_doorList=Lista bram
att_attPoint_deviceList=Lista urządzeń 
att_attPoint_channelList=Lista kanałów
att_attPoint_gateList=Lista bram
att_attPoint_recordTypeList=Wyciągnij typ rekordu
att_attPoint_door=Należy wybrać odpowiedną bramę
att_attPoint_device=Należy wybrać odpowiedne urządzenie
att_attPoint_gate=Proszę wybrać odpowiednią bramkę
att_attPoint_normalPassRecord=Normalny zapis przejścia
att_attPoint_verificationRecord=Rekord weryfikacyjny
att_person_attSet=Ustawienia kontroli obecności 
att_attPoint_point=Należy wybrać punkt kontroli obecności 
att_attPoint_count=Brak wystarczających ilości punktów kontroli obecności, operacja nieskuteczna. 
att_attPoint_notSelect=Brak odpowiednego modułu w konfiguracji
att_attPoint_accInsufficientPoints=Brak wystarczających punktów kontroli obecności uznanych z zapisów kontroli dostępu!
att_attPoint_parkInsufficientPoints=Brak wystarczających punktów kontroli obecności uznanych z zapisów parkingowych!
att_attPoint_insInsufficientPoints=Brak wystarczających punktów kontroli obecności uznanych z zapisów ekranu informacyjnego!
att_attPoint_pidInsufficientPoints=Brak wystarczających punktów kontroli obecności uznanych z zapisów dopasowania dokumentu do człowieka!
att_attPoint_doorOrParkDeviceName=Nazwa bramy lub urządzenia parkingowego
att_attPoint_vmsInsufficientPoints=Brak wystarczających punktów kontroli obecności uznanych z zapisów wideo!
att_attPoint_psgInsufficientPoints=Kanał ma niewystarczającą liczbę punktów frekwencji!
att_attPoint_delDevFail=Awaria usunięcia urządzenia z powodu używanego jako punkt kontroli obecności w pracy.
att_attPoint_pullingRecord=Punkt obecności regularnie otrzymuje rekordy, proszę czekać!
att_attPoint_lastTransactionTime=Ostatni czas pobierania danych
att_attPoint_masterDevice=Urządzenie główne
att_attPoint_channelName=Nazwa kanału
att_attPoint_cameraName=Nazwa kamery
att_attPoint_cameraIP=adres IP kamery
att_attPoint_channelIP=IP kanału
att_attPoint_gateNumber=Numer bramy
att_attPoint_gateName=Nazwa bramy
#Adres zalogowania się kontroli obecności przez aplikację
att_signAddress_address=Adres
att_signAddress_longitude=Długość geograficzna
att_signAddress_latitude=Szerokość geograficzna
att_signAddress_range=Ważny zakres
att_signAddress_rangeUnit=Jednostka: metr
#=====================================================================
#Zasada
att_rule_baseRuleSet=Ustawienia podstawowej zasady
att_rule_countConvertSet=Ustawienia obliczenie konserwacji
att_rule_otherSet=Inne Ustawienia
att_rule_baseRuleSignIn=Zasada pobrania karty przy zalogowaniu się do pracy
att_rule_baseRuleSignOut=Zasada oddania karty przy wylogowaniu się z pracy
att_rule_earliestPrinciple=Najwcześniej
att_rule_theLatestPrinciple=Najpóźniej
att_rule_principleOfProximity=Najbliżej
att_rule_baseRuleShortestMinutes=Najkrótszy przedział czasu kontroli obecności powinien być dłuższy niż (minimalna wartość: 10min)
att_rule_baseRuleLongestMinutes=Najdłuższy przedział czasu kontroli obecności powinien być krótszy niż (maksymalna wartość: 1440min)
att_rule_baseRuleLateAndEarly=W przypadku spóźnienia do pracy łącznie z wcześniejszym wyjściem z pracy, należy liczyć do nieobecności 
att_rule_baseRuleCountOvertime=Statystyki pracy nadliczbowej
att_rule_baseRuleFindSchSort=Wyszukiwanie kolejności zapisu zmiany
att_rule_groupGreaterThanDepartment=Grupa>dział
att_rule_departmentGreaterThanGroup=Dział->grupa
att_rule_baseRuleSmartFindClass=Zasada wyszukiwania zmiany inteligentnie
att_rule_timeLongest=Najdłuższy w godzinach
att_rule_exceptionLeast=Najmniejszy w anomaliach
att_rule_baseRuleCrossDay=Wyniki obliczeń obecności przy zmianach mających przedział czasu przypadający na kolejnym dniu 
att_rule_firstDay=Pierwszy dzień
att_rule_secondDay=Drugi dzień
att_rule_baseRuleShortestOvertimeMinutes=Minimalny przedział czasu pracy nadliczbowej pojedynczej (min)
att_rule_baseRuleMaxOvertimeMinutes=Maksymalny przedział czasu pracy nadliczbowej (min)
att_rule_baseRuleElasticCal=Metoda obliczania elastycznych godzin pracy
att_rule_baseRuleTwoPunch=Godzina pracy między pierwszym zalogowaniem się kartą do pracy a pierwszym wylogowaniem się kartą z pracy
att_rule_baseRuleStartEnd=Godzina pracy obliczona od zalogowania się kartą do wylogowania się kartą z pracy
att_rule_countConvertHour=Postawa przeliczania na godzinę
att_rule_formulaHour=Wzór: godzina pracy=ilość minut/60
att_rule_countConvertDay=Podstawa przeliczania na dzień
att_rule_formulaDay=Wzór: ilość dni=ilość min/ Ilość minut na dzień do przepracowania
att_rule_inFormulaShallPrevail=Licząc wynik obliczenia przez wzor;
att_rule_remainderHour=Reszta większa niż lub równa 
att_rule_oneHour=Licząc jako 1 godzina
att_rule_halfAnHour=Licząc jako 0.5 godzina, w przeciwnym razie nie jest brany pod uwagę.
att_rule_remainderDay=Iloraz większy lub równy liczbie minut do przepracowania
att_rule_oneDay=%, licząc jako 1 dzień
att_rule_halfAnDay=%, licząc jako 0.5 dnia, w przeciwnym razie nie jest brany pod uwagę.
att_rule_countConvertAbsentDay=Podstawa przeliczania dni nieobecnych
att_rule_markWorkingDays=Licząc z dni roboczych
att_rule_countConvertDecimal=Dokładna liczba miejsc po przecinku
att_rule_otherSymbol=Ustawienia znaków w raporcie kontroli obecności
att_rule_arrive=Należne/obecne
att_rule_noSignIn=Niezalogowany się
att_rule_noSignOff=Niewylogowany się
att_rule_off=Przeniesienie do innej zmiany w celu rekuperacji
att_rule_class=Nadrobienie zaległości w pracy
att_rule_shortLessLong=Najkrótszy przedział czasu obecności nie może być dłuższy niż najdłuższy przedział czasu obecności!
att_rule_symbolsWarning=Należy ustawić znaki obecności w raporcie, aby nie był pusty!
att_rule_reportSettingSet=Ustawienia eksportowania raportu
att_rule_shortDateFormat=Format daty
att_rule_shortTimeFormat=Format czasu
att_rule_baseRuleSignBreakTime=Czy wymaga logować się w przedziale czasu przerwy
att_leftMenu_custom_rule=Przystosowywanie zasady
att_custom_rule_already_exist={0} już istnieje przystosowywana zasada!
att_add_group_custom_rule=Dodanie zasady grupy
att_custom_rule_type=Typ zasady
att_rule_type_group=Zasada grupy
att_rule_type_dept=Zasada działowa
att_custom_rule_orgNames=Przedmioty do wykorzystania
att_rult_maxOverTimeType1=Bez limitu
att_rult_maxOverTimeType2=W aktualnym tygodniu
att_rult_maxOverTimeType3=W aktualnym miesiącu
att_rule_countConvertDayRemark1=Przykład: czas efektywnej pracy wynosi 500 minut, a liczba godzin, które powinny być przepracowane w ciągu dnia wynosi 480 minut, wynik to 500/480 = 1,04 z 1,0 do ostatniego miejsca po przecinku
att_rule_countConvertDayRemark2=Przykład: czas efektywnej pracy wynosi 500 minut, a liczba godzin, które powinny być przepracowane w ciągu dnia wynosi 480 minut, wynik to 500/480 = 1.04, 1.04 > 0.8 liczy się jako 1 dzień.
att_rule_countConvertDayRemark3=Przykład: czas efektywnej pracy wynosi 300 minut, a liczba godzin, które powinny być przepracowane w ciągu dnia wynosi 480 minut, wynik to 300/480 = 0,625, 0,2<0,625<0,8 liczy się przez pół dnia.
att_rule_countConvertDayRemark4=Podstawa przeliczania dni: liczba dni liczona jako dni robocze w przedziale czasu jest nieważna.
att_rule_countConvertDayRemark5=Licząc jako dni roboty: obowiązuje tylko do rozliczenie nieobecności i należy rozliczyć czas nieobecności według liczby dni roboczy w przedziałach czasu w przypadku nieobecności.
att_rule_baseRuleSmartFindRemark1=Zmiana z najdłuższym czasem: należy rozliczyć czas roboty na postawie czasu logowania w danym dniu i potem znaleźć zmianę z najdłuższym czasem; 
att_rule_baseRuleSmartFindRemark2=Najmniejsza ilość anomalii: należy rozliczyć czas roboty na postawie czasu logowania w danym dniu oraz zmian z najmniejszą ilością anomalii po znalezieniu zmiany z najmniejszą ilością anomalii;
att_rule_baseRuleHourValidator=Ilość minut wyroku w półgodzinie nie może być większa niż lub równa do ilości minut wyroku w jednej godzinie! 
att_rule_baseRuleDayValidator=Ilość minut wyroku w pół dniu nie może być większa niż lub równa do ilości minut wyroku w jednym dniu! 
att_rule_overtimeWarning=Maksymalna liczba godzin nadliczbowych nie może być mniejsza niż minimalna pojedyncza minimalna liczba godzin nadliczbowych!
att_rule_noSignInCountType=W przypadku braku zapisu zalogowania się odnotowywano jako 
att_rule_absent=Nieobecny
att_rule_earlyLeave=Wyjście z pracy wcześnie
att_rule_noSignOffCountType=W przypadku braku zapisu wylogowania się odnotowywano jako 
att_rule_minutes=Min
att_rule_noSignInCountLateMinute=W przypadku braku zalogowania się odnotowywano jako opóźnienie o minuty 
att_rule_noSignOffCountEarlyMinute=W przypadku braku wylogowania się odnotowywano jako wyjście wcześnie z pracy o minuty 
att_rule_incomplete=Niekompletny
att_rule_noCheckInIncomplete=Niekompletne zalogowanie się
att_rule_noCheckOutIncomplete=Niekompletne wylogowanie się
att_rule_lateMinuteWarning=W przypadku braku zalogowania się odnotowywano jako spóźnienie, i czas tego spóźnienia jest większy niż zero i mniejszy niż maksymalny czas trwania okresu kontroli obecności.
att_rule_earlyMinuteWarning=W przypadku braku wylogowania odnotowywano jako wyjście z pracy wcześniej, i czas tego wcześniejszego wyjścia jest większy niż zero i mniejszy niż maksymalny czas trwania okresu kontroli obecności.
att_rule_baseRuleNoSignInCountLateMinuteRemark=Gdy nie meldowanie się jest liczone jako spóźnione, jeśli nie melduje się, będzie liczone jako spóźnienie przez N minut
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=Gdy brak wypisania jest rejestrowany jako wczesny wyjazd, jeśli nie wylogowuje się, jest rejestrowany jako wczesne wyjście na N minut
#Święta
att_holiday_placeholderNo=Sugerowane począwszy od H, np. H1
att_holiday_placeholderName=Proponowany rok + nazwa święta, np. 1 maja 2017 r.
att_holiday_dayNumber=Ilość dni
att_holiday_validDate_msg=Istnieją święta w takim przedziale czasu
#Typ urlop
att_leaveType_leaveThing=Urlop 
att_leaveType_leaveMarriage=Urlop matrymonialny
att_leaveType_leaveBirth=Urlop macierzyński
att_leaveType_leaveSick=Zwolnienie lekarskie
att_leaveType_leaveAnnual=Coroczny urlop
att_leaveType_leaveFuneral=Urlop żałobny
att_leaveType_leaveHome=Wizyta rodzinna
att_leaveType_leaveNursing=Urlop pielęgnacyjny
att_leaveType_isDeductWorkLong=Czy odliczyć godziny pracy
att_leaveType_placeholderNo=Sugerowane począwszy od L, np. L1
att_leaveType_placeholderName=Propozycje kończące się urlopem, np. urlopem małżeńskim
#Rozliczenie w ustalonym czasie
att_timingcalc_timeCalcFrequency=Okres obliczeniowy
att_timingcalc_timeCalcInterval=Czas obliczenia w ustalonym czasie
att_timingcalc_timeSet=Ustawienia czasu obliczenia w ustalonym czasie
att_timingcalc_timeSelect=Należy wybrać czas
att_timingcalc_optionTip=Należy zachować co najmniej jedną uzasadnioną dzienną kalkulację obecności w określonym czasie
#Automatycznie eksportowania raportu
att_autoExport_reportType=Typ raportu
att_autoExport_fileType=Typ dokumentu
att_autoExport_fileName=Nazwa dokumentu
att_autoExport_fileDateFormat=Format daty
att_autoExport_fileTimeFormat=Format czasu
att_autoExport_fileContentFormat=Format treści
att_autoExport_fileContentFormatTxt=Przykład: {deptName}00{personPin}01{personName}02{attDatetime}03\r\n
att_autoExport_timeSendFrequency=Częstotliwość wysłania
att_autoExport_timeSendInterval=Odstęp czasu między kolejnymi przesyłami
att_autoExport_emailType=Typ odbiorcy maila
att_autoExport_emailRecipients=Odbiorca maila
att_autoExport_emailAddress=Adres mailowy 
att_autoExport_emailExample=Przykład: <EMAIL>,<EMAIL>
att_autoExport_emailSubject=Tytuł maila 
att_autoExport_emailContent=Treść maila
att_autoExport_field=Pole
att_autoExport_fieldName=Nazwa pola
att_autoExport_fieldCode=Kod pola
att_autoExport_reportSet=Ustawienia raportu
att_autoExport_timeSet=Ustawienia czasu wysłania maila
att_autoExport_emailSet=Ustawienia maila
att_autoExport_emailSetAlert=Należy wprowadzić adres mailowy
att_autoExport_emailTypeSet=Ustawienia odbiorcy
att_autoExport_byDay=Według dnia
att_autoExport_byMonth=Według miesiąca
att_autoExport_byPersonSet=Ustawienia według personelu
att_autoExport_byDeptSet=Ustawienia według działu
att_autoExport_byAreaSet=Ustawienia według strefy
att_autoExport_emailSubjectSet=Ustawienia tytułu
att_autoExport_emailContentSet=Ustawienia treści
att_autoExport_timePointAlert=Należy wybrać prawidłowy punkt czasowy do wysłania
att_autoExport_lastDayofMonth=Ostatni dzień każdego miesiąca
att_autoExport_firstDayofMonth=W pierwszym dniu miesiąca
att_autoExport_dayofMonthCheck=Dokładna data
att_autoExport_dayofMonthCheckAlert=Należy wybrać dokładną datę
att_autoExport_chooseDeptAlert=Należy wybrać dział!
att_autoExport_sendFormatSet=Ustawienia metody wysłania
att_autoExport_sendFormat=Metoda wysłania
att_autoExport_mailFormat=Metody wysłania mailowego
att_autoExport_ftpFormat=Metoda wysłania ftp
att_autoExport_sftpFormat=Metoda wysłania sftp
att_autoExport_ftpUrl=Adres serwera ftp
att_autoExport_ftpPort=Port serwera ftp
att_autoExport_ftpTimeSet=Ustawienia czasu wysłania ftp
att_autoExport_ftpParamSet=Ustawienia parametrów ftp
att_autoExport_ftpUsername=ID użytkownika ftp
att_autoExport_ftpPassword=Hasło ftp
att_autoExport_correctFtpParam=Należy wprowadzić parametry fpt poprawnie
att_autoExport_correctFtpTestParam=W trakcie przetestowaniu połączenia, należy utrzymać komunikację poprawnie
att_autoExport_inputFtpUrl=Należy wprowadzić adres serwera
att_autoExport_inputFtpPort=Należy wprowadzić port serwera
att_autoExport_ftpSuccess=Skuteczne połączenie
att_autoExport_ftpFail=Należy upewnić się, że Ustawienia parametrów jest poprawne
att_autoExport_validFtp=Należy wprowadzić upoważniony adres serwera
att_autoExport_validPort=Należy wprowadzić upoważniony port serwera
att_autoExport_selectExcelTip=Należy wybrać typ dokumentu jako EXCEL, a format treści jako wszystkie pola!
#=====================================================================
#Przedział czasu
att_timeSlot_periodType=Typ przedziału czasowego
att_timeSlot_normalTime=Standardowy okres czasu
att_timeSlot_elasticTime=Elastyczny okres czasu
att_timeSlot_startSignInTime=Godzina rozpoczęcia zalogowania się 
att_timeSlot_toWorkTime=Godzina rozpoczęcia pracy
att_timeSlot_endSignInTime=Godzina zakończenia zalogowania
att_timeSlot_allowLateMinutes=Dopuszczalne spóźnienie o minut
att_timeSlot_isMustSignIn=Trzeba zalogować się
att_timeSlot_startSignOffTime=Godzina rozpoczęcia wylogowania
att_timeSlot_offWorkTime=Godzina zakończenia pracy
att_timeSlot_endSignOffTime=Godzina zakończenia wylogowania
att_timeSlot_allowEarlyMinutes=Dopuszczalne wcześniejsze wyjście o minut
att_timeSlot_isMustSignOff=Trzeba wylogować się
att_timeSlot_workingHours=Czas pracy (min)
att_timeSlot_isSegmentDeduction=Czy obliczenie się w przedziale czasu
att_timeSlot_startSegmentTime=Rozpoczęcie czasu okresu
att_timeSlot_endSegmentTime=Zakończenie czasu okresu
att_timeSlot_interSegmentDeduction=Obliczenie w czasie okresu (min)
att_timeSlot_markWorkingDays=Ilość dni roboczych 
att_timeSlot_isAdvanceCountOvertime=Czy wcześniejsze zalogowanie naliczy się do godziny nadliczbowych
att_timeSlot_signInAdvanceTime=Zalogowanie jest wcześniej niż
att_timeSlot_isPostponeCountOvertime=Czy przedłużenie wylogowanie naliczy się do godziny nadliczbowych
att_timeSlot_signOutPosponeTime=Wylogowanie jest później niż 
att_timeSlot_isCountOvertime=Czy liczy się do godziny nadliczbowych
att_timeSlot_timeSlotLong=Godziny pracy muszą być zgodne z przedziałami kontroli obecności określonymi w zasadach.
att_timeSlot_alertStartSignInTime=Czas rozpoczęcia zalogowania się jest wcześniejszy niż czas rozpoczęcia pracy
att_timeSlot_alertEndSignInTime=Czas rozpoczęcia pracy jest wcześniejszy niż czas zakończenia wylogowania się 
att_timeSlot_alertStartSignInAndEndSignIn=Czas zakończenia zalogowania się jest wcześniejszy niż czas rozpoczęcia wylogowania
att_timeSlot_alertStartSignOffTime=Czas rozpoczęcia wylogowania się jest wcześniejszy niż czas zakończenia pracy
att_timeSlot_alertEndSignOffTime=czas zakończenia pracy jest wcześniejszy niż czas zakończenia wylogowania
att_timeSlot_alertStartUnequalEnd=Czas rozpoczęcia przedziału nie może się równać do czasu zakończenia przedziału
att_timeSlot_alertStartSegmentTime=Czas rozpoczęcia przedziału jest wcześniejszy niż czas zakończenia przedziału
att_timeSlot_alertStartAndEndTime=Czas rozpoczęcia pracy jest wcześniejszy niż czas rozpoczęcia przedziału
att_timeSlot_alertEndAndoffWorkTime=Czas zakończenia przedziału jest wcześniejszy niż czas zakończenia pracy
att_timeSlot_alertToWorkLesserAndEndTimeLesser=Czas rozpoczęcia pracy jest wcześniejszy niż czas rozpoczęcia przedziału, a czas zakończenia przedziału jest wcześniejszy czas zakończenia pracy
att_timeSlot_alertLessSignInAdvanceTime=Czas do wcześniejszego zalogowania się musi być wcześniejszy niż czas rozpoczęcia pracy
att_timeSlot_alertMoreSignInAdvanceTime=Liczba minut zalogowania się przed rozpoczęciem pracy doliczonych do godziny nadliczbowej jest mniejsza niż liczba minut przed rozpoczęciem pracy
att_timeSlot_alertMoreSignOutPosponeTime=Liczba minut wylogowania się po zakończeniu pracy doliczonych do godziny nadliczbowej jest mniejsza niż liczba minut po zakończeniu pracy
att_timeSlot_alertLessSignOutPosponeTime=Czas do późniejszego wylogowania się musi być późniejszy niż czas zakończenia pracy
att_timeSlot_time=Należy wprowadzić poprawny format czasu 
att_timeSlot_alertMarkWorkingDays=Liczba doliczona do dni roboczych nie może być pusta!
att_timeSlot_placeholderNo=Sugerowane począwszy od T, np. T01
att_timeSlot_placeholderName=Sugerowane począwszy od T
att_timeSlot_beforeToWork=Przed rozpoczęciem pracy
att_timeSlot_afterToWork=Po rozpoczęciu pracy
att_timeSlot_beforeOffWork=Przez zakończeniem pracy
att_timeSlot_afterOffWork=Po zakończeniu pracy
att_timeSlot_minutesSignInValid=Zalogowanie w ciągu minut jest ważne
att_timeSlot_toWork=Rozpoczęcie pracy
att_timeSlot_offWork=Zakończenie pracy
att_timeSlot_minutesSignInAsOvertime=Zalogowanie się minut wcześniej liczy się do nadgodziny
att_timeSlot_minutesSignOutAsOvertime=Po minutach liczy się do nadgodziny
att_timeSlot_minOvertimeMinutes=Najmniejsza ilość minut pracy nadliczbowej
att_timeSlot_enableWorkingHours=Czy włączyć godzinę pracy
att_timeSlot_eidtTimeSlot=Edytowanie przedziału czasowego
att_timeSlot_browseBreakTime=Przeglądanie przedziału czasowego przerwy
att_timeSlot_addBreakTime=Dodanie przedziału czasowego przerwy
att_timeSlot_enableFlexibleWork=Włączenie elastycznej godziny pracy 
att_timeSlot_advanceWorkMinutes=Można rozpocząć pracę wcześniej
att_timeSlot_delayedWorkMinutes=Można rozpocząć pracę później
att_timeSlot_advanceWorkMinutesValidMsg1=Liczba minut przed rozpoczęciem pracy jest większa niż liczba minut dostępnych do rozpoczęcia pracy wcześniej
att_timeSlot_advanceWorkMinutesValidMsg2=Liczba minut dostępnych do wcześniejszego rozpoczęcia pracy jest mniejsza niż liczba minut przed rozpoczęciem pracy
att_timeSlot_advanceWorkMinutesValidMsg3=Liczba minut dostępnych do wcześniejszego rozpoczęcia pracy jest mniejsza niż lub róźna do liczby minut liczenia do nadgodziny przed rozpoczęciem pracy
att_timeSlot_advanceWorkMinutesValidMsg4=Liczba minut zalogowania się przed rozpoczęciem pracy rozliczając do nadgodziny jest niemniejsza niż liczba minut dostępnych do wcześniejszego rozpoczęcia pracy 
att_timeSlot_delayedWorkMinutesValidMsg1=Liczba minut po zakończeniu pracy jest większa niż liczba minut dostępnych do rozpoczęcia pracy później
att_timeSlot_delayedWorkMinutesValidMsg2=Liczba minut dostępnych do rozpoczęcia pracy później jest mniejsza niż liczba minut po zakończeniu pracy
att_timeSlot_delayedWorkMinutesValidMsg3=Liczba minut dostępnych do rozpoczęcia pracy później jest niewiększa niż liczba minut wylogowania się po zakończeniu pracy rozliczając do nadgodziny
att_timeSlot_delayedWorkMinutesValidMsg4=Liczba minut wylogowania się po zakończeniu pracy rozliczając do nadgodziny jest niemniejsza niż liczba minut dostępnych do późniejszego rozpoczęcia pracy 
att_timeSlot_allowLateMinutesValidMsg1=Dopuszczona liczba minut spóźnienia do pracy jest mniejsza niż liczba minut po rozpoczęciu pracy
att_timeSlot_allowLateMinutesValidMsg2=Liczba minut po rozpoczęciu pracy jest mniejsza niż dopuszczona liczba minut spóźnienia do pracy
att_timeSlot_allowEarlyMinutesValidMsg1=Dopuszczona liczba minut wcześniej wychodzenia z pracy jest mniejsza niż liczba minut przed zakończeniem pracy
att_timeSlot_allowEarlyMinutesValidMsg2=Liczba minut przed zakończeniem pracy jest większa niż dopuszczona liczba minut wcześniej wychodzenia z pracy
att_timeSlot_timeOverlap=Czas {0} i {1} nakłada się, należy zmienić przedział czaasowy wybrany! 
att_timeSlot_atLeastOne=Co najmniej 1 okres odpoczynku!
att_timeSlot_mostThree=Do 3 okresów odpoczynku!
att_timeSlot_canNotEqual=Czas rozpoczęcia okresu odpoczynku nie może być równy godzinie zakończenia!
att_timeSlot_shoudInWorkTime=Upewnij się, że okres odpoczynku przypada w godzinach pracy!
att_timeSlot_repeatBreakTime=Powtórz okres odpoczynku!
att_timeSlot_toWorkLe=Czas rozpoczęcia pracy jest mniejszy niż najwcześniejszy czas rozpoczęcia wybranego przedziału czasowego przerwy: 
att_timeSlot_offWorkGe=Czas zakończenia pracy jest większy niż najpóźniejszy czas zakończenia wybranego przedziału czasowego przerwy:
att_timeSlot_crossDays_toWork=Najwcześniejszy czas rozpoczęcia przedziału czasowego przerwy musi być w przedziale czasowym 
att_timeSlot_crossDays_offWork=Najpóźniejszy czas zakończenia przedziału czasowego przerwy musi być w przedziale czasowym 
att_timeSlot_allowLateMinutesRemark=Zalogowanie się w okresie od czasu rozpoczęcia pracy do minut dopuszczalnych próźniejszego rozpoczęcia pracy liczy się do normalnego zalogowania 
att_timeSlot_allowEarlyMinutesRemark=Wylogowanie się w okresie od czasu zakończenia pracy do minut dopuszczalnych wcześniejszego zakończenia pracy liczy się do normalnego wylogowania 
att_timeSlot_isSegmentDeductionRemark=Czy odjąć przedział czasowy przerwy w przedziale? 
att_timeSlot_attEnableFlexibleWorkRemark1=W godzinie pracy elastycznej nie zależy ustawić liczby minut dopuszczonych do spóźnienia lub wcześniej wychodzenia z pracy
att_timeSlot_afterToWorkRemark=Liczba minut po rozpoczęciu pracy się równa liczbę minut dopuszczonych do późniejszego rozpoczęcia pracy
att_timeSlot_beforeOffWorkRemark=Liczba minut przed rozpoczęciem pracy się równa liczbę minut dopuszczonych do wcześniejszego rozpoczęcia pracy
att_timeSlot_attEnableFlexibleWorkRemark2=Liczba minut po zakonczeniu pracy jest niemniejsza niż czas zakończenia pracy lub liczba minut dopuszczonych do późniejszego zakończenia pracy 
att_timeSlot_attEnableFlexibleWorkRemark3=Liczba minut dostępnych do wcześniejszego rozpoczęcia pracy musi być mniejsza niż lub równa do liczby minut do rozliczenia nadgodziny po rozpoczęcia pracy N minut
att_timeSlot_attEnableFlexibleWorkRemark4=Liczba minut dostępnych do późniejszego rozpoczęcia pracy musi być mniejsza niż lub równa do liczby minut do rozliczenia nadgodziny po zakończeniu pracy N minut
att_timeSlot_attBeforeToWorkAsOvertimeRemark=Przykład: w przypadku rozpoczęcia pracy o 9.00 i zalogowania się na 60 minut przed rozpoczęciem pracy zalicza do nadgodzin, czyli zalogowanie się przed 8.00 do 8.00 będzie zaliczone do nadgodzin.
att_timeSlot_attAfterOffWorkAsOvertimeRemark=Przykład: w przypadku zakończenia pracy o 18.00 i wylogowania się na 60 minut po zakończeniu pracy zalicza do nadgodzin, czyli wylogowanie się po 19.00 będzie zaliczone do nadgodzin.
att_timeSlot_longTimeValidRemark=Uzasadniony czas zalogowania się do pracy i uzasadniony czas wylogowania się z pracy nie mogą być nakadane w przedziałach czasowych 
att_timeSlot_advanceWorkMinutesValidMsg5=Uzasadniona liczba minut zalogowania się przed zakończeniem pracy jest mniejsza niż liczba minut dopuszczalnych do wcześniejszego rozpoczęcia pracy
att_timeSlot_advanceWorkMinutesValidMsg6=Liczba minut dopuszczalnych do wcześniejszego rozpoczęcia pracy jest mniejsza niż uzasadniona liczba minut zalogowania się przed zakończeniem pracy
att_timeSlot_delayedWorkMinutesValidMsg5=Uzasadniona liczba minut zalogowania się po rozpoczęciem pracy jest większa niż liczba minut dopuszczalnych do późniejszego rozpoczęcia pracy
att_timeSlot_delayedWorkMinutesValidMsg6=Liczba minut dopuszczalnych do późniejszego rozpoczęcia pracy jest mniejsza niż uzasadniona liczba minut zalogowania się po rozpoczęciem pracy
att_timeSlot_advanceWorkMinutesValidMsg7=Czas zameldowania przed pracą nie może pokrywać się z czasem wymeldowania po wyjściu z pracy dzień wcześniej
att_timeSlot_delayedWorkMinutesValidMsg7=Czas wymeldowania po pracy nie może pokrywać się z godziną zameldowania przed pracą następnego dnia
att_timeSlot_maxOvertimeMinutes=Ogranicz maksymalną liczbę godzin nadliczbowych
#Zmiana
att_shift_basicSet=Podstawowe Ustawienia
att_shift_advancedSet=Ustawienia zaawansowane
att_shift_type=Typ zmiany
att_shift_name=Nazwa zmiany
att_shift_regularShift=Standardowa zmiana
att_shift_flexibleShift=Elastyczna zmiana
att_shift_color=Kolor
att_shift_periodicUnit=Jednostka
att_shift_periodNumber=Cyrk
att_shift_startDate=Data rozpoczęcia
att_shift_startDate_firstDay=Data rozpoczęcia w cyklu
att_shift_isShiftWithinMonth=Zmiana w ciągu miesiąca
att_shift_attendanceMode=Typ kontroli obecności
att_shift_shiftNormal=Przesunięcie karty według normalnej zmiany
att_shift_oneDayOneCard=Dowolnie raz przesunięcie karty w ciągu jednego dnia
att_shift_onlyBrushTime=Tylko obliczenie czas między przesunięciami karty      
att_shift_notBrushCard=Bez odczytywania karty
att_shift_overtimeMode=Typ pracy nadliczbowej
att_shift_autoCalc=Automatyczne obliczenie przez komputera
att_shift_mustApply=Należy złożyć wniosek o pracę nadliczbową.
att_shift_mustOvertime=Należy pracować w godzinach nadliczbowych lub być liczona jako nieobecność w pracy
att_shift_timeSmaller=Notowano z krótszego czasu 
att_shift_notOvertime=Niedoliczona do pracy nadliczbowej
att_shift_overtimeSign=Znak pracy nadliczbowej
att_shift_normal=w czasie pokoju
att_shift_restday=Dzień bez pracy
att_shift_timeSlotDetail=Szczegół przedziałów czasowych
att_shift_doubleDeleteTimeSlot=Należy kliknąć dwukrotnie na przedział czasowy zmiany, aby go usunąć.
att_shift_addTimeSlot=Dodanie przedziału czasowego
att_shift_cleanTimeSlot=Skasowanie przedziału czasowego
att_shift_NO=
att_shift_notAll=Odznaczanie wszystkich
att_shift_notTime=Pole wyboru Szczegóły przedziału czasowego jest odznaczone, co oznacza jego nakładanie się.
att_shift_notExistTime=Nie istnieje taki przedział czasowy!
att_shift_cleanAllTimeSlot=Na pewno skasować przedział czasowy wybranych zmian? 
att_shift_pleaseCheckBox=Należy najpierw zaznaczyć pola wyboru na liście po lewej stronie, które odpowiadają aktualnej godzinie wyświetlanej po prawej stronie.
att_shift_pleaseUnit=Należy wprowadzić jednostkę oraz ilość cyklu. 
att_shift_pleaseAllDetailTimeSlot=Należy wybrać szczegół przedziału czasowego
att_shift_placeholderNo=Sugerowane począwszy od S, np. S01
att_shift_placeholderName=Sugerowane począwszy od S i na końcu zmiany 
att_shift_workType=Typ pracy
att_shift_normalWork=Standardowa praca
att_shift_holidayOt=Praca nadliczbowa podczas święta
att_shift_attShiftStartDateRemark=Przykład: Data rozpoczęcia cyklu to 22, przy czym 3 dni w cyklu, więc 22/23/24 odpowiedają na zmiany A /B/C, a 19/20/21 odpowiadają zmiany A /B/C, i tak dalej.
att_shift_isShiftWithinMonthRemark1=Przy zmianach cyklicznych w ciągu miesiąca, cykle tylko idzie do ostatniego dnia każdego miesiąca, bez ciągłego rozkładu zmiany przypadających na kolejnym miesiącu
att_shift_isShiftWithinMonthRemark2=Przy zmianach cyklicznych bez limitu ciągu miesiącu, w przypadku cykle idącego do ostatniego dnia każdego miesiąca i nie skończy, należy rozplanować rozkład zmiany do kolejnego miesiącu itd. 
att_shift_workTypeRemark1=Praca normalna, odnotowywanie nadgodzin za wyjątkiem świąt, obecność nie jest wymagana w dniach świątecznych. Jeżeli godziny nadliczbowe są oznaczone jako święto, to godziny nadliczbowe są odnotowywane jako święto (z wyłączeniem normalnych godzin pracy).
att_shift_workTypeRemark2=Praca nadliczbowa w weekend, domyślne odnotowywanie nadgodzin jako w dniach wolnych od pracy i automatycznie komputer rolizczy godziny nadliczbowe, nie wymaga ządania pracy nadgodziny. Czas pracy w danym dniu rolicza się do nadgodziny i obecność nie jest wymagana w przyadku świąt. 
att_shift_workTypeRemark3=Praca nadliczbowa podczas świąt, domyślne odnotowywanie nadgodzin jako w dniach świątecznych i automatycznie komputer rolizczy godziny nadliczbowe, nie wymaga ządania pracy nadgodziny. Czas pracy w danym dniu rolicza się do nadgodziny.
att_shift_attendanceModeRemark1=Z wyjątkiem normalnego odczytywania kart według zmian, nie rozliczy do nadgodziny wcześniejszej lub późniejszej, np.
att_shift_attendanceModeRemark2=1. Nie jest wymagana odprawa lub ważna karta jest używana raz dziennie, nie są liczone nadgodziny;
att_shift_attendanceModeRemark3=2. Typ pracy: praca standardowa, typ kontroli obecności: bez odczytywania karty,
att_shift_periodStartMode=Typ rozpoczęcia cyklu
att_shift_periodStartModeByPeriod=Data rozpoczęcia w cyklu
att_shift_periodStartModeBySch=Data rozpoczęcia planowania rozkładu zmiany
att_shift_addTimeSlotToShift=Czy dodać przedział czasowy tej zmiany
#=====================================================================
#Grupowanie
att_group_editGroup=Edytowanie personelu do grupy
att_group_browseGroupPerson=Przeglądanie personelu do grupy
att_group_list=Lista grup
att_group_placeholderNo=Sugerowane począwszy od G, np. G1
att_group_placeholderName=Sugerowane począwszy od G i na końcu grupy
att_widget_deptHint=Uwaga: Importowanie wszystkich personelu w wybranych działach
att_widget_searchType=Wyszukiwanie z warunkami
att_widget_noPerson=Nikt nie został wybrany. 
#Zmiany w grupach
#Zmiany w działu
att_deptSch_existsDept=Istnieją zmiany w danym dziale, nie można działu usunąć. 
#Zmiany personelu
att_personSch_view=Przeglądanie zmian personelu
#Zmiany tymczasowe
att_schedule_type=Typ zmiany
att_schedule_tempType=Typ zmiany tymczasowej
att_schedule_normal=Standardowe zmiany
att_schedule_intelligent=Inteligentne zmiany
att_tempSch_scheduleType=Typ zmiany
att_tempSch_startDate=Data rozpoczęcia
att_tempSch_endDate=Data zakończenia 
att_tempSch_attendanceMode=Typ kontroli obecności
att_tempSch_overtimeMode=Typ pracy nadliczbowej
att_tempSch_overtimeRemark=Znak pracy nadliczbowej
att_tempSch_existsDept=Istnieje planowanie zmiany w danym dziale, nie można działu usunąć. 
att_schedult_opAddTempSch=Dodanie zmiany tymczasowej
att_schedule_cleanEndDate=Skasowanie czasu zakończenia
att_schedule_selectOne=Standardowa zmiana, należy tylko wybrać jedną!
att_schedule_selectPerson=Należy najpierw wybrać personelu!
att_schedule_selectDept=Należy najpierw wybrać dział!
att_schedule_selectGroup=Należy najpierw wybrać grupę!
att_schedule_selectOneGroup=Należy tylko wybrać jedną grupę!
att_schedule_arrange=Należy wybrać zmianę!
att_schedule_leave=Urlop
att_schedule_trip=Delegacja
att_schedule_out=Poza biurem
att_schedule_off=Urlop 
att_schedule_makeUpClass=Uzupełnienie pracy
att_schedule_class=Zmiana zmiany
att_schedule_holiday=Święto 
att_schedule_offDetail=Przeniesienie do innej zmiany w celu rekuperacji
att_schedule_makeUpClassDetail=Nadrobienie zaległości w pracy
att_schedule_classDetail=Zmiana rozkładu zmiany
att_schedule_holidayDetail=Święta
att_schedule_noSchDetail=Bez zmiany 
att_schedule_normalDetail=Standardowo
att_schedule_normalSchInfo=Zmiana wyśrodkowana: zmiana niemiędzydniowa
att_schedule_multipleInterSchInfo=Zmiana oddzielona przecinkiem: wiele zmian międzydniowych
att_schedule_inderSchFirstDayInfo=Zmiana jest przesunięta do tyłu przez ramki: oznaczony jako dzień 1 dla zmiany międzydniowej.
att_schedule_inderSchSecondDayInfo=Zmiana jest przesunięta do przodu przez ramki: oznaczony jako dzień 2 dla zmiany międzydniowej
att_schedule_timeConflict=Konflikt z istniejącymi przydziałami czasowymi zmiany, jego zapisywanie jest niedozwolone!
#=====================================================================
att_excp_notExisetPerson=Personel nie istnieje! 
att_excp_leavePerson=Personel już zrezygnował! 
#Formularz do dodawania zalogowania
att_sign_signTime=Czas otrzymania karty
att_sign_signDate=Data otrzymania karty
#Złożenie wniosku o nieobecność
att_leave_arilName=Nazwa urlopu
att_leave_image=Zdjęcie wniosku urlopowego
att_leave_imageShow=Brak obrazu
att_leave_imageType=Błąd: format jest nieprawidłowy, obsługiwany jest format obrazu JPEG、GIF、PNG!
att_leave_imageSize=Błąd: Wybrany obraz jest zbyt duży, rozmiar obrazu obsługuje do 4M!
att_leave_leaveLongDay=Czas urlopu (dni)
att_leave_leaveLongHour=Czas urlopu (godzin)
att_leave_leaveLongMinute=Czas urlopu (minut)
att_leave_endNoLessAndEqualStart=Czas zakończenia nie może być mniejszy lub równy czasowi rozpoczęcia
att_leave_typeNameNoExsists=Fałszywa nazwa klasy nie istnieje
att_leave_startNotNull=Czas rozpoczęcia nie może być pusty
att_leave_endNotNull=Godzina zakończenia nie może być pusta
att_leave_typeNameConflict=Fałszywa nazwa typu powoduje konflikt z nazwą statusu obecności
#Delegacja
att_trip_tripLongDay=Czas delegacji (dni)
att_trip_tripLongMinute=Czas delegacji (minut)
att_trip_tripLongHour=Czas delegacji (godzin)
#Praca poza biurem
att_out_outLongDay=Czas pracy poza biurem (dni)
att_out_outLongMinute=Czas pracy poza biurem (minut)
att_out_outLongHour=Czas pracy poza biurem (godzin)
#Praca nadliczbowa
att_overtime_type=Typ pracy nadliczbowej
att_overtime_normal=Praca nadliczobowa w dniach roboczych
att_overtime_rest=Praca nadliczobowa w dniach wolnych od pracy
att_overtime_overtimeLong=Czas pracy nadliczbowej (minut)
att_overtime_overtimeHour=Czas pracy nadliczbowej (godzin)
att_overtime_notice=Czas pracy nadliczbowej do żądania nie przekracza jednego dnia!
att_overtime_minutesNotice=Czas pracy nadliczbowej do żądania nie może być mniejszy niż najkrótszy czas pracy nadliczbowej!
#Odczytywania w celu uzupełnienia pracy
att_adjust_type=Typ zmiany
att_adjust_adjustDate=Data zmiany
att_adjust_shiftName=Zmiana do uzupełnienia
att_adjust_selectClass=Należy wybrać nazwę zmiany do uzupełnienia!
att_shift_notExistShiftWorkDate=Data poprawki nie jest w datach pracy zmiany do uzupełnienia, jej dodanie jest niedozwolone
att_adjust_shiftPeriodStartMode=Wybrana zmiana do uzupełnienia, w przypadku daty rozpoczęcia według rozkładu zmian, należy wybrać domyślnie zerową
att_adjust_shiftNameNoNull=Uzupełniające zmiany nie mogą być puste
att_adjust_shiftNameNoExsist=Zmiana makijażu nie istnieje
#Zmiana rozkładu zmiany
att_class_type=Rodzaj zmiany zmiany
att_class_sameTimeMoveShift=Zmiana osobista w tym samym dniu
att_class_differenceTimeMoveShift=Zmiana osobista w różnych datach
att_class_twoPeopleMove=Wymiania zmiany między dwoma osobami
att_class_moveDate=Wymiana daty
att_class_shiftName=Nazwa zmiany do zmiany 
att_class_moveShiftName=Nazwa zmian do wymiany
att_class_movePersonPin=Nazywa personeli do wymiany
att_class_movePersonName=Imię I nazwisko personeli do wymiany
att_class_movePersonLastName=Nazwisko personeli do wymiany
att_class_moveDeptName=Nazwa działów personeli do wymiany
att_class_personPin=Numer personelu nie może być pusty
att_class_shiftNameNoNull=Zmiana do zmiany nie może być pusta
att_class_personPinNoNull=Numer personeli do wymiany nie może być pusty
att_class_isNotExisetSwapPersonPin=Nie istnieje numer personeli do wymiany, należy dodać ponownie!
att_class_personNoSame=Personel do zmiany i personel do wymiany nie mogą być tacy sami, należy dodać ponownie!
att_class_outTime=Odstęp między datą do zmiany a datą do wymiany nie przekracza jednego miesiąca! 
att_class_shiftNameNoExsist=Zmiana korygująca nie istnieje
att_class_swapPersonNoExisist=Matchmaker nie istnieje
att_class_dateNoSame=Osoby są przenoszone w różnych terminach, daty nie mogą być takie same
#=====================================================================
#Węzły 
att_node_name=Węzły
att_node_type=Typ węzła 
att_node_leader=Bezpośrednie przełożony 
att_node_leaderNode=Węzeł meibezpośredniego przełożonego
att_node_person=Oznaczony personel
att_node_position=Oznaczone stanowisko 
att_node_choose=Wybieranie stanowiska 
att_node_personNoNull=Pozycja personelu nie może być pusta
att_node_posiitonNoNull=Pozycja stanowiska nie może być pusta
att_node_placeholderNo=Sugerowane począwszy od N, np. N01
att_node_placeholderName=Sugerowane począwszy od stanowiska lub imię i nazwiska, w końcu węzła, np. węzeł kierownika
att_node_searchPerson=Należy wprowadzić warunki do wyszukiwania
att_node_positionIsExist=Aktualne stanowisko już istnieje w danych węzłów, należy wybrać ponownie
#Procedura
att_flow_type=Typ procedury
att_flow_rule=Zasada procedury
att_flow_rule0=Nie dłużej niż 1 dzień
att_flow_rule1=Dłuższy niż 1 dzień i krótszy niż lub równy 3 dni
att_flow_rule2=Dłuższy niż 3 dzień i krótszy niż lub równy 7 dni
att_flow_rule3=Dłużej niż 7 dni
att_flow_node=Węzły zatwierdzenia
att_flow_start=Rozpoczęcie procedury
att_flow_end=Zakończenia procedury
att_flow_addNode=Dodanie węzła
att_flow_placeholderNo=Sugerowane począwszy od F, np. F01
att_flow_placeholderName=Sugerowane począwszy od Typu i na końcu procedura, np. procedura urlopu
att_flow_tips=Uwaga: Węzły są zatwierdzane w kolejności od góry do dołu, a po ich wybraniu można przeciągnąć kolejność.
#Składanie wniosku
att_apply_personPin=Numer wnioskodawcy
att_apply_type=Typ anomalii 
att_apply_flowStatus=Stan całkowity procedury
att_apply_start=Rozpoczęcie składania wniosku
att_apply_flowing=W procedurze
att_apply_pass=zatwierdzone
att_apply_over=Zakończone
att_apply_refuse=Odrzucone
att_apply_revoke=wycofane
att_apply_except=Anomalia
att_apply_view=Przeglądanie szczegółu
att_apply_leaveTips=Istnieje żądanie urlopowe od personelu w danym przedziale czasowym! 
att_apply_tripTips=Istnieje żądanie delegacji od personelu w danym przedziale czasowym! 
att_apply_outTips=Istnieje żądanie pracy poza biurem od personelu w danym przedziale czasowym! 
att_apply_overtimeTips=Istnieje żądanie pracy nadliczbowej od personelu w danym przedziale czasowym! 
att_apply_adjustTips=Istnieje żądanie odczytywania i uzupełnienia pracy od personelu w danym przedziale czasowym! 
att_apply_classTips=Istnieje żądanie zmiany zmiany od personelu w danym przedziale czasowym! 
#Zatwierdzenie
att_approve_wait=W oczekiwaniu na zatwierdzenie
att_approve_refuse=Niezatwierdzone
att_approve_reason=Przyczyna
att_approve_personPin=Numer personelu zatwierdzającego
att_approve_personName=Imię i nazwisko personelu zatwierdzającego
att_approve_person=Personel zatwierdzający
att_approve_isPass=Czy został zatwierdzony
att_approve_status=Stan aktualnego węzła 
att_approve_tips=Dany węzeł czasu już istnieje w zapisie procesie i jego powtarzalne podanie jest niedozwolone
att_approve_tips2=Węzeł procesu nie został ustalony, należy skontaktować się z Administratorem
att_approve_offDayConflicts=Podanie przeniesienie pracy na inną zmianę jest niedozwolone w dniach wolnych z pracy 
att_approve_shiftConflicts=Podanie uzupełnienia pracy jest niedozwolone w dniach pracy
att_approve_shiftNoSch=Podanie uzupełnienia pracy jest niedozwolone w dniach bez zmian
att_approve_classConflicts=Podanie przeniesienie pracy na inną zmianę jest niedozwolone w dniach bez zmian
att_approve_selectTime=Proces został określone według zasady po wprowadzeniu czasu
att_approve_withoutPermissionApproval=Istnieje przepływ pracy bez pozwolenia na zatwierdzenie, sprawdź!
#=====================================================================
#Rozliczenie obecności 
att_op_calculation=Rozliczenie obecności
att_op_calculation_notice=Istnieją dane obecności do rozliczenia za kulisami, należy spróbować później! 
att_op_calculation_leave=Zawiera zrezygnowanych personelu
att_statistical_choosePersonOrDept=Należy wybrać dział lub personelu!
att_statistical_sureCalculation=Na pewno wykonać rozliczenie obecności? 
att_statistical_filter=Warunki do przefiltrowania są gotowe!
att_statistical_initData=Inicjalizowanie danych podstawowych jest zakończone!
att_statistical_exception=Inicjalizowanie danych anomalii jest zakończone!
att_statistical_error=Awaria rozliczenia obecności!
att_statistical_begin=Rozpoczęcie rozliczenia obecności!
att_statistical_end=Zakończenie rozliczenia obecności!
att_statistical_noticeTime=Okres czasu do wybrania: ostatnie dwa miesiące do obecnego dnia!
att_statistical_remarkHoliday=Święto
att_statistical_remarkClass=Zmiana 
att_statistical_remarkNoSch=Weekend 
att_statistical_remarkRest=Urlop
#Raport oryginalnego wpisów
att_op_importAccRecord=Importowanie zapisów kontroli dostępu
att_op_importParkRecord=Importowanie zapisów parkingu
att_op_importInsRecord=Importowanie zapisów ekranu informacyjnego
att_op_importPidRecord=Importowanie zapisów dopasowania dokumentu do człowieka 
att_op_importVmsRecord=Importowanie zapisów wideo
att_op_importUSBRecord=Importowanie zapisów pendrive’a
att_transaction_noAccModule=Brak modułu kontroli dostępu!
att_transaction_noParkModule=Brak modułu parking!
att_transaction_noInsModule=Brak modułu ekranu informacyjnego!
att_transaction_noPidModule=Brak modułu dopasowania dokumentu do człowieka!
att_transaction_exportRecord=Eksportuj oryginalne rekordy
att_transaction_exportAttPhoto=Eksportuj zdjęcia obecności
att_transaction_fileIsTooLarge=Wyeksportowany plik jest za duży, proszę zawęzić zakres dat
att_transaction_exportDate=Data eksportu
att_statistical_attDatetime=Godzina I data kontroli dostępu
att_statistical_attPhoto=Zdjęcie kontroli dostępu
att_statistical_attDetail=Szczegół kontroli dostępu
att_statistical_acc=Sprzęt kontroli dostępu
att_statistical_att=Urządzenie kontroli obecności w pracy
att_statistical_park=Urządzenie parking
att_statistical_faceRecognition=Urządzenie rozpoznania twarzy
att_statistical_app=Urządzenie komórki
att_statistical_vms=Urządzenie wideo
att_statistical_psg=Wyposażenie kanału
att_statistical_dataSources=Źródło danych
att_transaction_SyncRecord=Synchronizuj rekordy obecności
#Tabela szczegółowa logowania dziennego
att_statistical_dayCardDetail=Wpisz szczegóły
att_statistical_cardDate=Data odczytywania karty
att_statistical_cardNumber=Ilość odczytywania karty
att_statistical_earliestTime=Najwcześniejszy czas
att_statistical_latestTime=Najpóźniejszy czas
att_statistical_cardTime=Czas odczytywania karty
#Raport nieobecności
att_statistical_leaveDetail=Szczegół wniosku urlopu
#Raport dzienny logowania
att_statistical_attDate=Data kontroli obecności w pracy
att_statistical_week=Tydzień
att_statistical_shiftInfo=Informacja zmiany 
att_statistical_shiftTimeData=Czas rozpoczęcie i zakończenia pracy
att_statistical_cardValidData=Dane logowania
att_statistical_cardValidCount=Ilość odczytywania karty
att_statistical_lateCount=Ilość spóźnienia do pracy
att_statistical_lateMinute=Liczba minut spóźnienia do pracy
att_statistical_earlyCount=Ilość wcześniejszego wyjścia z pracy
att_statistical_earlyMinute=Liczba minut wcześniejszego wyjścia z pracy
att_statistical_countData=Dane ilości
att_statistical_minuteData=Dane minut 
att_statistical_attendance_minute=Obecny (min)
att_statistical_overtime_minute=Praca nadliczbowa (min)
att_statistical_unusual_minute=Anomalia (min)
#Raport miesięczny logowania
att_monthdetail_should_hour=Zależna (godzina)
att_monthdetail_actual_hour=Rzeczywista (godzina)
att_monthdetail_valid_hour=Uzasadniona (godzina)
att_monthdetail_absent_hour=Nieobecna (godzina)
att_monthdetail_leave_hour=Urlop (godzina)
att_monthdetail_trip_hour=Delegacja (godzina)
att_monthdetail_out_hour=Poza biurem (godzina)
att_monthdetail_should_day=Zależny (dni)
att_monthdetail_actual_day=Rzeczywista obecność (dni)
att_monthdetail_valid_day=Ważna obecność (dni)
att_monthdetail_absent_day=Nieobecność (dni)
att_monthdetail_leave_day=Urlop (dni)
att_monthdetail_trip_day=Delegacja (dni)
att_monthdetail_out_day=Poza biurem (dni)
#Raport statystyczny miesięczny
att_statistical_late_minute=Godzina (godz.)
att_statistical_early_minute=Godzina (godz.)
#Raport statystyczny działu
#Raport statystyczny roczny
att_statistical_should=Godzina do przepracowania
att_statistical_actual=Rzeczywista godzina
att_statistical_valid=Ważna godzina
att_statistical_numberOfTimes=Częstotliwość
att_statistical_usually=w czasie pokoju
att_statistical_rest=Dni wolny od pracy
att_statistical_holiday=Święto
att_statistical_total=Suma
att_statistical_month=Miesiąc 
att_statistical_year=Rok
att_statistical_attendance_hour=Godzina obecna (godz.)
att_statistical_attendance_day=Godzina obecna (dni)
att_statistical_overtime_hour=Nadgodzina (godz.)
att_statistical_unusual_hour=Anomalia (godz.)
att_statistical_unusual_day=Anomalia (dni)
#Parametry urządzenia kontroli obecności
att_deviceOption_query=Przeglądanie parametrów urządzenia
att_deviceOption_noOption=Brak informacji parametrów, należy najpierw pobrać parametry urządzenia.
att_deviceOption_name=Nazwa parametru
att_deviceOption_value=Wartość parametru
att_deviceOption_UserCount=Ilość użytkowników aktualnych
att_deviceOption_MaxUserCount=Maksymalna ilość użytkowników aktualnych
att_deviceOption_FaceCount=Aktualna ilość formy twarzy
att_deviceOption_MaxFaceCount=Maksymalna ilość formy twarzy
att_deviceOption_FacePhotoCount=Aktualna ilość obrazu twarzy
att_deviceOption_MaxFacePhotoCount=Maksymalna ilość obrazu twarzy
att_deviceOption_FingerCount=Aktualna ilość formy odcisku palca
att_deviceOption_MaxFingerCount=Maksymalna ilość formy odcisku palca
att_deviceOption_FingerPhotoCount=Aktualna ilość obrazu odcisku palca
att_deviceOption_MaxFingerPhotoCount=Maksymalna ilość obrazu odcisku palca
att_deviceOption_FvCount=Aktualna ilość formy żyły palcowej
att_deviceOption_MaxFvCount=Maksymalna ilość formy żyły palcowej
att_deviceOption_FvPhotoCount=Aktualna ilość obrazu żyły palcowej
att_deviceOption_MaxFvPhotoCount=Maksymalna ilość obrazu żyły palcowej
att_deviceOption_PvCount=Aktualna ilość formy odbitki dłoni
att_deviceOption_MaxPvCount=Maksymalna ilość formy odbitki dłoni
att_deviceOption_PvPhotoCount=Aktualna ilość obrazu odbitki dłoni
att_deviceOption_MaxPvPhotoCount=Maksymalna ilość obrazu odbitki dłoni
att_deviceOption_TransactionCount=Aktualna ilość zapisów
att_deviceOption_MaxAttLogCount=Maksymalna ilość zapisów
att_deviceOption_UserPhotoCount=Aktualna ilość zdjęcia użytkownika 
att_deviceOption_MaxUserPhotoCount=Maksymalna ilość zdjęcia użytkownika
att_deviceOption_FaceVersion=Wersja algorytmu rozpoznania twarzy
att_deviceOption_FPVersion=Wersja algorytmu rozpoznania odcinka palca 
att_deviceOption_FvVersion=Wersja algorytmu rozpoznania żyły palcowej
att_deviceOption_PvVersion=Wersja algorytmu rozpoznania odbitki dłoni
att_deviceOption_FWVersion=Wersja oprogramowania układowego
att_deviceOption_PushVersion=Wersja Push
#=====================================================================
#API
att_api_areaCodeNotNull=Numer strefy nie może być pusty
att_api_pinsNotNull=Dane pins nie mogą być puste
att_api_pinsOverSize=Długość danych pins nie może przekraczać 500
att_api_areaNoExist=Nie istnieje strefa
att_api_sign=Uzupełnienie logowania się
#=====================================================================
#Przedział czasowy przerwy 
att_leftMenu_breakTime=Przedział czasowy przerwy
att_breakTime_startTime=Czas rozpoczęcia 
att_breakTime_endTime=Czas zakończenia 
#=====================================================================
#Importowanie zapisów pendrive’a
att_import_uploadFileSuccess=Plik został przesłany pomyślnie, a dane z pliku zostały przeanalizowane, należy poczekać
att_import_resolutionComplete=Dane zostały przeanalizowane, rozpocznie aktualizację bazy danych.
att_import_snNoExist=Nie istnieje urządzenie kontroli obecności od pliku importowanego, należy wybrać plik.
att_import_fileName_msg=Wymaganie formatu pliku importowanego: zaczyna się od numeru seryjnego urządzenia oddzielono podkreśleniem "_", np. "3517171600001_attlog.dat".
att_import_notSupportFormat=Ten format nie jest obecnie obsługiwany!
att_import_selectCorrectFile=Należy wybrać plik z prawidłowym formatem!
att_import_fileFormat=Format pliku
att_import_targetFile=Plik celowy
att_import_startRow=Liczba rzędu od nagłówka
att_import_startRowNote=Pierwszy rząd formatu danych to dane do importu, należy sprawdzić plik przed importowaniem.
att_import_delimiter=Delimiter
#=====================================================================
#Dziennik obsługiwania urządzenia
att_device_op_log_op_type=Kod operacyjny Kod
att_device_op_log_dev_sn=Numer seryjny urządzenia.
att_device_op_log_op_content=Treść operacyjna
att_device_op_log_operator_pin=Numer operatora
att_device_op_log_operator_name=Imię i nazwisko operatora
att_device_op_log_op_time=Czas obsługiwania
att_device_op_log_op_who_value=Wartości obiektu operacji
att_device_op_log_op_who_content=Opis działania obiektu operacji
att_device_op_log_op_value1=Obiekt operacji nr 2.
att_device_op_log_op_value_content1=Opis działania obiektu operacji 2
att_device_op_log_op_value2=Obiekt operacji nr 3.
att_device_op_log_op_value_content2=Opis działania obiektu operacji 3
att_device_op_log_op_value3=Obiekt operacji nr 4.
att_device_op_log_op_value_content3=Opis działania obiektu operacji 4
#Rodzaj operacji w dzienniku operacyjnym
att_device_op_log_opType_0=Uruchomienie (komputer)
att_device_op_log_opType_1=Wyłączenie (maszyny lub urządzenia)
att_device_op_log_opType_2=Niepowodzenie weryfikacji
att_device_op_log_opType_3=Alarm
att_device_op_log_opType_4=Przejście do menu
att_device_op_log_opType_5=Zmiana ustawień
att_device_op_log_opType_6=Odciski palców zapisywane
att_device_op_log_opType_7=kod rejestracyjny
att_device_op_log_opType_8=Rejestracja kart HID
att_device_op_log_opType_9=Usunięcie użytkownika
att_device_op_log_opType_10=Usunięcie nacisków palców
att_device_op_log_opType_11=Usunięcie hasła
att_device_op_log_opType_12=Usunięcie karty radiowej
att_device_op_log_opType_13=Skasowanie danych
att_device_op_log_opType_14=Dodanie karty MF
att_device_op_log_opType_15=Zapisywanie karty MF
att_device_op_log_opType_16=Zarejestrowanie karty MF
att_device_op_log_opType_17=Usunięcie rejestru karty MF
att_device_op_log_opType_18=Skasowanie treści karty MF
att_device_op_log_opType_19=Przeniesienie danych rejestracyjnych do karty
att_device_op_log_opType_20=Skopiowanie danych karty do urządzenia
att_device_op_log_opType_21=Ustawienia czasu
att_device_op_log_opType_22=Ustawienia fabryczne
att_device_op_log_opType_23=Usunięcie zapisów wchodzenia/wychodzenia
att_device_op_log_opType_24=Skasowanie uprawień administratora 
att_device_op_log_opType_25=Edytowanie ustawienia grupy kontroli dostępu
att_device_op_log_opType_26=Edytowanie Ustawienia kontroli dostępu użytkownika
att_device_op_log_opType_27=Edytowanie przedziału czasowego kontroli dostępu
att_device_op_log_opType_28=Edytowanie ustawienia kombinacja przyblokowania I odblokowania 
att_device_op_log_opType_29=Przyblokowanie
att_device_op_log_opType_30=Zapisywanie nowego użytkownika 
att_device_op_log_opType_31=Zmiana atrybucji przycisków palców
att_device_op_log_opType_32=Przymusowy alarm
att_device_op_log_opType_34=Przeciw powrotu
att_device_op_log_opType_35=Usuwanie zdjęć obecności
att_device_op_log_opType_36=Modyfikacja innych informacji o użytkowniku
att_device_op_log_opType_37=Święta
att_device_op_log_opType_38=Odnawianie danych
att_device_op_log_opType_39=Tworzenie kopii zapasowych danych
att_device_op_log_opType_40=Przesyłanie przez pendrive'a USB
att_device_op_log_opType_41=Pobieranie przez pendrive'a USB
att_device_op_log_opType_42=Szyfrowanie obecności na pendrivie USB
att_device_op_log_opType_43=Usuwanie zapisów po udanym pobraniu z pendrive'a USB
att_device_op_log_opType_53=Otwieracz do drzwi
att_device_op_log_opType_54=Magnes bramy
att_device_op_log_opType_55=Alarm
att_device_op_log_opType_56=Odzyskiwanie Parametry
att_device_op_log_opType_68=Rejestrowanie zdjęcia użytkownika 
att_device_op_log_opType_69=Modyfikowanie zdjęcia użytkownika
att_device_op_log_opType_70=Modyfikowanie nazwy użytkownika
att_device_op_log_opType_71=Modyfikowanie uprawnień użytkowników
att_device_op_log_opType_76=Modyfikowanie ustawień sieciowych IP
att_device_op_log_opType_77=Modyfikowanie maski ustawień sieciowych
att_device_op_log_opType_78=Modyfikowanie ustawień sieciowych Gateway
att_device_op_log_opType_79=Modyfikowanie ustawień sieciowych DNS
att_device_op_log_opType_80=Zmiana hasła konfiguracji połączenia
att_device_op_log_opType_81=Modyfikowanie ID urządzenia w ustawieniach połączenia
att_device_op_log_opType_82=Modyfikacja adresu serwera w chmurze
att_device_op_log_opType_83=Modyfikowanie portu serwera w chmurze
att_device_op_log_opType_87=Modyfikowanie ustawień kontroli dostępu
att_device_op_log_opType_88=Modyfikowanie oznaczeń parametrów twarzy
att_device_op_log_opType_89=Modyfikowanie oznaczeń parametru odcisku palca
att_device_op_log_opType_90=Modyfikowanie oznaczeń parametrów żyły palcowej
att_device_op_log_opType_91=Modyfikowanie oznaczeń parametrów odbitki dłoni
att_device_op_log_opType_92=Znak uaktualizacji przez pendrive’a USB
att_device_op_log_opType_100=Modyfikowanie informacji o karcie RF
att_device_op_log_opType_101=Zarejestrowanie twarzy
att_device_op_log_opType_102=Modyfikowanie uprawień personelu
att_device_op_log_opType_103=Usunięcie uprawień personelu
att_device_op_log_opType_104=Dodawanie uprawień personelu
att_device_op_log_opType_105=Usunięcie zapisu kontroli dostępu
att_device_op_log_opType_106=Usunięcie rejestru twarzy
att_device_op_log_opType_107=Usunięcie zdjęcia personelu
att_device_op_log_opType_108=Modyfikowanie parametrów
att_device_op_log_opType_109=Wybrano WIFISSID
att_device_op_log_opType_110=Umożliwienie proxy
att_device_op_log_opType_111=Zmiana IP proxy
att_device_op_log_opType_112=Zmiana portu proxy 
att_device_op_log_opType_113=Zmiana hasła personelu
att_device_op_log_opType_114=Modyfikowanie informacji twarzy
att_device_op_log_opType_115=Zmiana hasła operatora 
att_device_op_log_opType_116=Odzyskiwanie ustawiania kontroli dostępu
att_device_op_log_opType_117=Wprowadzone hasło operatora nie jest prawidłowe
att_device_op_log_opType_118=Hasło operatora zostanie zablokowane 
att_device_op_log_opType_120=Zmień długość danych karty legalnej
att_device_op_log_opType_121=Zarejestruj żyłę palca
att_device_op_log_opType_122=Modyfikuj żyłę palca
att_device_op_log_opType_123=Usuń żyłę palca
att_device_op_log_opType_124=Zarejestruj Palm
att_device_op_log_opType_125=Zmodyfikuj Palm
att_device_op_log_opType_126=Usuń Palm
#Opis działania obiektu operacji
att_device_op_log_content_pin=Numer użytkownika:
att_device_op_log_content_alarm=Alarm:
att_device_op_log_content_alarm_reason=Przyczyna alarmu:
att_device_op_log_content_update_no=L. p. opcji do zmiany:
att_device_op_log_content_update_value=Wartości opcji do zmiany:
att_device_op_log_content_finger_no=Numer odcisku palca:
att_device_op_log_content_finger_size=Długość formy odcisku palca:
#=====================================================================
#Przepływ pracy
att_flowable_datetime_to=Do 
att_flowable_todomsg_leave=Zatwierdzenie urlopu
att_flowable_todomsg_sign=Zatwierdzenie uzupełnienia logowania się
att_flowable_todomsg_overtime=Zatwierdzenie pracy nadliczbowej
att_flowable_notifymsg_leave=Zawiadomienie o urlopie
att_flowable_notifymsg_sign=Zawiadomienie o uzupełnieniu logowania się
att_flowable_notifymsg_overtime=Zawiadomienie o godzinach nadliczbowych
att_flowable_shift=Zmiana:
att_flowable_hour=Godzina 
att_flowable_todomsg_trip=Sprawdzenie i zatwierdzenie delegacji
att_flowable_notifymsg_trip=Zawiadomienie o delegacji
att_flowable_todomsg_out=Sprawdzenie i zatwierdzenie pracy poza biurem
att_flowable_notifymsg_out=Zawiadomienie o pracy poza biurem
att_flow_apply=Składanie wniosku
att_flow_applyTime=Czas podaniawniosku
att_flow_approveTime=Czas zatwierdzenie 
att_flow_operateUser=Osoba zatwierdzająca 
att_flow_approve=Zatwierdzenie
att_flow_approveComment=Noty na marginesie
att_flow_approvePass=Wynik zatwierdzenia
att_flow_status_processing=W trakcie zatwierdzenia
#=====================================================================
#biotime
att_h5_pers_personIdNull=ID pracownika nie może być pusty
att_h5_attPlaceNull=Punkt logowania się nie może być pusty
att_h5_attAreaNull=Strefa kontroli obecności nie może być pusta
att_h5_pers_personNoExist=Nie istnieje numer pracownika
att_h5_signRemarkNull=Uwaga nie może być pusta
att_h5_common_pageNull=błąd parametru paginacji
att_h5_taskIdNotNull=ID węzły zadania nie może być pusty
att_h5_auditResultNotNull=Wynik zatwierdzenia nie może być pusty
att_h5_latLongitudeNull=Długość i szerokość geograficzna nie mogą być pusty.
att_h5_pers_personIsNull=Nie istnieje ID pracownika
att_h5_pers_personIsNotInArea=Nie ma ustawienia strefy dla obecnego personelu
att_h5_mapApiConnectionsError=Błąd połączenia mapy API
att_h5_googleMap=Mapa Google
att_h5_gaodeMap=Mapa AutoNavi
att_h5_defaultMap=Mapa domyślna 
att_h5_shiftTime=Czas do planowania rozkładu zmiany
att_h5_signTimes=Czas do uzupełnienia logowania
att_h5_enterKeyWords=Należy wprowadzić słowa kluczowe: 
att_h5_mapSet=Ustawienia mapy do kontroli obecności
att_h5_setMapApiAddress=Ustawienia parametrów mapy
att_h5_MapSetWarning=Przełączenie mapy spowoduje, że wpisany adres logowania przez komórkę nie będzie mógł być dopasowany do szerokości i długości geograficznej, należy zachować ostrożność, aby ją zmodyfikować!
att_h5_mapSelect=Wybieranie mamy
att_h5_persNoHire=Pracownik nie został jeszcze zatrudniony w systemie.
att_slef_apiKey=API-KEY
att_self_apiJsKey=API-JS-KEY
att_self_noComputeAtt=Obecność na dzień dzisiejszy nie została uwzględniona
att_self_noSignRecord=Brak przesunięcia karty na dzień dzisiejszy
att_self_imageUploadError=Wysłanie obrazu nie powiodło się
att_self_attSignAddressAreaIsExist=W okolicy znajdują się punkty odprawy.
att_self_signRuleIsError=Aktualny czas do uzupełnienia logowania nie jest w zakresie czasu dopuszczalnego do uzupełnienia logowania
att_self_signAcrossDay=Nie można uzupełnić logowania przy zmianach w dwóch dniach z rzędu!
att_self_todaySignIsExist=Istnieje uzupełnienie logowania na dzień dzisiejszy! 
att_self_signSetting=Ustawienia uzupełnienia logowania
att_self_allowSign=Umożliwienie uzupełnienia logowania: 
att_self_allowSignSuffix=Zapis obecności w dniach
att_self_onlyThisMonth=Tylko w aktualnym miesiącu 
att_self_allowAcrossMonth=Dopuszczalny przypada na kolejnym miesiącu 
att_self_thisTimeNoSch=Brak zmiany w aktualnym przedziału czasowego!
att_self_revokeReason=Przyczyna wycofania:
att_self_revokeHint=Należy wprowadzić przyczynę wycofania w 20 lub mniej znakach do analizowania i zatwierdzenia 
att_self_persSelfLogin=samoobsługowe logowanie pracowników
att_self_isOpenSelfLogin=Czy aktywować portal samoobsługowego logowania pracowników
att_self_applyAndWorkTimeOverlap=Nakładanie się czas podania i godzin pracy
att_apply_DurationIsZero=Czas podania jest 0, składanie jest niedozwolone
att_sign_mapWarn=Mapa nie załadowała się, należy sprawdzić połączenie sieciowe i wartość klucza mapy.
att_admin_applyWarn=Nieskuteczna operacja, istnieje personel wolny od zmiany lub czas podania nie jest w zakresie zmiany! ({0})
att_self_getPhotoFailed=Nie istnieje obraz
att_self_view=Przeglądanie
# Kod dwuwymiarowy
att_param_qrCodeUrl=Kod dwuwymiarowy Url
att_param_qrCodeUrlHref=Adres serwera: port
att_param_appAttQrCode=Kod dwuwymiarowy do kontroli obecności przez komórkę 
att_param_timingFrequency=Odstęp czasu: 5-59 min lub 1-24 godziny 
att_sign_signTimeNotNull=Czas uzupełnienia logowania nie może być pusty
att_apply_overLastMonth=Czas podania przekroczą dwa miesiące 
att_apply_withoutDetail=Brak informacji procesu 
att_flowable_noAuth=Należy przeglądać przez konto super administratora
att_apply_overtimeOverMaxTimeLong=Czas pracy nadliczbowej przekracza maksymalny czas pracy nadliczbowej
# Znak parametrów ustawienia kontroli obecności
att_other_arrive=√
att_other_late=L
att_other_early=E
att_other_absent=□
att_other_noSignIn=[
att_other_noSignOff=]
att_other_leave=∆
att_other_overtime=OT
att_other_off=○
att_other_classes=●
att_other_trip=T
att_other_out=G
att_other_incomplete=├
att_other_outcomplete=┤
# 服务器下发命令
att_devCmd_submitTime=Czas przesłania
att_devCmd_returnedResult=Zwróć wynik
att_devCmd_returnTime=czas zwrotu
att_devCmd_content=Treść polecenia
att_devCmd_clearCmd=Wyczyść listę poleceń
# 实时点名
att_realTime_selectDept=Wybierz dział
att_realTime_noSignPers=Niezarejestrowana osoba
att_realTime_signPers=Zameldowano się
att_realTime_signMonitor=Monitorowanie logowania
att_realTime_signDateTime=Czas zameldowania
att_realTime_realTimeSet=Ustawienie wezwań w czasie rzeczywistym
att_realTime_openRealTime=Włącz wezwanie w czasie rzeczywistym
att_realTime_rollCallEnd=Zakończenie apelu w czasie rzeczywistym
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=Zaplanowano
att_personSch_cycleSch=Planowanie cyklu
att_personSch_cleanCycleSch=Wyczyść harmonogram cyklu
att_personSch_cleanTempSch=Wyczyść tymczasowy harmonogram
att_personSch_personCycleSch=Harmonogram cyklu osoby
att_personSch_deptCycleSch=Harmonogram cykli działu
att_personSch_groupCycleSch=Planowanie cyklu grupy
att_personSch_personTempSch=Tymczasowy harmonogram osoby
att_personSch_deptTempSch=Tymczasowy harmonogram działu
att_personSch_groupTempSch=Tymczasowy harmonogram grupowy
att_personSch_checkGroupFirst=Proszę sprawdzić grupę po lewej lub osobę na liście po prawej, aby działać!
att_personSch_sureDeleteGroup=Czy na pewno chcesz usunąć {0} i harmonogram odpowiadający grupie?
att_personSch_sch=Harmonogram
att_personSch_delSch=Usuń harmonogram
#考勤计算
att_statistical_sureAllCalculate=Czy na pewno obliczyć frekwencję dla całego personelu?
#异常管理
att_exception_downTemplate=Pobierz szablon importu
att_exception_signImportTemplate=Dołączony szablon importu dziennika
att_exception_leaveImportTemplate=Pozostaw szablon importu
att_exception_overtimeImportTemplate=Szablon importu nadgodzin
att_exception_adjustImportTemplate=Dostosuj szablon importu
att_exception_cellDefault=Niewymagane pole
att_exception_cellRequired=Wymagane pole
att_exception_cellDateTime=Wymagane pole, format czasu to rrrr-MM-dd HH: mm: ss, na przykład: 2020-07-07 08:30:00
att_exception_cellLeaveTypeName=Pole wymagane, na przykład: „Urlop okolicznościowy”, „Urlop małżeński”, „Urlop macierzyński”, „Zwolnienie chorobowe”, „Urlop rodzinny”, „Urlop rodzinny”, „Urlop rodzinny”, „Urlop na karmienie piersią”, „Praca Trip ”,„ Going Out ”i tak dalej.
att_exception_cellOvertimeSign=Pole wymagane, dla eaxmple: 'Normalny dogr.', 'Dogr. dzień odpoczynku', 'Dogr. wakacje'
att_exception_cellAdjustType=Wymagane pole, na przykład: „Dostosuj odpoczynek”, „Dołącz obecność”
att_exception_cellAdjustDate=Wymagane pole, format czasu to rrrr-MM-dd, na przykład: 2020-07-07
att_exception_cellShiftName=Pole wymagane, gdy typ korekty to „Dołączanie obecności”
att_exception_refuse=Odrzuć
att_exception_end=Nienormalny koniec
att_exception_delete=Usuń
att_exception_stop=Wstrzymaj
#时间段
att_timeSlot_normalTimeAdd=Dodaj normalny przedział czasowy
att_timeSlot_elasticTimeAdd=Dodaj elastyczny przedział czasowy
#班次
att_shift_addRegularShift=Dodaj zwykłą zmianę
att_shift_addFlexibleShift=Dodaj elastyczne przesunięcie
#参数设置
att_param_notLeaveSetting=Ustawienie obliczania braku urlopu
att_param_smallestUnit=Minimalna jednostka
att_param_workDay=Dzień pracy
att_param_roundingControl=Kontrola zaokrąglania
att_param_abort=Down (discard)
att_param_rounding=zaokrąglanie
att_param_carry=W górę (noś)
att_param_reportSymbol=Wyświetl symbol raportu
att_param_convertCountValid=Wprowadź liczbę i dozwolone jest tylko jedno miejsce po przecinku
att_other_leaveThing=Swobodny
att_other_leaveMarriage=Małżeństwo
att_other_leaveBirth=Macierzyństwo
att_other_leaveSick=Chory
att_other_leaveAnnual=Rocznie
att_other_leaveFuneral=Żałoba
att_other_leaveHome=Strona główna
att_other_leaveNursing=Karmienie piersią
att_other_leavetrip=Biznes
att_other_leaveout=Out
att_common_schAndRest=Harmonogram i odpoczynek
att_common_timeLongs=Długość czasu
att_personSch_checkDeptOrPersFirst=Proszę sprawdzić grupę po lewej lub osobę na liście po prawej, aby działać!
att_personSch_checkCalendarFirst=Najpierw wybierz datę, którą chcesz zaplanować!
att_personSch_cleanCheck=Wyczyść czek
att_personSch_delTimeSlot=Wyczyść wybrany okres
att_personSch_repeatTimeSlotNoAdd=Nie zostanie dodany żaden powtarzający się okres!
att_personSch_showSchInfo=Pokaż szczegóły harmonogramu
att_personSch_sureToCycleSch=Czy na pewno planujesz {0} cyklicznie?
att_personSch_sureToTempSch=Czy na pewno tymczasowo zaplanować {0}?
att_personSch_sureToCycleSchDeptOrGroup=Czy na pewno planujesz cyklicznie {0} planowanie całego personelu?
att_personSch_sureToTempSchDeptOrGroup=Czy na pewno chcesz tymczasowo zaplanować cały personel {0}?
att_personSch_sureCleanCycleSch=Czy na pewno chcesz wyczyścić harmonogram cyklu dla {0} od {1} do {2}?
att_personSch_sureCleanTempSch=Czy na pewno chcesz wyczyścić tymczasowe przesunięcie dla {0} z {1} do {2}?
att_personSch_sureCleanCycleSchDeptOrGroup=Czy na pewno chcesz wyczyścić harmonogram cykli dla całego personelu w okresie {0} od {1} do {2}?
att_personSch_sureCleanTempSchDeptOrGroup=Czy na pewno chcesz wyczyścić tymczasowy harmonogram dla całego personelu w okresie {0} od {1} do {2}?
att_personSch_today=Dzisiaj
att_personSch_timeSoltName=Nazwa okresu czasu
att_personSch_export=Eksportuj planowanie personelu
att_personSch_exportTemplate=Eksportuj szablon tymczasowej zmiany personelu
att_personSch_import=Importuj tymczasowe planowanie personelu
att_personSch_tempSchTemplate=Tymczasowy szablon planowania personelu
att_personSch_tempSchTemplateTip=Wybierz godzinę rozpoczęcia i zakończenia harmonogramu, pobierz szablon harmonogramu w tym dniu
att_personSch_opTip=Instrukcje dotyczące obsługi
att_personSch_opTip1=1. Możesz przeciągnąć przedział czasowy do pojedynczej daty w kontrolce kalendarza, aby zaplanować.
att_personSch_opTip2=2. W kontrolce kalendarza kliknij dwukrotnie pojedynczą datę, aby zaplanować.
att_personSch_opTip3=3. W kontrolce kalendarza naciśnij i przytrzymaj przycisk myszy, aby wybrać wiele dat do zaplanowania.
att_personSch_schRules=Zasady harmonogramu
att_personSch_schRules1=1. Harmonogram cyklu: W ciągu tego samego dnia, późniejszy harmonogram nadpisze poprzedni harmonogram, niezależnie od tego, czy zachodzi na siebie.
att_personSch_schRules2=2.Terminowy harmonogram: Jeśli w tym samym dniu zachodzi nakładanie się, poprzedni harmonogram tymczasowy zostanie nadpisany. Jeśli nie ma nakładania się, będą istnieć jednocześnie.
att_personSch_schRules3=3.Cycle and Temporary: Jeśli w tym samym dniu nastąpi nakładanie się, harmonogram cyklów nadpisze tymczasowy harmonogram, a jeśli nie będzie się nakładać, będzie on również istniał jednocześnie.
att_personSch_schStatus=Stan harmonogramu
#左侧菜单-排班管理
att_leftMenu_schDetails=Szczegóły harmonogramu
att_leftMenu_detailReport=Szczegółowy raport o obecności
att_leftMenu_signReport=Dołączone szczegóły dziennika
att_leftMenu_leaveReport=Zostaw szczegóły
att_leftMenu_abnormal=Raport o nietypowej obecności
att_leftMenu_yearLeaveSumReport=Roczny raport sumy urlopów
att_leave_maxFileCount=Możesz dodać maksymalnie 4 zdjęcia
#时间段
att_timeSlot_add=Ustaw przedział czasowy
att_timeSlot_select=Wybierz przedział czasu!
att_timeSlot_repeat=Okres "{0}" się powtarza!
att_timeSlot_overlapping=Okres "{0}" pokrywa się z czasem pracy "{1}"!
att_timeSlot_addFirst=Najpierw ustaw przedział czasu!
att_timeSlot_notEmpty=Okres odpowiadający numerowi personelu {0} nie może być pusty!
att_timeSlot_notExist=Okres "{1}" odpowiadający numerowi personelu {0} nie istnieje!
att_timeSlot_repeatEx=Okres „{1}” odpowiadający numerowi personelu {0} pokrywa się z czasem pracy „{2}”
att_timeSlot_importRepeat=Okres "{1}" odpowiadający numerowi personelu {0} jest powtarzany
att_timeSlot_importNotPin=W systemie nie ma personelu o numerze {0}!
att_timeSlot_elasticTimePeriod=Numer personelu {0}, nie może importować elastycznego okresu czasu '{1}'!
#导入
att_import_overData=Obecna liczba importów wynosi {0}, przekraczając limit 30 000, proszę importować partiami!
att_import_existIllegalType=Zaimportowany {0} ma niedozwolony typ!
#验证方式
att_verifyMode_0=Automatyczne rozpoznawanie
att_verifyMode_1=Tylko odcisk palca
att_verifyMode_2=Tylko przypnij
att_verifyMode_3=Tylko hasło
att_verifyMode_4=Tylko karta
att_verifyMode_5=Odcisk palca lub hasło
att_verifyMode_6=Odcisk palca lub karta
att_verifyMode_7=Karta lub hasło
att_verifyMode_8=Kod PIN i odcisk palca
att_verifyMode_9=Odcisk palca i hasło
att_verifyMode_10=Karta i odcisk palca
att_verifyMode_11=Karta i hasło
att_verifyMode_12=Odcisk palca, hasło i karta
att_verifyMode_13=Kod PIN i odcisk palca oraz hasło
att_verifyMode_14=(kod PIN i odcisk palca) lub (karta i odcisk palca)
att_verifyMode_15=Twarz
att_verifyMode_16=Twarz i odcisk palca
att_verifyMode_17=Twarz i hasło
att_verifyMode_18=Twarz i karta
att_verifyMode_19=Twarz, odcisk palca i karta
att_verifyMode_20=Twarz, odcisk palca i hasło
att_verifyMode_21=Żyła palca
att_verifyMode_22=Finger Vein i hasło
att_verifyMode_23=Żyła palca i karta
att_verifyMode_24=Finger Vein oraz hasło i karta
att_verifyMode_25=Palmprint
att_verifyMode_26=Palmprint i karta
att_verifyMode_27=Odcisk dłoni i twarz
att_verifyMode_28=Odcisk dłoni i odcisk palca
att_verifyMode_29=Odcisk dłoni, odcisk palca i twarz
# 工作流
att_flow_schedule=Przejrzyj postęp
att_flow_schedulePass=(Pomyślnie)
att_flow_scheduleNot=(W trakcie przeglądu)
att_flow_scheduleReject=(Odrzucony)
# 工作时长表
att_workTimeReport_total=Całkowity czas pracy
# 自动导出报表
att_autoExport_startEndTime=Czas rozpoczęcia i zakończenia
# 年假
att_annualLeave_setting=Ustawienie salda urlopów rocznych
att_annualLeave_settingTip1=Aby skorzystać z funkcji bilansu urlopów rocznych, musisz ustawić czas wejścia dla każdego pracownika; gdy czas wejścia nie jest ustawiony, pozostały urlop roczny pracownika w raporcie bilansu urlopu rocznego jest wyświetlany jako pusty.
att_annualLeave_settingTip2=Jeśli aktualna data jest późniejsza niż data wystawienia rozliczenia, ta modyfikacja wejdzie w życie w następnym roku; jeżeli data bieżąca jest wcześniejsza niż data emisji rozliczenia, gdy data wydania rozliczenia nadejdzie, zostanie ona rozliczona, a urlop roczny ponownie wystawiony.
att_annualLeave_calculate=Coroczne rozliczenie i data wystawienia urlopu
att_annualLeave_workTimeCalculate=Oblicz według wskaźnika czasu pracy
att_annualLeave_rule=Reguła corocznego urlopu
att_annualLeave_ruleCountOver=Osiągnięto maksymalny ustawiony limit liczby
att_annualLeave_years=Senior lata
att_annualLeave_eachYear=Co roku
att_annualLeave_have=Ma
att_annualLeave_days=Dni corocznego urlopu
att_annualLeave_totalDays=Całkowity roczny urlop
att_annualLeave_remainingDays=Pozostały urlop wypoczynkowy
att_annualLeave_consecutive=Ustawienie reguły urlopu rocznego musi być ustawione na kolejne lata
# 年假结余表
att_annualLeave_report=Bilans urlopu rocznego
att_annualLeave_validDate=Ważna data
att_annualLeave_useDays=Użyj {0} dni
att_annualLeave_calculateDays=Wydanie {0} dni
att_annualLeave_notEnough=Niewystarczający roczny urlop {0}!
att_annualLeave_notValidDate={0} nie mieści się w ważnym zakresie corocznego urlopu!
att_annualLeave_notDays={0} nie ma corocznego urlopu!
att_annualLeave_tip1=Zhang San ist am 1. September letzten Jahres beigetreten
att_annualLeave_tip2=Einstellung des Jahresurlaubssaldos
att_annualLeave_tip3=Das Clearing- und Ausstellungsdatum ist der 1. Januar eines jeden Jahres. Es wird durch Abrunden gemäß dem Arbeitsverhältnis berechnet. Wenn die Dienstzeit ≤ 1 ist, gibt es 3 Tage Jahresurlaub, und wenn die Dienstzeit ≤ 1 ist, gibt es 5 Tage Jahresurlaub.
att_annualLeave_tip4=Berechnung des Jahresurlaubs
att_annualLeave_tip5=Letztes Jahr 09-01 ~ 12-31 genießen Sie 4 / 12x3 = 1,0 Tage
att_annualLeave_tip6=Dieses Jahr 01-01 ~ 12-31 genießen 4,0 Tage (dieses Jahr 01-01 ~ 08-31 genießen 8 / 12x3 = 2,0 Tage + dieses Jahr 09-01 ~ 12-31 genießen 4 / 12x5≈2.0 Tage)
# att SDC
att_sdc_name=Sprzęt wideo
att_sdc_wxMsg_firstData=Witaj, masz powiadomienie o meldowaniu się obecności
att_sdc_wxMsg_stateData=Brak poczucia pomyślnego sprawdzenia obecności
att_sdc_wxMsg_remark=Przypomnienie: Ostateczny wynik obecności jest zależny od strony szczegółów zameldowania.
# 时间段
att_timeSlot_conflict=Przedział czasowy koliduje z innymi przedziałami czasowymi dnia
att_timeSlot_selectFirst=Wybierz przedział czasowy
# 事件中心
att_eventCenter_sign=Rejestracja obecności
#异常管理
att_exception_classImportTemplate=Szablon importu klas
att_exception_cellClassAdjustType=Wymagane pole, takie jak: "{0}", "{1}", "{2}"
att_exception_swapDateDate=pole niewymagane, format czasu to rrrr-MM-dd, np.: 2020-07-07
#消息中心
att_message_leave=Zawiadomienie o obecności {0}
att_message_leaveContent={0} przesłano {1}, {2} czas to {3}~{4}
att_message_leaveTime=Czas wyjazdu
att_message_overtime=Zawiadomienie o obecności i nadgodzinach
att_message_overtimeContent={0} przesłano nadgodziny, a nadgodziny to {1}~{2}
att_message_overtimeTime=Czas w nadgodzinach
att_message_sign=Zawiadomienie o obecności
att_message_signContent={0} przesłał dodatkowy znak, a czas dodatkowego podpisania to {1}
att_message_adjust=Zawiadomienie o zmianie frekwencji
att_message_adjustContent={0} przesłał korektę, a data korekty to {1}
att_message_class=Powiadomienie o obecności i zmianie
att_message_classContent=Zawartość klasy
att_message_classContent0={0} przesłał zmianę, data zmiany to {1}, a zmiana to {2}
att_message_classContent1={0} przesłał zmianę, data zmiany to {1}, a data zmiany to {2}
att_message_classContent2={0} ({1}) i {2} ({3}) zamiana klas
#推送中心
att_pushCenter_transaction=Rekord obecności
# 时间段
att_timeSlot_workTimeNotEqual=Czas pracy nie może być równy czasowi zwolnienia z pracy
att_timeSlot_signTimeNotEqual=Początkowy czas logowania nie może być równy końcowemu czasowi wylogowania
# 北向接口A
att_api_notNull={0} nie może być pusty!
att_api_startDateGeEndDate=Czas rozpoczęcia nie może być większy ani równy czasowi zakończenia!
att_api_leaveTypeNotExist=Fałszywy gatunek nie istnieje!
att_api_imageLengthNot2000=Długość adresu obrazu nie może przekraczać 2000!
# 20221230新增国际化
att_personSch_workTypeNotNull=Typ pracy odpowiadający numerowi personelu {0} nie może być pusty!
att_personSch_workTypeNotExist=Typ pracy odpowiadający numerowi personelu {0} nie istnieje!
att_annualLeave_recalculate=Przelicz ponownie
# 20230530新增国际化
att_leftMenu_dailyReport=Dzienny raport obecności
att_leftMenu_overtimeReport=Raport o nadgodzinach
att_leftMenu_lateReport=Spóźniony raport
att_leftMenu_earlyReport=Opuść wcześniejsze zgłoszenie
att_leftMenu_absentReport=Zgłoś nieobecność
att_leftMenu_monthReport=Miesięczny raport frekwencji
att_leftMenu_monthWorkTimeReport=Miesięczny raport czasu pracy
att_leftMenu_monthCardReport=Miesięczny raport karty
att_leftMenu_monthOvertimeReport=Miesięczny raport o nadgodzinach
att_leftMenu_overtimeSummaryReport=Raport podsumowujący nadgodziny personelu
att_leftMenu_deptOvertimeSummaryReport=Raport podsumowujący nadgodziny działu
att_leftMenu_deptLeaveSummaryReport=Raport podsumowujący urlopy w dziale
att_annualLeave_calculateDay=Liczba rocznych dni urlopu
att_annualLeave_adjustDay=Dostosuj dni
att_annualLeave_sureSelectDept=Czy na pewno chcesz wykonać operację {0} na wybranym dziale?
att_annualLeave_sureSelectPerson=Czy na pewno chcesz wykonać operację {0} na wybranej osobie?
att_annualLeave_calculateTip1=Podczas obliczania według stażu pracy: obliczenie urlopu rocznego jest dokładne co do miesiąca, jeśli staż pracy wynosi 10 lat i 3 miesiące, wówczas do obliczeń zostanie użytych 10 lat i 3 miesiące;
att_annualLeave_calculateTip2=Kiedy przeliczenie nie jest oparte na stażu pracy: obliczenie urlopu rocznego jest dokładne dla roku, jeśli staż pracy wynosi 10 lat i 3 miesiące, wówczas do obliczeń zostanie użytych 10 lat;
att_rule_isInCompleteTip=Priorytet jest najwyższy, gdy brak logowania lub wylogowania jest rejestrowany jako niekompletny, a spóźnienia, wcześniejsze urlopy, nieobecność i ważne
att_rule_absentTip=Gdy brak zalogowania lub wylogowania jest rejestrowany jako nieobecność, długość nieobecności jest równa długości godzin pracy pomniejszonej o długość późnego lub wcześniejszego urlopu
att_timeSlot_elasticTip1=0, efektywny czas jest równy rzeczywistemu czasowi, brak absencji
att_timeSlot_elasticTip2=Jeśli rzeczywisty czas jest dłuższy niż czas pracy, efektywny czas jest równy czasowi pracy, bez absencji
att_timeSlot_elasticTip3=Jeśli rzeczywisty czas trwania jest krótszy niż czas pracy, efektywny czas trwania jest równy rzeczywistemu czasowi trwania, a absencja jest równa czasowi pracy minus rzeczywisty czas trwania
att_timeSlot_maxWorkingHours=Godziny pracy nie mogą przekraczać
# 20231030
att_customReport=Niestandardowy raport obecności
att_customReport_byDayDetail=Szczegółowo według dnia
att_customReport_byPerson=Podsumowanie według osoby
att_customReport_byDept=Podsumowanie według działu.
att_customReport_queryMaxRange=Maksymalny zakres zapytania to cztery miesiące.
# 20240630新增国际化
att_personSch_shiftWorkTypeTip1=1. W przypadku pracy nadgodzin w normalne dni pracy/odpoczynku priorytet harmonogramu jest niższy niż w przypadku wakacji
att_personSch_shiftWorkTypeTip2=2. Gdy rodzaj pracy jest nadgodzinami w czasie wakacji, priorytet harmonogramu jest wyższy niż priorytet wakacji
att_personVerifyMode=Metoda weryfikacji personelu
att_personVerifyMode_setting=Ustawienia metody weryfikacji
att_personSch_importCycSch=Import harmonogramu cyklu personelu
att_personSch_cycSchTemplate=Szablon harmonogramu cyklu personelu
att_personSch_exportCycSchTemplate=Pobierz szablon harmonogramu cyklu personelu
att_personSch_scheduleTypeNotNull=Typ Shift nie może być pusty lub nie istnieje!
att_personSch_shiftNotNull=Zmiana nie może być pusta!
att_personSch_shiftNotExist=Zmiana nie istnieje!
att_personSch_onlyAllowOneShift=Regularny harmonogram pozwala zaplanować tylko jedną zmianę!
att_shift_attShiftStartDateRemark2=Tydzień, w którym znajduje się data rozpoczęcia cyklu, to pierwszy tydzień; Miesiąc, w którym znajduje się data rozpoczęcia cyklu, to pierwszy miesiąc.
#打卡状态
att_cardStatus_setting=Ustawienia statusu pracowych
att_cardStatus_name=Nazwa
att_cardStatus_value=Wartość
att_cardStatus_alias=Alias
att_cardStatus_every_day=Codziennie
att_cardStatus_by_week=W okresie tygodniowym
att_cardStatus_autoState=Stan automatyczny
att_cardStatus_attState=Stan pracowy
att_cardStatus_signIn=Zaloguj się
att_cardStatus_signOut=Wyloguj się
att_cardStatus_out=wychodzić
att_cardStatus_outReturn=Powrót po opuszczeniu
att_cardStatus_overtime_signIn=Zaloguj się do pracy dodatkowej
att_cardStatus_overtime_signOut=Wyloguj się z pracy dodatkowej
# 20241030新增国际化
att_leaveType_enableMaxDays=Aktywacja ograniczeń rocznych
att_leaveType_maxDays=Ograniczenie roczne (dni)
att_leaveType_applyMaxDays=Zgłoszenie roczne nie może przekroczyć {0} dni
att_param_overTimeSetting=Ustawienia poziomów godzin opłaty pracu
att_param_overTimeLevel=Poziom godzin opłaty pracu (godziny)
att_param_overTimeLevelEnable=Czy włączyć obliczenia poziomów godzin opłaty pracu?
att_param_reportColor=Kolor wyświetlania raportu
# APP
att_app_signClientTip=Tento urządzenie już zostało zarejestrowane przez innego użytkownika dzisiaj
att_app_noSignAddress=Zakres kontroli nie skonfigurowany, zwróć się do admina aby go ustawić
att_app_notInSignAddress=Nie docelowość w punkcie kontroli, brak możliwości skreślenia
att_app_attendance=Moje obowiązywanie
att_app_apply=Aplikacja na obowiązywanie
att_app_approve=Moje zatwierdzenia
# 20250530
att_node_leaderNodeExist=Księgowa już jest etapa zgody bezpośredniego nadrzędu
att_signAddress_init=Inicjowanie mapy
att_signAddress_initTips=Wprowadź klucz mapy i inicjuj mapę do wyboru adresu