#系统名称
att_systemName=근태관리 시스템 1.0
#=====================================================================
#左侧菜单
att_module=근태
#左侧菜单-考勤设备
att_leftMenu_attendanceDevice=근태 장치
att_leftMenu_device=장치
att_leftMenu_point=근태 포인트
att_leftMenu_sign_address=App 체크인 주소
att_leftMenu_adms_devCmd=서버 Cmd
#左侧菜单-基础信息
att_leftMenu_basicInformation=기본 정보
att_leftMenu_rule=근태 설정
att_leftMenu_base_rule=기본 규칙
att_leftMenu_department_rule=부서 규칙
att_leftMenu_holiday=휴일 설정
att_leftMenu_leaveType=휴가 설정
att_leftMenu_timingCalculation=시간 계산
att_leftMenu_autoExport=보고서 이메일 발송
att_leftMenu_param=근태 설정
#左侧菜单-班次管理
att_leftMenu_shiftManagement=근무규칙 스케줄 관리
att_leftMenu_timeSlot=근무 규칙
att_leftMenu_shift=근무규칙 스케줄
#左侧菜单-排班管理
att_leftMenu_scheduleManagement=근무규칙 설정
att_leftMenu_group=근무규칙 그룹 설정
att_leftMenu_groupPerson=그룹화
att_leftMenu_groupSch=근무규칙 적용 (그룹)
att_leftMenu_deptSch=근무규칙 적용 (부서)
att_leftMenu_personSch=근무규칙 적용 (사용자)
att_leftMenu_tempSch=근무규칙 적용 (임시 사용자)
att_leftMenu_nonSch=설정되지 않은 사용자
#左侧菜单-异常管理
att_leftMenu_exceptionManagement=예외 이력 
att_leftMenu_sign=근태 수정
att_leftMenu_leave=휴가 신청
att_leftMenu_trip=출장
att_leftMenu_out=외근
att_leftMenu_overtime=초과 근무
att_leftMenu_adjust=추가 및 수정
att_leftMenu_class=근무 규칙 변경
#左侧菜单-统计报表
att_leftMenu_statisticalReport=통계 보고서
att_leftMenu_manualCalculation=수동 계산
att_leftMenu_transaction=근무 이력 (상세내역)
att_leftMenu_dayCardDetailReport=업무 이력 (인증이력)
att_leftMenu_leaveSummaryReport=휴가 요약 보고서
att_leftMenu_dayDetailReport=일별 보고서
att_leftMenu_monthDetailReport=사용자 상세 보고서 (월별)
att_leftMenu_monthStatisticalReport=사용자 통계 보고서 (월별)
att_leftMenu_deptStatisticalReport=부서 통계 보고서 (월별)
att_leftMenu_yearStatisticalReport=사용자 통계 보고서 (연도별)
att_leftMenu_attSignCallRollReport=근태 확인 보고서
att_leftMenu_workTimeReport=근태 시간 보고서
#左侧菜单-设备操作日志
att_leftMenu_device_op_log=장치 설정 이력
#左侧菜单-实时点名
att_leftMenu_realTime_callRoll=근태 확인
#=====================================================================
#公共
att_common_person=사용자
att_common_pin=사용자 ID
att_common_group=그룹
att_common_dept=부서
att_common_symbol=기호
att_common_deptNo=부서 번호
att_common_deptName=부서명
att_common_groupNo=그룹 번호
att_common_groupName=그룹명
att_common_operateTime=설정 시간
att_common_operationFailed=설정 실패
att_common_id=신분증
att_common_deptId=부서 번호
att_common_groupId=그룹 번호
att_common_deviceId=장치 ID
att_person_pin=사용자 ID
att_person_name=이름
att_person_lastName=업체
att_person_internalCard=카드 번호
att_person_attendanceMode=근태 모드
att_person_normalAttendance=정상 근태
att_person_noPunchCard=인증로그 없음
att_common_attendance=근태
att_common_attendance_hour=근태 (시간)
att_common_attendance_day=근태 (일자)
att_common_late=지각
att_common_early=조퇴
att_common_overtime=초과 근무
att_common_exception=예외 이력
att_common_absent=결근
att_common_leave=휴가
att_common_trip=출장
att_common_out=외근
att_common_staff=사용자
att_common_superadmin=관리자
att_common_msg=메시지 내용
att_common_min=지속시간 (분)
att_common_letterNumber=숫자 또는 문자만 입력 가능합니다
att_common_relationDataCanNotDel=관련 데이터를 삭제할 수 없습니다
att_common_relationDataCanNotEdit=관련 데이터를 수정할 수 없습니다
att_common_needSelectOneArea=구역을 선택하십시오
att_common_neesSelectPerson=사용자를 선택하십시오
att_common_nameNoSpace=이름은 공백을 포함할 수 없습니다
att_common_digitsValid=소수점 이하 두 자리 이하의 숫자만 입력할 수 있습니다
att_common_numValid=숫자만 입력하십시오
#=====================================================================
#工作面板
att_dashboard_worker=근무지
att_dashboard_today=당일 근태
att_dashboard_todayCount=당일 통계
att_dashboard_exceptionCount=예외이력 통계 (당월)
att_dashboard_lastWeek=마지막 주
att_dashboard_lastMonth=마지막 달
att_dashboard_perpsonNumber=전체 인원
att_dashboard_actualNumber=인증한 사용자
att_dashboard_notArrivedNumber=인증하지 않은 사용자
att_dashboard_attHour=근무 시간
#区域
#设备
att_op_syncDev=장치에 프로그램 데이터 동기화
att_op_account=근태 로그
att_op_check=데이터 동기화
att_op_deleteCmd=장치 삭제
att_op_dataSms=신규 간단 메시지
att_op_clearAttPic=사용자 사진 삭제
att_op_clearAttLog=근태로그 삭제
att_device_waitCmdCount=실행 대기 중인 Cmd
att_device_status=장치 상태
att_device_register=장치 등록
att_device_isRegister=등록 장치
att_device_existNotRegDevice=등록 장치가 아닙니다 / 데이터를 가져올 수 없습니다
att_device_fwVersion=펌웨어 버전
att_device_transInterval=새로 고침 (분)
att_device_cmdCount=장치 연결 개수 초과
att_device_delay=이력 확인시간 (초)
att_device_timeZone=시간대
att_device_operationLog=설정 로그
att_device_registeredFingerprint=지문 등록
att_device_registeredUser=사용자 등록
att_device_fingerprintImage=지문 이미지
att_device_editUser=사용자 편집
att_device_modifyFingerprint=지문 수정
att_device_faceRegistration=얼굴 등록
att_device_userPhotos=사용자 사진
att_device_attLog=근태 로그 업로드
att_device_operLog=사용자 정보 업로드
att_device_attPhoto=사용자 등록 사진 업로드
att_device_isOnLine=온라인 상태
att_device_InputPin=사용자 번호 입력
att_device_getPin=선택한 사용자 정보 가져오기
att_device_separatedPin=쉼표로 구분된 여러 명의 사용자 번호
att_device_authDevice=승인된 장치
att_device_disabled=장치가 비활성화되어 사용할 수 없습니다
att_device_autoAdd=신규 장치가 자동으로 추가됩니다
att_device_receivePersonOnlyDb=데이터베이스에있는 사용자 데이터만 수신
att_devMenu_control=장치 제어
att_devMenu_viewOrGetInfo=정보확인 / 가져오기
att_devMenu_clearData=장치 데이터 삭제
att_device_disabledOrOffline=비활성화 상태이거나 온라인 상태가 아니므로 작동할 수 없습니다
att_device_areaStatus=장치 구역 상태
att_device_areaCommon=구역이 정상입니다
att_device_areaEmpty=구역이 비어 있습니다
att_device_isRegDev=시간대 또는 사용자 상태를 수정하려면 장치를 재시작해야 합니다
att_device_canUpgrade=다음 장치를 업그레이드 할 수 있습니다
att_device_offline=다음 장치는 오프라인 상태이며 작동할 수 없습니다
att_device_oldProtocol=Old 프로토콜
att_device_newProtocol=New 프로토콜
att_device_noMoreTwenty=Old 프로토콜 장치 펌웨어 업그레이드 패키지는 20M을 초과할 수 없습니다
att_device_transferFilesTip=펌웨어가 감지되었습니다 / 파일을 전송하십시오
att_op_clearAttPers=장치 사용자 삭제
#区域人员
att_op_forZoneAddPers=사용자 구역 설정
att_op_dataUserSms=개별 간단 메시지 추가
att_op_syncPers=장치 동기화
att_areaPerson_choiceArea=구역을 설정합니다
att_areaPerson_byAreaPerson=구역별
att_areaPerson_setByAreaPerson=구역별 사용자 설정
att_areaPerson_importBatchDel=일괄 삭제 가져오기
att_areaPerson_syncToDevSuccess=설정 완료 되었습니다 / 장치에 전송 중이오니 기다려 주십시오
att_areaPerson_personId=사용자 ID
att_areaPerson_areaId=구역 ID
att_area_existPerson=해당 구역 내 사용자가 설정되어 있습니다
att_areaPerson_notice1=구역 또는 사용자를 동시에 비워둘 수 없습니다
att_areaPerson_notice2=구역 또는 사용자가 검색되지 않았습니다
att_areaPerson_notice3=구역 내 설정된 장치가 없습니다
att_areaPerson_addArea=구역 추가
att_areaPerson_delArea=구역 삭제
att_areaPerson_persNoExit=등록되지 않은 사용자
att_areaPerson_importTip1=가져온 사용자가 사용자 관리 모듈에 있는지 확인하십시오
att_areaPerson_importTip2=일괄 가져오기 기능은 장치에 자동으로 전송되지 않으므로 수동으로 동기화 하십시오
att_areaPerson_addAreaPerson=구역별 사용자 추가
att_areaPerson_delAreaPerson=구역별 사용자 삭제
att_areaPerson_importDelAreaPerson=구역별 사용자 가져오기 및 삭제
att_areaPerson_importAreaPerson=구역별 사용자 가져오기
#考勤点
att_attPoint_name=항목명
att_attPoint_list=포인트 리스트
att_attPoint_deviceModule=장치 유형
att_attPoint_acc=출입 통제
att_attPoint_park=주차 관리
att_attPoint_ins=안면 키오스크
att_attPoint_pid=신분증
att_attPoint_vms=영상
att_attPoint_psg=통로
att_attPoint_doorList=출입문 목록
att_attPoint_deviceList=장치 목록
att_attPoint_channelList=채널 목록
att_attPoint_gateList=게이트 목록
att_attPoint_recordTypeList=기록 유형 가져오기
att_attPoint_door=해당 출입문 선택
att_attPoint_device=해당 장치 선택
att_attPoint_gate=해당 게이트를 선택하세요.
att_attPoint_normalPassRecord=정상 통과 기록
att_attPoint_verificationRecord=기록 확인
att_person_attSet=근태 설정
att_attPoint_point=근태구역 선택
att_attPoint_count=근태 라이선스가 부족하여 설정할 수 없습니다
att_attPoint_notSelect=기능을 구성해 주십시오
att_attPoint_accInsufficientPoints=출입통제 근태포인트가 부족하여 설정할 수 없습니다
att_attPoint_parkInsufficientPoints=주차 관리 근태포인트가 부족하여 설정할 수 없습니다
att_attPoint_insInsufficientPoints=키오스크 근태포인트가 부족하여 설정할 수 없습니다
att_attPoint_pidInsufficientPoints=신분증 스캔 근태포인트가 부족하여 설정할 수 없습니다
att_attPoint_doorOrParkDeviceName=출입문 명 또는 주차장치 명
att_attPoint_vmsInsufficientPoints=영상 근태포인트가 부족하여 설정할 수 없습니다
att_attPoint_psgInsufficientPoints=채널 근태포인트가 부족하여 설정할 수 없습니다
att_attPoint_delDevFail=근태 장치로 설정되어 있어 삭제할 수 없습니다
att_attPoint_pullingRecord=근태이력 업로드 중이니 잠시만 기다려주십시오
att_attPoint_lastTransactionTime=마지막 데이터 가져온 시간
att_attPoint_masterDevice=마스터 장치
att_attPoint_channelName=채널 이름
att_attPoint_cameraName=카메라 이름
att_attPoint_cameraIP=카메라 IP
att_attPoint_channelIP=채널 IP
att_attPoint_gateNumber=게이트 번호
att_attPoint_gateName=게이트 이름
#APP考勤签到地址
att_signAddress_address=주소
att_signAddress_longitude=경도
att_signAddress_latitude=위도
att_signAddress_range=해당 범위
att_signAddress_rangeUnit=단위 : 미터 (m)
#=====================================================================
#规则
att_rule_baseRuleSet=기본 설정
att_rule_countConvertSet=근무시간 계산 설정
att_rule_otherSet=기타 설정
att_rule_baseRuleSignIn=출근 규칙
att_rule_baseRuleSignOut=퇴근 규칙
att_rule_earliestPrinciple=처음 인증 출근
att_rule_theLatestPrinciple=마지막 인증 퇴근
att_rule_principleOfProximity=기준에 근접한 시간적용
att_rule_baseRuleShortestMinutes=최소 시간은 10분 이상으로 설정하십시오
att_rule_baseRuleLongestMinutes=최대 시간은 1,440분 이하로 설정하십시오
att_rule_baseRuleLateAndEarly=설정 시간 범위에 해당하지 않는 인증은 결근으로 처리됩니다
att_rule_baseRuleCountOvertime=초과근무 통계
att_rule_baseRuleFindSchSort=이동로그 검색
att_rule_groupGreaterThanDepartment=그룹->부서
att_rule_departmentGreaterThanGroup=부서->그룹
att_rule_baseRuleSmartFindClass=지능형 이동 규칙
att_rule_timeLongest=최대 근무시간으로 적용되는 근무규칙 적용
att_rule_exceptionLeast=지각, 조퇴가 적은 근무규칙 적용
att_rule_baseRuleCrossDay=교대 근무 근태로그
att_rule_firstDay=당일만 설정
att_rule_secondDay=다음 날 설정
att_rule_baseRuleShortestOvertimeMinutes=최소 초과근무 시간 (분)
att_rule_baseRuleMaxOvertimeMinutes=최대 초과근무 시간 (분)
att_rule_baseRuleElasticCal=유연근무 근태로그
att_rule_baseRuleTwoPunch=연속 인증 누적시간
att_rule_baseRuleStartEnd=처음/마지막 인증 시간 계산
att_rule_countConvertHour=시간 전환 규칙
att_rule_formulaHour=기준 : 시간 
att_rule_countConvertDay=일자 전환 규칙
att_rule_formulaDay=기준 : 일자 
att_rule_inFormulaShallPrevail=표준 근무시간 적용
att_rule_remainderHour=나머지 값은 크거나 같음
att_rule_oneHour=이상일 경우 1시간으로 반올림 계산 
att_rule_halfAnHour=이상일 경우 30분으로 반올림 계산,  그렇지 않을 경우 반영되지 않음
att_rule_remainderDay=결과 시간이 근무 시간보다 크거나 같음
att_rule_oneDay=%,1일로 계산됩니다
att_rule_halfAnDay=%,0.5일로 계산되며 그렇지 않을 경우 반영되지 않음
att_rule_countConvertAbsentDay=무단결근 일수 전환 규칙
att_rule_markWorkingDays=출근 일자로 계산
att_rule_countConvertDecimal=소수점의 정확한 자릿수
att_rule_otherSymbol=근태 보고서에 반영될 기호 설정
att_rule_arrive=정상 근무
att_rule_noSignIn=출근로그 없음
att_rule_noSignOff=퇴근로그 없음
att_rule_off=휴일 추가
att_rule_class=근태로그 추가
att_rule_shortLessLong=최소 근무 시간은 최대 근무 시간을 초과할 수 없습니다
att_rule_symbolsWarning=근태 보고서에 표시를 설정해야 합니다
att_rule_reportSettingSet=보고서 출력 설정
att_rule_shortDateFormat=일자 형식
att_rule_shortTimeFormat=시간 형식
att_rule_baseRuleSignBreakTime=휴식 시간을 근무 시간에 포함할 지 여부
att_leftMenu_custom_rule=커스텀 규칙
att_custom_rule_already_exist={0} 커스텀 규칙이 등록되어 있습니다
att_add_group_custom_rule=그룹 규칙 추가
att_custom_rule_type=규칙 유형
att_rule_type_group=그룹 규칙
att_rule_type_dept=부서 규칙
att_custom_rule_orgNames=사용 대상
att_rult_maxOverTimeType1=제한 없음
att_rult_maxOverTimeType2=이번 주
att_rult_maxOverTimeType3=이번 달
att_rule_countConvertDayRemark1=예 : 설정 근무 시간은 500분이고 실 근무 시간은 일일 480분입니다 결과는 500 / 480 = 1.04로 1.0으로 계산합니다
att_rule_countConvertDayRemark2=예 : 설정 근무 시간은 500분이고 실 근무 시간은 일일 480분입니다 결과는 500 / 480 = 1.04로 1.04>0.8 입니다
att_rule_countConvertDayRemark3=예 : 설정 근무 시간은 300분이고 실 근무 시간은 일일 480분입니다 결과는 반나절 동안 300 / 480 =0.625, 0.2<0.625<0.8 입니다. 
att_rule_countConvertDayRemark4=일일 전환 기준 : 해당 기간의 근무일로 기록되지 않습니다
att_rule_countConvertDayRemark5=근무일수 기준 : 결근일수에 한해서만 계산하고, 시간당 결근일수만 있으면 시간당 근무일수를 계산하여 결근시간을 설정
att_rule_baseRuleSmartFindRemark1=최장 근무시간 : 당일 인증시간에 따라 근무 스케줄에 해당하는 근무시간을 계산하여 최장 근무시간과 근무 스케줄을 확인합니다
att_rule_baseRuleSmartFindRemark2=최소 근무시간 : 당일 인증시간에 따라 근무 스케줄에 해당하는 근무시간을 계산하여 최소 근무시간과 근무 스케줄을 확인합니다
att_rule_baseRuleHourValidator=30분 반올림 시간은 1시간 이상으로 설정할 수 없습니다
att_rule_baseRuleDayValidator=12시간 반올림 시간은 1일 이상으로 설정할 수 없습니다
att_rule_overtimeWarning=최대 초과 근무 시간은 최소 초과 근무 시간보다 작게 설정할 수 없습니다
att_rule_noSignInCountType=출근인증 누락 횟수
att_rule_absent=결근
att_rule_earlyLeave=조퇴
att_rule_noSignOffCountType=퇴근인증 누락 횟수
att_rule_minutes=분
att_rule_noSignInCountLateMinute=출근인증 누락으로 지각 처리됩니다.
att_rule_noSignOffCountEarlyMinute=퇴근인증 누락으로 조퇴 처리됩니다. 
att_rule_incomplete=인증 미완료
att_rule_noCheckInIncomplete=인증 미완료되어 출근으로 처리되지 않습니다
att_rule_noCheckOutIncomplete=인증 미완료되어 퇴근으로 처리되지 않습니다
att_rule_lateMinuteWarning=지각 시간은 0보다 크고 최대 근무 시간보다 작게 설정해야 합니다
att_rule_earlyMinuteWarning=조퇴 시간은 0보다 크고 최대 근무 시간보다 작게 설정해야 합니다
att_rule_baseRuleNoSignInCountLateMinuteRemark=출근인증하지 않은 경우 지각으로 처리되며, N분 지각으로 처리됩니다
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=퇴근인증하지 않은 경우 조퇴로 처리되며, N분 조퇴로 처리됩니다
#节假日
att_holiday_placeholderNo=휴일 번호를 입력하십시오
att_holiday_placeholderName=휴일 규칙명을 입력하십시오
att_holiday_dayNumber=일 수
att_holiday_validDate_msg=설정기간을 휴일로 설정
#假种
att_leaveType_leaveThing=휴가
att_leaveType_leaveMarriage=결혼
att_leaveType_leaveBirth=출산 휴가
att_leaveType_leaveSick=병가
att_leaveType_leaveAnnual=연차
att_leaveType_leaveFuneral=상조
att_leaveType_leaveHome=개인 사유
att_leaveType_leaveNursing=육아 휴가
att_leaveType_isDeductWorkLong=근무 시간에 추가 여부
att_leaveType_placeholderNo=휴가 번호를 입력하십시오
att_leaveType_placeholderName=휴가명을 입력하십시오
#定时计算
att_timingcalc_timeCalcFrequency=산출 간격
att_timingcalc_timeCalcInterval=산출 간격시간
att_timingcalc_timeSet=산출 기준시간
att_timingcalc_timeSelect=시간을 선택하십시오
att_timingcalc_optionTip=한 개 이상 일일 근무 산출이 유지되어야 합니다
#自动导出报表
att_autoExport_reportType=보고서 유형
att_autoExport_fileType=파일 형식
att_autoExport_fileName=파일명
att_autoExport_fileDateFormat=데이터 삭제
att_autoExport_fileTimeFormat=시간 삭제
att_autoExport_fileContentFormat=컨텐츠 삭제
att_autoExport_fileContentFormatTxt=예：{deptName}00{personPin}01{personName}02{attDatetime}03
att_autoExport_timeSendFrequency=전송주기
att_autoExport_timeSendInterval=전송시간 간격
att_autoExport_emailType=이메일 수신 유형
att_autoExport_emailRecipients=받는 사람
att_autoExport_emailAddress=이메일 주소
att_autoExport_emailExample=예 : <EMAIL>,<EMAIL>
att_autoExport_emailSubject=제목
att_autoExport_emailContent=본문
att_autoExport_field=필드
att_autoExport_fieldName=필드명
att_autoExport_fieldCode=필드 번호
att_autoExport_reportSet=보고서 설정
att_autoExport_timeSet=이메일 전송 시간 설정
att_autoExport_emailSet=이메일 설정
att_autoExport_emailSetAlert=이메일 주소를 입력하십시오
att_autoExport_emailTypeSet=받는 사람 설정
att_autoExport_byDay=매일
att_autoExport_byMonth=월간
att_autoExport_byPersonSet=사용자별 설정
att_autoExport_byDeptSet=부서별 설정
att_autoExport_byAreaSet=구역별 설정
att_autoExport_emailSubjectSet=제목 설정
att_autoExport_emailContentSet=본문 설정
att_autoExport_timePointAlert=전송 기간 설정
att_autoExport_lastDayofMonth=매월 마지막 날
att_autoExport_firstDayofMonth=매월 첫째 날
att_autoExport_dayofMonthCheck=특정 일자 지정
att_autoExport_dayofMonthCheckAlert=세부 일자 선택
att_autoExport_chooseDeptAlert=세부 부서 선택
att_autoExport_sendFormatSet=발송 모드 설정
att_autoExport_sendFormat=발송 모드
att_autoExport_mailFormat=이메일 발송 방법
att_autoExport_ftpFormat=FTP 발송 방법
att_autoExport_sftpFormat=SFTP 발송 방법
att_autoExport_ftpUrl=FTP 서버 주소
att_autoExport_ftpPort=FTP 서버 포트
att_autoExport_ftpTimeSet=FTP 전송 시간 설정
att_autoExport_ftpParamSet=FTP 기능 설정
att_autoExport_ftpUsername=FTP 사용자 이름
att_autoExport_ftpPassword=FTP 비밀번호
att_autoExport_correctFtpParam=FTP 기능 설정을 입력하십시오
att_autoExport_correctFtpTestParam=통신이 정상 여부를 확인하려면 연결 테스트를 실행하십시오
att_autoExport_inputFtpUrl=서버 주소를 입력하십시오
att_autoExport_inputFtpPort=서버 포트를 입력하십시오
att_autoExport_ftpSuccess=연결 성공
att_autoExport_ftpFail=기능 설정을 다시 확인하십시오
att_autoExport_validFtp=사용 가능한 서버 주소를 입력하십시오
att_autoExport_validPort=사용 가능한 서버 포트를 입력하십시오
att_autoExport_selectExcelTip=파일 형식은 EXCEL을 선택하고 전체 필드값이 출력됩니다
#=====================================================================
#时间段
att_timeSlot_periodType=근무 유형
att_timeSlot_normalTime=정규 근무
att_timeSlot_elasticTime=유연 근무
att_timeSlot_startSignInTime=출근 적용 시작 시간
att_timeSlot_toWorkTime=출근 시간
att_timeSlot_endSignInTime=출근 적용 종료 시간
att_timeSlot_allowLateMinutes=지각 허용 시간 (분)
att_timeSlot_isMustSignIn=출근 시 인증
att_timeSlot_startSignOffTime=퇴근 적용 시작 시간
att_timeSlot_offWorkTime=퇴근 시간
att_timeSlot_endSignOffTime=퇴근 적용 종료 시간
att_timeSlot_allowEarlyMinutes=조퇴 허용 시간 (분)
att_timeSlot_isMustSignOff=퇴근 시 인증
att_timeSlot_workingHours=근무 시간 (분)
att_timeSlot_isSegmentDeduction=시간 공제 여부
att_timeSlot_startSegmentTime=공제 시작 시간
att_timeSlot_endSegmentTime=공제 종료 시간
att_timeSlot_interSegmentDeduction=공제 시간 (분)
att_timeSlot_markWorkingDays=근무 시간
att_timeSlot_isAdvanceCountOvertime=자동 초과 근무 (조기 출근)
att_timeSlot_signInAdvanceTime=자동 초과 근무 종료 시간 (출근)
att_timeSlot_isPostponeCountOvertime=자동 초과 근무 (퇴근 지연)
att_timeSlot_signOutPosponeTime=자동 초과 근무 시작 시간 (퇴근)
att_timeSlot_isCountOvertime=근무 시간을 초과 근무에 적용
att_timeSlot_timeSlotLong=근무 시간은 설정된 규칙 내 근태 설정을 충족해야 합니다
att_timeSlot_alertStartSignInTime=출근 시작 시간은 출근 시간보다 작아야 합니다
att_timeSlot_alertEndSignInTime=출근 종료 시간은 출근 시간보다 커야 합니다
att_timeSlot_alertStartSignInAndEndSignIn=퇴근 시작 시간은 퇴근 기준시간보다 빠른 시간으로 설정하십시오
att_timeSlot_alertStartSignOffTime=초과 근무 시작시간은 퇴근 기준시간보다 늦은 시간으로 설정하십시오
att_timeSlot_alertEndSignOffTime=초과 근무 시작시간은 퇴근 시간보다 작아야 합니다
att_timeSlot_alertStartUnequalEnd=근무일이 0보다 작을 수 없습니다
att_timeSlot_alertStartSegmentTime=공제시간은 0보다 작을 수 없습니다
att_timeSlot_alertStartAndEndTime=퇴근 시간과 출근 시간은 겹칠 수 없습니다
att_timeSlot_alertEndAndoffWorkTime=23시간을 초과할 수 없습니다
att_timeSlot_alertToWorkLesserAndEndTimeLesser=59분 이상 설정할 수 없습니다
att_timeSlot_alertLessSignInAdvanceTime=조기 출근 시간은 설정 시작시간보다 작아야 합니다
att_timeSlot_alertMoreSignInAdvanceTime=출근 전 인증 유효 시간이 출근 전 초과근무 유효 시간보다 커야 합니다
att_timeSlot_alertMoreSignOutPosponeTime=퇴근 후 인증 유효 시간이 퇴근 후 초과근무 유효 시간보다 커야 합니다
att_timeSlot_alertLessSignOutPosponeTime=퇴근 지연 시간은 퇴근 종료 시간보다 커야 합니다
att_timeSlot_time=정확한 시간 형식을 입력하십시오
att_timeSlot_alertMarkWorkingDays=근무 일수는 비워 둘 수 없습니다
att_timeSlot_placeholderNo=근무 규칙 번호를 입력하십시오
att_timeSlot_placeholderName=근무 규칙명을 입력하십시오
att_timeSlot_beforeToWork=출근 전
att_timeSlot_afterToWork=출근 후
att_timeSlot_beforeOffWork=퇴근 전
att_timeSlot_afterOffWork=퇴근 후
att_timeSlot_minutesSignInValid=출근은 설정 시간 내 유효합니다
att_timeSlot_toWork=근무 중
att_timeSlot_offWork=비번
att_timeSlot_minutesSignInAsOvertime=초과 근무 분 단위 인증
att_timeSlot_minutesSignOutAsOvertime=설정 시간 이후 초과 근무 시작
att_timeSlot_minOvertimeMinutes=최소 초과 근무 시간
att_timeSlot_enableWorkingHours=근무 시간 가능 여부
att_timeSlot_eidtTimeSlot=시간 편집
att_timeSlot_browseBreakTime=휴식 시간 확인
att_timeSlot_addBreakTime=휴식 시간 추가
att_timeSlot_enableFlexibleWork=유연 근무 출근
att_timeSlot_advanceWorkMinutes=조기 출근 가능
att_timeSlot_delayedWorkMinutes=출근 연기 가능
att_timeSlot_advanceWorkMinutesValidMsg1=출근 기준시간은 조기 출근 시간보다 커야 합니다
att_timeSlot_advanceWorkMinutesValidMsg2=조기 출근시간은 출근 기준시간보다 작아야 합니다
att_timeSlot_advanceWorkMinutesValidMsg3=유연 근무 조기 출근 유효 시간이 출근 전 초과근무 유효 시간보다 작거나 같아야 합니다
att_timeSlot_advanceWorkMinutesValidMsg4=유연 근무 출근 연기 유효 시간이 퇴근 후 초과근무 유효 시간보다 작거나 같아야 합니다 
att_timeSlot_delayedWorkMinutesValidMsg1=퇴근 유효 시간이 유연 근무 출근 연기 유효 시간보다 커야합니다
att_timeSlot_delayedWorkMinutesValidMsg2=유연 근무 출근 연기 유효 시간은 퇴근 유효 시간보다 작거나 같거나 같아야 합니다
att_timeSlot_delayedWorkMinutesValidMsg3=유연 근무 출근 연기 유효 시간은 퇴근 후 초과근무 유효 시간보다 작거나 같아야 합니다
att_timeSlot_delayedWorkMinutesValidMsg4=퇴근 후 초과근무 유효 시간은 유연 근무 출근 연기 유효 시간보다 커야합니다
att_timeSlot_allowLateMinutesValidMsg1=지각 허용시간은 출근 허용 최대 시간보다 빠른 시간으로 설정해야 합니다
att_timeSlot_allowLateMinutesValidMsg2=출근 허용 최대 시간은 지각 허용 시간보다 늦은 시간으로 설정해야 합니다
att_timeSlot_allowEarlyMinutesValidMsg1=조퇴 허용 시간은 퇴근 허용 시간보다 작아아 합니다
att_timeSlot_allowEarlyMinutesValidMsg2=퇴근 허용 시간은 조퇴 허용 시간보다 커야 합니다
att_timeSlot_timeOverlap=휴식시간 중복으로 기간을 다시 설정해야 합니다
att_timeSlot_atLeastOne=최소 1회 휴식시간
att_timeSlot_mostThree=최대 3회 휴식시간
att_timeSlot_canNotEqual=휴식 시간의 시작 시간과 종료 시간은 동일하게 설정할 수 없습니다
att_timeSlot_shoudInWorkTime=휴식 시간이 근무 시간에 포함되어 있는지 확인하십시오
att_timeSlot_repeatBreakTime=휴식 시간을 반복 하십시오
att_timeSlot_toWorkLe=근무 시간은 선택한 휴식 시간의 시작 시간보다 작아야 합니다 
att_timeSlot_offWorkGe=퇴근 시간은 선택한 휴식 시간의 종료 시간보다 커야 합니다
att_timeSlot_crossDays_toWork=휴식 기간의 최소 시작 시간은 근무 시간 내에서 설정해야 합니다
att_timeSlot_crossDays_offWork=휴식 기간의 최대 종료 시간은 근무 시간 내에서 설정해야 합니다
att_timeSlot_allowLateMinutesRemark=근무 시간에서 지각 허용 시간까지 계산
att_timeSlot_allowEarlyMinutesRemark=조퇴 시간에서 퇴근 허용 시간까지 계산
att_timeSlot_isSegmentDeductionRemark=휴식 시간 제외 여부
att_timeSlot_attEnableFlexibleWorkRemark1=유연 근무는 지각 및 조퇴를 설정하지 않습니다
att_timeSlot_afterToWorkRemark=퇴근 시간이 유연 근무 출근 연기 유효 시간과 같습니다
att_timeSlot_beforeOffWorkRemark=출근 전 시간이 유연 근무 조기 출근 유효 시간과 같습니다
att_timeSlot_attEnableFlexibleWorkRemark2=퇴근 인증 유효 시간은 유연 근무 출근 연기 유효 시간+퇴근시간보다 크거나 같아야 합니다
att_timeSlot_attEnableFlexibleWorkRemark3=유연 근무 조기 출근 유효 시간이 출근 전 초과근무 유효 시간보다 작거나 같아야 합니다
att_timeSlot_attEnableFlexibleWorkRemark4=유연 근무 출근 연기 유효 시간이 퇴근 후 초과근무 유효 시간보다 작거나 같아야 합니다 
att_timeSlot_attBeforeToWorkAsOvertimeRemark=예 : 9시 출근일 경우, 8시에 출근 인증 시 해당 60분 은 초과 근무에 반영됩니다
att_timeSlot_attAfterOffWorkAsOvertimeRemark=예 : 18시 퇴근일 경우, 19시에 퇴근 인증 시 해당 60분은 초과 근무에 반영됩니다
att_timeSlot_longTimeValidRemark=출근 인증과 퇴근 인증 시간은 동일할 수 없습니다
att_timeSlot_advanceWorkMinutesValidMsg5=퇴근 전 인증 유효 시간이 유연 근무 조기 출근 유효 시간보다 커야합니다
att_timeSlot_advanceWorkMinutesValidMsg6=유연 근무 조기 출근 유효 시간이 출근 인증 유효 시간보다 작거나 같아야 합니다
att_timeSlot_delayedWorkMinutesValidMsg5=출근 후 인증 유효 시간이 유연 근무 출근 연기 유효 시간보다 커야합니다
att_timeSlot_delayedWorkMinutesValidMsg6=유연 근무 출근 연기 유효 시간이 출근 후 인증 유효 시간보다 작거나 같아야 합니다
att_timeSlot_advanceWorkMinutesValidMsg7=출근 시간과 당일 퇴근 시간은 중복될 수 없습니다
att_timeSlot_delayedWorkMinutesValidMsg7=퇴근 시간과 다음 날 출근 시간은 중복될 수 없습니다
att_timeSlot_maxOvertimeMinutes=최대 초과 근무시간 제한
#班次
att_shift_basicSet=스케줄 유형
att_shift_advancedSet=스케줄 이름
att_shift_type=근무 규칙 유형
att_shift_name=근무 규칙명
att_shift_regularShift=정규 근무
att_shift_flexibleShift=유연 근무
att_shift_color=색상
att_shift_periodicUnit=단위
att_shift_periodNumber=반복 횟수
att_shift_startDate=시작 일자
att_shift_startDate_firstDay=주기 기준일자
att_shift_isShiftWithinMonth=매월 반복
att_shift_attendanceMode=근무 유형
att_shift_shiftNormal=기본 설정
att_shift_oneDayOneCard=출근 확인
att_shift_onlyBrushTime=자율 출/퇴근
att_shift_notBrushCard=임시 출/퇴근
att_shift_overtimeMode=초과근무 유형
att_shift_autoCalc=자동 계산
att_shift_mustApply=기준시간 이상 근무 시 초과근무 반영
att_shift_mustOvertime=기준시간 이하 근무 시 부재 중으로 기록
att_shift_timeSmaller=최소 근무시간 반영
att_shift_notOvertime=초과근무 신청 시간은 반영되지 않음
att_shift_overtimeSign=초과근무 유형
att_shift_normal=평일
att_shift_restday=휴일
att_shift_timeSlotDetail=스케줄 세부정보
att_shift_doubleDeleteTimeSlot=더블 클릭하면 기간을 삭제할 수 있습니다
att_shift_addTimeSlot=스케줄 추가
att_shift_cleanTimeSlot=스케줄 삭제
att_shift_NO=아니오
att_shift_notAll=전체선택 해제
att_shift_notTime=시간대가 겹칠 경우 세부 정보 확인란을 선택할 수 없습니다
att_shift_notExistTime=시간표가 등록되지 않았습니다
att_shift_cleanAllTimeSlot=선택한 근무시간을 삭제하시겠습니까?
att_shift_pleaseCheckBox=확인란을 선택하십시오
att_shift_pleaseUnit=주기 단위, 횟수를 입력하십시오
att_shift_pleaseAllDetailTimeSlot=세부 사항을 선택하십시오
att_shift_placeholderNo=근무규칙 스케줄 번호를 입력하십시오
att_shift_placeholderName=근무규칙 스케줄명을 입력하십시오
att_shift_workType=업무 유형
att_shift_normalWork=정상근무
att_shift_holidayOt=휴일 OT
att_shift_attShiftStartDateRemark=예 : 주기 기준일자가 22일, 3일 주기로 설정 시 / 22일, 23일, 24일은 A그룹 / 19일, 20일, 21일은 B그룹으로 설정됩니다
att_shift_isShiftWithinMonthRemark1=월간 교대근무 설정 시 해당 월의 마지막 일자까지 순환하여 교대근무가 설정됩니다
att_shift_isShiftWithinMonthRemark2=주기별 교대근무 설정 시 월 변경과 관계없이 주기적으로 교대근무가 설정됩니다
att_shift_workTypeRemark1=정상 근무, 초과 근무는 휴일에 포함되지 않습니다
att_shift_workTypeRemark2=주말 근무, 초과 근무는 자동 계산되며 별도 근무 신청을 할 필요가 없습니다 / 해당 일자 근무는 연장 근무로 기록되며 휴일은 포함하지 않습니다
att_shift_workTypeRemark3=휴일 근무, 초과 근무, 휴일 근무는 자동 계산되며 별도 근무 신청을 할 필요가 없습니다
att_shift_attendanceModeRemark1=교대 근무 인증은 조퇴 또는 초과 근무로 간주되지 않습니다
att_shift_attendanceModeRemark2=1. 체크 인이 필요하지 않거나 하루에 한 번 유효한 카드를 사용하면 초과 근무가 계산되지 않습니다.
att_shift_attendanceModeRemark3=2. 작업 유형 : 일반 근무, 근태 모드 : 인증모드 없음, 해당 근무 시간은 주간 근무 시간으로 설정됩니다
att_shift_periodStartMode=기간 시작 유형
att_shift_periodStartModeByPeriod=기간 시작 일자
att_shift_periodStartModeBySch=교대 시작 일자
att_shift_addTimeSlotToShift=해당 교대 근무를 시간 슬롯을 추가할지 여부
#=====================================================================
#分组
att_group_editGroup=그룹에 사용자 추가
att_group_browseGroupPerson=그룹에 설정된 사용자 편집
att_group_list=그룹 목록
att_group_placeholderNo=그룹 번호를 입력하십시오
att_group_placeholderName=그룹명을 입력하십시오
att_widget_deptHint=참고 : 선택한 부서의 전체 사용자가 적용됩니다
att_widget_searchType=조건 검색
att_widget_noPerson=선택한 사용자가 없습니다
#分组排班
#部门排班
att_deptSch_existsDept=해당 부서에 설정된 항목이 있어 삭제할 수 없습니다
#人员排班
att_personSch_view=사용자 스케줄 확인
#临时排班
att_schedule_type=스케줄 유형
att_schedule_tempType=임시 유형
att_schedule_normal=기본 스케줄
att_schedule_intelligent=스마트 스케줄
att_tempSch_scheduleType=스케줄 유형
att_tempSch_startDate=시작 일자
att_tempSch_endDate=종료 일자
att_tempSch_attendanceMode=근태 모드
att_tempSch_overtimeMode=초과근무 모드
att_tempSch_overtimeRemark=초과근무 표시
att_tempSch_existsDept=해당 부서에 설정된 근무 유형이 있어 삭제할 수 없습니다
att_schedult_opAddTempSch=신규 임시 유형
att_schedule_cleanEndDate=종료 시간 입력되지 않음
att_schedule_selectOne=한 개 스케줄만 설정할 수 있습니다
att_schedule_selectPerson=사용자를 선택하십시오
att_schedule_selectDept=부서를 선택하십시오
att_schedule_selectGroup=그룹을 선택하십시오
att_schedule_selectOneGroup=한 개 그룹만 설정 가능합니다
att_schedule_arrange=교대 근무를 선택하십시오
att_schedule_leave=휴가
att_schedule_trip=출장
att_schedule_out=외근
att_schedule_off=휴식
att_schedule_makeUpClass=추가
att_schedule_class=조정
att_schedule_holiday=휴일
att_schedule_offDetail=휴일 추가
att_schedule_makeUpClassDetail=근태 추가
att_schedule_classDetail=교대근무 수정
att_schedule_holidayDetail=휴일
att_schedule_noSchDetail=설정되지 않음
att_schedule_normalDetail=정상
att_schedule_normalSchInfo=교대근무 관리 : 교대근무 없음
att_schedule_multipleInterSchInfo=교대근무 구분 : 여러 일자 설정
att_schedule_inderSchFirstDayInfo=교대 근무는 첫 번째 일자로 기록됩니다
att_schedule_inderSchSecondDayInfo=교대 근무는 두 번째 일자로 기록됩니다
att_schedule_timeConflict=설정된 교대근무와 중복되어 저장할 수 없습니다
#=====================================================================
att_excp_notExisetPerson=등록되지 않은 사용자입니다
att_excp_leavePerson=사용자 퇴사
#补签单
att_sign_signTime=인증 시간
att_sign_signDate=인증 일자
#请假
att_leave_arilName=휴가 유형
att_leave_image=결제문서 등록
att_leave_imageShow=이미지 없음
att_leave_imageType=사진 형식이 올바르지 않습니다 (JPEG, GIF, PNG 형식 지원)
att_leave_imageSize=최대 사진 크기는 4MB 입니다
att_leave_leaveLongDay=휴가 기간 (일)
att_leave_leaveLongHour=휴가 시간 (시간)
att_leave_leaveLongMinute=휴가 기간 (분)
att_leave_endNoLessAndEqualStart=종료 시간은 시작 시간보다 작거나 같을 수 없습니다
att_leave_typeNameNoExsists=휴일이 존재하지 않습니다
att_leave_startNotNull=시작 시간은 비워 둘 수 없습니다
att_leave_endNotNull=종료 시간은 비워 둘 수 없습니다
att_leave_typeNameConflict=휴일 이름과 근태 상태 이름이 중복되었습니다
#出差
att_trip_tripLongDay=출장 시간 (일)
att_trip_tripLongMinute=출장 시간 (분)
att_trip_tripLongHour=출장 시간 (시간)
#外出
att_out_outLongDay=외근 시간 (일)
att_out_outLongMinute=외근 시간 (분)
att_out_outLongHour=외근 시간 (시간)
#加班
att_overtime_type=초과 근무 유형
att_overtime_normal=초과 근무 (일)
att_overtime_rest=초과 근무 (휴일)
att_overtime_overtimeLong=초과 근무 시간 (분)
att_overtime_overtimeHour=초과 근무 시간 (시간)
att_overtime_notice=초과 근무 신청 시간은 일일 이상 설정할 수 없습니다
att_overtime_minutesNotice=초과 근무 신청 시간은 최소 초과 근무 기준시간보다 작게 설정할 수 없습니다
#调休补班
att_adjust_type=선택 유형
att_adjust_adjustDate=설정 일자
att_adjust_shiftName=추가할 근무규칙 스케줄
att_adjust_selectClass=근태에 추가할 근무규칙을 선택하십시오
att_shift_notExistShiftWorkDate=변경 일자가 설정된 근무 일자가 아니므로 추가 할 수 없습니다
att_adjust_shiftPeriodStartMode=설정한 교대근무에 포함될 교대근무 사항 (시작 일자가 교대근무 기준 인 경우 기본값은 0)
att_adjust_shiftNameNoNull=교대 항목은 비워 둘 수 없습니다
att_adjust_shiftNameNoExsist=등록되지 않은 교대 항목
#调班
att_class_type=선택 유형
att_class_sameTimeMoveShift=임시 근무시간 적용
att_class_differenceTimeMoveShift=개인 스케줄 일자 조정
att_class_twoPeopleMove=사용자 근무규칙 교환
att_class_moveDate=변경 일자
att_class_shiftName=스케줄 이름 조정
att_class_moveShiftName=신규 근무규칙에 사용자를 추가하십시오
att_class_movePersonPin=변경할 사용자 ID
att_class_movePersonName=변경할 사용자 이름
att_class_movePersonLastName=변경할 사용자 업체
att_class_moveDeptName=변경할 사용자 부서
att_class_personPin=사용자 ID
att_class_shiftNameNoNull=신규 근무규칙에 추가된 사용자가 없습니다
att_class_personPinNoNull=신규 사용자 ID는 필수 입력항목 입니다
att_class_isNotExisetSwapPersonPin=관리자를 추가 하십시오
att_class_personNoSame=사용자가 중복 되었습니다 / 다시 선택하십시오
att_class_outTime=변경 일자와 기존 일자 범위는 한 달을 초과 할 수 없습니다
att_class_shiftNameNoExsist=등록되지 않은 변경 항목
att_class_swapPersonNoExisist=교환한 사용자가 존재하지 않습니다
att_class_dateNoSame=다른 일자에 개인 교대, 일자는 같을 수 없습니다
#=====================================================================
#节点
att_node_name=중요
att_node_type=중요 유형
att_node_leader=승인자
att_node_leaderNode=중요 부서장 설정
att_node_person=지정 인원
att_node_position=직급 설정
att_node_choose=직급 선택
att_node_personNoNull=사용자는 비워 둘 수 없습니다
att_node_posiitonNoNull=직급은 비워 둘 수 없습니다
att_node_placeholderNo=N 로 시작 권장
att_node_placeholderName=직급 또는 이름으로 사용하는 것을 권장드립니다
att_node_searchPerson=입력 검색 기준
att_node_positionIsExist=해당 직급에 중요 데이터가 등록되어 있습니다 / 다시 선택하십시오
#流程
att_flow_type=프로세스 유형
att_flow_rule=프로세스 규칙
att_flow_rule0=1 일 이하
att_flow_rule1=1 일 초과 3 일 이하
att_flow_rule2=3 일 초과 7 일 이하
att_flow_rule3=7 일 초과
att_flow_node=중요 사항 평가
att_flow_start=프로세스 시작
att_flow_end=프로세스 종료
att_flow_addNode=중요항목 추가
att_flow_placeholderNo=F 로 시작 권장
att_flow_placeholderName=프로세스로 시작/종료를 권장
att_flow_tips=참고 : 승인 프로세스는 위에서 아래로 진행됩니다
#申请
att_apply_personPin=신청인 ID
att_apply_type=예외 유형
att_apply_flowStatus=흐름 전체 상태
att_apply_start=프로세스 시작
att_apply_flowing=대기 중
att_apply_pass=승인
att_apply_over=종료
att_apply_refuse=반려
att_apply_revoke=취소
att_apply_except=예외
att_apply_view=상세 정보보기
att_apply_leaveTips=설정 기간 해당 사용자 휴가 신청
att_apply_tripTips=설정 기간 해당 사용자 출장 신청
att_apply_outTips=설정 기간 해당 사용자 외근 신청
att_apply_overtimeTips=설정 기간 해당 사용자 초과근무 신청
att_apply_adjustTips=설정 기간 해당 사용자 대체근무 신청
att_apply_classTips=설정 기간 해당 사용자 교대 신청
#审批
att_approve_wait=승인 보류
att_approve_refuse=반려
att_approve_reason=사유
att_approve_personPin=승인자 ID
att_approve_personName=승인자 이름
att_approve_person=승인자
att_approve_isPass=승인 여부
att_approve_status=현재 프로세스 상태
att_approve_tips=사용자에 예외 로그가 저장되어 있어 추가로 설정할 수 없습니다
att_approve_tips2=프로세스가 설정되지 않았습니다 / 관리자에게 문의하십시오
att_approve_offDayConflicts=비번 일자에는 휴가 신청을 할 수 없습니다
att_approve_shiftConflicts=근무일에는 추가 근무 신청을 할 수 없습니다
att_approve_shiftNoSch=신청한 일자는 변경할 수 없습니다
att_approve_classConflicts=신청되지 않은 일자는 교대 근무를 설정할 수 없습니다
att_approve_selectTime=시간을 선택하면 설정에 따라 프로세스가 설정됩니다
att_approve_withoutPermissionApproval=승인 권한이 없는 사용자가 포함되어 있습니다
#=====================================================================
#考勤计算
att_op_calculation=근태 계산
att_op_calculation_notice=근태 데이터는 백그라운드에서 실행 중입니다 / 다시 시도하십시오
att_op_calculation_leave=퇴사 사용자 포함
att_statistical_choosePersonOrDept=부서 또는 사용자를 선택하십시오
att_statistical_sureCalculation=근태 로그를 계산하시겠습니까?
att_statistical_filter=필터링 준비되었습니다
att_statistical_initData=데이터가 초기화되었습니다
att_statistical_exception=예외 데이터 초기화되었습니다
att_statistical_error=결근 산출 오류
att_statistical_begin=산출 시작
att_statistical_end=산출 종료
att_statistical_noticeTime=설정기간 근태이력을 산출합니다 / 2개월 이내 기간으로 설정하십시오
att_statistical_remarkHoliday=H
att_statistical_remarkClass=C
att_statistical_remarkNoSch=NS
att_statistical_remarkRest=R
#原始记录表
att_op_importAccRecord=출입통제 항목에서 근태로그 가져오기
att_op_importParkRecord=주차관리 항목에서 근태로그 가져오기
att_op_importInsRecord=키오스크 항목에서 근태로그 가져오기
att_op_importPidRecord=신분증 인증 항목에서 근태로그 가져오기
att_op_importVmsRecord=영상 항목에서 근태로그 가져오기
att_op_importUSBRecord=USB에서 근태로그 가져오기
att_transaction_noAccModule=출입통제 항목 기능이 없습니다
att_transaction_noParkModule=주차관리 항목 기능이 없습니다
att_transaction_noInsModule=안면 키오스크 항목 기능이 없습니다
att_transaction_noPidModule=신분증 인증 항목 기능이 없습니다
att_transaction_exportRecord=인증이력 내보내기
att_transaction_exportAttPhoto=캡쳐사진 내보내기
att_transaction_fileIsTooLarge=내보내기 일자 범위가 넓습니다
att_transaction_exportDate=내보내기 일자
att_statistical_attDatetime=근태 일자
att_statistical_attPhoto=근태 사진
att_statistical_attDetail=근태 내역
att_statistical_acc=출입통제 장치
att_statistical_att=근태관리 장치
att_statistical_park=LPR Camera
att_statistical_faceRecognition=얼굴 인식 장치
att_statistical_app=모바일 장치
att_statistical_vms=영상 장치
att_statistical_psg=채널 장비
att_statistical_dataSources=데이터 정보
att_transaction_SyncRecord=근태 로그 동기화
#日打卡详情表
att_statistical_dayCardDetail=출근 세부 정보
att_statistical_cardDate=로그 일자
att_statistical_cardNumber=인증 횟수
att_statistical_earliestTime=처음 인증
att_statistical_latestTime=마지막 인증
att_statistical_cardTime=인증 시간
#请假汇总表
att_statistical_leaveDetail=휴가 상세정보
#日明细报表
att_statistical_attDate=근태 일자
att_statistical_week=주별
att_statistical_shiftInfo=근무규칙 정보
att_statistical_shiftTimeData=시작 시간/종료 시간
att_statistical_cardValidData=인증 시간
att_statistical_cardValidCount=인증 횟수
att_statistical_lateCount=지각 횟수 
att_statistical_lateMinute=지각 시간(분) 
att_statistical_earlyCount=조퇴 횟수
att_statistical_earlyMinute=조퇴 시간(분)
att_statistical_countData=시간 데이터
att_statistical_minuteData=분 데이터
att_statistical_attendance_minute=근태(분)
att_statistical_overtime_minute=초과 근무 시간(분)
att_statistical_unusual_minute=비정상(분)
#月明细报表
att_monthdetail_should_hour=해야 할(시간)
att_monthdetail_actual_hour=실제(시간)
att_monthdetail_valid_hour=유효(시간)
att_monthdetail_absent_hour=완료(시간)
att_monthdetail_leave_hour=휴가(시간)
att_monthdetail_trip_hour=출장(시간)
att_monthdetail_out_hour=외근(시간)
att_monthdetail_should_day=해야 할(일)
att_monthdetail_actual_day=실제(일)
att_monthdetail_valid_day=유효(일)
att_monthdetail_absent_day=완료(일)
att_monthdetail_leave_day=휴가(일)
att_monthdetail_trip_day=출장(일)
att_monthdetail_out_day=외근(일)
#月统计报表
att_statistical_late_minute=지각 시간(분)
att_statistical_early_minute=조퇴 시간(분)
#部门统计报表
#年度统计报表
att_statistical_should=설정 시간
att_statistical_actual=근무 시간
att_statistical_valid=유효 시간
att_statistical_numberOfTimes=시간
att_statistical_usually=평일
att_statistical_rest=주말
att_statistical_holiday=휴일
att_statistical_total=합계
att_statistical_month=월별 통계
att_statistical_year=연도별 통계
att_statistical_attendance_hour=근태(시간)
att_statistical_attendance_day=근태(일)
att_statistical_overtime_hour=초과 근무(시간)
att_statistical_unusual_hour=예외 시간(시간)
att_statistical_unusual_day=예외 시간(일)
#考勤设备参数
att_deviceOption_query=장치 설정값 확인
att_deviceOption_noOption=설정값 정보가 없습니다 / 먼저 장치 설정값을 가져오십시오
att_deviceOption_name=설정값 이름
att_deviceOption_value=설정값
att_deviceOption_UserCount=현재 사용자 수량
att_deviceOption_MaxUserCount=최대 사용자 수량
att_deviceOption_FaceCount=현재 얼굴 템플릿 수량
att_deviceOption_MaxFaceCount=최대 얼굴 템플릿 수량
att_deviceOption_FacePhotoCount=현재 얼굴 이미지 수량
att_deviceOption_MaxFacePhotoCount=최대 얼굴 이미지 수량
att_deviceOption_FingerCount=현재 지문 템플릿 수량
att_deviceOption_MaxFingerCount=최대 지문 템플릿 수량
att_deviceOption_FingerPhotoCount=현재 지문 이미지 수량
att_deviceOption_MaxFingerPhotoCount=최대 지문 이미지 수량
att_deviceOption_FvCount=현재 손가락 정맥 템플릿 수량
att_deviceOption_MaxFvCount=손가락 정맥 템플릿의 최대 수량
att_deviceOption_FvPhotoCount=현재 손가락 정맥 이미지 수량
att_deviceOption_MaxFvPhotoCount=손가락 정맥 이미지 수량
att_deviceOption_PvCount=현재 손바닥 정맥 템플릿 수량
att_deviceOption_MaxPvCount=최대 손바닥 정맥 템플릿 수량
att_deviceOption_PvPhotoCount=현재 손바닥 정맥 이미지 수량
att_deviceOption_MaxPvPhotoCount=최대 손바닥 정맥 이미지 수량
att_deviceOption_TransactionCount=현재 로그 수량
att_deviceOption_MaxAttLogCount=최대 로그 수량
att_deviceOption_UserPhotoCount=현재 사용자 사진 수량
att_deviceOption_MaxUserPhotoCount=최대 사용자 사진 수량
att_deviceOption_FaceVersion=얼굴 인식 알고리즘 버전
att_deviceOption_FPVersion=지문 인식 알고리즘 버전
att_deviceOption_FvVersion=손가락 정맥 인식 알고리즘 버전
att_deviceOption_PvVersion=손바닥 인식 알고리즘 버전
att_deviceOption_FWVersion=펌웨어 버전
att_deviceOption_PushVersion=Push Ver.
#=====================================================================
#API
att_api_areaCodeNotNull=구역 번호는 비워 둘 수 없습니다
att_api_pinsNotNull=Pin 데이터는 비워 둘 수 없습니다
att_api_pinsOverSize=Pin 데이터 길이는 500을 초과 할 수 없습니다
att_api_areaNoExist=등록되지 않은 구역입니다
att_api_sign=추가
#=====================================================================
#休息时间段
att_leftMenu_breakTime=휴식 시간
att_breakTime_startTime=시작 시간
att_breakTime_endTime=종료 시간
#=====================================================================
#导入U盘记录
att_import_uploadFileSuccess=파일 분석 후 업로드를 시작합니다 / 잠시만 기다려 주십시오
att_import_resolutionComplete=분석이 완료되면 데이터베이스 업데이트를 시작하십시오
att_import_snNoExist=가져온 파일에 해당하는 근태 장치가 없습니다 / 파일을 다시 선택하십시오
att_import_fileName_msg=가져온 파일 이름 형식 요구 사항 : 장치 일련번호는 밑줄 "_" "으로 시작하고 밑줄로 구분됩니다 (예 :" "3517171600001_attlog.dat" "")
att_import_notSupportFormat=해당 형식은 지원되지 않습니다
att_import_selectCorrectFile=올바른 형식 파일을 선택하십시오
att_import_fileFormat=파일 형식
att_import_targetFile=대상 파일
att_import_startRow=헤더 시작 부분의 행 수
att_import_startRowNote=첫 번째 행은 데이터 형식입니다 / 가져오기 전에 파일을 확인하십시오
att_import_delimiter=분리 기호
#=====================================================================
#设备操作日志
att_device_op_log_op_type=운영 코드
att_device_op_log_dev_sn=장치 일련번호 
att_device_op_log_op_content=운영 내용
att_device_op_log_operator_pin=운영자 번호
att_device_op_log_operator_name=운영자 이름
att_device_op_log_op_time=운영 시간
att_device_op_log_op_who_value=운영 대상 값
att_device_op_log_op_who_content=운영 대상 설명
att_device_op_log_op_value1=운영 대상 1
att_device_op_log_op_value_content1=운영 대상 설명 1
att_device_op_log_op_value2=운영 대상 2
att_device_op_log_op_value_content2=운영 대상 설명 2
att_device_op_log_op_value3=운영 대상 3
att_device_op_log_op_value_content3=운영 대상 설명 3
#操作日志的操作类型
att_device_op_log_opType_0=전원 켜짐
att_device_op_log_opType_1=전원 꺼짐
att_device_op_log_opType_2=인증 실패
att_device_op_log_opType_3=알람
att_device_op_log_opType_4=메뉴 입력
att_device_op_log_opType_5=설정 변경
att_device_op_log_opType_6=지문 등록
att_device_op_log_opType_7=비밀번호 등록
att_device_op_log_opType_8=HID 카드 등록
att_device_op_log_opType_9=사용자 삭제
att_device_op_log_opType_10=지문 삭제
att_device_op_log_opType_11=비밀번호 삭제
att_device_op_log_opType_12=RF 카드 삭제
att_device_op_log_opType_13=데이터 지우기
att_device_op_log_opType_14=MF 카드 생성
att_device_op_log_opType_15=MF 카드 등록
att_device_op_log_opType_16=MF 카드 등록
att_device_op_log_opType_17=MF 카드 등록 삭제
att_device_op_log_opType_18=MF 카드 내용 삭제
att_device_op_log_opType_19=등록 데이터를 카드로 이동
att_device_op_log_opType_20=카드에서 장치로 데이터 복사
att_device_op_log_opType_21=시간 설정
att_device_op_log_opType_22=공장 초기화 설정
att_device_op_log_opType_23=출입 로그 삭제
att_device_op_log_opType_24=관리자 권한 삭제
att_device_op_log_opType_25=출입통제 그룹 설정 수정
att_device_op_log_opType_26=사용자 출입통제 설정 수정
att_device_op_log_opType_27=출입통제 시간 수정
att_device_op_log_opType_28=그룹인증 조합 설정 수정
att_device_op_log_opType_29=잠금 해제
att_device_op_log_opType_30=신규 사용자 등록
att_device_op_log_opType_31=지문 속성 변경
att_device_op_log_opType_32=협박 알람
att_device_op_log_opType_34=안티 패스백
att_device_op_log_opType_35=근태 사진 삭제
att_device_op_log_opType_36=사용자 정보 수정
att_device_op_log_opType_37=휴일
att_device_op_log_opType_38=데이터 복원
att_device_op_log_opType_39=백업 데이터
att_device_op_log_opType_40=USB 업로드
att_device_op_log_opType_41=USB 다운로드
att_device_op_log_opType_42=USB 근태 로그 암호화
att_device_op_log_opType_43=USB 다운로드 성공 후 로그 삭제
att_device_op_log_opType_53=퇴실버튼
att_device_op_log_opType_54=출입문 센서
att_device_op_log_opType_55=알람
att_device_op_log_opType_56=설정 복원
att_device_op_log_opType_68=등록된 사용자 사진
att_device_op_log_opType_69=사용자 사진 수정
att_device_op_log_opType_70=사용자 이름 수정
att_device_op_log_opType_71=사용자 권한 수정
att_device_op_log_opType_76=네트워크 설정 IP 수정
att_device_op_log_opType_77=네트워크 설정 서브넷마스크 수정
att_device_op_log_opType_78=네트워크 설정 게이트웨이 수정
att_device_op_log_opType_79=네트워크 설정 DNS 수정
att_device_op_log_opType_80=연결 설정 비밀번호 수정
att_device_op_log_opType_81=연결 설정 장치 ID 수정
att_device_op_log_opType_82=ADMS 주소 수정
att_device_op_log_opType_83=ADMS 포트 수정
att_device_op_log_opType_87=출입통제 로그 설정 수정
att_device_op_log_opType_88=얼굴 설정 플래그 수정
att_device_op_log_opType_89=지문 설정 플래그 수정
att_device_op_log_opType_90=손가락 정맥 설정 플래그 수정
att_device_op_log_opType_91=손바닥 정맥 설정 플래그 수정
att_device_op_log_opType_92=USB 업그레이드 플래그
att_device_op_log_opType_100=RF 카드 정보 수정
att_device_op_log_opType_101=얼굴 등록
att_device_op_log_opType_102=직원 권한 수정
att_device_op_log_opType_103=직원 권한 삭제
att_device_op_log_opType_104=직원 권한 추가
att_device_op_log_opType_105=출입통제 로그 삭제
att_device_op_log_opType_106=얼굴 삭제
att_device_op_log_opType_107=직원 사진 삭제
att_device_op_log_opType_108=설정 수정
att_device_op_log_opType_109=WiFi SSID를 선택하십시오
att_device_op_log_opType_110=프록시 사용
att_device_op_log_opType_111=프록시 수정
att_device_op_log_opType_112=프록시 포트 수정
att_device_op_log_opType_113=개인 비밀번호 수정
att_device_op_log_opType_114=얼굴 정보 수정
att_device_op_log_opType_115=관리자 비밀번호 수정
att_device_op_log_opType_116=출입통제 설정 재개
att_device_op_log_opType_117=관리자 비밀번호 입력 오류
att_device_op_log_opType_118=관리자 비밀번호 잠금
att_device_op_log_opType_120=카드 데이터 길이 수정
att_device_op_log_opType_121=손가락 정맥 등록
att_device_op_log_opType_122=손가락 정맥 수정
att_device_op_log_opType_123=손가락 정맥 삭제
att_device_op_log_opType_124=손바닥 정맥 등록
att_device_op_log_opType_125=손바닥 정맥 수정
att_device_op_log_opType_126=손바닥 정맥 삭제
#操作对象描述
att_device_op_log_content_pin=사용자 ID :
att_device_op_log_content_alarm=알람 :
att_device_op_log_content_alarm_reason=알람 사유 :
att_device_op_log_content_update_no=품목 번호 수정 :
att_device_op_log_content_update_value=값 수정 :
att_device_op_log_content_finger_no=지문 번호 :
att_device_op_log_content_finger_size=지문 템플릿 크기 :
#=====================================================================
#工作流
att_flowable_datetime_to=to
att_flowable_todomsg_leave=휴가 신청 승인
att_flowable_todomsg_sign=신청 이력 승인
att_flowable_todomsg_overtime=초과 근무 승인
att_flowable_notifymsg_leave=휴가 사유 
att_flowable_notifymsg_sign=신청 이력 사유
att_flowable_notifymsg_overtime=초과 근무 사유
att_flowable_shift=교대 :
att_flowable_hour=시간
att_flowable_todomsg_trip=출장 승인
att_flowable_notifymsg_trip=출장
att_flowable_todomsg_out=외근 승인
att_flowable_notifymsg_out=외근 알림
att_flow_apply=신청
att_flow_applyTime=신청 시간
att_flow_approveTime=처리 시간
att_flow_operateUser=검토자
att_flow_approve=승인
att_flow_approveComment=내용
att_flow_approvePass=승인 결과
att_flow_status_processing=승인
#=====================================================================
#biotime
att_h5_pers_personIdNull=사용자 ID는 비워 둘 수 없습니다
att_h5_attPlaceNull=근태 포인트는 비워 둘 수 없습니다
att_h5_attAreaNull=근태 구역은 비워 둘 수 없습니다
att_h5_pers_personNoExist=등록되지 않은 사용자 번호입니다 
att_h5_signRemarkNull=[비고]는 비워 둘 수 없습니다
att_h5_common_pageNull=페이지 설정 오류
att_h5_taskIdNotNull=작업 노드의 ID는 비워둘 수 없습니다
att_h5_auditResultNotNull=승인 결과는 비워둘 수 없습니다
att_h5_latLongitudeNull=경도와 위도는 비워둘 수 없습니다
att_h5_pers_personIsNull=사용자 ID가 존재하지 않습니다
att_h5_pers_personIsNotInArea=사용자가 구역을 설정하지 않았습니다
att_h5_mapApiConnectionsError=지도 API 연결 오류
att_h5_googleMap=구글 지도
att_h5_gaodeMap=가오드 지도
att_h5_defaultMap=기본 지도
att_h5_shiftTime=교대 시간
att_h5_signTimes=추가 시간
att_h5_enterKeyWords=키워드를 입력하십시오 :
att_h5_mapSet=근태 Map 설정
att_h5_setMapApiAddress=지도 설정
att_h5_MapSetWarning=지도를 전환하면 등록된 APP 주소의 위도와 경도를 일치시킬 수 없습니다 / 주의해서 수정하십시오
att_h5_mapSelect=지도 선택
att_h5_persNoHire=사용자가 회사 정보에 등록되지 않았습니다
att_slef_apiKey=API Key
att_self_apiJsKey=API-JS-Key
att_self_noComputeAtt=당일 근태는 설정되지 않았습니다
att_self_noSignRecord=당일 근태 로그가 없습니다
att_self_imageUploadError=이미지 업로드 실패
att_self_attSignAddressAreaIsExist=해당 구역에 등록된 근태 포인트가 있습니다
att_self_signRuleIsError=현재 추가 시간이 허용되는 추가 시간 내에 있지 않습니다
att_self_signAcrossDay=교대 스케줄에 서명 할 수 없습니다
att_self_todaySignIsExist=오늘은 추가된 로그가 있습니다
att_self_signSetting=추가된 로그 규칙 설정
att_self_allowSign=로그인 허용 :
att_self_allowSignSuffix=일, 근태 로그
att_self_onlyThisMonth=당월
att_self_allowAcrossMonth=월간 허용
att_self_thisTimeNoSch=현재 기간에는 변화가 없습니다
att_self_revokeReason=해지 사유 :
att_self_revokeHint=해지 사유를 20자 이내로 입력하십시오
att_self_persSelfLogin=사용자 Self Login
att_self_isOpenSelfLogin=사용자 Self Login 항목을 시작할지 여부
att_self_applyAndWorkTimeOverlap=신청 시간과 작업 시간이 겹칩니다
att_apply_DurationIsZero=신청 기간은 0이며 신청은 허용되지 않습니다
att_sign_mapWarn=지도 로딩 실패, 네트워크 연결 및 지도 키 값을 확인하십시오
att_admin_applyWarn=작업이 실패했습니다 / 예약되지 않은 사람이 있거나 신청 시간이 예약 된 범위에 있지 않습니다 ({0})
att_self_getPhotoFailed=사진이 없습니다
att_self_view=보기
# 二维码
att_param_qrCodeUrl=QR 코드 URL
att_param_qrCodeUrlHref=서버 주소 : 포트
att_param_appAttQrCode=모바일 근태 QR 코드
att_param_timingFrequency=시간 간격 : 5-59 분 또는 1-24 시간
att_sign_signTimeNotNull=추가 로그 시간은 비워 둘 수 없습니다
att_apply_overLastMonth=신청 기간이 지난 달보다 오래되었습니다
att_apply_withoutDetail=프로세스 세부 사항 없음
att_flowable_noAuth=운영 관리자 계정으로 확인하십시오
att_apply_overtimeOverMaxTimeLong=최대 초과 근무시간 초과
# 考勤设置参数符号
att_other_arrive=√
att_other_late=지각
att_other_early=조퇴
att_other_absent=결근
att_other_noSignIn=출근로그 없음
att_other_noSignOff=퇴근로그 없음
att_other_leave=휴가
att_other_overtime=초과근무
att_other_off=조정
att_other_classes=보충
att_other_trip=출장
att_other_out=외근
att_other_incomplete=ㅏ
att_other_outcomplete=ㅓ
# 服务器下发命令
att_devCmd_submitTime=인증 시간
att_devCmd_returnedResult=반환 결과
att_devCmd_returnTime=반환 시간
att_devCmd_content=Cmd 내용
att_devCmd_clearCmd=Cmd 리스트 삭제
# 实时点名
att_realTime_selectDept=부서를 선택하십시오
att_realTime_noSignPers=미등록자
att_realTime_signPers=출근
att_realTime_signMonitor=출근 모니터링
att_realTime_signDateTime=정시 확인
att_realTime_realTimeSet=실시간 근태 설정
att_realTime_openRealTime=실시간 근태 활성화
att_realTime_rollCallEnd=실시간 근태 종료
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=스케줄
att_personSch_cycleSch=스케줄 주기
att_personSch_cleanCycleSch=스케줄 삭제 주기
att_personSch_cleanTempSch=임시 스케줄 삭제
att_personSch_personCycleSch=사용자 주기 스케줄
att_personSch_deptCycleSch=부서 주기 스케줄
att_personSch_groupCycleSch=그룹 주기 스케줄
att_personSch_personTempSch=임시 사용자 스케줄
att_personSch_deptTempSch=부서 임시 스케줄
att_personSch_groupTempSch=그룹 임시 스케줄
att_personSch_checkGroupFirst=그룹 또는 사용자 목록을 확인하여 운영하십시오
att_personSch_sureDeleteGroup={0}과 그룹에 해당하는 스케줄을 삭제 하시겠습니까?
att_personSch_sch=스케줄
att_personSch_delSch=스케줄 삭제
#考勤计算
att_statistical_sureAllCalculate=전체 사용자 근태 계산을 실행하시겠습니까?
#异常管理
att_exception_downTemplate=템플릿 다운로드 및 가져오기
att_exception_signImportTemplate=템플릿 다운로드
att_exception_leaveImportTemplate=템플릿 다운로드
att_exception_overtimeImportTemplate=템플릿 다운로드
att_exception_adjustImportTemplate=템플릿 다운로드
att_exception_cellDefault=필수 항목이 아닙니다
att_exception_cellRequired=필수 필드
att_exception_cellDateTime=필수 필드, 시간 형식은 yyyy-MM-dd HH : mm : ss입니다. 예 : 2020-07-07 08:30:00
att_exception_cellLeaveTypeName='개인 휴가', '결혼 휴가', '출산 휴가', '병가', '연간 휴가', '출산 휴가', '가족 휴가', '육아 휴가', '출장', '외출' 과 같은 필수 항목
att_exception_cellOvertimeSign='일반 초과 근무', '휴일 초과 근무', '휴일 초과 근무' 와 같은 필수 항목
att_exception_cellAdjustType=다음과 같은 필수 항목 : '휴가 이월', '스케줄 이월'
att_exception_cellAdjustDate=필수 필드, 시간 형식은 yyyy-MM-dd입니다. 예 : 2020-07-07
att_exception_cellShiftName=수정 유형이 스케줄 인 경우 필수 항목
att_exception_refuse=거부
att_exception_end=비정상적인 종료
att_exception_delete=삭제
att_exception_stop=일시 중지
#时间段
att_timeSlot_normalTimeAdd=정규 근무 시간대 추가
att_timeSlot_elasticTimeAdd=유연 근무 시간대 추가
#班次
att_shift_addRegularShift=정규 근무 추가
att_shift_addFlexibleShift=유연 근무 추가
#参数设置
att_param_notLeaveSetting=비 휴가 계산 설정
att_param_smallestUnit=최소 단위
att_param_workDay=근무일
att_param_roundingControl=반올림 제어
att_param_abort=Down (삭제)
att_param_rounding=반올림
att_param_carry=무조건 반올림하여 계산
att_param_reportSymbol=보고서 표시 기호
att_param_convertCountValid=숫자를 입력하십시오 / 소수점 하나만 허용됩니다
att_other_leaveThing=휴가
att_other_leaveMarriage=결혼
att_other_leaveBirth=출산 휴가
att_other_leaveSick=병가
att_other_leaveAnnual=연차
att_other_leaveFuneral=상조
att_other_leaveHome=개인 사유
att_other_leaveNursing=육아 휴가
att_other_leavetrip=출장
att_other_leaveout=외근
att_common_schAndRest=스케줄 및 휴식
att_common_timeLongs=시간 길이
att_personSch_checkDeptOrPersFirst=부서 또는 사용자 목록을 확인하십시오
att_personSch_checkCalendarFirst=먼저 스케줄 일자를 선택하십시오
att_personSch_cleanCheck=체크 삭제
att_personSch_delTimeSlot=선택된 스케줄 삭제
att_personSch_repeatTimeSlotNoAdd=반복 시간은 추가되지 않습니다
att_personSch_showSchInfo=스케줄 정보 표시
att_personSch_sureToCycleSch=주기적으로 {0}을 (를) 스케줄 설정하시겠습니까?
att_personSch_sureToTempSch=일시적으로 {0}을 (를) 스케줄 설정하시겠습니까?
att_personSch_sureToCycleSchDeptOrGroup={0} 아래의 전체 사용자를 주기적으로 스케줄 설정하시겠습니까?
att_personSch_sureToTempSchDeptOrGroup={0} 아래의 전체 사용자를 임시로 스케줄 설정하시겠습니까?
att_personSch_sureCleanCycleSch={1}에서 {2}까지의 정기 스케줄 {0}을 (를) 삭제하시겠습니까?
att_personSch_sureCleanTempSch={1}에서 {2}까지의 임시 스케줄 {0}을 (를) 삭제하시겠습니까?
att_personSch_sureCleanCycleSchDeptOrGroup={0} 미만의 모든 사용자에 대해 {1}에서 {2}까지의 정기 스케줄을 삭제하시겠습니까?
att_personSch_sureCleanTempSchDeptOrGroup={0} 미만의 모든 사용자에 대해 {1}에서 {2}까지의 임시 스케줄을 삭제하시겠습니까?
att_personSch_today=당일
att_personSch_timeSoltName=기간명
att_personSch_export=사용자 스케줄 내보내기
att_personSch_exportTemplate=임시 사용자 스케줄 템플릿 내보내기
att_personSch_import=임시 사용자 스케줄 가져오기
att_personSch_tempSchTemplate=임시 사용자 스케줄 템플릿 가져오기
att_personSch_tempSchTemplateTip=스케줄 시작 시간과 종료 시간을 선택하고 해당 범위 안에 스케줄 템플릿을 다운로드하십시오
att_personSch_opTip=운영 지침
att_personSch_opTip1=1. 마우스로 시간 슬롯을 끌어와서 스케줄을 설정할 수 있습니다
att_personSch_opTip2=2. 스케줄 관리에서 일자를 두 번 클릭하여 스케줄 설정하십시오
att_personSch_opTip3=3. 스케줄 관리에서 마우스를 누른 채로 여러 일자를 선택하여 스케줄 설정을 하십시오
att_personSch_schRules=스케줄 규칙
att_personSch_schRules1=1. 주기적 스케줄 : 교차 여부와 상관없이 동일한 일자 이후 이전 스케줄을 덮어씁니다
att_personSch_schRules2=2. 임시 스케줄 : 같은 일자에 교차가 있고, 이전 임시 스케줄을 덮어씁니다 / 교차가 없다면 동시에 존재합니다
att_personSch_schRules3=3. 기간 및 임시 : 같은 일자에 교차가 있는 경우 해당 기간을 일시적으로 덮고, 교차가 없으면 동시에 존재합니다
att_personSch_schStatus=스케줄 상태
#左侧菜单-排班管理
att_leftMenu_schDetails=스케줄 정보
att_leftMenu_detailReport=출석내역 보고서
att_leftMenu_signReport=근태 수정 세부 정보
att_leftMenu_leaveReport=세부 정보 양식 나가기
att_leftMenu_abnormal=예외 보고서
att_leftMenu_yearLeaveSumReport=연간 휴가합계 보고서
att_leave_maxFileCount=최대 4장의 사진을 추가 할 수 있습니다
#时间段
att_timeSlot_add=근무규칙 설정
att_timeSlot_select=근무규칙을 선택하십시오
att_timeSlot_repeat=근무규칙 {0} 이 중복됩니다
att_timeSlot_overlapping=근무규칙에서 {0}기간이 "{1}"의 근무규칙과 겹칩니다
att_timeSlot_addFirst=근무규칙을 설정 하십시오
att_timeSlot_notEmpty=사용자 ID {0}에 해당하는 근무규칙은 비워둘 수 없습니다
att_timeSlot_notExist=사용자 ID {0}에 해당하는 "{1}"근무규칙이 없습니다
att_timeSlot_repeatEx=사용자 ID {0}에 해당하는 "{1}"기간이 "{2}"의 근무규칙과 겹칩니다
att_timeSlot_importRepeat=사용자 ID {0}에 해당하는 "{1}"기간이 반복됩니다
att_timeSlot_importNotPin=시스템에 {0} 해당 사용자 ID가 없습니다
att_timeSlot_elasticTimePeriod=인원 번호 {0}, 탄력 시간대 "{1}" 을 가져올 수 없습니다!
#导入
att_import_overData=현재 가져오기 수는 {0} 입니다 / 최대 한도를 초과합니다 (30,000)
att_import_existIllegalType=가져온 {0} 에 잘못된 유형이 있습니다
#验证方式
att_verifyMode_0=자동 인식
att_verifyMode_1=지문
att_verifyMode_2=비밀번호
att_verifyMode_3=비밀번호
att_verifyMode_4=카드
att_verifyMode_5=지문 or 비밀번호
att_verifyMode_6=지문 or 카드
att_verifyMode_7=카드 or 비밀번호
att_verifyMode_8=비밀번호 + 지문
att_verifyMode_9=지문 + 비밀번호
att_verifyMode_10=카드 + 지문
att_verifyMode_11=카드 + 비밀번호
att_verifyMode_12=지문 + 비밀번호 + 카드
att_verifyMode_13=사용자 ID +지문 + 비밀번호
att_verifyMode_14=(사용자 ID + 지문) or (카드 + 지문)
att_verifyMode_15=얼굴
att_verifyMode_16=얼굴 + 지문
att_verifyMode_17=얼굴 + 비밀번호
att_verifyMode_18=얼굴 + 카드
att_verifyMode_19=얼굴 + 지문 + 카드
att_verifyMode_20=얼굴 + 지문 + 비밀번호
att_verifyMode_21=손가락 정맥
att_verifyMode_22=손가락 정맥 + 비밀번호
att_verifyMode_23=손가락 정맥 + 카드
att_verifyMode_24=손가락 정맥 + 비밀번호 + 카드
att_verifyMode_25=손바닥 정맥
att_verifyMode_26=손바닥 정맥 + 카드
att_verifyMode_27=손바닥 정맥 + 얼굴
att_verifyMode_28=지문 + 지문
att_verifyMode_29=손바닥 정맥 + 지문 + 얼굴
# 工作流
att_flow_schedule=승인 진행률
att_flow_schedulePass=(승인)
att_flow_scheduleNot=(승인되지 않음)
att_flow_scheduleReject=(거부)
# 工作时长表
att_workTimeReport_total=총 근무 시간
# 自动导出报表
att_autoExport_startEndTime=시작 및 종료시간
# 年假
att_annualLeave_setting=잔여 연차 휴가 설정
att_annualLeave_settingTip1=연차 휴가 잔여 기능을 사용하기 위해서는 사용자별 근무 스케줄을 설정해야 하며, 설정되지 않은 경우 사용자의 연차 잔여는 빈 상태로 표시됩니다
att_annualLeave_settingTip2=현재 일자가 정산 일자보다 크면 다음 년도부터 수정 사항이 적용되며, 현재 일자가 정산 일자보다 작으면 해당 일자가 삭제되고 연차가 재발행됩니다
att_annualLeave_calculate=연차 갱신일
att_annualLeave_workTimeCalculate=근무시간 비율에 따른 계산
att_annualLeave_rule=연차 규칙
att_annualLeave_ruleCountOver=최대 설정 수 제한에 도달했습니다
att_annualLeave_years=근무 연수
att_annualLeave_eachYear=매년
att_annualLeave_have=Yes
att_annualLeave_days=연차 휴가 일수
att_annualLeave_totalDays=총 연차 휴가
att_annualLeave_remainingDays=남은 연차 휴가
att_annualLeave_consecutive=연차 규칙 설정은 연속 연도여야 합니다
# 年假结余表
att_annualLeave_report=연차 대차 대조표
att_annualLeave_validDate=유효한 일자
att_annualLeave_useDays={0} 일 사용
att_annualLeave_calculateDays={0} 일 해제
att_annualLeave_notEnough={0}의 연차가 부족합니다
att_annualLeave_notValidDate={0}은 연차 유효 기간이 아닙니다
att_annualLeave_notDays={0}은 남은 연차가 없습니다
att_annualLeave_tip1=ㅇㅇ은 작년 9월 1일에 입사했습니다
att_annualLeave_tip2=잔여 연차 설정
att_annualLeave_tip3=연차 갱신일은 매년 1월 1일이며, 근로율에 따라 반올림하여 계산됩니다 / 근속 년수가 1년 이하인 경우 3일, 근속 년수가 3년 이하인 경우 5일의 연차가 있습니다
att_annualLeave_tip4=연차 유급계산
att_annualLeave_tip5=작년 09-01 ~ 12-31 4/12x3=1.0일
att_annualLeave_tip6=올해 01-01 ~ 12-31 4.0 일 (올해 01-01 ~ 08-31 8 / 12x3=2.0일 / 올해 09-01 ~ 12-31 4 / 12x5=2.0일)
# att SDC
att_sdc_name=비디오 장치
att_sdc_wxMsg_firstData=근태 체크 알람이 있습니다
att_sdc_wxMsg_stateData=출근 인증 성공
att_sdc_wxMsg_remark=알림 : 최종 근태 결과는 체크인 세부정보 페이지에 따라 변경됩니다
# 时间段
att_timeSlot_conflict=근무시간이 당일 다른 근무시간과 겹칩니다
att_timeSlot_selectFirst=근무시간을 선택하십시오
# 事件中心
att_eventCenter_sign=출석 체크인
#异常管理
att_exception_classImportTemplate=템플릿 다운로드
att_exception_cellClassAdjustType=필수 필드(예: "{0}", "{1}", "{2}")
att_exception_swapDateDate=비필수 필드, 시간 형식은 yyyy-MM-dd입니다. 예: 2020-07-07
#消息中心
att_message_leave=출석 알림 {0}
att_message_leaveContent={0} 이(가) {1}을(를) 신청했으며 {2} 시간은 {3}~{4}입니다.
att_message_leaveTime=휴가 기간
att_message_overtime=출석 및 초과근무 알림
att_message_overtimeContent={0} 초과 근무를 신청했으며 초과 근무시간은 {1}~{2}입니다.
att_message_overtimeTime=초과 근무시간
att_message_sign=근태 수정 알림
att_message_signContent={0}이(가) 근태 수정을 신청했으며 근태 수정 시간은 {1}입니다.
att_message_adjust=휴식 시간 조정 알림
att_message_adjustContent={0}이(가) 휴식 시간 조정을 신청했으며 조정 날짜는 {1}입니다.
att_message_class=스케줄 조정 알림
att_message_classContent=스케줄 조정 세부정보
att_message_classContent0={0}이(가) 스케줄 조정을 신청했으며 조정 날짜는 {1}이고, 조정 사항는 {2}입니다.
att_message_classContent1={0}이(가) 스케줄 조정을 신청했으며 조정 날짜는 {1}이고, 조정 날짜는 {2}입니다.
att_message_classContent2={0}({1}) 과 {2}({3})의 스케줄을 교환합니다
#推送中心
att_pushCenter_transaction=출석 기록
# 时间段
att_timeSlot_workTimeNotEqual=출근 시간은 퇴근 시간과 같을 수 없습니다
att_timeSlot_signTimeNotEqual=체크인 시작 시간은 체크아웃 종료 시간과 같을 수 없습니다.
# 北向接口A
att_api_notNull={0}은(는) 비워둘 수 없습니다!
att_api_startDateGeEndDate=시작 시간은 종료 시간보다 크거나 같을 수 없습니다!
att_api_leaveTypeNotExist=휴가 목록에 존재하지 않는 유형입니다
att_api_imageLengthNot2000=이미지 경로의 길이는 2000을 초과할 수 없습니다!
# 20221230新增国际化
att_personSch_workTypeNotNull=사용자 번호 {0}에 해당하는 작업 유형은 비워둘 수 없습니다!
att_personSch_workTypeNotExist=사용자 번호 {0}에 해당하는 작업 유형이 없습니다!
att_annualLeave_recalculate=재계산
# 20230530新增国际化
att_leftMenu_dailyReport=일일 출석 보고서
att_leftMenu_overtimeReport=초과 근무 보고서
att_leftMenu_lateReport=늦은 보고
att_leftMenu_earlyReport=조기 신고 종료
att_leftMenu_absentReport=부재 신고
att_leftMenu_monthReport=출석 월별 보고서
att_leftMenu_monthWorkTimeReport=월간 작업 시간 보고서
att_leftMenu_monthCardReport=월별 카드 보고서
att_leftMenu_monthOvertimeReport=월간 초과 근무 보고서
att_leftMenu_overtimeSummaryReport=직원 초과 근무 요약 보고서
att_leftMenu_deptOvertimeSummaryReport=부서 초과 근무 요약 보고서
att_leftMenu_deptLeaveSummaryReport=부서 휴가 요약 보고서
att_annualLeave_calculateDay=연차 휴가 일수
att_annualLeave_adjustDay=일 조정
att_annualLeave_sureSelectDept=선택한 부서에서 {0} 작업을 수행하시겠습니까?
att_annualLeave_sureSelectPerson=선택한 사람에 대해 {0} 작업을 수행하시겠습니까?
att_annualLeave_calculateTip1=근속 기간에 따라 계산할 경우: 연차 계산은 월 단위로 정확하며, 근속 기간이 10년 3개월이면 10년 3개월로 계산됩니다.
att_annualLeave_calculateTip2=근속연수를 기준으로 환산하지 않을 경우: 연차산정은 1년 단위로 정확하며, 근속연수가 10년 3개월이면 10년으로 계산합니다.
att_rule_isInCompleteTip=무로그인 또는 노로그아웃이 미완료로 기록되고, 지각, 조퇴, 결근, 유효한 경우 우선순위가 가장 높습니다.
att_rule_absentTip=로그인 없음 또는 로그아웃 없음이 결근으로 기록되는 경우, 결근 기간은 근무 시간에서 지각에서 조퇴까지의 길이를 뺀 것과 같습니다.
att_timeSlot_elasticTip1=0, 유효 시간은 실제 시간과 동일, 결근 없음
att_timeSlot_elasticTip2=실제 시간이 근무 시간보다 길면 유효 시간이 근무 시간과 같아 결근 없음
att_timeSlot_elasticTip3=실제 근무 기간이 근무 기간보다 짧은 경우 유효 기간은 실제 근무 기간과 같고, 결근은 근무 기간에서 실제 근무 기간을 뺀 것과 같습니다.
att_timeSlot_maxWorkingHours=작업 시간은 다음보다 길 수 없습니다.
# 20231030
att_customReport=출석 사용자 지정 보고서
att_customReport_byDayDetail=일별 세부 정보
att_customReport_byPerson=개인별 요약
att_customReport_byDept=부서별 요약
att_customReport_queryMaxRange=쿼리 최대 범위는 4개월입니다.
# 20240630新增国际化
att_personSch_shiftWorkTypeTip1=1. 근무 유형이 정상 근무/휴일 초과 근무 시, 배차 우선순위가 공휴일보다 낮다
att_personSch_shiftWorkTypeTip2=2. 업무 유형이 공휴일에 야근할 때, 배차 우선순위가 공휴일보다 높다
att_personVerifyMode=인력 검증 방법
att_personVerifyMode_setting=인증 방법 설정
att_personSch_importCycSch=인원 주기 배정 가져오기
att_personSch_cycSchTemplate=인원 주기 배열 템플릿
att_personSch_exportCycSchTemplate=인원 주기 배열 템플릿 다운로드
att_personSch_scheduleTypeNotNull=클래스 유형은 비어 있거나 없을 수 없습니다!
att_personSch_shiftNotNull=편수는 비워둘 수 없습니다!
att_personSch_shiftNotExist=편수가 존재하지 않습니다!
att_personSch_onlyAllowOneShift=보통 배열은 한 배열만 허용한다!
att_shift_attShiftStartDateRemark2=주기 시작 날짜가 있는 주는 첫 주입니다.주기 시작 날짜가 있는 달은 첫 번째 달입니다.
#打卡状态
att_cardStatus_setting=근태상태설정
att_cardStatus_name=이름
att_cardStatus_value=값
att_cardStatus_alias=별칭
att_cardStatus_every_day=매일
att_cardStatus_by_week=주로
att_cardStatus_autoState=자동상태
att_cardStatus_attState=근태상태
att_cardStatus_signIn=시그인
att_cardStatus_signOut=시그아웃
att_cardStatus_out=외출
att_cardStatus_outReturn=출장
att_cardStatus_overtime_signIn=오버타임시그인
att_cardStatus_overtime_signOut=오버타임시그아웃
# 20241030新增国际化
att_leaveType_enableMaxDays=연간 제한 활성화
att_leaveType_maxDays=연간 제한 (일)
att_leaveType_applyMaxDays=연간 신청은 {0} 일을 초과할 수 없습니다
att_param_overTimeSetting=초과근무 등급 설정
att_param_overTimeLevel=초과근무 등급 (시간)
att_param_overTimeLevelEnable=초과근무 등급 계산을 활성화하시겠습니까?
att_param_reportColor=보고서 표시 색상
# APP
att_app_signClientTip=이 장치는 오늘 다른 사람에 의해 이미 체크인되었습니다
att_app_noSignAddress=출퇴근 기록 영역 설정되지 않음 관리자에게 연락하여 설정하세요
att_app_notInSignAddress=출퇴근 장소에 도착하지 않아 체크인 불가능함
att_app_attendance=내 출퇴근 기록
att_app_apply=출퇴근 신청
att_app_approve=내가 승인해야 할 것
# 20250530
att_node_leaderNodeExist=직속 상사 승인 노드가 이미 존재합니다
att_signAddress_init=지도 초기화
att_signAddress_initTips=지도 키를 입력하고 주소 선택을 위해 지도를 초기화해주세요