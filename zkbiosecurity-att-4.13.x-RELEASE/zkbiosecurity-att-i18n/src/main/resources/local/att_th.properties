#系统名称
att_systemName=Attendance System 1.0
#=====================================================================
#左侧菜单
att_module=บันทึกเวลา
#左侧菜单-考勤设备
att_leftMenu_attendanceDevice=การจัดการอุปกรณ์บันทึกเวลา
att_leftMenu_device=อุปกรณ์บันทึกเวลา
att_leftMenu_point=จุดเชื่อมต่ออุปกรณ์บันทึกเวลา
att_leftMenu_sign_address=ที่อยู่การเข้าสู่ระบบมือถือ
att_leftMenu_adms_devCmd=คำสั่งจากเซิร์ฟเวอร์
#左侧菜单-基础信息
att_leftMenu_basicInformation=ข้อมูลพื้นฐาน
att_leftMenu_rule=กฏ
att_leftMenu_base_rule=กฎบันทึกเวลา
att_leftMenu_department_rule=กฏแผนก
att_leftMenu_holiday=วันหยุด
att_leftMenu_leaveType=ประเภทลา
att_leftMenu_timingCalculation=การคำนวณแบบเวลา
att_leftMenu_autoExport=รายงานอัตโนมัติ
att_leftMenu_param=ตั้งค่าพารามิเตอร์
#左侧菜单-班次管理
att_leftMenu_shiftManagement=กะทำงาน
att_leftMenu_timeSlot=ตารางเวลา
att_leftMenu_shift=กะทำงาน
#左侧菜单-排班管理
att_leftMenu_scheduleManagement=ตารางเวลา
att_leftMenu_group=กลุ่ม
att_leftMenu_groupPerson=กลุ่มพนักงาน
att_leftMenu_groupSch=ตารางเวลาแบบกลุ่ม
att_leftMenu_deptSch=ตารางเวลาแบบแผนก
att_leftMenu_personSch=ตารางเวลาแบบพนักงาน
att_leftMenu_tempSch=ตารางเวลาแบบชั่วคราว
att_leftMenu_nonSch=พนักงานที่ไม่ได้กำหนดตารางเวลา
#左侧菜单-异常管理
att_leftMenu_exceptionManagement=การจัดการข้อยกเว้น
att_leftMenu_sign=บันทึกเพิ่มเติม
att_leftMenu_leave=ลา
att_leftMenu_trip=การเดินทางเพื่อธุรกิจ
att_leftMenu_out=ออก
att_leftMenu_overtime=ล่วงเวลา
att_leftMenu_adjust=ปรับและอนุมัย
att_leftMenu_class=เปลี่ยนกะทำงาน
#左侧菜单-统计报表
att_leftMenu_statisticalReport=คำนวนรายงาน
att_leftMenu_manualCalculation=คำนวณด้วยตนเอง
att_leftMenu_transaction=รายงานบันทึกทั้งหมด
att_leftMenu_dayCardDetailReport=รายงานบันทึกแบบรายวัน
att_leftMenu_leaveSummaryReport=รายงานลางาน
att_leftMenu_dayDetailReport=รายงานประจำวัน
att_leftMenu_monthDetailReport=รายงานประจำเดือน
att_leftMenu_monthStatisticalReport=รายงานสถิติพนักงานรายเดือน
att_leftMenu_deptStatisticalReport=รายงานสถิติแผนกรายเดือน
att_leftMenu_yearStatisticalReport=รายงานประจำปี (โดยพนักงาน)
att_leftMenu_attSignCallRollReport=ลงชื่อเข้าใช้การโทรเข้า
att_leftMenu_workTimeReport=รายงานเวลาทำงาน
#左侧菜单-设备操作日志
att_leftMenu_device_op_log=บันทึกการทำงานของอุปกรณ์
#左侧菜单-实时点名
att_leftMenu_realTime_callRoll=โทรเข้า
#=====================================================================
#公共
att_common_person=พนักงาน
att_common_pin=รหัส
att_common_group=กลุ่ม
att_common_dept=แผนก
att_common_symbol=สัญลักษณ์
att_common_deptNo=รหัสแผนก
att_common_deptName=ชื่อแผนก
att_common_groupNo=รหัสกลุ่ม
att_common_groupName=ชื่อกลุ่ม
att_common_operateTime=เวลาดำเนินการ
att_common_operationFailed=การดำเนินการล้มเหลว
att_common_id=รหัส
att_common_deptId=รหัสแผนก
att_common_groupId=รหัสกลุ่ม
att_common_deviceId=รหัสอุปกรณ์
att_person_pin=รหัสพนักงาน
att_person_name=ชื่อ
att_person_lastName=นามสกุล
att_person_internalCard=หมายเลขบัตร
att_person_attendanceMode=โหมดการบันทึกเวลา
att_person_normalAttendance=บันทึกเวลาปกติ
att_person_noPunchCard=ไม่มีบัตรบันทึกเวลา
att_common_attendance=บันทึกเวลา
att_common_attendance_hour=บันทึกเวลา (ชั่วโมง)
att_common_attendance_day=บันทึกเวลา (วัน)
att_common_late=มาสาย
att_common_early=ออกจาก
att_common_overtime=โอที
att_common_exception=ข้อยกเว้น
att_common_absent=ขาดงาน
att_common_leave=ลางาน
att_common_trip=เดินทางเพื่อธุรกิจ
att_common_out=ออก
att_common_staff=พนักงาน
att_common_superadmin=ผู้ดูแลระบบ
att_common_msg=เนื้อหาข้อความ
att_common_min=ระยะเวลาข้อความสั้น (นาที)
att_common_letterNumber=สามารถป้อนตัวเลขหรือตัวอักษรเท่านั้น!
att_common_relationDataCanNotDel=ข้อมูลที่เกี่ยวข้องไม่สามารถลบได้
att_common_relationDataCanNotEdit=ข้อมูลที่เกี่ยวข้องไม่สามารถแก้ไขได้
att_common_needSelectOneArea=โปรดเลือกพื้นที่
att_common_neesSelectPerson=โปรดเลือกพนักงาน
att_common_nameNoSpace=ชื่อไม่สามารถมีช่องว่าง!
att_common_digitsValid=มันสามารถป้อนตัวเลขที่ไม่เกินสองทศนิยม
att_common_numValid=ป้อนเฉพาะตัวเลข!
#=====================================================================
#工作面板
att_dashboard_worker=ทำงาน
att_dashboard_today=บันทึกเวลาวันนี้
att_dashboard_todayCount=สถิติการแบ่งกลุ่มบันทึกเวลาวันนี้
att_dashboard_exceptionCount=สถิติที่ผิดปกติ (เดือนนี้)
att_dashboard_lastWeek=สัปดาห์ที่แล้ว
att_dashboard_lastMonth=เดือนที่แล้ว
att_dashboard_perpsonNumber=พนักงานทั้งหมด
att_dashboard_actualNumber=พนักงานมาทำงาน
att_dashboard_notArrivedNumber=พนักงานขาดงาน
att_dashboard_attHour=เวลาทำงาน
#区域
#设备
att_op_syncDev=ซิงโครไนซ์ข้อมูลซอฟต์แวร์ไปยังอุปกรณ์
att_op_account=การตรวจสอบข้อมูลการบันทึกเวลา
att_op_check=อัปโหลดข้อมูลอีกครั้ง
att_op_deleteCmd=ล้างคำสั่งอุปกรณ์
att_op_dataSms=ข้อความสาธารณะ
att_op_clearAttPic=ล้างรูปภาพบันทึกเวลา
att_op_clearAttLog=ล้างธุรกรรมการบันทึกเวลา
att_device_waitCmdCount=คำสั่งที่จะดำเนินการ
att_device_status=เปิดใช้งานสถานะ
att_device_register=อุปกรณ์ที่ลงทะเบียน
att_device_isRegister=อุปกรณ์ที่ลงทะเบียน
att_device_existNotRegDevice=อุปกรณ์เครื่องที่ไม่ได้ลงทะเบียนไม่สามารถรับข้อมูลได้!
att_device_fwVersion=เวอร์ชั่นของเฟิร์มแวร์
att_device_transInterval=ระยะเวลารีเฟรช (นาที)
att_device_cmdCount=จำนวนคำสั่งสูงสุดในการสื่อสารกับเซิร์ฟเวอร์
att_device_delay=เรียกดูเวลาบันทึก(วินาที)
att_device_timeZone=เขตเวลา
att_device_operationLog=บันทึกการทำงาน
att_device_registeredFingerprint=ลงทะเบียนลายนิ้วมือ
att_device_registeredUser=ลงทะเบียนพนักงาน
att_device_fingerprintImage=ภาพลายนิ้วมือ
att_device_editUser=แก้ไขพนักงาน
att_device_modifyFingerprint=แก้ไขลายนิ้วมือ
att_device_faceRegistration=การลงทะเบียนใบหน้า
att_device_userPhotos=รูปพนักงาน
att_device_attLog=จะอัปโหลดบันทึกเวลาเข้างานหรือไม่
att_device_operLog=จะอัปโหลดข้อมูลพนักงานหรือไม่
att_device_attPhoto=จะอัปโหลดรูปภาพบันทึกเวลาหรือไม่
att_device_isOnLine=สถานะออนไลน์
att_device_InputPin=ป้อนหมายเลขบุคคล
att_device_getPin=ดึงข้อมูลพนักงานที่ระบุ
att_device_separatedPin=จำนวนพนักงานจำนวนมากให้คั่นด้วยเครื่องหมายจุลภาค
att_device_authDevice=อุปกรณ์ที่ได้รับอนุญาต
att_device_disabled=อุปกรณ์ต่อไปนี้ถูกปิดใช้งานและไม่สามารถใช้งานได้!
att_device_autoAdd=อุปกรณ์ใหม่จะถูกเพิ่มโดยอัตโนมัติ
att_device_receivePersonOnlyDb=รับเฉพาะข้อมูลของพนักงานที่อยู่ในฐานข้อมูล
att_devMenu_control=การควบคุมอุปกรณ์
att_devMenu_viewOrGetInfo=ดูและดึงข้อมูล
att_devMenu_clearData=ล้างข้อมูลอุปกรณ์
att_device_disabledOrOffline=อุปกรณ์ไม่ได้เปิดใช้งานหรือไม่ออนไลน์ไม่สามารถใช้งานได้!
att_device_areaStatus=สถานะพื้นที่อุปกรณ์
att_device_areaCommon=พื้นที่ปกติ
att_device_areaEmpty=พื้นที่ว่างเปล่า
att_device_isRegDev=การปรับเปลี่ยนเขตเวลาหรือสถานะของผู้รับจดทะเบียนต้องให้อุปกรณ์รีสตาร์ทเพื่อให้มีผล!
att_device_canUpgrade=สามารถอัปเกรดอุปกรณ์ต่อไปนี้ได้
att_device_offline=อุปกรณ์ต่อไปนี้ออฟไลน์และไม่สามารถใช้งานได้!
att_device_oldProtocol=โปรโตคอลเก่า
att_device_newProtocol=โปรโตคอลใหม่
att_device_noMoreTwenty=แพ็คเกจอัพเกรดเฟิร์มแวร์ของอุปกรณ์โปรโตคอลเก่าต้องไม่เกิน 20M
att_device_transferFilesTip=ตรวจพบเฟิร์มแวร์สำเร็จ ,กำลังถ่ายโอนไฟล์
att_op_clearAttPers=ล้างพนักงานในอุปกรณ์
#区域人员
att_op_forZoneAddPers=การตั้งค่าพนักงานในพื้นที่
att_op_dataUserSms=ข้อความส่วนตัว
att_op_syncPers=ซิงโครไนซ์กับอุปกรณ์อีกครั้ง
att_areaPerson_choiceArea=โปรดเลือกพื้นที่!
att_areaPerson_byAreaPerson=ตามพื้นที่
att_areaPerson_setByAreaPerson=กำหนดโดยพนักงานในพื้นที่
att_areaPerson_importBatchDel=นำเข้าการลบ
att_areaPerson_syncToDevSuccess=การดำเนินการสำเร็จ! โปรดรอคำสั่งที่จะส่ง
att_areaPerson_personId=รหัสพนักงาน
att_areaPerson_areaId=รหัสพื้นที่
att_area_existPerson=มีพนักงานอยู่ในพื้นที่
att_areaPerson_notice1=พื้นที่หรือพนักงานต้องไม่ว่างเปล่าในเวลาเดียวกัน
att_areaPerson_notice2=ไม่มีมีพนักงานหรือพื้นที่!
att_areaPerson_notice3=ไม่พบอุปกรณ์ในพื้นที่!
att_areaPerson_addArea=เพิ่มพื้นที่
att_areaPerson_delArea=ลบพื้นที่
att_areaPerson_persNoExit=ไม่มีพนักงาน
att_areaPerson_importTip1=โปรดตรวจสอบว่าพนักงานที่นำเข้ามีอยู่แล้วในโมดูลพนักงาน
att_areaPerson_importTip2=พนักงานที่นำเข้าแบบกลุ่มจะไม่ถูกส่งไปยังอุปกรณ์โดยอัตโนมัติและจำเป็นต้องซิงโครไนซ์ด้วยตนเอง
att_areaPerson_addAreaPerson=เพิ่มพื้นที่ของพนักงาน
att_areaPerson_delAreaPerson=ลบพื้นที่ของพนักงาน
att_areaPerson_importDelAreaPerson=นำเข้าและลบพื้นที่ของพนักงาน
att_areaPerson_importAreaPerson=นำเข้าพื้นที่ของพนักงาน
#考勤点
att_attPoint_name=ชื่อจุดบันทึกเวลา
att_attPoint_list=รายการจุดบันทึกเวลา
att_attPoint_deviceModule=โมดูลอุปกรณ์
att_attPoint_acc=การควบคุมประตู
att_attPoint_park=จอดรถ
att_attPoint_ins=FaceKiosk
att_attPoint_pid=ใบรับรองของพนักงาน
att_attPoint_vms=วิดีโอ
att_attPoint_psg=สิทธิการผ่าน
att_attPoint_doorList=รายการประตู
att_attPoint_deviceList=รายการอุปกรณ์
att_attPoint_channelList=รายการช่อง
att_attPoint_gateList=รายชื่อประตู
att_attPoint_recordTypeList=ดึงบันทึกประเภท
att_attPoint_door=โปรดเลือกประตูที่สอดคล้องกัน
att_attPoint_device=โปรดเลือกอุปกรณ์ที่เกี่ยวข้อง
att_attPoint_gate=โปรดเลือกประตูที่เกี่ยวข้อง
att_attPoint_normalPassRecord=บันทึกผ่านปกติ
att_attPoint_verificationRecord=บันทึกการตรวจสอบ
att_person_attSet=ตั้งค่าการบันทึกเวลา
att_attPoint_point=โปรดเลือกจุดบันทึกเวลา
att_attPoint_count=ใบอนุญาตการบันทึกเวลาไม่เพียงพอที่ได้รับอนุญาต การดำเนินการล้มเหลว!
att_attPoint_notSelect=โมดูลไม่ได้รับการกำหนดค่า
att_attPoint_accInsufficientPoints=จุดบันทึกเวลาไม่เพียงพอที่ได้รับอนุญาติสำหรับบันทึกการควบคุมประตู
att_attPoint_parkInsufficientPoints=จุดบันทึกเวลาไม่เพียงพอที่ได้รับอนุญาติสำหรับบันทึกที่จอดรถ
att_attPoint_insInsufficientPoints=จุดบันทึกเวลาเข้างานไม่เพียงพอที่ได้รับอนุญาติสำหรับ faceKiosk
att_attPoint_pidInsufficientPoints=จุดบันทึกเวลาเวลาไม่เพียงพอที่ได้รับอนุญาติสำหรับพนักงาน
att_attPoint_doorOrParkDeviceName=ชื่อประตูหรือชื่ออุปกรณ์จอดรถ
att_attPoint_vmsInsufficientPoints=จุดบันทึกเวลาไม่เพียงพอที่ได้รับอนุญาติสำหรับวิดีโอ
att_attPoint_psgInsufficientPoints=จุดบันทึกเวลาไม่เพียงพอสำหรับช่อง
att_attPoint_delDevFail=ลบอุปกรณ์ล้มเหลวอุปกรณ์ถูกใช้เป็นการบันทึกเวลา
att_attPoint_pullingRecord=จุดบันทึกเวลาถูกบันทึกเป็นประจำ โปรดรอสักครู่!
att_attPoint_lastTransactionTime=เวลาดึงข้อมูลล่าสุด
att_attPoint_masterDevice=อุปกรณ์หลัก
att_attPoint_channelName=ชื่อช่อง
att_attPoint_cameraName=ชื่อกล้อง
att_attPoint_cameraIP=กล้อง ip
att_attPoint_channelIP=ช่อง ip
att_attPoint_gateNumber=หมายเลขประตู
att_attPoint_gateName=ชื่อประตู
#APP考勤签到地址
att_signAddress_address=ที่อยู่
att_signAddress_longitude=ลองจิจูด
att_signAddress_latitude=ละติจูด
att_signAddress_range=ช่วงที่มีประสิทธิภาพ
att_signAddress_rangeUnit=หน่วยคือเมตร
#=====================================================================
#规则
att_rule_baseRuleSet=การตั้งค่ากฎพื้นฐาน
att_rule_countConvertSet=การตั้งค่าการคำนวณ
att_rule_otherSet=การตั้งค่าอื่น ๆ
att_rule_baseRuleSignIn=กฎการเช็คอิน
att_rule_baseRuleSignOut=กฎการเช็คเอาท์
att_rule_earliestPrinciple=กฎที่เก่าที่สุด
att_rule_theLatestPrinciple=กฎล่าสุด
att_rule_principleOfProximity=กฎความใกล้ชิด
att_rule_baseRuleShortestMinutes=ระยะเวลาขั้นต่ำควรมากกว่า (ขั้นต่ำ 10 นาที)
att_rule_baseRuleLongestMinutes=ช่วงเวลาสูงสุดควรน้อยกว่า (สูงสุด 1,440 นาที)
att_rule_baseRuleLateAndEarly=การลาพักล่าช้าและก่อนกำหนดถูกนับว่าไม่อยู่
att_rule_baseRuleCountOvertime=สถิติการทำงานล่วงเวลา
att_rule_baseRuleFindSchSort=ค้นหากะบันทึก
att_rule_groupGreaterThanDepartment=กลุ่ม-> แผนก
att_rule_departmentGreaterThanGroup=แผนก-> กลุ่ม
att_rule_baseRuleSmartFindClass=กฎกะการจับคู่อัตโนมัติ
att_rule_timeLongest=ระยะเวลาการทำงานที่ยาวนานที่สุด
att_rule_exceptionLeast=ผิดปกติน้อยที่สุด
att_rule_baseRuleCrossDay=คำนวณการเข้างานสำหรับการเลื่อนข้ามวัน
att_rule_firstDay=วันแรก
att_rule_secondDay=วันที่สอง
att_rule_baseRuleShortestOvertimeMinutes=การทำงานล่วงเวลาที่สั้นที่สุดครั้งเดียว (นาที)
att_rule_baseRuleMaxOvertimeMinutes=การทำงานล่วงเวลาสูงสุด (นาที)
att_rule_baseRuleElasticCal=การคำนวณระยะเวลาที่ยืดหยุ่น
att_rule_baseRuleTwoPunch=เวลาสะสมสำหรับการเข้างานทุกๆสองครั้ง
att_rule_baseRuleStartEnd=การคำนวณเวลาเข้างานครั้งแรกสุดและครั้งสุดท้ายของเวลา
att_rule_countConvertHour=กฎการแปลงชั่วโมง
att_rule_formulaHour=สูตร: ชั่วโมง=นาที / 60
att_rule_countConvertDay=กฎการแปลงวัน
att_rule_formulaDay=สูตร： วัน=นาที / จำนวนนาทีในการทำงานต่อวัน
att_rule_inFormulaShallPrevail=นำผลลัพธ์ที่คำนวณโดยสูตรเป็นมาตรฐาน
att_rule_remainderHour=ส่วนที่เหลือมากกว่าหรือเท่ากับ
att_rule_oneHour=บันทึกเป็นหนึ่งชั่วโมง
att_rule_halfAnHour=คำนวณเป็นครึ่งชั่วโมงมิฉะนั้นละเว้น
att_rule_remainderDay=ผลหารมากกว่าหรือเท่ากับนาทีการทำงาน
att_rule_oneDay=% คำนวณเป็นหนึ่งวัน
att_rule_halfAnDay=% คำนวณเป็นครึ่งวันมิฉะนั้นละเว้น
att_rule_countConvertAbsentDay=เกณฑ์การแปลงวัน
att_rule_markWorkingDays=คำนวณเป็นวันทำงาน
att_rule_countConvertDecimal=ตัวเลขที่แน่นอนของจุดทศนิยม
att_rule_otherSymbol=การตั้งค่าสัญลักษณ์ผลลัพธ์การบันทึกเวลาทำงานในรายงาน
att_rule_arrive=คาดหมาย/ความจริง
att_rule_noSignIn=ไม่เช็คอิน
att_rule_noSignOff=ไม่เช็คเอาท์
att_rule_off=ปรับที่เหลือ
att_rule_class=บึนทึกเวลาเพิ่มเติม
att_rule_shortLessLong=ตารางเวลาบันทึกเวลาต้องไม่ยาวกว่าช่วงเวลาที่บันทึกเวลาที่ยาวที่สุด
att_rule_symbolsWarning=คุณต้องกำหนดสัญลักษณ์ในรายงานการบันทึกเวลา
att_rule_reportSettingSet=ตั้งค่าการส่งออกรายงาน
att_rule_shortDateFormat=รูปแบบวันที่
att_rule_shortTimeFormat=รูปแบบเวลา
att_rule_baseRuleSignBreakTime=ใช้เวลาในช่วงพักหรือไม่
att_leftMenu_custom_rule=กฎที่กำหนดเอง
att_custom_rule_already_exist={0} มีกฎที่กำหนดเองอยู่แล้ว
att_add_group_custom_rule=เพิ่มกฎการจัดกลุ่ม
att_custom_rule_type=ประเภทของกฎ
att_rule_type_group=กฎการจัดกลุ่ม
att_rule_type_dept=กฎของแผนก
att_custom_rule_orgNames=การใช้วัตถุ
att_rult_maxOverTimeType1=ไม่ จำกัด
att_rult_maxOverTimeType2=สัปดาห์นี้
att_rult_maxOverTimeType3=เดือนนี้
att_rule_countConvertDayRemark1=ตัวอย่าง: เวลาทำงานที่มีประสิทธิภาพคือ 500 นาทีและเวลาทำงานคือ 480 นาทีต่อวัน ผลลัพธ์คือ 500/480=1.04 และทศนิยมสุดท้ายจะถูกเก็บไว้ที่ 1.0
att_rule_countConvertDayRemark2=ตัวอย่าง: เวลาทำงานที่มีประสิทธิภาพคือ 500 นาทีและเวลาทำงานคือ 480 นาทีต่อวัน ผลลัพธ์คือ 500/480=1.04, 1.04> 0.8
att_rule_countConvertDayRemark3=ตัวอย่าง: เวลาทำงานที่มีประสิทธิภาพคือ 300 นาทีและเวลาทำงานคือ 480 นาทีต่อวัน ผลลัพธ์คือ 300/480=0.625, 0.2 <0.625 <0.8 สำหรับครึ่งวัน
att_rule_countConvertDayRemark4=เกณฑ์การแปลงวัน: บันทึกเป็นวันทำการ ในช่วงเวลาไม่ทำงาน
att_rule_countConvertDayRemark5=มันถูกบันทึกเป็นจำนวนวันทำงาน: มัน จำกัด อยู่ที่การคำนวณจำนวนวันที่เสร็จสมบูรณ์และตราบใดที่มีความสำเร็จในแต่ละช่วงเวลาความสมบูรณ์ของการคำนวณจะขึ้นอยู่กับจำนวนวันทำงาน ของช่วงเวลา;
att_rule_baseRuleSmartFindRemark1=เวลาที่ยาวนานที่สุด: ตามจุดการ์ดของวันให้คำนวณชั่วโมงทำงานที่สอดคล้องกับการเปลี่ยนแต่ละครั้งในวันนั้นและค้นหาการเปลี่ยนแปลงของเวลาทำงานที่ยาวที่สุดของวัน
att_rule_baseRuleSmartFindRemark2=ความผิดปกติขั้นต่ำ: คำนวณจำนวนเวลาที่ผิดปกติที่สอดคล้องกับการเปลี่ยนแปลงแต่ละครั้งในแต่ละวันตามจุดของการ์ดในแต่ละวันและหาการเปลี่ยนแปลงที่มีจำนวนความผิดปกติน้อยที่สุดในวันนั้นเพื่อคำนวณเวลาทำงาน
att_rule_baseRuleHourValidator=นาทีการคำนวนครึ่งชั่วโมงไม่สามารถมากกว่าหรือเท่ากับ 1 ชั่วโมง!
att_rule_baseRuleDayValidator=ระยะเวลาการคิดครึ่งวันไม่สามารถมากกว่าหรือเท่ากับระยะเวลาการตัดสินของหนึ่งวัน!
att_rule_overtimeWarning=ระยะเวลาการทำงานล่วงเวลาสูงสุดต้องไม่น้อยกว่าระยะเวลาการล่วงเวลาต่ำสุดขั้นต่ำเพียงครั้งเดียว!
att_rule_noSignInCountType=ขาดการเช็คอินนับเป็น
att_rule_absent=การขาดงาน
att_rule_earlyLeave=ออกก่อนเวลา
att_rule_noSignOffCountType=ขาดการเช็คเอาต์นับเป็น
att_rule_minutes=นาที
att_rule_noSignInCountLateMinute=ขาดการเช็คอินนับเป็นนาทีที่ช้าสุด
att_rule_noSignOffCountEarlyMinute=เช็คเอาต์ไม่ครบ นับเป็นนาทีที่เหลือที่ออกก่อนเวลา
att_rule_incomplete=ไม่สมบูรณ์
att_rule_noCheckInIncomplete=ไม่สมบูรณ์และไม่มีการเช็คอิน
att_rule_noCheckOutIncomplete=ไม่สมบูรณ์และไม่มีการเช็คเอาท์
att_rule_lateMinuteWarning=นาทีสุดท้ายที่เข้างานในการเช็คอินควรมากกว่า 0 และน้อยกว่าระยะเวลาการเข้างานที่ยาวที่สุด
att_rule_earlyMinuteWarning=นาที่แรกที่เช็คเอาท์ในการออกก่อนเวลาควรมากว่า 0 และน้อยกว่าระยะเวลาการเข้างานที่ยาวที่สุด
att_rule_baseRuleNoSignInCountLateMinuteRemark=เมื่อไม่ได้ทำการเช็คอินจะถูกนับเป็นสายถ้าไม่ได้ทำการเช็คอินมันจะถูกนับเป็นสายเป็นเวลา N นาที
att_rule_baseRuleNoSignOffCountEarlyMinuteRemark=เมื่อไม่ได้ลงชื่อออกจะนับเป็นการออกก่อนกำหนดหากไม่ออกจากระบบจะถูกนับเป็นการออกก่อนเวลา N นาที
#节假日
att_holiday_placeholderNo=ขอแนะนำให้เริ่มต้นด้วย H เช่น H01
att_holiday_placeholderName=ขอแนะนำให้ตั้งชื่อด้วย [ปี] + [ชื่อวันหยุด] เช่น [2017 วันแรงงาน]
att_holiday_dayNumber=จำนวนวัน
att_holiday_validDate_msg=วันหยุดในช่วงเวลานี้
#假种
att_leaveType_leaveThing=การลากิจ
att_leaveType_leaveMarriage=ออกจากการแต่งงาน
att_leaveType_leaveBirth=ลาคลอด
att_leaveType_leaveSick=ลาป่วย
att_leaveType_leaveAnnual=ลาประจำปี
att_leaveType_leaveFuneral=การลาหยุดเมื่อสูญเสียคนใกล้ชิด
att_leaveType_leaveHome=ลากลับบ้าน
att_leaveType_leaveNursing=การลาให้นมบุตร
att_leaveType_isDeductWorkLong=ยกเลิกชั่วโมงทำงานหรือไม่
att_leaveType_placeholderNo=ขอแนะนำให้เริ่มต้นด้วย L เช่น L1
att_leaveType_placeholderName=ขอแนะนำให้ลงท้ายด้วย วันหยุด เช่น. วันหยุด
#定时计算
att_timingcalc_timeCalcFrequency=ช่วงการคำนวณ
att_timingcalc_timeCalcInterval=เวลาการคำนวณที่ตั้งเวลา
att_timingcalc_timeSet=การตั้งค่าการคำนวณเวลาที่กำหนด
att_timingcalc_timeSelect=โปรดเลือกเวลา
att_timingcalc_optionTip=ต้องเก็บการคำนวณการเข้างานตามกำหนดเวลาที่ถูกต้องอย่างน้อยหนึ่งรายการ
#自动导出报表
att_autoExport_reportType=ประเภทรายงาน
att_autoExport_fileType=ประเภทไฟล์
att_autoExport_fileName=ชื่อไฟล์
att_autoExport_fileDateFormat=รูปแบบวันที่
att_autoExport_fileTimeFormat=รูปแบบเวลา
att_autoExport_fileContentFormat=รูปแบบเนื้อหา
att_autoExport_fileContentFormatTxt=ตัวอย่าง：{deptName}00{personPin}01{personName}02{attDatetime}03
att_autoExport_timeSendFrequency=ส่งความถี่
att_autoExport_timeSendInterval=ส่งเวลาในช่วง
att_autoExport_emailType=ประเภทตัวรับจดหมาย
att_autoExport_emailRecipients=ตัวรับเมล
att_autoExport_emailAddress=อีเมล
att_autoExport_emailExample=ตัวอย่าง: foxmail.com 123 @ 456 @ foxmail.com
att_autoExport_emailSubject=ชื่ออีเมล
att_autoExport_emailContent=เนื้อหาของเมล
att_autoExport_field=ฟิลด์
att_autoExport_fieldName=ชื่อฟิลด์
att_autoExport_fieldCode=หมายเลขฟิลด์
att_autoExport_reportSet=การตั้งค่ารายงาน
att_autoExport_timeSet=การตั้งค่าเวลาส่งเมล
att_autoExport_emailSet=การตั้งค่าเมล
att_autoExport_emailSetAlert=โปรดป้อนที่อยู่อีเมลของคุณ
att_autoExport_emailTypeSet=การตั้งค่าตัวรับสัญญาณ
att_autoExport_byDay=ตามวัน
att_autoExport_byMonth=ตามเดือน
att_autoExport_byPersonSet=กำหนดโดยพนักงาน
att_autoExport_byDeptSet=กำหนดโดยแผนก
att_autoExport_byAreaSet=ตั้งตามพื้นที่
att_autoExport_emailSubjectSet=การตั้งค่าชื่อ
att_autoExport_emailContentSet=การตั้งค่าเนื้อหา
att_autoExport_timePointAlert=โปรดเลือกจุดส่งเวลาที่ถูกต้อง
att_autoExport_lastDayofMonth=วันสุดท้ายของเดือน
att_autoExport_firstDayofMonth=วันแรกของเดือน
att_autoExport_dayofMonthCheck=วันที่ที่ระบุ
att_autoExport_dayofMonthCheckAlert=โปรดเลือกวันที่ที่ระบุ
att_autoExport_chooseDeptAlert=โปรดเลือกแผนก
att_autoExport_sendFormatSet=การตั้งค่าโหมดส่ง
att_autoExport_sendFormat=โหมดส่ง
att_autoExport_mailFormat=วิธีการส่งกล่องเมล
att_autoExport_ftpFormat=วิธีการส่ง FTP
att_autoExport_sftpFormat=วิธีการส่ง SFTP
att_autoExport_ftpUrl=ที่อยู่เซิร์ฟเวอร์ FTP
att_autoExport_ftpPort=พอร์ตเซิร์ฟเวอร์ FTP
att_autoExport_ftpTimeSet=ตั้งค่าเวลาส่ง FTP
att_autoExport_ftpParamSet=ตั้งค่าพารามิเตอร์ FTP
att_autoExport_ftpUsername=ชื่อผู้ใช้ FTP
att_autoExport_ftpPassword=รหัส FTP
att_autoExport_correctFtpParam=กรุณากรอกพารามิเตอร์ ftp ให้ถูกต้อง
att_autoExport_correctFtpTestParam=โปรดทดสอบการเชื่อมต่อเพื่อให้แน่ใจว่าการสื่อสาร เป็นปกติ
att_autoExport_inputFtpUrl=โปรดป้อนที่อยู่เซิร์ฟเวอร์
att_autoExport_inputFtpPort=โปรดป้อนพอร์ตเซิร์ฟเวอร์
att_autoExport_ftpSuccess=การเชื่อมต่อสำเร็จ
att_autoExport_ftpFail=โปรดตรวจสอบว่าการตั้งค่าพารามิเตอร์ ไม่ถูกต้อง
att_autoExport_validFtp=โปรดป้อนที่อยู่เซิร์ฟเวอร์ที่ถูกต้อง
att_autoExport_validPort=โปรดป้อนพอร์ตเซิร์ฟเวอร์ที่ถูกต้อง
att_autoExport_selectExcelTip=เลือกประเภทไฟล์ EXCEL และรูปแบบเนื้อหาคือทุกฟิลด์
#=====================================================================
#时间段
att_timeSlot_periodType=ประเภทตาราง
att_timeSlot_normalTime=ตารางเวลาปกติ
att_timeSlot_elasticTime=ตารางเวลาที่ยืดหยุ่น
att_timeSlot_startSignInTime=เวลาเริ่มต้นเช็คอิน
att_timeSlot_toWorkTime=เวลาเช็คอิน
att_timeSlot_endSignInTime=เวลาสิ้นสุดของการเช็คอิน
att_timeSlot_allowLateMinutes=อนุญาตให้สาย (นาที)
att_timeSlot_isMustSignIn=ต้องเช็คอิน
att_timeSlot_startSignOffTime=เวลาเริ่มต้นเช็คเอาท์
att_timeSlot_offWorkTime=เวลาเช็คเอาต์
att_timeSlot_endSignOffTime=เวลาสิ้นสุดการเช็คเอาต์
att_timeSlot_allowEarlyMinutes=อนุญาตให้ออกก่อน (นาที)
att_timeSlot_isMustSignOff=ต้องเช็คเอาท์
att_timeSlot_workingHours=เวลาทำงาน (นาที)
att_timeSlot_isSegmentDeduction=เวลาพักหักอัตโนมัติ
att_timeSlot_startSegmentTime=เวลาเริ่มต้น
att_timeSlot_endSegmentTime=เวลาสิ้นสุด
att_timeSlot_interSegmentDeduction=เวลาที่หัก (นาที)
att_timeSlot_markWorkingDays=วันทำงาน
att_timeSlot_isAdvanceCountOvertime=โอทีอัตโนมัติ (เช็คอินก่อนเวลา)
att_timeSlot_signInAdvanceTime=เวลาสิ้นสุดโอทีอัตโนมัติ (เช็คอิน)
att_timeSlot_isPostponeCountOvertime=โอทีอัตโนมัติ(เช็คเอาท์ช้าสุด)
att_timeSlot_signOutPosponeTime=เวลาเริ่มโอทีอัตโนมัติ (เช็คเอาท์)
att_timeSlot_isCountOvertime=คำนวณเป็นค่าทำงานล่วงเวลา
att_timeSlot_timeSlotLong=ชั่วโมงการทำงานต้องตรงตามช่วงเวลาการเข้างานที่กำหนดโดยกฎ:
att_timeSlot_alertStartSignInTime=เวลาเริ่มต้นการเช็คอินต้องน้อยกว่าเวลาเช็คอิน
att_timeSlot_alertEndSignInTime=เวลาสิ้นสุดการเช็คอินต้องมากกว่าเวลาเช็คอิน
att_timeSlot_alertStartSignInAndEndSignIn=เวลาเริ่มต้นเช็คเอาต์ต้องน้อยกว่าเวลาเช็คเอาต์
att_timeSlot_alertStartSignOffTime=เวลาเริ่มต้นการทำงานล่วงเวลาต้องไม่น้อยกว่าเวลาเช็คเอาต์
att_timeSlot_alertEndSignOffTime=เวลาเริ่มการทำงานล่วงเวลาไม่สามารถมากกว่าเวลาสิ้นสุดการเช็คเอาท์
att_timeSlot_alertStartUnequalEnd=วันทำงานต้องไม่น้อยกว่า 0
att_timeSlot_alertStartSegmentTime=เวลาที่ถูกหักไม่สามารถน้อยกว่า 0
att_timeSlot_alertStartAndEndTime=เวลาเริ่มเช็คเอาต์ไม่สามารถเท่ากับเวลาสิ้นสุดการเช็คอิน
att_timeSlot_alertEndAndoffWorkTime=ชั่วโมงต้องไม่มากกว่า 23
att_timeSlot_alertToWorkLesserAndEndTimeLesser=นาทีต้องไม่มากกว่า 59
att_timeSlot_alertLessSignInAdvanceTime=เวลาเช็คอินก่อนเวลาควรน้อยกว่าเวลาเริ่มงาน
att_timeSlot_alertMoreSignInAdvanceTime=จำนวนนาทีทำโอทีก่อนเวลาทำงาน 'ทำงาน' น้อยกว่าจำนวนนาทีก่อนที่จะ 'ทำงาน'
att_timeSlot_alertMoreSignOutPosponeTime='หยุดงาน' จำนวนนาทีของการทำงานล่วงเวลาน้อยกว่า 'หลังเลิกงาน' นาที
att_timeSlot_alertLessSignOutPosponeTime=เวลาเช็คเอาต์ล่าช้าควรมากกว่าเวลาสิ้นสุดการทำงาน
att_timeSlot_time=โปรดป้อนรูปแบบเวลาที่ถูกต้อง
att_timeSlot_alertMarkWorkingDays=วันทำงานต้องไม่ว่างเปล่า
att_timeSlot_placeholderNo=ขอแนะนำให้เริ่มต้นด้วย T เช่น T01
att_timeSlot_placeholderName=ขอแนะนำให้เริ่มต้นด้วย T หรือลงท้ายด้วย schedable
att_timeSlot_beforeToWork=ก่อนทำงาน
att_timeSlot_afterToWork=หลังเลิกงาน
att_timeSlot_beforeOffWork=ก่อนออกงาน
att_timeSlot_afterOffWork=หลังเลิกงาน
att_timeSlot_minutesSignInValid=เช็คอินใช้ได้ภายในไม่กี่นาที
att_timeSlot_toWork=เข้างาน
att_timeSlot_offWork=หยุดงาน
att_timeSlot_minutesSignInAsOvertime=เช็คอินนาทีที่แล้วสำหรับการทำงานล่วงเวลา
att_timeSlot_minutesSignOutAsOvertime=เริ่มนับนาทีล่วงเวลาในภายหลัง
att_timeSlot_minOvertimeMinutes=นาทีการล่วงเวลาขั้นต่ำ
att_timeSlot_enableWorkingHours=เปิดใช้งานชั่วโมงทำงานหรือไม่
att_timeSlot_eidtTimeSlot=แก้ไขเวลา
att_timeSlot_browseBreakTime=ค้นหาช่วงเวลาที่เหลือ
att_timeSlot_addBreakTime=เพิ่มเวลาพัก
att_timeSlot_enableFlexibleWork=เปิดใช้งานที่ยืดหยุ่น
att_timeSlot_advanceWorkMinutes=สามารถไปทำงานก่อนได้
att_timeSlot_delayedWorkMinutes=สามารถเลื่อนการทำงานได้
att_timeSlot_advanceWorkMinutesValidMsg1=จำนวนนาที ก่อนที่จะไปทำงาน มากกว่าจำนวนนาทีที่ สามารถไปทำงานล่วงหน้า
att_timeSlot_advanceWorkMinutesValidMsg2=จำนวนนาทีที่ สามารถไปทำงานล่วงหน้า น้อยกว่าจำนวนนาที ก่อนไปทำงาน
att_timeSlot_advanceWorkMinutesValidMsg3=จำนวนนาทีที่สามารถ ทำงานล่วงหน้า น้อยกว่าหรือเท่ากับจำนวนนาที ก่อนลงชื่อเข้าใช้เพื่อทำงานล่วงเวลา
att_timeSlot_advanceWorkMinutesValidMsg4=จำนวนนาทีที่สามารถ ก่อนลงชื่อเข้าใช้งานล่วงเวลา มากกว่าหรือเท่ากับจำนวนนาที ทำงานล่วงหน้า
att_timeSlot_delayedWorkMinutesValidMsg1=จำนวนนาทีหลังจาก หลังเลิกงาน มากกว่าจำนวนนาทีที่สามารถเลื่อนออกไปทำงานได้
att_timeSlot_delayedWorkMinutesValidMsg2=จำนวนนาทีสำหรับ อาจมาสายในการทำงาน น้อยกว่าจำนวนนาทีหลังจาก หลังเลิกงาน
att_timeSlot_delayedWorkMinutesValidMsg3=จำนวนนาทีที่สามารถกำหนดให้ทำงานได้น้อยกว่าหรือเท่ากับจำนวนนาทีหลังเลิกงาน ที่ออกจากงาน และเริ่มทำงานล่วงเวลา
att_timeSlot_delayedWorkMinutesValidMsg4=จำนวนนาทีที่สามารถทำได้หลังเลิกงานการลงชื่อออกและการเริ่มทำงานล่วงเวลา มากกว่าหรือเท่ากับจำนวนนาทีหลังจาก กำหนดเวลาให้ทำงาน
att_timeSlot_allowLateMinutesValidMsg1=จำนวนนาทีที่อนุญาตให้มาสายน้อยกว่าจำนวนนาทีหลังเลิกงาน
att_timeSlot_allowLateMinutesValidMsg2=จำนวนนาทีหลังจาก หลังเลิกงาน มากกว่าจำนวนนาทีที่อนุญาตให้ นาทีถึงช้า
att_timeSlot_allowEarlyMinutesValidMsg1=อนุญาตนาทีแรก น้อยกว่า นาทีก่อนงาน
att_timeSlot_allowEarlyMinutesValidMsg2=จำนวนนาทีก่อนทำงานมากกว่าจำนวนนาทีที่อนุญาตก่อนเวลา
att_timeSlot_timeOverlap=เวลาที่เหลือทับซ้อนกันโปรดแก้ไขช่วงเวลาที่เหลือ!
att_timeSlot_atLeastOne=พักอย่างน้อย 1 ครั้ง!
att_timeSlot_mostThree=พักได้สูงสุด 3 ช่วง!
att_timeSlot_canNotEqual=เวลาเริ่มต้นของช่วงเวลาพักไม่สามารถเท่ากับเวลาสิ้นสุด!
att_timeSlot_shoudInWorkTime=โปรดตรวจสอบให้แน่ใจว่าช่วงเวลาที่เหลืออยู่ภายในเวลาทำการ!
att_timeSlot_repeatBreakTime=ช่วงเวลาพัก
att_timeSlot_toWorkLe=เวลาทำงานน้อยกว่าเวลาเริ่มต้นขั้นต่ำของช่วงเวลาพักที่เลือก:
att_timeSlot_offWorkGe=ชั่วโมงนอกมากกว่าเวลาสิ้นสุดสูงสุดของช่วงเวลาพักที่เลือก:
att_timeSlot_crossDays_toWork=เวลาเริ่มต้นขั้นต่ำสำหรับรอบระยะเวลาพักอยู่ภายในช่วงเวลา:
att_timeSlot_crossDays_offWork=เวลาสิ้นสุดสูงสุดของช่วงเวลาที่เหลืออยู่ภายในช่วงเวลา:
att_timeSlot_allowLateMinutesRemark=จาก เวลาทำงาน ไปยังการ์ด ที่ได้รับอนุญาตให้สาย เพื่อคำนวณบัตรทำงานปกติ
att_timeSlot_allowEarlyMinutesRemark=เริ่มต้นจาก นอกเวลางาน ในจำนวนนาทีที่ได้รับอนุญาตให้ออกก่อนกำหนดซึ่งเป็นบัตรประจำหน้าที่ปกติ
att_timeSlot_isSegmentDeductionRemark=การลบช่วงเวลาพักในช่วงเวลา
att_timeSlot_attEnableFlexibleWorkRemark1=งานที่มีความยืดหยุ่นไม่ได้รับอนุญาตให้ตั้งค่าจำนวนการมาสายล่าช้าออกก่อนกำหนด
att_timeSlot_afterToWorkRemark=นาที หลังจากเลิกงาน จะเท่ากับนาที รอการทำงาน
att_timeSlot_beforeOffWorkRemark=ก่อนงาน นาทีเท่ากับ สามารถไปทำงานได้ นาที
att_timeSlot_attEnableFlexibleWorkRemark2=จำนวนนาทีหลังจาก หลังเลิกงาน มากกว่าหรือเท่ากับ ชั่วโมงหยุด+ ชั่วโมงทำงานสาย
att_timeSlot_attEnableFlexibleWorkRemark3=นาที คุณสามารถทำงานล่วงหน้า ต้องน้อยกว่าหรือเท่ากับ นาทีการทำงานเพื่อทำงานล่วงเวลา นาที
att_timeSlot_attEnableFlexibleWorkRemark4=เลื่อนการทำงานนาทีต้องน้อยกว่าหรือเท่ากับ N นาทีที่หยุดงาน
att_timeSlot_attBeforeToWorkAsOvertimeRemark=ตัวอย่าง: คลาส 9: 00 ลงชื่อเข้าใช้เพื่อทำงานล่วงเวลา 60 นาทีก่อนทำงานแล้วเช็คอินก่อน 8 โมงเช้าถึง 8 นาฬิกาทำงานล่วงเวลา
att_timeSlot_attAfterOffWorkAsOvertimeRemark=ตัวอย่าง: หลังจาก 18.00 น.หลังเลิกงาน 60 นาทีทำงานให้ลงนามในการถอนและทำงานล่วงเวลาจากนั้นเริ่มทำงานล่วงเวลาจาก 19 โมงเช้าถึงเวลาเช็คเอาต์
att_timeSlot_longTimeValidRemark=เวลาเซ็นชื่อที่มีประสิทธิภาพของ หลังเลิกงาน และเวลาเซ็นชื่อที่มีประสิทธิภาพของ ก่อนทำงาน ไม่สามารถทับซ้อนกันในช่วงเวลา!
att_timeSlot_advanceWorkMinutesValidMsg5=จำนวนนาทีที่ถูกต้องก่อนการเช็คอินจะต้องมากกว่าจำนวนนาทีที่สามารถทำงานได้ล่วงหน้า
att_timeSlot_advanceWorkMinutesValidMsg6=จำนวนนาทีที่คุณสามารถไปทำงานล่วงหน้าได้น้อยกว่าจำนวนนาทีที่ถูกต้องก่อนการเช็คอิน
att_timeSlot_delayedWorkMinutesValidMsg5=จำนวนนาทีที่ถูกต้องหลังการเช็คอินมากกว่าจำนวนนาทีที่สามารถเลื่อนออกไปทำงานได้
att_timeSlot_delayedWorkMinutesValidMsg6=จำนวนนาทีที่สามารถเลื่อนออกไปทำงานได้น้อยกว่าจำนวนนาทีที่ถูกต้องหลังจากลงชื่อเข้าใช้งาน
att_timeSlot_advanceWorkMinutesValidMsg7=เวลาเช็คอินก่อนทำงานไม่สามารถซ้อนทับกับเวลาเช็คเอาต์หลังเลิกงานในวันก่อน
att_timeSlot_delayedWorkMinutesValidMsg7=เวลาเช็คเอาต์หลังเลิกงานไม่สามารถซ้อนทับกับเวลาเช็คอินก่อนทำงานในวันถัดไป
att_timeSlot_maxOvertimeMinutes=จำกัด ชั่วโมงการทำงานล่วงเวลาสูงสุด
#班次
att_shift_basicSet=ตั้งค่าพื้นฐาน
att_shift_advancedSet=ตั้งค่าขั้นสูง
att_shift_type=ประเภทกะทำงาน
att_shift_name=ชื่อกะทำงาน
att_shift_regularShift=กะทำงานปกติ
att_shift_flexibleShift=กะทำงานกำหนดเอง
att_shift_color=สี
att_shift_periodicUnit=หน่วย
att_shift_periodNumber=รอบ
att_shift_startDate=วันที่เริ่ม
att_shift_startDate_firstDay=วันที่เริ่มรอบ
att_shift_isShiftWithinMonth=วงจรกะในหนึ่งเดือน
att_shift_attendanceMode=โหมดการบันทึกเวลา
att_shift_shiftNormal=ลงเวลาตามกะทำงานปกติ
att_shift_oneDayOneCard=ลงเวลาหนึ่งครั้งในหนึ่งวัน
att_shift_onlyBrushTime=คำนวณเฉพาะเวลาเข้างาน
att_shift_notBrushCard=ไม่ลงเวลาทำงาน
att_shift_overtimeMode=โหมดการทำงานล่วงเวลา
att_shift_autoCalc=คอมพิวเตอร์คำนวณอัตโนมัติ
att_shift_mustApply=ต้องใช้การทำงานล่วงเวลาสำหรับ
att_shift_mustOvertime=ต้องทำงานล่วงเวลาหรือขาดงาน
att_shift_timeSmaller=ระยะเวลาที่สั้นลงระหว่างการคำนวณอัตโนมัติและการรับค่าล่วงเวลา
att_shift_notOvertime=ไม่คำนวณว่าเป็นการทำงานล่วงเวลา
att_shift_overtimeSign=ประเภทการทำงานล่วงเวลา
att_shift_normal=วันปกติ
att_shift_restday=วันหยุด
att_shift_timeSlotDetail=รายละเอียดตารางเวลา
att_shift_doubleDeleteTimeSlot=ดับเบิลคลิกที่ระยะเวลาการเปลี่ยน คุณสามารถลบช่วงเวลา
att_shift_addTimeSlot=เพิ่มตารางเวลา
att_shift_cleanTimeSlot=ล้างตารางเวลา
att_shift_NO=ไม่
att_shift_notAll=ไม่เลือกทั้งหมด
att_shift_notTime=หากไม่สามารถกาเครื่องหมายในกล่องกาเครื่องหมายรายละเอียดตารางเวลาได้ แสดงว่ามีการทับซ้อนกันในตารางเวลา
att_shift_notExistTime=ไม่มีตารางเวลานี้
att_shift_cleanAllTimeSlot=คุณแน่ใจว่าจะล้างตารางเวลาสำหรับกะที่เลือกหรือไม่
att_shift_pleaseCheckBox=โปรดเลือกช่องทำเครื่องหมายที่ด้านซ้ายซึ่งเป็นเวลาเดียวกับเวลาที่แสดงผลปัจจุบันทางด้านขวา
att_shift_pleaseUnit=กรุณากรอกหน่วยของรอบและจำนวนรอบ
att_shift_pleaseAllDetailTimeSlot=โปรดเลือกรายละเอียดตารางเวลา
att_shift_placeholderNo=ขอแนะนำให้เริ่มต้นด้วย S เช่น S0
att_shift_placeholderName=ขอแนะนำให้เริ่มต้นด้วย S หรือลงท้ายด้วยชื่อกะ
att_shift_workType=ประเภทการทำงาน
att_shift_normalWork=งานปกติ
att_shift_holidayOt=โอทีวันหยุด
att_shift_attShiftStartDateRemark=ตัวอย่าง: วันที่เริ่มต้นของรอบคือหมายเลข 22 มีระยะเวลาสามวันจากนั้นหมายเลข 22/23/24 อยู่ใน Class A / B / C และเลขที่ 19/20/21 อยู่ใน Class A. / B คลาส / คลาส C ก่อนและหลังวันที่และอื่น ๆ
att_shift_isShiftWithinMonthRemark1=เปลี่ยนภายในเดือน, รอบเท่านั้นถึงวันสุดท้ายของแต่ละเดือน, ไม่กำหนดเวลาติดต่อกันตลอดทั้งเดือน;
att_shift_isShiftWithinMonthRemark2=ไม่ใช่การเปลี่ยนรอบในหนึ่งเดือน รอบจะวนเป็นวันสุดท้ายของแต่ละเดือน หากรอบหนึ่งไม่สิ้นสุด ให้ไปเดือนถัดไป เป็นต้น
att_shift_workTypeRemark1=หมายเหตุ: เมื่อเลือกประเภทงานเป็นค่าล่วงเวลาในวันพักการเข้างานจะไม่ถูกคำนวณในวันที่เป็นวันหยุด
att_shift_workTypeRemark2=OT วันหยุดสุดสัปดาห์ ค่าล่วงเวลาเป็นค่าเริ่มต้นเป็นวันพัก และคอมพิวเตอร์จะคำนวณค่าล่วงเวลาโดยอัตโนมัติ ไม่จำเป็นต้องมีการทำงานล่วงเวลา ชั่วโมงทำงานของวันจะถูกบันทึกเป็นชั่วโมงทำงานล่วงเวลา และไม่นับการเข้างานในช่วงวันหยุด
att_shift_workTypeRemark3=OT วันหยุดค่าล่วงเวลาเป็นค่าเริ่มต้นในวันหยุด และคอมพิวเตอร์จะคำนวณค่าล่วงเวลาโดยอัตโนมัติ ไม่จำเป็นต้องมีการทำงานล่วงเวลา และเวลาทำงานของวันจะถูกบันทึกเป็นชั่วโมงการทำงานล่วงเวลา
att_shift_attendanceModeRemark1=ยกเว้นการเลื่อนตามกะโดยปกติ ไม่ถือว่าเป็นการล่วงเวลาเร็วหรือล่าช้า ตัวอย่างเช่น
att_shift_attendanceModeRemark2=1 ไม่จำเป็นต้องเช็คอินหรือใช้บัตรที่ถูกต้องวันละครั้ง ไม่นับการทำงานล่วงเวลา
att_shift_attendanceModeRemark3=2. ประเภทของงาน: งานปกติ, โหมดการเข้างาน: รูดบัตรฟรีแล้วเวลาเปลี่ยนวันถือเป็นเวลาทำงานที่มีประสิทธิภาพ;
att_shift_periodStartMode=ประเภทเริ้มต้นของช่วงเวลา
att_shift_periodStartModeByPeriod=วันที่เริ่มรอบช่วงเวลา
att_shift_periodStartModeBySch=วันเริ้มกะ
att_shift_addTimeSlotToShift=เพิ่มตารางเวลาของกะนี้หรือไม่
#=====================================================================
#分组
att_group_editGroup=แก้ไขพนักงานสำหรับกลุ่ม
att_group_browseGroupPerson=เรียกดูกลุ่มของพนักงาน
att_group_list=รายชื่อกลุ่ม
att_group_placeholderNo=ขอแนะนำให้เริ่มต้นด้วย G เช่น G1
att_group_placeholderName=ขอแนะนำให้เริ่มต้นด้วย G หรือลงท้ายด้วย กลุ่ม
att_widget_deptHint=หมายเหตุ: นำเข้าพนักงานทั้งหมดภายใต้แผนกที่เลือก
att_widget_searchType=ดึงข้อมูลแบบมีเงื่อนไข
att_widget_noPerson=ไม่ได้เลือกใคร
#分组排班
#部门排班
att_deptSch_existsDept=มีกะการทำงานในแผนกและไม่อนุญาตให้ลบแผนก
#人员排班
att_personSch_view=ดูตารางเวลาพนักงาน
#临时排班
att_schedule_type=ประเภทตารางการทำงาน
att_schedule_tempType=ชนิดชั่วคราว
att_schedule_normal=กำหนดการปกติ
att_schedule_intelligent=ตารางอัฉริยะ
att_tempSch_scheduleType=ประเภทตารางการทำงาน
att_tempSch_startDate=วันที่เริ่ม
att_tempSch_endDate=วันที่สิ้นสุด
att_tempSch_attendanceMode=โหมดการบันทึกเวลา
att_tempSch_overtimeMode=โหมดการทำงานล่วงเวลา
att_tempSch_overtimeRemark=เครื่องหมายการทำงานล่วงเวลา
att_tempSch_existsDept=มีกะชั่วคราวในแผนกและไม่ได้รับอนุญาตให้ลบแผนก
att_schedult_opAddTempSch=กะการทำงานชั่วคราวใหม่
att_schedule_cleanEndDate=เวลาสิ้นสุดว่างเปล่า
att_schedule_selectOne=ตารางปกติเลือกได้เพียงกะเดียวเท่านั้น!
att_schedule_selectPerson=โปรดเลือกพนักงานก่อน
att_schedule_selectDept=โปรดเลือกแผนกก่อน!
att_schedule_selectGroup=โปรดเลือกกลุ่มก่อน!
att_schedule_selectOneGroup=สามารถเลือกได้เพียงกลุ่มเดียว!
att_schedule_arrange=โปรดเลือกกะ!
att_schedule_leave=ลา
att_schedule_trip=เดินทาง
att_schedule_out=ออก
att_schedule_off=หยุด
att_schedule_makeUpClass=เพิ่มเติม
att_schedule_class=ปรับ
att_schedule_holiday=วันหยุด
att_schedule_offDetail=ปรับวันหยุด
att_schedule_makeUpClassDetail=บันทึกเวลาเพิ่มเติม
att_schedule_classDetail=ปรับกะการทำงาน
att_schedule_holidayDetail=วันหยุด
att_schedule_noSchDetail=ไม่ได้กำหนดตารางเวลา
att_schedule_normalDetail=ปกติ
att_schedule_normalSchInfo=กะการทำงานกลาง: ไม่มีการข้ามวัน
att_schedule_multipleInterSchInfo=กะการทำงานคั่นด้วยเครื่องหมายจุลภาค: การเลื่อนข้ามวันหลายรายการ
att_schedule_inderSchFirstDayInfo=กะการทำงานแบบข้ามวันหยุดไปข้างหลัง: การข้ามวันจะถูกบันทึกเป็นวันแรก
att_schedule_inderSchSecondDayInfo=กะการทำงานแบบข้ามวันหยุดไปข้างหน้า: การข้ามวันจะถูกบันทึกเป็นวันที่สอง
att_schedule_timeConflict=ขัดแย้งกับช่วงเวลากะที่มีอยู่ไม่อนุญาตให้บันทึก!
#=====================================================================
att_excp_notExisetPerson=ไม่มีพนักงาน
att_excp_leavePerson=พนักงานลาออก
#补签单
att_sign_signTime=บันทึกเวลา
att_sign_signDate=บันทึกวัน
#请假
att_leave_arilName=ประเภทการลา
att_leave_image=รูปภาพการลา
att_leave_imageShow=ไม่มีรูปภาพ
att_leave_imageType=คำแนะนำที่ไม่ถูกต้อง: รูปแบบรูปภาพไม่ถูกต้องรองรับรูปแบบ: JPEG, GIF, PNG!
att_leave_imageSize=คำแนะนำที่ไม่ถูกต้อง: รูปภาพที่เลือกมีขนาดใหญ่เกินไปขนาดสูงสุดของรูปภาพคือ 4MB!
att_leave_leaveLongDay=ระยะเวลาลา (วัน)
att_leave_leaveLongHour=ระยะเวลาลา (ชั่วโมง)
att_leave_leaveLongMinute=ระยะเวลาลา (นาที)
att_leave_endNoLessAndEqualStart=เวลาสิ้นสุดต้องไม่น้อยกว่าหรือเท่ากับเวลาเริ่มต้น
att_leave_typeNameNoExsists=ไม่มีชื่อวันหยุด
att_leave_startNotNull=เวลาเริ่มต้นต้องไม่ว่างเปล่า
att_leave_endNotNull=เวลาสิ้นสุดต้องไม่ว่างเปล่า
att_leave_typeNameConflict=ชื่อวันหยุดขัดแย้งกับชื่อสถานะการบันทึกเวลาทำงาน
#出差
att_trip_tripLongDay=ระยะเวลาเดินทาง (วัน)
att_trip_tripLongMinute=ระยะเวลาของการเดินทาง (นาที)
att_trip_tripLongHour=ระยะเวลาเดินทาง (ชั่วโมง)
#外出
att_out_outLongDay=ระยะเวลาในการออก (วัน)
att_out_outLongMinute=ระยะเวลาในการออก (นาที)
att_out_outLongHour=ระยะเวลาในการออก (เวลา)
#加班
att_overtime_type=ประเภทโอที
att_overtime_normal=โอทีปกติ
att_overtime_rest=โอทีสุดสัปดาห์
att_overtime_overtimeLong=ความยาวของการทำงานล่วงเวลา (นาที)
att_overtime_overtimeHour=ชั่วโมงการทำงานล่วงเวลา (ชั่วโมง)
att_overtime_notice=ไม่อนุญาตให้ใช้เวลาเกินกว่าหนึ่งวัน
att_overtime_minutesNotice=เวลาสมัครงานล่วงเวลาต้องไม่น้อยกว่าเวลาทำงานล่วงเวลาขั้นต่ำ!
#调休补班
att_adjust_type=ประเภทการเปลี่ยน
att_adjust_adjustDate=วันที่เปลี่ยน
att_adjust_shiftName=ชื่อกะ
att_adjust_selectClass=โปรดเลือกชื่อกะที่ต้องการบันทึกเวลา
att_shift_notExistShiftWorkDate={1} การกำหนดตารางเวลากะที่เปลี่ยน {0} และส่วนที่เหลือไม่ได้รับอนุญาตให้ใช้กะการทำงาน
att_adjust_shiftPeriodStartMode=กะที่เลือกไว้สำหรับกะที่เปลี่ยน ถ้าวันที่เริ่มต้นเป็นกะ ค่าเริ่มต้นคือ0
att_adjust_shiftNameNoNull=ต้องระบุกะการทำงาน จะเป็นค่าว่างไม่ได้
att_adjust_shiftNameNoExsist=ไม่มีกะการทำงาน
#调班
att_class_type=ประเภทวการเปลี่ยน
att_class_sameTimeMoveShift=ปรับกะส่วนตัวในวันเดียวกัน
att_class_differenceTimeMoveShift=ปรับกะส่วนตัวในวันอื่น ๆ
att_class_twoPeopleMove=การแลกเปลี่ยนสองคน
att_class_moveDate=วันที่เปลี่ยน
att_class_shiftName=ชื่อกะที่เปลี่ยน
att_class_moveShiftName=การเปลี่ยนแปลงที่ปรับใหม่ไม่สามารถว่างเปล่าได้
att_class_movePersonPin=รหัสพนักงานที่เปลี่ยน
att_class_movePersonName=ชื่อพนักงานที่เปลี่ยน
att_class_movePersonLastName=นามสกุลพนักงานที่เปลี่ยน
att_class_moveDeptName=ชื่อแผนกที่เปลี่ยน
att_class_personPin=รหัสพนักงาน
att_class_shiftNameNoNull=การเปลี่ยนกะการทำงานใหม่ไม่สามารถว่างเปล่า
att_class_personPinNoNull=รหัสพนักงานใหม่ต้องไม่ว่างเปล่า!
att_class_isNotExisetSwapPersonPin=ไม่มีคนเปลี่ยนใหม่โปรดเพิ่มอีกครั้ง!
att_class_personNoSame=คุณไม่สามารถปรับเปลี่ยนคนเดียวกันได้โปรดลองอีกครั้ง
att_class_outTime=วันที่ปรับและวันที่โอนไม่เกินหนึ่งเดือน！
att_class_shiftNameNoExsist=ไม่มีการเปลี่ยนกะ
att_class_swapPersonNoExisist=ไม่มีพนักงานที่ปรับเปลี่ยน
att_class_dateNoSame=พนักงานจะถูกโอนในวันที่แตกต่างกันวันที่ไม่สามารถเหมือนกัน
#=====================================================================
#节点
att_node_name=โหนด
att_node_type=ประเภทโหนด
att_node_leader=โหนดโดยตรง
att_node_leaderNode=โหนดผู้นำโดยตรง
att_node_person=พนักงานที่กำหนด
att_node_position=กำหนดตำแหน่ง
att_node_choose=เลือกตำแหน่ง
att_node_personNoNull=พนักงานต้องไม่ว่างเปล่า
att_node_posiitonNoNull=ตำแหน่งต้องไม่ว่างเปล่า
att_node_placeholderNo=ขอแนะนำให้เริ่มต้นด้วย N เช่น N01
att_node_placeholderName=ขอแนะนำให้เริ่มต้นด้วยตำแหน่งหรือชื่อที่ลงท้ายด้วยโหนดเช่นโหนดผู้จัดการ
att_node_searchPerson=เกณฑ์การค้นหา
att_node_positionIsExist=ตำแหน่งที่มีอยู่แล้วในข้อมูลโหนดกรุณาเลือกตำแหน่งอีกครั้ง
#流程
att_flow_type=ประเภทโฟลว์
att_flow_rule=กฎโฟลว์
att_flow_rule0=น้อยกว่าหรือเท่ากับ 1 วัน
att_flow_rule1=มากกว่า 1 วันและน้อยกว่าหรือเท่ากับ 3 วัน
att_flow_rule2=มากกว่า 3 วันและน้อยกว่าหรือเท่ากับ 7 วัน
att_flow_rule3=มากกว่า 7 วัน
att_flow_node=โหนดการอนุมัติ
att_flow_start=เริ่มกระบวนการ
att_flow_end=สิ้นสุดกระบวนการ
att_flow_addNode=เพิ่มโหนด
att_flow_placeholderNo=ขอแนะนำให้เริ่มต้นด้วย F เช่น F01
att_flow_placeholderName=ขอแนะนำให้เริ่มต้นด้วยชนิดลงท้ายด้วย flow เช่นออกจาก Flow
att_flow_tips=หมายเหตุ: ลำดับการอนุมัติของโหนดมาจากบนลงล่างและคุณสามารถลากการเรียงลำดับหลังจากเลือก
#申请
att_apply_personPin=รหัสพนักงาน
att_apply_type=ประเภทข้อยกเว้น
att_apply_flowStatus=สถานะโฟลว์
att_apply_start=การเริ่มต้นแอปพลิเคชัน
att_apply_flowing=รอดำเนินการ
att_apply_pass=ผ่าน
att_apply_over=สิ้นสุด
att_apply_refuse=ปฏิเสธ
att_apply_revoke=ยกเลิก
att_apply_except=ข้อยกเว้น
att_apply_view=ดูรายละเอียด
att_apply_leaveTips=พนักงานมีคำขอลาระหว่างช่วงเวลานี้!
att_apply_tripTips=พนักงานมีคำขอลาสำหรับการเดินทางเพื่อธุรกิจในช่วงเวลานี้!
att_apply_outTips=พนักงานได้สมัครเพื่อใช้งานในช่วงเวลานี้!
att_apply_overtimeTips=พนักงานมีคำขอทำงานล่วงเวลาในช่วงเวลานี้!
att_apply_adjustTips=ในช่วงเวลานี้พนักงานสามารถสมัครเพื่ออบรมได้!
att_apply_classTips=บุคลากรมีแอปพลิเคชัน ในช่วงเวลานี้!
#审批
att_approve_wait=รอการอนุมัติ
att_approve_refuse=ไม่ผ่าน
att_approve_reason=เหตุผล
att_approve_personPin=รหัสผู้อนุมัติ
att_approve_personName=ชื่อผู้อนุมัติ
att_approve_person=อนุมัติ
att_approve_isPass=จะอนุมัติหรือไม่
att_approve_status=สถานะโหนดปัจจุบัน
att_approve_tips=จุดเวลามีอยู่ในโฟลว์และไม่สามารถทำซ้ำได้
att_approve_tips2=โหนดของโฟลว์ไม่ได้รับการกำหนดค่ากรุณาติดต่อผู้ดูแลระบบเพื่อกำหนดค่า
att_approve_offDayConflicts={0} ไม่ได้ถูกกำหนดเวลาหรือกำหนดไว้ในวันที่ {1} และส่วนที่เหลือไม่ได้รับอนุญาต
att_approve_shiftConflicts={0} มีการเปลี่ยนแปลงใน {1} อยู่แล้วและไม่อนุญาตให้มีการเปลี่ยนแปลงในวันทำงาน!
att_approve_shiftNoSch={0} ไม่อนุญาตให้ใช้แอพพลิเคชั่น {1} เมื่อไม่ได้กำหนดกะไว้ใน !
att_approve_classConflicts=ไม่อนุญาตให้มีการเปลี่ยนกะในวันที่ไม่ได้กำหนดไว้
att_approve_selectTime=เวลาที่เลือกจะกำหนดกระบวนการตามกฎ
att_approve_withoutPermissionApproval=มีขั้นตอนการทำงานโดยไม่ได้รับอนุญาตให้อนุมัติโปรดตรวจสอบ!
#=====================================================================
#考勤计算
att_op_calculation=การคำนวณการบันทึกเวลา
att_op_calculation_notice=ข้อมูลการบันทึกเวลาถูกคำนวณในพื้นหลังโปรดลองอีกครั้งในภายหลัง
att_op_calculation_leave=รวมถึงพนักงานที่ลาออกด้วย
att_statistical_choosePersonOrDept=โปรดเลือกแผนกหรือพนักงาน
att_statistical_sureCalculation=คุณแน่ใจหรือไม่ว่าต้องการดำเนินการคำนวณการบันทึกเวลา
att_statistical_filter=เงื่อนไขการกรองพร้อม
att_statistical_initData=การเริ่มต้นของฐานข้อมูลเสร็จสมบูรณ์
att_statistical_exception=การเริ่มต้นของข้อมูลข้อยกเว้นเสร็จสมบูรณ์
att_statistical_error=การคำนวณขาดหายไปล้มเหลว
att_statistical_begin=เริ่มคำนวณ
att_statistical_end=สิ้นสุดการคำนวณ!
att_statistical_noticeTime=ช่วงเวลาตัวเลือกการบันทึกเวลา: สองเดือนแรกถึงวัน!
att_statistical_remarkHoliday=H
att_statistical_remarkClass=C
att_statistical_remarkNoSch=NS
att_statistical_remarkRest=R
#原始记录表
att_op_importAccRecord=นำเข้าบันทึกการควบคุมประตู
att_op_importParkRecord=นำเข้าบันทึกที่จอดรถ
att_op_importInsRecord=นำเข้าบันทึก faceKiosk
att_op_importPidRecord=นำเข้าบันทึกใบรับรองพนักงาน
att_op_importVmsRecord=นำเข้าบันทึกวิดีโอ
att_op_importUSBRecord=นำเข้าบันทึก U Disk 
att_transaction_noAccModule=ไม่มีโมดูลการควบคุมประตู
att_transaction_noParkModule=ไม่มีโมดูลที่จอดรถ!
att_transaction_noInsModule=ไม่มีโมดูล faceKiosk!
att_transaction_noPidModule=ไม่มีโมดูลใบรับรองพนักงาน!
att_transaction_exportRecord=ส่งออกบันทึกดั้งเดิม
att_transaction_exportAttPhoto=ส่งออกรูปภาพการบันทึกเวลา
att_transaction_fileIsTooLarge=ไฟล์ที่ส่งออกมีขนาดใหญ่เกินไปโปรด จำกัด ช่วงวันที่ให้แคบลง
att_transaction_exportDate=วันที่ส่งออก
att_statistical_attDatetime=เวลาบันทึก
att_statistical_attPhoto=ภาพบันทึกเวลา
att_statistical_attDetail=รายละเอียดการบันทึกเวลา
att_statistical_acc=อุปกรณ์ควบคุมการควบคุมประตู
att_statistical_att=อุปกรณ์บันทึกเวลา
att_statistical_park=กล้อง LPR
att_statistical_faceRecognition=อุปกรณ์จดจำใบหน้า
att_statistical_app=อุปกรณ์มือถือ
att_statistical_vms=อุปกรณ์วิดีโอ
att_statistical_psg=อุปกรณ์ช่องสัญญาณ
att_statistical_dataSources=แหล่งข้อมูล
att_transaction_SyncRecord=ซิงโครไนซ์บันทึกการบันทึกเวลา
#日打卡详情表
att_statistical_dayCardDetail=รายละเอียดการเช็คอิน
att_statistical_cardDate=วันที่บันทึก
att_statistical_cardNumber=จำนวนบันทึก
att_statistical_earliestTime=เวลาแรกสุด
att_statistical_latestTime=เวลาหลังสุด
att_statistical_cardTime=ลงเวลา
#请假汇总表
att_statistical_leaveDetail=รายละเอียดการลา
#日明细报表
att_statistical_attDate=วันที่บันทึกเวลา
att_statistical_week=สัปดาห์
att_statistical_shiftInfo=ข้อมูลกะการทำงาน
att_statistical_shiftTimeData=เวลาทำงาน
att_statistical_cardValidData=ลงเวลางาน
att_statistical_cardValidCount=จำนวนลงเวลา
att_statistical_lateCount=จำนวนหลังสุด
att_statistical_lateMinute=นาทีหลังสุด
att_statistical_earlyCount=จำนวนแรกสุด
att_statistical_earlyMinute=นาทีแรกสุด
att_statistical_countData=จำนวนข้อมูล
att_statistical_minuteData=ข้อมูลนาที
att_statistical_attendance_minute=การบันทึกเวลา (นาที)
att_statistical_overtime_minute=การทำงานล่วงเวลา (นาที)
att_statistical_unusual_minute=ข้อยกเว้น (นาที)
#月明细报表
att_monthdetail_should_hour=ควร (เวลา)
att_monthdetail_actual_hour=จริง (เวลา)
att_monthdetail_valid_hour=ถูกต้อง (เวลา)
att_monthdetail_absent_hour=เสร็จสิ้น (เวลา)
att_monthdetail_leave_hour=ออก (เวลา)
att_monthdetail_trip_hour=การเดินทางเพื่อธุรกิจ (รายชั่วโมง)
att_monthdetail_out_hour=ออกไป (รายชั่วโมง)
att_monthdetail_should_day=ควร (วัน)
att_monthdetail_actual_day=จริง (วัน)
att_monthdetail_valid_day=มีผลบังคับใช้ (วัน)
att_monthdetail_absent_day=เสร็จสิ้น (วัน)
att_monthdetail_leave_day=ออก (วัน)
att_monthdetail_trip_day=การเดินทางเพื่อธุรกิจ (วัน)
att_monthdetail_out_day=ออกไป (วัน)
#月统计报表
att_statistical_late_minute=ระยะเวลา (นาที)
att_statistical_early_minute=ระยะเวลา (นาที)
#部门统计报表
#年度统计报表
att_statistical_should=ควร
att_statistical_actual=จริง
att_statistical_valid=ที่ถูกต้อง
att_statistical_numberOfTimes=จำนวน
att_statistical_usually=วันธรรมดา
att_statistical_rest=วันหยุดสุดสัปดาห์
att_statistical_holiday=วันหยุด
att_statistical_total=รวม
att_statistical_month=สถิติของเดือน
att_statistical_year=สถิติแห่งปี
att_statistical_attendance_hour=บันทึกเวลา (ชั่วโมง)
att_statistical_attendance_day=บันทึกเวลา (วัน)
att_statistical_overtime_hour=การทำงานล่วงเวลา (ชั่วโมง)
att_statistical_unusual_hour=ข้อยกเว้น (ชั่วโมง)
att_statistical_unusual_day=ผิดปกติ (วัน)
#考勤设备参数
att_deviceOption_query=ดูพารามิเตอร์อุปกรณ์
att_deviceOption_noOption=ไม่มีข้อมูลพารามิเตอร์โปรดดึงพารามิเตอร์อุปกรณ์ก่อน
att_deviceOption_name=ชื่อพารามิเตอร์
att_deviceOption_value=ค่าพารามิเตอร์
att_deviceOption_UserCount=ปัจจุบัน
att_deviceOption_MaxUserCount=จำนวนผู้ใช้สูงสุด
att_deviceOption_FaceCount=จำนวนเทมเพลตใบหน้าปัจจุบัน
att_deviceOption_MaxFaceCount=จำนวนเทมเพลตใบหน้าสูงสุด
att_deviceOption_FacePhotoCount=จำนวนภาพใบหน้าปัจจุบัน
att_deviceOption_MaxFacePhotoCount=จำนวนภาพใบหน้าสูงสุด
att_deviceOption_FingerCount=จำนวนเทมเพลตลายนิ้วมือปัจจุบัน
att_deviceOption_MaxFingerCount=จำนวนแม่แบบลายนิ้วมือสูงสุด
att_deviceOption_FingerPhotoCount=จำนวนภาพลายนิ้วมือในปัจจุบัน
att_deviceOption_MaxFingerPhotoCount=จำนวนภาพลายนิ้วมือสูงสุด
att_deviceOption_FvCount=จำนวนเทมเพลตหลอดเลือดดำนิ้วปัจจุบัน
att_deviceOption_MaxFvCount=จำนวนสูงสุดของแม่แบบเส้นเลือดดำ
att_deviceOption_FvPhotoCount=จำนวนภาพเส้นเลือดดำปัจจุบัน
att_deviceOption_MaxFvPhotoCount=จำนวนภาพเส้นเลือดดำ
att_deviceOption_PvCount=จำนวนเทมเพลตฝ่ามือปัจจุบัน
att_deviceOption_MaxPvCount=จำนวนเทมเพลตปาล์มสูงสุด
att_deviceOption_PvPhotoCount=รูปภาพฝ่ามือปัจจุบัน
att_deviceOption_MaxPvPhotoCount=จำนวนรูปภาพฝ่ามือสูงสุด
att_deviceOption_TransactionCount=จำนวนบันทึกเวลาปัจจุบัน
att_deviceOption_MaxAttLogCount=จำนวนบันทึกเวลาสูงสุด
att_deviceOption_UserPhotoCount=รูปถ่ายของผู้ใช้ปัจจุบัน
att_deviceOption_MaxUserPhotoCount=จำนวนรูปภาพผู้ใช้สูงสุด
att_deviceOption_FaceVersion=เวอร์ชั่นอัลกอริธึมการจดจำใบหน้า
att_deviceOption_FPVersion=เวอร์ชั่นอัลกอริธึมจดจำลายนิ้วมือ
att_deviceOption_FvVersion=เวอร์ชั่นอัลกอริธึมการจดจำเส้นเลือดดำ
att_deviceOption_PvVersion=เวอร์ชั่นอัลกอริทึมการจดจำฝ่ามือ
att_deviceOption_FWVersion=เวอร์ชันของเฟิร์มแวร์
att_deviceOption_PushVersion=เวอร์ชั่นพุช
#=====================================================================
#API
att_api_areaCodeNotNull=หมายเลขพื้นที่ต้องไม่ว่างเปล่า
att_api_pinsNotNull=ข้อมูล PIN ไม่ได้รับอนุญาตให้ว่างเปล่า
att_api_pinsOverSize=ความยาวข้อมูล PIN ไม่เกิน 500
att_api_areaNoExist=ไม่มีพื้นที่
att_api_sign=รายงานบันทึกเพิ่มเติม
#=====================================================================
#休息时间段
att_leftMenu_breakTime=เวลาพัก
att_breakTime_startTime=เวลาเริ่มต้น
att_breakTime_endTime=เวลาสิ้นสุด
#=====================================================================
#导入U盘记录
att_import_uploadFileSuccess=อัปโหลดไฟล์สำเร็จเริ่มวิเคราะห์ข้อมูลไฟล์โปรดรอ ...
att_import_resolutionComplete=หลังจากการวิเคราะห์เสร็จสมบูรณ์ให้เริ่มอัปเดตฐานข้อมูล
att_import_snNoExist=อุปกรณ์การบันทึกเวลาไม่สอดคล้องกับไฟล์ที่นำเข้า โปรดเลือกไฟล์อีกครั้ง
att_import_fileName_msg=ข้อกำหนดรูปแบบชื่อไฟล์ที่นำเข้า: หมายเลขซีเรียลของอุปกรณ์เริ่มต้นด้วยและถูกคั่นด้วยเครื่องหมายขีดล่าง "_" เช่น： "3517171600001_attlog.dat" 。
att_import_notSupportFormat=รูปแบบนี้ไม่ได้รับการสนับสนุนในขณะนี้!
att_import_selectCorrectFile=โปรดเลือกไฟล์รูปแบบที่ถูกต้อง!
att_import_fileFormat=รูปแบบไฟล์
att_import_targetFile=ไฟล์เป้าหมาย
att_import_startRow=จำนวนแถวที่จุดเริ่มต้นของส่วนหัว
att_import_startRowNote=บรรทัดแรกของรูปแบบข้อมูลคือการนำเข้าข้อมูลโปรดตรวจสอบไฟล์ก่อนนำเข้า
att_import_delimiter=ตัวคั่น
#=====================================================================
#设备操作日志
att_device_op_log_op_type=รหัสการทำงาน
att_device_op_log_dev_sn=หมายเลขซีเรียล
att_device_op_log_op_content=เนื้อหาการทำงาน
att_device_op_log_operator_pin=หมายเลขการทำงาน
att_device_op_log_operator_name=ชื่อการทำงาน
att_device_op_log_op_time=เวลาทำงาน
att_device_op_log_op_who_value=ค่าการทำงาน
att_device_op_log_op_who_content=คำอธิบายการำทงาน
att_device_op_log_op_value1=วัตถุการดำเนินงาน 2
att_device_op_log_op_value_content1=คำอธิบายวัตถุการดำเนินงาน 2
att_device_op_log_op_value2=วัตถุการดำเนินงาน 3
att_device_op_log_op_value_content2=คำอธิบายวัตถุการดำเนินงาน 3
att_device_op_log_op_value3=วัตถุการดำเนินงาน 4
att_device_op_log_op_value_content3=คำอธิบายวัตถุการดำเนินงาน 4
#操作日志的操作类型
att_device_op_log_opType_0=เปิดเครื่อง
att_device_op_log_opType_1=ปิดเครื่อง
att_device_op_log_opType_2=การตรวจสอบล้มเหลว
att_device_op_log_opType_3=แจ้งเตือน
att_device_op_log_opType_4=เข้าสู่เมนู
att_device_op_log_opType_5=เปลี่ยนการตั้งค่า
att_device_op_log_opType_6=ลงทะเบียนลายนิ้วมือ
att_device_op_log_opType_7=ลงทะเบียนรหัสผ่าน
att_device_op_log_opType_8=ลงทะเบียนบัตร HID
att_device_op_log_opType_9=ลบผู้ใช้
att_device_op_log_opType_10=ลบลายนิ้วมือ
att_device_op_log_opType_11=ลบรหัสผ่าน
att_device_op_log_opType_12=ลบการ์ด RF
att_device_op_log_opType_13=ล้างข้อมูล
att_device_op_log_opType_14=สร้างการ์ด MF
att_device_op_log_opType_15=ลงทะเบียนการ์ด MF
att_device_op_log_opType_16=ลงทะเบียนการ์ด MF
att_device_op_log_opType_17=ลบการลงทะเบียนการ์ด MF
att_device_op_log_opType_18=ล้างเนื้อหาการ์ด MF
att_device_op_log_opType_19=ย้ายข้อมูลการลงทะเบียนไปยังการ์ด
att_device_op_log_opType_20=คัดลอกข้อมูลจากการ์ดไปยังเครื่อง
att_device_op_log_opType_21=ตั้งเวลา
att_device_op_log_opType_22=การตั้งค่าจากโรงงาน
att_device_op_log_opType_23=ลบรายการและออกจากบันทึก
att_device_op_log_opType_24=ล้างสิทธิ์ของผู้ดูแลระบบ
att_device_op_log_opType_25=แก้ไขการตั้งค่ากลุ่มควบคุมการควบคุมประตู
att_device_op_log_opType_26=แก้ไขการตั้งค่าการควบคุมประตูของผู้ใช้
att_device_op_log_opType_27=ปรับเปลี่ยนช่วงเวลาการควบคุมประตู
att_device_op_log_opType_28=ปรับเปลี่ยนการตั้งค่าการปลดล็อก
att_device_op_log_opType_29=ปลดล็อค
att_device_op_log_opType_30=ลงทะเบียนผู้ใช้ใหม่
att_device_op_log_opType_31=เปลี่ยนคุณสมบัติของลายนิ้วมือ
att_device_op_log_opType_32=สัญญาณเตือนการบังขับ
att_device_op_log_opType_34=Anti-Passback
att_device_op_log_opType_35=ลบรูปภาพการบันทึกเวลา
att_device_op_log_opType_36=แก้ไขข้อมูลผู้ใช้
att_device_op_log_opType_37=วันหยุด
att_device_op_log_opType_38=กู้คืนข้อมูล
att_device_op_log_opType_39=ข้อมูลสำรอง
att_device_op_log_opType_40=การอัปโหลด U Disk
att_device_op_log_opType_41=ดาวน์โหลด U Disk
att_device_op_log_opType_42=การเข้ารหัสบันทึกเวลาของ U Disk
att_device_op_log_opType_43=ลบบันทึกหลังจากดาวน์โหลดดิสก์ USB สำเร็จ
att_device_op_log_opType_53=สวิตช์ขาออก
att_device_op_log_opType_54=เซ็นเซอร์ประตู
att_device_op_log_opType_55=แจ้งเตือน
att_device_op_log_opType_56=กู้คืนพารามิเตอร์
att_device_op_log_opType_68=รูปถ่ายของผู้ใช้ที่ลงทะเบียน
att_device_op_log_opType_69=แก้ไขรูปภาพของผู้ใช้
att_device_op_log_opType_70=แก้ไขชื่อผู้ใช้
att_device_op_log_opType_71=แก้ไขสิทธิ์ผู้ใช้
att_device_op_log_opType_76=แก้ไขการตั้งค่าเครือข่ายไอพี
att_device_op_log_opType_77=แก้ไขรูปแบบการตั้งค่าเครือข่าย
att_device_op_log_opType_78=แก้ไขเกตเวย์การตั้งค่าเครือข่าย
att_device_op_log_opType_79=แก้ไข DNS การตั้งค่าเครือข่าย
att_device_op_log_opType_80=แก้ไขรหัสผ่านการตั้งค่าการเชื่อมต่อ
att_device_op_log_opType_81=ปรับเปลี่ยน ID อุปกรณ์การตั้งค่าการเชื่อมต่อ
att_device_op_log_opType_82=แก้ไขที่อยู่คลาวด์เซิร์ฟเวอร์
att_device_op_log_opType_83=ปรับเปลี่ยนพอร์ตคลาวด์เซิร์ฟเวอร์
att_device_op_log_opType_87=แก้ไขการตั้งค่าบันทึกการควบคุมการประตู
att_device_op_log_opType_88=แก้ไขการตั้งค่าสถานะพารามิเตอร์ใบหน้า
att_device_op_log_opType_89=แก้ไขการตั้งค่าสถานะพารามิเตอร์ของลายนิ้วมือ
att_device_op_log_opType_90=แก้ไขการตั้งค่าสถานะพารามิเตอร์เส้นเลือดดำ
att_device_op_log_opType_91=แก้ไขการตั้งค่าสถานะพารามิเตอร์ฝ่ามือ
att_device_op_log_opType_92=ตั้งค่าสถานะการอัปเกรด U Disk
att_device_op_log_opType_100=แก้ไขข้อมูลบัตร RF
att_device_op_log_opType_101=ลงทะเบียนใบหน้า
att_device_op_log_opType_102=แก้ไขสิทธิ์ของพนักงาน
att_device_op_log_opType_103=ลบการสิทธิ์ของพนักงาน
att_device_op_log_opType_104=เพิ่มสิทธิ์พนักงาน
att_device_op_log_opType_105=ลบบันทึกการควบคุมการเข้าถึง
att_device_op_log_opType_106=ลบใบหน้า
att_device_op_log_opType_107=ลบใบหน้าพนักงาน
att_device_op_log_opType_108=แก้ไขพารามิเตอร์
att_device_op_log_opType_109=เลือก WIFI SSID
att_device_op_log_opType_110=เปิดใช้งานพร็อกซี
att_device_op_log_opType_111=การปรับเปลี่ยนพร็อกซี
att_device_op_log_opType_112=การปรับเปลี่ยนพอร์ตพร็อกซี
att_device_op_log_opType_113=ปรับเปลี่ยนรหัสผ่านพนักงาน
att_device_op_log_opType_114=แก้ไขข้อมูลใบหน้า
att_device_op_log_opType_115=ปรับเปลี่ยนรหัสผ่านของผู้ปฎิบัติงาน
att_device_op_log_opType_116=ตั้งค่าควบคุมประตูต่อ
att_device_op_log_opType_117=ข้อผิดพลาดในการป้อนรหัสผ่านการดำเนินการ
att_device_op_log_opType_118=ล็อครหัสผ่านของการดำเนินการ
att_device_op_log_opType_120=แก้ไขความยาวข้อมูลบัตรที่ถูกต้อง
att_device_op_log_opType_121=ลงทะเบียนเส้นเลือดดำของนิ้ว
att_device_op_log_opType_122=แก้ไขเส้นเลือดของนิ้ว
att_device_op_log_opType_123=ลบเส้นเลือดดำของนิ้ว
att_device_op_log_opType_124=ลงทะเบียนฝ่ามือ
att_device_op_log_opType_125=แก้ไขฝ่ามือ
att_device_op_log_opType_126=ลบฝ่ามือ
#操作对象描述
att_device_op_log_content_pin=รหัสผู้ใช้:
att_device_op_log_content_alarm=แจ้งเตือน:
att_device_op_log_content_alarm_reason=เหตุการแจ้งเตือน:
att_device_op_log_content_update_no=แก้ไขหมายเลขรายการ:
att_device_op_log_content_update_value=แก้ไขค่า:
att_device_op_log_content_finger_no=หมายเลขลายนิ้วมือ:
att_device_op_log_content_finger_size=ความยาวเทมเพลตลายนิ้วมือ:
#=====================================================================
#工作流
att_flowable_datetime_to=ถึง
att_flowable_todomsg_leave=อนุมัตการลา
att_flowable_todomsg_sign=บันทึกการอนุมัตเพิ่มเติม
att_flowable_todomsg_overtime=อนุมัติล่วงเวลา
att_flowable_notifymsg_leave=แจ้งเตือนการลา
att_flowable_notifymsg_sign=บันทึกการแจ้งเตือนเพิ่มเติม
att_flowable_notifymsg_overtime=แจ้งเตือนการทำงานล่วงเวลา
att_flowable_shift=กะการทำงาน:
att_flowable_hour=ชั่วโมง
att_flowable_todomsg_trip=การอนุมัติการเดินทางเพื่อธุรกิจ
att_flowable_notifymsg_trip=การเดินทางเพื่อธุรกิจ
att_flowable_todomsg_out=อนุมัติการออกไป
att_flowable_notifymsg_out=แจ้งเตือนการออกไป
att_flow_apply=สมัคร
att_flow_applyTime=เวลาสมัคร
att_flow_approveTime=เวลาในการดำเนินการ
att_flow_operateUser=ผู้ตรวจทาน
att_flow_approve=อนุมัติ
att_flow_approveComment=คำอธิบายประกอบ
att_flow_approvePass=ผลการอนุมัติ
att_flow_status_processing=อนุมัติ
#=====================================================================
#biotime
att_h5_pers_personIdNull=รหัสพนักงานต้องไม่ว่างเปล่า
att_h5_attPlaceNull=ตำแหน่งเช็คอินต้องไม่ว่างเปล่า
att_h5_attAreaNull=พื้นที่การบันทึกเวลาต้องไม่ว่างเปล่า
att_h5_pers_personNoExist=ไม่มีหมายเลขพนักงาน
att_h5_signRemarkNull=หมายเหตุไม่สามารถเว้นว่างได้
att_h5_common_pageNull=ข้อผิดพลาดของพารามิเตอร์
att_h5_taskIdNotNull=id โหนดงานต้องไม่ว่างเปล่า
att_h5_auditResultNotNull=ผลการอนุมัติต้องไม่ว่างเปล่า
att_h5_latLongitudeNull=ลองจิจูดและละติจูดต้องไม่ว่างเปล่า
att_h5_pers_personIsNull=รหัสพนักงานไม่มีอยู่
att_h5_pers_personIsNotInArea=พนักงานไม่ได้ตั้งค่าพื้นที่
att_h5_mapApiConnectionsError=ข้อผิดพลาดในการเชื่อมต่อAPIของแผนที่
att_h5_googleMap=Google Map
att_h5_gaodeMap=แผนที่
att_h5_defaultMap=แผนที่เริ่มต้น
att_h5_shiftTime=เวลากะการทำงาน
att_h5_signTimes=เวลา
att_h5_enterKeyWords=โปรดป้อนคำสำคัญ:
att_h5_mapSet=การตั้งค่าแผนการบันทึกเลา
att_h5_setMapApiAddress=ตั้งค่าพารามิเตอร์แผนที่
att_h5_MapSetWarning=การเปลี่ยนแผนที่จะทำให้ที่อยู่การลงชื่อเข้าใช้ในมือถือที่ป้อนล้มเหลวต้องการสอดคล้องกับละติจูดและลองจิจูดโปรดแก้ไขด้วยความระมัดระวัง!
att_h5_mapSelect=เลือกแผนที่
att_h5_persNoHire=พนักงานยังไม่ได้เข้าบริษัท ในเวลานี้
att_slef_apiKey=API-KEY
att_self_apiJsKey=API-JS-KEY
att_self_noComputeAtt=การบันทึกเวลาวันนี้ยังไม่ได้รับการพิจารณา
att_self_noSignRecord=ไม่มีการบันทึกเข้างาน
att_self_imageUploadError=การอัพโหลดภาพล้มเหลว
att_self_attSignAddressAreaIsExist=มีจุดเช็คอินอยู่ในพื้นที่แล้ว
att_self_signRuleIsError=เวลาปัจจุบันไม่อยู่ในเวลาบันทึกเวลาที่อนุญาต
att_self_signAcrossDay=กำหนดตารางข้ามวันไม่สามารถลงชื่อได้!
att_self_todaySignIsExist=วันนี้มีการลงชื่อเข้าใช้
att_self_signSetting=ตั้งค่าการเข้าใช้
att_self_allowSign=อนุญาตการลงชื่อ:
att_self_allowSignSuffix=วันที่บันทึกเวลาด้วย
att_self_onlyThisMonth=เฉพาะเดือนนี้
att_self_allowAcrossMonth=อนุญาตให้ข้ามเดือน
att_self_thisTimeNoSch=ไม่มีการเปลี่ยนแปลงในช่วงเวลาปัจจุบัน!
att_self_revokeReason=เหตุผลสำหรับการยกเลิก:
att_self_revokeHint=โปรดป้อนสาเหตุของการยกเลิกภายใน 20 คำเพื่อการตรวจสอบ
att_self_persSelfLogin=การเข้าสู่ระบบด้วยตนเองของพนักงาน
att_self_isOpenSelfLogin=เริ่มเข้าสู่ระบบด้วยตนเองของพนักงานหรือไม่
att_self_applyAndWorkTimeOverlap=เวลาสมัครและเวลาทำงานทับซ้อนกัน
att_apply_DurationIsZero=ระยะเวลาการใช้งานคือ0 ไม่อนุญาติให้สมัคร
att_sign_mapWarn=การโหลดแผนที่ล้มเหลวโปรดตรวจสอบการเชื่อมต่อเครือข่ายและทำแผนที่ค่า KEY
att_admin_applyWarn=การดำเนินการล้มเหลวมีคนที่ไม่ได้กำหนดเวลาไว้หรือเวลาสมัครไม่อยู่ในขอบเขตของการจัดตารางเวลา!({0})
att_self_getPhotoFailed=ไม่มีรูปภาพ
att_self_view=ดู
# 二维码
att_param_qrCodeUrl=URL ของ QR Code
att_param_qrCodeUrlHref=ที่อยู่เซิร์ฟเวอร์: พอร์ต
att_param_appAttQrCode=QR สำหรับการบันทึกเวลาผ่านมือถือ
att_param_timingFrequency=ช่วงเวลา: 5-59 นาทีหรือ 1-24 ชั่วโมง
att_sign_signTimeNotNull=เวลาบันทึกต่อท้ายจะต้องไม่ว่างเปล่า
att_apply_overLastMonth=เริ้มสมัครนานกว่าเดือนที่แล้ว
att_apply_withoutDetail=ไม่มีรายละเอียดการดำเนินการ
att_flowable_noAuth=โปรดใช้บัญชีผู้ดูแลระบบขั้นสูงเพื่อดู
att_apply_overtimeOverMaxTimeLong=ค่าล่วงเวลายาวกว่าค่าล่วงเวลาสูงสุด
# 考勤设置参数符号
att_other_arrive=√
att_other_late=L
att_other_early=E
att_other_absent=□
att_other_noSignIn=[
att_other_noSignOff=]
att_other_leave=∆
att_other_overtime=OT
att_other_off=○
att_other_classes=●
att_other_trip=T
att_other_out=G
att_other_incomplete=├
att_other_outcomplete=┤
# 服务器下发命令
att_devCmd_submitTime=ส่งเวลา
att_devCmd_returnedResult=ส่งคืนผลลัพธ์
att_devCmd_returnTime=เวลาส่งคืน
att_devCmd_content=เนื้อหาคำสั่ง
att_devCmd_clearCmd=ล้างรายการคำสั่ง
# 实时点名
att_realTime_selectDept=กรุณาเลือกแผนก
att_realTime_noSignPers=พนักงานที่ไม่ลงทะเบียน
att_realTime_signPers=เช็คอิน
att_realTime_signMonitor=การตรวจสอบการลงชื่อเข้าใช้
att_realTime_signDateTime=เวลาเช็คอิน
att_realTime_realTimeSet=การตั้งค่าการโทรแบบเรียลไทม์
att_realTime_openRealTime=เปิดใช้งานการโทรตามเวลาจริง
att_realTime_rollCallEnd=การโทรแบบเรียลไทม์สิ้นสุดลง
# 12.0.0版本新增国际化
#人员排班
att_personSch_sched=ตารางเวลา
att_personSch_cycleSch=รอบตารางเวลา
att_personSch_cleanCycleSch=ล้างรอบตารางเวลา
att_personSch_cleanTempSch=ล้างตารางเวลาชั่วคราว
att_personSch_personCycleSch=รอบตารางของพนักงาน
att_personSch_deptCycleSch=รอบตารางแผนก
att_personSch_groupCycleSch=รอบตารางกลุ่ม
att_personSch_personTempSch=ตารางชั่วคราวของพนักงาน
att_personSch_deptTempSch=ตารางชั่วคราวของแผนก
att_personSch_groupTempSch=ตารางชั่วคราวของกลุ่ม
att_personSch_checkGroupFirst=โปรดตรวจสอบกลุ่มทางด้านซ้ายหรือพนักงานในรายชื่อทางด้านขวาเพื่อดำเนินการ!
att_personSch_sureDeleteGroup=คุณแน่ใจหรือไม่ว่าจะลบ {0} แลตารางเวลาที่สอดคล้องกับกลุ่ม
att_personSch_sch=ตารางเวลา
att_personSch_delSch=ลบตารางเวลา
#考勤计算
att_statistical_sureAllCalculate=คุณแน่ใจหรือไม่ว่าจะคำนวณบันทึกเวลาสำหรับพนักงานทั้งหมด?
#异常管理
att_exception_downTemplate=ดาวน์โหลดและนำเข้าเทมเพลต
att_exception_signImportTemplate=นำเข้าเทมเพลตรายงการบันทึกเวลา
att_exception_leaveImportTemplate=นำเข้าเทมเพลตการลา
att_exception_overtimeImportTemplate=นำเข้าเทมเพลตการทำงานงานล่วงเวลา
att_exception_adjustImportTemplate=นำเข้าเทมเพลตการเปลี่ยนกะทำงาน
att_exception_cellDefault=ไม่มีฟิลด์ที่ต้อง
att_exception_cellRequired=ฟิลด์ที่ต้องการ
att_exception_cellDateTime=ฟิลด์ที่ต้องการรูปแบบเวลาคือ yyyy-MM-dd HH: mm: ss เช่น: 2020-07-07 08:30:00
att_exception_cellLeaveTypeName=ฟิลด์ที่ต้องกรอกเช่น 'ลาพักร้อน', 'ลาแต่งงาน', 'ลาคลอด', 'ลาป่วย', 'ลาพักผ่อนประจำปี', 'ลาปลิดชีพ', 'ลาครอบครัว', 'ลาให้นมลูก', 'เดินทางเพื่อธุรกิจ', 'ออกไปข้างนอก' ชื่อหยิ่ง
att_exception_cellOvertimeSign=ฟิลด์ที่ต้องกรอกเช่น 'การทำงานล่วงเวลาปกติ', 'การทำงานล่วงเวลาในวันพัก', 'การทำงานล่วงเวลาในวันหยุด'
att_exception_cellAdjustType=ฟิลด์ที่ต้องการเช่น: 'ส่วนที่เหลือ', 'แต่งหน้า'
att_exception_cellAdjustDate=ฟิลด์ที่ต้องการรูปแบบเวลาคือ yyyy-MM-dd เช่น 2020-07-07
att_exception_cellShiftName=ฟิลด์ที่ต้องการเมื่อมีประเภทการเปลี่ยนกะทำงานคือกะที่ต้องการเปลี่ยน
att_exception_refuse=ปฏิเสธ
att_exception_end=จุดสิ้นสุด
att_exception_delete=ลบ
att_exception_stop=หยุดชั่วคราว
#时间段
att_timeSlot_normalTimeAdd=เพิ่มตารางเวลาปกติ
att_timeSlot_elasticTimeAdd=เพิ่มตารางเวลาแบบยืดหยุ่น
#班次
att_shift_addRegularShift=เพิ่มกะปกติ
att_shift_addFlexibleShift=เพิ่มกะแบบยืดหยุ่น
#参数设置
att_param_notLeaveSetting=การตั้งค่าการคำนวณที่ไม่ลา
att_param_smallestUnit=หน่วยขั้นต่ำ
att_param_workDay=วันทำงาน
att_param_roundingControl=การควบคุมการปัดเศษ
att_param_abort=ลง (ละทิ้ง)
att_param_rounding=ปัดเศษ
att_param_carry=ขึ้น (พกพา)
att_param_reportSymbol=สัญลักษณ์แสดงรายงาน
att_param_convertCountValid=โปรดป้อนตัวเลขและอนุญาตให้ใช้ทศนิยมหนึ่งตำแหน่งเท่านั้น
att_other_leaveThing=พนักงาน
att_other_leaveMarriage=การแต่งงาน
att_other_leaveBirth=การคลอดบุตร
att_other_leaveSick=ป่วย
att_other_leaveAnnual=ประจำปี
att_other_leaveFuneral=ศพ
att_other_leaveHome=ครอบครัว
att_other_leaveNursing=โรงพยาบาล
att_other_leavetrip=ธุรกิจ
att_other_leaveout=อื่น ๆ
att_common_schAndRest=กำหนดเวลาและพักผ่อน
att_common_timeLongs=ระยะเวลา
att_personSch_checkDeptOrPersFirst=โปรดตรวจสอบแผนกทางด้านซ้ายหรือพนักงานในรายการทางด้านขวา!
att_personSch_checkCalendarFirst=โปรดเลือกวันที่คุณต้องการกำหนดเวลาก่อน!
att_personSch_cleanCheck=ล้างรายการที่เลือก
att_personSch_delTimeSlot=ล้างตารางเวลาที่เลือก
att_personSch_repeatTimeSlotNoAdd=จะไม่มีการเพิ่มช่วงเวลาซ้ำ
att_personSch_showSchInfo=แสดงรายละเอียดตารางเวลา
att_personSch_sureToCycleSch=คุณแน่ใจหรือว่าจะกำหนดรอบตารางเวลา {0} หรือไม่
att_personSch_sureToTempSch=คุณแน่ใจว่าหรือว่าจะกำหนดรอบตารางเวลา {0} หรือไม่
att_personSch_sureToCycleSchDeptOrGroup=คุณแน่ใจว่าจะกำหนดรอบตารางเวลาพนักงานทั้งหมด {0} หรือไม่
att_personSch_sureToTempSchDeptOrGroup=คุณแน่ใจว่าจะกำหนดรอบตารางเวลาชั่วคราวพนักงานทั้งหมด{0} หรือไม่
att_personSch_sureCleanCycleSch=คุณแน่ใจหรือไม่ว่าต้องการลบรอบตารางเวลา {0} จาก {1} ถึง {2}
att_personSch_sureCleanTempSch=คุณแน่ใจหรือไม่ว่าต้องการลบ {0} กะชั่วคราวจาก {1} ถึง {2}
att_personSch_sureCleanCycleSchDeptOrGroup=คุณแน่ใจหรือไม่ว่าต้องการลบตารางเวลาจาก{1} ถึง {2} สำหรับทุกคน{0}
att_personSch_sureCleanTempSchDeptOrGroup=คุณแน่ใจหรือไม่ว่าคุณต้องการลบตารางชั่วคราวจาก {1} ถึง {2} สำหรับทุกคน{0}?
att_personSch_today=วันนี้
att_personSch_timeSoltName=ชื่อตารางเวลา
att_personSch_export=ส่งออกตารางเวลาของพนักงาน
att_personSch_exportTemplate=ส่งออกเทมเพลตกำหนดตารางเวลาชั่วคราว
att_personSch_import=นำเข้าตารางเวลาชั่วคราวของพนักงาน
att_personSch_tempSchTemplate=เทมเพลตตารางเวลาชั่วคราวของพนักงาน
att_personSch_tempSchTemplateTip=โปรดเลือกเวลาเริ่มต้นและเวลาสิ้นสุด ดาวน์โหลดเทมเพลตกำหนดภายในช่วงนั้น
att_personSch_opTip=คำแนะนำการใช้งาน
att_personSch_opTip1=1.คุณสามารถใช้เมาส์เพื่อลากช่วงเวลาไปยังวันที่เดียวในการควบคุมปฏิทินสำหรับการจัดตาราง
att_personSch_opTip2=2.ในตัวควบคุมปฏิทินให้ดับเบิลคลิกวันที่เดียวเพื่อกำหนด
att_personSch_opTip3=3.ในการควบคุมปฏิทินกดเมาส์ค้างไว้เพื่อเลือกวันที่หลายวันสำหรับการจัดตาราง
att_personSch_schRules=กฎของตารางเวลา
att_personSch_schRules1=1.รอบตารางเวลา: เขียนทับตารางเวลาก่อนหน้านี้หลังจากวันที่เดียวกันโดยไม่มีทางแยก
att_personSch_schRules2=2.ตารางชั่วคราว: มีการแยกในวันที่เดียวกันและตารางกะชั่วคราวก่อนหน้านี้จะถูกเขียนทับในภายหลังหากไม่มีแยกจะมีอยู่ในเวลาเดียวกัน
att_personSch_schRules3=3.รอบเวลาและชั่วคราว: หากมีทางแยกในวันเดียวกันระยะเวลาจะได้รับการคุ้มครองชั่วคราวและถ้าไม่มีทางแยกพวกเขาก็จะปรากฏขึ้นพร้อมกัน
att_personSch_schStatus=สถานะตารางเวลา
#左侧菜单-排班管理
att_leftMenu_schDetails=รายละเอียดตารางเวลา
att_leftMenu_detailReport=รายงานรายละเอียดการบันทึกเวลา
att_leftMenu_signReport=ตารางรายละเอียดการลงทะเบียน
att_leftMenu_leaveReport=รายงานการลา
att_leftMenu_abnormal=ตารางการเข้างานผิดปกติ
att_leftMenu_yearLeaveSumReport=รายงานการลาพักประจำปี
att_leave_maxFileCount=คุณสามารถเพิ่มรูปภาพได้สูงสุด 4 รูป
#时间段
att_timeSlot_add=ตั้งค่าตารางเวลา
att_timeSlot_select=โปรดเลือกตารางเวลา
att_timeSlot_repeat=ตารางเวลา "{0}" ซ้ำ!
att_timeSlot_overlapping=ตารางเวลา "{0}" ทับซ้อนกับเวลากัน "{1}"!
att_timeSlot_addFirst=โปรดตั้งค่าตารางเวลาก่อน
att_timeSlot_notEmpty=ตารางเวลาที่สอดคล้องกับหมายเลขพนักงาน {0} ต้องไม่ว่างเปล่า!
att_timeSlot_notExist=ตารางเวลา"{1}" ไม่ตรงกับหมายเลขพนักงาน {0}!
att_timeSlot_repeatEx=ตารางเวลา "{1}" ที่ตรงกับหมายเลขพนักงาน {0} มีทับซ้อนกับเวลา "{2}"
att_timeSlot_importRepeat=ตารางเวลา "{1}" ที่ตรงกับหมายเลขพนักงาน {0} ซ้ำกัน
att_timeSlot_importNotPin=ไม่มีพนักงานที่มีหมายเลข {0} ในระบบ!
att_timeSlot_elasticTimePeriod=หมายเลขบุคลากร {0} ไม่สามารถนำเข้าช่วงเวลายืดหยุ่น "{1}"!
#导入
att_import_overData=จำนวนการนำเข้าปัจจุบันคือ {0} เกินขีด จำกัด 30,000 โปรดนำเข้าเป็นชุด!
att_import_existIllegalType={0} ประเภทการนำเข้าผิดประเภท
#验证方式
att_verifyMode_0=บันทึกอัตโนมัติ
att_verifyMode_1=ลายนิ้วมือ
att_verifyMode_2=รหัส PIN
att_verifyMode_3=รหัส
att_verifyMode_4=บัตร
att_verifyMode_5=ลายนิ้วมือหรือรหัสผ่าน
att_verifyMode_6=ลายนิ้วมือหรือการ์ด
att_verifyMode_7=บัตรหรือรหัสผ่าน
att_verifyMode_8=รหัส PIN และลายนิ้วมือ
att_verifyMode_9=ลายนิ้วมือและรหัสผ่าน
att_verifyMode_10=บัตรและลายนิ้วมือ
att_verifyMode_11=บัตรและรหัสผ่าน
att_verifyMode_12=ลายนิ้วมือและรหัสผ่านและบัตร
att_verifyMode_13=รหัส PIN และลายนิ้วมือและรหัส
att_verifyMode_14=(หมายเลข PIN และลายนิ้วมือ) หรือ (บัตรและลายนิ้วมือ)
att_verifyMode_15=ใบหน้า
att_verifyMode_16=ใบหน้าและลายนิ้วมือ
att_verifyMode_17=ใบหน้าและรหัสผ่าน
att_verifyMode_18=ใบหน้าและบัตร
att_verifyMode_19=ใบหน้าและลายนิ้วมือและทั้งบัตร
att_verifyMode_20=ใบหน้าและลายนิ้วมือและรหัส
att_verifyMode_21=นิ้วเส้นเลือดดำ
att_verifyMode_22=นิ้วเส้นเลือดำและรหัส
att_verifyMode_23=นิ้วเส้นเลือดดำและบัตร
att_verifyMode_24=นิ้วเส้นเลือดและรหัสและบัตร
att_verifyMode_25=ฝ่ามือ
att_verifyMode_26=ฝ่ามือและบัตร
att_verifyMode_27=ฝ่ามือและใบหน้า
att_verifyMode_28=ฝ่ามือและลายนิ้วมือ
att_verifyMode_29=ฝ่ามือและลายนิ้วมือและใบหน้า
# 工作流
att_flow_schedule=กระบวนการตรวจสอบ
att_flow_schedulePass=(ผ่าน)
att_flow_scheduleNot=(ไม่อนุมัติ)
att_flow_scheduleReject=(ปฏิเสธ)
# 工作时长表
att_workTimeReport_total=ชั่วโมงการทำงานทั้งหมด
# 自动导出报表
att_autoExport_startEndTime=เวลาเริ่มต้นและสิ้นสุด
# 年假
att_annualLeave_setting=การตั้งค่ายอดการลาพักประจำปี
att_annualLeave_settingTip1=ในการใช้ฟังก์ชันยอดการลางานประจำปีคุณต้องกำหนดเวลาเข้าทำงานสำหรับพนักงานแต่ละคนเมื่อไม่ได้กำหนดเวลาเข้างานการลาประจำปีที่เหลือของตารางยอดการลาประจำปีของพนักงานจะแสดงเป็นว่างเปล่า
att_annualLeave_settingTip2=หากวันที่ปัจจุบันมากกว่าวันที่ออกการหักบัญชีการแก้ไขนี้จะมีผลในปีถัดไปหากวันที่ปัจจุบันน้อยกว่าวันที่ออกการหักบัญชีเมื่อถึงวันที่ออกการเคลียร์จะถูกล้างและการลาประจำปีจะออกให้ใหม่
att_annualLeave_calculate=คำนวนวันหักลาออกจากการลาประจำปี
att_annualLeave_workTimeCalculate=คำนวณตามอัตราส่วนเวลาทำงาน
att_annualLeave_rule=กฎการลางานประจำปี
att_annualLeave_ruleCountOver=ถึงขีด จำกัด จำนวนสูงสุดแล้ว
att_annualLeave_years=ปีการทำงาน
att_annualLeave_eachYear=ทุกปี
att_annualLeave_have=ใช่
att_annualLeave_days=วันลาพักผ่อนประจำปี
att_annualLeave_totalDays=การลาพักผ่อนประจำปีทั้งหมด
att_annualLeave_remainingDays=วันหยุดพักผ่อนประจำปีที่เหลืออยู่
att_annualLeave_consecutive=กฎการลาประจำปีต้องเป็นปีติดต่อกัน
# 年假结余表
att_annualLeave_report=งบดุลการลาประจำปี
att_annualLeave_validDate=วันที่ถูกต้อง
att_annualLeave_useDays=ใช้ {0} วัน
att_annualLeave_calculateDays=ออก {0} วัน
att_annualLeave_notEnough=วันหยุดพักผ่อนประจำปีใน {0} ไม่เพียงพอ!
att_annualLeave_notValidDate={0} ไม่อยู่ในช่วงการลาพักผ่อนประจำปีที่ถูกต้อง!
att_annualLeave_notDays={0} ไม่มีวันหยุดประจำปี!
att_annualLeave_tip1=Zhang San เข้าเมื่อวันที่ 1 กันยายนปีที่แล้ว
att_annualLeave_tip2=การตั้งค่ายอดการลาพักประจำปี
att_annualLeave_tip3=วันที่หักบัญชีและวันที่ออกคือวันที่ 1 มกราคมของทุกปีคำนวณโดยการปัดเศษตามอัตราส่วนการทำงานหากระยะเวลาทำงานน้อยกว่าหรือเท่ากับ 1 จะมีวันลาประจำปี 3 วันและหากอายุราชการน้อยกว่าหรือเท่ากับ 3 ปีจะมีวันลาประจำปี 5 วัน
att_annualLeave_tip4=คำนวนการลาประจำปี
att_annualLeave_tip5=ปีที่แล้ว 09-01 ~ 12-31 รวม 4 / 12x3=1.0 วัน
att_annualLeave_tip6=ปีนี้ 01-01 ~ 12-31 รวม 4.0 วัน (ปีนี้ 01-01 ~ 08-31 รวม 8 / 12x3=2.0 วัน   ปีนี้ 09-01 ~ 12-31 รวม 4 / 12x5≈2.0วัน)
# att SDC
att_sdc_name=อุปกรณ์วิดีโอ
att_sdc_wxMsg_firstData=สวัสดีคุณมีการเช็คอินการบันทึกเวลา
att_sdc_wxMsg_stateData=การเช็คอินสำเร็จ
att_sdc_wxMsg_remark=หมายเหตึ: ผลการการบันทึกเวลาสุดท้ายขึ้นอยู่กับหน้ารายละเอียดการเช็คอิน
# 时间段
att_timeSlot_conflict=ตารางเวลาขัดแย้งกับตารางเวลาอื่น ๆ ของวัน
att_timeSlot_selectFirst=โปรดเลือกตารางเวลา
# 事件中心
att_eventCenter_sign=บันทึกเวลาเช็คอิน
#异常管理
att_exception_classImportTemplate=นำเข้าเทมเพลตการเปลี่ยนกะทำงาน
att_exception_cellClassAdjustType=ฟิลด์ที่ต้องระบุ เช่น: "{0}", "{1}", "{2}"
att_exception_swapDateDate=ฟิลด์ที่ไม่จำเป็น รูปแบบเวลาคือ ปปปป-MM-dd เช่น: 2020-07-07
#消息中心
att_message_leave=บันทึกเวลาทำงาน{0}
att_message_leaveContent={0} ส่งแล้ว {1} {2} เวลาคือ {3}~{4}
att_message_leaveTime=เวลาการลา
att_message_overtime=บันทึกการเข้างานและการทำงานล่วงเวลา
att_message_overtimeContent={0} ส่งค่าล่วงเวลา และค่าล่วงเวลาคือ {1}~{2}
att_message_overtimeTime=ทำงานล่วงเวลา
att_message_sign=บันทึกการเข้างาน
att_message_signContent={0} ยืนยันการเข้างานและเวลาเข้างานคือ {1}
att_message_adjust=บันทึกการปรับเปลี่ยน
att_message_adjustContent={0} ยืนยันการปรับเปลี่ยนและวันที่ปรับเปลี่ยนคือ {1}
att_message_class=บันทึกการเปลียนกะทำงาน
att_message_classContent=รายละเอียดการเปลี่ยนกะ
att_message_classContent0={0} ยืนยันกะทำงานของวันทำงานคือ{1} และกะคือ {2}
att_message_classContent1={0}ยืนยันกะทำงานของวันทำงานคือ {1} และวันที่กะทำงานคือ {2}
att_message_classContent2={0} ({1}) และ {2} ({3}) การสลับกะทำงาน
#推送中心
att_pushCenter_transaction=บันทึกเวลาทำงาน
# 时间段
att_timeSlot_workTimeNotEqual=เวลาเช็คอินไม่สามารถเท่ากับเวลาเช็คเอาท์ได้
att_timeSlot_signTimeNotEqual=เวลาเริ่มต้นในการเช็คอินไม่สามารถเท่ากับเวลาเช็คเอาท์ได้
# 北向接口A
att_api_notNull={0} ไม่สามารถเว้นว่างได้!
att_api_startDateGeEndDate=เวลาเริ่มต้นต้องไม่มากกว่าหรือเท่ากับเวลาสิ้นสุด!
att_api_leaveTypeNotExist=ไม่มีรายละเอียดข้อมูล
att_api_imageLengthNot2000=ความยาวของที่อยู่รูปภาพต้องไม่เกิน 2000!
# 20221230新增国际化
att_personSch_workTypeNotNull=หมายเลขบุคลากร {0} ประเภทงานที่สอดคล้องกันไม่สามารถว่างได้!
att_personSch_workTypeNotExist=ไม่มีตำแหน่งงานที่ตรงกับหมายเลขบุคลากร {0}
att_annualLeave_recalculate=คำนวณใหม่
# 20230530新增国际化
att_leftMenu_dailyReport=รายงานการเข้าร่วมประจำวัน
att_leftMenu_overtimeReport=รายงานการทำงานล่วงเวลา
att_leftMenu_lateReport=รายงานล่าช้า
att_leftMenu_earlyReport=ออกจากรายงานล่วงหน้า
att_leftMenu_absentReport=ไม่มีรายงาน
att_leftMenu_monthReport=รายงานการเข้าร่วมประจำเดือน
att_leftMenu_monthWorkTimeReport=รายงานเวลาทำงานประจำเดือน
att_leftMenu_monthCardReport=รายงานบัตรประจำเดือน
att_leftMenu_monthOvertimeReport=รายงานการทำงานล่วงเวลารายเดือน
att_leftMenu_overtimeSummaryReport=รายงานสรุปการทำงานล่วงเวลาของพนักงาน
att_leftMenu_deptOvertimeSummaryReport=รายงานสรุปการทำงานล่วงเวลาของแผนก
att_leftMenu_deptLeaveSummaryReport=รายงานสรุปการลาของแผนก
att_annualLeave_calculateDay=จำนวนวันลาประจำปี
att_annualLeave_adjustDay=ปรับวัน
att_annualLeave_sureSelectDept=คุณแน่ใจหรือไม่ว่าต้องการดำเนินการ {0} ในแผนกที่เลือก
att_annualLeave_sureSelectPerson=คุณแน่ใจหรือไม่ว่าต้องการดำเนินการ {0} กับบุคคลที่เลือก
att_annualLeave_calculateTip1=เมื่อคำนวณตามอายุงาน: การคำนวณวันลาประจำปีจะแม่นยำเป็นเดือน หากอายุงาน 10 ปี 3 เดือน จะใช้ 10 ปี 3 เดือนในการคำนวณ
att_annualLeave_calculateTip2=เมื่อการแปลงไม่ได้ขึ้นอยู่กับระยะเวลาของการบริการ: การคำนวณวันลาประจำปีจะแม่นยำสำหรับปี หากระยะเวลาของการบริการคือ 10 ปี 3 เดือน 10 ปีจะถูกใช้สำหรับการคำนวณ
att_rule_isInCompleteTip=ลำดับความสำคัญจะสูงสุดเมื่อไม่มีการลงชื่อเข้าใช้หรือไม่มีการออกจากระบบถูกบันทึกว่าไม่สมบูรณ์ และการมาสาย การลาก่อนกำหนด การขาดงาน และถูกต้อง
att_rule_absentTip=เมื่อไม่มีการลงชื่อเข้าใช้หรือไม่ได้ออกจากระบบจะถูกบันทึกเป็นการขาดงาน ระยะเวลาของการขาดงานจะเท่ากับระยะเวลาของชั่วโมงทำงานลบด้วยระยะเวลาของการลาสายถึงก่อนเวลา
att_timeSlot_elasticTip1=0 เวลามีผลเท่ากับเวลาจริง ไม่มีขาด
att_timeSlot_elasticTip2=หากเวลาจริงนานกว่าเวลาทำงาน เวลาที่มีประสิทธิภาพจะเท่ากับเวลาทำงาน ไม่มีการขาดงาน
att_timeSlot_elasticTip3=หากระยะเวลาจริงน้อยกว่าระยะเวลาการทำงาน ระยะเวลาที่มีผลจะเท่ากับระยะเวลาจริง และการขาดงานจะเท่ากับระยะเวลาการทำงานลบด้วยระยะเวลาจริง
att_timeSlot_maxWorkingHours=ชั่วโมงทำงานต้องไม่เกิน
# 20231030
att_customReport=งบที่กำหนดเองสำหรับการเข้าร่วมประชุม
att_customReport_byDayDetail=รายละเอียดรายวัน
att_customReport_byPerson=สรุปโดยพนักงาน
att_customReport_byDept=สรุปโดยแผนก
att_customReport_queryMaxRange=ระยะเวลาสอบถามสูงสุดไม่เกินสี่เดือน
# 20240630新增国际化
att_personSch_shiftWorkTypeTip1=1, ประเภทของงานคือการทำงานปกติ / วันหยุดทำงานล่วงเวลาลำดับความสำคัญของการจัดตารางจะน้อยกว่าวันหยุด
att_personSch_shiftWorkTypeTip2=2 ประเภทของงานคือการทำงานล่วงเวลาในวันหยุดลำดับความสำคัญของการจัดตารางสูงกว่าวันหยุด
att_personVerifyMode=วิธีการตรวจสอบบุคคล
att_personVerifyMode_setting=วิธีการตรวจสอบ การตั้งค่า
att_personSch_importCycSch=นำเข้าตารางรอบบุคลากร
att_personSch_cycSchTemplate=เทมเพลตการจัดตารางบุคลากร
att_personSch_exportCycSchTemplate=ดาวน์โหลด เทมเพลตการจัดตารางบุคลากร
att_personSch_scheduleTypeNotNull=ประเภทกะไม่สามารถว่างหรือไม่มีอยู่!
att_personSch_shiftNotNull=กะต้องไม่ว่าง!
att_personSch_shiftNotExist=กะไม่มี!
att_personSch_onlyAllowOneShift=ตารางปกติอนุญาตให้เข้าเรียนได้เพียง 1 ตารางเท่านั้น!
att_shift_attShiftStartDateRemark2=สัปดาห์ที่วันที่เริ่มต้นของรอบเป็นสัปดาห์แรก เดือนที่วันที่เริ่มต้นของวงจรคือเดือนมกราคม
#打卡状态
att_cardStatus_setting=การตั้งค่าสถานะการเช็จ
att_cardStatus_name=ชื่อ
att_cardStatus_value=ค่า
att_cardStatus_alias=ชื่อย่อ
att_cardStatus_every_day=ทุกวัน
att_cardStatus_by_week=ตามสัปดาห์
att_cardStatus_autoState=สถานะอัตโนมัด
att_cardStatus_attState=สถานะเช็จ
att_cardStatus_signIn=การเข้ารถ
att_cardStatus_signOut=การออกรถ
att_cardStatus_out=ออกจาก
att_cardStatus_outReturn=การคืนที่
att_cardStatus_overtime_signIn=การเข้ารถพัฒนา
att_cardStatus_overtime_signOut=การออกรถพัฒนา
# 20241030新增国际化
att_leaveType_enableMaxDays=เปิดใช้การจำกัดวันสูงสุดของปี
att_leaveType_maxDays=จำกัดวันสูงสุด(วัน)
att_leaveType_applyMaxDays=ไม่สามารถยืนยันคำร้องได้เกิน {0} วัน
att_param_overTimeSetting=การตั้งค่าระดับการลาหมายเวลา
att_param_overTimeLevel=ระดับการลาหมายเวลา (ชั่วโมง)
att_param_overTimeLevelEnable=เปิดใช้การคำนวณระดับการลาหมายเวลา
att_param_reportColor=สีที่แสดงในรายงาน
# APP
att_app_signClientTip=กระบวนที่นี้ได้เข้าสู่ระบบและเป็นที่อยู่ให้ผู้ใช้งานอื่นๆ วันนี้
att_app_noSignAddress=พื้นที่เช็คอินยังไม่ตั้งค่า โปรดติดต่อแอดมิน đểตั้งค่า
att_app_notInSignAddress=ยังไม่ถึงจุดเช็คอิน ไม่สามารถเช็คอินได้
att_app_attendance=การตรวจตัวของฉัน
att_app_apply=ขอตรวจตัว
att_app_approve=การอนุมัติของฉัน
# 20250530
att_node_leaderNodeExist=จุดตรวจสอบการอนุมัติของผู้บังคับบัญชาชั้นตรงเผ่นอยู่แล้ว
att_signAddress_init=เริ่มต้นแผนที่
att_signAddress_initTips=โปรดใส่รหัส แล้วเริ่มต้นการตั้งค่าแผนที่เพื่อเลือกที่อยู่