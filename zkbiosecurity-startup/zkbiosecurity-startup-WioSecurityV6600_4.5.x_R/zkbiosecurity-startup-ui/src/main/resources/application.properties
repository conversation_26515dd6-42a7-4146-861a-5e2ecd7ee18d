#打包需加的配置
system.dashboardUrl=dashboard.do?dashboard
system.homePage=dashboard.do?dashboard
system.menuType=text-only
system.skin=lightgreen
system.skins=lightgreen
system.systemName=万傲瑞达6600
server.tomcat.threads.max=1000
spring.redis.lettuce.pool.max-active=300
spring.redis.lettuce.pool.max-idle=100
spring.redis.lettuce.pool.min-idle=100
server.servlet.encoding.force=true
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.tomcat.threads.min-spare=80
spring.redis.lettuce.pool.max-wait=-1
system.data.security-encrypt=true
server.port=8098
#https配置
#http.server.port=0
#security.require-ssl=true
#server.ssl.key-store=classpath:xmzkteco.p12
#server.ssl.key-store-password=xmzkteco
#server.ssl.keyStoreType=PKCS12
#server.ssl.keyAlias=tomcat
#spring.profiles.active=test
spring.jpa.show-sql=true
logging.level.org.springframework.data=DEBUG
spring.jpa.properties.hibernate.hbm2ddl.auto=update
spring.jpa.properties.hibernate.show_sql=false
#SQL SERVER配置
#spring.datasource.url=************************************************************************
#spring.datasource.username=root
#spring.datasource.password=Test123
#spring.datasource.driver-class-name=com.microsoft.sqlserver.jdbc.SQLServerDriver
#spring.jpa.properties.hibernate.dialect=com.zkteco.zkbiosecurity.core.config.SqlServerDialect
#MYSQL配置
#spring.datasource.url=*************************************
#spring.datasource.username= root
#spring.datasource.password= root
#spring.datasource.driver-class-name = com.mysql.jdbc.Driver
#POSTGRESQL
spring.datasource.url=***************************************************************
spring.datasource.username=postgres
spring.datasource.password=123456
spring.datasource.driver-class-name=org.postgresql.Driver
spring.jpa.properties.hibernate.dialect=com.zkteco.zkbiosecurity.core.config.PostgreDialect
#Oracle
#spring.datasource.url=*******************************************
#spring.datasource.username=security_fang
#spring.datasource.password=sa123
#spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
#下面为连接池的补充设置，应用到上面所有数据源中
# 初始化大小，最小，最大
spring.datasource.druid.initial-size=5
spring.datasource.druid.max-active=20
spring.datasource.druid.min-idle=5
# 配置获取连接等待超时的时间
spring.datasource.druid.max-wait=60000
# 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
spring.datasource.druid.timeBetweenEvictionRunsMillis=60000
# 配置一个连接在池中最小生存的时间，单位是毫秒
spring.datasource.druid.minEvictableIdleTimeMillis=300000
spring.datasource.druid.validationQuery=SELECT 1
spring.datasource.druid.testWhileIdle=true
spring.datasource.druid.testOnBorrow=false
spring.datasource.druid.testOnReturn=false
#spring.datasource.druid.stat-view-servlet.enabled=true
#
spring.http.encoding.force=true
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
server.tomcat.uri-encoding=UTF-8
#freemark配置
spring.freemarker.allow-request-override=false
spring.freemarker.allow-session-override=false
spring.freemarker.cache=true
spring.freemarker.charset=UTF-8
spring.freemarker.check-template-location=true
spring.freemarker.content-type=text/html
spring.freemarker.enabled=true
spring.freemarker.expose-request-attributes=false
spring.freemarker.expose-session-attributes=false
spring.freemarker.expose-spring-macro-helpers=true
spring.freemarker.prefer-file-system-access=false
spring.freemarker.suffix=.html
spring.freemarker.template-loader-path=classpath:/static
spring.freemarker.settings.template_update_delay=0
spring.freemarker.settings.default_encoding=UTF-8
spring.freemarker.settings.classic_compatible=true
spring.freemarker.order=1
#文件上传大小设置
spring.http.multipart.maxFileSize=50Mb
spring.http.multipart.maxRequestSize=500Mb
#spring.freemarker.request-context-attribute=request
#spring.jackson.time-zone=UTC
#spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
#i18n configuation
spring.defined.messages.basename=local/*.properties
spring.redis.host=127.0.0.1
spring.redis.port=6380
spring.redis.password=123456
spring.redis.database=1
spring.redis.timeout=1000
spring.redis.pool.max-active=20
spring.redis.pool.max-wait=-1
spring.redis.pool.max-idle=8
spring.redis.pool.min-idle=0
#tomcat线程
server.tomcat.max-threads=1000
server.tomcat.max-connections=2000
server.tomcat.min-spare-threads=80
#内部配置
biosecurity.date.sysDateFmt=yyyy-MM-dd
biosecurity.date.sysTimeFmt=HH:mm:ss
biosecurity.date.sysDateTimeFmt=yyyy-MM-dd HH:mm:ss
#开发模式下系统关键字
system.key=3165CF7BAA851E46AC0ADD1BEBE75BEE
#安装日期
system.installDate=a9/sAveMpKa1M70N+AsUsg==
#国家编码
system.country=CN
#安装语言 zh_CN en_US es
system.language=zh_CN
#--- system -----
#系统使用中控标(false)还是白标(true)-系统默认false
system.oem=false
#是否对接
integate.enabled=false
#许可认证路径
licenseAuthUrl=http://***************:8080/ZKBioLicense/authController.do?fileOnlineActiveAuth
#许可获取邮箱验证码
licenseEmailUrl=http://***************:8080/ZKBioLicense/authController.do?getVerifyCode
#许可在线检测
licenseCheckLicenseUrl=http://***************:8080/ZKBioLicense/authController.do?checkLicense
#许可在线更新
licenseUpdateUrl=http://***************:8080/ZKBioLicense/downloadController.do?updateLicense
#系统
system.version=1.0.0-SNAPSHOT.version
base.version=v5000
#系统产品号  ZKBioAccess ZKBioSecurity=5
system.productCode=ZKBioSecurity
package.bits=32
#打包时间
package.time=20161027
#是否是开发环境
system.devMode=false
#系统文件路径
system.filePath=BioSecurityFile
#adms通信端口
adms.push.port=8088
#netty是否支持https
adms.netty.https=false
#云服务地址
cloud.server.url=https://demo.xmzkteco.com
#是否为云服务
system.isCloud=false
#允许放过app请求
security.anon.url=app/v1/.*,api/v3/wechatAuth/.*,MP_verify_EhhKoouExlmVvQAP.txt,static/wechatAuthResult/.*,static/exceptionRecordDetail/.*
#云服务注册地址   v1.0旧的自动注册${cloud.server.url}/BiosecurityRegister/portal/register.do?auto  新的${cloud.server.url}/BiosecurityRegister/portal/companyRegister.do
cloud.server.register.url=${cloud.server.url}/BiosecurityRegister/portal/companyRegister.do
#激活验证
cloud.server.verify.url=${cloud.server.url}/BiosecurityRegister/portal/companyRegister.do?verify