<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<!-- 控制台 appender -->
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger - %msg%n
			</pattern>
		</encoder>
	</appender>
	<!-- 出错日志 appender -->
	<appender name="errorAppender"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>logs/zkbiosecurity-error.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
		<!-- 每天一归档 -->
		  <fileNamePattern>logs/zkbiosecurity-error.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
		   <!-- 单个日志文件最多 10MB, 60天的日志周期，最大不能超过10GB -->
		   <maxFileSize>10MB</maxFileSize>    
		   <maxHistory>60</maxHistory>
		   <totalSizeCap>10GB</totalSizeCap>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger-(%file:%line\) - %msg%n
			</pattern>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.LevelFilter"><!-- 只打印错误日志 -->
			<level>ERROR</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<!-- info日志 appender -->
	<appender name="infoAppender"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>logs/zkbiosecurity-info.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
		<!-- 每天一归档 -->
		  <fileNamePattern>logs/zkbiosecurity-info.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
		   <!-- 单个日志文件最多 10MB, 60天的日志周期，最大不能超过20GB -->
		   <maxFileSize>10MB</maxFileSize>    
		   <maxHistory>60</maxHistory>
		   <totalSizeCap>1GB</totalSizeCap>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger - %msg%n
			</pattern>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.LevelFilter"><!-- 只打印错误日志 -->
			<level>INFO</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>
	
	<!-- info日志 appender -->
	<appender name="warnAppender"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<File>logs/zkbiosecurity-warn.log</File>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
		<!-- 每天一归档 -->
		  <fileNamePattern>logs/zkbiosecurity-warn.log.%d{yyyy-MM-dd}.%i</fileNamePattern>
		   <!-- 单个日志文件最多 10MB, 60天的日志周期，最大不能超过20GB -->
		   <maxFileSize>10MB</maxFileSize>    
		   <maxHistory>60</maxHistory>
		   <totalSizeCap>1GB</totalSizeCap>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger - %msg%n
			</pattern>
		</encoder>
		<filter class="ch.qos.logback.classic.filter.LevelFilter"><!-- 只打印错误日志 -->
			<level>WARN</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
	</appender>

	<!--redis 打印级别设置-->
	<logger name="org.springframework.data.redis.core.RedisConnectionUtils" level="ERROR">
		<appender-ref ref="errorAppender"/>
	</logger>
	<!--日志打印的包的范围，及分类日志文件存储 -->
	<logger name="com.zkteco" additivity="false" level="INFO">
 		<appender-ref ref="STDOUT" />
		<appender-ref ref="errorAppender" />
		<appender-ref ref="infoAppender" />
		<appender-ref ref="warnAppender" />
	</logger>

	<!--控制台打印资源加载信息 -->
	<root level="INFO">
		<appender-ref ref="STDOUT" />
		<appender-ref ref="infoAppender" />
	</root>
</configuration>