<?xml version="1.0"?>
<project
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
        xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zkteco</groupId>
        <artifactId>zkbiosecurity-startup</artifactId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>zkbiosecurity-startup-ui</artifactId>
    <version>${parent.version}</version>
    <properties>
        <foldex.version>2.5.0-RELEASE</foldex.version>
        <cloud.sdk.version>3.0.2-RELEASE</cloud.sdk.version>
        <business.sdk.version>3.1.0-RELEASE</business.sdk.version>
        <dll.version>3.0.1-RELEASE</dll.version>
        <i18n.version>3.4.0-RELEASE</i18n.version>
        <guard.version>3.11.0-RELEASE</guard.version>
        <boot.version>3.12.0-RELEASE</boot.version>
        <auth.version>3.11.0-RELEASE</auth.version>
        <system.version>3.14.0-RELEASE</system.version>
        <license.version>3.10.0-RELEASE</license.version>
        <cmd.version>3.10.0-RELEASE</cmd.version>
        <module.api.version>3.13.0-RELEASE_YFDZ2025071400104</module.api.version>
        <adms.version>3.13.0-RELEASE</adms.version>
        <pers.version>3.14.0-RELEASE_YFDZ2025071400104</pers.version>
        <acc.version>3.13.0-RELEASE_YFDZ2025071400104</acc.version>
        <acc.advanced.version>3.12.0-RELEASE</acc.advanced.version>
        <vis.version>3.12.0-RELEASE</vis.version>
        <att.version>4.13.0-RELEASE_YFDZ2025071400104</att.version>
        <pos.version>3.7.0-RELEASE</pos.version>
        <park.version>3.12.0-RELEASE</park.version>
        <patrol.version>3.10.0-RELEASE</patrol.version>
        <pid.version>3.13.0-RELEASE</pid.version>
        <ins.version>3.12.0-RELEASE</ins.version>
        <ele.version>3.12.0-RELEASE</ele.version>
        <led.version>3.4.1-RELEASE</led.version>
        <dashboard.version>3.8.0-RELEASE</dashboard.version>
        <posid.version>3.13.0-RELEASE</posid.version>
        <workflow.version>3.6.0-RELEASE</workflow.version>
        <psg.version>3.12.0-RELEASE</psg.version>
        <serve.center.version>2.11.0-RELEASE</serve.center.version>
        <ivs.version>2.16.0-RELEASE</ivs.version>
        <vdb.version>1.3.0-RELEASE</vdb.version>
        <space.version>1.1.0-RELEASE</space.version>
        <ene.version>1.2.0-RELEASE</ene.version>
        <ias.version>1.5.0-RELEASE</ias.version>
        <abs.version>1.0.0-RELEASE</abs.version>
    </properties>
    <dependencies>
        <!--平台相关版本定义-->
        <!--boot相关版本-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot</artifactId>
            <version>2.3.12.RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-core</artifactId>
            <version>${boot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-redis</artifactId>
            <version>${boot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-scheduler</artifactId>
            <version>${boot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-security</artifactId>
            <version>${boot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-ui</artifactId>
            <version>${boot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-web</artifactId>
            <version>${boot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-base</artifactId>
            <version>${boot.version}</version>
        </dependency>
        <!--boot相关版本结束-->
        <!--通用国际化版本-->
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-i18n</artifactId>
            <version>${i18n.version}</version>
        </dependency>
        <!--平台加密工具版本-->
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-foldex</artifactId>
            <version>${foldex.version}</version>
        </dependency>
        <!--动态库版本-->
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-dll</artifactId>
            <version>${dll.version}</version>
        </dependency>
        <!--加密套件版本-->
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-guard</artifactId>
            <version>${guard.version}</version>
        </dependency>
        <!--云端sdk版本-->
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-cloud-sdk</artifactId>
            <version>${cloud.sdk.version}</version>
        </dependency>
        <!--授权Auth模块版本定义-->
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-auth-system</artifactId>
            <version>${auth.version}</version>
        </dependency>
        <!--系统System模块版本定义-->
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-system-system</artifactId>
            <version>${system.version}</version>
        </dependency>
        <!--许可license模块版本定义-->
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-license-system</artifactId>
            <version>${license.version}</version>
        </dependency>
        <!--cmd模块-->
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-cmd</artifactId>
            <version>${cmd.version}</version>
        </dependency>
        <!--通信adms模块-->
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-adms-system</artifactId>
            <version>${adms.version}</version>
        </dependency>
        <!--模块间API定义-->
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-module-all</artifactId>
            <version>${module.api.version}</version>
        </dependency>
        <!--人事模块-->
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-pers-system</artifactId>
            <version>${pers.version}</version>
        </dependency>
        <!--门禁模块-->
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-acc-system</artifactId>
            <version>${acc.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-acc-advanced-system</artifactId>
            <version>${acc.advanced.version}</version>
        </dependency>
        <!--考勤模块-->
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-att-system</artifactId>
            <version>${att.version}</version>
        </dependency>
<!--        &lt;!&ndash;消费模块&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.zkteco</groupId>-->
<!--            <artifactId>zkbiosecurity-pos-system</artifactId>-->
<!--            <version>${pos.version}</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash;梯控模块&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.zkteco</groupId>-->
<!--            <artifactId>zkbiosecurity-ele-system</artifactId>-->
<!--            <version>${ele.version}</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash;访客模块&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.zkteco</groupId>-->
<!--            <artifactId>zkbiosecurity-vis-system</artifactId>-->
<!--            <version>${vis.version}</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash;巡更模块&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.zkteco</groupId>-->
<!--            <artifactId>zkbiosecurity-patrol-system</artifactId>-->
<!--            <version>${patrol.version}</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash;停车场模块&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.zkteco</groupId>-->
<!--            <artifactId>zkbiosecurity-park-system</artifactId>-->
<!--            <version>${park.version}</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash;信息屏模块&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.zkteco</groupId>-->
<!--            <artifactId>zkbiosecurity-ins-system</artifactId>-->
<!--            <version>${ins.version}</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash;人证模块&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.zkteco</groupId>-->
<!--            <artifactId>zkbiosecurity-pid-system</artifactId>-->
<!--            <version>${pid.version}</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash;LED模块&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.zkteco</groupId>-->
<!--            <artifactId>zkbiosecurity-led-system</artifactId>-->
<!--            <version>${led.version}</version>-->
<!--        </dependency>-->
        <!--Dashboard -->
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-dashboard-system</artifactId>
            <version>${dashboard.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.zkteco</groupId>-->
<!--            <artifactId>zkbiosecurity-posid-system</artifactId>-->
<!--            <version>${posid.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.zkteco</groupId>-->
<!--            <artifactId>zkbiosecurity-workflow-system</artifactId>-->
<!--            <version>${workflow.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.zkteco</groupId>-->
<!--            <artifactId>zkbiosecurity-psg-system</artifactId>-->
<!--            <version>${psg.version}</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-business-sdk</artifactId>
            <version>${business.sdk.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.zkteco</groupId>-->
<!--            <artifactId>zkbiosecurity-serve-center-system</artifactId>-->
<!--            <version>${serve.center.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.zkteco</groupId>-->
<!--            <artifactId>zkbiosecurity-ivs-system</artifactId>-->
<!--            <version>${ivs.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.zkteco</groupId>-->
<!--            <artifactId>zkbiosecurity-vdb-system</artifactId>-->
<!--            <version>${vdb.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.zkteco</groupId>-->
<!--            <artifactId>zkbiosecurity-space-system</artifactId>-->
<!--            <version>${space.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.zkteco</groupId>-->
<!--            <artifactId>zkbiosecurity-ene-system</artifactId>-->
<!--            <version>${ene.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.zkteco</groupId>-->
<!--            <artifactId>zkbiosecurity-ias-system</artifactId>-->
<!--            <version>${ias.version}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.zkteco</groupId>-->
<!--            <artifactId>zkbiosecurity-abs-system</artifactId>-->
<!--            <version>${abs.version}</version>-->
<!--        </dependency>-->
    </dependencies>



    <build>
        <finalName>zkbiosecurity-startup</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>unpack</id>
                        <phase>package</phase>
                        <goals>
                            <goal>unpack-dependencies</goal>
                        </goals>
                        <configuration>
                            <includeGroupIds>com.zkteco.dll</includeGroupIds>
                            <outputDirectory>target/lib</outputDirectory>
                            <includes>dll/**,so/**,dylib/**</includes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <includeGroupIds>com.zkteco</includeGroupIds>
                            <excludeGroupIds>com.zkteco.dll</excludeGroupIds>
                            <outputDirectory>target/lib/jar</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.18</version>

                <configuration>
                    <mainClass>com.zkteco.zkbiosecurity.ZKBiosecurityAdmsApp</mainClass>
                    <outputDirectory>target/lib/jar</outputDirectory>
                    <layout>ZIP</layout>
                    <executable>true</executable>
                    <excludeGroupIds>com.zkteco,com.zkteco.dll</excludeGroupIds>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
