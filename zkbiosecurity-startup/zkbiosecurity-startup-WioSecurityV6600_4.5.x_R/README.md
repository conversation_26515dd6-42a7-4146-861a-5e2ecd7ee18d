# zkbiosecurity startup

## UI startup

startup for develop profile

### step 1

for mac os user :

> copy zkbiosecurity-startup-ui/src/main/dll/libaegean.dylib to your local path
like /users/sanye/three

for windows user :

> copy zkbiosecurity-startup-ui/src/main/dll/libaegean.dll to your local path
like D:\\sanye\\three

for linux user :

if you develop on linux.

please contact me.

welcome on board!

### step 2

modify pom.xml in your project(refer to the zkbiosecurity-demo).

### step 3

run your application with VM args below:

${aegean} : libaegean.dylib absolute path

{username} : your ldap username

{password} : your ldap password

> VM args: -agentpath:${aegean}={username}={password} -XX:+DisableAttachMechanism

eg. -agentpath:"D\\sanye\\three\\libaegean.dll"=sanye=123456 -XX:+DisableAttachMechanism
