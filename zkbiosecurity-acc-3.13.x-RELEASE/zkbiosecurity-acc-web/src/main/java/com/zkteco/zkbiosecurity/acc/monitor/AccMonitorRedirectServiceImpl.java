/*
 * File Name: AccMonitorRedirectServiceImpl <NAME_EMAIL> on 2018/6/1 15:49. Copyright:Copyright ©
 * 1999-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.monitor;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.utils.AccEnumUtil;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.utils.DateUtil.DateStyle;
import com.zkteco.zkbiosecurity.pers.service.PersParamsService;
import jodd.util.StringUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
@Component
public class AccMonitorRedirectServiceImpl implements AccMonitorRedirectService {

    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccRTMonitorService accRTMonitorService;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccPersonService accPersonService;
    @Autowired
    private AccTransactionService accTransactionService;
    @Autowired
    private AccCacheService accCacheService;
    @Autowired
    private AccParamService accParamService;
    @Autowired
    private PersParamsService persParamsService;
    /** 是否消息推送是否采用 发布/订阅 模式 */
    @Value("${acc.message.publish:false}")
    private boolean ACC_MESSAGE_PUBLISH;
    @Value("${system.skin:default}")
    private String systemSkin;
    @Autowired
    private AccDeviceEventService accDeviceEventService;
    @Value("${system.productCode:ZKBioSecurity}")
    private String productCode;
    @Value("${system.language:zh_CN}")
    private String language;

    @Override
    public void sendAccTransaction2RTMonitor(AccTransactionItem accTransactionItem, Map<String, String> userData,
        AccDeviceEventItem tempDevEvent) {
        String doorId = accTransactionItem.getEventPointId();
        final AccQueryDoorItem accDoor = getAccDoorFromCache(accTransactionItem.getDevSn(), doorId);
        // AccDoorItem accDoor = accDoorService.getItemById(accTransactionItem.getEventPointId());
        JSONObject ret = new JSONObject();
        JSONArray audios = new JSONArray();
        String tmpAudioPath = "";
        // 判断全局联动中是否有设置音频文件
        if (accTransactionItem.getAudioFilePath() != null) {
            audios.add(accTransactionItem.getAudioFilePath());
        }
        if (Objects.nonNull(accDoor)
            && !(Objects.nonNull(accDoor.getIsDisableAudio()) ? accDoor.getIsDisableAudio() : false)) {
            tmpAudioPath = getAudioPath(tempDevEvent);
        } else if (Objects.isNull(accDoor)) {
            tmpAudioPath = getAudioPath(tempDevEvent);
        }
        if (StringUtils.isNotBlank(tmpAudioPath)) {
            audios.add(tmpAudioPath);
        } else if (StringUtils.isBlank(tmpAudioPath) && StringUtils.isBlank(accTransactionItem.getAudioFilePath())) {
            // 当事件没有关联音频的时候，添加空音频路径，保持音频与事件排序一致
            audios.add(null);
        }
        ret.put("audios", audios);
        JSONArray rows = new JSONArray();
        JSONObject row = new JSONObject();
        row.put("id", accTransactionItem.getUniqueKey());
        // 将人员pin保存一份（不做敏感处理）
        accTransactionItem.setPersonPin(accTransactionItem.getPin());
        // 敏感信息处理
        //encryptItemProp(accTransactionItem);
        JSONObject userdata = buildUserDate(accTransactionItem, userData);
        if (accTransactionItem.getVidDevices() != null && accTransactionItem.getVidDevices().size() > 0) {
            row.put("vidDescription", accTransactionItem.getDescription());
            row.put("vidDevices", accTransactionItem.getVidDevices());
        }
        row.put("userdata", userdata);
        row.put("style", (accTransactionItem.getEventLevel() == AccConstants.EVENT_NORMAL ? "color:green"
            : (accTransactionItem.getEventLevel() == AccConstants.EVENT_WARNING ? "color:#E57A14" : "color:red")));
        // 数据等级:0正常、1异常、2报警
        row.put("dataLevel", accTransactionItem.getEventLevel());
        JSONArray data = new JSONArray();
        data.add(DateUtil.dateToString(accTransactionItem.getEventTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));// 时间
        data.add(accTransactionItem.getAreaName());// 区域
        data.add(accTransactionItem.getDevAlias() != null
            ? accTransactionItem.getDevAlias() + "(" + accTransactionItem.getDevSn() + ")" : "");// 设备
        data.add(accTransactionItem.getEventPointName());// 事件点
        String eventName = accTransactionItem.getEventName();
        if (accTransactionItem.getEventLevel() == AccConstants.EVENT_ALARM
            && ObjectUtils.isNotEmpty(accTransactionItem.getEventPriority())) {
            eventName += "," + AccConstants.ALARM_EVENT_PRIORITY.get(accTransactionItem.getEventPriority());
        }
        data.add(eventName);// 事件描述
        data.add(accTransactionItem.getCardNo());// 卡号
        /*String name =
            accTransactionService.getPersonAllName(accTransactionItem.getName(), accTransactionItem.getLastName());
        if (StringUtils.isNotBlank(name)) {
            name = "(" + name + ")";
        }*/
        String name = "";
        if (StringUtils.isNotBlank(accTransactionItem.getName())) {
            name = accTransactionItem.getName().trim();
        }
        // 中文下不显示lastname，非中文且lastname不为空时，拼接姓名，解决先前lastname为null时也拼接下去的问题
        if (!language.equals("zh_CN") && StringUtils.isNotBlank(accTransactionItem.getLastName())) {
            name = StringUtil.isNotBlank(name) ? (name + " " + accTransactionItem.getLastName()).trim() : accTransactionItem.getLastName().trim();
        }
        data.add(name);// 人员姓名
        data.add(accTransactionItem.getReaderName());// 读头名称
        data.add(accTransactionItem.getVerifyModeName());// 验证方式
        data.add(accTransactionItem.getDevAlias() != null
            ? accTransactionItem.getDevAlias() + "##" + accTransactionItem.getDevSn() + "##"
                + accTransactionItem.getEventPointName() + "##" + accTransactionItem.getEventPointType()
            : "");// 事件信息，前端用于事件过滤
        data.add(Objects.nonNull(accDoor) ? accDoor.getId() : "");
        data.add(Objects.nonNull(accDoor) && Objects.nonNull(accDoor.getEnabled()) ? accDoor.getEnabled() : true);// 因门禁里的门变为可禁用，故梯控默认给个true。
        data.add(accTransactionItem.getPin());// app需要获取pin
        row.put("data", data);
        rows.add(row);
        ret.put("rows", rows);
        // add by colin.cheng 2021-12-14 消息推送改为基于redis的发布/订阅，兼容多实例。
        if (ACC_MESSAGE_PUBLISH) {
            accCacheService.sendMessageToTopic(ret.toJSONString(), AccConstants.RTMONITOR_CHANNEL_TRANSACTION);
        } else {
            // 推送事件记录到实时监控
            messagingTemplate.convertAndSend("/topic/accRTMonitor/getEventData", ret);
            // 推送事件记录到电子地图
            messagingTemplate.convertAndSend("/topic/accMapMonitor/getEventData", ret);
            // add by xjing.huang 20190529 推送事件记录到dashboard
            messagingTemplate.convertAndSend("/topic/dashboardRTMonitor/getEventData", ret);
            // ZKBioAccess版本推送实时报警事件到看板
            if (productCode.equals("ZKBioAccess") && "alarm".equals(userdata.getString("status"))) {
                // 推送事件记录到实时监控
                ret.put("priority", accDeviceEventService.getEventPriorityByDevSnAndEventNo(accTransactionItem.getDevSn(),
                        accTransactionItem.getEventNo()));
                messagingTemplate.convertAndSend("/topic/accDashboardRTMonitor/getEventData", ret);
            }
        }
    }

    private String getAudioPath(AccDeviceEventItem accDeviceEvent) {
        String audioPath = "";
        // AccDeviceEventItem accDeviceEvent =
        // accDeviceEventService.getItemByDeviceIdAndEventNo(accTransactionItem.getDevId(),
        // accTransactionItem.getEventNo());
        if (Objects.nonNull(accDeviceEvent)) {
            audioPath = accRTMonitorService.getAudioPath(accDeviceEvent.getBaseMediaFileId());
        }
        return audioPath;
    }

    /**
     * 个人敏感信息处理
     *
     * @param item:
     * @return void
     * <AUTHOR>
     * @date 2022-09-30 16:00
     * @since 1.0.0
     */
    private void encryptItemProp(AccTransactionItem item) {
        String pin = persParamsService.getEncryptPin(item.getPin());
        String name = persParamsService.getEncryptName(item.getName());
        String lastName = persParamsService.getEncryptLastName(item.getLastName());
        String cardNo = persParamsService.getEncryptCardNo(item.getCardNo());
        item.setPin(pin);
        item.setName(name);
        item.setLastName(lastName);
        item.setCardNo(cardNo);
    }

    @Override
    public void sendDeviceDoorState2RTMonitor(ZKResultMsg resultMsg) {
        JSONObject data = new JSONObject();
        JSONArray stateArray = null;
        JSONArray doorStates = new JSONArray();
        JSONArray auxInStates = new JSONArray();
        JSONArray auxOutStates = new JSONArray();
        JSONObject dev = null;
        JSONObject devState = (JSONObject)resultMsg.getData();
        stateArray = devState.getJSONArray("doorState");
        if (Objects.nonNull(stateArray) && stateArray.size() > 0) {
            for (int i = 0; i < stateArray.size(); i++) {
                JSONObject door = stateArray.getJSONObject(i);
                dev = putDoorDataToMonitor(door);
                doorStates.add(dev);
            }
            data.put("doorStates", doorStates);
        }
        // add by colin.cheng 2021-12-14 消息推送改为基于redis的发布/订阅，兼容多实例。
        if (ACC_MESSAGE_PUBLISH) {
            accCacheService.sendMessageToTopic(data.toJSONString(), AccConstants.RTMONITOR_CHANNEL_DOORSTATE);
        } else {
            // 推送门状态到电子地图
            messagingTemplate.convertAndSend("/topic/accMapMonitor/getDoorState", data);
        }

        stateArray = devState.getJSONArray("auxInState");
        if (Objects.nonNull(stateArray) && stateArray.size() > 0) {
            for (int i = 0; i < stateArray.size(); i++) {
                JSONObject auxIn = stateArray.getJSONObject(i);
                int connect = auxIn.getIntValue("connect");
                auxIn.put("image", AccEnumUtil.AuxInImage.getValue(connect));
                auxInStates.add(auxIn);
            }
            data.put("auxInStates", auxInStates);
        }

        stateArray = devState.getJSONArray("auxOutState");
        if (Objects.nonNull(stateArray) && stateArray.size() > 0) {
            for (int i = 0; i < stateArray.size(); i++) {
                JSONObject auxOut = stateArray.getJSONObject(i);
                int connect = auxOut.getIntValue("connect");
                auxOut.put("image", AccEnumUtil.AuxOutImage.getValue(connect));
                auxOutStates.add(auxOut);
            }
            data.put("auxOutStates", auxOutStates);
        }
        // add by colin.cheng 2021-12-14 消息推送改为基于redis的发布/订阅，兼容多实例。
        if (ACC_MESSAGE_PUBLISH) {
            accCacheService.sendMessageToTopic(data.toJSONString(), AccConstants.RTMONITOR_CHANNEL_DEVSTATE);
        } else {
            messagingTemplate.convertAndSend("/topic/accRTMonitor/getDevState", data);
        }
    }

    private JSONObject putDoorDataToMonitor(JSONObject door) {
        JSONObject data = new JSONObject();
        int sensor = door.getIntValue("sensor");
        int relay = door.getIntValue("relay");
        int alarm = door.getIntValue("alarm");
        int connect = door.getIntValue("connect");
        short alarmLevel = door.getShortValue("alarmLevel");
        int doorState = door.getIntValue("doorState");
        boolean isNewAccess = door.getBooleanValue("isNewAccess");
        String alarmValue = AccEnumUtil.Alarm.getValue(doorState, alarmLevel, alarm);
        data.put("id", door.getString("id"));
        data.put("areaId", door.getString("areaId"));
        data.put("devAlias", door.getString("devAlias"));
        data.put("devSn", door.getString("devSn"));
        data.put("no", door.getString("no"));
        data.put("name", door.getString("name"));
        data.put("connect", connect);
        data.put("sensor", StringUtils.isNotBlank(AccEnumUtil.Sensor.getValue(doorState, sensor))
            ? AccEnumUtil.Sensor.getValue(doorState, sensor) : "");
        data.put("relay", StringUtils.isNotBlank(AccEnumUtil.Relay.getValue(doorState, relay))
            ? AccEnumUtil.Relay.getValue(doorState, relay) : "");
        data.put("alarm", StringUtils.isNotBlank(alarmValue) ? alarmValue : "");
        if (isNewAccess) {
            alarmLevel = 0;
        }
        data.put("image", AccEnumUtil.DoorImage.getValue(doorState, connect, alarmLevel, alarm, relay, sensor));
        data.put("lockDisplay", door.getString("lockDisplay"));
        data.put("opDisplay",
            doorState == ConstUtil.DEV_STATE_ONLINE || doorState == ConstUtil.DEV_STATE_LOCK ? "inline" : "none");
        data.put("iconFolderName", door.getString("iconFolderName"));
        // 设备离线、报警事件默认报警声提示
        if (connect == ConstUtil.DEV_STATE_OFFLINE) {
            data.put("audio", "/public/media/sound/alarm.wav");
        } else if (alarm > 0) {
            if (!door.getBoolean("isDisableAudio")) {
                data.put("audio", "/public/media/sound/alarm.wav");
            }
        }
        return data;
    }

    @Override
    public void sendDeviceMonitor(AccDeviceMonitorItem accDeviceMonitorItem) {
        JSONObject dxGrid = new JSONObject();
        dxGrid.put("pos", 0);
        List rows = new ArrayList();
        JSONObject row = new JSONObject();
        String status = accDeviceService.getStatus(accDeviceMonitorItem.getDevSn());
        row.put("id", accDeviceMonitorItem.getId());
        JSONObject userData = new JSONObject();
        userData.put("areaId", accDeviceMonitorItem.getAreaId());
        userData.put("status", accDeviceMonitorItem.getDevStatus());
        row.put("userdata", userData);
        if ("normal".equals(accDeviceMonitorItem.getDevStatus()))// 在线
        {
            row.put("style", "color:green");
            row.put("dataLevel", AccConstants.EVENT_NORMAL);
        } else if ("disable".equals(accDeviceMonitorItem.getDevStatus())) {
            row.put("style", "color:#E57A14");
            row.put("dataLevel", AccConstants.EVENT_WARNING);
        } else {
            row.put("style", "color:red");
            row.put("dataLevel", AccConstants.EVENT_ALARM);
        }
        JSONArray data = new JSONArray();
        data.add(accDeviceMonitorItem.getDevName());// 设备名称
        data.add(accDeviceMonitorItem.getDevSn());// 序列号
        data.add(accDeviceMonitorItem.getAreaName());// 区域
        data.add(accDeviceMonitorItem.getOpState());// 操作状态
        data.add(accDeviceMonitorItem.getCurState());// 当前状态
        data.add(accDeviceMonitorItem.getCmdCount());// 待执行命令条数
        data.add(accDeviceMonitorItem.getLastError().equals("") ? I18nUtil.i18nCode("common_none")
            : accDeviceMonitorItem.getLastError());// 最近异常状态
        data.add("0,1");
        row.put("data", data);
        rows.add(row);
        dxGrid.put("rows", rows);
        dxGrid.put("total_count", rows.size());
        // add by colin.cheng 2021-12-14 消息推送改为基于redis的发布/订阅，兼容多实例。
        if (ACC_MESSAGE_PUBLISH) {
            accCacheService.sendMessageToTopic(dxGrid.toJSONString(), AccConstants.RTMONITOR_CHANNEL_DEVEVENT);
        } else {
            messagingTemplate.convertAndSend("/topic/accDeviceMonitor/getDeviceEvents", dxGrid);
        }
    }

    /**
     * @Description: 组装用户数据
     * @param accTransactionItem
     * @param userData
     * @return
     */
    public JSONObject buildUserDate(AccTransactionItem accTransactionItem, Map<String, String> userData) {
        JSONObject userdata = new JSONObject();
        String photoPath = "";
        String status = accTransactionItem.getEventLevel() == AccConstants.EVENT_NORMAL ? "normal"
            : (accTransactionItem.getEventLevel() == AccConstants.EVENT_WARNING ? "warning" : "alarm");
        if ((!"normal".equals(status) || accTransactionItem.getEventNo() == 4008) && StringUtils.isBlank(photoPath)) {
            String skin = (StringUtils.isBlank(systemSkin) || "default".equals(systemSkin)) ? "" : systemSkin;
            photoPath = "images/" + skin + "/userImage.gif";
        }
        boolean isExistPerson = StringUtils.isNotBlank(accTransactionItem.getPin());
        // 查询人员照片路径，解决普通联动，弹出的抓拍窗口，不会显示人员照片的问题
        if (isExistPerson) {
            photoPath = accPersonService.getPersPersonPhotoPathByPin(accTransactionItem.getPersonPin());
        }
        userdata.put("photoPath", StringUtils.isNotBlank(photoPath) ? photoPath : userData.get("photoPath"));
        userdata.put("photoBase64", StringUtils.isNotBlank(photoPath)
                ? AccConstants.PHOTO_BASE64_PREFIX + FileEncryptUtil.getDecryptFileBase64(photoPath)
                : userData.get("photoBase64"));
        userdata.put("status", status);
        userdata.put("isExistCardOp", accTransactionService.isExistCardOp(accTransactionItem.getEventNo())
                && StringUtils.isNotBlank(accTransactionItem.getCardNo())
                && !StringUtils.isNotBlank(userData.get("photoPath")));
        userdata.put("eventNo", accTransactionItem.getEventNo());
        if (StringUtils.isNotBlank(accTransactionItem.getVidLinkageHandle())
            && !accTransactionItem.getVidLinkageHandle().startsWith("/upload")) {
            JSONObject tranJson = JSONObject.parseObject(JSONObject.toJSONString(accTransactionItem));
            userdata.put("globalLinkageEvent", tranJson);
        }
        String eventLevel = accTransactionItem.getEventLevel() + "";
        if (ObjectUtils.isNotEmpty(accTransactionItem.getEventPriority())) {
            eventLevel += "#" + accTransactionItem.getEventPriority();
        }
        userdata.put("eventLevel", eventLevel);
        return userdata;
    }

    @Override
    public void sendDataToLcdRtMonitoring(AccTransactionItem accTransaction, String photoPath) {
        // 过滤事件 仅人员成功进门时发送数据
        if (StringUtils.isNotBlank(accTransaction.getPin())) {
            JSONObject retJson = new JSONObject();
            String personName = accTransaction.getName();
            if (StringUtils.isNotBlank(accTransaction.getLastName())
                && !LocaleMessageSourceUtil.language.equals("zh_CN")) {
                // 中文下不显示lastName
                personName +=
                    (StringUtils.isNotBlank(accTransaction.getName()) ? " " : "") + accTransaction.getLastName().trim();
            }
            retJson.put("personName", StringUtils.isNotBlank(personName) ? personName : "");
            retJson.put("eventPointName", accTransaction.getEventPointName());
            retJson.put("eventName", accTransaction.getEventName());
            retJson.put("status", (accTransaction.getEventLevel() == AccConstants.EVENT_NORMAL ? "normal"
                : (accTransaction.getEventLevel() == AccConstants.EVENT_WARNING ? "warning" : "alarm")));
            retJson.put("eventTime",
                DateUtil.dateToString(accTransaction.getEventTime(), DateStyle.YYYY_MM_DD_HH_MM_SS));
            retJson.put("pin", accTransaction.getPin());
            retJson.put("readerState", accTransaction.getReaderState());
            retJson.put("cardNo", accTransaction.getCardNo());
            retJson.put("PersDepartment",
                StringUtils.isNotBlank(accTransaction.getDeptName()) ? accTransaction.getDeptName() : "");
            retJson.put("areaName", accTransaction.getAreaName());
            retJson.put("photoPath", StringUtils.isNotBlank(photoPath)
                ? AccConstants.PHOTO_BASE64_PREFIX + FileEncryptUtil.getDecryptFileBase64(photoPath) : "");
            // add by colin.cheng 2021-12-14 消息推送改为基于redis的发布/订阅，兼容多实例。
            if (ACC_MESSAGE_PUBLISH) {
                accCacheService.sendMessageToTopic(retJson.toString(), AccConstants.RTMONITOR_CHANNEL_LCD);
            } else {
                // 推送事件记录到LCD
                messagingTemplate.convertAndSend("/topic/accLcdRTMonitor/getLcdEvent", retJson.toString());
            }
        }
    }

    // 接收redis订阅消息的方法

    public void convertAndSendAccTransaction(String message) {
        final JSONObject ret = getMessage(message);
        if (ret != null) {
            // 推送事件记录到实时监控
            messagingTemplate.convertAndSend("/topic/accRTMonitor/getEventData", ret);
            // 推送事件记录到电子地图
            messagingTemplate.convertAndSend("/topic/accMapMonitor/getEventData", ret);
            // add by xjing.huang 20190529 推送事件记录到dashboard
            messagingTemplate.convertAndSend("/topic/dashboardRTMonitor/getEventData", ret);
        }
    }

    public void convertAndSendDoorState(String message) {
        final JSONObject data = getMessage(message);
        if (data != null) {
            messagingTemplate.convertAndSend("/topic/accMapMonitor/getDoorState", data);
        }
    }

    public void convertAndSendDevState(String message) {
        final JSONObject data = getMessage(message);
        if (data != null) {
            messagingTemplate.convertAndSend("/topic/accRTMonitor/getDevState", data);
        }
    }

    public void convertAndSendDevEvent(String message) {
        final JSONObject dxGrid = getMessage(message);
        if (dxGrid != null) {
            messagingTemplate.convertAndSend("/topic/accDeviceMonitor/getDeviceEvents", dxGrid);
        }
    }

    public void convertAndSendLcdEvent(String message) {
        final JSONObject retJson = getMessage(message);
        if (retJson != null) {
            // 推送事件记录到LCD
            messagingTemplate.convertAndSend("/topic/accLcdRTMonitor/getLcdEvent", retJson.toString());
        }
    }

    private JSONObject getMessage(String message) {
        if (message != null) {
            return JSONObject.parseObject(message);
        }
        return null;
    }

    /**
     * 从缓存中查询门
     * 
     * @param sn
     * @param doorId
     * @return
     */
    private AccQueryDoorItem getAccDoorFromCache(String sn, String doorId) {
        if (StringUtils.isNotBlank(sn) && StringUtils.isNotBlank(doorId)) {
            final AccQueryDeviceItem queryDeviceItem = accDeviceService.getQueryItemBySn(sn);
            final List<AccQueryDoorItem> doorItemList = queryDeviceItem.getAccDoorItemList();
            for (AccQueryDoorItem item : doorItemList) {
                if (doorId.equals(item.getId())) {
                    return item;
                }
            }
        }
        return null;
    }
}
