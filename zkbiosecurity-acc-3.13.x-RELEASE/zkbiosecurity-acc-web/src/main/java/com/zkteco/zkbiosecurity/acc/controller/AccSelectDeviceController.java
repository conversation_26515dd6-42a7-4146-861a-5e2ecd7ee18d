package com.zkteco.zkbiosecurity.acc.controller;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.zkteco.zkbiosecurity.acc.remote.AccSelectDeviceRemote;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.vo.Acc4PersDeviceSelectItem;
import com.zkteco.zkbiosecurity.acc.vo.AccSelectDeviceItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;

@Controller
public class AccSelectDeviceController extends BaseController implements AccSelectDeviceRemote {

    @Autowired
    private AccDeviceService accDeviceService;

    @Override
    public DxGrid list(AccSelectDeviceItem codition) {
        Pager pager = accDeviceService.getItemsByPage(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    /**
     * 卡格式测试：选择设备双列表
     * 
     * @param condition
     * @return
     */

    @Override
    public DxGrid selectDeviceList(Acc4PersDeviceSelectItem condition) {
        if (StringUtils.isBlank(condition.getSelectId())) {
            condition.setSelectId("-1");
        }
        if (condition.getType().equals("noSelected")) {
            condition.setNotInId(condition.getSelectId());
        } else if (condition.getType().equals("selected")) {
            condition.setInId(condition.getSelectId());
        }
        Pager pager = accDeviceService.getSelectDeviceItemByPage(condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public ZKResultMsg getDeviceList() {
        return new ZKResultMsg(accDeviceService.getSupportWiegandFmtDevices());

    }
}
