package com.zkteco.zkbiosecurity.acc.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.remote.AccVerifyModeRulePersonGroupRemote;
import com.zkteco.zkbiosecurity.acc.service.AccPersonService;
import com.zkteco.zkbiosecurity.acc.service.AccVerifyModeRuleService;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonVerifyModeRuleItem;
import com.zkteco.zkbiosecurity.acc.vo.AccVerifyModeRulePersonGroupItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

@Controller
public class AccVerifyModeRulePersonGroupController extends BaseController
    implements AccVerifyModeRulePersonGroupRemote {

    @Autowired
    private AccVerifyModeRuleService accVerifyModeRuleService;
    @Autowired
    private AccPersonService accPersonService;

    @RequiresPermissions("acc:verifyModeRulePersonGroup")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/verifyModeRulePersonGroup/accVerifyModeRulePersonGroup");
    }

    @RequiresPermissions("acc:verifyModeRulePersonGroup:refresh")
    @Override
    public DxGrid list(AccVerifyModeRulePersonGroupItem codition) {
        Pager pager = accVerifyModeRuleService.getItemsByPage(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:verifyModeRulePersonGroup:addPerson")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_verifyModeRulePersonGroup",
        opType = "pers_common_addPerson", requestParams = {"verifyModeRuleName", "personPins"},
        opContent = "pers_person_pin")
    @Override
    public ZKResultMsg addPerson(String verifyModeRulePersonGroupId, String personIds, String deptIds) {
        if (StringUtils.isNotBlank(deptIds)) {
            personIds = accPersonService.getPersonIdsByDeptIds(deptIds);
        }
        if (StringUtils.isNotBlank(verifyModeRulePersonGroupId) && StringUtils.isNotBlank(personIds)) {
            List<String> personIdList = new ArrayList<>(Arrays.asList(personIds.split(",")));
            // 获取验证方式规则人员组已经存在的人员ID并且过滤掉。
            List<String> existPersonIds = accVerifyModeRuleService.getExistPersonIds();
            if (existPersonIds.size() > 0) {
                personIdList.removeAll(existPersonIds);
            }
            accVerifyModeRuleService.addPerson(verifyModeRulePersonGroupId, personIdList);
        }
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:verifyModeRulePersonGroup:delPerson")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_verifyModeRulePersonGroup",
        opType = "pers_common_delPerson", requestParams = {"verifyModeRuleName", "personPins"},
        opContent = "pers_person_pin")
    @Override
    public ZKResultMsg delPerson(String verifyModeRulePersonGroupId, String personIds) {
        accVerifyModeRuleService.delPerson(verifyModeRulePersonGroupId, personIds);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:verifyModeRulePersonGroup:refresh")
    @Override
    public DxGrid personList(AccPersonVerifyModeRuleItem condition) {
        Pager pager = accPersonService.getPersonVerifyModeRuleItemList(request.getSession().getId(), condition,
            getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

}
