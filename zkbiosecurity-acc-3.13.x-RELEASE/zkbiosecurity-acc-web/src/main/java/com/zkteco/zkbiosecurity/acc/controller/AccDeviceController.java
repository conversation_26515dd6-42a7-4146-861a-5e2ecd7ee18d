/*
 * @author: GenerationTools
 * 
 * @date: 2018-03-08 下午02:41 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zkteco.zkbiosecurity.acc.constants.AccCacheKeyConstants;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.remote.AccDeviceRemote;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.annotation.LogChangeRequest;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.*;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.format.TreeBuilder;
import com.zkteco.zkbiosecurity.base.utils.Base64Util;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.net.InetAddress;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 门禁设备Controller
 * 
 * @author: GenerationTools
 * @date: 2018-03-08 下午02:41
 */
@Controller
public class AccDeviceController extends ExportController implements AccDeviceRemote {
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccParamService accParamService;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private AccBaseDictionaryService accBaseDictionaryService;
    @Autowired
    private AccDSTimeService accDSTimeService;
    @Autowired
    private AccReaderService accReaderService;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private AccSupportFuncService accSupportFuncService;
    @Value("${adms.push.port:8088}")
    private int hostPort;
    @Value("${server.port:8098}")
    private int serverPort;
    @Value("${security.require-ssl:false}")
    private String isSupportHttps;
    @Value("${system.filePath:BioSecurityFile}")
    private String systemFilePath;
    private static final String DEV_UPGRADEFIRMWARE_FILENAME = "emfw.cfg";
    private static final String READER_UPGRADEFIRMWARE_FILENAME = "reader.bin";
    private static final int defWaitCount = 5;// 等待固件重启默认轮询次数
    private static final int maxWaitCount = 12;// 等待固件重启最大轮询次数

    @RequiresPermissions("acc:device")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/device/accDevice");
    }

    @RequiresPermissions({"acc:device:add", "acc:device:edit"})
    @Override
    public ModelAndView edit(String id) {
        request.setAttribute("accParams", accParamService.getAccParams());
        request.setAttribute("persParams", accParamService.getPersParams());
        if (StringUtils.isNotBlank(id)) {
            AccDeviceItem accDeviceItem = accDeviceService.getItemById(id);

            if (Objects.nonNull(accDeviceItem)) {
                // 是否支持时区
                if (accDeviceItem.getMachineType() == AccConstants.DEVICE_BIOIR_9000) {
                    accDeviceItem.setAcpanelType(AccConstants.ACPANEL_1_DOOR);
                }
                if (accDeviceOptionService.isSupportFun(accDeviceItem.getSn(), "MachineTZFunOn")) {
                    request.setAttribute("supportTimeZone", 1);
                }
                if (accDeviceOptionService.isSupportFun(accDeviceItem.getSn(), "DSTFunOn")) {
                    request.setAttribute("supportDst", 1);
                    if (StringUtils.isNotBlank(accDeviceItem.getAccDSTimeId())) {
                        final AccDSTimeItem dsTimeItem = accDSTimeService.getItemById(accDeviceItem.getAccDSTimeId());
                        accDeviceItem.setAccDSTimeName(I18nUtil.i18nCode(dsTimeItem.getName()));
                    }
                }
                request.setAttribute("masterSlave", accDeviceOptionService.getMasterSlave(accDeviceItem.getSn()));// 主从机配置
                // 传给前端设置为韦根读头的名称
                if (StringUtils.isNotBlank(accDeviceItem.getWgReaderId())) {
                    String readerName = "";
                    AccReaderItem accReader = accReaderService.getItemById(accDeviceItem.getWgReaderId());
                    if (Objects.nonNull(accReader)) {
                        readerName = accReader.getName();
                    }
                    request.setAttribute("readerName", readerName);
                }
            }
            request.setAttribute("editPage", true);
            request.setAttribute("tempAccDevice", accDeviceItem);
        }
        return new ModelAndView("acc/device/editAccDevice");
    }

    @RequiresPermissions({"acc:device:add", "acc:device:edit"})
    @LogChangeRequest(module = "acc_module", object = "common_leftMenu_device", opType = "common_op_edit",
        fields = {"alias", "iconType", "timeZone", "authAreaId", "wgReaderId"}, vo = AccDeviceItem.class,
        service = AccDeviceService.class)
    @Override
    public ZKResultMsg save(AccDeviceItem item) {
        ZKResultMsg res = new ZKResultMsg();
        if (item.getFourToTwo() == null) {
            item.setFourToTwo(false);
        }
        if (StringUtils.isEmpty(item.getId())) {
            String acpanelOptions = request.getParameter("acpanelOptions");
            String accLevelId = request.getParameter("levelId");
            String clearAllData = request.getParameter("clearAllData");
            AccSearchAddDeviceItem tempInfo = new AccSearchAddDeviceItem();
            tempInfo.setLevelId(accLevelId);
            tempInfo.setClearAllData(clearAllData);
            accDeviceService.addPullDevice(item, acpanelOptions, tempInfo);
        } else {
            AccDeviceItem oldDev = accDeviceService.getItemById(item.getId());
            item = accDeviceService.saveItem(item);
            if (StringUtils.isNotBlank(item.getWgReaderId()) && !item.getWgReaderId().equals(oldDev.getWgReaderId())) {
                AccReaderItem accReaderItem = accReaderService.getItemById(item.getWgReaderId());
                if (Objects.nonNull(accReaderItem)) {
                    List<String> delLevelIdList = accLevelService.getLevelByDevId(item.getId());
                    List<String> doorIdList = accDoorService.getIdByDevId(item.getId());
                    List<String> addLevelIdList = accLevelService.getLevelIdByDoorId(accReaderItem.getDoorId());
                    if ((Objects.nonNull(delLevelIdList) && !delLevelIdList.isEmpty())
                        && (Objects.nonNull(addLevelIdList) && !addLevelIdList.isEmpty())) {// 删除和新增权限组都不为空时，判断是否存在相同权限组，移除相同权限组，避免重复操作
                        List<String> retainList = new ArrayList<>();
                        retainList.addAll(delLevelIdList);
                        retainList.retainAll(addLevelIdList);// 取出交集
                        if (!retainList.isEmpty()) { // 移除存在交集部分,避免删除和添加重复权限组操作
                            delLevelIdList.removeAll(retainList);
                            addLevelIdList.removeAll(retainList);
                        }
                    }

                    if (Objects.nonNull(delLevelIdList) && !delLevelIdList.isEmpty()) {
                        delLevelIdList.forEach(delLevelId -> accLevelService.immeDelLevelDoor(delLevelId, doorIdList));// 删除旧权限组
                    }
                    if (Objects.nonNull(addLevelIdList) && !addLevelIdList.isEmpty()) {
                        addLevelIdList.forEach(addLevelId -> accLevelService.addLevelDoor(addLevelId, doorIdList));// 添加门和权限
                    }
                }
            }
            // 夏令时
            accDSTimeService.setDSTime(item.getAccDSTimeId(), item.getId());
        }
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("acc:device:refresh")
    @Override
    public DxGrid list(AccDeviceItem condition) {
        Pager pager =
            accDeviceService.loadPagerByAuthFilter(request.getSession().getId(), condition, getPageNo(), getPageSize());// 根据登录用户权限过滤设备
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("acc:device:del")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "common_op_del",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg del(String ids) {
        accDeviceService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:device:export")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "common_op_export",
        opContent = "common_op_export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AccDeviceExportItem accDeviceItem = new AccDeviceExportItem();
        setConditionValue(accDeviceItem);
        // 根据当前登录用户设置需要过滤的区域
        String areaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId());
        if (StringUtils.isNotBlank(areaIds)) {
            accDeviceItem.setAreaIdIn(areaIds);
        }
        // 离线状态设备
        Set<String> offlineSns = new HashSet<>();
        // 在线状态设备
        Set<String> onLineSns = new HashSet<>();
        String connectState = accDeviceItem.getConnectState();
        if (StringUtils.isNotBlank(connectState) && connectState.equals(AccConstants.DEV_STATE_DISABLE + "")) {
            accDeviceItem.setEnabled(false);
        } else {
            // 不带条件查询或者查询的是非禁用设备
            // 获取redis缓存中的门禁设备信息keys,从而获取所有设备信息
            Map<Short, Set<String>> snsMap = accDeviceService.getDevSnsByRedis();
            offlineSns = snsMap.get(AccConstants.DEV_STATE_OFFLINE);
            onLineSns = snsMap.get(AccConstants.DEV_STATE_ONLINE);
            // 添加查询状态条件
            if (StringUtils.isNotBlank(connectState)) {
                accDeviceItem.setEnabled(true);
                if (connectState.equals(AccConstants.DEV_STATE_ONLINE + "")) {
                    accDeviceItem.setSnsNotIn(StringUtils.join(offlineSns, ","));
                } else {
                    accDeviceItem.setSnIn(StringUtils.join(offlineSns, ","));
                }
            }
        }
        List<AccDeviceExportItem> accDeviceItemList =
            accDeviceService.getExportItemList(accDeviceItem, getBeginIndex(), getEndIndex());
        Map<String, String> commTypeValue = Maps.newHashMap();
        Map<String, String> netConnectModeValue = Maps.newHashMap();
        Map<String, String> ableValue = Maps.newHashMap();
        // 通信方式
        commTypeValue.put("1", "TCP/IP");
        commTypeValue.put("2", "RS485");
        commTypeValue.put("3", "HTTP");
        commTypeValue.put("4", "MQTT");
        // 连接方式
        netConnectModeValue.put("0", I18nUtil.i18nCode("acc_dev_netModeWired"));
        netConnectModeValue.put("1", I18nUtil.i18nCode("acc_dev_netMode4G"));
        netConnectModeValue.put("2", I18nUtil.i18nCode("acc_dev_netModeWifi"));
        // 状态
        ableValue.put("0", I18nUtil.i18nCode("common_offline"));
        ableValue.put("1", I18nUtil.i18nCode("common_online"));
        ableValue.put("2", I18nUtil.i18nCode("common_disable"));
        Map<String, String> authAreaMap = accDeviceService.getAuthAreaByDev(accDeviceItemList);
        // 离线状态设备
        Set<String> offlineSnSet = new HashSet<>();
        // 在线状态设备
        Set<String> onLineSnSet = new HashSet<>();
        offlineSnSet.addAll(offlineSns);
        onLineSnSet.addAll(onLineSns);
        accDeviceItemList.stream().forEach(item -> {
            if (item.getAuthAreaId() != null) {
                item.setAuthAreaName(authAreaMap.get(item.getAuthAreaId()));
            }
            // 暂时手动进行数据格式化
            item.setCommType(commTypeValue.get(item.getCommType()));
            if (item.getEnabled()) {
                if (offlineSnSet.size() > 0 && offlineSnSet.contains(item.getSn())) {
                    item.setConnectState(ableValue.get("0"));
                } else if (onLineSnSet.size() > 0 && onLineSnSet.contains(item.getSn())) {
                    item.setConnectState(ableValue.get("1"));
                }
            } else {
                item.setConnectState(ableValue.get("2"));
            }
            if ("0".equals(item.getIsRegistrationDevice()) || "f".equals(item.getIsRegistrationDevice())) {// 设置登记机显示
                item.setIsRegistrationDevice(I18nUtil.i18nCode("common_no"));
            } else {
                item.setIsRegistrationDevice(I18nUtil.i18nCode("common_yes"));
            }
            if (item.getComPort() != null && item.getComAddress() != null) {
                String rs485Param =
                    String.format("COM%s(%s) %s", item.getComPort(), item.getComAddress(), item.getBaudrate());
                item.setRs485Param(rs485Param);
            }
            String NetConnectMode = accDeviceOptionService.getValueByNameAndDevSn(item.getSn(), "NetConnectMode");
            item.setNetConnectMode(netConnectModeValue.get(NetConnectMode));
        });
        excelExport(accDeviceItemList, AccDeviceExportItem.class);
    }

    @Override
    public boolean isExistByAlias(String alias) {
        AccDeviceItem accDeviceItem = new AccDeviceItem();
        accDeviceItem.setAlias(alias);
        return accDeviceService.getByCondition(accDeviceItem).size() > 0 ? false : true;
    }

    @Override
    public ZKResultMsg getAllIPSn() {
        JSONObject devInfoJson = new JSONObject();
        List<AccDeviceItem> accDeviceItemList = accDeviceService.getByCondition(new AccDeviceItem());
        JSONArray ipArray = new JSONArray();
        JSONArray snArray = new JSONArray();
        accDeviceItemList.stream().forEach(item -> {
            ipArray.add(item.getIpAddress());
            snArray.add(item.getSn());
        });
        devInfoJson.put("ipAddress", ipArray);
        devInfoJson.put("sn", snArray);
        return I18nUtil.i18nMsg(new ZKResultMsg(devInfoJson));
    }

    @RequiresPermissions("acc:device:searchDev")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "common_dev_searchDev",
        opContent = "common_dev_searchDev")
    @Override
    public ZKResultMsg searchDev(Long nowTime) {
        JSONObject devInfoJson = new JSONObject();
        try {
            List<AccSearchDeviceItem> deviceItems = accDeviceService.searchDeviceList();
            devInfoJson.put("ret", "ok");
            devInfoJson.put("devCount", deviceItems.size());
            JSONArray jsonArray = new JSONArray();
            List<String> macList = Lists.newArrayList();
            for (AccSearchDeviceItem item : deviceItems) {
                JSONObject rowData = new JSONObject();
                rowData.put("IP", item.getIp());
                if (item.getMacAddress() != null && !"".equals(item.getMacAddress())) {// 判断mac地址非空
                    if (macList.contains(item.getMacAddress())) {
                        rowData.put("exception",
                            I18nUtil.i18nCode("common_dev_macAddress") + I18nUtil.i18nCode("common_dev_duplicateData"));
                    } else {
                        rowData.put("MAC", item.getMacAddress());
                        macList.add(item.getMacAddress());
                    }
                }
                rowData.put("NetMask", item.getSubnetMask());
                rowData.put("GATEIPAddress", item.getGateway());
                if (StringUtils.isNotBlank(item.getSn())) {
                    rowData.put("SN", item.getSn());
                } else {
                    rowData.put("exception", I18nUtil.i18nCode("common_dev_snNull"));
                }
                rowData.put("Device", item.getDeviceName());
                rowData.put("Authorize", item.getAuthFlag());
                rowData.put("Protype", item.getProtype());
                rowData.put("ModeType", item.getModeType());
                rowData.put("IsSupportSSL", item.getIsSupportSSL());
                rowData.put("DNSFunOn", item.getDnsFunOn());
                rowData.put("IsSupportNVR", item.getIsSupportNVR());
                if (item.getDns() != null) {
                    rowData.put("DNS", item.getDns());
                }
                if (item.getServerUrl() != null) {
                    rowData.put("WebServerURL", item.getServerUrl());
                }
                if (item.getIsSupportMultiCard() != null) {
                    rowData.put("isSupportMultiCard", item.getIsSupportMultiCard().toString());
                }
                if (item.getMaxMCUCardBits() != null) {
                    rowData.put("MaxMCUCardBits", item.getMaxMCUCardBits());
                }
                if (item.getMachineType() != null) {
                    rowData.put("MachineType", item.getMachineType());
                }
                if (item.getSubControlOn() != null) {
                    rowData.put("SubControlOn", item.getSubControlOn());
                }
                if (item.getMasterControlOn() != null) {
                    rowData.put("MasterControlOn", item.getMasterControlOn());
                }
                if (item.getRegDeviceType() != null) {
                    rowData.put("RegDeviceType", item.getRegDeviceType());
                }
                jsonArray.add(rowData);
            }
            devInfoJson.put("devData", jsonArray);
            devInfoJson.put("nowTime", nowTime);
            devInfoJson.put("isSupportHttps", isSupportHttps);
            devInfoJson.put("hostAddress", InetAddress.getLocalHost().getHostAddress());
            devInfoJson.put("hostPort", hostPort);
        } catch (Exception e) {
            log.error("search dev error", e);
            throw new ZKBusinessException(e);
        }
        return I18nUtil.i18nMsg(new ZKResultMsg(devInfoJson));
    }

    @RequiresPermissions("acc:device:searchDev")
    @Override
    public ModelAndView getSearchDevInfo() {
        request.setAttribute("accParams", accParamService.getAccParams());
        request.setAttribute("persParams", accParamService.getPersParams());
        request.setAttribute("hostPort", hostPort);
        return new ModelAndView("acc/device/opAccSearchDev");
    }

    @RequiresPermissions("acc:device:updateMThreshold")
    @Override
    public ModelAndView getDevMThresholdInfo(String ids, String type) {
        return getById(ids, type);
    }

    @RequiresPermissions("acc:device:updateIpAddr")
    @Override
    public ModelAndView getDevIPAddressInfo(String ids, String type) {
        return getById(ids, type);
    }

    @RequiresPermissions("acc:device:updateCommPwd")
    @Override
    public ModelAndView getDevCommPwdInfo(String ids, String type) {
        return getById(ids, type);
    }

    @RequiresPermissions("acc:device:updateNetConnectMode")
    @Override
    public ModelAndView getDevNetConnectModeInfo(String ids, String type) {
        return getById(ids, type);
    }

    @RequiresPermissions("acc:device:addChildDevice")
    @Override
    public ModelAndView addChildDeviceInfo(String ids, String type) {
        return getById(ids, type);
    }

    @RequiresPermissions("acc:device:modParentDevice")
    @Override
    public ModelAndView modParentDeviceInfo(String ids, String type) {
        return getById(ids, type);
    }

    @RequiresPermissions("acc:device:configParentDevice")
    @Override
    public ModelAndView configParentDeviceInfo(String ids, String type) {
        return getById(ids, type);
    }

    @RequiresPermissions("acc:device:setDevIOState")
    @Override
    public ModelAndView getDevIOStateInfo(String ids, String type) {
        return getById(ids, type);
    }

    private ModelAndView getById(String id, String type) {
        if (StringUtils.isNotBlank(id)) {
            AccDeviceItem accDeviceItem = accDeviceService.getItemById(id);
            request.setAttribute("editPage", true);
            request.setAttribute("tempAccDevice", accDeviceItem);
            if (StringUtils.isNotBlank(type)) {
                if (accDeviceItem != null) {
                    String status = accDeviceService.getStatus(accDeviceItem.getSn());
                    if (!status.equals(String.valueOf(AccConstants.DEV_STATE_ONLINE)))// 判断设备是否在线
                    {
                        request.setAttribute("failedReason", status);
                        return new ModelAndView("acc/device/opDevNotSupportFeatureTips");
                    }
                }
                if (type.equals("updateIpAddr")) {
                    // 进入修改IP之前判断是否支持双网卡 by juvenile.li add 20170608
                    if (accDeviceOptionService.isSupportFunList(accDeviceItem.getSn(), 28)) {
                        request.setAttribute("isSupportDualCard", true);// 支持双网卡
                        AccDeviceOptionItem devOpt =
                            accDeviceOptionService.getDevOptValueBySnAndName(accDeviceItem.getSn(), "IPAddress1");
                        if (devOpt != null)// 由于新设备的网卡2可能没有值，上传上来值都为空，会没有保存起来 by juvenile.li add 20180110
                        {
                            request.setAttribute(devOpt.getName(), devOpt.getValue());
                        }
                        devOpt = accDeviceOptionService.getDevOptValueBySnAndName(accDeviceItem.getSn(), "NetMask1");
                        if (devOpt != null) {
                            request.setAttribute(devOpt.getName(), devOpt.getValue());
                        }
                        devOpt =
                            accDeviceOptionService.getDevOptValueBySnAndName(accDeviceItem.getSn(), "GATEIPAddress1");
                        if (devOpt != null) {
                            request.setAttribute(devOpt.getName(), devOpt.getValue());
                        }
                    }
                    return new ModelAndView("acc/device/opAccUpdateIpAddr");
                } else if (type.equals("updateCommPwd")) {
                    boolean isExistOldPwd = true;// 是否存在旧密码
                    if (accDeviceItem.getMachineType().equals(AccConstants.DEVICE_ACCESS_CONTROL)) {
                        request.setAttribute("isSupportChar", AccConstants.DEV_NOT_SUPPORT_FEATURE);// 1.代表设备不支持
                        if (accDeviceItem.getCommPwd() == null || "".equals(accDeviceItem.getCommPwd())
                            || "0".equals(accDeviceItem.getCommPwd())) {
                            isExistOldPwd = false;
                        }
                    } else {
                        if (accDeviceItem.getCommPwd() == null || "".equals(accDeviceItem.getCommPwd())) {
                            isExistOldPwd = false;
                        }
                    }
                    request.setAttribute("isExistOldPwd", isExistOldPwd);
                    return new ModelAndView("acc/device/opAccUpdateCommPwd");
                } else if (type.equals("updateMThreshold")) {
                    AccDeviceOptionItem multiBioDataSupport =
                            accDeviceOptionService.getDevOptValueBySnAndName(accDeviceItem.getSn(), "MultiBioDataSupport");
                    AccDeviceOptionItem multiBioPhotoSupport =
                            accDeviceOptionService.getDevOptValueBySnAndName(accDeviceItem.getSn(), "MultiBioPhotoSupport");
                    boolean supportFinger = false;
                    if (multiBioDataSupport != null) {
                        supportFinger =
                                "1".equals(multiBioDataSupport.getValue().split(":")[BaseConstants.BaseBioType.FP_BIO_TYPE]);
                    } else {
                        AccDeviceOptionItem fingerFunOn =
                                accDeviceOptionService.getDevOptValueBySnAndName(accDeviceItem.getSn(), AccConstants.FINGER_FUN_ON);
                        AccDeviceOptionItem isOnlyRFMachine =
                                accDeviceOptionService.getDevOptValueBySnAndName(accDeviceItem.getSn(), AccConstants.ONLY_RF_MACHINE);
                        if (fingerFunOn != null) {
                            supportFinger = "1".equals(fingerFunOn.getValue());
                        } else if (isOnlyRFMachine != null) {
                            supportFinger = !"1".equals(isOnlyRFMachine.getValue());
                        }
                    }
                    if (supportFinger) {
                        String mThreshold = "55";// 指纹比对阀值
                        AccDeviceOptionItem accDeviceOpt =
                                accDeviceOptionService.getDevOptValueBySnAndName(accDeviceItem.getSn(), "MThreshold");
                        if (accDeviceOpt != null) {
                            mThreshold = accDeviceOpt.getValue();
                        }
                        request.setAttribute("mThreshold", mThreshold);
                    }
                    boolean supportFace = false;
                    if (multiBioDataSupport != null) {
                        supportFace =
                                "1".equals(multiBioDataSupport.getValue().split(":")[BaseConstants.BaseBioType.FACE_BIO_TYPE]);
                        if (!supportFace) {
                            supportFace = "1"
                                    .equals(multiBioDataSupport.getValue().split(":")[BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE]);
                        }
                    } else {
                        supportFace = accDeviceOptionService.isSupportFun(accDeviceItem.getSn(), AccConstants.FACE_FUN_ON);
                    }
                    if (supportFace) {
                        String faceMThr = "70";// 人脸比对阀值
                        AccDeviceOptionItem accDeviceOpt =
                                accDeviceOptionService.getDevOptValueBySnAndName(accDeviceItem.getSn(), "~FaceMThr");
                        if (accDeviceOpt != null) {
                            faceMThr = accDeviceOpt.getValue();
                        }
                        request.setAttribute("faceMThr", faceMThr);
                    }

                    boolean supportPalm = false;
                    if (multiBioDataSupport != null) {
                        supportPalm =
                                "1".equals(multiBioDataSupport.getValue().split(":")[BaseConstants.BaseBioType.PALM_BIO_TYPE]);
                        if (!supportPalm && multiBioDataSupport.getValue().split(":").length > AccConstants.TEMPLATE_VISILIGHT_PALM) {
                            supportPalm = "1".equals(multiBioDataSupport.getValue().split(":")[AccConstants.TEMPLATE_VISILIGHT_PALM]);
                        }
                    } else {
                        supportPalm = accDeviceOptionService.isSupportFun(accDeviceItem.getSn(), AccConstants.PV_FUN_ON);
                    }
                    if (supportPalm) {
                        String pvMThreshold = "50";// 手掌比对阀值
                        AccDeviceOptionItem accDeviceOpt =
                                accDeviceOptionService.getDevOptValueBySnAndName(accDeviceItem.getSn(), "PvMThreshold");
                        if (accDeviceOpt != null) {
                            pvMThreshold = accDeviceOpt.getValue();
                        }
                        request.setAttribute("pvMThreshold", pvMThreshold);
                    }
                    if (!(supportFinger || supportFace || supportPalm) || accDeviceItem.getMachineType() == AccConstants.DEVICE_BIOIR_9000) {
                        request.setAttribute("failedReason", AccConstants.DEV_NOT_SUPPORT_FEATURE);// 1代表设备不支持此操作
                        return new ModelAndView("acc/device/opDevNotSupportFeatureTips");
                    }
                    return new ModelAndView("acc/device/opAccUpdateMThreshold");
                } else if (type.equals("updateNetConnectMode")) {
                    // 获取修改设置支持切换网络的设备
                    boolean isSupportWifi = accDeviceOptionService.getAccSupportFunListVal(accDeviceItem.getId(), 24);
                    boolean isSupport4G = accDeviceOptionService.getAccSupportFunListVal(accDeviceItem.getId(), 25);
                    // 不支持WIFI或者支持4G功能
                    if (!isSupportWifi && !isSupport4G) {
                        request.setAttribute("failedReason", AccConstants.DEV_NOT_SUPPORT_FEATURE);// 1代表设备不支持此操作
                        return new ModelAndView("acc/device/opDevNotSupportFeatureTips");
                    }

                    request.setAttribute("isSupportWifi", isSupportWifi);
                    request.setAttribute("isSupport4G", isSupport4G);
                    int netConnectMode = Integer.parseInt(
                        accDeviceOptionService.getValueByNameAndDevSn(accDeviceItem.getSn(), "NetConnectMode"));
                    // wifi模式下
                    if (2 == netConnectMode) {
                        AccDeviceOptionItem devOpt =
                            accDeviceOptionService.getDevOptValueBySnAndName(accDeviceItem.getSn(), "WirelessSSID");
                        if (devOpt != null) {
                            request.setAttribute("wirelessSSID", devOpt.getValue());
                        }
                    }
                    request.setAttribute("netConnectMode", netConnectMode);
                    return new ModelAndView("acc/device/opAccUpdateNetConnectMode");
                } else if ("configParentDevice".equals(type) || "modParentDevice".equals(type)) {
                    // 配置修改主设备或者变更主设备
                    request.setAttribute("type", type);
                    AccDeviceOptionItem devOpt =
                        accDeviceOptionService.getDevOptValueBySnAndName(accDeviceItem.getSn(), "WebServerURL");
                    if (Objects.nonNull(devOpt)) {
                        request.setAttribute("webServerURL", devOpt.getValue());
                    }
                    return new ModelAndView("acc/device/opAccConfigParentDev");
                } else if ("queryAuthorizeList".equals(type)) {
                    long cmdId = accDeviceService.queryAuthorizeListFromDev(accDeviceItem.getId()); // 下发获取待授权设备命令
                    request.setAttribute("cmdId", cmdId);
                    request.setAttribute("devId", accDeviceItem.getId());
                    return new ModelAndView("acc/device/opAccDeviceAuthorize");
                } else if ("setDevIOState".equals(type)) {
                    AccDeviceOptionItem devOpt =
                        accDeviceOptionService.getDevOptValueBySnAndName(accDeviceItem.getSn(), "Reader1IOState");
                    // 只有新一体机能设置进出状态
                    if (devOpt == null || accDeviceItem.getMachineType() != AccConstants.DEVICE_ACCESS_CONTROL) {
                        request.setAttribute("failedReason", AccConstants.DEV_NOT_SUPPORT_FEATURE);// 1代表设备不支持此操作
                        return new ModelAndView("acc/device/opDevNotSupportFeatureTips");
                    } else {
                        request.setAttribute("devId", accDeviceItem.getId());
                        request.setAttribute("devIOState", devOpt.getValue());
                        return new ModelAndView("acc/device/opAccSetDevIOState");
                    }
                } else if ("setResourceFile".equals(type)) {
                    if (!accDeviceOptionService.isSupportFun(accDeviceItem.getSn(), "adOn")) {
                        request.setAttribute("failedReason", AccConstants.DEV_NOT_SUPPORT_FEATURE);// 1代表设备不支持此操作
                        return new ModelAndView("acc/device/opDevNotSupportFeatureTips");
                    }
                    return new ModelAndView("acc/device/setAccResourceFiles");
                }
            }
        }
        return null;
    }

    @Override
    public ZKResultMsg validPushDevCount(String sn, String machineType) {
        ZKResultMsg resultMsg = accDeviceService.validPushDevCount(sn, machineType);
        // if (resultMsg.getData() != null) {
        // int dataCount = (int) resultMsg.getData();
        // resultMsg.setMsg(String.format(I18nUtil.i18nCode(resultMsg.getMsg()), dataCount));
        // resultMsg.setData(null);
        // }
        // else {
        // resultMsg.setMsg(I18nUtil.i18nCode(resultMsg.getMsg()));
        // }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public ZKResultMsg modifyIPAddress(AccSearchAddDeviceItem item) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        String mac = item.getMac();// 修改设备IP和WebServerIP都通过mac广播修改，MAC参数必须放在第一位
        String door4ToDoor2 = item.getDoor4ToDoor2();
        String options = "";
        String commPwd = StringUtils.isNotBlank(item.getCommPwd()) ? Base64Util.getFromBase64(item.getCommPwd()) : "";
        String ipAddress = item.getIpAddress();
        if (item.getType().equals("devIP") || item.getType().isEmpty()) {
            String newIP = item.getNewIP();
            String ip = IpUtil.getLocalIpAddr(request);// localhost访问下，获取的非服务器ip地址
            List<String> ipList = IpUtil.getAllIPAddr();// 获取本机所有网段的ip
            if (newIP.equals(ip) || ipList.contains(newIP)) {
                resultMsg.setRet("500");
                resultMsg.setMsg("acc_dev_addDevIpTip");
                return I18nUtil.i18nMsg(resultMsg);
            }
            String subnetMask = item.getSubnetMask();
            String gateway = item.getGateway();
            options =
                String.format("MAC=%s,SRCIPAddress=%s,IPAddress=%s,GATEIPAddress=%s,NetMask=%s,Reboot=1", mac,
                    ipAddress, newIP, gateway, subnetMask);
        } else { // 修改 webserverIP
            String webServerIP = item.getWebServerIP();// 新服务器地址
            String webServerPort = item.getWebServerPort();
            //String newPwd = item.getNewPwd();
            String newWebServerType = item.getNewWebServerType();
            String newDNS = item.getNewDNS();
            String newWebServerURL = item.getNewWebServerURL();
            String isSupportSSL = item.getIsSupportSSL();
            if (StringUtils.isNotBlank(newWebServerType) && newWebServerType.equals("2")) { // 使用域名方式
                if (isSupportHttps.equals("true")) {
                    // 当系统支持https方式设置WebServerURL参数，在一体机中使用WebServerURL时，一定要把WebServerURLModel=1,WebServerURLModel只在一体机中有用，代表开启域名模式
                    options =
                        String.format("MAC=%s,SRCIPAddress=%s,WebServerURL=%s,DNS=%s,WebServerURLModel=1",
                            mac, ipAddress, "https://" + newWebServerURL + ":" + webServerPort, newDNS);
                } else {
                    options =
                        String.format("MAC=%s,SRCIPAddress=%s,WebServerURL=%s,DNS=%s,WebServerURLModel=1",
                            mac, ipAddress, "http://" + newWebServerURL + ":" + webServerPort, newDNS);
                }
            } else {
                if (isSupportHttps.equals("true")) {
                    // 当系统支持https，设备不支持SSL的情况已经在前端屏蔽
                    options = String.format("MAC=%s,SRCIPAddress=%s,WebServerURL=%s,WebServerURLModel=1", mac,
                        ipAddress, "https://" + webServerIP + ":" + webServerPort);
                } else {
                    if (isSupportSSL.equals("true")) {
                        options = String.format("MAC=%s,SRCIPAddress=%s,WebServerURL=%s,WebServerURLModel=1",
                            mac, ipAddress, "http://" + webServerIP + ":" + webServerPort);
                    } else {
                        options = String.format(
                            "MAC=%s,SRCIPAddress=%s,WebServerIP=%s,WebServerPort=%s,WebServerURLModel=0", mac,
                            ipAddress, webServerIP, webServerPort);
                    }
                }
            }
            // 设置四门转两门参数
            if (StringUtils.isNotBlank(door4ToDoor2)) {
                options += ",Door4ToDoor2=" + door4ToDoor2;
            }
            if (StringUtils.isNotBlank(options)) {
                options += ",Reboot=1";// 设置完webserverIp 后需要重启才会生效
            }
        }
        String msgStr = "acc_dev_rebootAfterOperate";
        int ret = accDeviceService.modifyDeviceOptions(options, commPwd);
        if (ret < 0) {
            if ("devIP".equals(item.getType()) && ConstUtil.COMM_PWD_ERROR.equals(String.valueOf(ret))) {
                msgStr = "common_dev_modifyAndCommPwdError";
            } else if ("webServerIP".equals(item.getType()) && ConstUtil.COMM_PWD_ERROR.equals(String.valueOf(ret))) {
                msgStr = "common_commStatus_commPwdError";
            } else {
                msgStr = accBaseDictionaryService.getCommReason(ret);// 获取字段中错误返回值
            }
            if ("".equals(msgStr)) {
                msgStr = I18nUtil.i18nCode("common_unknown");
            }
            resultMsg.setRet("pwdError");
            resultMsg.setMsg(msgStr);
        } else if ("webServerIP".equals(item.getType())) { // 执行成功且为添加设备
            try {
                Thread.sleep(3000);// 等待3s保证设备重启成功
            } catch (InterruptedException e) {
                log.error(e.getMessage());
            }
            accDeviceService.authDevice(item.getSn(), item);
            resultMsg.setMsg(msgStr);
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @RequiresPermissions("acc:device:enable")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "common_enable",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg setDevEnable(String ids) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        accDeviceService.setDevEnable(ids);
        return I18nUtil.i18nMsg(resultMsg);
    }

    @RequiresPermissions("acc:device:disable")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "common_disable",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg setDevDisable(String ids) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        accDeviceService.setDevDisable(ids);
        return I18nUtil.i18nMsg(resultMsg);
    }

    @RequiresPermissions("acc:device:syncAllData")
    // @LogRequest(module="acc_module", object="common_leftMenu_device", opType="common_dev_syncAllDataToDev",
    // requestParams= {"alias"}, opContent="common_dev_name")
    @Override
    public ModelAndView getDevIdsBySyncData(String ids, String type) {
        if (type != null && !type.isEmpty()) {
            List<AccDeviceItem> list = accDeviceService.getItemByIds(ids);
            StringBuffer devName = new StringBuffer("");
            StringBuffer disabledDevName = new StringBuffer("");
            AccDeviceItem dev = null;
            StringBuffer retIds = new StringBuffer();
            Map<String, StringBuffer> devNameMap = new HashMap<String, StringBuffer>();
            for (int i = 0, len = list.size(); i < len; i++) {
                dev = list.get(i);
                short machineType = dev.getMachineType();
                if (accDeviceService.isEnabled(dev.getSn())) {
                    retIds.append(dev.getId()).append(",");
                    String devTypeName = AccConstants.MACHINETYPE_DEVTYPE.get(machineType);
                    if (devTypeName == null) {
                        // 如果没有该类型，则默认为0（统称控制器）,后续如需要再添加
                        devTypeName = AccConstants.MACHINETYPE_DEVTYPE.get(AccConstants.DEVICE_CONTROL);
                    }
                    if (devNameMap.containsKey(devTypeName)) {
                        devNameMap.get(devTypeName).append(dev.getAlias() + "&" + dev.getId() + ",");
                    } else {
                        StringBuffer devNameBuf = new StringBuffer(
                            devTypeName + "-" + I18nUtil.i18nCode(AccConstants.DEV_NAME_MAP.get(devTypeName)) + ":"
                                + dev.getAlias() + "&" + dev.getId() + ",");
                        devNameMap.put(devTypeName, devNameBuf);
                    }
                } else {
                    disabledDevName.append(dev.getAlias() + ",");
                }
            }
            for (StringBuffer nameBuf : devNameMap.values()) {
                devName.append(nameBuf.substring(0, nameBuf.length() - 1) + ";");
            }
            request.setAttribute("devIds", retIds.length() > 0 ? retIds.substring(0, retIds.length() - 1) : null);
            request.setAttribute("devicesName",
                devName.length() > 0 ? devName.substring(0, devName.length() - 1) : null);
            request.setAttribute("disabledDevName",
                disabledDevName.length() > 0 ? disabledDevName.substring(0, disabledDevName.length() - 1) : null);
            if ("syncAllData".equals(type)) {
                return new ModelAndView("acc/device/opAccSyncAllData");
            }
        }
        return null;
    }

    @RequiresPermissions("acc:device:upgradeFirmware")
    @Override
    public ModelAndView getDevIdsByUpgradeFirmware(String type, String checkOffline, String ids) {
        if (type != null && !type.isEmpty()) {
            List<AccDeviceItem> list = accDeviceService.getItemByIds(ids);
            Map<String, String> devNameMap = new HashMap<String, String>();
            StringBuffer disabledDevName = new StringBuffer("");
            StringBuffer offlineDevName = new StringBuffer("");
            StringBuffer supportDevFun = new StringBuffer("");
            AccDeviceItem dev = null;
            StringBuffer retIds = new StringBuffer();
            Map<String, String> regExMap = new HashMap<String, String>();
            regExMap.put("(?i)(Inbio)(1|2|4)60", "InBioX60");
            regExMap.put("(?i)(Inbio)(2|4)80", "InBioX80");
            regExMap.put("(?i)(C)3-(1|2|4)00", "C3");
            regExMap.put("(?i)(K)2-(1|2|4)00", "K2");
            regExMap.put("^F20", "F20");
            regExMap.put("^TF2000", "TF2000");
            regExMap.put("^TA1200", "TA1200");
            regExMap.put("^FV300", "FV300");
            regExMap.put("^V300", "V300");
            for (int i = 0; i < list.size(); i++) {
                dev = list.get(i);
                if ((String.valueOf(AccConstants.DEV_STATE_ONLINE)).equals(accDeviceService.getStatus(dev.getSn()))) {
                    String name = dev.getDeviceName();
                    String value = name;
                    for (String regEx : regExMap.keySet()) {
                        if (Pattern.compile(regEx).matcher(dev.getDeviceName()).find()) {
                            name = regEx;
                            value = regExMap.get(regEx);
                            break;
                        }
                    }
                    retIds.append(dev.getId()).append(",");
                    devNameMap.put(name, value);
                } else {
                    if ((String.valueOf(AccConstants.DEV_STATE_DISABLE))
                        .equals(accDeviceService.getStatus(dev.getSn()))) {
                        disabledDevName.append(dev.getAlias()).append(",");
                    } else {
                        if (StringUtils.isNotBlank(checkOffline)) {
                            String name = dev.getDeviceName();
                            String value = name;
                            devNameMap.put(name, value);
                        } else {
                            offlineDevName.append(dev.getAlias()).append(",");
                        }
                    }
                    list.remove(i);
                    i--;
                }
            }
            StringBuffer devName = new StringBuffer("");
            boolean isNeed = false;
            for (String key : devNameMap.keySet()) {
                devName.append(devNameMap.get(key)).append(":");
                for (int j = 0, innerLen = list.size(); j < innerLen; j++) {
                    if ((Pattern.compile(key).matcher(list.get(j).getDeviceName()).find()
                        || key.equals(list.get(j).getDeviceName()))
                        && (String.valueOf(AccConstants.DEV_STATE_ONLINE))
                            .equals(accDeviceService.getStatus(list.get(j).getSn()))) {
                        isNeed = true;
                        devName.append(list.get(j).getAlias() + "&" + list.get(j).getId()).append(",");
                    }
                }
                if (isNeed) {
                    devName.append(";");
                } else {
                    if (devName.indexOf(";") < 0) {
                        devName = new StringBuffer("");
                    } else {
                        devName.substring(0, devName.lastIndexOf(";") - 1);
                    }
                }
            }
            request.setAttribute("retIds",
                retIds.toString().equals("") ? "" : retIds.substring(0, retIds.length() - 1));
            request.setAttribute("devicesName",
                devName.toString().equals("") ? "" : devName.substring(0, devName.length() - 1));
            request.setAttribute("disabledDevName", disabledDevName.toString().equals("") ? ""
                : disabledDevName.substring(0, disabledDevName.length() - 1));
            request.setAttribute("offlineDevName",
                offlineDevName.toString().equals("") ? "" : offlineDevName.substring(0, offlineDevName.length() - 1));
            request.setAttribute("supportDevFun", supportDevFun.toString().equals("") ? ""
                : supportDevFun.substring(0, supportDevFun.toString().length() - 1));
            if (StringUtils.isNotBlank(checkOffline)) {
                request.setAttribute("checkOffline", checkOffline);
            }
            if ("upgradeFirmware".equals(type)) {
                return new ModelAndView("acc/device/opAccUpgradeFirmware");
            }
        }
        return null;
    }

    @RequiresPermissions("acc:device:rebootDevice")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "common_dev_reboot",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg rebootDevice(String ids) {
        String offline = "";
        List<String> rebootDevIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(ids)) {
            List<AccDeviceItem> devList = accDeviceService.getItemByIds(ids);
            for (AccDeviceItem dev : devList) {
                if (!(String.valueOf(AccConstants.DEV_STATE_ONLINE)).equals(accDeviceService.getStatus(dev.getSn())))// 判断设备是否在线
                                                                                                                     // 0离线，1在线
                {
                    offline += dev.getAlias() + I18nUtil.i18nCode("common_dev_offlinePrompt") + ",";
                } else {
                    rebootDevIdList.add(dev.getId());
                }
            }
            accDeviceService.rebootDevice(rebootDevIdList, false);// 修改成功后重启设备
            if (StringUtils.isNotBlank(offline)) {
                offline = offline.substring(0, offline.length() - 1);
                throw new ZKBusinessException(offline);
            }
        }
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:device:getOptFromDev")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "common_dev_getDevOpt",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg getOptFromDev(String ids) {
        StringBuffer failedDev = new StringBuffer("");
        ZKResultMsg resultMsg = new ZKResultMsg();
        String retStr = "ok";
        try {
            if (StringUtils.isNotBlank(ids)) {
                List<AccDeviceItem> devList = accDeviceService.getItemByIds(ids);
                List<Long> cmdIdList = new ArrayList<>();
                Map<Long, String> cmdIdAndDevNameMap = new HashMap<>();
                for (AccDeviceItem dev : devList) {
                    if (!accDeviceService.getStatus(dev.getSn())
                        .equals(String.valueOf(AccConstants.DEV_STATE_ONLINE))) { // 设备离线 或 禁用
                        failedDev
                            .append(dev.getAlias() + "," + I18nUtil.i18nCode("common_dev_offlinePrompt") + "</br>");
                        retStr = "500";
                        resultMsg.setRet(retStr);
                        resultMsg.setMsg(failedDev.substring(0, failedDev.length() - 1));
                    } else {
                        long cmdIdTemp = 0;// 用于判断
                        int optionCount = 3;// 参数遍历次数
                        if (accDeviceService.isNewAccessControlDevice(dev.getMachineType(), dev.getDeviceName())) {
                            optionCount = 4;// 参数遍历次数，如果是一体机需要遍历额外的参数
                        }
                        for (int i = 1; i < optionCount; i++) {// 获取设备参数
                            List<Long> cmdIds = accDeviceService.getOptFromDev(dev.getSn(), i);
                            for (long cmdId : cmdIds) {
                                if (cmdId < 0) {
                                    if (cmdIdTemp != 0) {// 如果有命令失败，要把设备从返回值列表中移除
                                        cmdIdList.remove(cmdIdTemp);
                                        cmdIdAndDevNameMap.remove(cmdIdTemp);
                                    }
                                    String describe = i == 1 ? I18nUtil.i18nCode("acc_dev_baseOptionTip")
                                        : I18nUtil.i18nCode("acc_dev_funOptionTip");
                                    failedDev.append(
                                        dev.getAlias() + ":" + describe + "," + I18nUtil.i18nCode("common_op_failed"));
                                    retStr = "400";
                                    resultMsg.setRet(retStr);
                                    resultMsg.setMsg(I18nUtil.i18nCode("acc_dev_sendComandoTip"));
                                    break;
                                } else {
                                    cmdIdTemp = cmdId;
                                    cmdIdList.add(cmdId);
                                    cmdIdAndDevNameMap.put(cmdId, dev.getAlias());
                                }
                            }
                        }
                    }
                }
                if (cmdIdList.size() > 0) {
                    for (long cmdId : cmdIdList) {
                        Map<String, String> resultMap = accDeviceService.getCmdResultById(cmdId, 30);
                        if (Objects.nonNull(resultMap)) {
                            Integer result = Integer.valueOf(resultMap.get("result"));
                            if (failedDev.indexOf(cmdIdAndDevNameMap.get(cmdId)) == -1) {
                                if (Objects.isNull(result)) {
                                    failedDev.append(cmdIdAndDevNameMap.get(cmdId) + ","
                                        + I18nUtil.i18nCode("common_op_failed") + "<br>");
                                    retStr = "400";
                                } else if (result < 0) {
                                    String failedInfo = accBaseDictionaryService.getCommReason(result);
                                    failedInfo = StringUtils.isNotBlank(failedInfo) ? I18nUtil.i18nCode(failedInfo)
                                        : result.toString();
                                    failedDev.append(cmdIdAndDevNameMap.get(cmdId) + ","
                                        + I18nUtil.i18nCode("common_op_failed") + ","
                                        + I18nUtil.i18nCode("common_dev_errorCode") + ":" + failedInfo + "<br>");
                                    retStr = "400";
                                }
                            }
                        }
                    }
                    if (retStr.equals("400")) {
                        resultMsg.setRet(retStr);
                        resultMsg.setMsg(I18nUtil.i18nCode("common_dev_getDevOptFails"));
                    }
                }
            }
        } catch (Exception e) {
            throw ZKBusinessException.errorException(I18nUtil.i18nCode("common_dev_getDevOptFails"));
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public ZKResultMsg syncAllDataByDevType(String devType, String devId) {
        JSONArray dataArray = new JSONArray();
        Set<String> funcSet = new LinkedHashSet<String>();
        if (AccConstants.DEVICE_CONTROL_NAME.equals(devType)) {
            funcSet = new LinkedHashSet<String>(AccConstants.DEVICE_CONTROL_FUNCTION);
        } else if (AccConstants.DEVICE_ACCESS_VEIN_NAME.equals(devType)) {
            // funcSet = new LinkedHashSet<String>(AccConstUtil.DEVICE_CONTROL_FUNCTION);
        } else if (AccConstants.DEVICE_ACCESS_CONTROL_NAME.equals(devType)) {
            funcSet = new LinkedHashSet<String>(AccConstants.DEVICE_ACCESS_FUNCTION);
        } else if (AccConstants.DEVICE_WIRELESS_LOCK_NAME.equals(devType)) {
            funcSet = new LinkedHashSet<String>(AccConstants.DEVICE_WIRELESS_LOCK_FUNCTION);
        }
        // 判断设备是否支持韦根格式自定义功能
        List<AccDeviceItem> devList = new ArrayList<AccDeviceItem>();
        if (StringUtils.isNotBlank(devId) && !devId.equals("undefined")) {
            devList = accDeviceService.getItemByIds(devId);
        }
        for (AccDeviceItem dev : devList) {
            if (accDeviceOptionService.isSupportFun(dev.getSn(), "~CardFormatFunOn")) {
                funcSet.add(AccConstants.AccSyncDataType.WIEGANDFMT.getValue());// 韦根格式
            }
            if (accDeviceOptionService.isSupportFun(dev.getSn(), "OutRelaySetFunOn")) {
                funcSet.add(AccConstants.AccSyncDataType.OUTRELAYSET.getValue());// 辅助输出参数
            }
            if (accSupportFuncService.isSupportIssueBgVerify()
                && (dev.getCommType().equals(AccConstants.COMM_TYPE_PUSH_HTTP)
                    || dev.getCommType().equals(AccConstants.COMM_TYPE_BEST_MQTT))
                && dev.getMachineType() != AccConstants.DEVICE_BIOIR_9000) {
                funcSet.add(AccConstants.AccSyncDataType.BACKGROUNDVERIFYPARAM.getValue());
            }
            if (AccConstants.DEVICE_ACCESS_CONTROL == dev.getMachineType())// 一体机支持首人常开
            {
                if (!funcSet.contains(AccConstants.AccSyncDataType.FIRSTPERSON.getValue())) {
                    funcSet.add(AccConstants.AccSyncDataType.FIRSTPERSON.getValue());
                }
            }
            if (accDeviceOptionService.isSupportFunList(dev.getSn(), 8)) {
                funcSet.add(AccConstants.AccSyncDataType.AUXINSET.getValue());
            }
            if (accSupportFuncService.isSupportVerifyModeRule()
                && accDeviceOptionService.isSupportFunList(dev.getSn(), 12)) {
                funcSet.add(AccConstants.AccSyncDataType.VERIFYMODERULE.getValue());
            }
        }
        if (funcSet.size() > 0) {
            for (String func : funcSet) {
                dataArray.add(func);
            }
        }
        return I18nUtil.i18nMsg(new ZKResultMsg(dataArray));
    }

    @RequiresPermissions("acc:device:syncAllData")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "common_dev_syncAllDataToDev",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg syncAllData(String devIds, String optBoxValue) {
        try {
            String[] optBox = optBoxValue.split(",");
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了 by juvenile.li add 20171114
            List<AccDeviceItem> devList = accDeviceService.getItemByIdsAndMoveUpParentDev(devIds);
            // 通知其他模块同步数据的设备id
            List<String> devIdList = new ArrayList<>();
            int size = devList.size();
            for (int i = 0; i < size; i++) {
                AccDeviceItem dev = devList.get(i);
                devIdList.add(dev.getId());
                int total = (int)((i + 1) / (float)size * 100);
                int lastTotal = (int)((i + 0.3) / (float)size * 100);
                progressCache.setProcess(new ProcessBean(30, lastTotal,
                    dev.getAlias() + ":" + I18nUtil.i18nCode("common_dev_syncPrompt") + "<br/>"));
                // 删除设备待执行指令,移到controller层处理,修复反馈sqlserver数据库待执行指令多时,出现数据库互锁的问题
                accDeviceService.clearCmdCache(dev.getSn(), "-1111");
                accDeviceService.syncDataToDev(dev.getSn(), optBox);
                // 获取需要同步的设备下被绑定的读头一体机，同时进行同步操作
                List<AccDeviceItem> asWgReaderDevSnList = accDeviceService.getDevAsWGReaderByDevId(dev.getId());
                for (AccDeviceItem deviceItem : asWgReaderDevSnList) {
                    devIdList.add(deviceItem.getId());
                    accDeviceService.clearCmdCache(dev.getSn(), "-1111");
                    accDeviceService.syncDataToDev(deviceItem.getSn(),
                        new String[] {AccConstants.AccSyncDataType.ACCLEVEL.getValue(),
                            AccConstants.AccSyncDataType.TIMEZONEANDHOLIDAY.getValue()});
                }
                progressCache.setProcess(new ProcessBean(100, total,
                    dev.getAlias() + ":" + I18nUtil.i18nCode("common_dev_syncSuccess") + "<br/>"));
            }
            accDeviceService.informOtherModuleSyncData(devIdList);
        } catch (Exception e) {
            log.error("exception", e);
        } finally {
            if (accDeviceService.checkPermission(request.getRequestedSessionId(), "acc:deviceMonitor")) {
                progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish"));
            } else {
                progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish"));
            }

        }
        return null;
    }

    @RequiresPermissions("acc:device:syncTime")
    @Override
    public ModelAndView getDevSyncTimeInfo(String ids, String type, String checkOffline) {
        return getDevIds(ids, type, checkOffline);
    }

    @RequiresPermissions("acc:device:uploadPersonInfo")
    @Override
    public ModelAndView getUploadPersonInfo(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            AccDeviceItem dev = accDeviceService.getItemById(ids);
            Map<String, String> devNameMap = new HashMap<>();
            StringBuffer disabledDevName = new StringBuffer("");
            StringBuffer offlineDevName = new StringBuffer("");
            StringBuffer supportDevFun = new StringBuffer("");
            StringBuffer retIds = new StringBuffer();
            // 判断设备是否在线
            if ((String.valueOf(AccConstants.DEV_STATE_ONLINE)).equals(accDeviceService.getStatus(dev.getSn()))) {
                if (dev.getParentDeviceId() != null) {
                    request.setAttribute("failedReason", AccConstants.DEV_NOT_SUPPORT_FEATURE);// 1代表设备不支持此操作
                    return new ModelAndView("acc/device/opDevNotSupportFeatureTips");
                }
                request.setAttribute("devicesName", dev.getDeviceName() + ":" + dev.getAlias());
                retIds.append(dev.getId());
                devNameMap.put(dev.getDeviceName(), dev.getDeviceName());
                // 生物识别新协议参数
                AccDeviceOptionItem bioDataSupport =
                    accDeviceOptionService.getDevOptValueBySnAndName(dev.getSn(), "MultiBioDataSupport");
                if (bioDataSupport != null) {
                    supportDevFun.append(bioDataSupport.getValue());
                } else {
                    // 老协议设备根据新协议参数形式构造判断参数
                    String authority = "0";
                    // 指纹
                    AccDeviceOptionItem fingerFunOn =
                        accDeviceOptionService.getDevOptValueBySnAndName(dev.getSn(), AccConstants.FINGER_FUN_ON);
                    AccDeviceOptionItem isOnlyRFMachine =
                        accDeviceOptionService.getDevOptValueBySnAndName(dev.getSn(), AccConstants.ONLY_RF_MACHINE);
                    if (fingerFunOn != null) {
                        authority += ":" + fingerFunOn.getValue();
                    } else if (isOnlyRFMachine != null) {
                        authority += "1".equals(isOnlyRFMachine.getValue()) ? ":0" : ":1";
                    }
                    // 近红外人脸
                    if (accDeviceOptionService.isSupportFun(dev.getSn(), AccConstants.BIOPHOTO_FUN_ON)) {
                        authority += ":0";
                    } else {
                        authority +=
                            accDeviceOptionService.isSupportFun(dev.getSn(), AccConstants.FACE_FUN_ON) ? ":1" : ":0";
                    }
                    // 此处代表模版一体化中的声纹:虹膜:视网膜:掌纹，由于之前设备都没有支持这些验证方式所以全部放0
                    authority += ":0:0:0:0";
                    // 指静脉
                    authority += accDeviceOptionService.isSupportFun(dev.getSn(), AccConstants.FV_FUN_ON) ? ":1" : ":0";
                    // 掌静脉
                    authority += accDeviceOptionService.isSupportFun(dev.getSn(), AccConstants.PV_FUN_ON) ? ":1" : ":0";
                    // 可见光人脸
                    authority +=
                        accDeviceOptionService.isSupportFun(dev.getSn(), AccConstants.BIOPHOTO_FUN_ON) ? ":1" : ":0";
                    supportDevFun.append(authority);
                }
            } else {
                if ((String.valueOf(AccConstants.DEV_STATE_DISABLE)).equals(accDeviceService.getStatus(dev.getSn()))) {
                    disabledDevName.append(dev.getAlias());
                } else {
                    offlineDevName.append(dev.getAlias());
                }
            }
            request.setAttribute("retIds", retIds.toString().equals("") ? "" : retIds);
            request.setAttribute("disabledDevName", disabledDevName.toString().equals("") ? "" : disabledDevName);
            request.setAttribute("offlineDevName", offlineDevName.toString().equals("") ? "" : offlineDevName);
            request.setAttribute("supportDevFun", supportDevFun.toString().equals("") ? "" : supportDevFun);
        }
        return new ModelAndView("acc/device/opAccUploadPersonInfo");
    }

    @RequiresPermissions("acc:device:uploadTransaction")
    @Override
    public ModelAndView getUploadTransactionInfo(String ids, String type, String checkOffline) {
        return getDevIds(ids, type, checkOffline);
    }

    private ModelAndView getDevIds(String ids, String type, String checkOffline) {
        if (StringUtils.isNotBlank(ids) && StringUtils.isNotBlank(type)) {
            List<AccDeviceItem> list = accDeviceService.getItemByIds(ids);
            Map<String, String> devNameMap = new HashMap<String, String>();
            StringBuffer disabledDevName = new StringBuffer("");
            StringBuffer warnInfoDevName = new StringBuffer("");
            StringBuffer offlineDevName = new StringBuffer("");
            StringBuffer supportDevFun = new StringBuffer("");
            AccDeviceItem dev = null;
            StringBuffer retIds = new StringBuffer();
            for (int i = 0; i < list.size(); i++) {
                dev = list.get(i);
                if ((String.valueOf(AccConstants.DEV_STATE_ONLINE)).equals(accDeviceService.getStatus(dev.getSn())))// 判断设备是否在线
                {
                    String name = dev.getDeviceName();
                    if (type.equals(AccConstants.UPLOAD_TRANSACTION)) {
                        retIds.append(dev.getId()).append(",");
                        if (!devNameMap.containsKey(name)) {
                            String isSupportAccount = "0";
                            devNameMap.put(name, name);
                            // TODO 暂时屏蔽新架构设备支持自动检查并获取记录功能 modify ********
                            // if (accDeviceService.isNewAccessControlDevice(dev.getMachineType(), dev.getDeviceName())
                            // || accDeviceOptionService.getAccSupportFunListVal(dev.getId(), 11))
                            // {
                            // isSupportAccount = "1";
                            // }
                            supportDevFun.append(name + ":" + isSupportAccount + ",");
                        }
                    }
                    // 同步时间，有父设备需要提示信息
                    else if ("syncTime".equals(type) && dev.getParentDeviceId() != null) {
                        warnInfoDevName.append(dev.getAlias()).append(",");
                    } else {
                        retIds.append(dev.getId()).append(",");
                        devNameMap.put(name, name);
                    }
                } else {
                    if ((String.valueOf(AccConstants.DEV_STATE_DISABLE))
                        .equals(accDeviceService.getStatus(dev.getSn()))) {
                        disabledDevName.append(dev.getAlias()).append(",");
                    } else {
                        if (StringUtils.isNotBlank(checkOffline)) {
                            String name = dev.getDeviceName();
                            devNameMap.put(name, name);
                        } else {
                            offlineDevName.append(dev.getAlias()).append(",");
                        }
                    }
                    list.remove(i);
                    i--;
                }
            }

            StringBuffer devName = new StringBuffer("");
            boolean isNeed = false;
            for (String key : devNameMap.keySet()) {
                devName.append(devNameMap.get(key)).append(":");
                for (int j = 0, innerLen = list.size(); j < innerLen; j++) {
                    if ((Pattern.compile(key).matcher(list.get(j).getDeviceName()).find()
                        || key.equals(list.get(j).getDeviceName()))
                        && (String.valueOf(AccConstants.DEV_STATE_ONLINE))
                            .equals(accDeviceService.getStatus(list.get(j).getSn()))) {
                        isNeed = true;
                        devName.append(list.get(j).getAlias() + "&" + list.get(j).getId()).append(",");
                    }
                }
                if (isNeed) {
                    devName.append(";");
                } else {
                    if (devName.indexOf(";") < 0) {
                        devName = new StringBuffer("");
                    } else {
                        devName.substring(0, devName.lastIndexOf(";") - 1);
                    }
                }
            }
            request.setAttribute("retIds",
                retIds.toString().equals("") ? "" : retIds.substring(0, retIds.length() - 1));
            request.setAttribute("devicesName",
                devName.toString().equals("") ? "" : devName.substring(0, devName.length() - 1));
            request.setAttribute("disabledDevName", disabledDevName.toString().equals("") ? ""
                : disabledDevName.substring(0, disabledDevName.length() - 1));
            request.setAttribute("offlineDevName",
                offlineDevName.toString().equals("") ? "" : offlineDevName.substring(0, offlineDevName.length() - 1));
            request.setAttribute("warnInfoDevName", warnInfoDevName.toString().equals("") ? ""
                : warnInfoDevName.substring(0, warnInfoDevName.length() - 1));
            request.setAttribute("supportDevFun", supportDevFun.toString().equals("") ? ""
                : supportDevFun.substring(0, supportDevFun.toString().length() - 1));
            if (StringUtils.isNotBlank(checkOffline)) {
                request.setAttribute("checkOffline", checkOffline);
            }
            switch (type) {
                case "uploadTransaction":
                    return new ModelAndView("acc/device/opAccUploadTransaction");
                case "syncTime":
                    return new ModelAndView("acc/device/opAccSyncTime");
            }
        }
        return null;
    }

    @RequiresPermissions("acc:device:issueBGVerifyParam")
    @Override
    public ModelAndView getDevIdsByIssueBgValid(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            List<AccDeviceItem> accDeviceItemList = accDeviceService.getItemByIds(ids);
            String offlineRule = "";
            StringBuffer open = new StringBuffer("open:");
            StringBuffer close = new StringBuffer("close:");
            StringBuffer idsBuffer = new StringBuffer();
            StringBuffer devNameBuffer = new StringBuffer();
            StringBuffer disabledDevName = new StringBuffer("");
            StringBuffer noSupportDevName = new StringBuffer("");
            StringBuffer offlineDevName = new StringBuffer("");
            for (AccDeviceItem dev : accDeviceItemList) {
                if (accDeviceOptionService.isSupportFun(dev.getSn(), "AutoServerFunOn")
                    && dev.getMachineType() != AccConstants.DEVICE_BIOIR_9000) {
                    if (accDeviceService.getStatus(dev.getSn())
                        .equals(String.valueOf(AccConstants.DEV_STATE_DISABLE))) {
                        disabledDevName.append(dev.getAlias() + ",");// 禁用
                    } else if (accDeviceService.getStatus(dev.getSn())
                        .equals(String.valueOf(AccConstants.DEV_STATE_ONLINE))) {// 在线
                        if (accDeviceOptionService.isSupportDevParam(dev.getSn(), "AutoServerMode")) {
                            close.append(dev.getAlias()).append("&").append(dev.getId()).append(",");// 已开启后台验证设备
                            AccDeviceOptionItem accDeviceOptionItem =
                                accDeviceOptionService.getDevOptValueBySnAndName(dev.getSn(), "ReaderOfflineRule");// 取出读头的离线规则用于前端页面离线规则显示回填
                            if (Objects.nonNull(accDeviceOptionItem) && "".equals(offlineRule)) {
                                offlineRule = accDeviceOptionItem.getValue();// 多设备只记录一次离线规则
                            }
                        } else {
                            open.append(dev.getAlias()).append("&").append(dev.getId()).append(",");// 未开启后台验证设备
                        }
                        idsBuffer.append(dev.getId()).append(",");
                        devNameBuffer.append(dev.getAlias()).append(",");
                    } else {
                        offlineDevName.append(dev.getAlias() + ",");// 离线设备
                    }
                } else {
                    noSupportDevName.append(dev.getAlias() + ",");// 不支持后台验证设备
                }
            }
            if (idsBuffer.length() > 0) {
                request.setAttribute("ids", idsBuffer.substring(0, idsBuffer.length() - 1).toString());
                request.setAttribute("alias", devNameBuffer.substring(0, devNameBuffer.length() - 1).toString());
            }

            String ret = "";
            if (open.length() > 6) {
                ret = open.substring(0, open.length() - 1) + ";" + close.substring(0, close.length() - 1);
            } else {
                ret = close.substring(0, close.length() - 1) + ";" + open.substring(0, open.length() - 1);
            }
            request.setAttribute("devicesName", ret);
            request.setAttribute("offlineRule", StringUtils.isNotBlank(offlineRule) ? offlineRule : "0");
            request.setAttribute("disabledDevName",
                disabledDevName.length() > 0 ? disabledDevName.substring(0, disabledDevName.length() - 1) : null);
            request.setAttribute("noSupportDevName",
                noSupportDevName.length() > 0 ? noSupportDevName.substring(0, noSupportDevName.length() - 1) : null);
            request.setAttribute("offlineDevName",
                offlineDevName.length() > 0 ? offlineDevName.substring(0, offlineDevName.length() - 1) : null);
            return new ModelAndView("acc/device/opAccIssueVerifyParam");
        }
        return null;
    }

    @RequiresPermissions("acc:device:issueBGVerifyParam")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "acc_dev_issueVerifyParam",
        requestParams = {"devName"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg issueBGVerifyParam(String devIds, String verifyParam, String offlineRule) {
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            List<AccDeviceItem> accDeviceItemList = new ArrayList<>();
            Map<Long, AccDeviceItem> accDeviceItemMap = new HashMap<>();
            if (StringUtils.isNotBlank(devIds)) {
                accDeviceItemList = accDeviceService.getItemByIds(devIds);
            }
            StringBuffer retCmdBuf = new StringBuffer();
            for (int i = 0; i < accDeviceItemList.size(); i++) {
                AccDeviceItem dev = accDeviceItemList.get(i);
                long ret = accDeviceService.issueVerifyParamToDev(dev.getId(), verifyParam, offlineRule, true);
                if (ret > 0) {
                    retCmdBuf.append(ret).append(",");
                    accDeviceItemMap.put(ret, dev);
                }
                int lastTotal = (i + 1) * (50 / accDeviceItemList.size());
                progressCache.setProcess(
                    new ProcessBean(30, lastTotal, dev.getAlias() + ":" + I18nUtil.i18nCode("common_dev_cmdSendSucceed")
                        + "<br/>" + I18nUtil.i18nCode("common_dev_waitResults") + "<br/>"));
            }

            // 判断是否执行成功
            String[] cmdIdArray = retCmdBuf.toString().split(",");
            int cmdIdArrayLen = cmdIdArray.length;
            AccDeviceItem devItem = null;
            for (int index = 0; index < cmdIdArrayLen; index++) {
                if (!cmdIdArray[index].equals("")) {
                    long cmdId = Long.parseLong(cmdIdArray[index]);
                    int i = 1;
                    int total = i * (50 / accDeviceItemList.size());
                    Map<String, String> reslutMap = accDeviceService.getCmdResultById(cmdId, 20);
                    if (Objects.nonNull(reslutMap)) {
                        Integer ret = Integer.parseInt(reslutMap.get("result"));
                        if (ret >= 0) {
                            devItem = accDeviceItemMap.get(cmdId);
                            progressCache.setProcess(new ProcessBean(100, total,
                                devItem.getAlias() + ":" + I18nUtil.i18nCode("acc_dev_verifyParamSuccess") + "<br/>"));
                            // //accDevOpBiz.getOptFromDev(accDevOpBiz.getDevParam(dev), ConstUtil.ACP_OPTIONS, 0);
                            // //下发成功后获取设备参数
                            // // for (int y = 1; y < 3; y++)
                            // // {
                            // // accDevOpBiz.getOptFromDev(dev, y, 180);//获取设备参数
                            // // }
                            accDeviceOptionService.setDevOptValByName(devItem.getId(), "AutoServerMode", verifyParam);// 更新后台验证参数
                            accDeviceOptionService.setDevOptValByName(devItem.getId(), "ReaderOfflineRule",
                                offlineRule);// 更新后台验证参数
                            // accDeviceOptionService.setDevOptValByName(devItem.getId(), "Reader1OfflineRefuse",
                            // offlineRule);//更新后台验证离线规则
                        } else {
                            // if (Objects.isNull(ret)) {
                            // progressCache.setProcess(new ProcessBean(100, total,
                            // I18nUtil.i18nCode("acc_dev_getInfoFail")));
                            // }
                            // else {
                            String failedInfo = accBaseDictionaryService.getCommReason(ret);
                            failedInfo = StringUtils.isNotBlank(failedInfo) ? I18nUtil.i18nCode(failedInfo)
                                : String.valueOf(ret);
                            progressCache
                                .setProcess(new ProcessBean(100, total, I18nUtil.i18nCode("acc_dev_getInfoFail") + ","
                                    + I18nUtil.i18nCode("common_dev_errorCode") + ":" + failedInfo + "</br>"));
                            // }
                        }
                    } else {
                        progressCache.setProcess(new ProcessBean(100, total, I18nUtil.i18nCode("acc_dev_getInfoFail")));
                    }
                    i++;
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
        return null;
    }

    @Override
    public ZKResultMsg checkCombOpenDoor(String devIds) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        if (StringUtils.isNotBlank(devIds)) {
            String devAlias = accDeviceService.checkCombOpenDoor(devIds);
            if (StringUtils.isNotBlank(devAlias)) {
                String msg = I18nUtil.i18nCode("acc_dev_combOpenDoorTip",
                    devAlias.length() > 0 ? devAlias.substring(0, devAlias.length() - 1) : "");// (**************)已设置多人开门，不能同时使用后台验证功能!
                resultMsg.setMsg(msg);
                resultMsg.setRet(AccConstants.DEV_HASBEENSET_COMBOPENDOOR);
            }
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public ZKResultMsg isOnline(String deviceId) {
        List<AccDeviceItem> accDeviceItemList = accDeviceService.getItemByIds(deviceId);
        StringBuffer offLineAlias = new StringBuffer("");
        StringBuffer offLineIds = new StringBuffer("");
        JSONObject retJson = new JSONObject();
        for (AccDeviceItem dev : accDeviceItemList) {
            if (accDeviceService.getStatus(dev.getSn()).equals(String.valueOf(AccConstants.DEV_STATE_OFFLINE))) {
                offLineIds.append(dev.getId()).append(",");
                offLineAlias.append(dev.getAlias()).append(",");
            }
        }
        retJson.put("ids",
            offLineIds.length() > 0 ? offLineIds.substring(0, offLineIds.length() - 1) : offLineIds.toString());
        retJson.put("alias",
            offLineAlias.length() > 0 ? offLineAlias.substring(0, offLineAlias.length() - 1) : offLineAlias.toString());
        return I18nUtil.i18nMsg(new ZKResultMsg(retJson));
    }

    @Override
    public boolean isExistIpAddress(String ipAddress) {
        return accDeviceService.isExistIpAddress(ipAddress);
    }

    @Override
    public boolean isExistAlias(String alias) {
        return accDeviceService.isExistAlias(alias);
    }

    @Override
    public boolean checkOperPwd(String operationPwd) {
        String checkPwd = DateUtil.dateToString(new Date(), DateUtil.DateStyle.YYYYMMDD);
        String key = accParamService.getParamValByName("acc.addDeviceOperKeyword");
        if ((encryKey(key) + sortStr(checkPwd)).equals(operationPwd)) {
            request.getSession().setAttribute("operationPwd", operationPwd);
            return true;
        } else {
            request.getSession().removeAttribute("operationPwd");
            return false;
        }
    }

    private String encryKey(String key) {
        StringBuffer sb = new StringBuffer("");
        if (!"xxx".equals(key)) {
            String s = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
            int index = 0;
            for (int i = 0; i < key.length(); i++) {
                index = s.indexOf(key.charAt(i));
                sb.append((index + 1) % 10);
            }
        }
        return sb.toString();
    }

    private String sortStr(String str) {
        char[] s1 = str.toCharArray();
        for (int i = 0; i < s1.length; i++) {
            for (int j = 0; j < i; j++) {
                if (s1[i] < s1[j]) {
                    char temp = s1[i];
                    s1[i] = s1[j];
                    s1[j] = temp;
                }
            }
        }
        return String.valueOf(s1);
    }

    @RequiresPermissions("acc:device:upgradeFirmware")
    @Override
    public ZKResultMsg upgradeFirmware(String devIds, MultipartFile devFile, String upgradeType, String devRadio) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        if (StringUtils.isNotBlank(devIds) && StringUtils.isNotBlank(upgradeType)) {
            String ret = "ok";
            try {
                progressCache.beginProcess(I18nUtil.i18nCode("common_op_startProcessing") + "<br/>");
                List<AccDeviceItem> deviceItemList = accDeviceService.getItemByIds(devIds);
                progressCache.setProcess(new ProcessBean(100, 0,
                    "<font>" + I18nUtil.i18nCode("acc_dev_transferFilesTip") + "...</font><br/>"));// 第二次表单提交会刷新进度条，故这里再加入提示。
                File filePath = null;
                if (devFile.getName() != null) {
                    if (devFile != null) {
                        String fileName = "";
                        switch (upgradeType) {
                            case "device":
                                fileName = DEV_UPGRADEFIRMWARE_FILENAME;
                                break;

                            case "reader":
                                fileName = READER_UPGRADEFIRMWARE_FILENAME;
                                break;
                        }
                        filePath = saveFile(devFile, fileName);
                    }
                }
                progressCache.setProcess(new ProcessBean(100, 45,
                    "<font>" + I18nUtil.i18nCode("common_dev_fileUploadSuccess") + "...</font><br/>"));// 数据处理进度初始化
                List<Long> cmdIdList = accDeviceService.upgradeFirmware(devIds, filePath, "localhost", serverPort);
                progressCache
                    .setProcess(new ProcessBean(100, 50, "<font>" + I18nUtil.i18nCode("common_dev_cmdSendSucceed") + ","
                        + I18nUtil.i18nCode("common_dev_upgrade") + "...</font><br/>"));
                // 如果不是升级设备固件则底下的重启获取参数流程不用走，目前除了升级设备固件还有升级读头固件
                if (StringUtils.isNotBlank(upgradeType) && !upgradeType.equals("device")) {
                    return I18nUtil.i18nMsg(resultMsg);
                }
                // 判断是否执行成功
                List<AccDeviceItem> successDevList = new ArrayList<>();
                List<String> devIdList = new ArrayList<>();// 执行成功需要重启的设备ID lsit
                for (int index = 0; index < cmdIdList.size(); index++) {
                    long cmdId = cmdIdList.get(index);
                    Map<String, String> resultMap = accDeviceService.getCmdResultById(cmdId, 1800);
                    if (Objects.nonNull(resultMap)) {
                        int total = (50 / deviceItemList.size()) * (index + 1) + 30;
                        Integer result = Integer.parseInt(resultMap.get("result"));
                        AccDeviceItem dev = accDeviceService.getItemByDevSn(resultMap.get("sn"));
                        if (result >= 0) {
                            progressCache.setProcess(new ProcessBean(70, total,
                                "<font>" + dev.getAlias() + ":" + I18nUtil.i18nCode("common_dev_upgradeSuccess") + ","
                                    + I18nUtil.i18nCode("common_dev_reboot") + "...</font><br/>"));
                            AccDeviceItem accDev = deviceItemList.get(index);
                            // 有父设备，但其实命令结果是通过父设备进行返回的。 by juvenile.li add 20170929
                            if (StringUtils.isNotBlank(accDev.getParentDeviceId())) {
                                successDevList.add(accDev);
                                devIdList.add(accDev.getId());
                            } else {
                                successDevList.add(dev);
                                devIdList.add(dev.getId());
                            }
                        } else {
                            String failedInfo = accBaseDictionaryService.getCommReason(result);
                            failedInfo =
                                StringUtils.isNotBlank(failedInfo) ? I18nUtil.i18nCode(failedInfo) : result.toString();
                            progressCache.setProcess(new ProcessBean(total + 20, total + 20,
                                "<font class='zk-msg-error'>" + dev.getAlias() + ":"
                                    + I18nUtil.i18nCode("common_dev_upgradeFail") + ","
                                    + I18nUtil.i18nCode("common_dev_errorCode") + ":" + failedInfo
                                    + "...</font><br/>"));
                        }
                    }
                }
                if (devIdList.size() > 0) {// 执行成功重启设备
                    accDeviceService.rebootDevice(devIdList, true);
                }
                if (successDevList.size() > 0) {
                    int count = defWaitCount;
                    for (int i = 1; i <= count; i++) { // 让进度条动起来...
                        if (i == maxWaitCount) { // 最大循环次数
                            progressCache
                                .setProcess(new ProcessBean(70 + i, 70 + i, "<font>" + "..." + "</font><br/>"));
                            break;
                        }
                        if (i == 1) {
                            progressCache.setProcess(new ProcessBean(70 + i, 70 + i, "<font>"
                                + I18nUtil.i18nCode("common_devOpType_connect") + "..." + "</font>" + "<br/>"));
                            progressCache.setProcess(new ProcessBean(70 + i, 70 + i,
                                I18nUtil.i18nCode("common_dev_dealTimeout",
                                    (maxWaitCount * 15000) / (60 * 1000) + I18nUtil.i18nCode("common_minutes"))
                                    + "<br/>"));
                        } else {
                            progressCache.setProcess(new ProcessBean(70 + i, 70 + i));
                            if (Short.parseShort(accDeviceService
                                .getStatus(deviceItemList.get(0).getSn())) != ConstUtil.DEV_STATE_ONLINE) {
                                count += 1;
                            }
                        }
                        Thread.sleep(15000);// 保证设备有足够的时间重启
                    }
                    // 重新获取设备所有参数
                    for (AccDeviceItem dev : successDevList) {
                        progressCache.setProcess(new ProcessBean(85, 85, "<font>" + dev.getAlias() + ":"
                            + I18nUtil.i18nCode("common_devOpType_dataUpdate") + "...</font><br/>"));
                        int optionCount = 3;// 参数遍历次数
                        if (accDeviceService.isNewAccessControlDevice(dev.getMachineType(), dev.getDeviceName())) {
                            optionCount = 4;// 参数遍历次数，如果是一体机需要遍历额外的参数
                        }
                        for (int i = 1; i < optionCount; i++) {
                            accDeviceService.getOptFromDev(dev.getSn(), i);
                        }
                        progressCache.setProcess(new ProcessBean(90, 90, "<font>" + dev.getAlias() + ":"
                            + I18nUtil.i18nCode("common_dev_upgradeSuccess") + "</font><br/>"));
                    }
                }
            } catch (Exception e) {
                ret = "error";
                resultMsg.setRet(ret);
                resultMsg.setMsg("common_dev_upgradeFail");
                log.error("exception ", e);
            } finally {
                if (ret.equals("error")) {
                    progressCache.finishProcess("<font class='zk-msg-error'>"
                        + I18nUtil.i18nCode("common_dev_upgradeFail") + "...</font><br/>");
                } else {
                    progressCache
                        .finishProcess("<font>" + I18nUtil.i18nCode("common_progress_finish") + "</font><br/>");
                }
            }
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    /**
     * @Description: 保存设备固件升级文件
     * <AUTHOR>
     * @date 2018/8/29 16:35
     * @param devFile
     * @param fileName
     * @return
     */
    private File saveFile(MultipartFile devFile, String fileName) throws Exception {
        String realPath = systemFilePath + "/upload/acc/firmware";
        File tempFile = new File(realPath);
        if (!tempFile.isAbsolute()) {
            realPath = ClassUtil.getRootPath() + "/" + realPath;
            tempFile = new File(realPath);
        }
        if (!tempFile.exists()) {
            tempFile.mkdirs();
        }
        File file = new File(realPath + "/" + fileName);
        if (file.exists()) {
            file.delete();
        }
        devFile.transferTo(file);
        return file;
    }

    @RequiresPermissions("acc:device:uploadPersonInfo")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "common_dev_getPersonInfo",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg uploadPersonInfo(String devIds, String dataType, String tempClientId) {
        // 当设备处于wifi 情况下，并且客户端为火狐浏览器访问时，ajaxsubmit可能会重复提交多次请求。过滤掉 add By juvenile.li 20170510
        if (!tempClientId.equals(request.getSession().getAttribute("beforeClientId"))) {
            request.getSession().setAttribute("beforeClientId", tempClientId);
            if (dataType.equals("0")) {
                getPersonInfoFromDev(devIds);
            } else {
                uploadPersonInfoFromDev(devIds, dataType);// dataType 0是获取人员信息，1是获取指纹信息,2是指获取面部,3指静脉
            }
        }
        return null;
    }

    private void uploadPersonInfoFromDev(String devIds, String dataType) {
        List<String> devIdList = Lists.newArrayList(devIds.split(","));
        progressCache.beginProcess(I18nUtil.i18nCode("common_op_startProcessing") + "...<br/>");
        List<String> cmdIdList = Lists.newArrayList();
        String devId = null;
        // Map<String, String> devSnMap = Maps.newHashMap();
        Map<String, String> dataMap = null;
        int total = 0;
        for (int i = 0, devSize = devIdList.size(); i < devSize; i++) {
            devId = devIdList.get(i);
            dataMap = accDeviceService.getPersonInfoFromDev(devId, dataType);
            if (dataMap.containsKey("cmdIds") && !dataMap.get("cmdIds").equals("")) {
                String cmdIds = dataMap.get("cmdIds");
                String[] cmdIdArray = cmdIds.split(",");
                for (String cmdId : cmdIdArray) {
                    cmdIdList.add(cmdId);// cmdId
                    // devSnMap.put(array.split("_")[0], array.split("_")[1]);//cmdId - devSn
                }
            }
            progressCache.setProcess(new ProcessBean(100, total,
                dataMap.get("devName") + I18nUtil.i18nCode("common_dev_cmdSendSucceed") + "<br/>"));
        }
        List<String> cmdIdArray = Lists.newArrayList(cmdIdList);
        // 开始处理后台数据 将进度等待超时设置成40分钟，40分钟内没处理完毕，可能提示处理等待超时 modified by max 20161011
        int ret = dealQueryDataFromRedis(cmdIdArray, 1800000, progressCache, false, false); // 还未确定时间
        if (ret == 0) {
            for (String cmdId : cmdIdList) {
                String key = AccCacheKeyConstants.QUERY_ACC_CONFLICT_CARD_CMDID + cmdId;
                String conflictCard = accDeviceService.getQueryData(key);
                if (!"".equals(conflictCard)) {
                    String msg = I18nUtil.i18nCode("acc_dev_conflictCardNo", conflictCard);
                    progressCache
                        .setProcess(new ProcessBean(100, 99, "<font class='zk-msg-error'>" + msg + "</font><br/>"));
                }
            }
            progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish"));
        } else if (ret == -1) {
            // 处理异常
            progressCache.finishProcess(
                "<font class='zk-msg-error'>" + I18nUtil.i18nCode("common_progress_getDataFailed") + "...</font><br/>");
        } else if (ret == 1) {
            // 服务器繁忙，等待反馈结果超时
            progressCache.finishProcess(
                "<font class='zk-msg-warn'>" + I18nUtil.i18nCode("common_progress_busyFailed") + "</font><br/>");
        }
    }

    /**
     * 获取人员信息前端处理
     *
     * @param devId
     */
    private void getPersonInfoFromDev(String devId) {
        progressCache.beginProcess(I18nUtil.i18nCode("common_op_startProcessing") + "...<br/>");
        AccDeviceItem accDeviceItem = accDeviceService.getItemById(devId);
        // 命令数量用于计算进度条
        int cmdCount = 1;
        // 是否支持extuser表
        if (accDeviceOptionService.isSupportDevParam(accDeviceItem.getSn(), "UserOpenDoorDelayFunOn")
            || accDeviceOptionService.isSupportFunList(accDeviceItem.getSn(), 5)) {
            cmdCount++;
        }
        // 是否支持多卡表
        if (accDeviceOptionService.isSupportFunList(accDeviceItem.getSn(), 7)) {
            cmdCount++;
        }
        // 用于计算整个过程中各部分命令所占进度条百分比
        int everyProcess = 100 / cmdCount;
        // 用于计算进度条进度
        int currentProcess = 0;
        int ret = 0;
        Long cmdId = accDeviceService.getAllPersonFromDev(accDeviceItem);
        progressCache
            .setProcess(new ProcessBean(100, currentProcess, I18nUtil.i18nCode("common_dev_cmdSendSucceed") + "<br/>"));
        ret = dealQueryDataFromRedis(cmdId, 900000, progressCache, currentProcess, currentProcess + everyProcess);
        if (ret == 0) {
            cmdId = accDeviceService.getAllPersonCardFromDev(accDeviceItem);
            if (cmdId != -1L) {
                progressCache.setProcess(
                    new ProcessBean(100, currentProcess, I18nUtil.i18nCode("common_dev_cmdSendSucceed") + "<br/>"));
                currentProcess += everyProcess;
                dealQueryDataFromRedis(cmdId, 900000, progressCache, currentProcess, currentProcess + everyProcess);
            }
            cmdId = accDeviceService.getAllPersonExtInfoFromDev(accDeviceItem);
            if (cmdId != -1L) {
                progressCache.setProcess(
                    new ProcessBean(100, currentProcess, I18nUtil.i18nCode("common_dev_cmdSendSucceed") + "<br/>"));
                currentProcess += everyProcess;
                dealQueryDataFromRedis(cmdId, 1800000, progressCache, currentProcess, currentProcess + everyProcess);
            }
        }
        progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish"));
    }

    /**
     * 根据返回值处理情况计算进度条
     *
     * @param cmdId
     * @param timeout
     * @param dealRate
     * @param currentProgress
     * @param maxProgress
     */
    private int dealQueryDataFromRedis(Long cmdId, int timeout, ProgressCache dealRate, int currentProgress,
        int maxProgress) {
        int ret = 0;
        int time = timeout;
        dealRate.setProcess(new ProcessBean(100, currentProgress, I18nUtil.i18nCode("common_op_processing") + "<br/>"));
        try {
            while (time > 0) {
                int process = 0;
                String key = AccCacheKeyConstants.QUERY_DATA + cmdId;
                String queryData = accDeviceService.getQueryData(key);
                if (!"".equals(queryData)) {
                    JSONObject tempJson = JSONObject.parseObject(queryData);
                    AccQueryDeviceItem dev = accDeviceService.getQueryItemBySn(tempJson.getString("sn"));// 从缓存取设备信息
                    if (tempJson.containsKey("exception")) {
                        // 异常直接100% 结束 everyProcess * (cmdLen - cmdIdList.size())
                        dealRate.setProcess(new ProcessBean(100, maxProgress,
                            "<font class='zk-msg-error'>" + I18nUtil.i18nCode("acc_dev_getInfoFail") + "</font><br/>"
                                + "<font class='zk-msg-error'>" + I18nUtil.i18nCode(tempJson.getString("exception"))
                                + "</font><br/>"));
                        accDeviceService.delQueryData(key);
                        break;
                    } else if (tempJson.getIntValue("packIdx") <= tempJson.getIntValue("packCnt")) {
                        if (tempJson.getIntValue("packCnt") != 0) {
                            process = ((maxProgress - currentProgress) * tempJson.getIntValue("packIdx")
                                / tempJson.getIntValue("packCnt"));
                        }
                        process += currentProgress;
                        // 避免超过当前过程的最大进度
                        process = (process >= maxProgress ? maxProgress - 1 : process);
                        dealRate.setProcess(new ProcessBean(100, process));
                        if (tempJson.getIntValue("packCnt") == tempJson.getIntValue("packIdx")) {// 表示该命令全局处理完成
                            String tableDesc = "";
                            if (tempJson.containsKey("table")) {
                                tableDesc = StringUtils.defaultString(I18nUtil.i18nCode(accBaseDictionaryService
                                    .getBaseDictionaryMap("opType").get(tempJson.getString("table"))), "");
                            }

                            StringBuffer desc = new StringBuffer("");
                            if (tempJson.getIntValue("packCnt") == 0) {
                                desc.append(dev.getAlias())
                                    .append(":" + I18nUtil.i18nCode("acc_dev_getInfoXSuccess", tableDesc) + "<br/>")
                                    .append(" " + I18nUtil.i18nCode("acc_dev_noData") + "<br/>");

                            } else if (Long.parseLong(tempJson.getString("count")) == 0) {// 非account命令没数据的时候packCnt=1，count=0
                                desc.append(dev.getAlias())
                                    .append(":" + I18nUtil.i18nCode("acc_dev_getInfoXSuccess", tableDesc) + "<br/>");
                                if (tempJson.getString("table").equals("transaction")) {// 旧设备获取新记录的时候会出现packCnt=1但是count=0的情况，需要特殊处理
                                    desc.append(" " + I18nUtil.i18nCode("acc_dev_noNewData") + "<br/>");
                                } else {
                                    desc.append(" " + I18nUtil.i18nCode("acc_dev_noData") + "<br/>");
                                }
                            } else {
                                desc.append(dev.getAlias())
                                    .append(":" + I18nUtil.i18nCode("acc_dev_getInfoXSuccess", tableDesc) + "<br/>")
                                    .append(I18nUtil.i18nCode("common_dev_getTotal") + " "
                                        + Long.valueOf(tempJson.getString("count")))
                                    .append(" " + I18nUtil.i18nCode("common_dev_record"))
                                    .append("," + I18nUtil.i18nCode("common_dev_duplicateData"))
                                    .append(" " + (Long.parseLong(tempJson.getString("count"))
                                        - Long.parseLong(tempJson.getString("update"))
                                        - Long.parseLong(tempJson.getString("insert"))))
                                    .append(" " + I18nUtil.i18nCode("common_dev_record") + ",")
                                    .append(I18nUtil.i18nCode("common_dev_insertData") + " "
                                        + Long.valueOf(tempJson.getString("insert")) + " ")
                                    .append(I18nUtil.i18nCode("common_dev_record") + ",")
                                    .append(I18nUtil.i18nCode("common_dev_updateData") + " "
                                        + Long.valueOf(tempJson.getString("update")) + " ")
                                    .append(I18nUtil.i18nCode("common_dev_record") + "<br/>");
                            }
                            dealRate.setProcess(new ProcessBean(100, maxProgress, desc.toString()));
                            accDeviceService.delQueryData(key);
                            break;
                        }
                    }
                }
                Thread.sleep(4000);
                time -= 4000;
            }
            if (time <= 0) {
                // 处理等待超时
                ret = 1;
                dealRate.setProcess(new ProcessBean(100, maxProgress,
                    "<font class='zk-msg-warn'>" + I18nUtil.i18nCode("common_progress_busyFailed") + "</font><br/>"));
            }
        } catch (Exception e) {
            ret = -1;
            dealRate.setProcess(new ProcessBean(100, maxProgress, "<font class='zk-msg-error'>"
                + I18nUtil.i18nCode("common_progress_getDataFailed") + "...</font><br/>"));
            log.error("exception ", e);
        }
        return ret;
    }

    private int dealQueryDataFromRedis(List<String> cmdIdList, int timeout, ProgressCache dealRate, boolean newLog,
        boolean isAccount) {
        int ret = 0;
        String key = null;
        AccQueryDeviceItem dev = null;
        int cmdLen = cmdIdList.size();
        int time = timeout;
        try {
            if (isAccount) {
                accDeviceService.dealAccountDataFromRedis(cmdIdList);
            }
            // 新增一个数据数据处理中提示
            dealRate.setProcess(new ProcessBean(100, 0, I18nUtil.i18nCode("common_op_processing") + "<br/>"));
            // 分块，计算进度条
            int everyProcess = 100 / cmdLen;
            while (time > 0) {
                if (cmdIdList.size() == 0) {
                    break;// 表示在规定时间内命令全部执行完成跳出轮询
                }
                for (int j = 0; j < cmdIdList.size(); j++) {
                    key = AccCacheKeyConstants.QUERY_DATA + cmdIdList.get(j);
                    String queryData = accDeviceService.getQueryData(key);
                    if (!"".equals(queryData)) {
                        JSONObject tempJson = JSONObject.parseObject(queryData);
                        dev = accDeviceService.getQueryItemBySn(tempJson.getString("sn"));// 从缓存取设备信息
                        int process = everyProcess * (cmdLen - cmdIdList.size());
                        if (dev != null) {
                            if (tempJson.containsKey("exception")) {
                                // 异常直接100% 结束 everyProcess * (cmdLen - cmdIdList.size())
                                dealRate.setProcess(new ProcessBean(100, 99,
                                    "<font class='zk-msg-error'>" + dev.getAlias() + ":"
                                        + I18nUtil.i18nCode("acc_dev_getInfoFail") + "</font><br/>"
                                        + "<font class='zk-msg-error'>" + dev.getAlias() + ":"
                                        + I18nUtil.i18nCode(tempJson.getString("exception")) + "</font><br/>"));
                                accDeviceService.delQueryData(key);
                                cmdIdList.remove(j);
                            } else if (tempJson.getIntValue("packIdx") <= tempJson.getIntValue("packCnt")) {

                                if (tempJson.getIntValue("packCnt") != 0) {
                                    process = (everyProcess * (cmdLen - cmdIdList.size())) + (everyProcess
                                        * tempJson.getIntValue("packIdx") / tempJson.getIntValue("packCnt"));
                                }
                                // 避免内部进度直接到达100，最终提示信息错过问题；modified by max 20161102
                                process = (process == 100 ? process - 1 : process);
                                dealRate.setProcess(new ProcessBean(100, process));
                                if (tempJson.getIntValue("packCnt") == tempJson.getIntValue("packIdx")) {// 表示该命令全局处理完成
                                    String tableDesc = "";
                                    if (tempJson.containsKey("table")) {
                                        tableDesc =
                                            StringUtils.defaultString(
                                                I18nUtil.i18nCode(accBaseDictionaryService
                                                    .getBaseDictionaryMap("opType").get(tempJson.getString("table"))),
                                                "");
                                    }

                                    StringBuffer desc = new StringBuffer("");
                                    if (tempJson.getIntValue("packCnt") == 0) {
                                        desc.append(dev.getAlias())
                                            .append(
                                                ":" + I18nUtil.i18nCode("acc_dev_getInfoXSuccess", tableDesc) + "<br/>")
                                            .append(" " + I18nUtil.i18nCode("acc_dev_noData") + "<br/>");

                                    } else if (Long.parseLong(tempJson.getString("count")) == 0) {// 非account命令没数据的时候packCnt=1，count=0
                                        desc.append(dev.getAlias()).append(
                                            ":" + I18nUtil.i18nCode("acc_dev_getInfoXSuccess", tableDesc) + "<br/>");
                                        if (tempJson.getString("table").equals("transaction")) {// 旧设备获取新记录的时候会出现packCnt=1但是count=0的情况，需要特殊处理
                                            desc.append(" " + I18nUtil.i18nCode("acc_dev_noNewData") + "<br/>");
                                        } else {
                                            desc.append(" " + I18nUtil.i18nCode("acc_dev_noData") + "<br/>");
                                        }
                                    } else {
                                        desc.append(dev.getAlias())
                                            .append(
                                                ":" + I18nUtil.i18nCode("acc_dev_getInfoXSuccess", tableDesc) + "<br/>")
                                            .append(I18nUtil.i18nCode("common_dev_getTotal") + " "
                                                + Long.valueOf(tempJson.getString("count")))
                                            .append(" " + I18nUtil.i18nCode("common_dev_record"))
                                            .append("," + I18nUtil.i18nCode("common_dev_duplicateData"))
                                            .append(" " + (Long.parseLong(tempJson.getString("count"))
                                                - Long.parseLong(tempJson.getString("update"))
                                                - Long.parseLong(tempJson.getString("insert"))))
                                            .append(" " + I18nUtil.i18nCode("common_dev_record") + ",")
                                            .append(I18nUtil.i18nCode("common_dev_insertData") + " "
                                                + Long.valueOf(tempJson.getString("insert")) + " ")
                                            .append(I18nUtil.i18nCode("common_dev_record") + ",")
                                            .append(I18nUtil.i18nCode("common_dev_updateData") + " "
                                                + Long.valueOf(tempJson.getString("update")) + " ")
                                            .append(I18nUtil.i18nCode("common_dev_record") + "<br/>");
                                    }
                                    dealRate.setProcess(new ProcessBean(100, process, desc.toString()));
                                    accDeviceService.delQueryData(key);
                                    cmdIdList.remove(j); // 删除已经有返回值的设备
                                }
                            }
                        } else {
                            dealRate.setProcess(new ProcessBean(100, process,
                                "<font class='zk-msg-error'>" + I18nUtil.i18nCode("common_dev_notExistDev") + ", SN="
                                    + tempJson.getString("sn") + "</font><br/>"));
                            accDeviceService.delQueryData(key);
                            cmdIdList.remove(j); // 删除已经有返回值的设备
                        }
                    }
                }
                Thread.sleep(4000);
                time -= 4000;
            }
            // 规定时间内没得到处理结果 add by max 20161011
            if (time <= 0) {
                // 处理等待超时
                ret = 1;
            }
        } catch (Exception e) {
            ret = -1;
            log.error("exception ", e);
        }

        return ret;
    }

    @RequiresPermissions("acc:device:uploadTransaction")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "common_dev_getTrans",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg uploadTransaction(AccDeviceUploadTransactionItem item) {
        boolean newLog = AccConstants.NEW_RECORDS.equals(item.getRecords());
        // List<AccDeviceItem> accDeviceItemList = accDeviceService.getItemByIds(devIds);
        progressCache.beginProcess(I18nUtil.i18nCode("common_op_startProcessing") + "...<br/>");
        List<String> cmdIdList = new ArrayList<>();
        int total = 0;
        // 是否支持身份登记设备
        if (accDeviceOptionService.getAccSupportFunListVal(item.getDevId(), 30)) {
            accDeviceService.getIdentityCardInfo(item.getDevId());
        }
        Map<String, String> devDataMap = accDeviceService.uploadTransaction(item);
        cmdIdList.addAll(Lists.newArrayList(devDataMap.get("cmdId").split(",")));
        progressCache.setProcess(new ProcessBean(100, total,
            devDataMap.get("alias") + ":" + I18nUtil.i18nCode("common_dev_cmdSendSucceed") + "<br/>"));
        // 开始处理后台数据,改成一个小时的超时 因为设备支持了50w记录了 modified by max 20151019
        int ret = dealQueryDataFromRedis(cmdIdList, 3600000, progressCache, newLog,
            AccConstants.CHECK_NEW_RECORDS.equals(item.getRecords()));
        if (ret == 0) {
            // retStr = SUCCESS;
            progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish"));
        } else if (ret == -1) {
            // 处理异常
            progressCache.finishProcess("<font class='zk-msg-error'>"
                + I18nUtil.i18nCode("common_commStatus_getEventFailed") + "...</font><br/>");
        } else if (ret == 1) {
            // 服务器繁忙，等待反馈结果超时
            progressCache.finishProcess(
                "<font class='zk-msg-warn'>" + I18nUtil.i18nCode("common_progress_busyFailed") + "</font><br/>");
        }
        return null;
    }

    @RequiresPermissions("acc:device:syncTime")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "common_dev_syncTime",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg syncTime(String devIds) {
        if (StringUtils.isNotBlank(devIds)) {
            List<AccDeviceItem> accDeviceItemList = accDeviceService.getItemByIds(devIds);
            if (accDeviceItemList != null && !accDeviceItemList.isEmpty()) {
                progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
                StringBuffer cmdIdBuf = new StringBuffer();
                Map<Long, String> devDataMap = Maps.newHashMap();
                AccDeviceItem dev = null;
                for (int i = 0, len = accDeviceItemList.size(); i < len; i++) {
                    dev = accDeviceItemList.get(i);
                    int total = (int)(((i + 1) * 0.5) / (float)accDeviceItemList.size() * 100);
                    long cmdId = accDeviceService.syncTime(dev.getSn());
                    cmdIdBuf.append(cmdId).append(",");
                    devDataMap.put(cmdId, dev.getAlias());
                    progressCache.setProcess(new ProcessBean(100, total,
                        dev.getAlias() + " : " + I18nUtil.i18nCode("common_dev_cmdSendSucceed") + "...<br/>"));
                }
                progressCache
                    .setProcess(new ProcessBean(10, 50, I18nUtil.i18nCode("common_dev_syncTime") + "...<br/>"));
                int j = 1;
                String[] cmdIdArray = cmdIdBuf.toString().split(",");
                int cmdIdArrayLen = cmdIdArray.length;
                for (int index = 0; index < cmdIdArrayLen; index++) {
                    if (!cmdIdArray[index].equals("") && Long.parseLong(cmdIdArray[index]) > 0) {
                        long cmdId = Long.parseLong(cmdIdArray[index]);
                        int total = (int)((j * 0.5 + 1) / (float)accDeviceItemList.size() * 100);
                        Map<String, String> resultMap = accDeviceService.getCmdResultById(cmdId, 20);
                        String alias = devDataMap.get(cmdId);
                        if (Objects.nonNull(resultMap)) {
                            Integer ret = Integer.valueOf(resultMap.get("result"));
                            if (ret >= 0) {
                                progressCache.setProcess(new ProcessBean(100, total,
                                    alias + ":" + I18nUtil.i18nCode("common_dev_syncTimeSuccess") + "...<br/>"));
                            } else {
                                String failedInfo = StringUtils.isNotBlank(accBaseDictionaryService.getCommReason(ret))
                                    ? I18nUtil.i18nCode(accBaseDictionaryService.getCommReason(ret)) : ret.toString();
                                progressCache.setProcess(new ProcessBean(100, total,
                                    alias + ":" + I18nUtil.i18nCode("common_dev_syncTimeFail") + ","
                                        + I18nUtil.i18nCode("common_dev_errorCode") + ":" + failedInfo + "...<br/>"));
                            }
                        } else {
                            progressCache.setProcess(new ProcessBean(100, total,
                                alias + ":" + I18nUtil.i18nCode("common_dev_syncTimeFail") + "...<br/>"));
                        }
                        j++;
                    }
                }
                progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish"));
            }
        }
        return null;
    }

    @Override
    public ZKResultMsg isExistsIp(String id, String ipAddress) {
        boolean isExist = true;
        ZKResultMsg resultMsg = new ZKResultMsg();
        if (StringUtils.isNotBlank(id)) {
            isExist = accDeviceService.isExistsIp(id, ipAddress);
        }
        resultMsg.setData(isExist);
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public ZKResultMsg isExistsSecondIp(String id, String newIp) {
        boolean isExist = true;
        ZKResultMsg resultMsg = new ZKResultMsg();
        if (StringUtils.isNotBlank(id)) {
            isExist = accDeviceService.isExistsSecondIp(id, newIp);
        }
        resultMsg.setData(isExist);
        return I18nUtil.i18nMsg(resultMsg);
    }

    @RequiresPermissions("acc:device:updateIpAddr")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "common_dev_modifyIPAddress",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg updateIpAddr(String devId, String ipAddress, String subnetMask, String gateway,
        String ipAddressSec, String netMaskSec, String gateIPAddressSec) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        String msgStr = I18nUtil.i18nCode("common_op_failed");
        Integer ret = null;
        int result = -1;
        AccDeviceItem device = accDeviceService.getItemById(devId);
        // 支持双网卡，需要设置第二网卡的IP 这种情况需要先改第二网卡ip，改第一网卡会导致push重连
        if (accDeviceOptionService.getAccSupportFunListVal(devId, 28)) {
            Long cmdId = accDeviceService.updateIpAddrEx(devId, ipAddressSec, netMaskSec, gateIPAddressSec, ""); // 修改设备扩展网卡ip参数
            if (cmdId != null && cmdId > 0) {
                Map<String, String> resultMap = accDeviceService.getCmdResultById(cmdId, 20);// 获取命令返回值
                if (Objects.nonNull(resultMap)) {
                    ret = Integer.valueOf(resultMap.get("result"));
                    if (ret >= 0) { // 成功修改设备ip信息
                        accDeviceOptionService.setDevOptValByName(devId, "IPAddress1", ipAddressSec);
                        accDeviceOptionService.setDevOptValByName(devId, "GATEIPAddress1", gateIPAddressSec);
                        accDeviceOptionService.setDevOptValByName(devId, "NetMask1", netMaskSec);
                        if (device.getMachineType() == AccConstants.DEVICE_BIOIR_9000) {
                            accDeviceOptionService.setDevOptValByName(devId, "ServerCommIP", ipAddressSec);
                        }
                    } else {
                        String failedInfo = StringUtils.isNotBlank(accBaseDictionaryService.getCommReason(ret))
                            ? I18nUtil.i18nCode(accBaseDictionaryService.getCommReason(ret)) : ret.toString();
                        msgStr = I18nUtil.i18nCode("common_op_failed") + I18nUtil.i18nCode("common_dev_errorCode") + ":"
                            + failedInfo;
                        throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_ERROR, msgStr);
                    }
                }
            }
        }
        Long updateIpAddrCmdId = accDeviceService.updateIpAddr(devId, ipAddress, subnetMask, gateway);
        if (updateIpAddrCmdId != null && updateIpAddrCmdId > 0) {
            Map<String, String> resultMap = accDeviceService.getCmdResultById(updateIpAddrCmdId, 20);// 获取命令返回值
            if (Objects.nonNull(resultMap)) {
                result = Integer.parseInt(resultMap.get("result"));
            }
        }
        if (result >= 0) { // 成功修改设备ip信息
            device.setIpAddress(ipAddress);
            device.setSubnetMask(subnetMask);
            device.setGateway(gateway);
            accDeviceService.saveAccDevice(device);
            accDeviceService.updateAdmsDevIpAddr(device.getSn(), ipAddress, subnetMask, gateway);
            accDeviceService.updateDevInfoWithDoorAndAuxBySn(device.getSn());
            // 更新adms设备参数，修复admsDeviceService initCacheInfo方法重置缓存,导致服务重启pull设备连接失败
            accDeviceService.updateAdmsDevOption(device);
            // 更新设备ip地址到其他模块
            accDeviceService.updateDevIpAddrForOtherModule(device.getSn(), ipAddress);
        } else {
            throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_ERROR, "common_op_failed");
        }
        accDeviceService.rebootDevice(Lists.newArrayList(devId), false);// 如果IP修改成功，发送重启设备命令
        resultMsg.setMsg("acc_dev_rebootAfterOperate");
        return I18nUtil.i18nMsg(resultMsg);
    }

    @RequiresPermissions("acc:device:setRegistrationDevice")
    @Override
    public ModelAndView getDevIdsBySetRegistrationDevice(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            AccDeviceItem accDeviceItem = accDeviceService.getItemById(ids);
            if (!(accDeviceItem.getMachineType() == AccConstants.DEVICE_ACCESS_CONTROL
                && Short.valueOf(accDeviceItem.getCommType()) == ConstUtil.COMM_HTTP)) // pull一体机不能作为登记机
            {
                request.setAttribute("failedReason", AccConstants.DEV_NOT_SUPPORT_FEATURE);// 1代表设备不支持此操作
                return new ModelAndView("acc/device/opDevNotSupportFeatureTips");
            }
            request.setAttribute("isRegistrationDevice", accDeviceItem.getIsRegistrationDevice());
            request.setAttribute("alias", accDeviceItem.getAlias());
            request.setAttribute("editPage", true);
            request.setAttribute("devId", ids);
            return new ModelAndView("acc/device/opAccSetRegistrationDevice");
        }
        return null;
    }

    @RequiresPermissions("acc:device:setRegistrationDevice")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "acc_dev_setRegistrationDevice",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg setRegistrationDevice(String devId, String isRegistrationSel) {
        if (StringUtils.isNotBlank(devId) && StringUtils.isNotBlank(isRegistrationSel)) {
            boolean isRegist = Boolean.valueOf(isRegistrationSel);
            accDeviceService.setRegistrationDevice(devId, isRegist);
        }
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:device:queryDevRule")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "acc_dev_accessRules",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ModelAndView queryDevRule(String ids) {
        AccDeviceItem tempDev = accDeviceService.getItemById(ids);
        // IR9000不支持查看门禁规则 by juvenile.li add 20170828
        if (tempDev.getMachineType() == AccConstants.DEVICE_BIOIR_9000) {
            request.setAttribute("failedReason", AccConstants.DEV_NOT_SUPPORT_FEATURE);// 1代表设备不支持此操作
            return new ModelAndView("acc/device/opDevNotSupportFeatureTips");
        }
        List<List<String>> ruleList = accDeviceService.queryDevRule(ids);
        request.setAttribute("ruleList", ruleList);
        return new ModelAndView("acc/device/opAccQueryDevRule");
    }

    @RequiresPermissions("acc:device:queryDevUsage")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "acc_dev_queryDevVolume",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ModelAndView queryDevUsage(String ids) {
        List<List<String>> retList = accDeviceService.queryDevInfo(ids);
        request.setAttribute("retList", retList);
        StringBuilder devIds = new StringBuilder();
        for (List<String> list : retList) {
            // 拼接在线设备的id
            if (StringUtils.isBlank(list.get(6))) {
                devIds.append(list.get(5)).append(",");
            }
        }
        if (devIds.length() > 0) {
            request.setAttribute("devIds", devIds.substring(0, devIds.length() - 1));
        } else {
            request.setAttribute("getAll", "disabled='disabled'");
        }
        return new ModelAndView("acc/device/opAccQueryDevUsage");
    }

    @RequiresPermissions("acc:device:queryDevUsage")
    @Override
    public ZKResultMsg getDevUsage(String devId) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        JSONObject retObject = new JSONObject();
        JSONObject userJson = new JSONObject();
        JSONObject fingerJson = new JSONObject();
        JSONObject fvJson = new JSONObject();
        JSONObject faceJson = new JSONObject();
        //JSONObject bioPhotoJson = new JSONObject();
        JSONObject palmJson = new JSONObject();
        JSONObject irisJson = new JSONObject();
        //JSONObject faceBiodataJson = new JSONObject();
        // 先统一放入不支持标志，之后代码有循环到具体数据的时候会进行修改
        fingerJson.put(devId, "false");// 不支持返回false
        fvJson.put(devId, "false");// 不支持返回false
        faceJson.put(devId, "false");// 不支持返回false
        //bioPhotoJson.put(devId, "false");
        //faceBiodataJson.put(devId, "false");
        palmJson.put(devId, "false");
        irisJson.put(devId, "false");

        retObject.put("id", devId);
        AccDeviceItem dev = accDeviceService.getItemById(devId);
        Map<String, Long> cmdMap = accDeviceService.getDevUserAndTempCounts(devId);
        AccDeviceOptionItem maxMultiBioPhotoCount =
            accDeviceOptionService.getDevOptValueBySnAndName(dev.getSn(), "MaxMultiBioPhotoCount");
        AccDeviceOptionItem maxMultiBioDataCount =
            accDeviceOptionService.getDevOptValueBySnAndName(dev.getSn(), "MaxMultiBioDataCount");
        Map<Short, String> bioTemplateVersion = accDeviceService.getBioTemplateVersionBySn(dev.getSn());
        // 设备指纹版本
        String fingerVersion = bioTemplateVersion.get(BaseConstants.BaseBioType.FP_BIO_TYPE);
        // 设备指静脉版本
        String fvVersion = bioTemplateVersion.get(BaseConstants.BaseBioType.VEIN_BIO_TYPE);
        // 设备人脸版本
        String faceVersion = bioTemplateVersion.get(BaseConstants.BaseBioType.FACE_BIO_TYPE);
        // 设备掌纹版本
        String pvVersion = bioTemplateVersion.get(BaseConstants.BaseBioType.PALM_BIO_TYPE);
        // 设备可见光版本
        String vislightFaceVersion = bioTemplateVersion.get(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
        // 设备可见光掌纹版本
        String vislightPvVersion = bioTemplateVersion.get(AccConstants.TEMPLATE_VISILIGHT_PALM);
        //设备指纹版本
        String irisVersion = bioTemplateVersion.get(AccConstants.TEMPLATE_VISILIGHT_IRIS);
        for (String key : cmdMap.keySet()) {
            String[] cmdIdKey = key.split("_");
            Long cmdId = cmdMap.get(key);
            if (cmdId > 0) {
                Map<String, String> resultMap = accDeviceService.getCmdResultById(cmdId, 20);
                if (Objects.nonNull(resultMap)) {
                    int ret = Integer.parseInt(resultMap.get("result"));
                    if (ret >= 0) {
                        if ("".equals(accDeviceService.getQueryData(AccCacheKeyConstants.ERROR_USER))) {
                            JSONObject dataCountJson = null;
                            if (!""
                                .equals(accDeviceService.getQueryData(AccCacheKeyConstants.QUERY_DATA_COUNT + cmdId))) {
                                dataCountJson = JSONObject.parseObject(
                                    accDeviceService.getQueryData(AccCacheKeyConstants.QUERY_DATA_COUNT + cmdId));
                                if (dataCountJson.containsKey(ConstUtil.USER)) {
                                    String maxUserCountOpt =
                                        accDeviceOptionService.getValueByNameAndDevSn(dev.getSn(), "~MaxUserCount");
                                    int maxUserCount = Integer
                                        .parseInt(!AccConstants.DEVICE_OPTIONS_DEFAULT_VALUE.equals(maxUserCountOpt)
                                            ? maxUserCountOpt
                                            : dev.getCommType() == ConstUtil.COMM_HTTP ? "90" : "300")
                                        * 100;
                                    userJson.put(devId,
                                        dataCountJson.get(ConstUtil.USER).toString() + "/" + maxUserCount);
                                } else if (dataCountJson.containsKey(ConstUtil.TEMPLATEV10)
                                    || (dataCountJson.containsKey(AccConstants.BIODATA) && cmdIdKey.length > 1
                                        && BaseConstants.BaseBioType.FP_BIO_TYPE == Short.parseShort(cmdIdKey[1]))) {
                                    int maxFingerCount = 0;
                                    if (maxMultiBioDataCount != null) {
                                        maxFingerCount = Integer.parseInt(maxMultiBioDataCount.getValue()
                                            .split(":")[BaseConstants.BaseBioType.FP_BIO_TYPE]);
                                        fingerJson.put(devId,
                                            dataCountJson.get(AccConstants.BIODATA).toString() + "/" + maxFingerCount);
                                    } else {
                                        String maxFingerCountOpt = accDeviceOptionService
                                            .getValueByNameAndDevSn(dev.getSn(), "~MaxFingerCount");
                                        if (maxFingerCountOpt != null) {
                                            maxFingerCount = Integer.parseInt(maxFingerCountOpt) * 100;
                                        }
                                        if (AccConstants.FP_VERSION_12.equals(fingerVersion)) {
                                            fingerJson.put(devId, dataCountJson.get(AccConstants.BIODATA).toString()
                                                + "/" + maxFingerCount);
                                        } else {
                                            fingerJson.put(devId, dataCountJson.get(ConstUtil.TEMPLATEV10).toString()
                                                + "/" + maxFingerCount);
                                        }
                                    }
                                    // 存放设备支持的指纹模板版本
                                    fingerJson.put("fingerVersion", "V" + fingerVersion);
                                } else if (dataCountJson.containsKey(AccConstants.FVTEMPLATE)) {
                                    int maxFvCount = 0;
                                    if (maxMultiBioDataCount != null) {
                                        maxFvCount = Integer.parseInt(maxMultiBioDataCount.getValue()
                                            .split(":")[BaseConstants.BaseBioType.VEIN_BIO_TYPE]);
                                        fvJson.put(devId,
                                            dataCountJson.get(AccConstants.BIODATA).toString() + "/" + maxFvCount);
                                    } else {
                                        String maxFvCountOpt =
                                            accDeviceOptionService.getValueByNameAndDevSn(dev.getSn(), "~MaxFvCount");
                                        if (maxFvCountOpt != null) {
                                            maxFvCount = Integer.parseInt(maxFvCountOpt) * 100;
                                        }
                                        fvJson.put(devId,
                                            dataCountJson.get(AccConstants.FVTEMPLATE).toString() + "/" + maxFvCount);
                                    }
                                    // 存放设备支持的指静脉模板版本
                                    fvJson.put("fvVersion", "V" + fvVersion);
                                } /*else if (dataCountJson.containsKey(AccConstants.BIOPHOTO)) {
                                    int maxBiophotoCount = 0;
                                    if (maxMultiBioPhotoCount != null) {
                                        maxBiophotoCount = Integer.parseInt(maxMultiBioPhotoCount.getValue()
                                            .split(":")[BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE]);
                                    } else {
                                        String maxBiophotoCountOpt = accDeviceOptionService
                                            .getValueByNameAndDevSn(dev.getSn(), "~MaxBioPhotoCount");
                                        if (maxBiophotoCountOpt != null) {
                                            maxBiophotoCount = Integer.parseInt(maxBiophotoCountOpt);
                                        }
                                    }
                                    bioPhotoJson.put(devId,
                                        dataCountJson.get(AccConstants.BIOPHOTO).toString() + "/" + maxBiophotoCount);
                                }*/ else if (dataCountJson.containsKey(ConstUtil.FACEV7)
                                    || (dataCountJson.containsKey(AccConstants.BIODATA) && cmdIdKey.length > 1
                                        && BaseConstants.BaseBioType.FACE_BIO_TYPE == Short.parseShort(cmdIdKey[1]))) {
                                    int maxFaceCount = 0;
                                    if (maxMultiBioDataCount != null) {
                                        maxFaceCount = Integer.parseInt(maxMultiBioDataCount.getValue()
                                            .split(":")[BaseConstants.BaseBioType.FACE_BIO_TYPE]);
                                        faceJson.put(devId,
                                            dataCountJson.get(AccConstants.BIODATA).toString() + "/" + maxFaceCount);
                                    } else {
                                        String maxFaceCountOpt =
                                            accDeviceOptionService.getValueByNameAndDevSn(dev.getSn(), "~MaxFaceCount");
                                        if (maxFaceCountOpt != null) {
                                            maxFaceCount = Integer.parseInt(maxFaceCountOpt);
                                        }
                                        faceJson.put(devId,
                                            dataCountJson.get(ConstUtil.FACEV7).toString() + "/" + maxFaceCount);
                                    }
                                    // 设备面部版本
                                    faceJson.put("faceVersion", "V" + faceVersion);
                                } else if (dataCountJson.containsKey(AccConstants.BIODATA) && cmdIdKey.length > 1
                                    && BaseConstants.BaseBioType.PALM_BIO_TYPE == Short.parseShort(cmdIdKey[1])) {
                                    int maxPalmCount = 0;
                                    if (maxMultiBioDataCount != null) {
                                        maxPalmCount = Integer.parseInt(maxMultiBioDataCount.getValue()
                                            .split(":")[BaseConstants.BaseBioType.PALM_BIO_TYPE]);
                                    } else {
                                        String maxPalmCountOpt =
                                            accDeviceOptionService.getValueByNameAndDevSn(dev.getSn(), "~MaxPvCount");
                                        if (maxPalmCountOpt != null) {
                                            maxPalmCount = Integer.parseInt(maxPalmCountOpt);
                                        }
                                    }
                                    palmJson.put(devId + "",
                                        dataCountJson.get(AccConstants.BIODATA).toString() + "/" + maxPalmCount);
                                    // 设备掌纹版本
                                    palmJson.put("pvVersion", "V" + pvVersion);
                                } else if (dataCountJson.containsKey(AccConstants.BIODATA) && cmdIdKey.length > 1
                                    && AccConstants.TEMPLATE_VISILIGHT_PALM == Short.parseShort(cmdIdKey[1])) {
                                    int maxPalmCount = 0;
                                    if (maxMultiBioDataCount != null) {
                                        maxPalmCount = Integer.parseInt(maxMultiBioDataCount.getValue()
                                            .split(":")[AccConstants.TEMPLATE_VISILIGHT_PALM]);
                                    } else {
                                        String maxPalmCountOpt =
                                            accDeviceOptionService.getValueByNameAndDevSn(dev.getSn(), "~MaxPvCount");
                                        if (maxPalmCountOpt != null) {
                                            maxPalmCount = Integer.parseInt(maxPalmCountOpt);
                                        }
                                    }
                                    palmJson.put(devId + "",
                                        dataCountJson.get(AccConstants.BIODATA).toString() + "/" + maxPalmCount);
                                    // 设备掌纹版本
                                    palmJson.put("pvVersion", "V" + vislightPvVersion);
                                } else if (dataCountJson.containsKey(AccConstants.BIODATA) && cmdIdKey.length > 1
                                    && BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE == Short.parseShort(cmdIdKey[1])) {
                                    int maxVislightFaceTempCount = 0;
                                    if (maxMultiBioDataCount != null) {
                                        maxVislightFaceTempCount = Integer.parseInt(maxMultiBioDataCount.getValue()
                                            .split(":")[BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE]);
                                    } else {
                                        String maxBiophotoCountOpt =
                                            accDeviceOptionService.getValueByNameAndDevSn(dev.getSn(), "~MaxFaceCount");
                                        if (maxBiophotoCountOpt != null) {
                                            maxVislightFaceTempCount = Integer.parseInt(maxBiophotoCountOpt);
                                        }
                                    }
                                    faceJson.put(devId, dataCountJson.get(AccConstants.BIODATA).toString() + "/"
                                        + maxVislightFaceTempCount);
                                    // 设备支持的可见光模板版本
                                    faceJson.put("faceVersion", "V" + vislightFaceVersion);
                                } else if (dataCountJson.containsKey(AccConstants.BIODATA) && cmdIdKey.length > 1
                                        && AccConstants.TEMPLATE_VISILIGHT_IRIS.equals(Short.parseShort(cmdIdKey[1]))) {
                                    int maxirisTempCount = 0;
                                    if (maxMultiBioDataCount != null) {
                                        maxirisTempCount = Integer.parseInt(maxMultiBioDataCount.getValue().split(":")[AccConstants.TEMPLATE_VISILIGHT_IRIS]);
                                    } else {
                                        String maxBiophotoCountOpt = accDeviceOptionService.getValueByNameAndDevSn(dev.getSn(), "~MaxIrisCount");
                                        if (maxBiophotoCountOpt != null) {
                                            maxirisTempCount = Integer.parseInt(maxBiophotoCountOpt);
                                        }
                                    }
                                    irisJson.put(devId, dataCountJson.get(AccConstants.BIODATA).toString() + "/" + maxirisTempCount);
                                    // 设备虹膜模板版本
                                    irisJson.put("irisVersion", "V" + irisVersion);
                                }
                                accDeviceService.delQueryData(AccCacheKeyConstants.QUERY_DATA_COUNT + cmdId);
                            }
                            accDeviceService.delQueryData(AccCacheKeyConstants.SUM_RECORD_USER);
                        }
                        accDeviceService.delQueryData(AccCacheKeyConstants.ERROR_USER);
                    }
                }
            }
        }
        retObject.put("user", userJson);
        retObject.put("finger", fingerJson);
        retObject.put("fv", fvJson);
        retObject.put("face", faceJson);
        //retObject.put("biophoto", bioPhotoJson);
        retObject.put("palm", palmJson);
        retObject.put("iris", irisJson);
        //retObject.put("faceBiodata", faceBiodataJson);
        resultMsg.setData(retObject);
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public ZKResultMsg checkPinLenAndCardLen(String commTypeVal, String maxMCUCardBits) {
        String pinLen = accParamService.getParamValByName("pers.pinLen");
        String cardLen = accParamService.getParamValByName("pers.cardLen");
        String pinSupportLetter = accParamService.getParamValByName("pers.pinSupportLetter");
        if (Integer.parseInt(maxMCUCardBits) < Integer.parseInt(cardLen)) {
            throw new ZKBusinessException(I18nUtil.i18nCode("common_dev_beyondCardLen", maxMCUCardBits));
        }
        if (commTypeVal.equals("1") || commTypeVal.equals("2")) {
            if (Integer.parseInt(pinLen) > 9 || pinSupportLetter.equals("true")) {
                throw new ZKBusinessException(I18nUtil.i18nCode("common_dev_deviceNotSupportPin"));
            }
        }
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @RequiresPermissions("acc:device:updateMThreshold")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "common_dev_modifyFPThreshold",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg updateMThreshold(Acc4UpdateMThreshold acc4UpdateMThreshold) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        Long cmdId = accDeviceService.updateMThreshold(acc4UpdateMThreshold);
        if (cmdId != null && cmdId > 0) {
            Map<String, String> resultMap = accDeviceService.getCmdResultById(cmdId, 20);
            if (Objects.nonNull(resultMap)) {
                int ret = Integer.parseInt(resultMap.get("result"));
                if (ret >= 0) {
                    if (StringUtils.isNotBlank(acc4UpdateMThreshold.getMThreshold())) {
                        accDeviceOptionService.setDevOptValByName(acc4UpdateMThreshold.getDevId(), "MThreshold", acc4UpdateMThreshold.getMThreshold());
                    }
                    if (StringUtils.isNotBlank(acc4UpdateMThreshold.getFaceMThr())) {
                        accDeviceOptionService.setDevOptValByName(acc4UpdateMThreshold.getDevId(), "~FaceMThr", acc4UpdateMThreshold.getFaceMThr());
                    }
                    if (StringUtils.isNotBlank(acc4UpdateMThreshold.getPvMThreshold())) {
                        accDeviceOptionService.setDevOptValByName(acc4UpdateMThreshold.getDevId(), "PvMThreshold", acc4UpdateMThreshold.getPvMThreshold());
                    }
                    accDeviceService.rebootDevice(Lists.newArrayList(acc4UpdateMThreshold.getDevId()), false);// 重启设备
                } else {
                    // 从字典获取失败原因
                    String failedInfo = accBaseDictionaryService.getCommReason(ret);
                    failedInfo =
                        StringUtils.isNotBlank(failedInfo) ? I18nUtil.i18nCode(failedInfo) : Integer.toString(ret);
                    String msgStr = I18nUtil.i18nCode("common_op_failed")
                        + I18nUtil.i18nCode("common_dev_errorCode") + ":" + failedInfo;
                    throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_ERROR, msgStr);
                }
                resultMsg.setMsg("acc_dev_rebootAfterOperate");
            } else {
                throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_ERROR,
                    I18nUtil.i18nCode("common_op_failed"));
            }
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public ZKResultMsg checkPwd(String oldCommPwd, String devId) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        boolean isExist = false;
        if (StringUtils.isNotBlank(oldCommPwd) && StringUtils.isNotBlank(devId)) {
            AccDeviceItem tempDevice = accDeviceService.getItemById(devId);
            if (tempDevice != null) {
                if (tempDevice.getCommPwd() == null || oldCommPwd.equals(tempDevice.getCommPwd())
                    || (oldCommPwd.equals(""))) {
                    isExist = true;
                }
            }
        }
        resultMsg.setData(isExist);
        return I18nUtil.i18nMsg(resultMsg);
    }

    @RequiresPermissions("acc:device:updateCommPwd")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "common_dev_modifyCommPwd",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg updateCommPwd(String devId, String newCommPwd) {
        AccDeviceItem device = accDeviceService.getItemById(devId);
        int ret = -1;
        Long cmdId = accDeviceService.updateCommPwd(devId, newCommPwd);
        if (cmdId != null && cmdId > 0) {
            Map<String, String> resultMap = accDeviceService.getCmdResultById(cmdId, 20);// 获取命令返回值
            if (Objects.nonNull(resultMap)) {
                ret = Integer.parseInt(resultMap.get("result"));
            }
        }
        if (ret >= 0) { // 成功修改设备ip信息
            device.setCommPwd(newCommPwd);
            accDeviceService.saveAccDevice(device);
            accDeviceService.updateAdmsDeviecPwd(device.getSn(), newCommPwd);
        } else {
            throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_ERROR, "common_op_failed");
        }
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @RequiresPermissions("acc:device:updateRs485Addr")
    @Override
    public ModelAndView getRs485Addr(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            AccDeviceItem dev = accDeviceService.getItemById(ids);
            if (dev != null) {
                // 当前只有一体机和C4（inbioX80需要该功能（无拨码开关），其他的改了重启后会变回拨码开关的值。
                if (Short.valueOf(dev.getCommType()).shortValue() == AccConstants.COMM_TYPE_PUSH_HTTP
                    || !AccConstants.DEVICE_SUPPORT_MODIFY_RS485_ADDRESS.contains(dev.getMachineType())) {
                    request.setAttribute("failedReason", AccConstants.DEV_NOT_SUPPORT_FEATURE);// 1代表设备不支持此操作
                    return new ModelAndView("acc/device/opDevNotSupportFeatureTips");
                } else {
                    short devConnectState = Short.valueOf(accDeviceService.getStatus(dev.getSn()));
                    if (devConnectState != ConstUtil.DEV_STATE_ONLINE) {
                        request.setAttribute("failedReason", devConnectState);// 含离线和禁用
                        return new ModelAndView("acc/device/opDevNotSupportFeatureTips");
                    }
                }
                request.setAttribute("accDev", dev);
                request.setAttribute("editPage", true);// 去掉保存并继续按钮
                return new ModelAndView("acc/device/opAccUpdateRs485Addr");
            }
        }
        return null;
    }

    @RequiresPermissions("acc:device:updateRs485Addr")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "acc_dev_modifyRS485Addr",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg updateRs485Addr(String devId, String comAddr) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        if (StringUtils.isNotBlank(devId) && StringUtils.isNotBlank(comAddr)) {
            Long cmdId = accDeviceService.updateRs485Addr(devId, comAddr);
            if (cmdId != null && cmdId > 0) {
                Map<String, String> resultMap = accDeviceService.getCmdResultById(cmdId, 20);
                Integer ret = Integer.valueOf(resultMap.get("result"));
                if (ret >= 0) { // 执行命令成功修改数据库数据
                    accDeviceService.rebootDevice(Lists.newArrayList(devId), false);
                    AccDeviceItem accDeviceItem = new AccDeviceItem();
                    accDeviceItem.setId(devId);
                    accDeviceItem.setComAddress(comAddr);
                    accDeviceService.updateItemByParam(accDeviceItem);
                    accDeviceService.updateAdmsComAddress(accDeviceItem.getSn(), comAddr);
                } else {
                    String failedInfo = accBaseDictionaryService.getCommReason(ret);
                    failedInfo = StringUtils.isNotBlank(failedInfo) ? I18nUtil.i18nCode(failedInfo) : ret.toString();
                    String msgStr = I18nUtil.i18nCode("common_op_failed") + I18nUtil.i18nCode("common_dev_errorCode")
                        + ":" + failedInfo;
                    throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_ERROR, msgStr);
                }
            }
        }
        resultMsg.setMsg("acc_dev_rebootAfterOperate");
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public TreeItem tree() {
        List<TreeItem> items = new ArrayList<>();

        List<AccDeviceItem> accDeviceItemList = accDeviceService.getByCondition(new AccDeviceItem());
        if (!accDeviceItemList.isEmpty()) {
            TreeItem item = null;
            for (AccDeviceItem accDeviceItem : accDeviceItemList) {
                item = new TreeItem();
                item.setId(accDeviceItem.getId());
                item.setText(accDeviceItem.getAlias());
                item.setParent(new TreeItem("0"));
                items.add(item);
            }
        }
        List<TreeItem> treeItems = TreeBuilder.newTreeBuilder(TreeItem.class, String.class).buildToTreeList(items);
        return new TreeItem("0", treeItems);
    }

    @RequiresPermissions("acc:device:setTimeZone")
    @Override
    public ModelAndView getDevIdsBySetTimeZone(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            List<AccDeviceItem> accDeviceItemList = accDeviceService.getItemByIds(ids);
            StringBuffer devName = new StringBuffer();
            StringBuffer disabledDevName = new StringBuffer();
            StringBuffer warnInfoDevName = new StringBuffer();
            StringBuffer noSupportDevName = new StringBuffer();
            List<String> timeZoneKeyList = Lists.newArrayList();
            StringBuffer devIds = new StringBuffer();
            Map<String, StringBuffer> tzNameMap = Maps.newHashMap();
            accDeviceItemList.forEach(dev -> {
                String status = accDeviceService.getStatus(dev.getSn());
                if (!(String.valueOf(AccConstants.DEV_STATE_DISABLE)).equals(status)) {
                    if (dev.getParentDeviceId() != null) {
                        warnInfoDevName.append(dev.getAlias()).append(",");
                    } else if (accDeviceOptionService.isSupportFun(dev.getSn(), "MachineTZFunOn")) {
                        devIds.append(dev.getId()).append(",");
                        String tzVal = dev.getTimeZone();
                        if (StringUtils.isNotBlank(tzVal)) {
                            String tzKey = getTZKeyByVal(tzVal);
                            if (!timeZoneKeyList.contains(tzVal)) {
                                timeZoneKeyList.add(tzVal);
                            }
                            // 夏令时
                            String dsTimeId = StringUtils.defaultString(dev.getAccDSTimeId());
                            String dsTimeName = "";
                            if (accDeviceOptionService.isSupportFun(dev.getSn(), "DSTFunOn")
                                || accDeviceOptionService.isSupportDevParam(dev.getSn(), "~DSTF")) {
                                if (StringUtils.isNotBlank(dsTimeId)) {
                                    AccDSTimeItem dsTimeItem = accDSTimeService.getItemById(dsTimeId);
                                    dsTimeName = I18nUtil.i18nCode(dsTimeItem.getName());
                                }
                            }
                            if (tzNameMap.containsKey(tzKey)) {
                                tzNameMap.get(tzKey).append(dev.getAlias()).append("&").append(dev.getId()).append("&").append(dsTimeId).append("&").append(dsTimeName).append(",");
                            } else {
                                StringBuffer dstNameBuf = new StringBuffer(tzKey + ":" + dev.getAlias() + "&"
                                    + dev.getId() + "&" + dsTimeId + "&" + dsTimeName + ",");
                                tzNameMap.put(tzKey, dstNameBuf);
                            }
                        } else {
                            String tzNotSet = I18nUtil.i18nCode("acc_dev_notSetDst");
                            // 该设备没有设备过夏令时，属于尚未设置行列
                            if (tzNameMap.containsKey(tzNotSet)) {
                                tzNameMap.get(tzNotSet).append(dev.getAlias()).append("&").append(dev.getId()).append(",");
                            } else {
                                StringBuffer tzNameBuf =
                                    new StringBuffer(tzNotSet + ":" + dev.getAlias() + "&" + dev.getId() + ",");
                                tzNameMap.put(tzNotSet, tzNameBuf);
                            }
                        }
                    } else {
                        noSupportDevName.append(dev.getAlias()).append(",");
                    }
                } else {
                    disabledDevName.append(dev.getAlias()).append(",");
                }
            });
            tzNameMap.forEach((k, v) -> {
                devName.append(v.substring(0, v.length() - 1)).append(";");
            });
            request.setAttribute("timeZoneKey", devName.length() > 0
                ? (!timeZoneKeyList.isEmpty() ? timeZoneKeyList.get(0) : accDSTimeService.getDefaultTimeZone()) : "");
            request.setAttribute("devIds", devIds.length() > 0 ? devIds.substring(0, devIds.length() - 1) : null);
            request.setAttribute("devicesName",
                devName.length() > 0 ? devName.substring(0, devName.length() - 1) : null);
            request.setAttribute("disabledDevName",
                disabledDevName.length() > 0 ? disabledDevName.substring(0, disabledDevName.length() - 1) : null);
            request.setAttribute("warnInfoDevName",
                warnInfoDevName.length() > 0 ? warnInfoDevName.substring(0, warnInfoDevName.length() - 1) : null);
            request.setAttribute("noSupportDevName",
                noSupportDevName.length() > 0 ? noSupportDevName.substring(0, noSupportDevName.length() - 1) : null);
            return new ModelAndView("acc/device/opAccDevSetTimeZone");
        }
        return null;
    }

    @RequiresPermissions("acc:device:setTimeZone")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "acc_dev_setTimeZone",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg setTimeZone(String devIds, String timeZone) {
        progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
        List<AccDeviceItem> accDeviceItemList = accDeviceService.getItemByIds(devIds);
        int size = accDeviceItemList.size();
        AccDeviceItem dev = null;
        for (int i = 0; i < size; i++) {
            dev = accDeviceItemList.get(i);
            int total = (int)((i + 1) / (float)size * 100);
            int lastTotal = (int)((i + 0.3) / (float)size * 100);
            progressCache.setProcess(new ProcessBean(30, lastTotal,
                dev.getAlias() + ":" + I18nUtil.i18nCode("acc_dev_timeZoneSetting") + "<br/>"));
            accDeviceService.setDeviceTimeZoneToDev(dev, timeZone);
            progressCache.setProcess(new ProcessBean(100, total,
                dev.getAlias() + ":" + I18nUtil.i18nCode("acc_dev_timeZoneCmdSuccess") + "<br/>"));
        }
        progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish"));
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:device:setTimeZone")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "acc_dev_setTimeZone",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg setTimeZoneAndDsTime(String devIds, String timeZone, String accDSTimeId) {
        progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
        List<AccDeviceItem> accDeviceItemList = accDeviceService.getItemByIds(devIds);
        int size = accDeviceItemList.size();
        AccDeviceItem dev = null;
        AccDSTimeItem accDSTimeItem = null;
        if (StringUtils.isNotBlank(accDSTimeId)) {
            accDSTimeItem = accDSTimeService.getItemById(accDSTimeId);
        }
        for (int i = 0; i < size; i++) {
            dev = accDeviceItemList.get(i);
            int total = (int)((i + 1) / (float)size * 100);
            int lastTotal = (int)((i + 0.3) / (float)size * 100);
            progressCache.setProcess(new ProcessBean(30, lastTotal,
                dev.getAlias() + ":" + I18nUtil.i18nCode("acc_dev_timeZoneSetting") + "<br/>"));
            accDeviceService.setDeviceTimeZoneToDev(dev, timeZone);
            progressCache.setProcess(new ProcessBean(100, total,
                dev.getAlias() + ":" + I18nUtil.i18nCode("acc_dev_timeZoneCmdSuccess") + "<br/>"));
            if (accDSTimeItem != null) {
                progressCache.setProcess(new ProcessBean(30, lastTotal,
                    dev.getAlias() + ":" + I18nUtil.i18nCode("acc_dev_dstSettingTip") + "<br/>"));
            } else {
                progressCache.setProcess(new ProcessBean(30, lastTotal,
                    dev.getAlias() + ":" + I18nUtil.i18nCode("acc_dev_dstDelTip") + "<br/>"));
            }
            accDSTimeService.setDSTime(accDSTimeId, dev.getId());
            progressCache.setProcess(new ProcessBean(100, total,
                dev.getAlias() + ":" + I18nUtil.i18nCode("acc_dev_dstCmdSuccess") + "<br/>"));
        }
        progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish"));
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:device:setDSTime")
    @Override
    public ModelAndView getDevIdsBySetDstimeSortByDstime(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            // Map<String, String> devMap = accDeviceService.getDevIdsBySetDstimeSortByDstime(ids);
            StringBuffer devName = new StringBuffer("");
            StringBuffer disabledDevName = new StringBuffer("");
            StringBuffer noSupportDevName = new StringBuffer("");
            StringBuffer warnInfoDevName = new StringBuffer("");
            StringBuffer devIds = new StringBuffer();
            List<AccDeviceItem> accDeviceItemLists = accDeviceService.getItemByIds(ids);
            Map<String, StringBuffer> dstNameMap = Maps.newHashMap();
            accDeviceItemLists.stream().forEach(dev -> {
                String status = accDeviceService.getStatus(dev.getSn());
                if (!(String.valueOf(AccConstants.DEV_STATE_DISABLE)).equals(status)) {
                    if (StringUtils.isNotBlank(dev.getParentDeviceId())) {
                        warnInfoDevName.append(dev.getAlias()).append(",");
                    }
                    // 添加绿标一体机支持的条件判断："~DSTF" by seven.wu 20181218
                    else if (accDeviceOptionService.isSupportDevParam(dev.getSn(), "DSTFunOn")
                        || accDeviceOptionService.isSupportDevParam(dev.getSn(), "~DSTF")) {
                        devIds.append(dev.getId()).append(",");
                        String devDSTimeName = StringUtils.isNotBlank(dev.getAccDSTimeId())
                            ? accDSTimeService.getItemById(dev.getAccDSTimeId()).getName() : null;
                        if (devDSTimeName == null) {
                            String dstNotSet = I18nUtil.i18nCode("acc_dev_notSetDst");
                            // 该设备没有设备过夏令时，属于尚未设置行列
                            if (dstNameMap.containsKey(dstNotSet)) {
                                dstNameMap.get(dstNotSet).append(dev.getAlias() + "&" + dev.getId() + ",");
                            } else {
                                StringBuffer dstNameBuf =
                                    new StringBuffer(dstNotSet + ":" + dev.getAlias() + "&" + dev.getId() + ",");
                                dstNameMap.put(dstNotSet, dstNameBuf);
                            }
                        } else {
                            // 该设备设置过夏令时
                            String dstKey = devDSTimeName + "-" + dev.getAccDSTimeId();
                            if (dstNameMap.containsKey(dstKey)) {
                                dstNameMap.get(dstKey).append(dev.getAlias() + "&" + dev.getId() + ",");
                            } else {
                                StringBuffer dstNameBuf =
                                    new StringBuffer(dstKey + ":" + dev.getAlias() + "&" + dev.getId() + ",");
                                dstNameMap.put(dstKey, dstNameBuf);
                            }
                        }
                    } else {
                        noSupportDevName.append(dev.getAlias()).append(",");
                    }
                } else {
                    disabledDevName.append(dev.getAlias()).append(",");
                }
            });
            dstNameMap.forEach((k, v) -> {
                devName.append(v.substring(0, v.length() - 1) + ";");
            });
            request.setAttribute("devIds", devIds.length() > 0 ? devIds.substring(0, devIds.length() - 1) : null);
            request.setAttribute("devicesName",
                devName.length() > 0 ? devName.substring(0, devName.length() - 1) : null);
            request.setAttribute("disabledDevName",
                disabledDevName.length() > 0 ? disabledDevName.substring(0, disabledDevName.length() - 1) : null);
            request.setAttribute("warnInfoDevName",
                warnInfoDevName.length() > 0 ? warnInfoDevName.substring(0, warnInfoDevName.length() - 1) : null);
            request.setAttribute("noSupportDevName",
                noSupportDevName.length() > 0 ? noSupportDevName.substring(0, noSupportDevName.length() - 1) : null);
            return new ModelAndView("acc/device/opAccDevSetDstime");
        }
        return null;
    }

    @RequiresPermissions("acc:device:setDSTime")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "acc_dev_setDstime",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg setDSTime(String devIds, String dstimeId) {
        if (StringUtils.isNotBlank(devIds)) {
            List<AccDeviceItem> accDeviceItemList = accDeviceService.getItemByIds(devIds);
            AccDSTimeItem accDSTimeItem = null;
            if (StringUtils.isNotBlank(dstimeId)) {
                accDSTimeItem = accDSTimeService.getItemById(dstimeId);
            }
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            int size = accDeviceItemList.size();
            AccDeviceItem dev = null;
            for (int i = 0; i < size; i++) {
                dev = accDeviceItemList.get(i);
                int total = (int)((i + 1) / (float)size * 100);
                int lastTotal = (int)((i + 0.3) / (float)size * 100);
                if (accDSTimeItem != null) {
                    progressCache.setProcess(new ProcessBean(30, lastTotal,
                        dev.getAlias() + ":" + I18nUtil.i18nCode("acc_dev_dstSettingTip") + "<br/>"));
                    accDeviceService.setOrDelDST(dev, accDSTimeItem, "set");
                } else {
                    progressCache.setProcess(new ProcessBean(30, lastTotal,
                        dev.getAlias() + ":" + I18nUtil.i18nCode("acc_dev_dstDelTip") + "<br/>"));
                    accDeviceService.setOrDelDST(dev, accDSTimeItem, "del");
                }
                accDeviceService.saveDSTime(dev, accDSTimeItem);
                progressCache.setProcess(new ProcessBean(100, total,
                    dev.getAlias() + ":" + I18nUtil.i18nCode("acc_dev_dstCmdSuccess") + "<br/>"));
            }
            progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish"));
        }
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    /**
     * @Description: 根据时区的值获取时区的Key
     * <AUTHOR>
     * @date 2018/5/16 14:53
     * @param tzVal 时区值
     * @return
     */
    private String getTZKeyByVal(String tzVal) {
        int tempNum = Integer.parseInt(tzVal);
        String deliveryVal = "";
        if (tempNum % 100 != 0) {
            deliveryVal = Math.abs(tempNum % 100) + "";
        }
        // 该设备设置过夏令时
        return "UTC" + tzVal.charAt(0) + Math.abs(tempNum / 100) + deliveryVal + "_" + tzVal;
    }

    /**
     * 比较两个字符串的大小
     *
     * <AUTHOR> href=mailto:<EMAIL>>方武略</a>
     * @since 2015年1月22日 下午15:45:30
     * @param defVersion 默认版本<acc.firmVer.acPanel=5.7.6;acc.firmVer.acDevice=6.64>
     * @param curVersion 当前版本
     * @param deviceType 设备类型 0:门禁控制器 1：一体机
     * @return -1:小于；0：等于 ；1：大于
     */
    private int verComparison(String curVersion, String defVersion, int deviceType) {
        String[] fullStr = curVersion.split(" ");
        if (deviceType == 0) {
            if (fullStr[2].split("\\.").length > 3)// 老固件不带小版本号
            {
                curVersion = fullStr[2].substring(0, fullStr[2].lastIndexOf("."));
            } else
            // 新固件带版本号
            {
                curVersion = fullStr[2];
            }
        } else if (deviceType == 1) {
            curVersion = fullStr[1].substring(0, fullStr[1].lastIndexOf("."));
        }
        int result = 0;// 默认相等
        String[] curStr = curVersion.split("\\.");
        String[] defStr = defVersion.split("\\.");
        if (defVersion.equalsIgnoreCase(curVersion)) {
            return result;
        }
        for (int i = 0; i < curStr.length; i++) {
            // 废弃之前的强转整型比较，统一改成用ASCII 字符版本比较 modified by max 20170103
            if (curStr[i].compareToIgnoreCase(defStr[i]) > 0) {
                result = 1;// 大于
                break;
            } else if (curStr[i].compareToIgnoreCase(defStr[i]) < 0) {
                result = -1;// 小于
                break;
            }
        }
        return result;
    }

    @Override
    public ModelAndView searchWifiList(String devId) {
        if (StringUtils.isNotBlank(devId)) {
            Map<String, String> dataMap = accDeviceService.searchWifiListFromDev(devId);
            if (dataMap.containsKey("failedReason")) {
                request.setAttribute("failedReason", dataMap.get("failedReason"));// 含离线和禁用
                return new ModelAndView("acc/device/opDevNotSupportFeatureTips");
            }
            request.setAttribute("cmdId", dataMap.get("cmdId"));
            request.setAttribute("devId", devId);
        }
        return new ModelAndView("acc/device/opAccSearchWifi");
    }

    @Override
    public ZKResultMsg getWifiList(String cmdId) {
        ZKResultMsg resultMsg = accDeviceService.getWifiList(cmdId);
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public ZKResultMsg searchWifiListAgain(String devId) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        if (StringUtils.isNotBlank(devId)) {
            Map<String, String> dataMap = accDeviceService.searchWifiListFromDev(devId);
            resultMsg.setData(dataMap.get("cmdId"));
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public ZKResultMsg switchNetWorkTest(String devId, String currentMode, String netConnectMode, String wirelessSSID,
        String wirelessKey) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        if (StringUtils.isNotBlank(devId)) {
            if (netConnectMode == null) {
                throw new ZKBusinessException(I18nUtil.i18nCode("common_commStatus_dataParamError"));
            }
            Long cmdId =
                accDeviceService.switchNetWorkTest(devId, currentMode, netConnectMode, wirelessSSID, wirelessKey);
            resultMsg.setData(cmdId);
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public ZKResultMsg getCmdResultById(String devId, String cmdId, String type) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        if (StringUtils.isNotBlank(cmdId)) {
            Map<String, String> resultMap = accDeviceService.getCmdResultById(Long.parseLong(cmdId), 25);
            Integer ret = Objects.isNull(resultMap) ? Integer.valueOf(ConstUtil.CMD_TIMEOUT)
                : Integer.valueOf(resultMap.get("result"));
            if ("identityCard".equals(type)) {
                if (ret != null && ret > 0) { // 获取结果 大于0为成功
                    // 暂时未用到
                    // 获取身份证信息，把身份证信息也返回出去。 by juvenile.li add 20171222
                    // String key = AccConstUtil.IDENTITY_CARD_INFO_KEY + cmdId;
                    // JSONObject jsonData = AccRedisUtil.getJson(key);
                    // if (jsonData != null && jsonData.has("data"))
                    // {
                    // JSONArray jsonArray = jsonData.getJSONArray("data");
                    // JSONObject jsonObject = jsonArray.getJSONObject(0);
                    // obj.put("identityCardInfo", jsonObject);
                    // }
                }
            } else if ("testNetConnect".equals(type)) { // 测试网络相关的，0为成功
                if (ret != null && ret != 0) { // 获取结果 0为成功
                    if (ret == Integer.parseInt(ConstUtil.CMD_TIMEOUT)) {
                        resultMsg.setRet("500");
                    } else {
                        resultMsg.setRet("400");
                    }
                }
            }
            resultMsg.setData(ret);
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @RequiresPermissions("acc:device:updateNetConnectMode")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "acc_dev_updateNetConnectMode",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg updateNetConnectMode(String devId, String netConnectMode, String wirelessSSID,
        String wirelessKey) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        if (StringUtils.isNotBlank(devId)) {
            if (StringUtils.isBlank(netConnectMode)) {
                throw new ZKBusinessException(I18nUtil.i18nCode("common_commStatus_dataParamError"));
            }
            Long cmdId = accDeviceService.updateNetConnectMode(devId, netConnectMode, wirelessSSID, wirelessKey);
            Map<String, String> resultMap = accDeviceService.getCmdResultById(cmdId, 20);
            Integer ret = Integer.valueOf(resultMap.get("result"));
            if (ret >= 0) {
                accDeviceService.rebootDevice(Lists.newArrayList(devId), false);// 执行成功，重启设备
                accDeviceOptionService.setDevOptValByName(devId, "NetConnectMode", netConnectMode);
                if (netConnectMode.equals("2")) { // WIFI模式
                    accDeviceOptionService.setDevOptValByName(devId, "WirelessSSID", wirelessSSID);
                    accDeviceOptionService.setDevOptValByName(devId, "WirelessKey", wirelessKey);
                }
            }
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public ZKResultMsg grantAuthority(AccSearchAddDeviceItem item) {
        accDeviceService.grantAuthority(item);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @RequiresPermissions("acc:device:addChildDevice")
    @Override
    public DxGrid getAuthorizeChildDevList(AccAuthorizeChildDevItem accAuthorizeChildDevItem) {
        Pager pager = new Pager();
        List<AccAuthorizeChildDevItem> accAuthorizeChildDevItemList =
            accDeviceService.getAuthorizeChildDevList(accAuthorizeChildDevItem);
        pager.setData(accAuthorizeChildDevItemList);
        pager.setTotal(accAuthorizeChildDevItemList.size());
        pager.setPage(getPageNo());
        pager.setSize(getPageSize());
        return GridUtil.convert(pager, accAuthorizeChildDevItem.getClass());
    }

    @Override
    public ZKResultMsg validChildDevCount(String devId, String devSns) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        if (StringUtils.isNotBlank(devId)) {
            int devCount = 0;
            int doorCount = 0;
            AccDeviceItem accDevice = accDeviceService.getItemById(devId);
            // String key = AccConstants.DEVICE_AUTHORIZE_KEY + accDevice.getSn();
            String data = accDeviceService.getDeviceAuthorize(accDevice.getSn());
            if (StringUtils.isNotBlank(data)) {
                long cmdId = accDeviceService.queryAuthorizeListFromDev(devId);
                if (cmdId > 0) {
                    Map<String, String> resultMap = accDeviceService.getCmdResultById(cmdId, 60);
                    if (Objects.nonNull(resultMap) && Integer.parseInt(resultMap.get("result")) > 0) {
                        data = accDeviceService.getDeviceAuthorize(accDevice.getSn());
                    }
                }
            }

            if (StringUtils.isNotBlank(data)) {
                List<String> devSnList = Lists.newArrayList(Arrays.asList(devSns.split(",")));
                JSONObject json = JSONObject.parseObject(data);
                JSONArray jsonArr = json.getJSONArray("data");
                if (jsonArr.size() > 0) {
                    for (int i = 0; i < jsonArr.size(); i++) {
                        JSONObject devJson = jsonArr.getJSONObject(i);
                        if (devSnList.contains(devJson.getString("SN"))) {
                            devCount++;
                            doorCount += devJson.getIntValue("LockCount");
                        }
                    }
                }
            }

            resultMsg = accDeviceService.validChildDevCount(devCount, doorCount);
            // if (resultMsg.getData() != null) {
            // int dataCount = (int) resultMsg.getData();
            // resultMsg.setMsg(String.format(I18nUtil.i18nCode(resultMsg.getMsg()), dataCount));
            // resultMsg.setData(null);
            // }
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public ZKResultMsg getParentDevList() {
        List<SelectItem> selectItems = new ArrayList<>();
        List<AccDeviceItem> deviceItemList =
            accDeviceService.getByMachineTypeAndAuth(request.getSession().getId(), AccConstants.DEVICE_BIOIR_9000);
        SelectItem selectItem = null;
        if (Objects.nonNull(deviceItemList) && deviceItemList.size() > 0) {
            for (AccDeviceItem accDeviceItem : deviceItemList) {
                selectItem = new SelectItem();
                selectItem.setValue(accDeviceItem.getId());
                selectItem.setText(accDeviceItem.getAlias());
                selectItems.add(selectItem);
            }
        }
        return I18nUtil.i18nMsg(new ZKResultMsg(selectItems));
    }

    @Override
    @RequiresPermissions({"acc:device:modParentDevice", "acc:device:configParentDevice"})
    public ZKResultMsg configParentDevice(String id, String parentDevId, String webServerURL) {
        Long cmdId = accDeviceService.configParentDevice(id, parentDevId, webServerURL);
        if (Objects.nonNull(cmdId)) {
            if (cmdId < 0 && StringUtils.isNotBlank(parentDevId)) {
                throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_ERROR,
                    I18nUtil.i18nCode("acc_dev_maxSubCount", -1 * cmdId));
            }
            Map<String, String> resultMap = accDeviceService.getCmdResultById(cmdId, 60);
            if (Objects.nonNull(resultMap)) {
                int ret = Integer.parseInt(resultMap.get("result"));
                if (ret == 0) {
                    Long rebootId = accDeviceService.rebootDevice(Lists.newArrayList(id), true).get(0);
                    if (Objects.nonNull(rebootId)) {
                        Map<String, String> rebootMap = accDeviceService.getCmdResultById(rebootId, 60);
                        if (Integer.parseInt(rebootMap.get("result")) == 0) {
                            if (StringUtils.isBlank(parentDevId)) {// 子设备切换成主设备
                                accDeviceService.changeParentDevice(id);
                            } else {
                                accDeviceService.updateParentDevice(id, parentDevId);
                            }
                        } else {
                            if (Integer.parseInt(rebootMap.get("result")) == Integer.parseInt(ConstUtil.CMD_TIMEOUT)) {
                                throw new ZKBusinessException(I18nUtil.i18nCode("common_commStatus_cmdTimeOut"));
                            }
                            throw new ZKBusinessException(I18nUtil.i18nCode("common_op_failed"));
                        }
                    }
                }
            }
        }
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public ZKResultMsg checkDevExist() {
        int count = accDeviceService.getByCondition(new AccDeviceItem()).size();
        return I18nUtil.i18nMsg(new ZKResultMsg(count));
    }

    @Override
    public ZKResultMsg getDevTreeJson(String type, String area, String getDstime) {
        boolean getTime = StringUtils.isNotBlank(getDstime) ? true : false;
        JSONObject devsJson = new JSONObject();
        devsJson.put("id", 0);
        JSONArray devJsonArray = new JSONArray();
        AccDeviceItem accDeviceItem = new AccDeviceItem();
        // new ArrayList<>();
        String areaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId());
        if (StringUtils.isNotBlank(areaIds)) {
            if (StringUtils.isNotBlank(area)) {
                areaIds = areaIds + "," + area;
            }
            accDeviceItem.setAreaIdIn(areaIds);
        } else if (StringUtils.isNotBlank(area)) {
            accDeviceItem.setAreaIdIn(area);
        }
        List<AccDeviceItem> accDeviceItemList = accDeviceService.getByCondition(accDeviceItem);
        JSONObject devInfoJson = null;
        for (AccDeviceItem dev : accDeviceItemList) {
            if (getTime) {
                // 子设备的夏令时不支持，屏蔽掉 by juvenile.li add 20180109
                // 添加绿标一体机支持夏令时的参数判断:"~DSTF" by seven.wu 20181218
                if ((accDeviceOptionService.isSupportFun(dev.getSn(), "DSTFunOn")
                    || accDeviceOptionService.isSupportDevParam(dev.getSn(), "~DSTF"))
                    && dev.getParentDeviceId() == null) { // 支持夏令时
                    devInfoJson = new JSONObject();
                    devInfoJson.put("id", dev.getId());
                    devInfoJson.put("text", dev.getAlias());
                    devInfoJson.put("open", 1);
                    devJsonArray.add(devInfoJson);
                }
            } else {
                devInfoJson = new JSONObject();
                devInfoJson.put("id", dev.getId());
                devInfoJson.put("text", dev.getAlias());
                devInfoJson.put("open", 1);
                devJsonArray.add(devInfoJson);
            }
        }
        if (type != null && type.equals("all")) {// 带全部勾选
            if (devJsonArray.size() > 0) {
                JSONObject childJson = new JSONObject();
                JSONArray childArray = new JSONArray();
                childJson.put("id", "-1");
                childJson.put("open", "1");
                childJson.put("text", I18nUtil.i18nCode("common_all"));// 全部
                childJson.put("item", devJsonArray);
                childArray.add(childJson);
                devsJson.put("item", childArray);
            } else {
                devsJson.put("item", new JSONArray());
            }
        } else {
            devsJson.put("item", devJsonArray);
        }
        return I18nUtil.i18nMsg(new ZKResultMsg(devsJson));
    }

    @Override
    @RequiresPermissions("acc:device:addChildDevice")
    public ZKResultMsg authorizeChildDevice(String devId, String devSns) {
        ZKResultMsg resultMsg = accDeviceService.authorizeChildDevice(devId, devSns);
        if ("subEmpty".equals(resultMsg.getRet())) {
            throw ZKBusinessException.errorException(I18nUtil.i18nCode(resultMsg.getMsg()));
        } else if ("noLicense".equals(resultMsg.getRet())) {
            throw ZKBusinessException
                .errorException(String.format(I18nUtil.i18nCode(resultMsg.getMsg()), resultMsg.getData()));
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public ZKResultMsg checkServerConnection(String devId, String parentDevId, String serverAddress) {
        ZKResultMsg resultMsg = ZKResultMsg.failMsg();
        if (StringUtils.isNotBlank(devId)) {
            resultMsg = accDeviceService.checkServerConnection(devId, parentDevId, serverAddress);
            if ("fail".equals(resultMsg.getRet()) && Objects.nonNull(resultMsg.getData())) {
                resultMsg.setMsg(String.format(I18nUtil.i18nCode(resultMsg.getMsg()), resultMsg.getData()));
            }
        }
        return resultMsg;
    }

    @Override
    public ZKResultMsg isOnlineByDevSn(String devSns) {
        ZKResultMsg zkResultMsg = ZKResultMsg.failMsg();
        if (StringUtils.isNotBlank(devSns)) {
            JSONArray stateArray = new JSONArray();
            List<String> snList = Lists.newArrayList(devSns.split(","));
            snList.stream().forEach(sn -> {
                JSONObject devStatus = new JSONObject();
                String status = accDeviceService.getStatus(sn);
                Long cmdCount = accDeviceService.getCmdCount(sn);
                devStatus.put("connectState", status);
                devStatus.put("cmdCount", cmdCount);
                devStatus.put("sn", sn);
                stateArray.add(devStatus);
            });
            zkResultMsg = ZKResultMsg.successMsg();
            zkResultMsg.setData(stateArray);
        }
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg getDevInfo(AccDeviceParamItem item) {
        return I18nUtil.i18nMsg(accDeviceService.getDevInfo(item));
    }

    @Override
    public ModelAndView lookUpChildDeviceInfo(String id) {
        request.setAttribute("parentDevId", id);
        return new ModelAndView("acc/device/opAccChildDevList");
    }

    @Override
    public DxGrid getChildDevList(String parentDevId) {
        Pager pager = new Pager();
        List<AccDeviceItem> deviceItemList = accDeviceService.getChildDevList(parentDevId);
        pager.setData(deviceItemList);
        pager.setTotal(deviceItemList.size());
        pager.setPage(getPageNo());
        pager.setSize(getPageSize());
        return GridUtil.convert(pager, AccDeviceItem.class);
    }

    @Override
    public ZKResultMsg isSupportClearAdministrator(String devIds) {
        ZKResultMsg zkResultMsg = ZKResultMsg.successMsg();
        AccDeviceItem dev = accDeviceService.getItemById(devIds);
        // 清除设备管理员是绿标一体机支持的
        if (dev != null && dev.getMachineType() != AccConstants.DEVICE_ACCESS_CONTROL) {
            zkResultMsg.setRet("400");
            zkResultMsg.setMsg(I18nUtil.i18nCode("acc_dev_devNotSupportFunction"));
        }
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg clearAdministrator(String devIds) {
        return accDeviceService.clearAdministrator(devIds);
    }

    @Override
    public ZKResultMsg setDevIOState() {
        String devId = request.getParameter("devId");
        String devIOState = request.getParameter("InOrOutState");
        if (StringUtils.isNotBlank(devId)) {
            AccDeviceItem device = accDeviceService.getItemById(devId);
            int ret = -1;
            // 发送设置设备进出状态的命令
            Long cmdId = accDeviceService.setDevIOState(devId, devIOState);
            if (cmdId != null && cmdId > 0) {
                Map<String, String> resultMap = accDeviceService.getCmdResultById(cmdId, 20);// 获取命令返回值
                if (Objects.nonNull(resultMap)) {
                    ret = Integer.parseInt(resultMap.get("result"));
                }
            }
            if (ret >= 0) { // 成功设置设备进出状态

                accDeviceOptionService.setDevOptValByName(devId, "Reader1IOState", devIOState);
                // 修改设备底下所有门的主机出入状态
                accDoorService.setDoorHostStatus(devId, devIOState);
            } else {
                throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_ERROR, "common_op_failed");
            }
        }
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @Override
    public ZKResultMsg validDevRegDeviceType(String regDeviceType) {
        ZKResultMsg zkResultMsg = ZKResultMsg.successMsg();
        zkResultMsg.setData(accDeviceService.validDevRegDeviceType(regDeviceType));
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg getRemoteRegistDev() {
        String optionName = request.getParameter("optionName");
        ZKResultMsg zkResultMsg = accDeviceService.getRemoteRegistDev(optionName, request.getSession().getId());
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg remoteRegistration() {
        String devId = request.getParameter("devId");
        String templateType = request.getParameter("templateType");
        String templateNo = request.getParameter("templateNo");
        String pin = request.getParameter("pin");
        Long cmdId = accDeviceService.remoteRegistration(devId, Integer.parseInt(templateType),
            Integer.parseInt(templateNo), pin);
        return new ZKResultMsg(cmdId);
    }

    @Override
    public ZKResultMsg getRemoteTemplate() {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        long sendTime = System.currentTimeMillis();
        Long cmdId = Long.parseLong(request.getParameter("cmdId"));
        if (cmdId != null && cmdId > 0) {
            int ret = -1;
            // 超时设置10秒
            Map<String, String> resultMap = accDeviceService.getCmdResultById(cmdId, 30);// 获取命令返回值
            if (Objects.nonNull(resultMap)) {
                ret = Integer.parseInt(resultMap.get("result"));
            }
            if (ret != 0) {
                zkResultMsg.setRet("400");
                zkResultMsg.setMsg(I18nUtil.i18nCode("base_remote_cancle"));
                if (ret == 6) {
                    zkResultMsg.setMsg(I18nUtil.i18nCode("base_remote_cancle"));
                } else if (ret == 4) {
                    zkResultMsg.setMsg(I18nUtil.i18nCode("base_remote_qualified"));
                } else if (ret == 2 || ret == 5) {
                    zkResultMsg.setMsg(I18nUtil.i18nCode("base_remote_repeat"));
                } else if (ret == -12) {
                    zkResultMsg.setMsg(I18nUtil.i18nCode("base_remote_busyDevice"));
                } else if (ret == -1110 || ret == -1) {
                    zkResultMsg.setMsg(I18nUtil.i18nCode("common_commStatus_cmdTimeOut"));
                }
            } else {
                String personPin = request.getParameter("personPin");
                String bioType = request.getParameter("bioType");
                zkResultMsg = accDeviceService.getRemoteTemplate(personPin, bioType, sendTime);
            }
        }
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg getReadIDCardInfo() {
        ZKResultMsg result = new ZKResultMsg();
        if (StringUtils.isNotBlank(request.getParameter("cmdId"))) {
            String type = request.getParameter("type");
            result = accDeviceService.getReadIDCardInfo(Long.parseLong(request.getParameter("cmdId")), type);
        }
        return result;
    }

    @Override
    public ModelAndView getDevExtendInfo(String ids) {
        AccDeviceItem device = accDeviceService.getItemById(ids);
        short devConnectState = Short.valueOf(accDeviceService.getStatus(device.getSn()));
        if (devConnectState != ConstUtil.DEV_STATE_ONLINE) {
            // 判断离线或禁用状态
            request.setAttribute("failedReason", devConnectState);
            return new ModelAndView("acc/device/opDevNotSupportFeatureTips");
        }
        // 界面显示参数
        List<String> faceUiOption = Arrays.asList(AccConstants.DEV_EXTEND_FACEUI_OPTION.split(","));
        // 人脸参数
        List<String> faceOption = Arrays.asList(AccConstants.DEV_EXTEND_FACEOPTION.split(","));
        // 门禁参数
        List<String> accOption = Arrays.asList(AccConstants.DEV_EXTEND_ACCOPTION.split(","));
        // 可视对讲参数
        //List<String> videoIntercomOption = Arrays.asList(AccConstants.DEV_EXTEND_VIDEOINTERCOMOPTION.split(","));
        List<AccDeviceOptionItem> faceUiOptionItemList = accDeviceOptionService.getDevExtendParams(ids, faceUiOption);
        List<AccDeviceOptionItem> faceOptionItemList = accDeviceOptionService.getDevExtendParams(ids, faceOption);
        List<AccDeviceOptionItem> accOptionItemList = accDeviceOptionService.getDevExtendParams(ids, accOption);
        //List<AccDeviceOptionItem> videoIntercomOptionItemList = accDeviceOptionService.getDevExtendParams(ids, videoIntercomOption);
        boolean isExistFaceUiOption = Objects.nonNull(faceUiOptionItemList) && !faceUiOptionItemList.isEmpty();
        boolean isExistFaceOption = Objects.nonNull(faceOptionItemList) && !faceOptionItemList.isEmpty();
        boolean isExistAccOption = Objects.nonNull(accOptionItemList) && !accOptionItemList.isEmpty();
        //boolean isVideoIntercomOption = Objects.nonNull(videoIntercomOptionItemList) && !videoIntercomOptionItemList.isEmpty();
        if (isExistFaceUiOption || isExistFaceOption || isExistAccOption/* || isVideoIntercomOption*/) {
            Map<String, String> faceUiParamMap = new HashMap<>(16);
            for (AccDeviceOptionItem item : faceUiOptionItemList) {
                faceUiParamMap.put(item.getName(), item.getValue());
            }
            request.setAttribute("faceUiParamMap", faceUiParamMap);
            Map<String, String> faceParamMap = new HashMap<>(16);
            for (AccDeviceOptionItem item : faceOptionItemList) {
                faceParamMap.put(item.getName(), item.getValue());
            }
            request.setAttribute("faceParamMap", faceParamMap);
            Map<String, String> accParamMap = new HashMap<>(16);
            for (AccDeviceOptionItem item : accOptionItemList) {
                accParamMap.put(item.getName(), item.getValue());
            }
            request.setAttribute("accParamMap", accParamMap);

            // vms方案可视对讲才需要在这里配置参数
            /*if ("4".equals(accDeviceOptionService.getValueByNameAndDevSn(device.getSn(), "VideoProtocol"))) {
                Map<String, String> videoIntercomParamMap = new HashMap<>(16);
                for (AccDeviceOptionItem item : videoIntercomOptionItemList) {
                    videoIntercomParamMap.put(item.getName(), item.getValue());
                }
                request.setAttribute("videoIntercomParamMap", videoIntercomParamMap);
            }*/

            request.setAttribute("item", device);
            return new ModelAndView("acc/device/opAccSetExtendParam");
        } else {
            // 不支持扩展功能提示
            request.setAttribute("failedReason", AccConstants.DEV_NOT_SUPPORT_FEATURE);
            return new ModelAndView("acc/device/opDevNotSupportFeatureTips");
        }
    }

    @RequiresPermissions("acc:device:updateDevExtendParam")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "acc_dev_setExtendParam",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg updateDevExtendParam(@RequestParam Map<String, String> params) {
        ZKResultMsg resultMsg = accDeviceService.updateDevExtendParam(params);
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public ModelAndView getNtpServerInfo(String ids) {
        List<AccDeviceItem> accDeviceItemList = accDeviceService.getItemByIds(ids);
        StringBuffer devName = new StringBuffer("");
        StringBuffer setDevice = new StringBuffer("");
        StringBuffer disabledDevName = new StringBuffer("");
        StringBuffer enbaleDevs = new StringBuffer("");
        StringBuffer noSupportDevName = new StringBuffer("");
        String serverAddress = "";
        boolean enableNTP = false;
        int setDevCount = 0;
        for (AccDeviceItem item : accDeviceItemList) {
            String status = accDeviceService.getStatus(item.getSn());
            if (!(String.valueOf(AccConstants.DEV_STATE_DISABLE)).equals(status)) {
                if (StringUtils.isNotBlank(item.getParentDeviceId())) {
                    noSupportDevName.append(item.getAlias()).append(",");
                } else if (accDeviceOptionService.getAccSupportFunListVal(item.getId(), 61)) {
                    String tempServerAddress = accDeviceService.getDevNtpById(item.getId());
                    enableNTP = accDeviceOptionService.isSupportDevParam(item.getSn(), AccConstants.NTP_FUN_ON);
                    enbaleDevs.append(item.getId()).append(",");
                    if (enableNTP || StringUtils.isNotBlank(tempServerAddress)) {
                        setDevice.append(item.getAlias()).append("&").append(item.getId()).append(",");
                        if (setDevCount == 0) {
                            serverAddress = tempServerAddress;
                        }
                        setDevCount++;
                    } else {
                        devName.append(item.getAlias()).append("&").append(item.getId()).append(",");
                    }
                } else {
                    noSupportDevName.append(item.getAlias()).append(",");
                }
            } else {
                disabledDevName.append(item.getAlias()).append(",");
            }
        }
        StringBuffer devNameStr = new StringBuffer();
        if (devName.length() > 0) {
            devNameStr.append(I18nUtil.i18nCode("acc_common_notSet")).append(":")
                .append(devName.substring(0, devName.length() - 1)).append(";");
        }
        if (setDevice.length() > 0) {
            if (setDevCount == 1) {
                request.setAttribute("serverAddress", serverAddress);
                request.setAttribute("enableNTP", enableNTP ? "1" : "0");
            }
            devNameStr.append(I18nUtil.i18nCode("acc_common_hasBeanSet")).append(":").append(setDevice);
        }
        // 已设置和未设置的列表
        request.setAttribute("devNameStr",
            devNameStr.length() > 0 ? devNameStr.substring(0, devNameStr.length() - 1) : "");
        request.setAttribute("disabledDevName",
            disabledDevName.length() > 0 ? disabledDevName.substring(0, disabledDevName.length() - 1) : "");
        request.setAttribute("ids", enbaleDevs.length() > 0 ? enbaleDevs.substring(0, enbaleDevs.length() - 1) : "");
        request.setAttribute("noSupportDevName",
            noSupportDevName.length() > 0 ? noSupportDevName.substring(0, noSupportDevName.length() - 1) : "");
        request.setAttribute("editPage", false);
        return new ModelAndView("acc/device/setAccDeviceNtpServer");
    }

    @Override
    public void setNtpServer(String ids, Short status, String serverAddress) {
        accDeviceService.setNtpServer(ids, status, serverAddress);
        progressCache.finishProcess(I18nUtil.i18nCode("common_dev_cmdSendSucceed"));
    }

    @RequiresPermissions("acc:device:replace")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "acc_dev_replace",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg replaceDevInfo(String newSn) {
        String devId = request.getParameter("devId");
        accDeviceService.replaceDevInfo(devId, newSn);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public String validSn(String newSn) {
        boolean rs = accDeviceService.validSn(newSn);
        return rs + "";
    }

    @Override
    public TreeItem devSnTree() {
        List<TreeItem> items = new ArrayList<>();
        List<AccDeviceItem> accDeviceItemList = accDeviceService.getByCondition(new AccDeviceItem());
        if (!accDeviceItemList.isEmpty()) {
            TreeItem item = null;
            for (AccDeviceItem accDeviceItem : accDeviceItemList) {
                item = new TreeItem();
                item.setId(accDeviceItem.getSn());
                item.setText(accDeviceItem.getAlias());
                item.setParent(new TreeItem("0"));
                items.add(item);
            }
        }
        List<TreeItem> treeItems = TreeBuilder.newTreeBuilder(TreeItem.class, String.class).buildToTreeList(items);
        return new TreeItem("0", treeItems);
    }

    @RequiresPermissions("acc:device:clearCmdCache")
    @LogRequest(module = "acc_module", object = "common_leftMenu_device", opType = "common_devMonitor_clearCmdCache",
        requestParams = {"alias"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg clearCmdCache(String ids) {
        accDeviceService.clearCmdCacheByIds(ids);
        ZKResultMsg resultMsg = new ZKResultMsg();
        // 清除缓存命令成功！请及时手动同步数据到设备，以确保系统中和设备中权限一致！
        resultMsg.setMsg("common_devMonitor_clearCmdSuccess");
        return resultMsg;
    }

    @Override
    public ZKResultMsg isSupportReplaceDevice(String devIds) {
        ZKResultMsg zkResultMsg = ZKResultMsg.successMsg();
        AccDeviceItem dev = accDeviceService.getItemById(devIds);
        // pull设备不支持替换设备功能
        if (dev != null && dev.getCommType() != AccConstants.COMM_HTTP) {
            zkResultMsg.setRet("400");
            zkResultMsg.setMsg(I18nUtil.i18nCode("acc_dev_devNotSupportFunction"));
        }
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg validAddPushDev(String sn, String machineType, String isSupportNVR) {
        ZKResultMsg resultMsg = accDeviceService.validPushDevCount(sn, machineType);
        if (resultMsg.getRet().equals("ok") || resultMsg.getRet().equals("message")) {
            if (StringUtils.isNotBlank(isSupportNVR) && "true".equals(isSupportNVR)) {
                String userName = request.getParameter("accUsername");
                String userPassword = request.getParameter("accPassword");
                String ipAddress = request.getParameter("alias");
                ZKResultMsg validUserMsg = accDeviceService.validIvsUerInfo(userName, userPassword, ipAddress);
                if (!validUserMsg.isSuccess()) {
                    resultMsg.setRet("nvrFail");
                    resultMsg.setMsg(I18nUtil.i18nCode("acc_device_validFail"));
                    return resultMsg;
                }
            }
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @RequiresPermissions("acc:device:setResourceFile")
    @Override
    public ModelAndView setResourceFile(String ids, String type) {
        return getById(ids, type);
    }

    @Override
    public ZKResultMsg getAdResourceFile() {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        List<String> fileTypes = new ArrayList<>();
        fileTypes.add("1");
        fileTypes.add("2");
        zkResultMsg.setData(accDeviceService.getNamesByfileTypes(fileTypes));
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @RequiresPermissions("acc:device:setResourceFile")
    @Override
    public ZKResultMsg uploadAdResourceFile(Acc4UploadAdFileItem item) {
        accDeviceService.uploadAdResourceFile(item);
        return new ZKResultMsg();
    }

    @Override
    public ZKResultMsg getDevLinkIPC(String devSn) {
        return new ZKResultMsg(accDeviceService.getDevLinkIPC(devSn));
    }

    @Override
    public ModelAndView getFaceVerifyServerInfo(String ids) {
        List<AccDeviceItem> accDeviceItemList = accDeviceService.getItemByIds(ids);
        StringBuilder devName = new StringBuilder();
        StringBuilder disabledDevName = new StringBuilder();
        StringBuilder enableDevs = new StringBuilder();
        StringBuilder noSupportDevName = new StringBuilder();
        int setDevCount = 0;
        for (AccDeviceItem item : accDeviceItemList) {
            String status = accDeviceService.getStatus(item.getSn());
            if (!(String.valueOf(AccConstants.DEV_STATE_DISABLE)).equals(status)) {
                if (StringUtils.isNotBlank(item.getParentDeviceId())) {
                    noSupportDevName.append(item.getAlias()).append(",");
                } else if (accDeviceOptionService.isSupportFun(item.getSn(), "FaceBgVerifySupport")) {
                    setDevCount++;
                    enableDevs.append(item.getId()).append(",");
                    devName.append(item.getAlias()).append(",");
                } else {
                    noSupportDevName.append(item.getAlias()).append(",");
                }
            } else {
                disabledDevName.append(item.getAlias()).append(",");
            }
        }
        if (setDevCount == 1) {
            String devId = enableDevs.substring(0, enableDevs.length() - 1);
            AccDeviceOptionItem faceVerifyMode = accDeviceOptionService.getItemByDevIdAndName(devId, "FaceVerifyMode");
            if (faceVerifyMode != null && StringUtils.isNotBlank(faceVerifyMode.getValue())) {
                request.setAttribute("faceVerifyMode", faceVerifyMode.getValue());
            }
            AccDeviceOptionItem faceBgServerType = accDeviceOptionService.getItemByDevIdAndName(devId, "FaceBgServerType");
            if (faceBgServerType != null && StringUtils.isNotBlank(faceBgServerType.getValue())) {
                request.setAttribute("faceBgServerType", faceBgServerType.getValue());
            }
            AccDeviceOptionItem isAccessLogic = accDeviceOptionService.getItemByDevIdAndName(devId, "IsAccessLogicFunOn");
            if (isAccessLogic != null && StringUtils.isNotBlank(isAccessLogic.getValue())) {
                request.setAttribute("isAccessLogic", isAccessLogic.getValue());
            }
            AccDeviceOptionItem faceThreshold = accDeviceOptionService.getItemByDevIdAndName(devId, "FaceThreshold");
            if (faceThreshold != null && StringUtils.isNotBlank(faceThreshold.getValue())) {
                request.setAttribute("faceThreshold", faceThreshold.getValue());
            }
            AccDeviceOptionItem faceBgServerURL = accDeviceOptionService.getItemByDevIdAndName(devId, "FaceBgServerURL");
            if (faceBgServerURL != null && StringUtils.isNotBlank(faceBgServerURL.getValue())) {
                request.setAttribute("faceBgServerURL", faceBgServerURL.getValue());
            }
            AccDeviceOptionItem faceBgServerSecret = accDeviceOptionService.getItemByDevIdAndName(devId, "FaceBgServerSecret");
            if (faceBgServerSecret != null && StringUtils.isNotBlank(faceBgServerSecret.getValue())) {
                request.setAttribute("faceBgServerSecret", faceBgServerSecret.getValue());
            }
        }
        // 已设置和未设置的列表
        request.setAttribute("devNameStr", devName.length() > 0 ? devName.substring(0, devName.length() - 1) : "");
        request.setAttribute("disabledDevName",
                disabledDevName.length() > 0 ? disabledDevName.substring(0, disabledDevName.length() - 1) : "");
        request.setAttribute("ids", enableDevs.length() > 0 ? enableDevs.substring(0, enableDevs.length() - 1) : "");
        request.setAttribute("noSupportDevName",
                noSupportDevName.length() > 0 ? noSupportDevName.substring(0, noSupportDevName.length() - 1) : "");
        request.setAttribute("editPage", false);
        return new ModelAndView("acc/device/opAccSetFaceVerifyServer");
    }

    @Override
    public void setFaceVerifyServer(Acc4SetFaceVerifyServer item) {
        accDeviceService.setFaceVerifyServer(item);
        progressCache.finishProcess(I18nUtil.i18nCode("common_dev_cmdSendSucceed"));
    }
}