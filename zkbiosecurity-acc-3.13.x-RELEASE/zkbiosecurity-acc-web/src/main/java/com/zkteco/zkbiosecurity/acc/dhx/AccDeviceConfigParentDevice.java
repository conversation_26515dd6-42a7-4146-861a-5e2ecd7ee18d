/*
 * File Name: AccDeviceConfigParentDevice
 * <NAME_EMAIL> on 2018/5/25 16:49.
 * Copyright:Copyright © 1999-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.dhx;

import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.core.utils.ShowGridColumn;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
@Component
public class AccDeviceConfigParentDevice implements ShowGridColumn {

    @Autowired
    private AccDeviceOptionService accDeviceOptionService;

    @Override
    public boolean isShow(Object obj) {
        AccDeviceItem accDeviceItem = (AccDeviceItem)obj;
        if (accDeviceItem.getParentDeviceId() == null || "".equals(accDeviceItem.getParentDeviceId())) {
            return accDeviceOptionService.isSupportFunList(accDeviceItem.getSn(), 31);
        }
        return false;
    }
}
