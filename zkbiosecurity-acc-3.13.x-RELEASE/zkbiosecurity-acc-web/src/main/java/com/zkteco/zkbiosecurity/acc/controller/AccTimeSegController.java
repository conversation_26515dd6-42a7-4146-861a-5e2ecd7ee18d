package com.zkteco.zkbiosecurity.acc.controller;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.remote.AccTimeSegRemote;
import com.zkteco.zkbiosecurity.acc.service.AccTimeSegService;
import com.zkteco.zkbiosecurity.acc.vo.AccTimeSegItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 门禁时间段controller
 * 
 * @author: yulong.dai
 * @date: 2018-02-24 上午10:18
 */
@Controller
public class AccTimeSegController extends BaseController implements AccTimeSegRemote {
    @Autowired
    private AccTimeSegService accTimeSegService;

    @RequiresPermissions("acc:timeSeg")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/timeSeg/accTimeSeg");
    }

    @RequiresPermissions({"acc:timeSeg:edit", "acc:timeSeg:add"})
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("tempAccTimeSeg", accTimeSegService.getItemById(id));
        }
        return new ModelAndView("acc/timeSeg/editAccTimeSeg");
    }

    @RequiresPermissions({"acc:timeSeg:edit", "acc:timeSeg:add"})
    @LogRequest(module = "acc_module", object = "common_leftMenu_timeZone", opType = "common_op_edit",
        requestParams = {"name"}, opContent = "common_timeSeg_name")
    @Override
    public ZKResultMsg save(AccTimeSegItem item) {
        accTimeSegService.saveItem(item);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:timeSeg:refresh")
    @Override
    public DxGrid list(AccTimeSegItem codition) {
        Pager pager = accTimeSegService.getItemsByPage(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:timeSeg:del")
    @LogRequest(module = "acc_module", object = "common_leftMenu_timeZone", opType = "common_op_del",
        requestParams = {"names"}, opContent = "common_timeSeg_name")
    @Override
    public ZKResultMsg del(String ids) {
        accTimeSegService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public String validName(String name) {
        return accTimeSegService.validName(name);
    }

    @Override
    public ZKResultMsg getTimeSegList() {
        return new ZKResultMsg(accTimeSegService.getTimeSegList());
    }

    @Override
    public ZKResultMsg dataValid(AccTimeSegItem accTimeSegItem) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        String result = "true";
        Date startTime = accTimeSegItem.getStartTime();
        Date endTime = accTimeSegItem.getEndTime();
        // 开始日期不能大于结束日期
        if (startTime != null && endTime != null && startTime.getTime() > accTimeSegItem.getEndTime().getTime()) {
            resultMsg.setData(I18nUtil.i18nCode("common_startEndDateCompare"));
            return I18nUtil.i18nMsg(resultMsg);
        }

        String checkResult =
            checkTimeseg(accTimeSegItem.getSundayStart1(), I18nUtil.i18nCode("common_timeSeg_sundayStart1"),
                accTimeSegItem.getSundayEnd1(), I18nUtil.i18nCode("common_timeSeg_sundayEnd1"),
                accTimeSegItem.getSundayStart2(), I18nUtil.i18nCode("common_timeSeg_sundayStart2"),
                accTimeSegItem.getSundayEnd2(), I18nUtil.i18nCode("common_timeSeg_sundayEnd2"),
                accTimeSegItem.getSundayStart3(), I18nUtil.i18nCode("common_timeSeg_sundayStart3"),
                accTimeSegItem.getSundayEnd3(), I18nUtil.i18nCode("common_timeSeg_sundayEnd3"));
        if (checkResult != null) {
            resultMsg.setData(checkResult);
            return I18nUtil.i18nMsg(resultMsg);
        }

        checkResult = checkTimeseg(accTimeSegItem.getMondayStart1(), I18nUtil.i18nCode("common_timeSeg_mondayStart1"),
            accTimeSegItem.getMondayEnd1(), I18nUtil.i18nCode("common_timeSeg_mondayEnd1"),
            accTimeSegItem.getMondayStart2(), I18nUtil.i18nCode("common_timeSeg_mondayStart2"),
            accTimeSegItem.getMondayEnd2(), I18nUtil.i18nCode("common_timeSeg_mondayEnd2"),
            accTimeSegItem.getMondayStart3(), I18nUtil.i18nCode("common_timeSeg_mondayStart3"),
            accTimeSegItem.getMondayEnd3(), I18nUtil.i18nCode("common_timeSeg_mondayEnd3"));
        if (checkResult != null) {
            resultMsg.setData(checkResult);
            return I18nUtil.i18nMsg(resultMsg);
        }

        checkResult = checkTimeseg(accTimeSegItem.getTuesdayStart1(), I18nUtil.i18nCode("common_timeSeg_tuesdayStart1"),
            accTimeSegItem.getTuesdayEnd1(), I18nUtil.i18nCode("common_timeSeg_tuesdayEnd1"),
            accTimeSegItem.getTuesdayStart2(), I18nUtil.i18nCode("common_timeSeg_tuesdayStart2"),
            accTimeSegItem.getTuesdayEnd2(), I18nUtil.i18nCode("common_timeSeg_tuesdayEnd2"),
            accTimeSegItem.getTuesdayStart3(), I18nUtil.i18nCode("common_timeSeg_tuesdayStart3"),
            accTimeSegItem.getTuesdayEnd3(), I18nUtil.i18nCode("common_timeSeg_tuesdayEnd3"));
        if (checkResult != null) {
            resultMsg.setData(checkResult);
            return I18nUtil.i18nMsg(resultMsg);
        }

        checkResult =
            checkTimeseg(accTimeSegItem.getWednesdayStart1(), I18nUtil.i18nCode("common_timeSeg_wednesdayStart1"),
                accTimeSegItem.getWednesdayEnd1(), I18nUtil.i18nCode("common_timeSeg_wednesdayEnd1"),
                accTimeSegItem.getWednesdayStart2(), I18nUtil.i18nCode("common_timeSeg_wednesdayStart2"),
                accTimeSegItem.getWednesdayEnd2(), I18nUtil.i18nCode("common_timeSeg_wednesdayEnd2"),
                accTimeSegItem.getWednesdayStart3(), I18nUtil.i18nCode("common_timeSeg_wednesdayStart3"),
                accTimeSegItem.getWednesdayEnd3(), I18nUtil.i18nCode("common_timeSeg_wednesdayEnd3"));
        if (checkResult != null) {
            resultMsg.setData(checkResult);
            return I18nUtil.i18nMsg(resultMsg);
        }

        checkResult =
            checkTimeseg(accTimeSegItem.getThursdayStart1(), I18nUtil.i18nCode("common_timeSeg_thursdayStart1"),
                accTimeSegItem.getThursdayEnd1(), I18nUtil.i18nCode("common_timeSeg_thursdayEnd1"),
                accTimeSegItem.getThursdayStart2(), I18nUtil.i18nCode("common_timeSeg_thursdayStart2"),
                accTimeSegItem.getThursdayEnd2(), I18nUtil.i18nCode("common_timeSeg_thursdayEnd2"),
                accTimeSegItem.getThursdayStart3(), I18nUtil.i18nCode("common_timeSeg_thursdayStart3"),
                accTimeSegItem.getThursdayEnd3(), I18nUtil.i18nCode("common_timeSeg_thursdayEnd3"));
        if (checkResult != null) {
            resultMsg.setData(checkResult);
            return I18nUtil.i18nMsg(resultMsg);
        }

        checkResult = checkTimeseg(accTimeSegItem.getFridayStart1(), I18nUtil.i18nCode("common_timeSeg_fridayStart1"),
            accTimeSegItem.getFridayEnd1(), I18nUtil.i18nCode("common_timeSeg_fridayEnd1"),
            accTimeSegItem.getFridayStart2(), I18nUtil.i18nCode("common_timeSeg_fridayStart2"),
            accTimeSegItem.getFridayEnd2(), I18nUtil.i18nCode("common_timeSeg_fridayEnd2"),
            accTimeSegItem.getFridayStart3(), I18nUtil.i18nCode("common_timeSeg_fridayStart3"),
            accTimeSegItem.getFridayEnd3(), I18nUtil.i18nCode("common_timeSeg_fridayEnd3"));
        if (checkResult != null) {
            resultMsg.setData(checkResult);
            return I18nUtil.i18nMsg(resultMsg);
        }

        checkResult =
            checkTimeseg(accTimeSegItem.getSaturdayStart1(), I18nUtil.i18nCode("common_timeSeg_saturdayStart1"),
                accTimeSegItem.getSaturdayEnd1(), I18nUtil.i18nCode("common_timeSeg_saturdayEnd1"),
                accTimeSegItem.getSaturdayStart2(), I18nUtil.i18nCode("common_timeSeg_saturdayStart2"),
                accTimeSegItem.getSaturdayEnd2(), I18nUtil.i18nCode("common_timeSeg_saturdayEnd2"),
                accTimeSegItem.getSaturdayStart3(), I18nUtil.i18nCode("common_timeSeg_saturdayStart3"),
                accTimeSegItem.getSaturdayEnd3(), I18nUtil.i18nCode("common_timeSeg_saturdayEnd3"));
        if (checkResult != null) {
            resultMsg.setData(checkResult);
            return I18nUtil.i18nMsg(resultMsg);
        }

        checkResult =
            checkTimeseg(accTimeSegItem.getHolidayType1Start1(), I18nUtil.i18nCode("common_timeSeg_holidaytype1Start1"),
                accTimeSegItem.getHolidayType1End1(), I18nUtil.i18nCode("common_timeSeg_holidaytype1End1"),
                accTimeSegItem.getHolidayType1Start2(), I18nUtil.i18nCode("common_timeSeg_holidaytype1Start2"),
                accTimeSegItem.getHolidayType1End2(), I18nUtil.i18nCode("common_timeSeg_holidaytype1End2"),
                accTimeSegItem.getHolidayType1Start3(), I18nUtil.i18nCode("common_timeSeg_holidaytype1Start3"),
                accTimeSegItem.getHolidayType1End3(), I18nUtil.i18nCode("common_timeSeg_holidaytype1End3"));
        if (checkResult != null) {
            resultMsg.setData(checkResult);
            return I18nUtil.i18nMsg(resultMsg);
        }

        checkResult =
            checkTimeseg(accTimeSegItem.getHolidayType2Start1(), I18nUtil.i18nCode("common_timeSeg_holidaytype2Start1"),
                accTimeSegItem.getHolidayType2End1(), I18nUtil.i18nCode("common_timeSeg_holidaytype2End1"),
                accTimeSegItem.getHolidayType2Start2(), I18nUtil.i18nCode("common_timeSeg_holidaytype2Start2"),
                accTimeSegItem.getHolidayType2End2(), I18nUtil.i18nCode("common_timeSeg_holidaytype2End2"),
                accTimeSegItem.getHolidayType2Start3(), I18nUtil.i18nCode("common_timeSeg_holidaytype2Start3"),
                accTimeSegItem.getHolidayType2End3(), I18nUtil.i18nCode("common_timeSeg_holidaytype2End3"));
        if (checkResult != null) {
            resultMsg.setData(checkResult);
            return I18nUtil.i18nMsg(resultMsg);
        }

        checkResult =
            checkTimeseg(accTimeSegItem.getHolidayType3Start1(), I18nUtil.i18nCode("common_timeSeg_holidaytype3Start1"),
                accTimeSegItem.getHolidayType3End1(), I18nUtil.i18nCode("common_timeSeg_holidaytype3End1"),
                accTimeSegItem.getHolidayType3Start2(), I18nUtil.i18nCode("common_timeSeg_holidaytype3Start2"),
                accTimeSegItem.getHolidayType3End2(), I18nUtil.i18nCode("common_timeSeg_holidaytype3End2"),
                accTimeSegItem.getHolidayType3Start3(), I18nUtil.i18nCode("common_timeSeg_holidaytype3Start3"),
                accTimeSegItem.getHolidayType3End3(), I18nUtil.i18nCode("common_timeSeg_holidaytype3End3"));
        if (checkResult != null) {
            resultMsg.setData(checkResult);
            return I18nUtil.i18nMsg(resultMsg);
        }

        resultMsg.setData(result);
        return I18nUtil.i18nMsg(resultMsg);
    }

    /**
     * 用于验证时间段的有效性的算法
     * 
     * <AUTHOR>
     * @date 2018/5/25 14:37
     * @param start1 所验证某天时间段的第一区间的开始时间
     * @param start1Vname 第一时间区间开始时间名称，例如"${common_timeSeg_mondayStart1}"
     * @param end1 所验证某天时间段的第一区间的结束时间
     * @param end1Vname 第一时间区间结束时间名称，例如"${common_timeSeg_mondayEnd1}"
     * @param start2 所验证某天时间段的第二区间的开始时间
     * @param start2Vname 第二时间区间开始时间名称
     * @param end2 所验证某天时间段的第二区间的结束时间
     * @param end2Vname 第二时间区间结束时间名称
     * @param start3 所验证某天时间段的第三区间的开始时间
     * @param start3Vname 第三时间区间开始时间名称
     * @param end3 所验证某天时间段的第三区间的结束时间
     * @param end3Vname 第三时间区间结束时间名称
     * @return String
     */
    private String checkTimeseg(String start1, String start1Vname, String end1, String end1Vname, String start2,
        String start2Vname, String end2, String end2Vname, String start3, String start3Vname, String end3,
        String end3Vname) {
        String checkResult = checkStartEndTime(start1, start1Vname, end1, end1Vname);
        if (checkResult != null) {
            return checkResult;
        }
        checkResult = checkStartEndTime(start2, start2Vname, end2, end2Vname);
        if (checkResult != null) {
            return checkResult;
        }
        checkResult = checkStartEndTime(start3, start3Vname, end3, end3Vname);
        if (checkResult != null) {
            return checkResult;
        }

        // 处理时间区间间的有效性-下一个时区的开始时间要大于前一个的结束时间（都为0时可以等于），后面的全为0， 那么也可能小于
        if (start1.equals("00:00") && end1.equals("00:00")) {
            if (!start2.equals("00:00")) {
                return I18nUtil.i18nCode("common_timeSeg_fillError1", start2Vname);// %s 填写错误，请先使用时间区间1
            }
            if (!end2.equals("00:00")) {
                return I18nUtil.i18nCode("common_timeSeg_fillError1", end2Vname);// %s 填写错误，请先使用时间区间1
            }
            if (!start3.equals("00:00")) {
                return I18nUtil.i18nCode("common_timeSeg_fillError1", start3Vname);// %s 填写错误，请先使用时间区间1
            }
            if (!end3.equals("00:00")) {
                return I18nUtil.i18nCode("common_timeSeg_fillError1", end3Vname);// %s 填写错误，请先使用时间区间1
            }
        } else if (start2.toString().equals("00:00") && end2.toString().equals("00:00")) {
            // 时间区间1使用且合法（已check）二全为0，则不能使用三
            if (!start3.toString().equals("00:00")) {
                return I18nUtil.i18nCode("common_timeSeg_fillError2", start3Vname);// %s 填写错误，请先使用时间区间2
            }
            if (!end3.toString().equals("00:00")) {
                return I18nUtil.i18nCode("common_timeSeg_fillError2", end3Vname);// %s 填写错误，请先使用时间区间2
            }
        }
        // 2也使用了(1的结尾此时肯定不为0,2的结尾也肯定不为0)
        else if (DateUtil.stringToDate(start2, DateUtil.DateStyle.HH_MM).getTime() <= DateUtil
            .stringToDate(end1, DateUtil.DateStyle.HH_MM).getTime()) {
            return I18nUtil.i18nCode("common_timeSeg_fillError3", start2Vname, end1Vname);// %s 不能小于等于 %s
        }
        // 使用全部三个区间时，验证第三个时间区间的有效性。#start3 != zero and 去掉该条件，否则会出现时间区间1时间设置为09：:0-12:00
        // 时间区间2设置为12:01-18:00，时间区间3可以设置为00：00-10:00
        else if (!end3.equals("00:00") && DateUtil.stringToDate(start3, DateUtil.DateStyle.HH_MM).getTime() <= DateUtil
            .stringToDate(end2, DateUtil.DateStyle.HH_MM).getTime()) {
            return I18nUtil.i18nCode("common_timeSeg_fillError3", start3Vname, end2Vname);// %s 不能小于等于 %s
        }
        // 可以选择只使用前两个时间区间，最后一个留空。
        return null;
    }

    /**
     * @Description: 用户验证两个时间点的有效性（开始时间和结束时间）
     * <AUTHOR>
     * @date 2018/5/25 14:37
     * @param startTime 时间区间开始时间
     * @param startVname 时间区间开始时间名称
     * @param endTime 时间区间结束时间
     * @param endVname 时间区间结束时间名称
     * @return
     */
    private String checkStartEndTime(String startTime, String startVname, String endTime, String endVname) {
        Date startDate = DateUtil.stringToDate(startTime, DateUtil.DateStyle.HH_MM);
        Date endDate = DateUtil.stringToDate(endTime, DateUtil.DateStyle.HH_MM);
        if (startDate.getTime() > endDate.getTime()) {
            return I18nUtil.i18nCode("common_timeSeg_fillError4", startVname, endVname);// %s 不能大于 %s
        } else if (startDate.getTime() == endDate.getTime() && !startTime.toString().equals("00:00")) {
            return I18nUtil.i18nCode("common_timeSeg_fillError5", startVname, endVname);// %s 不能等于 %s
        }
        return null;
    }
}