package com.zkteco.zkbiosecurity.acc.api.controller;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zkteco.zkbiosecurity.acc.api.vo.AccApiDoorItem;
import com.zkteco.zkbiosecurity.acc.api.vo.AccApiDoorStateItem;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.utils.AccEnumUtil;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 门禁门接口
 *
 * <AUTHOR>
 * @Date: 2018/11/13 09:34
 */
@Controller
@RequestMapping(value = {"/api/door"})
@Slf4j
@Api(tags = "AccDoor", description = "acc door")
public class AccApiDoorController {

    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccRTMonitorService accRTMonitorService;
    @Autowired
    private AccBaseDictionaryService accBaseDictionaryService;
    @Autowired
    private AccCloudService accCloudService;

    /**
     * 根据门ID获取门信息
     *
     * @param id
     * @return
     * @auther lambert.li
     * @date 2018/11/13 15:12
     */
    @ApiOperation(value = "Get Door By Id", notes = "Return Door Object", response = ApiResultMessage.class)
    @RequestMapping(value = {"/get"}, method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"id"})
    public ApiResultMessage getDoorById(@RequestParam(name = "id", required = false) String id) {
        if (StringUtils.isBlank(id)) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_DOORIDNOTNULL,
                I18nUtil.i18nCode("acc_api_doorIdNotNull"));
        }
        ApiResultMessage rs = ApiResultMessage.successMessage();
        AccDoorItem accDoorItem = accDoorService.getItemById(id);
        if (accDoorItem != null) {
            rs.setData(AccApiDoorItem.createApiDoor(accDoorItem));
        }
        return rs;
    }

    /**
     * 分页获取门列表
     *
     * @param pageNo
     * @param pageSize
     * @return
     * @auther lambert.li
     * @date 2018/11/13 15:12
     */
    @ResponseBody
    @RequestMapping(value = {"/list"}, method = RequestMethod.GET, produces = "application/json")
    @ApiOperation(value = "Get Door List", notes = "Return Door List", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"pageNo", "pageSize"})
    public ApiResultMessage list(@RequestParam(name = "pageNo", required = false) Integer pageNo,
        @RequestParam(name = "pageSize", required = false) Integer pageSize) {
        if (pageNo == null || pageSize == null) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE,
                I18nUtil.i18nCode("acc_api_parameterValueCannotBeNull"));
        }
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        if (pageSize > 1000) {
            return ApiResultMessage.message(AccConstants.API_PAGE_OVERSIZE,
                I18nUtil.i18nCode("common_api_pageSizeOver"));
        }
        List<AccApiDoorItem> apiDoorItemList = Lists.newArrayList();
        List<AccDoorItem> accDoorItemList =
            (List<AccDoorItem>)accDoorService.getItemsByPage(new AccDoorItem(), pageNo - 1, pageSize).getData();
        if (!accDoorItemList.isEmpty()) {
            accDoorItemList.forEach(accDoorItem -> {
                AccApiDoorItem accApiDoorItem = AccApiDoorItem.createApiDoor(accDoorItem);
                if (accApiDoorItem != null) {
                    apiDoorItemList.add(accApiDoorItem);
                }
            });
        }
        return ApiResultMessage.successMessage(apiDoorItemList);
    }

    /**
     * 根据门ID远程开门
     *
     * @param doorId
     * @param interval
     * @return
     * @auther lambert.li
     * @date 2018/11/13 15:22
     */
    @ResponseBody
    @RequestMapping(value = "/remoteOpenById", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Remote Open By Id", notes = "Return Result Object", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"doorId", "interval"})
    public ApiResultMessage remoteOpenById(@RequestParam(name = "doorId", required = false) String doorId,
        @RequestParam(name = "interval", required = false) Integer interval) {
        if (StringUtils.isBlank(doorId)) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_DOORIDNOTNULL,
                I18nUtil.i18nCode("acc_api_doorIdNotNull"));
        } else if (interval == null) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_INTERVAL_SIZE,
                I18nUtil.i18nCode("acc_api_openingTimeCannotBeNull"));
        } else if (1 > interval || interval > 254) {// 开门时长在1~254之间
            return ApiResultMessage.message(AccConstants.ACC_DOOR_INTERVAL_SIZE,
                I18nUtil.i18nCode("acc_api_doorIntervalSize"));
        }
        AccDoorItem accDoorItem = accDoorService.getItemById(doorId);
        if (accDoorItem == null) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_NOTEXIST, I18nUtil.i18nCode("acc_api_doorNotExist"));
        }
        String[] ids = doorId.split(",");
        Map<String, String> doorState = accDoorService.getDoorIds(ids, "openDoor");
        if (doorState.containsKey("disabledDoorsName") && StringUtils.isNotBlank(doorState.get("disabledDoorsName"))) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_OFFLINE, I18nUtil.i18nCode("acc_api_doorOffline"));
        } else if (doorState.containsKey("offlineDoorsName")
            && StringUtils.isNotBlank(doorState.get("offlineDoorsName"))) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_OFFLINE, I18nUtil.i18nCode("acc_api_doorOffline"));
        }
        Map<String, String> dataMap = accRTMonitorService.operateDoor("openDoor", interval.toString(), doorId);
        if (dataMap == null) {
            return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("common_api_programError"));
        }
        return dealResultData(dataMap);
    }

    /**
     * 根据门名称远程开门
     *
     * @param doorName
     * @param interval
     * @return
     * @auther lambert.li
     * @date 2018/11/13 15:22
     */
    @ResponseBody
    @RequestMapping(value = "/remoteOpenByName", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Remote Open By Name", notes = "Return Result Object", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"doorName", "interval"})
    public ApiResultMessage remoteOpenByName(@RequestParam(name = "doorName", required = false) String doorName,
        @RequestParam(name = "interval", required = false) Integer interval) {
        if (StringUtils.isBlank(doorName)) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_DOORNAMENOTNULL,
                I18nUtil.i18nCode("acc_api_doorNameNotNull"));
        } else if (interval == null) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_INTERVAL_SIZE,
                I18nUtil.i18nCode("acc_api_openingTimeCannotBeNull"));
        } else if (1 > interval || interval > 254) {// 开门时长在1~254之间
            return ApiResultMessage.message(AccConstants.ACC_DOOR_INTERVAL_SIZE,
                I18nUtil.i18nCode("acc_api_doorIntervalSize"));
        }
        // acc_api_levelNotExist
        AccDoorItem accDoorItem = accDoorService.getItemByName(doorName);
        if (accDoorItem == null) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_NOTEXIST, I18nUtil.i18nCode("acc_api_doorNotExist"));
        }
        String[] ids = accDoorItem.getId().split(",");
        Map<String, String> doorState = accDoorService.getDoorIds(ids, "openDoor");
        if (doorState.containsKey("disabledDoorsName") && StringUtils.isNotBlank(doorState.get("disabledDoorsName"))) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_OFFLINE, I18nUtil.i18nCode("acc_api_doorOffline"));
        } else if (doorState.containsKey("offlineDoorsName")
            && StringUtils.isNotBlank(doorState.get("offlineDoorsName"))) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_OFFLINE, I18nUtil.i18nCode("acc_api_doorOffline"));
        }
        Map<String, String> dataMap = accRTMonitorService.operateDoor("openDoor", interval.toString(), accDoorItem.getId());
        if (dataMap == null) {
            return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("common_api_programError"));
        }
        return dealResultData(dataMap);
    }

    /**
     * 根据门ID远程关门
     *
     * @param doorId
     * @return
     * @auther lambert.li
     * @date 2018/11/13 15:40
     */
    @ResponseBody
    @RequestMapping(value = "/remoteCloseById", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Remote Close By Id", notes = "Return Result Object", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"doorId"})
    public ApiResultMessage remoteCloseById(@RequestParam(name = "doorId", required = false) String doorId) {
        if (StringUtils.isBlank(doorId)) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_DOORIDNOTNULL,
                I18nUtil.i18nCode("acc_api_doorIdNotNull"));
        }
        AccDoorItem accDoorItem = accDoorService.getItemById(doorId);
        if (accDoorItem == null) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_NOTEXIST, I18nUtil.i18nCode("acc_api_doorNotExist"));
        }
        String[] ids = doorId.split(",");
        Map<String, String> doorState = accDoorService.getDoorIds(ids, "openDoor");
        if (doorState.containsKey("disabledDoorsName") && StringUtils.isNotBlank(doorState.get("disabledDoorsName"))) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_OFFLINE, I18nUtil.i18nCode("acc_api_doorOffline"));
        } else if (doorState.containsKey("offlineDoorsName")
            && StringUtils.isNotBlank(doorState.get("offlineDoorsName"))) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_OFFLINE, I18nUtil.i18nCode("acc_api_doorOffline"));
        }
        Map<String, String> dataMap = accRTMonitorService.operateDoor("closeDoor", null, doorId);
        if (dataMap == null) {
            return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("common_api_programError"));
        }
        return dealResultData(dataMap);
    }

    /**
     * 根据门名称远程关门
     *
     * @param doorName
     * @return
     * @auther lambert.li
     * @date 2018/11/13 15:40
     */
    @ResponseBody
    @RequestMapping(value = "/remoteCloseByName", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Remote Close By Name", notes = "Return Result Object", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"doorName"})
    public ApiResultMessage remoteCloseByName(@RequestParam(name = "doorName", required = false) String doorName) {
        if (StringUtils.isBlank(doorName)) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_DOORNAMENOTNULL,
                I18nUtil.i18nCode("acc_api_doorNameNotNull"));
        }
        AccDoorItem accDoorItem = accDoorService.getItemByName(doorName);
        if (accDoorItem == null) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_NOTEXIST, I18nUtil.i18nCode("acc_api_doorNotExist"));
        }
        String[] ids = accDoorItem.getId().split(",");
        Map<String, String> doorState = accDoorService.getDoorIds(ids, "openDoor");
        if (doorState.containsKey("disabledDoorsName") && StringUtils.isNotBlank(doorState.get("disabledDoorsName"))) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_OFFLINE, I18nUtil.i18nCode("acc_api_doorOffline"));
        } else if (doorState.containsKey("offlineDoorsName")
            && StringUtils.isNotBlank(doorState.get("offlineDoorsName"))) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_OFFLINE, I18nUtil.i18nCode("acc_api_doorOffline"));
        }
        Map<String, String> dataMap = accRTMonitorService.operateDoor("closeDoor", null, accDoorItem.getId());// 远程关门
        if (dataMap == null) {
            return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("common_api_programError"));
        }
        return dealResultData(dataMap);
    }

    /**
     * 获取所有门状态
     *
     * @return
     * @auther lambert.li
     * @date 2018/11/22 16:09
     */
    @ResponseBody
    @RequestMapping(value = "/allDoorState", method = RequestMethod.GET, produces = "application/json")
    @ApiOperation(value = "Get Doors State", notes = "Return Doors State", response = ApiResultMessage.class)
    @ApiLogRequest
    public ApiResultMessage getAllDoorState() {
        List<AccDeviceItem> accDeviceItemList = accDeviceService.getByCondition(new AccDeviceItem());
        Map<String, AccDeviceItem> devMap = Maps.newHashMap();
        if (!accDeviceItemList.isEmpty()) {// 根据sn存放设备信息
            accDeviceItemList.forEach(accDeviceItem -> devMap.put(accDeviceItem.getSn(), accDeviceItem));
        }
        List<AccApiDoorStateItem> apiDoorStateItemList = Lists.newArrayList();
        JSONObject devState = (JSONObject)accRTMonitorService.getDevStateData(null).getData();// 获取门状态
        JSONArray stateArray = devState.getJSONArray("doorState");
        if (Objects.nonNull(stateArray) && stateArray.size() > 0) {
            for (int i = 0; i < stateArray.size(); i++) {
                JSONObject door = stateArray.getJSONObject(i);
                AccApiDoorStateItem apiDoorStateItem = buildApiDoorState(door);
                AccDeviceItem accDeviceItem = devMap.get(door.getString("devSn"));
                if (accDeviceItem != null) {// 设置设备ID
                    apiDoorStateItem.setDeviceId(accDeviceItem.getId());
                }
                apiDoorStateItemList.add(apiDoorStateItem);
            }
        }
        return ApiResultMessage.successMessage(apiDoorStateItemList);
    }

    /**
     * 根据sn获取门状态
     *
     * @param deviceSn
     * @return
     * @auther lambert.li
     * @date 2018/11/22 16:13
     */
    @ResponseBody
    @RequestMapping(value = "/doorStateBySn", method = RequestMethod.GET, produces = "application/json")
    @ApiOperation(value = "Get Door State By Sn", notes = "Return Door State", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"deviceSn"})
    public ApiResultMessage doorStateBySn(@RequestParam(name = "deviceSn", required = false) String deviceSn) {
        if (StringUtils.isBlank(deviceSn)) {
            return ApiResultMessage.message(AccConstants.ACC_TRANSACTION_SNNOTNULL,
                I18nUtil.i18nCode("acc_api_devSnNotNull"));
        }
        ApiResultMessage rs = accRTMonitorService.getDoorStateBySn(deviceSn);
        List<AccApiDoorStateItem> apiDoorStateItemList = Lists.newArrayList();
        if (rs.getData() != null) {
            AccDeviceItem accDeviceItem = accDeviceService.getItemByDevSn(deviceSn);
            JSONArray doorState = (JSONArray)rs.getData();
            for (int i = 0; i < doorState.size(); i++) {
                JSONObject door = doorState.getJSONObject(i);
                AccApiDoorStateItem apiDoorStateItem = buildApiDoorState(door);
                apiDoorStateItem.setDeviceId(accDeviceItem.getId());
                apiDoorStateItemList.add(apiDoorStateItem);
            }
        } else {
            return ApiResultMessage.message(AccConstants.ACC_DEV_NOTEXIST,
                I18nUtil.i18nCode("acc_api_deviceNumberDoesNotExist"));
        }
        return ApiResultMessage.successMessage(apiDoorStateItemList);
    }

    /**
     * 根据门ID获取门状态
     *
     * @param doorId
     * @return
     * @auther lambert.li
     * @date 2018/11/22 16:19
     */
    @ResponseBody
    @RequestMapping(value = "/doorStateById", method = RequestMethod.GET, produces = "application/json")
    @ApiOperation(value = "Get Door State By Id", notes = "Return Door State", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"doorId"})
    public ApiResultMessage doorStateById(@RequestParam(name = "doorId", required = false) String doorId) {
        if (StringUtils.isBlank(doorId)) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_DOORIDNOTNULL,
                I18nUtil.i18nCode("acc_api_doorIdNotNull"));
        }

        JSONArray doorState = (JSONArray)accRTMonitorService.getDoorState(doorId).getData();// 根据门ID获取门状
        List<AccApiDoorStateItem> apiDoorStateItemList = Lists.newArrayList();
        if (Objects.nonNull(doorState) && doorState.size() > 0) {
            AccDoorItem accDoorItem = accDoorService.getItemById(doorId);
            for (int i = 0; i < doorState.size(); i++) {
                JSONObject door = doorState.getJSONObject(i);
                AccApiDoorStateItem apiDoorStateItem = buildApiDoorState(door);
                apiDoorStateItem.setDeviceId(accDoorItem.getDeviceId());
                apiDoorStateItemList.add(apiDoorStateItem);
            }
        } else {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_NOTEXIST, I18nUtil.i18nCode("acc_api_doorNotExist"));
        }
        return ApiResultMessage.successMessage(apiDoorStateItemList);
    }

    /**
     * 处理获取命令执行结果
     *
     * @param dataMap
     * @return
     * @auther lambert.li
     * @date 2018/11/22 16:14
     */
    private ApiResultMessage dealResultData(Map<String, String> dataMap) {
        String cmdIdData = dataMap.get("cmdId");
        if ("true".equals(dataMap.get("notExistDev"))) { // 设备不存在
            return ApiResultMessage.message(AccConstants.ACC_DEV_NOTEXIST, I18nUtil.i18nCode("common_dev_notExistDev"));
        }
        if (StringUtils.isNotBlank(dataMap.get("offline"))) {// 设备离线
            return ApiResultMessage.message(AccConstants.ACC_DEV_OFFLINE, I18nUtil.i18nCode("acc_api_devOffline"));
        }
        if (StringUtils.isNotBlank(dataMap.get("notSupport"))) {// 设备不支持
            return ApiResultMessage.message(AccConstants.ACC_DEV_NOTSUPPORTFUNCTION,
                I18nUtil.i18nCode("acc_dev_devNotSupportFunction"));
        }
        if (StringUtils.isNotBlank(dataMap.get("faile"))) {// 下发失败
            return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, I18nUtil.i18nCode("common_op_failed"));
        }
        if (StringUtils.isNotBlank(cmdIdData)) {
            for (String cmdData : cmdIdData.split(",")) {
                String cmdId = cmdData.split("=")[0];
                Map<String, String> resultMap = accDeviceService.getCmdResultById(Long.valueOf(cmdId), 20);
                if (Objects.isNull(resultMap) || Objects.isNull(Integer.valueOf(resultMap.get("result")))
                    || Integer.parseInt(resultMap.get("result")) < 0) {// 下发命令失败
                    return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR,
                        I18nUtil.i18nCode("common_api_programError"));
                }
            }
        }
        return ApiResultMessage.successMessage();
    }

    /**
     * 组装门状态
     *
     * @param door
     * @return
     * @auther lambert.li
     * @date 2018/11/22 16:18
     */
    private AccApiDoorStateItem buildApiDoorState(JSONObject door) {
        AccApiDoorStateItem apiDoorStateItem = new AccApiDoorStateItem();
        int connect = door.getIntValue("connect");
        // 门磁状态
        String sensor = door.getString("sensor");
        // 继电器状态
        String relay = door.getString("relay");
        int alarmLevel = door.getIntValue("alarmLevel");
        // 报警状态
        int alarm = AccEnumUtil.Alarm.getAlarmType(alarmLevel, door.getIntValue("alarm"));
        apiDoorStateItem.setId(door.getString("id"));
        apiDoorStateItem.setName(door.getString("name"));
        apiDoorStateItem.setAlarm(alarm + "");
        apiDoorStateItem.setSensor(sensor);
        apiDoorStateItem.setRelay(relay);
        apiDoorStateItem.setConnect(String.valueOf(connect));
        return apiDoorStateItem;
    }
}
