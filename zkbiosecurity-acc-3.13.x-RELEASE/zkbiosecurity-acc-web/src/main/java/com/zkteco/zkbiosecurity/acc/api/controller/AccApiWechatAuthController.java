package com.zkteco.zkbiosecurity.acc.api.controller;

import com.zkteco.zkbiosecurity.acc.api.vo.AccApiReaderItem;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.vo.AccReaderItem;
import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.acc.service.WechatService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.acc.service.AccExceptionRecordService;
import com.zkteco.zkbiosecurity.acc.vo.AccExceptionRecordItem;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.http.MediaType;
import java.util.HashMap;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import org.springframework.core.io.ClassPathResource;
import java.io.BufferedReader;
import java.io.InputStreamReader;

@Controller
@RequestMapping(value = { "/api/v3/wechatAuth" })
@Api(tags = "AccApiWechatAuthController", description = "acc transaction")
public class AccApiWechatAuthController {

    @Autowired
    private WechatService wechatService;

    @Autowired
    private PersPersonService persPersonService;

    @Autowired
    private AccExceptionRecordService accExceptionRecordService;

    /**
     * 返回结果页面
     *
     * 
     */
    @RequestMapping(value = { "/wechatAuthResult" }, method = RequestMethod.GET)
    public void wechatAuthResult(HttpServletResponse response) throws IOException {
        try {
            // 直接读取HTML文件内容
            ClassPathResource resource = new ClassPathResource("static/wechatAuthResult/wechatAuthResult.html");
            InputStream inputStream = resource.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            StringBuilder content = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }

            response.setContentType("text/html;charset=UTF-8");
            response.getWriter().write(content.toString());
        } catch (Exception e) {
            response.setStatus(HttpStatus.NOT_FOUND.value());
            response.getWriter().write("文件未找到: " + e.getMessage());
        }
    }

    /**
     * 跳转到异常记录详情页面
     */
    @RequestMapping(value = { "/exceptionRecordDetail" }, method = RequestMethod.GET)
    public void exceptionRecordDetail(HttpServletResponse response) throws IOException {
        try {
            // 直接读取HTML文件内容
            ClassPathResource resource = new ClassPathResource(
                    "static/exceptionRecordDetail/exceptionRecordDetail.html");
            InputStream inputStream = resource.getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            StringBuilder content = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                content.append(line).append("\n");
            }

            response.setContentType("text/html;charset=UTF-8");
            response.getWriter().write(content.toString());
        } catch (Exception e) {
            response.setStatus(HttpStatus.NOT_FOUND.value());
            response.getWriter().write("文件未找到: " + e.getMessage());
        }
    }

    /**
     * 测试静态资源访问
     */
    @RequestMapping(value = { "/testStatic" }, method = RequestMethod.GET)
    public void testStatic(HttpServletResponse response) throws IOException {
        response.setContentType("text/html;charset=UTF-8");
        response.getWriter().write("<html><body><h1>静态资源测试成功</h1></body></html>");
    }

    @RequestMapping(value = "/login", method = RequestMethod.POST)
    @ResponseBody
    public ApiResultMessage login(@RequestBody Map<String, String> body) {
        String employeeId = body.get("employeeId");
        String password = body.get("password");

        if (StringUtils.isBlank(employeeId) || StringUtils.isBlank(password)) {
            return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, "工号和密码不能为空");
        }

        try {
            // 根据工号查询人员信息
            PersPersonItem persPersonItem = persPersonService.getItemByPin(employeeId);
            if (persPersonItem == null) {
                return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, "工号不存在");
            }

            // 验证密码
            if (!persPersonService.checkSelfPwd(persPersonItem, password)) {
                return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, "密码错误");
            }

            // 构建返回的用户信息
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("pin", persPersonItem.getPin());
            userInfo.put("name", persPersonItem.getName());
            userInfo.put("deptName", persPersonItem.getDeptName());
            userInfo.put("status", persPersonItem.getStatus());

            return ApiResultMessage.successMessage(userInfo);
        } catch (Exception e) {
            return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, "登录失败: " + e.getMessage());
        }
    }

    @RequestMapping(value = "/getAppId", method = RequestMethod.GET)
    @ResponseBody
    public ApiResultMessage getAppId() {
        try {
            // 从Redis获取appId，如果为空则返回默认值
            String appId = wechatService.getAppId();
            return ApiResultMessage.successMessage(appId);
        } catch (Exception e) {
            return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, "获取appId失败: " + e.getMessage());
        }
    }

    @RequestMapping(value = "/getExceptionRecordDetail", method = RequestMethod.GET)
    @ResponseBody
    public ApiResultMessage getExceptionRecordDetail(@RequestParam("id") String id) {
        if (StringUtils.isBlank(id)) {
            return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, "记录ID不能为空");
        }

        try {
            // 根据ID获取异常记录详情
            AccExceptionRecordItem record = accExceptionRecordService.getItemById(id);
            if (record == null) {
                return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, "记录不存在");
            }

            return ApiResultMessage.successMessage(record);
        } catch (Exception e) {
            return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, "获取异常记录详情失败: " + e.getMessage());
        }
    }

    @RequestMapping(value = "/getOpenId", method = RequestMethod.POST)
    @ResponseBody
    public ApiResultMessage getOpenId(@RequestBody Map<String, String> body) {
        String code = body.get("code");
        String pin = body.get("pin");

        if (StringUtils.isBlank(code)) {
            return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, "微信授权码不能为空");
        }

        try {
            // 调用微信服务获取openid
            String openId = wechatService.getOpenId(code);
            if (StringUtils.isNotBlank(pin)) {
                // 可以将openid与用户工号关联存储
                wechatService.saveUserOpenId(pin, openId);
            }
            return ApiResultMessage.successMessage(openId);
        } catch (Exception e) {
            return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, "获取openid失败: " + e.getMessage());
        }
    }

    /**
     * 注销登录
     */
    @RequestMapping(value = "/logout", method = RequestMethod.POST)
    @ResponseBody
    public ApiResultMessage logout(@RequestBody Map<String, String> body) {
        String employeeId = body.get("employeeId");
        String password = body.get("password");

        if (StringUtils.isBlank(employeeId) || StringUtils.isBlank(password)) {
            return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, "工号和密码不能为空");
        }

        try {
            // 根据工号查询人员信息
            PersPersonItem persPersonItem = persPersonService.getItemByPin(employeeId);
            if (persPersonItem == null) {
                return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, "工号不存在");
            }

            // 验证密码
            if (!persPersonService.checkSelfPwd(persPersonItem, password)) {
                return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, "密码错误");
            }

            // 清除用户的微信授权信息
            wechatService.clearUserOpenId(employeeId);

            // 可以在这里添加其他注销逻辑，比如清除session、记录日志等

            return ApiResultMessage.successMessage("注销成功");
        } catch (Exception e) {
            return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, "注销失败: " + e.getMessage());
        }
    }
}
