package com.zkteco.zkbiosecurity.acc.controller;

import com.zkteco.zkbiosecurity.acc.remote.AccPersonLastAddrRemote;
import com.zkteco.zkbiosecurity.acc.service.AccPersonLastAddrService;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonLastAddrItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.WebContextUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

/**
 * 对应百傲瑞达 AccPersonLastAddrController
 * @author:	yibiao.shen
 * @date:	2018-03-09  14:52:41
 */
@Controller
public class AccPersonLastAddrController extends ExportController implements AccPersonLastAddrRemote {

	@Autowired
	private AccPersonLastAddrService accPersonLastAddrService;

	
	@RequiresPermissions("acc:personLastAddr")
	@Override
	public ModelAndView index() {
		return new ModelAndView("acc/personLastAddr/accPersonLastAddr");
	}

	@RequiresPermissions("acc:personLastAddr:refresh")
	@Override
	public DxGrid list(AccPersonLastAddrItem condition) {
		Pager pager = accPersonLastAddrService.getItemsByPage(condition, getPageNo(), getPageSize());
		return GridUtil.convert(pager, condition.getClass());
	}

	@RequiresPermissions("acc:personLastAddr:del")
	@Override
	public ZKResultMsg del(String ids) {
		accPersonLastAddrService.deleteByIds(ids);
		return I18nUtil.i18nMsg(new ZKResultMsg());
	}

	@RequiresPermissions("acc:personLastAddr:del")
	@LogRequest(module="acc_module", object="acc_trans_lastAddr", opType="common_op_clearData", opContent="common_op_clearData")
    @Override
    public ZKResultMsg clearData() {
		accPersonLastAddrService.deleteAllData();
		return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:personLastAddr:export")
	@LogRequest(module="acc_module", object="acc_trans_lastAddr", opType="common_op_export", opContent="common_op_export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) throws InvocationTargetException, IllegalAccessException {
		AccPersonLastAddrItem accPersonLastAddrItem = new AccPersonLastAddrItem();
		setConditionValue(accPersonLastAddrItem);
		List<AccPersonLastAddrItem> itemList = accPersonLastAddrService.getItemData(AccPersonLastAddrItem.class, accPersonLastAddrItem, getBeginIndex(), getEndIndex());
		excelExport(itemList,AccPersonLastAddrItem.class);
    }

    @Override
    public ZKResultMsg getPersonLastAddrByPin(String pin) {
		ZKResultMsg res = new ZKResultMsg();
		Map retMap = accPersonLastAddrService.getPersonLastAddrByPin(pin,WebContextUtil.getCurrentSessionId());
		res.setData(retMap);
        return res;
    }
}
