package com.zkteco.zkbiosecurity.acc.api.controller;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 门禁事件记录接口
 *
 * <AUTHOR>
 * @Date: 2018/11/14 09:55
 */
@Controller
@RequestMapping(value = {"/api/v3/transaction"})
@Api(tags = "AccTransaction", description = "acc transaction")
public class AccApiV3TransactionController {

    @Autowired
    private AccTransactionService accTransactionService;

    /**
     * 根据设备SN获取事件记录
     *
     * @auther lambert.li
     * @date 2018/11/14 17:33
     * @param deviceSn
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "Get Transaction List By Sn", notes = "Return Transactions",
        response = ApiResultMessage.class)
    @RequestMapping(value = {"/device"}, method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"deviceSn", "startDate", "endDate", "pageNo", "pageSize"})
    public ApiResultMessage getTransactionsByDeviceSn(@RequestParam(name = "deviceSn") String deviceSn,
        @RequestParam(name = "startDate", required = false) String startDate,
        @RequestParam(name = "endDate", required = false) String endDate, @RequestParam(name = "pageNo") Integer pageNo,
        @RequestParam(name = "pageSize") Integer pageSize) {

        if (StringUtils.isBlank(deviceSn)) {
            return ApiResultMessage.message(AccConstants.ACC_TRANSACTION_SNNOTNULL,
                I18nUtil.i18nCode("acc_api_devSnNotNull"));
        }
        if (pageNo == null || pageSize == null) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE,
                I18nUtil.i18nCode("acc_api_parameterValueCannotBeNull"));
        }
        ApiResultMessage apiResultMessage = checkParam(startDate, endDate, pageNo, pageSize);
        if (apiResultMessage != null) {
            return apiResultMessage;
        }
        Date startTime = null;
        Date endTime = null;
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            startTime = DateUtil.stringToDate(startDate);
            endTime = DateUtil.stringToDate(endDate);
        }
        Pager pager =
            accTransactionService.getAccApiTransactionsBySnAndPage(deviceSn, startTime, endTime, pageNo, pageSize);
        return ApiResultMessage.successMessage(pager);
    }

    /**
     * 根据人员编号获取事件记录
     *
     * @auther lambert.li
     * @date 2018/11/14 17:33
     * @param pin
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "Get Transaction List By Pin", notes = "Return Transactions",
        response = ApiResultMessage.class)
    @RequestMapping(value = {"/person"}, method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"pin", "startDate", "endDate", "pageNo", "pageSize"})
    public ApiResultMessage getTransactionsByPin(@RequestParam(name = "pin") String pin,
        @RequestParam(name = "startDate", required = false) String startDate,
        @RequestParam(name = "endDate", required = false) String endDate, @RequestParam(name = "pageNo") Integer pageNo,
        @RequestParam(name = "pageSize") Integer pageSize) {

        if (StringUtils.isBlank(pin)) {
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }
        if (pageNo == null || pageSize == null) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE,
                I18nUtil.i18nCode("acc_api_parameterValueCannotBeNull"));
        }
        ApiResultMessage apiResultMessage = checkParam(startDate, endDate, pageNo, pageSize);
        if (apiResultMessage != null) {
            return apiResultMessage;
        }
        Date startTime = null;
        Date endTime = null;
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            startTime = DateUtil.stringToDate(startDate);
            endTime = DateUtil.stringToDate(endDate);
        }
        Pager pager =
            accTransactionService.getAccApiTransactionsByPinAndPage(pin, startTime, endTime, pageNo, pageSize);
        return ApiResultMessage.successMessage(pager);
    }

    /**
     * 校验API接口参数设置的合理性
     *
     * @param startDate:
     * @param endDate:
     * @param pageNo:
     * @param pageSize:
     * @return void
     * <AUTHOR>
     * @throws @date 2020-09-01 15:27
     * @since 1.0.0
     */
    private ApiResultMessage checkParam(String startDate, String endDate, Integer pageNo, Integer pageSize) {
        Date startTime = null;
        Date endTime = null;
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        if (pageSize > 1000) {
            return ApiResultMessage.message(AccConstants.API_PAGE_OVERSIZE,
                I18nUtil.i18nCode("common_api_pageSizeOver"));
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            startTime = DateUtil.stringToDate(startDate);
            endTime = DateUtil.stringToDate(endDate);
            // 时间格式错误
            if (startTime == null || endTime == null) {
                return ApiResultMessage.message(AccConstants.API_DATE_ERROR,
                    I18nUtil.i18nCode("common_dsTime_timeValid2"));
            }
            // 开始时间不能大于结束时间
            if (startTime.getTime() > endTime.getTime()) {
                return ApiResultMessage.message(AccConstants.API_DATE_STARTTIME_LARGE,
                    I18nUtil.i18nCode("common_dsTime_timeValid4"));
            }
        }
        return null;
    }

    /**
     * 根据人员编号获取FirstInAndLastOut记录
     *
     * <AUTHOR>
     * @Param pin
     * @Param startDate
     * @Param endDate
     * @Param pageNo
     * @Param pageSize
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * @date 2020/9/3 11:43
     */
    @ApiOperation(value = "Get FirstInAndLastOut List By Pin", notes = "Return FirstInAndLastOut List",
        response = ApiResultMessage.class)
    @RequestMapping(value = {"/firstInAndLastOut"}, method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"pin", "startDate", "endDate", "pageNo", "pageSize"})
    public ApiResultMessage getFirstInLastOutByPin(@RequestParam(name = "pin") String pin,
        @RequestParam(name = "startDate", required = false) String startDate,
        @RequestParam(name = "endDate", required = false) String endDate, @RequestParam(name = "pageNo") Integer pageNo,
        @RequestParam(name = "pageSize") Integer pageSize) {
        if (StringUtils.isBlank(pin)) {
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }
        if (pageNo == null || pageSize == null) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE,
                I18nUtil.i18nCode("acc_api_parameterValueCannotBeNull"));
        }
        ApiResultMessage apiResultMessage = checkParam(startDate, endDate, pageNo, pageSize);
        if (apiResultMessage != null) {
            return apiResultMessage;
        }
        Date startTime = null;
        Date endTime = null;
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            startTime = DateUtil.stringToDate(startDate);
            endTime = DateUtil.stringToDate(endDate);
        }
        Pager pager =
            accTransactionService.getAccApiFirstInLastOutByPinAndPage(pin, startTime, endTime, pageNo, pageSize);
        return ApiResultMessage.successMessage(pager);
    }
}
