/*
 * @author:	GenerationTools
 * @date:	2018-03-20 下午02:07
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import com.zkteco.zkbiosecurity.acc.remote.AccMapPosRemote;
import com.zkteco.zkbiosecurity.acc.service.AccMapPosService;
import com.zkteco.zkbiosecurity.acc.vo.AccMapPosItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

/**
 * TODO
 * @author:	GenerationTools
 * @date:	2018-03-20 下午02:07
 */
@Controller
public class AccMapPosController extends BaseController implements AccMapPosRemote {
	@Autowired
	private AccMapPosService accMapPosService;

	@RequiresPermissions("acc:accMapPos")
	@Override
	public ModelAndView index() {
		return new ModelAndView("acc/accMapPos/accMapPos");
	}

	@RequiresPermissions("acc:accMapPos:edit")
	@Override
	public ModelAndView edit(String id) {
		if(StringUtils.isNotBlank(id)) {
			request.setAttribute("item", accMapPosService.getItemById(id));
		}
		return new ModelAndView("acc/accMapPos/editAccMapPos");
	}

	@RequiresPermissions("acc:baseMapPos:edit")
    @LogRequest(module="acc_module",object="acc_leftMenu_electronicMap",opType="acc_accMapPos_edit",requestParams= {"id"},opContent="acc_accMapPos_id")
	@Override
	public ZKResultMsg save(AccMapPosItem item) {
		ZKResultMsg res = new ZKResultMsg();
		accMapPosService.saveItem(item);
		return I18nUtil.i18nMsg(res);
	}

	@RequiresPermissions("acc:accMapPos:refresh")
	@Override
	public DxGrid list(AccMapPosItem codition) {
		Pager pager = accMapPosService.getItemsByPage(codition, getPageNo(), getPageSize());
		return GridUtil.convert(pager, codition.getClass());
	}

	@RequiresPermissions("acc:map:delEntity")
    @LogRequest(module="acc_module",object="acc_leftMenu_electronicMap",opType="base_map_delEntity",requestParams= {"name"},opContent="common_dev_name")
	@Override
	public ZKResultMsg del(String ids) {
		accMapPosService.deleteByIds(ids);
		return I18nUtil.i18nMsg(new ZKResultMsg());
	}

	@Override
	public ZKResultMsg getEntityWidthByMapId(String mapId) {
		double  width = 40.0;//默认实体大小为40
		AccMapPosItem tmpAccMapPosItem = new AccMapPosItem();
		tmpAccMapPosItem.setMapId(mapId);
		List<AccMapPosItem> itemList = accMapPosService.getByCondition(tmpAccMapPosItem);
		if(!itemList.isEmpty())
		{
			AccMapPosItem accMapPosItem = itemList.get(0);
			if(accMapPosItem.getWidth() != null)
			{
				width = accMapPosItem.getWidth();
			}
		}
		return new ZKResultMsg(width);
	}
}