/**
 * <AUTHOR>
 * @date 2020/3/27 11:18
 */

package com.zkteco.zkbiosecurity.acc.controller;

import com.zkteco.zkbiosecurity.acc.remote.AccFirstInLastOutRemote;
import com.zkteco.zkbiosecurity.acc.service.AccFirstInLastOutService;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.vo.AccFirstInLastOutItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">caiyun.chen</a>
 * @version v1.0
 */
@Controller
public class AccFirstInLastOutController extends ExportController implements AccFirstInLastOutRemote {
    @Autowired
    private AccFirstInLastOutService accFirstInLastOutService;
    @Autowired
    private AccTransactionService accTransactionService;

    @RequiresPermissions("acc:firstInLastOut")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/firstInLastOut/accFirstInLastOut");
    }

    @RequiresPermissions("acc:firstInLastOut:refresh")
    @Override
    public DxGrid list(AccFirstInLastOutItem condition) {
        Pager pager = accFirstInLastOutService.loadTransactionByAuthUserFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize(), getLimitCount());
        return GridUtil.convert(pager, condition.getClass(), getPageList());
    }

    @RequiresPermissions("acc:firstInLastOut:clearData")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_firstInLastOut", opType = "common_op_clearData",
        opContent = "common_op_clearData")
    @Override
    public ZKResultMsg clearData() {
        accFirstInLastOutService.deleteAllData();
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:firstInLastOut:export")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_firstInLastOut", opType = "common_op_export",
        opContent = "common_op_export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AccFirstInLastOutItem accFirstInLastOutItem = new AccFirstInLastOutItem();
        setConditionValue(accFirstInLastOutItem);
        List<AccFirstInLastOutItem> itemList = accFirstInLastOutService.getExportItemData(request.getSession().getId(),
            accFirstInLastOutItem, getBeginIndex(), getEndIndex());
        excelExport(itemList, AccFirstInLastOutItem.class);
    }
}
