package com.zkteco.zkbiosecurity.acc.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.service.AccRTMonitorService;

public class AccLcdRTMonitorWSController {
	@Autowired
	private AccRTMonitorService accRTMonitorService;

	@MessageMapping("/accLcdRTMonitor/getLcdEvent")
	@SendTo("/topic/accLcdRTMonitor/getLcdEvent")
	public JSONObject getPersonInfo(String _params) {
		return null;
	}
}
