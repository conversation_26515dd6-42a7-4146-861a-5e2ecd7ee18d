package com.zkteco.zkbiosecurity.acc.api.controller;

import com.zkteco.zkbiosecurity.acc.service.AccApiThirdPartyService;
import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 门禁对接大掌柜接口
 * <AUTHOR>
 * @Date: 2018/11/14 19:30
 */
@Controller
@RequestMapping(value = {"/api/third"})
@Slf4j
public class AccApiThirdPartyController {

    @Autowired
    private AccApiThirdPartyService accApiThirdPartyService;

    /**
     * 对接大掌柜SFAS;IPC绑定的读头远程开门;实时监控产生虚拟事件;门禁当考勤
     * @auther lambert.li
     * @date 2018/11/15 8:34
     * @param pin
     * @param readerId
     * @return
     */
    @RequestMapping(value = "/openDoorAndCreateEvent", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"pin","readerId"})
    public ApiResultMessage openDoorAndCreateEvent(@RequestParam(name = "pin") String pin, @RequestParam(name = "readerId") String readerId)
    {
        return accApiThirdPartyService.openDoorAndCreateEvent(pin, readerId);
    }
}
