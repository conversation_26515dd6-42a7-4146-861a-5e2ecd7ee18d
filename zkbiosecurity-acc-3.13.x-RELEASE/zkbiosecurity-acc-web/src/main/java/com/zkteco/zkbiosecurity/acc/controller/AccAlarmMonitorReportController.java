package com.zkteco.zkbiosecurity.acc.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.remote.AccAlarmMonitorReportRemote;
import com.zkteco.zkbiosecurity.acc.service.AccAlarmMonitorService;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.vo.AccAlarmMonitorItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 报警记录
 * 
 * <AUTHOR>
 * @date Created In 11:42 2020/3/26
 */
@Controller
public class AccAlarmMonitorReportController extends ExportController implements AccAlarmMonitorReportRemote {

    @Autowired
    private AccAlarmMonitorService accAlarmMonitorService;
    @Autowired
    private AccTransactionService accTransactionService;

    @Override
    @RequiresPermissions("acc:alarmReport")
    public ModelAndView index() {
        return new ModelAndView("acc/alarmMonitor/accAlarmMonitorReport");
    }

    @Override
    @RequiresPermissions("acc:alarmReport:refresh")
    public DxGrid list(AccAlarmMonitorItem accAlarmMonitorItem) {
        long limit = getLimitCount();
        accAlarmMonitorItem.setAreaNameIn(accTransactionService.getAreaNamesBySessionId(request.getSession().getId()));
        Pager pager = accAlarmMonitorService.getPager(request.getSession().getId(), accAlarmMonitorItem, getPageNo(), getPageSize(), limit);
        return GridUtil.convert(pager, accAlarmMonitorItem.getClass(), getPageList());
    }

    @Override
    @RequiresPermissions("acc:alarmReport")
    @LogRequest(module = "acc_module", object = "acc_rtMonitor_ackAlarm", opType = "common_op_export",
        opContent = "common_op_export")
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AccAlarmMonitorItem item = new AccAlarmMonitorItem();
        setConditionValue(item);
        List<AccAlarmMonitorItem> items = (List<AccAlarmMonitorItem>)accAlarmMonitorService
            .getItemData(AccAlarmMonitorItem.class, item, getBeginIndex(), getEndIndex());
        excelExport(items, AccAlarmMonitorItem.class);
    }

    @Override
    public ModelAndView editAcknowledged(String ids) {
        request.setAttribute("id", ids);
        request.setAttribute("description", accAlarmMonitorService.getDescription(ids));
        request.setAttribute("editPage", false);
        return new ModelAndView("acc/alarmMonitor/editAcknowledged");
    }
}
