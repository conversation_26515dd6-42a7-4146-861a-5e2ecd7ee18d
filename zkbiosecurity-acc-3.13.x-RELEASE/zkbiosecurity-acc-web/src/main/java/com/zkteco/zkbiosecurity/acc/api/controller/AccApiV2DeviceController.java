package com.zkteco.zkbiosecurity.acc.api.controller;

import java.util.List;

import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.api.vo.AccApiDeviceItem;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/11/1 16:32
 */
@Controller
@RequestMapping(value = {"/api/v2/device"})
@Slf4j
@Api(tags = "AccDevice", description = "acc device")
public class AccApiV2DeviceController {

    @Autowired
    private AccDeviceService accDeviceService;

    /**
     * 分页获取门禁设备
     *
     * @param pageNo
     * @param pageSize
     * @return
     * @auther lambert.li
     * @date 2018/11/21 16:41
     */
    @ResponseBody
    @RequestMapping(value = {"/list"}, method = RequestMethod.GET, produces = "application/json")
    @ApiOperation(value = "Get Acc Devices", notes = "Return Acc Device List", response = ApiResultMessage.class)
    @ApiImplicitParams({
        @ApiImplicitParam(name = "pageNo", value = "Page Index", required = true, dataType = "int",
            paramType = "query"),
        @ApiImplicitParam(name = "pageSize", value = "One Page Size", required = true, dataType = "int",
            paramType = "query")})
    @ApiLogRequest(requestParams = {"pageNo","pageSize"})
    public ApiResultMessage list(@RequestParam(name = "pageNo", required = false) Integer pageNo,
        @RequestParam(name = "pageSize", required = false) Integer pageSize) {
        List<AccApiDeviceItem> accApiDeviceItemList = Lists.newArrayList();
        if (pageNo == null || pageSize == null) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE,
                I18nUtil.i18nCode("acc_api_parameterValueCannotBeNull"));
        }
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        if (pageSize > 1000) {
            return ApiResultMessage.message(AccConstants.API_PAGE_OVERSIZE,
                I18nUtil.i18nCode("common_api_pageSizeOver"));
        }
        Pager pager = accDeviceService.getItemsByPage(new AccDeviceItem(), pageNo - 1, pageSize);
        List<AccDeviceItem> accDeviceItemList = (List<AccDeviceItem>)pager.getData();
        if (!accDeviceItemList.isEmpty()) {
            accDeviceItemList
                .forEach(accDeviceItem -> accApiDeviceItemList.add(AccApiDeviceItem.createAccDevice(accDeviceItem)));
        }
        pager.setData(accApiDeviceItemList);
        return ApiResultMessage.successMessage(pager);
    }
}
