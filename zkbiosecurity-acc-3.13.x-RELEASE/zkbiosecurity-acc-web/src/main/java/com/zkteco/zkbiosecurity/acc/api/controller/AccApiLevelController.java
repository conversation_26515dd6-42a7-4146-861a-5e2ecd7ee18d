package com.zkteco.zkbiosecurity.acc.api.controller;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.api.vo.AccApiLevelAddDoorItem;
import com.zkteco.zkbiosecurity.acc.api.vo.AccApiLevelAddItem;
import com.zkteco.zkbiosecurity.acc.api.vo.AccApiLevelItem;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.AccApiLevelService;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.vo.AccLevelItem;
import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 门禁权限组接口
 *
 * <AUTHOR>
 * @Date: 2018/11/12 16:57
 */
@Controller
@RequestMapping(value = {"/api/accLevel"})
@Slf4j
@Api(tags = "AccLevel", description = "acc level")
public class AccApiLevelController {

    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private AccApiLevelService accApiLevelService;

    /**
     * 根据Id获取权限组信息
     *
     * @param id
     * @return
     * @auther lambert.li
     * @date 2018/11/12 17:05
     */
    @ApiOperation(value = "Get AccLevel By Id", notes = "Return AccLevel Object", response = ApiResultMessage.class)
    @RequestMapping(value = "/getById/{id}", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"id"})
    public ApiResultMessage getById(@PathVariable(required = false) String id) {
        if (StringUtils.isBlank(id)) {
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_LEVELIDNOTNULL,
                I18nUtil.i18nCode("acc_api_levelIdNotNull"));
        }
        AccLevelItem accLevel = accLevelService.getItemById(id);
        AccApiLevelItem accApiLevelItem = AccApiLevelItem.createApiLevel(accLevel);
        return ApiResultMessage.successMessage(accApiLevelItem);
    }

    /**
     * 根据权限组名称获取权限组信息
     *
     * @param name
     * @return
     * @auther lambert.li
     * @date 2018/11/12 18:48
     */
    @ApiOperation(value = "Get AccLevel By Name", notes = "Return AccLevel Object", response = ApiResultMessage.class)
    @RequestMapping(value = "/getByName/{name}", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"name"})
    public ApiResultMessage getByName(@PathVariable String name) {
        if (StringUtils.isBlank(name)) {
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_LEVELNAMENOTNULL,
                I18nUtil.i18nCode("acc_levelImport_nameNotNull"));
        }
        AccLevelItem accLevelItem = accLevelService.getItemByName(name);
        AccApiLevelItem accApiLevelItem = AccApiLevelItem.createApiLevel(accLevelItem);
        return ApiResultMessage.successMessage(accApiLevelItem);
    }

    /**
     * 分页获取权限组信息
     *
     * @param pageNo
     * @param pageSize
     * @return
     * @auther lambert.li
     * @date 2018/11/12 18:49
     */
    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET, produces = "application/json")
    @ApiOperation(value = "Get AccLevel List", notes = "Return AccLevel List", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"pageNo", "pageSize"})
    public ApiResultMessage list(@RequestParam(name = "pageNo") Integer pageNo,
        @RequestParam(name = "pageSize") Integer pageSize) {
        ApiResultMessage rs = new ApiResultMessage();
        if (pageNo == null || pageSize == null) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE,
                I18nUtil.i18nCode("acc_api_parameterValueCannotBeNull"));
        }
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.failedMessage(AccConstants.API_WRONG_PAGE);
        }
        List<AccApiLevelItem> apiAccLevels = Lists.newArrayList();
        List<AccLevelItem> accLevelItemList =
            (List<AccLevelItem>)accLevelService.getItemsByPage(new AccLevelItem(), pageNo - 1, pageSize).getData();
        if (!accLevelItemList.isEmpty()) {
            accLevelItemList.forEach(accLevelItem -> {
                AccApiLevelItem accApiLevelItem = AccApiLevelItem.createApiLevel(accLevelItem);
                if (accApiLevelItem != null) {
                    apiAccLevels.add(accApiLevelItem);
                }
            });
        }
        rs.setData(apiAccLevels);
        return rs;
    }

    /**
     * 同步人员的权限
     *
     * @param pin
     * @param levelIds 权限组id，多个id使用逗号,隔开（如：1,2,3）
     */
    @ResponseBody
    @RequestMapping(value = "/syncPerson", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Sync Person Level", notes = "Return Result Object", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"levelIds", "pin"})
    public ApiResultMessage syncPerson(@RequestParam(name = "pin") String pin,
        @RequestParam(name = "levelIds") String levelIds) {
        ApiResultMessage rs = null;
        try {
            rs = accLevelService.syncApiPersonLevel(pin, levelIds);
        } catch (Exception e) {
            log.error("api accLevel/syncPerson error ", e);
            rs = ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, I18nUtil.i18nCode("common_api_programError"));
        }
        return rs;
    }

    /**
     * 删除人员权限
     *
     * @param pin
     * @param levelIds
     */
    @ResponseBody
    @RequestMapping(value = "/deleteLevel", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Delete Person Level", notes = "Return Result Object", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"levelIds", "pin"})
    public ApiResultMessage deleteLevel(@RequestParam String pin, @RequestParam String levelIds) {
        ApiResultMessage rs = null;
        try {
            rs = accLevelService.deleteApiPersonLevel(pin, levelIds);
        } catch (Exception e) {
            log.error("api accLevel/deleteLevel error ", e);
            rs = ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, I18nUtil.i18nCode("common_api_programError"));
        }
        return rs;
    }

    /**
     * 新增门禁权限组API接口
     *
     * @param accApiLevelAddItems:
     * @return
     */
    @ApiOperation(value = "Add Level", notes = "Create Or Update  Level", response = ApiResultMessage.class)
    @RequestMapping(value = "/addLevel", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"accApiLevelAddItems[name, areaName, timeSegName]"})
    public ApiResultMessage add(@RequestBody List<AccApiLevelAddItem> accApiLevelAddItems) {
        ApiResultMessage rs = null;
        ApiResultMessage ars = new ApiResultMessage();
        if (accApiLevelAddItems != null && !accApiLevelAddItems.isEmpty()) {
            if (accApiLevelAddItems.size() > PersConstants.API_OPERATE_DATALIMIT) {
                return ApiResultMessage.message(PersConstants.PERSON_OPERATE_OVERSIZE, I18nUtil
                    .i18nCode("pers_api_dataLimit", accApiLevelAddItems.size(), PersConstants.API_OPERATE_DATALIMIT));
            }
            JSONArray array = new JSONArray();
            int error = 0;
            for (AccApiLevelAddItem accApiLevelAddItem : accApiLevelAddItems) {
                try {
                    rs = accApiLevelService.addApiLevel(accApiLevelAddItem);
                    if (0 != rs.getCode()) {
                        String name = StringUtils.trim(StringUtils.defaultIfEmpty(accApiLevelAddItem.getName(), "")
                            + " " + StringUtils.defaultIfEmpty(accApiLevelAddItem.getTimeSegName(), "") + " "
                            + StringUtils.defaultIfEmpty(accApiLevelAddItem.getAreaName(), ""));
                        array.add(name + ":" + rs.getMessage());
                        error++;
                    }
                } catch (ZKBusinessException e) {
                    String name = StringUtils.trim(StringUtils.defaultIfEmpty(accApiLevelAddItem.getName(), "") + " "
                        + StringUtils.defaultIfEmpty(accApiLevelAddItem.getTimeSegName(), "") + " "
                        + StringUtils.defaultIfEmpty(accApiLevelAddItem.getAreaName(), ""));
                    array.add(name + ":" + rs.getMessage());
                    error++;
                } catch (Exception e) {
                    log.error("api level/add error ", e);
                    rs = ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR,
                        I18nUtil.i18nCode("common_api_programError"));
                }
            }
            int success = accApiLevelAddItems.size() - error;
            ars.setMessage(I18nUtil.i18nCode("pers_import_result", success, error));
            ars.setData(array);
        }
        return ars;
    }

    /**
     * 同步权限信息
     *
     * @param levelId
     */
    @ResponseBody
    @RequestMapping(value = "/syncLevel", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Sync Level", notes = "Return Result Object", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"levelId"})
    public ApiResultMessage syncLevel(@RequestParam(name = "levelId") String levelId) {
        ApiResultMessage rs = null;
        try {

            rs = accLevelService.syncApiLevel(levelId);
        } catch (Exception e) {
            log.error("api accLevel/syncLevel error ", e);
            rs = ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, I18nUtil.i18nCode("common_api_programError"));
        }
        return rs;
    }

    /**
     * API添加门禁权限组下加门接口
     *
     * @param accApiLevelAddDoorItems:
     */
    @ApiOperation(value = "Add LevelDoor", notes = "Add LevelDoor", response = ApiResultMessage.class)
    @RequestMapping(value = "/addLevelDoor", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"accApiLevelAddDoorItems[doorName, levelName]"})
    public ApiResultMessage addDoor(@RequestBody List<AccApiLevelAddDoorItem> accApiLevelAddDoorItems) {
        ApiResultMessage rs = null;
        ApiResultMessage ars = new ApiResultMessage();
        if (accApiLevelAddDoorItems != null && !accApiLevelAddDoorItems.isEmpty()) {
            if (accApiLevelAddDoorItems.size() > PersConstants.API_OPERATE_DATALIMIT) {
                return ApiResultMessage.message(PersConstants.PERSON_OPERATE_OVERSIZE, I18nUtil.i18nCode(
                    "pers_api_dataLimit", accApiLevelAddDoorItems.size(), PersConstants.API_OPERATE_DATALIMIT));
            }
            JSONArray array = new JSONArray();
            int error = 0;
            for (AccApiLevelAddDoorItem accApiLevelAddDoorItem : accApiLevelAddDoorItems) {
                try {
                    rs = accApiLevelService.addApiLevelDoor(accApiLevelAddDoorItem);
                    if (0 != rs.getCode()) {
                        String name =
                            StringUtils.trim(StringUtils.defaultIfEmpty(accApiLevelAddDoorItem.getLevelName(), "") + " "
                                + StringUtils.defaultIfEmpty(accApiLevelAddDoorItem.getDoorName(), ""));
                        array.add(name + ":" + rs.getMessage());
                        error++;
                    }
                } catch (ZKBusinessException e) {
                    String name = StringUtils.trim(StringUtils.defaultIfEmpty(accApiLevelAddDoorItem.getLevelName(), "")
                        + " " + StringUtils.defaultIfEmpty(accApiLevelAddDoorItem.getDoorName(), ""));
                    array.add(name + ":" + rs.getMessage());
                    error++;
                } catch (Exception e) {
                    log.error("api level/add error ", e);
                    rs = ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR,
                        I18nUtil.i18nCode("common_api_programError"));
                }
            }
            int success = accApiLevelAddDoorItems.size() - error;
            ars.setMessage(I18nUtil.i18nCode("pers_import_result", success, error));
            ars.setData(array);
        }
        return ars;
    }

    /**
     * API新增批量添加人员门禁权限接口
     *
     * @param
     * @return
     * <AUTHOR>
     * @throws
     * @date 2024-01-17 14:12
     * @since 1.0.0
     */
    @ApiOperation(value = "Add LevelPerson", notes = "Add LevelPerson", response = ApiResultMessage.class)
    @RequestMapping(value = "/addLevelPerson", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"pin", "levelIds"})
    public ApiResultMessage addLevelperson(@RequestParam String pin, @RequestParam List<String> levelIds) {
        ApiResultMessage rs = null;
        ApiResultMessage ars = new ApiResultMessage();
        if (levelIds != null && !levelIds.isEmpty()) {
            if (levelIds.size() > PersConstants.API_OPERATE_DATALIMIT) {
                return ApiResultMessage.message(PersConstants.PERSON_OPERATE_OVERSIZE,
                    I18nUtil.i18nCode("pers_api_dataLimit", levelIds.size(), PersConstants.API_OPERATE_DATALIMIT));
            }

            JSONArray array = new JSONArray();
            int error = 0;
            for (String levelId : levelIds) {
                try {
                    rs = accApiLevelService.addApiPersonLevel(pin, levelId);
                    if (0 != rs.getCode()) {
                        String name = StringUtils
                            .trim(StringUtils.defaultIfEmpty(pin, "") + " " + StringUtils.defaultIfEmpty(levelId, ""));
                        array.add(name + ":" + rs.getMessage());
                        error++;
                    }
                } catch (ZKBusinessException e) {
                    String name = StringUtils
                        .trim(StringUtils.defaultIfEmpty(pin, "") + " " + StringUtils.defaultIfEmpty(levelId, ""));
                    array.add(name + ":" + rs.getMessage());
                    error++;
                } catch (Exception e) {
                    log.error("api levelPerson/add error ", e);
                    rs = ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR,
                        I18nUtil.i18nCode("common_api_programError"));
                }
            }
            int success = levelIds.size() - error;
            ars.setMessage(I18nUtil.i18nCode("pers_import_result", success, error));
            ars.setData(array);
        } else {
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_LEVELIDNOTNULL,
                I18nUtil.i18nCode("acc_api_levelIdNotNull"));
        }
        return ars;
    }
}
