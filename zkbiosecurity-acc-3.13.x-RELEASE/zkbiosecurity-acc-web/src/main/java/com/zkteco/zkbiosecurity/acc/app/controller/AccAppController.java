package com.zkteco.zkbiosecurity.acc.app.controller;

import java.util.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.app.vo.AccAppDoorItem;
import com.zkteco.zkbiosecurity.acc.app.vo.AccAppLevelItem;
import com.zkteco.zkbiosecurity.acc.app.vo.AccAppTransactionItem;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.utils.AccEnumUtil;
import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccLevelItem;
import com.zkteco.zkbiosecurity.base.vo.AppResultMessage;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;

/**
 * APP门禁接口
 * 
 * <AUTHOR>
 * @Date: 2018/12/5 17:26
 */
@RestController
@RequestMapping(value = "/app/v1")
public class AccAppController {

    @Autowired
    private AccAppService accAppService;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private AccRTMonitorService accRTMonitorService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccAlarmMonitorService accAlarmMonitorService;

    @Autowired
    protected HttpServletRequest request;

    /**
     * 获取门禁所有权限组
     * 
     * @auther lambert.li
     * @date 2018/12/5 18:04
     * @return
     */
    @RequestMapping(value = "/getAccLevels", method = RequestMethod.POST)
    public AppResultMessage getAccLevels() {
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        List<AccAppLevelItem> accAppLevelItems = new ArrayList<>();
        List<AccLevelItem> accLevelItemList = accLevelService.getByCondition(new AccLevelItem());
        if (!accLevelItemList.isEmpty()) {
            // 权限组不为空时组装权限组数据返回
            accLevelItemList.forEach(levelItem -> {
                AccAppLevelItem accAppLevelItem = new AccAppLevelItem();
                accAppLevelItem.setAccLevelId(levelItem.getId()).setAccLevelName(levelItem.getName())
                    .setSelected(levelItem.getInitFlag() != null && levelItem.getInitFlag() ? "true" : "false");
                accAppLevelItems.add(accAppLevelItem);
            });
        }
        appResultMessage.setData(accAppLevelItems);
        return appResultMessage;
    }

    /**
     * 根据用户及门名称、区域名称获取门数据
     * 
     * @auther lambert.li
     * @date 2018/12/6 17:53
     * @param data {"token":"","doorName":"","pageNo":1,"pageSize":20}
     * @return
     */
    @RequestMapping(value = "/getDoorByDoorName", method = RequestMethod.POST)
    public AppResultMessage getDoorByDoorName(@RequestBody JSONObject data) {
        String token = data.getString("token");
        String doorName = data.getString("doorName");
        int pageNo = data.getIntValue("pageNo");
        int pageSize = data.getIntValue("pageSize");
        String loginType = data.getString("loginType");
        String selectAreaId = data.getString("selectAreaId");
        AccAppDoorItem condition = new AccAppDoorItem();
        condition.setFilter(doorName);
        condition.setSelectAreaId(selectAreaId);
        // modify by xjiang.huang 2023-07-18 新增登录类型
        AppResultMessage appResultMessage =
            accAppService.getDoorByDoorName(token, condition, pageNo, pageSize, loginType);
        // AppResultMessage appResultMessage = accAppService.getDoorByDoorName(token, doorName, pageNo, pageSize);
        List<AccAppDoorItem> accAppDoorItemList = new ArrayList<>();
        JSONObject dataJson = new JSONObject();
        int total = 0;
        if (appResultMessage.getData() != null) {
            JSONObject doorJson = (JSONObject)appResultMessage.getData();
            JSONArray doorArray = doorJson.getJSONArray("rows");
            total = doorJson.getIntValue("totalCount");
            if (doorArray.size() > 0) {
                for (int i = 0; doorArray.size() > i; i++) {
                    JSONObject door = doorArray.getJSONObject(i);
                    AccAppDoorItem appDoor = new AccAppDoorItem();
                    appDoor.setId(door.getString("id"));
                    appDoor.setDoorName(door.getString("name"));
                    appDoor.setDevName(door.getString("devName"));
                    // modify by xjiang.huang 2023-07-18 新增返回时区
                    appDoor.setTimeZone(door.getString("timeZone"));
                    appDoor.setAreaName(door.containsKey("areaName") ? door.getString("areaName") : null);
                    appDoor.setRelay(door.getString("relay"));
                    appDoor.setSensor(door.getString("sensor"));
                    appDoor.setDoorStatus(door.getShortValue("connect"));

                    if (AccConstants.DEV_STATE_ONLINE == door.getShortValue("connect")) {
                        // 设备在线，判断门报警状态
                        if (0 == AccEnumUtil.Alarm.getAlarmType(door.getIntValue("alarmLevel"),
                            door.getIntValue("alarm"))) {
                            // 0:无报警
                            appDoor.setDoorAlarmStatus(0);
                        } else {
                            // 1：报警
                            appDoor.setAlarm(AccEnumUtil.Alarm.getAlarmType(door.getIntValue("alarmLevel"),
                                door.getIntValue("alarm")));
                            appDoor.setDoorAlarmStatus(1);
                        }

                        if (ConstUtil.DEV_STATE_LOCK == door.getIntValue("doorValue")) {
                            appDoor.setDoorStatus(ConstUtil.DEV_STATE_LOCK);
                        }
                    } else if (AccConstants.DEV_STATE_DISABLE == door.getShortValue("connect")) {
                        // 禁用
                        appDoor.setDoorAlarmStatus(2);
                    } else if (AccConstants.DEV_STATE_OFFLINE == door.getShortValue("connect")) {
                        // 离线
                        appDoor.setDoorAlarmStatus(3);
                    }
                    accAppDoorItemList.add(appDoor);
                }
            }
        }
        dataJson.put("totalCount", total);
        dataJson.put("rows", accAppDoorItemList);
        appResultMessage.setData(dataJson);
        return appResultMessage;
    }

    /**
     * 门操作接口（开门、关门、取消报警）
     * 
     * @auther lambert.li
     * @date 2018/12/6 16:22
     * @param data {"doorId":1,"type":"openDoor","interval":5}
     * @return
     */
    @RequestMapping(value = "/operateDoor", method = RequestMethod.POST)
    public AppResultMessage operateDoor(@RequestBody JSONObject data) {
        String doorId = data.getString("doorId");
        String type = data.getString("type");
        String interval = data.getString("interval");
        String userName = request.getParameter("user_name");
        Map<String, String> dataMap = accRTMonitorService.operateDoor(type, interval, doorId);
        return dealResultData(dataMap, userName, type);
    }

    /**
     * 获取门事件记录
     * 
     * @auther lambert.li
     * @date 2018/12/10 10:46
     * @param data {"doorId":1, "filter":"","pageNo":1,"pageSize":20}
     * @return
     */
    @RequestMapping(value = "/getDoorDataByDoorIdAndFilters", method = RequestMethod.POST)
    public AppResultMessage getDoorDataByDoorIdAndFilters(@RequestBody JSONObject data) {
        String doorId = data.getString("doorId");
        String filters = data.getString("filter");
        int pageNo = data.containsKey("pageNo") ? data.getIntValue("pageNo") : 1;
        int pageSize = data.containsKey("pageSize") ? data.getIntValue("pageSize") : 20;
        return accAppService.getDoorDataByDoorIdAndFilters(doorId, filters, pageNo, pageSize);
    }

    /**
     * 确认报警
     * 
     * @auther lambert.li
     * @date 2018/12/7 11:47
     * @param data
     * @return
     */
    @RequestMapping(value = "/ackAlarm", method = RequestMethod.POST)
    public AppResultMessage ackAlarm(@RequestBody JSONObject data) {
        AppResultMessage appResultMessage = new AppResultMessage();
        // 报警事件id
        String ids = data.getString("uniqueKeys");
        if (StringUtils.isNotBlank(ids)) {
            accAlarmMonitorService.checkAlarmMonitor(ids);
            accAlarmMonitorService.sendAllAlarmMonitorWS(false);
        }
        return appResultMessage;
    }

    /**
     * 获取门禁报表记录
     * 
     * @auther lambert.li
     * @date 2018/12/7 11:47
     * @param data {"doorId":1, "filter":"","startTime":"2018-12-07 18:57:41","endTime":"2018-12-07
     *            18:59:41","pageNo":1,"pageSize":20}
     * @return
     */
    @RequestMapping(value = "/getReportByFilters", method = RequestMethod.POST)
    public AppResultMessage getReportByFilters(@RequestBody JSONObject data) {
        // 页码
        int pageNo = data.containsKey("pageNo") ? data.getIntValue("pageNo") : 1;
        // 每页记录数
        int pageSize = data.containsKey("pageSize") ? data.getIntValue("pageSize") : 20;
        // 搜索框数据
        String filters = data.getString("filters");
        // 开始时间
        Date startTime = data.containsKey("startTime") ? data.getDate("startTime") : null;
        // 结束时间
        Date endTime = data.containsKey("endTime") ? data.getDate("endTime") : null;
        // 门ID
        String doorId = data.getString("doorId");
        // token
        return accAppService.getReportByFilters(doorId, filters, startTime, endTime, pageNo, pageSize);
    }

    /**
     * 获取报警监控的报警记录
     * 
     * @auther lambert.li
     * @date 2018/12/10 10:48
     * @param data {"clientId":1, "userName":"","userPwd":"","eventNos":"28,101,700"}
     * @return
     */
    @RequestMapping(value = "/getRTMonitorAlarmData", method = RequestMethod.POST)
    public AppResultMessage getRTMonitorAlarmData(@RequestBody JSONObject data) {
        // 报警事件唯一key值
        String clientId = data.getString("clientId");
        // 当前登录用户名
        String userName = data.getString("userName");
        // 用户密码
        String userPwd = data.getString("userPwd");
        // 显示的报警类型
        String eventNos = data.getString("eventNos");
        JSONArray retJsonArr = accAppService.getAlarmEventData(eventNos);
        JSONObject retJson = new JSONObject();
        retJson.put("ret", "OK");
        retJson.put("data", retJsonArr);
        return new AppResultMessage(retJson.toJSONString());
    }

    /**
     * 获取所有门列表
     * 
     * @auther lambert.li
     * @date 2018/12/10 10:58
     * @return
     */
    @RequestMapping(value = "/getAllDoor", method = RequestMethod.POST)
    public AppResultMessage getAllDoor() {
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        List<AccDoorItem> accDoorItemList = accDoorService.getByCondition(new AccDoorItem());
        List<AccAppDoorItem> accAppDoorItemList = new ArrayList<>();
        if (!accDoorItemList.isEmpty()) {
            accDoorItemList.forEach(accDoorItem -> {
                AccAppDoorItem accAppDoorItem = new AccAppDoorItem();
                accAppDoorItem.setId(accDoorItem.getId());
                accAppDoorItem.setDoorName(accDoorItem.getName());
                accAppDoorItemList.add(accAppDoorItem);
            });
        }
        appResultMessage.setData(accAppDoorItemList);
        return appResultMessage;
    }

    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 处理获取命令执行结果
     * 
     * @auther lambert.li
     * @date 2018/12/6 17:25
     * @param dataMap
     * @return
     */
    private AppResultMessage dealResultData(Map<String, String> dataMap, String operator, String opType) {
        String cmdIdData = dataMap.get("cmdId");
        if ("true".equals(dataMap.get("notExistDev"))) {
            // 设备不存在
            return AppResultMessage.failMessage().setRet(String.valueOf(AccConstants.ACC_DEV_NOTEXIST));
        }
        if (StringUtils.isNotBlank(dataMap.get("offline"))) {
            // 设备离线
            return AppResultMessage.failMessage().setRet(String.valueOf(AccConstants.ACC_DEV_OFFLINE));
        }
        if (StringUtils.isNotBlank(dataMap.get("notSupport"))) {
            // 设备不支持
            return AppResultMessage.failMessage().setRet(String.valueOf(AccConstants.ACC_DEV_NOTSUPPORTFUNCTION));
        }
        if (StringUtils.isNotBlank(dataMap.get("faile"))) {
            // 下发失败
            return AppResultMessage.failMessage().setRet(String.valueOf(AccConstants.ACC_DEV_ISSUEDFAILED));
        }
        if (StringUtils.isNotBlank(cmdIdData)) {
            for (String cmdData : cmdIdData.split(",")) {
                String cmdId = cmdData.split("=", 2)[0];
                Map<String, String> resultMap = accDeviceService.getCmdResultById(Long.valueOf(cmdId), 20);
                if (Objects.isNull(resultMap) || Objects.isNull(Integer.parseInt(resultMap.get("result")))
                    || Integer.parseInt(resultMap.get("result")) < 0) {
                    // 命令执行失败
                    return AppResultMessage.failMessage();
                }
            }
        }
        return AppResultMessage.successMessage();
    }

    /**
     * 实时监控
     * 
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws @date 2021-01-29 10:29
     * @since 1.0.0
     */
    @RequestMapping(value = "/monitor", method = RequestMethod.POST)
    public AppResultMessage monitor(@RequestBody JSONObject data) {
        // 实时事件唯一key值
        String clientId = data.getString("clientId");
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        List<AccAppTransactionItem> appTransactions = accAppService.getRTMonitorData(clientId);
        appResultMessage.setData(appTransactions);
        return appResultMessage;
    }
}
