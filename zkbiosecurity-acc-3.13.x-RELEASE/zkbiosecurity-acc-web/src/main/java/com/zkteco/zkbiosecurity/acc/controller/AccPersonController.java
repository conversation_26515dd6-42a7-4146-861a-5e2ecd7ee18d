/*
 * @author: GenerationTools
 * 
 * @date: 2018-03-02 下午02:10 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.zkteco.zkbiosecurity.acc.service.AccRTMonitorService;
import com.zkteco.zkbiosecurity.pers.service.PersAttributeService;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.remote.AccPersonRemote;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonService;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.LocaleMessageSourceUtil;
import com.zkteco.zkbiosecurity.pers.service.PersParamsService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 门禁人员Controller
 * 
 * @author: GenerationTools
 * @date: 2018-03-02 下午02:10
 */
@Controller
public class AccPersonController extends BaseController implements AccPersonRemote {
    @Autowired
    private AccPersonService accPersonService;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersParamsService persParamsService;
    @Value("${system.levelTime.support:false}")
    private boolean isSupportLevelTime;
    @Autowired
    private PersAttributeService persAttributeService;
    @Autowired
    private AccRTMonitorService accRTMonitorService;

    @RequiresPermissions("pers:person:accEdit")
    @Override
    public ModelAndView edit(@RequestParam(value = "personId", required = false) String personId) {
        if (StringUtils.isNotBlank(personId)) {
            request.setAttribute("item", accPersonService.getItemByPersonId(personId));
            request.setAttribute("tempList", accLevelService.getLevelByPersonId(personId));
            request.setAttribute("personId", personId);
        }
        String areaNames = accRTMonitorService.getAreaNamesBySessionId(request.getSession().getId());
        request.setAttribute("areaNames", areaNames);

        AccLevelItem masterLevel = accLevelService.getMasterLevelBySessionId(request.getSession().getId());
        if (Objects.nonNull(masterLevel)) {
            request.setAttribute("defaultLevel", masterLevel);
        }
        if (isSupportLevelTime) {
            return new ModelAndView("acc/person/editAccPersonEx");
        } else {
            return new ModelAndView("acc/person/editAccPerson");
        }
    }

    @RequiresPermissions("acc:personLevelByPerson:setAccParams")
    @LogRequest(module = "acc_module", object = "common_leftMenu_levelSetByPerson", opType = "pers_person_accSetting",
        requestParams = {"pins"}, opContent = "pers_person_pin")
    @Override
    public ZKResultMsg saveParamSet(AccPersonItem item, String ids) {
        ZKResultMsg res = new ZKResultMsg();
        if (StringUtils.isNotBlank(ids)) {
            for (String id : ids.split(",")) {
                item.setPersonId(id);
                item.setDisabled(item.getDisabled() == null ? false : item.getDisabled());
                accPersonService.saveParamSet(item);
            }
        }
        return I18nUtil.i18nMsg(res);
    }

    @Override
    public ZKResultMsg getPersonCountByDept(String deptIds) {
        long personCount = accPersonService.getPersonCountByDept(deptIds);
        return I18nUtil.i18nMsg(new ZKResultMsg(personCount));
    }

    @Override
    @RequiresPermissions("acc:globalLinkage:refresh")
    public DxGrid getGlobalLinkagePerson(AccPersonListItem codition) {
        codition.setModelType("ACC_GLOBALLINKAGE");
        Pager pager =
            accPersonService.getPersonItemList(request.getSession().getId(), codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @Override
    public ModelAndView filterPersonByVaildCard(String cardNo) {
        Map<String, String> filterPersonMap = accPersonService.filterPersonByVaildCard(cardNo);
        if ("true".equals(filterPersonMap.get("cardNoExist"))) {
            request.setAttribute("personPin", filterPersonMap.get("personPin"));
            return new ModelAndView("acc/rtMonitor/opAccSetCardNoToPerson");
        }
        request.setAttribute("cardNo", cardNo);
        return new ModelAndView("acc/rtMonitor/opAccSelectNoCardPersonContent");
    }

    @Override
    public DxGrid getNoCardPerson(AccSelectPersonRadioItem accSelectPersonRadioItem) {
        Pager pager = new Pager();
        if (accSelectPersonRadioItem.getType().equals("noSelected")) {
            pager = accPersonService.getNoCardPerson(request.getSession().getId(), accSelectPersonRadioItem,
                getPageNo(), getPageSize());
        } else if (accSelectPersonRadioItem.getType().equals("selected")) {
            pager.setData(new ArrayList<AccSelectPersonItem>());
        }
        return GridUtil.convert(pager, accSelectPersonRadioItem.getClass());
    }

    @Override
    public DxGrid selectPersonlist(AccSelectPersonItem codition) {
        Pager pager = new Pager();
        if (codition.getType().equals("noSelected")) {
            pager =
                accPersonService.getNoExistPerson(request.getSession().getId(), codition, getPageNo(), getPageSize());
        } else if (codition.getType().equals("selected")) {
            pager.setData(new ArrayList<AccSelectPersonItem>());
        }
        return GridUtil.convert(pager, codition.getClass());
    }

    @Override
    public ZKResultMsg setPersonCard(String personId, String cardNo) {
        accPersonService.setCardNoToPerson(personId, cardNo);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public ModelAndView getAllField(@RequestParam(value = "cardNo", required = false) String cardNo) {
        Map<String, String> persParams = persParamsService.getPersParams();
        List<PersAttributeItem> attributes = persAttributeService.getByCondition(new PersAttributeItem());
        for (PersAttributeItem item : attributes) {
            String attrName = I18nUtil.i18nCode("pers_attr_" + item.getAttrName());
            if (StringUtils.isNotBlank(attrName) && !attrName.contains("pers_attr_")) {
                item.setShowName(attrName);
            }
        }
        request.setAttribute("attributes", attributes);
        request.setAttribute("persParams", persParams);
        request.setAttribute("cardNo", cardNo);
        request.setAttribute("editPage", true);
        if ("zh_CN".equals(LocaleMessageSourceUtil.language)) {
            return new ModelAndView("pers/person/editPersPersonZH");
        }
        request.setAttribute("showSMS", accPersonService.checkShowSMS());
        return new ModelAndView("pers/person/editPersPerson");
    }

    @Override
    public DxGrid selectLevelPersonList(AccSelectPersonItem condition) {
        Pager pager = new Pager();
        if (condition.getType().equals("noSelected")) {
            pager = accPersonService.getNoExistLevelPerson(request.getSession().getId(), condition, getPageNo(),
                getPageSize());
        } else if (condition.getType().equals("selected")) {
            pager.setData(new ArrayList<AccSelectPersonItem>());
        }
        return GridUtil.convert(pager, condition.getClass());
    }
}