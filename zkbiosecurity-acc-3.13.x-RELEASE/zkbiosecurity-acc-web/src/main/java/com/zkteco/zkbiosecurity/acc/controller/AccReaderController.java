/*
 * @author: GenerationTools
 * 
 * @date: 2018-03-13 上午10:06 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import com.zkteco.zkbiosecurity.acc.remote.AccReaderRemote;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import java.util.Arrays;

/**
 * @author: GenerationTools
 * @date: 2018-03-13 上午10:06
 */
@Controller
public class AccReaderController extends BaseController implements AccReaderRemote {
    @Autowired
    private AccReaderService accReaderService;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccReaderOptionService accReaderOptionService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccChannelService accChannelService;

    @RequiresPermissions("acc:reader")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/reader/accReader");
    }

    @RequiresPermissions("acc:reader:edit")
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            AccReaderItem accReaderItem = accReaderService.getItemById(id);
            // 隐藏部分人员信息
            AccReaderOptionItem infoReveal =
                accReaderOptionService.getByOptName(accReaderItem.getId(), "IsSupportInfoReveal");
            accReaderItem.setUserInfoReveal(infoReveal != null ? infoReveal.getValue() : "0");
            request.setAttribute("item", accReaderItem);
            // 判断是否支持485
            AccDoorItem accDoorItem = accDoorService.getItemById(accReaderItem.getDoorId());
            String deviceSn = accDoorItem.getDeviceSn();
            String ext485ReaderFunOn = accDeviceOptionService.getOptVal(deviceSn, "~Ext485ReaderFunOn");
            if (StringUtils.isBlank(ext485ReaderFunOn) || ext485ReaderFunOn.equals("0")) {
                request.setAttribute("noSupportRS485", true);// 不支持485读头
            }

            // 判断是否支持读头属性配置
            request.setAttribute("isSupportConfig", accDeviceOptionService.isSupportFunList(deviceSn, 18));
            // 旧的inbio5不支持韦根或RS485混合,前端页面会只展示支持的 add juvenile.li 20171214
            request.setAttribute("isSupportWgOrRS485", accDeviceOptionService.isSupportFunList(deviceSn, 32));
            AccDeviceOptionItem devOpt =
                accDeviceOptionService.getDevOptValueBySnAndName(deviceSn, "SupportReaderType");
            if (devOpt != null) {
                request.setAttribute("supportReaderType", devOpt.getValue());
            }

            if (accDeviceOptionService.isSupportFunList(deviceSn, 0)) {
                request.setAttribute("readerDisable", true);// 支持读头通讯类型选择
            }
            if (accDeviceOptionService.isSupportFunList(deviceSn, 1)) {
                request.setAttribute("encrypt", true);// 支持加密读头设置
            }
            // 读头型号
            AccReaderOptionItem readerModel = accReaderOptionService.getByOptName(accReaderItem.getId(), "ReaderModel");
            if (readerModel != null) {
                request.setAttribute("readerModel", readerModel.getValue());
            }
            // 读头验证方式
            AccReaderOptionItem readMode =
                accReaderOptionService.getByOptName(accReaderItem.getId(), "IdentityCardVerifyMode");
            if (readMode != null) {
                request.setAttribute("readMode", readMode.getValue());
            }
            boolean cardControl = accReaderService.checkCardControl();
            if (cardControl) {
                request.setAttribute("requireCardControl", true);
            }
            if (StringUtils.isNotBlank(accReaderItem.getExtDevId())) {
                // 代表属于扩展板的读头
                request.setAttribute("extBoard", true);
            }
            if (accDeviceOptionService.isSupportFunList(deviceSn, 49)) {
                request.setAttribute("supportOSDP", true);// 支持osdp配置
            }
            // 判断是否支持读头人员锁定配置
            request.setAttribute("isSupportUserLockFun", accDeviceOptionService.isSupportFunList(deviceSn, 50));
            // 判断读头是否支持设置“隐藏部分人员信息”参数
            request.setAttribute("isSupportInfoReveal", accDeviceOptionService.isSupportFunList(deviceSn, 51));
            // 门数量
            int lockCount = Integer.parseInt(accDeviceOptionService.getOptVal(deviceSn, "LockCount"));
            request.setAttribute("lockCount", lockCount);
        }
        return new ModelAndView("acc/reader/editAccReader");
    }

    @RequiresPermissions("acc:reader:edit")
    @LogRequest(module = "acc_module", object = "common_leftMenu_reader", opType = "common_op_edit",
        requestParams = {"name"}, opContent = "acc_readerDefine_readerName")
    @Override
    public ZKResultMsg save(AccReaderItem item) {
        ZKResultMsg res = new ZKResultMsg();
        String applyTo = request.getParameter("applyTo");
        // String readerModel = request.getParameter("readerModel");//读头型号
        String readMode = request.getParameter("readMode");// 读取数据模式 0 普通模式 / 1 身份证模式
        if (StringUtils.isBlank(applyTo)) {
            applyTo = "";
        }
        item.setIdCardMode(readMode);
        accReaderService.saveItem(item, applyTo);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("acc:reader:refresh")
    @Override
    public DxGrid list(AccReaderItem condition) {
        Pager pager =
            accReaderService.loadPagerByAuthFilter(request.getSession().getId(), condition, getPageNo(), getPageSize());// 根据登录用户权限过滤设备
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("acc:reader:del")
    @Override
    public ZKResultMsg del(String ids) {
        accReaderService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public boolean isExist(AccReaderItem item) {
        return accReaderService.isExist(item.getName());
    }

    @Override
    public boolean isExistVid() {
        return accReaderService.isExistVid();
    }

    @Override
    public boolean isExistIP(AccReaderItem item) {

        return accReaderService.isExistIP(item.getIp());
    }

    @Override
    public boolean readerCommAddressExist(AccReaderItem item) {

        return accReaderService.readerCommAddressExist(item);
    }

    @Override
    public DxGrid getWGReaderFilterList(AccSelectReaderRadioItem condition) {
        Pager pager = accReaderService.getWGReaderFilterListByAuthFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize());
        return GridUtil.convert(pager, AccSelectReaderRadioItem.class);
    }

    @Override
    public DxGrid selectReaderList(AccSelectReaderItem condition) {
        if (StringUtils.isBlank(condition.getSelectId())) {
            condition.setSelectId("-1");
        }
        if (condition.getType().equals("noSelected")) {
            condition.setNotInId(condition.getSelectId());
        } else if (condition.getType().equals("selected")) {
            condition.setInId(condition.getSelectId());
        }
        String authAreaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId());
        if (StringUtils.isNotBlank(authAreaIds)) {
            condition.setAuthAreaIdIn(authAreaIds);
        }
        Pager pager = accReaderService.getSelectItemByPage(condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public DxGrid selectReaderBindAiDeviceList(AccSelectReaderRadioItem condition) {
        if (StringUtils.isBlank(condition.getSelectId())) {
            condition.setSelectId("-1");
        }
        if (condition.getType().equals("noSelected")) {
            condition.setNotInId(condition.getSelectId());
        } else if (condition.getType().equals("selected")) {
            condition.setInId(condition.getSelectId());
        }
        Pager pager = accReaderService.selectReaderBindAiDeviceList(condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public DxGrid getTcpReaderFilterList(AccSelectReaderRadioItem condition) {
        Pager pager = accReaderService.getTcpReaderFilterList(condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, AccSelectReaderRadioItem.class);
    }

    @Override
    public ZKResultMsg startReaderIDCard() {
        String readerId = request.getParameter("readerId");
        String result = "-1"; // 表示处理失败
        if (StringUtils.isNotBlank(readerId)) {
            result = accReaderService.startReaderIDCard(readerId);
        }
        return new ZKResultMsg(result);
    }

    @Override
    public ModelAndView getChannelByEntityId() {
        String entityId = request.getParameter("entityId");
        request.setAttribute("entityId", entityId);
        request.setAttribute("entityName", "AccReader");
        // 获取已绑定当前实体的摄像头（通道）
        String channel2EntityIds = accChannelService.getBindChannelIds(Arrays.asList(entityId), "AccReader");
        request.setAttribute("value", channel2EntityIds);
        AccReaderItem readerItem = accReaderService.getItemById(entityId);
        request.setAttribute("deviceSn", readerItem.getDeviceSn());
        return new ModelAndView("acc/reader/accReaderSelectChannelContent");
    }

    @RequiresPermissions("acc:reader:bindChannel")
    @LogRequest(module = "acc_module", object = "common_leftMenu_reader", opType = "common_vid_bindOrUnbindChannel",
        requestParams = {}, opContent = "common_vid_bindOrUnbindChannel")
    @Override
    public ZKResultMsg bindOrUnbindChannel() {
        String entityId = request.getParameter("entityId");
        String entityName = request.getParameter("entityName");
        String channelIds = request.getParameter("channelIds");
        String deviceSn = request.getParameter("deviceSn");
        ZKResultMsg ret = accChannelService.bindOrUnbindChannel(deviceSn, channelIds, entityName, entityId);
        return I18nUtil.i18nMsg(ret);
    }
}