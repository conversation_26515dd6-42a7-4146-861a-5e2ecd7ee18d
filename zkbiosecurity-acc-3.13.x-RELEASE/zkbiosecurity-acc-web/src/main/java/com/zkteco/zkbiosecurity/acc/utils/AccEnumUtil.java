package com.zkteco.zkbiosecurity.acc.utils;


import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

/**
 * 枚举工具类
 * 和固件相关的兼容性（比如报警等）均在此处以多维数组形式存在。
 * @Modified By:
 */
public class AccEnumUtil
{

	public enum Sensor
	{
		// 门磁状态
		OFFLINE(new String[] {"acc_rtMonitor_unknown"}, ConstUtil.DEV_STATE_OFFLINE),
		ONLINE(new String[] {"acc_rtMonitor_noSensor", "common_close", "common_open"},ConstUtil.DEV_STATE_ONLINE),
		DISABLE(new String[] {"acc_rtMonitor_unknown"}, ConstUtil.DEV_STATE_DISABLE);

		// 成员变量
		private String[] values;
		private int index;

		// 构造方法
		private Sensor(String[] values, int index)
		{
			this.values = values;
			this.index = index;
		}

		// 普通方法
		public static String getValue(int index, int valueIndex)
		{
			for (Sensor c : Sensor.values())
			{
				if (c.getIndex() == index)
				{
					return c.values.length == 1 ? c.values[0] : c.values[valueIndex];
				}
			}
			return null;
		}

		// get 方法
		public int getIndex()
		{
			return index;
		}

		public String[] getValues()
		{
			return values;
		}
	}

	public enum Relay
	{
		// 继电器状态
		OFFLINE(new String[] {"acc_rtMonitor_unknown"}, ConstUtil.DEV_STATE_OFFLINE),
		ONLINE(new String[] {"common_close", "common_open"}, ConstUtil.DEV_STATE_ONLINE),
		DISABLE(new String[] {"acc_rtMonitor_unknown"}, ConstUtil.DEV_STATE_DISABLE);

		// 成员变量
		private String[] values;
		private int index;

		// 构造方法
		private Relay(String[] values, int index)
		{
			this.values = values;
			this.index = index;
		}

		// 普通方法
		public static String getValue(int index, int valueIndex)
		{
			for (Relay c : Relay.values())
			{
				if (c.getIndex() == index)
				{
					return c.values.length == 1 ? c.values[0] : c.values[valueIndex];
				}
			}
			return null;
		}

		// get 方法
		public int getIndex()
		{
			return index;
		}

		public String[] getValues()
		{
			return values;
		}
	}

	// 报警状态
	public enum Alarm
	{
		//在线,级别,状态
		OFFLINE(new String[][] {
				{"common_none"},
				{"common_none"}
			}, ConstUtil.DEV_STATE_OFFLINE),
		ONLINE(new String[][] {
				{ "common_none", "acc_rtMonitor_alarm", "acc_rtMonitor_openTimeout"},
				{"common_none", "acc_rtMonitor_openForce", "acc_rtMonitor_tamper", "acc_rtMonitor_duressPwdOpen",
					"acc_rtMonitor_duressFingerOpen", "acc_rtMonitor_duressOpen", "acc_rtMonitor_openTimeout",
					"acc_newEventNo_58", "acc_newEventNo_54", "acc_newEventNo_55",
					"acc_newEventNo_56", "acc_newEventNo_57", "acc_newEventNo_106", "acc_newEventNo_107",
						"acc_newEventNo_109"
				}
			},ConstUtil.DEV_STATE_ONLINE), 
		DISABLE(new String[][] { 
				{"acc_rtMonitor_unknown"},
				{"acc_rtMonitor_unknown"}
			}, ConstUtil.DEV_STATE_DISABLE);

		// 成员变量
		private String[][] values;
		private int index;

		// 构造方法
		private Alarm(String[][] values, int index)
		{
			this.values = values;
			this.index = index;
		}

		public static int getAlarmType(int alarmLevel, int alarm)
		{
			if(alarmLevel != 0)
			{
				if((alarm & 8) == 8 &&  (alarm & 4) == 4)//胁迫指纹开门和胁迫密码开门
				{
					return 5;
				}
				else if((alarm & 8) == 8)//胁迫指纹开门
				{
					return 4;
				}
				else if((alarm & 4) == 4)//胁迫密码开门
				{
					return 3;
				}
				else if((alarm & 2) == 2)//防拆
				{
					return 2;
				}
				else if((alarm & 1) == 1)//意外开门
				{
					return 1;
				}
				else if((alarm & 16) == 16)//门开超时
				{
					return 6;
				}
				else if((alarm & 256) == 256)//常开报警
				{
					return 7;
				}
				else if((alarm & 512) == 512)//电池电压过低
				{
					return 8;
				}
				else if((alarm & 1024) == 1024)//请立即更换电池
				{
					return 9;
				}
				else if((alarm & 2048) == 2048)//非法操作
				{
					return 10;
				}
				else if((alarm & 4096) == 4096)//启用后备电源
				{
					return 11;
				}
				else if((alarm & 32) == 32)//市电掉电
				{
					return 12;
				}
				else if((alarm & 64) == 64)//电池掉电
				{
					return 13;
				}
				else if((alarm & 128) == 128)//读头防拆报警
				{
					return 14;
				}
				return 0;//无报警
			}
			else
			{
				if(alarm > 0 && (alarm & 16) != 16)
				{
					return 1;
				}
				else if((alarm & 16) == 16)//门开超时
				{
					return 2;
				}
			}
			return alarm;
		}
		
		// 普通方法
		public static String getValue(int index, int alarmLevel, int alarmIndex)
		{
			for (Alarm c : Alarm.values())
			{
				if (c.getIndex() == index)
				{
					if (index == ConstUtil.DEV_STATE_OFFLINE || index == ConstUtil.DEV_STATE_DISABLE)
					{
						return c.values[0][0];
					}
					else
					{
						alarmIndex = getAlarmType(alarmLevel, alarmIndex);
						return c.values[alarmLevel][alarmIndex];
					}
				}
			}
			return null;
		}

		// get方法
		public int getIndex()
		{
			return index;
		}

		public String[][] getValues()
		{
			return values;
		}
	}

	public enum DoorImage
	{
		// 门状态图
		OFFLINE(new String[][][][] { { { { "offline", "offline_to_device" } } } }, ConstUtil.DEV_STATE_OFFLINE), 
		ONLINE(new String[][][][] {// [新老固件][报警状态][有无继电器][门磁状态]
				{// 老固件
					{// alarm=0
						{ "nosensor_old", "closed_old", "opened_old" },// relay=0
						{ "nosensor_old", "closed_old", "opened_old" }// relay=1   // (sensor=0,1,2)
					}, 
					{// alarm=1
						{ "alarm_nosensor_old", "alarm_closed_old", "alarm_opened_old" },
						{ "alarm_nosensor_old", "alarm_closed_old", "alarm_opened_old" }
					}, 
					{// alarm=2
						{ "open_timeout_old", "alarm_timeout_closed_old", "open_timeout_old" },
						{ "open_timeout_old", "open_timeout_old", "open_timeout_old" }
					}
				}, 
				{// 新固件
					{// 无报警
						{ "nosensor", "closed", "opened" },// relay=0
						{ "nosensor_unlocked", "closed_unlocked", "opened_unlocked" },// relay=1
					}, 
					{// 有报警
						{ "alarm_nosensor", "alarm_closed", "alarm_opened" }, 
						{ "alarm_nosensor_unlocked", "alarm_closed_unlocked", "alarm_opened_unlocked" } 
					}, 
					{// 门开超时(有且仅有门开超时时才会到这里)。
						{ "alarm_nosensor", "alarm_timeout_closed", "alarm_timeout_opened" }, 
						{ "alarm_nosensor_unlocked", "alarm_timeout_closed_unlocked", "alarm_timeout_opened_unlocked"}
					},
					{// 市电掉电
						{ "alarm_nosensor_cutout", "alarm_closed_cutout", "alarm_opened_cutout" }, 
						{ "alarm_nosensor_cutout", "alarm_closed_cutout", "alarm_opened_cutout"}
					},
					{// 电池掉电
						{ "alarm_nosensor_poweroff", "alarm_closed_poweroff", "alarm_opened_poweroff" }, 
						{ "alarm_nosensor_poweroff", "alarm_closed_poweroff", "alarm_opened_poweroff"}
					}
				}
			},ConstUtil.DEV_STATE_ONLINE), 
		DISABLE(new String[][][][] { { { { "disabled", "disabled" } } } }, ConstUtil.DEV_STATE_DISABLE), 
		LOCK(new String[][][][] { { { { "alwayslocked"} } } }, 3);

		// 成员变量
		private String[][][][] values;
		private int index;

		// 构造方法
		private DoorImage(String[][][][] values, int index)
		{
			this.values = values;
			this.index = index;
		}

		private static int getAlarmState(int alarmLevel,int alarmIndex)
		{
			if(alarmLevel != 0)
			{
				if((alarmIndex & 128) == 128)//读头防拆报警
				{
					return 1;
				}
				if((alarmIndex & 64) == 64)//电池掉电
				{
					return 4;
				}
				else if((alarmIndex & 32) == 32)//市电掉电
				{
					return 3;
				}
				else if((alarmIndex & 16) == 16)//门开超时报警
				{
					return 2;
				}
				else if((alarmIndex & 15) != 0)//仅报警
				{
					return 1;
				}
				else if(((alarmIndex >> 8) & 31) != 0)//无线锁需要先右移8位,仅报警
				{
					return 1;
				}
			}
			else
			{
				if((alarmIndex & 15) != 0)//仅报警
				{
					return 1;
				}
				else if((alarmIndex & 16) == 16)//门开超时报警
				{
					return 2;
				}
				else if((alarmIndex & 32) == 32)//市电掉电
				{
					return 3;
				}
				else if((alarmIndex & 64) == 64)//电池掉电
				{
					return 4;
				}
			}
			return 0;//即无报警，也无门开超时
		}
		
		// 普通方法
		public static String getValue(int index,int devOnline, int alarmLevel, int alarmIndex, int relayIndex, int sensorIndex)
		{
			if (devOnline == ConstUtil.DEV_STATE_ONLINE && index == ConstUtil.DEV_STATE_LOCK)
			{
				return LOCK.values[0][0][0][0];
			}
			for (DoorImage c : DoorImage.values())
			{
				if (c.getIndex() == devOnline)
				{
					if ((devOnline == ConstUtil.DEV_STATE_OFFLINE || devOnline == ConstUtil.DEV_STATE_DISABLE))
					{
						return c.values[0][0][0][0];
					}
					if ((devOnline == ConstUtil.DEV_STATE_ONLINE && (index & 1) == ConstUtil.DEV_STATE_OFFLINE))
					{
						return OFFLINE.values[0][0][0][1];
					}
					else
					{
						alarmIndex = getAlarmState(alarmLevel, alarmIndex);
						return c.values[alarmLevel][alarmIndex][relayIndex][sensorIndex];
					}
				}
			}
			return null;
		}

		// get方法
		public int getIndex()
		{
			return index;
		}

		public String[][][][] getValues()
		{
			return values;
		}
	}

	public enum AuxInImage
	{
		// 辅助输入状态图
		OFFLINE(new String[] { "off" }, ConstUtil.DEV_STATE_OFFLINE), 
		ONLINE(new String[] { "on" }, ConstUtil.DEV_STATE_ONLINE), 
		DISABLE(new String[] { "disable" }, ConstUtil.DEV_STATE_DISABLE);

		// 成员变量
		private String[] values;
		private int index;

		// 构造方法
		private AuxInImage(String[] values, int index)
		{
			this.values = values;
			this.index = index;
		}

		// 普通方法
		public static String getValue(int index)
		{
			return getValue(index, 0);
		}
		
		// 普通方法
		public static String getValue(int index, int valueIndex)
		{
			for (AuxInImage c : AuxInImage.values())
			{
				if (c.getIndex() == index)
				{
					return c.values[valueIndex];
				}
			}
			return null;
		}

		// get 方法
		public int getIndex()
		{
			return index;
		}

		public String[] getValues()
		{
			return values;
		}
	}

	public enum AuxOutImage
	{
		// 辅助输入状态图
		OFFLINE(new String[] { "off" }, ConstUtil.DEV_STATE_OFFLINE), 
		ONLINE(new String[] { "on" }, ConstUtil.DEV_STATE_ONLINE), 
		DISABLE(new String[] { "disable" }, ConstUtil.DEV_STATE_DISABLE);

		// 成员变量
		private String[] values;
		private int index;

		// 构造方法
		private AuxOutImage(String[] values, int index)
		{
			this.values = values;
			this.index = index;
		}

		// 普通方法
		public static String getValue(int index)
		{
			return getValue(index, 0);
		}
		
		// 普通方法
		public static String getValue(int index, int valueIndex)
		{
			for (AuxOutImage c : AuxOutImage.values())
			{
				if (c.getIndex() == index)
				{
					return c.values[valueIndex];
				}
			}
			return null;
		}

		// get 方法
		public int getIndex()
		{
			return index;
		}

		public String[] getValues()
		{
			return values;
		}
	}
	
	public enum EventLevelColor
	{
		LEVEL0("green", 0), LEVEL1("#E57A14", 1), LEVEL2("red", 2);
		// 成员变量
		private String value;
		private int index;

		// 构造方法
		private EventLevelColor(String value, int index)
		{
			this.value = value;
			this.index = index;
		}

		// 普通方法
		public static String getValue(int index)
		{
			for (EventLevelColor c : EventLevelColor.values())
			{
				if (c.getIndex() == index)
				{
					return c.value;
				}
			}
			return null;
		}

		// get set 方法
		public String getValue()
		{
			return value;
		}

		public int getIndex()
		{
			return index;
		}
	}

	public enum ReaderState
	{
		IN(I18nUtil.i18nCode("common_in"), 0), OUT(I18nUtil.i18nCode("common_out"), 1), NO(I18nUtil.i18nCode("common_none"), 2);
		// 成员变量
		private String value;
		private int index;

		// 构造方法
		private ReaderState(String value, int index)
		{
			this.value = value;
			this.index = index;
		}

		// 普通方法
		public static String getValue(int index)
		{
			for (ReaderState c : ReaderState.values())
			{
				if (c.getIndex() == index)
				{
					return c.value;
				}
			}
			return null;
		}

		// get set 方法
		public String getValue()
		{
			return value;
		}

		public int getIndex()
		{
			return index;
		}
	}
}