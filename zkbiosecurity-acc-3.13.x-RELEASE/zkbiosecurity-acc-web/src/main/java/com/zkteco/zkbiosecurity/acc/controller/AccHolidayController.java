/*
 * @author: GenerationTools
 * 
 * @date: 2018-02-26 下午05:53 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zkteco.zkbiosecurity.acc.remote.AccHolidayRemote;
import com.zkteco.zkbiosecurity.acc.service.AccHolidayService;
import com.zkteco.zkbiosecurity.acc.vo.AccHolidayItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 门禁节假日controller
 * 
 * @author: GenerationTools
 * @date: 2018-02-26 下午05:53
 */
@Controller
public class AccHolidayController extends BaseController implements AccHolidayRemote {

    //private final int MAX_HOLIDAY_COUNT_PER_TYPE = 32;
    @Autowired
    private AccHolidayService accHolidayService;

    @RequiresPermissions("acc:holiday")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/holiday/accHoliday");
    }

    @RequiresPermissions({"acc:holiday:edit", "acc:holiday:add"})
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("tempAccHoliday", accHolidayService.getItemById(id));
        }
        return new ModelAndView("acc/holiday/editAccHoliday");
    }

    @RequiresPermissions({"acc:holiday:edit", "acc:holiday:add"})
    @LogRequest(module = "acc_module", object = "common_leftMenu_holiday", opType = "common_op_edit",
        requestParams = {"name"}, opContent = "common_holiday_name")
    @Override
    public ZKResultMsg save(AccHolidayItem item) {
        ZKResultMsg res = new ZKResultMsg();
        accHolidayService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("acc:holiday:refresh")
    @Override
    public DxGrid list(AccHolidayItem codition) {
        Pager pager = accHolidayService.getItemsByPage(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:holiday:del")
    @LogRequest(module = "acc_module", object = "common_leftMenu_holiday", opType = "common_op_del",
        requestParams = {"names"}, opContent = "common_holiday_name")
    @Override
    public ZKResultMsg del(String ids) {
        accHolidayService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public String isExist(String name) {
        return accHolidayService.isExist(name);
    }

    @Override
    public ZKResultMsg dataValid(AccHolidayItem accHolidayItem) {
        String result = "true";
        Date nowDate = new Date();
        // 去除数量限制，设备没有数量限制
        /*int holidayCount = accHolidayService.getByCondition(new AccHolidayItem()).size();
        if (StringUtils.isNotBlank(accHolidayItem.getId())) {
            // 如果是编辑操作，统计时减去自身
            holidayCount--;
        }
        if (holidayCount == MAX_HOLIDAY_COUNT_PER_TYPE) {
            // 每种假日类型包含的节假日数量不能超过%s个
            result = I18nUtil.i18nCode("common_holiday_countError", String.valueOf(MAX_HOLIDAY_COUNT_PER_TYPE));
            return I18nUtil.i18nMsg(new ZKResultMsg(result));
        }*/
        Date startDate = accHolidayItem.getStartDate();
        Date endDate = accHolidayItem.getEndDate();
        if (startDate.getTime() > endDate.getTime()) {
            result = I18nUtil.i18nCode("common_startEndDateCompare");// 开始日期不能大于结束日期.
            return I18nUtil.i18nMsg(new ZKResultMsg(result));
        }
        if (DateUtil.getYear(endDate) < DateUtil.getYear(nowDate)
            || DateUtil.getYear(startDate) < DateUtil.getYear(nowDate)
            || DateUtil.getYear(endDate) - DateUtil.getYear(startDate) != 0) {
            result = I18nUtil.i18nCode("common_holiday_fillError3");// 节假日日期不能跨年.
            return I18nUtil.i18nMsg(new ZKResultMsg(result));
        }
        List<Date> currentDate = getDateBetween(startDate, endDate);
        List<Date> existDate = getAllHolidaysDate(accHolidayItem);
        for (Date date : currentDate) {
            if (existDate.contains(date)) {
                // 日期：%s 已经被设置为节假日，不能重复设置！
                result = I18nUtil.i18nCode("common_holiday_fillError4",
                    DateUtil.dateToString(date, DateUtil.DateStyle.YYYY_MM_DD));
                return I18nUtil.i18nMsg(new ZKResultMsg(result));
            }
        }
        List<String> monthAndDay = Lists.newArrayList();
        for (Date date : currentDate) {
            monthAndDay.add(DateUtil.getMonth(date) + "-" + DateUtil.getDay(date));
        }
        Set<List<String>> allMonthDay = getAllMonthDay(accHolidayItem);
        for (Date date : currentDate) {
            if (allMonthDay.contains(monthAndDay)) {
                // 按年循环日期：%s - %s 设置重复
                result = I18nUtil.i18nCode("common_holiday_fillError5", String.valueOf(DateUtil.getMonth(date)),
                    String.valueOf(DateUtil.getDay(date)));
                return I18nUtil.i18nMsg(new ZKResultMsg(result));
            }
        }
        return I18nUtil.i18nMsg(new ZKResultMsg(result));
    }

    /**
     * @Description: 获取单个节假日对象的日期
     * <AUTHOR>
     * @date 2018/5/25 14:27
     * @param startDate
     * @param endDate
     * @return
     */
    public List<Date> getDateBetween(Date startDate, Date endDate) {
        List<Date> dateList = Lists.newArrayList();
        int delta = (int)((endDate.getTime() - startDate.getTime()) / 1000 / 24 / 60 / 60);
        for (int d = 0; d < delta + 1; d++) {
            dateList.add(new Date(startDate.getTime() + 1000L * 24 * 60 * 60 * d));
        }
        return dateList;
    }

    /**
     * @Description: 获取所有已设置为节假日的日期 （如果是编辑，当前节假日应除外）
     * <AUTHOR>
     * @date 2018/5/25 11:23
     * @param accHolidayItem
     * @return
     */
    public List<Date> getAllHolidaysDate(AccHolidayItem accHolidayItem) {
        Set<Date> allDate = Sets.newHashSet();
        List<AccHolidayItem> holidayList = Lists.newArrayList();
        if (StringUtils.isNotBlank(accHolidayItem.getId())) {
            AccHolidayItem tempHoliday = new AccHolidayItem();
            tempHoliday.setNotInId(accHolidayItem.getId());
            holidayList.addAll(accHolidayService.getByCondition(tempHoliday));
        } else {
            List<AccHolidayItem> holidays = accHolidayService.getByCondition(new AccHolidayItem());
            if (holidays != null && holidays.size() > 0) {
                holidayList.addAll(holidays);
            }
        }

        holidayList.stream().forEach(holiday -> {
            Date startDate = holiday.getStartDate();
            Date endDate = holiday.getEndDate();
            allDate.addAll(getDateBetween(startDate, endDate));
        });
        return Lists.newArrayList(allDate);
    }

    /**
     * @Description: 获取日期中的月份和天，来判断按年循环的有效性。
     * <AUTHOR>
     * @date 2018/5/25 14:28
     * @param accHolidayItem
     * @return
     */
    public Set<List<String>> getAllMonthDay(AccHolidayItem accHolidayItem)// 所有的月和日
    {
        AccHolidayItem tempHoliday = null;
        Set<Date> allDate = Sets.newHashSet();
        List<String> allMonthDay = Lists.newArrayList();
        List<AccHolidayItem> allHolidaysList = Lists.newArrayList();
        if (StringUtils.isNotBlank(accHolidayItem.getId())) { // 编辑时过滤自身
            if (!accHolidayItem.getIsLoopByYear()) {
                tempHoliday = new AccHolidayItem();
                tempHoliday.setNotInId(accHolidayItem.getId());
                tempHoliday.setIsLoopByYear(true);
                allHolidaysList.addAll(accHolidayService.getByCondition(tempHoliday));
            } else {
                tempHoliday = new AccHolidayItem();
                tempHoliday.setNotInId(accHolidayItem.getId());
                allHolidaysList.addAll(accHolidayService.getByCondition(tempHoliday));
            }
        } else { // 新增取出所有
            if (!accHolidayItem.getIsLoopByYear()) {
                tempHoliday = new AccHolidayItem();
                tempHoliday.setIsLoopByYear(true);
                allHolidaysList.addAll(accHolidayService.getByCondition(tempHoliday));
            } else {
                allHolidaysList.addAll(accHolidayService.getByCondition(new AccHolidayItem()));
            }
        }
        allHolidaysList.stream().forEach(holiday -> {
            Date startDate = holiday.getStartDate();
            Date endDate = holiday.getEndDate();
            allDate.addAll(getDateBetween(startDate, endDate));
        });
        allDate.stream().forEach(date -> {
            String monthAndDay = DateUtil.getMonth(date) + "-" + DateUtil.getDay(date);
            allMonthDay.add(monthAndDay);
        });
        return new HashSet(allMonthDay);
    }
}