/*
 * @author: GenerationTools
 * 
 * @date: 2018-03-13 上午09:53 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.remote.AccInterlockRemote;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.acc.service.AccInterlockService;
import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccInterlockItem;
import com.zkteco.zkbiosecurity.acc.vo.AccInterlockSelectDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccInterlockSelectDoorItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * @author: GenerationTools
 * @date: 2018-03-13 上午09:53
 */
@Controller
public class AccInterlockController extends BaseController implements AccInterlockRemote {
    @Autowired
    private AccInterlockService accInterlockService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDoorService accDoorService;

    @RequiresPermissions("acc:interlock")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/interlock/accInterlock");
    }

    @RequiresPermissions({"acc:interlock:add", "acc:interlock:edit"})
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("item", accInterlockService.getItemById(id));
        }
        return new ModelAndView("acc/interlock/editAccInterlock");
    }

    @RequiresPermissions({"acc:interlock:add", "acc:interlock:edit"})
    @LogRequest(module = "acc_module", object = "acc_eventNo_25", opType = "common_op_edit", requestParams = {"name"},
        opContent = "common_name")
    @Override
    public ZKResultMsg save(AccInterlockItem item) {
        ZKResultMsg res = new ZKResultMsg();
        accInterlockService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("acc:interlock:refresh")
    @Override
    public DxGrid list(AccInterlockItem condition) {
        Pager pager = accInterlockService.loadPagerByAuthFilter(request.getSession().getId(), condition, getPageNo(), getPageSize());
        // 根据反潜转换成相应的门名称组合显示在反潜列表页面中
        List<AccInterlockItem> accAntiPassbackItemList = (List<AccInterlockItem>)pager.getData();
        accAntiPassbackItemList.forEach(accInterlockItem -> {
            if (accInterlockItem.getInterlockRule() != null) {
                accInterlockItem
                    .setInterlockRuleShow(accInterlockService.convertInterlockRule(accInterlockItem.getId()));
            } else if (accInterlockItem.getLockRule() != null) {
                accInterlockService.completionItem(accInterlockItem);
                String apbType = accInterlockItem.getLockRule() == 1 ? I18nUtil.i18nCode("acc_interlock_ruleInfo") : I18nUtil.i18nCode("acc_globalInterlock_isGroupInterlock");
                String group1 = I18nUtil.i18nCode("acc_interlock_group1") + ":" + accInterlockItem.getGroup1DoorNames();
                String group2 = "";
                if (StringUtils.isNotBlank(accInterlockItem.getGroup2DoorNames())) {
                    group2 = I18nUtil.i18nCode("acc_interlock_group2") + ":" + accInterlockItem.getGroup2DoorNames();
                }
                accInterlockItem.setInterlockRuleShow((apbType + " " + group1 + " " + group2).trim());
            }
        });
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("acc:interlock:del")
    @LogRequest(module = "acc_module", object = "acc_eventNo_25", opType = "common_op_del",
        requestParams = {"deviceNames"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg del(String ids) {
        accInterlockService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public ZKResultMsg getRule(String deviceId) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        List<SelectItem> selectItemList = new ArrayList<SelectItem>();
        Map<Integer, String> doorMap = accInterlockService.getRule(deviceId);
        if (doorMap.size() > 1)// 当前控制器为两门
        {
            SelectItem selectItem = new SelectItem();
            selectItem.setValue("1");
            String rule1 = I18nUtil.i18nCode("acc_interlock_mode1Or2", doorMap.get(1), doorMap.get(2));
            selectItem.setText(rule1);// 1-2 门互锁
            selectItemList.add(selectItem);
        }
        if (doorMap.size() == 4)// 当前控制器为四门
        {
            SelectItem selectItem2 = new SelectItem();
            selectItem2.setValue("2");
            String rule2 = I18nUtil.i18nCode("acc_interlock_mode1Or2", doorMap.get(3), doorMap.get(4));
            selectItem2.setText(rule2);// 3-4门互锁
            selectItemList.add(selectItem2);

            SelectItem selectItem3 = new SelectItem();
            selectItem3.setValue("3");
            String rule3 = I18nUtil.i18nCode("acc_interlock_mode3", doorMap.get(1), doorMap.get(2), doorMap.get(3));
            selectItem3.setText(rule3);// 1-2-3 门互锁
            selectItemList.add(selectItem3);

            SelectItem selectItem4 = new SelectItem();
            selectItem4.setValue("4");
            String rule4 = I18nUtil.i18nCode("acc_interlock_mode4", doorMap.get(1), doorMap.get(2), doorMap.get(3),
                doorMap.get(4));
            selectItem4.setText(rule4);
            selectItemList.add(selectItem4);

            SelectItem selectItem5 = new SelectItem();
            selectItem5.setValue("5");
            String rule5 = I18nUtil.i18nCode("acc_interlock_mode5", doorMap.get(1), doorMap.get(2), doorMap.get(3),
                doorMap.get(4));
            selectItem5.setText(rule5);// 1-2-3-4 门互锁
            selectItemList.add(selectItem5);
        }
        zkResultMsg.setData(selectItemList);
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @Override
    public DxGrid selectDevicelist(AccInterlockSelectDeviceItem condition) {
        String filterDevId = accInterlockService.getDevIdWithInterlock();
        if (StringUtils.isBlank(condition.getSelectId())) {
            condition.setSelectId("-1");
        }
        if (condition.getType().equals("noSelected")) {
            if (StringUtils.isNotBlank(filterDevId)) {
                filterDevId = condition.getSelectId() + "," + filterDevId;
                condition.setSelectId(filterDevId);
            }
            condition.setSelectDeviceIdsNotIn(condition.getSelectId());
        } else if (condition.getType().equals("selected")) {
            condition.setSelectDeviceIdsIn(condition.getSelectId());
        }
        Pager pager = accDeviceService.loadInterlockSelectDeviceByAuthFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public ModelAndView selectDoor(String deviceId, String notInId, String interLockId, String group) {
        request.setAttribute("deviceId", deviceId);
        request.setAttribute("notInId", notInId);
        request.setAttribute("interLockId", interLockId);
        request.setAttribute("group", group);
        return new ModelAndView("acc/interlock/accInterlockSelectDoor");
    }

    @Override
    public DxGrid selectDoorlist(AccInterlockSelectDoorItem codition) {
        if (StringUtils.isBlank(codition.getSelectId())) {
            codition.setSelectId("-1");
        }
        codition.setEnabled(true);
        if (codition.getType().equals("noSelected")) {
            codition.setSelectDoorIdsNotIn(codition.getSelectId());
        } else if (codition.getType().equals("selected")) {
            codition.setSelectDoorIdsIn(codition.getSelectId());
        }
        Pager pager = accDoorService.getItemsByPage(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @Override
    public boolean validName(String name) {
        return !accInterlockService.nameExists(name);
    }

    @Override
    public ZKResultMsg getAutoFillDoorByDev(String devId) {
        ZKResultMsg msg = new ZKResultMsg();
        if (StringUtils.isNotBlank(devId)) {
            String lockCount = accInterlockService.getLockCountByDevice(devId);
            if ("2".equals(lockCount)) {
                List<String> devIdList = new ArrayList<>();
                devIdList.add(devId);
                List<AccDoorItem> doorList = accDoorService.getItemsByDevIds(devIdList);
                if (doorList != null && doorList.size() > 0) {
                    List<JSONObject> doorMsgList = doorList.stream().parallel().map(door -> {
                        JSONObject json = new JSONObject();
                        json.put("id", door.getId());
                        json.put("name", door.getName());
                        return json;
                    }).collect(Collectors.toList());
                    msg.setData(doorMsgList);
                }
            }
        }
        return msg;
    }

    @Override
    public ZKResultMsg validDetermineInterlock(String deviceId) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        zkResultMsg.setData(accInterlockService.validDetermineInterlock(deviceId));
        return I18nUtil.i18nMsg(zkResultMsg);
    }

}