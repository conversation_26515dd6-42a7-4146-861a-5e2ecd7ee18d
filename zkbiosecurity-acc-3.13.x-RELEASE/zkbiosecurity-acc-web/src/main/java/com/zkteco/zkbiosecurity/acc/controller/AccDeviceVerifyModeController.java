/*
 * @author: GenerationTools
 * 
 * @date: 2018-03-20 下午04:20 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.remote.AccDeviceVerifyModeRemote;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceVerifyModeService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceVerifyModeItem;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.format.TreeBuilder;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.*;

/**
 * 设备验证方式Controller
 */
@Controller
public class AccDeviceVerifyModeController extends BaseController implements AccDeviceVerifyModeRemote {

    @Autowired
    private AccDeviceVerifyModeService accDeviceVerifyModeService;

    @Override
    public TreeItem getDeviceVerifyModeTree() {
        List<TreeItem> items = new ArrayList<>();
        Map<Integer, String> verifyModesMap = AccConstants.VERIFY_MODE;
        // 对map的key进行升序排序
        Set keySet = verifyModesMap.keySet();
        Object[] keySetArray = keySet.toArray();
        Arrays.sort(keySetArray);
        for (Object key : keySetArray) {
            TreeItem item = new TreeItem();
            item.setId(verifyModesMap.get(key));
            item.setText(I18nUtil.i18nCode(verifyModesMap.get(key)));
            item.setParent(new TreeItem("0"));
            items.add(item);
        }
        List<TreeItem> treeItems = TreeBuilder.newTreeBuilder(TreeItem.class, String.class).buildToTreeList(items);
        return new TreeItem("0", treeItems);
    }

    @Override
    public TreeItem getDeviceVerifyModeTreeByDeviceId(String deviceId) {
        List<AccDeviceVerifyModeItem> accDeviceVerifyModeItemList =
            accDeviceVerifyModeService.getVerifyModeByDeviceId(deviceId);
        List<TreeItem> items = new ArrayList<>();
        for (AccDeviceVerifyModeItem verifyModeItem : accDeviceVerifyModeItemList) {
            TreeItem item = new TreeItem();
            item.setId(verifyModeItem.getVerifyNo().toString());
            item.setText(I18nUtil.i18nCode(verifyModeItem.getName()));
            item.setParent(new TreeItem(verifyModeItem.getDeviceSn()));
            items.add(item);
        }
        List<TreeItem> treeItems = TreeBuilder.newTreeBuilder(TreeItem.class, String.class).buildToTreeList(items);
        return new TreeItem("0", treeItems);
    }

    @Override
    public TreeItem getSupportNewVerifyModeTree() {
        List<AccDeviceVerifyModeItem> accDeviceVerifyModeItemList = accDeviceVerifyModeService.getSupportNewVerifyModeTree();
        List<TreeItem> items = new ArrayList<>();
        for (AccDeviceVerifyModeItem verifyModeItem : accDeviceVerifyModeItemList) {
            TreeItem item = new TreeItem();
            item.setId(verifyModeItem.getVerifyNo().toString());
            item.setText(I18nUtil.i18nCode(verifyModeItem.getName()));
            item.setParent(new TreeItem("0"));
            items.add(item);
        }
        List<TreeItem> treeItems = TreeBuilder.newTreeBuilder(TreeItem.class, String.class).buildToTreeList(items);
        return new TreeItem("0", treeItems);
    }
}