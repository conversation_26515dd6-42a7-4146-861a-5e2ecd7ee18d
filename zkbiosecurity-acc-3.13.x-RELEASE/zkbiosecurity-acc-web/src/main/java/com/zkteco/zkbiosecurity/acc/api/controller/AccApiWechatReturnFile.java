package com.zkteco.zkbiosecurity.acc.api.controller;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller

public class AccApiWechatReturnFile {

    /**
     * 返回微信公众平台验证文件内容
     *
     * @return
     */
    @RequestMapping(value = "/MP_verify_EhhKoouExlmVvQAP.txt", method = RequestMethod.GET, produces = MediaType.TEXT_PLAIN_VALUE)
    @ResponseBody
    public String getWechatVerifyFile() {
        return "EhhKoouExlmVvQAP";
    }
}
