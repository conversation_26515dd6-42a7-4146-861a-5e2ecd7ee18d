package com.zkteco.zkbiosecurity.acc.api.controller;

import java.util.List;

import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.api.vo.AccApiDoorItem;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/11/1 16:42
 */
@Controller
@RequestMapping(value = {"/api/v2/door"})
@Slf4j
@Api(tags = "AccDoor", description = "acc door")
public class AccApiV2DoorController {
    @Autowired
    private AccDoorService accDoorService;

    /**
     * 分页获取门列表
     *
     * @param pageNo
     * @param pageSize
     * @return
     * @auther lambert.li
     * @date 2018/11/13 15:12
     */
    @ResponseBody
    @RequestMapping(value = {"/list"}, method = RequestMethod.GET, produces = "application/json")
    @ApiOperation(value = "Get Door List", notes = "Return Door List", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"pageNo","pageSize"})
    public ApiResultMessage list(@RequestParam(name = "pageNo", required = false) Integer pageNo,
        @RequestParam(name = "pageSize", required = false) Integer pageSize) {
        if (pageNo == null || pageSize == null) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE,
                I18nUtil.i18nCode("acc_api_parameterValueCannotBeNull"));
        }
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        if (pageSize > 1000) {
            return ApiResultMessage.message(AccConstants.API_PAGE_OVERSIZE,
                I18nUtil.i18nCode("common_api_pageSizeOver"));
        }
        Pager pager = accDoorService.getItemsByPage(new AccDoorItem(), pageNo - 1, pageSize);
        List<AccApiDoorItem> apiDoorItemList = Lists.newArrayList();
        List<AccDoorItem> accDoorItemList = (List<AccDoorItem>)pager.getData();
        if (!accDoorItemList.isEmpty()) {
            accDoorItemList.forEach(accDoorItem -> {
                AccApiDoorItem accApiDoorItem = AccApiDoorItem.createApiDoor(accDoorItem);
                if (accApiDoorItem != null) {
                    apiDoorItemList.add(accApiDoorItem);
                }
            });
        }
        pager.setData(apiDoorItemList);
        return ApiResultMessage.successMessage(pager);
    }
}
