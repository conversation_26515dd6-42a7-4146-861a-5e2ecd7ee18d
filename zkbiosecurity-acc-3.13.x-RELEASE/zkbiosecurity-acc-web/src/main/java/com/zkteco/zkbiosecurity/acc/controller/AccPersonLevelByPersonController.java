package com.zkteco.zkbiosecurity.acc.controller;

import java.io.File;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.remote.AccPersonLevelByPersonRemote;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonLevelByPersonService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonService;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * <AUTHOR>
 * @date 2018/3/14 10:54
 */
@Controller
public class AccPersonLevelByPersonController extends ExportController implements AccPersonLevelByPersonRemote {

    private Logger logger = LoggerFactory.getLogger(AccPersonLevelByPersonController.class);
    @Autowired
    private AccPersonLevelByPersonService accPersonLevelByPersonService;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private AccPersonService accPersonService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private PersPersonService persPersonService;
    @Value("${security.session.timeout:1800}")
    private Long sessionTimeout;

    @RequiresPermissions("acc:personLevelByPerson")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/personLevelByPerson/accPersonLevelByPerson");
    }

    @RequiresPermissions("acc:personLevelByPerson:refresh")
    @Override
    public DxGrid list(AccPersonLevelByPersonItem codition) {
        Pager pager = accPersonLevelByPersonService.loadPagerByAuthFilter(request.getSession().getId(), codition,
            getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:personLevelByPerson:refresh")
    @Override
    public DxGrid getPersonLevel(AccPersonLevelItem codition) {
        String authAreaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId());
        if (StringUtils.isNotBlank(authAreaIds)) {
            codition.setAuthAreaIdIn(authAreaIds);
        }
        Pager pager = accLevelService.getAccPersonLevelItemsByPage(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @Override
    public DxGrid getPersonSelectLevel(AccPersonSelectLevelItem codition) {
        if (StringUtils.isBlank(codition.getSelectId())) {
            codition.setSelectId("-1");
        }
        List<String> selectLevelIds = new ArrayList<>(Arrays.asList(codition.getSelectId().split(","))); // 获取选中权限组的ID
        if (codition.getType().equals("noSelected")) {
            StringBuilder notInLevelId = new StringBuilder();
            notInLevelId.append(StringUtils.join(selectLevelIds, ","));
            if (StringUtils.isNotBlank(codition.getNotInId())) {
                notInLevelId.append(",").append(codition.getNotInId());
            }
            codition.setNotInId(notInLevelId.toString());
        } else if (codition.getType().equals("selected")) {
            codition.setInId(StringUtils.join(selectLevelIds, ","));
        }
        codition.setFilterId(codition.getPersonId());
        String authAreaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId()); // 获取当前登录用户所属区域
        if (StringUtils.isNotBlank(authAreaIds)) {
            codition.setAuthAreaIdIn(authAreaIds);
        }
        Pager pager = accLevelService.getItemsByPage(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:personLevelByPerson:addLevel")
    @LogRequest(module = "acc_module", object = "common_leftMenu_levelSetByPerson",
        opType = "common_level_addPersonLevel", requestParams = {"personPin", "levelNames"},
        opContent = "common_level_name")
    @Override
    public ZKResultMsg addLevel(@RequestParam(value = "personId") String personId,
        @RequestParam(value = "levelIds") String levelIds) {
        if (StringUtils.isNotBlank(personId) && StringUtils.isNotBlank(levelIds)) {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_startProcessing") + "<br/>");
            // 添加人员权限到数据库
            accLevelService.addLevelAndTimesByParamIds(personId, levelIds);
            String[] levelAndTimesArray = levelIds.split(",");
            if (levelIds.indexOf(";") > 0) {
                levelAndTimesArray = levelIds.split(";");
            }
            for (int i = 0, total = levelAndTimesArray.length; i < total; i++) {
                String levelId = levelAndTimesArray[i];
                if (levelAndTimesArray[i].indexOf(",") > 0) {
                    levelId = levelAndTimesArray[i].split(",")[0];
                }
                boolean isSetToDev = accLevelService.getDoorCountByLevelId(levelId) > 0;// 判断权限组中是否已经有添加门
                if (isSetToDev) {
                    accLevelService.setPersonLevelTimesToDev(levelId, personId);
                    // 拓展接口，为了兼容华为pad设备下发downLoad命令通知设备已下发完成的功能 by mingfa.zheng 20200727
                    // accLevelService.issuedDownloadToDev("levelType", levelId);
                }
                progressCache.setProcess(new ProcessBean(0, (int)((i + 1) * 1.0 / total * 100)));
            }
            progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish"));
        }
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:personLevelByPerson:delLevel")
    @LogRequest(module = "acc_module", object = "common_leftMenu_levelSetByPerson",
        opType = "common_level_delPersonLevel", requestParams = {"personPin", "levelNames"},
        opContent = "common_level_name")
    @Override
    public ZKResultMsg delLevel(String personId, String levelIds) {
        if (StringUtils.isNotBlank(personId) && StringUtils.isNotBlank(levelIds)) {
            List<String> levelIdList = new ArrayList<>(Arrays.asList(levelIds.split(",")));
            accLevelService.immeDelPersonLevel(levelIdList, personId);
        }
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:personLevelByPerson:setAccParams")
    @Override
    public ModelAndView setAccParam(String ids) {
        AccPersonItem tempAccPerson = null;
        // 获取自定义属性
        String[] personIds = ids.split(",");
        request.setAttribute("ids", ids);
        request.setAttribute("pins", request.getParameter("pins"));
        List<AccLevelItem> tempList = null;
        if (personIds != null && personIds.length == 1) {
            tempAccPerson = accPersonService.getItemByPersonId(personIds[0]);
            request.setAttribute("tempAccPerson", tempAccPerson);
            if (tempAccPerson != null) {
                tempList = accLevelService.getLevelByPersonId(tempAccPerson.getId());
                request.setAttribute("tempList", tempList);
            } else {
                logger.error("per_person acc_person data not sync");
            }
        }
        request.setAttribute("editPage", "true");
        return new ModelAndView("acc/personLevelByPerson/setAccPersonParam");
    }

    @RequiresPermissions("acc:personLevelByPerson:export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AccPersonLevelItem accPersonLevelItem = new AccPersonLevelItem();
        setConditionValue(accPersonLevelItem);
        // 根据当前登录用户所属权限过滤区域。
        String authAreaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId());
        if (StringUtils.isNotBlank(authAreaIds)) {
            accPersonLevelItem.setAuthAreaIdIn(authAreaIds);
        }
        List<AccPersonLevelItem> accPersonLevelItemList =
            accPersonLevelByPersonService.getExportItemList(accPersonLevelItem, getBeginIndex(), getEndIndex());
        Map<String, String> authAreaMap =
            accPersonLevelByPersonService.getAuthAreaByPersonLevel(accPersonLevelItemList);
        accPersonLevelItemList.stream().forEach(item -> {
            item.setAuthAreaName(authAreaMap.get(item.getAuthAreaId()));
        });
        excelExport(accPersonLevelItemList, AccPersonLevelItem.class);
    }

    @RequiresPermissions("acc:personLevelByPerson:export")
    @LogRequest(module = "acc_module", object = "common_leftMenu_levelSetByPerson", opType = "common_op_export",
        opContent = "common_op_export")
    @Override
    public void exportPersonLevel(HttpServletRequest request, HttpServletResponse response) {
        if (isNeedValid("pwd")) {
            try {
                String loginPwd = this.request.getParameter("loginPwd");
                boolean ret = accLevelService.verifyLoginPwd(request.getSession().getId(), loginPwd);
                if (!ret) {
                    throw new ZKBusinessException("auth_user_pwdIncorrect");
                }
            } catch (ZKBusinessException e) {
                throw e;
            } catch (Exception e) {
                this.log.error("", e);
            }
        }
        // 选中的人员ids
        String personIds = request.getParameter("personIds");
        AccPersonLevelItem accPersonLevelItem = new AccPersonLevelItem();
        accPersonLevelItem.setPersonIdIn(personIds);
        // 根据当前登录用户所属权限过滤区域。
        String authAreaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId());
        if (StringUtils.isNotBlank(authAreaIds)) {
            accPersonLevelItem.setAuthAreaIdIn(authAreaIds);
        }
        List<AccPersonLevelItem> accPersonLevelItemList =
            accPersonLevelByPersonService.getExportItemList(accPersonLevelItem, getBeginIndex(), getEndIndex());
        // 数据集为空，不导出
        if (Objects.isNull(accPersonLevelItemList) || accPersonLevelItemList.isEmpty()) {
            // 设置导出状态
            stringRedisTemplate.opsForValue().set(EXPORT_RESULT + request.getSession().getId(), "end");
            stringRedisTemplate.expire(EXPORT_RESULT + request.getSession().getId(), sessionTimeout, TimeUnit.SECONDS);
            throw ZKBusinessException.warnException("common_report_dataSourceNull");
        }

        String reportType = request.getParameter("reportType");
        String jsonColumn = request.getParameter("jsonColumn");
        String fileNamePrefix = request.getParameter("tableNameParam");

        List<String> personIdList = StrUtil.strToList(personIds);
        try {
            List<AccPersonLevelItem> exportPersonLevelItemList = null;
            // excel文件存储的临时路径
            String filePath = ClassUtil.getRootPath() + FileUtils.separator + FileUtils.systemFilePath
                + FileUtils.separator + "acc" + FileUtils.separator + "tempPersonLevel" + FileUtils.separator;
            File file = new File(filePath);
            if (!file.exists()) {
                file.mkdirs();
            }
            // {personId:pin}
            JSONObject jsonData = JSONObject.parseObject(request.getParameter("jsonData"));
            // 获取选中人员的区域map
            Map<String, String> authAreaMap =
                accPersonLevelByPersonService.getAuthAreaByPersonLevel(accPersonLevelItemList);
            Map<String, String> personIdAndPinMap = persPersonService.getPinsByPersonIds(personIdList);
            for (String personId : personIdList) {
                AccPersonLevelItem personLevelItem = new AccPersonLevelItem();
                personLevelItem.setPersonId(personId);
                exportPersonLevelItemList =
                    accPersonLevelByPersonService.getExportItemList(personLevelItem, getBeginIndex(), getEndIndex());
                if (Objects.nonNull(exportPersonLevelItemList) && !exportPersonLevelItemList.isEmpty()) {
                    exportPersonLevelItemList.stream().forEach(item -> {
                        item.setAuthAreaName(authAreaMap.get(item.getAuthAreaId()));
                    });
                    // 文件名，包含后缀
                    String fileName = personIdAndPinMap.get(personId) + "." + ExcelUtil.XLS;
                    // 生成excel文件到指定位置
                    ExcelUtil.generateExcel(exportPersonLevelItemList, AccPersonLevelItem.class, jsonColumn, fileName,
                        reportType, filePath, null);
                }
            }

            /*----------页面reponse设置----------*/
            String agent = request.getHeader("User-Agent");
            boolean isMSIE = (agent != null && (agent.indexOf("MSIE") != -1 || agent.indexOf("Trident") != -1));
            // IE浏览器
            if (isMSIE) {
                fileNamePrefix = java.net.URLEncoder.encode(fileNamePrefix, "UTF-8");
                // IE会将空格转成+号
                fileNamePrefix = fileNamePrefix.replaceAll("\\+", " ");
            } else {
                fileNamePrefix = new String(fileNamePrefix.getBytes("UTF-8"), "ISO-8859-1");
            }
            // Edge浏览器兼容处理
            if (request.getHeader("User-Agent") != null && request.getHeader("User-Agent").indexOf("Edge") != -1) {
                // 空格预处理
                fileNamePrefix = fileNamePrefix.replaceAll(" ", "%20");
                // 编码转换
                fileNamePrefix = URLEncoder.encode(fileNamePrefix, "ISO-8859-1");
                // 空格还原
                fileNamePrefix = fileNamePrefix.replace("%2520", " ");
            }
            // 导出的压缩包路径
            String tempPath = filePath + FileUtils.separator + fileNamePrefix + "_"
                + (new SimpleDateFormat("yyyyMMddHHmmss")).format(new Date()) + ".zip";
            File excelFile = new File(filePath);
            File[] excelFiles = excelFile.listFiles();
            if (StringUtils.isNotBlank(request.getParameter("encryptPassword"))) {
                ArrayList<File> files = new ArrayList<>();
                files.addAll(Arrays.asList(excelFiles));
                FileUtil.fileEncryptZip(tempPath, files, request.getParameter("encryptPassword"), request, response);
            } else {
                response = FileUtil.downLoadFiles(tempPath, excelFiles, request, response);
            }
            response.flushBuffer();
            // 删除临时文件夹
            File zipTempFile = new File(tempPath);
            if (zipTempFile.exists()) {
                // 删除临时压缩包
                zipTempFile.delete();
            }
            FileUtils.deleteDirectory(filePath);
        } catch (Exception e) {
            log.error(fileNamePrefix + " export error", e);
            response.setHeader("Content-Disposition", "");
            response.setContentType("application/json");
            throw new ZKBusinessException("common_report_exportFaild");
        } finally {
            stringRedisTemplate.opsForValue().set(EXPORT_RESULT + request.getSession().getId(), "end");
            stringRedisTemplate.expire(EXPORT_RESULT + request.getSession().getId(), sessionTimeout, TimeUnit.SECONDS);
        }
    }

    @RequiresPermissions("acc:personLevelByPerson:sync")
    @LogRequest(module = "acc_module", object = "common_leftMenu_levelSetByPerson", opType = "common_dev_synchronize",
        opContent = "common_dev_synchronize", requestParams = {"personPins"})
    @Override
    public ZKResultMsg syncPersonLevel(String personIds) {
        if (StringUtils.isNotBlank(personIds)) {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_startProcessing") + "<br/>");
            return accPersonLevelByPersonService.syncPersonLevel(request.getSession().getId(), personIds);
        }
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public DxGrid getPersonSelectLevelByPersonId(AccPersonShowLevelItem condition) {
        if (StringUtils.isBlank(condition.getSelectId())) {
            condition.setSelectId("-1");
        }
        if (StringUtils.isNoneBlank(condition.getPersonId())) {
            condition.setFilterId(condition.getPersonId());
        } else if (condition.getType().equals("selected")) {
            List<String> selectLevelIds = new ArrayList<>(Arrays.asList(condition.getSelectId().split(","))); // 获取选中权限组的ID
            StringBuilder inLevelId = new StringBuilder();
            inLevelId.append(StringUtils.join(selectLevelIds, ","));
            if (StringUtils.isNotBlank(condition.getInId())) {
                inLevelId.append(",").append(condition.getInId());
            }
            condition.setInId(inLevelId.toString());
        }

        List<AccLevelItem> levelList = accLevelService.getPersonShowLevelItem(condition);
        Pager page = new Pager();
        page.setData(levelList);
        page.setTotal(levelList.size());
        return GridUtil.convert(page, condition.getClass());
    }
}
