package com.zkteco.zkbiosecurity.acc.controller;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.remote.AccAlarmTransactionRemote;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.vo.AccAlarmTransactionItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * @author: verber
 * @date: 2018-04-29 14:56:41
 */
@Controller
public class AccAlarmTransactionController extends ExportController implements AccAlarmTransactionRemote {

    @Autowired
    private AccTransactionService accTransactionService;

    @RequiresPermissions("acc:alarmTransaction")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/alarmTransaction/accAlarmTransaction");
    }

    @RequiresPermissions("acc:alarmTransaction:refresh")
    @Override
    public DxGrid list(AccAlarmTransactionItem condition) {
        Pager pager = accTransactionService.loadAlarmTransactionByAuthUserFilter(request.getSession().getId(),
            condition, getPageNo(), getPageSize(), getLimitCount());
        return GridUtil.convert(pager, condition.getClass(), getPageList());
    }

    @RequiresPermissions("acc:alarmTransaction:del")
    @Override
    public ZKResultMsg del(String ids) {
        accTransactionService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:alarmTransaction:del")
    @LogRequest(module = "acc_module", object = "common_leftMenu_exceptTransaction", opType = "common_op_clearData",
        opContent = "common_op_clearData")
    @Override
    public ZKResultMsg clearData() {
        accTransactionService.deleteAllExceptionData();
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:alarmTransaction:export")
    @LogRequest(module = "acc_module", object = "common_leftMenu_exceptTransaction", opType = "common_op_export",
        opContent = "common_op_export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response)
        throws InvocationTargetException, IllegalAccessException {
        AccAlarmTransactionItem accAlarmTransactionItem = new AccAlarmTransactionItem();
        setConditionValue(accAlarmTransactionItem);
        accAlarmTransactionItem
            .setAreaNameIn(accTransactionService.getAreaNamesBySessionId(request.getSession().getId()));
        accAlarmTransactionItem
            .setDeptCodeIn(accTransactionService.getDeptCodesBySessionId(request.getSession().getId()));
        List<AccAlarmTransactionItem> intemList = (List<AccAlarmTransactionItem>)accTransactionService
            .getItemData(AccAlarmTransactionItem.class, accAlarmTransactionItem, getBeginIndex(), getEndIndex());
        excelExport(intemList, AccAlarmTransactionItem.class);
    }
}
