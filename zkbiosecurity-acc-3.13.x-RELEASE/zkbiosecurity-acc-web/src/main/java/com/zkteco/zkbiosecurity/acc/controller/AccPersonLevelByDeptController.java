package com.zkteco.zkbiosecurity.acc.controller;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.remote.AccPersonLevelByDeptRemote;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonLevelByDeptService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeptLevelItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeptSelectLevelItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelByDeptItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2018/3/14 10:54
 */
@Controller
public class AccPersonLevelByDeptController extends BaseController implements AccPersonLevelByDeptRemote {

    private Logger logger = LoggerFactory.getLogger(AccPersonLevelByDeptController.class);

    @Autowired
    private AccPersonLevelByDeptService accPersonLevelByDeptService;
    @Autowired
    private AccPersonService accPersonService;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private ProgressCache progressCache;

    @RequiresPermissions("acc:personLevelByDept")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/personLevelByDept/accPersonLevelByDept");
    }

    @RequiresPermissions("acc:personLevelByDept:refresh")
    @Override
    public DxGrid list(AccPersonLevelByDeptItem codition) {
        Pager pager = accPersonLevelByDeptService.getPagerFilterAuth(request.getSession().getId(), codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:personLevelByDept:refresh")
    @Override
    public DxGrid getDeptLevel(AccDeptLevelItem codition) {
        String authAreaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId());
        if (StringUtils.isNotBlank(authAreaIds)){
            codition.setAuthAreaIdIn(authAreaIds);
        }
        Pager pager = accLevelService.getDeptLevel(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:personLevelByDept:addLevel")
    @Override
    public DxGrid getDeptSelectLevel(AccDeptSelectLevelItem codition) {
        String deptId = codition.getDeptId();
        if (StringUtils.isBlank(codition.getSelectId())){
            codition.setSelectId("-1");
        }
        List<String> selectLevelIds = new ArrayList<>(Arrays.asList(codition.getSelectId().split(",")));  //获取选中权限组的ID
        if (codition.getType().equals("noSelected")){
            codition.setNotInId(StringUtils.join(selectLevelIds,","));
        }
        else if (codition.getType().equals("selected")){
            codition.setInId(StringUtils.join(selectLevelIds,","));
        }
        String authAreaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId()); //获取当前登录用户所属区域
        if (StringUtils.isNotBlank(authAreaIds)){
            codition.setAuthAreaIdIn(authAreaIds);
        }
        codition.setFilterId(deptId);
        Pager pager = accLevelService.getItemsByPage(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:personLevelByDept:addLevel")
    @LogRequest(module = "acc_module", object = "common_leftMenu_levelSetByDept", opType = "common_level_addPersonLevel", requestParams = {"deptCode", "levelNames"}, opContent = "common_level_name")
    @Override
    public ZKResultMsg addLevel(String deptId, String levelIds, String immeUpdate) {
        if (StringUtils.isNotBlank(deptId) && StringUtils.isNotBlank(levelIds)) {
            try {
                accPersonLevelByDeptService.setDeptLinkLevel(deptId, levelIds);
                if ("true".equals(immeUpdate)) {
                    String personIds = accPersonService.getPersonIdsByDeptIds(deptId);
                    if (StringUtils.isNotBlank(personIds)) {
                        String clientId = request.getParameter("clientId");
                        progressCache.beginProcess(I18nUtil.i18nCode("common_op_startProcessing") + "<br/>");
                        //避免personIds过大导致biz业务处理产生事务超时问题   modified by max 20160518
                        List<String> personArrayIds = accPersonService.splitPersonIds(personIds, AccConstants.LEVEL_SPLIT_COUNT);
                        int[] currentCount = {0};
                        int[] totalCount = {personArrayIds.size()};
                        personArrayIds.forEach(personArrayId -> {
                            accLevelService.addPersonLevel(levelIds, personArrayId);
                            currentCount[0] = currentCount[0] + 1;
                            progressCache.setProcess(new ProcessBean(0, (int) ((currentCount[0]) * 100.0 / totalCount[0])), clientId);// 单进度条，只需要设置总进度，当前进度传0即可
                        });
                    }
                    progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish"));
                }
            } catch (Exception e) {
                logger.error("addPerson with level error", e);
            }
        }
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:personLevelByDept:delLevel")
    @LogRequest(module="acc_module",object="common_leftMenu_levelSetByDept",opType="common_level_delPersonLevel",requestParams= {"deptCode","levelName"},opContent="common_level_name")
    @Override
    public ZKResultMsg delLevel(String leftIds, String rightIds, String immeUpdate) {
        if(StringUtils.isNotBlank(leftIds) && StringUtils.isNotBlank(rightIds))
        {
            accPersonLevelByDeptService.delLevel(leftIds, rightIds);
            if ("true".equals(immeUpdate)) {
                try {
                    progressCache.beginProcess(I18nUtil.i18nCode("common_op_startProcessing") + "<br/>");
                    List<String> levelIdList = new ArrayList<>(Arrays.asList(rightIds.split(",")));
                    String personIds = accPersonService.getPersonIdsByDeptIds(leftIds);
                    //对人员ids进行分批处理
                    List<String> personArrayIds = accPersonService.splitPersonIds(personIds, AccConstants.LEVEL_SPLIT_COUNT);
                    for (int i = 0,total = personArrayIds.size(); i < total; i++) {
                        //立即删除部门下人员权限
                        accLevelService.immeDelPersonLevel(levelIdList, personArrayIds.get(i));
                        //置null，释放点内存
                        personArrayIds.set(i, null);
                        //休眠一会
                        Thread.sleep(1000);
                        // 单进度条，只需要设置总进度，当前进度传0即可
                        progressCache.setProcess(new ProcessBean(0, (int) ((i + 1) * 1.0 / total * 100)));
                    }
                    progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish"));
                }  catch (Exception e) {
                    logger.error("deletePerson with level error", e);
                }
            } else {
                progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish"));
            }
        }
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public ZKResultMsg getLevelListByDept(@RequestParam(value = "deptId") String deptId) {
        AccDeptLevelItem condition = new AccDeptLevelItem();
        String authAreaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId());
        if (StringUtils.isNotBlank(authAreaIds)) {
            condition.setAuthAreaIdIn(authAreaIds);
        }
        condition.setDeptId(deptId);
        List<AccDeptLevelItem> accLevelDeptList = accPersonLevelByDeptService.getByCondition(condition);
        Map<String, String> accLevelDeptMap = new HashMap<>();
        accLevelDeptList.stream().forEach(accLevelDept -> {
            if (StringUtils.isNotBlank(accLevelDept.getId())) {
                accLevelDeptMap.put(accLevelDept.getId(), accLevelDept.getName());
            }
        });
        return I18nUtil.i18nMsg(new ZKResultMsg(accLevelDeptMap));
    }
}
