/*
 * @author:	GenerationTools
 * @date:	2018-03-14 下午02:44
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import com.zkteco.zkbiosecurity.acc.remote.AccDeviceMonitorRemote;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceMonitorService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceMonitorItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 设备监控
 * @author:	GenerationTools
 * @date:	2018-03-14 下午02:44
 */
@Controller
public class AccDeviceMonitorController extends ExportController implements AccDeviceMonitorRemote {

	@Autowired
	private AccDeviceService accDeviceService;
	@Autowired
	private AccDeviceMonitorService accDeviceMonitorService;

	/* (非 Javadoc)
	 * index
	 * <p>对override方法的补充说明，若不需要则删除此行</p>
	 * 
	 * @return
	 * @see com.zkteco.zkbiosecurity.acc.remote.AccDeviceMonitorRemote#index()
	 */
	@Override
	@RequiresPermissions({"acc:deviceMonitor", "acc:deviceMonitor:browse"})
	public ModelAndView index() {
		return new ModelAndView("acc/deviceMonitor/accDeviceMonitor");
	}

	/* (非 Javadoc)
	 * clearCmdCache
	 * <p>对override方法的补充说明，若不需要则删除此行</p>
	 * 
	 * @param id
	 * @return
	 * @see com.zkteco.zkbiosecurity.acc.remote.AccDeviceMonitorRemote#clearCmdCache(java.lang.String)
	 */
	@Override
	@LogRequest(module="acc_module",object="common_leftMenu_devMonitioring",opType="acc_dev_clearAllCmdCache", requestParams = "alias", opContent="common_dev_name")
	@RequiresPermissions("acc:deviceMonitor:clearCmdCache")
	public ZKResultMsg clearCmdCache(String sn, String alias) {
		accDeviceService.clearCmdCache(sn,"-1112");
		ZKResultMsg resultMsg = new ZKResultMsg();
		resultMsg.setMsg("common_devMonitor_clearCmdSuccess");//清除缓存命令成功！请及时手动同步数据到设备，以确保系统中和设备中权限一致！
		return resultMsg;
	}



	/* (非 Javadoc)
	 * export
	 * <p>对override方法的补充说明，若不需要则删除此行</p>
	 * 
	 * @param request
	 * @param response
	 * @see com.zkteco.zkbiosecurity.acc.remote.AccDeviceMonitorRemote#export(javax.servlet.http.HttpServletRequest, javax.servlet.http.HttpServletResponse)
	 */
	@Override
	@LogRequest(module="acc_module", object="common_leftMenu_devMonitioring", opType="common_op_export", opContent="common_op_export")
	@RequiresPermissions("acc:deviceMonitor:export")
	public void export(HttpServletRequest request, HttpServletResponse response) {
        String exportType = request.getParameter("exportType");
        String recordStart = request.getParameter("recordstart");
        String recordCount = request.getParameter("recordcount");
        String queryConditions = request.getParameter("queryConditions");
		String[] conditionAry = queryConditions.replaceAll("%20"," ").split("&");
		String devStatus = "";
		for (String query : conditionAry) {
			if (query.startsWith("devStatus")) {
				devStatus = query.split("=", 2)[1];
			}
		}
		int beginIndex = 0;
        int endIndex = 30000;
        int exportCount = 30000;
        if(exportType.equals("3") && StringUtils.isNotBlank(recordStart) && StringUtils.isNotBlank(recordCount)) {
			beginIndex = Integer.parseInt(recordStart) - 1;
			exportCount = Integer.parseInt(recordCount) > 30000 ? 30000 : Integer.parseInt(recordCount);//导出多少条记录
        }

        AccDeviceItem accDeviceItem = new AccDeviceItem();
        setConditionValue(accDeviceItem);
		//根据当前登录用户设置需要过滤的区域，由于设备监控暂时无法根据权限过滤屏蔽
        String areaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId());
        if (StringUtils.isNotBlank(areaIds)) {
        	accDeviceItem.setAreaIdIn(areaIds);
		}
		List<AccDeviceMonitorItem> accDeviceMonitorItemList = accDeviceMonitorService.filterDeviceState(accDeviceItem, beginIndex, exportCount, devStatus);
        if (!accDeviceMonitorItemList.isEmpty()) {
        	accDeviceMonitorItemList.stream().forEach(accDeviceMonitorItem -> {
        		if (StringUtils.isBlank(accDeviceMonitorItem.getLastError())) {
					accDeviceMonitorItem.setLastError(I18nUtil.i18nCode("common_none"));
				}
			});
		}
		excelExport(accDeviceMonitorItemList, AccDeviceMonitorItem.class);
	}
}