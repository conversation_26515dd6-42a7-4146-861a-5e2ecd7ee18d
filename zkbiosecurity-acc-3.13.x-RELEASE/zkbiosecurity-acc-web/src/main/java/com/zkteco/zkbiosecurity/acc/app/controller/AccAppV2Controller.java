package com.zkteco.zkbiosecurity.acc.app.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.app.vo.AccAppDoorItem;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.AccAppService;
import com.zkteco.zkbiosecurity.acc.utils.AccEnumUtil;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccLevelItem;
import com.zkteco.zkbiosecurity.base.vo.AppResultMessage;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;

@RestController
@RequestMapping(value = "/app/v2/acc")
public class AccAppV2Controller {

    @Autowired
    private AccAppService accAppService;

    @Autowired
    protected HttpServletRequest request;

    /**
     * 获取门禁报表记录
     * 
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2023-09-07 14:04
     * @since 1.0.0
     */
    @RequestMapping(value = "/getReportByFilters", method = RequestMethod.POST)
    public AppResultMessage getReportByFilters(@RequestBody JSONObject data) {
        // 人员自助登录 userName 为pin
        String userName = request.getParameter("user_name");
        String userId = request.getParameter("user_id");
        userId = userId.split("_")[0];
        // 页码
        int pageNo = data.containsKey("pageNo") ? data.getIntValue("pageNo") : 1;
        // 每页记录数
        int pageSize = data.containsKey("pageSize") ? data.getIntValue("pageSize") : 20;
        // 搜索框数据
        String filters = data.getString("filters");
        // 开始时间
        Date startTime = data.containsKey("startTime") ? data.getDate("startTime") : null;
        // 结束时间
        Date endTime = data.containsKey("endTime") ? data.getDate("endTime") : null;
        String eventNo = data.getString("eventNo");
        // 门ID
        String doorId = data.getString("doorId");
        String loginType = data.getString("loginType");
        return accAppService.getReportByFilters(doorId, filters, startTime, endTime, pageNo, pageSize, userName, userId,
            loginType, eventNo);
    }

    /**
     * 门禁就详情
     * 
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2023-09-07 14:04
     * @since 1.0.0
     */
    @RequestMapping(value = "/getReportDetail", method = RequestMethod.POST)
    public AppResultMessage getReportDetail(@RequestBody JSONObject data) {
        String id = data.getString("id");
        return accAppService.getReportDetail(id);
    }

    /**
     * 设备在线离线统计
     * 
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2023-09-07 14:05
     * @since 1.0.0
     */
    @RequestMapping(value = "/getAccDeviceCount", method = RequestMethod.POST)
    public AppResultMessage getAccDeviceCount(@RequestBody JSONObject data) {
        String areaIds = data.getString("areaIds");
        return accAppService.getAccDeviceCount(areaIds);
    }

    /**
     * 查询设备
     * 
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2023-09-07 14:03
     * @since 1.0.0
     */
    @RequestMapping(value = "/getAccDevices", method = RequestMethod.POST)
    public AppResultMessage getAccDevices(@RequestBody JSONObject data) {
        String areaIds = data.getString("areaIds");
        String state = data.getString("state");
        // 页码
        int pageNo = data.containsKey("pageNo") ? data.getIntValue("pageNo") : 1;
        // 每页记录数
        int pageSize = data.containsKey("pageSize") ? data.getIntValue("pageSize") : 20;
        AccDeviceItem condition = new AccDeviceItem();
        condition.setAreaIdIn(areaIds);
        if (StringUtils.isNotBlank(state)) {
            condition.setConnectState(state);
        }

        String userName = request.getParameter("user_name");

        return accAppService.getAccDevicesPager(condition, pageNo - 1, pageSize, userName);
    }

    /**
     * 管理员获取门禁权限组
     * 
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2023-08-31 10:27
     * @since 1.0.0
     */
    @RequestMapping(value = "/getAccLevels", method = RequestMethod.POST)
    public AppResultMessage getAccLevels(@RequestBody JSONObject data) {
        String userName = request.getParameter("user_name");
        String name = data.containsKey("name") ? data.getString("name") : "";
        // 页码
        int pageNo = data.containsKey("pageNo") ? data.getIntValue("pageNo") : 1;
        // 每页记录数
        int pageSize = data.containsKey("pageSize") ? data.getIntValue("pageSize") : 20;
        AccLevelItem codition = new AccLevelItem();
        String areaIds = data.containsKey("areaIds") ? data.getString("areaIds") : "";
        codition.setName(name);
        codition.setAreaIds(areaIds);
        return accAppService.loadPagerByAuthFilter(userName, codition, pageNo - 1, pageSize);
    }

    /**
     * 根据权限组进行门操作
     * 
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2023-08-31 10:47
     * @since 1.0.0
     */
    @RequestMapping(value = "/operateDoorByLevel", method = RequestMethod.POST)
    public AppResultMessage operateDoor(@RequestBody JSONObject data) {
        String levelIds = data.getString("levelIds");
        String type = data.getString("type");
        String interval = data.getString("interval");
        String userName = request.getParameter("user_name");
        return accAppService.operateDoorByLevel(userName, type, interval, levelIds);
    }

    /**
     * 获取管理员有权限的门列表是否有锁定状态的记录
     * 
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2023-09-06 17:28
     * @since 1.0.0
     */
    @RequestMapping(value = "/getAllDoor", method = RequestMethod.POST)
    public AppResultMessage getAllDoor(@RequestBody JSONObject data) {
        String token = data.getString("token");
        String doorName = data.getString("doorName");
        Short doorStatus = data.getShort("doorStatus");
        String loginType = data.getString("loginType");
        AppResultMessage appResultMessage = accAppService.getDoorByDoorName(token, doorName, 1, 2000, loginType);
        // AppResultMessage appResultMessage = accAppService.getDoorByDoorName(token, doorName, pageNo, pageSize);
        List<AccAppDoorItem> accAppDoorItemList = new ArrayList<>();
        JSONObject dataJson = new JSONObject();
        if (appResultMessage.getData() != null) {
            JSONObject doorJson = (JSONObject)appResultMessage.getData();
            JSONArray doorArray = doorJson.getJSONArray("rows");
            if (doorArray.size() > 0) {
                for (int i = 0; doorArray.size() > i; i++) {
                    JSONObject door = doorArray.getJSONObject(i);
                    AccAppDoorItem appDoor = new AccAppDoorItem();
                    appDoor.setId(door.getString("id"));
                    appDoor.setDoorName(door.getString("name"));
                    appDoor.setDevName(door.getString("devName"));
                    appDoor.setTimeZone(door.getString("timeZone"));
                    appDoor.setAreaName(door.containsKey("areaName") ? door.getString("areaName") : null);
                    appDoor.setRelay(door.getString("relay"));
                    appDoor.setSensor(door.getString("sensor"));
                    appDoor.setDoorStatus(door.getShortValue("connect"));
                    if (AccConstants.DEV_STATE_ONLINE == door.getShortValue("connect")) {
                        // 设备在线，判断门报警状态
                        if (0 == AccEnumUtil.Alarm.getAlarmType(door.getIntValue("alarmLevel"),
                            door.getIntValue("alarm"))) {
                            // 0:无报警
                            appDoor.setDoorAlarmStatus(0);
                        } else {
                            // 1：报警
                            appDoor.setAlarm(AccEnumUtil.Alarm.getAlarmType(door.getIntValue("alarmLevel"),
                                door.getIntValue("alarm")));
                            appDoor.setDoorAlarmStatus(1);
                        }

                        if (ConstUtil.DEV_STATE_LOCK == door.getIntValue("doorValue")) {
                            appDoor.setDoorStatus(ConstUtil.DEV_STATE_LOCK);
                        }
                    } else if (AccConstants.DEV_STATE_DISABLE == door.getShortValue("connect")) {
                        // 禁用
                        appDoor.setDoorAlarmStatus(2);
                    } else if (AccConstants.DEV_STATE_OFFLINE == door.getShortValue("connect")) {
                        // 离线
                        appDoor.setDoorAlarmStatus(3);
                    }
                    if (null == doorStatus) {
                        accAppDoorItemList.add(appDoor);
                    } else if (doorStatus.equals(appDoor.getDoorStatus())) {
                        accAppDoorItemList.add(appDoor);
                    }
                }
            }
        }

        dataJson.put("totalCount", accAppDoorItemList.size());
        dataJson.put("rows", accAppDoorItemList);
        appResultMessage.setData(dataJson);
        return appResultMessage;
    }

    /**
     * 获取门禁权限组列表
     * 
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2025-04-17 10:59
     * @since 1.0.0
     */
    @RequestMapping(value = "/getAccLevelsByCodition", method = RequestMethod.POST)
    public AppResultMessage getAccLevelsByCodition(@RequestBody JSONObject data) {
        String userName = request.getParameter("user_name");
        String name = data.containsKey("name") ? data.getString("name") : "";
        // 页码
        int pageNo = data.containsKey("pageNo") ? data.getIntValue("pageNo") : 1;
        // 每页记录数
        int pageSize = data.containsKey("pageSize") ? data.getIntValue("pageSize") : 20;
        String loginType = data.getString("loginType");
        AccLevelItem codition = new AccLevelItem();
        codition.setName(name);
        return accAppService.getAccLevelsByCodition(loginType, userName, codition, pageNo - 1, pageSize);
    }

    /**
     * 人员添加权限组
     * 
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2025-04-17 11:07
     * @since 1.0.0
     */
    @RequestMapping(value = "/addLevelPerson", method = RequestMethod.POST)
    public AppResultMessage addLevelPerson(@RequestBody JSONObject data) {
        String pin = data.getString("pin");
        String levelIds = data.getString("levelIds");
        String lang = data.getString("lang");
        return accAppService.addLevelPerson(lang, pin, levelIds);
    }
}
