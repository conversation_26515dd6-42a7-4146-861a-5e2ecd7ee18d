package com.zkteco.zkbiosecurity.acc.dhx;

import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.vo.AccAlarmMonitorItem;
import com.zkteco.zkbiosecurity.core.utils.ShowGridColumn;

/**
 * 是否显示确认报警操作按钮
 *
 * <AUTHOR>
 * @date 2022/7/18 16:49
 */
@Component
public class AccShowAcknowledgedAlarm implements ShowGridColumn {
    @Override
    public boolean isShow(Object obj) {
        AccAlarmMonitorItem item = (AccAlarmMonitorItem)obj;
        if (AccConstants.DEV_ALARMEVENTPRIORITY_HIGH.equals(item.getStatus())) {
            return false;
        }
        return true;
    }
}
