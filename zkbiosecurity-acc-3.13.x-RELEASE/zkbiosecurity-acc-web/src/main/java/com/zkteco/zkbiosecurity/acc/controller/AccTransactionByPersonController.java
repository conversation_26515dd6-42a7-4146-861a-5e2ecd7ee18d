package com.zkteco.zkbiosecurity.acc.controller;

import com.zkteco.zkbiosecurity.acc.remote.AccTransactionByPersonRemote;
import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonService;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionPersonDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionPersonItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 对应百傲瑞达 AccTransactionByPersonController
 * @author:	yibiao.shen
 * @date:	2018-03-09  16:32:41
 */
@Controller
public class AccTransactionByPersonController extends ExportController implements AccTransactionByPersonRemote {
	@Autowired
	private AccPersonService accPersonService;
	@Autowired
	private AccDoorService accDoorService;


	@RequiresPermissions("acc:transactionByPerson")
	@Override
	public ModelAndView index() {
		return new ModelAndView("acc/transactionByPerson/accTransactionByPerson");
	}

	@RequiresPermissions("acc:transactionByPerson:refresh")
	@Override
	public DxGrid list(AccTransactionPersonItem condition) {
		Pager pager = accPersonService.getAccTransactionPerson(request.getSession().getId(), condition, getPageNo(), getPageSize());
		return GridUtil.convert(pager, condition.getClass());
	}

	@RequiresPermissions("acc:transactionByPerson:refresh")
	@Override
	public DxGrid doorList(AccTransactionPersonDoorItem condition) {
		Pager pager = accDoorService.getItemsByPage(condition, getPageNo(), getPageSize());
		return GridUtil.convert(pager, condition.getClass());
	}

	@RequiresPermissions("acc:transactionByPerson:export")
	@LogRequest(module="acc_module", object="common_leftMenu_searchByPerson", opType="common_op_export", opContent="common_op_export")
	@Override
	public void export(HttpServletRequest request, HttpServletResponse response) {
		String personId = request.getParameter("personId");
		AccTransactionPersonDoorItem accTransactionPersonDoorItem = new AccTransactionPersonDoorItem();
		if(StringUtils.isNotBlank(personId)) {
			accTransactionPersonDoorItem.setPersonId(personId);
		}
		List<AccTransactionPersonDoorItem> itemList = accDoorService.getPersonDoorItemData(AccTransactionPersonDoorItem.class, accTransactionPersonDoorItem, getBeginIndex(), getEndIndex());
		excelExport(itemList,AccTransactionPersonDoorItem.class);
	}

}
