package com.zkteco.zkbiosecurity.acc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.remote.AccMapRemote;
import com.zkteco.zkbiosecurity.acc.service.AccMapService;
import com.zkteco.zkbiosecurity.acc.service.AccParamService;
import com.zkteco.zkbiosecurity.acc.service.AccRTMonitorService;
import com.zkteco.zkbiosecurity.acc.utils.AccEnumUtil;
import com.zkteco.zkbiosecurity.acc.vo.AccMapItem;
import com.zkteco.zkbiosecurity.acc.vo.AccMapPosItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.format.TreeBuilder;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 电子地图controller
 * 
 * @date: 2018-03-20 下午02:07
 */
@Controller
public class AccMapController extends BaseController implements AccMapRemote {
    @Autowired
    private AccMapService accMapService;
    @Autowired
    private AccParamService accParamService;
    @Autowired
    private AccRTMonitorService accRTMonitorService;
    @Value("${system.filePath:BioSecurityFile}")
    private String systemFilePath;
    private final static long MAX_FILE_SIZE = 1024 * 1024 * 5;

    @RequiresPermissions("acc:map")
    @Override
    public ModelAndView index() {
        // 视频插件加载版本,解决ocx取不到值
        String loadPlugins = accParamService.getVidParams().get("vid.loadPlugins");
        request.setAttribute("loadPlugins", loadPlugins);
        String pinEncryptMode = accParamService.getParamValByName("pers.pin.encryptMode");
        String nameEncryptMode = accParamService.getParamValByName("pers.name.encryptMode");
        String cardEncryptMode = accParamService.getParamValByName("pers.cardNo.encryptMode");
        request.setAttribute("pinEncryptMode", pinEncryptMode);
        request.setAttribute("nameEncryptMode", nameEncryptMode);
        request.setAttribute("cardEncryptMode", cardEncryptMode);
        return new ModelAndView("acc/map/accMap");
    }

    @RequiresPermissions({"acc:map:add", "acc:map:edit"})
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("item", accMapService.getItemById(id));
            request.setAttribute("editPage", true);
        }
        return new ModelAndView("acc/map/editAccMap");
    }

    @RequiresPermissions({"acc:map:add", "acc:map:edit"})
    @LogRequest(module = "acc_module", object = "acc_leftMenu_electronicMap", opType = "common_op_edit",
        requestParams = {"name"}, opContent = "base_map_name")
    @Override
    public ZKResultMsg save(@RequestParam(value = "file", required = false) MultipartFile file, AccMapItem item) {
        ZKResultMsg res = new ZKResultMsg();
        String modify = request.getParameter("modify_path");// 是否修改路径标志
        String path = systemFilePath + AccConstants.MAP_PATH;// 地图图片文件夹路径
        // 保存图片
        if (StringUtils.isBlank(item.getId()) || (AccConstants.ENABLE + "").equals(modify)) {
            if (file == null || file.getSize() > MAX_FILE_SIZE) {
                throw ZKBusinessException.warnException(I18nUtil.i18nCode("acc_map_imgSizeError", 5));
            }
            long fName = System.currentTimeMillis();
            // 修改保存mapPath为相对路径，修复安装路径与先前不一致，还原备份的数据库后显示异常
            item.setMapPath(AccConstants.MAP_PATH + "/" + fName + ".jpg");
            File upLoadPicPath = new File(path);
            if (!upLoadPicPath.isAbsolute()) { // 判断是否是绝对路径
                path = ClassUtil.getRootPath() + "/" + path;
                upLoadPicPath = new File(path);
            }
            if (!upLoadPicPath.exists()) {// 如果文件路径不存在，创建新的路径
                upLoadPicPath.mkdirs();
            }
            if (StringUtils.isNoneBlank(item.getId())) {
                AccMapItem oldMap = accMapService.getItemById(item.getId());
                File oldPic = new File(oldMap.getMapPath());
                if (!oldPic.isAbsolute()) {
                    oldPic = new File(FileUtils.getLocalFullPath(oldMap.getMapPath()));
                }
                if (oldPic.exists()) {
                    oldPic.delete();// 将原有图片删除
                }
            }
            try {
                String mapName = path + "/" + fName + ".jpg";
                File mapFile = new File(mapName);
                file.transferTo(mapFile);
            } catch (IOException e) {
                throw new ZKBusinessException("AccMapItem SaveMap Exception");
            }

        }
        accMapService.saveItem(item);
        res.setData(item.getId());
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("acc:map:refresh")
    @Override
    public DxGrid list(AccMapItem codition) {
        Pager pager = accMapService.getItemsByPage(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:map:del")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_electronicMap", opType = "common_op_del",
        requestParams = {"name"}, opContent = "base_map_name")
    @Override
    public ZKResultMsg del(String ids) {
        accMapService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public TreeItem getMapTree() {
        List<TreeItem> items = accMapService.createMapTree(request.getSession().getId());
        if (items == null || items.isEmpty()) {
            TreeItem infoItem = new TreeItem();
            infoItem.setId("_");
            infoItem.setText("<span class='warningColor'>" + I18nUtil.i18nCode("base_map_addMap") + "</span>");
            infoItem.setIm0("comm_iconAlert.png");
            items.add(infoItem);
        }

        List<TreeItem> treeItems = TreeBuilder.newTreeBuilder(TreeItem.class, String.class).buildToTreeList(items);
        return new TreeItem("0", treeItems);
    }

    @Override
    public ModelAndView getMap(String id) {
        if (StringUtils.isNotBlank(id)) {
            AccMapItem tempMapItem = accMapService.getItemById(id);
            // 根据图片路径，把图片转成base64格式的字符串
            String imgBase64Str = "";
            String mapPath = tempMapItem.getMapPath();
            File mapfile = new File(mapPath);
            if (!mapfile.isAbsolute()) {
                mapfile = new File(FileUtils.getLocalFullPath(mapPath));
            }
            if (mapfile.exists()) {
                try {
                    InputStream in = new FileInputStream(mapfile);
                    byte[] data = new byte[in.available()];// 读取图片字节数组
                    in.read(data);
                    imgBase64Str = Base64Utils.encodeToString(data);
                    tempMapItem.setMapPath("data:image/jpg;base64," + imgBase64Str);
                    in.close();

                } catch (IOException e) {
                    throw new ZKBusinessException("AccMapController getMap base64 error");
                }
            }

            List<AccMapPosItem> mapPosList = accMapService.getMapPosList(id);
            request.setAttribute("tempMap", tempMapItem);
            request.setAttribute("mapPosList", mapPosList);
            request.setAttribute("mapId", id);
        }

        String personPhotoMaxHeight = accParamService.getParamValByName("acc.personPhotoMaxHeight");
        String accIsShowPhoto = accParamService.getParamValByName("acc.isShowPhoto");
        String accIsShowSound = accParamService.getParamValByName("acc.isShowSound");
        String loadPlugins = accParamService.getVidParams().get("vid.loadPlugins");
        request.setAttribute("personPhotoMaxHeight", personPhotoMaxHeight);
        request.setAttribute("accIsShowPhoto", accIsShowPhoto);
        request.setAttribute("accIsShowSound", accIsShowSound);
        request.setAttribute("loadPlugins", loadPlugins);
        request.setAttribute("gridName", request.getParameter("gridName"));
        return new ModelAndView("acc/map/accMapRTMonitor");
    }

    @RequiresPermissions("acc:map:saveMapPos")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_electronicMap", opType = "common_op_savePositon",
        requestParams = {"name"}, opContent = "base_map_name")
    @Override
    public ZKResultMsg saveMapPos(String mapId, Double mapWidth, Double mapHeight, String posArray) {
        accMapService.saveMapPos(mapId, mapWidth, mapHeight, posArray);
        if ("addEntityToMap".equals(request.getParameter("flag")))// 如果是添加门或摄像头，则前台不显示操作成功。
        {
            return null;
        }
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:map:addDoorToMap")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_electronicMap", opType = "acc_map_addDoor",
        requestParams = {"name"}, opContent = "common_name")
    @Override
    public ZKResultMsg addDoor(String mapId, Double width, String entityType, String entityIds, String logMethod) {
        accMapService.addEntity(mapId, width, entityType, entityIds, logMethod);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:map:addChannelToMap")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_electronicMap", opType = "acc_map_addChannel",
        requestParams = {"name"}, opContent = "common_name")
    @Override
    public ZKResultMsg addChannel(String mapId, Double width, String entityType, String entityIds, String logMethod) {
        accMapService.addEntity(mapId, width, entityType, entityIds, logMethod);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public boolean isExist(AccMapItem item) {
        return accMapService.isExist(item.getName());
    }

    @RequiresPermissions({"acc:map:addDoorToMap", "acc:map:addChannelToMap"})
    @Override
    public ModelAndView getEntitySelectItem(String mapId, String entityType, String width) {
        request.setAttribute("mapId", mapId);
        request.setAttribute("entityType", entityType);
        request.setAttribute("width", width);
        // 添加门
        if (entityType.equals("AccDoor")) {
            request.setAttribute("logMethod", "addDoorToMap");
            return new ModelAndView("acc/map/accMapSelectDoor");
        }
        // 添加视频
        else {
            request.setAttribute("logMethod", "addChannelToMap");
            return new ModelAndView("acc/map/accMapSelectChannel");
        }
    }

    @Override
    public ZKResultMsg isExistVid() {
        ZKResultMsg res = new ZKResultMsg();
        if (!accMapService.isExistVidDevice()) {
            res.setRet("false");
            res.setMsg("common_vid_noDev");
        }
        return I18nUtil.i18nMsg(res);
    }

    @MessageMapping("/accMapMonitor/getDoorState")
    @SendTo("/topic/accMapMonitor/getDoorState")
    public Object getDoorState(String params) {
        JSONObject paramJson = JSONObject.parseObject(params);
        JSONObject resultJson = new JSONObject();
        JSONArray doorStates = new JSONArray();
        JSONArray doorStateArray =
            (JSONArray)accRTMonitorService.getDoorState(paramJson.getString("doorIds")).getData();
        if (Objects.nonNull(doorStateArray) && doorStateArray.size() > 0) {
            for (int i = 0; i < doorStateArray.size(); i++) {
                JSONObject door = doorStateArray.getJSONObject(i);
                JSONObject data = new JSONObject();
                int sensor = door.getIntValue("sensor");
                int relay = door.getIntValue("relay");
                int alarm = door.getIntValue("alarm");
                int connect = door.getIntValue("connect");
                short alarmLevel = door.getShortValue("alarmLevel");
                int doorState = door.getIntValue("doorState");
                boolean isNewAccess = door.getBooleanValue("isNewAccess");
                String alarmValue = AccEnumUtil.Alarm.getValue(doorState, alarmLevel, alarm);
                data.put("id", door.getString("id"));
                data.put("areaId", door.getString("areaId"));
                data.put("devAlias", door.getString("devAlias"));
                data.put("devSn", door.getString("devSn"));
                data.put("no", door.getString("no"));
                data.put("name", door.getString("name"));
                data.put("connect", connect);
                data.put("sensor", AccEnumUtil.Sensor.getValue(doorState, sensor));
                data.put("relay", AccEnumUtil.Relay.getValue(doorState, relay));
                data.put("alarm", alarmValue);
                if (isNewAccess) {
                    alarmLevel = 0;
                }
                data.put("image", AccEnumUtil.DoorImage.getValue(doorState, connect, alarmLevel, alarm, relay, sensor));
                data.put("lockDisplay", door.getString("lockDisplay"));
                data.put("opDisplay", doorState == ConstUtil.DEV_STATE_ONLINE || doorState == ConstUtil.DEV_STATE_LOCK
                    ? "inline" : "none");
                data.put("iconFolderName", door.getString("iconFolderName"));
                // 设备离线、报警事件默认报警声提示
                if (connect == ConstUtil.DEV_STATE_OFFLINE) {
                    data.put("audio", "/public/media/sound/alarm.wav");
                } else if (alarm > 0) {
                    if (!door.getBoolean("isDisableAudio")) {
                        data.put("audio", "/public/media/sound/alarm.wav");
                    }
                }
                doorStates.add(data);
            }
            resultJson.put("doorStates", doorStates);
        }
        resultJson.put("clientId", paramJson.getString("clientId"));
        return resultJson;
    }

    @MessageMapping("/accMapMonitor/getEventData")
    @SendTo("/topic/accMapMonitor/getEventData")
    public JSONObject getEventData() {
        return null;
    }

    @Override
    public ModelAndView getVidPreview() {
        String channelId = request.getParameter("channelId");
        request.setAttribute("channelId", channelId);
        String vidDevice = request.getParameter("vidDevice");
        request.setAttribute("vidDevice", vidDevice);
        ModelAndView modelAndView = new ModelAndView("vid/preview/opVidPreview");
        if (accMapService.isExistVid()) {
            String loadPlugins = accParamService.getVidParams().get("vid.loadPlugins");
            request.setAttribute("loadPlugins", loadPlugins);
        } else if (accMapService.isExistVms()) {
            Map<String, String> vmsChannelMap = accMapService.getVmsChannelById(channelId);
            if (Objects.nonNull(vmsChannelMap) && !vmsChannelMap.isEmpty()) {
                request.setAttribute("serialNumber", vmsChannelMap.get("serialNumber"));
                request.setAttribute("channelNo", vmsChannelMap.get("channelNo"));
            }
            return new ModelAndView("vms/preview/opVmsPreview");
        }
        return modelAndView;
    }
}