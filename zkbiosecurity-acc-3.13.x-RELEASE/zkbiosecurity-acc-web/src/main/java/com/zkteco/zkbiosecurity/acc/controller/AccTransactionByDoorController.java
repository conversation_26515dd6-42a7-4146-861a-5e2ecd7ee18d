package com.zkteco.zkbiosecurity.acc.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.remote.AccTransactionByDoorRemote;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonService;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionByDoorService;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionDoorPersonItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 以门查询业务Controller
 *
 * <AUTHOR>
 * @DATE 2020-08-07 15:44
 */
@Controller
public class AccTransactionByDoorController extends ExportController implements AccTransactionByDoorRemote {
    @Autowired
    private AccTransactionByDoorService accTransactionByDoorService;
    @Autowired
    private AccPersonService accPersonService;
    @Autowired
    private AccLevelService accLevelService;

    @RequiresPermissions("acc:transactionByDoor")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/transactionByDoor/accTransactionByDoor");
    }

    @RequiresPermissions("acc:transactionByDoor:refresh")
    @Override
    public DxGrid list(AccTransactionDoorItem condition) {
        Pager pager = accTransactionByDoorService.loadPagerByAuthFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("acc:transactionByDoor:refresh")
    @Override
    public DxGrid personList(AccTransactionDoorPersonItem condition) {
        Pager pager = accPersonService.getDoorPersonByAuthFilter(request.getSession().getId(), condition, getPageNo(),
            getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("acc:transactionByDoor:export")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_searchByDoor", opType = "common_op_export",
        opContent = "common_op_export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        String doorId = request.getParameter("doorId");
        AccTransactionDoorPersonItem accTransactionDoorPersonItem = new AccTransactionDoorPersonItem();
        if (StringUtils.isNotBlank(doorId)) {
            accTransactionDoorPersonItem.setDoorId(doorId);
        }
        String userId = accLevelService.getUserIdBySessionId(request.getSession().getId());
        if (StringUtils.isNotBlank(userId)) {
            accTransactionDoorPersonItem.setUserId(userId);
        }
        List<AccTransactionDoorPersonItem> itemList =
            accPersonService.getDoorPersonItemData(accTransactionDoorPersonItem, getBeginIndex(), getEndIndex());
        excelExport(itemList, AccTransactionDoorPersonItem.class);
    }
}
