package com.zkteco.zkbiosecurity.acc.api.controller;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.api.vo.AccApiDeviceItem;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 门禁设备接口
 *
 * <AUTHOR>
 * @Date: 2018/11/16 16:55
 */
@Controller
@RequestMapping(value = {"/api/device"})
@Slf4j
@Api(tags = "AccDevice", description = "acc device")
public class AccApiDeviceController {

    @Autowired
    private AccDeviceService accDeviceService;


    /**
     * 根据sn获取门禁设备
     *
     * @param sn
     * @return
     * @auther lambert.li
     * @date 2018/11/21 16:40
     */
    @ApiOperation(value = "Get Acc Device Info By Sn", notes = "Return Acc Device Object", response = ApiResultMessage.class)
    @RequestMapping(value = {"/getAcc"}, method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"sn"})
    public ApiResultMessage getBySn(@RequestParam(name = "sn", required = false) String sn) {
        ApiResultMessage rs = ApiResultMessage.successMessage();

        if (StringUtils.isBlank(sn)) {
            return ApiResultMessage.message(AccConstants.ACC_TRANSACTION_SNNOTNULL, I18nUtil.i18nCode("acc_api_devSnNotNull"));
        }

        AccDeviceItem accDeviceItem = accDeviceService.getItemByDevSn(sn);
        if (accDeviceItem != null) {
            AccApiDeviceItem accApiDeviceItem = AccApiDeviceItem.createAccDevice(accDeviceItem);
            rs.setData(accApiDeviceItem);
            return rs;
        }
        return ApiResultMessage.message(AccConstants.ACC_DEV_NOTEXIST, I18nUtil.i18nCode("acc_api_deviceNumberDoesNotExist"));
    }


    /**
     * 分页获取门禁设备
     *
     * @param pageNo
     * @param pageSize
     * @return
     * @auther lambert.li
     * @date 2018/11/21 16:41
     */
    @ResponseBody
    @RequestMapping(value = {"/accList"}, method = RequestMethod.GET, produces = "application/json")
    @ApiOperation(value = "Get Acc Devices", notes = "Return Acc Device List", response = ApiResultMessage.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNo", value = "Page Index", required = true, dataType = "int", paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "One Page Size", required = true, dataType = "int", paramType = "query")
    })
    @ApiLogRequest(requestParams = {"pageNo","pageSize"})
    public ApiResultMessage list(@RequestParam(name = "pageNo", required = false) Integer pageNo, @RequestParam(name = "pageSize", required = false) Integer pageSize) {
        List<AccApiDeviceItem> accApiDeviceItemList = Lists.newArrayList();
        if (pageNo == null || pageSize == null) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE, I18nUtil.i18nCode("acc_api_parameterValueCannotBeNull"));
        }
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        if (pageSize > 1000) {
            return ApiResultMessage.message(AccConstants.API_PAGE_OVERSIZE, I18nUtil.i18nCode("common_api_pageSizeOver"));
        }
        List<AccDeviceItem> accDeviceItemList = (List<AccDeviceItem>) accDeviceService.getItemsByPage(new AccDeviceItem(), pageNo - 1, pageSize).getData();
        if (!accDeviceItemList.isEmpty()) {
            accDeviceItemList.forEach(accDeviceItem -> accApiDeviceItemList.add(AccApiDeviceItem.createAccDevice(accDeviceItem)));
        }
        return ApiResultMessage.successMessage(accApiDeviceItemList);
    }
}
