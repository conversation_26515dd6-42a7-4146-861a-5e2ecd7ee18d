package com.zkteco.zkbiosecurity.acc.api.controller;

import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.base.annotation.ApiPermissions;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;

/**
  * <AUTHOR>
  * @Date: 2020/01/19 14:12
 */

@Controller
@RequestMapping(value = {"/api/accLevel"})
@Slf4j
public class AccCloudApiLevelController {

    @Autowired
    private AccLevelService accLevelService;

    /**
     * 从二号项目迁移代码：根据区域获取门禁权限组（云调用接口）
     * <AUTHOR>
     * @param zkMessage
     */
    @ResponseBody
    @ApiPermissions(moduleCode = "acc", moduleName = "acc_module")
    @RequestMapping(value = "/getLevelByAuth", method = RequestMethod.POST, produces = "application/json")
    public ZKResultMsg getLevelByAuth(@RequestBody ZKMessage zkMessage)
    {
        ZKResultMsg rs = null;
        try {
            Map<String, Object> dataMap = zkMessage.getContent();
            String areaCodes = (String) dataMap.get("areaCodes");
            int page = (int) dataMap.get("page");
            int pageSize = (int) dataMap.get("pageSize");
            rs = accLevelService.getLevelByAreaCodes(areaCodes, page, pageSize);
        }
        catch(Exception e) {
            log.error("api accLevel/getLevelByAuth error ", e);
            rs = ZKResultMsg.failMsg();
        }
        return I18nUtil.i18nMsg(rs);
    }

    /**
     * 获取人员有权限的门信息（云调用接口）
     *
     * @param zkMessage
     */
    @ResponseBody
    @ApiPermissions(moduleCode = "acc", moduleName = "acc_module")
    @RequestMapping(value = "/getPersonLevelDoor", method = RequestMethod.POST, produces = "application/json")
    public ZKResultMsg getPersonLevelDoor(@RequestBody ZKMessage zkMessage)
    {
        ZKResultMsg rs = null;
        try {
            Map<String, Object> dataMap = zkMessage.getContent();
            String personPin = (String) dataMap.get("personPin");
            boolean isAdmin = MapUtils.getBoolean(dataMap, "isAdmin", false);
            rs = accLevelService.getPersonLevelDoor(personPin, isAdmin);
        }
        catch(Exception e) {
            log.error("api accLevel/getPersonLevelDoor error ", e);
            rs = ZKResultMsg.failMsg();
        }
        return I18nUtil.i18nMsg(rs);
    }

    /**
     * 获取人员有权限的门信息（云调用接口）
     *
     * @param zkMessage
     */
    @ResponseBody
    @ApiPermissions(moduleCode = "acc", moduleName = "acc_module")
    @RequestMapping(value = "/getLevelByPin", method = RequestMethod.POST, produces = "application/json")
    public ZKResultMsg getLevelByPin(@RequestBody ZKMessage zkMessage)
    {
        ZKResultMsg rs = null;
        try {
            Map<String, Object> dataMap = zkMessage.getContent();
            String personPins = (String) dataMap.get("personPins");
            rs = accLevelService.getLevelByPersonPin(personPins);
        }
        catch(Exception e) {
            log.error("api accLevel/getPersonLevelDoor error ", e);
            rs = ZKResultMsg.failMsg();
        }
        return I18nUtil.i18nMsg(rs);
    }
}
