/*
 * @author: GenerationTools
 * 
 * @date: 2018-03-14 下午02:44 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.remote.AccDeviceEventRemote;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceEventService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceEventItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceEventSelectItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.format.TreeBuilder;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-14 下午02:44
 */
@Controller
public class AccDeviceEventController extends BaseController implements AccDeviceEventRemote {
    @Autowired
    private AccDeviceEventService accDeviceEventService;

    @RequiresPermissions("acc:deviceEvent")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/deviceEvent/accDeviceEvent");
    }

    @RequiresPermissions("acc:deviceEvent:edit")
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("item", accDeviceEventService.getItemById(id));
        }
        return new ModelAndView("acc/deviceEvent/editAccDeviceEvent");
    }

    @RequiresPermissions("acc:deviceEvent:edit")
    @LogRequest(module = "acc_module", object = "common_leftMenu_event", opType = "common_op_edit",
        requestParams = {"name"}, opContent = "common_event_name")
    @Override
    public ZKResultMsg save(AccDeviceEventItem item) {
        ZKResultMsg res = new ZKResultMsg();

        String fileOpType = request.getParameter("opType");
        String syncAllDevice = request.getParameter("syncAllDevice");

        accDeviceEventService.saveItem(item, fileOpType, syncAllDevice);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("acc:deviceEvent:refresh")
    @Override
    public DxGrid list(AccDeviceEventItem codition) {
        // 根据登录用户权限过滤设备
        Pager pager = accDeviceEventService.loadPagerByAuthFilter(request.getSession().getId(), codition, getPageNo(),
            getPageSize());
        List<AccDeviceEventItem> data = (List<AccDeviceEventItem>)pager.getData();
        for (AccDeviceEventItem event : data) {
            event.setLevelAndEventPriority(event.getEventLevel() + ";" + event.getEventPriority());
        }
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:deviceEvent:del")
    @Override
    public ZKResultMsg del(String ids) {
        accDeviceEventService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:deviceEvent:setSound")
    @Override
    public ZKResultMsg setSound(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            String fileOpType = request.getParameter("opType");
            String baseMediaFileId = request.getParameter("baseMediaFileId");
            String baseMediaFileName = request.getParameter("baseMediaFileName");
            String baseMediaFilePath = request.getParameter("baseMediaFilePath");
            String baseMediaFileSize = request.getParameter("baseMediaFileSize");
            String baseMediaFileSuffix = request.getParameter("baseMediaFileSuffix");
            accDeviceEventService.setSound(ids, fileOpType, baseMediaFileId, baseMediaFileName, baseMediaFilePath,
                baseMediaFileSize, baseMediaFileSuffix);
        }
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public TreeItem tree() {
        Set<String> eventList = accDeviceEventService.getAllEventNameSet();
        List<TreeItem> items = new ArrayList<>();
        for (String eventName : eventList) {
            TreeItem item = new TreeItem();
            item.setId(eventName);
            item.setText(I18nUtil.i18nCode(eventName));
            item.setParent(new TreeItem("0"));
            items.add(item);
        }
        List<TreeItem> treeItems = TreeBuilder.newTreeBuilder(TreeItem.class, String.class).buildToTreeList(items);
        return new TreeItem("0", treeItems);
    }

    @Override
    public DxGrid listSelect(AccDeviceEventSelectItem condition) {
        Pager pager = new Pager();
        int pageNo = getPageNo();
        int pageSize = getPageSize();
        Set<String> eventList = accDeviceEventService.getAllEventNameSet();
        List<AccDeviceEventSelectItem> items = new ArrayList<>();
        AccDeviceEventSelectItem item = null;
        for (String eventName : eventList) {
            String name = I18nUtil.i18nCode(eventName);
            if (StringUtils.isNotBlank(condition.getName()) && !name.contains(condition.getName())) {
                continue;
            }
            item = new AccDeviceEventSelectItem();
            item.setName(name);
            item.setId(eventName);
            items.add(item);
        }
        int beginIndex = pageNo * pageSize;
        int endIndex = (pageNo + 1) * pageSize - 1;
        List<AccDeviceEventSelectItem> data = new ArrayList<>();
        for (int i = beginIndex; i <= endIndex; i++) {
            if (items.size() <= i) {
                break;
            }
            data.add(items.get(i));
        }
        pager.setPage(pageNo);
        pager.setSize(pageSize);
        pager.setTotal(items.size());
        pager.setData(data);
        return GridUtil.convert(pager, condition.getClass());
    }
}