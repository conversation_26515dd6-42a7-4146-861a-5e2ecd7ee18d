package com.zkteco.zkbiosecurity.acc.controller;

import com.zkteco.zkbiosecurity.acc.remote.AccChannelRemote;
import com.zkteco.zkbiosecurity.acc.service.AccChannelService;
import com.zkteco.zkbiosecurity.acc.vo.AccChannelSelectItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

@Controller
public class AccChannelController extends BaseController implements AccChannelRemote {

    @Autowired
    private AccChannelService accChannelService;

    @Override
    public DxGrid list(AccChannelSelectItem condition) {
        if (StringUtils.isBlank(condition.getSelectId())){
            condition.setSelectId("-1");
        }
        //左列表
        if (condition.getType().equals("noSelected")){
            condition.setEnabled(true);
            condition.setNotInId(condition.getSelectId());
        }
        //右列表
        else if (condition.getType().equals("selected")){
            condition.setInId(condition.getSelectId());
        }
        //根据用户登录权限过滤显示摄像头
        Pager pager = accChannelService.loadPagerByAuthFilter(request.getSession().getId(), condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public ZKResultMsg isExistVidChannel() {
        return accChannelService.isExistVidChannel();
    }
}
