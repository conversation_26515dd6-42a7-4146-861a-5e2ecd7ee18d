/*
 * @author: GenerationTools
 * 
 * @date: 2018-03-14 下午03:02 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.remote.AccCombOpenPersonRemote;
import com.zkteco.zkbiosecurity.acc.service.AccCombOpenPersonService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonService;
import com.zkteco.zkbiosecurity.acc.vo.AccCombOpenPersonItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonCombOpenPersonItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-14 下午03:02
 */
@Controller
public class AccCombOpenPersonController extends BaseController implements AccCombOpenPersonRemote {
    @Autowired
    private AccCombOpenPersonService accCombOpenPersonService;
    @Autowired
    private AccPersonService accPersonService;

    @RequiresPermissions("acc:combOpenPerson")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/combOpenPerson/accCombOpenPerson");
    }

    @RequiresPermissions({"acc:combOpenPerson:add", "acc:combOpenPerson:edit"})
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("item", accCombOpenPersonService.getItemById(id));
        }
        return new ModelAndView("acc/combOpenPerson/editAccCombOpenPerson");
    }

    @RequiresPermissions({"acc:combOpenPerson:add", "acc:combOpenPerson:edit"})
    @LogRequest(module = "acc_module", object = "acc_leftMenu_personGroup", opType = "common_op_edit",
        requestParams = {"name"}, opContent = "acc_combOpen_personGroupName")
    @Override
    public ZKResultMsg save(AccCombOpenPersonItem item) {
        ZKResultMsg res = new ZKResultMsg();
        accCombOpenPersonService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("acc:combOpenPerson:refresh")
    @Override
    public DxGrid list(AccCombOpenPersonItem codition) {
        Pager pager = accCombOpenPersonService.loadPagerByAuthFilter(request.getSession().getId(), codition,
            getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:combOpenPerson:del")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_personGroup", opType = "common_op_del",
        requestParams = {"names"}, opContent = "acc_combOpen_personGroupName")
    @Override
    public ZKResultMsg del(String ids) {
        accCombOpenPersonService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public String valid(String name) {
        AccCombOpenPersonItem item = accCombOpenPersonService.getItemByName(name);
        boolean rs = item == null;
        return rs + "";
    }

    @Override
    public ZKResultMsg getCombOpenJsonData() {
        ZKResultMsg zkResultMsg = accCombOpenPersonService.getCombOpenJsonData();
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @Override
    public ZKResultMsg checkPersonCount(String groupId) {
        ZKResultMsg zkResultMsg = accCombOpenPersonService.checkPersonCount(groupId);
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @RequiresPermissions("acc:combOpenPerson:addPerson")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_personGroup", opType = "pers_common_addPerson",
        requestParams = {"combOpenName", "personPins"}, opContent = "pers_person_pin")
    @Override
    public ZKResultMsg addPerson(String combOpenPersonId, String personIds, String deptIds) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        if (StringUtils.isNotBlank(deptIds)) {
            personIds = accPersonService.getPersonIdsByDeptIds(deptIds);
        }
        if (StringUtils.isNotBlank(combOpenPersonId) && StringUtils.isNotBlank(personIds)) {
            List<String> personIdList = new ArrayList<>(Arrays.asList(personIds.split(",")));
            // 获取多人开门组已经存在的人员ID并且过滤掉。
            List<String> existPersonIds = accCombOpenPersonService.getExistPersonIds();
            if (existPersonIds.size() > 0) {
                personIdList.removeAll(existPersonIds);
            }
            zkResultMsg = accCombOpenPersonService.addPerson(combOpenPersonId, personIdList);
        }
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @RequiresPermissions("acc:combOpenPerson:delPerson")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_personGroup", opType = "pers_common_delPerson",
        requestParams = {"combOpenName", "personPins"}, opContent = "pers_person_pin")
    @Override
    public ZKResultMsg delPerson(String combOpenId, String personIds) {
        return I18nUtil.i18nMsg(accCombOpenPersonService.delPerson(combOpenId, personIds));
    }

    @RequiresPermissions("acc:combOpenPerson:refresh")
    @Override
    public DxGrid personList(AccPersonCombOpenPersonItem condition) {
        Pager pager = accPersonService.getCombOpenPersonItemList(request.getSession().getId(), condition, getPageNo(),
            getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public ZKResultMsg getCombOpenList() {
        return new ZKResultMsg(accCombOpenPersonService.getCombOpenList());
    }
}