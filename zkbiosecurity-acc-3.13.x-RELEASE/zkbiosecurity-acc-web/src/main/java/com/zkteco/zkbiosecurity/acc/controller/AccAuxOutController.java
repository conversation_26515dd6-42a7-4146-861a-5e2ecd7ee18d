package com.zkteco.zkbiosecurity.acc.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.remote.AccAuxOutRemote;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.AccAuxOutItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTimeSegItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 辅助输出
 * 
 * @author: GenerationTools
 * @date: 2018-03-14 上午09:38
 */
@Controller
public class AccAuxOutController extends ExportController implements AccAuxOutRemote {
    @Autowired
    private AccAuxOutService accAuxOutService;
    @Autowired
    private AccTimeSegService accTimeSegService;
    @Autowired
    private AccRTMonitorService accRTMonitorService;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccBaseDictionaryService accBaseDictionaryService;

    @RequiresPermissions("acc:auxOut")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/auxOut/accAuxOut");
    }

    @RequiresPermissions("acc:auxOut:edit")
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            AccAuxOutItem item = accAuxOutService.getItemById(id);
            boolean outRelaySetFunOn = accAuxOutService.isSupportOutRelaySet(item.getDevSn());
            request.setAttribute("item", item);
            request.setAttribute("outRelaySetFunOn", outRelaySetFunOn);
        }
        return new ModelAndView("acc/auxOut/editAccAuxOut");
    }

    @RequiresPermissions("acc:auxOut:edit")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_auxOut", opType = "common_op_edit",
        requestParams = {"name"}, opContent = "common_name")
    @Override
    public ZKResultMsg save(AccAuxOutItem item) {
        ZKResultMsg res = new ZKResultMsg();
        accAuxOutService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("acc:auxOut:refresh")
    @Override
    public DxGrid list(AccAuxOutItem codition) {
        Pager pager =
            accAuxOutService.loadPagerByAuthFilter(request.getSession().getId(), codition, getPageNo(), getPageSize());// 根据登录用户权限过滤设备
        List<AccAuxOutItem> accAuxOutItems = (List<AccAuxOutItem>)pager.getData();
        accAuxOutItems.stream().filter(item -> item.getAccTimeSegId() != null).forEach(accAuxOutItem -> {
            AccTimeSegItem accTimeSegItem = accTimeSegService.getItemById(accAuxOutItem.getAccTimeSegId());
            accAuxOutItem.setAccTimeSegName(accTimeSegItem.getName());
        });
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:auxOut:del")
    @Override
    public ZKResultMsg del(String ids) {
        accAuxOutService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions({"acc:auxOut:openAuxOut", "acc:auxOut:closeAuxOut", "acc:auxOut:normalOpen"})
    @Override
    public ModelAndView getAuxOutIds(String type, String ids) {
        List<String> idList = Arrays.asList(ids.split(","));
        Map<String, String> retMap = accAuxOutService.getAuxOutIds(idList);
        request.setAttribute("retIds", retMap.getOrDefault("retIds", ""));
        request.setAttribute("auxOutsName", retMap.getOrDefault("auxOutsName", ""));
        request.setAttribute("disabledAuxOutsName",
                retMap.getOrDefault("disabledAuxOutsName", ""));
        request.setAttribute("offlineAuxOutsName",
                retMap.getOrDefault("offlineAuxOutsName", ""));
        request.setAttribute("type", type);
        request.setAttribute("name", retMap.getOrDefault("auxOutNames", ""));
        return new ModelAndView("/acc/auxOut/opAccAuxOut");
    }

    @Override
    public boolean isExist(AccAuxOutItem item) {
        return accAuxOutService.isExist(item);
    }

    /**
     * 远程打开
     *
     * @author: train.chen
     * @date: 2018年5月18日 下午4:39:35
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "acc_leftMenu_auxOut", opType = "acc_rtMonitor_remoteOpen",
            requestParams = "name", opContent = "common_name")
    @RequiresPermissions("acc:auxOut:openAuxOut")
    public ZKResultMsg openAuxOut(String ids, String openInterval) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateAuxOut("openAuxOut", openInterval, ids);
        return dealResultData(dataMap);
    }

    /**
     * 远程关闭
     *
     * @author: train.chen
     * @date: 2018年5月18日 下午4:39:51
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "acc_leftMenu_auxOut", opType = "acc_rtMonitor_remoteClose",
            requestParams = "name", opContent = "common_name")
    @RequiresPermissions("acc:auxOut:closeAuxOut")
    public ZKResultMsg closeAuxOut(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateAuxOut("closeAuxOut", null, ids);
        return dealResultData(dataMap);
    }

    /**
     * 远程常开
     *
     * @author: train.chen
     * @date: 2018年5月18日 下午4:40:04
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "acc_leftMenu_auxOut", opType = "acc_rtMonitor_remoteNormalOpen",
            requestParams = "name", opContent = "common_name")
    @RequiresPermissions("acc:auxOut:normalOpen")
    public ZKResultMsg auxOutNormalOpen(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateAuxOut("auxOutNormalOpen", null, ids);
        return dealResultData(dataMap);
    }

    // 验证用户登录密码
    private void checkUserPwd() {
        if (isNeedValid("pwd")) {
            try {
                String loginPwd = this.request.getParameter("loginPwd");
                boolean ret = accLevelService.verifyLoginPwd(request.getSession().getId(), loginPwd);
                if (!ret) {
                    throw new ZKBusinessException("auth_user_pwdIncorrect");
                }
            } catch (ZKBusinessException e) {
                throw e;
            } catch (Exception e) {
                this.log.error("", e);
            }
        }
    }

    /**
     * @Description: 处理返回数据
     * <AUTHOR>
     * @date 2018/6/6 14:54
     * @param dataMap
     * @return
     */
    private ZKResultMsg dealResultData(Map<String, String> dataMap) {
        String cmdIdData = dataMap.get("cmdId");
        StringBuilder msg = new StringBuilder();
        ZKResultMsg resultMsg = new ZKResultMsg();
        if ("true".equals(dataMap.get("notExistDev"))) {
            msg = new StringBuilder(I18nUtil.i18nCode("common_dev_opFaileAndReason") + I18nUtil.i18nCode("common_dev_notExistDev"));
            resultMsg.setMsg(msg.toString());
            return resultMsg;
        }
        if (!"".equals(cmdIdData)) {
            for (String cmdData : cmdIdData.split(",")) {
                String cmdId = cmdData.split("=")[0];
                String doorName = cmdData.split("=")[1];
                Map<String, String> resultMap = accDeviceService.getCmdResultById(Long.valueOf(cmdId), 20);
                if (Objects.isNull(resultMap)) {
                    msg.append(doorName).append(",").append(I18nUtil.i18nCode("common_op_failed")).append(";");
                } else {
                    int ret = Integer.parseInt(resultMap.get("result"));
                    if (ret < 0) {
                        String failedInfo = I18nUtil.i18nCode(accBaseDictionaryService.getCommReason(ret));
                        msg.append(doorName).append(",").append(I18nUtil.i18nCode("common_dev_opFaileAndReason")).append(failedInfo).append(";");
                    }
                }
            }
        }
        if (!"".equals(dataMap.get("offline"))) {
            for (String doorName : dataMap.get("offline").split(",")) {
                msg.append(doorName).append(",").append(I18nUtil.i18nCode("common_dev_offlinePrompt")).append(";");
            }
        }
        if (!"".equals(dataMap.get("notSupport"))) {
            for (String doorName : dataMap.get("notSupport").split(",")) {
                msg.append(doorName).append(",").append(I18nUtil.i18nCode("acc_dev_devNotSupportFunction")).append(";");
            }
        }
        if (msg.length() > 0) {
            resultMsg.setRet("400");
            resultMsg.setMsg(msg.toString());
        }
        resultMsg.setData("");
        return resultMsg;
    }
}