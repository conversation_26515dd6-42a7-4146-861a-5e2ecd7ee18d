package com.zkteco.zkbiosecurity.acc.controller;

import com.zkteco.zkbiosecurity.acc.remote.AccSelectDoorRemote;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.acc.vo.AccSelectDoorItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

@Controller
public class AccSelectDoorController extends BaseController implements AccSelectDoorRemote{

    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccDeviceService accDeviceService;

    @Override
    public DxGrid list(AccSelectDoorItem codition) {
        if (codition.getType().equals("noSelected")){
            codition.setSelectDoorIdsNotIn(codition.getSelectId());
        }
        else if (codition.getType().equals("selected")){
            codition.setSelectDoorIdsIn(codition.getSelectId());
        }
        codition.setEnabled(true);
        Pager pager = accDoorService.getItemsByPage(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }
}
