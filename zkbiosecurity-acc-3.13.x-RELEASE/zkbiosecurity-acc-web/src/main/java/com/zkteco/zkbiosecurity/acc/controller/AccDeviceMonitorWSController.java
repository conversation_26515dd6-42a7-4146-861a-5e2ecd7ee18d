/*
 * Project Name: zkbiosecurity-acc-web
 * File Name: AccDeviceMonitorWSController.java
 * Copyright: Copyright(C) 1985-2018 ZKTeco Inc. All rights reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceMonitorService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceMonitorItem;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: train.chen
 * @date: 2018年5月11日 上午11:37:17
 */
@Controller
public class AccDeviceMonitorWSController extends BaseController{

	@Autowired
	private AccDeviceMonitorService accDeviceMonitorService;
	@Autowired
	private AccDeviceService accDeviceService;
	//	@Autowired
	//	private SimpMessagingTemplate messagingTemplate;

	@SuppressWarnings({"rawtypes", "unchecked"})
	@MessageMapping("/accDeviceMonitor/getDeviceEvents")
	@SendTo("/topic/accDeviceMonitor/getDeviceEvents")
	public JSONObject getDeviceEvents(String _params) {
		JSONObject params = JSONObject.parseObject(_params);
		JSONObject dxGrid = new JSONObject();
		String areaIds = params.containsKey("areaId") ? params.getString("areaId") : "";
		dxGrid.put("pos",0);
		List rows = new ArrayList();
		List<AccDeviceMonitorItem> accDeviceMonitorItems = accDeviceMonitorService.getDeviceMonitor(areaIds);
		accDeviceMonitorItems.forEach(item -> {
			JSONObject row = new JSONObject();
//			String status = accDeviceService.getStatus(item.getDevSn());
			row.put("id", item.getId());
			JSONObject userData = new JSONObject();
			userData.put("areaId", item.getAreaId());
			userData.put("status", item.getDevStatus());
			row.put("userdata", userData);
			if ("normal".equals(item.getDevStatus())) {//在线
				row.put("style", "color:green");
				row.put("dataLevel", AccConstants.EVENT_NORMAL);
			} else if ("disable".equals(item.getDevStatus())) {
				row.put("style", "color:#E57A14");
				row.put("dataLevel", AccConstants.EVENT_WARNING);
			} else {
				row.put("style", "color:red");
				row.put("dataLevel", AccConstants.EVENT_ALARM);
			}
			JSONArray data = new JSONArray();
			data.add(item.getDevName());//设备名称
			data.add(item.getDevSn());//序列号
			data.add(item.getAreaName());//区域
			data.add(item.getOpState());//操作状态
			data.add(item.getCurState());//当前状态
			data.add(item.getCmdCount());//待执行命令条数
			data.add(item.getLastError().equals("") ? I18nUtil.i18nCode("common_none") : item.getLastError());//最近异常状态
			row.put("data", data);
			rows.add(row);
		});
		dxGrid.put("rows", rows);
		dxGrid.put("clientId", params.get("clientId"));
		dxGrid.put("total_count",rows.size());
		return dxGrid;
	}
}
