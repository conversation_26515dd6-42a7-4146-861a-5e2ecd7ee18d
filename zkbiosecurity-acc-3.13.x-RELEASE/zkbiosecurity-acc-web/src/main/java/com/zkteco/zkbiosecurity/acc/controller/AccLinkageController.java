/*
 * @author: GenerationTools
 * 
 * @date: 2018-03-16 下午04:41 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.remote.AccLinkageRemote;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

import lombok.extern.slf4j.Slf4j;

/**
 * 门禁联动Controller
 */
@Controller
@Slf4j
public class AccLinkageController extends BaseController implements AccLinkageRemote {
    @Autowired
    private AccLinkageService accLinkageService;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDeviceEventService accDeviceEventService;
    @Autowired
    private AccLevelService accLevelService;

    @RequiresPermissions("acc:linkage")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/linkage/accLinkage");
    }

    @RequiresPermissions({"acc:linkage:add", "acc:linkage:edit"})
    @Override
    public ModelAndView edit(String id) {
        boolean showLine = accLinkageService.checkShowLine();
        if (StringUtils.isNotBlank(id)) {
            AccLinkageItem accLinkageItem = accLinkageService.getLinkageParams(id);
            request.setAttribute("item", accLinkageItem);
            if (showLine) {
                List<AccLinkage4LineContactItem> accLinkage4LineContactItems =
                    accLinkageService.getLineContactsByIds(accLinkageItem.getLineContactId());
                request.setAttribute("contactList", accLinkage4LineContactItems);
            }
            if (StringUtils.isNotBlank(accLinkageItem.getVdbExtensionId())) {
                List<AccLinkage4VdbExtensionItem> accLinkage4VdbExtensionItems =
                    accLinkageService.getVdbExtensionByIds(accLinkageItem.getVdbExtensionId());
                request.setAttribute("vdbExtensionList", accLinkage4VdbExtensionItems);
            }
        }
        // 判断是否开启云SIP
        request.setAttribute("supportSip", accLinkageService.supportCloudSip() + "");
        request.setAttribute("showLine", showLine);
        request.setAttribute("showWhatsapp", accLinkageService.checkShowWhatsapp());
        request.setAttribute("showSMS", accLinkageService.checkShowSMS());
        request.setAttribute("showDigifort", accLinkageService.checkShowDigifort());
        return new ModelAndView("acc/linkage/editAccLinkage");
    }

    @RequiresPermissions({"acc:linkage:add", "acc:linkage:edit"})
    @LogRequest(module = "acc_module", object = "acc_dev_linkage", opType = "common_op_edit", requestParams = {"name"},
        opContent = "common_linkIO_linkageName")
    @Override
    public ZKResultMsg save(AccLinkageItem item) {
        ZKResultMsg res = new ZKResultMsg();
        String[] inputsAddr = request.getParameter("inputAddr").split(",");// [0_any] or [1_AccDoor, 2_AccAuxIn]
        String outputDoor = request.getParameter("outputDoorAddr");
        String outputAuxOut = request.getParameter("outputAuxOutAddr");
        String[] outputDoorAddr = ("").equals(outputDoor) ? new String[] {} : outputDoor.split(",");
        String[] outputAuxOutAddr = ("").equals(outputAuxOut) ? new String[] {} : outputAuxOut.split(",");
        String[] outputsAddr = new String[outputDoorAddr.length + outputAuxOutAddr.length];
        System.arraycopy(outputDoorAddr, 0, outputsAddr, 0, outputDoorAddr.length);
        System.arraycopy(outputAuxOutAddr, 0, outputsAddr, outputDoorAddr.length, outputAuxOutAddr.length);
        String[] triggerCondArray = request.getParameter("triggerCond").split(",");
        short doorActionType = Short.parseShort(request.getParameter("doorActionType"));
        short doorActionTime = Short.parseShort(request.getParameter("doorActionTime"));
        short auxoutActionType = Short.parseShort(request.getParameter("auxoutActionType"));
        short auxoutActionTime = Short.parseShort(request.getParameter("auxoutActionTime"));
        String[] mailAddr = request.getParameter("mailAddr").replaceAll(";", ",").split(",");
        String[] mobileNo = null;
        if (StringUtils.isNotBlank(request.getParameter("mobileNo"))) {
            mobileNo = request.getParameter("mobileNo").replaceAll(";", ",").split(",");
        }
        String[] whatsappMobileNo = null;
        if (StringUtils.isNotBlank(item.getWhatsappMobileNo())) {
            whatsappMobileNo = item.getWhatsappMobileNo().replaceAll(";", ",").split(",");
        }
        String[] partitionIdArray =
            StringUtils.isBlank(item.getIasPartitionIds()) ? new String[] {} : item.getIasPartitionIds().split(",");
        String popUpVideo = request.getParameter("popUpVideo");
        String popUpTime = request.getParameter("popUpTime");
        String record = request.getParameter("record");
        // 录像回放事件发生前（）s
        String recordBeforeTime = request.getParameter("recordBeforeTime");
        // 录像回放事件发生后（）s;原录像时长
        String recordTime = request.getParameter("recordTime");
        String capture = request.getParameter("capture");
        String captureTime = request.getParameter("captureTime");
        String digiEventNames = request.getParameter("digiEventNames");
        String lineContactIds = request.getParameter("lineContactId");
        String vdbExtensionIds = request.getParameter("vdbExtensionId");
        AccLinkageBeanItem accLinkageBean = new AccLinkageBeanItem();
        accLinkageBean.setInputsAddr(inputsAddr);
        accLinkageBean.setOutputsAddr(outputsAddr);
        accLinkageBean.setTriggerCondArray(triggerCondArray);
        accLinkageBean.setDoorActionType(doorActionType);
        accLinkageBean.setDoorActionTime(doorActionTime);
        accLinkageBean.setAuxoutActionType(auxoutActionType);
        accLinkageBean.setAuxoutActionTime(auxoutActionTime);
        accLinkageBean.setMailAddr(mailAddr);
        accLinkageBean.setMobileNo(mobileNo);
        accLinkageBean.setLineContactIds(lineContactIds);
        accLinkageBean.setVdbExtensionIds(vdbExtensionIds);
        accLinkageBean.setVdbIvrId(item.getVdbIvrId());
        accLinkageBean.setWhatsappMobileNo(whatsappMobileNo);
        accLinkageBean.setPartitionActionType(item.getPartitionActionType());
        accLinkageBean.setIasPartitionIds(partitionIdArray);
        accLinkageBean.setIasManufacture(item.getIasManufacture());
        accLinkageBean.setPartitionArmType(item.getPartitionArmType());
        if (StringUtils.isNotBlank(popUpVideo) && (AccConstants.POP_UP_VIDEO + "").equals(popUpVideo)) {
            accLinkageBean.setPopUpTime(Integer.parseInt(popUpTime));
        }
        if (StringUtils.isNotBlank(record) && (AccConstants.RECORD + "").equals(record)) {
            accLinkageBean.setRecordBeforeTime(Integer.parseInt(recordBeforeTime));
            accLinkageBean.setRecordTime(Integer.parseInt(recordTime));
        }
        if (StringUtils.isNotBlank(capture) && (AccConstants.CAPTURE + "").equals(capture)) {
            accLinkageBean.setCapture(capture);
            if (StringUtils.isNotBlank(captureTime)) {
                accLinkageBean.setCaptureTime(Integer.parseInt(captureTime));
            } else {
                accLinkageBean.setCaptureTime(0);
            }
        }
        if (StringUtils.isNotBlank(digiEventNames)) {
            accLinkageBean.setDigiEventNames(digiEventNames);
        }
        accLinkageService.saveItem(accLinkageBean, item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("acc:linkage:refresh")
    @Override
    public DxGrid list(AccLinkageItem codition) {
        String authAreaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId());
        if (StringUtils.isNotBlank(authAreaIds)) {
            codition.setAuthAreaIdIn(authAreaIds);
        }
        Pager pager = accLinkageService.getItemsByPage(codition, getPageNo(), getPageSize());
        List<AccLinkageItem> accLinkageItemList = (List<AccLinkageItem>)pager.getData();
        // 拼装联动条件
        accLinkageItemList.forEach(accLinkageItem -> {
            List<String> triggerCondList = accLinkageService.getTriggerCondByLinkId(accLinkageItem.getId());
            StringBuilder triggerStr = new StringBuilder();
            triggerCondList.forEach(triggerCond -> {
                triggerStr.append(I18nUtil.i18nCode(triggerCond) + ",");
            });
            accLinkageItem.setTriggerCond(triggerStr.toString().substring(0, triggerStr.length() - 1));
        });
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:linkage:del")
    @LogRequest(module = "acc_module", object = "acc_dev_linkage", opType = "common_op_del", requestParams = {"names"},
        opContent = "common_linkIO_linkageName")
    @Override
    public ZKResultMsg del(String ids) {
        accLinkageService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public ZKResultMsg getAllFilterId() {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        String idStr = accLinkageService.getAllFilterId();
        zkResultMsg.setData(idStr);
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @Override
    public ZKResultMsg checkTriggerOpt() {
        String devId = request.getParameter("devId");
        String triggerOpt = request.getParameter("triggerOpt");
        String[] triggerCondArray = new String[0];
        if (triggerOpt.length() > 0) {
            triggerCondArray = request.getParameter("triggerOpt").split(",");
        }
        String[] inAddrArray = request.getParameter("inAddr").split(",");
        String linkageId = request.getParameter("linkageId");
        ZKResultMsg zkResultMsg = accLinkageService.checkTriggerOpt(devId, triggerCondArray, inAddrArray, linkageId);
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @Override
    public ZKResultMsg checkMailParam() {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        zkResultMsg.setData(accLinkageService.completeMailInfo());
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @Override
    public ZKResultMsg isSupportLockDoor(String devId) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        zkResultMsg.setData(accDeviceOptionService.getAccSupportFunListVal(devId, 2));
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @Override
    public String valid(String name) {
        AccLinkageItem item = accLinkageService.getItemByName(name);
        return (item == null) + "";
    }

    @Override
    public ZKResultMsg getLinkTriggerOpt() {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        String deviceId = request.getParameter("devId");
        String accLinkageId = request.getParameter("accLinkageId");
        Map<String, Object> triggerOptMap = accLinkageService.getLinkTriggerOpt(deviceId, accLinkageId);
        List<AccDeviceEventItem> eventList = (List<AccDeviceEventItem>)triggerOptMap.get("eventList");
        Map<Integer, String> checkEventMap = (Map<Integer, String>)triggerOptMap.get("checkEventMap");
        Map<Integer, String> checkParentElementMap = (Map<Integer, String>)triggerOptMap.get("checkParentElementMap");
        JSONArray triggerOptArray = new JSONArray();
        try {
            JSONArray deviceEvent = new JSONArray();// 设备的条件
            JSONArray auxInEvent = new JSONArray();// 辅助输入条件
            JSONArray doorEvent = new JSONArray();// 只门支持的条件
            JSONObject dataJson = null;
            for (AccDeviceEventItem event : eventList) {
                int eventNo = (int)event.getEventNo();
                // 需要过滤的联动事件
                if (!accDeviceEventService.isLinkTriggerFilterEventByNo(eventNo)) {
                    dataJson = new JSONObject();
                    dataJson.put("id", event.getEventNo());
                    dataJson.put("text", I18nUtil.i18nCode(event.getName()));
                    if (checkEventMap.containsKey(eventNo)) {
                        dataJson.put("checked", 1);
                    }
                    // 设备事件 by juvenile.li add 20170906
                    if (accDeviceEventService.isDeviceEventByNo(eventNo)) {
                        deviceEvent.add(dataJson);
                    } else if (accDeviceEventService.isAuxinEventByNo(eventNo)) {
                        // 辅助输入事件
                        auxInEvent.add(dataJson);
                    } else {
                        doorEvent.add(dataJson);
                    }
                }
            }
            // 父节点
            List<String> parentElement = new ArrayList<>();
            parentElement.add(I18nUtil.i18nCode("common_linkIO_doorEvent"));
            if (auxInEvent.size() > 0) {
                parentElement.add(I18nUtil.i18nCode("common_linkIO_auxInEvent"));
            }
            if (deviceEvent.size() > 0) {
                parentElement.add(I18nUtil.i18nCode("common_linkIO_deviceEvent"));
            }

            for (int i = 0; i < parentElement.size(); i++) {
                dataJson = new JSONObject();
                dataJson.put("id", i + "_parent");
                dataJson.put("text", parentElement.get(i));
                dataJson.put("open", "1");
                if (checkParentElementMap.containsKey(i)) {
                    dataJson.put("checked", "-1");
                }
                dataJson.put("item", i == AccConstants.LINKAGE_DOOR ? doorEvent
                    : i == AccConstants.LINKAGE_AUXIN ? auxInEvent : deviceEvent);
                triggerOptArray.add(dataJson);
            }
        } catch (Exception e) {
            throw new ZKBusinessException(e);
        }
        zkResultMsg.setData(triggerOptArray);
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @Override
    public ZKResultMsg getInOutInfo() {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        String devId = request.getParameter("devId");
        String accLinkageId = request.getParameter("accLinkageId");
        String[] triggerOpt = request.getParameter("triggerOpt").split(",");// 触发条件
        Map<String, Object> map = accLinkageService.getInOutInfo(devId, triggerOpt, accLinkageId);
        Map<String, Map<Integer, String>> checkedOutputMap =
            (Map<String, Map<Integer, String>>)map.get("checkedOutputMap");
        JSONArray doorOutputArray = (JSONArray)map.get("doorOutputArray");
        JSONArray auxOutOutputArray = (JSONArray)map.get("auxOutOutputArray");
        String devSn = (String)map.get("devSn");
        JSONArray inputArray = (JSONArray)map.get("inputArray");
        JSONObject actionObj = new JSONObject();
        actionObj.put("inputArray", inputArray);
        // 需要返回的输出点
        JSONArray outputArray = new JSONArray();
        if (accDeviceOptionService.isSupportFun(devSn, "~CtlAllRelayFunOn")) {
            outputArray = buildOutputArray(checkedOutputMap, doorOutputArray, auxOutOutputArray);// 组装输出点信息
            actionObj.put("doorOutputArray", outputArray.get(0));
            actionObj.put("auxOutOutputArray", outputArray.get(1));
            actionObj.put("ctlAllRelayFunOn", true);
        } else {
            // 不支持“所有”选项
            if (doorOutputArray.size() > 0) {
                actionObj.put("doorOutputArray", doorOutputArray);
            }
            if (auxOutOutputArray.size() > 0) {
                actionObj.put("auxOutOutputArray", auxOutOutputArray);
            }
            actionObj.put("ctlAllRelayFunOn", false);
        }
        zkResultMsg.setData(actionObj);
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @Override
    public DxGrid selectDevicelist(AccLinkageSelectDeviceItem codition) {
        String filterDevId = accLinkageService.getAllFilterId();
        if (StringUtils.isBlank(codition.getSelectId())) {
            codition.setSelectId("-1");
        }
        if (codition.getType().equals("noSelected")) {
            if (StringUtils.isNotBlank(filterDevId)) {
                filterDevId = codition.getSelectId() + "," + filterDevId;
                codition.setSelectId(filterDevId);
            }
            codition.setSelectDeviceIdsNotIn(codition.getSelectId());
        } else if (codition.getType().equals("selected")) {
            codition.setSelectDeviceIdsIn(codition.getSelectId());
        }
        String authAreaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId());
        if (StringUtils.isNotBlank(authAreaIds)) {
            codition.setAuthAreaIdIn(authAreaIds);
        }
        Pager pager = accDeviceService.getItemsByPage(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    /**
     * 组装输出点信息
     * 
     * @param checkedOutputMap
     * @param doorOutputArray
     * @param auxOutArray
     * @return
     */
    private JSONArray buildOutputArray(Map<String, Map<Integer, String>> checkedOutputMap, JSONArray doorOutputArray,
        JSONArray auxOutArray) {
        JSONArray outputArray = new JSONArray();
        try {
            JSONObject dataJson = new JSONObject();
            if (doorOutputArray.size() > 0) {
                dataJson.put("id", "0_AccDoor");
                dataJson.put("text", I18nUtil.i18nCode("common_linkIO_allDoor"));
                dataJson.put("open", "1");
                if (checkedOutputMap != null && checkedOutputMap.containsKey("AccDoor")
                    && checkedOutputMap.get("AccDoor").containsKey(AccConstants.LINKAGE_ALL))// 所有门
                {
                    dataJson.put("checked", "1");
                }
                dataJson.put("item", doorOutputArray);
                outputArray.add(dataJson);
            } else {
                // 没有门的异常情况
                outputArray.add("");
            }

            dataJson = new JSONObject();
            if (auxOutArray.size() > 0) {
                dataJson.put("id", "0_AccAuxOut");
                dataJson.put("text", I18nUtil.i18nCode("common_linkIO_allAuxOut"));
                dataJson.put("open", "1");
                if (checkedOutputMap != null && checkedOutputMap.containsKey("AccAuxOut")
                    && checkedOutputMap.get("AccAuxOut").containsKey(AccConstants.LINKAGE_ALL))// 所有辅助输出
                {
                    dataJson.put("checked", "1");
                }
                dataJson.put("item", auxOutArray);
                outputArray.add(dataJson);
            } else// 没有辅助输出的异常情况
            {
                outputArray.add("");
            }
        } catch (Exception e) {
            log.error("buildOutputArray", e);
        }
        return outputArray;
    }

    @Override
    public ZKResultMsg checkSMSModemParam() {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        zkResultMsg.setData(accLinkageService.completeSMSModemInfo());
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @Override
    public ZKResultMsg getDigifortGlobalEvents(String linkageId, String type) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        zkResultMsg.setData(accLinkageService.getDigifortGlobalEvents(linkageId, type));
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @Override
    public DxGrid selectLineContactList(AccLinkageSelectContactItem condition) {
        // 根据已经存在的读头ID获取
        Pager pager = new Pager();
        String selectIds = request.getParameter("selectValue");
        if (StringUtils.isBlank(selectIds)) {
            selectIds = "";
        }
        if (StringUtils.isNotBlank(condition.getSelectId())) {
            selectIds = selectIds + "," + condition.getSelectId();
        }
        condition.setSelectId(selectIds);
        if (condition.getType().equals("noSelected")) {
            pager = accLinkageService.getNoExistLineContacts(condition, getPageNo(), getPageSize());
        } else if (condition.getType().equals("selected")) {
            pager.setData(new ArrayList<AccLinkageSelectContactItem>());
        }
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public DxGrid getSelectIasPartition(AccLinkageSelectIasPartitionItem condition) {
        Pager pager = accLinkageService.getSelectIasPartition(request.getSession().getId(), condition, getPageNo(),
            getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public TreeItem getIasPartitionTreeByIds(String iasPartitionIds) {
        return accLinkageService.getIasPartitionTreeByIds(request.getSession().getId(), iasPartitionIds);
    }

    @Override
    public ZKResultMsg getArmTypeByManufacture(String manufacture) {
        List<SelectItem> selectItems = new ArrayList<>();
        SelectItem selectItem = null;
        ZKResultMsg retData = accLinkageService.getArmTypeByManufacture(manufacture);
        JSONArray dataArray = (JSONArray)retData.getData();
        if (dataArray.size() > 0) {
            for (Object obj : dataArray) {
                JSONObject data = (JSONObject)obj;
                selectItem = new SelectItem();
                selectItem.setValue(data.get("type") + "");
                selectItem.setText(data.get("name") + "");
                selectItems.add(selectItem);
            }
        }
        return new ZKResultMsg(selectItems);
    }

    @Override
    public ZKResultMsg getVdbIvr() {
        List<SelectItem> items = accLinkageService.getVdbIvrSelectItems();
        return new ZKResultMsg(items);
    }

    @Override
    public DxGrid selectVdbExtensionList(AccLinkageSelectExtensionItem condition) {
        Pager pager = new Pager();
        String selectIds = request.getParameter("selectValue");
        if (StringUtils.isBlank(selectIds)) {
            selectIds = "-1";
        }
        if (StringUtils.isNotBlank(condition.getSelectId())) {
            selectIds = selectIds + "," + condition.getSelectId();
        }
        condition.setSelectId(selectIds);
        if (condition.getType().equals("noSelected")) {
            String userId = accLevelService.getUserIdBySessionId(request.getSession().getId());
            if (StringUtils.isNotBlank(userId)) {
                condition.setUserId(userId);
            }
            pager = accLinkageService.getExtensionList(condition, getPageNo(), getPageSize());
        } else {
            pager.setData(new ArrayList<AccLinkageSelectExtensionItem>());
        }

        return GridUtil.convert(pager, condition.getClass());
    }
}