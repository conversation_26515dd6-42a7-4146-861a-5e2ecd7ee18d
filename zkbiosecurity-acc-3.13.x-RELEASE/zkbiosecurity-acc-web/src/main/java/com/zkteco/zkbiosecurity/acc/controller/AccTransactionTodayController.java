package com.zkteco.zkbiosecurity.acc.controller;

import java.lang.reflect.InvocationTargetException;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.remote.AccTransactionTodayRemote;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionTodayItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 对应百傲瑞达 AccTransactionController
 * 
 * @author: yibiao.shen
 * @date: 2018-03-07 16:52:41
 */
@Controller
public class AccTransactionTodayController extends ExportController implements AccTransactionTodayRemote {
    @Autowired
    private AccTransactionService accTransactionService;

    @RequiresPermissions("acc:transactionToday")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/transactionToday/accTransactionToday");
    }

    @RequiresPermissions("acc:transactionToday:refresh")
    @Override
    public DxGrid list(AccTransactionTodayItem condition) {
        condition.setStartTime(DateUtil.getTodayBeginTime());
        condition.setEndTime(DateUtil.getTodayEndTime());
        Pager pager = accTransactionService.loadTodayTransactionByAuthUserFilter(request.getSession().getId(),
            condition, getPageNo(), getPageSize(), getLimitCount());
        return GridUtil.convert(pager, condition.getClass(), getPageList());
    }

    @RequiresPermissions("acc:transactionToday:del")
    @Override
    public ZKResultMsg del(String ids) {
        accTransactionService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:transactionToday:del")
    @LogRequest(module = "acc_module", object = "acc_trans_today", opType = "common_op_clearData",
        opContent = "common_op_clearData")
    @Override
    public ZKResultMsg clearData() {
        accTransactionService.deleteAllDataByToday();
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:transactionToday:export")
    @LogRequest(module = "acc_module", object = "acc_trans_today", opType = "common_op_export",
        opContent = "common_op_export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response)
        throws InvocationTargetException, IllegalAccessException {
        AccTransactionTodayItem accTransactionTodayItem = new AccTransactionTodayItem();
        setConditionValue(accTransactionTodayItem);
        accTransactionTodayItem.setStartTime(DateUtil.getTodayBeginTime());
        accTransactionTodayItem.setEndTime(DateUtil.getTodayEndTime());
        accTransactionTodayItem
            .setAreaNameIn(accTransactionService.getAreaNamesBySessionId(request.getSession().getId()));
        accTransactionTodayItem
            .setDeptCodeIn(accTransactionService.getDeptCodesBySessionId(request.getSession().getId()));
        List<AccTransactionTodayItem> itemList = (List<AccTransactionTodayItem>)accTransactionService
            .getItemData(AccTransactionTodayItem.class, accTransactionTodayItem, getBeginIndex(), getEndIndex());
        excelExport(itemList, AccTransactionTodayItem.class);
    }
}
