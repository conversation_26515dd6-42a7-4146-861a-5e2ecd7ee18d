/*
 * @author: GenerationTools
 * 
 * @date: 2018-03-14 下午03:02 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.remote.AccCombOpenDoorRemote;
import com.zkteco.zkbiosecurity.acc.service.AccCombOpenDoorService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.acc.vo.AccCombOpenCombItem;
import com.zkteco.zkbiosecurity.acc.vo.AccCombOpenDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccCombOpenSelectDoorItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * @author: GenerationTools
 * @date: 2018-03-14 下午03:02
 */
@Controller
public class AccCombOpenDoorController extends BaseController implements AccCombOpenDoorRemote {
    @Autowired
    private AccCombOpenDoorService accCombOpenDoorService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDoorService accDoorService;

    @RequiresPermissions("acc:combOpenDoor")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/combOpenDoor/accCombOpenDoor");
    }

    @RequiresPermissions({"acc:combOpenDoor:add", "acc:combOpenDoor:edit"})
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("item", accCombOpenDoorService.getItemById(id));
            List<AccCombOpenCombItem> accCombOpenCombItemList = accCombOpenDoorService.getAccCombOpenCombList(id);
            request.setAttribute("tempList", accCombOpenCombItemList);
        }
        return new ModelAndView("acc/combOpenDoor/editAccCombOpenDoor");
    }

    @RequiresPermissions({"acc:combOpenDoor:add", "acc:combOpenDoor:edit"})
    @LogRequest(module = "acc_module", object = "acc_leftMenu_combOpen", opType = "common_op_edit",
        requestParams = {"name"}, opContent = "acc_combOpen_comboName")
    @Override
    public ZKResultMsg save(AccCombOpenDoorItem item) {
        ZKResultMsg res = new ZKResultMsg();
        String[] groupIds = request.getParameterValues("groupId");
        String[] openerNumbers = request.getParameterValues("openerNumber");
        String[] sorts = request.getParameterValues("sort");
        List<String> groupList = new ArrayList<>();
        for (String groupId : groupIds) {
            if (StringUtils.isNotBlank(groupId)) {
                groupList.add(groupId);
            }
        }
        groupIds = groupList.toArray(new String[5]);
        List<String> openerNumberList = new ArrayList<>();
        for (String openerNumber : openerNumbers) {
            if (StringUtils.isNotBlank(openerNumber)) {
                openerNumberList.add(openerNumber);
            }
        }
        openerNumbers = openerNumberList.toArray(new String[5]);

        accCombOpenDoorService.saveItem(item, groupIds, sorts, openerNumbers);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("acc:combOpenDoor:refresh")
    @Override
    public DxGrid list(AccCombOpenDoorItem codition) {
        String authAreaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId());
        if (StringUtils.isNotBlank(authAreaIds)) {
            codition.setAuthAreaIdIn(authAreaIds);
        }
        Pager pager = accCombOpenDoorService.getItemsByPage(codition, getPageNo(), getPageSize());
        List<AccCombOpenDoorItem> accCombOpenDoorItemList = (List<AccCombOpenDoorItem>)pager.getData();
        accCombOpenDoorItemList.forEach(accCombOpenDoorItem -> {
            // 用于显示多人开门人员组中的人员数
            accCombOpenDoorItem.setVerifyOneTime(
                String.valueOf(accCombOpenDoorService.getCombOpenPersonByGroup(accCombOpenDoorItem.getId())));
            // 用于显示多人开门人员组中的人员信息
            accCombOpenDoorItem
                .setCombOpenPersonId(accCombOpenDoorService.getCombOpenComb(accCombOpenDoorItem.getId()));
        });
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:combOpenDoor:del")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_combOpen", opType = "common_op_del",
        requestParams = {"names"}, opContent = "acc_combOpen_comboName")
    @Override
    public ZKResultMsg del(String ids) {
        accCombOpenDoorService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public String valid(String name) {
        AccCombOpenDoorItem item = accCombOpenDoorService.getItemByName(name);
        boolean rs = item == null;
        return rs + "";
    }

    @Override
    public ZKResultMsg validBackgroundVerify(String doorId) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        boolean backgroundVerifyFlag = true;
        backgroundVerifyFlag = accCombOpenDoorService.validBackgroundVerify(doorId);
        zkResultMsg.setData(backgroundVerifyFlag);
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @Override
    public DxGrid selectDoorlist(AccCombOpenSelectDoorItem codition) {
        List<String> doorIdList = accDoorService.getDoorIdAsReader();
        if (StringUtils.isBlank(codition.getSelectId())) {
            codition.setSelectId("-1");
        }
        if (codition.getType().equals("noSelected")) {
            if (doorIdList != null && doorIdList.size() > 0) {
                codition.setSelectId(codition.getSelectId() + "," + StringUtils.join(doorIdList, ","));
            }
            codition.setSelectDoorIdsNotIn(codition.getSelectId());
        } else if (codition.getType().equals("selected")) {
            codition.setSelectDoorIdsIn(codition.getSelectId());
        }
        String authAreaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId());
        if (StringUtils.isNotBlank(authAreaIds)) {
            codition.setAuthAreaIdIn(authAreaIds);
        }
        codition.setEnabled(true);
        Pager pager = accCombOpenDoorService.getItemsByPage(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }
}