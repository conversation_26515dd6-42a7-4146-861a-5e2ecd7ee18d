package com.zkteco.zkbiosecurity.acc.controller;

import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.zkteco.zkbiosecurity.security.SecurityService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.remote.AccTransactionRemote;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.service.AccParamService;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionCardNoItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionPhotoItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 对应百傲瑞达 AccTransactionController
 * 
 * @author: yibiao.shen
 * @date: 2018-03-07 16:52:41
 */
@Controller
public class AccTransactionController extends ExportController implements AccTransactionRemote {

    @Autowired
    private AccTransactionService accTransactionService;
    @Autowired
    private AccParamService accParamService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Value("${security.session.timeout:1800}")
    private Long sessionTimeout;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private SecurityService securityService;

    @RequiresPermissions("acc:transaction")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/transaction/accTransaction");
    }

    @RequiresPermissions("acc:transaction:refresh")
    @Override
    public DxGrid list(AccTransactionItem condition) {
        Pager pager = accTransactionService.loadTransactionByAuthUserFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize(), getLimitCount());
        return GridUtil.convert(pager, condition.getClass(), getPageList());
    }

    @RequiresPermissions("acc:transaction:refresh")
    @Override
    public ZKResultMsg listByApp(String pin, String doorId, int pageNo, int pageSize) {
        AccTransactionItem condition = new AccTransactionItem();
        condition.setPin(pin);
        condition.setEventPointId(doorId);
        Pager pager = accTransactionService.getItemsByPage(condition, pageNo, pageSize);
        ZKResultMsg msg = new ZKResultMsg(pager.getData());
        return msg;
    }

    @RequiresPermissions("acc:transaction:del")
    @Override
    public ZKResultMsg del(String ids) {
        accTransactionService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:transaction:del")
    @LogRequest(module = "acc_module", object = "common_leftMenu_transaction", opType = "common_op_clearData",
        opContent = "common_op_clearData")
    @Override
    public ZKResultMsg clearData() {
        accTransactionService.deleteAllData();
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:transaction:export")
    @LogRequest(module = "acc_module", object = "common_leftMenu_transaction", opType = "common_op_export",
        opContent = "common_op_export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AccTransactionItem accTransactionItem = new AccTransactionItem();
        setConditionValue(accTransactionItem);
        accTransactionItem.setAreaNameIn(accTransactionService.getAreaNamesBySessionId(request.getSession().getId()));
        accTransactionItem.setDeptCodeIn(accTransactionService.getDeptCodesBySessionId(request.getSession().getId()));
        List<AccTransactionItem> itemList = (List<AccTransactionItem>)accTransactionService
            .getItemData(AccTransactionItem.class, accTransactionItem, getBeginIndex(), getEndIndex());
        excelExport(itemList, AccTransactionItem.class);
    }

    @Override
    public ZKResultMsg isFileExist(String vidLinkageHandle) {
        boolean isExist = accTransactionService.isFileExist(vidLinkageHandle);
        return I18nUtil.i18nMsg(new ZKResultMsg(String.valueOf(isExist)));
    }

    @RequiresPermissions("acc:rtMonitor:transactionTodayHappen")
    @Override
    public DxGrid getTransactionsTodayLast(AccTransactionItem accTransactionItem) {
        accTransactionItem.setStartTime(DateUtil.getTodayBeginTime());
        accTransactionItem.setEndTime(DateUtil.getTodayEndTime());
        Pager pager = accTransactionService.getTransactionsTodayLast(accTransactionItem);
        return GridUtil.convert(pager, accTransactionItem.getClass());
    }

    @Override
    public ZKResultMsg readerCardNo(String readerIds, String type, Date time) {
        if (Objects.isNull(time)) {// 前端首次不传时间，用服务器时间传出去，再传进来
            time = new Date();
        }
        List<AccTransactionCardNoItem> item = accTransactionService.getCurrIssueCardNoByReader(readerIds, time);
        Map<String, Object> map = new HashMap<>();
        String cardNo = StringUtils.EMPTY;
        if (!CollectionUtils.isEmpty(item)) {
            cardNo = item.get(0).getCardNo();
            // 获取身份证的数据
            if ("IDCard".equals(type)) {
                JSONObject identityCardInfo = accTransactionService.getIdentityCardInfo(cardNo);
                if (identityCardInfo != null) {
                    if (identityCardInfo.containsKey("data")) {
                        JSONArray jsonArray = identityCardInfo.getJSONArray("data");
                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                        map.put("identityCardInfo", jsonObject.toJSONString());
                    }
                }
            }
        }
        map.put("cardNo", cardNo);
        map.put("time", DateUtil.dateToString(time, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        return I18nUtil.i18nMsg(new ZKResultMsg(map));
    }

    @Override
    public ZKResultMsg viewVidLinkData(String vidLinkageHandle, String fileType) {
        boolean capturePhotoEncrypt = securityService.checkPermissionExceptSupperUser(request.getSession().getId(),
            "acc:capturePhoto:encryptProp");
        ZKResultMsg ret = accTransactionService.viewVidLinkData(vidLinkageHandle, fileType);
        JSONObject data = new JSONObject();
        data.put("dataList", ret.getData());
        data.put("capturePhotoEncrypt", capturePhotoEncrypt);
        ret.setData(data);
        return ret;
    }

    @Override
    public ZKResultMsg getVideoFile(String id) {
        Map<String, Object> retMap = accTransactionService.getVideoFileValidate(id);
        if (Objects.nonNull(retMap)) {
            ZKResultMsg ret = (ZKResultMsg)retMap.get("ret");
            if (null != ret) {
                return I18nUtil.i18nMsg(ret);
            }
            String filePath = retMap.get("filePath").toString();
            File file = (File)retMap.get("file");
            int index = filePath.lastIndexOf(File.separator);
            String fileName = filePath.substring(index + 1);
            // 雄迈NVR录像，下载的时候，比较特殊，路径名称会变成一个文件夹，下面才是文件
            if (file.isDirectory() && file.listFiles().length > 0) {
                File childFile = file.listFiles()[0];
                fileName = fileName.substring(0, fileName.lastIndexOf("."))
                    + childFile.getName().substring(childFile.getName().lastIndexOf("."));
            }

            // 设置响应头和客户端保存文件名
            response.setCharacterEncoding("utf-8");
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
            try {
                // 打开本地文件流
                InputStream inputStream = new FileInputStream(filePath);
                // 激活下载操作
                OutputStream os = response.getOutputStream();
                // 循环写入输出流
                byte[] b = new byte[2048];
                int length;
                while ((length = inputStream.read(b)) > 0) {
                    os.write(b, 0, length);
                }
                os.close();
                inputStream.close();
            } catch (Exception e) {
                log.error("exception", e);
            }
        }
        return null;
    }

    @RequiresPermissions("acc:transaction:exportPhoto")
    @Override
    public void exportPhoto() throws Exception {
        // 验证用户登录密码
        if (isNeedValid("pwd")) {
            try {
                String loginPwd = this.request.getParameter("loginPwd");
                boolean ret = accLevelService.verifyLoginPwd(request.getSession().getId(), loginPwd);
                if (!ret) {
                    throw new ZKBusinessException("auth_user_pwdIncorrect");
                }
            } catch (ZKBusinessException e) {
                throw e;
            } catch (Exception e) {
                this.log.error("", e);
            }
        }
        // 设置导出结果的状态
        stringRedisTemplate.opsForValue().set(EXPORT_RESULT + request.getSession().getId(), "start");
        stringRedisTemplate.expire(EXPORT_RESULT + request.getSession().getId(), sessionTimeout, TimeUnit.SECONDS);

        try {
            // 根据时间范围查询图片文件路径集合
            String startDate = request.getParameter("startDate");
            String endDate = request.getParameter("endDate");
            String deviceSns = request.getParameter("deviceSns");

            AccTransactionPhotoItem condition = new AccTransactionPhotoItem();
            condition.setStartTime(DateUtil.stringToDate(startDate, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
            condition.setEndTime(DateUtil.stringToDate(endDate, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
            condition.setInDeviceSn(deviceSns);
            // 图片路径集合
            List<AccTransactionPhotoItem> accTransactionPhotoItems =
                accTransactionService.getPhotoFilePathList(request.getSession().getId(), condition);
            // 导出压缩包名称前缀
            String fileNamePrefix = I18nUtil.i18nCode("acc_trans_photo");
            String name = fileNamePrefix;
            // 图片文件集合
            List<AccTransactionPhotoItem> photoFileItems = new ArrayList<>();
            // 文件大小 (单位:byte)
            Long fileSizeTotal = 0L;
            for (AccTransactionPhotoItem item : accTransactionPhotoItems) {
                File file = new File(FileUtils.getLocalFullPath(item.getCapturePhotoPath()));
                if (file.exists()) {
                    item.setCapturePhotoPath(file.getAbsolutePath());
                    photoFileItems.add(item);
                    fileSizeTotal += file.length();
                }
            }

            // 文件数量为0，不进行导出。
            if (photoFileItems.size() == 0) {
                throw ZKBusinessException.warnException("common_report_dataSourceNull");
            }

            // 如果文件总的大小超过 500M/压缩率(20%-80%) 暂设 1G 或者文件数量大于1万 提示缩小时间范围导出
            if (fileSizeTotal > 1 * 1024 * 1024 * 1024 || photoFileItems.size() > 10000) {
                throw ZKBusinessException.warnException(I18nUtil.i18nCode("acc_trans_fileIsTooLarge"));
            }

            /*----------页面reponse设置----------*/
            String agent = request.getHeader("User-Agent");
            boolean isMSIE = (agent != null && (agent.indexOf("MSIE") != -1 || agent.indexOf("Trident") != -1));
            // IE浏览器
            if (isMSIE) {
                fileNamePrefix = java.net.URLEncoder.encode(fileNamePrefix, "UTF-8");
                // IE会将空格转成+号
                fileNamePrefix = fileNamePrefix.replaceAll("\\+", " ");
            } else {
                fileNamePrefix = new String(fileNamePrefix.getBytes("UTF-8"), "ISO-8859-1");
            }
            // Edge浏览器兼容处理
            if (request.getHeader("User-Agent") != null && request.getHeader("User-Agent").indexOf("Edge") != -1) {
                // 空格预处理
                fileNamePrefix = fileNamePrefix.replaceAll(" ", "%20");
                // 编码转换
                fileNamePrefix = URLEncoder.encode(fileNamePrefix, "ISO-8859-1");
                // 空格还原
                fileNamePrefix = fileNamePrefix.replace("%2520", " ");
            }
            String isEncrypt = request.getParameter("isEncrypt");
            if (StringUtils.isNotBlank(isEncrypt) && (!ExcelUtil.ENCRYPT_PASSWORD_YES.equals(isEncrypt))) {
                name = fileNamePrefix;
            }
            // 导出的压缩包路径 RootPath/upload/event/fileNamePrefix_yyyyMMdd_yyyyMMdd.zip
            String tempPath = (ClassUtil.getRootPath() + File.separator + FileUtils.systemFilePath + File.separator
                + "upload" + File.separator + "event").replace("/", File.separator) + File.separator + name + "_"
                + DateUtil.dateToString(condition.getStartTime(), DateUtil.DateStyle.YYYYMMDDHHMMSS) + "_"
                + DateUtil.dateToString(condition.getEndTime(), DateUtil.DateStyle.YYYYMMDDHHMMSS) + ".zip";

            File zipTempFile = new File(tempPath);
            if (!zipTempFile.exists()) {
                zipTempFile.createNewFile();
            }
            // 解密照片存储的临时路径
            String tempDecryptPath = AccConstants.LINKAGE_PHOTO_PATH + File.separator + "export_decrypt";
            response.reset();
            FileOutputStream fileOutputStream = new FileOutputStream(zipTempFile);
            ZipOutputStream zipOutputStream = new ZipOutputStream(fileOutputStream);
            try {
                File file;
                for (AccTransactionPhotoItem item : photoFileItems) {
                    file = new File(item.getCapturePhotoPath());
                    String fileName = file.getName();
                    // 压缩的文件按设备/日期/分目录
                    ZipEntry zipEntry = new ZipEntry(item.getCompressDirectory() + fileName);
                    String photoPath = item.getCapturePhotoPath()
                        .substring(item.getCapturePhotoPath().indexOf(File.separator + "upload"));
                    // 对图片进行解密
                    byte[] decPhoto = FileEncryptUtil.getDecryptFile(photoPath);
                    if (Objects.nonNull(decPhoto)) {
                        // 保存解密后的图片
                        String decryFilePath =
                            FileEncryptUtil.saveByte2File(tempDecryptPath + File.separator + fileName, decPhoto);
                        file = new File(FileUtils.getLocalFullPath(decryFilePath));
                    }
                    FileInputStream fileInputStream = new FileInputStream(file);
                    BufferedInputStream bufferedInputStream = new BufferedInputStream(fileInputStream, 512);
                    zipOutputStream.putNextEntry(zipEntry);
                    try {
                        byte[] buffer = new byte[512];
                        int index;
                        while ((index = bufferedInputStream.read(buffer)) != -1) {
                            zipOutputStream.write(buffer, 0, index);
                        }
                    } finally {
                        if (null != bufferedInputStream) {
                            bufferedInputStream.close();
                        }
                        if (null != fileInputStream) {
                            fileInputStream.close();
                        }
                    }
                }
            } finally {
                if (null != zipOutputStream) {
                    zipOutputStream.close();
                }
                if (null != fileOutputStream) {
                    fileOutputStream.close();
                }
            }

            // 导出压缩包下载
            try {
                if (StringUtils.isNotBlank(isEncrypt) && ExcelUtil.ENCRYPT_PASSWORD_YES.equals(isEncrypt)) {
                    ArrayList<File> files = new ArrayList<>();
                    files.add(zipTempFile);
                    String zipPath = (ClassUtil.getRootPath() + File.separator + FileUtils.systemFilePath
                        + File.separator + "upload").replace("/", File.separator) + File.separator + fileNamePrefix
                        + "_" + DateUtil.dateToString(condition.getStartTime(), DateUtil.DateStyle.YYYYMMDDHHMMSS) + "_"
                        + DateUtil.dateToString(condition.getEndTime(), DateUtil.DateStyle.YYYYMMDDHHMMSS) + ".zip";
                    FileUtil.fileEncryptZip(zipPath, files, request.getParameter("encryptPassword"), request, response);
                    // 删除临时文件
                    File file = new File(zipPath);
                    if (file.exists()) {
                        file.delete();
                    }
                } else {
                    FileUtils.downloadZip(zipTempFile, response);
                }
                response.flushBuffer();
            } finally {
                // 删除临时文件夹
                if (zipTempFile.exists()) {
                    // 删除临时压缩包
                    zipTempFile.delete();
                }
                // 删除解密临时文件夹
                FileUtil.deleteDirectory(FileUtil.getLocalFullPath(tempDecryptPath));
            }
        } finally {
            // 导出结束
            stringRedisTemplate.opsForValue().set(EXPORT_RESULT + request.getSession().getId(), "end");
        }
    }

    @Override
    public ZKResultMsg getDigifortVideoFile(String cameraName, long eventTime) {
        stringRedisTemplate.opsForValue().set(EXPORT_RESULT + request.getSession().getId(), "start");
        stringRedisTemplate.expire(EXPORT_RESULT + request.getSession().getId(), sessionTimeout, TimeUnit.SECONDS);
        ZKResultMsg ret = accTransactionService.getDigifortVideoFile(cameraName, eventTime);
        String filePath = "";
        if (ret.getData() != null) {
            filePath = (String)ret.getData();
        } else {
            stringRedisTemplate.opsForValue().set(EXPORT_RESULT + request.getSession().getId(), "end");
            stringRedisTemplate.expire(EXPORT_RESULT + request.getSession().getId(), sessionTimeout, TimeUnit.SECONDS);
            return ZKResultMsg.failMsg(ret.getMsg());
        }
        if (StringUtils.isEmpty(filePath)) {
            stringRedisTemplate.opsForValue().set(EXPORT_RESULT + request.getSession().getId(), "end");
            stringRedisTemplate.expire(EXPORT_RESULT + request.getSession().getId(), sessionTimeout, TimeUnit.SECONDS);
            return ZKResultMsg.failMsg();
        }
        String fileName = filePath.substring(filePath.lastIndexOf(File.separator) + 1);
        // 设置响应头和客户端保存文件名
        response.setCharacterEncoding("utf-8");
        response.setContentType("multipart/form-data");
        response.setHeader("Content-Disposition", "attachment;fileName=" + fileName);
        try {
            // 打开本地文件流
            InputStream inputStream = new FileInputStream(filePath);
            // 激活下载操作
            OutputStream os = response.getOutputStream();
            // 循环写入输出流
            byte[] b = new byte[2048];
            int length;
            while ((length = inputStream.read(b)) > 0) {
                os.write(b, 0, length);
            }
            os.close();
            inputStream.close();
        } catch (Exception e) {
            log.error("exception", e);
        }
        stringRedisTemplate.opsForValue().set(EXPORT_RESULT + request.getSession().getId(), "end");
        stringRedisTemplate.expire(EXPORT_RESULT + request.getSession().getId(), sessionTimeout, TimeUnit.SECONDS);
        return null;
    }

    @Override
    public ZKResultMsg getDecryptPhotoBase64(String photoPath) {
        if (StringUtils.isNotBlank(photoPath)) {
            String decryptPhoto =
                accParamService.getDecryptBase64ByCapturePhotoPath(request.getSession().getId(), photoPath);
            if (StringUtils.isNotBlank(decryptPhoto)) {
                return new ZKResultMsg(AccConstants.PHOTO_BASE64_PREFIX + decryptPhoto);
            }
        }
        return ZKResultMsg.failMsg();
    }
}
