package com.zkteco.zkbiosecurity.acc.controller;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import com.zkteco.zkbiosecurity.acc.remote.AccMapSelectChannelRemote;
import com.zkteco.zkbiosecurity.acc.service.AccMapPosService;
import com.zkteco.zkbiosecurity.acc.service.AccMapService;
import com.zkteco.zkbiosecurity.acc.vo.AccMapSelectChannelItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;

@Controller
public class AccMapSelectChannelController extends BaseController implements AccMapSelectChannelRemote {

    @Autowired
    private AccMapService accMapService;
    @Autowired
    private AccMapPosService accMapPosService;

    @Override
    public DxGrid list(AccMapSelectChannelItem condition) {
        String mapId = request.getParameter("mapId");
        String type = condition.getType();
        String filterIds = StringUtils.isNotBlank(condition.getSelectId()) ? "-1," + condition.getSelectId() : "-1";
        if ("noSelected".equals(type)) {
            String filterChannelIds = accMapPosService.getEntityIdsByMapIdAndEntityType(mapId, "VidChannel");
            if (StringUtils.isNotBlank(filterChannelIds)) {
                filterIds += "," + filterChannelIds;
            }
            condition.setNotInId(filterIds);
        } else if ("selected".equals(type)) {
            condition.setInId(filterIds);
        }
        Pager pager = accMapService.getSelectChannelItemsByPage(request.getSession().getId(), condition, getPageNo(),
            getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }
}
