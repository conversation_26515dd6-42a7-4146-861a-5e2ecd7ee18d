/*
 * @author: GenerationTools
 * 
 * @date: 2018-03-14 下午02:44 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.zkteco.zkbiosecurity.acc.service.AccParamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.remote.AccAlarmMonitorRemote;
import com.zkteco.zkbiosecurity.acc.service.AccAlarmMonitorService;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.vo.AccAlarmMonitorHistoryItem;
import com.zkteco.zkbiosecurity.acc.vo.AccAlarmMonitorItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-14 下午02:44
 */
@Controller
public class AccAlarmMonitorController extends ExportController implements AccAlarmMonitorRemote {

    @Autowired
    private AccAlarmMonitorService accAlarmMonitorService;
    @Autowired
    private AccTransactionService accTransactionService;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private AccParamService accParamService;

    @Override
    @RequiresPermissions("acc:alarmMonitor")
    public ModelAndView index() {
        String pinEncryptMode = accParamService.getParamValByName("pers.pin.encryptMode");
        String nameEncryptMode = accParamService.getParamValByName("pers.name.encryptMode");
        request.setAttribute("pinEncryptMode", pinEncryptMode);
        request.setAttribute("nameEncryptMode", nameEncryptMode);
        return new ModelAndView("acc/alarmMonitor/accAlarmMonitor");
    }

    @Override
    public ZKResultMsg ackAlarm(String key) {
        accAlarmMonitorService.checkAlarmMonitor(key);
        accAlarmMonitorService.sendAllAlarmMonitorWS(false);
        return new ZKResultMsg();
    }

    @Override
    public ZKResultMsg receiveNewMessagesRequest() {
        accAlarmMonitorService.sendAllAlarmMonitorWS(false);
        return ZKResultMsg.successMsg();
    }

    @Override
    public ZKResultMsg getDataByEventNum(Map filters) {
        JSONObject jsonObject = new JSONObject();
        filters.put("sessionId", request.getSession().getId());
        jsonObject.put("analysis", accAlarmMonitorService.getAnalysisByAuthFilter(request.getSession().getId()));
        /*List dataList = accAlarmMonitorService.getDataByEventNum(filters);
        jsonObject.put("alarm", accAlarmMonitorService.createJsonFromItems(dataList));*/
        return new ZKResultMsg(jsonObject);
    }

    @Override
    public ZKResultMsg getFirstPage(Map filters) {
        filters.put("sessionId", request.getSession().getId());
        List dataList = accAlarmMonitorService.getFirstPage(filters);
        return new ZKResultMsg(accAlarmMonitorService.createJsonFromItems(dataList));
    }

    @Override
    public ZKResultMsg getInfoByPageNo(Map filters) {
        Integer pageNo = Integer.valueOf(filters.get("pageNo") + "");
        Integer targetNum = Integer.valueOf(filters.get("targetNum") + "");
        filters.put("sessionId", request.getSession().getId());
        List dataList = accAlarmMonitorService.getNotAcknowledgedItemByPage(filters, pageNo, targetNum);
        return new ZKResultMsg(accAlarmMonitorService.createJsonFromItems(dataList));
    }

    @RequiresPermissions("acc:alarmMonitor:ackAlarm")
    @Override
    public ModelAndView editAcknowledged(String id) {
        request.setAttribute("id", id);
        request.setAttribute("description", accAlarmMonitorService.getDescription(id));
        request.setAttribute("editPage", false);
        return new ModelAndView("acc/alarmMonitor/editAcknowledged");
    }

    @Override
    @LogRequest(module = "acc_module", object = "acc_rtMonitor_alarmMonitor", opType = "acc_rtMonitor_ackAlarm",
        requestParams = {"id"}, opContent = "common_number")
    public ZKResultMsg editStatus(String id, Short status, String acknowledgement, Boolean sendEmail, String emails) {
        if (isNeedValid("pwd")) {
            try {
                String loginPwd = this.request.getParameter("loginPwd");
                boolean ret = accLevelService.verifyLoginPwd(request.getSession().getId(), loginPwd);
                if (!ret) {
                    throw new ZKBusinessException("auth_user_pwdIncorrect");
                }
            } catch (ZKBusinessException e) {
                throw e;
            } catch (Exception e) {
                this.log.error("", e);
            }
        }
        accAlarmMonitorService.changeStatus(id, status, acknowledgement);
        // 发送邮件先不处理；
        /*if (sendEmail != null && sendEmail) {
            accAlarmMonitorService.sendAlarmStatusEmail(id, status, acknowledgement, emails);
        }*/
        ZKResultMsg res = ZKResultMsg.successMsg();
        if (status == 2) {
            res.setData(id);
        }
        return res;
    }

    @Override
    @RequiresPermissions("acc:alarmMonitor:history")
    public ModelAndView showHistory(String id) {
        AccAlarmMonitorItem item = accAlarmMonitorService.getById(id);
        item.setEventName(I18nUtil.i18nCode(item.getEventName()));
        request.setAttribute("item", item);
        return new ModelAndView("acc/alarmMonitor/showHistory");
    }

    @Override
    @RequiresPermissions("acc:alarmHistory")
    public ModelAndView history() {
        return new ModelAndView("acc/alarmMonitor/alarmHistory");
    }

    @Override
    @RequiresPermissions("acc:alarmHistory:refresh")
    public DxGrid historyList(AccAlarmMonitorHistoryItem condition) {
        long limit = getLimitCount();
        String areaNames = accTransactionService.getAreaNamesBySessionId(request.getSession().getId());
        condition.setAreaNameIn(areaNames);
        Pager pager = accAlarmMonitorService.getHistoryByPage(condition, getPageNo(), getPageSize(), limit);
        return GridUtil.convert(pager, condition.getClass(), getPageList());
    }

    @Override
    @RequiresPermissions("acc:alarmHistory:clear")
    @LogRequest(module = "acc_module", object = "acc_alarm_history", opType = "common_search_clear",
        opContent = "common_search_clear")
    public ZKResultMsg clearHistoryData() {
        return accAlarmMonitorService.clearHistoryData();
    }

    @Override
    @RequiresPermissions("acc:alarmHistory:export")
    @LogRequest(module = "acc_module", object = "acc_alarm_history", opType = "common_op_export",
        opContent = "common_op_export")
    public void exportHistory(HttpServletRequest request, HttpServletResponse response) {
        AccAlarmMonitorHistoryItem historyItem = new AccAlarmMonitorHistoryItem();
        setConditionValue(historyItem);
        List<AccAlarmMonitorHistoryItem> itemList = (List<AccAlarmMonitorHistoryItem>)accAlarmMonitorService
            .getHistoryData(AccAlarmMonitorHistoryItem.class, historyItem, getBeginIndex(), getEndIndex());
        excelExport(itemList, AccAlarmMonitorHistoryItem.class);
    }
}