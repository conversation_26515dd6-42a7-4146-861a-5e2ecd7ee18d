package com.zkteco.zkbiosecurity.acc.dhx;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.core.utils.ShowGridColumn;

/**
 * 是否显示临时权限
 * 
 * <AUTHOR>
 * @date 2024/4/23 16:25
 */
@Component
public class AccShowLevelTime implements ShowGridColumn {
    /** 是否支持临时权限功能 */
    @Value("${system.levelTime.support:false}")
    private boolean isSupportLevelTime;

    @Override
    public boolean isShow(Object item) {
        return isSupportLevelTime;
    }
}
