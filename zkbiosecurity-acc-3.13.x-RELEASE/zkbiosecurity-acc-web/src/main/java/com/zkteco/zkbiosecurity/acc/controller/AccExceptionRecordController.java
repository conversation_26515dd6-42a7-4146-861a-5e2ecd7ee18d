package com.zkteco.zkbiosecurity.acc.controller;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.remote.AccExceptionRecordRemote;
import com.zkteco.zkbiosecurity.acc.service.AccExceptionRecordService;
import com.zkteco.zkbiosecurity.acc.vo.AccExceptionRecordItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 异常记录控制器
 * 
 * @author: AI Assistant
 * @date: 2025-07-21
 */
@Controller
public class AccExceptionRecordController extends ExportController implements AccExceptionRecordRemote {

    @Autowired
    private AccExceptionRecordService accExceptionRecordService;

    @RequiresPermissions("acc:exceptionRecord")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/exceptionRecord/accExceptionRecord");
    }

    @Override
    public DxGrid list(AccExceptionRecordItem condition) {
//        condition.setStatusFilter((short)1);
        Pager pager = accExceptionRecordService.loadExceptionRecordByAuthUserFilter(

            request.getSession().getId(), condition, getPageNo(), getPageSize(), getLimitCount());
        return GridUtil.convert(pager, condition.getClass(), getPageList());
    }

    @RequiresPermissions("acc:exceptionRecord:add")
    @Override
    public ModelAndView add() {
        ModelAndView mv = new ModelAndView("acc/exceptionRecord/accExceptionRecordEdit");
        mv.addObject("item", new AccExceptionRecordItem());
        mv.addObject("editType", "add");
        return mv;
    }

    @RequiresPermissions("acc:exceptionRecord:edit")
    @Override
    public ModelAndView edit(String id) {
        ModelAndView mv = new ModelAndView("acc/exceptionRecord/accExceptionRecordEdit");
        AccExceptionRecordItem item = accExceptionRecordService.getItemById(id);
        mv.addObject("item", item != null ? item : new AccExceptionRecordItem());
        mv.addObject("editType", "edit");
        return mv;
    }

    @RequiresPermissions("acc:exceptionRecord:save")
    @LogRequest(module = "acc_module", object = "acc_exception_record", opType = "common_op_save",
        opContent = "common_op_save")
    @Override
    public ZKResultMsg save(AccExceptionRecordItem item) {
        try {
            accExceptionRecordService.saveItem(item);
            return  ZKResultMsg.successMsg();
        } catch (Exception e) {
            return  ZKResultMsg.failMsg();
        }
    }

    @RequiresPermissions("acc:exceptionRecord:del")
    @LogRequest(module = "acc_module", object = "acc_exception_record", opType = "common_op_del",
        opContent = "common_op_del")
    @Override
    public ZKResultMsg del(String ids) {
        try {
            accExceptionRecordService.deleteByIds(ids);
            return  ZKResultMsg.successMsg();
        } catch (Exception e) {
            return  ZKResultMsg.failMsg();
        }
    }

    @RequiresPermissions("acc:exceptionRecord:clearData")
    @LogRequest(module = "acc_module", object = "acc_exception_record", opType = "common_op_clearData",
        opContent = "common_op_clearData")
    @Override
    public ZKResultMsg clearData() {
        try {
            accExceptionRecordService.deleteAllData();
            return  ZKResultMsg.successMsg();
        } catch (Exception e) {
            return  ZKResultMsg.failMsg();
        }
    }

    @RequiresPermissions("acc:exceptionRecord:export")
    @LogRequest(module = "acc_module", object = "acc_exception_record", opType = "common_op_export",
        opContent = "common_op_export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response)
        throws InvocationTargetException, IllegalAccessException {
        AccExceptionRecordItem condition = new AccExceptionRecordItem();
        setConditionValue(condition);
        condition.setAreaNameIn(accExceptionRecordService.getAreaNamesBySessionId(request.getSession().getId()));
        condition.setDeptCodeIn(accExceptionRecordService.getDeptCodesBySessionId(request.getSession().getId()));
        condition.setStatusFilter((short)1);

        List<AccExceptionRecordItem> itemList = (List<AccExceptionRecordItem>) accExceptionRecordService
            .getItemData(AccExceptionRecordItem.class, condition, getBeginIndex(), getEndIndex());
        excelExport(itemList, AccExceptionRecordItem.class);
    }

    @RequiresPermissions("acc:exceptionRecord:send")
    @LogRequest(module = "acc_module", object = "acc_exception_record", opType = "acc_exception_sendNotification",
        opContent = "acc_exception_sendNotification")
    @Override
    public ZKResultMsg sendNotification(String ids) {
        try {
            if (StringUtils.isBlank(ids)) {
                return I18nUtil.i18nMsg(new ZKResultMsg("false", "common_param_error"));
            }
            
            String[] idArray = ids.split(",");
            int successCount = 0;
            int totalCount = idArray.length;
            
            for (String id : idArray) {
                AccExceptionRecordItem item = accExceptionRecordService.getItemById(id);
                if (item != null) {
                    if (accExceptionRecordService.sendExceptionNotification(item)) {
                        successCount++;
                    }
                }
            }
            
            String message = String.format("发送完成，成功：%d，总数：%d", successCount, totalCount);
            return  ZKResultMsg.successMsg();
        } catch (Exception e) {
            return ZKResultMsg.failMsg();
        }
    }

    @RequiresPermissions("acc:exceptionRecord:resend")
    @LogRequest(module = "acc_module", object = "acc_exception_record", opType = "acc_exception_resendFailed",
        opContent = "acc_exception_resendFailed")
    @Override
    public ZKResultMsg resendFailed(String ids) {
        try {
            int successCount = accExceptionRecordService.resendFailedRecords(ids);
            String message = String.format("重新发送完成，成功：%d", successCount);
            return  ZKResultMsg.successMsg();
        } catch (Exception e) {
            return ZKResultMsg.failMsg();
        }
    }

    @RequiresPermissions("acc:exceptionRecord:edit")
    @LogRequest(module = "acc_module", object = "acc_exception_record", opType = "acc_exception_updateStatus",
        opContent = "acc_exception_updateStatus")
    @Override
    public ZKResultMsg updateStatus(String id, String exceptionStatus) {
        try {
            AccExceptionRecordItem item = accExceptionRecordService.updateExceptionStatus(id, exceptionStatus);
            if (item != null) {
                return  ZKResultMsg.successMsg();
            } else {
                return ZKResultMsg.failMsg();
            }
        } catch (Exception e) {
            return ZKResultMsg.failMsg();
        }
    }

    @RequiresPermissions("acc:exceptionRecord:view")
    @Override
    public ZKResultMsg detail(String id) {
        try {
            AccExceptionRecordItem item = accExceptionRecordService.getItemById(id);
            if (item != null) {
                return  ZKResultMsg.successMsg();
            } else {
                return  ZKResultMsg.failMsg();
            }
        } catch (Exception e) {
            return  ZKResultMsg.failMsg();
        }
    }


}
