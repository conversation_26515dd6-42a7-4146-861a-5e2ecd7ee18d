/*
 * Project Name: zkbiosecurity-acc-web
 * File Name: AccDeviceMonitorWSController.java
 * Copyright: Copyright(C) 1985-2018 ZKTeco Inc. All rights reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceEventService;
import com.zkteco.zkbiosecurity.acc.service.AccRTMonitorService;
import com.zkteco.zkbiosecurity.acc.utils.AccEnumUtil;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceEventItem;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;

import java.util.List;
import java.util.Objects;

/**
 * @author: train.chen
 * @date: 2018年5月11日 上午11:37:17
 */
@Controller
public class AccRTMonitorWSController {

	//	@Autowired
	//	private SimpMessagingTemplate messagingTemplate;
	@Autowired
	private AccRTMonitorService accRTMonitorService;
	@Autowired
    private AccDeviceEventService accDeviceEventService;

	@MessageMapping("/accRTMonitor/getEventData")
	@SendTo("/topic/accRTMonitor/getEventData")
	public Object getEventData(String _params) {
		return null;
	}

	@MessageMapping("/accRTMonitor/getDevState")
	@SendTo("/topic/accRTMonitor/getDevState")
	public Object getDevState(String params) {
		JSONObject paramJson = JSONObject.parseObject(params);
		String areaIds = paramJson.containsKey("areaIds") ? paramJson.getString("areaIds") : "";
		JSONObject data = new JSONObject();
		JSONArray stateArray = null;
		JSONArray doorStates = new JSONArray();
		JSONArray auxInStates = new JSONArray();
		JSONArray auxOutStates = new JSONArray();
		JSONObject dev = null;
		JSONObject devState = (JSONObject) accRTMonitorService.getDevStateData(areaIds).getData();
		stateArray = devState.getJSONArray("doorState");
		if (Objects.nonNull(stateArray) && stateArray.size() > 0) {
			for (int i = 0; i < stateArray.size() ; i++) {
				JSONObject door = stateArray.getJSONObject(i);
				dev = putDoorDataToMonitor(door);
				doorStates.add(dev);
			}
			data.put("doorStates", doorStates);
		}

		stateArray = devState.getJSONArray("auxInState");
		if (Objects.nonNull(stateArray) && stateArray.size() > 0) {
			for (int i = 0; i < stateArray.size() ; i++) {
				JSONObject auxIn = stateArray.getJSONObject(i);
				int connect = auxIn.getIntValue("connect");
				auxIn.put("image", AccEnumUtil.AuxInImage.getValue(connect));
				auxInStates.add(auxIn);
			}
			data.put("auxInStates", auxInStates);
		}

		stateArray = devState.getJSONArray("auxOutState");
		if (Objects.nonNull(stateArray) && stateArray.size() > 0) {
			for (int i = 0; i < stateArray.size() ; i++) {
				JSONObject auxOut = stateArray.getJSONObject(i);
				int connect = auxOut.getIntValue("connect");
				auxOut.put("image", AccEnumUtil.AuxOutImage.getValue(connect));
				auxOutStates.add(auxOut);
			}
			data.put("auxOutStates", auxOutStates);
		}
		return data;
	}

	private JSONObject putDoorDataToMonitor(JSONObject door) {
		JSONObject data = new JSONObject();
		int sensor = door.getIntValue("sensor");
		int relay = door.getIntValue("relay");
		int alarm = door.getIntValue("alarm");
		int connect = door.getIntValue("connect");
		short alarmLevel = door.getShortValue("alarmLevel");
		int doorState = door.getIntValue("doorState");
		boolean isNewAccess = door.getBooleanValue("isNewAccess");
		String alarmValue = AccEnumUtil.Alarm.getValue(doorState, alarmLevel, alarm);
		data.put("id", door.getString("id"));
		data.put("areaId", door.getString("areaId"));
		data.put("devAlias", door.getString("devAlias"));
		data.put("devSn", door.getString("devSn"));
		data.put("no", door.getString("no"));
		data.put("name", door.getString("name"));
		data.put("connect", connect);
		data.put("sensor", StringUtils.isNotBlank(AccEnumUtil.Sensor.getValue(doorState, sensor)) ? AccEnumUtil.Sensor.getValue(doorState, sensor) : "");
		data.put("relay", StringUtils.isNotBlank(AccEnumUtil.Relay.getValue(doorState, relay)) ? AccEnumUtil.Relay.getValue(doorState, relay) : "");
		data.put("alarm", StringUtils.isNotBlank(alarmValue) ? alarmValue : "");
		if (isNewAccess) {
			alarmLevel = 0;
		}
		data.put("image", AccEnumUtil.DoorImage.getValue(doorState, connect, alarmLevel, alarm, relay, sensor));
		data.put("lockDisplay", door.getString("lockDisplay"));
		data.put("opDisplay", doorState == ConstUtil.DEV_STATE_ONLINE || doorState == ConstUtil.DEV_STATE_LOCK ? "inline" : "none");
		//设备离线、报警事件默认报警声提示
		if(connect == ConstUtil.DEV_STATE_OFFLINE) {
			data.put("audio", "/public/media/sound/alarm.wav");
		} else if (alarm > 0) {
			if(!door.getBoolean("isDisableAudio")) {
				data.put("audio", "/public/media/sound/alarm.wav");
			}
		}
		data.put("doorState", doorState);
		return data;
	}

	private String getAlarmAudioPath(String alarmName, String doorId) {
		String alarmAudioPath = "";
		if(!I18nUtil.i18nCode("common_none").equals(alarmName))
		{
			if(I18nUtil.i18nCode("acc_rtMonitor_duressOpen").equals(alarmName) || I18nUtil.i18nCode("acc_rtMonitor_duressFingerOpen").equals(alarmName) || I18nUtil.i18nCode("acc_rtMonitor_duressPwdOpen").equals(alarmName)) {
                alarmName = I18nUtil.i18nCode("acc_newEventNo_101");
			}
			else if(I18nUtil.i18nCode("acc_rtMonitor_tamper").equals(alarmName)) {
                alarmName = I18nUtil.i18nCode("acc_eventNo_100");
			}
			else if(I18nUtil.i18nCode("acc_rtMonitor_duressOpen").equals(alarmName)) {
                alarmName = I18nUtil.i18nCode("acc_newEventNo_101");
			}
			else if(I18nUtil.i18nCode("acc_rtMonitor_alarm").equals(alarmName)) {
				return "/public/media/sound/alarm.wav";//有些机器没有个性声音设置，则赋值默认报警声音
			}

			List<AccDeviceEventItem> accDeviceEvents = accDeviceEventService.getItemsByNameAndDoorId(alarmName, doorId);
			if(Objects.nonNull(accDeviceEvents) && Objects.nonNull(accDeviceEvents.get(0)) && StringUtils.isNotBlank(accDeviceEvents.get(0).getBaseMediaFilePath())) {
				alarmAudioPath = accDeviceEvents.get(0).getBaseMediaFilePath();
			}
		}
		return alarmAudioPath;
	}
}
