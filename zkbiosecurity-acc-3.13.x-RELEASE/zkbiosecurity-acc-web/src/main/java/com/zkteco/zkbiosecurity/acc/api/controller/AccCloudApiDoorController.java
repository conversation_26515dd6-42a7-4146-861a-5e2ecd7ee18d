package com.zkteco.zkbiosecurity.acc.api.controller;

import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.base.annotation.ApiPermissions;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2020/01/19 14:10
*/

@Controller
@RequestMapping(value = {"/api/door"})
@Slf4j
public class AccCloudApiDoorController {

    @Autowired
    private AccDoorService accDoorService;

    /**
     * 迁移代码：从二号项目迁移app远程操作门接口
     * @auther zbx.zhong
     * @date 2019/6/9 16:19
     * @param message
     * @return
     */
    @ResponseBody
    @ApiPermissions(moduleCode = "acc", moduleName = "acc_module")
    @RequestMapping(value = "/operateDoorByApp", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Operate Door By App", notes = "Operate Door By App", response = ApiResultMessage.class)
    public ZKResultMsg operateDoorByApp(@RequestBody ZKMessage message)
    {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        Map map = message.getContent();
        String messageId = message.getMessageId();
        String openInterval = MapUtils.getString(map, "openInterval");
        String operator = MapUtils.getString(map, "operator");
        boolean isAdmin = MapUtils.getBoolean(map, "isAdmin", false);
        Map<String, List<Short>> doorMap = (Map<String, List<Short>>) map.get("doors");
        if (doorMap != null && !doorMap.isEmpty()) {
            for(String sn : doorMap.keySet()) {
                if (doorMap.get(sn) != null && !doorMap.get(sn).isEmpty()) {
                    String doorNos = StringUtils.join(doorMap.get(sn), ",");
                    List<Short> doorNoList = Arrays.stream(doorNos.split(",")).map(s-> Short.parseShort(s.trim())).collect(Collectors.toList());
                    String doorIds = accDoorService.getDoorByDevSnAndDoorNos(sn, doorNoList);
                    if (StringUtils.isNotBlank(doorIds)) {
                        String opType = messageId.split("#", 2)[1];
                        if ("cloudOpenDoor".equals(opType)) {
                            opType = "openDoor";
                        } else if ("cloudCancelAlarm".equals(opType)) {
                            opType = "cancelAlarm";
                        }
                        resultMsg = accDoorService.operateDoor(opType, openInterval, doorIds, operator, isAdmin);
                    }
                }
            }
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    /**
     * 置顶门功能
     * @auther zbx.zhong
     * @date 2019/7/11 16:19
     * @param message
     * @return
     */
    @ResponseBody
    @ApiPermissions(moduleCode = "acc", moduleName = "acc_module")
    @RequestMapping(value = "/topDoor", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Top Door By App", notes = "Top Door By App", response = ApiResultMessage.class)
    public ZKResultMsg topDoor(@RequestBody ZKMessage message)
    {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        Map map = message.getContent();
        String operator = (String) map.get("operator");//pin
        Map<String, Short> devDoorMap = (Map<String, Short>) map.get("devDoorMap");
        if (devDoorMap != null && !devDoorMap.isEmpty()) {
            for(String sn : devDoorMap.keySet()) {
                if (devDoorMap.get(sn) != null) {
                    String doorNos = StringUtils.join(devDoorMap.get(sn), ",");
                    List<Short> doorNoList = Arrays.stream(doorNos.split(",")).map(s-> Short.parseShort(s.trim())).collect(Collectors.toList());
                    String doorId = accDoorService.getDoorByDevSnAndDoorNos(sn, doorNoList);
                    if(StringUtils.isNotBlank(doorId)){
                        resultMsg = accDoorService.topDoor(operator, doorId);
                    }
                }
            }
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    /**
     * 置顶门功能
     * @auther zbx.zhong
     * @date 2019/7/11 16:19
     * @param message
     * @return
     */
    @ResponseBody
    @ApiPermissions(moduleCode = "acc", moduleName = "acc_module")
    @RequestMapping(value = "/cancleTopDoor", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Cancle Top Door By App", notes = "Cancle Top Door By App", response = ApiResultMessage.class)
    public ZKResultMsg cancleTopDoor(@RequestBody ZKMessage message)
    {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        Map map = message.getContent();
        String operator = (String) map.get("operator");//pin
        Map<String, Short> devDoorMap = (Map<String, Short>) map.get("devDoorMap");
        if (devDoorMap != null && !devDoorMap.isEmpty()) {
            for(String sn : devDoorMap.keySet()) {
                if (devDoorMap.get(sn) != null) {
                    String doorNos = StringUtils.join(devDoorMap.get(sn), ",");
                    List<Short> doorNoList = Arrays.stream(doorNos.split(",")).map(s-> Short.parseShort(s.trim())).collect(Collectors.toList());
                    String doorId = accDoorService.getDoorByDevSnAndDoorNos(sn, doorNoList);
                    if(StringUtils.isNotBlank(doorId)){
                        resultMsg = accDoorService.cancleTopDoor(operator, doorId);
                    }
                }
            }
        }
        return I18nUtil.i18nMsg(resultMsg);
    }
}
