/*
 * @author: GenerationTools
 * 
 * @date: 2018-03-14 下午03:02 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.remote.AccFirstOpenRemote;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.acc.service.AccFirstOpenService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonService;
import com.zkteco.zkbiosecurity.acc.vo.AccFirstOpenItem;
import com.zkteco.zkbiosecurity.acc.vo.AccFirstOpenSelectDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonFirstOpenItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * @author: GenerationTools
 * @date: 2018-03-14 下午03:02
 */
@Controller
public class AccFirstOpenController extends BaseController implements AccFirstOpenRemote {
    @Autowired
    private AccFirstOpenService accFirstOpenService;
    @Autowired
    private AccPersonService accPersonService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDoorService accDoorService;

    @RequiresPermissions("acc:firstOpen")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/firstOpen/accFirstOpen");
    }

    @RequiresPermissions({"acc:firstOpen:add", "acc:firstOpen:edit"})
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("item", accFirstOpenService.getItemById(id));
        }
        return new ModelAndView("acc/firstOpen/editAccFirstOpen");
    }

    @RequiresPermissions({"acc:firstOpen:add", "acc:firstOpen:edit"})
    @LogRequest(module = "acc_module", object = "acc_leftMenu_firstOpen", opType = "common_op_edit",
        requestParams = {"doorName"}, opContent = "acc_door_name")
    @Override
    public ZKResultMsg save(AccFirstOpenItem item) {
        ZKResultMsg res = new ZKResultMsg();
        accFirstOpenService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("acc:firstOpen:refresh")
    @Override
    public DxGrid list(AccFirstOpenItem condition) {
        Pager pager = accFirstOpenService.loadPagerByAuthFilter(request.getSession().getId(), condition, getPageNo(),
            getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("acc:firstOpen:del")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_firstOpen", opType = "common_op_del",
        requestParams = {"doorNames"}, opContent = "acc_door_name")
    @Override
    public ZKResultMsg del(String ids) {
        accFirstOpenService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:firstOpen:addPerson")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_firstOpen", opType = "pers_common_addPerson",
        requestParams = {"doorName", "personPins"}, opContent = "pers_person_pin")
    @Override
    public ZKResultMsg addPerson(String firstOpenId, String personIds, String deptIds) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        if (StringUtils.isNotBlank(deptIds)) {
            personIds = accPersonService.getPersonIdsByDeptIds(deptIds);
        }
        if (StringUtils.isNotBlank(firstOpenId) && StringUtils.isNotBlank(personIds)) {
            List<String> personIdList = new ArrayList<>(Arrays.asList(personIds.split(",")));
            zkResultMsg = accFirstOpenService.addPerson(firstOpenId, personIdList);
        }
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @RequiresPermissions("acc:firstOpen:delPerson")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_firstOpen", opType = "pers_common_delPerson",
        requestParams = {"doorName", "personPins"}, opContent = "pers_person_pin")
    @Override
    public ZKResultMsg delPerson(String firstOpenId, String personIds) {
        ZKResultMsg zkResultMsg = accFirstOpenService.delPerson(firstOpenId, personIds);
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @RequiresPermissions("acc:firstOpen:refresh")
    @Override
    public DxGrid personList(AccPersonFirstOpenItem condition) {
        Pager pager = accPersonService.getFirstOpenPersonItemList(request.getSession().getId(), condition, getPageNo(),
            getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public DxGrid selectDoorlist(AccFirstOpenSelectDoorItem condition) {
        List<String> doorIdList = accDoorService.getDoorIdAsReader(); // 获取需要过滤的门ids
        if (StringUtils.isBlank(condition.getSelectId())) {
            condition.setSelectId("-1");
        }
        if (condition.getType().equals("noSelected")) {
            if (doorIdList != null && doorIdList.size() > 0) {
                condition.setSelectId(condition.getSelectId() + "," + StringUtils.join(doorIdList, ","));
            }
            condition.setSelectDoorIdsNotIn(condition.getSelectId());
        } else if (condition.getType().equals("selected")) {
            condition.setSelectDoorIdsIn(condition.getSelectId());
        }
        condition.setEnabled(true);
        Pager pager = accFirstOpenService.loadFirstOpenSelectDoorByAuthFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }
}