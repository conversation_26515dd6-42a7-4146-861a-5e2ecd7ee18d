/*
 * @author: GenerationTools
 * 
 * @date: 2018-03-02 下午02:15 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import java.io.IOException;
import java.util.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.remote.AccLevelRemote;
import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonService;
import com.zkteco.zkbiosecurity.acc.service.AccTimeSegService;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.ExcelUtil;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 门禁权限组controller
 *
 * @author: yulong.dai
 * @date: 2018-03-02 下午02:15
 */
@Controller
public class AccLevelController extends ExportController implements AccLevelRemote {
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private AccTimeSegService accTimeSegService;
    @Autowired
    private AccPersonService accPersonService;

    /** 授权设备时可选权限组最大人员数量 */
    private int AUTHDEV_MAX_PERSON = 5000;

    @RequiresPermissions("acc:level")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/level/accLevel");
    }

    @RequiresPermissions({"acc:level:add", "acc:level:edit"})
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("personCount", accLevelService.getLevelPersonCount(id));
            request.setAttribute("doorCount", accLevelService.getLevelDoorCount(id));
            request.setAttribute("item", accLevelService.getItemById(id));
        } else {
            request.setAttribute("timeSegId",
                Objects.nonNull(accTimeSegService.getInitTimeSeg()) ? accTimeSegService.getInitTimeSeg().getId() : "");
        }
        return new ModelAndView("acc/level/editAccLevel");
    }

    @RequiresPermissions({"acc:level:add", "acc:level:edit"})
    @LogRequest(module = "acc_module", object = "acc_leftMenu_level", opType = "common_op_edit",
        requestParams = {"name"}, opContent = "common_level_name")
    @Override
    public ZKResultMsg save(AccLevelItem item) {
        ZKResultMsg res = new ZKResultMsg();
        progressCache.beginProcess(I18nUtil.i18nCode("common_op_startProcessing") + "<br/>", "",
            I18nUtil.i18nCode("common_op_currProgress"));
        item = accLevelService.saveItem(item);
        String levelId = item.getId();
        if (StringUtils.isNotBlank(levelId)) {
            if (item.getChangeLevel() != null && item.getChangeLevel()) {
                // 下发访客权限
                accLevelService.syncTimeSegVisLevelToDev(levelId);
                // 获取权限组下的设备ids
                List<String> deviceIdList = accLevelService.getDevIdsByLevelId(levelId);
                // 获取权限组下的人员ids
                List<String> personIdList = accLevelService.getPersonIdsByLevelId(levelId);
                List<String> tempPersonIdList = new ArrayList<>();
                for (int i = 0, devSize = deviceIdList.size(); i < devSize; i++) {
                    int total = (int)((i * 100.0 / devSize)); // 总进度
                    total = total == 100 ? 99 : total;// 防止前端过快结束
                    for (int j = 0, personSize = personIdList.size(); j < personSize; j++) {
                        tempPersonIdList.add(personIdList.get(j));
                        if (tempPersonIdList.size() % AccConstants.LEVEL_SPLIT_COUNT == 0) {
                            accLevelService.syncTimeSegLevelToDev(deviceIdList.get(i), tempPersonIdList);
                            progressCache.setProcess(new ProcessBean((int)(j * 100.0 / personSize), total, "",
                                I18nUtil.i18nCode("common_op_currProgress")));
                            tempPersonIdList = new ArrayList<>();
                        }
                    }
                    if (!tempPersonIdList.isEmpty()) {
                        accLevelService.syncTimeSegLevelToDev(deviceIdList.get(i), tempPersonIdList);
                        progressCache
                            .setProcess(new ProcessBean(100, total, "", I18nUtil.i18nCode("common_op_currProgress")));
                        tempPersonIdList = new ArrayList<>();
                    }
                }
            }
        }
        progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish"), "",
            I18nUtil.i18nCode("common_op_currProgress"));
        res.setData(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("acc:level:refresh")
    @Override
    public DxGrid list(AccLevelItem codition) {
        Pager pager =
            accLevelService.loadPagerByAuthFilter(request.getSession().getId(), codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:level:del")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_level", opType = "common_op_del",
        requestParams = {"names"}, opContent = "common_level_name")
    @Override
    public ZKResultMsg del(String ids) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        if (StringUtils.isNotBlank(ids)) {
            List<String> levelIdList = new ArrayList<>(Arrays.asList(ids.split(",")));
            AccLevelItem accLevelItem = new AccLevelItem();
            accLevelItem.setInitFlag(true);
            if (levelIdList.contains(accLevelService.getByCondition(accLevelItem).get(0).getId())) {
                resultMsg.setRet("500");
                resultMsg.setMsg("common_prompt_initDataCanNotDel");
            } else {
                progressCache.beginProcess(I18nUtil.i18nCode("common_op_startProcessing") + "<br/>", "",
                    I18nUtil.i18nCode("common_op_currProgress"));
                List<String> tempPersonIdList = new ArrayList<>();
                for (int i = 0, levelSize = levelIdList.size(); i < levelSize; i++) {
                    int total = (int)((i * 100.0 / levelSize)); // 总进度
                    total = total == 100 ? 99 : total;// 防止前端过快结束
                    List<String> personIdList = accLevelService.getPersonIdsByLevelId(levelIdList.get(i)); // 根据权限组id获取该权限组下的人员ids
                    if (personIdList != null && personIdList.size() > 0) {
                        List<String> deviceIdList = accLevelService.getDevIdsByLevelId(levelIdList.get(i));// 查询权限组中的设备
                        for (String deviceId : deviceIdList) {
                            for (int j = 0, personSize = personIdList.size(); j < personSize; j++) {
                                tempPersonIdList.add(personIdList.get(j));
                                // 对人员进行分批处理
                                if (tempPersonIdList.size() % AccConstants.LEVEL_SPLIT_COUNT == 0) {
                                    accLevelService.immeDelLevel(deviceId, levelIdList.get(i), tempPersonIdList);
                                    progressCache.setProcess(new ProcessBean((int)(j * 100.0 / personSize), total, "",
                                        I18nUtil.i18nCode("common_op_currProgress")));
                                    tempPersonIdList = new ArrayList<>();
                                }
                            }
                            if (tempPersonIdList.size() > 0) {
                                accLevelService.immeDelLevel(deviceId, levelIdList.get(i), tempPersonIdList);
                                progressCache.setProcess(
                                    new ProcessBean(100, total, "", I18nUtil.i18nCode("common_op_currProgress")));
                            }
                        }
                    } else {
                        accLevelService.delAccLevelDoorByLevelId(levelIdList.get(i)); // 删除权限组和门的中间表数据
                    }
                }
                accLevelService.deleteByIds(ids); // 删除权限组
                progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish") + "<br/>", "",
                    I18nUtil.i18nCode("common_op_currProgress"));
            }
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public ZKResultMsg getLevelList() {
        return new ZKResultMsg(accLevelService.getLevelList(request.getSession().getId()));
    }

    @Override
    public DxGrid selectDoorlist(AccLevelSelectDoorItem codition) {
        List<String> doorIdList = accDoorService.getDoorIdAsReader(); // 获取需要过滤的门ids
        String levelId = codition.getLevelId(); // 获取选中的权限组的ID
        if (StringUtils.isBlank(codition.getSelectId())) {
            codition.setSelectId("-1");
        }
        if (codition.getType().equals("noSelected")) {
            if (doorIdList != null && doorIdList.size() > 0) {
                codition.setSelectId(codition.getSelectId() + "," + StringUtils.join(doorIdList, ","));
            }
            codition.setSelectDoorIdsNotIn(codition.getSelectId());
            // codition.setSelectDoorIdsNotIn(StringUtils.join(selectDooorIds,","));
        } else if (codition.getType().equals("selected")) {
            codition.setSelectDoorIdsIn(codition.getSelectId());
        }
        codition.setFilterId(levelId); // 根据权限组的ID过滤人员
        codition.setEnabled(true);
        Pager pager =
            accDoorService.getPagerFilterAuth(request.getSession().getId(), codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:level:addDoor")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_level", opType = "acc_map_addDoor",
        requestParams = {"levelName", "doorNames"}, opContent = "common_level_name")
    @Override
    public ZKResultMsg addDoor(String levelId, String doorIds) {
        if (StringUtils.isNotBlank(levelId) && StringUtils.isNotBlank(doorIds)) {
            List<String> doorIdList = new ArrayList<>(Arrays.asList(doorIds.split(",")));
            // 查询出一体机当读头的门ids
            List<String> asWGReaderDoorIdList = accDoorService.getDoorIdsAsWGReaderByDoorId(doorIdList);
            if (asWGReaderDoorIdList != null && !asWGReaderDoorIdList.isEmpty()) {
                doorIdList.addAll(asWGReaderDoorIdList);
            }
            String clientId = request.getParameter("clientId");
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_startProcessing") + "<br/>", "",
                I18nUtil.i18nCode("common_op_currProgress"));
            accLevelService.addLevelByParamIds(StringUtils.join(doorIdList, ","), levelId, "door");
            List<String> deviceIdList = accDoorService.getDevAndParentDevIdsByDoorIds(doorIdList);
            List<String> personIdList = accLevelService.getPersonIdsByLevelId(levelId);
            if (personIdList != null && !personIdList.isEmpty()) {// 权限组中存在人员才进行人员下发
                List<String> personArrayIds = accPersonService.splitPersonIds(StringUtils.join(personIdList, ","),
                    AccConstants.LEVEL_SPLIT_COUNT);
                int[] currentCount = {0};
                int[] totalCount = {personArrayIds.size()};// 由于lambda表达式，这里声明成数组，如果更高明的写法可以替换
                personArrayIds.forEach(personArrayId -> {
                    // 下发人员权限到设备
                    accLevelService.handleLevelAddDoor(personArrayId, StringUtils.join(deviceIdList, ","), levelId);
                    currentCount[0] = currentCount[0] + 1;
                    progressCache.setProcess(new ProcessBean(0, (int)((currentCount[0]) * 100.0 / totalCount[0])),
                        clientId);// 单进度条，只需要设置总进度，当前进度传0即可
                });
            }
            // 下发访客信息到设备(由于访客人数不会很多所以单独处理)
            accLevelService.setVisitorToDev(levelId, deviceIdList);
            progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish") + "<br/>", "",
                I18nUtil.i18nCode("common_op_currProgress"));
        }
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:level:delDoor")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_level", opType = "acc_level_doorDelete",
        requestParams = {"levelName", "doorNames"}, opContent = "common_level_name")
    @Override
    public ZKResultMsg delDoor(String levelId, String doorIds) {
        if (StringUtils.isNotBlank(levelId) && StringUtils.isNotBlank(doorIds)) {
            List<String> doorIdList = new ArrayList<>(Arrays.asList(doorIds.split(",")));
            // 获取绑定的读头一体机设备同时删除
            List<String> asWGReaderDoorIdList =
                accDoorService.getDoorIdsAsWGReaderByDoorIdAndLevelId(doorIdList, levelId);
            if (asWGReaderDoorIdList != null && asWGReaderDoorIdList.size() > 0) {
                doorIdList.addAll(asWGReaderDoorIdList);
            }
            String clientId = request.getParameter("clientId");
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_startProcessing") + "<br/>", "",
                I18nUtil.i18nCode("common_op_currProgress"));
            accLevelService.delLevelDoorByParams(levelId, doorIdList);// 权限组删除门--数据库
            List<String> deviceIdList = accDoorService.getDevIdsByDoorIds(doorIdList); // 根据门ids获取设备的ids
            // 查询出权限组中的人员id
            List<String> personIdList = accLevelService.getPersonIdsByLevelId(levelId);
            if (personIdList != null && personIdList.size() > 0) {
                List<String> personArrayIds = accPersonService.splitPersonIds(StringUtils.join(personIdList, ","),
                    AccConstants.LEVEL_SPLIT_COUNT);
                int[] currentCount = {0};
                int[] totalCount = {personArrayIds.size()};
                personArrayIds.parallelStream().forEach(personArrayId -> {
                    // 删除人员权限
                    accLevelService.handleLevelDelDoor(levelId, personArrayId, StringUtils.join(deviceIdList, ","));
                    // 通知其他模块删除门禁权限
                    accLevelService.deleteAccLevel4OtherModule(doorIdList, Arrays.asList(personArrayId.split(",")));
                    currentCount[0] = currentCount[0] + 1;
                    progressCache.setProcess(new ProcessBean(0, (int)((currentCount[0]) * 100.0 / totalCount[0])),
                        clientId);// 单进度条，只需要设置总进度，当前进度传0即可
                });
            }
            accLevelService.delVisitorToDev(levelId, deviceIdList); // 删除访客权限
            progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish") + "<br/>", "",
                I18nUtil.i18nCode("common_op_currProgress"));
        }
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:level:refresh")
    @Override
    public DxGrid doorList(AccLevelDoorItem codition) {
        List<String> doorIdList = accDoorService.getDoorIdAsReader(); // 获取需要过滤的门ids
        if (doorIdList != null && doorIdList.size() > 0) {
            codition.setNotInId(StringUtils.join(doorIdList, ","));
        }
        Pager pager =
            accDoorService.accLevelDoorList(request.getSession().getId(), codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @Override
    public ZKResultMsg doorListByApp(String id) {

        AccLevelDoorItem condition = new AccLevelDoorItem();// 构造查询条件

        List<String> doorIdList = accDoorService.getDoorIdAsReader(); // 获取需要过滤的门ids
        if (doorIdList != null && doorIdList.size() > 0) {
            condition.setNotInId(StringUtils.join(doorIdList, ","));
        }
        List<AccLevelItem> acclevels = accLevelService.getLevelByPersonId(id);// 查找到该用户所属的所有门禁权限组
        List<AccDoorItem> doors = new ArrayList<AccDoorItem>();
        for (AccLevelItem item : acclevels) {
            String levelId = item.getId();
            condition.setAccLevelId(levelId);
            // 根据门禁权限组id以及排除掉的门id查询所有门vo
            List<AccDoorItem> doorItems =
                (List<AccDoorItem>)accDoorService.getItemsByPage(condition, 0, 1000000).getData();
            doors.addAll(doorItems);
        }
        ZKResultMsg msg = new ZKResultMsg(doors);
        return msg;
    }

    @Override
    public String valid(String name) {
        AccLevelItem item = accLevelService.getItemByName(name);
        boolean rs = item == null;
        return rs + "";
    }

    @RequiresPermissions("acc:level:export")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_level", opType = "acc_level_exportLevel",
        opContent = "acc_level_exportLevel")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AccLevelItem accLevelItem = new AccLevelItem();
        setConditionValue(accLevelItem);
        String userId = accLevelService.getUserIdBySessionId(request.getSession().getId());
        accLevelItem.setUserId(userId);
        List<AccLevelItem> accLevelItemList =
            accLevelService.getExportLevelItemList(accLevelItem, getBeginIndex(), getEndIndex());
        excelExport(accLevelItemList, AccLevelItem.class);
    }

    @Override
    public ZKResultMsg getPersonCount(String levelId) {
        Long personCount = accLevelService.getLevelPersonCount(levelId);
        return new ZKResultMsg(personCount);
    }

    @Override
    public ZKResultMsg levelIsDelete(String ids) {
        ZKResultMsg zkResultMsg = accLevelService.levelIsDelete(ids);
        if ("vis".equals(zkResultMsg.getData())) {
            zkResultMsg.setMsg(I18nUtil.i18nCode("acc_level_visUsed", zkResultMsg.getMsg()));
        }
        return zkResultMsg;
    }

    @LogRequest(module = "acc_module", object = "acc_leftMenu_level", opType = "acc_eventNo_8",
        requestParams = {"names"}, opContent = "common_level_name")
    @RequiresPermissions("acc:level:openDoor")
    @Override
    public ZKResultMsg openDoor(String ids, String openInterval) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap =
            accLevelService.operateDoor("openDoor", openInterval, ids, request.getSession().getId());
        if (dataMap.size() > 1) {
            return accLevelService.dealResultData(dataMap);
        }
        return new ZKResultMsg();
    }

    @LogRequest(module = "acc_module", object = "acc_leftMenu_level", opType = "acc_eventNo_9",
        requestParams = {"names"}, opContent = "common_level_name")
    @RequiresPermissions("acc:level:closeDoor")
    @Override
    public ZKResultMsg closeDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accLevelService.operateDoor("closeDoor", null, ids, request.getSession().getId());
        if (dataMap.size() > 1) {
            return accLevelService.dealResultData(dataMap);
        }
        return new ZKResultMsg();
    }

    @LogRequest(module = "acc_module", object = "acc_leftMenu_level", opType = "acc_eventNo_7",
        requestParams = {"names"}, opContent = "common_level_name")
    @RequiresPermissions("acc:level:cancelAlarmDoor")
    @Override
    public ZKResultMsg cancelAlarmDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap =
            accLevelService.operateDoor("cancelAlarm", null, ids, request.getSession().getId());
        if (dataMap.size() > 1) {
            return accLevelService.dealResultData(dataMap);
        }
        return new ZKResultMsg();
    }

    @LogRequest(module = "acc_module", object = "acc_leftMenu_level", opType = "acc_eventNo_8",
        requestParams = {"names"}, opContent = "common_level_name")
    @RequiresPermissions("acc:level:normalOpenDoor")
    @Override
    public ZKResultMsg normalOpenDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap =
            accLevelService.operateDoor("normalOpenDoor", null, ids, request.getSession().getId());
        if (dataMap.size() > 1) {
            return accLevelService.dealResultData(dataMap);
        }
        return new ZKResultMsg();
    }

    @LogRequest(module = "acc_module", object = "acc_leftMenu_level", opType = "common_enable",
        requestParams = {"names"}, opContent = "common_level_name")
    @RequiresPermissions("acc:level:enableDoor")
    @Override
    public ZKResultMsg enableDoor(String ids) {
        ZKResultMsg res = accLevelService.enableDoor(ids, request.getSession().getId());
        return I18nUtil.i18nMsg(res);
    }

    @LogRequest(module = "acc_module", object = "acc_leftMenu_level", opType = "common_disable",
        requestParams = {"names"}, opContent = "common_level_name")
    @RequiresPermissions("acc:level:disableDoor")
    @Override
    public ZKResultMsg disableDoor(String ids) {
        ZKResultMsg res = new ZKResultMsg();
        accLevelService.disableDoor(ids, request.getSession().getId());
        return I18nUtil.i18nMsg(res);
    }

    @LogRequest(module = "acc_module", object = "acc_leftMenu_level", opType = "acc_newEventNo_233",
        requestParams = {"names"}, opContent = "common_level_name")
    @RequiresPermissions("acc:level:lockDoor")
    @Override
    public ZKResultMsg lockDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accLevelService.operateDoor("lockDoor", null, ids, request.getSession().getId());
        if (dataMap.size() > 1) {
            return accLevelService.dealResultData(dataMap);
        }
        return new ZKResultMsg();
    }

    @LogRequest(module = "acc_module", object = "acc_leftMenu_level", opType = "acc_newEventNo_234",
        requestParams = {"names"}, opContent = "common_level_name")
    @RequiresPermissions("acc:level:unLockDoor")
    @Override
    public ZKResultMsg unLockDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap =
            accLevelService.operateDoor("unLockDoor", null, ids, request.getSession().getId());
        if (dataMap.size() > 1) {
            return accLevelService.dealResultData(dataMap);
        }
        return new ZKResultMsg();
    }

    @LogRequest(module = "acc_module", object = "acc_leftMenu_level", opType = "acc_eventNo_11",
        requestParams = {"names"}, opContent = "common_level_name")
    @RequiresPermissions("acc:level:enableNormalOpenDoor")
    @Override
    public ZKResultMsg enableNormalOpenDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap =
            accLevelService.operateDoor("enableNormalOpenDoor", null, ids, request.getSession().getId());
        if (dataMap.size() > 1) {
            return accLevelService.dealResultData(dataMap);
        }
        return new ZKResultMsg();
    }

    @LogRequest(module = "acc_module", object = "acc_leftMenu_level", opType = "acc_eventNo_10",
        requestParams = {"names"}, opContent = "common_level_name")
    @RequiresPermissions("acc:level:disableNormalOpenDoor")
    @Override
    public ZKResultMsg disableNormalOpenDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap =
            accLevelService.operateDoor("disableNormalOpenDoor", null, ids, request.getSession().getId());
        if (dataMap.size() > 1) {
            return accLevelService.dealResultData(dataMap);
        }
        return new ZKResultMsg();
    }

    @Override
    public ZKResultMsg authDevAddLevelVerify(String id) {
        Long personCount = accLevelService.getLevelPersonCount(id);
        if (personCount > AUTHDEV_MAX_PERSON) {
            return ZKResultMsg.failMsg(I18nUtil.i18nCode("acc_level_personExceedMax", personCount, AUTHDEV_MAX_PERSON));
        }
        return ZKResultMsg.successMsg();
    }

    @LogRequest(module = "acc_module", object = "acc_leftMenu_level", opType = "acc_level_exportLevelDoor",
        opContent = "acc_level_exportLevelDoor")
    @RequiresPermissions("acc:levelDoor:export")
    @Override
    public void exportLevelDoor(HttpServletRequest request, HttpServletResponse response) {
        // 验证用户登录密码
        checkUserPwd();
        String levelIds = request.getParameter("levelIds");
        AccLevelDoorExportItem accLevelDoorItem = new AccLevelDoorExportItem();
        accLevelDoorItem.setLevelIdsIn(levelIds);
        List<AccLevelDoorExportItem> accLevelDoorItemList =
            accDoorService.getExportLevelDoorItemList(accLevelDoorItem, getBeginIndex(), getEndIndex());
        excelExport(accLevelDoorItemList, AccLevelDoorExportItem.class);
    }

    @LogRequest(module = "acc_module", object = "acc_leftMenu_level", opType = "acc_level_importLevel",
        opContent = "acc_level_importLevel")
    @RequiresPermissions("acc:level:import")
    @Override
    public ZKResultMsg importExcel(MultipartFile upload) throws IOException {
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_uploadFileSuccess") + "<br/>"));
            List<AccLevelItem> itemList = ExcelUtil.excelImport(upload.getInputStream(), AccLevelItem.class);
            progress += 10;
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_resolutionComplete") + "<br/>"));
            // 是否更新已存在数据
            boolean updateExistData = false;
            String isupdateExistData = request.getParameter("updateExistData");
            if ("1".equals(isupdateExistData)) {
                updateExistData = true;
            }
            return I18nUtil.i18nMsg(accLevelService.importLevelData(itemList, updateExistData));
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                    + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                    new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("Import AccLevel Info Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
    }

    @LogRequest(module = "acc_module", object = "acc_leftMenu_level", opType = "acc_level_importLevelDoor",
        opContent = "acc_level_importLevelDoor")
    @RequiresPermissions("acc:levelDoor:import")
    @Override
    public ZKResultMsg importLevelDoorExcel(MultipartFile upload) throws IOException {
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_uploadFileSuccess") + "<br/>"));
            List<AccLevelDoorExportItem> itemList =
                ExcelUtil.excelImport(upload.getInputStream(), AccLevelDoorExportItem.class);
            progress += 10;
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_resolutionComplete") + "<br/>"));
            // 是否更新已存在数据
            boolean updateExistData = false;
            String isupdateExistData = request.getParameter("updateExistData");
            if ("1".equals(isupdateExistData)) {
                updateExistData = true;
            }
            return I18nUtil.i18nMsg(accLevelService.importLevelDoorData(itemList, updateExistData));
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                    + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                    new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("Import AccLevelDoor Info Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
    }

    // 验证用户登录密码
    public void checkUserPwd() {
        if (isNeedValid("pwd")) {
            try {
                String loginPwd = this.request.getParameter("loginPwd");
                boolean ret = accLevelService.verifyLoginPwd(request.getSession().getId(), loginPwd);
                if (!ret) {
                    throw new ZKBusinessException("auth_user_pwdIncorrect");
                }
            } catch (ZKBusinessException e) {
                throw e;
            } catch (Exception e) {
                this.log.error("", e);
            }
        }
    }
}