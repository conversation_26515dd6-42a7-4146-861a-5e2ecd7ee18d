/*
 * Project Name: zkbiosecurity-acc-web File Name: AccDeviceMonitorWSController.java Copyright: Copyright(C) 1985-2018
 * ZKTeco Inc. All rights reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.service.AccAlarmMonitorService;

/**
 * 报警WebSocket连接
 * 
 * @author: train.chen
 * @date: 2018年5月11日 上午11:37:17
 */
@Controller
public class AccAlarmMonitorWSController {

    @Autowired
    private AccAlarmMonitorService accAlarmMonitorService;

    @MessageMapping("/accAlarmMonitor/getEventData")
    @SendTo("/topic/accAlarmMonitor/getEventData")
    public JSONObject getDeviceEvents(String _params) {
        JSONObject params = JSONObject.parseObject(_params);
        return accAlarmMonitorService.getAllJson(false);
    }

}
