package com.zkteco.zkbiosecurity.acc.controller;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.remote.AccPersonLevelByLevelRemote;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonLevelByLevelService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonService;
import com.zkteco.zkbiosecurity.acc.utils.AccExportUtil;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonForLevelItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelByLevelExportItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelByLevelItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonListItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * <AUTHOR>
 * @date 2018/3/14 10:54
 */
@Controller
public class AccPersonLevelByLevelController extends ExportController implements AccPersonLevelByLevelRemote {

    @Autowired
    private AccPersonLevelByLevelService accPersonLevelByLevelService;
    @Autowired
    private AccPersonService accPersonService;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private ProgressCache progressCache;

    private Logger logger = LoggerFactory.getLogger(AccPersonLevelByLevelController.class);

    @RequiresPermissions("acc:personLevelByLevel")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/personLevelByLevel/accPersonLevelByLevel");
    }

    @RequiresPermissions("acc:personLevelByLevel:refresh")
    @Override
    public DxGrid list(AccPersonLevelByLevelItem codition) {
        Pager pager = accPersonLevelByLevelService.loadPagerByAuthFilter(request.getSession().getId(), codition,
            getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:personLevelByLevel:refresh")
    @Override
    public DxGrid getLevelPerson(AccPersonForLevelItem condition) {
        Pager pager = accPersonService.getLevelPersonItemsByAuthFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("acc:personLevelByLevel:addPerson")
    @LogRequest(module = "acc_module", object = "common_leftMenu_levelSetByLevel", opType = "pers_common_addPerson",
        requestParams = {"levelName", "personPins"}, opContent = "pers_person_pin")
    @Override
    public ZKResultMsg addPerson(String levelId, String personIds, String deptIds) {
        String clientId = request.getParameter("clientId");
        progressCache.beginProcess(I18nUtil.i18nCode("common_op_startProcessing") + "<br/>");
        if (StringUtils.isNotBlank(deptIds)) {
            personIds = accPersonService.getPersonIdsByDeptIds(deptIds);
        }
        if (StringUtils.isNotBlank(personIds) && StringUtils.isNotBlank(levelId)) {
            try {
                // 对人员ids进行分批处理，避免人员ids过大导致service业务处理产生事务超时问题
                List<String> personArrayIds =
                    accPersonService.splitPersonIds(personIds, AccConstants.LEVEL_SPLIT_COUNT);
                int[] currentCount = {0};
                int[] totalCount = {personArrayIds.size()};
                personArrayIds.forEach(personArrayId -> {
                    // 添加人员权限到数据库
                    accLevelService.addPersonAndTimesByParamIds(personArrayId, levelId);
                    currentCount[0] = currentCount[0] + 1;
                    // 单进度条，只需要设置总进度，当前进度传0即可
                    progressCache.setProcess(new ProcessBean(0, (int)((currentCount[0]) * 100.0 / totalCount[0])),
                        clientId);
                });
            } catch (Exception e) {
                logger.error("addPerson with level error", e);
            }
        }
        progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish") + "<br/>", "",
            I18nUtil.i18nCode("common_op_currProgress"));
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:personLevelByLevel:delPerson")
    @LogRequest(module = "acc_module", object = "common_leftMenu_levelSetByLevel", opType = "pers_common_delPerson",
        requestParams = {"levelName", "personPins"}, opContent = "pers_person_pin")
    @Override
    public ZKResultMsg delPerson(String levelId, String personIds) {
        ZKResultMsg zkResultMsg = accPersonLevelByLevelService.delPerson(levelId, personIds);
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @RequiresPermissions("acc:personLevelByLevel:export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        AccPersonListItem accPersonListItem = new AccPersonListItem();
        setConditionValue(accPersonListItem);
        List<AccPersonListItem> accPersonListItemList = accPersonLevelByLevelService
            .getExportItemList(request.getSession().getId(), accPersonListItem, getBeginIndex(), getEndIndex());
        excelExport(accPersonListItemList, AccPersonListItem.class);
    }

    @LogRequest(module = "acc_module", object = "common_leftMenu_levelSetByLevel",
        opType = "acc_level_exportLevelPerson", opContent = "acc_level_exportLevelPerson")
    @RequiresPermissions("acc:personLevelByLevel:export")
    @Override
    public void exportLevelPerson(HttpServletRequest request, HttpServletResponse response) {
        // 验证用户登录密码
        if (isNeedValid("pwd")) {
            try {
                String loginPwd = this.request.getParameter("loginPwd");
                boolean ret = accLevelService.verifyLoginPwd(request.getSession().getId(), loginPwd);
                if (!ret) {
                    throw new ZKBusinessException("auth_user_pwdIncorrect");
                }
            } catch (ZKBusinessException e) {
                throw e;
            } catch (Exception e) {
                this.log.error("", e);
            }
        }
        String levelIds = request.getParameter("levelIds");
        AccPersonLevelByLevelExportItem accLevelPersonItem = new AccPersonLevelByLevelExportItem();
        accLevelPersonItem.setLevelIdsIn(levelIds);
        List<AccPersonLevelByLevelExportItem> accLevelPersonItemList =
            accPersonService.getExportLevelPersonItemListByAuthFilter(accLevelPersonItem, getBeginIndex(),
                getEndIndex(), request.getSession().getId());
        excelExport(accLevelPersonItemList, AccPersonLevelByLevelExportItem.class);
    }

    @LogRequest(module = "acc_module", object = "common_leftMenu_levelSetByLevel",
        opType = "acc_level_importLevelPerson", opContent = "acc_level_importLevelPerson")
    @RequiresPermissions("acc:personLevelByLevel:import")
    @Override
    public ZKResultMsg importLevelPerson(MultipartFile upload) throws IOException {
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_uploadFileSuccess") + "<br/>"));
            List<AccPersonLevelByLevelExportItem> itemList =
                AccExportUtil.excelImport(upload.getInputStream(), AccPersonLevelByLevelExportItem.class);
            progress += 10;
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_resolutionComplete") + "<br/>"));
            return I18nUtil.i18nMsg(accLevelService.importAccPersonData(itemList));
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                    + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                    new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("Import Person Info Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
    }
}
