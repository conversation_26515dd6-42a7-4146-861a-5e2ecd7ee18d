/*
 * @author:	GenerationTools
 * @date:	2018-03-13 下午05:00
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import com.zkteco.zkbiosecurity.acc.remote.AccAuxInRemote;
import com.zkteco.zkbiosecurity.acc.service.AccAuxInService;
import com.zkteco.zkbiosecurity.acc.service.AccChannelService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.acc.vo.AccAuxInItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import java.util.Arrays;

/**
 * @author:	GenerationTools
 * @date:	2018-03-13 下午05:00
 */
@Controller
public class AccAuxInController extends BaseController implements AccAuxInRemote {
	@Autowired
	private AccAuxInService accAuxInService;
	@Autowired
	private AccDeviceOptionService accDeviceOptionService;
	@Autowired
	private AccChannelService accChannelService;

	@RequiresPermissions("acc:auxIn")
	@Override
	public ModelAndView index() {
		return new ModelAndView("acc/auxIn/accAuxIn");
	}

	@RequiresPermissions("acc:auxIn:edit")
	@Override
	public ModelAndView edit(String id) {
		if(StringUtils.isNotBlank(id)) {
			AccAuxInItem accAuxInItem = accAuxInService.getItemById(id);
			request.setAttribute("item", accAuxInItem);
			request.setAttribute("supportAuxInTimezone", accDeviceOptionService.getAccSupportFunListVal(accAuxInItem.getDevId(), 8));
		}
		return new ModelAndView("acc/auxIn/editAccAuxIn");
	}

	@RequiresPermissions("acc:auxIn:edit")
    @LogRequest(module="acc_module",object="common_leftMenu_auxIn",opType="common_op_edit",requestParams= {"name"},opContent="common_name")
	@Override
	public ZKResultMsg save(AccAuxInItem item) {
		ZKResultMsg res = new ZKResultMsg();
		accAuxInService.saveItem(item);
		return I18nUtil.i18nMsg(res);
	}

	@RequiresPermissions("acc:auxIn:refresh")
	@Override
	public DxGrid list(AccAuxInItem codition) {
		Pager pager = accAuxInService.loadPagerByAuthFilter(request.getSession().getId(), codition, getPageNo(), getPageSize());
		return GridUtil.convert(pager, codition.getClass());
	}

	@RequiresPermissions("acc:auxIn:del")
	@Override
	public ZKResultMsg del(String ids) {
		accAuxInService.deleteByIds(ids);
		return I18nUtil.i18nMsg(new ZKResultMsg());
	}
	
	@Override
	public boolean isExist(AccAuxInItem item) {
        return accAuxInService.isExist(item);
	}

	@Override
	public ModelAndView getChannelByEntityId() {
		String entityId = request.getParameter("entityId");
		request.setAttribute("entityId", entityId);
		request.setAttribute("entityName", "AccAuxIn");
		// 获取已绑定当前实体的摄像头（通道）
		String channel2EntityIds = accChannelService.getBindChannelIds(Arrays.asList(entityId), "AccAuxIn");
		request.setAttribute("value", channel2EntityIds);
		return new ModelAndView("acc/auxIn/accAuxInSelectChannelContent");
	}

	@RequiresPermissions("acc:auxIn:bindChannel")
	@LogRequest(module="acc_module",object="common_leftMenu_auxIn",opType="common_vid_bindOrUnbindChannel",requestParams= {},opContent="common_vid_bindOrUnbindChannel")
	@Override
	public ZKResultMsg bindOrUnbindChannel() {
		String entityId = request.getParameter("entityId");
		String entityName = request.getParameter("entityName");
		String channelIds = request.getParameter("channelIds");
		String deviceSn = request.getParameter("deviceSn");
        ZKResultMsg ret = accChannelService.bindOrUnbindChannel(deviceSn, channelIds, entityName, entityId);
		return I18nUtil.i18nMsg(ret);
	}
}