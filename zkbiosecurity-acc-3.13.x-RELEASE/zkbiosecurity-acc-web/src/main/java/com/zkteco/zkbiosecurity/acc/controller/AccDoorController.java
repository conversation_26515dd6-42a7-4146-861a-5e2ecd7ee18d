/*
 * @author: GenerationTools
 * 
 * @date: 2018-03-03 上午11:59 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.remote.AccDoorRemote;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceOptionItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 门禁门controller
 *
 * @author: GenerationTools
 * @date: 2018-03-03 上午11:59
 */
@Controller
public class AccDoorController extends ExportController implements AccDoorRemote {

    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccRTMonitorService accRTMonitorService;
    @Autowired
    private AccBaseDictionaryService accBaseDictionaryService;
    @Autowired
    private AccDeviceVerifyModeService accDeviceVerifyModeService;
    @Autowired
    private AccLevelService accLevelService;

    @RequiresPermissions("acc:door")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/door/accDoor");
    }

    @RequiresPermissions("acc:door:edit")
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            AccDoorItem item = accDoorService.getItemById(id);
            request.setAttribute("item", item);
            boolean delayOpenDoorFunOn = accDeviceOptionService.isSupportFun(item.getDeviceSn(), "DelayOpenDoorFunOn");// 门开延时
            request.setAttribute("delayOpenDoorFunOn", delayOpenDoorFunOn);
            boolean userOpenDoorDelayFunOn =
                accDeviceOptionService.isSupportFun(item.getDeviceSn(), "UserOpenDoorDelayFunOn");// 是否支持残疾人辅助开门时间
            request.setAttribute("userOpenDoorDelayFunOn", userOpenDoorDelayFunOn);
            boolean multiCardInterTimeFunOn =
                accDeviceOptionService.isSupportFun(item.getDeviceSn(), "MultiCardInterTimeFunOn");// 是否支多人开门组操作间隔时间设置
            request.setAttribute("multiCardInterTimeFunOn", multiCardInterTimeFunOn);
            boolean longLockDelay = !accDoorService.isNewAccessControlDevice(item.getDeviceId());
            request.setAttribute("longLockDelay", longLockDelay);
            boolean inputIOSettingFunOn = accDeviceOptionService.isSupportFunList(item.getDeviceSn(), 8);
            request.setAttribute("inputIOSettingFunOn", inputIOSettingFunOn);
            boolean supportWGDataProcess = accDeviceOptionService.isSupportFunList(item.getDeviceSn(), 33);
            request.setAttribute("supportWGDataProcess", supportWGDataProcess);
            // 是否支持允许超级用户在门锁定时通行
            boolean allowSUAccessLock = accDeviceOptionService.isSupportFunList(item.getDeviceSn(), 48);
            request.setAttribute("allowSUAccessLock", allowSUAccessLock);
            request.setAttribute("editPage", true);
            // 设备是否有新验证方式参数 NewVFStyles
            AccDeviceOptionItem newVFStylesItem =
                accDeviceOptionService.getDevOptValueBySnAndName(item.getDeviceSn(), "NewVFStyles");
            if (Objects.nonNull(newVFStylesItem) && StringUtils.isNotBlank(newVFStylesItem.getValue())
                && !"0".equals(newVFStylesItem.getValue())) {
                // 获取门对应新验证方式编号,用于前端回填显示
                String newVFModeNos =
                    accDeviceVerifyModeService.getVerifyModeNosByNewVFStyles(item.getVerifyMode() + "");
                if (StringUtils.isNotBlank(newVFModeNos)) {
                    // 新验证方式逻辑关系,0-或,1-与,与关系才有逻辑位
                    String verifyStyleLogic =
                        String.valueOf(AccConstants.NEW_VERIFY_MODE_LOGIC).equals(newVFModeNos.split(",")[0]) ? "1"
                            : "0";
                    request.setAttribute("verifyStyleLogic", verifyStyleLogic);
                    request.setAttribute("newVFStyles", newVFModeNos);
                }
            }
            request.setAttribute("supportDoorPassword",
                accDeviceOptionService.isSupportFun(item.getDeviceSn(), "cmd.data.update.doorPassword"));
            if (Short.valueOf(item.getDevMachineType()) == AccConstants.DEVICE_ACCESS_CONTROL) {
                // request.setAttribute("masterSlave", accDeviceOptionService.getMasterSlave(item.getDeviceSn()));
                return new ModelAndView("acc/door/editAccDoorACD");
            }
        }
        return new ModelAndView("acc/door/editAccDoor");
    }

    @RequiresPermissions("acc:door:edit")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_door", opType = "common_op_edit",
        requestParams = {"name"}, opContent = "acc_door_name")
    @Override
    public ZKResultMsg save(AccDoorItem item, @RequestParam(required = false) String applyTo) {
        ZKResultMsg res = new ZKResultMsg();
        accDoorService.saveItem(item, applyTo);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("acc:door:refresh")
    @Override
    public DxGrid list(AccDoorItem codition) {
        Pager pager =
            accDoorService.loadPagerByAuthFilter(request.getSession().getId(), codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions({"acc:door:openDoor", "acc:door:closeDoor", "acc:door:cancelAlarm", "acc:door:normalOpenDoor",
        "acc:door:lockDoor", "acc:door:unLockDoor", "acc:door:enableNormalOpenDoor", "acc:door:disableNormalOpenDoor"})
    @Override
    public ModelAndView getDoorIds(String ids) {
        String[] idArr = ids.split(",");
        String type = request.getParameter("type");
        Map<String, String> retMap = accDoorService.getDoorIds(idArr, type);
        request.setAttribute("retIds", retMap.get("retIds"));
        request.setAttribute("doorsName", retMap.get("doorsName"));
        request.setAttribute("disabledDoorsName", retMap.get("disabledDoorsName"));
        request.setAttribute("offlineDoorsName", retMap.get("offlineDoorsName"));
        request.setAttribute("notSupportDoorsName", retMap.get("notSupportDoorsName"));
        request.setAttribute("type", type);

        return new ModelAndView("acc/door/opAccDoor");
    }

    @Override
    public ZKResultMsg getVerifyMode(String deviceId) {
        List<SelectItem> selectItemList = accDoorService.getVerifyMode(deviceId);
        return new ZKResultMsg(selectItemList);
    }

    @Override
    public ZKResultMsg getWiegandFmtList() {
        List<SelectItem> selectItemList = accDoorService.getWiegandFmtList();
        return new ZKResultMsg(selectItemList);
    }

    @Override
    public boolean validGlobalApb(String doorId) {
        return accDoorService.validGlobalApb(doorId);
    }

    @Override
    public boolean isExist(String name) {
        return accDoorService.isExist(name);
    }

    @Override
    public boolean checkPwd(String forcePwd) {
        return accDoorService.checkPwd(forcePwd);
    }

    @Override
    public ZKResultMsg getAccDeviceOpt(String deviceId) {
        ZKResultMsg res = new ZKResultMsg();
        Map<String, Object> devOptMap = accDoorService.getAccDeviceOpt(deviceId);
        res.setData(devOptMap);
        return res;
    }

    @RequiresPermissions("acc:door:enable")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_door", opType = "common_enable", requestParams = {"name"},
        opContent = "acc_door_name")
    @Override
    public ZKResultMsg enable(String ids) {
        ZKResultMsg res = accDoorService.enable(ids);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("acc:door:disable")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_door", opType = "common_disable",
        requestParams = {"name"}, opContent = "acc_door_name")
    @Override
    public ZKResultMsg disable(String ids) {
        ZKResultMsg res = new ZKResultMsg();
        accDoorService.disable(ids);
        return I18nUtil.i18nMsg(res);
    }

    /**
     *
     * 远程开门
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:34:40
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "acc_leftMenu_door", opType = "acc_eventNo_8", requestParams = "name",
        opContent = "acc_door_name")
    @RequiresPermissions("acc:door:openDoor")
    public ZKResultMsg openDoor(String ids, String openInterval) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateDoor("openDoor", openInterval, ids);
        return dealResultData(dataMap);
    }

    /**
     *
     * 远程关门
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:36:52
     * @return
     */
    @Override
    @RequiresPermissions("acc:door:closeDoor")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_door", opType = "acc_eventNo_9", requestParams = "name",
        opContent = "acc_door_name")
    public ZKResultMsg closeDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateDoor("closeDoor", null, ids);
        return dealResultData(dataMap);
    }

    /**
     *
     * 取消报警
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:37:19
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "acc_leftMenu_door", opType = "acc_eventNo_7", requestParams = "name",
        opContent = "acc_door_name")
    @RequiresPermissions("acc:door:cancelAlarm")
    public ZKResultMsg cancelAlarm(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateDoor("cancelAlarm", null, ids);
        return dealResultData(dataMap);
    }

    /**
     *
     * 远程锁定
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:37:34
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "acc_leftMenu_door", opType = "acc_newEventNo_233",
        requestParams = "name", opContent = "acc_door_name")
    @RequiresPermissions("acc:door:lockDoor")
    public ZKResultMsg lockDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateDoor("lockDoor", null, ids);
        return dealResultData(dataMap);
    }

    /**
     *
     * 远程解锁
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:37:49
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "acc_leftMenu_door", opType = "acc_newEventNo_234",
        requestParams = "name", opContent = "acc_door_name")
    @RequiresPermissions("acc:door:unLockDoor")
    public ZKResultMsg unLockDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateDoor("unLockDoor", null, ids);
        return dealResultData(dataMap);
    }

    /**
     *
     * 远程常开
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:38:08
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "acc_leftMenu_door", opType = "acc_rtMonitor_remoteNormalOpen",
        requestParams = "name", opContent = "acc_door_name")
    @RequiresPermissions("acc:door:normalOpenDoor")
    public ZKResultMsg normalOpenDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateDoor("normalOpenDoor", null, ids);
        return dealResultData(dataMap);
    }

    /**
     *
     * 启用当天常开时间段
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:38:42
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "acc_leftMenu_door", opType = "acc_eventNo_11", requestParams = "name",
        opContent = "acc_door_name")
    @RequiresPermissions("acc:door:enableNormalOpenDoor")
    public ZKResultMsg enableNormalOpenDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateDoor("enableNormalOpenDoor", null, ids);
        return dealResultData(dataMap);
    }

    /**
     *
     * 禁用当天常开时间段
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:39:15
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "acc_leftMenu_door", opType = "acc_eventNo_10", requestParams = "name",
        opContent = "acc_door_name")
    @RequiresPermissions("acc:door:disableNormalOpenDoor")
    public ZKResultMsg disableNormalOpenDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateDoor("disableNormalOpenDoor", null, ids);
        return dealResultData(dataMap);
    }

    /**
     * @Description: 处理返回数据
     * <AUTHOR>
     * @date 2018/6/6 14:54
     * @param dataMap
     * @return
     */
    private ZKResultMsg dealResultData(Map<String, String> dataMap) {
        String cmdIdData = dataMap.get("cmdId");
        String msg = "";
        ZKResultMsg resultMsg = new ZKResultMsg();
        if ("true".equals(dataMap.get("notExistDev"))) {
            msg = I18nUtil.i18nCode("common_dev_opFaileAndReason") + I18nUtil.i18nCode("common_dev_notExistDev");
            resultMsg.setMsg(msg);
            return resultMsg;
        }
        if (!"".equals(cmdIdData)) {
            for (String cmdData : cmdIdData.split(",")) {
                String cmdId = cmdData.split("=")[0];
                String doorName = cmdData.split("=")[1];
                Map<String, String> resultMap = accDeviceService.getCmdResultById(Long.valueOf(cmdId), 20);
                if (Objects.isNull(resultMap)) {
                    msg += doorName + "," + I18nUtil.i18nCode("common_op_failed") + ";";
                } else {
                    Integer ret = Integer.valueOf(resultMap.get("result"));
                    if (Objects.isNull(ret)) {
                        msg += doorName + "," + I18nUtil.i18nCode("common_op_failed") + ";";
                    } else if (ret < 0) {
                        String failedInfo = I18nUtil.i18nCode(accBaseDictionaryService.getCommReason(ret));
                        msg += doorName + "," + I18nUtil.i18nCode("common_dev_opFaileAndReason") + failedInfo + ";";
                    }
                }
            }
        }
        if (!"".equals(dataMap.get("offline"))) {
            for (String doorName : dataMap.get("offline").split(",")) {
                msg += doorName + "," + I18nUtil.i18nCode("common_dev_offlinePrompt") + ";";
            }
        }
        if (!"".equals(dataMap.get("notSupport"))) {
            for (String doorName : dataMap.get("notSupport").split(",")) {
                msg += doorName + "," + I18nUtil.i18nCode("acc_dev_devNotSupportFunction") + ";";
            }
        }
        if (!"".equals(msg)) {
            resultMsg.setRet("400");
            resultMsg.setMsg(msg);
        }
        resultMsg.setData("");
        return resultMsg;
    }

    // 验证用户登录密码
    public void checkUserPwd() {
        if (isNeedValid("pwd")) {
            try {
                String loginPwd = this.request.getParameter("loginPwd");
                boolean ret = accLevelService.verifyLoginPwd(request.getSession().getId(), loginPwd);
                if (!ret) {
                    throw new ZKBusinessException("auth_user_pwdIncorrect");
                }
            } catch (ZKBusinessException e) {
                throw e;
            } catch (Exception e) {
                this.log.error("", e);
            }
        }
    }
}