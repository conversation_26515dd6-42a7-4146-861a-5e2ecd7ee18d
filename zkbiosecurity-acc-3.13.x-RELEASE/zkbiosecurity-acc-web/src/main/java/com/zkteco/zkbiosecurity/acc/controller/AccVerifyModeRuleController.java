/*
 * @author: GenerationTools
 * 
 * @date: 2018-03-14 下午03:02 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.remote.AccVerifyModeRuleRemote;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.acc.service.AccTimeSegService;
import com.zkteco.zkbiosecurity.acc.service.AccVerifyModeRuleService;
import com.zkteco.zkbiosecurity.acc.vo.AccDoorVerifyModeRuleItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTimeSegItem;
import com.zkteco.zkbiosecurity.acc.vo.AccVerifyModeRuleItem;
import com.zkteco.zkbiosecurity.acc.vo.AccVerifyModeRuleSelectDoorItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: GenerationTools
 * @date: 2018-03-14 下午03:02
 */
@Controller
public class AccVerifyModeRuleController extends BaseController implements AccVerifyModeRuleRemote {
    @Autowired
    private AccVerifyModeRuleService accVerifyModeRuleService;
    @Autowired
    private AccTimeSegService accTimeSegService;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccDeviceService accDeviceService;

    @RequiresPermissions("acc:verifyModeRule")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/verifyModeRule/accVerifyModeRule");
    }

    @RequiresPermissions({"acc:verifyModeRule:add", "acc:verifyModeRule:edit"})
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            AccVerifyModeRuleItem item = accVerifyModeRuleService.getItemById(id);
            request.setAttribute("item", item);
            // 不支持都为旧验证方式
            request.setAttribute("newVerifyMode", item.getNewVerifyMode() != null && item.getNewVerifyMode() == 1
                ? AccConstants.IS_NEW_VERIFY_MODE : AccConstants.NOT_NEW_VERIFY_MODE);
        } else {
            // 新增默认选用旧验证方式
            request.setAttribute("newVerifyMode", AccConstants.NOT_NEW_VERIFY_MODE);
        }
        return new ModelAndView("acc/verifyModeRule/editAccVerifyModeRule");
    }

    @RequiresPermissions({"acc:verifyModeRule:add", "acc:verifyModeRule:edit"})
    @LogRequest(module = "acc_module", object = "acc_leftMenu_verifyModeRule", opType = "common_op_edit",
        requestParams = {"name"}, opContent = "acc_common_ruleName")
    @Override
    public ZKResultMsg save(AccVerifyModeRuleItem item) {
        ZKResultMsg res = new ZKResultMsg();
        accVerifyModeRuleService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("acc:verifyModeRule:refresh")
    @Override
    public DxGrid list(AccVerifyModeRuleItem codition) {
        Pager pager = accVerifyModeRuleService.getItemsByPage(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:verifyModeRule:del")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_verifyModeRule", opType = "common_op_del",
        requestParams = {"names"}, opContent = "acc_common_ruleName")
    @Override
    public ZKResultMsg del(String ids) {
        accVerifyModeRuleService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public String valid(String name) {
        AccVerifyModeRuleItem item = accVerifyModeRuleService.getItemByName(name);
        boolean rs = item == null;
        return rs + "";
    }

    @Override
    public ZKResultMsg getTimeSegJSONWithoutRule(String id) {
        if (StringUtils.isBlank(id)) {
            id = "-1";
        }
        ZKResultMsg zkResultMsg = accVerifyModeRuleService.getTimeSegJSONWithoutRule(id);
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @Override
    public ZKResultMsg getTimeSegById(String id) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        AccTimeSegItem accTimeSegItem = accTimeSegService.getItemById(id);
        zkResultMsg.setData(accTimeSegItem);
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @Override
    public ZKResultMsg getVerifyMode() {
        ZKResultMsg zkResultMsg = accVerifyModeRuleService.getVerifyMode();
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @RequiresPermissions("acc:verifyModeRule:refresh")
    @Override
    public DxGrid doorList(AccDoorVerifyModeRuleItem codition) {
        String authAreaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId());
        if (StringUtils.isNotBlank(authAreaIds)) {
            codition.setAuthAreaIdIn(authAreaIds);
        }
        Pager pager = accDoorService.getItemsByPage(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:verifyModeRule:addDoor")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_verifyModeRule", opType = "acc_map_addDoor",
        requestParams = {"verifyModeRuleName", "doorNames"}, opContent = "acc_door_name")
    @Override
    public ZKResultMsg addDoor(String verifyModeRuleId, String doorIds) {
        if (StringUtils.isNotBlank(verifyModeRuleId) && StringUtils.isNotBlank(doorIds)) {
            List<String> doorIdList = new ArrayList<>(Arrays.asList(doorIds.split(",")));
            accVerifyModeRuleService.addDoor(verifyModeRuleId, doorIdList);
        }
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("acc:verifyModeRule:delDoor")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_verifyModeRule", opType = "acc_level_doorDelete",
        requestParams = {"verifyModeRuleName", "doorNames"}, opContent = "acc_door_name")
    @Override
    public ZKResultMsg delDoor(String verifyModeRuleId, String doorIds) {
        accVerifyModeRuleService.delDoor(verifyModeRuleId, doorIds);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public DxGrid selectDoorlist(AccVerifyModeRuleSelectDoorItem condition) {
        String verifyModeRuleId = condition.getVerifyModeRuleId(); // 获取选中的验证方式规则的ID
        String filterDoorIds = accVerifyModeRuleService.getFilterDoorId(verifyModeRuleId);
        if (StringUtils.isBlank(condition.getSelectId())) {
            condition.setSelectId("-1");
        }
        if (condition.getType().equals("noSelected")) {
            if (StringUtils.isNotBlank(filterDoorIds)) {
                condition.setSelectId(condition.getSelectId() + "," + filterDoorIds);
            }
            condition.setSelectDoorIdsNotIn(condition.getSelectId());
        } else if (condition.getType().equals("selected")) {
            condition.setSelectDoorIdsIn(condition.getSelectId());
        }
        condition.setFilterId(verifyModeRuleId); // 根据验证方式规则的ID过滤人员
        String authAreaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId());
        if (StringUtils.isNotBlank(authAreaIds)) {
            condition.setAuthAreaIdIn(authAreaIds);
        }
        condition.setEnabled(true);
        Pager pager = accDoorService.getItemsByPage(condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public ZKResultMsg getTimeSegListWithoutRule(String id) {
        if (StringUtils.isBlank(id)) {
            id = "-1";
        }
        return new ZKResultMsg(accVerifyModeRuleService.getTimeSegListWithoutRule(id));
    }
}