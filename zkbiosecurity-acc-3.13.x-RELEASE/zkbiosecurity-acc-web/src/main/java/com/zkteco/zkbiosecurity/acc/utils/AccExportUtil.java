package com.zkteco.zkbiosecurity.acc.utils;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.beanutils.ConvertUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.filesystem.FileMagic;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import com.zkteco.zkbiosecurity.base.annotation.DateType;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.ExcelUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;

/**
 * 导入工具类
 *
 * <AUTHOR>
 * @date 2023-07-06 18:07
 * @since 1.0.0
 */
public class AccExportUtil {

    private final static Logger loger = LoggerFactory.getLogger(ExcelUtil.class);

    @SuppressWarnings({"unchecked", "rawtypes"})
    @Deprecated
    public static <T> List<T> excelImport(InputStream inputStream, Class<T> cls) {
        Workbook workbook = createImportWorkBook(inputStream);
        Sheet sheet = workbook.getSheetAt(0);
        Field[] fields = cls.getDeclaredFields();
        List<Field> fieldList =
            Arrays.stream(fields).filter(f -> f.getAnnotation(GridColumn.class) != null).collect(Collectors.toList());
        Row columnNames = sheet.getRow(1);
        Date date = null;
        Field fieldStr = null;
        DataFormatter formatter = new DataFormatter();
        List<T> list = new ArrayList();
        try {
            for (Row row : sheet) {
                if (row.getRowNum() > 1) {
                    T t = cls.newInstance();
                    for (Cell cell : row) {
                        Cell columnNameCell = columnNames.getCell(cell.getColumnIndex());
                        if (StringUtils.isEmpty(formatter.formatCellValue(columnNameCell))) {
                            new ZKBusinessException("common_report_columnError");
                        }
                        if (columnNameCell != null) {
                            String columnName = formatter.formatCellValue(columnNameCell);
                            Field field = fieldList.stream().filter(
                                f -> columnName.equals(I18nUtil.i18nCode(f.getAnnotation(GridColumn.class).label())))
                                .findFirst().orElseThrow(() -> new ZKBusinessException("common_report_columnError"));
                            field.setAccessible(true);
                            if (field.getType() == Date.class || field.getType() == Timestamp.class) {
                                DateType dateType = field.getAnnotation(DateType.class);
                                String format = getDateFormt(dateType);
                                if (cell.getCellType() == CellType.NUMERIC) {
                                    if (cell.getNumericCellValue() > 0) {
                                        date = DateUtil.getJavaDate(cell.getNumericCellValue());
                                        if (date == null) {
                                            fieldStr = cls.getDeclaredField(field.getName() + "Str");
                                            fieldStr.setAccessible(true);
                                            fieldStr.set(t, PersConstants.PERSON_DATE_ERROR);
                                        } else {
                                            field.set(t, date);
                                        }
                                    }
                                } else {
                                    if (!StringUtils.isEmpty(formatter.formatCellValue(cell))) {
                                        date = com.zkteco.zkbiosecurity.core.utils.DateUtil
                                            .stringToDate(formatter.formatCellValue(cell), format);
                                        if (date == null) {
                                            fieldStr = cls.getDeclaredField(field.getName() + "Str");
                                            fieldStr.setAccessible(true);
                                            fieldStr.set(t, PersConstants.PERSON_DATE_ERROR);
                                        } else {
                                            field.set(t, date);
                                        }
                                    } else {
                                        fieldStr = cls.getDeclaredField(field.getName() + "Str");
                                        fieldStr.setAccessible(true);
                                        fieldStr.set(t, PersConstants.PERSON_DATE_SETNULL);
                                    }
                                }
                            } else {
                                if (!StringUtils.isEmpty(formatter.formatCellValue(cell))) {
                                    field.set(t,
                                        ConvertUtils.convert(formatter.formatCellValue(cell), field.getType()));
                                }
                            }
                        }
                    }
                    list.add(t);
                }
            }
        } catch (ZKBusinessException e) {
            loger.error("import business error", e);
            throw e;
        } catch (Exception e) {
            loger.error("import error", e);
            throw new ZKBusinessException("common_op_failed");
        }
        return list;
    }

    private static Workbook getImportWorkBook(InputStream inputStream) throws IOException {
        Workbook workbook = null;
        // 输入流必须支持mark/reset方法
        if (!inputStream.markSupported()) {
            inputStream = new BufferedInputStream(inputStream);
        }
        // 使用微软Office文件系统,Excel2003
        if (FileMagic.valueOf(inputStream) == FileMagic.OLE2) {
            workbook = new HSSFWorkbook(inputStream);
        } else if (FileMagic.valueOf(inputStream) == FileMagic.OOXML) {
            workbook = new XSSFWorkbook(inputStream);
        } else {
            throw new ZKBusinessException("auth_license_fileIncorrectFormat");
        }
        return workbook;
    }

    private static Workbook createImportWorkBook(InputStream inputStream) {
        Workbook workbook = null;
        try {
            workbook = getImportWorkBook(inputStream);
        } catch (Exception ex) {
            if (ex instanceof ZKBusinessException) {
                throw new ZKBusinessException(ex.getMessage(), ex);
            } else {
                throw new ZKBusinessException("Open the EXCEL file flow failure!", ex);
            }
        }
        return workbook;
    }

    public static String getDateFormt(DateType dateType) {
        String format = "yyyy-MM-dd HH:mm:ss";
        if (dateType != null) {
            switch (dateType.type()) {
                case "date":
                    format = "yyyy-MM-dd";
                    break;
                case "time":
                    format = "HH:mm:ss";
                    break;
                case "timestamp":
                    format = "yyyy-MM-dd HH:mm:ss";
                    break;
            }
        }
        return format;
    }
}
