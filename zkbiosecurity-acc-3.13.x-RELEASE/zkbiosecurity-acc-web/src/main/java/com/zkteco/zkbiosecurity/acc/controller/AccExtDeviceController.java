package com.zkteco.zkbiosecurity.acc.controller;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.remote.AccExtDeviceRemote;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccExtDeviceService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceSelectItem;
import com.zkteco.zkbiosecurity.acc.vo.AccExtDeviceItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;

@Controller
public class AccExtDeviceController extends BaseController implements AccExtDeviceRemote {
    @Autowired
    private AccExtDeviceService accExtDeviceService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;

    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/extDevice/accExtDevice");
    }

    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            AccExtDeviceItem item = accExtDeviceService.getItemById(id);
            if (item.getDevProtocolType() == null) {
                item.setDevProtocolType((short)1);
            }
            request.setAttribute("item", item);
        }
        return new ModelAndView("acc/extDevice/editAccExtDevice");
    }

    @Override
    public ZKResultMsg save(AccExtDeviceItem item) {
        ZKResultMsg res = new ZKResultMsg();
        if (AccConstants.EXT_BOARD_TYPE_DM10 == item.getExtBoardType()) {
            accExtDeviceService.setDM10(item);
        } else if (AccConstants.EXT_BOARD_TYPE_AUX485 == item.getExtBoardType()) {
            accExtDeviceService.setAUX485(item);
        } else if (AccConstants.EXT_BOARD_TYPE_EX0808 == item.getExtBoardType()) {
            accExtDeviceService.setEX0808(item);
        }
        AccDeviceItem devItem = accDeviceService.getItemById(item.getDevId());
        if (devItem != null) {
            accDeviceService.updateCacheDeviceInfo(devItem);
        }
        return res;
    }

    @Override
    public DxGrid list(AccExtDeviceItem codition) {
        Pager pager = accExtDeviceService.loadPagerByAuthFilter(request.getSession().getId(), codition, getPageNo(),
            getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @Override
    public ZKResultMsg del(String ids) {
        ZKResultMsg res = new ZKResultMsg();
        accExtDeviceService.deleteByIds(ids);
        return res;
    }

    @Override
    public DxGrid selectDeviceList(AccDeviceSelectItem condition) {
        Pager pager = new Pager(getPageNo(), getPageSize());
        pager.setData(new ArrayList<AccDeviceSelectItem>());
        if (condition.getType().equals("noSelected")) {
            String filterIds = condition.getSelectId();
            List<AccDeviceItem> accDeviceItems = accDeviceService.getByCondition(new AccDeviceItem());
            for (AccDeviceItem accDeviceItem : accDeviceItems) {
                // 过滤不支持扩展板功能的设备
                if (!isSupportExtBoardFun(accDeviceItem.getSn())) {
                    if (StringUtils.isBlank(filterIds)) {
                        filterIds = accDeviceItem.getId();
                    } else {
                        filterIds += "," + accDeviceItem.getId();
                    }
                }
            }
            condition.setNotInId(filterIds);
            pager = accDeviceService.getDeviceSelectItem(request.getSession().getId(), condition, getPageNo(),
                getPageSize());
        }
        return GridUtil.convert(pager, condition.getClass());
    }

    /**
     * 根据sn号判断是否支持扩展板功能
     *
     * @param sn
     * @return
     */
    private boolean isSupportExtBoardFun(String sn) {
        return accDeviceOptionService.isSupportFunList(sn, 45) || accDeviceOptionService.isSupportFunList(sn, 46)
            || accDeviceOptionService.isSupportFunList(sn, 47);
    }

    @Override
    public ZKResultMsg getExtBoardTypeSelect(String devId) {
        List<SelectItem> selectItems = new ArrayList<>();
        SelectItem selectItem = null;
        if (accDeviceOptionService.getAccSupportFunListVal(devId, 45)) {
            selectItem = new SelectItem();
            selectItem.setValue(AccConstants.EXT_BOARD_TYPE_DM10 + "");
            selectItem.setText("DM10");
            selectItems.add(selectItem);
        }
        if (accDeviceOptionService.getAccSupportFunListVal(devId, 46)) {
            selectItem = new SelectItem();
            selectItem.setValue(AccConstants.EXT_BOARD_TYPE_AUX485 + "");
            selectItem.setText("AUX485");
            selectItems.add(selectItem);
        }
        if (accDeviceOptionService.getAccSupportFunListVal(devId, 47)) {
            selectItem = new SelectItem();
            selectItem.setValue(AccConstants.EXT_BOARD_TYPE_EX0808 + "");
            selectItem.setText("EX0808");
            selectItems.add(selectItem);
        }
        return new ZKResultMsg(selectItems);
    }

    @Override
    public boolean isExistAlias(String alias) {
        return accExtDeviceService.isExistAlias(alias);
    }

    @Override
    public boolean isExistAddress(Short commAddress, String devId) {
        return accExtDeviceService.isExistAddress(commAddress, devId);
    }

    @Override
    public ZKResultMsg getDevProtocolTypeSelect(String devId) {
        List<SelectItem> selectItems = new ArrayList<>();
        if (StringUtils.isNotBlank(devId)) {
            SelectItem selectItem = null;
            Short selectedType = accExtDeviceService.getDevProtocolType(devId);
            if (selectedType != null && AccConstants.EXT_PROTOCOL_TYPE.containsKey(selectedType)) {
                selectItem = new SelectItem();
                selectItem.setValue(selectedType + "");
                selectItem.setText(AccConstants.EXT_PROTOCOL_TYPE.get(selectedType));
                selectItems.add(selectItem);
                return new ZKResultMsg(selectItems);
            }
            if (accDeviceOptionService.getAccSupportFunListVal(devId, 49)) {
                selectItem = new SelectItem();
                selectItem.setValue(AccConstants.EXT_PROTOCOL_TYPE_OSDP + "");
                selectItem.setText("OSDP");
                selectItems.add(selectItem);
            }
            selectItem = new SelectItem();
            selectItem.setValue(AccConstants.EXT_PROTOCOL_TYPE_ZK485 + "");
            selectItem.setText("ZK485");
            selectItems.add(selectItem);
        }
        return new ZKResultMsg(selectItems);
    }

    @Override
    public ZKResultMsg getExtBoardTypeSelects(String devId, String devProtocolType) {
        List<SelectItem> selectItems = new ArrayList<>();
        if (StringUtils.isNotBlank(devId) && StringUtils.isNotBlank(devProtocolType)) {
            SelectItem selectItem = null;
            if (accDeviceOptionService.getAccSupportFunListVal(devId, 45)
                && AccConstants.EXT_PROTOCOL_TYPE_ZK485.toString().equals(devProtocolType)) {
                selectItem = new SelectItem();
                selectItem.setValue(AccConstants.EXT_BOARD_TYPE_DM10 + "");
                selectItem.setText("DM10");
                selectItems.add(selectItem);
            }
            if (accDeviceOptionService.getAccSupportFunListVal(devId, 46)
                && AccConstants.EXT_PROTOCOL_TYPE_ZK485.toString().equals(devProtocolType)) {
                selectItem = new SelectItem();
                selectItem.setValue(AccConstants.EXT_BOARD_TYPE_AUX485 + "");
                selectItem.setText("AUX485");
                selectItems.add(selectItem);
            }
            if (accDeviceOptionService.getAccSupportFunListVal(devId, 47)) {
                selectItem = new SelectItem();
                selectItem.setValue(AccConstants.EXT_BOARD_TYPE_EX0808 + "");
                selectItem.setText("EX0808");
                selectItems.add(selectItem);
            }
        }
        return new ZKResultMsg(selectItems);
    }
}
