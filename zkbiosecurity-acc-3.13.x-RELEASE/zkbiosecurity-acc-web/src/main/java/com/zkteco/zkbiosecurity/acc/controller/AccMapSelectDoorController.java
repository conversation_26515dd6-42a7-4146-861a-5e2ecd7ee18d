package com.zkteco.zkbiosecurity.acc.controller;

import com.zkteco.zkbiosecurity.acc.remote.AccMapSelectDoorRemote;
import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.acc.service.AccMapService;
import com.zkteco.zkbiosecurity.acc.vo.AccMapSelectDoorItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

@Controller
public class AccMapSelectDoorController extends BaseController implements AccMapSelectDoorRemote {

    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccMapService accMapService;

    @Override
    public DxGrid list(AccMapSelectDoorItem condition) {
        String mapId = request.getParameter("mapId");
        condition.setFilterId(mapId);
        String type = condition.getType();
        String selectId = condition.getSelectId();
        if("noSelected".equals(type))
        {
            condition.setNotInId(selectId);
        }
        else if("selected".equals(type))
        {
            condition.setInId("-1,"+selectId);
        }
        condition.setEnabled(true);
        Pager pager = accDoorService.getSelectDoorItemsByPage(condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }
}
