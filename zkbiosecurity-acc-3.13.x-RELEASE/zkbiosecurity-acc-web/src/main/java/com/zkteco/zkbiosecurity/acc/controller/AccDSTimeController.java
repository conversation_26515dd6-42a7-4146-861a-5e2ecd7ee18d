/*
 * @author: yulong.dai
 * 
 * @date: 2018-02-28 下午02:21 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.remote.AccDSTimeRemote;
import com.zkteco.zkbiosecurity.acc.service.AccDSTimeService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.vo.AccDSTimeItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 门禁夏令时controller
 * 
 * @author: yulong.dai
 * @date: 2018-02-28 下午02:21
 */
@Controller
public class AccDSTimeController extends BaseController implements AccDSTimeRemote {
    @Autowired
    private AccDSTimeService accDSTimeService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private ProgressCache progressCache;

    @RequiresPermissions("acc:dSTime")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/dSTime/accDSTime");
    }

    @RequiresPermissions({"acc:dSTime:add", "acc:dSTime:edit"})
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("tempAccDSTime", accDSTimeService.getItemById(id));
        }
        return new ModelAndView("acc/dSTime/editAccDSTime");
    }

    @RequiresPermissions({"acc:dSTime:add", "acc:dSTime:edit"})
    @LogRequest(module = "acc_module", object = "acc_leftMenu_dSTime", opType = "common_op_edit",
        requestParams = {"name"}, opContent = "common_dsTime_name")
    @Override
    public ZKResultMsg save(AccDSTimeItem item) {
        ZKResultMsg res = new ZKResultMsg();
        accDSTimeService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("acc:dSTime:refresh")
    @Override
    public DxGrid list(AccDSTimeItem codition) {
        Pager pager = accDSTimeService.getItemsByPage(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @RequiresPermissions("acc:dSTime:del")
    @LogRequest(module = "acc_module", object = "acc_leftMenu_dSTime", opType = "common_op_del",
        requestParams = {"name"}, opContent = "common_dsTime_name")
    @Override
    public ZKResultMsg del(String ids) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        if (accDeviceService.getCountByDSTimeId(ids) > 0) {
            resultMsg.setRet("500");
            resultMsg.setMsg("common_dsTime_delete");
        } else {
            accDSTimeService.deleteByIds(ids);
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public ZKResultMsg getDSTimeList() {
        List<SelectItem> selectItemList = Lists.newArrayList();
        List<AccDSTimeItem> accDSTimeItemList = accDSTimeService.getByCondition(new AccDSTimeItem());
        accDSTimeItemList.stream().forEach(dstime -> {
            selectItemList.add(new SelectItem(dstime.getName(), dstime.getId()));
        });
        return I18nUtil.i18nMsg(new ZKResultMsg(selectItemList));
    }

    @Override
    public boolean isExistName(String name) {
        return accDSTimeService.isExistName(name);
    }

    @RequiresPermissions("acc:dSTime:setDSTime")
    @Override
    public ModelAndView getById(String id) {
        request.setAttribute("id", id);
        return new ModelAndView("acc/dSTime/opAccSetDSTime");
    }

    @RequiresPermissions("acc:dSTime:setDSTime")
    @Override
    public ZKResultMsg setDSTime(String dSTimeId, String devIds) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        progressCache.beginProcess(I18nUtil.i18nCode("common_op_startProcessing") + "<br/>");
        List<AccDeviceItem> accDeviceItemList = accDeviceService.getItemByIds(devIds);
        for (int i = 0; i < accDeviceItemList.size(); i++) {
            String devAlias = accDSTimeService.setDSTime(dSTimeId, accDeviceItemList.get(i).getId());
            progressCache.setProcess(new ProcessBean(100, (int)((i + 1) / (float)accDeviceItemList.size() * 100),
                devAlias + ":" + I18nUtil.i18nCode("common_dev_cmdSendSucceed") + "<br/>"));
        }
        progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish"));
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public ZKResultMsg getDSTimeListByTimeZone(String timeZone) {
        final ZKResultMsg res = ZKResultMsg.successMsg();
        if (StringUtils.isNotBlank(timeZone)) {
            final AccDSTimeItem condition = new AccDSTimeItem();
            condition.setTimeZone(timeZone);
            final List<AccDSTimeItem> items = accDSTimeService.getByCondition(condition);
            for (AccDSTimeItem item : items) {
                item.setName(I18nUtil.i18nCode(item.getName()));
            }
            res.setData(items);
        } else {
            res.setData(new ArrayList<>());
        }
        return res;
    }

    @Override
    public ZKResultMsg hasDSTimeByTimeZone(String timeZone) {
        final ZKResultMsg res = ZKResultMsg.successMsg();
        if (StringUtils.isNotBlank(timeZone)) {
            res.setData(accDSTimeService.isExistByTimeZone(timeZone));
        } else {
            res.setData(false);
        }
        return res;
    }

    @Override
    public ModelAndView getDSTimeSelectView(String timeZone, String dsTime) {
        request.setAttribute("dsTime", dsTime);
        request.setAttribute("timeZone", timeZone);
        request.setAttribute("editPage", true);
        return new ModelAndView("acc/dSTime/selectDSTime");
    }
}