package com.zkteco.zkbiosecurity.acc.api.controller;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.api.vo.AccApiLevelItem;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.vo.AccLevelItem;
import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/11/1 16:46
 */
@Controller
@RequestMapping(value = {"/api/v2/accLevel"})
@Slf4j
@Api(tags = "AccLevel", description = "acc level")
public class AccApiV2LevelController {
    @Autowired
    private AccLevelService accLevelService;

    /**
     * 分页获取权限组信息
     *
     * @param pageNo
     * @param pageSize
     * @return
     * @auther lambert.li
     * @date 2018/11/12 18:49
     */
    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET, produces = "application/json")
    @ApiOperation(value = "Get AccLevel List", notes = "Return AccLevel List", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"pageNo", "pageSize"})
    public ApiResultMessage list(@RequestParam(name = "pageNo") Integer pageNo,
        @RequestParam(name = "pageSize") Integer pageSize) {
        ApiResultMessage rs = new ApiResultMessage();
        if (pageNo == null || pageSize == null) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE,
                I18nUtil.i18nCode("acc_api_parameterValueCannotBeNull"));
        }
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.failedMessage(AccConstants.API_WRONG_PAGE);
        }
        List<AccApiLevelItem> apiAccLevels = Lists.newArrayList();
        Pager pager = accLevelService.getItemsByPage(new AccLevelItem(), pageNo - 1, pageSize);
        List<AccLevelItem> accLevelItemList = (List<AccLevelItem>)pager.getData();
        if (!accLevelItemList.isEmpty()) {
            accLevelItemList.forEach(accLevelItem -> {
                AccApiLevelItem accApiLevelItem = AccApiLevelItem.createApiLevel(accLevelItem);
                if (accApiLevelItem != null) {
                    apiAccLevels.add(accApiLevelItem);
                }
            });
        }
        pager.setData(apiAccLevels);
        rs.setData(pager);
        return rs;
    }

    /**
     * 根据Id获取权限组信息
     *
     * @param id
     * @return
     * @auther lambert.li
     * @date 2018/11/12 17:05
     */
    @ApiOperation(value = "Get AccLevel By Id", notes = "Return AccLevel Object", response = ApiResultMessage.class)
    @RequestMapping(value = "/getById", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"id"})
    public ApiResultMessage getById(@RequestParam(required = false) String id) {
        if (StringUtils.isBlank(id)) {
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_LEVELIDNOTNULL,
                I18nUtil.i18nCode("acc_api_levelIdNotNull"));
        }
        AccLevelItem accLevel = accLevelService.getItemById(id);
        AccApiLevelItem accApiLevelItem = AccApiLevelItem.createApiLevel(accLevel);
        return ApiResultMessage.successMessage(accApiLevelItem);
    }

    /**
     * 根据权限组名称获取权限组信息
     *
     * @param name
     * @return
     * @auther lambert.li
     * @date 2018/11/12 18:48
     */
    @ApiOperation(value = "Get AccLevel By Name", notes = "Return AccLevel Object", response = ApiResultMessage.class)
    @RequestMapping(value = "/getByName", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"name"})
    public ApiResultMessage getByName(@RequestParam String name) {
        if (StringUtils.isBlank(name)) {
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_LEVELNAMENOTNULL,
                I18nUtil.i18nCode("acc_levelImport_nameNotNull"));
        }
        AccLevelItem accLevelItem = accLevelService.getItemByName(name);
        AccApiLevelItem accApiLevelItem = AccApiLevelItem.createApiLevel(accLevelItem);
        return ApiResultMessage.successMessage(accApiLevelItem);
    }
}
