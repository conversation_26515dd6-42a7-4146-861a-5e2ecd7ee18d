package com.zkteco.zkbiosecurity.acc.dhx;

import com.zkteco.zkbiosecurity.acc.service.AccReaderService;
import com.zkteco.zkbiosecurity.core.utils.ShowGridColumn;
import com.zkteco.zkbiosecurity.core.utils.SpringContextUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AccShowBindChannel implements ShowGridColumn {

    @Autowired
    private AccReaderService accReaderService;

    @Override
    public boolean isShow(Object obj) {
        return accReaderService.isExistVid();
    }

    /**
     * 用于前端html反射调用是否显示视频相关设置（方法中用到的bean需求通过SpringContextUtil取）
     *
     * @return
     */
    public boolean isShow() {
        return SpringContextUtil.getBean(AccReaderService.class).isExistVid();
    }

    public boolean accMapIsShow() {
        return SpringContextUtil.getBean(AccReaderService.class).accMapIsShowVid();
    }
}
