package com.zkteco.zkbiosecurity.acc.controller;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.remote.AccParamRemote;
import com.zkteco.zkbiosecurity.acc.service.AccLinkageService;
import com.zkteco.zkbiosecurity.acc.service.AccParamService;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionAutoExportService;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.guard.foldex.utils.FoldexUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

@Controller
public class AccParamController extends BaseController implements AccParamRemote {
    @Autowired
    private AccParamService accParamService;
    @Autowired
    private AccLinkageService accLinkageService;
    @Autowired
    private AccTransactionAutoExportService accTransactionAutoExportService;

    @RequiresPermissions("acc:param:refresh")
    @Override
    public ModelAndView index() {
        Map<String, String> accParams = accParamService.getAccParams();
        String receiver = accParams.get("acc.receiver");
        if (receiver != null && !"".equals(receiver.trim())) {
            accParams.put("acc.receiver", FoldexUtil.decryptByRandomSey(receiver));
        }
        String smsReceiver = accParams.get("acc.smsReceiver");
        if (smsReceiver != null && !"".equals(smsReceiver.trim())) {
            accParams.put("acc.smsReceiver", FoldexUtil.decryptByRandomSey(smsReceiver));
        }
        String autoExportEmail = accParams.get("acc.autoExportEmail");
        if (autoExportEmail != null && !"".equals(autoExportEmail.trim())) {
            accParams.put("acc.autoExportEmail", FoldexUtil.decryptByRandomSey(autoExportEmail));
        }
        request.setAttribute("accParams", accParams);
        request.setAttribute("showSMS", accLinkageService.checkShowSMS());
        // 读取参数并校验，校验失败则代表自动导出功能不启用
        // add by colin 2020-5-9 11:44:32
        request.setAttribute("transactionAutoExportEnable",
            StringUtils.isNotBlank(accTransactionAutoExportService.createCron()));
        return new ModelAndView("acc/param/accParams");
    }

    @RequiresPermissions("acc:param:edit")
    @LogRequest(module = "acc_module", object = "common_leftMenu_paramSet", opType = "common_leftMenu_paramSet",
        opContent = "common_leftMenu_paramSet")
    @Override
    public ZKResultMsg save(@RequestParam Map<String, String> params) {
        ZKResultMsg res = new ZKResultMsg();
        String downNewLogHours = request.getParameter("down_event_hours");
        params.put("acc.downNewlog", downNewLogHours);
        StringBuffer StrBuf = new StringBuffer("0 0 ").append(downNewLogHours).append(" * * ?");
        params.put("acc.downNewLogExpression", StrBuf.toString());
        String receiver = request.getParameter("mail");
        if (receiver != null) {
            params.put("acc.receiver", receiver.replaceAll(";", ","));// 由于前端提交的数据可能是用分号来分割的，后台统一使用逗号分割存入redis
        } else { // 报警监控收件人邮箱全部删掉后
            params.put("acc.receiver", "");// 收件人邮箱
        }

        // 报警事件通过手机号码发送短信
        String smsReceiver = request.getParameter("smsReceiver");
        if (StringUtils.isNotBlank(smsReceiver)) {
            // 由于前端提交的数据可能是用分号来分割的，后台统一使用逗号分割存入redis
            params.put("acc.smsReceiver", smsReceiver.replaceAll(";", ","));
        } else {
            // 报警监控收件人电话号码全部删掉后
            // 收件人电话号码
            params.put("acc.smsReceiver", "");
        }
        accParamService.saveItem(params);
        // add by colin 2019-11-7 18:09:05
        // 参数修改后通知自动导出模块重新设置定时处理时间
        accTransactionAutoExportService.changeAutoProcessorTime();
        return I18nUtil.i18nMsg(res);
    }
}
