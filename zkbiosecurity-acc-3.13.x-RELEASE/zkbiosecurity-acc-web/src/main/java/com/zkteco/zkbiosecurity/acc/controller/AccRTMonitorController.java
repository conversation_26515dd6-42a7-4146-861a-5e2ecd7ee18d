/*
 * @author: GenerationTools
 * 
 * @date: 2018-03-14 下午02:44 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.remote.AccRTMonitorRemote;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.AccRTMonitorItem;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 实时监控
 * 
 * @author: GenerationTools
 * @date: 2018-03-14 下午02:44
 */
@Controller
public class AccRTMonitorController extends ExportController implements AccRTMonitorRemote {

    @Autowired
    private AccRTMonitorService accRTMonitorService;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccAuxInService accAuxInService;
    @Autowired
    private AccAuxOutService accAuxOutService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccBaseDictionaryService accBaseDictionaryService;
    @Autowired
    private AccParamService accParamService;
    @Autowired
    private AccLevelService accLevelService;

    @Override
    @RequiresPermissions({"acc:rtMonitor", "acc:rtMonitor:browse"})
    public ModelAndView index() {
        // 视频插件加载版本,解决ocx取不到值
        String loadPlugins = accParamService.getVidParams().get("vid.loadPlugins");
        String accIsShowPhoto = accParamService.getParamValByName("acc.isShowPhoto");
        String accIsShowSound = accParamService.getParamValByName("acc.isShowSound");
        String personPhotoMaxHeight = accParamService.getParamValByName("acc.personPhotoMaxHeight");
        request.setAttribute("accIsShowPhoto", accIsShowPhoto);
        request.setAttribute("accIsShowSound", accIsShowSound);
        request.setAttribute("personPhotoMaxHeight", personPhotoMaxHeight);
        request.setAttribute("loadPlugins", loadPlugins);

        String pinEncryptMode = accParamService.getParamValByName("pers.pin.encryptMode");
        String nameEncryptMode = accParamService.getParamValByName("pers.name.encryptMode");
        String cardEncryptMode = accParamService.getParamValByName("pers.cardNo.encryptMode");
        request.setAttribute("pinEncryptMode", pinEncryptMode);
        request.setAttribute("nameEncryptMode", nameEncryptMode);
        request.setAttribute("cardEncryptMode", cardEncryptMode);
        return new ModelAndView("acc/rtMonitor/accRTMonitor");
    }

    @Override
    public DxGrid list(AccRTMonitorItem codition) {
        return null;
    }

    /**
     *
     * 获取所有门设备
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:36:11
     * @return
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    @Override
    public List getAllDoor() {
        List data = new ArrayList();
        ZKResultMsg resultMsg =
            accDoorService.getDoorStatus(request.getSession().getId(), I18nUtil.i18nCode("common_paging_loading"));
        JSONArray doorStates = (JSONArray)resultMsg.getData();
        if (Objects.nonNull(doorStates) && doorStates.size() > 0) {
            doorStates.forEach(door -> data.add(door));
        }
        return data;
    }

    /**
     *
     * 获取所有辅助输入设备
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:36:30
     * @return
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    @Override
    public List getAllAuxIn() {
        List data = new ArrayList();
        ZKResultMsg resultMsg = accAuxInService.getAuxInStatus(request.getSession().getId());
        JSONArray auxInStatus = (JSONArray)resultMsg.getData();
        if (Objects.nonNull(auxInStatus) && auxInStatus.size() > 0) {
            auxInStatus.stream().forEach(auxIn -> data.add(auxIn));
        }
        return data;
    }

    /**
     *
     * 获取所有辅助输出设备
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:36:41
     * @return
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    @Override
    public List getAllAuxOut() {
        List data = new ArrayList();
        ZKResultMsg resultMsg = accAuxOutService.getAuxOutStatus(request.getSession().getId());
        JSONArray auxOutStatus = (JSONArray)resultMsg.getData();
        if (Objects.nonNull(auxOutStatus) && auxOutStatus.size() > 0) {
            auxOutStatus.stream().forEach(auxOut -> data.add(auxOut));
        }
        return data;
    }

    /**
     *
     * 远程开门
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:34:40
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "common_leftMenu_rtMonitor", opType = "acc_eventNo_8",
        requestParams = "name", opContent = "acc_door_name")
    @RequiresPermissions("acc:rtMonitor:openDoor")
    public ZKResultMsg openDoor(String ids, String openInterval) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateDoor("openDoor", openInterval, ids);
        return dealResultData(dataMap);
    }

    /**
     *
     * 远程关门
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:36:52
     * @return
     */
    @Override
    @RequiresPermissions("acc:rtMonitor:closeDoor")
    @LogRequest(module = "acc_module", object = "common_leftMenu_rtMonitor", opType = "acc_eventNo_9",
        requestParams = "name", opContent = "acc_door_name")
    public ZKResultMsg closeDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateDoor("closeDoor", null, ids);
        return dealResultData(dataMap);
    }

    /**
     *
     * 取消报警
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:37:19
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "common_leftMenu_rtMonitor", opType = "acc_eventNo_7",
        requestParams = "name", opContent = "acc_door_name")
    @RequiresPermissions({"acc:rtMonitor:cancelAlarm"})
    public ZKResultMsg cancelAlarm(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateDoor("cancelAlarm", null, ids);
        return dealResultData(dataMap);
    }

    /**
     *
     * 远程锁定
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:37:34
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "common_leftMenu_rtMonitor", opType = "acc_newEventNo_233",
        requestParams = "name", opContent = "acc_door_name")
    @RequiresPermissions("acc:rtMonitor:lockDoor")
    public ZKResultMsg lockDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateDoor("lockDoor", null, ids);
        return dealResultData(dataMap);
    }

    /**
     *
     * 远程解锁
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:37:49
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "common_leftMenu_rtMonitor", opType = "acc_newEventNo_234",
        requestParams = "name", opContent = "acc_door_name")
    @RequiresPermissions("acc:rtMonitor:unLockDoor")
    public ZKResultMsg unLockDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateDoor("unLockDoor", null, ids);
        return dealResultData(dataMap);
    }

    /**
     * 远程常开
     *
     * @return
     * @author: train.chen
     * @date: 2018年5月18日 下午4:38:08
     */
    @Override
    @LogRequest(module = "acc_module", object = "common_leftMenu_rtMonitor", opType = "acc_rtMonitor_remoteNormalOpen",
        requestParams = "name", opContent = "acc_door_name")
    @RequiresPermissions("acc:rtMonitor:normalOpenDoor")
    public ZKResultMsg normalOpenDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateDoor("normalOpenDoor", null, ids);
        return dealResultData(dataMap);
    }

    @Override
    public ZKResultMsg getAreasByUser() {
        Map<String, String> areaMap = accRTMonitorService.getFilterAreaMap(request.getSession().getId());
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        if (areaMap.size() > 0) {
            JSONObject dataJson = new JSONObject();
            dataJson.put("areaId", areaMap.get("areaIds"));
            dataJson.put("areaName", areaMap.get("areaName"));
            resultMsg.setData(dataJson);
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    /**
     *
     * 启用当天常开时间段
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:38:42
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "common_leftMenu_rtMonitor", opType = "acc_eventNo_11",
        requestParams = "name", opContent = "acc_door_name")
    @RequiresPermissions("acc:rtMonitor:enableNormalOpenDoor")
    public ZKResultMsg enableNormalOpenDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateDoor("enableNormalOpenDoor", null, ids);
        return dealResultData(dataMap);
    }

    /**
     *
     * 禁用当天常开时间段
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:39:15
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "common_leftMenu_rtMonitor", opType = "acc_eventNo_10",
        requestParams = "name", opContent = "acc_door_name")
    @RequiresPermissions("acc:rtMonitor:disableNormalOpenDoor")
    public ZKResultMsg disableNormalOpenDoor(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateDoor("disableNormalOpenDoor", null, ids);
        return dealResultData(dataMap);
    }

    /**
     *
     * 远程打开
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:39:35
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "common_leftMenu_rtMonitor", opType = "acc_rtMonitor_remoteOpen",
        requestParams = "name", opContent = "common_name")
    @RequiresPermissions("acc:rtMonitor:openAuxOut")
    public ZKResultMsg openAuxOut(String ids, String openInterval) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateAuxOut("openAuxOut", openInterval, ids);
        return dealResultData(dataMap);
    }

    /**
     *
     * 远程关闭
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:39:51
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "common_leftMenu_rtMonitor", opType = "acc_rtMonitor_remoteClose",
        requestParams = "name", opContent = "common_name")
    @RequiresPermissions("acc:rtMonitor:closeAuxOut")
    public ZKResultMsg closeAuxOut(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateAuxOut("closeAuxOut", null, ids);
        return dealResultData(dataMap);
    }

    /**
     *
     * 远程常开
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:40:04
     * @return
     */
    @Override
    @LogRequest(module = "acc_module", object = "common_leftMenu_rtMonitor", opType = "acc_rtMonitor_remoteNormalOpen",
        requestParams = "name", opContent = "common_name")
    @RequiresPermissions("acc:rtMonitor:auxOutNormalOpen")
    public ZKResultMsg auxOutNormalOpen(String ids) {
        // 验证用户登录密码
        checkUserPwd();
        Map<String, String> dataMap = accRTMonitorService.operateAuxOut("auxOutNormalOpen", null, ids);
        return dealResultData(dataMap);
    }

    /**
     * @Description: 处理返回数据
     * <AUTHOR>
     * @date 2018/6/6 14:54
     * @param dataMap
     * @return
     */
    private ZKResultMsg dealResultData(Map<String, String> dataMap) {
        String cmdIdData = dataMap.get("cmdId");
        String msg = "";
        ZKResultMsg resultMsg = new ZKResultMsg();
        if ("true".equals(dataMap.get("notExistDev"))) {
            msg = I18nUtil.i18nCode("common_dev_opFaileAndReason") + I18nUtil.i18nCode("common_dev_notExistDev");
            resultMsg.setMsg(msg);
            return resultMsg;
        }
        if (!"".equals(cmdIdData)) {
            for (String cmdData : cmdIdData.split(",")) {
                String cmdId = cmdData.split("=")[0];
                String doorName = cmdData.split("=")[1];
                Map<String, String> resultMap = accDeviceService.getCmdResultById(Long.valueOf(cmdId), 20);
                if (Objects.isNull(resultMap)) {
                    msg += doorName + "," + I18nUtil.i18nCode("common_op_failed") + ";";
                } else {
                    Integer ret = Integer.valueOf(resultMap.get("result"));
                    if (Objects.isNull(ret)) {
                        msg += doorName + "," + I18nUtil.i18nCode("common_op_failed") + ";";
                    } else if (ret < 0) {
                        String failedInfo = I18nUtil.i18nCode(accBaseDictionaryService.getCommReason(ret));
                        msg += doorName + "," + I18nUtil.i18nCode("common_dev_opFaileAndReason") + failedInfo + ";";
                    }
                }
            }
        }
        if (!"".equals(dataMap.get("offline"))) {
            for (String doorName : dataMap.get("offline").split(",")) {
                msg += doorName + "," + I18nUtil.i18nCode("common_dev_offlinePrompt") + ";";
            }
        }
        if (!"".equals(dataMap.get("notSupport"))) {
            for (String doorName : dataMap.get("notSupport").split(",")) {
                msg += doorName + "," + I18nUtil.i18nCode("acc_dev_devNotSupportFunction") + ";";
            }
        }
        if (!"".equals(msg)) {
            resultMsg.setRet("400");
            resultMsg.setMsg(msg);
        }
        resultMsg.setData("");
        return resultMsg;
    }

    @Override
    public ZKResultMsg isKeepAlive() {
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @Override
    public Boolean isShowlcdRTMonitor() {
        return accRTMonitorService.isShowlcdRTMonitor();
    }

    @Override
    public ModelAndView openLcdRTMonitor() {
        String areaNames = accRTMonitorService.getAreaNamesBySessionId(request.getSession().getId());
        String pinEncryptMode = accParamService.getParamValByName("pers.pin.encryptMode");
        String nameEncryptMode = accParamService.getParamValByName("pers.name.encryptMode");
        String cardEncryptMode = accParamService.getParamValByName("pers.cardNo.encryptMode");
        request.setAttribute("pinEncryptMode", pinEncryptMode);
        request.setAttribute("nameEncryptMode", nameEncryptMode);
        request.setAttribute("cardEncryptMode", cardEncryptMode);
        request.setAttribute("areaNames", areaNames);
        return new ModelAndView("acc/rtMonitor/openAccLcdRTMonitor");
    }

    @Override
    public ZKResultMsg getPersonInfo(String areaNames) {
        return accRTMonitorService.getPersonInfo(request.getSession().getId(), areaNames);
    }

    // 验证用户登录密码
    public void checkUserPwd() {
        if (isNeedValid("pwd")) {
            try {
                String loginPwd = this.request.getParameter("loginPwd");
                boolean ret = accLevelService.verifyLoginPwd(request.getSession().getId(), loginPwd);
                if (!ret) {
                    throw new ZKBusinessException("auth_user_pwdIncorrect");
                }
            } catch (ZKBusinessException e) {
                throw e;
            } catch (Exception e) {
                this.log.error("", e);
            }
        }
    }
}
