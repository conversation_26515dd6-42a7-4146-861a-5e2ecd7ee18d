package com.zkteco.zkbiosecurity.acc.dhx;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.license.provider.BaseLicenseProvider;

@Component
public class AccDeviceAddShow {
    @Autowired
    private BaseLicenseProvider baseLicenseProvider;

    public boolean isShow() {
        return baseLicenseProvider.isModuleLicensed(ConstUtil.LICENSE_MODULE_ACC_PULL);
    }
}
