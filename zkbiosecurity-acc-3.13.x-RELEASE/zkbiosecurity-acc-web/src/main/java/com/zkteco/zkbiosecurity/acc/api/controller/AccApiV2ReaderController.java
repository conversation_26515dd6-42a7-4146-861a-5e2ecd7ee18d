package com.zkteco.zkbiosecurity.acc.api.controller;

import java.util.List;

import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.api.vo.AccApiReaderItem;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.AccReaderService;
import com.zkteco.zkbiosecurity.acc.vo.AccReaderItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @date 2022/11/1 16:18
 */
@Controller
@RequestMapping(value = {"/api/v2/reader"})
@Api(tags = "AccReader", description = "acc reader")
public class AccApiV2ReaderController {
    @Autowired
    private AccReaderService accReaderService;

    /**
     * 分页获取读头信息
     *
     * @param pageNo
     * @param pageSize
     * @return
     * @auther lambert.li
     * @date 2018/11/14 9:28
     */
    @ResponseBody
    @RequestMapping(value = {"/list"}, method = RequestMethod.GET, produces = "application/json")
    @ApiOperation(value = "Get Acc Readers", notes = "Return Acc Reader List", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"pageNo","pageSize"})
    public ApiResultMessage list(@RequestParam(name = "pageNo", required = false) Integer pageNo,
        @RequestParam(name = "pageSize", required = false) Integer pageSize) {
        if (pageNo == null || pageSize == null) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE,
                I18nUtil.i18nCode("acc_api_parameterValueCannotBeNull"));
        }
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        List<AccApiReaderItem> accApiReaderItemList = Lists.newArrayList();
        Pager pager = accReaderService.getItemsByPage(new AccReaderItem(), pageNo - 1, pageSize);
        List<AccReaderItem> accReaderItemList = (List<AccReaderItem>)pager.getData();
        if (!accReaderItemList.isEmpty()) {
            accReaderItemList.forEach(accReaderItem -> {
                AccApiReaderItem accApiReaderItem = AccApiReaderItem.createApiReader(accReaderItem);
                if (accApiReaderItem != null) {
                    accApiReaderItemList.add(accApiReaderItem);
                }
            });
        }
        pager.setData(accApiReaderItemList);
        return ApiResultMessage.successMessage(pager);
    }
}
