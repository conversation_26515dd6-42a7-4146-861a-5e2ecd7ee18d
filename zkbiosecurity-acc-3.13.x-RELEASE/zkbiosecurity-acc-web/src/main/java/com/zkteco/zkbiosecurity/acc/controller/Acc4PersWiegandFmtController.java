/*
 * @author:	GenerationTools
 * @date:	2018-02-23 下午03:48
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import com.zkteco.zkbiosecurity.acc.remote.Acc4PersWiegandFmtRemote;
import com.zkteco.zkbiosecurity.acc.service.AccWiegandFmtService;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.Objects;

/**
 * @author:	GenerationTools
 * @date:	2018-02-23 下午03:48
 */
@Controller
public class Acc4PersWiegandFmtController extends BaseController implements Acc4PersWiegandFmtRemote {
	@Autowired
	private AccWiegandFmtService accWiegandFmtService;

	@Override
	public ZKResultMsg start(String deviceId) {
		ZKResultMsg msg =new ZKResultMsg();
		String result = accWiegandFmtService.start(deviceId);
		if (!"success".equals(result)) {
			msg.setRet("fail");
			msg.setMsg(result);
		}
		msg.setData(result);
		return I18nUtil.i18nMsg(msg);
	}

	@Override
	public ZKResultMsg readerCard(String deviceId) {
		ZKResultMsg msg =new ZKResultMsg();
		String result = accWiegandFmtService.readerCard(deviceId);
		if (Objects.isNull(result)) {
			msg.setRet("500");
		}
		msg.setData(result);
		return I18nUtil.i18nMsg(msg);
	}

	@Override
	public ZKResultMsg recommendFmt(String deviceId, String sizeCode, String cardNo, String orgCardNo, String bitscount, String withSizeCode) {
		ZKResultMsg msg =new ZKResultMsg();
		String result = accWiegandFmtService.recommendFmt(deviceId,sizeCode,cardNo,orgCardNo,bitscount,withSizeCode);
		msg.setData(result);
		return I18nUtil.i18nMsg(msg);
	}

	@Override
	public ZKResultMsg getAllFilterId() {
		ZKResultMsg msg =new ZKResultMsg();
		String result = accWiegandFmtService.getAllFilterId();
		msg.setData(result);
		return I18nUtil.i18nMsg(msg);
	}

	@Override
	public ZKResultMsg stop(String deviceId) {
		ZKResultMsg msg = new ZKResultMsg();
		String result = accWiegandFmtService.stop(deviceId);
		msg.setData(result);
		return I18nUtil.i18nMsg(msg);
	}
}