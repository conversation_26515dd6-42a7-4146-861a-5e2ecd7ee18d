package com.zkteco.zkbiosecurity.acc.controller;

import com.zkteco.zkbiosecurity.acc.remote.AccSelectReaderRemote;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccReaderService;
import com.zkteco.zkbiosecurity.acc.vo.AccSelectReaderItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

@Controller
public class AccSelectReaderController extends BaseController implements AccSelectReaderRemote {

    @Autowired
    private AccReaderService accReaderService;
    @Autowired
    private AccDeviceService accDeviceService;

    @Override
    public DxGrid list(AccSelectReaderItem condition) {
        String type = condition.getType();
        if(StringUtils.isBlank(condition.getSelectId())){
            condition.setSelectId("-1");
        }
        if("noSelected".equals(type))
        {
            condition.setNotInId(condition.getSelectId() + (StringUtils.isNotBlank(condition.getFilterIds()) ? "," + condition.getFilterIds() : ""));
        }
        else if("selected".equals(type))
        {
            condition.setInId(condition.getSelectId());
        }
        String authAreaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId());
        if (StringUtils.isNotBlank(authAreaIds)){
            condition.setAuthAreaIdIn(authAreaIds);
        }
        Pager pager = accReaderService.getReaderZoneItemByPage(condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }
}
