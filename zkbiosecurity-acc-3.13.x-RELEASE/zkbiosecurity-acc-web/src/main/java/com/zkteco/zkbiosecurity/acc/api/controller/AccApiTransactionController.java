package com.zkteco.zkbiosecurity.acc.api.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.zkteco.zkbiosecurity.acc.api.vo.AccApiTransactionItem;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 门禁事件记录接口
 *
 * <AUTHOR>
 * @Date: 2018/11/14 09:55
 */
@Controller
@RequestMapping(value = {"/api/transaction"})
@Api(tags = "AccTransaction", description = "acc transaction")
public class AccApiTransactionController {

    @Autowired
    private AccTransactionService accTransactionService;

    /**
     * 根据设备SN获取事件记录
     *
     * @auther lambert.li
     * @date 2018/11/14 17:33
     * @param deviceSn
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "Get Transaction List By Sn", notes = "Return Transactions",
        response = ApiResultMessage.class)
    @RequestMapping(value = {"/device/{deviceSn}"}, method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"deviceSn", "startDate", "endDate", "pageNo", "pageSize"})
    public ApiResultMessage getTransactionsByDeviceSn(@PathVariable(name = "deviceSn") String deviceSn,
        @RequestParam(name = "startDate", required = false) String startDate,
        @RequestParam(name = "endDate", required = false) String endDate, @RequestParam(name = "pageNo") Integer pageNo,
        @RequestParam(name = "pageSize") Integer pageSize) {
        if (StringUtils.isBlank(deviceSn)) {
            return ApiResultMessage.message(AccConstants.ACC_TRANSACTION_SNNOTNULL,
                I18nUtil.i18nCode("acc_api_devSnNotNull"));
        }
        if (pageNo == null || pageSize == null) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE,
                I18nUtil.i18nCode("acc_api_parameterValueCannotBeNull"));
        }
        ApiResultMessage apiResultMessage = checkParam(startDate, endDate, pageNo, pageSize);
        if (apiResultMessage != null) {
            return apiResultMessage;
        }
        Date startTime = null;
        Date endTime = null;
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            startTime = DateUtil.stringToDate(startDate);
            endTime = DateUtil.stringToDate(endDate);
        }
        return accTransactionService.getAccApiTransactionsBySn(deviceSn, startTime, endTime, pageNo, pageSize);
    }

    /**
     * 根据人员编号获取事件记录
     *
     * @auther lambert.li
     * @date 2018/11/14 17:33
     * @param pin
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ApiOperation(value = "Get Transaction List By Pin", notes = "Return Transactions",
        response = ApiResultMessage.class)
    @RequestMapping(value = {"/person/{pin}"}, method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"pin", "startDate", "endDate", "pageNo", "pageSize"})
    public ApiResultMessage getTransactionsByPin(@PathVariable(name = "pin") String pin,
        @RequestParam(name = "startDate", required = false) String startDate,
        @RequestParam(name = "endDate", required = false) String endDate, @RequestParam(name = "pageNo") Integer pageNo,
        @RequestParam(name = "pageSize") Integer pageSize) {

        if (StringUtils.isBlank(pin)) {
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }
        if (pageNo == null || pageSize == null) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE,
                I18nUtil.i18nCode("acc_api_parameterValueCannotBeNull"));
        }
        ApiResultMessage apiResultMessage = checkParam(startDate, endDate, pageNo, pageSize);
        if (apiResultMessage != null) {
            return apiResultMessage;
        }
        Date startTime = null;
        Date endTime = null;
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            startTime = DateUtil.stringToDate(startDate);
            endTime = DateUtil.stringToDate(endDate);
        }
        return accTransactionService.getAccApiTransactionsByPin(pin, startTime, endTime, pageNo, pageSize);
    }

    /**
     * 获取实时事件
     *
     * @auther lambert.li
     * @date 2018/11/21 16:49
     * @param timestamp
     * @return
     */
    @ApiOperation(value = "Monitor", notes = "Return Transactions", response = ApiResultMessage.class)
    @RequestMapping(value = "/monitor", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"timestamp"})
    public ApiResultMessage monitor(@RequestParam(name = "timestamp") String timestamp) {
        if (StringUtils.isBlank(timestamp)) {
            return ApiResultMessage.message(AccConstants.ACC_TIMESTAMPNOTNULL,
                I18nUtil.i18nCode("acc_api_timesTampNotNull"));
        }
        List<AccApiTransactionItem> apiTransactions = accTransactionService.getRTMonitorData(timestamp);
        return ApiResultMessage.successMessage(apiTransactions);
    }

    /**
     * 分页获取事件记录
     *
     * @auther lambert.li
     * @date 2018/11/14 17:34
     * @param personPin
     * @param startDate
     * @param endDate
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET, produces = "application/json")
    @ApiOperation(value = "Get Transactions List", notes = "Return Transactions List",
        response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"personPin", "startDate", "endDate", "pageNo", "pageSize"})
    public ApiResultMessage list(@RequestParam(name = "personPin", required = false) String personPin,
        @RequestParam(name = "startDate", required = false) String startDate,
        @RequestParam(name = "endDate", required = false) String endDate, @RequestParam(name = "pageNo") Integer pageNo,
        @RequestParam(name = "pageSize") Integer pageSize) {

        if (pageNo == null || pageSize == null) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE,
                I18nUtil.i18nCode("acc_api_parameterValueCannotBeNull"));
        }
        ApiResultMessage apiResultMessage = checkParam(startDate, endDate, pageNo, pageSize);
        if (apiResultMessage != null) {
            return apiResultMessage;
        }
        Date startTime = null;
        Date endTime = null;
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            startTime = DateUtil.stringToDate(startDate);
            endTime = DateUtil.stringToDate(endDate);
        }
        List<AccApiTransactionItem> apiTransactions =
            accTransactionService.getAccApiTransactionList(personPin, startTime, endTime, pageNo, pageSize);
        return ApiResultMessage.successMessage(apiTransactions);
    }

    /**
     * 校验API接口参数设置的合理性
     *
     * @param startDate:
     * @param endDate:
     * @param pageNo:
     * @param pageSize:
     * @return void
     * <AUTHOR>
     * @throws @date 2020-09-01 15:27
     * @since 1.0.0
     */
    private ApiResultMessage checkParam(String startDate, String endDate, Integer pageNo, Integer pageSize) {
        Date startTime = null;
        Date endTime = null;
        if (pageNo == null || pageSize == null) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE,
                I18nUtil.i18nCode("acc_api_parameterValueCannotBeNull"));
        }
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        if (pageSize > 1000) {
            return ApiResultMessage.message(AccConstants.API_PAGE_OVERSIZE,
                I18nUtil.i18nCode("common_api_pageSizeOver"));
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            startTime = DateUtil.stringToDate(startDate);
            endTime = DateUtil.stringToDate(endDate);
            // 时间格式错误
            if (startTime == null || endTime == null) {
                return ApiResultMessage.message(AccConstants.API_DATE_ERROR,
                    I18nUtil.i18nCode("common_dsTime_timeValid2"));
            }
            // 开始时间不能大于结束时间
            if (startTime.getTime() > endTime.getTime()) {
                return ApiResultMessage.message(AccConstants.API_DATE_STARTTIME_LARGE,
                    I18nUtil.i18nCode("common_dsTime_timeValid4"));
            }
        }
        return null;
    }

    /**
     * 根据人员编号获取FirstInAndLastOut记录
     *
     * <AUTHOR>
     * @Param pin
     * @Param startDate
     * @Param endDate
     * @Param pageNo
     * @Param pageSize
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * @date 2020/9/3 11:43
     */
    @ApiOperation(value = "Get FirstInAndLastOut List By Pin", notes = "Return FirstInAndLastOut List",
        response = ApiResultMessage.class)
    @RequestMapping(value = {"/firstInAndLastOut/{pin}"}, method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"pin", "startDate", "endDate", "pageNo", "pageSize"})
    public ApiResultMessage getFirstInLastOutByPin(@PathVariable(name = "pin") String pin,
        @RequestParam(name = "startDate", required = false) String startDate,
        @RequestParam(name = "endDate", required = false) String endDate, @RequestParam(name = "pageNo") Integer pageNo,
        @RequestParam(name = "pageSize") Integer pageSize) {
        ApiResultMessage apiResultMessage = checkParam(startDate, endDate, pageNo, pageSize);

        if (StringUtils.isBlank(pin)) {
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }

        if (apiResultMessage != null) {
            return apiResultMessage;
        }
        Date startTime = null;
        Date endTime = null;
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            startTime = DateUtil.stringToDate(startDate);
            endTime = DateUtil.stringToDate(endDate);
        }
        return accTransactionService.getAccApiFirstInLastOutByPin(pin, startTime, endTime, pageNo, pageSize);
    }

    /**
     * 获取门记录、获取当天门事件记录、获取开关门事件记录、获取人员门禁事件记录
     *
     * <AUTHOR>
     * @date 2021-06-15 15:34
     * @param zkMessage
     * @since 1.0.0
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @ApiOperation(value = "Get Door Transactions List", notes = "Return Transactions", response = ZKResultMsg.class)
    @RequestMapping(value = "/getDoorTransactions", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"activeType", "appId", "appKey", "async", "content", "contentId", "defaultContent",
        "listContent", "messageId", "messageType", "moduleCode", "repeat", "repeatCount"})
    public ZKResultMsg getDoorTransactions(@RequestBody ZKMessage zkMessage) {

        Map<String, Object> content = zkMessage.getContent();
        String devSn = MapUtils.getString(content, "devSn");
        String startTime = MapUtils.getString(content, "startTime");
        String endTime = MapUtils.getString(content, "endTime");
        String filter = MapUtils.getString(content, "filter");
        String type = MapUtils.getString(content, "type");
        String eventNo = MapUtils.getString(content, "eventNo");
        String personPin = MapUtils.getString(content, "personPin");
        Integer pageNo = MapUtils.getInteger(content, "pageNo");
        Integer pageSize = MapUtils.getInteger(content, "pageSize");

        if (content.size() == 0) {
            return ZKResultMsg.failMsg(I18nUtil.i18nCode("common_api_pageNotNull"));
        }
        if (pageNo == null || pageSize == null) {
            return ZKResultMsg.failMsg(I18nUtil.i18nCode("acc_api_parameterValueCannotBeNull"));
        }
        ZKResultMsg zkResultMsg = checkApiParam(startTime, endTime, pageNo, pageSize);
        if (zkResultMsg != null) {
            return zkResultMsg;
        }
        Date startDate = null;
        Date endDate = null;
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            startDate = DateUtil.stringToDate(startTime);
            endDate = DateUtil.stringToDate(endTime);
        }
        if (StringUtils.isNotBlank(personPin)) {
            return accTransactionService.getAccApiTransactionsBySnAndPin(devSn, personPin, startDate, endDate, pageNo,
                pageSize);
        } else {
            return accTransactionService.getAccApiTransactionsByFilterAndType(devSn, filter, type, eventNo, startDate,
                endDate, pageNo, pageSize);
        }
    }

    /**
     * 校验API接口参数设置的合理性
     *
     * <AUTHOR>
     * @date 2021-06-21 15:03
     * @param startDate
     * @param endDate
     * @param pageNo
     * @param pageSize
     * @since 1.0.0
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    private ZKResultMsg checkApiParam(String startDate, String endDate, Integer pageNo, Integer pageSize) {
        Date startTime = null;
        Date endTime = null;
        if (pageNo < 0 || pageSize <= 0) {
            return ZKResultMsg.failMsg(I18nUtil.i18nCode("common_api_pageValid"));
        }
        if (pageSize > 1000) {
            return ZKResultMsg.failMsg(I18nUtil.i18nCode("common_api_pageSizeOver"));
        }
        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            startTime = DateUtil.stringToDate(startDate);
            endTime = DateUtil.stringToDate(endDate);
            // 时间格式错误
            if (startTime == null || endTime == null) {
                return ZKResultMsg.failMsg(I18nUtil.i18nCode("common_dsTime_timeValid2"));
            }
            // 开始时间不能大于结束时间
            if (startTime.getTime() > endTime.getTime()) {
                return ZKResultMsg.failMsg(I18nUtil.i18nCode("common_dsTime_timeValid4"));
            }
        }
        return null;
    }

    /**
     * 获取门禁事件记录详情
     *
     * <AUTHOR>
     * @date 2021-06-15 17:51
     * @param zkMessage
     * @since 1.0.0
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @ApiOperation(value = "Get Door Transaction Detail", notes = "Return Transaction Detail",
        response = ZKResultMsg.class)
    @RequestMapping(value = "/getDoorTransactionDetail", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"activeType", "appId", "appKey", "async", "content", "contentId", "defaultContent",
        "listContent", "messageId", "messageType", "moduleCode", "repeat", "repeatCount"})
    public ZKResultMsg getDoorTransactionDetail(@RequestBody ZKMessage zkMessage) {
        Map<String, Object> content = zkMessage.getContent();
        String id = MapUtils.getString(content, "id");
        return accTransactionService.getAccApiTransactionDetailById(id);
    }

    /**
     * 根据事件id获取事件信息
     *
     * @param id:事件id
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2022-10-26 15:37
     * @since 1.0.0
     */
    @ApiOperation(value = "Get AccTransaction By Id", notes = "Return AccTransaction Object",
        response = ApiResultMessage.class)
    @RequestMapping(value = "/getById/{id}", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"id"})
    public ApiResultMessage getById(@PathVariable(required = false) String id) {
        if (StringUtils.isBlank(id)) {
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_LEVELIDNOTNULL,
                I18nUtil.i18nCode("acc_api_levelIdNotNull"));
        }
        return accTransactionService.getAccApiTransactionsById(id);
    }

}
