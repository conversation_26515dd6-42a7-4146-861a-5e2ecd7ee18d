/*
 * @author: GenerationTools
 * 
 * @date: 2018-03-13 上午10:27 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.remote.AccAntiPassbackRemote;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 门禁反潜Controller
 */
@Controller
public class AccAntiPassbackController extends BaseController implements AccAntiPassbackRemote {
    @Autowired
    private AccAntiPassbackService accAntiPassbackService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccReaderService accReaderService;
    @Autowired
    private AccDoorService accDoorService;

    @RequiresPermissions("acc:antiPassback")
    @Override
    public ModelAndView index() {
        return new ModelAndView("acc/antiPassback/accAntiPassback");
    }

    @RequiresPermissions({"acc:antiPassback:add", "acc:antiPassback:edit"})
    @Override
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("item", accAntiPassbackService.getItemById(id));
        }
        return new ModelAndView("acc/antiPassback/editAccAntiPassback");
    }

    @RequiresPermissions({"acc:antiPassback:add", "acc:antiPassback:edit"})
    @LogRequest(module = "acc_module", object = "acc_eventNo_24", opType = "common_op_edit", requestParams = {"name"},
        opContent = "common_name")
    @Override
    public ZKResultMsg save(AccAntiPassbackItem item) {
        ZKResultMsg res = new ZKResultMsg();
        if (item.getApbRule() != null) {
            item.setApbRuleType(null);
        }
        accAntiPassbackService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("acc:antiPassback:refresh")
    @Override
    public DxGrid list(AccAntiPassbackItem condition) {
        String authAreaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId());
        if (StringUtils.isNotBlank(authAreaIds)) {
            condition.setAuthAreaIdIn(authAreaIds);
        }
        Pager pager = accAntiPassbackService.getItemsByPage(condition, getPageNo(), getPageSize());
        // 根据反潜转换成相应的门名称组合显示在反潜列表页面中
        List<AccAntiPassbackItem> accAntiPassbackItemList = (List<AccAntiPassbackItem>)pager.getData();
        accAntiPassbackItemList.forEach(accAntiPassbackItem -> {
            if (accAntiPassbackItem.getApbRule() != null) {
                accAntiPassbackItem
                    .setApbRuleShow(accAntiPassbackService.convertAntiPassbackRule(accAntiPassbackItem.getId()));
            } else if (accAntiPassbackItem.getApbRuleType() != null) {
                accAntiPassbackService.completionItem(accAntiPassbackItem);
                String apbType = accAntiPassbackItem.getApbRuleType() == 0 ? I18nUtil.i18nCode("acc_apb_door") : I18nUtil.i18nCode("acc_apb_readerHead");
                String group1 = I18nUtil.i18nCode("acc_interlock_group1") + ":" + accAntiPassbackItem.getGroup1Names();
                String group2 = I18nUtil.i18nCode("acc_interlock_group2") + ":" + accAntiPassbackItem.getGroup2Names();
                accAntiPassbackItem.setApbRuleShow(apbType + " " + group1 + " " + group2);
            }
        });
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("acc:antiPassback:del")
    @LogRequest(module = "acc_module", object = "acc_eventNo_24", opType = "common_op_del",
        requestParams = {"deviceNames"}, opContent = "common_dev_name")
    @Override
    public ZKResultMsg del(String ids) {
        accAntiPassbackService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public ZKResultMsg getRule(String deviceId) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        AccDeviceItem dev = accDeviceService.getItemById(deviceId);
        Map<String, Object> map = accAntiPassbackService.getApbRule(deviceId);
        Map<Integer, String> doorMap = (Map<Integer, String>)map.get("doorMap");
        Integer readerCount = (Integer)map.get("readerCount");
        List<SelectItem> selectItemList = new ArrayList<>();
        if (doorMap != null && doorMap.size() > 0) {
            String rule = "";
            AccDeviceOptionItem accDeviceOptionItem = new AccDeviceOptionItem(deviceId, "AccSupportFunList");
            List<AccDeviceOptionItem> accDeviceOptionItemList =
                accDeviceOptionService.getByCondition(accDeviceOptionItem);
            if ((accDeviceOptionItemList != null && accDeviceOptionItemList.size() > 0)
                && (AccConstants.DEVICE_C3_460 == dev.getMachineType()
                    || AccConstants.DEVICE_C3_400 == dev.getMachineType()
                    || AccConstants.DEVICE_INBIO5_400 == dev.getMachineType()
                    || AccConstants.DEVICE_C5_400 == dev.getMachineType())) {
                rule = I18nUtil.i18nCode("acc_apb_reader7");// 任意门反潜
                selectItemList.add(getJsonObj("7", rule));
            }
            if (AccConstants.DEVICE_ACCESS_CONTROL == dev.getMachineType()) {
                rule = String.format(I18nUtil.i18nCode("acc_apb_controlOut"), doorMap.get(1));// 出反潜
                selectItemList.add(getJsonObj("1", rule));
                rule = String.format(I18nUtil.i18nCode("acc_apb_controlIn"), doorMap.get(1));// 入反潜
                selectItemList.add(getJsonObj("2", rule));
                rule = String.format(I18nUtil.i18nCode("acc_apb_controlInOut"), doorMap.get(1));// 出入反潜
                selectItemList.add(getJsonObj("3", rule));
            } else if (doorMap.size() == AccConstants.ACPANEL_1_DOOR) {
                // 单门双向
                rule = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(1));
                selectItemList.add(getJsonObj("1", rule));
            } else if (doorMap.size() == AccConstants.ACPANEL_2_DOOR && readerCount == 4
                || dev.getMachineType() == AccConstants.DEVICE_C4_200) {
                // 两门双向
                rule = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(1));
                selectItemList.add(getJsonObj("1", rule));

                rule = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(2));
                selectItemList.add(getJsonObj("2", rule));

                rule = I18nUtil.i18nCode("acc_apb_reader2", doorMap.get(1), doorMap.get(2));
                selectItemList.add(getJsonObj("3", rule));

                rule = I18nUtil.i18nCode("acc_apb_twoDoor", doorMap.get(1), doorMap.get(2));
                selectItemList.add(getJsonObj("4", rule));
            } else if (doorMap.size() == AccConstants.ACPANEL_2_DOOR && readerCount == 2) {
                // 两门单向
                rule = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(1));
                selectItemList.add(getJsonObj("16", rule));

                rule = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(2));
                selectItemList.add(getJsonObj("32", rule));

                rule = I18nUtil.i18nCode("acc_apb_reader2", doorMap.get(1), doorMap.get(2));
                selectItemList.add(getJsonObj("48", rule));

                rule = I18nUtil.i18nCode("acc_apb_twoDoor", doorMap.get(1), doorMap.get(2));
                selectItemList.add(getJsonObj("1", rule));
            } else if (doorMap.size() == AccConstants.ACPANEL_4_DOOR)// 四门单向
            {
                rule = I18nUtil.i18nCode("acc_apb_twoDoor", doorMap.get(1), doorMap.get(2));
                selectItemList.add(getJsonObj("1", rule));

                rule = I18nUtil.i18nCode("acc_apb_twoDoor", doorMap.get(3), doorMap.get(4));
                selectItemList.add(getJsonObj("2", rule));

                rule = I18nUtil.i18nCode("acc_apb_fourDoor", doorMap.get(1), doorMap.get(2), doorMap.get(3),
                    doorMap.get(4));
                selectItemList.add(getJsonObj("3", rule));

                rule = I18nUtil.i18nCode("acc_apb_fourDoor2", doorMap.get(1), doorMap.get(2), doorMap.get(3),
                    doorMap.get(4));
                selectItemList.add(getJsonObj("4", rule));

                rule = I18nUtil.i18nCode("acc_apb_fourDoor3", doorMap.get(1), doorMap.get(2), doorMap.get(3),
                    doorMap.get(4));
                selectItemList.add(getJsonObj("5", rule));

                rule = I18nUtil.i18nCode("acc_apb_fourDoor4", doorMap.get(1), doorMap.get(2), doorMap.get(3),
                    doorMap.get(4));
                selectItemList.add(getJsonObj("6", rule));
                // 支持485读头
                if (accDeviceOptionService.isSupportFun(dev.getSn(), "~Ext485ReaderFunOn")) {
                    rule = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(1));
                    selectItemList.add(getJsonObj("16", rule));

                    rule = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(2));
                    selectItemList.add(getJsonObj("32", rule));

                    rule = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(3));
                    selectItemList.add(getJsonObj("64", rule));

                    rule = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(4));
                    selectItemList.add(getJsonObj("128", rule));

                    rule = I18nUtil.i18nCode("acc_apb_reader2", doorMap.get(1), doorMap.get(2));
                    selectItemList.add(getJsonObj("48", rule));

                    rule = I18nUtil.i18nCode("acc_apb_reader2", doorMap.get(1), doorMap.get(3));
                    selectItemList.add(getJsonObj("80", rule));

                    rule = I18nUtil.i18nCode("acc_apb_reader2", doorMap.get(1), doorMap.get(4));
                    selectItemList.add(getJsonObj("144", rule));

                    rule = I18nUtil.i18nCode("acc_apb_reader2", doorMap.get(2), doorMap.get(3));
                    selectItemList.add(getJsonObj("96", rule));

                    rule = I18nUtil.i18nCode("acc_apb_reader2", doorMap.get(2), doorMap.get(4));
                    selectItemList.add(getJsonObj("160", rule));

                    rule = I18nUtil.i18nCode("acc_apb_reader2", doorMap.get(3), doorMap.get(4));
                    selectItemList.add(getJsonObj("196", rule));// 对照pullsdk文档由192改为196--add by wenxin 20150120

                    rule = I18nUtil.i18nCode("acc_apb_reader3", doorMap.get(1), doorMap.get(2), doorMap.get(3));
                    selectItemList.add(getJsonObj("112", rule));

                    rule = I18nUtil.i18nCode("acc_apb_reader3", doorMap.get(1), doorMap.get(2), doorMap.get(4));
                    selectItemList.add(getJsonObj("176", rule));

                    rule = I18nUtil.i18nCode("acc_apb_reader3", doorMap.get(1), doorMap.get(3), doorMap.get(4));
                    selectItemList.add(getJsonObj("208", rule));

                    rule = I18nUtil.i18nCode("acc_apb_reader3", doorMap.get(2), doorMap.get(3), doorMap.get(4));
                    selectItemList.add(getJsonObj("224", rule));

                    rule = I18nUtil.i18nCode("acc_apb_reader4", doorMap.get(1), doorMap.get(2), doorMap.get(3),
                        doorMap.get(4));
                    selectItemList.add(getJsonObj("240", rule));
                }
            }
        }
        zkResultMsg.setData(selectItemList);
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @Override
    public ZKResultMsg validGlobalApb(String devId) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        Boolean setGlobalApb = accAntiPassbackService.validGlobalApb(devId);
        zkResultMsg.setData(setGlobalApb);
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @Override
    public DxGrid selectDevicelist(AccAntiPassbackSelectDeviceItem codition) {
        String filterDevId = accAntiPassbackService.getDevIdWithApb();
        if (StringUtils.isBlank(codition.getSelectId())) {
            codition.setSelectId("-1");
        }
        if (codition.getType().equals("noSelected")) {
            if (StringUtils.isNotBlank(filterDevId)) {
                filterDevId = codition.getSelectId() + "," + filterDevId;
                codition.setSelectId(filterDevId);
            }
            codition.setSelectDeviceIdsNotIn(codition.getSelectId());
        } else if (codition.getType().equals("selected")) {
            codition.setSelectDeviceIdsIn(codition.getSelectId());
        }
        String authAreaIds = accDeviceService.getAreaIdsByAuthFilter(request.getSession().getId());
        if (StringUtils.isNotBlank(authAreaIds)) {
            codition.setAuthAreaIdIn(authAreaIds);
        }
        Pager pager = accDeviceService.getItemsByPage(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    /**
     * 组装反潜规则数据提取出的方法
     * 
     * @author: mingfa.zheng
     * @date: 2018/4/25 16:22
     * @return:
     */
    private SelectItem getJsonObj(String val, String name) {
        SelectItem selectItem = new SelectItem();
        selectItem.setValue(val);
        selectItem.setText(name);
        return selectItem;
    }

    @Override
    public ModelAndView selectReader(String deviceId, String notInId, String group) {
        request.setAttribute("deviceId", deviceId);
        request.setAttribute("notInId", notInId);
        request.setAttribute("group", group);
        return new ModelAndView("acc/antiPassback/AccAntiPassbackSelectReader");
    }

    @Override
    public DxGrid selectReaderList(AccAntiPassbackSelectReaderItem codition) {
        if (StringUtils.isBlank(codition.getSelectId())) {
            codition.setSelectId("-1");
        }
        codition.setDoorEnabled(true);
        if (codition.getType().equals("noSelected")) {
            codition.setSelectReaderIdsNotIn(codition.getSelectId());
        } else if (codition.getType().equals("selected")) {
            codition.setSelectReaderIdsIn(codition.getSelectId());
        }
        Pager pager = accReaderService.getSimpleItemsByPage(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @Override
    public ModelAndView selectDoor(String deviceId, String notInId, String group) {
        request.setAttribute("deviceId", deviceId);
        request.setAttribute("notInId", notInId);
        request.setAttribute("group", group);
        return new ModelAndView("acc/antiPassback/AccAntiPassbackSelectDoor");
    }

    @Override
    public DxGrid selectDoorList(AccAntiPassbackSelectDoorItem codition) {
        if (StringUtils.isBlank(codition.getSelectId())) {
            codition.setSelectId("-1");
        }
        codition.setEnabled(true);
        if (codition.getType().equals("noSelected")) {
            codition.setSelectDoorIdsNotIn(codition.getSelectId());
        } else if (codition.getType().equals("selected")) {
            codition.setSelectDoorIdsIn(codition.getSelectId());
        }
        Pager pager = accDoorService.getItemsByPage(codition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, codition.getClass());
    }

    @Override
    public ZKResultMsg validDetermineApb(String devId) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        zkResultMsg.setData(accAntiPassbackService.validDetermineApb(devId));
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @Override
    public ZKResultMsg validApbCount(String deviceId) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        List<Long> count = new ArrayList<Long>();
        Long doorCount = accDoorService.getIdByDevId(deviceId).stream().count();
        Long readerCount = accReaderService.getItemListByDevId(deviceId).stream().count();
        count.add(doorCount);
        count.add(readerCount);
        zkResultMsg.setData(count);
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @Override
    public boolean validName(String name) {
        return !accAntiPassbackService.nameExists(name);
    }

}