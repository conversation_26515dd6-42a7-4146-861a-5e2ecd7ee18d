/*
 * File Name: PersExtendJSAndCSS.java Created by juvenle.li Copyright:Copyright ? 1985-2017 ZKTeco Inc.All right
 * reserved.
 */
package com.zkteco.zkbiosecurity.acc.web.extend;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.core.utils.PropertiesUtil;
import com.zkteco.zkbiosecurity.core.web.extend.ExtendJSAndCSS;

@Component
public class AccExtendJSAndCSS implements ExtendJSAndCSS {

    @Override
    public List<String> extend() {
        List<String> exts = new ArrayList<String>();
        // 添加js
        exts.add("js/i18n-keys-acc.js");
        exts.add("js/acc.js");
        exts.add("js/accDevice.js");
        exts.add("js/accLevel.js");
        exts.add("js/accMonitorRequest.js");
        exts.add("js/accRTMonitor.dict.js");
        exts.add("js/accRTMonitor.js");
        exts.add("js/accWebSocket.js");
        exts.add("js/ftypes.custom.js");
        exts.add("js/rtMonitorUtil.js");
        exts.add("js/showTime.js");
        exts.add("js/timepicker.js");
        exts.add("js/timeinput.widget.js");
        exts.add("js/accLinkage.js");
        // 添加css
        exts.add("css/acc.css");
        exts.add("css/timepicker.css");
        exts.add("css/timezoneset.css");
        exts.add("css/acc-lightgreen.css");
        if (PropertiesUtil.enableRTL()) {
            exts.add("/css/accRTL.css");
        }
        return exts;
    }

}
