package com.zkteco.zkbiosecurity.acc.api.controller;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.api.vo.AccApiReaderItem;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.AccReaderService;
import com.zkteco.zkbiosecurity.acc.vo.AccReaderItem;
import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * 门禁读头接口
 *
 * <AUTHOR>
 * @Date: 2018/11/14 08:52
 */
@Controller
@RequestMapping(value = {"/api/reader"})
@Api(tags = "AccReader", description = "acc reader")
public class AccApiReaderController {

    @Autowired
    private AccReaderService accReaderService;

    /**
     * 根据读头ID获取读头信息
     *
     * @param id
     * @return
     * @auther lambert.li
     * @date 2018/11/14 9:28
     */
    @ApiOperation(value = "Get Acc Reader Info By Id", notes = "Return Acc Reader Object", response = ApiResultMessage.class)
    @RequestMapping(value = {"/getAcc"}, method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"id"})
    public ApiResultMessage getReaderById(@RequestParam(name = "id", required = false) String id) {
        if (StringUtils.isBlank(id)) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_DOORIDNOTNULL, I18nUtil.i18nCode("acc_api_readerIdCannotBeNull"));
        }
        AccReaderItem accReaderItem = accReaderService.getItemById(id);
        if (accReaderItem == null) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_DOORIDNOTNULL, I18nUtil.i18nCode("acc_api_theReaderDoesNotExist"));
        }
        AccApiReaderItem accApiReaderItem = AccApiReaderItem.createApiReader(accReaderItem);
        return ApiResultMessage.successMessage(accApiReaderItem);
    }

    /**
     * 分页获取读头信息
     *
     * @param pageNo
     * @param pageSize
     * @return
     * @auther lambert.li
     * @date 2018/11/14 9:28
     */
    @ResponseBody
    @RequestMapping(value = {"/accList"}, method = RequestMethod.GET, produces = "application/json")
    @ApiOperation(value = "Get Acc Readers", notes = "Return Acc Reader List", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"pageNo","pageSize"})
    public ApiResultMessage list(@RequestParam(name = "pageNo", required = false) Integer pageNo, @RequestParam(name = "pageSize", required = false) Integer pageSize) {
        if (pageNo == null || pageSize == null) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE, I18nUtil.i18nCode("acc_api_parameterValueCannotBeNull"));
        }
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(AccConstants.API_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        List<AccApiReaderItem> accApiReaderItemList = Lists.newArrayList();
        List<AccReaderItem> accReaderItemList = (List<AccReaderItem>) accReaderService.getItemsByPage(new AccReaderItem(), pageNo - 1, pageSize).getData();
        if (!accReaderItemList.isEmpty()) {
            accReaderItemList.forEach(accReaderItem -> {
                AccApiReaderItem accApiReaderItem = AccApiReaderItem.createApiReader(accReaderItem);
                if (accApiReaderItem != null) {
                    accApiReaderItemList.add(accApiReaderItem);
                }
            });
        }
        return ApiResultMessage.successMessage(accApiReaderItemList);
    }
}
