<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信授权结果</title>
    <style>
        body { font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif; background: #f7f7f7; margin: 0; padding: 0; }
        .container { max-width: 400px; margin: 80px auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); padding: 32px 24px; text-align: center; }
        .result-icon { font-size: 64px; margin-bottom: 16px; }
        .success { color: #07c160; }
        .fail { color: #fa5151; }
        .msg { font-size: 20px; margin-bottom: 12px; }
        .desc { color: #888; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <!-- 登录表单 -->
        <div id="loginForm" style="display: block;">
            <h2 style="margin-bottom: 24px; color: #333;">员工登录</h2>
            
            <!-- 输入框区域 -->
            <div style="margin-bottom: 24px;">
                <div style="margin-bottom: 16px;">
                    <input type="text" id="employeeId" placeholder="请输入工号" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; box-sizing: border-box;">
                </div>
                <div style="margin-bottom: 24px;">
                    <input type="password" id="password" placeholder="请输入密码" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; box-sizing: border-box;">
                </div>
            </div>
            
            <!-- 按钮区域 -->
            <div style="display: flex; gap: 16px;">
                <button onclick="login()" style="flex: 1; padding: 12px; background: #07c160; color: white; border: none; border-radius: 4px; font-size: 16px; cursor: pointer;">登录</button>
                <button onclick="logout()" style="flex: 1; padding: 12px; background: #f8f9fa; color: #666; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; cursor: pointer;">注销</button>
            </div>
            
            <div id="loginError" style="color: #fa5151; font-size: 14px; margin-top: 12px; display: none;"></div>
        </div>
        
        <!-- 授权结果 -->
        <div id="resultContainer" style="display: none;">
            <div id="resultIcon" class="result-icon success">✔</div>
            <div id="resultMsg" class="msg">授权成功</div>
            <div id="resultDesc" class="desc">您已成功完成微信授权。</div>
            <div id="userInfo" style="margin-top: 16px; padding: 12px; background: #f8f9fa; border-radius: 4px; font-size: 12px; color: #666; display: none;">
                <div>用户信息：<span id="userInfoText"></span></div>
            </div>
        </div>
        
        <!-- 错误信息显示区域 -->
        <div id="errorContainer" style="display: none; margin-top: 16px; padding: 12px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px;">
            <div style="color: #856404; font-size: 14px; margin-bottom: 8px;">错误详情：</div>
            <div id="errorDetails" style="font-size: 12px; color: #856404; word-break: break-all;"></div>
        </div>
    </div>
    <script>
        // // 可通过URL参数?success=0/1控制页面显示
        // function getQueryParam(name) {
        //     const match = window.location.search.match(new RegExp('(?:[?&]' + name + '=)([^&]*)'));
        //     return match ? decodeURIComponent(match[1]) : null;
        // }
        // var success = getQueryParam('success');
        // if(success === '0') {
        //     document.getElementById('resultIcon').textContent = '✖';
        //     document.getElementById('resultIcon').className = 'result-icon fail';
        //     document.getElementById('resultMsg').textContent = '授权失败';
        //     document.getElementById('resultDesc').textContent = '请重试或联系管理员。';
        // }


        function urlencoded(str) {
            str = (str + '').toString();

            return encodeURIComponent(str).replace(/!/g, '%21').replace(/'/g, '%27').replace(/\(/g, '%28').replace(/\)/g, '%29').replace(/\*/g, '%2A').replace(/%20/g, '+');
        }

        // 获取URL参数的函数
        function getUrlParam(name) {
            const match = window.location.search.match(new RegExp('(?:[?&]' + name + '=)([^&]*)'));
            return match ? decodeURIComponent(match[1]) : null;
        }

        // 获取code参数
        const code = getUrlParam('code');
        if (code == null || code === '') {
            // 如果没有code参数，先获取appId，然后重定向到微信授权页面
            getAppIdAndRedirect();
        }
        // 如果有code参数，保存到全局变量，等待登录成功后使用
        window.wechatCode = code;
        
        // 获取appId并重定向到微信授权页面
        function getAppIdAndRedirect() {
            var xhr = new XMLHttpRequest();
            xhr.open('GET', '/api/v3/wechatAuth/getAppId', true);
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        const appId = response.data || 'wxc71eb85bdaee3f9e'; // 默认值
                        const local = window.location.href;
                        window.location.href = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' + appId + '&redirect_uri=' + urlencoded(local) + '&response_type=code&scope=snsapi_base&state=1#wechat_redirect';
                    } catch (e) {
                        console.error('获取appId失败:', e);
                        // 使用默认appId
                        const local = window.location.href;
                        window.location.href = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxc71eb85bdaee3f9e&redirect_uri=' + urlencoded(local) + '&response_type=code&scope=snsapi_base&state=1#wechat_redirect';
                    }
                }
            };
            xhr.send();
        }

        // 登录函数
        function login() {
            const employeeId = document.getElementById('employeeId').value.trim();
            const password = document.getElementById('password').value.trim();
            const loginError = document.getElementById('loginError');
            
            if (!employeeId || !password) {
                loginError.textContent = '请输入工号和密码';
                loginError.style.display = 'block';
                return;
            }
            
            // 发送登录请求
            var xhr = new XMLHttpRequest();
            xhr.open('POST', '/api/v3/wechatAuth/login', true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            console.log('登录响应:', response);
                            
                            // 检查响应格式，支持多种可能的成功标识
                            console.log('检查响应:', response);
                            console.log('response.success:', response.success);
                            console.log('response.successMessage:', response.successMessage);
                            console.log('response.code:', response.code);
                            console.log('response.ret:', response.ret);
                            console.log('response.data:', response.data);
                            
                            // 更严格的成功判断逻辑
                            if (response.success === true || 
                                response.successMessage === true || 
                                (response.code !== undefined && response.code === 0) ||
                                (response.ret === 'ok')) {
                                
                                // 检查是否有用户数据
                                if (response.data) {
                                    console.log('登录成功，用户信息:', response.data);
                                    // 显示用户信息
                                    document.getElementById('userInfo').style.display = 'block';
                                    const userInfoText = `工号: ${response.data.pin || 'N/A'}, 姓名: ${response.data.name || 'N/A'}, 部门: ${response.data.deptName || 'N/A'}`;
                                    document.getElementById('userInfoText').textContent = userInfoText;
                                } else {
                                    console.log('登录成功，但无用户数据');
                                    // 显示错误信息
                                    document.getElementById('errorContainer').style.display = 'block';
                                    document.getElementById('errorDetails').textContent = '登录成功但未返回用户数据，请检查后端接口';
                                }
                                
                                // 登录成功，隐藏登录表单，显示结果
                                document.getElementById('loginForm').style.display = 'none';
                                document.getElementById('resultContainer').style.display = 'block';
                                
                                // 如果有微信code，则获取openid
                                if (window.wechatCode) {
                                    getOpenId(window.wechatCode);
                                }
                            } else {
                                console.log('登录失败，错误信息:', response.message || response.msg);
                                // 显示详细错误信息
                                document.getElementById('errorContainer').style.display = 'block';
                                document.getElementById('errorDetails').textContent = `错误代码: ${response.code || 'N/A'}, 错误信息: ${response.message || response.msg || '未知错误'}`;
                                
                                loginError.textContent = response.message || response.msg || '登录失败';
                                loginError.style.display = 'block';
                            }
                        } catch (e) {
                            console.error('解析响应失败:', e);
                            console.log('原始响应文本:', xhr.responseText);
                            
                            // 显示解析错误信息
                            document.getElementById('errorContainer').style.display = 'block';
                            document.getElementById('errorDetails').textContent = `JSON解析失败: ${e.message}, 原始响应: ${xhr.responseText || '无响应内容'}`;
                            
                            loginError.textContent = '登录失败，请重试';
                            loginError.style.display = 'block';
                        }
                    } else {
                        console.log('网络请求失败，状态码:', xhr.status);
                        // 显示网络错误信息
                        document.getElementById('errorContainer').style.display = 'block';
                        document.getElementById('errorDetails').textContent = `网络请求失败，状态码: ${xhr.status}, 响应文本: ${xhr.responseText || '无响应内容'}`;
                        
                        loginError.textContent = '登录失败，请重试';
                        loginError.style.display = 'block';
                    }
                }
            };
            xhr.send(JSON.stringify({ 
                employeeId: employeeId, 
                password: password 
            }));
        }
        
        // 通过ajax请求后端接口获取openid
        function getOpenId(code) {
            var xhr = new XMLHttpRequest();
            xhr.open('POST', '/api/v3/wechatAuth/getOpenId', true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        console.log('openid获取成功');
                    } else {
                        console.log('openid获取失败');
                    }
                }
            };
            const employeeId = document.getElementById('employeeId').value.trim();


            xhr.send(JSON.stringify({ code: code,pin:employeeId }));
        }

        // 注销功能
        function logout() {
            const employeeId = document.getElementById('employeeId').value.trim();
            const password = document.getElementById('password').value.trim(); // 复用密码输入框
            
            if (!employeeId) {
                alert('请先输入工号');
                return;
            }

            if (!password) {
                alert('请输入密码进行注销');
                return;
            }
            
            // 发送注销请求到后端
            var xhr = new XMLHttpRequest();
            xhr.open('POST', '/api/v3/wechatAuth/logout', true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.success || response.successMessage || (response.code !== undefined && response.code === 0)) {
                                // 注销成功，重置页面状态
                                resetPageState();
                                alert('注销成功！');
                            } else {
                                alert('注销失败：' + (response.message || response.msg || '未知错误'));
                            }
                        } catch (e) {
                            alert('注销失败：响应解析错误');
                        }
                    } else {
                        alert('注销失败：网络请求错误');
                    }
                }
            };
            xhr.send(JSON.stringify({ employeeId: employeeId, password: password }));
        }
        
        // 重置页面状态
        function resetPageState() {
            // 显示登录表单
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('resultContainer').style.display = 'none';
            document.getElementById('errorContainer').style.display = 'none';
            document.getElementById('userInfo').style.display = 'none';
            document.getElementById('loginError').style.display = 'none';
            
            // 清空输入框
            document.getElementById('employeeId').value = '';
            document.getElementById('password').value = '';
            
            // 清除微信code
            window.wechatCode = null;
        }

    </script>
</body>
</html> 