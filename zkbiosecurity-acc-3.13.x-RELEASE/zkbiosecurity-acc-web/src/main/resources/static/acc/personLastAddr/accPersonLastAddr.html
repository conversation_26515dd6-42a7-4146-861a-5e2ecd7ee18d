<#assign gridName="accPersonLastAddrGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}" style="height:100%;width:100%">
	<@ZKUI.Searchbar>
		<@ZKUI.SearchTop>
			<tr>
				<td valign="middle">
					<@i18n 'common_time_from'/>&nbsp;&nbsp;
					<@ZKUI.Input type="datetime" endId="endTimeId${uuid}" id="startTime${uuid}" name="startTime" title="common_time_from" max="today" todayRange="start" today="-3" hideLabel="true" noOverToday="true" readonly="false"/>
					&nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
					<@ZKUI.Input type="datetime" id="endTimeId${uuid}" name="endTime" title="common_to" max="today" today="true" todayRange="end" noOverToday="true" hideLabel="true" readonly="false"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="pin" maxlength="30" title="pers_person_pin" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="devAlias" maxlength="30" title="common_dev_name" type="text"/>
				</td>
			</tr>
		</@ZKUI.SearchTop>
		<@ZKUI.SearchBelow>
			<tr>
				<td valign="middle">
					<@ZKUI.Input name="deptCode" maxlength="30" title="pers_dept_deptNo" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="deptName" maxlength="30" title="pers_dept_deptName" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.ComboTree url="/accDeviceEvent.do?tree" title="common_eventDescription" name="eventName" readonly="readonly"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="cardNo" maxlength="30" title="pers_card_cardNo" type="text"/>
				</td>
			</tr>
			<tr>
				<td valign="middle">
					<@ZKUI.Input name="readerName" maxlength="30" title="acc_readerDefine_readerName" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="areaName" maxlength="30" title="base_area_name" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="eventPointName" maxlength="30" title="common_eventPoint" type="text"/>
				</td>
			</tr>
			<tr>
				<td valign="middle">
					<@ZKUI.Input name="likeName" maxlength="30" title="pers_person_wholeName" type="text"/>
				</td>
			</tr>
		</@ZKUI.SearchBelow>
	</@ZKUI.Searchbar>
	<@ZKUI.Toolbar>
		<@ZKUI.ToolItem id="refresh" type="refresh" permission="acc:personLastAddr:refresh"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="/accPersonLastAddr.do?clearData" action="accClearPersonLastAddrData" text="common_op_clearData" img="comm_del.png" permission="acc:personLastAddr:del"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="/accPersonLastAddr.do?export" type="export" permission="acc:personLastAddr:export"></@ZKUI.ToolItem>
	</@ZKUI.Toolbar>
	<@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccPersonLastAddrItem" query="/accPersonLastAddr.do?list" fixColumn="7"/>
</@ZKUI.GridBox>
<script type="text/javascript">
	/**
	 *下拉树的树对象选中事件
	 */
	function onTreeChecked(id, state) {
		var comboTreeId = this.treeId;//获取下拉树id
		var comboTree = ZKUI.ComboTree.get(comboTreeId);//获取下拉树对象
		var combo = comboTree.combo;//获取下拉树的下拉框对象
		var tree = comboTree.tree;//获取下拉树的树对象
	}

    var gridName = "${gridName}";
    function accClearPersonLastAddrData(id, bar) {
        deleteConfirm(function(result) {
            if (result) {
                openMessage(msgType.loading);
                $.ajax({
                    url : id,
                    type : "post",
                    success : function(result) {
                        dealRetResult(eval(result), function() {
                            closeMessage();
                            ZKUI.Grid.reloadGrid(gridName);
                        });
                    }
                });
            }
        }, "<@i18n 'common_prompt_sureToDelAll'/>");

    }

    //添加链接
	function addHref(v) {
        return "<a href='javascript:redirectToAccMap(("+v+"))'>"+v+"</a>";
    }

    //弹出电子地图窗体
    function redirectToAccMap(pin)
    {
        $.ajax({
            type: "POST",
            url: "/accPersonLastAddr.do?getPersonLastAddrByPin",
            async: true,
            data:{"pin":pin},
            success: function(result)
            {
                var data = result.data;
                var accDoorId = data.accDoorId;
                var mapId = data.accMapId;
                var mapPosId = data.accMapPosId;
                var isSuperUser = data.isSuperUser;
                var isAccMapAccess = data.isAccMapAccess;
                var isAreaAccess = data.isAreaAccess;
                if(isSuperUser)
                {
                    openAccMap(mapId, mapPosId, accDoorId);
                }
                else
                {
                    if(isAccMapAccess)
                    {
                        if(isAreaAccess)
						{
                            openAccMap(mapId, mapPosId, accDoorId);
                        }
                        else
						{
                            var text = "<@i18n 'acc_map_noAreaAccess'/>";
                            messageBox({messageType:"alert",text:text});
						}
                    }
                    else
                    {
						var text = "<@i18n 'acc_map_noAccess'/>";
						messageBox({messageType:"alert",text:text});
                    }
                }
            }
        });
    }

    //打开电子地图
    var windowOpen = null;
    function openAccMap(mapId, mapPosId, accDoorId)
    {
        if(mapId != null && mapPosId != null)
        {
            if(windowOpen != null)
            {
                windowOpen.close();
            }
            var path = "/skip.do?page=acc_map_accMap&mapPosId=" + mapPosId + "&idMap="+ mapId +"&accDoorId="+ accDoorId +"&alarmHerfExist=1&isPersonLastAddr=1";
            var opts = {
                path:path,
                width:1300,
				height:700,
				title:"<@i18n 'acc_leftMenu_electronicMap'/>",
				gridName:"gridbox"
            }
            DhxCommon.createWindow(opts);
            // windowOpen = window.open(path,'ZKBiosecurity','height=700, width=1300, top=50, left=130, toolbar=no, menubar=no, scrollbars=no, resizable=no, location=no, status=no');
        }
        else
        {
            var text = "<@i18n 'acc_location_unable'/>";
            messageBox({messageType:"alert",text: text});
        }
    }
</script>