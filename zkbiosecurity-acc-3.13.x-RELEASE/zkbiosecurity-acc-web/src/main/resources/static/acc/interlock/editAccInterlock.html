<#assign formId = "${uuid!}">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
<form action='/accInterlock.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
	<input type='hidden' name='id' value='${(item.id)!}'/>
	<table class='tableStyle'>
		<tr>
			<th>
				<label><@i18n 'common_name'/></label><span class="required">*</span>
			</th>
			<td>
				<input type="text" value="${(item.name)!}" name="name" style="width:261px">
			</td>
		</tr>
		<tr>
			<th>
				<label><@i18n 'common_dev_name'/></label><span class="required">*</span>
			</th>
			<td>
				<#if (item.id)??>
				<input type="text" id="deviceName" name="deviceName" style="width:262px" value="${(item.deviceName)!}" readonly="readonly"/>
				<#else>
				<input type="text" id="deviceName" name="deviceName" style="width:262px" value="${(item.deviceName)!}" readonly="true" placeholder="<@i18n 'common_op_clickChoice'/>"/>
				</#if>
				<input type="hidden" id="deviceId" onchange="changeDevice(this.value)" name="deviceId" value="${(item.deviceId)!}"/>
			</td>
		</tr>
		<tr class="interlockRule1">
			<th>
				<label><@i18n 'acc_interlock_rule'/></label><span class="required">*</span>
			</th>
			<td>
				<@ZKUI.Combo name="interlockRule" id="interlockRule${uuid!}" readonly="true" width="268" value="${(item.interlockRule)!}" title="acc_interlock_rule" hideLabel="true" empty="true">
				</@ZKUI.Combo>
			</td>
		</tr>
		<tr class="interlockRule2">
			<th>
				<label><@i18n 'acc_interlock_rule'/></label>
			</th>
			<td>
				<@ZKUI.Combo name="lockRule" width="267" onChange="typeChange" readonly="true" empty="false" hideLabel="true" id="interlockStatus" value="${(item.lockRule)!}">
					<option value = "1"><@i18n 'acc_interlock_ruleInfo'/></option>
					<option value = "2"><@i18n 'acc_globalInterlock_isGroupInterlock'/></option>
				</@ZKUI.Combo>
			</td>
		</tr>
	</table>
	<fieldset style="margin-top: 10px" class="setType1">
		<legend><@i18n 'acc_interlock_group1'/></legend>
		<textarea style="width: 100%; height: 60px; cursor:pointer" onclick="selectDoor('1');return false;" readonly id="group1Names" placeholder="<@i18n 'common_op_clickChoice'/>" name="group1DoorNames">${(item.group1DoorNames)!}</textarea>
		<input type="hidden" name="group1DoorIds" id="group1DoorIds" value="${(item.group1DoorIds)!}">
		<div style="width: 100%; margin-top: 10px">
			<div style="float: right;">
				<button class="button-form editButton" onclick="selectClean('1');return false;"><@i18n 'common_op_clearData'/></button>
			</div>
		</div>
	</fieldset>

	<fieldset style="margin-top: 10px" id="group2Set" class="setType2">
		<legend><@i18n 'acc_interlock_group2'/></legend>
		<textarea style="width: 100%; height: 60px; cursor:pointer" onclick="selectDoor('2');return false;" readonly id="group2Names" placeholder="<@i18n 'common_op_clickChoice'/>" name="group2DoorNames">${(item.group2DoorNames)!}</textarea>
		<input type="hidden" name="group2DoorIds" id="group2DoorIds" value="${(item.group2DoorIds)!}">
		<div style="width: 100%; margin-top: 10px">
			<div style="float: right">
				<button class="button-form editButton" onclick="selectClean('2');return false;"><@i18n 'common_op_clearData'/></button>
			</div>
		</div>
	</fieldset>
</form>
<script type='text/javascript'>
    $(function() {
    	<#if !(item.id)??>
        var opts = {//弹窗配置对象
            path: "skip.do?page=acc_interlock_accInterlockSelectDeviceRadio",//设置弹窗路径
            width: 920,//设置弹窗宽度
            height: 480,//设置弹窗高度
            title: "",//设置弹窗标题
            onSure:"selectDeviceHandler"//回调事件
        };
        selectContent("#${formId} #deviceName", opts);//给按钮注册选人事件
        </#if>
        var lockState = getInterLockState();
        ZKUI.Combo.get('interlockStatus').combo.selectOption(lockState);

        $('#${formId}').validate( {
            debug : true,
            rules : {
				"name":{
                    required : true,
                    unInputChar : true,
                    maxlength : 30,
                    overRemote : ["/accInterlock.do?validName", "${(item.name)!}"]
				},
                "deviceName":{
                    required : true
                }
            },
            submitHandler : function()
            {
            	<@submitHandler/>
            }
        });
        changeDevice("${(item.deviceId)!}", true);
    });

    $("#${formId} #deviceId").change(function() {
        if($("#${formId} #deviceId").val() != "")
        {
            ZKUI.Combo.get("interlockRule${uuid!}").load("accInterlock.do?getRule&deviceId="+$("#${formId} #deviceId").val());
        }
    });

    //渲染当前的互锁模式
    if($("#${formId} #deviceId").val() != "") {
        ZKUI.Combo.get("interlockRule${uuid!}").load("accInterlock.do?getRule&deviceId="+$("#${formId} #deviceId").val());
    }
    <#if (item.id)??>
		$("#${formId} #deviceName").attr("disabled", true);
		<#if item.lockRule??>
			$(".interlockRule1").hide();
			$(".interlockRule2").show();
		<#else>
			$(".interlockRule2").hide();
			$(".interlockRule1").show();
			$(".setType1").hide();
			$(".setType2").hide();
		</#if>
	</#if>

     function getInterLockState() {
        var group2Names = "${(item.group2DoorNames)!}";
        if(group2Names.trim() == null || group2Names.trim() == "") {
            return 1;
        }
        return 0;
    }

    function selectDeviceHandler(value,text,event) {
        var oldId = $("#${formId} #deviceId").val();
        $("#${formId} #deviceId").val(value);
        if (oldId != value)
        {
            $("#${formId} #deviceId").change();
        }
        $("#${formId} #deviceName").val(text);
        $("#${formId} #deviceId").valid();
        autofillGroup(value);
    }

    function validDetermineInterlock(devId){
        if (devId != "") {
            $.ajax({
                type: "post",
                url: "accInterlock.do?validDetermineInterlock",
                dataType: "json",
                async :false,
                data:
				{
					'deviceId' : devId
				},
                success: function (result)
                {
					if(result.data == false) {
						$(".interlockRule1").show();
						$(".interlockRule2").hide();
						$(".setType1").hide();
						$(".setType2").hide();
						$(':input[name="interlockRule"]').rules("add", {required : true});
						$("#group1Names").rules("remove");
						$("#group2Names").rules("remove");
					}else{
						$(".interlockRule2").show();
						$(".interlockRule1").hide();
						$(".setType1").show();
						<#if (item.id)?? && (item.lockRule)?? && item.lockRule == 2>
						$(".setType2").hide();
						</#if>
						<#if (item.id)??>
						ZKUI.Combo.get("interlockStatus").combo.disable(true);
						</#if>
						$(':input[name="interlockRule"]').rules("remove");
						$("#group1Names").rules("add", {required : true,groupInterlockValid : true});
						$("#group2Names").rules("add", {group2Valid : true});
					}
                },
                error: function (XMLHttpRequest, textStatus, errorThrown)
                {
                    messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
                }
            });
		}
   	}

   	//清除所选数据
	function selectClean(groupStr) {
		group = groupStr;
		var lockRule = ZKUI.Combo.get('interlockStatus').combo.getSelected();
		if(lockRule == 2){
			if(group == "1") {
				if($("#group1DoorNames").val() == "") {
					return  "<@i18n 'common_prompt_dataNull'/>";
				}
				$("#group1Names").val("");
				$("#group1DoorIds").val("");
			}
		}else {
			if(group == "1") {
				if($("#group1DoorNames").val() == "") {
					return  "<@i18n 'common_prompt_dataNull'/>";
				}
				$("#group1Names").val("");
				$("#group1DoorIds").val("");
			}else if(group == "2") {
				if($("#group2DoorNames").val() == "") {
					return  "<@i18n 'common_prompt_dataNull'/>";
				}
				$("#group2Names").val("");
				$("#group2DoorIds").val("");
			}
		}
	}
	function selectDoor(groupStr) {
        group = groupStr;
        var filterId = groupStr=="1" ? "2" : "1";
        var deviceId = $("#deviceId").val();
        var selectStr = "input[name='group" + filterId +"DoorIds']";
        var notInId = $(selectStr).val();
        if(deviceId!=null && deviceId.trim()!="") {
            var path = "/accInterlock.do?selectDoor&deviceId=" + deviceId + "&notInId=" + notInId + "&group=" + groupStr + "&interLockId=${(item.id)!}";
            var opts = {
                path:path,
                width: 880,
                height: 510,
                title:"<@i18n 'acc_map_addDoor'/>",
                gridName:"gridBox"
            };
            DhxCommon.createWindow(opts);
		}
    }

    function selectDoorHandler(value,text) {
        if(group == "1") {
            $("input[name='group1DoorIds']").val(value);
            $("#group1Names").val(text);
            var type = ZKUI.Combo.get('interlockStatus').combo.getSelectedValue();
            if(type + "" == "1") {
                validByOtherNotNull("input[name='group2DoorIds']");
			}else {
                $('#${formId}').valid();
			}

		}else if(group == "2") {
            $("input[name='group2DoorIds']").val(value);
            $("#group2Names").val(text);
            validByOtherNotNull("input[name='group1DoorIds']");
        }
        //$('#${formId}').valid()
	}

	function changeDevice(devId, noClear) {
        if(devId==null || devId=="") {
		    $(".editButton").attr("disabled", "disabled");
		    $(".setType1").hide();
			$(".setType2").hide();
			$(".interlockRule2").hide();
		}else {
			validDetermineInterlock(devId);
            $(".editButton").removeAttr("disabled", "disabled");
            $(".lockRule").removeAttr("disabled", "disabled");
            if(noClear == null || !noClear) {
                $("input[name='group1DoorIds']").val("");
                $("#group1Names").html("");
                $("input[name='group2DoorIds']").val("");
                $("#group2Names").html("");
			}
		}
    }

    function typeChange(val) {
		switch (val + '') {
			case '1':
			    $("#group2Set").show();
                $("#group2Set input").removeAttr("disabled", "disabled");
                $("#group1DoorIds").val("");
                $("#group2DoorIds").val("");
                $("#group1Names").val("");
                $("#group2Names").val("");
			    break;
			case '2':
			    var group2Names = "${(item.group2DoorNames)!}";
                if(group2Names.trim() == null || group2Names.trim() == "") {
                    $("#group2Set").hide();
                    $("#group2Set input").attr("disabled", "disabled");
                    $("#group2DoorIds").val("");
                    $("#group2Names").val("");
                }else{
                    $("#group2Set").hide();
                    $("#group2Set input").attr("disabled", "disabled");
                    $("#group1DoorIds").val("");
                    $("#group2DoorIds").val("");
                    $("#group2Names").val("");
                }
                break;
		}
    }

    function validByOtherNotNull(id) {
        if($(id).val().trim() != "") {
            $('#${formId}').valid();
        }
    }

    function autofillGroup(id) {
        $.ajax({
            type: "POST",
            url: "/accInterlock.do?getAutoFillDoorByDev",
            data:{
                devId: id
            },
            success: function(retData)
            {
                if(retData.ret == "ok")
                {
                    var res = retData.data;
                    if(res != null && res.length == 2) {
                        ZKUI.Combo.get('interlockStatus').combo.selectOption("0");
                        $("input[name='group1DoorIds']").val(res[0].id);
                        $("input[name='group2DoorIds']").val(res[1].id);
                        $("#group1Names").html(res[0].name);
                        $("#group2Names").html(res[1].name);
                    }
                }
            }
        });
    }

    jQuery.validator.addMethod("groupInterlockValid", function (value, element) {
		var type = ZKUI.Combo.get('interlockStatus').combo.getSelectedValue();
		var ids = $("input[name='group1DoorIds']").val();
		if(type == "2"){
			if(ids.indexOf(",") != -1){
				return true;
			} else {
				return false;
			}
		} else {
			return true;
		}
	},function(){
        return  "<@i18n 'acc_interlock_groupInterlockCountErr'/>";
    });

	jQuery.validator.addMethod("group2Valid", function (value, element) {
    	 var type = ZKUI.Combo.get('interlockStatus').combo.getSelectedValue();
    	 var door2Ids = $("input[name='group2DoorIds']").val();
    	 if(type == "1"){
			if(door2Ids != ""){
				return true;
			} else {
				return false;
			}
		} else {
			return true;
		}
	},function(){
        return  "<@i18n 'common_jqMsg_required'/>";
    });
</script>
</#macro>