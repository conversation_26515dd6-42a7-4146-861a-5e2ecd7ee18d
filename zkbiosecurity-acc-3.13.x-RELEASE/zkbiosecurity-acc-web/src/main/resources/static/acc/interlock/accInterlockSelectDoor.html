<@ZKUI.SelectContent gridName="interLockSelectDoorContent" showColumns="checkbox,doorName,deviceAlias,deviceSn" textField="doorName" vo="com.zkteco.zkbiosecurity.acc.vo.AccInterlockSelectDoorItem" onSure="selectDoorHandler" query="/accInterlock.do?selectDoorlist&deviceId="+deviceId+"&notInId="+notInId + "&group=" + group + "&interLockId=" + interLockId>
<@ZKUI.Searchbar>
<@ZKUI.SearchTop>
<tr>
    <td valign="middle">
        <@ZKUI.Input name="doorName"  maxlength="30" title="acc_door_name" type="text"/>
    </td>
    <td valign="middle">
        <@ZKUI.Input name="deviceSn"  maxlength="15" title="common_dev_sn" type="text"/>
    </td>
</tr>
</@ZKUI.SearchTop>
</@ZKUI.Searchbar>
</@ZKUI.SelectContent>
