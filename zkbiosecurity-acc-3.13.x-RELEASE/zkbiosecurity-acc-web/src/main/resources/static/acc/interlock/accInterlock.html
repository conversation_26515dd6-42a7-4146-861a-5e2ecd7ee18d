<#assign gridName="accInterlockGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
				<td valign="middle">
					<@ZKUI.Input name="deviceName"  maxlength="30" title="common_dev_name" type="text"/>
				</td>
			</tr>
    	</@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="acc:interlock:refresh"/>
    	<@ZKUI.ToolItem id="/accInterlock.do?edit" width="520" height="500" text="common_op_new" img="comm_add.png" action="commonAdd" permission="acc:interlock:add"/>
    	<@ZKUI.ToolItem id="/accInterlock.do?del&deviceNames=(deviceName)" text="common_op_del" img="comm_del.png" action="commonDel" permission="acc:interlock:del"/>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccInterlockItem" query="/accInterlock.do?list"/>
</@ZKUI.GridBox>