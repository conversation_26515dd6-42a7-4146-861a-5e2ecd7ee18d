<#assign editPage="true">
<#assign formId = "${uuid!}">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
<script type='text/javascript'>
    $(function() {
        $('#${formId}').validate( {
            debug : true,
            rules :
                {
                    'name' :
                        {
                            required : true,
                            unInputChar:true,
                            overRemote : ["accCombOpenPerson.do?valid", "${(item.name)!}"]
                        }
                },
            submitHandler : function()
            {
            <@submitHandler/>
            }
        });
    });
</script>

<form action='/accCombOpenPerson.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
	<input type='hidden' name='id' value='${(item.id)!}'/>
	<table class='tableStyle'>
		<tr>
			<th><label><@i18n 'acc_combOpen_personGroupName'/></label><span class='required'>*</span></th>
			<td><input name='name' type='text' value='${(item.name)!}' maxlength="30"/></td>
		</tr>
		<tr>
			<th><label><@i18n 'common_remark'/></label></th>
			<td><input name='remark' type='text' value='${(item.remark)!}' maxlength="30"/></td>
		</tr>
	</table>
</form>
</#macro>