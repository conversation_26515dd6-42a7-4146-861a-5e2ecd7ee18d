<#assign gridName="accCombOpenPersonGrid${uuid!}">
<script type="text/javascript">
    function accCombOpenPersonLeftGridClick(rid) {
        //此方法注册的是官方事件，所以this指代的是官方grid对象，通过访问属性zkgrid获取我们自定义的grid对象
        var leftGrid = this.zkgrid;//获取左表格
        var dbGrid = ZKUI.DGrid.get(leftGrid.options.dGridName);//双列表对象
        var rightGrid = dbGrid.rightGrid;//右表格对象
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        rightGrid.reload(function() {
            rightCombOpenCallback(row.id, rightGrid, rid);//手动回调
        },{linkId:rid});//传递id到右表格查询
    }

    //右表格回调事件
    function rightCombOpenCallback(id, rightGrid, rid) {
        var dbGrid = ZKUI.DGrid.get(rightGrid.options.dGridName);//双列表对象
        var leftGrid = dbGrid.leftGrid;//获取左表格
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        // rightGrid.setTitle("已选部门:"+row.name);//设置右表格标题
    }

    function afterAddCombOpenPerson(value,text,event) {
        var combOpenPersonId = this.options.linkId;
        var combOpenName = this.options.linkName;
        var personDeptTree="accPersonDeptTree"+this.options.uuid;
        var deptIds = ZKUI.ComboTree.get(personDeptTree).getValue();
        if(deptIds != undefined && deptIds != null && deptIds != ""){
            var personCount = accGetPersonCountByDeptIds(deptIds);
            if (parseInt(personCount) == 0){
                messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'pers_widget_noDeptPerson'/>"});
                return false;
            }
            if(text == "") {
            	text = ZKUI.ComboTree.get(personDeptTree).getText();
            }
        }
        onLoading(function(){
            $.ajax({
                url:"accCombOpenPerson.do?addPerson",
                type:"post",
                dataType:"json",
                data:{
                    "combOpenPersonId" : combOpenPersonId,
                    "personIds" : value,
                    "deptIds" : deptIds,
                    //下面的参数是为了添加系统日志新增的，还待改善。
                    "combOpenName" : combOpenName,
                    "personPins" : text
                },
                success:function (result) {
                    openMessage(msgType.success);
                    var dbGrid = ZKUI.DGrid.get("${gridName}");
					var rightGrid = dbGrid.rightGrid;
					rightGrid.reload();
                }
            });
        });
    }

    /*function delCombOpenPerson(){
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        var leftGrid = dbGrid.leftGrid;
        var rightGrid = dbGrid.rightGrid;
        var leftGridName = leftGrid.gridName || "gridbox";
        var rightGridName = rightGrid.gridName || "gridbox";
        var leftIds = ZKUI.Grid.GRID_PULL[leftGridName].grid.getSelectedRowId();
        var rightIds = ZKUI.Grid.GRID_PULL[rightGridName].grid.getCheckedRows(0);
        if(leftIds=="" || leftIds ==null || rightIds=="" || rightIds==null ){
            messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});
        }else {
            deleteConfirm(function(result){
                if(result){
                    $.ajax({
                        url: "accCombOpenPerson.do?delPerson",
                        type: "post",
                        dataType: "json",
                        data: {
                            "combOpenId": leftIds,
                            "personIds": rightIds
                        },
                        success: function (result) {
                            dealRetResult(eval(result),
                                function(){
                                    var dbGrid = ZKUI.DGrid.get("${gridName}");
                                    var leftGrid = dbGrid.leftGrid;
                                    var rightGrid = dbGrid.rightGrid;
                                    leftGrid.reload();
                                    rightGrid.reload();
                            })
                        }
                    });
                }
            },"common_prompt_sureToDelThese");
        }
    }*/
</script>
<@ZKUI.DGrid gridName="${gridName}">
	<@ZKUI.LeftGrid title="acc_combOpen_changeLevel">
		<@ZKUI.Searchbar>
			<@ZKUI.SearchTop>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="name" maxlength="30" title="acc_combOpen_personGroupName" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Input name="remark" maxlength="50" title="common_remark" type="text"/>
					</td>
				</tr>
			</@ZKUI.SearchTop>
		</@ZKUI.Searchbar>
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="acc:combOpenPerson:refresh"/>
			<@ZKUI.ToolItem id="accCombOpenPerson.do?edit" text="common_op_new" width="420" height="175" img="comm_add.png" action="commonAdd" permission="acc:combOpenPerson:add"/>
			<@ZKUI.ToolItem id="accCombOpenPerson.do?del&names=(name)" text="common_op_del" img="comm_del.png" action="accDGridCommonDel" permission="acc:combOpenPerson:del"/>
		</@ZKUI.Toolbar>
		<!-- 配置左表格选中事件 -->
		<@ZKUI.Grid onRowSelect="accCombOpenPersonLeftGridClick" vo="com.zkteco.zkbiosecurity.acc.vo.AccCombOpenPersonItem" query="accCombOpenPerson.do?list" autoFirst="true"/>
	</@ZKUI.LeftGrid>
	<@ZKUI.RightGrid title="pers_common_browsePerson" leftFieldName="linkId">
		<@ZKUI.Searchbar>
			<@ZKUI.SearchTop>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="personPin" maxlength="30" title="pers_person_pin" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Input name="likeName" maxlength="24" title="pers_person_wholeName" type="text"/>
					</td>
				</tr>
			</@ZKUI.SearchTop>
			<@ZKUI.SearchBelow>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="deptName" maxlength="30" title="pers_dept_deptName" type="text"/>
					</td>
				</tr>
			</@ZKUI.SearchBelow>
		</@ZKUI.Searchbar>
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem type="refresh" permission="acc:combOpenPerson:refresh"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="accCombOpenPerson.do?delPerson&combOpenId=(@id:gs)&combOpenName=(@name:gs)&personIds=(id)&personPins=(personPin)" accModuleType="person" action="accRightGridCommonDel" text="pers_common_delPerson" img="comm_del.png" permission="acc:combOpenPerson:delPerson"/>
		</@ZKUI.Toolbar>
		<@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccPersonCombOpenPersonItem" query="accCombOpenPerson.do?personList"/>
	</@ZKUI.RightGrid>
</@ZKUI.DGrid>
