<#assign formId="params${uuid!}">
<#include "/public/template/setTemplate.html">
<#macro setContent>
<link href="css/sidenavFieldset.css" rel="stylesheet" type="text/css" />
<script src="js/sidenavFieldset.js"></script>
<style type="text/css">
	.setFormTemplate .marginBottom24 {
		margin-bottom: 24px;
	}
	.setFormTemplate .marginBottom16 {
		margin-bottom: 16px;
	}
	.setFormTemplate .marginBottom12 {
		margin-bottom: 12px;
	}
	.setFormTemplate .mode2_checkbox > div {
		width: 75px;
	}
</style>
<div id="setTemplate">
    <div class="setFormTemplate">
        <form action="accParam.do?save" id="${formId}" onkeydown="if(event.keyCode==13){return false;}" method="post">
            <div style="position:relative; border:0px solid #71A8D8; height: 100%; width: 100%;">
                <div>
                    <fieldset>
                        <legend><@i18n 'common_param_typeOfGetTrans' /></legend>
                        <div>
                            <div class="marginBottom24">
                                <@ZKUI.Input hideLabel="true" type="radio" name="down_event_mode" id="down_event_mode1" value="mode1"/>&nbsp;<@i18n 'common_param_periodicallyObtain' />
                            </div>
                            <div class="marginBottom16">
                                <@i18n 'common_param_interval' />
                            </div>
                            <div class="marginBottom24">
                                <@ZKUI.Combo hideLabel="true" width="400" empty="false" id="regular_time" name="regular_time" disable="true" followType="close" followScroll=".content_div">
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                    <option value="5">5</option>
                                    <option value="6">6</option>
                                    <option value="7">7</option>
                                    <option value="8">8</option>
                                </@ZKUI.Combo>
                                <label><@i18n 'common_hours' /></label>
                            </div>
                        </div>
                        <div class="marginBottom16">
                            <@ZKUI.Input hideLabel="true" type="radio" name="down_event_mode" id="down_event_mode2" value="mode2"/>&nbsp;<@i18n 'common_param_timedObtain' />
                            <input class="button-form" style="min-width: 60px;" id="checkAll" name="checkAll" type="button" value="<@i18n 'common_tree_selectAll' />">
                            <input class="button-form" style="min-width: 60px;" id="cancel" name="cancel" type="button" value="<@i18n 'common_edit_cancel' />">
                        </div>
                        <div id="span_mode2" class="marginBottom12">
                            <div>
                                <div class="mode2_checkbox marginBottom24" style="display:flex;">
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="0" name="mode2_value"/>&nbsp;0:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="1" name="mode2_value"/>&nbsp;1:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="2" name="mode2_value"/>&nbsp;2:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="3" name="mode2_value"/>&nbsp;3:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="4" name="mode2_value"/>&nbsp;4:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="5" name="mode2_value"/>&nbsp;5:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="6" name="mode2_value"/>&nbsp;6:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="7" name="mode2_value"/>&nbsp;7:00</div>
                                </div>
                                <div class="mode2_checkbox marginBottom24" style="display:flex;">
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="8" name="mode2_value"/>&nbsp;8:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="9" name="mode2_value"/>&nbsp;9:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="10" name="mode2_value"/>&nbsp;10:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="11" name="mode2_value"/>&nbsp;11:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="12" name="mode2_value"/>&nbsp;12:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="13" name="mode2_value"/>&nbsp;13:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="14" name="mode2_value"/>&nbsp;14:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="15" name="mode2_value"/>&nbsp;15:00</div>
                                </div>
                                <div class="mode2_checkbox" style="display:flex;">
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="16" name="mode2_value"/>&nbsp;16:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="17" name="mode2_value"/>&nbsp;17:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="18" name="mode2_value"/>&nbsp;18:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="19" name="mode2_value"/>&nbsp;19:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="20" name="mode2_value"/>&nbsp;20:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="21" name="mode2_value"/>&nbsp;21:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="22" name="mode2_value"/>&nbsp;22:00</div>
                                    <div><@ZKUI.Input hideLabel="true" type="checkbox" value="23" name="mode2_value"/>&nbsp;23:00</div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="warningImage"></div><div class="warningColor"><@i18n 'common_param_getTransTips' /></div>
                        </div>
                    </fieldset>
                </div>
                <!-- 信息自动导出 -->
                <div>
                    <fieldset>
                        <legend><@i18n 'acc_autoExport_title' /></legend>
                        <div>
                            <div class="marginBottom24">
                                <div style="width: 100px;text-align: ${leftRTL!'left'}"><@i18n 'acc_autoExport_frequencyTitle'/></div>
                            </div>
                            <div>
                                <@ZKUI.Combo hideLabel="true" width="400" empty="false" id="frequencySelect" name="acc.autoExportFrequency" onChange="changeFrequency" followType="close" followScroll=".content_div">
                                    <option value="0"><@i18n 'acc_autoExport_frequencyDay' /></option>
                                    <option value="1"><@i18n 'acc_autoExport_frequencyMonth' /></option>
                                    <option value="2"><@i18n 'common_none' /></option>
                                </@ZKUI.Combo>
                            </div>
                        </div>
                        <div>
                            <div class="monthFre" style="margin-left: 100px; margin-top: 24px;">
                               <div>
                                    <@ZKUI.Input hideLabel="true" type="radio" name="acc.monthFrequency" id="monthFrequency0" value="0"/>
                                    <label><@i18n 'acc_autoExport_firstDayMonth' /></label>
                                </div>
                            </div>
                            <div class="monthFre marginBottom24" style="margin-left: 100px;">
                               <div>
                                    <@ZKUI.Input hideLabel="true" type="radio" name="acc.monthFrequency" id="monthFrequency1" value="1"/>
                                        <@i18n 'acc_autoExport_specificDate' />：
                                        <#assign months=1..28/>
                                        <@ZKUI.Combo hideLabel="true" width="55" empty="false" id="idMonthFrequencyDate" name="acc.monthFrequencyDate" followType="close" followScroll=".content_div">
                                            <#list months as month>
                                                <#if month<10>
                                                    <option value="${month}">0${month}</option>
                                                <#else>
                                                    <option value="${month}">${month}</option>
                                                </#if>
                                            </#list>
                                            <option value="29">---</option>
                                        </@ZKUI.Combo>
                                    (1-28)
                                </div>
                            </div>
                            <div class="dayFre marginBottom24" style="margin-left:100px; margin-top: 24px;">
                                <#assign hours=0..23/>
                                <@ZKUI.Combo hideLabel="true" width="55" empty="false" id="idDayFrequencyHour" name="acc.dayFrequencyHour" followType="close" followScroll=".content_div">
                                    <#list hours as hour>
                                        <#if hour<10>
                                            <option value="${hour}">0${hour}</option>
                                        <#else>
                                            <option value="${hour}">${hour}</option>
                                        </#if>
                                    </#list>
                                    <option value="24">---</option>
                                </@ZKUI.Combo>
                                :
                                <#assign minutes=0..59/>
                                <@ZKUI.Combo hideLabel="true" width="55" empty="false" id="idDayFrequencyMinute" name="acc.dayFrequencyMinute" followType="close" followScroll=".content_div">
                                    <#list minutes as minute>
                                        <#if minute<10>
                                            <option value="${minute}">0${minute}</option>
                                        <#else>
                                            <option value="${minute}">${minute}</option>
                                        </#if>
                                    </#list>
                                    <option value="60">---</option>
                                </@ZKUI.Combo>
                                (<@i18n 'common_hour' />:<@i18n 'common_minute' />)
                            </div>
                            <div class="dayFre" style="display: flex;">
                               <div style="width: 100px;">
                                    <div style="text-align: ${leftRTL!'left'}"><@i18n 'acc_autoExport_exportModeTitle' />:</div>
                                </div>
                               <div>
                                    <@ZKUI.Input hideLabel="true" type="radio" value="0" name="acc.exportMode" id="dailyMode"/>
                                    <label><@i18n 'acc_autoExport_dailyMode' /></label>
                                </div>
                            </div>
                            <div class="monthFre" style="display: flex;">
                               <div style="width: 100px;">
                                    <div style="width: 100px;text-align: ${leftRTL!'left'}"><@i18n 'acc_autoExport_exportModeTitle' />:</div>
                                </div>
                               <div>
                                    <@ZKUI.Input hideLabel="true" type="radio" value="1" name="acc.exportMode" id="monthlyMode"/>
                                    <label><@i18n 'acc_autoExport_monthlyMode' /></label>
                                </div>
                            </div>
                            <div class="allMode marginBottom24" style="margin-left: 100px;">
                               <div>
                                    <@ZKUI.Input hideLabel="true" type="radio" value="2" name="acc.exportMode" id="allMode"/>
                                    <label><@i18n 'acc_autoExport_allMode' /></label>
                                </div>
                            </div>
                        </div>
                        <div class="autoExportEmail">
                            <div class="marginBottom24"><@i18n 'acc_autoExport_recipientMail' /></div>
                            <div class="marginBottom12">
                                <textarea rows="4" name="acc.autoExportEmail" style="width: 394px;" placeholder="<@i18n 'common_email_example'/>">${(accParams['acc.autoExportEmail'])!}</textarea>
                            </div>
                            <div><div class="warningImage"></div> <div class="warningColor"><@i18n 'common_email_setMailTip'/></div></div>
                        </div>
                    </fieldset>
                </div>
                <!-- 外出时间配置 -->
                <div>
                    <fieldset>
                        <legend>外出时间配置</legend>
                        <div class="marginBottom24">
                            <div style="width: 400px;text-align: ${leftRTL!'left'}">休息时间段：超过时间x分钟时同时推送至组长和课长</div>
                            <div >
                                <@ZKUI.Input hideLabel="true" id="restOutTime" name="acc.restOutTime" value="${(accParams['acc.restOutTime'])!}" maxlength="50" size="50" style="width:394px;" placeholder="请输入休息外出时间"/>(分钟)
                            </div>
                        </div>
                        <div class="marginBottom24">
                            <div style="width: 400px;text-align: ${leftRTL!'left'}">上班时间段：超时时间x分钟时推送至组长</div>
                            <div >
                                <@ZKUI.Input hideLabel="true" id="workOutTime" name="acc.workOutTime" value="${(accParams['acc.workOutTime'])!}" maxlength="50" size="50" style="width:394px;" placeholder="请输入上班外出时间"/>(分钟)
                            </div>
                        </div>
                        <div class="marginBottom24">
                            <div style="width: 400px;text-align: ${leftRTL!'left'}">上班时间段：超时时间x分钟时同时推送至课长</div>
                            <div >
                                <@ZKUI.Input hideLabel="true" id="workAndRestOutTime" name="acc.workAndRestOutTime" value="${(accParams['acc.workAndRestOutTime'])!}" maxlength="50" size="50" style="width:394px;" placeholder="请输入上班且休息外出时间"/>(分钟)
                            </div>
                        </div>
                    </fieldset>
                </div>
                <!-- end -->
                <div>
                    <fieldset>
                        <legend><@i18n 'common_param_configRTMonitor' /></legend>
                        <div>
                            <div class="marginBottom16">
                                <@i18n 'common_param_rtMonitorPhotoSize' />&nbsp;&nbsp;&nbsp;<@i18n 'common_param_maxHeight' />
                            </div>
                            <div class="marginBottom12">
                                <@ZKUI.Input hideLabel="true" id="personPhotoMaxHeight"  name="acc.personPhotoMaxHeight" value="${(accParams['acc.personPhotoMaxHeight'])!140}" maxlength="3" size="7" style="ime-mode:disabled"
                                       onkeyup="valueLimit(this.value)" onafterpaste="valueLimit(this.value)" style="width:394px;"/>
                                <span style="display: inline-block">px</span><span class="form_note">(80 - 500)</span>
                            </div>
                            <div>
                                <div class="warningImage"></div><div class="warningColor"><@i18n 'common_param_rtMonitorPhotoSizePs' /></div>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div id="multipleEmail">
                    <fieldset style="min-height: 100px">
                        <legend><@i18n 'common_param_alarmReceiveMail' /></legend>
                        <div class="marginBottom12">
                            <textarea id="mailAddr" rows="4" style="width: 394px;" name="mail" placeholder="<@i18n 'common_email_example'/>">${(accParams['acc.receiver'])!}</textarea>
                        </div>
                        <div><div class="warningImage"></div> <div class="warningColor"><@i18n 'common_email_setMailTip'/></div></div>
                    </fieldset>
                </div>
                <#if showSMS?exists && showSMS?string == "true">
                <div id="multipleSMS">
                    <fieldset style="min-height: 100px">
                        <legend><@i18n 'common_param_alarmReceiveMobileNo' /></legend>
                        <div class="marginBottom12">
                            <textarea id="smsReceiver" maxlength="200" rows="4" style="width: 394px; padding: 0 3px;" name="smsReceiver" placeholder="<@i18n 'common_smsModem_example'/>">${(accParams['acc.smsReceiver'])!}</textarea>
                        </div>
                        <div><div class="warningImage"></div> <div class="warningColor"><@i18n 'common_smsModem_setMobileNoTip'/></div></div>
                    </fieldset>
                </div>
                </#if>
                <!--<div>
                    &lt;!&ndash; 个人敏感信息保护 &ndash;&gt;
                    <fieldset>
                        <legend><@i18n 'pers_param_infoProtection'/></legend>
                        <div class="marginBottom16">
                            <@ZKUI.Input id="accSensitivePhotos" hideLabel="true" type="checkbox" name="acc.capturePhoto.encryptProp" trueValue="true" falseValue="false" eventCheck=true/>
                            <span><@i18n "pers_capture_catchPhoto"/></span>
                        </div>
                        <div class="warningImage"></div>
                        <div class="warningColor"><@i18n 'pers_param_infoProtectionWarnMsg'/></div>
                    </fieldset>
                </div>-->
            </div>
            <input id="id_down_event_hours" type="hidden" value="" name="down_event_hours"/>
        </form>
    <div class="sidenavContext"></div>
</div>
<script type="text/javascript">

    // 回填个人敏感信息保护
    $("#accSensitivePhotos").prop("checked", "${(accParams['acc.capturePhoto.encryptProp'])!}" == 'true');

    // 邮箱验证
    jQuery.validator.addMethod("accParamMailValid", function(val, element)
    {
        //var mailReg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
        var mailReg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*$/;
        if (val == "")
        {
            return true;
        }
        else
        {
            var mailArray = val.replace(/;/g, ",").split(",");//统一将输入的分号替换成逗号，支持混合输入
            for (var i = 0; i < mailArray.length; i++)
            {
                if (mailArray[i] != "" && !(this.optional(element) || (mailReg.test(mailArray[i]))))
                {
                    return false;
                }
            }
            return true;
        }
    }, I18n.getValue("common_email_inputEmailError"));

    //表单验证
    var validateObj = $("#${formId}").validate( {
        debug : true,
        rules :
            {
                "acc.personPhotoMaxHeight" :
                    {
                        required: true,
                        digits: true,
                        range: [80, 500]
                    },
                /* "event_max_line" :
                    {
                        required : true,
                        digits : true,
                        min :10,
                        max :100
                    },
                "eventInitializeLine" :
                    {
                        required : true,
                        digits : true,
                        min :0,
                        max :5
                    },*/
                "mail":
                {
                    accParamMailValid : true,
                    maxlength : 200
                },
                "acc.autoExportEmail":
                {
                    accParamMailValid : true,
                    maxlength : 200
                },
                "smsReceiver" :
                {
                    mobileNoValid : true,
                    mobileNoLengthValid: true
                }
            },
        submitHandler : function()
        {
            set_down_event_hours();
            if(!check_form())
            {
                return;
            }
        <@submitHandler />
        }
    });


    $("#${formId} #checkAll").click(function(){
        $("input[name='mode2_value']").attr("checked",true);
    });
    $("#${formId} #cancel").click(function(){
        $("input[name='mode2_value']").attr("checked",false);
    });

    //设置下载事件记录方式
    var down_event_mode = null;//用户选择的设置下载记录的方式
    $("input:radio[name='down_event_mode']").click(function(){
        var val = $("input:radio[name='down_event_mode']:checked").attr("value");
        down_event_mode = val;
        if(val == "mode1")
        {
            $("#${formId} #span_mode2").find(":checkbox").each(function(){
                $(this).attr("disabled", "disabled");
            });
            ZKUI.Combo.get('regular_time').combo.disable(false);
            $("#${formId} #checkAll").attr("disabled", "disabled");
            $("#${formId} #cancel").attr("disabled", "disabled");
        }
        else if(val == "mode2")
        {
            ZKUI.Combo.get('regular_time').combo.disable(true);
            $("#${formId} #checkAll").attr("disabled", false);
            $("#${formId} #cancel").attr("disabled", false);
            $("#${formId} #span_mode2").find(":checkbox").each(function(){
                this.disabled = "";//初始化第二种设置方式的值
            });
        }
    });

    var hours = "${(accParams['acc.downNewlog'])!}";//保证获取到的时间点为字符串.
    hours = hours.split(',');
    var length = hours.length;
    var is_setted = false;//判断是否已经初始化获取事件记录时间
    var temp = true;//用于判断设置的时间是否间隔相等
    if(length > 2)
    {
        var temp_value = parseInt(hours[1], 10)-parseInt(hours[0], 10);//获取第二个值和第一个值的差，用于判断是否是第一种设置方式
        for(i=0;i<length-1;i++)
        {
            if(parseInt(hours[i+1], 10)-parseInt(hours[i], 10) != temp_value)
            {
                temp = false;
            }
        }
        if(temp && (temp_value*length == (24+(temp_value-((24%temp_value!=0)?(24%temp_value):temp_value)))))
        {
            $("#${formId} #down_event_mode1").attr("checked","checked");
            $("#${formId} #down_event_mode2").removeAttr("checked");
            down_event_mode = $("input:radio[name='down_event_mode']:checked").attr("value");
            ZKUI.Combo.get("regular_time").combo.setComboValue(temp_value);//初始化第一种设置方式的值
            is_setted = true;
            $("#${formId} #span_mode2").find(":checkbox").each(function(){
                $(this).attr("disabled", "disabled");
            });
            ZKUI.Combo.get('regular_time').combo.disable(false);
            $("#${formId} #checkAll").attr("disabled", "disabled");
            $("#${formId} #cancel").attr("disabled", "disabled");
        }
    }
    if(!is_setted)
    {
        $("#${formId} #down_event_mode2").attr("checked","checked");
        $("#${formId} #down_event_mode1").removeAttr("checked");
        $("#${formId} #checkAll").attr("disabled", false);
        $("#${formId} #cancel").attr("disabled", false);
        $("#${formId} #span_mode2").find(":checkbox").each(function(){
            for(var i=0;i<length;i++)
            {
                if(hours[i] == this.value)
                {
                    this.checked = "checked";//初始化第二种设置方式的值
                }
            }
        });
        down_event_mode = $("input:radio[name='down_event_mode']:checked").attr("value");
    }

    var new_hours = '';//用户已选择的时间点
    function set_down_event_hours()//设置定时下载事件记录的时间，用于后端获取
    {
        if(down_event_mode == "mode1")
        {
            new_hours = '';
            var regular_time = ZKUI.Combo.get('regular_time').combo.getSelectedValue();
            var new_hour = 0;
            while(new_hour < 24)//计算第一种模式的时间点
            {
                new_hours = new_hours+new_hour+',';
                new_hour = new_hour+parseInt(regular_time, 10);
            }
        }
        else if(down_event_mode == "mode2")
        {
            new_hours = '';
            $("#${formId} #span_mode2").find(":checkbox:checked").each(function(){
                new_hours = new_hours+this.value+',';
            })
        }
        if(new_hours != '')
        {
            if(new_hours.charAt(new_hours.length-1) == ',')
            {
                new_hours = new_hours.substring(0,new_hours.length-1)//截取最后一个逗号
                $("#${formId} #id_down_event_hours").val(new_hours);
            }
        }
    }

    var t = $("input:checkbox[name='mode2_value']:checked").val();
    function check_form()
    {
        if(new_hours == '')
        {
            messageBox({messageType:"alert", type:"alert", text:"<@i18n 'common_param_seldownTime' />"});
            return false;
        }
        return true;
    }
    $("#${formId} #id_event_max_line").val("${(accParams['acc.eventMaxLine'])!}");
    $("#${formId} #idEventInitializeLine").val("0");

    window.setTimeout(function () {
        $("#${formId}OK").attr('disabled',true);
        <@ZKUI.Permission name="acc:param:edit">
            $("#${formId}OK").attr('disabled',false);
        </@ZKUI.Permission>
    },100);

    /**
     * 自动导入界面的切换
     */
    function changeFrequency(value) {
        $(".allMode").show();
        $(".autoExportEmail").show();
        var change = !$("input[name='acc.exportMode']")[2].checked;
        if(value=="0"){
            $(".dayFre").show();
            if(change){
                $("input[name='acc.exportMode']").first().attr('checked', 'checked');
            }
            $(".monthFre").hide();
        }else if(value=="1"){
            $(".monthFre").show();
            if(change){
                $("input[name='acc.exportMode']").eq(1).attr('checked', 'checked');
            }
            $(".dayFre").hide();
        }else {
            $(".monthFre").hide();
            $(".dayFre").hide();
            $(".allMode").hide();
            $(".autoExportEmail").hide();
        }
    }

    $(function () {
        var frequency = ${(accParams['acc.autoExportFrequency'])!0};
        if(!transactionAutoExportEnable()){
            frequency = 2;
        }
        checkOb(ZKUI.Combo.get("frequencySelect").combo, ZKUI.Combo.get("frequencySelect").combo.getOption(frequency).value);
        $("input[name='acc.monthFrequency']")[${(accParams['acc.monthFrequency'])!0}].checked = true;
        checkOb(ZKUI.Combo.get("idMonthFrequencyDate").combo, ZKUI.Combo.get("idMonthFrequencyDate").combo.getOptionByIndex(${(accParams['acc.monthFrequencyDate'])!1}-1).value);
        checkOb(ZKUI.Combo.get("idDayFrequencyHour").combo, ZKUI.Combo.get("idDayFrequencyHour").combo.getOptionByIndex(${(accParams['acc.dayFrequencyHour'])!0}).value);
        checkOb(ZKUI.Combo.get("idDayFrequencyMinute").combo, ZKUI.Combo.get("idDayFrequencyMinute").combo.getOptionByIndex(${(accParams['acc.dayFrequencyMinute'])!0}).value);
        changeFrequency(ZKUI.Combo.get("frequencySelect").combo.getSelectedValue());
        var index = "${(accParams['acc.exportMode'])!'0'}";
        if(index.trim() != ""){
            $("input[name='acc.exportMode']")[index].checked=true;
        }
    })

    function checkOb(obj, value){
        if(obj) {
            obj.setComboValue(value)
        }
    }

    function transactionAutoExportEnable() {
        var enable = "${transactionAutoExportEnable!}";
        if(enable!=null && enable !=""){
            if(enable==true || enable=="true"){
                return true;
            }
        }
        return false;
    }

    function valueLimit(val){
        $("#personPhotoMaxHeight").val(val.replace(/\D/g,''));
    }
</script>
</#macro>
