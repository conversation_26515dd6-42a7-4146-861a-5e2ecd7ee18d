<#assign editPage="true">
<#include '/public/template/editTemplate.html'>
<#macro editContent>

<form action='/accMap.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
	<input type='hidden' name='id' value='${(item.id)!}'/>
	<input type="hidden" id="map_id" name="baseMap.id" value="${(item.id)!}"/>
	<input type="hidden" name="mapPath" value="${(item.mapPath)!}"/>
	<input type="hidden" name="width" value="${(item.width)!}"/>
	<input type="hidden" name="height" value="${(item.height)!}"/>
	<table class='tableStyle'>
		<tr>
			<th><label><@i18n 'base_map_name'/></label><span class='required'>*</span></th>
			<td><input name='name' type='text' value='${(item.name)!}'/></td>
		</tr>
		<tr>
			<th><label><@i18n 'base_area_name'/></label><span class='required'>*</span></th>
			<td>
			    <@ZKUI.ComboTree  name="authAreaId" value="${(item.authAreaId)!}" url="/authArea.do?tree" hideLabel="true" width="148" type="radio">
				</@ZKUI.ComboTree>
			</td>
		</tr>
		<#if (item.id)?exists>
		<tr>
			<th><label><@i18n 'base_map_modifyPath'/></label></th>
			<td>
				<@ZKUI.Input hideLabel="true" type="checkbox" id="id_modify_path" name="modify_path" trueValue="1" falseValue="0" eventCheck="true" />
			</td>
		</tr>
		</#if>
		<tr id="map_path">
			<th><label><@i18n 'base_map_mapPath'/></label><span class="required">*</span></th>
			<td>
			    <div style="width: 240px; overflow: auto;">
					<@ZKUI.Upload name="file" id="id_file" file_accept="image/*" onChange="checkImgSize" />
				</div>
			</td>
		</tr>
	</table>
</form>

<script type='text/javascript'>
	(function() {
		$('#${formId}').validate( {
			debug : true,
			rules :
			{
				'name' :
				{
					required : true,
					unInputChar:true,
					rangelength : [1, 30],
					overRemote : ["accMap.do?isExist", "${(item.name)!}"]
				},
				"authAreaId":
				{
					required : true
				},
				"file":
				{
					required : true,
					accept:"jpg|JPG|jpeg|JPEG|gif|GIF|png|PNG|bmp|BMP"
				}
			},
			messages:
			{
				"file":
				{
	                required: "<@i18n 'base_map_picType'/>",//请上传后缀名为jpg的图片
	                accept: "<@i18n 'base_map_picType'/>"
			    }
			},
			submitHandler : function()
			{
				
				<@submitHandler callBackFun="createItemCookie(result[sysCfg.data]);reloadTree()"/>
			}
		});

		if ($("input[name='id']").val() != "") {
			updateUploadButtonState();
		}
	})();
	
	$("#id_modify_path").change(function(){
		updateUploadButtonState();
	});

	/** 修改上传文件按钮状态 */
	function updateUploadButtonState() {
		var ret = $("#id_modify_path").attr("checked");
		if(ret=="checked") {
			ZKUI.Upload.get("id_file").disabled(false);
			$("#id_file input[name=file]").rules("add", {required: true, accept: "jpg|JPG|jpeg|JPEG|gif|GIF|png|PNG|bmp|BMP"});
		}
		else {
			ZKUI.Upload.get("id_file").disabled(true);
			$("#id_file input[name=file]").rules("remove");
		}
	}
</script>
</#macro>