<@ZKUI.SelectContent onSure="dealSelectDoor" showColumns="checkbox,doorName,deviceAlias,deviceSn" textField="doorName" value="" vo="com.zkteco.zkbiosecurity.acc.vo.AccSelectDoorItem" query="/accMapSelectDoor.do?list&mapId=${mapId!}">
    <@ZKUI.Searchbar>
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="doorName"  maxlength="30" title="acc_door_name" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="deviceSn"  maxlength="30" title="common_dev_sn" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    
</@ZKUI.SelectContent>

<script type="text/javascript">

//添加门，点确定之后调用的方法
function dealSelectDoor(value, text, event)
{
	var mapId = "${(mapId)!}";
	var entityType = "${(entityType)!}";
	var width = "${(width)!}";
	var logMethod = "${(logMethod)!}";
	var filterDoorIds = "${(filterDoorIds)!}";
	
	if(value == "-1" || value == "")
	{
		messageBox({messageType:"alert",text: "<@i18n 'acc_level_doorRequired'/>"});
		return null;
	}
	else
	{
		$.ajax({
			url:"/accMap.do?addDoor",
			type:"post",
			data: {
				mapId:mapId,
				width:width,
				entityType:entityType,
				entityIds:value,
				logMethod:logMethod,
                name:text
			},
			success:function(result) {
				dealRetResult(eval(result),function(){
					mapTree.selectItem(mapId,true,true);
				});
			}
	    });
	}
	
}
</script>