<#assign leftRTL = "left">
<#assign rightRTL = "right">
<#if enableRTL?? && enableRTL=="true">
<!-- 定义全局对齐方式变量(针对阿拉伯页面) -->
<#assign leftRTL = "right">
<#assign rightRTL = "left">
</#if>
<!-- 报警声音页面 -->
<div id="idAlarmSound" style="width:0px; height: 0px;"></div>

<!-- 地图监控页面页面 -->
<div>
	<div id="id_map_${(tempMap.id)!}" style="overflow: auto; width:100%; height: 100%; position: absolute;">
		<!--套上DIV，放大/缩小操作对整个元素操作-->
		<div style="display: inline-block;transform-origin: 0 0 0;">
		<input type="hidden" value="0" id="reloadFlag"><!-- 用于标识右侧的地图图片是否重新加载 -->
		<input id="loadPlugins" type="hidden" value="${loadPlugins!}"><!-- 视频插件加载版本,用于加载ocx -->
		<#assign doorIds = "">
		<#list (mapPosList) as item>
			<#if item.entityType == "AccDoor">
				<div id="item_${item.id}" align="center" style="float: ${leftRTL!'left'}; position: absolute;left:${(item.leftX)!}px; top:${(item.topY)!}px; width:${(item.width)!}px"
					entity_type="${(item.entityType)!}" >
					<div id="acc_door_${item.id}">
						<!--<img id="${item.entityId}_image" value="${item.entityId}" data="${item.id}" data-name="${item.entityName}" class="can_drag poPupImage ${(item.entityType)!}" onmousedown="return false;"
							style="width:${(item.width)!}px;cursor: move;" src="images/doorState/icon/door/door_default.png" >-->
						<div id="${item.entityId}_image" value="${item.entityId}" data="${item.id}" data-name="${item.entityName}" onmousedown="return false;"
							 style="width:${(item.width)!}px;cursor: move;" class="can_drag poPupImage ${(item.entityType)!} acc_rt_doorImg door_door_default" ></div>
						<div class="poPupInfo" style="position: fixed;z-index: 99;visibility: hidden;" align="${leftRTL!'left'}">
							<p class="poPupInfo_title_p"><@i18n 'common_status'/></p>
							<table class="poPupInfo_text_table">
								<#assign fields = [
													["common_dev_entity", item.entityId + "_devAlias", entityList[item_index].device.alias],
													["common_dev_sn", item.entityId + "_devSn", entityList[item_index].device.sn],
													["common_number", item.entityId + "_no", entityList[item_index].doorNo],
													["acc_door_sensor", item.entityId + "_sensor", "common_paging_loading"],
													["acc_door_relay", item.entityId + "_relay", "common_paging_loading"],
													["common_alarm", item.entityId + "_alarm", "common_paging_loading"]
													]>
								<#list fields as field>
									<tr>
										<td><@i18n '${field[0]}'/>:</td><td id="${field[1]}"><@i18n '${field[2]}'/></td>
									</tr>
								</#list>
							</table>
							<div id="${item.entityId}_opDisplay" onclick="showFlag=false;hidePopUpInfo()">
								<ul class="poPupInfo_op_ul">
									<@ZKUI.Permission name="acc:rtMonitor:openDoor">
										<li onclick="accMapRemoteOperate('accRTMonitor.do?openDoor', '${item.entityId}', '${item.entityName}', '<@i18n 'acc_eventNo_8'/>')" title="<@i18n 'acc_eventNo_8'/>"><@i18n 'acc_eventNo_8'/></li>
									</@ZKUI.Permission>
									<@ZKUI.Permission name="acc:rtMonitor:closeDoor">
										<li onclick="accMapRemoteOperate('accRTMonitor.do?closeDoor', '${item.entityId}', '${item.entityName}', '<@i18n 'acc_eventNo_9'/>')" title="<@i18n 'acc_eventNo_9'/>"><@i18n 'acc_eventNo_9'/></li>
									</@ZKUI.Permission>
									<div id="${item.entityId}_lockDisplay">
										<@ZKUI.Permission name="acc:rtMonitor:lockDoor">
											<li onclick="accMapRemoteOperate('accRTMonitor.do?lockDoor', '${item.entityId}', '${item.entityName}', '<@i18n 'acc_newEventNo_233'/>')" title="<@i18n 'acc_newEventNo_233'/>"><@i18n 'acc_newEventNo_233'/></li>
										</@ZKUI.Permission>
										<@ZKUI.Permission name="acc:rtMonitor:unLockDoor">
											<li onclick="accMapRemoteOperate('accRTMonitor.do?unLockDoor', '${item.entityId}', '${item.entityName}', '<@i18n 'acc_newEventNo_234'/>')" title="<@i18n 'acc_newEventNo_234'/>"><@i18n 'acc_newEventNo_234'/></li>
										</@ZKUI.Permission>
									</div>
									<@ZKUI.Permission name="acc:rtMonitor:cancelAlarm">
										<li onclick="accMapRemoteOperate('accRTMonitor.do?cancelAlarm', '${item.entityId}', '${item.entityName}', '<@i18n 'acc_eventNo_7'/>')" title="<@i18n 'acc_eventNo_7'/>"><@i18n 'acc_eventNo_7'/></li>
									</@ZKUI.Permission>
									<@ZKUI.Permission name="acc:rtMonitor:normalOpenDoor">
										<li onclick="accMapRemoteOperate('accRTMonitor.do?normalOpenDoor', '${item.entityId}', '${item.entityName}', '<@i18n 'acc_rtMonitor_remoteNormalOpen'/>')" title="<@i18n 'acc_rtMonitor_remoteNormalOpen'/>"><@i18n 'acc_rtMonitor_remoteNormalOpen'/></li>
									</@ZKUI.Permission>
									<@ZKUI.Permission name="acc:rtMonitor:enableNormalOpenDoor">
										<li onclick="accMapRemoteOperate('accRTMonitor.do?enableNormalOpenDoor', '${item.entityId}', '${item.entityName}', '<@i18n 'common_rtMonitor_enableIntradayTZ'/>')" title="<@i18n 'common_rtMonitor_enableIntradayTZ'/>"><@i18n 'common_rtMonitor_enableIntradayTZ'/></li>
									</@ZKUI.Permission>
									<@ZKUI.Permission name="acc:rtMonitor:disableNormalOpenDoor">
										<li onclick="accMapRemoteOperate('accRTMonitor.do?disableNormalOpenDoor', '${item.entityId}', '${item.entityName}', '<@i18n 'common_rtMonitor_disableIntradayTZ'/>')" title="<@i18n 'common_rtMonitor_disableIntradayTZ'/>"><@i18n 'common_rtMonitor_disableIntradayTZ'/></li>
									</@ZKUI.Permission>
								</ul>
							</div>
							<div>
								<@ZKUI.Permission name="acc:rtMonitor:transactionTodayHappen">
								    <ul class="poPupInfo_op_ul" style="border-top: none; !important">
	                                	<li onclick="openTransactionTodayLastWindow('skip.do?page=acc_rtMonitor_accTransactionsTodayLast&eventPointType=0&eventPointId=${item.entityId}')"><@i18n 'acc_doorEventLatestHappen'/></li>
	                                </ul>
							    </@ZKUI.Permission>
								<@ZKUI.Permission name="acc:rtMonitor:liveView">
									<ul class="poPupInfo_op_ul acc_channel_preview_display_${item.channelIds}" style="border-top: none!important">
										<li onclick="accRTMonitorChannelPreview('${item.channelIds}')"><@i18n 'system_module_videoPreview'/></li>
									</ul>
								</@ZKUI.Permission>
							</div>
						</div>
					</div>
					<div style="position: relative;">
						<p id="${item.id}_name" style="position: absolute;width:200px;margin-${leftRTL!'left'}:-80px;text-align: center;"
							title="${item.entityName}"><font id="${item.id}">${item.entityName}</font></p>
					</div>
				</div>
				<#if item_index == 0>
					<#assign doorIds = item.entityId>
				<#else>
					<#assign doorIds = doorIds + "," + item.entityId>
				</#if>
			</#if>
			<#if item.entityType == "VidChannel">
				<div id="item_${item.id}" align="center" style="float: ${leftRTL!'left'}; position: absolute;left:${(item.leftX)!}px; top:${(item.topY)!}px; width:${(item.width)!}px;"
					entity_type="${(item.entityType)!}" >
					<div>
						<img id="${item.entityId}_${(item.entityType)!}" value="${item.entityId}" data="${item.id}" data-name="${item.entityName}" class="can_drag poPupImage ${(item.entityType)!}" onmousedown="return false;"
							style="width:${(item.width)!}px;cursor: move;" src="images/acc_map_channel.png"
							<#if systemModules?lower_case?index_of("ivs") == -1>
								ondblclick="vidPreview(this.getAttribute('value'), '${item.entityName}');"
							<#else>
								ondblclick="loadPlayVideo(this.getAttribute('value'), 600, 420, '${item.entityName}');"
							</#if> />
					</div>
					<div style="position: relative;">
						<p id="${item.id}_name" style="position: absolute;width:200px;margin-${leftRTL!'left'}:-80px;text-align: center;"
							title="${item.entityName}">${item.entityName}</p>
					</div>
				</div>
			</#if>
		</#list>
		<input id="doorIds" type="hidden" value="${doorIds}"/>
		<img class="map" <#if (mapId)?exists && (mapId) != "" > src="${(tempMap.mapPath)!}" onerror="this.src='/images/notFoundMapPic.jpg'" </#if>
		     style="width:${(tempMap.width)!}px;height:${(tempMap.height)!}px"/>
		</div>
	</div>
</div>
<div class="contextMenu displayN" id="del_icon_menu">
    <ul>
        <li id="del_icon" class="door_ops"><img src="public/images/dhtmlxToolbarImgs/comm_del.png" /><@i18n 'base_map_delEntity'/></li>
    </ul>
</div>
<script>
	/* 查询门最近发生事件弹窗 */
    function openTransactionTodayLastWindow(id){
    	var opts = {//弹窗配置对象
    			path: id,//设置弹窗路径
    			width: 1000,//设置弹窗宽度
    			height: 300,//设置弹窗高度
    			title: "<@i18n 'acc_doorEventLatestHappen'/>",//设置弹窗标题
    			gridName: "gridbox"//设置grid
    		};
    	DhxCommon.createWindow(opts);
    }
    
	//右键菜单删除图标--start
    <@ZKUI.Permission name="acc:map:delEntity">
	$(".poPupImage").contextMenu('del_icon_menu',
	{
	    menuStyle: {
	        width: 'auto'
	    },
	    bindings:
	    {
	        'del_icon': function(obj)
	        {
	        	var itemId = $(obj).attr("data");
	        	var name = $(obj).attr("data-name");
	        	var path = "accMapPos.do?del&ids="+itemId + "&name=" + encodeURIComponent(name);
	        	$.get(path, function(result){
	    			dealRetResult(eval(result),function(){
	    				$(obj).parent().parent().remove();
	    				updateDoorIds();
	    			});
	    		}, "json");
	        }
	    }
	});
	</@ZKUI.Permission>
	//右键菜单删除图标--end
	
	var isMove = false ;
	var obj;
	$(".poPupImage").mousedown(function (e){
		isMove = true;
		$(this).next("div.poPupInfo").mouseout();
		obj = document.getElementById("item_" + $(this).attr("data").split("_")[0]);
        var abs_x = e.pageX - parseInt($(obj).css("left"));
        var abs_y = e.pageY - parseInt($(obj).css("top"));
        $(document).mousemove(function (e){
			if (isMove)
            {
                $(obj).css({
                	'left':e.pageX - abs_x < 5 ? 5 : e.pageX - abs_x
                	, 
                	'top':e.pageY - abs_y < 5 ? 5 : e.pageY - abs_y
                });
            }
        });
    }).mouseup(function (){
    	isMove = false;
     	$(this).next("div.poPupInfo").mouseover();
    });

	/** 若图标拖动中，点击地图停止拖动，修复放大、缩小拖动中图标无法取消 */
    $("div[id^='id_map_']").click(function () {
    	if (isMove) {
			isMove = false;
    	}
    });
	
	$("div[id^='acc_door_']").mouseover(function(){//显示浮动信息
		if(!isMove)
		{
			var offsetTop = $(this).parent().offset().top + $(this).height()/2 - $(document).scrollTop();
			if($(document.body).height() - offsetTop < $(this).children("div").height())//判断距离底部的距离是否小于提示框的高度
			{
				offsetTop = $(this).parent().offset().top + ($(this).children("img").height()/2) - ($(this).children("div").height());
			}
			
			var offsetLeft = $(this).parent().offset().left + $(this).width()/2 - $(document).scrollLeft();
			if($(window).width() - offsetLeft < $(this).children("div").width())//判断距离底部的距离是否小于提示框的高度
			{
				offsetLeft = $(this).parent().offset().left + ($(this).children("img").width()/2) - $(this).children("div").width();
			}
			$(this).children(".poPupInfo").css("top", offsetTop);
			$(this).children(".poPupInfo").css("left", offsetLeft);
			// $(this).children(".poPupInfo").css("visibility","visible");
			$("body div.dataViewOpItem:first-child").remove();
			$("body").prepend('<div class="dataViewOpItem" onmouseover="showFlag=true" onmouseout="showFlag=false;hidePopUpInfo()" style="background-color: white;position: fixed;top: '+ offsetTop +'px;left: '+ offsetLeft +'px;z-index: 9999999;" align="${leftRTL!'left'}">'+ $(this).children(".poPupInfo").html() + '</div>');

			
		}
	}).mouseout(function(){//隐藏浮动信息
		hidePopUpInfo();
	});
	function hidePopUpInfo(obj)
	{	
		showFlag = false;
		window.setTimeout(function(){
			if(!showFlag)
			{
				$("body").children("div.dataViewOpItem:first").remove();
			}
		}, 30);
		
		//$(obj).children("div").hide();
	}
	
	var doorIds = "";
	function updateDoorIds()
	{
		doorIds = "";
		$(".AccDoor").each(function(i){
			var doorId = this.id.split("_")[0];
			if(i == 0)
			{
				doorIds = doorId;
			}
			else
			{
				doorIds += "," + this.id.split("_")[0];
			}
		});
	}
	updateDoorIds();

    var mapClient;

    var clientId = Math.round(Math.random() * 10000)+"${uuid!}";
    function loadDevStateData()
	{
		try
		{
			// window.clearTimeout(devStateTimeout);
			// debugger;
			if(doorIds == "")
			{
				return;
			}
			// if (!mapClient) {
			if(mapClient) {
                mapClient.close();
			}
                mapClient = Web.getSocket({
                    id:"dClient",
                    url:"accMapMonitor/getDoorState",
                    param:JSON.stringify({"clientId" : clientId, "doorIds": doorIds}),
                    onMessage:function(resp) {
                        mapClient.devData = JSON.parse(resp.body);
                        if(mapClient.devData.clientId && clientId!=mapClient.devData.clientId) {
                            return;
                        }
                        dealDevStateData();
                    }
                });
		}
		catch(err) 
		{
			console.log("Error name: " + err.name + "");
			console.log("Error message: " + err.message);
        }
	}

	
	//处理设备状态数据
	function dealDevStateData()
	{
		var doorStates = mapClient.devData.doorStates;
		var iconFolderName;
		for(var i = 0; i < doorStates.length; i++)
		{
			var id = doorStates[i].id;
            doorStates[i].sensor = I18n.getValue(doorStates[i].sensor);
            doorStates[i].relay = I18n.getValue(doorStates[i].relay);
            doorStates[i].alarm = I18n.getValue(doorStates[i].alarm);
			iconFolderName = doorStates[i].iconFolderName;
			for(var key in doorStates[i])
			{
				if(document.getElementById(id + "_" + key))
				{
					var $obj = $("#" + id + "_" + key);
					if($obj.is("div") && key == "image")
					{
						// $obj.attr("src", "/images/doorState/icon/"+iconFolderName+"/door_" + doorStates[i][key] + ".png");
						var divCenter = $($($obj.parent()).parent());
						var entityType = divCenter.attr("entity_type");
						$obj.attr("class", "can_drag poPupImage " + entityType + " acc_rt_doorImg" + " door_" + iconFolderName + "_" + doorStates[i][key]);
					}
					else if($obj.is("td"))
					{
						$obj.html(doorStates[i][key]);
					}
					else if($obj.is("div"))
					{
						$obj.css("display", doorStates[i][key]);
					}
				}
			}
			
			if(document.getElementById("accMapAudioCheckbox").checked)
			{
				if(doorStates[i].audio)
				{
					if(isFirefox()||isChrome())
					{
						var allAudios = $("audio[name='alarmAudio']");
						var addFlag = "true";
						for (j = 0; j < allAudios.length; j++) 
						{
							if($(allAudios[j]).attr('res') == doorStates[i].audio)
							{
								if($(allAudios[j]).attr('allIds').indexOf(","+doorStates[i].id+",")<0)
								{
									var allIds = $(allAudios[j]).attr('allIds')+doorStates[i].id+",";
									$(allAudios[j]).attr('allIds',allIds);
								}
								addFlag = "false";
							}
						}
						if(addFlag == "true")
						{
							var doorAudio = "<audio name='alarmAudio' allIds=',"+doorStates[i].id+",' controls='controls' hidden='false' loop='false' res='"+doorStates[i].audio+"' ><source id='alarmsource2' name = 'soundname' src='"+doorStates[i].audio+"' type='audio/mp3' preload='auto' /><source id='alarmsource1' name = 'soundname' src='"+doorStates[i].audio+"' type='audio/x-wav' preload='auto'/><embed id='alarmsource3' name = 'soundname' height='100' width='100' src='"+doorStates[i].audio+"' preload='auto'/></audio>";
							$("#alarm_sound").append(doorAudio);
							var alarmAudio = $("audio[allIds=',"+doorStates[i].id+",']")[0];
							alarmAudio.play();
						}
					}
					else
					{
						var allbgsound = $("bgsound[name='alarmSound']");
						var addFlag = "true";
						for (j = 0; j < allbgsound.length; j++) 
						{
							if($(allbgsound[j]).attr('res') == doorStates[i].audio)
							{
								if($(allbgsound[j]).attr('allIds').indexOf(","+doorStates[i].id+",")<0)
								{
									var allIds = $(allbgsound[j]).attr('allIds')+doorStates[i].id+",";
									$(allbgsound[j]).attr('allIds',allIds);
								}
								addFlag = "false";
							}
						}
						if(addFlag == "true")
						{
							var doorbgsound = "<bgsound name='alarmSound' src='"+doorStates[i].audio+"' allIds=',"+doorStates[i].id+",' loop='-1'  res='"+doorStates[i].audio+"' />"
							$("#alarm_sound").append(doorbgsound);
						}
					}
				}
			}
			
		}
	}
	
	loadDevStateData();
	function pwdChange() {
		$("#password_hidden").val(hex_md5($("#loginPwd").val()));
	}
	function accMapRemoteOperate(itemId, id, name, titleText)
	{
		var data = {title: titleText, id: id, opObj: name};
		window.setTimeout(function () {
			isNeedValidUserForExport(function(ret) {
				if(ret && ret.ret === sysCfg.success && itemId.indexOf("open") == -1) {
					openConfirm(data);
				} else {
					openForm(data);
				}
			})
		}, 10);

		function dealRetFun(result)//处理操作结果函数
		{
			if(result[sysCfg.ret] == sysCfg.success)
			{
				closeMessage();
				openMessage(msgType.success,I18n.getValue(result[sysCfg.msg]));
				$(".jbox-icon").addClass("jbox-rtBeforeIcon");
			}
			else if(result[sysCfg.ret] == "500")
			{
				openMessage(msgType.error, result[sysCfg.msg] );
			} else {
				openMessage(msgType.error);
			}
		}
		var akeyCode = 0;  //用于获取单击键盘的键值
		var alreadySubmit = false;
		function openForm(data) {
			var html = "<div style='padding:10px;margin-${leftRTL!'left'}:30px;'>" +
						"<div><@i18n 'common_target'/>:&nbsp;&nbsp;" + data.opObj + "</div>" +
						"<form id='openDoorForm' action='" + itemId + "&name=" + encodeURIComponent(data.opObj) + "' method='post'>" +
						"<table style='padding-top: 8px;'><tr id='loginPwdRow' hidden='hidden'><th style='text-align: left;padding:5px 5px 5px 0px;'><@i18n 'auth_user_userPwd'/><span class='required'>*</span></th>" +
						"<td><input type='password' id='loginPwd' name='mapLoginPwd' maxlength='50' autofocus='true' onchange='pwdChange()'/><input type='hidden'  name='loginPwd' id='password_hidden'/></td></tr>"+
						"<tr id='openIntervalTr'><th style='text-align: left;padding: 5px 5px 5px 0px;;'><@i18n 'common_open'/>:</th><td><input type='text' name='openInterval' size='8' maxlength='3' value='5'/>" +
						"<@i18n 'common_second'/><span class='form_note'>(1-254)</span></td></tr>" +
						"</table></form>" +
						"</div>";
			var result = false;
			isNeedValidUserForExport(function(ret) {
				result = ret.ret === sysCfg.success;
				if(ret && ret.ret === sysCfg.success) {
					$("#loginPwdRow").hide();
				} else {
					if(itemId.indexOf("open") == -1) {
						$("#openIntervalTr").hide();
					}
					$("#loginPwdRow").show();
				}
			})
			$.jBox(html, {
				title: data.title,
				loaded: function() {
					if(result) {
						$("#loginPwd").rules("remove");
						$("input[name=openInterval]").focus();
					} else {
						$("#loginPwd").focus();
						$("#loginPwd").rules("add", {required : true});
					}
				},
				submit: function (v, h, f) {

					if (v == "ok")
					{
						$("#openDoorForm").submit();
						return false;
					}
				}
			});

			$("#openDoorForm").validate({
				rules : {
					"openInterval" : {
						required : true,
						range: [1, 254]
					}
				},
				submitHandler: function(form)
				{
					$(".jbox-button-focus").addClass("jbox-button-hover");
					if(alreadySubmit)
					{
						return;
					}
					alreadySubmit = true;
					$.jBox.close();
					onLoading(function(){
						$(form).ajaxSubmit({
							async : true,
							dataType : 'json',
							data: {
								name: data.opObj,
								ids: data.id
							},
							success: function(result)
							{
								dealRetFun(result);
							}
						});
					});
				}
			});
		}
		var alreadySubmit = false;
		function openConfirm(data) {
			var html = "<div style='margin-top: -3px'>" +
	    							"<div>"+"<@i18n 'common_target'/>"+": "+ data.opObj + "</div>" +
		    						"<div>"+"<@i18n 'common_prompt_executeOperate'/>".format(titleText) + "</div>" +
								"</div>";

			$.jBox.confirm(html, data.title,
				function (v, h, f) {
					if (v == "ok")
					{
						$(".jbox-button-focus").addClass("jbox-button-hover");
						if(alreadySubmit)
						{
							return;
						}
						alreadySubmit = true;
						$.jBox.close();
						onLoading(function(){
							$(".jbox-icon").addClass("jbox-rtBeforeIcon");
							$.ajax({
								type: "POST",
								url: itemId,
								async : true,
								data: {
									name: data.opObj,
									ids: data.id
								},
								success: function(result)
								{
									dealRetFun(result);
								}
							});
						});
						return false;
					}
					return true;
				}
			);
			$(".jbox-content").children().addClass("jbox-rtContent");
			$(".jbox-icon").addClass("jbox-rtIcon");
			//leo 解决实时监控->远程关门，弹出的提示框，按回车键不会进入下一步(浏览器focus兼容性问题)
		}
		function keyDownForEnter(e)
		{
			akeyCode = e.keyCode;
			if($(".jbox-button-focus") && akeyCode == '13' && alreadySubmit == false)
			{
				$("#password_hidden").val(hex_md5($("#rtLoginPwd").val()));
				$(".jbox-button-focus").click();
			}
		}
		document.onkeydown = keyDownForEnter;
		$(".jbox-button-focus").css("border", "1px solid #888888");
		$(".jbox-button-focus").focus();
	}
	
	var audioArray = new Array();//音频文件的数组
	var eventAudioTimeout;
	loadAudioEventData();
	//播放实时事件产生的音频文件
	function loadAudioEventData()
	{
		window.clearTimeout(eventAudioTimeout);
		var accMapAudioCheckbox = document.getElementById("accMapAudioCheckbox");
		if(accMapAudioCheckbox && accMapAudioCheckbox.checked)
		{
			if(eventAudioTimeout >0)
			{
				closeAudio("alert_sound");
				//使用定时器3秒钟判断资源数，如果该资源数>0,则停止当前声音，并播放下一个声音，如果资源数=0，则不做任何操作
				if(audioArray.length>0)
				{
					$("#myplay").remove();
					var sound = audioArray.shift();
					loadAndPlayAudio("alert_sound" , sound);
				}
			}
		}
		else
		{
			while(audioArray.length>0)
			{
				audioArray.pop();
			}
			//audioArray = new Array();//重新设置为空
		}
		eventAudioTimeout = window.setTimeout("loadAudioEventData()" , 3000);
	}
	
	
    var eventGrid = ZKUI.Grid.get("${gridName!}").grid;
	accMapLoadEventData();

	/* 存在视频模块才加载弹出视频方法 */
	if ("${systemModules}".toLowerCase().indexOf("vid") != -1 || "${systemModules}".toLowerCase().indexOf("vms") != -1) {
		window.setTimeout('loadPopUpVideo("acc")', 1000);
	}

	//获取实时事件数据
	function accMapLoadEventData()
	{
		try
		{
            var clientId = Math.round(Math.random() * 10000)+"${uuid!}";

            var client = Web.getSocket({
                id : "accMapEventClient",
                url : "accMapMonitor/getEventData",
                param:JSON.stringify({"clientId" : clientId}),
                onMessage:function(resp) {
                    var data = JSON.parse(resp.body);
                    if(data.clientId && clientId!=data.clientId) {
                        return;
                    }
                    if(data != null && typeof(eventGrid.setSizes) == "function") {
                        var rows = data.rows;
                        var normalNum = 0;
                        var warningNum = 0;
                        var alarmNum = 0;
                        var totalNum = 0;	//添加的总接收条数
                        for(var index in rows) {
                            if (doorIds != null && doorIds != "") {
                                var doorIdArry = doorIds.toString().split(",");
                                for (var doorId in doorIdArry)		//遍历地图上所有的门
                                {
                                    if (doorIdArry[doorId] == rows[index].data[10])	//判断门事件，所对应的门是否在该地图上
                                    {
                                        var id = rows[index].id;
                                        var maxEventCount = parseInt($("#maxEventCount").html());
                                        if (eventGrid.getRowsNum() >= maxEventCount) {
                                            eventGrid.deleteRow(eventGrid.getRowId(eventGrid.getRowsNum() - 1));
                                        }
                                        var eventNameInfo = rows[index].data[4].split(",");
										var eventName = "";
										if(eventNameInfo.length > 1) {
											eventName = I18n.getValue(eventNameInfo[0]) + "("+ I18n.getValue(eventNameInfo[1]) + ")";
										} else {
											eventName = I18n.getValue(rows[index].data[4]);
										}
                                        if (rows[index].data[4] == "acc_eventNo_7") {
                                            $("#alarm_sound").html("");
                                            closeAlarmSound();//取消报警操作，停止音频
                                            audioArray = new Array();//重新设置为空
                                        }
                                        rows[index].data[4]  = eventName;
                                        rows[index].data[4] = I18n.getValue(rows[index].data[4]);
                                        rows[index].data[8] = convertVerifyModeName(rows[index].data[8]);
                                        var personPin = rows[index].data[12];
										var personName = rows[index].data[6];
										var personCard = rows[index].data[5];
										<#if hasPermission('acc:pin:encryptProp','true')>
											personPin = convertToEncrypt(personPin,"${pinEncryptMode!S2}");
										</#if>
										<#if hasPermission('acc:name:encryptProp','true')>
											personName = convertToEncrypt(personName,"${nameEncryptMode!S1}");
										</#if>
										<#if hasPermission('acc:cardNo:encryptProp','true')>
											personCard = convertToEncrypt(personCard,"${cardEncryptMode!S2}");
										</#if>
										rows[index].data[6] = personPin;
										if (personName != "") {
											rows[index].data[6] = personPin + "(" + personName + ")";
										}
										rows[index].data[5] = personCard;
                                        eventGrid.addRow(id, rows[index].data, 0);
                                        var textColor = getTextColorByDataLevel(rows[index].dataLevel, rows[index].style);
                                        eventGrid.setRowTextStyle(id, textColor);
                                        for (var key in rows[index].userdata) {
                                        	if (key == "globalLinkageEvent") {
												var globalLinkageEvent = rows[index].userdata[key];
												globalLinkageEvent.cardNo = personCard;
												globalLinkageEvent.pin = personPin;
												globalLinkageEvent.name = personName;
												<#if hasPermission('acc:name:encryptProp','true')>
													globalLinkageEvent.lastName = convertToEncrypt(globalLinkageEvent.lastName, "${nameEncryptMode!S1}");
												</#if>
											}
                                            eventGrid.setUserData(id, key, rows[index].userdata[key]);
                                        }
                                        //弹出视频
                                        if (typeof(rows[index].vidDevices) != "undefined") {
                                            global_vidDevices.push({
                                                vidDescription: rows[index].vidDescription,
                                                vidDevices: rows[index].vidDevices
                                            });
                                        }
                                        if ("${systemModules}".toLowerCase().indexOf("ivs") != -1) {
											/** 弹出联动视频实时预览 */
											accRTMonitorPopUpVideo();
										}
                                        //消息提示
                                        accMapEventMsg(id);
                                        //事件声音提醒
                                        var tempAudio = data.audios;
                                        if (tempAudio.length > 0 && tempAudio[index] != null) {
                                            audioArray.push(tempAudio[index]);
                                            var arrLen = audioArray.length;
                                            //当音频列表中的播放路径大于50时，移除超出50部分旧数据，保留最多50条音频路径__modifyBy qingj.qiu
                                            for (var j = 0; arrLen >= 50 && j < (arrLen - 50); j++) {
                                                audioArray.shift();
                                            }
                                        }
                                        switch (rows[index].userdata["status"]) {
                                            case "normal":
                                                normalNum++;
                                                break;
                                            case "warning":
                                                warningNum++;
                                                break;
                                            case "alarm":
                                                alarmNum++;
                                                break;
                                        }
                                        totalNum++;
                                    }
                                }
                            }
                        }
                        eventGrid.setSizes(false);
                        accMapSetEventFooterValues(totalNum, normalNum, warningNum, alarmNum);//有改动
                    }
                }
            });
		}
		catch(err) 
		{
			if(typeof(console) != "undefined")
			{
				console.log("Error name: " + err.name + "");
				console.log("Error message: " + err.message);
			}
			window.clearTimeout(eventTimeout);
		    // eventTimeout = window.setTimeout("loadEventData()" , 1500);
		}
	}
	
	
	function accMapSetEventFooterValues(rowNum, normalNum, warningNum, alarmNum)
	{
		var totalCount = parseInt($("#totalEventCount").html());
		$("#totalEventCount").html(totalCount + (rowNum ? rowNum : 0));
		
		var normalCount = parseInt($("#normalEventCount").html());
		$("#normalEventCount").html(normalCount + (normalNum ? normalNum : 0));
		
		var warningCount = parseInt($("#warningEventCount").html());
		$("#warningEventCount").html(warningCount + (warningNum ? warningNum : 0));
		
		var alarmCount = parseInt($("#alarmEventCount").html());
		$("#alarmEventCount").html(alarmCount + (alarmNum ? alarmNum : 0));
	}
	
	//事件消息提示
	function accMapEventMsg(id)
	{
	    // var gridName = eventGrid.entBox.id;
		var photoBase64 = eventGrid.getUserData(id, "photoBase64");
		var personPinName = eventGrid.cells(id, eventGrid.getColIndexById("pinName")).getValue();
		var eventTime = eventGrid.cells(id, eventGrid.getColIndexById("eventTime")).getValue();
		var eventName = eventGrid.cells(id, eventGrid.getColIndexById("eventName")).getValue();
		var msgObj = document.getElementById("accMapMsgCheckbox");
		
		if(eventGrid.getUserData(id, "globalLinkageEvent") == null)
		{
			if(msgObj.checked && photoBase64 != "")//选中时开启
			{
				if("data:image/jpg;base64," == photoBase64) {
					photoBase64 = "/images/" + ((sysCfg.skin=='default'|| ! sysCfg.skin ) ? '': sysCfg.skin) + "/userImage.gif";
				}
				var nowTime = new Date().getTime();
				var photoEncrypt = "";
				<#if hasPermission('acc:headPortrait:encryptProp','true')>
					photoEncrypt = "filter: blur(5px);"
				</#if>
                var text = "<div style='padding: 0px 15px 0px 15px;text-align: center'>" +
                    "<img src='" + photoBase64 + "' onerror='this.src=\"/images/userImage.gif\"' style='max-height: ${personPhotoMaxHeight!140}px;border: 1px solid #a4bed4;" + photoEncrypt + "'/>" +
                    "<p>" + personPinName + "</p>" +
                    "<p style='margin-top: 3px; text-overflow:ellipsis; overflow: hidden; width: 150px; white-space:nowrap;'>" + eventName + "</p>" +
                    "<p>" + eventTime + "</p>" +
                    "</div>";
				$.jBox.messager(text, "<@i18n 'common_devMonitor_msgTip'/>", 0, {
					id: "jbox-div",
					width: 'auto',
					showType: 'fade',
					timeout: 5000
				});
				<#if enableRTL?? && enableRTL=="true">
					$(".jbox-messager .jbox").css("right","auto");
					$(".jbox-messager .jbox").css("left","1px");
				</#if>
			}
		}
		else
		{
			var vidLinkageHandle = eventGrid.getUserData(id, "globalLinkageEvent").vidLinkageHandle;
			var vidLinkageData = eventGrid.getUserData(id, "globalLinkageEvent").vidLinkageData;
			if(vidLinkageHandle != undefined && vidLinkageHandle != '')
			{
				var split = vidLinkageHandle.split('_');
				var type = split[0];
				if((type & 2) != 0 && split.length > 1)//包含抓图
				{
					var photoEncrypt = "";
					var captureEncrypt = "";
					<#if hasPermission('acc:headPortrait:encryptProp','true')>
						photoEncrypt = "filter: blur(5px);"
					</#if>
					<#if hasPermission('acc:capturePhoto:encryptProp','true')>
						captureEncrypt = "filter: blur(5px);"
					</#if>
					// 弹出抓拍图
					accPopUpCapture(id, split[1],I18n.getValue("acc_eventNo_8"), "${gridName!}", {
						vidLinkageData: vidLinkageData,
						photoEncryptStyle: photoEncrypt,
						captureEncryptStyle: captureEncrypt
					});
				}
			}
		}
	}
	
	
	//改变文本的颜色
	var num = true;
	var setIntervalOut;
	$( function()
		{ 
			window.clearTimeout(setIntervalOut);
			setIntervalOut = setInterval(changeTextColor, 1000);
		});
	
	function changeTextColor()
	{
		if(mapPosId != -1 )
		{
			if(num)
			{
				num = false;
				$("#" + mapPosId).css({"color":"green", "font-weight":"bold", "font-size":"125%"});
				$("#item_" + mapPosId ).css("border","2px solid green");
			}
			else
			{
				num = true;
				$("#" + mapPosId).css({"color":"black", "font-weight":"normal", "font-size":"100%"});
				$("#item_" + mapPosId ).css("border","none");
			}
			
		}
	}
	
	//打开报警声音
	function playAlarmSound()
	{
		if($("#idAlarmSound").html() == "")//避免声音重复
	  	{
	      	if($.browser.msie)// IE下
	      	{
	          	$("#idAlarmSound").append('<bgsound src="/public/media/sound/alarm.mid" loop="-1"/>');
	      	}
	      	else
	      	{
	          	$("#idAlarmSound").append('<embed src="/public/media/sound/alarm.mid" type="application/x-mplayer2" hidden="true" autostart="true" playcount="true" loop="true" height="0" width="0"/>');
	      	}
		}
	}

	// 关闭报警声音
	function closeAlarmSound()
	{
		$("#idAlarmSound").html("");
	}

	//用于获取电子地图中的实体，比如门。后续还有辅助输入输出。
	function getEntityInfo(url,entityType)
	{
		$.ajax({
			type: "POST",
			url: url,
			dataType: "json",
			data: {"door" : doorsIdArray.toString()},
			async: false,
			success: function(data)
			{
			   showDoorsInfo(data,entityType);
			},
			error:function (XMLHttpRequest, textStatus, errorThrown)
			{
				messageBox({messageType: "alert", title: I18n.getValue("common_prompt_title"), text: "<@i18n 'common_prompt_serverError'/>" + "-628"});
			}
		});
	}

	function renderRTStateAndEvent()
	{
		isRefreshMap = false;
		var accRtUrl = '/accRTMonitor.do?getRTStateAndLog&type=all&logId=' + logId +'&step=100&stateInit=' + mapInit +'&pageId=' + pageId;
		ajaxRTMonitor = $.ajax({
			type: "POST",
			url: accRtUrl,
			dataType: "json",
			data: {"door" : doorsIdArray.toString()},
			async: true,
			success: function(rtData)
			{
				renderStateAndEvents(rtData);
				if(isRefreshMap)
				{
					mapInit = true;
				}
				if(actionName == "accMap" && doorsIdArray.length > 0)//需要判断是否是当前页面,有门时才发送请求
				{
					window.setTimeout("renderRTStateAndEvent()", 3000);//等*秒执行刷新函数
				}
				else//比如跳转到其他页面的情况。
				{
					mapRTMonitorPageInit = false;//确保下次再点击刷新页面时，请求继续发送
				}
			},
			error:function (XMLHttpRequest, textStatus, errorThrown)
			{
				//alert(XMLHttpRequest+textStatus+errorThrown);
				//abort状态下，textStatus和errorThrown两个变量的值均为abort
				if(textStatus != "abort" && actionName == "accMap" && doorsIdArray.length > 0)//需要判断是否是当前页面,有门时才发送请求
				{
					window.setTimeout("renderRTStateAndEvent()", 3000);//等*秒执行刷新函数
				}
				else//比如跳转到其他页面的情况。
				{
					mapRTMonitorPageInit = false;//确保下次再点击刷新页面时，请求继续发送
				}
			}
		});
	}

	var logId = 0;//logId初始值，全局变量
	var baseUrl = "${base}";//基础URL,否则js文件中无法使用。
	var pageId = new Date().getTime();//一个用户可以开多个页面，该值用来保存当前页面创建时间，及初始化状态的时间。
	//-----------门禁监控start
	var doorsIdArray = new Array();
	//-----------门禁监控end

	function vidPreview(channelId, channelName)
	{
		var systemModules = "${systemModules}";
		if (systemModules.toLowerCase().indexOf("vms") != -1) {
			popUpVmsPreview(channelId);
		}else{
			$.jBox.open("iframe:/accMap.do?getVidPreview&channelId="+channelId, "&nbsp;&nbsp;"+channelName, 350, 327, {
				id:"accMapVidPreview",
				showScrolling: false,
				iframeScrolling: 'no',
				showIcon: 'jbox-title-icon-video',
				buttons: { /* '${acc_eventNo_8}': 'ok', '${common_edit_cancel}': 'cancel' */ },
				loaded:function(h){
					var $div = $("#"+this.id+" #jbox");
					$div.find("#jbox-content").css({"overflow-y":"hidden","overflow":"hidden"});
					/* if(navigator.userAgent.indexOf("Firefox")!=-1)
                    {
                        $("#accMapVidPreview #jbox").bind("mouseup.draggable",function(){
                            window.setTimeout(function(){
                                $("#accMapVidPreview .jbox-container iframe")[0].contentWindow.replay();
                            },50);
                        });
                    } */
					closeMessage();
				}
			});
		}
	}

	//关闭页面之后销毁定时请求
    /*if(!mapAttachTabCloseEvent)
    {
        system.tabbar.attachEvent("onTabClose", function(id) {
            //执行销毁
            if(id == "/accMap.do")
            {
                // window.clearTimeout(devStateTimeout);
                window.clearTimeout(eventAudioTimeout);
                // window.clearTimeout(eventTimeout);
            }
            return true;
        });
        mapAttachTabCloseEvent = true;
    }*/

    startMonitor();

</script>
