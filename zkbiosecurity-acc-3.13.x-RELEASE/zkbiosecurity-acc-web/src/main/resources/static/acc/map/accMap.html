<#assign leftRTL = "left">
<#assign rightRTL = "right">
<#if enableRTL?? && enableRTL=="true">
<!-- 定义全局对齐方式变量(针对阿拉伯页面) -->
<#assign leftRTL = "right">
<#assign rightRTL = "left">
</#if>
<#assign gridName="accMapGrid${uuid!}">
<#assign toolbarId="accMapToolbar${uuid!}">
<script type="text/javascript" src="/js/accMap.js" charset="UTF-8"></script>
<script type="text/javascript" src="/system/js/baseMediaFile.js" charset="UTF-8"></script>
<script type="text/javascript" src="/public/controls/jquery/jquery.contextmenu.r2.packed.js" charset="UTF-8"></script>
<style type="text/css">
	.acc_rtm_box {
		position:relative;
		width:100%;
		height:100%;
	}

	.acc_rtm_box .acc_rtm_search {
		width:100%;
	}

	.poPupInfo_op_ul {
		background-color:#fff;
	}

	.acc_rtm_grid {
		position:absolute;
		left:0px;
		right:0px;
		top:0px;
		bottom:20px
	}

	.acc_rtm_grid_foot {
		position:absolute;
		bottom:0px;
		height:18px;
		left:0px;
		right:0px;
		padding: 2px 10px;
	}

	.acc_rtm_box .acc_rtm_tab {
		position:absolute;
		left:0px;
		right:0px;
		bottom:0px;
		top: 34px;
	}

	.acc_rtm_box .acc_rtm_tab .acc_rtm_tab_box {
		position:relative;
		width:100%;
		height:100%;
	}

	.acc_rtm_box .acc_rtm_tab .acc_rtm_tab_cell {
		width:100%;
		height:100%;
	}

	.acc_rtm_dataview {
		position:absolute;
		bottom:20px;
		left:0px;
		right:0px;
		top:40px;
	}
</style>
<script type="text/javascript">

//初始化布局控件
var myLayouts = new Array();
var layoutName = "mapContent";
function initMapLayout()
{
	myLayouts[layoutName] = this.layout;
}

</script>
<@ZKUI.GridBox style="height:100%;width:100%">
	<@ZKUI.Toolbar id="${toolbarId}">
		<@ZKUI.ToolItem id="refresh" action="reloadTree" text="common_op_refresh" img="comm_refresh.png" permission="acc:map:refresh"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="accMap.do?edit" type="add" text="common_op_new" img="comm_add.png" permission="acc:map:add"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="accMap.do?edit&id=" action="editMap" text="common_op_edit" img="comm_edit.png" permission="acc:map:edit"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="accMap.do?del" action="delMap" text="common_op_del" img="comm_del.png" permission="acc:map:del"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="accMap.do?saveMapPos" action="saveMapPos" text="common_op_savePositon" img="comm_saveMapPos.png" permission="acc:map:saveMapPos"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="accMap.do?getEntitySelectItem&entityType=AccDoor" action="addEntityToMap" text="acc_map_addDoor" img="acc_addDoor.png" permission="acc:map:addDoorToMap"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="accMap.do?getEntitySelectItem&entityType=VidChannel" action="addChannelToMap" text="acc_map_addChannel" img="acc_map_addChannel.png" permission="acc:map:addChannelToMap" isShow="JAVA#com.zkteco.zkbiosecurity.acc.dhx.AccShowBindChannel.isShow"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="zoomIn" type="button" action="newZoomIn" text="common_op_zoomIn" img="comm_zoomIn.png" ></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="zoomOut" type="button" action="newZoomOut" text="common_op_zoomOut" img="coom_zoomOut.png" ></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="fullScreen" type="button" action="fullScreen" text="common_op_fullScreen" img="base_map_fullScreen.png" ></@ZKUI.ToolItem>
	</@ZKUI.Toolbar>
	<@ZKUI.Layout id="layout${uuid!}" style="position:absolute; top:35px;bottom:0px;left:0px;right:0px;width:auto;height:auto;" onInit="initMapLayout" pattern="3L">
		<@ZKUI.Cell width="15%" title="base_map_list" >
			<@ZKUI.Tree id="tree${uuid!}" url="/accMap.do?getMapTree" onXLE="loadSelectedMap" onClick="treeClick"></@ZKUI.Tree>
			
		</@ZKUI.Cell>
		<@ZKUI.Cell title="base_map_currentMap" height="400">
			
		</@ZKUI.Cell >
		<@ZKUI.Cell title="acc_rtMonitor_realTimeEvent">
			<div class="acc_rtm_grid">
				<@ZKUI.Grid gridName="${gridName}" showColumns="!filter" originSort="true" vo="com.zkteco.zkbiosecurity.acc.vo.AccRTMonitorItem" gridType="right" nopaging="true"/>
			</div>
			<div id="menuData${uuid}" style="display: none;">
				<@ZKUI.Permission name="pers:person:edit">
					<div id="addCardToNewPerson" img="pers_batchAddPersCard.png" text="<@i18n 'common_rtMonitor_cardRegisterPerson'/>"></div>
				</@ZKUI.Permission>
				<div id="addCardToOldPerson" img="pers_batchAddPersCard.png" text="<@i18n 'acc_rtMonitor_addToRegPerson'/>"></div>
			</div>
			<div id="eventTop_footer" class="dhx_toolbar_dhx_web acc_rtm_grid_foot">
				<div id="alert_sound"></div>
				<div id="alarm_sound"></div>
				<div style="float: ${leftRTL!'left'};">
					<@i18n 'common_rtMonitor_totalReceived'/>:<em id="totalEventCount">0</em><em id="maxEventCount" hidden ="true">200</em>
				</div>
				<div class="divBorder" style="margin-${leftRTL!'left'}: 5%;">
					<span style="display:inline-block"><span class="icv-greenPilot"></span><@i18n 'common_normal'/>:<em id="normalEventCount">0</em>&nbsp; </span>
					<span style="display:inline-block"><span class="icv-yellowPilot"></span><@i18n 'common_exception'/>:<em id="warningEventCount">0</em>&nbsp;</span>
					<span style="display:inline-block"><span class="icv-redPilot"></span><@i18n 'common_alarm'/>:</a><em id="alarmEventCount">0</em></span>
				</div>
				<div class="divBorder" style="margin-${leftRTL!'left'}: 5%;">
					<a href="javascript:accMapClearEventRow()"><@i18n 'common_rtMonitor_clearRowsData'/></a>
				</div>
				<div class="divBorder" style="margin-${leftRTL!'left'}: 5%;">
					<a href="javascript:void(0);" id="a_newMsg${uuid!}" style="display: none;"><img height="12px" src="/images/newMsg.gif"/><@i18n 'common_newMsg'/>:<em id="newMsgEventCount">0</em></a>
				</div>
				<div id="showPhoto" style="float: ${rightRTL!'right'};">
					<@i18n 'common_rtMonitor_showPhotos'/>
					<@ZKUI.Input hideLabel="true" id="accMapMsgCheckbox" type="checkbox" checked="checked"/>
				</div>
				<div id="showAudio" style="float: ${rightRTL!'right'};">
					<@i18n 'acc_rtMonitor_playAudio'/>
					<@ZKUI.Input hideLabel="true" id="accMapAudioCheckbox" type="checkbox"/>
				</div>
			</div>
		</@ZKUI.Cell >
		
	</@ZKUI.Layout>
	
</@ZKUI.GridBox>

<div id="mapLeft" style="width: 100%;"></div>
<div id="mapRight${uuid}" style="width: 100%;"></div>

<script type="text/javascript">


//mapPosId 编号，idMap 地图编号，accDoorId 事件点编号
var mapPosId = "${mapPosId!-1}";
var idMap = "${idMap!-1}";
var accDoorId = "${accDoorId!-1}";
var isPersonLastAddr="${isPersonLastAddr!-1}";

var getMapTreeUrl = "/accMap.do?getMapTree";
var getMapUrl = "/accMap.do?getMap&gridName=${gridName!}&id=";

var mapTree
//区域地图树加载完之后，回调函数，用于加载右边地图
function loadSelectedMap()
{
	mapTree = this;
	if (mapTree.getSelectedItemId() == "")
	{
  		// var model= mapTree.getItemText(mapTree._selected[0].id);
  		var mapId = "";
  		var mapIdList = mapTree.getAllLeafs();
  		if (mapIdList != "" && mapIdList != "_") {
  		    var mapIdArray = mapIdList.split(",");
  		    if (mapIdArray.length > 0) {
  		        mapId = mapIdArray[0];
			}
		}
		if(mapTree.getIndexById(getCookie("mapTreeId")) != null)
		{
			mapId = getCookie("mapTreeId");
		}
	  		//填充右边框架内容
		if(idMap != -1 && mapPosId != -1 && accDoorId != -1)
		{
			mapId = idMap;	
		}

		if(mapId != "")
		{
            mapTree.selectItem(mapId,true,true);
		}
		else
		{
            $("#mapRight${uuid}").html("");
            myLayouts[layoutName].cells(IS_ENABLE_RTL ? "a" : "b").setText("<@i18n 'base_map_currentMap'/>");
            setMapRightHtmlByPath(getMapUrl,"mapRight${uuid}");
		}
		
	}
	else
	{
		$("#mapRight${uuid}").html("");
		myLayouts[layoutName].cells(IS_ENABLE_RTL ? "a" : "b").setText("<@i18n 'base_map_currentMap'/>");
	}
}

function reloadTree()
{
	mapTree.refreshItem();
	// 重置放大/缩小参数
	multiple = 0;
	zoomScale = 1;
}

function createItemCookie(itemId)
{
    createCookie("mapTreeId", itemId);
}

var accMapTreeClickTime = 0;//下一次点击时间
var clickId = "";
function treeClick(id) {
    var t = new Date().getTime();
    if(t < accMapTreeClickTime && clickId == id)
    {
        return;
    }
    clickId = id;
    accMapTreeClickTime = t + 2000;//间隔2秒之后点击才有效果
	if(id.toString().indexOf("_")<0 && id.toString() != "0")
	{
        createItemCookie(id);
		//通过当前选中的菜单来设置右边的标题文本
		var model = mapTree.getItemText(id);
		var text = "<img src='/public/images/title-icon.gif'/><font style='font-weight: bold;'>" + model + "</font>";
		myLayouts[layoutName].cells(IS_ENABLE_RTL ? "a" : "b").progressOn();
		//设置右边内容
		setMapRightHtmlByPath(getMapUrl+id,"mapRight${uuid}");
		//加载地图的图片
		myLayouts[layoutName].cells(IS_ENABLE_RTL ? "a" : "b").attachObject("mapRight${uuid}");
		myLayouts[layoutName].cells(IS_ENABLE_RTL ? "a" : "b").progressOff();
		myLayouts[layoutName].cells(IS_ENABLE_RTL ? "a" : "b").setText(text);
		// 重置放大/缩小参数
		multiple = 0;
		zoomScale = 1;
	}
	return true;
}

//通过请求来设置某对象的文本
function setMapRightHtmlByPath(path, id)
{
	$.ajaxSetup({
		async : true
	});
	$.get(path, function(result)
	{
		$("#" + id).html(result);
	}, "html");
}

function loadAccMapRightClickMenu()
{
    var cardNo;
    var eventGrid = ZKUI.Grid.get("${gridName}").grid;
    var menu = new dhtmlXMenuObject();
    menu.setIconsPath(sysCfg.rootPath + "/public/images/opToolbar/");
    menu.renderAsContextMenu();
    menu.attachEvent("onClick", function onButtonClick(menuItemId, type) {
        var dhxGrid = ZKUI.Grid.get("${gridName}").grid;
        var data = dhxGrid.contextID;
        if(menuItemId == "addCardToNewPerson") {
            DhxCommon.createWindow("/persPerson.do?edit&cardNo=" + cardNo + "^0^0^950^650^<@i18n 'common_op_new'/>");
        }
        else {
            DhxCommon.createWindow("/accPerson.do?filterPersonByVaildCard&cardNo=" + cardNo + "^0^0^900^500^<@i18n 'pers_person_selectedPerson'/>");
        }
        return true;
    });
    menu.loadFromHTML("menuData${uuid}", true, function () {});
    eventGrid.enableContextMenu(menu);
    eventGrid.attachEvent("onBeforeContextMenu", function(rowId, celInd, grid){
    	// 开启卡号敏感信息保护,屏蔽右键功能
		<#if !hasPermission('acc:cardNo:encryptProp', 'true')>
			//判断事件点，过滤右键功能,当事件点的eventNo=27才能显示右键菜单
			if(this.getUserData(rowId, "isExistCardOp")) {
				cardNo = this.cells(rowId, this.getColIndexById("cardNo")).getValue();
				return true;
			}
		</#if>
        return false;
    });
}

//初始化实时监控列表
initEventGrid();//创建Grid
function initEventGrid()
{
    loadAccMapRightClickMenu();
}
//跳转到报警监控
function redirectToAlarmPage()
{
	var path = "/accAlarmMonitor.do?list";
	// window.top.system.submenuTree.selectItem("iframe:" + path,true,true);
    window.location.href = path;
}

//清空行
function accMapClearEventRow()
{
	eventGrid.clearAll();
    $("#totalEventCount").html(0);
	$("#normalEventCount").html(0);
	$("#warningEventCount").html(0);
	$("#alarmEventCount").html(0);
}


//消息提示
function enableMsg(obj)
{
	if(obj.checked)//选中时则播放
	{
		openMessage(msgType.success, "<@i18n 'common_devMonitor_turnedMsgTip'/>");//运行消息提示
	}
	else
	{
		openMessage(msgType.warning, "<@i18n 'common_devMonitor_closedMsgTip'/>");//关闭消息提示
	}
	createCookie("accMapRTEventMsg${(user.id)!}", obj.checked);
}

$("#accMapMsgCheckbox").click(function() {
	enableMsg(this);
});

//音频是否播放提示
function enableAudio(obj)
{
	createCookie("accMapRTEventAudio${(user.id)!}", obj.checked);
	if(obj.checked)//选中时则暂停
	{
		openMessage(msgType.success, "<@i18n 'acc_rtMonitor_alarmSoundOpen'/>");//运行消息提示
	}
	else
	{
		openMessage(msgType.warning, "<@i18n 'acc_rtMonitor_alarmSoundClose'/>");//关闭消息提示
		//关闭事件声音
		//closeAlarmSound();
		closeAudio("alert_sound");
		//关闭门声音
		closeDoorSound();
	}
}

$("#accMapAudioCheckbox").click(function() {
	enableAudio(this);
});

function closeDoorSound()
{
	var doorAlarmAudio = $("audio[name='alarmAudio']");
	if(doorAlarmAudio != "undefined")
	{
		for(var i = 0; i < doorAlarmAudio.length; i++)
		{
			removeObj(doorAlarmAudio[i]);
		}
	}
	doorAlarmAudio = $("bgsound[name='alarmSound']");
	if(doorAlarmAudio != "undefined")
	{
		for(var i = 0; i < doorAlarmAudio.length; i++)
		{
			removeObj(doorAlarmAudio[i]);
		}
	}
}

//移除对象，解决IE不支持remove方法
function removeObj(obj)
{
	var parentObj = obj.parentNode;
	if(parentObj)
	{
		parentObj.removeChild(obj);
	}
}

//改变文本的颜色，人员最后访问位置跳转过来时，高亮对应位置。
var num = true;
var setIntervalOut;
$( function()
{
    window.clearTimeout(setIntervalOut);
    setIntervalOut = setInterval(changeTextColor, 1000);
});

function changeTextColor()
{
    if(mapPosId != -1 )
    {
        if(num)
        {
            num = false;
            $("#" + mapPosId).css({"color":"green", "font-weight":"bold", "font-size":"125%"});
            $("#item_" + mapPosId ).css("border","2px solid green");
        }
        else
        {
            num = true;
            $("#" + mapPosId).css({"color":"black", "font-weight":"normal", "font-size":"100%"});
            $("#item_" + mapPosId ).css("border","none");
        }

    }
}

// 恢复显示图片和信息的状态
$(function () {
    var audioCheck = getCookie("accMapRTEventAudio${(user.id)!}");
    var msgCheck = getCookie("accMapRTEventMsg${(user.id)!}");
    if(audioCheck!=undefined && audioCheck!=null){
        changeCheckStatus("#accMapAudioCheckbox", audioCheck);
	}
    if(msgCheck!=undefined && msgCheck!=null){
        changeCheckStatus("#accMapMsgCheckbox", msgCheck);
    }
});

function changeCheckStatus(id, status){
	if(status === true || status === "true"){
		$(id).attr("checked", "checked");
	}else {
		$(id).removeAttr("checked");
	}
}

</script>
<#if systemModules?lower_case?index_of("vid") != -1>
	<link rel="stylesheet" type="text/css" href="/css/vidPopUpPreview.css?un=${sysTimeMillis}" />
	<script type="text/javascript" src="/js/vidPopUpPreview.js" charset="UTF-8"></script>
	<script type="text/javascript" src="/js/vidPreview.js" charset="UTF-8"></script>
<#elseif systemModules?lower_case?index_of("vms") != -1>
	<script type="text/javascript" src="/js/vmsPreview.js" charset="UTF-8"></script>
	<script type="text/javascript" src="/js/vmsPopUpPreview.js" charset="UTF-8"></script>
</#if>