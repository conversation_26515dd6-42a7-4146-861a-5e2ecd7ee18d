<@ZKUI.SelectContent onSure="dealSelectChannel" showColumns="checkbox,name,alias,sn" textField="name" value="" vo="com.zkteco.zkbiosecurity.acc.vo.AccMapSelectChannelItem" query="/accMapSelectChannel.do?list&mapId=${mapId!}">
    <@ZKUI.Searchbar>
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="name"  maxlength="30" title="common_dev_channelName" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="sn"  maxlength="30" title="common_dev_sn" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    
</@ZKUI.SelectContent>

<script type="text/javascript">

//添加门，点确定之后调用的方法
function dealSelectChannel(value, text, event)
{
	var mapId = "${(mapId)!}";
	var entityType = "${(entityType)!}";
	var width = "${(width)!}";
	var logMethod = "${(logMethod)!}";
	if(value == "-1" || value == "")
	{
		messageBox({messageType:"alert",text: "<@i18n 'vid_channel_needSelected'/>"});
		return null;
	}
	else
	{
		$.ajax({
			url:"/accMap.do?addChannel",
			type:"post",
			data: {
				mapId:mapId,
				width:width,
				entityType:entityType,
				entityIds:value,
				logMethod:logMethod,
                name:text
			},
			success:function(result) {
				dealRetResult(eval(result),function(){
					mapTree.selectItem(mapId,true,true);
				});
			}
	    });
	}
	
}
</script>