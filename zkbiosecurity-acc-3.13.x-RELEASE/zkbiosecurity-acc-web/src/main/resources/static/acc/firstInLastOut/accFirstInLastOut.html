<#assign gridName="accFirstInLastOut${uuid!}">
<@ZKUI.GridBox gridName="${gridName}" style="height:100%;width:100%">
<@ZKUI.Searchbar>
<@ZKUI.SearchTop>
<tr>
    <td valign="middle">
        <@i18n 'common_time_from'/>&nbsp;&nbsp;
        <@ZKUI.Input type="datetime" endId="endTimeId${uuid}" id="startTime${uuid}" name="startTime" title="common_time_from" max="today" todayRange="start" today="-7" offsetField="D" hideLabel="true" noOverToday="true" readonly="false"/>
        &nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
        <@ZKUI.Input type="datetime" id="endTimeId${uuid}" name="endTime" title="common_to" max="today" today="true" todayRange="end" noOverToday="true" hideLabel="true" readonly="false"/>
    </td>
    <td valign="middle">
        <@ZKUI.Input name="pin" maxlength="30" title="pers_person_pin" type="text"/>
    </td>
</tr>
</@ZKUI.SearchTop>
<@ZKUI.SearchBelow>
<tr>
    <td valign="middle">
        <@ZKUI.Input name="deptCode" maxlength="30" title="pers_dept_deptNo" type="text"/>
    </td>
    <td valign="middle">
        <@ZKUI.Input name="deptName" maxlength="30" title="pers_dept_deptName" type="text"/>
    </td>
    <td valign="middle">
        <@ZKUI.Input name="likeName" maxlength="30" title="pers_person_wholeName" type="text"/>
    </td>
</tr>
</@ZKUI.SearchBelow>
</@ZKUI.Searchbar>
<@ZKUI.Toolbar>
<@ZKUI.ToolItem id="refresh" type="refresh" permission="acc:firstInLastOut:refresh"></@ZKUI.ToolItem>
<@ZKUI.ToolItem id="accFirstInLastOut.do?clearData" action="accClearFirstInLastOutData" text="common_op_clearData" img="comm_del.png" permission="acc:firstInLastOut:clearData"></@ZKUI.ToolItem>
<@ZKUI.ToolItem id="accFirstInLastOut.do?export" type="export" permission="acc:firstInLastOut:export"></@ZKUI.ToolItem>
</@ZKUI.Toolbar>
<@ZKUI.Grid pageList="true" limitCount="200000" vo="com.zkteco.zkbiosecurity.acc.vo.AccFirstInLastOutItem" query="accFirstInLastOut.do?list" showColumns="!deptCode"/>
</@ZKUI.GridBox>

<script type="text/javascript">
    var gridName = "${gridName}";
    function accClearFirstInLastOutData(id, bar) {
        deleteConfirm(function(result) {
            if (result) {
                openMessage(msgType.loading);
                $.ajax({
                    url : id,
                    type : "post",
                    success : function(result) {
                        dealRetResult(eval(result), function() {
                            closeMessage();
                            ZKUI.Grid.reloadGrid(gridName);
                        });
                    }
                });
            }
        }, "<@i18n 'common_prompt_sureToDelAll'/>");

    }
</script>