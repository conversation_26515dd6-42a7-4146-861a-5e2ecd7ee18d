<#include "/public/template/editTemplate.html">
<#assign leftRTL = "left">
<#assign rightRTL = "right">
<#if enableRTL?? && enableRTL=="true">
<!-- 定义全局对齐方式变量(针对阿拉伯页面) -->
<#assign leftRTL = "right">
<#assign rightRTL = "left">
</#if>
<#macro editContent>
<div style="margin: 10px">
    <form action="accAlarmMonitor.do?editStatus" method="post" id="${formId}" enctype="multipart/form-data">
        <input type="hidden" id="alarmId" name="id" value="${id!}"/>
        <table class="tableStyle">
            <tr>
                <td><@i18n 'acc_alarm_eventDescription'/></td>
            </tr>
            <tr>
                <td>
                <textarea style="width: 500px; height: 80px;" disabled="disabled">${description!}</textarea>
                </td>
            </tr>
            <tr id="loginPwdRow" hidden="hidden">
                <td>
                    <@i18n 'auth_user_userPwd'/><span class="required">*</span>
                    <input type="password" maxlength="18" id="alarmLoginPwd${uuid!}" name="userLoginPwd" autofocus="true"/>
                    <input type="hidden"  name="loginPwd" id="password_hidden"/>
                </td>
            </tr>
            <tr>
                <td>
                    <div style="float: ${leftRTL!'left'}; margin-${rightRTL!'right'}: 20px">
                        <@ZKUI.Input hideLabel="true" type="radio" id="processStatus" name="status" value="1" checked="checked" />
                        <@i18n 'acc_alarm_inProcess'/>
                    </div>
                    <div style="float: ${leftRTL!'left'}">
                        <@ZKUI.Input hideLabel="true" type="radio" id="acknowledgedStatus" name="status" value="2"/>
                        <@i18n 'acc_alarm_acknowledged'/>
                    </div>
                </td>
            </tr>
            <tr>
                <td><@i18n 'acc_alarm_acknowledgement'/></td>
            </tr>
            <tr>
                <td>
                    <textarea style="width: 500px; height: 80px;" form="${formId}" name="acknowledgement" maxlength="100"></textarea>
                </td>
            </tr>
            <tr style="display:none">
                <td>
                    <@ZKUI.Input hideLabel="true" type="checkbox" name="sendEmail" id="isSendEmail" value="true" checked="checked"/>
                    <@i18n 'pers_person_IsSendMail'/>
                    <div><span class="warningImage"></span> <span class="warningColor"><@i18n 'acc_alarm_acknowledgeText'/></span></div>
                </td>
            </tr>
            <tr style="display:none">
                <td>
                    <div id="emailTextarea">
                        <textarea style="width: 500px; height: 50px;" form="${formId}" name="emails" placeholder="<@i18n 'common_email_example'/>"></textarea>
                        <div><span class="warningImage"></span> <span class="warningColor"><@i18n 'common_linkIO_errorMailTip'/></span></div>
                    </div>
                </td>
            </tr>
        </table>
    </form>
</div>
<script type="text/javascript">
    $().ready(function() {

        $("#${formId}").validate( {
            debug : true,
            rules :
                {
                    "status" :
                        {
                            required : true
                        },
                    "acknowledgement":
                        {
                            validInputStr:true,
                            maxlength:100
                        },
                    "emails":
                        {
                            accParamMailValid:true
                        }
                },
            submitHandler: function() {
                if(!getRowById($("#alarmId").val())){
                    messageBox({messageType:"alert", text: "<@i18n 'acc_alarm_confirmed'/>"});
                    return;
                }
                foreSubmit();
            },
        });
    });
    function foreSubmit() {
        $("#${formId} #editForm${uuid}").attr("disabled",true);
        $('#${formId}').ajaxSubmit({
             async : true,
             dataType : 'json',
             success: function(result)
             {
                if(result.ret == "500") {
                    openMessage(msgType.error, result[sysCfg.msg] );
                } else {
                    try {
                     if(result.data!=null && deleteRowById != undefined){
                         deleteRowById(result.data);
                     }
                     isSubmiting=false;
                     if(loadNewAlarm){
                         loadNewAlarm();
                     }
                 }catch (e) {
                     reloadAlarmReport();
                 }
                 DhxCommon.closeWindow();
                }
             },
             error: function(XMLHttpRequest, textStatus, errorThrown)
             {
                 isSubmiting=false;
                 if(XMLHttpRequest.status == 0)
                 {
                     openMessage(msgType.error, "<@i18n 'common_prompt_serverFailed'/>");
                 }
                 else
                 {
                     openMessage(msgType.error, "<@i18n 'common_prompt_serverError'/>" + XMLHttpRequest.status);
                 }
             }
        });
    }
    jQuery.validator.addMethod("accParamMailValid", function(val, element)
    {
        //var mailReg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
        var mailReg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*$/;
        if (val == "")
        {
            return true;
        }
        else
        {
            var mailArray = val.replace(/;/g, ",").split(",");//统一将输入的分号替换成逗号，支持混合输入
            for (var i = 0; i < mailArray.length; i++)
            {
                if (mailArray[i] != "" && !(this.optional(element) || (mailReg.test(mailArray[i]))))
                {
                    return false;
                }
            }
            return true;
        }
    }, I18n.getValue("common_email_inputEmailError"));

     $("input[name='sendEmail']:checkbox").click(function(){
        if($(this).attr("checked") == "checked")
        {
           $('#emailTextarea').show();
        } else {
            $('#emailTextarea').hide();
        }
    });
    $("#alarmLoginPwd${uuid!}").on("change", function() {
		if($("#alarmLoginPwd${uuid!}").val()) {
			$("#password_hidden").val(hex_md5($("#alarmLoginPwd${uuid!}").val()));
		}
		else {
			$("#password_hidden").val("");
		}
	})
	isNeedValidUserForExport(function(ret) {
		if(ret && ret.ret === sysCfg.success) {
			$("#loginPwdRow").hide();
			$("input[name='userLoginPwd']").rules("remove");
		} else {
			$("#loginPwdRow").show();
			$("input[name='userLoginPwd']").rules("add", {required : true});
		}
	})
</script>
</#macro>