<#assign leftRTL = "left">
<#assign rightRTL = "right">
<#if enableRTL?? && enableRTL=="true">
<!-- 定义全局对齐方式变量(针对阿拉伯页面) -->
<#assign leftRTL = "right">
<#assign rightRTL = "left">
</#if>
<#assign gridName="alarmMonitoringGrid${uuid!}">
<link rel="stylesheet" href="/css/accAlarmMonitor.css" type="text/css"></link>
<#if enableRTL?? && enableRTL=="true">
<link rel="stylesheet" href="/css/accAlarmMonitorRTL.css" type="text/css"></link>
</#if>
<div class="alarmContent leftAlarm">
	<!--分析-->
	<div class="leftContent zk-content-bg-color" style="margin-top:-10px">
		<div class="accAlarmTopTitle"><@i18n 'acc_musterPointReport_dataAnalysis'/></div>
		<div id="totalStr"><@i18n 'acc_alarm_total'/></div>
		<div id="totalShow">0</div>
		<div id="main" style="width: 289px;height:150px;"></div>
		<!--today-->
		<div style="width: 100%; height: 100px" class="zk-content-bg-color">
			<div class="accAlarmTopTitle" style="margin: 0;"><@i18n 'acc_alarm_today'/></div>
			<div style="width: 280px; margin: auto; height: 50px">
				<div class="todayInfo unbandledInfo accAlarmInfoBg" style="text-align:center !important" name="unhandled" onclick="disableStatus(this)">
					<div id="unhandled" class="infoNum accInfoNum">0</div>
					<div class="accInfoNum"><@i18n 'acc_alarm_unhandled'/></div>
				</div>
				<div class="todayInfo progressInfo accAlarmInfoBg" style="text-align:center !important" name="inProgess" onclick="disableStatus(this)">
					<div id="progress" class="infoNum accInfoNum">0</div>
					<div class="accInfoNum"><@i18n 'acc_alarm_inProcess'/></div>
				</div>
				<div class="todayInfo acknowledgedInfo accAlarmInfoBg" style="text-align:center !important">
					<div id="acknowledged" class="infoNum zk-colors-fg-green accInfoNum">0</div>
					<div class="accInfoNum"><@i18n 'acc_alarm_acknowledged'/></div>
				</div>
			</div>
		</div>
	</div>
	<!--top5-->
	<div class="leftContent zk-content-bg-color">
		<div class="accAlarmTopTitle">
			<@i18n 'acc_alarm_top5'/>
		</div>
		<div id="topShowContent" style="width: 260px"></div>
	</div>
	<!--time-->
	<div class="leftContent zk-content-bg-color" id = "timeContent">
		<div class="accAlarmTopTitle">
			<@i18n 'acc_alarm_monitoringTime'/>
		</div>
		<div style="height: 110px;margin: 20px 10px;margin-bottom: 0px;">
			<div id="watchAlarmTime">00:00</div>
			<div style="margin: 10px 0"><@i18n 'common_timeSeg_startTime'/></div>
			<div id="nowTime"></div>
		</div>
	</div>
	<div class="leftContent zk-content-bg-color" id="controlShow">
		<!--        <div style="width:18px;margin: auto;user-select: none" onclick="$('#controlContent').slideToggle()"><img src="public/images/opToolbar/comm_moreActions.png" alt=""></div>-->
		<div style="margin: 10px auto;height: 35px;" id="controlContent">
			<div style="float: left; padding-right: 13px" class="uncheckDiv button-borderColor" id="accAlarmSoundPause${uuid!}" onclick="clickSoundPauseButton(this)">
				<div style="height: 100%; user-select: none;">
					<img src="images/alarm/alarm_mute.png" style="height: 18px;width: 18px;margin: 7px">
					<@i18n 'acc_alarm_mute'/>
				</div>
			</div>
			<div style="float: right;padding-right: 13px" class="uncheckDiv button-borderColor" id="accAlarmSuspendPause${uuid!}" onclick="clickPauseButton(this)">
				<div style="height: 100%; user-select: none">
					<img src="images/alarm/alarm_pause.png" style="height: 18px;width: 18px;margin: 7px">
					<@i18n 'acc_alarm_suspend'/>
				</div>
			</div>
		</div>
	</div>
</div>
<div class="alarmContent rightAlarm">
	<@ZKUI.GridBox gridName="${gridName}">
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem id="accAlarmMonitor.do?editAcknowledged" width="600" height="400" title="acc_rtMonitor_ackAlarm" action="commonOpenOperateOne" img="acc_alarmMonitor.png" text="acc_rtMonitor_ackAlarm" permission="acc:alarmMonitor:ackAlarm"/>
			<@ZKUI.ToolItem id="accAlarmMonitor.do?showHistory" title="acc_alarm_history" width="600" height="520" action="commonOpenOperateOne" img="acc_viewing_rules.png" text="acc_alarm_history" permission="acc:alarmMonitor:history"/>
		</@ZKUI.Toolbar>
		<@ZKUI.Grid originSort="true" vo="com.zkteco.zkbiosecurity.acc.vo.AccAlarmMonitorItem" gridType="right" nopaging="true"/>
	</@ZKUI.GridBox>
</div>

<script>
    var dhxGridT = ZKUI.Grid.get("${gridName}");
    var myChart = echarts.init(document.getElementById('main'));
    var dataName = ["<@i18n 'acc_musterPointReport_danger'/>", "<@i18n 'auth_security_strengthLevel2'/>", "<@i18n 'auth_security_strengthLevel1'/>", "<@i18n 'auth_security_strengthLevel0'/>"];
    var colorSelect = [
        [ '#FBC02D', '#FFA000', '#F57C00', '#F44336'],
        [ '#2F75B5', '#FFD966', '#FD5C0C', '#D8090F'],
        [ '#0F4C81', '#FFD966', '#FD5C0C', '#D8090F'],
        [ '#7AC143', '#FFD966', '#FD5C0C', '#D8090F']
    ];

    var textColors = {
        "default": '#000',
        "lightgreen":'#000',
        "techblue" : '#cbe1f1'
    }
    // 选择最后一套配色方案
    var skin = getSystemSkinAcc();
    var colors = colorSelect[2];
    var textColor = textColors[skin];
    if(skin == "techblue")
    {
    	colors = colorSelect[3];
    }

        // 当前页数
    window.alarmCurrentPageNo = 0;
    // 初始化最大值
    window.alarmCurrentFlag = 0;
    window.alarmFilter = {};
    window.alarmFilter.priority = {
                                    Critical: true,
                                    Higher: true,
                                    Medium: true,
                                    Low: true};
    window.alarmFilter.status = {unhandled:true, inProgess:true};

    window.timeInterval = null;
    window.alarmInterval = null;
    $(function () {
        dhxGridT.grid.setColSorting('');
        initTime();
        loadInitPage();
        initDevEventClient();
        initOnPageLoad();
        firstShowOrHideTime();
        initAccAlarmButtonIcon();
    });

    function createGrid(dataVal) {
        var option = {
            tooltip: {
                trigger: 'item',
                formatter: '{a}:{b} <br/>{c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: '53%',
                top: 'center',
                itemWidth: 20,          // 图形宽度。
                itemHeight: 20,         // 图形高度。
                data: dataName,
                formatter: function(name) {
                    let formatName=name;
                    for(let item in dataVal){
                        if(dataVal[item].name == name){
                            formatName = name + " (" + dataVal[item].value + ")";
                        }
                    }
                    return formatName;
                },
                textStyle: {
                	color: textColor
            	}
            },
            title: {
                show:false,
                text:'Total',
                left: '17%',
                top: '36%',
                textStyle:{
                    color:'#031f2d',
                    fontSize: '12',
                    align:'center'
                }
            },
            color : colors,
            series: [
                {
                    name: "<@i18n 'acc_alarm_priority'/>",
                    type: 'pie',
                    // radius: ['50%', '70%'],
                    radius: ['50%', '80%'],
                    center: ['25%', '50%'],
                    avoidLabelOverlap: false,
                    label: {
                        normal: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            show: false,
                            textStyle: {
                                fontSize: '15',
                                fontWeight: 'bold'
                            }
                        }
                    },
                    labelLine: {
                        normal: {
                            show: false
                        }
                    },
                    data: dataVal,
                    itemStyle: { // 此配置
                        normal: {
                            borderWidth: 2,
                            borderColor: 'rgb(235,235,235)',
                        },
                        emphasis: {
                            borderWidth: 0,
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }
            ]
        };
        myChart.setOption(option);
        var total = 0;
        for (let i = 0; i < dataVal.length; i++) {
            total+=dataVal[i].value;
        }
        $("#totalShow").text(total);

        for (let i = 0; i < dataVal.length; i++) {
            $("#"+dataVal[i].name).text(dataVal[i].value);
        }
    }

    myChart.on('legendselectchanged', function(obj) {
        var selected = obj.selected;
        // 使用 legendToggleSelect Action 会重新触发 legendselectchanged Event，导致本函数重复运行
        // 使得 无 selected 对象
        if (selected != undefined) {
            window.alarmFilter.priority = selected;
        }
        scrollT = 0;
        loadInitPage();
    });


    function initTop5(dataList) {
        $("#topShowContent").html("");
        for (let i = 0; i < dataList.length; i++) {
            $("#topShowContent").append("<div style='width: 100%; height: 15px;margin-top: 15px; clear: both'>" +
                        "<div style=\"float: ${leftRTL!'left'};\">"+dataList[i][0]+"</div>" +
                        "<div style=\"float: ${rightRTL!'right'}; color: #F44336\">"+dataList[i][1]+"</div>" +
                "</div>");
        }
    }

    var startTime;

    function initTime(){
        startTime = new Date();
        $("#watchAlarmTime").text("00:00");
        var dayStr = new Date().format("yyyy-MM-dd HH:mm:ss");
        $("#nowTime").text(dayStr);
        initTimeInterval();
    }

    function initTimeInterval() {
        if(window.timeInterval!=null){
            window.clearInterval(window.timeInterval);
        }
        window.timeInterval = window.setInterval(function () {
            var now = new Date();
            $("#watchAlarmTime").text(formatSeconds((now - startTime)/1000));
        },1000);
    }

    function initToday(val) {
        $("#unhandled").text(val.unhandled);
        $("#progress").text(val.progress);
        $("#acknowledged").text(val.acknowledged);
    }

    function formatSeconds(value) {
        var theTime = parseInt(value);// 秒
        var theTime1 = 0;// 分
        var theTime2 = 0;// 小时
        if (theTime > 60) {
            theTime1 = parseInt(theTime / 60);
            theTime = parseInt(theTime % 60);
            if (theTime1 > 60) {
                theTime2 = parseInt(theTime1 / 60);
                theTime1 = parseInt(theTime1 % 60);
            }
        }

        var result = "" + parseInt(theTime);//秒
        if (10 > theTime > 0) {
            result = "0" + parseInt(theTime);//秒
        } else {
            result = "" + parseInt(theTime);//秒
        }

        if (10 > theTime1 > 0) {
            result = "0" + parseInt(theTime1) + ":" + result;//分，不足两位数，首位补充0，
        } else {
            result = "" + parseInt(theTime1) + ":" + result;//分
        }
        if (theTime2 > 0) {
            result = "" + parseInt(theTime2) + ":" + result;//时
        }
        return result;
    }


    var scrollT;
    var nextFlag = true;

    var scrollHandler = function () {
        var tableH = $(".objbox>table").height();
        var pageH = $(".objbox").height();
        scrollT = $(".objbox").scrollTop(); //滚动条top
        if (tableH - pageH - scrollT <= 50) {
            if(nextFlag){
                nextFlag = false;
                $(".objbox").off("scroll",scrollHandler);
                loadNextPage();
                nextFlag = true;
            }
        }
    };

    function loadNewAlarm() {
        var dhxGrid = ZKUI.Grid.get("${gridName}");
        var firstRowId = 0;
        var filter = getFilter();
        $.ajax({
            url:"accAlarmMonitor.do?getDataByEventNum",
            data:filter,
            method: "post",
            success: function (res) {
                var analysis = res.data.analysis;
                var createRes = {};
                createRes.data = res.data.alarm;
                loadDataToGrid(createRes, true);
                createGrid(analysis.chartValue);
                initToday(analysis.todayValue);
                initTop5(analysis.top5Value);
            },
            error:function (res) {
                console.log(res)
                console.log("err loadNewAlarm res")
            }
        });
    }

    /**
     * 获取下一页
     * @returns {boolean}
     */
    function loadNextPage(){
        var filter = getFilter();
        filter["pageNo"] = window.alarmCurrentPageNo;
        filter["targetNum"] = window.alarmCurrentFlag;
        $.ajax({
            url:"accAlarmMonitor.do?getInfoByPageNo",
            data: filter,
            method: "post",
            async: false,
            success:function (res) {
                loadDataToGrid(res, false, true);
                window.alarmCurrentPageNo++;
            }
        });
    }

    /**
     * 初始化获取60条数据
     */
    function loadInitPage() {
        dhxGridT.grid.clearAll();
        var filterData = getFilter();
        priorityNum = {};
        $.ajax({
            url:"accAlarmMonitor.do?getFirstPage",
            data:filterData,
            method: "post",
            success:function (res) {
                if(loadDataToGrid(res)){
                    window.alarmCurrentPageNo = 3;
                    window.alarmCurrentFlag = res.data.dxGrid.rows[0].eventNum;
                }
                loadNewAlarm();
            }
        });
    }

    var filterNames = {
        Critical: "<@i18n 'acc_musterPointReport_danger'/>",
        Higher: "<@i18n 'auth_security_strengthLevel2'/>",
        Medium: "<@i18n 'auth_security_strengthLevel1'/>",
        Low: "<@i18n 'auth_security_strengthLevel0'/>"
    };

    function getFilter() {
        var filterData = {};
        for(let k in window.alarmFilter){
            for(let item in window.alarmFilter[k]){
                var filterKey = item;
                for (let key in filterNames) {
                    if(filterNames[key] == item){
                        filterKey = key;
                    }
                }
                filterData[filterKey] = window.alarmFilter[k][item];
            }
        }
        return filterData;
    }

	//var maxEventNum;
	var priorityNum = {};

    function loadDataToGrid(resData, toFirst, init){
        var statusColor = ["red", "#FBC02D"];
        var data = resData.data;
        var dhxGrid = ZKUI.Grid.get("${gridName}");

        if(data != null && data.dxGrid !=null && dhxGrid && dhxGrid.grid){
            data = data.dxGrid;
            dhxGrid = dhxGrid.grid;
            var rows = data.rows;
            $.jBox.closeMessager();

            if(dhxGrid.getRowsNum() == 0) {
            	init = true;
            } else if (dhxGrid.getRowsNum()+"" == $("#totalShow").text()) {
            	init = false;
            }

            for(var index in rows)
            {
                var id = rows[index].id;
                var rowIndex;
                rowIndex = dhxGrid.getRowsNum();
				rowIndex = rowIndex==null?0:rowIndex;
                if(!dhxGrid._added_rows.includes(id) || init) {
					rowIndex = getRowIndex(rows[index], rowIndex);
					var eventName = rows[index].data[4];
					rows[index].data[4] = I18n.getValue(eventName);
					rows[index].data[7] = I18n.getValue(rows[index].data[7]);
					rows[index].data[8] = I18n.getValue(rows[index].data[8]);
					var personPin = rows[index].data[10];
					var personName = rows[index].data[5];
					<#if hasPermission('acc:pin:encryptProp','true')>
						personPin = convertToEncrypt(personPin,"${pinEncryptMode!S2}");
					</#if>
					<#if hasPermission('acc:name:encryptProp','true')>
						personName = convertToEncrypt(personName,"${nameEncryptMode!S1}");
					</#if>
					rows[index].data[5] = personPin;
					if (personName != "") {
						rows[index].data[5] = personPin + "(" + personName + ")";
					}
					dhxGrid.addRow(id, rows[index].data, rowIndex);
					var priorityStyle = rows[index].priorityStyle;
					var statusStyle = rows[index].statusStyle;
					var priorityBaseStyle = "margin: 10px; border: none; border-radius:8px; text-align:center; width: 80px; display:block;";
					if(priorityStyle + "" == "" || priorityStyle + "" == "null") {
						priorityStyle = "4";
					}
					if(statusStyle + "" == "" || statusStyle + "" == "null") {
						statusStyle = "1";
					}
					if (priorityStyle == "4") {
						priorityBaseStyle = priorityBaseStyle + "color:black;";
					}else {
						priorityBaseStyle = priorityBaseStyle + "color:white;";
					}
					priorityBaseStyle = priorityBaseStyle + "background-color:" + colors[priorityStyle] + ";";
					dhxGrid.setCellTextStyle(dhxGridT.grid.getRowId(rowIndex), 7, priorityBaseStyle);
					dhxGrid.setCellTextStyle(dhxGridT.grid.getRowId(rowIndex), 8, "color:" + statusColor[statusStyle]);
					for(var key in rows[index].userdata)
					{
						dhxGrid.setUserData(id, key, rows[index].userdata[key]);
					}
				}
			}
			dhxGrid.setSizes(false);
			if(!toFirst){
				setTimeout(function () {
					$(".objbox").scrollTop(scrollT);
					$(".objbox").scroll(scrollHandler);
				},100);
			}
			alarmSoud();
			reload = false;
			return true;
        }
        alarmSoud();
    }
	function getRowIndex(rowData, rowsNum) {
		var priorityStyle = rowData.priorityStyle;
		var rowIndex = null;
		var lastDangerousNum = priorityNum.lastDangerousNum;
		var lastHigherNum = priorityNum.lastHigherNum
		var lastMediumNum = priorityNum.lastMediumNum;
		var lastLowNum = priorityNum.lastLowNum;
		var lastUnknownNum = priorityNum.lastUnknownNum;
		switch (priorityStyle) {
			case "3" :
				priorityNum["lastDangerousNum"] = lastDangerousNum == undefined ? 0 : lastDangerousNum + 1;
				priorityNum["lastHigherNum"] = lastHigherNum == undefined ? undefined : lastHigherNum + 1;
				priorityNum["lastMediumNum"] = lastMediumNum == undefined ? undefined : lastMediumNum + 1;
				priorityNum["lastLowNum"] = lastLowNum == undefined ? undefined : lastLowNum + 1;
				priorityNum["lastUnknownNum"] = lastUnknownNum == undefined ? undefined : lastUnknownNum + 1;
				rowIndex = 0 ;
				break;
			case "2" :
				rowIndex = lastDangerousNum == undefined ? 0 : lastDangerousNum + 1;
				priorityNum["lastHigherNum"] = lastHigherNum == undefined ? rowIndex : lastHigherNum + 1;
				priorityNum["lastMediumNum"] = lastMediumNum == undefined ? undefined : lastMediumNum + 1;
				priorityNum["lastLowNum"] = lastLowNum == undefined ? undefined : lastLowNum + 1;
				priorityNum["lastUnknownNum"] = lastUnknownNum == undefined ? undefined : lastUnknownNum + 1;
				break;
			case "1" :
				if (lastDangerousNum == undefined && lastHigherNum == undefined) {
					rowIndex = 0;
				} else if(lastHigherNum != undefined) {
					rowIndex = lastHigherNum + 1;
				} else if (lastDangerousNum != undefined) {
					rowIndex = lastDangerousNum + 1;
				}
				priorityNum["lastMediumNum"] = lastMediumNum == undefined ? rowIndex : lastMediumNum + 1;
				priorityNum["lastLowNum"] = lastLowNum == undefined ? undefined : lastLowNum + 1;
				priorityNum["lastUnknownNum"] = lastUnknownNum == undefined ? undefined : lastUnknownNum + 1;
				break;
			case "0" :
				if (lastDangerousNum == undefined && lastHigherNum == undefined && lastMediumNum == undefined) {
					rowIndex = 0;
				} else if(lastMediumNum != undefined) {
					rowIndex = lastMediumNum + 1;
				} else if (lastHigherNum != undefined) {
					rowIndex = lastHigherNum + 1;
				} else if (lastDangerousNum != undefined) {
					rowIndex = lastDangerousNum + 1;
				}
				priorityNum["lastLowNum"] = lastLowNum == undefined ? rowIndex : lastLowNum + 1;
				priorityNum["lastUnknownNum"] = lastUnknownNum == undefined ? undefined : lastUnknownNum + 1;
				break;
			default:
				rowIndex = rowsNum;
				priorityNum["lastUnknownNum"] = lastUnknownNum == undefined ? rowIndex : lastUnknownNum + 1;
				break;
		}
		return rowIndex;
	}

	function changeOtherPriorityNum(operation, priorityStyle) {
		var lastDangerousNum = priorityNum.lastDangerousNum;
		var lastHigherNum = priorityNum.lastHigherNum;
		var lastMediumNum = priorityNum.lastMediumNum;
		var lastLowNum = priorityNum.lastLowNum;
		var lastUnknownNum = priorityNum.lastUnknownNum;
		var exitHigherRow = lastHigherNum - 1 == lastDangerousNum;
		var exitMediumRow = lastMediumNum - 1 == lastHigherNum;
		var exitLowRow = lastLowNum - 1 == lastMediumNum;
		var exitUnknownRow = lastUnknownNum - 1 == lastLowNum;
		if(operation == "del"){
			switch (priorityStyle) {
				case "3" :
					priorityNum["lastDangerousNum"] = lastDangerousNum == 0 ? undefined : lastDangerousNum - 1;
					priorityNum["lastHigherNum"] = lastHigherNum == undefined ? undefined : lastHigherNum - 1;
					priorityNum["lastMediumNum"] = lastMediumNum == undefined ? undefined : lastMediumNum - 1;
					priorityNum["lastLowNum"] = lastLowNum == undefined ? undefined : lastLowNum - 1;
					priorityNum["lastUnknownNum"] = lastUnknownNum == undefined ? undefined : lastUnknownNum - 1;
					break;
				case "2" :
					if(lastHigherNum == 0 || exitHigherRow) {
						priorityNum["lastHigherNum"] = undefined;
					} else {
						priorityNum["lastHigherNum"] = lastHigherNum - 1;
					}
					priorityNum["lastMediumNum"] = lastMediumNum == undefined ? undefined : lastMediumNum - 1;
					priorityNum["lastLowNum"] = lastLowNum == undefined ? undefined : lastLowNum - 1;
					priorityNum["lastUnknownNum"] = lastUnknownNum == undefined ? undefined : lastUnknownNum - 1;
					break;
				case "1" :
					if(lastMediumNum == 0 || exitMediumRow) {
						priorityNum["lastMediumNum"] = undefined;
					} else {
						priorityNum["lastMediumNum"] = lastMediumNum - 1;
					}
					priorityNum["lastLowNum"] = lastLowNum == undefined ? undefined : lastLowNum - 1;
					priorityNum["lastUnknownNum"] = lastUnknownNum == undefined ? undefined : lastUnknownNum - 1;
					break;
				case "0" :
					if(lastLowNum == 0 || exitLowRow) {
						priorityNum["lastLowNum"] = undefined;
					} else {
						priorityNum["lastLowNum"] = lastMediumNum - 1;
					}
					priorityNum["lastUnknownNum"] = lastUnknownNum == undefined ? undefined : lastUnknownNum - 1;
					break;
				default:
					if(lastUnknownNum == 0 || exitUnknownRow) {
						priorityNum["lastUnknownNum"] = undefined;
					} else {
						priorityNum["lastUnknownNum"] = lastLowNum - 1;
					}
					break;
			}
		}
	}
    function commonOpenOperateOne(id, bar, opt) {
        if (bar) {
        	var width = opt.width;
        	var height = opt.height;
            var gridName = bar.gridName || "gridbox";
            var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
            if (!opt.noSelect && ids == "") {
                messageBox({ messageType : "alert", text : "common_prompt_selectObj" });
            }else if(ids.split(",").length > 1)
            {
                messageBox({
                    messageType : "alert",
                    text : "<@i18n 'common_prompt_onlySelectOneObject'/>"
                });
            }
            else {
                id = toURL(id, "id=" + ids);
                var opts = $.extend({
                    path : fillParamsFromGrid(gridName, ids, id),
                    gridName : gridName,
                    width:width,
                    height:height
                }, JSON.parse(JSON.stringify(opt)));
                DhxCommon.createWindow(opts);
            }
        }
    }

    function deleteRowById(id) {
        if(id!=null){
            var dhxGrid = ZKUI.Grid.get("${gridName}");
            var rowData = dhxGrid.grid.getRowById(id);
            if(rowData) {
				var backgroundColor = rowData.cells[7].style.backgroundColor;
				backgroundColor = setRgbTo16(backgroundColor);
				var priority = colors.indexOf(backgroundColor);
				changeOtherPriorityNum("del", priority+"");
            }
            dhxGrid.grid.deleteRow(id);
        }
    }

    function aknowledgeRowById(id) {
        if(id!=null){
            var dhxGrid = ZKUI.Grid.get("${gridName}");
            var row = dhxGrid.grid.getRowById(id);
            if(row){
                dhxGrid.grid.getRowById(id).cells[8].innerHTML="<@i18n'acc_alarm_inProcess'/>";
                dhxGrid.grid.setCellTextStyle(id, 8, "color:#FBC02D");
            }
        }
    }

//    websocket

    var client;
    var clientId = Math.round(Math.random() * 10000)+"${uuid!}";

    function initDevEventClient() {
        client = Web.getSocket({
            id:"alarmClient",
            url : "accAlarmMonitor/changeEventDataStatus",
            param:JSON.stringify({"clientId" : clientId}),
            onMessage : function(resp) {
            	var data = JSON.parse(resp.body);
            	if (data.data.alarm != undefined) {
            		var createRes = {};
					createRes.data = data.data.alarm;
					loadDataToGrid(createRes, true);
            	} else {
					var delId = data.data.id;
					var status = data.data.status;
					if(delId != null && status == 2)
					{
						deleteRowById(delId);
						$.jBox.closeMessager();
						//dhxGrid.setSizes(false);
						alarmSoud();
					}else if(delId != null && status == 1){
						aknowledgeRowById(delId);
						$.jBox.closeMessager();
					}
            	}
            }
        })
    }

// 声音
    var alarmSoundFlag = false;

    function alarmSoud() {
        var dhxGrid = ZKUI.Grid.get("${gridName}");
        dhxGrid = dhxGrid.grid;
        if(dhxGrid.getRowsNum() > 0 && !getRadioPause()) {
            playAlarmSound();
        }else {
            alarmSoundFlag = false;
            closeAlarmSound();
        }
    }

    function playAlarmSound() {
        if(!dhx4.isFF && !dhx4.isChrome)// IE下
        {
            $("body bgsound").remove();
            $("body").append('<bgsound src="/public/media/sound/alarm.mid" loop="-1"/>');
        }
        else
        {
            if (typeof(Worker) != "undefined")//"支持HTML5"
            {
                if($(".jp-jplayer").size() <= 0)
                {
                    $("body").prepend("<div class='jp-jplayer'></div>");
                    $(".jp-jplayer").jPlayer({
                        ready : function(event) {
                            $(this).jPlayer("setMedia", {
                                wav : "/public/media/sound/alarm.wav"
                            }).jPlayer("play");
                        },
                        ended: function (event) {
                            $(this).jPlayer("play");
                        },
                        swfPath : "../../public/controls/jquery",
                        supplied : "wav",
                        wmode : "window",
                        smoothPlayBar : true,
                        keyEnabled : true,
                        remainingDuration : true,
                        toggleDuration : true
                    });
                }
                else
                {
                    $(".jp-jplayer").jPlayer("play");
                }
            }
            else
            {
                $("body embed").remove();
                $("body").append('<embed src="/public/media/sound/alarm.mid" type="application/x-mplayer2" hidden="true" autostart="true" playcount="true" loop="true" height="0" width="0"/>');
            }
        }
    }

    window.alarm = {};
    window.alarm.closeAlarmSound = function () {
        if(!dhx4.isFF && !dhx4.isChrome)// IE下
        {
            $("body bgsound").remove();
        }
        else
        {
            if (typeof(Worker) != "undefined")//"支持HTML5"
            {
                $(".jp-jplayer").jPlayer("pause");
            }
            else
            {
                $("body embed").remove();
            }
        }
    }

    var closeAlarmSound = window.alarm.closeAlarmSound;

    function changeCheckStatus(ob, checked) {
        var img = $(ob).children("div").children("img").attr("src");
        img = img.replace("_selected.png",".png");
        if(checked){
            $(ob).attr("class","");
            $(ob).addClass("accAlarmCheckedDiv");
            img = img.replace(".png", "_selected.png");
        }else {
            $(ob).attr("class","");
            $(ob).addClass("uncheckDiv");
            $(ob).addClass("button-borderColor");
        }
        $(ob).children("div").children("img").attr("src", img);
    }

    function clickPauseButton(ob) {
        var changeTo = !$(ob).hasClass("accAlarmCheckedDiv");
        createCookie("accAlarmEventSuspend", changeTo);
        changeCheckStatus(ob, changeTo)
        disableEventMonitor(ob);
    }

    function clickSoundPauseButton(ob) {
        var status = !$(ob).hasClass("accAlarmCheckedDiv");
        createCookie("accAlarmEventAudio", status);
        showAccAlarmTip(ob,status);
        changeCheckStatus(ob, status);
        alarmSoud();
    }
	function showAccAlarmTip(ob, checked) {
		if(checked){
            openMessage(msgType.warning, "<@i18n 'acc_rtMonitor_alarmSoundClose'/>");
        }else {
            openMessage(msgType.success, "<@i18n 'acc_rtMonitor_alarmSoundOpen'/>");
        }
	}
    function disableEventMonitor(ob) {
        var changeTo = $(ob).hasClass("accAlarmCheckedDiv");
        if(changeTo)//选中时则暂停
        {
            window.clearInterval(window.timeInterval);
            window.clearInterval(window.alarmInterval);
            openMessage(msgType.warning, "<@i18n 'common_devMonitor_monitorSuspended'/>");
        }
        else
        {
            initTime();
            if(window.alarmInterval!=null){
                window.clearInterval(window.alarmInterval);
            }
            window.alarmInterval = window.setInterval(function () {
                loadNewAlarm();
            }, 5000);
            openMessage(msgType.success, "<@i18n 'common_devMonitor_monitorOpened'/>");
        }
    }

    function getRadioPause() {
        return $("#accAlarmSoundPause${uuid!}").hasClass("accAlarmCheckedDiv");
    }

    function initOnPageLoad(){
        Web.attachEvent("onAfterMenuClick", function(menuId){
            if(menuId!="accAlarmMonitor.do"){
                if(window.timeInterval!=null) {
                    clearInterval(window.timeInterval);
                }
                if(window.alarmInterval!=null){
                    clearInterval(window.alarmInterval);
                }
                if(window.alarm.closeAlarmSound!=null){
                    window.alarm.closeAlarmSound();
                }
            }
        });
    }

    $(window).resize(function() {
        showOrHideTime();
    });

    //855

    function firstShowOrHideTime() {
        var winHeight = $(window).height();
        if(winHeight>=865){
            $("#timeContent").show();
        }else {
            $("#timeContent").hide();
        }
    }

    function showOrHideTime() {
        var winHeight = $(window).height();
        if(winHeight>=855){
            $("#timeContent").slideDown();
        }else {
            $("#timeContent").slideUp();
        }
    }

    function disableStatus(ob) {
        if($(ob).hasClass("disableTodayInfo")){
            $(ob).removeClass("disableTodayInfo");
            $(ob).children("div").removeClass("disableTodayInfo");
        }else {
            $(ob).addClass("disableTodayInfo");
            $(ob).children("div").addClass("disableTodayInfo");
        }
        window.alarmFilter.status = {
            unhandled: !$("div[name='unhandled']").hasClass("disableTodayInfo"),
            inProgess: !$("div[name='inProgess']").hasClass("disableTodayInfo"),
        }
        loadInitPage();
    }

    function getRowById(id) {
        return dhxGridT.grid.getRowById(id);
    }

	function initAccAlarmButtonIcon() {
		var audioCheck = getCookie("accAlarmEventAudio") == "false" ? false : true;
		changeCheckStatus($("#accAlarmSoundPause${uuid!}"), audioCheck);
		var suspendCheck = getCookie("accAlarmEventSuspend") == "false" ? false : true;
		changeCheckStatus($("#accAlarmSuspendPause${uuid!}"), suspendCheck);
		if(suspendCheck)//选中时则暂停
        {
            window.clearInterval(window.timeInterval);
            window.clearInterval(window.alarmInterval);
        } else {
			window.alarmInterval = window.setInterval(function () {
            	loadNewAlarm();
        	}, 5000);
		}
	}

	function setRgbTo16(str){
		let reg = /^(rgb|RGB)/;
		if(!reg.test(str)){return;}
		// 将str中的数字提取出来放进数组中
		var arr = str.slice(4, str.length-1).split(",");
		let c = '#';
		for(var i = 0; i < arr.length; i++){
			// Number() 函数把对象的值转换为数字
			// toString(16) 将数字转换为十六进制的字符表示
			var t = Number(arr[i]).toString(16);
			//如果小于16，需要补0操作,否则只有5位数
			if(Number(arr[i]) < 16){
				t = '0' + t;
			}
			c += t;
		}
		return c.toUpperCase();
	}

</script>