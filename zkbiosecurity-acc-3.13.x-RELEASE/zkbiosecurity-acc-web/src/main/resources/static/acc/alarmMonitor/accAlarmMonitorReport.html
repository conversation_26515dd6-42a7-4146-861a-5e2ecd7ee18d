<#assign gridName="accAlarmReportGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}" style="height:100%;width:100%">
    <@ZKUI.Searchbar>
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@i18n 'common_time_from'/>&nbsp;&nbsp;
                    <@ZKUI.Input type="datetime" endId="endTimeId${uuid}" id="startTime${uuid}" name="startTime" title="common_time_from" max="today" today="-7" offsetField="D" todayRange="start" hideLabel="true" noOverToday="true" readonly="false"/>
                    &nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
                    <@ZKUI.Input type="datetime" id="endTimeId${uuid}" name="endTime" title="common_to" max="today" today="true" todayRange="end" noOverToday="true" hideLabel="true" readonly="false"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Combo empty="true" name="priority" width="148" title="acc_alarm_priority">
                        <option value="0"><@i18n 'auth_security_strengthLevel0'/></option>
                        <option value="1"><@i18n 'auth_security_strengthLevel1'/></option>
                        <option value="2"><@i18n 'auth_security_strengthLevel2'/></option>
                        <option value="3"><@i18n 'acc_musterPointReport_danger'/></option>
                    </@ZKUI.Combo>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="devAlias" maxlength="30" title="common_dev_name" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
        <@ZKUI.SearchBelow>
        <tr>
            <td valign="middle">
                <@ZKUI.Combo empty="true" name="status" width="148" title="common_inOutStatus">
                    <option value="0"><@i18n 'acc_alarm_unhandled'/></option>
                    <option value="1"><@i18n 'acc_alarm_inProcess'/></option>
                    <option value="2"><@i18n 'acc_alarm_acknowledged'/></option>
                </@ZKUI.Combo>
            </td>
            <td valign="middle">
                <@ZKUI.ComboGrid id="accEventId${uuid}" title="common_eventDescription" width="137" queryField="name" name="eventName"
                grid_vo="com.zkteco.zkbiosecurity.acc.vo.AccDeviceEventSelectItem"
                grid_query="accDeviceEvent.do?listSelect"/>
            </td>
            <td>
                <@ZKUI.Input name="devSn" maxlength="30" title="common_dev_sn" type="text"/>
            </td>
        </tr>
        </@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
        <@ZKUI.ToolItem id="refresh" type="refresh" permission="acc:alarmReport:refresh"></@ZKUI.ToolItem>
        <@ZKUI.ToolItem id="accAlarmMonitorReport.do?editAcknowledged" width="600" height="400" title="acc_rtMonitor_ackAlarm" action="commonOpenOperate" selectNumber="1" img="acc_alarmMonitor.png" text="acc_rtMonitor_ackAlarm" permission="acc:alarmMonitor:ackAlarm"/>
        <@ZKUI.ToolItem id="accAlarmMonitorReport.do?export" type="export" text="common_op_export" img="comm_export.png" permission="acc:alarmReport:export"></@ZKUI.ToolItem>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid pageList="true" limitCount="200000" vo="com.zkteco.zkbiosecurity.acc.vo.AccAlarmMonitorItem" query="accAlarmMonitorReport.do?list"/>
</@ZKUI.GridBox>
<script>
    function reloadAlarmReport() {
        ZKUI.Grid.reloadGrid("accAlarmReportGrid${uuid!}");
    }

    function getRowById(id) {
        var dhxGridT = ZKUI.Grid.get("${gridName}");
        return dhxGridT.grid.getRowById(id);
    }
</script>