<#assign gridName="accAlarmHistoryGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}" style="height:100%;width:100%">
    <@ZKUI.Searchbar>
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@i18n 'common_time_from'/>&nbsp;&nbsp;
                    <@ZKUI.Input type="datetime" endId="endTimeId${uuid}" id="startTime${uuid}" name="startTime" title="common_time_from" max="today" today="-7" offsetField="D" todayRange="start" hideLabel="true" noOverToday="true" readonly="false"/>
                    &nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
                    <@ZKUI.Input type="datetime" id="endTimeId${uuid}" name="endTime" title="common_to" max="today" today="true" todayRange="end" noOverToday="true" hideLabel="true" readonly="false"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="createrCode" maxlength="30" title="common_opUser" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="deviceName" maxlength="30" title="common_dev_name" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
        <@ZKUI.SearchBelow>
            <tr>
                <td valign="middle">
                    <@ZKUI.Combo empty="true" name="status" width="148" title="common_inOutStatus">
                        <option value="1"><@i18n 'acc_alarm_inProcess'/></option>
                        <option value="2"><@i18n 'acc_alarm_acknowledged'/></option>
                    </@ZKUI.Combo>
                </td>
                <td valign="middle">
                    <@ZKUI.ComboGrid id="accEventId${uuid}" title="common_eventDescription" width="137" queryField="name" name="eventName"
                    grid_vo="com.zkteco.zkbiosecurity.acc.vo.AccDeviceEventSelectItem"
                    grid_query="accDeviceEvent.do?listSelect"/>
                </td>
                <td>
                    <@ZKUI.Input name="devSn" maxlength="30" title="common_dev_sn" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
        <@ZKUI.ToolItem id="refresh" type="refresh" permission="acc:alarmHistory:refresh"></@ZKUI.ToolItem>
        <@ZKUI.ToolItem id="accAlarmMonitor.do?exportHistory" type="export" text="common_op_export" img="comm_export.png" permission="acc:alarmHistory:export"></@ZKUI.ToolItem>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid pageList="true" limitCount="200000"  vo="com.zkteco.zkbiosecurity.acc.vo.AccAlarmMonitorHistoryItem" query="accAlarmMonitor.do?historyList"/>
</@ZKUI.GridBox>

<script>
    var gridName = "${gridName}";
    function accClearData(id, bar) {
        deleteConfirm(function(result) {
            if (result) {
                openMessage(msgType.loading);
                $.ajax({
                    url : id,
                    type : "post",
                    success : function(result) {
                        dealRetResult(eval(result), function() {
                            closeMessage();
                            ZKUI.Grid.reloadGrid(gridName);
                        });
                    }
                });
            }
        }, "<@i18n 'common_prompt_sureToDelAll'/>");

    }
</script>