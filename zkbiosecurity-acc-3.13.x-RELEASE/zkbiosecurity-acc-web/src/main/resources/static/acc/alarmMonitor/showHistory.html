<#include "/public/template/editTemplate.html">
<#macro editContent>
    <div style="margin: 10px">
        <table class="tableStyle">
            <tr>
                <td><@i18n 'common_eventDescription'/></td>
                <td>
                    <input type="text" disabled="disabled" value="${item.eventName!}">
                </td>
            </tr>
            <tr>
                <td><@i18n 'common_dev_name'/></td>
                <td>
                    <input type="text" disabled="disabled" value="${item.devAlias!}">
                </td>
            </tr>
            <tr>
                <td><@i18n 'common_eventPoint'/></td>
                <td>
                    <input type="text" disabled="disabled" value="${item.eventPointName!}">
                </td>
            </tr>
        </table>
        <fieldset>
            <legend><@i18n 'acc_alarm_acknowledgement'/></legend>
            <div style="height: 290px; width: 100%; overflow-y: scroll">
                <div style="width: 100%">
                    <#list item.historyItemList as historys>
                    <div>
                        ${(historys.createTime)?string("yyyy-MM-dd HH:mm:ss")!}
                        <br/>
                        <@i18n 'common_opUser'/>：${historys.createrCode!}
                        <br/>
                        <@i18n 'common_status'/>：${historys.statusStr}
                        <hr/>
                        ${historys.acknowledgement!}
                        <hr/>
                    </div>
                    </#list>
                </div>
            </div>
        </fieldset>
    </div>
</#macro>
<#macro buttonContent>
    <button class='button-form' onclick="DhxCommon.closeWindow();"><@i18n 'common_op_close'/></button>
</#macro>