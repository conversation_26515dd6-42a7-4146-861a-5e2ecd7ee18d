<#include "/public/template/editTemplate.html">
<#macro editContent>
<!--<div id="intputAccTrigger" style="width: 100%; height: 300px; overflow: auto;"></div>-->
<input type="hidden" value="${id!}" name="accDSTime.id" id="id_model_pk">
<input type="hidden" name="devGroup" class="wZBaseCharField" id="devGroup">
<table cellpadding="3px" id="levelset_edit_table" class="tableStyle" >
    <tr>
        <th style="width: 60px;"><label><@i18n 'common_leftMenu_device'/></label></th>
        <td colspan="4">
            <div style="clear: both; width: 90%;">
                <div id="idDevGroup" class="clearTableStyle zk-content-bg-color zk-border-color" style="border: 1px solid #e3e3e3; height: 300px;background-color:#f3f5f0;"></div>
                <div class="levelHint" id="devHint" style="display: none"><img src="/images/icon_alert.gif" /><span style="position: absolute;"><@i18n 'acc_level_noDoor'/></span></div>
            </div>
        </td>
    </tr>
</table>
</#macro>
<#macro buttonContent>
<button class='button-form' id="confirmButton${uuid}"><@i18n 'common_edit_ok'/></button>
<button class='button-form' onclick="DhxCommon.closeWindow()" id="closeButton${uuid}"><@i18n 'common_edit_cancel'/></button>
</#macro>

<script type="text/javascript">
    getDevTree("idDevGroup");
    var devTree;
    var isDevTree = false;
    function getDevTree(divId)//获取设备树
    {
        $.ajax({
            url: "/accDevice.do?checkDevExist",
            type: "POST",
            dataType:"json",
            async: false,
            success:function(data) {
                if(data)
                {

                    $.ajax({
                        url: "/accDevice.do?getDevTreeJson",
                        type: "POST",
                        data: {
                            type : "all",
                            getDstime : "true"
                        },
                        dataType: "json",
                        async: false,
                        success: function (data) {
                            isDevTree = true;
                            devTree = new dhtmlXTreeObject(divId, "100%", "100%", 0);
                            devTree.setSkin(sysCfg.dhxSkin);
                            devTree.setImagePath(sysCfg.treeImgPath);
                            devTree.enableCheckBoxes(1);
                            devTree.enableThreeStateCheckboxes(true);
                            devTree.enableSmartXMLParsing(true);
                            devTree.setDataMode("json");
                            // devTree.loadJSON("/accDevice.do?getDevTreeJson.action?type=all&getDstime=true");
                            devTree.loadJSONObject(data.data);
                            devTree.attachEvent("onCheck", function(id){
                                if(devTree.getAllChecked() == "") {
                                    $("#OK").attr("disabled", true);
                                }
                                else {
                                    $("#OK").attr("disabled", false);// 启用确定按钮
                                }
                            });
                        },
                    });
                }
                else
                {
                    $("#devHint").show();
                }
                data = null;
            },
            error:function(XMLHttpRequest, textStatus, errorThrown)
            {
                messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
            }
        });
    }

    function onSubmit()
    {
        var devIds = devTree.getAllChecked();
        var progressParams =
            {
                "winTitle" : "<@i18n 'common_progress_proCmd'/>",
                "dealDataPath" : "accDSTimeAction!setDSTime.action?dSTimeId=${id!}&devIds="+devIds,
                "singleMode" : true,
                "callback" : function(){
                    reloadGrid();
                }
            };
        if(devIds != "")//勾选设备为空，弹出窗体并提醒用户选着设备 --modify by kaichun-li
        {
            DhxCommon.closeWindow();
            openProgress(progressParams);
        }
        else
        {
            messageBox({messageType: "alert", title: "${common_prompt_title}", text: "${acc_dev_selectDev}"});
        }
    }

    $("#confirmButton${uuid!}").click(function () {
        var devIds = devTree.getAllChecked();
        if(devIds != "") { //勾选设备为空，弹出窗体并提醒用户选着设备
            DhxCommon.closeWindow();
            openSingleDSTimeProgress(devIds);
        }
        else {
            messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'acc_dev_selectDev'/>"});
        }
    });

    function openSingleDSTimeProgress(devIds) {
        var opts = {
            dealPath: "/accDSTime.do?setDSTime&dSTimeId=${id!}&devIds="+devIds,//进度处理请求
            type: "single",//进度类型，取值single,public,custom
            onCallback:"callbackAccDSTimeProgress",//处理完后回调
            height:250,//弹窗高度
            useReq:true,//使用请求参数作为进度条参数
            autoClose:true,//自动关闭
            delay:5,//自动关闭时间(5秒后自动关闭)
            title:"<@i18n 'common_progress_proCmd' />" //弹窗标题
        }
        openProcess(opts);
    }
    
    function callbackAccDSTimeProgress() {
        
    }

</script>
