<#include "/public/template/editTemplate.html">
<#macro editContent>
<script type="text/javascript">
    $().ready(function() {
        // 字母和数字的验证
        $("#${formId}").validate( {
            debug : true,
            rules : {
                "name" : {
                    required : true,
                    unInputChar:true,
                    overRemote : [ "/accDSTime.do?isExistName", "${(tempAccDSTime.name)!}" ],
                },
                "timeZone" : {
                    required : true
                }
    		},
			submitHandler : function()
			{
				if(beforeSubmit() == true)
				{
				    <@submitHandler/>
				}
			}
    	});
    });
</script>
<style type="text/css">
    .search-combo-box {
        display: inline-block;
        padding-right: 5px;
    }
</style>
<form method="post" onkeydown="if(event.keyCode==13){return false;}"
	  id="${formId}" action="/accDSTime.do?save" enctype="multipart/form-data" >
	<input type="hidden" name="id" id="id_model_pk" value="${(tempAccDSTime.id)!}" />
	<input type="hidden" name="dstimeMode" id="idDstimeMode" value="${(tempAccDSTime.dstimeMode)!1}" />
	<input type="hidden" name="startTime" id="idStartTime" value="${(tempAccDSTime.startTime)!}" />
	<input type="hidden" name="endTime" id="idEndTime" value="${(tempAccDSTime.endTime)!}" />
	<table class="tableStyle">
		<tbody>
		<tr>
			<th><label><@i18n 'common_dsTime_name'/></label><span class="required">*</span></th>
			<td>
				<input type="text" name="name" id="idName" value="${(tempAccDSTime.name)!}" maxlength="30"/>
			</td>
		</tr>
        <tr>
            <th>
                <label><@i18n 'acc_dev_timeZone'/></label><span class="required">*</span>
            </th>
            <td>
                <@ZKUI.Combo name="timeZone" id="accTimeZone${uuid}" width="148" empty="true" hideLabel="true" onChange="changeTimeZone" title="acc_dev_timeZone" key="systemTimeZone" value="${(tempAccDSTime.timeZone)!}" readonly="true"/>
            </td>
        </tr>
		<tr>
			<th><label><@i18n 'common_op_startTime'/></label><span class="required">*</span></th>
			<td id="tag_id_mod2_snd_month">
				<@ZKUI.Combo id="idTxtm2sy" width="65" hideLabel="true" readonly="readonly" value="03" empty="false">
                    <option value="01"><@i18n 'common_jan'/></option>
                    <option value="02"><@i18n 'common_feb'/></option>
                    <option value="03"><@i18n 'common_mar'/></option>
                    <option value="04"><@i18n 'common_apr'/></option>
                    <option value="05"><@i18n 'common_may'/></option>
                    <option value="06"><@i18n 'common_jun'/></option>
                    <option value="07"><@i18n 'common_jul'/></option>
                    <option value="08"><@i18n 'common_aug'/></option>
                    <option value="09"><@i18n 'common_sep'/></option>
                    <option value="10"><@i18n 'common_oct'/></option>
                    <option value="11"><@i18n 'common_nov'/></option>
                    <option value="12"><@i18n 'common_dec'/></option>
                </@ZKUI.Combo>
				<@ZKUI.Combo id="idTxtm2sw" width="70" hideLabel="true" readonly="readonly" value="02" empty="false">
                    <option value="01"><@i18n 'common_dsTime_first'/></option>
                    <option value="02"><@i18n 'common_dsTime_second'/></option>
                    <option value="03"><@i18n 'common_dsTime_thirt'/></option>
                    <option value="04"><@i18n 'common_dsTime_forth'/></option>
                    <option value="05"><@i18n 'common_dsTime_fifth'/></option>
                </@ZKUI.Combo>
				<@ZKUI.Combo id="idTxtm2sd" width="90" hideLabel="true" readonly="readonly" value="00" empty="false">
                    <option value="01"><@i18n 'common_monday'/></option>
                    <option value="02"><@i18n 'common_tuesday'/></option>
                    <option value="03"><@i18n 'common_wednesday'/></option>
                    <option value="04"><@i18n 'common_thursday'/></option>
                    <option value="05"><@i18n 'common_friday'/></option>
                    <option value="06"><@i18n 'common_saturday'/></option>
                    <option value="00"><@i18n 'common_sunday'/></option>
                </@ZKUI.Combo>
				<@ZKUI.Combo id ="idTxtm2sh" width="40" hideLabel="true" readonly="readonly" value="02" empty="false">
                    <option value="00">0</option>
                    <option value="01">1</option>
                    <option value="02">2</option>
                    <option value="03">3</option>
                    <option value="04">4</option>
                    <option value="05">5</option>
                    <option value="06">6</option>
                    <option value="07">7</option>
                    <option value="08">8</option>
                    <option value="09">9</option>
                    <option value="10">10</option>
                    <option value="11">11</option>
                    <option value="12">12</option>
                    <option value="13">13</option>
                    <option value="14">14</option>
                    <option value="15">15</option>
                    <option value="16">16</option>
                    <option value="17">17</option>
                    <option value="18">18</option>
                    <option value="19">19</option>
                    <option value="20">20</option>
                    <option value="21">21</option>
                    <option value="22">22</option>
                    <option value="23">23</option>
                </@ZKUI.Combo>
				<@i18n 'common_oclock'/>
			</td>
		</tr>
		<tr >
			<th><label><@i18n 'common_op_endTime'/></label><span class="required">*</span></th>
			<td id="tag_id_mod2_end_month">
                <@ZKUI.Combo id="idTxtm2ey" width="65" hideLabel="true" readonly="readonly" value="11" empty="false">
                    <option value="01"><@i18n 'common_jan'/></option>
                    <option value="02"><@i18n 'common_feb'/></option>
                    <option value="03"><@i18n 'common_mar'/></option>
                    <option value="04"><@i18n 'common_apr'/></option>
                    <option value="05"><@i18n 'common_may'/></option>
                    <option value="06"><@i18n 'common_jun'/></option>
                    <option value="07"><@i18n 'common_jul'/></option>
                    <option value="08"><@i18n 'common_aug'/></option>
                    <option value="09"><@i18n 'common_sep'/></option>
                    <option value="10"><@i18n 'common_oct'/></option>
                    <option value="11"><@i18n 'common_nov'/></option>
                    <option value="12"><@i18n 'common_dec'/></option>
                </@ZKUI.Combo>
                <@ZKUI.Combo id="idTxtm2ew" width="70" hideLabel="true" readonly="readonly" value="01" empty="false">
                    <option value="01"><@i18n 'common_dsTime_first'/></option>
                    <option value="02"><@i18n 'common_dsTime_second'/></option>
                    <option value="03"><@i18n 'common_dsTime_thirt'/></option>
                    <option value="04"><@i18n 'common_dsTime_forth'/></option>
                    <option value="05"><@i18n 'common_dsTime_fifth'/></option>
                </@ZKUI.Combo>
                <@ZKUI.Combo id="idTxtm2ed" width="90" hideLabel="true" readonly="readonly" value="00" empty="false">
                    <option value="01"><@i18n 'common_monday'/></option>
                    <option value="02"><@i18n 'common_tuesday'/></option>
                    <option value="03"><@i18n 'common_wednesday'/></option>
                    <option value="04"><@i18n 'common_thursday'/></option>
                    <option value="05"><@i18n 'common_friday'/></option>
                    <option value="06"><@i18n 'common_saturday'/></option>
                    <option value="00"><@i18n 'common_sunday'/></option>
                </@ZKUI.Combo>
                <@ZKUI.Combo id ="idTxtm2eh" width="40" hideLabel="true" readonly="readonly" value="02" empty="false">
                    <option value="00">0</option>
                    <option value="01">1</option>
                    <option value="02">2</option>
                    <option value="03">3</option>
                    <option value="04">4</option>
                    <option value="05">5</option>
                    <option value="06">6</option>
                    <option value="07">7</option>
                    <option value="08">8</option>
                    <option value="09">9</option>
                    <option value="10">10</option>
                    <option value="11">11</option>
                    <option value="12">12</option>
                    <option value="13">13</option>
                    <option value="14">14</option>
                    <option value="15">15</option>
                    <option value="16">16</option>
                    <option value="17">17</option>
                    <option value="18">18</option>
                    <option value="19">19</option>
                    <option value="20">20</option>
                    <option value="21">21</option>
                    <option value="22">22</option>
                    <option value="23">23</option>
                </@ZKUI.Combo>
                <@i18n 'common_oclock'/>
			</td>
		</tr>
		</tbody>
	</table>
</form>
<div id="idInfo" align="center" class='zk-msg-error' style="height: 13px;margin-top: 10px;">
	<ul class="errorlist">
		<li style="width:100%;"></li>
	</ul>
</div>
<script type="text/javascript">
    initData();// 初始化，加载数据
    function initData()
    {
        var startTime = $("#idStartTime").val();
        var endTime = $("#idEndTime").val();
        if($("#id_model_pk").val() != "" && $("#idDstimeMode").val() == 1)// 模式二
        {
            //编辑时初始化开始时间
            var comb = null;
            var optionIndex = null;

            comb = ZKUI.Combo.get('idTxtm2sy').combo;
            optionIndex = comb.getIndexByValue(startTime.substring(0,2));
            comb.selectOption(optionIndex);

            comb = ZKUI.Combo.get('idTxtm2sw').combo;
            optionIndex = comb.getIndexByValue(startTime.substring(2,4));
            comb.selectOption(optionIndex);

            comb = ZKUI.Combo.get('idTxtm2sd').combo;
            optionIndex = comb.getIndexByValue(startTime.substring(4,6));
            comb.selectOption(optionIndex);

            comb = ZKUI.Combo.get('idTxtm2sh').combo;
            optionIndex = comb.getIndexByValue(startTime.substring(6,8));
            comb.selectOption(optionIndex);

            comb = ZKUI.Combo.get('idTxtm2ey').combo;
            optionIndex = comb.getIndexByValue(endTime.substring(0,2));
            comb.selectOption(optionIndex);

            comb = ZKUI.Combo.get('idTxtm2ew').combo;
            optionIndex = comb.getIndexByValue(endTime.substring(2,4));
            comb.selectOption(optionIndex);

            comb = ZKUI.Combo.get('idTxtm2ed').combo;
            optionIndex = comb.getIndexByValue(endTime.substring(4,6));
            comb.selectOption(optionIndex);

            comb = ZKUI.Combo.get('idTxtm2eh').combo;
            optionIndex = comb.getIndexByValue(endTime.substring(6,8));
            comb.selectOption(optionIndex);
        }
    }


    // 提交前验证
    function beforeSubmit()
    {
            var startTime = "";
            var endTime = "";
            var startMonth = ZKUI.Combo.get('idTxtm2sy').combo.getSelectedValue();
            var startWeek = ZKUI.Combo.get('idTxtm2sw').combo.getSelectedValue();
            var startDay  = ZKUI.Combo.get('idTxtm2sd').combo.getSelectedValue();
            var startHour = ZKUI.Combo.get('idTxtm2sh').combo.getSelectedValue();

            startTime = startMonth + startWeek +startDay + startHour;

            var endMonth = ZKUI.Combo.get('idTxtm2ey').combo.getSelectedValue();
            var endWeek = ZKUI.Combo.get('idTxtm2ew').combo.getSelectedValue();
            var endDay = ZKUI.Combo.get('idTxtm2ed').combo.getSelectedValue();
            var endHour = ZKUI.Combo.get('idTxtm2eh').combo.getSelectedValue();

            endTime = endMonth + endWeek +endDay + endHour;

            if(startTime == "" || endTime == "")
            {
                $("#idInfo").show();
                $("#idInfo li").html("<@i18n 'common_dsTime_timeValid2'/>");
                return false;
            }
            else if(startTime == endTime)
            {
                $("#idInfo").show();
                $("#idInfo li").html("<@i18n 'common_dsTime_timeValid1'/>");
                return false;
            }
            $("#idInfo").hide();
            $("#idStartTime").val(startTime);
            $("#idEndTime").val(endTime);
        return true;
    }
</script>
</#macro>
