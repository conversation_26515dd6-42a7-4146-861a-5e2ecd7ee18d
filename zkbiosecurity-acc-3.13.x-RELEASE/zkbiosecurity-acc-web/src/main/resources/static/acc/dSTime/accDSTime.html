<#assign gridName="accDSTimeGrid${uuid!}">
<script>
	function convertDsTime(v) {
        var Month = {
            "code":{"01":"<@i18n 'common_jan'/>","02":"<@i18n 'common_feb'/>","03":"<@i18n 'common_mar'/>","04":"<@i18n 'common_apr'/>","05":"<@i18n 'common_may'/>","06":"<@i18n 'common_jun'/>",
                "07":"<@i18n 'common_jul'/>","08":"<@i18n 'common_aug'/>","09":"<@i18n 'common_sep'/>","10":"<@i18n 'common_oct'/>","11":"<@i18n 'common_nov'/>","12":"<@i18n 'common_dec'/>"},
            "getCode":function(code){
                return this.code[code];
            }
        };
        var Order = {
            "code":{"01":"<@i18n 'common_dsTime_first'/>","02":"<@i18n 'common_dsTime_second'/>","03":"<@i18n 'common_dsTime_thirt'/>","04":"<@i18n 'common_dsTime_forth'/>","05":"<@i18n 'common_dsTime_fifth'/>"},
            "getCode":function(code){
                return this.code[code];
            }
        };
        var Week = {
            "code":{"01":"<@i18n 'common_monday'/>","02":"<@i18n 'common_tuesday'/>","03":"<@i18n 'common_wednesday'/>","04":"<@i18n 'common_thursday'/>","05":"<@i18n 'common_friday'/>",
                "06":"<@i18n 'common_saturday'/>","00":"<@i18n 'common_sunday'/>"},
            "getCode":function(code){
                return this.code[code];
            }
        };
        return Month.getCode(v.substring(0,2))+"&nbsp"+Order.getCode(v.substring(2,4))+"&nbsp"+Week.getCode(v.substring(4,6))+"&nbsp"+parseInt(v.substring(6,8),10)+"<@i18n 'common_oclock'/>";
	}

    function accDSTimeOperate(id, bar, opt) {
        if(bar) {
            var gridName = bar.gridName;
            var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
            if(ids == "") {
                messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});
            }
            else if (ids.split(",").length > 1) {
                messageBox({messageType:"alert",text:"<@i18n 'common_prompt_onlySelectOneObject'/>"});
            }
            else {
                var opts = $.extend({path:id + "&id=" + ids, gridName:gridName}, JSON.parse(JSON.stringify(opt)));
                DhxCommon.createWindow(opts);
            }
        }
    }

    function convertTimeZone(v) {
    	if(v.endsWith("00")) {
			return "UTC" + v.substr(0, 3);
    	} else {
    		return "UTC" + v.substr(0, 3) + ":" + v.slice(3);
    	}
    }
</script>
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem type="refresh" permission="acc:dSTime:refresh" />
    	<@ZKUI.ToolItem type="add" id="accDSTime.do?edit" width="570" height="230" permission="acc:dSTime:add"/>
    	<@ZKUI.ToolItem id="accDSTime.do?del&name=(name)" text="common_op_del" img="comm_del.png" action="commonDel" permission="acc:dSTime:del"/>
    	<#--<@ZKUI.ToolItem id="accDSTime.do?getById" action="accDSTimeOperate" text="common_dsTime_setting" title="common_dsTime_setting" img="acc_daylight_time.png" width="480" height="440" permission="acc:dSTime:setDSTime"/>-->
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccDSTimeItem" query="accDSTime.do?list"/>
</@ZKUI.GridBox>