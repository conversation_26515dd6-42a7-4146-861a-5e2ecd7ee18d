<#include "/public/template/editTemplate.html">
<#macro editContent>
<style>
    .dsStart, .dsEnd {
        color: grey;
    }
    .dsTitle {
        font-weight: bold;
        font-size: 11px;
    }
    .dsTimeDiv {
        display: flex;
        border-bottom: solid 1px grey;
        padding-bottom: 10px;
        margin-bottom: 5px;
    }
    .dsTimeRadio {
        margin: 1px 5px;
    }
    #dsTimeSelect {
        margin-top: 20px;
        height: 160px;
        overflow-y: auto;
    }
</style>
<form  id="${formId}">
    <div>
        <label><@i18n 'base_i18n_pleaseSelect'/></label>
        <div id="dsTimeSelect"></div>
    </div>
</form>

<script>
    $().ready(function() {
        $("#${formId}").validate( {
            debug : true,
            rules : {
                "accDSTimeId" : {required : true}
            },
			submitHandler : function()
			{
			    var dsId = $('input:radio[name="accDSTimeId"]:checked').val();
			    var dsName = $("label[for='dsRadio"+dsId+"']").find(".dsTitle").text();
			    selectDsTimeCallBack(dsId, dsName);
			    DhxCommon.closeWindow();
			}
    	});
    });
    $(function() {
        initDst("${timeZone!}", "${dsTime!}");
    })

    function initDst(timezone, dst) {
        getDstByTimeZone(timezone, function(res) {
            if(res.ret == 'ok') {
                var data = res.data;
                var noneSelected = true;
                for(var i = 0; i < data.length; i++) {
                    var selected = false;
                    if(data[i].id == dst) {
                        selected = true;
                        noneSelected = false;
                    }
                    addDsTime(data[i].name, data[i].startTime, data[i].endTime, data[i].id, selected);
                }
                addDsTime(I18n.getValue("acc_dsTimeUtc_none"), null, null, null, noneSelected);
            }
        })
    }

    function getDstByTimeZone(val, doFun) {
        $.ajax({
                url: "accDSTime.do?getDSTimeListByTimeZone",
                data: {
                    timeZone: val
                },
                success: function(res) {
                    doFun(res);
                }
            });
    }

    function addDsTime(title, start, end, id, selected) {
        var radioId = "dsRadio" + id;
        var dsTimeDiv = $('<label for="' + radioId + '"></label>');
        var dsTimeTitle = $("<div class='dsTitle'></div>");
        dsTimeDiv.append(dsTimeTitle);
        dsTimeTitle.text(title);
        var optVal = "";
        var selectOpt = $("<div class='dsTimeDiv' style='border-bottom: none;'></div>");
        if(start != null && end != null) {
            var startText = I18n.getValue("common_op_startTime");
            var endText = I18n.getValue("common_op_endTime");
            var dsTimeStart = $("<div class='dsStart'></div>");
            dsTimeDiv.append(dsTimeStart);
            dsTimeStart.html(startText + ": " + convertDsTime(start));
            var dsTimeEnd = $("<div class='dsEnd'></div>");
            dsTimeDiv.append(dsTimeEnd);
            dsTimeEnd.html(endText + ": " + convertDsTime(end));
            optVal = id;
            selectOpt = $("<div class='dsTimeDiv' ></div>");
        }

        var input = $('<input type="radio" class="dsTimeRadio" value = "' + optVal + '" name="accDSTimeId" id="' + radioId + '">');
        selectOpt.append(input);
        selectOpt.append(dsTimeDiv);
        if(selected) {
            input.attr('checked','true');
        }
        $("#dsTimeSelect").append(selectOpt);
    }

    function clearDsTime() {
        $("#dsTimeSelect").html("");
    }

    function convertDsTime(v) {
        var Month = {
            "code":{"01":I18n.getValue('common_jan'),"02":I18n.getValue('common_feb'),"03":I18n.getValue('common_mar'),"04":I18n.getValue('common_apr'),"05":I18n.getValue('common_may'),"06":I18n.getValue('common_jun'),
                "07":I18n.getValue('common_jul'),"08":I18n.getValue('common_aug'),"09":I18n.getValue('common_sep'),"10":I18n.getValue('common_oct'),"11":I18n.getValue('common_nov'),"12":I18n.getValue('common_dec')},
            "getCode":function(code){
                return this.code[code];
            }
        };
        var Order = {
            "code":{"01":I18n.getValue('common_dsTime_first'),"02":I18n.getValue('common_dsTime_second'),"03":I18n.getValue('common_dsTime_thirt'),"04":I18n.getValue('common_dsTime_forth'),"05":I18n.getValue('common_dsTime_fifth'), "11":I18n.getValue('acc_dsTime_last')},
            "getCode":function(code){
                return this.code[code];
            }
        };
        var Week = {
            "code":{"01":I18n.getValue('common_monday'),"02":I18n.getValue('common_tuesday'),"03":I18n.getValue('common_wednesday'),"04":I18n.getValue('common_thursday'),"05":I18n.getValue('common_friday'),
                "06":I18n.getValue('common_saturday'),"00":I18n.getValue('common_sunday')},
            "getCode":function(code){
                return this.code[code];
            }
        };
        if($("#supportMinute").length > 0 && $("#supportMinute").val()) {
            if(v.length < 10) {
                v = v + "00";
            }
            return Month.getCode(v.substring(0,2))+"&nbsp"+Order.getCode(v.substring(2,4))+"&nbsp"+Week.getCode(v.substring(4,6))+"&nbsp"+v.substring(6,8)+":"+v.substring(8,10);
        } else {
            return Month.getCode(v.substring(0,2))+"&nbsp"+Order.getCode(v.substring(2,4))+"&nbsp"+Week.getCode(v.substring(4,6))+"&nbsp"+parseInt(v.substring(6,8),10)+I18n.getValue('common_oclock');
        }
    }

</script>
</#macro>