<div style="margin-top: 3px; margin-left: 12px; text-align: left;" class="trEditHidden"><span id="warningContent${uuid}" class="warningColor" style="font-size: 13px; letter-spacing:1px"></span></div>
<@ZKUI.SelectContent showColumns="id,name,doorName,deviceAlias" setMode="true,true,false,true" type="radio" onSure="bindAiDevice" style="position:absolute;top:30px;bottom:0px;left:0px;right:0px;height:auto;" textField="name" noAutoDisable="true" value="${value!}" vo="com.zkteco.zkbiosecurity.acc.vo.AccSelectReaderRadioItem" query="accReader.do?selectReaderBindAiDeviceList&authAreaIdIn=${areaId!}">
    <@ZKUI.Searchbar>
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="name"  maxlength="30" title="acc_readerDefine_readerName" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="doorName"  maxlength="30" title="acc_door_name" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="deviceAlias"  maxlength="30" title="common_ownedDev" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
</@ZKUI.SelectContent>

<script type="text/javascript">
    $("#warningContent${uuid}").html("<@i18n 'acc_reader_note'/>".format("${areaName!}"));
    function bindAiDevice(value, text, event) {
        var aiDeviceId = "${aiDeviceId!}";
            $.ajax({
                url:"aiDevice.do?setAccReader",
                type:"post",
                data: {
                    "aiDeviceId" : aiDeviceId,
                    "accReaderId" : value,
                    "devAlias" : "${aiDevAlias!}",
                    "readerName" : text
                },
                success:function(result)
                {
                    if(result.ret == sysCfg.success) {
                        DhxCommon.closeWindow();
                        messageBox({messageType:"confirm", text: "<@i18n 'ai_device_isSyncDataToDev'/>",
                            callback: function(result){
                                if(result) {
                                    openMessage(msgType.loading);
                                    $.ajax({
                                        url:"aiDevice.do?syncAllData",
                                        type:"post",
                                        data: {
                                            "ids" : aiDeviceId,
                                            "devAlias" : "${aiDevAlias!}"
                                        },
                                        success:function(result){
                                            closeMessage();
                                            dealRetResult(eval(result), function() {
                                                ZKUI.Grid.reloadGrid("${gridName!}");
                                            });
                                        }
                                    });
                                } else {
                                    openMessage(msgType.success, result[sysCfg.msg], result[sysCfg.ptimeout]);
                                    ZKUI.Grid.reloadGrid("${gridName!}");
                                }
                            }});
                    };
                }
            });
    }
</script>