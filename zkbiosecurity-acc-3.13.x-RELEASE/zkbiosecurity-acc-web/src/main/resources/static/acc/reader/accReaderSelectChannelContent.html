<@ZKUI.SelectContent showColumns="checkbox,name,alias,sn" onSure="bindChannel" textField="name" noAutoDisable="true" value="${value!}" vo="com.zkteco.zkbiosecurity.acc.vo.AccChannelSelectItem" query="/accChannel.do?list&deviceSn=${deviceSn!}">
<!-- 搜索栏 -->
<@ZKUI.Searchbar>
<@ZKUI.SearchTop>
<tr>
    <td valign="middle">
        <@ZKUI.Input name="name" maxlength="30" title="common_dev_channelName" type="text"/>
    </td>
    <td valign="middle">
        <@ZKUI.Input name="sn" maxlength="30" title="common_dev_sn" type="text"/>
    </td>
</tr>
</@ZKUI.SearchTop>
</@ZKUI.Searchbar>
</@ZKUI.SelectContent>

<script type="text/javascript">
    /**
     * 绑定视频摄像头
     */
    function bindChannel(value, text, event)
    {
        var maxSelectItem = 5;
        if (value.split(",").length > maxSelectItem) {
            messageBox({messageType:"alert",text: "<@i18n 'common_prompt_noPass'/>".format(maxSelectItem)});
            return null;
        }
        var entityId = "${entityId!}";
        var entityName = "${entityName!}";
        var deviceSn = "${deviceSn!}";
        $.ajax({
            url:"/accReader.do?bindOrUnbindChannel",
            type:"post",
            data: {
                entityId:entityId,
                entityName:entityName,
                channelIds:value,
                deviceSn:deviceSn
            },
            success:function(result)
            {
                if (result.ret === sysCfg.success)
                {
                    openMessage("success", "${common_op_succeed}");
                    if (typeof afterReaderBindVidChannel == "function") {
                        afterReaderBindVidChannel();
                    }
                }
                else
                {
                    openMessage("error", "${common_op_failed}");
                }
            }
        });
    }
</script>