<#assign gridName="accReaderGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
				<td valign="middle">
					<@ZKUI.Input name="name"  maxlength="30" title="acc_readerDefine_readerName" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="doorName"  maxlength="30" title="acc_door_name" type="text"/>
				</td>
			</tr>
    	</@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="acc:reader:refresh"></@ZKUI.ToolItem>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccReaderItem" query="accReader.do?list"/>
</@ZKUI.GridBox>

<script type="text/javascript">

	/**
	 * 绑定摄像头后刷新界面
	 */
	function afterReaderBindVidChannel() {
		ZKUI.Grid.reloadGrid("${gridName}", function(){}, null);
	}

	function accReaderBindChannel(gridName, domObj, rid) {
        $.ajax({
            type: "POST",
            url: "accChannel.do?isExistVidChannel",
            dataType: "json",
            async: false,
            success: function(data)
            {
                if(data.ret == sysCfg.success)
                {
                    var path = "accReader.do?getChannelByEntityId&entityId=" + rid;
                    var opts = {//弹窗配置对象
                        path: path,//设置弹窗路径
                        width: 900,//设置弹窗宽度
                        height: 470,//设置弹窗高度
                        title: "<@i18n 'common_vid_bindOrUnbindChannel'/>",//设置弹窗标题
                        gridName: "gridbox"//设置grid

                    };
                    DhxCommon.createWindow(opts);
                }
                else
                {
                    messageBox({messageType: "alert", text: data.msg});
                }
            }
        });
    }
    
</script>