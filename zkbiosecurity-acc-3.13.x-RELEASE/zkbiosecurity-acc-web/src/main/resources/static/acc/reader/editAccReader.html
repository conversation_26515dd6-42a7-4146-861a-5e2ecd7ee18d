<#include '/public/template/editTemplate.html'>
<#macro editContent>

<form action='/accReader.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
	<input type='hidden' name='id' value='${(item.id)!}'/>
	<table class='tableStyle'>
	    <tr>
		    <th><label><@i18n 'acc_door_name'/></label><span class="required">*</span></th>
		    <td><input name="doorName" type="text" value="${(item.doorName)!}" disabled="disabled" /></td>
		</tr>
		<tr>
			<th><label><@i18n 'common_reader_name'/></label><span class='required'>*</span></th>
			<td><input name='name' type='text' maxlength="30" value='${(item.name)!}'/></td>
		</tr>
		<tr>
			<th><label><@i18n 'common_number'/></label><span class='required'>*</span></th>
			<td>
			    <input name='readerNo' type='text' value='${(item.readerNo)!}' disabled="disabled" />
			</td>
		</tr>
		<tr>
			<th><label><@i18n 'acc_reader_inout'/></label><span class='required'>*</span></th>
			<td>
			    <label><@ZKUI.Input hideLabel="true" type="radio" name="readerState" disabled="disabled" value="0" checked="checked"/><@i18n 'common_in'/></label>
				<label>
					<#if (item.readerState)?exists&&item.readerState!=0>
						<@ZKUI.Input hideLabel="true" type="radio" name="readerState" disabled="disabled" value="1" checked="checked"/>
					<#else>
						<@ZKUI.Input hideLabel="true" type="radio" name="readerState" disabled="disabled" value="1" />
					</#if>
				<@i18n 'common_out'/>
				</label>
			</td>
		</tr>
	    <#if (isSupportConfig)?exists && isSupportConfig>
			<tr>
				<th><label><@i18n 'acc_reader_commType'/></label><span class="required">*</span></th>
				<td>
					<@ZKUI.Combo hideLabel="true" width="148" empty="false" id="commType" name="commType" oldValue="${(item.commType)!}">
						<option value="1" tag="address" tagName="<@i18n 'acc_readerCommType_rs485Address'/>"><@i18n 'acc_readerCommType_rs485'/></option>
						<option value="2" tag="address" tagName="<@i18n 'acc_readerCommType_wgAddress'/>"><@i18n 'acc_readerCommType_wiegand'/></option>
						<#if (isSupportWgOrRS485)?exists && isSupportWgOrRS485>
						<option value="3" tag="address" tagName="<@i18n 'acc_readerCommType_rs485Address'/>"><@i18n 'acc_readerCommType_wg485'/></option>
						</#if>
						<option value="4" tag="tcp"><@i18n 'acc_readerCommType_tcp'/></option>
						<option value="8" tag="mac"><@i18n 'acc_readerCommType_zigbee'/></option>
						<option value="0"><@i18n 'acc_readerCommType_disable'/></option>
					</@ZKUI.Combo>
				</td>
			</tr>
			<tr class="commType_address">
				<th><label><@i18n 'acc_readerCommType_rs485Address'/></label><span class="required">*</span></th>
				<td><input type="text" id="readerCommAddress" name="commAddress" value="${(item.commAddress)!}"
					oldValue="${(item.commAddress)!}" tempValue="${(item.commAddress)!}" valid="required: true,digits: true,comAddressRange:[1,15]"/></td>
			</tr>
			<tr class="commType_tcp">
				<th><label><@i18n 'common_ipAddress'/></label><span class="required">*</span></th>
				<td class="ipAddressValidate">
					<div id="ipInput" class="ip_input" style="width: 150px"></div>
					<input type="hidden" name="ip" value="${(item.ip)!}" id="idIPAddress"/>
				</td>
			</tr>
			<tr style="display:none">
				<th><label><@i18n 'common_dev_ipPort'/></label><span class="required">*</span></th>
				<td><input type="text" name="port" value="${(item.port)!4376}" readonly="readonly"/></td>
			</tr>
			<tr class="commType_mac">
				<th><label><@i18n 'common_dev_macAddress'/></label><span class="required">*</span></th>
				<td><input type="text" name="mac" value="${(item.mac)!}" valid="required: true,macValid: true"/></td>
			</tr>
			<#if "${Application['system.language']}" == "zh_CN">
				<tr class="commType_readerModel" id="tr_readMode">
					<th><label><@i18n 'acc_reader_readMode_idCard'/></label><span class="required"></span></th>
					<td>
						<#if (readMode)?exists&&readMode=="1">
							<@ZKUI.Input hideLabel="true" type="checkbox" id="readMode" checked="checked"/>
						<#else>
							<@ZKUI.Input hideLabel="true" type="checkbox" id="readMode" />
						</#if>
						<input type="hidden" name="readMode" value="0" id="readModeHidden" />
					</td>
				</tr>
			</#if>
		</#if>
		<#if (readerDisable)?exists && (isSupportConfig)?exists && !isSupportConfig>
			<tr>
				<th><label><@i18n 'acc_reader_commType'/></label></th>
				<td>
					<@ZKUI.Combo hideLabel="true" width="148" empty="false" name="commType" id="commType" value="${(item.commType)!}">
						<#if !noSupportRS485?exists>
						<option value="3" <#if item.commType==3>selected="selected"</#if>><@i18n 'acc_readerCommType_wg485'/></option>
						</#if>
						<#if !noSupportRS485?exists>
						<option value="1" <#if item.commType==1>selected="selected"</#if>><@i18n 'acc_readerCommType_rs485'/></option>
						</#if>
						<option value="2" <#if item.commType==2>selected="selected"</#if>><@i18n 'acc_readerCommType_wiegand'/></option>
						<option value="0" <#if item.commType==0>selected="selected"</#if>><@i18n 'acc_readerCommType_disable'/></option>
					</@ZKUI.Combo>
				</td>
			</tr>
			
		</#if>
		<#if (supportOSDP)?exists>
		<tr class="rs485ProtocolTypeTr" style="display:none">
			<th><label><@i18n 'acc_reader_rs485Type'/></label></th>
			<td>
				<@ZKUI.Combo hideLabel="true" width="148" empty="false" name="rs485ProtocolType" id="rs485ProtocolType" value="${(item.rs485ProtocolType)!}">
					<option value="1" <#if item.rs485ProtocolType==1>selected="selected"</#if>>ZK485</option>
					<option value="2" <#if item.rs485ProtocolType==2>selected="selected"</#if>>OSDP</option>
				</@ZKUI.Combo>
			</td>
		</tr>
		</#if>
		<#if (encrypt)?exists>
			<tr>
				<th><label><@i18n 'acc_reader_encrypt'/></label></th>
				<td>
					<#if requireCardControl?exists>
						<@ZKUI.Input hideLabel="true" type="checkbox" id="readerEncrypt" disabled="disabled"/>
					<#else>
						<@ZKUI.Input hideLabel="true" type="checkbox" id="readerEncrypt"/>
					</#if>
					<input type="hidden" name="readerEncrypt" value="false" id="readerEncryptHidden" />
				</td>
			</tr>
		</#if>
		<#if (isSupportUserLockFun)?exists && isSupportUserLockFun>
			<tr>
				<th><label><@i18n 'acc_reader_userLock'/></label></th>
				<td>
					<@ZKUI.Combo hideLabel="true" width="148" empty="false" name="userLockFun" id="userLockFun" value="${(item.userLockFun)!}">
						<option value="0" <#if item.userLockFun==0>selected="selected"</#if>><@i18n 'common_no'/></option>
						<option value="1" <#if item.userLockFun==1>selected="selected"</#if>><@i18n 'common_yes'/></option>
					</@ZKUI.Combo>
				</td>
			</tr>
		</#if>
		<#if (isSupportInfoReveal)?exists && isSupportInfoReveal>
			<tr>
				<th><label><@i18n 'acc_reader_userInfoReveal'/></label></th>
				<td>
					<@ZKUI.Input hideLabel="true" disabled="disabled" type="checkbox" id="userInfoReveal"/>
					<input type="hidden" name="userInfoReveal" value="0" id="userInfoRevealHidden" />
				</td>
			</tr>
		</#if>

		<!--目前只有人员锁定功能设置需要复制-->
		<#if (isSupportUserLockFun)?exists && isSupportUserLockFun>
			<tr>
				<th><label><@i18n 'common_copySettingsTo'/></label></th>
				<td>
					<@ZKUI.Combo hideLabel="true" width="148" empty="false" name="applyTo" id="id_applyTo">
						<option value="0" selected="selected">---------</option>
						<option value="1"><@i18n 'acc_reader_allReaderOfCurDev'/></option>
					</@ZKUI.Combo>
				</td>
			</tr>
		</#if>

	</table>
	<#if (encrypt)?exists>
		<div style="padding-${leftRTL!'left'}: 10px;padding-top: 10px"><span class="warningImage"></span><span class="warningColor"><@i18n 'acc_reader_tip1'/></span></div>
	</#if>
    <#if (isSupportInfoReveal)?exists && isSupportInfoReveal>
        <div style="padding-${leftRTL!'left'}: 10px;padding-top: 10px"><span class="warningImage"></span><span class="warningColor"><@i18n 'acc_reader_tip4'/></span></div>
    </#if>
		<div class="commType_warning" style="padding-${leftRTL!'left'}: 10px;padding-top: 10px; display: none"><span class="warningImage"></span><span class="warningColor"><@i18n 'acc_reader_tip2'/></span></div>
	<#if (supportOSDP)?exists>
		<div style="padding-${leftRTL!'left'}: 10px;padding-top: 10px"><span class="warningImage"></span><span class="warningColor"><@i18n 'acc_reader_tip3'/></span></div>
	</#if>

</form>

<script type='text/javascript'>
	$(function() {
		$('#${formId}').validate( {
			debug : true,
			rules :
					{
						'name' :
								{
									required : true,
									unInputChar:true,
									overRemote : ["accReader.do?isExist", "${(item.name)!}"]
								},
						'doorName' :
								{
									required : true
								},
						'readerNo' :
								{
									required : true
								}
								<#if (isSupportConfig)?exists && isSupportConfig>
						,"ip" :
								{
									overRemoteIP : [ "/accReader.do?isExistIP", "${(item.ip)!}","commType","${(item.commType)!}"],
									ipAddressValid: true
								}
								</#if>
					},
			submitHandler : function()
			{
				$("#${formId} [name='readerState']").attr("disabled", false);
				<@submitHandler/>
			}
		});
	});


	<#if (isSupportInfoReveal)?exists && isSupportInfoReveal>
		<#if (item.userInfoReveal)?exists && item.userInfoReveal?string == "1">
			$("#userInfoReveal").attr("checked", "checked");
			$("#userInfoRevealHidden").val("1");
		</#if>
	</#if>
	$("#userInfoReveal").click(function(){
		var userInfoRevealChecked = $("#userInfoReveal").attr("checked");
		$("#userInfoRevealHidden").val(userInfoRevealChecked?"1":"0");
	});

	function userInfoRevealValChange() {
		var commType;
		if (ZKUI.Combo.PULL.commType) {
			commType = ZKUI.Combo.PULL.commType.combo.getSelectedValue();
		}
		var readerEncryptChecked = $("#readerEncrypt").attr("checked");
		var userInfoRevealDisabled = readerEncryptChecked;
		$("#readerEncryptHidden").val(readerEncryptChecked?true:false);
		$("#userInfoReveal").attr("disabled", !userInfoRevealDisabled);
        if (!userInfoRevealDisabled) {
            $("#userInfoRevealHidden").val("0");
			$("#userInfoReveal").attr("checked", false);
        }

		if (commType == 1 || commType == 3) {
			$('.rs485ProtocolTypeTr').show();
		} else {
			$('.rs485ProtocolTypeTr').css('display', 'none');
		}
	}

	<#if (encrypt)?exists>
	<#if (item.readerEncrypt)?exists && item.readerEncrypt?string == "true">
			$("#readerEncrypt").attr("checked", "checked");
		$("#readerEncryptHidden").val(true);
	</#if>

	$("#readerEncrypt").click(function(){
		userInfoRevealValChange();
	});
	</#if>
	<#if (isSupportConfig)?exists && isSupportConfig>
			jQuery.validator.addMethod("ipAddressValid", function(value, element)
			{
				var ipv4_1 = $("#ipv4_1").val();
				var ipv4_2 = $("#ipv4_2").val();
				var ipv4_3 = $("#ipv4_3").val();
				var ipv4_4 = $("#ipv4_4").val();
				if (ipv4_1 == "" || ipv4_2 == "" || ipv4_3 == "" || ipv4_4 == "")
				{
					$("#ipInput #ipv4").css('border','1px solid red');
					return false;
				}
				else
				{
					$("#ipInput #ipv4").css('border','1px solid #c3c2c2');
					return true;
				}
			}, "<@i18n 'common_jqMsg_required'/>");
	jQuery.validator.addMethod("overRemoteIP", function(value, element, remoteParam){
		//console.log(element.value + "---" + element.id);
		var url = remoteParam[0];
		var oldValue = remoteParam[1];
		var commType = ZKUI.Combo.get(remoteParam[2]) ? ZKUI.Combo.get(remoteParam[2]).combo.getSelectedValue() : "";
		if(value != oldValue || commType != remoteParam[3])
		{
			var param = {
				url : url, //后台处理程序
				type : "post" //数据发送方式
			};
			//下面是jquery remote源码
			if ( this.optional(element) )
				return true;

			var previous = this.previousValue(element);
			if (!this.settings.messages[element.name] )
				this.settings.messages[element.name] = {};
			previous.originalMessage = this.settings.messages[element.name].remote;
			this.settings.messages[element.name].remote = previous.message;

			param = typeof param == "string" && {url:param} || param;

			if ( this.pending[element.name] ) {
				return "pending";
			}
			if ( previous.old === value ) {
				return previous.valid;
			}

			previous.old = value;
			var validator = this;
			this.startRequest(element);
			var data = {};
			data[element.name] = value;
			$.ajax($.extend(true, {
				url: url,
				mode: "abort",
				port: "validate" + element.name,
				dataType: "json",
				data: data,
				success: function(response) {
					validator.settings.messages[element.name].remote = previous.originalMessage;
					var valid = response === true;
					if ( valid ) {
						var submitted = validator.formSubmitted;
						validator.prepareElement(element);
						validator.formSubmitted = submitted;
						validator.successList.push(element);
						validator.showErrors();
					} else {
						var errors = {};
						var message = response || validator.defaultMessage( element, "remote" );
						errors[element.name] = previous.message = $.isFunction(message) ? message(value) : message;
						validator.showErrors(errors);
					}
					previous.valid = valid;
					validator.stopRequest(element, valid);
				}
			}, param));
			return "pending";
		}
		return true;
	}, "<@i18n 'common_jqMsg_remote'/>");
	jQuery.validator.addMethod("macValid", function(val, element)
	{
		var macReg = /^[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}:[A-F\d]{2}$/i;
		var re = new RegExp(macReg);
		if (val.trim() != "" && !re.test(val))
		{
			return false;
		}
		return true;
	}, "<@i18n 'acc_reader_macError'/>");
	jQuery.validator.addMethod("comAddressValid", function(val, element)
	{
		var flag = true;
		var commType = ZKUI.Combo.get("commType").combo.getSelectedValue();
		if(commType != ZKUI.Combo.PULL.commType.options.oldValue || val != $(element).attr("oldValue"))
		{
			$.ajax({
				type: "POST",
				url: "accReader.do?readerCommAddressExist",
				data:{
					"commType":commType,
					"commAddress":val,
					"id":"${(item.id)!}"
				},
				async: false,
				success: function(ret)
				{
					flag = !ret;
				}
			});
		}
		return flag;
	}, "<@i18n 'acc_readerComAddress_repeat'/>");

	jQuery.validator.addMethod("comAddressRange", function(val, element, param)
	{
		var flag = true;
		param[1] = 15;//rs485  range [1,15]
		if(val < param[0] || val > param[1]){
			flag = false;
		}
		return flag;
	}, "<@i18n 'common_jqMsg_range'/>");
	//ip地址输入框
	var ipv4 = new IpV4Box("ipv4" , "ipInput");
	ipv4.setValue($("#idIPAddress").val());
	$("#ipv4_1").keyup(function()
	{
		$("#idIPAddress").attr("value", ipv4.getValue());
		$("#idIPAddress").valid();
	});
	$("#ipv4_2").keyup(function()
	{
		$("#idIPAddress").attr("value", ipv4.getValue());
		$("#idIPAddress").valid();
	});
	$("#ipv4_3").keyup(function()
	{
		$("#idIPAddress").attr("value", ipv4.getValue());
		$("#idIPAddress").valid();
	});
	$("#ipv4_4").keyup(function()
	{
		$("#idIPAddress").attr("value", ipv4.getValue());
		$("#idIPAddress").valid();
	});
	//$("tr.readerState input").removeAttr("disabled");


	var commType = "${(item.commType)!}",supportReaderType="${(supportReaderType)!}";
	accReaderForEachCommType();
	function accReaderForEachCommType() {
		var accCommTypeDel = new Array();
		ZKUI.Combo.PULL.commType.combo.forEachOption(function(options) {
			var value = options.value;
			if(value == commType) {
				ZKUI.Combo.PULL.commType.setValue(value);
			}
			else if((value & supportReaderType) != value) {
				accCommTypeDel.push(value);
			}
		})
		for (var i = 0; i < accCommTypeDel.length; i++) {
			ZKUI.Combo.PULL.commType.combo.deleteOption(accCommTypeDel[i]);
		}
	}

	var commTypeTagMaps = {
		"1" : { tag:"address", tagName:"<@i18n 'acc_readerCommType_rs485Address'/>"},
		"2" : { tag:"", tagName:"<@i18n 'acc_readerCommType_wgAddress'/>"},
		"3" : { tag:"address", tagName:"<@i18n 'acc_readerCommType_rs485Address'/>"},
		"4" : { tag:"tcp", tagName:"<@i18n 'acc_readerCommType_tcp'/>"},
		"8" : { tag:"mac", tagName:"<@i18n 'acc_readerCommType_zigbee'/>"},
		"0" : { tag:"", tagName:"<@i18n 'acc_readerCommType_disable'/>"},
	}

	function commTypeChange(value) {
		userInfoRevealValChange();
		$("tr[class^='commType']").hide();
		$(".commType_warning").hide();
		var val = ZKUI.Combo.PULL.commType.combo.getSelected();
		var tag = commTypeTagMaps[val].tag;
		$("tr[class^='commType'] input[type='text']").each(function(){
			$(this).rules("remove");
			$(this).removeData('previousValue');
			$(this).valid();
		});
		$("#idIPAddress").rules("remove");
		if(tag)
		{
			$("tr[class='commType_"+tag+"']").show();

			$("tr[class='commType_"+tag+"'] input").each(function(){
				var $inp = $(this);
				if($inp.attr("valid"))
				{
					var validOpt = eval("({"+$inp.attr("valid")+"})");
					$inp.rules("add",validOpt);
				}
			});
			if("address" == tag)//RS485地址或韦根地址
			{
				if($("#readerCommAddress").attr("tempValue") != $("#readerCommAddress").attr("oldValue"))
				{
					$("#readerCommAddress").val($("#readerCommAddress").attr("tempValue"));
				}
				$("tr[class='commType_"+tag+"'] label").text(commTypeTagMaps[val].tagName);
				$("#readerCommAddress").valid();
			}
			else
			{
				$("#readerCommAddress").attr("tempValue",$("#readerCommAddress").val());
				$("#readerCommAddress").val($("#readerCommAddress").attr("oldValue"));
				if("tcp" == tag)
				{
					$(".ipAddressValidate .ts_box").remove();
					$(".ipAddressValidate #ipv4").css("border","1px solid #C3C2C2");
					$("#idIPAddress").rules("add", {
						overRemoteIP : [ "/accReader.do?isExistIP", "${(item.ip)!}","commType","${(item.commType)!}"],
						ipAddressValid: true}
					);

					$(".commType_readerModel").show();
					if (sysCfg.language == "zh_CN") {
						$(".commType_warning").show();
					}
				}
			}
		}
	}
	ZKUI.Combo.get("commType").combo.attachEvent("onChange",function(value,text) {
		commTypeChange(value);
	})
	commTypeChange(ZKUI.Combo.PULL.commType.combo.getSelectedValue());
	<#if (readMode)?exists && readMode?string == "1">
        $("#readMode").attr("checked", "checked");
	    $("#readModeHidden").val("1");
	</#if>
	$("#readMode").click(function(){
		if($(this).attr("checked"))
		{
			$("#readModeHidden").val("1");
		}
		else
		{
			$("#readModeHidden").val("0");
		}
	});
	</#if>

	<#if (extBoard)?exists>
        ZKUI.Combo.PULL.commType.combo.disable(true);
        $("#readerCommAddress").attr("disabled", true);
        $(".commType_address").hide();
	</#if>

	<#if (readerDisable)?exists && (isSupportConfig)?exists && !isSupportConfig>
		ZKUI.Combo.get("commType").combo.attachEvent("onChange", function(value, text){
			var commType = value;
			if (commType == 1 || commType == 3) {
				$('.rs485ProtocolTypeTr').show();
			} else {
				$('.rs485ProtocolTypeTr').css('display', 'none');
			}
		});
		ZKUI.Combo.get("commType").combo.callEvent("onChange",[ZKUI.Combo.get("commType").combo.getSelectedValue()]);
	</#if>
</script>
</#macro>