<#assign gridName="accAlarmTransactionGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}" style="height:100%;width:100%">
	<@ZKUI.Searchbar>
		<@ZKUI.SearchTop>
			<tr>
				<td valign="middle">
					<@i18n 'common_time_from'/>&nbsp;&nbsp;
					<@ZKUI.Input type="datetime" endId="endTimeId${uuid}" id="startTime${uuid}" name="startTime" title="common_time_from" max="today" today="-7" offsetField="D" todayRange="start" hideLabel="true" noOverToday="true" readonly="false"/>
					&nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
					<@ZKUI.Input type="datetime" id="endTimeId${uuid}" name="endTime" title="common_to" max="today" today="true" todayRange="end" noOverToday="true" hideLabel="true" readonly="false"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="pin" maxlength="30" title="pers_person_pin" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="devAlias" maxlength="30" title="common_dev_name" type="text"/>
				</td>
			</tr>
		</@ZKUI.SearchTop>
		<@ZKUI.SearchBelow>
			<tr>
				<td valign="middle">
					<@ZKUI.Input name="deptCode" maxlength="30" title="pers_dept_deptNo" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="deptName" maxlength="30" title="pers_dept_deptName" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.ComboGrid id="accEventId${uuid}" title="common_eventDescription" width="137" queryField="name" name="eventName"
					grid_vo="com.zkteco.zkbiosecurity.acc.vo.AccDeviceEventSelectItem"
					grid_query="accDeviceEvent.do?listSelect"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="cardNo" maxlength="30" title="pers_card_cardNo" type="text"/>
				</td>
			</tr>
			<tr>
				<td valign="middle">
					<@ZKUI.Input name="readerName" maxlength="30" title="acc_readerDefine_readerName" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="areaName" maxlength="30" title="base_area_name" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="eventPointName" maxlength="30" title="common_eventPoint" type="text"/>
				</td>
			</tr>
			<tr>
				<td valign="middle">
					<@ZKUI.Input name="likeName" maxlength="30" title="pers_person_wholeName" type="text"/>
				</td>
				<td>
					<@ZKUI.Input name="devSn" maxlength="30" title="common_dev_sn" type="text"/>
				</td>
                <td>
                    <@ZKUI.Combo name="eventLevel" maxlength="30" empty="true" title="common_event_level" readonly="true">
                    <option value="1"><@i18n 'common_exception'/></option>
                    <option value="2"><@i18n 'common_alarm'/></option>
                    </@ZKUI.Combo>
                </td>
			</tr>
		</@ZKUI.SearchBelow>
	</@ZKUI.Searchbar>
	<@ZKUI.Toolbar>
		<@ZKUI.ToolItem id="refresh" type="refresh" permission="acc:alarmTransaction:refresh"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="accAlarmTransaction.do?clearData" action="accClearAlarmData" text="common_op_clearData" img="comm_del.png" permission="acc:alarmTransaction:del"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="accAlarmTransaction.do?export" type="export" text="common_op_export" img="comm_export.png" permission="acc:alarmTransaction:export"></@ZKUI.ToolItem>
	</@ZKUI.Toolbar>
	<@ZKUI.Grid pageList="true" limitCount="200000" vo="com.zkteco.zkbiosecurity.acc.vo.AccAlarmTransactionItem" query="accAlarmTransaction.do?list" showColumns="!logId,deptCode"/>
</@ZKUI.GridBox>
<script type="text/javascript">
	function onTreeChecked(id, state) {
		var comboTreeId = this.treeId;
		var comboTree = ZKUI.ComboTree.get(comboTreeId);
		var combo = comboTree.combo;
		var tree = comboTree.tree;
	}

    var gridName = "${gridName}";
    function accClearAlarmData(id, bar) {
        deleteConfirm(function(result) {
            if (result) {
                openMessage(msgType.loading);
                $.ajax({
                    url : id,
                    type : "post",
                    success : function(result) {
                        dealRetResult(eval(result), function() {
                            closeMessage();
                            ZKUI.Grid.reloadGrid(gridName);
                        });
                    }
                });
            }
        }, "<@i18n 'common_prompt_sureToDelAll'/>");

    }
</script>