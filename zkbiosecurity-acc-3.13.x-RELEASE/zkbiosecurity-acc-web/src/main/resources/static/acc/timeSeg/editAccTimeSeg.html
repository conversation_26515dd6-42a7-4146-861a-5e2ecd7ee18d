<#include "/public/template/editTemplate.html">
<#macro editContent>
<script type="text/javascript">
    TimeZoneBox.messageObj = {
        alert: {
            title: "<@i18n 'common_prompt_title'/>",
            text: "<@i18n 'acc_InputMethod_tips'/>"
        }
    }
    $().ready(function() {
        // 字母和数字的验证
        $("#${formId}").validate( {
            debug : true,
            rules : {
                "name" : {
                    required : true,
                    unInputChar:true,
                    overRemote : ["/accTimeSeg.do?validName", "${(tempAccTimeSeg.name)!}"]
                }
             },
            submitHandler : function(){
                var initFlag = "${(tempAccTimeSeg.initFlag)!}";
                if(initFlag==false){
                    if(beforeSubmit())
                    {
                        $("#${formId}").attr("action", "/accTimeSeg.do?dataValid");
                        $("#inputValueDiv${uuid}").appendTo($("#${formId}"));
                        $('#${formId}').ajaxSubmit(function(returnData) {
                            if (returnData.data == "true")
                            {
                                onLoading(function(){
                                    $("#${formId}").attr("action", "/accTimeSeg.do?save");
                                    <@submitHandler/>
                                });
                            }
                            else
                            {
                                $("#id_info${uuid}").show();
                                $("#id_info${uuid}").html("*"+returnData.data);
                            }
                        });
                        /*$("#inputValueDiv${uuid}").appendTo($("#${formId}"));
                        <@submitHandler/>*/
                    }
                }else{
                    messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_initDataCanNotEdit'/>"});
                }
            }
        });
    });
</script>
<form method="post" id="${formId}" action="/accTimeSeg.do?save" enctype="multipart/form-data" onkeydown="if(event.keyCode==13){return false;}">
	<input type="hidden" value="${(tempAccTimeSeg.id)!}" name="id" id="id_model_pk">
	<table class="tableStyle" style="border-bottom: none">
		<tr>
			<th>
				<label><@i18n 'common_timeSeg_name'/></label><span class="required">*</span>
			</th>
			<td>
				<input type="text" maxlength="30" name="name"
					   value="${(tempAccTimeSeg.name)!}" id="id_timeseg_name">
			</td>
		</tr>
		<tr id="id_startDateTr" style="display: none;">
			<th><label><@i18n 'common_startDate'/></label></th>
			<td>
				<@ZKUI.Input type="date" id="startDate2${uuid}" endId="endDate2${uuid}" todayRange="start" value="${(tempAccTimeSeg.startTime?string('yyyy-MM-dd'))!}" name="startTime" hideLabel="true" notCheckRange="true" readonly="true"/>
			</td>
		</tr>
		<tr id="id_endDateTr" style="display: none;">
			<th><label><@i18n 'common_endDate'/></label></th>
			<td>
				<@ZKUI.Input type="date" id="endDate2${uuid}" todayRange="end" value="${(tempAccTimeSeg.endTime?string('yyyy-MM-dd'))!}" name="endTime" hideLabel="true" notCheckRange="true" readonly="true"/>
			</td>
		</tr>
		<tr>
			<th>
				<label><@i18n 'common_remark'/></label>
			</th>
			<td>
				<input type="text" maxlength="50" name="remark" value="${(tempAccTimeSeg.remark)!}" id="id_remark">
			</td>
		</tr>
		<!--<tr><td colspan="7"><hr color="#AAA" size="1px"/></td>-->
	</table>

</form>
<!-- 为了减少验证时间，点击提交后移植表单中 -->
<div id="inputValueDiv${uuid}" class="displayN">
	<#assign dateValue = ["${(tempAccTimeSeg.mondayStart1)!}","${(tempAccTimeSeg.mondayEnd1)!}","${(tempAccTimeSeg.mondayStart2)!}","${(tempAccTimeSeg.mondayEnd2)!}","${(tempAccTimeSeg.mondayStart3)!}","${(tempAccTimeSeg.mondayEnd3)!}",
	"${(tempAccTimeSeg.tuesdayStart1)!}","${(tempAccTimeSeg.tuesdayEnd1)!}","${(tempAccTimeSeg.tuesdayStart2)!}","${(tempAccTimeSeg.tuesdayEnd2)!}","${(tempAccTimeSeg.tuesdayStart3)!}","${(tempAccTimeSeg.tuesdayEnd3)!}",
	"${(tempAccTimeSeg.wednesdayStart1)!}","${(tempAccTimeSeg.wednesdayEnd1)!}","${(tempAccTimeSeg.wednesdayStart2)!}","${(tempAccTimeSeg.wednesdayEnd2)!}","${(tempAccTimeSeg.wednesdayStart3)!}","${(tempAccTimeSeg.wednesdayEnd3)!}",
	"${(tempAccTimeSeg.thursdayStart1)!}","${(tempAccTimeSeg.thursdayEnd1)!}","${(tempAccTimeSeg.thursdayStart2)!}","${(tempAccTimeSeg.thursdayEnd2)!}","${(tempAccTimeSeg.thursdayStart3)!}","${(tempAccTimeSeg.thursdayEnd3)!}",
	"${(tempAccTimeSeg.fridayStart1)!}","${(tempAccTimeSeg.fridayEnd1)!}","${(tempAccTimeSeg.fridayStart2)!}","${(tempAccTimeSeg.fridayEnd2)!}","${(tempAccTimeSeg.fridayStart3)!}","${(tempAccTimeSeg.fridayEnd3)!}",
	"${(tempAccTimeSeg.saturdayStart1)!}","${(tempAccTimeSeg.saturdayEnd1)!}","${(tempAccTimeSeg.saturdayStart2)!}","${(tempAccTimeSeg.saturdayEnd2)!}","${(tempAccTimeSeg.saturdayStart3)!}","${(tempAccTimeSeg.saturdayEnd3)!}",
	"${(tempAccTimeSeg.sundayStart1)!}","${(tempAccTimeSeg.sundayEnd1)!}","${(tempAccTimeSeg.sundayStart2)!}","${(tempAccTimeSeg.sundayEnd2)!}","${(tempAccTimeSeg.sundayStart3)!}","${(tempAccTimeSeg.sundayEnd3)!}",
	"${(tempAccTimeSeg.holidayType1Start1)!}","${(tempAccTimeSeg.holidayType1End1)!}","${(tempAccTimeSeg.holidayType1Start2)!}","${(tempAccTimeSeg.holidayType1End2)!}","${(tempAccTimeSeg.holidayType1Start3)!}","${(tempAccTimeSeg.holidayType1End3)!}",
	"${(tempAccTimeSeg.holidayType2Start1)!}","${(tempAccTimeSeg.holidayType2End1)!}","${(tempAccTimeSeg.holidayType2Start2)!}","${(tempAccTimeSeg.holidayType2End2)!}","${(tempAccTimeSeg.holidayType2Start3)!}","${(tempAccTimeSeg.holidayType2End3)!}",
	"${(tempAccTimeSeg.holidayType3Start1)!}","${(tempAccTimeSeg.holidayType3End1)!}","${(tempAccTimeSeg.holidayType3Start2)!}","${(tempAccTimeSeg.holidayType3End2)!}","${(tempAccTimeSeg.holidayType3Start3)!}","${(tempAccTimeSeg.holidayType3End3)!}"]>
	<#assign dateNames = ["monday","tuesday","wednesday","thursday","friday","saturday","sunday","holidayType1","holidayType2","holidayType3"]>
	<#list dateNames as dateName>
	<input type="text" size="5" value="${dateValue[dateName_index*6]}"
		   id="id_${dateName_index}_start1" maxlength="8"  name="${dateName}Start1">
	<input type="text" size="5" value="${dateValue[dateName_index*6+1]}"
		   id="id_${dateName_index}_end1" maxlength="8" name="${dateName}End1">
	<input type="text" size="5" value="${dateValue[dateName_index*6+2]}"
		   id="id_${dateName_index}_start2" maxlength="8" name="${dateName}Start2">
	<input type="text" size="5" value="${dateValue[dateName_index*6+3]}"
		   id="id_${dateName_index}_end2" maxlength="8" name="${dateName}End2">
	<input type="text" size="5" value="${dateValue[dateName_index*6+4]}"
		   id="id_${dateName_index}_start3" maxlength="8" name="${dateName}Start3">
	<input type="text" size="5" value="${dateValue[dateName_index*6+5]}"
		   id="id_${dateName_index}_end3" maxlength="8" name="${dateName}End3">
</#list>
</div>

<div class="timeseg_edit_div_inner">
	<table class="timeseg_edit_table_inner">
		<tr>
			<td colspan="1" class="blank_bgcolor" id="id_blank1" rowspan="2">
				<div class="out">
					<b><@i18n 'common_time'/></b>
					<em><@i18n 'common_date'/></em>
				</div>
			</td>
			<td colspan="2" class="blue_bgcolor">
				<@i18n 'common_timeSeg_interval1'/>
			</td>
			<td colspan="2" class="blue_bgcolor">
				<@i18n 'common_timeSeg_interval2'/>
			</td>
			<td colspan="2" class="blue_bgcolor">
				<@i18n 'common_timeSeg_interval3'/>
			</td>
		</tr>
		<tr>
			<td class="blue_bgcolor">
				<@i18n 'common_timeSeg_startTime'/>
			</td>
			<td class="blue_bgcolor">
				<@i18n 'common_timeSeg_endTime'/>
			</td>
			<td class="blue_bgcolor">
				<@i18n 'common_timeSeg_startTime'/>
			</td>
			<td class="blue_bgcolor">
				<@i18n 'common_timeSeg_endTime'/>
			</td>
			<td class="blue_bgcolor">
				<@i18n 'common_timeSeg_startTime'/>
			</td>
			<td class="blue_bgcolor">
				<@i18n 'common_timeSeg_endTime'/>
			</td>
		</tr>
		<#assign date = ["common_monday", "common_tuesday", "common_wednesday", "common_thursday", "common_friday", "common_saturday", "common_sunday", "common_holiday_type1", "common_holiday_type2", "common_holiday_type3"]>
		<#list date as i>
		<tr>
			<td class="blue_bgcolor">
                <@i18n '${i}'/>
			</td>
			<td>
				<div id=${i_index}_start1 style="direction: ltr;">
				</div>
			</td>

			<td>
				<div id=${i_index}_end1 style="direction: ltr;"></div>
			</td>

			<td>
				<div id=${i_index}_start2 style="direction: ltr;"></div>
			</td>

			<td>
				<div id=${i_index}_end2 style="direction: ltr;"></div>
			</td>

			<td>
				<div id=${i_index}_start3 style="direction: ltr;"></div>
			</td>

			<td>
				<div id=${i_index}_end3 style="direction: ltr;"></div>
			</td>
		</tr>
	</#list>
	</table>
	<table class="timeseg_edit_table">
		<tr>
			<td style="padding-top:15px;">
				<label><@i18n 'common_timeSeg_copyDate'/></label>
				<@ZKUI.Input hideLabel="true" type="checkbox" name="copyDate" id="idCopyDate" />
			</td>
		</tr>
	</table>
</div>
<div id="id_info${uuid}" class="zk-msg-error" style="padding: 2px 10px;"></div>
<script type="text/javascript">

	// L3支持开始/结束日期设置
	if(sysCfg.securityLevel && sysCfg.securityLevel >= 3) {
		$("#id_startDateTr").show();
		$("#id_endDateTr").show();
	}

    var time_inputs = new Array();
    $("div[id*='_start'], div[id*='_end']").each(function() {
        //alert($(this).attr("id"))
        var div_id = $(this).attr("id");
        time_inputs.push(new TimeZoneBox(div_id + "_span", div_id));//span便于区分
    });

    //合并两个单元格
    $("#id_blank1").attr("rowspan", "2");
    $("#id_blank2").remove();

    //初始化
    var time_val = "";
    for (t in time_inputs)
    {
        time_val = $("#id_" + time_inputs[t].id.replace("_span", "")).val();
        if (time_val == "")
        {
            time_val = "00:00";
        }
        else
        {
            time_val = time_val.substring(0, 2) + ":" + time_val.substring(3, 5);
        }
        time_inputs[t].setValue(time_val);
    }

    function beforeSubmit()
    {
        if ($("#id_model_pk").val() == 1)
        {
            return false;
        }
        for (t in time_inputs)
        {
            var real_id = time_inputs[t].id.replace("_span", "");
            var time = time_inputs[t].getValue();
           	if (time.split(":")[0].length == 1) {
           	    time = "0" + time.split(":")[0] + ":" + time.split(":")[1];
			}
			if (time.split(":")[1].length == 1) {
                time = time.split(":")[0] + ":" + "0" + time.split(":")[1];
			}
            $("#id_" + real_id).val(time);
        }

        var has_error = false;
        $("input[id*='_span_']").each(function() {
            if ($(this).val() == "") {
                $(this).addClass("error");
                has_error = true;
            }
        });
        if (has_error)
        {
            $("#id_info${uuid}").html("*<@i18n 'common_timeSeg_fillError6'/>");//请在文本框内输入有效的时间！
            return false;
        }
        return true;
    }

    var week_date = ["0", "1", "2", "3", "4"];//"5", "6"
    var start_end = ["start1", "end1", "start2", "end2", "start3", "end3"];
    var span = ["span_1", "span_2"];
    var val_array = new Array();// 每个区间开始、结束的时间
    var tempVal = "";// 临时变量
    $("#idCopyDate").click(function(){
        if($(this).attr("checked"))
        {
            val_array = new Array();
            for(var y = 0; y < start_end.length; y++)
            {
                for(var z = 0; z < span.length; z++)
                {
                    tempVal = $("#"+week_date[0]+"_"+start_end[y]+"_"+span[z]).val();// 只以星期一为模板copy

                    val_array.push(tempVal == "" ? "00" : tempVal);
                }
            }
            copyDate(val_array);
        }
        else
        {
            val_array = new Array();
            for(var y = 0; y < start_end.length; y++)
            {
                for(var z = 0; z < span.length; z++)
                {
                    val_array.push("00");
                }
            }
            copyDate(val_array);
        }
    });

    function copyDate(val_array)// 复制、粘贴数据
    {
        var i = -1;
        for(var x = 1; x < week_date.length; x++)
        {
            for(var y = 0; y < start_end.length; y++)
            {
                for(var z = 0; z < span.length; z++)
                {
                    i++;
                    $("#"+week_date[x]+"_"+start_end[y]+"_"+span[z]).val(val_array[i]);
                    i = i == 11 ? -1 : i;
                }
            }
        }
    }
</script>
</#macro>
