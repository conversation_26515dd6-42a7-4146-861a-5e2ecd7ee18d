<!-- 定义变量  begin -->
<!-- 左侧标题 -->
<#assign guideTitle = "acc_guide_title">
<!-- 左侧图片 -->
<#assign guideImg = "/images/${attr('system.skin','lightgreen')}/acc_guide_map.png">
<#assign other = "common_leftMenu_rtMonitor">
<!-- 定义变量  end -->

<!-- 引入模板，必须在上述变量定义后才能引入 -->
<#include "public/template/guideTemplate.html">

<!-- 宏定义，即定义各区域引导内容-->
<!-- whoContent: who区域内容 -->
<#macro whoContent>
    <div class="guide-item-desc"><@i18n 'acc_guide_addPersonTip'/></div>
    <div class="guide-item-menu">
        <#if hasPermission('pers:person')>
        <a onclick="openMenuItem('persPerson.do','Pers')"><@i18n 'common_op_addPerson'/></a>
        <#else>
        <span class="noPermissionTip warningColor"><@i18n 'common_no_authority_access'/></span>
        </#if>
    </div>
</#macro>

<!-- whenContent: when区域内容 -->
<#macro whenContent>
    <div class="guide-item-desc"><@i18n 'acc_guide_timesegTip'/></div>
    <div class="guide-item-menu">
        <#if hasPermission('acc:timeSeg')>
        <a onclick="openMenuItem('accTimeSeg.do','Acc')"><@i18n 'common_leftMenu_timeZone'/></a>
        <#else>
        <span class="noPermissionTip warningColor"><@i18n 'common_no_authority_access'/></span>
        </#if>
    </div>
</#macro>
<#macro whereContent>
    <div class="guide-item-desc"><@i18n 'acc_guide_addDeviceTip'/></div>
    <div class="guide-item-menu">
        <#if hasPermission('acc:device')>
        <a onclick="openMenuItem('accDevice.do','Acc')"><@i18n 'common_leftMenu_device'/></a>
        <#else>
        <span class="noPermissionTip warningColor"><@i18n 'common_no_authority_access'/></span>
    </#if>
    </div>
</#macro>
<#macro howContent>
    <div class="guide-item-desc"><@i18n 'acc_guide_addLevelTip'/></div>
    <div class="guide-item-menu">
        <#if hasPermission('acc:level')>
        <a onclick="openMenuItem('accLevel.do','Acc')"><@i18n 'acc_guide_addLevelTip'/></a>
        <#else>
        <span class="noPermissionTip warningColor"><@i18n 'common_no_authority_access'/></span>
        </#if>
    </div>

    <div class="guide-item-desc" style="padding-top:20px"><@i18n 'acc_guide_personLevelTip'/></div>
    <div class="guide-item-menu">
        <#if hasPermission('acc:personLevelByLevel')>
        <a onclick="openMenuItem('accPersonLevelByLevel.do','Acc')"><@i18n 'common_leftMenu_levelSetByLevel' /></a>
        <#else>
        <span class="noPermissionTip warningColor"><@i18n 'common_no_authority_access'/></span>
        </#if>
    </div>

    <div class="guide-item-menu">
        <#if hasPermission('acc:personLevelByPerson')>
        <a onclick="openMenuItem('accPersonLevelByPerson.do','Acc')"><@i18n 'common_leftMenu_levelSetByPerson' /></a>
        <#else>
        <span class="noPermissionTip warningColor"><@i18n 'common_no_authority_access'/></span>
        </#if>
    </div>

    <div class="guide-item-menu">
        <#if hasPermission('acc:personLevelByDept')>
        <a onclick="openMenuItem('accPersonLevelByDept.do','Acc')"><@i18n 'common_leftMenu_levelSetByDept' /></a>
        <#else>
        <span class="noPermissionTip warningColor"><@i18n 'common_no_authority_access'/></span>
    </#if>
    </div>
</#macro>
<#macro otherContent>
    <div class="guide-item-desc"><@i18n 'acc_guide_rtMonitorTip'/></div>
    <div class="guide-item-menu">
        <#if hasPermission('acc:rtMonitor')>
        <a onclick="openMenuItem('accRTMonitor.do','Acc')"><@i18n 'common_leftMenu_rtMonitor' /></a>
        <#else>
        <span class="noPermissionTip warningColor"><@i18n 'common_no_authority_access'/></span>
        </#if>
    </div>

    <div class="guide-item-menu">
        <#if hasPermission('acc:alarmMonitor')>
        <a onclick="openMenuItem('accAlarmMonitor.do','Acc')"><@i18n 'acc_rtMonitor_alarmMonitor' /></a>
        <#else>
        <span class="noPermissionTip warningColor"><@i18n 'common_no_authority_access'/></span>
        </#if>
    </div>

    <div class="guide-item-desc acc-guide-desc"><@i18n 'acc_guide_rtMonitorTip2'/></div>
    <div class="guide-item-menu">
        <#if hasPermission('acc:map')>
        <a onclick="openMenuItem('accMap.do','Acc')"><@i18n 'acc_leftMenu_electronicMap' /></a>
        <#else>
        <span class="noPermissionTip warningColor"><@i18n 'common_no_authority_access'/></span>
        </#if>
    </div>
</#macro>
<!-- whereContent: where区域内容 -->
<!-- howContent: how区域内容 -->
<!-- otherContent: other区域内容 -->
<style>
    .acc-guide-desc {
        padding-top : 20px;
    }
</style>