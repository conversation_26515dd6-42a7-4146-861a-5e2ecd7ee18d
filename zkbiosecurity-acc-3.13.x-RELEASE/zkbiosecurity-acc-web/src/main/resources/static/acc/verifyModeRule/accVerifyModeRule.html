<#assign gridName="accVerifyModeRuleGrid${uuid!}">
<script type="text/javascript">
    /*function leftGridClick(id,ind){
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        var leftGrid = dbGrid.leftGrid;
        var rightGrid = dbGrid.rightGrid;
        rightGrid.reload(null, {"accVerifyModeRuleId":id});
    }*/

    function accVerifyModeRuleLeftGridClick(rid) {
        //此方法注册的是官方事件，所以this指代的是官方grid对象，通过访问属性zkgrid获取我们自定义的grid对象
        var leftGrid = this.zkgrid;//获取左表格
        var dbGrid = ZKUI.DGrid.get(leftGrid.options.dGridName);//双列表对象
        var rightGrid = dbGrid.rightGrid;//右表格对象
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        rightGrid.reload(function() {
            rightCallback(row.id, rightGrid, rid);//手动回调
        },{accVerifyModeRuleId:rid});//传递id到右表格查询
    }

    //右表格回调事件
    function rightCallback(id, rightGrid, rid) {
        var dbGrid = ZKUI.DGrid.get(rightGrid.options.dGridName);//双列表对象
        var leftGrid = dbGrid.leftGrid;//获取左表格
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        // rightGrid.setTitle("已选部门:"+row.name);//设置右表格标题
    }

    function afterAddVerifyModeRuleDoor(value, text, event) {
        var verifyModeRuleId = this.options.verifyModeRuleId;
        var verifyModeRuleName = this.options.verifyModeRuleName;
        onLoading(function(){
            $.ajax({
                url:"/accVerifyModeRule.do?addDoor",
                type:"post",
                dataType:"json",
                data:{
                    "verifyModeRuleId" : verifyModeRuleId,
                    "doorIds" : value,
                    "verifyModeRuleName" : verifyModeRuleName,
                    "doorNames" : text
                },
                success:function (result) {
                    openMessage(msgType.success);
                    var dbGrid = ZKUI.DGrid.get("${gridName}");
                    dbGrid.rightGrid.reload();
                }
            });
        });
    }

   /* function delVerifyModeRuleDoor(){
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        var leftGrid = dbGrid.leftGrid;
        var rightGrid = dbGrid.rightGrid;
        var leftGridName = leftGrid.gridName || "gridbox";
        var rightGridName = rightGrid.gridName || "gridbox";
        var leftIds = ZKUI.Grid.GRID_PULL[leftGridName].grid.getSelectedRowId();
        var rightIds = ZKUI.Grid.GRID_PULL[rightGridName].grid.getCheckedRows(0);
        if(leftIds=="" || leftIds ==null || rightIds=="" || rightIds==null ){
            messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});
        }else {
            deleteConfirm(function(result){
                if(result){
                    $.ajax({
                        url:"/accVerifyModeRule.do?delDoor",
                        type:"post",
                        dataType:"json",
                        data:{
                            "verifyModeRuleId" : leftIds,
                            "doorIds" :rightIds
                        },
                        success:function (result) {
                            openMessage(msgType.success);
                            var dbGrid = ZKUI.DGrid.get("${gridName}");
                            var leftGrid = dbGrid.leftGrid;
                            var rightGrid = dbGrid.rightGrid;
                            leftGrid.reload();
                            rightGrid.reload();
                        }
                    });
                }
            },"common_prompt_sureToDelThese");
        }
    }*/
</script>
<@ZKUI.DGrid gridName="${gridName}">
	<@ZKUI.LeftGrid title="acc_leftMenu_verifyModeRule">
		<@ZKUI.Searchbar>
			<@ZKUI.SearchTop>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="name" maxlength="30" title="acc_common_ruleName" type="text"/>
					</td>
				</tr>
			</@ZKUI.SearchTop>
		</@ZKUI.Searchbar>
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="acc:verifyModeRule:refresh"/>
			<@ZKUI.ToolItem id="/accVerifyModeRule.do?edit" text="common_op_new" width="1400" height="550" img="comm_add.png" action="commonAdd" permission="acc:verifyModeRule:add"/>
			<@ZKUI.ToolItem id="/accVerifyModeRule.do?del&names=(name)" text="common_op_del" img="comm_del.png" action="accDGridCommonDel" permission="acc:verifyModeRule:del"/>
		</@ZKUI.Toolbar>
		<!-- 配置左表格选中事件 -->
		<@ZKUI.Grid onRowSelect="accVerifyModeRuleLeftGridClick" vo="com.zkteco.zkbiosecurity.acc.vo.AccVerifyModeRuleItem" query="/accVerifyModeRule.do?list" autoFirst="true"/>
	</@ZKUI.LeftGrid>
	<@ZKUI.RightGrid title="acc_leftMenu_door" leftFieldName="accVerifyModeRuleId">
		<@ZKUI.Searchbar>
			<@ZKUI.SearchTop>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="doorName" maxlength="30" title="acc_door_name" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Input name="deviceAlias" maxlength="20" title="common_ownedDev" type="text"/>
					</td>
				</tr>
			</@ZKUI.SearchTop>
		</@ZKUI.Searchbar>
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem type="refresh" permission="acc:verifyModeRule:refresh"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="/accVerifyModeRule.do?delDoor&verifyModeRuleId=(@id:gs)&verifyModeRuleName=(@name:gs)&doorIds=(id)&doorNames=(doorName)" action="accRightGridCommonDel" text="acc_level_doorDelete" img="comm_del.png" permission="acc:verifyModeRule:delDoor"/>
		</@ZKUI.Toolbar>
		<@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccDoorVerifyModeRuleItem" query="/accVerifyModeRule.do?doorList"/>
	</@ZKUI.RightGrid>
</@ZKUI.DGrid>
