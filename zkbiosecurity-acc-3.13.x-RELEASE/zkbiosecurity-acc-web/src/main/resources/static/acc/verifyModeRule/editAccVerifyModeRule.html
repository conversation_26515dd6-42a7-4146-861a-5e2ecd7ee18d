<#assign formId = "${uuid!}">
<#include "/public/template/editTemplate.html">
<#macro editContent>
<style type="text/css">

	.accVerifyModeRule_edit_table_inner {
		text-align: center;
		width: 100%;
		background-color: #FFFFFF;
	}

	.accVerifyModeRule_edit_table_inner input[type="text"] {
		width: 40px !important;
		height: 18px !important;
		line-height: 18px !important;
		padding: 0px !important;
	}

	.accVerifyModeRule_edit_table_inner .dhxcombo_dhx_web {
		text-align: left!important;
	}

	.accVerifyModeRule_edit_table_inner .search-label {
		padding: 0;
	}

	.accVerifyModeRule_edit_table_inner .search-combo-box input:first-child {
		width: 80px!important;
	}
</style>
<form action='/accVerifyModeRule.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
	<input type="hidden" value="${(item.id)!}" name="id" id="id_model_pk">
    <input type="hidden" value="${(item.timeSegId)!}" name="tSegId" id="timeSegId">
	<table class="tableStyle" style="border-bottom: none">
		<tr>
			<th>
				<label><@i18n 'acc_verifyRule_name'/></label><span class="required">*</span>
			</th>
			<td>
				<input type="text" id="ruleName" name="name" value="${(item.name)!}" maxlength="20">
			</td>
			<th>
				<label><@i18n 'common_leftMenu_timeZone'/></label><span class="required">*</span>
			</th>
			<td>
				<@ZKUI.Combo empty="true" width="148" id="accVerifyModeRuleTimeSeg" name="timeSegId" value="${(item.timeSegId)!}" path="accVerifyModeRule.do?getTimeSegListWithoutRule&id=${(item.timeSegId)!}" onChange="loadTimeSeg">
				</@ZKUI.Combo>
			</td>
			</tr>
				<th>
					<label><@i18n 'common_verifyMode_entiy'/></label>
				</th>
				<td>
					<div style="display: flex;">
						<div><@ZKUI.Input hideLabel="true" type="radio" name="newVerifyMode" id="verifyMode_old" value="0" checked="checked"/><@i18n 'acc_verifyRule_oldVerifyMode'/></div>
						<div>
							<#if (item.newVerifyMode)?exists && item.newVerifyMode=="1">
								<@ZKUI.Input hideLabel="true" type="radio" id="verifyMode_new" name="newVerifyMode" value="1" checked="checked"/>
							<#else>
								<@ZKUI.Input hideLabel="true" type="radio" id="verifyMode_new" name="newVerifyMode" value="1"/>
							</#if>
						<@i18n 'acc_verifyRule_newVerifyMode'/>
						</div>
					</div>
				</td>
			</tr>
	</table>

	<!-- 为了减少验证时间，点击提交后移植表单中 -->
	<div id="inputValueDiv" class="displayN">
		<#assign dateValue = ["${(tempAccTimeSeg.mondayStart1)!}","${(tempAccTimeSeg.mondayEnd1)!}","${(tempAccTimeSeg.mondayStart2)!}","${(tempAccTimeSeg.mondayEnd2)!}","${(tempAccTimeSeg.mondayStart3)!}","${(tempAccTimeSeg.mondayEnd3)!}",
		"${(tempAccTimeSeg.tuesdayStart1)!}","${(tempAccTimeSeg.tuesdayEnd1)!}","${(tempAccTimeSeg.tuesdayStart2)!}","${(tempAccTimeSeg.tuesdayEnd2)!}","${(tempAccTimeSeg.tuesdayStart3)!}","${(tempAccTimeSeg.tuesdayEnd3)!}",
		"${(tempAccTimeSeg.wednesdayStart1)!}","${(tempAccTimeSeg.wednesdayEnd1)!}","${(tempAccTimeSeg.wednesdayStart2)!}","${(tempAccTimeSeg.wednesdayEnd2)!}","${(tempAccTimeSeg.wednesdayStart3)!}","${(tempAccTimeSeg.wednesdayEnd3)!}",
		"${(tempAccTimeSeg.thursdayStart1)!}","${(tempAccTimeSeg.thursdayEnd1)!}","${(tempAccTimeSeg.thursdayStart2)!}","${(tempAccTimeSeg.thursdayEnd2)!}","${(tempAccTimeSeg.thursdayStart3)!}","${(tempAccTimeSeg.thursdayEnd3)!}",
		"${(tempAccTimeSeg.fridayStart1)!}","${(tempAccTimeSeg.fridayEnd1)!}","${(tempAccTimeSeg.fridayStart2)!}","${(tempAccTimeSeg.fridayEnd2)!}","${(tempAccTimeSeg.fridayStart3)!}","${(tempAccTimeSeg.fridayEnd3)!}",
		"${(tempAccTimeSeg.saturdayStart1)!}","${(tempAccTimeSeg.saturdayEnd1)!}","${(tempAccTimeSeg.saturdayStart2)!}","${(tempAccTimeSeg.saturdayEnd2)!}","${(tempAccTimeSeg.saturdayStart3)!}","${(tempAccTimeSeg.saturdayEnd3)!}",
		"${(tempAccTimeSeg.sundayStart1)!}","${(tempAccTimeSeg.sundayEnd1)!}","${(tempAccTimeSeg.sundayStart2)!}","${(tempAccTimeSeg.sundayEnd2)!}","${(tempAccTimeSeg.sundayStart3)!}","${(tempAccTimeSeg.sundayEnd3)!}",
		"${(tempAccTimeSeg.holidayType1Start1)!}","${(tempAccTimeSeg.holidayType1End1)!}","${(tempAccTimeSeg.holidayType1Start2)!}","${(tempAccTimeSeg.holidayType1End2)!}","${(tempAccTimeSeg.holidayType1Start3)!}","${(tempAccTimeSeg.holidayType1End3)!}",
		"${(tempAccTimeSeg.holidayType2Start1)!}","${(tempAccTimeSeg.holidayType2End1)!}","${(tempAccTimeSeg.holidayType2Start2)!}","${(tempAccTimeSeg.holidayType2End2)!}","${(tempAccTimeSeg.holidayType2Start3)!}","${(tempAccTimeSeg.holidayType2End3)!}",
		"${(tempAccTimeSeg.holidayType3Start1)!}","${(tempAccTimeSeg.holidayType3End1)!}","${(tempAccTimeSeg.holidayType3Start2)!}","${(tempAccTimeSeg.holidayType3End2)!}","${(tempAccTimeSeg.holidayType3Start3)!}","${(tempAccTimeSeg.holidayType3End3)!}"]>
		<#assign dateNames = ["monday","tuesday","wednesday","thursday","friday","saturday","sunday","holidayType1","holidayType2","holidayType3"]>
		<#list dateNames as dateName>
		<input type="text" size="5" value="${dateValue[dateName_index*6]}" id="id_${dateName_index}_start1" maxlength="8">
		<input type="text" size="5" value="${dateValue[dateName_index*6+1]}" id="id_${dateName_index}_end1" maxlength="8">
		<input type="text" size="5" value="${dateValue[dateName_index*6+2]}" id="id_${dateName_index}_start2" maxlength="8">
		<input type="text" size="5" value="${dateValue[dateName_index*6+3]}" id="id_${dateName_index}_end2" maxlength="8">
		<input type="text" size="5" value="${dateValue[dateName_index*6+4]}" id="id_${dateName_index}_start3" maxlength="8">
		<input type="text" size="5" value="${dateValue[dateName_index*6+5]}" id="id_${dateName_index}_end3" maxlength="8">
	</#list>
	</div>

	<div class="timeseg_edit_div_inner">
		<table class="accVerifyModeRule_edit_table_inner">
			<tr>
				<td colspan="1" class="blank_bgcolor" id="id_blank1" rowspan="2">
					<div class="out">
						<b><@i18n 'common_time'/></b>
						<em><@i18n 'common_date'/></em>
					</div>
				</td>
				<td colspan="4" class="blue_bgcolor">
					<@i18n 'common_timeSeg_interval1'/>
				</td>
				<td colspan="4" class="blue_bgcolor">
					<@i18n 'common_timeSeg_interval2'/>
				</td>
				<td colspan="4" class="blue_bgcolor">
					<@i18n 'common_timeSeg_interval3'/>
				</td>
			</tr>
			<tr>
				<td class="blue_bgcolor">
					<@i18n 'common_timeSeg_startTime'/>
				</td>
				<td class="blue_bgcolor">
					<@i18n 'common_timeSeg_endTime'/>
				</td>
				<td class="blue_bgcolor">
					<@i18n 'acc_verifyRule_door'/>
				</td>
				<td class="blue_bgcolor">
					<@i18n 'acc_verifyRule_person'/>
				</td>
				<td class="blue_bgcolor">
					<@i18n 'common_timeSeg_startTime'/>
				</td>
				<td class="blue_bgcolor">
					<@i18n 'common_timeSeg_endTime'/>
				</td>
				<td class="blue_bgcolor">
					<@i18n 'acc_verifyRule_door'/>
				</td>
				<td class="blue_bgcolor">
					<@i18n 'acc_verifyRule_person'/>
				</td>
				<td class="blue_bgcolor">
					<@i18n 'common_timeSeg_startTime'/>
				</td>
				<td class="blue_bgcolor">
					<@i18n 'common_timeSeg_endTime'/>
				</td>
				<td class="blue_bgcolor">
					<@i18n 'acc_verifyRule_door'/>
				</td>
				<td class="blue_bgcolor">
					<@i18n 'acc_verifyRule_person'/>
				</td>
			</tr>
			<#assign date = ["common_monday", "common_tuesday", "common_wednesday", "common_thursday","common_friday","common_saturday","common_sunday","common_holiday_type1","common_holiday_type2","common_holiday_type3"]>
			<#assign dateNames = ["monday","tuesday","wednesday","thursday","friday","saturday","sunday","holidayType1","holidayType2","holidayType3"]>
			<#list date as i>
			<tr>
				<td class="blue_bgcolor">
					<@i18n i/>
				</td>
				<td>
					<div id=${i_index}_start1 style="direction: ltr;"></div>
				</td>

				<td>
					<div id=${i_index}_end1 style="direction: ltr;"></div>
				</td>

				<td>
					<div id=${i_index}_1VSDoor>
						<div id="${i_index}_1VSDoor_old" style="<#if newVerifyMode?exists&&newVerifyMode=='1'>display:none;</#if>">
							<@ZKUI.Combo empty="false" width="95" id="${i_index}_Time1VSDoorSelect" name="${dateNames[i_index]}Time1VSDoor">
							</@ZKUI.Combo>
					</div>
						<div id="${i_index}_1VSDoor_new" style="width:95px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;text-align:center;<#if newVerifyMode?exists&&newVerifyMode=='0'>display:none;</#if>">
							<a id="${i_index}_1DoorSelect" href="javascript:void(0)" onclick="openSelectNVSWindow(this,'${i_index}_1NVSDoor')"><@i18n 'common_select'/></a>
							<input id="${i_index}_1NVSDoor" type="hidden" name="${dateNames[i_index]}Time1VSDoor" value="0"/>
						</div>
					</div>
				</td>
				<td>
					<div id=${i_index}_1VSPerson>
						<div id="${i_index}_1VSPerson_old" style="<#if newVerifyMode?exists&&newVerifyMode=='1'>display:none;</#if>">
							<@ZKUI.Combo empty="false" width="95" id="${i_index}_Time1VSPersonSelect" name="${dateNames[i_index]}Time1VSPerson">
							</@ZKUI.Combo>
						</div>
						<div id="${i_index}_1VSPerson_new" style="width:95px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;text-align:center;<#if newVerifyMode?exists&&newVerifyMode=='0'>display:none;</#if>">
							<a id="${i_index}_1PersonSelect" href="javascript:void(0)" onclick="openSelectNVSWindow(this,'${i_index}_1NVSPerson')"><@i18n 'common_select'/></a>
							<input id="${i_index}_1NVSPerson" type="hidden" name="${dateNames[i_index]}Time1VSPerson" value="0" />
						</div>
					</div>
				</td>

				<td>
					<div id=${i_index}_start2 style="direction: ltr;"></div>
				</td>

				<td>
					<div id=${i_index}_end2 style="direction: ltr;"></div>
				</td>

				<td>
					<div id=${i_index}_2VSDoor>
						<div id="${i_index}_2VSDoor_old" style="<#if newVerifyMode?exists&&newVerifyMode=='1'>display:none;</#if>">
							<@ZKUI.Combo empty="false" width="95" id="${i_index}_Time2VSDoorSelect" name="${dateNames[i_index]}Time2VSDoor">
							</@ZKUI.Combo>
						</div>
						<div id="${i_index}_2VSDoor_new" style="width:95px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;text-align:center;<#if newVerifyMode?exists&&newVerifyMode=='0'>display:none;</#if>">
							<a id="${i_index}_2DoorSelect" href="javascript:void(0)" onclick="openSelectNVSWindow(this,'${i_index}_2NVSDoor')"><@i18n 'common_select'/></a>
							<input id="${i_index}_2NVSDoor" type="hidden" name="${dateNames[i_index]}Time2VSDoor" value="0" />
						</div>
					</div>
				</td>
				<td>
					<div id=${i_index}_2VSPerson>
						<div id="${i_index}_2VSPerson_old" style="<#if newVerifyMode?exists&&newVerifyMode=='1'>display:none;</#if>">
							<@ZKUI.Combo empty="false" width="95" id="${i_index}_Time2VSPersonSelect" name="${dateNames[i_index]}Time2VSPerson">
							</@ZKUI.Combo>
						</div>
						<div id="${i_index}_2VSPerson_new" style="width:95px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;text-align:center;<#if newVerifyMode?exists&&newVerifyMode=='0'>display:none;</#if>">
							<a id="${i_index}_2PersonSelect" href="javascript:void(0)" onclick="openSelectNVSWindow(this,'${i_index}_2NVSPerson')"><@i18n 'common_select'/></a>
							<input id="${i_index}_2NVSPerson" type="hidden" name="${dateNames[i_index]}Time2VSPerson" value="0" />
						</div>
					</div>
				</td>

				<td>
					<div id=${i_index}_start3 style="direction: ltr;"></div>
				</td>

				<td>
					<div id=${i_index}_end3 style="direction: ltr;"></div>
				</td>

				<td>
					<div id=${i_index}_3VSDoor>
						<div id="${i_index}_3VSDoor_old" style="<#if newVerifyMode?exists&&newVerifyMode=='1'>display:none;</#if>">
							<@ZKUI.Combo empty="false" width="95" id="${i_index}_Time3VSDoorSelect" name="${dateNames[i_index]}Time3VSDoor">
							</@ZKUI.Combo>
						</div>
						<div id="${i_index}_3VSDoor_new" style="width:95px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;text-align:center;<#if newVerifyMode?exists&&newVerifyMode=='0'>display:none;</#if>">
							<a id="${i_index}_3DoorSelect" href="javascript:void(0)" onclick="openSelectNVSWindow(this,'${i_index}_3NVSDoor')"><@i18n 'common_select'/></a>
							<input id="${i_index}_3NVSDoor" type="hidden" name="${dateNames[i_index]}Time3VSDoor" value="0" />
						</div>
					</div>
				</td>
				<td>
					<div id=${i_index}_3VSPerson>
						<div id="${i_index}_3VSPerson_old" style="<#if newVerifyMode?exists&&newVerifyMode=='1'>display:none;</#if>">
							<@ZKUI.Combo empty="false" width="95" id="${i_index}_Time3VSPersonSelect" name="${dateNames[i_index]}Time3VSPerson">
							</@ZKUI.Combo>
						</div>
						<div id="${i_index}_3VSPerson_new" style="width:95px;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;text-align:center;<#if newVerifyMode?exists&&newVerifyMode=='0'>display:none;</#if>">
							<a id="${i_index}_3PersonSelect" href="javascript:void(0)" onclick="openSelectNVSWindow(this,'${i_index}_3NVSPerson')"><@i18n 'common_select'/></a>
							<input id="${i_index}_3NVSPerson" type="hidden" name="${dateNames[i_index]}Time3VSPerson" value="0" />
						</div>
					</div>
				</td>
			</tr>
		</#list>
		</table>
		<table class="timeseg_edit_table">
			<tr>
				<td style="padding-top:15px;">
					<label><@i18n 'acc_verifyRule_copy'/></label>
					<@ZKUI.Input type="checkbox" name="copyDate" id="idCopyDate" />
				</td>
			</tr>
		</table>
	</div>
</form>
<div style="margin-top: 10px; margin-left: 15px;">
	<span class="warningImage"></span>
	<span class="warningColor"><@i18n 'acc_verifyRule_tip3'/></span>
</div>
<script type="text/javascript">
    (function() {
        // 字母和数字的验证
        $("#${formId}").validate( {
            debug : true,
            rules :
			{
				"name" :
				{
					required : true,
					overRemote : ["/accVerifyModeRule.do?valid", "${(item.name)!}"]
				},
				"timeSegId" :
				{
					required : true
				}
			},
            submitHandler : function(){
            	var flag = false;
				var isNewVerifyMode = getIsNewVerifyMode();
				if (isNewVerifyMode) {
					// 处理新验证方式转为十进制提交保存
					$("input[id$='NVSDoor']").each(function(index){
						if ($(this).val() != "0") {
							flag = true;
							convertToNewVerifyModeDecimal($(this));
						}
					});
					$("input[id$='NVSPerson']").each(function(index){
						if ($(this).val() != "0") {
							flag = true;
							convertToNewVerifyModeDecimal($(this));
						}
					});
					$("div[id$='VSDoor_old']").remove();
					$("div[id$='VSPerson_old']").remove();
				} else {
					$("div[id$='VSDoor_old']").children().each(function(){
						var id = $(this).children()[1].id;
						if (ZKUI.Combo.get(id).combo.getSelectedValue() != 255)
						{
							flag = true;
						}
					});
					$("div[id$='VSPerson_old']").children().each(function(){
						var id = $(this).children()[1].id;
						if (ZKUI.Combo.get(id).combo.getSelectedValue() != 255)
						{
							flag = true;
						}
					});
				}
				if (!flag)
				{
					messageBox({
						messageType : "alert",
						text : "<@i18n 'acc_verifyRule_tip1'/>"
					});
					return;
				}

            	<@submitHandler />
            }
        });
    })();

    var time_inputs = {};
    $("div[id*='_start'], div[id*='_end']").each(function() {
        var div_id = $(this).attr("id");
        var tzb = new TimeZoneBox(div_id + "_span", div_id);
        //time_inputs.push(tzb);//span便于区分
        time_inputs[div_id + "_span"]=tzb;
        tzb.setEnable(true);
    });

    //合并两个单元格
    $("#${formId} #id_blank1").attr("rowspan", "2");
    $("#${formId} #id_blank2").remove();

    //初始化
    var time_val = "";
    for (t in time_inputs)
    {
        time_val = $("#${formId} #id_" + time_inputs[t].id.replace("_span", "")).val();
        if (time_val == "")
        {
            time_val = "00:00";
        }
        else
        {
            time_val = time_val.substring(0, 2) + ":" + time_val.substring(3, 5);
        }
        time_inputs[t].setValue(time_val);
    }

    var week_date = ["0", "1", "2", "3", "4"];//"5", "6"
    var door_person = ["1VSDoor_old", "1VSPerson_old", "2VSDoor_old", "2VSPerson_old", "3VSDoor_old", "3VSPerson_old"];
    var val_array = new Array();// 每个区间开始、结束的时间
    var tempVal = "";// 临时变量
    $("#${formId} #idCopyDate").click(function(){
    	var isNewVerifyMode = getIsNewVerifyMode();
    	var isCopyDateChecked = $(this).attr("checked");
        if(isCopyDateChecked)
        {
        	if (isNewVerifyMode) {
        		copyNewVerifyMode(isCopyDateChecked);
        	} else {
				val_array = new Array();
				for(var y = 0; y < door_person.length; y++)
				{
					// tempVal = $("#${formId} #"+week_date[0]+"_"+door_person[y]).children().val();// 只以星期一为模板copy
					var id = $("#${formId} #"+week_date[0]+"_"+door_person[y]).children().children()[1].id;
					var tempVal = ZKUI.Combo.get(id).combo.getSelectedValue();
					val_array.push(tempVal == "" ? "255" : tempVal);
				}
				copyDate(val_array);
        	}

        }
        else
        {
        	if (isNewVerifyMode) {
        		copyNewVerifyMode(isCopyDateChecked);
        	} else {
        		val_array = new Array();
				for(var y = 0; y < door_person.length; y++)
				{
					val_array.push("255");
				}
				copyDate(val_array);
        	}

        }
    });

    function copyDate(val_array)// 复制、粘贴数据
    {
        var i = -1;
        for(var x = 1; x < week_date.length; x++)
        {
            for(var y = 0; y < door_person.length; y++)
            {
                i++;
                // $("#${formId} #"+week_date[x]+"_"+door_person[y]).children().val(val_array[i]);
                var id = $("#${formId} #"+week_date[x]+"_"+door_person[y]).children().children()[1].id;
				var tempVal = ZKUI.Combo.get(id).combo.setComboValue(val_array[i]);
                i = i == 5 ? -1 : i;
            }
        }
    }

    /** 复制新验证方式 */
    function copyNewVerifyMode(isCopyDateChecked) {
    	var door_person_select = ["1DoorSelect", "1PersonSelect", "2DoorSelect", "2PersonSelect", "3DoorSelect", "3PersonSelect"];
		var door_person_nvs = ["1NVSDoor", "1NVSPerson", "2NVSDoor", "2NVSPerson", "3NVSDoor", "3NVSPerson"]
    	if (isCopyDateChecked) {
			for (var x = 1; x < week_date.length; x++) {
				for (var y = 0; y < door_person_select.length; y++) {
					var text = $("#${formId} #" + "0_" + door_person_select[y]).text();
					var val = $("#${formId} #" + "0_" + door_person_nvs[y]).val();
					$("#${formId} #" + week_date[x] + "_" + door_person_select[y]).text(text);
					$("#${formId} #" + week_date[x] + "_" + door_person_nvs[y]).val(val);
				}
			}
    	} else {
			for (var x = 1; x < week_date.length; x++) {
				for (var y = 0; y < door_person_select.length; y++) {
					$("#${formId} #" + week_date[x] + "_" + door_person_select[y]).text(I18n.getValue("common_select"));
					$("#${formId} #" + week_date[x] + "_" + door_person_nvs[y]).val("0");
				}
			}
    	}

    }

    function loadTimeSeg()
    {
        var d = ["monday","tuesday","wednesday","thursday","friday","saturday","sunday","holidayType1","holidayType2","holidayType3"],dl = d.length;
        var a = ["Start1","End1","Start2","End2","Start3","End3"],al=a.length;
        var selectedTimeSegId = ZKUI.Combo.get("accVerifyModeRuleTimeSeg").combo.getSelectedValue();
        if (selectedTimeSegId) {
            $.ajax({
                type: "POST",
                url: "accVerifyModeRule.do?getTimeSegById&id=" + selectedTimeSegId,
                dataType: "json",
                async: false,
                success: function(result)
                {
                    var dataArray = eval(result.data);
                    for (var i = 0; i < dl; i++)
                    {
                        for (var j = 0; j < al; j++)
                        {
                            var time_val = dataArray[d[i]+a[j]];
                            var id = i +"_"+a[j].toLowerCase()+"_span";
                            if(time_inputs[id] != undefined)
                            {
                                time_inputs[id].setValue(time_val);
                            }
                        }
                    }
                }
            });
        }
        else
        {
            for (var i = 0; i < dl; i++)
            {
                for (var j = 0; j < al; j++)
                {
                    var time_val = "00:00";
                    var id = i +"_"+a[j].toLowerCase()+"_span";
                    if(time_inputs[id] != undefined)
                    {
                        time_inputs[id].setValue(time_val);
                    }
                }
            }
        }
    }
    
    
    function editLoadTimeSeg()
    {
        var d = ["monday","tuesday","wednesday","thursday","friday","saturday","sunday","holidayType1","holidayType2","holidayType3"],dl = d.length;
        var a = ["Start1","End1","Start2","End2","Start3","End3"],al=a.length;
        var selectedTimeSegId = $("#timeSegId").val();
        if (selectedTimeSegId) {
            $.ajax({
                type: "POST",
                url: "accVerifyModeRule.do?getTimeSegById&id=" + selectedTimeSegId,
                dataType: "json",
                async: false,
                success: function(result)
                {
                    var dataArray = eval(result.data);
                    for (var i = 0; i < dl; i++)
                    {
                        for (var j = 0; j < al; j++)
                        {
                            var time_val = dataArray[d[i]+a[j]];
                            var id = i +"_"+a[j].toLowerCase()+"_span";
                            if(time_inputs[id] != undefined)
                            {
                                time_inputs[id].setValue(time_val);
                            }
                        }
                    }
                }
            });
        }
        else
        {
            for (var i = 0; i < dl; i++)
            {
                for (var j = 0; j < al; j++)
                {
                    var time_val = "00:00";
                    var id = i +"_"+a[j].toLowerCase()+"_span";
                    if(time_inputs[id] != undefined)
                    {
                        time_inputs[id].setValue(time_val);
                    }
                }
            }
        }
    }

    var verifyValue = ["${(item.mondayTime1VSDoor)!255}","${(item.mondayTime1VSPerson)!255}","${(item.mondayTime2VSDoor)!255}","${(item.mondayTime2VSPerson)!255}","${(item.mondayTime3VSDoor)!255}","${(item.mondayTime3VSPerson)!255}",
        "${(item.tuesdayTime1VSDoor)!255}","${(item.tuesdayTime1VSPerson)!255}","${(item.tuesdayTime2VSDoor)!255}","${(item.tuesdayTime2VSPerson)!255}","${(item.tuesdayTime3VSDoor)!255}","${(item.tuesdayTime3VSPerson)!255}",
        "${(item.wednesdayTime1VSDoor)!255}","${(item.wednesdayTime1VSPerson)!255}","${(item.wednesdayTime2VSDoor)!255}","${(item.wednesdayTime2VSPerson)!255}","${(item.wednesdayTime3VSDoor)!255}","${(item.wednesdayTime3VSPerson)!255}",
        "${(item.thursdayTime1VSDoor)!255}","${(item.thursdayTime1VSPerson)!255}","${(item.thursdayTime2VSDoor)!255}","${(item.thursdayTime2VSPerson)!255}","${(item.thursdayTime3VSDoor)!255}","${(item.thursdayTime3VSPerson)!255}",
        "${(item.fridayTime1VSDoor)!255}","${(item.fridayTime1VSPerson)!255}","${(item.fridayTime2VSDoor)!255}","${(item.fridayTime2VSPerson)!255}","${(item.fridayTime3VSDoor)!255}","${(item.fridayTime3VSPerson)!255}",
        "${(item.saturdayTime1VSDoor)!255}","${(item.saturdayTime1VSPerson)!255}","${(item.saturdayTime2VSDoor)!255}","${(item.saturdayTime2VSPerson)!255}","${(item.saturdayTime3VSDoor)!255}","${(item.saturdayTime3VSPerson)!255}",
        "${(item.sundayTime1VSDoor)!255}","${(item.sundayTime1VSPerson)!255}","${(item.sundayTime2VSDoor)!255}","${(item.sundayTime2VSPerson)!255}","${(item.sundayTime3VSDoor)!255}","${(item.sundayTime3VSPerson)!255}",
        "${(item.holidayType1Time1VSDoor)!255}","${(item.holidayType1Time1VSPerson)!255}","${(item.holidayType1Time2VSDoor)!255}","${(item.holidayType1Time2VSPerson)!255}","${(item.holidayType1Time3VSDoor)!255}","${(item.holidayType1Time3VSPerson)!255}",
        "${(item.holidayType2Time1VSDoor)!255}","${(item.holidayType2Time1VSPerson)!255}","${(item.holidayType2Time2VSDoor)!255}","${(item.holidayType2Time2VSPerson)!255}","${(item.holidayType2Time3VSDoor)!255}","${(item.holidayType2Time3VSPerson)!255}",
        "${(item.holidayType3Time1VSDoor)!255}","${(item.holidayType3Time1VSPerson)!255}","${(item.holidayType3Time2VSDoor)!255}","${(item.holidayType3Time2VSPerson)!255}","${(item.holidayType3Time3VSDoor)!255}","${(item.holidayType3Time3VSPerson)!255}"];

	// 新验证方式map
	var newVerifyModeMap;
	// 支持新验证方式设备数
    var newVerifyModeLength = 0;
    function loadVerifyMode() {
    	$.ajax({
			type : "POST",
			url : "/accVerifyModeRule.do?getVerifyMode",
			dataType : "json",
			async : false,
			success : function(result)
			{
				newVerifyModeMap = new Map(Object.entries(result.data.newVerifyModeMap));
				newVerifyModeLength = result.data.supportNewVerifyModeLength;

				var doorVerifyMode = result.data.doorVerifyMode;
				var optionHtml = "{text:'-----------',value:'255'},";
				for (var i = 0; i < doorVerifyMode.length; i++)
				{
					optionHtml += "{text:'" + doorVerifyMode[i].name + "',value:'" + doorVerifyMode[i].verifyNo + "'},";
				}
				optionHtml = optionHtml.substring(0, optionHtml.length - 1);
				optionHtml = "[" + optionHtml + "]";
				optionHtml = eval('(' + optionHtml + ')');
				$("div[id$='VSDoor_old']").children().each(function(index) {
					ZKUI.Combo.get($(this).children()[1].id).combo.addOption(optionHtml);
					ZKUI.Combo.get($(this).children()[1].id).combo.setComboValue(verifyValue[index*2]);
				});

				var personVerifyMode = result.data.doorVerifyMode;
				var personOptionHtml = "{text:'-----------',value:'255'},";
				for (var i = 0; i < personVerifyMode.length; i++)
				{
					personOptionHtml += "{text:'" + personVerifyMode[i].name + "',value:'" + personVerifyMode[i].verifyNo + "'},";
				}
				personOptionHtml = personOptionHtml.substring(0, personOptionHtml.length - 1);
				personOptionHtml = "[" + personOptionHtml + "]";
				personOptionHtml = eval('(' + personOptionHtml + ')');
				$("div[id$='VSPerson_old']").children().each(function(index){
					ZKUI.Combo.get($(this).children()[1].id).combo.addOption(personOptionHtml);
					ZKUI.Combo.get($(this).children()[1].id).combo.setComboValue(verifyValue[index*2+1]);
				});
			},
			error : function(XMLHttpRequest, textStatus, errorThrown)
			{
				messageBox({
					messageType : "alert",
					text : "<@i18n 'common_prompt_serverError'/>" + "-615"
				});
			}
		});
    }

    // 加载验证方式
	loadVerifyMode();

    window.setTimeout(function(){
        editLoadTimeSeg();//第一次加载运行
    }, 100);

	/** 修改验证方式 */
    $("input[name='newVerifyMode']").change(function(){
    	// 为旧验证方式
        if ($(this).val() == "0") {
        	// 隐藏新验证方式
        	$("div[id$='VSDoor_new']").each(function(index){
        		$(this).hide();
        	});
        	$("div[id$='VSPerson_new']").each(function(index){
        		$(this).hide();
        	});
        	// 显示旧验证方式
        	$("div[id$='VSDoor_old']").each(function(index){
        		$(this).show();
        	});
        	$("div[id$='VSPerson_old']").each(function(index){
        		$(this).show();
        	});

        }
        else if ($(this).val() == "1") {
        	// 为新验证方式
        	if (newVerifyModeLength > 0) {
        		// 隐藏旧验证方式
				$("div[id$='VSDoor_old']").each(function(index){
					$(this).hide();
				});
				$("div[id$='VSPerson_old']").each(function(index){
					$(this).hide();
				});
				// 显示新验证方式
				$("div[id$='VSDoor_new']").each(function(index){
					$(this).show();
				});
				$("div[id$='VSPerson_new']").each(function(index){
					$(this).show();
				});
        	} else {
        		messageBox({
					messageType : "alert",
					text : "<@i18n 'acc_verifyRule_newVerifyModeNoSupportTip'/>"
				});
				$("#verifyMode_old").prop("checked", true);
				return;
        	}
        }
    });

	// 打开选择新验证方式界面
    function openSelectNVSWindow(selectObj,NVSDoorId) {
    	var selectNVSId = selectObj.id;
    	var verifyModeNos = $("#" + NVSDoorId).val();
		var opts = {
            path: "skip.do?page=acc_verifyModeRule_opAccSelectNewVerifyMode&selectNVSId=" + selectNVSId + "&NVSDoorId=" + NVSDoorId + "&verifyModeNos=" + verifyModeNos,
            width: 480,
            height: 200,
            title: "<@i18n 'acc_verifyRule_newVerifyModeSelectTitle'/>",
            gridName: "gridbox"
        };
        DhxCommon.createWindow(opts);
    }

	/** 支持的新验证方式编号转为十进制数 */
    function convertToNewVerifyModeDecimal(obj) {
    	if (obj.val() == "0") {
    		return;
    	}
    	var newVFStylesArray = [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];
		var newVFStylesLength = newVFStylesArray.length - 1;
		var newVerifyStyleNoArray = obj.val().split(",");
		var verifyStyleLogic = "0";
		// 第一位为0,与关系
		if (newVerifyStyleNoArray[0] == "0") {
			verifyStyleLogic = "1";
		}
		// 因二进制低位到高位由右向左,所以逆序赋值
		newVFStylesArray[newVFStylesLength] = parseInt(verifyStyleLogic);
		for (var i = 0; i < newVerifyStyleNoArray.length; i++) {
			if (newVerifyStyleNoArray[i] != 0) {
				newVFStylesArray[newVFStylesLength-parseInt(newVerifyStyleNoArray[i])] = 1;
			}
		}
		// 将二进制转为十进制数
		var newVerifyMode = parseInt(newVFStylesArray.toString().replace(/,/g, ""),2);
		obj.val(newVerifyMode);
    }

	// 编辑
	if ($("#id_model_pk").val() != "") {
		var isNewVerifyMode = getIsNewVerifyMode();
		if (isNewVerifyMode) {
			$("input[name='newVerifyMode'][value=0]").attr("disabled", true);
			$("a[id$='DoorSelect']").each(function(index) {
				setNewVerifyValue($(this), verifyValue[index*2])
			})
			$("a[id$='PersonSelect']").each(function(index) {
				setNewVerifyValue($(this), verifyValue[index*2+1])
			})
		} else {
			$("input[name='newVerifyMode'][value=1]").attr("disabled", true);
		}
	}

	/** 验证方式-门/人 设置值 */
	function setNewVerifyValue(obj, val) {
		if (val != 255) {
			var binaryArray = decimalToBinary(val, 16);
			var verifyNo = "";
			var verifyText = "";
			var logic = binaryArray[0] == 0 ? "/" : "+"
			for (var i = 0; i < binaryArray.length; i++) {
				if (binaryArray[i] == "1" && newVerifyModeMap && newVerifyModeMap.has(i+"")) {
					verifyNo += i + ",";
					// 逻辑位不添加显示
					if (i > 0) {
						verifyText += I18n.getValue(newVerifyModeMap.get(i+"")) + logic;
					}
				}
			}
			if (verifyNo) {
				verifyNo = verifyNo.substring(0, verifyNo.length - 1);
				obj.next().val(verifyNo);
			}
			if (verifyText) {
				verifyText = verifyText.substring(0, verifyText.length - logic.length);
				obj.text(verifyText);
				obj.attr("title", verifyText);
			}
		}
	}

	// 十进制转二进制
	function decimalToBinary(decimalData, length) {
        var binaryArray = new Array();
        for (var i = 0; i < length; i++) {
            binaryArray[i] = (parseInt(decimalData) >> i & 1);
        }
        return binaryArray;
    }

	// 判断是否为新验证方式
    function getIsNewVerifyMode() {
    	// 默认为false,海外版暂时屏蔽新验证方式选项
    	var isNewVerifyMode = false;
    	var verifyModeObj = $("input[name='newVerifyMode']:checked").val();
    	if (verifyModeObj) {
    		isNewVerifyMode = $("input[name='newVerifyMode']:checked").val() == "0" ? false : true;
    	}
		return isNewVerifyMode;
    }
</script>
</#macro>
