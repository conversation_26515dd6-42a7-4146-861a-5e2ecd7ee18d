<#assign editPage = "true">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
<form action="" method="post" id="${formId}" onkeydown="if(event.keyCode==13){return false;}">
    <table cellpadding="8" class="tableStyle">
        <tr>
            <th><label><@i18n 'common_verifyMode_entiy'/></label><span class="required">*</span></th>
            <td>
                <@ZKUI.Combo id="newVerifyStyleCombo${uuid!}" empty="false" value="0" onChange="changeVerifyMode" hideLabel="true" readonly="true" width="148">
                    <option value="0"><@i18n 'common_verifyMode_cardOrFpOrPwd'/></option>
                    <option value="1"><@i18n 'common_newVerify_mode_manualConfig'/></option>
                </@ZKUI.Combo><br/>
                <div id="newVerifyStyleSetDiv${uuid!}" style="display:none;">
                    <label>
                        <@ZKUI.Input hideLabel="true" name="verifyStyleLogic" type="radio" value="0" checked="checked"/>
                        <span><@i18n 'common_newVerify_mode_logicOr'/></span>
                    </label>
                    <label>
                        <@ZKUI.Input hideLabel="true" name="verifyStyleLogic" type="radio" value="1"/>
                        <span><@i18n 'common_newVerify_mode_logicAnd'/></span>
                    </label><br/>
                    <@ZKUI.ComboTree empty="false" id="newVFStylesTree${uuid!}" value="${verifyModeNos}" hideLabel="true" isTree="true" readonly="true" width="148" url="accDeviceVerifyMode.do?getSupportNewVerifyModeTree"/>
                </div>
            </td>
        </tr>
    </table>
</form>
<script type="">
    $().ready(function(){
        initNewVerifyMode();
    })

    function initNewVerifyMode() {
        // 或关系，如果全选为自动识别
        openMessage(msgType.loading);
        window.setTimeout(function() {
            var verifyNos = $("#" + "${NVSDoorId}").val();
            if (verifyNos && verifyNos != "0") {
                var treeItemsNo = ZKUI.ComboTree.get("newVFStylesTree${uuid}").tree.getAllChildless();
                if (verifyNos != treeItemsNo) {
                    ZKUI.Combo.PULL["newVerifyStyleCombo${uuid}"].combo.setComboValue('1');
                    $("#newVerifyStyleSetDiv${uuid!}").show();
                }
                // 逻辑与关系
                if (verifyNos.split(",")[0] == "0") {
                    $("input[name='verifyStyleLogic'][value=1]").prop("checked", true);
                }
            }
            closeMessage();
        }, 500);
    }

    $("#${formId}").validate({
        debug : true,
        rules :
        {

        },
        submitHandler: function() {
            var newVerifyStyleTexts = ZKUI.ComboTree.get("newVFStylesTree${uuid!}").combo.getComboText().split(",");
            var newVerifyStyleNos = ZKUI.ComboTree.get("newVFStylesTree${uuid!}").combo.getSelectedValue();
            if (newVerifyStyleNos) {
                if (newVerifyStyleNos.split(",")[0] == "0") {
                    newVerifyStyleNos = newVerifyStyleNos.substring(newVerifyStyleNos.indexOf(",") + 1, newVerifyStyleNos.length);
                }
            }
            // 选中的逻辑关系
            var logicChecked = $("input[name='verifyStyleLogic']:checked").val();
            // 默认为或
            var logic = "/";
            var newVerifyModes = "";
            if (logicChecked == "1") {
                // 添加"0",表示0位支持,与关系
                logic = "+";
                newVerifyStyleNos = "0" + "," + newVerifyStyleNos;
            }
            var newVFStyleComboValue = ZKUI.Combo.get("newVerifyStyleCombo${uuid!}").combo.getSelected();
            if (newVFStyleComboValue == "0") {
                // 自动识别设置全部编号和内容
                newVerifyStyleNos = ZKUI.ComboTree.get("newVFStylesTree${uuid!}").tree.getAllChildless();
                var newVerifyStylesNoArray = newVerifyStyleNos.split(",");
                for (var i = 0; i < newVerifyStylesNoArray.length; i++) {
                    newVerifyStyleTexts[i] = ZKUI.ComboTree.get("newVFStylesTree${uuid!}").tree.getItemText(newVerifyStylesNoArray[i]);
                }
            }
            // 组装回填显示的文本信息
            for (var i = 0; i < newVerifyStyleTexts.length; i++) {
                newVerifyModes += newVerifyStyleTexts[i] + logic
            }
            newVerifyModes = newVerifyModes.substring(0,newVerifyModes.length - logic.length);
            if (newVerifyModes == "") {
                newVerifyModes = I18n.getValue("common_select");
                newVerifyStyleNos = "0";
            }
            $("#" + "${selectNVSId}").text(newVerifyModes);
            $("#" + "${selectNVSId}").attr("title", newVerifyModes);
            $("#" + "${NVSDoorId}").val(newVerifyStyleNos);
            DhxCommon.getCurrentWindow().close();
        }
    });

    /** 修改验证方式 */
	function changeVerifyMode(value) {
		// 自动识别
		if (value == "0") {
			$("#newVerifyStyleSetDiv${uuid!}").hide();
			// 手动配置且"与"关系切换为自动配置时，将逻辑关系切换为"或"
			$("input[name='verifyStyleLogic'][value=0]").prop("checked", true);
		} else if (value == "1") {
			// 手动配置
			$("#newVerifyStyleSetDiv${uuid!}").show();
		}
	}
</script>
</#macro>
