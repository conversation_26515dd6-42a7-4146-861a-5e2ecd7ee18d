<#include "/public/template/editTemplate.html">
<#macro editContent>
<form method="post" id="${formId}" action="/accPerson.do?saveParamSet" enctype="multipart/form-data">
    <div id="idAccDiv" style="height: 100%; padding-bottom:0px;">
        <input type="hidden" value="${(ids)!}" name="ids" id="id_model_pk">
        <input type="hidden" value="${(pins)!}" name="pins" id="pins">
        <table class="tableStyle">
            <tr>
                <th style="width: 55% !important;"><label><@i18n 'common_level_superuser'/></label></th>
                <td>
                    <@ZKUI.Combo width="148" onChange="superAuthChange" readonly="true" empty="false" hideLabel="true" name="superAuth" value="${(tempAccPerson.superAuth)!0}">
                        <option value="0"><@i18n 'common_no'/></option>
                        <option value="15"><@i18n 'common_yes'/></option>
                    </@ZKUI.Combo>
                </td>
            </tr>
            <tr>
                <th><label><@i18n 'pers_person_devOpAuth'/></label></th>
                <td>
                    <@ZKUI.Combo width="148" empty="false" readonly="true" hideLabel="true" name="privilege" value="${(tempAccPerson.privilege)!0}">
                        <option value="0"><@i18n 'common_level_generalUser'/></option>
                        <option value="14"><@i18n 'common_level_administrator'/></option>
                        <option value="2"><@i18n 'common_level_enroller'/></option>
                    </@ZKUI.Combo>
                </td>
            </tr>
            <tr>
                <th><label><@i18n 'acc_pers_delayPassage'/></label></th>
                <td>
                    <#if (tempAccPerson.delayPassage)?exists && (tempAccPerson.delayPassage)?string == "true">
                        <@ZKUI.Input hideLabel="true" type="checkbox" id="delayPassage" name="delayPassage" value="true" checked="checked"/>
                    <#else>
                        <@ZKUI.Input hideLabel="true" type="checkbox" id="delayPassage" name="delayPassage" value="true"/>
                    </#if>
                </td>
            </tr>
            <tr>
                <th><label><@i18n 'pers_person_disabled'/></label></th>
                <td>
                    <#if (tempAccPerson.disabled)?exists && (tempAccPerson.disabled)?string == "true">
                        <@ZKUI.Input hideLabel="true" type="checkbox" id="idDisabled" name="disabled" value="true" checked="checked"/>
                    <#else>
                        <@ZKUI.Input hideLabel="true" type="checkbox" id="idDisabled" name="disabled" value="true"/>
                    </#if>
                </td>
            </tr>
            <tr>
                <th><label><@i18n 'common_level_setValidTime'/></label></th>
                <td>
                    <#if (tempAccPerson.isSetValidTime)?exists && (tempAccPerson.isSetValidTime)?string == "true">
                        <@ZKUI.Input hideLabel="true" type="checkbox" id="isSetValidTime" name="isSetValidTime" value="true" checked="checked"/>
                    <#else>
                        <@ZKUI.Input hideLabel="true" type="checkbox" id="isSetValidTime" name="isSetValidTime" value="true"/>
                    </#if>
                </td>
            </tr>
            <tr class="accSetValidTime" style="display:none">
                <th><label><@i18n 'common_level_startTime'/></label><span class="required" id="spanStartTime">*</span>
                </th>
                <td>
                    <@ZKUI.Input id="validTimeStart" hideLabel="true" type="datetime" readonly="true" today="true" name="startTime" value="${((tempAccPerson.startTime)?string('yyyy-MM-dd HH:mm:ss'))!}"/>
                </td>
            </tr>
            <tr class="accSetValidTime" style="display:none">
                <th><label><@i18n 'common_level_endTime'/></label><span class="required" id="spanEndTime">*</span></th>
                <td>
                    <@ZKUI.Input id="validTimeEnd" hideLabel="true" type="datetime" readonly="true" today="true" name="endTime" todayRange="end" value="${((tempAccPerson.endTime)?string('yyyy-MM-dd HH:mm:ss'))!}"/>
                </td>
            </tr>
        </table>
    <!--</div>-->
</form>
<script type="text/javascript">

    $("#${formId}").validate( {
        debug : true,
        submitHandler : function()
        {
            <@submitHandler/>
        }
    });

    initSuperAuth();

    function initSuperAuth() {
        var value = "${(tempAccPerson.superAuth)!0}";
        if(value == '15')
        {
            $("#isSetValidTime").removeAttr("checked");
            $('#isSetValidTime').attr("disabled","disabled");
            $('.accSetValidTime').css('display','none');
            $("#delayPassage").removeAttr("checked");
            $('#delayPassage').attr("disabled","disabled");
        }
        else
        {
            $('#isSetValidTime').removeAttr("disabled");
            $('#delayPassage').removeAttr("disabled");
            clickSetValidTime(document.getElementById("isSetValidTime"));
        }
    }

    function superAuthChange(value, text) {
        if(value == '15')
        {
            $("#isSetValidTime").removeAttr("checked");
            $('#isSetValidTime').attr("disabled","disabled");
            $('.accSetValidTime').css('display','none');
            $("#delayPassage").removeAttr("checked");
            $('#delayPassage').attr("disabled","disabled");
        }
        else
        {
            $('#isSetValidTime').removeAttr("disabled");
            $('#delayPassage').removeAttr("disabled");
            clickSetValidTime(document.getElementById("isSetValidTime"))
        }
    }

    function clickSetValidTime(obj) {
        if(obj.checked)
        {
            accSetValidTimeShow();
        }
        else {
            accSetValidTimeHide();
        }
    }

    $("#isSetValidTime").click(function() {
        clickSetValidTime(this);
    });

    //显示
    function accSetValidTimeShow()
    {
        $("#${formId} input[name='startTime']").rules("add", { validTimeStartValid:true});
        $("tr[class^='accSetValidTime']").each(function(){
            $(this).show();
        });
    }

    jQuery.validator.addMethod("validTimeStartValid", function(value, element){
        if(convertDateFromString($("#${formId} input[name='startTime']").val()) < convertDateFromString($("#${formId} input[name='endTime']").val()))
        {
            return true;
        }
        else
        {
            return false;
        }
    },function(){
        return "<@i18n 'common_startEndDateCompare' />";
    });

    function convertDateFromString(dateString) {
        var date = new Date(dateString.replace(/-/g,"/"))
        return date.getTime();
    }

    //隐藏
    function accSetValidTimeHide()
    {
        $("tr[class^='accSetValidTime']").each(function(){
            $(this).hide();
        });
        $("#${formId} input[name='startTime']").rules("remove");
        $("#${formId} input[name='endTime']").rules("remove");
    }
</script>
</#macro>