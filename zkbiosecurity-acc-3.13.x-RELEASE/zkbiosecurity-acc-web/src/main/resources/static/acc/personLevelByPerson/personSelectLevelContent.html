<@ZKUI.SelectContent showColumns="checkbox,levelName,timeSegName,startTime,endTime,clearDate" gridName="accSelectPersonLevelGrid"
	textField="levelName" copy="true" personId="${personId!}" personPin="${personPin!}" vo="com.zkteco.zkbiosecurity.acc.vo.AccPersonSelectLevelItem"
	query="accPersonLevelByPerson.do?getPersonSelectLevel&personId=${personId!}&notInId=${notInId!}"
	onSure="afterAddPersonLevel">
	<@ZKUI.Searchbar>
		<@ZKUI.SearchTop>
			<tr>
				<td valign="middle">
					<@ZKUI.Input name="levelName"  maxlength="30" title="common_level_name" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="timeSegName"  maxlength="30" title="acc_timeSeg_entity" type="text"/>
				</td>
			</tr>
		</@ZKUI.SearchTop>
	</@ZKUI.Searchbar>
</@ZKUI.SelectContent>
<script type="text/javascript">
	function personLevelDeleteDateClick(id) {
		var dhGrid = ZKUI.Select.get("accSelectPersonLevelGrid").rightGrid.grid;
		dhGrid.cellById(id, 3).setValue("");
		dhGrid.cellById(id, 4).setValue("");
	}

	function convertPersonSelectLevelClearDate(v, opts, gridName, colIndex, rid) {
		return "<div class='icv-ic_clear acc_icv zk-colors-fg-green' onclick=\"personLevelDeleteDateClick('" + rid + "')\" ></div>";
	}


	$(function () {
		// 设置左列表字段隐藏
		if ("${attr('system.levelTime.support','false')}" === "true") {
			ZKUI.Select.get("accSelectPersonLevelGrid").leftGrid.grid.setColumnHidden(3, true);
			ZKUI.Select.get("accSelectPersonLevelGrid").leftGrid.grid.setColumnHidden(4, true);
			ZKUI.Select.get("accSelectPersonLevelGrid").leftGrid.grid.setColumnHidden(5, true);
			ZKUI.Select.get("accSelectPersonLevelGrid").leftGrid.grid.setColWidth(2, 360);
			// 设置开始时间和结束时间单击编辑
			ZKUI.Select.get("accSelectPersonLevelGrid").rightGrid.grid.enableMathEditing(true);
			ZKUI.Select.get("accSelectPersonLevelGrid").rightGrid.grid.enableEditEvents(true,false,true);
			ZKUI.Select.get("accSelectPersonLevelGrid").rightGrid.grid.setDateFormat("%Y-%m-%d %H:%i:%s");
		}
	});
</script>
