<#assign gridName="accPersonLevelByPersonGrid${uuid!}">
<@ZKUI.DGrid gridName="${gridName}">
	<@ZKUI.LeftGrid title="common_level_editLevelPerson" width="57%">
		<@ZKUI.Searchbar>
			<@ZKUI.SearchTop>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="persPin"  maxlength="30" title="pers_person_pin" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Input name="likeName"  maxlength="30" title="pers_person_wholeName" type="text"/>
					</td>
				</tr>
			</@ZKUI.SearchTop>
			<@ZKUI.SearchBelow>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="deptName"  maxlength="30" title="pers_dept_deptName" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Combo empty="true" name="privilege" title="pers_person_devOpAuth">
							<option value="0"><@i18n 'common_level_generalUser'/></option>
							<option value="14"><@i18n 'common_level_administrator'/></option>
							<option value="2"><@i18n 'common_level_enroller'/></option>
						</@ZKUI.Combo>
					</td>
				</tr>
			</@ZKUI.SearchBelow>
		</@ZKUI.Searchbar>
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="acc:personLevelByPerson:refresh"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="accPersonLevelByPerson.do?setAccParam&pins=(persPin)" text="pers_person_accSetting" img="acc_controlSet.png" action="commonOpenOperate" permission="acc:personLevelByPerson:setAccParams"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="accPersonLevelByPerson.do?exportPersonLevel" type="export" action="exportPersonLevel" permission="acc:personLevelByPerson:export"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="accPersonLevelByPerson.do?syncPersonLevel" img="acc_personLevel_syncPersonLevel.png" text="acc_personLevelByPerson_syncLevel" action="syncPersonLevel" permission="acc:personLevelByPerson:sync"></@ZKUI.ToolItem>
		</@ZKUI.Toolbar>
		<@ZKUI.Grid onRowSelect="accPersonLevelByPersonLeftGridClick" vo="com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelByPersonItem" query="accPersonLevelByPerson.do?list" autoFirst="true" showColumns="!startTime,endTime"/>
	</@ZKUI.LeftGrid>
	<@ZKUI.RightGrid callback="accPersonLevelByPersonRightCallback" leftFieldName="personId">
		<@ZKUI.Searchbar>
			<@ZKUI.SearchTop>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="name"  maxlength="30" title="common_level_name" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Input name="timeSegName"  maxlength="30" title="acc_timeSeg_entity" type="text"/>
					</td>
				</tr>
			</@ZKUI.SearchTop>
		</@ZKUI.Searchbar>
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem type="refresh" permission="acc:personLevelByPerson:refresh"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="accPersonLevelByPerson.do?delLevel&personId=(@id:gs)&personPin=(@pin:gs)&levelIds=(id)&levelNames=(name)" action="accRightGridCommonDel" text="common_level_delPersonLevel" img="comm_del.png" permission="acc:personLevelByPerson:delLevel"></@ZKUI.ToolItem>
			<#--<@ZKUI.ToolItem id="accPersonLevelByPerson.do?export&personIds=(@id:gs)" type="export" permission="acc:personLevelByPerson:export"/>-->
		</@ZKUI.Toolbar>
		<@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelItem" query="accPersonLevelByPerson.do?getPersonLevel"/>
	</@ZKUI.RightGrid>
</@ZKUI.DGrid>
<script type='text/javascript'>

    function accPersonLevelByPersonLeftGridClick(rid) {
        var leftGrid = this.zkgrid;//获取左表格
        var dbGrid = ZKUI.DGrid.get(leftGrid.options.dGridName);//双列表对象
        var rightGrid = dbGrid.rightGrid;//右表格对象
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        rightGrid.reload(function() {
            accPersonLevelByPersonRightCallback(row.id, rightGrid, rid);//手动回调
        }, {personId : rid});//传递id到右表格查询
    }

    //右表格回调事件
    function accPersonLevelByPersonRightCallback(id, rightGrid, rid) {
    	if(id != "-1") {
    		var dbGrid = ZKUI.DGrid.get(rightGrid.options.dGridName);//双列表对象
        	var leftGrid = dbGrid.leftGrid;//获取左表格
        	var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        	rightGrid.setTitle(I18n.getValue("pers_common_browsePerson") + " " + row.persPin + " " + I18n.getValue("common_level_personLevel"));//设置右表格标题
    	}
    }
	function accPersonSelectLevel(gridName, domObj, rid) {
		var path = "skip.do?page=acc_personLevelByPerson_personSelectLevelContent&personId=" + rid;
		var width = 850;
		if ("${attr('system.levelTime.support','false')}" === "true") {
			width = 1158;
		}
		var opts = {
			path: path,
			width: width,
			height: 450,
			title: "<@i18n 'common_level_addPersonLevel'/>",
			gridName: "gridBox"
		};
		DhxCommon.createWindow(opts);
	}
    function afterAddPersonLevel(value, text, event) {
        var personId = this.options.personId;
        var personPin = this.options.personPin;
		//  获取权限组id;支持临时权限组装开始/结束时间
        var accLevelIds = value;
		var validTimeResult = true;
		var validTip = "";
		if ("${attr('system.levelTime.support','false')}" === "true") {
			var resultMap = accPersonLevelByPersonValidTime(value);
			validTimeResult = resultMap.get("timeValidResult");
			validTip = resultMap.get("validTip");
			accLevelIds = resultMap.get("accLevelIds");
		}
		if (validTimeResult) {
			DhxCommon.closeWindow();
			var opts = {
				dealPath: "accPersonLevelByPerson.do?addLevel" ,//进度处理请求
				type: "single",//进度类型，取值single,public,custom
				useReq:true,
				autoClose:true,
				delay:5,
				onCallback:"finishPersonAddLevelProcess",//处理完后回调
				height:250,
				title:"<@i18n 'common_progress_proCmd'/>", //弹窗标题
				data:{
					personId : personId,
					personPin : personPin,
					levelIds : accLevelIds,
					levelNames : text
				}
			}
			openProcess(opts);
		} else {
			openMessage(msgType.warning, validTip);
		}
        return false;
    }

    function accPersonLevelByPersonValidTime(value) {
    	// 支持临时权限
		var accLevelIds = "";
		var dhGrid = ZKUI.Select.get("accSelectPersonLevelGrid").rightGrid.grid;
		var timeValidResult = true;
		var validTip = "";
		var resultMap = new Map();
		for (var i = 0; i < dhGrid.rowsCol.length; i++) {
			var id = dhGrid.rowsCol[i].idd;
			var startTimeStr = dhGrid.cellById(id, 3).getValue();
			var endTimeStr = dhGrid.cellById(id, 4).getValue();
			var levelName = dhGrid.cellById(id, 1).getValue();
			if (endTimeStr != '' && startTimeStr != '' && endTimeStr <= startTimeStr) {
				timeValidResult = false;
				validTip = validTip + levelName + ":"+ "<@i18n 'common_dsTime_timeValid4'/>" + "</br>";
				dhGrid.cellById(id, 3).setValue("");
				dhGrid.cellById(id, 4).setValue("");
			} else if ((endTimeStr != '' && startTimeStr == '') || (endTimeStr == '' && startTimeStr != '')) {
				timeValidResult = false;
				validTip = validTip + levelName + ":"+ "<@i18n 'common_levelImport_timeNotNull'/>" + "</br>";
				dhGrid.cellById(id, 3).setValue("");
				dhGrid.cellById(id, 4).setValue("");
			} else {
				accLevelIds += dhGrid.rowsCol[i].idd + "," + startTimeStr + "," + endTimeStr + ";";
			}
		}
		resultMap.set("timeValidResult",timeValidResult);
		resultMap.set("validTip", validTip);
		resultMap.set("accLevelIds",accLevelIds);
		return resultMap;
    }

    function finishPersonAddLevelProcess ()
	{
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        var rightGrid = dbGrid.rightGrid;//右表格对象
        rightGrid.reload();
	}

	function exportPersonLevel(id, bar, opts) {
		if (bar) {
			var dbGrid = ZKUI.DGrid.get("${gridName}");
			//右表格对象
        	var rightGrid = dbGrid.rightGrid;
			var gridName = bar.gridName;
			// 获取选中的人员id
            var personIds = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
            if(personIds == "") {
                messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});
            } else {
             	var jsonData = {};
            	var personId = personIds.split(",");
				for (var i=0; i < personId.length; i++) {
					var personPin = ZKUI.Grid.GRID_PULL[gridName].grid.getRowData(personId[i]).persPin;
					jsonData[personId[i]] = personPin;
				}
            	// 替换双引号，避免解析错误
            	jsonData = JSON.stringify(jsonData).replace(/\"/g,"'");
				opts.path = "skip.do?page=acc_personLevelByPerson_opExportPersonLevel&personIds=" + personIds + "&jsonData=" + jsonData +"&gridName=" + rightGrid.gridName + "&actionName=" + encodeURIComponent(id);
				if(opts.maxExportCount) {
					opts.path = opts.path + "&maxExportCount=" + opts.maxExportCount;
				}
				opts.width = opts.width || 450;
				opts.height = opts.height || 240;
				DhxCommon.createWindow(opts);
            }
    	}
	}

	function accConvertPrivilege(v) {
		v = v + '';
	    switch (v)
		{
			case '':return "<@i18n 'common_level_generalUser'/>";break;
			case '0':return "<@i18n 'common_level_generalUser'/>";break;
			case '2':return "<@i18n 'common_level_enroller'/>";break;
			case '14':return "<@i18n 'common_level_administrator'/>";break;
            default:return v;
		}
	}

	function syncPersonLevel(id, bar, opts) {
		if (bar) {
			var dbGrid = ZKUI.DGrid.get("${gridName}");
			//右表格对象
        	var rightGrid = dbGrid.rightGrid;
			var gridName = bar.gridName;
			// 获取选中的人员id
            var personIds = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
            var text = I18n.getValue("common_prompt_executeOperate").format(opts.text);
            if(personIds == "") {
                text = I18n.getValue("common_prompt_selectObj");
            }
			actionConfirm(function (result) {
				if (result) {
					var personId = personIds.split(",");
					var personPins = "";
					for (var i=0; i < personId.length; i++) {
						var personPin = ZKUI.Grid.GRID_PULL[gridName].grid.getRowData(personId[i]).persPin;
						personPins += personPin;
					}
					DhxCommon.closeWindow();
					var opts = {
						dealPath: "accPersonLevelByPerson.do?syncPersonLevel" ,//进度处理请求
						type: "single",//进度类型，取值single,public,custom
						useReq:true,
						autoClose:true,
						delay:5,
						onCallback:"finishPersonAddLevelProcess",//处理完后回调
						height:250,
						title:"<@i18n 'common_progress_proCmd'/>", //弹窗标题
						data:{
							personIds : personIds,
							personPins: personPins
						}
					}
					openProcess(opts);
					return false;
				}
			}, text);
		}
	}

</script>