<#assign formId = "accLevel${uuid!}">
<#assign editPage="true">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
	<form action='accLevel.do?save' method='post' id='${formId}' enctype='multipart/form-data' onkeydown="if(event.keyCode==13){return false;}" >
		<input type='hidden' name='id' value='${(item.id)!}'/>
		<table class='tableStyle'>
			<tr>
				<th><label><@i18n 'common_level_name'/></label><span class='required'>*</span></th>
				<td><input name='name' id="idLevelName" type='text' value='${(item.name)!}' maxlength="30"/></td>
			</tr>
			<tr>
				<th><label><@i18n 'acc_timeSeg_entity'/></label><span class='required'>*</span></th>
				<td>
					<@ZKUI.Combo type="radio" width="148" empty="false" path="accTimeSeg.do?getTimeSegList" name="timeSegId" readonly="true" hideLabel="true" value="${(item.timeSegId)!timeSegId}"/>
				</td>
			</tr>
			<tr>
				<th><label><@i18n 'base_area_entity'/></label><span class='required'>*</span></th>
				<td>
					<@ZKUI.ComboTree type="radio" width="148" autoFirst="true" url="authArea.do?tree" name="authAreaId" readonly="true" hideLabel="true" value="${(item.authAreaId)!}"/>
				</td>
			</tr>
		</table>
	</form>

	<script type='text/javascript'>
        var gridName = DhxCommon.getCurrentWindow().opts.gridName;
        $().ready(function() {
            $("#${formId}").validate( {
                debug : true,
                rules : {
                    "name" :
                        {
                            required : true,
                            unInputChar:true,
                            overRemote : ["accLevel.do?valid", "${(item.name)!}"]
                        },
                    "timeSegId" :
                        {
                            required : true
                        },
                    "authAreaId" :
                        {
                            required : true
                        }
                },
                submitHandler : function()
                {
                    var levelId = "${(item.id)!}";
                    var personCount = "${(personCount)!}";
                    var doorCount = "${(doorCount)!}";
                    var oldTimeSegId = "${(item.timeSegId)!}";
                    var newTimeSegId = $("#${formId} input[name='timeSegId']").val();
                    var initFlag = "${(item.initFlag)!}";
                    var areaId = $("#${formId} input[name='authAreaId']").val();
                    // 判断权限组中是否带人员，时间段改变则需要下发命令
                     if(levelId == "" ) {
                         var fun = function(result) {
                             var needRefresh = true;
                         	<@ZKUI.Permission name="acc:level:addDoor">
                                 var retLevelId = result.data.id;
							 	 var retLevelName = result.data.name;
                                 needRefresh = false;
                                 messageBox({messageType:"confirm", text: "<@i18n 'acc_level_isAddDoor'/>",
                                 	callback: function(result){
                             	    	if(result) {
                             	    	    //'/accLevel.do?filterLevelDoor&id='+retLevelId+'^0^0^900^470^添加门'
                                            var path = "skip.do?page=acc_level_accLevelSelectDoorContent&levelId="+retLevelId+"&levelName="+retLevelName+"&levelType=add"
                                            var opts = {
                                                path:path,
                                                width: 900,
                                                height: 470,
                                                title:"<@i18n 'acc_map_addDoor'/>",
                                                gridName:"gridBox"
                                            };
                                         	DhxCommon.createWindow(opts);
                                            var leftGrid = ZKUI.Grid.get(gridName);
                                            leftGrid.reload();
                                     	} else {
                                            finishEditLevelProcess();
										}
                                 }});
                             </@ZKUI.Permission>
                             if(needRefresh) {
                                 finishEditLevelProcess();
                             }
                         };
                         <@submitHandler callBackFun="fun(result)"/>
                     } else if(doorCount > 0 && oldTimeSegId != newTimeSegId) {
                         var levelName = $("#${formId} #idLevelName").val();
                         DhxCommon.closeWindow();
                         var progressParams = {
                                 dealPath: "accLevel.do?save",//进度处理请求
                                 type: "public",//进度类型，取值single,public,custom
                                 onCallback:"finishEditLevelProcess",//处理完后回调
                                 title:"<@i18n 'common_progress_proCmd'/>", //弹窗标题
							     data : {
                                     //&id="+ levelId + "&name=" + levelName + "&changeLevel=true&timeSegId="+newTimeSegId+"&initCode="+initCode+"&authAreaId="+areaId
                                     id : levelId,
									 name : levelName,
                                     changeLevel : true,
                                     timeSegId : newTimeSegId,
                                     initFlag : initFlag,
                                     authAreaId : areaId
								 }
                             };
                         openProcess(progressParams);
                     } else {
                         <@submitHandler/>
                         finishEditLevelProcess();
                     }
                }
            });
        });

        function finishEditLevelProcess(){
            var leftGrid = ZKUI.Grid.get(gridName);
            var dbGrid = ZKUI.DGrid.get(leftGrid.options.dGridName);//双列表对象
            var rightGrid = dbGrid.rightGrid;//右表格对象
            leftGrid.reload();
            rightGrid.reload();
        }
	</script>
</#macro>