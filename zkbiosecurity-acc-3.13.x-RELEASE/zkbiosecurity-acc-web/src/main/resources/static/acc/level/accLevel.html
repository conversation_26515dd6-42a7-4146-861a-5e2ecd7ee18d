<#assign gridName="accLevelGrid${uuid!}">
<script type="text/javascript">

    function accLevelLeftGridClick(rid) {
        //此方法注册的是官方事件，所以this指代的是官方grid对象，通过访问属性zkgrid获取我们自定义的grid对象
        var leftGrid = this.zkgrid;//获取左表格
        var dbGrid = ZKUI.DGrid.get(leftGrid.options.dGridName);//双列表对象
        var rightGrid = dbGrid.rightGrid;//右表格对象
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        rightGrid.reload(function() {
            accLevelRightCallback(row.id, rightGrid, rid);//手动回调
        },{accLevelId:rid});//传递id到右表格查询
    }

    //url="/skip.do?page=acc_level_accLevelSelectDoorContent&levelId=(id)"
    function accLevelAddDoor(gridName, domObj, rid) {
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        var leftGrid = dbGrid.leftGrid;
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        var path = "skip.do?page=acc_level_accLevelSelectDoorContent&levelId="+ rid + "&levelName=" + row.name;
        var opts = {
            path:path,
            width: 1150,
            height: 510,
            title:"<@i18n 'acc_map_addDoor'/>",
            gridName:"gridBox"
        };
        DhxCommon.createWindow(opts);
    }

    //右表格回调事件
    function accLevelRightCallback(id, rightGrid, rid) {
        var dbGrid = ZKUI.DGrid.get(rightGrid.options.dGridName);//双列表对象
        var leftGrid = dbGrid.leftGrid;//获取左表格
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        rightGrid.setTitle(I18n.getValue("common_level_browseLevel") + " " + row.name + "（" + row.authAreaName + "）" + I18n.getValue("acc_level_doorGroup"));//设置右表格标题
    }

    function finishAccLevelAddDoorProcess(levelId, levelType) {
        var dbGrid = ZKUI.DGrid.get("${gridName}"); //双列表对象
        var leftGrid = dbGrid.leftGrid; //左表格对象
        var rightGrid = dbGrid.rightGrid; //右表格对象
        if (levelType && levelType == 'add'){
            window.setTimeout(function(){
                leftGrid.reload();
                rightGrid.reload();
            },1000);
        } else {
            setAccCount(leftGrid, rightGrid, "", "add", "door", 1000, levelId);
        }
    }

    //删除权限组
	function delAccLevel(id, bar, opt) {
        if(bar){
            var gridName=bar.gridName||"gridbox";
            var leftGrid = ZKUI.Grid.get(gridName);
            var dbGrid = ZKUI.DGrid.get(leftGrid.options.dGridName);//双列表对象
            var rightGrid = dbGrid.rightGrid;//右表格对象
            var ids=ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
            if(ids==""){
                messageBox({messageType:"alert",text:"common_prompt_selectObj"})
            }else{
                deleteConfirm(function(result){
                    if(result){
                    	var checkId="accLevel.do?levelIsDelete";
                    	var params = splitURL(fillParamsFromGrid(gridName, ids, checkId));
                    	params.data.ids = ids;
                        $.ajax({
                            type: "post",
                            url : params.url,
                            dataType : "json",
                            data : params.data,
                            async: false,
                            success: function(retData) {
								if (retData.ret == "ok") {
                                    var param = splitURL(fillParamsFromGrid(gridName, ids, id));
                                    param.data.ids = ids;
                                    var opts = {
                                        dealPath: param.url,//进度处理请求
                                        type: "public",//进度类型，取值single,public,custom
                                        onCallback: "finishAccDelLevelProcess",
                                        title:"<@i18n 'common_progress_proCmd'/>", //弹窗标题,
                                        data :param.data,
                                        useReq:true,//使用请求参数作为进度条参数
                                        autoClose:true,//自动关闭
                                        delay:5//自动关闭时间(5秒后自动关闭)
                                        //height:250//弹窗高度
                                    }
                                    openProcess(opts);
								} else if (retData.data == "acc") {
                                    $.jBox.tip("<@i18n 'common_prompt_initDataCanNotDel'/>", "warning");
								} else {
                                    messageBox({messageType:"alert",text:retData.msg});
								}
                            }
                        });
                    }
                },"common_prompt_sureToDelThese")
            }
        }
    }

    function finishAccDelLevelProcess() {
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        var leftGrid = dbGrid.leftGrid;
        var rightGrid = dbGrid.rightGrid;
		window.setTimeout(function () {
			leftGrid.reload();
			rightGrid.reload();
		}, 2000);
    }

    //权限组删除门
    function delAccLevelDoor(id, bar, opt){
	    if (bar){
            var gridName = bar.gridName || "gridbox";
            var rightGrid = ZKUI.Grid.get(gridName);
            var leftGrid = ZKUI.DGrid.get(rightGrid.options.dGridName).leftGrid;
            var levelId = leftGrid.grid.getSelectedRowId();
            var doorIds = rightGrid.grid.getCheckedRows(0);
            var num = doorIds.split(",").length;
            if(doorIds=="" || doorIds==null ){
                messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});
            }else {
                deleteConfirm(function(result){
                    if(result){
						var param = splitURL(fillParamsFromGrid(gridName, doorIds, id));
						var opts = {
							dealPath: param.url,//进度处理请求
							type: "single",//进度类型，取值single,public,custom
							onCallback: "finishAccLevelDelDoorProcess",
							title:"<@i18n 'common_progress_proCmd'/>", //弹窗标题,
							data: param.data,
                            useReq:true,//使用请求参数作为进度条参数
                            autoClose:true,//自动关闭
                            delay:5,//自动关闭时间(5秒后自动关闭)
                            height:250//弹窗高度
						}
						openProcess(opts);

                    }
                },"common_prompt_sureToDelThese");
            }
		}
    }

    function finishAccLevelDelDoorProcess() {
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        var leftGrid = dbGrid.leftGrid;
        var rightGrid = dbGrid.rightGrid;
        var doorIds = rightGrid.grid.getCheckedRows(0);
        var num = doorIds.split(",").length;
		setAccCount(leftGrid, rightGrid, num, "del", "door", 3000);
    }

    function operateLevelDoor(id, bar, opt) {
    	var akeyCode = 0;  //用于获取单击键盘的键值
    	var alreadySubmit = false;
    	var itemId = id;
		if(bar) {
			var gridName = bar.gridName;
			var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
			if (ids == "") {
				messageBox({messageType: "alert", text: "<@i18n 'common_prompt_selectObj'/>"});
			} else {
				var leftGrid = ZKUI.DGrid.get("${gridName}").leftGrid;
				var vals = [];
				ids.split(",").forEach(function(id) {//获取某列的值，这里第2列
					vals.push($(leftGrid.grid.cells(id, 1).getValue()).text());
				})
				var levelNames = vals.join(",");
				var data;
				data = {title: "", id: 0, opObj: "<@i18n 'common_currentAll'/>"};
				data.title = opt.text;
				function dealRetFun(result)
				{
					if(result[sysCfg.ret] == sysCfg.success)
					{
						closeMessage();
						openMessage(msgType.success, I18n.getValue(result[sysCfg.msg]));
						$(".jbox-icon").addClass("jbox-rtBeforeIcon");
					}
					else if (result[sysCfg.ret] == sysCfg.error) {
						closeMessage();
						var msgArray = result[sysCfg.msg].split(";");
						var msg = "";
						for (i in msgArray) {
							if (msgArray[i] != "") {
								msg += "<font class='zk-msg-error'>" + msgArray[i] + "</font><br>";
							}
						}
						openMessage(msgType.error, msg);
					}
					else if(result[sysCfg.ret] == "500")
					{
						openMessage(msgType.error, result[sysCfg.msg] );
					} else {
						openMessage(msgType.error);
					}
				}
				var uuid = bar.options.id;
				function openForm(data) {
					var html = "<div style='padding:10px;margin-${leftRTL!'left'}:30px;'>" +
							"<form id='openForm" + uuid + "' action='" + itemId + "&name=" + encodeURIComponent(data.opObj) + "' method='post'>" +
							"<table style='padding-top: 8px;'><tr id='loginPwdRow' hidden='hidden'><th style='text-align: left;padding:5px 5px 5px 0px;'><@i18n 'auth_user_userPwd'/><span class='required'>*</span></th>" +
							"<td><input type='password' id='levelLoginPwd' name='levelLoginPwd' maxlength='50' autofocus='true' onchange='pwdChange()'/><input type='hidden'  name='loginPwd' id='password_hidden'/></td></tr>"+
							"<tr id='openIntervalTr'><th style='text-align: left;padding: 5px 5px 5px 0px;;'><@i18n 'common_open'/>:</th><td><input type='text' name='openInterval' size='8' maxlength='3' value='5'/>" +
							"<@i18n 'common_second'/><span class='form_note'>(1-254)</span></td></tr>" +
							"</table></form>" +
							"</div>";

					var result = false;
					isNeedValidUserForExport(function(ret) {
						result = ret.ret === sysCfg.success;
						if(ret && ret.ret === sysCfg.success) {
							$("#loginPwdRow").hide();
						} else {
							if(itemId.indexOf("open") == -1) {
								$("#openIntervalTr").hide();
							}
							$("#loginPwdRow").show();
						}
					})
					$.jBox(html, {
						title: data.title,
						loaded: function () {
							if(result) {
								$("#levelLoginPwd").rules("remove");
								$("input[name=openInterval]").focus();
							} else {
								$("#levelLoginPwd").focus();
								$("#levelLoginPwd").rules("add", {required : true});
							}
						},
						submit: function (v, h, f) {
							if (v == "ok") {
								$("#openForm" + uuid).submit();
								return false;
							}
						}
					});

					$("#openForm" + uuid).validate({
						rules: {
							"openInterval": {
								required: true,
								digits: true,
								range: [1, 254]
							}
						},
						submitHandler: function (form) {
							$(".jbox-button-focus").addClass("jbox-button-hover");
							if (alreadySubmit) {
								return;
							}
							alreadySubmit = true;
							$.jBox.close();
							onLoading(function () {
								$(form).ajaxSubmit({
									async : true,
									dataType : 'json',
									data: {
										ids: ids,
										names: levelNames
									},
									success: function(result)
									{
										dealRetFun(result);
									}
								});
							});
						}
					});
				}

				function openConfirm(data) {
					var html = "<div style='margin-top: -2px'>" +
							"<div><@i18n 'common_target'/>:" + data.opObj + "</div>" +
							"<div>" + "<@i18n 'common_prompt_executeOperate'/>".format(data.title) + "</div>" +
							"</div>";
					alreadySubmit = false;
					akeyCode = 0;  //用于获取单击键盘的键值
					$.jBox.confirm(html, data.title, function (v, h, f) {
						if (v == "ok") {
							$(".jbox-button-focus").addClass("jbox-button-hover");
							if (alreadySubmit) {
								return;
							}
							alreadySubmit = true;
							$.jBox.close();
							onLoading(function () {
								$(".jbox-icon").addClass("jbox-rtBeforeIcon");
								$.ajax({
									type: "POST",
									url: itemId,
									async: true,
									data: {
										ids: ids,
										name: data.opObj,
										names: levelNames
									},
									success: function (result) {
										dealRetFun(result);
									}
								});
							});
							return false;
						}
						return true;
					});
					$(".jbox-content").children().addClass("jbox-rtContent");
					$(".jbox-icon").addClass("jbox-rtIcon");
				}
				window.setTimeout(function () {
					isNeedValidUserForExport(function(ret) {
						if(ret && ret.ret === sysCfg.success && itemId.indexOf("open") == -1) {
							openConfirm(data);
						} else {
							openForm(data);
						}
					})
				}, 10);
			}
		}
		function keyDownForEnter(e)
		{
			akeyCode = e.keyCode;
			if($(".jbox-button-focus") && akeyCode == '13' && alreadySubmit == false)
			{
				$(".jbox-button-focus").click();
			}
		}
		 //leo 解决实时监控->远程关门，弹出的提示框，按回车键不会进入下一步(浏览器focus兼容性问题)
		document.onkeydown = keyDownForEnter;
		$(".jbox-button-focus").css("border", "1px solid #888888");
		$(".jbox-button-focus").focus();
	}
	function pwdChange() {
		$("#password_hidden").val(hex_md5($("#levelLoginPwd").val()));
	}
	function exportLevelDoor(id, bar, opts){
		if(bar) {
			var gridName = bar.gridName;
			var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
			if (ids == "") {
				messageBox({messageType: "alert", text: "<@i18n 'common_prompt_selectObj'/>"});
			} else {
				var leftGrid = ZKUI.DGrid.get("${gridName}").leftGrid;
				var vals = [];
				ids.split(",").forEach(function(id) {//获取某列的值，这里第2列
					vals.push($(leftGrid.grid.cells(id, 1).getValue()).text());
				})
				var levelNames = vals.join(",");
				opts.path = "skip.do?page=acc_level_opExportLevelDoor&gridName=" + gridName + "&actionName=" + encodeURIComponent(id)+"&levelIds="+ ids + "&levelNames=" + levelNames;
				if(opts.maxExportCount) {
            		opts.path = opts.path + "&maxExportCount=" + opts.maxExportCount;
				}
				opts.width = opts.width || 550;
				opts.height = opts.height || 250;
				DhxCommon.createWindow(opts);
			}
		}
	}

	function reloadAccLevelGrid(){
		var dbGrid = ZKUI.DGrid.get("${gridName}");
        var leftGrid = dbGrid.leftGrid;
        var rightGrid = dbGrid.rightGrid;
		leftGrid.reload();
		rightGrid.reload();
	}
</script>
<@ZKUI.DGrid gridName="${gridName}">
	<@ZKUI.LeftGrid title="acc_leftMenu_level">
		<@ZKUI.Searchbar>
			<@ZKUI.SearchTop>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="name" maxlength="30" title="common_level_name" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Input name="timeSegName" maxlength="30" title="acc_timeSeg_entity" type="text"/>
					</td>
				</tr>
			</@ZKUI.SearchTop>
		</@ZKUI.Searchbar>
	<@ZKUI.Toolbar>
		<@ZKUI.ToolItem type="refresh" permission="acc:level:refresh"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="accLevel.do?edit" text="common_op_new" width="400" height="200" img="comm_add.png" action="commonAdd" permission="acc:level:add"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="accLevel.do?del&names=(name)" text="common_op_del" img="comm_del.png" action="delAccLevel" permission="acc:level:del"/>
			<@ZKUI.ToolItem type="more" text="acc_level_doorControl" img="acc_dev_control.png">
			<@ZKUI.ToolItem id="accLevel.do?openDoor" img="comm_remoteopendoor.png" text="acc_eventNo_8" permission="acc:level:openDoor" action="operateLevelDoor"/>
			<@ZKUI.ToolItem id="accLevel.do?closeDoor" img="comm_remoteclosedoor.png" text="acc_eventNo_9" permission="acc:level:closeDoor" action="operateLevelDoor"/>
			<@ZKUI.ToolItem id="accLevel.do?cancelAlarmDoor" img="acc_cancelAlarm.png" text="acc_eventNo_7" permission="acc:level:cancelAlarmDoor" action="operateLevelDoor"/>
			<@ZKUI.ToolItem id="accLevel.do?normalOpenDoor" img="comm_remoteNormalOpen.png" text="acc_rtMonitor_remoteNormalOpen" permission="acc:level:normalOpenDoor" action="operateLevelDoor"/>
			<@ZKUI.ToolItem id="accLevel.do?lockDoor" img="acc_remote_lock.png" text="acc_newEventNo_233" permission="acc:level:lockDoor" action="operateLevelDoor"/>
			<@ZKUI.ToolItem id="accLevel.do?unLockDoor" img="acc_remote_unlock.png" text="acc_newEventNo_234" permission="acc:level:unLockDoor" action="operateLevelDoor"/>
			<@ZKUI.ToolItem id="accLevel.do?enableNormalOpenDoor" img="comm_enableDSTime.png" text="common_rtMonitor_enableIntradayTZ" permission="acc:level:enableNormalOpenDoor" action="operateLevelDoor"/>
			<@ZKUI.ToolItem id="accLevel.do?disableNormalOpenDoor" img="comm_disableDSTime.png" text="common_rtMonitor_disableIntradayTZ" permission="acc:level:disableNormalOpenDoor" action="operateLevelDoor"/>
		</@ZKUI.ToolItem>
		<@ZKUI.ToolItem type="more" text="common_op_export" img="comm_export.png">
			<@ZKUI.ToolItem id="accLevel.do?export" type="export" text="acc_level_exportLevel" title="acc_level_exportLevel" permission="acc:level:export" />
			<@ZKUI.ToolItem id="accLevel.do?exportLevelDoor" type="export" action="exportLevelDoor" text="acc_level_exportLevelDoor" title="acc_level_exportLevelDoor" permission="acc:levelDoor:export" />
			</@ZKUI.ToolItem>
			<@ZKUI.ToolItem type="more" text="common_op_import" img="comm_import.png">
			<@ZKUI.ToolItem id="accLevel.do?import" type="import" showImportProcess="true" updateExistData="true" text="acc_level_importLevel" onFinish="reloadAccLevelGrid" title="acc_level_importLevel" width="500" height="250" permission="acc:level:import" />
			<@ZKUI.ToolItem id="accLevel.do?importLevelDoor" type="import" showImportProcess="true" text="acc_level_importLevelDoor" onFinish="reloadAccLevelGrid" title="acc_level_importLevelDoor" width="500" height="250" permission="acc:levelDoor:import" />
		</@ZKUI.ToolItem>
	</@ZKUI.Toolbar>
	<!-- 配置左表格选中事件 -->
	<@ZKUI.Grid onRowSelect="accLevelLeftGridClick" vo="com.zkteco.zkbiosecurity.acc.vo.AccLevelItem" query="accLevel.do?list" autoFirst="true"/>
	</@ZKUI.LeftGrid>
	<@ZKUI.RightGrid callback="accLevelRightCallback" leftFieldName="accLevelId">
		<@ZKUI.Searchbar>
			<@ZKUI.SearchTop>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="name" maxlength="30" title="acc_door_name" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Input name="devName" maxlength="30" title="common_ownedDev" type="text"/>
					</td>
				</tr>
			</@ZKUI.SearchTop>
		</@ZKUI.Searchbar>
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem type="refresh" permission="acc:level:refresh"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="accLevel.do?delDoor&levelId=(@id:gs)&levelName=(@name:gs)&doorIds=(id)&doorNames=(name)" text="acc_level_doorDelete" img="comm_del.png" action="delAccLevelDoor" permission="acc:level:delDoor"/>
			<!-- <#-- <@ZKUI.ToolItem id="accLevel.do?export" type="export" permission="acc:level:export"/>-->-->
		</@ZKUI.Toolbar>
		<@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccLevelDoorItem" query="accLevel.do?doorList"/>
	</@ZKUI.RightGrid>
</@ZKUI.DGrid>