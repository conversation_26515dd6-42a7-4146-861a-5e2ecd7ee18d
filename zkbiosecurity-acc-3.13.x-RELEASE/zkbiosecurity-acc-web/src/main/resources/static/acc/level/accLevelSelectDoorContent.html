<@ZKUI.SelectContent setMode="true,true,false,true" onSure="addSelectDoor" showColumns="checkbox,doorName,deviceAlias,deviceSn,areaName" textField="doorName" copy="true" levelId="${levelId!}" vo="com.zkteco.zkbiosecurity.acc.vo.AccLevelSelectDoorItem" query="accLevel.do?selectDoorlist&levelId=${levelId!}">
    <@ZKUI.Searchbar>
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="doorName"  maxlength="30" title="acc_door_name" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="deviceSn"  maxlength="30" title="common_dev_sn" type="text"/>
                </td>

            </tr>
        </@ZKUI.SearchTop>
        <@ZKUI.SearchBelow>
            <tr>
                <td valign="middle">
                    <@ZKUI.ComboTree width="148" url="authArea.do?tree" title="base_area_name" name="areaId" readonly="readonly"/>
                </td>
            </tr>
        </@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
</@ZKUI.SelectContent>

<script type="text/javascript">
    //添加门，点确定之后调用的方法
    function addSelectDoor(value, text, event) {
        var levelId = "${levelId!}";
        var levelName = "${levelName!}";
        DhxCommon.closeWindow();
        var opts = {
            dealPath: "accLevel.do?addDoor",//进度处理请求
            type: "single",//进度类型，取值single,public,custom
            onCallback:"reloadAccLevelAddDoor",//处理完后回调
            height:250,//弹窗高度
            title:"<@i18n 'common_progress_proCmd'/>", //弹窗标题
            useReq:true,//使用请求参数作为进度条参数
            autoClose:true,//自动关闭
            delay:5,//自动关闭时间(5秒后自动关闭)
            data:{
                levelId : levelId,
                levelName : levelName,
                doorIds : value,
                doorNames : text
            }
        }
        openProcess(opts);
        return false;
    }

    function reloadAccLevelAddDoor() {
        var levelId = "${levelId!}";
        var levelType = "${levelType!}";
        finishAccLevelAddDoorProcess(levelId, levelType);
    }
</script>