<@ZKUI.FormBox>
    <@ZKUI.Form id="accExceptionRecordForm" action="accExceptionRecord.do?save" method="post">
        <@ZKUI.Hidden name="id" value="${item.id!}"/>
        <@ZKUI.Hidden name="editType" value="${editType!}"/>
        
        <@ZKUI.FormItem>
            <@ZKUI.FormRow>
                <@ZKUI.FormCol title="pers_person_pin" required="true">
                    <@ZKUI.Input name="pin" value="${item.pin!}" maxlength="50" required="true"/>
                </@ZKUI.FormCol>
                <@ZKUI.FormCol title="pers_person_name" required="true">
                    <@ZKUI.Input name="name" value="${item.name!}" maxlength="100" required="true"/>
                </@ZKUI.FormCol>
            </@ZKUI.FormRow>
            
            <@ZKUI.FormRow>
                <@ZKUI.FormCol title="pers_dept_deptName" required="true">
                    <@ZKUI.Input name="deptName" value="${item.deptName!}" maxlength="100" required="true"/>
                </@ZKUI.FormCol>
                <@ZKUI.FormCol title="acc_exception_receiverPosition">
                    <@ZKUI.Input name="receiverPosition" value="${item.receiverPosition!}" maxlength="100"/>
                </@ZKUI.FormCol>
            </@ZKUI.FormRow>
            
            <@ZKUI.FormRow>
                <@ZKUI.FormCol title="acc_readerDefine_readerName" required="true">
                    <@ZKUI.Input name="readerName" value="${item.readerName!}" maxlength="100" required="true"/>
                </@ZKUI.FormCol>
                <@ZKUI.FormCol title="base_area_name">
                    <@ZKUI.Input name="areaName" value="${item.areaName!}" maxlength="100"/>
                </@ZKUI.FormCol>
            </@ZKUI.FormRow>
            
            <@ZKUI.FormRow>
                <@ZKUI.FormCol title="acc_exception_enterTime" required="true">
                    <@ZKUI.DateTimeInput name="enterTime" value="${item.enterTime!}" required="true"/>
                </@ZKUI.FormCol>
                <@ZKUI.FormCol title="acc_exception_exitTime">
                    <@ZKUI.DateTimeInput name="exitTime" value="${item.exitTime!}"/>
                </@ZKUI.FormCol>
            </@ZKUI.FormRow>
            
            <@ZKUI.FormRow>
                <@ZKUI.FormCol title="acc_exception_subject" required="true">
                    <@ZKUI.Combo name="subject" value="${item.subject!}" required="true">
                        <option value="异常进出"><@i18n 'acc_exception_subject_abnormalAccess'/></option>
                        <option value="迟到"><@i18n 'acc_exception_subject_late'/></option>
                    </@ZKUI.Combo>
                </@ZKUI.FormCol>
                <@ZKUI.FormCol title="acc_exception_status" required="true">
                    <@ZKUI.Combo name="exceptionStatus" value="${item.exceptionStatus!}" required="true">
                        <option value="未闭环"><@i18n 'acc_exception_status_unclosed'/></option>
                        <option value="已返回"><@i18n 'acc_exception_status_returned'/></option>
                    </@ZKUI.Combo>
                </@ZKUI.FormCol>
            </@ZKUI.FormRow>
            
            <@ZKUI.FormRow>
                <@ZKUI.FormCol title="acc_exception_sendTime">
                    <@ZKUI.DateTimeInput name="sendTime" value="${item.sendTime!}" readonly="true"/>
                </@ZKUI.FormCol>
                <@ZKUI.FormCol title="acc_exception_sendStatus">
                    <@ZKUI.Combo name="status" value="${item.status!}" readonly="true">
                        <option value="0"><@i18n 'acc_exception_status_unsent'/></option>
                        <option value="1"><@i18n 'acc_exception_status_sent'/></option>
                        <option value="2"><@i18n 'acc_exception_status_failed'/></option>
                    </@ZKUI.Combo>
                </@ZKUI.FormCol>
            </@ZKUI.FormRow>
            
            <@ZKUI.FormRow>
                <@ZKUI.FormCol title="acc_exception_errorMessage" colspan="2">
                    <@ZKUI.TextArea name="errorMessage" value="${item.errorMessage!}" rows="3" readonly="true"/>
                </@ZKUI.FormCol>
            </@ZKUI.FormRow>
            
            <@ZKUI.FormRow>
                <@ZKUI.FormCol title="common_remark" colspan="2">
                    <@ZKUI.TextArea name="remark" value="${item.remark!}" rows="3"/>
                </@ZKUI.FormCol>
            </@ZKUI.FormRow>
        </@ZKUI.FormItem>
        
        <@ZKUI.FormButton>
            <@ZKUI.Button type="submit" text="common_op_save" img="comm_save.png"/>
            <@ZKUI.Button type="button" text="common_op_cancel" img="comm_cancel.png" onclick="closeDialog()"/>
            <#if editType == "edit">
                <@ZKUI.Button type="button" text="acc_exception_sendNotification" img="comm_send.png" onclick="sendNotification()"/>
                <#if item.status?? && item.status == 2>
                    <@ZKUI.Button type="button" text="acc_exception_resend" img="comm_resend.png" onclick="resendNotification()"/>
                </#if>
            </#if>
        </@ZKUI.FormButton>
    </@ZKUI.Form>
</@ZKUI.FormBox>

<script type="text/javascript">
    $(document).ready(function() {
        // 表单验证
        $("#accExceptionRecordForm").validate({
            rules: {
                pin: {
                    required: true,
                    maxlength: 50
                },
                name: {
                    required: true,
                    maxlength: 100
                },
                deptName: {
                    required: true,
                    maxlength: 100
                },
                readerName: {
                    required: true,
                    maxlength: 100
                },
                enterTime: {
                    required: true
                },
                subject: {
                    required: true
                },
                exceptionStatus: {
                    required: true
                }
            },
            messages: {
                pin: {
                    required: "<@i18n 'common_validate_required'/>",
                    maxlength: "<@i18n 'common_validate_maxlength'/>"
                },
                name: {
                    required: "<@i18n 'common_validate_required'/>",
                    maxlength: "<@i18n 'common_validate_maxlength'/>"
                },
                deptName: {
                    required: "<@i18n 'common_validate_required'/>",
                    maxlength: "<@i18n 'common_validate_maxlength'/>"
                },
                readerName: {
                    required: "<@i18n 'common_validate_required'/>",
                    maxlength: "<@i18n 'common_validate_maxlength'/>"
                },
                enterTime: {
                    required: "<@i18n 'common_validate_required'/>"
                },
                subject: {
                    required: "<@i18n 'common_validate_required'/>"
                },
                exceptionStatus: {
                    required: "<@i18n 'common_validate_required'/>"
                }
            },
            submitHandler: function(form) {
                openMessage(msgType.loading);
                $(form).ajaxSubmit({
                    success: function(result) {
                        dealRetResult(eval(result), function() {
                            closeMessage();
                            closeDialog();
                            if (parent.ZKUI && parent.ZKUI.Grid) {
                                parent.ZKUI.Grid.reloadGrid("${gridName!}");
                            }
                        });
                    },
                    error: function() {
                        closeMessage();
                        showMessage("<@i18n 'common_op_failed'/>", msgType.error);
                    }
                });
            }
        });
    });
    
    /**
     * 发送通知
     */
    function sendNotification() {
        var id = $("input[name='id']").val();
        if (!id) {
            showMessage("<@i18n 'common_data_notFound'/>", msgType.warning);
            return;
        }
        
        confirmMessage(function(result) {
            if (result) {
                openMessage(msgType.loading);
                $.ajax({
                    url: "accExceptionRecord.do?sendNotification",
                    type: "post",
                    data: { ids: id },
                    success: function(result) {
                        dealRetResult(eval(result), function() {
                            closeMessage();
                            // 刷新表单数据
                            location.reload();
                        });
                    }
                });
            }
        }, "<@i18n 'acc_exception_confirm_sendNotification'/>");
    }
    
    /**
     * 重新发送通知
     */
    function resendNotification() {
        var id = $("input[name='id']").val();
        if (!id) {
            showMessage("<@i18n 'common_data_notFound'/>", msgType.warning);
            return;
        }
        
        confirmMessage(function(result) {
            if (result) {
                openMessage(msgType.loading);
                $.ajax({
                    url: "accExceptionRecord.do?resendFailed",
                    type: "post",
                    data: { ids: id },
                    success: function(result) {
                        dealRetResult(eval(result), function() {
                            closeMessage();
                            // 刷新表单数据
                            location.reload();
                        });
                    }
                });
            }
        }, "<@i18n 'acc_exception_confirm_resendFailed'/>");
    }
    
    /**
     * 关闭对话框
     */
    function closeDialog() {
        if (parent.closeDialog) {
            parent.closeDialog();
        } else {
            window.close();
        }
    }
</script>
