<#assign gridName="accExceptionRecordGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}" style="height:100%;width:100%">
    <@ZKUI.Searchbar>
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="pin" maxlength="30" title="pers_person_pin" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="name" maxlength="30" title="pers_person_name" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="deptName" maxlength="30" title="pers_dept_deptName" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
        <@ZKUI.SearchBelow>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="readerName" maxlength="30" title="acc_readerDefine_readerName" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Combo name="subject" maxlength="30" empty="true" title="acc_exception_subject" readonly="true">
                        <option value="1"><@i18n 'acc_exception_subject_abnormalAccess'/></option>
                        <option value="2"><@i18n 'acc_exception_subject_late'/></option>
                    </@ZKUI.Combo>
                </td>
                <td valign="middle">
                    <@ZKUI.Combo name="exceptionStatus" maxlength="30" empty="true" title="acc_exception_status" readonly="true">
                        <option value="2"><@i18n 'acc_exception_status_unclosed'/></option>
                        <option value="1"><@i18n 'acc_exception_status_returned'/></option>
                    </@ZKUI.Combo>
                </td>
            </tr>
            <tr>
                <td valign="middle">
                    <@ZKUI.Combo name="status" maxlength="30" empty="true" title="acc_exception_sendStatus" readonly="true">
                        <option value="1"><@i18n '发送成功'/></option>
                        <option value="2"><@i18n '发送失败'/></option>
                        <option value="3"><@i18n '不需要发送'/></option>
                    </@ZKUI.Combo>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="startTime" title="common_startTime" type="datetime"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="endTime" title="common_endTime" type="datetime"/>
                </td>
            </tr>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="sendStartTime" title="acc_exception_sendStartTime" type="datetime"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="sendEndTime" title="acc_exception_sendEndTime" type="datetime"/>
                </td>
<!--                <td valign="middle">-->
<!--                    <@ZKUI.Input name="areaName" maxlength="30" title="base_area_name" type="text"/>-->
<!--                </td>-->
            </tr>
        </@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
        <@ZKUI.ToolItem id="refresh" type="refresh" permission="acc:exceptionRecord:refresh"></@ZKUI.ToolItem>
        <@ZKUI.ToolItem id="accExceptionRecord.do?export" type="export" text="common_op_export" img="comm_export.png" permission="acc:exceptionRecord:export"></@ZKUI.ToolItem>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid pageList="true" limitCount="200000" vo="com.zkteco.zkbiosecurity.acc.vo.AccExceptionRecordItem" query="accExceptionRecord.do?list" fixColumn="8" showColumns="!deptCode,!devId,!readerId,!creator,!updater"/>
</@ZKUI.GridBox>

<script type="text/javascript">
    var gridName = "${gridName}";
    
    /**
     * 发送通知
     */
    function sendNotification(id, bar) {
        var selectedIds = ZKUI.Grid.getSelectedIds(gridName);
        if (selectedIds.length == 0) {
            showMessage("<@i18n 'common_prompt_selectData'/>", msgType.warning);
            return;
        }
        
        confirmMessage(function(result) {
            if (result) {
                openMessage(msgType.loading);
                $.ajax({
                    url: "accExceptionRecord.do?sendNotification",
                    type: "post",
                    data: { ids: selectedIds.join(",") },
                    success: function(result) {
                        dealRetResult(eval(result), function() {
                            closeMessage();
                            ZKUI.Grid.reloadGrid(gridName);
                        });
                    }
                });
            }
        }, "<@i18n 'acc_exception_confirm_sendNotification'/>");
    }
    
    /**
     * 重新发送失败记录
     */
    function resendFailed(id, bar) {
        var selectedIds = ZKUI.Grid.getSelectedIds(gridName);
        if (selectedIds.length == 0) {
            showMessage("<@i18n 'common_prompt_selectData'/>", msgType.warning);
            return;
        }
        
        confirmMessage(function(result) {
            if (result) {
                openMessage(msgType.loading);
                $.ajax({
                    url: "accExceptionRecord.do?resendFailed",
                    type: "post",
                    data: { ids: selectedIds.join(",") },
                    success: function(result) {
                        dealRetResult(eval(result), function() {
                            closeMessage();
                            ZKUI.Grid.reloadGrid(gridName);
                        });
                    }
                });
            }
        }, "<@i18n 'acc_exception_confirm_resendFailed'/>");
    }
    
    /**
     * 清除所有数据
     */
    function clearAllData(id, bar) {
        deleteConfirm(function(result) {
            if (result) {
                openMessage(msgType.loading);
                $.ajax({
                    url: id,
                    type: "post",
                    success: function(result) {
                        dealRetResult(eval(result), function() {
                            closeMessage();
                            ZKUI.Grid.reloadGrid(gridName);
                        });
                    }
                });
            }
        }, "<@i18n 'common_prompt_sureToDelAll'/>");
    }
    
    /**
     * 显示统计信息
     */
    function showStatistics(id, bar) {
        $.ajax({
            url: "accExceptionRecord.do?statistics",
            type: "post",
            success: function(result) {
                var data = eval(result);
                if (data.success) {
                    var stats = data.data;
                    var content = "<div style='padding: 20px;'>" +
                        "<h3><@i18n 'acc_exception_statistics'/></h3>" +
                        "<table class='table table-bordered'>" +
                        "<tr><td><@i18n 'acc_exception_totalCount'/>:</td><td>" + stats.totalCount + "</td></tr>" +
                        "<tr><td><@i18n 'acc_exception_unsentCount'/>:</td><td>" + stats.unsentCount + "</td></tr>" +
                        "<tr><td><@i18n 'acc_exception_sentCount'/>:</td><td>" + stats.sentCount + "</td></tr>" +
                        "<tr><td><@i18n 'acc_exception_failedCount'/>:</td><td>" + stats.failedCount + "</td></tr>" +
                        "<tr><td><@i18n 'acc_exception_unclosedCount'/>:</td><td>" + stats.unclosedCount + "</td></tr>" +
                        "<tr><td><@i18n 'acc_exception_returnedCount'/>:</td><td>" + stats.returnedCount + "</td></tr>" +
                        "</table>" +
                        "</div>";
                    
                    showDialog({
                        title: "<@i18n 'acc_exception_statistics'/>",
                        content: content,
                        width: 400,
                        height: 300
                    });
                } else {
                    showMessage(data.msg, msgType.error);
                }
            }
        });
    }
    
    /**
     * 更新异常状态
     */
    function updateExceptionStatus(id, status) {
        $.ajax({
            url: "accExceptionRecord.do?updateStatus",
            type: "post",
            data: { id: id, exceptionStatus: status },
            success: function(result) {
                dealRetResult(eval(result), function() {
                    ZKUI.Grid.reloadGrid(gridName);
                });
            }
        });
    }
    
    /**
     * 自定义行操作按钮
     */
    function customRowOperations(rowData) {
        var operations = [];
        
        // 如果是未闭环状态，显示"标记为已返回"按钮
        if (rowData.exceptionStatus == '未闭环') {
            operations.push({
                text: "<@i18n 'acc_exception_markAsReturned'/>",
                onclick: "updateExceptionStatus('" + rowData.id + "', '已返回')"
            });
        }
        
        // 如果发送失败，显示"重新发送"按钮
        if (rowData.status == 2) {
            operations.push({
                text: "<@i18n 'acc_exception_resend'/>",
                onclick: "resendFailed('" + rowData.id + "')"
            });
        }
        
        return operations;
    }
</script>
