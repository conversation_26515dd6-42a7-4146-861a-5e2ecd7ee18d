<#assign gridName="accAntiPassbackGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
			<tr>
				<td valign="middle">
					<@ZKUI.Input name="name" maxlength="30" title="common_name" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="deviceName" maxlength="30" title="common_dev_name" type="text"/>
				</td>
			</tr>
    	</@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="acc:antiPassback:refresh"/>
    	<@ZKUI.ToolItem id="accAntiPassback.do?edit" text="common_op_new" width="520" height="500" img="comm_add.png" action="commonAdd" permission="acc:antiPassback:add"/>
    	<@ZKUI.ToolItem id="accAntiPassback.do?del&deviceNames=(deviceName)" text="common_op_del" img="comm_del.png" action="commonDel" permission="acc:antiPassback:del"/>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccAntiPassbackItem" query="accAntiPassback.do?list"/>
</@ZKUI.GridBox>