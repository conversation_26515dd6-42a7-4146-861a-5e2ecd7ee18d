<#assign formId = "${uuid!}">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
<form action='/accAntiPassback.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
	<input type='hidden' name='id' value='${(item.id)!}'/>
	<table class='tableStyle'>
		<tr>
			<th>
				<label><@i18n 'common_name'/></label><span class="required">*</span>
			</th>
			<td>
				<input type="text" value="${(item.name)!}" name="name" style="width:261px">
			</td>
		</tr>
		<tr>
			<th>
				<label><@i18n 'common_dev_name'/></label><span class="required">*</span>
			</th>
			<td>
				<#if (item.id)??>
				<input type="text" id="deviceName" name="deviceName" style="width:261px" readonly="readonly" value="${(item.deviceName)!}"/>
				<#else>
				<input type="text" id="deviceName" name="deviceName" style="width:261px" readonly="true" placeholder="<@i18n 'common_op_clickChoice'/>" value="${(item.deviceName)!}"/>
				</#if>
				<input type="hidden" id="deviceId" name="deviceId" onchange="changeDevice(this.value)" value="${(item.deviceId)!}"/>
			</td>
		</tr>
		<tr class="setRules1">
			<th>
				<label><@i18n 'acc_apb_rules'/></label><span class="required">*</span>
			</th>
			<td>
				<@ZKUI.Combo name="apbRule" id="apbRule${uuid!}" width="268" value="${(item.apbRule)!}" readonly="true" title="acc_apb_rules" hideLabel="true" empty="true" >
				</@ZKUI.Combo>
			</td>
		</tr>
		<tr class="setRules2">
			<th>
				<label><@i18n 'acc_apb_rules'/></label>
			</th>
			<td>
				<@ZKUI.Combo name="apbRuleType" class="apbRuleType" empty="false" width="268" id="apbRuleType${uuid}" hideLabel="true" empty="true">
				</@ZKUI.Combo>
			</td>
		</tr>
	</table>
	<!--读头1-->
	<fieldset style="margin-top: 10px" class="setType1">
		<legend><@i18n 'acc_interlock_group1'/></legend>
		<textarea style="width: 100%; height: 60px; cursor:pointer" onclick="selectReader('1');return false;" readonly class="group1Names" id="readerGroup1Names" placeholder="<@i18n 'common_op_clickChoice'/>" name="group1Names">${(item.group1Names)!}</textarea>
		<input type="hidden" id="readerGroup1Ids" name="group1Ids" value="${(item.group1Ids)!}">
		<div style="width: 100%; margin-top: 10px">
			<div style="float: right;">
				<button class="button-form" onclick="selectClean('1');return false;"><@i18n 'common_op_clearData'/></button>
			</div>
		</div>
	</fieldset>
	<!--读头2-->
	<fieldset style="margin-top: 10px" class="setType1">
		<legend><@i18n 'acc_interlock_group2'/></legend>
		<textarea style="width: 100%; height: 60px; cursor:pointer" onclick="selectReader('2');return false;" readonly class="group2Names" id="readerGroup2Names" placeholder="<@i18n 'common_op_clickChoice'/>" name="group2Names">${(item.group2Names)!}</textarea>
		<input type="hidden" id="readerGroup2Ids" name="group2Ids" value="${(item.group2Ids)!}">
		<div style="width: 100%; margin-top: 10px">
			<div style="float: right;">
				<button class="button-form" onclick="selectClean('2');return false;"><@i18n 'common_op_clearData'/></button>
			</div>
		</div>
	</fieldset>

	<!--门1-->
	<fieldset style="margin-top: 10px" class="setType2">
		<legend><@i18n 'acc_interlock_group1'/></legend>
		<textarea style="width: 100%; height: 60px; cursor:pointer" onclick="selectDoor('1');return false;" readonly class="group1Names" id="doorGroup1Names" placeholder="<@i18n 'common_op_clickChoice'/>" name="group1Names">${(item.group1Names)!}</textarea>
		<input type="hidden" id="doorGroup1Ids" name="group1Ids" value="${(item.group1Ids)!}">
		<div style="width: 100%; margin-top: 10px">
			<div style="float: right;">
				<button class="button-form" onclick="selectClean('1');return false;"><@i18n 'common_op_clearData'/></button>
			</div>
		</div>
	</fieldset>
	<!--门2-->
	<fieldset style="margin-top: 10px" class="setType2">
		<legend><@i18n 'acc_interlock_group2'/></legend>
		<textarea style="width: 100%; height: 60px; cursor:pointer" onclick="selectDoor('2');return false;" readonly class="group2Names" id="doorGroup2Names" placeholder="<@i18n 'common_op_clickChoice'/>" name="group2Names">${(item.group2Names)!}</textarea>
		<input type="hidden" id="doorGroup2Ids" name="group2Ids" value="${(item.group2Ids)!}">
		<div style="width: 100%; margin-top: 10px">
			<div style="float: right;">
				<button class="button-form" onclick="selectClean('2');return false;"><@i18n 'common_op_clearData'/></button>
			</div>
		</div>
	</fieldset>
</form>
<script type='text/javascript'>
    jQuery.validator.addMethod("validGlobalApb", function(value, element){
        var setGlobalApb = true;
        var devId = $("#${formId} #deviceId").val();
        if (devId != "") {
            $.ajax({
                type: "post",
                url: "accAntiPassback.do?validGlobalApb",
                dataType: "json",
                async :false,
                data:
				{
					'deviceId' : devId
				} ,
                success: function (result)
                {
                    setGlobalApb = result.data;
                },
                error: function (XMLHttpRequest, textStatus, errorThrown)
                {
                    messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
                }
            });
		}
        return setGlobalApb;
    },function(){
        return  "<@i18n 'acc_apb_conflictWithGapb'/>";
    });
    function validDetermineApb(devId){
        if (devId != "") {
            $.ajax({
                type: "post",
                url: "accAntiPassback.do?validDetermineApb",
                dataType: "json",
                async :false,
                data:
				{
					'devId' : devId
				} ,
                success: function (result)
                {
					if(result.data == false){
						$(':input[name="apbRule"]').rules("add", {required : true});
						ZKUI.Combo.get("apbRuleType${uuid}").combo.setComboValue("");
						$("#readerGroup1Names").rules("remove");
						$("#readerGroup2Names").rules("remove");
						$("#doorGroup1Names").rules("remove");
						$("#doorGroup2Names").rules("remove");
						$(".setRules1").show();
						$(".setRules2").hide();
						$(".setType2 input").attr("disabled", "disabled");
						$(".setType1 input").attr("disabled", "disabled");
						$(".setType2 textarea").attr("disabled", "disabled");
						$(".setType1 textarea").attr("disabled", "disabled");
						$(".setType1").hide();
						$(".setType2").hide();
					}else{
						$(".setRules2").show();
						$(".setRules1").hide();
						validApbCount(devId);
						<#if (item.id)??>
						ZKUI.Combo.get("apbRuleType${uuid}").combo.disable(true);
						</#if>
						$(':input[name="apbRule"]').rules("remove");
						$("#readerGroup1Names").rules("add", {required : true});
						$("#readerGroup2Names").rules("add", {required : true});
						$("#doorGroup1Names").rules("add", {required : true});
						$("#doorGroup2Names").rules("add", {required : true});
					}
                },
                error: function (XMLHttpRequest, textStatus, errorThrown)
                {
                    messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
                }
            });
		}

   	}

    function validApbCount(devId){
        if (devId != "") {
            $.ajax({
                type: "post",
                url: "accAntiPassback.do?validApbCount",
                dataType: "json",
                async :false,
                data:
				{
					'deviceId' : devId
				} ,
                success: function (data)
                {
					var count=data.data;
					var doorCount = count[0];
					var readerCount = count[1];
					//var value = $(".apbRuleType option:selected").val();
					var apbRuleTypeCombo = ZKUI.Combo.get('apbRuleType${uuid}');
       				apbRuleTypeCombo.combo.clearAll();
        			apbRuleTypeCombo.clear();
					if (doorCount <= 1){
						apbRuleTypeCombo.combo.addOption([{
							value: 1,
							text: "<@i18n 'acc_apb_readerHead'/>"
                		}]);
                		apbRuleTypeCombo.combo.setComboValue("1");
                		typeChange("1");
                		type = "1";
					}else if(readerCount <= 1){
						apbRuleTypeCombo.combo.addOption([{
							value: 0,
							text: "<@i18n 'acc_apb_door'/>"
                		}]);
                		apbRuleTypeCombo.combo.setComboValue("0");
						typeChange("2");
						type = "2";
					}else if(readerCount <=1 && doorCount <= 1){
						return  "<@i18n 'acc_dev_notApb'/>";
					}else{
						apbRuleTypeCombo.combo.addOption([{
								value: 1,
								text: "<@i18n 'acc_apb_readerHead'/>"
							},{
								value: 0,
								text: "<@i18n 'acc_apb_door'/>"
							}
                		]);
                		var apbRuleType = "${(item.apbRuleType)!'1'}"
                		apbRuleTypeCombo.combo.setComboValue(apbRuleType);
                		if(apbRuleType==0){
							typeChange("2");
						}else{
							typeChange("1");
						}
					}
                },
                error: function (XMLHttpRequest, textStatus, errorThrown)
                {
                    messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
                }
            });
		}

   	}

    $(function() {
        var opts = {
            path: "skip.do?page=acc_antiPassback_accAntiPassbackSelectDeviceRadio",
            width: 920,
            height: 480,
            title: "",
            onSure:"selectDeviceHandler"
        };
        selectContent("#${formId} #deviceName", opts);//给按钮注册选人事件

        $('#${formId}').validate( {
            debug : true,
            rules : {
				"name":{
					required : true,
					unInputChar : true,
					maxlength : 30,
					overRemote : ["accAntiPassback.do?validName", "${(item.name)!}"]
				},
                "deviceName":{
                    required : true,
                    validGlobalApb: true
                }
            },
            submitHandler : function()
            {
            	<@submitHandler/>
            }
        });
        changeDevice("${(item.deviceId)!}", true)
    });
   jQuery.validator.addMethod("apbRule", function(value){
		if(value == ""){
			return false;
		}else{
			return true;
		}
    },function(){
        return  "<@i18n 'att_exception_cellRequired'/>";
    });
	<#if (item.id)??>
		$("#${formId} #deviceName").attr("disabled", true);
		<#if item.apbRuleType??>
			$(".setRules1").hide();
			$(".setRules2").show();
			$(':input[name="apbRule"]').rules("remove");
			$("#readerGroup1Names").rules("add", {required : true});
			$("#readerGroup2Names").rules("add", {required : true});
			$("#doorGroup1Names").rules("add", {required : true});
			$("#doorGroup2Names").rules("add", {required : true});
		<#else>
			$(".setRules2").hide();
			$(".setRules1").show();
			$(".setType1").hide();
			$(".setType2").hide();
			ZKUI.Combo.get("apbRule${uuid!}").load("accAntiPassback.do?getRule&deviceId=${item.deviceId}");
			$(':input[name="apbRule"]').rules("add", {required : true});
			$("#readerGroup1Names").rules("remove");
			$("#readerGroup2Names").rules("remove");
			$("#doorGroup1Names").rules("remove");
			$("#doorGroup2Names").rules("remove");
		</#if>
	</#if>


    $("#${formId} #deviceId").change(function(){
        if($("#${formId} #deviceId").val() != "")
        {
            ZKUI.Combo.get("apbRule${uuid!}").load("accAntiPassback.do?getRule&deviceId="+$("#${formId} #deviceId").val());
        }
    });

	function selectDeviceHandler(value,text,event){
		var oldId = $("#${formId} #deviceId").val();
		$("#${formId} #deviceId").val(value);
		if (oldId != value)
		{
			$("#${formId} #deviceId").change();
		}
		$("#${formId} #deviceName").val(text);
		$("#${formId} #deviceName").valid();
	}
	 var type,value;
	 ZKUI.Combo.get("apbRuleType${uuid}").combo.attachEvent("onChange",function(value,text){
		if(value == "1"){
			typeChange("1");
			type = "1";
		}else if(value ="0"){
			typeChange("2");
			type = "2";
		}
	});

	<#if (item.id)??>
		<#if item.apbRuleType??&&value??>
			<#if value == "0">
				$(".setType2").show();
				$(".setType1").hide();
			<#else>
				$(".setType1").show();
				$(".setType2").hide();
			</#if>
		</#if>
	</#if>



	function typeChange(val) {
		switch (val + '') {
			case '1':
				$(".setType1").show();
				$(".setType2").hide();
				$(".setType1 input").removeAttr("disabled", "disabled");
				$(".setType2 input").attr("disabled", "disabled");
				$(".setType1 textarea").removeAttr("disabled", "disabled");
				$(".setType2 textarea").attr("disabled", "disabled");
				$("#doorGroup1Ids").val("");
				$("#doorGroup2Ids").val("");
				$("#doorGroup1Names").val("");
				$("#doorGroup2Names").val("");
				break;
			case '2':
				$(".setType2").show();
				$(".setType1").hide();
				$(".setType2 input").removeAttr("disabled", "disabled");
				$(".setType1 input").attr("disabled", "disabled");
				$(".setType2 textarea").removeAttr("disabled", "disabled");
				$(".setType1 textarea").attr("disabled", "disabled");
				$("#readerGroup1Ids").val("");
				$("#readerGroup2Ids").val("");
				$("#readerGroup1Names").val("");
				$("#readerGroup2Names").val("");
				break;
		}
	}
	function changeDevice(devId, noClear) {
		if(devId=="") {
			$(".apbRuleType").attr("disabled", "disabled");
			$(".setType1").hide();
			$(".setType2").hide();
			$(".setRules2").hide();
		}else {
			validDetermineApb(devId);
			$(".apbRuleType").removeAttr("disabled", "disabled");
			if(noClear == null || !noClear) {
				$("input[name='group1Ids']").val("");
				$(".group1Names").html("");
				$("input[name='group2Ids']").val("");
				$(".group2Names").html("");
			}
		}
	}
	// 选读头
	function selectReader(groupStr) {
		selectCommon("selectReader", groupStr);
	}
	// 选门
	function selectDoor(groupStr) {
		selectCommon("selectDoor", groupStr);
	}
	//清除所选数据
	function selectClean(groupStr) {
		group = groupStr;
		var apbRuleType = ZKUI.Combo.get('apbRuleType${uuid}').combo.getSelected();
		if(apbRuleType == 0){
			if(group == "1") {
				if($("#doorGroup1Names").val() == "") {
					return  "<@i18n 'common_prompt_dataNull'/>";
				}
				$("#doorGroup1Names").val("");
				$("#doorGroup1Ids").val("");
			}else if(group == "2") {
				if($("#doorGroup2Names").val() == "") {
					return  "<@i18n 'common_prompt_dataNull'/>";
				}
				$("#doorGroup2Names").val("");
				$("#doorGroup2Ids").val("");
			}
        }else {
			if(group == "1") {
				if($("#readerGroup1Names").val() == "") {
					return  "<@i18n 'common_prompt_dataNull'/>";
				}
				$("#readerGroup1Names").val("");
				$("#readerGroup1Ids").val("");
			}else if(group == "2") {
				if($("#readerGroup2Names").val() == "") {
					return  "<@i18n 'common_prompt_dataNull'/>";
				}
				$("#readerGroup2Names").val("");
				$("#readerGroup2Ids").val("");
			}
        }
	}
	var group;
	function selectCommon(addr,groupStr) {
		group = groupStr;
		var selectStrDoor  = $("#doorGroup"+groupStr+"Ids").val();
		var selectStrReader  = $("#readerGroup"+groupStr+"Ids").val();
		var deviceId = $("#deviceId").val();
		var id = $("input[name='id']").val();
		var selectStr;
		if(addr == "selectDoor"){
			if (selectStrDoor == "" || id != ""){
					var filterId = groupStr=="1" ? "2" : "1";
			}else{
				var filterId = groupStr=="1" ? "1" : "2";
			}
		   selectStr  = $("#doorGroup"+filterId+"Ids").val();
		}else if(addr =="selectReader"){
			if (selectStrReader == "" || id != ""){
				var filterId = groupStr=="1" ? "2" : "1";
			}else{
				var filterId = groupStr=="1" ? "1" : "2";
			}
		   selectStr  = $("#readerGroup"+filterId+"Ids").val();
		}
		var notInId = selectStr;
		if(deviceId!=null && deviceId.trim()!="") {
			var path = "accAntiPassback.do?" + addr + "&deviceId=" + deviceId + "&notInId=" + notInId + "&group=" + groupStr;
			var opts = {
				path:path,
				width: 880,
				height: 510,
				title:"<@i18n 'acc_globalInterlock_addGroup'/>",
				gridName:"gridBox"
			};
			DhxCommon.createWindow(opts);
		}
	}

	var apbRuleType = ZKUI.Combo.get('apbRuleType${uuid}').combo.getSelected();
	if (apbRuleType==0){
		type="2";
	}else{
		type="1";
	}

	function selectAddrHandler(value,text) {
        var typeClass = "setType" + type;
        if(group == "1") {
            $("."+ typeClass +" input[name='group1Ids']").val(value);
            $("."+typeClass+" .group1Names").val(text);
            validByOtherNotNull("."+typeClass+" input[name='group2Ids']");
        }else if(group == "2") {
            $("."+typeClass+" input[name='group2Ids']").val(value);
            $("."+typeClass+" .group2Names").val(text);
            validByOtherNotNull("."+typeClass+" input[name='group1Ids']");
        }
    }

    function validByOtherNotNull(id) {
        if($(id).val().trim() != "") {
            $('#${formId}').valid();
        }
    }
</script>
</#macro>