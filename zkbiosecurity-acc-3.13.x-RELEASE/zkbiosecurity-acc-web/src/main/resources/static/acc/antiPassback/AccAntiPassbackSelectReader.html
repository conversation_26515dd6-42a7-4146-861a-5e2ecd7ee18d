<@ZKUI.SelectContent gridName="antiPassbackSelectReaderContent" showColumns="checkbox,name,doorName,deviceAlias" textField="name" vo="com.zkteco.zkbiosecurity.acc.vo.AccAntiPassbackSelectReaderItem" onSure="selectAddrHandler"  query="accAntiPassback.do?selectReaderList&deviceId=" + deviceId + "&notInId=" + notInId>
<@ZKUI.Searchbar>
<@ZKUI.SearchTop>
<tr>
    <td valign="middle">
        <@ZKUI.Input name="name"  maxlength="30" title="acc_readerDefine_readerName" type="text"/>
    </td>
    <td valign="middle">
        <@ZKUI.Input name="doorName"  maxlength="30" title="acc_door_name" type="text"/>
    </td>
    <td valign="middle">
        <@ZKUI.Input name="deviceAlias"  maxlength="30" title="common_ownedDev" type="text"/>
    </td>
</tr>
</@ZKUI.SearchTop>
</@ZKUI.Searchbar>
</@ZKUI.SelectContent>
