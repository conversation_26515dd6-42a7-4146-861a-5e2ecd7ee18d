<#assign leftRTL = "left">
<#assign rightRTL = "right">
<#if enableRTL?? && enableRTL=="true">
    <!-- 定义全局对齐方式变量(针对阿拉伯页面) -->
    <#assign leftRTL = "right">
    <#assign rightRTL = "left">
</#if>
<div id="idAccDiv">
	<table>
		<tr>
            <!--门禁权限组-->
			<td valign="top" style="padding-top:3px;">
                <fieldset class="zk-border-color-gray" style="width: 360px; height: 300px; border: 1px solid #DDDDDD; margin: 0; padding: 0px;">
                    <legend style="border:0"><@i18n 'common_level_setting'/></legend>
                    <table style="width:100%;height:100%">
                        <tr>
                            <td style="width: 75%;" valign="top">
                                <input type="hidden" name="acc.personLevelIds" id="accPersonTimeLevelIds">
                                <input type="hidden" name="accPersonLevelFilterIds" id="accPersonLevelFilterIds">
                                <div id="sub_selectedAccLevel" style="display:none"></div>
                                <div id="sub_selectedAccLevelGrid" style="width:440px; height:240px; padding: 0px; line-height:22px;">
                                    <@ZKUI.GridBox gridName="selectPersonEditAccLevelGrid" style="height:240px;width:440">
                                    <@ZKUI.Grid showColumns="checkbox,levelName,startTime,endTime,clearDate" onXLE="initAccCellGridHandel" onEditCell="editAccCellGridHandel" nopaging="true"
                                    vo="com.zkteco.zkbiosecurity.acc.vo.AccPersonShowLevelItem" query="accPersonLevelByPerson.do?getPersonSelectLevelByPersonId&type=selected&personId=${personId!}"/>
                                    </@ZKUI.GridBox>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <table style="width:100%;height:100%">
                                    <tr>
                                        <td width="33%">
                                            <a href="javascript:void(0)" class="acc_person_level acc_person_level_noBorder" id="btn${uuid}"><@i18n 'common_op_add'/></a>
                                        </td>
                                        <td width="33%">
                                            <a href="javascript:void(0)" class="acc_person_level" onclick="checkAllAccLevelGrid(false)"><@i18n 'common_tree_selectAll'/></a>
                                        </td>
                                        <td width="33%">
                                            <a href="javascript:void(0)" class="acc_person_level acc_person_level_noBorder" onclick="checkUnAllAccLevelGrid()"><@i18n 'common_tree_unselectAll'/></a>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </fieldset>
			</td>
            <td valign="top" style="padding-${leftRTL!'left'}: 100px;">
                <table class="tableStyle">
                    <tr>
                        <th style="width: 55% !important;">
                            <span class="accTooltip">
                                <span class="acc_icv icv-ic_que zk-colors-fg-green" />
                                <span class="accCommonTipClass">
                                    <p class="warningColor"><@i18n 'acc_editPerson_adminTip'/></p>
                                </span>
                            </span>
                            <label><@i18n 'common_level_superuser'/></label>
                        </th>
                        <td>
                            <@ZKUI.Combo id="accSuperAuth${uuid}" empty="false" width="148" name="acc.superAuth"  hideLabel="true" value="${(item.superAuth)!0}">
                                <option value="0"><@i18n 'common_no'/></option>
                                <option value="15"><@i18n 'common_yes'/></option>
                            </@ZKUI.Combo>
                        </td>
                    </tr>
                    <tr>
                        <th class="accNoTip">
                            <label><@i18n 'pers_person_devOpAuth'/></label>
                        </th>
                        <td>
                            <@ZKUI.Combo empty="false" width="148" name="acc.privilege"  hideLabel="true" value="${(item.privilege)!}">
                                <option value="0"><@i18n 'common_level_generalUser'/></option>
                                <option value="14"><@i18n 'common_level_administrator'/></option>
                                <option value="2"><@i18n 'common_level_enroller'/></option>
                            </@ZKUI.Combo>
                        </td>
                    </tr>
                    <tr>
                        <th>
                            <span class="accTooltip">
                                <span class="acc_icv icv-ic_que zk-colors-fg-green" />
                                <span class="accCommonTipClass">
                                    <p class="warningColor"><@i18n 'acc_editPerson_delayPassageTip'/></p>
                                </span>
                            </span>
                            <label><@i18n 'acc_pers_delayPassage'/></label>
                        </th>
                        <td>
                            <@ZKUI.Input id="accDelayPassage${uuid}" hideLabel="true" name="acc.delayPassage" type="checkbox" eventCheck="true" value="${(item.delayPassage?string('true','false'))!'false'}"/>
                        </td>
                    </tr>
                    <tr>
                        <th>
                            <span class="accTooltip">
                                <span class="acc_icv icv-ic_que zk-colors-fg-green" />
                                <span class="accCommonTipClass">
                                    <p class="warningColor"><@i18n 'acc_editPerson_disabledTip'/></p>
                                </span>
                            </span>
                            <label><@i18n 'pers_person_disabled'/></label>
                        </th>
                        <td>
                            <@ZKUI.Input id="accDisabled${uuid}" hideLabel="true" name="acc.disabled" type="checkbox" eventCheck="true" value="${(item.disabled?string('true','false'))!'false'}"/>
                        </td>
                    </tr>
                    <tr>
                        <th class="accNoTip"><label><@i18n 'common_level_setValidTime'/></label></th>
                        <td>
                            <@ZKUI.Input id="accIsSetValidTime${uuid}" hideLabel="true" name="acc.isSetValidTime" type="checkbox" eventCheck="true" value="${(item.isSetValidTime?string('true','false'))!'false'}"/>
                        </td>
                    </tr>
                    <tr class="accSetValidTime" style="display:none">
                        <th class="accNoTip"><label><@i18n 'common_level_startTime'/></label><span class="required" id="spanStartTime">*</span></th>
                        <td>
                            <@ZKUI.Input type="datetime" id="startDate2${uuid}" endId="endDate2${uuid}" todayRange="start" value="${(item.startTime?string('yyyy-MM-dd HH:mm:ss'))!}" today="0" name="acc.startTime" hideLabel="true" notCheckRange="true" readonly="true"/>
                        </td>
                    </tr>
                    <tr class="accSetValidTime" style="display:none">
                        <th class="accNoTip"><label><@i18n 'common_level_endTime'/></label><span class="required" id="spanEndTime">*</span></th>
                        <td>
                            <@ZKUI.Input type="datetime" id="endDate2${uuid}" todayRange="end" value="${(item.endTime?string('yyyy-MM-dd HH:mm:ss'))!}" today="0" name="acc.endTime" hideLabel="true" notCheckRange="true" readonly="true"/>
                        </td>
                    </tr>
                    <tr class="accSetValidTime" style="display:none">
                        <td class="accNoTip" colspan="2" style="padding:5px">
                            <span class="warningImage"></span><span class="warningColor"><@i18n 'common_dev_validTimeTip' /></span>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </td>
		</tr>
	</table>
</div>
<style>
    #selectPersonEditAccLevelGrid.gridbox_dhx_web.gridbox table.hdr td div.hdrcell img {
        padding-left: 0px !important;
    }
</style>
<script type="text/javascript">

var oldLevelName = new Array();
function accLevelSelectHandler(value, text, event) {
    var rightGrid = this.rightGrid.grid;
    var valArr = value.split(",");
    var textArr = text.split(",");
    $.each(valArr,function(i,v){
        var showValue = textArr[i];
        var startTimeStr = rightGrid.cellById(v, 3).getValue();
        var endTimeStr = rightGrid.cellById(v, 4).getValue();
        ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].grid.addRow(v, [v,showValue,"",startTimeStr,endTimeStr,v], 0);
        ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].grid.setSizes(false);
    });
    accLevelSelectChange();
}

function accLevelSelectChange(){
    var accTimeLevelIdsArray = [];
    var accLevelFilterIds = "";
    var dhGrid = ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].grid;
    var rowCount = dhGrid.getRowsNum();
    for (var i = 0; i < dhGrid.rowsCol.length; i++) {
        var id = dhGrid.rowsCol[i].idd;
        accLevelFilterIds += id + ",";
        if (dhGrid.cells(id, 0).isChecked()) {
            var startTimeStr = dhGrid.cellById(id, 3).getValue();
            var endTimeStr = dhGrid.cellById(id, 4).getValue();
            if (endTimeStr != '' && startTimeStr != '' && endTimeStr <= startTimeStr) {
                messageBox({
                    messageType: "alert",
                    title: "<@i18n 'common_prompt_title'/>",
                    text: "<@i18n 'common_dsTime_timeValid4'/>"
                });
                dhGrid.cellById(id, 3).setValue("");
                dhGrid.cellById(id, 4).setValue("");
                return false;
            }
            var accTimeLevelIds = {
                "levelId": dhGrid.rowsCol[i].idd,
                "startTime": startTimeStr,
                "endTime": endTimeStr
            };
            accTimeLevelIdsArray.push(accTimeLevelIds);
        }
    }

    $("#accPersonTimeLevelIds").val(JSON.stringify(accTimeLevelIdsArray));
    $("#accPersonLevelFilterIds").val(accLevelFilterIds.replace(/,$/, ""));
}

function initAccCellGridHandel() {
    checkAllAccLevelGrid(true);
    // 编辑模式下，读取该人员的初始权限组
    if(isPersEditMode){
        var dhGrid = ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].grid;
        var rowCount = dhGrid.getRowsNum();
        var areaNames = "${(areaNames)!}";
        for (var i = 0; i < dhGrid.rowsCol.length; i++) {
            var id = dhGrid.rowsCol[i].idd;
            var chkCell = dhGrid.cells(id, 0);
            var areaName = dhGrid.cellById(id, 6).getValue();
            if(typeof(areaNames) == "undefined" || areaNames == "" || areaNames.indexOf(areaName) < 0)
            {
               chkCell.disabledF(true);
            }
            //chkCell.cell.style.pointerEvents="none";//阻止复选框点击事件，还原设置pointerEvents为all
            if(dhGrid.cells(id, 0).isChecked()) {
                oldLevelName.push(dhGrid.cellById(id, 1).getValue());
            }
        }
    }
}

function editAccCellGridHandel() {
    accLevelSelectChange();
    return true;
}

//initDefaultLevel();

function initDefaultLevel() {
    var dataGrid = ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].grid;
    dataGrid.enableMathEditing(true);
    dataGrid.enableEditEvents(true,false,true);
    dataGrid.setDateFormat("%Y-%m-%d %H:%i:%s"); //yyyy-MM-dd HH:mm:ss
    dataGrid.clearAll();
    if(!isPersEditMode){//新增
        dataGrid.addRow("${defaultLevel.id}", ["${defaultLevel.id}","${defaultLevel.name}","","","","${defaultLevel.id}"], 0);
        dataGrid.setSizes(false);
    }
    accLevelSelectChange();
}
function loadDeptAccLevel(deptId) {
    var existMasterLevel = "${defaultLevel}";
    $.ajax({
        url: "accPersonLevelByDept.do?getLevelListByDept&deptId=" + deptId,
        success: function (result) {
            var resultData = result[sysCfg.data];
            if (result[sysCfg.ret] == sysCfg.success && JSON.stringify(resultData) != JSON.stringify({})) {
                $("#sub_selectedAccLevel").html("");
                var dataGrid = ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].grid;
                dataGrid.clearAll();
                var flag  = false;
                $.each(result[sysCfg.data], function (k, v) {
                    if (existMasterLevel != "" && "${defaultLevel.id}" == k) {
                        flag = true;
                    }
                    $("#sub_selectedAccLevel").append("<label><input type='checkbox' name='level' value='" + k + "' onchange='accLevelSelectChange()' checked='checked' style='margin-right:3px;'><span title=" + v + ">" + v + "</span></label><br>");
                    dataGrid.addRow(k, [k, v, "", "", "", k], 0);
                });
                if (!flag && existMasterLevel != "" && !isPersEditMode) {
                    $("#sub_selectedAccLevel").append("<label><input type='checkbox' name='level' value='${defaultLevel.id}' onchange='accLevelSelectChange()' checked='checked' style='margin-right:3px;'><span title='${defaultLevel.name}'>" + "${defaultLevel.name}" + "</span></label><br>");
                    dataGrid.addRow("${defaultLevel.id}", ["${defaultLevel.id}", "${defaultLevel.name}", "", "", "", "${defaultLevel.id}"], 0);
                }
            } else {
                if (isPersEditMode) {//编辑模式下，不需要默认通用权限组
                    $("#sub_selectedAccLevel").html("");
                    var dataGrid = ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].grid;
                    dataGrid.clearAll();
                } else if(existMasterLevel != "") {
                    var dataGrid = ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].grid;
                    $("#sub_selectedAccLevel").append("<label><input type='checkbox' name='level' value='${defaultLevel.id}' onchange='accLevelSelectChange()' checked='checked' style='margin-right:3px;'><span title='${defaultLevel.name}'>" + "${defaultLevel.name}" + "</span></label><br>");
                    dataGrid.addRow("${defaultLevel.id}", ["${defaultLevel.id}", "${defaultLevel.name}", "", "", "", "${defaultLevel.id}"], 0);
                    dataGrid.setSizes(false);
                }
            }
            accLevelSelectChange();
        }
    });
}

function checkUnAllAccLevelGrid() {
    //ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].grid.uncheckAll();
    //ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].setCheckState(false);
    accChangeSelectStatus(false);
    accLevelSelectChange();
}

function checkAllAccLevelGrid(initFlag) {
    if(initFlag) {
        ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].grid.checkCurAll();
        ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].setCheckState(true);
    } else {
        accChangeSelectStatus(true);
    }
    //ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].grid.checkCurAll();
    //ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].setCheckState(true);
    accLevelSelectChange();
}
function accChangeSelectStatus(selectedFlag) {
    var dhGrid = ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].grid;
    var rowCount = dhGrid.getRowsNum();
    var areaNames = "${(areaNames)!}";
    for (var i = 0; i < dhGrid.rowsCol.length; i++) {
        var id = dhGrid.rowsCol[i].idd;
        var chkCell = dhGrid.cells(id, 0);
        var areaName = dhGrid.cellById(id, 6).getValue();
        if(typeof(areaNames) == "undefined" || areaNames == "" || areaNames.indexOf(areaName) < 0)
        {
           continue;
        } else {
            chkCell.setChecked(selectedFlag)
        }
    }
    $($(dhGrid .hdr).find("img[checkhead='true']")[0]).attr("checked",selectedFlag)
    if(selectedFlag) {
        $($(dhGrid .hdr).find("img[checkhead='true']")[0]).attr("src","public/controls/dhtmlx/skins/web/imgs//dhxgrid_web/item_chk1.gif");
    } else {
        $($(dhGrid .hdr).find("img[checkhead='true']")[0]).attr("src","public/controls/dhtmlx/skins/web/imgs//dhxgrid_web/item_chk0.gif");
    }
}


function checkAllSelectAccLevel() {
    var dhGrid = ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].grid;
    if($($(dhGrid.hdr).find("img[checkhead='true']")[0]).attr("checked")) {
        accChangeSelectStatus(false);
    } else {
        accChangeSelectStatus(true);
    }
    accLevelSelectChange();
    return true;
}

//ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].options.onCheckA = "checkAllSelectAccLevel";
$(ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].grid.hdr).find("img[checkhead='true']")[0].onclick = checkAllSelectAccLevel;

function deleteAccPersonDateClick(id) {
    var dhGrid = ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].grid;
    dhGrid.cellById(id, 3).setValue("");
    dhGrid.cellById(id, 4).setValue("");
    accLevelSelectChange();
}

function convertEditAccPersonClearDate(v) {
    return "<div class='icv-ic_clear acc_icv zk-colors-fg-green' onclick=\"deleteAccPersonDateClick('" + v + "')\" ></div>";
}

$(function () {
     $("#btn${uuid}").click(function () {
         var filterIds = $("#accPersonLevelFilterIds").val();
         var opts = {//选人控件弹窗配置
             path: "skip.do?page=acc_personLevelByPerson_personSelectLevelContent&notInId=" + filterIds + "&personId=''",//弹窗路径
             title: "<@i18n 'common_level_addPersonLevel'/>",//弹窗标题
             width: 1108,//窗口宽度
             height: 450,//窗口高度
             onSure: "accLevelSelectHandler",//回调事件
             callback: function () {
                 var sid = $(this.cell).find(".select_layout_box")[0];
                 if (sid) {
                     DhxCommon.initEvents(ZKUI.Select.get(sid.id), ZKUI.Select.suport_evts, opts);
                 }
             }
        };
        DhxCommon.createWindow(opts);
    });



    var $accSuperAuthCombo = ZKUI.Combo.get("accSuperAuth${uuid}").combo;
    //超级用户是屏蔽时间选择组件
    $accSuperAuthCombo.attachEvent("onChange", function(val){
        if(val == '15')
        {
            $("#accIsSetValidTime${uuid}").removeAttr("checked").change();
            $('#accIsSetValidTime${uuid}').attr("disabled","disabled");
            $('.accSetValidTime').css('display','none');

            $("#accDelayPassage${uuid}").removeAttr("checked").change();
            $('#accDelayPassage${uuid}').attr("disabled","disabled");
        }
        else
        {
            $('#accIsSetValidTime${uuid}').removeAttr("disabled");

            $('#accDelayPassage${uuid}').removeAttr("disabled");
        }
    });

    $accSuperAuthCombo.callEvent("onChange",["${(item.superAuth)!0}"]);

    //时间验证
    jQuery.validator.addMethod("accValidTimeStartValid", function(value, element){
        if($("input[name='acc.startTime']").val() < $("input[name='acc.endTime']").val()) {
            return true;
        } else {
            return false;
        }
    },function(){
        return "<@i18n 'common_search_dateMsg'/>";
    });

    $("#accIsSetValidTime${uuid}").change(function(){
        if(($(this).is(":checked")))
        {
            $(".accSetValidTime").show();
            $("input[name='acc.startTime']").rules("add", {accValidTimeStartValid:true});
            $("input[name='acc.startTime']").rules("add", {required:true});
            $("input[name='acc.endTime']").rules("add", {required:true});
        }
        else
        {
            $(".accSetValidTime").hide();
            $("input[name='acc.startTime']").rules("remove");
            $("input[name='acc.endTime']").rules("remove");
        }
    }).change();
});
/** 提交前做权限组时间校验 */
function validAccDataBeforeSubmit() {
    var flag = true;
    var dhGrid = ZKUI.Grid.GRID_PULL["selectPersonEditAccLevelGrid"].grid;
    var rowCount = dhGrid.getRowsNum();
    var text = "[<@i18n 'pers_person_accSet'/>]: ";
    for (var i = 0; i < dhGrid.rowsCol.length; i++) {
        var id = dhGrid.rowsCol[i].idd;
        if (dhGrid.cells(id, 0).isChecked()) {
            var startTimeStr = dhGrid.cellById(id, 3).getValue();
            var endTimeStr = dhGrid.cellById(id, 4).getValue();
            if (endTimeStr != '' && startTimeStr != '' && endTimeStr <= startTimeStr) {
                openMessage(msgType.warning, text + "<@i18n 'common_dsTime_timeValid4'/>");
                return false;
                break;
            } else if ((endTimeStr != '' && startTimeStr == '') || (endTimeStr == '' && startTimeStr != '')) {
                openMessage(msgType.warning, text + "<@i18n 'common_levelImport_timeNotNull'/>");
                return false;
                break;
            }
        }
    }
    return flag;
}
</script>
