<#assign leftRTL = "left">
<#assign rightRTL = "right">
<#if enableRTL?? && enableRTL=="true">
<!-- 定义全局对齐方式变量(针对阿拉伯页面) -->
<#assign leftRTL = "right">
<#assign rightRTL = "left">
</#if>
<script type="text/javascript">
    function checkAllAccLevel(){
        $("#sub_selectedAccLevel input").each(function(){
            if($(this)[0].disabled) {
               return true;
            } else {
                $(this)[0].checked=true;
            }
        });
        accLevelSelectChange();
    }
    function unCheckAllAccLevel() {
        $("#sub_selectedAccLevel input").each(function(){
            if($(this)[0].disabled) {
               return true;
            } else {
                $(this)[0].checked=false;
            }
        });
        accLevelSelectChange();
    }
</script>
<div id="idAccDiv">
    <table>
        <tr>
            <!--门禁权限组-->
            <td valign="top" style="padding-top:3px;">
                <fieldset class="zk-border-color-gray" style="width: 360px; height: 250px; border: 1px solid #DDDDDD; margin: 0; padding: 0px;">
                    <legend style="border:0"><@i18n 'common_level_setting'/></legend>
                    <table style="width:100%;height:100%">
                        <tr>
                            <td style="width: 100%;" valign="top">
                                <input type="hidden" name="acc.personLevelIds" id="accPersonLevelIds">
                                <input type="hidden" name="accPersonLevelFilterIds" id="accPersonLevelFilterIds">
                                <div id="sub_selectedAccLevel" class="personLevel" style="height:190px; padding-${leftRTL!'left'}:8px; overflow:auto; line-height:22px;">
                                    <#if tempList??><!-- tempList为编辑人员时，该人员拥有的权限组 -->
                                        <#if (tempList?size>0)>
                                            <#list tempList as level>
                                                <#if areaNames?contains(level.authAreaName) >
                                                <label><@ZKUI.Input hideLabel="true" type='checkbox' value='${level.id}' onchange='accLevelSelectChange()' checked='checked' style='margin-right:3px;'/>${level.name}</label><br>
                                                <#else>
                                                    <label><@ZKUI.Input hideLabel="true" type='checkbox' value='${level.id}' onchange='accLevelSelectChange()' disabled='disabled' checked='checked' style='margin-right:3px;'/>${level.name}</label><br>
                                                </#if>
                                            </#list>
                                        <#else> <!-- 没有选择权限组，显示提示图标 -->
                                            <span class='warningImage'></span><span class='warningColor'><@i18n 'acc_level_noneSelect'/></span>
                                        </#if>
                                    </#if>
                                </div>
                             </td>
                        </tr>
                        <tr>
                            <td>
                                <table style="width:100%;height:100%">
                                    <tr>
                                        <td width="33%">
                                            <a href="javascript:void(0)" class="acc_person_level acc_person_level_noBorder" id="btn${uuid}"><@i18n 'common_op_add'/></a>
                                        </td>
                                        <td width="33%">
                                            <a href="javascript:void(0)" class="acc_person_level" onclick="checkAllAccLevel()"><@i18n 'common_tree_selectAll'/></a>
                                        </td>
                                        <td width="33%">
                                            <a href="javascript:void(0)" class="acc_person_level acc_person_level_noBorder" onclick="unCheckAllAccLevel();"><@i18n 'common_tree_unselectAll'/></a>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </fieldset>
            </td>
            <td valign="top" style="padding-${leftRTL!'left'}: 100px;">
                <table class="tableStyle">
                    <tr>
                        <th style="width: 55% !important;">
                            <span class="accTooltip">
                                <span class="acc_icv icv-ic_que zk-colors-fg-green" />
                                <span class="accCommonTipClass">
                                    <p class="warningColor"><@i18n 'acc_editPerson_adminTip'/></p>
                                </span>
                            </span>
                            <label><@i18n 'common_level_superuser'/></label>
                        </th>
                        <td>
                            <@ZKUI.Combo id="accSuperAuth${uuid}" empty="false" width="148" name="acc.superAuth"  hideLabel="true" value="${(item.superAuth)!0}">
                            <option value="0"><@i18n 'common_no'/></option>
                            <option value="15"><@i18n 'common_yes'/></option>
                            </@ZKUI.Combo>
                        </td>
                    </tr>
                    <tr>
                        <th class="accNoTip">
                            <label><@i18n 'pers_person_devOpAuth'/></label>
                        </th>
                        <td>
                            <@ZKUI.Combo empty="false" width="148" name="acc.privilege"  hideLabel="true" value="${(item.privilege)!}">
                            <option value="0"><@i18n 'common_level_generalUser'/></option>
                            <option value="14"><@i18n 'common_level_administrator'/></option>
                            <option value="2"><@i18n 'common_level_enroller'/></option>
                        </@ZKUI.Combo>
                        </td>
                    </tr>
                    <tr>
                        <th>
                            <span class="accTooltip">
                                <span class="acc_icv icv-ic_que zk-colors-fg-green" />
                                <span class="accCommonTipClass">
                                    <p class="warningColor"><@i18n 'acc_editPerson_delayPassageTip'/></p>
                                </span>
                            </span>
                            <label><@i18n 'acc_pers_delayPassage'/></label>
                        </th>
                        <td>
                            <@ZKUI.Input id="accDelayPassage${uuid}" hideLabel="true" name="acc.delayPassage" type="checkbox" eventCheck="true" value="${(item.delayPassage?string('true','false'))!'false'}"/>
                        </td>
                    </tr>
                    <tr>
                        <th>
                            <span class="accTooltip">
                                <span class="acc_icv icv-ic_que zk-colors-fg-green" />
                                <span class="accCommonTipClass">
                                    <p class="warningColor"><@i18n 'acc_editPerson_disabledTip'/></p>
                                </span>
                            </span>
                            <label><@i18n 'pers_person_disabled'/></label>
                        </th>
                        <td>
                            <@ZKUI.Input id="accDisabled${uuid}" hideLabel="true" name="acc.disabled" type="checkbox" eventCheck="true" value="${(item.disabled?string('true','false'))!'false'}"/>
                        </td>
                    </tr>
                    <tr>
                        <th class="accNoTip"><label><@i18n 'common_level_setValidTime'/></label></th>
                        <td>
                            <@ZKUI.Input id="accIsSetValidTime${uuid}" hideLabel="true" name="acc.isSetValidTime" type="checkbox" eventCheck="true" value="${(item.isSetValidTime?string('true','false'))!'false'}"/>
                        </td>
                    </tr>
                    <tr class="accSetValidTime" style="display:none">
                        <th class="accNoTip"><label><@i18n 'common_level_startTime'/></label><span class="required" id="spanStartTime">*</span></th>
                        <td>
                            <@ZKUI.Input type="datetime" id="startDate2${uuid}" endId="endDate2${uuid}" todayRange="start" value="${(item.startTime?string('yyyy-MM-dd HH:mm:ss'))!}" today="0" name="acc.startTime" hideLabel="true" notCheckRange="true" readonly="true"/>
                        </td>
                    </tr>
                    <tr class="accSetValidTime" style="display:none">
                        <th class="accNoTip"><label><@i18n 'common_level_endTime'/></label><span class="required" id="spanEndTime">*</span></th>
                        <td>
                            <@ZKUI.Input type="datetime" id="endDate2${uuid}" todayRange="end" value="${(item.endTime?string('yyyy-MM-dd HH:mm:ss'))!}" today="0" name="acc.endTime" hideLabel="true" notCheckRange="true" readonly="true"/>
                        </td>
                    </tr>
                    <tr class="accSetValidTime" style="display:none">
                        <td class="accNoTip" colspan="2" style="padding:5px">
                            <span class="warningImage"></span><span class="warningColor"><@i18n 'common_dev_validTimeTip' /></span>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</div>
<script type="text/javascript">

function accLevelSelectHandler(value, text, event) {
    var valArr = value.split(",");
    var textArr = text.split(",");
    var oldValArr = {};
    var $input = $("#sub_selectedAccLevel input");
    if($input.length > 0){
        $input.each(function(){
            oldValArr[$(this).val()] = $(this).val();
        });
    }else{
        $("#sub_selectedAccLevel").html("");
    }
    $.each(valArr,function(i,v){
        if(oldValArr[v] == undefined){
            $("#sub_selectedAccLevel").append("<label><span id='selectedAccLevelSpan" + v + "'></span></label>" + textArr[i] + "<br>");
            loadUIToDiv("input", "#selectedAccLevelSpan" + v, {
                useInputReq:true,
                hideLabel:true,
                type:"checkbox",
                value:v,
                trueValue: v,
                onchange:"accLevelSelectChange()",
                eventCheck:true,
                dynamicCallback: "loadAccLevel(" + i +","+ (valArr.length-1) +")"
            });
        }
    });
}
function loadAccLevel(index, length) {
    if(index == length){
        accLevelSelectChange();
    }
}
function accLevelGetFilterIds(){
    var accLevelFilterIds = "";
    $("#sub_selectedAccLevel input").each(function(){
        accLevelFilterIds += $(this).val() + ",";
    });
    $("#accPersonLevelFilterIds").val(accLevelFilterIds.replace(/,$/,""));
    return $("#accPersonLevelFilterIds").val();
}
function accLevelGetSelectedIds() {
    var accLevelIds = "";
    $("#sub_selectedAccLevel input:checked").each(function(){
        accLevelIds += $(this).val() + ",";
    });
    $("#accPersonLevelIds").val(accLevelIds.replace(/,$/,""));
    return $("#accPersonLevelIds").val();
}
function accLevelSelectChange(){
    accLevelGetFilterIds();
    accLevelGetSelectedIds();
}

function loadDeptAccLevel(deptId){
    var existMasterLevel = "${defaultLevel}";
    $.ajax({
        url : "accPersonLevelByDept.do?getLevelListByDept&deptId="+deptId,
        success : function(result) {
            var resultData = result[sysCfg.data];
            if (result[sysCfg.ret] == sysCfg.success && JSON.stringify(resultData) != JSON.stringify({})) {
                $("#sub_selectedAccLevel").html("");
                var i = 0;
                var flag  = false;
                $.each(result[sysCfg.data], function (k, v) {
                    if(existMasterLevel != "" && "${defaultLevel.id}" == k) {
                        flag = true;
                    }
                    $("#sub_selectedAccLevel").append("<label><span id='loadDeptAccLevelSpan_" + k + "'></span>" + v + "</label><br>");
                        loadUIToDiv("input", "#loadDeptAccLevelSpan_" + k, {
                            useInputReq:true,
                            hideLabel:true,
                            type:"checkbox",
                            value:k,
                            trueValue: k,
                            onchange:"accLevelSelectChange()",
                            eventCheck:true
                    });
                    i++;
                });

                if(!flag && existMasterLevel != "" && !isPersEditMode) {
                    $("#sub_selectedAccLevel").append("<label><span id='loadDeptAccLevelSpan_${defaultLevel.id}'></span>" + "${defaultLevel.name}" + "</label><br>");
                        loadUIToDiv("input", "#loadDeptAccLevelSpan_${defaultLevel.id}", {
                            useInputReq:true,
                            hideLabel:true,
                            type:"checkbox",
                            value:"${defaultLevel.id}",
                            trueValue: "${defaultLevel.id}",
                            onchange:"accLevelSelectChange()",
                            eventCheck:true
                    });
                }
            }
            else {
                if (!isPersEditMode && existMasterLevel != "") {//新增
                    $("#sub_selectedAccLevel").append("<label><span id='loadDeptAccLevelSpan_${defaultLevel.id}'></span>" + "${defaultLevel.name}" + "</label><br>");
                        loadUIToDiv("input", "#loadDeptAccLevelSpan_${defaultLevel.id}", {
                            useInputReq:true,
                            hideLabel:true,
                            type:"checkbox",
                            value:"${defaultLevel.id}",
                            trueValue: "${defaultLevel.id}",
                            onchange:"accLevelSelectChange()",
                            eventCheck:true
                    });
                } else {
                    $("#sub_selectedAccLevel").html("<span class='warningImage'></span><span class='warningColor'><@i18n 'acc_level_noneSelect'/></span>");
                }
            }
        }
    });
}

$(function () {
    $("#btn${uuid}").click(function(){
        var filterIds = accLevelGetFilterIds();
        var opts = {//选人控件弹窗配置
            path:"skip.do?page=acc_personLevelByPerson_personSelectLevelContent&notInId=" + filterIds,//弹窗路径
            title:"<@i18n 'common_level_addPersonLevel'/>",//弹窗标题
            width:900,//窗口宽度
            height:470,//窗口高度
            onSure:"accLevelSelectHandler",//回调事件
            callback:function(){
                var sid = $(this.cell).find(".select_layout_box")[0];
                if (sid) {
                    DhxCommon.initEvents(ZKUI.Select.get(sid.id), ZKUI.Select.suport_evts, opts);
                }
            }
        };
        DhxCommon.createWindow(opts);
    });

    var $accSuperAuthCombo = ZKUI.Combo.get("accSuperAuth${uuid}").combo;
    //超级用户是屏蔽时间选择组件
    $accSuperAuthCombo.attachEvent("onChange", function(val){
        if(val == '15')
        {
            $("#accIsSetValidTime${uuid}").removeAttr("checked").change();
            $('#accIsSetValidTime${uuid}').attr("disabled","disabled");
            $('.accSetValidTime').css('display','none');

            $("#accDelayPassage${uuid}").removeAttr("checked").change();
            $('#accDelayPassage${uuid}').attr("disabled","disabled");
        }
        else
        {
            $('#accIsSetValidTime${uuid}').removeAttr("disabled");

            $('#accDelayPassage${uuid}').removeAttr("disabled");
        }
    });

    $accSuperAuthCombo.callEvent("onChange",["${(item.superAuth)!0}"]);

    //时间验证
    jQuery.validator.addMethod("accValidTimeStartValid", function(value, element){
        if($("input[name='acc.startTime']").val() < $("input[name='acc.endTime']").val()) {
            return true;
        } else {
            return false;
        }
    },function(){
        return "<@i18n 'common_search_dateMsg'/>";
    });

    $("#accIsSetValidTime${uuid}").change(function(){
        if(($(this).is(":checked")))
        {
            $(".accSetValidTime").show();
            $("input[name='acc.startTime']").rules("add", {accValidTimeStartValid:true});
            $("input[name='acc.startTime']").rules("add", {required:true});
            $("input[name='acc.endTime']").rules("add", {required:true});
        }
        else
        {
            $(".accSetValidTime").hide();
            $("input[name='acc.startTime']").rules("remove");
            $("input[name='acc.endTime']").rules("remove");
        }
    }).change();
});
</script>
