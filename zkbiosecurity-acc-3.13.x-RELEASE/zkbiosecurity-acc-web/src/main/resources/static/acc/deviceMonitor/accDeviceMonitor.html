<#assign gridName="devMonitorGrid${uuid!}">
<style type="text/css">
.dev_monitor_box${uuid!} {
	position:absolute;
	left:0px;
	right:0px;
	top:0px;
	bottom:20px
}

.dev_monitor_status${uuid!} {
	position:absolute;
	bottom:0px; 
	height:18px; 
	left:0px;
	right:0px;
	padding: 2px 10px;
}
.dev_monitor_inline {
	display:inline-block;
}

.dev_monitor_status${uuid!} .divBorder {
	margin-left: 5%;
}
</style>
<div class="dev_monitor_box${uuid!}">
	<@ZKUI.GridBox gridName="${gridName}">
	    <@ZKUI.Searchbar hideQueryButton="true" hideTip="true">
	    	<@ZKUI.SearchTop>
	    		<tr>
				    <td valign="middle">
				    	<@ZKUI.ComboTree url="/authArea.do?tree" id="authAreaId${uuid!}" title="base_area_entity" name="authAreaId"/>
				    </td>
				    <td valign="middle">
				    	<@ZKUI.Combo title="common_status" readonly="readonly" id="devStatus${uuid}" name="devStatus">
	                        <option value="normal"><@i18n 'common_normal'/></option>
	                        <option value="exception"><@i18n 'common_exception'/></option>
	                        <option value="disable"><@i18n 'common_disable'/></option>
	                	</@ZKUI.Combo>
				    </td>
				    <td>
				    	<label class="search-label"><@i18n 'common_dev_name'/></label>
	                	<input id="devName${uuid}" name="alias" type="text" class="search-input" title="<@i18n 'common_dev_name'/>"/>
				    </td>
				    <td>
				    	<label class="search-label"><@i18n 'common_dev_sn'/></label>
	                	<input id="devSn${uuid}" name="sn" type="text" class="search-input" title="<@i18n 'common_dev_sn'/>"/>
				    </td>
				</tr>
	    	</@ZKUI.SearchTop>
	    </@ZKUI.Searchbar>
	    <@ZKUI.Toolbar>
	    	<@ZKUI.ToolItem type="export" id="/accDeviceMonitor.do?export" permission="acc:deviceMonitor:export"/>
	    	<@ZKUI.ToolItem id="/accDeviceMonitor.do?clearCmdCache" action="accClearAllCmdCache" img="comm_del.png" text="acc_dev_clearAllCmdCache" permission="acc:deviceMonitor:clearCmdCache"/>
	    </@ZKUI.Toolbar>
	    <@ZKUI.Grid originSort="true" vo="com.zkteco.zkbiosecurity.acc.vo.AccDeviceMonitorItem" gridType="right" nopaging="true"/>
	</@ZKUI.GridBox>
</div>
<div class="dhx_toolbar_dhx_web dev_monitor_status${uuid!}">
    <div style="float: left;">
        <@i18n 'common_devMonitor_devCount'/>：<span id="devCount${uuid!}">0</span>
    </div>
	<div class="divBorder">
		<span class="dev_monitor_inline">
			<span class="icv-greenPilot"></span>
			<@i18n 'common_normal'/>：
			<span id="normalCount${uuid!}">0</span>&nbsp; 
		</span>
		<span class="dev_monitor_inline">
			<span class="icv-redPilot"></span>
			<@i18n 'common_exception'/>：
			<span id="exceptionCount${uuid!}">0</span>&nbsp; 
		</span>
		<span class="dev_monitor_inline">
			<span class="icv-yellowPilot"></span>
			<@i18n 'common_disable'/>：
			<span id="disableCount${uuid!}">0</span>
		</span>
	</div>
	<div class="divBorder">
        <@i18n 'common_devMonitor_commandSum'/>：<span id="cmdCount${uuid!}">0</span>
    </div>
    <div class="divBorder displayN">
        <@i18n 'common_devMonitor_msgTip'/> <input id="msgCheckbox${uuid!}" type="checkbox"/>
    </div>
    <div style="float: ${rightRTL!'right'};">
        <img id="monitorStateImg${uuid!}" src="${base}/public/images/greenPilotLamp.gif" height="12px" />
        <@i18n 'common_devMonitor_suspendMonitor'/> 
		<@ZKUI.Input hideLabel="true" type="checkbox" id="disableMonitorId${uuid!}" />
    </div>
</div>
<script type="text/javascript">

(function() {
	var devCounts = {
		normal : 0,
		exception : 0,
		disable : 0,
		dev : 0,
		cmd : 0
	};
	var clientId = Math.round(Math.random() * 10000)+"${uuid!}";
	var initFlag = true;
	var afterSocket = false;
	var filterAreaId = accDeviceMonitorGetAreasByUser();

    function accDeviceMonitorGetAreasByUser() {
        var v = "";
        $.ajax({
            url:"accRTMonitor.do?getAreasByUser",
            type:"post",
            async:false,
            dataType:"json",
            data:{
            },
            success:function (result) {
                if (result.data != null) {
                    v = result.data.areaId;
                }
            }
        });
        return v;
    }

	function devInfoFilter() {
		var areaTree = ZKUI.ComboTree.get("authAreaId${uuid!}");
		var dhxGrid = ZKUI.Grid.get("${gridName}");
		if (areaTree && dhxGrid && areaTree.combo.getActualValue() == areaTree.tree.getAllChecked()) {
			dhxGrid = dhxGrid.grid;
			dhxGrid.filterBy(2, function(value, id) {
				var areaInfo = areaTree.combo.getComboText();
				var areaIdArray = areaTree.combo.getActualValue().split(",");
				var areaId = dhxGrid.getUserData(id, "areaId") + "";
				var areaFlag = (areaInfo == "" ? true : (areaIdArray == "" ? value.indexOf(areaInfo) != -1 : $.inArray(areaId, areaIdArray) != -1))
				var devStatus = ZKUI.Combo.get("devStatus${uuid}").getValue();
				var devName = $("#devName${uuid!}").val().trim();
				var devSn = $("#devSn${uuid!}").val().trim();
				if (areaFlag 
						&& (devStatus == "" || devStatus == dhxGrid.getUserData(id, "status")) 
						&& (devName == "" || dhxGrid.cells(id, 0).getValue().toUpperCase().indexOf(devName.toUpperCase()) != -1) 
						&& (devSn == "" || dhxGrid.cells(id, 1).getValue().indexOf(devSn) != -1)) {
					return true;
				}
				else {
					return false;
				}
				return false;
			});
			if(afterSocket) {
				calculateFooterValues();
			}
		}
	}
	
	function calculateFooterValues() {
		var dhxGrid = ZKUI.Grid.get("${gridName}").grid;
		var gridName = "${gridName}";
	    /*if (dhxGrid.getRowsNum() == 0 && !document.getElementById(gridName + "noPrompt")) {
		    $("#" + gridName + " .objbox").append("<p id='" + gridName + "noPrompt'><span class='warningImage'></span><span class='warningColor'><@i18n 'common_devMonitor_noDevs'/></span></p>");//不存在设备
	    }
	    else if (dhxGrid.getRowsNum() > 0) {
		    $("#" + gridName + "noPrompt").remove();
	    }*/

	    var normalCount = 0;//正常数量
	    var exceptionCount = 0;//异常数量
	    var disbleCount = 0;//禁用数量
	    var devSumCount = 0;//待执行命令总数
	    var ids = dhxGrid.getAllRowIds(",") != "" ? dhxGrid.getAllRowIds(",").split(",") : {};
	    for (var i = 0; i < ids.length; i++)
	    {
		    switch (dhxGrid.getUserData(ids[i], "status"))
		    {
			    case "normal":
				    normalCount++;
				    break;
			    case "exception":
				    exceptionCount++;
				    break;
			    case "disable":
				    disbleCount++;
				    break;
		    }
		    devSumCount += parseFloat(dhxGrid.cells2(i, 5).getValue());
	    }
	    devSumCount = isNaN(devSumCount) ? 0 : devSumCount;
	    devCounts.normal= normalCount;
	    devCounts.exception= exceptionCount;
	    devCounts.disable= disbleCount;
	    devCounts.dev= dhxGrid.getRowsNum();
	    devCounts.cmd= devSumCount;
	    return true;
	}
	
	function accDevClearSearch(obId, gridName) {
		$("#" + obId + " input").each(function() {
			if (typeof ($(this).attr("defaultVal")) != "undefined") {
				$(this).val($(this).attr("defaultVal"));
				return;
			}
			if(ZKUI.Input.isDateType(this.id) && ZKUI.Input.get(this.id).initValue) {
				$(this).val(ZKUI.Input.get(this.id).initValue);
				return;
			}
			if ($(this).attr("isInit") != "true") {
				$(this).val("");
				$(this).change();
			}
		});
		$("#" + obId + " select").each(function() {
			this[0].selected = "selected";
		});
		loadSearchText(gridName);
		ZKUI.Searchbar.PULL[gridName].options.combos.forEach(function(key) {
			ZKUI.Combo.PULL[key].clear();
		});
		ZKUI.Searchbar.PULL[gridName].options.comboTrees.forEach(function(key) {
			ZKUI.ComboTree.PULL[key].clear();
		});
		$("#" + obId + " input[type=text][readonly!=readonly][disabled!='disabled']:first").focus();
	}
	
	function disableMonitor(event) {
		if(event.currentTarget.checked) {
			parseMonitor();
			openMessage(msgType.warning, "common_devMonitor_monitorSuspended");
			$("#monitorStateImg${uuid!}").attr("src", sysCfg.rootPath + "/public/images/redPilotLamp.gif");
		} else {
			initDevEventClient();
			 openMessage(msgType.success, "common_devMonitor_monitorOpened");
		     $("#monitorStateImg${uuid!}").attr("src", sysCfg.rootPath + "/public/images/greenPilotLamp.gif");
		}
	}
	
	function devConnectMsg(addDevMsg) {
	    var msgObj = document.getElementById("msgCheckbox${uuid}");
	    if (msgObj.checked)//选中时开启
	    {
		    $.jBox.messager(addDevMsg, "<@i18n 'common_devMonitor_newDevice'/>", 0, {
		        icon : 'info',
		        top : '20',
		        showType : 'fade',
		        buttons : {
			        '<@i18n 'common_devMonitor_doTotPrompt'/>' : true
		        },
		        submit : function(v, h, f)
		        {
			        msgObj.checked = false;
			        enableMsg(msgObj);
			        return false;
		        }
		    });
	    }
    }
	
	function enableMsg(event) {
		if(event.currentTarget.checked) {
		    openMessage(msgType.success, "common_devMonitor_turnedMsgTip");
	    }
	    else {
		    openMessage(msgType.warning, "common_devMonitor_closedMsgTip");
	    }
	    createCookie("accEnableMsg", event.currentTarget.checked);
	}

	function bindDomEvent() {
		$(document.getElementById("msgCheckbox${uuid!}")).on("click", enableMsg);
		$(document.getElementById("disableMonitorId${uuid!}")).on("click", disableMonitor);
		ZKUI.Combo.get("devStatus${uuid}").combo.attachEvent("onChange", devInfoFilter);
		$("#devName${uuid},#devSn${uuid}").on("keyup", devInfoFilter);
		ZKUI.Searchbar.get("${gridName}").attachEvent("onBeforeClear", function(obId, gridName) {
			accDevClearSearch(obId, gridName);
			return false;
		})
		ZKUI.ComboTree.get("authAreaId${uuid!}").combo.attachEvent("onChange", devInfoFilter);
	}
	
	var client;
	
	function initDevEventClient() {
		client = Web.getSocket({
			id:"accDevEventClient",
			url : "/accDeviceMonitor/getDeviceEvents",
			param:JSON.stringify({"clientId" : clientId, "areaId" : filterAreaId}),
			onMessage : function(resp) {
				afterSocket = true;
				var dataObj = JSON.parse(resp.body);
				if(dataObj.clientId && clientId!=dataObj.clientId) {
					return;
				}
				var dhxGrid = ZKUI.Grid.get("${gridName}").grid;
				var gridName = "${gridName}";
				var op =  [];
            	<@ZKUI.Permission name="acc:deviceMonitor:clearCmdCache">
                    op.push("0");
			    </@ZKUI.Permission>
                <@ZKUI.Permission name="adms:devCmd:refresh">
                    op.push("1");
                </@ZKUI.Permission>
				if (initFlag) {
                    dhxGrid.clearAll();//清除原来的数据
					dhxGrid.enableSmartRendering(dataObj.rows.length > 1000 ? true : false);
					dhxGrid.parse(dataObj, function() {
                        for ( var index in dataObj.rows) {
                            var id = dataObj.rows[index].id;
                            dhxGrid.cells(id, 3).setValue(I18n.getValue(dataObj.rows[index].data[3]));
                            dhxGrid.cells(id, 4).setValue(I18n.getValue(dataObj.rows[index].data[4]));
                            if ("common_none" != dataObj.rows[index].data[6]){//判断是否是有异常状态，异常状态显示为红色
                                dhxGrid.cells(id, 6).setValue("<font class='zk-msg-error'>" + I18n.getValue(dataObj.rows[index].data[6]) + "</font>");
                            }
                            if (op.length > 0)
							{
                                dhxGrid.cells(id, 7).setValue(op.join());
							}
							var textColor = getTextColorByDataLevel(dataObj.rows[index].dataLevel, dataObj.rows[index].style)
							dhxGrid.setRowTextStyle(id, textColor);
                        }
						calculateFooterValues();
						ZKUI.Grid.get("${gridName}").grid.setSizes(false);
						initFlag = false;
						dhxGrid._f_rowsBuffer = [].concat(dhxGrid.rowsBuffer);
					}, "json");
				}
				else if (dataObj.rows.length > 0) {
					var addNum = 0;
					var addDevMsg = "";
					for ( var index in dataObj.rows) {
						var id = dataObj.rows[index].id;
						var textColor = getTextColorByDataLevel(dataObj.rows[index].dataLevel, dataObj.rows[index].style);
						if (dhxGrid.doesRowExist(id)) {
						    for (var i = 0; i < dhxGrid.getColumnsNum(); i++) {
								dhxGrid.cells(id, i).setValue(I18n.getValue(dataObj.rows[index].data[i]));
							}
							dhxGrid.setRowTextStyle(id, textColor);
							dhxGrid.setUserData(id, "status", dataObj.rows[index].userdata["status"]);
							calculateFooterValues();
						}
						else {
						    dhxGrid.addRow(id, dataObj.rows[index].data);
							dhxGrid.setRowTextStyle(id, textColor);
							dhxGrid.setUserData(id, "status", dataObj.rows[index].userdata["status"]);
							dhxGrid.setUserData(id, "areaId", dataObj.rows[index].userdata["areaId"]);
							dhxGrid._f_rowsBuffer = dhxGrid._f_rowsBuffer || [];
							dhxGrid._f_rowsBuffer.push(dhxGrid.rowsAr[id]);
							addDevMsg += dataObj.rows[index].data[0] + "  " + dataObj.rows[index].data[1] + "<br/>";
							addNum++;
						}
                        if ("common_none" != dataObj.rows[index].data[6]){//判断是否是有异常状态，异常状态显示为红色
                            dhxGrid.cells(id, 6).setValue("<font class='zk-msg-error'>" + I18n.getValue(dataObj.rows[index].data[6]) + "</font>");
                        }
                        if (op.length > 0)
                        {
                            dhxGrid.cells(id, 7).setValue(op.join());
                        }
					}
					if (addNum != 0)
					{
						devInfoFilter();
						devConnectMsg(addDevMsg);
					}
					ZKUI.Grid.get("${gridName}").grid.setSizes(false);
					initFlag = false;
				}
				else {
					ZKUI.Grid.get("${gridName}").grid.setSizes(false);
					initFlag = false;
				}
			}
		})
	}
	
	function startMonitor() {
		ZKUI.Grid.get("${gridName}").grid.setSizes(false);
		initFlag = false;
	}

	function parseMonitor() {
		if(client) {
			Web.closeClient("accDevEventClient");
		}
	}

	//绑定
	Web.watch(devCounts, function(n, o, p) {
		$("#" + p + "Count${uuid!}").html(n);
	});

	function init() {
		bindDomEvent();
		setTimeout(initDevEventClient,200);
	}
	init();
})();

function accClearCmdCache(sn, alias) {
	 messageBox({
        messageType : "confirm",
        text : "<@i18n 'common_devMonitor_whetherClearCmd'/>",
        callback : function(result)
        {
        	window.setTimeout(function(){
		        if (result)
		        {
			        var operateURL = 'accDeviceMonitor.do?clearCmdCache';
			        $.ajax({
			            type : "POST",
			            url : operateURL,
			            data : {
		                	"sn" : sn,
		                	"alias" : alias
		            	},
			            dataType : "json",
			            async : true,
			            success : function(result) {
				            dealRetResult(result,function(){});
			            },
			            error : function(XMLHttpRequest, textStatus, errorThrown)
			            {
				            if (XMLHttpRequest.status == 0) {
					            openMessage(msgType.error, "<@i18n 'common_prompt_serverFailed'/>");
				            }
				            else {
					            openMessage(msgType.error, "<@i18n 'common_prompt_serverError'/>" + XMLHttpRequest.status);
				            }
			            }
			        });
		        }
		    }, 10);
        }
    });//确定要清除命令队列？
}

function acc_clearCmdCache(gridName, obj, rid, rowIndex, column, row) {
    accClearCmdCache(row.devSn, row.devName);
}

//查看命令
function acc_viewCommand(gridName, obj, rid, rowIndex, column, row) {
	accViewCommand(row.devSn);
}

//查看命令
function accViewCommand(sn) {
    openMenuItem("admsDevCmd.do", "System", "sn=" + sn);
}

function accClearAllCmdCache(id, bar, opt) {
	var dhxGrid = ZKUI.Grid.get(bar.gridName).grid;
	var rowNum = dhxGrid.getRowsNum();
	if (rowNum == 0) {
		openMessage(msgType.warning, "<@i18n 'common_devMonitor_noDevs'/>");
	}
	else {
		var sn = "";
		var alias = "";
		for (var i = 0; i < dhxGrid.getRowsNum(); i++) {
			alias = alias + "," + dhxGrid.cellByIndex(i, 0).getValue();
			sn = sn + "," + dhxGrid.cellByIndex(i, 1).getValue();
		}
		alias = alias.substr(1);
		sn = sn.substr(1);
        accClearCmdCache(sn, alias);
	}
}

startMonitor();
</script>
