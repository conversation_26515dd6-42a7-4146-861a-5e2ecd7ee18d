<#assign editPage="true">
<#assign formId = "${uuid!}">
<#include '/public/template/editTemplate.html'>
<#macro editContent>

<form action='accCombOpenDoor.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
	<input type="hidden" value="${(item.id)!}" name="id" id="id_model_pk">
	<table class='tableStyle'>
		<tr>
			<th><label><@i18n 'acc_door_name'/><label><span class="required">*</span></th>
			<td>
				<input type="hidden" id="filterDoorId" value="-1"/>
				<#if (item.id)??>
				<input type="text" id="devDoorName" name="doorName" value="${(item.doorName)!}" readonly="readonly"/>
				<#else>
				<input type="text" id="devDoorName" name="doorName" value="${(item.doorName)!}" readonly="true" placeholder="<@i18n 'common_op_clickChoice'/>"/>
				</#if>
				<input type="hidden" id="devDoorId" name="doorId" value="${(item.doorId)!}"/>
			</td>
		</tr>

		<tr>
			<th><label><@i18n 'acc_combOpen_comboName'/></label><span class="required">*</span></th>
			<td>
				<input type="text" maxlength="30" name="name" value="${(item.name)!}">
			</td>
		</tr>
		<tr>
			<th><label><@i18n 'acc_combOpen_eachGroupCount'/></label></th>
			<td>
				<div class="zk-border-color" style="border: 1px dotted #7ac142;padding:8px;color: #FF9966; overflow: auto;height: 150px; width: 480px;">
					<table width="100%" cellpadding="0" cellspacing="0">
						<#list 1..5 as t>
						<tr>
							<td><@i18n 'acc_combOpen_group'/>${t}</td>
							<td>
								<@ZKUI.Combo width="148" id="group${t}" name="groupId" value="${(tempList[t-1].accCombOpenPersonId)!}" path="accCombOpenPerson.do?getCombOpenList" onChange="groupChange">
								</@ZKUI.Combo>
								<input type="hidden" name="sort" value="${t}" >
							</td>
							<td>
								<@ZKUI.Combo width="148" empty="false" id="openerNumber${t}" value="${(tempList[t-1].openerNumber)!}" name="openerNumber" disable="true" onChange="openerNumberChange">
									<option id="openerNumber${t}Option0" value="0" <#if (tempList[t-1].openerNumber)??&&(tempList[t-1].openerNumber)==0>selected="selected" </#if> >0</option>
									<option id="openerNumber${t}Option1" value="1" <#if (tempList[t-1].openerNumber)??&&(tempList[t-1].openerNumber)==1>selected="selected" </#if> >1</option>
									<option id="openerNumber${t}Option2" value="2" <#if (tempList[t-1].openerNumber)??&&(tempList[t-1].openerNumber)==2>selected="selected" </#if> >2</option>
									<option id="openerNumber${t}Option3" value="3" <#if (tempList[t-1].openerNumber)??&&(tempList[t-1].openerNumber)==3>selected="selected" </#if> >3</option>
									<option id="openerNumber${t}Option4" value="4" <#if (tempList[t-1].openerNumber)??&&(tempList[t-1].openerNumber)==4>selected="selected" </#if> >4</option>
									<option id="openerNumber${t}Option5" value="5" <#if (tempList[t-1].openerNumber)??&&(tempList[t-1].openerNumber)==5>selected="selected" </#if> >5</option>
								</@ZKUI.Combo>
								<script type="text/javascript">
									window.setTimeout(function(){
										setGroupPersonCount("group${t}", '${t}', 0);
									}, 200);
								</script>
							</td>
							<td><span id="allPersonCount${t}">(0)</span></td>
						</tr>
						</#list>
					</table>
				</div>
			</td>
		</tr>
		<tr id="addition_fields"></tr>
	</table>
<script type='text/javascript'>
    $(function() {
        var opts = {
            path: "skip.do?page=acc_combOpenDoor_accCombOpenSelectDoorRadio",
            width: 950,
            height: 500,
            title: "",
            onSure:"selectDoorHandler"
        };
        selectContent("#${formId} #devDoorName", opts);

        //验证全局反潜设备
        jQuery.validator.addMethod("validBackgroundVerify", function(value, element){
            var setGlobalApb = true;
            $.ajax({
                type: "post",
                url: "accCombOpenDoor.do?validBackgroundVerify",
                dataType: "json",
                async :false,
                data:
				{
					'doorId' : value
				} ,
                success: function (result)
                {
                    setGlobalApb = result.data;
                },
                error: function (XMLHttpRequest, textStatus, errorThrown)
                {
                    messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
                }
            });
            return setGlobalApb;
        },function(){
            return  "<@i18n 'acc_combOpen_backgroundVerifyTip'/>";
        });
        $('#${formId}').validate( {
            debug : true,
            rules :
			{
				'name' :
				{
					required : true,
					unInputChar:true,
					overRemote : ["accCombOpenDoor.do?valid", "${(item.name)!}"]
				},
				'doorId' :
				{
					required : true,
					validBackgroundVerify : true
				}
			},
            submitHandler : function() {
                var count = 0;
                var group = "";
                for (var i=1; i<6; i++) {
                	// 各组开门人数统计
                	var openerNumberComboObjSelectedValue = ZKUI.Combo.get("openerNumber" + i).combo.getSelectedValue();
                	if (openerNumberComboObjSelectedValue != "") {
                		count += parseInt(openerNumberComboObjSelectedValue);
                	}
					// 开门人员组判断，设置不能重复
                    var groupComboObjSelectedValue = ZKUI.Combo.get("group" + i).combo.getSelectedValue();
                    if(groupComboObjSelectedValue != "") {
                        if(group.indexOf(groupComboObjSelectedValue) < 0) {
                            group += groupComboObjSelectedValue + ",";
                        }
                        else {
                            messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'acc_combOpen_groupNotUnique'/>"});
                            return;
                        }
                    }
                }
                if(count > 5) {
                    messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'acc_combOpen_mostPersonCount'/>"});
                    return;
                }
                if(count < 2) {
                    messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'acc_combOpen_leastPersonCount'/>"});
                    return;
                }
            	<@submitHandler/>
            }
        });
    });

    function groupChange(value, text) {
		var parentObj = $(this)[0].getParent();
		var currentNo = parentObj.replace("group", "")
		setGroupPersonCount(parentObj, currentNo, 1);
    }

    //initFlag初始化标识，编辑时，第一次加载处理，下拉框选项改变时，组人员数恢复0
    function setGroupPersonCount(id,num,initFlag) {
        var nextNuM = parseInt(num)+1;
        var obj = ZKUI.Combo.get(id).combo;
        if(obj.getSelectedValue() != "") {
            var groupId = obj.getSelectedValue();//该组的ID
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "accCombOpenPerson.do?checkPersonCount",
                data: {r:Math.random(),groupId:groupId},
                success: function (result) {
					if (initFlag == 1) {
						ZKUI.Combo.get("openerNumber" + num).combo.setComboValue("");
					}
					ZKUI.Combo.get("openerNumber" + num).combo.disable(false);
					$("#${formId} #allPersonCount"+num).html("("+result.data.personCount+")");
                }
            })
        }
        else {
            // 默认显示第一个元素
            ZKUI.Combo.get("openerNumber" + num).combo.setComboValue("");
            ZKUI.Combo.get("openerNumber" + num).combo.disable(true);
            $("#${formId} #allPersonCount" + num).text("(0)");
        }
    }

    function openerNumberChange(value, text) {
    	var parentObj = $(this)[0].getParent();
    	var comId = $(parentObj).parent().attr("comid");
    	var currentNo = comId.replace("openerNumber", "")
		validSelectCount(currentNo);
    }

	/** 同时验证人数校验 */
    function validSelectCount(objNo) {
		// 选中的数量
    	var selectCount = ZKUI.Combo.get("openerNumber" + objNo).combo.getSelected();
    	// 当前组支持的最大数量
    	var allPersonCount = parseInt($("#allPersonCount" + objNo).text().replace("(", "").replace(")", ""));
    	if (parseInt(selectCount) > allPersonCount) {
    		openMessage(msgType.warning, "<@i18n 'acc_dev_combOpenDoorPersonCountTip'/>".format(objNo, allPersonCount));
    		ZKUI.Combo.get("openerNumber" + objNo).combo.setComboValue("");
    		return;
    	}
    }

    function openWindow(id){
        DhxCommon.createWindow(id);
    }

    function selectDoorHandler(value,text,event){
        var oldId = $("#${formId} #devDoorId").val();
        $("#${formId} #devDoorId").val(value);
        if (oldId != value)
        {
            $("#${formId} #devDoorId").change();
        }
        $("#${formId} #devDoorName").val(text);
        $("#${formId} #devDoorId").valid();
    }

    <#if (item.id)??>
    $("#${formId} #devDoorName").attr("disabled", true);
    </#if>
</script>
</form>
</#macro>
