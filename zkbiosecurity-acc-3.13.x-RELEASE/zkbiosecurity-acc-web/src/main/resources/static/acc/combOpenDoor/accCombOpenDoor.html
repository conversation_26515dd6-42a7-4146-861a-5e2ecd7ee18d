<#assign gridName="accCombOpenDoorGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
				<td valign="middle">
					<@ZKUI.Input name="name"  maxlength="30" title="acc_combOpen_comboName" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="deviceAlias"  maxlength="20" title="common_dev_name" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="doorName"  maxlength="30" title="acc_door_name" type="text"/>
				</td>
			</tr>
    	</@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="acc:combOpenDoor:refresh"/>
    	<@ZKUI.ToolItem id="accCombOpenDoor.do?edit" text="common_op_new" width="700" height="345" img="comm_add.png" action="commonAdd" permission="acc:combOpenDoor:add"/>
    	<@ZKUI.ToolItem id="accCombOpenDoor.do?del&names=(name)" text="common_op_del" img="comm_del.png" action="commonDel" permission="acc:combOpenDoor:del"/>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccCombOpenDoorItem" query="accCombOpenDoor.do?list"/>
</@ZKUI.GridBox>