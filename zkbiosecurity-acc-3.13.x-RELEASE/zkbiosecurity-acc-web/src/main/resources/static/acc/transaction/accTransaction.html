<#assign gridName="accTransactionGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}" style="height:100%;width:100%">
	<@ZKUI.Searchbar>
		<@ZKUI.SearchTop>
	 	<tr>
	 		<td valign="middle">
				<@i18n 'common_time_from'/>&nbsp;&nbsp;
				<@ZKUI.Input type="datetime" endId="endTimeId${uuid}" id="startTime${uuid}" name="startTime" title="common_time_from" max="today" today="-7" offsetField="D" todayRange="start" hideLabel="true" noOverToday="true" readonly="false"/>
				&nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
				<@ZKUI.Input type="datetime" id="endTimeId${uuid}" name="endTime" title="common_to" max="today" today="true" todayRange="end" noOverToday="true" hideLabel="true" readonly="false"/>
    		</td>
	 		<td valign="middle">
	 			<@ZKUI.Input name="pin" maxlength="30" title="pers_person_pin" type="text"/>
	 		</td>
	 		<td valign="middle">
				<@ZKUI.Input name="devAlias" maxlength="30" title="common_dev_name" type="text"/>
			</td>
	 	</tr>
		</@ZKUI.SearchTop>
		<@ZKUI.SearchBelow>
		<tr>
			<td valign="middle">
				<@ZKUI.Input name="deptCode" maxlength="30" title="pers_dept_deptNo" type="text"/>
			</td>
			<td valign="middle">
				<@ZKUI.Input name="deptName" maxlength="30" title="pers_dept_deptName" type="text"/>
			</td>
			<td valign="middle">
				<@ZKUI.ComboGrid id="accEventId${uuid}" title="common_eventDescription" width="137" queryField="name" name="eventName"
				grid_vo="com.zkteco.zkbiosecurity.acc.vo.AccDeviceEventSelectItem"
				grid_query="accDeviceEvent.do?listSelect"/>
			</td>
			<td valign="middle">
				<@ZKUI.Input name="cardNo" maxlength="30" title="pers_card_cardNo" type="text"/>
			</td>
	 	</tr>
	 	<tr>
			<td valign="middle">
				<@ZKUI.Input name="readerName" maxlength="30" title="acc_readerDefine_readerName" type="text"/>
			</td>
			<td valign="middle">
				<@ZKUI.Input name="areaName" maxlength="30" title="base_area_name" type="text"/>
			</td>
			<td valign="middle">
				<@ZKUI.Input name="eventPointName" maxlength="30" title="common_eventPoint" type="text"/>
			</td>
	 	</tr>
	 	<tr>
			<td valign="middle">
				<@ZKUI.Input name="likeName" maxlength="30" title="pers_person_wholeName" type="text"/>
			</td>
			<td>
				<@ZKUI.Input name="devSn" maxlength="30" title="common_dev_sn" type="text"/>
			</td>
            <td>
                <@ZKUI.Combo name="eventLevel" maxlength="30" empty="true" id="eventLevel" title="common_event_level">
                <option value="0"><@i18n 'common_normal'/></option>
                <option value="1"><@i18n 'common_exception'/></option>
                <option value="2"><@i18n 'common_alarm'/></option>
                </@ZKUI.Combo>
            </td>
	 	</tr>
	    </@ZKUI.SearchBelow>
	</@ZKUI.Searchbar>
	
	<@ZKUI.Toolbar>
		<@ZKUI.ToolItem id="refresh" type="refresh" permission="acc:transaction:refresh"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="accTransaction.do?clearData" action="accClearData" text="common_op_clearData" img="comm_del.png" permission="acc:transaction:del"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem id="accTransaction.do?export" type="export" text="common_op_export" img="comm_export.png" permission="acc:transaction:export"></@ZKUI.ToolItem>
        <@ZKUI.ToolItem id="accTransaction.do?exportPhoto" action="exportPhoto" type="export" text="acc_trans_exportPhoto" title="acc_trans_exportPhoto" img="comm_export.png" permission="acc:transaction:exportPhoto"></@ZKUI.ToolItem>
	</@ZKUI.Toolbar>
	<@ZKUI.Grid pageList="true" limitCount="200000" vo="com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem" query="accTransaction.do?list" fixColumn="8" showColumns="!logId,deptCode"/>
</@ZKUI.GridBox>
<script type="text/javascript">

	var accTransactionStartTime;
	var accTransactionEndTime;

	var systemModules = "${systemModules}";
	/**
	 *下拉树的树对象选中事件
	 */
	function onTreeChecked(id, state) {
		var comboTreeId = this.treeId;//获取下拉树id
		var comboTree = ZKUI.ComboTree.get(comboTreeId);//获取下拉树对象
		var combo = comboTree.combo;//获取下拉树的下拉框对象
		var tree = comboTree.tree;//获取下拉树的树对象
	}

    var gridName = "${gridName}";
    function accClearData(id, bar) {
        deleteConfirm(function(result) {
            if (result) {
                openMessage(msgType.loading);
                $.ajax({
                    url : id,
                    type : "post",
                    success : function(result) {
                        dealRetResult(eval(result), function() {
                            closeMessage();
                            ZKUI.Grid.reloadGrid(gridName);
                        });
                    }
                });
            }
        }, "<@i18n 'common_prompt_sureToDelAll'/>");

    }

    function exportPhoto(id, bar, opts) {
        if (bar) {
        	accTransactionStartTime = ZKUI.Input.get("startTime${uuid}").obj.getDate();
			accTransactionEndTime = ZKUI.Input.get("endTimeId${uuid}").obj.getDate();
            var gridName = bar.gridName;
            opts.path = opts.path || "skip.do?page=acc_transaction_opExportPhoto&gridName=" + gridName + "&actionName=" + encodeURIComponent(id);
            if(opts.maxExportCount) {
                opts.path = opts.path + "&maxExportCount=" + opts.maxExportCount;
            }
            opts.path = opts.path;
            opts.width = opts.width || 450;
            opts.height = opts.height || 320;
            DhxCommon.createWindow(opts);
        }
	}
</script>