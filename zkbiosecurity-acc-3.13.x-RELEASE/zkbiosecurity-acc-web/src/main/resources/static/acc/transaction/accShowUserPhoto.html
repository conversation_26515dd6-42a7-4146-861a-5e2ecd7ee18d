<style type="text/css">
.picview { width:96%; margin:auto auto auto auto; text-align:center; height:500px; overflow:hidden; }
</style>

	<!--<#if !(message)?exists>-->
		<!--<div align="center" id="content${uuid}" class="picview"><img src="${(photoPath)!}" onerror="javascript:this.src='/images/userImage.gif';" width="453px" height="451px" id="img${uuid}"/></div>-->
	<!--<#else>-->
		<div align="center" id="content${uuid}" class="picview"><img src="${(photoBase64)!}" onerror="javascript:this.src='/images/${attr('system.skin','lightgreen')}/userImage.gif';" style="max-width: 453px;max-height: 451px" id="img${uuid}"/></div>
	<!--</#if>-->
<script language="javascript">
    var rate = 0.2;
    var pp = document.getElementById("img${uuid}");
    var vv = document.getElementById("content${uuid}");

    var ie=document.all;
    var nn6=document.getElementById&&!document.all;
    var isdrag=false;
    var y,x;
    var st,sl;

    pp.addEventListener('DOMMouseScroll',
        function(event) {
            if (event.detail < 0) resizeImg(false);
            else resizeImg(true);
            event.preventDefault()
        },false)
    function resizeImg(isSmall) {
        if (!isSmall)
        {
            bigit()
        }
        else
        {
            smallit()
        }
        var left = ($("#content").height()-$("#img").height()-50)/2
        if(left < 0)
        {
            $("#img").css("margin-top",0);
        }
        else
        {
            $("#img").css("margin-top",left);
        }

    }
    function moveMouse(e) {
        if (isdrag) {
            var mouseX = nn6 ? e.clientX : event.clientX;
            var mouseY = nn6 ? e.clientY : event.clientY;

            vv.scrollTop = st+(y-mouseY);
            vv.scrollLeft = sl+(x-mouseX);
            return false;
        }
    }

    function initDrag(e) {
        var oDragHandle = nn6 ? e.target : event.srcElement;
        isdrag = true;
        x = nn6 ? e.clientX : event.clientX;
        y = nn6 ? e.clientY : event.clientY;

        st = vv.scrollTop;
        sl = vv.scrollLeft;

        document.onmousemove = moveMouse;
        return false;
    }
    pp.onmousedown=initDrag;
    pp.onmouseup=new Function("isdrag=false");

    function bigit(){
        pp.style.height = pp.height*(1+rate)+"px";
        pp.style.width = pp.width*(1+rate)+"px";
    }
    function smallit(){
        pp.style.height = pp.height*(1-rate)+"px";
        pp.style.width = pp.width*(1-rate)+"px";
    }

    function clickMove(s){
        if(s=="up"){
            vv.scrollTop = vv.scrollTop-100;
        }else if(s=="down"){
            vv.scrollTop = vv.scrollTop+100;
        }else if(s=="left"){
            vv.scrollLeft = vv.scrollLeft-100;
        }else if(s=="right"){
            vv.scrollLeft = vv.scrollLeft+100;
        }
    }
</script>