<#assign gridName="accVerifyModeRulePersonGroupGrid${uuid!}">
<script type="text/javascript">
    function accVerifyModePersonGroupLeftGridClick(rid) {
        //此方法注册的是官方事件，所以this指代的是官方grid对象，通过访问属性zkgrid获取我们自定义的grid对象
        var leftGrid = this.zkgrid;//获取左表格
        var dbGrid = ZKUI.DGrid.get(leftGrid.options.dGridName);//双列表对象
        var rightGrid = dbGrid.rightGrid;//右表格对象
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        rightGrid.reload(function() {
            rightCallback(row.id, rightGrid, rid);//手动回调
        },{linkId:rid});//传递id到右表格查询
    }

    //右表格回调事件
    function rightCallback(id, rightGrid, rid) {
        var dbGrid = ZKUI.DGrid.get(rightGrid.options.dGridName);//双列表对象
        var leftGrid = dbGrid.leftGrid;//获取左表格
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        // rightGrid.setTitle("已选部门:"+row.name);//设置右表格标题
    }

    function afterAddVerifyModeRulePerson(value, text, event) {
        var verifyModeRulePersonGroupId = this.options.linkId;
        var verifyModeRuleName = this.options.linkName;
        var personDeptTree="accPersonDeptTree"+this.options.uuid;
        var deptIds = ZKUI.ComboTree.get(personDeptTree).getValue();
        if(deptIds != undefined && deptIds != null && deptIds != ""){
            var personCount = accGetPersonCountByDeptIds(deptIds);
            if (parseInt(personCount) == 0){
                messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'pers_widget_noDeptPerson'/>"});
                return false;
            }
        }
        onLoading(function(){
            $.ajax({
                url:"/accVerifyModeRulePersonGroup.do?addPerson",
                type:"post",
                dataType:"json",
                data:{
                    "verifyModeRulePersonGroupId" : verifyModeRulePersonGroupId,
                    "personIds" : value,
                    "deptIds" : deptIds,
                    "verifyModeRuleName" : verifyModeRuleName,
                    "personPins" : text
                },
                success:function (result) {
                    openMessage(msgType.success);
                    var dbGrid = ZKUI.DGrid.get("${gridName}");
                    dbGrid.rightGrid.reload();
                }
            });
        });
    }

    /*function delVerifyModeRulePerson(){
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        var leftGrid = dbGrid.leftGrid;
        var rightGrid = dbGrid.rightGrid;
        var leftGridName = leftGrid.gridName || "gridbox";
        var rightGridName = rightGrid.gridName || "gridbox";
        var leftIds = ZKUI.Grid.GRID_PULL[leftGridName].grid.getSelectedRowId();
        var rightIds = ZKUI.Grid.GRID_PULL[rightGridName].grid.getCheckedRows(0);
        if(leftIds=="" || leftIds ==null || rightIds=="" || rightIds==null ){
            messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});
        }else {
            deleteConfirm(function(result){
                if(result){
                    $.ajax({
                        url:"/accVerifyModeRulePersonGroup.do?delPerson",
                        type:"post",
                        dataType:"json",
                        data:{
                            "verifyModeRulePersonGroupId" : leftIds,
                            "personIds" :rightIds
                        },
                        success:function (result) {
                            openMessage(msgType.success);
                            var dbGrid = ZKUI.DGrid.get("${gridName}");
                            var leftGrid = dbGrid.leftGrid;
                            var rightGrid = dbGrid.rightGrid;
                            leftGrid.reload();
                            rightGrid.reload();
                        }
                    });
                }
            },"common_prompt_sureToDelThese");
        }
    }*/

</script>
<@ZKUI.DGrid gridName="${gridName}">
    <@ZKUI.LeftGrid title="acc_leftMenu_verifyModeRule">
        <@ZKUI.Searchbar>
            <@ZKUI.SearchTop>
                <tr>
                    <td valign="middle">
                        <@ZKUI.Input name="name" maxlength="30" title="acc_common_ruleName" type="text"/>
                    </td>
                </tr>
            </@ZKUI.SearchTop>
        </@ZKUI.Searchbar>
        <@ZKUI.Toolbar>
            <@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="acc:verifyModeRulePersonGroup:refresh"/>
        </@ZKUI.Toolbar>
        <!-- 配置左表格选中事件 -->
        <@ZKUI.Grid onRowSelect="accVerifyModePersonGroupLeftGridClick" vo="com.zkteco.zkbiosecurity.acc.vo.AccVerifyModeRulePersonGroupItem" query="/accVerifyModeRulePersonGroup.do?list" autoFirst="true"/>
    </@ZKUI.LeftGrid>
    <@ZKUI.RightGrid title="pers_common_browsePerson" leftFieldName="linkId">
        <@ZKUI.Searchbar>
            <@ZKUI.SearchTop>
                <tr>
                    <td valign="middle">
                        <@ZKUI.Input name="personPin" maxlength="30" title="pers_person_pin" type="text"/>
                    </td>
                    <td valign="middle">
                        <@ZKUI.Input name="likeName" maxlength="24" title="pers_person_wholeName" type="text"/>
                    </td>
                    <!--TODO 姓氏显示问题 showExpression -->
                    <!--<td valign="middle">
                        <@ZKUI.Input name="personLastName" maxlength="24" title="pers_person_lastName" type="text" showExpression = "#language!='zh_CN'"/>
                    </td>-->
                </tr>
            </@ZKUI.SearchTop>
            <@ZKUI.SearchBelow>
                <tr>
                    <td valign="middle">
                        <@ZKUI.Input name="cardNo" maxlength="30" title="pers_card_cardNo" type="text"/>
                    </td>
                    <td valign="middle">
                        <@ZKUI.Input name="deptName" maxlength="30" title="pers_dept_deptName" type="text"/>
                    </td>
                </tr>
            </@ZKUI.SearchBelow>
        </@ZKUI.Searchbar>
        <@ZKUI.Toolbar>
            <@ZKUI.ToolItem type="refresh" permission="acc:verifyModeRulePersonGroup:refresh"></@ZKUI.ToolItem>
            <@ZKUI.ToolItem id="/accVerifyModeRulePersonGroup.do?delPerson&verifyModeRulePersonGroupId=(@id:gs)&verifyModeRuleName=(@name:gs)&personIds=(id)&personPins=(personPin)" action="accRightGridCommonDel" text="pers_common_delPerson" img="comm_del.png" permission="acc:verifyModeRulePersonGroup:delPerson"/>
        </@ZKUI.Toolbar>
        <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccPersonVerifyModeRuleItem" query="accVerifyModeRulePersonGroup.do?personList"/>
    </@ZKUI.RightGrid>
</@ZKUI.DGrid>
