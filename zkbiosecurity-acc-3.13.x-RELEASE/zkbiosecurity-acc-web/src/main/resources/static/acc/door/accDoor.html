<#assign gridName="accDoorGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
				<td valign="middle">
					<@ZKUI.Input name="name"  maxlength="30" title="acc_door_name" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="deviceAlias"  maxlength="30" title="common_ownedDev" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.ComboTree width="148" url="authArea.do?tree" title="base_area_name" name="areaIdIn" readonly="readonly"/>
				</td>
			</tr>
    	</@ZKUI.SearchTop>
 		<@ZKUI.SearchBelow>
 			<tr>
				<td valign="middle">
					<@ZKUI.Input name="deviceSn" maxlength="30" title="common_dev_sn" type="text"/>
				</td>
			</tr>
 		</@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="acc:door:refresh"></@ZKUI.ToolItem>
    	<@ZKUI.ToolItem id="accDoor.do?getDoorIds&type=openDoor&ids=" text="acc_eventNo_8" img="comm_remoteopendoor.png" action="accDoorOperateDoor" permission="acc:door:openDoor"></@ZKUI.ToolItem>
    	<@ZKUI.ToolItem id="accDoor.do?getDoorIds&type=closeDoor&ids=" text="acc_eventNo_9" img="comm_remoteclosedoor.png" action="accDoorOperateDoor" permission="acc:door:closeDoor"></@ZKUI.ToolItem>
    	<@ZKUI.ToolItem id="accDoor.do?enable&name=(name)" text="common_enable" img="comm_enable.png" action="commonOperate" permission="acc:door:enable"></@ZKUI.ToolItem>
    	<@ZKUI.ToolItem id="accDoor.do?disable&name=(name)" text="common_disable" img="comm_disable.png" action="commonOperate" permission="acc:door:disable"></@ZKUI.ToolItem>
    	<@ZKUI.ToolItem id="accDoor.do?getDoorIds&type=cancelAlarm&ids=" text="acc_eventNo_7" img="acc_cancelAlarm.png" action="accDoorOperateDoor" permission="acc:door:cancelAlarm"></@ZKUI.ToolItem>
    	<@ZKUI.ToolItem id="accDoor.do?getDoorIds&type=normalOpenDoor&ids=" text="acc_rtMonitor_remoteNormalOpen" img="comm_remoteNormalOpen.png" action="accDoorOperateDoor" permission="acc:door:normalOpenDoor"></@ZKUI.ToolItem>
		<@ZKUI.ToolItem type="more">
    		<@ZKUI.ToolItem id="accDoor.do?getDoorIds&type=lockDoor&ids=" text="acc_newEventNo_233" img="acc_remote_lock.png" action="accDoorOperateDoor" permission="acc:door:lockDoor"></@ZKUI.ToolItem>
    		<@ZKUI.ToolItem id="accDoor.do?getDoorIds&type=unLockDoor&ids=" text="acc_newEventNo_234" img="acc_remote_unlock.png" action="accDoorOperateDoor" permission="acc:door:unLockDoor"></@ZKUI.ToolItem>
    		<@ZKUI.ToolItem id="accDoor.do?getDoorIds&type=enableNormalOpenDoor&ids=" text="common_rtMonitor_enableIntradayTZ" img="comm_enableDSTime.png" action="accDoorOperateDoor" permission="acc:door:enableNormalOpenDoor"></@ZKUI.ToolItem>
    		<@ZKUI.ToolItem id="accDoor.do?getDoorIds&type=disableNormalOpenDoor&ids=" text="common_rtMonitor_disableIntradayTZ" img="comm_disableDSTime.png" action="accDoorOperateDoor" permission="acc:door:disableNormalOpenDoor"></@ZKUI.ToolItem>
		</@ZKUI.ToolItem>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccDoorItem" query="accDoor.do?list" showColumns="!doorNo"/>
</@ZKUI.GridBox>

<script type="text/javascript">
	var accDoorGridName = "${gridName}";
	function accDoorOperateDoor(id,bar,opt) {
        var path = id;
        var gridName = bar.gridName || "gridbox";
        var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);//获取选中列的ID
        if(ids == "") {
            messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});
        }
        else if(ids.split(",").length > 10)
        {
            messageBox({messageType:"alert",text:"<@i18n 'common_prompt_onlySelectTenObject'/>"});
        }
        else
        {
            path += ids;
            var height = 410;
            var title = opt.text;
            if(id.indexOf("openDoor") > 0) {
                height = 440;
            }
            var opts = {//弹窗配置对象
                path: path,//设置弹窗路径
                width: 540,//设置弹窗宽度
                height: height,//设置弹窗高度
                title: title,//设置弹窗标题
                gridName: "gridbox"//设置grid

            };
            DhxCommon.createWindow(opts);
        }
    }
</script>