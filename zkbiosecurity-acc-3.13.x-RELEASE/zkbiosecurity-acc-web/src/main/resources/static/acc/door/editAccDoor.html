<#include '/public/template/editTemplate.html'>
<#macro editContent>

<form action='/accDoor.do?save' method='post' id='${formId}' enctype='multipart/form-data'>

	<input type="hidden" name="id" id="doorId" value="${(item.id)!}" />
	<input type="hidden" name="deviceId" id="deviceId" value="${(item.deviceId)!}" />
	<input type="hidden" name="machine_type" value="${(item.devMachineType)!}" />

	<table cellpadding="0" cellspacing="0" style="width: 100%">
		<tr>
			<td>
				<table cellpadding="8" class="tableStyle">

					<tr>
						<th ><label><@i18n 'common_dev_name'/></label><span class="required">*</span></th>
						<td>
							<input type="text" name="deviceAlias" value="${(item.deviceAlias)!}" disabled="disabled"/>
						</td>
						<th><label><@i18n 'acc_door_number'/></label><span class="required">*</span></th>
						<td>
							<input type="text" name="doorNo" id="id_door_no" value="${(item.doorNo)!}" disabled="disabled"/>
						</td>
					</tr>
					<tr>
						<th><label><@i18n 'acc_door_name'/></label><span class="required">*</span></th>
						<td>
							<input type="text" id="id_door_name" maxlength="100" name="name" value="${(item.name)!}" />
						</td>
						<th><label><@i18n 'acc_door_activeTimeZone'/></label><span class="required">*</span></th>
						<td>
							<@ZKUI.Combo empty="false" value="${(item.activeTimeSegId)!}" name="activeTimeSegId" hideLabel="true" readonly="true" width="148" path="/accTimeSeg.do?getTimeSegList">
							</@ZKUI.Combo>
						</td>
					</tr>
					<tr>
						<th><label><@i18n 'common_verifyMode_entiy'/></label><span class="required">*</span></th>
						<td>
							<#if (newVFStyles)?exists>
								<@ZKUI.Combo id="newVerifyStyleCombo${uuid!}" empty="false" value="0" onChange="changeVerifyMode" hideLabel="true" readonly="true" width="148">
									<option value="0"><@i18n 'common_verifyMode_cardOrFpOrPwd'/></option>
									<option value="1"><@i18n 'common_newVerify_mode_manualConfig'/></option>
								</@ZKUI.Combo><br/>
								<div id="newVerifyStyleSetDiv${uuid!}" style="display:none;">
									<label>
										<#if (verifyStyleLogic)?exists && verifyStyleLogic=="0">
											<@ZKUI.Input hideLabel="true" type="radio" name="verifyStyleLogic" value="0" checked="checked"/>
										<#else>
											<@ZKUI.Input hideLabel="true" type="radio" name="verifyStyleLogic" value="0"/>
										</#if>
										<span><@i18n 'common_newVerify_mode_logicOr'/></span>
									</label>
									<label>
										<#if (verifyStyleLogic)?exists && verifyStyleLogic=="1">
											<@ZKUI.Input hideLabel="true" type="radio" name="verifyStyleLogic" value="1" checked="checked"/>
										<#else>
											<@ZKUI.Input hideLabel="true" type="radio" name="verifyStyleLogic" value="1"/>
										</#if>
										<span><@i18n 'common_newVerify_mode_logicAnd'/></span>
									</label>
									<br/>
									<@ZKUI.ComboTree empty="false" value="${(newVFStyles)!}" id="newVFStylesTree${uuid!}" name="verifyMode" hideLabel="true" isTree="true" readonly="true" width="148" url="accDeviceVerifyMode.do?getDeviceVerifyModeTreeByDeviceId&deviceId=${(item.deviceId)!}"/>
								</div>
							<#else>
								<@ZKUI.Combo empty="false" value="${(item.verifyMode)!}" name="verifyMode" hideLabel="true" readonly="true" width="148" path="accDoor.do?getVerifyMode&deviceId=${(item.deviceId)!}">
								</@ZKUI.Combo>
							</#if>
						</td>
						<th><label><@i18n 'acc_door_lockOpenDuration'/></label><span class="required">*</span></th>
						<td>
							<input type="text" name="lockDelay" id="idLockDelay" value="${(item.lockDelay)!}" maxlength="3" onfocus="getDefValue(this, 'lockDelay');" onblur="checkTotalValue('lockDelay');"/><span class="gray"><@i18n 'common_second'/>(1-254)</span>
						</td>
					</tr>
					<tr>
						<th id="tdWgInputFmt"><label><@i18n 'pers_wgFmt_entity'/></label></th>
						<td>
							<@ZKUI.Combo id="idWgInputFmt" value="${(item.wgInputFmtId)!}" empty="false" name="wgInputFmtId" hideLabel="true" readonly="true" width="148" path="/accDoor.do?getWiegandFmtList">
							</@ZKUI.Combo>
						</td>
						<th id="tdLatchDoorType"><label><@i18n 'acc_door_requestToExit'/></label><span class="required">*</span></th>
						<td>
							<@ZKUI.Combo hideLabel="true" width="148" empty="false" name="latchDoorType" id="idLatchDoorType">
								<option value="0" <#if (item.latchDoorType)?exists && (item.latchDoorType) == 0>selected="selected"</#if>><@i18n 'acc_door_withoutUnlock'/></option>
								<option value="1" <#if (item.latchDoorType)?exists && (item.latchDoorType) == 1>selected="selected"</#if>><@i18n 'acc_door_unlocking'/></option>
							</@ZKUI.Combo>
						</td>
					</tr>
					<tr>
						<th ><label><@i18n 'acc_door_wgFmtReverse'/></label></th>
						<td>
							<#if supportWGDataProcess?string != "true" && (item.wgReversed)?exists && (item.wgReversed) != 0>
								<@ZKUI.Input hideLabel="true" type="checkbox" name="wgReversed" value="3" disabled="disabled" checked="checked" />
							<#elseif supportWGDataProcess?string != "true">
								<@ZKUI.Input hideLabel="true" type="checkbox" name="wgReversed" value="3" disabled="disabled" />
							<#elseif (item.wgReversed)?exists && (item.wgReversed) != 0>
								<@ZKUI.Input hideLabel="true" type="checkbox" name="wgReversed" value="3" checked="checked" />
							<#else>
								<@ZKUI.Input hideLabel="true" type="checkbox" name="wgReversed" value="3" />
							</#if>
						</td>
					<th id="tdLatchTime"><label><@i18n 'acc_door_alarmDelay'/></label></th>
					<td>
						<input type="text" name="latchTimeOut" id="idLatchTimeOut" value="${(item.latchTimeOut)!}" maxlength="3"/><span class="gray"><@i18n 'common_second'/>(5-254)</span>
					</td>
					</tr>
					<tr>
						<th><label><@i18n 'common_punchInterval'/></label><span class="required">*</span></th>
						<td>
							<input type="text" name="actionInterval" id="id_card_intervaltime" value="${(item.actionInterval)!}" maxlength="3" /><span class="gray"><@i18n 'common_second'/>(0-254)</span>
						</td>
						<th id="tdLatchTimeZone"><label><@i18n 'acc_door_latchTimeZone'/></label></th>
						<td>
							<@ZKUI.Combo id="id_latch_timeSeg${uuid}" autoFirst="true" empty="false" value="${(item.latchTimeSegId)!}" name="latchTimeSegId" hideLabel="true" readonly="true" width="148" path="/accTimeSeg.do?getTimeSegList">
							</@ZKUI.Combo>
						</td>
					</tr>
					<tr>
						<th><label><@i18n 'acc_door_sensorType'/></label><span class="required">*</span></th>
						<td>
							<@ZKUI.Combo hideLabel="true" width="148" empty="false" name="doorSensorStatus" id="idDoorSensorStatus">
								<option selected="selected" value="0"><@i18n 'common_none'/></option>
								<option value="1" <#if (item.doorSensorStatus)?exists && (item.doorSensorStatus) == 1>selected="selected"</#if>><@i18n 'acc_door_normalOpen'/></option>
								<option value="2" <#if (item.doorSensorStatus)?exists && (item.doorSensorStatus) == 2>selected="selected"</#if>><@i18n 'acc_door_normalClose'/></option>
							</@ZKUI.Combo>
						</td>
						<th id="tdInApbDuration"><label><@i18n 'acc_door_entranceApbDuration'/></label></th>
						<td>
							<input type="text" name="inApbDuration" id="idInApbDuration" value="${(item.inApbDuration)!}"  maxlength="3" /><span class="gray"><@i18n 'common_minute'/>(0-120)</span>
						</td>
					</tr>
					<tr>
						<th id="tdBackLock" class="gray"><label><@i18n 'acc_door_closeAndReverseState'/></label></th>
						<td>
							<@ZKUI.Input hideLabel="true" type="checkbox" id="idBackLock" name="backLock" value="${(item.backLock)?string}"/>
						</td>
						<th><label><@i18n 'acc_door_duressPassword'/></label></th>
						<td>
							<input type="password" maxlength="6" size="8" name="forcePwd" onkeypress="return isNotSpace(event);" id="id_force_pwd" value="${(item.forcePwd)!}" /><span class="gray">(<@i18n 'acc_door_max6BitInteger'/>)</span>
						</td>
					</tr>
					<tr id="tr_supper_pwd" style="display: table-row;">
						<th id="tdSensorDelay" class="gray"><label><@i18n 'acc_door_sensorDelay'/></label><span class="required" id ="idRequiredSensor"></span></th>
						<td>
							<input type="text" name="sensorDelay" id="idSensorDelay" value="${(item.sensorDelay)!}"  maxlength="3"/><span class="gray"><@i18n 'common_second'/>(1-254)</span>
						</td>
						<th><label><@i18n 'common_emergencyPassword'/></label></th>
						<td>
							<input type="password" maxlength="8" size="8" name="supperPwd" onkeypress="return isNotSpace(event);"  id="id_supper_pwd" value="${(item.supperPwd)!}" /><span class="gray">(<@i18n 'common_8BitInteger'/>)</span>
						</td>
					</tr>
					<tr>
						<th><label><@i18n 'acc_door_passageModeTimeZone'/></label></th>
						<td>
							<@ZKUI.Combo value="${(item.passModeTimeSegId)!}" name="passModeTimeSegId" hideLabel="true" readonly="true" width="148" path="accTimeSeg.do?getTimeSegList">
							</@ZKUI.Combo>
						</td>
						<th><label><@i18n 'acc_door_disableAudio'/></label></th>
						<td>
							<@ZKUI.Input hideLabel="true" type="checkbox" id="idIsDisableAudio" name="isDisableAudio" value="${((item.isDisableAudio)?string)!}"/>
						</td>
					</tr>
					<tr>
						<th><label><@i18n 'acc_door_extDelayDrivertime'/></th>
						<td>
							<#if userOpenDoorDelayFunOn?string == "true">
								<input type="text" name="extDelayDrivertime" id="idExtDelayDrivertime" value="${(item.extDelayDrivertime)!0}" maxlength="2" onfocus="getDefValue(this,'extDelay');" onblur="checkTotalValue('extDelay');"/><span class="gray"><@i18n 'common_second'/>(0-60)</span>
							<#else>
								<input type="text" name="extDelayDrivertime" id="idExtDelayDrivertime" value="" maxlength="2" readOnly="readOnly" style="user-select: none;"/><span class="gray"><@i18n 'common_second'/>(0-60)</span>
							</#if>
						</td>
						<th><label><@i18n 'acc_door_delayOpenTime'/></th>
						<td>
							<#if delayOpenDoorFunOn?string == "true">
								<input type="text" name="delayOpenTime" id="idDelayOpenTime" value="${(item.delayOpenTime)!0}" maxlength="2" /><span class="gray"><@i18n 'common_second'/>(0-60)</span>
							<#else>
								<input type="text" name="delayOpenTime" id="idDelayOpenTime" value="" maxlength="2" readOnly="readOnly" style="user-select: none;"/><span class="gray"><@i18n 'common_second'/>(0-60)</span>
							</#if>
						</td>
					</tr>
					<tr>
						<th><label><@i18n 'acc_door_combOpenInterval'/></label><span class="required">*</span></th>
						<td>
							<#if multiCardInterTimeFunOn?string == "true">
							<input type="text" maxlength="3" name="combOpenInterval" value="${(item.combOpenInterval)!}"/><span class="gray"><@i18n 'common_second'/>(5-60)</span>
							<#else>
							<input type="text" maxlength="3" name="combOpenInterval" value="${(item.combOpenInterval)!}" readOnly="readOnly" style="user-select: none;"/><span class="gray"><@i18n 'common_second'/>(5-60)</span>
						</#if>
						</td>
						<th><label><@i18n 'acc_door_allowSUAccessLock'/></label></th>
						<td>
							<#if allowSUAccessLock?string != "true" && (item.allowSUAccessLock)?exists && (item.allowSUAccessLock) != "0">
								<@ZKUI.Input hideLabel="true" type="checkbox" value="1" name="allowSUAccessLock" disabled="disabled" checked="checked" />
							<#elseif allowSUAccessLock?string != "true">
								<@ZKUI.Input hideLabel="true" type="checkbox" value="1" name="allowSUAccessLock" disabled="disabled" />
							<#elseif (item.allowSUAccessLock)?exists && (item.allowSUAccessLock) != "0">
								<@ZKUI.Input hideLabel="true" type="checkbox" value="1" name="allowSUAccessLock" checked="checked" />
							<#else>
								<@ZKUI.Input hideLabel="true" type="checkbox" value="1" name="allowSUAccessLock"/>
							</#if>
						</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
	<div style="margin-${leftRTL!'left'}: 200px;margin-top: 45px;">
		<table cellpadding="8" class="tableStyle" >
			<tr>
				<td style="width: 240px;"><label><@i18n 'common_copySettingsTo'/></label></td>
				<td colspan="3">
					<@ZKUI.Combo hideLabel="true" width="240" empty="false" name="applyTo" id="id_applyTo" >
						<option value="0" selected="selected">---------</option>
						<option value="1"><@i18n 'acc_door_allDoorOfCurDev'/></option>
						<option value="2"><@i18n 'acc_door_allDoorOfAllControlDev'/></option>
					</@ZKUI.Combo>
				</td>
			</tr>
		</table>
	</div>
</form>

<script type='text/javascript'>
    (function() {
    	editAccDoor();

        //自定义验证
        //sensorDelay
        jQuery.validator.addMethod("sensorDelayValid", function(value, element){
            var lockDelay = $("input[name='lockDelay']").val();
            if(lockDelay != "" && parseInt(value) <= parseInt(lockDelay))
            {
                window.setTimeout(function(){//为了实现异步
                    if($("#idLockDelay").hasClass("error")  && !$("#idSensorDelay").hasClass("error"))
                    {
                        $("#idLockDelay").valid();
                    }
                },10);
                return false;
            }
            else if($("#idLockDelay").hasClass("error")  && !$("#idSensorDelay").hasClass("error"))
            {
                window.setTimeout(function(){//为了实现异步
                    $("#idLockDelay").valid();
                },10);
            }
            return true;
        }, "<@i18n 'acc_door_durationLessLock'/>");
        jQuery.validator.addMethod("lockDelayValid", function(value, element){
            var sensorDelay = $("input[name='sensorDelay']").val();
            if(sensorDelay != "" && parseInt(value) >= parseInt(sensorDelay))
            {
                window.setTimeout(function(){//为了实现异步
                    if($("#idSensorDelay").hasClass("error") && !$("#idLockDelay").hasClass("error"))
                    {
                        $("#idSensorDelay").valid();
                    }
                },10);
                return false;
            }
            else if($("#idSensorDelay").hasClass("error") && !$("#idLockDelay").hasClass("error"))
            {
                $("#idSensorDelay").valid();
            }
            return true;
        }, "<@i18n 'acc_door_lockMoreDuration'/>");
        jQuery.validator.addMethod("lockAndExtDelayValid", function(value, element){
            var sensorDelay = $("input[name='sensorDelay']").val();
            var extDelayDrivertime = $("input[name='extDelayDrivertime']").val();
            var lockDelay = $("input[name='lockDelay']").val();
            if((parseInt(lockDelay)+parseInt(extDelayDrivertime)) >= parseInt(sensorDelay))
            {
                return false;
            }
            else
            {
                $("#idLockDelay").removeClass("error");
                $("#idExtDelayDrivertime").removeClass("error");
                $(".ts_box").remove();
            }

            return true;
        }, "<@i18n 'acc_door_lockAndExtLessDuration'/>");
        //验证全局反潜设备
        jQuery.validator.addMethod("validGlobalApb", function(value, element){
            var setGlobalApb = true;//是否设置全局反潜
            if(value > 0)
            {
                var doorId = $("input[name='id']").val();
                $.ajax({
                    type: "post",
                    url: "/accDoor.do?validGlobalApb",
                    dataType: "json",
                    async :false,
                    data:
                        {
                            'doorId' : doorId
                        } ,
                    success: function (data)
                    {
                        setGlobalApb = data;
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown)
                    {
                        messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
                    }
                });
            }
            return setGlobalApb;
        },function(){
            return  "<@i18n 'acc_apb_conflictWithGapb'/>";
        });

        // 字母和数字的验证
        $("#${formId}").validate({
            debug : true,
            rules :
                {
                    "lockDelay" :
					{
						required : true,
						range : [1,254],
						digits : true,
						lockDelayValid : true,
						lockAndExtDelayValid : true
					},
                    "actionInterval" :
					{
						required : true,
						range : [0,254],
						digits : true
					},
                    "supperPwd" :
					{
						digits : true,
						rangelength : [ 8, 8 ]
					},
                    "activeTimeSegId" :
					{
						required : true
					},
                    "doorSensorStatus" :
					{
						required : true
					},
                    "verifyMode" :
					{
						required : true
					},
                    "name" :
					{
						required : true,
						unInputChar:true,
                        overRemote: ["/accDoor.do?isExist", "${(item.name)!}"]
					},
					"forcePwd" :
					{
						digits : true,
						rangelength : [ 0, 6 ],
						remote : {
							url : "accDoor.do?checkPwd", //后台处理程序
							type : "post", //数据发送方式
							dataType : "json" //接受数据格式
						}
					},
					"sensorDelay" :
					{
						range : [1,254],
						sensorDelayValid : true,
						lockAndExtDelayValid : true
					},
					"inApbDuration" :
					{
						range: [0,120],
						digits : true,
						validGlobalApb : true
					},
					"latchTimeOut" :
					{
						range : [5,254],
						required : true,
						digits : true
					},
					"delayOpenTime" :
					{
						range : [0,60],
						digits : true
					},
					"extDelayDrivertime" :
					{
						range : [0,60],
						digits : true,
						lockAndExtDelayValid : true
					},
					"combOpenInterval" : {
						required : true,
						digits: true,
						range: [5,60]
					}
				},
			messages:
			{
				"name":
				{
					remote: "<@i18n 'acc_door_nameRepeat'/>"
				},
				"forcePwd":
				{
					remote : "<@i18n 'acc_door_duressPwdError'/>"
				},
				"supperPwd":
				{
					rangelength: jQuery.validator.format("<@i18n 'acc_door_urgencyStatePwd'/>")
				}
			},
			submitHandler : function()
			{
				openMessage(msgType.loading,"<@i18n 'common_op_processing'/>");

				// 处理新验证方式转为十进制提交保存
				<#if (newVFStyles)?exists>
					/** 验证方式校验 */
					var validFlag = validNewVerifyMode();
					if (!validFlag) {
						return;
					}

					var newVFStyleComboValue = ZKUI.Combo.get("newVerifyStyleCombo${uuid!}").combo.getSelected();
					// 逻辑位
					var verifyStyleLogic = $("input:radio[name='verifyStyleLogic']:checked").attr("value");
					// 勾选的验证方式
					var verifyMode = ZKUI.ComboTree.get("newVFStylesTree${uuid!}").getValue();
					var newVFStylesArray = [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];
					// 选项为自动识别
					if (newVFStyleComboValue == "0") {
						verifyStyleLogic = "0";
						verifyMode = ZKUI.ComboTree.get("newVFStylesTree${uuid!}").tree.getAllChildless();
					}
					var newVFStylesLength = newVFStylesArray.length - 1;
					// 因二进制低位到高位由右向左,所以逆序赋值
					newVFStylesArray[newVFStylesLength] = parseInt(verifyStyleLogic);
					var verifyModeArray = verifyMode.split(",");
					for (var i=0; i < verifyModeArray.length; i++) {
						if (verifyModeArray[i] != 0) {
							newVFStylesArray[newVFStylesLength-parseInt(verifyModeArray[i])] = 1;
						}
					}
					// 将二进制转为十进制数
					var newVerifyMode = parseInt(newVFStylesArray.toString().replace(/,/g, ""),2);
					$("input[name='verifyMode']").attr("value", newVerifyMode);
				</#if>

				$("input[name='deviceAlias']").attr("disabled", false);
				$("input[name='doorNo']").attr("disabled", false);
				$("input[name='latchTimeOut']").attr("disabled", false);

				$('#${formId}').ajaxSubmit({
					success: function(result)
					{
						dealRetResult(result,function(){
							if(typeof(updateDoorName) != "undefined")//实时监控-窗口关闭之前
							{
							    try {
                                    updateDoorName($("#${formId} #doorId").val(), $("#${formId} #id_door_name").val());//修复加载过一次实时监控，切换到门页面编辑报错问题
							    }
                                catch (e) {
							    }
							}
							if(isContinueAdd)
							{
								refreshCurrentWindow();
							}
							else
							{
                                DhxCommon.closeWindow();
							}

							//如果不选中树中任何节点，就是使用下面注释
							//tree.clearSelection(tree.getSelectedItemId());
                            if (typeof(accDoorGridName) != "undefined") {
                                ZKUI.Grid.reloadGrid(accDoorGridName);//重新加载数据列表
                            }
							if(typeof(tree) != "undefined" && document.getElementById("gridbox"))
							{
								var id = tree.getSelectedItemId();
								if(id.indexOf("_") < 0)
								{
									mygrids["gridbox"].clearAndLoad(mygrids["gridbox"].path+"&deviceId="+id,"json");
								}
								else
								{
									var id = id.split("_")[0];
									mygrids["gridbox"].clearAndLoad(mygrids["gridbox"].path+"&authAreaId="+id,"json");
								}
							}
						});
					},
					error: function(XMLHttpRequest, textStatus, errorThrown)
					{
						messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
					}
				});
			}
    	});
        $("#id_force_pwd").valid();//解决实时监控编辑门时，门名称验证失效问题
    })();


    <#if inputIOSettingFunOn?string == "false">
        ZKUI.Combo.get("id_latch_timeSeg${uuid}").combo.disable(true);
    	$("#${formId} #tdLatchTimeZone").attr("class", "gray");
    </#if>

    var defExtDelay;
    var defLockDelay;
    //获得焦点时，保存当前值
    function getDefValue(obj, objType)
    {
        if(objType == "lockDelay")
        {
            defLockDelay = $(obj).val();
        }
        else
        {
            defExtDelay = $(obj).val();
        }
    }
    //验证锁驱动时间和延长通行时间的总和是否大于254
    function checkTotalValue(objType)
    {
    <#if userOpenDoorDelayFunOn?string == "true">
        var lockDelay= parseInt($("#idLockDelay").val());
        var extDelay= parseInt($("#idExtDelayDrivertime").val());
        if((lockDelay + extDelay) > 254)
        {
            messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'acc_door_lockAndExtDelayTip'/>"});
            if(objType == "lockDelay")
            {
                $("#idLockDelay").val(defLockDelay);
            }
            else
            {
                $("#idExtDelayDrivertime").val(defExtDelay);
            }
        }
    </#if>
    }

    $("#idSensorDelay").attr("disabled", "disabled");
    // 闭门回锁判断
    <#if (item.backLock)?exists>
        <#if (item.backLock)?string == "true">
        	$("#idBackLock").attr("checked", "true");
    		$("#idBackLock").val(true);
    	<#else>
    		$("#idBackLock").removeAttr("checked");
    		$("#idBackLock").val(false);
    	</#if>
    </#if>

    $("#idBackLock").change(function(){
        if($(this).attr("checked") == "checked")
        {
            $("#idBackLock").val(true);
        }
        else
        {
            $("#idBackLock").val(false);
        }
    });


    <#if (item.isDisableAudio)?exists>
        <#if (item.isDisableAudio)?string == "true">
        	$("#idIsDisableAudio").attr("checked", "true");
    		$("#idIsDisableAudio").val(true);
    	<#else>
    		$("#idIsDisableAudio").removeAttr("checked");
    		$("#idIsDisableAudio").val(false);
    	</#if>
    </#if>

    $("#idIsDisableAudio").change(function(){
        if($(this).attr("checked") == "checked")
        {
            $("#idIsDisableAudio").val(true);
        }
        else
        {
            $("#idIsDisableAudio").val(false);
        }
    });


    // 门磁类型判断
    checkSensorStatus(ZKUI.Combo.get("idDoorSensorStatus").combo.getSelectedValue());

    ZKUI.Combo.get("idDoorSensorStatus").combo.attachEvent("onChange", function(){
        checkSensorStatus(ZKUI.Combo.get("idDoorSensorStatus").combo.getSelectedValue());
    });

    function checkSensorStatus(val)
    {
        if(val == 0)// 无门磁
        {
            $("#idSensorDelay").val("");
            $("#idSensorDelay").attr("disabled", "disabled");
            $("#idBackLock").removeAttr("checked");
            $("#idBackLock").val("false");
            $("#idBackLock").attr("disabled", "disabled");
            $("#tdSensorDelay").attr("class", "gray");
            $("#tdBackLock").attr("class", "gray");
            $("#idLockDelay").removeClass("error");
            $("#idExtDelayDrivertime").removeClass("error");
            $(".ts_box").remove();
            $("#idSensorDelay").rules("remove");
            $("#idSensorDelay").valid();
            $("#idRequiredSensor").html("");
        }
        else// 常开、常闭
        {
            $("#idSensorDelay").removeAttr("disabled");
            $("#idBackLock").removeAttr("disabled");
        <#if (item.backLock)?exists && (item.backLock)?string == "true">
            $("#idBackLock").attr("checked", true);
            $("#idBackLock").val(true);
        </#if>
            $("#tdSensorDelay").removeAttr("class");
            $("#tdBackLock").removeAttr("class");
            $("#idSensorDelay").rules("add", {digits: true,range: [1,254],required:true,sensorDelayValid : true});
            $("#idSensorDelay").val(<#if (item.sensorDelay)?exists>${(item.sensorDelay)!}<#else>15</#if>);
            $("#idRequiredSensor").html("*");
        }
    }

    <#if (item.readerType)?exists && (item.readerType==11) >
        $("#enterAndExit").attr("disabled","disabled");
    </#if>
    // 出门按钮状态
    checkLatchDoorType(ZKUI.Combo.get("idLatchDoorType").combo.getSelectedValue());
    ZKUI.Combo.get("idLatchDoorType").combo.attachEvent("onChange", function(){
        checkLatchDoorType(ZKUI.Combo.get("idLatchDoorType").combo.getSelectedValue());
    });
    function checkLatchDoorType(value)
    {
        if(value == 0)//锁定
        {
            $("#idLatchTimeOut").removeAttr("disabled");
            $("#tdLatchTime").removeAttr("class");
            if($("#idLatchTimeOut").val() == "")
            {
                $("#idLatchTimeOut").val(10);
            }
			<#if inputIOSettingFunOn?string == "true">
            	ZKUI.Combo.get("id_latch_timeSeg${uuid}").combo.disable(true);

            	$("#tdLatchTimeZone").attr("class", "gray");
			</#if>
        }
        else//不锁定
        {
            $("#idLatchTimeOut").attr("disabled", "disabled");
            $("#tdLatchTime").attr("class", "gray");
            $("#idLatchTimeOut").val("");
			<#if inputIOSettingFunOn?string == "true">
				ZKUI.Combo.get("id_latch_timeSeg${uuid}").combo.disable(false);
				$("#tdLatchTimeZone").removeAttr("class");
			</#if>
        }
    }

    //此处根据设备参数决定显示的内容
    $.ajax({
        type: "GET",
        url: "accDoor.do?getAccDeviceOpt&deviceId="+ $("#deviceId").val(),
        dataType: "json",
        async: false,
        success: function(result)
        {
			var data = result.data;
            var cardFormatFun = data.CardFormatFunOn;// 为1支持韦根格式
            var rexinputFun = data.REXInputFunOn;// 为1支持出门按钮状态
            var timeApbFun = data.TimeAPBFunOn;// 为1支持入时间反潜
            if(cardFormatFun == "" || cardFormatFun == 0)// 韦根格式
            {
                $("#tdWgInputFmt").attr("class", "gray");
                ZKUI.Combo.get("idWgInputFmt").combo.disable(true);
            }
            if(rexinputFun == 0)// 出门按钮状态
            {
                $("#tdLatchDoorType").attr("class", "gray");
                ZKUI.Combo.get("idLatchDoorType").combo.disable(true);
            }
            if(timeApbFun == 0)// 入时间反潜
            {
                $("#tdInApbDuration").attr("class", "gray");
                $("#idInApbDuration").attr("disabled", "disabled");
            }
        },
        error:function (XMLHttpRequest, textStatus, errorThrown)
        {
            messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
        }
    });

    function isNotSpace(e)
    {
        if ($.browser.msie)
        {
            if ((event.keycode == 32))
            {
                return false;
            }
            else
            {
                return true;
            }
        }
        else
        {
            if ((e.which == 32))
            {
                return false;
            }
            else
            {
                return true;
            }
        }
    }

    // 编辑
    function editAccDoor() {
		window.setTimeout(function() {
			if ($("#doorId").val() != "") {
				<#if newVFStyles?exists>
					var treeItemsNo = ZKUI.ComboTree.get("newVFStylesTree${uuid}").tree.getAllChildless();
					var treeSelectedNo = ZKUI.ComboTree.get("newVFStylesTree${uuid}").combo.getSelectedValue();
					if (treeItemsNo != treeSelectedNo) {
						ZKUI.Combo.PULL["newVerifyStyleCombo${uuid}"].combo.setComboValue('1');
						$("#newVerifyStyleSetDiv${uuid!}").show();
					}
				</#if>
			}
		},500)
    }

	/** 修改验证方式 */
	function changeVerifyMode(value) {
		// 自动识别
		if (value == "0") {
			$("#newVerifyStyleSetDiv${uuid!}").hide();
			var treeItemsNo = ZKUI.ComboTree.get("newVFStylesTree${uuid}").tree.getAllChildless();
			ZKUI.ComboTree.get("newVFStylesTree${uuid}").setValue(treeItemsNo);
		} else if (value == "1") {
			// 手动配置
			$("#newVerifyStyleSetDiv${uuid!}").show();
		}
	}

	/** 校验验证方式，L3密码不能作为独立验证方式使用 */
	function validNewVerifyMode() {
		var flag = true;
		// 安全等级大于等于3
        if (sysCfg.securityLevel && sysCfg.securityLevel >= 3) {
        	var newVFStyleComboValue = ZKUI.Combo.get("newVerifyStyleCombo${uuid!}").combo.getSelected();
        	// 自动识别不校验
        	if (newVFStyleComboValue == "0") {
        		return flag;
        	}
			var verifyStyleLogic = $("input:radio[name='verifyStyleLogic']:checked").attr("value");
			var verifyMode = ZKUI.ComboTree.get("newVFStylesTree${uuid!}").getValue();
			// "或"逻辑关系，勾选项中不能包含密码，密码对应位为9
			if (verifyStyleLogic == "0" && verifyMode.indexOf("9") != -1) {
				flag = false;
			}
			// "与"逻辑关系，不能只勾选密码
			if (verifyStyleLogic == "1" && verifyMode == "9") {
				flag = false;
			}
			if (!flag) {
				openMessage(msgType.warning, I18n.getValue("acc_door_verifyModeSinglePwd"));
			}
        }
		return flag;
	}
</script>
</#macro>