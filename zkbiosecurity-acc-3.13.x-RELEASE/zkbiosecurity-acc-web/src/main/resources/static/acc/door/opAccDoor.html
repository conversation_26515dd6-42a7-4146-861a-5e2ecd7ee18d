<#include '/public/template/editTemplate.html'>
<#macro editMain></#macro>
<div id="opDoor${uuid!}">
<@ZKUI.Process id="operateDoor${uuid}" dealPath="" onFinish="onFinishHandler" confirmText="acc_dev_start" onSure="cusConfirm">
<!-- content内容标签必须指定name属性，用来指定嵌套位置 -->
	<@ZKUI.Content name="op">
		<form action="accDoor.do?${type}" method="post" id="${formId}" onkeydown="if(event.keyCode==13){return false;}">
			<input type="hidden" name="type" id="type" value="${(type)!}"/>
			<input type="hidden" name="ids" id="doorIds" value="${(retIds)!}"/>
			<input type="hidden" class="doorsName" name="name" id="doorNameById" value="${(doorsName)!}"/>
			<input type="hidden" class="doorsName" tip="<@i18n 'acc_door_disabled'/>" name="disabledDoorsName" value="${(disabledDoorsName)!}"/>
			<input type="hidden" class="doorsName" tip="<@i18n 'acc_door_offline'/>" name="offlineDoorsName" value="${(offlineDoorsName)!}"/>
			<input type="hidden" class="doorsName" tip="<@i18n 'acc_door_notSupport'/>" name="notSupportDoorsName" value="${(notSupportDoorsName)!}"/>
			<@i18n 'common_dev_selectedDoor'/>:
			<div name="selectDoor" id="selectDoor" class="zk-content-bg-color zk-border-color" style="border: solid 1px green; height: 110px; overflow: auto;">
			</div>
			<div class="zk-border-color" style="margin-top: 10px; padding-bottom: 10px; border: solid 1px green;">
				<table style="padding-top: 8px;" class="tableStyle" >
					<tr id="loginPwdRow" hidden="hidden">
						<td><@i18n 'auth_user_userPwd'/><span class="required">*</span></td>
						<td>
							<input type="password" maxlength="18" id="loginPwd${uuid!}" name="userLoginPwd" autofocus="true"/>
							<input type="hidden"  name="loginPwd" id="password_hidden"/>
						</td>
					</tr>
					<#if type?exists && type == "openDoor">
						<tr id="">
							<td><@i18n 'common_dev_openTimes'/><span class="required">*</span></td>
							<td>
								<input class="valid" type="text" value="5" size="8" maxlength="3" name="openInterval" id="openInterval">
								<@i18n 'common_second'/>
								<span class="form_note">(1-254)</span>
							</td>
						</tr>
					</#if>
				</table>
			</div>
		</form>
	</@ZKUI.Content>
</@ZKUI.Process>
</div>
<script type="text/javascript">

    var processWindowId = "operateDoor${uuid}";
    var processIds = ZKUI.Process.get(processWindowId).ids;

    $(function(){
        $("#opDoor${uuid!} #"+processIds.totalId).hide();
        $("#opDoor${uuid!} #"+processIds.currentId).hide();
        $("#loginPwd${uuid!}").focus();
        var $selectDoor = $("#selectDoor");
        $(".doorsName").each(function(){
            var value = $(this).val();
            if(value != '' && value.length>0)
            {
                var doorName = value.replace(/,/g,", ");
                if(!$(this).attr("tip"))
                {
                    $selectDoor.append("<div style='padding-${leftRTL!'left'}: 10px; margin-bottom: 4px;'>" + doorName + "</div>");
                }
                else
                {
                    $selectDoor.append("<div class='zk-msg-error' style='padding-${leftRTL!'left'}: 10px; margin-top: 10px;'><b>"+$(this).attr("tip")+"</b><br/>" + doorName + "</div>");
                }
            }
        });
        $selectDoor.append("<br>");
        if($("#doorNameById").val() == "")
        {
            $("#opDoor${uuid!} #"+processIds.confirmButtonId).attr("disabled", true);
        }
        /*if(!($("#type").val() == "openDoor"))
        {
            $("#openDoor").hide();
        }*/
        $("#${formId}").validate({
            debug : true,
            rules :
                {
                    "openInterval" :
					{
						required: true,
						range : [1, 254],
						digits: true
					}
                },
            submitHandler: function() {
                formSubmit();
            }
        });
    });

    function formSubmit()
    {
        $("#"+processIds.confirmButtonId).attr("disabled", true);
        var time = $("#openInterval").val();
		if("${(type)!}" != "openDoor" || (time < 255 && time > 0))
		{
			$('#${formId}').ajaxSubmit({
				success: function(data)
				{
					if (!data)
					{
						openMessage(msgType.error);
					}
					else if(data.ret == "500")
					{
						$("#opDoor${uuid!} #"+processIds.infoDivId).empty();
                        $("#opDoor${uuid!} #"+processIds.infoDivId).append("<font color=\"red\"><@i18n 'common_dev_opFaileAndReason'/><@i18n 'auth_user_pwdIncorrect'/></font>");
                        $("#opDoor${uuid!} #"+processIds.confirmButtonId).attr("disabled", false);
					}
					else {
						$("#opDoor${uuid!} #"+processIds.infoDivId).empty();
						var msgStr = data["msg"];
						var dataObj = data["data"];
						if(dataObj == 'undefined')
						{
							dataObj = "<@i18n 'acc_notReturnMsg'/>";
						}
						$("#opDoor${uuid!} #"+processIds.infoDivId).append(I18n.getValue(msgStr) +"</br><font color=\"red\">"+ dataObj+"</font>");
					}
					$("#opDoor${uuid!} #"+processIds.confirmButtonId).attr("disabled", false);
				},
				error: function(XMLHttpRequest, textStatus, errorThrown)
				{
					messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
				}
			});
		}
		else
		{
			$("#opDoor${uuid!} #"+processIds.infoDivId).empty();
			var msg = "<@i18n 'common_jqMsg_range'/>".format(1,254);
			$("#opDoor${uuid!} #"+processIds.infoDivId).append("<font color=\"red\"><@i18n 'common_dev_opFaileAndReason'/>" + msg + "</font>");
			$("#opDoor${uuid!} #"+processIds.confirmButtonId).attr("disabled", false);
		}
    }
    function cusConfirm()
    {
        $("#${formId}").submit();
    }

    $("#loginPwd${uuid!}").on("change", function() {
		if($("#loginPwd${uuid!}").val()) {
			$("#password_hidden").val(hex_md5($("#loginPwd${uuid!}").val()));
		}
		else {
			$("#password_hidden").val("");
		}
	})
	isNeedValidUserForExport(function(ret) {
		if(ret && ret.ret === sysCfg.success) {
			$("#loginPwdRow").hide();
			$("input[name='userLoginPwd']").rules("remove");
		} else {
			$("#loginPwdRow").show();
			$("input[name='userLoginPwd']").rules("add", {required : true});
		}
	})
</script>
