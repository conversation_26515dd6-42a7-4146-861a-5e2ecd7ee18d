<#include "/public/template/editTemplate.html">
<#macro editContent>
<form method="post" onkeydown="if(event.keyCode==13){return false;}" id="${formId}" action="/accDoor.do?save" enctype="multipart/form-data" >
	<#if (item.id)?exists>
		<input type="hidden" name="id" id="doorId" value="${(item.id)!}" />
		<input type="hidden" name="deviceId" id="deviceId" value="${(item.deviceId)!}" />
		<input type="hidden" name="machine_type" value="${(item.devMachineType)!}" />
		<input type="hidden" name="machine_protype" value="${(item.devCommType)!}"/>
	</#if>
    <input type="hidden" name="wgInputType" value="1" />
    <input type="hidden" name="wgOutputType" value="1" />
	
	<table cellpadding="0" cellspacing="0" style="width: 100%">
		<tr>
			<td>
				<table cellpadding="8" class="tableStyle">
				
					<tr>
						<th ><label><@i18n 'common_dev_name'/></label><span class="required">*</span></th>
						<td>
							<input type="text" name="deviceAlias" value="${(item.deviceAlias)!}" disabled="disabled"/>
						</td>
						<th><label><@i18n 'acc_door_number'/></label><span class="required">*</span></th>
						<td>
							<input type="text" name="doorNo" id="id_door_no" value="${(item.doorNo)!}" disabled="disabled"/>
						</td>
					</tr>
					<tr>
						<th><label><@i18n 'acc_door_name'/></label><span class="required">*</span></th>
						<td>
							<input type="text" id="id_door_name" maxlength="100" name="name" value="${(item.name)!}" />
						</td>
						<th><label><@i18n 'acc_door_activeTimeZone'/></label><span class="required">*</span></th>
						<td>
							<@ZKUI.Combo empty="false" value="${(item.activeTimeSegId)!}" name="activeTimeSegId" hideLabel="true" readonly="true" width="148" path="/accTimeSeg.do?getTimeSegList">
							</@ZKUI.Combo>
						</td>
					</tr>
					<tr>
						<th><label><@i18n 'common_verifyMode_entiy'/></label><span class="required">*</span></th>
						<td>
							<#if (newVFStyles)?exists>
								<@ZKUI.Combo id="newVerifyStyleCombo${uuid!}" empty="false" value="0" onChange="changeVerifyMode" hideLabel="true" readonly="true" width="148">
									<option value="0"><@i18n 'common_verifyMode_cardOrFpOrPwd'/></option>
									<option value="1"><@i18n 'common_newVerify_mode_manualConfig'/></option>
								</@ZKUI.Combo><br/>
								<div id="newVerifyStyleSetDiv${uuid!}" style="display:none;">
									<label>
										<#if (verifyStyleLogic)?exists && verifyStyleLogic=="0">
										<@ZKUI.Input hideLabel="true" type="radio" name="verifyStyleLogic" value="0" checked="checked"/>
										<#else>
										<@ZKUI.Input hideLabel="true" type="radio" name="verifyStyleLogic" value="0"/>
									</#if>
									<span><@i18n 'common_newVerify_mode_logicOr'/></span>
									</label>
									<label>
										<#if (verifyStyleLogic)?exists && verifyStyleLogic=="1">
										<@ZKUI.Input hideLabel="true" type="radio" name="verifyStyleLogic" value="1" checked="checked"/>
										<#else>
										<@ZKUI.Input hideLabel="true" type="radio" name="verifyStyleLogic" value="1"/>
									</#if>
									<span><@i18n 'common_newVerify_mode_logicAnd'/></span>
									</label>
									<br/>
									<@ZKUI.ComboTree empty="false" value="${(newVFStyles)!}" id="newVFStylesTree${uuid!}" name="verifyMode" hideLabel="true" isTree="true" readonly="true" width="148" url="accDeviceVerifyMode.do?getDeviceVerifyModeTreeByDeviceId&deviceId=${(item.deviceId)!}"/>
								</div>
							<#else>
								<@ZKUI.Combo empty="false" value="${(item.verifyMode)!}" name="verifyMode" hideLabel="true" readonly="true" width="148" path="accDoor.do?getVerifyMode&deviceId=${(item.deviceId)!}">
								</@ZKUI.Combo>
							</#if>
						</td>
						<th><label><@i18n 'acc_door_lockOpenDuration'/></label><span class="required">*</span></th>
						<td>
							<input type="text" name="lockDelay" id="id_lock_delay" value="${(item.lockDelay)!}" maxlength="3" /><span class="gray"><@i18n 'common_second'/>(1-254)</span>
						</td>
					</tr>
					<tr>
						<th id="optionTime"><label><@i18n 'common_punchInterval'/></label><span class="required">*</span></th>
						<td>
							<input type="text" name="actionInterval" id="id_card_intervaltime" value="${(item.actionInterval)!}" maxlength="3" /><span class="gray"><@i18n 'common_second'/>(0-254)</span>
						</td>
						<th><label><@i18n 'acc_door_sensorType'/></label><span class="required">*</span></th>
						<td>
							<@ZKUI.Combo name="doorSensorStatus" hideLabel="true" empty="false" id="idDoorSensorStatus" width ="148px">
								<option selected="selected" value="0"><@i18n 'common_none'/></option>
								<option value="1" <#if (item.doorSensorStatus)?exists && (item.doorSensorStatus) == 1> selected="selected" </#if>><@i18n 'acc_door_normalOpen'/></option>
								<option value="2" <#if (item.doorSensorStatus)?exists && (item.doorSensorStatus) == 2> selected="selected"</#if>><@i18n 'acc_door_normalClose'/></option>
							</@ZKUI.Combo>
						</td>
						</tr>
					<tr>
						<th id="tdInApbDuration"><label><@i18n 'acc_door_entranceApbDuration'/></label></th>
						<td>
							<input type="text" name="inApbDuration" id="idInApbDuration" value="${(item.inApbDuration)!}"  maxlength="3" /><span class="gray"><@i18n 'common_minute'/>(0-120)</span>
						</td>
						<th id="tdSensorDelay" class="gray"><label><@i18n 'acc_door_sensorDelay'/></label><span class="required" id="idRequiredSensor"></span></th>
						<td>
							<input type="text" name="sensorDelay" id="idSensorDelay" value="${(item.sensorDelay)!}"  maxlength="3"/><span class="gray"><@i18n 'common_second'/>(1-254)</span>
						</td>
						</tr>
					<tr>
						<th><label><@i18n 'acc_door_duressPassword'/></label></th>
						<td>
							<input type="password" maxlength="6" size="8" name="forcePwd" onkeypress="return isNotSpace(event);" id="id_force_pwd" value="${(item.forcePwd)!}" /><span class="gray">(<@i18n 'acc_door_max6BitInteger'/>)</span>
						<th><label><@i18n 'acc_door_passageModeTimeZone'/></label></th>
						<td>
							<@ZKUI.Combo value="${(item.passModeTimeSegId)!}" name="passModeTimeSegId" hideLabel="true" readonly="true" width="148" path="/accTimeSeg.do?getTimeSegList">
							</@ZKUI.Combo>
						</td>
					</tr>
					<tr>
						<th><label><@i18n 'common_emergencyPassword'/></label></th>
						<td>
							<input type="password" maxlength="8" size="8" name="supperPwd" onkeypress="return isNotSpace(event);"  id="id_supper_pwd" value="${(item.supperPwd)!}" /><span class="gray">(<@i18n 'common_8BitInteger'/>)</span>
						</td>
                        <!-- 绿标一体机开放支持设置多人开门间隔 by seven.wu 2181218-->
						<#if multiCardInterTimeFunOn?string == "true">
							<th><label><@i18n 'acc_door_combOpenInterval'/></label><span class="required">*</span></th>
							<td>
								<input type="text" maxlength="3" name="combOpenInterval" value="${(item.combOpenInterval)!}"/><span class="gray"><@i18n 'common_second'/>(5-60)</span>
							</td>
						</#if>
						<th id="tdBackLock" class="gray displayN"><label><@i18n 'acc_door_closeAndReverseState'/></label></th>
						<td class="displayN">
							<@ZKUI.Input hideLabel="true" type="checkbox" id="idBackLock" name="backLock" value="${(item.backLock)?string}"/>
						</td>
					</tr>
					<tr id="trWgInOutFmt">
						<th id="tdWgInputFmt"><label><@i18n 'pers_wgFmt_in'/></label></th>
						<td>
							<@ZKUI.Combo id="idWgInputFmt" value="${(item.wgInputFmtId)!}" empty="false" name="wgInputFmtId" hideLabel="true" readonly="true" width="148" path="/accDoor.do?getWiegandFmtList">
							</@ZKUI.Combo>
						</td>
						<th id="tdWgOutputFmt"><label>${pers_wgFmt_out}</label></th>
						<td>
							<@ZKUI.Combo id="idWgOutputFmt" value="${(item.wgOutputFmtId)!}" empty="false" name="wgOutputFmtId" hideLabel="true" readonly="true" width="148" path="/accDoor.do?getWiegandFmtList">
							</@ZKUI.Combo>
						</td>
					</tr>
					<!-- <tr>
						<th><label>${pers_wgFmt_inType}</label></th>
						<td>
							<select name="accDoor.wgInputType" id="idWgInputType">
								<option value="0" <#if (tempAccDoor.wgInputType)?exists && (tempAccDoor.wgInputType) == 0>selected="selected"</#if>>${acc_door_pin}</option>
								<option value="1" <#if (tempAccDoor.wgInputType)?exists && (tempAccDoor.wgInputType) == 1>selected="selected"</#if>>${pers_card_cardNo}</option>
							</select>
						</td>
						<th><label>${pers_wgFmt_outType}</label></th>
						<td>
							<select name="accDoor.wgOutputType" id="idWgOutputType">
								<option value="0" <#if (tempAccDoor.wgOutputType)?exists && (tempAccDoor.wgOutputType) == 0>selected="selected"</#if>>${acc_door_pin}</option>
								<option value="1" <#if (tempAccDoor.wgOutputType)?exists && (tempAccDoor.wgOutputType) == 1>selected="selected"</#if>>${pers_card_cardNo}</option>
							</select>
						</td>
					</tr> -->
					<tr id="trHostOutState">
						<th id="hostOutState"><label><@i18n 'acc_door_hostOutState'/></label></th>
						<td>
							<@ZKUI.Combo hideLabel="true" width="148" empty="false" name="hostStatus" id="idHostStatus">
								<option value="1" <#if (item.hostStatus)?exists && (item.hostStatus) == 1>selected="selected"</#if>><@i18n 'acc_door_inState'/></option>
								<option value="0" <#if (item.hostStatus)?exists && (item.hostStatus) == 0>selected="selected"</#if>><@i18n 'acc_door_outState'/></option>
							</@ZKUI.Combo>
						</td>
						<th id="slaveOutState"><label><@i18n 'acc_door_slaveOutState'/></label></th>
						<td>
							<@ZKUI.Combo hideLabel="true" width="148" empty="false" name="slaveStatus" id="idSlaveStatus">
								<option value="0" <#if (item.slaveStatus)?exists && (item.slaveStatus) == 0>selected="selected"</#if>><@i18n 'acc_door_outState'/></option>
								<option value="1" <#if (item.slaveStatus)?exists && (item.slaveStatus) == 1>selected="selected"</#if>><@i18n 'acc_door_inState'/></option>
							</@ZKUI.Combo>
						</td>
					</tr>
					<tr>
						<th><label><@i18n 'acc_door_disableAudio'/></label></th>
						<td>
							<@ZKUI.Input hideLabel="true" type="checkbox" id="idIsDisableAudio" name="isDisableAudio" value="${((item.isDisableAudio)?string)!}"/>
						</td>
						<#if (supportDoorPassword)?exists && supportDoorPassword>
						<th><label><@i18n 'acc_door_doorPassword'/></label></th>
						<td>
							<input type="password" maxlength="6" name="doorPwd" onkeypress="return isNotSpace(event);" value="${(item.doorPwd)!}" />
						</td>
						</#if>
					</tr>
				</table>
			</td>
		</tr>
	</table>
	<!-- div style="position: absolute; z-index: 1; bottom: 45px; width: 100%;" -->
	<div style="margin-${leftRTL!'left'}: 200px;margin-top: 45px;">
		<table cellpadding="8" class="tableStyle" >
			<tr>
				<td style="width: 240px;"><label><@i18n 'common_copySettingsTo'/></label></td>
				<td colspan="3">
					<@ZKUI.Combo hideLabel="true" width="240" empty="false" name="applyTo" id="id_applyTo" >
						<option value="0" selected="selected">---------</option>
						<option value="2"><@i18n 'acc_door_allDoorOfAllStandaloneDev'/></option>
					</@ZKUI.Combo>
				</td>
			</tr>
		</table>
	</div>
</form>
<script type="text/javascript">
    $().ready(function() {
    	editAccDoor();

        //自定义验证
        //sensorDelay
        jQuery.validator.addMethod("sensorDelayValid", function(value, element){
            var lockDelay = $("input[name='lockDelay']").val();
            if(lockDelay != "" && parseInt(value) <= parseInt(lockDelay))
            {
                window.setTimeout(function(){//为了实现异步
                    if($("#id_lock_delay").hasClass("error")  && !$("#idSensorDelay").hasClass("error"))
                    {
                        $("#id_lock_delay").valid();
                    }
                },10);
                return false;
            }
            else if($("#id_lock_delay").hasClass("error")  && !$("#idSensorDelay").hasClass("error"))
            {
                window.setTimeout(function(){//为了实现异步
                    $("#id_lock_delay").valid();
                },10);
            }
            return true;
        }, "<@i18n 'acc_door_durationLessLock'/>");
        jQuery.validator.addMethod("lockDelayValid", function(value, element){
            var sensorDelay = $("input[name='sensorDelay']").val();
            if(sensorDelay != "" && parseInt(value) >= parseInt(sensorDelay))
            {
                window.setTimeout(function(){//为了实现异步
                    if($("#idSensorDelay").hasClass("error") && !$("#id_lock_delay").hasClass("error"))
                    {
                        $("#idSensorDelay").valid();
                    }
                },10);
                return false;
            }
            else if($("#idSensorDelay").hasClass("error") && !$("#id_lock_delay").hasClass("error"))
            {
                $("#idSensorDelay").valid();
            }
            return true;
        }, "<@i18n 'acc_door_lockMoreDuration'/>");

        // 字母和数字的验证
        $("#${formId}").validate( {
            debug : true,
            rules :
                {
                    "lockDelay" : {
						required : true,
						range : [1,254],
						digits : true,
						lockDelayValid : true
					},
                    "actionInterval" : {
						required : true,
						range : [0,254],
						digits : true
					},
                    "supperPwd" : {
						digits : true,
						rangelength : [ 8, 8 ]
					},
                    "activeTimeSegId" : {
						required : true
					},
                    "doorSensorStatus" : {
						required : true
					},
                    "verifyMode" : {
						required : true
					},
                    "name" : {
						required : true,
						validInputStr: true,
						unInputChar:true,
						overRemote: ["/accDoor.do?isExist", "${(item.name)!}"]
					},
                    "forcePwd" : {
						digits : true,
						rangelength : [ 0, 6 ],
						remote : {
							url : "/accDoor.do?checkPwd", //后台处理程序
							type : "post", //数据发送方式
							dataType : "json" //接受数据格式
						}
					},
                    "sensorDelay" : {
						range : [1,254],
						sensorDelayValid : true
					},
                    "inApbDuration" : {
						range: [0,120],
						digits : true
					},
                    "latchTimeOut" : {
						range :[5,254]
					},
					"combOpenInterval" : {
						required : true,
						digits: true,
						range: [5,60]
					},
					"doorPwd" : {
						doorPwdValid : true
					}
                },
            messages:
                {
                    "name":
                        {
                            remote: "<@i18n 'acc_door_nameRepeat'/>"
                        },
                    "aforcePwd":
                        {
                            remote : "<@i18n 'acc_door_duressPwdError'/>"
                        },
                    "supperPwd":
                        {
                            rangelength: jQuery.validator.format("<@i18n 'acc_door_urgencyStatePwd'/>")
                        }
                },
            submitHandler : function()
            {
            	// 处理新验证方式转为十进制提交保存
				<#if (newVFStyles)?exists>
					/** 验证方式校验 */
					var validFlag = validNewVerifyMode();
					if (!validFlag) {
						return;
					}

					var newVFStyleComboValue = ZKUI.Combo.get("newVerifyStyleCombo${uuid!}").combo.getSelected();
					// 逻辑位
					var verifyStyleLogic = $("input:radio[name='verifyStyleLogic']:checked").attr("value");
					// 勾选的验证方式
					var verifyMode = ZKUI.ComboTree.get("newVFStylesTree${uuid!}").getValue();
					var newVFStylesArray = [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];
					// 选项为自动识别
					if (newVFStyleComboValue == "0") {
						verifyStyleLogic = "0";
						verifyMode = ZKUI.ComboTree.get("newVFStylesTree${uuid!}").tree.getAllChildless();
					}
					var newVFStylesLength = newVFStylesArray.length - 1;
					// 因二进制低位到高位由右向左,所以逆序赋值
					newVFStylesArray[newVFStylesLength] = parseInt(verifyStyleLogic);
					var verifyModeArray = verifyMode.split(",");
					for (var i=0; i < verifyModeArray.length; i++) {
						if (verifyModeArray[i] != 0) {
							newVFStylesArray[newVFStylesLength-parseInt(verifyModeArray[i])] = 1;
						}
					}
					// 将二进制转为十进制数
					var newVerifyMode = parseInt(newVFStylesArray.toString().replace(/,/g, ""),2);
					$("input[name='verifyMode']").attr("value", newVerifyMode);
				</#if>

                $("#id_card_intervaltime").attr("disabled", false);//新一体机修改次属性
                $("input[name='deviceAlias']").attr("disabled", false);
                $("input[name='doorNo']").attr("disabled", false);
                $('#${formId}').ajaxSubmit({
                    success: function(result)
                    {
                        dealRetResult(result,function(){
                            if(typeof(updateDoorName) != "undefined")//实时监控-窗口关闭之前
                            {
                                try {
                                    updateDoorName($("#${formId} #doorId").val(), $("#${formId} #id_door_name").val());//修复加载过一次实时监控，切换到门页面编辑报错问题
                                }
                                catch (e) {
                                }
                            }
                            if(isContinueAdd)
                            {
                                refreshCurrentWindow();
                            }
                            else
                            {
                                DhxCommon.closeWindow();
                            }
                            //如果不选中树中任何节点，就是使用下面注释
                            //tree.clearSelection(tree.getSelectedItemId());
							if (typeof(accDoorGridName) != "undefined") {
                                ZKUI.Grid.reloadGrid(accDoorGridName);//重新加载数据列表
                            }
                            if(typeof(tree) != "undefined" && document.getElementById("gridbox"))
                            {
                                var id = tree.getSelectedItemId();
                                if(id.indexOf("_") < 0)
                                {
                                    mygrids["gridbox"].clearAndLoad(mygrids["gridbox"].path+"&deviceId="+id,"json");
                                }
                                else
                                {
                                    var id = id.split("_")[0];
                                    mygrids["gridbox"].clearAndLoad(mygrids["gridbox"].path+"&authAreaId="+id,"json");
                                }
                            }
                        });
                    },
                    error: function(XMLHttpRequest, textStatus, errorThrown)
                    {
                        //alert(XMLHttpRequest+" --- "+textStatus+" --- "+errorThrown)
                        messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
                    }
                });
            }
        });
        //修改实时监控一体机设置门验证门磁延时时长输入任意值时可以点击确定的错误，原因为胁迫密码验证失效；    modified by 吴春婷   20160530
        $("#id_force_pwd").valid();//解决实时监控编辑门时，门名称验证失效问题
    });
	
	
	$("#idSensorDelay").attr("disabled", "disabled");
	
	<#if (item.isDisableAudio)?exists>
		<#if (item.isDisableAudio)?string == "true">
			$("#idIsDisableAudio").attr("checked", "true");
			$("#idIsDisableAudio").val(true);
		<#else>
			$("#idIsDisableAudio").removeAttr("checked");
			$("#idIsDisableAudio").val(false);
		</#if>
	</#if>
	
	$("#idIsDisableAudio").change(function(){
		if($(this).attr("checked") == "checked")
		{
			$("#idIsDisableAudio").val(true);
		}
		else
		{
			$("#idIsDisableAudio").val(false);
		}
	});
	
	// 门磁类型判断
	checkSensorStatus(ZKUI.Combo.get("idDoorSensorStatus").combo.getSelectedValue());

	ZKUI.Combo.get("idDoorSensorStatus").combo.attachEvent("onChange", function(){
		checkSensorStatus(ZKUI.Combo.get("idDoorSensorStatus").combo.getSelectedValue());
	});
	
	function checkSensorStatus(val)
	{
		if(val == 0)// 无门磁
		{
			$("#idSensorDelay").val("");
			$("#idSensorDelay").attr("disabled", "disabled");
			$("#tdSensorDelay").attr("class", "gray");
			$("#id_lock_delay").removeClass("error");
			$(".ts_box").remove();
			$("#idSensorDelay").rules("remove");
			$("#idSensorDelay").valid();
			$("#idRequiredSensor").html("");
		}
		else// 常开、常闭
		{
			$("#idSensorDelay").removeAttr("disabled");
			$("#tdSensorDelay").removeAttr("class");
			$("#idSensorDelay").rules("add", {digits: true,range: [1,254],required:true,sensorDelayValid : true});
			$("#idSensorDelay").val(<#if (item.sensorDelay)?exists>${(item.sensorDelay)!}<#else>15</#if>);
			$("#idRequiredSensor").html("*");
		}
	}
	//此处根据设备参数决定显示的内容
	$.ajax({
		type: "GET",
		url: "/accDoor.do?getAccDeviceOpt&deviceId="+ $("#deviceId").val(),
		dataType: "json",
		async: false,
        success: function(result)
        {
            var data = result.data;
			var cardFormatFun = data.CardFormatFunOn;// 为1支持韦根格式
			var timeApbFun = data.TimeAPBFunOn;// 为1支持入时间反潜
			var standaloneDevice = data.standaloneDevice;//根据设备名过滤是否支持门的操作间隔
			if(cardFormatFun == 0)// 韦根格式
			{
				$("#tdWgInputFmt").attr("class", "gray");
				$("#idWgInputFmt").attr("disabled", "disabled");
				$("#tdWgOutputFmt").attr("class", "gray");
				$("#idWgOutputFmt").attr("disabled", "disabled");
				$("#trWgInOutFmt").hide();
			}
			if(timeApbFun == 0)// 入时间反潜
			{
				$("#tdInApbDuration").attr("class", "gray");
				$("#idInApbDuration").attr("disabled", "disabled");
			}
			
			if(standaloneDevice)//一体机不允许设置操作时间
			{
				$("#optionTime").attr("class", "gray");
				$("#id_card_intervaltime").attr("disabled", "disabled");
			}
		},
		error:function (XMLHttpRequest, textStatus, errorThrown)
		{
			//alert(XMLHttpRequest+" --- "+textStatus+" --- "+errorThrown)
		    messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
		}
	});

	// 编辑
    function editAccDoor() {
		window.setTimeout(function() {
			if ($("#doorId").val() != "") {
				<#if newVFStyles?exists>
					var treeItemsNo = ZKUI.ComboTree.get("newVFStylesTree${uuid}").tree.getAllChildless();
					var treeSelectedNo = ZKUI.ComboTree.get("newVFStylesTree${uuid}").combo.getSelectedValue();
					if (treeItemsNo != treeSelectedNo) {
						ZKUI.Combo.PULL["newVerifyStyleCombo${uuid}"].combo.setComboValue('1');
						$("#newVerifyStyleSetDiv${uuid!}").show();
					}
				</#if>
			}
		},500)
    }

	/** 修改验证方式 */
	function changeVerifyMode(value) {
		// 自动识别
		if (value == "0") {
			$("#newVerifyStyleSetDiv${uuid!}").hide();
			var treeItemsNo = ZKUI.ComboTree.get("newVFStylesTree${uuid}").tree.getAllChildless();
			ZKUI.ComboTree.get("newVFStylesTree${uuid}").setValue(treeItemsNo);
		} else if (value == "1") {
			// 手动配置
			$("#newVerifyStyleSetDiv${uuid!}").show();
		}
	}

	/** 校验验证方式，L3密码不能作为独立验证方式使用 */
	function validNewVerifyMode() {
		var flag = true;
		// 安全等级大于等于3
        if (sysCfg.securityLevel && sysCfg.securityLevel >= 3) {
        	var newVFStyleComboValue = ZKUI.Combo.get("newVerifyStyleCombo${uuid!}").combo.getSelected();
        	// 自动识别不校验
        	if (newVFStyleComboValue == "0") {
        		return flag;
        	}
			var verifyStyleLogic = $("input:radio[name='verifyStyleLogic']:checked").attr("value");
			var verifyMode = ZKUI.ComboTree.get("newVFStylesTree${uuid!}").getValue();
			// "或"逻辑关系，勾选项中不能包含密码，密码对应位为9
			if (verifyStyleLogic == "0" && verifyMode.indexOf("9") != -1) {
				flag = false;
			}
			// "与"逻辑关系，不能只勾选密码
			if (verifyStyleLogic == "1" && verifyMode == "9") {
				flag = false;
			}
			if (!flag) {
				openMessage(msgType.warning, I18n.getValue("acc_door_verifyModeSinglePwd"));
			}
        }
		return flag;
	}

	jQuery.validator.addMethod("doorPwdValid", function (value, element) {
		var pattenChar = /^[A-Za-z0-9]+$/;
		return this.optional(element) || (pattenChar.test(value));
	}, function () {
		return I18n.getValue("pers_person_pinPrompt");
	});
</script>
</#macro>
