<#include "/public/template/editTemplate.html">
<#macro editContent>
<script type="text/javascript">
    (function () {
        DhxCommon.updateWindow(400,200);
        $("#${formId}SaveContinue").hide();//隐藏保存并继续按钮
        $("#${formId}OK").hide();//隐藏确认按钮
    })();
    accNotSupportTip();
    function accNotSupportTip() {
        var failedReasons = ["devNotOpForOffLine", "devNotSupportFunction", "devNotOpForDisable"];
        if(${failedReason} != null){
            var value = I18n.getValue("acc_dev_"+failedReasons[${failedReason}]);
            $("#result${uuid}").append(value);
        }
    }
</script>
<div style="text-align: center; line-height: 24px; margin-top: 32px; ">
    <img src="/public/images/alert.png"/>
    <span id="result${uuid}" class='warningColor'></span>
</div>
</#macro>