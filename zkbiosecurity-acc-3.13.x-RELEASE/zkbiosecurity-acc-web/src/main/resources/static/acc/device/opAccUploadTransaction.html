<script type="text/javascript">
    var alias = new Array();
    (function(){
        var devNameStr = "${(devicesName)!}";
        var devArr = devNameStr.split(";");
        var disabledDevNameStr = "${(disabledDevName)!}";
        var offlineDevNameStr = "${(offlineDevName)!}";
        if(devNameStr.length == 0)
        {
            $("#accUploadTransaction${uuid}ConfirmButton").attr("disabled", true);
        }
        else
        {
            for(var i=0; i<devArr.length; i++)
            {
                var currDev = devArr[i];
                var devsuffix = currDev.split(":")[1];
                var devSuffixArr = devsuffix.substring(0,devsuffix.length-1).split(",");
                var ids ="";
                var combDevName ="" ;
                var devAlias ="" ;
                for(var j=1; j<=devSuffixArr.length; j++)
                {
                    ids += devSuffixArr[j-1].split("&")[1] + ",";
                    combDevName += devSuffixArr[j-1].split("&")[0] + ",";
                    devAlias += devSuffixArr[j-1].split("&")[0] + ",";
                    if(j % 5 == 0)
                    {
                        combDevName += "</br>";
                    }
                }
                ids = ids.substring(0,ids.length-1);
                combDevName = combDevName.substring(0, combDevName.length-1);
                devAlias = devAlias.substring(0, devAlias.length-1);
                var devTypeName = currDev.split(":")[0];
                $("<div style='padding-left: 10px; margin-bottom: 4px;' id='" + devTypeName + "' value='" + ids + "'>" +
                    "<span id='accUploadTransactionDevRadioSpan"+ i +"'></span>" +
                    "<span style='font-weight: bold;'>"+ devTypeName + "</span>" + " : " + combDevName + "</div>").appendTo("#selectDev");
                loadUIToDiv("input", "#accUploadTransactionDevRadioSpan"+i, {
                    useInputReq:true,
                    hideLabel:true,
                    type:"radio",
                    name:"devRadio",
                    value:currDev.split(":")[0],
                    defaultChecked:i==0
                });
                alias[currDev.split(":")[0]] = devAlias;
            }
        }

        if(disabledDevNameStr != "" && disabledDevNameStr.length > 0)
        {
            $("<div class='zk-msg-error' style='padding-left: 10px; margin-top: 10px;'><b><@i18n 'acc_dev_disabled'/></b><br/>" + disabledDevNameStr + "</div>").appendTo("#selectDev");
        }

        if(offlineDevNameStr != "" && offlineDevNameStr.length > 0)
        {
            $("<div class='zk-msg-error' style='padding-left: 10px; margin-top: 10px;'><b><@i18n 'acc_dev_offline'/></b><br/>" + offlineDevNameStr + "</div>").appendTo("#selectDev");
        }
    })();

    // 延时加载，避免上面loadUIToDiv,方法内取不到元素数据
    window.setTimeout(function () {
        //对选择的设备进行操作  主要是将选择的设备的Id赋值给ids
        $("input:radio[name='devRadio']").each(function(){
            $(this).click(function(){
                var val = $(this).val();
                var divVal  = val.replace(/\//g, '\\/').replace("(", "\\(").replace(")", "\\)");
                $("#devIds").attr("value", $("div#"+divVal).attr("value"));
                showOptRadio(val);
            });
        });
        //初始化
        $('input:radio[name="devRadio"]:checked').each(function(){
            var val = $(this).val();
            var divVal  = val.replace(/\//g, '\\/').replace("(", "\\(").replace(")", "\\)");
            $("#devIds").attr("value", $("div#"+divVal).attr("value"));
            showOptRadio(val);
        });
    }, 100);
    //根据设备的名称显示所允许的操作
    function showOptRadio(val)
    {
        var supportDevFun = "${(supportDevFun)}";
        var supportDevFunArr = supportDevFun.split(",");
        for(var i=0;i < supportDevFunArr.length;i++)
        {
            var nameAndFun = supportDevFunArr[i];
            if(nameAndFun.indexOf(val) >= 0)
            {
                var fun = nameAndFun.split(":")[1];
                if(fun == "1")
                {
                    $("#getNewOrAllTrans").hide();
                    $("#getTrans").show();
                    $("#id_get_records_3").attr("checked","checked");
                }
                else
                {
                    $("#id_get_records_0").attr("checked","checked");
                    $("#getNewOrAllTrans").show();
                    $("#getTrans").hide();
                }
            }
        }
    }

    function accUploadTransaction()
    {
        var submitFun = function(){
            if($("#startTime${uuid}").hasClass("error"))
            {
                return;
            }
            if($("#endTimeId${uuid}").hasClass("error"))
            {
                return;
            }
            $("#accUploadTransaction${uuid}ConfirmButton").attr("disabled", true);
            //操作处理线程
            var getRecords = $("input[name='get_records']:checked").val();
            var devRadio = $("input[name='devRadio']:checked").val();
            var aliasStr = alias[devRadio];
            var devIds = $("#devIds").val();
            var checkVal =  1;
            if(getRecords == 1)
            {
                checkVal = 0;
            }
            else
            {
                checkVal = 1;
            }
            var setValidDate = $("input[name='setValidDate']").val();
            var startTime = $("input[name='startTime']").val();
            var endTime = $("input[name='endTime']").val();
            $("input[name='get_records'][value='"+checkVal+"']").attr("disabled",true);
            $("#setValidDate${uuid}").attr("disabled",true);
            $("#startTime${uuid}").attr("disabled",true);
            $("#endTimeId${uuid}").attr("disabled",true);

            ZKUI.Process.get("accUploadTransaction${uuid}").options.dealPath="accDevice.do?uploadTransaction&devId=" + devIds + "&records=" + getRecords + "&alias=" + encodeURIComponent(aliasStr)
            + "&setValidDate=" + setValidDate + "&startTime=" + startTime + "&endTime=" + endTime;
            ZKUI.Process.get("accUploadTransaction${uuid}").beginProgress();
        }
        submitFun();
    }

    function finishUploadTransactionProgress()
    {
        $("input[name='get_records']").attr("disabled",false);
        $("#setValidDate${uuid}").attr("disabled",false);
        $("#startTime${uuid}").attr("disabled",false);
        $("#endTimeId${uuid}").attr("disabled",false);
        $('#accUploadTransaction${uuid}ConfirmButton').attr('disabled',false);
    }

    $("#setValidDate${uuid}").change(function(){
        if(($(this).is(":checked")))
        {
            $('#validDate${uuid}').show();
        }
        else
        {
            $('#validDate${uuid}').hide();
        }
    }).change();

</script>

<@ZKUI.Process id="accUploadTransaction${uuid}" onFinish="finishUploadTransactionProgress" type="single" confirmText="acc_dev_start" onSure="accUploadTransaction">
<!-- content内容标签必须指定name属性，用来指定嵌套位置 -->
    <@ZKUI.Content name="op">
        <form action="accDevice.do?uploadTransaction" method="post" onsubmit="return false;" onkeydown="if(event.keyCode==13){return false;}" id="accUploadTransactionForm${uuid}" enctype="multipart/form-data">
            <input type="hidden"  name="devIds" id="devIds" value=${(retIds)!} />
            <!--<input type="hidden"  name="devName" id="devNameById" value="${(devicesName)!}"/>-->
            <!--<input type="hidden" id="disabledDevNameById" name="disabledDevName" value="${(disabledDevName)!}"/>-->
            <!--<input type="hidden" id="offlineDevNameById" name="offlineDevName" value="${(offlineDevName)!}"/>-->
            <!--<input type="hidden" id="supportDevFunById" name="supportDevFun" value="${(supportDevFun)}"/>-->
            <div style="padding: 0px 10px;">
                <div style="padding-bottom: 2px;"><label><@i18n 'common_dev_selectedDev'/></label></div>
                <div class="access-infoDiv zk-content-bg-color zk-border-color" style="border: solid 1px green;background-color:white;width:99%;height: 120px; overflow:auto">
                    <div name="selectDev" id="selectDev"></div>
                </div>
            </div>
            <table cellpadding="0" cellspacing="0" width="100%">
                <tr style="height: 20px;" id="getNewOrAllTrans">
                    <td width="40%">
                        <@ZKUI.Input type="radio" name="get_records" value="1" id="id_get_records_0" checked="checked"/> <@i18n 'common_dev_getNewTrans'/>
                    </td>
                    <td width="40%">
                        <@ZKUI.Input type="radio" name="get_records" value="0" id="id_get_records_1"/> <@i18n 'common_dev_getAllTrans'/>
                    </td>
                </tr>
                <tr style="height: 20px;" id="getTrans" hidden="hidden">
                    <td width="40%">
                        <@ZKUI.Input type="radio" name="get_records" value="3" id="id_get_records_3"/> <@i18n 'acc_dev_eventAutoCheckAndUpload'/>
                    </td>
                </tr>
                <tr style="height: 20px;">
                    <td colspan="2" style="padding: 12px 0 0 12px;">
                        <@ZKUI.Input id="setValidDate${uuid}" hideLabel="true" name="setValidDate" type="checkbox" eventCheck="true" trueValue="true" falseValue="false"/><@i18n 'common_level_setValidTime'/>
                    </td>
                </tr>
                <tr style="height: 20px;" hidden="hidden" id="validDate${uuid}">
                    <td colspan="2" style="padding: 12px 0 0 12px;">
                        <@i18n 'common_time_from'/>
                        <@ZKUI.Input type="datetime" endId="endTimeId${uuid}" id="startTime${uuid}" name="startTime" title="common_time_from" max="today" today="-3" todayRange="start" hideLabel="true" noOverToday="true" readonly="false"/>
                        <@i18n 'common_to'/>
                        <@ZKUI.Input type="datetime" id="endTimeId${uuid}" name="endTime" title="common_to" max="today" today="true" todayRange="end" noOverToday="true" hideLabel="true" readonly="false"/>
                    </td>
                </tr>
            </table>
        </form>
    </@ZKUI.Content>
</@ZKUI.Process>