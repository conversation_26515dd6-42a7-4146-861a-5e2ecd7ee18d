<#include "/public/template/editTemplate.html"> 
<#macro editContent>
<style type="text/css">
<!--
.trWifi{
	display: none;
}
-->
</style>
<form action="/accDevice.do?configParentDevice" method="post" onkeydown="if(event.keyCode==13){return false;}" id="${formId}" autocomplete="off">
<table class="tableStyle">
	<tr>
		<td align="left"></td>
	</tr>
	<tr>
		<td><div class="div_box1" style="display: block; background-image: none;position: relative;min-height: 128px;">
			<input type="hidden" value="${(tempAccDevice.id)!}" name="id">
			<table cellpadding="3px">
				<tbody>
				<tr>
					<th style="width: 100px;"><label><@i18n 'acc_dev_parent'/></label></th>
					<td>
						<!--<select id="parentDevId" name="parentDevId">
							<option value="-1">&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;&#45;</option>
							<#if (tempDevList?? && tempDevList?size>0)>
								<#list tempDevList as tempDev>
									<option value="${tempDev.id}">${tempDev.alias}</option>
								</#list>
						    </#if>
						</select>-->
						<@ZKUI.Combo name="parentDevId" id="parentDevId${uuid}" value="${(tempAccDevice.parentDeviceId)!}" hideLabel="true" empty="true" title="acc_dev_parent" path="/accDevice.do?getParentDevList" onChange="parentDevChange">
						</@ZKUI.Combo>
					</td>
				</tr>
				<tr id="tr_WebServerURL" style="display: none">
					<th>
						<label><@i18n 'common_dev_serverAddress'/></label><span class="required">*</span>
					</th>
					<td>
						<input id="WebServerURL" name="webServerURL" type="text" readonly="readonly" value="${(webServerURL)!}"/>
					</td>
				</tr>
				</tbody>
			</table>
			<div style="position: absolute;bottom: 0px;">
				<span class='warningImage' style='margin-top:3px;'></span>
				<span class='warningColor'><@i18n 'acc_dev_parentTips'/></span>
			</div>
		</div></td>
	</tr>
</table>
</form>
<script type="text/javascript">
$("#${formId}").validate({
	debug : true,
	rules :
	{
		"parentDevId":{
			// required: true
		}
	},
	submitHandler : function()
	{
		switchNetBeforeSubmit(function(){
			<@submitHandler/>
		});
	}
});

$().ready(function(){
	$("#${formId}OK").attr("disabled", true);
	$("#${formId}SaveContinue").remove();
	<#if type?string == "modParentDevice">
	/*var parentDevId = "${(tempAccDevice.parentDeviceId)!}";
	$("#parentDevId").val(parentDevId);*/
	// $("#parentDevId").change(function(){
	// 	if($(this).val() == "")
	// 	{
	// 		$("#tr_WebServerURL").show();
	// 	}
	// 	else
	// 	{
	// 		$("#tr_WebServerURL").hide();
	// 	}
	// });
	//var url = window.location.protocol + "//" +window.location.host;
	$("#${formId} #WebServerURL").val("${(webServerURL)!}");
	</#if>
});

function parentDevChange(value, text) {
	if ("${(type)!}" == "modParentDevice" && value == "") {
        $("#${formId} #tr_WebServerURL").show();
	}
	else {
        $("#${formId} #tr_WebServerURL").hide();
	}
}

function switchNetBeforeSubmit(submitFunc)
{
 	var parentDeviceId = ZKUI.Combo.get("parentDevId${uuid}").combo.getSelectedValue();
	if("${(tempAccDevice.id)!}" == parentDeviceId)
	{
		//openMessage(msgType.warning,"${common_op_processing}");
		return;
	}
	<#if tempAccDevice?? && !tempAccDevice.parentDeviceId??>
	if(parentDeviceId == '')
	{
		messageBox({messageType:"alert", text: "<@i18n 'acc_dev_seletParentDevice'/>"});
		return;
	}
	</#if>
	openMessage(msgType.loading,"<@i18n 'common_op_processing'/>");
	$.ajax({
		url:"/accDevice.do?checkServerConnection",
		type:"post",
		dataType:"json",
		data:{
			devId:"${(tempAccDevice.id)!}",
			parentDevId:parentDeviceId,
			serverAddress:"${(webServerURL)!}"
		},
		success: function(data)
        {
        	if(data[sysCfg.ret] == sysCfg.success)
			{
                getConfigParentDevResultById(data[sysCfg.data],submitFunc);
			}
        	else if(data[sysCfg.ret] == sysCfg.error)
        	{
        		messageBox({messageType:"alert", text: data[sysCfg.msg]});
        		closeMessage();
        	}
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) 
        {
        	closeMessage();
        	messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
        }
	});
}

function getConfigParentDevResultById(cmdId,submitFunc)
{
	$.ajax({
		url:"/accDevice.do?getCmdResultById",
		type:"post",
		dataType:"json",
		data:{
			id:"${(tempAccDevice.id)!}",
			cmdId:cmdId,
			type:"testNetConnect"
		},
		success: function (data)
        {
        	if(data[sysCfg.ret] == sysCfg.success)
			{
        		messageBox({messageType:"confirm", text: "<@i18n 'acc_dev_testNetConnectSuccess'/>", callback:function(result){
    				if(result)
    			    {
    					submitFunc();
    				}
    			    else
    			    {
    			    	ret = false;
    			    }
    			    return true; //close
    			}});
        		closeMessage();
			}
        	else if(data[sysCfg.ret] == sysCfg.error)
        	{
        		messageBox({messageType:"alert", text: "<@i18n 'acc_dev_testNetConnectFailed'/>"});
        		closeMessage();
        	}
        	else
        	{
        		messageBox({messageType:"alert", text: "<@i18n 'common_commStatus_cmdTimeOut'/>"});
       			closeMessage();
        	}
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) 
        {
        	messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
        }
	});
}
</script>
</#macro>
