<#assign formId = "accReplaceDev${uuid!}">
<#assign editPage = true>
<#include "/public/template/editTemplate.html">
<#macro editContent>
    <form action="accDevice.do?replaceDevInfo" method="post" id="${formId}" enctype="multipart/form-data">
            <table class="tableStyle">
                <@ZKUI.Input type="hidden" id="devId" name="devId" hideLabel="true" value="${(devId)!}"/>
                <@ZKUI.Input type="hidden" id="alias" name="alias" hideLabel="true" value="${(alias)!}"/>
                <tr>
                    <th>
                        <label><@i18n 'common_dev_sn'/></label><span class="required">*</span>
                    </th>
                    <td>
                        <@ZKUI.Input type="text" id="newSn${uuid}" name="newSn" hideLabel="true" />
                    </td>
                </tr>
            </table>
    </form>
    <div style="margin-top: 30px; text-align: ${leftRTL!'left'};margin-${leftRTL!'left'}: 10px;"><span class="warningImage"></span> <span class="warningColor"><@i18n 'acc_dev_replaceTip2'/></span></div>
    <div style="margin-${leftRTL!'left'}: 10px; margin-top: 15px; text-align: ${leftRTL!'left'};"><span class="warningImage"></span> <span class="warningColor"><@i18n 'acc_dev_replaceTip1'/></span></div>
    <script type="text/javascript">
        $().ready(function() {
            $("#${formId}").validate( {
                debug : true,
                rules :
                {
                    "newSn" :
                    {
                        required : true,
                        unInputChar:true,
                        overRemote : ["accDevice.do?validSn", "${(newSn)!}"]
                    }
                },
                submitHandler : function()
                {
                    messageBox({messageType:"confirm", text: "<@i18n 'acc_dev_replaceTip'/>", callback: function(result){
                        if(result) {
                            <@submitHandler/>
                        }
                    }});
                }
            });
		});
    </script>
</#macro>