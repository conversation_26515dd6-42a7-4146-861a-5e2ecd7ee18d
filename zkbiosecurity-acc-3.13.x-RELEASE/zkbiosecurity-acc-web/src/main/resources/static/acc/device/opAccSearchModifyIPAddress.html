<#include "/public/template/editTemplate.html">
<#macro editContent>
<table class="tableStyle">
    <tr>
        <th>
            <label><@i18n "common_dev_originalIP"/></label>
        </th>
        <td>
            <div style="display:inherit;">
            <@ZKUI.IP id="idOldIP" value="${ipAddress!}" disabled="true"/>
            </div>
        </td>
    </tr>
    <tr>
        <th>
            <label><@i18n "common_dev_newIP"/></label><span class='required'>*</span>
        </th>
        <td id="idNewIP">
            <div style="display:inherit;">
            <@ZKUI.IP name="newIP" id="newIP"/>
            </div>
        </td>
    </tr>
    <tr>
        <th>
            <label><@i18n "common_subnetMask"/></label><span class='required'>*</span>
        </th>
        <td id="idSubnetMask">
            <div style="display:inherit;">
            <@ZKUI.IP name="subnetMask" id="subnetMask" value="${subnetMask!}"/>
            </div>
        </td>
    </tr>
    <tr>
        <th>
            <label><@i18n "common_gatewayAddress"/></label><span class='required'>*</span>
        </th>
        <td id="idGateway">
            <div style="display:inherit;">
            <@ZKUI.IP name="gateway" id="gateway" value="${gateway!}"/>
            </div>
        </td>
    </tr>
    <tr>
        <th>
            <label><@i18n "common_dev_commPwd"/></label>
        </th>
        <td>
            <input type="password" id="idCommPwd${uuid}"/>
        </td>
    </tr>
</table>
<script type="text/javascript">
    // 修改设备IP地址
    function modifyIPAddress()
    {
        var newIP =  ZKUI.IP.get("newIP").getValue();
        var gateway =  ZKUI.IP.get("gateway").getValue();
        var subnetMask = ZKUI.IP.get("subnetMask").getValue();
        if(newIP == "" || newIP == "...")
        {
            messageBox({messageType:"alert", text: "<@i18n 'common_dev_validNewIP'/>"});
            return false;
        }

        var reg = /^(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}$/;
        if((newIP != "" || newIP == "...") && !reg.test(newIP))
        {
            messageBox({messageType:"alert", text: "<@i18n 'common_dev_validIPv4'/>"});
            return false;
        }

        if((subnetMask != "" || subnetMask != "...")  && !reg.test(subnetMask))
        {
            messageBox({messageType:"alert", text: "<@i18n 'common_dev_validSubnetMask'/>"});
            return false;
        }

        if((gateway != "" || gateway != "..." ) && !reg.test(gateway))
        {
            messageBox({messageType:"alert", text: "<@i18n 'common_dev_validGateway'/>"});
            return false;
        }

        for(var i = 0; i < ipArray.length; i++)
        {
            if(ipArray[i] == newIP)
            {
                messageBox({messageType:"alert", text: "<@i18n 'common_dev_hasDevIP'/>"});
                return false;
            }
        }
        var rowId = "${rowId!}";
        var mac = "${mac!}";// mac 地址
        var commPwd = window.btoa($("#idCommPwd${uuid}").val());
        var ipAddress = "${ipAddress!}";
        $.ajax({
            type: "POST",
            url: "accDevice.do?modifyIPAddress",
            data: {"type": "", "newIP": newIP, "subnetMask": subnetMask, "gateway": gateway, "mac": mac, "commPwd":commPwd, "ipAddress":ipAddress},
            dataType: "json",
            async: false,
            success: function(retData){
                if(retData["ret"] == "ok")
                {
                    messageBox({messageType:"alert", text: "<@i18n 'common_op_succeed'/>", callback: function(result){
                            devGrid.cells(rowId, 0).cell.innerHTML = newIP;// 改IP
                            devGrid.cells(rowId, 2).cell.innerHTML = subnetMask;// 改子网掩码
                            devGrid.cells(rowId, 3).cell.innerHTML = gateway;// 改网关
                            for(var i = 0; i < ipArray.length; i++)
                            {
                                if(ipArray[i] == $("#idOldIP").val())
                                {
                                    ipArray.splice(i, 1);
                                    ipArray.push(newIP);
                                    break;
                                }
                            }
                            DhxCommon.closeWindow();
                        }});
                }
                else if(retData["ret"] == "pwdError")
                {
                    messageBox({messageType:"alert", text: "<@i18n 'common_dev_opFaileAndReason'/>" + retData["msg"]});
                }
                else
                {
                    messageBox({messageType:"alert", text: retData["msg"]});
                }
                data = null;
            },
            error:function (XMLHttpRequest, textStatus, errorThrown)
            {
                messageBox({messageType:"alert", text: "<@i18n 'common_prompt_serverError'/>"+ "-614"});
            }
        });
    }
</script>
</#macro>
<#macro buttonContent>
<button class='button-form' onclick="modifyIPAddress()" id="confirmButton${uuid}"><@i18n 'common_edit_ok'/></button>
<button class='button-form' onclick="DhxCommon.closeWindow()" id="closeButton${uuid}"><@i18n 'common_edit_cancel'/></button>
</#macro>