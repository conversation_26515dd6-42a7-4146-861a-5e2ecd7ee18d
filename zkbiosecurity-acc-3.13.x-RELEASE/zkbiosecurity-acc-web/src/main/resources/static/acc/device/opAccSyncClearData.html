<#assign editPage = "true">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
<form action="" method="post" id="${formId}" onkeydown="if(event.keyCode==13){return false;}">
    <table cellpadding="8" class="tableStyle">
        <tr>
            <th>
                <label id="syncDataClearContent${uuid}"></label>
            </th>
        </tr>
        <tr>
            <th>
                <@i18n 'common_dev_clearDevDataBeforeSync'/>
            </th>
            <td>
                <@ZKUI.Input hideLabel="true" type='checkbox' id='isSyncClearData'/>
            </td>
        </tr>
    </table>
</form>
<script type="">
    $().ready(function() {
        $("#syncDataClearContent${uuid}").html(I18n.getValue("common_prompt_executeOperate").format(I18n.getValue("common_dev_syncAllDataToDev")));
    })

    $("#${formId}").validate({
        debug : true,
        submitHandler: function() {
            // 给同步所有数据页面，隐藏的删除数据选项设置值
            if ($("#isSyncClearData").attr("checked") == "checked") {
                $("#clearData").attr("checked", true);
            } else {
                $("#clearData").attr("checked", false);
            }
            DhxCommon.getCurrentWindow().close();
            // 调用开始同步方法
            setAccSyncAllData();
        }
    });

</script>
</#macro>
