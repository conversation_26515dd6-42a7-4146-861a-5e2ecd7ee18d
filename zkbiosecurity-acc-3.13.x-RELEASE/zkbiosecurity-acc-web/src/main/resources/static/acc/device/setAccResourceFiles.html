<#assign formId = "${uuid!}">
<#assign editPage="true">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
<form action="accDevice.do?uploadAdResourceFile" method="post" id="${formId}" enctype='multipart/form-data' onkeydown="if(event.keyCode==13){return false;}">
    <input type='hidden' name='devId' value='${(tempAccDevice.id)!}'/>
    <table class='tableStyle'>
        <tr>
            <th>
                <label><@i18n 'common_number'/></label><span class="required">*</span>
            </th>
            <td>
                <@ZKUI.Combo name="number" readonly="true" width="148" hideLabel="true" empty="true" >
                    <option value = "1">1</option>
                    <option value = "2">2</option>
                    <option value = "3">3</option>
                </@ZKUI.Combo>
            </td>
        </tr>
        <tr>
            <th>
                <label><@i18n 'common_linkIO_displayTime'/></label><span class="required">*</span>
            </th>
            <td>
                <input type="text" id="displayTime" name="displayTime" maxlength="3" value="10">
                <span class="gray"><@i18n 'common_linkIO_s'/>(0-300)</span>
            </td>
        </tr>
        <tr >
            <th>
                <label><@i18n 'acc_dev_playOrder'/></label>
            </th>
            <td>
                <input type="text" id="playOrder" name="playOrder" maxlength="2">
            </td>
        </tr>
        <tr >
            <th><label><@i18n 'base_leftMenu_AudioFile' /></label><span class="required">*</span></th>
            <td>
                <@ZKUI.Combo name="fileId" type="radio" id="fileId" readonly="true" width="148" hideLabel="true" empty="true" path="accDevice.do?getAdResourceFile">
                </@ZKUI.Combo>
            </td>
        </tr>
    </table>
</form>
<script type="text/javascript">
    $().ready(function() {

        //验证
        $("#${formId}").validate( {
            debug: true,
            rules:
            {
                "number":{
                    required : true
                },
                "popUpTime":{
                    required : true,
                    digits: true,
                    range:[1,300]
                },
                "playOrder":{
                    digits: true,
                    range:[1,99]
                },
                "fileId":{
                    required : true
                }
            },
            submitHandler : function()
            {
                <@submitHandler />
            }
        });
    });
</script>
</#macro>