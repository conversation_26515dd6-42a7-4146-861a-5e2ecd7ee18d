<@ZKUI.Process id="accSyncTime${uuid}" type="single" confirmText="common_dev_synchronize" onSure="accSyncTime">
<!-- content内容标签必须指定name属性，用来指定嵌套位置 -->
<@ZKUI.Content name="op">
<script type="text/javascript">
    var devNameStr = "${(devicesName)!}";
    var devAlias = "";
    var disabledDevNameStr = "${(disabledDevName)!}";
    var offlineDevNameStr = "${(offlineDevName)!}";
    var warnInfoDevNameStr = "${(warnInfoDevName)!}";
    if(devNameStr == "" || devNameStr.length == 0)
    {
        $("#accSyncTime${uuid}ConfirmButton").attr("disabled", true);
    }
    else
    {
        var devArr = devNameStr.split(";");
        for(var i=0; i<devArr.length; i++)
        {
            var currDev = devArr[i];
            var devsuffix = currDev.split(":")[1];
            var devSuffixArr = devsuffix.substring(0,devsuffix.length-1).split(",");
            var ids ="";
            var combDevName ="" ;
            devAlias ="" ;
            for(var j=1; j<=devSuffixArr.length; j++)
            {
                var temp = devSuffixArr[j-1].split("&");
                ids += temp[1] + ",";
                combDevName += temp[0] + ",";
                devAlias += temp[0] + ",";
                if(j % 5 == 0)
                {
                    combDevName += "</br>";
                }
            }
            ids = ids.substring(0,ids.length-1);
            combDevName = combDevName.substring(0, combDevName.length-1);
            devAlias = devAlias.substring(0, devAlias.length-1);
            var devTypeName = currDev.split(":")[0];
            $("<div style='padding-left: 10px;' id='" + devTypeName + "' value='" + ids + "'>" +
                "<span style='font-weight: bold;'>"+ devTypeName + "</span>" + " : " + combDevName + "</div>").appendTo("#selectDev");
        }

    }

    if(disabledDevNameStr != "" && disabledDevNameStr.length > 0)
    {
        $("<div class='zk-msg-error' style='padding-left: 10px; margin-top: 10px;'><b><@i18n 'acc_dev_disabled'/></b><br/>" + disabledDevNameStr + "</div>").appendTo("#selectDev");
    }

    if(offlineDevNameStr != "" && offlineDevNameStr.length > 0)
    {
        $("<div class='zk-msg-error' style='padding-left: 10px; margin-top: 10px;'><b><@i18n 'acc_dev_offline'/></b><br/>" + offlineDevNameStr + "</div>").appendTo("#selectDev");
    }

    if(warnInfoDevNameStr != "" && warnInfoDevNameStr.length > 0)
    {
        $("<div style='padding-left: 10px; margin-top: 10px; color: #E57A14;'><b><@i18n 'acc_dev_syncTimeWarnTip'/></b><br/>" + warnInfoDevNameStr + "</div>").appendTo("#selectDev");
    }

    function accSyncTime(){
        //提交和进度条监控方法
        var submitFun = function(){
            ZKUI.Process.get("accSyncTime${uuid}").options.dealPath="/accDevice.do?syncTime&devIds=${(retIds)!}&alias=" + encodeURIComponent(devAlias);
            ZKUI.Process.get("accSyncTime${uuid}").beginProgress();
        }
        execAccConfirm($("#devIds").val(), submitFun);
    }

</script>
    <div style="padding: 0px 10px;">
        <div style="padding-bottom: 2px;"><label><@i18n 'common_dev_selectedDev'/></label></div>
        <div class="zk-content-bg-color zk-border-color" style="border: solid 1px green;background-color:white;width:99%;height: 110px; overflow:auto">
            <div name="selectDev" id="selectDev"></div>
        </div>
    </div>
    <form action="/accDevice.do?syncTime" id="opForm" method="post" >
        <input type="hidden" name="devIds" id="devIds" value="${(retIds)!}"/>
    </form>
    </@ZKUI.Content>
</@ZKUI.Process>