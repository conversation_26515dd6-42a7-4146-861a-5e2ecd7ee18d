<#assign editPage = "true">
<#assign formId = "searchDevForm">
<#include "/public/template/editTemplate.html">
<#macro editContent>
<script type="text/javascript">
$("#${formId}").validate( {
	rules : {
        "alias" :
		{
			required: true,
			unInputChar:true,
			overRemote : [ "accDevice.do?isExistAlias", "${(tempAccDevice.alias)!}" ]
		},
		"ipAddress":
		{
            overRemote : [ "accDevice.do?isExistIpAddress", "${(ipAddress)!}" ],
		},
		"authAreaId":
		{
			required: true
		}
	},
 	submitHandler: function(form)
   	{
 		if ($("#${formId} #level").hasClass("gray")) {
			ZKUI.Combo.get("idAccLevel").setValue("");
		}
		addAccDevBeforeSubmit(function(){
			var fun = function(){
				$('#${formId}').ajaxSubmit({
					async : true,
					dataType : 'json',
					beforeSubmit:function() {openMessage(msgType.loading);},
					success: function(retData)
					{
						if(retData["ret"] == "500")// 和设备通信异常
						{
							messageBox({messageType:"alert", text:retData['msg']});
							DhxCommon.closeWindow();
						}
						else if(retData["ret"] == "600")// 设备重复添加
						{
							messageBox({messageType:"alert", text:"<@i18n 'common_dev_devRepeat' />"});
							DhxCommon.closeWindow();
						}
						else if(retData["ret"] == "ok")
						{
							//validSubmit = false;// 加完设备后，重置为false，避免添加其他设备判断出问题
							//devGrid.deleteRow($("#rowId").val());// 删除行
							openMessage(msgType.success);
							devGrid.cells($("#rowId").val(), 7).cell.innerHTML = "<@i18n 'common_dev_devHasAdd'/>";// 这个设备改为已添加
							$("#addDevHint").show();
							addDevCount = addDevCount + 1;
							$("#addDevCount").text(addDevCount);
							//reloadGrid("gridbox");
							DhxCommon.closeWindow();
						}
						closeMessage();
					},
					error: function(XMLHttpRequest, textStatus, errorThrown)
					{
						if(XMLHttpRequest.status == 0)
						{
							openMessage(msgType.error, "<@i18n 'common_prompt_serverFailed'/>");
						}
						else
						{
							openMessage(msgType.error, "<@i18n 'common_prompt_serverError'/>" + XMLHttpRequest.status);
						}
					}
				});
			};
			fun();
		});
   	} 
});

//搜索设备，默认设备收拾为联网
var device_mode=1;
window.setTimeout(function(){
	$("#idCommPwd").keypress(function (event) {
		var eventObj = event || e;
		var keyCode = eventObj.keyCode || eventObj.which;
		if (keyCode != 32)
    	{
    	   return true;
    	}
		else
		{
	       return false;
		}
	}).focus(function () {
	    this.style.imeMode = 'disabled';
	}).bind("paste", function () {
	    var clipboard = window.clipboardData.getData("Text");
	    if (/^[a-zA-Z]+$/.test(clipboard))
	    {
	        return true;
	    }
	    else
	    {
	        return false;
	    }
	});
}, 300);

function accAddAcpanelTypeChange(value) {
	if(value == 4){
		$("#${formId} #trFourToTwo").show();
	}
	else
	{
		$("#${formId} #trFourToTwo").hide();
		$("#${formId} #trFourToTwo input").attr("checked",false)
	}
}
</script>
<style>
	.accSearchAddDevTable div.ip_input {
		display: inline-block;
	}
</style>
<input type="hidden" name="rowId" id="rowId" value="${rowId}" />
<form id="${formId}" action="accDevice.do?save" method="post">
	<table class="tableStyle accSearchAddDevTable">
	<tr>
		<th><label><@i18n "common_dev_name"/></label><span class="required">*</span></th>
		<td><input type="text" name="alias" id="idAlias" value="${devName}" maxlength="20" /></td>
	</tr>
	<tr>
		<th>
			<label><@i18n 'common_ipAddress'/></label><span class="required">*</span>
		</th>
		<td>
			<div style="display:inherit;">
			<@ZKUI.IP name="ipAddress" id="ipAddress" value="${ipAddress!}" disabled="true"/>
			</div>
		</td>
	</tr>
	<tr>
		<th><label><@i18n "common_dev_commPwd"/></label></th>
		<td>
			<input type="password" name="commPwd" id="idCommPwd" />
			<input type="hidden" name="ipPort" id="idIPPort" value="4370"/>
			<input type="hidden" name="commType" id="idCommType" value="${commType!}"/>
			<input type="hidden" name="acpanelOptions" id="idAcpanelOptions" />
		</td>
	</tr>
	<tr <#if "${Application['system.language']}" == "zh_CN" && "${Application['system.productCode']}" == "ZKBioAccess">style="display:none;"</#if>>
		<th>
			<label><@i18n "acc_dev_iconType"/></label><span class="required">*</span>
		</th>
		<td>
			<@ZKUI.Combo width="148" empty="false" name="iconType" hideLabel="true" value="1">
				<option value="1"><@i18n 'acc_door_entity'/></option>
				<option value="2"><@i18n 'acc_dev_carGate'/></option>
				<option value="3"><@i18n 'acc_dev_channelGate'/></option>
			</@ZKUI.Combo>
		</td>
	</tr>
	<tr id="trAcpanelType" class="div_id_acc" style="">
		<th>
			<label>
				<@i18n 'acc_dev_acpType'/>
			</label>
		</th>
		<td>
			<@ZKUI.Combo width="148" empty="false" id="accAcpanelType" name="acpanelType" hideLabel="true" value="${tempAccDevice.acpanelType!1}" onChange="accAddAcpanelTypeChange">
			<option value="1"><@i18n 'acc_dev_oneDoorACP'/></option>
			<option value="2"><@i18n 'acc_dev_twoDoorACP'/></option>
			<option value="4"><@i18n 'acc_dev_fourDoorACP'/></option>
			<option value="5"><@i18n 'acc_dev_onDoorACD'/></option>
			</@ZKUI.Combo>
		</td>
	</tr>
	<tr>
		<th><label><@i18n "base_area_entity"/></label><span class="required">*</span></th>
		<td>
			<div>
				<@ZKUI.ComboTree autoFirst="true" width="148" type="radio" url="authArea.do?tree" value="${(tempAccDevice.authAreaId)!}"  hideLabel="true" name="authAreaId"/>
			</div>
		</td>
	</tr>
	<tr id="trFourToTwo" class="div_id_acc" style="display: none;">
		<th>
			<label>
				<@i18n 'acc_dev_switchToTwoDoorTwoWay'/>
			</label>
		</th>
		<td>
			<@ZKUI.Input hideLabel="true" type="checkbox" id="idFourToTwo" name="fourToTwo" value="${(tempAccDevice.fourToTwo?string)!}"/>
		</td>
	</tr>
	<tr id="level">
		<th style="width:50%;"><label><@i18n "acc_dev_addLevel"/></label></th>
		<td>
			<@ZKUI.Combo id="idAccLevel" type="radio" width="148" path="/accLevel.do?getLevelList" name="levelId" readonly="readonly" hideLabel="true" onChange="accCheckIsAllowAddToLevel"/>
		</td>
	</tr>
	<tr id="levelTip" style="display:none;">
		<td colspan="2">
			<div><span class='warningImage' style='margin-top:3px;'></span><span class='warningColor'><@i18n "acc_dev_levelTip"/></span></div>
		</td>
	</tr>
	<tr>
		<th style="width: 50%;"><label><@i18n "common_dev_clearDevDataWhenAdding"/></label></th>
		<td>
			<@ZKUI.Input hideLabel="true" type="checkbox" name="clearAllData" id="idClearAllData" trueValue="1" falseValue="0" eventCheck="true"/>
		</td>
	</tr>
	</table>
</form>
<div style="margin-top: 10px; margin-left: 12px; text-align: left;">
	<span class="warningImage"></span>
	<span class="warningColor"><@i18n "common_dev_devClearDataTip"/></span>
</div>
</#macro>