<#include "/public/template/editTemplate.html"> 
<#macro editContent>
<style type="text/css">
<!--
.trWifi{
	display: none;
}
-->
</style>
<form action="/accDevice.do?updateNetConnectMode" method="post" onkeydown="if(event.keyCode==13){return false;}" id="${formId}" autocomplete="off">
<table class="tableStyle">
	<tr>
		<td align="left"></td>
	</tr>
	<tr>
		<td><div class="div_box1" style="display: block; background-image: none;">
			<input type="hidden" value="${(tempAccDevice.id)!}" name="devId">
			<input type="hidden" id="alias" name="alias" value="${(tempAccDevice.alias)!}"/>
			<table cellpadding="3px" class="tableStyle">
				<tbody>
				<tr>
					<th style="width: 150px;"><label><@i18n 'common_dev_netConnectMode'/></label></th>
					<td>
						<label><@ZKUI.Input name="netConnectMode" type="radio" value="0"/>&nbsp;<@i18n 'acc_dev_netModeWired'/></label>
						&nbsp;<label><@ZKUI.Input name="netConnectMode" type="radio" value="1"/>&nbsp;<@i18n 'acc_dev_netMode4G'/></label>
						&nbsp;<label><@ZKUI.Input name="netConnectMode" type="radio" value="2"/>&nbsp;<@i18n 'acc_dev_netModeWifi'/></label>
					</td>
				</tr>
				<tr class="trWifi">
					<th></th>
					<td><a id="seachWifiBtn" href="javascript:;"><@i18n 'acc_dev_searchWifi'/></a></td>
				</tr>
				<tr class="trWifi">
					<th><label><@i18n 'acc_dev_wirelessSSID'/></label><span class="required">*</span></th>
					<td><input id="wirelessSSID" name="wirelessSSID" value="${(wirelessSSID)!}" type="text"/></br></td>
				</tr>
				<tr class="trWifi">
					<th><label><@i18n 'acc_dev_wirelessKey'/></label><span class="required">*</span></th>
					<td><input id="wirelessKey" name="wirelessKey" type="password"/></td>
				</tr></tbody>
			</table>
		</div></td>
	</tr>
</table>
</form>
<script type="text/javascript">
$("#${formId}").validate({
	debug : true,
	rules :
	{
	},
	submitHandler : function()
	{
		switchNetBeforeSubmit(function(){
			<@submitHandler/>
		});
	}
});

$().ready(function(){
	$("#${formId}OK").attr("disabled", true);
	$("#${formId}SaveContinue").remove();
	var isSupportWifi = "${(isSupportWifi?string)!}",isSupport4G="${(isSupport4G?string)!}",netConnectMode="${netConnectMode!}",devId="${(tempAccDevice.id)!}";
	if(isSupport4G=="false" || netConnectMode == '2')//不支持或者当前是WIFI禁用4G
	{
		$("input[name='netConnectMode'][value='1']").attr("disabled","disabled");
	}
	if(isSupportWifi=="false" || netConnectMode == '1')//不支持或者当前是4G禁用WIFI
	{
		$("input[name='netConnectMode'][value='2']").attr("disabled","disabled");
	}
	if(netConnectMode != '2')//WIFI
	{
		$("tr.trWifi input").each(function(){$(this).rules("remove");});
	}
	else
	{
		$("tr.trWifi").show();
		$("tr.trWifi input").each(function(){$(this).rules("add", {required: true,validInputStr: true});});
	}
	$("input[name='netConnectMode'][value='"+netConnectMode+"']").attr("checked","checked");
	$("input[name='netConnectMode']").change(function(){
		$("tr.trWifi").hide();
		$("tr.trWifi input").each(function(){$(this).rules("remove");});
		if($(this).val()=="2")
		{
			$("tr.trWifi").show();
			$("tr.trWifi input").each(function(){$(this).rules("add", {required: true,validInputStr: true});});
		}
	});
	
	$("#${formId} #seachWifiBtn").click(function(){
        DhxCommon.createWindow("/accDevice.do?searchWifiList&devId=${(tempAccDevice.id)!}^0^0^304^410^<@i18n 'acc_dev_searchWifi'/>");
	});
});
function switchNetBeforeSubmit(submitFunc)
{
	openMessage(msgType.loading,"<@i18n'common_op_processing'/>");
	var netConnectMode = $("input[name='netConnectMode']:checked").val();
	$.ajax({
		url:"/accDevice.do?switchNetWorkTest",
		type:"post",
		dataType:"json",
		data:{
			devId:"${(tempAccDevice.id)!}",
			currentMode:"${netConnectMode!}",
			netConnectMode:netConnectMode,
			wirelessSSID:$("#${formId} #wirelessSSID").val(),
			wirelessKey:$("#${formId} #wirelessKey").val()
		},
		success: function (data)
        {
        	if(data[sysCfg.ret] == sysCfg.success)
			{
        		getCmdResultById(data[sysCfg.data],submitFunc);
			}
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) 
        {
        	closeMessage();
        	messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
        }
	});
}

function getCmdResultById(cmdId,submitFunc)
{
	$.ajax({
		url:"/accDevice.do?getCmdResultById",
		type:"post",
		dataType:"json",
		data:{
			devId:"${(tempAccDevice.id)!}",
			cmdId:cmdId,
			type:"testNetConnect"
		},
		success: function (data)
        {
        	if(data[sysCfg.ret] == sysCfg.success)
			{
        		messageBox({messageType:"confirm", text: "<@i18n 'acc_dev_testNetConnectSuccess'/>", callback:function(result){
    				if(result)
    			    {
    					submitFunc();
    				}
    			    else
    			    {
    			    	ret = false;
    			    }
    			    return true; //close
    			}});
        		closeMessage();
			}
        	else if(data[sysCfg.ret] == sysCfg.error)
        	{
        		messageBox({messageType:"alert", text: "<@i18n 'acc_dev_testNetConnectFailed'/>"});
        		closeMessage();
        	}
        	else
        	{
        		messageBox({messageType:"alert", text: "<@i18n 'common_commStatus_cmdTimeOut'/>"});
       			closeMessage();
        	}
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) 
        {
        	messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
        }
	});
}

</script>
</#macro>
