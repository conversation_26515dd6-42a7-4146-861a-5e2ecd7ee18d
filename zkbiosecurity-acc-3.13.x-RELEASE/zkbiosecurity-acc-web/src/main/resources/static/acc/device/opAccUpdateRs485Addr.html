<#include "/public/template/editTemplate.html">
<#macro editContent>
<script type="text/javascript">
$(function() {
	jQuery.validator.addMethod("devComAddr", function(value, element) {
		var pattenNum = /^(\d{1,2}|[1]\d{2}|2[0-4]\d|255)$/;
		return this.optional(element) || (pattenNum.test(value));
	}, "<@i18n 'acc_dev_rs485AddrTip'/>");
	$("#${formId}").validate( {
		debug : true,
		rules : {
			"comAddr" : {
				required : true,
				devComAddr : true,
				digits: true,
				range: [1,63]
			}
		},
		submitHandler : function(form) {
			$.ajaxSetup( {
				async : true,
				dataType : 'json'
			});
			$('#${formId}').ajaxSubmit( {
				success : function(data) {
					if (!data) {
						openMessage(msgType.error);
			} 
			else 
			{
				
				openMessage(msgType.success);
                DhxCommon.closeWindow();
                // ZKUI.Grid.reloadGrid(gridName);
			}
		},
		error : function(XMLHttpRequest, textStatus, errorThrown) 
		{
			messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
		}
			});
		}
	});
});
</script>
<form action="/accDevice.do?updateRs485Addr" method="post"
	onkeydown="if(event.keyCode==13){return false;}" id="${formId}"
	enctype="multipart/form-data">
	<input type="hidden" id="id_model_pk" name="devId" value="${(accDev.id)!}" />
	<input type="hidden" id="alias" name="alias" value="${(accDev.alias)!}"/>
	<table class="tableStyle" cellpadding="3px">
		<tr>
			<th>
				<label>
					<@i18n 'common_dev_rs485Address'/><span class="required">*</span>
				</label>
			</th>
			<td id="tag_id_newip" class="ip_input"></td>
			<td>
				<input type="text" id="comAddr" name="comAddr" maxlength="2"
					value="${(accDev.comAddress)!}" />(<@i18n 'common_range'/>1-63)
			</td>
		</tr>
	</table>
</form>
<div id="id_info" style="display: none;">
	<ul class="errorlist">
		<li style="width: 100%;"></li>
	</ul>
</div>
</#macro>