<#assign gridName="accDeviceGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
				<td valign="middle">
					<@ZKUI.Input name="alias" maxlength="30" title="common_dev_name" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="sn" maxlength="30" title="common_dev_sn" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="ipAddress" maxlength="30" title="common_ipAddress" type="text"/>
				</td>
			</tr>
    	</@ZKUI.SearchTop>
 		<@ZKUI.SearchBelow>
 			<tr>
				<td valign="middle">
					<@ZKUI.Input name="deviceName"  maxlength="30" title="common_dev_deviceModel" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.ComboTree url="authArea.do?tree" title="base_area_name" name="areaIdIn" readonly="readonly"/>
				</td>
				<td valign="middle">
					<@ZKUI.Combo title="common_status" readonly="readonly" name="connectState">
					<option value="1"><@i18n 'common_online'/></option>
					<option value="0"><@i18n 'common_offline'/></option>
					<option value="2"><@i18n 'common_disable'/></option>
				</@ZKUI.Combo>
				</td>
			</tr>
 		</@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="reloadDevGrid" permission="acc:device:refresh"/>
		<@ZKUI.ToolItem id="accDevice.do?edit" text="common_op_new" width="450" height="600" img="comm_add.png" action="commonAdd" permission="acc:device:add" isShow="JAVA#accDeviceAddShow.isShow"/>
		<@ZKUI.ToolItem id="accDevice.do?del&alias=(alias)" text="common_op_del" img="comm_del.png" action="commonDel" permission="acc:device:del"/>
		<@ZKUI.ToolItem id="accDevice.do?export" type="export" permission="acc:device:export"/>
		<@ZKUI.ToolItem id="accDevice.do?getSearchDevInfo&type=searchDev" text="common_dev_searchDev" title="common_dev_searchDev" img="comm_search.png" width="1100" height="585" action="commonOpen" permission="acc:device:searchDev"/>
		<@ZKUI.ToolItem type="more" text="common_dev_devControl" img="acc_dev_control.png">
			<@ZKUI.ToolItem id="accDevice.do?clearAdministrator" text="acc_dev_clearAdmin" title="acc_dev_clearAdmin" img="acc_dev_clearAdministrator.png" action="clearAdministratorOperate" permission="acc:device:clearAdministrator"/>
			<@ZKUI.ToolItem id="accDevice.do?clearCmdCache&alias=(alias)" img="acc_dev_clearAdministrator.png" text="common_devMonitor_clearCmdCache" permission="acc:device:clearCmdCache" action="commonOperate" selectNumber="50"/>
			<@ZKUI.ToolItem id="accDevice.do?getDevIdsByUpgradeFirmware&type=upgradeFirmware" text="common_dev_upgradeFirmware" title="common_dev_upgradeFirmware" img="comm_upgradeFirmware.png" action="commonOpenOperate" selectNumber="50" width="600" height="440" permission="acc:device:upgradeFirmware"/>
			<@ZKUI.ToolItem id="accDevice.do?rebootDevice&alias=(alias)" text="common_dev_reboot" title="common_dev_reboot" img="comm_rebootDevice.png" action="commonOperate" selectNumber="50" permission="acc:device:rebootDevice"/>
			<@ZKUI.ToolItem id="accDevice.do?getDevSyncTimeInfo&type=syncTime&alias=(alias)" text="common_dev_syncTime" title="common_dev_syncTime" img="comm_syncTime.png" action="commonOpenOperate" selectNumber="50" width="600" height="410" permission="acc:device:syncTime"/>
			<@ZKUI.ToolItem id="accDevice.do?setDevEnable&alias=(alias)" text="common_enable" img="comm_enable.png" action="commonOperate" selectNumber="50" permission="acc:device:enable"/>
			<@ZKUI.ToolItem id="accDevice.do?setDevDisable&alias=(alias)" text="common_disable" img="comm_disable.png" action="commonOperate" selectNumber="50" permission="acc:device:disable"/>
			<@ZKUI.ToolItem id="accDevice.do?getDevIdsBySyncData&type=syncAllData" text="common_dev_syncAllDataToDev" width="800" height="470" title="common_dev_syncAllDataToDev" img="comm_syncAllData.png" action="commonOpenOperate" selectNumber="50" permission="acc:device:syncAllData"/>
		</@ZKUI.ToolItem>
		<@ZKUI.ToolItem type="more" text="common_adf_setting" img="acc_dev_setting.png">
			<@ZKUI.ToolItem id="accDevice.do?getDevIdsByIssueBgValid" text="acc_dev_issueVerifyParam" title="acc_dev_issueVerifyParam" img="acc_issueVerifyParam.png" action="commonOpenOperate" selectNumber="50" width="600" height="520" permission="acc:device:issueBGVerifyParam"/>
			<@ZKUI.ToolItem id="accDevice.do?getDevIdsBySetTimeZone" text="acc_dev_setTimeZone" title="acc_dev_setTimeZone" img="acc_setdevicetime.png" action="commonOpenOperate" selectNumber="50" width="600" height="430" permission="acc:device:setTimeZone"/>
            <@ZKUI.ToolItem id="accDevice.do?getDevIdsBySetRegistrationDevice" text="acc_dev_setRegistrationDevice" title="acc_dev_setRegistrationDevice" img="acc_settingdevice.png" mode="single" action="accOpenOperate" width="420" height="205" permission="acc:device:setRegistrationDevice"/>
			<#-- <@ZKUI.ToolItem id="accDevice.do?getDevIdsBySetDstimeSortByDstime" text="acc_dev_setDstime" title="acc_dev_setDstime" img="acc_daylight_time.png" action="commonOpenOperate" selectNumber="50" width="600" height="410" permission="acc:device:setDSTime"/>-->
			<@ZKUI.ToolItem id="accDevice.do?getDevMThresholdInfo&type=updateMThreshold" text="common_dev_modifyFPThreshold" title="common_dev_modifyFPThreshold" img="comm_changeDevMThreshold.png" width="400" height="250" mode="single" action="accOpenOperate" permission="acc:device:updateMThreshold"/>
			<@ZKUI.ToolItem id="accDevice.do?getDevExtendInfo" text="acc_dev_setExtendParam" title="acc_dev_setExtendParam" img="acc_dev_setExtendParam.png" width="500" height="500" mode="single" action="accOpenOperate" permission="acc:device:updateDevExtendParam"/>
			<!--ntp服务器-->
			<@ZKUI.ToolItem id="accDevice.do?getNtpServerInfo" text="acc_device_setNTPService" title="acc_device_setNTPService" img="acc_dev_setIOState.png" width="550" height="560" action="commonOpenOperate" selectNumber="50" permission="acc:device:setDevNTP"/>
			<@ZKUI.ToolItem id="skip.do?page=acc_device_replaceAccDevInfo&devId=(id)&alias=(alias)"  text="acc_dev_replace" title="acc_dev_replace" img="acc_dev_replace.png" width="400" height="220" mode="single" action="accDeviceReplaceOperate" permission="acc:device:replace"/>
			<@ZKUI.ToolItem id="accDevice.do?setResourceFile&type=setResourceFile" text="acc_dev_upResourceFile" title="acc_dev_upResourceFile" img="acc_dev_upResourceFile.png" width="450" height="300" action="accOpenOperate" mode="single" permission="acc:device:setResourceFile"/>
			<@ZKUI.ToolItem id="accDevice.do?getFaceVerifyServerInfo" text="acc_dev_setFaceServerInfo" title="acc_dev_setFaceServerInfo" img="acc_dev_setExtendParam.png" width="600" height="600" action="commonOpenOperate" selectNumber="50" permission="acc:device:setFaceServerInfo"/>
		</@ZKUI.ToolItem>
		<@ZKUI.ToolItem type="more" text="common_dev_viewOrGetInfo" img="acc_dev_viewOrGetInfo.png">
			<@ZKUI.ToolItem id="accDevice.do?getOptFromDev&alias=(alias)" text="common_dev_getDevOpt" title="common_dev_getDevOpt" img="comm_getOptFromDev.png" action="commonOperate" selectNumber="50" permission="acc:device:getOptFromDev"/>
			<@ZKUI.ToolItem id="accDevice.do?getUploadPersonInfo" text="common_dev_getPersonInfo" title="common_dev_getPersonInfo" img="comm_uploadUserInfo.png" mode="single" action="accOpenOperate" width="600" height="470" permission="acc:device:uploadPersonInfo"/>
			<@ZKUI.ToolItem id="accDevice.do?getUploadTransactionInfo&type=uploadTransaction" text="common_dev_getTrans" title="common_dev_getTrans" img="comm_uploadTransaction.png" mode="single" action="accOpenOperate" width="600" height="480" permission="acc:device:uploadTransaction"/>
			<@ZKUI.ToolItem id="accDevice.do?queryDevRule&alias=(alias)" text="acc_dev_accessRules" title="acc_dev_accessRules" img="acc_viewing_rules.png" width="600" height="580" mode="single" action="accOpenOperate" permission="acc:device:queryDevRule"/>
			<@ZKUI.ToolItem id="accDevice.do?queryDevUsage&alias=(alias)" text="acc_dev_queryDevVolume" title="acc_dev_queryDevVolume" img="acc_dev_queryDevVolume.png" width="900" height="450" action="commonOpenOperate" selectNumber="50" permission="acc:device:queryDevUsage"/>
		</@ZKUI.ToolItem>
		<@ZKUI.ToolItem type="more" text="common_dev_communication" img="acc_dev_communication.png">
			<@ZKUI.ToolItem id="accDevice.do?getDevIPAddressInfo&type=updateIpAddr" text="common_dev_modifyIPAddress" title="common_dev_modifyIPAddress" img="comm_changeDevIp.png" action="commonOpenOperate" selectNumber="1" width="420" height="370" permission="acc:device:updateIpAddr"/>
			<@ZKUI.ToolItem id="accDevice.do?getDevCommPwdInfo&type=updateCommPwd" text="common_dev_modifyCommPwd" title="common_dev_modifyCommPwd" img="comm_changeDevPwd.png" action="accOpenOperate" width="430" height="230" mode="single" permission="acc:device:updateCommPwd"/>
			<!--	修改RS485地址图标名称 comm_changeDevIp --acc_changeRS485Addr	图标在门禁本模块	-->
			<#-- <@ZKUI.ToolItem id="accDevice.do?getRs485Addr" text="acc_dev_modifyRS485Addr" title="acc_dev_modifyRS485Addr" img="acc_changeRS485Addr.png" action="accOpenOperate" width="420" height="190" mode="single" permission="acc:device:updateRs485Addr"/>-->
			<@ZKUI.ToolItem id="accDevice.do?getDevNetConnectModeInfo&type=updateNetConnectMode" text="acc_dev_updateNetConnectMode" title="acc_dev_updateNetConnectMode" img="acc_swith_net.png" width="450" height="250" mode="single" action="accOpenOperate" permission="acc:device:updateNetConnectMode"/>
		</@ZKUI.ToolItem>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem" query="accDevice.do?list" hideTreeImg="true" showColumns="!commType,netConnectMode,rs485Param"/>
</@ZKUI.GridBox>
<script type="text/javascript">

    function accOpenOperate(id, bar, opt) {
        var maxSelectItem = 50;
        if(bar) {
            var gridName = bar.gridName;
            var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
            if(ids == "") {
                messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});
            }
            else if (opt.mode == "single" && ids.split(",").length > 1) {
				messageBox({messageType:"alert",text:"<@i18n 'common_prompt_onlySelectOneObject'/>"});
            }
            else if (ids.split(",").length > maxSelectItem) {
                messageBox({messageType:"alert",text: "<@i18n 'common_prompt_noPass'/>".format(maxSelectItem)});
			}
            else {
                var opts = $.extend({path:fillParamsFromGrid(gridName,ids,id) + "&ids=" + ids, gridName:gridName}, JSON.parse(JSON.stringify(opt)));
                DhxCommon.createWindow(opts);
            }
        }
    }

    function convertConnectState(v) {
        if (v == "1") {
            v = "<span class='zk-msg-normal'>" + I18n.getValue("common_online") + "</span>";
		}
        if (v == "0") {
            v = "<span class='zk-msg-error'>" + I18n.getValue("common_offline") + "</span>";
		}
        if (v == "2") {
            v = "<span class='zk-msg-warn'>" + I18n.getValue("common_disable") + "</span>";
		}
		return v;
	}

	function accAddChildDevice(gridName, domObj, rid) {
        var opts = {//弹窗配置对象
            path: "accDevice.do?addChildDeviceInfo&ids=" + rid + "&type=queryAuthorizeList",//设置弹窗路径
            width: 580,//设置弹窗宽度
            height: 450,//设置弹窗高度
            title: "<@i18n 'acc_dev_addChildDevice'/>",//设置弹窗标题
            gridName: "gridbox"//设置grid
        };
        DhxCommon.createWindow(opts);
    }

    function accLookUpChildDevice(gridName, domObj, rid) {
        var opts = {//弹窗配置对象
            path: "accDevice.do?lookUpChildDeviceInfo&id=" + rid,//设置弹窗路径
            width: 800,//设置弹窗宽度
            height: 450,//设置弹窗高度e
            title: "<@i18n 'acc_dev_lookUpChildDevice'/>",//设置弹窗标题
            gridName: "gridbox"//设置grid
        };
        DhxCommon.createWindow(opts);
    }

	function accModParentDevice(gridName, domObj, rid) {
        var opts = {//弹窗配置对象
            path: "accDevice.do?modParentDeviceInfo&ids=" + rid + "&type=modParentDevice",//设置弹窗路径
            width: 450,//设置弹窗宽度
            height: 250,//设置弹窗高度
            title: "<@i18n 'acc_dev_modParentDevice'/>",//设置弹窗标题
            gridName: "gridbox"//设置grid
        };
        DhxCommon.createWindow(opts);
    }

    function accConfigParentDevice(gridName, domObj, rid) {
        var opts = {//弹窗配置对象
            path: "accDevice.do?configParentDeviceInfo&ids=" + rid + "&type=configParentDevice",//设置弹窗路径
            width: 450,//设置弹窗宽度
            height: 250,//设置弹窗高度
            title: "<@i18n 'acc_dev_configParentDevice'/>",//设置弹窗标题
            gridName: "gridbox"//设置grid
        };
        DhxCommon.createWindow(opts);
    }

    var _nextTime = 0;//下一次点击时间
    function reloadDevGrid()
    {
        var t = new Date().getTime();
        if(t < _nextTime)
        {
            return;
        }
        else
        {
            _nextTime = t + 2000;//间隔2秒之后点击才有效果
            ZKUI.Grid.get("${gridName}").reload();
        }
    }

	//选择清除设备管理员时做校验
	function clearAdministratorOperate(id, bar, opt)
	{
		var maxSelectItem = 50;
		var gridName = bar.gridName;
		var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
		if(ids == "") {
			messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});
		}
		else if (ids.split(",").length > maxSelectItem) {
			messageBox({messageType:"alert",text: "<@i18n 'common_prompt_noPass'/>".format(maxSelectItem)});
		}
		else
		{
			$.ajax({
				url:"accDevice.do?isSupportClearAdministrator",
				type: "POST",
				dataType: "json",
				data: {"devIds" : ids},
				async: false,
				success:function(result)
				{
					if(result[sysCfg.ret] == sysCfg.error)
					{
						openMessage("warning",result[sysCfg.msg]);
					}
					else
					{
						clearAdministrator(ids,"<@i18n 'acc_dev_sureToClear'/>");
					}
				},
				error:function(XMLHttpRequest, textStatus, errorThrown)
				{
					messageBox({messageType: "alert", title:"<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>" + "-628"});
				}
			});
		}
	}
	//清除设备管理员操作
	function clearAdministrator(id, text)
	{
		messageBox({
			messageType : "confirm",
			text : text,
			callback : function(result)
			{
				if (result)
				{
					openMessage(msgType.loading);
					setTimeout(function () {
						$.ajax({
							url:"accDevice.do?clearAdministrator",
							type: "POST",
							data: {"devIds" : id},
							async: false,
							success:function(retData)
							{
								if(retData[sysCfg.ret] == sysCfg.success)
								{
									closeMessage();
									openMessage(msgType.success);
									ZKUI.Grid.reloadGrid("${gridName}");
								}
								else if(retData[sysCfg.ret] == sysCfg.error)
								{
									closeMessage();
									openMessage("warning",retData[sysCfg.msg]);
								}
								else
								{
									closeMessage();
									openMessage("warning", "<@i18n 'common_op_failed'/>");
								}
							},
							error:function(XMLHttpRequest, textStatus, errorThrown)
							{
								messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>" + "-628"});
							}
						});
					},500);
				}
			}
		})
	}

	//查看命令
	function acc_viewCommand(gridName, obj, rid, rowIndex, column, row) {
		accViewCommand(row.sn);
	}

	//查看命令
	function accViewCommand(sn) {
		//clearTimeout(timeoutFlag);
		openMenuItem("admsDevCmd.do", "System", "sn=" + sn);
	}

	function accDeviceReplaceOperate(id, bar, opt){
        var gridName = bar.gridName;
		var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
		if(ids == "") {
			messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});
		}
		else if (opt.mode == "single" && ids.split(",").length > 1) {
			messageBox({messageType:"alert",text:"<@i18n 'common_prompt_onlySelectOneObject'/>"});
		} else {
			$.ajax({
				url:"accDevice.do?isSupportReplaceDevice",
				type: "POST",
				dataType: "json",
				data: {"devIds" : ids},
				async: false,
				success:function(result)
				{
					if(result[sysCfg.ret] == sysCfg.error)
					{
						openMessage("warning",result[sysCfg.msg]);
					}
					else
					{
						var opts = $.extend({path:fillParamsFromGrid(gridName,ids,id) + "&ids=" + ids, gridName:gridName}, JSON.parse(JSON.stringify(opt)));
						DhxCommon.createWindow(opts);
					}
				},
				error:function(XMLHttpRequest, textStatus, errorThrown)
				{
					messageBox({messageType: "alert", title:"<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>" + "-628"});
				}
			});
		}
	}

	function convertTZ(v) {
        if (v != "") {
        	var sign = v.charAt(0);
        	var number = parseInt(v.substring(1));
        	var tz = Math.trunc(number/100);
        	var deliveryVal = number%100;
            v = "UTC" + sign + tz;
            if(deliveryVal != 0){
            	v += ":" + deliveryVal;
            }
		}
		return v;
	}
</script>