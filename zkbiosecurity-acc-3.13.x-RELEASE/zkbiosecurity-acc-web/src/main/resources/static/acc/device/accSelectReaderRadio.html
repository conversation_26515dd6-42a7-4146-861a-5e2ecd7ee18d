<@ZKUI.SelectContent setMode="true,true,false,true" showColumns="id,name,doorName,deviceAlias" onSure="accDeviceAddSelectReader" type="radio" textField="name" value="${value!}" vo="com.zkteco.zkbiosecurity.acc.vo.AccSelectReaderRadioItem" query="accReader.do?getWGReaderFilterList">
    <@ZKUI.Searchbar>
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="name"  maxlength="30" title="acc_readerDefine_readerName" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="doorName"  maxlength="30" title="acc_door_name" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="deviceAlias"  maxlength="30" title="common_ownedDev" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
</@ZKUI.SelectContent>
<script type="text/javascript">
    function accDeviceAddSelectReader(value, text, event) {
        $("#wigendReaderId").val(value);
        $("#readerName").val(text);
        // return false;
    }
</script>