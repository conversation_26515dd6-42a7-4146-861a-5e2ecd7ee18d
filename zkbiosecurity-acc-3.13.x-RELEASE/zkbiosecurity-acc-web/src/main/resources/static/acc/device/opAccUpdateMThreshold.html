<#include "/public/template/editTemplate.html">
<#macro editContent>
	<script type="text/javascript">
		$().ready(function() {
			$("#${formId}").validate( {
				debug : true,
				rules :
				{
					"mThreshold" :
					{
						required : true,
						range : [35,70]
					},
					"faceMThr" :
					{
						required : true,
						range : [0,100]
					},
					"pvMThreshold" :
					{
						required : true,
						range : [0,100]
					}
				},
				submitHandler : function()
				{
					execAccConfirm($("#id_model_pk").val(), function(){<@submitHandler/>});
				}
			});
		});
	</script>

	<form action="accDevice.do?updateMThreshold" method="post" onkeydown="if(event.keyCode==13){return false;}" id="${formId}" enctype="multipart/form-data">
		<input type="hidden" id="id_model_pk" name="devId" value="${(tempAccDevice.id)!}"/>
		<input type="hidden" id="devAlias" name="alias" value="${(tempAccDevice.alias)!}"/>
		<table  class="tableStyle"  cellpadding="0" cellspacing="0" >
			<#if (mThreshold)?exists>
			<tr>
				<th>
					<label><@i18n 'common_dev_fpThreshold'/></label><span class="required">*</span>
				</th>
				<td>
					<input name="mThreshold" value="${(mThreshold)!}" type="text" size="20" maxlength="3" style="width: 30px;"/><lable class="gray">(35-70)</lable>
				</td>
			</tr>
			</#if>
			<#if (faceMThr)?exists>
			<tr>
				<th>
					<label><@i18n 'common_dev_faceThreshold'/></label><span class="required">*</span>
				</th>
				<td>
					<input name="faceMThr" value="${(faceMThr)!}" type="text" size="20" maxlength="3" style="width: 30px;"/><lable class="gray">(0-100)</lable>
				</td>
			</tr>
			</#if>
			<#if (pvMThreshold)?exists>
			<tr>
				<th>
					<label><@i18n 'common_dev_palmThreshold'/></label><span class="required">*</span>
				</th>
				<td>
					<input name="pvMThreshold" value="${(pvMThreshold)!}" type="text" size="20" maxlength="3" style="width: 30px;"/><lable class="gray">(0-100)</lable>
				</td>
			</tr>
			</#if>
		</table>
	</form>
</#macro>