<#assign formId="accDevNtpForm${uuid}">
<#include "/public/template/editTemplate.html">
<#macro editMain></#macro>
<@ZKUI.Process id="accDevFaceVerifyServer${uuid}" type="single" confirmText="acc_dev_start" onSure="accDevFaceVerifyServer" onFinish="finishProgress">
<!-- content内容标签必须指定name属性，用来指定嵌套位置 -->
<@ZKUI.Content name="op">
<div style="padding-bottom: 2px;"><label><@i18n 'common_dev_selectedDev'/></label></div>
<div class="access-infoDiv zk-content-bg-color zk-border-color" style="border: solid 1px green;background-color:white;width:99%;height: 100px; overflow:auto; margin-bottom: 7px">
    <div name="selectDev" id="selectDev"></div>
</div>

<fieldset>
    <legend><@i18n 'acc_dev_setFaceServerInfo'/></legend>
    <form action="accDevice.do?setFaceVerifyServer" id="${formId}" method="post">
        <input type="hidden" name="ids" id="ids" value="${(ids)!}"/>
        <table class="tableStyle">
            <tr>
                <th><@i18n 'acc_dev_faceVerifyMode'/></th>
                <td>
                    <@ZKUI.Combo id="faceVerifyMode${uuid}" width="148" empty="false" hideLabel="true" name="faceVerifyMode" value="${faceVerifyMode!0}" onChange="changeFaceVerifyMode">
                    <option value="0"><@i18n 'acc_dev_faceVerifyMode1'/></option>
                    <option value="1"><@i18n 'acc_dev_faceVerifyMode2'/></option>
                    <option value="2"><@i18n 'acc_dev_faceVerifyMode3'/></option>
                    </@ZKUI.Combo>
                </td>
            </tr>
            <tr id="faceBgServerTypeTr">
                <th><@i18n 'acc_dev_faceBgServerType'/></th>
                <td>
                    <@ZKUI.Combo id="faceBgServerType${uuid}" width="148" empty="false" hideLabel="true" name="faceBgServerType" value="${faceBgServerType!0}" onChange="changeFaceBgServerType">
                    <option value="0"><@i18n 'acc_dev_faceBgServerType1'/></option>
                    <option value="1"><@i18n 'acc_dev_faceBgServerType2'/></option>
                    </@ZKUI.Combo>
                </td>
            </tr>
            <tr id="isAccessLogicTr">
                <th><@i18n 'acc_dev_isAccessLogic'/></th>
                <td>
                    <@ZKUI.Combo id="isAccessLogic${uuid}" width="148" empty="false" hideLabel="true" name="isAccessLogic" value="${isAccessLogic!0}">
                        <option value="0"><@i18n 'common_disable'/></option>
                        <option value="1"><@i18n 'common_enable'/></option>
                    </@ZKUI.Combo>
                </td>
            </tr>
            <tr id="faceThresholdTr">
                <th><@i18n 'common_dev_faceThreshold'/></th>
                <td>
                    <input type="text" id="faceThreshold" name="faceThreshold" value="${faceThreshold!40}"/>
                </td>
            </tr>
            <tr id="faceBgServerURLTr">
                <th><@i18n 'pers_param_faceServerAddr'/></th>
                <td>
                    <input type="text" id="faceBgServerURL" name="faceBgServerURL" value="${faceBgServerURL!}"/>
                </td>
            </tr>
            <tr id="faceBgServerSecretTr">
                <th><@i18n 'pers_param_faceServerSecret'/></th>
                <td>
                    <input type="text" id="faceBgServerSecret" name="faceBgServerSecret" value="${faceBgServerSecret!}"/>
                </td>
            </tr>
        </table>
    </form>
</fieldset>
<script>
    $(function() {
        var devNameStr = "${(devNameStr)!}";
        var devAlias = "";
        var alias = new Array();
        var disabledDevNameStr = "${(disabledDevName)!}";
        var noSupportDevNameStr = "${(noSupportDevName)!}";
        var warnInfoDevNameStr = "${(warnInfoDevName)!}";

        if(devNameStr != "" || devNameStr.length > 0)
        {
            $("<div style='padding-${leftRTL!'left'}: 10px; margin-bottom: 4px;'>" + devNameStr + "</div>").appendTo("#selectDev");
        }
        if(disabledDevNameStr != "" && disabledDevNameStr.length > 0)
        {
            $("<div style='padding-left: 10px; margin-top: 10px; color: red;'><b><@i18n 'acc_dev_disabled'/></b><br/>" + disabledDevNameStr + "</div>").appendTo("#selectDev");
        }
        if(noSupportDevNameStr != "" && noSupportDevNameStr.length > 0)
        {
            $("<div style='padding-left: 10px; margin-top: 10px; color: red;'><b><@i18n 'acc_dev_noSupport'/></b><br/>" + noSupportDevNameStr + "</div>").appendTo("#selectDev");
        }
        if(warnInfoDevNameStr != "" && warnInfoDevNameStr.length > 0)
        {
            $("<div style='padding-left: 10px; margin-top: 10px; color: #E57A14;'><b><@i18n 'acc_dev_setDstimeWarnTip'/></b><br/>" + warnInfoDevNameStr + "</div>").appendTo("#selectDev");
        }

        ZKUI.Combo.get('faceVerifyMode${uuid}').combo.setComboValue("${faceVerifyMode!0}");
        changeFaceVerifyMode("${faceVerifyMode!0}");
        ZKUI.Combo.get('faceBgServerType${uuid}').combo.setComboValue("${faceBgServerType!0}");
        changeFaceBgServerType("${faceBgServerType!0}");
    });

    function accDevFaceVerifyServer()
    {

        var submitFun = function(){
            $("#accDevFaceVerifyServer${uuid}ConfirmButton").attr("disabled", true);
            var faceBgServerType = ZKUI.Combo.get('faceBgServerType${uuid}').combo.getSelectedValue();
            var isAccessLogic = ZKUI.Combo.get('isAccessLogic${uuid}').combo.getSelectedValue();
            var faceVerifyMode = ZKUI.Combo.get('faceVerifyMode${uuid}').combo.getSelectedValue();
            var faceThreshold = $("input[name='faceThreshold']").val();
            var faceBgServerURL = $("input[name='faceBgServerURL']").val();
            var faceBgServerSecret = $("input[name='faceBgServerSecret']").val();
            var ids = $("#ids").val();
            ZKUI.Process.get("accDevFaceVerifyServer${uuid}").options.dealPath="accDevice.do?setFaceVerifyServer&ids=" + ids + "&faceBgServerType=" + faceBgServerType + "&isAccessLogic=" + isAccessLogic
            + "&faceVerifyMode=" + faceVerifyMode + "&faceThreshold=" + faceThreshold + "&faceBgServerURL=" + faceBgServerURL + "&faceBgServerSecret=" + faceBgServerSecret;
            ZKUI.Process.get("accDevFaceVerifyServer${uuid}").beginProgress();
        };
        execAccConfirm($("#ids").val(), submitFun);
    }

    function changeFaceVerifyMode(val){
        if (val == "0") {
            $("#faceBgServerTypeTr").hide();
            $("#isAccessLogicTr").hide();
            $("#faceThresholdTr").hide();
        }
        else {
            $("#faceBgServerTypeTr").show();
            $("#isAccessLogicTr").show();
            $("#faceThresholdTr").show();
        }
    }

    function changeFaceBgServerType(val){
        if (val == "0") {
            $("#faceBgServerURLTr").hide();
            $("#faceBgServerSecretTr").hide();
        }
        else {
            $("#faceBgServerURLTr").show();
            $("#faceBgServerSecretTr").show();
        }
    }

</script>
</@ZKUI.Content>
</@ZKUI.Process>