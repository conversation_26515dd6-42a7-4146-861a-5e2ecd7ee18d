<#assign formId="accDevNtpForm${uuid}">
<#include "/public/template/editTemplate.html">
<#macro editMain></#macro>
<@ZKUI.Process id="accDevNtpServer${uuid}" type="single" confirmText="acc_dev_start" onSure="accDevNtpServer" onFinish="finishProgress">
<!-- content内容标签必须指定name属性，用来指定嵌套位置 -->
<@ZKUI.Content name="op">
<div style="padding-bottom: 2px;"><label><@i18n 'common_dev_selectedDev'/></label></div>
<div class="access-infoDiv zk-content-bg-color zk-border-color" style="border: solid 1px green;background-color:white;width:99%;height: 100px; overflow:auto; margin-bottom: 7px">
    <div name="selectDev" id="selectDev"></div>
</div>

<fieldset>
    <legend><@i18n 'acc_device_setNTPService'/></legend>
    <form action="accDevice.do?setNtpServer" id="${formId}" method="post">
        <input type="hidden" name="ids" id="ids" value="${(ids)!}"/>
        <table class="tableStyle">
            <tr>
                <th><@i18n 'common_status'/></th>
                <td>
                    <@ZKUI.Combo id="status${uuid}" width="148" empty="false" hideLabel="true" name="status" onChange="ntpStatusChange">
                    <option value="1"><@i18n 'common_enable'/></option>
                    <option value="0"><@i18n 'common_disable'/></option>
                    </@ZKUI.Combo>
                </td>
            </tr>
            <tr>
                <th><@i18n 'common_dev_serverAddress'/></th>
                <td>
                    <textarea id="addressArea" form="${formId}" name="serverAddress" maxlength="150"></textarea>
                    <div><span class="warningImage" style="margin-top: 4px;"></span><span class="warningColor"><@i18n 'acc_device_setNTPServiceTip'/></span></div>
                </td>
            </tr>
        </table>
    </form>
</fieldset>
<style>
    #addressArea{
        height: 70px;
        width: 365px;
    }
</style>
<script>
    $(function () {
        var devNameStr = "${(devNameStr)!}";
        var devAlias = "";
        var alias = new Array();
        var disabledDevNameStr = "${(disabledDevName)!}";
        var noSupportDevNameStr = "${(noSupportDevName)!}";
        var warnInfoDevNameStr = "${(warnInfoDevName)!}";

        if(devNameStr == "" || devNameStr.length == 0)
        {
            $("#accDevNtpServer${uuid}ConfirmButton").attr("disabled", true);
            ZKUI.Combo.get("status${uuid}").combo.disable(true);
            $("#addressArea").attr("disabled", "disabled");
        }
        else
        {
            var devArr = devNameStr.split(";");
            for(var i=0; i<devArr.length; i++)
            {
                var currDev = devArr[i];
                var devsuffix = currDev.split(":")[1];
                var devSuffixArr = devsuffix.split(",");
                var ids ="";
                var combDevName ="" ;
                devAlias ="" ;
                for(var j=1; j<=devSuffixArr.length; j++)
                {
                    var temp = devSuffixArr[j-1].split("&");
                    ids += temp[1] + ",";
                    combDevName += temp[0] + ",";
                    devAlias += temp[0] + ",";
                    if(j % 5 == 0)
                    {
                        combDevName += "</br>";
                    }
                }
                ids = ids.substring(0,ids.length-1);
                combDevName = combDevName.substring(0, combDevName.length-1);
                devAlias = devAlias.substring(0, devAlias.length-1);
                var devTypeName = currDev.split(":")[0].split("-");
                var devDstName = devTypeName[1];
                var devDstVal = devTypeName[1];
                if(devDstName == undefined)
                {
                    devDstName = "devDstId";
                }
                if(devDstVal == undefined)
                {
                    devDstVal = "";
                }
                $("<div style='padding-left: 10px;' id='" + devDstName + "' value='" + devDstVal + "'>" +
                    "<span id='devRadioId"+ i +"'></span>" +
                    "<span style='font-weight: bold;' name='devType'>"+ devTypeName[0] + "</span>" + " : " + combDevName + "</div>").appendTo("#selectDev");
                alias[ids] = devAlias;
                loadUIToDiv("input", "#devRadioId" +i, {
                        useInputReq:true,
                        hideLabel:true,
                        type:"radio",
                        name:"devRadio",
                        dstattr:devTypeName[0],
                        value:ids,
                        defaultChecked:i==0
                });
            }
        }
        if(disabledDevNameStr != "" && disabledDevNameStr.length > 0)
        {
            $("<div style='padding-left: 10px; margin-top: 10px; color: red;'><b><@i18n 'acc_dev_disabled'/></b><br/>" + disabledDevNameStr + "</div>").appendTo("#selectDev");
        }
        if(noSupportDevNameStr != "" && noSupportDevNameStr.length > 0)
        {
            $("<div style='padding-left: 10px; margin-top: 10px; color: red;'><b><@i18n 'acc_dev_noSupport'/></b><br/>" + noSupportDevNameStr + "</div>").appendTo("#selectDev");
        }
        if(warnInfoDevNameStr != "" && warnInfoDevNameStr.length > 0)
        {
            $("<div style='padding-left: 10px; margin-top: 10px; color: #E57A14;'><b><@i18n 'acc_dev_setDstimeWarnTip'/></b><br/>" + warnInfoDevNameStr + "</div>").appendTo("#selectDev");
        }
    });

    function accDevNtpServer()
    {
        $("#${formId}").validate();
        $("textarea[name='serverAddress']").rules("add",{accNtpUrlValid: true, required: true})
        if(!$("#${formId}").valid()) {
            return false;
        }

        var submitFun = function(){
            $("#accDevNtpServer${uuid}ConfirmButton").attr("disabled", true);
            var devType = $("input[name='devRadio']:checked").val();
            var status =ZKUI.Combo.get('status${uuid}').combo.getSelectedValue();
            var serverAddress = $("textarea[name='serverAddress']").val();
            var ids = $("#ids").val();
            ZKUI.Process.get("accDevNtpServer${uuid}").options.dealPath="accDevice.do?setNtpServer&ids=" + ids + "&status=" + status + "&serverAddress=" + serverAddress;
            ZKUI.Process.get("accDevNtpServer${uuid}").beginProgress();
        };
        execAccConfirm($("#ids").val(), submitFun);
        //submitFun();
    }

    function ntpStatusChange(val){
        if(val == "0") {
            $("#addressArea").attr("disabled", "disabled");
            $("#addressArea").val("");
            $(".ts_box").remove();
        } else{
            $("#addressArea").removeAttr("disabled");
        }
    }

   jQuery.validator.addMethod("accNtpUrlValid", function(val, element)
    {
        var urlReg = /^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/;
        if (val == "")
        {
            return true;
        }
        else
        {
            var urlArray = val.replace(/;/g, ",").split(",");//统一将输入的分号替换成逗号，支持混合输入
            for (var i = 0; i < urlArray.length; i++)
            {
                if (urlArray[i] != "" && !(this.optional(element) || (urlReg.test(urlArray[i]))))
                {
                    return false;
                }
            }
            return true;
        }
    }, I18n.getValue("common_jqMsg_url"));

    window.setTimeout(function () {
        //初始化
        $('input:radio[name="devRadio"]:checked').each(function(){
            var id = $(this).attr("id");
            var dstattr = ZKUI.Input.get(id).options.dstattr;
            if(dstattr == "<@i18n 'acc_common_notSet'/>")   //修改下拉框中选项
            {
                ZKUI.Combo.get('status${uuid}').combo.setComboValue("0");
                $("#addressArea").attr("disabled", "disabled");
            }
            else if(dstattr == "<@i18n 'acc_common_hasBeanSet'/>")
            {
                ZKUI.Combo.get('status${uuid}').combo.setComboValue("1");
                $("#addressArea").removeAttr("disabled");
                if("${(serverAddress)}") {
                    $("#addressArea").val("${(serverAddress)}");
                }
            }
            $("#ids").val($(this).attr("value"));
        });

        //动态选择
        $('input:radio[name="devRadio"]').each(function(){
            $(this).click(function(){
                var id = $(this).attr("id");
                var dstattr = ZKUI.Input.get(id).options.dstattr;
                if(dstattr == "<@i18n 'acc_common_notSet'/>")   //修改下拉框中选项
                {
                    ZKUI.Combo.get('status${uuid}').combo.setComboValue("0");
                    $("#addressArea").attr("disabled", "disabled");
                }
                else if(dstattr == "<@i18n 'acc_common_hasBeanSet'/>")
                {
                    ZKUI.Combo.get('status${uuid}').combo.setComboValue("1");
                    $("#addressArea").removeAttr("disabled");
                    if("${(serverAddress)}") {
                        $("#addressArea").val("${(serverAddress)}");
                    }
                }
                $("#ids").val($(this).attr("value"));
            });
        });
    }, 100);

</script>
</@ZKUI.Content>
</@ZKUI.Process>