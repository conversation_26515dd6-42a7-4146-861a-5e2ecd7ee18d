<#assign formId="accUpgradeFirmwareform${uuid}">
<#include "/public/template/editTemplate.html">
<#macro editMain></#macro>
<@ZKUI.Process id="accUpgradeFirmware${uuid}" type="single" dealPath="" confirmText="common_dev_upgrade" onSure="accUpgradeFirmware">
<@ZKUI.Content name="op">

    <form method="post" id="${formId}" action="/accDevice.do?upgradeFirmware" enctype="multipart/form-data" >
        <div style="padding: 0px 10px;">
            <div style="padding-bottom: 2px;"><label><@i18n 'common_dev_selectedDev'/></label>(<span class="warningColor"><@i18n 'common_dev_onlyUpgradeSameType'/></span>)</div>
            <div class="access-infoDiv zk-content-bg-color zk-border-color" style="border: solid 1px green;background-color:white;width:99%;height: 120px;overflow:auto">
                <div name="selectDev" id="selectDev"></div>
            </div>
            <div style="display: flex;align-items: center">
                <@i18n 'common_dev_selectFile'/>
                <@ZKUI.Upload id="devFile${uuid}" name="devFile" />
            </div>
        </div>
    </form>

    <script type="text/javascript">
        $(function(){
            var devNameStr = "${(devicesName)!}";
            var devArr = devNameStr.split(";");
            var disabledDevNameStr = "${(disabledDevName)!}";
            var offlineDevNameStr = "${(offlineDevName)!}";
            if(devNameStr.length == 0)
            {
                $("#accUpgradeFirmware${uuid}ConfirmButton").attr("disabled", true);
                ZKUI.Upload.get("devFile${uuid}").disabled(true);
            }
            else
            {
                for(var i=0; i<devArr.length; i++)
                {
                    var currDev = devArr[i];
                    var devsuffix = currDev.split(":")[1];
                    var devSuffixArr = devsuffix.substring(0, devsuffix.length-1).split(",");
                    var ids = "";
                    var combDevName = "";
                    for(var j = 1; j <= devSuffixArr.length; j++)
                    {
                        var temp = devSuffixArr[j-1].split("&");
                        ids += temp[1] + ",";
                        combDevName += temp[0] + ",";
                        if(j % 5 == 0)
                        {
                            combDevName += "</br>";
                        }
                    }
                    ids = ids.substring(0,ids.length-1);
                    combDevName = combDevName.substring(0, combDevName.length-1);
                    var devTypeName = currDev.split(":")[0];
                    $("<div style='padding-left: 10px;' id='" + devTypeName + "' value='" + ids + "'>" +
                        "<span id='accUpgradeFirmwareDevRadioSpan"+ i +"'></span>" +
                        "<span style='font-weight: bold;'>"+ devTypeName + "</span>" + " : " + combDevName + "</div>").appendTo("#selectDev");
                    loadUIToDiv("input", "#accUpgradeFirmwareDevRadioSpan"+i, {
                        useInputReq:true,
                        hideLabel:true,
                        type:"radio",
                        name:"devRadio",
                        devIds:ids,
                        value:currDev.split(":")[0],
                        defaultChecked:i==0
                    });
                }
            }

            if(disabledDevNameStr != "" && disabledDevNameStr.length > 0)
            {
                $("<div class='zk-msg-error' style='padding-left: 10px; margin-top: 10px;'><b><@i18n 'acc_dev_disabled'/></b><br/>" + disabledDevNameStr + "</div>").appendTo("#selectDev");
            }

            if(offlineDevNameStr != "" && offlineDevNameStr.length > 0)
            {
                $("<div class='zk-msg-error' style='padding-left: 10px; margin-top: 10px;'><b><@i18n 'acc_dev_offline'/></b><br/>" + offlineDevNameStr + "</div>").appendTo("#selectDev");
            }

            // 延时加载，避免上面loadUIToDiv,方法内取不到元素数据
            window.setTimeout(function(){
                //对选择的设备进行操作  主要是将选择的设备的Id赋值给ids
                /*$("input:radio").each(function(){
                    $(this).click(function(){
                        var val = $(this).val();
                        var divVal  = val.replace(/\//g, '\\/');
                        $("#ids").attr("value", $("div#"+divVal).attr("value"));
                    });
                });
                //初始化
                $('input:radio[name="devRadio"]:checked').each(function(){
                    var val = $(this).val();
                    var divVal  = val.replace(/\//g, '\\/');
                    $("#ids").attr("value", $("div#"+divVal).attr("value"));
                });*/
            },100);

            //升级文件名自定义的验证,必须使用emfw.cfg，同时兼容读头升级固件，文件名为*.bin;（新增支持img后缀文件上传，支持CFace01固件升级）
            jQuery.validator.addMethod("fileNameValid", function(value, element){
                if (value.substring(value.lastIndexOf('\\') + 1).toLowerCase().indexOf(".cfg") > -1 || value.substring(value.lastIndexOf('\\') + 1).toLowerCase().indexOf(".bin") > -1
                    || value.substring(value.lastIndexOf('\\') + 1).toLowerCase().indexOf(".img") > -1)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            },"<@i18n 'acc_dev_selRightFile'/>");

            //验证
            $("#${formId}").validate({
                debug : true,
                rules :
                    {
                        "devFile" :
                            {
                                required : true,
                                fileNameValid : true
                            }
                    },
                submitHandler: function(form) {
                    $("#confirmButton").attr("disabled", true);  //禁用掉升级按钮，防止在操作过程中多次点击该按钮
                    var upgradeType = "device";
                    var devFileName = $("#devFile${uuid} input[name=devFile]").val();
                    if (devFileName.substring(devFileName.lastIndexOf('\\') + 1).toLowerCase().indexOf(".bin") > -1)
                    {
                        upgradeType = "reader";
                    }
                    var devRadioId = $('input:radio[name="devRadio"]:checked').attr("id");
                    var devIds = ZKUI.Input.get(devRadioId).options.devIds;
                    $('#${formId}').ajaxSubmit({
                        async : true,
                        dataType : 'json',
                        data: {
                            devIds:devIds,
                            clientId: ZKUI.Process.get("accUpgradeFirmware${uuid}").options.clientId,
                            upgradeType: upgradeType
                        },
                        success: function(data)
                        {
                            if (!data)
                            {
                                openMessage(msgType.error);
                            }
                            else
                            {
                                ZKUI.Grid.reloadGrid("${gridName}");
                            }
                        },
                        error:function (XMLHttpRequest, textStatus, errorThrown)
                        {
                            messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
                        }
                    });
                    ZKUI.Process.get("accUpgradeFirmware${uuid}").beginProgress(true);
                }
            });
        });

        function accUpgradeFirmware()
        {
            $("#${formId}").submit();
        }

    </script>
</@ZKUI.Content>
</@ZKUI.Process>
