<#include "/public/template/editTemplate.html">
<#macro editContent>
    <form action="accDevice.do?updateIpAddr" method="post" onkeydown="if(event.keyCode==13){return false;}" id="${formId}" enctype="multipart/form-data">
        <input type="hidden" id="id_model_pk" name="devId" value="${(tempAccDevice.id)!}"/>
        <input type="hidden" id="alias" name="alias" value="${(tempAccDevice.alias)!}"/>
        <table class="tableStyle accDeviceTable" cellpadding="3px">
            <#if (isSupportDualCard?? && isSupportDualCard?string == "true")>
            <tr>
                <th colspan="2" align="center"><label><@i18n 'acc_dev_networkCard'/>1</label></th>
            </tr>
        </#if>
        <tr>
            <th><label><@i18n 'common_ipAddress'/></label><span class="required">*</span></th>
            <td>
                <!--<div id="tag_id_newip" name="tag_id_newip" class="ip_input" ></div>-->
                <!--<input type="hidden" id="id_ipaddress" name="ipAddress" value="${(tempAccDevice.ipAddress)!}"/>-->
                <div style="display:inherit;">
                <@ZKUI.IP name="ipAddress" id="id_ipaddress" value="${(tempAccDevice.ipAddress)!}"/>
                </div>
            </td>
        </tr>
        <tr>
            <th><label><@i18n 'common_subnetMask'/></label><span class="required">*</span></th>
            <td>
                <!--<div id="tag_id_subnet_mask" name="tag_id_subnet_mask" class="ip_input"></div>-->
                <!--<input type="hidden" id="id_subnetMask" name="subnetMask" value="${(tempAccDevice.subnetMask)!}"/>-->
                <div style="display:inherit;">
                <@ZKUI.IP name="subnetMask" id="id_subnetMask" value="${(tempAccDevice.subnetMask)!}"/>
                </div>
            </td>
        </tr>
        <tr>
            <th><label><@i18n 'common_gatewayAddress'/></label><span class="required">*</span></th>
            <td>
                <!--<div id="tag_id_gateway" name="tag_id_gateway" class="ip_input"> </div>-->
                <!--<input type="hidden" id="id_gateway" name="gateway" value="${(tempAccDevice.gateway)!}">-->
                <div style="display:inherit;">
                <@ZKUI.IP name="gateway" id="id_gateway" value="${(tempAccDevice.gateway)!}"/>
                </div>
            </td>
        </tr>
        <#if (isSupportDualCard?? && isSupportDualCard?string == "true")>
        <tbody>
        <tr>
            <th colspan="2" style="padding: 8px 10px 1px;"><label><@i18n 'acc_dev_networkCard'/>2</label></th>
        </tr>
        <tr>
            <th><label><@i18n 'common_ipAddress'/></label><span class="required">*</span></th>
            <td>
                <!--<div id="tag_id_newip1" name="tag_id_newip1" class="ip_input" ></div>-->
                <!--<input type="hidden" id="id_ipaddress1" name="ipAddress1" value="${(IPAddress1)!}"/>-->
                <div style="display:inherit;">
                <@ZKUI.IP name="ipAddressSec" id="id_ipaddress1" value="${(IPAddress1)!}"/>
                </div>
            </td>
        </tr>
        <tr>
            <th><label><@i18n 'common_subnetMask'/></label><span class="required">*</span></th>
            <td>
                <!--<div id="tag_id_subnet_mask1" name="tag_id_subnet_mask1" class="ip_input"></div>-->
                <!--<input type="hidden" id="id_subnetMask1" name="netMask1" value="${(NetMask1)!}"/>-->
                <div style="display:inherit;">
                <@ZKUI.IP name="netMaskSec" id="id_subnetMask1" value="${(NetMask1)!}"/>
                </div>
            </td>
        </tr>
        <tr>
            <th><label><@i18n 'common_gatewayAddress'/></label><span class="required">*</span></th>
            <td>
                <!--<div id="tag_id_gateway1" name="tag_id_gateway1" class="ip_input"> </div>-->
                <!--<input type="hidden" id="id_gateway1" name="gateIPAddress1" value="${(GATEIPAddress1)!}">-->
                <div style="display:inherit;">
                <@ZKUI.IP name="gateIPAddressSec" id="id_gateway1" value="${(GATEIPAddress1)!}"/>
                </div>
            </td>
        </tr>

        <tr class="selectNetworkCard" style="display: none;">
            <th colspan="2" align="center"><label><@i18n 'acc_dev_networkCard'/>1</label></th>
        </tr>
        </tbody>
    </#if>
    </table>
    </form>
    <div id="id_info" style="display:none;">
        <ul class="errorlist">
            <li style="width:100%;"></li>
        </ul>
    </div>
<script type="text/javascript">
    $("#${formId}").validate({
        debug : true,
        rules :
            {
                "ipAddress":
                {
                    newIpAddressValid : true,
                },
                "subnetMask":
                {
                    newIpAddressValid : true,
                },
                "gateway":
                {
                    newIpAddressValid : true,
                }
                <#if (isSupportDualCard?? && isSupportDualCard?string == "true")>
                    ,
                    "ipAddressSec":
                    {
                        newIpAddressValid : true,
                    },
                    "netMaskSec":
                    {
                        newIpAddressValid : true,
                    },
                    "gateIPAddressSec":
                    {
                        newIpAddressValid : true,
                    }
                </#if>
                },

                submitHandler : function()
                {
                    var isExist;
                    $.ajax({
                        type: "POST",
                        url: "accDevice.do?isExistsIp",
                        data : {
                            "id":"${(tempAccDevice.id)!}",
                            "ipAddress" : ZKUI.IP.get("id_ipaddress").ip.getValue()
                        },
                        dataType: "json",
                        async: false,
                        success: function(retData)
                        {
                            isExist = retData.data;
                        }
                    });
                    if (!isExist)
                    {
                        messageBox({messageType:"alert", text: "<@i18n 'common_dev_hasDevIP'/>"});
                        return false;
                    }
                    <#if (isSupportDualCard?? && isSupportDualCard?string == "true")>
                        //检测网卡1，网卡2不能同一个网段
                        var firstIp = ZKUI.IP.get("id_ipaddress").ip.getValue();
                        var secondIp = ZKUI.IP.get("id_ipaddress1").ip.getValue();
                        if(firstIp.substring(0,firstIp.lastIndexOf(".")) == secondIp.substring(0,secondIp.lastIndexOf(".")))
                        {
                            messageBox({messageType:"alert", text: "<@i18n 'acc_dev_networkSegmentSame'/>"});
                            return false;
                        }

                        var isExist;
                        $.ajax({
                            type: "POST",
                            url: "accDevice.do?isExistsSecondIp",
                            data : {
                                "id":"${(tempAccDevice.id)!}",
                                "newIp" : ZKUI.IP.get("id_ipaddress1").ip.getValue()
                            },
                            dataType: "json",
                            async: false,
                            success: function(retData)
                            {
                                isExist = retData.data;
                            }
                        });
                        if (!isExist)
                        {
                            messageBox({messageType:"alert", text: "<@i18n 'common_dev_hasDevIP'/>"});
                            return false;
                        }
                    </#if>
                execAccConfirm($("#id_model_pk").val(), function(){<@submitHandler/>});
            }
        }
    );


    jQuery.validator.addMethod("newIpAddressValid", function(value, element)
    {
        var ipv4_1 = value.split(".")[0];
        var ipv4_2 = value.split(".")[1];
        var ipv4_3 = value.split(".")[2];
        var ipv4_4 = value.split(".")[3];
        var idArray = element.id.split("_");
        var ipId = idArray[0] + "_" + idArray[1];
        if (ipv4_1 == "" || ipv4_2 == "" || ipv4_3 == "" || ipv4_4 == "")
        {
            setTimeout(function(){
                $("#" + ipId).removeClass("dhtmlxComboxError");
                $("#" + ipId + " span").css('border','1px solid red');
            },1);
            return false;
        }
        else
        {
            $("#" + ipId + " span").css('border','1px solid #c3c2c2');
            return true;
        }
    }, "<@i18n 'common_jqMsg_required'/>");
</script>
</#macro>