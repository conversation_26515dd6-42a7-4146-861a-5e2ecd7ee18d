<!--<div id="devAuthorizeGridBox" style="height: 330px;"></div>-->
<#assign gridName="accAuthorizeChildDevGrid${uuid!}">
<!--<input type="hidden" id="devId" value="${tempAccDevice.id}">-->
<input type="hidden" id="cmdId" value="${cmdId}">
<div style="height: 85%">
    <@ZKUI.GridBox gridName="${gridName}">
        <!--<@ZKUI.Searchbar>-->
            <!--<@ZKUI.SearchTop>-->
                <!--<td valign="middle">-->
                    <!--<@ZKUI.Input name="devSn" maxlength="30" title="common_dev_sn" type="text"/>-->
                <!--</td>-->
                <!--<td valign="middle">-->
                    <!--<@ZKUI.Input name="ipAddress" maxlength="30" title="common_ipAddress" type="text"/>-->
                <!--</td>-->
            <!--</@ZKUI.SearchTop>-->
        <!--</@ZKUI.Searchbar>-->
        <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccAuthorizeChildDevItem" query="accDevice.do?getAuthorizeChildDevList&cmdId=${cmdId!}"/>
    </@ZKUI.GridBox>
</div>
<div class="bottomDiv bottomDivR" style="margin-top: 12px">
<input type="button" class="button-form" value="<@i18n 'common_edit_ok'/>" onclick="submit()" />
<input type="button" class="button-form" onclick="DhxCommon.closeWindow()" value="<@i18n 'common_op_close'/>" />
</div>
<script type="text/javascript">
/*$(function(){
	var devAuthorizeGrid = new dhtmlXGridObject('devAuthorizeGridBox');
	//devAuthorizeGrid.setImagePath(sysCfg.rootPath+"/public/controls/dhtmlx/dhtmlxGrid/codebase/imgs/");
	devAuthorizeGrid.setHeader(",<@i18n 'common_dev_deviceModel'/>,<@i18n 'common_ipAddress'/>,<@i18n 'common_dev_sn'/>,<@i18n 'common_dev_macAddress'/>");
	devAuthorizeGrid.setColumnMinWidth("40,70,70,70,70");
	devAuthorizeGrid.setInitWidths("40,70,100,150,*");//150
	devAuthorizeGrid.setColAlign("left,left,left,left,left");
	devAuthorizeGrid.setColTypes("ch,ro,ro,ro,ro");//ro
	devAuthorizeGrid.enableAutoHeight(false);
	devAuthorizeGrid.enableAutoWidth(false);
	devAuthorizeGrid.setColSorting("na,na,na,na,na");// 排序
	devAuthorizeGrid.init();
	devAuthorizeGrid.enableHeaderMenu("false,false,false,false,false");
	devAuthorizeGrid.setSkin(sysCfg.dhxSkin);
	// openMessage(msgType.loading);
	$.ajax({
		url:"accDeviceAction!getDeviceAuthorizeList.action",
		type:"post",
		dataType:"json",
		data:{id:$("#devId").val(),cmdId:$("#cmdId").val()},
		success: function (result)
        {
			closeMessage();
        	if(result[sysCfg.ret] == sysCfg.success)
			{
        		if(result[sysCfg.data] != undefined && result[sysCfg.data].data != undefined && result[sysCfg.data].data.length > 0)
        		{
	        		$(result[sysCfg.data].data).each(function(i,val){
	        			devAuthorizeGrid.addRow(val.SN,["<input type=checkbox name=devSns class=chcekClass onClick=isCheckedAll('devSns','devAuthorizeHeadBox','count') value="+val.SN+" style=margin-left:7px; />",val["~DeviceName"],val.IPAddress,val.SN,val.MAC]);
	        		});
	        		devAuthorizeGrid.setSizes();
        		}
        		else
        		{
        			messageBox({messageType:"alert", text: "${acc_dev_noData}"});
        		}
			}
        	else
        	{
        		messageBox({messageType:"alert", text: data[sysCfg.msg]});
        	}
        	//showLoading(false, devAuthorizeGrid);
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) 
        {
        	showLoading(false, devAuthorizeGrid);
        	messageBox({messageType: "alert", title: "${common_prompt_title}", text: "${common_prompt_serverError}"});
        }
	});
	$("#devAuthorizeHeadBox").click(function(){
		checkedAll('devSns','devAuthorizeHeadBox','count');
	});
});*/

// function initAuthChildDevList() {
//     openMessage(msgType.loading);
//     // closeMessage();
// }

function submit()
{
    var devSns = ZKUI.Grid.GRID_PULL["${gridName}"].grid.getCheckedRows(0);
	if(devSns == "")
	{
		messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_widget_noItems'/>"});
		return;	
	}
    openMessage(msgType.loading);
	$.ajax({
	    type: "POST",
	    url: 'accDevice.do?validChildDevCount',
	    data:{
	        devId:"${devId}",
            devSns:devSns
        },
	    dataType: "json",
	    async: true,
	    success: function(data)
	    {
	        if(data["ret"] == sysCfg.success)
	        {
	        	$.ajax({
	        		url:"accDevice.do?authorizeChildDevice",
	        		type:"post",
	        		dataType:"json",
	        		data:{
	        		    devId:"${devId}",
                        devSns:devSns
                    },
	        		success: function (result)
	                {
                        closeMessage();
	        			openMessage(msgType.success);
	        			DhxCommon.closeWindow();
	                }
	        	});
	        }
	        else if(data["ret"] == "message")
	        {
	        	messageBox({messageType:"confirm", text: data["msg"], callback:function(result)
            	{
	        		if(result)
        			{
                        $.ajax({
	    	        		url:"accDevice.do?authorizeChildDevice",
	    	        		type:"post",
	    	        		dataType:"json",
	    	        		data:{
	    	        		    devId : "${devId}",
                                devSns : devSns
                            },
	    	        		success: function (result)
	    	                {
                                closeMessage();
	    	        			openMessage(msgType.success);
                                DhxCommon.closeWindow();
	    	                }
	    	        	});
        			}
        			else
        			{
        				closeMessage();
        			}
            	}});
	        }
	        else if(data["ret"] == "noLicense")
	        {
	            messageBox({messageType:"alert", text: data["msg"], callback:function(result)
            	{
            		if(result)
            		{
            		    closeMessage();
            		}
            	}});
	        }
	    },
	    error:function (XMLHttpRequest, textStatus, errorThrown)
	    {
 			messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>" + "-728"});
 		}
	});
}
</script>