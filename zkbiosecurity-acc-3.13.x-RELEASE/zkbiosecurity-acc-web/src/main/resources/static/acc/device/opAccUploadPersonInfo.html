<style type="text/css">
    .acc_upload_user_labelEllipsis {
        text-overflow:ellipsis;
        overflow: hidden;
        white-space:nowrap;
    }
</style>
<script type="text/javascript">
    $(function(){
        var devNameStr = "${(devicesName)!}";
        var disabledDevNameStr = "${(disabledDevName)!}";
        var offlineDevNameStr = "${(offlineDevName)!}";
        if(devNameStr.length == 0)
        {
            $("#accUploadPersonInfo${uuid}ConfirmButton").attr("disabled", true);
        }
        else
        {
            $("<div style='padding-left: 10px; margin-bottom: 4px;'>" +
                "<span id='span${uuid}'></span>" +
                "<span style='font-weight: bold;'>"+ devNameStr.split(":")[0] + "</span>" + " : " + devNameStr.split(":")[1] + "</div>").appendTo("#selectDev");
            loadUIToDiv("input", "#span${uuid}", {
                useInputReq:true,
                hideLabel:true,
                type:"radio",
                defaultChecked:true
            });
        }

        if(disabledDevNameStr != "" && disabledDevNameStr.length > 0)
        {
            $("<div class='zk-msg-error' style='padding-left: 10px; margin-top: 10px;'><b><@i18n 'acc_dev_disabled'/></b><br/>" + disabledDevNameStr + "</div>").appendTo("#selectDev");
        }

        if(offlineDevNameStr != "" && offlineDevNameStr.length > 0)
        {
            $("<div class='zk-msg-error' style='padding-left: 10px; margin-top: 10px;'><b><@i18n 'acc_dev_offline'/></b><br/>" + offlineDevNameStr + "</div>").appendTo("#selectDev");
        }

        //没有可操作的设备时，屏蔽生物模版的操作按钮
        if ($("#devIds").val() == "")
        {
            $("#tdGetFP").hide();
            $("#tdGetFV").hide();
            $("#tdGetFace").hide();
          	//TDB 屏蔽获取比对照片
			$("#tdGetBiophoto").hide();
            //屏蔽获取掌纹
            $("#tdGetIdPalm").hide();
            $("#tdGetIdVisLightPalm").hide();
            $("#tdGetIdIris").hide();
        }
        else
        {
            showOptRadio();
        }

        //根据设备的名称显示所允许的操作
        function showOptRadio()
        {
            var supportDevFun = "${(supportDevFun)}";
            var supportDevFunArr = supportDevFun.split(":");
            if(supportDevFunArr[1] == "1")
            {
                $("#tdGetFP").show();
            }
            else
            {
                $("#tdGetFP").hide();
            }
            if(supportDevFunArr[7] == "1")
            {
                $("#tdGetFV").show();
            }
            else
            {
                $("#tdGetFV").hide();
            }
            if(supportDevFunArr[2] == "1")
            {
                $("#tdGetFace").show();
            }
            else
            {
                $("#tdGetFace").hide();
            }
            //$("#isRemind").val(fun.substr(4,1))
            //TDB 是否显示获取比对照片选项
            if (supportDevFunArr[9] == "1")
            {
                $("#tdGetBiophoto").show();
            }
            else
            {
                $("#tdGetBiophoto").hide();
            }
            //是否显示获取掌纹信息选项
            if (supportDevFunArr[8] == "1")
            {
                $("#tdGetIdPalm").show();
            }
            else
            {
                $("#tdGetIdPalm").hide();
            }

            //是否显示获取掌纹信息选项(针对可见光掌纹）
             if (supportDevFunArr[10] == "1") {
                $("#tdGetIdVisLightPalm").show();
            }
             else
            {
                $("#tdGetIdVisLightPalm").hide();
            }
            //是否显示获取虹膜信息选项
             if (supportDevFunArr[4] == "1") {
                $("#tdGetIdIris").show();
            }
             else
            {
                $("#tdGetIdIris").hide();
            }
        }

        $("#${formId}").validate({
            debug : true,
            submitHandler: function(form) {

            }
        });
    });

    function accUploadPersonInfo()
    {
        var submitFun = function()
        {
            var ajaxSubmitFun = function()
            {
                var dataType =  $("input[name='dataType']:checked").val();
                var devName = "${(devicesName)!}";
                var aliasStr = devName.split(":")[1];
                var dataCountOnly = $("#hiddenDataCountOnly").val();
                var checkVal =  1;
                if(dataType == 1)
                {
                    checkVal = 0;
                }
                else
                {
                    checkVal = 1;
                }
                $("input[name='dataType']").attr("disabled",true);
                $("#accUploadPersonInfo${uuid}ConfirmButton").attr("disabled", true);
                $("#dataCountOnly").attr("disabled",true);
                //操作处理线程
                var clientId = new Date().getTime()  + "_" +  Math.round(Math.random() * 10000);
                ZKUI.Process.get("accUploadPersonInfo${uuid}").options.dealPath="accDevice.do?uploadPersonInfo&devIds=${(retIds)!}&dataType=" + dataType + "&dataCountOnly=" + dataCountOnly + "&tempClientId=" + clientId + "&alias=" + encodeURIComponent(aliasStr);
                ZKUI.Process.get("accUploadPersonInfo${uuid}").beginProgress();
            }
            var submit = function(result)
            {
                if(result)
                {
                    ajaxSubmitFun();
                }
                else
                {
                    $("#confirmButton").attr("disabled", false);
                }
            }
            //获取的数据类型 0人员 1指纹 2人脸 3指静脉 4虹膜
            var type = $("input:radio[name='dataType']:checked").val();
            debugger
            if(type == "0")
            {
                ajaxSubmitFun();
            }
            else
            {
                //非人员类型，要进行提醒
                if($("#dataCountOnly").attr("checked") != "checked")
                {
                    messageBox({messageType:"confirm",text:"<@i18n 'acc_dev_getPersonInfoPrompt'/>",callback:submit});
                }
                else
                {
                    ajaxSubmitFun();
                }
            }
        }
        if($("#isRemind").val() == "1" && $("#hiddenDataCountOnly").val() == "false")
        {
            messageBox
            ({
                messageType:"confirm",
                text: "<@i18n 'acc_dev_noRegDevTip'/>",
                callback: function(result)
                {
                    if(result)
                    {
                        execAccConfirm($("#devIds").val(), submitFun);
                    }
                }
            });
        }
        else
        {
            execAccConfirm($("#devIds").val(), submitFun);
        }
    }

    //选择获取数据类别
    $("input:radio[name='dataType']").change(function(){
        var dataType = $("input[name='dataType']:checked").val();
        $("input:radio[name='dataType']").attr("checked", false);
        $("#"+$(this).attr("id")).attr("checked", true);
        if(dataType == 2)
        {
            $("#trFile").show();
        }
        else
        {
            $("#trFile").hide();
        }
    });

    var operator = function(obj)
    {
        var devType = $(obj).attr("value");
        if(devType == "iFace7")
        {
            $("label[for='id_get_records_2']").show();
        }
        else
        {
            $("label[for='id_get_records_2']").hide();
        }
        $("input:hidden#ids").val($(obj).attr("data-ids"));
        $("input:radio[name='dataType']:first").attr("checked", true);//切换机型的时候默认选择第一个
    }
    //判断选择类型的设备
    $("input:radio[name='devRadio']").each(function(i){
        if(i == 0)
        {
            var is = $(this).attr("checked");
            if(is == "checked")
            {
                operator($(this));
            }
        }
        $(this).bind("click", function(){
            operator($(this));
        })
    });
    $("input:checkbox[name = 'dataCountOnly']").change(function(){
        if(this.checked)
        {
            $("#dataCountOnly").attr("value", "true");
            $("#hiddenDataCountOnly").val("true");
        }
        else
        {
            $("#dataCountOnly").attr("value", "false");
            $("#hiddenDataCountOnly").val("false");
        }
    });


    function finishProgress(type)
    {
        $("input[name='dataType']").attr("disabled",false);
        $("#dataCountOnly").attr("disabled",false);
        $('#confirmButton').attr('disabled',false);
    }
</script>

<@ZKUI.Process id="accUploadPersonInfo${uuid}" type="single" confirmText="acc_dev_start" onSure="accUploadPersonInfo" onFinish="finishProgress">
<!-- content内容标签必须指定name属性，用来指定嵌套位置 -->
    <@ZKUI.Content name="op">
        <form action="accDevice.do?uploadPersonInfo" method="post" id="accUploadPersonInfoForm${uuid}" >
            <input type="hidden"  name="devIds" id="devIds" value="${(retIds)!}"/>
            <input type="hidden" id="isRemind" name="isRemind"/>
            <div style="padding: 0px 10px;">
                <div style="padding-bottom: 2px;"><label><@i18n 'common_dev_selectedDev'/></label></div>
                <div class="access-infoDiv zk-content-bg-color zk-border-color" style="border: solid 1px green;background-color:white;width:99%;height: 110px; overflow:auto">
                    <div name="selectDev" id="selectDev"></div>
                </div>
            </div>

            <div style="width: 100%;">
                <div  id="tdGetPerson" style="width: 47%;display: inline-block; padding: 3px" class="acc_upload_user_labelEllipsis">
                    <label title="<@i18n 'acc_dev_getPersonInfo'/>"><@ZKUI.Input type="radio" name="dataType" value="0" id="idPerson" checked="checked"/> <@i18n 'acc_dev_getPersonInfo'/></label>
                </div>

                <div id="tdGetFP" style="width: 47%;display: inline-block; padding: 3px" class="acc_upload_user_labelEllipsis">
                    <label title="<@i18n 'acc_dev_getFPInfo'/>"><@ZKUI.Input type="radio" name="dataType" value="1" id="idFp"/> <@i18n 'acc_dev_getFPInfo'/></label>
                </div>

                <div id="tdGetFace" style="width: 47%;display: inline-block; padding: 3px" class="acc_upload_user_labelEllipsis">
                    <label title="<@i18n 'acc_dev_getFaceInfo'/>"><@ZKUI.Input type="radio" name="dataType" value="2" id="idFace"/> <@i18n 'acc_dev_getFaceInfo'/></label>
                </div>

                <div id="tdGetFV" style="width: 47%;display: inline-block; padding: 3px" class="acc_upload_user_labelEllipsis">
                    <label title="<@i18n 'acc_dev_getFingerVeinInfo'/>"><@ZKUI.Input type="radio" name="dataType" value="7" id="idFv"/> <@i18n 'acc_dev_getFingerVeinInfo'/></label>
                </div>

                <div id="tdGetIdPalm" style="width: 47%;display: inline-block; padding: 3px" class="acc_upload_user_labelEllipsis">
                    <label title="<@i18n 'acc_dev_getPalmInfo'/>"><@ZKUI.Input type="radio" name="dataType" value="8" id="idPalm"/> <@i18n 'acc_dev_getPalmInfo'/></label>
                </div>
                <div id="tdGetIdVisLightPalm" style="width: 47%;display: inline-block; padding: 3px" class="acc_upload_user_labelEllipsis">
                    <label title="<@i18n 'acc_dev_getPalmInfo'/>"><@ZKUI.Input type="radio" name="dataType" value="10" id="idVisLightPalm"/> <@i18n 'acc_dev_getPalmInfo'/></label>
                </div>
                <div id="tdGetIdIris" style="width: 47%;display: inline-block; padding: 3px" class="acc_upload_user_labelEllipsis">
                    <label title="<@i18n 'acc_dev_getIrisInfo'/>"><@ZKUI.Input type="radio" name="dataType" value="4" id="idIris"/> <@i18n 'acc_dev_getIrisInfo'/></label>
                </div>
                <div id="tdGetBiophoto" style="width: 47%;display: inline-block; padding: 3px" class="acc_upload_user_labelEllipsis">
                    <label title="<@i18n 'acc_dev_getFaceInfo'/>"><@ZKUI.Input type="radio" name="dataType" value="9" id="idBiophoto"/> <@i18n 'acc_dev_getFaceInfo'/></label>
                </div>
            </div>
        </form>
    </@ZKUI.Content>
</@ZKUI.Process>