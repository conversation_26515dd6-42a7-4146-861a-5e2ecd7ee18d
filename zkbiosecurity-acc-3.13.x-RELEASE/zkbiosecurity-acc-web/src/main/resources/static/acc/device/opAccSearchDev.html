<#assign leftRTL = "left">
<#assign rightRTL = "right">
<#if enableRTL?? && enableRTL=="true">
<!-- 定义全局对齐方式变量(针对阿拉伯页面) -->
<#assign leftRTL = "right">
<#assign rightRTL = "left">
</#if>
<div id="accSearchDevGrid${uuid!}" style="padding: 3px 3px 0px;">
    <table width="100%">
        <tr>
            <td width="100%">
                <table>
                    <tr>
                        <td colspan="2">
                            <div id="searchDiv${uuid!}">
                                <button id="opSearch" onclick="searchDev(this)" class="button-form"><@i18n 'common_dev_beginSearch'/></button>
                                <span id="devSearchNoDevTip"><@i18n 'common_dev_searchNoDevTip'/></span>
                                <a id="downloadSearchTool${uuid}" href="upload/deviceSettingTool_overseas.exe" title="<@i18n 'common_dev_downloadSearchTool'/>"><@i18n 'common_dev_downloadSearchTool'/></a>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div class="acc-search-totalProgressDiv" id="totalProgressDiv">
                                <div class="totalProgressTitle"><@i18n 'common_op_totalProgress'/></div>
                                <div id="totalProgressId" class="progressBar">
                                    <span><div id="percentNum" style="z-index: 2; position: absolute; font-weight: 100;"></div></span>
                                    <img src="${base}/public/images/searching.gif" id="loadingImg" width="14px" style="margin-top: -15px; float: ${rightRTL!'right'}; display: none"/>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div id="idSearchResult" style="margin-top: 15px; margin-${leftRTL!'left'}: 10px; display:none;">
                                <div id="idHasDev" class="displayN"><@i18n 'common_dev_searchDevCount'/><span id="searchDevCount"></span></div>
                                <span id="idNoDev" class="displayN"><@i18n 'common_dev_searchNoDev'/></span>
                                <span id="addDevHint" style="display: none"><@i18n 'common_dev_hasAddDevCount'/><span id="addDevCount"></span></span>
                            </div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr height="5px;"><td></td></tr>
        <tr>
            <td width="100%">
                <table class="searchTable">
                    <tr>
                        <td><@i18n 'common_ipAddress'/><input id="devIPAddress${uuid!}" type="text" style="width: 120px; margin-left:7px;" onchange="devInfoFilter(this)" onkeyup="devInfoFilter(this)" disabled="true" maxlength="30"/></td>
                        <td><@i18n 'common_dev_devType'/><input id="devType${uuid!}" type="text" style="width: 120px; margin-left:7px;" onchange="devInfoFilter(this)" onkeyup="devInfoFilter(this)" disabled="true" maxlength="30"/></td>
                        <td><@i18n 'common_dev_sn'/><input id="devSN${uuid!}" type="text" style="width: 120px; margin-left:7px;" onchange="devInfoFilter(this)" onkeyup="devInfoFilter(this)" disabled="true" maxlength="30"/></td>
                        <td>
                            <div id="clearSearch" style="margin-left: 20px; display: none;" class="search_clear_button" onclick="clearSearch();devInfoFilter();" title="${common_search_clearTitle}">
                            </div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr height="5px;"><td></td></tr>
        <tr>
            <td>
                <div id="devGridBox${uuid!}" style="height:330px;"></div>
            </td>
        </tr>
    </table>
    <div style="margin-top: 10px; margin-left: 15px;">
	    <span class="warningImage"></span>
	    <span class="warningColor" id="warningColor${uuid!}"></span>
	</div>
</div>
<!-- 关闭按钮 -->
<div class="bottomDiv bottomDivR" style="margin-top:10px;">
    <button class="button-form" onclick="accDevCloseWindow()" id="closeButton"><@i18n 'common_op_close'/></button>
</div>
<script type="text/javascript">
	
	$().ready(function(){
		$("#warningColor${uuid!}").html("<@i18n 'common_dev_checkServerPortTip'/>".format("${hostPort}"));
	});
	
    if (sysCfg.language == 'zh_CN'){
        $("#downloadSearchTool${uuid}").attr("href", "upload/deviceSettingTool.exe");
    }

    function accDevCloseWindow() {
        clearInterval(interval);
        DhxCommon.closeWindow();
    }

	if(typeof(ajaxDevSearchObj) != "undefined" && ajaxDevSearchObj != null)  //将还没有返回的ajax调用全部终止掉
	{
		errorFlag = false;//用于判断是否是手动中断ajax请求，如果是手动中断，不提示错误信息
		ajaxDevSearchObj.abort();
	}
	var interval;  //定时器的变量

	 //进度条id
	var totalProgress_node_id = "totalProgressId";
	 //进度条进度
	var totalProgress = 0;
	 //进度条的速度
	var progressSpeed;
	function accBeginProgress()
	{
	    $("#loadingImg").show();
	    progressSpeed = 80;
	    totalProgress = 1;
	    interval = setInterval("accDoProgress()",progressSpeed);//1000为1秒钟
	    accReloadSearchCondition();
	}

	/**
	 * 重新加载搜索条件
	 *
	 * <AUTHOR>
	 * @since 2015年3月3日 下午2:37:14
	 */
	var searchDivHtml = $("#searchDiv${uuid!}").html();
	function accReloadSearchCondition()
	{
	    $("#devIPAddress${uuid!}").val("");
	    $("#devType${uuid!}").val("");
	    $("#devSN${uuid!}").val("");
	    $("#devIPAddress${uuid!}").attr("disabled", true);
		$("#devType${uuid!}").attr("disabled", true);
		$("#devSN${uuid!}").attr("disabled", true);
		$("#clearSearch").hide();
		$("#searchDiv${uuid!}").addClass("gray");
		$("#searchDiv${uuid!}").append($("#downloadSearchTool${uuid}").html());
		$("#downloadSearchTool${uuid}").remove();
	}

	//设置进度
	function accSetProgress(node_id,progress)
	{
	    if (node_id)
	    {
	        //var obj = document.getElmentById(node_id);
	        $("#" + node_id + " > span").css("width", String(progress) + "%");
	        $("#percentNum").html(progress + "%");
	      	//计算进度条数值位置
	        $("#percentNum").css("left", $("#totalProgressId").position().left + $("#totalProgressId").width() / 2 - $("#percentNum").width() / 2);
	        //$("#" + node_id + "Text").html(String(progress) + "%");
	    }
	}


	//进行循环获取进度阶段
	function accDoProgress()
	{
        accSetProgress(totalProgress_node_id,totalProgress);
        totalProgress++;
        /* if(totalProgress == 55)
        {
        	clearInterval(interval);
            progressSpeed = 250;
            interval = setInterval("accDoProgress()", progressSpeed);//1000为1秒钟
        } */
        if(totalProgress == 100)
        {
        	clearInterval(interval);
        }
	}
	$("#OK").remove();//
	var devGrid;
	accLoadDevGrid();
	function accLoadDevGrid()// 生成列表
	{
		devGrid = new dhtmlXGridObject('devGridBox${uuid!}');
		devGrid.setImagePath(sysCfg.rootPath+"/public/controls/dhtmlx/dhtmlxGrid/codebase/imgs/");
		devGrid.setHeader("<@i18n 'common_ipAddress'/>, <@i18n 'common_dev_macAddress'/>, <@i18n 'common_subnetMask'/>, <@i18n 'common_gatewayAddress'/>, " +
			"<@i18n 'common_dev_sn'/>, <@i18n 'common_dev_devType'/>, <@i18n 'common_dev_serverSet'/>, <@i18n 'common_relatedOp'/>");
		devGrid.setColumnMinWidth("80,70,70,70,70,70,70,70,70");
		devGrid.setInitWidths("120,135,110,110,115,100,140,*");//150
		//devGrid.setInitWidths("100,115,85,90,105,70,140,*");//150
        if (IS_ENABLE_RTL) {
            devGrid.setColAlign("right,right,right,right,right,right,right,right");
        }
		devGrid.setColTypes("ro,ro,ro,ro,ro,ro,ro,ro");//ro
		devGrid.enableAutoHeight(false);
		devGrid.enableAutoWidth(false);
		devGrid.setColSorting("str,str,str,str,str,str,str,str");// 排序
		devGrid.init();
		devGrid.enableHeaderMenu("true,true,true,true,true,true,true,false");
		devGrid.setSkin(sysCfg.dhxSkin);
		ZKUI.Grid.attachSizes(devGrid);
        devGrid.setSizes();
	}

    function clearSearch() {
        $("#devIPAddress${uuid!}").val("");
        $("#devType${uuid!}").val("");
        $("#devSN${uuid!}").val("");
    }

	// var ipv4 = null;// 定义一个ip4，避免验证前出错
	var ipArray = new Array();//全局变量，存放所有ip地址
	function getAllIPSn()// 获取所有Ip和Sn
	{
		//获取数据库中所有的IP地址、sn,避免用户修改ip地址时写入数据库中已有的ip地址（包含考勤和门禁
		var stamp = new Date().getTime();
		$.ajax({
			type: "POST",
			url: "/accDevice.do?getAllIPSn",
			dataType: "json",
			async: false,
			success: function(retData)
			{
				ipArray = retData["data"]["ipAddress"];
				snArray = retData["data"]["sn"];
			},
			error: function(XMLHttpRequest, textStatus, errorThrown)
			{
				messageBox({messageType:"alert", text: "<@i18n 'common_prompt_serverError'/>"+ "-612"});
			}
		});
	}

	var searching = false;//当前处于未搜索状态
	var addDevCount = 0;
	nowTime = new Date().getTime();
	function searchDev(obj)// 开始搜索设备
	{
        selectTab = "searchDev";
	    //leo 按下搜索按钮，按钮文本显示为“搜索中...”
	    obj.innerHTML="<@i18n 'common_dev_searching'/>";
	    accBeginProgress();
        //把搜索设备按钮设置为不能使用
	    $("#opSearch").attr("disabled", true);
		addDevCount = 0;
		//判断当前是否正在搜索
		if(searching == true)
		{
			messageBox({messageType:"alert", text: "<@i18n 'common_dev_searchAndWait'/>"});
			return false;
		}
		getAllIPSn();
		devGrid = null;
		accLoadDevGrid();//？为什么要再初始化一次,清空的话使用下面的clearAll即可---陈立
		//devGrid.clearAll();
//		$("#idSearching").show();
		$("#idHasDev").hide();
		$("#idSearchResult").hide();
		// showLoading(true, "devGridBox${uuid!}");
		searching = true;
		ajaxDevSearchObj = $.ajax({
	        url: "/accDevice.do?searchDev&nowTime=" + nowTime,
	        type: "POST",
	        dataType:"json",
	        async: true,
	        success:function(retData)
	        {
	            $("#loadingImg").hide();
	        	//先清楚掉所有Interval
	            clearInterval(interval);
	            accSetProgress(totalProgress_node_id, 100);
                var isSupportHttps = retData["data"]["isSupportHttps"];
                hostAddress = retData["data"]["hostAddress"];
                hostPort = retData["data"]["hostPort"];
				if(retData["data"]["nowTime"] == nowTime)
				{
					searching = false;
					//leo 搜索结束后，讲按钮文本改回为“开始搜索”
					obj.innerHTML="<@i18n 'common_dev_beginSearch'/>";
					//$("#idSearching").hide();
					if(retData["ret"] == "500")// 和设备通信异常
					{
						messageBox({messageType:"alert", text: "<@i18n 'common_dev_searchFails'/>" + retData['msg']});
					}
					else if(retData["ret"] == "400")// 内部程序异常
					{
						messageBox({messageType:"alert", text: "<@i18n 'common_prompt_serverError'/>"+ "-615"});
					}
					else
					{
						$("#idSearchResult").show();
						if(retData["data"]["devCount"] == "0")
						{
							$("#idHasDev").hide();
							$("#idNoDev").show();
						}
						else
						{
							$("#idHasDev").show();
							$("#idNoDev").hide();
							$("#searchRet").show();
				        	//$("#searchDevCount").html(retData["data"]["devCount"]);
				        	var data = retData["data"]["devData"];
				        	var oprateHtml = "";
				        	var moreOprateHtml = "";
				        	var devSNArray = getDevGridSN();
				        	for(var i = data.length-1; i >= 0; i--)
				        	{
                                var WebServer = "";
                                oprateHtml = "";
                                var protype = "pull";
                                if (typeof(data[i].Protype) != "undefined" && data[i].Protype == "push") {
                                    protype = "push";
                                }
				        		moreOprateHtml = "<div id='moreOperate_"+i+"' style='z-index: 2; display: none; position: absolute;' cursorIn=0>"
													+"<table style='position: absolute; width: 120px;' class='poPupInfo'>"
														+"<tbody>"
															+"<tr><td><a href='javascript:void(0)' id='modifyIP_"+i+"_"+protype+"'><@i18n 'common_dev_modifyIPAddress'/></a></td></tr>"
															+"<tr><td><a href='javascript:void(0)' id='modifyWebServer_"+i+"' style='display:none'><@i18n 'common_dev_serverSet'/></a></td></tr>"
														+"</tbody>"
													+"</table>"
												+"</div>";
				        		if(snArray.length > 0)
				        		{
				        			if($.inArray(data[i].SN, devSNArray) >= 0)
				        			{
				        				break;
				        			}
				        			for(var j = 0; j < snArray.length; j++)
					        		{
					        			if(data[i].SN == snArray[j])
					        			{
					        				oprateHtml = "<@i18n 'common_dev_devHasAdd'/>";
					        				$("#addDevHint").show();
					        				addDevCount += 1;
					        				$("#addDevCount").text(addDevCount);
					        				break;
					        			}
					        			else if(j == snArray.length-1)// 最后一次循环
					        			{
                                            if(data[i].SN != snArray[j])
                                            {
                                                if (typeof(data[i].ModeType) != "undefined" && data[i].ModeType == "2") {
                                                    oprateHtml = "<a href='javascript:void(0)' id='addDevAuthorize_"+i+"'><@i18n 'common_op_add'/></a>&nbsp;";
                                                }
                                                else if(protype == "push") //push设备的情况下，添加的对话框跟pull设备添加输入框内容不一样
                                                {
                                                    if (isSupportHttps == "true" && (typeof(data[i].IsSupportSSL) == "undefined" || data[i].IsSupportSSL == "0"))
                                                    {
                                                        oprateHtml = "<@i18n 'acc_dev_commTypeErr'/>";
                                                    }
                                                    else
                                                    {
                                                        oprateHtml = "<a style='display: inline-block' href='javascript:void(0)' id='modifyWebServer_"+i+"'><@i18n 'common_op_add'/></a>&nbsp;";
                                                        oprateHtml += "<a style='display: inline-block' href='javascript:void(0)' id='modifyIP_"+i+"_"+protype+"'><@i18n 'common_dev_modifyIPAddress'/></a>&nbsp;";
                                                    }
                                                }
                                                else
                                                {
                                                    if(data[i].Device != "FR5200" && data[i].Device != "FR4200")
                                                    {
                                                        oprateHtml = "<a href='javascript:void(0)' id='addDev_"+i+"'><@i18n 'common_op_add'/></a>&nbsp;";
                                                    }
                                                    oprateHtml += "<a href='javascript:void(0)' id='modifyIP_"+i+"_"+protype+"'><@i18n 'common_dev_modifyIPAddress'/></a>&nbsp;";
                                                }
                                            }
					        			}
					        		}
				        		}
				        		else
				        		{
                                    if (typeof(data[i].ModeType) != "undefined" && data[i].ModeType == "2") {
                                        oprateHtml = "<a href='javascript:void(0)' id='addDevAuthorize_"+i+"'><@i18n 'common_op_add'/></a>&nbsp;";
                                    }
                                    else if(typeof(data[i].Protype) != "undefined" && data[i].Protype == "push")
                                    {
                                        if (isSupportHttps == "true" && (typeof(data[i].IsSupportSSL) == "undefined" || data[i].IsSupportSSL == "0"))
                                        {
                                            oprateHtml = "<@i18n 'acc_dev_commTypeErr'/>";
                                        }
                                        else
                                        {
                                            oprateHtml = "<a style='display: inline-block' href='javascript:void(0)' id='modifyWebServer_"+i+"'><@i18n 'common_op_add'/></a>&nbsp;";
                                            oprateHtml += "<a style='display: inline-block' href='javascript:void(0)' id='modifyIP_"+i+"_"+protype+"'><@i18n 'common_dev_modifyIPAddress'/></a>&nbsp;";
                                        }
                                    }
                                    else
                                    {
                                        if(data[i].Device != "FR5200" && data[i].Device != "FR4200")
                                        {
                                            oprateHtml = "<a style='display: inline-block' href='javascript:void(0)' id='addDev_"+i+"'><@i18n 'common_op_add'/></a>&nbsp;";
                                        }
                                        oprateHtml += "<a style='display: inline-block' href='javascript:void(0)' id='modifyIP_"+i+"_"+protype+"'><@i18n 'common_dev_modifyIPAddress'/></a>&nbsp;";
                                    }
					        	}

                                if(typeof(data[i].SubControlOn) != "undefined" && data[i].SubControlOn == "1" && oprateHtml != "<@i18n 'common_dev_devHasAdd'/>")
                                {
                                    oprateHtml = "<@i18n 'acc_dev_addChildDeviceTip'/>";
                                }

                                if(data[i].WebServerURL)
                                {
                                    WebServer = data[i].WebServerURL;
                                }
                                else
                                {
                                    if(data[i].WebServerIP != undefined)
                                    {
                                        WebServer = "http://"+data[i].WebServerIP + ":";
                                    }
                                    if(data[i].WebServerPort != undefined)
                                    {
                                        WebServer += data[i].WebServerPort;
                                    }
                                }

                                if(typeof(data[i].exception) != "undefined")
                                {
                                    oprateHtml = data[i].exception;
                                }
				        		devGrid.addRow(i, [data[i].IP, data[i].MAC, data[i].NetMask, data[i].GATEIPAddress, data[i].SN, data[i].Device, WebServer, oprateHtml], 0);
				        		if(typeof(data[i].exception) != "undefined")
                        		{
                        			devGrid.setRowColor(i,"red");
                        		}
				        		ipArray.push(data[i].IP);
				        	}
				        	devGrid.sortRows(0);//按ip排序
				        	devGrid.setSizes(false);

                            //授权操作
                            $("a[id^='addDevAuthorize_']").each(function(){
                                $(this).click(function(){
                                    var rowId = $(this).attr("id").split("_")[1];
                                    var sn = devGrid.cells(rowId, 4).cell.innerHTML;
                                    var ipAddress = devGrid.cells(rowId, 0).cell.innerHTML;
                                    var devData = retData["data"]["devData"][rowId];
                                    var isSupportControlCard = "false";//是否支持控卡
                                    if (typeof(devData.isSupportControlCard) != "undefined")
                                    {
                                        isSupportControlCard = devData.isSupportControlCard;
                                    }
                                    var regDeviceType = "";//项目机参数
                                    if (typeof(devData.RegDeviceType) != "undefined")
                                    {
                                        regDeviceType = devData.RegDeviceType;
                                    }
                                    var isSupportNVR = "false";//是否支持NVR
                                    if (typeof(devData.IsSupportNVR) != "undefined")
                                    {
                                        isSupportNVR = devData.IsSupportNVR;
                                    }
                                    if (typeof(devData.ParentSN) != "undefined")
                                    {
                                        createWindow('acc_opAccSearchAddChildDev.action?rowId='+ rowId + '&ipAddress=' + ipAddress + '&sn='+sn+'&ParentSN='+devData.ParentSN+'&ParentDevId='+devData.ParentDevId+'&ParentDevAlias='+devData.ParentDevAlias+'&type=serchAddChildDev^0^0^350^200^<@i18n "acc_dev_configParentDevice"/>');
                                    }
                                    else
                                    {
                                        if ("${Application['system.language']}" == "zh_CN" || validDevRegDeviceType(regDeviceType)) {
                                            var opts = {
                                                path: "skip.do?page=acc_device_opAccAddPushDev&rowId=" + rowId + "&ipAddress=" + ipAddress + "&sn=" + sn + "&isSupportControlCard=" + isSupportControlCard + "&isSupportNVR=" + isSupportNVR,
                                                width: 520,
                                                height: 320,
                                                title: "<@i18n 'common_op_add'/>"
                                            };
                                            DhxCommon.createWindow(opts);
                                        } else {
                                            messageBox({messageType:"alert", text: "<@i18n 'acc_dev_regDeviceTypeTip'/>"});
                                        }
                                    }
                                });
                            });

							$("a[id^='addDev_']").each(function(){
								$(this).click(function(){
									var rowId = $(this).attr("id").split("_")[1];
									var ipAddress = devGrid.cells(rowId, 0).cell.innerHTML;
                                    var opts = {
                                        path: "skip.do?page=acc_device_opAccSearchAddDev&rowId=" + rowId + "&ipAddress=" + ipAddress + "&devName=" + ipAddress + "&commType=1",
                                        width: 520,
                                        height: 500,
                                        title: "<@i18n 'common_op_add'/>"
                                    };
                                    DhxCommon.createWindow(opts);
								});
							});

							// 修改ip地址操作页面
							$("a[id^='modifyIP_']").each(function(){
								$(this).click(function(){
									var rowId = $(this).attr("id").split("_")[1];
									var protype = $(this).attr("id").split("_")[2];
                                    var ipAddress = devGrid.cells(rowId, 0).cell.innerHTML;
                                    var mac = devGrid.cells(rowId, 1).cell.innerHTML;
                                    var subnetMask = devGrid.cells(rowId, 2).cell.innerHTML;
                                    var gateway = devGrid.cells(rowId, 3).cell.innerHTML;
                                    var opts = {
                                        path: "skip.do?page=acc_device_opAccSearchModifyIPAddress&rowId=" + rowId + "&ipAddress="+ ipAddress
                                            + "&mac=" + mac + "&subnetMask=" + subnetMask + "&gateway=" + gateway + "&protype=" + protype,
                                        width: 500,
                                        height: 270,
                                        title: "<@i18n 'common_dev_modifyIPAddress'/>"
                                    };
                                    DhxCommon.createWindow(opts);
								});
							});

                            // 设置设备通信的服务器IP和端口页面
                            $("a[id^='modifyWebServer_']").each(function(){
                                $(this).click(function(){
                                    var rowId = $(this).attr("id").split("_")[1];
                                    var serverAddress = devGrid.cells(rowId, 6).cell.innerHTML;
                                    var newWebServerIP = "";
                                    if (serverAddress.split("//").length > 1)
                                    {
                                        newWebServerIP = serverAddress.split("//")[1].split(":")[0];
                                    }
                                    var newWebServerPort = "";
                                    if (serverAddress.split(":").length > 2)
                                    {
                                        newWebServerPort = serverAddress.split(":")[2];
                                    }
                                    var ipAddress = devGrid.cells(rowId, 0).cell.innerHTML;
                                    var sn = devGrid.cells(rowId, 4).cell.innerHTML;
                                    var mac = devGrid.cells(rowId, 1).cell.innerHTML;
                                    var devData = retData["data"]["devData"][rowId];
                                    var dns = "0.0.0.0";//是否支持dns设置
                                    if (typeof(devData.DNSFunOn) != "undefined" && devData.DNSFunOn == "1")
                                    {
                                        dns = devData.DNS;
                                    }
                                    else if (typeof(devData.WebServerURLModel) != "undefined")
                                    {
                                        dns = devData.DNS;
                                    }
                                    var isSupportSSL = "false";
                                    if (typeof(devData.IsSupportSSL) != "undefined" && devData.IsSupportSSL == "1")
                                    {
                                        isSupportSSL = "true";
                                    }
                                    var isSupportNVR = "false";//是否支持NVR
                                    if (typeof(devData.IsSupportNVR) != "undefined")
                                    {
                                        isSupportNVR = devData.IsSupportNVR;
                                    }
                                    var machineType = "";
                                    if (typeof(devData.MachineType) != "undefined")
                                    {
                                        machineType = devData.MachineType;
                                    }
                                    var isSupportControlCard = "false";//是否支持控卡
                                    if (typeof(devData.isSupportControlCard) != "undefined")
                                    {
                                        isSupportControlCard = devData.isSupportControlCard;
                                    }
                                    var regDeviceType = "";//项目机参数
                                    if (typeof(devData.RegDeviceType) != "undefined")
                                    {
                                        regDeviceType = devData.RegDeviceType;
                                    }
                                    if ("${Application['system.language']}" == "zh_CN" || validDevRegDeviceType(regDeviceType)) {
                                        var opts = {
                                            path: "skip.do?page=acc_device_opAccSearchAddPushDev&rowId=" + rowId + "&ipAddress=" + ipAddress + "&devName=" + ipAddress + "&sn=" + sn + "&mac=" + mac + "&newWebServerIP=" + newWebServerIP + "&newWebServerPort=" + newWebServerPort
                                            + "&dns=" + dns + "&isSupportSSL=" + isSupportSSL + "&machineType=" + machineType + "&isSupportControlCard=" + isSupportControlCard
                                            +  "&hostAddress=" + hostAddress + "&hostPort=" + hostPort + "&isSupportNVR=" + isSupportNVR,
                                            width: 520,
                                            height: 450,
                                            title: "<@i18n 'common_op_add'/>"
                                        };
                                        DhxCommon.createWindow(opts);
                                    } else {
                                        messageBox({messageType:"alert", text: "<@i18n 'acc_dev_regDeviceTypeTip'/>"});
                                    }
                                });
                            });
				        }
						$("#devIPAddress${uuid!}").attr("disabled", false);
						$("#devType${uuid!}").attr("disabled", false);
						$("#devSN${uuid!}").attr("disabled", false);
						$("#clearSearch").show();
						$("#searchDiv${uuid!}").removeClass("gray");
						$("#searchDiv${uuid!}").html(searchDivHtml)
					}
					$("#searchDevCount").html(devGrid.getRowsNum());
					$("#opSearch").attr("disabled", false);
				}
	        },
	        error:function(XMLHttpRequest, textStatus, errorThrown)
			{
	        	$("#opSearch").attr("disabled", false);
	            //先清除Interval
	            clearInterval(interval);
	        	searching = false;
				if(errorFlag)
				{
					messageBox({messageType:"alert", text: "<@i18n 'common_prompt_serverError'/>"+ "-615"});
				}
				errorFlag = true;
			}
	    });
	}


	//进行条件过滤
	function devInfoFilter(obj)
	{
		devGrid.filterBy(0, function(value, id){
			if(($("#devIPAddress${uuid!}").val() == "" || devGrid.cells(id,0).getValue().indexOf($("#devIPAddress${uuid!}").val()) != -1) && ($("#devType${uuid!}").val() == "" || devGrid.cells(id,5).getValue().toUpperCase().indexOf($("#devType${uuid!}").val().toUpperCase()) != -1) && ($("#devSN${uuid!}").val() == "" || devGrid.cells(id,4).getValue().indexOf($("#devSN${uuid!}").val()) != -1))
			{
				return true;
			}
			else
			{
				return false;
			}
		});

		//处理谷歌，IE浏览器器搜索设备，过滤后要点击2次才有效果的问题
		$(obj).blur();
		$(obj).focus();

		//$("#searchDevCount").html(devGrid.getRowsNum());
	}

	/**
	 * 获取设备列表中已存在的设备sn数组--防止重复显示设备信息
	 * add by wenxin
	 * @return devSNArray
	 */
	function getDevGridSN()
	{
		var devSNArray = new Array();
		var rowNum = devGrid.getRowsNum();
		for(var i = 0; i < rowNum; i ++)
		{
			devSNArray[devSNArray.length] = devGrid.cells(i, 4).cell.innerHTML;
		}
		return devSNArray;
	}

	function createWindow(ids) {
        DhxCommon.createWindow(ids);
    }

    jQuery.validator.addMethod("ipPort", function(value, element) {
        var reg = /^(\d*)$/;
        if(value!="" && !reg.test(value))
        {
            return false;
        }
        return true;
    },function(){
        return   "<@i18n 'common_dev_validIPPort'/>"
    });

    function validDevRegDeviceType(regDeviceType) {
	    var regDeviceTypeMatch = true;
        $.ajax({
            type: "POST",
            url: "accDevice.do?validDevRegDeviceType",
            dataType: "json",
            async: false,
            data : {regDeviceType : regDeviceType},
            success: function(retData)
            {
                regDeviceTypeMatch = retData["data"];
            }
        });
        return regDeviceTypeMatch;
    }
</script>