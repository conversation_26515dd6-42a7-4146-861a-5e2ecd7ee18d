<#include "/public/template/editTemplate.html"> 
<#macro editContent>
	<script type="text/javascript">
		$("#${formId}").validate( {
			debug : true,
			rules :
			{
			},
			submitHandler : function()
			{
				<@submitHandler/>;
			}
		});
	</script>
		<form action="accDevice.do?setRegistrationDevice" method="post" onkeydown="if(event.keyCode==13){return false;}" id="${formId}" enctype="multipart/form-data">
			<input type="hidden" id="id_model_pk" name="devId" value="${(devId)!}"/>
			<input type="hidden" id="alias" name="alias" value="${(alias)!}"/>
			<table  class="tableStyle" cellpadding="3px">
			<tr>
				<th>
					<label><@i18n 'acc_dev_setRegistrationDevice'/></label>
				</th>
				<td>
					<@ZKUI.Combo empty="false" name="isRegistrationSel" id="isRegistrationSel" readonly="true" hideLabel="true" value="${(isRegistrationDevice?string)!}">
						<option value="true"><@i18n 'common_yes'/></option>
						<option value="false"><@i18n 'common_no'/></option>
					</@ZKUI.Combo>
				</td>
			</tr>
		</table>
		</form>
	<div id="id_info" style="display:none;">
		<ul class="errorlist">
			<li style="width:100%;"></li>
		</ul>
	</div>
</#macro>


