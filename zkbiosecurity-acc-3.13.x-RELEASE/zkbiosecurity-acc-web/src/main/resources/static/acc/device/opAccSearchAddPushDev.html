<#assign editPage = "true">
<#assign formId = "searchDevForm">
<#include "/public/template/editTemplate.html">
<#macro editContent>
<input type="hidden" name="rowId" id="rowId" value="${rowId}"/>
<form id="${formId}" action="accDevice.do?modifyIPAddress" method="post"
      onkeydown="if(event.keyCode==13){return false;}">
    <table class="tableStyle">
        <tr>
            <th><label><@i18n 'common_dev_name'/></label><span class="required">*</span></th>
            <td><input type="text" name="alias" id="alias" onkeypress="return isNotSpace(event);"
                       value="${(ipAddress)!}" maxlength="20"/></td>
        </tr>
        <tr>
            <th><label><@i18n 'common_dev_newServerIP'/></label><span class="required">*</span></th>
            <td>
                <#if dns?exists && dns != '0.0.0.0'>
                    <@ZKUI.Input hideLabel="true" type="radio" id="ip" name="newWebServerType" value="1" checked="checked"/><@i18n 'common_ipAddress'/>
                    <@ZKUI.Input hideLabel="true" type="radio" id="domain" name="newWebServerType" value="2"/><@i18n 'common_domain'/><br/>
                </#if>
                <div style="display:inherit;">
                <@ZKUI.IP id="newWebServerIP" name="newWebServerIP" value="${(newWebServerIP)!}" hideLabel="true"/>
                </div>
                <input type="text" id="newWebServerURL${uuid}" name="newWebServerURL" hidden="true"/>
                <input type="hidden" id="webServerIP" value="${(newWebServerIP)!}"/>
            </td>
        </tr>
        <tr>
            <th><label><@i18n 'common_dev_newServerPort'/></label><span class="required">*</span></th>
            <td><input type="text" id="newWebServerPort" name="newWebServerPort" value="${(newWebServerPort)!}"/>
            </td>
        </tr>
        <tr id="trDNS" hidden="true">
            <th><label>DNS</label><span class="required">*</span></th>
            <td>
                <div style="display:inherit;">
                <@ZKUI.IP id="newDNS" name="newDNS" value="${(dns)!}"/>
                </div>
            </td>
        </tr>
        <tr>
            <th><label><@i18n 'common_dev_commPwd'/></label></th>
            <td><input type="password" id="idCommPwd" onkeypress="return isNotSpace(event);"/>
        </tr>
        <tr>
            <th>
                <label><@i18n 'acc_dev_iconType'/></label><span class="required">*</span>
            </th>
            <td>
                <@ZKUI.Combo empty="false" name="iconType" width="148" readonly="true" hideLabel="true">
                    <option value="1"><@i18n 'acc_leftMenu_door'/></option>
                    <option value="2"><@i18n 'acc_dev_carGate'/></option>
                    <option value="3"><@i18n 'acc_dev_channelGate'/></option>
                </@ZKUI.Combo>
            </td>
        </tr>
        <tr>
            <th><label><@i18n 'base_area_entity'/></label><span class="required">*</span></th>
            <td>
                <div>
                    <@ZKUI.ComboTree autoFirst="true" readonly="true" width="148" type="radio" url="authArea.do?tree" hideLabel="true" name="authAreaId" value="${(authAreaId)!}"/>
                </div>
            </td>
        </tr>
    <tr id="level" <#if machineType?exists && (machineType == '40')>style="display:none;"</#if>>
    <th style="width:50%;"><label><@i18n 'acc_dev_addLevel'/></label></th>
    <td>
        <@ZKUI.Combo type="radio" width="148" path="accLevel.do?getLevelList" name="levelId" id="idAccLevel${uuid}" readonly="readonly" hideLabel="true" onChange="accCheckIsAllowAddToLevel"/>
    </td>
    </tr>
    <tr id="levelTip" style="display:none;">
        <td colspan="2">
            <div><span class='warningImage' style='margin-top:3px;'></span><span
                    class='warningColor'><@i18n 'acc_dev_levelTip'/></span></div>
        </td>
    </tr>
    <#if machineType?exists && (machineType == '7' || machineType == '10' || machineType == '2')>
        <tr>
            <th style="width: 50%;"><label><@i18n 'acc_dev_switchToTwoDoorTwoWay'/></label></th>
            <td>
                <@ZKUI.Input hideLabel="true" type="checkbox" name="door4ToDoor2" id="idDoor4ToDoor2" trueValue="1" falseValue="0" eventCheck="true"/>
            </td>
        </tr>
    </#if>
    <#if systemModules?lower_case?index_of("ivs")!=-1 && isSupportNVR?exists && isSupportNVR == "true">
        <tr>
            <th><label><@i18n 'base_db_userName'/></label><span class='required'>*</span></th>
            <td><input id='accUsername' name='accUsername' type='text'/></td>
        </tr>
        <tr>
            <th><label><@i18n 'base_login_password'/></label><span class='required'>*</span></th>
            <td><input id='accPassword' name='accPassword' type='password'/></td>
        </tr>
    </#if>
    <tr>
        <th style="width: 50%;"><label><@i18n 'common_dev_clearDevDataWhenAdding'/></label></th>
        <td>
            <@ZKUI.Input hideLabel="true" type="checkbox" name="clearAllData" id="idClearAllData" trueValue="1" falseValue="0" eventCheck="true"/>
        </td>
    </tr>
</table>
</form>
<div style="margin-top: 10px; margin-left: 15px; text-align: left;">
    <span class="warningImage"></span>
    <span class="warningColor"><@i18n 'common_dev_devClearDataTip'/></span>
</div>

<script type="text/javascript">
    $("#${formId}").validate({
        rules : {
            "newWebServerPort" : {
                required: true,
                ipPort:true
            },
            "authAreaId" : {
                required: true
            },
            "newWebServerIP" : {
                IpValid : true
            },
            "alias" :
            {
                required: true,
                unInputChar:true,
                // overRemote : ["/accDevice.do?isExistByAlias", "${(ipAddress)!}"]
                remote : {
                    url : "/accDevice.do?isExistAlias", //后台处理程序
                    type : "post", //数据发送方式
                    dataType : "json" //接受数据格式
                }
            },
            "iconType":
            {
                required:true
            },
            "accUsername" :
            {
                required: true
            },
            "accPassword":
            {
                required: true
            }
        },
        submitHandler:function(form)
        {
            submitForm();
        }
    });

    var ipArray = new Array();//全局变量，存放所有ip地址
    function getServiceIp()// 获取所有Ip和Sn
    {
        //获取数据库中所有的IP地址、sn,避免用户修改ip地址时写入数据库中已有的ip地址（包含考勤和门禁
        var stamp = new Date().getTime();
        $.ajax({
            type: "POST",
            url: "accDevice.do?getAllIPSn",
            dataType: "json",
            async: false,
            success: function(retData)
            {
                ipArray = retData["data"]["ipAddress"];
            },
            error: function(XMLHttpRequest, textStatus, errorThrown)
            {
                messageBox({messageType:"alert", text: "<@i18n 'common_prompt_serverError'/>"+ "-612"});
            }
        });
    }

    function submitForm()
    {
    	var webServerPort=$("#newWebServerPort").val();
        var ipAddress = "${ipAddress}";
        if (ipArray.indexOf(ipAddress) >= 0) { //软件中存在相同设备IP
            messageBox({messageType:"alert", text: "<@i18n 'acc_dev_ipAddressConflict'/>"});
        }
        else {
        	if(webServerPort != "${hostPort!}") {
        		messageBox({messageType:"confirm", text: "<@i18n 'acc_dev_checkServerPortTip'/>", callback:function(result)
                {
                    if(result)
                    {
                    	submitFormData();
                    }
                }});
        	}
        	else {
        		submitFormData();
        	}
        }

    }
    
    function submitFormData() {
    	var webServerIP= ZKUI.IP.get("newWebServerIP").getValue();
        var webServerPort=$("#newWebServerPort").val();
        var newPwd=$("#idCommPwd").val();
        var mac="${(mac)!}";
        var sn="${(sn)!}";
        var devName = $("#alias").val();
        var newWebServerURL = $("#newWebServerURL${uuid}").val();
        var isSupportSSL = "${(isSupportSSL)!}";
        var machineType = "${(machineType)!}";
        var ipAddress = "${(ipAddress)!}";
        var isSupportNVR = "${(isSupportNVR)!}";
        getServiceIp();
    	openMessage(msgType.loading);
    	onLoading(function(){
    	$.ajax({
            type: "POST",
            url: "accDevice.do?validAddPushDev",
            data: {"sn": sn, "machineType" : machineType, "isSupportNVR" : isSupportNVR},
            dataType: "json",
            async: true,
            success: function(data)
            {
                //处理不添加的权限组
                if ($("#${formId} #level").hasClass("gray")) {
                    ZKUI.Combo.get("idAccLevel${uuid}").setValue("");
                }
                //closeMessage();
                if(data["ret"] == "ok")
                {
                    excuteModify(webServerIP, webServerPort, newPwd, mac, sn, devName, newWebServerURL, isSupportSSL, ipAddress);
                }
                else if (data["ret"] == "nvrFail") {
                    messageBox({messageType:"alert", text: data["msg"], callback:function(result)
                        {
                            if(result)
                            {
                                DhxCommon.closeWindow();
                            }
                        }});
                }
                else if(data["ret"] == "message")
                {
                    messageBox({messageType:"confirm", text: data["msg"], callback:function(result)
                        {
                            if(result)
                            {
                                excuteModify(webServerIP, webServerPort, newPwd, mac, sn, devName, newWebServerURL, isSupportSSL, ipAddress);
                            }
                            else
                            {
                                DhxCommon.closeWindow();
                            }
                        }});
                }
                else if(data["ret"] == "noLicense")
                {
                    messageBox({messageType:"alert", text: data["msg"], callback:function(result)
                        {
                            if(result)
                            {
                                DhxCommon.closeWindow();
                            }
                        }});
                }
            },
            error:function (XMLHttpRequest, textStatus, errorThrown)
            {
                messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title' />", text: "<@i18n 'common_prompt_serverError' />" + "-728"});
            }
        });
        });
    }

    function excuteModify(webServerIP, webServerPort, commPwd, mac, sn, devName, newWebServerURL, isSupportSSL, ipAddress)
    {
        onLoading(function(){
            var rowId=$("#rowId").val();
            var pwd = window.btoa(commPwd);
            $('#${formId}').ajaxSubmit({
                type: "POST",
                url: "accDevice.do?modifyIPAddress",
                data: {"type": "webServerIP", "webServerIP": webServerIP, "webServerPort": webServerPort, "mac": mac,
                    "sn": sn, "devName" : devName, "isSupportSSL" : isSupportSSL, "ipAddress":ipAddress,
                    "commPwd": pwd},
                dataType: "json",
                async: false,
                success: function(retData){
                closeMessage();
                if(retData["ret"] == sysCfg.success)
                {
                    messageBox({messageType:"alert", text: "<@i18n 'acc_dev_rebootAfterOperate'/>", callback: function(result){
                            var webServerAddress = "";
                            var isSupportHttps = isSupportSSL;
                            if (isSupportHttps == "true")
                            {
                                if ($("#ip").attr("checked") == "checked" || $("#ip").length == 0)
                                {
                                    webServerAddress = "https://" + webServerIP + ":" + webServerPort;
                                }
                                else
                                {
                                    webServerAddress = "https://" + newWebServerURL + ":" + webServerPort;
                                }
                            }
                            else
                            {
                                if ($("#ip").attr("checked") == "checked" || $("#ip").length == 0)
                                {
                                    webServerAddress = "http://" + webServerIP + ":" + webServerPort;
                                }
                                else
                                {
                                    webServerAddress = "http://" + newWebServerURL + ":" + webServerPort;
                                }
                            }
                            devGrid.cells(rowId, 6).cell.innerHTML = webServerAddress;
                            DhxCommon.closeWindow();
                        }});
                }
                else if(retData["ret"] == "pwdError")
                {
                    messageBox({messageType:"alert", text: "<@i18n 'common_dev_opFaileAndReason' />" +  retData["msg"]});
                }
                else if(retData["ret"] == "500"){
                    messageBox({messageType:"alert", text: "<@i18n 'common_dev_opFaileAndReason' />" + retData["msg"]});
                }
                else
                {
                    messageBox({messageType:"alert", text: "<@i18n 'common_prompt_serverError' />"+ "-614"});
                }
            },
            error:function (XMLHttpRequest, textStatus, errorThrown)
            {
                closeMessage();
                messageBox({messageType:"alert", text: "<@i18n 'common_prompt_serverError' />"+ "-614"});
            }
        });
        });
    }

    $("#ip").click(function(){
        change(this);
    });

    $("#domain").click(function(){
        change(this);
    });

    function change(radio)
    {

        if ($(radio).val() == 1)
        {
            //$("#newWebServerURL${uuid}").val("");
            $("#newWebServerURL${uuid}").rules("remove");
            $("#newWebServerURL${uuid}").valid();

            $("#newWebServerIP").show();
            $("#newWebServerURL${uuid}, #trDNS").hide();

            $("#newWebServerIP_hidden").rules("remove");
            $("#newWebServerIP_hidden").rules("add", {IpValid : true, webServerValidate : true});
            $("#newWebServerIP_hidden").valid();
            $("input[name='newDNS']").rules("remove");
            $("input[name='newDNS']").valid();
        }
        else
        {
            $("#newWebServerIP_hidden").rules("remove");
            $("#newWebServerIP_hidden").valid();

            $("#newWebServerURL${uuid}, #trDNS").show();
            $("#newWebServerIP").hide();

            // $(":input[id^='dns_']").each(function(){
            //     // $(this).rules("remove");
            //     $(this).valid();
            //     // $(this).rules("add", {required: true});
            // });

            $("#newWebServerURL${uuid}").rules("remove");
            $("#newWebServerURL${uuid}").valid();
            // $("#newWebServerURL${uuid}").rules("add", {required: true, domainValid: true});
            $("#newWebServerURL${uuid}").rules("add", {required: true, domainValid: true});
            $("input[name='newDNS']").rules("remove");
            $("input[name='newDNS']").rules("add", {IpValid: true});
            $("input[name='newDNS']").valid();
        }
    }

    jQuery.validator.addMethod("webServerValidate", function(val, element)
    {
        var ipv4IP = ZKUI.IP.get("newWebServerIP").getValue();
        var ipAddress = "${(ipAddress)!}";
        var idArray = element.id.split("_");
        var ipId = idArray[0];
        if (ipv4IP == ipAddress)
        {
            setTimeout(function(){
                $("#" + ipId).removeClass("dhtmlxComboxError");
                $("#" + ipId + " span").css('border','1px solid red');
            },1);
            return false;
        }
        else
        {
            $("#" + ipId + " span").css('border','1px solid #c3c2c2');
            return true;
        }
    }, "<@i18n 'acc_dev_modifyDevIpTip'/>");

    jQuery.validator.addMethod("domainValid", function(val, element)
    {
        if (element.id != undefined && element.id != '' ) {
            var domainReg = /^(([0-9a-z_!~*'()-]+\.)*([0-9a-z][0-9a-z-]{0,61})?[0-9a-z]\.[a-z]{2,6})$/;
            var re = new RegExp(domainReg);
            if (val.trim() != "" && !re.test(val))
            {
                return false;
            }
            return true;
        }
        return true;
    }, "<@i18n 'acc_dev_inputDomainError'/>");

    var reg = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/;
    var patt = new RegExp(reg);
    var webServerIp = ZKUI.IP.get("newWebServerIP").getValue();
    if (patt.test(webServerIp))
    {
        if (webServerIp == "..." || webServerIp == "0.0.0.0")
        {
            webServerIp = "${hostAddress!}";
            $("#webServerIP").val(webServerIp);
            ZKUI.IP.get("newWebServerIP").setValue(webServerIp);
        }
        var webServerPort = $("#newWebServerPort").val();
        if (webServerPort == "0" || webServerPort == "80" || webServerPort != "${hostPort!}")
        {
            webServerPort = "${hostPort!}";
            $("#newWebServerPort").val(webServerPort);
        }
        $("#newWebServerIP_hidden").rules("add", {IpValid : true, webServerValidate : true});
    }
    <#if dns?exists && dns != '0.0.0.0'>
    else {
        ZKUI.IP.get("newWebServerIP").setValue("0.0.0.0");
        $("#newWebServerURL${uuid}").val($("#webServerIP").val());
        $("#domain").prop("checked", true).click();
    }
    </#if>
</script>
</#macro>