<#assign formId="accDevSetDSTimeForm${uuid}">
<#include "/public/template/editTemplate.html">
<#macro editMain></#macro>
<@ZKUI.Process id="accDevSetDSTime${uuid}" type="single" confirmText="acc_dev_start" onSure="accDevSetDSTime" onFinish="finishProgress">
<!-- content内容标签必须指定name属性，用来指定嵌套位置 -->
	<@ZKUI.Content name="op">
	<div style="padding: 0px 10px;">
		<div style="padding-bottom: 2px;"><label><@i18n 'acc_dev_selectedDst'/></label></div>
		<div class="access-infoDiv zk-content-bg-color zk-border-color" style="border: solid 1px green;background-color:white;width:99%;height: 100px; overflow:auto">
			 <div name="selectDev" id="selectDev"></div>
		</div>

		<form action="/accDevice.do?setDSTime" id="${formId}" method="post">
			<input type="hidden" name="devIds" id="devIds" value="${(devIds)!}"/>
			<table >
				<tr>
					<td><label><@i18n 'acc_leftMenu_dSTime'/></label>
						<@ZKUI.Combo id="dstimeId${uuid}" name="dstimeId" hideLabel="true" empty="true" title="acc_leftMenu_dSTime" path="/accDSTime.do?getDSTimeList">
						</@ZKUI.Combo>
					</td>
				</tr>
			</table>
		</form>
	</div>
	<script>
        var devNameStr = "${(devicesName)!}";
        var devAlias = "";
        var alias = new Array();
        var disabledDevNameStr = "${(disabledDevName)!}";
        var noSupportDevNameStr = "${(noSupportDevName)!}";
        var warnInfoDevNameStr = "${(warnInfoDevName)!}";
        if(devNameStr == "" || devNameStr.length == 0)
        {
            $("#accDevSetDSTime${uuid}ConfirmButton").attr("disabled", true);
        }
        else
        {
            var devArr = devNameStr.split(";");
            for(var i=0; i<devArr.length; i++)
            {
                var currDev = devArr[i];
                var devsuffix = currDev.split(":")[1];
                var devSuffixArr = devsuffix.split(",");
                var ids ="";
                var combDevName ="" ;
                devAlias ="" ;
                for(var j=1; j<=devSuffixArr.length; j++)
                {
                    var temp = devSuffixArr[j-1].split("&");
                    ids += temp[1] + ",";
                    combDevName += temp[0] + ",";
                    devAlias += temp[0] + ",";
                    if(j % 5 == 0)
                    {
                        combDevName += "</br>";
                    }
                }
                ids = ids.substring(0,ids.length-1);
                combDevName = combDevName.substring(0, combDevName.length-1);
                devAlias = devAlias.substring(0, devAlias.length-1);
                var devTypeName = currDev.split(":")[0].split("-");
                var devDstName = devTypeName[1];
                var devDstVal = devTypeName[1];
                if(devDstName == undefined)
                {
                    devDstName = "devDstId";
                }
                if(devDstVal == undefined)
                {
                    devDstVal = "";
                }
                $("<div style='padding-left: 10px;' id='" + devDstName + "' value='" + devDstVal + "'>" +
                    "<span id='accSetDsTimeDevRadioSpan"+ i +"'></span>" +
                    "<span style='font-weight: bold;'>"+ devTypeName[0] + "</span>" + " : " + combDevName + "</div>").appendTo("#selectDev");
                loadUIToDiv("input", "#accSetDsTimeDevRadioSpan" + i, {
					useInputReq:true,
					hideLabel:true,
					type:"radio",
					id:"devRadioId" + i,
					name:"devRadio",
					value:ids,
					dstattr:devDstVal,
					defaultChecked:i==0
				});
                alias[ids] = devAlias;
            }
        }
        if(disabledDevNameStr != "" && disabledDevNameStr.length > 0)
        {
            $("<div class='zk-msg-error' style='padding-left: 10px; margin-top: 10px;'><b><@i18n 'acc_dev_disabled'/></b><br/>" + disabledDevNameStr + "</div>").appendTo("#selectDev");
        }
        if(noSupportDevNameStr != "" && noSupportDevNameStr.length > 0)
        {
            $("<div class='zk-msg-error' style='padding-left: 10px; margin-top: 10px;'><b><@i18n 'acc_dev_noSupport'/></b><br/>" + noSupportDevNameStr + "</div>").appendTo("#selectDev");
        }
        if(warnInfoDevNameStr != "" && warnInfoDevNameStr.length > 0)
        {
            $("<div style='padding-left: 10px; margin-top: 10px; color: #E57A14;'><b><@i18n 'acc_dev_setDstimeWarnTip'/></b><br/>" + warnInfoDevNameStr + "</div>").appendTo("#selectDev");
        }


		function accDevSetDSTime()
		{
			var submitFun = function(){
				$.ajaxSetup( {
					async : true,
					dataType : 'json'
				});
				//操作处理线程
                var devType = $("input[name='devRadio']:checked").val();
                var aliasStr = alias[devType];
				$('#${formId}').ajaxSubmit({
					async : true,
					dataType : 'json',
					data: {
                        clientId: ZKUI.Process.get("accDevSetDSTime${uuid}").options.clientId,
						alias: aliasStr
					},
					success: function(result)
					{

					},
					error: function(XMLHttpRequest, textStatus, errorThrown)
					{
						messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
					}
				});
                ZKUI.Process.get("accDevSetDSTime${uuid}").beginProgress(true);
			}
			execAccConfirm($("#devIds").val(), submitFun);
		}

		// 延时加载，避免上面loadUIToDiv,方法内取不到元素数据
    	window.setTimeout(function () {
			//对选择的设备进行操作  主要是将选择的设备的Id赋值给ids
			$("input:radio").each(function(){
				$(this).click(function(){
					var val = $(this).val();
					$("#devIds").val(val);
					var id = $(this).attr("id");
					var dstattr = ZKUI.Input.get(id).options.dstattr;
					updateDSTimeComboValue(dstattr);
				});
			});
		}, 100);
        //初始化
        function initAccDSTime() {
            var val = $('input:radio[name="devRadio"]:checked').val();
            $('input:radio[name="devRadio"]:checked').click();
        }


        function updateDSTimeComboValue(value) {
            ZKUI.Combo.get("dstimeId${uuid}").combo.setComboValue(value);
        }

        setTimeout(function () {
            initAccDSTime();
		}, 100);
	</script>
	</@ZKUI.Content>
</@ZKUI.Process>