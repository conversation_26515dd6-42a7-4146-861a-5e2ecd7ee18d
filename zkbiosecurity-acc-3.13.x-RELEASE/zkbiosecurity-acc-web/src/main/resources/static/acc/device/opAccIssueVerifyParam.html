<@ZKUI.Process id="setVerifyParam${uuid}" dealPath="" type="single" confirmText="acc_dev_start" onSure="setAccVerifyParam">
    <!-- content内容标签必须指定name属性，用来指定嵌套位置 -->
    <@ZKUI.Content name="op">
        <div style="padding: 0px 10px;">
            <input type="hidden"  name="devIds" id="devIds" value="${(ids)!}"/>
            <div style="padding-bottom: 2px;"><label><@i18n 'common_dev_selectedDev'/></label></div>
            <div name="selectDev" id="selectDev${uuid}" class="zk-content-bg-color zk-border-color" style="height: 110px; overflow:auto">
            </div>
            <fieldset class="zk-content-bg-color zk-border-color" style="margin-top: 8px">
                <legend class="zk-content-bg-color">
                    <@i18n 'acc_dev_issueVerifyParam'/>
                </legend>
                <table style="padding-top: 8px;" class="tableStyle" >
                    <tr>
                        <th id="verifyParamText${uuid}"><@i18n 'acc_dev_backgroundVerify'/></th>
                        <td>
                            <@ZKUI.Combo empty="false" id="verifyParam${uuid}" name="verifyParam" width="148" hideLabel="true" onChange="accVerifyParamChange">
                                <option value="0"><@i18n 'common_disable'/></option>
                                <option value="1"><@i18n 'common_enable'/></option>
                            </@ZKUI.Combo>
                        </td>
                    </tr>
                    <tr>
                        <th id="offlineText${uuid}"><label><@i18n 'acc_gapb_devOfflineRule'/></label></th>
                        <td>
                            <@ZKUI.Combo empty="false" id="offlineRule${uuid}" name="offlineRule" value="${(offlineRule)!}" width="148" hideLabel="true">
                                <option value="0"><@i18n 'acc_gapb_standardLevel'/></option>
                                <option value="1" ><@i18n 'acc_gapb_accessDenied'/></option>
                            </@ZKUI.Combo>
                        </td>
                    </tr>
                </table>
            </fieldset>
        </div>
    </@ZKUI.Content>
</@ZKUI.Process>
<script type="text/javascript">
    var onlineDeviceFlag = false;//是否有在线的设备
    $(function(){
        var devNameStr = "${(devicesName)!}";
        var validArr = devNameStr.split(";");
        var disabledDevNameStr = "${(disabledDevName)!}";
        var noSupportDevNameStr = "${(noSupportDevName)!}";
        var offlineDevNameStr =  "${(offlineDevName)!}";
        if(devNameStr.length <= 10)
        {
            $("#setVerifyParam${uuid}ConfirmButton").attr("disabled", true);
        }
        else
        {
            for(var i=0; i<validArr.length; i++)
            {
                var currValidArr = validArr[i].split(":");
                if(currValidArr.length <= 1)
                {
                    continue;
                }
                var validValArr = currValidArr[1].split(",");
                var ids ="";
                var combDevName ="" ;
                for(var j=1; j <= validValArr.length; j++)
                {
                    var temp = validValArr[j-1].split("&");
                    ids += temp[1] + ",";
                    combDevName += temp[0] + ",";
                    if(j % 5 == 0)
                    {
                        combDevName += "</br>";
                    }
                }
                ids = ids.substring(0,ids.length-1);
                combDevName = combDevName.substring(0, combDevName.length-1);
                var type = currValidArr[0] == "close" ? "<@i18n 'acc_dev_enableFeature'/>" : "<@i18n 'acc_dev_disableFeature'/>";
                if(ids != "")
                {
                    onlineDeviceFlag = true;
                    $("<div style='padding-left: 10px;' id='" + type + "' value='" + ids + "'>" +
                        "<span id='accIssueVerifyParamDevRadio"+ i +"'></span>" +
                        "<span style='font-weight: bold;'>"+ type + "</span>" + " : " + combDevName + "</div>").appendTo("#selectDev${uuid}");
                    loadUIToDiv("input", "#accIssueVerifyParamDevRadio" + i, {
                        useInputReq:true,
                        hideLabel:true,
                        type:"radio",
                        name:"devRadio",
                        value:ids,
                        modul:type,
                        defaultChecked:i==0
                    });
                }
            }
        }
        if(disabledDevNameStr != "" && disabledDevNameStr.length > 0)
        {
            $("<div class='zk-msg-error' style='padding-left: 10px; margin-top: 10px;'><b><@i18n 'acc_dev_disabled'/></b><br/>" + disabledDevNameStr + "</div>").appendTo("#selectDev${uuid}");
        }
        if(noSupportDevNameStr != "" && noSupportDevNameStr.length > 0)
        {
            $("<div class='zk-msg-error' style='padding-left: 10px; margin-top: 10px;'><b><@i18n 'acc_dev_noSupport'/></b><br/>" + noSupportDevNameStr + "</div>").appendTo("#selectDev${uuid}");
        }
        if(offlineDevNameStr != "" && offlineDevNameStr.length > 0)
        {
            $("<div class='zk-msg-error' style='padding-left: 10px; margin-top: 10px;'><b><@i18n 'acc_dev_offline'/></b><br/>" + offlineDevNameStr + "</div>").appendTo("#selectDev${uuid}");
        }
        if(!onlineDeviceFlag) {
            $('#setVerifyParam${uuid}ConfirmButton').attr("disabled", true);
            $("#offlineText${uuid}").attr("class", "gray");
            $("#offlineText${uuid}").attr("disabled", "disabled");
            ZKUI.Combo.get("offlineRule${uuid}").combo.disable(true);
            $("#verifyParamText${uuid}").attr("class", "gray");
            $("#verifyParamText${uuid}").attr("disabled", "disabled");
            ZKUI.Combo.get("verifyParam${uuid}").combo.disable(true);
        }
    });
    function execBGVerifyParamConfirmFun()
    {
        beginBGVerifyParamProcess();
    }

    function beginBGVerifyParamProcess() {
        var submitFun = function(){
            //操作处理线程
            $("#setVerifyParam${uuid}ConfirmButton").attr("disabled", true);
            //操作处理进度
            ZKUI.Process.get("setVerifyParam${uuid}").options.dealPath="/accDevice.do?issueBGVerifyParam&devIds="+$("#devIds").val()+"&devName=${(alias)!}&verifyParam=" + ZKUI.Combo.get("verifyParam${uuid}").combo.getSelected() + "&offlineRule=" + ZKUI.Combo.get("offlineRule${uuid}").combo.getSelected();
            ZKUI.Process.get("setVerifyParam${uuid}").beginProgress();
        }
        execAccConfirm($("#devIds").val(), submitFun);
    }

    function setAccVerifyParam()
    {
        //启用
        if(ZKUI.Combo.get("verifyParam${uuid}").combo.getSelected() == "1")
        {
            //检测设备关联的门是否设置多人开门规则
            $.ajax({
                url: "/accDevice.do?checkCombOpenDoor&devIds=" + $("#devIds").val(),
                type: "POST",
                dataType:"json",
                async: false,
                success:function(data)
                {
                    if(data.ret == "ok")
                    {
                        execBGVerifyParamConfirmFun();
                    }
                    else if(data.ret == "hasBeenSetCombOpenDoor")
                    {
                        if(data.msg != "")
                        {
                            messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: data.msg});
                        }
                        else
                        {
                            execBGVerifyParamConfirmFun();
                        }
                    }
                },
                error:function (XMLHttpRequest, textStatus, errorThrown)
                {
                    messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
                }
            });
        }
        else//禁用
        {
            execBGVerifyParamConfirmFun();
        }
    }

    // 延时加载，避免上面loadUIToDiv,方法内取不到元素数据
    window.setTimeout(function () {
        //初始化
        $('input:radio[name="devRadio"]:checked').each(function(){
            $("#devIds").val($(this).val());   //将选择的Id值付给ids
            var id = $(this).attr("id");
            var modul = ZKUI.Input.get(id).options.modul;
            if(modul == "<@i18n 'acc_dev_enableFeature'/>")   //修改下拉框中选项
            {
                setVerifyEnable();
            }
            else if(modul == "<@i18n 'acc_dev_disableFeature'/>")
            {
                setVerifyDisable();
            }
        });
        //动态选择
        $('input:radio[name="devRadio"]').each(function(){
            $(this).click(function(){
                $("#devIds").val($(this).val());   //将选择的Id值付给ids
                var id = $(this).attr("id");
                var modul = ZKUI.Input.get(id).options.modul;
                if(modul == "<@i18n 'acc_dev_enableFeature'/>")   //修改下拉框中选项
                {
                    setVerifyEnable();
                    ZKUI.Combo.get("offlineRule${uuid}").combo.setComboValue("${(offlineRule)!}");
                }
                else if(modul == "<@i18n 'acc_dev_disableFeature'/>")
                {
                    setVerifyDisable();
                }
            });
        });
    }, 100);

    function accVerifyParamChange(value) {
        if(value == "0") {
            setVerifyDisable(value);
        }
        else if(value == "1") {
            setVerifyEnable(value);
        }
    }

    // 设置禁用
    function setVerifyDisable()
    {
        ZKUI.Combo.get("verifyParam${uuid}").combo.forEachOption(function(options) {
            var value = options.value;
            if(value == "0") {
                ZKUI.Combo.get("verifyParam${uuid}").combo.setComboValue("0");
                $("#offlineText${uuid}").attr("class", "gray");
                $("#offlineText${uuid}").attr("disabled", "disabled");
                ZKUI.Combo.get("offlineRule${uuid}").combo.disable(true);
                ZKUI.Combo.get("offlineRule${uuid}").combo.setComboValue("0");
            }
        })
    }
    // 设置启用
    function setVerifyEnable(value)
    {
        ZKUI.Combo.get("verifyParam${uuid}").combo.forEachOption(function(options) {
            var value = options.value;
            if(value == "1") {
                ZKUI.Combo.get("verifyParam${uuid}").combo.setComboValue("1");
                $("#offlineText${uuid}").removeAttr("class");
                $("#offlineText${uuid}").removeAttr("disabled");
                ZKUI.Combo.get("offlineRule${uuid}").combo.disable(false);
            }
        })
    }
</script>