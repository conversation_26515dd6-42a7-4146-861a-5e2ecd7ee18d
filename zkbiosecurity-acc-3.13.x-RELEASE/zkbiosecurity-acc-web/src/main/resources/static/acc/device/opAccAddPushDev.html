<#assign editPage = "true">
<#assign formId = "searchDevForm${uuid}">
<#include "/public/template/editTemplate.html">
<#macro editContent>
<form id="${formId}" action="accDevice.do?grantAuthority" method="post">
    <table class="tableStyle">
        <tr>
            <th><label><@i18n 'common_dev_name'/></label><span class="required">*</span></th>
            <td><input type="text" name="alias" id="alias" onkeypress="return isNotSpace(event);" value="${(ipAddress)!}" maxlength="20"/></td>
        </tr>
        <tr <#if "${Application['system.language']}" == "zh_CN" && "${Application['system.productCode']}" == "ZKBioAccess">style="display:none;"</#if>>
            <th>
                <label><@i18n 'acc_dev_iconType'/></label><span class="required">*</span>
            </th>
            <td>
                <@ZKUI.Combo empty="false" name="iconType" width="148" readonly="true" hideLabel="true">
                    <option value="1"><@i18n "acc_leftMenu_door"/></option>
                    <option value="2"><@i18n "acc_dev_carGate"/></option>
                    <option value="3"><@i18n "acc_dev_channelGate"/></option>
                </@ZKUI.Combo>
            </td>
        </tr>
        <tr>
            <th><label><@i18n 'base_area_entity'/></label><span class="required">*</span></th>
            <td>
                <div>
                    <@ZKUI.ComboTree autoFirst="true" width="148" type="radio" url="authArea.do?tree" hideLabel="true" id="authAreaId${uuid}" name="authAreaId"/>
                </div>
            </td>
        </tr>
        <tr id="level">
            <th style="width:50%;"><label><@i18n 'acc_dev_addLevel'/></label></th>
            <td>
                <@ZKUI.Combo type="radio" width="148" path="accLevel.do?getLevelList" onChange="changeLevel" name="levelId" id="idAccLevel${uuid}" readonly="readonly" hideLabel="true" />
            </td>
        </tr>
        <#if systemModules?lower_case?index_of("ivs")!=-1 && isSupportNVR?exists && isSupportNVR == "true">
            <tr>
                <th><label><@i18n 'base_db_userName'/></label><span class='required'>*</span></th>
                <td><input id='accUsername' name='accUsername' type='text'/></td>
            </tr>
            <tr>
                <th><label><@i18n 'base_login_password'/></label><span class='required'>*</span></th>
                <td><input id='accPassword' name='accPassword' type='password'/></td>
            </tr>
        </#if>
        <tr id="levelTip" style="display:none;">
            <td colspan="2">
                <div><span class='warningImage' style='margin-top:3px;'></span><span class='warningColor'><@i18n 'acc_dev_levelTip'/></span></div>
            </td>
        </tr>
        <tr>
            <th style="width: 50%;"><label><@i18n 'common_dev_clearDevDataWhenAdding'/></label></th>
            <td>
                <@ZKUI.Input hideLabel="true" type="checkbox" name="clearAllData" id="idClearAllData" trueValue="1" falseValue="0" eventCheck="true"/>
            </td>
        </tr>
    </table>
</form>
<div style="margin-top: 10px; margin-left: 15px; text-align: left;">
    <span class="warningImage"></span>
    <span class="warningColor"><@i18n 'common_dev_devClearDataTip'/></span>
</div>
<script type="text/javascript">
    $("#${formId}").validate( {
        rules : {
            "authAreaId":
            {
                required: true
            },
            "alias" :
            {
                required: true,
                unInputChar:true,
                overRemote : [ "accDevice.do?isExistAlias", "" ]
            },
            "accUserName" :
            {
                required: true
            },
            "accPassWord":
            {
                required: true
            }
        },
        submitHandler:function(form)
        {
            //处理不添加的权限组
            if ($("#level").hasClass("gray"))
            {
                ZKUI.Combo.get("idAccLevel${uuid}").combo.disable(true);
            }
            else
            {
                ZKUI.Combo.get("idAccLevel${uuid}").combo.disable(false);
            }
            submitForm();
        }
    });

    function submitForm()
    {
        openMessage(msgType.loading);
        var areaId = ZKUI.ComboTree.get("authAreaId${uuid}").combo.getSelectedValue();
        var rowId = "${rowId}";
        var sn = "${sn}";
        // var accLevelId = $("#idAccLevel").val();
        var check = "false";
        var clearAllData = $("#${formId} #idClearAllData").attr("checked");
        if ($("#${formId} #idClearAllData").attr("checked") == "checked")
        {
            check = "true";
        }
        var devName = $("#${formId} #alias").val();
        var isSupportNVR = "${isSupportNVR}";
        $('#${formId}').ajaxSubmit({
            type: "POST",
            url: "accDevice.do?validAddPushDev",
            data: {"sn": sn, "clearAllData" : check, "isSupportNVR": isSupportNVR},
            dataType: "json",
            async: true,
            success: function(data)
            {
                closeMessage();
                if(data["ret"] == sysCfg.success)
                {
                    devAuthorize(sn, devName);
                }
                else if (data["ret"] == "nvrFail") {
                    messageBox({messageType:"alert", text: data["msg"], callback:function(result)
                        {
                            if(result)
                            {
                                DhxCommon.closeWindow();
                            }
                        }});
                }
                else if(data["ret"] == "message")
                {
                    messageBox({messageType:"confirm", text: data["msg"], callback:function(result)
                        {
                            if(result)
                            {
                                devAuthorize(sn, devName);
                            }
                            else
                            {
                                DhxCommon.closeWindow();
                            }
                        }});
                }
                else if(data["ret"] == "noLicense" || data["ret"] == "500")
                {
                    messageBox({messageType:"alert", text: data["msg"], callback:function(result)
                        {
                            if(result)
                            {
                                DhxCommon.closeWindow();
                            }
                        }});
                }
            },
            error:function (XMLHttpRequest, textStatus, errorThrown)
            {
                messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>" + "-728"});
            }
        });
    }

    function devAuthorize(sn, devName)
    {
        onLoading(function(){
            $('#${formId}').ajaxSubmit({
                type: "POST",
                dataType: "json",
                data : {
                    "sn" : sn,
                    "devName": devName,
                },
                async: true,
                success: function(res)
                {
                    closeMessage();
                    if (res["ret"] == "ok"){
                        messageBox({messageType:"alert", text: "<@i18n 'acc_dev_authorizedSuccessful'/>"});
                    } else {
                        messageBox({messageType:"alert", text: res["msg"]});
                    }
                    DhxCommon.closeWindow();
                },
                error: function(XMLHttpRequest, textStatus, errorThrown)
                {
                    messageBox({messageType:"alert", text: "<@i18n 'common_prompt_serverError'/>"+ "-612"});
                }
            });
        });
    }

    function changeLevel(id) {
        $.ajax({
            url: "accLevel.do?authDevAddLevelVerify",
            data: {
                id: id,
            },
            success: function(res) {
                if(res.ret != 'ok') {
                    messageBox({messageType: "alert", text: res.msg});
                    ZKUI.Combo.get('idAccLevel${uuid}').combo.setComboValue("");
                }
            }
        })
    }
</script>
</#macro>