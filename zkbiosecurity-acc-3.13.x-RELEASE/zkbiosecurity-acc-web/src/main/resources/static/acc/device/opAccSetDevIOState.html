<#include "/public/template/editTemplate.html"> 
<#macro editContent>
    <script type="text/javascript">
        $("#${formId}").validate( {
            debug : true,
            rules :
            {
            },
            submitHandler : function() {
                <@submitHandler/>;
            }
        })
    </script>
        <form action="accDevice.do?setDevIOState" method="post" onkeydown="if(event.keyCode==13){return false;}" id="${formId}" enctype="multipart/form-data">
            <input type="hidden" id="id_model_pk" name="devId" value="${(devId)!}"/>
            <input type="hidden" id="accDevIOState" name="devIOState" value="${(devIOState?string)!}"/>
            <table  class="tableStyle" cellpadding="3px">
            <tr>
                <th>
                    <label><@i18n 'acc_dev_hostState'/></label>
                </th>
                <td>
                    <@ZKUI.Combo empty="false" id="accInOrOutState${uuid}" name="InOrOutState" value="" width="148" hideLabel="true">
                        <option value="0"><@i18n 'acc_door_outState'/></option>
                        <option value="1"><@i18n 'acc_door_inState'/></option>
                    </@ZKUI.Combo>
                </td>
            </tr>
        </table>
        </form>
    <div id="id_info" style="display:none;">
        <ul class="errorlist">
            <li style="width:100%;"></li>
        </ul>
    </div>
    <script>
        if($("#accDevIOState").val() == "1")
        {
            ZKUI.Combo.get("accInOrOutState${uuid}").combo.setComboValue("1");
        }
        else
        {
            ZKUI.Combo.get("accInOrOutState${uuid}").combo.setComboValue("0");
        }
    </script>
</#macro>


