<#assign formId = "${uuid}">
<#include "/public/template/editTemplate.html">
<#macro editContent>
<script type="text/javascript">

function changeContentRule(obj, item)
{
	$("#${formId} .tableStyle tr").removeClass("rowSelected");
	obj.className = "odd_dhx_skyblue rowSelected";
	$("#${formId} #contentRuleId").val(item);
}
$("#${formId} #tr0").click();
$("#${formId}SaveContinue").hide();
$("#${formId}OK").hide();
</script>
<form id="${formId}">
	<div id="baseTab" class="accQueryDevRule" style="overflow-y: auto;overflow-x: hidden;">
		<table class="tableStyle">
			<tr>
				<td width="70%">
					<table width="100%" id="ruleContentTable">
						<tbody>
							<tr class="odd_dhx_skyblue" width="100%">
								<th><label><@i18n 'acc_dev_ruleType'/></label></th>
								<th><label><@i18n 'base_system_description'/></label></th>
							</tr>
							<#list ruleList as item>
									<tr class="odd_dhx_skyblue cursorStyle" id="tr${item_index}" onclick="changeContentRule(this, '${item[2]}')" onmousedown="this.className='odd_dhx_skyblue rowSelected';">
										<th><label>${item[0]}</label></th>
										<th><label>${item[1]}</label></th>
									</tr>
							</#list>
						</tbody>
					</table>
				</td>
				<td>
					<table width="100%">
						<tbody>
							<tr class="odd_dhx_skyblue" width="100%">
								<th><label><@i18n 'acc_dev_contenRule'/></label></th>
							</tr>
							<tr>
								<th style="max-width:none; min-width:none;"><textarea rows="14" cols="21" id="contentRuleId" class="zk-content-bg-color zk-border-color-gray" style="background-color: #F5F2EF;" readonly></textarea></th>
							</tr>
							<tr><th><lable class="gray"><@i18n 'acc_dev_ruleContentTip'/></lable></th></tr>
                            <tr>
                                <th class="zk-msg-warn">
                                    <span class="warningImage" style="margin-top: 2px;"></span>
                                    <table>
                                        <tr rowspan="2"><@i18n 'acc_common_linkageAndApbTip'/></tr></br>
                                    </table>
                                </th>
                            </tr>
						</tbody>
					</table>
				</td>
			</tr>
		</table>
	</div>
</form>
</#macro>
<#macro buttonContent>
	<button class='button-form' onclick="DhxCommon.closeWindow()"><@i18n 'common_op_close'/></button>
</#macro>
