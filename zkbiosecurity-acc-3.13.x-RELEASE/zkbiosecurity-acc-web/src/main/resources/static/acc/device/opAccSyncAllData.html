<script type="text/javascript">
    var devNameStr = "${(devicesName)!}";
    var disabledDevNameStr = "${(disabledDevName)!}";
    var devAlias = "";
    var alias = new Array();
    if(devNameStr == "" || devNameStr.length == 0)
    {
        $("#setAccSyncAllData${uuid}ConfirmButton").attr("disabled", true);
    }
    else
    {
        var devArr = devNameStr.split(";");
        for(var i=0; i<devArr.length; i++)
        {
            var currDev = devArr[i];
            var devsuffix = currDev.split(":")[1];
            //var devSuffixArr = devsuffix.substring(0,devsuffix.length-1).split(",");
            var devSuffixArr = devsuffix.split(",");
            var ids ="";
            var combDevName ="" ;
            devAlias ="" ;
            for(var j=1; j<=devSuffixArr.length; j++)
            {
                var temp = devSuffixArr[j-1].split("&");
                ids += temp[1] + ",";
                combDevName += temp[0] + ",";
                devAlias += temp[0] + ",";
            }
            ids = ids.substring(0,ids.length-1);
            combDevName = combDevName.substring(0, combDevName.length-1);
            devAlias = devAlias.substring(0, devAlias.length-1);
            var devTypeName = currDev.split(":")[0].split("-");
            $("<div style='padding-left: 10px;' id='" + devTypeName[0] + "' value='" + ids + "'>" +
                "<span id='span${uuid}" + i + "'></span>" +
                "<span style='font-weight: bold;'>"+ devTypeName[1] + "</span>" + " : " + combDevName + "</div>").appendTo("#selectDev");
            loadUIToDiv("input", "#span${uuid}" + i, {
                useInputReq:true,
                hideLabel:true,
                type:"radio",
                id:"devRadioId" + i,
                name:"devRadio",
                value:devTypeName[0],
                defaultChecked:i==0,
                dynamicCallback: "loadDevNameCallback(" + i + ")"
            });
            alias[devTypeName[0]] = devAlias;
        }
    }
    if(disabledDevNameStr != "" && disabledDevNameStr.length > 0)
    {
        $("<div class='zk-msg-error' style='padding-left: 10px; margin-top: 10px;'><b><@i18n 'acc_dev_disabled'/></b><br/>" + disabledDevNameStr + "</div>").appendTo("#selectDev");
    }

    var clickMethod = function(){
        $("#selectDevFunction").empty();
        var devType = "";
        var devIds = "";
        if ($("input[id^='devRadio']:checked").val()) {
            devType = $("input[id^='devRadio']:checked").val();
            var devIds = $("#"+devType).attr("value");//同一类型的设备ID
            $("#devIds").val(devIds);
        }
        $.ajax({
            type: "POST",
            url: 'accDevice.do?syncAllDataByDevType',
            data: {"devId":devIds,"devType":devType},
            dataType: "json",
            async: false,
            success: function(retData)
            {
                var html = "";
                if(retData.data.length == 0)
                {
                    html = "<table width='750px'><tr><td width='600px'><span class='warningImage'></span><span class='warningColor'><@i18n 'acc_dev_noOption'/></span></td></tr></table>"
                }
                else
                {
                    var selectAll = "<@i18n 'common_tree_unselectAll'/>";
                    html = "<table width='780px'><tr><td width='20%'><div><input class='button-form' type='button' style='width:138px;' id='checkAll' name='checkAll' value='" + selectAll + "'/></div></td><td><div><table><tr>";
                    for (var i = 0, len = retData.data.length; i < len; i++)
                    {
                        var funcName = I18n.getValue("acc_dev_" + retData.data[i]);// 名称
                        html = html + "<td width='25%'><span id='optCheckBox${uuid}"+i+"'></span>" + funcName + "</td >";
                        loadUIToDiv("input", "#optCheckBox${uuid}"+i, {
                            useInputReq:true,
                            hideLabel:true,
                            type:"checkbox",
                            id:retData.data[i],
                            name:"optBox",
                            value:retData.data[i],
                            trueValue: retData.data[i],
                            eventCheck:true
                        });
                        if ((i + 1) % 3 == 0)
                        {
                            html = html + "</tr><tr>";
                        }
                    }
                }
                $("#selectDevFunction").append(html);
            }
        });
        // 延时加载，避免上面loadUIToDiv,方法内取不到元素数据
        window.setTimeout(function () {
            // 取隐藏域name为optbox同级相邻的checkbox
            var $optBox = $("input[name='optBox']").siblings("input[type=checkbox]");
            var boxNum = $optBox.length;
            var $optBoxChecked = $("input[name='optBox']").siblings("input[type=checkbox]:checked");
            $("#setAccSyncAllData${uuid}ConfirmButton").attr("disabled", $optBoxChecked.length == 0 ? true : false);
            $("#checkAll").click(function() {
                //设置相反的选项
                $("input[name='optBox']").siblings("input[type=checkbox]").attr("checked",$optBox.length != $("input[name='optBox']").siblings("input[type=checkbox]:checked").length ? true : false);
                $("#setAccSyncAllData${uuid}ConfirmButton").attr("disabled", $("input[name='optBox']").siblings("input[type=checkbox]:checked").length == 0 ? true : false);
                var len = $("input[name='optBox']").siblings("input[type=checkbox]:checked").length;
                if(len == boxNum)
                {
                    $("#checkAll").attr("value", "<@i18n 'common_tree_unselectAll'/>");
                }
                else
                {
                    $("#checkAll").attr("value", "<@i18n 'common_tree_selectAll'/>");
                }
            });
            $('input[name="optBox"]').siblings("input[type=checkbox]").click(function(){
                $("#setAccSyncAllData${uuid}ConfirmButton").attr("disabled", $("input[name='optBox']").siblings("input[type=checkbox]:checked").length == 0 ? true : false);
                if ($("#accLevel").is(":checked"))
                {
                    $("#timeZoneAndHoliday").attr("checked",true);
                }
                var len = $("input[name='optBox']").siblings("input[type=checkbox]:checked").length;
                if(len == boxNum)
                {
                    $("#checkAll").attr("value", "<@i18n 'common_tree_unselectAll'/>");
                }
                else
                {
                    $("#checkAll").attr("value", "<@i18n 'common_tree_selectAll'/>");
                }
            })
        }, 500);
    }

    /** 默认选中第一台设备，第一台设备加载出来后加载底下选项 */
    function loadDevNameCallback(index) {
        if (index == 0) {
            clickMethod();
        }
        // 先解绑后绑定,修复多次绑定导致多次调用点击事件
        $('input[name="devRadio"]').unbind("click").click(clickMethod);
    }

    function setAccSyncAllData() {
        var devType = $("input[id^='devRadio']:checked").val();
        var aliasStr = alias[devType];
        var optBoxValue = "";
        $("input[name='optBox']").each(function (i, obj) {
            if ($(this).siblings("input[type=checkbox]:checked").length > 0) {
                optBoxValue = optBoxValue + obj.value + ",";
            }
        });
        if ($("#clearData").attr("checked") == "checked") {
            optBoxValue += $("#clearData")[0].value + ",";
        }
        if(optBoxValue != "")
        {
            optBoxValue = optBoxValue.substring(0, optBoxValue.length - 1);
        }
        var submitFun = function(){
            //操作处理线程
            $("#setAccSyncAllData${uuid}confirmButton").attr("disabled", true);
            //操作处理进度
            ZKUI.Process.get("setAccSyncAllData${uuid}").options.dealPath="/accDevice.do?syncAllData&devIds=" + $("#devIds").val() + "&optBoxValue=" + optBoxValue + "&alias=" + encodeURIComponent(aliasStr);
            ZKUI.Process.get("setAccSyncAllData${uuid}").beginProgress();
        }
        execAccConfirm($("#devIds").val(), submitFun);
    }

    // 打开删除设备中数据选项设置
    function openSyncClearDataWindow() {
		var opts = {
            path: "skip.do?page=acc_device_opAccSyncClearData",
            width: 380,
            height: 200,
            title: "<@i18n 'common_prompt_title'/>",
            gridName: "gridbox"
        };
        DhxCommon.createWindow(opts);
    }

    function skipToAccDeviceMonitor() {
        DhxCommon.closeWindow();
        openMenuItem("accDeviceMonitor.do", "Acc");
    }
</script>

<@ZKUI.Process id="setAccSyncAllData${uuid}" dealPath="" type="single" confirmText="common_dev_synchronize" onSure="openSyncClearDataWindow">
<!-- content内容标签必须指定name属性，用来指定嵌套位置 -->
    <@ZKUI.Content name="op">
        <div style="padding: 0px 10px;">
            <@i18n 'common_dev_selectedDev'/>
            <div class="access-infoDiv zk-content-bg-color zk-border-color" style="border: solid 1px green;background-color:white;width:99%;height: 100px; overflow:auto">
                <div name="selectDev" id="selectDev"></div>
            </div>
            <div style="display: none;">
                <input type='checkbox' id='clearData' value='clearData'/>
            </div>
            <input type="hidden" name="devIds" value="${(devIds)!}" id="devIds"/>
            <div name="selectDevFunction" id="selectDevFunction"></div>
        </div>
    </@ZKUI.Content>
</@ZKUI.Process>
