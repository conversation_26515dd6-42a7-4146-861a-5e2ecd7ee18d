<#assign formId="accDevSetTimeZoneForm${uuid}">
<#include "/public/template/editTemplate.html">
<#macro editMain></#macro>
<@ZKUI.Process id="accDevSetTimeZone${uuid}" type="single" confirmText="acc_dev_start" onSure="accDevSetTimeZone" onFinish="finishProgress">
<!-- content内容标签必须指定name属性，用来指定嵌套位置 -->
	<@ZKUI.Content name="op">
	<script type="text/javascript" src="/js/accTimeZoneSelectDsTime.js"></script>
	<script type="text/javascript">
		$(function(){
			$("#${formId}").validate({
				debug : true,
				rules :
				{
					"timeZone" :
					{
						required : true
					}
				},
				submitHandler: function(form)
				{
					var submitFun = function(){
						$('#${formId}').ajaxSubmit({
							async : true,
							dataType : 'json',
							data: {
								clientId: ZKUI.Process.get("accDevSetTimeZone${uuid}").options.clientId,
								alias : dev<PERSON><PERSON><PERSON>
							},
							success: function(data)
							{
								if (data == undefined)
								{
									openMessage(msgType.error);
								}
							},
							error: function(XMLHttpRequest, textStatus, errorThrown)
							{
								messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
							}
						});
                        ZKUI.Process.get("accDevSetTimeZone${uuid}").beginProgress(true);
					};
					execAccConfirm($("#devIds").val(), submitFun);
				}
			});
		});
		var devNameStr = "${(devicesName)!}";
		var devAlias = "";
		var disabledDevNameStr = "${(disabledDevName)!}";
		var noSupportDevNameStr = "${(noSupportDevName)!}";
		var warnInfoDevNameStr = "${(warnInfoDevName)!}";
		if(devNameStr == "" || devNameStr.length == 0)
		{
            $("#accDevSetTimeZone${uuid}ConfirmButton").attr("disabled", true);
		}
		else
		{
			var devArr = devNameStr.split(";");
			for(var i=0; i<devArr.length; i++)
			{
				var currDev = devArr[i];
				var devsuffix = currDev.split(":")[1];
				var devSuffixArr = devsuffix.substring(0,devsuffix.length).split(",");
				var ids ="";
				var combDevName ="" ;
				devAlias ="" ;
				// 夏令时
				var dsTimeId = "";
				var dsTimeName = "";
				for(var j=1; j<=devSuffixArr.length; j++)
				{
					var temp = devSuffixArr[j-1].split("&");
					ids += temp[1] + ",";
					combDevName += temp[0] + ",";
                    devAlias += temp[0] + ",";
                    dsTimeId += temp[2] + ",";
                    dsTimeName += temp[3] + ",";
					if(j % 5 == 0)
					{
						combDevName += "</br>";
					}
				}
				ids = ids.substring(0,ids.length-1);
				combDevName = combDevName.substring(0, combDevName.length-1);
                devAlias = devAlias.substring(0, devAlias.length-1);
				var devTypeName = currDev.split(":")[0].split("_");
				dsTimeId = dsTimeId.substring(0, dsTimeId.length - 1);
				dsTimeName = dsTimeName.substring(0, dsTimeName.length - 1);
				$("<div style='padding-left: 10px;' id='" + devTypeName[0] + "' value='" + ids + "'>" +
				 	"<span id='accSetTimeZoneDevRadioSpan"+ i +"'></span>" +
				 	"<span style='font-weight: bold;'>"+ devTypeName[0] + "</span>" + " : " + combDevName + "</div>").appendTo("#selectDev");
				 	loadUIToDiv("input", "#accSetTimeZoneDevRadioSpan" + i, {
                        useInputReq:true,
                        hideLabel:true,
                        type:"radio",
                        id:"devRadioId" + i,
                        devIdsAttr:ids,
                        name:"devRadio",
                        value:devTypeName[1],
                        dsTimeId:dsTimeId,
                        dsTimeName:dsTimeName,
                        defaultChecked:i==0
                    });
			}
		}
		if(disabledDevNameStr != "" && disabledDevNameStr.length > 0)
		{
			$("<div class='zk-msg-error' style='padding-left: 10px; margin-top: 10px;'><b><@i18n 'acc_dev_disabled'/></b><br/>" + disabledDevNameStr + "</div>").appendTo("#selectDev");
		}
		if(noSupportDevNameStr != "" && noSupportDevNameStr.length > 0)
		{
			$("<div class='zk-msg-error' style='padding-left: 10px; margin-top: 10px;'><b><@i18n 'acc_dev_noSupport'/></b><br/>" + noSupportDevNameStr + "</div>").appendTo("#selectDev");
		}

		if(warnInfoDevNameStr != "" && warnInfoDevNameStr.length > 0)
		{
			$("<div style='padding-left: 10px; margin-top: 10px; color: #E57A14;'><b><@i18n 'acc_dev_setTimeZoneWarnTip'/></b><br/>" + warnInfoDevNameStr + "</div>").appendTo("#selectDev");
		}

        function accDevSetTimeZone()
        {
        	$("#${formId}").submit();
        }
		// 延时加载，避免上面loadUIToDiv,方法内取不到元素数据
    	window.setTimeout(function () {
			//对选择的设备进行操作  主要是将选择的设备的Id赋值给ids
			$("input:radio").each(function(){
				$(this).click(function(){
					var val = $(this).val();
					var id = $(this).attr("id");
					var devIdsAttr = ZKUI.Input.get(id).options.devIdsAttr;
					$("#devIds").attr("value", devIdsAttr);
					ZKUI.Combo.get("accTimeZone${uuid}").combo.setComboValue($(this).val());
					changeAccDsTime();
				});
			});
		}, 100);
		//初始化
		function initAccTimeZone() {
			var val = $('input:radio[name="devRadio"]:checked').val();
			$('input:radio[name="devRadio"]:checked').click();
			changeTimeZone(val);
		}
		setTimeout(function () {
			initAccTimeZone();
		}, 100);

		/** 初始化夏令时 */
		function changeAccDsTime() {
			setTimeout(function () {
				var id = $('input:radio[name="devRadio"]:checked').attr("id");
				var dsTimeId = ZKUI.Input.get(id).options.dsTimeId;
				var dsTimeName = ZKUI.Input.get(id).options.dsTimeName;
				selectDsTimeCallBack(dsTimeId, dsTimeName);
			}, 200);
		}

		function selectDsTimeCallBack(id, name) {
			if(id != null && id.trim() != "" && name != null && name.trim() != "") {
				$("#accDSTimeId${uuid}").val(id);
				$("#accDSTimeName${uuid}").val(name);
			} else {
				$("#accDSTimeId${uuid}").val("");
				$("#accDSTimeName${uuid}").val("<@i18n 'acc_dsTimeUtc_none'/>");
			}
		}


	</script>

	<div style="padding: 0px 10px;">
		<div style="padding-bottom: 2px;"><label><@i18n 'acc_dev_selectedTZ'/></label></div>
		<div class="access-infoDiv zk-content-bg-color zk-border-color" style="border: solid 1px green;background-color:white;width:99%;height: 100px; overflow:auto">
			 <div name="selectDev" id="selectDev"></div>
		</div>
		<form id="${formId}" action="/accDevice.do?setTimeZoneAndDsTime" method="post" enctype="multipart/form-data" >
			<input type="hidden" name="devIds" value="${(devIds)!}" id="devIds" />
			<input type="hidden" id="supportMinute" value="${supportMinute!false}">
			<table class="tableStyle">
				<tr>
					<th>
						<label><@i18n 'acc_dev_timeZone'/></label>
					</th>
					<td>
						<@ZKUI.Combo name="timeZone" id="accTimeZone${uuid}" width="150" empty="true" hideLabel="true" onChange="changeTimeZone" title="acc_dev_timeZone" key="systemTimeZone" value="${timeZoneKey}" readonly="true"/>
					</td>
				</tr>
				<tr>
					<th>
						<label><@i18n 'acc_dev_setDstime'/></label>
					</th>
					<td>
						<input type="hidden" name="accDSTimeId" id="accDSTimeId${uuid}">
						<div id="noUtcDiv" style="display: none">
							<label><@i18n 'acc_dsTimeUtc_AreaNone'/></label>
						</div>
						<div id="selectUtcDiv" onclick="selectDsTime()" style="display: none">
							<input type="text" style="width: 144px" id="accDSTimeName${uuid}" disabled="disabled">
							<span class="icv-daylightSavingSetting"></span>
						</div>
					</td>
				</tr>
			</table>
		</form>
	</div>
	</@ZKUI.Content>
</@ZKUI.Process>