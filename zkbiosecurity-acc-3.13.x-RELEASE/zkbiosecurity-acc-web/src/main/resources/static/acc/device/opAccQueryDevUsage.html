<#include "/public/template/editTemplate.html">
<#macro editContent>
<style>
td {
 align: center;
}

.baseTab {
	overflow-y: auto;
	overflow-x: hidden;
	height: 270px;
	padding-left: 15px;
	padding-right: 15px
}
</style>
<form id='${formId}'>
	<div id="baseTab" class="baseTab">
		<table id ="showTable" class="showTable">
			<tbody>
				<tr>
					<td width="101px"><span class="icv-ico_device" style="font-size: 24px; margin-left: 2px;" onmousemove="imgMouseMove(event,'common_dev_name')" title="<@i18n 'common_dev_name'/>"></span></td>
					<td width="80px"><span class="acc_queryDevUsageIconSize icv-ico_person" onmousemove="imgMouseMove(event,'pers_common_personCount')" title="<@i18n 'pers_common_personCount'/>"></span></td>
					<!--<td width="80px"><span class="acc_queryDevUsageIconSize icv-ico_biophoto" onmousemove="imgMouseMove(event,'pers_person_cropFaceCount')" title="<@i18n 'pers_person_cropFaceCount'/>"></span></td>-->
					<td width="148px" colspan="2"><span class="acc_queryDevUsageIconSize icv-ico_fingerprint" onmousemove="imgMouseMove(event,'pers_person_templateCount')" title="<@i18n 'pers_person_templateCount'/>"></span></td>
					<td width="148px" colspan="2"><span class="acc_queryDevUsageIconSize icv-ico_finger_vein" onmousemove="imgMouseMove(event,'pers_person_VeinTemplateCount')" title="<@i18n 'pers_person_VeinTemplateCount'/>"></span></td>
					<td width="148px" colspan="2"><span class="acc_queryDevUsageIconSize icv-ico_metric" onmousemove="imgMouseMove(event,'pers_person_faceTemplateCount')" title="<@i18n 'pers_person_faceTemplateCount'/>"></span></td>
					<td width="148px" colspan="2"><span class="acc_queryDevUsageIconSize icv-ico_palm" onmousemove="imgMouseMove(event,'pers_person_palmTemplateCount')" title="<@i18n 'pers_person_palmTemplateCount'/>"></span></td>
                    <td width="148px" colspan="2"><span class="acc_queryDevUsageIconSize icv-ico_iris" onmousemove="imgMouseMove(event,'pers_person_irisCount')" title="<@i18n 'pers_person_irisCount'/>"></span></td>
					<!--<td width="148px" colspan="2"><span class="acc_queryDevUsageIconSize icv-ico_face_biophoto" onmousemove="imgMouseMove(event,'pers_person_faceBiodataCount')" title="<@i18n 'pers_person_faceBiodataCount'/>"></span></td>-->
					<td><input type="button" class="button-form viewButton" name="getAllData" value="<@i18n 'acc_dev_getAll'/>" idAttr="${devIds!}" onclick="getDevUsage(this);" ${getAll!}/></td>
				</tr>
				<#list retList as item>
					<tr>
						<td width="101px"><label>${item[0]}</label></td>
						<td><label id="user${item[5]}"></label></td>
						<!--<td><label id="biophoto${item[7]}"></label></td>-->
						<td width="78px"><label id="finger${item[5]}"></label></td>
						<td width="70px"><label id="fingerV${item[5]}"></label></td>
						<td width="78px"><label id="fv${item[5]}"></label></td>
						<td width="70px"><label id="fvV${item[5]}"></label></td>
						<td width="78px"><label id="face${item[5]}"></label></td>
						<td width="70px"><label id="faceV${item[5]}"></label></td>
						<td width="78px"><label id="palm${item[5]}"></label></td>
						<td width="70px"><label id="palmV${item[5]}"></label></td>
                        <td width="70px"><label id="iris${item[5]}"></label></td>
                        <td width="70px"><label id="irisV${item[5]}"></label></td>
						<!--<td width="78px"><label id="faceBiodata${item[7]}"></label></td>
						<td width="70px"><label id="faceBiodataV${item[7]}"></label></td>-->
						<td><input type="button" class="button-form viewButton" name="getDevData" value="<@i18n 'acc_dev_get'/>" idAttr="${item[5]}" onclick="getDevUsage(this);" ${item[6]}/></td>
					</tr>
				</#list>
			</tbody>
		</table>
	</div>
</form>
<div style="text-align: left;padding-left:20px;"><span class="warningImage"></span> <span class="warningColor"><@i18n 'acc_dev_softwareData'/></span></div>
<script type="text/javascript">
function getDevUsage(usageObj)
{
	var devId = $(usageObj).attr("idAttr");
	var idsArr = devId.split(",");
	if("getAllData" == $(usageObj).attr("name"))
	{
		$("#${formId} #baseTab input[name='getAllData']").attr("disabled","disabled");
	}
    for(var i=0;i<idsArr.length;i++)
	{
		devId = idsArr[i];
		$("#${formId} #user"+idsArr[i]).html("<img src='${base}/images/base_wait.gif'/>");
		$("#${formId} #finger"+idsArr[i]).html("<img src='${base}/images/base_wait.gif'/>");
		$("#${formId} #fv"+idsArr[i]).html("<img src='${base}/images/base_wait.gif'/>");
		$("#${formId} #face"+idsArr[i]).html("<img src='${base}/images/base_wait.gif'/>");
		//$("#${formId} #biophoto"+idsArr[i]).html("<img src='${base}/images/base_wait.gif'/>");
		$("#${formId} #palm"+idsArr[i]).html("<img src='${base}/images/base_wait.gif'/>");
		$("#${formId} #iris"+idsArr[i]).html("<img src='${base}/images/base_wait.gif'/>");
		//$("#${formId} #faceBiodata"+idsArr[i]).html("<img src='${base}/images/base_wait.gif'/>");
		$("#${formId} #fingerV"+idsArr[i]).html("<img src='${base}/images/base_wait.gif'/>");
		$("#${formId} #fvV"+idsArr[i]).html("<img src='${base}/images/base_wait.gif'/>");
		$("#${formId} #faceV"+idsArr[i]).html("<img src='${base}/images/base_wait.gif'/>");
		$("#${formId} #palmV"+idsArr[i]).html("<img src='${base}/images/base_wait.gif'/>");
		$("#${formId} #irisV"+idsArr[i]).html("<img src='${base}/images/base_wait.gif'/>");
		//$("#${formId} #faceBiodataV"+idsArr[i]).html("<img src='${base}/images/base_wait.gif'/>");
		$("#${formId} #baseTab input[idattr="+devId+"]").attr("disabled","disabled");
		$.ajax({
		type: "post",
		url: "accDevice.do?getDevUsage",
		dataType: "json",
		async :true,
		timeout : 60000,
		data:
		{
			'devId' : devId
		},
		success: function (value)
	    {
            var data = value.data;
	    	var retId = data["id"];
	    	var title = I18n.getValue("acc_rtMonitor_nonsupport");
                if(data["user"] == undefined || data["user"][retId] == undefined)
                {
                    $("#${formId} #user"+retId).html("<@i18n 'common_failed'/>");
                }
                else
                {
                    $("#${formId} #user"+retId).html(data["user"][retId]);
                }
                if(data["finger"] == undefined || data["finger"][retId] == undefined)
                {
                    $("#${formId} #finger"+retId).html("<@i18n 'common_failed'/>");
					$("#${formId} #fingerV"+retId).html("<@i18n 'common_failed'/>");
                }
                else if (data["finger"][retId] == "false")
                {
                    var html = "<span class='icv-close' onmousemove='imgMouseMove(event,null)' title='" + title +"'></span>";
                    $("#${formId} #finger"+retId).html(html);
					$("#${formId} #fingerV"+retId).html(html);
                }
                else
                {
                    $("#${formId} #finger"+retId).html(data["finger"][retId]);
					$("#${formId} #fingerV"+retId).html(data["finger"]["fingerVersion"]);
                }
                if(data["fv"] == undefined || data["fv"][retId] == undefined)
                {
                    $("#${formId} #fv"+retId).html("<@i18n 'common_failed'/>");
					$("#${formId} #fvV"+retId).html("<@i18n 'common_failed'/>");
                }
                else if (data["fv"][retId] == "false")
                {
                    var html = "<span class='icv-close' onmousemove='imgMouseMove(event, null)' title='" + title + "'></span>";
                    $("#${formId} #fv"+retId).html(html);
					$("#${formId} #fvV"+retId).html(html);
                }
                else
                {
                    $("#${formId} #fv"+retId).html(data["fv"][retId]);
					$("#${formId} #fvV"+retId).html(data["fv"]["fvVersion"]);
                }

                if(data["face"] == undefined || data["face"][retId] == undefined)
                {
                    $("#${formId} #face"+retId).html("<@i18n 'common_failed'/>");
					$("#${formId} #faceV"+retId).html("<@i18n 'common_failed'/>");
                }
                else if (data["face"][retId] == "false")
                {
                    var html = "<span class='icv-close' onmousemove='imgMouseMove(event, null)' title='" + title + "'></span>";
                    $("#${formId} #face"+retId).html(html);
					$("#${formId} #faceV"+retId).html(html);
                }
                else
                {
                    $("#${formId} #face"+retId).html(data["face"][retId]);
					$("#${formId} #faceV"+retId).html(data["face"]["faceVersion"]);
                }

<!--				if(data["biophoto"] == undefined || data["biophoto"][retId] == undefined)-->
<!--				{-->
<!--					$("#${formId} #biophoto"+retId).html("<@i18n 'common_failed'/>");-->
<!--				}-->
<!--				else if (data["biophoto"][retId] == "false")-->
<!--				{-->
<!--					var html = "<span class='icv-close' onmousemove='imgMouseMove(event, null)' title='" + title + "'></span>";-->
<!--					$("#${formId} #biophoto"+retId).html(html);-->
<!--				}-->
<!--				else-->
<!--				{-->
<!--					$("#${formId} #biophoto"+retId).html(data["biophoto"][retId]);-->
<!--				}-->

				if(data["palm"] == undefined || data["palm"][retId] == undefined)
				{
					$("#${formId} #palm"+retId).html("<@i18n 'common_failed'/>");
					$("#${formId} #palmV"+retId).html("<@i18n 'common_failed'/>");
				}
				else if (data["palm"][retId] == "false")
				{
					var html = "<span class='icv-close' onmousemove='imgMouseMove(event, null)' title='" + title + "'></span>";
					$("#${formId} #palm"+retId).html(html);
					$("#${formId} #palmV"+retId).html(html);
				}
				else
				{
					$("#${formId} #palm"+retId).html(data["palm"][retId]);
					$("#${formId} #palmV"+retId).html(data["palm"]["pvVersion"]);
				}
				if(data["iris"] == undefined || data["iris"][retId] == undefined)
                {
                    $("#${formId} #iris"+retId).html("<@i18n 'common_failed'/>");
					$("#${formId} #irisV"+retId).html("<@i18n 'common_failed'/>");
                }
                else if (data["iris"][retId] == "false")
                {
                    var html = "<span class='icv-close' onmousemove='imgMouseMove(event,null)' title='" + title +"'></span>";
                    $("#${formId} #iris"+retId).html(html);
					$("#${formId} #irisV"+retId).html(html);
                }
                else
                {
                    $("#${formId} #iris"+retId).html(data["iris"][retId]);
					$("#${formId} #irisV"+retId).html(data["iris"]["irisVersion"]);
                }

<!--				if(data["faceBiodata"] == undefined || data["faceBiodata"][retId] == undefined)-->
<!--				{-->
<!--					$("#${formId} #faceBiodata"+retId).html("<@i18n 'common_failed'/>");-->
<!--					$("#${formId} #faceBiodataV"+retId).html("<@i18n 'common_failed'/>");-->
<!--				}-->
<!--				else if (data["faceBiodata"][retId] == "false")-->
<!--				{-->
<!--					var html = "<span class='icv-close' onmousemove='imgMouseMove(event, null)' title='" + title + "'></span>";-->
<!--					$("#${formId} #faceBiodata"+retId).html(html);-->
<!--					$("#${formId} #faceBiodataV"+retId).html(html);-->
<!--				}-->
<!--				else-->
<!--				{-->
<!--					$("#${formId} #faceBiodata"+retId).html(data["faceBiodata"][retId]);-->
<!--					$("#${formId} #faceBiodataV"+retId).html(data["faceBiodata"]["vislightFaceVersion"]);-->
<!--				}-->
			$("#${formId} #baseTab input[idattr="+retId+"]").removeAttr("disabled");
			$("#${formId} #baseTab input[name='getAllData']").removeAttr("disabled");
    	},	
    	error: function (XMLHttpRequest, textStatus, errorThrown) 
    	{
    		id = /devIds=(.*)&?/.exec(this.data)[1];
			$("#${formId} #baseTab input[name='getAllData']").removeAttr("disabled");
			$("#${formId} #baseTab input[idattr="+id+"]").removeAttr("disabled");
			$("#${formId} #user"+id).html("<@i18n 'common_failed'/>");
			$("#${formId} #finger"+id).html("<@i18n 'common_failed'/>");
			$("#${formId} #fv"+id).html("<@i18n 'common_failed'/>");
			$("#${formId} #face"+id).html("<@i18n 'common_failed'/>");
			//$("#${formId} #biophoto"+id).html("<@i18n 'common_failed'/>");
			$("#${formId} #palm"+id).html("<@i18n 'common_failed'/>");
			$("#${formId} #iris"+id).html("<@i18n 'common_failed'/>");
			//$("#${formId} #faceBiodata"+id).html("<@i18n 'common_failed'/>");
    	}
		});
		}	
	}
	function imgMouseMove(event,title){
	    event.stopPropagation();//停止事件传播
		if(title == null)
		{
            event.currentTarget.title = I18n.getValue("acc_rtMonitor_nonsupport");
        }
        else
		{
            event.currentTarget.title= I18n.getValue(title);//重设title
        }
	}

</script>
</#macro>
<#macro buttonContent>
	<button class='button-form' onclick="DhxCommon.closeWindow()"><@i18n 'common_op_close'/><tton>
</#macro>
