<#assign editPage="true">
<#include "/public/template/editTemplate.html">
<#macro editContent>
<#if ((supportDst)?exists)>
<script type="text/javascript" src="/js/accTimeZoneSelectDsTime.js"></script>
</#if>
<script type="text/javascript">
$("#${formId}").validate({
	debug : true,
	rules :
	{
		"ipAddress":
		{
            overRemote : [ "accDevice.do?isExistIpAddress", "${(tempAccDevice.ipAddress)!}" ],
            IpValid : true
		},
		"alias" :
		{
			required: true,
			unInputChar:true,
            overRemote : [ "accDevice.do?isExistAlias", "${(tempAccDevice.alias)!}" ]
		},
		"authAreaId":
		{
			required: true
		},
		"comPort" :
		{
			digits: true
		},
		"comAddress" :
		{
			digits: true,
			range: [1,63]
		},
		"baudrate" :
		{
			digits: true
		},
		"timeZone":
		{
			required:true
		},
		"iconType":
		{
			required:true
		}
	},
	submitHandler : function()
	{
	    //	    将制表符替换成空格
		var tempAlias = $("#${formId} input[id^='idAlias']").val().replace(/\t/g," ");
		$("#${formId} input[id^='idAlias']").val(tempAlias);
		$("#${formId} input[id^='idCommType']").each(function(){
			$(this).attr("disabled", false);
		});
		$("#${formId} #idAcpanelType").attr("disabled", false);
		$("#${formId} #idComAddress").attr("disabled", false);
		$("#idBaudrate").attr("disabled", false);
		$("#${formId} #idSn").attr("disabled", false);
		$("#${formId} #idFourToTwo").attr("disabled", false);
		if ($("#${formId} #idFourToTwo").attr("checked") == "checked")
		{
			$("#${formId} #idFourToTwo").val(true);
		}
		else
		{
			$("#${formId} #idFourToTwo").val(false);
		}
		addAccDevBeforeSubmit(function(){
			if($("#${formId} #idModelPk").val() == "")//新增时才需要判断固件版本是否需要升级
			{
				addAccDevAfterSubmit(function(){
					//处理不添加的权限组
					if ($("#${formId} #level").hasClass("gray")) {
						ZKUI.Combo.get("idAccLevel").setValue("");
					}
					var newIp = ZKUI.IP.get("ipAddress").ip.getValue();
					<@submitHandler />
				});
			}
			else
			{
			    <@submitHandler />
			}
		});
	}
});

/*$("#${formId} #modeSelect").change(function() {
	var value = $("#${formId} #modeSelect").val();
	$("#${formId} #masterSlave").attr("value",value);
});*/
</script>
<style>
    .rs485p0{
        background : url('${base}/images/rs485/rs485down.png') no-repeat scroll center;
        background-size:13px 31px;
        width:16px; 
        height:70px; 
        float:${leftRTL!'left'};
        cursor:pointer;
        text-align:center;
        color: white;
        margin: 1px;
        position:relative;
    }
    
    .rs485p0 p{
        position: absolute;
        bottom: 0px;
        padding-${leftRTL!'left'}: 5px;
    }
    
    .rs485p1{
        background : url('${base}/images/rs485/rs485up.png') no-repeat scroll center;
        background-size:13px 31px;
        width:16px; 
        height:70px; 
        float:${leftRTL!'left'};
        cursor:pointer;
        text-align:center;
        color: white;
        margin: 1px;
        position:relative;
    }
    
    .rs485p1 p{
        position: absolute;
        bottom: 0px;
        padding-${leftRTL!'left'}: 5px;
    }
    
    .rs485NoClick{
        background : url('${base}/images/rs485/rs485down.png') no-repeat scroll center;
        background-size:13px 31px;
        width:16px; 
        height:70px; 
        float:${leftRTL!'left'};
        cursor:text;
        text-align:center;
        color: white;
        margin: 1px;
        position:relative;
    }
    
    .rs485NoClick p{
        position: absolute;
        bottom: 0px;
        padding-${leftRTL!'left'}: 5px;
    }
	.accDeviceTable .ts_box {
		margin-${leftRTL!'left'}: 80px;
		margin-top: -8px
	}
</style>
<form action="accDevice.do?save" method="post" onkeydown="if(event.keyCode==13){return false;}" id="${formId}" enctype="multipart/form-data" autocomplete="off">
	<input type="hidden" id="idModelPk" name="id" value="${(tempAccDevice.id)!}">
	<input type="hidden" id="devCommType" value="${(tempAccDevice.commType)!}" />
	<input type="hidden" name="acpanelOptions" id="idAcpanelOptions" />
	<table class="tableStyle accDeviceTable">
		<tr>
			<th colspan="4" align="${leftRTL!'left'}"></th>
		</tr>
		<tr>
			<td>
				<div class="div_box1" style="display: block; background-image: none;">
					<table class="tableStyle">
						<tbody>
							<tr>
								<th style="width: 48% !important;">
									<label><@i18n 'common_dev_name'/></label><span class="required">*</span>
								</th>
								<td>
									<input maxlength="20" type="text" name="alias" id="idAlias" value="${(tempAccDevice.alias)!}">
								</td>
							</tr>
							<tr >
								<th>
									<label><@i18n '是否推送异常记录'/></label>
								</th>
								<td>
									<@ZKUI.Combo width="148" empty="false" name="isPushException" hideLabel="true" value="${tempAccDevice.isPushException!}">
									<option value="1"><@i18n '推送'/></option>
									<option value="2"><@i18n '不推送'/></option>
								</@ZKUI.Combo>
			                </td>
		                    </tr>
							<tr id="trCommType">
								<th>
									<label><@i18n 'common_dev_commType'/></label><span class="required">*</span>
								</th>
								<td>
									<label style="display:inline-block"><!-- 阿拉伯风格设置的direction只对bolck级有效，inline级会有字符的特殊处理,导致错位 -->
										<#if (tempAccDevice.commType)?exists&&tempAccDevice.commType==1 || !(tempAccDevice.commType)?exists>
											<@ZKUI.Input hideLabel="true" type="radio" id="idCommType0" name="commType" value="1" checked="checked"/>
										<#else>
											<@ZKUI.Input hideLabel="true" type="radio" id="idCommType0" name="commType" value="1"/>
										</#if>TCP/IP
									</label>
									<label id="idHTTPCommType">
										<#if (tempAccDevice.commType)?exists&&tempAccDevice.commType==3>
											<@ZKUI.Input hideLabel="true" type="radio" id="idCommType2" name="commType" value="3" checked="checked"/>
										<#else>
											<@ZKUI.Input hideLabel="true" type="radio" id="idCommType2" name="commType" value="3"/>
										</#if>HTTP
									</label>
									<label>
										<#if (tempAccDevice.commType)?exists&&tempAccDevice.commType==2>
											<@ZKUI.Input hideLabel="true" type="radio" id="idCommType1" name="commType" value="2" checked="checked"/>
										<#else>
											<@ZKUI.Input hideLabel="true" type="radio" id="idCommType1" name="commType" value="2"/>
										</#if>RS485
									</label>
								</td>
							</tr>
							<tr id="trSn" style="display: none;">
								<th>
									<label><@i18n 'common_dev_sn'/></label><span class="required">*</span>
								</th>
								<td>
									<input type="text" maxlength="50" name="sn" value="${(tempAccDevice.sn)!}" id="idSn">
								</td>
							</tr>
							<tr class="idHttpTcp">
								<th>
									<label><@i18n 'common_ipAddress'/></label><span class="required">*</span>
								</th>
								<td>
									<div style="display:inherit;">
									<#if (tempAccDevice.id)?exists>
                                    	<@ZKUI.IP name="ipAddress" id="ipAddress" value="${(tempAccDevice.ipAddress)!}" disabled="true"/>
									<#else>
										<@ZKUI.IP name="ipAddress" id="ipAddress" value="${(tempAccDevice.ipAddress)!}"/>
									</#if>
				</div>
								</td>
							</tr>
							<tr class="idHttpTcp">
								<th>
									<label><@i18n 'common_dev_ipPort'/></label><span class="required">*</span>
								</th>
								<td>
									<input type="text" name="ipPort" maxlength="8" id="idIPPort" value="<#if (tempAccDevice.ipPort)?exists>${tempAccDevice.ipPort}<#else>4370</#if>" />
								</td>
							</tr>
							<!-- rs485 -->
							<tr class="idComm" style="display: none;">
								<th>
									<label><@i18n 'common_dev_serialPortNo'/></label><span class="required">*</span>
								</th>
								<td>
                                    <@ZKUI.Combo empty="false" id="idComPort" name="comPort" width="148" hideLabel="true" readonly="readonly" value="${(tempAccDevice.comPort)!}">
                                        <#list 1..255 as i>
                                            <option value="${i}">COM${i}</option>
                                        </#list>
                                    </@ZKUI.Combo>
								</td>
							</tr>
							<tr class="idComm" style="display: none;">
								<th>
									<label><@i18n 'common_dev_rs485Address'/></label><span class="required">*</span>
								</th>
								<td>
									<input type="text" name="comAddress" value="${(tempAccDevice.comAddress)!}" id="idComAddress" maxlength="2" onkeyup="paintRS485Pic(this.value)">
								</td>
							</tr>
                            <tr class="idComm" style="display: none;">
                                <th>
                                    <label><@i18n 'acc_dev_rs485AddrFigure'/></label>
                                </th>
                                <td>
                                    <div class="rs485-bg-color" style="width: 148px; height:67px; background-color: #464a4e;">
                                        <div id="rs1" class="rs485p0" onclick="updateRS485Addr(this)"><span style="font-size: 10px; word-wrap:normal !important; ">ON</span><p>1</p></div>
                                        <div id="rs2" class="rs485p0" onclick="updateRS485Addr(this)"><p>2</p></div>
                                        <div id="rs3" class="rs485p0" onclick="updateRS485Addr(this)"><p>3</p></div>
                                        <div id="rs4" class="rs485p0" onclick="updateRS485Addr(this)"><p>4</p></div>
                                        <div id="rs5" class="rs485p0" onclick="updateRS485Addr(this)"><p>5</p></div>
                                        <div id="rs6" class="rs485p0" onclick="updateRS485Addr(this)"><p>6</p></div>
                                        <div class="rs485NoClick"><p>7</p></div>
                                        <div class="rs485NoClick"><span style="font-size: 10px; word-wrap:normal !important;">KE</span><p>8</p></div>
                                    </div>
                                </td>
                            </tr>
							<tr class="idComm" style="display: none;">
								<th>
									<label><@i18n 'common_dev_baudrate'/></label><span class="required">*</span>
								</th>
								<td>
                                    <@ZKUI.Combo empty="false" id="idBaudrate" name="baudrate" width="148" hideLabel="true" readonly="readonly" value="${(tempAccDevice.baudrate)!}">
                                        <option value="38400">38400</option>
                                        <option value="19200">19200</option>
                                        <option value="57600">57600</option>
                                        <option value="115200">115200</option>
                                    </@ZKUI.Combo>
								</td>
							</tr>

							<tr id="trCommPwd" class="div_id_acc div_id_vid"
								style="display: table-row;">
								<th>
									<label for="idCommPwd">
                                        <@i18n 'common_dev_commPwd'/>
									</label>
								</th>
								<td>
									<input type="password" style="display:none"><!-- 为了去除谷歌浏览器记住密码 问题-->
									<input type="password" onkeydown="if(event.keyCode=='32'){return false;}" name="commPwd" value="${(tempAccDevice.commPwd)!}" id="idCommPwd">
									<input type="password" style="display:none"><!-- 为了去除谷歌浏览器记住密码 问题-->
								</td>
							</tr>
							<tr>
								<th>
									<label><@i18n 'acc_dev_iconType'/></label><span class="required">*</span>
								</th>
								<td>
									<@ZKUI.Combo width="148" empty="false" name="iconType" hideLabel="true" value="${tempAccDevice.iconType!1}">
										<option value="1"><@i18n 'acc_door_entity'/></option>
										<option value="2"><@i18n 'acc_dev_carGate'/></option>
										<option value="3"><@i18n 'acc_dev_channelGate'/></option>
									</@ZKUI.Combo>
								</td>
							</tr>
							<tr id="trAcpanelType" class="div_id_acc" style="">
								<th>
									<label>
                                        <@i18n 'acc_dev_acpType'/>
									</label>
								</th>
								<td>
                                    <@ZKUI.Combo width="148" empty="false" id="accAcpanelType" name="acpanelType" hideLabel="true" value="${tempAccDevice.acpanelType!1}" onChange="accAddAcpanelTypeChange">
										<option value="1"><@i18n 'acc_dev_oneDoorACP'/></option>
										<option value="2"><@i18n 'acc_dev_twoDoorACP'/></option>
										<option value="4"><@i18n 'acc_dev_fourDoorACP'/></option>
										<option value="5"><@i18n 'acc_dev_onDoorACD'/></option>
									</@ZKUI.Combo>
								</td>
							</tr>
                            <tr id="trModeSelect" style="display: none;">
                                <th>
                                    <label><@i18n 'common_dev_rs485ConfigMasterSlave'/></label>
                                </th>
                                <td>
									<@ZKUI.Combo width="148" empty="false" name="masterSlave" key="accMasterSlave" hideLabel="true" value="${masterSlave!}">
                                        <option value="0" selected="selected"><@i18n 'common_none'/></option>
                                        <option value="1"><@i18n 'acc_dev_master'/></option>
                                        <option value="2"><@i18n 'acc_dev_slave'/></option>
									</@ZKUI.Combo>
                                </td>
                            </tr>
							<tr>
								<th><label><@i18n 'base_area_entity'/></label><span class="required">*</span></th>
								<td>
                                    <@ZKUI.ComboTree autoFirst="true" type="radio" width="148" url="authArea.do?tree" value="${tempAccDevice.authAreaId!}" hideLabel="true" name="authAreaId"/>
                                </td>
							</tr>
							<tr id="trFourToTwo" class="div_id_acc" style="display: none;">
								<th>
									<label>
										<@i18n 'acc_dev_switchToTwoDoorTwoWay'/>
									</label>
								</th>
								<td>
									<#if (tempAccDevice.fourToTwo)?exists && tempAccDevice.fourToTwo==true>
										<@ZKUI.Input hideLabel="true" type="checkbox" id="idFourToTwo" name="fourToTwo" value="${(tempAccDevice.fourToTwo?string)!}" checked="checked"/>
									<#else>
										<@ZKUI.Input hideLabel="true" type="checkbox" id="idFourToTwo" name="fourToTwo" value="${(tempAccDevice.fourToTwo?string)!}"/>
									</#if>
								</td>
							</tr>
                            
                            <tr class="trEditHidden" id="level">
                                <th>
                                    <label><@i18n 'acc_dev_addLevel'/></label>
                                </th>
                                <td>
									<@ZKUI.Combo id="idAccLevel" empty="true" width="148" path="accLevel.do?getLevelList" name="levelId" readonly="readonly" hideLabel="true" onChange="accCheckIsAllowAddToLevel"/>
								</td>
                            </tr>
                            
							<tr class="trEditHidden" id="levelTip" style="display:none;">
								<td colspan="2">
									<div><span class='warningImage' style='margin-top:3px;'></span><span class='warningColor'><@i18n 'acc_dev_levelTip'/></span></div>
								</td>
							</tr>
                            <tr id="setWigendReader" style="display: none">
                                <th>
                                    <label><@i18n 'acc_dev_setWGReader'/></label>
                                </th>
                                <td style="display: flex;align-items: center;">
	                                <input type="text" id="readerName" readonly="readonly" style="cursor: pointer;" value="${readerName!}" placeholder="<@i18n 'acc_dev_selectReader'/>" onclick="accSelectSetReader();"/>
									<span class="icv-ic_del" onclick="accDevClearReader()" style="font-size: 15px;margin-left: 5px;cursor: pointer;"></span>
									<input type="hidden" name="wgReaderId" id="wigendReaderId" value="${(tempAccDevice.wgReaderId)!}"/>
                                </td>
                            </tr>
							<tr class="trEditHidden">
								<th>
									<label><@i18n 'common_dev_clearDevDataWhenAdding'/></label>
								</th>
								<td>
									<@ZKUI.Input hideLabel="true" type="checkbox" id="idClearAllData" name="clearAllData" trueValue="1" falseValue="0" eventCheck="true"/>
								</td>
							</tr>
							<#if ((supportTimeZone)?exists)>
							<tr>
								<th><label><@i18n 'acc_dev_timeZone'/></label><span class="required">*</span></th>
								<td>
									<@ZKUI.Combo width="148" empty="true" name="timeZone" onChange="changeTimeZone" path="baseDictionaryValue.do?selectList&key=systemTimeZone" hideLabel="true" value="${(tempAccDevice.timeZone)!}"/>
								</td>
							</tr>
							</#if>
							<#if ((supportDst)?exists)>
							<tr>
								<th>
									<label><@i18n 'acc_dev_setDstime'/></label>
								</th>
								<td>
									<input type="hidden" name="accDSTimeId" id="accDSTimeId${uuid}">
									<div id="noUtcDiv" style="display: none">
										<label><@i18n 'acc_dsTimeUtc_AreaNone'/></label>
									</div>
									<div id="selectUtcDiv" onclick="selectDsTime()" style="display: none">
										<input type="text" style="width: 144px" id="accDSTimeName${uuid}" disabled="disabled">
										<span class="icv-daylightSavingSetting"></span>
									</div>
								</td>
							</tr>
							</#if>
						</tbody>
					</table>
				</div>
			</td>
		</tr>
	</table>
    <div style="margin-top: 10px; margin-${leftRTL!'left'}: 15px; text-align: ${leftRTL!'left'}; " class="trEditHidden">
        <span class="warningImage"></span>
        <span class="warningColor"><@i18n 'common_dev_devClearDataTip'/></span>
    </div>
</form>
<script type="text/javascript">

	//初始化通信方式
	var comm_type = $("#${formId} #trCommType input[name='commType']:checked").val();
	if(comm_type == "1" || comm_type == "3")
	{
		$("#${formId} #idIPPort").rules("add", { required: true, digits: true});
		$("#${formId} .idHttpTcp").show();
		$("#${formId} #idComAddress").rules("remove");
		$("#${formId} .idComm").hide();
		if(comm_type == '3')
		{
			$("#${formId} #trSn").show();
			$("#${formId} #idSn").rules("add", { required: true });
		}
		else
		{
			$("#${formId} #idSn").rules("remove");
			$("#${formId} #trSn").hide();
		}
	}
	else if(comm_type == '2')
	{
		$("#${formId} .idHttpTcp").hide();
		$("#${formId} .idComm").show();
        $("#${formId} #ipAddress_hidden").rules("remove");
        $("#${formId} #idIPPort").rules("remove");
	}
	
	//切换通信方式
	$("#${formId} #trCommType input[name='commType']").click(function(){
		if(this.value == '1' || this.value == '3')
		{
			//IP地址输入框
			$("#${formId} .idHttpTcp").show();
			$("#${formId} .idComm").hide();
			if(this.value == '1')
			{
				$("#${formId} #idSn").rules("remove");
			}
			else
			{
				$("#${formId} #idSn").rules("add", { required: true});
				$("#${formId} #trSn").show();
			}
			$("#${formId} #ipAddress_hidden").rules("remove");
			$("#${formId} #ipAddress_hidden").rules("add", {IpValid: true});
			$("#${formId} #ipAddress_hidden").valid();
			$("#${formId} #idIPPort").rules("add", { required: true, digits: true});
			$("#${formId} #idComAddress").rules("remove");
			if($("#${formId} #idAcpanelType").val() == '5')
			{
			    //$("#trModeSelect").show();
			}
		}
		if(this.value == '2')
		{
			$("#${formId} .idHttpTcp").hide();
			$("#${formId} #trSn").hide();
			$("#${formId} .idComm").show();
			$("#${formId} #idComAddress").rules("add", {required: true});
			$("#${formId} #idComAddress").rules("add", {digits: true,range: [1,63]});
			$("#${formId} #ipAddress_hidden").rules("remove");
			$("#${formId} #ipAddress_hidden").valid();
            $("#${formId} #idIPPort").rules("remove");
			$("#${formId} #idSn").rules("remove");
			$("#${formId} #trModeSelect").hide();
		}
		comm_type = this.value;
	});

	function accAddAcpanelTypeChange(value) {
		var commType = $("#${formId} #trCommType input[name='commType']:checked").val();
		if(value == 4){
			$("#${formId} #trFourToTwo").show();
			if(commType != '2')
			{
			    $("#${formId} #trModeSelect").hide();
			}
		}
		else if(value == 5)
		{
			$("#${formId} #trFourToTwo").hide();
		    if(commType != '2')
		    {
		        //$("#trModeSelect").show();
		    }
		}
		else
		{
			$("#${formId} #trFourToTwo").hide();
			$("#${formId} #trFourToTwo input").attr("checked",false)
			if(commType != '2')
			{
			    $("#${formId} #trModeSelect").hide();
			}
		}
	}
	
	if ($("#${formId} #idFourToTwo").val() == "true")//四门转二门时需要显示设置
	{
		$("#${formId} #trFourToTwo").show();
		$("#${formId} #idFourToTwo").attr("disabled", true);
	}

	if($("#${formId}").find("#idAlias").val() != "")//编辑
	{
		$("#${formId} input[id^='idCommType']").each(function(){
			$(this).attr("disabled", true);
		});
		$("#${formId} #idIPPort").attr("disabled", true);
		$("#${formId} #idComAddress").attr("disabled", true);   //disabled
		$("#${formId} #idSn").attr("disabled", true);   //disabled
		$("#${formId} .trEditHidden").hide();
		if($("#${formId} #devCommType").val() != "3")
		{
			$("#${formId} #idHTTPCommType").hide();
		}
		$("#${formId} #trCommPwd").hide();
		if(comm_type == "1" || comm_type == "2") {
			$("#${formId} #trCommPwd").show();
		}
		$("#${formId} #trAcpanelType").hide();
	}
	else// 新增，隐藏HTTP通信方式
	{
		$("#${formId} #idHTTPCommType").hide();
	}

	if("${tempAccDevice.id}" != "")
	{
        ZKUI.Combo.get("accAcpanelType").combo.disable(true);
        ZKUI.Combo.get("idComPort").combo.disable(true);
        ZKUI.Combo.get("idBaudrate").combo.disable(true);
    }
	
	paintRS485Pic($("#${formId} #idComAddress").val());//初始化RS485的拨码模拟图
	
	/**
	 * 根据RS485地址，画出RS485的拨码模拟图
	 */
	function paintRS485Pic(rs485Addr)
	{
		if(rs485Addr >= 0 && rs485Addr < 64)
		{
			for(var i = 1; i <= 6; i++)
			{
				$("#${formId} #rs" + i).attr("class", "rs485p" + ((rs485Addr & Math.pow(2, i - 1)) >> (i - 1)));
			}
		}
	}
	 
    /**
   	 * 根据RS485的拨码模拟图，更新rs485地址
    */
	function updateRS485Addr(rs485Obj)
	{
        <#if !(tempAccDevice.id)?exists>
            var rs485Addr = 0;
        	if($(rs485Obj).attr("class") == "rs485p1")
        	{
        		$(rs485Obj).attr("class", "rs485p0");
        	}
        	else if($(rs485Obj).attr("class") == "rs485p0")
        	{
        		$(rs485Obj).attr("class", "rs485p1");
        	}
        	for(var i = 1; i <= 6; i++)
        	{
        		if($("#${formId} #rs" + i).attr("class") == "rs485p1")
        		{
        			rs485Addr += Math.pow(2, i - 1);
        		}
        	}
        	$("#${formId} #idComAddress").val(rs485Addr);
        </#if>
	}
    <#if (tempAccDevice.machineType)?exists>
        <#if (tempAccDevice.machineType)?string == "101">
        	$("#${formId} #setWigendReader").show();
         	<#if (masterSlave)?exists>
         		$("#${formId} #modeSelect").val(${masterSlave});
         	</#if>
         	if(comm_type != '2')
         	{
         	   //$("#trModeSelect").show();
         	}
        </#if>
    </#if>
    
    function accDevClearReader()
    {
    	$("#${formId} #wigendReaderId").val("");
    	$("#${formId} #readerName").val("");
    }
    
	function accSelectSetReader()
	{
   		DhxCommon.createWindow("skip.do?page=acc_device_accSelectReaderRadio^0^0^920^480^<@i18n 'acc_readerDefine_selReader'/>");
	}
<#if ((supportDst)?exists)>
	function selectDsTimeCallBack(id, name) {
		if(id != null && id.trim() != "" && name != null && name.trim() != "") {
			$("#accDSTimeId${uuid}").val(id);
			$("#accDSTimeName${uuid}").val(name);
		} else {
			$("#accDSTimeId${uuid}").val("");
			$("#accDSTimeName${uuid}").val("<@i18n 'acc_dsTimeUtc_none'/>");
		}
	}
	$(function() {
		setTimeout(function () {
			changeTimeZone("${(tempAccDevice.timeZone)!}");
		}, 100);
		setTimeout(function () {
			selectDsTimeCallBack("${tempAccDevice.accDSTimeId!}", "${tempAccDevice.accDSTimeName!}");
		}, 200);
	})
</#if>
</script>
</#macro>
