<#assign formId = "${uuid!}">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
<#assign editPage = true>
<script type='text/javascript'>
    $().ready(function() {
        $('#${formId}').validate( {
            debug : true,
            rules :
            {
                "VOLUME": {
                    required: true,
                    digits: true,
                    range: [0,100]
                },
                "FVInterval": {
                    required: true,
                    digits: true,
                    range: [0,10000]
                },
                "FaceMThr": {
                    required: true,
                    digits: true,
                    range: [0,100]
                },
                "FacePitchThreshold": {
                    required: true,
                    digits: true,
                    range: [0,180]
                },
                "FaceRollYawThreshold": {
                    required: true,
                    digits: true,
                    range: [0,180]
                },
                "ModifyClarityCond": {
                    required: true,
                    digits: true,
                    range: [0,100]
                },
                "FaceMinThreshold": {
                    required: true,
                    digits: true,
                    range: [0,100]
                },
                "IRTempLowThreshold": {
                    required: true,
                    range: [30.0,60.0]
                },
                "IRTempThreshold": {
                    required: true,
                    range: [30.0,60.0]
                },
                "IRTempCorrection": {
                    required: true,
                    range: [-9999.99, 99999.99]
                }
            },
            submitHandler : function()
            {
                var minTemperature = getUploadTemperature($("#${formId} #irTempLowThreshold"));
                var maxTemperature = getUploadTemperature($("#${formId} #irTempThreshold"));
                var irTempCorrection = getUploadTemperature($("#${formId} #irTempCorrection"));
                $("#${formId} #irTempLowThreshold").val(minTemperature);
                $("#${formId} #irTempThreshold").val(maxTemperature);
                $("#${formId} #irTempCorrection").val(irTempCorrection);
                <@submitHandler/>
            }
        });
    });

    tabbar = new dhtmlXTabBar("updateDevExtendedTabbar", "top");

    <#if faceUiParamMap?exists &&(faceUiParamMap?size > 0)>
        tabbar.addTab("uiParams","<@i18n 'acc_extendParam_faceUI'/><span class='required'>*</span>","140");
        tabbar.tabs("uiParams").attachObject("faceUIParams");
    </#if>
    <#if faceParamMap?exists &&(faceParamMap?size > 0)>
        tabbar.addTab("faceParams","<@i18n 'acc_extendParam_faceParam'/><span class='required'>*</span>","140");
        tabbar.tabs("faceParams").attachObject("faceParams");
    </#if>
    <#if accParamMap?exists &&(accParamMap?size > 0)>
        tabbar.addTab("accParams","<@i18n 'acc_extendParam_accParam'/><span class='required'>*</span>","140");
        tabbar.tabs("accParams").attachObject("accParams");
    </#if>
    <#if videoIntercomParamMap?exists &&(videoIntercomParamMap?size > 0)>
        tabbar.addTab("videoIntercomParams","<@i18n 'acc_extendParam_intercomParam'/><span class='required'>*</span>","140");
        tabbar.tabs("videoIntercomParams").attachObject("videoIntercomParams");
    </#if>

    <#if faceUiParamMap?exists &&(faceUiParamMap?size > 0)>
        tabbar.tabs("uiParams").setActive();
    <#elseif faceParamMap?exists &&(faceParamMap?size > 0)>
        tabbar.tabs("faceParams").setActive();
    <#elseif accParamMap?exists &&(accParamMap?size > 0)>
        tabbar.tabs("accParams").setActive();
    <#elseif videoIntercomParamMap?exists &&(videoIntercomParamMap?size > 0)>
        tabbar.tabs("videoIntercomParams").setActive();
    </#if>
</script>
<form action='accDevice.do?updateDevExtendParam' method='post' id='${formId}'>
    <input type="hidden" name="devId" value="${(item.id)!}">
    <input type="hidden" name="alias" value="${(item.alias)!}">
    <div id="updateDevExtendedTabbar" style="width: 100%; height:380px; padding: 0px; float:left; margin-top: 10px;"></div>
    <div id="faceUIParams" style="display: none;">
        <table cellpadding="0" cellspacing="0" style="width: 100%;">
            <tr>
                <td>
                    <table cellpadding="8" class="tableStyle">
                        <#if faceUiParamMap['VOLUME']?exists>
                            <tr>
                                <th>
                                    <label><@i18n 'acc_extendParam_volume'/></label><span class="required">*</span>
                                </th>
                                <td>
                                    <input type="text" maxlength="3" name="VOLUME" value="${(faceUiParamMap['VOLUME'])!'70'}">
                                </td>
                            </tr>
                        </#if>
                        <#if faceUiParamMap['FVInterval']?exists>
                            <tr>
                                <th>
                                    <label><@i18n 'acc_extendParam_identInterval'/></label><span class="required">*</span>
                                </th>
                                <td>
                                    <input type="text" maxlength="5" name="FVInterval" value="${(faceUiParamMap['FVInterval'])!'0'}">
                                </td>
                            </tr>
                        </#if>
                        <#if faceUiParamMap['EnableShowRegResult']?exists>
                            <tr>
                                <th>
                                    <label><@i18n 'acc_extendParam_historyVerifyResult'/></label>
                                </th>
                                <td>
                                    <label>
                                        <#if faceUiParamMap['EnableShowRegResult'] == '1'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableShowRegResult" value="1" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableShowRegResult" value="1"/>
                                        </#if>
                                    <@i18n 'common_yes'/>
                                    </label>
                                    <label>
                                        <#if faceUiParamMap['EnableShowRegResult'] == '0'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableShowRegResult" value="0" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableShowRegResult" value="0"/>
                                        </#if>
                                    <@i18n 'common_no'/>
                                    </label>
                                </td>
                            </tr>
                        </#if>
                        <#if faceUiParamMap['EnableShowIP']?exists>
                            <tr>
                                <th>
                                    <label><@i18n 'acc_extendParam_showIp'/></label>
                                </th>
                                <td>
                                    <label>
                                        <#if faceUiParamMap['EnableShowIP'] == '1'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableShowIP" value="1" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableShowIP" value="1"/>
                                        </#if>
                                    <@i18n 'common_yes'/>
                                    </label>
                                    <label>
                                        <#if faceUiParamMap['EnableShowIP'] == '0'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableShowIP" value="0" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableShowIP" value="0"/>
                                        </#if>
                                    <@i18n 'common_no'/>
                                    </label>
                                </td>
                            </tr>
                        </#if>
                        <#if faceUiParamMap['EnableShowMacAddress']?exists>
                            <tr>
                                <th>
                                    <label><@i18n 'acc_extendParam_macAddress'/></label>
                                </th>
                                <td>
                                    <label>
                                        <#if faceUiParamMap['EnableShowMacAddress'] == '1'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableShowMacAddress" value="1" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableShowMacAddress" value="1"/>
                                        </#if>
                                    <@i18n 'common_yes'/>
                                    </label>
                                    <label>
                                        <#if faceUiParamMap['EnableShowMacAddress'] == '0'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableShowMacAddress" value="0" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableShowMacAddress" value="0"/>
                                        </#if>
                                    <@i18n 'common_no'/>
                                    </label>
                                </td>
                            </tr>
                        </#if>
                        <#if faceUiParamMap['timeFormat']?exists>
                            <tr>
                                <th>
                                    <label><@i18n 'acc_extendParam_24HourFormat'/></label>
                                </th>
                                <td>
                                    <label>
                                        <#if faceUiParamMap['timeFormat'] == '1'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="timeFormat" value="1" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="timeFormat" value="1"/>
                                        </#if>
                                    <@i18n 'common_yes'/>
                                    </label>
                                    <label>
                                        <#if faceUiParamMap['timeFormat'] == '0'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="timeFormat" value="0" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="timeFormat" value="0"/>
                                        </#if>
                                    <@i18n 'common_no'/>
                                    </label>
                                </td>
                            </tr>
                        </#if>
                        <#if faceUiParamMap['DtFmt']?exists>
                            <tr>
                                <th>
                                    <label><@i18n 'acc_extendParam_dateFormat'/></label>
                                </th>
                                <td>
                                    <@ZKUI.Combo autoFirst="true" empty="false" hideLabel="true" name="DtFmt" value="${(faceUiParamMap['DtFmt'])!'9'}" width="148">
                                        <option value=0>YY-MM-DD</option>
                                        <option value=1>YY/MM/DD</option>
                                        <option value=2>YY.MM.DD</option>
                                        <option value=3>MM-DD-YY</option>
                                        <option value=4>MM/DD/YY</option>
                                        <option value=5>MM.DD.YY</option>
                                        <option value=6>DD-MM-YY</option>
                                        <option value=7>DD/MM/YY</option>
                                        <option value=8>DD.MM.YY</option>
                                        <option value=9>YYYY-MM-DD</option>
                                    </@ZKUI.Combo>
                                </td>
                            </tr>
                        </#if>
                    </table>
                </td>
            </tr>
        </table>
    </div>

    <div id="faceParams" style="display: none;">
        <table cellpadding="0" cellspacing="0" style="width: 100%;">
            <tr>
                <td>
                    <table cellpadding="8" class="tableStyle">
                        <#if faceParamMap['~FaceMThr']?exists>
                            <tr>
                                <th>
                                    <label><@i18n 'acc_extendParam_1NThreshold'/></label><span class="required">*</span>
                                </th>
                                <td>
                                    <input type="text" maxlength="3" name="FaceMThr" value="${(faceParamMap['~FaceMThr'])!'82'}">
                                </td>
                            </tr>
                        </#if>
                        <#if faceParamMap['FacePitchThreshold']?exists>
                            <tr>
                                <th>
                                    <label><@i18n 'acc_extendParam_facePitchAngle'/></label><span class="required">*</span>
                                </th>
                                <td>
                                    <input type="text" maxlength="3" name="FacePitchThreshold" value="${(faceParamMap['FacePitchThreshold'])!'35'}">
                                </td>
                            </tr>
                        </#if>
                        <#if faceParamMap['FaceRollYawThreshold']?exists>
                            <tr>
                                <th>
                                    <label><@i18n 'acc_extendParam_faceRotationAngle'/></label><span class="required">*</span>
                                </th>
                                <td>
                                    <input type="text" maxlength="3" name="FaceRollYawThreshold" value="${(faceParamMap['FaceRollYawThreshold'])!'30'}">
                                </td>
                            </tr>
                        </#if>
                        <#if faceParamMap['ModifyClarityCond']?exists>
                            <tr>
                                <th>
                                    <label><@i18n 'acc_extendParam_imageQuality'/></label><span class="required">*</span>
                                </th>
                                <td>
                                    <input type="text" maxlength="3" name="ModifyClarityCond" value="${(faceParamMap['ModifyClarityCond'])!'80'}">
                                </td>
                            </tr>
                        </#if>
                        <#if faceParamMap['FaceMinThreshold']?exists>
                            <tr>
                                <th>
                                    <label><@i18n 'acc_extendParam_miniFacePixel'/></label>
                                </th>
                                <td>
                                    <input type="text" maxlength="3" name="FaceMinThreshold" value="${(faceParamMap['FaceMinThreshold'])!'80'}">
                                </td>
                            </tr>
                        </#if>
                        <#if faceParamMap['IsSupportFaceAntiFake']?exists>
                            <tr>
                                <th>
                                    <label><@i18n 'acc_extendParam_biopsy'/></label>
                                </th>
                                <td>
                                    <label>
                                        <#if faceParamMap['IsSupportFaceAntiFake'] == '1'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="IsSupportFaceAntiFake" value="1" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="IsSupportFaceAntiFake" value="1"/>
                                        </#if>
                                    <@i18n 'common_yes'/>
                                    </label>
                                    <label>
                                        <#if faceParamMap['IsSupportFaceAntiFake'] == '0'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="IsSupportFaceAntiFake" value="0" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="IsSupportFaceAntiFake" value="0"/>
                                        </#if>
                                    <@i18n 'common_no'/>
                                    </label>
                                </td>
                            </tr>
                        </#if>
                        <#if faceParamMap['EnableIRTempImage']?exists>
                            <tr>
                                <th>
                                    <label><@i18n 'acc_extendParam_showThermalImage'/></label>
                                </th>
                                <td>
                                    <label>
                                        <#if faceParamMap['EnableIRTempImage'] == '1'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableIRTempImage" value="1" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableIRTempImage" value="1"/>
                                        </#if>
                                    <@i18n 'common_yes'/>
                                    </label>
                                    <label>
                                        <#if faceParamMap['EnableIRTempImage'] == '0'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableIRTempImage" value="0" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableIRTempImage" value="0"/>
                                        </#if>
                                    <@i18n 'common_no'/>
                                    </label>
                                </td>
                            </tr>
                        </#if>
                        <#if faceParamMap['AttributeFunOn']?exists>
                            <tr>
                                <th>
                                    <label><@i18n 'acc_extendParam_attributeAnalysis'/></label>
                                </th>
                                <td>
                                    <label>
                                        <#if faceParamMap['AttributeFunOn'] == '1'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="AttributeFunOn" value="1" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="AttributeFunOn" value="1"/>
                                        </#if>
                                    <@i18n 'common_yes'/>
                                    </label>
                                    <label>
                                        <#if faceParamMap['AttributeFunOn'] == '0'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="AttributeFunOn" value="0" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="AttributeFunOn" value="0"/>
                                        </#if>
                                    <@i18n 'common_no'/>
                                    </label>
                                </td>
                            </tr>
                        </#if>
                        <#if faceParamMap['EnalbeIRTempDetection']?exists>
                            <tr id="temperatureAttribute_id">
                                <th>
                                    <label><@i18n 'acc_extendParam_temperatureAttribute'/>
                                </th>
                                <td>
                                    <label>
                                        <#if faceParamMap['EnalbeIRTempDetection'] == '1'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnalbeIRTempDetection" value="1" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnalbeIRTempDetection" value="1"/>
                                        </#if>
                                    <@i18n 'common_yes'/>
                                    </label>
                                    <label>
                                        <#if faceParamMap['EnalbeIRTempDetection'] == '0'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnalbeIRTempDetection" value="0" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnalbeIRTempDetection" value="0"/>
                                        </#if>
                                    <@i18n 'common_no'/>
                                    </label>
                                </td>
                            </tr>
                        </#if>
                        <#if faceParamMap['EnableNormalIRTempPass']?exists>
                            <tr id="enableNormalIRTempPassDiv">
                                <th><label><@i18n 'acc_dev_enableNormalIRTempPass'/></label></th>
                                <td>
                                    <label>
                                        <#if faceParamMap['EnableNormalIRTempPass'] == '1'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableNormalIRTempPass" value="1" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableNormalIRTempPass" value="1"/>
                                        </#if>
                                    <@i18n 'common_yes'/>
                                    </label>
                                    <label>
                                        <#if faceParamMap['EnableNormalIRTempPass'] == '0'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableNormalIRTempPass" value="0" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableNormalIRTempPass" value="0"/>
                                        </#if>
                                    <@i18n 'common_no'/>
                                    </label>
                                </td>
                            </tr>
                        </#if>
                        <#if faceParamMap['IRTempLowThreshold']?exists>
                            <tr id="minTemperature_id">
                                <th>
                                    <label><@i18n 'acc_extendParam_minTemperature'/></label><span class="required">*</span>
                                </th>
                                <td>
                                    <input id="irTempLowThreshold" type="text" maxlength="6" name="IRTempLowThreshold" value="${(faceParamMap['IRTempLowThreshold'])!'30'}" onkeyup="temperatureValid(this)"/>
                                </td>
                            </tr>
                        </#if>
                        <#if faceParamMap['IRTempThreshold']?exists>
                            <tr id="maxTemperature_id">
                                <th>
                                    <label><@i18n 'acc_extendParam_maxTemperature'/></label><span class="required">*</span>
                                </th>
                                <td>
                                    <input id="irTempThreshold" type="text" maxlength="6" name="IRTempThreshold" value="${(faceParamMap['IRTempThreshold'])!'37.3'}" onkeyup="temperatureValid(this)"/>
                                </td>
                            </tr>
                        </#if>
                        <#if faceParamMap['IRTempCorrection']?exists>
                            <tr id="irTempCorrectionDiv">
                                <th><label><@i18n 'acc_dev_tempCorrection'/></label></th>
                                <td>
                                    <input type="text" name="IRTempCorrection" maxlength="8" id="irTempCorrection" value="${(faceParamMap['IRTempCorrection'])!'0'}" onkeyup="temperatureValid(this)"/>
                                </td>
                            </tr>
                        </#if>
                        <#if faceParamMap['IRTempUnit']?exists>
                            <tr id="irTempUnitDiv">
                                <th><label><@i18n 'acc_dev_tempUnit'/></label></th>
                                <td>
                                    <label>
                                        <#if faceParamMap['IRTempUnit'] == '0'>
                                            <@ZKUI.Input hideLabel="true" type="radio" id="celsiusUnit" name="IRTempUnit" value="0" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" id="celsiusUnit" name="IRTempUnit" value="0"/>
                                        </#if>
                                    ℃
                                    </label>
                                    <label>
                                        <#if faceParamMap['IRTempUnit'] == '1'>
                                            <@ZKUI.Input hideLabel="true" type="radio" id="fahrenheitUnit" name="IRTempUnit" value="1" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" id="fahrenheitUnit" name="IRTempUnit" value="1"/>
                                        </#if>
                                    ℉
                                    </label>
                                </td>
                            </tr>
                        </#if>
                        <#if faceParamMap['EnalbeMaskDetection']?exists>
                            <tr id="maskAttribute_id">
                                <th>
                                    <label><@i18n 'acc_extendParam_maskAttribute'/></label>
                                </th>
                                <td>
                                    <label>
                                        <#if faceParamMap['EnalbeMaskDetection'] == '1'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnalbeMaskDetection" value="1" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnalbeMaskDetection" value="1"/>
                                        </#if>
                                    <@i18n 'common_yes'/>
                                    </label>
                                    <label>
                                        <#if faceParamMap['EnalbeMaskDetection'] == '0'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnalbeMaskDetection" value="0" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnalbeMaskDetection" value="0"/>
                                        </#if>
                                    <@i18n 'common_no'/>
                                    </label>
                                </td>
                            </tr>
                        </#if>
                        <#if faceParamMap['EnableWearMaskPass']?exists>
                            <tr id="enableWearMaskPassDiv">
                                <th><label><@i18n 'acc_dev_enableWearMaskPass'/></label></th>
                                <td>
                                    <label>
                                        <#if faceParamMap['EnableWearMaskPass'] == '1'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableWearMaskPass" value="1" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableWearMaskPass" value="1"/>
                                        </#if>
                                    <@i18n 'common_yes'/>
                                    </label>
                                    <label>
                                        <#if faceParamMap['EnableWearMaskPass'] == '0'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableWearMaskPass" value="0" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableWearMaskPass" value="0"/>
                                        </#if>
                                    <@i18n 'common_no'/>
                                    </label>
                                </td>
                            </tr>
                        </#if>
                        <#if faceParamMap['EnableUnregisterPass']?exists>
                            <tr>
                                <th><label><@i18n 'acc_dev_enableUnregisterPass'/></label></th>
                                <td>
                                    <label>
                                        <#if faceParamMap['EnableUnregisterPass'] == '1'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableUnregisterPass" value="1" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableUnregisterPass" value="1"/>
                                        </#if>
                                    <@i18n 'common_yes'/>
                                    </label>
                                    <label>
                                        <#if faceParamMap['EnableUnregisterPass'] == '0'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableUnregisterPass" value="0" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableUnregisterPass" value="0"/>
                                        </#if>
                                    <@i18n 'common_no'/>
                                    </label>
                                </td>
                            </tr>
                        </#if>
                        <#if faceParamMap['EnableTriggerAlarm']?exists>
                            <tr>
                                <th><label><@i18n 'acc_dev_enableTriggerAlarm'/></label></th>
                                <td>
                                    <label>
                                        <#if faceParamMap['EnableTriggerAlarm'] == '1'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableTriggerAlarm" value="1" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableTriggerAlarm" value="1"/>
                                        </#if>
                                    <@i18n 'common_yes'/>
                                    </label>
                                    <label>
                                        <#if faceParamMap['EnableTriggerAlarm'] == '0'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableTriggerAlarm" value="0" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="EnableTriggerAlarm" value="0"/>
                                        </#if>
                                    <@i18n 'common_no'/>
                                    </label>
                                </td>
                            </tr>
                        </#if>
                    </table>
                </td>
            </tr>
        </table>
    </div>

    <div id="accParams" style="display: none;">
        <table cellpadding="0" cellspacing="0" style="width: 100%;">
            <tr>
                <td>
                    <table cellpadding="8" class="tableStyle">
                        <#if accParamMap['ChannelMode']?exists>
                            <tr>
                                <th style="width: 180px;">
                                    <label><@i18n 'acc_extendParam_gateMode'/></label><span class="required">*</span>
                                </th>
                                <td>
                                    <label>
                                        <#if accParamMap['ChannelMode'] == '1'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="ChannelMode" value="1" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="ChannelMode" value="1"/>
                                        </#if>
                                    <@i18n 'common_yes'/>
                                    </label>
                                    <label>
                                        <#if accParamMap['ChannelMode'] == '0'>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="ChannelMode" value="0" checked="checked"/>
                                        <#else>
                                            <@ZKUI.Input hideLabel="true" type="radio" name="ChannelMode" value="0"/>
                                        </#if>
                                    <@i18n 'common_no'/>
                                    </label>
                                </td>
                            </tr>
                        </#if>
                    <#if accParamMap['IsSupportQRcode']?exists>
                        <tr>
                            <th style="width: 180px;">
                                <label><@i18n 'acc_extendParam_qrcodeEnable'/></label><span class="required">*</span>
                            </th>
                            <td>
                                <label>
                                    <#if accParamMap['QRCodeEnable'] == '1'>
                                        <@ZKUI.Input hideLabel="true" type="radio" name="QRCodeEnable" value="1" checked="checked"/>
                                    <#else>
                                        <@ZKUI.Input hideLabel="true" type="radio" name="QRCodeEnable" value="1"/>
                                    </#if>
                                <@i18n 'common_yes'/>
                                </label>
                                <label>
                                    <#if accParamMap['QRCodeEnable'] == '0'>
                                        <@ZKUI.Input hideLabel="true" type="radio" name="QRCodeEnable" value="0" checked="checked"/>
                                    <#else>
                                        <@ZKUI.Input hideLabel="true" type="radio" name="QRCodeEnable" value="0"/>
                                    </#if>
                                <@i18n 'common_no'/>
                                </label>
                            </td>
                        </tr>
                    </#if>
                    </table>
                </td>
            </tr>
        </table>
    </div>

    <div id="videoIntercomParams" style="display: none;">
        <table cellpadding="0" cellspacing="0" style="width: 100%;">
            <tr>
                <td>
                    <table cellpadding="8" class="tableStyle">
                        <#if videoIntercomParamMap['VideoProtocol']?exists && videoIntercomParamMap['VideoProtocol'] == '4'>
                        <tr class="vmsIntercomSetting">
                            <th style="width: 180px;">
                                <label><@i18n 'acc_dev_intercomServer'/></label>
                            </th>
                            <td>
                                <div style="display:inherit;">
                                <@ZKUI.IP name="VMSSvrUrl" id="intercomIp" value="${videoIntercomParamMap['VMSSvrUrl']!'0.0.0.0'}"/>
                                </div>
                            </td>
                        </tr>
                        <tr class="vmsIntercomSetting">
                            <th style="width: 180px;">
                                <label><@i18n 'acc_dev_intercomPort'/></label>
                            </th>
                            <td>
                                <input type="text" name="VMSSvrPort" maxlength="8" id="intercomPort" value="${videoIntercomParamMap['VMSSvrPort']!8098}" />
                            </td>
                        </tr>
                        </#if>
                    </table>
                </td>
            </tr>
        </table>
    </div>

</form>

<script type='text/javascript'>

    $().ready(function() {
        var minTemperature = $("#${formId} #irTempLowThreshold").val();
        var maxTemperature = $("#${formId} #irTempThreshold").val();
        var irTempCorrection = $("#${formId} #irTempCorrection").val();
        $("#${formId} #irTempLowThreshold").val((parseFloat(minTemperature)/100).toFixed(2));
        $("#${formId} #irTempThreshold").val((parseFloat(maxTemperature)/100).toFixed(2));
        $("#${formId} #irTempCorrection").val((parseFloat(irTempCorrection)/100).toFixed(2));
        var irTempUnit = $("#${formId} input[name='IRTempUnit']:checked").val();
        changeIRTempThresholdRules(irTempUnit);

        // 体温检测属性
        if ($("input[name='EnalbeIRTempDetection']:checked").val() !== "1") {
            $("#enableNormalIRTempPassDiv").hide();
            $("#minTemperature_id").hide();
            $("#maxTemperature_id").hide();
            $("#irTempCorrectionDiv").hide();
            $("#irTempUnitDiv").hide();
        }
        // 口罩检测属性
        if ($("input[name='EnalbeMaskDetection']:checked").val() !== "1") {
            $("#enableWearMaskPassDiv").hide();
        }
        // 可视对讲
        if ($("input[name='VisualIntercomFunOn']:checked").val() == "0") {
            $(".vmsIntercomSetting").hide();
        }
    })

    // 根据温度单位修改校验规则
    function changeIRTempThresholdRules(irTempUnit) {
        // 摄氏
        if (irTempUnit === "0") {
            if ($("#${formId} #irTempLowThreshold").length > 0) {
                $("#${formId} #irTempLowThreshold").rules("add", { range: [30.0,60.0]});
            }
            if ($("#${formId} #irTempThreshold").length > 0) {
                $("#${formId} #irTempThreshold").rules("add", { range: [30.0,60.0]});
            }
        } else if(irTempUnit === "1"){
            // 华氏
            if ($("#${formId} #irTempLowThreshold").length > 0) {
                $("#${formId} #irTempLowThreshold").rules("add", { range: [86.0,113.0]});
            }
            if ($("#${formId} #irTempThreshold").length > 0) {
                $("#${formId} #irTempThreshold").rules("add", { range: [86.0,113.0]});
            }
        }
    }

    //温度单位转换
    $("input[name='IRTempUnit']").change(function(){
        if($(this).val() == "0")//摄氏温度
        {
            changeIRTempThresholdRules($(this).val());
        }
        else//华氏温度
        {
            changeIRTempThresholdRules($(this).val());
        }
    });

    // 体温检测属性设置
    $("input[name='EnalbeIRTempDetection']").change(function(){
        if($(this).val() == "1")
        {
            $("#enableNormalIRTempPassDiv").show();
            $("#minTemperature_id").show();
            $("#maxTemperature_id").show();
            $("#irTempCorrectionDiv").show();
            $("#irTempUnitDiv").show();
            $("#${formId} input[name='IRTempThreshold']").rules("add", { required: true, range: [30.0,60.0]});
            $("#${formId} input[name='IRTempCorrection']").rules("add", { required: true, range: [-9999.99, 99999.99]});
        }
        else
        {
            $("#enableNormalIRTempPassDiv").hide();
            $("#minTemperature_id").hide();
            $("#maxTemperature_id").hide();
            $("#irTempCorrectionDiv").hide();
            $("#irTempUnitDiv").hide();
            $("#${formId} input[name='IRTempThreshold']").rules("remove");
            $("#${formId} input[name='IRTempCorrection']").rules("remove");
        }
    });

    // 口罩检测属性设置
    $("input[name='EnalbeMaskDetection']").change(function(){
        if($(this).val() == "1")
        {
            $("#enableWearMaskPassDiv").show();
        }
        else
        {
            $("#enableWearMaskPassDiv").hide();
        }
    });

    function temperatureValid(obj) {
        var value = obj.value;
        var indexOfDot = value.indexOf(".");
        if (indexOfDot != -1) {
            var tempNum = value.substring(indexOfDot, value.length);
            if (tempNum.length > 3) {
                $("#${formId} #"+obj.id).val( value.substring(0,value.length-1));
            }
        }
    }

    /** 上传的温度格式转化 */
    function getUploadTemperature(obj) {
        var temperature = "";
        if (obj && obj.length > 0) {
            temperature = obj.val();
            // 不包含小数位，补0保留两位有效数字
            var decimalPosition = temperature.indexOf('.');
            if (decimalPosition < 0)
            {
                decimalPosition = temperature.length;
                temperature += '.';
            }
            var length = decimalPosition + 2;
            for (var i = temperature.length; i <= length; i++) {
                temperature += '0';
            }
        }
        // 去掉小数点
        return parseInt(temperature.replace(".",""));
    }

    $("input[name='VisualIntercomFunOn']").change(function(){
        if($(this).val() == "0")
        {
            $(".vmsIntercomSetting").hide();
        }
        else
        {
            $(".vmsIntercomSetting").show();
        }
    });
</script>
</#macro>