<#include "/public/template/editTemplate.html">
<#macro editContent>
	<script type="text/javascript">
		$().ready(function() {
			jQuery.validator.addMethod("validPwd", function(value, element){
				var pattenChar = /^[A-Za-z0-9]{1,6}$/;
				return this.optional(element) || (pattenChar.test(value));
			}, "<@i18n 'pers_person_pwdOnlyLetterNum'/>");
			
			jQuery.validator.addMethod("validNewPwd", function(value, element){
				if(value.indexOf("0") == "0")
				{
					return false;
				}
				return true;
			}, "<@i18n 'acc_dev_pwdStartWithZero'/>");
			
			jQuery.validator.addMethod("pwsOldValid", function(value, element){
				var isExist = true;
				var old_commpwd = $("#${formId} #id_old_commpwd").val();
	       		var dev_id = $("#${formId} #id_model_pk").val();
		  		$.ajax({
		            type: "post",
		            url: "/accDevice.do?checkPwd",
		           	dataType: "json",
		            async :false,
		            data:
		            {
					   'oldCommPwd' : old_commpwd,
					   'devId' : dev_id
		            } ,
		            success: function (data) 
		            {
		               	isExist = data.data;
		               	if(eval(isExist))
		               	{
		               		 $("#${formId} #r_msg").html("<label class='zk-msg-normal'>" + I18n.getValue("common_prompt_right") + "</label>");
		               	}
		               	else
		               	{
		               		$("#${formId} #r_msg").html("<label class='zk-msg-error'>" + I18n.getValue("common_prompt_wrong") + "</label>");
		               	}
		            },
		            error: function (XMLHttpRequest, textStatus, errorThrown) 
		            {
		            	messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
		            }
				});
				return isExist;
			},function(){
				/* return  "${auth_user_pwdIncorrect}"; */
			});
			
			$("#${formId}").validate( {
				debug : true,
				rules :
				{
					"id_old_commpwd" :
					{
						<#if isExistOldPwd?string == "true">
						required : true, 
						</#if>
						pwsOldValid : true
					},

					"newCommPwd" :
					{
						<#if (isSupportChar)?exists>
						validNewPwd : true,
						digits : true
						</#if>
					},

					"confCommPwd" : 
					{
						equalTo : "#id_new_commpwd"
					}
				},
				submitHandler : function()
				{
					execAccConfirm($("#${formId} #id_model_pk").val(), function(){<@submitHandler/>});
				}
			});
		});

        $("#${formId} #id_new_commpwd").rules("add", {validPwd: true});
        $("#${formId} #id_conf_commpwd").rules("add", {validPwd: true});
	</script>

	<form action="/accDevice.do?updateCommPwd" method="post" onkeydown="if(event.keyCode==13){return false;}" id="${formId}" enctype="multipart/form-data">
		<input type="hidden" id="id_model_pk" name="devId" value="${(tempAccDevice.id)!}"/>
		<input type="hidden" id="alias" name="alias" value="${(tempAccDevice.alias)!}"/>
			<table class="tableStyle">
			  <#if isExistOldPwd?string == "true">
				<tr id="tr_old_pwd">
					<th>
						<label><@i18n 'base_fp_oldPwd'/></label><span class="required">*</span>
					</th>
			        <td>
			        	<input type="password" id="id_old_commpwd" onkeypress="return isNotSpace(event);" name="id_old_commpwd" />&nbsp;
			        	<span id="r_msg"></span>
			        </td>
				</tr>
			  </#if>
			    <tr id="tr_new_pwd">
			        <th>
			        	<label><@i18n 'base_fp_newPwd'/></label>
			        </th>
			        <td>
			        	<input name="newCommPwd" type="password" onkeypress="return isNotSpace(event);" id="id_new_commpwd" maxlength="6"/>
						<#if (isSupportChar)?exists>
						<p><span style="color:#999999"><@i18n 'acc_device_pwdRequired'/></span></p>
						</#if>
			        </td>
				</tr>
				<tr id="tr_conf_pwd">
			        <th>
			        	<label><@i18n 'base_fp_confirmPwd'/></label>
			        </th>
			        <td>
			        	<input name="confCommPwd" type="password" onkeypress="return isNotSpace(event);" id="id_conf_commpwd" maxlength="6"/>
			        	<span id="e_msg2"></span>
			        </td>
				</tr>
			</table>
	</form>
	<div id="id_info" style="display:none;">
		<ul class="errorlist">
			<li style="width:100%;"></li>
		</ul>
	</div>
</#macro>