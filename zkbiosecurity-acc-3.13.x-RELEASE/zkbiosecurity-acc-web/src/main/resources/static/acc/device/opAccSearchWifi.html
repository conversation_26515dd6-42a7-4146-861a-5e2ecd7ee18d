<#include "/public/template/editTemplate.html">
<#macro editContent>
<div id="searchWifiGridBox" style="width:280px;height:300px;"></div>
<input type="hidden" id="devId" value="${devId}">
<input type="hidden" id="cmdId" value="${cmdId}">
<script type="text/javascript">
    var searchWifiGrid;
    var selectId;
    var time = 0;
	loadSearchWifiGrid();
	loadData(0);
	function loadSearchWifiGrid()// 生成列表
	{
		searchWifiGrid = new dhtmlXGridObject('searchWifiGridBox');
		searchWifiGrid.setImagePath(sysCfg.rootPath+"/public/controls/dhtmlx/dhtmlxGrid/codebase/imgs/");
		searchWifiGrid.setHeader("<@i18n 'acc_dev_wirelessSSID'/>,<@i18n 'acc_dev_signalIntensity'/>");
		searchWifiGrid.setColumnMinWidth("180,60");
		searchWifiGrid.setColumnMinWidth("225,*");
		searchWifiGrid.setInitWidths("180,*");//150
		searchWifiGrid.setColAlign("left,left");
		searchWifiGrid.setColTypes("ro,ro");//ro
		searchWifiGrid.enableAutoHeight(false);
		searchWifiGrid.enableAutoWidth(false);
		searchWifiGrid.setColSorting("na,na");// 排序
		searchWifiGrid.init();
		searchWifiGrid.enableHeaderMenu("false,false");
		searchWifiGrid.setSkin(sysCfg.dhxSkin);
		searchWifiGrid.attachEvent("onRowSelect", function(id,ind){
		    if (id == selectId) {
		        var nowTime = new Date().getTime();
		        if (nowTime - time < 400) { //模拟双击事件
                    $("#wirelessSSID").val(searchWifiGrid.cells(id,0).getValue());
                    DhxCommon.closeWindow();
                }
            }
            time = nowTime;
            selectId = id;
		});
		openMessage(msgType.loading);
	}
	function loadData(count)
	{
		$.ajax({
			url:"/accDevice.do?getWifiList",
			type:"post",
			dataType:"json",
			data:{cmdId:$("#cmdId").val()},
			success: function (result)
	        {
	        	if(result[sysCfg.ret] ==  sysCfg.success)
				{
	        		if(result[sysCfg.data] != undefined)
	        		{
	        			//ssid  ecn  mac  rssi
		        		$(result[sysCfg.data]).each(function(i,val){
		        			var img = 0;
		        			if(val.rssi >= -90 && val.rssi < -75)
		        			{
		        				img = 1;
		        			}
		        			else if(val.rssi >= -75 && val.rssi < -60)
		        			{
		        				img = 2;
		        			}
		        			else if(val.rssi >= -60 && val.rssi < -45)
		        			{
		        				img = 3;
		        			}
		        			else if(val.rssi >= -45 && val.rssi < -30)
		        			{
		        				img = 4;
		        			}
		        			else if(val.rssi >= -30)
		        			{
		        				img = 5;
		        			}
		        			searchWifiGrid.addRow(i+1,[val.ssid,"<img src='${base}/images/signal_"+img+".png'>"]);
		        		});

		        		closeMessage();
	        		}
	        		else
	        		{
	        			if(++count<60)
	        			{
	        				window.setTimeout(function(){
			        			loadData(count);
			        		},200);
	        			}
	        			else
	        			{
	        				messageBox({messageType:"alert", text: "<@i18n 'common_dev_searchFails'/><@i18n 'common_commStatus_cmdTimeOut'/>"});
	        				closeMessage();
	        			}
	        		}
				}
	        	else
	        	{
	        		messageBox({messageType:"alert", text: result[sysCfg.msg]});
	        	}

	        },
	        error: function (XMLHttpRequest, textStatus, errorThrown)
	        {
	        	messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
	        }
		});
	}
	
	function searchDev()
    {
		loadSearchWifiGrid();
		$.ajax({
			url:"/accDevice.do?searchWifiListAgain",
			type:"post",
			dataType:"json",
			data:{devId: "${devId}"},
			success: function (result)
	        {
	        	if(result[sysCfg.ret] == sysCfg.success)
				{
	        		$("#cmdId").val(result[sysCfg.data]);
	        		loadData(0);
				}
	        	else
	        	{
	        		messageBox({messageType:"alert", text: result[sysCfg.msg]});
				}
	        }
		});
    }
</script>
</#macro>
<#macro buttonContent>
<button class='button-form' id="confirmButton${uuid}"  onclick="searchDev()"><@i18n 'acc_dev_resetSearch'/></button>
<button class='button-form' onclick="DhxCommon.closeWindow()" id="closeButton${uuid}"><@i18n 'common_op_close'/></button>
</#macro>