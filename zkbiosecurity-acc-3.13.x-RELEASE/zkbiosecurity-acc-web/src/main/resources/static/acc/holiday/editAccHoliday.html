<#assign editPage="true">
<#include "/public/template/editTemplate.html">
<#macro editContent>
<script type="text/javascript">
    $().ready(function() {
        // 字母和数字的验证
        $("#${formId}").validate( {
            debug : true,
            rules : {
                "name" : {
                    required : true,
                    unInputChar:true,
                    overRemote : [ "accHoliday.do?isExist", "${(tempAccHoliday.name)!}" ]
        		},
				"startDate":{
					required : true
				},
				"endDate":{
					required : true
				}
   		 	},
			submitHandler : function() {
				$("#${formId}").attr("action", "accHoliday.do?dataValid");
				$('#${formId}').ajaxSubmit(function(returnData){
					if(returnData.data == "true")
					{
						$("#${formId}").attr("action", "accHoliday.do?save");
						<@submitHandler/>
					}
					else
					{
						$("#id_info${uuid}").show();
						$("#id_info${uuid} li").html(returnData.data);
					}
				});
			}
  	  	});
    });
</script>

<form method="post" id="${formId}" action="accHoliday.do?save" enctype="multipart/form-data">
	<input type="hidden" value="${(tempAccHoliday.id)!}" name="id" id="id_model_pk">
	<table class="tableStyle">
		<tbody>
		<tr>
			<th>
				<label><@i18n 'common_holiday_name'/></label><span class="required">*</span>
			</th>
			<td>
				<input type="text" maxlength="30" name="name" id="id_holiday_name" value="${(tempAccHoliday.name)!}">
			</td>
		</tr>
		<tr>
			<th>
				<label><@i18n 'common_holiday_type'/></label><span class="required">*</span>
			</th>
			<td>
				<@ZKUI.Combo width="148" empty="false" readonly="true" value="${(tempAccHoliday.holidayType)!}" hideLabel="true" name="holidayType">
					<option value="1"><@i18n 'common_holiday_type1'/></option>
					<option value="2"><@i18n 'common_holiday_type2'/></option>
					<option value="3"><@i18n 'common_holiday_type3'/></option>
				</@ZKUI.Combo>
			</td>
		</tr>
		<tr>
			<th>
				<label><@i18n 'common_startDate'/></label><span class="required">*</span>
			</th>
			<td><@ZKUI.Input type="date" today="true" value="${((tempAccHoliday.startDate)?string('yyyy-MM-dd'))!}" id="startDate" name="startDate" readonly="true" hideLabel="true" style="cursor: pointer;"/></td>
		</tr>
		<tr>
			<th>
				<label><@i18n 'common_endDate'/></label><span class="required">*</span>
			</th>
			<td><@ZKUI.Input type="date" today="true" value="${((tempAccHoliday.endDate)?string('yyyy-MM-dd'))!}" id="endDate" name="endDate" readonly="true" hideLabel="true" style="cursor: pointer;"/></td>
		</tr>
		<tr>
			<th>
				<label><@i18n 'common_holiday_recurring'/></label>
			</th>
			<td>
				<@ZKUI.Combo width="148" empty="false" name="isLoopByYear" hideLabel="true" value="${(tempAccHoliday.isLoopByYear?string)!}">
					<option value="false"><@i18n 'common_no'/></option>
					<option value="true"><@i18n 'common_yes'/></option>
				</@ZKUI.Combo>
			</td>
		</tr>
		<tr>
			<th>
				<label><@i18n 'common_remark'/></label>
			</th>
			<td>
				<input type="text" maxlength="50" name="remark" value="${(tempAccHoliday.remark)!}">
			</td>
		</tr>
		<tr id="addition_fields"></tr>
		</tbody>
	</table>
</form>
<div id="id_info${uuid}" style="display:none;">
	<ul class="errorlist">
		<li style="width:100%;"></li>
	</ul>
</div>
</#macro>
