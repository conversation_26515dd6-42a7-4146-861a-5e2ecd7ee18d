<@ZKUI.SelectContent gridName="accLinkageSelectVdbExtensionContent" showColumns="checkbox,extensionNumber,extensionName" textField="extensionNumber" onSure="selectBindObjectCallback" vo="com.zkteco.zkbiosecurity.acc.vo.AccLinkageSelectExtensionItem" copy="true" query="accLinkage.do?selectVdbExtensionList&selectValue=${selectValue!}">
    <@ZKUI.Searchbar>
        <@ZKUI.SearchTop>
            <tr>
              <td>
                  <@ZKUI.Input name="extensionNumber"  maxlength="20" title="common_vdb_extensionNumber" type="text"/>
              </td>
              <td valign="middle">
                  <@ZKUI.Input name="extensionName" maxlength="30" title="common_vdb_extensionName" type="text"/>
              </td>
            </tr>
        </@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
</@ZKUI.SelectContent>