<table width="100%" height="100%">
	<tr valign="top">
		<td class="content_td">
			<div class="content_div" style="height: 50px;overflow: auto;display: none">
				<div id="intputTree" style="width: 100%; height: 300px;"></div>
			</div>
			<script type="text/javascript">
                $(".dhxwin_active .content_div").height($(".dhxwin_active .content_td").height());
                $(".dhxwin_active .content_div").show();
			</script>
		</td>
	</tr>
	<tr valign="middle" height="46px">
		<td >
			<!-- editAfter begin-->
			<div class="bottomDiv bottomDivR">
				<button class='button-form' onclick="onAccLinkageSubmit()" id="confirmButton"><@i18n 'common_edit_ok'/></button>
				<button class='button-form' onclick="DhxCommon.closeWindow()" id="closeButton"><@i18n 'common_edit_cancel'/></button>
			</div>
			<!-- editAfter end-->
		</td>
	</tr>
</table>
<script type="text/javascript">
	// 触发条件-->树形菜单
	var triggerOptArray = null;
	var triggerOptTree = null;

	var getUrl = "/accLinkage.do?getLinkTriggerOpt&devId=" + $("#deviceId").val() + "&accLinkageId=" + $("#accLinkId").val();
	getLinkData(getUrl, "triggerOpt");
	initLinkTree("intputTree", triggerOptArray, "triggerOptTree");
	onlyCheckOneParent(triggerOptTree);
	validButton(triggerOptTree);

	function onAccLinkageSubmit()
	{
		setTriggerInfoToPage(triggerOptTree);
		buildPageData("triggerCond", "triggerCondName", "linkageTriggerCondName");
		getUrl = "/accLinkage.do?getInOutInfo&devId=" + $("#deviceId").val() + "&accLinkageId=" + $("#accLinkId").val() + "&triggerOpt=" + $("#triggerCond").val();
		getLinkData(getUrl);
		if(inputTypeNew != inputTypeOld || !inputTypeNew)
		{
			$("#linkageInputName").html("");// 切换触发条件，输入点需要重新init
			if (inputTypeOld)
			{
				getUrl = "/accLinkage.do?getInOutInfo&devId=" + $("#deviceId").val() + "&triggerOpt=" + $("#triggerCond").val();
				getLinkData(getUrl);
				messageBox({
					messageType : "alert",
					title : "<@i18n 'common_prompt_title'/>",
					text : "<@i18n 'acc_globalLinkage_reselectInput'/>"
				});
			}
			initLinkTree("linkageInputName", inputArray, "inputTree");
		}

		if (!inputTypeOld)
		{
			initLinkTree("selectedDoorName", doorOutputArray, "doorOutputTree");
            if (auxOutOutputArray != null && ((ctlAllRelayFunOn && auxOutOutputArray.item.length > 0) || (!ctlAllRelayFunOn && auxOutOutputArray.length > 0)))
            {
				initLinkTree("selectedAuxOutName", auxOutOutputArray, "auxOutOutputTree");
            }
		}
		inputTypeOld = inputTypeNew;
		DhxCommon.closeWindow();
	}

</script>
