<#assign formId = "${uuid!}">
<#include "/public/template/editTemplate.html">
<#macro editContent>
<script type="text/javascript">
    //输入点-->树形菜单
    var inputArray = null;
    var inputTree = null;

    // 输出点-->树形菜单
    var doorOutputArray = null;
    var doorOutputTree = null;
    var auxOutOutputArray = null;
    var auxOutOutputTree = null;
    var ctlAllRelayFunOn = null;

    var changeTrigger = false;// 判断是否改变了触发事件
    var inputTypeNew = null;//新的门、读头或辅助输入
    var inputTypeOld = null;//旧的门、读头或辅助输入

	/** 检测是否含有视频模块 */
    function checkExistVidModule() {
		if ("${systemModules}".toLowerCase().indexOf("vid") != -1 || "${systemModules}".toLowerCase().indexOf("vms") != -1
			|| "${systemModules}".toLowerCase().indexOf("ivs") != -1) {
    		return true;
    	}
    	return false;
    }
	function checkSupportVdbLinkage() {
		var result = false;
		<@ZKUI.Permission name="vdb">
			result = true;
		</@ZKUI.Permission>
		if (result && "${supportSip}" == "false" && "${systemModules}".toLowerCase().indexOf("vdb") != -1) {
			result = true;
		} else {
			result = false;
		}
		return result;
	}
    tabbar = new dhtmlXTabBar("linkageTabbar", "top");
    tabbar.addTab("output","<@i18n 'common_linkIO_outputPoint'/>","140");
    tabbar.tabs("output").attachObject("linkageOutput");
    if (checkExistVidModule()) {
	    tabbar.addTab("video","<@i18n 'common_linkIO_videoLink'/>","140");
	    tabbar.tabs("video").attachObject("linkageVideo");
    }
    tabbar.addTab("mail","<@i18n 'common_linkIO_sendMail'/>","140");
    tabbar.tabs("mail").attachObject("linkageMail");
    // 入侵
    if ("${systemModules}".toLowerCase().indexOf("ias") != -1) {
		tabbar.addTab("ias","<@i18n 'base_linkage_ias_moduleName'/>","140");
	    tabbar.tabs("ias").attachObject("linkageIas");
    }
    //可视对讲

	if (checkSupportVdbLinkage()) {
		tabbar.addTab("vdb","<@i18n 'common_vdb_linkage'/>","180");
		tabbar.tabs("vdb").attachObject("linkageVdb");
	}

	<#if showSMS?exists && showSMS?string == "true">
        tabbar.addTab("sms","<@i18n 'common_linkIO_sendSMS'/>","140");
        tabbar.tabs("sms").attachObject("linkageSMS");
	</#if>
    <#if showDigifort?exists && showDigifort?string == "true">
        tabbar.addTab("digifort","<@i18n 'acc_digifort_linkage'/>","140");
    	tabbar.tabs("digifort").attachObject("digifortTab");
	</#if>
	<#if showLine?exists && showLine?string == "true">
        tabbar.addTab("line","<@i18n 'acc_line_linkage'/>","140");
        tabbar.tabs("line").attachObject("linkageLine");
	</#if>
	<#if showWhatsapp?exists && showWhatsapp?string == "true">
        tabbar.addTab("whatsapp","<@i18n 'acc_whatsapp_linkage'/>","140");
        tabbar.tabs("whatsapp").attachObject("linkageWhatsapp");
	</#if>
	tabbar.tabs("output").setActive();


    (function(){
        $("#${formId}").validate({
            debug: true,
            rules :
			{
				"mailAddr" :
					{
						moreMailValid : true,
						mailLenghthValid : true
					},
				"doorActionTime" :
					{
						range : [1, 254],
						digits : true
					},
				"auxoutActionTime" :
					{
						range : [1, 254],
						digits : true
					},
				"name" :
					{
						required : true,
						unInputChar:true,
						overRemote : ["accLinkage.do?valid", "${(item.name)!}"]
					},
				"popUpTime" :
					{
						required : true,
						range : [ 5, 60 ],
						digits : true
					},
				"recordBeforeTime": {
					required : true,
					range : [ 10, 180 ],
					digits : true
				},
				"recordTime": {
					required : true,
					range : [ 10, 180 ],
					digits : true
				},
				"captureTime" :
					{
						required : true,
						range : [ 10, 60 ],
						digits : true
					},
				"mobileNo" :{
						mobileNoValid : true,
						mobileNoLengthValid : true
					},
				"whatsappMobileNo" :
					{
						mobileNoValid : true,
						mobileNoLengthValid: true
					},
			},
            submitHandler : function()
            {
            	// 不存在视频，清除视频联动设置，修复升级去除原有的视频模块，导致未设置输出点等内容，可以保存成功
            	if (!checkExistVidModule()) {
            		$("#popUpVideo").attr("checked", false);
            		$("#record").attr("checked", false);
            		$("#linkCapture").attr("checked", false);
            		$("#popUpVideo_hidden").val("0");
            		$("#record_hidden").val("0");
            		$("#linkCapture_hidden").val("0");
            	}

                $("#${formId} #inputTree").val(inputTree == null ? "" : inputTree.getAllChecked());
                $("#${formId} #doorOutputTree").val(doorOutputTree == null ? "" : doorOutputTree.getAllChecked());
                $("#${formId} #auxOutOutputTree").val(auxOutOutputTree == null ? "" : auxOutOutputTree.getAllChecked());
                var checkRet = checkTriggerOpt();
                var supportVdb = ("${supportSip}" == "false") && ("${systemModules}".toLowerCase().indexOf("vdb") != -1);
                if($("#${formId} #deviceId").val() == "")
                {
                    messageBox({messageType : "alert", text : "<@i18n 'acc_dev_selectDev'/>"});
                }
                else if($("#${formId} #triggerCond").val() == "")
                {
                    messageBox({
                        messageType : "alert",
                        text : "<@i18n 'acc_globalLinkage_selectTrigger'/>"
                    });
                }
                else if(checkRet != true)
                {
                    var msg = "<@i18n 'common_linkIO_conditionExist'/>".format(checkRet);
                    messageBox({
                        messageType : "alert",
                        text : msg
                    });
                }
                else if($("#${formId} #inputTree").val() == "")
                {
                    messageBox({
                        messageType : "alert",
                        text : "<@i18n 'acc_globalLinkage_selectInput'/>"
                    });
                } else if (supportVdb && !checkVdbLinkageNotifyCount()) {
					messageBox({messageType: "alert", text: "<@i18n 'common_vdb_notificationObjectMaxCount'/>"});
                } else if (supportVdb && !checkVdbLinkageSet()) {
                	messageBox({messageType: "alert", text: "<@i18n 'common_vdb_notificationObjectFail'/>"});
                } else if(!checkVidLinkage()) {
                    messageBox({
                        messageType : "alert",
                        text : "<@i18n 'acc_globalLinkage_selectAtLeastOne'/>"
                    });
                }
                else
                {
                	// 去掉提交联动触发条件名称,避免名称含特殊符号被过滤
                	$("#triggerCondName").val("");

                	// 更新入侵分区重新选择后值
                	var iasPartitionTree = ZKUI.Tree.get("iasPartitionTreeId");
                	if (iasPartitionTree) {
                		var iasPartitionIds = iasPartitionTree.getValue();
						$("#iasPartitionIds${uuid}").val(iasPartitionIds);
                	}

                	<@submitHandler />
                }
            }
        });
        if($("#${formId} #deviceId").val() != "" && typeof($("#${formId} #deviceId").val()) != "undefined")
        {
            getUrl = "accLinkage.do?getInOutInfo&devId=" + $("#${formId} #deviceId").val() + "&accLinkageId=" + $("#${formId} #accLinkId").val() + "&triggerOpt=" + $("#${formId} #triggerCond").val();
            getLinkData(getUrl);
            if(inputTypeNew != inputTypeOld || !inputTypeNew)
            {
                $("#${formId} #inputInfoDiv").html("");// 切换触发条件，输入点需要重新init
                initLinkTree("linkageInputName", inputArray, "inputTree");
            }
            else
            {
                initLinkTree("linkageInputName", inputArray, "inputTree");
            }
            inputTypeOld = inputTypeNew;
            initLinkTree("selectedDoorName", doorOutputArray, "doorOutputTree");
            if (auxOutOutputArray != null && ((ctlAllRelayFunOn && auxOutOutputArray.item.length > 0) || (!ctlAllRelayFunOn && auxOutOutputArray.length > 0)))
            {
            	initLinkTree("selectedAuxOutName", auxOutOutputArray, "auxOutOutputTree");
            }
        }

        var opts = {//弹窗配置对象
            path: "skip.do?page=acc_linkage_accLinkageSelectDeviceRadio",//设置弹窗路径
            width: 920,//设置弹窗宽度
            height: 500,//设置弹窗高度
            title: "<@i18n 'common_linkIO_selectDevice'/>",//设置弹窗标题
            onSure:"selectDeviceHandler"//回调事件
        };
        selectContent("#deviceName", opts);//给按钮注册选人事件

    	<#if (item.id)??>
        	$("#${formId} #deviceName").attr("disabled", true);
    	<#else>
        	accTimeDisableByIds('#linkCapture','#capturePop','#captureTime');
    	</#if>

        $("#${formId} #deviceId").change(function(){
            cleanAllData();
        });

        checkLinkMailParam();//验证发件邮箱是否设置
		<#if "${showSMS}">
			checkLinkSMSParam();
		</#if>
        <#if "${showDigifort}">
            if ($("#accLinkId").val() != "")
            {
                buildDigifortEvents("digiEventIds", "digiEventNames", "digifortGlobalEventsList");
            }
            else
            {
                buildPageData("digiEventIds", "digiEventNames", "digifortGlobalEventsList");
            }
        </#if>
        buildPageData("triggerCond", "triggerCondName", "linkageTriggerCondName");
        isSupportLockDoor($("#${formId} #deviceId").val());
        checkedDoorOutActionType('${(item.doorActionType)!0}','');
        checkedAuxoutActionType('${(item.auxoutActionType)!0}','');
    })();

    function selectDeviceHandler(value,text,event){
        var oldId = $("#${formId} #deviceId").val();
        $("#${formId} #deviceId").val(value);
        if (oldId != value)
        {
            $("#${formId} #deviceId").change();
        }
        $("#${formId} #deviceName").val(text);
        $("#${formId} #deviceId").valid();
    }

    function isSupportLockDoor(devId)
    {
        if (devId != "")
        {
            $.ajax({
                type : "POST",
                url : "accLinkage.do?isSupportLockDoor&devId=" + devId,
                dataType : "json",
                async : true,
                success : function(result)
                {
                    if (result.data)
                    {
                        ZKUI.Combo.get("doorActionType${uuid!}").combo.filter(function (opt) {
                            return true;
                        }, false)
                    }
                    else
                    {
                        ZKUI.Combo.get("doorActionType${uuid!}").combo.filter(function (opt) {
                            var content = opt.text;
                            if (content == I18n.getValue("common_lock") || content == I18n.getValue("common_unlock")) {
                                return false;
                            }else {
                                return true;
                            }
                        }, false)
                        //$("#doorActionType option[value='2'],#doorActionType option[value='3']").attr("disabled", true);
                    }
                    if ($("#accLinkId").val() == "")
                    {
                        $("#doorActionType").val("0");
                    }
                    $("#doorActionType").change();
                }
            });
        }
    }

    function linkageOpenWindow(path,width,height,title) {
        var opts = {
            path : path,
            width : width,
            height : height,
            title : I18n.getValue(title),
            gridName : "gridbox"
        }
        DhxCommon.createWindow(opts);
    }

    function accTimeDisableByIds(obj,idcapturePop,idCaptureTime)
    {
        if (obj.checked)
        {
            $(idcapturePop).attr('disabled', false);
        }
        else
        {
            $(idcapturePop).attr('disabled', true);
            $(idcapturePop).attr('checked', false);
            $(idCaptureTime).attr('disabled', true);
            $(idCaptureTime).val($(idCaptureTime).attr('default'));
            $(idCaptureTime).valid();
        }
    }

    // 检测输出动作类型，关闭、打开、常开
    function checkedDoorOutActionType(selectVal, checkObjId)
    {
        if (selectVal == '0' || selectVal == '255' || selectVal == '2' || selectVal == '3')
        {
            $("#${formId} #doorActionTime").rules("remove");
            $("#${formId} #doorActionTime").parent().parent().hide();
            $("#${formId} #doorActionTime").val((selectVal == '2' || selectVal == '3') ? 0 : selectVal);
        }
        else
        {
            $("#${formId} #doorActionTime").rules("add", {
                required : true,
                range : [1, 254]
            });
            $("#${formId} #doorActionTime").parent().parent().show();
            var actionTime = $("#${formId} #doorActionTime").val();
            actionTime = (actionTime == 0 || actionTime == 255) ? 20 : actionTime;
            $("#${formId} #doorActionTime").val(actionTime);
        }
    }

    function checkedAuxoutActionType(selectVal, checkObjId) {
        if (selectVal == '0' || selectVal == '255' || selectVal == '2' || selectVal == '3')
        {
            $("#${formId} #auxoutActionTime").rules("remove");
            $("#${formId} #auxoutActionTime").parent().parent().hide();
            $("#${formId} #auxoutActionTime").val((selectVal == '2' || selectVal == '3') ? 0 : selectVal);
        }
        else
        {
            $("#${formId} #auxoutActionTime").rules("add", {
                required : true,
                range : [1, 254]
            });
            $("#${formId} #auxoutActionTime").parent().parent().show();
            var actionTime = $("#${formId} #auxoutActionTime").val();
            actionTime = (actionTime == 0 || actionTime == 255) ? 20 : actionTime;
            $("#${formId} #auxoutActionTime").val(actionTime);
        }
    }

    function addTriggerCondition() {
        if($("#deviceId").val() == "")
        {
            messageBox({messageType : "alert", text : I18n.getValue("acc_dev_selectDev")});
        }
        else
        {
            var opts = {//弹窗配置对象
                path:"skip.do?page=acc_linkage_opAccLinkageTriggerCond",//弹窗路径
                title:"<@i18n 'common_linkIO_selectCondition'/>",//弹窗标题
                width:520,//窗口宽度
                height:400,//窗口高度
                gridName: "gridbox"//设置grid
            }
            DhxCommon.createWindow(opts);
        }
    }

    //屏蔽回车事件
    function removeLinkageEnterEvent(e) {
        var code;
        if (!e) var e = window.event;
        if (e.keyCode) code = e.keyCode;
        else if (e.which) code = e.which;
        if (code == 13 && window.event) {
            e.returnValue = false;
        } else if (code == 13) {
            e.preventDefault();
        }
    }

	function lineContactSelectChange() {
		var lineContactIds = "";
		var lineContactFilterIds = "";
		$("#linkageLineContacts input:checked").each(function(){
			lineContactIds += $(this).val() + ",";
		});
		$("#linkageLineContacts input").each(function(){
			lineContactFilterIds += $(this).val() + ",";
		});
		$("#lineContactId").val(lineContactIds.replace(/,$/,""));
		$("#lineContactFilterIds").val(lineContactFilterIds.replace(/,$/,""));
	}

    function addLineContact() {
		var opts = {//选人控件弹窗配置
			path:"skip.do?page=acc_linkage_selectLineContactContent&selectValue="+$("#lineContactFilterIds").val(),//弹窗路径
			title:"<@i18n 'acc_line_addContact' />",//弹窗标题
			width:800,//窗口宽度
			height:500,
			onSure:"selectContactCallback"
		}
		DhxCommon.createWindow(opts);

    }

    function selectContactCallback(value, text, event)
	{
		var valArr = value.split(",");
		var textArr = text.split(",");
		var oldValArr = {};
		var $input = $("#linkageLineContacts input");
		if($input.length > 0){
			$input.each(function(){
				oldValArr[$(this).val()] = $(this).val();
			});
		}else{
			$("#linkageLineContacts").html("");
		}
		$.each(valArr,function(i,v){
			if(oldValArr[v] == undefined){
				$("#linkageLineContacts").append("<label><span id='selectedLineContactSpan" + v + "'></span></label>" + textArr[i] + "<br>");
				loadUIToDiv("input", "#selectedLineContactSpan" + v, {
					useInputReq:true,
					hideLabel:true,
					type:"checkbox",
					value:v,
					trueValue: v,
					onchange:"lineContactSelectChange()",
					eventCheck:true
				});
			}
		});
		window.setTimeout(function () {
			lineContactSelectChange();
		}, 200);
	}

	function addVdbExtension() {
		var opts = {//选人控件弹窗配置
			path:"skip.do?page=acc_linkage_selectVdbExtensionContent&selectValue="+$("#vdbExtensionFilterIds").val(),//弹窗路径
			title:"<@i18n 'common_vdb_addBindObject' />",//弹窗标题
			width:950,//窗口宽度
			height:500,
			onSure:"selectBindObjectCallback"
		}
		DhxCommon.createWindow(opts);

    }

    function selectBindObjectCallback(value, text, event)
	{
		var rightGrid = ZKUI.Grid.get("rightaccLinkageSelectVdbExtensionContent");
		var valArr = value.split(",");
		var textArr = text.split(",");
		var oldValArr = {};
		var $input = $("#linkageVdbExtensions input");
		if($input.length > 0){
			$input.each(function(){
				oldValArr[$(this).val()] = $(this).val();
			});
		}else{
			$("#linkageVdbExtensions").html("");
		}
		$.each(valArr,function(i,v){
			var row = rightGrid.grid.getRowData(v);
			var extensionInfo = row.extensionName + " (" + textArr[i]+ ")" ;
			if(oldValArr[v] == undefined){
				$("#linkageVdbExtensions").append("<label><span id='selectedVdbExtensionSpan" + v + "'></span></label>" + extensionInfo + "<br>");
				loadUIToDiv("input", "#selectedVdbExtensionSpan" + v, {
					useInputReq:true,
					hideLabel:true,
					type:"checkbox",
					value:v,
					trueValue: v,
					onchange:"vdbExtensionSelectChange()",
					eventCheck:true
				});
			}
		});
		window.setTimeout(function () {
			vdbExtensionSelectChange();
		}, 200);
	}


	function vdbExtensionSelectChange() {
		var vdbExtensionIds = "";
		var vdbExtensionFilterIds = "";
		$("#linkageVdbExtensions input:checked").each(function(){
			vdbExtensionIds += $(this).val() + ",";
		});
		$("#linkageVdbExtensions input").each(function(){
			vdbExtensionFilterIds += $(this).val() + ",";
		});
		$("#vdbExtensionId").val(vdbExtensionIds.replace(/,$/,""));
		$("#vdbExtensionFilterIds").val(vdbExtensionFilterIds.replace(/,$/,""));
	}

    $("#popUpVideo").click(function() {
		accTimeDisableById(this,'#popUpTime');
	});

	$("#record").click(function() {
		accTimeDisableById(this,'#recordBeforeTime');
		accTimeDisableById(this,'#recordTime');
	});

	$("#linkCapture").click(function() {
		accTimeDisableByIds(this,'#capturePop','#captureTime');
	});

	$("#capturePop").click(function() {
		accTimeDisableById(this,'#captureTime');
	});
</script>
<form action='accLinkage.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
	<input type="hidden" id="accLinkId" name="id" value="${(item.id)!}" onchange="isSupportLockDoor(this.value)"/>
	<table style="margin-bottom: 10px">
		<td width="150px">
			<label><@i18n 'common_linkIO_linkageName'/></label><span class="required">*</span>
		</td>
		<td width="240px">
			<input id="accLinkageName" type="text" name="name" value="${(item.name)!}" onkeyup="$(this).valid()" maxlength="50">
		</td>
		<td width="150px"><label><@i18n 'common_dev_entity'/></label><span class='required'>*</span></td>
		<td>
			<input type="hidden" id="deviceId" name="deviceId" value="${(item.deviceId)!}" onchange="isSupportLockDoor(this.value)"/>
			<#if (item.id)??>
			<input type="text" id="deviceName" value="${(item.deviceAlias)!}" readonly="readonly"/>
			<#else>
			<input type="text" id="deviceName" value="${(item.deviceAlias)!}" readonly="true" placeholder="<@i18n 'common_op_clickChoice'/>"/>
			</#if>
		</td>
	</table>

	<input type="hidden" id="triggerCond" name="triggerCond" value="${(item.triggerCond)!}"/>
	<input type="hidden" id="triggerCondName" name="triggerCondName" value="${(item.triggerCondName)!}"/>
	<div id="linkageTriggerCond" align="${leftRTL!'left'}" style="width: 49%; height: 200px; float:${leftRTL!'left'};">
		<div style="width: 100%; height:10%; margin-top: 10px;">
			<h1 style="width: 100%; height: 100%; margin-bottom: 10px;">
				<@i18n 'common_linkIO_conditions'/><span class="required" style="margin-${rightRTL!'right'}: 10px">*</span>
				<a href="javascript:addTriggerCondition()"><@i18n 'common_op_add'/></a>
			</h1>
		</div>
		<div id="linkageTriggerCondName" style="width: 100%; height: 85%; background-color: #ffffff; overflow: auto; border: 1px solid #c3c2c2; padding-${leftRTL!'left'}: 2px;">
		</div>
	</div>

	<input id="inputTree" type="hidden" name="inputAddr" value="${(inputAddr)!}"/>
	<input id="inputType" type="hidden" name="inputType" value="${(item.inputType)!}"/>
	<input id="inputName" type="hidden" name="inputName" value="${(inputName)!}"/>
	<div id="linkageInput" align="center" style="width: 49%; height: 200px; text-align: ${leftRTL!'left'}; float:${leftRTL!'left'}; margin-${leftRTL!'left'}: 1.5%">
		<div style="width: 100%; height:10%; margin-top: 10px;">
			<h1 style="width: 100%; height: 100%; margin-bottom: 10px;">
				<@i18n 'common_linkIO_inputPoint'/><span class="required">*</span>
			</h1>
		</div>
		<div id="linkageInputName" style="width: 100%; height: 85%; background-color: #ffffff; overflow: auto; border: 1px solid #c3c2c2; padding-${leftRTL!'left'}: 2px;">
		</div>
	</div>

	<input id="doorOutputTree" type="hidden" name="outputDoorAddr" value="${(outputDoorAddr)!}"/>
	<input id="auxOutOutputTree" type="hidden" name="outputAuxOutAddr" value="${(outputAuxOutAddr)!}"/>
	<input id="outputDoorName" type="hidden" name="outputDoorName" value="${(outputDoorName)!}"/>
	<input id="outputAuxOutName" type="hidden" name="outputAuxOutName" value="${(outputAuxOutName)!}"/>
	<div id="linkageOutput" align="center" style="width: 100%; height: 250px; text-align: ${leftRTL!'left'}; float: ${leftRTL!'left'};">
		<div id="selectedDoor" style="width: 49%; height: 80%; float: ${leftRTL!'left'}; background-color: #f3f5f0">
			<div style="width: 100%; height:10%; margin-top: 10px;">
				<h1 style="width: 100%; height: 100%; margin-top: 2px;">
					<@i18n 'acc_door_entity'/>
				</h1>
			</div>
			<div style="width: 100%; height:80%; background-color: #ffffff;">
				<div id="selectedDoorName" style="width: 100%; height: 100%; overflow: auto; border: 1px solid #c3c2c2; padding-${leftRTL!'left'}: 2px;"></div>
			</div>
		</div>
		<div id="selectedAuxOut" style="width: 49%; height: 80%; float: ${leftRTL!'left'}; margin-${leftRTL!'left'}: 1.5%; background-color: #f3f5f0">
			<div style="width: 100%; height:10%; margin-top: 10px;">
				<h1 style="width: 100%; height: 100%; margin-top: 2px;">
					<@i18n 'acc_leftMenu_auxOut'/>
				</h1>
			</div>
			<div style="width: 100%; height:80%; background-color: #ffffff;">
				<div id="selectedAuxOutName" style="width: 100%; height: 100%; overflow: auto; border: 1px solid #c3c2c2; padding-${leftRTL!'left'}: 2px;"></div>
			</div>
		</div>
		<div id="linkageAction" style="width: 100%; height: 20%; float: ${leftRTL!'left'};">
			<div style="width: 50%; height: 100%; float: ${leftRTL!'left'};">
				<table class="tableStyle">
					<tr style="display: table-row;">
						<th align="${leftRTL!'left'}" style="width: 120px">
							<label><@i18n 'common_linkIO_actionType'/></label>
						</th>
						<td>
							<@ZKUI.Combo width="148" empty="false" id="doorActionType${uuid!}" name="doorActionType" onChange="checkedDoorOutActionType" hideLabel="true" readonly="readonly" value="${(item.doorActionType)!}">
								<option value="0" selected="selected"><@i18n 'common_close'/></option>
								<option value="1"><@i18n 'common_open'/></option>
								<option value="255"><@i18n 'common_normalOpen'/></option>
								<option value="2"><@i18n 'common_lock'/></option>
								<option value="3"><@i18n 'common_unlock'/></option>
							</@ZKUI.Combo>
						</td>
					</tr>
					<tr style="display: none">
						<th align="${leftRTL!'left'}" style="width: 120px">
							<@i18n 'common_linkIO_actionDelay'/><span class='required'>*</span>
						</th>
						<td>
							<input type="text" name="doorActionTime" value="${item.doorActionTime!}" id="doorActionTime" maxlength="3" onkeyup="$(this).valid();this.value=this.value.replace(/\D/g,'');" onafterpaste="this.value=this.value.replace(/\D/g,'')">
							<span class="gray"> <@i18n 'common_linkIO_s'/>(1-254)</span>
						</td>
					</tr>
				</table>
			</div>
			<div style="width: 50%; height: 100%; float: left;">
				<table class="tableStyle">
					<tr style="display: table-row;">
						<th align="${leftRTL!'left'}" style="width: 120px">
							<label><@i18n 'common_linkIO_actionType'/></label>
						</th>
						<td>
							<@ZKUI.Combo width="148" empty="false" id="auxoutActionType${uuid!}" name="auxoutActionType" onChange="checkedAuxoutActionType" hideLabel="true" readonly="readonly" value="${(item.auxoutActionType)!}">
								<option value="0"><@i18n 'common_close'/></option>
								<option value="1"><@i18n 'common_open'/></option>
								<option value="255"><@i18n 'common_normalOpen'/></option>
							</@ZKUI.Combo>
						</td>
					</tr>
					<tr style="display: none">
						<th align="${leftRTL!'left'}" style="width: 120px">
							<@i18n 'common_linkIO_actionDelay'/><span class='required'>*</span>
						</th>
						<td>
							<input type="text" name="auxoutActionTime" value="${item.auxoutActionTime!}" id="auxoutActionTime" maxlength="3" onkeyup="$(this).valid();this.value=this.value.replace(/\D/g,'');" onafterpaste="this.value=this.value.replace(/\D/g,'')">
							<span class="gray"> <@i18n 'common_linkIO_s'/>(1-254)</span>
						</td>
					</tr>
				</table>
			</div>
		</div>
	</div>

	<div id="linkageVideo" align="center" style="width: 98%; height: 130px;display:none">
		<fieldset style="width: 100%; text-align: ${leftRTL!'left'}; margin-top: 10px; background-color: #ffffff">
			<table class="tableStyle">
				<tr>
					<th>
						<#if item.popUpVideo?exists>
							<@ZKUI.Input hideLabel="true" type="checkbox" id="popUpVideo" name="popUpVideo" value="${(item.popUpVideo)!}" trueValue="1" falseValue="0" eventCheck=true/>
						<#else>
							<@ZKUI.Input hideLabel="true" type="checkbox" id="popUpVideo" name="popUpVideo" trueValue="1" falseValue="0" eventCheck=true/>
						</#if>
						<label><@i18n 'common_linkIO_popUpVideo'/></label>
					</th>
					<td style="width: 110px">
						<span><@i18n 'common_linkIO_displayTime'/></span>
					</td>
					<td>
						<input type="text" id="popUpTime" name="popUpTime" value="${item.popUpTime!10}" maxlength="2" default="10" <#if !(item.popUpTime?exists)>disabled="disabled"</#if>/>
					<span class="gray"> <@i18n 'common_linkIO_s'/>(5-60)</span>
					</td>
				</tr>
				<tr>
					<th>
						<#if item.record?exists>
							<@ZKUI.Input hideLabel="true" type="checkbox" id="record" name="record" value="${(item.record)!}" trueValue="2" falseValue="0" eventCheck=true/>
						<#else>
							<@ZKUI.Input hideLabel="true" type="checkbox" id="record" name="record" trueValue="2" falseValue="0" eventCheck=true/>
						</#if>
						<label><@i18n 'common_linkIO_video'/></label>
					</th>
					<td style="width: 110px;">
						<span><@i18n 'common_linkIO_beforeTime'/></span>
					</td>
					<td>
						<input type="text" id="recordBeforeTime" name="recordBeforeTime" value="${item.recordBeforeTime!10}" maxlength="3" default="10" <#if !(item.recordBeforeTime?exists)>disabled="disabled"</#if>/>
						<span class="gray"> <@i18n 'common_linkIO_s'/>(10-180)</span>
					</td>
				</tr>
				<tr>
					<th><input type="hidden" /></th>
					<td style="width: 110px;">
						<span><@i18n 'common_linkIO_afterTime'/></span>
					</td>
					<td>
						<input type="text" id="recordTime" name="recordTime" value="${item.recordTime!30}" maxlength="3" default="30" <#if !(item.recordTime?exists)>disabled="disabled"</#if>/>
					<span class="gray"> <@i18n 'common_linkIO_s'/>(10-180)</span>
					</td>
				</tr>
				<tr>
					<th>
						<#if item.capture?exists>
							<@ZKUI.Input hideLabel="true" type="checkbox" id="linkCapture" name="capture" value="${(item.capture)!}"
								trueValue="3" falseValue="0" eventCheck=true/>
						<#else>
							<@ZKUI.Input hideLabel="true" type="checkbox" id="linkCapture" name="capture"
								trueValue="3" falseValue="0" eventCheck=true/>
						</#if>
						<label><@i18n 'common_linkIO_photograph'/></label>
					</th>
					<td style="width: 110px;" colspan="2">
						<#if item.captureTime?exists>
							<@ZKUI.Input hideLabel="true" type="checkbox" id="capturePop" name="capturePop" value="${(item.capture)!}"
								trueValue="3" falseValue="0" eventCheck=true/>
						<#else>
							<@ZKUI.Input hideLabel="true" type="checkbox" id="capturePop" name="capturePop" trueValue="3" falseValue="0" eventCheck=true/>
						</#if>
						<label><@i18n 'common_linkIO_capturePop'/></label>
					</td>
				</tr>
				<tr>
					<th><input type="hidden" /></th>
					<td style="width: 110px">
						<span><@i18n 'common_linkIO_displayTime'/></span>
					</td>
					<td><input type="text" id="captureTime" name="captureTime" value="${item.captureTime!10}" maxlength="3" default="10" <#if !(item.captureTime?exists)>disabled="disabled"</#if>/>
					<span class="gray"> <@i18n 'common_linkIO_s'/>(10-60)</span>
					</td>
				</tr>
			</table>
		</fieldset>
		<div style="margin-top: 10px; text-align: ${leftRTL!'left'};">
			<span class="warningImage"></span>
			<span class="warningColor"><@i18n 'acc_common_vidlinkageTip'/></span>
			<#if systemModules?lower_case?index_of("vms") != -1>
				<span class="warningColor"><@i18n 'common_vid_linkageTip'/></span>
			</#if>
		</div>
	</div>

	<div id="linkageMail" align="${leftRTL!'left'}" style="width: 98%; height: 200px;">
		<div style="margin-top: 10px; margin-bottom: 2px;">
			<@i18n 'common_linkIO_mailAddr'/>
		</div>
		<fieldset style="width: 100%; text-align: ${leftRTL!'left'};  background-color: #ffffff">
			<textarea id="id_mailAddr" rows="4" style="width: 100%; border: 0px;" name="mailAddr" placeholder="<@i18n 'common_email_example'/>" onkeydown="removeLinkageEnterEvent(event)">${item.mailAddr!}</textarea>
		</fieldset>
		<div style="margin-top: 10px; text-align: ${leftRTL!'left'};"><span class="warningImage"></span> <span class="warningColor"><@i18n 'common_email_setMailTip'/></span></div>
		<div id="setMailParam" style="margin-top: 10px; text-align: ${leftRTL!'left'}; display: none;">
			<span class="warningImage"></span><span class="warningColor"><@i18n 'common_email_checkMailServer'/></span>
			<@ZKUI.Permission name="system:mail:getMailParam">
			<a href="javascript:linkageOpenWindow('baseMail.do?getMailParam',600,520,'base_mail_sendServerSetting')"><@i18n 'common_email_jumpToSetMail'/></a>
			</@ZKUI.Permission>
		</div>
	</div>
	<!--可视对讲配置-->
	<@ZKUI.Permission name="vdb">
	<#if systemModules?lower_case?index_of("vdb") != -1 && supportSip == "false">
	<div id="linkageVdb" align="${leftRTL!'left'}" style="width: 99%; height: 200px; float:${leftRTL!'left'};">
		<div style="width: 100%; height:10%; margin-top: 10px;">
			<h1 style="width: 100%; height: 100%; margin-top: 2px;">
				<label style="margin-${rightRTL!'right'}:10px;"><@i18n 'common_vdb_IVR'/></label>
				<@ZKUI.Combo width="148" id="vdbIvrCombo" hideLabel="true" empty="true" name="vdbIvrId" value='${(item.vdbIvrId)!}' path="accLinkage.do?getVdbIvr"></@ZKUI.Combo>
			</h1>
		</div>
		<input type="hidden" id="vdbExtensionId" name="vdbExtensionId" value="${(item.vdbExtensionId)!}"/>
		<input type="hidden" name="vdbExtensionFilterIds" id="vdbExtensionFilterIds" value="${(item.vdbExtensionId)!}" />
		<div style="width: 100%; height:10%; margin-top: 20px;">
			<h1 style="width: 100%; height: 100%; margin-top: 2px;">
				<@i18n 'common_vdb_notificationObject'/>
				<a href="javascript:addVdbExtension()"><@i18n 'common_op_add'/></a>
			</h1>
		</div>
		<div id="linkageVdbExtensions" style="width: 100%; height: 80%; background-color: #ffffff; overflow: auto; border: 1px solid #c3c2c2; padding-${leftRTL!'left'}: 2px;">
			<#if vdbExtensionList??>
			<#if (vdbExtensionList?size>0)>
			<#list vdbExtensionList as bindObject>
			<label><@ZKUI.Input hideLabel="true" type='checkbox' value='${bindObject.id}' onchange='vdbExtensionSelectChange()' checked='checked' style='margin-right:3px;'/>${bindObject.extensionNumber}</label><br>
			</#list>
			</#if>
			</#if>
		</div>
		<div style="margin-top: 10px; text-align: ${leftRTL!'left'};"><span class="warningImage"></span> <span class="warningColor"><@i18n 'common_vdb_notificationTip'/></span></div>
	</div>
	</#if>
	</@ZKUI.Permission>
	<!--入侵配置-->
	<#if systemModules?lower_case?index_of("ias") != -1>
	<div id="linkageIas" align="${leftRTL!'left'}" style="height: 200px;">
		<input type="hidden" id="iasPartitionIds${uuid}" name="iasPartitionIds" value="${(item.iasPartitionIds)!}"/>
		<input type="hidden" id="iasManufacture${uuid}" name="iasManufacture" value="${(item.iasManufacture)!'BOSCH'}"/>
		<div style="width: 100%; height:10%; margin-top: 10px;">
			<span style="width: 100%; height: 100%; margin-top: 2px;">
				<@i18n 'base_linkage_ias_partition'/>
			</span>
			<a class="" href="javascript:accSelectIasPartition()"><@i18n 'common_op_add'/></a>
		</div>
		<div id="linkageIasContent" style="width: 100%; height:80%; background-color: #ffffff;">
			<div id="selectedPartitionNames" style="height: 100%; overflow: auto; border: 1px solid #c3c2c2;">
				<@ZKUI.Tree id="iasPartitionTreeId" value="${(item.iasPartitionIds)!}" url="accLinkage.do?getIasPartitionTreeByIds&iasPartitionIds=${(item.iasPartitionIds)!}" type="checkbox"></@ZKUI.Tree>
			</div>
		</div>
		<div style="width: 50%; float: ${leftRTL!'left'}; margin-top: 5px;">
			<table class="tableStyle">
				<tr style="display: table-row;">
					<th align="${leftRTL!'left'}" style="width: 120px">
						<label><@i18n 'common_linkIO_actionType'/></label>
					</th>
					<td>
						<@ZKUI.Combo width="148" empty="false" id="partitionActionType${uuid!}" name="partitionActionType" onChange="checkedPartitionActionType" hideLabel="true" readonly="readonly" value="${(item.partitionActionType)!}">
							<option value="0" selected="selected"><@i18n 'base_linkage_ias_partitionArm'/></option>
							<option value="1"><@i18n 'base_linkage_ias_disarm'/></option>
						</@ZKUI.Combo>
					</td>
				</tr>
				<tr id="armTypeTr${uuid}">
					<th align="${leftRTL!'left'}" style="width: 120px">
						<@i18n 'base_linkage_ias_armType'/>
					</th>
					<td>
						<@ZKUI.Combo width="148" empty="false" autoFirst="true" id="partitionArmType${uuid!}" name="partitionArmType" hideLabel="true" readonly="readonly"
							value="${(item.partitionArmType)!''}" path="accLinkage.do?getArmTypeByManufacture&manufacture=${(item.iasManufacture)!'BOSCH'}">
						</@ZKUI.Combo>
					</td>
				</tr>
			</table>
		</div>
	</div>
	</#if>
	<#if showSMS?exists && showSMS?string == "true">
	<div id="linkageSMS" align="${leftRTL!'left'}" style="width: 98%; height: 200px;">
		<div style="margin-top: 10px; margin-bottom: 2px;">
			<@i18n 'common_linkIO_mobileNo'/>
		</div>
		<fieldset style="width: 100%; text-align: ${leftRTL!'left'};  background-color: #ffffff">
			<textarea id="id_mobileNo" rows="4" style="width: 100%; border: 0px;" name="mobileNo" placeholder="<@i18n 'common_smsModem_example'/>" onkeydown="removeLinkageEnterEvent(event)">${item.mobileNo!}</textarea>
		</fieldset>
		<div style="margin-top: 10px; text-align: ${leftRTL!'left'};"><span class="warningImage"></span> <span class="warningColor"><@i18n 'common_smsModem_setMobileNoTip'/></span></div>
		<div id="setSMSModemParam" style="margin-top: 10px; text-align: ${leftRTL!'left'}; display: none;">
			<span class="warningImage"></span><span class="warningColor"><@i18n 'common_smsModem_checkSMS'/></span>
			<@ZKUI.Permission name="smsModem:message:paramSetting">
			<a href="javascript:linkageOpenWindow('smsModemMessage.do?getSmsModemMessageParam',500,450,'smsModem_message_paramSetting')"><@i18n 'common_smsModem_jumpToSetSMS'/></a>
			</@ZKUI.Permission>
		</div>
	</div>
	</#if>
	<#if showDigifort?exists && showDigifort?string == "true">
	<div id="digifortTab" align="${leftRTL!'left'}" style="width: 99%; height: 200px;">
		<input type="hidden" id="digiEventIds" name="digiEventIds" value="${(item.digifortEventName)!}"/>
		<input type="hidden" id="digiEventNames" name="digiEventNames" value="${(item.digifortEventName)!}"/>
		<div style="width: 100%; height:10%; margin-top: 10px;">
			<h1 style="width: 100%; height: 100%; margin-bottom: 10px;">
				<@i18n 'acc_digifort_globalEvent'/>&nbsp;&nbsp;&nbsp;&nbsp;
				<a href="javascript:addDigifortGlobalEvent()"><@i18n 'common_op_add'/></a>
			</h1>
		</div>
		<div id="digifortGlobalEventsList" style="width: 100%; height: 85%; background-color: #ffffff; overflow: auto; border: 1px solid #c3c2c2; padding-left: 2px;">
		</div>
		<div style="margin-top: 10px; text-align: left;"><span class="warningImage"></span> <span class="warningColor"><@i18n 'acc_digifort_eventExpiredTip'/></span></div>
	</div>
	</#if>
	<#if showLine?exists && showLine?string == "true">
	<div id="linkageLine" align="${leftRTL!'left'}" style="width: 99%; height: 200px; float:${leftRTL!'left'};">
		<input type="hidden" id="lineContactId" name="lineContactId" value="${(item.lineContactId)!}"/>
		<input type="hidden" name="lineContactFilterIds" id="lineContactFilterIds">
		<div style="width: 100%; height:10%; margin-top: 10px;">
			<h1 style="width: 100%; height: 100%; margin-top: 2px;">
				<@i18n 'system_systemClient_contacts'/>
				<a href="javascript:addLineContact()"><@i18n 'common_op_add'/></a>
			</h1>
		</div>
		<div id="linkageLineContacts" style="width: 100%; height: 85%; background-color: #ffffff; overflow: auto; border: 1px solid #c3c2c2; padding-${leftRTL!'left'}: 2px;">
			<#if contactList??>
			<#if (contactList?size>0)>
			<#list contactList as lineContact>
			<label><@ZKUI.Input hideLabel="true" type='checkbox' value='${lineContact.id}' onchange='lineContactSelectChange()' checked='checked' style='margin-right:3px;'/>${lineContact.contact}</label><br>
			</#list>
			</#if>
			</#if>
		</div>
	</div>
	</#if>
	<#if showWhatsapp?exists && showWhatsapp?string == "true">
	<div id="linkageWhatsapp" align="${leftRTL!'left'}" style="width: 98%; height: 200px;">
		<div style="margin-top: 10px; margin-bottom: 2px;">
			<@i18n 'common_linkIO_mobileNo'/>
		</div>
		<fieldset style="width: 100%; text-align: ${leftRTL!'left'};  background-color: #ffffff">
			<textarea id="id_whatsappMobileNo" rows="4" style="width: 100%; border: 0px;" name="whatsappMobileNo" placeholder="<@i18n 'common_smsModem_example'/>" onkeydown="removeLinkageEnterEvent(event)">${item.whatsappMobileNo!}</textarea>
		</fieldset>
		<div style="margin-top: 10px; text-align: ${leftRTL!'left'};"><span class="warningImage"></span> <span class="warningColor"><@i18n 'common_smsModem_setMobileNoTip'/></span></div>
	</div>
	</#if>
	<div id="linkageTabbar" style="width: 100%; height:300px; padding: 0px; float:${leftRTL!'left'}; margin-top: 10px;"></div>
</form>
<script type="text/javascript">
	window.setTimeout(function () {
    	initAccLinkage();
    },100);

	function initAccLinkage() {
		if($("#${formId} #captureTime").val() == 0) {
			$("#${formId} #capturePop").attr('checked', false);
			accTimeDisableById('#capturePop','#captureTime');
		}
		if($("#${formId} #linkCapture").attr("checked") == "checked") {
			$("#${formId} #capturePop").attr('disabled', false);
		} else {
			$("#${formId} #capturePop").attr('disabled', true);
		}
	}

	// 添加入侵分区
	function accSelectIasPartition() {
		var iasPartitionIds = $("#iasPartitionIds${uuid}").val();
		var opts = {
			path: "skip.do?page=acc_linkage_accLinkageSelectIasPartition&filterId=" + iasPartitionIds,
			title: "<@i18n 'base_linkage_ias_addPartition'/>",
			width: 920,
			height: 500,
		}
		DhxCommon.createWindow(opts);
	}

	// 添加入侵分区后，重新加载已选择的入侵分区树
	function accSelectIasPartitionOnSure(iasPartitionIds) {
		// 厂商
		var manafacturer = ZKUI.Combo.get("accLinkageManufacturer").combo.getSelectedValue();
		var existManafacturer = $("#iasManufacture${uuid}").val();
		var iasPartitionExistIds = $("#iasPartitionIds${uuid}").val();
		if (existManafacturer != manafacturer) {
			iasPartitionExistIds = "";
		}
		if (iasPartitionExistIds) {
			iasPartitionIds = iasPartitionExistIds + "," + iasPartitionIds
		}
		$("#iasPartitionIds${uuid}").val(iasPartitionIds);
		$("#iasManufacture${uuid}").val(manafacturer);

		// 更新界面展示联动触发事件树
		ZKUI.Tree.get("iasPartitionTreeId").reloadTree("accLinkage.do?getIasPartitionTreeByIds&iasPartitionIds=" + iasPartitionIds);
		ZKUI.Tree.get("iasPartitionTreeId").setValue(iasPartitionIds);

		var partitionActionType = ZKUI.Combo.get("partitionActionType${uuid!}").combo.getSelectedValue();
		checkedPartitionActionType(partitionActionType);
	}

	// 检测入侵动作类型
    function checkedPartitionActionType(selectVal) {
    	// 根据厂商类型加载
    	var manufacture = $("#iasManufacture${uuid}").val();
    	// 布防,BOSCH有布防类型,RISCO无布防类型;
    	if (selectVal == "0") {
			$("#armTypeTr${uuid}").show();
			if (manufacture == "RISCO") {
				$("#armTypeTr${uuid}").hide();
				if (ZKUI.Combo.get("partitionArmType${uuid!}")) {
					ZKUI.Combo.get("partitionArmType${uuid!}").combo.setComboValue("");
				}
			} else {
				ZKUI.Combo.get("partitionArmType${uuid!}").load("accLinkage.do?getArmTypeByManufacture&manufacture=" + manufacture, true);
			}
    	} else if (selectVal == "1") {
			// 撤防
			$("#armTypeTr${uuid}").hide();
			if (ZKUI.Combo.get("partitionArmType${uuid!}")) {
				ZKUI.Combo.get("partitionArmType${uuid!}").combo.setComboValue("");
			}
    	}
    }

	if ("${systemModules}".toLowerCase().indexOf("ias") != -1) {
    	// 布防,BOSCH有布防类型,RISCO无布防类型;
    	if ("${(item.partitionActionType)}" == "1") {
			// 撤防
			$("#armTypeTr${uuid}").hide();
			if (ZKUI.Combo.get("partitionArmType${uuid!}")) {
				ZKUI.Combo.get("partitionArmType${uuid!}").combo.setComboValue("");
			}
    	}
	}
</script>
</#macro>

