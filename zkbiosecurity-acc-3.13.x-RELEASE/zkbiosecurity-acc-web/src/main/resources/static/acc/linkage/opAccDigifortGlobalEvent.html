<div style="margin-top: 10px; text-align: ${leftRTL!'left'};display: none;position: absolute;" id="noConnectTip">
    <span class="warningImage"></span>
    <span class="warningColor"><@i18n 'acc_digifort_checkConnection'/></span>
</div>
<table width="100%" height="100%" style="table-layout: fixed;">
    <tr valign="top">
        <td class="content_td">
            <div class="content_div" style="height: 50px;overflow: auto;display: none">
                <div id="digiEventTree" style="width: 100%; height: 300px;"></div>
            </div>
            <script type="text/javascript">
                $(".dhxwin_active .content_div").height($(".dhxwin_active .content_td").height());
                $(".dhxwin_active .content_div").show();
            </script>
        </td>
    </tr>
    <tr valign="middle" height="46px">
        <td >
            <!-- editAfter begin-->
            <div class="bottomDiv bottomDivR">
                <button class='button-form' onclick="onSubmit()" id="confirmButton"><@i18n 'common_edit_ok'/></button>
                <button class='button-form' onclick="DhxCommon.closeWindow()" id="closeButton"><@i18n 'common_edit_cancel'/></button>
            </div>
            <!-- editAfter end-->
        </td>
    </tr>
</table>
<script type="text/javascript">
    var url = "accLinkage.do?getDigifortGlobalEvents&linkageId=";
    var linkId = $("#accLinkId").val();
    if ($("#isGlobalLinkage").length > 0)
    {
        url = "accGlobalLinkage.do?getDigifortGlobalEvents&linkageId=";
        linkId = $("#accGlobalLinkageId").val();
    }
    $.ajax({
        type : "GET",
        async : false,
        dataType : "json",
        url : url + linkId,
        success : function(data)
        {
            if (data && data.ret == "ok")
            {
                digiEventTree = new dhtmlXTreeObject("digiEventTree", "100%", "100%", 0);
                digiEventTree.setSkin(sysCfg.dhxSkin);
                digiEventTree.setImagePath(sysCfg.treeImgPath);
                digiEventTree.enableCheckBoxes(1);
                digiEventTree.enableSmartXMLParsing(true);
                digiEventTree.setDataMode("json");
                var treeData = {
                    "id" : 0,
                    "item" : data.data
                };
                digiEventTree.loadJSONObject(treeData);
                $(":input[name='digiEventIdsChecked']").each(function(){
                    if($(this).attr("checked") == "checked")
                    {
                        digiEventTree.setCheck($(this).val(), 1);
                    }
                });
            }
            else
            {
                $("#noConnectTip").show();
            }
        }
    })


    function onSubmit()
    {
        var eventNames = digiEventTree.getAllChecked();
        $("#digiEventIds").val(eventNames);
        $("#digiEventNames").val(eventNames);
        buildPageData("digiEventIds", "digiEventNames", "digifortGlobalEventsList");
        DhxCommon.closeWindow()
    }

</script>
