<@ZKUI.SelectContent gridName="accLinkageSelectPartitionGrid" onSure="accSelectIasPartitionOnSure" copy="true" textField="name" vo="com.zkteco.zkbiosecurity.acc.vo.AccLinkageSelectIasPartitionItem" query="accLinkage.do?getSelectIasPartition&filterId=${filterId}">
    <@ZKUI.Searchbar onBeforeQuery="accLinkagePartitionOnBeforeQuery">
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.Combo id="accLinkageManufacturer" autoFirst="true" empty="false" name="manufacturer" title="base_linkage_ias_manufacture">
                        <option value="BOSCH">BOSCH</option>
                        <option value="RISCO">RISCO</option>
                        <#if "${Application['system.language']}" == "zh_CN">
                            <option value="HORN">熵基科技</option>
                        <#else>
                            <option value="HORN">ZKTECO</option>
                        </#if>
                    </@ZKUI.Combo>
                </td>
                <td valign="middle">
                    <@ZKUI.Input type="text" name="name" maxlength="30" title="base_linkage_ias_partitionName" />
                </td>
            </tr>
        </@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
</@ZKUI.SelectContent>

<script type="text/javascript">
    // 初始厂商
    var manafacturer = ZKUI.Combo.get("accLinkageManufacturer").combo.getSelectedValue();
    function accLinkagePartitionOnBeforeQuery() {
        // 厂商
        var newManafacturer = ZKUI.Combo.get("accLinkageManufacturer").combo.getSelectedValue();
        // 因只能设置相同厂商分区，查询时厂商和初始厂商不一致,移除已选项
        if (manafacturer != newManafacturer) {
            var selectedPartitionIds = ZKUI.Select.get("accLinkageSelectPartitionGrid").getSelectRows();
            if (selectedPartitionIds.length > 0) {
                $("#accLinkageSelectPartitionGridrightAddAll").click()
            }
        }
        // 赋值新厂商
        manafacturer = newManafacturer;
    }

</script>