<#assign gridName="accTransactionByDoorGrid${uuid!}">
<@ZKUI.DGrid gridName="${gridName}">
	<!--左页面(门列表页) -->
    <@ZKUI.LeftGrid title="acc_leftMenu_searchByDoor" width="56%">
	    <@ZKUI.Searchbar>
	    	<@ZKUI.SearchTop>
	    		<tr>
					<td valign="middle">
						<@ZKUI.Input name="name"  maxlength="30" title="acc_door_name" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.ComboTree isTree="true" name="deviceIds" url="accDevice.do?tree" title="common_dev_name" width="148" readonly="true"/>
					</td>
				</tr>
				
	    	</@ZKUI.SearchTop>
	    </@ZKUI.Searchbar>
	    <@ZKUI.Toolbar>
	    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="acc:transactionByDoor:refresh"></@ZKUI.ToolItem>
	    </@ZKUI.Toolbar>
	    <@ZKUI.Grid onRowSelect="accTransactionByDoorLeftGridClick" vo="com.zkteco.zkbiosecurity.acc.vo.AccTransactionDoorItem" query="accTransactionByDoor.do?list" autoFirst="true"/>
	</@ZKUI.LeftGrid>
	
	<!--右页面 (人列表页)-->
	<@ZKUI.RightGrid title="pers_person" leftField="id" rightParamName="doorId" width="44%">
	    <@ZKUI.Toolbar>
	    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="acc:transactionByDoor:refresh"></@ZKUI.ToolItem>
	    	<@ZKUI.ToolItem id="accTransactionByDoor.do?export&doorId=" action="exportDoorPerson" text="common_op_export" img="comm_export.png" permission="acc:transactionByDoor:export"></@ZKUI.ToolItem>
	    </@ZKUI.Toolbar>
	    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccTransactionDoorPersonItem" query="accTransactionByDoor.do?personList"/>
	</@ZKUI.RightGrid>
</@ZKUI.DGrid>

<script type="text/javascript">

	var doorId = "";
    var gridName = "${gridName}";
    var dbGrid = ZKUI.DGrid.get(gridName);
    var leftGrid = dbGrid.leftGrid;
    var rightGrid = dbGrid.rightGrid;

    function accTransactionByDoorLeftGridClick(rid) {
        //此方法注册的是官方事件，所以this指代的是官方grid对象，通过访问属性zkgrid获取我们自定义的grid对象
        // var leftGrid = this.zkgrid;//获取左表格
        // var dbGrid = ZKUI.DGrid.get(leftGrid.options.dGridName);//双列表对象
        // var rightGrid = dbGrid.rightGrid;//右表格对象
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        doorId = row.id;
        rightGrid.reload(function() {
            rightCallback(row.id, rightGrid, rid);//手动回调
        },{doorId:doorId});//传递id到右表格查询
    }

    //右表格回调事件
    function rightCallback(id, rightGrid, rid) {
        // var dbGrid = ZKUI.DGrid.get(rightGrid.options.dGridName);//双列表对象
        // var leftGrid = dbGrid.leftGrid;//获取左表格
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        rightGrid.setTitle("<@i18n 'common_op_browse'/>" + " " + row.name+ " " + "<@i18n 'acc_level_openingPersonnel'/>");//设置右表格标题
    }

    //导出对应门有权限的人员
	function exportDoorPerson(id, bar) {
        //如果doorId为空，则取左边列表第一条的数据
        if(doorId == "")
		{
            var rowData = leftGrid.grid.getRowDataByIndex(0)
            if(rowData != undefined)
            {
                doorId = rowData.id;
            }
            else
            {
                messageBox({messageType:"alert",text:"<@i18n 'common_prompt_noData'/>"});
                return;
            }
		}
        if (bar) {
            var gridName = bar.gridName;
            var path = "/skip.do?page=public_template_opExportRecord&gridName=" + gridName + "&actionName=" + encodeURIComponent(id + doorId);
            var opts = {
                path: path,//设置弹窗路径
                width: 550,//设置弹窗宽度
                height: 340,//设置弹窗高度
                title: "<@i18n 'common_op_export'/>",//设置弹窗标题
                gridName: gridName//设置grid
            }
            DhxCommon.createWindow(opts);
        }
    }
</script>