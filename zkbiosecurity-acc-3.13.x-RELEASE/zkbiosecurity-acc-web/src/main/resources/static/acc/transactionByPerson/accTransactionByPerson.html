<#assign gridName="accTransactionByDoorGrid${uuid!}">
<@ZKUI.DGrid gridName="${gridName}">
	<!-- 左页面(人列表页) -->
    <@ZKUI.LeftGrid title="common_leftMenu_searchByPerson" width="56%">
        <@ZKUI.Searchbar>
	        <@ZKUI.SearchTop>
	    		<tr>
					<td valign="middle">
						<@ZKUI.Input name="pin"  maxlength="30" title="pers_person_pin" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Input name="likeName"  maxlength="30" title="pers_person_wholeName" type="text"/>
					</td>
				</tr>
	    	</@ZKUI.SearchTop>
	 		<@ZKUI.SearchBelow>
	 			<tr>
					<td valign="middle">
						<@ZKUI.Input name="deptName" maxlength="30" title="pers_dept_deptName" type="text"/>
					</td>
	 			</tr>
	 		</@ZKUI.SearchBelow>
 		</@ZKUI.Searchbar>
	    <@ZKUI.Toolbar>
	    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="acc:transactionByPerson:refresh"></@ZKUI.ToolItem>
	    </@ZKUI.Toolbar>
	    <@ZKUI.Grid onRowSelect="accTransactionByPersonLeftGridClick" vo="com.zkteco.zkbiosecurity.acc.vo.AccTransactionPersonItem" query="accTransactionByPerson.do?list" autoFirst="true"/>
	</@ZKUI.LeftGrid>
	<!-- 右页面(门列表页) -->
	<@ZKUI.RightGrid title="acc_door_entity" leftField="id" rightParamName="personId" width="44%">
	    <@ZKUI.Toolbar>
	    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="acc:transactionByPerson:refresh"></@ZKUI.ToolItem>
	    	<@ZKUI.ToolItem id="accTransactionByPerson.do?export&personId=" action="exportPersonDoor" text="common_op_export" img="comm_export.png" permission="acc:transactionByPerson:export"></@ZKUI.ToolItem>
	    </@ZKUI.Toolbar>
	    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccTransactionPersonDoorItem" query="accTransactionByPerson.do?doorList"/>
	</@ZKUI.RightGrid>
	
</@ZKUI.DGrid>

<script type="text/javascript">
    var personId = "";
    var gridName = "${gridName}";
    var dbGrid = ZKUI.DGrid.get(gridName);
    var leftGrid = dbGrid.leftGrid;
    var rightGrid = dbGrid.rightGrid;

    function accTransactionByPersonLeftGridClick(rid) {
        //此方法注册的是官方事件，所以this指代的是官方grid对象，通过访问属性zkgrid获取我们自定义的grid对象
        // var leftGrid = this.zkgrid;//获取左表格
        // var dbGrid = ZKUI.DGrid.get(leftGrid.options.dGridName);//双列表对象
        // var rightGrid = dbGrid.rightGrid;//右表格对象
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
		personId = row.id;
        rightGrid.reload(function() {
            rightCallback(row.id, rightGrid, rid);//手动回调
        },{personId:row.id});//传递id到右表格查询
    }

    //右表格回调事件
    function rightCallback(id, rightGrid, rid) {
        // var dbGrid = ZKUI.DGrid.get(rightGrid.options.dGridName);//双列表对象
        // var leftGrid = dbGrid.leftGrid;//获取左表格
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        rightGrid.setTitle("<@i18n 'common_op_browse'/>" + " " + row.pin+ " " + "<@i18n 'acc_trans_hasAccLevel'/>");//设置右表格标题
    }

    //导出人能的门
	function exportPersonDoor(id, bar) {
        //如果doorId为空，则取左边列表第一条的数据
        if(personId == "")
        {
            var rowData = leftGrid.grid.getRowDataByIndex(0)
            if(rowData != undefined)
            {
                personId = rowData.id;
            }
            else
            {
                messageBox({messageType:"alert",text:"<@i18n 'common_prompt_noData'/>"});
                return;
            }
        }
        if (bar) {
            var gridName = bar.gridName;
            var path = "/skip.do?page=public_template_opExportRecord&gridName=" + gridName + "&actionName=" + encodeURIComponent(id + personId);
            var opts = {
                path: path,//设置弹窗路径
                width: 550,//设置弹窗宽度
                height: 340,//设置弹窗高度
                title: "<@i18n 'common_op_export'/>",//设置弹窗标题
                gridName: gridName//设置grid
            }
            DhxCommon.createWindow(opts);
        }
    }
</script>