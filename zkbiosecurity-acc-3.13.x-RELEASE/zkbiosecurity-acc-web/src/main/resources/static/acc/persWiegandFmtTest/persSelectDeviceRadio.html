<@ZKUI.SelectContent gridName="persSelectDeviceGrid" type="radio" onSure="wiegandFmtTestAfterSelectDevice" showColumns="id,deviceAlias,deviceSn" textField="deviceAlias" copy="true" areaId="${areaId!}" vo="com.zkteco.zkbiosecurity.acc.vo.Acc4PersDeviceSelectItem" query="/accSelectDevice.do?selectDeviceList">
    <@ZKUI.Searchbar>
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="deviceAlias"  maxlength="30" title="common_dev_name" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="deviceSn"  maxlength="30" title="common_dev_sn" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
</@ZKUI.SelectContent>
<script type="text/javascript">
    function wiegandFmtTestAfterSelectDevice(value, text, event) {
        $("#deviceName").val(text);
        $("#deviceId").val(value);
    }
</script>