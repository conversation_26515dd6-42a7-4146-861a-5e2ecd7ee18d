<#include '/public/template/editTemplate.html'>
<#macro editContent>

<table class="opTableStyle">
    <tr>
        <td width="280" valign="top">
            <div style="padding: 10px;">
                <form action="">
                    <label><@i18n 'common_leftMenu_device'/></label>
                    <span class="required">*</span>
                    <@ZKUI.Combo id="deviceName${uuid}" width="120" name="deviceName" readonly="readonly" path="/accSelectDevice.do?getDeviceList" value="" hideLabel="true" empty="true">
                    </@ZKUI.Combo>
                    <input type="hidden" id="deviceId" name="deviceId" />
                    <button id="startBtn" type="button" class="button-form" style="line-height:0;"><@i18n 'acc_wiegand_readCard'/></button>
                    <button id="clearBtn" type="button" class="button-form" style="line-height:0;"><@i18n 'acc_wiegand_clearCardInfo'/></button>
                </form>
            </div>
        </td>
    </tr>
    <tr>
        <td colspan="2" style="padding-${leftRTL!'left'}: 10px;">
            <form id="recommendFmtForm" action="/persWiegandFmt.do?recommendFmt">
                <table class="tableStyle" style="white-space: nowrap;">
                    <tr>
                    <#if Application['system.language'] != "es">
                        <td>&nbsp;</td>
                        <td><@i18n 'pers_wiegandFmt_siteCode'/></td>
                    </#if>
                    <#if Application['system.language'] == "es">
                        <td colspan="2" align="right"><@i18n 'pers_wiegandFmt_siteCode'/></td>
                            </#if>
                            <td><@i18n 'pers_card_cardNo'/><span class="required">*</span></td>
                        <td><@i18n 'acc_wiegand_originalCard'/><span id="currentBits"></span></td>
                    </tr>
                    <tr>
                        <td><label><@ZKUI.Input hideLabel="true" type="radio" name="cardRadio" value="0" checked="checked"/><@i18n 'acc_wiegand_card'/> 1</label></td>
                        <td><input id="sizeCode0" type="text" name="sizeCode0" style="width:60px;"/></td>
                        <td><input id="cardNo0" type="text" name="cardNo0"/></td>
                        <td><input type="text" name="orgCardNo" readonly="readonly" style="width:400px;"/></td>
                    </tr>
                    <tr>
                        <td><label><@ZKUI.Input hideLabel="true" type="radio" name="cardRadio" value="1"/><@i18n 'acc_wiegand_card'/> 2</label></td>
                        <td><input id="sizeCode1" type="text" name="sizeCode1" style="width:60px;"/></td>
                        <td><input id="cardNo1" type="text" name="cardNo1"/></td>
                        <td><input type="text" name="orgCardNo" readonly="readonly" style="width:400px;"/></td>
                    </tr>
                    <tr>
                        <td><label><@ZKUI.Input hideLabel="true" type="radio" name="cardRadio" value="2"/><@i18n 'acc_wiegand_card'/> 3</label></td>
                        <td><input id="sizeCode2" type="text" name="sizeCode2" style="width:60px;"/></td>
                        <td><input id="cardNo2" type="text" name="cardNo2"/></td>
                        <td><input type="text" name="orgCardNo" readonly="readonly" style="width:400px;"/></td>
                    </tr>
                    <tr>
                        <td colspan="4">
                        <div class="innerContent" style="line-height: 16px;padding-top: 16px;">
                        <label>
                        <span class="warningImage"></span>
                        <span class="warningColor"><@i18n 'acc_wiegand_warnInfo1'/></span>
                        </label>
                        </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4" style="padding-top:10px;">
                        <button id="recommendFmt" type="button" class="button-form" style="margin:0 5px 0 0;" disabled="disabled"><@i18n 'acc_wiegand_recommendFmt'/></button>
                        <label><@ZKUI.Input hideLabel="true" type="checkbox" id="withSizeCode" name="withSizeCode"/><@i18n 'acc_wiegand_withSizeCode'/></label>
                        <div id="loadingImage"></div>
                        </td>
                    </tr>
                </table>
             </form>
         </td>
    </tr>
    <tr>
        <td style="padding-${leftRTL!'left'}: 10px;">
            <form method="post" id="${formId}" action="/persWiegandFmt.do?edit" onkeydown="if(event.keyCode==13){return false;}">
                <input id="firstBitsCount" name="wiegandFmt.wiegandCount" type="hidden" />
                <input type="hidden" name="wiegandFmt.wiegandMode" value="2"/>
                <input type="hidden" id="wiegandFmtName" name="wiegandFmt.name"/>
                <input type="hidden" id="filterDevId" name="filterDevId"/>
                <table class="tableStyle" style="margin-top: 5px;">
                    <tr>
                        <td><@i18n 'pers_wgFmt_cardFormats'/></td>
                        <td><input id="cardFmt" type="text" name="wiegandFmt.cardFmt" style="width:550px;"/></td>
                    </tr>
                    <tr>
                        <td style="white-space: nowrap;"><@i18n 'acc_wiegand_parityFmt'/></td>
                        <td><input id="parityFmt" type="text" name="wiegandFmt.parityFmt" style="width:550px;"/></td>
                    </tr>
                </table>
            </form>
        </td>
    </tr>
</table>
<script type="text/javascript">
    $(function() {
        //返回拼好json格式的字符串
        function toArray(str)
        {
            var list = str.split(",");
            var myStr = "{";
            for(var i=0;i<list.length;i++)
            {
                try{
                    var keys = list[i].split("=");
                    var key = Trim(keys[0]);
                    var value= Trim(keys[1]);
                    if(i>0)
                    {
                        myStr += ",";
                    }
                    myStr += "\""+key+"\":\""+value+"\"";
                }catch(e)
                {
                    continue;
                }
            }
            myStr += "}";
            return myStr;
        }
        //替换掉字符串中头尾的空格
        function Trim(str){
            return str.replace(/(^\s*)|(\s*$)/g, "");
        }
        var beginDo = false,wgInterval,wgTimeout;
        // 当前窗口，
        var persWiegandFmtgridName = '${gridName}';
        var currentWindow = DhxCommon.getCurrentWindow();

        ZKUI.Combo.get("deviceName${uuid}").combo.attachEvent("onChange", function(){
            if(beginDo)
            {
                beginDo = false;
                $("#startBtn").removeAttr("disabled").text("<@i18n 'acc_wiegand_readCard'/>");
                window.clearInterval(wgInterval);
                window.clearTimeout(wgTimeout);
            }
            var deviceId = ZKUI.Combo.get("deviceName${uuid}").combo.getSelectedValue();
            $("#deviceId").val(deviceId);
        });
        //测试按钮
        $("#startBtn").click(function(){
            var deviceId = $("#deviceId").val();
            if(deviceId == ""){
              openMessage("warning", "<@i18n 'acc_dev_selectDev'/>");
            }else{
                if(!beginDo){
                    $.ajax({
                        type: "GET",
                        url: "/accPersWiegandFmt.do?start&deviceId="+deviceId,
                        async : false,
                        success: function(retData)
                        {
                            if(retData.data == "success")
                            {
                                 beginDo = true;
                            }
                            else
                            {
                                openMessage(msgType.error, retData.data);
                            }
                        }
                    });
                    if(!beginDo){
                        return;
                    }
                    window.clearInterval(wgInterval);
                    window.clearTimeout(wgTimeout);
                    var i = 0;
                    $("#startBtn").attr("disabled","disabled");

                    wgInterval = window.setInterval(function(){
                        $("#startBtn").text("<@i18n 'acc_wiegand_readCard'/>("+(60-i++)+")");
                        $.ajax({
                            type: "POST",
                            url: "/accPersWiegandFmt.do?readerCard",
                            data:{
                                deviceId:deviceId
                            },
                            success: function(retData)
                            {
                                 if(retData.ret == "ok"){
                                     var data1 = retData.data;
                                     var data2 = eval('(' + data1 + ')');
                                    if(data2.data)
                                    {
                                        var dataStr = data2.data.split("\t");

                                        var bitscount = parseInt(dataStr[1].split("=")[1]);
                                        var bits = dataStr[2].split("=")[1];
                                        var index = $("input[name='cardRadio']:checked").val();
                                        if($("#firstBitsCount").val() != '' && $("#firstBitsCount").val() != bitscount)
                                        {
                                              openMessage(msgType.warning,"<@i18n 'acc_wiegand_beforeCard'/>".format(bitscount));
                                        }
                                        else
                                        {
                                            $("#recommendFmt").removeAttr("disabled");
                                            $("input[name='orgCardNo']:eq("+index+")").val(bits);
                                            $("#firstBitsCount").val(bitscount);
                                            $("#currentBits").text("(<@i18n 'acc_wiegand_curentCount'/>)".format(bitscount));
                                        }
                                    }
                                }
                            }
                         });
                    },1000);

                    wgTimeout=window.setTimeout(function(){
                        if(wgInterval){
                            beginDo = false;
                            $("#startBtn").removeAttr("disabled").text("<@i18n 'acc_wiegand_readCard'/>")
                            window.clearInterval(wgInterval);
                        }
                    },60000);
                }
            }
        });

        $("#recommendFmtForm").validate( {
            debug : true,
            submitHandler : function()
            {
                return false;
            }
        });
        //推荐格式
        $("#recommendFmt").click(function(){
            var sizeCode = "",cardNo = "",orgCardNo="",flag = true,withSizeCode = $("#withSizeCode").is(":checked");
            $("#recommendFmtForm input[name='orgCardNo']").each(function(i){
                orgCardNo += $(this).val()+",";
                if($(this).val() != ""){
                    $("#sizeCode"+i).rules("remove");
                    $("#sizeCode"+i).rules("add",{validInputStr:true,digits:true});
                    $("#sizeCode"+i).valid();
                    $("#cardNo"+i).rules("remove");
                    $("#cardNo"+i).rules("add",{required:true,digits:true,validInputStr:true});
                    $("#cardNo"+i).valid();
                }else{
                    $("#sizeCode"+i).rules("remove");
                    //$("#sizeCode"+i).removeData('previousValue');
                    //$("#sizeCode"+i).valid();
                    $("#cardNo"+i).rules("remove");
                    //$("#cardNo"+i).removeData('previousValue');
                    //$("#cardNo"+i).valid();
                }
            });
            if(!$("#recommendFmtForm").valid()){
                 return;
            }

            $("#recommendFmtForm input[name^='sizeCode']").each(function(){
                sizeCode += $(this).val()+",";
                if($(this).val() != "")
                {
                    flag = false;
                }
            });
            if(withSizeCode && !flag){
                openMessage(msgType.warning, "<@i18n 'acc_wiegand_tip5'/>");
                return;
            }
            $("#recommendFmtForm input[name^='cardNo']").each(function(){
                 cardNo += $(this).val()+",";
            });

            sizeCode = sizeCode.replace(/,$/,"");
            cardNo = cardNo.replace(/,$/,"");
            orgCardNo = orgCardNo.replace(/,$/,"");

            $("#loadingImage").html("<img src='/public/images/loading.gif'/>");
            setTimeout(function(){
              $("#loadingImage").html("");
            },500);

            $.ajax({
                type: "POST",
                url: "/accPersWiegandFmt.do?recommendFmt",
                data:{
                    deviceId:$("#deviceId").val(),
                    sizeCode:sizeCode,
                    cardNo:cardNo,
                    orgCardNo:orgCardNo,
                    bitscount:$("#firstBitsCount").val(),
                    withSizeCode:withSizeCode
                },
                success: function(retData) {
                    //$("#loadingImage").html("");
                    if(retData.ret == "ok" && retData.data != null)
                    {
                       // var data = retData[sysCfg.data];
                        var result = retData.data;

                        //替换{、}为空字符串
                        var noleft = result.replace("{","");
                        var result1 = noleft.replace("}","")
                        //转成Json对象
                        var data=eval("("+toArray(result1)+")");
                        $("#cardFmt").val(data.cardFmt);
                        $("#parityFmt").val(data.parityFmt);

                        if(withSizeCode)
                        {
                            $("#recommendFmtForm input[name^='orgCardNo']").each(function(i){
                                if($(this).val() != "")
                                {
                                    var data = retData[sysCfg.data];
                                    if(data.sizeCode != 0)
                                    {
                                        $("#sizeCode"+i).val(data.sizeCode);
                                    }
                                }
                            });
                        }
                        $('#${formId}'+"OK").removeAttr("disabled");
                    }else{
                        openMessage(msgType.error, retData[sysCfg.msg]);
                    }
                }
            });
        });

        //保存
        $('#${formId}').validate({
            debug : true,
            rules :{
                 "wiegandFmt.cardFmt":{
                    required : true,
                    validInputStr: true
                 },
                 "wiegandFmt.parityFmt":{
                    required : true,
                    validInputStr: true
                 }
            },
            submitHandler : function(){
            var cardFmt = $("#cardFmt").val();
            var parityFmt = $("#parityFmt").val();
            var wiegandCount = $("#firstBitsCount").val();
            var siteCode=0,sc="";
            $("#recommendFmtForm input[name^='sizeCode']").each(function(){
                if($(this).val() != ""){
                    if(sc=="" || $(this).val() == sc){
                        sc = $(this).val();
                        siteCode = sc;
                    }else {
                        siteCode=0;
                    }
                 }
            });

            currentWindow.close();
            var opts = {//弹窗配置对象
                path: '/persWiegandFmt.do?edit&id='+''+'&cardFmt='+cardFmt+'&parityFmt='+parityFmt+'&wiegandCount='+wiegandCount+'&siteCode='+siteCode,//设置弹窗路径
                width: 950,//设置弹窗宽度
                height: 650,//设置弹窗高度
                title: "<@i18n ''/>",//设置弹窗标题
                gridName: persWiegandFmtgridName
            };
           // DhxCommon.createWindow('/persWiegandFmt.do?edit&cardFmt='+cardFmt+'&parityFmt='
          //  +parityFmt+'&wiegandCount='+wiegandCount+'&siteCode='+siteCode);
                DhxCommon.createWindow(opts);
            }
        });

        $("#clearBtn").click(function(){
            $("#recommendFmtForm input[type='text'],#cardFmt,#parityFmt,#firstBitsCount").val("");
            $("#currentBits").text("");
            $("#recommendFmt").attr("disabled","disabled");
            $("#${formId}"+"OK").attr("disabled","disabled");
            $("#recommendFmtForm input[name^='cardNo']").each(function(){
                $(this).rules("remove");
                $(this).valid();
            });
            $(".ts_box").remove();
        });

        //窗口关闭事件处理
        currentWindow.attachEvent("onClose", function(win){
            if(beginDo) {
                window.clearInterval(wgInterval);
                window.clearTimeout(wgTimeout);
                $.ajax({
                    type: "GET",
                    url: "/accPersWiegandFmt.do?stop&deviceId="+$("#deviceId").val(),
                    success: function(retData){
                    }
                });
            }
            return true;
        });

        //得到要过滤的设备的Ids
        $.ajax({
            type: "GET",
            url:"/accPersWiegandFmt.do?getAllFilterId",
            dataType: "text",
            async: true,
            success: function(data)
            {
                 $("#filterDevId").val(data);//待过滤设备的id
            }
        });
        $("#${formId}"+"SaveContinue").remove();
        window.setTimeout(function(){
            $("#${formId}"+"OK").attr("disabled","disabled");
        },10);
    });

</script>
</#macro>