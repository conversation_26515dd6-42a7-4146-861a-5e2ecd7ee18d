<#include '/public/template/editTemplate.html'>
<#macro editContent>

<form action='accAuxIn.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
	<input type='hidden' name='id' value='${(item.id)!}'/>
	<table class='tableStyle'>
		<tr>
			<th><label><@i18n 'common_dev_name'/></label><span class='required'>*</span></th>
			<td><input name='devAlias' type='text' value='${(item.devAlias)!}' disabled="disabled"/></td>
		</tr>
		<tr>
			<th><label><@i18n 'common_number'/></label><span class='required'>*</span></th>
			<td><input name='auxNo' type='text' value='${(item.auxNo)!}' disabled="disabled"/></td>
		</tr>
		<tr>
			<th><label><@i18n 'common_name'/></label><span class='required'>*</span></th>
			<td><input name='name' type='text' value='${(item.name)!}'/></td>
		</tr>
		<!--<tr>
			<th><label><@i18n 'common_dev_printingName'/></label><span class='required'>*</span></th>
			<td><input name='printerNumber' type='text' value='${(item.printerNumber)!}' disabled="disabled"/></td>
		</tr>-->
		<tr>
			<th><label><@i18n 'acc_auxIn_timeZone'/></label></th>
			<td>
			    <@ZKUI.Combo id="auxInTimeSegId" value="${(item.accTimeSegId)!}" name="accTimeSegId" hideLabel="true" empty="false" autoFirst="true" readonly="true" width="148" path="accTimeSeg.do?getTimeSegList">
			    </@ZKUI.Combo>
			</td>
		</tr>
		<tr>
			<th><label><@i18n 'common_remark'/></label></th>
			<td>
				<textarea name='remark' maxlength="50" type='text' rows="4" style="width: 148px;">${(item.remark)!}</textarea>
			</td>
		</tr>
	</table>
</form>

<script type='text/javascript'>

	$(function() {
		$('#${formId}').validate( {
			debug : true,
			rules :
			{
				'name' :
				{
					required : true,
                    maxlength : 30,
					unInputChar:true,
					overRemote : ["accAuxIn.do?isExist&devId=${(item.devId)!}", "${(item.name)!}"]
				},
				'remark' :
				{
                    maxlength : 50
				}
			},
            messages: {
                'name' : {
					remote: "<@i18n 'common_dev_nameVerifiyAuIn'/>"
				}
            },
			submitHandler : function()
			{
				<@submitHandler/>
			}
		});
	});

    <#if !(supportAuxInTimezone)?exists>
        ZKUI.Combo.get("auxInTimeSegId").combo.disable(true);
	</#if>

</script>
</#macro>