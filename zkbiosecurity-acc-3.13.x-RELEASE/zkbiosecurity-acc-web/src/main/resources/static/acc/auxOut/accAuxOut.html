<#assign gridName="accAuxOutGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
				<td valign="middle">
					<@ZKUI.Input name="name"  maxlength="30" title="common_name" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="devAlias"  maxlength="30" title="common_dev_name" type="text"/>
				</td>
				<!--<td valign="middle">
					<@ZKUI.Input name="printerNumber"  maxlength="30" title="common_dev_printingName" type="text"/>
				</td>-->
			</tr>
    	</@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="acc:auxOut:refresh"/>
    	<@ZKUI.ToolItem id="accAuxOut.do?getAuxOutIds&type=openAuxOut&ids=" action="accAuxOutOperateAuxOut" text="acc_rtMonitor_remoteOpen" width="500" img="comm_remoteNormalOpenAuxout.png" permission="acc:auxOut:openAuxOut"/>
    	<@ZKUI.ToolItem id="accAuxOut.do?getAuxOutIds&type=closeAuxOut&ids=" action="accAuxOutOperateAuxOut" text="acc_rtMonitor_remoteClose" img="comm_remotecloseAuxout.png" permission="acc:auxOut:closeAuxOut"/>
    	<@ZKUI.ToolItem id="accAuxOut.do?getAuxOutIds&type=auxOutNormalOpen&ids=" action="accAuxOutOperateAuxOut" text="acc_rtMonitor_remoteNormalOpen" img="comm_remoteNormalOpenAuxout.png" permission="acc:auxOut:normalOpen"/>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccAuxOutItem" query="accAuxOut.do?list"/>
</@ZKUI.GridBox>

<script type="text/javascript">
	function accAuxOutOperateAuxOut(id, bar) {
	    var path = id;
        var gridName = bar.gridName || "gridbox";
        var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
        if(ids == "") {
            messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});
        }
        else if(ids.split(",").length > 10)
		{
            messageBox({messageType:"alert",text:"<@i18n 'common_prompt_onlySelectTenObject'/>"});
		}
        else
        {
            path += ids;
            var height;
            var title;
            if(id.indexOf("openAuxOut") > 0)
			{
			    height = 440;
			    title = "<@i18n 'acc_rtMonitor_remoteOpen'/>";
			}
			else if(id.indexOf("closeAuxOut") > 0)
			{
                height = 410;
                title = "<@i18n 'acc_rtMonitor_remoteClose'/>";
			}
			else
			{
                height = 410;
                title = "<@i18n 'acc_rtMonitor_remoteNormalOpen'/>";
			}

            var opts = {
                path: path,
                width: 600,
                height: height,
                title: title,
                gridName: "gridbox"

            };
            DhxCommon.createWindow(opts);
        }
    }
</script>