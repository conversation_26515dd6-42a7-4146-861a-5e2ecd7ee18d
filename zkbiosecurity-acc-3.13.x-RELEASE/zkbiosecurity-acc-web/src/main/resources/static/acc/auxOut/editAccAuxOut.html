<#include '/public/template/editTemplate.html'>
<#macro editContent>

<form action='accAuxOut.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
	<input type='hidden' name='id' value='${(item.id)!}'/>
	<input type='hidden' name='devId' value='${(item.devId)!}'/>
	<table class='tableStyle'>
		<tr>
			<th><label><@i18n 'common_dev_name'/></label><span class='required'>*</span></th>
			<td><input name='devAlias' type='text' value='${(item.devAlias)!}' disabled="disabled"/></td>
		</tr>
		<tr>
			<th><label><@i18n 'common_number'/></label><span class='required'>*</span></th>
			<td><input name='auxNo' type='text' value='${(item.auxNo)!}' disabled="disabled"/></td>
		</tr>
		<tr>
			<th><label><@i18n 'common_name'/></label><span class='required'>*</span></th>
			<td><input name='name' type='text' value='${(item.name)!}'/></td>
		</tr>
		<!--<tr>
			<th><label><@i18n 'common_dev_printingName'/></label><span class='required'>*</span></th>
			<td><input name='printerNumber' type='text' value='${(item.printerNumber)!}' disabled="disabled"/></td>
		</tr>-->
		<#if outRelaySetFunOn?string == "true">
		<tr>
			<th><label><@i18n 'acc_auxOut_passageModeTimeZone'/></label></th>
			<td>
			    <@ZKUI.Combo value="${(item.accTimeSegId)!}" name="accTimeSegId" hideLabel="true" readonly="true" width="148" path="accTimeSeg.do?getTimeSegList">
			    </@ZKUI.Combo>
			</td>
		</tr>
		</#if>
		<tr>
			<th><label><@i18n 'common_remark'/></label></th>
			<td><textarea name='remark' type='text' maxlength="50" rows="4" style="width: 148px;">${(item.remark)!}</textarea></td>
		</tr>
	</table>
</form>

<script type='text/javascript'>
	$(function() {
		$('#${formId}').validate( {
			debug : true,
			rules :
			{
				'name' :
				{
					required : true,
                    maxlength : 30,
					unInputChar:true,
					overRemote : ["accAuxOut.do?isExist&devId=${(item.devId)!}", "${(item.name)!}"]
				},
			},
            messages: {
                'name' :
                    {
                        remote: "<@i18n 'common_dev_nameVerifiyAuOut'/>"
                    }
            },
			submitHandler : function()
			{
				<@submitHandler/>
			}
		});
	});
</script>
</#macro>