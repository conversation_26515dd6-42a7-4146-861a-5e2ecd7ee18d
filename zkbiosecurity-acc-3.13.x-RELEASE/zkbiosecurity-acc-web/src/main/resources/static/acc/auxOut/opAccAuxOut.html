<#include '/public/template/editTemplate.html'>
<#macro editMain></#macro>
<@ZKUI.Process id="operateAuxOut${uuid}" dealPath="" onFinish="onFinishHandler" confirmText="acc_dev_start" onSure="cusConfirm">
<!-- content内容标签必须指定name属性，用来指定嵌套位置 -->
	<@ZKUI.Content name="op">
		<form action="accAuxOut.do?${type}" method="post" id="${formId}" onkeydown="if(event.keyCode==13){return false;}">
			<input type="hidden" name="type" id="type" value="${(type)!}"/>
			<input type="hidden"  name="ids" id="auxOutIds" value="${(retIds)!}"/>
			<input type="hidden"  name="name" id="auxOutName" value="${(name)!}"/>
			<@i18n 'common_dev_selectedAuxOut'/>:
			<div name="selectDoor" id="selectDoor" class="zk-border-color" style="border: solid 1px green;height: 110px; overflow:auto">
			</div>
			<div class="zk-border-color" style="margin-top: 10px; padding-bottom: 10px; border: solid 1px green;">
				<table style="padding-top: 8px;" class="tableStyle" >
					<tr id="loginPwdRow" hidden="hidden">
						<td><@i18n 'auth_user_userPwd'/><span class="required">*</span></td>
						<td>
							<input type="password" maxlength="18" id="loginPwd${uuid!}" name="auxOutLoginPwd" autofocus="true"/>
							<input type="hidden"  name="loginPwd" id="password_hidden"/>
						</td>
					</tr>
					<tr id="openAuxOut">
						<td><label><@i18n 'common_dev_openTimes'/><span class="required">*</span></label></td>
						<td>
							<input class="valid" type="text" value="5" size="8" maxlength="3" name="openInterval" id="openInterval${uuid}">
							<@i18n 'common_second'/>
							<span class="form_note">(1-254)</span>
						</td>
					</tr>
				</table>
			</div>
		</form>
	</@ZKUI.Content>
</@ZKUI.Process>

<script type="text/javascript">
    var processWindowId = "operateAuxOut${uuid}";
    var processIds = ZKUI.Process.get(processWindowId).ids;
	$(function(){
		$("#"+processIds.totalId).hide();
		$("#"+processIds.currentId).hide();
		if($("#loginPwd${uuid!}")) {
			$("#loginPwd${uuid!}").focus();
		}
		var auxOutsNameStr = "${(auxOutsName)!}";
		var disabledAuxOutsName = "${(disabledAuxOutsName)!}";
		var offlineAuxOutsName = "${(offlineAuxOutsName)!}";
		if(auxOutsNameStr == "")
		{
			$("#"+processIds.confirmButtonId).attr("disabled", true);
		}
		else
		{
			var tempStr = auxOutsNameStr.split("#");
			auxOutsNameStr = "";
			for (i in tempStr) {
				auxOutsNameStr += "<b>" + tempStr[i].split("=")[0] + ":&nbsp&nbsp</b>" + tempStr[i].split("=")[1] + "</br>";
			}
		}
		if(!($("#type").val() == "openAuxOut"))
		{
			$("#openAuxOut").hide();
		}
		$("<div style='padding-left: 10px; margin-bottom: 4px;'>" + auxOutsNameStr  + "</div>").appendTo("#selectDoor");

		if(disabledAuxOutsName != "" && disabledAuxOutsName.length > 0)
		{
			var tempStr = disabledAuxOutsName.split("#");
			disabledAuxOutsName = "";
			for (i in tempStr) {
				disabledAuxOutsName += "<b>" + tempStr[i].split("=")[0] + ":&nbsp&nbsp</b>" + tempStr[i].split("=")[1] + "</br>";
			}
			$("<div class='zk-msg-error' style='padding-left: 10px; margin-top: 10px;'><b><@i18n 'acc_auxOut_disabled'/></b><br/>" + disabledAuxOutsName + "</div>").appendTo("#selectDoor");
		}

		if(offlineAuxOutsName != "" && offlineAuxOutsName.length > 0)
		{
			var tempStr = offlineAuxOutsName.split("#");
			offlineAuxOutsName = "";
			for (i in tempStr) {
				offlineAuxOutsName += "<b>" + tempStr[i].split("=")[0] + ":&nbsp&nbsp</b>" + tempStr[i].split("=")[1] + "</br>";
			}
			$("<div class='zk-msg-error' style='padding-left: 10px; margin-top: 10px;'><b><@i18n 'acc_auxOut_offline'/></b><br/>" + offlineAuxOutsName + "</div>").appendTo("#selectDoor");
		}

		$("#${formId}").validate({
			debug : true,
			rules :
			{
				"openInterval" :
				{
					required: true,
					range : [1, 254],
					digits: true
				}
			},
			submitHandler: function() {
				formSubmit();
			}

		});
	});
	function formSubmit()
	{
		var time = $("#openInterval${uuid}").val();
		$("#"+processIds.confirmButtonId).attr("disabled", true);
		$('#${formId}').ajaxSubmit({
			success: function(data)
			{
				if (!data)
				{
					openMessage(msgType.error);
				}
				else if (data.ret == "500") {
					$("#"+processIds.infoDivId).empty();
					$("#"+processIds.infoDivId).append("<font color=\"red\"><@i18n 'common_dev_opFaileAndReason'/><@i18n 'auth_user_pwdIncorrect'/>!</font>");
					$("#"+processIds.confirmButtonId).attr("disabled", false);
				}
				else {
					$("#"+processIds.infoDivId).empty();
					var msgStr = data["msg"];
					var dataObj = data["data"];
					$("#"+processIds.infoDivId).append(I18n.getValue(msgStr) +"</br><font color=\"red\">"+ dataObj+"</font>");
				}
				$("#"+processIds.confirmButtonId).attr("disabled", false);
			},
			error: function(XMLHttpRequest, textStatus, errorThrown)
			{
				messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
			}
		});
	}
	function cusConfirm()
	{
		$("#${formId}").submit();
	}
	$("#loginPwd${uuid!}").on("change", function() {
		if($("#loginPwd${uuid!}").val()) {
			$("#password_hidden").val(hex_md5($("#loginPwd${uuid!}").val()));
		}
		else {
			$("#password_hidden").val("");
		}
	})
	isNeedValidUserForExport(function(ret) {
		if(ret && ret.ret === sysCfg.success) {
			$("#loginPwdRow").hide();
			$("input[name='auxOutLoginPwd']").rules("remove");
		} else {
			$("#loginPwdRow").show();
			$("input[name='auxOutLoginPwd']").rules("add", {required : true});
		}
	})
	</script>
	
