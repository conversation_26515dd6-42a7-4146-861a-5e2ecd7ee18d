<#assign editPage = "true">
<#if Application['system.maxExportCount']??>
<#assign maxExportCount = "${Application['system.maxExportCount']!'30000'}">
</#if>
<#if !maxExportCount??>
<#assign maxExportCount = "30000">
</#if>
<#include "/public/template/editTemplate.html">
<#macro editContent>
<script type="text/javascript">
$(function(){
	var gridName = "${gridName}";
	var grid = ZKUI.Grid.get(gridName);
	var jsonColumn = {};
	jsonColumn['levelName'] = "<@i18n 'common_level_name'/>";
	jsonColumn['persPin'] = "<@i18n 'pers_person_pin'/>";
	if ("${attr('system.levelTime.support','false')}" === "true") {
		jsonColumn['startTime'] = "<@i18n 'common_level_startTime'/>";
        jsonColumn['endTime'] = "<@i18n 'common_level_endTime'/>";
    }
	var fieldData = grid.getDisplayField();
	document.getElementById("jsonColumn${uuid}").value = JSON.stringify(jsonColumn);
	document.getElementById("queryConditions${uuid}").value = fieldData.qc;
	document.getElementById("records${uuid}").value = fieldData.records;
	document.getElementById("treeId${uuid}").value = grid.options.query;
	document.getElementById("pageXmlPath${uuid}").value = grid.options.vo;
	document.getElementById("sortName${uuid}").value = grid.grid.col_name||"";
	document.getElementById("sortOrder${uuid}").value = grid.grid.sort_order||"";
	$("#${formId}").prop("action", "${actionName}");
});
</script>
<form method="post" action="" id="${formId}" enctype="multipart/form-data">
    <input type="hidden" name="jsonColumn" id="jsonColumn${uuid}"/>
    <input type="hidden" name="pageXmlPath" id="pageXmlPath${uuid}"/>
    <input type="hidden" name="queryConditions" id="queryConditions${uuid}"/>
    <input type="hidden" name="tableNameParam" value="<@i18n 'acc_level_exportPersonFileName'/>"/>
    <input type="hidden" name="searchObjectId" id="searchObjectId${uuid}"/>
    <input type="hidden" name="tableNameSearch" id="tableNameSearch${uuid}"/>
    <input type="hidden" name="custom" id="custom${uuid}"/>
    <input type="hidden" name="treeId" id="treeId${uuid}"/>
    <input type="hidden" name="exportData" id="exportData${uuid}"/>
    <input type="hidden" name="logMethod" value="<@i18n 'acc_level_exportLevelPerson'/>">
    <input type="hidden" name="records" id="records${uuid}"/>
    <input type="hidden" name="sortName" id="sortName${uuid}"/>
    <input type="hidden" name="sortOrder" id="sortOrder${uuid}"/>
    <input type="hidden" name="levelIds" id="levelId${uuid}" value="${(levelIds)!}"/>
    <input type="hidden" name="tableNameParam" id="tableNameParam${uuid}" value="<@i18n 'acc_level_exportPersonFileName'/>"/>
    <table class="tableStyle">
        <tbody>
        <tr id="loginPwdRow" hidden="hidden">
            <td><@i18n 'auth_user_userPwd'/><span class="required">*</span></td>
            <td>
                <input type="password" maxlength="18" id="loginPwd"/>
                <input type="hidden"  name="loginPwd" id="password_hidden"/>
            </td>
        </tr>
        <tr>
            <td width="120px"><@i18n 'common_file_encrypt' /></td>
            <td>
                <label><@ZKUI.Input hideLabel="true" name="isEncrypt" id="yes" type="radio" onchange="inputPassword()" value="1" checked="checked"/>
                    <span><@i18n 'common_yes' /></span></label>
                <label><@ZKUI.Input hideLabel="true" name="isEncrypt" id="no" type="radio" onchange="inputPassword()" value="0"/>
                    <span><@i18n 'common_no' /></span></label>
            </td>
        </tr>
        <tr class="passwordCheck">
            <td><@i18n 'common_file_encrypt_pwd'/><span class="required">*</span></td>
            <td>
                <input type="password" maxlength="18" id="encryptPassword" name="encryptPassword"/>
                <span class="export-input-eye"></span>
            </td>
        </tr>
        <tr>
            <td><@i18n 'common_report_fileType'/></td>
            <td>
                <@ZKUI.Combo hideLabel="true" width="148" empty="false" class="exportselect" name="reportType" id="reportType${uuid}">
                <option value="XLS"><@i18n 'common_report_excel'/></option>
            </@ZKUI.Combo>
            </td>
        </tr>
        <tr class="exportData">
            <td><@i18n 'common_report_exportType'/></td>
            <td>
                <div>
                    <@ZKUI.Input hideLabel="true" type="radio" checked="checked" value="1" name="exportType"/>
                    <@ZKUI.Format text="common_report_allRecordAtMostx">
                    <@ZKUI.Param>${maxExportCount!'30000'}</@ZKUI.Param>
                    </@ZKUI.Format>
                </div>
            </td>
        </tr>
        </tbody>
    </table>
</form>
</#macro>
<script type="text/javascript">
/**
 * 下载文件
 * @param url
 * @param params 参数
 * @param fileName 文件名
 * @param finishFun 请求完成回调
 * @param processFun 请求进度回调
 */
function downloadForExport(url, params, fileName, finishFun, processFun) {
	var formData = new FormData();
	if(params) {
		for (var key in params) {
			formData.append(key, params[key]);
		}
	}
	if($.browser && $.browser.token) {
		formData.append("browserToken", $.browser.token);
		if(localStorage.getItem("passToken")) {
			formData.append("passToken", localStorage.getItem("passToken"));
        }
	}
	var xhr = new XMLHttpRequest();
	xhr.open('POST', url, true);    // 也可用POST方式
	xhr.responseType = "blob";
	xhr.onload = function () {
		if (this.status === 200) {
			var content = this.response;
			var headerName = xhr.getResponseHeader("Content-disposition");
			if(headerName == null || headerName == '' || headerName == undefined) {
				var reader = new FileReader();
				reader.readAsText(content, 'utf-8');
				reader.onload = function (e) {
					if(reader.result && reader.result.indexOf("{")==0) {
						openMessage(msgType.error,JSON.parse(reader.result).msg);
					} else {
						openMessage(msgType.error,"<@i18n 'common_report_exportFaild'/>");
					}
				}
				return;
			}
			headerName = decodeURIComponent(headerName);
			var fn = headerName.substring(headerName.indexOf("_") + 1, headerName.length - (headerName.charAt(headerName.length-1) === "\"" ? 1 : 0));
			fn = fileName + "_" + fn;
			if (navigator.msSaveBlob == null) {
				var aTag = document.createElement('a');
				var blob = new Blob([content]);
				aTag.download = fn;
				aTag.href = URL.createObjectURL(blob);
				$("body").append(aTag);    // 修复firefox中无法触发click
				aTag.click();
				URL.revokeObjectURL(blob);
				$(aTag).remove();
			} else {
				navigator.msSaveBlob(content, fn);
			}
		}
	};

	if(typeof finishFun == "function") {
		xhr.onloadend = finishFun;
	}
	if(typeof processFun == "function") {
		xhr.onprogress = processFun;
	}
	xhr.send(formData);
}
$().ready(function(){
	function getExportResult() {
		$.ajax({
			url:"skip.do?getExportResult",
			success: function(res) {
				if(res[sysCfg.data]=="end") {
					closeMessage();
				} else {
					setTimeout(getExportResult, 3000);
				}
			}
		});
	}

	function getTableName() {
		var t = $("#tableNameSearch${uuid}").val();
		if(!t) {
			t = $("#tableNameParam${uuid}").val();
		}
		return t;
	}
	$("#${formId}").validate({
		debug : true,
		rules: {
		},
		messages:{
			"encryptPassword" : {
				noSpace:"<@i18n 'auth_user_noSpaceMsg'/>"
			}
		},
		submitHandler: function(form){
			openMessage(msgType.loading);
			document.getElementById("queryConditions${uuid}").disabled=true;
			var params = parseQuery(decodeURIComponent($("#${formId}").serialize()));
			document.getElementById("queryConditions${uuid}").disabled=false;
			params["queryConditions"] = document.getElementById("queryConditions${uuid}").value;
			downloadForExport("${actionName!}", params, getTableName(), function() {
				if(this.response.type.indexOf("text")!=-1) {
					return;
				}
				setTimeout(function() {
					DhxCommon.closeWindow();
				}, 3000);
			});
			setTimeout(function() {
				getExportResult();
				$("iframe[id^=jqFormIO]").remove();
			}, 2000);
		}
	});
	function getExportResult()
	{
	    $.ajax({
			url:"skip.do?getExportResult",
			success: function(res) {
				if(res[sysCfg.data]=="end") {
					closeMessage();
					DhxCommon.closeWindow();
				} else {
					setTimeout(getExportResult, 3000);
				}
			}
		});

	}
});
function inputPassword() {
	var passwordEncrypt = $("input[name='isEncrypt']:checked").val();
	if(passwordEncrypt === "1") {
		$("input[name='encryptPassword']").rules("add", {required : true});
        $("input[name='encryptPassword']").rules("add", {rangelength : [ 4, 18 ]});
		$(".passwordCheck").show();
	} else {
		$("input[name='encryptPassword']").rules("remove");
		$(".passwordCheck").hide();
	}
};
inputPassword();
$(".export-input-eye").on("mousedown", function(e) {
	$(this).addClass("export-input-eye-open");
	$("#encryptPassword").attr("type", "text");
})

$(".export-input-eye").on("mouseup", function(e) {
	$(this).removeClass("export-input-eye-open");
	$("#encryptPassword").attr("type", "password");
})
$("#loginPwd").on("change", function() {
    if($("#loginPwd").val()) {
        $("#password_hidden").val(hex_md5($("#loginPwd").val()));
    }
    else {
        $("#password_hidden").val("");
    }
})

isNeedValidUserForExport(function(ret) {
	if(ret && ret.ret === sysCfg.success) {
		$("#loginPwdRow").hide();
		$("input[name='loginPwd']").rules("remove");
	} else {
		$("#loginPwdRow").show();
		$("input[name='loginPwd']").rules("add", {required : true});
	}
})
</script>
