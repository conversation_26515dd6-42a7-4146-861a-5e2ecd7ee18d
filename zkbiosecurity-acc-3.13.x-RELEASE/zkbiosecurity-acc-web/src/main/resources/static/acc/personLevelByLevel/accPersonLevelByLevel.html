<#assign gridName="accPersonLevelByLevelGrid${uuid!}">
<@ZKUI.DGrid gridName="${gridName}">
	<@ZKUI.LeftGrid title="acc_leftMenu_level">
		<@ZKUI.Searchbar>
			<@ZKUI.SearchTop>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="name" maxlength="30" title="common_level_name" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Input name="timeSegName" maxlength="30" title="acc_timeSeg_entity" type="text"/>
					</td>
				</tr>
			</@ZKUI.SearchTop>
		</@ZKUI.Searchbar>
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem type="refresh" permission="acc:personLevelByLevel:refresh"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="accPersonLevelByLevel.do?exportLevelPerson" text="common_op_export" title="common_op_export" action="exportLevelPerson" type="export" permission="acc:personLevelByLevel:export"/>
			<@ZKUI.ToolItem id="accPersonLevelByLevel.do?importLevelPerson" text="common_op_import" title="common_op_import" showImportProcess="true" onFinish="reloadAccPersonLevelByLevel" type="import" permission="acc:personLevelByLevel:import"/>
		</@ZKUI.Toolbar>
		<!-- 配置左表格选中事件 -->
		<@ZKUI.Grid onRowSelect="accPersonLevelByLevelLeftGridClick" vo="com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelByLevelItem" query="/accPersonLevelByLevel.do?list" autoFirst="true"/>
	</@ZKUI.LeftGrid>
	<@ZKUI.RightGrid callback="accPersonLevelByLevelRightCallback" leftFieldName="levelId">
		<@ZKUI.Searchbar>
			<@ZKUI.SearchTop>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="personPin" maxlength="30" title="pers_person_pin" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Input name="likeName" maxlength="30" title="pers_person_wholeName" type="text"/>
					</td>
				</tr>
			</@ZKUI.SearchTop>
			<@ZKUI.SearchBelow>
				<tr>
					<!--<td valign="middle">
						<@ZKUI.Input name="cardNo"  maxlength="30" title="pers_card_cardNo" type="text"/>
					</td>-->
					<td valign="middle">
						<@ZKUI.Input name="deptName"  maxlength="30" title="pers_dept_entity" type="text"/>
					</td>
				</tr>
			</@ZKUI.SearchBelow>
		</@ZKUI.Searchbar>
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem type="refresh" permission="acc:personLevelByLevel:refresh"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="accPersonLevelByLevel.do?delPerson&levelId=(@id:gs)&levelName=(@name:gs)&personIds=(id)&personPins=(personPin)" accModuleType="person" action="accRightGridCommonDel" text="pers_common_delPerson" img="comm_del.png" permission="acc:personLevelByLevel:delPerson"></@ZKUI.ToolItem>
		</@ZKUI.Toolbar>
		<@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccPersonForLevelItem" query="accPersonLevelByLevel.do?getLevelPerson"/>
	</@ZKUI.RightGrid>
</@ZKUI.DGrid>
<script type="text/javascript">

    function accPersonLevelByLevelLeftGridClick(rid){
        var leftGrid = this.zkgrid;//获取左表格
        var dbGrid = ZKUI.DGrid.get(leftGrid.options.dGridName);//双列表对象
        var rightGrid = dbGrid.rightGrid;//右表格对象
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        rightGrid.reload(function() {
            accPersonLevelByLevelRightCallback(row.id, rightGrid, rid);//手动回调
        }, {levelId : rid});
    }

    //右表格回调事件
    function accPersonLevelByLevelRightCallback(id, rightGrid, rid) {
        var dbGrid = ZKUI.DGrid.get(rightGrid.options.dGridName);//双列表对象
        var leftGrid = dbGrid.leftGrid;//获取左表格
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        rightGrid.setTitle(I18n.getValue("pers_common_browsePerson") + " " + row.name + "（" + row.authAreaName + "）" + I18n.getValue("common_level_personLevel"));//设置右表格标题
    }
	function accLevelSelectPerson(gridName, domObj, rid) {
		var path = "skip.do?page=acc_personLevelByLevel_accLevelSelectPersonContent&levelId=" + rid;
		var width = 1100;
		if ("${attr('system.levelTime.support','false')}" === "true") {
			width = 1500;
		}
		var opts = {
			path: path,
			width: width,
			height: 450,
			title: "<@i18n 'pers_common_addPerson'/>",
			gridName: "gridBox"
		};
		DhxCommon.createWindow(opts);
	}

    function afterAddPersonLevelPerson(value, text, event) {
        var levelId = this.options.levelId;
        var levelName = this.options.linkName;
        var personDeptTree="accPersonDeptTree"+this.options.uuid;
        var deptIds = ZKUI.ComboTree.get(personDeptTree).getValue();
        if(deptIds != undefined && deptIds != null && deptIds != ""){
            var personCount = accGetPersonCountByDeptIds(deptIds);
            if (parseInt(personCount) == 0){
                messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'pers_widget_noDeptPerson'/>"});
                return false;
            }
		}
		var personIdsAndTimes = value;
		var validTimeResult = true;
		var validTip = "";
		if ("${attr('system.levelTime.support','false')}" === "true") {
			var resultMap = accPersonLevelByLevelValidTime(value, this.options.uuid);
			validTimeResult = resultMap.get("timeValidResult");
			validTip = resultMap.get("validTip");
			personIdsAndTimes = resultMap.get("personIdsAndTimes");
		}
		if (validTimeResult) {
			// 获取人员id；支持临时权限组装开始/结束时间
			DhxCommon.closeWindow();
			var opts = {
				dealPath: "accPersonLevelByLevel.do?addPerson",//进度处理请求
				type: "single",//进度类型，取值single,public,custom
				onCallback:"reloadAccPersonLevelByLevel",//处理完后回调
				height:250,//弹窗高度
				useReq:true,//使用请求参数作为进度条参数
				autoClose:true,//自动关闭
				delay:5,//自动关闭时间(5秒后自动关闭)
				title:"<@i18n 'common_progress_proCmd'/>", //弹窗标题
				data:{
					levelId : levelId,
					levelName : levelName,
					personIds : personIdsAndTimes,
					deptIds : deptIds,
					personPins : text
				}
			}
			openProcess(opts);
		} else {
			openMessage(msgType.warning, validTip);
		}
		return false;
    }
	function accPersonLevelByLevelValidTime (value, uuid) {
		var dhGrid = ZKUI.Select.get("accSelectPerson" + uuid).rightGrid.grid;
		var startTimeIndex = 5;
		var endTimeIndex = 6;
		if (sysCfg.language == "zh_CN") {
			startTimeIndex = 4;
			endTimeIndex = 5;
		}
		var timeValidResult = true;
		var validTip = "";
		var personIdsAndTimes = "";
		var resultMap = new Map();
		for (var i = 0; i < dhGrid.rowsCol.length; i++) {
			var id = dhGrid.rowsCol[i].idd;
			var startTimeStr = dhGrid.cellById(id, startTimeIndex).getValue();
			var endTimeStr = dhGrid.cellById(id, endTimeIndex).getValue();
			var pin = dhGrid.cellById(id, 1).getValue();
			if (endTimeStr != '' && startTimeStr != '' && endTimeStr <= startTimeStr) {
				timeValidResult = false;
				validTip = validTip + pin + ":"+ "<@i18n 'common_dsTime_timeValid4'/>" + "</br>";
				dhGrid.cellById(id, startTimeIndex).setValue("");
				dhGrid.cellById(id, endTimeIndex).setValue("");
			} else if ((endTimeStr != '' && startTimeStr == '') || (endTimeStr == '' && startTimeStr != '')) {
				timeValidResult = false;
				validTip = validTip +  pin + ":"+ "<@i18n 'common_levelImport_timeNotNull'/>" + "</br>";
				dhGrid.cellById(id, startTimeIndex).setValue("");
				dhGrid.cellById(id, endTimeIndex).setValue("");
			} else {
				personIdsAndTimes += dhGrid.rowsCol[i].idd + "," + startTimeStr + "," + endTimeStr + ";";
			}
		}
		resultMap.set("timeValidResult",timeValidResult);
		resultMap.set("validTip", validTip);
		resultMap.set("personIdsAndTimes",personIdsAndTimes);
		return resultMap;
	}

    function reloadAccPersonLevelByLevel() {
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        dbGrid.rightGrid.reload();
    }
    function exportLevelPerson(id, bar, opts){
		if(bar) {
			var gridName = bar.gridName;
			var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
			if (ids == "") {
				messageBox({messageType: "alert", text: "<@i18n 'common_prompt_selectObj'/>"});
			} else {
				var leftGrid = ZKUI.DGrid.get("${gridName}").leftGrid;
				var vals = [];
				ids.split(",").forEach(function(id) {//获取某列的值，这里第2列
					var row = leftGrid.grid.getRowData(id);
					vals.push(row.name);
				})
				var levelNames = vals.join(",");
				opts.path = "skip.do?page=acc_personLevelByLevel_opExportLevelPerson&gridName=" + gridName + "&actionName=" + encodeURIComponent(id)+"&levelIds="+ ids + "&levelNames=" + levelNames;
				if(opts.maxExportCount) {
            		opts.path = opts.path + "&maxExportCount=" + opts.maxExportCount;
				}
				opts.width = opts.width || 550;
				opts.height = opts.height || 250;
				DhxCommon.createWindow(opts);
			}
		}
	}

</script>