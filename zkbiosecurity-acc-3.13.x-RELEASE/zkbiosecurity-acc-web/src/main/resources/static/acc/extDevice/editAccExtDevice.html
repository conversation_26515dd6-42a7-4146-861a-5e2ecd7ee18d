<#assign editPage="true">
<#include "/public/template/editTemplate.html">
<#macro editContent>
	<style>
		.rs485p0{
			background : url('${base}/images/rs485/rs485down.png') no-repeat scroll center;
			background-size:13px 31px;
			width:16px;
			height:70px;
			float:left;
			cursor:pointer;
			text-align:center;
			color: white;
			margin: 1px;
			position:relative;
		}

		.rs485p0 p{
			position: absolute;
			bottom: 0px;
			padding-left: 5px;
		}

		.rs485p1{
			background : url('${base}/images/rs485/rs485up.png') no-repeat scroll center;
			background-size:13px 31px;
			width:16px;
			height:70px;
			float:left;
			cursor:pointer;
			text-align:center;
			color: white;
			margin: 1px;
			position:relative;
		}

		.rs485p1 p{
			position: absolute;
			bottom: 0px;
			padding-left: 5px;
		}
	</style>
	<script type="text/javascript">
		$("#${formId}").validate( {
			debug : true,
			rules :
			{
                "extBoardType": {
                    required: true
				},
                "commAddress" : {
                    required: true,
					digits: true,
					range: [1,15],
                    extCommAddressValid : true
				},
                "deviceAlias" : {
                    required: true
                },
				"alias" : {
            		required: true,
                    overRemote : [ "accExtDevice.do?isExistAlias", "${(item.alias)!}" ]
        		},
				"devProtocolType" : {
                	required: true
				}
			},
			submitHandler : function()
			{
                ZKUI.Combo.get('extBoardType').combo.disable(false);
                ZKUI.Combo.get('devProtocolType').combo.disable(false);
				<@submitHandler/>;
			}
		});

        paintRS485Pic(${(item.commAddress)!0});

        function paintRS485Pic(rs485Addr)
        {
            if(rs485Addr >= 0 && rs485Addr < 16)
            {
                for(var i = 1; i <= 4; i++)
                {
                    $("#${formId} #rs" + i).attr("class", "rs485p" + ((rs485Addr & Math.pow(2, i - 1)) >> (i - 1)));
                }
            }
        }

        function updateRS485Addr(rs485Obj)
        {
            var rs485Addr = 0;
            if($(rs485Obj).attr("class") == "rs485p1")
            {
                $(rs485Obj).attr("class", "rs485p0");
            }
            else if($(rs485Obj).attr("class") == "rs485p0")
            {
                $(rs485Obj).attr("class", "rs485p1");
            }
            for(var i = 1; i <= 4; i++)
            {
                if($("#${formId} #rs" + i).attr("class") == "rs485p1")
                {
                    rs485Addr += Math.pow(2, i - 1);
                }
            }
            $("#${formId} #commAddress").val(rs485Addr);
        }

        function selectDeviceHandler(value,text,event){
            $("#${formId} #deviceAlias").val(text);
            $("#${formId} #devId").val(value);
			ZKUI.Combo.get('devProtocolType').load("accExtDevice.do?getDevProtocolTypeSelect&devId="+$("#devId").val());
        }

        function extBoardTypeChange(value, text, event) {
            if ($("#devId").val() != "") {
                ZKUI.Combo.get('extBoardType').load("accExtDevice.do?getExtBoardTypeSelects&devId="+$("#devId").val()+"&devProtocolType="+value);
            }
		}

        if ($("#devId").val() != "") {
            setTimeout(function() {
                ZKUI.Combo.get('extBoardType').combo.disable(true);
                //编辑情况下不允许修改协议类型
				ZKUI.Combo.get('devProtocolType').combo.disable(true);
            }, 100);
        }

        jQuery.validator.addMethod("extCommAddressValid", function(value, element) {
            var flag = true;
            if (value != "${item.commAddress!}") {
				var devId = $("#devId").val();
				$.ajax({
					type: "POST",
					url: "accExtDevice.do?isExistAddress&commAddress=" + value + "&devId=" + devId,
					async:false,
					success: function(data)
					{
						flag = data;
					}
				});
            }
            return flag;
        }, "<@i18n 'common_jqMsg_remote'/>");

        <#if !(item.id)??>
        var opts = {
            path: "skip.do?page=acc_extDevice_accExtDeviceSelectDeviceRadio",
            width: 920,
            height: 480,
            title: "",
            onSure:"selectDeviceHandler"
        };
        selectContent("#${formId} #deviceAlias", opts);
		</#if>
	</script>
	<form action="accExtDevice.do?save" method="post" onkeydown="if(event.keyCode==13){return false;}" id="${formId}" enctype="multipart/form-data">
		<input type="hidden" id="id_model_pk" name="id" value="${(item.id)!}"/>
		<input type="hidden" id="devId" name="devId" value="${(item.devId)!}"/>
		<table class="tableStyle">
			<tr>
				<th>
					<label><@i18n 'common_name'/></label><span class="required">*</span>
				</th>
				<td>
					<input type="text" id="alias" name="alias" value="${(item.alias)!}"/>
				</td>
			</tr>
			<tr>
				<th>
					<label><@i18n 'common_dev_name'/></label><span class="required">*</span>
				</th>
				<td>
					<#if (item.id)??>
					<input type="text" id="deviceAlias" name="deviceAlias" value="${(item.deviceAlias)!}" readonly="readonly"/>
					<#else>
					<input type="text" id="deviceAlias" name="deviceAlias" value="${(item.deviceAlias)!}" readonly="true" placeholder="<@i18n 'common_op_clickChoice'/>"/>
					</#if>
				</td>
			</tr>
			<tr>
				<th>
					<label><@i18n 'acc_dev_protocol'/></label><span class="required">*</span>
				</th>
				<td>
					<@ZKUI.Combo empty="true" name="devProtocolType" id="devProtocolType" hideLabel="true" width="148" value="${(item.devProtocolType)!}" path="accExtDevice.do?getDevProtocolTypeSelect&devId=${(item.devId)!}" onChange="extBoardTypeChange">
					</@ZKUI.Combo>
				</td>
			</tr>
			<tr>
				<th>
					<label><@i18n 'acc_dev_extBoardType'/></label><span class="required">*</span>
				</th>
				<td>
					<@ZKUI.Combo empty="true" name="extBoardType" id="extBoardType" readonly="true" hideLabel="true" width="148" value="${(item.extBoardType)!}" path="accExtDevice.do?getExtBoardTypeSelects&devId=${(item.devId)!}&devProtocolType=${item.devProtocolType}">
					</@ZKUI.Combo>
				</td>
			</tr>
			<tr>
				<th>
					<label><@i18n 'common_dev_rs485Address'/></label><span class="required">*</span>
				</th>
				<td>
					<input type="text" id="commAddress" name="commAddress" value="${(item.commAddress)!}" onkeyup="paintRS485Pic(this.value)" maxlength="2"/>
				</td>
			</tr>
			<tr>
				<th>
					<label><@i18n 'acc_dev_rs485AddrFigure'/></label>
				</th>
				<td>
					<div class="rs485-bg-color" style="border-width: 1px; border-style: solid; width: 78px; height:67px; background-color: #464a4e;">
						<div id="rs1" class="rs485p0" onclick="updateRS485Addr(this)"><span style="font-size: 10px; word-wrap:normal !important; ">ON</span><p>1</p></div>
						<div id="rs2" class="rs485p0" onclick="updateRS485Addr(this)"><p>2</p></div>
						<div id="rs3" class="rs485p0" onclick="updateRS485Addr(this)"><p>3</p></div>
						<div id="rs4" class="rs485p0" onclick="updateRS485Addr(this)"><span style="font-size: 10px; word-wrap:normal !important;">KE</span><p>4</p></div>
					</div>
				</td>
			</tr>
		</table>
		<div style="margin-top: 10px; margin-${leftRTL!'left'}: 15px; text-align: ${leftRTL!'left'};>
			<span class="warningImage"></span>
			<span class="warningColor"><@i18n 'acc_dev_extBoardTip'/></span>
		</div>
	</form>
</#macro>
