<#assign gridName="accExtDeviceGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
				<td valign="middle">
					<@ZKUI.Input name="alias" maxlength="30" title="common_dev_name" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.ComboTree type="radio" url="authArea.do?tree" title="base_area_name" name="authAreaId" readonly="readonly"/>
				</td>
			</tr>
    	</@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="reloadDevGrid" permission="acc:extDevice:refresh"/>
		<@ZKUI.ToolItem id="accExtDevice.do?edit" text="common_op_new" width="450" height="350" img="comm_add.png" action="commonAdd" permission="acc:extDevice:add"/>
		<@ZKUI.ToolItem id="accExtDevice.do?del&alias=(alias)" text="common_op_del" img="comm_del.png" action="commonDel" permission="acc:extDevice:del"/>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccExtDeviceItem" query="accExtDevice.do?list"/>
</@ZKUI.GridBox>