<#assign formId = "${uuid!}">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
<script type="text/javascript" src="/system/js/ajaxfileupload.js" charset="UTF-8"></script>
<script type="text/javascript" src="/system/js/baseMediaFile.js" charset="UTF-8"></script>
<form action='/accDeviceEvent.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
	<input type='hidden' name='id' value='${(item.id)!}'/>
	<input type="hidden" name="opType" id="opType" value="select"/>
	<input type="hidden" name="baseMediaFileName" id="baseFileName" value=""/>
	<input type="hidden" name="baseMediaFilePath" id="delPath" value=""/>
	<input type="hidden" name="baseMediaFileSize" id="baseFileSize" value=""/>
	<input type="hidden" name="baseMediaFileSuffix" id="baseFileSuffix" value=""/>
	<table class='tableStyle'>
		<tr>
			<th><label><@i18n 'common_dev_name'/></label><span class='required'>*</span></th>
			<td><input name='devAlias' type='text' value='${(item.devAlias)!}' disabled="disabled" /></td>
		</tr>
		<tr>
			<th><label><@i18n 'common_event_name'/></label><span class='required'>*</span></th>
			<td><input name='name' type='text' value='${(item.name)!}' disabled="disabled"/></td>
		</tr>
		<tr>
			<th><label><@i18n 'common_event_number'/></label><span class='required'>*</span></th>
			<td><input name='eventNo' type='text' value='${(item.eventNo)!}' disabled="disabled"/></td>
		</tr>
		<tr>
			<th><label><@i18n 'common_event_level'/></label><span class='required'>*</span></th>
			<td>
				<@ZKUI.Combo empty="false" hideLabel="true" value="${(item.eventLevel)!}" readonly="true" disable="true" id="eventLevel${uuid}" name="eventLevel" width="148">
				<option value="0"><@i18n 'common_normal'/></option>
				<option value="1"><@i18n 'common_exception'/></option>
				<option value="2"><@i18n 'common_alarm'/></option>
			</@ZKUI.Combo>
			</td>
		</tr>
		<#if (item.eventLevel)?exists && (item.eventLevel)==2>
		<tr>
			<th><label><@i18n 'acc_alarm_priority'/></label><span class='required'>*</span></th>
			<td>
				<@ZKUI.Combo empty="false" hideLabel="true" value="${(item.eventPriority)!}" id="eventPriority${uuid}" name="eventPriority" width="148">
				<option value="0"><@i18n 'auth_security_strengthLevel0'/></option>
				<option value="1"><@i18n 'auth_security_strengthLevel1'/></option>
				<option value="2"><@i18n 'auth_security_strengthLevel2'/></option>
				<option value="3"><@i18n 'acc_musterPointReport_danger'/></option>
			</@ZKUI.Combo>
			</td>
		</tr>
		</#if>
		<tr style="display:none">
			<th><label><@i18n 'acc_deviceEvent_sound'/></label></th>
			<td style="white-space: nowrap">
				<@ZKUI.Input hideLabel="true" type="radio" id="idSelect" name="selectOrUpload" value="1" checked="checked" />
				<span><@i18n 'acc_deviceEvent_exist'/></span>
				<@ZKUI.Input hideLabel="true" type="radio" id="idUpload" name="selectOrUpload" value="2"/>
				<span><@i18n 'acc_deviceEvent_upload'/></span>
			</td>
		</tr>
		<tr>
			<th><label><@i18n 'acc_deviceEvent_sound'/></label></th>
			<td>
				<div style="display: flex;align-items: center;">
					<@ZKUI.ComboTree id="baseMediaFileId" name="baseMediaFileId" value="${(item.baseMediaFileId)!}" readonly="true" url="/baseMediaFile.do?getMediaFileList" hideLabel="true" width="148" type="radio" tree_onCheck="changePreView">
					</@ZKUI.ComboTree>
					<div class="upload-file-box">
						<input type="button" name="videoButton" id="selectVideoButton" class="upload-file-button" value="<@i18n 'base_file_play' />" disabled="disabled" onclick = "audioPlay();" style="cursor:pointer; height: 22px"/>
					</div>
				</div>
			</td>
		</tr>
		<tr id="uploadLabel" style="display:none">
			<th></th>
			<td>
			<div style="display: flex;align-items: center">
				<div style="min-width:230px;">
					<@ZKUI.Upload id="uploadBtn" file_id="upload" name="upload" file_accept="audio/mpeg,audio/wav" style="cursor: pointer;" onChange="fileUpload" />
				</div>
				<div class="upload-file-box">
					<input type="button" name="videoButton" id="uploadVideoButton" class="upload-file-button" value="<@i18n 'base_file_play'/>" onclick = "audioPlay();" disabled="disabled" style="cursor:pointer;height: 22px"/>
				</div>
			</div>
			</td>
		</tr>	
		<tr id="uploadRemind" style="display:none;">
			<td colspan=2>
				<div style="margin-${leftRTL!'left'}: 15px">
					<span class="warningImage"></span><span class="warningColor"><@i18n 'base_file_typeAndSizRemind'/></span>
				</div>
			</td>
		</tr>
		<tr>
			<td>
				<input name="selectPath" type="hidden" id="selectPath" value="${(item.baseMediaFilePath)!}"/>
				<input name="uploadPath" type="hidden" id="uploadPath" value=""/>
				<div id ="alert_sound"></div>
			</td>
		</tr>
		<tr>
			<th><label><@i18n 'common_event_copy'/></label></th>
			<td><@ZKUI.Input hideLabel="true" type="checkbox" name="syncAllDevice" id="syncAllDevice" trueValue="1" falseValue="0" eventCheck="true"/></td>
		</tr>
	</table>
</form>

<script type='text/javascript'>
	$(function() {
		$('#${formId}').validate( {
			debug : true,
			rules :
			{
				'eventNo' :
				{
					required : true
				},
				'name' :
				{
					required : true
				},
				'eventLevel' :
				{
					required : true
				},
				'upload':
				{
					accept: "mp3|MP3|wav|WAV|mpeg|MPEG"
				}
			},
			submitHandler : function()
			{
				stopAudio('videoButton','alert_sound');
				ZKUI.Combo.get("eventLevel${uuid}").combo.disable(false);
				$("#${formId} [name='name']").attr("disabled", false);
				<@submitHandler/>
			}
		});
	});
	
	var playPath = $("#${formId} #selectPath").val();
	if(playPath)
	{
		loadAudio(playPath,'alert_sound');
	}

	var bgsoundStat=1;
	function audioPlay()
	{
		var playPath;
		switch($("input[name$=selectOrUpload]:checked").attr("id"))
		{
			case "idSelect":
				playPath = $("#${formId} #selectPath").val();
				break;
			case "idUpload":
				playPath = $("#${formId} #uploadPath").val();
				break;
			default:
				break;
		}
		playOrStopAudio(playPath,'videoButton','alert_sound',bgsoundStat);
		if(bgsoundStat == 1)
		{
			bgsoundStat = 2;
		}
		else
		{
			bgsoundStat = 1;
		}
	}
	
	var mediaFileId = "${(item.baseMediaFileId)!}";
	if(mediaFileId != "")
	{
        $("#selectVideoButton").removeAttr("disabled");
    }
	//var comboxTree = loadComboxTree("soundFileName","baseMediaFileAction!getBaseMediaFileTreeJSON.action");
	//添加选中事件，为了联动
	//comboxTree.attachEvent("onCheck",changePreView);
	function changePreView(id,state)
	{
		$.ajax({
	        type: "post",
	        url: "/baseMediaFile.do?getFileWebUrl",
	        dataType: "json",
	        data: "fileId=" + id,
	        success: function (result) {
	            var dataObj=eval(result);
	    		if($("input[name$='baseMediaFileId']").val()=="")
	   			{
					$("#${formId} #selectVideoButton").attr({disabled:"disabled"});
				}
		 		else
			 	{
					$("#${formId} #selectVideoButton").removeAttr("disabled");
		 		}
				if($("#${formId} #myaudio").length>0)
				{
				 	$("#${formId} #myaudio").remove();
				}
				bgsoundStat = 1;
	            $("input[name$=videoButton]").attr("value","<@i18n 'base_file_play'/>");
	            var temp = dataObj.data;
	            $("#${formId} #selectPath").val(temp);
	            loadAudio(temp,'alert_sound');
	        },
		    error:function (XMLHttpRequest, textStatus, errorThrown)
			{
			    messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>" + "-628"});
			}
	      });
	}
	
	function fileUpload()
	{
		var docObj = document.getElementById("upload");
    	if (docObj.files && docObj.files[0]) {
			onLoading(function(){
				$.ajaxFileUpload({
					url: '/baseMediaFile.do?saveUploadFile', //用于文件上传的服务器端请求地址
					secureuri: false, //是否需要安全协议，一般设置为false
					fileElementId: 'upload', //文件上传域的ID
					dataType: 'json', //返回值类型 一般设置为json
					success: function (data, status)  //服务器成功响应处理函数
					{
						var dataObj = eval(data);
						if(dataObj.ret=="ok")
						{
							$("#uploadMsg").text("<@i18n 'base_file_alreadyUploaded'/>")
							var mymsg = eval(dataObj.data);
							$("#${formId} #baseFileName").val(mymsg.name);//文件的别名
							$("#${formId} #delPath").val(mymsg.path);//临时文件路径
							$("#${formId} #baseFileSize").val(mymsg.size);//文件大小
							$("#${formId} #baseFileSuffix").val(mymsg.suffix);//文件后缀名
							//将id临时保存，并且将文件路径临时保存，待提交时，根据radio值决定上传哪一个
							$("#${formId} #uploadPath").val(mymsg.path);
							loadAudio(mymsg.path,'alert_sound');
							$("#${formId} #uploadVideoButton").attr("value","<@i18n 'base_file_play'/>");
							//$("#${formId} [name='baseMediaFileId']").attr("value","");
							$("#${formId} #uploadVideoButton").removeAttr("disabled");
							bgsoundStat = 1;
							openMessage(msgType.success);
						}
						else
						{
							ZKUI.Upload.get("uploadBtn").resetText();
							openMessage(msgType.error,dataObj.msg);
						}
						// 重新初始化，避免再次选择文件触发不了onChange事件
						ZKUI.Upload.get("uploadBtn").init();
					},
					error: function (data, status, e)//服务器响应失败处理函数
					{
						if(status == 0)
						{
							openMessage(msgType.error, "<@i18n 'common_prompt_serverFailed'/>");
						}
						else
						{
							openMessage(msgType.error, "<@i18n 'common_prompt_serverError'/>" + XMLHttpRequest.status);
						}
						ZKUI.Upload.get("uploadBtn").init();
					}
				})
			});
		}
	}
	
	$("input[name$=selectOrUpload]").change(function(){
		switch($("input[name$=selectOrUpload]:checked").attr("id"))
		{
			case "idSelect":
				$("#${formId} #opType").val("select");
				$("#${formId} #selectLabel").css("display","");
				$("#${formId} #uploadLabel").css("display","none");
				$("#${formId} #uploadRemind").css("display","none");
				//音频停掉
				bgsoundStat = 1;
				if($("#${formId} #selectPath").val() != "")
				{
					loadAudio($("#${formId} #selectPath").val(),'alert_sound');
				}
				stopAudio('videoButton','alert_sound');
				break;
			case "idUpload":
				$("#${formId} #opType").val("upload");
				$("#${formId} #selectLabel").css("display","none");
				$("#${formId} #uploadLabel").css("display","");
				$("#${formId} #uploadRemind").css("display","");
				//音频停掉
				bgsoundStat = 1;
				if($("#${formId} #uploadPath").val() != "")
				{
					loadAudio($("#${formId} #uploadPath").val(),'alert_sound');
				}
				stopAudio('videoButton','alert_sound');
				break;
			default:
			break;
		}
	});
	
	//radio控件清空操作绑定点击事件，将播放按钮置灰
	$("#baseMediaFileIdTreeBoxIdClearAll").click(function(){
		$("#${formId} #selectVideoButton").attr({disabled:"disabled"});
	});
</script>
</#macro>