<#assign gridName="accDeviceEventGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
				<td valign="middle">
					<@ZKUI.ComboGrid id="accEventId${uuid}" title="common_eventDescription" width="137" queryField="name" name="name"
					grid_vo="com.zkteco.zkbiosecurity.acc.vo.AccDeviceEventSelectItem"
					grid_query="accDeviceEvent.do?listSelect"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="eventNo"  maxlength="4" title="common_event_number" type="text" onlyNum="true"/>
				</td>
				<td valign="middle">
				    <@ZKUI.Combo name="eventLevel" readonly="readonly" title="common_event_level" empty="true" >
		                <option value="0"><@i18n 'common_normal'/></option>
		                <option value="1"><@i18n 'common_exception'/></option>
		                <option value="2"><@i18n 'common_alarm'/></option>
	                </@ZKUI.Combo>
				</td>
			</tr>
    	</@ZKUI.SearchTop>
 		<@ZKUI.SearchBelow>
 			<tr>
 			    <td valign="middle">
					<@ZKUI.Input name="devAlias"  maxlength="30" title="common_dev_name" type="text"/>
				</td>
 			</tr>
 		</@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="acc:deviceEvent:refresh"/>
    	<@ZKUI.ToolItem id="/skip.do?page=acc_deviceEvent_editAccDeviceEventSound&name=(name)" text="acc_deviceEvent_batchSet" img="acc_setSound.png" action="setSound" permission="acc:deviceEvent:setSound"/>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccDeviceEventItem" query="/accDeviceEvent.do?list"/>
</@ZKUI.GridBox>
<script type="text/javascript">
//设置铃声
function setSound(id,bar)
{
    var path = id;
	var gridName = bar.gridName || "gridbox";
	var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);//获取选中列的ID
	if(ids == "") {
		messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});
	}
    else if(ids.split(",").length > 10)
    {
        messageBox({messageType:"alert",text:"<@i18n 'common_prompt_onlySelectTenObject'/>"});
    }
	else 
	{
        path += "&ids="+ids;
		var opts = {//弹窗配置对象
				path: path,//设置弹窗路径
				width: 480,//设置弹窗宽度
				height: 200,//设置弹窗高度
				title: "<@i18n 'acc_deviceEvent_batchSet'/>",//设置弹窗标题
				gridName: "gridbox"//设置grid

			};
		DhxCommon.createWindow(opts);
	}
}

</script>