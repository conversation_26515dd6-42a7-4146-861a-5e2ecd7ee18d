<#assign gridName="accFirstOpenGrid${uuid!}">
<script type="text/javascript">
    function accFirstOpenLeftGridClick(rid) {
        //此方法注册的是官方事件，所以this指代的是官方grid对象，通过访问属性zkgrid获取我们自定义的grid对象
        var leftGrid = this.zkgrid;//获取左表格
        var dbGrid = ZKUI.DGrid.get(leftGrid.options.dGridName);//双列表对象
        var rightGrid = dbGrid.rightGrid;//右表格对象
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        rightGrid.reload(function() {
            rightFirstOpenCallback(row.id, rightGrid, rid);//手动回调
        },{linkId:rid});//传递id到右表格查询
    }

    //右表格回调事件
    function rightFirstOpenCallback(id, rightGrid, rid) {
        var dbGrid = ZKUI.DGrid.get(rightGrid.options.dGridName);//双列表对象
        var leftGrid = dbGrid.leftGrid;//获取左表格
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
       // rightGrid.setTitle("已选部门:"+row.name);//设置右表格标题
    }

    function afterAddFirstOpenPerson(value, text, event) {
        var firstOpenId = this.options.linkId;
        var doorName = this.options.linkName;
        var personDeptTree="accPersonDeptTree"+this.options.uuid;
        var deptIds = ZKUI.ComboTree.get(personDeptTree).getValue();
        if(deptIds != undefined && deptIds != null && deptIds != ""){
            var personCount = accGetPersonCountByDeptIds(deptIds);
            if (parseInt(personCount) == 0){
                messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'pers_widget_noDeptPerson'/>"});
                return false;
            }
            if(text == "") {
            	text = ZKUI.ComboTree.get(personDeptTree).getText();
            }
        }
        onLoading(function(){
            $.ajax({
                url:"accFirstOpen.do?addPerson",
                type:"post",
                dataType:"json",
                data:{
                    "firstOpenId" : firstOpenId,
                    "personIds" : value,
                    "deptIds" : deptIds,
                    //下面两个参数是为了添加系统日志新增的，还待改善。
                    "doorName" : doorName,
                    "personPins" : text
                },
                success:function (result) {
                    openMessage(msgType.success);
                    var dbGrid = ZKUI.DGrid.get("${gridName}");
                    dbGrid.rightGrid.reload();
                }
            });
        });
    }

    /*function delFirstOpenPerson(id, bar){
        var gridName=bar.gridName||"gridbox";
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        var leftGrid = dbGrid.leftGrid;
        var rightGrid = dbGrid.rightGrid;
		var leftIds = leftGrid.grid.getSelectedRowId();  //获取左列表选择中的id
		var rightIds = rightGrid.grid.getCheckedRows(0);  //获取右列表选中的ids
		if(leftIds=="" || leftIds ==null || rightIds=="" || rightIds==null ){
            messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});
		}else {
            deleteConfirm(function(result){
                if(result){
                    $.ajax({
                        url:fillParamsFromGrid(gridName, rightIds, id),
                        type:"post",
                        dataType:"json",
                        data:{
                            "firstOpenId" : leftIds,
                            "personIds" :rightIds
                        },
                        success:function (result) {
                            openMessage(msgType.success);
                            setPersonCount(result.data, dbGrid.leftGrid);
                        }
                    });
                }
            },"common_prompt_sureToDelThese");
		}
	}*/

</script>
<@ZKUI.DGrid gridName="${gridName}">
	<@ZKUI.LeftGrid title="acc_firstOpen_setting">
		<@ZKUI.Searchbar>
			<@ZKUI.SearchTop>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="doorName" maxlength="30" title="acc_door_name" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Input name="deviceAlias" maxlength="30" title="common_dev_name" type="text"/>
					</td>
				</tr>
			</@ZKUI.SearchTop>
		</@ZKUI.Searchbar>
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="acc:firstOpen:refresh"/>
			<@ZKUI.ToolItem id="accFirstOpen.do?edit" text="common_op_new" width="400" height="180" img="comm_add.png" action="commonAdd" permission="acc:firstOpen:add"/>
			<@ZKUI.ToolItem id="accFirstOpen.do?del&doorNames=(doorName)" text="common_op_del" img="comm_del.png" action="accDGridCommonDel" permission="acc:firstOpen:del"/>
		</@ZKUI.Toolbar>
		<!-- 配置左表格选中事件 -->
		<@ZKUI.Grid onRowSelect="accFirstOpenLeftGridClick" vo="com.zkteco.zkbiosecurity.acc.vo.AccFirstOpenItem" query="accFirstOpen.do?list" autoFirst="true"/>
	</@ZKUI.LeftGrid>
	<@ZKUI.RightGrid title="acc_firstOpen_browsePerson" leftFieldName="linkId">
		<@ZKUI.Searchbar>
			<@ZKUI.SearchTop>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="personPin" maxlength="30" title="pers_person_pin" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Input name="likeName" maxlength="24" title="pers_person_wholeName" type="text"/>
					</td>
					<!--<td valign="middle">
						<@ZKUI.Input name="personLastName" maxlength="24" title="pers_person_lastName" type="text" showExpression = "#language!='zh_CN'"/>
					</td>-->
				</tr>
			</@ZKUI.SearchTop>
			<@ZKUI.SearchBelow>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="deptName" maxlength="30" title="pers_dept_deptName" type="text"/>
					</td>
				</tr>
			</@ZKUI.SearchBelow>
		</@ZKUI.Searchbar>
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem type="refresh" permission="acc:firstOpen:refresh"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="accFirstOpen.do?delPerson&firstOpenId=(@id:gs)&doorName=(@doorName:gs)&personIds=(id)&personPins=(personPin)" accModuleType="person" action="accRightGridCommonDel" text="pers_common_delPerson" img="comm_del.png" permission="acc:firstOpen:delPerson"/>
		</@ZKUI.Toolbar>
		<@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccPersonFirstOpenItem" query="accFirstOpen.do?personList"/>
	</@ZKUI.RightGrid>
</@ZKUI.DGrid>
