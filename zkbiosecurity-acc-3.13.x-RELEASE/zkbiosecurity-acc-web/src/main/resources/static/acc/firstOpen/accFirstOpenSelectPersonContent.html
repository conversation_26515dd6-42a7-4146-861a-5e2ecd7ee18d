<div id="accDivSearchType" style="height:25px;line-height:25px;display:block">
    <label style="margin-left:5px"><@ZKUI.Input id="searchType1${uuid}" name="searchType" value="1" checked="" type="radio"/>&nbsp;<@i18n 'pers_widget_searchType1'/></label>
    <label style="margin-left:30px"><@ZKUI.Input id="searchType2${uuid}" name="searchType" value="2" type="radio"/>&nbsp;<@i18n 'pers_widget_searchType2'/></label>
</div>
<div id="accMaskLayer${uuid}" style="position: absolute;width: 100%;display: none; top:30px;bottom:40px;z-index: 10000;opacity: 0.5;background-color: #dcdfe2;">
</div>
<@ZKUI.SelectContent setMode="true,true,false,true" uuid="${uuid}" gridName="accSelectPerson${uuid}" showColumns="checkbox,personPin,personName,personLastName,deptName" style="position:absolute;top:30px;bottom:0px;left:0px;right:0px;height:auto;" accFirstOpenId="${accFirstOpenId!}" copy="true" textField="personPin" vo="com.zkteco.zkbiosecurity.acc.vo.AccSelectPersonItem" query="accPerson.do?selectPersonlist&accFirstOpenId=${linkId!}">
    <div id="accDeptTree${uuid}" class="zk-content-bg-color" style="position: absolute;z-index: 10001;display: none;background-color: #f3f5f0;opacity:1;width:100%;height: 42px;padding-top: 15px;">
        <@ZKUI.ComboTree id="accPersonDeptTree${uuid}" type="checkbox" url="authDepartment.do?tree" tree_onCheck="onTreeChecked" title="base_department_deptName" width="148" readonly="true" name="deptIds"/>
        <div id="accDeptZone" style="display:inline-block;">
            <span style="color:#FF9900; position : relative; padding-left: 50px;"><@i18n 'pers_widget_deptHint'/></span>
        </div>
    </div>
    <@ZKUI.Searchbar>
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="personPin"  maxlength="30" title="pers_person_pin" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="likeName"  maxlength="50" title="pers_person_wholeName" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="deptName"  maxlength="30" title="pers_dept_deptName" type="text"/>
                </td>
                <!--<td valign="middle">-->
                <!--<@ZKUI.Input name="personLastName"  maxlength="50" title="pers_person_lastName" type="text" showExpression="#language!='zh_CN'"/>-->
                <!--</td>-->
                <!--<td valign="middle">
                    <@ZKUI.Input name="cardNo"  maxlength="30" title="pers_card_cardNo" type="text"/>
                </td>-->
            </tr>
        </@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
</@ZKUI.SelectContent>
<script type="text/javascript">

    $("#searchType1${uuid}").click(function(){
        accDeptSwitch(1);
    });
    $("#searchType2${uuid}").click(function(){
        accDeptSwitch(2);
    });

    function accDeptSwitch(t){
        if(t == 2){
            $("#accMaskLayer${uuid},#accDeptTree${uuid}").show();
            $("#"+ ZKUI.Select.get("accSelectPerson${uuid}").options.gridName+"ok").attr("disabled", false);
        }else{
            ZKUI.ComboTree.get("accPersonDeptTree${uuid}").clear();
            $("#accMaskLayer${uuid},#accDeptTree${uuid}").hide();
        }
    };
</script>