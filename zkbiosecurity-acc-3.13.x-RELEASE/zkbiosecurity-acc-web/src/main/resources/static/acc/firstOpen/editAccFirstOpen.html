<#assign editPage="true">
<#assign formId = "${uuid!}">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
<script type='text/javascript'>
    $(function() {
        var opts = {
            path: "skip.do?page=acc_firstOpen_accFirstOpenSelectDoorRadio",
            width: 950,
            height: 500,
            title: "",
            onSure:"selectDoorHandler"
        };
        selectContent("#${formId} #doorName", opts);

        $('#${formId}').validate( {
            debug : true,
            rules :
                {
                    "doorId" : {
                        required : true
                    },
                    "timeSegId" : {
                        required : true
                    }
                },
            submitHandler : function()
            {
            	<@submitHandler/>
            }
        });
    });

    <#if (item.id)??>
    	$("#${formId} #doorName").attr("disabled", true);
    </#if>

    function selectDoorHandler(value,text,event){
        var oldId = $("#${formId} #doorId").val();
        $("#${formId} #doorId").val(value);
        if (oldId != value)
        {
            $("#${formId} #doorId").change();
        }
        $("#${formId} #doorName").val(text);
        $("#${formId} #doorId").valid();
    }
</script>

<form action='accFirstOpen.do?save' method='post' id='${formId}' enctype='multipart/form-data'>
	<div id="baseTab">
		<input type="hidden" name="id" value="${(item.id)!}"/>
		<table class='tableStyle'>
			<tr>
				<th>
					<label><@i18n 'acc_door_name'/></label><span class="required">*</span>
				</th>
				<td>
					<#if (item.id)??>
					<input type="text" id="doorName" name="doorName" value="${(item.doorName)!}" readonly="readonly" />
					<#else>
					<input type="text" id="doorName" name="doorName" value="${(item.doorName)!}" readonly="true" placeholder="<@i18n 'common_op_clickChoice'/>"/>
					</#if>
					<input type="hidden" id="doorId" name="doorId" value="${(item.doorId)!}"/>
				</td>
			</tr>
			<tr>
				<th><label><@i18n 'acc_door_passageModeTimeZone'/></label><span class="required">*</span></th>
				<td>
					<@ZKUI.Combo name="timeSegId" width="148" value="${(item.timeSegId)!}" readonly="true" hideLabel="true" empty="true" title="acc_door_passageModeTimeZone" path="/accTimeSeg.do?getTimeSegList">
					</@ZKUI.Combo>
				</td>
			</tr>
		</table>
	</div>
</form>
</#macro>