<#assign gridName="accAuthorizeChildDevGrid${uuid!}">
<input type="hidden" id="cmdId" value="${cmdId}">
<div style="padding:10px;height: 95%">
    <@ZKUI.GridBox gridName="${gridName}" nopaging="true">
        <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem" query="/accTransaction.do?getTransactionsTodayLast&eventPointType=${eventPointType}&eventPointId=${eventPointId}"/>
    </@ZKUI.GridBox>
</div>
<script type="text/javascript">
    var systemModules = "${systemModules}";

    // 关闭时设置存在的modal为false，避免视频预览窗口未关闭，导致的无法操作问题
    DhxCommon.getCurrentWindow().attachEvent("onClose", function() {
        DhxCommon.getCurrentWindow().setModal(false);
        return true;
    });
</script>