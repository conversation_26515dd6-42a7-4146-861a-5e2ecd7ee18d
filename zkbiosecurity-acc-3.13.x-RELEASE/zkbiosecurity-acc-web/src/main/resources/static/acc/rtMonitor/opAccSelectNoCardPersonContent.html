<@ZKUI.SelectContent showColumns="id,personPin,personName,personLastName,deptName" onSure="accRTMonitorSelectNoCardPerson" type="radio" copy="true" textField="personPin" vo="com.zkteco.zkbiosecurity.acc.vo.AccSelectPersonRadioItem" query="/accPerson.do?getNoCardPerson">
    <@ZKUI.Searchbar>
        <@ZKUI.SearchTop>
            <tr>
                <input type="hidden" id="filterId" name="filterId" value="${filterId}"/> <!--过滤的id-->
                <td valign="middle">
                    <@ZKUI.Input name="personPin"  maxlength="30" title="pers_person_pin" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="likeName"  maxlength="50" title="pers_person_wholeName" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="deptName" width="120" maxlength="30" title="pers_dept_deptName" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
</@ZKUI.SelectContent>
<script type="text/javascript">
    function accRTMonitorSelectNoCardPerson(value, text, event) {
        var cardNo = "${cardNo}";
        $.ajax({
            url : "/accPerson.do?setPersonCard&personId=" + value + "&cardNo=" + cardNo,
            success: function(data) {
                if(data.ret == "ok")
                {
                    // closeWindow();
                    openMessage(msgType.success);
                }
            },
            error:function(XMLHttpRequest, textStatus, errorThrown)
            {
                messageBox({messageType:"alert", text: "<@i18n 'common_prompt_serverError'/>"+"-615"});
            }
        });
        // return false;
    }
</script>