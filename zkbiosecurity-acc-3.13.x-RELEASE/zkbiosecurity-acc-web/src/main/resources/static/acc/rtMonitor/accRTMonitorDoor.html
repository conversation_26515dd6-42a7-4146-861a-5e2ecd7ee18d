<#assign leftRTL = "left">
<#assign rightRTL = "right">
<#if enableRTL?? && enableRTL=="true">
<!-- 定义全局对齐方式变量(针对阿拉伯页面) -->
<#assign leftRTL = "right">
<#assign rightRTL = "left">
</#if>
<!-- 门DataView模板--begin -->
<textarea id="doorTemplateContainer${tabUUID!}" class="displayN">
	<div align="center" onclick="clickCenter(event,this)" class="unSelectDiv">
		<div onmouseover="showPopUpInfo(this);" onmouseout="accRTMonitorHidePopUpInfo(this);">
			<div onmousedown="return false;" class="acc_rt_doorImg door_{obj.iconFolderName}_{obj.image}" ></div>
<!-- 			<img onmousedown="return false;" border="0" src="${base}/images/doorState/icon{obj.iconFolderName}/door_{obj.image}.png"> -->
			<div class="displayN dataViewOpItem" style="position: fixed;top: 263px;${leftRTL!'left'}: 325px;z-index: 9999999;" align="${leftRTL!'left'}">
				<div class="poPupInfo">
					<p class="poPupInfo_title_p"><@i18n 'common_status'/></p>
					<table class="poPupInfo_text_table">
						<tr>
							<td><@i18n 'common_dev_entity'/>:</td>
							<td>{obj.devAlias}</td>
						</tr>
						<tr>
							<td><@i18n 'common_dev_sn'/>:</td>
							<td>{obj.devSn}</td>
						</tr>
						<tr>
							<td><@i18n 'common_number'/>:</td>
							<td>{obj.no}</td>
						</tr>
						<tr>
							<td><@i18n 'acc_door_sensor'/>:</td>
							<td>{obj.sensor}</td>
						</tr>
						<tr>
							<td><@i18n 'acc_door_relay'/>:</td>
							<td>{obj.relay}</td>
						</tr>
						<tr>
							<td><@i18n 'common_alarm'/>:</td>
							<td>{obj.alarm}</td>
						</tr>
					</table>
					<div style="display: {obj.opDisplay}" onclick="showFlag=false;accRTMonitorHidePopUpInfo()">
						<ul class="poPupInfo_op_ul">
							<@ZKUI.Permission name="acc:rtMonitor:openDoor">
								<li onclick="operateDoor('/accRTMonitor.do?openDoor', '{obj.id}', '${tabUUID!}')" title="<@i18n 'acc_eventNo_8'/>"><@i18n 'acc_eventNo_8'/></li>
							</@ZKUI.Permission>
							<@ZKUI.Permission name="acc:rtMonitor:closeDoor" text="${acc_rtMonitor_closeDoor}">
								<li onclick="operateDoor('/accRTMonitor.do?closeDoor', '{obj.id}', '${tabUUID!}')" title="<@i18n 'acc_eventNo_9'/>"><@i18n 'acc_eventNo_9'/></li>
							</@ZKUI.Permission>
							<@ZKUI.Permission name="acc:rtMonitor:lockDoor" text="${acc_newEventNo_233}">
								<li style="display: {obj.lockDisplay}" onclick="operateDoor('/accRTMonitor.do?lockDoor', '{obj.id}', '${tabUUID!}')" title="<@i18n 'acc_newEventNo_233'/>"><@i18n 'acc_newEventNo_233'/></li>
							</@ZKUI.Permission>
							<@ZKUI.Permission name="acc:rtMonitor:unLockDoor" text="${acc_newEventNo_234}">
								<li style="display: {obj.lockDisplay}" onclick="operateDoor('/accRTMonitor.do?unLockDoor', '{obj.id}', '${tabUUID!}')" title="<@i18n 'acc_newEventNo_234'/>"><@i18n 'acc_newEventNo_234'/></li>
							</@ZKUI.Permission>
							<@ZKUI.Permission name="acc:rtMonitor:cancelAlarm" text="${acc_eventNo_7}">
								<li onclick="operateDoor('/accRTMonitor.do?cancelAlarm', '{obj.id}', '${tabUUID!}')" title="<@i18n 'acc_eventNo_7'/>"><@i18n 'acc_eventNo_7'/></li>
							</@ZKUI.Permission>
							<@ZKUI.Permission name="acc:rtMonitor:normalOpenDoor" text="${acc_rtMonitor_remoteNormalOpen}">
								<li onclick="operateDoor('/accRTMonitor.do?normalOpenDoor', '{obj.id}', '${tabUUID!}')" title="<@i18n 'acc_rtMonitor_remoteNormalOpen'/>"><@i18n 'acc_rtMonitor_remoteNormalOpen'/></li>
							</@ZKUI.Permission>
							<@ZKUI.Permission name="acc:rtMonitor:enableNormalOpenDoor" text="${common_rtMonitor_enableIntradayTZ}">
							<li onclick="operateDoor('/accRTMonitor.do?enableNormalOpenDoor', '{obj.id}', '${tabUUID!}')" title="<@i18n 'common_rtMonitor_enableIntradayTZ'/>"><@i18n 'common_rtMonitor_enableIntradayTZ'/></li>
							</@ZKUI.Permission>
							<@ZKUI.Permission name="acc:rtMonitor:disableNormalOpenDoor" text="${common_rtMonitor_disableIntradayTZ}">
								<li onclick="operateDoor('/accRTMonitor.do?disableNormalOpenDoor', '{obj.id}', '${tabUUID!}')" title="<@i18n 'common_rtMonitor_disableIntradayTZ'/>"><@i18n 'common_rtMonitor_disableIntradayTZ'/></li>
							</@ZKUI.Permission>
						</ul>
					</div>
					<div>
						<@ZKUI.Permission name="acc:rtMonitor:transactionTodayHappen" text="${acc_doorEventLatestHappen}">
							<ul class="poPupInfo_op_ul" style="border-top: none!important">
                                   <!--<li onclick="DhxCommon.createWindow('/accTransaction.do?getTransactionsTodayLast&eventPointType=0&eventPointId={obj.id}^0^0^1000^300^<@i18n 'acc_doorEventLatestHappen'/>');$('body').children('div.dataViewOpItem:first').remove();" title="<@i18n 'acc_doorEventLatestHappen'/>"><@i18n 'acc_doorEventLatestHappen'/></li>-->
                                 <li onclick="DhxCommon.createWindow('/skip.do?page=acc_rtMonitor_accTransactionsTodayLast&eventPointType=0&eventPointId={obj.id}^0^0^1000^300^<@i18n 'acc_doorEventLatestHappen'/>');$('body').children('div.dataViewOpItem:first').remove();" title="<@i18n 'acc_doorEventLatestHappen'/>"><@i18n 'acc_doorEventLatestHappen'/></li>
                           	</ul>
	                     </@ZKUI.Permission>
						<@ZKUI.Permission name="acc:rtMonitor:liveView">
							<ul class="poPupInfo_op_ul acc_channel_preview_display_{obj.channelIds}" style="border-top: none!important">
								<li onclick="accRTMonitorChannelPreview('{obj.channelIds}')"><@i18n 'system_module_videoPreview'/></li>
							</ul>
						</@ZKUI.Permission>
					</div>
				</div>
			</div>
		</div>
		<p class="dhx_item_text" style="word-wrap:break-word; word-break:normal;" title="{obj.name}">{obj.name}</p>
	</div>
</textarea>
<@ZKUI.Toolbar id="rtmTooldoor${tabUUID!}" uuid="${tabUUID!}">
	<@ZKUI.ToolItem id="/accRTMonitor.do?openDoor" img="comm_remoteopendoor.png" text="acc_eventNo_8" permission="acc:rtMonitor:openDoor" action="_operateDoor"/>
	<@ZKUI.ToolItem id="/accRTMonitor.do?closeDoor" img="comm_remoteclosedoor.png" text="acc_eventNo_9" permission="acc:rtMonitor:closeDoor" action="_operateDoor"/>
	<@ZKUI.ToolItem id="/accRTMonitor.do?cancelAlarm" img="acc_cancelAlarm.png" text="acc_eventNo_7" permission="acc:rtMonitor:cancelAlarm" action="_operateDoor"/>
	<@ZKUI.ToolItem id="/accRTMonitor.do?lockDoor" img="acc_remote_lock.png" text="acc_newEventNo_233" permission="acc:rtMonitor:lockDoor" action="_operateDoor"/>
	<@ZKUI.ToolItem id="/accRTMonitor.do?unLockDoor" img="acc_remote_unlock.png" text="acc_newEventNo_234" permission="acc:rtMonitor:unLockDoor" action="_operateDoor"/>
	<@ZKUI.ToolItem id="/accRTMonitor.do?lcdRTMonitor" text="acc_leftMenu_LCDRTMonitor" img="acc_lcdmonitor.png" action="accLCDRTMonitor" permission="acc:rtMonitor:lcdRTMonitor" isShow="JAVA#accRTMonitorController.isShowlcdRTMonitor"></@ZKUI.ToolItem>
	<@ZKUI.ToolItem type="more">
		<@ZKUI.ToolItem id="/accRTMonitor.do?normalOpenDoor" img="comm_remoteNormalOpen.png" text="acc_rtMonitor_remoteNormalOpen" permission="acc:rtMonitor:normalOpenDoor" action="_operateDoor"/>
		<@ZKUI.ToolItem id="/accRTMonitor.do?enableNormalOpenDoor" img="comm_enableDSTime.png" text="common_rtMonitor_enableIntradayTZ" permission="acc:rtMonitor:enableNormalOpenDoor" action="_operateDoor"/>
		<@ZKUI.ToolItem id="/accRTMonitor.do?disableNormalOpenDoor" img="comm_disableDSTime.png" text="common_rtMonitor_disableIntradayTZ" permission="acc:rtMonitor:disableNormalOpenDoor" action="_operateDoor"/>
	</@ZKUI.ToolItem>
</@ZKUI.Toolbar>
<div id="doorDV${tabUUID!}" class="acc_rtm_dataview" onclick="clearDoorSelect('doorDV${tabUUID!}')"></div>
<div class="acc_rtm_grid_foot">
	<div style="float: ${leftRTL!'left'};margin-${leftRTL!'left'}: 5px;">
		<@i18n 'common_currentTotal'/>:<span id="doorCount${tabUUID!}">0</span>
	</div>
	<div class="divBorder" style="margin-${leftRTL!'left'}: 5%;">
		<span class="display_line_block">
			<span class="icv-greenPilot"></span>
			<@i18n 'common_online'/>:<em id="doorOnlineCount${tabUUID!}">0</em>&nbsp; 
		</span>
		<span class="display_line_block">
			<span class="icv-yellowPilot"></span>
			<@i18n 'common_disable'/>:<em id="doorDisbleCount${tabUUID!}">0</em>&nbsp;
		</span>
		<span class="display_line_block">
			<span class="icv-redPilot"></span>
			<@i18n 'common_offline'/>:<em id="doorOfflineCount${tabUUID!}">0</em>&nbsp;
		</span>
		<span class="display_line_block">
			<span class="icv-unknownPilot"></span>
			<@i18n 'common_unknown'/>:<em id="doorUnknownCount${tabUUID!}">0</em>
		</span>
    </div>
    <div class="divBorder" style="margin-${leftRTL!'left'}: 5%;">
		<@i18n 'acc_door_name'/>&nbsp;<input type="text" id="doorFilter${tabUUID!}" name="name" style="height: 16px;line-height: 16px;margin-top: -2px" onchange="dataRTFilter()" onkeyup="dataRTFilter()"/>
    </div>
</div>
<script type="text/javascript">
(function() {
    if(window.accRTM==undefined||window.accRTM==null){
        window.accRTM = {};
	}
    window.accRTM.doorId="${tabUUID!}";
	var doorView;
	if(Web) {
		Web.callEvent("onInitDataView", ["door", {container: "doorDV${tabUUID!}"},"/accRTMonitor.do?getAllDoor&u=${uuid!}", function(d) {
			doorView = d;
			<@ZKUI.Permission name="acc:door:edit">
			doorView.attachEvent("onItemDblClick", function(id){
		    	if(id.indexOf("CustomObject") == -1)
		    	{
		    		var opts = {
		    				path:"/accDoor.do?edit&id="+ id,
		    				width:910,
		    				height:570,
		    				title:"<@i18n 'common_op_edit'/>"
		    		}
		    		DhxCommon.createWindow(opts);
		    	}
			});
		    </@ZKUI.Permission>
		}]);
	}
})();

//打开LCD监控页面    add by chunting.wu
function accLCDRTMonitor(id, bar, opt) {
	window.open("/accRTMonitor.do?openLcdRTMonitor");
}

</script>
