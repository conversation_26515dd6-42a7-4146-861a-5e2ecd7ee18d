<#assign leftRTL = "left">
<#assign rightRTL = "right">
<#if enableRTL?? && enableRTL=="true">
<!-- 定义全局对齐方式变量(针对阿拉伯页面) -->
<#assign leftRTL = "right">
<#assign rightRTL = "left">
</#if>
<!-- 辅助输入DataView模板--begin -->
<textarea id="auxInTemplateContainer${tabUUID}" class="displayN">
	<div align="center">
		<div onmouseover="showPopUpInfo(this);" onmouseout="accRTMonitorHidePopUpInfo(this);">
			<div onmousedown="return false;" class="acc_rt_auxImg aux_in_{obj.image}" ></div>
<!-- 			<img onmousedown="return false;" border="0" src="${base}/images/auxInState/icon/aux_in_{obj.image}.png" > -->
			<div class="displayN dataViewOpItem poPupInfo" style="position: fixed;top: 263px;${leftRTL!'left'}: 325px;z-index: 9999999;" align="${leftRTL!'left'}">
				<div class="poPupInfo">
					<p class="poPupInfo_title_p"><@i18n 'common_status'/></p>
					<table class="poPupInfo_text_table">
						<tr>
							<td><@i18n 'common_dev_entity'/>:</td>
							<td>{obj.devAlias}</td>
						</tr>
						<tr>
							<td><@i18n 'common_number'/>:</td>
							<td>{obj.no}</td>
						</tr>
					</table>
				</div>
			</div>
		</div>
		<p style="word-wrap:break-word; word-break:normal;" title="{obj.name}">{obj.name}</p>
	</div>
</textarea>
<@ZKUI.Toolbar id="rtmToolauxIn${tabUUID!}" uuid="${tabUUID!}">
	<@ZKUI.ToolItem type="text" text=""/>
</@ZKUI.Toolbar>
<div id="auxInDV${tabUUID!}" class="acc_rtm_dataview"></div>
<div class="acc_rtm_grid_foot">
	<div style="float: ${leftRTL!'left'};margin-${leftRTL!'left'}: 5px;">
		<@i18n 'common_currentTotal'/>:<span id="auxInCount${tabUUID!}">0</span>
	</div>
	<div class="divBorder" style="margin-${leftRTL!'left'}: 5%;">
		<span style="display:inline-block">
			<span class="icv-greenPilot"></span>
			<@i18n 'common_online'/>:<em id="auxInOnlineCount${tabUUID!}">0</em>&nbsp; 
		</span>
		<span style="display:inline-block">
			<span class="icv-yellowPilot"></span>
			<@i18n 'common_disable'/>:<em id="auxInDisbleCount${tabUUID!}">0</em>&nbsp;
		</span>
		<span style="display:inline-block">
			<span class="icv-redPilot"></span>
			<@i18n 'common_offline'/>:<em id="auxInOfflineCount${tabUUID!}">0</em>&nbsp;
		</span>
		<span style="display:inline-block">
			<span class="icv-unknownPilot"></span>
			<@i18n 'common_unknown'/>:<em id="auxInUnknownCount${tabUUID!}">0</em>
		</span>
    </div>
    <div class="divBorder" style="margin-${leftRTL!'left'}: 5%;">
		<@i18n 'common_rtMonitor_auxInName'/>&nbsp;<input type="text" id="auxInFilter${tabUUID!}" name="name" style="height: 16px;line-height: 16px;margin-top: -2px" onkeyup="dataRTFilter()"/>
    </div>
</div>
<script type="text/javascript">
(function() {
	var auxInView;
	if(Web) {
		setTimeout(function(){
			Web.callEvent("onInitDataView", ["auxIn", {type: {template: "html->auxInTemplateContainer${tabUUID!}"}, container: "auxInDV${tabUUID!}"},"/accRTMonitor.do?getAllAuxIn&u=${uuid!}", function(d) {
				auxInView = d;
			}]);
		}, 100)
	}
})();
</script>