<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	  lang="${(currentLanguage == 'zh_CN')?string('zh',currentLanguage)}"
	  xml:lang="${(currentLanguage == 'zh_CN')?string('zh',currentLanguage)}">
<#assign leftRTL = "left">
<#assign rightRTL = "right">
<#if enableRTL?? && enableRTL=="true">
<!-- 定义全局对齐方式变量(针对阿拉伯页面) -->
<#assign leftRTL = "right">
<#assign rightRTL = "left">
</#if>
<head>
	<title>${sysName}</title>
	<script charset="utf-8"  type="text/javascript" src="/js/accWebSocket.js"></script>
	<script charset="utf-8" type="text/javascript" src="/js/showTime.js"></script>
	<link rel="stylesheet" href="/css/accLCDRTMonitor.css" type="text/css"></link>
	<#if enableRTL?? && enableRTL=="true">
	<link rel="stylesheet" href="/css/accLCDRTMonitorRTL.css" type="text/css"></link>
</#if>
<meta http-equiv="content-type" content="text/html; charset=UTF-8"></meta>
</head>
<body style="margin: 0 0 0 0; background: #F3F5F0;height: 100%;position: relative;top:0px;bottom: 0px;">
<div>
	<!-- start -->
	<div class="acc_lcdDhxcelltop_hdr" style="line-height: 64px;height: 64px; width: 100%; background: #7AC143;">
		<div style="width: 15%; float: left;position: absolute; text-align:center">
			<img src="/public/images/head/logo.png" border="0" style="margin: 0 auto;">
		</div>
		<div style="width: 100%; float: left; text-align: center">
			<b style="color: #FFFFFF; font-size: 200%; font-family: Arial, 微软雅黑;"><@i18n 'acc_leftMenu_LCDRTMonitor'/></b>
		</div>
	</div>
	<!-- 信息展示部分 -->
	<div class="titleDiv_lcd">
		<div class="title_lcd"><@i18n 'acc_LCDRTMonitor_current'/></div>
	</div>
	<!-- 当前通行人员-->
	<div class="currentInfo_lcd">
		<div id="visCurrentInfo_lcd"></div>
		<div class="allDateInfo_lcd">
			<div id="timeInfo_lcd" style="font-size: 8vw; margin-${rightRTL!'right'}: 60px; margin-top: 30px; font-family: Arial, 微软雅黑;"></div>
			<div class="dateInfo_lcd">
				<div id="weekInfo_lcd"></div>
				<div id="dateInfo_lcd"></div>
			</div>
		</div>
	</div>
	<div class="titleDiv_lcd">
		<div class="title_lcd"><@i18n 'acc_LCDRTMonitor_previous'/></div>
	</div>
	<div id="personInfoAll_lcd">
		<div id="visInfoAll_lcd"></div>
	</div>
</div>
</body>

<script type="text/javascript">
	/* 天气显示 */
    var count = 0;
    function showWeather()
    {
	    var nowTime = showTime();
	    var html = "<div style='font-size:500%;font-family:Arial,微软雅黑;''>" + nowTime + "</div>";
	    $('#weather_lcd').html(html);
	    $('#weather_lcd').attr("style", "float:${leftRTL!'left'};width: 40%;margin-top:5%;margin-${rightRTL!'right'}:2%");
    }
    showWeather();
    /* 定时触发 1分钟触发更新天气与时间*/
    window.setInterval("showWeather()", 60 * 1000);
    /* 默认加载页面信息 */
    var clientId = Math.round(Math.random() * 10000)+"${uuid!}";
    (function() {
    	initPersInfo();
    	function initPersInfo() {
    		 $.ajax({
                 url:"accRTMonitor.do?getPersonInfo&areaNames="+"${(areaNames)!}",
                 type:"GET",
                 async:false,
                 dataType:"json",
                 data:{
                 },
                 success:function (result) {
                	 var personInfo = result.data;
                     if (personInfo != "" && personInfo != null && typeof (personInfo) != "undefined")
                     {
						 for ( var personnel in personInfo)
                         {
							 var backName = "";
							 if(typeof(personInfo[personnel].personName) != "undefined")
							 {
								 backName = personInfo[personnel].personName;
							 }
							 var backDept = "";
							 if(typeof(personInfo[personnel].PersDepartment) != "undefined")
							 {
								 backDept = personInfo[personnel].PersDepartment;
							 }
							 var backPin = personInfo[personnel].pin;
							 var backPhotoPath = personInfo[personnel].photoPath;
							 if (typeof (backPhotoPath) == 'undefined' || backPhotoPath == null || backPhotoPath == "") {
								 backPhotoPath = "/images/userImage.gif";
							 }
							 var persPhoto = "<img src='" + backPhotoPath + "' onerror=\"this.src='/images/userImage.gif'\" style='border: 1px solid #CFE3FC;height:140px;max-width:120px'/>";//定义照片div
							 var persInfo = "<div class='lastInfoDiv_lcd'>" + "<p class='lastInfo_lcd'><b>" + backPin + "</b></p>" + "<p class='lastInfo_lcd'><b>" + backName + "</b></p>" + "<p class='lastInfo_lcd'><b>" + backDept + "</b></p>" + "</div>"
							 var textPers = "<div class='" + backPin + "_lcd'" + "id=" + "'" + backPin + "_lcd'" + " style='width: 19%; height: 24.5%; border: 1px; display: inline-block; margin-${leftRTL!'left'}:1%; text-align:center; vertical-align: top;'>" + "<div class='persPhoto_lcd'>" + persPhoto + "</div>" + "<div class='persInfo_lcd'>" + persInfo + "</div>" + "</div>";
							 $("#visInfoAll_lcd").append(textPers); /* //append()尾部插  */
						 }
                     }
                 }
             });

    	}
		function initLcdEventSocket() {
			var client = Web.getSocket({
				id : "persInfoClient",
				url : "accLcdRTMonitor/getLcdEvent",
				param:JSON.stringify({"clientId" : clientId}),
				onMessage:function(resp) {
					var data = JSON.parse(resp.body);
					if(data.clientId && clientId!=data.clientId) {
						return;
					}
					var html = "";
				    var rows = data;
				    var localhost = window.window.location.host;
					var areaName = rows.areaName;
					var areaNames = "${(areaNames)!}";
					if(typeof(areaNames) == "undefined" || areaNames == "" || areaNames.indexOf(areaName) < 0)
					{
						return;
					}
				    var personName = rows.personName;
				    var eventPointName = rows.eventPointName;
				    var eventTime = rows.eventTime;
				    var readerState = rows.readerState;
				    var personType = rows.personType;
				    var photoPath = rows.photoPath;
				    if (typeof (photoPath) == 'undefined' || photoPath == null || photoPath == "") {
						photoPath = "/images/userImage.gif";
					}
				    var pin = rows.pin;
				    var eventDescription = I18n.getValue(rows.eventName);
				    var department = rows.PersDepartment;
				    var cardNo = rows.cardNo;

		     	    if (readerState == 0 || readerState == 1)
				    {
					    //人员信息显示
					    var visPhotoWid = window.screen.width * 0.16 + "px"; //0.09636   0.42778
					    var visPhotoHei = window.screen.height * 0.32 + "px";//width='130px' height='153px'
					    var PhotoWid = window.screen.width * 0.098 + "px"; //0.09636   0.42778
					    var PhotoHei = window.screen.height * 0.160 + "px";//width='130px' height='153px'
					    var visPersPhoto = "<img id='photo_lcd' src='" + photoPath + "' onerror=\"this.src='/images/userImage.gif'\" width='" + visPhotoWid + "' height='" + visPhotoHei + "' style='border: 1px solid #CFE3FC;'/>";//定义照片div

					    var visInfo = "<div>" + "<table>" + "<tr>" + "<td style='vertical-align: top;'><p class='visCurrentTitlePFull_lcd'><b>"+"<@i18n 'common_time'/>"+"</b></p></td>" + "<td class='visCurrentText_lcd'><b>" + eventTime
					    	+ "</b></td>" + "</tr>" + "<tr>" + "<td style='vertical-align: top;'><p class='visCurrentTitlePFull_lcd'><b>"+"<@i18n 'common_eventDescription'/>"+"</b></p></td>" + "<td class='visCurrentText_lcd' style='margin-left: 50px;'><b>" + eventDescription + "</b></td>" + "</tr>" + "<tr>" + "<td style='vertical-align: top;'><p class='visCurrentTitlePFull_lcd'><b>"+"<@i18n 'common_eventPoint'/>"+"</b></p></td>" + "<td class='visCurrentText_lcd'><b>"
					    		+ eventPointName + "</b></td>" + "</tr>" + "<tr>" + "<td style='vertical-align: top;'><p class='visCurrentTitlePFull_lcd'><b>"+"<@i18n 'pers_person_pin'/>"+"</b></p></td>" + "<td class='visCurrentText_lcd' id='personID_lcd'><b>" + pin + "</b></td>" + "</tr>" + "<tr>" + "<td style='vertical-align: top;'><p class='visCurrentTitlePFull_lcd'><b>"+"<@i18n 'pers_person_wholeName'/>"+"</b></p></td>" + "<td class='visCurrentText_lcd' id='personName_lcd'><b>" + personName
					    			+ "</b></td>" + "</tr>" + "<tr>" + "<td style='vertical-align: top;'><p class='visCurrentTitlePFull_lcd'><b>"+"<@i18n 'pers_dept_entity'/>"+"</b></p></td>" + "<td class='visCurrentText_lcd' id='personDepartment_lcd'><b>" + department + "</b></td>" + "</tr>" + "<tr>" + "<td style='vertical-align: top;'><p class='visCurrentTitlePFull_lcd'><b>"+"<@i18n 'pers_card_cardNo'/>"+"</b></p></td>" + "<td class='visCurrentText_lcd'><b>" + cardNo + "<b></td>" + "</tr>" + "</table>" + "</div>";

					    var textVis = "<div id='person_info_" + 'vis_lcd' + "' style='width: 100%; height: 90%; margin-${leftRTL!'left'}:1%'>" + "<div class='visPersPhotoFull_lcd'>" + visPersPhoto + "</div>" + "<div class='visInfoFull_lcd'>" + visInfo + "</div>" + "</div>";
					    var textVisAll = "<div id='" + pin + "_lcd' style='width: 100%; height: 100%; margin-top: 10px;'>" + textVis + "</div>";

					    //获取上一次刷卡的人员信息
					    var lastPersonID = $("#personID_lcd").text();
					    var lastPersonName = $("#personName_lcd").text().trim();
					    var lastPersonDepartment = $("#personDepartment_lcd").text();
					    var lastPersonPhoto = $("#photo_lcd").attr("src");

					    var persPhoto = "<img src='" + lastPersonPhoto + "' onerror=\"this.src='/images/userImage.gif'\" style='border: 1px solid #CFE3FC;height:140px;max-width:120px'/>";//定义照片div
					    $("#visCurrentInfo_lcd").html(textVisAll);

					    //历史通行人员
					    if (lastPersonID != "")
					    {
						    var persInfo = "<div class='lastInfoDiv_lcd'>" + "<p class='lastInfo_lcd'><b>" + lastPersonID + "</b></p>" + "<p class='lastInfo_lcd'><b>" + lastPersonName + "</b></p>" + "<p class='lastInfo_lcd'><b>" + lastPersonDepartment + "</b></p>" + "</div>"
						    var textPers = "<div class='" + lastPersonID + "_lcd'" + "id=" + "'" + lastPersonID + "_lcd'" + " style='width: 19%; height: 24.5%; border: 1px ; display: inline-block;margin-${leftRTL!'left'}:1%;text-align:center; vertical-align: top;'>" + "<div class='persPhoto_lcd'>" + persPhoto + "</div>" + "<div class='persInfo_lcd'>" + persInfo + "</div>" + "</div>";
						    var exist = $('div').hasClass('' + lastPersonID);

						    $("#visInfoAll_lcd").prepend(textPers);
						    if ($("#visInfoAll_lcd").children().length > 5)
						    {
							    $("#visInfoAll_lcd> div").last().remove();
						    }
					    }
				    }
				}
			});
			return client;
		}
		var client = initLcdEventSocket();
		//设置定时器，10分钟发送请求，防止30分钟内没有任何操作导致页面出现登录超时的问题；
		setInterval(function() {
			client.send(new Date())
		}, 600000);
	})();
</script>
</html>