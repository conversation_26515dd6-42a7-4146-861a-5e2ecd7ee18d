<!-- 梯控DataView模板--begin -->
<textarea id="elevatorTemplateContainer${tabUUID!}" class="displayN">
    <div align="center">
        <div onmouseover="showPopUpInfo(this);" onmouseout="accRTMonitorHidePopUpInfo(this);">
            <div class="displayN dataViewOpItem poPupInfo" style="position: fixed;top: 263px;${leftRTL!'left'}: 325px;z-index: 9999999;" align="${leftRTL!'left'}">
                <div style="display: {obj.opDisplay};">
                    <ul class="poPupInfo_op_ul">
                        <@ZKUI.Permission name="acc:rtMonitor:openFloor" text="acc_rtm_open">
                            <a style="margin-${leftRTL!'left'}: 10px;" href="javascript:DhxCommon.createWindow('/eleRTMonitor.do?remoteOperate&opType=open^0^0^450^450^<@i18n 'acc_rtm_open'/>')"><@i18n 'acc_rtm_open'/></a>
                        </@ZKUI.Permission>
                        <@ZKUI.Permission name="acc:rtMonitor:closeFloor" text="acc_rtm_close">
                            <a style="margin-${leftRTL!'left'}: 10px;" href="javascript:DhxCommon.createWindow('/eleRTMonitor.do?remoteOperate&opType=close^0^0^450^450^<@i18n 'acc_rtm_close'/>')"><@i18n 'acc_rtm_close'/></a>
                        </@ZKUI.Permission>
                    </ul>
                </div>
            </div>
        </div>
        <p class="dhx_item_text" style="word-wrap:break-word; word-break:normal;" title="{obj.name}">{obj.name}</p>
    </div>
</textarea>
<@ZKUI.Toolbar id="rtmToolelevator${tabUUID!}" uuid="${tabUUID!}">
	<@ZKUI.ToolItem type="text" text="common_currentAll"/>
	<@ZKUI.ToolItem permission="acc:rtMonitor:openFloor" id="eleRTMonitor.do?remoteOperate&opType=open^0^0^450^450" title="acc_rtm_open" img="comm_enable.png" text="acc_rtm_open" action="operateElevator"/>
    <@ZKUI.ToolItem permission="acc:rtMonitor:closeFloor" id="eleRTMonitor.do?remoteOperate&opType=close^0^0^450^450" title="acc_rtm_close" img="comm_disable.png" text="acc_rtm_close" action="operateElevator"/>
</@ZKUI.Toolbar>
<div id="elevatorDV${tabUUID!}" class="acc_rtm_dataview"></div>
<div class="acc_rtm_grid_foot"></div>
<script type="text/javascript">
$(function() {
	var elevatorView;
	if(Web) {
		setTimeout(function(){
			Web.callEvent("onInitDataView", ["elevator", {type: {template: "html->elevatorTemplateContainer${tabUUID!}"}, container: "elevatorDV${tabUUID!}"},"", function(d) {
				elevatorView = d;
			}]);
		}, 100)
	}
});
</script>