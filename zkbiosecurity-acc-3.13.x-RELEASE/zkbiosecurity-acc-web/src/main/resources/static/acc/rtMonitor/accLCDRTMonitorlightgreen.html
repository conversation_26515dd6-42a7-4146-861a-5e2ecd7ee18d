<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      lang="${(currentLanguage == 'zh_CN')?string('zh',currentLanguage)}"
      xml:lang="${(currentLanguage == 'zh_CN')?string('zh',currentLanguage)}">
<#assign leftRTL = "left">
<#assign rightRTL = "right">
<#if enableRTL?? && enableRTL=="true">
<!-- 定义全局对齐方式变量(针对阿拉伯页面) -->
<#assign leftRTL = "right">
<#assign rightRTL = "left">
</#if>
<head>
    <title>${sysName}</title>
    <script charset="utf-8"  type="text/javascript" src="/js/accWebSocket.js"></script>
    <script charset="utf-8" type="text/javascript" src="/js/showTime.js"></script>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"></meta>
</head>
<body class="accLcdRTBody">
<div class="accLcdRTBody-head">
    <div class="acc_lcdDhxcelltop_hdr">
        <div class="accLcdRTHeadLogo">
            <img src="${Application['system.headLogo']!''}">
        </div>
        <div class="accLcdRTHeadTitle">
            <b class="accLcdRTHeadTitleContent"><@i18n 'acc_leftMenu_LCDRTMonitor'/></b>
        </div>
    </div>
</div>
<!-- 信息展示部分 -->
<div class="accLcdRTBody-rt-info">
    <div class="accLcdRTRow">
        <div class="title_lcd"><@i18n 'acc_LCDRTMonitor_current'/></div>
        <div class="currentInfo_lcd">
            <div id="visCurrentInfo_lcd">
            </div>
            <div class="allDateInfo_lcd">
                <div class="dateInfo_lcd"><div id="weekInfo_lcd"></div><div id="dateInfo_lcd"></div></div>
                <div id="timeInfo_lcd" class="accLcdRTTimeInfo"></div>
            </div>
        </div>
    </div>
</div>
<div class="accLcdRTBody-info">
    <div class="accLcdRTRow">
        <div class="title_lcd"><@i18n 'acc_LCDRTMonitor_previous'/></div>
        <div id="personInfoAll_lcd" class="accLcdRTInfoAll">
            <div id="visInfoAll_lcd" class="lcdPersonInfoItems">
            </div>
        </div>
    </div>
</div>
</body>

<script type="text/javascript">
	/* 天气显示 */
    var count = 0;
    var systemSkin = getSystemSkinAcc();
    systemSkin = systemSkin== "default" ? "": systemSkin;
    var errorSrc = "images/"+systemSkin+"/userImage.gif";
    var defaultSrc = "images/"+systemSkin+"/userImage.gif";
    function showWeather()
    {
	    var nowTime = showTime();
	    var html = "<div style='font-size:500%;font-family:Arial,微软雅黑;''>" + nowTime + "</div>"
	    $('#weather_lcd').html(html);
	    $('#weather_lcd').attr("style", "float:${leftRTL!'left'};width: 40%;margin-top:5%;margin-${rightRTL!'right'}:2%");
    }
    showWeather();
    /* 定时触发 1分钟触发更新天气与时间*/
    window.setInterval("showWeather()", 60 * 1000);
    /* 默认加载页面信息 */
    var clientId = Math.round(Math.random() * 10000)+"${uuid!}";
    (function() {
    	initPersInfo();
    	accLcdNoDataMessage("visCurrentInfo_lcd");
    	function initPersInfo() {
    		 $.ajax({
                 url:"accRTMonitor.do?getPersonInfo",
                 type:"GET",
                 async:false,
                 dataType:"json",
                 data:{
                    areaNames: "${(areaNames)!}"
                 },
                 success:function (result) {
                	 var personInfo = result.data;
                     if (personInfo != "" && personInfo != null && typeof (personInfo) != "undefined")
                     {
						 for ( var personnel in personInfo)
                         {
							 var backName = "";
							 if(typeof(personInfo[personnel].personName) != "undefined")
							 {
								 backName = personInfo[personnel].personName;
							 }
							 var backDept = "";
							 if(typeof(personInfo[personnel].PersDepartment) != "undefined")
							 {
								 backDept = personInfo[personnel].PersDepartment;
							 }
							 var backPin = personInfo[personnel].pin;
							 var backPhotoPath = personInfo[personnel].photoPath;
							 if (typeof (backPhotoPath) == 'undefined' || backPhotoPath == null || backPhotoPath == "") {
								 backPhotoPath = defaultSrc;
							 }

							 var persInfo = "<div class='lcdPersonInfoItem' id='lcdPersonInfoItemId'>\n"+
							 				"	 <div class='lcdPersImg'>\n"+
                            				"	 	<div class='lcdImgContent'><img class='lcdPersPhoto' src='" + backPhotoPath + "' onerror=\"this.src='"+errorSrc+"'\"/></div>\n"+
                        					"	 </div>\n"+
											"	 <div class='lcdLastPersonInfo'>\n"+
											"	 	<div class='lcdLastPersonInfo'>"+ backPin + "</div>\n"+
											"	 	<div class='lcdLastPersonInfo'>"+ backName + "</div>\n"+
											"	 	<div class='lcdLastPersonInfo'>"+ backDept + "</div>\n"+
											"	 </div>\n"+
											"</div>";
							 $("#visInfoAll_lcd").append(persInfo);
						 }
                     }
                     else {
                        accLcdNoDataMessage("visInfoAll_lcd");
                     }
                 }
             });

    	}
		function initLcdEventSocket() {
			var client = Web.getSocket({
				id : "persInfoClient",
				url : "accLcdRTMonitor/getLcdEvent",
				param:JSON.stringify({"clientId" : clientId}),
				onMessage:function(resp) {

					var data = JSON.parse(resp.body);
					if(data.clientId && clientId!=data.clientId) {
						return;
					}
					var html = "";
				    var rows = data;
				    var localhost = window.window.location.host;
					var areaName = rows.areaName;
					var areaNames = "${(areaNames)!}";
					if(typeof(areaNames) == "undefined" || areaNames == "" || areaNames.indexOf(areaName) < 0)
					{
						return;
					}
				    var personName = rows.personName;
				    var eventPointName = rows.eventPointName;
				    var eventTime = rows.eventTime;
				    var readerState = rows.readerState;
				    var personType = rows.personType;
				    var photoPath = rows.photoPath;
				    if (typeof (photoPath) == 'undefined' || photoPath == null || photoPath == "") {
						photoPath = defaultSrc;
					}
				    var pin = rows.pin;
				    var eventDescription = I18n.getValue(rows.eventName);
				    var department = rows.PersDepartment;
				    var cardNo = rows.cardNo;
				    <#if hasPermission('acc:pin:encryptProp','true')>
                        pin = convertToEncrypt(pin,"${pinEncryptMode!S2}");
                    </#if>
                    <#if hasPermission('acc:name:encryptProp','true')>
                        personName = convertToEncrypt(personName,"${nameEncryptMode!S1}");
                    </#if>
                    <#if hasPermission('acc:cardNo:encryptProp','true')>
                        cardNo = convertToEncrypt(cardNo,"${cardEncryptMode!S2}");
                    </#if>
                    var photoEncrypt = "";
                    <#if hasPermission('acc:headPortrait:encryptProp','true')>
                        photoEncrypt = "filter: blur(5px);"
                    </#if>
		     	    if (readerState == 0 || readerState == 1)
				    {
					    //人员信息显示
					    if ($("#lcdCurrentInfoContent").size() == 0){
					        $("#accLcdCurrentNoDataDiv").remove();
					        $("#visCurrentInfo_lcd").append("<div class='content' id='lcdCurrentInfoContent'></div>");
					    }
					    var currentInfo="<div class='lcdCurrentImage'>\n"+
                        		"	<img id='photo_lcd' class='lcdPersPhotoEx' src='" + photoPath + "' style='" + photoEncrypt + "' onerror=\"this.src='"+errorSrc + "'\">\n"+
								"</div>\n"+
								"<div class='lcdCurrentPersInfo'>\n"+
								"	<table class='lcdCurrentTable'>\n"+
								"		<tr>\n"+
								"			<th><@i18n 'common_time'/></th>\n"+
								"			<td>" + eventTime + "</td>\n"+
								"		</tr>\n"+
								"		<tr>\n"+
								"			<th><@i18n 'common_eventDescription'/></th>\n"+
								"			<td>" + eventDescription + "</td>\n"+
								"		</tr>\n"+
								"		<tr>\n"+
								"			<th><@i18n 'common_eventPoint'/></th>\n"+
								"			<td>"+ eventPointName +"</td>\n"+
								"		</tr>\n"+
								"		<tr>\n"+
								"			<th><@i18n 'pers_person_pin'/></th>\n"+
								"			<td id='personID_lcd'>"+ pin +"</td>\n"+
								"		</tr>\n"+
								"		<tr>\n"+
								"			<th><@i18n 'pers_person_wholeName'/></th>\n"+
								"			<td id='personName_lcd'>"+ personName +"</td>\n"+
								"		</tr>\n"+
								"		<tr>\n"+
								"			<th><@i18n 'pers_dept_entity'/></th>\n"+
								"			<td id='personDepartment_lcd'>"+ department +"</td>\n"+
								"		</tr>\n"+
								"		<tr>\n"+
								"			<th><@i18n 'pers_card_cardNo'/></th>\n"+
								"			<td>"+ cardNo +"</td>\n"+
								"		</tr>\n"+
								"	</table>\n"+
								"</div>";
							$("#lcdCurrentInfoContent").html(currentInfo);

					    //获取上一次刷卡的人员信息
					    var lastPersonID = $("#personID_lcd").text();
					    var lastPersonName = $("#personName_lcd").text().trim();
					    var lastPersonDepartment = $("#personDepartment_lcd").text();
					    var lastPersonPhoto = $("#photo_lcd").attr("src");

					    //历史通行人员
					    if (lastPersonID != "")
					    {
					        $("#accLcdNoDataDiv").remove();
						    var persInfo = "<div class='lcdPersonInfoItem' id='lcdPersonInfoItemId'>\n"+
							 				"	 <div class='lcdPersImg'>\n"+
                            				"	 	<div class='lcdImgContent'><img class='lcdPersPhoto' src='" + lastPersonPhoto + "' style='" + photoEncrypt + "' onerror=\"this.src='"+errorSrc+"'\"/></div>\n"+
                        					"	 </div>\n"+
											"	 <div class='lcdLastPersonInfo'>\n"+
											"	 	<div class='lcdLastPersonInfo'>"+ lastPersonID + "</div>\n"+
											"	 	<div class='lcdLastPersonInfo'>"+ lastPersonName + "</div>\n"+
											"	 	<div class='lcdLastPersonInfo'>"+ lastPersonDepartment + "</div>\n"+
											"	 </div>\n"+
											"</div>";
							 $("#visInfoAll_lcd").prepend(persInfo);
						    if ($("#visInfoAll_lcd").children().length > 5) {
						    	$("#visInfoAll_lcd").children().last().remove();
						    }
					    }
				    }
				}
			});
			return client;
		}
		var client = initLcdEventSocket();
		//设置定时器，10分钟发送请求，防止30分钟内没有任何操作导致页面出现登录超时的问题；
		setInterval(function() {
			client.send(new Date())
		}, 600000);
	})();

	function accLcdNoDataMessage(devId) {
	    var html="";
	    if("visInfoAll_lcd" == devId) {
	        html = "<div id='accLcdNoDataDiv' class='grid-empty-box' style='position: unset;'>"
	    } else {
	        html = "<div id='accLcdCurrentNoDataDiv' class='grid-empty-box' style='margin-top: 10%;position: unset;'>";
	    }
        html += "<div class='grid-empty-cont'><div class='grid-empty-img'></div><div class='grid-empty-txt'><@i18n 'base_msg_noData'/></div></div></div>";
        $("#"+devId).html("").append(html);
    }
</script>
</html>