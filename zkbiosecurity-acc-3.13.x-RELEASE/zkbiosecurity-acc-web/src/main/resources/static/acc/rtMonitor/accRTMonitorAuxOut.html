<#assign leftRTL = "left">
<#assign rightRTL = "right">
<#if enableRTL?? && enableRTL=="true">
<!-- 定义全局对齐方式变量(针对阿拉伯页面) -->
<#assign leftRTL = "right">
<#assign rightRTL = "left">
</#if>
<!-- 辅助输出DataView模板--begin -->
<textarea id="auxOutTemplateContainer${tabUUID!}" class="displayN">
	<div align="center" onclick="clickCenter(event,this,'auxOut')" class="unSelectDiv">
		<div onmouseover="showPopUpInfo(this);" onmouseout="accRTMonitorHidePopUpInfo()(this);">
			<div onmousedown="return false;" class="acc_rt_auxImg aux_out_{obj.image}" ></div>
<!-- 			<img onmousedown="return false;" border="0" src="${base}/images/auxOutState/icon/aux_out_{obj.image}.png" > -->
			<div class="displayN dataViewOpItem poPupInfo" style="position: fixed;top: 263px;${leftRTL!'left'}: 325px;z-index: 9999999;" align="${leftRTL!'left'}">
				<div class="poPupInfo">
					<p class="poPupInfo_title_p"><@i18n 'common_status'/></p>
					<table class="poPupInfo_text_table">
						<tr>
							<td><@i18n 'common_dev_entity'/>:</td>
							<td>{obj.devAlias}</td>
						</tr>
						<tr>
							<td><@i18n 'common_number'/>:</td>
							<td>{obj.no}</td>
						</tr>
					</table>
					<div style="display: {obj.opDisplay};" onclick="showFlag=false;accRTMonitorHidePopUpInfo()">
						<ul class="poPupInfo_op_ul">
							<@ZKUI.Permission name="acc:rtMonitor:openAuxOut">
								<li onclick="accRTMonitorOperateAuxOut('/accRTMonitor.do?openAuxOut', '{obj.id}', '${tabUUID!}')" title="<@i18n 'acc_eventNo_12'/>"><@i18n 'acc_rtMonitor_remoteOpen'/></li>
							</@ZKUI.Permission>
							<@ZKUI.Permission name="acc:rtMonitor:closeAuxOut">
								<li onclick="accRTMonitorOperateAuxOut('/accRTMonitor.do?closeAuxOut', '{obj.id}', '${tabUUID!}')" title="<@i18n 'acc_eventNo_13'/>"><@i18n 'acc_rtMonitor_remoteClose'/></li>
							</@ZKUI.Permission>
							<@ZKUI.Permission name="acc:rtMonitor:auxOutNormalOpen">
								<li onclick="accRTMonitorOperateAuxOut('/accRTMonitor.do?auxOutNormalOpen', '{obj.id}', '${tabUUID!}')" title="<@i18n 'acc_rtMonitor_remoteNormalOpen'/>"><@i18n 'acc_rtMonitor_remoteNormalOpen'/></li>
							</@ZKUI.Permission>
						</ul>
					</div>
				</div>
			</div>
		</div>
		<p class="dhx_item_text" style="word-wrap:break-word; word-break:normal;" title="{obj.name}">{obj.name}</p>
	</div>
</textarea>
<!-- 辅助输出DataView模板--end -->
<@ZKUI.Toolbar id="rtmToolauxOut${tabUUID!}" uuid="${tabUUID!}">
	<@ZKUI.ToolItem type="text" text="common_currentAll"/>
	<@ZKUI.ToolItem permission="acc:rtMonitor:openAuxOut" id="/accRTMonitor.do?openAuxOut" img="comm_remoteNormalOpenAuxout.png" text="acc_rtMonitor_remoteOpen" action="_operateAuxOut"/>
	<@ZKUI.ToolItem permission="acc:rtMonitor:closeAuxOut" id="/accRTMonitor.do?closeAuxOut" img="comm_remotecloseAuxout.png" text="acc_rtMonitor_remoteClose" action="_operateAuxOut"/>
	<@ZKUI.ToolItem permission="acc:rtMonitor:auxOutNormalOpen" id="/accRTMonitor.do?auxOutNormalOpen" img="comm_remoteNormalOpenAuxout.png" text="acc_rtMonitor_remoteNormalOpen" action="_operateAuxOut"/>
</@ZKUI.Toolbar>
<div id="auxOutDV${tabUUID!}" class="acc_rtm_dataview" onclick="clearOutSelect('auxOutDV${tabUUID!}')"></div>
<div class="acc_rtm_grid_foot">
	<div style="float: ${leftRTL!'left'};margin-${leftRTL!'left'}: 5px;">
		<@i18n 'common_currentTotal'/>:<span id="auxOutCount${tabUUID!}">0</span>
	</div>
	<div class="divBorder" style="margin-${leftRTL!'left'}: 5%;">
		<span style="display:inline-block">
			<span class="icv-greenPilot"></span>
			<@i18n 'common_online'/>:<em id="auxOutOnlineCount${tabUUID!}">0</em>&nbsp; 
		</span>
		<span style="display:inline-block">
			<span class="icv-yellowPilot"></span>
			<@i18n 'common_disable'/>:<em id="auxOutDisbleCount${tabUUID!}">0</em>&nbsp;
		</span>
		<span style="display:inline-block">
			<span class="icv-redPilot"></span>
			<@i18n 'common_offline'/>:<em id="auxOutOfflineCount${tabUUID!}">0</em>&nbsp;
		</span>
		<span style="display:inline-block">
			<span class="icv-unknownPilot"></span>
			<@i18n 'common_unknown'/>:<em id="auxOutUnknownCount${tabUUID!}">0</em>
		</span>
    </div>
    <div class="divBorder" style="margin-${leftRTL!'left'}: 5%;">
		<@i18n 'acc_rtMonitor_auxOutName'/>&nbsp;<input type="text" id="auxOutFilter${tabUUID!}" name="name" style="height: 16px;line-height: 16px;margin-top: -2px" onkeyup="dataRTFilter()"/>
    </div>
</div>
<script type="text/javascript">
(function() {
    if(window.accRTM==undefined||window.accRTM==null){
        window.accRTM = {};
    }
    window.accRTM.auxOutId="${tabUUID!}";
	var auxOutView;
	if(Web) {
		setTimeout(function(){
			Web.callEvent("onInitDataView", ["auxOut", {type: {template: "html->auxOutTemplateContainer${tabUUID!}"}, container: "auxOutDV${tabUUID!}"},"/accRTMonitor.do?getAllAuxOut&u=${uuid!}", function(d) {
				auxOutView = d;
			}]);
		}, 100)
	}
})();
</script>