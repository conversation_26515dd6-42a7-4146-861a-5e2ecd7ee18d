<#assign leftRTL = "left">
<#assign rightRTL = "right">
<#if enableRTL?? && enableRTL=="true">
<!-- 定义全局对齐方式变量(针对阿拉伯页面) -->
<#assign leftRTL = "right">
<#assign rightRTL = "left">
</#if>
<#assign gridName="accRTMonitorGrid${uuid!}">
<script type="text/javascript" src="/system/js/baseMediaFile.js" charset="UTF-8"></script>
<script src="js/registerClient.js"></script>
<style type="text/css">
.acc_rtm_box {
	position:relative;
	width:100%;
	height:100%;
}

<#if enableRTL?? && enableRTL=="true">
.dhx_dataview_item {
	float:right!important;
}
</#if>
.acc_rtm_box .acc_rtm_search {
	width:100%;
}

.poPupInfo_op_ul {
	background-color:#fff;
}

.acc_rtm_grid {
	position:absolute;
	left:0px;
	right:0px;
	top:0px;
	bottom:40px
}

.acc_rtm_grid_foot {
	position:absolute;
	bottom:0px; 
	height:18px; 
	left:0px;
	right:0px;
	padding: 2px 10px;
}

.acc_rtm_grid_foot1 {
    position:absolute;
    bottom:21px;
    height:18px;
    left:0px;
    right:0px;
    padding: 2px 10px;
    border-bottom: #c9c6c6 solid 1px;
}

.acc_rtm_box .acc_rtm_tab {
	position:absolute;
	left:0px;
	right:0px;
	bottom:0px;
	top: 34px;
}

.acc_rtm_box .acc_rtm_tab .acc_rtm_tab_box {
	position:relative;
	width:100%;
	height:100%;
}

.acc_rtm_box .acc_rtm_tab .acc_rtm_tab_cell {
	width:100%;
	height:100%;
}

.acc_rtm_dataview {
	position:absolute;
	bottom:20px;
	left:0px;
	right:0px;
	top:40px;
}

.dhxdataview_placeholder{
	height: 80px !important;
}

.dhx_dataview_item{
	height: 71px !important;
}
.accAlarmEventShowDiv {
	height : auto;
	width : auto;
    position: absolute;
    bottom: 60px;
    right: 20px;
 	border: 1px solid #B9C5D0;
 	border-radius: 3px;
 	max-height:190px;
 	overflow-y: auto;
    padding: 1px 1px;
    background-color: #eff4f8;
    display: none;
}
.accAlarmEventShowDiv td {
    padding:5px 5px 0px 20px;
}
.accRTMCancelAlarmEvent{
	padding-right:5px;
	cursor: pointer;
	padding-left: 30px;
}
.accRTMAlarmTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.accEventShowDiv {
	height : auto;
	width : auto;
    position: absolute;
    bottom: 1px;
    right: 1px;
 	border: 1px solid #B9C5D0;
 	background-color:#fff;
 	z-index:999999;
}
.acc-event-title-panel {
    background: #EAEFF4;
    border-bottom: 1px solid #EAEFF4;
}
</style>
<div class="acc_rtm_box" id="acc_rtm_box${uuid!}" gridName="${gridName!}">
<div id="alert_sound${uuid!}"></div>
<div id="alarm_sound${uuid!}"></div>
	<div class="acc_rtm_search">
		<input id="loadPlugins" type="hidden" value="${loadPlugins!}"><!-- 视频插件加载版本,用于加载ocx -->
		<@ZKUI.Searchbar hideQueryButton="true" hideActQueryButton="true" hideTip="true" gridName="accRTMSearch${uuid}">
	    	<@ZKUI.SearchTop>
	    		<tr>
				    <td valign="middle">
				    	<@ZKUI.ComboTree url="authArea.do?tree" id="authAreaId${uuid!}" onChange="dataRTFilter" title="base_area_entity" name="authAreaId"/>
						<input id="filterAreaId${uuid}" type="hidden"/>
						<input id="filterAreaName${uuid}" type="hidden"/>
				    </td>
				    <td valign="middle">
				    	<@ZKUI.Combo title="common_status" readonly="readonly" id="devStatus${uuid}" onChange="dataRTFilter">
	                        <option value="1"><@i18n 'common_online'/></option>
							<option value="2"><@i18n 'common_disable'/></option>
							<option value="0"><@i18n 'common_offline'/></option>
							<option value="-1"><@i18n 'common_unknown'/></option>
	                	</@ZKUI.Combo>
				    </td>
				    <td>
				    	<label class="search-label"><@i18n 'common_dev_name'/></label>
	                	<input id="devName${uuid}" type="text" class="search-input" onkeyup="dataRTFilter()" title="<@i18n 'common_dev_name'/>"/>
				    </td>
				</tr>
	    	</@ZKUI.SearchTop>
			<@ZKUI.SearchBelow>
				<tr>
					<td>
						<label class="search-label"><@i18n 'common_dev_sn'/></label>
						<input id="devSn${uuid}" type="text" class="search-input" onkeyup="dataRTFilter()" title="<@i18n 'common_dev_sn'/>"/>
					</td>
				</tr>
			</@ZKUI.SearchBelow>
	    </@ZKUI.Searchbar>
	</div>
	<div class="acc_rtm_tab">
		<@ZKUI.Layout id="accRTMLayout${uuid!}" pattern="2E">
			<@ZKUI.Cell hideHeader="true">
				<div id="accRTMonitorTab${uuid!}" class="acc_rtm_tab_box">
					<div id="doorTab${uuid!}" class="acc_rtm_tab_cell"></div>
					<div id="auxInTab${uuid!}" class="acc_rtm_tab_cell"></div>
					<div id="auxOutTab${uuid!}" class="acc_rtm_tab_cell"></div>
					<div id="elevatorTab${uuid!}" class="acc_rtm_tab_cell"></div>
				</div>
				<script type="text/javascript">
                    Web.closeClient("devClient");
                    Web.closeClient("eventClient");
					(function() {
						var tabbar;
						function initTabCell(id, url) {
							var tab = tabbar.cells(id);
							if (tab) {
								tab.progressOn();
								tab.attachObject(id +"Tab${uuid!}");
								setIdHtmlByPath(url + "&tabUUID=${uuid!}", id+"Tab${uuid!}");
								tab.progressOff();
							}
						}
						
						function initTabbar() {
							tabbar = new dhtmlXTabBar("accRTMonitorTab${uuid}", "top");
							tabbar.enableAutoReSize();
							tabbar.attachEvent("onSelect", function(id) {
								$("#acc_rtm_box${uuid!}").data("currentEntity", id);
								return true;
							});
							$("#acc_rtm_box${uuid!}").data("tabbar", tabbar);
							tabbar.addTab("door", "<@i18n 'acc_door_entity'/>", null, null, true);
							initTabCell("door", "skip.do?page=acc_rtMonitor_accRTMonitorDoor");
							tabbar.addTab("auxIn", "<@i18n 'common_leftMenu_auxIn'/>", null, null);
							initTabCell("auxIn", "skip.do?page=acc_rtMonitor_accRTMonitorAuxIn");
							tabbar.addTab("auxOut", "<@i18n 'acc_leftMenu_auxOut'/>", null, null);
							initTabCell("auxOut", "skip.do?page=acc_rtMonitor_accRTMonitorAuxOut");
							tabbar.setSizes();
						}
						
						var rtDataViews = {};
						$("#acc_rtm_box${uuid!}").data("rtDataViews", rtDataViews);
						ZKUI.Layout.get("accRTMLayout${uuid!}").layout.attachEvent("onPanelResizeFinish", function() {
							tabbar.setSizes();
							$("#${gridName}").height("100%");
							$("#${gridName}").width("100%");
							ZKUI.Grid.get("${gridName}").grid.setSizes();
						});

                        var filterAreaIds = accRTMonitorGetAreasByUser();
                        function accRTMonitorGetAreasByUser() {
                            var v = "";
                            $.ajax({
                                url:"accRTMonitor.do?getAreasByUser",
                                type:"post",
                                async:false,
                                dataType:"json",
                                data:{
                                },
                                success:function (result) {
                                    if (result.data != null) {
                                        v = result.data.areaId;
                                        $("#filterAreaId${uuid}").val(result.data.areaId);
                                        $("#filterAreaName${uuid}").val(result.data.areaName);
                                    }
                                }
                            });
                            return v;
                        }
						function dataRTFilter() {
							var areaIdArray = ZKUI.ComboTree.get("authAreaId${uuid!}").combo.getActualValue().split(",");
							var devName = $("#devName${uuid!}").val();
							var devStatus = ZKUI.Combo.get("devStatus${uuid!}").getValue();
							var devSn = $("#devSn${uuid!}").val();
							for(var key in rtDataViews)
						    {
								rtDataViews[key].filter(function(obj, value){
									var filter = "";
									var name = "";
									if($("#" + value + "Filter${uuid!}").length > 0){
										filter = $("#" + value + "Filter${uuid!}").val();
										name = $("#" + value + "Filter${uuid!}").attr("name");
									}
									if((areaIdArray == "" || $.inArray(obj.areaId.toString(), areaIdArray) != -1) 
											&& (devName == "" || obj.devAlias.toLowerCase().indexOf(devName.toLowerCase()) != -1)
											&& (devStatus == "" || obj.connect == devStatus)
											&& (devSn == "" || obj.devSn.toLowerCase().indexOf(devSn.toLowerCase()) != -1)
											&& (filter == "" || obj[name].toLowerCase().indexOf(filter.toLowerCase()) != -1))
									{
										return true;
									}
									return false;
								}, key);
								setViewFooterValues(key);
						    }
                            accRTMonitorEventFilter("${uuid!}");
						}
						
						window.dataRTFilter = dataRTFilter;
						var devClient;
						var clientId = Math.round(Math.random() * 10000)+"${uuid!}";
						function initDevClient() {
							if(!devClient) {
								devClient = Web.getSocket({
									id:"devClient",
									url:"accRTMonitor/getDevState",
									param:JSON.stringify({"clientId" : clientId, "areaIds" : filterAreaIds}),
									onMessage:function(resp) {
										devClient.devData = JSON.parse(resp.body);
										if(devClient.devData.clientId && clientId!=devClient.devData.clientId) {
											return;
										}
										updateDevState();
                                        autoChangeDoor();
									}
								});
							}
						}
						
						function updateDevState() {
							if(devClient && devClient.devData) {
								var data = devClient.devData;
								for(var index in data) {
									var viewCode = index.substring(0,index.indexOf("States"));
									if(!rtDataViews[viewCode]) {
										continue;
									}
									for(var i = 0; i < data[index].length; i++) {
										var id = data[index][i].id;
                                        data[index][i].sensor = I18n.getValue(data[index][i].sensor);
                                        data[index][i].relay = I18n.getValue(data[index][i].relay);
                                        data[index][i].alarm = I18n.getValue(data[index][i].alarm);
										if(rtDataViews[viewCode].exists(id)) {
											var viewData = rtDataViews[viewCode].get(id);
											rtDataViews[viewCode].set(id, $.extend(viewData, data[index][i]));
											if(data[index][i].image.indexOf("disable") != -1) {
												rtDataViews[viewCode].moveBottom(id);
											}
											else if(data[index][i].image.indexOf("alarm") != -1) {
												rtDataViews[viewCode].moveTop(id);
											}
											var audioCheckbox = document.getElementById("audioCheckbox${uuid!}");
											if(audioCheckbox && audioCheckbox.checked) {
												if(data[index][i].audio) {
													if(dhx4.isFF||dhx4.isChrome) {
														var allAudios = $("audio[name='alarmAudio']");
														var addFlag = "true";
														for (j = 0; j < allAudios.length; j++) {
															if($(allAudios[j]).attr('res') == data[index][i].audio) {
																if($(allAudios[j]).attr('allIds').indexOf(","+data[index][i].id+",")<0) {
																	var allIds = $(allAudios[j]).attr('allIds')+data[index][i].id+",";
																	$(allAudios[j]).attr('allIds',allIds);
																}
																addFlag = "false";
															}
														}
														if(addFlag == "true") {
															var doorAudio = "<audio name='alarmAudio' allIds=',"+data[index][i].id+",' controls='controls' hidden='false' loop='false' res='"+data[index][i].audio+"' ><source id='alarmsource2' name = 'soundname' src='"+data[index][i].audio+"' type='audio/mp3' preload='auto' /><source id='alarmsource1' name = 'soundname' src='"+data[index][i].audio+"' type='audio/x-wav' preload='auto'/><embed id='alarmsource3' name = 'soundname' height='100' width='100' src='"+data[index][i].audio+"' preload='auto'/></audio>";
															$("#alarm_sound${uuid!}").append(doorAudio);
															var alarmAudio = $("audio[allIds=',"+data[index][i].id+",']")[0];
															alarmAudio.play();
														}
													} else {
														var allbgsound = $("bgsound[name='alarmSound']");
														var addFlag = "true";
														for (j = 0; j < allbgsound.length; j++) {
															if($(allbgsound[j]).attr('res') == data[index][i].audio) {
																if($(allbgsound[j]).attr('allIds').indexOf(","+data[index][i].id+",")<0) {
																	var allIds = $(allbgsound[j]).attr('allIds')+data[index][i].id+",";
																	$(allbgsound[j]).attr('allIds',allIds);
																}
																addFlag = "false";
															}
														}
														if(addFlag == "true") {
															var doorbgsound = "<bgsound name='alarmSound' src='"+data[index][i].audio+"' allIds=',"+data[index][i].id+",' loop='-1'  res='"+data[index][i].audio+"' />";
															$("#alarm_sound${uuid!}").append(doorbgsound);
														}
													}
												}
											}
										}
									}
									setViewFooterValues(viewCode);
								}
							}
						}
						
						if(Web) {
							Web.hasInitDataView = true;
							Web.detachEvent("onInitDataView");
							Web.attachEvent("onInitDataView", function(id, opts, url, callback) {
								opts = opts || {};
								var dataViewConfig = {
										type: {
									        template: "html->doorTemplateContainer${uuid!}",
									        css:"ficon",
									    	width:70,
											height:70,
											margin:0,
											padding:3
									    }
								};
								dataViewConfig = $.extend(dataViewConfig, opts);
								var myDataView = new dhtmlXDataView(dataViewConfig);
								if(url) {
									myDataView.load(url, "json", function(){
										if(typeof(callback)=="function") {
											callback(myDataView);
										}
										initDevClient();
								    	setViewFooterValues(id);
								    });
								}
								rtDataViews[id] = myDataView;
							})
						}
						
						function setViewFooterValues(viewCode) {
							var offlineCount = 0;
						    var onlineCount = 0;
						    var disbleCount = 0;
						    var unknownCount = 0;
						    
					        for(var i = 0; i < rtDataViews[viewCode].dataCount(); i++)
					        {
					        	var connect = rtDataViews[viewCode].get(rtDataViews[viewCode].idByIndex(i)).connect;
					        	var doorState = rtDataViews[viewCode].get(rtDataViews[viewCode].idByIndex(i)).doorState;
					        	switch (parseInt(connect))
						    	{
									case 0:
										offlineCount++;
										break;
									case 1:
										if (typeof(doorState) != "undefined") {
											if (doorState & 1 == 1) {
												onlineCount++;
											} else {
												offlineCount++;
											}
										} else {
											onlineCount++;
										}
										break;
									case 2:
										disbleCount++;
										break;
									case -1:
										unknownCount++;
										break;
								}
					        }

							$("#" + viewCode + "OfflineCount${uuid!}").html(offlineCount);
							$("#" + viewCode + "OnlineCount${uuid!}").html(onlineCount);
							$("#" + viewCode + "DisbleCount${uuid!}").html(disbleCount);
							$("#" + viewCode + "UnknownCount${uuid!}").html(unknownCount);
							$("#" + viewCode + "Count${uuid!}").html(rtDataViews[viewCode].dataCount());
						    return true;
						}
						
						function init() {
							initTabbar();
							setCountTimeout(function() {
								tabbar.setSizes();
							}, 50, 10);
						}
						
						init();
					})()
				</script>
			</@ZKUI.Cell>
			<@ZKUI.Cell title="acc_rtMonitor_realTimeEvent">
				<div class="acc_rtm_grid">
					<@ZKUI.Grid gridName="${gridName}" showColumns="!filter" originSort="true" vo="com.zkteco.zkbiosecurity.acc.vo.AccRTMonitorItem" gridType="right" nopaging="true"/>
				</div>
                <div id="menuData${uuid}" style="display: none;">
                    <@ZKUI.Permission name="pers:person:edit">
                        <div id="addCardToNewPerson" img="pers_batchAddPersCard.png" text="<@i18n 'common_rtMonitor_cardRegisterPerson'/>"></div>
                    </@ZKUI.Permission>
                    <div id="addCardToOldPerson" img="pers_batchAddPersCard.png" text="<@i18n 'acc_rtMonitor_addToRegPerson'/>"></div>
                </div>

                <div class="dhx_toolbar_dhx_web acc_rtm_grid_foot1">
					<#if Application['system.securityLevel']?? && (Application['system.securityLevel'] >3 || Application['system.securityLevel'] =3) >
					<div id="showAlarm${uuid!}" style="float: ${rightRTL!'right'}; cursor: pointer;position: relative;" onclick="accRTMShowAlarmEvent(this);" >
						<span id="remindAlarmEvent"><@i18n 'acc_rtMonitor_alarmEvent'/></span>
						<img src="images/up.png" />
					</div>
					</#if>
                    <div id="showPhoto${uuid!}" style="float: ${rightRTL!'right'}">
                        <@i18n 'common_rtMonitor_showPhotos'/>
						<@ZKUI.Input hideLabel="true" id="msgCheckbox${uuid!}" type="checkbox" checked="checked"/>
                    </div>
                    <div id="showAudio${uuid!}" style="float: ${rightRTL!'right'}">
                        <@i18n 'acc_rtMonitor_playAudio'/>
						<@ZKUI.Input hideLabel="true" id="audioCheckbox${uuid!}" type="checkbox"/>
                    </div>
                </div>

				<div class="dhx_toolbar_dhx_web acc_rtm_grid_foot">
				    <div style="float: ${leftRTL!'left'};">
				    	<@i18n 'common_rtMonitor_totalReceived'/>:
				    	<em id="totalEventCount${uuid!}">0</em>
				    	<em id="maxEventCount${uuid!}" hidden ="true">100</em>
				    </div>
				    <div class="divBorder" style="margin-${leftRTL!'left'}: 5%;">
			    		<span class="display_line_block">
							<span class="icv-greenPilot"></span>
			    			<@i18n 'common_normal'/>:<em id="normalEventCount${uuid!}">0</em>&nbsp;
			    		</span>
						<span class="display_line_block">
							<span class="icv-yellowPilot"></span>
							<@i18n 'common_exception'/>:<em id="warningEventCount${uuid!}">0</em>&nbsp;
						</span>
						<span class="display_line_block">
							<!--<a href="javascript:redirectToAlarmPage()">-->
								<!--<img height="12px" src="${base}/public/images/redPilot.png"/>-->
								<!--<@i18n 'common_alarm'/>:-->
							<!--</a>-->
							<span class="icv-redPilot"></span>
							<@i18n 'common_alarm'/>:
							<em id="alarmEventCount${uuid!}">0</em>
						</span>
				    </div>
				    <div class="divBorder" style="margin-${leftRTL!'left'}: 2%;">
				    	<a href="javascript:clearEventRow('${gridName!}','${uuid!}')"><@i18n 'common_rtMonitor_clearRowsData'/></a>
				    </div>
				    <div class="divBorder" style="margin-${leftRTL!'left'}: 2%;">
				    	<a href="javascript:void(0);" id="a_newMsg${uuid!}" style="display: none;">
				    		<img height="12px" src="/images/newMsg.gif"/>
				    		<@i18n 'common_newMsg'/>:<em id="newMsgEventCount${uuid!}">0</em>
				    	</a>
				    </div>
				    <div class="divBorder" style="margin-${leftRTL!'left'}: 2%;">
			    		<@i18n 'common_eventDescription'/>&nbsp;
			    		<input type="text" id="eventTypeFilter${uuid!}" style="height: 16px;line-height: 16px;margin-top: -2px" onchange="dataViewFilter(this,'${gridName!}')" onkeyup="dataViewFilter(this,'${gridName!}')"/>
				    </div>

				</div>
				<script type="text/javascript">
				(function() {
					var MAX_NUM = 200;
					var audioArray = new Array();
					var eventAudioTimeout;
					var clientId = Math.round(Math.random() * 10000)+"${uuid!}";
					loadAudioEventData();
                    /* 存在视频模块才加载弹出视频方法 */
                    if ("${systemModules}".toLowerCase().indexOf("vid") != -1 || "${systemModules}".toLowerCase().indexOf("vms") != -1) {
                        window.setTimeout('loadPopUpVideo("acc")', 1000);
                    }
					function loadAudioEventData()
					{
						window.clearTimeout(eventAudioTimeout);
						var audioCheckbox = document.getElementById("audioCheckbox${uuid!}");
						if(audioCheckbox && audioCheckbox.checked)
						{
							if(eventAudioTimeout >0)
							{

								closeAudio("alert_sound${uuid!}");
								if(audioArray.length>0)
								{
									$("#myplay").remove();
									var sound = audioArray.shift();
									/*loadAndPlayAudio("alert_sound${uuid!}", sound, "${uuid!}");*/
                                    loadAndPlayAudio("alert_sound${uuid!}", sound);
								}
							}
						}
						else
						{
							while(audioArray.length>0)
							{
								audioArray.pop();
							}
						}
						eventAudioTimeout = window.setTimeout(loadAudioEventData , 3000);
					}

					function renderAccMonitorGridRow(row) {
						if(!ZKUI.Grid.get("${gridName}")) {
							accMonitorScheduler.stop();
							return;
						}
						var dhxGrid = ZKUI.Grid.get("${gridName}").grid;
						var authAreaName = $("#filterAreaName${uuid}").val();
						var isFilter = "false";
						if(dhxGrid && typeof(dhxGrid.setSizes) == "function") {
							var normalNum = 0;
							var warningNum = 0;
							var alarmNum = 0;

							isFilter = checkAuthArea(row.data[1], authAreaName);
							if(row.data[11] && "true" == isFilter) {
								var id = row.id;
								var maxEventCount = parseInt($("#maxEventCount${uuid!}").html());
								if(dhxGrid.getRowsNum() >= maxEventCount) {
									dhxGrid.deleteRow(dhxGrid.getRowId(dhxGrid.getRowsNum()-1));
								}
								var eventNameInfo = row.data[4].split(",");
								var eventName = "";
								if(eventNameInfo.length > 1) {
									eventName = I18n.getValue(eventNameInfo[0]) + "("+ I18n.getValue(eventNameInfo[1]) + ")";
								} else {
									eventName = I18n.getValue(row.data[4]);
								}
								if(row.data[4] == "acc_eventNo_7") {
									if(dhx4.isFF||dhx4.isChrome) {
										var allAudios = $("audio[name='alarmAudio']");
										for (j = 0; j < allAudios.length; j++) {
											var allIds = $(allAudios[j]).attr('allIds');
											if(allIds.indexOf(","+row.data[10]+",")>=0) {
												var tempStr = ","+row.data[10]+",";
												allIds = allIds.replace(tempStr,",");
												$(allAudios[j]).attr('allIds',allIds);
												if(allIds == ",") {
													$("audio[allIds=',']").remove();
												}
											}
										}
									}
									else {
										var allbgsound = $("bgsound[name='alarmSound']");
										for (j = 0; j < allbgsound.length; j++) {
											var allIds = $(allbgsound[j]).attr('allIds');
											if(allIds.indexOf(","+row.data[10]+",")>=0) {
												var tempStr = ","+row.data[10]+",";
												allIds = allIds.replace(tempStr,",");
												$(allbgsound[j]).attr('allIds',allIds);
												if(allIds == ",") {
													$("bgsound[allIds=',']").remove();
												}
											}
										}
									}
								}
								row.data[4] = eventName;
								row.data[8] = convertVerifyModeName(row.data[8]);
								var personPin = row.data[12];
								var personName = row.data[6];
								var personCard = row.data[5];
								<#if hasPermission('acc:pin:encryptProp','true')>
									personPin = convertToEncrypt(personPin,"${pinEncryptMode!S2}");
								</#if>
								<#if hasPermission('acc:name:encryptProp','true')>
									personName = convertToEncrypt(personName,"${nameEncryptMode!S1}");
								</#if>
								<#if hasPermission('acc:cardNo:encryptProp','true')>
									personCard = convertToEncrypt(personCard,"${cardEncryptMode!S2}");
								</#if>
								row.data[6] = personPin;
								if (personName != "") {
									row.data[6] = personPin + "(" + personName + ")";
								}
								row.data[5] = personCard;
								dhxGrid.addRow(id, row.data, 0);
								var textColor = getTextColorByDataLevel(row.dataLevel, row.style);
								dhxGrid.setRowTextStyle(id, textColor);
								dhxGrid._f_rowsBuffer=null;
								for(var key in row.userdata) {
									if (key == "globalLinkageEvent") {
				        				var globalLinkageEvent = row.userdata[key];
										globalLinkageEvent.cardNo = personCard;
										globalLinkageEvent.pin = personPin;
										globalLinkageEvent.name = personName;
										<#if hasPermission('acc:name:encryptProp','true')>
											globalLinkageEvent.lastName = convertToEncrypt(globalLinkageEvent.lastName, "${nameEncryptMode!S1}");
										</#if>
				        			}
									dhxGrid.setUserData(id, key, row.userdata[key]);
								}

								var areaName = ZKUI.ComboTree.get("authAreaId${uuid!}").combo.getComboText();
								var devStatus = ZKUI.Combo.get("devStatus${uuid!}").getValue().trim();
								var devName = $("#devName${uuid!}").val();
								var devSn = $("#devSn${uuid!}").val();
								var values = row.data[9].split("##");
								var currentEntity = $("#acc_rtm_box${uuid!}").data("currentEntity") || "door";
								var filter = $("input#"+ currentEntity +"Filter${uuid!}").val();
								if((areaName == "" || areaName.indexOf(dhxGrid.cells(id,1).getValue()) != -1)
											&& (authAreaName == "" || authAreaName.indexOf(dhxGrid.cells(id,1).getValue()) != -1)
											&& (devStatus == "" || devStatus == 1)
											&& (devName == "" || values[0].indexOf(devName) != -1)
											&& (devSn == "" || values[1].indexOf(devSn) != -1)
											&& (filter == "" ||
												(values.length >= 4 && ((values[2].indexOf(filter) != -1) && getCurrentEntityType(currentEntity) == values[3]))))
								{
									if(typeof(row.vidDevices) != "undefined")
									{
										global_vidDevices.push({vidDescription:row.vidDescription,vidDevices:row.vidDevices});
									}
									accRTMonitorEventMsg(id, "${uuid!}");
									if ("${systemModules}".toLowerCase().indexOf("ivs") != -1) {
										/** 弹出联动视频实时预览 */
										accRTMonitorPopUpVideo();
									}

									if(row.args != null) {
										audioArray.push(row.args);
										var arrLen = audioArray.length;
										for(var j = 0; arrLen >= 50 && j < (arrLen-50);j++) {
											audioArray.shift();
										}
									}
								}
								if(sysCfg.securityLevel && sysCfg.securityLevel >= 3 && row.dataLevel == "2") {
									pushAlarmEventToDiv(id, "${uuid!}");
								}
								switch (row.userdata["status"]) {
									case "normal":
										normalNum++;
										break;
									case "warning":
										warningNum++;
										break;
									case "alarm":
										alarmNum++;
										break;
								}
							}
							accRTMonitorEventFilter("${uuid!}");
							dhxGrid.setSizes(false);
							var rowsLength = 0;
							if (isFilter == "true") {
								rowsLength = 1;
							}
							setEventFooterValues(rowsLength, normalNum, warningNum, alarmNum, "${uuid!}");
						}

					}

					var accMonitorScheduler = new EventScheduler(renderAccMonitorGridRow, 100, 1, this);
					accMonitorScheduler.start();
					window.accMonitorScheduler = accMonitorScheduler;

					function initEventSocket() {
                        var authAreaName = $("#filterAreaName${uuid}").val();
						var client = Web.getSocket({
							id : "eventClient",
							url : "accRTMonitor/getEventData",
							param:JSON.stringify({"clientId" : clientId}),
							onMessage:function(resp) {
								var data = JSON.parse(resp.body);
								if(data.clientId && clientId!=data.clientId) {
									return;
								}
								var dhxGrid = ZKUI.Grid.get("${gridName}").grid;
								if(data != null && typeof(dhxGrid.setSizes) == "function") {
									var rows = data.rows;
									data.audios = data.audios || [];
									for(var index in rows) {
									    accMonitorScheduler.addData(rows[index], data.audios[index]);
						        	}
								}
							}
						});
						return client;
					}
					var client = initEventSocket();
                    setInterval(function(){
                        client.send(new Date());
                    },600000);
				})();
				</script>
			</@ZKUI.Cell>
		</@ZKUI.Layout>
	</div>
</div>
<script type="text/javascript">
function getCurrentEntityType(val) {
	switch(val)
	{
		case "door" :   return "0";
		case "auxIn" :  return "1";
		case "auxOut" : return "2";
		default:        return "0";
	}
}
loadRightClickMenu();

function loadRightClickMenu()
{
    var cardNo;
    var eventGrid = ZKUI.Grid.get("${gridName}").grid;
    var menu = new dhtmlXMenuObject();
    menu.setIconsPath(sysCfg.rootPath + "/public/images/opToolbar/");
    menu.renderAsContextMenu();
    menu.attachEvent("onClick", function onButtonClick(menuItemId, type) {
        var dhxGrid = ZKUI.Grid.get("${gridName}").grid;
        var data = dhxGrid.contextID;
        if(menuItemId == "addCardToNewPerson") {
            DhxCommon.createWindow("accPerson.do?getAllField&cardNo=" + cardNo + "^0^0^950^670^<@i18n 'common_op_new'/>");
        }
        else {
            DhxCommon.createWindow("accPerson.do?filterPersonByVaildCard&cardNo=" + cardNo + "^0^0^900^500^<@i18n 'pers_person_selectedPerson'/>");
        }
        return true;
    });
    menu.loadFromHTML("menuData${uuid}", true, function () {});
    eventGrid.enableContextMenu(menu);
    eventGrid.attachEvent("onBeforeContextMenu", function(rowId, celInd, grid){
		// 开启卡号敏感信息保护,屏蔽右键功能
		<#if !hasPermission('acc:cardNo:encryptProp', 'true')>
			//判断事件点，过滤右键功能,当事件点的eventNo=27才能显示右键菜单
			if(this.getUserData(rowId, "isExistCardOp")) {
				cardNo = this.cells(rowId, this.getColIndexById("cardNo")).getValue();
				return true;
			}
		</#if>
        return false;
    });
}

function accRTMonitorEventFilter(uuid) {
	var gridName = $("#acc_rtm_box"+uuid).attr("gridName");
	var currentEntity = $("#acc_rtm_box"+uuid).data("currentEntity") || "door";
	if(ZKUI.Grid.get(gridName)==null){
		return
	}
	var dhxGrid = ZKUI.Grid.get(gridName).grid;
	var areaName = ZKUI.ComboTree.get("authAreaId"+uuid).combo.getComboText();
	var authAreaName = $("#filterAreaName${uuid}").val();
	var devStatus = ZKUI.Combo.get("devStatus"+uuid).getValue().trim();
	var devName = $("#devName"+uuid).val();
	var devSn = $("#devSn"+uuid).val();
	var filter = $("input#"+ currentEntity +"Filter"+uuid).val();
	dhxGrid.filterBy(9,function(value, id) {
		var values = value.split("##");
		if((areaName == "" || areaName.indexOf(this.cells(id,1).getValue()) != -1)
				&& (authAreaName == "" || authAreaName.indexOf(this.cells(id,1).getValue()) != -1)
				&& (devStatus == "" || devStatus == 1)
				&& (devName == "" || values[0].toLowerCase().indexOf(devName.toLowerCase()) != -1)
				&& (devSn == "" || values[1].toLowerCase().indexOf(devSn.toLowerCase()) != -1)
				&& (!filter || (values.length >= 4 && ((values[2].toLowerCase().indexOf(filter.trim().toLowerCase()) != -1) && getCurrentEntityType(currentEntity) == values[3]))))
		{
			return true;
		}
		else {
			return false;
		}
	}, false);
	var eventType = $("#eventTypeFilter"+uuid).val();
	if(eventType != "")
	{
		dhxGrid.filterBy(4,eventType);
	}
	setEventFooterValues(0,0,0,0,uuid);
}

function _operateDoor(id, bar, opt) {
	operateDoor(id, 0, bar.options.uuid);
}

function operateDoor(itemId, id, uuid)
{
	var objId = "door";
	var rtDataViews = $("#acc_rtm_box"+uuid).data("rtDataViews");
	if(rtDataViews[objId].first() == undefined)
	{
		//messageBox({messageType:"alert",text: "<@i18n 'acc_rtMonitor_noLegalDoor'/>"});
		openMessage(msgType.warning, "<@i18n 'acc_rtMonitor_noLegalDoor'/>");
		return;
	}
	else
	{
		remoteOperate(itemId, id, objId, null, null, uuid);
	}
}

function updateDoorName(id, val) {
    var rtDataViews = $("#acc_rtm_box${uuid!}").data("rtDataViews");
    var viewData = rtDataViews["door"].get(id);
    viewData.name = val;
    rtDataViews["door"].set(id, viewData);
}
var eventBoxIndex = 0;
function accRTMonitorEventMsg(id, uuid)
{
	var gridName = $("#acc_rtm_box"+uuid).attr("gridName");
	var dhxGrid = ZKUI.Grid.get(gridName).grid;
	var photoBase64 = dhxGrid.getUserData(id, "photoBase64");
	var personPinName = dhxGrid.cells(id, dhxGrid.getColIndexById("pinName")).getValue();
	var eventTime = dhxGrid.cells(id, dhxGrid.getColIndexById("eventTime")).getValue();
	var eventName = dhxGrid.cells(id, dhxGrid.getColIndexById("eventName")).getValue();
	var eventLevelInfo = dhxGrid.getUserData(id, "eventLevel");
	var eventLevel = eventLevelInfo;
	if(eventLevelInfo.indexOf("#") > 0) {
		eventLevel = eventLevelInfo.split("#")[0];
	}
	var eventLevelText = convertAccEventLevelToText(dhxGrid.getUserData(id, "eventLevel"));
	var eventPointName = dhxGrid.cells(id, dhxGrid.getColIndexById("eventPointName")).getValue();
	var msgObj = document.getElementById("msgCheckbox"+uuid);
	if(dhxGrid.getUserData(id, "globalLinkageEvent") == null)
	{
		if(msgObj.checked && photoBase64 != "")//选中时开启
		{
			if("data:image/jpg;base64," == photoBase64) {
				photoBase64 = "/images/" + ((sysCfg.skin=='default'|| ! sysCfg.skin ) ? '': sysCfg.skin) + "/userImage.gif";
			}
            var nowTime = new Date().getTime();
            var photoEncrypt = "";
            <#if hasPermission('acc:headPortrait:encryptProp','true')>
            	photoEncrypt = "filter: blur(5px);"
            </#if>
			var text = "<div id='accEventContentDivId' style='padding: 0px 15px 0px 15px;text-align: center'>" +
							"<img src='" + photoBase64 +"' onerror='this.src=\"/images/userImage.gif\"' style='max-height: ${personPhotoMaxHeight!140}px;border: 1px solid #a4bed4;" + photoEncrypt + "'/>" +
							"<p>" + personPinName + "</p>" +
							"<p style='margin-top: 3px; text-overflow:ellipsis; overflow: hidden; width: 150px; white-space:nowrap;'>" + eventName + "</p>" +
							"<p>" + eventLevelText + "</p>" +
							"<p>" + eventTime + "</p>" +
							"<p>" + eventPointName + "</p>" +
						"</div>";
			if (sysCfg.securityLevel && sysCfg.securityLevel >= 3) {
				eventBoxIndex ++;
				var eventDiv = eventDiv+eventBoxIndex;
				eventDiv = document.createElement("div");
				eventDiv.setAttribute("id", "eventDivId"+eventBoxIndex);
				eventDiv.setAttribute("class", "accEventShowDiv");
				var eventDivIdTemp = "eventDivId"+eventBoxIndex;
				var eventHtml="<div class= 'accRTMEventTitle'>";
				var closeHtml ="<div class='icv-close' style='position:absolute; display:block; cursor:pointer; top:6px; right:6px; width:15px; height:15px;' onclick='removeAccRTMEvent("+eventDivIdTemp+")'></div>";
				var titleHtml="<div class='acc-event-title-panel' style='height:25px;'><div class='acc-jbox-title' style='float:left; width:90%; line-height:24px; padding-left:5px;overflow:hidden;text-overflow:ellipsis;word-break:break-all;'><@i18n 'common_devMonitor_msgTip' /></div></div>";
				var content = "<div style='margin:10px;line-height: 18px;'>" + text +"</div>";
				eventHtml += closeHtml + titleHtml + content +"</div>";
				eventDiv.innerHTML = eventHtml;
				document.body.appendChild(eventDiv);
				if(eventLevel != "2") {
					setTimeout("removeAccRTMEvent("+eventDivIdTemp+")", 5000);
				}
        	}
        	else {
        		$.jBox.messager(text, "<@i18n 'common_devMonitor_msgTip'/>", 0, {
					id: "jbox-div",
					width: 'auto',
					showType: 'fade',
					timeout: 5000/*,
					buttons: {"<@i18n 'common_devMonitor_doTotPrompt'/>": "ok"},
					submit: function (v, h, f) {
						msgObj.checked = false;
						enableMsg(msgObj);
						return true;
					}*/
				});
				<#if enableRTL?? && enableRTL=="true">
					$(".jbox-messager .jbox").css("right","auto");
					$(".jbox-messager .jbox").css("left","1px");
				</#if>
        	}

		}
	}
	else
	{
		/* 实时监控界面才进抓图方法 */
		var vidLinkageHandle = dhxGrid.getUserData(id, "globalLinkageEvent").vidLinkageHandle;
		var vidLinkageData = dhxGrid.getUserData(id, "globalLinkageEvent").vidLinkageData;
		if(vidLinkageHandle != undefined && vidLinkageHandle != '')
		{
			var split = vidLinkageHandle.split('_');
			var type = split[0];
			if((type & 2) != 0 && split.length > 1)//包含抓图
			{
				var photoEncrypt = "";
				var captureEncrypt = "";
				<#if hasPermission('acc:headPortrait:encryptProp','true')>
					photoEncrypt = "filter: blur(5px);"
				</#if>
				<#if hasPermission('acc:capturePhoto:encryptProp','true')>
					captureEncrypt = "filter: blur(5px);"
				</#if>
				// 弹出抓拍图
				accPopUpCapture(id, split[1],"<@i18n 'acc_eventNo_8'/>",gridName, {
					vidLinkageData: vidLinkageData,
					photoEncryptStyle: photoEncrypt,
					captureEncryptStyle: captureEncrypt
				});
			}
		}
	}
}
function removeAccRTMEvent (eventDiv) {
	if(eventDiv) {
		eventDiv.remove();
	}
}
function enableMsg(obj)
{
	if(obj.checked)//选中时则播放
	{
		openMessage(msgType.success, "<@i18n 'common_devMonitor_turnedMsgTip'/>");//运行消息提示
	}
	else
	{
		openMessage(msgType.warning, "<@i18n 'common_devMonitor_closedMsgTip'/>");//关闭消息提示
	}
	createCookie("accRTEventMsg${(user.id)!}", obj.checked);
}

$("#msgCheckbox${uuid!}").click(function() {
	enableMsg(this);
});


function _operateAuxOut(id, bar, opt) {
    accRTMonitorOperateAuxOut(id, 0, bar.options.uuid);
}

function accRTMonitorOperateAuxOut(itemId, id, uuid)
{
	var objId = "auxOut";
	var rtDataViews = $("#acc_rtm_box"+uuid).data("rtDataViews");
	if(rtDataViews[objId].first() == undefined)
	{
		messageBox({messageType:"alert",text: "<@i18n 'acc_rtMonitor_noLegalAuxOut'/>"});
	}
	else
	{
		remoteOperate(itemId, id, objId, null, null, uuid);
	}
}

function operateElevator(id)
{
	DhxCommon.createWindow("/" + id);
}
function pwdChange() {
	$("#password_hidden").val(hex_md5($("#rtLoginPwd").val()));
}
function remoteOperate(itemId, id, objId, event, remark, uuid) {
	$("body").children("div.dataViewOpItem:first").remove();
	$.jBox.close();
	id = id || 0;
	var rtDataViews = $("#acc_rtm_box"+uuid).data("rtDataViews");
	var opt = ZKUI.Toolbar.get("rtmTool"+objId+uuid).getItemById(itemId);
    var data;
	if(getDoorIds()!=""){
        data = { title:"", id:id, opObj:"<@i18n 'common_selected'/>" };
	}else {
        data = { title:"", id:id, opObj:"<@i18n 'common_currentAll'/>" };
	}
	if(opt) {
		data.title = opt.text;
	}
	if(id) {
		data.opObj = rtDataViews[objId].get(id).name;
	}
	function dealRetFun(result)
	{
		if(result[sysCfg.ret] == sysCfg.success)
		{
			closeMessage();
			rtDataViews[objId].unselectAll();
			var text = "<div align='center' style='min-height:20px;max-height:70px; width:290px;  '>" +
				"<div style='margin-top: 3px;min-height:20px; max-height: 70px;overflow-y:auto; '>" + I18n.getValue(result[sysCfg.msg]) + "</div>" +
			"</div>";
			//messageBox({messageType:"alert",text: text});
			openMessage(msgType.success, I18n.getValue(result[sysCfg.msg]));
			$(".jbox-icon").addClass("jbox-rtBeforeIcon");
		}
		else if (result[sysCfg.ret] == sysCfg.error) {
            closeMessage();
            rtDataViews[objId].unselectAll();
            var msgArray = result[sysCfg.msg].split(";");
            var msg = "";
            for (i in msgArray) {
                if (msgArray[i] != "") {
                    msg += msgArray[i] + "<br>";
                }
			}
            var text = "<div align='center' style='min-height:20px;max-height:70px; width:290px;  '>" +
                "<div style='margin-top: 3px;min-height:20px; max-height: 70px;overflow-y:auto; '>" + msg + "</div>" +
                "</div>";
            //messageBox({messageType:"alert",text: text});
            openMessage(msgType.error, msg);
		}
		else if(result[sysCfg.ret] == "500")
		{
			openMessage(msgType.error, result[sysCfg.msg] );
		} else {
			openMessage(msgType.error);
		}
        clearOutSelect("auxOutDV"+window.accRTM.auxOutId);
        clearDoorSelect("doorDV"+window.accRTM.doorId);
	}
	var alreadySubmit = false;
	function openForm(data) {
		var html = "<div style='padding:10px;margin-${leftRTL!'left'}:30px;'>" +
					"<div><@i18n 'common_target'/>:&nbsp;&nbsp;" + data.opObj + "</div>" +
					"<form id='openForm" + uuid + "' action='" + itemId + "&name=" + encodeURIComponent(data.opObj) + "' method='post'>" +
					"<table style='padding-top: 8px;'><tr id='loginPwdRow' hidden='hidden'><th style='text-align: left;padding:5px 5px 5px 0px;'><@i18n 'auth_user_userPwd'/><span class='required'>*</span></th>" +
					"<td><input type='password' id='rtLoginPwd' name='rtLoginPwd' maxlength='50' autofocus='true' onchange='pwdChange()'/><input type='hidden' name='loginPwd' id='password_hidden'/></td></tr>"+
					"<tr id='openIntervalTr'><th style='text-align: left;padding: 5px 5px 5px 0px;;'><@i18n 'common_open'/>:</th><td><input type='text' name='openInterval' size='8' maxlength='3' value='5'/>" +
					"<@i18n 'common_second'/><span class='form_note'>(1-254)</span></td></tr>" +
					"</table></form>" +
					"</div>";
		var result = false;
		isNeedValidUserForExport(function(ret) {
			result = ret.ret === sysCfg.success;
			if(ret && ret.ret === sysCfg.success) {
				$("#loginPwdRow").hide();
			} else {
				if(itemId.indexOf("open") == -1) {
					$("#openIntervalTr").hide();
				}
				$("#loginPwdRow").show();
			}
		})
		$.jBox(html, {
			title: data.title,
			loaded: function() {
				if(result) {
					$("#rtLoginPwd").rules("remove");
					$("input[name=openInterval]").focus();
				} else {
					$("#rtLoginPwd").focus();
					$("#rtLoginPwd").rules("add", {required : true});
				}
			},
			submit: function (v, h, f) {
			    if (v == "ok")
			    {
			    	$("#openForm"+uuid).submit();
			    	if(event != null && typeof(event) != "undefined")
					{
						createEvent(event, remark);
					}
			        return false;
			    }
			}
		});

		$("#openForm"+uuid).validate({
			rules : {
				"openInterval" : {
					required : true,
					digits: true,
					range: [1, 254]
				}
			},
		 	submitHandler: function(form)
		   	{
		 		$(".jbox-button-focus").addClass("jbox-button-hover");
				if(alreadySubmit)
				{
					return;
				}
				alreadySubmit = true;
				$.jBox.close();
				onLoading(function(){
					var objIds = "";
					if(data.id)
		    		{
						objIds = data.id;
		    		}
		    		else
		    		{
                        objIds = getDoorIds();
						if(objIds == ""){
                            objIds = [];
                            for(var i = 0; i < rtDataViews[objId].dataCount(); i++)
                            {
                                objIds.push(rtDataViews[objId].idByIndex(i));
                            }
                            objIds = objIds.join();
						}
		    		}
					$(form).ajaxSubmit({
						async : true,
						dataType : 'json',
						data: {
		        			ids: objIds
						},
						success: function(result)
						{
							dealRetFun(result);
						}
					});
				});
		   	}
		 });

	}

	function openConfirm(data) {
		var html = "<div style='margin-top: -2px'>" +
						"<div><@i18n 'common_target'/>:" + data.opObj + "</div>" +
						"<div>" + "<@i18n 'common_prompt_executeOperate'/>".format(data.title) + "</div>" +
					"</div>";
		alreadySubmit = false;
		var akeyCode = 0;  //用于获取单击键盘的键值
		$.jBox.confirm(html, data.title, function (v, h, f) {
			if (v == "ok")
			{
				$(".jbox-button-focus").addClass("jbox-button-hover");
				if(alreadySubmit)
				{
					return;
				}
				alreadySubmit = true;
				$.jBox.close();
				onLoading(function(){
					$(".jbox-icon").addClass("jbox-rtBeforeIcon");
					var objIds = "";
					if(data.id)
					{
						objIds = data.id;
					} else {
					    objIds = getDoorIds()
						if(objIds == ""){
                            objIds = [];
                            for(var i = 0; i < rtDataViews[objId].dataCount(); i++) {
                                objIds.push(rtDataViews[objId].idByIndex(i));
                            }
                            objIds = objIds.join();
						}
					}
			    	$.ajax({
						type: "POST",
						url: itemId,
						async : true,
						data: {
			    			ids: objIds,
							name: data.opObj
						},
						success: function(result)
					    {
			    			dealRetFun(result);
						}
			        });
				});
			    return false;
			}
			return true;
		} );
		$(".jbox-content").children().addClass("jbox-rtContent");
		$(".jbox-icon").addClass("jbox-rtIcon");
	}

	window.setTimeout(function () {
		isNeedValidUserForExport(function(ret) {
			if(ret && ret.ret === sysCfg.success && itemId.indexOf("open") == -1) {
				openConfirm(data);
			} else {
				openForm(data);
			}
		})
	}, 10);
	function keyDownForEnter(e)
	{
		akeyCode = e.keyCode;
		if($(".jbox-button-focus") && akeyCode == '13' && alreadySubmit == false)
		{
			$("#password_hidden").val(hex_md5($("#rtLoginPwd").val()));
			$(".jbox-button-focus").click();
		}
	}
	 //leo 解决实时监控->远程关门，弹出的提示框，按回车键不会进入下一步(浏览器focus兼容性问题)
	document.onkeydown = keyDownForEnter;
	$(".jbox-button-focus").css("border", "1px solid #888888");
	$(".jbox-button-focus").focus();
}

//点击抓拍图片切换大图
function changeImg(img)
{
	var imgSrc = $(img).attr("src");
	$(img).parent().parent().find("#bigImg").attr("src", imgSrc);
}

function createEvent(event, remark)
{
	$.ajax({
		type: "GET",
		url: "accGlobalLinkage.do?addLogAfterVidLinkage",
		data: {"vidLinkageEvent" : JSON.stringify(event), "remark" : remark},
		dataType: "json",
		async: true
	});
}

//清空行
function clearEventRow(gridName, uuid)
{
	var dhxGrid = ZKUI.Grid.get(gridName).grid;
	dhxGrid.clearAll();
	dhxGrid._f_rowsBuffer = [];
	$("#totalEventCount"+uuid).html(0);
	$("#normalEventCount"+uuid).html(0);
	$("#warningEventCount"+uuid).html(0);
	$("#alarmEventCount"+uuid).html(0);
}

function setEventFooterValues(rowNum, normalNum, warningNum, alarmNum, uuid) {
	var totalCount = parseInt($("#totalEventCount"+uuid).html());
	$("#totalEventCount"+uuid).html(totalCount + (rowNum ? rowNum : 0));
	var normalCount = parseInt($("#normalEventCount"+uuid).html());
	$("#normalEventCount"+uuid).html(normalCount + (normalNum ? normalNum : 0));

	var warningCount = parseInt($("#warningEventCount"+uuid).html());
	$("#warningEventCount"+uuid).html(warningCount + (warningNum ? warningNum : 0));

	var alarmCount = parseInt($("#alarmEventCount"+uuid).html());
	$("#alarmEventCount"+uuid).html(alarmCount + (alarmNum ? alarmNum : 0));
}


//显示浮动信息
function showPopUpInfo(obj)
{
	var offsetTop = $(obj).offset().top + $(obj).height()/1.5 - $(document).scrollTop();
	if($(document.body).height() - offsetTop < $(obj).children(".dataViewOpItem").height())
	{
		offsetTop = $(obj).offset().top - ($(document).scrollTop() + $(obj).children(".dataViewOpItem").height() + 10) + $(obj).height()/2.5;
	}
	var offsetLeft = $(obj).offset().left + $(obj).width()/1.5 - $(document).scrollLeft();
	if($(window).width() - offsetLeft < $(obj).children(".dataViewOpItem").width())
	{
		offsetLeft = $(obj).offset().left - ($(document).scrollLeft() + $(obj).children(".dataViewOpItem").width() + 10) + $(obj).width()/2.5;
	}
	$(obj).children("div").css("top", offsetTop);
	$(obj).children("div").css("left", offsetLeft);
	$("body div.dataViewOpItem:first-child").remove();
	$("body").prepend('<div class="dataViewOpItem" onmouseover="showFlag=true" onmouseout="showFlag=false;accRTMonitorHidePopUpInfo()" style="position: fixed;top: '+ offsetTop +'px;left: '+ offsetLeft +'px;z-index: 9999999;" align="${leftRTL!'left'}">'+ $(obj).children(".dataViewOpItem").html() + '</div>');
	<#if enableRTL?? && enableRTL=="true">
		if (offsetLeft - $("body div.dataViewOpItem:first-child").width() >= 0) {
            $("body div.dataViewOpItem:first-child").css("left", offsetLeft-$("body div.dataViewOpItem:first-child").width());
		}
	</#if>
	showFlag = true;
}

//进行条件过滤
function dataViewFilter(obj, gridName)
{
	ZKUI.Grid.get(gridName).grid.filterBy(4,obj.value);
}

//隐藏浮动信息
function accRTMonitorHidePopUpInfo(obj)
{
	showFlag = false;
	window.setTimeout(function(){
		if(!showFlag)
		{
			$("body").children("div.dataViewOpItem:first").remove();
		}
	}, 30);
}

function enableAudio(obj, uuid)
{
	createCookie("accRTEventAudio${(user.id)!}", obj.checked);
	if(obj.checked)//选中时则暂停
	{
		openMessage(msgType.success, "<@i18n 'acc_rtMonitor_alarmSoundOpen'/>");//运行消息提示
	}
	else
	{
		openMessage(msgType.warning, "<@i18n 'acc_rtMonitor_alarmSoundClose'/>");//关闭消息提示
		closeAudio("alert_sound${uuid!}");
		//关闭门声音
		closeDoorSound();
	}
}

$("#audioCheckbox${uuid!}").click(function() {
	enableAudio(this);
});

function closeDoorSound()
{
	var doorAlarmAudio = $("audio[name='alarmAudio']");
	if(doorAlarmAudio != "undefined")
	{
		for(var i = 0; i < doorAlarmAudio.length; i++)
		{
			removeObj(doorAlarmAudio[i]);
		}
	}
	doorAlarmAudio = $("bgsound[name='alarmSound']");
	if(doorAlarmAudio != "undefined")
	{
		for(var i = 0; i < doorAlarmAudio.length; i++)
		{
			removeObj(doorAlarmAudio[i]);
		}
	}
}

//移除对象，解决IE不支持remove方法
function removeObj(obj)
{
	var parentObj = obj.parentNode;
	if(parentObj)
	{
		parentObj.removeChild(obj);
	}
}

function redirectToAlarmPage() {
	openSystemTab("accAlarm.do", "<@i18n 'acc_rtMonitor_alarm'/>")
}

startMonitor();

function checkAuthArea(nowArea, filerArea) {
    if(filerArea == "" || filerArea.indexOf(nowArea) >= 0) {
        return "true";
	}
	return "false";
}

var doorIds = new Array();

var auxOutIds = new Array();

/**
 * 点击之后加入id列表
 * @param e
 * @param ob
 */
function clickCenter(event,ob,type) {
    var ids;
    var divID;
    var ob = $(ob).parent()[0];
    var id = $(ob).attr("dhx_f_id");
	if(type=="auxOut"){
        auxOutIds = addIdOrRemove(id,auxOutIds);
        ids = auxOutIds;
        divID = window.accRTM==undefined?"":window.accRTM.auxOutId;
        divID = "auxOutDV"+divID //
	}else {
        doorIds = addIdOrRemove(id,doorIds);
        ids = doorIds;
        divID = window.accRTM==undefined?"":window.accRTM.doorId;
        divID = "doorDV"+divID
	}
    //列表数据加框
    changeDoorSelect(ids,divID);
    var e = event || window.event;
    stopBubble(e);
}

/**
 * id在ids中则删除，否则加入
 */
function addIdOrRemove(id,ids) {
    var hasVal = false;
    for(let index = 0; index < ids.length; index++){
        if(ids[index]==id){
            ids.splice(index, 1);
            hasVal = true;
        }
    }
    if(!hasVal){
        ids.push(id)
    }
    return ids;
}

/**
 * 阻止事件冒泡，兼容IE
 * @param e
 */
function stopBubble(e) {
    //如果提供了事件对象，则这是一个非IE浏览器
    if ( e && e.stopPropagation )
    //因此它支持W3C的stopPropagation()方法
        e.stopPropagation();
    else
    //否则，我们需要使用IE的方式来取消事件冒泡
        window.event.cancelBubble = true;
}

/**
 * 将数组中的id对应的数据加上边框
 * @param arr
 */
function changeDoorSelect(arr,divId) {
	var divSelect = "#"+divId+" div[align='center']";
	$(divSelect).removeClass("selectDiv");
	for (let i = 0; i < arr.length; i++) {
		var borderDiv = $("#"+divId+" div[dhx_f_id='"+arr[i]+"']");
		var centerDiv = borderDiv.children(":first");
		if(centerDiv[0]){
            centerDiv[0].setAttribute("class",centerDiv[0].getAttribute("class")+" selectDiv");
		}
	}
}

/**
 * 自动判断当前是那个页面，并设置相应的选中框
 */
function autoChangeDoor() {
    var divId;
    var ids;
    var obs = $('.dhx_cell_tabbar');
    var i;
    for (i = 0; i < obs.length; i++) {
        var bool = $(obs[i]).css("visibility") == "hidden"
        if(!bool){
            break;
        }
    }
    if(i==2){
        ids = auxOutIds;
        divId = window.accRTM==undefined?"":window.accRTM.auxOutId;
        divId = "auxOutDV"+divId;
    }else {
        ids = doorIds;
        divId = window.accRTM==undefined?"":window.accRTM.doorId;
        divId = "doorDV"+divId;
    }
    changeDoorSelect(ids,divId);
}



/**
 * 获取id
 * @returns {*}
 */
function getDoorIds() {
    var ids;
	var obs = $('.dhx_cell_tabbar');
	var i;
    for (i = 0; i < obs.length; i++) {
        var bool = $(obs[i]).css("visibility") == "hidden"
		if(!bool){
		   break;
		}
    }
    if(i==2){
        ids = auxOutIds;
	}else {
        ids = doorIds;
	}
	if(ids.length>=1){
		var id = ids[0];
		for (let i = 1; i < ids.length; i++) {
			id = id+","+ids[i];
		}
		return id;
	}
	return ""
}

/**
 * 取消所有选中
 */
function clearDoorSelect(divId) {
    if(divId){
        var divSelect = "#"+divId+" div[align='center']";
        $(divSelect).removeClass("selectDiv");
        doorIds.length = 0;
        changeDoorSelect(doorIds,divId);
	}
}

function clearOutSelect(divId) {
    if(divId){
        var divSelect = "#"+divId+" div[align='center']";
        $(divSelect).removeClass("selectDiv");
        auxOutIds.length = 0;
        changeDoorSelect(doorIds,divId);
	}
}
var alarmEventShowDiv = document.createElement("div");
alarmEventShowDiv.setAttribute("id", "accAlarmEventShowDiv${uuid}");
alarmEventShowDiv.setAttribute("class", "accAlarmEventShowDiv zk-border-color");
alarmEventShowDiv.innerHTML="<div class= 'accRTMAlarmTitle'><div><label style='font-size:15px'><@i18n 'acc_rtMonitor_alarmEvent' /></label></div><div title='<@i18n 'common_op_close'/>' class='icv-close accRTMCancelAlarmEvent' onclick='accRTMHideAlarmEvent()'></div></div>";
document.body.appendChild(alarmEventShowDiv);
var accRTMAlarmEventTable = document.createElement("table");
accRTMAlarmEventTable.innerHTML ="<table style='width:190px;'><tr></tr></table>";
alarmEventShowDiv.append(accRTMAlarmEventTable);
var textColors = {
				"default": '#000',
				"lightgreen":'#000',
				"techblue" : '#cbe1f1'
			}
var skin = getSystemSkinAcc();
var textColor = textColors[skin];
function accRTMShowAlarmEvent() {
	$(".accAlarmEventShowDiv").show();
}
function accRTMHideAlarmEvent() {
	$(".accAlarmEventShowDiv").hide();
	clearInterval(remindAlarmEventInterval);
	var text = document.getElementById('remindAlarmEvent');
    text.style.color = textColor;
}

function flash() {
    var text = document.getElementById('remindAlarmEvent');
    text.style.color = (text.style.color=='red') ? textColor :'red';
}
var interval;  //定时器的变量
var remindAlarmEventInterval;
var i = 0;
function pushAlarmEventToDiv(id, uuid)
{
	var gridName = $("#acc_rtm_box"+uuid).attr("gridName");
	var dhxGrid = ZKUI.Grid.get(gridName).grid;
	var photoBase64 = dhxGrid.getUserData(id, "photoBase64");
	var personPinName = dhxGrid.cells(id, dhxGrid.getColIndexById("pinName")).getValue();
	var eventTime = dhxGrid.cells(id, dhxGrid.getColIndexById("eventTime")).getValue();
	var eventName = dhxGrid.cells(id, dhxGrid.getColIndexById("eventName")).getValue();
	var eventLevelText = convertAccEventLevelToText(dhxGrid.getUserData(id, "eventLevel"));
	var eventPointName = dhxGrid.cells(id, dhxGrid.getColIndexById("eventPointName")).getValue();
	var nowTime = new Date().getTime();
	var alarmEventInfo = document.createElement("tr");
	alarmEventInfo.setAttribute("id", "accAlarm"+i);
	alarmEventInfo.innerHTML = "<td>" + personPinName + "</td><td>" + eventTime + "</td><td style='color:red'>" + eventName + "</td><td>" +eventPointName+ "</td>" +
								"<td><div class='icv-ic_del' style='cursor: pointer' title=<@i18n 'common_op_del'/> src='images/del.png' onclick='delAccAlarmEvent(this, "+i+")' /></td>";
	$("#accAlarmEventShowDiv${uuid}").append(alarmEventInfo);
	remindAlarmEventInterval = setInterval(flash, 1000);
}
function delAccAlarmEvent(element, eventIndex) {
	$("#accAlarm" + eventIndex).remove();
}

/**
 * 初始化按钮状态
 */
$(function(){
    var audioCheck = getCookie("accRTEventAudio${(user.id)!}");
    var msgCheck = getCookie("accRTEventMsg${(user.id)!}");
    if(audioCheck!=null){
        if(!audioCheck||audioCheck=="false"){
            $("#audioCheckbox${uuid!}").removeAttr("checked");
		} else {
			$("#audioCheckbox${uuid!}").attr("checked", "checked");
		}
    }
    if(msgCheck!=null){
        if(!msgCheck||msgCheck=="false"){
            $("#msgCheckbox${uuid!}").removeAttr("checked")
		} else {
			$("#msgCheckbox${uuid!}").attr("checked", "checked");
		}
    }
})
</script>
<#if systemModules?lower_case?index_of("vid") != -1>
	<link rel="stylesheet" type="text/css" href="/css/vidPopUpPreview.css?un=${sysTimeMillis}" />
	<script type="text/javascript" src="/js/vidPreview.js" charset="UTF-8"></script>
	<script type="text/javascript" src="/js/vidPopUpPreview.js" charset="UTF-8"></script>
<#elseif systemModules?lower_case?index_of("vms") != -1>
	<script type="text/javascript" src="/js/vmsPreview.js" charset="UTF-8"></script>
	<script type="text/javascript" src="/js/vmsPopUpPreview.js" charset="UTF-8"></script>
<#elseif systemModules?lower_case?index_of("ivs") != -1>
	<script type="text/javascript" src="/js/ivsPopUpVideo.js" charset="UTF-8"></script>
</#if>