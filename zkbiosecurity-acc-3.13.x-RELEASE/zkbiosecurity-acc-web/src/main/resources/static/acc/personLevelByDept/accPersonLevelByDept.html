<#assign gridName="accPersonLevelByDeptGrid${uuid!}">
<@ZKUI.DGrid gridName="${gridName}">
	<@ZKUI.LeftGrid title="common_level_editLevelDept" width="57%">
		<@ZKUI.Searchbar>
			<@ZKUI.SearchTop>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="code"  maxlength="30" title="base_department_deptNo" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Input name="name"  maxlength="30" title="base_department_deptName" type="text"/>
					</td>
				</tr>
			</@ZKUI.SearchTop>
		</@ZKUI.Searchbar>
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="acc:personLevelByDept:refresh"></@ZKUI.ToolItem>
		</@ZKUI.Toolbar>
		<@ZKUI.Grid onRowSelect="accPersonLevelByDeptLeftGridClick" vo="com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelByDeptItem" query="/accPersonLevelByDept.do?list" autoFirst="true"/>
	</@ZKUI.LeftGrid>
	<@ZKUI.RightGrid callback="accPersonLevelByDeptRightCallback" leftFieldName="deptId">
		<@ZKUI.Searchbar>
			<@ZKUI.SearchTop>
				<tr>
					<td valign="middle">
						<@ZKUI.Input name="name"  maxlength="30" title="common_level_name" type="text"/>
					</td>
					<td valign="middle">
						<@ZKUI.Input name="timeSegName"  maxlength="30" title="acc_timeSeg_entity" type="text"/>
					</td>
				</tr>
			</@ZKUI.SearchTop>
		</@ZKUI.Searchbar>
		<@ZKUI.Toolbar>
			<@ZKUI.ToolItem type="refresh" permission="acc:personLevelByDept:refresh"></@ZKUI.ToolItem>
			<@ZKUI.ToolItem id="/accPersonLevelByDept.do?delLevel" text="common_level_delDefaultLevel" img="comm_del.png" action="delDefaultLevel" permission="acc:personLevelByDept:delLevel"></@ZKUI.ToolItem>
		</@ZKUI.Toolbar>
		<@ZKUI.Grid vo="com.zkteco.zkbiosecurity.acc.vo.AccDeptLevelItem" query="/accPersonLevelByDept.do?getDeptLevel"/>
	</@ZKUI.RightGrid>
</@ZKUI.DGrid>
<script type='text/javascript'>

    function accPersonLevelByDeptLeftGridClick(rid){
        var leftGrid = this.zkgrid;//获取左表格
        var dbGrid = ZKUI.DGrid.get(leftGrid.options.dGridName);//双列表对象
        var rightGrid = dbGrid.rightGrid;//右表格对象
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        rightGrid.reload(function() {
            accPersonLevelByDeptRightCallback(row.id, rightGrid, rid);//手动回调
        }, {deptId : rid});
    }

    //右表格回调事件
    function accPersonLevelByDeptRightCallback(id, rightGrid, rid) {
        var dbGrid = ZKUI.DGrid.get(rightGrid.options.dGridName);//双列表对象
        var leftGrid = dbGrid.leftGrid;//获取左表格
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        rightGrid.setTitle(I18n.getValue("common_level_browseDept") + " " + row.code + "（" + row.name + "）" + I18n.getValue("common_level_defaultLevel"));//设置右表格标题
    }

    function afterAddDeptLevel(value, text, event) {
        var deptId = this.options.deptId;
        var deptCode = this.options.deptCode;
        DhxCommon.closeWindow();
        openDeptAddLevelProcess(value, text, deptId, deptCode);
        return false;
    }

    function openDeptAddLevelProcess(value, text, deptId, deptCode) {
        var personCount = accGetPersonCountByDeptIds(deptId);
        if(parseInt(personCount) > 0)
        {
            messageBox({
                messageType : "confirm",
                text : "<@i18n 'common_level_immeUpdateLevel'/>",
                callback : function(result)
                {
                    if (result)
                    {
                        accPersonLevelByDeptProgress(value, text, deptId, deptCode);
                    }
                    else
                    {
                        accPersonLevelByDeptWithoutProgress(value, text, deptId, deptCode);
                    }
                }
            });
        }
        else
        {
            accPersonLevelByDeptWithoutProgress(value, text, deptId, deptCode);
        }
    }

    function accPersonLevelByDeptfinishProcess()
	{
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        var rightGrid = dbGrid.rightGrid;//右表格对象
        rightGrid.reload();
	}
    
    function accPersonLevelByDeptProgress(value, text, deptId, deptCode) {
        var opts = {
            dealPath: "/accPersonLevelByDept.do?addLevel",//进度处理请求
            type: "single",//进度类型，取值single,public,custom
            height:250,//弹窗高度
            useReq:true,//使用请求参数作为进度条参数
            autoClose:true,//自动关闭
            delay:5,//自动关闭时间(5秒后自动关闭)
            onCallback:"accPersonLevelByDeptfinishProcess",//处理完后回调
            title:"<@i18n 'common_progress_proCmd'/>", //弹窗标题
			data:{
                //deptId="+deptId+"&deptCode="+deptCode+"&levelIds="+ value + "&levelNames=" + encodeURI(text) + "&immeUpdate=true"
                deptId : deptId,
                deptCode : deptCode,
                levelIds : value,
                levelNames : text,
                immeUpdate : true
			}
        }
        openProcess(opts);
    }

    function accPersonLevelByDeptWithoutProgress(value, text, deptId, deptCode) {
        onLoading(function(){
            $.ajax({
                type: "POST",
                dataType: "json",
                url: "/accPersonLevelByDept.do?addLevel",
                data: {
                    "deptId" : deptId,
                    "deptCode" : deptCode,
                    "levelIds" : value,
                    "levelNames" : text
                },
                success: function (data)
                {
                    if (data.ret == "ok")
                    {
                        openMessage(msgType.success);
                        var dbGrid = ZKUI.DGrid.get("${gridName}");
                        var rightGrid = dbGrid.rightGrid;
                        rightGrid.reload();
                    }
                }
            });
        });
    }

    function delDefaultLevel(id,bar)
    {
        if(bar) {
            var gridName = bar.gridName || "gridbox";
            var rightIds = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
            gridName = $.trim(gridName.replace("right",""));
            var leftGrid = ZKUI.DGrid.get(gridName).leftGrid;//刷新左侧列表
            var rightGrid = ZKUI.DGrid.get(gridName).rightGrid;//刷新右侧列表
            var leftIds = leftGrid.grid.getSelectedRowId();
            if(leftIds == "" || leftIds == null || rightIds == "" || rightIds== null ) {
                messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});
            } else {
                var deptCode = leftGrid.grid.getRowData(leftIds).deptCode;
                var name = "";
                var ids = rightIds.split(",");
                for(var i in ids)
                {
                    var data = rightGrid.grid.getRowData(ids[i]);
                    name += data.name + ",";
                }
                name = name.substring(0, name.length - 1);
                deleteConfirm(function(result) {
                    if(result) {
                        var personCount = accGetPersonCountByDeptIds(leftIds);
                        var immeUpdate = false;
                        if(parseInt(personCount) > 0)
                        {
                            messageBox({
                                messageType : "confirm",
                                text : "<@i18n 'common_level_immeUpdateLevel'/>",
                                callback : function(result)
                                {
                                    if (result) {
                                        immeUpdate = true;
                                    }
                                    var opts = {
                                        dealPath: id,//进度处理请求
                                        type: "single",//进度类型，取值single,public,custom
                                        height:250,//弹窗高度
                                        useReq:true,//使用请求参数作为进度条参数
                                        autoClose:true,//自动关闭
                                        delay:5,//自动关闭时间(5秒后自动关闭)
                                        onCallback:"accPersonLevelByDeptfinishProcess",//处理完后回调
                                        title:"<@i18n 'common_progress_proCmd'/>", //弹窗标题
                                        data:{
                                            "leftIds" : leftIds,
                                            "rightIds" : rightIds,
                                            "immeUpdate" : immeUpdate,
                                            "deptCode" : deptCode,
                                            "levelName" : name
                                        }
                                    }
                                    openProcess(opts);
                                }
                            });
                        }
                        else{
                            onLoading(function(){
                                $.ajax({
                                    url:id,
                                    type:"post",
                                    async: false,
                                    data: {
                                        "leftIds" : leftIds,
                                        "rightIds" : rightIds,
                                        "immeUpdate" : immeUpdate,
										"deptCode" : deptCode,
                                        "levelName" : name
                                    },
                                    success:function(result) {
                                        dealRetResult(eval(result),function() {
                                            rightGrid.reload();//刷新右侧列表
                                        });
                                    }
                                });
                            });
                        }
                    }
                }, "<@i18n 'common_prompt_sureToDelThese'/>");
            }
        }
    }
</script>