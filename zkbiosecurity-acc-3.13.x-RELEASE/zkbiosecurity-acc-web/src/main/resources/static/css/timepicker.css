.hidden { display: none; }

div#time-picker-wrap {
    border: 2px solid #efefFF;
    color: #000000;
    font-size:12px;
    padding: 2px;
    margin: 0;
    background: #fff;
    position: absolute;
    text-align: center;
}

#time-picker-wrap table tr, #time-picker-wrap table td {
    font-size: 18px;
    vertical-align: middle;
    text-align: center;
}

#time-picker-wrap div.scoreboard {
    cursor: default;
    margin: 2px 6px;
    position: inherit;
}

#time-picker-wrap .scoreboard .increase-button span,
#time-picker-wrap .scoreboard .decrease-button span { 
    cursor: pointer;
    display: block;
    height: 16px;
    width: 24px;
    margin: 0 auto;
}
#time-picker-wrap .scoreboard .increase-button span {
    background: url("../images/increase.png") no-repeat;
}
#time-picker-wrap .scoreboard .decrease-button span {
    background: url("../images/decrease.png") no-repeat;
}

#time-picker-wrap .scoreboard .score span {
    font-family: monospace;
    display: block;
    padding: 0px 0px 0px 0px;
    font-size:13px;
}

#time-picker-wrap .time-suggest-wrap td {
    font-size: 12px;
    cursor: pointer;
    padding: 1px;
}

#time-picker-wrap .clear-wrap, #time-picker-wrap .close-wrap {
    font-size: 12px;
    text-align: center;
    padding: 2px 0;
    cursor: pointer;
    border: 1px solid #EEEFFF;
    background-color:#EEEFFF;
}

#time-picker-wrap .time-suggest-wrap td:hover,
#time-picker-wrap .clear-wrap:hover, #time-picker-wrap .close-wrap:hover {
    background: #77bbff;
    color: #fff;
    font-weight: bold;
}
