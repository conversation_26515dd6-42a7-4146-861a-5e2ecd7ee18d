.accLcdRTBody {
    position: relative;
    margin: 0;
    background: #eaeff4;
    height: 100%;
    width: 100%;
}

.accLcdRTBody-head {
    height: 8%;
    display: flex;
    align-items: center;
    background: #fff;
}

.accLcdRTBody-rt-info {
    box-sizing: border-box;
    padding: 20px;
    height: 50%;
    min-height: 350px;
}

.accLcdRTBody-info {
    height: 42%;
    min-height: 300px;
    box-sizing: border-box;
    padding: 0px 20px 20px;
}

.acc_lcdDhxcelltop_hdr {
    position: relative;
    width: 100%;
    background: #fff;
}

.accLcdRTRow {
    height: 100%;
    width: 100%;
    border-radius: 8px;
    padding: 10px 20px;
    background-color: #fff;
    box-sizing: border-box;
}

.accLcdRTHeadLogo {
    position: absolute;
    top: 50%;
    transform: translate(0%, -50%);
}

.accLcdRTHeadTitle {
    width: 100%;
    text-align: center;
}
.accLcdRTHeadTitleContent {
    font-size: 200%;
    font-family: Arial, 微软雅黑;
}

.titleDiv_lcd {
	width: 100%;
	margin-top:10px;
}

.title_lcd {
	font-size: 20px;
	font-family: Arial, 微软雅黑;
	height: 10%;
}

#fullScreenImg_lcd {
	position: absolute;
	right: 15px;
	cursor: pointer;
}
.currentInfo_lcd {
    height: 90%;
    width: 100%;
    display: flex;

}
#visCurrentInfo_lcd {
    flex: 2;
	height: 100%;
	padding-left: 20px;
}

.allDateInfo_lcd {
    color: #000000;
	flex: 1;
    height: 100%;
    display: flex;
    flex-flow: column;
    text-align: right;
    justify-content: center;
}

#weekInfo_lcd {
	margin-right:30px;
}

.dateInfo_lcd {
	font-size: 2.5vw;
	font-family: Arial, 微软雅黑;
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

#visInfoAll_lcd {
	width: 100%;
	height: 100%;
	margin-top: 0.5%;
	text-align: top;
}

.visCurrentTitleP_lcd {
	margin-left: 30px;
	font-size: 1.3vw;
	width: 100px;
}

#person_info_vis_lcd {
	width: 100%;
	height: 90%;
	margin-left: 1%;
}

.visPersPhoto_lcd {
	display: inline-block;
	margin-top: 1%;
	margin-left: 3%;
	vertical-align: top;
	box-sizing: border-box;
	-moz-box-sizing: border-box; /* Firefox */
	-webkit-box-sizing: border-box; /* Safari */
}

.visInfo_lcd {
	min-height: 80%;
	margin-left: 3%;
	display: inline-block;
	box-sizing: border-box;
	-moz-box-sizing: border-box; /* Firefox */
	-webkit-box-sizing: border-box; /* Safari */
}
.visInfo_lcd td {
	vertical-align: top;
}

.visCurrentText_lcd {
	margin-left: 10px;
	width: 300px;
	min-width: 340px;
	font-size: 25px;
	border-bottom: 1px solid gray;
	word-wrap: break-word;
	word-break: break-all;
}

.persPhoto_lcd {
	width: 40%;
	height: 50%;
	display: inline;
	text-align: center;
}

.persInfo_lcd {
	width: 58%;
	height: 50%;
	display: inline;
	text-align: center;
}
.lastPersInfo_lcd {
	margin-left:11%;
}
/* 全屏样式 */
.visCurrentTitlePFull_lcd {
	width: 150px;
	margin: 5px auto;
	font-size: 20px;
	text-align: left;
}

.visPersPhotoFull_lcd {
	width: 24.8%;
	height: 24.5%;
	margin-top: 2.5%;
	margin-left: 5%;
	display: inline-block;
	vertical-align: top;
	box-sizing: border-box;
	-moz-box-sizing: border-box; /* Firefox */
	-webkit-box-sizing: border-box; /* Safari */
}

.visInfoFull_lcd {
	min-height: 24.8%;
	margin-top: 1.5%;
	margin-left: 5%;
	font-family: Arial, 微软雅黑;
	display: inline-block;
	box-sizing: border-box;
	-moz-box-sizing: border-box; /* Firefox */
	-webkit-box-sizing: border-box; /* Safari */
}

.lastInfo_lcd {
	margin: 1%;
	width: 50%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	text-align: center;
	height: 21%;
	font-size: 30px;
}

.lastInfoDiv_lcd {
	margin-left:15%;
}
.accLcdRTTimeInfo {
    font-size: 8vw;
    font-family: Arial, 微软雅黑;
}

#visCurrentInfo_lcd .content {
    padding: 20px;
    display: flex;
    border: 2px solid #7AC143;
    box-sizing: border-box;
    box-shadow: 0px 0px 30px #7ac143 inset;
    height: 100%;
    background-image: url("../images/lcd_left_top.png"),
                url("../images/lcd_right_bottom.png"),
                url("../images/lcd_left_bottom.png"),
                url("../images/lcd_right_top.png");
    background-position: top left, top right, bottom left, bottom right;
    background-repeat: no-repeat, no-repeat, no-repeat, no-repeat;
}

#visCurrentInfo_lcd .content .lcdCurrentImage {
    max-width: 40%;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    flex-direction: column;
}

#visCurrentInfo_lcd .content .lcdCurrentPersInfo {
    width: 60%;
    padding: 0px 20px;
    box-sizing: border-box;
}

.lcdCurrentTable {
    border-collapse:separate;
    height: 100%;
}

.lcdCurrentTable th {
    font-size:1.3vw;
    text-align: left;
    font-weight: 500;
    min-width: 205px;
}
.lcdCurrentTable td {
    padding-left: 15px;
    min-width: 340px;
    font-size: 1.3vw;
    font-weight: 500;
    word-wrap: break-word;
    word-break: break-all;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.lcdPersPhoto {
    width: 100%;
    max-height: 140px;
    overflow: hidden;
}

.accLcdRTInfoAll {
    height: 90%;
    width: 100%;
}
.lcdPersonInfoItems {
    height: 100%;
    display: flex;
}

.lcdPersonInfoItem {
    height: 90%;
    width: 18%;
    border: 1px solid #EAEFF4;
    border-radius: 5px;
    margin-left: 17px;
    flex-direction: column;
    justify-content: center;
}

.lcdPersonInfoItem .lcdPersImg {
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 5px 0;
}
.lcdPersonInfoItem .lcdPersImg .lcdImgContent {
    width: 40%;
}
.lcdPersonInfoItem .lcdLastPersonInfo {
    text-align: center;
    width: 100%;
    font-size: 16px;
}

.lcdPersPhotoEx {
    height: 100%;
    width:100%;
}

.accMusterPointReportMain {
    width: 100%;
    height: 100%;
    display: flex;
}
.accMusterPointReportMain .accMusterPointReportLeft {
    flex: 2.5;
    height: 100%;
    display: inline-block;
    background-color: #fff;
}
.accMusterPointReportMain .accMusterPointReportRight {
    flex: 7.5;
    height: 100%;
    display: flex;
    flex-direction:column;
    border-radius: 5px;
}

.accMusterPointReportLeft .accMPRLHead {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 0px 5px;
    height: 24px;
}
.accMPRLHead .mprl-headTitle {
    display: flex;
    align-items: center;
}

.accMPRLHead .mprl-refresh {
   display: flex;
   align-items: center;
   cursor: pointer;
}
.accMusterPointReportLeft .accMPRLMid {
    display : flex;
}
.accMPRLMid .mprl-point {
}
.accMPRLMid .mprl-generate {
    cursor: pointer;
    background-image: url("../public/images/acc_musterPointReport_generate.png");
    width: 20px;
    height: 20px;
    background-repeat: no-repeat;
    margin-top: 3px;
    margin-left: 10px;
}

.accMPRLMid .mprl-generate:hover{
    cursor: pointer;
    background-image: url("../public/images/acc_musterPointReport_generate_over.png");
    width: 20px;
    height: 20px;
    background-repeat: no-repeat;
    margin-top: 3px;
    margin-left: 10px;
}
.accMusterPointReport-echart {
    height: 300px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.accMusterPointReportLeft .musterPointReportLegend {
    display: flex;
    width: 100%;
    align-items: end;
}
.accMusterPointReportLeft .musterPointReportLegend .musterPointReportContent {
    width: 100%;
    display: inline-block;
    box-sizing: border-box;
    text-align: left;
}

.accMusterPointReportLeft .musterPointReportLegend .musterPointReportContent .musterPointReportRow {
    display: flex;
    height: 45px;
    align-items: center;
    text-align: center;
}

.accMusterPointReportLeft .musterPointReportLegend .musterPointReportContent .musterPointReportRow + .musterPointReportRow {
    margin-top: 15px;
}
.accMusterPointReportLeft .musterPointReportLegend .musterPointReportContent .musterPointReportItem {
    flex: 1;
    font-size: 15px;
}
.accMusterPointReportLeft .musterPointReportLegend .musterPointReportContent .musterPointReportItem .musterPointTextAlign{
    text-align: center;
}
.acc-color-org {
    color: #FC5F21;
}
.acc-color-yellow {
    color: #FFC600;
}
.acc-color-green {
    color: #7AC143;
}
.accMusterPointReportLeft .font-big {
    font-size: 22px;
    font-weight: bold;
}

.accNoTip {
    padding-right: 28px !important;
}