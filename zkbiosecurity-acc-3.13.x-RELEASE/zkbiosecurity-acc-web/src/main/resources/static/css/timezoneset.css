@charset "utf-8";
/*timeseg edit start*/
.timeseg_edit_table #id_remark{width:500px !important;}
.timeseg_edit_table th{width:117px !important;}
.timeseg_edit_table_inner input{width:20px;}
.timeseg_edit_table_inner{text-align:center; border:1px solid #ccc;}
.timeseg_edit_table_inner td{border:1px solid #E8ECF7;}/*#ccc*/
.timeseg_edit_table_inner tr td{colspan:1;}
.blue_bgcolor{background-color:#aeb4b8;color:#fff;}
.blank_bgcolor{background-color:#f7f7f7; width:115px;}
.out{
    border-top:40px solid #aeb4b8;/*上边框宽度等于表格第一行行高*/
    width:0px;/*让容器宽度为0*/
    height:0px;/*让容器高度为0*/
    border-left:115px solid #ffffff;/*左边框宽度等于表格第一行第一格宽度*/       
    position:relative;/*让里面的两个子容器绝对定位*/
}
.out b{
    font-style:normal;
    display:block;
    position:absolute;
    top:-35px;
    left:-50px;
    width:35px;
    font-weight:bold;
}
.out em{
    font-style:normal;
    display:block;
    position:absolute;
    top:-20px;
    left:-100px;
    width:55x;
    font-weight:bold;
}

/*timeseg edit end*/