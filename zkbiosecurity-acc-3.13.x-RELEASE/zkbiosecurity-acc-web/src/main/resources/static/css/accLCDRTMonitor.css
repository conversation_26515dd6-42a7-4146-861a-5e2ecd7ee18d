.accLcdRTBody {
    margin: 0 0 0 0;
    background: #F3F5F0;
    height: 100%;
    position: relative;
    top:0px;
    bottom: 0px;
}
.acc_lcdDhxcelltop_hdr {
    line-height: 64px;
    height: 64px;
    width: 100%;
    background: #7AC143;
}
.accLcdRTHeadLogo {
    width: 15%;
    float: left;
    position: absolute;
    text-align: center;
}
.accLcdRTHeadLogoImg {
    border:0;
    margin: 0 auto;
}
.accLcdRTHeadTitle {
    width: 100%;
    float: left;
    text-align: center;
}
.accLcdRTHeadTitleContent {
    color: #FFFFFF;
    font-size: 200%;
    font-family: Arial, 微软雅黑;
}
.titleDiv_lcd {
	background: #DCDFE2;
	width: 100%;
	min-width: 1150px;
	margin-top:10px;
}

.title_lcd {
	font-size: 25px;
	font-family: Arial, 微软雅黑;
	margin-left: 55px;
}

#fullScreenImg_lcd {
	position: absolute;
	right: 15px;
	cursor: pointer;
}

.currentInfo_lcd {
	min-height: 350px;
	min-width: 1150px;
}

#visCurrentInfo_lcd {
	width: 62%;
	height: 100%;
	min-width: 650px;
	display: inline-block;
	box-sizing: border-box;
	-moz-box-sizing: border-box; /* Firefox */
	-webkit-box-sizing: border-box; /* Safari */
}

.allDateInfo_lcd {
	width: 37%;
	height: 100%;
	min-width: 420px;
	display: inline-block;
	box-sizing: border-box;
	-moz-box-sizing: border-box; /* Firefox */
	-webkit-box-sizing: border-box; /* Safari */
	vertical-align: top;
}

#weekInfo_lcd {
	font-size: 30px;
	font-family: Arial, 微软雅黑;
	margin-left: 35px;
	float: left;
	margin-right:30px;
}

#dateInfo_lcd {
	font-size: 35px;
	font-family: Arial, 微软雅黑;
}

#visInfoAll_lcd {
	width: 100%;
	height: 100%;
	margin-top: 2%;
	text-align: top;
}

.visCurrentTitleP_lcd {
	margin-left: 30px;
	font-size: 1.3vw;
	width: 100px;
}

#person_info_vis_lcd {
	width: 100%;
	height: 90%;
	margin-left: 1%;
}

.visPersPhoto_lcd {
	display: inline-block;
	margin-top: 1%;
	margin-left: 3%;
	vertical-align: top;
	box-sizing: border-box;
	-moz-box-sizing: border-box; /* Firefox */
	-webkit-box-sizing: border-box; /* Safari */
}

.visInfo_lcd {
	min-height: 80%;
	margin-left: 3%;
	display: inline-block;
	box-sizing: border-box;
	-moz-box-sizing: border-box; /* Firefox */
	-webkit-box-sizing: border-box; /* Safari */
}
.visInfo_lcd td {
	vertical-align: top;
}

.visCurrentText_lcd {
	margin-left: 10px;
	width: 300px;
	min-width: 340px;
	font-size: 25px;
	border-bottom: 1px solid gray;
	word-wrap: break-word;
	word-break: break-all;
}

.persPhoto_lcd {
	width: 40%;
	height: 50%;
	display: inline;
	text-align: center;
}

.persInfo_lcd {
	width: 58%;
	height: 50%;
	display: inline;
	text-align: center;
}
.lastPersInfo_lcd {
	margin-left:11%;
}
/* 全屏样式 */
.visCurrentTitlePFull_lcd {
	width: 150px;
	margin: 5px auto;
	font-size: 20px;
	text-align: left;
}

.visPersPhotoFull_lcd {
	width: 24.8%;
	height: 24.5%;
	margin-top: 2.5%;
	margin-left: 5%;
	display: inline-block;
	vertical-align: top;
	box-sizing: border-box;
	-moz-box-sizing: border-box; /* Firefox */
	-webkit-box-sizing: border-box; /* Safari */
}

.visInfoFull_lcd {
	min-height: 24.8%;
	margin-top: 1.5%;
	margin-left: 5%;
	font-family: Arial, 微软雅黑;
	display: inline-block;
	box-sizing: border-box;
	-moz-box-sizing: border-box; /* Firefox */
	-webkit-box-sizing: border-box; /* Safari */
}

.lastInfo_lcd {
	margin: 1%;
	width: 200px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	text-align: center;
	height: 21%;
	font-size: 16px;
}

.lastInfoDiv_lcd {
	margin-left:15%;
}