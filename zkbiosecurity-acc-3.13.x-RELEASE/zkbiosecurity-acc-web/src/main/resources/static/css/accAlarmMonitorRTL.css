.leftAlarm{
    width: 330px;
    margin-left: 10px;
}

.rightAlarm{
    box-shadow: 1px 1px 4px rgba(0,0,0,0.149019607843137);
    border-radius: 5px;
    width: calc(100% - 350px);
    margin-right: -2px;
    overflow-y: scroll;
    overflow-x: hidden;
}

.alarmContent{
    height: calc(100% - 16px);
    overflow-y: auto;
    overflow-x: hidden;
    float: right;
}

.accAlarmTopTitle{
    margin-top: 10px;
    height: 26px;
    width: 100%;
    font-size: 14px;
    font-weight: bold;
    padding-right: 15px;
    border-bottom: 1px solid #F3F5F0;
}

#totalShow{
    width: 80px;
    height: 0px;
    text-align: center;
    position: relative;
    font-weight: bolder;
    font-size: x-large;
    top: 64px;
    right:178px;
}

#totalStr{
    width: 80px;
    height: 0px;
    text-align: center;
    position: relative;
    top: 50px;
    right:178px;
}

.todayInfo{
    transition:transform 0.1s;
    user-select: none;
    text-align: center;
    float: right;
    width: 82px;
    height: 42px;
    margin: 7px 2px;
    padding: 7px 2px;
}

.todayInfo:hover{
    transform:scale(1.05);
}

.infoNum{
    font-size: x-large;
    font-weight: bold;
}

#unhandled{
    color: red;
}

#progress{
    color: #FFBA14;
}

#topShowContent{
    width: 100%;
    height: 160px;
    margin-right: 15px;
}

.leftContent{
    box-shadow: 1px 1px 4px rgba(0,0,0,0.149019607843137);
    border-radius: 5px;
    margin: 5px;
    margin-top: 0px;
    background-color: white;
}

.unbandledInfo{
    background-color: #FAF4F2;
}

.progressInfo{
    background-color: #FAF9F2;
}

.acknowledgedInfo{
    background-color: #F3FAF2;
}

.priorityBase{
    width: 80px;
    height: 0px;
    text-align: right;
    position: relative;
    font-weight: bolder;
    right: 215px;
}

#watchAlarmTime{
    width:192px;
    height:54px;
    font-size:49px;
    font-weight:400;
}

.uncheckDiv{
    display: block;
    height:32px;
    border:1px solid rgba(235, 235, 235, 1);
    border-radius:3px;
    margin-left: 7px;
}

.accAlarmCheckedDiv{
    display: block;
    height:32px;
    background:#7AC143;
    border:1px solid #7AC143;
    border-radius:3px;
    color: white;
    margin-left: 7px;
}

.disableTodayInfo{
    color: #E0E0E0!important;
}

#timeContent{
    transition:display 2s;
}

#controlContent{
    padding: 15px;
}
.accInfoNum {
    text-align:center;
}