/*-------------acc.css门禁系统单独的css------------*/ /* 公共的在public*/
 @charset "utf-8";

.AlarmLog td {
	color: red !important;
}

.IllegalLog td {
	color: #FF9900 !important;
}

.ip_input span {
    width: 148px;
}

/*pop-up*/
.poPupInfo {
	background: none repeat scroll 0 0 #FFFFFF;
	border: 1px dotted #999999;
	border-top: none;
	float: left;
	width: auto;
	min-width: 150px;
	max-width: 250px;
	padding-bottom: 3px;
}

.poPupInfo_text_table {
	color: #000;
	margin: 2px 8px;
}


.poPupInfo_title_p {
	font-family: Arial, Helvetica, Sans-serif;
	font-size: 12px;
	font-weight: bold;
	color: #688060;
	padding: 2px 6px;
	border-top: 1px dotted #999999;
	border-bottom: 1px dotted #999999;
	background-color: #aeb4b8;
}

.poPupInfo_op_ul {
	border-top: 1px dotted #999999;
	font-size: 12px;
	margin-top: 2px;
	color: #3371d4;
}

.poPupInfo_op_ul li {
	padding: 2px 8px;
	border: 1px solid #FFFFFF;
	line-height: 15px;
}

.poPupInfo_op_ul li:hover {
	background: none repeat scroll 0 0 #578bc0;
	border: 1px solid #F1F1F1;
	cursor: pointer;
	color: #FFFFFF;
}

.Link_blue1 a:link,.Link_blue1 a:visited {
	color: #32598a;
	text-decoration: underline !important;
	cursor: pointer;
}

.Link_blue1 a:hover {
	color: #ff6600;
	text-decoration: none !important;
	cursor: pointer;
}

#id_accfirstopen .ul_row_menu {
	_width: 130px;
}

#id_accfirstopen .ul_row_menu {
	width: 230px;
}

#id_accfirstopen #show_person_tree {
	padding: 10px 0px 0px 15px;
}

#id_accfirstopen .dt_bdiv {
	height: 280px;
}

#dataContainer {
	margin-top: 6px;
}

#tr_antiback_mode input,#tr_interlock_mode input {
	_margin: 3px 0px 1px 2px;
}

.interlock_mode td,.antiback_mode td {
	text-align: left !important;
}

.interlock_mode th,.antiback_mode th {
	text-align: right !important;
}

.combopen_tip {
	color: brown;
	line-height: 12px !important;
}

#id_acccombopenperson #show_person_tree {
	padding: 10px 0px 0px 15px;
}

#id_acccombopenperson  .ul_row_menu {
	width: 175px;
}

.rename_aux {
	padding: 2px;
	margin: 10px 0px 2px 14px;
	border: 1px solid rgb(202, 226, 249);
	border-width: 0 1px 1px 0;
}

.rename_aux td {
	border-color: #CAE2F9;
	border-style: solid;
	border-width: 1px 0 0 1px;
	padding: 5px;
	text-align: center;
}

/************** acc start ********************/ /*timeseg edit start*/
.timeseg_edit_table #id_remark {
	width: 500px !important;
}

.timeseg_edit_table th {
	width: 117px !important;
}

.timeseg_edit_div_inner {
	background-color: #F4F7FF;
	padding: 2px 10px 0px 10px;
	border: 1px solid #DBE4EE;
	border-top: none;
}

.timeseg_edit_table_inner {
	text-align: center;
	width: 100%;
	background-color: #FFFFFF;
}

.timeseg_edit_table_inner td {
	border: 1px solid #E8ECF7;
}  /*#ccc*/
.timeseg_edit_table_inner tr td {
	colspan: 1;
}

.timeseg_edit_table_inner input[type="text"] {
	width: 40px !important;
	height: 18px !important;
	line-height: 18px !important;
	padding: 0px !important;
}

.accVerifyModeRule_edit_table_inner td {
    border: 1px solid #E8ECF7;
}

.blue_bgcolor {
	background-color: #aeb4b8;
	color: #fff;
}

.blank_bgcolor {
	background-color: #f7f7f7;
	width: 115px;
}

.out {
	border-top: 40px solid #A3BDD3;
	width: 0px;
	height: 0px;
	border-left: 115px solid #D2DDF0;
	position: relative;
}

.out b {
	font-style: normal;
	display: block;
	position: absolute;
	top: -35px;
	left: -40px;
	width: 35px;
}

.out em {
	font-style: normal;
	display: block;
	position: absolute;
	top: -20px;
	left: -100px;
	width: 55x;
}

/*timeseg edit end*/
#id_remark {
	width: 300px !important;
}

/*combopen edit start*/
.combopen_edit_table input {
	width: 30px;
}

.combopen_edit_table th {
	width: 102px;
}

.combopen_edit_table2 th {
	width: 90px;
}

.combopen_edit_table #id_comb_name {
	width: 120px;
}

/*combopen edit end*/ /*door edit start*/
#door_edit_table tr td {
	colspan: 1;
}

#door_edit_table #id_door_no {
	width: 20px;
}

#door_edit_table #id_force_pwd {
	width: 80px;
}

#door_edit_table #id_supper_pwd {
	width: 80px;
}

#door_edit_table #id_lock_delay {
	width: 30px;
}

#door_edit_table #id_card_intervaltime {
	width: 30px;
}

#door_edit_table #id_duration_apb {
	width: 30px;
}

#door_edit_table #id_sensor_delay {
	width: 30px;
}

#door_edit_table #id_latch_time_out {
	width: 30px;
}

.door_edit_checkbox {
	margin: 2px 0px 0px 0px
}

/*door edit end*/ /*ʵʱ��� start*/
#id_comm_error p a {
	height: 16px;
	line-height: 16px;
	color: #FF0000;
}

.DoorBox {
	margin: 5px, 5px, 5px, 10px;
	width: 80px;
	padding: 10px;
	float: left;
}

.DoorBox .doorName {
	width: 100px;
	height: 16px;
	overflow: hidden;
	white-space: nowrap;
	-o-text-overflow: ellipsis;
	text-overflow: ellipsis;
}

.doorName:not(p) {
	clear: both;
}

.doorName:not(p) a {
	max-width: 100px;
}

#id_door_state {
	clear: both;
	position: relative;
	overflow: auto;
	margin-top: 3px;
	margin-right: 10px;
}  /* height:160px; word-wrap:break-word;
word-break:break-all; vertical-align:middle;white-space:nowrap;word-wrap:normal;*/
.DoorBox img {
	vertical-align: middle;
}

.monitor_datalist {
	border: 1px solid #bbb;
	margin: 0px 3px 5px 3px;
	height: 600px;
	overflow-y: scroll;
	_width: 98%;
}

.video_rec_datalist {
	border: 1px solid #bbb;
	margin: 0px 3px 5px 3px;
	height: 400px;
	overflow-y: scroll;
	_width: 98%;
}

.monitor_head {
	margin: 4px 20px 0px 3px; *
	margin: 4px 5px 0px 3px;
	_margin: 4px 20px 0px 3px;
	_width: 98%;
	position: relative;
}

.AlarmLog td {
	color: red !important;
}

.CommonLog td {
	color: green !important;
}

.IllegalLog td {
	color: #FF9900 !important;
}

.NoSensor td {
	color: #802A2A !important;
}

.OutLine td {
	color: gray !important;
}

.Disabled td {
	color: #FF9900 !important;
}

.link_alarm_unsure {
	color: red;
}

.link_alarm_unsure a:link,.link_alarm_unsure a:visited {
	color: red;
	cursor: pointer;
	text-decoration: underline !important;
}

.link_alarm_unsure a:hover {
	color: red;
	cursor: pointer;
	text-decoration: underline !important;
}

.link_alarm_sure {
	color: blue;
}

.link_alarm_sure a:link,.link_alarm_sure a:visited {
	color: red;
	cursor: pointer;
	text-decoration: underline !important;
}

.link_alarm_sure a:hover {
	color: blue;
	cursor: pointer;
	text-decoration: underline !important;
}

#id_monitor_doors #id_door_ops .action {
	margin-right: 3px;
}

#div_id_middiv {
	margin-bottom: 3px;
}

.opendoor_list {
	margin: 0 10px 0 0;
}

.closedoor_list {
	margin: 0 10px 0 0;
}

#id_monitor_events h1 {
	float: left;
	position: relative;
}

#id_monitor_events .link_alarm_unsure {
	float: right;
	position: relative;
	_margin-top: 3px;
}

#id_email {
	text-transform: none
}

.monitor_hdiv_right {
	width: 20px;
	height: 24px;
	_height: 22px;
	position: absolute;
	right: -17px; *
	right: -19px;
	top: 0px;
	background-color: rgb(107, 165, 215);
	border-top: 1px solid #cccccc;
	border-bottom: 1px solid #cccccc;
	z-index: 5\9;
}

.open_doors_form {
	width: 350px;
	height: 190px;
}  /*#FF6600*/
.close_doors_form {
	width: 300px;
	height: 150px;
}  /*#FF6600*/
.obtain_logs_time_form {
	width: 320px;
	height: 180px;
}  /*#FF6600*/ /*ie6
.acc_monitor_280{_height: expression( this.scrollHeight < 280 ? "280px" : "auto" );}
.acc_monitor_380{_height: expression( this.scrollHeight < 280 ? "380px" : "auto" );}*/
.pop_up_form_map {
	width: 320px;
	height: 320px;
}

.pop_up_form_map th {
	text-align: right;
}

.can_drag {
	cursor: move;
}

.map_show {
	display: none;
	margin: 40px 10px 10px 5px
}

#id_map_judge {
	white-space: nowrap;
	display: none;
	clear: both;
	margin: 40px 2px 2px;
	color: red;
}

.td_upload_map {
	text-align: left;
	width: 180px;
}

.worktable_map {
	width: 96% \9;
	_width: 100%;
	height: auto;
	display: none;
	border-width: 0px 1px 1px 1px;
	margin: 0px 0px 10px 0px;
	padding: 0px 10px 10px 10px;
}

.worktable_map {
	/* background: url("../images/images_bg.gif") repeat-x scroll 0 -877px
		transparent; */
	border: thin solid #CAE2F9
}

div#id_door_ops div div {
	display: inline;
	margin-right: 5px;
}

div#monitor_area,#monitor_device,#monitor_door {
	color: #32598A !important;
}

div#monitor_area label,#monitor_device label,#monitor_door label {
	margin: 0px 5px 0px 8px;
}

div#monitor_area select,#monitor_device select,#monitor_door select {
	width: 150px;
	margin: 0px;
}

.openalldoor {
	margin: 2px 1px 1px 10px;
}

div#id_door_loading {
	margin: 8px 2px 5px 5px;
	color: #FF6633;
}

div#id_map_loading {
	display: none;
	white-space: nowrap;
	margin: 8px 2px 2px 4px;
	display: inline;
	color: #FF6633;
}

*+html #id_monitor_events #id_showTbl {
	border: 1px solid #BBBBBB; /*height:608px;overflow-y:hidden;*/
}

*+html #id_monitor_events #id_showTbl .monitor_datalist {
	/*margin:0px 3px 5px 3px;*/
	margin: 0px 0px 0px 0px;
}

*+html #id_monitor_events #id_showTbl .monitor_head {
	/**margin:4px 5px 0px 3px;*/
	margin: 0px 0px 0px 0px;
}

.monitor_head {
	margin: 4px 20px 0px 3px; *
	margin: 4px 5px 0px 3px;
	_margin: 4px 20px 0px 3px;
	_width: 98%;
	position: relative;
}

div#id_level {
	overflow: auto;
	height: 210px;
	width: 250px;
}

div#id_door_group {
	width: 300px;
	height: 200px;
	overflow: auto;
	border: 1px solid #cae2f9;
	padding: 5px;
	margin-bottom: 5px;
}

div#id_door {
	background: none repeat scroll 0 0 #F3F7FF;
	border: 1px solid #9DC2E1;
	margin-top: 0; /*margin:0px -135px;*/
	width: 300px;
	border: 1px solid #cae2f9;
	padding: 5px;
	margin-bottom: 5px;
}

div#id_show_devices {
	margin-left: -135px;
	margin: 17px 0px 0px -157px\9;
	padding: 1px;
	position: absolute;
	width: 300px;
	z-index: 16;
}

#personlevel_bylevel .ul_row_menu,#personlevel_byperson .ul_row_menu {
	width: 130px;
}

.none_selected {
	color: #FF9900;
}

div.door_edit_table_title {
	width: 850px;
}

td.childcontent .grid {
	margin: 10px 10px 10px 80px;
}

#child_acc_AccDoor .dt_min_height {
	_padding-bottom: 0px !important;
}

#child_acc_AccDoor .pages,#child_acc_AccDoor .div_show_style {
	display: none;
}

#editForm #id_device {
	width: 180px;
}

#id_delay_time {
	width: 40px !important;
}

div.search_acpanel {
	margin-left: 10px;
}

div.search_acpanel table {
	width: 700px !important;
	border: solid 1px #CCC
}

div.search_acpanel table tr th {
	text-align: center;
}

#total_tip {
	margin: 0px 10px 8px 20px;
}

#total_tip .tip1 {
	display: inline;
	margin: 2px 10px 2px 0px;
}

#total_tip .tip2 {
	display: inline;
	margin: 2px 10px 2px 15px;
}

.pop_up_form {
	width: 300px;
	height: 150px;
}

.pop_up_form_pass {
	width: 380px;
	height: 125px;
}

.pop_up_form th {
	text-align: right;
}

/*.pop_up_form_device{width:500px;height:120px;}*/
.tbl_padding #com_port,.tbl_padding #baudrate {
	width: 100px !important;
}

.tbl_padding #start,.tbl_padding #end {
	width: 80px !important;
}

.search_add_device {
	margin: 2px 2px 2px 10px;
}

div.ACPanel_Searching {
	display: inline;
	color: #FF6633;
}

div.ACPanel_Searching img {
	margin: 1px 4px 3px 10px;
}

div#child_acc_AccDoor a:link {
	color: #5b80b2;
	text-decoration: underline;
}

div#child_acc_AccDoora:visited {
	color: #5b80b2;
	text-decoration: underline;
}

div#child_acc_AccDoor a:hover {
	text-decoration: underline;
	color: #ff6600;
}

.currunt_count {
	margin: 2px 2px 2px 8px;
	color: gray;
}

/** **/
.level_list {
	margin: 5px 2px 3px 5px;
	list-style-type: disc;
	list-style: disc;
}

.level_list li {
	clear: both;
	float: none;
}

.level_list li input {
	margin: 1px 3px 0px 0px;
}

#id_access_info #id_acc_starttime,#id_access_info #id_acc_endtime {
	width: 82px !important;
}

.level_list li p {
	display: inline;
}

.levelHint {
	display: none;
	height: 130px;
	left: 160px;
	opacity: 0.6;
	position: absolute;
	top: 60px;
	width: 308px;
	z-index: 2;
	color: black;
}

.clearTableStyle {
	border: solid 1px green;
	background-color: white;
	width: 95%;
	height: 300px;
}

/************** acc end **********************/
div#id_reader_group {
	border: 1px solid #71A8D8;
	overflow: auto;
	height: 360px;
	width: 190px;
}

div#id_gapb_group_1 {
	border: 1px solid #71A8D8;
	overflow: auto;
	height: 150px;
	width: 190px;
}

div#id_gapb_group_2 {
	border: 1px solid #71A8D8;
	overflow: auto;
	height: 150px;
	width: 190px;
}

/**/
div#id_aux_group {
	width: 300px;
	height: 200px;
	overflow: auto;
	border: 1px solid #cae2f9;
	padding: 5px;
	margin-bottom: 5px;
}

/**/
#editForm #id_reader {
	width: 180px;
}

/*IE6\*/
#level_name {
	width: 140px;
}

/*实时监控*/
div#id_door_ops div div {
	display: inline;
	margin-right: 5px;
}

div#monitor_area,#monitor_device,#monitor_door {
	color: #32598A !important;
}

div#monitor_area label,#monitor_device label,#monitor_door label {
	margin: 0px 5px 0px 8px;
}

div#monitor_area select,#monitor_device select,#monitor_door select {
	width: 150px;
	margin: 0px;
}

.openalldoor {
	margin: 2px 1px 1px 10px;
}

div#id_door_loading {
	margin: 8px 2px 5px 5px;
	color: #FF6633;
}

div#id_map_loading {
	display: none;
	white-space: nowrap;
	margin: 8px 2px 2px 4px;
	display: inline;
	color: #FF6633;
}
.jbox-title-icon-video{
	background:url('/public/controls/dhtmlx/skins/web/imgs/dhxtree_web/acc_vidChannel.png') 3px 5px no-repeat;
	background-size: 12px;
}

.acc_rt_auxImg {
	background-image:url("/images/auxInOutState.png");
	background-repeat:no-repeat;
	height:40px;
	width:40px;
}

.aux_in_disable {
	background-position: -5px -55px;
}
.aux_in_off {
	background-position: -55px -55px;
}
.aux_in_on {
	background-position: -105px -55px;
}
.aux_in_default {
	background-image:url("/images/acc_default.png");
}

.aux_out_disable {
	background-position: -5px -5px;
}
.aux_out_off {
	background-position: -55px -5px;
}
.aux_out_on {
	background-position: -105px -5px;
}

.aux_out_default {
	background-image:url("/images/acc_default.png");
}

.acc_rt_doorImg {
	background-image:url("/images/doorState/doorState.png");
	background-repeat:no-repeat;
	height:40px;
	width:40px;
}
/* carGate begin*/
.door_carGate_alarm_closed {
	background-position: -5px -5px;
}
.door_carGate_alarm_closed_cutout {
	background-position: -55px -5px;
}
.door_carGate_alarm_closed_old {
	background-position: -105px -5px;
}
.door_carGate_alarm_closed_poweroff {
	background-position: -155px -5px;
}
.door_carGate_alarm_closed_unlocked {
	background-position: -205px -5px;
}
.door_carGate_alarm_nosensor {
	background-position: -255px -5px;
}
.door_carGate_alarm_nosensor_cutout {
	background-position: -305px -5px;
}
.door_carGate_alarm_nosensor_old {
	background-position: -355px -5px;
}
.door_carGate_alarm_nosensor_poweroff {
	background-position: -405px -5px;
}
.door_carGate_alarm_nosensor_unlocked {
	background-position: -455px -5px;
}
.door_carGate_alarm_opened {
	background-position: -505px -5px;
}
.door_carGate_alarm_opened_cutout {
	background-position: -555px -5px;
}
.door_carGate_alarm_opened_old {
	background-position: -605px -5px;
}

.door_carGate_alarm_opened_poweroff {
	background-position: -5px -55px;
}
.door_carGate_alarm_opened_unlocked {
	background-position: -55px -55px;
}
.door_carGate_alarm_timeout_closed {
	background-position: -105px -55px;
}
.door_carGate_alarm_timeout_closed_old {
	background-position: -155px -55px;
}
.door_carGate_alarm_timeout_closed_unlocked {
	background-position: -205px -55px;
}
.door_carGate_alarm_timeout_opened {
	background-position: -255px -55px;
}
.door_carGate_alarm_timeout_opened_unlocked {
	background-position: -305px -55px;
}
.door_carGate_alwayslocked {
	background-position: -355px -55px;
}
.door_carGate_closed {
	background-position: -405px -55px;
}
.door_carGate_closed_old {
	background-position: -455px -55px;
}
.door_carGate_closed_unlocked {
	background-position: -505px -55px;
}
.door_carGate_disabled {
	background-position: -555px -55px;
}
.door_carGate_nosensor {
	background-position: -605px -55px;
}

.door_carGate_nosensor_old {
	background-position: -5px -105px;
}
.door_carGate_nosensor_unlocked {
	background-position: -55px -105px;
}
.door_carGate_offline {
	background-position: -105px -105px;
}
.door_carGate_offline_to_device {
	background-position: -155px -105px;
}
.door_carGate_open_timeout_old {
	background-position: -205px -105px;
}
.door_carGate_opened {
	background-position: -255px -105px;
}
.door_carGate_opened_old {
	background-position: -305px -105px;
}
.door_carGate_opened_unlocked {
	background-position: -355px -105px;
}
.door_carGate_default {
	background-position: -55px -455px;
}
/* carGate end*/
/*channelGate begin*/
.door_channelGate_alarm_closed {
	background-position: -5px -155px;
}
.door_channelGate_alarm_closed_cutout {
	background-position: -55px -155px;
}
.door_channelGate_alarm_closed_old {
	background-position: -105px -155px;
}
.door_channelGate_alarm_closed_poweroff {
	background-position: -155px -155px;
}
.door_channelGate_alarm_closed_unlocked {
	background-position: -205px -155px;
}
.door_channelGate_alarm_nosensor {
	background-position: -255px -155px;
}
.door_channelGate_alarm_nosensor_cutout {
	background-position: -305px -155px;
}
.door_channelGate_alarm_nosensor_old {
	background-position: -355px -155px;
}
.door_channelGate_alarm_nosensor_poweroff {
	background-position: -405px -155px;
}
.door_channelGate_alarm_nosensor_unlocked {
	background-position: -455px -155px;
}
.door_channelGate_alarm_opened {
	background-position: -505px -155px;
}
.door_channelGate_alarm_opened_cutout {
	background-position: -555px -155px;
}
.door_channelGate_alarm_opened_old {
	background-position: -605px -155px;
}

.door_channelGate_alarm_opened_poweroff {
	background-position: -5px -205px;
}
.door_channelGate_alarm_opened_unlocked {
	background-position: -55px -205px;
}
.door_channelGate_alarm_timeout_closed {
	background-position: -105px -205px;
}
.door_channelGate_alarm_timeout_closed_old {
	background-position: -155px -205px;
}
.door_channelGate_alarm_timeout_closed_unlocked {
	background-position: -205px -205px;
}
.door_channelGate_alarm_timeout_opened {
	background-position: -255px -205px;
}
.door_channelGate_alarm_timeout_opened_unlocked {
	background-position: -305px -205px;
}
.door_channelGate_alwayslocked {
	background-position: -355px -205px;
}
.door_channelGate_closed {
	background-position: -405px -205px;
}
.door_channelGate_closed_old {
	background-position: -455px -205px;
}
.door_channelGate_closed_unlocked {
	background-position: -505px -205px;
}
.door_channelGate_disabled {
	background-position: -555px -205px;
}
.door_channelGate_nosensor {
	background-position: -605px -205px;
}

.door_channelGate_nosensor_old {
	background-position: -5px -255px;
}
.door_channelGate_nosensor_unlocked {
	background-position: -55px -255px;
}
.door_channelGate_offline {
	background-position: -105px -255px;
}
.door_channelGate_offline_to_device {
	background-position: -155px -255px;
}
.door_channelGate_open_timeout_old {
	background-position: -205px -255px;
}
.door_channelGate_opened {
	background-position: -255px -255px;
}
.door_channelGate_opened_old {
	background-position: -305px -255px;
}
.door_channelGate_opened_unlocked {
	background-position: -355px -255px;
}

.door_channelGate_default {
	background-position: -55px -455px;
}
/*channelGate end/
/*door begin*/

.door_door_alarm_closed {
	background-position: -5px -305px;
}
.door_door_alarm_closed_cutout {
	background-position: -55px -305px;
}
.door_door_alarm_closed_old {
	background-position: -105px -305px;
}
.door_door_alarm_closed_poweroff {
	background-position: -155px -305px;
}
.door_door_alarm_closed_unlocked {
	background-position: -205px -305px;
}
.door_door_alarm_nosensor {
	background-position: -255px -305px;
}
.door_door_alarm_nosensor_cutout {
	background-position: -305px -305px;
}
.door_door_alarm_nosensor_old {
	background-position: -355px -305px;
}
.door_door_alarm_nosensor_poweroff {
	background-position: -405px -305px;
}
.door_door_alarm_nosensor_unlocked {
	background-position: -455px -305px;
}
.door_door_alarm_opened {
	background-position: -505px -305px;
}
.door_door_alarm_opened_cutout {
	background-position: -555px -305px;
}
.door_door_alarm_opened_old {
	background-position: -605px -305px;
}
.door_door_alarm_opened_poweroff {
	background-position: -5px -355px;
}
.door_door_alarm_opened_unlocked {
	background-position: -55px -355px;
}
.door_door_alarm_timeout_closed {
	background-position: -105px -355px;
}
.door_door_alarm_timeout_closed_old {
	background-position: -155px -355px;
}
.door_door_alarm_timeout_closed_unlocked {
	background-position: -205px -355px;
}
.door_door_alarm_timeout_opened {
	background-position: -255px -355px;
}
.door_door_alarm_timeout_opened_unlocked {
	background-position: -305px -355px;
}
.door_door_closed {
	background-position: -355px -355px;
}
.door_door_closed_old {
	background-position: -405px -355px;
}
.door_door_closed_unlocked {
	background-position: -455px -355px;
}
.door_door_disabled {
	background-position: -505px -355px;
}
.door_door_nosensor {
	background-position: -555px -355px;
}
.door_door_nosensor_old {
	background-position: -605px -355px;
}
.door_door_nosensor_unlocked {
	background-position: -5px -405px;
}
.door_door_offline {
	background-position: -55px -405px;
}
.door_door_offline_to_device {
	background-position: -105px -405px;
}
.door_door_open_timeout_old {
	background-position: -155px -405px;
}
.door_door_opened {
	background-position: -205px -405px;
}
.door_door_opened_old {
	background-position: -255px -405px;
}
.door_door_opened_unlocked {
	background-position: -305px -405px;
}

.door_door_alwayslocked {
	background-position: -5px -455px;
}
.door_door_default {
	background-position: -55px -455px;
}
/** door end */
.door_aperio_alarm_closed {
	background-position: -5px -505px;
}
.door_aperio_alarm_closed_unlocked {
	background-position: -55px -505px;
}
.door_aperio_alarm_nosensor {
	background-position: -105px -505px;
}
.door_aperio_alarm_nosensor_unlocked {
	background-position: -155px -505px;
}
.door_aperio_alarm_opened {
	background-position: -205px -505px;
}
.door_aperio_alarm_opened_unlocked {
	background-position: -255px -505px;
}
.door_aperio_alwayslocked {
    background-position: -305px -505px;
}
.door_aperio_closed {
    background-position: -355px -505px;
}
.door_aperio_closed_unlocked {
    background-position: -405px -505px;
}
.door_aperio_disabled {
    background-position: -455px -505px;
}
.door_aperio_nosensor {
    background-position: -505px -505px;
}
.door_aperio_nosensor_unlocked {
    background-position: -555px -505px;
}
.door_aperio_offline_to_device {
    background-position: -605px -505px;
}
.door_aperio_offline{
    background-position: -605px -505px;
}
.door_aperio_opened {
	background-position: -5px -555px;
}
.door_aperio_opened_unlocked {
	background-position: -55px -555px;
}

.accQueryDevRule .rowSelected {
	background-color: #e4f4db;
	cursor: pointer;
}

.accQueryDevRule .cursorStyle {
    cursor: pointer;
}

.showTable .viewButton {
    min-width: auto;
}

.showTable tr td {
	border:1px solid #e8eaeb;
}

.unSelectDiv {
	overflow: hidden;
	height: 70px;
	border: #f3f5f0 solid 2px;
	margin-left: 4px !important;
}

.selectDiv {
	border: #7AC143 solid 2px !important;
	border-radius:5px !important;
}

.acc_queryDevUsageIconSize {
    font-size: 17px;
    margin-left: 2px;
}
a.acc_person_level{
    display: block; 
    padding: 10px 5px;
    background-color: #F2F4F5;
    text-decoration: none;
    text-align: center;
    border: 1px solid #BFC3C6;
    border-style:none solid none solid;
    inline-size: max-content;
    min-width: 135px;
}
a.acc_person_level_noBorder{
    border-style:none none none none;
}
a.acc_person_level:hover{
    color: #7EC047
}

/** acc search device begin */
.acc-search-totalProgressDiv {
    display: flex;
    min-height: 40px;
    align-items: center;
}

.acc-search-totalProgressDiv .progressBar {
    width: 300px;
    min-width: 300px;
    padding-right: 2px;
    margin-top: 15px;
    margin-left: 5px;
}
.acc-search-totalProgressDiv .totalProgressTitle {
    word-wrap: break-word;
    word-break: normal;
    white-space: nowrap;
    margin-top: 15px;
}
/** acc search device end */
.accTooltip {
    position: relative;
    height: 22px;
    vertical-align: bottom;
}
.accTooltip:hover .accCommonTipClass {
    visibility: visible;
}
.accCommonTipClass {
    position: absolute;
    left: 18px;
    top: 18px;
    height: auto;
    background-color: #f3f5f0;
    z-index: 10;
    padding: 10px;
    border: 2px solid #dcdfe2;
    text-align: left;
    border-radius: 5px;
    visibility: hidden;
    font-size: 12px !important;
    line-height: 20px !important;
    width: 230px;
}
.acc_icv {
    font-size: 15px;
    cursor: pointer;
    vertical-align: text-top;
    display: inline-block;
}
.accNoTip {
    padding-left: 28px !important;
}

.acc_channel_preview_display_ {
    display: none;
}