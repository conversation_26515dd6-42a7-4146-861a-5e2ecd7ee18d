//门禁实时监控
//弹出框的形式远程开门
var curDoorState = "door_default";
var count = 0;
function showRemoteOpen(elementId, curDoorIds)//元素id和门id（pk)
{
	if(curDoorIds == "")
	{
		openMessage(msgType.info, "${acc_rtMonitor_noLegalDoor}");//当前没有符合条件的门！
		return false;
	}
	var mode = elementId.split("_")[0];
	if((mode == "openpart" || mode == "closepart") && curDoorIds.length == 0)
    {
		openMessage(msgType.info, "${acc_rtMonitor_noLegalDoor}");//当前没有符合条件的门！
        return false;
    }
	var path = "accRTMonitorAction!controlDoor.action?action=openDoor&mode=" + mode + "&curDoorIds=" + curDoorIds + "^0^0^450^250^${acc_eventNo_8}";//远程开门
	openWindow(path);
}

//远程关门弹出框。
function showRemoteClose(elementId, curDoorIds)
{
	var mode = elementId.split("_")[0];
	if((mode == "openpart" || mode == "closepart") && curDoorIds.length == 0)
    {
		openMessage(msgType.info, "${acc_rtMonitor_noLegalDoor}");//当前没有符合条件的门！
        return false;
    }
	var path = "accRTMonitorAction!controlDoor.action?action=closeDoor&mode=" + mode + "&curDoorIds=" + curDoorIds + "^0^0^450^250^${acc_eventNo_9}";//远程关门
	openWindow(path);
}

//取消报警
function showCancelAlarm(elementId, curDoorIds)
{
	var mode = elementId.split("_")[0];
	messageBox({messageType:"confirm", text: "${acc_rtMonitor_confirmCancelAlarm}", callback:function(result)
	{
		if(result == true)
		{
			remoteControl(mode,curDoorIds);
		}
	}});
}

//远程开门、关门操作，取消报警。
function remoteControl(mode,curDoorIds)
{
    var stamp5 = new Date().getTime();
    var getUrl = sysCfg.rootPath + "accRTMonitorAction!";
    var opCount = "one";//操作对象数量（一个门，或者多个门）
    if(mode == "openpart" || mode=="closepart" || mode == "cancelall")
    {
    	opCount = "more";
    }
    if(mode == "opendoor" || mode == "openpart")
    {
        var openInterval = 15;
        var enableNoTzs = false;
        if($("#id_open_no").attr("checked") == "checked")//常开
        {
        	getUrl += "normalOpenDoor.action?stamp=" + stamp5;
        }
        else if($("#id_openInterval_set").attr("checked") == "checked")//正常开门
        {
            openInterval = $("#id_open_sec1").val();
            getUrl += "openDoor.action?openInterval=" + openInterval + "&stamp=" + stamp5;
        }
        else if($("#id_reEnable_open").attr("checked") == "checked")//启用常开时间段
        {
        	getUrl += "enableNormalOpenDoor.action?stamp=" + stamp5;
        }
    }
    else if(mode=="closedoor" || mode=="closepart")//closepart
    {
        if($("#id_disable_no_tzs").attr("checked") == "checked")//禁用常开时间段
        {
        	getUrl += "disableNormalOpenDoor.action?stamp=" + stamp5;
        }
        else if($("#id_close_normal").attr("checked") == "checked")//关门
        {
        	getUrl += "closeDoor.action?stamp=" + stamp5;
        }
    }
    else if(mode == "cancelalarm" || mode == "cancelall")
    {
    	getUrl += "cancelAlarm.action?stamp=" + stamp5;
    }
    var verifyPwd = $("#verifyPwd").val();
    getUrl = getUrl + "&verifyPwd=" + verifyPwd + "&opCount=" + opCount;
    return controlDevice(mode, getUrl, curDoorIds);
}


function controlDevice(mode, getUrl, curDoorIds)
{
	var ret = sysCfg.success;
    $("#id_close").click();//关闭远程开关门的弹出框
    $.ajax({
        type: "POST",
        url: getUrl,
        dataType: "json",
        data: {"data" : curDoorIds},
        async: false,
        success: function(result)
        {
            ret = result.ret;
            dealRetResult(result, function(){
            	if(mode == 'cancelalarm' || mode == 'cancelall')
                {
            		if(ret == sysCfg.success)
            		{
            			closeAlarmSound();
            		}
                    $("#cancelall").parent().hide();//无论成功与否均隐藏。如果失败，会再自动产生
                }
            });
        },
        error:function(XMLHttpRequest, textStatus, errorThrown)
        {
            ret = sysCfg.error;
        }
    });
    return ret;
}


//根据服务器端返回的门、设备、区域相关数据渲染页面
//此处除了all，暂时未返回area，device ，door......
function showDoorsInfo(data, entityType)
{
	var doors = data.doors;
    var doorTotal = doors.length;
	if(doorTotal > 0)
    {
        for(var index in doors)
        {
			if(doors[index][3] != undefined)
       	    {
				var popupText = createPopupText(index, doors);
                var doorIdValue = doors[index][0];
                $("#"+entityType+"_"+doorIdValue+" .entityName").html(doorName);
                $("#"+entityType+"_"+doorIdValue+" .dhx_item_popupText").append(popupText);
            }
        }
    }
}

 //end of function showDoors

///根据服务器端返回的门、设备、区域相关数据渲染页面
//此处除了all，暂时未返回area，device ，door......
//使用stateDataView显示门
//stateDataView start
var stateDataView = null;
function showDataViewDoors(data)
{
    var doors = data.doors;
    //清空当前已有的门。
    if($("#id_door_state div,#id_door_state table").length > 0)
    {
        $("#id_datalist_view,#id_datalist_view_table").remove();
    }

    //$("#id_door_state #id_datalist_view").remove();//避免重复提示

    doorTotal = doors.length;
	if(doorTotal > 0)
    {
		if(stateDataView == null)
		{
			stateDataView = new dhtmlXDataView("dataContainer");
			stateDataView.define("type", "ficon");// ficon
		    stateDataView.customize({
		        icons_src_dir: '/public/controls/dhtmlx/dhtmlxstatestateDataView/common/images/'
		    });

		  	var doorJson = [];
			//function setStatus(str) {
			//  document.getElementById("stat").innerHTML += str + "<br>";
			//}
			////statestateDataView end
	        for(var index in doors)
	        {
				if(doors[index][3] != undefined)
	       	    {
					var popupText = createPopupText(index, doors);
					var doorIdValue = doors[index][0];
					var check = stateDataView.exists(doorIdValue);
					if(!check)
					{
						doorJson.push({
							"id": doorIdValue,
							"imgId": "default",
							"devName": doors[index][1],
							"doorNo": doors[index][2],
							"doorName": truncateText(doors[index][3]),
							"sensorState": "${common_paging_loading}",
							"relayState": "${common_paging_loading}",
							"alarmState": "${common_paging_loading}",
							"showOp": false
						});
					}
	            }
	        }
	        stateDataView.parse(doorJson, "json");
		}
		else
		{
			stateDataView.filter(function(obj,value){
				for(var index in doors)
    			{
					var doorIdValue = doors[index][0];
			    	if (obj.id == doorIdValue)
			    	{
			    		return true;
			    	}
		    	}
				return false;
		    },doors);
		}
    }
    else
    {
    	//$("#data_container").append("当前系统中没有添加门或者没有查询到符合您需要的门!");<div><a onclick='get_doors_area_device(url);'>重新加载</a></div>.empty()
    	$("#data_container").append("<div style='margin:8px 2px 2px 2px;color:red;'>${acc_rtMonitor_curSystemNoDoors}</div>");//当前系统中没有添加门或者没有查询到符合您需要的门!
        //html += "<div id='id_datalist_view' class='NoDoor' style='margin:8px 2px 2px 2px;color:red;'>当前系统中没有添加门或者没有查询到符合您需要的门!</div>"
    }

    $("#id_door_loading").hide();
    $("#dataContainer").scrollTop(1);// 触发滚动条
    html = null;

    if($("#id_door_state").height() > 150)
    {
    	//alert("大于150");
        $("#id_door_state").height(150);//最大150px
    }
}
 //end of function showDoors

/**
 * 提取门名称截取方法，便于实时监控及电子地图公用
 * @param {Object} textInfo 门名称
 * @return {TypeName} 处理后的门名称
 */
function truncateText(textInfo)
{
	var retText = textInfo;
	var len = textInfo.length;
 	var cnStr = textInfo.match(/[^\x00-\xff]/ig);//查看是否有中文字符串
 	if(cnStr!=null)
 	{
    	if(len >= 5)
   		{
        	retText = textInfo.substr(0,5) + "...";//4个字符
     	}
 	}
 	else
    {
        if(len >= 10)
        {
            retText = textInfo.substr(0,10) + "...";//4个字符
        }
    }
 	return retText;
}

//start of function createPopupText
/*
 * 创建需要显示的门信息、其他信息呢？
 * @param {Object} index
 * @param {Object} doors
 * @return {TypeName}
 */
function createPopupText(index, doors)
{
	//input中data为门的id
	doorName = truncateText(doors[index][3]);//将截取门名称的方法抽取出来

    var doorIdValue = doors[index][0];
    var popupText = "<span class='st'>${common_dev_entity}:&nbsp;</span><span class='devName ut'>"+doors[index][1]+"</span><br/>"
    				+"<span class='st'>${acc_door_number}:&nbsp;</span><span class='doorNo ut'>"+doors[index][2]+"</span><br/>"
    				+"<span class='st'>${acc_door_sensor}:&nbsp;</span><span class='sensor ut'>"+"${common_paging_loading}"+"</span><br/>"
    				+"<span class='st'>${acc_door_relay}:&nbsp;</span><span class='relay ut'>"+"${common_paging_loading}"+"</span><br/>"
    				+"<span class='st'>${common_alarm}:&nbsp;</span><span class='alarm ut'>"+"${common_paging_loading}"+"</span><br/>"
    				+"<span class='doorOp st displayN'><a id='opendoor' class='linkStyle' onclick='showRemoteOpen(this.id,"+doors[index][0]+")'>${common_open}</a>&nbsp;"
    				+"<a id='closedoor' class='linkStyle' onclick='showRemoteClose(this.id, "+doors[index][0]+")''>${common_close}</a>&nbsp;<a id='cancelalarm' class='linkStyle' onclick='showCancelAlarm(this.id, "+doors[index][0]+")'>${acc_rtMonitor_cancelAlarm}</a></span>";
	return popupText;
}
//end of function createPopupText


//处理异步间隔加载状态和事件。-darcy20130121
//此方法剩余电子地图调用。但这部分需要整合到地图或者和accRTEvent做一个整合--？？？？？
function renderStateAndEvents(rtData)
{
	if(rtData != null && rtData.logId != undefined)//??
    {
    	logId = rtData.logId;
    	mapInit = false;
    }
	var hasAlarms = renderDoorsState(rtData.doorStates,"AccDoor_");//门状态监控
	if(hasAlarms == false)
	{
	    $("#cancelall").parent().hide();
	    closeAlarmSound();
	}
	//renderEvents(rtData.data);//事件监控
}


//渲染门状态。
/*
 *
 * @param {Object} rtDoorsState
 * objPrefix前缀。后续可能继续修改
 * @param {Object} entityObj如门1$("#AccDoor_1")entity后续扩展为其他？
 * @return {TypeName}
 */


function renderDoorsState(rtDoorsState, objPrefix)
{
	curDoorState = "door_offline";
	var hasAlarm = false;
	if(rtDoorsState != "")
	{
		for(var index in rtDoorsState)
	    {
	    	var doorObj = rtDoorsState[index];//该变量记录了当前某个门的状态

	        var doorId = doorObj.id;//门id，临时变量--huangjs20120604
	        //创建一个变量，减少JS的开销
	        var alarm = doorObj.alarm;//报警状态
	        var sensor = doorObj.sensor;//门磁状态
	        var relay = doorObj.relay;//继电器状态
	        var enabled = doorObj.enabled;//启用状态
	        var connect = doorObj.connect;//连接状态
	        var alarmLevel = doorObj.alarmLevel;//为1代表为支持区分报警类型。为0代表兼容老的只有报警和门开超时的报警状态。
	        var $doorImg = $("#"+objPrefix+doorId+" img");
	     	var $doorSensor = $("#"+objPrefix+doorId).find(".dhx_item_popupText").find(".sensor");;//门磁状态-连接状态等。
	     	var $doorRelay =  $("#"+objPrefix+doorId).find(".dhx_item_popupText").find(".relay");//继电器状态
	     	var $doorAlarm =  $("#"+objPrefix+doorId).find(".dhx_item_popupText").find(".alarm");//报警状态
	     	var $doorOp = $("#"+objPrefix+doorId).find(".dhx_item_popupText").find(".doorOp");//远程操作
			if(connect != 1)//禁用(2)或者  启用但未连接(0）
	        {
	            $doorImg.attr("src", getDoorStateImgSrc(getDoorImgDict(0, connect)));//getDoorImgDict(DOOR_COMM_STATE, connect)??
	            $doorSensor.text(getDoorStateDict(DOOR_COMM_STATE, connect));
	            $doorRelay.text(getDoorStateDict(DOOR_RELAY_STATE, connect));
	            $doorAlarm.text(getDoorStateDict(DOOR_ALARM_STATE, connect));
	            alarm = 0;
	        }
			else
	        {
				$doorOp.show();//报警状态
	      		$doorSensor.text(getDoorStateDict(DOOR_SENSOR_STATE, sensor));
	      		$doorRelay.text(getDoorStateDict(DOOR_RELAY_STATE, relay));
	      		if(alarmLevel == 0)
	      		{
	      			$doorImg.attr("src", getDoorStateImgSrc(getDoorImgDict(connect, alarmLevel, alarm, sensor)));//getDoorStateImgSrc 有问题!!!!!
	      			//报警类型的判断
		      		$doorAlarm.text(getDoorStateDict(DOOR_ALARM_STATE, alarmLevel, alarm));
	      		}
	      		else//支持区分报警类型 alarmLevel=1
	      		{
	  				$doorImg.attr("src", getDoorStateImgSrc(getDoorImgDict(connect, alarmLevel, analyzeAlarmForDoorPic(alarm), relay, sensor)));//getDoorStateImgSrc 有问题!!!!
	  				//报警类型的判断
		      		$doorAlarm.text(getDoorStateDict(DOOR_ALARM_STATE, alarmLevel, analyzeAlarmForDoorInfo(alarm)));
	      		}
			}
			var isAlarm = renderAlarms(alarm);
			if (isAlarm)
			{
				hasAlarm = isAlarm;
			}
		}
	}
	return hasAlarm;
}

//渲染门状态。
/*
 *
 * @param {Object} rtDoorsState
 * objPrefix前缀。后续可能继续修改
 * @param {Object} entityObj如门1$("#AccDoor_1")entity后续扩展为其他？
 * @return {TypeName}
 */
function renderDataViewDoorsState(rtDoorsState)
{
	for(var index in rtDoorsState)
	{
	    var doorObj = rtDoorsState[index];//该变量记录了当前某个门的状态
	}

    //count++;
	curDoorState = "door_offline";
	if(rtDoorsState != "")
	{
		for(var index in rtDoorsState)
	    {
	    	var doorObj = rtDoorsState[index];//该变量记录了当前某个门的状态

	        var doorId = doorObj.id;//门id，临时变量--huangjs20120604
	        //创建一个变量，减少JS的开销
	        var alarm = doorObj.alarm;//报警状态
	        var sensor = doorObj.sensor;//门磁状态
	        var relay = doorObj.relay;//继电器状态
	        var enabled = doorObj.enabled;//启用状态
	        var connect = doorObj.connect;//连接状态
	        var alarmLevel = doorObj.alarmLevel;//为1代表为支持区分报警类型。为0代表兼容老的只有报警和门开超时的报警状态。

	     	var doorNewObj = stateDataView.get(doorId);
	     	if(doorNewObj != undefined)//临时处理---liujun
			{
				if(connect != 1)//禁用(2)或者  启用但未连接(0）
		        {
					doorNewObj.imgId = getDoorImgDict(0, connect);
					doorNewObj.sensorState = getDoorStateDict(DOOR_COMM_STATE, connect);
					doorNewObj.relayState = getDoorStateDict(DOOR_RELAY_STATE, connect);
					doorNewObj.alarmState = getDoorStateDict(DOOR_ALARM_STATE, connect);
					alarm = 0;
		        }
				else
		        {
					doorNewObj.showOp = true;
					doorNewObj.sensorState = getDoorStateDict(DOOR_SENSOR_STATE, sensor);
					doorNewObj.relayState = getDoorStateDict(DOOR_RELAY_STATE, relay);
		      		if(alarmLevel == 0)
		      		{
		      			doorNewObj.imgId = getDoorImgDict(connect, alarmLevel, alarm, sensor);
		      			//报警类型的判断
		      			doorNewObj.alarmState = getDoorStateDict(DOOR_ALARM_STATE, alarmLevel, alarm);
		      		}
		      		else//支持区分报警类型 alarmLevel=1
		      		{
		      			doorNewObj.imgId = getDoorImgDict(connect, alarmLevel, analyzeAlarmForDoorPic(alarm), relay, sensor);

		  				//报警类型的判断
		      			doorNewObj.alarmState = getDoorStateDict(DOOR_ALARM_STATE, alarmLevel, analyzeAlarmForDoorInfo(alarm));
		      		}
				}
				renderAlarms(alarm);
				stateDataView.set(doorId,doorNewObj);
			}
		}
	}
}

//渲染报警状态。
function renderAlarms(alarm)
{
	if(alarm > 0)//判断是否有报警
 	{
     	if($("#idAlarmSound").html() == "")//避免声音重复
     	{
     		playAlarmSound();// 播放报警声音
            $("#cancelall").parent().show();//取消全部报警
//            $("#cancelall").unbind("click");
//			$("#cancelall").click(function(){
//                var alarmDoors = new Array();//当前页面上所有报警的门
//                $("div[id^='door_']").each(function(){
//                    if($(this).find("img").attr("src").indexOf("door_alarm") != -1)
//                    {
//                        alarmDoors.push($(this).attr("data"));
//                    }
//                });
//                var mode = $(this).attr("id");
//        		getUrl = sysCfg.rootPath + "accRTMonitorAction!controlDevice.action?func="+ mode +"&data="+ alarmDoors;//所有报警的门非所有门，故type=part而非all
//        		controlDevice(mode,getUrl);
//        		mode = null;
//				alarmDoors = null;
//			});
		}
     	return true;
	}
	return false;
}

//校验用户密码，远程开关门等操作时
function verifyUserPwd(needHideId)
{
	var result = false;
	$.ajax({
		type: "GET",
		url: "authUserAction!isExistPassword.action?oldPassword=" + $("#verifyPwd").val(),
		dataType: "json",
		async: false,
		success: function(ret)
	    {
	    	if(ret)
	    	{
	    		if(needHideId != undefined)
	    		{
	    			$("#"+needHideId).show();
	    		}
	    		$("#verifyPwdTr").hide();
	    		result = true;
	    	}
	    	else
	    	{
	    		openMessage(msgType.warning, "${auth_user_pwdIncorrect}");
	    	}
		}
	});
	return result;
}

//点击实时监控的门图标出现门设置页面
function showDoorSet(doorId)
{
	createWindow("/accDoorAction!getById.action?id="+doorId+"^0^0^910^420^${common_op_edit}");
}

