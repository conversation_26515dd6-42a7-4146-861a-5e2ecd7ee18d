

function selectDsTime() {
    var timeZone = $("input[name='timeZone']").val();
    var dsTime = $("input[name='accDSTimeId']").val();
    var opts = {//弹窗配置对象
        path: "accDSTime.do?getDSTimeSelectView&timeZone=" + timeZone + "&dsTime=" + dsTime,//设置弹窗路径
        width: 500,//设置弹窗宽度
        height: 300,//设置弹窗高度
        title: I18n.getValue("acc_dev_setDstime"),//设置弹窗标题
        gridName: "gridbox"//设置grid
    };
    DhxCommon.createWindow(opts);
}

function changeTimeZone(id) {
    if(id != "" && id != null) {
        $.ajax({
            url: "accDSTime.do?hasDSTimeByTimeZone",
            data: {
                "timeZone":id
            },
            success: function(res) {
                if(res.data) {
                    selectDsTimeCallBack("", "");
                    $("#noUtcDiv").hide();
                    $("#selectUtcDiv").show();
                } else{
                    selectDsTimeCallBack("", "");
                    $("#noUtcDiv").show();
                    $("#selectUtcDiv").hide();
                }
            }
        })
    } else {
        selectDsTimeCallBack("", "");
        $("#noUtcDiv").show();
        $("#selectUtcDiv").hide();
    }
}
