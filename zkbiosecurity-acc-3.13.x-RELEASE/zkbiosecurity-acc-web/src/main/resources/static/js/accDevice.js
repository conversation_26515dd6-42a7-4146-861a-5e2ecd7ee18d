var snArray = new Array();//全局变量，存放所有sn -- 搜索设备中用到
var nowTime = null;
var devIpAddr = null;

// 验证是否通过
//var validSubmit = false;
// 添加设备前验证
function addAccDevBeforeSubmit(submitFunc)
{
	// if(ipv4 != null)
	// {
	// 	$("#idIPAddress").attr("value", ipv4.getValue());
	// 	ipv4.setEnable("true");
	// }
	if($("#idModelPk").val() != "" && $("#idModelPk").val() != undefined)// 编辑，不需要再次验证
	{
		// 放开disable，表单才能拿到数据
		$("input[id^='idCommType']").each(function(){
			$(this).attr("disabled", false);
		});
		$("#idAcpanelType").removeAttr("disabled");
		$("#idBaudrate").removeAttr("disabled");
		$("#idComPort").removeAttr("disabled");
		$("#idCommPwd").removeAttr("disabled");
		submitFunc();
		return true;
	}
	
	var commTypeVal = $("#trCommType input[name='commType']:checked").val();
    var ipAddress
	if (ZKUI.IP.get("ipAddress") != undefined) {
        ipAddress = ZKUI.IP.get("ipAddress").ip.getValue();
	} else {
        ipAddress = $("#idIPAddress").val();
	}
	var ipPort = $("#idIPPort").val();
	var commPwd = window.btoa($("#idCommPwd").val());
    var acpanelType = ""
    if (ZKUI.Combo.get("accAcpanelType") != undefined) {
        acpanelType = ZKUI.Combo.get("accAcpanelType").getValue();
    }
	var fourToTwo = 0;
	if ( $("#idFourToTwo").attr("checked") == "checked")
	{
		fourToTwo = 1;
	}
	var comAddress = $("#idComAddress").val();
	var comPort = "";
	var baudrate = "";
	if (ZKUI.Combo.get("idComPort") != undefined) {
		comPort = ZKUI.Combo.get("idComPort").getValue();
	}
	if (ZKUI.Combo.get("idBaudrate") != undefined) {
		baudrate = ZKUI.Combo.get("idBaudrate").getValue();
	}

	if(commTypeVal == undefined || commTypeVal == null || commTypeVal == "")
	{
		commTypeVal = $("#idCommType").val();//搜索后添加设备
	}
	
	if(commTypeVal == '1'|| commTypeVal == '3')
	{
		$("#idComAddress").val("");
		$("#idComPort").val("");
		$("#idBaudrate").val("");
	}
	else if(commTypeVal == '2')
	{
		$("#idIPAddress").val("");
		$("#idIPPort").val("");
	}
	if(commTypeVal == '1' || commTypeVal == '3')
	{
		var connectParam = "commType="+commTypeVal+"&ipAddress="+ipAddress+"&ipPort="+ipPort+"&commPwd="+commPwd+"&acpanelType="+acpanelType+"&fourToTwo="+fourToTwo;
		getAccData(connectParam, submitFunc);
	}
	else if(commTypeVal == '2')
	{
		var connectParam = "commType="+commTypeVal+"&comAddress="+comAddress+"&comPort="+comPort+"&baudrate="+baudrate+"&commPwd="+commPwd+"&acpanelType="+acpanelType+"&fourToTwo="+fourToTwo;
        getAccData(connectParam, submitFunc);
	}
}

// 获取设备参数并判断
function getAccData(connectParam, submitFunc)
{
	onLoading(function(){
		var stamp = new Date().getTime();
		$.ajax({
			type: "POST",
			url: "accDevice.do?getDevInfo&"+connectParam+"&stamp="+stamp,
			dataType: "json",
			async: true,
			success: function(retData)
			{
				closeMessage(1);
				if(retData['ret'] == "ok")
				{
					if(retData.data == "")
					{
						messageBox({messageType:"alert", text: I18n.getValue("common_dev_addDevConfirm5")+"("+retData['msg']+")"});
					}
					else
					{
					    var optionArray = new Array();
                        var tempOptionArray = retData["data"].split(",");
                        var lockCount = "";
                        var machineType = "";
                        for(var i = 0; i < tempOptionArray.length; i++)
                        {
                            tempArray = tempOptionArray[i].split("=");
                            optionArray.push("\"" + tempArray[0] + "\":\"" + (tempArray.length > 1 ? tempArray[1] : "") + "\"");
                            if(tempArray[0] == "LockCount")
                            {
                                lockCount = tempArray[1];
                            }
                            if(tempArray[0] == "MachineType")
                            {
                                machineType = tempArray[1];
                            }
                        }
                        if (machineType == 7) {
                            ZKUI.Combo.get("accAcpanelType").setValue(4);
                            $("#trFourToTwo input").attr("checked", true);
                        } else if (machineType == 101) {
                            ZKUI.Combo.get("accAcpanelType").setValue(5);
                        } else {
                            ZKUI.Combo.get("accAcpanelType").setValue(lockCount);
                        }
                        messageBox({messageType:"confirm", text: I18n.getValue("common_dev_addDevConfirm1"),
                            callback: function(result){
                                if(result)
                                {
                                    $("#idAcpanelOptions").val("{" + optionArray.toString() + "}");
                                    devIpAddr = $("#idIPAddress").val();
                                    submitFunc();
                                }
                        }});
					}
				}
				else if(retData['ret'] == "deviceLimit")
				{
					messageBox({messageType:"alert", text: retData["msg"]});
				}
				else if(retData['ret'] == "message")
				{
				    var optionArray = new Array();
                    var tempOptionArray = retData["data"].split(",");
                    var lockCount = "";
                    var machineType = "";
                    for(var i = 0; i < tempOptionArray.length; i++)
                    {
                        tempArray = tempOptionArray[i].split("=");
                        optionArray.push("\"" + tempArray[0] + "\":\"" + (tempArray.length > 1 ? tempArray[1] : "") + "\"");
                        if(tempArray[0] == "LockCount")
                        {
                            lockCount = tempArray[1];
                        }
                        if(tempArray[0] == "MachineType")
                        {
                            machineType = tempArray[1];
                        }
                    }
                    if (machineType == 7) {
                        ZKUI.Combo.get("accAcpanelType").setValue(4);
                        $("#trFourToTwo input").attr("checked", true);
                    } else if (machineType == 101) {
                        ZKUI.Combo.get("accAcpanelType").setValue(5);
                    } else {
                        ZKUI.Combo.get("accAcpanelType").setValue(lockCount);
                    }
					messageBox({messageType:"confirm", text: retData["msg"],
						callback:function(result){
		        			if(result)
	            			{
                                closeMessage();
                                $("#idAcpanelOptions").val("{" + optionArray.toString() + "}");
                                devIpAddr = $("#idIPAddress").val();
                                submitFunc();
	            			}
		            	}});
				}
				else if(retData['ret'] == "noLicense")
				{
				    messageBox({messageType:"alert", text: retData["msg"]});
				}
				else if(retData['ret'] == "noC3License")
				{
				    messageBox({messageType:"alert", text: retData["msg"]});
				}
				else if(retData['ret'] == "paramError")
				{
				    messageBox({messageType:"alert", text: retData["msg"]});
				}
				else
				{
					console.log(retData["msg"])
					console.log(retData["msg"] != undefined && retData["msg"] != null && retData["msg"] != "")
				    //var msg = retData['msg'] == "" ? "错误码："+retData['result'] : "原因："+retData['msg'];
				    if(retData["msg"] != undefined && retData["msg"] != null && retData["msg"] != "")
				    {
				        messageBox({messageType:"alert", text: I18n.getValue("common_dev_connectDevFails").format(retData["msg"])});
				    }
				    else
				    {
				        messageBox({messageType:"alert", text: I18n.getValue("common_deviceConnError")});
				    }
				}
			}
		});
	});
}

//用来判断设备是否禁用，如果禁用给出提示，
function isEnabledAndOpenWin(id)
{
	var checkboxName = "";
	var split = ",";
	var idNum = 0;
	if(id.indexOf("(id*") > 0)
	{
		var opParamStr = id.substring(id.indexOf("(id*") + "(id*".length, id.lastIndexOf(")"));
		var opParams = eval("(" + opParamStr + ")");
		checkboxName = opParams.checkboxName ? opParams.checkboxName : checkboxName;
	}
	var checkboxValues = getGridCheckboxValue(checkboxName, ",");
	if(checkboxValues != "")
	{
		 $.ajax({
			type: "GET",
			url: "accDeviceAction!getDevIds.action?deviceId="+ checkboxValues,
			dataType: "text",
			async: true,
			success: function(data)
		    {
			 	if(data != "")
			 	{
			 		var devArray = data.substring(0, data.length-1).split(",");
			 		var devStr = "";
			 		for(var i = 1; i <= devArray.length; i++)
			 		{
			 			devStr += devArray[i-1]+", ";
			 			if(i != 0 && i%2 == 0)
			 			{
			 				devStr += "<br/>";
			 			}
			 		}
					//messageBox({messageType:"alert",text:  "<div>"+devStr + "${common_dev_offlinePrompt}"+"</div>"});
			 		messageBox({messageType:"alert",text:  "<div>"+devStr + "${acc_dev_disable}"+"</div>"});
			 	}
			 	else
			 	{
			 		openWindowByOperate(id);
			 	}
			}
		});
	}
	else
	{
		openWindowByOperate(id);
	}
}

function execAccConfirm(devId, func)
{
	devOpType = 1;
	var devArray = devId.split(",");
	var isMulti = devArray.length > 1 ? true : false;
	var offlineArray = new Array();
	var submit = function(result)
	{
		if(result)
		{
			if(devOpType == 2) //操作所有设备
			{
				func();
			}
			else if(devOpType == 1)  //操作在线设备
			{
				if(isMulti)
				{
					if(equalArray(offlineArray, devArray))//所选择的设备全部离线，选择取消则关闭窗口
					{
						closeWindow(); //关闭窗口
					}
					else   //则操作在线设备
					{
						//比较方法    *****暂时a1数组的长度大于a2
						function filter(a1, a2) 
						{
							var myAry = [];
							for(var i=0;i<a1.length;i++)
							{
								//if(!a2.contains(a1[i]))
								if($.inArray(a1[i], a2) < 0)   //$.inArray(eventRowData.logId, logIdArray)
								myAry.push(a1[i]);
							}
							return myAry;
						}
						var tempArray = offlineArray.length > devArray.length ?  filter(offlineArray, devArray) : filter(devArray, offlineArray);
						$("#devIds").val(tempArray.join(","));
						func();
						setTimeout(function(){$("#devIds").val(devArray);}, 300);
					}
				}
				else
				{
					//closeWindow(); //关闭窗口   如果只有一个设备情况
					func();
				}
			}
		}
		else //confirm选择取消后，直接关闭窗口
		{
//			closeWindow();
		}
		
		
		$("input:hidden[name='ids']").val(devId);
	};
	//判断是否离线
	$.ajax({
		  type : "POST",
		  url : "/accDevice.do?isOnline",
		  data : { deviceId : devId},
		  dataType : "json",
		  success : function(r){
			   if(r != null && r != "")
			   {
				   if(r.data.ids == "" && r.data.alias == "")
				   {
					   func();
				   }
				   else
				   {
					   offlineArray = r.data.ids.split(",");
					   if(offlineArray.length < 1)
					   {
					       if(offlineArray != null && $.inArray(offlineArray[0], devArray) >= 0)
						   {
						       var tipText = r.data.alias + I18n.getValue("acc_dev_offlineAndContinue") ;//提示信息前添加一个空格与设备名称隔开
						       messageBox({messageType: "confirm",text: tipText , callback: submit});
						   }
					       else
					       {
					           messageBox({messageType: "alert",text: I18n.getValue("acc_dev_opException")});
					       }
					   }
					   else
					   {
						    //这边处理主要是为了confirm提示框中文字内容在一行太长，现在每两个alias一行
						    var aliasArray = r.data.alias.split(",");
						    var devStr = "";
					 		for(var i = 1; i <= aliasArray.length; i++)
					 		{
					 			if (i == aliasArray.length)
					 			{
					 				devStr += aliasArray[i-1];
					 			}
					 			else
					 			{
					 				devStr += aliasArray[i-1]+", ";
					 			}
					 			if(i != 0 && i%2 == 0)
					 			{
					 				devStr += "<br/>";
					 			}
					 		}
		    			 	var tipText = devStr + " "+ I18n.getValue("acc_dev_offlineAndSelect") + "<br/><br/>"
		    							+ "<div style=''>"
		    							+	"<div><b>" + I18n.getValue("acc_dev_selOpDevType") + "</b></div>"
		    							+ 		"<div style='margin-top:4px; text-align:center'>"
		    							+			"<table style='text-align: center;'>"
		    							+			"<tr>"
		    							+			  "<td width='69px' style='text-align: right;'><span id='onlineDevSpan'></span></td>"
		    							+  			  "<td style='text-align: left;'>" + I18n.getValue("acc_dev_opOnlineDev") + "</td>"
		    							+			"</tr>"
		    							+			"<tr>"
		    							+			  "<td style='text-align: right;'><span id='allDevSpan'></td>"
		    							+			  "<td style='text-align: left;'>" + I18n.getValue("acc_dev_opAllDev") + "</td>"
		    							+			"</tr>"
		    							+			"</table>"
		    							+		"</div>"
		    							+	"</div>"
		    							+ "</div>"
		    			    loadUIToDiv("input", "#onlineDevSpan", {
                                useInputReq:true,
                                hideLabel:true,
                                type:"radio",
                                id:"onlineDev",
                                name:"opType",
                                value:"1",
                                defaultChecked:true
                            });
                            loadUIToDiv("input", "#allDevSpan", {
                                useInputReq:true,
                                hideLabel:true,
                                type:"radio",
                                id:"allDev",
                                name:"opType",
                                value:"2"
                            });
						    messageBox({messageType: "confirm",text: tipText , width: '500px', callback: submit});
						    window.setTimeout(function () {
                                if(equalArray(offlineArray, devArray))
                                {
                                    $("input:radio[name='opType']").attr("checked", false);
                                    $("#onlineDev").attr('disabled',"true");
                                    $("#allDev").attr("checked", true);
                                    devOpType = 2;
                                }
                                $("input:radio[name='opType']").change(function(){
                                    $("input:radio[name='opType']").attr("checked", false);
                                    $("#"+$(this).attr("id")).attr("checked", true);
                                    devOpType = $("input:radio[name='opType']:checked").val();
                                });
							}, 300);
					   }
				   }
			   }
			   else
			   {
				   func();
			   }
		  }
	});
}
//数组比较-----lianghaibo
function equalArray(a1, a2)
{
	if (a1.length != a2.length)
	{
		return false;
	}
	for (var i = 0; i < a1.length; i++) 
	{
		var j = isContain(a2,a1[i]);
		if (j < 0) 
		{
		    return false;
		}
		else
		{
		    a2.splice(j,1);
		}
	}
	return true;
}

function isContain(_arr,_element)
{
	for ( var i = 0; i < _arr.length; i++) 
	{
		if (_arr[i] == _element) 
		{
			return i;
		}
	}
	return -1;
};

function addAccDevAfterSubmit(func)
{
	var commTypeVal = $("#trCommType input[name='commType']:checked").val();
	if(commTypeVal == undefined || commTypeVal == null || commTypeVal == "")
	{
		commTypeVal = $("#idCommType").val();//搜索后添加设备
	}
	if (commTypeVal == "1" || commTypeVal == "2")//添加pull设备
	{
	    func();
	}
}
jQuery.validator.addMethod("IpValid", function(value, element)
{
    var ipv4_1 = value.split(".")[0];
    var ipv4_2 = value.split(".")[1];
    var ipv4_3 = value.split(".")[2];
    var ipv4_4 = value.split(".")[3];
    var idArray = element.id.split("_");
    var ipId = idArray[0];
    if (ipv4_1 == "" || ipv4_2 == "" || ipv4_3 == "" || ipv4_4 == "")
    {
        return false;
    }
    else
    {
        return true;
    }
}, I18n.getValue("common_jqMsg_required"));
