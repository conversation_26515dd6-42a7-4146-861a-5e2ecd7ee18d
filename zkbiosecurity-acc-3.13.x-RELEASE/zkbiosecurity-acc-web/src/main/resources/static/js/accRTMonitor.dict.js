// -*- coding: utf-8 -*-
//Create by Darcy 2013-01-22
if(typeof(catalogStateDict) == "undefined")
{
	catalogStateDict = new Array();
}

//提取js中的数据字典--实时监控
function getDoorStateDict()
{
	
	var value = null;
	for(var i =0; i<arguments.length; i++)
	{
		if(i == 0)
		{
			value = catalogStateDict[arguments[i]];
			if (typeof(value) == 'undefined')
		  	{
				return "";
		  	}
		}
		else
		{
			value = value[arguments[i]];
			if (typeof(value) == 'undefined')
		  	{
				return "";
		  	}
		}
	}
   	return (typeof(value) == 'string') ? value : value[0];
}
                                  
//监控中设计状态的数据字典。
var DOOR_COMM_STATE = 0;
var DOOR_SENSOR_STATE = 1;//门磁类型，涉及img和popup
var DOOR_ALARM_STATE = 2;
var DOOR_RELAY_STATE = 3
//三维数组，参数判断作为另外一层

/*catalogStateDict =
[
	["未知", [["不支持"],["锁定","解锁"]], "未知"],//0
	["无门磁", "关闭", "打开"],
	[
		["无", "报警", "无"],
		["无", "门被意外打开", "防拆", "胁迫密码开门", "胁迫指纹开门", "胁迫开门", "门开超时"],//旧固件门开超时不记为报警。
		["未知"]
	],
	["关闭", "打开", ["未知"]],//["不支持"]
];*/

catalogStateDict =
	[
		["${acc_rtMonitor_unknown}", [["${acc_rtMonitor_nonsupport}"],["${acc_rtMonitor_lock}","${acc_rtMonitor_unLock}"]], "${acc_rtMonitor_unknown}"],//0
		["${acc_rtMonitor_noSensor}", "${common_close}", "${common_open}"],
		[
			["${common_none}", "${acc_rtMonitor_openForce}", "${acc_rtMonitor_openTimeout}", "${acc_rtMonitor_openForce}"],
			["${common_none}", "${acc_rtMonitor_openForce}", "${acc_rtMonitor_tamper}", "${acc_rtMonitor_duressPwdOpen}", "${acc_rtMonitor_duressFingerOpen}", "${acc_rtMonitor_duressOpen}", "${acc_rtMonitor_openTimeout}", "${acc_newEventNo_58}","${acc_newEventNo_54}","${acc_newEventNo_55}","${acc_newEventNo_56}","${acc_newEventNo_57}"],//旧固件门开超时不记为报警。
			["${acc_rtMonitor_unknown}"]
		],
		["${common_close}", "${common_open}", ["${acc_rtMonitor_unknown}"]],//["不支持"]
	];

//图片路径获取-----动态获取。避免重复代码。
if(typeof(catalogImgDict) == "undefined")
{
	catalogImgDict = new Array();
}
//提取js中的数据字典--实时监控
function getDoorImgDict()
{
	var value = null;
	for(var i =0; i<arguments.length; i++)
	{
		if(i == 0)
		{
			value = catalogImgDict[arguments[i]];
			if (typeof(value) == 'undefined')
		  	{
				return "";
		  	}
		}
		else
		{
			value = value[arguments[i]];
			if (typeof(value) == 'undefined')
		  	{
				return "";
		  	}
		}
	}
   	return (typeof(value) == 'string') ? value : value[0];
}
function getDoorStateImgSrc(doorState)
{   
	
	return sysCfg.rootPath + "acc/images/doorState/icon/door_"+doorState+".jpg";
}

//监控中设计状态的数据字典。
var DOOR_COMM_STATE = 0;
var DOOR_SENSOR_STATE = 1;//门磁类型，涉及img和popup
var DOOR_ALARM_STATE = 2;
//原有N层if else就为N维数组
//或者 {"state": [[],[]]}
catalogImgDict =
[
	["offline", "", "disabled"],//离线
	[
		[//在线（旧固件）connect=1.alarmPre=0.
			["nosensor_old", "closed_old", "opened_old"],//alarm=0(sensor=0,1,2)
			["alarm_nosensor_old", "alarm_closed_old", "alarm_opened_old"],//alarm=1(sensor=0,1,2)
			["open_timeout_old", "open_timeout_old", "open_timeout_old"],//alarm=2(不算做报警)
			["alarm_nosensor_old", "alarm_closed_old", "alarm_opened_old"],//alarm=3(sensor=0,1,2)
		],
		[//在线（新固件）connect=1.alarmPre=1.
			[//无报警
				["nosensor", "closed", "opened"], //relay=0(locked,锁上、关闭。sensor=0,1,2)
				["nosensor_unlocked", "closed_unlocked", "opened_unlocked"]//relay=1(unlocked, sensor=0,1,2)
			],
			[//有报警（目前支持四种报警状态，在页面上图片并不区分，或者传过来，11,12,13,14,）
				["alarm_nosensor", "alarm_closed", "alarm_opened"], //relay=0(sensor=0,1,2)
				["alarm_nosensor_unlocked", "alarm_closed_unlocked", "alarm_opened_unlocked"]//relay=1(sensor=0,1,2)
			],
			[// 门开超时+报警（同时）
				["alarm_nosensor", "alarm_timeout", "alarm_timeout"], //relay=0
				["alarm_nosensor_unlocked", "alarm_timeout_unlocked", "alarm_timeout_unlocked"]//relay=1
			]
		]
	]
];


//该函数主要用于解析报警类型，以便门图片浮出框报警类型的正确显示，
//显示按报警类型优先级高的优先显示的原则,适用于新固件（美国固件) ----by liangm 20130411
function analyzeAlarmForDoorInfo(retAlarm)
{
	if((retAlarm & 8) == 8 &&  (retAlarm & 4) == 4)//胁迫指纹开门和胁迫密码开门
	{
		return 5;
	}
	else if((retAlarm & 8) == 8)//胁迫指纹开门
	{
		return 4;
	}
	else if((retAlarm & 4) == 4)//胁迫密码开门
	{
		return 3;
	}
	else if((retAlarm & 2) == 2)//防拆
	{
		return 2;
	}
	else if((retAlarm & 1) == 1)//意外开门
	{
		return 1;
	}
	else if((retAlarm & 16) == 16)//门开超时
	{
		return 6;
	}
	else if((retAlarm & 256) == 256)//常开报警
	{
		return 7;
	}
	else if((retAlarm & 512) == 512)//电池电压过低
	{
		return 8;
	}
	else if((retAlarm & 1024) == 1024)//请立即更换电池
	{
		return 9;
	}
	else if((retAlarm & 2048) == 2048)//非法操作
	{
		return 10;
	}
	else if((retAlarm & 4096) == 4096)//启用后备电源
	{
		return 11;
	}
	return 0;//无报警
}


//该函数主要用于解析报警类型，以便门图片的正确显示，
//显示按报警类型优先级高的优先显示的原则,适用于新固件（美国固件) ----by liangm 20130411
function analyzeAlarmForDoorPic(retAlarm)
{
	if((retAlarm & 16) == 16)//门开超时报警
	{
		return 2;
	}
	else if((retAlarm & 15) != 0)//仅报警
	{
		return 1;
	}
	else if(((retAlarm >> 8) & 31) != 0)//无线锁需要先右移8位,仅报警
	{
		return 1;
	}
	return 0;//即无报警，也无门开超时
}