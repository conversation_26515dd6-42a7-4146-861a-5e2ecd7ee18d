/*
File name: electro_map.js
Description: electro-map of ZKAccess5.0 Security System
Author: Darcy
Create date: 2010.10.26
Company: ZKSoftware
*/

I18n.load([
    "acc_map_imgSizeError"
]);

//标签关闭事件注册的全局变量
var mapAttachTabCloseEvent = false;


//对门循环处理top和left时的公共代码
var new_door_top = 0;
var new_door_left = 0;
var new_door_width = 0;
var new_door_width_ = 0
//实现地图的放大缩小--start
var multiple = 0;  //当前倍数，只能在-10至10这个倍数范围缩放
var zoomScale = 1;

/** 放大 */
function newZoomIn(id, bar) {
    var mapId = mapTree.getSelectedItemId().toString();
    if (mapId != "" && mapId != "0" && mapId.indexOf("_") < 0) {
        var $zoom_img = $("#id_map_" + mapId + " img:last");
        if ($("#reloadFlag").val() == 0) {
            multiple = 0;
        }
        if (multiple >= 9) {
            openMessage(msgType.info, I18n.getValue("base_map_zoomInError"));//地图宽度到达上限(倍数超过10倍)，不能再放大！
            return false;
        }
        else {
            multiple++; //放大
        }
        zoomScale = zoomScale * 1.30;
        $zoom_img.parent().css("transform", "scale(" + zoomScale + ")");

        $("#reloadFlag").val(1);
    }
    else {
        messageBox({messageType: "alert", text: I18n.getValue("base_map_plSelMap")});//${base_map_plSelMap}://请选择地图
    }
}

/** 缩小 */
function newZoomOut(id, bar) {
    var mapId = mapTree.getSelectedItemId().toString();
    if (mapId != "" && mapId != "0" && mapId.indexOf("_") < 0) {
        var $zoom_img = $("#id_map_" + mapId + " img:last");
        if ($("#reloadFlag").val() == 0) {
            multiple = 0;
        }
        if (multiple <= -9) {
            openMessage(msgType.info, I18n.getValue("base_map_zoomOutError1"));//地图宽度到达上限(倍数超过10倍)，不能再缩小！
            return false;
        }
        else {
            multiple--;  //缩小
        }
        zoomScale = zoomScale * 0.8;
        $zoom_img.parent().css("transform", "scale(" + zoomScale + ")");
        $("#reloadFlag").val(1);
    }
    else {
        messageBox({messageType: "alert", text: I18n.getValue("base_map_plSelMap")});//${base_map_plSelMap}://请选择地图
    }
}

function loop_doors($img, scale) {
    var door_width = parseFloat($img.parent().parent().css("width").replace("px", ""), 10);//parseFloat否则会出现16.5/2=8
    new_door_width = parseFloat(door_width) * scale;//门宽度按比例变化
    var door_width_ = parseFloat($img.css("width").replace("px", ""), 10);//parseFloat否则会出现16.5/2=8
    new_door_width_ = parseFloat(door_width_) * scale;
    var door_top = parseFloat($img.parent().parent().css("top").replace("px", ""), 10);//门的上边距
    var door_left = parseFloat($img.parent().parent().css("left").replace("px", ""), 10);//当前门的左边距
    //alert('door_left='+door_left+'door_top='+door_top+' scale='+scale+"  door_width="+door_width)
    //中点(door_top+door_width/2, door_left + door_width/2)按比例变化即可
    //alert(new_door_width)
    new_door_top = (door_top + door_width / 2) * scale - new_door_width / 2;
    new_door_left = (door_left + door_width / 2) * scale - new_door_width / 2;
}

function zoom_img($img, scale, left, top) {
    //debugger;
    var img_width = $img.width();//当前地图宽度。不带px
    var img_height = $img.height();
    var img_top = $img.offset().top;
    var img_left = $img.offset().left;
    //alert('top--='+img_top+" left="+img_left);
    var box_width = scale * img_width;//放大或缩小后的地图宽度
    var box_height = scale * img_height;
    var valid = true;//标记能够继续缩小
    //比较imgBox的长宽比与img的长宽比大小

    if (scale > 1) {
        if (multiple >= 10) {
            openMessage(msgType.info, I18n.getValue("base_map_zoomInError"));//地图宽度到达上限(倍数超过10倍)，不能再放大！
            return false;
        }
        else {
            multiple++; //放大
        }
    }
    if (scale < 1) {
        if (multiple <= -10) {
            openMessage(msgType.info, I18n.getValue("base_map_zoomOutError1"));//地图宽度到达上限(倍数超过10倍)，不能再缩小！
            return false;
        }
        else {
            multiple--;  //缩小
        }
    }

    //门图标联动--坐标和大小
    var $zoom_imgs = $img.parent().find(".can_drag");//取到当前地图上的门


    //要先检测所有的门的坐标是否有效，只要有一个无效，将无法继续，直接返回，不做缩小操作（扩大不受影响）
    $zoom_imgs.each(function () {
        loop_doors($(this), scale);

        if (scale < 1) {
            if (multiple < -10) {
                valid = false;
                //return false;
            }
        }

        if (!valid) {
            return false;//中断each
        }
    });

    if (!valid) {
        //门图标的位置（Top或Left）到达下限，请稍作调整后再进行缩小操作！
        openMessage(msgType.info, I18n.getValue("base_map_zoomOutError3"));
        return false;
    }

    $zoom_imgs.each(function () {
        loop_doors($(this), scale);
        //alert('new_door_left='+new_door_left+'new_door_top='+new_door_top)
        $(this).parent().parent().css("top", new_door_top);//上边距
        $(this).parent().parent().css("left", new_door_left);//左边距
        $(this).css("width", new_door_width_);
        $(this).parent().parent().css("width", new_door_width);//只控制width即可，height：auto，默认40px
    });
    //重新设置img的width和height
    $img.width(box_width);
    $img.height(box_height);
}


function reloadDevGrid() {
    var t = new Date().getTime();
    if (t < _nextTime) {
        return;
    }
    else {
        _nextTime = t + 2000;//间隔2秒之后点击才有效果
        ZKUI.Grid.get("${gridName}").reload();
    }
}

var accMapNextTime = 0;//下一次点击时间
function saveMapPos(id, bar) {
    var t = new Date().getTime();
    if (t < accMapNextTime) {
        return;
    }
    accMapNextTime = t + 2000;//间隔2秒之后点击才有效果
    var mapId = mapTree.getSelectedItemId().toString();
    var mapName = mapTree.getSelectedItemText().toString();
    if (mapId != "" && mapId != "0" && mapId.indexOf("_") < 0) {
        var posArray = new Array();//二维
        var entityId = "";
        var entityWidth = "40";
        var entityType = "";
        var entityLeft = "";
        var entityTop = "";
        var mapPosId = "";
        var mapWidth = $("#id_map_" + mapId + " .map").width();
        var mapHeight = $("#id_map_" + mapId + " .map").height();
        $("#id_map_" + mapId + " .can_drag").each(function () {
            mapPosId = $(this).attr("data");
            entityWidth = $(this).width();//40
            var $posItem = $("#item_" + $(this).attr("data").split("_")[0]);
            entityLeft = parseInt($posItem.css("left"));//非$(this)
            entityTop = parseInt($posItem.css("top"));
            posArray.push(new Array(mapPosId, entityWidth, entityLeft, entityTop))
        });
        var stamp5 = new Date().getTime();
        var path = id + "&mapId=" + mapId + "&mapWidth=" + mapWidth + "&mapHeight=" + mapHeight + "&stamp=" + stamp5 + "&name=" + encodeURIComponent(mapName);
        $.ajax({
            url: path,
            type: "post",
            data: {
                posArray: posArray.toString()
            },
            success: function (result) {
                dealRetResult(eval(result), function () {
                    mapTree.selectItem(mapId, true, true);
                });
            }
        });
        /*$.post(path, {"posArray": posArray.toString()}, function(result){
            dealRetResult(result);
        }, "json");*/
    }
    else {
        messageBox({messageType: "alert", text: I18n.getValue("base_map_plSelMap")});
        //messageBox({messageType:"alert",text: "${base_map_plSelMap}"});//${base_map_plSelMap}://请选择地图
    }
}

function zoomIn(id, bar) {
    var mapId = mapTree.getSelectedItemId().toString();
    if (mapId != "" && mapId != "0" && mapId.indexOf("_") < 0) {
        var $zoom_img = $("#id_map_" + mapId + " img:last");
        if ($("#reloadFlag").val() == 0) {
            multiple = 0;
        }
        zoom_img($zoom_img, 1.25, 0, 0);
        $("#reloadFlag").val(1);
    }
    else {
        messageBox({messageType: "alert", text: I18n.getValue("base_map_plSelMap")});//${base_map_plSelMap}://请选择地图
    }
}


function zoomOut(id, bar) {
    var mapId = mapTree.getSelectedItemId().toString();

    if (mapId != "" && mapId != "0" && mapId.indexOf("_") < 0) {
        var $zoom_img = $("#id_map_" + mapId + " img:last");
        if ($("#reloadFlag").val() == 0) {
            multiple = 0;
        }
        zoom_img($zoom_img, 0.8, 0, 0);
        $("#reloadFlag").val(1);
    }
    else {
        messageBox({messageType: "alert", text: I18n.getValue("base_map_plSelMap")});//${base_map_plSelMap}://请选择地图
    }
}


var isFullScreen = false;
var menuWith = window.top.system.contentLayout.cells(IS_ENABLE_RTL ? "b" : "a")._getWidth();

$("#leftHeader").click(function() {
	 menuWith = window.top.system.contentLayout.cells(IS_ENABLE_RTL ? "b" : "a")._getWidth();
});
function fullScreen(id, bar) {
    var path = id;
    var toolItem = bar.getToolItemById("fullScreen");
    if (!isFullScreen) {
        menuWith = window.top.system.contentLayout.cells(IS_ENABLE_RTL ? "b" : "a")._getWidth();
        bar.setIcon("fullScreen", "base_map_exitFullScreen.png", "common_op_exitFullScreen");
        /*toolItem.setItemImage("base_map_exitFullScreen.png");
        toolItem.setItemText(I18n.getValue("common_op_exitFullScreen"));*/
        myLayouts[layoutName].cells(IS_ENABLE_RTL ? "b" : "a").collapse();
        myLayouts[layoutName].cells("c").collapse();	//将实时事件窗口缩小
        if (window.top.system.contentLayout && isPersonLastAddr == "-1" && !system.fullFlag) {
            window.top.system.contentLayout.cells(IS_ENABLE_RTL ? "b" : "a").collapse();
        }
        if (navigator.userAgent.toLowerCase().indexOf("chrome") != -1) {   //解决谷歌浏览器布局问题
            $(myLayouts[layoutName].base).width($(myLayouts[layoutName].base).parent().width());
            myLayouts[layoutName].setSizes();
            myLayouts[layoutName].cells(IS_ENABLE_RTL ? "b" : "a").setWidth(200);
        }
        isFullScreen = true;
    }
    else {
        var temWidth = window.top.system.contentLayout.cells(IS_ENABLE_RTL ? "b" : "a")._getWidth();
        if (window.top.system.contentLayout && !system.fullFlag) {
            window.top.system.contentLayout.cells(IS_ENABLE_RTL ? "b" : "a").expand();
            window.top.system.contentLayout.cells(IS_ENABLE_RTL ? "b" : "a").setWidth(menuWith);
        }
        bar.setIcon("fullScreen", "base_map_fullScreen.png", "common_op_fullScreen");
        /*toolItem.setItemImage("base_map_fullScreen.png");
        toolItem.setItemText(I18n.getValue("common_op_fullScreen"));*/
        myLayouts[layoutName].cells(IS_ENABLE_RTL ? "b" : "a").expand();
        myLayouts[layoutName].cells("c").expand();		//将实时事件窗口恢复
        if (navigator.userAgent.toLowerCase().indexOf("chrome") != -1) {   //解决谷歌浏览器布局问题
            $(myLayouts[layoutName].base).width($(myLayouts[layoutName].base).parent().width());
            myLayouts[layoutName].setSizes();
            //谷歌浏览器下宽度无法自动调整
            myLayouts[layoutName].cells(IS_ENABLE_RTL ? "b" : "a").setWidth(200);
        }
        isFullScreen = false;
    }
    resizeUI();
}
Web.attachEvent("onFullScreen", function() {
    var sysCode = CONST.getValue("sysCode");
    var menuId = CONST.getValue(sysCode + "_menuId");
    if(!arguments[0] && "accMap.do" == menuId && window.top.system.contentLayout) {
        //window.top.system.contentLayout.cells(IS_ENABLE_RTL ? "b" : "a").expand();
        window.top.system.contentLayout.cells(IS_ENABLE_RTL ? "b" : "a").setWidth(menuWith);
    }
})



function editMap(id, bar) {
    var mapId = mapTree.getSelectedItemId().toString();
    if (mapId != "" && mapId != "0" && mapId.indexOf("_") < 0) {
        var opts = {
            path: id + mapId,
            width: 416,
            height: 296,
            title: I18n.getValue("common_op_edit"),
            gridName: "gridbox"
        };
        DhxCommon.createWindow(opts);
    }
    else {
        messageBox({messageType: "alert", text: I18n.getValue("base_map_plSelMap")});//${base_map_plSelMap}://请选择地图
    }
}

function delMap(id, bar) {
    var mapId = mapTree.getSelectedItemId().toString();
    if (mapId != "" && mapId != "0" && mapId.indexOf("_") < 0) {
        var mapName = mapTree.getSelectedItemText();
        var submit = function (result) {
            if (result) {
                $.ajax({
                    url: id,
                    type: "post",
                    data: {ids: mapId, name: mapName},
                    success: function (result) {
                        dealRetResult(eval(result), function () {
                            reloadTree();
                        });
                    }
                });
            }
            else {
                // 取消
            }
            return true; //close
        };
        messageBox({messageType: "confirm", text: I18n.getValue("base_map_sureDelMap"), callback: submit});//${base_map_sureDelMap}://你确定要删除当前地图吗?
    }
    else {
        messageBox({messageType: "alert", text: I18n.getValue("base_map_plSelMap")});//${base_map_plSelMap}://请选择地图
    }
}


function addEntityToMap(id, bar) {
    var path = id;
    var mapId = mapTree.getSelectedItemId().toString();
    if (mapId != "" && mapId != "0" && mapId.indexOf("_") < 0) {
        //添加门的同事要先保存，保证添加的门的大小同步到数据库
        saveMapPos("/accMap.do?saveMapPos&flag=addEntityToMap", null);
        path = path + "&mapId=" + mapId;
        setTimeout(function () {
            entityWidth(path, mapId);
        }, 100);
    }
    else {

        messageBox({messageType: "alert", text: I18n.getValue("base_map_plSelMap")});//${base_map_plSelMap}://请选择地图
    }
}

//获取实体的width并且向后台发送请求添加窗体
function entityWidth(path, mapId) {
    $.ajax({
        type: "GET",
        url: "/accMapPos.do?getEntityWidthByMapId&mapId=" + mapId,//取门,摄像头的width
        dataType: "text",
        async: true,
        success: function (result) {
            var dataObj = JSON.parse(result);
            path = path + "&width=" + dataObj.data;
            //openWindow(path);//向后台发送请求并打开页面
            var title;
            if (path.indexOf("AccDoor") > 0) {
                title = I18n.getValue("acc_map_addDoor");
            }
            else {
                title = I18n.getValue("acc_map_addChannel");
            }
            var opts = {
                path: path,
                width: 880,
                height: 510,
                title: title,
                gridName: "gridbox"

            };
            DhxCommon.createWindow(opts);
        }
    });
}

/**
 * 添加视频到地图
 * @param id
 * @param bar
 */
function addChannelToMap(id, bar) {
    //先判断是否有视频模块和视频设备
    $.ajax({
        type: "POST",
        url: "/accMap.do?isExistVid",
        dataType: "json",
        async: true,
        success: function (result) {
            if (result.ret == "ok") {
                addEntityToMap(id, bar)
            }
            else {
                messageBox({messageType: "alert", text: result.msg});
            }
            // addEntityToMap(id,bar)
        }
    });
}


/**
 * 检测上图图片大小
 */
function checkImgSize(e) {
    var file = e.currentTarget;
    var fileSize = 0;
    if (!$.browser.msie) {
        fileSize = file.files[0].size;//火狐等标准取值办法
    }
    if (fileSize > 1024 * 1024 * 5)//5M
    {
        openMessage(msgType.info, I18n.getValue("acc_map_imgSizeError", 5));//${base_map_plSelMap}://请选择地图
        return false;
    }
    else {
        return true;
    }
}