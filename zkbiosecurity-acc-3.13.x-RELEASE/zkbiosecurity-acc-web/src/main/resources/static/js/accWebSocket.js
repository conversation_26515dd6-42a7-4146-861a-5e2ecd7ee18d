/**
 * 创建WebSocket并与服务端建立连接
 */
var webSocket = null;
function createWebSocket(func)
{

	var address = "";
	
	var eventClientId = "eventData"+new Date().getTime();
	var devClientId = "devState" +  new Date().getTime();
	if(window.location.protocol.indexOf("https:")!=-1)
	{
		address = "wss://" + window.location.host + "/accWebSocket/"+devClientId+"/"+eventClientId;
	}
	else if(window.location.protocol.indexOf("http:")!=-1)
	{
		address = "ws://" + window.location.host + "/accWebSocket/"+devClientId+"/"+eventClientId;
	}
    if(!window.WebSocket)
    {
    	openMessage("warning", "WebSocket is not supported!");
    }
    else if (window.WebSocket)
	{
    	webSocket = new WebSocket(address);
	}
	else if (window.MozWebSocket)
	{
		webSocket = new MozWebSocket(address);
	}
	    
	if (webSocket != null)
	{
		//打开连接方法
		webSocket.onopen = function(ret)
		{
			webSocket.send("link");
		};
		//关闭连接方法
		webSocket.onclose=function(ret)
		{
			webSocket.send("destory");
		}
		//接收消息方法
		webSocket.onmessage = function(ret)
		{
			if(typeof ret != 'undefined')
			{
				var retData = eval("("+ret.data+")");
				func(retData);
				
			}
		}
	}
	 
}