//弹出框开始start
//object光标移上去的对象，event为事件。divInfo为弹出框的位置div
var heightTaskItem = 0;
var halfObjectLen = 20;//正方形的边长一半.不随事件点而变化，只跟对象位置有关。
var imgWidth = 40;
function getPopUpInfo(object, event)
{
	var divInfo = $("#id_div_info");
	if(divInfo.css("display") == "none")//之前隐藏，则放开
	{
		//已测试ie8,ff18,google chrome22,猎豹v2.1.11,360 6.0.2.
		var grandparentNodeWidth = $(object.parentNode.parentNode).width();
		var grandparentNodeHeight = $(object.parentNode.parentNode).height();
		var popupTextWidth = $(object.parentNode).find(".dhx_item_popupText").width();  
		var popupTextHeight = $(object.parentNode).find(".dhx_item_popupText").height();
		var selfLeftPos = $(object).position().left;
		var selfTopPos = $(object).position().top;
		var posX = selfLeftPos + halfObjectLen + imgWidth/4;//获取相对(父元素)位置:offset()为绝对为位置
		var posY = selfTopPos + halfObjectLen - imgWidth/4;//+ heightTaskItem + 6;
		if(selfLeftPos/grandparentNodeWidth > 0.75)
		{
			//posX = selfLeftPos - popupTextWidth - halfObjectLen - imgWidth/4;
			startWidth=$("#dataContainer").width();
			if(startWidth-selfLeftPos > $(".poPupInfo").width())
			{
				posX = selfLeftPos + imgWidth/2+halfObjectLen/2;
		   	}
			
		   	else 
			{
		   		posX = selfLeftPos - $(".poPupInfo").width() +imgWidth/2+halfObjectLen/2 ;
			}
		
		}
		if(selfTopPos/grandparentNodeHeight > 0.66)
		{
			posY = selfTopPos - popupTextHeight - imgWidth/2;
			
		}
		var tblInfoTd = $(divInfo.find("td")[0]);//divInfo.lastChild;只有一个td
		var popupText = $(object.parentNode).find(".dhx_item_popupText").html();//dhx_item_popupText;//需要移植出去。暂时放这里。
		tblInfoTd.empty().append("<div style='font-family: Arial, Helvetica, Sans-serif;font-size: 12px;font-weight: bold;color: #688060;margin:0 0 4px 0;'>${acc_rtMonitor_doorState}</div>"+popupText);
		//${acc_rtMonitor_doorState}为国际化标签，门状态
		var marginLeft = posX;//posX + "px"不能是负值！！负值需要判断。需要去掉-400.
		var marginTop = posY + halfObjectLen;//新的Top
		divInfo.css({"top": marginTop + "px", "left": marginLeft, "z-index":2, "position": "absolute", "display": "inline"});
		divInfo.mouseover(function(){
			divInfo.css({"top": marginTop + "px", "left": marginLeft, "z-index":2, "position": "absolute", "display": "inline"});
			divInfo.attr("cursorIn", 1);
        }).mouseout(function(){
        	divInfo.attr("cursorIn", 0);
         	divInfo.css("display", "none");
        });
    }
    else
    {
   		divInfo.css("display", "none");
    }

	//alert(33)
};
//关闭popup弹出框。
function closePopUpInfo()
{
	var divInfo = $("#id_div_info");
	//处理两个div点不上的问题。
   	if(divInfo.attr("cursorIn") == 0)
   	{
   		divInfo.css("display", "none");
   	}
}

