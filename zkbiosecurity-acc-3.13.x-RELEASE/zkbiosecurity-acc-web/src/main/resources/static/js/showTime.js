showTimeInfo();
showWeekInfo();
showDateInfo();
/* 时间显示 */
function showTimeInfo()
{
	var nowTime = showTime();
	var seconds = showSecond();
	var html = nowTime + seconds;
	$('#timeInfo_lcd').html(html);
}

/* 星期显示 */
function showWeekInfo()
{
	var nowWeek = showWeek();
	$('#weekInfo_lcd').html(nowWeek);
}

/* 日期显示 */
function showDateInfo()
{
	var nowDate = showDate();
	$('#dateInfo_lcd').html(nowDate);
} 

function showTime()
{
	var nowDate = new Date();
	var hour = nowDate.getHours();
	var minute = nowDate.getMinutes();
	var day = nowDate.getDay();
	var lastTime;
	if(hour < 10)
	{
		hour = "0"+ hour;
	}
	if(minute <10)
	{
		minute = "0"+ minute;
	}
	lastTime = hour + ":" + minute;
	return lastTime;
}
function showSecond()
{
	var seconds = new Date().getSeconds();
	if(seconds < 10)
	{
		seconds = "0"+ seconds;
	}
	return ":" + seconds;
}
function showWeek()
{
	var day = new Date().getDay();
	switch (day)
	{
		case 0:
			day = I18n.getValue("common_sunday");
			break;
		case 1:
			day = I18n.getValue("common_monday");
		break;
			
		case 2:
			day = I18n.getValue("common_tuesday");
		break;
			
		case 3:
			day = I18n.getValue("common_wednesday");
		break;
			
		case 4:
			day = I18n.getValue("common_thursday");
		break;
			
		case 5:
			day = I18n.getValue("common_friday");
		break;
			
		case 6:
			day = I18n.getValue("common_saturday");
		break;
		
	}
	return day;
}
function showDate()
{
	var nowDate = new Date();
	var year = nowDate.getFullYear();
	var month = nowDate.getMonth() + 1;
	var date = nowDate.getDate();
	var today;
	if(date <10)
	{
		date = "0"+ date;
	}
	if(month <10)
	{
		month = "0"+ month;
	}
	if("${Application['system.language']}" != "zh_CN")
	{
		today = year + "/" + month + "/" + date;
	}
	else
	{
		today = month + "/" + date + "/" + year ;
	}
	return today;
}

/* 定时触发 1秒触发更新时间*/
window.setInterval("showTimeInfo();showWeekInfo();showDateInfo();", 1000); 