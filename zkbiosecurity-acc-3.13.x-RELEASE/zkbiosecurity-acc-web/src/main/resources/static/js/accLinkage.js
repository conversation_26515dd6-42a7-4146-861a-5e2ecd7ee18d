// 获取联动需要的数据：触发条件、输入点、输出点
function getLinkData(url, type)
{
	$.ajax({
	    type : "GET",
	    async : false,
	    dataType : "json",
	    url : url,
	    success : function(result)
	    {
		    if (type == "triggerOpt")
		    {
			    triggerOptArray = result.data;
		    }
			else
			{
				doorOutputArray = result.data.doorOutputArray;
		    	auxOutOutputArray = result.data.auxOutOutputArray;
		    	ctlAllRelayFunOn = result.data.ctlAllRelayFunOn;
			    inputArray = result.data.inputArray;
			    
			    //判断已选联动条件与新选择的联动类型是否一样（用于第四步的回选）
			    // 输入点非仅"任意"选项
			    if (inputArray.length > 1) {
                    var itemId = inputArray[1].id;
                    if(itemId.indexOf("Door") >= 0)
                    {
                        inputTypeNew = "door";
                        if(inputArray[1].item != undefined)
                        {
                            inputTypeNew = "reader";
                        }
                    }
                    else//不包涵“door”的是辅助输入
                    {
                        inputTypeNew = "auxIn";
                    }
			    } else {
			        inputTypeNew = "any";
			    }
		    }
	    }
	});
}

// 判断联动条件设置是否重复
function checkTriggerOpt()
{
	var checkRet = true;
	$.ajax({
	    type : "GET",
	    url : "/accLinkage.do?checkTriggerOpt&devId=" + $("#deviceId").val() + "&triggerOpt=" + $("#triggerCond").val() + "&inAddr=" + $("#inputTree").val() + "&linkageId=" + $("#accLinkId").val(),
	    dataType : "json",
	    async : false,
	    success : function(result)
	    {
		    if (result.data.length > 0)
		    {
			    checkRet = result.data.toString();
		    }
	    }
	});
	return checkRet;
}

// 初始化树
function initLinkTree(objId, dataArray, type)
{
	if(dataArray != "")
	{
		if (type == "triggerOptTree")
		{
			var objTree = new dhtmlXTreeObject(objId, "100%", "100%", "linkTrigger");
		}
		else
		{
			var objTree = new dhtmlXTreeObject(objId, "100%", "100%", 0);
		}
		objTree.setSkin(sysCfg.dhxSkin);
		objTree.setImagePath(sysCfg.treeImgPath);
		objTree.enableCheckBoxes(1);
		if (type == "triggerOptTree")
		{
			objTree.enableThreeStateCheckboxes(true);// 包含下级
		}
		objTree.enableSmartXMLParsing(true);
		objTree.setDataMode("json");
		/*var dataJson = {"id" : 0,  "item" : dataArray};
		var dataJson = type == "triggerOptTree" ? {
		    "id" : 0,
		    "item" : dataArray
		} : {
		    "id" : 0,
		    "item" : [ dataArray ]
		};*/
		var dataJson = "";
		if(type == "doorOutputTree" || type == "auxOutOutputTree")
		{
			if(ctlAllRelayFunOn)// 判断设备输出点是否支持所有选项
			{
				dataJson = {"id" : 0, "item" : [ dataArray ]};
			}
			else
			{
				dataJson = {"id" : 0,  "item" : dataArray};
			}
		}
		else if (type == "triggerOptTree")
		{
			dataJson = {"id" : "linkTrigger",  "item" : dataArray};
		}
		else
		{
			dataJson = {"id" : 0,  "item" : dataArray};
		}
		objTree.loadJSONObject(dataJson);
	
		if (type == "triggerOptTree")// 触发条件
		{
			triggerOptTree = objTree;
			objTree.setCheck("0_parent", 0);
			objTree.setCheck("1_parent", 0);//全部取消勾选后再根据页面上的值选择
	    	$(":input[name='triggerCondChecked']").each(function(){
				if($(this).siblings("input[type=checkbox]").attr("checked") == "checked")
				{
					objTree.setCheck($(this).val(), 1);
				}
			});
			triggerOptTree.attachEvent("onCheck", function(id)
			{
				onlyCheckOneParent(triggerOptTree);
				 validButton(triggerOptTree);
			});
		}
		else
		{
			//objTree.disableCheckbox("_AccDevice", 1);// 禁用设备复选框
	
			objTree.attachEvent("onCheck", function(id)
			{
				if (id == "0_any")// 任意
				{
					disableSubItems(objTree, 0);
				}
				else if (objTree.hasChildren(id) > 0)// 有子节点
				{
					disableSubItems(objTree, id);
				}
				$("#" + type).val(objTree.getAllChecked());
			});
	
			if (objTree.getAllChecked() != "")// 编辑时
			{
				var checkedItems = objTree.getAllChecked().toString().split(",");
				if (checkedItems == "0_any")
				{
					disableSubItems(objTree, 0);
				}
				else
				{
					for (var i = 0; i < checkedItems.length; i++)
					{
						if (objTree.hasChildren(checkedItems[i]))
						{
							disableSubItems(objTree, checkedItems[i]);
						}
					}
				}
			}
			//type == "inputTree" ? inputTree = objTree : outputTree = objTree;
			if (type == "inputTree")
			{
				inputTree = objTree;
			}
			else if (type == "doorOutputTree")
			{
				doorOutputTree = objTree;
			}
			else
			{
				auxOutOutputTree = objTree;
			}
		}
	}
}

// 禁用子节点
function disableSubItems(objTree, id)
{
	var subItems = objTree.getAllSubItems(id).toString();
	var subItemArray = subItems.split(",");
	var checkedState = null; //id == "_AccDevice" ? objTree.isItemChecked("0_any") == 1 ? 1 : 0 : objTree.isItemChecked(id) == 1 ? 1 : 0;
	if (id == 0)
	{
		checkedState = objTree.isItemChecked("0_any") == 1 ? 1 : 0;
	}
	else
	{
		checkedState = objTree.isItemChecked(id) == 1 ? 1 : 0;
	}
	var curCheckedState = checkedState == 1 ? 0 : 1;
	for (var i = 0; i < subItemArray.length; i++)
	{
		if (id == 0 && subItemArray[i] != "0_any")
		{
			objTree.disableCheckbox(subItemArray[i], 0);// 先解禁
			objTree.setCheck(subItemArray[i], 0);// 去掉勾选
		}
		objTree.disableCheckbox(subItemArray[i], checkedState);// 禁用子节点: 1 禁用，0 启用
	}
	if (id == 0)
	{
		objTree.disableCheckbox("0_any", 0);// 放开“任意”
	}
}

var checkedParentIds = new Array();// 选中的父节点
function onlyCheckOneParent(tree)
{
	var parentIds = new Array();
	var subIds = new Array();
	subIds = getCheckedSubIds(tree.getAllChecked());
	parentIds = getCheckedParentIds(tree, subIds);

	if (parentIds.length == 1)
	{
		checkedParentIds = parentIds;
	}
	else
		if (parentIds.length > 1)
		{
			var currentId = new Array();
			for (var i = 0; i < parentIds.length; i++)
			{
				for (var j = 0; j < checkedParentIds.length; j++)
				{
					if (parentIds[i] == checkedParentIds[j])
					{
						tree.setSubChecked(checkedParentIds[j], false);
					}
					else
					{
						currentId[currentId.length] = parentIds[i];
					}
				}
			}
			checkedParentIds = currentId;
		}
}

//根据子节点获取父节点id
function getCheckedParentIds(tree, subIds)
{
	var parentIds = new Array();
	for (var i = 0; i < subIds.length; i++)
	{
		var parentId = tree.getParentId(subIds[i]);
		if ($.inArray(parentId, parentIds) < 0 && new RegExp("_").test(parentId))
		{
			parentIds[parentIds.length] = parentId;
		}
	}
	return parentIds;
}

//获取选中的子节点id
function getCheckedSubIds(ids)
{
	var checkedIds = new Array();
	if (new RegExp("_").test(ids))
	{
		var checkedIdArray = ids.split(",");
		for (var i = 0; i < checkedIdArray.length; i++)
		{
			if (checkedIdArray[i].indexOf("_parent") < 0)
			{
				checkedIds[checkedIds.length] = checkedIdArray[i];
			}
		}
	}
	else
	{
		new RegExp(",").test(ids) ? checkedIds = ids.split(",") : checkedIds[0] = ids;
	}
	return checkedIds;
}

// 邮箱验证
jQuery.validator.addMethod("moreMailValid", function(val, element)
{
	//var mailReg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
	var mailReg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*$/;
	if (val == "")
	{
		return true;
	}
	else
	{
		var mailArray = val.replace(/;/g, ",").split(",");//统一将输入的分号替换成逗号，支持混合输入
		for (var i = 0; i < mailArray.length; i++)
		{
			if (mailArray[i] != "" && !(this.optional(element) || (mailReg.test(mailArray[i]))))
			{
				return false;
			}
		}
		return true;
	}
}, I18n.getValue("common_email_inputEmailError"));

//验证每个邮箱地址的长度
jQuery.validator.addMethod("mailLenghthValid", function(val, element)
{
	var mailArray = val.replace(/;/g, ",").split(",");//统一将输入的分号替换成逗号，支持混合输入
	for (var i = 0; i < mailArray.length; i++)
	{
		if (mailArray[i].length > 100)
		{
			return false;
		}
	}
	return true;
}, I18n.getValue("common_email_exceedMaxLen"));

jQuery.validator.addMethod("mobileNoLengthValid", function (val, element) {
    var mobileNoArray = val.replace(/;/g, ",").split(",");//统一将输入的分号替换成逗号，支持混合输入
    for (var i = 0; i < mobileNoArray.length; i++) {
        if (mobileNoArray[i].length > 20) {
            return false;
        }
    }
    return true;
}, I18n.getValue("pers_import_phoneTooLong"));

jQuery.validator.addMethod("mobileNoValid", function (val, element) {
    var mobileNoArray = val.replace(/;/g, ",").split(",");//统一将输入的分号替换成逗号，支持混合输入
    var pattenChar =  /^[0-9,;]*$/;
    for (var i = 0; i < mobileNoArray.length; i++) {
        return pattenChar.test(val);
    }
    return true;
}, I18n.getValue("common_jqMsg_number"));


// 时间禁用启用根据ID
function accTimeDisableById(obj,id)
{
	if (obj.checked)
	{
		$(id).attr('disabled', false);
	}
	else
	{
		$(id).val($(id).attr('default'));
		$(id).valid();
		$(id).attr('disabled', true);
	}
}

//将触发条件信息保存到页面
function setTriggerInfoToPage(tree)
{
	var triggerCond = "";
	var triggerCondName = "";
	var temptriggerNoArray = tree.getAllChecked().split(",");
	var parentId = "";
	var parentIdIndex = -1;
	for (var i = 0; i < temptriggerNoArray.length; i++)
	{
		if (temptriggerNoArray[i].indexOf("_parent") >= 0)
		{
			parentId = temptriggerNoArray[i];
		}
	}
	if ("" != parentId && $.inArray(parentId, temptriggerNoArray) >= 0)
	{
		parentIdIndex = $.inArray(parentId, temptriggerNoArray);
		temptriggerNoArray.splice(parentIdIndex, 1);
	}
	var tempCheckedIds = temptriggerNoArray;
	if (temptriggerNoArray.length == 1)
	{
		triggerCond = temptriggerNoArray.toString();
	}
	else if (temptriggerNoArray.length > 1)
	{
		for (var i = 0; i < temptriggerNoArray.length; i++)
		{
			if (i == temptriggerNoArray.length - 1)
			{
				triggerCond += temptriggerNoArray[i];
			}
			else
			{
				triggerCond += temptriggerNoArray[i] + ",";
			}
		}
	}
	$("#triggerCond").val(triggerCond);

	if (tempCheckedIds.length == 1)
	{
		triggerCondName = tree.getItemText(tempCheckedIds);
	}
	else if (tempCheckedIds.length > 1)
	{
		for (var i = 0; i < tempCheckedIds.length; i++)
		{
			if (i == tempCheckedIds.length - 1)
			{
				triggerCondName += tree.getItemText(tempCheckedIds[i]);
			}
			else
			{
				triggerCondName += tree.getItemText(tempCheckedIds[i]) + ",";
			}
		}
	}
	$("#triggerCondName").val(triggerCondName);
}

/**
 * 检测视频联动选项和联动输出点是否至少选择一项
 * 
 * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
 * @since 2014年12月2日 下午3:48:57
 */
function checkVidLinkage()
{
	var flag = false;
	var popUpVideoObj = document.getElementById("popUpVideo");
	if(popUpVideoObj && popUpVideoObj.checked)
	{
		flag = true;
	}
	if(document.getElementById("record").checked)
	{
		flag = true;
	}
	if(document.getElementById("linkCapture").checked)
	{
		flag = true;
	}
	var outputDoorAddr = $("#doorOutputTree").val();
	var outputAuxOutAddr = $("#auxOutOutputTree").val();
    var vdbExtensionIds = $("#vdbExtensionId").val();
    var ivrId = "";
    if(ZKUI.Combo.PULL.vdbIvrCombo) {
        ivrId = ZKUI.Combo.PULL.vdbIvrCombo.combo.getSelectedValue();
    }
	if((outputDoorAddr && outputDoorAddr != "") || (outputAuxOutAddr && outputAuxOutAddr != "")) {
		flag = true;
	} else if ($("#id_mailAddr").val() != "") {
	    flag = true;
	} else if ($("#id_mobileNo").val() && $("#id_mobileNo").val() != "") {
	    flag = true;
	} else if ($("#digiEventIds").val() && $("#digiEventIds").val() != "") {
	    flag = true;
	} else if ($("#lineContactId").val() && $("#lineContactId").val() != "") {
	    flag = true;
	} else if ($("#id_whatsappMobileNo").val() && $("#id_whatsappMobileNo").val() != "") {
	    flag = true;
	} else if (ZKUI.Tree.get("iasPartitionTreeId") && ZKUI.Tree.get("iasPartitionTreeId").getValue() != "") {
        flag = true;
	} else if((vdbExtensionIds && vdbExtensionIds != "") && (ivrId && ivrId != "")) {
        flag = true;
    }
	return flag;
}

/**
 * 用于联动选择参数后勾选或取消勾选后，添加或删除要提交的数据
 * 
 * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
 * @since 2014年12月24日 上午10:22:17
 * @param obj checkbox对象
 * @param idField 存储对应实体id的隐藏域id
 * @param nameField 存储对应实体name的隐藏域id
 */
function operate(obj, idField, nameField)
{
    var id = $(obj).val();
    var parentObj = $(obj).parent();
    // 父类下一元素的文本内容
    var name = $(parentObj).next().text();
	if($(obj).attr("checked") == undefined)
    {
		var oldIds = $("#" + idField).val();
		var oldIdArry = oldIds.split(",");
		var indexId = 0;
		var idString = "";
		for (var i = 0; i < oldIdArry.length; i++)
		{
			if (id == oldIdArry[i])
			{
				indexId = i
				break;
			}
		}
		oldIdArry.splice(indexId, 1);
		if (oldIdArry.length > 0)
		{
			for (var i = 0; i < oldIdArry.length; i++)
			{
				idString += oldIdArry[i] + ",";
			}
			idString = idString.substring(0, idString.length - 1);
		}
		$("#" + idField).val(idString);

		var nameString = $("#" + nameField).val().replace(name, "");
		var oldNames = $("#" + nameField).val();
		var indexName = oldNames.indexOf(name);
		var nameString = "";
		if(indexName + name.length == oldNames.length)
		{
			nameString = $("#" + nameField).val().replace("," + name, "");
			if(indexName == 0)
			{
				nameString = $("#" + nameField).val().replace(name, "");
			}
		}
		else
		{
			nameString = $("#" + nameField).val().replace(name + ",", "");
		}
		$("#" + nameField).val(nameString);
    }
	else if($(obj).attr("checked") == "checked")
    {
		if($("#" + idField).val() == "")
		{
			$("#" + idField).val($("#" + idField).val() + id);
			$("#" + nameField).val($("#" + nameField).val() + name);
		}
		else
		{
			$("#" + idField).val($("#" + idField).val() + "," + id);
			$("#" + nameField).val($("#" + nameField).val() + "," + name);
		}
    }
	if(idField == "triggerCond")
	{
		$("#selectedIds").val($("#" + idField).val());
	}
}

/**
 * 组装页面显示数据
 * 
 * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
 * @since 2014年12月25日 上午10:36:59
 * @param idField 存储对应实体id的隐藏域id
 * @param nameField 存储对应实体name的隐藏域id
 * @param displayField 用于显示的div的id
 */
function buildPageData(idField, nameField, displayField)
{
	var html = "";
	if($("#" + idField).val() != "")
	{
		var idArray = $("#" + idField).val().split(",");
		var nameArray = $("#" + nameField).val().split(",");
		for (var i = 0; i < idArray.length; i++) {
		    // 去空格处理,避免因digifort的id和name一致,id中包含空格,导致控件加载异常
		    var trimId = idArray[i].replace(/\s/g, "");
            html += "<span id='accTriggerCondSpan"+ trimId +"'></span><span>" + nameArray[i] + "</span><br>";;
            loadUIToDiv("input", "#accTriggerCondSpan" + trimId, {
                useInputReq:true,
                hideLabel:true,
                type:"checkbox",
                id:"idTriggerCond_" + idArray[i],
                name:idField + "Checked",
                value:idArray[i],
                onchange:"operate(this, '" + idField + "', '" + nameField + "')",
                trueValue:idArray[i],
                falseValue:"-1",
                eventCheck:true
            });
		}
	}
	$("#" + displayField).html(html);
}

/**
 * 获取和组装digifort页面数据
 * @param idField
 * @param nameField
 * @param displayField
 */
function buildDigifortEvents(idField, nameField, displayField)
{
    $.ajax({
        type : "GET",
        async : false,
        dataType : "json",
        url : "accLinkage.do?getDigifortGlobalEvents&type=checkExist&linkageId=" + $("#accLinkId").val(),
        success : function(data)
        {
            var filters = data.ret == "ok" ? data.data : [];
            var html = "";
            if($("#" + idField).val() != "")
            {
                var idArray = $("#" + idField).val().split(",");
                var nameArray = $("#" + nameField).val().split(",");
                var tempArray = new Array();
                for (var i = 0; i < idArray.length; i++)
                {
                    var trimId = idArray[i].replace(/\s/g, "");
                    if (filters.indexOf(idArray[i]) >= 0)//需要过滤的数据红色显示
                    {
                        html += "<span id='triggerCondSpan" + trimId + "'></span><span class='zk-msg-error'>" + nameArray[i] + "</span><br>"
                        loadUIToDiv("input", "#triggerCondSpan" + trimId, {
                            useInputReq:true,
                            hideLabel:true,
                            type:"checkbox",
                            id:"idTriggerCond_" + idArray[i],
                            name:idField + "Checked",
                            value:idArray[i],
                            onchange:"operate(this, '" + idField + "', '" + nameField + "')",
                            disabled: "disabled",
                            trueValue:idArray[i],
                            falseValue:"-1",
                            eventCheck:true
                        });
                    }
                    else
                    {
                        html += "<span id='triggerCondSpan" + trimId + "'></span><span>" + nameArray[i] + "</span><br>"
                        loadUIToDiv("input", "#triggerCondSpan" + trimId, {
                            useInputReq:true,
                            hideLabel:true,
                            type:"checkbox",
                            id:"idTriggerCond_" + idArray[i],
                            name:idField + "Checked",
                            value:idArray[i],
                            onchange:"operate(this, '" + idField + "', '" + nameField + "')",
                            trueValue:idArray[i],
                            falseValue:"-1",
                            eventCheck:true
                        });
                        tempArray.push(idArray[i]);//只提交还存在的数据
                    }
                }
                $("#" + idField).val(tempArray.join(","));
                $("#" + nameField).val(tempArray.join(","));//更新数据信息
            }
            $("#" + displayField).html(html);
        }
    });
}

function checkLinkMailParam()
{
	$.ajax({
	    type : "GET",
	    url : "/accLinkage.do?checkMailParam",
	    dataType : "json",
	    async : false,
	    success : function(result)
	    {
		    if (result.data)
		    {
		    	$("#setMailParam").hide();
		    }
		    else
		    {
		    	$("#setMailParam").show();
		    }
	    }
	});
}

function checkLinkSMSParam()
{
	$.ajax({
		type : "GET",
		url : "/accLinkage.do?checkSMSModemParam",
		dataType : "json",
		async : false,
		success : function(result)
		{
			if (result.data)
			{
				$("#setSMSModemParam").hide();
			}
			else
			{
				$("#setSMSModemParam").show();
			}
		}
	});
}

//全选
function checkAll(idField, nameField)
{
	$(":input[name='" + idField +"Checked']").siblings("input[type=checkbox]").each(function(){
		if($(this).attr("checked") == undefined)
		{
			$(this).attr("checked", "checked");
			operate($(this), idField, nameField);
		}
	});
}

//全不选
function uncheckAll(idField, nameField)
{
	$(":input[name='" + idField +"Checked']").siblings("input[type=checkbox]").each(function(){
		if($(this).attr("checked") == "checked")
		{
			$(this).removeAttr("checked");
			operate($(this), idField, nameField);
		}
	});
}

function cleanAllData()
{
	$("#linkageTriggerCondName, #linkageInputName, #selectedDoorName, #selectedAuxOutName").html("");
	$("#triggerCond, #triggerCondName, #triggerTree, #selectedIds, #inputAddr, #inputType, #inputName, #outputDoorAddr, #outputAuxOutAddr, #outputDoorName, #outputAuxOutName").val("");
	//输入点-->树形菜单
	inputArray = null;
	inputTree = null;
	
	// 输出点-->树形菜单
	doorOutputArray = null;
	doorOutputTree = null;
	auxOutOutputArray = null;
	auxOutOutputTree = null;
	ctlAllRelayFunOn = null;
	
	changeTrigger = false;// 判断是否改变了触发事件
	inputTypeNew = null;//新的门、读头或辅助输入
	inputTypeOld = null;//旧的门、读头或辅助输入
}

//验证确定按钮是否可点击
function validButton(tree)
{
	if (tree.getAllChecked().length == 0)
    {
    	$("#confirmButton").attr("disabled", true);
    }
    else
    {
    	$("#confirmButton").attr("disabled", false);
    }
}

//添加digifort事件弹窗
function addDigifortGlobalEvent()
{
    var opts = {//弹窗配置对象
        path:"/skip.do?page=acc_linkage_opAccDigifortGlobalEvent",//弹窗路径
        title:I18n.getValue("acc_digifort_chooseDigifortEvents"),//弹窗标题
        width:520,//窗口宽度
        height:400,//窗口高度
        gridName: "gridbox"//设置grid
    }
    DhxCommon.createWindow(opts);
}

function checkVdbLinkageNotifyCount() {
    var vdbExtensionId = document.getElementById("vdbExtensionId");
    var arr = vdbExtensionId.value.split(",");
    if(arr.length > 5) {
        return false;
    }
    return true;
}

function checkVdbLinkageSet() {
    var vdbExtensionId = document.getElementById("vdbExtensionId");
    var vdbExtensionIds = vdbExtensionId.value;
    var ivrId = ZKUI.Combo.PULL.vdbIvrCombo.combo.getSelectedValue();
    if((vdbExtensionIds != "" && ivrId == "") || (vdbExtensionIds == "" && ivrId != "")) {
        return false;
    }
    return true;
}

