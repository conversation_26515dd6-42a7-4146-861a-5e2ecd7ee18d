dhtmlXDataView.prototype.types.ficon={
	css:"ficon",
	template:dhtmlx.Template.fromHTML("<div align='center'>" +
										"<img id='id_img_row_{obj.imgId}' onmouseover='getPopUpInfo(this, event);' onmouseout='closePopUpInfo(this, event);' onmousedown='showDoorSet({common.doorId()})' border='0' src='{common.image()}'>" +
										"<div class='dhx_item_text'>{common.doorName()}</div><div id='{common.popupId()}' class='dhx_item_popupText displayN'>" +
											"<span class='st'>${common_dev_entity}:&nbsp;</span><span class='devName ut'>{common.devName()}</span><br/>" +
						    				"<span class='st'>${acc_door_number}:&nbsp;</span><span class='doorNo ut'>{common.doorNo()}</span><br/>" +
						    				"<span class='st'>${acc_door_sensor}:&nbsp;</span><span class='sensor ut'>{common.sensorState()}</span><br/>" +
						    				"<span class='st'>${acc_door_relay}:&nbsp;</span><span class='relay ut'>{common.relayState()}</span><br/>" +
						    				"<span class='st'>${common_alarm}:&nbsp;</span><span class='alarm ut'>{common.alarmState()}</span><br/>" +
						    				"{common.opText()}" +
										"</div>" +
									"</div>"),
	template_loading:dhtmlx.Template.fromHTML(""),
	width:75,
	height:75,
	margin:1,
	padding:0,
	drag_marker:"dnd_selector_cells.png",
	//custom properties
	icons_src_dir:"./",//"${base}acc/images/",//"./"
	image:function(obj)
	{
		return "/acc/images/doorState/icon/door_" + obj.imgId + ".jpg";//door_default.jpg"
	},
	devName: function(obj)// id='id_img_row_{common.imgid()}'这样写将会无法调用
	{
		//input中data为门的id
		return obj.devName;
	},
	doorId: function(obj)// id='id_img_row_{common.imgid()}'这样写将会无法调用
	{
		//input中data为门的id
		return obj.id;
	},
	doorNo: function(obj)// id='id_img_row_{common.imgid()}'这样写将会无法调用
	{
		//input中data为门的id
		return obj.doorNo;
	},
	doorName: function(obj)// id='id_img_row_{common.imgid()}'这样写将会无法调用
	{
		return obj.doorName;
	},
	sensorState: function(obj)// id='id_img_row_{common.imgid()}'这样写将会无法调用
	{
		return obj.sensorState;
	},
	relayState: function(obj)// id='id_img_row_{common.imgid()}'这样写将会无法调用
	{
		return obj.relayState;
	},
	alarmState: function(obj)// id='id_img_row_{common.imgid()}'这样写将会无法调用
	{
		return obj.alarmState;
	},
	opText: function(obj)// id='id_img_row_{common.imgid()}'这样写将会无法调用
	{
		var text = "";
		if(obj.showOp)
		{
			text = $("#doorOpDiv").html();
			if(text != null)//当前“大异步”情况下，有数据时切换页面会取不到div，此时当前请求也没有实际意义。
			{
				text = text.replace(/\(id\)/g, obj.id);
			}
		}
		return text;
	},
	popupId: function(obj)
	{
		//如popup_text_door_1
		return "popup_text_" + obj.id;		
	}
};

//table模式，原意是将显示的数据横向显示。
dhtmlXDataView.prototype.types.ftable={
	css:"ftable",
	//template:dhtmlx.Template.fromHTML("<div style='float: left; width: 17px;'><img onmousedown='return false;' border='0' src='{common.image()}'></div><div style='float: left; width: 115px; overflow:hidden;' class='dhx_item_text'><span style='padding-left: 2px; padding-right: 2px;'>{common.text()}</span></div><div style='float: left; width: 60px; text-align: right;' class='dhx_item_text'>{common.size()}</div><div style='float: left; width: 130px; padding-left: 10px;' class='dhx_item_text'>{common.date()}</div>"),
	template:dhtmlx.Template.fromHTML("<div style='float: left; width: 30px;'>"
											+"<img id='id_img_row_{common.imgId()}' onmouseover='getPopUpInfo(this, event);' onmouseout='closePopUpInfo(this, event);' onmousedown='return false;' border='0' src='{common.image()}'/>" 
											+"<div id='{common.popupId()}' class='dhx_item_popupText displayN'>{common.popupText()}</div>"
									   +"</div>"
									   +"<div style='float: left; width: 115px; overflow:hidden;' class='dhx_item_text'><span style='padding-left: 2px; padding-right: 2px;'>{common.text()}</span></div>"),
									   //"<div style='float: left; width: 60px; text-align: right;' class='dhx_item_text'>{common.size()}</div>"+
									  // "<div id='{common.popupId()}' class='dhx_item_popupText displayN'>{common.popupText()}</div>"),
	template_loading:dhtmlx.Template.fromHTML(""),
	width: 90,//370,
	height: 30,//20,
	margin:1,
	padding:0,
	drag_marker:"dnd_selector_lines.png",
	//custom properties
	icons_src_dir:"/public/controls/dhtmlx/dhtmlxDataView/common/images/",//"./"
	image:function(obj)
	{
		return "/acc/images/doorState/table/door_disabled.jpg";//door_default.jpg";
		//return this.icons_src_dir + "/ico_"+(obj.name.split(".")[1]||"fldr") + "_18.gif";
	},
	text:function(obj)
	{
		return "text"
		//return obj.text;
		//return obj.name.split(".")[0];
	},
	imgId: function(obj)// id='id_img_row_{common.imgid()}'这样写将会无法调用
	{
		return obj.imgId;
	},
	size:function(obj)
	{
		return obj.filesize?(obj.filesize+"b"):"";
	},
	popupText: function(obj)
	{
		//弹出框内容
		return obj.popupText;		
	},
	popupId: function(obj)
	{
		//如popup_text_door_1
		return "popup_text_" + obj.id;		
	}
};

dhtmlXDataView.prototype.types.fthumbs={
	css:"fthumbs",
	template:dhtmlx.Template.fromHTML("<div align='center'><img border='0' src='{common.image()}'><div class='dhx_item_text'><span>{common.text()}</span></div></div>"),
	width:110,
	height:116,
	margin:15,
	padding:2,
	//custom properties
	thumbs_creator_url:"./",
	icons_src_dir:"/public/controls/dhtmlx/dhtmlxDataView/common/images/",//"./"
	image:function(obj){
		return this.thumbs_creator_url+"?img="+this.photos_rel_dir+"/"+obj.name+"&width=94&height=94";
	},
	text:function(obj){
		return obj.name.split(".")[0];
	}
};


