var pageId = new Date().getTime();// 一个用户可以开多个页面，该值用来保存当前页面创建时间，及初始化状态的时间。
var logId = 0;// logId初始值，全局变量 var
var alarmLogId = 0;// 报警事件id初始值
var stateInit = true;// 状态初始化1是，0否
var isRefreshPage = true;// 是否是刷新页面
var monitorFilter = "";//门状态的监控的设备条件传给事件监控

// 监控哪些数据
var enableRTState = null;
var enableRTEvent = null;
var enableRTAlarm = null;
var enableRTPhoto = null;

function initRtMonitorModule(enableAlarm, enableState, enableEvent, enablePhoto)
{
	enableRTAlarm = enableAlarm != undefined ? enableAlarm : false;
	enableRTState = enableState != undefined ? enableState : false;
	enableRTEvent = enableEvent != undefined ? enableEvent : false;
	enableRTPhoto = enablePhoto != undefined ? enablePhoto : false;
}

if(typeof(rtDataMonitorPageInit) == "undefined" || !rtDataMonitorPageInit)
{
    rtDataMonitorPageInit = true;
 	//window.setTimeout('getAccRTData()', 1000)//等*秒执行刷新函数
}

//异步请求向服务器请求实时数据
function getAccRTData()
{
	isRefreshPage = false;
	var filterParam = {"state": enableRTState, "photo": enableRTPhoto, "alarm": enableRTAlarm}
	var getUrl = sysCfg.rootPath+'/accRTMonitor.do?getEventData&type=all&step=100' + "&stateInit=" + stateInit + "&alarmLogId=" + alarmLogId + "&clientId=" + pageId + monitorFilter;
	$.ajax({
	    type: "GET",
	    url: getUrl + "&logId="+ logId,//??"&logId="+ logId,
	    dataType: "json",
	    async: true,
	    data: filterParam,//event?
	    success: function(rtData)
	    {
			if(rtData != null)
			{
				renderAccRTDataCallback(rtData);//回调函数,获取门磁等状态
			}
			if(actionName == "accRTMonitor" || actionName == "accAlarmMonitor")//需要判断是否是当前页面！！！！！
            {
				window.setTimeout('getAccRTData()', 3000);//等*秒执行刷新函数
            }
            else//比如跳转到其他页面的情况。
			{
            	rtDataMonitorPageInit = false;//确保下次再点击刷新页面时，请求继续发送
			}
	    },
	    error:function (XMLHttpRequest, textStatus, errorThrown)
	    {
 			if(textStatus != "abort" && actionName == "accRTMonitor")//需要判断是否是当前页面！！！！！
			{
 				window.setTimeout('getAccRTData()', 3000);//等*秒执行刷新函数
			}
        	else//比如跳转到其他页面的情况。
			{
        		rtDataMonitorPageInit = false;//确保下次再点击刷新页面时，请求继续发送
			}
 		}
	});
}


//需要创建的回调函数。渲染实时状态，以及实时事件--需要和地图等合并。
function renderAccRTDataCallback(rtData)
{
	if(enableRTEvent)
 	{
 		appendEventToGridCallBack(rtData.data);	
	}	
	if(rtData != null && rtData.logId != undefined)//??		                                                   
	{
		logId = rtData.logId;
 		alrmLogId = rtData.logCountAlarm;//获取监控全部记录开始时的logCountAlarm(跳转页面使用)
		if(!isRefreshPage)
	    {
	 		alarmLogId = rtData.alarmLogId;
			stateInit = false;
	   	}
	}
 	//renderDataViewStateAndEvents(rtData);//事件和状态-需要拆分为下面两个回调方法。
 		
  	if(enableRTState)
  	{
  		appendStateCallBack(rtData.doorStates);
	}
	//需要判断当前是否默认执行。
	if(enableRTPhoto)
	{
		//appendPhotoCallBack(rtData.photos);
	}
	if(enableRTAlarm)
	{
		if(alarmLogId > rtData.alarmLogId)
		{
			appendAlarmToGridCallBack(rtData.alarmData, true);//alarmData
		}
		else
		{
			appendAlarmToGridCallBack(rtData.alarmData, false);//alarmData
		}
	}
}