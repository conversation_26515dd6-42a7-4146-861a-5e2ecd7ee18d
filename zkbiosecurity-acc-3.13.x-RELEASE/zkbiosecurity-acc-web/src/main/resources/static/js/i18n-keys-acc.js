/**
 * 国际化加载
 */

I18n.load([
    "acc_map_addDoor",
    "acc_map_addChannel",
    // 异常记录相关
    "acc_leftMenu_exceptionRecord",
    "acc_exception_record",
    "acc_exception_receiverPosition",
    "acc_exception_enterTime",
    "acc_exception_exitTime",
    "acc_exception_subject",
    "acc_exception_status",
    "acc_exception_sendTime",
    "acc_exception_sendStatus",
    "acc_exception_errorMessage",
    "acc_exception_sendStartTime",
    "acc_exception_sendEndTime",
    "acc_exception_subject_abnormalAccess",
    "acc_exception_subject_late",
    "acc_exception_status_unclosed",
    "acc_exception_status_returned",
    "acc_exception_status_unsent",
    "acc_exception_status_sent",
    "acc_exception_status_failed",
    "acc_exception_sendNotification",
    "acc_exception_resendFailed",
    "acc_exception_resend",
    "acc_exception_markAsReturned",
    "acc_exception_statistics",
    "acc_exception_confirm_sendNotification",
    "acc_exception_confirm_resendFailed",
    "acc_exception_totalCount",
    "acc_exception_unsentCount",
    "acc_exception_sentCount",
    "acc_exception_failedCount",
    "acc_exception_unclosedCount",
    "acc_exception_returnedCount",
    "acc_dev_accLevel",
    "acc_dev_timeZoneAndHoliday",
    "acc_dev_doorOpt",
    "acc_dev_linkage",
    "acc_dev_interlock",
    "acc_dev_antiPassback",
    "acc_dev_firstPerson",
    "acc_dev_linkage",
    "acc_dev_multiPerson",
    "acc_dev_wiegandFmt",
    "acc_dev_outRelaySet",
    "acc_dev_backgroundVerifyParam",
    "acc_dev_firstPerson",
    "acc_dev_auxinSet",
    "acc_dev_verifyModeRule",
    "acc_dev_devNotOpForOffLine",
    "acc_dev_devNotSupportFunction",
    "acc_dev_devNotOpForDisable",
    "acc_dev_offlineAndSelect",
    "acc_dev_selOpDevType",
    "acc_dev_opOnlineDev",
    "acc_dev_opAllDev",
    "acc_dev_offlineAndContinue",
    "acc_dev_opException",
    "acc_dev_selectDev",
    "acc_rtMonitor_nonsupport",
    "acc_dev_panelFWUpdatePrompt",
    "acc_dev_devFWUpdatePrompt",
    "acc_gapb_logicalAPB",
    "acc_gapb_timedAPB",
    "acc_gapb_logicalTimedAPB",
    "acc_gapb_allPerson",
    "acc_gapb_justSelected",
    "acc_gapb_excludeSelected",
    "acc_personLimit_userDate",
    "acc_personLimit_useDays",
    "acc_personLimit_useTimes",
    "acc_level_doorGroup",
    "acc_globalLinkage_selectTrigger",
    "acc_globalLinkage_noSupport",
    "acc_dev_addDevConfirm2",
    "acc_dev_addDevConfirm4",
    "acc_dev_addError",
    "acc_dev_noC3LicenseTip",
    "acc_dev_mismatchedDevice",
    "acc_dev_notContinueAddPullDevice",
    "acc_dev_cannotAddPullDevice",
    "acc_dev_maybeDisabled",
    "acc_door_pushMaxCount",
    "acc_door_verifyModeSinglePwd",
    "acc_verify_mode_onlyface",
    "acc_verify_mode_facefp",
    "acc_verify_mode_facepwd",
    "acc_verify_mode_facecard",
    "acc_verify_mode_facefpcard",
    "acc_verify_mode_facefppwd",
    "acc_verify_mode_fv",
    "acc_verify_mode_fvpwd",
    "acc_verify_mode_fvcard",
    "acc_verify_mode_fvpwdcard",
    "acc_verify_mode_pv",
    "acc_verify_mode_pvcard",
    "acc_verify_mode_pvface",
    "acc_verify_mode_pvfp",
    "acc_verify_mode_pvfacefp",
    "acc_eventNo_0",
    "acc_eventNo_1",
    "acc_eventNo_2",
    "acc_eventNo_3",
    "acc_eventNo_4",
    "acc_eventNo_5",
    "acc_eventNo_6",
    "acc_eventNo_7",
    "acc_eventNo_8",
    "acc_eventNo_9",
    "acc_eventNo_10",
    "acc_eventNo_11",
    "acc_eventNo_12",
    "acc_eventNo_13",
    "acc_eventNo_14",
    "acc_eventNo_15",
    "acc_eventNo_16",
    "acc_eventNo_17",
    "acc_eventNo_18",
    "acc_eventNo_19",
    "acc_eventNo_20",
    "acc_eventNo_21",
    "acc_eventNo_22",
    "acc_eventNo_23",
    "acc_eventNo_24",
    "acc_eventNo_25",
    "acc_eventNo_26",
    "acc_eventNo_27",
    "acc_eventNo_28",
    "acc_eventNo_29",
    "acc_eventNo_30",
    "acc_eventNo_31",
    "acc_eventNo_32",
    "acc_eventNo_33",
    "acc_eventNo_34",
    "acc_eventNo_35",
    "acc_eventNo_36",
    "acc_eventNo_37",
    "acc_eventNo_38",
    "acc_eventNo_39",
    "acc_eventNo_40",
    "acc_eventNo_41",
    "acc_eventNo_42",
    "acc_eventNo_43",
    "acc_eventNo_44",
    "acc_eventNo_45",
    "acc_eventNo_47",
    "acc_eventNo_48",
    "acc_eventNo_49",
    "acc_eventNo_50",
    "acc_eventNo_51",
    "acc_eventNo_52",
    "acc_eventNo_53",
    "acc_eventNo_100",
    "acc_eventNo_101",
    "acc_eventNo_102",
    "acc_eventNo_103",
    "acc_eventNo_200",
    "acc_eventNo_201",
    "acc_eventNo_202",
    "acc_eventNo_203",
    "acc_eventNo_204",
    "acc_eventNo_205",
    "acc_eventNo_206",
    "acc_eventNo_207",
    "acc_eventNo_208",
    "acc_eventNo_209",
    "acc_eventNo_210",
    "acc_eventNo_211",
    "acc_eventNo_212",
    "acc_eventNo_213",
    "acc_eventNo_214",
    "acc_eventNo_215",
    "acc_eventNo_216",
    "acc_eventNo_220",
    "acc_eventNo_221",
    "acc_eventNo_222",
    "acc_eventNo_223",
    "acc_eventNo_225",
    "acc_eventNo_226",
    "acc_newEventNo_0",
    "acc_newEventNo_1",
    "acc_newEventNo_2",
    "acc_newEventNo_3",
    "acc_newEventNo_20",
    "acc_newEventNo_21",
    "acc_newEventNo_26",
    "acc_newEventNo_27",
    "acc_newEventNo_29",
    "acc_newEventNo_30",
    "acc_newEventNo_41",
    "acc_newEventNo_43",
    "acc_newEventNo_44",
    "acc_newEventNo_45",
    "acc_newEventNo_48",
    "acc_newEventNo_54",
    "acc_newEventNo_55",
    "acc_newEventNo_56",
    "acc_newEventNo_57",
    "acc_newEventNo_58",
    "acc_newEventNo_59",
    "acc_newEventNo_60",
    "acc_newEventNo_61",
    "acc_newEventNo_62",
    "acc_newEventNo_63",
    "acc_newEventNo_64",
    "acc_newEventNo_65",
    "acc_newEventNo_66",
    "acc_newEventNo_67",
    "acc_newEventNo_68",
    "acc_newEventNo_69",
    "acc_newEventNo_70",
    "acc_newEventNo_71",
    "acc_newEventNo_73",
    "acc_newEventNo_74",
    "acc_newEventNo_101",
    "acc_newEventNo_104",
    "acc_newEventNo_105",
    "acc_newEventNo_106",
    "acc_newEventNo_107",
    "acc_newEventNo_108",
    "acc_newEventNo_109",
    "acc_newEventNo_110",
    "acc_newEventNo_112",
    "acc_newEventNo_114",
    "acc_newEventNo_115",
    "acc_newEventNo_116",
    "acc_newEventNo_117",
    "acc_newEventNo_118",
    "acc_newEventNo_119",
    "acc_newEventNo_120",
    "acc_newEventNo_121",
    "acc_newEventNo_159",
    "acc_newEventNo_214",
    "acc_newEventNo_217",
    "acc_newEventNo_218",
    "acc_newEventNo_222",
    "acc_newEventNo_223",
    "acc_newEventNo_224",
    "acc_newEventNo_227",
    "acc_newEventNo_228",
    "acc_newEventNo_229",
    "acc_newEventNo_230",
    "acc_newEventNo_232",
    "acc_newEventNo_233",
    "acc_newEventNo_234",
    "acc_newEventNo_235",
    "acc_newEventNo_236",
    "acc_newEventNo_237",
    "acc_newEventNo_239",
    "acc_newEventNo_240",
    "acc_newEventNo_243",
    "acc_newEventNo_244",
    "acc_newEventNo_247",
    "acc_eventNo_undefined",
    "acc_advanceEvent_500",
    "acc_advanceEvent_501",
    "acc_advanceEvent_502",
    "acc_advanceEvent_503",
    "acc_advanceEvent_504",
    "acc_advanceEvent_505",
    "acc_advanceEvent_506",
    "acc_advanceEvent_507",
    "acc_advanceEvent_508",
    "acc_advanceEvent_509",
    "acc_advanceEvent_510",
    "acc_alarmEvent_701",
    "acc_newEventNo_4008",
    "acc_newEventNo_4014",
    "acc_newEventNo_4015",
    "acc_newEventNo_4018",
    "acc_newEventNo_5023",
    "acc_newEventNo_5024",
    "acc_newEventNo_5029",
    "acc_newEventNo_6005",
    "acc_newEventNo_6006",
    "acc_newEventNo_6007",
    "acc_newEventNo_6011",
    "acc_newEventNo_6012",
    "acc_newEventNo_6013",
    "acc_newEventNo_6015",
    "acc_newEventNo_6016",
    "acc_newEventNo_6070",
    "acc_globalLinkage_trigger",
    "acc_rtMonitor_unknown",
    "acc_rtMonitor_alarm",
    "acc_rtMonitor_openTimeout",
    "acc_rtMonitor_openForce",
    "acc_rtMonitor_tamper",
    "acc_rtMonitor_duressPwdOpen",
    "acc_rtMonitor_duressFingerOpen",
    "acc_rtMonitor_duressOpen",
    "acc_rtMonitor_noSensor",
    "acc_trans_viewPhotos",
    "acc_digifort_chooseDigifortEvents",
    "acc_dev_addChildDevice",
    "acc_dev_modParentDevice",
    "acc_dev_configParentDevice",
    "acc_dev_lookUpChildDevice",
    "acc_globalInterlock_addGroup",
    "acc_common_bindOrUnbindChannel",
    "acc_map_addDoor",
    "acc_dsTimeUtc_none",
    "common_op_startTime",
    "common_op_endTime",
    "common_jan",
    "common_feb",
    "common_mar",
    "common_apr",
    "common_may",
    "common_jun",
    "common_jul",
    "common_aug",
    "common_sep",
    "common_oct",
    "common_nov",
    "common_dec",
    "common_dsTime_first",
    "common_dsTime_second",
    "common_dsTime_thirt",
    "common_dsTime_forth",
    "common_dsTime_fifth",
    "acc_dsTime_last",
    "common_monday",
    "common_tuesday",
    "common_wednesday",
    "common_thursday",
    "common_friday",
    "common_saturday",
    "common_sunday",
    "common_hour",
    "common_oclock",
    "acc_dev_setDstime",
    "auth_dept_entity",
    "acc_musterPoint_addDept",
    "acc_rtMonitor_ackAlarm",
    "common_normal",
    "common_exception",
    "common_alarm",
    "auth_security_strengthLevel0",
    "auth_security_strengthLevel1",
    "auth_security_strengthLevel2",
    "acc_musterPointReport_danger",
    "acc_leftMenu_zone",
    "acc_module",
    "acc_alarm_unhandled",
    "acc_alarm_inProcess",
    "acc_alarm_acknowledged",
    "pers_import_phoneTooLong"
], true);
