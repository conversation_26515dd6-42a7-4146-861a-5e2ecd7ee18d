ModuleGuide.register({
    id: "AccGuide",
    text:"acc_module",
    href:"skip.do?page=acc_guide_accGuide",
    icon: "icv-Acc",
    module:"Acc",
    autoOpenOnLoadMenu:true
})
function accCheckIsAllowAddToLevel(value, text) {
    $.ajax({
        type: "POST",
        url: "accLevel.do?getPersonCount&levelId=" + value,
        async: false,
        success: function (data) {
            if (data.ret == "ok") {
                if (data.data > 5000) {
                    $("#levelTip").show();
                    $("#level").addClass("gray");
                }
                else {
                    $("#levelTip").hide();
                    $("#level").removeClass("gray");
                }
            }
        }
    });
}

function accDGridCommonDel(id, bar) {
    if (bar) {
        var gridName = bar.gridName || "gridbox";
        var leftGrid = ZKUI.Grid.get(gridName);
        var dbGrid = ZKUI.DGrid.get(leftGrid.options.dGridName);//双列表对象
        var rightGrid = dbGrid.rightGrid;//右表格对象
        var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
        if (ids == "") {
            messageBox({messageType: "alert", text: "common_prompt_selectObj"})
        } else {
            deleteConfirm(function (result) {
                if (result) {
                    openMessage(msgType.loading);
                    var param = splitURL(fillParamsFromGrid(gridName, ids, id));
                    param.data.ids = ids;
                    $.ajax({
                        url: param.url,
                        type: "post",
                        data: param.data,
                        success: function (result) {
                            closeMessage();
                            dealRetResult(eval(result), function () {
                                leftGrid.reload();
                                rightGrid.reload();
                            })
                        }
                    })
                }
            }, "common_prompt_sureToDelThese")
        }
    }
}

function convertAccVidLinkage(v, opts, gridName, colIndex, rid) {
    var ret = "";
    var dataJson = JSON.parse(v);
    // json对象为空
    if (JSON.stringify(dataJson) === "{}") {
        return ret;
    }
    var vidLinkageHandle = dataJson.vidLinkageHandle;
    var photoPath = dataJson.photoPath;
    if (photoPath) {
        ret = getAccPersPhotoImg(photoPath);
    }
    if (vidLinkageHandle) {
        var linkageHandle = vidLinkageHandle.split(";");
        var align = IS_ENABLE_RTL ? "left" : "right";
        for(var i = 0; i < linkageHandle.length; i++) {
            var vidInfo = linkageHandle[i];
            if (vidInfo.indexOf("/") < 0 && vidInfo.indexOf("pers") != 0 && vidInfo.indexOf("event") != 0) {
                var split = vidInfo.split("_");
                if ((split[0] & 2) != 0) {
                    ret += "<img src='" + sysCfg.rootPath + "/images/base_vid_capture.png' style='cursor: pointer;padding-"+align+":10px;' height='18px' onclick='accCaptureByVidLinkHandle(\"" + split[1] + "\")'/>";
                }
                if ((split[0] & 1) != 0) {
                    ret += "<img src='" + sysCfg.rootPath + "/images/base_vid_video.png' style='cursor: pointer' height='18px' onmouseover='accViewVideoByVidLinkHandle(this,\"" + split[1] + "\")'/>";
                }
                //digifort联动视频
                if(vidInfo.indexOf("digifort") != -1){
                    ret += "&nbsp;&nbsp;<img src='" + sysCfg.rootPath + "/images/digifort.png' style='cursor: pointer' height='18px' data-cameraNames='"+split[2]+"' onmouseover='accViewDigifortByVidLinkHandle(this,\"" + split[1] + "\")'/>";
                }
            }
            else {
                ret = getAccPersPhotoImg(vidLinkageHandle);
            }
        }
    }
    return ret;
}

/** 解析是否加载设备抓拍照片图标*/
function getAccPersPhotoImg(photoPath) {
    var ret = "";
    var split = photoPath.split("_");
    // 照片存在,加载图标
    if (split[0] == 0) {
        ret = "<a href='javascript:createAccPersPhoto(\"" + split[1] + "\")'><img src='/images/base_device_attPhoto.png' style='cursor: pointer' height='18px'/></a>";
    }
    return ret;
}

function checkImgExists(imgurl) {
    return new Promise(function (resolve, reject) {
        var ImgObj = new Image(); //判断图片是否存在
        ImgObj.src = imgurl;
        ImgObj.onload = function (res) {
            resolve(res);
        }
        ImgObj.onerror = function (err) {
            reject(err);
        }
    });
}

function createAccPersPhoto(vidLinkageHandle) {
    $.ajax({
        type: "GET",
        url: "accTransaction.do?getDecryptPhotoBase64&photoPath=" + vidLinkageHandle,
        dataType: "json",
        async: true,
        success: function (data) {
            if (data[sysCfg.ret] == sysCfg.success && data[sysCfg.data]) {
                var opts = {//弹窗配置对象
                    path: "skip.do?page=acc_transaction_accShowUserPhoto&photoBase64=" + data[sysCfg.data] + "&message=acc_trans_viewPhotos",//设置弹窗路径
                    width: 500,//设置弹窗宽度
                    height: 500,//设置弹窗高度
                    title: I18n.getValue("acc_trans_viewPhotos"),//设置弹窗标题
                    gridName: "gridbox"//设置grid
                };
                DhxCommon.createWindow(opts);
            } else {
                openMessage(msgType.warning, I18n.getValue("common_vid_getPhotoFailed"));
            }
        }
    });
}
var systemAllModules;
function accCaptureByVidLinkHandle(vidLinkageHandle) {
    $.ajax({
        type: "GET",
        url: "accTransaction.do?viewVidLinkData&fileType=2&vidLinkageHandle=" + vidLinkageHandle,
        dataType: "json",
        async: true,
        success: function (data) {
            var photoList = data[sysCfg.data].dataList;
            var capturePhotoEncrypt = data[sysCfg.data].capturePhotoEncrypt;
            var photoEncryptStyle = "";
            if (capturePhotoEncrypt) {
                photoEncryptStyle = "filter: blur(5px);";
            }
            if (data[sysCfg.ret] == sysCfg.error) {
                openMessage(msgType.warning, I18n.getValue("common_vid_getPhotoFailed"));
            }
            else if (photoList && photoList.length > 0) {
                // video使用的为Vms模块，配置查看图片路径
                if (systemModules.toLowerCase().indexOf("vms") != -1) {
                    $.jPic(photoList, {
                        id: "jPicID1",
                        action: "vmsTransaction.do?getVmsCapture&id=",
                        width: "640",
                        height: "480",
                        photoEncryptStyle: photoEncryptStyle
                    });
                } else if (systemModules.toLowerCase().indexOf("ivs") != -1) {
                    // ivs 智能视频
                    $.jPic(photoList, {id: "jPicID1",action: "ivsTransaction.do?getIvsCapture&id=",width: "640",height: "480", photoEncryptStyle: photoEncryptStyle});
                } else {
                    $.jPic(photoList, {photoEncryptStyle: photoEncryptStyle});
                }
            }
            else {
                openMessage(msgType.warning, I18n.getValue("common_vid_getPhotoFailed"));
            }
        }
    });
}

function accViewVideoByVidLinkHandle(obj, vidLinkageHandle) {
    var $img = $(obj);
    if ($img.attr("data") == undefined) {
        $.ajax({
            type: "GET",
            url: "accTransaction.do?viewVidLinkData&fileType=1&vidLinkageHandle=" + vidLinkageHandle,
            dataType: "json",
            async: false,
            success: function (data) {
                if (data[sysCfg.ret] == sysCfg.error) {
                    openMessage(msgType.warning, I18n.getValue("common_vid_getVideoFailed"));
                }
                else {
                    var videoList = data[sysCfg.data].dataList;
                    $img.attr("data", videoList);
                    var imgArray = "";
                    // 组装页面下方抓拍小图片的html代码
                    /** 根据加载的视频模块是vid模块还是vms模块处理视频媒体的点击事件,vid模块是下载录像,vms是弹出录像回放 */
                    if (videoList && systemModules.toLowerCase().indexOf("vid") != -1) {
                        for (i = 0; i < videoList.length; i++) {
                            imgArray += (i > 0 ? "<br/>" : "") + '<a href="javascript:void(0);" onclick="accDownLoadVidFile(this,\'' + videoList[i].id + '\')"><img src="'
                                + sysCfg.rootPath + '/images/base_vid_video.png" height="18px" style="cursor: pointer;"/>' + videoList[i].channelName + '</a>';
                        }
                    }
                    else if (videoList && systemModules.toLowerCase().indexOf("vms") != -1) {
                        for (i = 0; i < videoList.length; i++) {
                            imgArray += (i > 0 ? "<br/>" : "") + '<a href="javascript:void(0);" onclick=\'popUpVmsVideoRecord(' + JSON.stringify(videoList[i]) + ')\'><img src="'
                                + sysCfg.rootPath + '/images/base_vid_video.png" height="18px" style="cursor: pointer;"/>' + videoList[i].channelName + '</a>';
                        }
                    } else if (videoList && systemModules.toLowerCase().indexOf("ivs") != -1) {
                        for (i = 0; i < videoList.length; i++) {
                            imgArray += (i > 0 ? "<br/>" : "") + '<a href="javascript:void(0);" onclick="loadPlaybackVideo(\'' + videoList[i].startTime + '\',\'' +
                                videoList[i].endTime + '\',\'' + videoList[i].channelId + '\',' + null + ',' + null + ',' + null + ','+ 0 + ')"><img src="'
                                + '/images/base_vid_video.png" height="18px" style="cursor: pointer;"/>' + videoList[i].channelName + '</a>';
                        }
                    }
                    if (imgArray == "") {
                        imgArray += "<span class='warningImage'></span><span class='warningColor'>" + I18n.getValue("common_vid_getVideoFailed") + "</span>";
                    }
                    else {
                        // imgArray += "<br/><span class='warningImage'></span><a href=\"/baseHelpDocument.do?getHelpDoc\" target='_blank'><span class='warningColor'>"+I18n.getValue("vid_trans_downloadVideoTip")+"</span></a>";
                    }
                    var myPop = new dhtmlXPopup();
                    myPop.attachHTML(imgArray);

                    function accShowPopup(inp) {
                        if (!myPop) {
                            myPop = new dhtmlXPopup();
                            myPop.attachHTML(imgArray);
                        }
                        if (myPop.isVisible()) {
                            myPop.hide();
                        }
                        else {
                            var x = dhx4.absLeft(inp); // inner dhtmlx method, return left position
                            var y = dhx4.absTop(inp); // inner dhtmlx method, return top position
                            var w = inp.offsetWidth;
                            var h = inp.offsetHeight;
                            myPop.show(x, y + 3, w, h);
                        }
                    }

                    $img.click(function () {
                        accShowPopup($(this)[0]);
                    });
                    var _t;
                    $img.hover(function () {
                        if (myPop) {
                            accShowPopup($(this)[0]);
                        }
                        if (_t) {
                            window.clearTimeout(_t);
                        }
                    }, function () {
                        _t = window.setTimeout(function () {
                            if (myPop) {
                                myPop.hide();
                            }
                        }, 1000);
                    });

                    $(".dhx_popup_dhx_web").hover(function () {
                        if (_t) {
                            window.clearTimeout(_t);
                        }
                        $(this).show();
                    }, function () {
                        $(this).hide();
                    });
                    accShowPopup($img[0]);
                }
            }
        });
    }
}

/**
 * 显示Digifort录像列表
 */
function accViewDigifortByVidLinkHandle(obj, eventTime){
    var $img = $(obj);
    var imgArray = "";
    // 组装页面下方抓拍小图片的html代码
    var cameraNames = $img.attr("data-cameraNames").split(",");
    for (var i = 0; i < cameraNames.length; i++)
    {
        imgArray += (i > 0 ? "<br/>" : "")+'<a href="javascript:void(0);" onclick="accDownLoadDigifortFile(this,\'' + cameraNames[i] + '\',' + eventTime + ')"><img src="'
            +sysCfg.rootPath+'/images/digifort.png" height="18px" style="cursor: pointer"/>' + cameraNames[i] + '</a>';
    }
    if (imgArray == "") {
        imgArray += "<span class='warningImage'></span><span class='warningColor'>"+I18n.getValue("common_vid_getVideoFailed")+"</span>";
    }
    var myPop = new dhtmlXPopup();
    myPop.attachHTML(imgArray);
    function accShowPopup(inp)
    {
        if (!myPop)
        {
            myPop = new dhtmlXPopup();
            myPop.attachHTML(imgArray);
        }
        if (myPop.isVisible())
        {
            myPop.hide();
        }
        else
        {
            var x = dhx4.absLeft(inp); // inner dhtmlx method, return left position
            var y = dhx4.absTop(inp); // inner dhtmlx method, return top position
            var w = inp.offsetWidth;
            var h = inp.offsetHeight;
            myPop.show(x,y+3,w,h);
        }
    }
    $img.click(function(){
        accShowPopup($(this)[0]);
    });
    var _t;
    $img.hover(function(){
        if (myPop)
        {
            accShowPopup($(this)[0]);
        }
        if(_t)
        {
            window.clearTimeout(_t);
        }
    },function(){
        _t = window.setTimeout(function(){
            if (myPop)
            {
                myPop.hide();
            }
        },1000);
    });

    $(".dhx_popup_dhx_web").hover(function(){
        if(_t)
        {
            window.clearTimeout(_t);
        }
        $(this).show();
    },function(){
        $(this).hide();
    });
    accShowPopup($img[0]);

}

function accGetPersonCountByDeptIds(deptIds) {
    var personCount = 0;
    $.ajax({
        type: "post",
        dataType: "json",
        async: false,
        url: "accPerson.do?getPersonCountByDept",
        data: {
            "deptIds": deptIds
        },
        success: function (data) {
            personCount = data.data;
        }
    });
    return personCount;
}

/**
 * 下载录像文件
 *
 * <AUTHOR>
 * @since 2015年2月27日 下午4:40:44
 * @param id
 */
function accDownLoadVidFile(obj, id) {
    openMessage(msgType.loading, I18n.getValue("common_vid_videoDownloading"));
    if ($("iframe.downLoadFrame").length > 3) {
        $("iframe.downLoadFrame").remove();
    }
    $(obj).addClass("disabled");//禁用
    if (typeof($(obj).attr("hasBeenClicked")) == "undefined") {
        $("iframe#downLoadFrame_" + id).remove();
        $(obj).attr("hasBeenClicked", "hasBeenClicked");
        var iframe = document.createElement("iframe");
        iframe.id = 'downLoadFrame_' + id;
        iframe.className = 'downLoadFrame';
        iframe.src = sysCfg.rootPath + "/accTransaction.do?getVideoFile&id=" + id;
        var showVidFile = function () {
            var html = iframe.contentWindow.document.body.innerText;
            if (html != "" && html.indexOf(sysCfg.ret)) {
                var msg = eval("(" + html + ")");
                if (msg[sysCfg.ret] == sysCfg.error) {
                    openMessage(msgType.error, msg.msg);
                }
                else if (msg[sysCfg.ret] == sysCfg.warning) {
                    openMessage(msgType.warning, msg.msg);
                }
                else {
                    closeMessage();
                }
            }
        };
        if (iframe.attachEvent) {
            iframe.attachEvent("onload", showVidFile);
        }
        else
        {
            iframe.onload = showVidFile;
        }
        $(iframe).css("display","none");
        $("body").append(iframe);
    }
    $(obj).removeClass("disabled").removeAttr("hasBeenClicked");
    closeMessage();
}

/**
 * 下载Digifort录像
 * @param cameraName
 * @param eventTime
 */
function accDownLoadDigifortFile(obj, cameraName, eventTime){
    openMessage(msgType.loading,I18n.getValue("common_vid_videoDownloading"));
    if($("iframe.downLoadFrame").length > 3)
    {
        $("iframe.downLoadFrame").remove();
    }
    $(obj).addClass("disabled");//禁用
    if(typeof($(obj).attr("hasBeenClicked")) == "undefined")
    {
        $("iframe#downLoadFrame_"+cameraName).remove();
        $(obj).attr("hasBeenClicked", "hasBeenClicked");
        var iframe = document.createElement("iframe");
        iframe.id='downLoadFrame_'+cameraName;
        iframe.className='downLoadFrame';
        iframe.src = sysCfg.rootPath+"/accTransaction.do?getDigifortVideoFile&cameraName=" + cameraName + "&eventTime=" + eventTime;
        var showVidFile = function()
        {
            var html = iframe.contentWindow.document.body.innerText;
            if(html != "" && html.indexOf(sysCfg.ret))
            {
                var msg = eval("("+html+")");
                if(msg.ret == 'fail')
                {
                    openMessage(msgType.error, msg.msg);
                }
                else if(msg.ret == sysCfg.warning)
                {
                    openMessage(msgType.warning, msg.msg);
                }
                else
                {
                    closeMessage();
                }
            }
        };

        function getDownLoadDigifortResult() {
            $.ajax({
                url:"skip.do?getExportResult",
                success: function(res) {
                    if(res[sysCfg.data]=="end") {
                        closeMessage();
                    } else {
                        setTimeout(getDownLoadDigifortResult, 3000);
                    }
                }
            });
        }

        if (iframe.attachEvent)
        {
            iframe.attachEvent("onload", showVidFile);
        }
        else
        {
            iframe.onload = showVidFile;
        }
        setTimeout(function() {
            getDownLoadDigifortResult();
        }, 2000);
        $(iframe).css("display", "none");
        $("body").append(iframe);
    }
    $(obj).removeClass("disabled").removeAttr("hasBeenClicked");
}

// 双列表右列表删除功能 by mingfa.zheng
function accRightGridCommonDel(id, bar, opt) {
    if (bar) {
        var gridName = bar.gridName || "gridbox";
        var rightGrid = ZKUI.Grid.get(gridName);
        var leftGrid = ZKUI.DGrid.get(rightGrid.options.dGridName).leftGrid;
        var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
        if (ids == "") {
            messageBox({messageType: "alert", text: "common_prompt_selectObj"});
        }
        else {
            deleteConfirm(function (result) {
                if (result) {
                    openMessage(msgType.loading);
                    var param = splitURL(fillParamsFromGrid(gridName, ids, id));
                    $.ajax({
                        url: param.url,
                        type: "post",
                        data: param.data,
                        success: function (result) {
                            closeMessage();
                            dealRetResult(eval(result), function () {
                                /*if (opt.accModuleType) {
                                    accSetPersonCount(result.data, opt.accModuleType, leftGrid);
                                } else {*/
                                ZKUI.Grid.reloadGrid(gridName);
                                //}
                            });
                        }
                    });
                }
            }, "common_prompt_sureToDelThese");
        }
    }
}

// 刷新双列表左边人员数量  by mingfa.zheng
function accSetPersonCount(num, type, leftGrid) {
    //var mygrid = mygrids["leftGridbox"];
    var rowId = leftGrid.grid.getSelectedRowId();
    var rowIndex = leftGrid.grid.getRowIndex(rowId);
    var colIndex;
    if (type == "person") {
        colIndex = leftGrid.grid.getColIndexById("personCount");//人员数列索引
    } else if (type == "door") {
        colIndex = leftGrid.grid.getColIndexById("doorCount");//门数列索引
    }
    // var personCount = parseInt(leftGrid.grid.cells(rowId, colIndex).cell.innerHTML);
    // personCount = personCount != "" ? parseInt(personCount) : 0;
    num = (num != "" && num != null) ? parseInt(num) : 0;

    // if(type != undefined && type == "add") {
    //     personCount = num;
    // } else {
    //     personCount = (personCount - num > 0) ?  personCount - num : 0;
    // }
    leftGrid.grid.cells(rowId, colIndex).cell.innerHTML = num;//更新人数
    leftGrid.grid.selectRow(rowIndex, true, true, true);
}

// 更新列表中的门、人 数量  by mingfa.zheng
function setAccCount(leftGrid, rightGrid, num, opType, type, timeOut, id) {
//	var mygrid = mygrids["leftGridbox"];
    var rowId = leftGrid.grid.getSelectedRowId();
    rowId = (rowId == undefined || rowId == null || rowId == "") ? id : rowId;
    var rowIndex = leftGrid.grid.getRowIndex(rowId);
    var type = (type == "person") ? "personCount" : "doorCount";
    var colIndex = leftGrid.grid.getColIndexById(type);//人员数列索引
    var count = parseInt(leftGrid.grid.cells(rowId, colIndex).cell.innerHTML);
    count = count != "" ? parseInt(count) : 0;
    num = num != "" ? parseInt(num) : 0;
    if (id != undefined) {
        leftGrid.grid.selectRow(rowIndex, true, true, true);//加完后重新点击查询
    } else {
        //reloadGrid("rightGridbox");//右边列表已经保存了过滤参数，直接刷新
        rightGrid.reload();
    }
    if (opType != undefined && opType == "add") {//添加人员后，以右边人员列表总人数更新人数
        window.setTimeout(function () {
            count = rightGrid.grid.getRowsNum();
            leftGrid.grid.cells(rowId, colIndex).cell.innerHTML = count;//更新个数
        }, timeOut);
    } else {
        count = (count - num > 0) ? count - num : 0;
        leftGrid.grid.cells(rowId, colIndex).cell.innerHTML = count;//更新个数
    }
}

var accMonitor;

function startMonitor() {
    if (accMonitor > 0) {
        clearInterval(accMonitor)
    }
    accMonitor = setInterval("accMonitorIsKeepAlive()", 1000 * 10 * 60);

}

function accMonitorIsKeepAlive() {
    $.ajax({
        type: "get",
        url: "accRTMonitor.do?isKeepAlive",
        dataType: "json",
        async: false,
        success: function (data) {
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            clearInterval(accMonitor);
        }
    });
}

/** 解析验证方式国际化key,兼容新验证方式*/
function convertVerifyModeName(vfStyles) {
    // 或关系用"/"连接，与关系用"+"号连接
    var logic = "/";
    if (vfStyles.indexOf("common_newVerify_mode_logic") != -1) {
        logic = "+";
        vfStyles = vfStyles.substring(vfStyles.indexOf(",") + 1, vfStyles.length);
    }
    var newVerifyModeName = "";
    var verifyModeName = vfStyles.split(",");
    for (var i = 0; i < verifyModeName.length; i++) {
        newVerifyModeName += I18n.getValue(verifyModeName[i]) + logic;
    }
    return newVerifyModeName.substring(0,newVerifyModeName.length - logic.length);
}

var allColors =["default", "lightgreen", "techblue"];
function getSystemSkinAcc() {
    if(allColors.indexOf(sysCfg.skin) == -1) {
        return "default";
    } else {
        return sysCfg.skin;
    }
}

/**
    按事件等级转换不同主题下gird列表行颜色
*/
function getTextColorByDataLevel(eventLevel, defaultStyle) {
    var systemSkin = (sysCfg.skin=='default' || !sysCfg.skin) ? "" : sysCfg.skin;
    var textColor = defaultStyle;
    // 深色模式修改颜色
    if (systemSkin == "techblue") {
        if (eventLevel == "0") {
            // 正常
            textColor = "color: #42AFFE";
        } else if (eventLevel == "1") {
            // 异常
            textColor = "color: #E57A14";
        } else if (eventLevel == 2) {
            // 报警
            textColor = "color: #FE6257";
        }
    }
    return textColor;
}

//事件级别转换
function convertToWordWithColor(v) {
    var level, priority, color;
    var datas = v.split(";");
    level = datas[0];
    priority = datas[1];
    if (level == "0") {
        return "<font class='zk-msg-normal'>"+I18n.getValue("common_normal")+"</font>";
    } else if (level == "1") {
        return "<font class='zk-msg-warn'>"+I18n.getValue("common_exception")+"</font>";
    } else if (level == "2") {
        switch (priority){
            case '0': priority = I18n.getValue("auth_security_strengthLevel0");break;
            case '1': priority = I18n.getValue("auth_security_strengthLevel1");break;
            case '2': priority = I18n.getValue("auth_security_strengthLevel2");break;
            case '3': priority = I18n.getValue("acc_musterPointReport_danger");break;
            default:priority = I18n.getValue("auth_security_strengthLevel0");
        }
        return "<font class='zk-msg-error'>"+I18n.getValue("common_alarm") + "(" + priority + ")"+"</font>";
    }
}

    function convertAccEventLevelToText(v) {
        var level = v;
        var priority = "";
        if(v.indexOf("#") > 0) {
            var levelAndEventPriority = v.split("#");
            level = levelAndEventPriority[0];
            priority = levelAndEventPriority[1];
        }
        if(level == "0") {
            return "<font class='zk-msg-normal'>"+I18n.getValue("common_normal")+"</font>";
        }
        else if(level == "1") {
            return "<font class='zk-msg-warn'>"+I18n.getValue("common_exception")+"</font>";;
        }
        else if(level == "2") {
            var result = I18n.getValue("common_alarm");
            var priorityText = "";
            switch (priority) {
                case "0" :
                    priorityText = I18n.getValue("auth_security_strengthLevel0");
                    break;
                case "1" :
                    priorityText = I18n.getValue("auth_security_strengthLevel1");
                    break;
                case "2" :
                    priorityText = I18n.getValue("auth_security_strengthLevel2");
                    break;
                case "3" :
                    priorityText = I18n.getValue("acc_musterPointReport_danger");
                    break;
            }
            if(priorityText != "") {
                result = result + "(" + priorityText +")";
            }
            return "<font class='zk-msg-error'>"+result+"</font>";
        }
        else {
            return level;
        }
    }

    function accHideWithAsterisk(str) {
        var strLength = str.length;
        if (strLength > 0) {
            var length = parseInt(strLength /2);
            if(length > 0){
                var strArray  = str.split('');
                if(length == 1) {
                    strArray[1] = '*';
                } else if( length > 1 && length < 5) {
                    for(var j = 2; j < length +1 ; j++) {
                        strArray[j] = '*';
                    }
                } else {
                    for(var j = 2; j < 6 ; j++) {
                        strArray[j] = '*';
                    }
                }
                str = strArray.join('');
            } else{
                str = str + "*";
            }
        }
        return str;
    }

/** 门底下读头绑定摄像头预览 */
function accRTMonitorChannelPreview(channelIds) {
    if (channelIds) {
        var title = "";
        // 调用ivs多窗口视频预览,目前最多支持4路
        playMultiVideo4Linkage(channelIds, 0, "PreviewVideo", title, 600, 420, "center");
    }
}

/** 联动弹出视频预览 */
function accRTMonitorPopUpVideo() {
    if(global_vidDevices != undefined && global_vidDevices.length > 0) {
        var obj = global_vidDevices.shift();
        var channelIds = "";
        // 同一联动视频通道显示时长一致
        var actionTime;
        for (var i = 0; i < obj.vidDevices.length; i++) {
            channelIds += obj.vidDevices[i].channelId + ",";
            actionTime = obj.vidDevices[i].vid_actionTime;
        }
        if (channelIds.length > 0 && actionTime) {
            channelIds = channelIds.substring(0, channelIds.length - 1);
            var title = I18n.getValue("common_linkIO_videoLink");
            // 宽度为屏幕大小减去抓拍窗口大小除以2
            var width = (window.screen.width - 680) / 2;
            var height = window.screen.height / 2.5;
            // 调用ivs多窗口视频预览,目前最多支持4路
            playMultiVideo4Linkage(channelIds, actionTime, "PopUpPreviewVideo", title, width, height, "rightTop");
        }
    }
}

function convertAccSelectPersonClearDate(v, opts, gridName, colIndex, rid) {
    return "<div class='icv-ic_del acc_icv zk-colors-fg-green' onclick=\"accLevelDeleteDateClick('" + rid + "')\" ></div>";
}