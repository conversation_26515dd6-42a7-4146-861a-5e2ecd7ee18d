//批量设置门禁参数
function editAccPerson(editPath, type)
{
	
	editPath = editPath.indexOf("#")>0 ? editPath.split("#")[0] : editPath; 
	var idsName = editPath.split("^")[1] ? editPath.split("^")[1] : "ids";
	var ids = getGridCheckboxValue(idsName);
	if(ids != "") //判断是否至少选中一个
	{
		createWindow("accPersonLevelByPersonAction!setAccParams.action?ids="+ ids +"^0^0^400^300^${pers_person_accSetting}");
	}
	else
	{
		messageBox({messageType:"alert", text: "${common_prompt_selectObj}"});
	}
}

// 删除权限组
function delLevel(delPath, type)
{
	//判断路径中是否存在"#",存在则取#号后面是数据为路径（在list页面中操作xml中存在）
	delPath = delPath.indexOf("#")>0 ? delPath.split("#")[0] : delPath;
	var idsName = delPath.split("^")[1] ? delPath.split("^")[1] : "ids";
	var ids = getGridCheckboxValue(idsName);
	if(ids != "") //判断是否至少选中一个
	{
		if(delPath.indexOf("$(") > 0)
		{
			var objId  = delPath.substring(delPath.indexOf("$(") + "$(".length, delPath.lastIndexOf(")"));
			delPath = delPath.replace("$(" + objId +")", $("#" + objId).val());//替换原来的id标识为当前值
		}
		delPath = delPath.split("^")[0] + (delPath.indexOf("?") > 0 ? "&" : "?") + "un=" + Date.parse(new Date())+"&"+idsName+"="+ids;
		var num = ids.split(",").length;
		messageBox({
			messageType: "confirm", 
			text: "${common_prompt_sureToDelThese}", 
			callback : function(data)
			{
				if(data)
				{
					//判断选中权限组中是否有默认权限组
					var isMasterLevel;
					$.ajax({
						type: "POST",
						url: "accLevelAction!isMasterLevel.action?ids=" + ids,
						dataType: "text",
						async: false,
						success: function(retData)
						{
							isMasterLevel = retData;
						}
					});
					if(isMasterLevel == "true")
					{
						$.jBox.tip("${common_prompt_initDataCanNotDel}", "warning");
						return;
					}
					else
					{
						doLevelProgress(delPath);
					}
				}
			}
		});
	} 
	else 
	{
		messageBox({messageType:"alert", text: "${common_prompt_selectObj}"});
	}
}

// 执行进度条
function doLevelProgress(delPath)
{
	var progressParams = 
	{
		"winTitle" : "${common_progress_proCmd}",
		"dealDataPath" : delPath,
		"singleMode" : false,
		"callback" : function(){
			window.setTimeout(function(){
				reloadGrid("leftGridbox");
				reloadGrid("rightGridbox");
			}, 2000);
		}
	};
	openProgress(progressParams);
}


// 删除权限组人员后执行的回调函数
var opPersonFunc = function(num)
{
	setCount(num, "del", "person", 3000);
};

// 删除权限组门后执行的回调函数
var opDoorFunc = function(num)
{
	setCount(num, "del", "door", 3000);
}

// 删除权限组中的人员
function delPersonByLevel(delPath)
{
	manyDel(delPath, opPersonFunc);//调用公共的删除方法
}

// 删除权限组中的门
function delDoorByLevel(delPath)
{
	//判断路径中是否存在"#",存在则取#号后面是数据为路径（在list页面中操作xml中存在）
	gridName = delPath.split("#")[1] ? delPath.split("#")[1] : defaultGridName;
	delPath = delPath.indexOf("#")>0 ? delPath.split("#")[0] : delPath;
	var idsName = delPath.split("^")[1] ? delPath.split("^")[1] : "ids";
	var ids = getGridCheckboxValue(idsName);
	var path = delPath;
	var num = ids.split(",").length;
	if(ids != "") //判断是否至少选中一个
	{
		if(delPath.indexOf("$(") > 0)
		{
			var objId  = delPath.substring(delPath.indexOf("$(") + "$(".length, delPath.lastIndexOf(")"));
			delPath = delPath.replace("$(" + objId +")", $("#" + objId).val());
		}
		
		var levelId = delPath.split("^")[0].split("objId=")[1];
		delPath = delPath.split("^")[0] + (delPath.indexOf("?") > 0 ? "&" : "?") + "un=" + Date.parse(new Date())+"&"+idsName+"="+ids;
		messageBox({
			messageType: "confirm", 
			text: "${common_prompt_sureToDelThese}", 
			callback : function(data)
			{
				if(data)
				{
					$.ajax({
						type: "POST",
						url: "accLevelAction!personCount.action?id="+levelId,
						dataType: "json",
						async: false,
						success: function(retData)
						{
							var personCount = retData.data;
							if(personCount > 0)
							{
								var progressParams = 
								{
									"winTitle" : "${common_progress_proCmd}",
									"dealDataPath" : delPath,
									"singleMode" : false,
									"callback" : function()
									{
										opDoorFunc(num);
									}
								};
								openProgress(progressParams);
							}
							else
							{
								$.ajax({
									type: "POST",
									url: delPath,
									success: function(result)
								    {
										dealRetResult(result, opDoorFunc(num));
								    },
									error:function (XMLHttpRequest, textStatus, errorThrown)
									{
										openMessage(msgType.error, "${common_prompt_serverError}" + XMLHttpRequest.status);
									}
						        });
							}
						}
					});
				}
			}
		});
	}
	else 
	{
		messageBox({messageType:"alert",text:"${common_prompt_selectObj}"});
	}
}
