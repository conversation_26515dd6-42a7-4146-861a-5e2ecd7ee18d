<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd ">
    <dubbo:reference id="accAntiPassbackService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccAntiPassbackService"/>
    <dubbo:reference id="accAuxInService" interface="com.zkteco.zkbiosecurity.acc.service.AccAuxInService"/>
    <dubbo:reference id="accAuxOutService" interface="com.zkteco.zkbiosecurity.acc.service.AccAuxOutService"/>
    <dubbo:reference id="accCombOpenCombService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccCombOpenCombService"/>
    <dubbo:reference id="accCombOpenDoorService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccCombOpenDoorService"/>
    <dubbo:reference id="accCombOpenPersonService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccCombOpenPersonService"/>
    <dubbo:reference id="accDeviceEventService" interface="com.zkteco.zkbiosecurity.acc.service.AccDeviceEventService"/>
    <dubbo:reference id="accDeviceOptionService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService"/>
    <dubbo:reference id="accDeviceService" interface="com.zkteco.zkbiosecurity.acc.service.AccDeviceService"/>
    <dubbo:reference id="accDeviceVerifyModeService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccDeviceVerifyModeService"/>
    <dubbo:reference id="accDoorService" interface="com.zkteco.zkbiosecurity.acc.service.AccDoorService"/>
    <dubbo:reference id="accDSTimeService" interface="com.zkteco.zkbiosecurity.acc.service.AccDSTimeService"/>
    <dubbo:reference id="accFirstOpenService" interface="com.zkteco.zkbiosecurity.acc.service.AccFirstOpenService"/>
    <dubbo:reference id="accGlobalLinkageInService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccGlobalLinkageInService"/>
    <dubbo:reference id="accGlobalLinkageMediaService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccGlobalLinkageMediaService"/>
    <dubbo:reference id="accGlobalLinkageOutService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccGlobalLinkageOutService"/>
    <dubbo:reference id="accGlobalLinkageService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccGlobalLinkageService"/>
    <dubbo:reference id="accGlobalLinkageTriggerService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccGlobalLinkageTriggerService"/>
    <dubbo:reference id="accGlobalLinkageVidService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccGlobalLinkageVidService"/>
    <dubbo:reference id="accHolidayService" interface="com.zkteco.zkbiosecurity.acc.service.AccHolidayService"/>
    <dubbo:reference id="accInterlockService" interface="com.zkteco.zkbiosecurity.acc.service.AccInterlockService"/>
    <dubbo:reference id="accLevelService" interface="com.zkteco.zkbiosecurity.acc.service.AccLevelService"/>
    <dubbo:reference id="accLinkageInOutService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccLinkageInOutService"/>
    <dubbo:reference id="accLinkageMediaService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccLinkageMediaService"/>
    <dubbo:reference id="accLinkageService" interface="com.zkteco.zkbiosecurity.acc.service.AccLinkageService"/>
    <dubbo:reference id="accLinkageTriggerService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccLinkageTriggerService"/>
    <dubbo:reference id="accMapPosService" interface="com.zkteco.zkbiosecurity.acc.service.AccMapPosService"/>
    <dubbo:reference id="accMapService" interface="com.zkteco.zkbiosecurity.acc.service.AccMapService"/>
    <dubbo:reference id="accParamService" interface="com.zkteco.zkbiosecurity.acc.service.AccParamService"/>
    <dubbo:reference id="accPersonLastAddrService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccPersonLastAddrService"/>
    <dubbo:reference id="accPersonLevelByDeptService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccPersonLevelByDeptService"/>
    <dubbo:reference id="accPersonLevelByLevelService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccPersonLevelByLevelService"/>
    <dubbo:reference id="accPersonLevelByPersonService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccPersonLevelByPersonService"/>
    <dubbo:reference id="accPersonService" interface="com.zkteco.zkbiosecurity.acc.service.AccPersonService"/>
    <dubbo:reference id="accReaderOptionService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccReaderOptionService"/>
    <dubbo:reference id="accReaderService" interface="com.zkteco.zkbiosecurity.acc.service.AccReaderService"/>
    <dubbo:reference id="accReaderZoneService" interface="com.zkteco.zkbiosecurity.acc.service.AccReaderZoneService"/>
    <dubbo:reference id="accRouteReaderService" interface="com.zkteco.zkbiosecurity.acc.service.AccRouteReaderService"/>
    <dubbo:reference id="accRouteService" interface="com.zkteco.zkbiosecurity.acc.service.AccRouteService"/>
    <dubbo:reference id="accTimeSegService" interface="com.zkteco.zkbiosecurity.acc.service.AccTimeSegService"/>
    <dubbo:reference id="accTransactionService" interface="com.zkteco.zkbiosecurity.acc.service.AccTransactionService"/>
    <dubbo:reference id="accVerifyModeRuleService"
                     interface="com.zkteco.zkbiosecurity.acc.service.AccVerifyModeRuleService"/>
    <dubbo:reference id="accZoneService" interface="com.zkteco.zkbiosecurity.acc.service.AccZoneService"/>
</beans>