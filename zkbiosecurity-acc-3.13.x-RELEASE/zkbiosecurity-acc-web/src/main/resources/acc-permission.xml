<?xml version="1.0" encoding="UTF-8"?>
<config>

    <!-- 万傲瑞达6600  -->
    <ZKBioCVSecurity>
        <enables>
            <enable>
                <code>AccDeviceAdd</code>
                <permission>acc:device:add</permission>
                <name>设备新增</name>
            </enable>
            <enable>
                <code>AccDeviceIssueBGVerifyParam</code>
                <permission>acc:device:issueBGVerifyParam</permission>
                <name>设置后台验证参数</name>
            </enable>
            <enable>
                <code>AccDeviceSetNTP</code>
                <permission>acc:device:setDevNTP</permission>
                <name>设置Ntp服务器</name>
            </enable>
            <enable condition="#language!='zh_CN'">
                <code>AccExtDevice</code>
                <permission>acc:extDevice</permission>
                <name>扩展板</name>
            </enable>
            <enable>
                <code>AccReaderBindChannel</code>
                <permission>acc:reader:bindChannel</permission>
                <name>读头绑定解绑摄像头</name>
            </enable>
            <enable>
                <code>AccAuxInBindChannel</code>
                <permission>acc:auxIn:bindChannel</permission>
                <name>辅助输入绑定解绑摄像头</name>
            </enable>
            <enable>
                <code>AccDSTime</code>
                <permission>acc:dSTime</permission>
                <name>夏令时</name>
            </enable>
            <enable>
                <code>AccRTMonitorLiveView</code>
                <permission>acc:rtMonitor:liveView</permission>
                <name>实时预览</name>
            </enable>
            <enable>
                <code>AccMap</code>
                <permission>acc:map</permission>
                <name>电子地图</name>
            </enable>
            <enable>
                <code>AccVerifyModeRule</code>
                <permission>acc:verifyModeRule</permission>
                <name>验证方式规则</name>
            </enable>
            <enable>
                <code>AccVerifyModeRulePersonGroup</code>
                <permission>acc:verifyModeRulePersonGroup</permission>
                <name>验证方式规则人员组</name>
            </enable>
            <enable condition="#language!='zh_CN'">
                <code>AccDoorEnable</code>
                <permission>acc:door:enable</permission>
                <name>门启用</name>
            </enable>
            <enable condition="#language!='zh_CN'">
                <code>AccDoorDisable</code>
                <permission>acc:door:disable</permission>
                <name>门禁用</name>
            </enable>
            <enable>
                <code>AccRTMonitorLcdRTMonitor</code>
                <permission>acc:rtMonitor:lcdRTMonitor</permission>
                <name>LCD监控页面</name>
            </enable>

            <enable>
                <code>AppAccRemoteControl</code>
                <permission>app:APPaccRemoteControl</permission>
                <name>移动端权限-门禁远程控制</name>
            </enable>
            <enable>
                <code>AppAccLockdown</code>
                <permission>app:APPaccLockdown</permission>
                <name>移动端权限-门禁紧急锁定</name>
            </enable>
        </enables>
        <disables>
            <disable>
                <code>AccDeviceUpdateCommPwd</code>
                <permission>acc:device:updateCommPwd</permission>
                <name>设备-修改通信密码</name>
            </disable>
            <disable condition="#language=='zh_CN'">
                <code>AccExtDevice</code>
                <permission>acc:extDevice</permission>
                <name>扩展板</name>
            </disable>
            <disable condition="#language=='zh_CN'">
                <code>AccDoorEnable</code>
                <permission>acc:door:enable</permission>
                <name>门启用</name>
            </disable>
            <disable condition="#language=='zh_CN'">
                <code>AccDoorDisable</code>
                <permission>acc:door:disable</permission>
                <name>门禁用</name>
            </disable>
            <disable>
                <code>AppAccMonitor</code>
                <permission>app:APPaccMonitor</permission>
                <name>移动端权限-门禁实时监控</name>
            </disable>
            <disable>
                <code>AppAccDevice</code>
                <permission>app:APPaccDevice</permission>
                <name>移动端权限-门禁设备</name>
            </disable>
            <disable>
                <code>AppAccAlarm</code>
                <permission>app:APPaccAlarm</permission>
                <name>移动端权限-门禁报警监控</name>
            </disable>
            <disable>
                <code>AccDeviceUpdateRs485Addr</code>
                <permission>acc:device:updateRs485Addr</permission>
                <name>修改RS485地址</name>
            </disable>
            <disable>
                <code>AccDeviceSetIntercomServer</code>
                <permission>acc:device:setIntercomServer</permission>
                <name>设置可视对讲服务器参数</name>
            </disable>
            <disable condition="#language!='zh_CN'">
                <code>AccDeviceUpdateNetConnectMode</code>
                <permission>acc:device:updateNetConnectMode</permission>
                <name>切换网络连接</name>
            </disable>
            <disable>
                <code>AccDeviceSetResourceFile</code>
                <permission>acc:device:setResourceFile</permission>
                <name>上传资源文件</name>
            </disable>
            <disable>
                <code>AccTransactionDel</code>
                <permission>acc:transaction:del</permission>
                <name>全部记录-清空记录</name>
            </disable>
            <disable>
                <code>AccTransactionTodayDel</code>
                <permission>acc:transactionToday:del</permission>
                <name>今日访问记录-清空记录</name>
            </disable>
            <disable>
                <code>AccAlarmTransactionDel</code>
                <permission>acc:alarmTransaction:del</permission>
                <name>全部异常记录-清空记录</name>
            </disable>
            <disable>
                <code>AccFirstInLastOutClearData</code>
                <permission>acc:firstInLastOut:clearData</permission>
                <name>人员进出记录-清空记录</name>
            </disable>
        </disables>
    </ZKBioCVSecurity>

    <!-- 万傲瑞达基础版 -->
    <ZKBioCVSecurityFoundation>
        <disables>
            <disable>
                <code>AccDeviceAdd</code>
                <permission>acc:device:add</permission>
                <name>设备新增</name>
            </disable>
            <disable>
                <code>AccDeviceUpdateCommPwd</code>
                <permission>acc:device:updateCommPwd</permission>
                <name>设备-修改通信密码</name>
            </disable>
            <disable>
                <code>AccDeviceIssueBGVerifyParam</code>
                <permission>acc:device:issueBGVerifyParam</permission>
                <name>设置后台验证参数</name>
            </disable>
            <disable>
                <code>AccDeviceSetNTP</code>
                <permission>acc:device:setDevNTP</permission>
                <name>设置Ntp服务器</name>
            </disable>
            <disable>
                <code>AccDeviceUpdateRs485Addr</code>
                <permission>acc:device:updateRs485Addr</permission>
                <name>修改RS485地址</name>
            </disable>
            <disable>
                <code>AccExtDevice</code>
                <permission>acc:extDevice</permission>
                <name>扩展板菜单</name>
            </disable>
            <disable>
                <code>AccReaderBindChannel</code>
                <permission>acc:reader:bindChannel</permission>
                <name>读头绑定解绑摄像头</name>
            </disable>
            <disable>
                <code>AccAuxInBindChannel</code>
                <permission>acc:auxIn:bindChannel</permission>
                <name>辅助输入绑定解绑摄像头</name>
            </disable>
            <disable>
                <code>AccDSTime</code>
                <permission>acc:dSTime</permission>
                <name>夏令时</name>
            </disable>
            <disable>
                <code>AccRTMonitorLiveView</code>
                <permission>acc:rtMonitor:liveView</permission>
                <name>实时预览</name>
            </disable>
            <disable>
                <code>AccMap</code>
                <permission>acc:map</permission>
                <name>电子地图</name>
            </disable>
            <disable>
                <code>AccVerifyModeRule</code>
                <permission>acc:verifyModeRule</permission>
                <name>验证方式规则</name>
            </disable>
            <disable>
                <code>AccVerifyModeRulePersonGroup</code>
                <permission>acc:verifyModeRulePersonGroup</permission>
                <name>验证方式规则人员组</name>
            </disable>
            <disable>
                <code>AccDoorEnable</code>
                <permission>acc:door:enable</permission>
                <name>门启用</name>
            </disable>
            <disable>
                <code>AccDoorDisable</code>
                <permission>acc:door:disable</permission>
                <name>门禁用</name>
            </disable>
            <disable>
                <code>AccTransactionDel</code>
                <permission>acc:transaction:del</permission>
                <name>全部记录-清空记录</name>
            </disable>
            <disable>
                <code>AccTransactionTodayDel</code>
                <permission>acc:transactionToday:del</permission>
                <name>今日访问记录-清空记录</name>
            </disable>
            <disable>
                <code>AccAlarmTransactionDel</code>
                <permission>acc:alarmTransaction:del</permission>
                <name>全部异常记录-清空记录</name>
            </disable>
            <disable>
                <code>AccFirstInLastOutClearData</code>
                <permission>acc:firstInLastOut:clearData</permission>
                <name>人员进出记录-清空记录</name>
            </disable>
        </disables>
    </ZKBioCVSecurityFoundation>

    <!-- bioaccess -->
    <ZKBioAccess>
        <enables>
            <enable>
                <code>AppAccRemoteControl</code>
                <permission>app:APPaccRemoteControl</permission>
                <name>移动端权限-门禁远程控制</name>
            </enable>
            <enable>
                <code>AppAccLockdown</code>
                <permission>app:APPaccLockdown</permission>
                <name>移动端权限-门禁紧急锁定</name>
            </enable>
        </enables>
        <disables>
            <disable>
                <code>AccDeviceUpdateCommPwd</code>
                <permission>acc:device:updateCommPwd</permission>
                <name>设备-修改通信密码</name>
            </disable>
            <disable>
                <code>AccDeviceIssueBGVerifyParam</code>
                <permission>acc:device:issueBGVerifyParam</permission>
                <name>设置后台验证参数</name>
            </disable>
            <disable>
                <code>AccVerifyModeRule</code>
                <permission>acc:verifyModeRule</permission>
                <name>验证方式规则</name>
            </disable>
            <disable>
                <code>AccVerifyModeRulePersonGroup</code>
                <permission>acc:verifyModeRulePersonGroup</permission>
                <name>验证方式规则人员组</name>
            </disable>
            <disable>
                <code>AccDeviceUpdateNetConnectMode</code>
                <permission>acc:device:updateNetConnectMode</permission>
                <name>切换网络连接</name>
            </disable>
            <disable>
                <code>AccRTMonitorLcdRTMonitor</code>
                <permission>acc:rtMonitor:lcdRTMonitor</permission>
                <name>LCD监控页面</name>
            </disable>
            <disable>
                <code>AccDeviceUpdateRs485Addr</code>
                <permission>acc:device:updateRs485Addr</permission>
                <name>修改RS485地址</name>
            </disable>
            <disable>
                <code>AccDeviceSetIntercomServer</code>
                <permission>acc:device:setIntercomServer</permission>
                <name>设置可视对讲服务器参数</name>
            </disable>
            <disable>
                <code>AccDeviceSetResourceFile</code>
                <permission>acc:device:setResourceFile</permission>
                <name>上传资源文件</name>
            </disable>
            <disable>
                <code>AppAccMonitor</code>
                <permission>app:APPaccMonitor</permission>
                <name>移动端权限-门禁实时监控</name>
            </disable>
            <disable>
                <code>AppAccDevice</code>
                <permission>app:APPaccDevice</permission>
                <name>移动端权限-门禁设备</name>
            </disable>
            <disable>
                <code>AppAccAlarm</code>
                <permission>app:APPaccAlarm</permission>
                <name>移动端权限-门禁报警监控</name>
            </disable>
            <disable>
                <code>AccTransactionDel</code>
                <permission>acc:transaction:del</permission>
                <name>全部记录-清空记录</name>
            </disable>
            <disable>
                <code>AccTransactionTodayDel</code>
                <permission>acc:transactionToday:del</permission>
                <name>今日访问记录-清空记录</name>
            </disable>
            <disable>
                <code>AccAlarmTransactionDel</code>
                <permission>acc:alarmTransaction:del</permission>
                <name>全部异常记录-清空记录</name>
            </disable>
            <disable>
                <code>AccFirstInLastOutClearData</code>
                <permission>acc:firstInLastOut:clearData</permission>
                <name>人员进出记录-清空记录</name>
            </disable>
        </disables>
    </ZKBioAccess>



</config>
