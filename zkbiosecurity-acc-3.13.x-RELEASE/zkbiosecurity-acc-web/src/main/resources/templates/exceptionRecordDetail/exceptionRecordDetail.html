<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>异常记录详情</title>
    <style>
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            background: #f7f7f7; 
            margin: 0; 
            padding: 0; 
        }
        .container { 
            max-width: 600px; 
            margin: 20px auto; 
            background: #fff; 
            border-radius: 8px; 
            box-shadow: 0 2px 8px rgba(0,0,0,0.08); 
            padding: 24px; 
        }
        .header {
            text-align: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 1px solid #eee;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 20px;
        }
        .detail-item {
            display: flex;
            margin-bottom: 16px;
            padding: 12px 0;
            border-bottom: 1px solid #f5f5f5;
        }
        .detail-item:last-child {
            border-bottom: none;
        }
        .label {
            width: 100px;
            color: #666;
            font-size: 14px;
            flex-shrink: 0;
        }
        .value {
            flex: 1;
            color: #333;
            font-size: 14px;
            word-break: break-all;
        }
        .status {
            font-size: 12px;
            font-weight: bold;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .error {
            text-align: center;
            padding: 40px;
            color: #fa5151;
        }
        .back-btn {
            display: inline-block;
            margin-top: 20px;
            padding: 10px 20px;
            background: #07c160;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>异常记录详情</h1>
        </div>
        
        <div id="loading" class="loading">
            正在加载数据...
        </div>
        
        <div id="error" class="error" style="display: none;">
            加载失败，请重试
        </div>
        
        <div id="content" style="display: none;">
            <div class="detail-item">
                <div class="label">工号：</div>
                <div class="value" id="pin"></div>
            </div>
            <div class="detail-item">
                <div class="label">姓名：</div>
                <div class="value" id="name"></div>
            </div>
            <div class="detail-item">
                <div class="label">部门：</div>
                <div class="value" id="deptName"></div>
            </div>
            <div class="detail-item">
                <div class="label">读头名称：</div>
                <div class="value" id="readerName"></div>
            </div>
            <div class="detail-item">
                <div class="label">时间：</div>
                <div class="value" id="time"></div>
            </div>
            <div class="detail-item">
                <div class="label">接收人员职位：</div>
                <div class="value" id="receiverPosition"></div>
            </div>
            <div class="detail-item">
                <div class="label">主题：</div>
                <div class="value" id="subject"></div>
            </div>
            <div class="detail-item">
                <div class="label">发送时间：</div>
                <div class="value" id="sendTime"></div>
            </div>
            <div class="detail-item">
                <div class="label">错误信息：</div>
                <div class="value" id="errorMessage"></div>
            </div>
            <div class="detail-item">
                <div class="label">异常状态：</div>
                <div class="value" id="exceptionStatus"></div>
            </div>
        </div>
        
        <div style="text-align: center;">
            <a href="javascript:history.back()" class="back-btn">返回</a>
        </div>
    </div>

    <script th:inline="javascript">
        // 从Thymeleaf获取后端传递的参数
        var recordId = /*[[${recordId}]]*/ '';
        
        // 格式化时间
        function formatTime(timeStr) {
            if (!timeStr) return '';
            const date = new Date(timeStr);
            return date.toLocaleString('zh-CN');
        }

        // 获取异常状态显示文本
        function getStatusText(status) {
            if (status === '1') return '未闭环';
            if (status === '2') return '已返回';
            return status || '';
        }

        // 获取主题显示文本
        function getSubjectText(subject) {
            if (subject === '1') return '异常进出';
            if (subject === '2') return '迟到';
            return subject || '';
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            if (!recordId) {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
                document.getElementById('error').textContent = '缺少记录ID参数';
                return;
            }

            // 发送请求获取异常记录详情
            var xhr = new XMLHttpRequest();
            xhr.open('GET', '/api/v3/wechatAuth/getExceptionRecordDetail?id=' + recordId, true);
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    document.getElementById('loading').style.display = 'none';
                    
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if ((response.code !== undefined && response.code === 0) && response.data) {
                                const data = response.data;
                                // 检查数据是否为空或无效
                                if (data && Object.keys(data).length > 0) {
                                    displayRecordDetail(data);
                                } else {
                                    document.getElementById('error').style.display = 'block';
                                    document.getElementById('error').textContent = '数据为空';
                                }
                            } else {
                                document.getElementById('error').style.display = 'block';
                                document.getElementById('error').textContent = response.message || '获取数据失败';
                            }
                        } catch (e) {
                            document.getElementById('error').style.display = 'block';
                            document.getElementById('error').textContent = '数据解析失败';
                        }
                    } else {
                        document.getElementById('error').style.display = 'block';
                        document.getElementById('error').textContent = '请求失败，请重试';
                    }
                }
            };
            xhr.send();
        });

        // 显示异常记录详情
        function displayRecordDetail(data) {
            document.getElementById('content').style.display = 'block';
            
            document.getElementById('pin').textContent = data.pin || '';
            document.getElementById('name').textContent = data.name || '';
            document.getElementById('deptName').textContent = data.deptName || '';
            document.getElementById('readerName').textContent = data.readerName || '';
            document.getElementById('time').textContent = formatTime(data.enterTime) || formatTime(data.exitTime) || '';
            document.getElementById('receiverPosition').textContent = data.receiverPosition || '';
            document.getElementById('subject').textContent = getSubjectText(data.subject) || '';
            document.getElementById('sendTime').textContent = formatTime(data.sendTime) || '';
            document.getElementById('errorMessage').textContent = data.errorMessage || '';
            
            const statusElement = document.getElementById('exceptionStatus');
            const statusText = getStatusText(data.exceptionStatus);
            statusElement.textContent = statusText;
            // if (data.exceptionStatus === '1') {
            //     statusElement.className = 'value status unclosed';
            // } else if (data.exceptionStatus === '2') {
            //     statusElement.className = 'value status closed';
            // }
        }
    </script>
</body>
</html> 