<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zkteco</groupId>
        <artifactId>zkbiosecurity-pom</artifactId>
        <version>3.3.0-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.zkteco</groupId>
    <artifactId>zkbiosecurity-acc</artifactId>
    <name>${project.artifactId}</name>
    <packaging>pom</packaging>
    <version>${revision}</version>
    <modules>
        <module>zkbiosecurity-acc-vo</module>
        <module>zkbiosecurity-acc-api</module>
        <module>zkbiosecurity-acc-service</module>
        <module>zkbiosecurity-acc-remote</module>
        <module>zkbiosecurity-acc-web</module>
        <module>zkbiosecurity-acc-system</module>
        <module>zkbiosecurity-acc-i18n</module>
        <module>zkbiosecurity-acc-client</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-auth-api</artifactId>
                <version>${auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-system-api</artifactId>
                <version>${system.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-license-api</artifactId>
                <version>${license.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-pers-api</artifactId>
                <version>${pers.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-adms-api</artifactId>
                <version>${adms.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-cmd</artifactId>
                <version>${cmd.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-auth-provider</artifactId>
                <version>${auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-module-all</artifactId>
                <version>${module.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-base</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-core</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-scheduler</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-model</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-redis</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-web</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-foldex</artifactId>
                <version>${foldex.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <properties>
        <module.name>acc</module.name>
        <revision>3.13.0-RELEASE_YFDZ2025071400104</revision>
        <boot.version>3.12.0-RELEASE</boot.version>
        <module.api.version>3.13.0-RELEASE_YFDZ2025071400104</module.api.version>
        <auth.version>3.11.0-RELEASE</auth.version>
        <system.version>3.14.0-RELEASE</system.version>
        <adms.version>3.13.0-RELEASE</adms.version>
        <pers.version>3.14.0-RELEASE_YFDZ2025071400104</pers.version>
        <cmd.version>3.10.0-RELEASE</cmd.version>
        <license.version>3.10.0-RELEASE</license.version>
        <foldex.version>2.5.0-RELEASE</foldex.version>
    </properties>

    <!-- 引入maven release 插件, 用于正式版本的自动发布 -->
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <!-- gitlab地址, 用于生产环境自动打TAG -->
    <scm>
        <developerConnection>scm:git:*******************************:biosecurity-pro/zkbiosecurity-acc.git
        </developerConnection>
        <tag>HEAD</tag>
    </scm>

    <repositories>
        <repository>
            <id>zkteco-internal-repository-releases</id>
            <name>zkteco-internal-repository-releases</name>
            <url>http://192.168.200.31:8080/artifactory/zkteco-internal-repository</url>
        </repository>
        <repository>
            <id>zkteco-internal-repository-snapshots</id>
            <name>zkteco-internal-repository-snapshots</name>
            <url>http://192.168.200.31:8080/artifactory/zkteco-internal-repository</url>
        </repository>
    </repositories>
</project>