package com.zkteco.zkbiosecurity.acc.init;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.service.AccDSTimeService;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.service.AccSupportFuncService;
import com.zkteco.zkbiosecurity.acc.service.AccTimeSegService;
import com.zkteco.zkbiosecurity.acc.vo.AccDSTimeItem;
import com.zkteco.zkbiosecurity.acc.vo.AccLevelItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTimeSegItem;
import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.utils.VersionUtil;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.system.constants.BaseDataConstants;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

@Component
@Order(value = 30)
public class AccInit implements CommandLineRunner {
    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private AccTimeSegService accTimeSegService;
    @Autowired
    private AccDSTimeService accDSTimeService;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private AccSupportFuncService accSupportFuncService;
    @Value("${system.isCloud:false}")
    private Boolean isCloud;

    @Override
    public void run(String... args) throws Exception {
        boolean alreadyInit = baseSysParamService.getAlreadyInitModule("AccInit");
        if (!alreadyInit) {
            initAuthPermission();
            initAccParams();
            initAccDSTime();
            initAccTimeSeg();
            initAccLevel();
            if (!isCloud) {
                // 初始化APP菜单
                initAppMenus();
            }
            initUpgradeVersion();
            baseSysParamService.setAlreadyInitModule("AccInit");
        }
    }

    /**
     * 初始化功能菜单
     */
    private void initAuthPermission() {
        AuthPermissionItem systemItem = null;
        AuthPermissionItem topMenuItem = null;
        AuthPermissionItem subMenuItem = null;
        AuthPermissionItem subButtonItem = null;

        // 门禁模块
        systemItem =
            new AuthPermissionItem("Acc", "acc_module", "acc", AuthContants.RESOURCE_TYPE_SYSTEM, ZKConstant.TRUE, 3);
        systemItem = authPermissionService.initData(systemItem);

        // 设备一级菜单
        topMenuItem = new AuthPermissionItem("AccDeviceManager", "acc_leftMenu_accDev", "acc:device:manager",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 1);
        topMenuItem.setParentId(systemItem.getId());
        topMenuItem.setImg("acc_device.png");
        topMenuItem.setImgHover("acc_device_over.png");
        topMenuItem = authPermissionService.initData(topMenuItem);
        // 设备菜单
        subMenuItem = new AuthPermissionItem("AccDevice", "common_leftMenu_device", "acc:device",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 1);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accDevice.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 设备-刷新
        subButtonItem = new AuthPermissionItem("AccDeviceRefresh", "common_op_refresh", "acc:device:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-新增
        subButtonItem = new AuthPermissionItem("AccDeviceAdd", "common_op_new", "acc:device:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-编辑
        subButtonItem = new AuthPermissionItem("AccDeviceEdit", "common_op_edit", "acc:device:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-删除
        subButtonItem = new AuthPermissionItem("AccDeviceDel", "common_op_del", "acc:device:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-导出
        subButtonItem = new AuthPermissionItem("AccDeviceExport", "common_op_export", "acc:device:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-搜索设备
        subButtonItem = new AuthPermissionItem("AccDeviceSearchDev", "common_dev_searchDev", "acc:device:searchDev",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-启用
        subButtonItem = new AuthPermissionItem("AccDeviceEnable", "common_enable", "acc:device:enable",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-禁用
        subButtonItem = new AuthPermissionItem("AccDeviceDisable", "common_disable", "acc:device:disable",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-同步所有数据
        subButtonItem = new AuthPermissionItem("AccDeviceSyncAllData", "common_dev_syncAllDataToDev",
            "acc:device:syncAllData", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 9);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-升级固件
        subButtonItem = new AuthPermissionItem("AccDeviceUpgradeFirmware", "common_dev_upgradeFirmware",
            "acc:device:upgradeFirmware", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 10);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-重启设备
        subButtonItem = new AuthPermissionItem("AccDeviceRebootDevice", "common_dev_reboot", "acc:device:rebootDevice",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 11);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-获取设备参数
        subButtonItem = new AuthPermissionItem("AccDeviceGetOptFromDev", "common_dev_getDevOpt",
            "acc:device:getOptFromDev", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 12);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-获取人员信息
        subButtonItem = new AuthPermissionItem("AccDeviceUploadPersonInfo", "common_dev_getPersonInfo",
            "acc:device:uploadPersonInfo", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 13);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-获取事件记录
        subButtonItem = new AuthPermissionItem("AccDeviceUploadTransaction", "common_dev_getTrans",
            "acc:device:uploadTransaction", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 14);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-同步时间
        subButtonItem = new AuthPermissionItem("AccDeviceSyncTime", "common_dev_syncTime", "acc:device:syncTime",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 15);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 设备-设置后台验证参数
        subButtonItem = new AuthPermissionItem("AccDeviceIssueBGVerifyParam", "acc_dev_issueVerifyParam",
            "acc:device:issueBGVerifyParam", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 16);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 设备-设置设备时区
        subButtonItem = new AuthPermissionItem("AccDeviceSetTimeZone", "acc_dev_setTimeZone", "acc:device:setTimeZone",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 17);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-设置登记机
        subButtonItem = new AuthPermissionItem("AccDeviceSetRegistrationDevice", "acc_dev_setRegistrationDevice",
            "acc:device:setRegistrationDevice", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 18);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-设置夏令时
        // subButtonItem = new AuthPermissionItem("AccDeviceSetDSTime", "acc_dev_setDstime", "acc:device:setDSTime",
        // AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 19);
        // subButtonItem.setParentId(subMenuItem.getId());
        // authPermissionService.initData(subButtonItem);
        // 设备-修改IP地址
        subButtonItem = new AuthPermissionItem("AccDeviceUpdateIpAddr", "common_dev_modifyIPAddress",
            "acc:device:updateIpAddr", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 20);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-修改通信密码
        subButtonItem = new AuthPermissionItem("AccDeviceUpdateCommPwd", "common_dev_modifyCommPwd",
            "acc:device:updateCommPwd", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 21);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-修改RS485地址
        /*subButtonItem = new AuthPermissionItem("AccDeviceUpdateRs485Addr", "acc_dev_modifyRS485Addr",
            "acc:device:updateRs485Addr", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 22);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);*/
        // 设备-修改指纹对比阈值
        subButtonItem = new AuthPermissionItem("AccDeviceUpdateMThreshold", "common_dev_modifyFPThreshold",
            "acc:device:updateMThreshold", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 23);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-切换网络连接
        subButtonItem = new AuthPermissionItem("AccDeviceUpdateNetConnectMode", "acc_dev_updateNetConnectMode",
            "acc:device:updateNetConnectMode", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 24);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-查看设备中的门禁规则
        subButtonItem = new AuthPermissionItem("AccDeviceQueryDevRule", "acc_dev_accessRules",
            "acc:device:queryDevRule", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 25);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-查询设备容量
        subButtonItem = new AuthPermissionItem("AccDeviceQueryDevUsage", "acc_dev_queryDevVolume",
            "acc:device:queryDevUsage", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 26);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-授权子设备
        subButtonItem = new AuthPermissionItem("AccDeviceAddChildDevice", "acc_dev_addChildDevice",
            "acc:device:addChildDevice", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 27);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-变更主设备
        subButtonItem = new AuthPermissionItem("AccDeviceModParentDevice", "acc_dev_modParentDevice",
            "acc:device:modParentDevice", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 28);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-配置主设备
        subButtonItem = new AuthPermissionItem("AccDeviceConfigParentDevice", "acc_dev_configParentDevice",
            "acc:device:configParentDevice", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 29);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-清除管理员
        subButtonItem = new AuthPermissionItem("AccDeviceClearAdministrator", "acc_dev_clearAdmin",
            "acc:device:clearAdministrator", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 30);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备-设置机器进出状态
        /*subButtonItem = new AuthPermissionItem("AccDeviceSetDevIOState", "acc_dev_setDevSate",
            "acc:device:setDevIOState", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 31);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);*/
        // 设备-设置防疫相关参数
        /*subButtonItem = new AuthPermissionItem("AccDeviceSetHep", "acc_dev_setHep", "acc:device:updateDevHepParam",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 32);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);*/
        // 设备-设置扩展参数
        subButtonItem = new AuthPermissionItem("AccDeviceSetExtendParam", "acc_dev_setExtendParam",
            "acc:device:updateDevExtendParam", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 33);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 设备-设置Ntp服务器
        subButtonItem = new AuthPermissionItem("AccDeviceSetNTP", "acc_device_setNTPService", "acc:device:setDevNTP",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 35);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 设备-设备替换
        subButtonItem = new AuthPermissionItem("AccDeviceExchange", "acc_dev_replace", "acc:device:replace",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 36);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("AccDeviceClearCmd", "common_devMonitor_clearCmdCache",
            "acc:device:clearCmdCache", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 37);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("AccDeviceSetResourceFile", "acc_dev_upResourceFile",
            "acc:device:setResourceFile", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 38);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("AccDeviceSetFaceServerInfo", "acc_dev_setFaceServerInfo",
                "acc:device:setFaceServerInfo", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 39);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 扩展板菜单
        subMenuItem = new AuthPermissionItem("AccExtDevice", "acc_leftMenu_extDev", "acc:extDevice",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accExtDevice.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 扩展板菜单-刷新
        subButtonItem = new AuthPermissionItem("AccExtDeviceRefresh", "common_op_refresh", "acc:extDevice:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 扩展板菜单-新增
        subButtonItem = new AuthPermissionItem("AccExtDeviceAdd", "common_op_new", "acc:extDevice:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 扩展板菜单-编辑
        subButtonItem = new AuthPermissionItem("AccExtDeviceEdit", "common_op_edit", "acc:extDevice:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 扩展板菜单-删除
        subButtonItem = new AuthPermissionItem("AccExtDeviceDel", "common_op_del", "acc:extDevice:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 门菜单
        subMenuItem = new AuthPermissionItem("AccDoor", "acc_leftMenu_door", "acc:door",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 3);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accDoor.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 门菜单-刷新
        subButtonItem = new AuthPermissionItem("AccDoorRefresh", "common_op_refresh", "acc:door:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 门菜单-编辑
        subButtonItem = new AuthPermissionItem("AccDoorEdit", "common_op_edit", "acc:door:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 门菜单-远程开门
        subButtonItem = new AuthPermissionItem("AccDoorOpenDoor", "acc_eventNo_8", "acc:door:openDoor",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 门菜单-远程关门
        subButtonItem = new AuthPermissionItem("AccDoorCloseDoor", "acc_eventNo_9", "acc:door:closeDoor",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 门菜单-启用
        subButtonItem = new AuthPermissionItem("AccDoorEnable", "common_enable", "acc:door:enable",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 门菜单-禁用
        subButtonItem = new AuthPermissionItem("AccDoorDisable", "common_disable", "acc:door:disable",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 门菜单-取消报警
        subButtonItem = new AuthPermissionItem("AccDoorCancelAlarm", "acc_eventNo_7", "acc:door:cancelAlarm",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 门菜单-远程常开
        subButtonItem = new AuthPermissionItem("AccDoorNormalOpenDoor", "acc_rtMonitor_remoteNormalOpen",
            "acc:door:normalOpenDoor", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 门菜单-远程锁定
        subButtonItem = new AuthPermissionItem("AccDoorLockDoor", "acc_newEventNo_233", "acc:door:lockDoor",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 9);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 门菜单-远程解锁
        subButtonItem = new AuthPermissionItem("AccDoorUnLockDoor", "acc_newEventNo_234", "acc:door:unLockDoor",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 10);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 门菜单-启用当天常开时间段
        subButtonItem = new AuthPermissionItem("AccDoorEnableNormalOpenDoor", "common_rtMonitor_enableIntradayTZ",
            "acc:door:enableNormalOpenDoor", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 11);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 门菜单-禁用当天常开时间段
        subButtonItem = new AuthPermissionItem("AccDoorDisableNormalOpenDoor", "common_rtMonitor_disableIntradayTZ",
            "acc:door:disableNormalOpenDoor", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 12);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /** --------------------------- 读头 --------------------------- */
        subMenuItem = new AuthPermissionItem("AccReader", "common_leftMenu_reader", "acc:reader",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 4);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accReader.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 读头-刷新
        subButtonItem = new AuthPermissionItem("AccReaderRefresh", "common_op_refresh", "acc:reader:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 读头-编辑
        subButtonItem = new AuthPermissionItem("AccReaderEdit", "common_op_edit", "acc:reader:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 读头-绑定/解绑摄像头
        subButtonItem = new AuthPermissionItem("AccReaderBindChannel", "common_vid_bindOrUnbindChannel",
            "acc:reader:bindChannel", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /** --------------------------- 辅助输入 --------------------------- */
        subMenuItem = new AuthPermissionItem("AccAuxIn", "common_leftMenu_auxIn", "acc:auxIn",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 5);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accAuxIn.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 辅助输入-刷新
        subButtonItem = new AuthPermissionItem("AccAuxInRefresh", "common_op_refresh", "acc:auxIn:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 辅助输入-编辑
        subButtonItem = new AuthPermissionItem("AccAuxInEdit", "common_op_edit", "acc:auxIn:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 辅助输入-绑定/解绑摄像头
        subButtonItem = new AuthPermissionItem("AccAuxInBindChannel", "common_vid_bindOrUnbindChannel",
            "acc:auxIn:bindChannel", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /** --------------------------- 辅助输出 --------------------------- */
        subMenuItem = new AuthPermissionItem("AccAuxOut", "acc_leftMenu_auxOut", "acc:auxOut",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 6);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accAuxOut.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 辅助输出-刷新
        subButtonItem = new AuthPermissionItem("AccAuxOutRefresh", "common_op_refresh", "acc:auxOut:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 辅助输出-编辑
        subButtonItem = new AuthPermissionItem("AccAuxOutEdit", "common_op_edit", "acc:auxOut:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 辅助输出-远程打开
        subButtonItem = new AuthPermissionItem("AccAuxOutOpenAuxOut", "acc_rtMonitor_remoteOpen",
            "acc:auxOut:openAuxOut", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 辅助输出-远程关闭
        subButtonItem = new AuthPermissionItem("AccAuxOutCloseAuxOut", "acc_rtMonitor_remoteClose",
            "acc:auxOut:closeAuxOut", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 辅助输出-远程常开
        subButtonItem = new AuthPermissionItem("AccAuxOutNormalOpen", "acc_rtMonitor_remoteNormalOpen",
            "acc:auxOut:normalOpen", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /** --------------------------- 事件类型 --------------------------- */
        subMenuItem = new AuthPermissionItem("AccDeviceEvent", "common_leftMenu_event", "acc:deviceEvent",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 7);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accDeviceEvent.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 事件类型-刷新
        subButtonItem = new AuthPermissionItem("AccDeviceEventRefresh", "common_op_refresh", "acc:deviceEvent:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 事件类型-编辑
        subButtonItem = new AuthPermissionItem("AccDeviceEventEdit", "common_op_edit", "acc:deviceEvent:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 事件类型-设置声音
        subButtonItem = new AuthPermissionItem("AccDeviceEventSetSound", "acc_deviceEvent_batchSet",
            "acc:deviceEvent:setSound", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 夏令时菜单
        subMenuItem = new AuthPermissionItem("AccDSTime", "acc_leftMenu_dSTime", "acc:dSTime",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 8);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accDSTime.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 夏令时-刷新
        subButtonItem = new AuthPermissionItem("AccDSTimeRefresh", "common_op_refresh", "acc:dSTime:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 夏令时-新增
        subButtonItem = new AuthPermissionItem("AccDSTimeAdd", "common_op_new", "acc:dSTime:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 夏令时-编辑
        subButtonItem = new AuthPermissionItem("AccDSTimeEdit", "common_op_edit", "acc:dSTime:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 夏令时-删除
        subButtonItem = new AuthPermissionItem("AccDSTimeDel", "common_op_del", "acc:dSTime:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // // 夏令时-设置夏令时
        // subButtonItem = new AuthPermissionItem("AccDSTimeSetDSTime", "common_dsTime_setting", "acc:dSTime:setDSTime",
        // AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        // subButtonItem.setParentId(subMenuItem.getId());
        // authPermissionService.initData(subButtonItem);

        // 设备监控
        /*subMenuItem = new AuthPermissionItem("AccDeviceMonitor", "common_leftMenu_devMonitioring", "acc:deviceMonitor",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 9);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accDeviceMonitor.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 设备监控-浏览
        subButtonItem = new AuthPermissionItem("AccDeviceMonitorBrowse", "common_op_browse", "acc:deviceMonitor:browse",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备监控-导出
        subButtonItem = new AuthPermissionItem("AccDeviceMonitorExport", "common_op_export", "acc:deviceMonitor:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 设备监控-清除命令
        subButtonItem = new AuthPermissionItem("AccDeviceMonitorClearCmd", "common_devMonitor_clearCmdCache",
            "acc:deviceMonitor:clearCmdCache", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);*/

        // 实时监控
        subMenuItem = new AuthPermissionItem("AccRTMonitor", "common_leftMenu_rtMonitor", "acc:rtMonitor",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 10);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accRTMonitor.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 实时监控-浏览
        subButtonItem = new AuthPermissionItem("AccRTMonitorBrowse", "common_op_browse", "acc:rtMonitor:browse",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 实时监控-远程开门
        subButtonItem = new AuthPermissionItem("AccRTMonitorOpenDoor", "acc_eventNo_8", "acc:rtMonitor:openDoor",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 实时监控-远程关门
        subButtonItem = new AuthPermissionItem("AccRTMonitorCloseDoor", "acc_eventNo_9", "acc:rtMonitor:closeDoor",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 实时监控-取消报警
        subButtonItem = new AuthPermissionItem("AccRTMonitorCancelAlarm", "acc_eventNo_7", "acc:rtMonitor:cancelAlarm",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 实时监控-远程锁定
        subButtonItem = new AuthPermissionItem("AccRTMonitorLockDoor", "acc_newEventNo_233", "acc:rtMonitor:lockDoor",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 实时监控-远程解锁
        subButtonItem = new AuthPermissionItem("AccRTMonitorUnLockDoor", "acc_newEventNo_234",
            "acc:rtMonitor:unLockDoor", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 实时监控-远程常开
        subButtonItem = new AuthPermissionItem("AccRTMonitorNormalOpenDoor", "acc_rtMonitor_remoteNormalOpen",
            "acc:rtMonitor:normalOpenDoor", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 实时监控-LCD实时监控
        subButtonItem = new AuthPermissionItem("AccRTMonitorLcdRTMonitor", "acc_leftMenu_LCDRTMonitor",
            "acc:rtMonitor:lcdRTMonitor", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 实时监控-启用当天常开时间段
        subButtonItem = new AuthPermissionItem("AccRTMonitorEnableNormalOpenDoor", "common_rtMonitor_enableIntradayTZ",
            "acc:rtMonitor:enableNormalOpenDoor", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 9);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 实时监控-禁用当天常开时间段
        subButtonItem =
            new AuthPermissionItem("AccRTMonitorDisableNormalOpenDoor", "common_rtMonitor_disableIntradayTZ",
                "acc:rtMonitor:disableNormalOpenDoor", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 10);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 实时监控-查询门最近发生事件
        subButtonItem = new AuthPermissionItem("AccRTMonitorTransactionTodayHappen", "acc_doorEventLatestHappen",
            "acc:rtMonitor:transactionTodayHappen", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 11);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 实时监控-远程打开
        subButtonItem = new AuthPermissionItem("AccRTMonitorOpenAuxOut", "acc_rtMonitor_remoteOpen",
            "acc:rtMonitor:openAuxOut", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 12);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 实时监控-远程关闭
        subButtonItem = new AuthPermissionItem("AccRTMonitorCloseAuxOut", "acc_rtMonitor_remoteClose",
            "acc:rtMonitor:closeAuxOut", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 13);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 实时监控-远程常开
        subButtonItem = new AuthPermissionItem("AccRTMonitorAuxOutNormalOpen", "acc_rtMonitor_remoteNormalOpen",
            "acc:rtMonitor:auxOutNormalOpen", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 14);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 实时监控-实时预览
        subButtonItem = new AuthPermissionItem("AccRTMonitorLiveView", "system_module_videoPreview",
            "acc:rtMonitor:liveView", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 15);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 报警监控
        subMenuItem = new AuthPermissionItem("AccAlarmMonitor", "acc_rtMonitor_alarmMonitor", "acc:alarmMonitor",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 10);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accAlarmMonitor.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 报警监控-确认报警
        subButtonItem = new AuthPermissionItem("AccAlarmMonitorAckAlarm", "acc_rtMonitor_ackAlarm",
            "acc:alarmMonitor:ackAlarm", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 报警监控-查看处理历史
        subButtonItem = new AuthPermissionItem("AccAlarmMonitorHistory", "acc_alarm_history",
            "acc:alarmMonitor:history", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        /** --------------------------- 电子地图 --------------------------- */
        subMenuItem = new AuthPermissionItem("AccMap", "acc_leftMenu_electronicMap", "acc:map",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 11);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accMap.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 电子地图-刷新
        subButtonItem = new AuthPermissionItem("AccMapRefresh", "common_op_refresh", "acc:map:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 电子地图-新增
        subButtonItem = new AuthPermissionItem("AccMapAdd", "common_op_new", "acc:map:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 电子地图-编辑
        subButtonItem = new AuthPermissionItem("AccMapEdit", "common_op_edit", "acc:map:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 电子地图-删除
        subButtonItem = new AuthPermissionItem("AccMapDel", "common_op_del", "acc:map:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 电子地图-保存位置
        subButtonItem = new AuthPermissionItem("AccMapSaveMapPos", "common_op_savePositon", "acc:map:saveMapPos",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 电子地图-添加门
        subButtonItem = new AuthPermissionItem("AccMapAddDoorToMap", "acc_map_addDoor", "acc:map:addDoorToMap",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 电子地图-添加摄像头
        subButtonItem = new AuthPermissionItem("AccMapAddChannelToMap", "acc_map_addChannel", "acc:map:addChannelToMap",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 电子地图-删除图标
        subButtonItem = new AuthPermissionItem("AccMapDelEntity", "base_map_delEntity", "acc:map:delEntity",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 门禁一级菜单
        topMenuItem = new AuthPermissionItem("AccAccessManager", "acc_leftMenu_accRule", "acc:access:manager",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
        topMenuItem.setParentId(systemItem.getId());
        topMenuItem.setImg("acc_control.png");
        topMenuItem.setImgHover("acc_control_over.png");
        topMenuItem = authPermissionService.initData(topMenuItem);

        // 时间段菜单
        subMenuItem = new AuthPermissionItem("AccTimeSeg", "common_leftMenu_timeZone", "acc:timeSeg",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 1);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accTimeSeg.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 时间段-刷新
        subButtonItem = new AuthPermissionItem("AccTimeSegRefresh", "common_op_refresh", "acc:timeSeg:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 时间段-新增
        subButtonItem = new AuthPermissionItem("AccTimeSegAdd", "common_op_new", "acc:timeSeg:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 时间段-编辑
        subButtonItem = new AuthPermissionItem("AccTimeSegEdit", "common_op_edit", "acc:timeSeg:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 时间段-删除
        subButtonItem = new AuthPermissionItem("AccTimeSegDel", "common_op_del", "acc:timeSeg:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 节假日菜单
        subMenuItem = new AuthPermissionItem("AccHoliday", "common_leftMenu_holiday", "acc:holiday",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accHoliday.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 节假日-刷新
        subButtonItem = new AuthPermissionItem("AccHolidayRefresh", "common_op_refresh", "acc:holiday:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 节假日-新增
        subButtonItem = new AuthPermissionItem("AccHolidayAdd", "common_op_new", "acc:holiday:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 节假日-编辑
        subButtonItem = new AuthPermissionItem("AccHolidayEdit", "common_op_edit", "acc:holiday:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 节假日-删除
        subButtonItem = new AuthPermissionItem("AccHolidayDel", "common_op_del", "acc:holiday:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 权限组菜单
        subMenuItem = new AuthPermissionItem("AccLevel", "acc_leftMenu_level", "acc:level",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 3);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accLevel.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 权限组-刷新
        subButtonItem = new AuthPermissionItem("AccLevelRefresh", "common_op_refresh", "acc:level:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 权限组-新增
        subButtonItem = new AuthPermissionItem("AccLevelAdd", "common_op_new", "acc:level:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 权限组-编辑
        subButtonItem = new AuthPermissionItem("AccLevelEdit", "common_op_edit", "acc:level:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 权限组-删除
        subButtonItem = new AuthPermissionItem("AccLevelDel", "common_op_del", "acc:level:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        /// 权限组-远程开门
        subButtonItem = new AuthPermissionItem("AccLevelOpenDoor", "acc_eventNo_8", "acc:level:openDoor",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 权限组-远程关门
        subButtonItem = new AuthPermissionItem("AccLevelCloseDoor", "acc_eventNo_9", "acc:level:closeDoor",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 权限组-取消报警
        subButtonItem = new AuthPermissionItem("AccLevelCancelAlarmDoor", "acc_eventNo_7", "acc:level:cancelAlarmDoor",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 权限组-远程常开
        subButtonItem = new AuthPermissionItem("AccLevelNormalOpenDoor", "acc_rtMonitor_remoteNormalOpen",
            "acc:level:normalOpenDoor", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        /* // 权限组-启用
        subButtonItem = new AuthPermissionItem("AccLevelEnableDoor", "common_enable", "acc:level:enableDoor",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 9);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 权限组-禁用
        subButtonItem = new AuthPermissionItem("AccLevelDisableDoor", "common_disable", "acc:level:disableDoor",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 10);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);*/

        // 权限组-远程锁定
        subButtonItem = new AuthPermissionItem("AccLevelLockDoor", "acc_newEventNo_233", "acc:level:lockDoor",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 11);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 权限组-远程解锁
        subButtonItem = new AuthPermissionItem("AccLevelUnLockDoor", "acc_newEventNo_234", "acc:level:unLockDoor",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 12);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 权限组-启用当天常开时间段
        subButtonItem = new AuthPermissionItem("AccLevelEnableNormalOpenDoor", "common_rtMonitor_enableIntradayTZ",
            "acc:level:enableNormalOpenDoor", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 13);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 权限组-禁用当天常开时间段
        subButtonItem = new AuthPermissionItem("AccLevelDisableNormalOpenDoor", "common_rtMonitor_disableIntradayTZ",
            "acc:level:disableNormalOpenDoor", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 14);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 权限组-添加门
        subButtonItem = new AuthPermissionItem("AccLevelAddDoor", "acc_map_addDoor", "acc:level:addDoor",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 15);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 权限组-删除门
        subButtonItem = new AuthPermissionItem("AccLevelDelDoor", "acc_level_doorDelete", "acc:level:delDoor",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 16);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 权限组-导出
        /*subButtonItem = new AuthPermissionItem("AccLevelExportDoor", "common_op_export", "acc:level:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 17);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);*/

        // 权限组-导出
        subButtonItem = new AuthPermissionItem("AccLevelExport", "acc_level_exportLevel", "acc:level:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 17);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 权限组-导出权限组门信息
        subButtonItem = new AuthPermissionItem("AccLevelDoorExport", "acc_level_exportLevelDoor",
            "acc:levelDoor:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 18);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 权限组-导入
        subButtonItem = new AuthPermissionItem("AccLevelImport", "acc_level_importLevel", "acc:level:import",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 19);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 权限组-导入门信息
        subButtonItem = new AuthPermissionItem("AccLevelDoorImport", "acc_level_importLevelDoor",
            "acc:levelDoor:import", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 20);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 按权限组设置菜单
        subMenuItem = new AuthPermissionItem("AccPersonLevelByLevel", "common_leftMenu_levelSetByLevel",
            "acc:personLevelByLevel", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 4);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accPersonLevelByLevel.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 按权限组设置-刷新
        subButtonItem = new AuthPermissionItem("AccPersonLevelByLevelRefresh", "common_op_refresh",
            "acc:personLevelByLevel:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 按权限组设置-添加人员
        subButtonItem = new AuthPermissionItem("AccPersonLevelByLevelAddPerson", "pers_common_addPerson",
            "acc:personLevelByLevel:addPerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 按权限组设置-删除人员
        subButtonItem = new AuthPermissionItem("AccPersonLevelByLevelDelPerson", "pers_common_delPerson",
            "acc:personLevelByLevel:delPerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 按权限组设置-导出
        subButtonItem = new AuthPermissionItem("AccPersonLevelByLevelExport", "common_op_export",
            "acc:personLevelByLevel:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 按权限组设置-导入
        subButtonItem = new AuthPermissionItem("AccPersonLevelByLevelImport", "common_op_import",
            "acc:personLevelByLevel:import", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 按人员设置菜单
        subMenuItem = new AuthPermissionItem("AccPersonLevelByPerson", "common_leftMenu_levelSetByPerson",
            "acc:personLevelByPerson", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 5);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accPersonLevelByPerson.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 按人员设置-刷新
        subButtonItem = new AuthPermissionItem("AccPersonLevelByPersonRefresh", "common_op_refresh",
            "acc:personLevelByPerson:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 按人员设置-门禁设置
        subButtonItem = new AuthPermissionItem("AccPersonLevelByPersonSetAccParams", "pers_person_accSetting",
            "acc:personLevelByPerson:setAccParams", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 按人员设置-添加默认权限组
        subButtonItem = new AuthPermissionItem("AccPersonLevelByPersonAddLevel", "common_level_addPersonLevel",
            "acc:personLevelByPerson:addLevel", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 按人员设置-删除所属权限组
        subButtonItem = new AuthPermissionItem("AccPersonLevelByPersonDelLevel", "common_level_delPersonLevel",
            "acc:personLevelByPerson:delLevel", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 按人员设置-导出
        subButtonItem = new AuthPermissionItem("AccPersonLevelByPersonExport", "common_op_export",
            "acc:personLevelByPerson:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 按人员设置-同步
        subButtonItem = new AuthPermissionItem("AccPersonLevelByPersonSync", "acc_personLevelByPerson_syncLevel",
            "acc:personLevelByPerson:sync", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 按部门设置菜单
        subMenuItem = new AuthPermissionItem("AccPersonLevelByDept", "common_leftMenu_levelSetByDept",
            "acc:personLevelByDept", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 6);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accPersonLevelByDept.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 按部门设置-刷新
        subButtonItem = new AuthPermissionItem("AccPersonLevelByDeptRefresh", "common_op_refresh",
            "acc:personLevelByDept:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 按部门设置-添加默认权限组
        subButtonItem = new AuthPermissionItem("AccPersonLevelByDeptAddLevel", "common_level_addDefaultLevel",
            "acc:personLevelByDept:addLevel", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 按部门设置-删除默认权限组
        subButtonItem = new AuthPermissionItem("AccPersonLevelByDeptDelLevel", "common_level_delDefaultLevel",
            "acc:personLevelByDept:delLevel", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 互锁菜单
        subMenuItem = new AuthPermissionItem("AccInterlock", "acc_leftMenu_interlock", "acc:interlock",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 7);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accInterlock.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 互锁-刷新
        subButtonItem = new AuthPermissionItem("AccInterlockRefresh", "common_op_refresh", "acc:interlock:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 互锁-新增
        subButtonItem = new AuthPermissionItem("AccInterlockAdd", "common_op_new", "acc:interlock:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 互锁-编辑
        subButtonItem = new AuthPermissionItem("AccInterlockEdit", "common_op_edit", "acc:interlock:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 互锁-删除
        subButtonItem = new AuthPermissionItem("AccInterlockDel", "common_op_del", "acc:interlock:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 联动菜单
        subMenuItem = new AuthPermissionItem("AccLinkage", "common_leftMenu_linkage", "acc:linkage",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 8);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accLinkage.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 联动-刷新
        subButtonItem = new AuthPermissionItem("AccLinkageRefresh", "common_op_refresh", "acc:linkage:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 联动-新增
        subButtonItem = new AuthPermissionItem("AccLinkageAdd", "common_op_new", "acc:linkage:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 联动-编辑
        subButtonItem = new AuthPermissionItem("AccLinkageEdit", "common_op_edit", "acc:linkage:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 联动-删除
        subButtonItem = new AuthPermissionItem("AccLinkageDel", "common_op_del", "acc:linkage:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 反潜菜单
        subMenuItem = new AuthPermissionItem("AccAntiPassback", "acc_leftMenu_antiPassback", "acc:antiPassback",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 9);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accAntiPassback.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 反潜-刷新
        subButtonItem = new AuthPermissionItem("AccAntiPassbackRefresh", "common_op_refresh",
            "acc:antiPassback:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 反潜-新增
        subButtonItem = new AuthPermissionItem("AccAntiPassbackAdd", "common_op_new", "acc:antiPassback:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 反潜-编辑
        subButtonItem = new AuthPermissionItem("AccAntiPassbackEdit", "common_op_edit", "acc:antiPassback:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 反潜-删除
        subButtonItem = new AuthPermissionItem("AccAntiPassbackDel", "common_op_del", "acc:antiPassback:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 首人常开
        subMenuItem = new AuthPermissionItem("AccFirstOpen", "acc_leftMenu_firstOpen", "acc:firstOpen",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 10);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accFirstOpen.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 首人常开-刷新
        subButtonItem = new AuthPermissionItem("AccFirstOpenRefresh", "common_op_refresh", "acc:firstOpen:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 首人常开-新增
        subButtonItem = new AuthPermissionItem("AccFirstOpenAdd", "common_op_new", "acc:firstOpen:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 首人常开-编辑
        subButtonItem = new AuthPermissionItem("AccFirstOpenEdit", "common_op_edit", "acc:firstOpen:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 首人常开-删除
        subButtonItem = new AuthPermissionItem("AccFirstOpenDel", "common_op_del", "acc:firstOpen:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 首人常门-添加人员
        subButtonItem = new AuthPermissionItem("AccFirstOpenAddPerson", "pers_common_addPerson",
            "acc:firstOpen:addPerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 首人常开-删除人员
        subButtonItem = new AuthPermissionItem("AccFirstOpenDelPerson", "pers_common_delPerson",
            "acc:firstOpen:delPerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 多人开门人员组
        subMenuItem = new AuthPermissionItem("AccCombOpenPerson", "acc_leftMenu_personGroup", "acc:combOpenPerson",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 11);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accCombOpenPerson.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 多人开门人员组-刷新
        subButtonItem = new AuthPermissionItem("AccCombOpenPersonRefresh", "common_op_refresh",
            "acc:combOpenPerson:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 多人开门人员组-新增
        subButtonItem = new AuthPermissionItem("AccCombOpenPersonAdd", "common_op_new", "acc:combOpenPerson:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 多人开门人员组-编辑
        subButtonItem = new AuthPermissionItem("AccCombOpenPersonEdit", "common_op_edit", "acc:combOpenPerson:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 多人开门人员组-删除
        subButtonItem = new AuthPermissionItem("AccCombOpenPersonDel", "common_op_del", "acc:combOpenPerson:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 多人开门人员组-添加人员
        subButtonItem = new AuthPermissionItem("AccCombOpenPersonAddPerson", "pers_common_addPerson",
            "acc:combOpenPerson:addPerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 多人开门人员组-删除人员
        subButtonItem = new AuthPermissionItem("AccCombOpenPersonDelPerson", "pers_common_delPerson",
            "acc:combOpenPerson:delPerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 多人开门
        subMenuItem = new AuthPermissionItem("AccCombOpenDoor", "acc_leftMenu_combOpen", "acc:combOpenDoor",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 12);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accCombOpenDoor.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 多人开门-刷新
        subButtonItem = new AuthPermissionItem("AccCombOpenDoorRefresh", "common_op_refresh",
            "acc:combOpenDoor:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 多人开门-新增
        subButtonItem = new AuthPermissionItem("AccCombOpenDoorAdd", "common_op_new", "acc:combOpenDoor:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 多人开门-编辑
        subButtonItem = new AuthPermissionItem("AccCombOpenDoorEdit", "common_op_edit", "acc:combOpenDoor:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 多人开门-删除
        subButtonItem = new AuthPermissionItem("AccCombOpenDoorDel", "common_op_del", "acc:combOpenDoor:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 验证方式规则
        subMenuItem = new AuthPermissionItem("AccVerifyModeRule", "acc_leftMenu_verifyModeRule", "acc:verifyModeRule",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 13);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accVerifyModeRule.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 验证方式规则-刷新
        subButtonItem = new AuthPermissionItem("AccVerifyModeRuleRefresh", "common_op_refresh",
            "acc:verifyModeRule:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 验证方式规则-新增
        subButtonItem = new AuthPermissionItem("AccVerifyModeRuleAdd", "common_op_new", "acc:verifyModeRule:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 验证方式规则-编辑
        subButtonItem = new AuthPermissionItem("AccVerifyModeRuleEdit", "common_op_edit", "acc:verifyModeRule:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 验证方式规则-删除
        subButtonItem = new AuthPermissionItem("AccVerifyModeRuleDel", "common_op_del", "acc:verifyModeRule:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 验证方式规则-添加门
        subButtonItem = new AuthPermissionItem("AccVerifyModeRuleAddDoor", "acc_map_addDoor",
            "acc:verifyModeRule:addDoor", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 验证方式规则-删除门
        subButtonItem = new AuthPermissionItem("AccVerifyModeRuleDelDoor", "acc_level_doorDelete",
            "acc:verifyModeRule:delDoor", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 验证方式规则人员组
        subMenuItem = new AuthPermissionItem("AccVerifyModeRulePersonGroup", "acc_leftMenu_verifyModeRulePersonGroup",
            "acc:verifyModeRulePersonGroup", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 14);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accVerifyModeRulePersonGroup.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 验证方式规则人员组-刷新
        subButtonItem = new AuthPermissionItem("AccVerifyModeRulePersonGroupRefresh", "common_op_refresh",
            "acc:verifyModeRulePersonGroup:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 验证方式规则人员组-添加人员
        subButtonItem = new AuthPermissionItem("AccVerifyModeRulePersonGroupAddPerson", "pers_common_addPerson",
            "acc:verifyModeRulePersonGroup:addPerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 验证方式规则人员组-删除人员
        subButtonItem = new AuthPermissionItem("AccVerifyModeRulePersonGroupDelPerson", "pers_common_delPerson",
            "acc:verifyModeRulePersonGroup:delPerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 参数设置菜单
        subMenuItem = new AuthPermissionItem("AccParam", "common_leftMenu_paramSet", "acc:param",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 15);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accParam.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 参数设置-刷新
        subButtonItem = new AuthPermissionItem("AccParamRefresh", "common_op_refresh", "acc:param:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 参数设置-编辑
        subButtonItem = new AuthPermissionItem("AccParamEdit", "common_op_edit", "acc:param:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        // 报表一级菜单
        topMenuItem = new AuthPermissionItem("AccReports", "acc_leftMenu_accReports", "acc:transaction:manager",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 4);
        topMenuItem.setParentId(systemItem.getId());
        topMenuItem.setImg("comm_reports.png");
        topMenuItem.setImgHover("comm_reports_over.png");
        topMenuItem = authPermissionService.initData(topMenuItem);

        // 全部记录菜单
        subMenuItem = new AuthPermissionItem("AccTransaction", "common_leftMenu_transaction", "acc:transaction",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 1);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accTransaction.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 全部记录-刷新
        subButtonItem = new AuthPermissionItem("AccTransactionRefresh", "common_op_refresh", "acc:transaction:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 全部记录-清空数据
        subButtonItem = new AuthPermissionItem("AccTransactionDel", "common_op_clearData", "acc:transaction:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 全部记录-导出
        subButtonItem = new AuthPermissionItem("AccTransactionExport", "common_op_export", "acc:transaction:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 导出照片
        subButtonItem = new AuthPermissionItem("AccTransactionExportPhoto", "acc_trans_exportPhoto",
            "acc:transaction:exportPhoto", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 今日访问记录菜单
        subMenuItem = new AuthPermissionItem("AccTransactionToday", "acc_trans_today", "acc:transactionToday",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accTransactionToday.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 今日访问记录-刷新
        subButtonItem = new AuthPermissionItem("AccTransactionTodayRefresh", "common_op_refresh",
            "acc:transactionToday:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 今日访问记录-清空数据
        subButtonItem = new AuthPermissionItem("AccTransactionTodayDel", "common_op_clearData",
            "acc:transactionToday:del", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 今日访问记录-导出
        subButtonItem = new AuthPermissionItem("AccTransactionTodayExport", "common_op_export",
            "acc:transactionToday:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 人员最后访问位置菜单
        // subMenuItem = new AuthPermissionItem("AccPersonLastAddr", "acc_trans_lastAddr", "acc:personLastAddr",
        // AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 3);
        // subMenuItem.setParentId(topMenuItem.getId());
        // subMenuItem.setActionLink("/accPersonLastAddr.do");
        // subMenuItem = authPermissionService.initData(subMenuItem);
        // // 人员最后访问位置-刷新
        // subButtonItem = new AuthPermissionItem("AccPersonLastAddrRefresh", "common_op_refresh",
        // "acc:personLastAddr:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        // subButtonItem.setParentId(subMenuItem.getId());
        // authPermissionService.initData(subButtonItem);
        // // 人员最后访问位置-清空数据
        // subButtonItem = new AuthPermissionItem("AccPersonLastAddrDel", "common_op_del", "acc:personLastAddr:del",
        // AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        // subButtonItem.setParentId(subMenuItem.getId());
        // authPermissionService.initData(subButtonItem);
        // // 人员最后访问位置-导出
        // subButtonItem = new AuthPermissionItem("AccPersonLastAddrExport", "common_op_export",
        // "acc:personLastAddr:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        // subButtonItem.setParentId(subMenuItem.getId());
        // subButtonItem = authPermissionService.initData(subButtonItem);

        // 全部异常记录菜单
        subMenuItem = new AuthPermissionItem("AccAlarmTransaction", "common_leftMenu_exceptTransaction",
            "acc:alarmTransaction", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 4);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accAlarmTransaction.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 全部异常记录-刷新
        subButtonItem = new AuthPermissionItem("AccAlarmTransactionRefresh", "common_op_refresh",
            "acc:alarmTransaction:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 全部异常记录-清空数据
        subButtonItem = new AuthPermissionItem("AccAlarmTransactionDel", "common_op_del", "acc:alarmTransaction:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 全部异常记录-导出
        subButtonItem = new AuthPermissionItem("AccAlarmTransactionExport", "common_op_export",
            "acc:alarmTransaction:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 报警监控报表
        subMenuItem = new AuthPermissionItem("AccAlarmReport", "acc_alarm_list", "acc:alarmReport",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 5);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accAlarmMonitorReport.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 报警监控报表-刷新
        subButtonItem = new AuthPermissionItem("AccAlarmReportRefresh", "common_op_refresh", "acc:alarmReport:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 报警监控报表-导出
        subButtonItem = new AuthPermissionItem("AccAlarmReportExport", "common_op_export", "acc:alarmReport:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 报警监控处理历史
        subMenuItem = new AuthPermissionItem("AccAlarmHistory", "acc_alarm_history", "acc:alarmHistory",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 6);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accAlarmMonitor.do?history");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 报警监控处理历史-刷新
        subButtonItem = new AuthPermissionItem("AccAlarmHistoryRefresh", "common_op_refresh",
            "acc:alarmHistory:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 报警监控处理历史-导出
        subButtonItem = new AuthPermissionItem("AccAlarmHistoryExport", "common_op_export", "acc:alarmHistory:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 以门查询菜单
        subMenuItem = new AuthPermissionItem("AccTransactionByDoor", "acc_leftMenu_searchByDoor",
            "acc:transactionByDoor", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 7);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accTransactionByDoor.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 以门查询-刷新
        subButtonItem = new AuthPermissionItem("AccTransactionByDoorRefresh", "common_op_refresh",
            "acc:transactionByDoor:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 以门查询-导出
        subButtonItem = new AuthPermissionItem("AccTransactionByDoorExport", "common_op_export",
            "acc:transactionByDoor:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 以人员查询菜单
        subMenuItem = new AuthPermissionItem("AccTransactionByPerson", "common_leftMenu_searchByPerson",
            "acc:transactionByPerson", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 8);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accTransactionByPerson.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 以人员查询-刷新
        subButtonItem = new AuthPermissionItem("AccTransactionByPersonRefresh", "common_op_refresh",
            "acc:transactionByPerson:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);
        // 以人员查询-导出
        subButtonItem = new AuthPermissionItem("AccTransactionByPersonExport", "common_op_export",
            "acc:transactionByPerson:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 人事编辑标签页权限初始化，“门禁设置”约定在“人员菜单下的orderNo为100”
        subMenuItem = authPermissionService.getItemByCode("PersPerson");
        if (null != subMenuItem) {
            subButtonItem = new AuthPermissionItem("PersPersonAccEdit", "pers_person_accSet", "pers:person:accEdit",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 100);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);
        }

        subMenuItem = new AuthPermissionItem("AccFirstInLastOut", "acc_leftMenu_firstInLastOut", "acc:firstInLastOut",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 9);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accFirstInLastOut.do");
        subMenuItem = authPermissionService.initData(subMenuItem);

        subButtonItem = new AuthPermissionItem("AccFirstInLastOutRefresh", "common_op_refresh",
            "acc:firstInLastOut:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("AccFirstInLastOutClearData", "common_op_clearData",
            "acc:firstInLastOut:clearData", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.saveItem(subButtonItem);

        subButtonItem = new AuthPermissionItem("AccFirstInLastOutExport", "common_op_export",
            "acc:firstInLastOut:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 异常记录菜单
        subMenuItem = new AuthPermissionItem("AccExceptionRecord", "acc_leftMenu_exceptionRecord", "acc:exceptionRecord",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 10);
        subMenuItem.setParentId(topMenuItem.getId());
        subMenuItem.setActionLink("accExceptionRecord.do");
        subMenuItem = authPermissionService.initData(subMenuItem);

        // 异常记录-刷新
        subButtonItem = new AuthPermissionItem("AccExceptionRecordRefresh", "common_op_refresh",
            "acc:exceptionRecord:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 异常记录-新增
        subButtonItem = new AuthPermissionItem("AccExceptionRecordAdd", "common_op_add",
            "acc:exceptionRecord:add", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 异常记录-编辑
        subButtonItem = new AuthPermissionItem("AccExceptionRecordEdit", "common_op_edit",
            "acc:exceptionRecord:edit", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 异常记录-删除
        subButtonItem = new AuthPermissionItem("AccExceptionRecordDel", "common_op_del",
            "acc:exceptionRecord:del", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 异常记录-查看
        subButtonItem = new AuthPermissionItem("AccExceptionRecordView", "common_op_view",
            "acc:exceptionRecord:view", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 异常记录-保存
        subButtonItem = new AuthPermissionItem("AccExceptionRecordSave", "common_op_save",
            "acc:exceptionRecord:save", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 异常记录-导出
        subButtonItem = new AuthPermissionItem("AccExceptionRecordExport", "common_op_export",
            "acc:exceptionRecord:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 异常记录-发送通知
        subButtonItem = new AuthPermissionItem("AccExceptionRecordSend", "acc_exception_sendNotification",
            "acc:exceptionRecord:send", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 异常记录-重新发送
        subButtonItem = new AuthPermissionItem("AccExceptionRecordResend", "acc_exception_resendFailed",
            "acc:exceptionRecord:resend", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 9);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 异常记录-清空数据
        subButtonItem = new AuthPermissionItem("AccExceptionRecordClearData", "common_op_clearData",
            "acc:exceptionRecord:clearData", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 10);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /** --------------------------- 敏感信息保护控制 --------------------------- */
        subMenuItem = new AuthPermissionItem("AccEncryptProp", "common_param_infoProtection", "acc:encryptProp",
            AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 999);
        subMenuItem.setParentId(systemItem.getId());
        subMenuItem = authPermissionService.initData(subMenuItem);

        subButtonItem = new AuthPermissionItem("AccPinEncryptProp", "pers_person_pin", "acc:pin:encryptProp",
            AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("AccNameEncryptProp", "pers_person_wholeName", "acc:name:encryptProp",
            AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("AccCardNoEncryptProp", "pers_card_cardNo", "acc:cardNo:encryptProp",
            AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("AccMobilePhoneEncryptProp", "pers_person_mobilePhone",
            "acc:mobilePhone:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("AccHeadPortraitEncryptProp", "pers_person_photo",
            "acc:headPortrait:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("AccCapturePhotoEncryptProp", "pers_capture_catchPhoto",
            "acc:capturePhoto:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 6);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
    }

    /**
     * 时间段初始化
     */
    private void initAccTimeSeg() {
        AccTimeSegItem timeSegItem = new AccTimeSegItem(I18nUtil.i18nCode("common_timeSeg_24HoursAccess"),
            I18nUtil.i18nCode("common_timeSeg_24HoursAccess"), true, "00:00", "23:59", "00:00", "00:00", "00:00",
            "00:00", "00:00", "23:59", "00:00", "00:00", "00:00", "00:00", "00:00", "23:59", "00:00", "00:00", "00:00",
            "00:00", "00:00", "23:59", "00:00", "00:00", "00:00", "00:00", "00:00", "23:59", "00:00", "00:00", "00:00",
            "00:00", "00:00", "23:59", "00:00", "00:00", "00:00", "00:00", "00:00", "23:59", "00:00", "00:00", "00:00",
            "00:00", "00:00", "23:59", "00:00", "00:00", "00:00", "00:00", "00:00", "23:59", "00:00", "00:00", "00:00",
            "00:00", "00:00", "23:59", "00:00", "00:00", "00:00", "00:00");
        accTimeSegService.initData(timeSegItem);
    }

    /**
     * 夏令时初始化
     */
    private void initAccDSTime() {
        // AccDSTimeItem accDSTimeItem = new AccDSTimeItem(I18nUtil.i18nCode("acc_dev_usadst"),
        // AccDSTimeItem.DSTIME_MODE_TWO, AccDSTimeItem.DSTIME_STARTTIME, AccDSTimeItem.DSTIME_ENDTIME, true);
        // accDSTimeService.initData(accDSTimeItem);

        // 初始化和时区绑定的夏令时
        AccDSTimeItem accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc1000_0", "10010002", "04010003", "+1000",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc1000_1", "10010002", "04010003", "+1000",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0330_0", "03020002", "11010002", "-0330",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_1000_0", "03020002", "11010002", "-1000",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0200_0", "03050002", "09050002", "-0200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0930_0", "10010002", "04010003", "+0930",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0100_0", "03050000", "10050001", "-0100",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0400_0", "03020002", "11010002", "-0400",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0400_1", "09010000", "04010000", "-0400",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0400_2", "10010000", "03050000", "-0400",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0300_0", "03050622", "10050623", "-0300",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0300_1", "03020002", "11010002", "-0300",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0200_0", "03050002", "10050003", "+0200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0200_1", "03050003", "10050004", "+0200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0200_2", "03050003", "10050004", "+0200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0200_3", "03050502", "10050002", "+0200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0200_4", "03050500", "10050501", "+0200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0200_5", "03050000", "10050000", "+0200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0200_6", "03050500", "10050500", "+0200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0200_7", "03050600", "10050601", "+0200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        // accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0200_8", "01010500", "01050100", "+0200",
        // AccDSTimeItem.DSTIME_MODE_TWO, true);
        // accDSTimeService.initData()(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0600_0", "03020002", "11010002", "-0600",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0600_1", "04010002", "10050002", "-0600",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0600_2", "09010622", "04010622", "-0600",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc1300_0", "09050003", "04010004", "+1300",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0500_0", "03020000", "11010001", "-0500",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0500_1", "03020002", "11010002", "-0500",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0500_2", "03020002", "11010002", "-0500",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0500_3", "03020002", "11010002", "-0500",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0500_4", "03020002", "11010002", "-0500",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0800_0", "03020002", "11010002", "-0800",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0800_1", "03020002", "11010002", "-0800",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0330_0", "03040100", "09030300", "+0330",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0000_0", "03050001", "10050002", "-0000",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc1200_0", "11020002", "01030003", "+1200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc1200_1", "03050002", "10050003", "+1200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc1200_2", "09050002", "04010003", "+1200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc1100_0", "10010002", "04010003", "+1100",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0700_0", "04010002", "10050002", "-0700",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0700_1", "03020002", "11010002", "-0700",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0100_0", "03050002", "10050003", "+0100",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0100_1", "03050002", "10050003", "+0100",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0100_2", "05030002", "04020003", "+0100",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0100_3", "03050002", "10050003", "+0100",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0100_4", "03050002", "10050003", "+0100",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0900_0", "03020002", "11010002", "-0900",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
    }

    /**
     * 权限组初始化
     */
    private void initAccLevel() {
        AccTimeSegItem accTimeSegItem = accTimeSegService.getInitTimeSeg();
        AuthAreaItem authAreaItem = authAreaService.getItemByCode("1");
        AccLevelItem accLevelItem =
            new AccLevelItem(I18nUtil.i18nCode("acc_level_master"), true, authAreaItem.getId(), accTimeSegItem.getId());
        accLevelService.initData(accLevelItem);
    }

    /**
     * 初始化参数
     */
    public void initAccParams() {
        baseSysParamService.initData(new BaseSysParamItem("acc.downNewlog", "0", "定时下载新记录的时间点"));
        baseSysParamService.initData(new BaseSysParamItem("acc.personPhotoMaxHeight", "140", "实时监控页面弹出人员照片大小"));
        // 屏蔽设置中开启、关闭显示照片和声音的功能
        // baseSysParamService.initData(new BaseSysParamItem("acc.isShowPhoto","1", "开启显示照片功能"));
        // baseSysParamService.initData(new BaseSysParamItem("acc.isShowSound","1", "开启声音提醒功能"));
        baseSysParamService.initData(new BaseSysParamItem("acc.downNewLogExpression", "0 0 0 * * ?", "定时获取事件记录"));
        baseSysParamService.initData(new BaseSysParamItem("acc.receiver", "", "收件人邮箱"));
        // baseSysParamService.initData(new BaseSysParamItem("acc.addDeviceOperKeyword", "xxx", "是否需要进行授权密码校验和操作权限"));
        baseSysParamService.initData(new BaseSysParamItem("acc.queryDoorEventLastLimit", "5", "获取查询的最近发生记录数"));
        baseSysParamService.initData(new BaseSysParamItem("acc.smsReceiver", "", "短信收件人手机号码"));
        // 信息自动导出
        baseSysParamService.initData(new BaseSysParamItem("acc.autoExportFrequency", "0", "自动导出类型"));
        baseSysParamService.initData(new BaseSysParamItem("acc.monthFrequency", "0", "按月导出类型"));
        baseSysParamService.initData(new BaseSysParamItem("acc.monthFrequencyDate", "29", "按月导出自定义月份"));
        baseSysParamService.initData(new BaseSysParamItem("acc.dayFrequencyHour", "24", "每天导出自定义小时"));
        baseSysParamService.initData(new BaseSysParamItem("acc.dayFrequencyMinute", "60", "每天导出自定义分钟"));
        baseSysParamService.initData(new BaseSysParamItem("acc.exportMode", "2", "导出数据选项"));
        baseSysParamService.initData(new BaseSysParamItem("acc.autoExportEmail", "", "自动导出发送的邮箱"));
        baseSysParamService.initData(new BaseSysParamItem("acc.autoExportCron", "", "自动导出定时任务cron表达式"));


        baseSysParamService.initData(new BaseSysParamItem("acc.restOutTime", "10", "休息时间段：超过时间x分钟时同时推送至组长和课长"));
        baseSysParamService.initData(new BaseSysParamItem("acc.workOutTime", "10", "上班时间段：超时时间x分钟时推送至组长"));
        baseSysParamService.initData(new BaseSysParamItem("acc.workAndRestOutTime", "10", "上班时间段：超时时间x分钟时同时推送至课长"));

        JSONObject values = new JSONObject();
        values.put("keptMonth", "15");
        values.put("runtime", "01:00:00");
        values.put("keptType", BaseDataConstants.DATA_CLEAN_KEPTMONTH);
        values.put("keptPhoto", "15");
        values.put("keptPhotoType", BaseDataConstants.DATA_CLEAN_KEPTMONTH);
        BaseSysParamItem item =
            new BaseSysParamItem("accReportDataClean", values.toString(), "base_dataClean_accTrans");
        baseSysParamService.initData(item);

        // 初始化消息推送参数
        JSONObject notificationValues = new JSONObject();
        notificationValues.put("Email", "1");
        notificationValues.put("SMS", "1");
        notificationValues.put("Whatsapp", "1");
        notificationValues.put("Line", "1");
        notificationValues.put("APP", "1");
        item = new BaseSysParamItem("accMessageNotification", notificationValues.toString(), "base_dataClean_accTrans");
        baseSysParamService.initData(item);

        // 详情许可显示
        if (accSupportFuncService.isSupportLcdRTMonitor()) {
            baseSysParamService.initData(new BaseSysParamItem("lcd.version", "true", "lcd许可", true));
        }

        // 个人敏感信息保护-抓拍照片（true启用、false禁用、默认启用）
        // baseSysParamService.initData(new BaseSysParamItem("acc.capturePhoto.encryptProp", "true", "个人敏感信息保护-抓拍照片"));
    }

    /**
     * 初始化管理员app权限
     */
    private void initAppMenus() {
        AuthPermissionItem appModuleItem = null;
        AuthPermissionItem appMenuItem = null;
        AuthPermissionItem appButtonItem = null;

        appModuleItem = authPermissionService.getItemByCode("App");
        if (null == appModuleItem) {
            // 管理员APP模块
            appModuleItem = new AuthPermissionItem("App", "app_module", "app", AuthContants.RESOURCE_TYPE_APP_SYSTEM,
                ZKConstant.TRUE, 998);
            authPermissionService.initData(appModuleItem);
        }
        appMenuItem = new AuthPermissionItem("AppAcc", "app_acc", "app:APPacc", AuthContants.RESOURCE_TYPE_APP_MENU,
            ZKConstant.TRUE, 2);
        appMenuItem.setParentId(appModuleItem.getId());
        appMenuItem = authPermissionService.initData(appMenuItem);
        // 门禁--实时监控
        appButtonItem = new AuthPermissionItem("AppAccMonitor", "app_acc_monitor", "app:APPaccMonitor",
            AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 1);
        appButtonItem.setParentId(appMenuItem.getId());
        authPermissionService.initData(appButtonItem);
        // 门禁--设备
        appButtonItem = new AuthPermissionItem("AppAccDevice", "app_acc_device", "app:APPaccDevice",
            AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 2);
        appButtonItem.setParentId(appMenuItem.getId());
        authPermissionService.initData(appButtonItem);
        // 门禁--报警监控
        appButtonItem = new AuthPermissionItem("AppAccAlarm", "app_acc_alarm", "app:APPaccAlarm",
            AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 3);
        appButtonItem.setParentId(appMenuItem.getId());
        authPermissionService.initData(appButtonItem);
        // 门禁--报表
        appButtonItem = new AuthPermissionItem("AppAccReport", "app_acc_report", "app:APPaccReport",
            AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 4);
        appButtonItem.setParentId(appMenuItem.getId());
        authPermissionService.initData(appButtonItem);

        // 门禁--远程控制
        appButtonItem = new AuthPermissionItem("AppAccRemoteControl", "app_acc_remoteControl",
            "app:APPaccRemoteControl", AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 5);
        appButtonItem.setParentId(appMenuItem.getId());
        authPermissionService.initData(appButtonItem);
        // 门禁--锁定
        appButtonItem = new AuthPermissionItem("AppAccLockdown", "app_acc_lockdown", "app:APPaccLockdown",
            AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 6);
        appButtonItem.setParentId(appMenuItem.getId());
        authPermissionService.initData(appButtonItem);
    }

    /**
     * 初始化模块版本信息
     */
    private void initUpgradeVersion() {
        String curVersion = VersionUtil.getReleaseGitTags(BaseConstants.ACC);
        // 快照版取不到tag name 不执行需要执行以下代码
        if (StringUtils.isNotBlank(curVersion)) {
            BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("AccUpgradeVersion");
            if (StringUtils.isBlank(baseSysParamItem.getId())) {
                // 没有则初始化版本信息
                baseSysParamItem = new BaseSysParamItem("AccUpgradeVersion", curVersion, "Acc Upgrade Version", true);
            }
            baseSysParamService.saveItem(baseSysParamItem);
        }
    }
}