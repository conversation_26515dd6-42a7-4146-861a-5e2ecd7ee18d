/**
 * File Name: AccCombOpenComb Created by GenerationTools on 2018-03-14 下午03:11 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccCombOpenCombItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.util.List;

/**
 * 对应百傲瑞达 AccCombOpenCombService
 * 
 * <AUTHOR>
 * @date: 2018-03-14 下午03:11
 * @version v1.0
 */
public interface AccCombOpenCombService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    AccCombOpenCombItem saveItem(AccCombOpenCombItem item);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AccCombOpenCombItem> getByCondition(AccCombOpenCombItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    AccCombOpenCombItem getItemById(String id);

    /**
     * 多人开门中间表迁移
     *
     * @param accCombOpenCombItems
     */
    void handlerTransfer(List<AccCombOpenCombItem> accCombOpenCombItems);

    /**
     * 根据多人开门id查询
     *
     * @param combOpenDoorId:多人开门id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccCombOpenCombItem>
     * <AUTHOR>
     * @date 2021-02-04 10:34
     * @since 1.0.0
     */
    List<AccCombOpenCombItem> getItemsByCombOpenDoorId(String combOpenDoorId);
}