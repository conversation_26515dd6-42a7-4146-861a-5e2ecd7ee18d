/**
 * File Name: AccLevel Created by GenerationTools on 2018-03-02 下午02:15 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.acc.api.vo.AccApiLevelItem;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;


/**
 * 对应百傲瑞达 AccLevelService
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-03-02 下午02:15
 */
public interface AccLevelService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     *
     * @param item
     */
    AccLevelItem saveItem(AccLevelItem item);

    /**
     * 根据条件查询
     *
     * @param condition
     * @return
     */
    List<AccLevelItem> getByCondition(AccLevelItem condition);

    /**
     * 通知其他模块删除权限
     *
     * @param doorIds:门id
     * @param personIds:人员id
     * @return void
     * <AUTHOR>
     * @date 2021-03-23 15:30
     * @since 1.0.0
     */
    void deleteAccLevel4OtherModule(List<String> doorIds, List<String> personIds);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    AccLevelItem getItemById(String id);

    /**
     * 权限组初始化
     */
    AccLevelItem initData(AccLevelItem item);

    /**
     * 添加人员权限，调用前需要先对人员id进行分批操作
     *
     * @return
     * <AUTHOR>
     * @since 2018/3/22 17:18
     */
    void addPersonLevel(String levelId, String personIds);

    /**
     * 删除人员的权限包含删除数据库
     *
     * @return
     * <AUTHOR>
     * @since 2018/3/26 10:08
     */
    void immeDelPersonLevel(List<String> levelIds, String personIds);

    /**
     * @return
     * @Description: 只删除人员权限不删除数据库
     * <AUTHOR>
     * @date 2018/9/12 17:17
     */
    void immeDelPersonLevelToDev(List<String> levelIds, String personIds);

    /**
     * 批量删除人员权限 数据库层操作
     *
     * @return
     * <AUTHOR>
     * @since 2018/3/26 10:17
     */
    void delBatchLevel(List<String> levelIds, List<String> personIds);

    /**
     * 更新人员权限（为了进度条的处理，需要放到一个方法里）
     *
     * @return
     * <AUTHOR>
     * @since 2018/3/26 11:05
     */
    void immeUpdatePersonLevel(String personId, String levelIds);

    /**
     * 按部门设置右侧权限组list
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/12 20:20
     */
    Pager getDeptLevel(AccDeptLevelItem codition, int pageNo, int pageSize);

    /**
     * 根据人员id获取对应权限组集合
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/13 9:29
     */
    List<AccLevelItem> getLevelByPersonId(String id);

    /**
     * 获取初始化通用权限
     *
     * @return
     */
    AccLevelItem getMasterLevel();

    /**
     * 获取权限组下拉框
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/17 10:47
     */
    List<SelectItem> getLevelList(String sessionId);

    /**
     * 权限组添加门
     *
     * @param levelId
     * @param doorIdList
     * @throws Exception
     */
    void immeAddLevelDoor(String levelId, List<String> doorIdList, boolean changeLevel);

    /**
     * 权限组删除门
     *
     * @param levelId
     * @param doorIdList
     * @throws Exception
     */
    void immeDelLevelDoor(String levelId, List<String> doorIdList);

    /**
     * 下发删除权限组权限到设备
     *
     * @param levelId
     * @param personIdList
     * @author: mingfa.zheng
     * @date: 2018/4/26 13:49
     * @return:
     */
    void immeDelLevel(String levelId, List<String> personIdList);

    /**
     * 按人员id删除人员权限
     *
     * @param personParamMap 按id、pin删除人员，格式{"pin": 1,2,3}， 或者 {"id": 1,2,3}，只传一种即可
     * @author: mingfa.zheng
     * @date: 2018/4/26 14:49
     * @return: List<AccPersonOptBean> [{"id": 1, "pin": 1}]
     */
    List<AccPersonOptItem> getPersonByIds(Map<String, Object> personParamMap);

    /**
     * 按人员id获取人员数据，查询人员对象必须进入这里
     *
     * @param personIds
     * @return 人员数据和指纹数据字典 ，格式 {"person": [{"pin":123, "cardNo":1234}, {}], "bioTemplate": [{"pin": 123, "templateNo":
     *         1}]}
     * @throws Exception
     */
    AccPersonInfoItem getPersonByIds(List<String> personIds);

    /**
     * 下发人员权限命令到设备
     *
     * @author: mingfa.zheng
     * @date: 2018/5/15 11:32
     * @return:
     */
    void setPersonLevelToDevice(String deviceId, AccPersonInfoItem personInfoBean, boolean isSyncData);

    /**
     * 下发人员卡号权限命令到设备
     *
     * @author: mingfa.zheng
     * @date: 2018/5/15 11:32
     * @return:
     */
    void setPersonCardLevelToDevice(String deviceId, AccPersonInfoItem personInfoBean);

    List<String> searchPersonByDev(String devId);

    /**
     * @param paramIds 人员Ids或者门Ids
     * @param levelIds 权限组Ids
     * @param type 插入门“door” 或者 人员 “person”
     * @return
     * @Description: 添加人员/门权限到数据库中，使用批量操作
     * <AUTHOR>
     * @date 2018/4/27 19:22
     */
    void addLevelByParamIds(String paramIds, String levelIds, String type);

    /**
     * 判断名称是否有效/允许
     *
     * @author: mingfa.zheng
     * @date: 2018/3/15 16:09
     * @return:
     */
    AccLevelItem getItemByName(String name);

    /**
     * 查询权限组中的人员数量
     *
     * @param levelId 权限组id
     * @author: mingfa.zheng
     * @date: 2018/5/3 15:39
     * @return:
     */
    Long getLevelPersonCount(String levelId);

    /**
     * 获取权限组中门的数量
     *
     * @param levelId 权限组id
     * @author: mingfa.zheng
     * @date: 2018/5/3 15:40
     * @return:
     */
    Long getLevelDoorCount(String levelId);

    /**
     * @param accPersonLevelItem 权限组item
     * @param pageNo 页数
     * @param pageSize 记录条数
     * @return
     * @Description: 获取按人员设置右侧权限组列表
     * <AUTHOR>
     * @date 2018/5/8 9:42
     */
    Pager getAccPersonLevelItemsByPage(AccPersonLevelItem accPersonLevelItem, int pageNo, int pageSize);

    /**
     * 下发访客权限到设备
     *
     * @author: mingfa.zheng
     * @date: 2018/5/8 14:22
     * @return:
     */
    void setVisitorLevelToDev(List<AccPersonInfoItem> accPersonInfoItemList, List<String> addAccLevelIds,
        List<String> delAccLevelIds);

    /**
     * 同步访客权限到设备
     *
     * @author: mingfa.zheng
     * @date: 2018/6/28 14:22
     * @return:
     */
    void syncVisitorLevelToDev(List<AccPersonInfoItem> accPersonInfoItemList, String deviceId,
        List<String> addAccLevelIds);

    /**
     * 根据设备id删除对应权限组中的门
     *
     * @param devId
     * <AUTHOR>
     * @since 2017年11月10日 下午3:44:51
     */
    void delLevelByDevId(String devId);

    /**
     * 删除人员相关的信息：权限，首人、多人 传入人员id或者pin，delData参数表上只需要删除命令，不需要删除表数据，如人员离职，不带这个参数则删除权限组人员
     *
     * @param personOptMap {"id": idColl, "pin": pinColl, "delData": "false"}中的
     * @author: mingfa.zheng
     * @date: 2018/5/11 16:10
     */
    void immeDelPersonLevel(Map<String, String> personOptMap);

    /**
     * @Description: 权限组时间段改变，重新下发权限
     * @author: mingfa.zheng
     * @date: 2018/8/14 16:32
     * @param: [deviceId, personIdList]
     * @return: void
     **/
    void syncTimeSegLevelToDev(String deviceId, List<String> personIdList);

    /**
     * 权限组时间段改变，重新下发访客权限
     *
     * @param levelId
     * @return void
     * <AUTHOR>
     * @date 2019/10/10 19:13
     */
    void syncTimeSegVisLevelToDev(String levelId);

    /**
     * @param devId 设备id
     * @param wgReaderId 韦根读头id
     * @return
     * @Description: 下发读头所在门的权限组给绑定的当作韦根读头的一体机设备
     * <AUTHOR>
     * @date 2018/5/29 9:34
     */
    void setLevelToDevAsReader(String devId, String wgReaderId);

    /**
     * 根据当前登录用户过滤权限数据
     *
     * @param sessionId
     * @param codition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager loadPagerByAuthFilter(String sessionId, AccLevelItem codition, int pageNo, int pageSize);

    /**
     * @Description: 获取门数量
     * @author: mingfa.zheng
     * @date: 2018/7/26 9:19
     * @param: [sessionId, levelId]
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    ZKResultMsg getDoorCount(String sessionId, String levelId);

    /**
     * @Description: 根据权限组id获取该权限组下的人员ids
     * @author: mingfa.zheng
     * @date: 2018/8/8 14:56
     * @param: [levelId]
     * @return: java.util.List<java.lang.String>
     **/
    List<String> getPersonIdsByLevelId(String levelId);

    /**
     * 权限组加门下发命令
     *
     * @param personIds
     * @param deviceIds
     * @param levelId
     */
    void handleLevelAddDoor(String personIds, String deviceIds, String levelId);

    /**
     * 删除门人员权限
     *
     * @param levelId
     * @param personIds
     * @param deviceIds
     */
    void handleLevelDelDoor(String levelId, String personIds, String deviceIds);

    /**
     * @Description: 根据权限组id和设备ids下发访客权限到设备。
     * @author: mingfa.zheng
     * @date: 2018/8/8 15:39
     * @param: [levelId, deviceIdList]
     * @return: void
     **/
    void setVisitorToDev(String levelId, List<String> deviceIdList);

    /**
     * @Description: 根据权限组id和设备ids删除访客权限
     * @author: mingfa.zheng
     * @date: 2018/8/8 19:47
     * @param: [levelId]
     * @return: void
     **/
    void delVisitorToDev(String levelId, List<String> deviceIdList);

    /**
     * @Description: 权限组删除门--数据库
     * @author: mingfa.zheng
     * @date: 2018/8/8 18:02
     * @param: [levelId, doorIdList]
     * @return: void
     **/
    void delLevelDoorByParams(String levelId, List<String> doorIdList);

    /**
     * @Description: 根据权限组id删除权限组和门的中间表数据
     * @author: mingfa.zheng
     * @date: 2018/8/9 16:36
     * @param: [levelId]
     * @return: void
     **/
    void delAccLevelDoorByLevelId(String levelId);

    /**
     * @Description: 判断权限组是否可以删除
     * @author: mingfa.zheng
     * @date: 2018/8/10 15:00
     * @param: [ids]
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    ZKResultMsg levelIsDelete(String ids);

    /**
     * @param devId
     * @return
     * @Description: 根据设备id获取权限组id
     * <AUTHOR>
     * @date 2018/8/13 14:39
     */
    List<String> getLevelByDevId(String devId);

    /**
     * @param levelId
     * @param doorIdList
     * @return
     * @Description: 添加设备权限
     * <AUTHOR>
     * @date 2018/8/13 14:39
     */
    void addLevelDoor(String levelId, List<String> doorIdList);

    /**
     * @param doorId
     * @return
     * @Description: 根据门id获取权限组id
     * <AUTHOR>
     * @date 2018/8/13 14:43
     */
    List<String> getLevelIdByDoorId(String doorId);

    /**
     * @Description: 根据权限组id获取设备ids
     * @author: mingfa.zheng
     * @date: 2018/8/14 15:15
     * @param: [levelId]
     * @return: java.util.List<java.lang.String>
     **/
    List<String> getDevIdsByLevelId(String levelId);

    /**
     * @param personId
     * @return
     * @Description: 手机app获取用户拥有权限的门
     * <AUTHOR>
     * @date 2018/9/27 10:16
     */
    List<String> getDoorIdsForApp(String personId);

    void syncVisitorToDevByLevelIdsAndDevId(List<String> levelList, String deviceId);

    /**
     * 同步人员权限----API
     *
     * @param
     * @return
     * @auther lambert.li
     * @date 2018/11/17 14:27
     */
    ApiResultMessage syncApiPersonLevel(String pin, String levelIds);

    /**
     * 删除人员权限---API
     *
     * @param
     * @return
     * @auther lambert.li
     * @date 2018/11/17 14:36
     */
    ApiResultMessage deleteApiPersonLevel(String pin, String levelIds);

    ApiResultMessage addApiLevel(AccApiLevelItem accApiLevelItem);

    /**
     * 同步权限组人员权限---API
     *
     * @param
     * @return
     * @auther lambert.li
     * @date 2018/11/17 14:42
     */
    ApiResultMessage syncApiLevel(String levelId);

    /**
     * 门禁权限组-accLevel迁移
     *
     * @param accLevelItems
     */
    void handlerTransfer(List<AccLevelItem> accLevelItems);

    /**
     * 门禁权限组-accLevelDoor迁移
     *
     * @param accLevelDoorItems
     */
    void handlerTransferToDoor(List<AccLevelDoorItem> accLevelDoorItems);

    /**
     * 门禁权限组-accLevelPerson迁移
     *
     * @param accPersonLevelItems
     */
    void handlerTransferToPerson(List<AccPersonLevelItem> accPersonLevelItems);

    /**
     * 门禁权限组-accDeptLevel迁移
     *
     * @param accDeptLevelItems
     */
    void handlerTransferToDept(List<AccDeptLevelItem> accDeptLevelItems);

    /**
     * 权限组组装人员权限相关命令下发给设备
     *
     * @param levelIds
     * @param personIds
     */
    void setPersonLevelToDev(String levelIds, String personIds);

    /**
     * 根据权限组id获取权限组的门数
     *
     * @param levelId
     * @return
     */
    Long getDoorCountByLevelId(String levelId);

    void immeDelLevel(String deviceId, String levelId, List<String> personIdList);

    /**
     * 获取人员有权限的门信息
     *
     * @param personPin
     * @return
     * @auther zbx.zhong
     * @date 2019/6/19 13:37
     */
    ZKResultMsg getPersonLevelDoor(String personPin, boolean isAdmin);

    /**
     * 从二号项目迁移代码根据人员编号获取人员权限组信息
     *
     * @param personPins
     * @return
     * @auther zbx.zhong
     * @date 2019/7/1 9:48
     */
    ZKResultMsg getLevelByPersonPin(String personPins);

    /**
     * 获取所有权限组名称
     *
     * @return
     * <AUTHOR>
     * @Param []
     * @since 2019-08-27 14:27
     */
    List<String> getAllLevelNames();

    /**
     * 根据权限组id获取权限组里的人员id
     *
     * @return
     * <AUTHOR>
     * @Param [levelIds]
     * @since 2019-08-27 15:43
     */
    List<String> getPersonIdsByLevelIdIn(List<String> levelIds);

    /**
     * 获取所有权限组名称和权限组id的map集合
     *
     * @return
     * <AUTHOR>
     * @Param []
     * @since 2019-08-27 17:32
     */
    Map<String, String> getLevelNameIdMap();

    /**
     * 从二号项目迁移代码:根据区域获取门禁权限组
     *
     * @param
     * @return
     * @auther zbx.zhong
     * @date 2019/8/22 9:37
     */
    ZKResultMsg getLevelByAreaCodes(String areaCodes, int page, int pageSize);

    /**
     * 批量启用所选权限组的门
     *
     * @param levelIds
     * @param sessionId
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-10 16:38
     */
    ZKResultMsg enableDoor(String levelIds, String sessionId);

    /**
     * 批量禁用所选权限组下的门
     *
     * @param ids
     * @param sessionId
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-13 11:07
     */
    void disableDoor(String ids, String sessionId);

    /**
     * 操作门
     *
     * @param opType
     * @param openInterval
     * @param levelIds
     * @param sessionId
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-02-11 11:42
     */
    Map<String, String> operateDoor(String opType, String openInterval, String levelIds, String sessionId);

    /**
     * 处理返回数据
     *
     * @param dataMap
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-02-11 14:23
     */
    ZKResultMsg dealResultData(Map<String, String> dataMap);

    /**
     * 检查时间段是否被使用
     *
     * @param timeSegId
     * @return
     */
    boolean checkTimeSegUsed(String timeSegId);

    /**
     * 根据设备id获取权限组下人员id
     *
     * @param devId:设备id
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2021-01-20 9:14
     * @since 1.0.0
     */
    List<String> getPersonIdByDevIdOrParentDevId(String devId);

    /**
     * 根据设备id获取所属权限组id
     *
     * @param devId:设备id
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2021-01-20 9:16
     * @since 1.0.0
     */
    List<String> getLevelIdsByDeviceId(String devId);

    /**
     * 保存权限组-门中间表信息
     *
     * @param levelDoors:
     * @return void
     * <AUTHOR>
     * @date 2021-01-28 9:37
     * @since 1.0.0
     */
    List<AccLevelDoorItem> saveAccLevelDoorList(List<AccLevelDoorItem> levelDoors);

    /**
     * 获取导出数据
     *
     * @param condition:条件
     * @param beginIndex:起始位置
     * @param endIndex:结束位置
     * @return java.util.List
     * <AUTHOR>
     * @date 2021-01-29 14:13
     * @since 1.0.0
     */
    List<?> getItemData(Class<?> cls, BaseItem condition, int beginIndex, int endIndex);

    /**
     * 根据权限组id获取门禁权限组-门中间表信息
     *
     * @param levelDoorIds:权限组id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccLevelDoorItem>
     * <AUTHOR>
     * @date 2021-02-01 18:30
     * @since 1.0.0
     */
    List<AccLevelDoorItem> getLevelDoorItemsByLevelDoorIds(List<String> levelDoorIds);

    /**
     * 根据权限组id获取权限组信息
     *
     * @param levelIdList:权限组id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccLevelItem>
     * <AUTHOR>
     * @date 2021-02-01 18:44
     * @since 1.0.0
     */
    List<AccLevelItem> getItemsByIdList(List<String> levelIdList);

    /**
     * 根据设备序列号获取门禁权限组-门中间表信息
     *
     * @param devSn:设备序列号
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccLevelDoorItem>
     * <AUTHOR>
     * @date 2021-02-02 14:10
     * @since 1.0.0
     */
    List<AccLevelDoorItem> getLevelDoorItemsByDevSn(String devSn);

    /**
     * 根据多个设备序列号获取门禁权限组-门中间表信息
     *
     * @param deviceSnList 设备序列号列表
     * @return 门禁权限组-门中间表信息
     * <AUTHOR>
     * @date 2021/4/30 9:49
     */
    List<AccLevelDoorItem> getLevelDoorItemsByDeviceSnList(List<String> deviceSnList);

    /**
     * 根据时间段id统计数量
     *
     * @param timeSegId:时间段id
     * @return int
     * <AUTHOR>
     * @date 2021-02-03 10:34
     * @since 1.0.0
     */
    int countByTimeSegId(String timeSegId);

    /**
     * 根据权限组id和区域id获取门数量
     *
     * @param levelId:权限组id
     * @param areaId:区域id
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021-02-03 15:18
     * @since 1.0.0
     */
    Long getDoorCountByLevelIdAndAreaIds(String levelId, List<String> areaId);

    /**
     * 分页查询，无复杂的业务逻辑
     *
     * @param condition:查询条件
     * @param page:页索引，从0开始
     * @param size:每页几条
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-02-03 11:10
     * @since 1.0.0
     */
    Pager getSimpleItemsByPage(BaseItem condition, int page, int size);

    /**
     * 根据条件查询
     *
     * @param condition:查询条件
     * @return java.util.List<?>
     * <AUTHOR>
     * @date 2021-02-03 11:42
     * @since 1.0.0
     */
    List<?> getItemsByCondition(BaseItem condition);

    /**
     * 保存item实体，无复杂的业务逻辑
     *
     * @param item:权限组信息
     * @return com.zkteco.zkbiosecurity.acc.vo.AccLevelItem
     * <AUTHOR>
     * @date 2021-02-03 11:21
     * @since 1.0.0
     */
    AccLevelItem saveSimpleItem(AccLevelItem item);

    /**
     * 根据权限组id查询权限组-门中间表信息
     *
     * @param levelId:权限组id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccLevelDoorItem>
     * <AUTHOR>
     * @date 2021-02-03 11:29
     * @since 1.0.0
     */
    List<AccLevelDoorItem> getLevelDoorItemsByLevelId(String levelId);

    /**
     * 根据设备序列号查询权限组-人员信息
     *
     * @param devSn:设备序列号
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccLevelPersonItem>
     * <AUTHOR>
     * @date 2021-02-04 14:03
     * @since 1.0.0
     */
    List<AccLevelPersonItem> getLevelPersonItemByDevSn(String devSn);

    /**
     * 根据设备序列号获取权限组id
     *
     * @param devSn:设备序列号
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2021-02-05 14:27
     * @since 1.0.0
     */
    List<String> getLevelIdsByDevSn(String devSn);

    /**
     * 根据设备ID和权限组ID查询出门编号
     *
     * @param levelId 权限组id
     * @param deviceId 设备id
     * @return 门编号
     * <AUTHOR>
     * @date 2021/4/20 10:21
     */
    List<Short> getDoorNoByLevelIdAndDevId(String levelId, String deviceId);

    /**
     * 删除设备人员权限
     *
     * @param devItemList:设备信息
     * @param personInfoMap:{"id": [1,2,3], "pin": [1,2,3]}
     * @return void
     * <AUTHOR>
     * @date 2021-06-08 16:54
     * @since 1.0.0
     */
    void delPersonLevelToDevice(List<AccDeviceItem> devItemList, Map<String, Collection<String>> personInfoMap);

    /**
     * 根据人员ids和区域ids获取人员的权限组；
     *
     * @param authAreaIds:
     * @param personIds:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelItem>
     * @throws @date 2021-06-10 15:40
     * <AUTHOR>
     * @since 1.0.0
     */
    List<AccPersonLevelItem> getAccPersonLevelItemsByCondition(String authAreaIds, String personIds);

    /**
     * 下发人员卡号到设备
     *
     * @param deviceId:设备id
     * @param accPersonInfoItem:人员信息
     * @return void
     * <AUTHOR>
     * @date 2022-01-25 18:13
     * @since 1.0.0
     */
    void setPersonCardToDevice(String deviceId, AccPersonInfoItem accPersonInfoItem);

    /**
     * 根据sessionId获取当前登录用户的id
     *
     * @param sessionId:
     * @return java.lang.String
     * @throws
     * <AUTHOR>
     * @date 2022-07-19 15:36
     * @since 1.0.0
     */
    String getUserIdBySessionId(String sessionId);

    /**
     * 根据条件获取要导出的权限组信息
     *
     * @param accLevelItem:
     * @param beginIndex:
     * @param endIndex:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccLevelItem>
     * @throws
     * <AUTHOR>
     * @date 2022-07-19 15:38
     * @since 1.0.0
     */
    List<AccLevelItem> getExportLevelItemList(AccLevelItem accLevelItem, int beginIndex, int endIndex);

    /**
     * 导入权限组信息
     *
     * @param itemList :
     * @param updateExistData :
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * @throws
     * <AUTHOR>
     * @date 2022-07-20 15:21
     * @since 1.0.0
     */
    ZKResultMsg importLevelData(List<AccLevelItem> itemList, boolean updateExistData);

    /**
     * 导入权限组门信息
     *
     * @param itemList:
     * @param updateExistData:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * @throws
     * <AUTHOR>
     * @date 2022-07-21 16:54
     * @since 1.0.0
     */
    ZKResultMsg importLevelDoorData(List<AccLevelDoorExportItem> itemList, boolean updateExistData);

    /**
     * 导入权限组人员信息
     *
     * @param itemList:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * @throws
     * <AUTHOR>
     * @date 2022-07-25 18:13
     * @since 1.0.0
     */
    ZKResultMsg importAccPersonData(List<AccPersonLevelByLevelExportItem> itemList);

    /**
     * 获取人员基础数据用于下发，不包含模板照片信息
     *
     * @param personIds
     * @return
     */
    AccPersonInfoItem getPersonBasicInfoByIds(List<String> personIds);

    /**
     * 验证当前用户登陆密码
     *
     * @param sessionId:
     * @param loginPwd:
     * @return boolean
     * @throws
     * <AUTHOR>
     * @date 2023-10-09 17:33
     * @since 1.0.0
     */
    boolean verifyLoginPwd(String sessionId, String loginPwd);

    /**
     * 添加人员(包含时间)/门权限到数据库中，使用批量操作
     *
     * @param personIdAndTimes:
     * @param levelId:
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2024-01-05 14:33
     * @since 1.0.0
     */
    void addPersonAndTimesByParamIds(String personIdAndTimes, String levelId);

    /**
     * 添加人员/门权限(实体)到数据库中，使用批量操作
     *
     * @param newLevelTimes:
     * @param type:
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2024-01-05 15:44
     * @since 1.0.0
     */
    void addLevelByParamIds(List<AccPersonTimeLevelItem> newLevelTimes, String type);

    /**
     * 权限组组装人员权限相关命令下发给设备
     *
     * @param levelId:
     * @param personIdTimes:
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2024-01-05 14:34
     * @since 1.0.0
     */
    void setPersonTimesLevelToDev(String levelId, String personIdTimes);

    /**
     * 添加人员/门权限(包含时间)到数据库中，使用批量操作
     *
     * @param personId:
     * @param levelAndTimes:
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2024-01-06 10:21
     * @since 1.0.0
     */
    void addLevelAndTimesByParamIds(String personId, String levelAndTimes);

    /**
     * 权限组组装人员权限相关命令下发给设备
     *
     * @param levelIdTimes:
     * @param personId:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-01-06 10:23
     * @since 1.0.0
     */
    void setPersonLevelTimesToDev(String levelIdTimes, String personId);

    /**
     * 人员编辑页面-添加人员权限-选权限组双列表
     *
     * @param condition:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccLevelItem>
     * <AUTHOR>
     * @throws
     * @date 2024-01-06 11:35
     * @since 1.0.0
     */
    List<AccLevelItem> getPersonShowLevelItem(AccPersonShowLevelItem condition);

    /**
     * 判断当前用户是否有默认权限组的区域权限
     *
     * @param sessionId:
     * @return com.zkteco.zkbiosecurity.acc.vo.AccLevelItem
     * <AUTHOR>
     * @throws
     * @date 2025-05-26 17:11
     * @since 1.0.0
     */
    AccLevelItem getMasterLevelBySessionId(String sessionId);
}