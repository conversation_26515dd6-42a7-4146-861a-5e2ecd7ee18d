package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;

/**
 * @Auther: lambert.li
 * @Date: 2018/11/17 17:09
 */
public interface AccApiThirdPartyService {

    /**
     * 对接大掌柜SFAS;IPC绑定的读头远程开门;实时监控产生虚拟事件;门禁当考勤
     * @auther lambert.li
     * @date 2018/11/15 8:34
     * @param
     * @return
     */
    ApiResultMessage openDoorAndCreateEvent(String pin, String readerId);
}
