/**
 * File Name: AccDoor Created by GenerationTools on 2018-03-03 上午11:59 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 对应百傲瑞达 AccDoorService
 * 
 * <AUTHOR>
 * @date: 2018-03-03 上午11:59
 * @version v1.0
 */
public interface AccDoorService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    AccDoorItem saveItem(AccDoorItem item, String applyTo);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AccDoorItem> getByCondition(AccDoorItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    public AccDoorItem getItemById(String id);

    Pager getSelectDoorItemsByPage(AccMapSelectDoorItem condition, int pageNo, int pageSize);

    Map<String, String> getDoorIds(String[] idArr, String type);

    /**
     *
     * 判断设备是否为新一体机
     *
     * <AUTHOR>
     * @since 2018年04月24日 下午3:59:03
     * @param deviceId
     * @return
     */
    boolean isNewAccessControlDevice(String deviceId);

    /**
     *
     * 获取设备支持的验证方式
     *
     * <AUTHOR>
     * @since 2018年04月25日 10:59:03
     * @param deviceId
     * @return
     */
    public List<SelectItem> getVerifyMode(String deviceId);

    /**
     *
     * 韦根格式
     *
     * <AUTHOR>
     * @since 2018年04月25日 10:59:03
     * @return
     */
    public List<SelectItem> getWiegandFmtList();

    /**
     * 验证设备关联的读头是否有参与全局反潜
     *
     * <AUTHOR>
     * @since 2018年4月25日 11:34:05
     * @return
     */
    public boolean validGlobalApb(String doorId);

    /**
     * 判断名称是否存在
     * 
     * <AUTHOR>
     * @since 2018年4月25日 11:34:05
     * @param name
     * @return
     */
    public boolean isExist(String name);

    /**
     * 判断密码是否合法
     * 
     * <AUTHOR>
     * @since 2018年4月25日 11:34:05
     * @param forcePwd
     * @return
     */
    public boolean checkPwd(String forcePwd);

    /**
     * 获取设备参数
     * 
     * <AUTHOR>
     * @since 2018年4月25日 11:34:05
     * @param deviceId
     * @return
     */
    public Map<String, Object> getAccDeviceOpt(String deviceId);

    /**
     * 根据门编号和设备ID获取item
     * 
     * <AUTHOR>
     * @since 2018年4月26日 11:34:05
     * @param doorNo
     * @param deviceId
     * @return
     */
    AccDoorItem getByDoorNoAndDeviceId(short doorNo, String deviceId);

    /**
     * 启用门
     * 
     * <AUTHOR>
     * @since 2018年4月27日 14:34:05
     * @param ids
     * @return
     */
    ZKResultMsg enable(String ids);

    /**
     * 禁用门
     * 
     * <AUTHOR>
     * @since 2018年4月27日 11:34:05
     * @param ids
     */
    void disable(String ids);

    /**
     * 获取导出数据
     * 
     * @author: verber
     * @date: 2018-05-03 11:30:27
     * @return
     */
    List<AccTransactionPersonDoorItem> getPersonDoorItemData(
        Class<AccTransactionPersonDoorItem> accTransactionPersonDoorItemClass, BaseItem condition, int beginIndex,
        int endIndex);

    /**
     * @Description: 获取导出数据
     * <AUTHOR>
     * @date 2018/5/14 10:35
     * @param accLevelDoorItem 查询对象
     * @param beginIndex 开始索引
     * @param endIndex 结束索引
     * @return
     */
    List<AccLevelDoorItem> getExportItemList(AccLevelDoorItem accLevelDoorItem, int beginIndex, int endIndex);

    /**
     * @Description: 根据门id和门磁类型获取门
     * <AUTHOR>
     * @date 2018/6/1 9:09
     * @param doorIdList
     * @return
     */
    List<AccDoorItem> getItemsByIdsAndSensorStatus(List<String> doorIdList);

    /**
     * @Description: 获取门状态，用于实时监控显示
     * <AUTHOR>
     * @date 2018/6/4 20:16
     * @param loadText
     * @return
     */
    ZKResultMsg getDoorStatus(String sessionId, String loadText);

    /**
     * 获取门下读头绑定的视频通道ID
     *
     * @param queryDoorItem:
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023-02-23 9:24
     * @since 1.0.0
     */
    String getDoorBindChannelIds(AccQueryDoorItem queryDoorItem);

    /**
     * 根据门ids获取门列表
     * 
     * @author: mingfa.zheng
     * @date: 2018/6/13 9:44
     * @return:
     */
    List<AccDoorItem> getItemsByIds(Collection<String> doorIdList);

    /**
     * 过滤门数据获取权限，根据权限组归属区域去过滤
     * 
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager getPagerFilterAuth(String sessionId, AccLevelSelectDoorItem condition, int pageNo, int pageSize);

    /**
     * @Description: 根据权限过滤显示门
     * <AUTHOR>
     * @date 2018/6/20 11:13
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager loadPagerByAuthFilter(String sessionId, AccDoorItem condition, int pageNo, int pageSize);

    /**
     * 查询设置为控制器的韦根读头的一体机的门id
     * 
     * @author: mingfa.zheng
     * @date: 2018/6/27 9:44
     * @return:
     */
    List<String> getDoorIdAsReader();

    /**
     * @Description: 获取权限组门列表
     *
     * @author: mingfa.zheng
     * @date: 2018/7/26 16:53
     * @param: [sessionId, condition, page, size]
     * @return: com.zkteco.zkbiosecurity.base.bean.Pager
     **/
    Pager accLevelDoorList(String sessionId, AccLevelDoorItem condition, int page, int size);

    /**
     * @Description: 根据门ids获取绑定的一体机当读头的门ids（一体机当读头方案中使用）
     *
     * @author: mingfa.zheng
     * @date: 2018/8/8 13:59
     * @param: []
     * @return: java.util.List<java.lang.String>
     **/
    List<String> getDoorIdsAsWGReaderByDoorId(List<String> doorIdList);

    /**
     * @Description: 查询某权限组中要操作的门绑定的读头一体机的门id
     *
     * @author: mingfa.zheng
     * @date: 2018/8/8 15:46
     * @param: [doorIdList, levelId]
     * @return: java.util.List<java.lang.String>
     **/
    List<String> getDoorIdsAsWGReaderByDoorIdAndLevelId(List<String> doorIdList, String levelId);

    /**
     * @Description: 根据门ids获取设备以及主设备的ids
     *
     * @author: mingfa.zheng
     * @date: 2018/8/8 14:48
     * @param: [doorIdList]
     * @return: java.util.List<java.lang.String>
     **/
    List<String> getDevAndParentDevIdsByDoorIds(List<String> doorIdList);

    /**
     * @Description: 根据门ids获取设备的ids
     *
     * @author: mingfa.zheng
     * @date: 2018/8/8 14:48
     * @param: [doorIdList]
     * @return: java.util.List<java.lang.String>
     **/
    List<String> getDevIdsByDoorIds(List<String> doorIdList);

    /**
     * @Description: 根据设备id获取门id
     * <AUTHOR>
     * @date 2018/8/13 14:09
     * @param devId
     * @return
     */
    List<String> getIdByDevId(String devId);

    /**
     * @Description: 根据设备id获取门
     * <AUTHOR>
     * @date 2018/8/22 17:56
     * @param devIdList
     * @return
     */
    List<AccDoorItem> getItemsByDevIds(List<String> devIdList);

    /**
     * @Description: 云服务调用远程开门接口
     * <AUTHOR>
     * @date 2018/9/5 11:22
     * @param doorMap
     * @param openInterval
     * @return
     */
    void cloudOpenDoor(Map<String, List<Integer>> doorMap, String openInterval);

    Long getDoorCount();

    List<AccDoorItem> getUploadCloudDoor(int pageNo, int pageSize);

    List<String> getUploadCloudPersonLevel(AccDoorItem accDoorItem);

    /**
     * 根据门名称获取门信息
     * 
     * @auther lambert.li
     * @date 2018/11/13 10:16
     * @param doorName
     * @return
     */
    AccDoorItem getItemByName(String doorName);

    /**
     * 根据设备id修改该设备底下所有门的主机出入状态
     *
     * <AUTHOR>
     * @since 2018年12月5日 下午5:45:37
     * @param devId
     * @param devIOState
     */
    void setDoorHostStatus(String devId, String devIOState);

    /**
     * 门数据迁移
     * 
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午10:20:15
     * @param items
     */
    void handlerTransfer(List<AccDoorItem> items);

    /**
     * 根据通信方式查询门id
     *
     * @param commType
     * @return
     */
    List<String> getDoorIdByCommType(List<Short> commType);

    /**
     * 根据区域id获取门
     *
     * @param areaIds
     * @return
     */
    List<AccDoorItem> getAccDoorByAreaIds(Collection<String> areaIds);

    /**
     * 根据设备序列号和门编号获取门
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/1/29 10:54
     * @param deviceSn
     * @param doorNo
     * @return com.zkteco.zkbiosecurity.acc.vo.AccDoorItem
     */
    AccDoorItem getDoorByDevSnAndDoorNo(String deviceSn, Short doorNo);

    /**
     *
     * @param doorMap
     */
    void cloudCancelAlarm(Map<String, List<Integer>> doorMap);

    /**
     * 根据设备SN和门编号获取门ids
     * 
     * @auther zbx.zhong
     * @date 2019/6/19 16:06
     * @param sn
     * @param doorNos
     * @return
     */
    String getDoorByDevSnAndDoorNos(String sn, List<Short> doorNos);

    /**
     * 置顶门
     * 
     * @auther zbx.zhong
     * @date 2019/6/19 16:06
     * @param operator
     * @param doorId
     * @return
     */
    ZKResultMsg topDoor(String operator, String doorId);

    /**
     * 取消置顶
     *
     * @auther zbx.zhong
     * @date 2019/6/19 16:06
     * @param operator
     * @param doorId
     * @return
     */
    ZKResultMsg cancleTopDoor(String operator, String doorId);

    /**
     * 获取门禁所有门数据
     *
     * <AUTHOR>
     * @since 2019-10-10 19:32
     * @Param []
     * @return
     */
    List<AccDoorItem> getAccDoorItems();

    /**
     * 从二号项目迁移代码：远程门操作（增加时间段判断是否能够操作门）
     * 
     * <AUTHOR>
     * @date 2019/11/11 14:07
     * @param opType
     * @param openInterval
     * @param doorIds
     * @param operator
     * @param isAdmin
     * @return
     */
    Map<String, String> operateDoorByTimeSeg(String opType, String openInterval, String doorIds, String operator,
        boolean isAdmin);

    /**
     * 从二号项目迁移代码：门操作
     * 
     * <AUTHOR>
     * @date 2019/11/11 15:08
     * @param opType
     * @param openInterval
     * @param doorIds
     * @param operator
     * @param isAdmin
     * @return
     */
    ZKResultMsg operateDoor(String opType, String openInterval, String doorIds, String operator, boolean isAdmin);

    /**
     * 保存Item实体，无复杂业务逻辑
     *
     * @param item:门信息
     * @return com.zkteco.zkbiosecurity.acc.vo.AccDoorItem
     * <AUTHOR>
     * @date 2021-01-25 15:42
     * @since 1.0.0
     */
    AccDoorItem saveSimpleItem(AccDoorItem item);

    /**
     * 获取其他门信息
     *
     * @param deviceId:设备id
     * @param doorId:门id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDoorItem>
     * <AUTHOR>
     * @date 2021-01-25 15:55
     * @since 1.0.0
     */
    List<AccDoorItem> getOtherDoorByDevId(String deviceId, String doorId);

    /**
     * 根据门id和设备id集合查询门信息
     *
     * @param doorId:门id
     * @param devIdList:设备id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDoorItem>
     * <AUTHOR>
     * @date 2021-01-25 16:23
     * @since 1.0.0
     */
    List<AccDoorItem> getDoorByDoorIdAndDevIds(String doorId, List<String> devIdList);

    /**
     * 根据门id和设备类型查询门信息
     *
     * @param doorId:门ID
     * @param machineType:设备类型
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDoorItem>
     * <AUTHOR>
     * @date 2021-01-25 16:26
     * @since 1.0.0
     */
    List<AccDoorItem> getOtherDoorByMacType(String doorId, Short machineType);

    /**
     * 分页查询，无复杂业务逻辑
     *
     * @param condition:查询条件
     * @param page:页索引，从0开始
     * @param size:每页几条
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-01-27 17:22
     * @since 1.0.0
     */
    Pager getSimpleItemsByPage(BaseItem condition, int page, int size);

    /**
     * 根据门ID集合获取门map <ID,AccDoorItem>
     *
     * @param doorIds:门id集合
     * @return java.util.Map<java.lang.String,com.zkteco.zkbiosecurity.acc.vo.AccDoorItem>
     * <AUTHOR>
     * @date 2021-01-28 9:14
     * @since 1.0.0
     */
    Map<String, AccDoorItem> getItemsMapByDoorIds(List<String> doorIds);

    /**
     * 根据有效时间段id统计数量
     *
     * @param activeTimeSegId:有效时间段id
     * @return boolean
     * <AUTHOR>
     * @date 2021-02-03 10:02
     * @since 1.0.0
     */
    int countByActiveTimeSegId(String activeTimeSegId);

    /**
     * 根据门常开时间段id统计数量
     *
     * @param passModeTimeSegId:常开时间段id
     * @return int
     * <AUTHOR>
     * @date 2021-02-03 10:03
     * @since 1.0.0
     */
    int countByPassModeTimeSegId(String passModeTimeSegId);

    /**
     * 根据出门按钮时间段id统计数量
     *
     * @param latchTimeSegId:出门按钮时间段id
     * @return int
     * <AUTHOR>
     * @date 2021-02-03 10:04
     * @since 1.0.0
     */
    int countByLatchTimeSegId(String latchTimeSegId);

    /**
     * 根据设备序列号获取门信息
     *
     * @param devSn:设备序列号
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDoorItem>
     * <AUTHOR>
     * @date 2021-02-04 11:18
     * @since 1.0.0
     */
    List<AccDoorItem> getItemsByDevSn(String devSn);

    /**
     * 获取门类简写名称
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021-02-07 14:01
     * @since 1.0.0
     */
    String getAccdoorSimpleName();

    /**
     * 导出权限组门信息
     * 
     * @param accLevelDoorItem:
     * @param beginIndex:
     * @param endIndex:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccLevelDoorItem>
     * <AUTHOR>
     * @date 2022-07-20 11:34
     * @since 1.0.0
     */
    List<AccLevelDoorExportItem> getExportLevelDoorItemList(AccLevelDoorExportItem accLevelDoorItem, int beginIndex,
        int endIndex);

    /**
     * 根据实时状态判断门是否在线
     *
     * @param devSn
     * @param doorNo
     * @return
     */
    boolean doorIsOnline(String devSn, Short doorNo);
}