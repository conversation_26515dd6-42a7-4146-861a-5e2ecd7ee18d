/**
 * File Name: AccMap
 * Created by GenerationTools on 2018-03-20 下午02:07
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccMapItem;
import com.zkteco.zkbiosecurity.acc.vo.AccMapPosItem;
import com.zkteco.zkbiosecurity.acc.vo.AccMapSelectChannelItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 对应百傲瑞达 BaseMapService
 * <AUTHOR>
 * @date:	2018-03-20 下午02:07
 * @version v1.0
 */
public interface AccMapService extends BaseService {

	/**
	 * 保存item实体，一般会有复杂业务逻辑
	 * @param item
	 */
	AccMapItem saveItem(AccMapItem item);

	/**
	 * 根据条件查询
	 * @param condition
	 * @return
	 */
	List<AccMapItem> getByCondition(AccMapItem condition);

	/**
	 * 根据ID查询
	 * @param id
	 * @return
	 */
	public AccMapItem getItemById(String id);
	
	/**
	 * 获取区域根节点ID
	 * <AUTHOR> 
	 * @date:  2018-03-20 下午02:07
	 * @param sessionId
	 * @return
	 */
	public List<TreeItem> createMapTree(String sessionId);
	
	/**
	 * 保存地图位置
	 * <AUTHOR> 
	 * @date:  2018-03-22 17:07
	 * @return
	 */
	public void saveMapPos(String mapId, Double width, Double height, String posArray);
	
	/**
	 * 保存添加的实体（门或摄像头）
	 * <AUTHOR> 
	 * @date:  2018-03-26 11:07
	 * @return
	 */
	public void addEntity(String mapId, Double width, String entityType, String entityIds, String logMethod);
	
	/**
	 * 判断是否存在
	 * @param name
	 * @return
	 */
	public boolean isExist(String name);

	/**
	 * 判断是否存在视频模块
	 * @return
	 */
    public boolean isExistVid();

	/**
	 * 判断是否存在VMS模块
	 * @return
	 */
    boolean isExistVms();

    /**
	 * 判断是否存在视频设备
	 * @return
	 */
	public boolean isExistVidDevice();

    public Pager getSelectChannelItemsByPage(String sessionId, AccMapSelectChannelItem condition, int pageNo, int pageSize);

    /**
     * 根据id获取vms模块通道信息
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/29 16:20
     * @param channelId 通道id
     * @return
     */
	Map<String, String> getVmsChannelById(String channelId);

	/**
	 * 根据地图ID获取地图元素信息
	 *
	 * @param mapId:地图ID
	 * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccMapPosItem>
	 * <AUTHOR>
	 * @date 2023-02-23 14:58
	 * @since 1.0.0
	 */
    List<AccMapPosItem> getMapPosList(String mapId);
}