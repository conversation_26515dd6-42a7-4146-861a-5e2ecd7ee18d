/**
 * File Name: AccDSTime Created by GenerationTools on 2018-02-28 下午02:21 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;

import com.zkteco.zkbiosecurity.acc.vo.AccDSTimeItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;

/**
 * 门禁夏令时service接口
 * 
 * <AUTHOR>
 * @date: 2018-02-28 下午02:21
 * @version v1.0
 */
public interface AccDSTimeService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    AccDSTimeItem saveItem(AccDSTimeItem item);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AccDSTimeItem> getByCondition(AccDSTimeItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    public AccDSTimeItem getItemById(String id);

    /**
     * 初始化数据
     */
    AccDSTimeItem initData(AccDSTimeItem item);

    /**
     * @Description: 判断夏令时名称是否存在
     * <AUTHOR>
     * @date 2018/5/18 17:04
     * @param name 夏令时名称
     * @return
     */
    boolean isExistName(String name);

    /**
     * 夏令时初始化数据已经不是一个了 add by colin.cheng 2021-12-17 14:27:09
     * 
     * @Description: 获取初始化夏令时
     * <AUTHOR>
     * @date 2018/5/21 10:54
     * @return
     */
    @Deprecated
    AccDSTimeItem getItemByInitFlag();

    /**
     * @Description: 设置夏令时
     * <AUTHOR>
     * @date 2018/7/6 15:35
     * @param dSTimeId
     * @param devId
     * @return
     */
    String setDSTime(String dSTimeId, String devId);

    /**
     * 
     * @Description: 夏令时数据迁移
     * <AUTHOR>
     * @since 2018年12月12日 下午4:44:37
     * @param items
     */
    void handlerTransfer(List<AccDSTimeItem> items);

    /**
     * 时区下是否有夏令时
     * 
     * @param timeZone
     * @return
     */
    Object isExistByTimeZone(String timeZone);

    /**
     * 获取当前系统的默认时区
     * 
     * @return
     */
    String getDefaultTimeZone();
}