/**
 * File Name: AccCombOpenDoor
 * Created by GenerationTools on 2018-03-14 下午03:02
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccCombOpenCombItem;
import com.zkteco.zkbiosecurity.acc.vo.AccCombOpenDoorItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.util.List;

/**
 * 对应百傲瑞达 AccCombOpenDoorService
 * <AUTHOR>
 * @date:	2018-03-14 下午03:02
 * @version v1.0
 */
public interface AccCombOpenDoorService extends BaseService {

	/**
	 * 保存item实体，一般会有复杂业务逻辑
	 * @param item
	 */
	AccCombOpenDoorItem saveItem(AccCombOpenDoorItem item, String[] groupIds, String[] sorts, String[] openerNumbers);

	/**
	 * 根据条件查询
	 * @param condition
	 * @return
	 */
	List<AccCombOpenDoorItem> getByCondition(AccCombOpenDoorItem condition);

	/**
	 * 根据ID查询
	 * @param id
	 * @return
	 */
	AccCombOpenDoorItem getItemById(String id);

	/**
	 * 获取多人开门组
	 * @author: mingfa.zheng
	 * @date: 2018/3/16 10:45
	 * @return:
	 */
	List<AccCombOpenCombItem> getAccCombOpenCombList(String id);

	/**
	 * 判断名称是否有效/允许
	 * @author: mingfa.zheng
	 * @date: 2018/3/15 16:09
	 * @return:
	 */
	AccCombOpenDoorItem getItemByName(String name);

	/**
	 *
	 * 验证门关联的设备是否启用后台验证
	 *
	 * <AUTHOR> href="<EMAIL>">linzj</a>
	 * @since 2015年6月10日 下午4:02:43
	 * @param doorId
	 * @return
	 */
	boolean validBackgroundVerify(String doorId);

	/**
	 * 用于显示多卡人员组中的人员数，便于在列表中显示
	 * @author: mingfa.zheng
	 * @date:  15:49
	 * @return:
	 */
	String getCombOpenComb(String id);

	/**
	 * 用于显示多人开门人员组中的人员数，便于在列表中显示
	 * @author: mingfa.zheng
	 * @date: 2018/5/24 21:26
	 * @return: 
	 */
	Integer getCombOpenPersonByGroup(String id);
	
	/**    
	 * @Description: 根据门id获取多人开门组
	 * <AUTHOR>  
	 * @date 2018/5/31 15:52  
	 * @param doorIdList 门id
	 * @return   
	 */  
    List<AccCombOpenDoorItem> getItemsByDoorIds(List<String> doorIdList);

	/**
	 * 多人开门数据迁移
	 *
	 * @param accCombOpenDoorItems
	 */
	void handlerTransfer(List<AccCombOpenDoorItem> accCombOpenDoorItems);
}