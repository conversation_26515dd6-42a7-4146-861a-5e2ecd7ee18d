package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;

import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTriggerGroupItem;

/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2024/3/27 18:21
 * @since 1.0.0
 */
public interface AccTriggerGroupService {
    String updateTriggerGroup(String address, String oldGroupId, Short type);

    List<AccDeviceItem> setDelTriggerGroupToDev(AccTriggerGroupItem accTriggerGroup, List<String> targetSnList,
        Boolean... isP2P);

    AccTriggerGroupItem getItemById(String id);

    List<AccDeviceItem> getDevByTriggerGroupAddrAndType(List<String> addrIds, Short type);
}
