package com.zkteco.zkbiosecurity.acc.service;

/**
 * 门禁信息自动导出
 * 
 * <AUTHOR>
 * @date Created In 17:53 2019/11/7
 */
public interface AccTransactionAutoExportService {
    /**
     * 自动读取配置参数，并返回cron表达式
     * 
     * @return
     */
    String createCron();

    /**
     * 自动获取配置参数并修改定时任务
     */
    void changeAutoProcessorTime();

    /**
     * 根据表达式修改定时任务时间
     * 
     * @param cron
     */
    void changeAutoProcessorTime(String cron);

    /**
     * 初始化定时任务
     */
    void initAutoProcessorTime();
}
