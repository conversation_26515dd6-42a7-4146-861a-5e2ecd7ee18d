/**
 * File Name: AccReaderOption Created by GenerationTools on 2018-03-13 上午10:06 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccReaderOptionItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.util.List;

/**
 * 对应百傲瑞达 AccReaderOptionService
 * 
 * <AUTHOR>
 * @date: 2018-03-13 上午10:06
 * @version v1.0
 */
public interface AccReaderOptionService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    AccReaderOptionItem saveItem(AccReaderOptionItem item);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AccReaderOptionItem> getByCondition(AccReaderOptionItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    public AccReaderOptionItem getItemById(String id);

    AccReaderOptionItem getByOptName(String readerId, String name);

    /**
     * 保存Item,无复杂的业务逻辑
     *
     * @param options:读头参数信息
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccReaderOptionItem>
     * <AUTHOR>
     * @date 2021-01-28 9:58
     * @since 1.0.0
     */
    List<AccReaderOptionItem> saveSimpleItem(List<AccReaderOptionItem> options);

    /**
     * 根据读头id获取读头参数信息
     *
     * @param readerId:读头id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccReaderOptionItem>
     * <AUTHOR>
     * @date 2021-01-28 18:39
     * @since 1.0.0
     */
    List<AccReaderOptionItem> getItemsByReaderId(String readerId);

    /**
     * 根据读头id和参数类型查询参数信息
     *
     * @param readerId:读头id
     * @param optType:参数类型
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccReaderOptionItem>
     * <AUTHOR>
     * @date 2021-01-29 9:50
     * @since 1.0.0
     */
    List<AccReaderOptionItem> getItemsByReaderIdAndOptType(String readerId, Short optType);
}