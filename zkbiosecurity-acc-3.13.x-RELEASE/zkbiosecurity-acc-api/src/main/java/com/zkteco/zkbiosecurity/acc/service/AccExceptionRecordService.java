package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;

import com.zkteco.zkbiosecurity.acc.vo.AccExceptionRecordItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * 异常记录服务接口
 * 
 * @author: AI Assistant
 * @date: 2025-07-21
 */
public interface AccExceptionRecordService extends BaseService {

    /**
     * 保存异常记录实体，一般会有复杂业务逻辑
     * 
     * @param item
     * @return
     */
    AccExceptionRecordItem saveItem(AccExceptionRecordItem item);

    /**
     * 根据条件查询异常记录
     * 
     * @param condition
     * @return
     */
    List<AccExceptionRecordItem> getByCondition(AccExceptionRecordItem condition);

    /**
     * 根据ID查询异常记录
     * 
     * @param id
     * @return
     */
    AccExceptionRecordItem getItemById(String id);

    /**
     * 分页查询异常记录（带权限过滤）
     * 
     * @param sessionId 会话ID
     * @param condition 查询条件
     * @param pageNo 页码
     * @param pageSize 页大小
     * @param limitCount 限制数量
     * @return
     */
    Pager loadExceptionRecordByAuthUserFilter(String sessionId, AccExceptionRecordItem condition, 
                                              int pageNo, int pageSize,
                                              long limitCount);

    /**
     * 获取导出数据
     * 
     * @param targetClass 目标类
     * @param condition 查询条件
     * @param beginIndex 开始索引
     * @param endIndex 结束索引
     * @return
     */
    List<?> getItemData(Class targetClass, BaseItem condition, int beginIndex, int endIndex);

    /**
     * 根据会话ID获取区域名称
     * 
     * @param sessionId
     * @return
     */
    String getAreaNamesBySessionId(String sessionId);

    /**
     * 根据会话ID获取部门编码
     * 
     * @param sessionId
     * @return
     */
    String getDeptCodesBySessionId(String sessionId);

    /**
     * 创建异常记录
     * 
     * @param pin 工号
     * @param name 姓名
     * @param deptName 部门名称
     * @param receiverPosition 接收人员职位
     * @param readerName 读头名称
     * @param enterTime 进入时间
     * @param exitTime 外出时间
     * @param subject 主题（异常进出、迟到）
     * @param exceptionStatus 异常状态（未闭环、已返回）
     * @return
     */
    AccExceptionRecordItem createExceptionRecord(String pin, String name, String deptName, 
                                                String receiverPosition, String readerName, 
                                                java.util.Date enterTime, java.util.Date exitTime, 
                                                String subject, String exceptionStatus);

    /**
     * 发送异常记录通知
     * 
     * @param item 异常记录
     * @return 是否发送成功
     */
    boolean sendExceptionNotification(AccExceptionRecordItem item);

    /**
     * 更新异常记录状态
     * 
     * @param id 记录ID
     * @param exceptionStatus 异常状态
     * @return
     */
    AccExceptionRecordItem updateExceptionStatus(String id, String exceptionStatus);


    /**
     * 清除所有异常记录
     */
    void deleteAllData();

    /**
     * 重新发送失败的异常记录
     * 
     * @param ids ID列表，逗号分隔
     * @return
     */
    int resendFailedRecords(String ids);



}
