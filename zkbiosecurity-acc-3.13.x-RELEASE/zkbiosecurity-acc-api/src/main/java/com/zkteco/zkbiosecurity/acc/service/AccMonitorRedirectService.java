package com.zkteco.zkbiosecurity.acc.service;/*
 * File Name: AccRTMonitorRedirectService
 * <NAME_EMAIL> on 2018/6/1 15:46.
 * Copyright:Copyright © 1999-2018 ZKTeco Inc.All right reserved.
 */

import com.zkteco.zkbiosecurity.acc.vo.AccDeviceEventItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceMonitorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.Map;

public interface AccMonitorRedirectService {

    /**
     * @Description: 推送实时事件到实时监控页面
     * <AUTHOR>
     * @date 2018/6/5 10:27
     * @param accTransactionItem
     * @param userData
     * @param tempDevEvent
     * @return
     */
    void sendAccTransaction2RTMonitor(AccTransactionItem accTransactionItem, Map<String, String> userData, AccDeviceEventItem tempDevEvent);

    /**
     * @Description: 推送设备状态到实时监控页面
     * <AUTHOR>
     * @date 2018/6/5 10:28
     * @param resultMsg
     * @return
     */
    void sendDeviceDoorState2RTMonitor(ZKResultMsg resultMsg);

    /**
     * @Description: 推送实时设备监控信息到设备监控页面
     * <AUTHOR>
     * @date 2018/6/7 20:23
     * @param accDeviceMonitorItem
     * @return
     */
    void sendDeviceMonitor(AccDeviceMonitorItem accDeviceMonitorItem);

    /**
     * 发送数据到LCD实时监控页面
     * <AUTHOR>
     * @since 2018年11月29日 下午3:26:10
     * @param accTransaction
     * @param photoPath
     */
	void sendDataToLcdRtMonitoring(AccTransactionItem accTransaction, String photoPath);
}
