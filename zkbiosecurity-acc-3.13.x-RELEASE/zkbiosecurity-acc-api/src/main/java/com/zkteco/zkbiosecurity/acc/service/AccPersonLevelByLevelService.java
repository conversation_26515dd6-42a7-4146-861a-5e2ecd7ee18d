/**
 * File Name: AccLevel
 * Created by GenerationTools on 2018-03-02 下午02:15
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelByLevelItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonListItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.List;

/**
 * 对应百傲瑞达 AccLevelService
 * <AUTHOR>
 * @date:	2018-03-02 下午02:15
 * @version v1.0
 */
public interface AccPersonLevelByLevelService extends BaseService {

	/**
	 * 根据条件查询
	 * @param condition
	 * @return
	 */
	List<AccPersonLevelByLevelItem> getByCondition(AccPersonLevelByLevelItem condition);

	/**
	 * 根据ID查询
	 * @param id
	 * @return
	 */
	AccPersonLevelByLevelItem getItemById(String id);

    /**
     * 权限组删除人员
     * <AUTHOR>
     * @since 2018/3/22 18:33
     * @return
     */
    ZKResultMsg delPerson(String levelIds, String personIds);

    /**
     * @Description: 根据权限过滤显示权限组
     * <AUTHOR>
     * @date 2018/6/20 14:51
     * @param sessionId
     * @param codition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager loadPagerByAuthFilter(String sessionId, AccPersonLevelByLevelItem codition, int pageNo, int pageSize);

	/**
	 * @Description: 获取导出数据
	 * <AUTHOR>
	 * @date 2018/6/27 10:35
	 * @param accPersonListItem 查询对象
	 * @param beginIndex 开始索引
	 * @param endIndex 结束索引
	 * @return
	 */
	List<AccPersonListItem> getExportItemList(String sessionId, AccPersonListItem accPersonListItem, int beginIndex, int endIndex);

	ZKResultMsg getPersonCount(String sessionId, String levelId);
}