/**
 * File Name: AccInterlock Created by GenerationTools on 2018-03-13 上午09:53 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.acc.vo.AccInterlockItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;

/**
 * 对应百傲瑞达 AccInterlockService
 * 
 * <AUTHOR>
 * @date: 2018-03-13 上午09:53
 * @version v1.0
 */
public interface AccInterlockService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    AccInterlockItem saveItem(AccInterlockItem item);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AccInterlockItem> getByCondition(AccInterlockItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    AccInterlockItem getItemById(String id);

    /**
     * 获取已设置互锁的设备id和不支持互锁的设备id
     *
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2015年6月8日 上午8:39:35
     * @return String
     */
    String getDevIdWithInterlock();

    /**
     *
     * 获取互锁模式
     *
     * <AUTHOR> href="<EMAIL>">linzj</a>
     * @since 2015年6月8日 下午3:34:39
     * @param deviceId
     * @return
     */
    Map<Integer, String> getRule(String deviceId);

    /**
     *
     * 根据互锁模式转换成相应的门名称组合显示在互锁列表页面中
     *
     * <AUTHOR> href="<EMAIL>">linzj</a>
     * @since 2015年5月26日 上午10:28:06
     * @param value
     * @return
     */
    String convertInterlockRule(String value);

    /**
     * @Description: 根据设备删除互锁
     * <AUTHOR>
     * @date 2018/5/30 11:49
     * @param devId 设备ID
     * @return
     */
    void delInterLockByDevId(String devId);

    /**
     * 互锁迁移
     *
     * @param accInterlockItems
     */
    void handlerTransfer(List<AccInterlockItem> accInterlockItems);

    /**
     * 根据设备id获取
     *
     * @param devId:设备id
     * @return void
     * <AUTHOR>
     * @date 2021-01-18 10:02
     * @since 1.0.0
     */
    List<AccInterlockItem> getItemsByDevId(String devId);

    /**
     * 根据用户权限过滤，获取互锁列表
     *
     * @param sessionId:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2022-10-14 14:15
     * @since 1.0.0
     */
    Pager loadPagerByAuthFilter(String sessionId, AccInterlockItem condition, int pageNo, int pageSize);

    boolean nameExists(String name);

    String getLockCountByDevice(String devId);

    AccInterlockItem completionItem(AccInterlockItem item);

    Boolean validDetermineInterlock(String deviceId);
}