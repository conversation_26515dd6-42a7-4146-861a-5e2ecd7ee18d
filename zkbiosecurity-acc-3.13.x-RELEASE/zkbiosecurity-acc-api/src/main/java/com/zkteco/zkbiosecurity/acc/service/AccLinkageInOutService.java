/**
 * File Name: AccLinkageInOut
 * Created by GenerationTools on 2018-03-16 下午04:41
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccLinkageInOutItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.util.List;

/**
 * 对应百傲瑞达 AccLinkageInOutService
 * <AUTHOR>
 * @date:	2018-03-16 下午04:41
 * @version v1.0
 */
public interface AccLinkageInOutService extends BaseService {

	/**
	 * 保存item实体，一般会有复杂业务逻辑
	 * @param item
	 */
	AccLinkageInOutItem saveItem(AccLinkageInOutItem item);

	/**
	 * 根据条件查询
	 * @param condition
	 * @return
	 */
	List<AccLinkageInOutItem> getByCondition(AccLinkageInOutItem condition);

	/**
	 * 根据ID查询
	 * @param id
	 * @return
	 */
	AccLinkageInOutItem getItemById(String id);

	/**
	 * 联动输出点迁移
	 *
	 * @param accLinkageInOutItems
	 */
	void handlerTransfer(List<AccLinkageInOutItem> accLinkageInOutItems);

	/**
	 * 根据联动id获取输入输出信息
	 *
	 * @param linkageId:联动id
	 * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccLinkageInOutItem>
	 * <AUTHOR>
	 * @date 2021-01-22 15:15
	 * @since 1.0.0
	 */
	List<AccLinkageInOutItem> getLinkageInOutItemsByLinkageId(String linkageId);
}