/**
 * File Name: AccFirstOpen Created by GenerationTools on 2018-03-14 下午03:02 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccFirstOpenItem;
import com.zkteco.zkbiosecurity.acc.vo.AccFirstOpenSelectDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonFirstOpenItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonOptItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.List;

/**
 * 对应百傲瑞达 AccFirstOpenService
 * 
 * <AUTHOR>
 * @date: 2018-03-14 下午03:02
 * @version v1.0
 */
public interface AccFirstOpenService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    AccFirstOpenItem saveItem(AccFirstOpenItem item);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AccFirstOpenItem> getByCondition(AccFirstOpenItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    AccFirstOpenItem getItemById(String id);

    /**
     * 获取已经设置首人开门的门id，用于前端过滤
     *
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2015年7月8日 下午5:16:06
     * @return
     */
    String getFilterDoorId();

    /**
     * 首人常开： 添加人员
     * 
     * @author: mingfa.zheng
     * @date: 2018/3/21 16:41
     * @return:
     */
    ZKResultMsg addPerson(String firstOpenId, List<String> personIdList);

    /**
     * 首人常开： 删除人员
     * 
     * @author: mingfa.zheng
     * @date: 2018/3/21 16:41
     * @return:
     */
    ZKResultMsg delPerson(String firstOpenId, String personIds);

    /**
     * 下发命令删除设备中的首人开门
     * 
     * @author: mingfa.zheng
     * @date: 2018/5/15 16:38
     * @return:
     */
    void delFirstOpenFromDev(List<AccPersonOptItem> personInfoList);

    /**
     * @Description: 根据门id集合获取首人开门
     * <AUTHOR>
     * @date 2018/5/31 15:32
     * @param doorIdList 门id集合
     * @return
     */
    List<AccFirstOpenItem> getItemsByDoorIds(List<String> doorIdList);

    /**
     * @Description: 获取首人常开人员数量
     *
     * @author: mingfa.zheng
     * @date: 2018/7/25 20:24
     * @param: [sessionId, firstOpenId]
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    ZKResultMsg getPersonCount(String sessionId, String firstOpenId);

    /**
     * 首人常开迁移
     *
     * @param accFirstOpenItems
     */
    void handlerTransfer(List<AccFirstOpenItem> accFirstOpenItems);

    /**
     * 检查时间段是否被使用
     * 
     * @param timeSegId
     * @return
     */
    boolean checkTimeSegUsed(String timeSegId);

    /**
     * 获取首人常开人员信息
     *
     * @param accFirstOpenId:首人常开id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccPersonFirstOpenItem>
     * <AUTHOR>
     * @date 2021-01-19 17:39
     * @since 1.0.0
     */
    List<AccPersonFirstOpenItem> getAccPersonFirstOpenItemById(String accFirstOpenId);

    /**
     * 根据sql查询对应的Class类型集合
     *
     * @param condition:查询条件
     * @return java.util.List<?>
     * <AUTHOR>
     * @date 2021-01-29 15:32
     * @since 1.0.0
     */
    List<?> getItemsData(Class<?> cls, BaseItem condition);

    /**
     * 获取首人常开人员id
     *
     * @param firstOpenId:首人常开id
     * @param personIds:人员id
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2021-01-29 15:57
     * @since 1.0.0
     */
    List<String> getPersonFirstOpenIdsByFirstOpenIdAndPersonIds(String firstOpenId, List<String> personIds);

    /**
     * 删除首人常开人员
     *
     * @param accPersonFirstOpenIds:首人常开人员id
     * @return void
     * <AUTHOR>
     * @date 2021-01-29 16:02
     * @since 1.0.0
     */
    void deleteAccPersonFirstOpen(List<String> accPersonFirstOpenIds);

    /**
     * 获取首人常开的人员数量
     *
     * @param firstOpenId:首人常开id
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2021-01-29 16:04
     * @since 1.0.0
     */
    Long countPersonByAccFirstOpenId(String firstOpenId);

    /**
     * 根据时间段id统计数量
     *
     * @param timeSegId:时间段id
     * @return boolean
     * <AUTHOR>
     * @date 2021-01-14 10:07
     * @since 1.0.0
     */
    int countByTimeSegId(String timeSegId);

    /**
     * 权限过滤获取首人常开
     *
     * @param sessionId:
     * @param condition:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2022-10-14 11:21
     * @since 1.0.0
     */
    Pager loadPagerByAuthFilter(String sessionId, AccFirstOpenItem condition, int pageNo, int pageSize);

    Pager loadFirstOpenSelectDoorByAuthFilter(String sessionId, AccFirstOpenSelectDoorItem condition, int pageNo,
        int pageSize);
}