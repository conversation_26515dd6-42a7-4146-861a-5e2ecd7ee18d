package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2019/1/16 14:12
 **/
public interface AccCloudService {

    /**
     * @Description 异步推送门禁消息到云端
     * <AUTHOR>
     * @Date 2019/1/16 14:14
     * @Param accTransactionItemList
     * @Return void
     */
    void asyncPushTransactionToCloud(List<AccTransactionItem> accTransactionItemList);

    /**
     * 从二号项目迁移代码：同步设备状态到云端
     * @auther zbx.zhong
     * @date 2019/6/19 14:38
     * @param sn
     * @return
     */
    void asyncDevStatusToCloud(String sn);

    /**
     * 删除云端设备数据
     * @auther lambert.li
     * @date 2019/8/16 18:41
     * @param devSnList
     * @return
     */
    void deleteCloudDevice(List<String> devSnList);

    /**
     * 判断是否激活当前模块许可，激活后才能推送数据到云端
     *
     * @return
     * <AUTHOR>
     * @date 2019/11/20 10:25
     */
    boolean isActiveLicense();

    /**
     * 推送门禁报警消息到微信小程序
     *
     * <AUTHOR>
     * @date 2021-10-14 10:41
     * @param accTransactionItemList
     * @since 1.0.0
     * @return void
     */
    void sendAlarmMsgToWxMiniPrograms(List<AccTransactionItem> accTransactionItemList);
}
