package com.zkteco.zkbiosecurity.acc.service;

import java.util.Map;

public interface AccParamService {

    /**
     * 保存参数
     */
    void saveItem(Map<String, String> params);

    /**
     * 根据条件查询
     * 
     * @return
     */
    Map<String, String> getPersParams();

    /**
     * 获取参数配置的值
     * 
     * @param paramName
     * @return
     */
    String getParamValByName(String paramName);

    /**
     * 获取门禁参数
     * 
     * <AUTHOR>
     * @since 2018/4/17 10:15
     * @return
     */
    Map<String, String> getAccParams();

    /**
     * 获取视频参数
     * 
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年6月29日 上午8:53:45
     * @return
     */
    Map<String, String> getVidParams();

    /**
     * 获取解密或模糊化处理的图片base64字符串
     *
     * @param photoPath:图片路径
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022-09-27 16:04
     * @since 1.0.0
     */
    String getAvatarBase64ByPath(String photoPath);

    /**
     * 获取解密或模糊化处理的图片base64字符串
     *
     * @param capturePhotoPath:抓拍图片路径
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022-09-27 16:47
     * @since 1.0.0
     */
    String getDecryptBase64ByCapturePhotoPath(String sessionId, String capturePhotoPath);
}
