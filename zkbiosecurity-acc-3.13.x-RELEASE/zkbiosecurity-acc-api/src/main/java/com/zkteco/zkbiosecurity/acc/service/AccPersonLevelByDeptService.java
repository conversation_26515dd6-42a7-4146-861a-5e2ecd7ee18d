/**
 * File Name: AccLevel
 * Created by GenerationTools on 2018-03-02 下午02:15
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccDeptLevelItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelByDeptItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 对应百傲瑞达 AccLevelService
 * <AUTHOR>
 * @date:	2018-03-02 下午02:15
 * @version v1.0
 */
public interface AccPersonLevelByDeptService extends BaseService {

	/**
	 * 部门添加权限组
	 * @return 
	 */
    void setDeptLinkLevel(String deptId, String levelIds);

    /**
     * 删除部门默认权限
     * @return 
     */
    void delLevel(String leftIds, String rightIds);

	/**
	 * 根据部门Id,获取该部门下的权限组（levelId, levelName）。
	 * @author: mingfa.zheng
	 * @date: 2018/5/15 16:47
	 * @return:
	 */
	Map<String, String> getLevelListByDept(String deptId);

	/**
	 * @Description: 根据登录用户权限过滤显示部门
	 * <AUTHOR>
	 * @date 2018/6/25 18:58
	 * @param sessionId
	 * @param codition
	 * @param pageNo
	 * @param pageSize
	 * @return
	 */
    Pager getPagerFilterAuth(String sessionId, AccPersonLevelByDeptItem codition, int pageNo, int pageSize);

    /**
     * 根据条件查询
     *
     * @param condition:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDeptLevelItem>
     * <AUTHOR>
     * @throws
     * @date 2025-05-26 18:56
     * @since 1.0.0
     */
    List<AccDeptLevelItem> getByCondition(AccDeptLevelItem condition);
}