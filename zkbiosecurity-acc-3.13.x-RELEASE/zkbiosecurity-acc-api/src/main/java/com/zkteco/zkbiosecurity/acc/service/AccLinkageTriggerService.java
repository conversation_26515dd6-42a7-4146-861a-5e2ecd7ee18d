/**
 * File Name: AccLinkageTrigger Created by GenerationTools on 2018-03-16 下午04:41 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccLinkageTriggerItem;
import com.zkteco.zkbiosecurity.acc.vo.AccLinkageTriggerItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.util.List;

/**
 * 对应百傲瑞达 AccLinkageTriggerService
 * 
 * <AUTHOR>
 * @date: 2018-03-16 下午04:41
 * @version v1.0
 */
public interface AccLinkageTriggerService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    AccLinkageTriggerItem saveItem(AccLinkageTriggerItem item);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AccLinkageTriggerItem> getByCondition(AccLinkageTriggerItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    AccLinkageTriggerItem getItemById(String id);

    /**
     * 通过联动id获取
     * 
     * @param accLinkageId
     * @return
     */
    List<AccLinkageTriggerItem> findByLinkageId(String accLinkageId);

    /**
     * 根据联动id和输入输出id获取触发信息
     *
     * @param linkageId:联动id
     * @param linkageInOutId:输入输出id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccLinkageTriggerItem>
     * <AUTHOR>
     * @date 2021-01-22 15:57
     * @since 1.0.0
     */
    List<AccLinkageTriggerItem> getItemsByLinkageIdAndLinkageInOutId(String linkageId, String linkageInOutId);
}