package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.app.vo.AccAppTopDoorByPersonItem;
import com.zkteco.zkbiosecurity.acc.vo.AccFirstOpenItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.util.List;

public interface AccAppTopDoorByPersonService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * @param item
     */
    AccAppTopDoorByPersonItem saveItem(AccAppTopDoorByPersonItem item);


    /**
     * 删除
     * @param item
     */
    AccAppTopDoorByPersonItem delete(AccAppTopDoorByPersonItem item);

    /**
     * 根据条件查询
     * @param condition
     * @return
     */
    List<AccAppTopDoorByPersonItem> getByCondition(AccAppTopDoorByPersonItem condition);
}
