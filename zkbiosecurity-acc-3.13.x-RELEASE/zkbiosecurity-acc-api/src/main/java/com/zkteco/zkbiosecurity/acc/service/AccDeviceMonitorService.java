package com.zkteco.zkbiosecurity.acc.service;
/*
 * File Name: AccDeviceMonitorService
 * <NAME_EMAIL> on 2018/6/7 11:47.
 * Copyright:Copyright © 1999-2018 ZKTeco Inc.All right reserved.
 */

import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceMonitorItem;

import java.util.List;

public interface AccDeviceMonitorService {

    /**
     * @Description: 获取监控设备信息
     * <AUTHOR>
     * @date 2018/5/15 11:23
     * @return
     */
    List<AccDeviceMonitorItem> getDeviceMonitor(String filterAreaIds);

    /**
     * @Description: 更新设备监控页面设备数据
     * <AUTHOR>
     * @date 2018/6/7 11:56
     * @param sn
     * @return
     */
    void updateAccDeviceRunState(String sn);

    /**
     * @Description: 导出过滤设备数据获取
     * <AUTHOR>
     * @date 2018/6/7 19:28
     * @param accDeviceItem
     * @param beginIndex
     * @param exportCount
     * @param devStatus
     * @return
     */
    List<AccDeviceMonitorItem> filterDeviceState(AccDeviceItem accDeviceItem, int beginIndex, int exportCount, String devStatus);
}
