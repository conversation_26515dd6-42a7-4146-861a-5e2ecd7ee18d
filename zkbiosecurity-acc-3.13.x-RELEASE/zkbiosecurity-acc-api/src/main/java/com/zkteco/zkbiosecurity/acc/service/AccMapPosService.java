/**
 * File Name: AccMapPos
 * Created by GenerationTools on 2018-03-20 下午02:07
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccMapPosItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 对应百傲瑞达 BaseMapPosService
 * <AUTHOR>
 * @date:	2018-03-20 下午02:07
 * @version v1.0
 */
public interface AccMapPosService extends BaseService {

	/**
	 * 保存item实体，一般会有复杂业务逻辑
	 * @param item
	 */
	AccMapPosItem saveItem(AccMapPosItem item);

	/**
	 * 根据条件查询
	 * @param condition
	 * @return
	 */
	List<AccMapPosItem> getByCondition(AccMapPosItem condition);

	/**
	 * 根据ID查询
	 * @param id
	 * @return
	 */
	AccMapPosItem getItemById(String id);
	/**
	 * 根据ID查询
	 * @param mapId
	 * @param entityType
	 * @return
	 */
	String getEntityIdsByMapIdAndEntityType(String mapId, String entityType);

	/**    
	 * @Description: 删除地图位置
	 * <AUTHOR>  
	 * @date 2018/6/20 16:07  
	 * @param delIdList
	 * @param entityType
	 * @return
	 */  
	void delMapPosByEntityId(List<String> delIdList, String entityType);

	/**
	 * 获取摄像头节点的名称
	 * @param entityId
	 * @return
	 */
    String getVidChannel2EntityName(String entityId);

	/**
	 * 获取摄像头名称
	 * @param entityId
	 * @return
	 */
	String getVidChannelName(String entityId);

	/**
	 * 获取摄像头信息
	 * @param entityId
	 * @return
	 */
	Map<String, String> getVidChannelById(String entityId);
}