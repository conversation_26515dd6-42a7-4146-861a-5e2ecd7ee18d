package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;

import com.zkteco.zkbiosecurity.acc.vo.AccAlarmMonitorItem;

/**
 * 报警事件监控缓存
 *
 * <AUTHOR>
 * @date 2021-12-24 14:56
 * @since 1.0.0
 */
public interface AccAlarmMonitorCacheListService {

    /**
     * 向监控缓存列表中添加一条报警事件
     * 
     * @param accAlarmMonitorItem
     */
    void add(AccAlarmMonitorItem accAlarmMonitorItem);

    /**
     * 删除(确认)一条监控
     * 
     * @param id
     */
    void remove(String id);

    /**
     * 获取监控列表
     * 
     * @return
     */
    List<AccAlarmMonitorItem> getMonitorList();

    /**
     * 限流
     * 
     * @return
     */
    boolean acquire();

    /**
     * 标记监控列表是否有改动为推送
     * 
     * @param change
     */
    void markMonitorListChange(boolean change);

    /**
     * 推送最新数据有删除标记
     */
    void delMonitorListChangeMark();
}
