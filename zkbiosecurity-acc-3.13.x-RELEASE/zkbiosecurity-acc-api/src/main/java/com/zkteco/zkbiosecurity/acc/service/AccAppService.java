package com.zkteco.zkbiosecurity.acc.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONArray;
import com.zkteco.zkbiosecurity.acc.app.vo.AccAppDoorItem;
import com.zkteco.zkbiosecurity.acc.app.vo.AccAppTransactionItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccLevelItem;
import com.zkteco.zkbiosecurity.base.vo.AppResultMessage;

/**
 * <AUTHOR>
 * @Date: 2018/12/5 17:43
 */
public interface AccAppService {

    /**
     * 根据门名称获取门信息
     *
     * @param token
     * @param doorName
     * @param pageNo
     * @param pageSize
     * @return
     * @auther lambert.li
     * @date 2018/12/10 9:55
     */
    AppResultMessage getDoorByDoorName(String token, String doorName, int pageNo, int pageSize);

    /**
     * 根据门名称获取门信息
     * 
     * @param token:
     * @param doorName:
     * @param pageNo:
     * @param pageSize:
     * @param loginType: 登录类型
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2023-07-18 17:49
     * @since 1.0.0
     */
    AppResultMessage getDoorByDoorName(String token, String doorName, int pageNo, int pageSize, String loginType);

    /**
     * 根据门名称获取门信息
     * 
     * @param token:
     * @param accAppDoorItem:
     * @param pageNo:
     * @param pageSize:
     * @param loginType:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2024-07-18 18:03
     * @since 1.0.0
     */
    AppResultMessage getDoorByDoorName(String token, AccAppDoorItem accAppDoorItem, int pageNo, int pageSize,
        String loginType);

    /**
     * 根据门ID及过滤条件获取门事件记录
     *
     * @param doorId
     * @param filters
     * @param pageNo
     * @param pageSize
     * @return
     * @auther lambert.li
     * @date 2018/12/10 9:56
     */
    AppResultMessage getDoorDataByDoorIdAndFilters(String doorId, String filters, int pageNo, int pageSize);

    /**
     * 获取门禁报表记录
     *
     * @param doorId
     * @param filters
     * @param startTime
     * @param endTime
     * @param pageNo
     * @param pageSize
     * @return
     * @auther lambert.li
     * @date 2018/12/10 9:58
     */
    AppResultMessage getReportByFilters(String doorId, String filters, Date startTime, Date endTime, int pageNo,
        int pageSize);

    /**
     * 获取初始APP报警监控的事件
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-08-03 10:03
     * @param eventNos
     * @return com.alibaba.fastjson.JSONArray
     */
    JSONArray getAlarmEventData(String eventNos);

    /**
     * 获取门禁实时事件记录
     * 
     * @param clientId:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.app.vo.AccAppTransactionItem>
     * <AUTHOR>
     * @throws @date 2021-01-29 10:12
     * @since 1.0.0
     */
    List<AccAppTransactionItem> getRTMonitorData(String clientId);

    /**
     * 获取门禁报表记录
     * 
     * @param doorId:
     * @param filters:
     * @param startTime:
     * @param endTime:
     * @param pageNo:
     * @param pageSize:
     * @param userName:
     * @param userId:
     * @param loginType:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2023-07-24 11:38
     * @since 1.0.0
     */
    AppResultMessage getReportByFilters(String doorId, String filters, Date startTime, Date endTime, int pageNo,
        int pageSize, String userName, String userId, String loginType, String inEventNo);

    /**
     * 根据区域统计在线 离线设备数
     * 
     * @param areaIds:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2023-07-24 11:39
     * @since 1.0.0
     */
    AppResultMessage getAccDeviceCount(String areaIds);

    AppResultMessage getAccDevicesPager(AccDeviceItem condition, int pageNo, int pageSize, String userName);

    /**
     * 门禁记录详情
     * 
     * @param id:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2023-08-23 15:54
     * @since 1.0.0
     */
    AppResultMessage getReportDetail(String id);

    /**
     * 获取权限组
     * 
     * @param username:
     * @param codition:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2023-08-31 10:21
     * @since 1.0.0
     */
    AppResultMessage loadPagerByAuthFilter(String username, AccLevelItem codition, int pageNo, int pageSize);

    /**
     * 根据权限组进行门操作
     * 
     * @param userName:
     * @param type:
     * @param interval:
     * @param levelIds:
     * @return java.util.Map<java.lang.String,java.lang.String>
     * <AUTHOR>
     * @throws
     * @date 2023-08-31 10:41
     * @since 1.0.0
     */
    AppResultMessage operateDoorByLevel(String userName, String type, String interval, String levelIds);

    /**
     * 处理获取命令执行结果
     * 
     * @param dataMap:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2023-08-31 10:43
     * @since 1.0.0
     */
    AppResultMessage dealResultData(Map<String, String> dataMap);

    /**
     * 获取门禁权限组列表
     * 
     * @param loginType:
     * @param username:
     * @param codition:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2025-04-17 10:52
     * @since 1.0.0
     */
    AppResultMessage getAccLevelsByCodition(String loginType, String username, AccLevelItem codition, int pageNo,
        int pageSize);

    /**
     * 人员添加权限组
     * 
     * @param lang:
     * @param pin:
     * @param levelIds:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2025-04-17 11:22
     * @since 1.0.0
     */
    AppResultMessage addLevelPerson(String lang, String pin, String levelIds);
}
