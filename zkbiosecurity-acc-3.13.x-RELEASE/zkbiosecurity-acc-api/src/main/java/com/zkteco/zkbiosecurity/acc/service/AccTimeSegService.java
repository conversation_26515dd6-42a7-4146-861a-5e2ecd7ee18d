package com.zkteco.zkbiosecurity.acc.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.acc.vo.AccTimeSegItem;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;

/**
 * 门禁时间段service
 * 
 * <AUTHOR>
 * @date: 2018-02-24 上午10:18
 * @version v1.0
 */
public interface AccTimeSegService extends BaseService {

    /**
     * 初始化数据
     */
    AccTimeSegItem initData(AccTimeSegItem item);

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    AccTimeSegItem saveItem(AccTimeSegItem item);

    /**
     * 新增时间段业务id
     *
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2021-02-03 10:24
     * @since 1.0.0
     */
    Long createBId();

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AccTimeSegItem> getByCondition(AccTimeSegItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    AccTimeSegItem getItemById(String id);

    /**
     * 获取时间段的下拉列表数据结构
     *
     * @return
     */
    List<SelectItem> getTimeSegList();

    /**
     * 获取初始化时间段
     * 
     * @return
     */
    AccTimeSegItem getInitTimeSeg();

    /**
     * @Description: 验证时间段名称是否存在
     * <AUTHOR>
     * @date 2018/5/23 17:12
     * @param name 时间段名称
     * @return
     */
    String validName(String name);

    /**
     * @Description: 根据门id获取门有效时间段信息
     * <AUTHOR>
     * @date 2018/5/31 16:37
     * @param doorIdList
     * @return
     */
    Map<String, String> getActiveTimeSegByDoorId(List<String> doorIdList);

    /**
     * @Description: 根据门id获取常开时间段信息
     * <AUTHOR>
     * @date 2018/5/31 16:52
     * @param doorIdList
     * @return
     */
    Map<String, String> getPassmodeTimeSegByDoorId(List<String> doorIdList);

    /**
     * @Description:
     * <AUTHOR>
     * @date 2018/6/15 15:04
     * @param timeSegId
     * @param date
     * @param eventDate
     * @return
     */
    boolean checkTimeSeg(String timeSegId, int date, Date eventDate);

    /**
     * 时间段迁移
     *
     * @param accTimeSegItems
     */
    void handlerTransfer(List<AccTimeSegItem> accTimeSegItems);

    /**
     * 根据时间段id获取业务id
     * 
     * @param timeSegId
     * @return
     */
    Long getBusinessIdByTimeSegId(String timeSegId);

    /**
     * 获取初始化时间段id
     * 
     * @return
     */
    String getInitTimeSegId();

    /**
     * 根据名称获取时间段
     *
     * @return
     */
    AccTimeSegItem getItemByName(String name);
}