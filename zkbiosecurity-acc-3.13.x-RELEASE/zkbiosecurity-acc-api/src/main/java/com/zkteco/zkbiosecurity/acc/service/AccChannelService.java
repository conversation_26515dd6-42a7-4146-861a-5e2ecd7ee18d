package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccChannelSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.Collection;

public interface AccChannelService {

    /**
     * 判断视频模块是否存在摄像头
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/15 14:50
     * @param
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    ZKResultMsg isExistVidChannel();

    /**
     * 根据实体id和实体名称获取绑定的视频摄像头id
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/15 14:50
     * @param entityIds 实体id
     * @param entityClassName 实体名称
     * @return java.lang.String
     */
    String getBindChannelIds(Collection<String> entityIds, String entityClassName);

    /**
     * 绑定/解绑视频通道（摄像头）
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/15 14:50
     * @param channelIds 已绑定的通道id
     * @param entityName 实体名称
     * @param entityId 实体id
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    ZKResultMsg bindOrUnbindChannel(String deviceSn, String channelIds, String entityName, String entityId);

    Pager loadPagerByAuthFilter(String sessionId, AccChannelSelectItem condition, int pageNo, int pageSize);
}
