/**
 * File Name: AccReader Created by GenerationTools on 2018-03-13 上午10:06 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.acc.vo.AccReaderItem;
import com.zkteco.zkbiosecurity.acc.vo.AccSelectReaderItem;
import com.zkteco.zkbiosecurity.acc.vo.AccSelectReaderRadioItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * 对应百傲瑞达 AccReaderService
 * 
 * <AUTHOR>
 * @date: 2018-03-13 上午10:06
 * @version v1.0
 */
public interface AccReaderService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     * @param applyTo
     * @param readerModel
     * @param readMode
     */
    AccReaderItem saveItem(AccReaderItem item, String applyTo, String readerModel, String readMode);

    /**
     * 功能描述: 保存实体
     * 
     * <AUTHOR>
     * @since 2019-04-22 9:24
     * @Param [item, applyTo]
     * @return
     */
    AccReaderItem saveItem(AccReaderItem item, String applyTo);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AccReaderItem> getByCondition(AccReaderItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    AccReaderItem getItemById(String id);

    /**
     * 判断是否存在
     * 
     * @param name
     * @return
     */
    boolean isExist(String name);

    /**
     * 判断是否存在视频模块
     * 
     * @return
     */
    boolean isExistVid();

    /**
     * 门禁电子地图判断是否显示添加摄像头按钮（ivs不支持，因新增方法）
     *
     * @return boolean
     * <AUTHOR>
     * @date 2021-08-27 16:39
     * @since 1.0.0
     */
    boolean accMapIsShowVid();

    /**
     * 选择控件获取读头列表
     * 
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager getSelectItemByPage(AccSelectReaderItem condition, int pageNo, int pageSize);

    /**
     * 根据进出状态和门ID获取item
     * 
     * <AUTHOR>
     * @date: 2018-04-26 上午10:06
     * @param readerState
     * @param doorId
     * @return
     */
    AccReaderItem getByReaderStateAndDoorId(short readerState, String doorId);

    /**
     * 根据设备ID获取item集合
     * 
     * <AUTHOR>
     * @date: 2018-05-11 14:06
     * @param deviceId
     * @return
     */
    List<AccReaderItem> getItemListByDevId(String deviceId);

    /**
     * 判断IP是否存在
     * 
     * @param ip
     * @return
     */
    boolean isExistIP(String ip);

    /**
     * 判断通信地址是否存在
     * 
     * @param item
     * @return
     */
    boolean readerCommAddressExist(AccReaderItem item);

    /**
     * @Description: 根据用户权限过滤显示读头
     * <AUTHOR>
     * @date 2018/6/20 11:35
     * @param sessionId
     * @param codition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager loadPagerByAuthFilter(String sessionId, AccReaderItem codition, int pageNo, int pageSize);

    /**
     * @Description: 判断系统是否控卡
     * <AUTHOR>
     * @date 2018/6/22 11:51
     * @return
     */
    boolean checkCardControl();

    /**
     * @Description: 过滤非一体机的读头，用于设置韦根读头
     * <AUTHOR>
     * @date 2018/6/26 12:00
     * @return
     */
    Pager getWGReaderFilterList(AccSelectReaderRadioItem condition, int pageNo, int pageSize);

    /**
     * @Description: 获取读头定义批量新增读头列表
     * <AUTHOR>
     * @date 2018/8/1 18:44
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager getReaderZoneItemByPage(AccSelectReaderItem condition, int pageNo, int pageSize);

    /**
     * 获取pull设备的读头id
     *
     * @return
     */
    List<String> getPullReaderIds();

    /**
     * 根据读头id获取对应的门id
     *
     * @param readerIds
     * @return
     */
    List<String> getDoorIdByReaderId(List<String> readerIds);

    /**
     * @Description ai模块获取绑定的读头(目前只支持inbio5的控制器)
     * <AUTHOR>
     * @Date 2018/11/20 14:40
     * @Param
     * @return
     **/
    Pager selectReaderBindAiDeviceList(AccSelectReaderRadioItem condition, int pageNo, int pageSize);

    /**
     * 根据读头id获取对应的设备id
     *
     * @param readerIds
     * @return
     */
    List<String> getDevIdByReaderId(List<String> readerIds);

    /**
     * 读头数据迁移
     * 
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午10:49:52
     * @param items
     */
    void handlerTransfer(List<AccReaderItem> items);

    /**
     * 功能描述:过滤非TCP读头，用于身份证读取方式是TCP读头的情况下
     * 
     * <AUTHOR>
     * @since 2019-05-06 17:22
     * @Param [condition, pageNo, pageSize]
     * @return
     */
    Pager getTcpReaderFilterList(AccSelectReaderRadioItem condition, int pageNo, int pageSize);

    /**
     * 功能描述:给普通模式的TCP读头下发身份证登记模式
     * 
     * <AUTHOR>
     * @since 2019-05-07 15:59
     * @Param [readerId]
     * @return
     */
    String startReaderIDCard(String readerId);

    /**
     * 功能描述:根据用户权限过滤非一体机的读头，用于设置韦根读头
     * 
     * <AUTHOR>
     * @since 2019-05-27 11:58
     * @Param [sessionId, condition, pageNo, pageSize]
     * @return
     */
    Pager getWGReaderFilterListByAuthFilter(String sessionId, AccSelectReaderRadioItem condition, int pageNo,
        int pageSize);

    /**
     * 根据设备sn查询读头信息-数据迁移使用
     * 
     * <AUTHOR>
     * @since 2019-07-23 15:11
     * @Param [sns]
     * @return
     */
    List<AccReaderItem> getItemListByDevSnIn(List<String> sns);

    /**
     * 保存Item集合
     *
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccReaderItem>
     * <AUTHOR>
     * @date 2021-01-28 8:49
     * @since 1.0.0
     */
    List<AccReaderItem> saveReaderItemList(List<AccReaderItem> items);

    /**
     * 根据id集合获取读头信息
     *
     * @param idList:读头id集合
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccReaderItem>
     * <AUTHOR>
     * @date 2021-01-28 17:26
     * @since 1.0.0
     */
    List<AccReaderItem> getItemsByIdList(List<String> idList);

    /**
     * 分页查询，无复杂的业务逻辑
     *
     * @param condition:查询条件
     * @param page:页索引，从0开始
     * @param size:每页几条
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-01-28 17:49
     * @since 1.0.0
     */
    Pager getSimpleItemsByPage(BaseItem condition, int page, int size);

    /**
     * 根据门id获取读头信息
     *
     * @param doorId:门id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccReaderItem>
     * <AUTHOR>
     * @date 2021-01-29 8:46
     * @since 1.0.0
     */
    List<AccReaderItem> getItemsByAccDoorId(String doorId);

    /**
     * 保存item对象，无复杂的业务逻辑
     *
     * @param item:读头信息
     * @return com.zkteco.zkbiosecurity.acc.vo.AccReaderItem
     * <AUTHOR>
     * @date 2021-01-29 8:56
     * @since 1.0.0
     */
    AccReaderItem saveSimpleItem(AccReaderItem item);

    /**
     * 根据设备id统计读头数量
     *
     * @param devId:设备id
     * @return int
     * <AUTHOR>
     * @date 2021-01-29 9:35
     * @since 1.0.0
     */
    int countReaderNumByDevId(String devId);

    /**
     * 根据读头编号和设备id获取读头信息
     *
     * @param readerNo:读头编号
     * @param devId:设备id
     * @return com.zkteco.zkbiosecurity.acc.vo.AccReaderItem
     * <AUTHOR>
     * @throws
     * @date 2021-02-01 10:35
     * @since 1.0.0
     */
    AccReaderItem getReaderByReaderNoAndDevId(Short readerNo, String devId);

    /**
     * 获取读头类简写名称
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021-02-07 14:03
     * @since 1.0.0
     */
    String getAccReaderSimpleName();

    /**
     * 根据读头id集合获取读头Item Map <readerId, AccReaderItem>
     *
     * @param readerList:读头id
     * @return java.util.Map<java.lang.String,com.zkteco.zkbiosecurity.acc.vo.AccReaderItem>
     * <AUTHOR>
     * @date 2021-02-07 16:59
     * @since 1.0.0
     */
    Map<String, AccReaderItem> getItemsMapByReaderIds(List<String> readerList);

    /**
     * 获取读头绑定的视频通道
     *
     * @param readerId:
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023-02-23 16:23
     * @since 1.0.0
     */
    String getAccReaderBindVidChannel(String readerId);

    List<AccReaderItem> getItemsByDoorIdIn(String doorIds);
}