/**
 * File Name: AccPerson Created by GenerationTools on 2018-03-02 下午02:10 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * 对应百傲瑞达 AccPersonService
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-03-02 下午02:10
 */
public interface AccPersonService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     *
     * @param item
     */
    AccPersonItem saveItem(AccPersonItem item);

    /**
     * 根据条件查询
     *
     * @param condition
     * @return
     */
    List<AccPersonItem> getByCondition(AccPersonItem condition);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    AccPersonItem getItemById(String id);

    /**
     * @param personId
     * @return com.zkteco.zkbiosecurity.acc.vo.AccPersonItem
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/4/28 11:19
     */
    AccPersonItem getItemByPersonId(String personId);

    /**
     * 根据部门ids获取部门下所有人员id
     *
     * @return
     * <AUTHOR>
     * @since 2018/6/26 17:33
     */
    String getPersonIdsByDeptIds(String deptIds);

    /**
     * 获取部门下人员数量
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/24 17:33
     */
    long getPersonCountByDept(String deptIds);

    /**
     * 根据传入的item返回人员ID对应人员vo的map集合
     *
     * @author: mingfa.zheng
     * @date: 2018/4/14 16:32
     * @return:
     */
    Map<String, AccSelectPersonItem> getPersonItemMap(Collection<? extends BaseItem> condition);

    /**
     * 根据ID集合获取item集合
     *
     * @return
     */
    List<AccPersonItem> getItemsByIds(List<String> idList);

    /**
     * 过滤已经选中的人员
     *
     * @author: mingfa.zheng
     * @date: 2018/4/20 18:20
     * @param：accSelectPersonItem 门禁对应选人控件的item
     * @return:
     */
    Pager getNoExistPerson(String sessionId, AccSelectPersonItem accSelectPersonItem, int pageNo, int pageSize);

    String getIdByPersonId(String persPersonId);

    Pager getDoorPerson(AccTransactionDoorPersonItem condition, int pageNo, int pageSize);

    Pager getAccTransactionPerson(String sessionId, AccTransactionPersonItem condition, int pageNo, int pageSize);

    List<AccPersonItem> getItemsByPersPersonIds(String persPersonIds);

    /**
     * 获取人员id和工号
     *
     * @param param
     * @param personParamList
     * @return List<Object [ ]>
     * @author: mingfa.zheng
     * @date: 2018/4/26 15:48
     */
    List<AccPersonOptItem> getPersonPinAndPersonIdByParam(String param, List<Object> personParamList);

    /**
     * 导出数据
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/12/3 11:48
     */
    List<AccTransactionDoorPersonItem> getDoorPersonItemData(AccTransactionDoorPersonItem condition, int beginIndex,
        int endIndex);

    /**
     * @param table
     * @param data
     * @param cmdId
     * @param sn
     * @return
     * @Description: 处理设备上传的人员信息
     * <AUTHOR>
     * @date 2018/6/12 17:52
     */
    Map<String, Integer> dealPersonInfo(String table, String data, String cmdId, String sn);

    /**
     * @param table
     * @param data
     * @param sn
     * @return
     * @Description: 处理设备上传的人员指纹信息
     * <AUTHOR>
     * @date 2018/6/12 17:52
     */
    Map<String, Integer> dealPersonTemplateInfo(String table, String data, String sn);

    /**
     * @param cardNo
     * @return
     * @Description: 过滤有有效卡的人员
     * <AUTHOR>
     * @date 2018/6/22 8:53
     */
    Map<String, String> filterPersonByVaildCard(String cardNo);

    /**
     * @param pageNo
     * @param pageSize
     * @return
     * @Description: 获取没有卡的人员列表
     * <AUTHOR>
     * @date 2018/6/22 10:01
     */
    Pager getNoCardPerson(String sessionId, AccSelectPersonRadioItem accSelectPersonRadioItem, int pageNo,
        int pageSize);

    /**
     * 显示双列表右列表人员没有卡号的数据
     *
     * @return
     * @author: mingfa.zheng
     * @date: 2018/4/26 15:48
     */
    Pager getPersonNoCardItemList(String sessionId, AccPersonListNoCardItem accPersonListNoCardItem, int pageNo,
        int pageSize);

    /**
     * 显示双列表右列表人员包含卡号的数据
     *
     * @return
     * @author: mingfa.zheng
     * @date: 2018/4/26 15:48
     */
    Pager getPersonItemList(String sessionId, AccPersonListItem accPersonListItem, int pageNo, int pageSize);

    /**
     * 获取门禁导出人员数据
     *
     * @return
     * @author: mingfa.zheng
     * @date: 2018/4/26 15:48
     */
    List<AccPersonListItem> getExportPersonItemList(String sessionId, AccPersonListItem accPersonListItem,
        int beginIndex, int endIndex);

    /**
     * 根据当前登录用户获取到该用户下的部门ids
     *
     * @param sessionId
     * @return
     * <AUTHOR>
     * @date 2018/6/28 14:56
     */
    String getDeptIdsByAuthFilter(String sessionId);

    /**
     * @param personId
     * @param cardNo
     * @return
     * @Description: 将卡添加到已登记人员中
     * <AUTHOR>
     * @date 2018/6/27 11:35
     */
    void setCardNoToPerson(String personId, String cardNo);

    /**
     * 保存门禁人员设置并下发给设备
     *
     * @param item
     */
    void saveParamSet(AccPersonItem item);

    /**
     * @param commType
     * @param data
     * @param table
     * @return
     * @Description: 统计添加、更新的人脸记录数
     * <AUTHOR>
     * @date 2018/6/28 19:46
     */
    Map<String, String> addPersonFaceRecord(Short commType, String data, String table);

    /**
     * @param commType
     * @param cmdId
     * @param sn
     * @param data
     * @param table
     * @return
     * @Description: 处理设备人员扩展信息如延迟通行
     * <AUTHOR>
     * @date 2018/6/29 9:03
     */
    Map<String, String> dealExtuserInfo(Short commType, Long cmdId, String sn, String data, String table);

    /**
     * @param commType
     * @param cmdId
     * @param data
     * @param table
     * @return
     * @Description: 处理人员多卡数据
     * <AUTHOR>
     * @date 2018/6/29 9:27
     */
    Map<String, String> dealMulCardUserInfo(Short commType, Long cmdId, String data, String table);

    /**
     * @param table
     * @param data
     * @param commType
     * @return
     * @Description: 添加人员指静脉处理
     * <AUTHOR>
     * @date 2018/7/4 13:55
     */
    Map<String, String> addPersonFvTempalteRecord(String table, String data, Short commType);

    /**
     * @param fieldMap
     * @return
     * @Description: 保存人员身份证信息
     * <AUTHOR>
     * @date 2018/7/17 14:14
     */
    void saveIdentityCardInfo(Map<String, String> fieldMap, String systemFilePath);

    /**
     * @param data
     * @return
     * @Description: 保存人员头像
     * <AUTHOR>
     * @date 2018/7/18 8:53
     */
    void savePersonPhoto(String data);

    /**
     * @Description: 对人员ids按splitSize进行分批
     * @author: mingfa.zheng
     * @date: 2018/8/9 9:53
     * @param: [personIds, splitSize]
     * @return:
     **/
    List<String> splitPersonIds(String personIds, int splitSize);

    /**
     * 处理设备上传比对照片
     *
     * @param commType
     * @param data
     * @param table
     * @return
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年9月27日 下午5:08:18
     */
    Map<String, String> addPersonBiophotoRecord(Short commType, String data, String table);

    /**
     * 获取设备权限组下人员比对照片数量
     *
     * @param devId
     * @return
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年9月28日 下午2:14:38
     */
    int getBiophotoCount(String devId);

    /**
     * 处理设备上传的一体化模板数据
     *
     * @param commType
     * @param data
     * @param table
     * @return
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年11月14日 下午3:05:23
     */
    Map<String, String> addPersonBiodataRecord(Short commType, String data, String table);

    /**
     * 门禁人员迁移
     *
     * @param accPersonItems
     */
    void handlerTransfer(List<AccPersonItem> accPersonItems);

    /**
     * 下发生物模板信息给设备 描述：用于获取生物模板信息，转而下发给其他设备
     *
     * @param table
     * @param data
     * @param commType
     * @param sn
     * <AUTHOR>
     * @since 2018年12月13日 上午10:47:22
     */
    void sendTemplateToDevs(String table, String data, Short commType, String sn);

    /**
     * 下发生物模板信息给其他设备
     *
     * @param table
     * @param resolveMap
     * @param sn
     */
    void sendTemplateToDevs(String table, List<Map<String, String>> resolveMap, String sn);

    /**
     * 下发人员信息给设备 描述：用于设备自动上传人员信息，转而下发给其他设备
     *
     * @param table
     * @param data
     * @param commType
     * @param sn
     * <AUTHOR>
     * @since 2018年12月13日 下午3:08:22
     */
    void sendPersToDevs(String table, String data, Short commType, String sn);

    /**
     * 下发多卡表信息到设备：用于设备自动上传
     *
     * @param table
     * @param data
     * @param sn
     * <AUTHOR>
     * @since 2018年12月13日 下午5:44:34
     */
    void sendMulCardUserInfoToDevs(String table, String data, String sn);

    /**
     * 下发扩展人员信息给设备：用于设备自动上传
     *
     * @param table
     * @param data
     * @param sn
     * <AUTHOR>
     * @since 2018年12月13日 下午7:15:07
     */
    void sendExtInfoToDevs(String table, String data, String sn);

    /**
     * 下发对比照片给设备 描述：用于设备上传对比照片，转而下发给其他设备
     *
     * @param table
     * @param data
     * @param sn
     */
    void sendBioPhotoInfoToDevs(String table, String data, String sn);

    /**
     * 根据人员pin获取人事人员照片路径
     *
     * @param pin
     * @return java.lang.String
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/5/23 16:33
     */
    String getPersPersonPhotoPathByPin(String pin);

    /**
     * 下发用户照片给设备 描述：用于设备上传用户照片，转而下发给其他设备
     *
     * @return
     * <AUTHOR>
     * @Param [table, data, sn]
     * @since 2019-07-02 15:21
     */
    void sendUserPicToDevs(String table, String data, String sn);

    /**
     * 判断是否有短信设置的许可
     *
     * @param
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-02-17 17:50
     */
    boolean checkShowSMS();

    /**
     * 处理设备上传的生物模版
     *
     * @param table
     * @param resolveMap
     * @return
     */
    Map<String, Integer> dealBioTemplate(String table, List<Map<String, String>> resolveMap);

    /**
     * 获取要导出的权限组人员信息
     *
     * @param accLevelPersonItem:
     * @param beginIndex:
     * @param endIndex:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelByLevelExportItem>
     * @throws
     * <AUTHOR>
     * @date 2022-07-25 14:02
     * @since 1.0.0
     */
    @Deprecated
    List<AccPersonLevelByLevelExportItem>
        getExportLevelPersonItemList(AccPersonLevelByLevelExportItem accLevelPersonItem, int beginIndex, int endIndex);

    /**
     * 获取按权限组设置人员信息
     *
     * @param sessionId:
     * @param condition:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * @throws
     * <AUTHOR>
     * @date 2022-07-26 10:19
     * @since 1.0.0
     */
    Pager getLevelPersonItemsByAuthFilter(String sessionId, AccPersonForLevelItem condition, int pageNo, int pageSize);

    /**
     * 按权限组加人选人控件数据过滤
     *
     * @param sessionId:
     * @param condition:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * @throws
     * <AUTHOR>
     * @date 2022-08-02 11:55
     * @since 1.0.0
     */
    Pager getNoExistLevelPerson(String sessionId, AccSelectPersonItem condition, int pageNo, int pageSize);

    /**
     * 首人常开人员列表
     *
     * @param sessionId:
     * @param condition:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * @throws
     * <AUTHOR>
     * @date 2022-08-02 14:15
     * @since 1.0.0
     */
    Pager getFirstOpenPersonItemList(String sessionId, AccPersonFirstOpenItem condition, int pageNo, int pageSize);

    /**
     * 多人开门人员组人员列表信息获取
     *
     * @param sessionId:
     * @param condition:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * @throws
     * <AUTHOR>
     * @date 2022-08-02 14:59
     * @since 1.0.0
     */
    Pager getCombOpenPersonItemList(String sessionId, AccPersonCombOpenPersonItem condition, int pageNo, int pageSize);

    /**
     * 验证方式规则人员组-人员列表
     *
     * @param sessionId:
     * @param condition:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * @throws
     * <AUTHOR>
     * @date 2022-08-02 17:47
     * @since 1.0.0
     */
    Pager getPersonVerifyModeRuleItemList(String sessionId, AccPersonVerifyModeRuleItem condition, int pageNo,
        int pageSize);

    /**
     * 根据权限过滤人员信息
     *
     * @param accLevelPersonItem:
     * @param beginIndex:
     * @param endIndex:
     * @param sessionId:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelByLevelExportItem>
     * @throws
     * <AUTHOR>
     * @date 2022-10-28 14:48
     * @since 1.0.0
     */
    List<AccPersonLevelByLevelExportItem> getExportLevelPersonItemListByAuthFilter(
        AccPersonLevelByLevelExportItem accLevelPersonItem, int beginIndex, int endIndex, String sessionId);

    /**
     * 获取门下人员-用户权限过滤
     *
     * @param sessionId:
     * @param condition:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * @throws
     * <AUTHOR>
     * @date 2023-12-21 10:43
     * @since 1.0.0
     */
    Pager getDoorPersonByAuthFilter(String sessionId, AccTransactionDoorPersonItem condition, int pageNo, int pageSize);

}