package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * <AUTHOR>
 * @date 2021/9/6 10:10
 * @since 1.0.0
 */
public interface AccLicensePointsCheckService {

    /**
     * 许可校验
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2021-09-06 10:25
     * @since 1.0.0
     */
    ZKResultMsg check();

    /**
     * （必须要在设备新增保存后调用）更新许可
     *
     * @return boolean
     * <AUTHOR>
     * @date 2021-09-06 10:11
     * @since 1.0.0
     */
    boolean update();

    /**
     * 判断是否走盒子许可
     *
     * @return boolean
     * <AUTHOR>
     * @date 2021-09-06 10:14
     * @since 1.0.0
     */
    boolean isZKBioCVV6000Server();
}
