/**
 * File Name: AccDevice Created by GenerationTools on 2018-03-08 下午02:41 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 对应百傲瑞达 AccDeviceService
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-03-08 下午02:41
 */
public interface AccDeviceService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     *
     * @param item
     */
    AccDeviceItem saveItem(AccDeviceItem item);

    /**
     * 根据条件查询
     *
     * @param condition
     * @return
     */
    List<AccDeviceItem> getByCondition(AccDeviceItem condition);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    AccDeviceItem getItemById(String id);

    /**
     * 过滤设备(通信类型、权限、事件编号)
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/4 15:11
     */
    List<String> getDevIdByAuthAndTrigger(List<Integer> triggerNoList);

    /**
     * 查询权限组下的设备
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/17 15:40
     */
    List<AccDeviceItem> getDevByLevel(String levelIds);

    /**
     * 搜索门禁设备
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/17 15:41
     */
    List<AccSearchDeviceItem> searchDeviceList();

    /**
     * 修改设备参数
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/17 15:41
     */
    int modifyDeviceOptions(String options, String pwd);

    /**
     * 设备授权
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/17 16:58
     */
    boolean authDevice(String sn, AccSearchAddDeviceItem item);

    /**
     * 添加push设备
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/19 14:42
     */
    boolean setPushDevice(String deviceJsonStr);

    /**
     * 根据设备SN获取设备Item
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/19 14:43
     */
    AccDeviceItem getItemByDevSn(String devSn);

    /**
     * 获取设备事件信息
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/23 8:51
     */
    Map<String, String> getDevEvent(String sn);

    /**
     * @return
     * <AUTHOR>
     * @since 2018/4/23 10:10
     */
    Map<String, String> getVerifyMode(String devSn);

    /**
     * 获取设备状态 0 离线；1 在线
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/24 17:30
     */
    String getStatus(String sn);

    /**
     * 设备启用
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/24 8:53
     */
    void setDevEnable(String ids);

    /**
     * 设备禁用
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/24 8:53
     */
    void setDevDisable(String ids);

    /**
     * 根据id获取设备
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/24 9:02
     */
    List<AccDeviceItem> getItemByIds(String ids);

    /**
     * 判断设备是否启用
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/24 9:15
     */
    boolean isEnabled(String sn);

    /**
     * 判断设备是否为新一体机
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/24 10:27
     */
    boolean isNewAccessControlDevice(short machineType, String devName);

    /**
     * 获取指纹、指静脉、人脸的功能权限组合 111表示三个都支持 101表示支持指纹和人脸
     *
     * @param sn
     * @return
     */
    String getFingerFvFaceFunOn(String sn);

    /**
     * @param devIds 设备Id
     * @return
     * @Description: 检查是否开启多人验证
     * <AUTHOR>
     * @date 2018/4/25 14:24
     */
    String checkCombOpenDoor(String devIds);

    /**
     * @param id 设备id
     * @return
     * @Description: 获取需要同步的设备下被绑定的读头一体机，同时进行同步操作
     * <AUTHOR>
     * @date 2018/4/25 16:50
     */
    List<AccDeviceItem> getDevAsWGReaderByDevId(String id);

    /**
     * @param sn 设备序列号
     * @return
     * @Description: 判断是否是单向设备
     * <AUTHOR>
     * @date 2018/4/25 19:06
     */
    boolean isOneWay(String sn);

    /**
     * @param id 设备id
     * @param verifyParam 是否开启验证
     * @param offlineRule 设备离线规则
     * @param isImme 是否为紧急命令
     * @return
     * @Description 下发后台验证参数
     * <AUTHOR>
     * @date 2018/4/25 13:48
     */
    long issueVerifyParamToDev(String id, String verifyParam, String offlineRule, boolean isImme);

    /**
     * @param sn 设备sn
     * @param optBox 需要同步的参数
     * @return
     * @Description: 同步数据到设备, 按所给的参数判断哪些数据需要同步
     * <AUTHOR>
     * @date 2018/4/25 16:56
     */
    void syncDataToDev(String sn, String[] optBox);

    /**
     * 根据设备类型获取设备ID
     *
     * @param machineType
     * @return
     * <AUTHOR>
     * @since 2018/4/25 10:27
     */
    List<String> getByMachineType(Short machineType);

    /**
     * 获取系统中已存在的设备或门数量+即将授权的数量(即已授权的数量+即将授权的数量)
     *
     * @param deviceCount 添加设数量
     * @param doorCount 添加设备的门数量
     * @param commType 设备类型 1、2：pull设备；3：push设备
     * @return countParams example: Map<String ,Integer> countParams = Maps.newHashMap();
     *         devCountParams.put("pullGateCount", 200); devCountParams.put("pullDevCount", 100);
     *         devCountParams.put("pushGateCount", 50); devCountParams.put("pushDevCount", 10);
     * <AUTHOR>
     * @since 2018年4月27日 14:37:55
     */
    Map<String, Integer> getAfterLicensedCount(int deviceCount, int doorCount, short commType);

    /**
     * 获取系统中已存在的设备或门数量(即已授权的数量)
     *
     * @return countParams example: Map<String ,Integer> countParams = Maps.newHashMap();
     *         devCountParams.put("pullGateCount", 200); devCountParams.put("pullDevCount", 100);
     *         devCountParams.put("pushGateCount", 50); devCountParams.put("pushDevCount", 10);
     * <AUTHOR>
     * @since 2018年4月27日 14:37:55
     */
    Map<String, Integer> getBeforeLicensedCount();

    /**
     * 创建设备业务id
     *
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2021-02-04 15:43
     * @since 1.0.0
     */
    Long createBId();

    /**
     * 获取事件记录
     *
     * @param item
     * @return
     */
    Map<String, String> uploadTransaction(AccDeviceUploadTransactionItem item);

    /**
     * 下发获取身份证表数据
     *
     * @param devId:设备id
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2020-09-23 9:50
     * @since 1.0.0
     */
    Long getIdentityCardInfo(String devId);

    /**
     * @param devSn 设备序列号
     * @return
     * @Description: 下发同步设备时间命令
     * <AUTHOR>
     * @date 2018/5/2 16:13
     */
    Long syncTime(String devSn);

    /**
     * @param id 设备ID
     * @param ipAddress 设备ip地址
     * @return
     * @Description: 判断ip是否存在
     * <AUTHOR>
     * @date 2018/5/3 13:47
     */
    boolean isExistsIp(String id, String ipAddress);

    /**
     * @param id 设备ID
     * @param newIp 新IP地址
     * @return
     * @Description: 判断设备第二网卡IP是否重复
     * <AUTHOR>
     * @date 2018/5/3 14:03
     */
    boolean isExistsSecondIp(String id, String newIp);

    /**
     * 修改设备ip地址
     *
     * @param devId
     * @param ipAddress
     * @param subnetMask
     * @param gateway
     */
    Long updateIpAddr(String devId, String ipAddress, String subnetMask, String gateway);

    /**
     * @param devId 设备ID
     * @param ipAddressSec 第二网卡的IP
     * @param netMaskSec 第二网卡的子网掩码
     * @param gateIPAddressSec 第二网卡的网关
     * @param serverNetworkCard 哪个网卡做子设备连接
     * @return
     * @Description: 修改设备扩展网卡ip等参数
     * <AUTHOR>
     * @date 2018/5/3 15:05
     */
    Long updateIpAddrEx(String devId, String ipAddressSec, String netMaskSec, String gateIPAddressSec,
        String serverNetworkCard);

    /**
     * @param devId 设备ID
     * @param isRegist 是否设置为登记机
     * @return
     * @Description: 设置登记机
     * <AUTHOR>
     * @date 2018/5/3 16:44
     */
    void setRegistrationDevice(String devId, boolean isRegist);

    /**
     * @param ids 设备ID
     * @return
     * @Description: 获取设备的使用情况, 包括指纹数和人员数
     * <AUTHOR>
     * @date 2018/5/4 9:56
     */
    List<List<String>> queryDevUsage(String ids);

    /**
     * @param devId 设备ID
     * @return
     * @Description: 获取设备的人员数量和指纹数量（从固件中获取）
     * <AUTHOR>
     * @date 2018/5/4 11:27
     */
    Map<String, Long> getDevUserAndTempCounts(String devId);

    /**
     * @param devIp 设备IP
     * @return
     * @Description: 根据设备IP获取设备
     * <AUTHOR>
     * @date 2018/5/7 10:18
     */
    AccDeviceItem getItemByIp(String devIp);

    /**
     * @param accDeviceItem 设备
     * @param beginIndex 开始条数
     * @param endIndex 结束条数
     * @return
     * @Description: 获取导出列表数据
     * <AUTHOR>
     * @date 2018/5/7 15:53
     */
    List<AccDeviceExportItem> getExportItemList(AccDeviceExportItem accDeviceItem, int beginIndex, int endIndex);

    /**
     * @param accDeviceItemList 设备集合
     * @return
     * @Description: 根据设备获取区域信息
     * <AUTHOR>
     * @date 2018/5/7 15:54
     */
    Map<String, String> getAuthAreaByDev(List<AccDeviceExportItem> accDeviceItemList);

    /**
     * 修改生物识别阈值
     *
     * @param acc4UpdateMThreshold
     * @return
     */
    Long updateMThreshold(Acc4UpdateMThreshold acc4UpdateMThreshold);

    /**
     * 修改设备密码
     *
     * @param devId
     * @param newCommPwd
     */
    Long updateCommPwd(String devId, String newCommPwd);

    /**
     * @param alias 设备名称
     * @return
     * @Description: 判断是否存在相同名称
     * <AUTHOR>
     * @date 2018/5/9 19:31
     */
    boolean isExistAlias(String alias);

    /**
     * @param ipAddress 设备IP
     * @return
     * @Description: 判断设备IP是否重复
     * <AUTHOR>
     * @date 2018/5/10 17:36
     */
    boolean isExistIpAddress(String ipAddress);

    /**
     * @param devIdList 需要重启设备id
     * @param imme 是否紧急命令
     * @return
     * @Description: 重启设备
     * <AUTHOR>
     * @date 2018/5/11 17:18
     */
    List<Long> rebootDevice(List<String> devIdList, boolean imme);

    /**
     * @param sn 设备sn
     * @param actionType 清空动作类型 -1111同步清空，-1112手动清空 add by max 20181102
     * @return
     * @Description: 删除设备待执行命令
     * <AUTHOR>
     * @date 2018/5/15 17:23
     */
    void clearCmdCache(String sn, String... actionType);

    /**
     * @param devIds 设备ID
     * @return
     * @Description: 根据设备ID批量获取设备信息，并对设备进行排序（把父设备提取出来，并放到列表前面做优先处理）
     * <AUTHOR>
     * @date 2018/5/16 11:50
     */
    List<AccDeviceItem> getItemByIdsAndMoveUpParentDev(String devIds);

    /**
     * @param devItem 设备
     * @param timeZone 时区
     * @return
     * @Description: 设置设备时区
     * <AUTHOR>
     * @date 2018/5/16 17:37
     */
    void setDeviceTimeZoneToDev(AccDeviceItem devItem, String timeZone);

    /**
     * @param dev 设备
     * @param accDSTimeItem 夏令时
     * @return
     * @Description: 保存夏令时
     * <AUTHOR>
     * @date 2018/5/18 14:23
     */
    void saveDSTime(AccDeviceItem dev, AccDSTimeItem accDSTimeItem);

    /**
     * @param devId 设备id
     * @param comAddr rs485地址
     * @return
     * @Description: 修改设备RS485地址
     * <AUTHOR>
     * @date 2018/5/21 11:51
     */
    Long updateRs485Addr(String devId, String comAddr);

    /**
     * @param devId 设备id
     * @param currentMode 当前设备连接方式
     * @param netConnectMode 修改连接方式
     * @param wirelessSSID wifi帐号
     * @param wirelessKey wifi密码
     * @return
     * @Description: 测试信号连接是否正常
     * <AUTHOR>
     * @date 2018/5/21 19:44
     */
    Long switchNetWorkTest(String devId, String currentMode, String netConnectMode, String wirelessSSID,
        String wirelessKey);

    /**
     * @param dev 设备item
     * @param accDSTimeItem 夏令时item
     * @param type 操作类型
     * @return
     * @Description: 设置或删除夏令时
     * <AUTHOR>
     * @date 2018/5/22 9:37
     */
    void setOrDelDST(AccDeviceItem dev, AccDSTimeItem accDSTimeItem, String type);

    /**
     * @param cmdId 命令id
     * @param timeout 超时时间
     * @return
     * @Description: 根据命令id获取命令返回值
     * <AUTHOR>
     * @date 2018/5/22 15:18
     */
    Map<String, String> getCmdResultById(Long cmdId, int timeout);

    /**
     * @param devId
     * @param netConnectMode
     * @param wirelessSSID
     * @param wirelessKey
     * @return
     * @Description: 切换设备网络连接方式
     * <AUTHOR>
     * @date 2018/5/22 17:55
     */
    Long updateNetConnectMode(String devId, String netConnectMode, String wirelessSSID, String wirelessKey);

    /**
     * @param item 添加设备item
     * @return
     * @Description: 添加跨网段push设备
     * <AUTHOR>
     * @date 2018/5/24 14:35
     */
    void grantAuthority(AccSearchAddDeviceItem item);

    /**
     * @param ids 夏令时ids
     * @return
     * @Description: 获取被使用夏令时个数
     * <AUTHOR>
     * @date 2018/5/28 10:49
     */
    int getCountByDSTimeId(String ids);

    /**
     * @param devSn
     * @return
     * @Description: 获取redis中虚拟待授权子设备信息
     * <AUTHOR>
     * @date 2018/5/29 13:45
     */
    String getDeviceAuthorize(String devSn);

    /**
     * @param devId 设备id
     * @return
     * @Description: 查询设备授权列表
     * <AUTHOR>
     * @date 2018/5/29 13:46
     */
    Long queryAuthorizeListFromDev(String devId);

    /**
     * @param accDeviceItem 设备item
     * @return
     * @Description: 更新设备信息
     * <AUTHOR>
     * @date 2018/5/29 17:25
     */
    void updateItemByParam(AccDeviceItem accDeviceItem);

    /**
     * @param sessionId
     * @param machineType
     * @return
     * @Description: 根据用户以及machineType获取设备
     * <AUTHOR>
     * @date 2018/5/29 18:00
     */
    List<AccDeviceItem> getByMachineTypeAndAuth(String sessionId, Short machineType);

    /**
     * @param sn
     * @param data
     * @return
     * @Description: 判断是否是登记机
     * <AUTHOR>
     * @date 2018/6/12 11:23
     */
    boolean isRegistrationDevice(String sn, String data);

    /**
     * @param id
     * @param parentDevId
     * @param webServerURL
     * @return
     * @Description: 修改主设备
     * <AUTHOR>
     * @date 2018/6/15 9:23
     */
    Long configParentDevice(String id, String parentDevId, String webServerURL);

    /**
     * @param optionMap
     * @return
     * @Description: 更新设备上传参数
     * <AUTHOR>
     * @date 2018/6/19 9:56
     */
    void updateDevOptions(Map<String, String> optionMap);

    /**
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     * @Description: 根据登录用户权限过滤显示设备
     * <AUTHOR>
     * @date 2018/6/20 10:43
     */
    Pager loadPagerByAuthFilter(String sessionId, AccDeviceItem condition, int pageNo, int pageSize);

    /**
     * @param *
     * @param
     * @return
     * @Description: push设备许可判断
     * <AUTHOR>
     * @date 2018/6/22 10:48
     */
    ZKResultMsg validPushDevCount(String sn, String machineType);

    /**
     * @param devCount
     * @param doorCount
     * @return
     * @Description: 校验授权IR9000子设备的许可数量
     * <AUTHOR>
     * @date 2018/6/22 11:15
     */
    ZKResultMsg validChildDevCount(int devCount, int doorCount);

    /**
     * @param devId
     * @param dataType
     * @return
     * @Description: 从设备获取人员信息
     * <AUTHOR>
     * @date 2018/6/26 14:56
     */
    Map<String, String> getPersonInfoFromDev(String devId, String dataType);

    /**
     * 根据当前登录用户获取到该用户下的区域ids
     *
     * @param sessionId
     * @return
     * <AUTHOR>
     * @date 2018/6/28 14:56
     */
    String getAreaIdsByAuthFilter(String sessionId);

    /**
     * @param cmdIdList
     * @return
     * @Description: 处理redis中的Account数据
     * <AUTHOR>
     * @date 2018/6/27 17:56
     */
    void dealAccountDataFromRedis(List<String> cmdIdList);

    /**
     * @param key
     * @return
     * @Description: 根据cmdId获取查询数据
     * <AUTHOR>
     * @date 2018/6/28 11:54
     */
    String getQueryData(String key);

    /**
     * @param key
     * @return
     * @Description: 删除查询数据
     * <AUTHOR>
     * @date 2018/6/28 11:58
     */
    void delQueryData(String key);

    /**
     * @param devId
     * @return
     * @Description: inbio5搜索WIFI列表
     * <AUTHOR>
     * @date 2018/7/9 9:25
     */
    Map<String, String> searchWifiListFromDev(String devId);

    /**
     * @param cmdId
     * @return
     * @Description: 获取wifi列表
     * <AUTHOR>
     * @date 2018/7/9 9:46
     */
    ZKResultMsg getWifiList(String cmdId);

    /**
     * @param accAuthorizeChildDevItem
     * @return
     * @Description: 获取设备授权列表信息
     * <AUTHOR>
     * @date 2018/7/9 20:04
     */
    List<AccAuthorizeChildDevItem> getAuthorizeChildDevList(AccAuthorizeChildDevItem accAuthorizeChildDevItem);

    /**
     * @param devId
     * @param devSns
     * @return
     * @Description: 授权子设备
     * <AUTHOR>
     * @date 2018/7/10 11:08
     */
    ZKResultMsg authorizeChildDevice(String devId, String devSns);

    /**
     * @param devId
     * @param parentDevId
     * @param serverAddress
     * @return
     * @Description: 测试连接服务器是否正常
     * <AUTHOR>
     * @date 2018/7/12 8:54
     */
    ZKResultMsg checkServerConnection(String devId, String parentDevId, String serverAddress);

    /**
     * @param id
     * @return
     * @Description: 子设备切换成主设备
     * <AUTHOR>
     * @date 2018/7/16 9:35
     */
    void changeParentDevice(String id);

    /**
     * @param id
     * @param parentDevId
     * @return
     * @Description: 变更主设备
     * <AUTHOR>
     * @date 2018/7/16 11:06
     */
    void updateParentDevice(String id, String parentDevId);

    /**
     * 开启或关闭韦根测试根据设备
     *
     * @param devId
     * @param openOrClose
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/27 13:46
     */
    String openWgFmtTestForDevice(String devId, boolean openOrClose);

    /**
     * 过滤韦根测试不支持的设备ID
     *
     * @return
     */
    String getAllWgFmtTestFilterId();

    /**
     * @param sn
     * @return
     * @Description: 获取子设备状态
     * <AUTHOR>
     * @date 2018/7/27 19:11
     */
    void getChildDevStatus(String sn);

    /**
     * 卡格式测试双列表
     *
     * @param condition
     * @param page
     * @param size
     * @return
     */
    Pager getSelectDeviceItemByPage(Acc4PersDeviceSelectItem condition, int page, int size);

    /**
     * 获取pull设备参数以及判断许可点数
     *
     * @param item
     * @return
     */
    ZKResultMsg getDevInfo(AccDeviceParamItem item);

    /**
     * 添加pull设备
     *
     * @param item
     * @param devInfo
     * @param tempInfo
     */
    void addPullDevice(AccDeviceItem item, String devInfo, AccSearchAddDeviceItem tempInfo);

    /**
     * 根据用户权限获取设备，选择设备调用（过滤IR9000）
     *
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     */
    Pager getItemByAuthFilter(String sessionId, AccSelectDeviceItem condition, int pageNo, int pageSize);

    /**
     * @param parentDevId
     * @return
     * @Description: 获取子设备列表
     * @date 2018/8/15 10:39
     */
    List<AccDeviceItem> getChildDevList(String parentDevId);

    /**
     * @param devSn
     * @return
     * @Description: 根据sn获取设备信息(优先从缓存查询)
     * <AUTHOR>
     * @date 2018/8/17 14:39
     */
    AccQueryDeviceItem getQueryItemBySn(String devSn);

    /**
     * @param filterAreaIds
     * @return
     * @Description: 根据区域获取设备信息
     * <AUTHOR>
     * @date 2018/8/23 11:11
     */
    List<AccQueryDeviceItem> getQueryItemsByAuth(String filterAreaIds);

    /**
     * @param devIds
     * @param file
     * @param host
     * @param port
     * @return
     * @Description: 下发固件升级命令
     * <AUTHOR>
     * @date 2018/8/29 10:16
     */
    List<Long> upgradeFirmware(String devIds, File file, String host, int port);

    /**
     * @param sn
     * @param optType
     * @return
     * @Description: 获取设备参数
     * <AUTHOR>
     * @date 2018/8/29 11:11
     */
    List<Long> getOptFromDev(String sn, int optType);

    /**
     * @return
     * @Description: 获取软件中设备数量
     * <AUTHOR>
     * @date 2018/9/4 15:45
     */
    long getDevicesCount();

    /**
     * @param page
     * @param pageSize
     * @return
     * @Description: 获取设备信息上传到云服务
     * <AUTHOR>
     * @date 2018/9/6 14:05
     */
    List<String> getUploadCloudDevices(int page, int pageSize);

    /**
     * 修改设备通讯密码
     *
     * @param deviceSn
     * @param newCommPwd
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年11月2日 下午4:10:40
     */
    void updateAdmsDeviecPwd(String deviceSn, String newCommPwd);

    /**
     * 修改设备ip
     *
     * @param deviceSn
     * @param ipAddress
     * @param subnetMask
     * @param gateway
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年11月2日 下午4:44:40
     */
    void updateAdmsDevIpAddr(String deviceSn, String ipAddress, String subnetMask, String gateway);

    /**
     * 保存门禁设备
     *
     * @param item
     * @return
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年11月2日 下午5:05:57
     */
    AccDeviceItem saveAccDevice(AccDeviceItem item);

    /**
     * 获取设备所有门禁规则
     *
     * @param ids
     * @return
     */
    List<List<String>> queryDevRule(String ids);

    /**
     * 清除设备管理员
     *
     * @param devIds
     * @return
     * <AUTHOR>
     * @since 2018年12月3日 下午5:59:20
     */
    ZKResultMsg clearAdministrator(String devIds);

    /**
     * 设置一体机出入状态
     *
     * @param devId
     * @param devIOState
     * @return
     * <AUTHOR>
     * @since 2018年12月5日 下午5:14:48
     */
    Long setDevIOState(String devId, String devIOState);

    /**
     * 根据设备的通信类型获取设备id
     *
     * @param commTypeList
     * @return
     */
    List<String> getDevIdsByCommType(List<Short> commTypeList);

    /**
     * @param items
     * @Description:门禁设备数据迁移
     * <AUTHOR>
     * @since 2018年12月12日 下午5:33:00
     */
    void handlerTransfer(List<AccDeviceItem> items);

    /**
     * 校验设备项目机参数
     *
     * @param regDeviceType
     * @return
     */
    Boolean validDevRegDeviceType(String regDeviceType);

    /**
     * 获取远程登记命令id
     *
     * @param devId
     * @param templateType
     * @param templateNo
     * @param pin
     * @return
     * <AUTHOR>
     * @since 2018年12月10日 上午11:27:23
     */
    Long remoteRegistration(String devId, int templateType, int templateNo, String pin);

    /**
     * 远程登记生物模版
     *
     * @param personPin
     * @param bioType
     * @param sendTime
     * @return
     * <AUTHOR>
     * @since 2018年12月10日 下午2:15:03
     */
    ZKResultMsg getRemoteTemplate(String personPin, String bioType, long sendTime);

    /**
     * 功能描述:根据读头id获取设备sn
     *
     * @return
     * <AUTHOR>
     * @Param [accReaderId]
     * @since 2019-04-16 9:39
     */
    String getDevSnByReaderId(String accReaderId);

    /**
     * 功能描述:修改adms中的rs485地址
     *
     * @return
     * <AUTHOR>
     * @Param [sn, comAddr]
     * @since 2019-04-16 14:32
     */
    void updateAdmsComAddress(String sn, String comAddr);

    /**
     * 功能描述: TCP读头读取身份证信息获取
     *
     * @return
     * <AUTHOR>
     * @Param [cmdId, type]
     * @since 2019-05-08 11:22
     */
    ZKResultMsg getReadIDCardInfo(Long cmdId, String type);

    /**
     * 从二号项目迁移代码：获取设备最后推送时间
     *
     * @return
     * @auther zbx.zhong
     * @date 2019/6/19 15:44
     */
    Long getLastPushTime();

    /**
     * 从二号项目迁移代码：根据最后推送时间获取设备信息上传到云服务
     *
     * @param lastUpdate
     * @return
     * @auther zbx.zhong
     * @date 2019/6/20 15:56
     */
    List<String> getUploadCloudDevicesByLastPushTime(Date lastUpdate);

    /**
     * 从二号项目迁移代码：设置设备最后推送时间
     *
     * @return
     * @auther zbx.zhong
     * @date 2019/6/20 15:46
     */
    void setLastPushTime();

    /**
     * 根据sn获取门禁设备信息--巡更数据迁移
     *
     * @return
     * <AUTHOR>
     * @Param [sns]
     * @since 2019-08-15 18:01
     */
    List<AccDeviceItem> getItemsByDevSnIn(List<String> sns);

    /**
     * 选设备双列表控件获取数据
     *
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager getDeviceSelectItem(String sessionId, AccDeviceSelectItem condition, int pageNo, int pageSize);

    /**
     * 获取设备人员表所有数据
     *
     * @param accDeviceItem
     * @return
     */
    Long getAllPersonFromDev(AccDeviceItem accDeviceItem);

    /**
     * 获取设备人员扩展表所有数据
     *
     * @param accDeviceItem
     * @return
     */
    Long getAllPersonExtInfoFromDev(AccDeviceItem accDeviceItem);

    /**
     * 获取设备多卡表所有数据
     *
     * @param accDeviceItem
     * @return
     */
    Long getAllPersonCardFromDev(AccDeviceItem accDeviceItem);

    /**
     * 获取设备的信息：支持的模板，状态等
     *
     * @param ids
     * @return java.util.List<java.util.List < java.lang.String>>
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-05-25 14:46
     */
    List<List<String>> queryDevInfo(String ids);

    /**
     * 修改设备扩展参数
     *
     * @param params
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2020/6/3 18:15
     */
    ZKResultMsg updateDevExtendParam(Map<String, String> params);

    /**
     * 更新设备ip地址到其他模块
     *
     * @param sn:设备序列号
     * @param ipAddress:设备ip地址
     * @return void
     * <AUTHOR>
     * @date 2020-11-02 17:10
     * @since 1.0.0
     */
    void updateDevIpAddrForOtherModule(String sn, String ipAddress);

    /**
     * 根据sn获取设备
     *
     * @param sns:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem>
     * @throws @date 2021-01-22 18:04
     * <AUTHOR>
     * @since 1.0.0
     */
    List<AccDeviceItem> getItemBySns(String sns);

    /**
     * 根据夏令时id获取门禁设备信息
     *
     * @param dsTimeId:夏令时id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem>
     * <AUTHOR>
     * @date 2021-01-19 13:55
     * @since 1.0.0
     */
    List<AccDeviceItem> getItemsByDSTimeId(List<String> dsTimeId);

    /**
     * 根据设备id集合获取设备Item Map <DeviceId, AccDeviceItem>
     *
     * @param devIdList:设备id集合
     * @return java.util.Map<java.lang.String, com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem>
     * <AUTHOR>
     * @date 2021-01-20 15:33
     * @since 1.0.0
     */
    Map<String, AccDeviceItem> getItemsMapByDevIds(List<String> devIdList);

    /**
     * 根据设备序列号获取父设备
     *
     * @param deviceSn:设备序列号
     * @return com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem
     * <AUTHOR>
     * @date 2021-01-26 14:00
     * @since 1.0.0
     */
    AccDeviceItem getParentDeviceByDevSn(String deviceSn);

    /**
     * 根据门id查询设备信息
     *
     * @param doorIds:门id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem>
     * <AUTHOR>
     * @date 2021-01-27 16:30
     * @since 1.0.0
     */
    List<AccDeviceItem> getDevItemByDoorIds(List<String> doorIds);

    /**
     * 根据门id查询设备信息
     *
     * @param doorId:门id
     * @return com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem
     * <AUTHOR>
     * @date 2021-01-27 16:35
     * @since 1.0.0
     */
    AccDeviceItem getDevItemByDoorId(String doorId);

    /**
     * 根据条件查询
     *
     * @param condition:查询条件
     * @return java.util.List<?>
     * <AUTHOR>
     * @date 2021-01-29 11:42
     * @since 1.0.0
     */
    List<?> getItemsByCondition(BaseItem condition);

    /**
     * 获取所有的门禁设备
     *
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem>
     * <AUTHOR>
     * @date 2021-02-03 9:55
     * @since 1.0.0
     */
    List<AccDeviceItem> getAllDeviceItems();

    /**
     * 根据读头id查询设备信息
     *
     * @param readerIds:读头id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem>
     * <AUTHOR>
     * @date 2021-02-07 15:42
     * @since 1.0.0
     */
    List<AccDeviceItem> getDevItemByReaderIds(List<String> readerIds);

    /**
     * 通知其他模块同步数据
     *
     * @param devIds:设备id
     * @return void
     * <AUTHOR>
     * @date 2021-03-16 18:56
     * @since 1.0.0
     */
    void informOtherModuleSyncData(List<String> devIds);

    /**
     * 根据菜单code判断是否有菜单权限
     *
     * @param menuCode:
     * @return boolean
     * @throws @date 2021-04-19 16:33
     * <AUTHOR>
     * @since 1.0.0
     */
    boolean checkPermissionByMenuCode(String menuCode);

    /**
     * 根据菜单permission和sessionId判断是否有菜单权限
     *
     * @param sessionId:
     * @param permission:
     * @return boolean
     * @throws @date 2021-05-20 16:23
     * <AUTHOR>
     * @since 1.0.0
     */
    boolean checkPermission(String sessionId, String permission);

    /**
     * 更新AccQueryDeviceItem对象的门信息
     *
     * @param item
     */
    void updateDoorByQueryItem(AccQueryDeviceItem item);

    /**
     * 更新AccQueryDeviceItem对象的辅助输入信息
     *
     * @param item
     */
    void updateAuxInByQueryItem(AccQueryDeviceItem item);

    /**
     * 更新AccQueryDeviceItem对象的辅助输出信息
     *
     * @param item
     */
    void updateAuxOutByQueryItem(AccQueryDeviceItem item);

    /**
     * 通过sn更新设备缓存信息以及门、辅助输入、辅助输出
     *
     * @param sn
     */
    void updateDevInfoWithDoorAndAuxBySn(String sn);

    /**
     * 更新缓存设备信息
     *
     * @param accDeviceItem:
     * @return com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem
     * @throws @date 2021-11-22 15:06
     * <AUTHOR>
     * @since 1.0.0
     */
    AccQueryDeviceItem updateCacheDeviceInfo(AccDeviceItem accDeviceItem);

    /**
     * 获取生物模板版本
     *
     * @param multiBioVersionAry:
     * @param bioType:
     * @return java.lang.String
     * @throws @date 2022-06-13 11:13
     * <AUTHOR>
     * @since 1.0.0
     */
    String getMultiBioVersion(String[] multiBioVersionAry, Short bioType);

    /**
     * 通过id判断是否设置了ntp服务
     *
     * @param id:
     * @return boolean
     * @throws @date 2022-07-26 14:14
     * <AUTHOR>
     * @since 1.0.0
     */
    boolean getNtpStatusById(String id);

    /**
     * 设置ntp服务地址
     *
     * @param id:
     * @param status:
     * @param serverAddress:
     * @return void
     * @throws @date 2022-07-26 14:31
     * <AUTHOR>
     * @since 1.0.0
     */
    void setNtpServer(String id, Short status, String serverAddress);

    /**
     * 保存要替换的设备sn,删除被替换设备的缓存数据
     *
     * @param devId :
     * @param newSn :
     * @return void
     * @throws @date 2022-08-10 15:33
     * <AUTHOR>
     * @since 1.0.0
     */
    void replaceDevInfo(String devId, String newSn);

    /**
     * 校验填写的sn是否已被添加或者是否已被设置为替换的sn
     *
     * @param newSn:
     * @return boolean
     * @throws @date 2022-08-10 14:57
     * <AUTHOR>
     * @since 1.0.0
     */
    boolean validSn(String newSn);

    /**
     * 根据用户权限过滤，获取选设备列表
     *
     * @param sessionId:
     * @param condition:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2022-10-14 14:35
     * @since 1.0.0
     */
    Pager loadInterlockSelectDeviceByAuthFilter(String sessionId, AccInterlockSelectDeviceItem condition, int pageNo,
        int pageSize);

    /**
     * 获取符合条件的远程登记设备数据
     *
     * @param optionNames:
     * @param sessionId:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2022-10-18 17:59
     * @since 1.0.0
     */
    ZKResultMsg getRemoteRegistDev(String optionNames, String sessionId);

    /**
     * 根据设备sn获取设备所支持的生物模板的版本信息
     *
     * @param sn:
     * @return java.util.Map<java.lang.Short, java.lang.String>
     * <AUTHOR>
     * @date 2022-12-09 16:43
     * @since 1.0.0
     */
    Map<Short, String> getBioTemplateVersionBySn(String sn);

    /**
     * 根据条件获取模板版本
     *
     * @param sn:
     * @param devOptionMap:
     * @param optionName:
     * @param bioType:
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022-12-09 17:56
     * @since 1.0.0
     */
    String getTemplateVersion(String sn, Map<String, String> devOptionMap, String optionName, Short bioType);

    /**
     * 根据sn获取设备命令数
     *
     * @param sn:
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2023-03-01 14:39
     * @since 1.0.0
     */
    Long getCmdCount(String sn);

    /**
     * 根据redis中的数据查找在线和离线状态的设备sn信息
     * 
     * @return java.util.Map<java.lang.Short,java.util.Set<java.lang.String>>
     * <AUTHOR>
     * @date 2023-04-07 9:49
     * @since 1.0.0
     */
    Map<Short, Set<String>> getDevSnsByRedis();

    /**
     * 更新ADMS设备参数
     *
     * @param item:
     * @return void
     * <AUTHOR>
     * @date 2023-05-30 16:09
     * @since 1.0.0
     */
    void updateAdmsDevOption(AccDeviceItem item);

    /**
     * 获取支持韦根格式的设备
     *
     * @return java.util.List<com.zkteco.zkbiosecurity.base.bean.SelectItem>
     * <AUTHOR>
     * @throws
     * @date 2023-06-01 14:06
     * @since 1.0.0
     */
    List<SelectItem> getSupportWiegandFmtDevices();

    /**
     * 根据指定sn获取设备事件信息
     *
     * @param sn:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDeviceEventItem>
     * <AUTHOR>
     * @throws
     * @date 2023-08-16 11:49
     * @since 1.0.0
     */
    List<AccDeviceEventItem> getDevEventBySn(String sn);

    /**
     * 根据设备ids清除设备命令
     *
     * @param ids:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2023-09-26 17:35
     * @since 1.0.0
     */
    void clearCmdCacheByIds(String ids);

    /**
     * 校验NVR用户名密码是否正确
     *
     * @param userName :
     * @param userPassword :
     * @param ipAddress
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * @throws
     * <AUTHOR>
     * @date 2024-01-19 11:06
     * @since 1.0.0
     */
    ZKResultMsg validIvsUerInfo(String userName, String userPassword, String ipAddress);

    /**
     * 判断设备是否支持best协议
     *
     * @param sn
     * @return
     */
    boolean isBestDevBySn(String sn);

    /**
     * 添加best设备
     *
     * @param deviceJsonStr
     * @return
     */
    boolean setBestDevice(String deviceJsonStr);

    String getParentSnByChildSns(List<String> snList);

    List<SelectItem> getNamesByfileTypes(List<String> fileTypes);

    /**
     * 设备上传资源文件
     *
     * @param item
     * @return
     */
    boolean uploadAdResourceFile(Acc4UploadAdFileItem item);

    /**
     * 获取设备从属的摄像头
     *
     * @param devSn
     * @return
     */
    List<SelectItem> getDevLinkIPC(String devSn);

    /**
     * 获取设备ntp信息
     *
     * @param id:
     * @return java.lang.String
     * <AUTHOR>
     * @throws
     * @date 2024-05-08 16:42
     * @since 1.0.0
     */
    String getDevNtpById(String id);

    /**
     * 设置扩展板缓存
     *
     * @param item
     */
    void updateExtDeviceCache(AccQueryDeviceItem item);

    /**
     * 设置人脸比对服务信息
     *
     * @param item
     */
    void setFaceVerifyServer(Acc4SetFaceVerifyServer item);
}