package com.zkteco.zkbiosecurity.acc.service;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.vo.AccAlarmMonitorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.List;
import java.util.Map;

/**
 * 报警监控
 */
public interface AccAlarmMonitorService {

    void sendAlarmMail(String subject, AccTransactionItem accTransactionItem, String Type);

    /**
     * 获取监控信息（100条）
     *
     * @return
     */
    List<AccAlarmMonitorItem> getAll();

    /**
     * 保存实体
     *
     * @param accAlarmMonitorItem
     * @return
     */
    AccAlarmMonitorItem saveItem(AccAlarmMonitorItem accAlarmMonitorItem);

    /**
     * 通过异常事件加入
     *
     * @param accTransaction
     */
    boolean addByTransaction(AccTransactionItem accTransaction);

    /**
     * 获取列表格式的数据
     *
     * @param newMess 是否是新消息
     * @return
     */
    JSONObject getAllJson(boolean newMess);

    /**
     * 推送最多100条消息给websocket
     *
     * @param newMess 标记是否有新消息
     */
    void sendAllAlarmMonitorWS(boolean newMess);

    /**
     * 确认警报
     *
     * @param ids
     * @return
     */
    ZKResultMsg checkAlarmMonitor(String ids);

    /**
     * 发送短信
     *
     * @param accTransactionItem
     * @param type
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2019-11-12 18:03
     */
    void sendAlarmSMS(AccTransactionItem accTransactionItem, String type);

    /**
     * 根据条件获取报警事件
     *
     * @param item
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccAlarmMonitorItem>
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-08-03 17:46
     */
    List<AccAlarmMonitorItem> getAlarmEventByCondition(AccAlarmMonitorItem item);

    /**
     * 组装APP需要的数据
     *
     * @param accAlarmMonitorItem
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-08-03 17:50
     */
    JSONObject createAppData(AccAlarmMonitorItem accAlarmMonitorItem);

    /**
     * 查找在某个报警事件之前的未确认报警事件
     *
     * @param id 报警事件id
     * @param length 指定时间之前的数量(具体返回数量为 该事件同一时间发生的时间数 + 之前的时间数)
     * @return
     */
    List<AccAlarmMonitorItem> getUncheckAlarmListBefore(String id, int length);

    /**
     * 获取事件编号之后发生的事件
     *
     * @return
     */
    List<AccAlarmMonitorItem> getDataByEventNum(Map filter);

    /**
     * 获取某个eventNum之后的分页数据
     *
     * @param page
     * @param target
     * @return
     */
    List<AccAlarmMonitorItem> getNotAcknowledgedItemByPage(Map<String, String> filter, int page, long target);

    /**
     * 将list转换成列表的json数据
     *
     * @param list
     * @return
     */
    JSONObject createJsonFromItems(List<AccAlarmMonitorItem> list);

    /**
     * 获取分析数据
     *
     * @return
     */
    JSONObject getAnalysis();

    /**
     * 根据id获取事件消息：事件类型<门名称>
     *
     * @param id
     * @return
     */
    String getDescription(String id);

    /**
     * 修改状态
     *
     * @param id
     * @param status
     * @param acknowledgement
     * @return
     */
    ZKResultMsg changeStatus(String id, Short status, String acknowledgement);

    /**
     * 发送状态更改的通知
     *
     * @param id
     * @param status
     * @param acknowledgement
     * @param emails
     * @return
     */
    ZKResultMsg sendAlarmStatusEmail(String id, Short status, String acknowledgement, String emails);

    /**
     * 通过id获取对象
     *
     * @param id
     * @return
     */
    AccAlarmMonitorItem getById(String id);

    /**
     * 分页获取数据
     *
     * @param condition :
     * @param page :
     * @param size :
     * @param limit
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * @throws
     * <AUTHOR>
     * @date 2022-06-17 9:51
     * @since 1.0.0
     */
    Pager getHistoryByPage(BaseItem condition, int page, int size, long limit);

    /**
     * 获取值
     *
     * @param targetClass
     * @param condition
     * @param beginIndex
     * @param endIndex
     * @return
     */
    List getHistoryData(Class targetClass, BaseItem condition, int beginIndex, int endIndex);

    /**
     * 清空历史
     *
     * @return
     */
    ZKResultMsg clearHistoryData();

    /**
     * 获取初始化的60条信息
     * 
     * @param filters:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccAlarmMonitorItem>
     * <AUTHOR>
     * @throws
     * @date 2022-06-17 14:56
     * @since 1.0.0
     */
    List<AccAlarmMonitorItem> getFirstPage(Map filters);

    /**
     * 获取分页信息
     *
     * @param condition
     * @param page
     * @param size
     * @param limit
     * @return
     */
    Pager getPager(String sessionId, AccAlarmMonitorItem condition, int page, int size, long limit);

    /**
     * 获取导出数据
     * 
     * @param targetClass:
     * @param condition:
     * @param beginIndex:
     * @param endIndex:
     * @return java.util.List<?>
     * <AUTHOR>
     * @throws
     * @date 2022-06-20 17:20
     * @since 1.0.0
     */
    List<?> getItemData(Class targetClass, BaseItem condition, int beginIndex, int endIndex);

    /**
     * 根据用户权限获取数据分析信息
     * 
     * @param sessionId:
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @throws
     * @date 2022-07-21 13:50
     * @since 1.0.0
     */
    JSONObject getAnalysisByAuthFilter(String sessionId);

    /**
     * 告警数据 4种类型告警的数量，和近10条数据
     *
     * @param sessionId:
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date  2024/3/7 14:05
     * @since 1.0.0
     */
    JSONObject getAccAlarmData(String sessionId);
}
