/**
 * File Name: AccVerifyModeRule
 * Created by GenerationTools on 2018-03-14 下午03:02
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccVerifyModeRuleItem;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.List;

/**
 * 对应百傲瑞达 AccVerifyModeRuleService
 * <AUTHOR>
 * @date:	2018-03-14 下午03:02
 * @version v1.0
 */
public interface AccVerifyModeRuleService extends BaseService {

	/**
	 * 保存item实体，一般会有复杂业务逻辑
	 * @param item
	 */
	AccVerifyModeRuleItem saveItem(AccVerifyModeRuleItem item);

	/**
	 * 根据条件查询
	 * @param condition
	 * @return
	 */
	List<AccVerifyModeRuleItem> getByCondition(AccVerifyModeRuleItem condition);

	/**
	 * 根据ID查询
	 * @param id
	 * @return
	 */
	AccVerifyModeRuleItem getItemById(String id);

	/**
	 * 判断名称是否有效/允许
	 * @author: mingfa.zheng
	 * @date: 2018/3/15 16:09
	 * @return:
	 */
	AccVerifyModeRuleItem getItemByName(String name);

	/**
	 * 获取还未设置验证方式规则的时间段
	 *
	 * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
	 * @since 2016年9月23日 上午10:51:55
	 * @param id 时间段id
	 * @return
	 */
	ZKResultMsg getTimeSegJSONWithoutRule(String id);

	/***
	 * 获取系统中支持该功能的设备的验证方式列表
	 *
	 * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
	 * @since 2016年10月14日 上午8:58:47
	 * @return
	 */
	ZKResultMsg getVerifyMode();

	/**
	 * 验证方式规则人员组： 添加人员
	 * @author: mingfa.zheng
	 * @date: 2018/3/21 16:41
	 * @return:
	 */
	void addPerson(String verifyModeRulePersonGroupId, List<String> personIdList);

	/**
	 * 验证方式规则人员组： 删除人员
	 * @author: mingfa.zheng
	 * @date: 2018/3/21 16:41
	 * @return:
	 */
	void delPerson(String verifyModeRuleGroupId,String personIds);

	/**
	 * 验证方式规则: 添加门
	 * @author: mingfa.zheng
	 * @date: 2018/3/23 16:16 
	 * @return: 
	 */
	void addDoor(String verifyModeRuleId, List<String> doorIdList);

	/**
	 * 验证方式规则: 删除门
	 * @author: mingfa.zheng
	 * @date: 2018/3/23 16:16
	 * @return:
	 */
	void delDoor(String verifyModeRuleId, String doorIds);

	/**
	 * 获取需要过滤的门id，包括已经设置过规则的门和不支持该功能的门
	 *
	 * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
	 * @since 2016年10月14日 上午10:08:43
	 * @return
	 */
	String getFilterDoorId(String id);

	/**
	 * 获取验证方式规则人员组已经存在的人员ID
	 * @author: mingfa.zheng
	 * @date: 2018/4/24 17:58
	 * @return:
	 */
	List<String> getExistPersonIds();

	/**
	 * 同步验证方式规则到设备
	 * @author: mingfa.zheng
	 * @date: 2018/5/30 19:58
	 * @return:
	 */
	void syncVerifyModeRuleToDev(String deviceId);

	/**
	 * 获取还未设置验证方式规则的时间段下拉列表数据
	 *
	 * @param id:时间段id
	 * @return java.util.List<com.zkteco.zkbiosecurity.base.bean.SelectItem>
	 * <AUTHOR>
	 * @date 2021-07-23 15:54
	 * @since 1.0.0
	 */
	List<SelectItem> getTimeSegListWithoutRule(String id);
}