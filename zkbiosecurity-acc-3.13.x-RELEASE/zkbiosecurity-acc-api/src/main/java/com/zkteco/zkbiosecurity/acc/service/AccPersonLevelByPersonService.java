/**
 * File Name: AccLevel Created by GenerationTools on 2018-03-02 下午02:15 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelByPersonItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 对应百傲瑞达 AccLevelService
 * 
 * <AUTHOR>
 * @date: 2018-03-02 下午02:15
 * @version v1.0
 */
public interface AccPersonLevelByPersonService extends BaseService {

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AccPersonLevelByPersonItem> getByCondition(AccPersonLevelByPersonItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    AccPersonLevelByPersonItem getItemById(String id);

    /**
     * @Description: 导出数据查询
     * <AUTHOR>
     * @date 2018/5/14 15:40
     * @param accPersonLevelItem
     *            导出数据类型
     * @param beginIndex
     *            开始索引
     * @param endIndex
     *            结束索引
     * @return
     */
    List<AccPersonLevelItem> getExportItemList(AccPersonLevelItem accPersonLevelItem, int beginIndex, int endIndex);

    /**
     * @Description: 根据
     * <AUTHOR>
     * @date 2018/5/14 16:24
     * @param *
     * @param accPersonLevelItemList
     * @return
     */
    Map<String, String> getAuthAreaByPersonLevel(List<AccPersonLevelItem> accPersonLevelItemList);

    Pager loadPagerByAuthFilter(String sessionId, AccPersonLevelByPersonItem codition, int pageNo, int pageSize);

    /**
     * 根据sessionId 和 人员ids 获取人员的权限组信息,并做下发；
     * 
     * @param sessionId:
     * @param personIds:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws @date
     *             2021-06-10 15:55
     * @since 1.0.0
     */
    ZKResultMsg syncPersonLevel(String sessionId, String personIds);
}