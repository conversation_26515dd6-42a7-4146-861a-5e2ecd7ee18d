package com.zkteco.zkbiosecurity.acc.service;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.vo.AccQueryDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccSearchAddDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;

/**
 * <AUTHOR>
 * @date 2021/2/1 16:33
 * @since 1.0.0
 */
public interface AccCacheService {

    /**
     * 获取身份证信息
     *
     * @param key:
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date 2021-02-01 16:36
     * @since 1.0.0
     */
    JSONObject getIdentityCardInfo(String key);

    /**
     * 置实时事件到客户端中，API实时监控接口使用
     *
     * @param accTransactionItemStr:事件信息
     * @return void
     * <AUTHOR>
     * @date 2021-02-01 16:34
     * @since 1.0.0
     */
    void setTransactionToClient(String accTransactionItemStr);

    /**
     * 存放设备最后一次事件记录，用于硬联动取前一条事件记录来判断触发点
     *
     * @param accTransaction:事件信息
     * @return void
     * <AUTHOR>
     * @date 2021-02-01 16:38
     * @since 1.0.0
     */
    void putLastRtLogToCache(AccTransactionItem accTransaction);

    /**
     * 事件记录入库数据保存
     *
     * @param data:
     * @return void
     * <AUTHOR>
     * @date 2021-02-01 16:39
     * @since 1.0.0
     */
    void putTransactionToDb(String data);

    /**
     * 获取设备最近上传记录
     *
     * @param sn:
     * @return com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem
     * <AUTHOR>
     * @date 2021-02-01 16:40
     * @since 1.0.0
     */
    AccTransactionItem getLastRtLogFromCache(String sn);

    /**
     * 获取临时设备信息
     *
     * @param sn:设备序列号
     * @return com.zkteco.zkbiosecurity.acc.vo.AccSearchAddDeviceItem
     * <AUTHOR>
     * @date 2021-02-03 14:47
     * @since 1.0.0
     */
    AccSearchAddDeviceItem getTempDevFromCache(String sn);

    /**
     * @Description: 存放临时设备信息
     * @param sn
     * @param item
     * @return
     */
    void putTempDevice2Cache(String sn, AccSearchAddDeviceItem item);

    /**
     * 获取门禁设备信息
     *
     * @param sn:设备序列号
     * @return com.zkteco.zkbiosecurity.acc.vo.AccQueryDeviceItem
     * <AUTHOR>
     * @date 2021-02-03 15:03
     * @since 1.0.0
     */
    AccQueryDeviceItem getDeviceInfo(String sn);

    /**
     * 设置门禁设备信息
     *
     * @param accQueryDeviceItem:
     * @return void
     * <AUTHOR>
     * @date 2021-02-03 15:04
     * @since 1.0.0
     */
    void putDeviceInfo(AccQueryDeviceItem accQueryDeviceItem);

    /**
     * 设置门禁设备参数信息
     *
     * @param sn:设备序列号
     * @param optionStr:参数
     * @return void
     * <AUTHOR>
     * @date 2021-02-03 15:06
     * @since 1.0.0
     */
    void putDeviceOptionInfo(String sn, String optionStr);

    /**
     * 获取门禁设备参数信息
     *
     * @param sn:设备序列号
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date 2021-02-05 9:27
     * @since 1.0.0
     */
    JSONObject getDeviceOptionInfo(String sn);

    /**
     * @Description: 获取设备授权信息
     * @param sn
     * @return
     */
    String getDeviceAuthorizeInfo(String sn);

    /**
     * 利用redis向指定主题发送消息
     * 
     * @param mess
     * @param topic
     */
    void sendMessageToTopic(String mess, String topic);
}
