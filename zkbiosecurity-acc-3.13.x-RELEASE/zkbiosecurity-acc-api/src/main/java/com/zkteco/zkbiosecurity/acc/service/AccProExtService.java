package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccLevelDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonOptItem;

import java.util.ArrayList;
import java.util.List;
/**
 * pro拓展业务
 *
 * <AUTHOR>
 * @date 2020-11-23 11:28
 * @since 1.0.0
 */
public interface AccProExtService {

    /**
     * 根据人员id填充威胁等级信息
     * 
     * @param accPersonOptItem
     * @param personId
     */
    default void setThreatLevel(AccPersonOptItem accPersonOptItem, String personId) {}

    default void setThreatLevel(List<AccPersonOptItem> accPersonOptItemList) {}

    /**
     * 下发权限组给设备
     * 
     * @param levelDoorItemList
     */
    default void setLevelToDev(List<AccLevelDoorItem> levelDoorItemList) {}

    /**
     * 删除权限组
     * 
     * @param levelId
     * @param doorIdList
     */
    default void delLevelDoorByParams(String levelId, List<String> doorIdList) {}

    /**
     * 删除验证方式组
     * 
     * @param personList
     */
    default void deleteAccPersonVerifyModeRuleByPersPersonIdIn(List<String> personList) {}

    /**
     * 通过人员id和设备id查询权限组相关信息
     * 
     * @param personIds
     * @param id
     * @return
     */
    default List<Object[]> getAccLevelDoorByPersonIdsAndDevId(List<String> personIds, String id) {
        return new ArrayList<>(4);
    }

    /**
     * 删除pro模块门禁数据
     * @param accDeviceItem
     */
    default void deleteProExtData(AccDeviceItem accDeviceItem) {}


    /**
     * 高级门禁根据事件编号获取联动触发事件名称
     * @param  eventNo
     * <AUTHOR>
     * @Date 2021/12/1 10:55
     * @return
     */
    default String getTriggerEventName(Integer eventNo) {return new String();}

    /**
    * 高级门禁根据设备sn,门磁状态，继电器状态判断是否满足全局互锁的条件
    * @param  sn
    * @param curDoorSensorState
    * @param curDoorRelayState
    * <AUTHOR>
    * @Date 2021/12/2 17:03
    * @return
    */
    default boolean checkSensorStateAndRelayState(String sn, String curDoorSensorState, String curDoorRelayState) {return false;}
}
