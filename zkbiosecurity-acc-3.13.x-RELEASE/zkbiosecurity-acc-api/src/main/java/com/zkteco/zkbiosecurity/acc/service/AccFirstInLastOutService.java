/**
 * <AUTHOR>
 * @date 2020/3/27 11:23
 */

package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;

import com.zkteco.zkbiosecurity.acc.vo.AccFirstInLastOutItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">caiyun.chen</a>
 * @version v1.0
 */
public interface AccFirstInLastOutService extends BaseService {
    /**
     * 保存first in last out
     *
     * <AUTHOR>
     * @param accTransactionItem
     * @return void
     * @date 2020/3/26 16:27
     */
    void saveFirstInLastOutHandle(AccTransactionItem accTransactionItem);

    /**
     * 删除所有数据
     *
     * <AUTHOR>
     * @param
     * @return void
     * @date 2020/3/26 17:50
     */
    void deleteAllData();

    /**
     * 获取导出数据
     *
     * <AUTHOR>
     * @param accFirstInLastOutItemClass, condition, beginIndex, endIndex
     * @return java.util.List<com.zkteco.zkbiosecurity.ele.vo.EleFirstInLastOutItem>
     * @date 2020/3/26 18:03
     */
    List<AccFirstInLastOutItem> getItemData(Class<AccFirstInLastOutItem> accFirstInLastOutItemClass, BaseItem condition,
        int beginIndex, int endIndex);

    /**
     * 获取导出数据
     *
     * @param sessionId:
     * @param condition:
     * @param beginIndex:
     * @param endIndex:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccFirstInLastOutItem>
     * <AUTHOR>
     * @date 2022-12-27 10:47
     * @since 1.0.0
     */
    List<AccFirstInLastOutItem> getExportItemData(String sessionId, AccFirstInLastOutItem condition, int beginIndex,
        int endIndex);

    /**
     * 根据用户权限加载事件报表
     * 
     * <AUTHOR>
     * @Param sessionId
     * @Param condition
     * @Param pageNo
     * @Param pageSize
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * @date 2020/8/5 11:36
     */
    Pager loadTransactionByAuthUserFilter(String sessionId, AccFirstInLastOutItem condition, int pageNo, int pageSize);

    /**
     * 根据缓存拉取记录时间获取记录保存first in last out
     * 
     * @return void
     * <AUTHOR>
     * @throws @date 2022-05-12 17:05
     * @since 1.0.0
     */
    void saveFirstInLastOutByCacheTime();

    /**
     * 根据用户权限加载事件报表
     *
     * @param sessionId:
     * @param condition:查询条件
     * @param pageNo:当前页
     * @param pageSize:查询数量
     * @param limitCount:限制数量
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2022-05-25 16:11
     * @since 1.0.0
     */
    Pager loadTransactionByAuthUserFilter(String sessionId, AccFirstInLastOutItem condition, int pageNo, int pageSize,
        long limitCount);
}
