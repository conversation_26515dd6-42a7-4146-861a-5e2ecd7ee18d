package com.zkteco.zkbiosecurity.acc.service;

import com.alibaba.fastjson.JSONObject;
import org.springframework.stereotype.Service;

import java.util.List;

public interface WechatService {

    /**
     * 获取公众号 AccessToken
     * 
     * @return
     */
    String getAccessToken();

    /**
     * 获取公众号用户列表
     * 
     * @return
     */
    List<String> getWechatUserList();

    /**
     * 获取公众号用户详细信息
     * 
     * @param openId
     * @return
     */
    JSONObject getWechatUserInfo(String openId);

    /**
     * 向微信公众号用户发送特定消息
     * 
     * @param messageJSONObject
     * @param openId
     * @param templateType
     * @param recordId          异常记录ID，用于构建重定向URL
     * @return
     */
    public JSONObject sendWechatMessage(JSONObject messageJSONObject, String openId, String templateType,
            String recordId);

    void wechatSendMessageTest();


    /**
     * 获取微信公众号AppId
     * 
     * @return
     */
    String getAppId();

    /**
     * 根据code获取openid
     * 
     * @param code
     * @return
     */
    String getOpenId(String code);

    /**
     * 保存用户openid
     * 
     * @param pin
     * @param openId
     */
    void saveUserOpenId(String pin, String openId);

    /**
     * 清除用户openid
     * 
     * @param pin
     */
    void clearUserOpenId(String pin);
}
