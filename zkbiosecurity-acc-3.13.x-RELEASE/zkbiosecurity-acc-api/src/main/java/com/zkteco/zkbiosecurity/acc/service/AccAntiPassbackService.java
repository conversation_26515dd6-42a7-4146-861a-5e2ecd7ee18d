/**
 * File Name: AccAntiPassback Created by GenerationTools on 2018-03-13 上午10:27 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.acc.vo.AccAntiPassbackItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;

/**
 * 对应百傲瑞达 AccAntiPassbackService
 * 
 * <AUTHOR>
 * @date: 2018-03-13 上午10:27
 * @version v1.0
 */
public interface AccAntiPassbackService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    AccAntiPassbackItem saveItem(AccAntiPassbackItem item);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AccAntiPassbackItem> getByCondition(AccAntiPassbackItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    AccAntiPassbackItem getItemById(String id);

    /**
     * 根据设备id获取反潜规则
     *
     * @author: mingfa.zheng
     * @date: 2018/4/26 15:48
     * @param deviceId 设备id
     * @return
     */
    Map<String, Object> getApbRule(String deviceId);

    /**
     * 获取已设置反潜的设备id
     *
     * @return String
     * @author: mingfa.zheng
     * @date: 2018/4/26 15:48
     */
    String getDevIdWithApb();

    /**
     * 验证是否有设置全局反潜
     *
     * @author: mingfa.zheng
     * @date: 2018/4/26 15:48
     * @param deviceId 设备id
     * @return boolean
     */
    Boolean validGlobalApb(String deviceId);

    /**
     *
     * 根据反潜转换成相应的门名称组合显示在反潜列表页面中
     *
     * @author: mingfa.zheng
     * @date: 2018/4/26 15:48
     * @param value
     * @return
     */
    String convertAntiPassbackRule(String value);

    /**
     * @Description: 根据设备Id删除全局反潜
     * <AUTHOR>
     * @date 2018/5/30 17:56
     * @param devId 设备ID
     * @return
     */
    void delAntipassbackByDevId(String devId);

    /**
     * 根据设备id获取反潜规则
     *
     * @param devIds
     * @return
     */
    Boolean validApbByDevIds(List<String> devIds);

    /**
     * 反潜迁移
     *
     * @param accAntiPassbackItems
     */
    void handlerTransfer(List<AccAntiPassbackItem> accAntiPassbackItems);

    /**
     * 根据设备id获取
     *
     * @param devId:设备id
     * @return void
     * <AUTHOR>
     * @date 2021-01-18 9:14
     * @since 1.0.0
     */
    List<AccAntiPassbackItem> getItemsByDevId(String devId);

    Boolean validDetermineApb(String deviceId);

    boolean nameExists(String name);

    /**
     * 补全item的设备id，设备名，组1，组2
     *
     * @param item
     * @return
     */
    AccAntiPassbackItem completionItem(AccAntiPassbackItem item);
}