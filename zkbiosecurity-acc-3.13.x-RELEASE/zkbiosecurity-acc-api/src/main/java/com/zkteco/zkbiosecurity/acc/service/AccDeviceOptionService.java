/**
 * File Name: AccDeviceOption Created by GenerationTools on 2018-03-20 上午09:48 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.acc.vo.AccDeviceOptionItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;

/**
 * 对应百傲瑞达 AccDeviceOptionService
 * 
 * <AUTHOR>
 * @date: 2018-03-20 上午09:48
 * @version v1.0
 */
public interface AccDeviceOptionService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    AccDeviceOptionItem saveItem(AccDeviceOptionItem item);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AccDeviceOptionItem> getByCondition(AccDeviceOptionItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    AccDeviceOptionItem getItemById(String id);

    /**
     * @Description: 判断是否单向控制器
     * <AUTHOR>
     * @date 2018/4/25 19:09
     * @param sn 设备序列号
     * @return
     */
    String getOptVal(String sn, String optName);

    /**
     * @Description: 判断设备是否支持参数
     * <AUTHOR>
     * @date 2018/4/25 17:03
     * @param sn 设备序列号
     * @param optName 参数名称
     * @return
     */
    boolean isSupportFun(String sn, String optName);

     /**
     * 新控制器参数，AccSupportFunList，根据位来获取功能支持参数
     * 位置 值 备注
     * 0 0不支持;1支持 读头禁用功能
     * 1 0不支持;1支持 485读头加密功能
     * 2 0不支持;1支持 门锁定功能
     * 3 0不支持;1支持 紧急双开、紧急双闭
     * 4 0不支持;1支持 通过门磁及继电器状态对应到Wiegand读头灯的红绿
     * 5 0不支持;1支持 长姓名的功能
     * 6 0不支持;1支持 韦根格式带sitecode
     * 7 0不支持;1支持 一人多卡功能
     * 8 0不支持;1支持 辅助输入、出门按钮受时间段控制
     * 9 0不支持;1支持 临时用户每天自动删除功能
     * 10 0不支持;1支持 支持wg test功能
     * 11 0不支持;1支持 支持account命令
     * 12 0不支持;1支持 不同用户不用时间段不同验证方式
     * 13 0不支持;1支持 控制485读头Led
     * 14 0不支持;1支持 门支持多种韦根格式，保留
     * 15 0不支持;1支持 权限表中doorid只表示一个门
     * 16 0不支持;1支持 用户超级权限独立表
     * 17 0不支持;1支持 支持添加门属性功能
     * 18 0不支持;1支持 读头属性功能
     * 19 0不支持;1支持 辅助输入属性表功能
     * 20 0不支持;1支持 辅助输出属性表功能
     * 21 0不支持;1支持 门参数表功能
     * 22 0不支持;1支持 反潜表功能
     * 23 0不支持;1支持 互锁表功能
     * 24 0不支持;1支持 Wireless-serial功能
     * 25 0不支持;1支持 4G-usb通信功能
     * 26 0不支持;1支持 表支持devid字段
     * 27 0不支持;1支持 是否支持从机
     * 28 0不支持;1支持 是否支持双网卡
     * 29 0不支持;1支持 是否支持授权表,主要是bioir9000
     * 30 0不支持;1支持 是否身份证登记
     * 31 0不支持;1支持 设备是否支持主从切换
     * 32 0不支持;1支持 设备是否支持混合读头,韦根/485读头
     * 33 0不支持;1支持 设备是否支持韦根数据处理 字节交换 位反转 字节交换
     * 34 0不支持;1支持 设备是否支持desfire控卡功能
     * 35 0不支持;1支持 支不支持下发二维码命令
     * 36 0 不支持;1支持 是否支持广告下发命令zksq200新增
     * 37 0 不支持;1支持 是否支持室内机功能zksq200新增
     * 38 0 不支持;1 支持 设备是否支持 上传比对照片功能
     * 39 0 不支持;1 支持 设备是否支持 上传一体化可见光人脸模板功能
     * 40 0 不支持;1 支持 设备是否远程登记
     * 41 0 不支持;1 支持 设备是否支持验证结果风格设置功能
     * 42 0 不支持;1 支持 设备是否支持设置认证服务器功能
     * 43 0 不支持;1 支持 设备是否支持资源文件下发功能（语音文件、开机画面、欢迎页面、屏保页面等）
     * 44 0 不支持;1 支持 设备是否支持对接AI设备（针对inbioPro等不支持读头属性表的非inbio5系列设备）
     * 45 0 不支持;1 支持 设备是否支持扩展板dm10
     * 46 0 不支持;1 支持 设备是否支持扩展板aux485
     * 47 0 不支持;1 支持 设备是否支持扩展板ex0808
     * 48 0 不支持;1 支持 设备是否支持门锁定时允许超级用户通过功能
     * 49 0 不支持;1 支持 设备是否支持osdp协议
     * 50 0 不支持;1 支持 是否支持人员在读头验证失败超过次数被锁定功能
     * 51 0 不支持;1 支持 是否支持给读头配置隐藏部分人员信息功能
     * 52 0 不支持;1 支持 是否支持梯控拓展版设置功能
     * 53 0 不支持;1 支持 是否支持梯控直达选层功能
     * 54 0 不支持;1 支持 是否支持梯控紧急接口恢复功能
     * 55 0 不支持;1 支持 是否支持下发直达选层表
     * 56 0 不支持;1 支持 是否支持多楼层(控制指令下发多楼层、事件解析多楼层)
     * 57 0 不支持;1 支持 是否支持语音模块
     * 58 0 不支持;1 支持 是否支持设置负楼层
     * 59 0 不支持;1 支持 是否支持身份证号当卡号
     * 60 0 不支持;1 支持 时间段是否支持有效期设置
     * 61 0 不支持;1 支持 是否支持NTP服务设置
     * 62 0 不支持;1 支持 是否支持权限临时时间段
     * 63 0 不支持;1 支持 是否支持可视对讲通讯录
     * @since 2018/4/17 19:35
     * @return
     */
    boolean getAccSupportFunListVal(String devId, int index);

    /**
     * 新控制器参数，AccSupportFunList，根据位来获取功能支持参数（设备注册时上传的json数据）
     *
     * @since 2018/4/17 19:37
     * @return
     */
    boolean getAccSupportFunListVal(int index, String value);

    /**
     * @Description: 判读AccSupportFunList是否支持参数
     * @param devSn
     * @param index
     * @return
     */
    boolean isSupportFunList(String devSn, int index);

    /**
     * 更新设备参数
     *
     * <AUTHOR>
     * @since 2018/4/18 11:07
     * @return
     */
    void updateDevOpt(String devInfoStr, String sn);

    /**
     * 获取设备扩展参数
     *
     * @modify by wenxin 2015-01-28
     * @param sn
     * @throws Exception
     */
    Map<String, String> getDevExtendOption(String sn);

    /**
     * 根据参数名称、设备ID获取对应值
     *
     * <AUTHOR>
     * @since 2018/4/24 10:37
     * @return
     */
    String getValueByNameAndDevSn(String devSn, String optName);

    /**
     * 查看是否支持设备参数--查询数据库
     *
     * <AUTHOR>
     * @since 2018/4/25 9:45
     * @param devSn 设备ID
     * @param optName 参数名
     * @return boolean true：支持；false：不支持
     */
    boolean isSupportDevParam(String devSn, String optName);

    /**
     * @Description: 根据sn和name获取设备参数
     * <AUTHOR>
     * @date 2018/5/3 10:06
     * @param devSn 设备序列号
     * @param optName 参数名称
     * @return
     */
    AccDeviceOptionItem getDevOptValueBySnAndName(String devSn, String optName);

    /**
     * @Description: 根据设备参数名称修改设备参数值
     * <AUTHOR>
     * @date 2018/5/23 14:42
     * @param devId 设备id
     * @param optionName 参数名称
     * @param optionVal 参数值
     * @return
     */
    void setDevOptValByName(String devId, String optionName, String optionVal);

    /**
     * @Description: 根据sn获取设备主从配置参数
     * <AUTHOR>
     * @date 2018/5/29 8:37
     * @param sn 设备序列号
     * @return
     */
    String getMasterSlave(String sn);

    /**
     * @Description: 根据主从配置修改设备参数表
     * <AUTHOR>
     * @date 2018/5/29 9:08
     * @param sn 设备sn
     * @param masterSlave 主从配置
     * @return
     */
    void setMasterSlaveOption(String sn, String masterSlave);

    /**
     * @Description: 根据sn获取设备信息(优先从缓存查询)
     * <AUTHOR>
     * @date 2018/8/17 14:39
     * @param devSn
     * @return
     */
    Map<String, String> getDevOptionBySn(String devSn);

    /**
     * 更新设备参数
     *
     * @param devSn:设备序列号
     * @param optName:参数名称
     * @param optValue:参数值
     * @return void
     * <AUTHOR>
     * @date 2021-09-02 15:33
     * @since 1.0.0
     */
    void updateDevOptions(String devSn, String optName, String optValue);

    /**
     * 设备参数迁移
     *
     * @Description:
     * <AUTHOR>
     * @since 2018年12月12日 下午5:55:50
     * @param items
     */
    void handlerTransfer(List<AccDeviceOptionItem> items);

    /**
     * 根据参数获取设备id
     *
     * <AUTHOR>
     * @since 2018年12月6日 下午7:06:28
     * @param optionName
     * @param optionVal
     * @return
     */
    List<String> getDevIdByOptNameAndOptVal(String optionName, String optionVal);

    /**
     * 功能描述:根据用户权限和参数获取设备id
     *
     * <AUTHOR>
     * @since 2019-05-23 17:19
     * @Param [optionName, optionVal, sessionId]
     * @return
     */
    List<String> getDevIdByOptNameAndOptValAndAuthFilter(String optionName, String optionVal, String sessionId);

    /**
     * 获取扩展参数
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2020/6/4 14:52
     * @param devId 设备id
     * @param optionNameList 参数集合
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDeviceOptionItem>
     */
    List<AccDeviceOptionItem> getDevExtendParams(String devId, List<String> optionNameList);

    /**
     * 根据设备参数名称查找支持该参数功能的设备sn
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-08-07 10:49
     * @param maskDetectionFunOn
     * @param irTempDetectionFunOn
     * @return java.util.List<java.lang.String>
     */
    List<String> getDevSnByOptNames(String maskDetectionFunOn, String irTempDetectionFunOn);

    /**
     * 根据设备序列号和参数名称查询参数
     *
     * @param devSnList:设备序列号集合
     * @param optName:参数名称
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDeviceOptionItem>
     * <AUTHOR>
     * @date 2020-11-24 17:34
     * @since 1.0.0
     */
    List<AccDeviceOptionItem> getOptionItemBySnsAndOptName(List<String> devSnList, String optName);

    /**
     * 根据参数名获取
     *
     * @param optName:参数名
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDeviceOptionItem>
     * <AUTHOR>
     * @date 2020-11-30 17:39
     * @since 1.0.0
     */
    List<AccDeviceOptionItem> getItemsByOptName(String optName);

    /**
     * 设备参数是否存在
     *
     * @param devSn:设备序列号
     * @param optName:参数名称
     * @return boolean
     * <AUTHOR>
     * @date 2020-12-17 11:59
     * @since 1.0.0
     */
    boolean isContainDevParam(String devSn, String optName);

    /**
     * 根据设备id和参数名称查询
     *
     * @param devId:设备id
     * @param optName:参数名称
     * @return com.zkteco.zkbiosecurity.acc.vo.AccDeviceOptionItem
     * <AUTHOR>
     * @date 2021-01-25 11:26
     * @since 1.0.0
     */
    AccDeviceOptionItem getItemByDevIdAndName(String devId, String optName);

    /**
     * 保存参数
     *
     * @param devId:设备id
     * @param optName:参数名称
     * @param optValue:参数值
     * @return void
     * <AUTHOR>
     * @date 2021-01-27 9:49
     * @since 1.0.0
     */
    void saveByDevIdAndOptionNameAndValue(String devId, String optName, String optValue);

    /**
     * 根据设备id获取设备参数
     *
     * @param devId:设备id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDeviceOptionItem>
     * <AUTHOR>
     * @date 2021-02-04 10:05
     * @since 1.0.0
     */
    List<AccDeviceOptionItem> getItemsByDevId(String devId);

    /**
     * 根据设备id和参数类型查询设备参数信息
     *
     * @param devId:设备id
     * @param optType:参数类型
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.model.AccDeviceOption>
     * <AUTHOR>
     * @date 2021-02-04 10:13
     * @since 1.0.0
     */
    List<AccDeviceOptionItem> getItemsByDevIdAndOptType(String devId, Short optType);

    /**
     * 批量保存设备参数
     *
     * @param optionItemList:参数集合
     * @return void
     * <AUTHOR>
     * @date 2021-02-04 15:07
     * @since 1.0.0
     */
    void saveAccDeviceOptionList(List<AccDeviceOptionItem> optionItemList);

    /**
     * 设备是否支持IPC联动
     * 
     * <AUTHOR>
     * @date 2024-05-09 14:37
     * @since 1.0.0
     */
    boolean isDeviceSupportIPCLink(String deviceSn);
}