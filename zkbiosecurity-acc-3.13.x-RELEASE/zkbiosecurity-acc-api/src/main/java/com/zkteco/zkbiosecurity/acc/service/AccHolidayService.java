/**
 * File Name: AccHoliday
 * Created by GenerationTools on 2018-02-26 下午05:53
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import java.util.Date;
import java.util.List;

import com.zkteco.zkbiosecurity.base.service.BaseService;

import com.zkteco.zkbiosecurity.acc.vo.AccHolidayItem;

/**
 * 对应百傲瑞达 AccHolidayService
 * <AUTHOR>
 * @date:	2018-02-26 下午05:53
 * @version v1.0
 */
public interface AccHolidayService extends BaseService {

	/**
	 * 保存item实体，一般会有复杂业务逻辑
	 * @param item
	 */
	AccHolidayItem saveItem(AccHolidayItem item);

	/**
	 * 根据条件查询
	 * @param condition
	 * @return
	 */
	List<AccHolidayItem> getByCondition(AccHolidayItem condition);

	/**
	 * 根据ID查询
	 * @param id
	 * @return
	 */
	public AccHolidayItem getItemById(String id);

    /**
     * @Description: 检查节假日名称是否存在
     * <AUTHOR>
     * @date 2018/5/23 17:15
     * @param name 节假日名称
     * @return
     */
    String isExist(String name);

    /**    
     * @Description: 判断是否属于节假日，是则返回节假日类型
	 * <AUTHOR>
     * @date 2018/6/15 14:59  
     * @param eventDate
     * @return   
     */  
    short isInHoliday(Date eventDate);

	/**
	 * 节假日迁移
	 *
	 * @param accHolidayItems
	 */
	void handlerTransfer(List<AccHolidayItem> accHolidayItems);
}