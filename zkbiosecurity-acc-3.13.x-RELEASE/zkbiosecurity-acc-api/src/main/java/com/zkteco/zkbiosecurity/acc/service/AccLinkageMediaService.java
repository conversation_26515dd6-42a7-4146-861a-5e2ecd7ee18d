/**
 * File Name: AccLinkageMedia Created by GenerationTools on 2018-03-16 下午04:41 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;

import com.zkteco.zkbiosecurity.acc.vo.AccLinkageMediaItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;

/**
 * 对应百傲瑞达 AccLinkageMediaService
 * 
 * <AUTHOR>
 * @date: 2018-03-16 下午04:41
 * @version v1.0
 */
public interface AccLinkageMediaService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    AccLinkageMediaItem saveItem(AccLinkageMediaItem item);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AccLinkageMediaItem> getByCondition(AccLinkageMediaItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    public AccLinkageMediaItem getItemById(String id);

    /**
     * 根据类型和内容获取数据
     * 
     * @param contactIds:
     * @param mediaType:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccLinkageMediaItem>
     * <AUTHOR>
     * @throws
     * @date 2022-02-17 16:06
     * @since 1.0.0
     */
    List<AccLinkageMediaItem> getItemsByCondition(String contactIds, short mediaType);
}