package com.zkteco.zkbiosecurity.acc.service;
/*
 * File Name: AccRTMonitorService
 * <NAME_EMAIL> on 2018/6/4 10:20.
 * Copyright:Copyright © 1999-2018 ZKTeco Inc.All right reserved.
 */

import com.zkteco.zkbiosecurity.acc.vo.AccQueryDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccQueryDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.List;
import java.util.Map;

public interface AccRTMonitorService{

    /**
     * @Description: 获取设备状态，实时监控显示
     * <AUTHOR>
     * @date 2018/6/4 20:15
     * @return
     */
    ZKResultMsg getDevStateData(String areaIds);

    /**
     * 获取门状态
     *
     * @param dev:设备信息
     * @param doorItemList:设备对应门信息
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2021-09-02 9:29
     * @since 1.0.0
     */
    ZKResultMsg getDoorState(AccQueryDeviceItem dev, List<AccQueryDoorItem> doorItemList);

    /**
     * @Description: 更新实时监控设备运行状态
     * <AUTHOR>
     * @date 2018/6/5 10:19
     * @return
     */
    void updateAccDeviceDoorStates(String sn);

    /**    
     * @Description: 远程操作门如远程开门、关门、禁用常开等
     * <AUTHOR>  
     * @date 2018/6/6 9:53  
     * @param opType
     * @param openInterval
     * @param ids
     * @return
     */
    Map<String, String> operateDoor(String opType, String openInterval, String ids);

    /**    
     * @Description: 远程操作辅助输出如打开辅助输出、关闭辅助输出、辅助输出常开等
     * <AUTHOR>  
     * @date 2018/6/6 19:02  
     * @param opType
     * @param openInterval
     * @param ids
     * @return
     */  
    Map<String,String> operateAuxOut(String opType, String openInterval, String ids);

    /**
     * 获取音频文件的路径
     * @author: mingfa.zheng
     * @date: 2018/6/11 17:16
     * @return:
     */
    String getAudioPath(String mediaFileId);

    /**
     * @Description: 获取门状态，电子地图显示
     * <AUTHOR>
     * @date 2018/6/20 18:53
     * @param doorIds
     * @return
     */
    ZKResultMsg getDoorState(String doorIds);

    /**
     * @Description: 判断是否是中文语言
     * <AUTHOR>
     * @date 2018/6/22 11:43
     * @return
     */
    boolean checkIsChinaLanguage();

    /**
     * @Description: 根据当前登录用户获取实时监控需要过滤区域
     * <AUTHOR>
     * @date 2018/8/23 8:55
     * @param sessionId
     * @return
     */
    Map<String,String> getFilterAreaMap(String sessionId);

    /**
     * 根据客户端ID获取实时事件
     * @auther lambert.li
     * @date 2018/11/14 18:09
     * @param
     * @return
     */
    List<AccTransactionItem> getRTMonitorData(String clientId);

    /**
     * 根据SN获取门状态
     * @auther lambert.li
     * @date 2018/11/16 18:18
     * @param
     * @return
     */
    ApiResultMessage getDoorStateBySn(String deviceSn);

    /**
     * 判断是否有LCD许可，判断是否能在实时监控显示LCD功能菜单；
     * 
     * <AUTHOR>
     * @since 2018年11月29日 下午2:50:51
     * @return
     */
	Boolean isShowlcdRTMonitor();

	/**
	 * 根据当前sessionId获取所在的所有区域的名称
	 * 
	 * <AUTHOR>
	 * @since 2018年11月29日 下午2:51:00
	 * @param sessionId
	 * @return
	 */
	String getAreaNamesBySessionId(String sessionId);

	/**
	 * 获取进入监控界面最近的5条人员通行信息
	 * 
	 * <AUTHOR>
	 * @since 2018年11月29日 下午2:52:12
	 * @param areaNames
	 * @return
	 */
	ZKResultMsg getPersonInfo(String sessionId, String areaNames);

    /**
     * 从二号项目迁移代码：远程操作门人员数据绑定
     * @auther zbx.zhong
     * @date 2019/7/29 11:03
     * @param sn
     * @param doorNo
     * @param operator
     * @param opType
     * @return
     */
    AccTransactionItem remoteOperateBindPerson(String sn, String doorNo, String operator, String opType);
}
