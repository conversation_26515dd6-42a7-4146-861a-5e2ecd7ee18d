/**
 * File Name: AccCombOpenPerson
 * Created by GenerationTools on 2018-03-14 下午03:02
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccCombOpenPersonItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.List;

/**
 * 对应百傲瑞达 AccCombOpenPersonService
 * <AUTHOR>
 * @date:	2018-03-14 下午03:02
 * @version v1.0
 */
public interface AccCombOpenPersonService extends BaseService {

	/**
	 * 保存item实体，一般会有复杂业务逻辑
	 * @param item
	 */
	AccCombOpenPersonItem saveItem(AccCombOpenPersonItem item);

	/**
	 * 根据条件查询
	 * @param condition
	 * @return
	 */
	List<AccCombOpenPersonItem> getByCondition(AccCombOpenPersonItem condition);

	/**
	 * 根据ID查询
	 * @param id
	 * @return
	 */
	AccCombOpenPersonItem getItemById(String id);

	/**
	 * 判断名称是否有效/允许
	 * @author: mingfa.zheng
	 * @date: 2018/3/15 16:09
	 * @return:
	 */
	AccCombOpenPersonItem getItemByName(String name);

	ZKResultMsg getCombOpenJsonData();

	/*
	 * add by noah
	 * 验证用户人数，改成直接取到组下名的人员数量。modify by: ob.huang 2013-08-20
	 * 验证用户选择对应组的开门人数是否合法
	 */
	ZKResultMsg checkPersonCount(String groupId);

	/**
	 * 多人开门组：添加人员
	 * @author: mingfa.zheng
	 * @date: 2018/3/22 11:26
	 * @return:
	 */
	ZKResultMsg addPerson(String combOpenPersonId, List<String> personIdList);

	/**
	 * 多人开门组：删除人员
	 * @author: mingfa.zheng
	 * @date: 2018/3/22 15:55
	 * @return:
	 */
	ZKResultMsg delPerson(String combOpenId, String personIds);

	/**
	 * 获取多人开门组已经存在的人员ID
	 * @author: mingfa.zheng
	 * @date: 2018/4/24 17:58
	 * @return:
	 */
	List<String> getExistPersonIds();

	/**
	 * @Description: 获取多人开门组人员数量
	 *
	 * @author: mingfa.zheng
	 * @date:  2018/7/25 20:42
	 * @param: [sessionId, accCombOpenPersonId]
	 * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
	 **/
	ZKResultMsg getPersonCount(String sessionId, String accCombOpenPersonId);

	/**
	 * 多人开门组迁移
	 *
	 * @param accCombOpenPersonItems
	 */
	void handlerTransfer(List<AccCombOpenPersonItem> accCombOpenPersonItems);

	Pager loadPagerByAuthFilter(String sessionId, AccCombOpenPersonItem condition, int pageNo, int pageSize);

	/**
	 * 获取多人开门人员组下拉列表数据
	 *
	 * @return java.util.List<com.zkteco.zkbiosecurity.base.bean.SelectItem>
	 * <AUTHOR>
	 * @date 2021-07-21 15:48
	 * @since 1.0.0
	 */
	List<SelectItem> getCombOpenList();
}