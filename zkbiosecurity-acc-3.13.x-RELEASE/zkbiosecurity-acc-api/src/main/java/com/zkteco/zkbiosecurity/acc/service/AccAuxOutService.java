/**
 * File Name: AccAuxOut Created by GenerationTools on 2018-03-14 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccAuxOutItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.List;
import java.util.Map;

/**
 * 对应百傲瑞达 AccAuxOutService
 * 
 * <AUTHOR>
 * @date: 2018-03-14 上午09:38
 * @version v1.0
 */
public interface AccAuxOutService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    AccAuxOutItem saveItem(AccAuxOutItem item);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AccAuxOutItem> getByCondition(AccAuxOutItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    AccAuxOutItem getItemById(String id);

    /**
     * 跟进ID获取对应辅助输出状态
     * 
     * @param idList
     * @return
     */
    Map<String, String> getAuxOutIds(List<String> idList);

    /**
     * 判断是否存在
     * 
     * @param item
     * @return
     */
    boolean isExist(AccAuxOutItem item);

    /**
     * 判断辅助输出是否支持自定义时间段
     *
     * <AUTHOR>
     * @since 2018年5月16日 10:55:23
     * @param devSn
     * @return
     */
    boolean isSupportOutRelaySet(String devSn);

    /**
     * @Description: 获取辅助输出状态，用于实时监控显示
     * <AUTHOR>
     * @date 2018/6/4 9:04
     * @return
     */
    ZKResultMsg getAuxOutStatus(String sessionId);

    /**
     * @Description: 根据用户权限过滤显示辅助输出
     * <AUTHOR>
     * @date 2018/6/20 11:20
     * @param sessionId
     * @param codition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager loadPagerByAuthFilter(String sessionId, AccAuxOutItem codition, int pageNo, int pageSize);

    /**
     * @Description: 根据设备ID获取辅助输出
     * <AUTHOR>
     * @date 2018/8/22 18:02
     * @param devIdList
     * @return
     */
    List<AccAuxOutItem> getItemsByDevIds(List<String> devIdList);

    /**
     * 根据id获取辅助输出item
     *
     * @param auxOutIdList
     * @return
     */
    List<AccAuxOutItem> getItemsByIds(List<String> auxOutIdList);

    /**
     * 辅助输出数据迁移
     * 
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午10:50:30
     * @param items
     */
    void handlerTransfer(List<AccAuxOutItem> items);

    /**
     * 检查时间段是否被使用
     *
     * @param timeSegId
     * @return
     */
    boolean checkTimeSegUsed(String timeSegId);

    /**
     * 批量保存辅助输出
     *
     * @param accDeviceItem:设备信息
     * @param accAuxOutList:辅助输出信息
     * @return void
     * <AUTHOR>
     * @date 2021-01-21 10:38
     * @since 1.0.0
     */
    void saveAuxOutList(AccDeviceItem accDeviceItem, List<AccAuxOutItem> accAuxOutList);

    /**
     * 保存辅助输出信息(没有复杂的业务逻辑)
     *
     * @param item:辅助输出信息
     * @return com.zkteco.zkbiosecurity.acc.vo.AccAuxOutItem
     * <AUTHOR>
     * @date 2021-01-21 10:45
     * @since 1.0.0
     */
    AccAuxOutItem saveSimpleItem(AccAuxOutItem item);

    /**
     * 根据辅助输出编号和设备id查询
     *
     * @param auxNo:辅助输出编号
     * @param devId:设备id
     * @return com.zkteco.zkbiosecurity.acc.vo.AccAuxOutItem
     * <AUTHOR>
     * @date 2021-02-01 10:46
     * @since 1.0.0
     */
    AccAuxOutItem getItemByAuxNoAndDevId(short auxNo, String devId);

    /**
     * 根据设备id获取辅助输出编号
     *
     * @param devId:设备id
     * @return java.util.List<java.lang.Short>
     * <AUTHOR>
     * @date 2021-02-02 18:28
     * @since 1.0.0
     */
    List<Short> getAuxOutNoByDevId(String devId);

    /**
     * 根据时间段id统计数量
     *
     * @param timeSegId:时间段id
     * @return int
     * <AUTHOR>
     * @date 2021-02-03 10:43
     * @since 1.0.0
     */
    int countByTimeSegId(String timeSegId);

    /**
     * 获取辅助输出类简写名称
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021-02-07 14:04
     * @since 1.0.0
     */
    String getAuxOutSimpleName();
}