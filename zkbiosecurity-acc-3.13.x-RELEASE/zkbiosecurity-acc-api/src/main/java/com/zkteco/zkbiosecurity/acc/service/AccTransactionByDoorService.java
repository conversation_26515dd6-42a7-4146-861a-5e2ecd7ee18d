package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccTransactionDoorItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;

/**
 * 以门查询业务service
 *
 * <AUTHOR>
 * @DATE 2020-08-07 15:44
 */
public interface AccTransactionByDoorService {

    /**
     * 根据用户权限获取数据
     *
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager loadPagerByAuthFilter(String sessionId, AccTransactionDoorItem condition, int pageNo, int pageSize);
}
