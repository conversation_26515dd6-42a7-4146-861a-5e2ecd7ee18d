package com.zkteco.zkbiosecurity.acc.service;
/*
 * File Name: AccBaseDictionaryService
 * <NAME_EMAIL> on 2018/5/2 11:51.
 * Copyright:Copyright © 1999-2018 ZKTeco Inc.All right reserved.
 */

import java.util.Map;

public interface AccBaseDictionaryService {

    /**
     * @Description: 获取数据字典Map（key ：code，value：dictValue）
     * <AUTHOR>
     * @date 2018/5/2 11:54
     * @param key 字典code
     * @return
     */
    Map<String, String> getBaseDictionaryMap(String key);

    /**
     * @Description: 获取设备通信错误返回值
     * <AUTHOR>
     * @date 2018/5/16 16:27
     * @param ret 错误值
     * @return
     */
    String getCommReason(int ret);
}
