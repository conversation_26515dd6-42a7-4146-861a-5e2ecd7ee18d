package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccExtDeviceItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import java.util.List;

public interface AccExtDeviceService extends BaseService {
    /**
     * 根据权限过滤显示扩展板
     *
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager loadPagerByAuthFilter(String sessionId, AccExtDeviceItem condition, int pageNo, int pageSize);

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    AccExtDeviceItem getItemById(String id);

    /**
     * 根据条件查询
     *
     * @param condition
     * @return
     */
    List<AccExtDeviceItem> getByCondition(AccExtDeviceItem condition);

    /**
     * 设置dm10属性相关
     *
     * @param accExtDeviceItem
     */
    void setDM10(AccExtDeviceItem accExtDeviceItem);

    /**
     * 设置aux485相关属性
     *
     * @param item
     */
    void setAUX485(AccExtDeviceItem item);

    /**
     * alias字段重复校验
     *
     * @param alias
     * @return
     */
    boolean isExistAlias(String alias);

    /**
     * 扩展板485地址是否重复
     *
     * @param commAddress
     * @param devId
     * @return
     */
    boolean isExistAddress(Short commAddress, String devId);

    /**
     * 设置扩展板ex0808
     *
     * @param item
     */
    void setEX0808(AccExtDeviceItem item);

    /**
     * 根据ids查询列表
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-03-26 16:34
     * @param extDevIds
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccExtDeviceItem>
     */
    List<AccExtDeviceItem> getItemByIds(String extDevIds);

    /**
     * 根据选中的设备ID查找已经被设置了的协议类型
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-04-02 15:34
     * @param devId
     * @return java.lang.Short
     */
    Short getDevProtocolType(String devId);

    /**
     * 分页查询，无复杂的业务逻辑
     *
     * @param condition:查询条件
     * @param page:页索引，从0开始
     * @param size:每页几条
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-02-02 16:59
     * @since 1.0.0
     */
    Pager getSimpleItemsByPage(BaseItem condition, int page, int size);

    /**
     * 保存item对象
     *
     * @param item:扩展板信息
     * @return void
     * <AUTHOR>
     * @date 2021-02-02 17:06
     * @since 1.0.0
     */
    AccExtDeviceItem saveSimpleItem(AccExtDeviceItem item);

    /**
     * 根据设备id获取所有扩展板编号
     *
     * @param devId:设备id
     * @return java.util.List<java.lang.Short>
     * <AUTHOR>
     * @date 2021-02-02 18:30
     * @since 1.0.0
     */
    List<Short> getExtBoardNoByDevId(String devId);

    /**
     * 根据设备id查询扩展板信息
     *
     * @param devId
     * @return
     */
    List<AccExtDeviceItem> getItemsByDevId(String devId);
}
