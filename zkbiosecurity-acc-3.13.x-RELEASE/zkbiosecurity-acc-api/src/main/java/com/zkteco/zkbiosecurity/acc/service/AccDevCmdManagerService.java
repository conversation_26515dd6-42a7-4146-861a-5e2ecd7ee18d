package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccBioTemplateItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonOptItem;

import java.util.List;

/**
 * 发送给设备命令相关Service接口
 *
 * <AUTHOR>
 * @date 2021-04-25 09:35
 */
public interface AccDevCmdManagerService {

    /**
     * 下发人员信息到设备
     *
     * @param devSn 设备序列号
     * @param personOptItemList 人员命令操作bean
     * @param imme 是否紧急
     * <AUTHOR>
     * @date 2021/4/25 9:38
     */
    void setPersonToDev(String devSn, List<AccPersonOptItem> personOptItemList, boolean imme);

    /**
     * 下发人员生物模版
     *
     * @param devSn 设备序列号
     * @param accBioTemplateItemList 生物模板数据
     * @param imme 是否紧急
     * <AUTHOR>
     * @date 2021/4/25 9:38
     */
    void setPersonBioTemplateToDev(String devSn, List<AccBioTemplateItem> accBioTemplateItemList, boolean imme);
}
