/**
 * File Name: AccAuxIn Created by GenerationTools on 2018-03-13 下午05:00 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccAuxInItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.List;

/**
 * 对应百傲瑞达 AccAuxInService
 * 
 * <AUTHOR>
 * @date: 2018-03-13 下午05:00
 * @version v1.0
 */
public interface AccAuxInService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    AccAuxInItem saveItem(AccAuxInItem item);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AccAuxInItem> getByCondition(AccAuxInItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    AccAuxInItem getItemById(String id);

    /**
     * 判断是否存在
     * 
     * @param item
     * @return
     */
    boolean isExist(AccAuxInItem item);

    /**
     * @Description: 获取辅助输出状态，用于实时监控显示
     * <AUTHOR>
     * @date 2018/6/4 8:59
     * @return
     */
    ZKResultMsg getAuxInStatus(String sessionId);

    /**
     * @Description: 根据用户权限过滤，显示辅助输入
     * <AUTHOR>
     * @date 2018/6/20 11:17
     * @param sessionId
     * @param codition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager loadPagerByAuthFilter(String sessionId, AccAuxInItem codition, int pageNo, int pageSize);

    /**
     * @Description: 根据设备ID获取辅助输入
     * <AUTHOR>
     * @date 2018/8/22 18:01
     * @param devIdList
     * @return
     */
    List<AccAuxInItem> getItemsByDevIds(List<String> devIdList);

    /**
     * 根据id获取辅助输入item
     *
     * @param auxInIdList
     * @return
     */
    List<AccAuxInItem> getItemsByIds(List<String> auxInIdList);

    /**
     * 辅助输入数据迁移
     * 
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午10:41:34
     * @param items
     */
    void handlerTransfer(List<AccAuxInItem> items);

    /**
     * 检查时间段是否被使用
     *
     * @param timeSegId
     * @return
     */
    boolean checkTimeSegUsed(String timeSegId);

    /**
     * 批量保存辅助输入
     *
     * @param accDeviceItem:设备信息
     * @param accAuxInList:辅助输入信息
     * @return void
     * <AUTHOR>
     * @date 2021-01-20 14:12
     * @since 1.0.0
     */
    void saveAuxInList(AccDeviceItem accDeviceItem, List<AccAuxInItem> accAuxInList);

    /**
     * 分页查询
     *
     * @param condition:条件
     * @param page:页索引，从0开始
     * @param size:每页几条
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-1-20 14:24
     * @since 1.0.0
     */
    Pager getSimpleItemsByPage(BaseItem condition, int page, int size);

    /**
     * 根据辅助输入编号和设备id查询设备信息
     *
     * @param auxNo:辅助输入编号
     * @param devId:设备id
     * @return com.zkteco.zkbiosecurity.acc.vo.AccAuxInItem
     * <AUTHOR>
     * @date 2021-02-01 10:50
     * @since 1.0.0
     */
    AccAuxInItem getItemByAuxNoAndDevId(short auxNo, String devId);

    /**
     * 根据设备id获取辅助输入编号
     *
     * @param devId:设备id
     * @return java.util.List<java.lang.Short>
     * <AUTHOR>
     * @date 2021-02-02 17:21
     * @since 1.0.0
     */
    List<Short> getAuxInNoByDevId(String devId);

    /**
     * 保存item实体，无复杂业务逻辑
     *
     * @param item:辅助输入信息
     * @return com.zkteco.zkbiosecurity.acc.vo.AccAuxInItem
     * <AUTHOR>
     * @date 2021-02-02 17:50
     * @since 1.0.0
     */
    AccAuxInItem saveSimpleItem(AccAuxInItem item);

    /**
     * 根据时间段id统计数量
     *
     * @param timeSegId:时间段id
     * @return int
     * <AUTHOR>
     * @date 2021-02-03 10:41
     * @since 1.0.0
     */
    int countByTimeSegId(String timeSegId);

    /**
     * 获取辅助输入类简写名称
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021-02-07 14:03
     * @since 1.0.0
     */
    String getAuxInSimpleName();
}