package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/1/29 17:29
 * @since 1.0.0
 */
public interface Acc4OtherGetVidChannel2EntityService {

    /**
     * 获取绑定的摄像头名称(通道名称)
     *
     * @param auxInIdList:辅助输入id
     * @return java.util.Map<java.lang.String,java.lang.String>
     * <AUTHOR>
     * @date 2021-01-29 17:32
     * @since 1.0.0
     */
    Map<String, String> getAccAuxInBindChannelNames(List<String> auxInIdList);

    /**
     * 获取读头绑定的摄像头名称
     *
     * @param readerIds:读头id
     * @return java.util.Map<java.lang.String,java.lang.String>
     * <AUTHOR>
     * @date 2021-01-29 17:35
     * @since 1.0.0
     */
    Map<String, String> getAccReaderBindChannelNames(List<String> readerIds);
}
