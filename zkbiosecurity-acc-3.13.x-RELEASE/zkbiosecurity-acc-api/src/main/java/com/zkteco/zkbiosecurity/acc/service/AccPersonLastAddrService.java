package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccPersonLastAddrItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import java.util.List;
import java.util.Map;

/**
 * 对应百傲瑞达 AccPersonLastAddrService
 * <AUTHOR>
 * @date:	2018-03-21 16:19:52
 * @version v1.0
 */
public interface AccPersonLastAddrService extends BaseService {
	/**
	 * 根据条件查询
	 * @param condition
	 * @return
	 */
	List<AccPersonLastAddrItem> getByCondition(AccPersonLastAddrItem condition);

	/**
	 * 删除所有数据
	 * <AUTHOR>
	 * @date:	2018-04-28 16:19:52
	 */
    void deleteAllData();

	/**
	 * 获取导出数据
	 * @author:	verber
	 * @date:	2018-05-03 11:30:27
	 * @return
	 */
	List<AccPersonLastAddrItem> getItemData(Class<AccPersonLastAddrItem> accPersonLastAddrItemClass, BaseItem condition, int beginIndex, int endIndex);

	/**
	 * @Description: 保存人员最后访问位置
	 * <AUTHOR>
	 * @date 2018/5/14 19:17
	 * @return
	 */
    void saveAccPersonLastAddrHandle(AccTransactionItem accTransactionItem);

	/**
	 * 获取人员最后访问位置信息
	 * @author:	verber
	 * @date:	2018-05-24 15:02
	 * @return
	 */
    Map<String, Object> getPersonLastAddrByPin(String pin,String sessionId);


	/**
	 * 人员最后访问位置数据迁移
	 *
	 * @param accPersonLastAddrItems
	 */
	void handlerTransfer(List<AccPersonLastAddrItem> accPersonLastAddrItems);
}
