package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.api.vo.AccApiLevelAddDoorItem;
import com.zkteco.zkbiosecurity.acc.api.vo.AccApiLevelAddItem;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;

public interface AccApiLevelService {

    /**
     * 新增门禁权限组API接口
     *
     * @return
     */
    ApiResultMessage addApiLevel(AccApiLevelAddItem accApiLevelAddItem);

    /**
     * API添加门禁权限组下加门接口
     *
     * @return
     */
    ApiResultMessage addApiLevelDoor(AccApiLevelAddDoorItem accApiLevelAddDoorItem);

    /**
     * 新增批量添加人员门禁权限接口
     *
     * @param
     * @return
     */
    ApiResultMessage addApiPersonLevel(String pin, String levelId);
}
