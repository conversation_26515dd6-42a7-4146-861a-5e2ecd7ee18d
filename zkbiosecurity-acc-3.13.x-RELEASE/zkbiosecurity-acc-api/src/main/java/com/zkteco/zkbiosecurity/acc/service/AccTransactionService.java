package com.zkteco.zkbiosecurity.acc.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.api.vo.AccApiTransactionItem;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 对应百傲瑞达 AccTransactionService
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-03-07 16:19:52
 */
public interface AccTransactionService extends BaseService {
    /**
     * 根据条件查询
     *
     * @param condition
     * @return
     */
    List<AccTransactionItem> getByCondition(AccTransactionItem condition);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    public AccTransactionItem getItemById(String id);

    /**
     * 处理事件记录
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/22 9:49
     */
    void handleRTLog(String rtLog, String sn);

    AccTransactionItem saveItem(AccTransactionItem at);

    /**
     * 清除所有门禁记录
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/28 16:49
     */
    void deleteAllData();

    /**
     * 清除当天所有门禁记录
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/28 16:49
     */
    void deleteAllDataByToday();

    /**
     * 清除所有异常门禁记录
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/28 16:49
     */
    void deleteAllExceptionData();

    /**
     * 清除所有异常门禁记录（拓展了异常记录的范围）
     * 
     * <AUTHOR>
     */
    void deleteAllExceptionDataExpand();

    /**
     * 获取导出数据
     *
     * @return
     * @author: verber
     * @date: 2018-05-03 11:30:27
     */
    List<?> getItemData(Class targetClass, BaseItem condition, int beginIndex, int endIndex);

    /**
     * 获取人员/访客信息
     *
     * @param pin:人员编号
     * @return java.util.Map<java.lang.String,java.lang.String>
     * <AUTHOR>
     * @date 2023-06-26 11:09
     * @since 1.0.0
     */
    Map<String, String> getPersonInfo(String pin);

    /**
     * @param filePath
     * @return
     * @Description: 判断文件是否存在
     * <AUTHOR>
     * @date 2018/6/12 9:01
     */
    boolean isFileExist(String filePath);

    /**
     * @param accTransactionItem
     * @return
     * @Description: 获取门最近发生事件
     * <AUTHOR>
     * @date 2018/6/14 19:46
     */
    Pager getTransactionsTodayLast(AccTransactionItem accTransactionItem);

    /**
     * 通过读头获得当前卡
     *
     * @param readerIds
     * @return
     * <AUTHOR>
     * @date 2018/6/28 9:01
     */
    List<AccTransactionCardNoItem> getCurrIssueCardNoByReader(String readerIds, Date time);

    /**
     * @param commType
     * @param data
     * @param sn
     * @return
     * @Description: 处理查询设备事件记录数据
     * <AUTHOR>
     * @date 2018/7/4 15:17
     */
    Map<String, String> dealQueryTransaction(int commType, String data, String sn);

    void sendAlarmMail(String subject, String content);// 附件暂不处理

    int countByDeviceSnAndDoorNo(String deviceSn, Short doorNo, Date startDatetime, Date endDatetime);

    List<AccTransactionItem> getBySNAndDoorNo(String deviceSn, Short doorNo, Date startDatetime, Date endDatetime);

    /**
     * @return
     * @Description: 获取软件中事件记录数量
     * <AUTHOR>
     * @date 2018/9/4 15:49
     */
    long getTransactionsCount();

    /**
     * 插入门禁实时事件记录
     *
     * @param sn
     * @param tempLog
     * @return
     * @auther lambert.li
     * @date 2018/11/15 9:09
     */
    void addRTLog(String sn, String tempLog);

    /**
     * 根据设备SN获取事件记录---API
     * 
     * @param deviceSn:
     * @param startDate:
     * @param endDate:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR>
     * @throws @date 2020-09-02 10:10
     * @since 1.0.0
     */
    ApiResultMessage getAccApiTransactionsBySn(String deviceSn, Date startDate, Date endDate, int pageNo, int pageSize);

    /**
     * 根据人员PIN获取事件记录---API
     * 
     * @param pin:
     * @param startDate:
     * @param endDate:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR>
     * @throws @date 2020-09-02 10:33
     * @since 1.0.0
     */
    ApiResultMessage getAccApiTransactionsByPin(String pin, Date startDate, Date endDate, int pageNo, int pageSize);

    /**
     * 分页获取门禁事件记录---API
     *
     * @param pin
     * @param startDate
     * @param endDate
     * @param pageNo
     * @param pageSize
     * @return
     * @auther lambert.li
     * @date 2018/11/17 15:14
     */
    List<AccApiTransactionItem> getAccApiTransactionList(String pin, Date startDate, Date endDate, int pageNo,
        int pageSize);

    /**
     * 获取门禁实时事件记录
     *
     * @param timestamp
     * @return
     * @auther lambert.li
     * @date 2018/11/17 15:18
     */
    List<AccApiTransactionItem> getRTMonitorData(String timestamp);

    /**
     * 根据设备序列号、门编号、开始时间、截止时间获取事件记录
     *
     * @param deviceSn 设备序列号
     * @param doorNo 门编号
     * @param startDatetime 开始时间
     * @param endDatetime 结束时间
     * @param page
     * @param size
     * @return
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年11月27日 下午3:37:17
     */
    Pager getAccTransactionBySnAndDoorNo(String deviceSn, Short doorNo, Date startDatetime, Date endDatetime, int page,
        int size);

    /**
     * 全部记录迁移
     *
     * @param accTransactionItems
     */
    void handlerTransfer(List<AccTransactionItem> accTransactionItems);

    /**
     * 面板-门禁模块-事件趋势：根据时间获取事件记录的事件时间和数量
     *
     * @return
     * <AUTHOR>
     * @Param [startDate, endDate]
     * @since 2019-01-10 14:38
     */
    List<Object[]> findByEventTime(Date startDate, Date endDate);

    /**
     * 功能描述:获取读取的身份证信息
     *
     * @return
     * <AUTHOR>
     * @Param [cardNo]
     * @since 2019-05-08 15:56
     */
    JSONObject getIdentityCardInfo(String cardNo);

    /**
     * 获取事件对应视频联动的信息
     *
     * @param vidLinkageHandle 联动句柄
     * @param fileType 文件类型(1录像，2抓拍)
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/29 10:21
     */
    ZKResultMsg viewVidLinkData(String vidLinkageHandle, String fileType);

    /**
     * 视频事件下载录像文件前验证
     *
     * @param transactionId 事件id
     * @return java.util.Map<java.lang.String , java.lang.Object>
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/29 10:51
     */
    Map<String, Object> getVideoFileValidate(String transactionId);

    /**
     * 清除门禁数据
     *
     * @return
     * <AUTHOR>
     * @Param [keptMonth]
     * @since 2019-09-03 16:34
     */
    void executeAccDataClean(String keptMonth);

    /**
     * 发送报警信息
     *
     * @param content
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2019-11-12 18:21
     */
    void sendAlarmSMS(String content);

    /**
     * 获取选定的时间之后的信息
     *
     * @param time 最早的时间
     * @param page 获取第几页数据
     * @param pageSize 每页数据量
     * @return
     */
    List getItemAfterTime(Date time, int page, int pageSize);

    /**
     * 组装邮件发送内容
     *
     * @param accTransactionItem
     * @return
     */
    String getMailContent(AccTransactionItem accTransactionItem);

    /**
     * 根据事件点id、记录入库开始时间、截止时间获取事件记录
     *
     * @param doorIds:门ids
     * @param startCreateTime:开始时间
     * @param endCreateTime:截止时间
     * @param page:页索引
     * @param size:每页几条
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-06-15 16:22
     * @since 1.0.0
     */
    Pager getAccTransactionByDoorIdsAndCreateTime(String doorIds, Date startCreateTime, Date endCreateTime, int page,
        int size);

    /**
     * 获取人员编号不为空（且过滤联动、全局联动）sql语句
     *
     * @param condition:
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022-09-19 16:24
     * @since 1.0.0
     */
    String getSqlByPinNotNull(AccTransactionItem condition);

    /**
     * 根据用户权限加载异常事件报表
     *
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-07-07 16:56
     */
    Pager loadAlarmTransactionByAuthUserFilter(String sessionId, AccAlarmTransactionItem condition, int pageNo,
        int pageSize);

    /**
     * 根据用户权限加载今日事件报表
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-07-07 17:05
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     */
    Pager loadTodayTransactionByAuthUserFilter(String sessionId, AccTransactionTodayItem condition, int pageNo,
        int pageSize);

    /**
     * 根据用户权限加载事件报表
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-07-07 17:06
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     */
    Pager loadTransactionByAuthUserFilter(String sessionId, AccTransactionItem condition, int pageNo, int pageSize);

    /**
     * 获取事件记录照片路径集合
     *
     * <AUTHOR>
     * @date 2020-07-22 17:53
     * @param sessionId
     * @param condition
     * @return
     */
    List<AccTransactionPhotoItem> getPhotoFilePathList(String sessionId, AccTransactionPhotoItem condition);

    /**
     * 根据sessionId获取区域名称
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-08-05 10:03
     * @param sessionId
     * @return java.lang.String
     */
    String getAreaNamesBySessionId(String sessionId);

    /**
     * 根据sessionId获取部门code
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-08-05 10:17
     * @param sessionId
     * @return java.lang.String
     */
    String getDeptCodesBySessionId(String sessionId);

    /**
     * 获取指定时间段某个设备的记录数量
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-08-11 10:07
     * @param accTransactionItem
     * @return java.lang.Long
     */
    Long countAccTransactionByCondition(AccTransactionItem accTransactionItem);

    /**
     * 组装短信发送内容
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-08-13 16:57
     * @param newTransaction
     * @return java.lang.String
     */
    String getSMSContent(AccTransactionItem newTransaction);

    /**
     * 根据人员PIN获取FirstInLastOut记录---API
     * 
     * <AUTHOR>
     * @Param pin
     * @Param startDate
     * @Param endDate
     * @Param pageNo
     * @Param pageSize
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * @date 2020/9/3 11:49
     */
    ApiResultMessage getAccApiFirstInLastOutByPin(String pin, Date startDate, Date endDate, int pageNo, int pageSize);

    /**
     * 下载Digifort录像
     * 
     * <AUTHOR>
     * @since 2020/7/17 18:37
     * @param cameraName
     * @param eventTime 时间
     * @returnjava.util.Map<java.lang.String,java.lang.Object>
     */
    ZKResultMsg getDigifortVideoFile(String cameraName, long eventTime);

    /**
     * 根据事件点id、开始时间、截止时间获取事件记录
     *
     * @param doorIds:门ids
     * @param startDatetime:开始时间
     * @param endDatetime:结束时间
     * @param page:页索引
     * @param size:每页几条
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-02-03 17:56
     * @since 1.0.0
     */
    Pager getAccTransactionByDoorIds(String doorIds, Date startDatetime, Date endDatetime, int page, int size);

    /**
     * 获取人员门禁事件记录---API
     *
     * <AUTHOR>
     * @date 2021-06-15 15:00
     * @param deviceSn
     * @param pin
     * @param startDate
     * @param endDate
     * @param pageNo
     * @param pageSize
     * @since 1.0.0
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    ZKResultMsg getAccApiTransactionsBySnAndPin(String deviceSn, String pin, Date startDate, Date endDate, int pageNo,
        int pageSize);

    /**
     * 获取门事件记录、获取当天门事件记录、获取开关门事件记录---API
     *
     * <AUTHOR>
     * @date 2021-06-16 14:39
     * @param deviceSn 设备编号
     * @param filter 过滤条件（人员编号或姓名）
     * @param type 事件类型（alarm报警事件 normal正常事件 warn异常事件）
     * @param eventNo 事件编号
     * @param startDate
     * @param endDate
     * @param pageNo
     * @param pageSize
     * @since 1.0.0
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    ZKResultMsg getAccApiTransactionsByFilterAndType(String deviceSn, String filter, String type, String eventNo,
        Date startDate, Date endDate, int pageNo, int pageSize);

    /**
     * 获取门禁事件记录详情
     * 
     * <AUTHOR>
     * @date 2021-06-15 17:47
     * @param id
     * @since 1.0.0
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    ZKResultMsg getAccApiTransactionDetailById(String id);

    /**
     * 判断实时监控页面的事件是否要有下发卡号给人员的操作
     *
     * @param eventNo 事件编号
     * @return boolean
     * <AUTHOR>
     * @date 2021-12-09 17:24
     */
    boolean isExistCardOp(Short eventNo);

    /**
     * 组装line推送内容
     * 
     * @param newTransaction:
     * @return java.lang.String
     * <AUTHOR>
     * @throws @date 2022-02-17 11:54
     * @since 1.0.0
     */
    String getLineContent(AccTransactionItem newTransaction);

    /**
     * 根据用户权限加载事件报表
     *
     * @param sessionId:
     * @param condition:查询条件
     * @param pageNo:当前页
     * @param pageSize:查询数量
     * @param limitCount:限制数量
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2022-05-25 13:53
     * @since 1.0.0
     */
    Pager loadTransactionByAuthUserFilter(String sessionId, AccTransactionItem condition, int pageNo, int pageSize,
        long limitCount);

    /**
     * 根据用户权限加载异常事件报表
     *
     * @param sessionId:
     * @param condition:查询条件
     * @param pageNo:当前页
     * @param pageSize:查询数量
     * @param limitCount:限制数量
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2022-05-25 14:25
     * @since 1.0.0
     */
    Pager loadAlarmTransactionByAuthUserFilter(String sessionId, AccAlarmTransactionItem condition, int pageNo,
        int pageSize, long limitCount);

    /**
     * 根据用户权限加载今日事件报表
     *
     * @param sessionId:
     * @param condition:查询条件
     * @param pageNo:当前页
     * @param pageSize:查询数量
     * @param limitCount:限制数量
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2022-05-25 16:07
     * @since 1.0.0
     */
    Pager loadTodayTransactionByAuthUserFilter(String sessionId, AccTransactionTodayItem condition, int pageNo,
        int pageSize, long limitCount);

    /**
     * 根据事件ID获取-API
     *
     * @param id:事件id
     * @return com.zkteco.zkbiosecurity.acc.api.vo.AccApiTransactionItem
     * <AUTHOR>
     * @date 2022-10-26 16:21
     * @since 1.0.0
     */
    ApiResultMessage getAccApiTransactionsById(String id);

    /**
     * 获取缓存读头信息
     *
     * @param sn:设备序列号
     * @param doorNo:门编号
     * @param readerState:读头状态
     * @return com.zkteco.zkbiosecurity.acc.vo.AccQueryReaderItem
     * <AUTHOR>
     * @date 2023-06-05 18:22
     * @since 1.0.0
     */
    AccQueryReaderItem getReaderCacheByDevSnAndDoorNoAndReaderState(String sn, String doorNo, Short readerState);

    /**
     * 根据sn分页查询事件记录
     * 
     * @param deviceSn:
     * @param startTime:
     * @param endTime:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2023-01-06 16:48
     * @since 1.0.0
     */
    Pager getAccApiTransactionsBySnAndPage(String deviceSn, Date startTime, Date endTime, Integer pageNo,
        Integer pageSize);

    /**
     * 根据pin分页获取事件记录
     * 
     * @param pin:
     * @param startTime:
     * @param endTime:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2023-01-06 17:06
     * @since 1.0.0
     */
    Pager getAccApiTransactionsByPinAndPage(String pin, Date startTime, Date endTime, Integer pageNo, Integer pageSize);

    /**
     * 分页获取事件记录
     * 
     * @param personPin:
     * @param startTime:
     * @param endTime:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2023-01-06 17:13
     * @since 1.0.0
     */
    Pager getAccApiTransactionListByPage(String personPin, Date startTime, Date endTime, Integer pageNo,
        Integer pageSize);

    /**
     * 根据人员编号分页获取FirstInAndLastOut记录
     * 
     * @param pin:
     * @param startTime:
     * @param endTime:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2023-01-06 17:16
     * @since 1.0.0
     */
    Pager getAccApiFirstInLastOutByPinAndPage(String pin, Date startTime, Date endTime, Integer pageNo,
        Integer pageSize);

    /**
     * 获取门记录、获取当天门事件记录、获取开关门事件记录、获取人员门禁事件记录(查询条件不同）
     * 
     * @param devSn:
     * @param personPin:
     * @param startDate:
     * @param endDate:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2023-01-06 17:45
     * @since 1.0.0
     */
    ZKResultMsg getBySnAndPinAndPage(String devSn, String personPin, Date startDate, Date endDate, Integer pageNo,
        Integer pageSize);

    /**
     * 获取门记录、获取当天门事件记录、获取开关门事件记录、获取人员门禁事件记录
     * 
     * @param devSn:
     * @param filter:
     * @param type:
     * @param eventNo:
     * @param startDate:
     * @param endDate:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2023-01-06 17:46
     * @since 1.0.0
     */
    ZKResultMsg getByFilterAndTypeAndPage(String devSn, String filter, String type, String eventNo, Date startDate,
        Date endDate, Integer pageNo, Integer pageSize);

    /**
     * 组装人员姓名信息
     *
     * @param firstName:
     * @param lastName:
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023-04-13 11:28
     * @since 1.0.0
     */
    String getPersonAllName(String firstName, String lastName);

    /**
     * 根据事件编号获取事件信息
     *
     * @param devEvent:
     * @param eventNo:
     * @return com.zkteco.zkbiosecurity.acc.vo.AccDeviceEventItem
     * <AUTHOR>
     * @throws
     * @date 2023-08-17 10:07
     * @since 1.0.0
     */
    AccDeviceEventItem getEventByEventNo(List<AccDeviceEventItem> devEvent, short eventNo);

    /**
     * 根据logId查询
     * 
     * <AUTHOR>
     * @date 2024-10-24 14:46
     * @since 1.0.0
     */
    AccTransactionItem getItemByLogId(Integer logId);

    public void checkExceptionRecord();


    public String pushWechat(String type, String pin,
                             AccExceptionRecordItem accExceptionRecordItem);
}
