/**
 * File Name: AccDeviceEvent Created by GenerationTools on 2018-03-14 下午02:44 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccDeviceEventItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.util.List;
import java.util.Set;

/**
 * 对应百傲瑞达 AccDeviceEventService
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-03-14 下午02:44
 */
public interface AccDeviceEventService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     *
     * @param item
     * @param fileOpType 音频文件操作类型（选择的或者上传的）
     * @param syncAllDevice 是同步到所有设备的此事件类型
     */
    AccDeviceEventItem saveItem(AccDeviceEventItem item, String fileOpType, String syncAllDevice);

    /**
     * 根据条件查询
     *
     * @param condition
     * @return
     */
    List<AccDeviceEventItem> getByCondition(AccDeviceEventItem condition);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    AccDeviceEventItem getItemById(String id);

    /**
     * 判断事件类型是否是辅助输入事件
     *
     * @param triggerNoList 事件编号List
     * @return boolean
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2015年4月28日 上午10:56:57
     */
    boolean isAuxInEvent(List<Integer> triggerNoList);

    /**
     * 设置铃声
     */
    void setSound(String ids, String fileOpType, String baseMediaFileId, String baseMediaFileName,
        String baseMediaFilePath, String baseMediaFileSize, String baseMediaFileSuffix);

    /**
     * 根据事件编号确定设备事件的事件等级（正常、异常、报警）
     *
     * @param eventNo
     * @return
     */
    short getDefaultEventTypeById(int eventNo);

    /**
     * @param devId
     * @param eventNo
     * @return
     * @Description: 根据设备id和事件编号获取设备事件
     * <AUTHOR>
     * @date 2018/6/7 16:24
     */
    AccDeviceEventItem getItemByDeviceIdAndEventNo(String devId, Short eventNo);

    /**
     * @param sessionId
     * @param codition
     * @param pageNo
     * @param pageSize
     * @return
     * @Description: 根据用户过滤显示设备事件记录
     * <AUTHOR>
     * @date 2018/6/20 11:22
     */
    Pager loadPagerByAuthFilter(String sessionId, AccDeviceEventItem codition, int pageNo, int pageSize);

    /**
     * @param alarmName
     * @param doorId
     * @return
     * @Description: 根据事件名称和门id获取事件
     * <AUTHOR>
     * @date 2018/6/22 11:59
     */
    List<AccDeviceEventItem> getItemsByNameAndDoorId(String alarmName, String doorId);

    /**
     * 获取全局联动触发的事件
     *
     * @return
     */
    List<AccDeviceEventItem> getGlobalLinkTriggerEvent();

    /**
     * 设备事件迁移
     *
     * @param items
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午10:00:59
     */
    void handlerTransfer(List<AccDeviceEventItem> items);

    /**
     * 获取系统中所有设备的事件类型名称无重复
     *
     * @return
     */
    List<AccDeviceEventItem> getAllEventNameDistinct();

    /**
     * 获取常量中不重复的设备事件
     *
     * @return java.util.Set<java.lang.String>
     * <AUTHOR>
     * @date 2022-09-09 10:04
     * @since 1.0.0
     */
    Set<String> getAllEventNameSet();

    /**
     * 判断事件是否为辅助输入事件
     *
     * @param eventNo
     * @return
     */
    boolean isAuxinEventByNo(Integer eventNo);

    /**
     * 判断事件编号是否是设备事件
     *
     * @param eventNo
     * @return
     */
    boolean isDeviceEventByNo(Integer eventNo);

    /**
     * 判断事件是否是辅助输出事件
     * 
     * <AUTHOR>
     * @date 2025-03-03 17:56
     * @since 1.0.0
     */
    boolean isAuxOutEventByNo(Integer eventNo);

    /**
     * 判断事件是否是扩展板事件
     * 
     * <AUTHOR>
     * @date 2025-03-03 17:59
     * @since 1.0.0
     */
    boolean isExtBoardEventByNo(Integer eventNo);

    /**
     * 判断事件是否是读头事件
     * 
     * <AUTHOR>
     * @date 2025-03-03 18:07
     * @since 1.0.0
     */
    boolean isReaderEventByNo(Integer eventNo);

    /**
     * 判断事件编号是否是联动过滤事件
     *
     * @param eventNo
     * @return
     */
    boolean isLinkTriggerFilterEventByNo(Integer eventNo);

    /**
     * 判断事件编号是否是联动事件
     *
     * @param eventNo 事件编号
     * @return 判断结果
     * <AUTHOR>
     * @date 2021/2/1 14:44
     */
    boolean isLinkageEventByNo(short eventNo);

    /**
     * 保存Item实体，无复杂操作逻辑
     *
     * @param item:事件信息
     * @return com.zkteco.zkbiosecurity.acc.vo.AccDeviceEventItem
     * <AUTHOR>
     * @date 2021-01-25 14:20
     * @since 1.0.0
     */
    AccDeviceEventItem saveSimpleItem(AccDeviceEventItem item);

    /**
     * 同步音频文件到所有设备的相同事件类型
     *
     * @param baseMediaFileId:文件id
     * @param eventNo:事件编号
     * @return void
     * <AUTHOR>
     * @date 2021-01-25 10:50
     * @since 1.0.0
     */
    void syncAllDeviceEvent(String baseMediaFileId, Short eventNo);

    /**
     * 批量保存设备事件信息
     *
     * @param eventItemList:事件信息
     * @return void
     * <AUTHOR>
     * @date 2021-02-04 15:33
     * @since 1.0.0
     */
    void saveAccDeviceEventList(List<AccDeviceEventItem> eventItemList);

    /**
     * 获取全局联动触发的事件
     *
     * @param commType 通信协议类型
     * @param filterEventList 过滤事件
     * <AUTHOR>
     * @since 2021-11-10 14:56
     */
    List<AccDeviceEventItem> getGlobalLinkTriggerEvent(short commType, List<Short> filterEventList);

    /**
     * 判断全局联动触发条件是否是和人员直接相关的条件
     *
     * @param eventNo
     * @return
     * <AUTHOR>
     * @Date 2021/12/17 17:01
     */
    boolean isPersTriggerFilter(Integer eventNo);

    /**
     * 通过设备id和事件编号获取优先级
     * 
     * @param devId:
     * @param eventNo:
     * @return java.lang.Short
     * <AUTHOR>
     * @throws
     * @date 2022-06-22 15:29
     * @since 1.0.0
     */
    Short getEventPriorityByDevIdAndEventNo(String devId, Short eventNo);

    /**
     * 通过设备SN和事件编号获取优先级
     *
     * @param devSn:
     * @param eventNo:
     * @return java.lang.Short
     * <AUTHOR>
     * @date  2024/4/28 18:04
     * @since 1.0.0
     */
    Short getEventPriorityByDevSnAndEventNo(String devSn, Short eventNo);
}