/**
 * File Name: AccLinkage Created by GenerationTools on 2018-03-16 下午04:41 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONArray;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 对应百傲瑞达 AccLinkageService
 * 
 * <AUTHOR>
 * @date: 2018-03-16 下午04:41
 * @version v1.0
 */
public interface AccLinkageService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    AccLinkageItem saveItem(AccLinkageBeanItem accLinkageBeanItem, AccLinkageItem item);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AccLinkageItem> getByCondition(AccLinkageItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    AccLinkageItem getItemById(String id);

    /**
     * 获取过滤设备id
     *
     * @author: mingfa.zheng
     * @date: 2018/3/19 14:32
     * @return:
     */
    String getAllFilterId();

    /**
     * 获取编辑联动返回给前端的参数
     * 
     * @author: mingfa.zheng
     * @date: 2018/3/19 14:32
     * @return:
     */
    AccLinkageItem getLinkageParams(String id);

    /**
     * 检测是否重复设置联动，重复则返回重复设置的触发条件
     *
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2015年6月2日 下午3:32:42
     * @param devId 设备id
     * @param triggerCondArray 触发条件
     * @param inAddrArray 输入点
     * @param linkageId 联动id
     * @return JSONArray
     */
    ZKResultMsg checkTriggerOpt(String devId, String[] triggerCondArray, String[] inAddrArray, String linkageId);

    /**
     * 检测是否设置邮箱
     *
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2015年4月23日 下午6:20:09
     * @return 是否设置邮箱
     */
    boolean completeMailInfo();

    /**
     * 判断名称是否有效/允许
     * 
     * @author: mingfa.zheng
     * @date: 2018/3/15 16:09
     * @return:
     */
    AccLinkageItem getItemByName(String name);

    /**
     * 获取联动触发事件
     *
     * <AUTHOR>
     * @since 2014年8月22日 下午4:19:55
     * @param deviceId 设备ID
     * @param accLinkageId 联动对象的ID
     * @return ZKResultMsg
     * @throws Exception 异常
     */
    Map<String, Object> getLinkTriggerOpt(String deviceId, String accLinkageId);

    /**
     * 获取联动输入、输出
     *
     * <AUTHOR>
     * @since 2014年8月25日 下午2:25:32
     * @param devId 设备id
     * @param triggerOpt 触发条件String数组
     * @param accLinkageId 联动对象Id
     * @return JSONObject
     * @throws Exception 异常
     */
    Map<String, Object> getInOutInfo(String devId, String[] triggerOpt, String accLinkageId);

    /**
     * 获取设备缓存的联动信息
     *
     * @param devSn:设备序列号
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccQueryLinkageItem>
     * <AUTHOR>
     * @date 2022-07-20 11:11
     * @since 1.0.0
     */
    List<AccQueryLinkageItem> getAccQueryLinkageItemList(String devSn);

    Map<String, String> getLinkageAction(int linkIndex, String devId);

    /**
     * 获取联动触发条件
     * 
     * @author: mingfa.zheng
     * @date: 2018/4/26 8:46
     * @return:
     */
    List<String> getTriggerCondByLinkId(String linkageId);

    /**
     * 根据linkindex和设备id获得联动数据库id
     *
     * <AUTHOR>
     * @since 2018年5月4日 下午7:44:18
     * @param linkIndex
     * @param devId 设备id
     * @return Integer
     * @throws Exception 异常
     */
    String getLinkageIdByIndexAndDevId(int linkIndex, String devId);

    /**
     * 联动迁移
     *
     * @param accLinkageItems
     */
    void handlerTransfer(List<AccLinkageItem> accLinkageItems);

    /**
     * 检测是否设置SMS
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2019-11-12 17:08
     * @param
     * @return boolean
     */
    boolean completeSMSModemInfo();

    /**
     * 校验是否显示短信设置
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2019-11-29 11:30
     * @param
     * @return boolean
     */
    boolean checkShowSMS();

    /**
     * 校验是否显示digifort设置
     * 
     * <AUTHOR>
     * @since 2020/7/14 17:30
     * @param
     * @return boolean
     */
    boolean checkShowDigifort();

    /**
     * 获取Digifort全局事件树
     * 
     * <AUTHOR>
     * @since 2020/7/15 16:52
     * @param linkageId
     * @param type
     * @return com.alibaba.fastjson.JSONArray
     */
    JSONArray getDigifortGlobalEvents(String linkageId, String type);

    /**
     * 根据设备id获取
     *
     * @param devId:设备id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccLinkageItem>
     * <AUTHOR>
     * @date 2021-01-15 17:54
     * @since 1.0.0
     */
    List<AccLinkageItem> getItemsByDevId(String devId);

    /**
     * 执行软联动，例如发送邮件、视频联动
     *
     * @param linkageId:联动id
     * @param lastTransaction:上一条触发联动的事件
     * @param newTransaction:当前事件
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2021-02-01 11:07
     * @since 1.0.0
     */
    ZKResultMsg executeExtendLinkage(String linkageId, AccTransactionItem lastTransaction,
        AccTransactionItem newTransaction);

    /**
     * 验证是否显示line通讯录设置
     *
     * @return boolean
     * <AUTHOR>
     * @throws
     * @date 2022-02-15 15:07
     * @since 1.0.0
     */
    boolean checkShowLine();

    /**
     * 获取line通讯录列表
     *
     * @param condition:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @throws
     * @date 2022-02-16 11:36
     * @since 1.0.0
     */
    Pager getNoExistLineContacts(AccLinkageSelectContactItem condition, int pageNo, int pageSize);

    /**
     * 根据LineContactId 获取通讯录信息
     *
     * @param lineContactId:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccLinkage4LineContactItem>
     * <AUTHOR>
     * @throws
     * @date 2022-02-16 15:55
     * @since 1.0.0
     */
    List<AccLinkage4LineContactItem> getLineContactsByIds(String lineContactId);

    /**
     * 判断是否有whatsapp的许可
     *
     * @return boolean
     * <AUTHOR>
     * @throws
     * @date 2022-02-21 18:08
     * @since 1.0.0
     */
    boolean checkShowWhatsapp();

    /**
     * 获取选入侵分区双列表数据
     *
     * @param sessionId:
     * @param condition:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2022-11-30 16:07
     * @since 1.0.0
     */
    Pager getSelectIasPartition(String sessionId, AccLinkageSelectIasPartitionItem condition, int pageNo, int pageSize);

    /**
     * 根据入侵分区ID，获取入侵分区树结构数据
     *
     * @param sessionId:
     * @param iasPartitionIds:
     * @return com.zkteco.zkbiosecurity.base.bean.TreeItem
     * <AUTHOR>
     * @date 2022-11-30 17:26
     * @since 1.0.0
     */
    TreeItem getIasPartitionTreeByIds(String sessionId, String iasPartitionIds);

    /**
     * 根据厂商获取布防类型
     *
     * @param manufacture:厂商
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2022-12-01 9:45
     * @since 1.0.0
     */
    ZKResultMsg getArmTypeByManufacture(String manufacture);

    /**
     * 获取ivr下拉框数据
     *
     * @return java.util.List<com.zkteco.zkbiosecurity.base.bean.SelectItem>
     * <AUTHOR>
     * @throws
     * @date 2024-04-16 9:02
     * @since 1.0.0
     */
    List<SelectItem> getVdbIvrSelectItems();

    /**
     * 获取可视对讲分机绑定信息
     *
     * @param condition:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @throws
     * @date 2024-04-16 10:23
     * @since 1.0.0
     */
    Pager getExtensionList(AccLinkageSelectExtensionItem condition, int pageNo, int pageSize);

    /**
     * 根据分机绑定id获取绑定对象信息
     *
     * @param extensionIds:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccLinkage4VdbExtensionItem>
     * <AUTHOR>
     * @throws
     * @date 2024-04-16 14:27
     * @since 1.0.0
     */
    List<AccLinkage4VdbExtensionItem> getVdbExtensionByIds(String extensionIds);

    /**
     * 判断是否支持云SIP
     *
     * @return java.lang.Boolean
     * <AUTHOR>
     * @throws
     * @date 2024-04-19 13:36
     * @since 1.0.0
     */
    Boolean supportCloudSip();
}