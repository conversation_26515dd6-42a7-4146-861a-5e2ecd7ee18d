/**
 * File Name: AccDeviceVerifyMode Created by GenerationTools on 2018-03-20 下午04:20 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccDeviceVerifyModeItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;

import java.util.List;
import java.util.Set;

/**
 * 对应百傲瑞达 AccDeviceVerifyModeService
 * 
 * <AUTHOR>
 * @date: 2018-03-20 下午04:20
 * @version v1.0
 */
public interface AccDeviceVerifyModeService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    AccDeviceVerifyModeItem saveItem(AccDeviceVerifyModeItem item);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<AccDeviceVerifyModeItem> getByCondition(AccDeviceVerifyModeItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    AccDeviceVerifyModeItem getItemById(String id);

    /**
     * 得到验证模式
     *
     * 创建时间：2014-7-21下午1:40:32
     *
     * <AUTHOR>
     * <AUTHOR>
     * @since 2015年1月15日 下午5:51:27
     * @param devSn
     * @return
     */
    String getVerifyMode(String devSn, String verifyModeNo);

    /**
     * 根据设备SN号获取门的验证方式编号集合
     *
     * <AUTHOR>
     * @since 2018年4月25日 上午10:19:49
     * @param sn
     * @return
     */
    List<Short> getVerifyNoBySN(String sn);

    /**
     * 验证方式迁移
     * 
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午10:13:17
     * @param items
     */
    void handlerTransfer(List<AccDeviceVerifyModeItem> items);

    /**
     * 根据设备ID获取验证方式
     *
     * @param deviceId:设备id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDeviceVerifyModeItem>
     * <AUTHOR>
     * @date 2020-11-10 14:48
     * @since 1.0.0
     */
    List<AccDeviceVerifyModeItem> getVerifyModeByDeviceId(String deviceId);

    /**
     * 根据新验证方式参数获取新验证方式编号
     *
     * @param verifyMode:验证方式编号
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020-11-16 10:21
     * @since 1.0.0
     */
    String getVerifyModeNosByNewVFStyles(String verifyMode);

    /**
     * 根据新验证方式参数获取新验证方式国际化key
     *
     * @param newVFStyles:
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020-11-16 11:43
     * @since 1.0.0
     */
    String getVerityModeNamesByNewVFStyles(String newVFStyles);

    /**
     * 获取设备支持且无重复的新验证方式
     *
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDeviceVerifyModeItem>
     * <AUTHOR>
     * @date 2020-11-30 17:32
     * @since 1.0.0
     */
    List<AccDeviceVerifyModeItem> getSupportNewVerifyModeTree();

    /**
     * 批量保存验证方式
     *
     * @param verifyModeList:验证方式
     * @return void
     * <AUTHOR>
     * @date 2021-02-04 15:39
     * @since 1.0.0
     */
    void saveAccDeviceVerifyModeList(List<AccDeviceVerifyModeItem> verifyModeList);

    /**
     * 根据设备id获取设备不重复的验证方式信息
     *
     * @param devIds:设备id
     * @return java.util.Set<java.lang.Object[]>
     * <AUTHOR>
     * @date 2021-02-08 9:47
     * @since 1.0.0
     */
    Set<Object[]> getCommonVerifyModeByDevId(List<String> devIds);

    /**
     * 判断是否支持新验证方式，获取验证方式名称
     *
     * @param devSn:设备序列号
     * @param verifyModeNo:验证方式编号
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021-6-18 15:42
     * @since 1.0.0
     */
    String getVerifyModeNameByDevSnAndVerifyModeNo(String devSn, String verifyModeNo);
}