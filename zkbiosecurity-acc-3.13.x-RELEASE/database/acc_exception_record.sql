-- 异常记录表创建脚本
-- 适用于 MySQL 数据库

CREATE TABLE `ACC_EXCEPTION_RECORD` (
  `ID` varchar(32) NOT NULL COMMENT '主键',
  `PIN` varchar(50) DEFAULT NULL COMMENT '工号',
  `NAME` varchar(100) DEFAULT NULL COMMENT '姓名',
  `DEPT_NAME` varchar(100) DEFAULT NULL COMMENT '部门名称',
  `DEPT_CODE` varchar(50) DEFAULT NULL COMMENT '部门编号',
  `RECEIVER_POSITION` varchar(100) DEFAULT NULL COMMENT '接收人员职位',
  `READER_NAME` varchar(100) DEFAULT NULL COMMENT '读头名称',
  `READER_ID` varchar(32) DEFAULT NULL COMMENT '读头ID',
  `ENTER_TIME` datetime DEFAULT NULL COMMENT '进入时间',
  `EXIT_TIME` datetime DEFAULT NULL COMMENT '外出时间',
  `SUBJECT` varchar(100) DEFAULT NULL COMMENT '主题（异常进出、迟到）',
  `EXCEPTION_STATUS` varchar(50) DEFAULT NULL COMMENT '异常状态（未闭环、已返回）',
  `SEND_TIME` datetime DEFAULT NULL COMMENT '发送时间',
  `STATUS` smallint(6) DEFAULT '0' COMMENT '状态（0:未发送 1:发送成功 2:发送失败）',
  `ERROR_MESSAGE` varchar(500) DEFAULT NULL COMMENT '错误信息',
  `AREA_NAME` varchar(100) DEFAULT NULL COMMENT '区域名称',
  `DEV_ALIAS` varchar(100) DEFAULT NULL COMMENT '设备名称',
  `DEV_ID` varchar(32) DEFAULT NULL COMMENT '设备ID',
  `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `CREATOR` varchar(50) DEFAULT NULL COMMENT '创建人',
  `UPDATER` varchar(50) DEFAULT NULL COMMENT '更新人',
  `REMARK` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`ID`),
  KEY `IDX_ACC_EXCEPTION_PIN` (`PIN`),
  KEY `IDX_ACC_EXCEPTION_DEPT_CODE` (`DEPT_CODE`),
  KEY `IDX_ACC_EXCEPTION_STATUS` (`STATUS`),
  KEY `IDX_ACC_EXCEPTION_ENTER_TIME` (`ENTER_TIME`),
  KEY `IDX_ACC_EXCEPTION_SEND_TIME` (`SEND_TIME`),
  KEY `IDX_ACC_EXCEPTION_SUBJECT` (`SUBJECT`),
  KEY `IDX_ACC_EXCEPTION_EXC_STATUS` (`EXCEPTION_STATUS`),
  KEY `IDX_ACC_EXCEPTION_CREATE_TIME` (`CREATE_TIME`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='异常记录表';

-- 插入测试数据（可选）
INSERT INTO `ACC_EXCEPTION_RECORD` (
  `ID`, `PIN`, `NAME`, `DEPT_NAME`, `DEPT_CODE`, `RECEIVER_POSITION`, 
  `READER_NAME`, `ENTER_TIME`, `EXIT_TIME`, `SUBJECT`, `EXCEPTION_STATUS`, 
  `STATUS`, `AREA_NAME`, `DEV_ALIAS`, `CREATOR`
) VALUES 
(
  UUID(), '001', '张三', '技术部', 'TECH001', '部门经理',
  '主门读头', '2025-07-21 08:30:00', NULL, '异常进出', '未闭环',
  0, '办公区域', '主门设备', 'system'
),
(
  UUID(), '002', '李四', '人事部', 'HR001', '人事主管',
  '侧门读头', '2025-07-21 09:15:00', '2025-07-21 18:30:00', '迟到', '已返回',
  1, '办公区域', '侧门设备', 'system'
),
(
  UUID(), '003', '王五', '财务部', 'FIN001', '财务经理',
  '后门读头', '2025-07-21 07:45:00', NULL, '异常进出', '未闭环',
  2, '办公区域', '后门设备', 'system'
);

-- 创建视图（可选，用于统计查询）
CREATE VIEW `V_ACC_EXCEPTION_STATISTICS` AS
SELECT 
  COUNT(*) as total_count,
  SUM(CASE WHEN STATUS = 0 THEN 1 ELSE 0 END) as unsent_count,
  SUM(CASE WHEN STATUS = 1 THEN 1 ELSE 0 END) as sent_count,
  SUM(CASE WHEN STATUS = 2 THEN 1 ELSE 0 END) as failed_count,
  SUM(CASE WHEN EXCEPTION_STATUS = '未闭环' THEN 1 ELSE 0 END) as unclosed_count,
  SUM(CASE WHEN EXCEPTION_STATUS = '已返回' THEN 1 ELSE 0 END) as returned_count,
  DATE(CREATE_TIME) as stat_date
FROM `ACC_EXCEPTION_RECORD`
GROUP BY DATE(CREATE_TIME)
ORDER BY stat_date DESC;
