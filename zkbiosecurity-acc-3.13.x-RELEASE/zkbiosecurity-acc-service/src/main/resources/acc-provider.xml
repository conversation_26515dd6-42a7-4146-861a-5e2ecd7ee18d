<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd ">
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccAntiPassbackService"
                   ref="accAntiPassbackServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccAuxInService" ref="accAuxInServiceImpl"
                   timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccAuxOutService" ref="accAuxOutServiceImpl"
                   timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccCombOpenCombService"
                   ref="accCombOpenCombServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccCombOpenDoorService"
                   ref="accCombOpenDoorServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccCombOpenPersonService"
                   ref="accCombOpenPersonServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccDeviceEventService"
                   ref="accDeviceEventServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService"
                   ref="accDeviceOptionServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccDeviceService" ref="accDeviceServiceImpl"
                   timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccDeviceVerifyModeService"
                   ref="accDeviceVerifyModeServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccDoorService" ref="accDoorServiceImpl"
                   timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccDSTimeService" ref="accDSTimeServiceImpl"
                   timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccFirstOpenService" ref="accFirstOpenServiceImpl"
                   timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccGlobalLinkageInService"
                   ref="accGlobalLinkageInServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccGlobalLinkageMediaService"
                   ref="accGlobalLinkageMediaServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccGlobalLinkageOutService"
                   ref="accGlobalLinkageOutServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccGlobalLinkageService"
                   ref="accGlobalLinkageService" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccGlobalLinkageTriggerService"
                   ref="accGlobalLinkageTriggerService" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccGlobalLinkageVidService"
                   ref="accGlobalLinkageVidServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccHolidayService" ref="accHolidayServiceImpl"
                   timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccInterlockService" ref="accInterlockServiceImpl"
                   timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccLevelService" ref="accLevelServiceImpl"
                   timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccLinkageInOutService"
                   ref="accLinkageInOutServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccLinkageMediaService"
                   ref="accLinkageMediaServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccLinkageService" ref="accLinkageServiceImpl"
                   timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccLinkageTriggerService"
                   ref="ccLinkageTriggerServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccMapPosService" ref="accMapPosServiceImpl"
                   timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccMapService" ref="accMapServiceImpl"
                   timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccParamService" ref="accParamServiceImpl"
                   timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccPersonLastAddrService"
                   ref="accPersonLastAddrServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccPersonLevelByDeptService"
                   ref="accPersonLevelByDeptServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccPersonLevelByLevelService"
                   ref="accPersonLevelByLevelServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccPersonLevelByPersonService"
                   ref="accPersonLevelByPersonServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccPersonService" ref="accPersonServiceImpl"
                   timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccReaderOptionService"
                   ref="accReaderOptionServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccReaderService" ref="accReaderServiceImpl"
                   timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccReaderZoneService" ref="accReaderZoneServiceImpl"
                   timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccRouteReaderService"
                   ref="accRouteReaderServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccRouteService" ref="accRouteServiceImpl"
                   timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccTimeSegService" ref="accTimeSegServiceImpl"
                   timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccTransactionService"
                   ref="accTransactionServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccVerifyModeRuleService"
                   ref="accVerifyModeRuleServiceImpl" timeout="10000"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.acc.service.AccZoneService" ref="accZoneServiceImpl"
                   timeout="10000"/>
</beans>