package com.zkteco.zkbiosecurity.acc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.dao.AccTransactionDao;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.service.AccAlarmMonitorService;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.utils.AccDataUtil;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.adms.service.AdmsDeviceService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.dashboard.service.Dashboard4AccService;
import com.zkteco.zkbiosecurity.dashboard.vo.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Transactional
public class Dashboard4AccServiceImpl implements Dashboard4AccService {
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccTransactionDao accTransactionDao;
    @Autowired
    private AccTransactionService accTransactionService;
    @Autowired(required = false)
    private AdmsDeviceService admsDeviceService;
    @Autowired
    private AccAlarmMonitorService accAlarmMonitorService;

    /**
     * 获取门禁设备总数
     * 
     * <AUTHOR>
     * @Date 2019/1/4 14:41
     * @param
     * @return java.lang.Long
     */
    @Override
    public Long getDeviceCount() {
        return accDeviceDao.count();
    }

    /**
     * 门禁门禁事件趋势 今日\本周\本月 时间点（time字段） 今日事件趋势： 0:00 - 23:00 OR本周事件趋势： "星期一", "星期二", "星期三", "星期四", "星期五","星期六", "星期日"
     * OR本月事件趋势： 1号 - 30号
     * 
     * <AUTHOR>
     * @Date 2018/12/26 14:30
     * @param timeType 判断是统计今日、本周、本月: day week month
     * @return
     */
    @Override
    public List<Dashboard4AccEventTrendsItem> getAccEventTrendsData(String timeType) {
        List<Dashboard4AccEventTrendsItem> list = new ArrayList<Dashboard4AccEventTrendsItem>();
        // 今日事件趋势
        if ("day".equals(timeType)) {
            list = getDateData();
        }
        // 本周事件趋势
        else if ("week".equals(timeType)) {
            list = getWeekData();
        }
        // 本月事件趋势
        else if ("month".equals(timeType)) {
            list = getMonthData();
        }
        return list;
    }

    /**
     * 按日期：获取当前日期门禁每小时发生的事件记录总数
     * 
     * <AUTHOR>
     * @since 2019-01-07 15:42
     * @Param []
     * @return
     */
    private List<Dashboard4AccEventTrendsItem> getDateData() {
        List<Dashboard4AccEventTrendsItem> itemList = new ArrayList<Dashboard4AccEventTrendsItem>();
        Date startDate = DateUtil.getTodayBeginTime();
        Date endDate = DateUtil.getTodayEndTime();
        List<Object[]> listTrans = accTransactionService.findByEventTime(startDate, endDate);
        String suffix = ":00";
        for (int i = 0; i < 24; i++) {
            Dashboard4AccEventTrendsItem item = new Dashboard4AccEventTrendsItem();
            item.setTime(i + suffix);
            item.setNumber(0 + "");
            itemList.add(item);
        }
        Map<String, Dashboard4AccEventTrendsItem> tempItemMap =
            Maps.newHashMap(CollectionUtil.listToKeyMap(itemList, Dashboard4AccEventTrendsItem::getTime));
        for (Object[] trans : listTrans) {
            Date time = (Date)trans[1];
            Dashboard4AccEventTrendsItem item = tempItemMap.get(time.getHours() + suffix);
            item.setNumber(Integer.parseInt(item.getNumber()) + Integer.parseInt(trans[0] + "") + "");
        }
        return itemList;
    }

    /**
     * 按星期：分别统计本周周一到周天发生的门禁事件记录的总数，
     * 
     * <AUTHOR>
     * @since 2019-01-07 15:43
     * @Param []
     * @return
     */
    private List<Dashboard4AccEventTrendsItem> getWeekData() {
        List<Dashboard4AccEventTrendsItem> itemList = new ArrayList<Dashboard4AccEventTrendsItem>();
        String[] weeks = {I18nUtil.i18nCode("common_monday"), I18nUtil.i18nCode("common_tuesday"),
            I18nUtil.i18nCode("common_wednesday"), I18nUtil.i18nCode("common_thursday"),
            I18nUtil.i18nCode("common_friday"), I18nUtil.i18nCode("common_saturday"),
            I18nUtil.i18nCode("common_sunday")};
        Date startDate = AccDataUtil.getWeekStartDate();
        Date endDate = DateUtil.getDayEndTime(DateUtil.getDateAfter(startDate, 6));
        List<Object[]> listTrans = accTransactionService.findByEventTime(startDate, endDate);
        for (int k = 0; k < 7; k++) {
            Dashboard4AccEventTrendsItem item = new Dashboard4AccEventTrendsItem();
            item.setTime(weeks[k]);
            item.setNumber(0 + "");
            itemList.add(item);
        }
        Map<String, Dashboard4AccEventTrendsItem> tempItemMap =
            Maps.newHashMap(CollectionUtil.listToKeyMap(itemList, Dashboard4AccEventTrendsItem::getTime));
        for (Object[] trans : listTrans) {
            Date time = (Date)trans[1];
            Dashboard4AccEventTrendsItem item = tempItemMap.get(weeks[DateUtil.getWeek(time).getNumber() - 1]);
            item.setNumber(Integer.parseInt(item.getNumber()) + Integer.parseInt(trans[0] + "") + "");
        }
        return itemList;
    }

    /**
     * 按月：分别统计当前月份每一天的门禁事件记录总数
     * 
     * <AUTHOR>
     * @since 2019-01-07 15:47
     * @Param []
     * @return
     */
    private List<Dashboard4AccEventTrendsItem> getMonthData() {
        List<Dashboard4AccEventTrendsItem> itemList = new ArrayList<Dashboard4AccEventTrendsItem>();
        String[] days = {I18nUtil.i18nCode("common_day_first"), I18nUtil.i18nCode("common_day_second"),
            I18nUtil.i18nCode("common_day_third"), I18nUtil.i18nCode("common_day_fourth"),
            I18nUtil.i18nCode("common_day_fifth"), I18nUtil.i18nCode("common_day_sixth"),
            I18nUtil.i18nCode("common_day_seventh"), I18nUtil.i18nCode("common_day_eighth"),
            I18nUtil.i18nCode("common_day_ninth"), I18nUtil.i18nCode("common_day_tenth"),
            I18nUtil.i18nCode("common_day_eleventh"), I18nUtil.i18nCode("common_day_twelfth"),
            I18nUtil.i18nCode("common_day_thirteenth"), I18nUtil.i18nCode("common_day_fourteenth"),
            I18nUtil.i18nCode("common_day_fifteenth"), I18nUtil.i18nCode("common_day_sixteenth"),
            I18nUtil.i18nCode("common_day_seventeenth"), I18nUtil.i18nCode("common_day_eighteenth"),
            I18nUtil.i18nCode("common_day_nineteenth"), I18nUtil.i18nCode("common_day_twentieth"),
            I18nUtil.i18nCode("common_day_twentyFirst"), I18nUtil.i18nCode("common_day_twentySecond"),
            I18nUtil.i18nCode("common_day_twentyThird"), I18nUtil.i18nCode("common_day_twentyFourth"),
            I18nUtil.i18nCode("common_day_twentyFifth"), I18nUtil.i18nCode("common_day_twentySixth"),
            I18nUtil.i18nCode("common_day_twentySeventh"), I18nUtil.i18nCode("common_day_twentyEighth"),
            I18nUtil.i18nCode("common_day_twentyNinth"), I18nUtil.i18nCode("common_day_thirtieth"),
            I18nUtil.i18nCode("common_day_thirtyFirst")};
        int year = DateUtil.getYear(new Date());
        int month = DateUtil.getMonth(new Date()) + 1;
        Date startDate = DateUtil.getStartMonthDate(year, month);
        Date endDate = DateUtil.getEndMonthDate(year, month);
        List<Object[]> listTrans = accTransactionService.findByEventTime(startDate, endDate);
        for (int i = 0; i < AccDataUtil.getCurrentMonthLastDay(); i++) {
            Dashboard4AccEventTrendsItem item = new Dashboard4AccEventTrendsItem();
            item.setTime(days[i]);
            item.setNumber(0 + "");
            itemList.add(item);
        }
        Map<String, Dashboard4AccEventTrendsItem> tempItemMap =
            Maps.newHashMap(CollectionUtil.listToKeyMap(itemList, Dashboard4AccEventTrendsItem::getTime));
        for (Object[] trans : listTrans) {
            Date time = (Date)trans[1];
            Dashboard4AccEventTrendsItem item = tempItemMap.get(days[DateUtil.getDay(time) - 1]);
            item.setNumber(Integer.parseInt(item.getNumber()) + Integer.parseInt(trans[0] + "") + "");
        }
        return itemList;
    }

    /**
     * 门禁异常事件TOP5
     * 
     * <AUTHOR>
     * @Date 2018/12/26 14:30
     * @param
     * @return
     */
    @Override
    public List<Dashboard4AccExceptionItem> getAccExceptionTopData() {
        List<Object[]> ExceptionCount = accTransactionDao.getExceptionEventCount();
        // 异常事件总数
        int eventTotal = 0;
        int top = 0;
        List<Dashboard4AccExceptionItem> list = new ArrayList<Dashboard4AccExceptionItem>();
        for (Object[] objArray : ExceptionCount) {
            eventTotal += Integer.parseInt(objArray[1] + "");
            // 本月异常事件top5
            if (top < 5) {
                Dashboard4AccExceptionItem exceptionItem = new Dashboard4AccExceptionItem();
                exceptionItem.setExceptionEventName(I18nUtil.i18nCode(objArray[0] + ""));
                exceptionItem.setNumber(objArray[1] + "");
                list.add(exceptionItem);
                top++;
            }
        }
        // 本月异常事件总数
        Dashboard4AccExceptionItem total = new Dashboard4AccExceptionItem();
        total.setExceptionEventName("total");
        total.setNumber(eventTotal + "");
        list.add(total);
        return list;
    }

    /**
     * 统计正常、异常、报警事件数
     * 
     * <AUTHOR>
     * @Date 2019/1/5 11:07
     * @param
     * @return
     */
    @Override
    public DashboardEventCountItem getEventCount() {
        DashboardEventCountItem item = new DashboardEventCountItem();
        // 正常、异常、报警事件数
        List<Object[]> eventCount = accTransactionDao.getEventCount();
        int normalEvent = 0;
        int exceptionEvent = 0;
        int alarmEvent = 0;
        int eventNo;
        for (Object[] objArray : eventCount) {
            eventNo = Integer.parseInt(objArray[0] + "");
            if ((eventNo >= 20 && eventNo < 100 && eventNo != 28) || (eventNo >= 500 && eventNo < 700)) {
                exceptionEvent += Integer.parseInt(objArray[1] + "");
            } else if (eventNo >= 100 && eventNo < 200 || eventNo == 28 || eventNo >= 700) {
                alarmEvent += Integer.parseInt(objArray[1] + "");
            } else {
                normalEvent += Integer.parseInt(objArray[1] + "");
            }
        }
        // 正常事件数
        item.setNormalevent(normalEvent);
        // 异常事件数
        item.setExceptionevent(exceptionEvent);
        // 报警事件数
        item.setAlarmevent(alarmEvent);
        // 总事件数
        item.setTotalevent(normalEvent + exceptionEvent + alarmEvent);
        return item;
    }

    /**
     * 获取门禁最新三条记录
     * 
     * <AUTHOR>
     * @Date 2019/1/5 11:06
     * @param
     * @return
     */
    @Override
    public List<Dashboard4TrasactionItem> getTransactionsLast() {
        List<Dashboard4TrasactionItem> list = new ArrayList<Dashboard4TrasactionItem>();
        Pager pager = accTransactionDao.getItemsBySql(AccTransactionItem.class,
            SQLUtil.getSqlByItem(new AccTransactionItem()), 0, 3);
        List<AccTransactionItem> accTransactionItems = (List<AccTransactionItem>)pager.getData();
        String status = "";
        for (AccTransactionItem accTransactionItem : accTransactionItems) {
            Dashboard4TrasactionItem item = new Dashboard4TrasactionItem();
            if (StringUtils.isNoneBlank(accTransactionItem.getName())) {
                item.setName(accTransactionItem.getPin() + "(" + accTransactionItem.getName() + ")");
            }
            if (StringUtils.isBlank(accTransactionItem.getEventPointName())) {
                item.setName(accTransactionItem.getDevAlias());
            }
            item.setEventPointName(accTransactionItem.getEventPointName());
            item.setEventName(I18nUtil.i18nCode(accTransactionItem.getEventName()));
            item.setEventTime(
                DateUtil.dateToString(accTransactionItem.getEventTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
            // 判断事件等级
            if ((accTransactionItem.getEventNo() >= 20 && accTransactionItem.getEventNo() < 100
                && accTransactionItem.getEventNo() != 28)
                || (accTransactionItem.getEventNo() >= 500 && accTransactionItem.getEventNo() < 600)) {
                status = "warning";
            } else if (accTransactionItem.getEventNo() >= 100 && accTransactionItem.getEventNo() < 200
                || accTransactionItem.getEventNo() == 28
                || (accTransactionItem.getEventNo() >= 700 && accTransactionItem.getEventNo() < 800)) {
                status = "alarm";
            } else {
                status = "normal";
            }
            item.setStatus(status);
            list.add(item);
        }
        return list;
    }

    @Override
    public DashboardDeviceCountItem getDeviceCountItem() {
        DashboardDeviceCountItem deviceCountItem = new DashboardDeviceCountItem();
        List<AccDevice> accDeviceList = accDeviceDao.findAll();
        if (!accDeviceList.isEmpty()) {
            deviceCountItem.setDeviceCount(accDeviceList.size());
            long onlineDeviceCount = 0;
            long offDeviceCount = 0;
            for (AccDevice dev : accDeviceList) {
                boolean isOnline = admsDeviceService.isOnline(dev.getSn());
                if (isOnline) {
                    onlineDeviceCount++;
                } else {
                    offDeviceCount++;
                }
            }
            deviceCountItem.setOnlineDeviceCount(onlineDeviceCount);
            deviceCountItem.setOfflineDeviceCount(offDeviceCount);
        }
        return deviceCountItem;
    }

    @Override
    public JSONObject getAccAlarmData(String sessionId) {
        return accAlarmMonitorService.getAccAlarmData(sessionId);
    }
}
