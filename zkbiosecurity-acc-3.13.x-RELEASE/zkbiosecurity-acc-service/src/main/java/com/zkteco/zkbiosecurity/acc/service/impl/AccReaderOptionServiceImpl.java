/**
 * File Name: AccReaderOptionServiceImpl Created by GenerationTools on 2018-03-13 上午10:06 Copyright:Copyright ©
 * 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.dao.AccReaderDao;
import com.zkteco.zkbiosecurity.acc.dao.AccReaderOptionDao;
import com.zkteco.zkbiosecurity.acc.model.AccReader;
import com.zkteco.zkbiosecurity.acc.model.AccReaderOption;
import com.zkteco.zkbiosecurity.acc.service.AccReaderOptionService;
import com.zkteco.zkbiosecurity.acc.vo.AccReaderOptionItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 对应百傲瑞达 AccReaderOptionServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-13 上午10:06
 * @version v1.0
 */
@Service
@Transactional
public class AccReaderOptionServiceImpl implements AccReaderOptionService {
    @Autowired
    private AccReaderOptionDao accReaderOptionDao;
    @Autowired
    private AccReaderDao accReaderDao;

    @Override
    public AccReaderOptionItem saveItem(AccReaderOptionItem item) {
        AccReaderOption accReaderOption = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accReaderOptionDao.findById(id)).orElse(new AccReaderOption());
        ModelUtil.copyProperties(item, accReaderOption);
        accReaderOptionDao.save(accReaderOption);
        item.setId(accReaderOption.getId());
        return item;
    }

    @Override
    public List<AccReaderOptionItem> getByCondition(AccReaderOptionItem condition) {
        return (List<AccReaderOptionItem>)accReaderOptionDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accReaderOptionDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accReaderOptionDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AccReaderOptionItem getItemById(String id) {
        List<AccReaderOptionItem> items = getByCondition(new AccReaderOptionItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public AccReaderOptionItem getByOptName(String readerId, String name) {
        AccReaderOptionItem accReaderOptionItem = null;
        AccReaderOption accReaderOption = accReaderOptionDao.findByAccReader_IdAndName(readerId, name);
        if (null != accReaderOption) {
            accReaderOptionItem = new AccReaderOptionItem();
            accReaderOptionItem.setId(accReaderOption.getId());
            accReaderOptionItem.setReaderId(accReaderOption.getAccReader().getId());
            accReaderOptionItem.setName(accReaderOption.getName());
            accReaderOptionItem.setValue(accReaderOption.getValue());
            accReaderOptionItem.setType(accReaderOption.getType());
        }
        return accReaderOptionItem;
    }

    @Override
    public List<AccReaderOptionItem> saveSimpleItem(List<AccReaderOptionItem> options) {
        if (Objects.nonNull(options) && !options.isEmpty()) {
            Map<String, AccReader> readerMap = new HashMap<>();
            for (AccReaderOptionItem readerOption : options) {
                AccReader accReader = accReaderDao.findOne(readerOption.getReaderId());
                if (Objects.nonNull(accReader)) {
                    readerMap.put(readerOption.getReaderId(), accReader);
                }
            }

            for (AccReaderOptionItem optionItem : options) {
                AccReaderOption accReaderOption = ModelUtil.copyProperties(optionItem, new AccReaderOption());
                AccReader accReader = readerMap.get(optionItem.getReaderId());
                accReaderOption.setAccReader(accReader);
                // 保存读头参数信息
                accReaderOptionDao.save(accReaderOption);
                optionItem.setId(accReaderOption.getId());
            }
        }
        return options;
    }

    @Override
    public List<AccReaderOptionItem> getItemsByReaderId(String readerId) {
        List<AccReaderOptionItem> accReaderOptionItems = new ArrayList<>();
        if (StringUtils.isNotBlank(readerId)) {
            List<AccReaderOption> accReaderOptionList = accReaderOptionDao.findByAccReader_Id(readerId);
            accReaderOptionItems = ModelUtil.copyListProperties(accReaderOptionList, AccReaderOptionItem.class);
        }
        return accReaderOptionItems;
    }

    @Override
    public List<AccReaderOptionItem> getItemsByReaderIdAndOptType(String readerId, Short optType) {
        List<AccReaderOptionItem> accReaderOptionItems = new ArrayList<>();
        if (StringUtils.isNotBlank(readerId) && Objects.nonNull(optType)) {
            List<AccReaderOption> readerOpts = accReaderOptionDao.findByAccReader_IdAndType(readerId, optType);
            if (Objects.nonNull(readerOpts)) {
                for (AccReaderOption readerOption : readerOpts) {
                    AccReaderOptionItem item = ModelUtil.copyProperties(readerOption, new AccReaderOptionItem());
                    item.setReaderId(readerOption.getAccReader().getId());
                    item.setReaderNo(readerOption.getAccReader().getReaderNo());
                    accReaderOptionItems.add(item);
                }
            }
        }
        return accReaderOptionItems;
    }
}