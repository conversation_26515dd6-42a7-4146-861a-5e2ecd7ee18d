package com.zkteco.zkbiosecurity.acc.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.vo.AccLevelItem;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthUserItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.EncrypAESUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.vis.service.VisGetAccLevelService;
import com.zkteco.zkbiosecurity.vis.vo.VisGetAccLevelItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
/**
 * 访客获取门禁权限组
 * <AUTHOR>
 * @since 2018年11月22日 下午2:03:18
 */
@Service
@Transactional
public class VisGetAccLevelServiceImpl implements VisGetAccLevelService
{

	@Autowired
	private AccLevelService accLevelService;
	@Autowired
	private AuthUserService authUserService;
	
	@Override
	public Pager loadPagerByAuthFilter(String sessionId, VisGetAccLevelItem visGetAccLevelItem, int pageNo, int pageSize)
	{
		AccLevelItem accLevelItem = new AccLevelItem();
		ModelUtil.copyProperties(visGetAccLevelItem,accLevelItem);
		Pager pager = accLevelService.loadPagerByAuthFilter(sessionId, accLevelItem, pageNo, pageSize);
		List<AccLevelItem> accLevelItemList = (List<AccLevelItem>) pager.getData();
		List<VisGetAccLevelItem> visGetAccLevelItemList = ModelUtil.copyListProperties(accLevelItemList, VisGetAccLevelItem.class);
		pager.setData(visGetAccLevelItemList);
		return pager;
	}

	@Override
	public Object getItemById(String moduleId)
	{
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ZKResultMsg getAllAccLevels(String token, List<String> visLevelIds, String levelName, int pageNo, int pageSize) {
		JSONArray accLevelArray = new JSONArray();

		AccLevelItem accLevelItem = new AccLevelItem();
		accLevelItem.setName(levelName);
		token =  EncrypAESUtil.decryptToString(token);
		String username = token.split("_")[0];
		AuthUserItem authUserItem = authUserService.getItemByUsername(username);
		if (authUserItem != null && !authUserItem.getIsSuperuser()){  //非超级管理员根据所拥有区域权限过滤
			if (StringUtils.isNotBlank(authUserItem.getAreaIds())){
				accLevelItem.setAreaIds(authUserItem.getAreaIds());
			} else { //如果没设置区域权限则不显示
				accLevelItem.setAreaIds("-1");
			}
		}
		List<AccLevelItem> accLevelItemList = (List<AccLevelItem>) accLevelService.getItemsByPage(accLevelItem, pageNo, pageSize).getData();
		accLevelItemList.forEach(accLevel -> {
			if(!visLevelIds.contains(accLevel.getId())) {//visLevelIds为已经添加过的权限id，已添加的不显示在前端
				JSONObject accLevelObject = new JSONObject();
				if((StringUtils.isNotBlank(levelName) && accLevel.getName().contains(levelName)) || StringUtils.isBlank(levelName)) {
					accLevelObject.put("accLevelId", accLevel.getId()); //权限id
					accLevelObject.put("accLevelName", accLevel.getName()); //权限组名称
					accLevelObject.put("selected", false); //表示未添加
					accLevelObject.put("accTimeSegName", accLevel.getTimeSegName());
					accLevelArray.add(accLevelObject);
				}
			}
		});

		return new ZKResultMsg(accLevelArray);
	}

	@Override
	public Pager getItemsPager(VisGetAccLevelItem visGetAccLevelItem, int pageNo, int pageSize)
	{
		AccLevelItem accLevelItem = new AccLevelItem();
		ModelUtil.copyProperties(visGetAccLevelItem,accLevelItem);
		Pager pager = accLevelService.getItemsByPage(accLevelItem, pageNo, pageSize);
		List<AccLevelItem> accLevelItemList = (List<AccLevelItem>) pager.getData();
		List<VisGetAccLevelItem> visGetAccLevelItemList = ModelUtil.copyListProperties(accLevelItemList, VisGetAccLevelItem.class);
		pager.setData(visGetAccLevelItemList);
		return pager;
	}
}
