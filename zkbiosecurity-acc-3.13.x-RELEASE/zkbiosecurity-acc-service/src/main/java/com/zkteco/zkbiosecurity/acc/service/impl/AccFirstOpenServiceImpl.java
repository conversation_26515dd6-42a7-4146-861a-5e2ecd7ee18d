/**
 * File Name: AccFirstOpenServiceImpl Created by GenerationTools on 2018-03-14 下午03:02 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;

import com.zkteco.zkbiosecurity.acc.vo.AccFirstOpenSelectDoorItem;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccDoorDao;
import com.zkteco.zkbiosecurity.acc.dao.AccFirstOpenDao;
import com.zkteco.zkbiosecurity.acc.dao.AccPersonFirstOpenDao;
import com.zkteco.zkbiosecurity.acc.dao.AccTimeSegDao;
import com.zkteco.zkbiosecurity.acc.enums.AccLinkTypeEnum;
import com.zkteco.zkbiosecurity.acc.model.AccDoor;
import com.zkteco.zkbiosecurity.acc.model.AccFirstOpen;
import com.zkteco.zkbiosecurity.acc.model.AccPersonFirstOpen;
import com.zkteco.zkbiosecurity.acc.model.AccTimeSeg;
import com.zkteco.zkbiosecurity.acc.service.AccFirstOpenService;
import com.zkteco.zkbiosecurity.acc.vo.AccFirstOpenItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonFirstOpenItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonOptItem;
import com.zkteco.zkbiosecurity.auth.provider.AuthSessionProvider;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonLinkService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 对应百傲瑞达 AccFirstOpenServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-14 下午03:02
 * @version v1.0
 */
@Service
@Transactional
public class AccFirstOpenServiceImpl implements AccFirstOpenService {
    @Autowired
    private AccFirstOpenDao accFirstOpenDao;
    @Autowired
    private AccTimeSegDao accTimeSegDao;
    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccPersonFirstOpenDao accPersonFirstOpenDao;
    @Autowired
    private PersPersonLinkService persPersonLinkService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AuthSessionProvider authSessionProvider;
    @Autowired
    private AuthUserService authUserService;

    @Override
    public AccFirstOpenItem saveItem(AccFirstOpenItem item) {
        AccFirstOpen accFirstOpen = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accFirstOpenDao.findById(id)).orElse(new AccFirstOpen());
        boolean isImmeToDev = false;
        // 如果是编辑，则要判断是否修改了时间段和门，如果修改了需要重新下发命令。
        if (StringUtils.isNotBlank(accFirstOpen.getId())) {
            if (!accFirstOpen.getTimeSegId().equals(item.getTimeSegId())) {
                isImmeToDev = true;
            }
        }
        ModelUtil.copyPropertiesIgnoreNull(item, accFirstOpen);
        AccDoor accDoor = accDoorDao.findById(item.getDoorId()).orElse(new AccDoor());
        accFirstOpen.setAccDoor(accDoor);
        if (isImmeToDev) {
            if (accFirstOpen.getAccPersonFirstOpenSet() != null && accFirstOpen.getAccPersonFirstOpenSet().size() > 0) {
                List<String> personIds =
                    (List<String>)CollectionUtil.getPropertyList(accFirstOpen.getAccPersonFirstOpenSet(),
                        AccPersonFirstOpen::getPersPersonId, "-1");
                List<String> pinList = new ArrayList<>(persPersonService.getPinsByPersonIds(personIds).values());
                if (pinList.size() > 0)// 修改后该组是否还有人，有人则重新下发命令，否则不下发
                {
                    accDevCmdManager.delFirstOpenFromDev(buildItemInfo(accFirstOpen), false);
                    accDevCmdManager.setFirstOpenToDev(buildItemInfo(accFirstOpen), pinList, false);
                }
            }
        }
        accFirstOpenDao.save(accFirstOpen);
        item.setId(accFirstOpen.getId());
        return item;
    }

    /**
     * 实体对象->VO对象
     *
     * @param accFirstOpen:实体对象
     * @return com.zkteco.zkbiosecurity.acc.vo.AccFirstOpenItem
     * <AUTHOR>
     * @date 2021-02-07 10:05
     * @since 1.0.0
     */
    private AccFirstOpenItem buildItemInfo(AccFirstOpen accFirstOpen) {
        AccFirstOpenItem item = new AccFirstOpenItem();
        item.setId(accFirstOpen.getId());
        AccDoor accDoor = accFirstOpen.getAccDoor();
        item.setDeviceSn(accDoor.getDevice().getSn());
        item.setDeviceAlias(accDoor.getDevice().getAlias());
        item.setDoorId(accDoor.getId());
        item.setDoorNo(accDoor.getDoorNo() + "");
        item.setTimeSegId(accFirstOpen.getTimeSegId());
        return item;
    }

    @Override
    public List<AccFirstOpenItem> getByCondition(AccFirstOpenItem condition) {
        return (List<AccFirstOpenItem>)accFirstOpenDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accFirstOpenDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            List<String> idList = (List<String>)CollectionUtil.strToList(ids);
            idList.stream().forEach(id -> {
                AccFirstOpen accFirstOpen = accFirstOpenDao.findById(id).orElse(new AccFirstOpen());
                if (accFirstOpen.getAccPersonFirstOpenSet() != null
                    && accFirstOpen.getAccPersonFirstOpenSet().size() > 0) {
                    // 首人开门存在人员才下发删除命令到设备
                    accDevCmdManager.delFirstOpenFromDev(buildItemInfo(accFirstOpen), false);
                    accPersonFirstOpenDao.delete(accFirstOpen.getAccPersonFirstOpenSet());
                }
                accFirstOpenDao.deleteById(id);
            });
        }
        return false;
    }

    @Override
    public AccFirstOpenItem getItemById(String id) {
        List<AccFirstOpenItem> items = getByCondition(new AccFirstOpenItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public String getFilterDoorId() {
        StringBuilder filterDoorId = new StringBuilder();
        List<AccFirstOpen> accFirstOpenList = accFirstOpenDao.findAll();
        for (AccFirstOpen accFirstOpen : accFirstOpenList) {
            filterDoorId.append(accFirstOpen.getAccDoor().getId()).append(",");
        }
        return StringUtils.isNotBlank(filterDoorId) ? filterDoorId.substring(0, filterDoorId.length() - 1) : "-1";
    }

    @Override
    public ZKResultMsg addPerson(String firstOpenId, List<String> personIdList) {
        AccFirstOpen accFirstOpen = accFirstOpenDao.findById(firstOpenId).orElse(new AccFirstOpen());
        // 进行分批处理
        List<List<String>> personIdsList = CollectionUtil.split(personIdList, AccConstants.LEVEL_SPLIT_COUNT);
        personIdsList.forEach(personIds -> {
            List<AccPersonFirstOpen> addAccPersonFirstOpenList = new ArrayList<>();
            List<String> addPersonIdList = new ArrayList<>();
            List<AccPersonFirstOpen> oldAccPersonFirstOpenList =
                accPersonFirstOpenDao.findByAccFirstOpenAndPersPersonIdIn(accFirstOpen, personIds);
            // 根据人员id进行分组
            Map<String, AccPersonFirstOpen> oldAccPersonFirstOpenMap =
                CollectionUtil.listToKeyMap(oldAccPersonFirstOpenList, AccPersonFirstOpen::getPersPersonId);
            personIds.forEach(personId -> {
                AccPersonFirstOpen accPersonFirstOpen = oldAccPersonFirstOpenMap.get(personId);
                // 新增才入库
                if (accPersonFirstOpen == null) {
                    accPersonFirstOpen = new AccPersonFirstOpen();
                    accPersonFirstOpen.setPersPersonId(personId);
                    accPersonFirstOpen.setAccFirstOpen(accFirstOpen);
                    addPersonIdList.add(personId);
                    addAccPersonFirstOpenList.add(accPersonFirstOpen);
                }
            });
            Map<String, String> personMap = persPersonService.getPinsByPersonIds(addPersonIdList);
            List<String> personPinList = new ArrayList<>(personMap.values());
            accDevCmdManager.setFirstOpenToDev(buildItemInfo(accFirstOpen), personPinList, false);
            accPersonFirstOpenDao.save(addAccPersonFirstOpenList);
        });
        // 获取返回的人数
        Long personCount = accPersonFirstOpenDao.countByAccFirstOpen_Id(firstOpenId);
        personCount = personCount == null ? Long.valueOf(0) : personCount;
        return new ZKResultMsg(personCount);
    }

    @Override
    public ZKResultMsg delPerson(String firstOpenId, String personIds) {
        AccFirstOpen accFirstOpen = accFirstOpenDao.findById(firstOpenId).orElse(new AccFirstOpen());
        if (StringUtils.isNotBlank(personIds)) {
            List<String> personIdList = (List<String>)CollectionUtil.strToList(personIds);
            List<AccPersonFirstOpen> delPersonFirstOpenList =
                accPersonFirstOpenDao.findByAccFirstOpenAndPersPersonIdIn(accFirstOpen, personIdList);
            accPersonFirstOpenDao.delete(delPersonFirstOpenList);
            // 门禁删除人员时，要将人事模块的中间表删除。
            persPersonLinkService.deleteBatchItem(AccLinkTypeEnum.ACC_FIRSTOPEN,
                (List<String>)CollectionUtil.strToList(firstOpenId), personIdList);
            // 防止报错
            if (accFirstOpen.getAccDoor() != null) {
                List<String> pinList = persPersonService.getPinsByIds(personIdList);
                accDevCmdManager.delFirstOpenFromDevByPins(accFirstOpen, pinList, false);
            }
        }
        // 获取返回的人数
        Long personCount = accPersonFirstOpenDao.countByAccFirstOpen_Id(firstOpenId);
        personCount = personCount == null ? Long.valueOf(0) : personCount;
        return new ZKResultMsg(personCount);
    }

    @Override
    public void delFirstOpenFromDev(List<AccPersonOptItem> personInfoList) {
        if (personInfoList != null && personInfoList.size() > 0) {
            List<String> personIdList = new ArrayList<String>();
            List<String> pinList = new ArrayList<String>();
            for (AccPersonOptItem personInfo : personInfoList) {
                personIdList.add(personInfo.getId());
                pinList.add(personInfo.getPin());
            }
            List<AccPersonFirstOpen> accPersonFirstOpenList = accPersonFirstOpenDao.findByPersPersonIdIn(personIdList);
            accPersonFirstOpenList.stream().forEach(accPersonFirstOpen -> {
                if (accPersonFirstOpen.getAccFirstOpen() != null) {
                    accDevCmdManager.delFirstOpenFromDev(buildItemInfo(accPersonFirstOpen.getAccFirstOpen()), false);
                }
            });
            accPersonFirstOpenDao.deleteInBatch(accPersonFirstOpenList);
        }
    }

    @Override
    public List<AccFirstOpenItem> getItemsByDoorIds(List<String> doorIdList) {
        AccFirstOpenItem accFirstOpenItem = new AccFirstOpenItem();
        accFirstOpenItem.setDoorIdsIn(StringUtils.join(doorIdList, ","));
        return getByCondition(accFirstOpenItem);
    }

    @Override
    public ZKResultMsg getPersonCount(String sessionId, String firstOpenId) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        SecuritySubject subject = authSessionProvider.getSecuritySubject(sessionId);
        if (!subject.getIsSuperuser() && subject.getDepartmentIds() != null && subject.getDepartmentIds().size() > 0) {
            List<PersPersonItem> persPersonItemList =
                persPersonService.getPersPersonByDeptIds(subject.getDepartmentIds());
            zkResultMsg.setData(accPersonFirstOpenDao.countByAccFirstOpen_IdAndPersPersonIdIn(firstOpenId,
                CollectionUtil.getItemIdsList(persPersonItemList)));
            return zkResultMsg;
        }
        zkResultMsg.setData(accPersonFirstOpenDao.countByAccFirstOpen_Id(firstOpenId));
        return zkResultMsg;
    }

    @Override
    public void handlerTransfer(List<AccFirstOpenItem> accFirstOpenItems) {
        List<List<AccFirstOpenItem>> accFirstOpenList =
            CollectionUtil.split(accFirstOpenItems, CollectionUtil.splitSize);
        for (List<AccFirstOpenItem> accFirstOpenItemList : accFirstOpenList) {
            // 获取首人常开集合对象 去重
            List<AccFirstOpen> accFirstOpens = accFirstOpenDao.findAll();
            Map<AccDoor, AccFirstOpen> accFirstOpenMap =
                CollectionUtil.listToKeyMap(accFirstOpens, AccFirstOpen::getAccDoor);
            // 获取时间段对象集合，封装成map 减少查询
            // 获取时间段对象根据时间段id
            Collection<String> ids =
                CollectionUtil.getPropertyList(accFirstOpenItemList, AccFirstOpenItem::getTimeSegId, "-1");
            List<Long> longList = new ArrayList<>();
            for (String str : ids) {
                Long i = Long.parseLong(str);
                longList.add(i);
            }
            List<AccTimeSeg> accTimeSegs = accTimeSegDao.findByBusinessIdIn(longList);
            Map<Long, AccTimeSeg> accTimeSegMap = CollectionUtil.listToKeyMap(accTimeSegs, AccTimeSeg::getBusinessId);
            for (AccFirstOpenItem accFirstOpenItem : accFirstOpenItemList) {
                // 创建对象
                AccFirstOpen accFirstOpen = new AccFirstOpen();
                // 查询门对象根据门编号以及设备号
                AccDoor accDoor = accDoorDao.findByDoorNoAndDevice_Sn(Short.parseShort(accFirstOpenItem.getDoorNo()),
                    accFirstOpenItem.getDeviceSn());
                if (Objects.nonNull(accDoor)) {
                    accFirstOpen.setAccDoor(accDoor);
                }
                AccTimeSeg accTimeSeg = accTimeSegMap.get(Long.valueOf(accFirstOpenItem.getTimeSegId()));
                if (Objects.nonNull(accTimeSeg)) {
                    accFirstOpen.setTimeSegId(accTimeSeg.getId());
                }
                accFirstOpenDao.save(accFirstOpen);
            }
        }
    }

    @Override
    public boolean checkTimeSegUsed(String timeSegId) {
        return accFirstOpenDao.countByTimeSegId(timeSegId) > 0;
    }

    @Override
    public List<AccPersonFirstOpenItem> getAccPersonFirstOpenItemById(String accFirstOpenId) {
        List<AccPersonFirstOpenItem> firstOpenItemList = new ArrayList<>();
        if (StringUtils.isNotBlank(accFirstOpenId)) {
            List<AccPersonFirstOpen> accPersonFirstOpenList =
                accPersonFirstOpenDao.findByAccFirstOpen_Id(accFirstOpenId);
            for (AccPersonFirstOpen firstOpen : accPersonFirstOpenList) {
                AccPersonFirstOpenItem firstOpenItem = new AccPersonFirstOpenItem();
                firstOpenItem.setPersPersonId(firstOpen.getPersPersonId());
                firstOpenItem.setAccFirstOpenId(firstOpen.getId());
                firstOpenItem.setId(firstOpen.getId());
                firstOpenItemList.add(firstOpenItem);
            }
        }
        return firstOpenItemList;
    }

    @Override
    public List<?> getItemsData(Class<?> cls, BaseItem condition) {
        return accFirstOpenDao.getItemsBySql(cls, SQLUtil.getSqlByItem(condition));
    }

    @Override
    public List<String> getPersonFirstOpenIdsByFirstOpenIdAndPersonIds(String firstOpenId, List<String> personIds) {
        List<String> personFirstOpenIds = new ArrayList<>();
        List<AccPersonFirstOpen> accPersonFirstOpenList =
            accPersonFirstOpenDao.findByAccFirstOpen_IdAndPersPersonIdIn(firstOpenId, personIds);
        if (Objects.nonNull(accPersonFirstOpenList) && !accPersonFirstOpenList.isEmpty()) {
            personFirstOpenIds =
                (List<String>)CollectionUtil.getPropertyList(accPersonFirstOpenList, AccPersonFirstOpen::getId, "-1");
        }
        return personFirstOpenIds;
    }

    @Override
    public void deleteAccPersonFirstOpen(List<String> accPersonFirstOpenIds) {
        if (Objects.nonNull(accPersonFirstOpenIds) && !accPersonFirstOpenIds.isEmpty()) {
            for (String id : accPersonFirstOpenIds) {
                accPersonFirstOpenDao.deleteById(id);
            }
        }
    }

    @Override
    public Long countPersonByAccFirstOpenId(String firstOpenId) {
        return accPersonFirstOpenDao.countByAccFirstOpen_Id(firstOpenId);
    }

    @Override
    public int countByTimeSegId(String timeSegId) {
        return accFirstOpenDao.countByTimeSegId(timeSegId);
    }

    @Override
    public Pager loadPagerByAuthFilter(String sessionId, AccFirstOpenItem condition, int pageNo, int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        return getItemsByPage(condition, pageNo, pageSize);
    }

    @Override
    public Pager loadFirstOpenSelectDoorByAuthFilter(String sessionId, AccFirstOpenSelectDoorItem condition, int pageNo,
        int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        return getItemsByPage(condition, pageNo, pageSize);
    }
}