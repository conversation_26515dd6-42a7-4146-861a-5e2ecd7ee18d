package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.ivideo.service.IVideoGetAccDoorService;
import com.zkteco.zkbiosecurity.ivideo.vo.IVideoGetAccDoorItem;
import com.zkteco.zkbiosecurity.ivideo.vo.IVideoGetAccDoorSelectItem;

@Service
@Transactional
public class IVideoGetAccDoorServiceImpl implements IVideoGetAccDoorService {
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccDeviceService accDeviceService;

    @Override
    public Pager getItemByAuthFilter(String sessionId, IVideoGetAccDoorSelectItem iVideoGetAccDoorSelectItem, int page,
        int size) {
        AccDoorItem accDoorItem = new AccDoorItem();
        accDoorItem.setName(iVideoGetAccDoorSelectItem.getName());
        accDoorItem.setDeviceSn(iVideoGetAccDoorSelectItem.getDeviceSn());
        accDoorItem.setInId(iVideoGetAccDoorSelectItem.getInId());
        accDoorItem.setNotId(iVideoGetAccDoorSelectItem.getNotId());
        Pager pager = accDoorService.loadPagerByAuthFilter(sessionId, accDoorItem, page, size);
        List<AccDoorItem> accDoorItems = (List<AccDoorItem>)pager.getData();
        List<IVideoGetAccDoorSelectItem> items = new ArrayList<IVideoGetAccDoorSelectItem>();
        items = ModelUtil.copyListProperties(accDoorItems, IVideoGetAccDoorSelectItem.class);
        pager.setData(items);
        return pager;
    }

    @Override
    public List<IVideoGetAccDoorItem> getAccDoorByDoorIdsIn(String doorIds) {
        List<AccDoorItem> accDoorItems = accDoorService.getItemsByIds(CollectionUtil.strToList(doorIds));
        List<IVideoGetAccDoorItem> items = new ArrayList<IVideoGetAccDoorItem>();
        items = ModelUtil.copyListProperties(accDoorItems, IVideoGetAccDoorItem.class);
        return items;
    }

    @Override
    public IVideoGetAccDoorItem getItemByName(String doorName) {
        AccDoorItem accDoorItem = accDoorService.getItemByName(doorName);
        IVideoGetAccDoorItem item = new IVideoGetAccDoorItem();
        if (accDoorItem != null) {
            item.setId(accDoorItem.getId());
        }
        return item;
    }

    @Override
    public List<IVideoGetAccDoorItem> getAccDevNameBySn(String sns) {
        List<AccDeviceItem> accDeviceItemList = accDeviceService.getItemBySns(sns);
        List<IVideoGetAccDoorItem> iVideoGetAccDoorItemList = new ArrayList<>();
        for (AccDeviceItem accDeviceItem : accDeviceItemList) {
            IVideoGetAccDoorItem iVideoGetAccDoorItem = new IVideoGetAccDoorItem();
            iVideoGetAccDoorItem.setDeviceName(accDeviceItem.getAlias());
            iVideoGetAccDoorItem.setDeviceSn(accDeviceItem.getSn());
            iVideoGetAccDoorItemList.add(iVideoGetAccDoorItem);
        }
        return iVideoGetAccDoorItemList;
    }
}
