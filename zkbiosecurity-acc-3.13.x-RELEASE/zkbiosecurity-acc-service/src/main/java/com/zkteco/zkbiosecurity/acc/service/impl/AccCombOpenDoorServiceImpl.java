/**
 * File Name: AccCombOpenDoorServiceImpl Created by GenerationTools on 2018-03-14 下午03:02 Copyright:Copyright ©
 * 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.dao.*;
import com.zkteco.zkbiosecurity.acc.model.*;
import com.zkteco.zkbiosecurity.acc.service.AccCombOpenDoorService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.vo.AccCombOpenCombItem;
import com.zkteco.zkbiosecurity.acc.vo.AccCombOpenDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonInfoItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 对应百傲瑞达 AccCombOpenDoorServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-14 下午03:02
 * @version v1.0
 */
@Service
@Transactional
public class AccCombOpenDoorServiceImpl implements AccCombOpenDoorService {
    @Autowired
    private AccCombOpenDoorDao accCombOpenDoorDao;
    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccCombOpenCombDao accCombOpenCombDao;
    @Autowired
    private AccCombOpenPersonDao accCombOpenPersonDao;
    @Autowired
    private AccCombOpenDoorBIdDao accCombOpenDoorBIdDao;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccLevelService accLevelService;

    @Override
    public AccCombOpenDoorItem saveItem(AccCombOpenDoorItem item, String[] groupIds, String[] sorts,
        String[] openerNumbers) {
        AccCombOpenDoor accCombOpenDoor = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accCombOpenDoorDao.findById(id)).orElse(new AccCombOpenDoor());
        // 忽视null,避免查询的数据被置空
        ModelUtil.copyPropertiesIgnoreNull(item, accCombOpenDoor);
        if (accCombOpenDoor.getBusinessId() == null) {
            accCombOpenDoor.setBusinessId(createBId());
        }
        AccDoor accDoor = accDoorDao.findById(item.getDoorId()).orElse(new AccDoor());
        accCombOpenDoor.setAccDoor(accDoor);
        List<AccCombOpenComb> accCombOpenCombSet = new ArrayList<>();
        if (StringUtils.isNotBlank(item.getId())) {// 更新
            for (AccCombOpenComb obj : accCombOpenDoor.getAccCombOpenCombList()) {
                accCombOpenCombSet.add(obj);
                accCombOpenCombDao.delete(obj);
            }
            // 先删除原来的一对多关系，然后重新赋值
            accCombOpenDoor.getAccCombOpenCombList().removeAll(accCombOpenCombSet);
            accCombOpenDoor = accCombOpenDoorDao.save(accCombOpenDoor);
            accCombOpenDoor.setName(accCombOpenDoor.getName());
            accCombOpenCombSet = new ArrayList<>();
            int sort = 1;
            for (int i = 0; i < groupIds.length; i++) {
                if (StringUtils.isNotBlank(groupIds[i])) {
                    String openerNumber = StringUtils.isBlank(openerNumbers[i]) ? "0" : openerNumbers[i];
                    // 人数为0的不保存
                    if (!"0".equals(openerNumber)) {
                        AccCombOpenComb accCombOpenComb = new AccCombOpenComb();
                        AccCombOpenPerson accCombOpenPerson =
                                accCombOpenPersonDao.findById(groupIds[i]).orElse(new AccCombOpenPerson());
                        accCombOpenComb.setAccCombOpenPerson(accCombOpenPerson);
                        accCombOpenComb.setOpenerNumber(Short.parseShort(openerNumber));
                        accCombOpenComb.setAccCombOpenDoor(accCombOpenDoor);
                        accCombOpenComb.setSort((short) sort);
                        accCombOpenCombSet.add(accCombOpenComb);
                        sort ++;
                    }
                }
            }
            accCombOpenDoor.setAccCombOpenCombList(accCombOpenCombSet);
            accCombOpenDoor = accCombOpenDoorDao.save(accCombOpenDoor);
            // 重新下发多人开卡命令
            addGroupCmdByCombOpenToDev(accCombOpenDoor);
        } else { // 新增
            int sort = 1;
            for (int i = 0; i < groupIds.length; i++) {
                if (StringUtils.isNotBlank(groupIds[i]) && i < openerNumbers.length) {
                    String openerNumber = StringUtils.isBlank(openerNumbers[i]) ? "0" : openerNumbers[i];
                    // 人数为0的不保存
                    if (!"0".equals(openerNumber)) {
                        AccCombOpenComb accCombOpenComb = new AccCombOpenComb();
                        AccCombOpenPerson accCombOpenPerson =
                                accCombOpenPersonDao.findById(groupIds[i]).orElse(new AccCombOpenPerson());
                        accCombOpenComb.setAccCombOpenPerson(accCombOpenPerson);
                        accCombOpenComb.setOpenerNumber(Short.parseShort(openerNumber));
                        accCombOpenComb.setSort((short) sort);
                        accCombOpenComb.setAccCombOpenDoor(accCombOpenDoor);
                        accCombOpenCombSet.add(accCombOpenComb);
                        sort ++;
                    }
                }
            }

            accCombOpenDoor.setAccCombOpenCombList(accCombOpenCombSet);
            if (accCombOpenDoorDao.findByName(item.getName()) != null) {
                throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_WARN, "acc_combOpen_groupNameRepeat");
            } else {
                accCombOpenDoor = accCombOpenDoorDao.save(accCombOpenDoor);
                // 多人开卡下发命令
                addGroupCmdByCombOpenToDev(accCombOpenDoor);
            }
        }
        item.setId(accCombOpenDoor.getId());
        return item;
    }

    @Override
    public List<AccCombOpenDoorItem> getByCondition(AccCombOpenDoorItem condition) {
        return (List<AccCombOpenDoorItem>)accCombOpenDoorDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accCombOpenDoorDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                // accCombOpenDoorDao.deleteById(id);
                AccCombOpenDoor accCombOpenDoor = accCombOpenDoorDao.findById(id).orElse(new AccCombOpenDoor());
                List<AccCombOpenComb> accCombOpenCombSet = accCombOpenDoor.getAccCombOpenCombList();
                for (AccCombOpenComb accCombOpenComb : accCombOpenCombSet) {
                    accCombOpenCombDao.delete(accCombOpenComb);
                }
                // 删除设备多人开门，如果存在多个多人开门组合，则相应的门不能直接关闭多人开门功能。modify by: ob.huang 2013-08-21--自己本身不能算--add by wenxin
                // 2015-01-09
                accCombOpenDoorDao.delete(accCombOpenDoor);
                List<AccCombOpenDoor> accCombOpenCombList =
                    accCombOpenDoorDao.findByAccDoor_Id(accCombOpenDoor.getAccDoor().getId());
                if (accCombOpenCombList != null && accCombOpenCombList.size() > 0) {
                    // false：只删除规则，不将该门的多卡功能开关关闭
                    accDevCmdManager.delCombOpenDoorFromDev(buildItemInfo(accCombOpenDoor), false, false);
                } else {
                    // 关闭该门的多人开门功能。
                    accDevCmdManager.delCombOpenDoorFromDev(buildItemInfo(accCombOpenDoor), true, false);
                }
            }
        }
        return false;
    }

    @Override
    public AccCombOpenDoorItem getItemById(String id) {
        List<AccCombOpenDoorItem> items = getByCondition(new AccCombOpenDoorItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public List<AccCombOpenCombItem> getAccCombOpenCombList(String id) {
        List<AccCombOpenCombItem> accCombOpenCombItemList = new ArrayList<AccCombOpenCombItem>();
        AccCombOpenDoor accCombOpenDoor = accCombOpenDoorDao.findById(id).orElse(new AccCombOpenDoor());
        List<AccCombOpenComb> combSet = accCombOpenDoor.getAccCombOpenCombList();
        for (int i = 1; i <= combSet.size(); i++) {
            for (AccCombOpenComb accCombOpenComb : combSet) {
                if (accCombOpenComb.getSort() == i) {
                    AccCombOpenCombItem accCombOpenCombItem = new AccCombOpenCombItem();
                    accCombOpenCombItem.setId(accCombOpenComb.getId());
                    accCombOpenCombItem.setAccCombOpenPersonId(accCombOpenComb.getAccCombOpenPerson().getId());
                    accCombOpenCombItem.setOpenerNumber(accCombOpenComb.getOpenerNumber());
                    accCombOpenCombItemList.add(accCombOpenCombItem);
                }
            }
        }
        return accCombOpenCombItemList;
    }

    @Override
    public AccCombOpenDoorItem getItemByName(String name) {
        AccCombOpenDoor accCombOpenDoor = accCombOpenDoorDao.findByName(name);
        if (accCombOpenDoor == null) {
            return null;
        } else {
            AccCombOpenDoorItem item = new AccCombOpenDoorItem();
            item.setId(accCombOpenDoor.getId());
            item.setName(accCombOpenDoor.getName());
            return item;
        }
    }

    @Override
    public boolean validBackgroundVerify(String doorId) {
        boolean backgroundVerifyFlag = true;
        String devSn = "";
        AccDoor accDoor = accDoorDao.findById(doorId).orElse(new AccDoor());
        if (accDoor != null) {
            devSn = accDoor.getDevice().getSn();
        }
        // 支持后台验证功能且开启了后台验证
        if (accDeviceOptionService.isSupportFun(devSn, "AutoServerFunOn")
            && accDeviceOptionService.isSupportFun(devSn, "AutoServerMode")) {
            backgroundVerifyFlag = false;
        }
        return backgroundVerifyFlag;
    }

    @Override
    public String getCombOpenComb(String id) {
        StringBuffer gourpString = new StringBuffer();
        List<AccCombOpenComb> accCombOpenCombList = accCombOpenCombDao.findByAccCombOpenDoor_Id(id);
        accCombOpenCombList.stream().forEach(accCombOpenComb -> {
            AccCombOpenPerson accCardPersonGroup = accCombOpenPersonDao
                .findById(accCombOpenComb.getAccCombOpenPerson().getId()).orElse(new AccCombOpenPerson());
            if (accCombOpenComb.getOpenerNumber() > 0) {
                gourpString.append(accCardPersonGroup.getName() + "(" + accCombOpenComb.getOpenerNumber() + ")" + ",");
            }
        });
        return String.valueOf(gourpString.length() > 0 ? gourpString.substring(0, gourpString.length() - 1) : "");
    }

    @Override
    public Integer getCombOpenPersonByGroup(String id) {
        Integer count = 0;
        List<AccCombOpenComb> list = accCombOpenCombDao.findByAccCombOpenDoor_Id(id);
        for (AccCombOpenComb accCombOpenComb : list) {
            count += accCombOpenComb.getOpenerNumber();
        }
        return count;
    }

    private Long createBId() {
        AccCombOpenDoorBId accCombOpenDoorBId = new AccCombOpenDoorBId();
        accCombOpenDoorBId = accCombOpenDoorBIdDao.save(accCombOpenDoorBId);
        return accCombOpenDoorBId.getId();
    }

    /**
     * 下发多人开门命令到设备
     * 
     * @author: mingfa.zheng
     * @date: 2018/5/25 13:55
     * @return:
     */
    private void addGroupCmdByCombOpenToDev(AccCombOpenDoor accCombOpenDoor) {
        AccDevice dev = accCombOpenDoor.getAccDoor().getDevice();
        AccDevice parentDev = dev.getParentDevice();
        List<Long> groupIdColl = new ArrayList<>();// 多人开门的组人员所属组的id
        List<String> personIdList = new ArrayList<>();
        for (AccCombOpenComb combOpenComb : accCombOpenDoor.getAccCombOpenCombList()) {
            // 获取多人开门的人员信息，并且更新控制器user表里面的group字段,要与mutilcard表里group字段一致
            for (int i = 0, k = combOpenComb.getOpenerNumber(); i < k; i++) {
                groupIdColl.add(combOpenComb.getAccCombOpenPerson().getBusinessId());
            }
            for (AccPersonCombOpenPerson accPersonCombOpenPerson : combOpenComb.getAccCombOpenPerson()
                .getAccPersonCombOpenPersonSet()) {
                personIdList.add(accPersonCombOpenPerson.getPersPersonId());
                // personIds.append(accPersonCombOpenPerson.getPersPersonId()).append(",");
            }
        }
        if (personIdList.size() > 0) {
            // personIds = personIds.substring(0, personIds.length() - 1);
            AccPersonInfoItem personInfoBean = accLevelService.getPersonByIds(personIdList);
            List<String> persPinList = Lists.newArrayList();
            // 删除人员指纹
            // for (AccBioTemplateItem accBioTemplateBean : personInfoBean.getAccBioTemplateItemList()) {
            // if (!persPinList.contains(accBioTemplateBean.getPin())) {
            // /*//优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了 by juvenile.li add 20171114
            // if (parentDev != null) {
            // accPersonBiz.delBioTemplateFromDev(ConstUtil.SYSTEM_MODULE_ACC, dev.getParentDevice(),
            // accBioTemplateBean);// 删除指纹
            // }
            // accPersonBiz.delBioTemplateFromDev(ConstUtil.SYSTEM_MODULE_ACC, dev, accBioTemplateBean);// 删除指纹*/
            // persPinList.add(accBioTemplateBean.getPin());
            // }
            // }
            // if (persPinList.size() > 0) {
            // // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了
            // if (parentDev != null) {
            // accDevCmdManager.delPersonBioTemplateFromDev(parentDev, persPinList, false);// 删除指纹
            // }
            // accDevCmdManager.delPersonBioTemplateFromDev(dev, persPinList, false);// 删除指纹
            // }
            // 删除人员
            // List<String> pinColl = new ArrayList<String>();// 人员pin号
            // for (AccPersonOptItem accPersonOptBean : personInfoBean.getPersonList()) {
            // pinColl.add(accPersonOptBean.getPin());
            // }
            // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了 by juvenile.li add 20171114
            if (parentDev != null) {
                accDevCmdManager.setPersonToDev(parentDev.getSn(), personInfoBean.getPersonList(), false);
                // accDevCmdManager.setPersonBioTemplateToDev(parentDev, personInfoBean.getAccBioTemplateItemList(),
                // false);
            }
            // accDevCmdManager.delPersonFromDev(dev, pinColl, false);
            accDevCmdManager.setPersonToDev(dev.getSn(), personInfoBean.getPersonList(), false);
            // accDevCmdManager.setPersonBioTemplateToDev(dev, personInfoBean.getAccBioTemplateItemList(), false);
        }
        accDevCmdManager.setCombOpenDoorToDev(buildItemInfo(accCombOpenDoor), false);
        // accDevCmdBiz.addGroupCmdByCombOpenToDev(dev, accCombOpenDoor.getId(),
        // accCombOpenDoor.getAccDoor().getDoorNo(), groupIdColl);
    }

    private AccCombOpenDoorItem buildItemInfo(AccCombOpenDoor accCombOpenDoor) {
        AccCombOpenDoorItem item = new AccCombOpenDoorItem();
        item.setId(accCombOpenDoor.getId());
        item.setBusinessId(accCombOpenDoor.getBusinessId());
        AccDoor accDoor = accCombOpenDoor.getAccDoor();
        item.setDoorId(accDoor.getId());
        item.setDoorNo(accDoor.getDoorNo() + "");
        item.setDeviceId(accDoor.getDevice().getId());
        item.setDeviceSn(accDoor.getDevice().getSn());
        return item;
    }

    @Override
    public List<AccCombOpenDoorItem> getItemsByDoorIds(List<String> doorIdList) {
        AccCombOpenDoorItem accCombOpenDoorItem = new AccCombOpenDoorItem();
        accCombOpenDoorItem.setDoorIdsIn(StringUtils.join(doorIdList, ","));
        return getByCondition(accCombOpenDoorItem);
    }

    @Override
    public void handlerTransfer(List<AccCombOpenDoorItem> accCombOpenDoorItems) {
        // 数据量大的时候处理，分批处理
        List<List<AccCombOpenDoorItem>> AccCombOpenDoorItemList =
            CollectionUtil.split(accCombOpenDoorItems, CollectionUtil.splitSize);
        for (List<AccCombOpenDoorItem> accCombOpenDoorItems1 : AccCombOpenDoorItemList) {
            // 获取已存在的记录根据名称获取
            Collection<String> names =
                CollectionUtil.getPropertyList(accCombOpenDoorItems1, AccCombOpenDoorItem::getName, "-1");
            List<AccCombOpenDoor> accCombOpenDoorList = accCombOpenDoorDao.findByNameIn(names);
            Map<String, AccCombOpenDoor> accCombOpenDoorMap =
                CollectionUtil.listToKeyMap(accCombOpenDoorList, AccCombOpenDoor::getName);
            for (AccCombOpenDoorItem accCombOpenDoorItem : accCombOpenDoorItems1) {
                AccCombOpenDoor accCombOpenDoor = accCombOpenDoorMap.remove(accCombOpenDoorItem.getName());
                if (Objects.isNull(accCombOpenDoor)) {
                    accCombOpenDoor = new AccCombOpenDoor();
                }
                ModelUtil.copyPropertiesIgnoreNullWithProperties(accCombOpenDoorItem, accCombOpenDoor, "id");
                // 获取门对象根据设备号以及门编号
                AccDoor accDoor = accDoorDao.findByDoorNoAndDevice_Sn(Short.parseShort(accCombOpenDoorItem.getDoorNo()),
                    accCombOpenDoorItem.getDeviceSn());
                if (Objects.nonNull(accDoor)) {
                    accCombOpenDoor.setAccDoor(accDoor);
                }
                accCombOpenDoor.setBusinessId(Long.valueOf(accCombOpenDoorItem.getId()));
                accCombOpenDoorDao.save(accCombOpenDoor);
            }
        }
    }
}