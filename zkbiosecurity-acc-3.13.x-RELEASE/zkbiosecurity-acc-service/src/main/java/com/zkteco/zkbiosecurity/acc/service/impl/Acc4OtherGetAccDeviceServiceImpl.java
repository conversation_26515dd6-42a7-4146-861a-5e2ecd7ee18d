package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.ArrayList;
import java.util.List;

import com.zkteco.zkbiosecurity.acc.vo.AccSelectDeviceItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.acc.service.Acc4OtherGetAccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.vo.AccDevice4OtherItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;

/**
 * 其他模块调用门禁获取设备信息
 *
 * <AUTHOR>
 * @date 2022/4/2 14:55
 * @since 1.0.0
 */
@Service
public class Acc4OtherGetAccDeviceServiceImpl implements Acc4OtherGetAccDeviceService {

    @Autowired
    private AccDeviceService accDeviceService;

    @Override
    public List<AccDevice4OtherItem> getAccDevicesByDeviceIds(String devIds) {
        List<AccDeviceItem> accDeviceItems = accDeviceService.getItemByIds(devIds);
        return ModelUtil.copyListProperties(accDeviceItems, AccDevice4OtherItem.class);
    }

    @Override
    public Pager getItemByAuthFilter(String sessionId, AccDevice4OtherItem condition, int pageNo, int size) {
        AccSelectDeviceItem selectDeviceItem = buildCondition(condition);
        Pager pager = accDeviceService.getItemByAuthFilter(sessionId, selectDeviceItem, pageNo, size);
        List<AccSelectDeviceItem> selectDeviceItems = (List<AccSelectDeviceItem>)pager.getData();
        if (!selectDeviceItems.isEmpty()) {
            List<AccDevice4OtherItem> accDevice4OtherItemList = buildAccDevice4OtherItemList(selectDeviceItems);
            pager.setData(accDevice4OtherItemList);
        }
        return pager;
    }

    /**
     * 组装门禁设备信息
     *
     * @param accSelectDeviceItemList:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDevice4OtherItem>
     * <AUTHOR>
     * @date 2023-04-24 10:47
     * @since 1.0.0
     */
    private List<AccDevice4OtherItem> buildAccDevice4OtherItemList(List<AccSelectDeviceItem> accSelectDeviceItemList) {
        List<AccDevice4OtherItem> accDevice4OtherItemList = new ArrayList<>();
        for (AccSelectDeviceItem item : accSelectDeviceItemList) {
            AccDevice4OtherItem device4OtherItem = ModelUtil.copyProperties(item, new AccDevice4OtherItem());
            device4OtherItem.setSn(item.getDeviceSn());
            device4OtherItem.setAlias(item.getDeviceAlias());
            accDevice4OtherItemList.add(device4OtherItem);
        }
        return accDevice4OtherItemList;
    }

    /**
     * 构造查询条件
     *
     * @param condition:
     * @return com.zkteco.zkbiosecurity.acc.vo.AccSelectDeviceItem
     * <AUTHOR>
     * @date 2023-04-24 10:47
     * @since 1.0.0
     */
    private AccSelectDeviceItem buildCondition(AccDevice4OtherItem condition) {
        AccSelectDeviceItem selectDeviceItem = ModelUtil.copyProperties(condition, new AccSelectDeviceItem());
        selectDeviceItem.setDeviceSn(condition.getSn());
        selectDeviceItem.setDeviceAlias(condition.getAlias());
        return selectDeviceItem;
    }
}
