package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.Acc4OtherSelectDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 其他模块获调用门实现类
 *
 * <AUTHOR>
 * @date 2021/1/13 16:31
 * @since 1.0.0
 */
@Service
public class Acc4OtherGetAccDoorServiceImpl implements Acc4OtherGetAccDoorService {

    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private AccRTMonitorService accRTMonitorService;
    @Autowired
    AccDeviceOptionService accDeviceOptionService;
    @Autowired
    AccDeviceService accDeviceService;
    @Autowired
    private AccBaseDictionaryService accBaseDictionaryService;

    @Override
    public List<Acc4OtherSelectDoorItem> getItemsByIds(List<String> doorIds) {
        List<AccDoorItem> outList = accDoorService.getItemsByIds(doorIds);
        List<Acc4OtherSelectDoorItem> selectItem =
            outList.stream().map(this::createAcc4OtherSelectDoorItem).collect(Collectors.toList());
        return selectItem;
    }

    /**
     * 获取Acc4OtherSelectDoorItem Vo
     *
     * @param accDoorItem 门Vo
     * @return Acc4OtherSelectDoorItem
     * <AUTHOR>
     * @date 2021/1/13 16:34
     */
    private Acc4OtherSelectDoorItem createAcc4OtherSelectDoorItem(AccDoorItem accDoorItem) {
        Acc4OtherSelectDoorItem selectDoorItem = new Acc4OtherSelectDoorItem();
        ModelUtil.copyProperties(accDoorItem, selectDoorItem);
        selectDoorItem.setName(accDoorItem.getName());
        return selectDoorItem;
    }

    @Override
    public Pager getItemByAuthFilter(String sessionId, Acc4OtherSelectDoorItem condition, int pageNo, int size) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        AccDoorItem door = createFilterDoor(condition);
        if (StringUtils.isNotBlank(userId)) {
            door.setUserId(userId);
        }
        Pager doorPage = accDoorService.getItemsByPage(door, pageNo, size);
        List<AccDoorItem> doorList = (List<AccDoorItem>)doorPage.getData();
        List selectItem = doorList.stream().map(this::createAcc4OtherSelectDoorItem).collect(Collectors.toList());
        doorPage.setData(selectItem);
        return doorPage;
    }

    /**
     * 转换成AccDoorItem 对象
     *
     * @param condition
     * @return AccDoorItem
     * <AUTHOR>
     * @date 2021/1/13 16:36
     */
    private AccDoorItem createFilterDoor(Acc4OtherSelectDoorItem condition) {
        AccDoorItem doorItem = new AccDoorItem();
        doorItem.setNotId(condition.getNotInId());
        doorItem.setAreaIdIn(condition.getAuthAreaIdIn());
        doorItem.setName(condition.getName());
        ModelUtil.copyProperties(condition, doorItem);
        return doorItem;
    }

    @Override
    public ZKResultMsg operate(String type, int opInterval, String doorId) {
        Map<String, String> resMap = accRTMonitorService.operateDoor(type, opInterval + "", doorId);
        return dealResultData(resMap);
    }

    @Override
    public void operateNoneReturn(String type, int opInterval, String doorId) {
        accRTMonitorService.operateDoor(type, opInterval + "", doorId);
    }

    @Override
    public Acc4OtherSelectDoorItem getItemById(String doorId) {
        AccDoorItem accDoorItem = accDoorService.getItemById(doorId);
        return createAcc4OtherSelectDoorItem(accDoorItem);
    }

    @Override
    public List<Acc4OtherSelectDoorItem> getNotSupportLockdownDoors(List<String> doorIds) {
        List<Acc4OtherSelectDoorItem> noSupportList = new ArrayList<>(doorIds.size());
        for (String doorId : doorIds) {
            AccDoorItem door = accDoorService.getItemById(doorId);
            if (door != null) {
                boolean isSupport = accDeviceOptionService.getAccSupportFunListVal(door.getDeviceId(), 2);
                if (!isSupport) {
                    Acc4OtherSelectDoorItem selectDoor = createOtherSelectDoorItem(door);
                    noSupportList.add(selectDoor);
                }
            }
        }
        return noSupportList;
    }

    @Override
    public List<Acc4OtherSelectDoorItem> getItemsByCondition(Acc4OtherSelectDoorItem condition) {
        AccDoorItem accDoorItem = new AccDoorItem();
        ModelUtil.copyPropertiesIgnoreNull(condition, accDoorItem);
        accDoorItem.setNotId(condition.getNotInId());
        List<AccDoorItem> accDoorItems = accDoorService.getByCondition(accDoorItem);
        List<Acc4OtherSelectDoorItem> items = ModelUtil.copyListProperties(accDoorItems, Acc4OtherSelectDoorItem.class);
        return items;
    }

    @Override
    public List<String> getAllDoorIdByDoorId(String doorId) {
        List<String> allDoorIds = null;
        if (StringUtils.isNotBlank(doorId)) {
            AccDoorItem accDoorItem = accDoorService.getItemById(doorId);
            if (accDoorItem != null) {
                // 根据设备id获取所有门id集合
                allDoorIds = accDoorService.getIdByDevId(accDoorItem.getDeviceId());
            }
        }
        return allDoorIds;
    }

    private Acc4OtherSelectDoorItem createOtherSelectDoorItem(AccDoorItem accDoorItem) {
        Acc4OtherSelectDoorItem selectDoorItem = new Acc4OtherSelectDoorItem();
        ModelUtil.copyProperties(accDoorItem, selectDoorItem);
        selectDoorItem.setName(accDoorItem.getName());
        return selectDoorItem;
    }

    private String dealResMap(Map<String, String> resultMap) {
        List<String> resList =
            resultMap.keySet().stream().filter(k -> StringUtils.isNotBlank(resultMap.get(k))).map(k -> {
                switch (k) {
                    case "cmdId":
                        return "ok";
                    case "offline":
                        return "acc_dev_devNotOpForOffLine";
                    case "faile":
                        return "common_commStatus_executeCmdFailed";
                    case "notExistDev":
                        return "common_dev_notExistDev";
                    case "notSupport":
                        return "acc_dev_devNotSupportFunction";
                    default:
                        return "";
                }
            }).collect(Collectors.toList());
        return resList.get(0);
    }

    /**
     * @Description: 处理返回数据
     * <AUTHOR>
     * @date 2018/6/6 14:54
     * @param dataMap
     * @return
     */
    private ZKResultMsg dealResultData(Map<String, String> dataMap) {
        String cmdIdData = dataMap.get("cmdId");
        String msg = "";
        ZKResultMsg resultMsg = new ZKResultMsg();
        if ("true".equals(dataMap.get("notExistDev"))) {
            msg = I18nUtil.i18nCode("common_dev_opFaileAndReason") + I18nUtil.i18nCode("common_dev_notExistDev");
            resultMsg.setMsg(msg);
            return resultMsg;
        }
        if (!"".equals(cmdIdData)) {
            for (String cmdData : cmdIdData.split(",")) {
                String cmdId = cmdData.split("=")[0];
                String doorName = cmdData.split("=")[1];
                Map<String, String> resultMap = accDeviceService.getCmdResultById(Long.valueOf(cmdId), 20);
                if (Objects.isNull(resultMap)) {
                    msg += doorName + "," + I18nUtil.i18nCode("common_op_failed") + ";";
                } else {
                    Integer ret = Integer.valueOf(resultMap.get("result"));
                    if (Objects.isNull(ret)) {
                        msg += doorName + "," + I18nUtil.i18nCode("common_op_failed") + ";";
                    } else if (ret < 0) {
                        String failedInfo = I18nUtil.i18nCode(accBaseDictionaryService.getCommReason(ret));
                        msg += doorName + "," + I18nUtil.i18nCode("common_dev_opFaileAndReason") + failedInfo + ";";
                    }
                }
            }
        }
        if (!"".equals(dataMap.get("offline"))) {
            for (String doorName : dataMap.get("offline").split(",")) {
                msg += doorName + "," + I18nUtil.i18nCode("common_dev_offlinePrompt") + ";";
            }
        }
        if (!"".equals(dataMap.get("notSupport"))) {
            for (String doorName : dataMap.get("notSupport").split(",")) {
                msg += doorName + "," + I18nUtil.i18nCode("acc_dev_devNotSupportFunction") + ";";
            }
        }
        if (!"".equals(msg)) {
            resultMsg.setRet("400");
            resultMsg.setMsg(msg);
        }
        resultMsg.setData("");
        return resultMsg;
    }
}
