package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.acc.service.AccAuxOutService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccRTMonitorService;
import com.zkteco.zkbiosecurity.acc.utils.VmsGetAccServiceUtil;
import com.zkteco.zkbiosecurity.acc.vo.AccAuxOutItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.vms.service.VmsGetAccAuxOutService;
import com.zkteco.zkbiosecurity.vms.vo.VmsSelectAuxOutItem;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date Created In 10:23 2020/4/17
 */
@Service
@Slf4j
public class VmsGetAccAuxOutServiceImpl implements VmsGetAccAuxOutService {
    @Autowired
    AccRTMonitorService accRTMonitorService;
    @Autowired
    AccAuxOutService accAuxOutService;
    @Autowired
    AccDeviceService accDeviceService;

    @Override
    public ZKResultMsg operate(String type, int opInterval, String auxOutId) {
        Map<String, String> resultMap = accRTMonitorService.operateAuxOut(type, opInterval + "", auxOutId);
        return dealResMap(resultMap, auxOutId);
    }

    @Override
    public List<VmsSelectAuxOutItem> getItemsByIds(List<String> auxOutIds) {
        List<AccAuxOutItem> items = accAuxOutService.getItemsByIds(auxOutIds);
        return items.stream().map(v -> createSelectItem(v)).collect(Collectors.toList());
    }

    @Override
    public Pager getItemByAuthFilter(String sessionId, VmsSelectAuxOutItem condition, int pageNo, int size) {
        String areaIds = accDeviceService.getAreaIdsByAuthFilter(sessionId);
        AccAuxOutItem accAuxOutItem = createAuxOutFilter(condition, areaIds);
        Pager pager = accAuxOutService.getItemsByPage(accAuxOutItem, pageNo, size);
        List<AccAuxOutItem> auxOutList = (List<AccAuxOutItem>)pager.getData();
        List<VmsSelectAuxOutItem> selectList =
            auxOutList.stream().map(this::createSelectItem).collect(Collectors.toList());
        pager.setData(selectList);
        return pager;
    }

    // ---- private method

    private AccAuxOutItem createAuxOutFilter(VmsSelectAuxOutItem condition, String areaIds) {
        AccAuxOutItem accAuxOutItem = new AccAuxOutItem();
        ModelUtil.copyProperties(condition, accAuxOutItem);
        accAuxOutItem.setName(condition.getAuxName());
        accAuxOutItem.setAuxNo(condition.getAuxNo());
        accAuxOutItem.setDevAlias(condition.getDeviceAlias());
        accAuxOutItem.setDevSn(condition.getDeviceSn());
        accAuxOutItem.setDevId(condition.getDeviceId());
        accAuxOutItem.setAreaIdIn(areaIds);
        return accAuxOutItem;
    }

    private ZKResultMsg dealResMap(Map<String, String> resultMap, String auxOutId) {
        String res = VmsGetAccServiceUtil.dealResMap(resultMap);
        if (StringUtils.equals("ok", res)) {
            AccAuxOutItem out = accAuxOutService.getItemById(auxOutId);
            res = out == null ? "" : out.getName();
            ZKResultMsg resultMsg = ZKResultMsg.successMsg();
            resultMsg.setMsg(res);
            return resultMsg;
        }
        return ZKResultMsg.failMsg(res);
    }

    private VmsSelectAuxOutItem createSelectItem(AccAuxOutItem accAuxOutItem) {
        VmsSelectAuxOutItem vmsItem = new VmsSelectAuxOutItem();
        vmsItem.setId(accAuxOutItem.getId());
        vmsItem.setAuxNo(accAuxOutItem.getAuxNo());
        vmsItem.setAuxName(accAuxOutItem.getName());
        vmsItem.setDeviceAlias(accAuxOutItem.getDevAlias());
        vmsItem.setDeviceSn(accAuxOutItem.getDevSn());
        vmsItem.setDeviceId(accAuxOutItem.getDevId());
        ModelUtil.copyProperties(accAuxOutItem, vmsItem);
        return vmsItem;
    }
}
