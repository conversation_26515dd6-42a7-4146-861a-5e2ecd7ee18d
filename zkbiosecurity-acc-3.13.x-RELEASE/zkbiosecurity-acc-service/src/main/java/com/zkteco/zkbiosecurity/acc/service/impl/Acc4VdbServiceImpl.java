package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.zkteco.zkbiosecurity.acc.vo.Acc4VdbOptionItem;
import com.zkteco.zkbiosecurity.acc.vo.AccShortKey4VdbItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.service.Acc4VdbService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.vo.Acc4VdbSipAccountItem;
import com.zkteco.zkbiosecurity.acc.vo.AccContact4VdbItem;

/**
 * 门禁模块对接可视对讲模块调用命令下发
 */
@Service
public class Acc4VdbServiceImpl implements Acc4VdbService {
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccDeviceService accDeviceService;

    @Override
    public void setDeviceSipInfo(Acc4VdbSipAccountItem item) {
        accDevCmdManager.setSipAccount(item.getDeviceSn(), item, false);
    }

    @Override
    public void addContacts(String devSn, List<AccContact4VdbItem> accContact4VdbItems) {
        accDevCmdManager.setSipContact(devSn, accContact4VdbItems, false);
    }

    @Override
    public void delContacts(String devSn, List<String> accContactIds) {
        accDevCmdManager.delSipContact(devSn, accContactIds, false);
    }

    @Override
    public Map<Short, Set<String>> getDeviceStatusByDevSns(List<String> devSns) {
        Map<Short, Set<String>> snsMap = accDeviceService.getDevSnsByRedis();
        return snsMap;
    }

    @Override
    public void setVdbOptionToAcc(Acc4VdbOptionItem item) {
        accDevCmdManager.setVdbOption(item.getDeviceSn(), item, false);
    }

    @Override
    public void clearDeviceSipInfo(Acc4VdbSipAccountItem item) {
        accDevCmdManager.clearSipAccount(item.getDeviceSn(), item, false);
    }

    @Override
    public void setDevShortKey(String devSn, List<AccShortKey4VdbItem> items) {
        accDevCmdManager.setVdbDevShortKey(devSn, items, false);
    }

    @Override
    public void delDevShortKey(String devSn, List<String> shortKeyIdList) {
        accDevCmdManager.delVdbDevShortKey(devSn, shortKeyIdList, false);
    }

}
