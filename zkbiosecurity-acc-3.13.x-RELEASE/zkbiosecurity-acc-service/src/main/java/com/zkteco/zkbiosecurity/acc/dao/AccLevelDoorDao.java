package com.zkteco.zkbiosecurity.acc.dao;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.acc.model.AccLevelDoor;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 权限组-门 中间表Dao
 *
 * <AUTHOR>
 * @date 2018/3/22 18:00
 */
public interface AccLevelDoorDao extends BaseDao<AccLevelDoor, String> {
    /**
     * 根据设备ID和权限组ID查询出门编号。
     *
     * @author: mingfa.zheng
     * @date: 2018/4/28 9:42
     * @return:
     */
    @Query(value = "select e.accDoor.doorNo from AccLevelDoor e where e.accLevel.id = ?1 and e.accDoor.device.id = ?2")
    List<Short> getDoorNoByLevelIdAndDevId(String levelId, String deviceId);

    List<AccLevelDoor> findByAccDoor_Device_IdAndAccLevel_IdIn(String deviceId, List<String> levelIdList);

    List<AccLevelDoor> findByAccLevel_IdIn(List<String> levelIdList);

    /**
     * @Description: 根据人员ids和设备ID查询出权限和门的中间表。
     * @author: mingfa.zheng
     * @date: 2018/8/2 11:27
     * @param: [personIds, deviceId]
     * @return: java.util.List<com.zkteco.zkbiosecurity.acc.model.AccLevelDoor>
     **/
    @Query(value = "select alp.PERS_PERSON_ID, t.BUSINESS_ID, ad.DOOR_NO from ACC_LEVEL_DOOR e left join ACC_DOOR ad "
        + "on e.DOOR_ID=ad.ID left join ACC_DEVICE dev on ad.DEV_ID=dev.ID left join ACC_LEVEL_PERSON alp on alp.LEVEL_ID=e.LEVEL_ID left join "
        + "ACC_LEVEL l on l.ID=e.LEVEL_ID left join ACC_TIMESEG t on t.ID = l.TIMESEG_ID where alp.PERS_PERSON_ID in (?1) "
        + "and dev.ID=(?2)", nativeQuery = true)
    List<Object[]> getAccLevelDoorByPersonIdsAndDevId(List<String> personIds, String deviceId);

    /**
     * 通过设备ID查询出改设备所在的权限组ID
     *
     * @author: mingfa.zheng
     * @date: 2018/4/26 18:29
     * @return:
     */
    @Query(value = "select distinct d.LEVEL_ID from ACC_LEVEL_DOOR d where d.DOOR_ID in "
        + "(select ad.ID from ACC_DOOR ad left join ACC_DEVICE acd on ad.DEV_ID = acd.ID "
        + "where acd.ID = ?1 or acd.PARENT_ID = ?1)", nativeQuery = true)
    List<String> getLevelIdByDeviceId(String deviceId);

    /**
     * 通过设备序列号查询出设备所在的权限组ID
     *
     * @param devSn:设备序列号
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2021-02-05 14:36
     * @since 1.0.0
     */
    @Query(
        value = "select distinct d.LEVEL_ID from ACC_LEVEL_DOOR d where d.DOOR_ID in "
            + "(select ad.ID from ACC_DOOR ad left join ACC_DEVICE acd on ad.DEV_ID = acd.ID " + "where acd.SN = ?1)",
        nativeQuery = true)
    List<String> getLevelIdByDeviceSn(String devSn);

    /**
     * 权限组删除门--数据库
     *
     * @param : levelId 权限组ID
     * @param : doorIdList 门ID集合
     * @author: mingfa.zheng
     * @date: 2018/5/2 9:09
     * @return:
     */
    @Modifying
    @Query(value = "delete from ACC_LEVEL_DOOR where LEVEL_ID = ?1 and DOOR_ID in (?2)", nativeQuery = true)
    void delLevelDoorByParams(String levelId, List<String> doorIdList);

    /**
     * 权限组删除门--数据库
     *
     * @param : levelId 权限组ID
     * @param : deviceId 设备ID
     * @author: mingfa.zheng
     * @date: 2018/5/2 9:09
     * @return:
     */
    @Modifying
    @Query(
        value = "delete from ACC_LEVEL_DOOR where LEVEL_ID = ?1 and DOOR_ID in "
            + "(select ad.ID from ACC_DOOR ad left join ACC_DEVICE acd on ad.DEV_ID = acd.ID where acd.ID = ?2)",
        nativeQuery = true)
    void delLevelDoorByParams(String levelId, String deviceId);

    // select count(e.id) from AccLevel e inner join e.accDoorList d where e.id = ? and d.device.wgReaderId is null
    @Query(
        value = "select count(e.accLevel.id) from AccLevelDoor e where e.accLevel.id = ?1 and e.accDoor.device.wgReaderId is null")
    Long getLevelDoorCount(String levelId);

    @Query(
        value = "select count(e.accLevel.id) from AccLevelDoor e where e.accLevel.id = ?1 and e.accDoor.device.wgReaderId is null and e.accDoor.device.authAreaId in (?2)")
    Long getDoorCountByLevelIdAndAreaIds(String levelId, Collection<String> areaIds);

    @Query(value = "select count(e.accLevel.id) from AccLevelDoor e where e.accLevel.id = ?1")
    Long getDoorCountByLevelId(String levelId);

    @Modifying
    @Query(value = "delete from AccLevelDoor e where e.accLevel.id = ?1")
    void deleteByLevelId(String levelId);

    List<AccLevelDoor> findByAccDoor_Id(String doorId);

    /**
     * @Description: 根据设备id和权限组id和删除的门ids查询出所在权限组的门id
     * @author: mingfa.zheng
     * @date: 2018/7/10 20:26
     * @param: [deviceId, levelId, doorIdList]
     * @return: java.util.List<java.lang.String>
     **/
    @Query(
        value = "select e.accDoor.id from AccLevelDoor e where e.accDoor.device.id = ?1 and e.accLevel.id = ?2 and e.accDoor.id in (?3)")
    List<String> getDoorIdsByDevIdAndlevelId(String deviceId, String levelId, List<String> doorIdList);

    @Query(
        value = "select ald.DOOR_ID from ACC_LEVEL_DOOR ald left join ACC_LEVEL_PERSON alp on ald.LEVEL_ID = alp.LEVEL_ID where alp.PERS_PERSON_ID = ?1 and ald.DOOR_ID NOT IN "
            + "(select ad.id from ACC_DOOR ad left join ACC_DEVICE dev on ad.DEV_ID = dev.ID where dev.MACHINE_TYPE = 101 and dev.WG_READER_ID is not null)",
        nativeQuery = true)
    List<String> getLevelDoorByPersonId(String personId);

    @Query(
        value = "select alp.PERS_PERSON_ID from ACC_LEVEL_DOOR ald INNER join ACC_LEVEL_PERSON alp on ald.LEVEL_ID = alp.LEVEL_ID where ald.DOOR_ID = ?1",
        nativeQuery = true)
    List<String> getPersonIdsByDoorId(String doorId);

    /**
     * @param
     * @return
     * @Description: 根据门id和权限组id查询出所在权限组的门
     * <AUTHOR>
     * @Date 2018/12/18 10:42
     */
    AccLevelDoor findByAccDoor_IdAndAccLevel_Id(String doorId, String levelId);

    @Query(value = "select e.accDoor.id from AccLevelDoor e where e.accLevel.id in(?1)")
    List<String> getDoorIdsByLevelIdsIn(List<String> levelIds);

    @Query(
        value = "select e.accDoor.id from AccLevelDoor e where e.accLevel.id in(?1) and e.accDoor.device.authAreaId in (?2) ")
    List<String> getDoorIdsByLevelIdsInAndAreaIdsIn(List<String> levelIds, List<String> areaIds);

    /**
     * 根据设备序列号查询权限组-门中间表信息
     *
     * @param devSn:设备序列号
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.model.AccLevelDoor>
     * <AUTHOR>
     * @date 2021-01-26 15:33
     * @since 1.0.0
     */
    List<AccLevelDoor> findByAccDoor_Device_Sn(String devSn);

    /**
     * 根据多个序列号查询权限组-门中间表信息
     *
     * @param deviceSnList 序列号列表
     * @return 权限组-门中间表信息
     * <AUTHOR>
     * @date 2021/4/30 9:12
     */
    List<AccLevelDoor> findByAccDoor_Device_SnIn(List<String> deviceSnList);

    /**
     * 根据权限组id查询权限组-门中间表信息
     *
     * @param levelId:权限组id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.model.AccLevelDoor>
     * <AUTHOR>
     * @date 2021-02-03 11:28
     * @since 1.0.0
     */
    List<AccLevelDoor> findByAccLevel_Id(String levelId);

    /**
     * 根据设备序列号查询权限组-门中间表信息 根据门编号排序
     *
     * @param devSn
     * @return
     */
    List<AccLevelDoor> findByAccDoor_Device_SnOrderByAccDoor_DoorNo(String devSn);

    /**
     * 根据人员id和权限组id查询出权限和门的中间表(临时权限)
     *
     * @param personIds
     * @param levelIds
     * @return
     */
    @Query(
        value = "select alp.PERS_PERSON_ID, t.BUSINESS_ID, ad.DOOR_NO,dev.ID,alp.START_TIME,alp.END_TIME from ACC_LEVEL_DOOR e left join ACC_DOOR ad "
            + "on e.DOOR_ID=ad.ID left join ACC_DEVICE dev on ad.DEV_ID=dev.ID left join ACC_LEVEL_PERSON alp on alp.LEVEL_ID=e.LEVEL_ID left join "
            + "ACC_LEVEL l on l.ID=e.LEVEL_ID left join ACC_TIMESEG t on t.ID = l.TIMESEG_ID where alp.PERS_PERSON_ID in (?1) "
            + "and l.ID = (?2)",
        nativeQuery = true)
    List<Object[]> getAccLevelDoorAndTimesByPersonIdsAndLevelId(List<String> personIds, String levelId);

    /**
     * 根据人员id查询出权限和门的中间表
     *
     * @param personId:
     * @return java.util.List<java.lang.Object[]>
     * <AUTHOR>
     * @throws
     * @date 2024-01-06 10:41
     * @since 1.0.0
     */
    @Query(
        value = "select alp.PERS_PERSON_ID, t.BUSINESS_ID, ad.DOOR_NO, dev.ID,alp.START_TIME,alp.END_TIME from ACC_LEVEL_DOOR e left join ACC_DOOR ad "
            + "on e.DOOR_ID=ad.ID left join ACC_DEVICE dev on ad.DEV_ID=dev.ID left join ACC_LEVEL_PERSON alp on alp.LEVEL_ID=e.LEVEL_ID left join "
            + "ACC_LEVEL l on l.ID=e.LEVEL_ID left join ACC_TIMESEG t on t.ID = l.TIMESEG_ID where alp.PERS_PERSON_ID = ?1 ",
        nativeQuery = true)
    List<Object[]> getAccLevelDoorByPersonId(String personId);

    /**
     * 根据人员id和设备id查询出权限和门的中间表(临时权限)
     *
     * @param personIds:
     * @param deviceId:
     * @return java.util.List<java.lang.Object[]>
     * <AUTHOR>
     * @throws
     * @date 2024-01-09 10:08
     * @since 1.0.0
     */
    @Query(
        value = "select alp.PERS_PERSON_ID, t.BUSINESS_ID, ad.DOOR_NO,dev.ID,alp.START_TIME,alp.END_TIME from ACC_LEVEL_DOOR e left join ACC_DOOR ad "
            + "on e.DOOR_ID=ad.ID left join ACC_DEVICE dev on ad.DEV_ID=dev.ID left join ACC_LEVEL_PERSON alp on alp.LEVEL_ID=e.LEVEL_ID left join "
            + "ACC_LEVEL l on l.ID=e.LEVEL_ID left join ACC_TIMESEG t on t.ID = l.TIMESEG_ID where alp.PERS_PERSON_ID in (?1) "
            + "and dev.ID=(?2)",
        nativeQuery = true)
    List<Object[]> getAccLevelDoorTimesByPersonIdsAndDevId(List<String> personIds, String deviceId);
}
