/**
 * File Name: AccDeviceOptionServiceImpl Created by GenerationTools on 2018-03-20 上午09:48 Copyright:Copyright ©
 * 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceOptionDao;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.model.AccDeviceOption;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceOptionItem;
import com.zkteco.zkbiosecurity.auth.provider.AuthSessionProvider;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 对应百傲瑞达 AccDeviceOptionServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-20 上午09:48
 * @version v1.0
 */
@Service
@Transactional
public class AccDeviceOptionServiceImpl implements AccDeviceOptionService {
    @Autowired
    private AccDeviceOptionDao accDeviceOptionDao;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private AuthSessionProvider authSessionProvider;

    @Override
    public AccDeviceOptionItem saveItem(AccDeviceOptionItem item) {
        AccDeviceOption accDeviceOption = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accDeviceOptionDao.findById(id)).orElse(new AccDeviceOption());
        ModelUtil.copyPropertiesIgnoreNull(item, accDeviceOption);
        if (item != null && StringUtils.isNoneBlank(item.getDeviceId())) {
            final Optional<AccDevice> accDeviceOptional = accDeviceDao.findById(item.getDeviceId());
            if (accDeviceOptional.isPresent()) {
                accDeviceOption.setAccDevice(accDeviceOptional.get());
            }
        }
        accDeviceOptionDao.save(accDeviceOption);
        item.setId(accDeviceOption.getId());
        return item;
    }

    @Override
    public List<AccDeviceOptionItem> getByCondition(AccDeviceOptionItem condition) {
        return (List<AccDeviceOptionItem>)accDeviceOptionDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accDeviceOptionDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accDeviceOptionDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AccDeviceOptionItem getItemById(String id) {
        List<AccDeviceOptionItem> items = getByCondition(new AccDeviceOptionItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public String getOptVal(String sn, String optName) {
        String ret = String.valueOf(AccConstants.DISABLE_OPTION);
        Map<String, String> devOptMap = getDevOptionBySn(sn);
        ret = devOptMap.containsKey(optName) ? String.valueOf(devOptMap.get(optName)) : ret;
        return ret;
    }

    @Override
    public boolean isSupportFun(String sn, String optName) {
        boolean ret = false;
        Map<String, String> devOptMap = getDevOptionBySn(sn);
        ret = devOptMap.containsKey(optName) && devOptMap.get(optName).equals("1") ? true : ret;
        return ret;
    }

    @Cacheable(value = {"acc:devOptionMap"}, key = "#sn", condition = "#sn != null")
    @Override
    public Map<String, String> getDevExtendOption(String sn) {
        Map<String, String> devOptMap = new HashMap<>();
        // 查询数据
        List<AccDeviceOption> optionList = accDeviceOptionDao.findByAccDevice_Sn(sn);// 从缓存取设备信息
        for (AccDeviceOption deviceOption : optionList) {
            if (deviceOption.getType() == ConstUtil.DEV_FUN_PARAM) {
                devOptMap.put(deviceOption.getName(), deviceOption.getValue());
            }
        }
        return devOptMap;
    }

    @Override
    public boolean getAccSupportFunListVal(String devId, int index) {
        AccDeviceOption option = accDeviceOptionDao.findByAccDevice_IdAndName(devId, "AccSupportFunList");
        if (option != null) {
            String val = option.getValue();
            return getAccSupportFunListVal(index, val);
        }
        return false;
    }

    @Override
    public boolean getAccSupportFunListVal(int index, String value) {
        if (StringUtils.isNotBlank(value)) {
            if (value.length() > index && value.charAt(index) == '1') {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean isSupportFunList(String devSn, int index) {
        Map<String, String> devOptMap = getDevOptionBySn(devSn);
        String accSupportFunList = devOptMap.get("AccSupportFunList");
        return getAccSupportFunListVal(index, accSupportFunList);
    }

    @Override
    public void updateDevOpt(String devInfoStr, String sn) {
        JSONObject devInfo = JSONObject.parseObject(devInfoStr);
        AccDevice accDevice = null;
        Map<String, String> optionMap = getDevOptionBySn(sn);
        for (String key : devInfo.keySet()) {
            String newVal = devInfo.getString(key);
            if (StringUtils.isBlank(newVal)) {
                continue;
            }
            if (optionMap.containsKey(key)) {
                String optValue = optionMap.get(key);
                if (!optValue.equals(newVal)) {
                    AccDeviceOption accDeviceOption = accDeviceOptionDao.findByAccDevice_SnAndName(sn, key);
                    accDeviceOption.setValue(newVal);
                    accDeviceOptionDao.save(accDeviceOption);
                    updateDevOptions(sn, key, newVal);
                }
            } else {
                AccDeviceOption accDevOpt = new AccDeviceOption();
                if (accDevice == null) {
                    accDevice = accDeviceDao.findBySn(sn);
                }
                accDevOpt.setAccDevice(accDevice);
                accDevOpt.setName(key);
                accDevOpt.setValue(newVal);
                if (AccConstants.DEV_COMM_KEY_PARAM_LIST.contains(key)) {
                    accDevOpt.setType(ConstUtil.DEV_COMM_PARAM);// 表示该参数为通信关键参数.
                } else if (AccConstants.DEV_FUN_KEY_PARAM_LIST.contains(key)) {
                    accDevOpt.setType(ConstUtil.DEV_FUN_PARAM);// 表示该参数为设备扩展功能关键参数.
                }
                accDeviceOptionDao.save(accDevOpt);
                updateDevOptions(sn, accDevOpt.getName(), newVal);
            }
        }
    }

    @Override
    public String getValueByNameAndDevSn(String devSn, String optName) {
        Map<String, String> optionMap = getDevOptionBySn(devSn);
        if (optionMap.get(optName) == null) {
            return AccConstants.DEVICE_OPTIONS_DEFAULT_VALUE;
        }
        return optionMap.get(optName);
    }

    @Override
    public boolean isSupportDevParam(String devSn, String optName) {
        boolean ret = false;
        Map<String, String> optionMap = getDevOptionBySn(devSn);
        if (optionMap.get(optName) != null) {
            ret = (AccConstants.ENABLE.toString()).equals(optionMap.get(optName)) ? true : ret;
        }
        return ret;
    }

    @Override
    public AccDeviceOptionItem getDevOptValueBySnAndName(String devSn, String optName) {
        AccDeviceOption accDeviceOption = accDeviceOptionDao.findByAccDevice_SnAndName(devSn, optName);
        AccDeviceOptionItem accDeviceOptionItem = null;
        if (accDeviceOption != null) {
            accDeviceOptionItem = new AccDeviceOptionItem();
            ModelUtil.copyPropertiesIgnoreNull(accDeviceOption, accDeviceOptionItem);
            accDeviceOptionItem.setDeviceId(accDeviceOption.getAccDevice().getId());
        }
        return accDeviceOptionItem;
    }

    @Override
    public void setDevOptValByName(String devId, String optionName, String optionVal) {
        AccDeviceOption accDeviceOption = accDeviceOptionDao.findByAccDevice_IdAndName(devId, optionName);
        AccDevice accDevice = accDeviceDao.findById(devId).orElse(null);
        if (accDeviceOption != null) {
            accDeviceOption.setValue(optionVal);
        } else {
            accDeviceOption = new AccDeviceOption();
            accDeviceOption.setAccDevice(accDevice);
            accDeviceOption.setName(optionName);
            accDeviceOption.setValue(optionVal);
            if (AccConstants.DEV_COMM_KEY_PARAM_LIST.contains(optionName)) {
                accDeviceOption.setType(ConstUtil.DEV_COMM_PARAM);// 表示该参数为通信关键参数.
            } else if (AccConstants.DEV_FUN_KEY_PARAM_LIST.contains(optionName)) {
                accDeviceOption.setType(ConstUtil.DEV_FUN_PARAM);// 表示该参数为设备扩展功能关键参数.
            } else {
                accDeviceOption.setType(ConstUtil.DEV_GENERAL_PARAM);
            }
        }
        if (accDevice != null) {
            accDeviceOptionDao.save(accDeviceOption);
            updateDevOptions(accDevice.getSn(), accDeviceOption.getName(), accDeviceOption.getValue());
        }
    }

    @Override
    public String getMasterSlave(String sn) {
        String masterSlave = AccConstants.ACC_MASTERSLAVE_CONFIG_CANCEL;
        // 获取主从机配置 需要加上PC485AsInbio485=1
        AccDeviceOption PC485AsInbioOption = accDeviceOptionDao.findByAccDevice_SnAndName(sn, "PC485AsInbio485");// 总开关参数
        AccDeviceOption MasterInbioOption = accDeviceOptionDao.findByAccDevice_SnAndName(sn, "MasterInbio485");// 主从配置参数
        if (Objects.nonNull(PC485AsInbioOption)
            && (String.valueOf(AccConstants.ENABLED)).equals(PC485AsInbioOption.getValue())) {
            if (Objects.nonNull(MasterInbioOption)
                && (String.valueOf(AccConstants.ENABLED)).equals(MasterInbioOption.getValue())) {
                masterSlave = AccConstants.ACC_MASTERSLAVE_CONFIG_MASTER;
            } else if (Objects.nonNull(MasterInbioOption)
                && (String.valueOf(AccConstants.DISABLE)).equals(MasterInbioOption.getValue())) {
                masterSlave = AccConstants.ACC_MASTERSLAVE_CONFIG_SLAVE;
            }
        }
        return masterSlave;
    }

    @Override
    public void setMasterSlaveOption(String sn, String masterSlave) {
        AccDeviceOption pC485AsInbioOption = accDeviceOptionDao.findByAccDevice_SnAndName(sn, "PC485AsInbio485");// 总开关
        AccDeviceOption masterInbioOption = accDeviceOptionDao.findByAccDevice_SnAndName(sn, "MasterInbio485");// 主从机配置
        AccDeviceOption rS232BaudRateOption = null;// 波特率
        if ((AccConstants.ACC_MASTERSLAVE_CONFIG_CANCEL).equals(masterSlave)) { // 无
            if (Objects.nonNull(pC485AsInbioOption)) {
                pC485AsInbioOption.setValue(String.valueOf(AccConstants.DISABLE));
            }
            if (Objects.nonNull(masterInbioOption)) {
                masterInbioOption.setValue(String.valueOf(AccConstants.DISABLE));
            }
        } else if ((AccConstants.ACC_MASTERSLAVE_CONFIG_MASTER).equals(masterSlave)) { // 主机
            rS232BaudRateOption = accDeviceOptionDao.findByAccDevice_SnAndName(sn, "RS232BaudRateValue");
            if (Objects.nonNull(pC485AsInbioOption)) {
                pC485AsInbioOption.setValue(String.valueOf(AccConstants.ENABLE));
            }
            if (Objects.nonNull(masterInbioOption)) {
                masterInbioOption.setValue(String.valueOf(AccConstants.ENABLE));
            }
            if (Objects.nonNull(rS232BaudRateOption)) {
                rS232BaudRateOption.setValue("115200");// 485???
            }
        } else if ((AccConstants.ACC_MASTERSLAVE_CONFIG_SLAVE).equals(masterSlave)) {// 从机
            rS232BaudRateOption = accDeviceOptionDao.findByAccDevice_SnAndName(sn, "RS232BaudRateValue");
            if (Objects.nonNull(pC485AsInbioOption)) {
                pC485AsInbioOption.setValue(String.valueOf(AccConstants.ENABLE));
            }
            if (Objects.nonNull(masterInbioOption)) {
                masterInbioOption.setValue(String.valueOf(AccConstants.DISABLE));
            }
            if (Objects.nonNull(rS232BaudRateOption)) {
                rS232BaudRateOption.setValue("115200");// 485???
            }
        }
        if (Objects.nonNull(pC485AsInbioOption)) {
            accDeviceOptionDao.save(pC485AsInbioOption);
        }
        if (Objects.nonNull(masterInbioOption)) {
            accDeviceOptionDao.save(masterInbioOption);
        }
        if (Objects.nonNull(rS232BaudRateOption)) {
            accDeviceOptionDao.save(rS232BaudRateOption);
        }
    }

    @Override
    public Map<String, String> getDevOptionBySn(String devSn) {
        JSONObject optionJson = accCacheManager.getDeviceOptionInfo(devSn);// 优先从缓存中查询
        Map<String, String> optionMap = Maps.newHashMap();
        if (optionJson != null) {
            optionJson.forEach((k, v) -> optionMap.put(k, v.toString()));
        } else {
            List<AccDeviceOption> accDeviceOptionList = accDeviceOptionDao.findByAccDevice_Sn(devSn);
            if (accDeviceOptionList != null && !accDeviceOptionList.isEmpty()) {
                accDeviceOptionList.forEach(option -> optionMap.put(option.getName(), option.getValue()));
                accCacheManager.putDeviceOptionInfo(devSn, JSONObject.toJSON(optionMap).toString());// 更新设备参数信息
            }
        }
        return optionMap;
    }

    @Override
    public void updateDevOptions(String devSn, String optName, String optValue) {
        JSONObject optionJson = accCacheManager.getDeviceOptionInfo(devSn);
        // 缓存查不出时，从db还原缓存
        if (optionJson == null) {
            List<AccDeviceOption> accDeviceOptionList = accDeviceOptionDao.findByAccDevice_Sn(devSn);
            Map<String, String> optionMap = Maps.newHashMap();
            if (accDeviceOptionList != null && !accDeviceOptionList.isEmpty()) {
                accDeviceOptionList.forEach(option -> optionMap.put(option.getName(), option.getValue()));
            }
            optionJson = (JSONObject)JSONObject.toJSON(optionMap);
        }
        optionJson.put(optName, optValue);
        accCacheManager.putDeviceOptionInfo(devSn, optionJson.toJSONString());// 更新设备参数信息
    }

    @Override
    public void handlerTransfer(List<AccDeviceOptionItem> optionItems) {
        // 按设备sn分组， key：pin value:
        Map<String, List<AccDeviceOptionItem>> deviceOpMap =
            optionItems.stream().collect(Collectors.groupingBy(AccDeviceOptionItem::getDeviceSn));
        // 获取数据库中原有的设备参数取出，用于比较
        List<List<String>> snsList = CollectionUtil.split(deviceOpMap.keySet(), CollectionUtil.splitSize);
        Map<String, AccDeviceOption> optionAllMap = new HashMap<String, AccDeviceOption>();
        Map<String, AccDevice> deviceAllMap = new HashMap<String, AccDevice>();
        // 数据大时分批处理
        for (List<String> sns : snsList) {
            List<AccDeviceOption> options = accDeviceOptionDao.findByAccDevice_SnIn(sns);
            for (AccDeviceOption option : options) {
                String key = option.getAccDevice().getSn() + "_" + option.getName();
                optionAllMap.put(key, option);
            }
            // 將所有的設備查詢出來
            List<AccDevice> devices = accDeviceDao.findBySnIn(sns);
            for (AccDevice accDevice : devices) {
                deviceAllMap.put(accDevice.getSn(), accDevice);
            }
        }
        // 检测判断数据后保存更新
        for (AccDeviceOptionItem devOpItem : optionItems) {
            AccDeviceOption devOp = optionAllMap.remove(devOpItem.getDeviceSn() + "_" + devOpItem.getName());
            if (Objects.isNull(devOp)) {
                devOp = new AccDeviceOption();
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(devOpItem, devOp, "id");
            devOp.setAccDevice(deviceAllMap.get(devOpItem.getDeviceSn()));
            accDeviceOptionDao.save(devOp);
        }
        optionAllMap = null;
        deviceAllMap = null;
    }

    @Override
    public List<String> getDevIdByOptNameAndOptVal(String optionName, String optionVal) {
        return accDeviceOptionDao.getDevIdByOptNameAndOptVal(optionName, optionVal);
    }

    @Override
    public List<String> getDevIdByOptNameAndOptValAndAuthFilter(String optionName, String optionVal, String sessionId) {
        String areaIds = "";
        // 获取当前登录用户信息
        SecuritySubject securitySubject = authSessionProvider.getSecuritySubject(sessionId);
        if (!securitySubject.getIsSuperuser() && securitySubject.getAreaIds() != null
            && securitySubject.getAreaIds().size() > 0) {
            // 非超级用户进行数据权限过滤
            areaIds = StrUtil.collectionToStr(securitySubject.getAreaIds());
        }
        if (securitySubject.getIsSuperuser()) {
            return accDeviceOptionDao.getDevIdByOptNameAndOptVal(optionName, optionVal);
        } else {
            return accDeviceOptionDao.getDevIdByOptNameAndOptValAndDevAreaIdIn(optionName, optionVal,
                StrUtil.strToList(areaIds));
        }
    }

    @Override
    public List<AccDeviceOptionItem> getDevExtendParams(String id, List<String> optionNameList) {
        List<AccDeviceOption> accDeviceOptionList = accDeviceOptionDao.findByAccDevice_IdAndNameIn(id, optionNameList);
        List<AccDeviceOptionItem> accDeviceOptionItemList = new ArrayList<>();
        if (Objects.nonNull(accDeviceOptionList) && !accDeviceOptionList.isEmpty()) {
            accDeviceOptionItemList = ModelUtil.copyListProperties(accDeviceOptionList, AccDeviceOptionItem.class);
        }
        return accDeviceOptionItemList;
    }

    @Override
    public List<String> getDevSnByOptNames(String maskDetectionFunOn, String irTempDetectionFunOn) {
        return accDeviceOptionDao.getDevSnByOptNames(maskDetectionFunOn, irTempDetectionFunOn);
    }

    @Override
    public List<AccDeviceOptionItem> getOptionItemBySnsAndOptName(List<String> devSnList, String optName) {
        List<AccDeviceOption> accDeviceOptionList = accDeviceOptionDao.findByAccDevice_SnInAndName(devSnList, optName);
        if (Objects.nonNull(accDeviceOptionList) && !accDeviceOptionList.isEmpty()) {
            List<AccDeviceOptionItem> accDeviceOptionItemList = Lists.newArrayList();
            for (AccDeviceOption opt : accDeviceOptionList) {
                AccDeviceOptionItem optionItem = new AccDeviceOptionItem();
                optionItem.setDeviceId(opt.getAccDevice().getId());
                optionItem.setDeviceSn(opt.getAccDevice().getSn());
                optionItem.setId(opt.getId());
                optionItem.setName(opt.getName());
                optionItem.setValue(opt.getValue());
                accDeviceOptionItemList.add(optionItem);
            }
            return accDeviceOptionItemList;
        }
        return null;
    }

    @Override
    public List<AccDeviceOptionItem> getItemsByOptName(String optName) {
        List<AccDeviceOption> optionList = accDeviceOptionDao.findByName(optName);
        if (Objects.nonNull(optionList) && !optionList.isEmpty()) {
            List<AccDeviceOptionItem> accDeviceOptionItemList = Lists.newArrayList();
            for (AccDeviceOption opt : optionList) {
                AccDeviceOptionItem optionItem = new AccDeviceOptionItem();
                optionItem.setDeviceId(opt.getAccDevice().getId());
                optionItem.setDeviceSn(opt.getAccDevice().getSn());
                optionItem.setId(opt.getId());
                optionItem.setName(opt.getName());
                optionItem.setValue(opt.getValue());
                accDeviceOptionItemList.add(optionItem);
            }
            return accDeviceOptionItemList;
        }
        return null;
    }

    @Override
    public boolean isContainDevParam(String devSn, String optName) {
        boolean ret = false;
        Map<String, String> devOptMap = getDevOptionBySn(devSn);
        if (devOptMap.containsKey(optName)) {
            ret = true;
        }
        return ret;
    }

    @Override
    public AccDeviceOptionItem getItemByDevIdAndName(String devId, String optName) {
        AccDeviceOption accDeviceOption = accDeviceOptionDao.findByAccDevice_IdAndName(devId, optName);
        AccDeviceOptionItem optionItem = null;
        if (Objects.nonNull(accDeviceOption)) {
            optionItem = new AccDeviceOptionItem();
            optionItem.setId(accDeviceOption.getId());
            optionItem.setDeviceId(accDeviceOption.getAccDevice().getId());
            optionItem.setDeviceSn(accDeviceOption.getAccDevice().getSn());
            optionItem.setValue(accDeviceOption.getValue());
            optionItem.setName(accDeviceOption.getName());
            optionItem.setType(accDeviceOption.getType());
        }
        return optionItem;
    }

    @Override
    public void saveByDevIdAndOptionNameAndValue(String devId, String optName, String optValue) {
        // 保存参数
        AccDeviceOption option = accDeviceOptionDao.findByAccDevice_IdAndName(devId, optName);
        if (Objects.isNull(option)) {
            AccDevice accDevice = accDeviceDao.findById(devId).orElse(null);
            if (Objects.nonNull(accDevice)) {
                option = new AccDeviceOption().setAccDevice(accDevice).setName(optName);
            }
        }
        option.setValue(optValue);
        accDeviceOptionDao.save(option);
    }

    @Override
    public List<AccDeviceOptionItem> getItemsByDevId(String devId) {
        List<AccDeviceOption> accDeviceOptions = accDeviceOptionDao.findByAccDevice_Id(devId);
        List<AccDeviceOptionItem> accDeviceOptionItemList = new ArrayList<>();
        if (Objects.nonNull(accDeviceOptions) && !accDeviceOptions.isEmpty()) {
            for (AccDeviceOption option : accDeviceOptions) {
                AccDeviceOptionItem item = new AccDeviceOptionItem();
                ModelUtil.copyProperties(option, item);
                item.setDeviceId(option.getAccDevice().getId());
                accDeviceOptionItemList.add(item);
            }
        }
        return accDeviceOptionItemList;
    }

    @Override
    public List<AccDeviceOptionItem> getItemsByDevIdAndOptType(String devId, Short optType) {
        List<AccDeviceOption> accDeviceOptions = accDeviceOptionDao.findByAccDevice_IdAndType(devId, optType);
        List<AccDeviceOptionItem> accDeviceOptionItemList = new ArrayList<>();
        if (Objects.nonNull(accDeviceOptions) && !accDeviceOptions.isEmpty()) {
            for (AccDeviceOption option : accDeviceOptions) {
                AccDeviceOptionItem item = new AccDeviceOptionItem();
                ModelUtil.copyProperties(option, item);
                item.setDeviceId(option.getAccDevice().getId());
                accDeviceOptionItemList.add(item);
            }
        }
        return accDeviceOptionItemList;
    }

    @Override
    public void saveAccDeviceOptionList(List<AccDeviceOptionItem> optionItemList) {
        // 组装设备map信息
        List<String> devIdList =
            (List<String>)CollectionUtil.getPropertyList(optionItemList, AccDeviceOptionItem::getDeviceId, "-1");
        List<AccDevice> accDeviceList = accDeviceDao.findByIdList(devIdList);
        Map<String, AccDevice> deviceMap = CollectionUtil.listToKeyMap(accDeviceList, AccDevice::getId);

        List<AccDeviceOption> accDeviceOptions = new ArrayList<>();
        for (AccDeviceOptionItem item : optionItemList) {
            AccDeviceOption option = ModelUtil.copyPropertiesIgnoreNull(item, new AccDeviceOption());
            AccDevice accDevice = deviceMap.get(item.getDeviceId());
            if (Objects.nonNull(accDevice)) {
                option.setAccDevice(accDevice);
            }
            accDeviceOptions.add(option);
        }
        if (!accDeviceOptions.isEmpty()) {
            accDeviceOptionDao.save(accDeviceOptions);
        }
        // 加入缓存
        final Map<String, List<AccDeviceOptionItem>> devOptMap =
            optionItemList.stream().filter(opt -> StringUtils.isNotBlank(opt.getDeviceId()))
                .collect(Collectors.groupingBy(AccDeviceOptionItem::getDeviceId));
        for (String devId : deviceMap.keySet()) {
            final AccDevice accDevice = deviceMap.get(devId);
            JSONObject optionJson = accCacheManager.getDeviceOptionInfo(accDevice.getSn());
            if (optionJson == null) {
                optionJson = new JSONObject();
            }
            for (AccDeviceOptionItem optItem : devOptMap.get(devId)) {
                optionJson.put(optItem.getName(), optItem.getValue());
            }
            accCacheManager.putDeviceOptionInfo(accDevice.getSn(), optionJson.toJSONString());
        }
    }

    @Override
    public boolean isDeviceSupportIPCLink(String deviceSn) {
        return isSupportFunList(deviceSn, 65);
    }
}