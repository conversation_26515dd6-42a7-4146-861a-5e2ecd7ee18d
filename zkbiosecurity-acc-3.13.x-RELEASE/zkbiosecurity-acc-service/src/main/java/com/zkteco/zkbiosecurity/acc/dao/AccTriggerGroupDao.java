package com.zkteco.zkbiosecurity.acc.dao;

import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.acc.model.AccTriggerGroup;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2024/3/18 14:55
 * @since 1.0.0
 */
public interface AccTriggerGroupDao extends BaseDao<AccTriggerGroup, String> {
    /**
     * 查询当前最大业务id
     *
     * @return
     */
    @Query("SELECT MAX(t.bussinessId) FROM AccTriggerGroup t")
    Long findMaxBussinessId();

}
