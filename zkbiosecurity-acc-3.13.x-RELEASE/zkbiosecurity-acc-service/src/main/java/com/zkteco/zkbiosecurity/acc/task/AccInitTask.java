package com.zkteco.zkbiosecurity.acc.task;

import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.acc.service.AccTransactionAutoExportService;

@Component
@Order(value = 32)
public class AccInitTask implements CommandLineRunner {
    @Autowired
    private AccFetchTransactionTask accFetchTransactionTask;
    @Autowired
    private AccReportDataCleanTask accReportDataCleanTask;
    @Autowired
    private AccTransactionAutoExportService accTransactionAutoExportService;
    @Autowired
    private AccFirstInLastOutTask accFirstInLastOutTask;
    @Autowired
    AccCheckAttShiftTask accCheckAttShiftTask;

    @Override
    public void run(String... args) throws Exception {
        accFetchTransactionTask.initFetchTransaction();
        accReportDataCleanTask.initReportDataClean();
        accTransactionAutoExportService.initAutoProcessorTime();
        accFirstInLastOutTask.initSaveFirstInLastOut();
        accCheckAttShiftTask.initAccCheckAttShiftTask();
    }
}
