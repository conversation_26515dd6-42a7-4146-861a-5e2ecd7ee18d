package com.zkteco.zkbiosecurity.acc.utils;

import java.io.*;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.ClassUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.FileUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

import sun.misc.BASE64Decoder;

public class AccDataUtil {

    private static String TGZ_SUFFIX = ".tgz";
    public final static String TGZ_SUB_PATH = "tgz_sub";
    public final static String TGZ_SPLIT_PATH = "tgz_split";

    /**
     * 根据日期格式转化TimeStamp为String
     *
     * <AUTHOR>
     * @since 2018年5月4日 下午4:49:25
     * @param timestamp
     * @param pattern
     * @return
     */
    public static String formatDateTime(Date timestamp, String pattern) {
        SimpleDateFormat datetimeFormat = new SimpleDateFormat(pattern);
        return datetimeFormat.format(new Date(timestamp.getTime()));
    }

    /**
     * 获取事件对应照片的路径
     *
     * <AUTHOR> 邱清江
     * @since 2015年5月4日 下午5:48:17
     * @return
     */
    public static String getAccTransactionPhotoPath(Date eventTime, Integer logId, String sn, String pin) {
        String day = formatDateTime(eventTime, "yyyy-MM-dd");
        String imageName = getAccTransactionImageName(eventTime, logId, sn, pin);
        if (StringUtils.isBlank(imageName)) {
            return "pers/images/userImage.gif";
        }
        String path = AccConstants.LINKAGE_PHOTO_PATH + "/" + sn + "/" + day + "/" + imageName;
        return path;
    }

    /**
     * 获取事件相对应的照片名称
     *
     * <AUTHOR>
     * @since 2018年5月4日 下午4:26:42
     * @param eventTime
     * @param logId
     * @param sn
     * @param pin
     * @return
     */
    public static String getAccTransactionImageName(Date eventTime, Integer logId, String sn, String pin) {
        String imageName = "";
        if (logId != null) {
            // ipc自己拍照才有logid 图片命名格式20141104143822_12345698741552_1122.jpg
            imageName = formatDateTime(eventTime, "yyyyMMddHHmmss") + "_" + sn + "_" + logId + ".jpg";
        } else {
            // 一体机拍照，直接是 20141104143742-1.jpg 1是具体pin号人员 或20141104143742.jpg
            String flag = StringUtils.isNotBlank(pin) ? "-" + pin : "";
            imageName = formatDateTime(eventTime, "yyyyMMddHHmmss") + flag + ".jpg";
        }
        return imageName;
    }

    public static Date getGMT() {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat gmtFormat = new SimpleDateFormat(DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS.getValue());
        gmtFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        String dateStr = gmtFormat.format(calendar.getTime());
        Date date = DateUtil.stringToDate(dateStr, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
        return date;
    }

    public static Timestamp transformTime(String transformTime) {
        long timeStamp = Long.parseLong(transformTime);
        long second = timeStamp % 60;
        timeStamp /= 60;
        long minute = timeStamp % 60;
        timeStamp /= 60;
        long hour = timeStamp % 24;
        timeStamp /= 24;
        long date = timeStamp % 31 + 1;
        timeStamp /= 31;
        long month = timeStamp % 12;
        timeStamp /= 12;
        long year = timeStamp + 100;
        Timestamp time = new Timestamp((int)year, (int)month, (int)date, (int)hour, (int)minute, (int)second, 0);
        return time;
    }

    public static Date transformDate(String transfomrDate) {
        int time = Integer.parseInt(transfomrDate);
        int year = time / 10000 - 1900;
        int month = (time % 10000) / 100 - 1;
        int date = time % 10000 % 100;
        return new Date(year, month, date);
    }

    /**
     * 根据扩展人员funswitch参数，获取定义的内容，下发的0-3：0都没有，1延迟通行，2临时人员，3延迟通行和临时人员
     * 按二进制位运算：0011=3,0010=2,0001=1,低位第一位标识是否延迟通行，低位第二位表示是否临时人员
     *
     * <AUTHOR>
     * @since 2016年11月2日 下午4:08:28
     * @param funswitch 0-3之间
     * @param index 0:获取是否延迟通行；1是否临时人员；
     * @return
     */
    public static String getFunswitchByIndex(String funswitch, int index) {
        String funswitchTemp = StringUtils.isBlank(funswitch) ? "0" : funswitch;
        String contentFlag = Integer.toBinaryString(Integer.parseInt(funswitchTemp));
        // 左側补位
        contentFlag = String.format("%04d", Integer.valueOf(contentFlag));
        // 反序
        contentFlag = new StringBuffer(contentFlag).reverse().toString();
        return contentFlag.charAt(index) + "";
    }

    /**
     *
     * base64解码并将解码后的字符串保存到tgz并解压
     * 
     * @param dataStr
     * @param devSn
     * @param module
     * @return
     */
    public static List<String> decodeStrAndGenerateTxt(String dataStr, String devSn, String table, String module) {
        BASE64Decoder decoder = new BASE64Decoder();
        try {
            dataStr = dataStr.replace("\\", "");
            // 1.Base64解码
            byte[] b = decoder.decodeBuffer(dataStr);
            for (int i = 0; i < b.length; ++i) {
                if (b[i] < 0) {
                    b[i] += 256;// 调整异常数据
                }
            }
            // 一体机解压tgz后生成ACC_ATT_LOG
            return createDataTxt(devSn, table, module, b);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     *
     * 读取tgz文件，并解压，分割成多个txt文件
     * 
     * <AUTHOR>
     * @since 2017年9月20日 下午5:14:18
     * @param devSn
     * @param table
     * @param module
     * @param b
     * @return 返回文件路径
     * @throws IOException
     * @throws Exception
     */
    public static List<String> createDataTxt(String devSn, String table, String module, byte[] b)
        throws IOException, Exception {
        // WebApplicationContext webApplicationContext = ContextLoader.getCurrentWebApplicationContext();
        // 获取项目的绝对路径
        String relativePathStr =
            FileUtil.systemFilePath + module + File.separator + "account" + File.separator + devSn + File.separator;
        File eventSoundFilePath = new File(relativePathStr);
        if (!eventSoundFilePath.isAbsolute()) { // 判断是否是绝对路径
            relativePathStr = ClassUtil.getRootPath() + "/" + relativePathStr;
        }
        // ServletContext servletContext = webApplicationContext.getServletContext();
        // String realPath = servletContext.getRealPath("/");
        // 2.生成tgz压缩文件
        generalTgzFile(b, relativePathStr, table);

        // 3.解压缩成txt文件
        String accountTgzFilePath = relativePathStr + table + TGZ_SUFFIX;
        String tgzSubPath = relativePathStr + TGZ_SUB_PATH + File.separator;// 解压目录
        decompressingFile(accountTgzFilePath, tgzSubPath);

        // 4.删除压缩文件
        File compressedFile = new File(accountTgzFilePath);
        compressedFile.delete();

        List<String> filePathList = new ArrayList<>();
        File tgzSubFile = new File(tgzSubPath);
        if (tgzSubFile.listFiles().length > 0) {
            // 5.将文件按行拆分成多个文件
            String tgzSplitPath = relativePathStr + TGZ_SPLIT_PATH + File.separator;// 分割目录
            splitDataToSaveFile(10000, tgzSubFile.listFiles()[0], tgzSplitPath);
            File tgzSplitFile = new File(tgzSplitPath);
            for (String c : tgzSplitFile.list()) {
                filePathList.add(tgzSplitPath + c);
            }
        }

        // 6.删除原文件
        FileUtil.deleteDirectory(tgzSubPath);

        // 7.返回文件路径列表
        return filePathList;
    }

    /**
     * 生成tgz文件
     * 
     * @param b
     * @param relativePathStr
     * @param table
     * @throws IOException
     */
    private static void generalTgzFile(byte[] b, String relativePathStr, String table) throws IOException {
        File accountDir = new File(relativePathStr);
        if (!accountDir.exists()) {
            accountDir.mkdirs();
        }
        String accountTgzFilePath = relativePathStr + table + TGZ_SUFFIX;
        OutputStream out = null;
        try {
            out = new FileOutputStream(accountTgzFilePath);
            out.write(b);
            out.flush();
            out.close();
        } catch (IOException e) {
            throw e;
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    throw e1;
                }
            }
        }
    }

    /**
     *
     * 解压文件
     *
     * @param filePath
     * @param folderPath
     * @throws IOException
     */
    private static void decompressingFile(String filePath, String folderPath) throws IOException {
        // TFile src = new TFile(filePath);
        // File dest = new File(folderPath);
        // try
        // {
        // src.cp_r(dest);
        // TVFS.umount(src);
        // }
        // catch (IOException e)
        // {
        // throw e;
        // }
    }

    /**
     *
     * 按行分割文件
     *
     * <AUTHOR> 邱清江
     * @since 2016年1月29日 上午11:21:22
     * @param rows 为多少行一个文件
     * @param sourceFile 为源文件路径
     * @param targetDirectoryPath 文件分割后存放的目标目录
     * @return
     * @throws Exception
     */
    public static void splitDataToSaveFile(int rows, File sourceFile, String targetDirectoryPath) throws Exception {
        File targetFile = new File(targetDirectoryPath);
        if (!sourceFile.exists() || rows <= 0 || sourceFile.isDirectory()) {
            return;
        }
        if (!targetFile.exists()) {
            targetFile.mkdirs();
        }
        InputStreamReader in = null;
        BufferedReader br = null;
        BufferedWriter bw = null;
        try {
            in = new InputStreamReader(new FileInputStream(sourceFile), "GBK");
            br = new BufferedReader(in);
            String str = "";
            String tempData = br.readLine();
            int i = 1, s = 0;
            while (tempData != null) {
                str += tempData + "\r\n";
                if (i % rows == 0) {
                    bw = new BufferedWriter(
                        new OutputStreamWriter(new FileOutputStream(
                            targetFile.getAbsolutePath() + "/" + sourceFile.getName() + "_" + (s) + ".txt"), "GBK"),
                        1024);

                    bw.write(str);
                    bw.close();

                    str = "";
                    s += 1;
                }
                i++;
                tempData = br.readLine();
            }
            if ((i - 1) % rows != 0) {

                bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(
                    targetFile.getAbsolutePath() + "/" + sourceFile.getName() + "_" + (s) + ".txt"), "GBK"), 1024);
                bw.write(str);
                bw.close();
                br.close();
                s += 1;
            }
            in.close();
        } catch (Exception e) {
            throw e;
        } finally {
            if (bw != null) {
                bw.close();
            }
            if (br != null) {
                br.close();
            }
            if (in != null) {
                in.close();
            }
        }
    }

    public static int compareTime(Date oldTime, Date newTime) {
        Calendar oldCal = Calendar.getInstance();
        Calendar newCal = Calendar.getInstance();
        oldCal.setTime(oldTime);
        newCal.setTime(newTime);
        return oldCal.compareTo(newCal);
    }

    /**
     * 推荐卡格式
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/27 15:20
     * @param bitscount
     * @param orgCardNos
     * @param cardNos
     * @param sizeCodes
     * @return java.lang.String
     */
    public static String recommendCardFmt(int bitscount, String[] orgCardNos, String[] cardNos, String[] sizeCodes) {
        StringBuffer cardFmt = new StringBuffer();
        for (int i = 0; i < bitscount; i++) {
            cardFmt.append("0");
        }
        int cLength = 0;
        for (int i = 0; i < orgCardNos.length; i++) {
            StringBuffer org = new StringBuffer(orgCardNos[i]);
            if (!StringUtils.isEmpty(cardNos[i])) {
                String a = new BigInteger(cardNos[i]).toString(2);
                switch (a.length() % 4)// 二进制按 4位补齐前面的0
                {
                    case 1:
                        a = "000" + a;
                        break;
                    case 2:
                        a = "00" + a;
                        break;
                    case 3:
                        a = "0" + a;
                        break;
                }
                int start = org.lastIndexOf(a);
                if (start != -1) {
                    int end = start + a.length();
                    StringBuffer str = new StringBuffer();
                    for (int j = start; j < end; j++) {
                        str.append("c");
                    }
                    org.replace(start, end, str.toString());
                    cardFmt.replace(start, end, str.toString());
                    int l = end - start + 1;

                    if (cLength == 0) {
                        cLength = l;
                    } else {
                        String card = org.substring(0, start);
                        if (cLength < l)// 上一张卡匹配的卡号位长度比当前这张卡短
                        {
                            card = orgCardNos[i - 1].substring(0, start);
                        }

                        if (start > 1) {
                            int last = card.lastIndexOf("1");
                            if (last != -1 && (cLength - l >= start - last)) {
                                throw new ZKBusinessException(I18nUtil.i18nCode("acc_wiegand_tip1"));
                            }
                        }

                        if (cLength < l)// 条件和上面631行虽相同，但是cLength在639有使用，故此这么写
                        {
                            cLength = l;// 记录卡号位匹配最长的
                        }
                    }
                } else {
                    throw new ZKBusinessException(I18nUtil.i18nCode("acc_wiegand_tip3", cardNos[i]));
                }
            }
            if (sizeCodes.length > i && !StringUtils.isEmpty(sizeCodes[i])) {
                String a = Long.toBinaryString(Long.parseLong(sizeCodes[i]));
                int start = org.indexOf(a);
                if (start != -1) {
                    int end = start + a.length();
                    StringBuffer str = new StringBuffer();
                    for (int j = start; j < end; j++) {
                        str.append("s");
                    }
                    org.replace(start, end, str.toString());
                    cardFmt.replace(start, end, str.toString());
                    // 匹配完之后S和C之间包含有1或者 S C 之后还有很多位没有匹配到
                    String tempStr = org.substring(1, org.length() - 1);
                    if (tempStr.contains("s") && tempStr.contains("c")
                        && (tempStr.contains("1") || cardFmt.toString().matches("c0+"))) {
                        throw new ZKBusinessException(I18nUtil.i18nCode("acc_wiegand_tip2", sizeCodes[i], cardNos[i]));
                    }
                } else {
                    throw new ZKBusinessException(I18nUtil.i18nCode("acc_wiegand_tip4", sizeCodes[i]));
                }
            }
        }
        // 首尾处理
        if (cardFmt.indexOf("c") != 0)// 非首位包含C的格式进行首位替换为P
        {
            cardFmt.replace(0, 1, "p");
        }
        if (cardFmt.lastIndexOf("c") < cardFmt.length() - 1)// 非尾位包含C的格式进行尾位替换为P
        {
            cardFmt.replace(cardFmt.length() - 1, cardFmt.length(), "p");
        }

        String cardFmtStr = cardFmt.toString();
        if (cardFmtStr.matches("(p0+c+p)|(pc+0+p)")) {
            cardFmtStr = replace(cardFmtStr, "(p0+c)|(c0+p)", "0", "x");
        } else {
            cardFmtStr = replace(cardFmt.toString(), "(p0+s)|(s0+p)|(c0+p)", "0", "s");
            cardFmtStr = replace(cardFmtStr, "(p0+c)|(s0+c)", "0", "c");
        }
        return cardFmtStr;
    }

    public static String replace(String str, String regex, String oldStr, String newStr) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        if (matcher.find()) {
            String group = matcher.group();
            return str.replace(group, group.replaceAll(oldStr, newStr));
        }
        return str;
    }

    /**
     * 推荐奇偶校验格式
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/27 15:29
     * @param orgCardNos
     * @param bitscount
     * @return java.lang.String
     */
    public static String recommendParityFmt(String[] orgCardNos, int bitscount) {
        String parityFmt = null;
        for (int i = 0; i < orgCardNos.length; i++) {
            String orgStr = orgCardNos[i];
            if (StringUtils.isNotEmpty(orgStr)) {
                String fmt = recommendParityFmt(orgStr, bitscount);
                if (parityFmt == null) {
                    parityFmt = fmt;
                } else {
                    if (!parityFmt.equals(fmt))// 与前一张卡推荐的奇偶校验格式不同
                    {

                    }
                }
            }
        }
        return parityFmt;
    }

    /**
     * 推荐奇偶校验格式
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/27 15:32
     * @param orgStr
     * @param bitscount
     * @return java.lang.String
     */
    private static String recommendParityFmt(String orgStr, int bitscount) {
        StringBuffer parityFmt = new StringBuffer();
        int count = orgStr.replaceAll("0", "").length();
        int index = bitscount / 2;
        String beforeStr = orgStr.substring(0, index);
        if (count % 2 == 1) {
            count = beforeStr.replaceAll("0", "").length();
            if (count % 2 == 1)// 前面一半不符合1个数为偶数的偶校验，需要往前找1，或者往后找1
            {
                String endStr = orgStr.substring(index, orgStr.length());
                int lastIndexOf = beforeStr.lastIndexOf("1");
                int indexOf = endStr.indexOf("1");
                if (indexOf <= ((index - 1) - lastIndexOf)) {
                    index += indexOf + 1;
                } else {
                    index = lastIndexOf + 1;
                }
            }
            for (int j = 0; j < bitscount; j++) {

                if (j < index) {
                    parityFmt.append("e");
                } else {
                    parityFmt.append("o");
                }
            }
        } else {
            count = beforeStr.replaceAll("0", "").length();
            String endStr = orgStr.substring(index, orgStr.length());
            int indexOf = endStr.indexOf("1");
            int bindex = index;
            if (count % 2 == 1)// 前面一半不符合1个数为偶数的偶校验，需要往前找1
            {
                if (count > 1) {
                    int lastIndexOf = beforeStr.lastIndexOf("1");
                    index = lastIndexOf + 1;
                } else// 只有一个1,不符合偶校验，需要往后找1
                {
                    index += indexOf + 1;
                    endStr = orgStr.substring(index, orgStr.length());
                    indexOf = endStr.indexOf("1");
                    bindex = index;
                    bindex += indexOf + 1;
                }
            } else// 后一半不符合奇校验，需要往后找1
            {
                bindex += indexOf + 1;
            }
            for (int j = 0; j < bitscount; j++) {
                if (j < index) {
                    parityFmt.append("e");
                } else if (j < bindex) {
                    parityFmt.append("b");
                } else {
                    parityFmt.append("o");
                }
            }
        }
        return parityFmt.toString();
    }

    /**
     * 反推区位码
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date ` 15:33
     * @param orgCardNos
     * @param cardFmt
     * @return int
     */
    public static int reverseSize(String[] orgCardNos, String cardFmt) {
        int beginIndex = cardFmt.indexOf("x");
        if (beginIndex == -1) {
            return 0;
        }
        int endIndex = cardFmt.lastIndexOf("x") + 1;
        String sizeCode = "";
        for (int i = 0; i < orgCardNos.length; i++) {
            if (StringUtils.isNotEmpty(orgCardNos[i])) {
                String str = orgCardNos[i].substring(beginIndex, endIndex);// 二进制卡号
                if ("".equals(sizeCode)) {
                    sizeCode = str;
                } else {
                    if (!sizeCode.equals(str)) {
                        throw new ZKBusinessException(I18nUtil.i18nCode("acc_wiegand_tip1"));
                    }
                }
            }
        }
        if (!StringUtils.isNotEmpty(sizeCode)) {
            return 0;
        }
        return Integer.parseInt(sizeCode, 2);
    }

    /**
     * 获取当前周周一
     * 
     * <AUTHOR>
     * @since 2019-01-07 15:38
     * @Param []
     * @return
     */
    public static Date getWeekStartDate() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        Date date = cal.getTime();
        return date;
    }

    /**
     * 取得当月天数
     * 
     * <AUTHOR>
     * @since 2019-01-07 16:21
     * @Param []
     * @return
     */
    public static int getCurrentMonthLastDay() {
        Calendar a = Calendar.getInstance();
        a.set(Calendar.DATE, 1);// 把日期设置为当月第一天
        a.roll(Calendar.DATE, -1);// 日期回滚一天，也就是最后一天
        int maxDate = a.get(Calendar.DATE);
        return maxDate;
    }

    /**
     * 对比时间是否发生了变化
     *
     * @param date1:
     * @param date2:
     * @return java.lang.Boolean
     * <AUTHOR>
     * @throws @date 2021-11-26 16:59
     * @since 1.0.0
     */
    public static Boolean isSameDay(Date date1, Date date2) {
        if (date1 == null && date2 == null) {
            return true;
        } else if (date1 != null && date2 != null && DateUtil.isSameInstant(date1, date2)) {
            return true;
        }
        return false;
    }
}
