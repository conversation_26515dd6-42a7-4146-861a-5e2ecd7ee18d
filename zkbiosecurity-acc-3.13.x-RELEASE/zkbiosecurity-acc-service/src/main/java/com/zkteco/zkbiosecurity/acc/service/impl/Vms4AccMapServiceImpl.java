package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccMapPosService;
import com.zkteco.zkbiosecurity.acc.service.AccMapService;
import com.zkteco.zkbiosecurity.acc.vo.AccMapItem;
import com.zkteco.zkbiosecurity.acc.vo.AccMapPosItem;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.FileUtils;
import com.zkteco.zkbiosecurity.vms.service.Vms4AccMapService;
import com.zkteco.zkbiosecurity.vms.vo.Vms4AccMapItem;
import com.zkteco.zkbiosecurity.vms.vo.Vms4AccMapPosItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

@Service
public class Vms4AccMapServiceImpl implements Vms4AccMapService {

    @Autowired
    private AccMapService accMapService;
    @Autowired
    private AccMapPosService accMapPosService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AuthAreaService authAreaService;

    @Override
    public Vms4AccMapItem getItemById(String id) {
        AccMapItem item = accMapService.getItemById(id);
        //根据图片路径，把图片转成base64格式的字符串
        String imgBase64Str = "";
        String mapPath = item.getMapPath();
        File mapfile = new File(mapPath);
        if (!mapfile.isAbsolute()) {
            mapfile = new File(FileUtils.getLocalFullPath(mapPath));
        }
        InputStream in = null;
        if(mapfile.exists())
        {

            try {
                in = new FileInputStream(mapfile);
                byte[] data = new byte[in.available()];//读取图片字节数组
                in.read(data);
                imgBase64Str = Base64Utils.encodeToString(data);
                item.setMapPath("data:image/jpg;base64,"+imgBase64Str);
            } catch (IOException e) {
                throw new ZKBusinessException("AccMapController getMap base64 error");
            } finally {
                if (in != null){
                    try {
                        in.close();
                    }
                    catch (IOException e)
                    {
                        throw new ZKBusinessException("AccMapController getMap base64 error");
                    }
                }

            }
        }
        return  createMapItem(item);
    }

    //过滤地图上除摄像头的实体
    @Override
    public List<Vms4AccMapPosItem> getMapPosItemList(String mapId) {
        AccMapPosItem accMapPosItem = new AccMapPosItem();
        List<AccMapPosItem> accMapPosItems = new ArrayList<>();
        accMapPosItem.setMapId(mapId);
        List<AccMapPosItem> items = accMapPosService.getByCondition(accMapPosItem);
        for (AccMapPosItem pos : items) {
            if("VidChannel".equals(pos.getEntityType())) {
                pos.setEntityName(accMapPosService.getVidChannelName(pos.getEntityId()));
                accMapPosItems.add(pos);
            }
        }
        List<Vms4AccMapPosItem> vms4AccMapPosItems = new ArrayList<>();
        for (AccMapPosItem item : accMapPosItems){
            Vms4AccMapPosItem vms4AccMapPosItem = createMapPosItem(item);
            vms4AccMapPosItems.add(vms4AccMapPosItem);
        }
        return vms4AccMapPosItems;
    }

    @Override
    public List<TreeItem> createMapTree(String sessionId) {
        List<TreeItem> items = new ArrayList<TreeItem>();
        TreeItem item = null;
        TreeItem pItem = null;
        String areaIds = accDeviceService.getAreaIdsByAuthFilter(sessionId);
        AccMapItem accMapItem = new AccMapItem();
        if (StringUtils.isNotBlank(areaIds)) {
            accMapItem.setAreaIdIn(areaIds);
        }
        List<AccMapItem> accMapList = accMapService.getByCondition(accMapItem);//根据登录用户权限过滤
        if(accMapList.isEmpty())
        {
            return new ArrayList<>();//没有地图返回空
        }
        List<String> areaIdList = new ArrayList<>();
        for (AccMapItem accMap : accMapList) {
            item = new TreeItem();
            item.setId(accMap.getId());
            item.setText(accMap.getName());
            item.setIm0("base_map.png");
            item.setIm1("base_map.png");
            item.setIm2("base_map.png");
            pItem = new TreeItem(accMap.getAuthAreaId()+"_");//把地图所属区域作为父节点
            item.setParent(pItem);

            items.add(item);

            areaIdList.add(accMap.getAuthAreaId());
        }

        //获取所有区域，区域ID都加上"_"，用于前端点击过滤
        List<AuthAreaItem> authAreaItemList = authAreaService.getItemsByIds(areaIdList);
        for (AuthAreaItem authAreaItem : authAreaItemList) {
            item = new TreeItem();
            item.setId(authAreaItem.getId()+"_");
            item.setText(authAreaItem.getName());
            item.setIm0("base_area.png");
            item.setIm1("base_area.png");
            item.setIm2("base_area.png");
            if(authAreaItem.getParentId() != null)
            {
                pItem = new TreeItem(authAreaItem.getParentId()+"_");
            }
            else
            {
                pItem = new TreeItem("0");
            }
            item.setParent(pItem);

            items.add(item);
        }

        return items;
    }

    @Override
    public  List<String> getMapChannel(String mapId) {
        AccMapPosItem accMapPosItem = new AccMapPosItem();
        accMapPosItem.setMapId(mapId);
        List<String> channelIds = new ArrayList<>();
        List<AccMapPosItem> mapPosItemList = accMapPosService.getByCondition(accMapPosItem);
        for (AccMapPosItem pos : mapPosItemList) {
            if("VidChannel".equals(pos.getEntityType())) {
                String channelId = pos.getEntityId();
                channelIds.add(channelId);
            }
        }
        return channelIds;
    }

    private Vms4AccMapItem createMapItem(AccMapItem accMapItem) {
        Vms4AccMapItem vmsItem = new Vms4AccMapItem();
        vmsItem.setId(accMapItem.getId());
        vmsItem.setAuthAreaId(accMapItem.getAuthAreaId());
        vmsItem.setName(accMapItem.getName());
        vmsItem.setMapPath(accMapItem.getMapPath());
        vmsItem.setAreaIdIn(accMapItem.getAreaIdIn());
        vmsItem.setHeight(accMapItem.getHeight());
        vmsItem.setWidth(accMapItem.getWidth());
        return vmsItem;
    }

    private Vms4AccMapPosItem createMapPosItem(AccMapPosItem accMapPosItem) {
        Vms4AccMapPosItem vmsItem = new Vms4AccMapPosItem();
        vmsItem.setId(accMapPosItem.getId());
        vmsItem.setEntityId(accMapPosItem.getEntityId());
        vmsItem.setEntityType(accMapPosItem.getEntityType());
        vmsItem.setWidth(accMapPosItem.getWidth());
        vmsItem.setEntityName(accMapPosItem.getEntityName());
        vmsItem.setLeftX(accMapPosItem.getLeftX());
        vmsItem.setTopY(accMapPosItem.getTopY());
        vmsItem.setMapId(accMapPosItem.getMapId());
        vmsItem.setMapName(accMapPosItem.getMapName());
        return vmsItem;
    }
}
