package com.zkteco.zkbiosecurity.acc.data.upgrade;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * 版本升级
 *
 * <AUTHOR>
 * @date 2022/11/16 17:31
 */
@Slf4j
@Component
public class AccVer3_5_0 implements UpgradeVersionManager {
    @Autowired
    private AuthPermissionService authPermissionService;

    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public String getVersion() {
        return "v3.5.0";
    }

    @Override
    public String getModule() {
        return BaseConstants.ACC;
    }

    @Override
    public boolean executeUpgrade() {
        // 升级菜单
        updateMenu();
        return true;
    }

    private void updateMenu() {
        AuthPermissionItem topMenuItem = authPermissionService.getItemByCode("AccReports");
        if (Objects.nonNull(topMenuItem)) {
            // 添加报警监控报表菜单
            AuthPermissionItem subMenuItem = authPermissionService.getItemByCode("AccAlarmReport");
            if (Objects.isNull(subMenuItem)) {
                // 报警监控报表
                subMenuItem = new AuthPermissionItem("AccAlarmReport", "acc_alarm_list", "acc:alarmReport",
                    AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 5);
                subMenuItem.setParentId(topMenuItem.getId());
                subMenuItem.setActionLink("accAlarmMonitorReport.do");
                subMenuItem = authPermissionService.initData(subMenuItem);
                // 报警监控报表-刷新
                AuthPermissionItem subButtonItem = new AuthPermissionItem("AccAlarmReportRefresh", "common_op_refresh",
                    "acc:alarmReport:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
                subButtonItem.setParentId(subMenuItem.getId());
                authPermissionService.initData(subButtonItem);
                // 报警监控报表-导出
                subButtonItem = new AuthPermissionItem("AccAlarmReportExport", "common_op_export",
                    "acc:alarmReport:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);
            }

            // 报警监控处理历史报表
            subMenuItem = authPermissionService.getItemByCode("AccAlarmHistory");
            if (Objects.isNull(subMenuItem)) {
                // 报警监控处理历史
                subMenuItem = new AuthPermissionItem("AccAlarmHistory", "acc_alarm_history", "acc:alarmHistory",
                    AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 6);
                subMenuItem.setParentId(topMenuItem.getId());
                subMenuItem.setActionLink("accAlarmMonitor.do?history");
                subMenuItem = authPermissionService.initData(subMenuItem);
                // 报警监控处理历史-刷新
                AuthPermissionItem subButtonItem = new AuthPermissionItem("AccAlarmHistoryRefresh", "common_op_refresh",
                    "acc:alarmHistory:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
                subButtonItem.setParentId(subMenuItem.getId());
                authPermissionService.initData(subButtonItem);
                // 报警监控处理历史-导出
                subButtonItem = new AuthPermissionItem("AccAlarmHistoryExport", "common_op_export",
                    "acc:alarmHistory:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);
            }
        }
        // 添加设备按钮
        topMenuItem = authPermissionService.getItemByCode("AccDeviceManager");
        if (Objects.nonNull(topMenuItem)) {
            AuthPermissionItem subMenuItem = authPermissionService.getItemByCode("AccDevice");
            if (Objects.nonNull(subMenuItem)) {
                // 添加设置Ntp服务器菜单按钮
                AuthPermissionItem subButtonItem = authPermissionService.getItemByCode("AccDeviceSetNTP");
                if (subButtonItem == null) {
                    // 设备-设置Ntp服务器
                    subButtonItem = new AuthPermissionItem("AccDeviceSetNTP", "acc_device_setNTPService",
                        "acc:device:setDevNTP", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 35);
                    subButtonItem.setParentId(subMenuItem.getId());
                    authPermissionService.initData(subButtonItem);
                }
                // 添加替换设备菜单按钮
                subButtonItem = authPermissionService.getItemByCode("AccDeviceSetNTP");
                if (subButtonItem == null) {
                    // 设备-设备替换
                    subButtonItem = new AuthPermissionItem("AccDeviceExchange", "acc_dev_replace", "acc:device:replace",
                        AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 36);
                    subButtonItem.setParentId(subMenuItem.getId());
                    authPermissionService.initData(subButtonItem);
                }
            }
            // 添加报警监控-查看处理历史 菜单升级
            subMenuItem = authPermissionService.getItemByCode("AccAlarmMonitor");
            if (Objects.nonNull(subMenuItem)) {
                AuthPermissionItem subButtonItem = authPermissionService.getItemByCode("AccAlarmMonitorHistory");
                if (topMenuItem != null && subMenuItem != null && subButtonItem == null) {
                    subButtonItem = new AuthPermissionItem("AccAlarmMonitorHistory", "acc_alarm_history",
                        "acc:alarmMonitor:history", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
                    subButtonItem.setParentId(subMenuItem.getId());
                    authPermissionService.initData(subButtonItem);
                }
            }
        }
        topMenuItem = authPermissionService.getItemByCode("AccAccessManager");
        if (Objects.nonNull(topMenuItem)) {
            AuthPermissionItem subMenuItem = authPermissionService.getItemByCode("AccLevel");
            if (Objects.nonNull(subMenuItem)) {
                AuthPermissionItem subButtonItem = authPermissionService.getItemByCode("AccLevelExport");
                if (subButtonItem != null) {
                    // 权限组-导出 修改名称
                    subButtonItem.setCode("AccLevelExport");
                    subButtonItem.setName("acc_level_exportLevel");
                    authPermissionService.saveItem(subButtonItem);
                }
                // 新增权限组-导出权限组门信息
                subButtonItem = authPermissionService.getItemByCode("AccLevelDoorExport");
                if (subButtonItem == null) {
                    subButtonItem = new AuthPermissionItem("AccLevelDoorExport", "acc_level_exportLevelDoor",
                        "acc:levelDoor:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 18);
                    subButtonItem.setParentId(subMenuItem.getId());
                    authPermissionService.initData(subButtonItem);
                }
                // 新增权限组-导入
                subButtonItem = authPermissionService.getItemByCode("AccLevelImport");
                if (subButtonItem == null) {
                    subButtonItem = new AuthPermissionItem("AccLevelImport", "acc_level_importLevel",
                        "acc:level:import", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 19);
                    subButtonItem.setParentId(subMenuItem.getId());
                    authPermissionService.initData(subButtonItem);
                }
                // 新增权限组-导入门信息
                subButtonItem = authPermissionService.getItemByCode("AccLevelDoorImport");
                if (subButtonItem == null) {
                    subButtonItem = new AuthPermissionItem("AccLevelDoorImport", "acc_level_importLevelDoor",
                        "acc:levelDoor:import", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 20);
                    subButtonItem.setParentId(subMenuItem.getId());
                    authPermissionService.initData(subButtonItem);
                }
            }
            subMenuItem = authPermissionService.getItemByCode("AccPersonLevelByLevel");
            if (Objects.nonNull(subMenuItem)) {
                // 按权限组设置-导出人员信息
                AuthPermissionItem subButtonItem = authPermissionService.getItemByCode("AccPersonLevelByLevelExport");
                if (subButtonItem != null) {
                    subButtonItem.setName("acc_level_exportLevelPerson");
                    authPermissionService.saveItem(subButtonItem);
                }
                // 按权限组设置-导入人员信息
                subButtonItem = authPermissionService.getItemByCode("AccPersonLevelByLevelImport");
                if (subButtonItem == null) {
                    subButtonItem = new AuthPermissionItem("AccPersonLevelByLevelImport", "acc_level_importLevelPerson",
                        "acc:personLevelByLevel:import", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
                    subButtonItem.setParentId(subMenuItem.getId());
                    authPermissionService.initData(subButtonItem);
                }
            }
        }
    }
}
