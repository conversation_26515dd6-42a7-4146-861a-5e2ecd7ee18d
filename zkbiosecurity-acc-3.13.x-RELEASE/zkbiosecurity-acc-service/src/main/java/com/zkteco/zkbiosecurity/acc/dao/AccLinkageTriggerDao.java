/**
 * File Name: AccLinkageTrigger
 * Created by GenerationTools on 2018-03-16 下午04:41
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccLinkageTrigger;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * 对应百傲瑞达 AccLinkageTriggerDao
 * <AUTHOR>
 * @date:	2018-03-16 下午04:41
 * @version v1.0
 */
public interface AccLinkageTriggerDao extends BaseDao<AccLinkageTrigger, String> {

    List<AccLinkageTrigger> findByAccLinkage_Id(String accLinkageId);

    @Query(value="SELECT accLinkage.id FROM AccLinkageTrigger WHERE linkageIndex = ?1 and accLinkage.accDevice.id = ?2")
    List<String> getLinkageIdByIndexAndDevId(int linkIndex, String devId);

    @Query(value="SELECT e FROM AccLinkageTrigger e WHERE e.accLinkage.accDevice.id = ?1 AND e.triggerCond = ?2")
    AccLinkageTrigger getByDevIdAndTriggerCond(String devId, Short eventNo);

    List<AccLinkageTrigger> findByAccLinkage_IdAndAccLinkageInOut_Id(String accLinkageId, String accLinkageInOutId);
}