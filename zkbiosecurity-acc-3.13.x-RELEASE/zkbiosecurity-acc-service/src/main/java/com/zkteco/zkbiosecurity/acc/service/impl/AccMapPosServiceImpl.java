/**
 * File Name: AccMapPosServiceImpl
 * Created by GenerationTools on 2018-03-20 下午02:07
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.dao.AccMapPosDao;
import com.zkteco.zkbiosecurity.acc.model.AccMapPos;
import com.zkteco.zkbiosecurity.acc.service.AccMapPosService;
import com.zkteco.zkbiosecurity.acc.vo.AccMapPosItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.vid.service.Vid4AccMapPosService;
import com.zkteco.zkbiosecurity.vid.service.Vid4OtherGetVidChannel2EntityService;
import com.zkteco.zkbiosecurity.vid.vo.Vid4OtherChannelItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 对应百傲瑞达 AccMapPosServiceImpl
 * <AUTHOR>
 * @date:	2018-03-20 下午02:07
 * @version v1.0
 */
@Service
@Transactional
public class AccMapPosServiceImpl implements AccMapPosService, Vid4AccMapPosService {
	@Autowired
	private AccMapPosDao accMapPosDao;
	@Autowired(required = false)
	private Vid4OtherGetVidChannel2EntityService vid4OtherGetVidChannel2EntityService;

	@Override
	public AccMapPosItem saveItem(AccMapPosItem item){
		AccMapPos accMapPos = Optional.ofNullable(item)
						.map(i->i.getId())
						.filter(StringUtils::isNotBlank)
						.flatMap(id->accMapPosDao.findById(id))
						.orElse(new AccMapPos());
		ModelUtil.copyPropertiesIgnoreNull(item, accMapPos);
		accMapPosDao.save(accMapPos);
		item.setId(accMapPos.getId());
		return item;
	}

	@Override
	public List<AccMapPosItem> getByCondition(AccMapPosItem condition){
		return (List<AccMapPosItem>) accMapPosDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
	}

	@Override
	public Pager getItemsByPage(BaseItem condition, int page, int size) {
		return accMapPosDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
	}

	@Override
	public boolean deleteByIds(String ids) {
		if (StringUtils.isNotEmpty(ids)) {
			String[] idArray = StringUtils.split(ids, ",");
			for (String id : idArray) {
				accMapPosDao.deleteById(id);
			}
		}
		return false;
	}

	@Override
	public AccMapPosItem getItemById(String id) {
		List<AccMapPosItem> items = getByCondition(new AccMapPosItem(id));
		return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
	}

	@Override
	public String getEntityIdsByMapIdAndEntityType(String mapId, String entityType) {
		String filterEntityIds = "";
		List<String> entityIdList = accMapPosDao.getEntityIdByMapIdAndEntityType(mapId,entityType);
		for (String entityId : entityIdList) {
			filterEntityIds += entityId + ",";
		}
		
		return StringUtils.isNoneBlank(filterEntityIds)? filterEntityIds.substring(0, filterEntityIds.length()-1):"";
	}

	@Override
	public void delMapPosByEntityId(List<String> delIdList, String entityType) {
		accMapPosDao.delMapPosByEntityId(delIdList, entityType);
	}

	/**
	 * 获取摄像头节点的名称
	 *
	 * @param entityId
	 * @return
	 */
	@Override
	public String getVidChannel2EntityName(String entityId) {
		if (Objects.nonNull(vid4OtherGetVidChannel2EntityService)) {
			return vid4OtherGetVidChannel2EntityService.getVidChannel2EntityName(entityId);
		}
		return "";
	}

	@Override
	public String getVidChannelName(String entityId) {
		if (Objects.nonNull(vid4OtherGetVidChannel2EntityService)) {
			return vid4OtherGetVidChannel2EntityService.getVidChannelNameById(entityId);
		}
		return "";
	}

	@Override
	public Map<String, String> getVidChannelById(String entityId) {
		Vid4OtherChannelItem vid4OtherChannelItem = null;
		if (Objects.nonNull(vid4OtherGetVidChannel2EntityService)) {
			vid4OtherChannelItem = vid4OtherGetVidChannel2EntityService.getVidChannelById(entityId);
		}
		Map<String, String> map = new HashMap<>(16);
		if (Objects.nonNull(vid4OtherChannelItem)) {
			map.put("name", vid4OtherChannelItem.getName());
			map.put("enabled", String.valueOf(vid4OtherChannelItem.getEnabled()));
		}
		return map;
	}

	@Override
	public void delVidMapPostByEntityId(List<String> delIdList) {
		accMapPosDao.delMapPosByEntityId(delIdList, "VidChannel");
	}
}