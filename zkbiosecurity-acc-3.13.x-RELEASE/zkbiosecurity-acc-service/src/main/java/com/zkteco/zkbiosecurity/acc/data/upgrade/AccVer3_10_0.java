package com.zkteco.zkbiosecurity.acc.data.upgrade;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/9/27 14:00
 * @since 1.0.0
 */
@Slf4j
@Component
public class AccVer3_10_0 implements UpgradeVersionManager {

    @Autowired
    private AuthPermissionService authPermissionService;

    @Override
    public String getVersion() {
        return "v3.10.0";
    }

    @Value("${system.language:zh_CN}")
    private String language;
    @Value("${system.isCloud:false}")
    private Boolean isCloud;

    @Override
    public String getModule() {
        return BaseConstants.ACC;
    }

    @Override
    public boolean executeUpgrade() {
        // 升级菜单
        updateMenu();
        return true;
    }

    /**
     * 更新菜单权限
     * 
     * <AUTHOR>
     * @date 2023-09-27 14:02
     * @since 1.0.0
     */
    private void updateMenu() {
        // 屏蔽远程释放按键
        AuthPermissionItem subButtonItem = authPermissionService.getItemByCode("AccRTMonitorOpenFloor");
        if (Objects.nonNull(subButtonItem)) {
            subButtonItem.setAvailable(ZKConstant.FALSE);
            authPermissionService.saveItem(subButtonItem);
        }
        // 屏蔽远程锁定按键
        subButtonItem = authPermissionService.getItemByCode("AccRTMonitorCloseFloor");
        if (Objects.nonNull(subButtonItem)) {
            subButtonItem.setAvailable(ZKConstant.FALSE);
            authPermissionService.saveItem(subButtonItem);
        }
        // 补充电子地图添加摄像头权限
        AuthPermissionItem subMenuItem = authPermissionService.getItemByCode("AccMap");
        subButtonItem = authPermissionService.getItemByCode("AccMapAddChannelToMap");
        if (subMenuItem != null && subButtonItem == null) {
            subButtonItem = new AuthPermissionItem("AccMapAddChannelToMap", "acc_map_addChannel",
                "acc:map:addChannelToMap", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);
        }
        // 屏蔽设备-修改RS485地址功能按钮
        subButtonItem = authPermissionService.getItemByCode("AccDeviceUpdateRs485Addr");
        if (Objects.nonNull(subButtonItem)) {
            subButtonItem.setAvailable(ZKConstant.FALSE);
            authPermissionService.saveItem(subButtonItem);
        }
        AuthPermissionItem topMenuItem = authPermissionService.getItemByCode("AccAccessManager");
        if (Objects.nonNull(topMenuItem)) {
            subMenuItem = authPermissionService.getItemByCode("AccVerifyModeRule");
            // 添加验证方式规则相关菜单和按钮升级处理
            if (subMenuItem == null) {
                // 验证方式规则
                subMenuItem = new AuthPermissionItem("AccVerifyModeRule", "acc_leftMenu_verifyModeRule",
                    "acc:verifyModeRule", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 13);
                subMenuItem.setParentId(topMenuItem.getId());
                subMenuItem.setActionLink("accVerifyModeRule.do");
                subMenuItem = authPermissionService.initData(subMenuItem);
                // 验证方式规则-刷新
                subButtonItem = new AuthPermissionItem("AccVerifyModeRuleRefresh", "common_op_refresh",
                    "acc:verifyModeRule:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
                subButtonItem.setParentId(subMenuItem.getId());
                authPermissionService.initData(subButtonItem);
                // 验证方式规则-新增
                subButtonItem = new AuthPermissionItem("AccVerifyModeRuleAdd", "common_op_new",
                    "acc:verifyModeRule:add", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
                subButtonItem.setParentId(subMenuItem.getId());
                authPermissionService.initData(subButtonItem);
                // 验证方式规则-编辑
                subButtonItem = new AuthPermissionItem("AccVerifyModeRuleEdit", "common_op_edit",
                    "acc:verifyModeRule:edit", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
                subButtonItem.setParentId(subMenuItem.getId());
                authPermissionService.initData(subButtonItem);
                // 验证方式规则-删除
                subButtonItem = new AuthPermissionItem("AccVerifyModeRuleDel", "common_op_del",
                    "acc:verifyModeRule:del", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
                subButtonItem.setParentId(subMenuItem.getId());
                authPermissionService.initData(subButtonItem);
                // 验证方式规则-添加门
                subButtonItem = new AuthPermissionItem("AccVerifyModeRuleAddDoor", "acc_map_addDoor",
                    "acc:verifyModeRule:addDoor", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
                subButtonItem.setParentId(subMenuItem.getId());
                authPermissionService.initData(subButtonItem);
                // 验证方式规则-删除门
                subButtonItem = new AuthPermissionItem("AccVerifyModeRuleDelDoor", "acc_level_doorDelete",
                    "acc:verifyModeRule:delDoor", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
                subButtonItem.setParentId(subMenuItem.getId());
                authPermissionService.initData(subButtonItem);
            }
            subMenuItem = authPermissionService.getItemByCode("AccVerifyModeRulePersonGroup");
            // 验证方式规则人员组菜单、按钮升级处理
            if (subMenuItem == null) {
                // 验证方式规则人员组
                subMenuItem =
                    new AuthPermissionItem("AccVerifyModeRulePersonGroup", "acc_leftMenu_verifyModeRulePersonGroup",
                        "acc:verifyModeRulePersonGroup", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 14);
                subMenuItem.setParentId(topMenuItem.getId());
                subMenuItem.setActionLink("accVerifyModeRulePersonGroup.do");
                subMenuItem = authPermissionService.initData(subMenuItem);
                // 验证方式规则人员组-刷新
                subButtonItem = new AuthPermissionItem("AccVerifyModeRulePersonGroupRefresh", "common_op_refresh",
                    "acc:verifyModeRulePersonGroup:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
                subButtonItem.setParentId(subMenuItem.getId());
                authPermissionService.initData(subButtonItem);
                // 验证方式规则人员组-添加人员
                subButtonItem = new AuthPermissionItem("AccVerifyModeRulePersonGroupAddPerson", "pers_common_addPerson",
                    "acc:verifyModeRulePersonGroup:addPerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
                subButtonItem.setParentId(subMenuItem.getId());
                authPermissionService.initData(subButtonItem);
                // 验证方式规则人员组-删除人员
                subButtonItem = new AuthPermissionItem("AccVerifyModeRulePersonGroupDelPerson", "pers_common_delPerson",
                    "acc:verifyModeRulePersonGroup:delPerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
                subButtonItem.setParentId(subMenuItem.getId());
                authPermissionService.initData(subButtonItem);
            }
        }
        if (!"zh_CN".equals(language) && !isCloud) {
            AuthPermissionItem appModuleItem = null;
            AuthPermissionItem appMenuItem = null;
            AuthPermissionItem appButtonItem = null;

            appModuleItem = authPermissionService.getItemByCode("App");
            if (null == appModuleItem) {
                // 管理员APP模块
                appModuleItem = new AuthPermissionItem("App", "app_module", "app",
                    AuthContants.RESOURCE_TYPE_APP_SYSTEM, ZKConstant.TRUE, 998);
                authPermissionService.initData(appModuleItem);
            }
            appMenuItem = authPermissionService.getItemByCode("AppAcc");
            if (null == appMenuItem) {
                appMenuItem = new AuthPermissionItem("AppAcc", "app_acc", "app:APPacc",
                    AuthContants.RESOURCE_TYPE_APP_MENU, ZKConstant.TRUE, 2);
                appMenuItem.setParentId(appModuleItem.getId());
                appMenuItem = authPermissionService.initData(appMenuItem);
            }

            // 门禁--报表
            appButtonItem = new AuthPermissionItem("AppAccReport", "app_acc_report", "app:APPaccReport",
                AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 4);
            appButtonItem.setParentId(appMenuItem.getId());
            authPermissionService.initData(appButtonItem);
            // 门禁--远程控制
            appButtonItem = new AuthPermissionItem("AppAccRemoteControl", "app_acc_remoteControl",
                "app:APPaccRemoteControl", AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 5);
            appButtonItem.setParentId(appMenuItem.getId());
            authPermissionService.initData(appButtonItem);
            // 门禁--锁定
            appButtonItem = new AuthPermissionItem("AppAccLockdown", "app_acc_lockdown", "app:APPaccLockdown",
                AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 6);
            appButtonItem.setParentId(appMenuItem.getId());
            authPermissionService.initData(appButtonItem);
        }
    }
}
