package com.zkteco.zkbiosecurity.acc.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.dao.*;
import com.zkteco.zkbiosecurity.acc.model.*;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.utils.AccDataUtil;
import com.zkteco.zkbiosecurity.acc.utils.AccDeviceUtil;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.auth.provider.AuthSessionProvider;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthUserItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.EncrypAESUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Transactional
public class Acc4PersPersonServiceImpl implements Acc4PersPersonService {
    @Autowired
    private AccPersonDao accPersonDao;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private AccLevelDao accLevelDao;
    @Autowired
    private AccPersonCombOpenPersonDao accPersonCombOpenPersonDao;
    @Autowired
    private AccPersonVerifyModeRuleDao accPersonVerifyModeRuleDao;
    @Autowired
    private AccLevelPersonDao accLevelPersonDao;
    @Autowired(required = false)
    private Acc4AccZonePersonService acc4AccZonePersonService;
    @Autowired(required = false)
    private Acc4AccGlobalApbService acc4AccGlobalApbService;
    @Autowired(required = false)
    private Acc4AccGlobalLinkageService acc4AccGlobalLinkageService;
    @Autowired(required = false)
    private Acc4AccPersonLimitService acc4AccPersonLimitService;
    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AccLevelDeptDao accLevelDeptDao;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private AuthSessionProvider authSessionProvider;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AccLevelDoorDao accLevelDoorDao;
    @Autowired(required = false)
    private AccProExtService accProExtService;
    @Autowired
    private AccTimeSegService accTimeSegService;

    @Override
    public Boolean editAccPerson(Acc4PersPersonItem accPerson) {
        boolean modifyAccInfo = false;
        // 前端有提交权限组信息时（编辑的时候有标签页的权限时），才进行权限组的下发/变更
        AccPerson tempPerson = accPersonDao.findByPersonId(accPerson.getPersonId());
        if (tempPerson == null) {
            // 不存在则初始化人员
            tempPerson = new AccPerson();
            modifyAccInfo = true;
        } else if (!(accPerson.getSuperAuth().equals(tempPerson.getSuperAuth())
            && accPerson.getPrivilege().equals(tempPerson.getPrivilege())
            && accPerson.getDelayPassage().equals(tempPerson.getDelayPassage())
            && accPerson.getDisabled().equals(tempPerson.getDisabled())
            && accPerson.getIsSetValidTime().equals(tempPerson.getIsSetValidTime()))) {
            modifyAccInfo = true;
        } else if (accPerson.getIsSetValidTime().equals(tempPerson.getIsSetValidTime())) {
            if (!(AccDataUtil.isSameDay(accPerson.getStartTime(), tempPerson.getStartTime())
                && AccDataUtil.isSameDay(accPerson.getEndTime(), tempPerson.getEndTime()))) {
                modifyAccInfo = true;
            }
        }
        ModelUtil.copyPropertiesIgnoreNull(accPerson, tempPerson);
        // 设置成没有有效时间时，需要把开始时间和结束时间置空
        if (accPerson.getUpdateAccSet() && !accPerson.getIsSetValidTime()) {
            tempPerson.setStartTime(null);
            tempPerson.setEndTime(null);
        }
        // 以下字段需要默认值处理
        if (tempPerson.getIsSetValidTime() == null) {
            tempPerson.setIsSetValidTime(false);

        }
        if (tempPerson.getDelayPassage() == null) {
            tempPerson.setDelayPassage(false);
        }
        if (tempPerson.getSuperAuth() == null) {
            tempPerson.setSuperAuth((short)0);
        }
        if (tempPerson.getDisabled() == null) {
            tempPerson.setDisabled(false);
        }
        if (tempPerson.getPrivilege() == null) {
            tempPerson.setPrivilege((short)0);
        }
        accPersonDao.save(tempPerson);
        List<String> levelIdList = null;
        List<String> exitLevelIds = new ArrayList<>();
        PersPersonItem persPersonItem = persPersonService.getItemById(accPerson.getPersonId());
        if (accPerson.getPersonModify() || accPerson.getUpdateAccSet()) {
            exitLevelIds = accLevelPersonDao.getLevelIdByPersonId(accPerson.getPersonId());
        }
        boolean accLevelModify = false;
        if (accPerson.getUpdateAccSet()) {
            String accPersonLevelIds = accPerson.getPersonLevelIds();
            // 根据传过来的权限组id再进行一次查询，以数据库为准，避免并发操作导致数据不一致
            List<String> accPersonLevelIdList = new ArrayList<>();
            if (StringUtils.isNotBlank(accPersonLevelIds)) {
                accPersonLevelIdList =
                    accLevelDao.getLevelIdsByLevelIds((List<String>)CollectionUtil.strToList(accPersonLevelIds));
            }
            // 复制一份id用于存放已有权限组id和传入的权限组id，用于取交集
            List<String> tempIdList = new ArrayList<>(accPersonLevelIdList);
            tempIdList.retainAll(exitLevelIds);
            // 原有权限组id去除交集部分得出为需要删除的权限组id
            exitLevelIds.removeAll(tempIdList);
            if (!exitLevelIds.isEmpty()) {
                accLevelService.delBatchLevel(exitLevelIds,
                    (List<String>)CollectionUtil.strToList(accPerson.getPersonId()));
                delPersonLevelFromDev(exitLevelIds, persPersonItem.getPin());
                accLevelModify = true;
            }
            // 复制一份用于查询发送数据
            levelIdList = new ArrayList<>(accPersonLevelIdList);
            // 传入的权限组id去除交集id为需要增加的id
            accPersonLevelIdList.removeAll(tempIdList);

            if (!accPersonLevelIdList.isEmpty()) {
                accLevelService.addLevelByParamIds(accPerson.getPersonId(), StringUtils.join(accPersonLevelIdList, ","),
                    "person");
                accLevelModify = true;
            }
        } else if (accPerson.getPersonModify() && !accPerson.getUpdateAccSet()) {
            levelIdList = exitLevelIds;
        }

        if (levelIdList != null && !levelIdList.isEmpty()) {
            Map<AccDevice, List<Object[]>> levelDoorMap = new HashMap<>();
            final List<AccLevelDoor> doorLevelList = accLevelDoorDao.findByAccLevel_IdIn(levelIdList);
            doorLevelList.forEach(levelDoor -> {
                final AccLevel accLevel = levelDoor.getAccLevel();
                final AccDoor accDoor = levelDoor.getAccDoor();
                Object[] res = new Object[4];
                res[0] = persPersonItem.getPin();
                res[1] = accTimeSegService.getBusinessIdByTimeSegId(accLevel.getTimeSegId());
                res[2] = accDoor.getDoorNo();
                res[3] = accLevel.getBusinessId();
                final AccDevice device = accDoor.getDevice();

                if (levelDoorMap.containsKey(device)) {
                    levelDoorMap.get(device).add(res);
                } else {
                    List<Object[]> resList = new ArrayList<>();
                    resList.add(res);
                    levelDoorMap.put(device, resList);
                }
            });
            List<AccDevice> accDeviceList = new ArrayList<>(levelDoorMap.keySet());
            // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了
            AccDeviceUtil.moveUpParentDevList(accDeviceList);
            // 有修改人员信息、门禁人员信息或者权限组信息就下发人员信息到设备
            if (modifyAccInfo || accPerson.getPersonModify() || accLevelModify) {
                AccPersonInfoItem accPersonInfoItem =
                    accLevelService.getPersonByIds((List<String>)CollectionUtil.strToList(persPersonItem.getId()));
                accDeviceList.forEach(accDevice -> {
                    setPersonInfoToDev(accDevice, accPersonInfoItem);
                });
            }
            if (accLevelModify) {
                Map<String, List<AccPersonLevelOptItem>> personLevelOptMap = new HashMap<>();
                for (Map.Entry<AccDevice, List<Object[]>> entry : levelDoorMap.entrySet()) {
                    putPersLevelToMap(persPersonItem, personLevelOptMap, entry.getKey(), entry.getValue());
                }
                accDeviceList.forEach(accDevice -> {
                    accDevCmdManager.setPersonLevelToDev(accDevice.getSn(), personLevelOptMap.get(accDevice.getId()),
                        false);
                });
            }
        }
        return true;
    }

    /**
     * 只下发人员信息，不下发人员权限
     * 
     * @param accDevice:
     * @param accPersonInfoItem:
     * @return void
     * <AUTHOR>
     * @throws @date 2021-12-02 9:40
     * @since 1.0.0
     */
    private void setPersonInfoToDev(AccDevice accDevice, AccPersonInfoItem accPersonInfoItem) {
        List<String> pins = new ArrayList<>();
        for (AccPersonOptItem accPersonOptBean : accPersonInfoItem.getPersonList()) {
            pins.add(accPersonOptBean.getPin());
        }
        // 多卡数据类似于指纹，下发之前需要按照pin号全部删除然后再重新下发
        accDevCmdManager.delMulCardUserFromDev(accDevice.getSn(), pins, false);
        // 根据pin号删除比对照片
        accDevCmdManager.delAllBiophotoByPins(accDevice.getSn(), pins, false);
        accDevCmdManager.delPersonBioTemplateFromDev(accDevice.getSn(), pins, false);
        // 下发人员信息
        accDevCmdManager.setPersonToDev(accDevice.getSn(), accPersonInfoItem.getPersonList(), false);
        List<AccBioTemplateItem> allBioTempBeans = accPersonInfoItem.getAccBioTemplateItemList();
        if (allBioTempBeans != null && !allBioTempBeans.isEmpty()) {
            // 下发生物模板信息
            accDevCmdManager.setPersonBioTemplateToDev(accDevice.getSn(), allBioTempBeans, false);
        }
        // 拓展接口，为了兼容华为pad设备下发downLoad命令通知设备已下发完成的功能 by mingfa.zheng 20200727
        accDevCmdManager.downloadOver(accDevice.getSn(), false);
    }

    /**
     * 构造人员权限组
     * 
     * @param persPersonItem
     * @param personLevelOptMap
     * @param dev
     * @param accLevelDoorList
     */
    private void putPersLevelToMap(PersPersonItem persPersonItem,
        Map<String, List<AccPersonLevelOptItem>> personLevelOptMap, AccDevice dev, List<Object[]> accLevelDoorList) {
        List<AccPersonLevelOptItem> personLevelList = new ArrayList<>();
        Map<String, List<Short>> pinTimeSegDoorMap = new HashMap<>();
        if (accLevelDoorList != null) {
            for (Object[] ob : accLevelDoorList) {
                String tempKey;
                if (accProExtService != null) {
                    tempKey = persPersonItem.getPin() + "_" + ob[1] + "_" + ob[3];
                } else {
                    tempKey = persPersonItem.getPin() + "_" + ob[1];
                }
                Short doorNo = Short.parseShort(ob[2] + "");
                if (!pinTimeSegDoorMap.containsKey(tempKey)) {
                    List<Short> doorNoList = new ArrayList<>();
                    doorNoList.add(doorNo);
                    pinTimeSegDoorMap.put(tempKey, doorNoList);
                } else {
                    pinTimeSegDoorMap.get(tempKey).add(doorNo);
                }
            }
        }
        // 拼装下发人员权限的数据。
        for (String key : pinTimeSegDoorMap.keySet()) {
            String[] pinTimeSegId = key.split("_");
            AccPersonLevelOptItem accPersonLevelOptItem = new AccPersonLevelOptItem();
            accPersonLevelOptItem.setPin(pinTimeSegId[0]);
            accPersonLevelOptItem.setTimeSegId(Long.parseLong(pinTimeSegId[1]));
            if (accProExtService != null) {
                accPersonLevelOptItem.setLevelId(Long.valueOf(pinTimeSegId[2]));
            }
            accPersonLevelOptItem.setDeviceId(dev.getBusinessId());
            List<Short> doorNoList = pinTimeSegDoorMap.get(key);
            if (doorNoList != null && doorNoList.size() > 0) {
                accPersonLevelOptItem.setDoorNoList(doorNoList);
                personLevelList.add(accPersonLevelOptItem);
            }
        }
        personLevelOptMap.put(dev.getId(), personLevelList);
    }

    @Override
    public Boolean checkDelPerson(String personIds) {
        return true;
    }

    @Override
    public void delAccPerson(String personIds) {
        deleteAccPerson(personIds);
    }

    @Override
    public Boolean leaveAccPerson(String personIds) {
        deleteAccPerson(personIds);
        return true;
    }

    @Override
    public Boolean checkForcePwd(String personPwd) {
        return accDoorDao.countByForcePwd(personPwd) > 0;
    }

    @Override
    public void batchDeptChange(List<String> personIds, String deptId) {
        if (StringUtils.isNotBlank(deptId) && !personIds.isEmpty()) {
            // 根据人员id获取需要删除的权限组ids。
            List<String> delLevelIds = accLevelPersonDao.getLevelIdByPersonIdIn(personIds);
            // SET集合去重
            Set<String> delLevelIdSet = Sets.newHashSet(delLevelIds);
            // 根据调整的部门获取需要新增的权限组ids。
            List<String> addLevelIds = accLevelDeptDao.getLevelIdsByDeptId(deptId);
            if (!delLevelIdSet.isEmpty()) {
                accLevelService.immeDelPersonLevel(Lists.newArrayList(delLevelIdSet), StringUtils.join(personIds, ","));
            }
            if (addLevelIds != null && !addLevelIds.isEmpty()) {
                accLevelService.addPersonLevel(StringUtils.join(addLevelIds, ","), StringUtils.join(personIds, ","));
            }
        }
    }

    private void deleteAccPerson(String personIds) {
        if (StringUtils.isNotBlank(personIds)) {
            List<String> personList = (List<String>)CollectionUtil.strToList(personIds);
            Map<String, String> personOptMap = new HashMap<String, String>();
            personOptMap.put("id", personIds);
            accLevelService.immeDelPersonLevel(personOptMap);
            // 多人开门组删除人员
            accPersonCombOpenPersonDao.deleteAccPersonCombOpenPersonByPersPersonIdIn(personList);
            // 验证方式规则人员组删除人员
            accPersonVerifyModeRuleDao.deleteAccPersonVerifyModeRuleByPersPersonIdIn(personList);
            if (accProExtService != null) {
                accProExtService.deleteAccPersonVerifyModeRuleByPersPersonIdIn(personList);
            }
            // 查看区域内人员删除人员
            if (acc4AccZonePersonService != null) {
                acc4AccZonePersonService.deleteAccZonePersonByPersPersonIdIn(personList);
            }
            // 全局反潜删除人员
            if (acc4AccGlobalApbService != null) {
                acc4AccGlobalApbService.deleteAccPersonGlobalApbByPersPersonIdIn(personList);
            }
            // 全局联动删除人员
            if (acc4AccGlobalLinkageService != null) {
                acc4AccGlobalLinkageService.deleteAccGlobalLinkagePersonByPersPersonIdIn(personList);
            }
            // 人员有效性删除人员
            if (acc4AccPersonLimitService != null) {
                acc4AccPersonLimitService.deleteAccPersonPersonLimitByPersPersonIdIn(personList);
            }
            accPersonDao.deleteAccPersonByPersonIdIn(personList);
        }
    }

    private void delPersonLevelFromDev(List<String> levelIds, String pin) {
        if (levelIds != null && levelIds.size() > 0) {
            List<String> pinList = new ArrayList<>();
            pinList.add(pin);
            List<AccDevice> devList = accDeviceDao.getDevByLevel(levelIds);// 获取设备Set
            // 删除人员权限命令
            for (AccDevice dev : devList) {
                // 全删全下权限
                accDevCmdManager.delPersonLevelFromDev(dev.getSn(), pinList, false);// 删除权限命令,
                accDevCmdManager.delPersonBioTemplateFromDev(dev.getSn(), pinList, false);// 删除指纹
                accDevCmdManager.delPersonFromDev(dev.getSn(), pinList, false);// 删除人员信息
            }
        }
    }

    /**
     * 重新下发人员信息
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinjie.you</a>
     * @date 2018/9/7 10:15
     * @param id
     * @return void
     */
    @Override
    public void reIssuePersonInfo(String id) {
        AccPersonInfoItem accPersonInfoItem =
            accLevelService.getPersonByIds((List<String>)CollectionUtil.strToList(id));
        List<AccDevice> accDeviceList = accDoorDao.getDevByPersonLevel((List<String>)CollectionUtil.strToList(id));
        if (accDeviceList != null && accDeviceList.size() > 0) {
            // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了
            AccDeviceUtil.moveUpParentDevList(accDeviceList);
            accDeviceList.forEach(accDevice -> {
                accLevelService.setPersonLevelToDevice(accDevice.getId(), accPersonInfoItem, false);
            });
        }
    }

    @Override
    public Map<String, String> getAccPersonExtParam(String personId) {
        Map<String, String> extParams = new HashMap<>();
        AccPerson accPerson = accPersonDao.findByPersonId(personId);
        List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonId(personId);
        if (accPerson != null) {
            extParams.put("acc.delayPassage", String.valueOf(accPerson.getDelayPassage()));
            extParams.put("acc.superAuth", String.valueOf(accPerson.getSuperAuth()));
            extParams.put("acc.disabled", String.valueOf(accPerson.getDisabled()));
            extParams.put("acc.isSetValidTime", String.valueOf(accPerson.getIsSetValidTime()));
            extParams.put("acc.privilege", String.valueOf(accPerson.getPrivilege()));
            if (accPerson.getStartTime() != null && accPerson.getEndTime() != null) {
                extParams.put("acc.isSetValidTime", "true");
                extParams.put("acc.startTime",
                    DateUtil.dateToString(accPerson.getStartTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                extParams.put("acc.endTime",
                    DateUtil.dateToString(accPerson.getEndTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
            } else {
                extParams.put("acc.isSetValidTime", "false");
            }
        }
        if (!CollectionUtil.isEmpty(levelIdList)) {
            extParams.put("acc.personLevelIds", StringUtils.join(levelIdList, ","));
        }
        return extParams;
    }

    @Override
    public List<Acc4PersonLevelItem> getPersonLevelByPersonId(String personId, String token) {
        List<Acc4PersonLevelItem> acc4PersonLevelItemList = Lists.newArrayList();
        Acc4PersonLevelItem acc4PersonLevelItem = null;
        token = EncrypAESUtil.decryptToString(token);
        String username = token.split("_")[0];
        String areaIds = "";
        AuthUserItem authUserItem = authUserService.getItemByUsername(username);
        if (authUserItem != null && !authUserItem.getIsSuperuser()) {
            // 非超级管理员根据权限过滤
            areaIds = authUserItem.getAreaIds();
        }
        if (StringUtils.isNotBlank(personId)) {
            // 获取人员拥有所有权限组信息
            List<AccLevelItem> accLevelItemList = accLevelService.getLevelByPersonId(personId);
            String levelIds = "";
            if (!accLevelItemList.isEmpty()) {
                // 设置人员已拥有的权限组select为true
                for (AccLevelItem accLevelItem : accLevelItemList) {
                    acc4PersonLevelItem = new Acc4PersonLevelItem();
                    acc4PersonLevelItem.setLevelId(accLevelItem.getId());
                    acc4PersonLevelItem.setLevelName(accLevelItem.getName());
                    acc4PersonLevelItem.setPersonId(personId);
                    acc4PersonLevelItem.setSelected(true);
                    acc4PersonLevelItemList.add(acc4PersonLevelItem);
                    levelIds += accLevelItem.getId() + ",";
                }
            }
            AccLevelItem accLevelItem = new AccLevelItem();
            if (StringUtils.isNotBlank(levelIds)) {
                // 设置需要过滤的权限组ID
                levelIds = levelIds.substring(0, levelIds.length() - 1);
                accLevelItem.setNotInId(levelIds);
                if (StringUtils.isNotBlank(areaIds)) {
                    accLevelItem.setAreaIds(areaIds);
                }
            }
            List<AccLevelItem> allLevelList = accLevelService.getByCondition(accLevelItem);

            if (!allLevelList.isEmpty()) {
                // 设置人员未拥有的权限组select为false
                for (AccLevelItem levelItem : allLevelList) {
                    acc4PersonLevelItem = new Acc4PersonLevelItem();
                    acc4PersonLevelItem.setLevelId(levelItem.getId());
                    acc4PersonLevelItem.setLevelName(levelItem.getName());
                    acc4PersonLevelItem.setPersonId(personId);
                    acc4PersonLevelItem.setSelected(false);
                    acc4PersonLevelItemList.add(acc4PersonLevelItem);
                }
            }
        } else {
            // 人员id不存在，根据权限过滤权限组信息
            AccLevelItem accLevelItem = new AccLevelItem();
            if (StringUtils.isNotBlank(areaIds)) {
                accLevelItem.setAreaIds(areaIds);
            }
            List<AccLevelItem> allLevelList = accLevelService.getByCondition(accLevelItem);
            if (!allLevelList.isEmpty()) {
                for (AccLevelItem levelItem : allLevelList) {
                    acc4PersonLevelItem = new Acc4PersonLevelItem();
                    acc4PersonLevelItem.setLevelId(levelItem.getId());
                    acc4PersonLevelItem.setLevelName(levelItem.getName());
                    acc4PersonLevelItem.setPersonId(personId);
                    if (levelItem.getInitFlag() != null && levelItem.getInitFlag()) {
                        // 设置初始化权限组选中
                        acc4PersonLevelItem.setSelected(true);
                    } else {
                        acc4PersonLevelItem.setSelected(false);
                    }
                    acc4PersonLevelItemList.add(acc4PersonLevelItem);
                }
            }
        }
        return acc4PersonLevelItemList;
    }
}
