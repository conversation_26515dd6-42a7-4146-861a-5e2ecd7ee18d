package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.acc.service.AccRTMonitorService;
import com.zkteco.zkbiosecurity.acc.utils.VmsGetAccServiceUtil;
import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.vms.service.VmsGetAccDoorService;
import com.zkteco.zkbiosecurity.vms.vo.VmsSelectDoorItem;

/**
 * <AUTHOR>
 * @date Created In 17:38 2020/4/17
 */
@Service
public class VmsGetAccDoorServiceImpl implements VmsGetAccDoorService {

    @Autowired
    AccRTMonitorService accRTMonitorService;

    @Autowired
    AccDoorService accDoorService;

    @Autowired
    AccDeviceOptionService accDeviceOptionService;

    @Autowired
    AccDeviceService accDeviceService;

    @Override
    public ZKResultMsg operate(String type, int opInterval, String doorId) {
        Map<String, String> resMap = accRTMonitorService.operateDoor(type, opInterval + "", doorId);
        String res = VmsGetAccServiceUtil.dealResMap(resMap);
        if (StringUtils.equals("ok", res)) {
            AccDoorItem item = accDoorService.getItemById(doorId);
            res = item == null ? "" : item.getName();
            ZKResultMsg zkResultMsg = ZKResultMsg.successMsg();
            zkResultMsg.setMsg(res);
            return zkResultMsg;
        }
        return ZKResultMsg.failMsg(res);
    }

    @Override
    public List<VmsSelectDoorItem> getItemsById(List<String> doorIds) {
        List<AccDoorItem> outList = accDoorService.getItemsByIds(doorIds);
        List<VmsSelectDoorItem> selectItem =
            outList.stream().map(this::createVmsSelectDoorItem).collect(Collectors.toList());
        return selectItem;
    }

    @Override
    public Pager getItemByAuthFilter(String sessionId, VmsSelectDoorItem condition, int pageNo, int size) {
        String areaIds = accDeviceService.getAreaIdsByAuthFilter(sessionId);
        AccDoorItem door = createFilterDoor(condition);
        door.setAreaIdIn(areaIds);
        Pager doorPage = accDoorService.getItemsByPage(door, pageNo, size);
        List<AccDoorItem> doorList = (List<AccDoorItem>)doorPage.getData();
        List selectItem = doorList.stream().map(this::createVmsSelectDoorItem).collect(Collectors.toList());
        doorPage.setData(selectItem);
        return doorPage;
    }

    @Override
    public List<VmsSelectDoorItem> getNotSupportLockdownDoors(List<String> doorIds) {
        List<VmsSelectDoorItem> noSupportList = new ArrayList<>(doorIds.size());
        for (String doorId : doorIds) {
            AccDoorItem door = accDoorService.getItemById(doorId);
            if (door != null) {
                boolean isSupport = accDeviceOptionService.getAccSupportFunListVal(door.getDeviceId(), 2);
                if (!isSupport) {
                    VmsSelectDoorItem selectDoor = createVmsSelectDoorItem(door);
                    noSupportList.add(selectDoor);
                }
            }
        }
        return noSupportList;
    }

    private VmsSelectDoorItem createVmsSelectDoorItem(AccDoorItem accDoorItem) {
        VmsSelectDoorItem selectDoorItem = new VmsSelectDoorItem();
        ModelUtil.copyProperties(accDoorItem, selectDoorItem);
        selectDoorItem.setDoorName(accDoorItem.getName());
        return selectDoorItem;
    }

    private AccDoorItem createFilterDoor(VmsSelectDoorItem condition) {
        AccDoorItem doorItem = new AccDoorItem();
        doorItem.setNotId(condition.getNotInId());
        doorItem.setAreaIdIn(condition.getAuthAreaIdIn());
        doorItem.setName(condition.getDoorName());
        ModelUtil.copyProperties(condition, doorItem);
        return doorItem;
    }
}
