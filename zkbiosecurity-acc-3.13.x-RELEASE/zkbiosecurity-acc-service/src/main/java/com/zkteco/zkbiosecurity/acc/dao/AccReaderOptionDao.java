/**
 * File Name: AccReaderOption Created by GenerationTools on 2018-03-13 上午10:06 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccReaderOption;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import java.util.List;

/**
 * 对应百傲瑞达 AccReaderOptionDao
 * 
 * <AUTHOR>
 * @date: 2018-03-13 上午10:06
 * @version v1.0
 */
public interface AccReaderOptionDao extends BaseDao<AccReaderOption, String> {
    AccReaderOption findByAccReader_IdAndName(String readerId, String name);

    /**
     * 查找读头相应类型的参数
     *
     * @param readerId
     * @param type
     * @return
     */
    List<AccReaderOption> findByAccReader_IdAndType(String readerId, Short type);

    /**
     * 根据读头id查询读头参数信息
     *
     * @param readerId:读头id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.model.AccReaderOption>
     * <AUTHOR>
     * @date 2021-01-28 18:34
     * @since 1.0.0
     */
    List<AccReaderOption> findByAccReader_Id(String readerId);
}