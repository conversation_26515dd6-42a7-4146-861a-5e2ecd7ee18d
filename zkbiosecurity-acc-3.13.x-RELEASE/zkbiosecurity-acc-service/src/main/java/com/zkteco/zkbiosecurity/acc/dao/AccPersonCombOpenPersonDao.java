package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccPersonCombOpenPerson;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;

public interface AccPersonCombOpenPersonDao extends BaseDao<AccPersonCombOpenPerson, String>{

    /**
     * 查询出多人开门组已经存在的人员ID
     * @author: mingfa.zheng
     * @date: 2018/4/24 15:53
     * @return:
     */
    @Query("select persPersonId from AccPersonCombOpenPerson")
    List<String> getPersonIdByCombPersonId();

    Integer countByAccCombOpenPerson_IdAndPersPersonIdNotIn(String combOpenPersonId, List<String> personIdList);

    @Modifying
    @Query("delete from AccPersonCombOpenPerson where persPersonId in ?1")
    void delPersonNullByPersonIdScope(List<String> personIdList);

    List<AccPersonCombOpenPerson> findByPersPersonIdIn(List<String> personIds);

    AccPersonCombOpenPerson findByPersPersonId(String personId);

    List<AccPersonCombOpenPerson> findByAccCombOpenPerson_Id(String id);

    void deleteAccPersonCombOpenPersonByPersPersonIdIn(List<String> personIdList);

    Long countByAccCombOpenPerson_Id(String combOpenPersonId);

    Long countByAccCombOpenPerson_IdAndPersPersonIdIn(String combOpenPersonId, Collection<String> personIds);
}
