/**
 * File Name: AccDeviceVerifyModeServiceImpl Created by GenerationTools on 2018-03-20 下午04:20 Copyright:Copyright ©
 * 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceVerifyModeDao;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.model.AccDeviceVerifyMode;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceVerifyModeService;
import com.zkteco.zkbiosecurity.acc.utils.AccDeviceUtil;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceOptionItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceVerifyModeItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 对应百傲瑞达 AccDeviceVerifyModeServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-20 下午04:20
 * @version v1.0
 */
@Service
@Transactional
public class AccDeviceVerifyModeServiceImpl implements AccDeviceVerifyModeService {
    @Autowired
    private AccDeviceVerifyModeDao accDeviceVerifyModeDao;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;

    @Override
    public AccDeviceVerifyModeItem saveItem(AccDeviceVerifyModeItem item) {
        AccDeviceVerifyMode accDeviceVerifyMode =
            Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
                .flatMap(id -> accDeviceVerifyModeDao.findById(id)).orElse(new AccDeviceVerifyMode());

        ModelUtil.copyProperties(item, accDeviceVerifyMode);
        accDeviceVerifyModeDao.save(accDeviceVerifyMode);
        item.setId(accDeviceVerifyMode.getId());
        return item;
    }

    @Override
    public List<AccDeviceVerifyModeItem> getByCondition(AccDeviceVerifyModeItem condition) {
        return (List<AccDeviceVerifyModeItem>)accDeviceVerifyModeDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accDeviceVerifyModeDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accDeviceVerifyModeDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AccDeviceVerifyModeItem getItemById(String id) {
        List<AccDeviceVerifyModeItem> items = getByCondition(new AccDeviceVerifyModeItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public String getVerifyMode(String devSn, String verifyModeNo) {
        String ret = "";
        if (StringUtils.isNotBlank(devSn) && StringUtils.isNotBlank(verifyModeNo)) {
            if (AccConstants.VERIFY_MODE.containsKey(Integer.parseInt(verifyModeNo))) {
                ret = AccConstants.VERIFY_MODE.get(Integer.parseInt(verifyModeNo));
            }
        }
        return "".equals(ret) ? AccConstants.VERIFY_MODE_UNDEFINED + verifyModeNo : ret;
    }

    @Override
    public List<Short> getVerifyNoBySN(String sn) {
        return accDeviceVerifyModeDao.getVerifyNoBySN(sn);
    }

    @Override
    public void handlerTransfer(List<AccDeviceVerifyModeItem> modeItems) {
        // 按设备sn分组， key：pin value:
        Map<String, List<AccDeviceVerifyModeItem>> deviceOpMap =
            modeItems.stream().collect(Collectors.groupingBy(AccDeviceVerifyModeItem::getDeviceSn));
        // 获取数据库中原有的设备参数取出，用于比较
        List<List<String>> snsList = CollectionUtil.split(deviceOpMap.keySet(), CollectionUtil.splitSize);
        Map<String, AccDeviceVerifyMode> optionAllMap = new HashMap<String, AccDeviceVerifyMode>();
        Map<String, AccDevice> deviceAllMap = new HashMap<String, AccDevice>();
        // 数据大时分批处理
        for (List<String> sns : snsList) {
            List<AccDeviceVerifyMode> modes = accDeviceVerifyModeDao.findByAccDevice_SnIn(sns);
            for (AccDeviceVerifyMode mode : modes) {
                String key = mode.getAccDevice().getSn() + "_" + mode.getVerifyNo();
                optionAllMap.put(key, mode);
            }
            // 將所有的設備查詢出來
            List<AccDevice> devices = accDeviceDao.findBySnIn(sns);
            for (AccDevice accDevice : devices) {
                deviceAllMap.put(accDevice.getSn(), accDevice);
            }
        }
        // 检测判断数据后保存更新
        for (AccDeviceVerifyModeItem modeItem : modeItems) {
            AccDeviceVerifyMode mode = optionAllMap.remove(modeItem.getDeviceSn() + "_" + modeItem.getVerifyNo());
            if (Objects.isNull(mode)) {
                mode = new AccDeviceVerifyMode();
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(modeItem, mode, "id");
            mode.setAccDevice(deviceAllMap.get(modeItem.getDeviceSn()));
            accDeviceVerifyModeDao.save(mode);
        }
        optionAllMap = null;
        deviceAllMap = null;

    }

    @Override
    public List<AccDeviceVerifyModeItem> getVerifyModeByDeviceId(String deviceId) {
        if (StringUtils.isNotBlank(deviceId)) {
            List<AccDeviceVerifyMode> verifyModeList =
                accDeviceVerifyModeDao.findByAccDevice_IdOrderByVerifyNoAsc(deviceId);
            return ModelUtil.copyListProperties(verifyModeList, AccDeviceVerifyModeItem.class);
        }
        return null;
    }

    @Override
    public String getVerifyModeNosByNewVFStyles(String verifyMode) {
        String verifyModeNos = "";
        if (StringUtils.isNotBlank(verifyMode)) {
            char[] verifyModeBinary = AccDeviceUtil.decimalToBinary(verifyMode, 16);
            // 新验证方式支持位对应的编号
            StringBuilder newVerifyModeNo = new StringBuilder();
            for (int i = 0; i < verifyModeBinary.length; i++) {
                // 包含且支持新验证方式
                if (AccConstants.NEW_VERIFY_MODE.containsKey(i)
                    && AccConstants.ENABLE == Integer.parseInt(String.valueOf(verifyModeBinary[i]))) {
                    newVerifyModeNo.append(i).append(",");
                }
                if (StringUtils.isNotBlank(newVerifyModeNo)) {
                    verifyModeNos = newVerifyModeNo.substring(0, newVerifyModeNo.length() - 1);
                }
            }
        }
        return verifyModeNos;
    }

    @Override
    public String getVerityModeNamesByNewVFStyles(String newVFStyles) {
        String verifyModeNames = "";
        if (StringUtils.isNotBlank(newVFStyles)) {
            char[] newBinaryVfStyles = AccDeviceUtil.decimalToBinary(newVFStyles, 16);
            // 新验证方式支持位对应的编号
            StringBuilder newVerifyModeName = new StringBuilder();
            for (int i = 0; i < newBinaryVfStyles.length; i++) {
                // 包含且支持新验证方式
                if (AccConstants.NEW_VERIFY_MODE.containsKey(i)
                    && AccConstants.ENABLE == Integer.parseInt(String.valueOf(newBinaryVfStyles[i]))) {
                    newVerifyModeName.append(AccConstants.NEW_VERIFY_MODE.get(i)).append(",");
                }
            }
            verifyModeNames = StringUtils.isBlank(newVerifyModeName) ? "common_verifyMode_other"
                : newVerifyModeName.substring(0, newVerifyModeName.length() - 1);
        }
        return verifyModeNames;
    }

    @Override
    public List<AccDeviceVerifyModeItem> getSupportNewVerifyModeTree() {
        // 支持新验证方式规则参数
        List<AccDeviceOptionItem> supportNewVFStylesOptItems = accDeviceOptionService.getItemsByOptName("NewVFStyles");
        if (Objects.nonNull(supportNewVFStylesOptItems) && !supportNewVFStylesOptItems.isEmpty()) {
            List<String> sns = (List<String>)CollectionUtil.getPropertyList(supportNewVFStylesOptItems,
                AccDeviceOptionItem::getDeviceSn, "-1");
            List<AccDeviceOptionItem> accDeviceOptionItemList =
                accDeviceOptionService.getOptionItemBySnsAndOptName(sns, "AccSupportFunList");
            // 支持不同验证方式设置的设备id
            List<String> devIdSupportVS = new ArrayList<>();
            for (AccDeviceOptionItem option : accDeviceOptionItemList) {
                String val = option.getValue();
                // 支持验证方式规则设置功能
                if (accDeviceOptionService.getAccSupportFunListVal(12, val)) {
                    devIdSupportVS.add(option.getDeviceId());
                }
            }
            Set<Object[]> devVerifyModeList = new HashSet<>();
            if (devIdSupportVS.size() > 0) {
                devVerifyModeList = accDeviceVerifyModeDao.getCommonVerifyModeByDevId(devIdSupportVS);
            }
            List<AccDeviceVerifyModeItem> accDeviceVerifyModeItemList = Lists.newArrayList();
            for (Object[] verifyMode : devVerifyModeList) {
                AccDeviceVerifyModeItem item = new AccDeviceVerifyModeItem();
                item.setVerifyNo(Short.valueOf(verifyMode[0] + ""));
                item.setName(I18nUtil.i18nCode(verifyMode[1] + ""));
                accDeviceVerifyModeItemList.add(item);
            }
            return accDeviceVerifyModeItemList;
        }
        return null;
    }

    @Override
    public void saveAccDeviceVerifyModeList(List<AccDeviceVerifyModeItem> verifyModeList) {
        // 组装设备map信息
        List<String> devIdList =
            (List<String>)CollectionUtil.getPropertyList(verifyModeList, AccDeviceVerifyModeItem::getDeviceId, "-1");
        List<AccDevice> accDeviceList = accDeviceDao.findByIdList(devIdList);
        Map<String, AccDevice> deviceMap = CollectionUtil.listToKeyMap(accDeviceList, AccDevice::getId);

        List<AccDeviceVerifyMode> accDeviceVerifyModeList = new ArrayList<>();
        for (AccDeviceVerifyModeItem item : verifyModeList) {
            AccDeviceVerifyMode event = ModelUtil.copyProperties(item, new AccDeviceVerifyMode());
            AccDevice accDevice = deviceMap.get(item.getDeviceId());
            if (Objects.nonNull(accDevice)) {
                event.setAccDevice(accDevice);
            }
            accDeviceVerifyModeList.add(event);
        }
        if (!accDeviceVerifyModeList.isEmpty()) {
            accDeviceVerifyModeDao.save(accDeviceVerifyModeList);
        }
    }

    @Override
    public Set<Object[]> getCommonVerifyModeByDevId(List<String> devIds) {
        Set<Object[]> devVerifyModeList = new HashSet<>();
        if (Objects.nonNull(devIds) && !devIds.isEmpty()) {
            devVerifyModeList = accDeviceVerifyModeDao.getCommonVerifyModeByDevId(devIds);
        }
        return devVerifyModeList;
    }

    @Override
    public String getVerifyModeNameByDevSnAndVerifyModeNo(String devSn, String verifyModeNo) {
        String verifyModeName = "";
        Map<String, String> devOptMap = accDeviceOptionService.getDevOptionBySn(devSn);
        // 支持新验证方式参数
        if (StringUtils.isNotBlank(devOptMap.get("NewVFStyles")) && !"0".equals(devOptMap.get("NewVFStyles"))
                && !AccConstants.VERIFY_MODE_OTHERS.toString().equals(verifyModeNo)) {
            verifyModeName = getVerityModeNamesByNewVFStyles(verifyModeNo);
        } else {
            verifyModeName = getVerifyMode(devSn, verifyModeNo);
        }
        return verifyModeName;
    }
}