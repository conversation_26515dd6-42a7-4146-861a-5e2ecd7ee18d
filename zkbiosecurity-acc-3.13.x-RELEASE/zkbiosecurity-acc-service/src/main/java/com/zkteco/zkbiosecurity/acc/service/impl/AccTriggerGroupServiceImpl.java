package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.dao.AccTriggerGroupAddrDao;
import com.zkteco.zkbiosecurity.acc.dao.AccTriggerGroupDao;
import com.zkteco.zkbiosecurity.acc.model.AccTriggerGroup;
import com.zkteco.zkbiosecurity.acc.model.AccTriggerGroupAddr;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccTriggerGroupService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTriggerGroupItem;
import com.zkteco.zkbiosecurity.adms.service.AdmsCacheService;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;

/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2024/3/27 18:24
 * @since 1.0.0
 */
@Service
@Transactional
public class AccTriggerGroupServiceImpl implements AccTriggerGroupService {
    @Autowired
    private AccTriggerGroupDao accTriggerGroupDao;
    @Autowired
    private AccTriggerGroupAddrDao accTriggerGroupAddrDao;
    @Autowired(required = false)
    private AdmsCacheService admsCacheService;
    @Autowired
    private AccDeviceService accDeviceService;

    @Override
    public String updateTriggerGroup(String address, String oldGroupId, Short type) {
        AccTriggerGroup group = null;
        // 1. 维护旧的触发点
        if (StringUtils.isNotBlank(oldGroupId)) {
            Optional<AccTriggerGroup> groupOptional = accTriggerGroupDao.findById(oldGroupId);
            if (groupOptional.isPresent()) {
                group = groupOptional.get();
                group.setType(type);
                group = accTriggerGroupDao.save(group);
            }
        }
        // 2. 维护触发点组中的触发点
        if (StringUtils.isNotBlank(address)) {
            // 2. 分割id
            String[] groupId = splitId(address);
            // 3. 新增触发点
            if (group == null) {
                Long bid = createGroupBID();
                group = createAndSaveGroup(bid, type);
            }
            // 4. 构建触发点对象
            final AccTriggerGroup groupFinal = group;
            Set<AccTriggerGroupAddr> addrSet = Arrays.stream(groupId).filter(StringUtils::isNotBlank)
                .map(id -> new AccTriggerGroupAddr().setAccTriggerGroup(groupFinal).setAddress(id))
                .collect(Collectors.toSet());
            // 5. 将触发点保存到组
            return updateGroupAddr(group, addrSet);
        }
        // 6. 删除之前的触发点
        /*if (group != null) {
            final AccTriggerGroupItem item = getItem(group);
            accTriggerGroupDao.delete(group.getId());
            setDelTriggerGroupToDev(item, oldTargetSnList, isP2P);
        }*/
        return null;
    }

    /**
     * 将id分割成数组
     *
     * @param ids
     * @return
     */
    private String[] splitId(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            return StringUtils.split(ids, ",");
        }
        return new String[0];
    }

    /**
     * 获取新的递增业务id
     *
     * @return
     */
    private Long createGroupBID() {
        Long maxBId = accTriggerGroupDao.findMaxBussinessId();
        if (maxBId == null) {
            return 1L;
        }
        return maxBId + 1L;
    }

    /**
     * 创建并保存新的触发点组
     *
     * @param bid 业务id
     * @param type 触发点组类型
     * @return
     */
    private AccTriggerGroup createAndSaveGroup(Long bid, Short type) {
        AccTriggerGroup group = new AccTriggerGroup();
        group.setBussinessId(bid);
        group.setType(type);
        return accTriggerGroupDao.save(group);
    }

    /**
     * 更新触发点组中的触发点
     *
     * @param group
     * @param addrSet
     * @return
     */
    private String updateGroupAddr(AccTriggerGroup group, Set<AccTriggerGroupAddr> addrSet) {
        Set<AccTriggerGroupAddr> oldSet = group.getAddrSet();
        // 没有变化直接返回
        if (setEquals(addrSet, oldSet)) {
            return group.getId();
        }
        // 如果有改动，就删除之前的
        accTriggerGroupAddrDao.delete(oldSet);
        // 编辑和新增都要保存
        List<AccTriggerGroupAddr> accTriggerGroupAddrs = accTriggerGroupAddrDao.saveAll(addrSet);
        if (accTriggerGroupAddrs != null && !accTriggerGroupAddrs.isEmpty()) {
            group.setAddrSet(new HashSet<>(accTriggerGroupAddrs));
            group = accTriggerGroupDao.save(group);
        }
        return group.getId();
    }

    /**
     * 对比两个触发点组中的触发点地址是否一样
     *
     * @param addrSet
     * @param oldSet
     * @return
     */
    private boolean setEquals(Set<AccTriggerGroupAddr> addrSet, Set<AccTriggerGroupAddr> oldSet) {
        if (addrSet.size() == oldSet.size()) {
            Set<String> newGroup = addrSet.stream().map(AccTriggerGroupAddr::getAddress).collect(Collectors.toSet());
            Set<String> oldGroup = oldSet.stream().map(AccTriggerGroupAddr::getAddress).collect(Collectors.toSet());
            return newGroup.containsAll(oldGroup);
        }
        return false;
    }

    private AccTriggerGroupItem getItem(AccTriggerGroup triggerGroup) {
        final AccTriggerGroupItem item = new AccTriggerGroupItem();
        item.setId(triggerGroup.getId());
        item.setBussinessId(triggerGroup.getBussinessId());
        item.setType(triggerGroup.getType());
        final List<String> addrs =
            triggerGroup.getAddrSet().stream().map(AccTriggerGroupAddr::getAddress).collect(Collectors.toList());
        item.setAddrs(StringUtils.join(addrs, ","));
        return item;
    }

    @Override
    public List<AccDeviceItem> setDelTriggerGroupToDev(AccTriggerGroupItem accTriggerGroup, List<String> targetSnList,
        Boolean... isP2P) {
        List<AccDeviceItem> deviceItems = new ArrayList<>();
        if (accTriggerGroup != null && !CollectionUtil.isEmpty(targetSnList)) {
            List<AccTriggerGroupItem> items = new ArrayList<>();
            AccTriggerGroupItem item = new AccTriggerGroupItem();
            item.setId(accTriggerGroup.getId());
            item.setBussinessId(accTriggerGroup.getBussinessId());
            items.add(item);
            String addrs = accTriggerGroup.getAddrs();
            Set<String> p2pSet = new HashSet<>();
            if (StringUtils.isNotBlank(addrs)) {
                final String[] addrArr = StringUtils.split(addrs, ",");
                if (addrArr != null && addrArr.length > 0) {
                    if (isP2P.length == 1) {
                        for (String sn : targetSnList) {
                            JSONObject deviceInfo = JSONObject.parseObject(admsCacheService.getDeviceInfo(sn));
                            if (deviceInfo != null) {
                                sn = StringUtils.isNotBlank(deviceInfo.getString("parentSN"))
                                    ? deviceInfo.getString("parentSN") : sn;
                            }
                            if (!p2pSet.contains(sn)) {
                                // accDevCmdManager.delTriggerGroupFromDev(sn, Arrays.asList(sn), items, false);
                                p2pSet.add(sn);
                            }
                        }
                    } /*else {
                        accDevCmdManager.delTriggerGroupFromDev(targetSnList.get(0), targetSnList, items, false);
                      }*/
                }
            }
        }
        return deviceItems;
    }

    @Override
    public AccTriggerGroupItem getItemById(String id) {
        if (StringUtils.isNotBlank(id)) {
            final Optional<AccTriggerGroup> groupOptional = accTriggerGroupDao.findById(id);
            if (groupOptional.isPresent()) {
                final AccTriggerGroup accTriggerGroup = groupOptional.get();
                return getItem(accTriggerGroup);
            }
        }
        return null;
    }

    @Override
    public List<AccDeviceItem> getDevByTriggerGroupAddrAndType(List<String> addrIds, Short type) {
        if (addrIds != null && addrIds.size() > 0) {
            if (AccTriggerGroupItem.TRIGGER_TYPE_DOOR.equals(type)) {
                return accDeviceService.getDevItemByDoorIds(addrIds);
            } else if (AccTriggerGroupItem.TRIGGER_TYPE_READER.equals(type)) {
                return accDeviceService.getDevItemByReaderIds(addrIds);
            }
        }
        return new ArrayList<>(0);
    }

}
