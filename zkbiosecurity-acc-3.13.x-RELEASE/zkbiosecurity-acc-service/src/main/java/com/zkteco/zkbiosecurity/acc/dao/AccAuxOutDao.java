/**
 * File Name: AccAuxOut Created by GenerationTools on 2018-03-14 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.acc.model.AccAuxOut;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 门禁辅助输出dao
 */
public interface AccAuxOutDao extends BaseDao<AccAuxOut, String> {

    @Query("select e.id, e.name from AccAuxOut e where e.accDevice.id=?1 order by e.id")
    List<Object[]> getAuxOutInfoByDevId(String deviceId);

    AccAuxOut findByNameAndAccDevice_Id(String name, String devId);

    List<AccAuxOut> findByIdIn(List<String> idList);

    AccAuxOut findByAuxNoAndAccDevice_Id(short eventAddr, String devId);

    int countByTimeSegId(String timeSegId);

    List<AccAuxOut> findByAccDevice_IdIn(List<String> devIdList);

    /**
     * 根据sns集合获取辅助输出集合
     * 
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午10:52:12
     * @param sns
     * @return
     */
    List<AccAuxOut> findByAccDevice_SnIn(Collection<String> sns);

    List<AccAuxOut> findByExtDevIdOrderByAuxNoAsc(String id);

    @Query(value = "select e.auxNo from AccAuxOut e where e.accDevice.id = ?1 order by e.auxNo")
    List<Short> findAuxOutNoByDevId(String devId);
}