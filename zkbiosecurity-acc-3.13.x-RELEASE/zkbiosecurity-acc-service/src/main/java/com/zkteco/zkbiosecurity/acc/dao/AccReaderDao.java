/**
 * File Name: AccReader Created by GenerationTools on 2018-03-13 上午10:06 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.model.AccReader;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 对应百傲瑞达 AccReaderDao
 * 
 * <AUTHOR>
 * @date: 2018-03-13 上午10:06
 * @version v1.0
 */
public interface AccReaderDao extends BaseDao<AccReader, String> {

    List<AccReader> findByAccDoor_Device_Id(String deviceId);

    AccReader findByName(String name);

    /**
     * 获取含有485通讯方式的读头的门
     *
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2016年10月31日 下午5:50:53
     * @return
     */
    @Query(value = "SELECT accDoor.id FROM AccReader WHERE commType in (0,1,4)")
    List<String> getDoorIdWith485Reader();

    @Query(value = "select e from AccReader e where e.readerNo = ?1 and e.accDoor.device.id = ?2")
    AccReader getByReaderNoAndDevId(short readerNo, String devId);

    @Query(
        value = "select e from AccReader e join e.accDoor door join door.device device where e.readerState=?1 and door.doorNo=?2 and device.id=?3")
    AccReader getReaderByStateAndDoorNoAndDevId(short state, short eventAddr, String devId);

    AccReader findByReaderStateAndAccDoor_Id(short readerState, String doorId);

    List<AccReader> findByAccDoor_Id(String doorId);

    @Query(value = "SELECT e from AccReader e where e.accDoor.device.id = ?1 and e.id <> ?2")
    List<AccReader> getOtherReaderByDevId(String devId, String readerId);

    AccReader findByIpAndCommType(String ip, short commType);

    @Query(
        value = "SELECT dev.id from ACC_DEVICE dev LEFT JOIN ACC_DOOR ad on ad.DEV_ID = dev.id LEFT JOIN ACC_READER ar on ar.door_id = ad.id where ar.id = ?1",
        nativeQuery = true)
    String getDevIdByReaderId(String readerId);

    List<AccReader> findByCommTypeAndCommAddressAndAccDoor_Device_Id(Short commType, Short commAddress,
        String deviceId);

    List<AccReader> findByAccDoor_Device_MachineType(short machineType);

    @Query(
        value = "SELECT e from AccReader e where e.accDoor.device.commType = ?1 and (e.accDoor.device.machineType in (?2) or e.accDoor.device.deviceName = ?3)")
    List<AccReader> getByDevCommTypeAndMachineTypeAndDeviceName(short commHttp, List<Short> machineType,
        String deviceName);

    @Query(value = "select e.id from AccReader e where e.accDoor.device.commType in (?1)")
    List<String> getReaderIdByDevCommTypeIn(List<Short> pullCommType);

    @Query(value = "select distinct e.accDoor.id from AccReader e where e.id in (?1)")
    List<String> getDoorIdByReaderId(List<String> readerIds);

    @Query(
        value = "select distinct dev.id from AccReader e left join e.accDoor d left join d.device dev where e.id in (?1)")
    List<String> getDevIdByReaderId(List<String> readerIds);

    @Query(value = "select e from AccReader e where e.commType <> ?1 or e.commType is null")
    List<AccReader> findByCommType(short commType);

    List<AccReader> findByAccDoor_Device_snIn(List<String> sns);

    List<AccReader> findByExtDevIdOrderByReaderNoAsc(String id);

    @Query(value = "select e.readerNo from AccReader e where e.accDoor.device.id = ?1 order by e.readerNo")
    List<Short> findReaderNoByDevId(String devId);

    List<AccReader> findByAccDoor_Device_IdOrderByReaderNoAsc(String deviceId);

    /**
     * 通过读头编号和设备id查询读头
     * 
     * @param readerNo
     * @param devId
     * @return
     */
    @Query(
        value = "select e from AccReader e join e.accDoor door join door.device device where e.readerNo=?1 and device.id=?2")
    AccReader getReaderByReaderNoAndDevId(short readerNo, String devId);

    /**
     * 通过设备id统计读头数量
     * 
     * @param deviceId
     * @return
     */
    int countByAccDoor_Device_Id(String deviceId);

    /**
     * 通过读头查询设备
     *
     * @param readerIdList
     * @return
     */
    @Query(value = "select distinct dev from AccReader r join r.accDoor d join d.device dev where r.id in (?1)")
    List<AccDevice> getDeviceByReaderId(List<String> readerIdList);
}