package com.zkteco.zkbiosecurity.acc.utils;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import com.zkteco.zkbiosecurity.acc.vo.AccPersonOptItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonTimeLevelItem;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;

public class AccLevelUtil {

    /**
     * 权限变动，解析数据使用 将 [{"pin": 1, "id": 1, "cardNo":111}] -> {"id": [1,2,3], "pin": [1,2,3]}
     *
     * @modify by: wenxin
     * @since 2014年8月29日 上午11:30:30
     * @param personInfoList
     * @return Map<String, Collection<String>>
     */
    public static Map<String, Collection<String>> setLevelMap(List<AccPersonOptItem> personInfoList) {
        Map<String, Collection<String>> delParamMap = new HashMap<>();
        List<String> idList = new ArrayList<>();
        List<String> pinList = new ArrayList<>();
        for (AccPersonOptItem personInfo : personInfoList) {
            idList.add(personInfo.getId());
            pinList.add(personInfo.getPin());
        }
        delParamMap.put("id", idList);
        delParamMap.put("pin", pinList);
        return delParamMap;
    }

    /**
     * 将时间权限组信息转化为vo对象
     *
     * @param timeLevelIds:
     * @param personId:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccPersonTimeLevelItem>
     * <AUTHOR>
     * @throws
     * @date 2024-04-22 15:24
     * @since 1.0.0
     */
    public static List<AccPersonTimeLevelItem> timeLevelIdsToLevelTimeItems(String timeLevelIds, String personId) {
        List<AccPersonTimeLevelItem> timeLevelItems = new ArrayList<>();
        if (StringUtils.isBlank(timeLevelIds)) {
            return timeLevelItems;
        }
        // 带时间权限组，格式：权限组id1,开始时间,结束时间;权限组id2,开始时间,结束时间;... 即同一个权限组的数据，用逗号分隔，多个权限组间用分号分隔。
        List<String> levelList =
            Arrays.stream(StringUtils.split(timeLevelIds, ";")).distinct().collect(Collectors.toList());
        for (String level : levelList) {
            if (StringUtils.isBlank(level)) {
                continue;
            }
            Object[] obj = Arrays.stream(StringUtils.split(level, ",")).toArray();
            if (StringUtils.isBlank(obj[0].toString())) {
                continue;
            }
            AccPersonTimeLevelItem item = new AccPersonTimeLevelItem();
            item.setLevelId(obj[0].toString());
            if (obj.length > 2) {
                item.setStartTime(obj[1].toString());
                item.setEndTime(obj[2].toString());
            }
            item.setPersonId(personId);
            timeLevelItems.add(item);
        }
        return timeLevelItems;
    }

    /**
     * 将时间人员转化为vo对象
     *
     * <AUTHOR>
     * @date 2023-06-27 16:48
     * @param timePersonIds
     * @param levelId
     * @since 1.0.0
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccPersonTimeLevelItem>
     */
    public static List<AccPersonTimeLevelItem> timePersonIdsToLevelTimeItems(String timePersonIds, String levelId) {
        List<AccPersonTimeLevelItem> personTimeLevelItems = new ArrayList<>();
        if (StringUtils.isBlank(timePersonIds)) {
            return personTimeLevelItems;
        }
        // 带时间人员，格式：人员id1,开始时间,结束时间;人员id2,开始时间,结束时间;... 即同一个人员的数据，用逗号分隔，多个人员间用分号分隔。
        List<String> personTimeList =
            Arrays.stream(StringUtils.split(timePersonIds, ";")).distinct().collect(Collectors.toList());
        for (String personTime : personTimeList) {
            Object[] obj = Arrays.stream(StringUtils.split(personTime, ",")).toArray();
            if (StringUtils.isBlank(obj[0].toString())) {
                continue;
            }
            AccPersonTimeLevelItem item = new AccPersonTimeLevelItem();
            item.setPersonId(obj[0].toString());
            if (obj.length > 2) {
                item.setStartTime(obj[1].toString());
                item.setEndTime(obj[2].toString());
            }
            item.setLevelId(levelId);
            personTimeLevelItems.add(item);
        }
        return personTimeLevelItems;
    }

    /**
     * 将逗号隔开的权限组id字符串转为vo对象
     * 
     * @param accPersonLevelIds:
     * @param personId:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccPersonTimeLevelItem>
     * <AUTHOR>
     * @throws
     * @date 2024-04-22 15:21
     * @since 1.0.0
     */
    public static List<AccPersonTimeLevelItem> levelIdsToLevelTimeItems(String accPersonLevelIds, String personId) {
        List<AccPersonTimeLevelItem> timeLevelItems = new ArrayList<>();
        if (StringUtils.isBlank(accPersonLevelIds)) {
            return timeLevelItems;
        }
        List<String> levelIdList = StrUtil.strToList(accPersonLevelIds);
        for (String levelId : levelIdList) {
            AccPersonTimeLevelItem item = new AccPersonTimeLevelItem();
            item.setLevelId(levelId);
            item.setPersonId(personId);
            timeLevelItems.add(item);
        }
        return timeLevelItems;
    }

    /**
     * 将逗号隔开的人员id字符串转为vo对象
     *
     * @param personIds:
     * @param levelId:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccPersonTimeLevelItem>
     * <AUTHOR>
     * @throws
     * @date 2024-04-22 17:09
     * @since 1.0.0
     */
    public static List<AccPersonTimeLevelItem> personIdsToLevelTimeItems(String personIds, String levelId) {
        List<AccPersonTimeLevelItem> timeLevelItems = new ArrayList<>();
        if (StringUtils.isBlank(personIds)) {
            return timeLevelItems;
        }
        List<String> personIdList = StrUtil.strToList(personIds);
        for (String personId : personIdList) {
            AccPersonTimeLevelItem item = new AccPersonTimeLevelItem();
            item.setLevelId(levelId);
            item.setPersonId(personId);
            timeLevelItems.add(item);
        }
        return timeLevelItems;
    }

    public static List<AccPersonTimeLevelItem> buildAccLevelPersonItem(String accPersonLevelTimeInfos,
        String personId) {
        List<AccPersonTimeLevelItem> accLevelPersonItems = null;
        if (StringUtils.isNotBlank(accPersonLevelTimeInfos)) {
            accLevelPersonItems = new ArrayList<>();
            AccPersonTimeLevelItem accLevelPersonItem = null;
            if (accPersonLevelTimeInfos.contains("levelId")) {
                JSONArray accPersonLevelArray = JSON.parseArray(accPersonLevelTimeInfos);
                for (Object object : accPersonLevelArray) {
                    JSONObject jsonObject = JSON.parseObject(object + "");
                    accLevelPersonItem = new AccPersonTimeLevelItem();
                    accLevelPersonItem.setLevelId(jsonObject.getString("levelId"));
                    // 表示没有传时间
                    String startTime = null;
                    if (jsonObject.containsKey("startTime")) {
                        startTime = jsonObject.getString("startTime");
                        startTime = StringUtils.isNotBlank(startTime) ? startTime : "";
                    }
                    String endTime = null;
                    if (jsonObject.containsKey("endTime")) {
                        endTime = jsonObject.getString("endTime");
                        endTime = StringUtils.isNotBlank(endTime) ? endTime : "";
                    }
                    if ("".equals(startTime) || "".equals(endTime)) {
                        // 只要一个为空，则另一个也为空
                        startTime = "";
                        endTime = "";
                    }
                    // 如果时间值为null，表示没有传值
                    accLevelPersonItem.setStartTime(startTime);
                    accLevelPersonItem.setEndTime(endTime);
                    accLevelPersonItem.setPersonId(personId);
                    accLevelPersonItems.add(accLevelPersonItem);
                }
            } else {
                List<String> levelIdList = StrUtil.strToList(accPersonLevelTimeInfos);
                for (String levelId : levelIdList) {
                    // 表示没有传时间
                    accLevelPersonItem = new AccPersonTimeLevelItem();
                    accLevelPersonItem.setLevelId(levelId);
                    accLevelPersonItem.setStartTime(null);
                    accLevelPersonItem.setEndTime(null);
                    accLevelPersonItem.setPersonId(personId);
                    accLevelPersonItems.add(accLevelPersonItem);
                }
            }
        }
        return accLevelPersonItems;
    }
}
