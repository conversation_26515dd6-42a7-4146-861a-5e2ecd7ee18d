package com.zkteco.zkbiosecurity.acc.task;

import java.util.concurrent.ScheduledFuture;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.service.AccFirstInLastOutService;
import com.zkteco.zkbiosecurity.scheduler.ScheduleService;

@Component
public class AccFirstInLastOutTask {
    @Autowired
    private ScheduleService scheduleService;

    private ScheduledFuture<?> scedulefuture;
    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private AccFirstInLastOutService accFirstInLastOutService;

    public void initSaveFirstInLastOut() {
        if (scedulefuture != null) {
            scedulefuture.cancel(true);
        }
        scedulefuture = scheduleService
            .startScheduleTask(() -> accFirstInLastOutService.saveFirstInLastOutByCacheTime(), "0 0/10 * * * ?");
    }
}
