package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.service.AccTransactionByDoorService;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionDoorItem;
import com.zkteco.zkbiosecurity.auth.provider.AuthSessionProvider;
import com.zkteco.zkbiosecurity.base.bean.Pager;

/**
 * 以门查询业务service实现类
 *
 * <AUTHOR>
 * @DATE 2020-08-07 15:56
 */
@Service
public class AccTransactionByDoorServiceImpl implements AccTransactionByDoorService {
    @Autowired
    private AccTransactionService accTransactionService;
    @Autowired
    private AuthUserService authUserService;

    @Override
    public Pager loadPagerByAuthFilter(String sessionId, AccTransactionDoorItem condition, int pageNo, int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        return accTransactionService.getItemsByPage(condition, pageNo, pageSize);
    }
}
