/*
 * File Name: AccDeviceMonitorServiceImpl <NAME_EMAIL> on 2018/6/7 11:48. Copyright:Copyright ©
 * 1999-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.service.AccBaseDictionaryService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceMonitorService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccMonitorRedirectService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceMonitorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccQueryDeviceItem;
import com.zkteco.zkbiosecurity.adms.bean.DeviceRunState;
import com.zkteco.zkbiosecurity.adms.service.AdmsDeviceService;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
@Service
@Transactional
public class AccDeviceMonitorServiceImpl implements AccDeviceMonitorService {

    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private AccBaseDictionaryService accBaseDictionaryService;
    @Autowired(required = false)
    private AdmsDeviceService admsDeviceService;
    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private AccMonitorRedirectService accMonitorRedirectService;
    @Autowired
    private AccDeviceService accDeviceService;

    @Override
    public List<AccDeviceMonitorItem> getDeviceMonitor(String filterAreaIds) {
        List<AccQueryDeviceItem> accDeviceList = accDeviceService.getQueryItemsByAuth(filterAreaIds);
        List<AccDeviceMonitorItem> accDeviceMonitorItemList = new ArrayList<>();
        if (accDeviceList != null && accDeviceList.size() > 0) {
            Map<String, String> commStatusMap = accBaseDictionaryService.getBaseDictionaryMap("commStatus");
            Map<String, String> opTypeMap = accBaseDictionaryService.getBaseDictionaryMap("opType");
            for (AccQueryDeviceItem dev : accDeviceList) {
                DeviceRunState deviceRunState = admsDeviceService.getDeviceRunState(dev.getSn());
                if (Objects.nonNull(deviceRunState)) {
                    int status = Integer.parseInt(deviceRunState.getActionNo());// 设备状态，包含正常、离线、异常等
                    if (status < 0) {
                        accCacheManager.putDevLastError(dev.getSn(), commStatusMap.containsKey(String.valueOf(status))
                            ? commStatusMap.get(String.valueOf(status)) : "");
                    }
                    String lastError = accCacheManager.getDevLastError(dev.getSn());// 最近异常状态
                    AccDeviceMonitorItem accDeviceMonitorItem = new AccDeviceMonitorItem();
                    accDeviceMonitorItem.setAreaName(dev.getAuthAreaName());
                    accDeviceMonitorItem.setAreaId(dev.getAuthAreaId());
                    accDeviceMonitorItem.setDevName(dev.getAlias());
                    accDeviceMonitorItem.setId(dev.getSn());
                    accDeviceMonitorItem.setDevSn(dev.getSn());
                    accDeviceMonitorItem.setCurState(commStatusMap.containsKey(String.valueOf(status))
                        ? commStatusMap.get(String.valueOf(status)) : "");
                    accDeviceMonitorItem.setOpState(getOpTypeByCmd(deviceRunState.getActionName(), opTypeMap));
                    accDeviceMonitorItem
                        .setDevStatus((dev.getEnabled() ? (status >= 0 ? "normal" : "exception") : "disable"));
                    accDeviceMonitorItem.setCmdCount(deviceRunState.getCmdCount());
                    accDeviceMonitorItem.setLastError(lastError);
                    accDeviceMonitorItemList.add(accDeviceMonitorItem);
                }
            }
        }
        return accDeviceMonitorItemList;
    }

    @Override
    public void updateAccDeviceRunState(String sn) {
        AccQueryDeviceItem dev = accDeviceService.getQueryItemBySn(sn);
        if (dev != null) {
            DeviceRunState deviceRunState = admsDeviceService.getDeviceRunState(dev.getSn());
            if (Objects.nonNull(deviceRunState)) {
                Map<String, String> opTypeMap = accBaseDictionaryService.getBaseDictionaryMap("opType");
                Map<String, String> commStatusMap = accBaseDictionaryService.getBaseDictionaryMap("commStatus");
                int status = Integer.parseInt(deviceRunState.getActionNo());// 设备状态，包含正常、离线、异常等
                String devStatus = "normal";
                if (status < 0) {
                    devStatus = "exception";
                    if (AccConstants.DEV_MONITOR_DISABLE == status) {
                        devStatus = "disable";
                    }
                    accCacheManager.putDevLastError(dev.getSn(), commStatusMap.containsKey(String.valueOf(status))
                        ? commStatusMap.get(String.valueOf(status)) : "");
                }
                String lastError = accCacheManager.getDevLastError(dev.getSn());// 最近异常状态
                AccDeviceMonitorItem accDeviceMonitorItem = new AccDeviceMonitorItem();
                accDeviceMonitorItem.setAreaName(dev.getAuthAreaName());
                accDeviceMonitorItem.setAreaId(dev.getAuthAreaId());
                accDeviceMonitorItem.setDevName(dev.getAlias());
                accDeviceMonitorItem.setId(dev.getSn());
                accDeviceMonitorItem.setDevSn(dev.getSn());
                accDeviceMonitorItem.setCurState(
                    commStatusMap.containsKey(String.valueOf(status)) ? commStatusMap.get(String.valueOf(status)) : "");
                accDeviceMonitorItem.setOpState(getOpTypeByCmd(deviceRunState.getActionName(), opTypeMap));
                accDeviceMonitorItem.setDevStatus(devStatus);
                accDeviceMonitorItem.setCmdCount(deviceRunState.getCmdCount());
                accDeviceMonitorItem.setLastError(lastError);
                accMonitorRedirectService.sendDeviceMonitor(accDeviceMonitorItem);
            }
        }
    }

    @Override
    public List<AccDeviceMonitorItem> filterDeviceState(AccDeviceItem accDeviceItem, int beginIndex, int exportCount,
        String devStatus) {
        List<AccDeviceItem> accDeviceItemList = accDeviceService.getByCondition(accDeviceItem);
        List<AccDeviceMonitorItem> accDeviceMonitorItemList = Lists.newArrayList();
        if (Objects.nonNull(accDeviceItemList) && accDeviceItemList.size() > 0) {
            int index = 0;
            Map<String, String> opTypeMap = accBaseDictionaryService.getBaseDictionaryMap("opType");
            Map<String, String> commStatusMap = accBaseDictionaryService.getBaseDictionaryMap("commStatus");
            for (AccDeviceItem dev : accDeviceItemList) {
                DeviceRunState deviceRunState = admsDeviceService.getDeviceRunState(dev.getSn());
                if (Objects.nonNull(deviceRunState)) {
                    int status = Integer.parseInt(deviceRunState.getActionNo());// 设备状态，包含正常、离线、异常等
                    String devState = (dev.getEnabled() ? (status >= 0 ? "normal" : "exception") : "disable");
                    if ("".equals(devStatus) || (StringUtils.isNotBlank(devStatus) && devStatus.equals(devState))) { // 设备状态为空的和状态不为空且与当前设备状态一致情况下导出
                        if (index < beginIndex) { // 小于等于开始索引跳过，执行下一条记录
                            index++;
                            continue;
                        }
                        if (status < 0) {
                            accCacheManager.putDevLastError(dev.getSn(),
                                commStatusMap.containsKey(String.valueOf(status))
                                    ? commStatusMap.get(String.valueOf(status)) : "");// 设备状态为异常，则保存到redis中
                        }
                        String lastError = accCacheManager.getDevLastError(dev.getSn());// 最近异常状态
                        AccDeviceMonitorItem accDeviceMonitorItem = new AccDeviceMonitorItem();
                        accDeviceMonitorItem.setAreaName(dev.getAuthAreaName());
                        accDeviceMonitorItem.setAreaId(dev.getAuthAreaId());
                        accDeviceMonitorItem.setDevName(dev.getAlias());
                        accDeviceMonitorItem.setId(dev.getSn());
                        accDeviceMonitorItem.setDevSn(dev.getSn());
                        accDeviceMonitorItem.setCurState(commStatusMap.containsKey(String.valueOf(status))
                            ? commStatusMap.get(String.valueOf(status)) : "");
                        accDeviceMonitorItem.setOpState(getOpTypeByCmd(deviceRunState.getActionName(), opTypeMap));
                        accDeviceMonitorItem
                            .setDevStatus((dev.getEnabled() ? (status >= 0 ? "normal" : "exception") : "disable"));
                        accDeviceMonitorItem.setCmdCount(deviceRunState.getCmdCount());
                        accDeviceMonitorItem.setLastError(lastError);
                        accDeviceMonitorItemList.add(accDeviceMonitorItem);
                    }
                }
                if (accDeviceMonitorItemList.size() == exportCount) { // 大于结束索引跳出循环返回
                    break;
                }
            }
        }
        return accDeviceMonitorItemList;
    }

    private String getOpTypeByCmd(String cmdStr, Map<String, String> opTypeMap) {
        String commParam = cmdStr.trim();
        String retStr = "";
        if (commParam.startsWith(ConstUtil.QUEUE_ERROR)) {
            retStr = opTypeMap.get(ConstUtil.QUEUE_ERROR);
        } else if (commParam.startsWith(AccConstants.CMD_RETURN)) {
            retStr = opTypeMap.get(AccConstants.CMD_RETURN);
        } else if (commParam.startsWith(ConstUtil.CONNECT)) {
            retStr = opTypeMap.get(ConstUtil.CONNECT);
        } else if (commParam.startsWith(ConstUtil.GET_RT_LOG)) {
            retStr = opTypeMap.get(ConstUtil.GET_RT_LOG);
        } else if (commParam.startsWith(ConstUtil.DOWN_NEWLOG)) {
            retStr = opTypeMap.get(ConstUtil.DOWN_NEWLOG);
        } else if (commParam.startsWith(ConstUtil.DISCONNECT)) {
            retStr = opTypeMap.get(ConstUtil.DISCONNECT);
        } else if (commParam.startsWith(ConstUtil.DATA_UPDATE)) {
            retStr = opTypeMap.get(ConstUtil.DATA_UPDATE);
        } else if (commParam.startsWith(ConstUtil.DATA_QUERY)) {
            retStr = opTypeMap.get(ConstUtil.DATA_QUERY);
        } else if (commParam.startsWith(ConstUtil.DATA_DELETE)) {
            retStr = opTypeMap.get(ConstUtil.DATA_DELETE);
        } else if (commParam.startsWith(ConstUtil.DATA_COUNT)) {
            retStr = opTypeMap.get(ConstUtil.DATA_COUNT);
        } else if (commParam.startsWith(ConstUtil.DEVICE_GET)) {
            retStr = opTypeMap.get(ConstUtil.DEVICE_GET);
        } else if (commParam.startsWith(ConstUtil.DEVICE_SET)) {
            retStr = opTypeMap.get(ConstUtil.DEVICE_SET);
        } else if (commParam.startsWith(ConstUtil.GET_OPTIONS)) {
            // String[] strs = commParam.split(" ", 2);
            retStr = opTypeMap.get(ConstUtil.GET_OPTIONS);
            // String opt = strs[2];
            // retStr = opTypeMap.get(Integer.parseInt(ConstUtil.DEV_GET_OPTION)) + opt;
        } else if (commParam.startsWith(ConstUtil.SET_OPTIONS)) {
            // String[] strs = commParam.split("=")[0].split(" ", 3);
            // String opt=strs[2];
            retStr = opTypeMap.get(ConstUtil.SET_OPTIONS);
        } else if (commParam.startsWith(ConstUtil.CANCEL_ALARM)) {
            String[] strs = commParam.split(" ");
            String opt = strs[2];
            retStr = opTypeMap.get(ConstUtil.CANCEL_ALARM) + opt;
        } else if (commParam.startsWith(ConstUtil.CHECK_SERVICE)) {
            retStr = opTypeMap.get(ConstUtil.CHECK_SERVICE);
        } else if (commParam.startsWith(ConstUtil.CHECK_DISK)) {
            retStr = opTypeMap.get(ConstUtil.CHECK_DISK);
        } else if (commParam.startsWith(ConstUtil.UPGRADE)) {
            retStr = opTypeMap.get(ConstUtil.UPGRADE);
        } else if (commParam.startsWith(ConstUtil.CONTROL_DEVICE)) {
            retStr = opTypeMap.get(ConstUtil.CONTROL_DEVICE);
        } else if (commParam.startsWith(ConstUtil.DEV_HEARTBEAT)) {
            retStr = opTypeMap.get(ConstUtil.DEV_HEARTBEAT);
        } else {
            retStr = commParam;
        }
        return retStr;
    }

    private String getDevCmdTable(String cmdStr) {
        String retStr = "";
        cmdStr = cmdStr.trim();
        if (cmdStr.startsWith(ConstUtil.USERAUTHORIZE)) {
            retStr = ConstUtil.USERAUTHORIZE;
        } else if (cmdStr.startsWith(ConstUtil.USER)) {
            retStr = ConstUtil.USER;
        } else if (cmdStr.startsWith(ConstUtil.EXTUSER)) {
            retStr = ConstUtil.EXTUSER;
        } else if (cmdStr.startsWith(ConstUtil.HOLIDAY)) {
            retStr = ConstUtil.HOLIDAY;
        } else if (cmdStr.startsWith(ConstUtil.TIMEZONE)) {
            retStr = ConstUtil.TIMEZONE;
        } else if (cmdStr.startsWith(ConstUtil.FIRSTCARD)) {
            retStr = ConstUtil.FIRSTCARD;
        } else if (cmdStr.startsWith(ConstUtil.MULTIMCARD)) {
            retStr = ConstUtil.MULTIMCARD;
        } else if (cmdStr.startsWith(ConstUtil.TRANSACTION)) {
            retStr = ConstUtil.TRANSACTION;
        } else if (cmdStr.startsWith(ConstUtil.INOUTFUN)) {
            retStr = ConstUtil.INOUTFUN;
        } else if (cmdStr.startsWith(ConstUtil.TEMPLATEV10)) {
            retStr = ConstUtil.TEMPLATEV10;
        } else {
            retStr = cmdStr;
        }
        return retStr;
    }
}
