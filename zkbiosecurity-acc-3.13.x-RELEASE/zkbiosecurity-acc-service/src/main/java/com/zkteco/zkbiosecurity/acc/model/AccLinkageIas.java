package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/12/1 15:38
 * @since 1.0.0
 */
@Entity
@Table(name = "ACC_LINKAGE_IAS")
@Getter
@Setter
@Accessors(chain = true)
public class AccLinkageIas extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    @ManyToOne
    @JoinColumn(name = "LINKAGE_ID")
    private AccLinkage accLinkage;

    /** 分区ID */
    @Column(name = "PARTITION_ID", length = 100)
    private String partitionId;

    /** 动作类型 */
    @Column(name = "ACTION_TYPE")
    private Short actionType;

    /** 布防类型 */
    @Column(name = "ARM_TYPE")
    private Short armType;

    /** 厂商 */
    @Column(name = "MANUFACTURE", length = 50)
    private String manufacture;

    public AccLinkageIas() {}
}
