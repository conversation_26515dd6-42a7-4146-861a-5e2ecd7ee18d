package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.model.AccAuxIn;
import com.zkteco.zkbiosecurity.acc.model.AccReader;
import com.zkteco.zkbiosecurity.acc.service.Acc4OtherGetVidChannel2EntityService;
import com.zkteco.zkbiosecurity.vid.service.Vid4OtherGetVidChannel2EntityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/1/29 17:28
 * @since 1.0.0
 */
@Service
public class Acc4OtherGetVidChannel2EntityServiceImpl implements Acc4OtherGetVidChannel2EntityService {

    @Autowired(required = false)
    private Vid4OtherGetVidChannel2EntityService vid4OtherGetVidChannel2EntityService;

    @Override
    public Map<String, String> getAccAuxInBindChannelNames(List<String> auxInIdList) {
        Map<String, String> channelNameMap = new HashMap<>();
        if (Objects.nonNull(vid4OtherGetVidChannel2EntityService)) {
            channelNameMap =
                vid4OtherGetVidChannel2EntityService.getBindChannelNames(auxInIdList, AccAuxIn.class.getSimpleName());
        }
        return channelNameMap;
    }

    @Override
    public Map<String, String> getAccReaderBindChannelNames(List<String> readerIds) {
        Map<String, String> channelNameMap = new HashMap<>();
        if (Objects.nonNull(vid4OtherGetVidChannel2EntityService)) {
            channelNameMap =
                vid4OtherGetVidChannel2EntityService.getBindChannelNames(readerIds, AccReader.class.getSimpleName());
        }
        return channelNameMap;
    }
}
