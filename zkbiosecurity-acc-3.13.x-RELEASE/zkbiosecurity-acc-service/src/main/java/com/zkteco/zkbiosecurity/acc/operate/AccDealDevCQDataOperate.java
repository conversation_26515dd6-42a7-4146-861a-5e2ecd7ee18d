/*
 * File Name: AccDealDevCQDataOperate <NAME_EMAIL> on 2018/6/12 13:53. Copyright:Copyright ©
 * 1999-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.operate;

import java.io.File;
import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zkteco.zkbiosecurity.acc.bean.AccDevCQDataBean;
import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.constants.AccCacheKeyConstants;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonService;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.vo.AccQueryDeviceItem;
import com.zkteco.zkbiosecurity.base.utils.Base64Util;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.ClassUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.FileEncryptUtil;
import com.zkteco.zkbiosecurity.hep.service.Hep4OtherTransactionService;
import com.zkteco.zkbiosecurity.hep.vo.Hep4OtherTransactionItem;
import com.zkteco.zkbiosecurity.push.enums.PushCenter4OtherEnum;
import com.zkteco.zkbiosecurity.push.service.PushCenter4OtherService;
import com.zkteco.zkbiosecurity.push.vo.PushCenter4OtherItem;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
@Component
@Slf4j
public class AccDealDevCQDataOperate {

    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private AccPersonService accPersonService;
    @Autowired
    private AccTransactionService accTransactionService;
    @Value("${system.filePath:BioSecurityFile}")
    private String systemFilePath;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired(required = false)
    private Hep4OtherTransactionService hep4OtherTransactionService;
    @Autowired(required = false)
    private PushCenter4OtherService pushCenter4OtherService;
    @Autowired
    private AccDeviceService accDeviceService;

    @Autowired
    private BaseSysParamService baseSysParamService;

    /**
     * @Description: 处理设备实时上传数据（包括人员、指纹、事件记录、抓拍照片等）
     * <AUTHOR>
     * @date 2018/6/12 13:54
     * @param devCQDataBean
     * @return
     */
    public void dealDataByCData(AccDevCQDataBean devCQDataBean) {
        String type = devCQDataBean.getType();
        String table = devCQDataBean.getTable();
        if (ConstUtil.TABLEDATA.equals(type)) { // 实体表的数据记录
            if (ConstUtil.TEMPLATEV10.equals(table)) {
                // 处理指纹数据
                dealBioTemplate(devCQDataBean, true);
            } else if (ConstUtil.USER.equals(table)) {
                // 处理人员数据
                dealPersonInfo(devCQDataBean, true);
            } else if (ConstUtil.USERPIC.equals(table)) {
                // 处理图片数据
                dealPersonPic(devCQDataBean, true);
            } else if (ConstUtil.FACEV7.equals(table)) {
                // 与处理指纹模板共用一个处理方法
                dealBioTemplate(devCQDataBean, true);
            } else if (AccConstants.FVTEMPLATE.equals(table)) {
                dealBioTemplate(devCQDataBean, true);
            } else if (AccConstants.ATTPHOTO.equals(table)) {
                // 处理一体机上传的照片
                dealAttPhotoInfo(devCQDataBean);
            } else if (AccConstants.IDENTITY_CARD_INFO.equals(table)) {
                // 处理上传的身份证信息表
                dealIdentityCardInfo(devCQDataBean, true);
            } else if (AccConstants.BIOPHOTO.equals(table)) {
                // 处理设备上传的比对照片
                dealBioPhotoInfo(devCQDataBean, true);
            } else if (AccConstants.MUL_CARD_USER.equals(table)) {
                // 处理多卡信息表
                dealMulCardUserInfo(devCQDataBean, true);
            } else if (ConstUtil.EXTUSER.equals(table)) {
                // 处理扩展人员
                dealExtuserInfo(devCQDataBean, true);
            } else if (AccConstants.BIODATA.equals(table)) {
                dealBioTemplate(devCQDataBean, true);
            }
        }
    }

    private void dealWifiListInfo(AccDevCQDataBean wifiListBean, boolean autoUpload) {
        String[] dataArray = wifiListBean.getData().split("\r\n");
        String[] fieldArr = null;
        String[] keyValArr = null;
        Map<String, String> fieldMap = null;
        JSONObject json = new JSONObject();
        JSONArray dataArr = new JSONArray();
        for (String tempRecord : dataArray) {
            fieldMap = Maps.newHashMap();
            fieldArr = StringUtils.split(tempRecord.split("APList ")[1], "\t");
            for (int i = 0, len = fieldArr.length; i < len; i++) {
                keyValArr = fieldArr[i].trim().split("=", 2);
                fieldMap.put(keyValArr[0], keyValArr[1]);
            }
            dataArr.add(fieldMap);
        }
        json.put("sn", wifiListBean.getSn());
        json.put("table", wifiListBean.getTable());
        json.put("data", dataArr);
        accCacheManager.setWifiListInfo(wifiListBean.getCmdId(), json);
    }

    /**
     * @Description: 处理查询设备数据
     * <AUTHOR>
     * @date 2018/7/4 15:11
     * @param devCQDataBean
     * @return
     */
    public void dealDataByQueryData(AccDevCQDataBean devCQDataBean) {
        String table = devCQDataBean.getTable();
        if (ConstUtil.TRANSACTION.equals(table)) {
            dealTransaction(devCQDataBean);// 处理事件记录
        } else if (ConstUtil.USER.equals(table)) {
            dealPersonInfo(devCQDataBean, false);// 处理人员表
        } else if (ConstUtil.TEMPLATEV10.equals(table)) {
            dealBioTemplate(devCQDataBean, false);// 处理指纹
        } else if (ConstUtil.EXTUSER.equals(table)) {
            dealExtuserInfo(devCQDataBean, false);// 处理扩展人员信息
        } else if (AccConstants.FVTEMPLATE.equals(table)) {
            dealBioTemplate(devCQDataBean, false);// 处理指静脉数据
        } else if (ConstUtil.FACEV7.equals(table)) {
            dealBioTemplate(devCQDataBean, false);// 处理面部数据
        } else if (AccConstants.MUL_CARD_USER.equals(table)) {
            dealMulCardUserInfo(devCQDataBean, false);// 处理多卡人员信息
        } else if (AccConstants.IDENTITY_CARD_INFO.equals(table)) {
            dealIdentityCardInfo(devCQDataBean, false);// 处理身份证信息
        } else if ("vmtable".equals(devCQDataBean.getType())) {// 虚拟表使用
            if (AccConstants.WIFI_LIST.equals(table)) {
                // 出来搜索WIFI 列表数据
                dealWifiListInfo(devCQDataBean, true);
            }
        } else if (AccConstants.BIOPHOTO.equals(table)) {
            // 处理设备上传的比对照片
            dealBioPhotoInfo(devCQDataBean, false);
        } else if (AccConstants.BIODATA.equals(table)) {
            // 处理设备上传一体化模板
            dealBioTemplate(devCQDataBean, false);
        }
    }

    /**
     * @Description: 处理服务器查询设备事件信息
     * <AUTHOR>
     * @date 2018/7/4 15:12
     * @param devCQDataBean
     * @return
     */
    private void dealTransaction(AccDevCQDataBean devCQDataBean) {
        JSONObject dataJson = new JSONObject();
        try {
            int count = devCQDataBean.getCount();
            if (count == 0) {
                dataJson.put("count", "0");
                dataJson.put("insert", "0");
                dataJson.put("update", "0");
            } else if (count > 0) {
                // 先把包的信息传给前端
                setPackageInfoToCache(devCQDataBean, dataJson);

                int commType = devCQDataBean.getCommType();
                String data = devCQDataBean.getData();
                Map<String, String> dataMap =
                    accTransactionService.dealQueryTransaction(commType, data, devCQDataBean.getSn());
                dataJson.put("count", count);
                dataJson.put("insert", dataMap.get("insert"));
                dataJson.put("update", dataMap.get("update"));
            } else {
                dataJson.put("count", count);
                dataJson.put("insert", "-1");
                dataJson.put("update", "-1");
            }
            setDataToCache(devCQDataBean, dataJson);
        } catch (Exception e) {
            dataJson.put("exception", "common_dev_devDealException");
            setDataToCache(devCQDataBean, dataJson);
            throw new ZKBusinessException(e);
        }
    }

    private void dealIdentityCardInfo(AccDevCQDataBean devCQDataBean, boolean autoUpload) {
        int count = devCQDataBean.getCount();
        if (count > 0) {
            JSONObject json = new JSONObject();
            String data = devCQDataBean.getData();
            String split = devCQDataBean.getTable();
            String[] dataArray = StringUtils.split(data.trim(), "\r\n");
            String[] fieldArr = null;
            Map<String, String> fieldMap = null;
            String[] keyValArr = null;
            JSONArray dataArr = new JSONArray();
            String idCardPhysicalNo = "";
            for (String record : dataArray) {
                fieldArr = StringUtils.split(record.split(split)[1].trim(), "\t");
                fieldMap = Maps.newHashMap();
                for (int i = 0, len = fieldArr.length; i < len; i++) {
                    keyValArr = fieldArr[i].trim().split("=", 2);
                    fieldMap.put(keyValArr[0], keyValArr[1]);
                    if ("SN_Num".equals(keyValArr[0])) {
                        idCardPhysicalNo = keyValArr[1];
                    }
                }
                dataArr.add(fieldMap);
            }

            String key = AccCacheKeyConstants.IDENTITY_CARD_INFO_KEY + idCardPhysicalNo;
            if (!autoUpload) {
                // 非自动上传带命令编号,KEY
                key = AccCacheKeyConstants.IDENTITY_CARD_INFO_KEY + devCQDataBean.getCmdId();
            }
            json.put("sn", devCQDataBean.getSn());
            json.put("table", devCQDataBean.getTable());
            json.put("data", dataArr);
            accCacheManager.setIdentityCardInfo(key, json.toJSONString());
            accPersonService.saveIdentityCardInfo(fieldMap, systemFilePath);
        }
    }

    /**
     * @Description: 处理一体机上传抓拍照片
     * <AUTHOR>
     * @date 2018/6/13 15:28
     * @param devCQDataBean
     * @return
     */
    private void dealAttPhotoInfo(AccDevCQDataBean devCQDataBean) {
        String datas = devCQDataBean.getData();
        for (String data : datas.split("\r\n")) {
            String photoName = StringUtils.substringBetween(data, "pin=", "\t");// 设备上传图片名称
            if (photoName.contains("-")) {
                // 由于设备照片文件名是否带pin规则过于复杂且不好兼容，所以统一只使用时间来命名文件
                photoName = photoName.substring(0, photoName.indexOf("-")) + ".jpg";
            }
            String picBase64 = StringUtils.substringAfter(data, "photo=");
            String photoPath = systemFilePath + File.separator + AccConstants.LINKAGE_PHOTO_PATH + File.separator
                + devCQDataBean.getSn();
            File eventSoundFilePath = new File(photoPath);
            if (!eventSoundFilePath.isAbsolute()) { // 判断是否是绝对路径
                photoPath = ClassUtil.getRootPath() + "/" + photoPath;
                eventSoundFilePath = new File(photoPath);
            }
            if (!eventSoundFilePath.exists()) {
                eventSoundFilePath.mkdirs();
            }
            photoPath = photoPath + "/" + photoName;
            Base64Util.generateImage(picBase64, photoPath);// 生成图片
            String encryptPath =
                AccConstants.LINKAGE_PHOTO_PATH + File.separator + devCQDataBean.getSn() + File.separator + photoName;
            // 对考勤照片进行加密
            FileEncryptUtil.encryptFileByPath(encryptPath);
            // 推送考勤照片
            sendAttPhotoToThird(devCQDataBean.getSn(), photoName);
        }
    }

    private void sendAttPhotoToThird(String devSn, String photoName) {
        if (Objects.nonNull(hep4OtherTransactionService)) {
            Hep4OtherTransactionItem acc4HepTransactionItem = new Hep4OtherTransactionItem();
            acc4HepTransactionItem.setPhotoName(photoName);
            acc4HepTransactionItem.setVidLinkageHandle(AccConstants.LINKAGE_PHOTO_PATH + "/" + devSn + "/" + photoName);
            String timePin = photoName.substring(0, photoName.indexOf("."));
            if (timePin.indexOf("-") > 0) {
                acc4HepTransactionItem
                    .setEventTime(DateUtil.stringToDate(timePin.split("-")[0], DateUtil.DateStyle.YYYYMMDDHHMMSS));
            } else {
                acc4HepTransactionItem.setEventTime(DateUtil.stringToDate(timePin, DateUtil.DateStyle.YYYYMMDDHHMMSS));
            }
            acc4HepTransactionItem.setDevSn(devSn);
            hep4OtherTransactionService.pushPhotoToHep(acc4HepTransactionItem);
        }
        if (Objects.nonNull(pushCenter4OtherService)) {
            // 推送至数据推送中心
            PushCenter4OtherItem pushCenter4OtherItem = new PushCenter4OtherItem();
            pushCenter4OtherItem.setPushCenter4OtherEnum(PushCenter4OtherEnum.ACC_TRANSACTION_IMAGE);
            pushCenter4OtherItem.setContent(AccConstants.LINKAGE_PHOTO_PATH + "/" + devSn + "/" + photoName);
            pushCenter4OtherService.pushData(pushCenter4OtherItem);
        }
    }

    private void dealFvtemplateInfo(AccDevCQDataBean devCQDataBean, boolean autoUpload) {
        JSONObject dataJson = new JSONObject();
        try {
            int count = devCQDataBean.getCount();
            if (count == 0) {
                dataJson.put("count", "0");
                dataJson.put("insert", "0");
                dataJson.put("update", "0");
            } else if (count > 0) {
                // 先把包的信息传给前端
                if (!autoUpload) {
                    setPackageInfoToCache(devCQDataBean, dataJson);
                }
                Map<String, String> addMaps = accPersonService.addPersonFvTempalteRecord(devCQDataBean.getTable(),
                    devCQDataBean.getData(), devCQDataBean.getCommType());
                // 下发给权限组下的其他设备
                if (autoUpload) {
                    accPersonService.sendTemplateToDevs(devCQDataBean.getTable(), devCQDataBean.getData(),
                        devCQDataBean.getCommType(), devCQDataBean.getSn());
                }
                dataJson.put("count", addMaps.get("count"));
                dataJson.put("insert", addMaps.get("insert"));
                dataJson.put("update", addMaps.get("update"));
            } else if (count < 0) {
                dataJson.put("count", count);
                dataJson.put("insert", "-1");
                dataJson.put("update", "-1");
            }
            if (!autoUpload) {
                setDataToCache(devCQDataBean, dataJson);
            }
        } catch (Exception e) {
            log.error("deal fvtemplate error", e);
            if (!autoUpload) {
                dataJson.put("exception", "common_dev_devDealException");
                setDataToCache(devCQDataBean, dataJson);
            }
        }
    }

    private void dealPersonPic(AccDevCQDataBean devCQDataBean, boolean autoUpload) {
        String data = devCQDataBean.getData();
        for (String userPic : data.split("\n")) {
            accPersonService.savePersonPhoto(userPic);
        }
        if (autoUpload) {
            accPersonService.sendUserPicToDevs(devCQDataBean.getTable(), data, devCQDataBean.getSn());
        }
    }

    /**
     * @Description: 处理设备上传人员信息
     * <AUTHOR>
     * @date 2018/6/12 19:17
     * @param personData
     * @param autoUpload
     * @return
     */
    private void dealPersonInfo(AccDevCQDataBean personData, boolean autoUpload) {
        JSONObject dataJson = new JSONObject();
        try {
            int count = personData.getCount();
            if (count == 0) {
                dataJson.put("count", 0);
                dataJson.put("insert", 0);
                dataJson.put("update", 0);
            } else if (count > 0) {
                // 先把包的信息传给前端
                if (!autoUpload) {
                    setPackageInfoToCache(personData, dataJson);
                }
                Map<String, Integer> retMap = accPersonService.dealPersonInfo(personData.getTable(),
                    personData.getData(), String.valueOf(personData.getCmdId()), personData.getSn());
                dataJson.put("count", retMap.get("count"));
                dataJson.put("insert", retMap.get("insert"));
                dataJson.put("update", retMap.get("update"));
                // 下发给权限组下的其他设备
                if (autoUpload) {
                    accPersonService.sendPersToDevs(personData.getTable(), personData.getData(),
                        personData.getCommType(), personData.getSn());
                }
            } else {
                dataJson.put("count", count);
                dataJson.put("insert", -1);
                dataJson.put("update", -1);
            }
            if (!autoUpload) {
                setDataToCache(personData, dataJson);// 操作完成后，将数据(count、insert、update、timeout)更新到内存(redis)中的数据
            }
        } catch (Exception e) {
            log.error("deal user info error", e);
            if (!autoUpload) {
                dataJson.put("exception", "common_dev_devDealException");
                setDataToCache(personData, dataJson);
            }
        }
    }

    private void setPackageInfoToCache(AccDevCQDataBean transData, JSONObject dataJson) {
        String key = AccCacheKeyConstants.QUERY_DATA + transData.getCmdId();
        if (!accCacheManager.exists(key)) {
            JSONObject json = new JSONObject();
            json.put("sn", transData.getSn());
            json.put("packCnt", transData.getPackCnt());
            json.put("packIdx", 0);
            json.put("count", 0);
            json.put("insert", 0);
            json.put("update", 0);
            json.put("table", transData.getTable());

            int commType = transData.getCommType();
            if (commType == ConstUtil.COMM_TCPIP) { // pull设备,给个固件的超时时间
                json.put("timeout", AccConstants.PULL_WAIT_TIME);
            } else if (commType == ConstUtil.COMM_RS485) { // pull设备,给个固件的超时时间
                json.put("timeout", AccConstants.PULL_WAIT_TIME * 3);
            } else if (commType == ConstUtil.COMM_HTTP) {// push设备,根据每个设备包在多少来设定超时时间
                json.put("timeout", transData.getPackCnt() * AccConstants.PUSH_WAIT_TIME);
            }
            accCacheManager.setPackageInfoToCache(transData.getCmdId(), json);
        }
    }

    /**
     * @Description: 保存处理结果
     * @param transData
     * @param dataJson
     * @return
     */
    private synchronized void setDataToCache(AccDevCQDataBean transData, JSONObject dataJson) {
        if (Objects.nonNull(dataJson)) {
            String key = AccCacheKeyConstants.QUERY_DATA + transData.getCmdId();
            JSONObject json = new JSONObject();
            if (!dataJson.containsKey("exception")) {
                json.put("sn", transData.getSn());
                json.put("packCnt", transData.getPackCnt());
                json.put("packIdx", transData.getPackIdx());
                json.put("table", transData.getTable());
                if (accCacheManager.exists(key)) { // 存在(新增setPackageInfoToCache方法，所以这时候redis肯定存在)
                    JSONObject tempJson = accCacheManager.getQueryData(transData.getCmdId());
                    json.put("count", dataJson.getIntValue("count") + tempJson.getIntValue("count"));
                    json.put("insert", dataJson.getIntValue("insert") + tempJson.getIntValue("insert"));
                    json.put("update", dataJson.getIntValue("update") + tempJson.getIntValue("update"));
                    // 必须自行统计这个值，不然前端进度条结束判断会有问题，主要是处理上是多线程，packIdx不能根据设备上传的顺序去更新
                    json.put("packIdx", tempJson.getIntValue("packIdx") + 1);
                    if (tempJson.containsKey("timeout")) {
                        json.put("timeout", tempJson.getIntValue("timeout"));// 超时时间
                    }
                    accCacheManager.setPackageInfoToCache(transData.getCmdId(), json);
                } else { // 不存在(数据为空的时候)
                    json.put("count", dataJson.getString("count"));
                    json.put("insert", dataJson.getString("insert"));
                    json.put("update", dataJson.getString("update"));
                    accCacheManager.setPackageInfoToCache(transData.getCmdId(), json);
                }
            } else {
                dataJson.put("sn", transData.getSn());
                accCacheManager.setPackageInfoToCache(transData.getCmdId(), dataJson);
            }
        }
    }

    /**
     * @Description: 处理设备上传指纹模板
     * <AUTHOR>
     * @date 2018/6/12 19:18
     * @param devCQDataBean
     * @param autoUpload
     * @return
     */
    private void dealTemplate(AccDevCQDataBean devCQDataBean, boolean autoUpload) {
        JSONObject dataJson = new JSONObject();
        int count = devCQDataBean.getCount();
        if (count == 0) {
            dataJson.put("count", 0);
            dataJson.put("insert", 0);
            dataJson.put("update", 0);
        } else if (count > 0) {
            // 先把包的信息传给前端
            if (!autoUpload) {
                setPackageInfoToCache(devCQDataBean, dataJson);
            }
            Map<String, Integer> retMap = accPersonService.dealPersonTemplateInfo(devCQDataBean.getTable(),
                devCQDataBean.getData(), devCQDataBean.getSn());// 处理设备上传指纹信息
            dataJson.put("count", retMap.get("count"));
            dataJson.put("insert", retMap.get("insert"));
            dataJson.put("update", retMap.get("update"));
            // 下发给权限组下的其他设备
            if (autoUpload) {
                accPersonService.sendTemplateToDevs(devCQDataBean.getTable(), devCQDataBean.getData(),
                    devCQDataBean.getCommType(), devCQDataBean.getSn());
            }
        } else {
            dataJson.put("count", count);
            dataJson.put("insert", -1);
            dataJson.put("update", -1);
        }
        if (!autoUpload) {
            setDataToCache(devCQDataBean, dataJson);// 操作完成后，将数据(count、insert、update、timeout)更新到内存(redis)中的数据
        }
    }

    /**
     * 处理设备上传生物模版统一方法
     * 
     * @param devCQDataBean
     * @param autoUpload
     */
    private void dealBioTemplate(AccDevCQDataBean devCQDataBean, boolean autoUpload) {
        JSONObject dataJson = new JSONObject();
        int count = devCQDataBean.getCount();
        if (count == 0) {
            dataJson.put("count", 0);
            dataJson.put("insert", 0);
            dataJson.put("update", 0);
        } else if (count > 0) {
            // 先把包的信息传给前端
            if (!autoUpload) {
                setPackageInfoToCache(devCQDataBean, dataJson);
            }
            List<Map<String, String>> resolveMap = getDataMapFromDevUpload(devCQDataBean);
            Map<String, Integer> retMap = accPersonService.dealBioTemplate(devCQDataBean.getTable(), resolveMap);
            dataJson.put("count", retMap.get("count"));
            dataJson.put("insert", retMap.get("insert"));
            dataJson.put("update", retMap.get("update"));
            // 下发给权限组下的其他设备
            if (autoUpload) {
                accPersonService.sendTemplateToDevs(devCQDataBean.getTable(), resolveMap, devCQDataBean.getSn());
            }
        } else {
            dataJson.put("count", count);
            dataJson.put("insert", -1);
            dataJson.put("update", -1);
        }
        if (!autoUpload) {
            setDataToCache(devCQDataBean, dataJson);// 操作完成后，将数据(count、insert、update、timeout)更新到内存(redis)中的数据
        }
    }

    /**
     * @Description: 处理多卡信息表
     * <AUTHOR>
     * @date 2018/6/29 9:21
     * @param mulCardData
     * @param autoUpload
     * @return
     */
    private void dealMulCardUserInfo(AccDevCQDataBean mulCardData, boolean autoUpload) {
        JSONObject dataJson = new JSONObject();
        try {
            int count = mulCardData.getCount();
            if (count == 0) {
                dataJson.put("count", "0");
                dataJson.put("insert", "0");
                dataJson.put("update", "0");
            } else if (count > 0) {
                // 先把包的信息传给前端
                if (!autoUpload) {
                    setPackageInfoToCache(mulCardData, dataJson);// 如果下面解析代码报错，是否要将包信息的redis内容删除？否则key一直在redis中--add by
                                                                 // wenxin
                }
                Map<String, String> resultMap = accPersonService.dealMulCardUserInfo(mulCardData.getCommType(),
                    mulCardData.getCmdId(), mulCardData.getData(), mulCardData.getTable());
                // 下发给权限组下的其他设备
                if (autoUpload) {
                    accPersonService.sendMulCardUserInfoToDevs(mulCardData.getTable(), mulCardData.getData(),
                        mulCardData.getSn());
                }
                // Map<String, Map<String, String>> resolveMap = new HashMap<String, Map<String, String>>();
                // int commType = mulCardData.getCommType();
                // long cmdId = mulCardData.getCmdId();
                // //解析数据
                // if (commType == ConstUtil.COMM_TCPIP || commType == ConstUtil.COMM_RS485)
                // {
                // resolveMap = accPersonBiz.getDataMapByPull(mulCardData);
                // }
                // else if (commType == ConstUtil.COMM_HTTP)
                // {
                // resolveMap = accPersonBiz.getDataMapByPush(mulCardData);
                // }
                // //从解析的数据中获取卡号集合用于查询
                // Set<String> cardNoSet = new HashSet<String>();
                // Set<String> pinSet = new HashSet<String>();
                // for (String key : resolveMap.keySet())
                // {
                // Map<String, String> data = resolveMap.get(key);
                // String cardNo = data.get("cardno");
                // pinSet.add(data.get("pin"));
                // if (StringUtils.isNotBlank(cardNo))
                // {
                // //set本身已经去重功能，没必要加contain modfied by max 20151120优化代码；
                // cardNoSet.add(cardNo);
                // }
                // }
                // Map<String, Set<String>> personCardMap = accPersonBiz.getPersCardByCardNo(cardNoSet);
                // PersonAndCardBean personAndCardBean = accPersonBiz.getPersPersonByPin(pinSet);
                // dataJson = accPersonBiz.insertMulCardRecord(resolveMap, cmdId, personAndCardBean, personCardMap);
                dataJson.put("count", resultMap.get("count"));
                dataJson.put("insert", resultMap.get("insert"));
                dataJson.put("update", resultMap.get("update"));
            } else if (count < 0) {
                dataJson.put("count", count);
                dataJson.put("insert", "-1");
                dataJson.put("update", "-1");
            }
            if (!autoUpload) {
                setDataToCache(mulCardData, dataJson);
            }
        } catch (Exception e) {
            if (!autoUpload) {
                dataJson.put("exception", "common_dev_devDealException");
                setDataToCache(mulCardData, dataJson);
            }
            throw new ZKBusinessException(e);
        }
    }

    /**
     * @Description: 处理扩展人员
     * <AUTHOR>
     * @date 2018/6/29 9:22
     * @param personData
     * @param autoUpload
     * @return
     */
    private void dealExtuserInfo(AccDevCQDataBean personData, boolean autoUpload) {
        JSONObject dataJson = new JSONObject();
        try {
            int count = personData.getCount();
            if (count == 0) {
                dataJson.put("count", "0");
                dataJson.put("insert", "0");
                dataJson.put("update", "0");
            } else if (count > 0) {
                // 先把包的信息传给前端
                if (!autoUpload) {
                    setPackageInfoToCache(personData, dataJson);
                }
                Map<String, String> resultMap = accPersonService.dealExtuserInfo(personData.getCommType(),
                    personData.getCmdId(), personData.getSn(), personData.getData(), personData.getTable());
                dataJson.put("count", resultMap.get("count"));
                dataJson.put("insert", resultMap.get("insert"));
                dataJson.put("update", resultMap.get("update"));
                // 下发给权限组下的其他设备
                if (autoUpload) {
                    accPersonService.sendExtInfoToDevs(personData.getTable(), personData.getData(), personData.getSn());
                }
            }
            if (!autoUpload) {
                setDataToCache(personData, dataJson);
            }
        } catch (Exception e) {
            if (!autoUpload) {
                dataJson.put("exception", "common_dev_devDealException");
                setDataToCache(personData, dataJson);
            }
            throw new ZKBusinessException(e);
        }
    }

    /**
     * @Description: 添加人脸处理
     * <AUTHOR>
     * @date 2018/6/28 19:43
     * @param devCQDataBean
     * @param autoUpload
     * @return
     */
    private void dealFaceInfo(AccDevCQDataBean devCQDataBean, boolean autoUpload) {
        JSONObject dataJson = new JSONObject();
        try {
            int count = devCQDataBean.getCount();
            if (count == 0) {
                dataJson.put("count", "0");
                dataJson.put("insert", "0");
                dataJson.put("update", "0");
            } else if (count > 0) {
                // 先把包的信息传给前端
                if (!autoUpload) {
                    setPackageInfoToCache(devCQDataBean, dataJson);
                }
                Map<String, String> addMaps = accPersonService.addPersonFaceRecord(devCQDataBean.getCommType(),
                    devCQDataBean.getData(), devCQDataBean.getTable());
                // 下发给权限组下的其他设备 ，目前的同步方式会影响性能
                // accPersonBiz.sendPersOrTemplateToDevs(devCQDataBean);
                dataJson.put("count", addMaps.get("count"));
                dataJson.put("insert", addMaps.get("insert"));
                dataJson.put("update", addMaps.get("update"));
            } else if (count < 0) {
                dataJson.put("count", count);
                dataJson.put("insert", "-1");
                dataJson.put("update", "-1");
            }
            if (!autoUpload) {
                setDataToCache(devCQDataBean, dataJson);
            }
        } catch (Exception e) {
            if (!autoUpload) {
                dataJson.put("exception", "common_dev_devDealException");
                setDataToCache(devCQDataBean, dataJson);
            }
            throw new ZKBusinessException(e);
        }
    }

    /**
     * 处理设备上传比对照片
     * 
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年9月4日 上午10:41:45
     * @param devCQDataBean
     * @param autoUpload
     */
    private void dealBioPhotoInfo(AccDevCQDataBean devCQDataBean, boolean autoUpload) {
        JSONObject dataJson = new JSONObject();
        try {
            int count = devCQDataBean.getCount();
            if (count == 0) {
                dataJson.put("count", "0");
                dataJson.put("insert", "0");
                dataJson.put("update", "0");
            } else if (count > 0) {
                // 先把包的信息传给前端
                setPackageInfoToCache(devCQDataBean, dataJson);
                // 统计添加、更新比对照片
                Map<String, String> addMaps = accPersonService.addPersonBiophotoRecord(devCQDataBean.getCommType(),
                    devCQDataBean.getData(), devCQDataBean.getTable());
                dataJson.put("count", addMaps.get("count"));
                dataJson.put("insert", addMaps.get("insert"));
                dataJson.put("update", addMaps.get("update"));
                if (autoUpload) {
                    accPersonService.sendBioPhotoInfoToDevs(devCQDataBean.getTable(), devCQDataBean.getData(),
                        devCQDataBean.getSn());
                }
            } else if (count < 0) {
                dataJson.put("count", count);
                dataJson.put("insert", "-1");
                dataJson.put("update", "-1");
            }
            if (!autoUpload) {
                setDataToCache(devCQDataBean, dataJson);
            }
        } catch (Exception e) {
            if (!autoUpload) {
                dataJson.put("exception", "common_dev_devDealException");
                setDataToCache(devCQDataBean, dataJson);
            }
            throw new ZKBusinessException(e);
        }
    }

    /**
     * 处理设备上传一体化模板数据
     * 
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年11月14日 下午2:55:43
     * @param devCQDataBean
     * @param autoUpload
     */
    private void dealBioDataInfo(AccDevCQDataBean devCQDataBean, boolean autoUpload) {
        JSONObject dataJson = new JSONObject();
        try {
            int count = devCQDataBean.getCount();
            if (count == 0) {
                dataJson.put("count", "0");
                dataJson.put("insert", "0");
                dataJson.put("update", "0");
            } else if (count > 0) {
                // 先把包的信息传给前端
                setPackageInfoToCache(devCQDataBean, dataJson);
                Map<String, String> addMaps = accPersonService.addPersonBiodataRecord(devCQDataBean.getCommType(),
                    devCQDataBean.getData(), devCQDataBean.getTable());
                dataJson.put("count", addMaps.get("count"));
                dataJson.put("insert", addMaps.get("insert"));
                dataJson.put("update", addMaps.get("update"));
            } else if (count < 0) {
                dataJson.put("count", count);
                dataJson.put("insert", "-1");
                dataJson.put("update", "-1");
            }
            if (!autoUpload) {
                setDataToCache(devCQDataBean, dataJson);
            }
        } catch (Exception e) {
            if (!autoUpload) {
                dataJson.put("exception", "common_dev_devDealException");
                setDataToCache(devCQDataBean, dataJson);
            }
            throw new ZKBusinessException(e);
        }

    }

    private List<Map<String, String>> getDataMapFromDevUpload(AccDevCQDataBean devCQDataBean) {
        AccQueryDeviceItem accQueryDeviceItem = accDeviceService.getQueryItemBySn(devCQDataBean.getSn());
        short commType = accQueryDeviceItem.getCommType();
        List<Map<String, String>> resolveMap = new ArrayList<>();
        if (commType == ConstUtil.COMM_TCPIP || commType == ConstUtil.COMM_RS485) {
            resolveMap = getDataMapByPull(devCQDataBean.getTable(), devCQDataBean.getData());// pull通信方式-- 解析人员
        } else if (commType == ConstUtil.COMM_HTTP) {
            resolveMap = getDataMapByPush(devCQDataBean.getTable(), devCQDataBean.getData());// push通信方式-- 解析人员
        } else if (commType == AccConstants.COMM_TYPE_BEST_MQTT) {
            // Mqtt通信方式-- 解析人员 add by colin 2020-10-30 14:28:18
            resolveMap = getDataMapByMqtt(devCQDataBean.getTable(), devCQDataBean.getData());
        }
        return resolveMap;
    }

    /**
     * 解析push数据包
     * 
     * @param table
     * @param data
     * @return
     */
    private List<Map<String, String>> getDataMapByPush(String table, String data) {
        List<Map<String, String>> retMap = new ArrayList<>();
        String[] personDataArray = StringUtils.split(data.trim(), "\r\n");
        String[] fieldArr = null;
        Map<String, String> fieldMap = null;
        String[] keyValArr = null;
        for (String record : personDataArray) {
            fieldArr = StringUtils.split(record.split(table)[1].trim(), "\t");
            fieldMap = new HashMap<>();
            for (int i = 0, len = fieldArr.length; i < len; i++) {
                keyValArr = fieldArr[i].trim().split("=", 2);
                fieldMap.put(keyValArr[0], keyValArr[1]);
            }
            // 过滤访客人员，访客pin号为8开头的9位数字
            if (!(fieldMap.get("pin").trim().length() == 9 && fieldMap.get("pin").trim().startsWith("8"))) {
                retMap.add(fieldMap);
            }
        }
        return retMap;
    }

    private List<Map<String, String>> getDataMapByPull(String table, String data) {
        List<Map<String, String>> retMap = new ArrayList<>();
        String[] recordArr = StringUtils.split(data, "\r\n"); // 分割每一条数据 [Pin,FunSwitch, 2765,0, 111,0, 112,0]
        // 表示为表头
        List<String> headList = new ArrayList<>();
        String[] headArray = StringUtils.split(recordArr[0], ",");
        for (String head : headArray) {
            headList.add(head.toLowerCase());
        }
        String[] infoArr = null;
        Map<String, String> tempMap = null;
        for (int i = 1, len = recordArr.length; i < len; i++) {
            // 表示数据
            infoArr = recordArr[i].split(",");
            tempMap = new HashMap<>();
            for (int j = 0, innerLen = infoArr.length; j < innerLen; j++) {
                tempMap.put(headList.get(j), infoArr[j]);
            }
            if (!(tempMap.get("pin").trim().length() == 9 && tempMap.get("pin").trim().startsWith("8"))) {
                retMap.add(tempMap);
            }
        }
        return retMap;
    }

    private List<Map<String, String>> getDataMapByMqtt(String table, String data) {
        List<Map<String, String>> retMap = new ArrayList<>();
        Map<String, String> fieldMap;
        if (StringUtils.isNoneBlank(data)) {
            JSONArray jsonArr = JSONObject.parseArray(data);
            for (int i = 0; i < jsonArr.size(); i++) {
                fieldMap = new HashMap<>();
                JSONObject json = jsonArr.getJSONObject(i);
                for (String key : json.keySet()) {
                    String val = json.getString(key);
                    if (AccConstants.MQTT_PUSH_PARAM.containsKey(key)) {
                        String pushKey = AccConstants.MQTT_PUSH_PARAM.get(key);
                        fieldMap.put(pushKey, val);
                    } else {
                        fieldMap.put(key, val);
                    }
                }
                // 过滤访客人员，访客pin号为8开头的9位数字
                if (!(fieldMap.get("pin").trim().length() == 9 && fieldMap.get("pin").trim().startsWith("8"))) {
                    retMap.add(fieldMap);
                }
            }
        }
        return retMap;
    }
}
