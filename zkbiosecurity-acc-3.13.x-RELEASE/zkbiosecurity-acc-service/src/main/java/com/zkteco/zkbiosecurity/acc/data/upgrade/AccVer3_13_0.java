package com.zkteco.zkbiosecurity.acc.data.upgrade;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AccVer3_13_0 implements UpgradeVersionManager {
    @Autowired
    private AuthPermissionService authPermissionService;

    @Override
    public String getModule() {
        return BaseConstants.ACC;
    }

    @Override
    public String getVersion() {
        return "v3.13.0";
    }

    @Override
    public boolean executeUpgrade() {
        AuthPermissionItem subMenuItem = authPermissionService.getItemByCode("AccDevice");
        if (subMenuItem != null) {
            AuthPermissionItem subButtonItem = new AuthPermissionItem("AccDeviceSetFaceServerInfo", "acc_dev_setFaceServerInfo",
                    "acc:device:setFaceServerInfo", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 39);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);
        }
        return true;
    }
}
