package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Entity
@Table(name = "ACC_EXT_DEVICE")
@Getter
@Setter
@Accessors(chain = true)
public class AccExtDevice extends BaseModel implements Serializable {

    @Column(name = "EXT_BOARD_NO")
    private Short extBoardNo;

    @Column(name = "ALIAS", length = 100, nullable = false, unique = true)
    private String alias;

    @Column(name = "EXT_BOARD_TYPE", nullable = false)
    private Short extBoardType;

    /** RS485 Address */
    @Column(name = "COMM_ADDRESS")
    private Short commAddress;

    /** 所属设备id */
    @Column(name = "DEV_ID")
    private String devId;

    @Column(name = "DEV_PROTOCOL_TYPE")
    private Short devProtocolType;

    /** 对应设备上rs485物理口 pro */
    @Column(name = "SERIAL_PORT")
    private Short serialPort;

    /** aperio地址模式 */
    @Column(name = "APERIO_MACADDR_MODE")
    private Short aperioMacAddrMode;

    @Column(name = "PARENT_ID")
    private String parentId;

    // pro字段

    /** 扩展板序列号 */
    @Column(name = "SN", length = 30)
    private String sn;

    /** 扩展板固件版本 */
    @Column(name = "FW_VERSION", length = 50)
    private String fwVersion;
}
