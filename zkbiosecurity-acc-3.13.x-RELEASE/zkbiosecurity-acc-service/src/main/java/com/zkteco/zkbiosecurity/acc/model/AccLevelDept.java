package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 门禁权限组-部门中间表
 * <AUTHOR>
 * @date 2018/3/14 17:04
 */
@Entity
@Table(name = "ACC_LEVEL_DEPT")
@Getter
@Setter
@Accessors(chain=true)
public class AccLevelDept extends BaseModel implements Serializable {

    @ManyToOne
    @JoinColumn(name="LEVEL_ID", nullable = false)
    private AccLevel accLevel;

    @Column(name = "DEPT_ID", nullable = false)
    private String deptId;

    public AccLevelDept() {
    }

    public AccLevelDept(AccLevel accLevel, String deptId) {
        this.accLevel = accLevel;
        this.deptId = deptId;
    }
}
