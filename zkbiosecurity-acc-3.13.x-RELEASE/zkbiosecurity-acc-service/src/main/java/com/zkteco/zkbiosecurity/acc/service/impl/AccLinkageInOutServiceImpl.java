/**
 * File Name: AccLinkageInOutServiceImpl Created by GenerationTools on 2018-03-16 下午04:41 Copyright:Copyright ©
 * 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.dao.AccLinkageDao;
import com.zkteco.zkbiosecurity.acc.dao.AccLinkageInOutDao;
import com.zkteco.zkbiosecurity.acc.model.AccLinkage;
import com.zkteco.zkbiosecurity.acc.model.AccLinkageInOut;
import com.zkteco.zkbiosecurity.acc.service.AccLinkageInOutService;
import com.zkteco.zkbiosecurity.acc.vo.AccLinkageInOutItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 对应百傲瑞达 AccLinkageInOutServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-16 下午04:41
 * @version v1.0
 */
@Service
@Transactional
public class AccLinkageInOutServiceImpl implements AccLinkageInOutService {
    @Autowired
    private AccLinkageInOutDao accLinkageInOutDao;
    @Autowired
    private AccLinkageDao accLinkageDao;

    @Override
    public AccLinkageInOutItem saveItem(AccLinkageInOutItem item) {
        AccLinkageInOut accLinkageInOut = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accLinkageInOutDao.findById(id)).orElse(new AccLinkageInOut());

        ModelUtil.copyProperties(item, accLinkageInOut);
        accLinkageInOutDao.save(accLinkageInOut);
        item.setId(accLinkageInOut.getId());
        return item;
    }

    @Override
    public List<AccLinkageInOutItem> getByCondition(AccLinkageInOutItem condition) {
        return (List<AccLinkageInOutItem>)accLinkageInOutDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accLinkageInOutDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accLinkageInOutDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AccLinkageInOutItem getItemById(String id) {
        List<AccLinkageInOutItem> items = getByCondition(new AccLinkageInOutItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public void handlerTransfer(List<AccLinkageInOutItem> accLinkageInOutItems) {
        // 数据量大的时候处理，分批处理
        List<List<AccLinkageInOutItem>> accLinkageInOutItemList =
            CollectionUtil.split(accLinkageInOutItems, CollectionUtil.splitSize);
        for (List<AccLinkageInOutItem> itemList : accLinkageInOutItemList) {
            // 获取已存在的记录根据名称获取
            Collection<String> names =
                CollectionUtil.getPropertyList(itemList, AccLinkageInOutItem::getLinkageName, "-1");
            List<AccLinkage> accLinkageList = accLinkageDao.findByNameIn(names);
            Map<String, AccLinkage> accLinkageMap = CollectionUtil.listToKeyMap(accLinkageList, AccLinkage::getName);
            for (AccLinkageInOutItem accLinkageInOutItem : itemList) {
                AccLinkageInOut accLinkageInOut = new AccLinkageInOut();
                ModelUtil.copyPropertiesIgnoreNullWithProperties(accLinkageInOutItem, accLinkageInOut, "id");
                AccLinkage accLinkage = accLinkageMap.get(accLinkageInOutItem.getLinkageName());
                if (Objects.nonNull(accLinkage)) {
                    accLinkageInOut.setAccLinkage(accLinkage);
                }
                accLinkageInOut.setInputId(String.valueOf(accLinkageInOutItem.getInputId()));
                accLinkageInOut.setOutputId(String.valueOf(accLinkageInOutItem.getOutputId()));
                accLinkageInOutDao.save(accLinkageInOut);
            }
        }

    }

    @Override
    public List<AccLinkageInOutItem> getLinkageInOutItemsByLinkageId(String linkageId) {
        List<AccLinkageInOut> linkageInOutList = accLinkageInOutDao.findByAccLinkage_Id(linkageId);
        List<AccLinkageInOutItem> linkageInOutItemList = new ArrayList<>();
        if (Objects.nonNull(linkageInOutList) && linkageInOutList.size() > 0) {
            for (AccLinkageInOut linkageInOut : linkageInOutList) {
                AccLinkageInOutItem item = new AccLinkageInOutItem();
                item.setId(linkageInOut.id);
                item.setLinkageId(linkageInOut.getAccLinkage().getId());
                item.setInputId(linkageInOut.getInputId());
                item.setInputType(linkageInOut.getInputType());
                item.setOutputId(linkageInOut.getOutputId());
                item.setOutputType(linkageInOut.getOutputType());
                item.setActionTime(linkageInOut.getActionTime());
                item.setActionType(linkageInOut.getActionType());
                linkageInOutItemList.add(item);
            }
        }
        return linkageInOutItemList;
    }
}