/**
 * File Name: AccAuxIn Created by GenerationTools on 2018-03-13 下午05:00 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 AccAuxIn
 * 
 * <AUTHOR>
 * @date: 2018-03-13 下午05:00
 * @version v1.0
 */
@Entity
@Table(name = "ACC_AUXIN")
@Getter
@Setter
@Accessors(chain = true)
public class AccAuxIn extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**  */
    @ManyToOne
    @JoinColumn(name = "DEV_ID")
    private AccDevice accDevice;

    /**  */
    @Column(name = "AUX_NO", nullable = false)
    private Short auxNo;

    /**  */
    @Column(name = "PRINTER_NUMBER", length = 20, nullable = false)
    private String printerNumber;

    /**  */
    @Column(name = "NAME", length = 100, nullable = false)
    private String name;

    /**  */
    @Column(name = "REMARK", length = 50)
    private String remark;

    /** 时间段 */
    @Column(name = "TIMESEG_ID")
    private String timeSegId;

    /** 所属扩展板id */
    @Column(name = "EXT_DEV_ID")
    private String extDevId;

    // pro

    /** 检测模式 0正常 1线路检测 mqtt */
    @Column(name = "INPUT_MODE")
    private String inputMode;

    /** 电阻值 mqtt */
    @Column(name = "SUPERVISED_RESISTOR")
    private String supervisedResistor;

    /** 是否禁用, true禁用，false可用 */
    @Column(name = "DISABLE")
    private Boolean disable;

    /** 输入类型, 0:无, 1:常开, 2:常闭*/
    @Column(name = "INPUT_TYPE")
    private String inputType;
}