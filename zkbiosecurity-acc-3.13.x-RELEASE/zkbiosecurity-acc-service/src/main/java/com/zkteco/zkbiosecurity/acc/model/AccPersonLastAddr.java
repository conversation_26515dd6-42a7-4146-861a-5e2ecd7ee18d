package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.zkteco.zkbiosecurity.core.convert.EncryptConverter;
import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 AccPersonLastAddr
 * 
 * <AUTHOR>
 * @date: 2018-03-21 11:16:25
 * @version v1.0
 */
@Entity
@Table(name = "ACC_PERSON_LASTADDR")
@Getter
@Setter
@Accessors(chain = true)
public class AccPersonLastAddr extends BaseModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 事件时间 */
    @Column(name = "EVENT_TIME")
    private Date eventTime;

    /** 人员编号 */
    @Column(name = "PIN", length = 30)
    private String pin;

    /** 姓名 */
    @Column(name = "NAME", length = 50)
    private String name;

    /** 姓氏 */
    @Column(name = "LAST_NAME", length = 50)
    private String lastName;

    /** 部门名称 */
    @Column(name = "DEPT_CODE", length = 100)
    private String deptCode;

    /** 部门名称 */
    @Column(name = "DEPT_NAME", length = 100)
    private String deptName;

    /** 区域名称 */
    @Column(name = "AREA_NAME", length = 100)
    private String areaName;

    /** 人员卡号 */
    @Convert(converter = EncryptConverter.class)
    @Column(name = "CARD_NO")
    private String cardNo;

    /** 设备id */
    @Column(name = "DEV_ID")
    private String devId;

    /** 设备序列号 */
    @Column(name = "DEV_SN", length = 30)
    private String devSn;

    /** 设备名称 */
    @Column(name = "DEV_ALIAS", length = 100)
    private String devAlias;

    /** 验证方式编号 */
    @Column(name = "VERIFY_MODE_NO")
    private Short verifyModeNo;

    /** 验证方式名称 */
    @Column(name = "VERIFY_MODE_NAME", length = 100)
    private String verifyModeName;

    /** 事件编号 */
    @Column(name = "EVENT_NO", nullable = false)
    private Short eventNo;

    /** 事件名称 */
    @Column(name = "EVENT_NAME", length = 100, nullable = false)
    private String eventName;

    /** 事件点类型 */
    @Column(name = "EVENT_POINT_TYPE")
    private Short eventPointType;

    /** 事件点id */
    @Column(name = "EVENT_POINT_ID")
    private String eventPointId;

    /** 事件点名称 */
    @Column(name = "EVENT_POINT_NAME", length = 100)
    private String eventPointName;

    /** 读头状态 */
    @Column(name = "READER_STATE")
    private Short readerState;

    /** 读头名称 */
    @Column(name = "READER_NAME", length = 100)
    private String readerName;

    /** 联动触发条件 */
    @Column(name = "TRIGGER_COND")
    private Short triggerCond;// 联动触发条件

    /** 描述 */
    @Column(name = "DESCRIPTION", length = 200)
    private String description;// 描述

    /** 媒体文件 */
    @Column(name = "VID_LINKAGE_HANDLE", length = 256)
    private String vidLinkageHandle;

    /** 门禁区域 */
    @Column(name = "ACC_ZONE", length = 30)
    private String accZone;// 门禁区域

    /** default constructor */
    public AccPersonLastAddr() {

    }
}
