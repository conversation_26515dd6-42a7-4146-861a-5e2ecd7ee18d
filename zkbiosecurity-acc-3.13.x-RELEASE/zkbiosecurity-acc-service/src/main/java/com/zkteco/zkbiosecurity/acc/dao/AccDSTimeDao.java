/**
 * File Name: AccDSTime Created by GenerationTools on 2018-02-28 下午02:21 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccDSTime;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 对应百傲瑞达 AccDSTimeDao
 * 
 * <AUTHOR>
 * @date: 2018-02-28 下午02:21
 * @version v1.0
 */
public interface AccDSTimeDao extends BaseDao<AccDSTime, String> {

    AccDSTime findByName(String name);

    AccDSTime findByInitFlag(boolean initFlag);

    /**
     * 失去下是否存在夏令时
     * 
     * @param timeZone
     * @return
     */
    boolean existsByTimeZone(String timeZone);
}