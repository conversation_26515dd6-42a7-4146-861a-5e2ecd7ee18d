/*
 * File Name: AccBaseDictionaryServiceImpl
 * <NAME_EMAIL> on 2018/5/2 11:51.
 * Copyright:Copyright © 1999-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.AccBaseDictionaryService;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryValueService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 门禁数据字典查询
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
@Service
@Transactional
public class AccBaseDictionaryServiceImpl implements AccBaseDictionaryService {

    @Autowired
    private BaseDictionaryValueService baseDictionaryValueService;

    @Override
    public Map<String, String> getBaseDictionaryMap(String key) {
        return baseDictionaryValueService.getDictionaryValuesMap(key);
    }

    @Override
    public String getCommReason(int ret) {
        return getBaseDictionaryMap("commStatus").containsKey(String.valueOf(ret)) ? getBaseDictionaryMap("commStatus").get(String.valueOf(ret)) : "";
    }
}
