/**
 * File Name: AccTimeSeg Created by GenerationTools on 2018-02-24 上午10:18 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 AccTimeSeg
 * 
 * <AUTHOR>
 * @date: 2018-02-24 上午10:18
 * @version v1.0
 */
@Entity
@Table(name = "ACC_TIMESEG")
@Getter
@Setter
@Accessors(chain = true)
public class AccTimeSeg extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /* BusinessID 下发设备中使用 */
    @Column(name = "BUSINESS_ID", unique = true)
    private Long businessId;

    /** 时间段名称 */
    @Column(name = "NAME", length = 30, nullable = true)
    private String name;

    /** 备注 */
    @Column(name = "REMARK", length = 50)
    private String remark;

    @Column(name = "INIT_FLAG")
    private Boolean initFlag;

    /** 时间段有效开始时间 */
    @Column(name = "START_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date startTime;

    /** 时间段有效结束时间 */
    @Column(name = "END_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date endTime;

    /**  */
    @Column(name = "SUNDAY_START1", nullable = true, length = 20)
    private String sundayStart1;

    /**  */
    @Column(name = "SUNDAY_END1", nullable = true, length = 20)
    private String sundayEnd1;

    /**  */
    @Column(name = "SUNDAY_START2", nullable = true, length = 20)
    private String sundayStart2;

    /**  */
    @Column(name = "SUNDAY_END2", nullable = true, length = 20)
    private String sundayEnd2;

    /**  */
    @Column(name = "SUNDAY_START3", nullable = true, length = 20)
    private String sundayStart3;

    /**  */
    @Column(name = "SUNDAY_END3", nullable = true, length = 20)
    private String sundayEnd3;

    /**  */
    @Column(name = "MONDAY_START1", nullable = true, length = 20)
    private String mondayStart1;

    /**  */
    @Column(name = "MONDAY_END1", nullable = true, length = 20)
    private String mondayEnd1;

    /**  */
    @Column(name = "MONDAY_START2", nullable = true, length = 20)
    private String mondayStart2;

    /**  */
    @Column(name = "MONDAY_END2", nullable = true, length = 20)
    private String mondayEnd2;

    /**  */
    @Column(name = "MONDAY_START3", nullable = true, length = 20)
    private String mondayStart3;

    /**  */
    @Column(name = "MONDAY_END3", nullable = true, length = 20)
    private String mondayEnd3;

    /**  */
    @Column(name = "TUESDAY_START1", nullable = true, length = 20)
    private String tuesdayStart1;

    /**  */
    @Column(name = "TUESDAY_END1", nullable = true, length = 20)
    private String tuesdayEnd1;

    /**  */
    @Column(name = "TUESDAY_START2", nullable = true, length = 20)
    private String tuesdayStart2;

    /**  */
    @Column(name = "TUESDAY_END2", nullable = true, length = 20)
    private String tuesdayEnd2;

    /**  */
    @Column(name = "TUESDAY_START3", nullable = true, length = 20)
    private String tuesdayStart3;

    /**  */
    @Column(name = "TUESDAY_END3", nullable = true, length = 20)
    private String tuesdayEnd3;

    /**  */
    @Column(name = "WEDNESDAY_START1", nullable = true, length = 20)
    private String wednesdayStart1;

    /**  */
    @Column(name = "WEDNESDAY_END1", nullable = true, length = 20)
    private String wednesdayEnd1;

    /**  */
    @Column(name = "WEDNESDAY_START2", nullable = true, length = 20)
    private String wednesdayStart2;

    /**  */
    @Column(name = "WEDNESDAY_END2", nullable = true, length = 20)
    private String wednesdayEnd2;

    /**  */
    @Column(name = "WEDNESDAY_START3", nullable = true, length = 20)
    private String wednesdayStart3;

    /**  */
    @Column(name = "WEDNESDAY_END3", nullable = true, length = 20)
    private String wednesdayEnd3;

    /**  */
    @Column(name = "THURSDAY_START1", nullable = true, length = 20)
    private String thursdayStart1;

    /**  */
    @Column(name = "THURSDAY_END1", nullable = true, length = 20)
    private String thursdayEnd1;

    /**  */
    @Column(name = "THURSDAY_START2", nullable = true, length = 20)
    private String thursdayStart2;

    /**  */
    @Column(name = "THURSDAY_END2", nullable = true, length = 20)
    private String thursdayEnd2;

    /**  */
    @Column(name = "THURSDAY_START3", nullable = true, length = 20)
    private String thursdayStart3;

    /**  */
    @Column(name = "THURSDAY_END3", nullable = true, length = 20)
    private String thursdayEnd3;

    /**  */
    @Column(name = "FRIDAY_START1", nullable = true, length = 20)
    private String fridayStart1;

    /**  */
    @Column(name = "FRIDAY_END1", nullable = true, length = 20)
    private String fridayEnd1;

    /**  */
    @Column(name = "FRIDAY_START2", nullable = true, length = 20)
    private String fridayStart2;

    /**  */
    @Column(name = "FRIDAY_END2", nullable = true, length = 20)
    private String fridayEnd2;

    /**  */
    @Column(name = "FRIDAY_START3", nullable = true, length = 20)
    private String fridayStart3;

    /**  */
    @Column(name = "FRIDAY_END3", nullable = true, length = 20)
    private String fridayEnd3;

    /**  */
    @Column(name = "SATURDAY_START1", nullable = true, length = 20)
    private String saturdayStart1;

    /**  */
    @Column(name = "SATURDAY_END1", nullable = true, length = 20)
    private String saturdayEnd1;

    /**  */
    @Column(name = "SATURDAY_START2", nullable = true, length = 20)
    private String saturdayStart2;

    /**  */
    @Column(name = "SATURDAY_END2", nullable = true, length = 20)
    private String saturdayEnd2;

    /**  */
    @Column(name = "SATURDAY_START3", nullable = true, length = 20)
    private String saturdayStart3;

    /**  */
    @Column(name = "SATURDAY_END3", nullable = true, length = 20)
    private String saturdayEnd3;

    /**  */
    @Column(name = "HOLIDAYTYPE1_START1", nullable = true, length = 20)
    private String holidayType1Start1;

    /**  */
    @Column(name = "HOLIDAYTYPE1_END1", nullable = true, length = 20)
    private String holidayType1End1;

    /**  */
    @Column(name = "HOLIDAYTYPE1_START2", nullable = true, length = 20)
    private String holidayType1Start2;

    /**  */
    @Column(name = "HOLIDAYTYPE1_END2", nullable = true, length = 20)
    private String holidayType1End2;

    /**  */
    @Column(name = "HOLIDAYTYPE1_START3", nullable = true, length = 20)
    private String holidayType1Start3;

    /**  */
    @Column(name = "HOLIDAYTYPE1_END3", nullable = true, length = 20)
    private String holidayType1End3;

    /**  */
    @Column(name = "HOLIDAYTYPE2_START1", nullable = true, length = 20)
    private String holidayType2Start1;

    /**  */
    @Column(name = "HOLIDAYTYPE2_END1", nullable = true, length = 20)
    private String holidayType2End1;

    /**  */
    @Column(name = "HOLIDAYTYPE2_START2", nullable = true, length = 20)
    private String holidayType2Start2;

    /**  */
    @Column(name = "HOLIDAYTYPE2_END2", nullable = true, length = 20)
    private String holidayType2End2;

    /**  */
    @Column(name = "HOLIDAYTYPE2_START3", nullable = true, length = 20)
    private String holidayType2Start3;

    /**  */
    @Column(name = "HOLIDAYTYPE2_END3", nullable = true, length = 20)
    private String holidayType2End3;

    /**  */
    @Column(name = "HOLIDAYTYPE3_START1", nullable = true, length = 20)
    private String holidayType3Start1;

    /**  */
    @Column(name = "HOLIDAYTYPE3_END1", nullable = true, length = 20)
    private String holidayType3End1;

    /**  */
    @Column(name = "HOLIDAYTYPE3_START2", nullable = true, length = 20)
    private String holidayType3Start2;

    /**  */
    @Column(name = "HOLIDAYTYPE3_END2", nullable = true, length = 20)
    private String holidayType3End2;

    /**  */
    @Column(name = "HOLIDAYTYPE3_START3", nullable = true, length = 20)
    private String holidayType3Start3;

    /**  */
    @Column(name = "HOLIDAYTYPE3_END3", nullable = true, length = 20)
    private String holidayType3End3;

    public boolean equals(Object obj) {
        AccTimeSeg other = (AccTimeSeg)obj;
        if (startTime == null && other.startTime != null || startTime != null && other.startTime == null) {
            return false;
        } else if (endTime == null && other.endTime != null || endTime != null && other.endTime == null) {
            return false;
        } else if (startTime != null && startTime.compareTo(other.startTime) != 0) {
            return false;
        } else if (endTime != null && endTime.compareTo(DateUtil.getDayEndTime(other.endTime)) != 0) {
            return false;
        } else if (fridayEnd1.compareTo(other.fridayEnd1) != 0) {
            return false;
        } else if (fridayEnd2.compareTo(other.fridayEnd2) != 0) {
            return false;
        } else if (fridayEnd3.compareTo(other.fridayEnd3) != 0) {
            return false;
        } else if (fridayStart1.compareTo(other.fridayStart1) != 0) {
            return false;
        } else if (fridayStart2.compareTo(other.fridayStart2) != 0) {
            return false;
        } else if (fridayStart3.compareTo(other.fridayStart3) != 0) {
            return false;
        } else if (holidayType1End1.compareTo(other.holidayType1End1) != 0) {
            return false;
        } else if (holidayType1End2.compareTo(other.holidayType1End2) != 0) {
            return false;
        } else if (holidayType1End3.compareTo(other.holidayType1End3) != 0) {
            return false;
        } else if (holidayType1Start1.compareTo(other.holidayType1Start1) != 0) {
            return false;
        } else if (holidayType1Start2.compareTo(other.holidayType1Start2) != 0) {
            return false;
        } else if (holidayType1Start3.compareTo(other.holidayType1Start3) != 0) {
            return false;
        } else if (holidayType2End1.compareTo(other.holidayType2End1) != 0) {
            return false;
        } else if (holidayType2End2.compareTo(other.holidayType2End2) != 0) {
            return false;
        } else if (holidayType2End3.compareTo(other.holidayType2End3) != 0) {
            return false;
        } else if (holidayType2Start1.compareTo(other.holidayType2Start1) != 0) {
            return false;
        } else if (holidayType2Start2.compareTo(other.holidayType2Start2) != 0) {
            return false;
        } else if (holidayType2Start3.compareTo(other.holidayType2Start3) != 0) {
            return false;
        } else if (holidayType3End1.compareTo(other.holidayType3End1) != 0) {
            return false;
        } else if (holidayType3End2.compareTo(other.holidayType3End2) != 0) {
            return false;
        } else if (holidayType3End3.compareTo(other.holidayType3End3) != 0) {
            return false;
        } else if (holidayType3Start1.compareTo(other.holidayType3Start1) != 0) {
            return false;
        } else if (holidayType3Start2.compareTo(other.holidayType3Start2) != 0) {
            return false;
        } else if (holidayType3Start3.compareTo(other.holidayType3Start3) != 0) {
            return false;
        } else if (mondayEnd1.compareTo(other.mondayEnd1) != 0) {
            return false;
        } else if (mondayEnd2.compareTo(other.mondayEnd2) != 0) {
            return false;
        } else if (mondayEnd3.compareTo(other.mondayEnd3) != 0) {
            return false;
        } else if (mondayStart1.compareTo(other.mondayStart1) != 0) {
            return false;
        } else if (mondayStart2.compareTo(other.mondayStart2) != 0) {
            return false;
        } else if (mondayStart3.compareTo(other.mondayStart3) != 0) {
            return false;
        } else if (saturdayEnd1.compareTo(other.saturdayEnd1) != 0) {
            return false;
        } else if (saturdayEnd2.compareTo(other.saturdayEnd2) != 0) {
            return false;
        } else if (saturdayEnd3.compareTo(other.saturdayEnd3) != 0) {
            return false;
        } else if (saturdayStart1.compareTo(other.saturdayStart1) != 0) {
            return false;
        } else if (saturdayStart2.compareTo(other.saturdayStart2) != 0) {
            return false;
        } else if (saturdayStart3.compareTo(other.saturdayStart3) != 0) {
            return false;
        } else if (sundayEnd1.compareTo(other.sundayEnd1) != 0) {
            return false;
        } else if (sundayEnd2.compareTo(other.sundayEnd2) != 0) {
            return false;
        } else if (sundayEnd3.compareTo(other.sundayEnd3) != 0) {
            return false;
        } else if (sundayStart1.compareTo(other.sundayStart1) != 0) {
            return false;
        } else if (sundayStart2.compareTo(other.sundayStart2) != 0) {
            return false;
        } else if (sundayStart3.compareTo(other.sundayStart3) != 0) {
            return false;
        } else if (thursdayEnd1.compareTo(other.thursdayEnd1) != 0) {
            return false;
        } else if (thursdayEnd2.compareTo(other.thursdayEnd2) != 0) {
            return false;
        } else if (thursdayEnd3.compareTo(other.thursdayEnd3) != 0) {
            return false;
        } else if (thursdayStart1.compareTo(other.thursdayStart1) != 0) {
            return false;
        } else if (thursdayStart2.compareTo(other.thursdayStart2) != 0) {
            return false;
        } else if (thursdayStart3.compareTo(other.thursdayStart3) != 0) {
            return false;
        } else if (tuesdayEnd1.compareTo(other.tuesdayEnd1) != 0) {
            return false;
        } else if (tuesdayEnd2.compareTo(other.tuesdayEnd2) != 0) {
            return false;
        } else if (tuesdayEnd3.compareTo(other.tuesdayEnd3) != 0) {
            return false;
        } else if (tuesdayStart1.compareTo(other.tuesdayStart1) != 0) {
            return false;
        } else if (tuesdayStart2.compareTo(other.tuesdayStart2) != 0) {
            return false;
        } else if (tuesdayStart3.compareTo(other.tuesdayStart3) != 0) {
            return false;
        } else if (wednesdayEnd1.compareTo(other.wednesdayEnd1) != 0) {
            return false;
        } else if (wednesdayEnd2.compareTo(other.wednesdayEnd2) != 0) {
            return false;
        } else if (wednesdayEnd3.compareTo(other.wednesdayEnd3) != 0) {
            return false;
        } else if (wednesdayStart1.compareTo(other.wednesdayStart1) != 0) {
            return false;
        } else if (wednesdayStart2.compareTo(other.wednesdayStart2) != 0) {
            return false;
        } else if (wednesdayStart3.compareTo(other.wednesdayStart3) != 0) {
            return false;
        }
        return true;
    }

}