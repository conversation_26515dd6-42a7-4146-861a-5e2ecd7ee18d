package com.zkteco.zkbiosecurity.acc.dao;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.acc.model.AccExtDevice;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

public interface AccExtDeviceDao extends BaseDao<AccExtDevice, String> {
    List<AccExtDevice> findByDevIdIn(Collection<String> devIds);

    AccExtDevice findByAlias(String alias);

    int countAccExtDeviceByDevIdAndCommAddress(String devId, Short commaddress);

    @Query(value = "select e.extBoardNo from AccExtDevice e where e.devId = ?1 order by e.extBoardNo")
    List<Short> findExtBoardNoByDevId(String devId);

    int countAccExtDeviceByDevIdAndExtBoardType(String devId, Short extBoardType);

    List<AccExtDevice> findByParentId(String parentId);
}
