package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.AccRTMonitorService;
import com.zkteco.zkbiosecurity.ivideo.service.IVideoOperateAccDoorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class IVideoOperateAccDoorServiceImpl implements IVideoOperateAccDoorService {
    @Autowired
    private AccRTMonitorService accRTMonitorService;
    @Override
    public void operateDoor(String opType, String tempInterval, String doorId) {
        accRTMonitorService.operateDoor(opType, tempInterval, doorId);
    }
}
