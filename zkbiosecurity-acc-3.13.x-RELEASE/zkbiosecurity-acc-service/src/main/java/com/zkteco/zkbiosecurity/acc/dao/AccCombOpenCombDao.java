/**
 * File Name: AccCombOpenComb
 * Created by GenerationTools on 2018-03-14 下午03:11
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import com.zkteco.zkbiosecurity.acc.model.AccCombOpenComb;

import java.util.List;

/**
 * 对应百傲瑞达 AccCombOpenCombDao
 * <AUTHOR>
 * @date:	2018-03-14 下午03:11
 * @version v1.0
 */
public interface AccCombOpenCombDao extends BaseDao<AccCombOpenComb, String> {

    //accCombOpenPerson.id
    List<AccCombOpenComb> findByAccCombOpenPerson_IdIn(List<String> combOpenPersonId);

    //accCombOpenDoor.id
    List<AccCombOpenComb> findByAccCombOpenDoor_Id(String combOpenDoorId);

    List<AccCombOpenComb> findByAccCombOpenPerson_Id(String personId);
}