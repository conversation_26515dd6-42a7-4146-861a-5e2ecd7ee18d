package com.zkteco.zkbiosecurity.acc.service.impl;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.service.Acc4PersDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.vo.Acc4PersDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceConditionItem;
import com.zkteco.zkbiosecurity.acc.vo.AccSelectDeviceItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
 * @date $date$ $time$
 * $params$
 * @return $returns$
 */
@Service
@Transactional
public class Acc4PersDeviceServiceImpl implements Acc4PersDeviceService {
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccLevelService accLevelService;

    @Override
    public List<Acc4PersDeviceItem> getItemsByPage(AccDeviceConditionItem condition, int page, int size) {
        AccSelectDeviceItem accSelectDeviceItem = new AccSelectDeviceItem();
        ModelUtil.copyPropertiesIgnoreNull(condition,accSelectDeviceItem);
        List<Acc4PersDeviceItem> acc4PersDeviceItemList = Lists.newArrayList();
        Pager pager = accDeviceService.getItemsByPage(accSelectDeviceItem, page, size);
        if (pager.getData() != null) {
            List<AccSelectDeviceItem> accSelectDeviceItemList = (List<AccSelectDeviceItem>) accDeviceService.getItemsByPage(accSelectDeviceItem, page, size).getData();
            acc4PersDeviceItemList = ModelUtil.copyListProperties(accSelectDeviceItemList, Acc4PersDeviceItem.class);
        }
        return acc4PersDeviceItemList;
    }

    @Override
    public String getMasterLevelId() {
        return accLevelService.getMasterLevel().getId();
    }
}
