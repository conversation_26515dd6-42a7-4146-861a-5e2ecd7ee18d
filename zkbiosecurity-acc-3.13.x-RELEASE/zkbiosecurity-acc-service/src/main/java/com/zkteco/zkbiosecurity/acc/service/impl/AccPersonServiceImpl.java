/**
 * File Name: AccPersonServiceImpl Created by GenerationTools on 2018-03-02 下午02:10 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import java.io.File;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.constants.AccCacheKeyConstants;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.*;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.model.AccPerson;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonService;
import com.zkteco.zkbiosecurity.acc.utils.AccDataUtil;
import com.zkteco.zkbiosecurity.acc.utils.AccDeviceUtil;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.auth.provider.AuthSessionProvider;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.utils.Base64Util;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.*;
import com.zkteco.zkbiosecurity.pers.vo.*;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

import lombok.extern.slf4j.Slf4j;

/**
 * 对应百傲瑞达 AccPersonServiceImpl
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-03-02 下午02:10
 */
@Service
@Transactional
@Slf4j
public class AccPersonServiceImpl implements AccPersonService {
    @Autowired
    private AccPersonDao accPersonDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private PersBioTemplateService persBioTemplateService;
    @Autowired
    private PersCardService persCardService;
    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private AccLevelPersonDao accLevelPersonDao;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AuthSessionProvider authSessionProvider;
    @Autowired
    private PersIdentityCardInfoService persIdentityCardInfoService;
    @Autowired
    private AccLevelDoorDao accLevelDoorDao;
    @Autowired
    private Pers2OtherService pers2OtherService;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private PersBioPhotoService persBioPhotoService;
    @Autowired
    private AccPersonFirstOpenDao accPersonFirstOpenDao;

    /**
     * best协议和push协议人员相关字段兼容 pro拓展
     **/
    private static final Map<String, String> MQTT_PUSH_PARAM = new HashMap<>();

    static {
        // mqtt 参数转 push
        MQTT_PUSH_PARAM.put("pin", "pin");
        MQTT_PUSH_PARAM.put("passwd", "password");
        MQTT_PUSH_PARAM.put("group", "group");
        MQTT_PUSH_PARAM.put("idNum", "cardno");
        MQTT_PUSH_PARAM.put("firstName", "name");
        MQTT_PUSH_PARAM.put("lastName", "lastName");
        MQTT_PUSH_PARAM.put("funSwitch", "funswitch");
        MQTT_PUSH_PARAM.put("disable", "disable");
        // push没有的
        MQTT_PUSH_PARAM.put("startTime", "starttime");
        MQTT_PUSH_PARAM.put("endTime", "endtime");
        MQTT_PUSH_PARAM.put("organizationNum", "organizationNum");
        MQTT_PUSH_PARAM.put("threatLevel", "threatLevel");
        // 多卡
        MQTT_PUSH_PARAM.put("card", "cardno");
        MQTT_PUSH_PARAM.put("type", "cardtype");
        MQTT_PUSH_PARAM.put("lossFlag", "lossFlag");
    }

    @Override
    public AccPersonItem saveItem(AccPersonItem item) {
        // AccPerson accPerson = Optional.ofNullable(item)
        // .map(i->i.getId())
        // .filter(StringUtils::isNotBlank)
        // .flatMap(id->accPersonDao.findById(id))
        // .orElse(new AccPerson());
        AccPerson accPerson = null;
        if (StringUtils.isNotBlank(item.getPersonId())) {
            accPerson = accPersonDao.findByPersonId(item.getPersonId());
        }
        if (accPerson == null) {
            accPerson = new AccPerson();
        }
        ModelUtil.copyPropertiesIgnoreNull(item, accPerson);
        if (item.getIsSetValidTime() == null) {
            accPerson.setIsSetValidTime(false);
            accPerson.setStartTime(null);
            accPerson.setEndTime(null);
        } else {
            accPerson.setIsSetValidTime(true);
            accPerson.setStartTime(item.getStartTime());
            accPerson.setEndTime(item.getEndTime());
        }
        if (item.getDelayPassage() == null) {
            accPerson.setDelayPassage(false);
        }
        accPersonDao.save(accPerson);
        item.setId(accPerson.getId());
        return item;
    }

    @Override
    public List<AccPersonItem> getByCondition(AccPersonItem condition) {
        return (List<AccPersonItem>)accPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        Pager pager = accPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        return pager;
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accPersonDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AccPersonItem getItemById(String id) {
        List<AccPersonItem> items = getByCondition(new AccPersonItem(id));
        return Optional.ofNullable(items).filter(i -> !i.isEmpty()).map(i -> i.get(0)).orElse(null);
    }

    @Override
    public AccPersonItem getItemByPersonId(String personId) {
        return Optional.ofNullable(personId).filter(StringUtils::isNotBlank).map(accPersonDao::findByPersonId)
            .filter(Objects::nonNull).map(accPerson -> ModelUtil.copyProperties(accPerson, new AccPersonItem()))
            .orElse(null);
    }

    @Override
    public String getPersonIdsByDeptIds(String deptIds) {
        String personIds = "";
        StringBuilder personIdsBuilder = new StringBuilder();
        if (StringUtils.isNotBlank(deptIds)) {
            List<List<String>> deptIdsList =
                CollectionUtil.split(CollectionUtil.strToList(deptIds), CollectionUtil.splitSize);
            for (List<String> deptIdList : deptIdsList) {
                List<String> personIdList = persPersonService.getEnabledCredentialPersonIdsByDeptIds(deptIdList);
                if (personIdList != null && personIdList.size() > 0) {
                    personIdsBuilder.append(StringUtils.join(personIdList, ",")).append(",");
                }
            }
        }
        if (personIdsBuilder.length() > 0) {
            personIds = personIdsBuilder.substring(0, personIdsBuilder.length() - 1);
        }
        return personIds;
    }

    @Override
    public long getPersonCountByDept(String deptIds) {
        long persPersonCount = 0;
        if (StringUtils.isNotBlank(deptIds)) {
            List<List<String>> deptIdsList =
                CollectionUtil.split(CollectionUtil.strToList(deptIds), CollectionUtil.splitSize);
            for (List<String> deptIdList : deptIdsList) {
                persPersonCount += persPersonService.getPersonCountByDeptIds(StringUtils.join(deptIdList, ","));
            }
        }
        return persPersonCount;
    }

    @Override
    public Map<String, AccSelectPersonItem> getPersonItemMap(Collection<? extends BaseItem> condition) {
        List<String> personIdList = (List<String>)CollectionUtil.getItemIdsList(condition); // 获取人员ID的集合
        List<PersPersonItem> persPersonItemList = persPersonService.getItemsByIds(personIdList);
        List<AccSelectPersonItem> accSelectPersonItemList =
            new ArrayList<AccSelectPersonItem>(persPersonItemList.size());
        persPersonItemList.stream().forEach(personItem -> {
            AccSelectPersonItem accSelectPersonItem = new AccSelectPersonItem();
            accSelectPersonItem.setPersonId(personItem.getId());
            accSelectPersonItem.setPersonPin(personItem.getPin());
            accSelectPersonItem.setPersonName(personItem.getName());
            accSelectPersonItem.setPersonLastName(personItem.getLastName());
            accSelectPersonItem.setDeptName(personItem.getDeptName());
            //accSelectPersonItem.setCardNo(personItem.getCardNos());
            accSelectPersonItem.setGender(personItem.getGender());
            accSelectPersonItemList.add(accSelectPersonItem);
        });
        Map<String, AccSelectPersonItem> accSelectPersonItemMap =
            CollectionUtil.listToKeyMap(accSelectPersonItemList, AccSelectPersonItem::getPersonId);
        return accSelectPersonItemMap;
    }

    @Override
    public Pager getNoExistPerson(String sessionId, AccSelectPersonItem condition, int pageNo, int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        condition.setEnabledCredential(true);
        return getItemsByPage(condition, pageNo, pageSize);
    }

    @Override
    public List<AccPersonItem> getItemsByIds(List<String> idList) {
        List<AccPersonItem> accPersonItemList = new ArrayList<>();
        List<AccPerson> accPersonList = accPersonDao.findByIdIn(idList);
        accPersonItemList = ModelUtil.copyListProperties(accPersonList, AccPersonItem.class);
        return accPersonItemList;
    }

    @Override
    public String getIdByPersonId(String persPersonId) {
        return Optional.ofNullable(persPersonId).filter(StringUtils::isNotBlank)
            .map(pid -> accPersonDao.getIdsByPersonId(CollectionUtil.strToList(pid))).filter(l -> !l.isEmpty())
            .map(l -> l.get(0)).orElse("");
    }

    @Override
    public Pager getDoorPerson(AccTransactionDoorPersonItem condition, int pageNo, int pageSize) {
        List<String> personIdList = accLevelDoorDao.getPersonIdsByDoorId(condition.getDoorId());
        Pager pager = new Pager();
        List<AccTransactionDoorPersonItem> accTransactionDoorPersonItemList = new ArrayList<>();
        if (Objects.nonNull(personIdList) && personIdList.size() > 0) {
            List<PersPersonItem> persPersonItems = persPersonService.getItemsByIds(personIdList);
            for (PersPersonItem personItem : persPersonItems) {
                AccTransactionDoorPersonItem item = new AccTransactionDoorPersonItem();
                item.setId(personItem.getId());
                item.setDeptName(personItem.getDeptName());
                item.setName(personItem.getName());
                item.setLastName(personItem.getLastName());
                item.setPin(personItem.getPin());
                accTransactionDoorPersonItemList.add(item);
            }
        }
        pager.setData(accTransactionDoorPersonItemList);
        pager.setTotal(accTransactionDoorPersonItemList.size());
        pager.setSize(pageSize);
        pager.setPage(pageNo);
        return pager;
    }

    @Override
    public Pager getAccTransactionPerson(String sessionId, AccTransactionPersonItem condition, int pageNo,
        int pageSize) {
        PersPersonItem persPersonItem = new PersPersonItem();
        persPersonItem.setPin(condition.getPin());
        persPersonItem.setName(condition.getName());
        persPersonItem.setDeptId(condition.getDeptId());
        persPersonItem.setDeptName(condition.getDeptName());
        persPersonItem.setSortName(condition.getSortName());
        persPersonItem.setSortOrder(condition.getSortOrder());
        persPersonItem.setLikeName(condition.getLikeName());
        Pager pager = persPersonService.loadPagerByAuthUserFilter(sessionId, persPersonItem, pageNo, pageSize);
        List<PersPersonItem> persPersonItemList = (List<PersPersonItem>)pager.getData();
        List<AccTransactionPersonItem> accPersonItemList = new ArrayList<>();
        AccTransactionPersonItem accTransactionPersonItem = null;
        for (PersPersonItem personItem : persPersonItemList) {
            accTransactionPersonItem = new AccTransactionPersonItem();
            accTransactionPersonItem.setId(personItem.getId());
            accTransactionPersonItem.setPersPersonId(personItem.getId());
            accTransactionPersonItem.setPin(personItem.getPin());
            accTransactionPersonItem.setName(personItem.getName());
            accTransactionPersonItem.setLastName(personItem.getLastName());
            accTransactionPersonItem.setDeptId(personItem.getDeptId());
            accTransactionPersonItem.setDeptName(personItem.getDeptName());

            accPersonItemList.add(accTransactionPersonItem);
        }
        pager.setData(accPersonItemList);

        return pager;
    }

    @Override
    public List<AccPersonItem> getItemsByPersPersonIds(String persPersonIds) {
        List<PersPersonItem> persPersonItemList = persPersonService.getItemsByIds(persPersonIds);
        AccPersonItem accPersonItem = new AccPersonItem();
        accPersonItem.setInPersonId(persPersonIds);
        List<AccPersonItem> accPersonItemList = getByCondition(accPersonItem);
        Map<String, PersPersonItem> persPersonItemMap = CollectionUtil.itemListToIdMap(persPersonItemList);
        accPersonItemList.stream().forEach(item -> {
            PersPersonItem persPersonItem = persPersonItemMap.get(item.getPersonId());
            item.setPersonPin(persPersonItem.getPin());
            item.setPersonCardNo(
                StringUtils.isNotBlank(persPersonItem.getCardNos()) ? persPersonItem.getCardNos() : "");
            item.setPersonPassword(
                StringUtils.isNotBlank(persPersonItem.getPersonPwd()) ? persPersonItem.getPersonPwd() : "");
        });
        return accPersonItemList;
    }

    @Override
    public List<AccPersonOptItem> getPersonPinAndPersonIdByParam(String param, List<Object> personParamList) {
        // List<PersPersonItem> personPinAndPersonIdList = new ArrayList<PersPersonItem>();
        List<AccPersonOptItem> personInfoList = new ArrayList<AccPersonOptItem>();
        List<List<Object>> personParamListStr = CollectionUtil.split(personParamList, AccConstants.LEVEL_SPLIT_COUNT);
        personParamListStr.stream().forEach(personParams -> { // 解决查询数据库参数过多的问题
            String personParam = StringUtils.join(personParams, ",");
            List<PersPersonItem> persPersonItemList = null;
            if (param.equals("pin")) {
                persPersonItemList = persPersonService.getItemsByPins(personParam);
            } else {
                persPersonItemList = persPersonService.getItemsByIds(personParam);
            }
            // List<Object[]> partPersonPinAndPersonIdList = dao.getPersonPinAndPersonIdByParam(param, personParam);
            // personPinAndPersonIdList.addAll(persPersonItemList);
            persPersonItemList.forEach(personPinAndPersonId -> {
                AccPersonOptItem personBean = new AccPersonOptItem();
                personBean.setId(personPinAndPersonId.getId());
                personBean.setPin(personPinAndPersonId.getPin());
                personInfoList.add(personBean);
            });
        });
        return personInfoList;
    }

    @Override
    public List<AccTransactionDoorPersonItem> getDoorPersonItemData(AccTransactionDoorPersonItem condition,
        int beginIndex, int endIndex) {
        return accLevelPersonDao.getItemsDataBySql(AccTransactionDoorPersonItem.class, SQLUtil.getSqlByItem(condition),
            beginIndex, endIndex, true);
    }

    @Override
    public List<AccPersonLevelByLevelExportItem>
        getExportLevelPersonItemList(AccPersonLevelByLevelExportItem accLevelPersonItem, int beginIndex, int endIndex) {
        return accPersonDao.getItemsDataBySql(AccPersonLevelByLevelExportItem.class,
            SQLUtil.getSqlByItem(accLevelPersonItem), beginIndex, endIndex, true);
    }

    @Override
    public Pager getLevelPersonItemsByAuthFilter(String sessionId, AccPersonForLevelItem condition, int pageNo,
        int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        Pager pager =
            accLevelPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        List<AccPersonForLevelItem> items = (List<AccPersonForLevelItem>)pager.getData();
        for (AccPersonForLevelItem item : items) {
            item.setPersonPin(item.getPersonPin());
        }
        return pager;
    }

    /**
     * @param table
     * @param data
     * @return
     * @Description: 解析pull设备上传数据
     * <AUTHOR>
     * @date 2018/6/13 11:51
     */
    private Map<String, Map<String, String>> getDataMapByPull(String table, String data) {
        Map<String, Map<String, String>> retMap = new HashMap<>();
        String[] recordArr = StringUtils.split(data, "\r\n"); // 分割每一条数据 [Pin,FunSwitch, 2765,0, 111,0, 112,0]
        // 表示为表头
        List<String> headList = new ArrayList<>();
        String[] headArray = StringUtils.split(recordArr[0], ",");
        for (String head : headArray) {
            headList.add(head.toLowerCase());
        }
        String[] infoArr = null;
        Map<String, String> tempMap = null;
        String cardHex = baseSysParamService.getValByName("pers.cardHex");
        for (int i = 1, len = recordArr.length; i < len; i++) {
            // 表示数据
            infoArr = recordArr[i].split(",");// StringUtils.split(, ",");
            tempMap = new HashMap<String, String>();
            for (int j = 0, innerLen = infoArr.length; j < innerLen; j++) {
                if ("name".equals(headList.get(j).toLowerCase())) {
                    if (infoArr[j].contains("#")) {
                        tempMap.put(headList.get(j), infoArr[j].split("#")[0]);
                        tempMap.put("lastName", infoArr[j].split("#")[1]);
                    } else {
                        tempMap.put(headList.get(j), infoArr[j]);
                    }
                } else if ("cardno".equals(headList.get(j).toLowerCase()) && StringUtils.isNotBlank(infoArr[j])
                    && cardHex.equals("1")) {
                    tempMap.put(headList.get(j), AccDeviceUtil.convertCardNo(infoArr[j], 10, 16));
                } else {
                    tempMap.put(headList.get(j), infoArr[j]);
                }
            }
            if (!(tempMap.get("pin").trim().length() == 9 && tempMap.get("pin").trim().startsWith("8"))) {
                Map<String, String> getMap = getExtendMap(table, tempMap);
                retMap.put(getMap.get("compareKey"), getMap);
            }
        }
        return retMap;
    }

    /**
     * @param table
     * @param data
     * @return
     * @Description: 解析push设备上传数据
     * <AUTHOR>
     * @date 2018/6/13 11:50
     */
    private Map<String, Map<String, String>> getDataMapByPush(String table, String data) {
        Map<String, Map<String, String>> retMap = new HashMap<>();
        String[] personDataArray = StringUtils.split(data.trim(), "\r\n");
        String[] fieldArr = null;
        Map<String, String> fieldMap = null;
        String[] keyValArr = null;
        String cardHex = baseSysParamService.getValByName("pers.cardHex");
        for (String record : personDataArray) {
            fieldArr = StringUtils.split(record.split(table)[1].trim(), "\t");
            fieldMap = new HashMap<>();
            for (int i = 0, len = fieldArr.length; i < len; i++) {
                keyValArr = fieldArr[i].trim().split("=", 2);
                if ("name".equals(keyValArr[0].toLowerCase())) {
                    if (keyValArr[1].contains("#")) {
                        fieldMap.put(keyValArr[0], keyValArr[1].split("#")[0]);
                        fieldMap.put("lastName", keyValArr[1].split("#")[1]);
                    } else {
                        fieldMap.put(keyValArr[0], keyValArr[1]);
                    }
                } else if ("cardno".equals(keyValArr[0].toLowerCase())) {
                    String cardNo = keyValArr[1];
                    if (StringUtils.isNotBlank(cardNo) && table.equals(AccConstants.MUL_CARD_USER)) {// 由于多卡表本身存储的是16进制，先转化为10进制才能根据系统设置来判断
                        cardNo = AccDeviceUtil.convertCardNo(cardNo, 16, 10);
                    }
                    if (StringUtils.isNotBlank(cardNo) && cardHex.equals("1")) {
                        fieldMap.put(keyValArr[0], AccDeviceUtil.convertCardNo(cardNo, 10, 16));
                    } else {
                        fieldMap.put(keyValArr[0], cardNo);
                    }
                } else {
                    fieldMap.put(keyValArr[0], keyValArr[1]);
                }
            }
            // 过滤访客人员，访客pin号为8开头的9位数字
            if (!(fieldMap.get("pin").trim().length() == 9 && fieldMap.get("pin").trim().startsWith("8"))) {
                Map<String, String> getMap = getExtendMap(table, fieldMap);
                if (getMap.containsKey("compareKey")) {
                    retMap.put(getMap.get("compareKey"), getMap);
                }
            }
        }
        return retMap;
    }

    /**
     * @param table
     * @param map
     * @return
     * @Description: pull和push公共扩展的字段 组装数据，将数据组装到map中
     * <AUTHOR>
     * @date 2018/6/13 13:35
     */
    private Map<String, String> getExtendMap(String table, Map<String, String> map) {
        StringBuffer compareKey = new StringBuffer();
        String pin = map.get("pin");
        String personPwd = map.get("password");
        if (table.equals(ConstUtil.TEMPLATEV10)) {// 指纹
            map.put("version", String.valueOf(BaseConstants.BaseBioType.FP_BIO_VERSION));
            map.put("bioType", String.valueOf(BaseConstants.BaseBioType.FP_BIO_TYPE));
            map.put("compareKey", compareKey.append(pin).append("_").append(map.get("fingerid"))
                .append("_" + BaseConstants.BaseBioType.FP_BIO_TYPE).toString());// pin号_模版编号_模版类型
        } else if (table.equals(ConstUtil.USER)) {
            map.put("status", String.valueOf(PersConstants.PERSON_NORMAL));
            map.put("compareKey", compareKey.append(pin).toString());
            map.remove("password");
            map.put("personPwd", personPwd);
        } else if (table.equals(ConstUtil.FACEV7)) {
            map.put("version", String.valueOf(BaseConstants.BaseBioType.FACE_BIO_VERSION));
            map.put("bioType", String.valueOf(BaseConstants.BaseBioType.FACE_BIO_TYPE));
            map.put("compareKey", compareKey.append(pin).append("_").append(map.get("faceid"))
                .append("_" + BaseConstants.BaseBioType.FACE_BIO_TYPE).toString());
        }
        // leo 20141010 新增处理人脸模板
        else if (table.equals("face")) {
            map.put("version", String.valueOf(BaseConstants.BaseBioType.FACE_BIO_VERSION));
            map.put("bioType", String.valueOf(BaseConstants.BaseBioType.FACE_BIO_TYPE));
            map.put("compareKey", compareKey.append(pin).append("_").append(map.get("fid"))
                .append("_" + BaseConstants.BaseBioType.FACE_BIO_TYPE).toString());
        } else if (table.equals("extuser")) {
            map.put("compareKey", compareKey.append(pin).toString());
        } else if (table.equals("mulcarduser")) {
            map.put("compareKey", compareKey.append(pin).append("_").append(map.get("cardno")).toString());
        } else if (table.equals("biodata")) {
            String version = map.get("majorver");
            if (!"0".equals(map.get("minorver"))) {
                version += "." + map.get("minorver");
            }
            if (map.get("type").equals(String.valueOf(BaseConstants.BaseBioType.PALM_BIO_TYPE))) {
                map.put("version", version);
                map.put("bioType", String.valueOf(BaseConstants.BaseBioType.PALM_BIO_TYPE));
                map.put("compareKey", compareKey.append(pin).append("_").append(map.get("index"))
                    .append("_" + BaseConstants.BaseBioType.PALM_BIO_TYPE).toString());// pin号_模版编号_模版类型
            } else if (map.get("type").equals(String.valueOf(BaseConstants.BaseBioType.FP_BIO_TYPE))) {
                map.put("version", version);// 指纹算法12.0
                map.put("bioType", String.valueOf(BaseConstants.BaseBioType.FP_BIO_TYPE));
                map.put("compareKey", compareKey.append(pin).append("_").append(map.get("no"))
                    .append("_" + BaseConstants.BaseBioType.FP_BIO_TYPE).toString());// pin号_模版编号_模版类型
            } else if (map.get("type").equals(String.valueOf(BaseConstants.BaseBioType.FACE_BIO_TYPE))) {
                map.put("version", version);// 人脸算法12.0
                map.put("bioType", String.valueOf(BaseConstants.BaseBioType.FACE_BIO_TYPE));
                map.put("compareKey", compareKey.append(pin).append("_").append(map.get("index"))
                    .append("_" + BaseConstants.BaseBioType.FACE_BIO_TYPE).toString());// pin号_模版编号_模版类型
            } else if (map.get("type").equals(String.valueOf(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE))) {
                map.put("version", version);
                map.put("bioType", String.valueOf(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE));
                map.put("compareKey", compareKey.append(pin).append("_").append(map.get("index"))
                    .append("_" + BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE).toString());// pin号_模版编号_模版类型
            }
        }
        return map;
    }

    @Override
    public Map<String, Integer> dealPersonInfo(String table, String data, String cmdId, String sn) {
        Map<String, Integer> retMap = new HashMap<>();
        Map<String, Map<String, String>> updateData = new HashMap<>();
        Map<String, Map<String, String>> insertData = new HashMap<>();
        List<String> conflictCardList = new ArrayList<>();// 和软件中卡号有冲突(重复)
        Map<String, Map<String, String>> resolveMap = getDataMapFromDevUpload(table, data, sn);
        int count = resolveMap.size();
        List<String> cardNoList = Lists.newArrayList();
        for (String key : resolveMap.keySet()) {
            Map<String, String> dataMap = resolveMap.get(key);
            String cardNo = dataMap.get("cardno");
            if (StringUtils.isNotBlank(cardNo) && !cardNoList.contains(cardNo)) {
                cardNoList.add(cardNo);
            }
        }
        List<PersCardItem> persCardItemList = persCardService.getMasterCardByCardNos(cardNoList);
        List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(resolveMap.keySet());// 获取软件中人员信息
        List<PersCardItem> cardSetList = persCardService
            .getMasterCardByPersonIdList((List<String>)CollectionUtil.getItemIdsList(persPersonItemList));
        Map<String, PersPersonItem> personItemMap =
            Maps.newHashMap(CollectionUtil.listToKeyMap(persPersonItemList, PersPersonItem::getPin));// 获取人员对象集合
        Map<String, PersCardItem> persCardItemMap =
            Maps.newHashMap(CollectionUtil.listToKeyMap(persCardItemList, PersCardItem::getCardNo));
        Map<String, PersCardItem> cardSetMap =
            Maps.newHashMap(CollectionUtil.listToKeyMap(cardSetList, PersCardItem::getPersonPin));
        for (String pin : resolveMap.keySet()) {
            Map<String, String> personMap = resolveMap.get(pin);
            if (Objects.nonNull(personMap)) {
                PersPersonItem tempPerson = null;
                boolean updateFlag = true;// true：更新；false：插入
                if (personItemMap.containsKey(pin)) {
                    tempPerson = personItemMap.get(pin);
                    if (checkPersonRecordIsChange(persCardItemMap, personMap, tempPerson)) {
                        // 如果检测到上传用户数据跟软件数据一致就不要进行以下判断，主要是解决多线程情况下，相同数据更新和查询造成死锁问题 add by max 20160525
                        continue;
                    }
                }
                if (Objects.isNull(tempPerson)) { // pin号在数据库中不存在
                    updateFlag = false;
                }
                if (personMap.containsKey("cardno") && StringUtils.isNotBlank(personMap.get("cardno"))) {
                    PersCardItem persCard = null;
                    String cardNo = personMap.get("cardno");

                    if (persCardItemMap.containsKey(cardNo)) {
                        persCard = persCardItemMap.get(cardNo);
                    }
                    // 重复的卡号关联的人不能和当前获取的人是同一个人；防止重复获取人员信息时，更新人员时，会提示卡号重复问题
                    if (Objects.nonNull(persCard)
                        && (Objects.isNull(tempPerson) ? true : !persCard.getPersonPin().equals(tempPerson.getPin()))) {// 卡号存在，异常
                        // 加标记
                        personMap.put("exceptionFlag", String.valueOf(PersConstants.PERSON_ISEXCEPTION));// 卡号重复
                        if (!conflictCardList.contains(cardNo)) {
                            conflictCardList.add(cardNo);
                        }
                    }
                }
                // 当密码的长度超过8位的时候，密码异常
                if (personMap.containsKey("personPwd") && personMap.get("personPwd").length() > 8) {
                    personMap.put("exceptionFlag", String.valueOf(PersConstants.PERSON_PWD_EXCEPTION));// 密码异常
                }
                if (!personMap.containsKey("exceptionFlag")) {
                    personMap.put("exceptionFlag", "0");// 正常
                }
                resolveMap.put(pin, personMap);// 将修改的数据重新放到map中
                if (updateFlag) {
                    updateData.put(pin, resolveMap.get(pin));
                } else {
                    insertData.put(pin, resolveMap.get(pin));
                    // insertPin.append(",'").append(personPin).append("'");
                }
            }
            if (conflictCardList.size() > 0 && !"-1".equals(cmdId)) {
                setConflictCardToCache(conflictCardList, cmdId);
            }
        }
        if (!conflictCardList.isEmpty()) {
            // 保存卡号重复数据到redis
            setConflictCardToCache(conflictCardList, cmdId);
        }
        retMap.put("count", count);
        retMap.put("insert", insertData.size());
        retMap.put("update", updateData.size());
        // pers_person插入和更新操作
        persPersonOper(insertData, updateData, personItemMap, sn);
        // pers_card插入和更新
        persCard(insertData, updateData, personItemMap, cardSetMap);
        // boolean supportTime = accDeviceOptionService.isSupportFun(sn, "DateFmtFunOn");
        // accPerson(insertData, updateData, supportTime, personItemMap);
        return retMap;
    }

    private void accPerson(Map<String, Map<String, String>> insertData, Map<String, Map<String, String>> updateData,
        boolean supportTime, Map<String, PersPersonItem> personItemMap) {
        // 1.插入
        if (insertData != null && insertData.size() > 0) {
            Map<String, Map<String, String>> accData = getInsertAccPerson(insertData, supportTime);
            List<AccPerson> accPersonList = buildAccPersonData(accData, personItemMap);
            accPersonDao.save(accPersonList);
        }
        // 2.更新 acc_person
        if (updateData != null && updateData.size() > 0) {
            Map<String, Map<String, String>> updateAccData = getUpdateAccPerson(updateData, supportTime);
            updateAccPerson(updateAccData, personItemMap);
        }
    }

    private void updateAccPerson(Map<String, Map<String, String>> accPersonMap,
        Map<String, PersPersonItem> personItemMap) {
        List<AccPerson> accPersonList = Lists.newArrayList();
        Set<String> keySet = accPersonMap.keySet();
        Map<String, AccPerson> accAllPersonMap = getAccPersonByPin(personItemMap.keySet());
        for (String key : keySet) {
            Map<String, String> fieldMap = accPersonMap.get(key);
            if (fieldMap.containsKey("id")) { // 保存的是pin
                PersPersonItem persPerson = null;
                if (Objects.nonNull(personItemMap.get(key))) {
                    persPerson = personItemMap.get(key);
                }
                if (Objects.nonNull(persPerson)) { // 人事中有该人
                    AccPerson accPerson = accAllPersonMap.get(persPerson.getId());
                    // boolean combopenPersonFlag = true;//是否需要修改combopenPersonId字段(新增不需要，编辑需要)
                    if (Objects.isNull(accPerson)) { // 人事中有该人，门禁中没有该人
                        accPerson = new AccPerson();
                        // accPerson.setId(persPerson.getId());
                        accPerson.setPersonId(persPerson.getId());
                        accPerson.setSuperAuth((short)0);
                        // accPerson.setIsObeyGapb(false);
                        // combopenPersonFlag = false;
                    }
                    if (fieldMap.containsKey("validTime")) {
                        accPerson.setIsSetValidTime(fieldMap.get("validTime").equals("1"));
                    }
                    if (fieldMap.containsKey("superAuth") && StringUtils.isNotBlank(fieldMap.get("superAuth"))) {
                        accPerson.setSuperAuth(Short.parseShort(fieldMap.get("superAuth")));
                    }
                    // if (fieldMap.containsKey("combopenPersonId") &&
                    // StringUtils.isNotBlank(fieldMap.get("combopenPersonId"))) {
                    // AccCombOpenPerson accCombOpenPerson =
                    // accCombOpenPersonDao.findById(fieldMap.get("combopenPersonId")).orElse(null);
                    // if (Objects.nonNull(accCombOpenPerson))
                    // {
                    // accPerson.setAccCombOpenPerson(accCombOpenPerson);
                    // }
                    // }
                    if (fieldMap.containsKey("startTime")) {
                        accPerson.setStartTime(StringUtils.isNotBlank(fieldMap.get("startTime"))
                            ? DateUtil.stringToDate(fieldMap.get("startTime"), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS)
                            : null);
                    }
                    if (fieldMap.containsKey("endTime")) {
                        accPerson.setStartTime(StringUtils.isNotBlank(fieldMap.get("endTime"))
                            ? DateUtil.stringToDate(fieldMap.get("endTime"), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS)
                            : null);
                    }
                    if (fieldMap.containsKey("privilege")) {
                        accPerson.setPrivilege(Short.parseShort(fieldMap.get("privilege")));
                    }
                    if (fieldMap.containsKey("disable")) {
                        accPerson.setDisabled(fieldMap.get("disable").equals("1"));
                    }
                    if (Objects.isNull(accPerson.getDelayPassage())) {
                        accPerson.setDelayPassage(false);// 等待获取extuser表时再次更新该字段
                    }
                    accPersonList.add(accPerson);
                }
            }
        }
        if (accPersonList.size() > 0) {
            accPersonDao.save(accPersonList);
        }
    }

    private Map<String, AccPerson> getAccPersonByPin(Set<String> pinSet) {
        List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(pinSet);
        List<String> personIds = (List<String>)CollectionUtil.getItemIdsList(persPersonItemList);
        List<AccPerson> accPersonList = accPersonDao.findByPersonIdIn(personIds);
        Map<String, AccPerson> accPersonMap = CollectionUtil.listToKeyMap(accPersonList, AccPerson::getPersonId);
        return accPersonMap;
    }

    private Map<String, Map<String, String>> getUpdateAccPerson(Map<String, Map<String, String>> updateData,
        boolean supportTime) {
        String startTime = null;
        String endTime = null;
        String isValidTime = null;
        Map<String, Map<String, String>> updateAccData = Maps.newHashMap();
        for (String tempKey : updateData.keySet()) {
            Map<String, String> curr = updateData.get(tempKey);
            Map<String, String> temp = new HashMap<String, String>();
            temp.put("id", curr.get("pin"));// 保存pin
            // temp.put("id", (pinMap.get(tempKey)).getId().toString());
            startTime = curr.get("starttime");
            endTime = curr.get("endtime");
            isValidTime = "1";
            if ((StringUtils.isBlank(startTime) || startTime.equals("0"))
                && (StringUtils.isBlank(endTime) || endTime.equals("0"))) {
                isValidTime = "0";
            }
            if (!"0".equals(startTime) && StringUtils.isNotBlank(startTime)) {
                startTime = supportTime
                    ? DateUtil.dateToString(AccDataUtil.transformTime(startTime),
                        DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS)
                    : DateUtil.dateToString(AccDataUtil.transformDate(startTime),
                        DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
            } else {
                startTime = null;
            }
            if (!"0".equals(endTime) && StringUtils.isNotBlank(endTime)) {
                endTime = supportTime
                    ? DateUtil.dateToString(AccDataUtil.transformTime(endTime), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS)
                    : DateUtil.dateToString(AccDataUtil.transformDate(endTime), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
            } else {
                endTime = null;
            }
            // temp.put("id", (pinMap.get(tempKey)).getId().toString());
            temp.put("validTime", isValidTime);
            temp.put("superAuth", curr.get("superauthorize"));
            temp.put("combopenPersonId", curr.containsKey("group") ? curr.get("group") : "0");// 多人开门权限组
            // 是否和多人开门中的id对应(group是否对应combopenPersonId)
            // temp.put("obeyGapb", "0");//是否重置反潜 0：不重置 1：重置--屏蔽 add by wenxin 20150215(固件没有上传)
            temp.put("startTime", startTime);
            temp.put("endTime", endTime);
            if (curr.containsKey("privilege")) {
                temp.put("privilege", curr.get("privilege"));
            }
            if (curr.get("disable") != null) {
                temp.put("disable", curr.get("disable"));
            } else {
                temp.put("disable", AccConstants.DISABLE.toString());// 一体机不支持黑名单 默认disabled=false
            }
            updateAccData.put(curr.get("pin"), temp);
        }
        return updateAccData;
    }

    private List<AccPerson> buildAccPersonData(Map<String, Map<String, String>> accPersonMap,
        Map<String, PersPersonItem> personItemMap) {
        List<AccPerson> accPersonList = Lists.newArrayList();
        Set<String> keySet = accPersonMap.keySet();
        for (String key : keySet) {
            Map<String, String> fieldMap = accPersonMap.get(key);
            AccPerson accPerson = new AccPerson();
            if (fieldMap.containsKey("id"))// 保存的是pin
            {
                PersPersonItem persPerson = null;
                if (Objects.nonNull(personItemMap.get(key))) {
                    persPerson = personItemMap.get(key);
                }
                if (Objects.nonNull(persPerson)) {
                    // accPerson.setId(persPerson.getId());
                    accPerson.setPersonId(persPerson.getId());
                    if (fieldMap.containsKey("validTime")) {
                        accPerson.setIsSetValidTime(fieldMap.get("validTime").equals("1"));
                    }
                    if (fieldMap.containsKey("superAuth") && StringUtils.isNotBlank(fieldMap.get("superAuth"))) {
                        // byte[16] 进行Base64之后的值 by juvenile add 20160711
                        if ("AAAAAAAAAAAAAAAAAAAAAA==".equals(fieldMap.get("superAuth"))) {
                            accPerson.setSuperAuth((short)0);
                        } else if ("//////////8AAAAAAAAAAA==".equals(fieldMap.get("superAuth"))) {
                            accPerson.setSuperAuth((short)15);
                        } else {
                            accPerson.setSuperAuth(Short.parseShort(fieldMap.get("superAuth")));
                        }
                    } else {
                        accPerson.setSuperAuth((short)0);
                    }
                    // if (fieldMap.containsKey("obeyGapb")) {
                    // accPerson.setIsObeyGapb(fieldMap.get("obeyGapb").equals("1"));
                    // }
                    if (fieldMap.containsKey("startTime")) {
                        accPerson.setStartTime(StringUtils.isNotBlank(fieldMap.get("startTime"))
                            ? DateUtil.stringToDate(fieldMap.get("startTime"), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS)
                            : null);
                    }
                    if (fieldMap.containsKey("endTime")) {
                        accPerson.setEndTime(StringUtils.isNotBlank(fieldMap.get("endTime"))
                            ? DateUtil.stringToDate(fieldMap.get("endTime"), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS)
                            : null);
                    }
                    if (fieldMap.containsKey("disable")) {
                        accPerson.setDisabled(fieldMap.get("disable").equals("1"));
                    } else {
                        accPerson.setDisabled(false);
                    }
                    if (fieldMap.containsKey("privilege")) {
                        accPerson.setPrivilege(Short.parseShort(fieldMap.get("privilege")));
                    } else {
                        accPerson.setPrivilege((short)0);
                    }
                    accPerson.setDelayPassage(false);// 等待获取extuser表时再次更新该字段
                    accPersonList.add(accPerson);
                }
            }
        }
        return accPersonList;
    }

    private Map<String, Map<String, String>> getInsertAccPerson(Map<String, Map<String, String>> insertData,
        boolean supportTime) {
        String startTime = null;
        String endTime = null;
        String isValidTime = null;
        Map<String, Map<String, String>> accData = new HashMap<>();
        for (String tempKey : insertData.keySet()) {
            Map<String, String> curr = insertData.get(tempKey);
            Map<String, String> temp = null;
            temp = new HashMap<>();
            startTime = curr.get("starttime");
            endTime = curr.get("endtime");
            isValidTime = "1";// 是否设置有效时间 0：没有设置 1：设置
            if ((StringUtils.isBlank(startTime) || startTime.equals("0"))
                && (StringUtils.isBlank(endTime) || endTime.equals("0"))) {
                isValidTime = "0";
            }
            if (!startTime.equals("0") && StringUtils.isNotBlank(startTime)) {
                // modified by max 20150707修复startTime为空bug
                startTime = supportTime
                    ? DateUtil.dateToString(AccDataUtil.transformTime(startTime),
                        DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS)
                    : DateUtil.dateToString(AccDataUtil.transformDate(startTime),
                        DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
            } else {
                startTime = null;
            }
            if (!endTime.equals("0") && StringUtils.isNotBlank(endTime)) {
                // modified by max 20150707修复endTime为空bug
                endTime = supportTime
                    ? DateUtil.dateToString(AccDataUtil.transformTime(endTime), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS)
                    : DateUtil.dateToString(AccDataUtil.transformDate(endTime), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
            } else {
                endTime = null;
            }
            temp.put("id", curr.get("pin"));// 保存pin
            temp.put("validTime", isValidTime);
            temp.put("superAuth", curr.get("superauthorize"));
            temp.put("combopenPersonId", curr.containsKey("group") ? curr.get("group") : "0");// 多人开门权限组
            // 是否和多人开门中的id对应(group是否对应combopenPersonId)
            temp.put("obeyGapb", "1");// 是否遵循全局反潜 0：不重置 1：重置
            temp.put("startTime", startTime);
            temp.put("endTime", endTime);
            if (curr.containsKey("privilege")) {
                temp.put("privilege", curr.get("privilege"));
            }
            if (curr.containsKey("disable")) {
                temp.put("disable", curr.get("disable"));
            } else {
                temp.put("disable", AccConstants.DISABLE.toString());// 一体机不支持黑名单 默认disabled=false
            }
            temp.put("delayPassage", "0");
            accData.put(curr.get("pin"), temp);
        }
        return accData;
    }

    /**
     * @param insertData
     * @param updateData
     * @param personItemMap
     * @param persCardItemMap
     * @return
     * @Description: 更新人员卡号
     * <AUTHOR>
     * @date 2018/6/29 9:41
     */
    private void persCard(Map<String, Map<String, String>> insertData, Map<String, Map<String, String>> updateData,
        Map<String, PersPersonItem> personItemMap, Map<String, PersCardItem> persCardItemMap) {
        // 1.插入
        if (insertData != null && insertData.size() > 0) {
            Set<String> userDataKey = insertData.keySet();
            Map<String, Map<String, String>> cardData = Maps.newHashMap();
            Map<String, String> curr = null;
            Map<String, String> temp = null;
            for (String tempKey : userDataKey) {
                curr = insertData.get(tempKey);
                // 添加curr.get("cardno")非空判断 add by max 20151116
                if (StringUtils.isNotBlank(curr.get("cardno")) && !curr.get("cardno").equals("0")) {
                    temp = getMap(curr, new String[] {"cardNo"}, new String[] {"cardno"});
                    temp.put("personId", curr.get("pin"));// 保存pin
                    temp.put("cardState", String.valueOf(PersConstants.CARD_VALID));
                    temp.put("cardType", curr.containsKey("cardType") ? String.valueOf(curr.get("cardType"))
                        : String.valueOf(PersConstants.MAIN_CARD));
                    temp.put("issueTime", DateUtil.dateToString(new Date(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                    if (curr.keySet().contains("exceptionFlag")) {
                        temp.put("exceptionFlag", curr.get("exceptionFlag"));
                    }
                    cardData.put(tempKey, temp);
                }
            }
            insertPersCardData(cardData, personItemMap);
        }
        // 2.更新
        if (updateData != null && updateData.size() > 0) {
            Map<String, Map<String, String>> updateCardData = Maps.newHashMap();
            Map<String, String> curr = null;
            Map<String, String> temp = null;
            for (String tempKey : updateData.keySet()) {
                curr = updateData.get(tempKey);
                temp = Maps.newHashMap();
                String currCardNo = curr.get("cardno");
                if (StringUtils.isNotBlank(currCardNo) && !currCardNo.equals("0")) {
                    temp.put("personId", curr.get("pin"));// 保存pin
                    temp.put("cardNo", currCardNo);
                    temp.put("cardState", String.valueOf(PersConstants.CARD_VALID));
                    updateCardData.put(tempKey, temp);
                }
            }
            // 更新卡表 更新条件 只更新启用的卡
            if (updateCardData.size() > 0) {
                updatePersCard(updateCardData, personItemMap, persCardItemMap);
            }
        }
    }

    private void updatePersCard(Map<String, Map<String, String>> persCardMap, Map<String, PersPersonItem> persPersonMap,
        Map<String, PersCardItem> cardSetMap) {
        List<PersCardItem> persCardItemList = Lists.newArrayList();
        for (String key : persCardMap.keySet()) {
            Map<String, String> fieldMap = persCardMap.get(key);
            PersCardItem persCard = null;
            if (fieldMap.containsKey("cardState") && fieldMap.containsKey("personId")) {
                // 根据pin号和卡状态 查找卡表数据对象
                if (cardSetMap.containsKey(fieldMap.get("personId"))) {
                    if (Objects.nonNull(cardSetMap.get(fieldMap.get("personId")))) {
                        persCard = cardSetMap.get(fieldMap.get("personId"));
                    }
                }
            }
            if (Objects.nonNull(persCard)) {// 编辑
                if (fieldMap.containsKey("cardNo")) {
                    persCard.setCardNo(fieldMap.get("cardNo"));
                }
                if (fieldMap.containsKey("cardState")) {
                    persCard.setCardState(Short.parseShort(fieldMap.get("cardState")));
                }
                if (fieldMap.containsKey("issueTime")) {
                    persCard.setIssueTime(Timestamp.valueOf(fieldMap.get("issueTime")));
                } else {
                    persCard.setIssueTime(DateUtil.getCurrentTime());
                }
            } else { // 新增
                persCard = new PersCardItem();
                if (fieldMap.containsKey("personId")) {
                    PersPersonItem persPerson = null;
                    if (Objects.nonNull(persPersonMap.get(fieldMap.get("personId")))) {
                        persPerson = persPersonMap.get(fieldMap.get("personId"));
                    }
                    persCard.setPersonId(persPerson.getId());
                    persCard.setPersonPin(persPerson.getPin());
                }
                if (fieldMap.containsKey("cardNo")) {
                    persCard.setCardNo(fieldMap.get("cardNo"));
                }
                if (fieldMap.containsKey("cardState")) {
                    persCard.setCardState(Short.parseShort(fieldMap.get("cardState")));
                }
                if (fieldMap.containsKey("cardType")) {
                    persCard.setCardType(Short.parseShort(fieldMap.get("cardType")));
                } else {
                    persCard.setCardType(PersConstants.MAIN_CARD);
                }
                if (fieldMap.containsKey("issueTime")) {
                    persCard.setIssueTime(Timestamp.valueOf(fieldMap.get("issueTime")));
                } else {
                    persCard.setIssueTime(DateUtil.getCurrentTime());
                }
            }
            persCardItemList.add(persCard);
        }
        persCardService.saveCards(persCardItemList);
    }

    private void insertPersCardData(Map<String, Map<String, String>> persCardMap,
        Map<String, PersPersonItem> personItemMap) {
        List<PersCardItem> persCardList = Lists.newArrayList();
        List<PersPersonItem> persPersonList = Lists.newArrayList();
        Set<String> keySet = persCardMap.keySet();
        for (String key : keySet) {
            Map<String, String> fieldMap = persCardMap.get(key);
            PersCardItem persCard = new PersCardItem();
            if (fieldMap.containsKey("personId")) {
                PersPersonItem persPerson = null;
                if (personItemMap.containsKey(fieldMap.get("personId"))) {
                    persPerson = personItemMap.get(fieldMap.get("personId"));
                    if (StringUtils.isNotBlank(fieldMap.get("exceptionFlag"))
                        && fieldMap.get("exceptionFlag").equals("1")) {
                        persPerson.setExceptionFlag(Short.parseShort(fieldMap.get("exceptionFlag")));// 卡号重复
                        persPersonList.add(persPerson);
                    }
                    persCard.setPersonPin(persPerson.getPin());
                    persCard.setPersonId(persPerson.getId());
                }
            }
            if (fieldMap.containsKey("cardNo")) {
                persCard.setCardNo(fieldMap.get("cardNo"));
            }
            if (fieldMap.containsKey("cardState")) {
                persCard.setCardState(Short.parseShort(fieldMap.get("cardState")));
            }
            if (fieldMap.containsKey("cardType")) {
                persCard.setCardType(Short.parseShort(fieldMap.get("cardType")));
            }
            if (fieldMap.containsKey("issueTime")) {
                persCard.setIssueTime(Timestamp.valueOf(fieldMap.get("issueTime")));
            } else {
                persCard.setIssueTime(DateUtil.getCurrentTime());
            }
            persCardList.add(persCard);
        }
        if (persPersonList.size() > 0) {
            persPersonService.batchSaveItem(persPersonList, ConstUtil.SYSTEM_MODULE_ACC);
        }
        persCardService.saveCards(persCardList);
    }

    /**
     * @param insertData
     * @param updateData
     * @param personItemMap
     * @return
     * @Description: 保存人员
     * <AUTHOR>
     * @date 2018/6/28 16:16
     */
    private void persPersonOper(Map<String, Map<String, String>> insertData,
        Map<String, Map<String, String>> updateData, Map<String, PersPersonItem> personItemMap, String sn) {
        // 1.插入
        if (!insertData.isEmpty()) {
            Map<String, Map<String, String>> data = getInsertPersPerson(insertData);
            List<PersPersonItem> persPersonList = buildPersonData(data, sn);
            // System.out.println("----------------------待入库数据："+persPersonList.size());
            if (persPersonList.size() > 0) {
                persPersonService.batchSaveItem(persPersonList, ConstUtil.SYSTEM_MODULE_ACC);// 批量保存人员信息
                for (PersPersonItem personItem : persPersonList) {
                    personItemMap.put(personItem.getPin(), personItem);// 用于人员添加卡号获取人员信息
                }
            }
        }
        // 2.更新
        if (!updateData.isEmpty()) {
            Map<String, Map<String, String>> updateUserData = getUpdatePersPerson(updateData);
            updatePersPerson(updateUserData, personItemMap);
        }
    }

    private void updatePersPerson(Map<String, Map<String, String>> updateUserData,
        Map<String, PersPersonItem> personItemMap) {
        List<PersPersonItem> personItemList = Lists.newArrayList();
        for (String pin : updateUserData.keySet()) {
            if (StringUtils.isNotBlank(pin)) {
                Map<String, String> fieldMap = updateUserData.get(pin);
                PersPersonItem persPerson = null;
                if (personItemMap.containsKey(pin)) {
                    persPerson = personItemMap.get(pin);
                }
                if (fieldMap.containsKey("status")) {
                    persPerson.setStatus(Short.parseShort(fieldMap.get("status")));
                }
                if (fieldMap.containsKey("name")) {
                    if (StringUtils.isNotBlank(fieldMap.get("name")) && StringUtils.isBlank(persPerson.getName())) {// 防止设备中的姓名将软件中的人员姓名覆盖掉
                        persPerson.setName(fieldMap.get("name"));
                    }
                }
                if (fieldMap.containsKey("lastName")) {
                    if (StringUtils.isNotBlank(fieldMap.get("lastName"))
                        && StringUtils.isBlank(persPerson.getLastName())) {// 防止设备中的姓名将软件中的人员姓名覆盖掉
                        persPerson.setLastName(fieldMap.get("lastName"));
                    }
                }
                if (fieldMap.containsKey("personPwd")) {
                    persPerson.setPersonPwd(fieldMap.get("personPwd"));
                }
                if (fieldMap.containsKey("personType")) {
                    persPerson.setPersonType(Short.parseShort(fieldMap.get("personType")));
                }
                if (fieldMap.containsKey("exceptionFlag")) {
                    persPerson.setExceptionFlag(Short.parseShort(fieldMap.get("exceptionFlag")));
                }
                if (fieldMap.containsKey("vicecard")) {
                    persPerson.setIdCard(fieldMap.get("vicecard"));
                }
                personItemList.add(persPerson);
                personItemMap.put(pin, persPerson);
                // persPersonService.saveItem(persPerson, null, null, extParams);
            }
        }
        if (personItemList.size() > 0) {
            persPersonService.batchSaveItem(personItemList, ConstUtil.SYSTEM_MODULE_ACC);
        }
    }

    /**
     * 解析更新的人事人员信息
     *
     * @param updateData
     * @return Map<String, Map < String, String>>
     */
    private Map<String, Map<String, String>> getUpdatePersPerson(Map<String, Map<String, String>> updateData) {
        Map<String, Map<String, String>> updateUserData = new HashMap<>();
        Set<String> updateUserDataKey = updateData.keySet();
        for (String tempKey : updateUserDataKey) {
            Map<String, String> curr = updateData.get(tempKey);
            curr.put("personType", String.valueOf(PersConstants.PERSON_TYPE_EMPLOYEE));
            Map<String,
                String> temp = getMap(curr,
                    new String[] {"status", "pin", "name", "lastName", "personPwd", "personType", "exceptionFlag"},
                    new String[] {"status", "pin", "name", "lastName", "personPwd", "personType", "exceptionFlag"});
            updateUserData.put(curr.get("pin"), temp);
        }
        return updateUserData;
    }

    /**
     * @param personMap
     * @param sn
     * @return
     * @Description: 组装人员信息
     * <AUTHOR>
     * @date 2018/6/28 16:48
     */
    private List<PersPersonItem> buildPersonData(Map<String, Map<String, String>> personMap, String sn) {
        List<PersPersonItem> persPersonList = new ArrayList<>();
        for (String pin : personMap.keySet()) {
            if (StringUtils.isNotBlank(pin)) {
                Map<String, String> fieldMap = personMap.get(pin);
                PersPersonItem persPerson = new PersPersonItem();
                persPerson.setIsFrom("ACC_DEVICE-" + sn);// 表明从设备上传及设备sn
                AuthDepartmentItem authDepartmentItem = authDepartmentService.getItemByCode("1");
                persPerson.setPin(pin);
                persPerson.setDeptId(authDepartmentItem.getId());// 新增人员添加默认部门
                if (fieldMap.containsKey("status")) {
                    persPerson.setStatus(Short.parseShort(fieldMap.get("status")));
                }
                if (fieldMap.containsKey("name")) {
                    persPerson.setName(fieldMap.get("name"));
                }
                if (fieldMap.containsKey("lastName")) {
                    persPerson.setLastName(fieldMap.get("lastName"));
                }
                if (fieldMap.containsKey("personPwd")) {
                    persPerson.setPersonPwd(fieldMap.get("personPwd"));
                }
                // if (fieldMap.containsKey("nameSpell")) {
                // persPerson.setNamePell(fieldMap.get("nameSpell"));
                // }
                if (fieldMap.containsKey("personType")) {
                    persPerson.setPersonType(Short.parseShort(fieldMap.get("personType")));
                }
                if (fieldMap.containsKey("exceptionFlag")) {
                    persPerson.setExceptionFlag(Short.parseShort(fieldMap.get("exceptionFlag")));
                }
                persPerson.setSelfPwd(PersConstants.PERSON_SELFPWD);
                persPersonList.add(persPerson);
            }
        }
        return persPersonList;
    }

    /**
     * 解析插入的人事人员信息
     *
     * @param insertData
     * @return Map<String, Map < String, String>>
     */
    private Map<String, Map<String, String>> getInsertPersPerson(Map<String, Map<String, String>> insertData) {
        Map<String, Map<String, String>> data = new HashMap<String, Map<String, String>>();
        Map<String, String> curr = null;
        Map<String, String> temp = null;
        Set<String> insertDataKeySet = insertData.keySet();
        for (String insertPerson : insertDataKeySet) {
            curr = insertData.get(insertPerson);
            curr.put("personType", String.valueOf(PersConstants.PERSON_TYPE_EMPLOYEE));
            temp = getMap(curr,
                new String[] {"status", "pin", "name", "lastName", "personPwd", "personType", "exceptionFlag"},
                new String[] {"status", "pin", "name", "lastName", "personPwd", "personType", "exceptionFlag"});
            data.put(curr.get("pin"), temp);
        }
        return data;
    }

    private Map<String, String> getMap(Map<String, String> map, String[] keys, String[] getKey) {
        Map<String, String> temp = new HashMap<>();
        for (int i = 0; i < keys.length; i++) {
            String tempStr = map.get(getKey[i]);
            if ("name".equals(keys[i])) {
                if (StringUtils.isNotBlank(tempStr) && tempStr.contains("__")) {
                    temp.put(keys[i], tempStr.split("__", 2)[0]);
                } else {
                    temp.put(keys[i], tempStr);
                }
            } else {
                temp.put(keys[i], tempStr);
            }
        }
        return temp;
    }

    /**
     * 更新卡号重复的数据到缓存
     *
     * @param conflictCardList
     * @param cmdId
     */
    private void setConflictCardToCache(List<String> conflictCardList, String cmdId) {
        StringBuffer conflictCardNo = new StringBuffer("");
        for (String cardNo : conflictCardList) {
            conflictCardNo.append(cardNo).append(",");
        }
        accCacheManager.set(AccCacheKeyConstants.QUERY_ACC_CONFLICT_CARD_CMDID + cmdId,
            conflictCardNo.length() > 0 ? conflictCardNo.substring(0, conflictCardNo.length() - 1) : "");
    }

    /**
     * 将数据库里的人员、卡好对比设备上传上来的信息,目前只是比较姓名、卡号、密码
     *
     * @param persCardItemMap
     * @param personDataMap
     * @param persPersonItem
     * @return
     */
    private boolean checkPersonRecordIsChange(Map<String, PersCardItem> persCardItemMap,
        Map<String, String> personDataMap, PersPersonItem persPersonItem) {
        String name = persPersonItem.getName() == null ? "" : persPersonItem.getName();
        String checkName = personDataMap.get("name") == null ? "" : personDataMap.get("name");

        String checkCardNo = personDataMap.get("cardno") == null || personDataMap.get("cardno").equals("0") ? ""
            : personDataMap.get("cardno");
        String cardNo = "";
        // personcardmap中可能是其他人员的卡号，需要比对是否是当前人员的卡号
        if (persCardItemMap.get(checkCardNo) != null
            && persCardItemMap.get(checkCardNo).getPersonPin().equals(persPersonItem.getPin())) {
            cardNo = persCardItemMap.get(checkCardNo).getCardNo();
        }
        String personPwd = persPersonItem.getPersonPwd() == null ? "" : persPersonItem.getPersonPwd();
        String checkPersonPwd = personDataMap.get("personPwd") == null ? "" : personDataMap.get("personPwd");
        return cardNo.equals(checkCardNo) && personPwd.equals(checkPersonPwd) && name.equals(checkName);
    }

    /**
     * @param table
     * @param data
     * @param sn
     * @return
     * @Description: 解析设备上传人员数据
     * <AUTHOR>
     * @date 2018/6/13 11:48
     */
    private Map<String, Map<String, String>> getDataMapFromDevUpload(String table, String data, String sn) {
        AccDevice accDevice = accDeviceDao.findBySn(sn);
        short commType = accDevice.getCommType();
        Map<String, Map<String, String>> resolveMap = new HashMap<>();
        if (commType == ConstUtil.COMM_TCPIP || commType == ConstUtil.COMM_RS485) {
            resolveMap = getDataMapByPull(table, data);// pull通信方式-- 解析人员
        } else if (commType == ConstUtil.COMM_HTTP) {
            resolveMap = getDataMapByPush(table, data);// push通信方式-- 解析人员
        } else if (commType == AccConstants.COMM_TYPE_BEST_MQTT) {
            // Mqtt通信方式-- 解析人员 add by colin 2020-10-30 14:28:18
            resolveMap = getDataMapByMqtt(table, data);
        }
        return resolveMap;
    }

    @Override
    public Map<String, Integer> dealPersonTemplateInfo(String table, String data, String sn) {
        Map<String, Integer> retMap = new HashMap<>();
        Map<String, Map<String, String>> resolveMap = getDataMapFromDevUpload(table, data, sn);
        Map<String, PersBioTemplateItem> persBioTemplateMap = null;
        Map<String, JSONObject> updateData = new HashMap<>(); // 将已经插入数据库中的记录放到updateDate的Map中
        Map<String, JSONObject> insertData = new HashMap<>(); // 需要新增记录记录放到insertData的Map中
        int insertCount = 0;
        int updateCount = 0;
        JSONObject bioTemplateJson = null;
        for (String compareKey : resolveMap.keySet()) {
            String pin = compareKey.split("_")[0];
            PersPersonItem personItem = persPersonService.getItemByPin(pin);
            if (personItem != null) {
                List<PersBioTemplateItem> bioTemplateItems =
                    persBioTemplateService.getItemByPersonId(personItem.getId());
                persBioTemplateMap = putPersBioTemplateToMap(pin, bioTemplateItems);
                String tempCompareKey = ""; // 临时key,用于取消胁迫指纹：即由胁迫指纹改为正常指纹，设置胁迫指纹：即由正常指纹改为胁迫指纹
                if (table.equals(ConstUtil.TEMPLATEV10)) {
                    int fvIndex = Integer.parseInt(compareKey.split("_")[1]);
                    if (fvIndex > 10) {
                        fvIndex -= 16;
                    }
                    tempCompareKey = compareKey.replace("_" + compareKey.split("_")[1] + "_", "_" + fvIndex + "_");
                }
                if (persBioTemplateMap.containsKey(compareKey) || persBioTemplateMap.containsKey(tempCompareKey)) { // 软件中存在相应指纹
                    bioTemplateJson = new JSONObject();
                    if (updateData.containsKey(personItem.getId())) {
                        bioTemplateJson = updateData.get(personItem.getId());
                    }
                    buildPersonBioTemplate(resolveMap.get(compareKey), table, bioTemplateJson);
                    updateData.put(personItem.getId(), bioTemplateJson);// 存放需要更新的数据
                    updateCount++;
                } else { // 新增
                    bioTemplateJson = new JSONObject();
                    if (insertData.containsKey(personItem.getId())) {
                        bioTemplateJson = insertData.get(personItem.getId());
                    }
                    buildPersonBioTemplate(resolveMap.get(compareKey), table, bioTemplateJson);
                    insertData.put(personItem.getId(), bioTemplateJson);
                    insertCount++;
                }
            }
        }

        if (insertData.size() > 0) {
            updateBioTemplate(insertData);// 更新人员指纹模板,保存到数据库
        }
        if (updateData.size() > 0) {
            updateBioTemplate(updateData);// 更新人员指纹模板,保存到数据库
        }
        retMap.put("count", resolveMap.size());
        retMap.put("insert", insertCount);
        retMap.put("update", updateCount);
        return retMap;
    }

    /**
     * @param bioTemplateData
     * @return
     * @Description: 更新人员指纹模板
     * <AUTHOR>
     * @date 2018/6/13 11:45
     */
    private void updateBioTemplate(Map<String, JSONObject> bioTemplateData) {
        for (String personId : bioTemplateData.keySet()) {
            JSONObject bioTemplateJson = bioTemplateData.get(personId);
            persBioTemplateService.updateBioTemplateJson(personId, bioTemplateJson.toJSONString());
        }
    }

    /**
     * @param bioTemplateMap
     * @return
     * @Description: 封装人员指纹模板数据
     * <AUTHOR>
     * @date 2018/6/13 11:45
     */
    private void buildPersonBioTemplate(Map<String, String> bioTemplateMap, String table, JSONObject dataJson) {
        Map<String, PersBioTemplateItem> bioTemplateList = new HashMap<>();
        PersBioTemplateItem persBioTemplateItem = new PersBioTemplateItem();
        if (table.equals(ConstUtil.TEMPLATEV10)) {
            if (dataJson.containsKey("fpList")) {
                bioTemplateList = (Map<String, PersBioTemplateItem>)dataJson.get("fpList");
            }
            int fingerId = Integer.parseInt(bioTemplateMap.get("fingerid"));
            if (fingerId > 9) { // 胁迫指纹
                fingerId = fingerId - 16;
                persBioTemplateItem.setDuress(true);// 胁迫指纹
            } else if ("3".equals(bioTemplateMap.get("valid"))) {
                persBioTemplateItem.setDuress(true);// 胁迫指纹
            } else {
                persBioTemplateItem.setDuress(false);// 非胁迫指纹
            }
            persBioTemplateItem.setTemplateNo((short)fingerId);// 修改templateNo,前端显示用
            persBioTemplateItem.setTemplateNoIndex(PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX);// 未上传指纹索引、修改指纹索引默认为0
            persBioTemplateItem.setTemplate(bioTemplateMap.get("template"));// 指纹模板
            persBioTemplateItem.setValidType(Short.valueOf(bioTemplateMap.get("valid")));
            persBioTemplateItem.setVersion(bioTemplateMap.get("version"));
            bioTemplateList.put(persBioTemplateItem.getTemplateNo() + "_" + persBioTemplateItem.getTemplateNoIndex(),
                persBioTemplateItem);
            dataJson.put("fpList", bioTemplateList);
        } else if (ConstUtil.FACEV7.equals(table)) {// facev7
            if (dataJson.containsKey("faceList")) {
                bioTemplateList = (Map<String, PersBioTemplateItem>)dataJson.get("faceList");
            }
            persBioTemplateItem.setTemplateNo(PersConstants.PERS_BIOTEMPLATE_DEF_NO);
            persBioTemplateItem.setDuress(false);
            persBioTemplateItem.setTemplateNoIndex(Short.valueOf(bioTemplateMap.get("faceid")));
            persBioTemplateItem.setTemplate(bioTemplateMap.get("face"));// 面部模板
            persBioTemplateItem.setValidType(Short.valueOf(bioTemplateMap.get("valid")));
            persBioTemplateItem.setVersion(bioTemplateMap.get("version"));
            bioTemplateList.put(persBioTemplateItem.getTemplateNo() + "_" + persBioTemplateItem.getTemplateNoIndex(),
                persBioTemplateItem);
            dataJson.put("faceList", bioTemplateList);// 面部数据
        } else if (ConstUtil.FACE.equals(table)) {// FACE
            if (dataJson.containsKey("faceList")) {
                bioTemplateList = (Map<String, PersBioTemplateItem>)dataJson.get("faceList");
            }
            persBioTemplateItem.setTemplateNo(PersConstants.PERS_BIOTEMPLATE_DEF_NO);
            persBioTemplateItem.setDuress(false);
            persBioTemplateItem.setTemplateNoIndex(Short.valueOf(bioTemplateMap.get("fid")));
            persBioTemplateItem.setTemplate(bioTemplateMap.get("face"));// 面部模板
            persBioTemplateItem.setValidType(Short.valueOf(bioTemplateMap.get("valid")));
            persBioTemplateItem.setVersion(bioTemplateMap.get("version"));
            bioTemplateList.put(persBioTemplateItem.getTemplateNo() + "_" + persBioTemplateItem.getTemplateNoIndex(),
                persBioTemplateItem);
            dataJson.put("faceList", bioTemplateList);// 面部数据
        } else if ("biodata".equals(table)) {
            Short bioType = Short.valueOf(bioTemplateMap.get("type"));// 生物模板类型
            String version = bioTemplateMap.get("majorver");
            if (!"0".equals(bioTemplateMap.get("minorver"))) {
                version += "." + bioTemplateMap.get("minorver");
            }
            if (bioType.equals(BaseConstants.BaseBioType.FP_BIO_TYPE)) {
                if (dataJson.containsKey("fpList")) {
                    bioTemplateList = (Map<String, PersBioTemplateItem>)dataJson.get("fpList");
                }
                boolean duress = false;// 0：非胁迫，1：胁迫，默认为0
                if (Objects.nonNull(bioTemplateMap.get("duress")) && "1".equals(bioTemplateMap.get("duress"))) {
                    duress = true;
                }
                persBioTemplateItem.setDuress(duress);
                int fingerId = Integer.parseInt(bioTemplateMap.get("no"));
                persBioTemplateItem.setTemplateNo((short)fingerId);// 修改templateNo,前端显示用
                persBioTemplateItem.setTemplateNoIndex(PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX);
                persBioTemplateItem.setTemplate(bioTemplateMap.get("tmp"));// 指纹模板
                persBioTemplateItem.setValidType(Short.valueOf(bioTemplateMap.get("valid")));
                persBioTemplateItem.setVersion(version);
                bioTemplateList.put(
                    persBioTemplateItem.getTemplateNo() + "_" + persBioTemplateItem.getTemplateNoIndex(),
                    persBioTemplateItem);
                dataJson.put("fpList", bioTemplateList);
            } else if (bioType.equals(BaseConstants.BaseBioType.PALM_BIO_TYPE)) {
                if (dataJson.containsKey("palmList")) {
                    bioTemplateList = (Map<String, PersBioTemplateItem>)dataJson.get("palmList");
                }
                persBioTemplateItem.setDuress(false);
                int palmId = Integer.parseInt(bioTemplateMap.get("index"));
                persBioTemplateItem.setTemplateNoIndex((short)palmId);// 修改templateNo,前端显示用
                persBioTemplateItem.setTemplateNo(PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX);
                persBioTemplateItem.setTemplate(bioTemplateMap.get("tmp"));// 掌纹模板
                persBioTemplateItem.setValidType(Short.valueOf(bioTemplateMap.get("valid")));
                persBioTemplateItem.setVersion(version);
                bioTemplateList.put(
                    persBioTemplateItem.getTemplateNo() + "_" + persBioTemplateItem.getTemplateNoIndex(),
                    persBioTemplateItem);
                dataJson.put("palmList", bioTemplateList);
            } else if (bioType.equals(BaseConstants.BaseBioType.FACE_BIO_TYPE)) {
                if (dataJson.containsKey("faceList")) {
                    bioTemplateList = (Map<String, PersBioTemplateItem>)dataJson.get("faceList");
                }
                persBioTemplateItem.setDuress(false);
                int faceId = Integer.parseInt(bioTemplateMap.get("index"));
                persBioTemplateItem.setTemplateNoIndex((short)faceId);// 修改templateNo,前端显示用
                persBioTemplateItem.setTemplateNo(PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX);
                persBioTemplateItem.setTemplate(bioTemplateMap.get("tmp"));// 人脸模板
                persBioTemplateItem.setValidType(Short.valueOf(bioTemplateMap.get("valid")));
                persBioTemplateItem.setVersion(version);
                bioTemplateList.put(
                    persBioTemplateItem.getTemplateNo() + "_" + persBioTemplateItem.getTemplateNoIndex(),
                    persBioTemplateItem);
                dataJson.put("faceList", bioTemplateList);
            } else if (bioType.equals(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE)) {
                if (dataJson.containsKey("vislightList")) {
                    bioTemplateList = (Map<String, PersBioTemplateItem>)dataJson.get("vislightList");
                }
                persBioTemplateItem.setDuress(false);
                int faceId = Integer.parseInt(bioTemplateMap.get("index"));
                persBioTemplateItem.setTemplateNoIndex((short)faceId);// 修改templateNo,前端显示用
                persBioTemplateItem.setTemplateNo(PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX);
                persBioTemplateItem.setTemplate(bioTemplateMap.get("tmp"));// 可见光模板
                persBioTemplateItem.setValidType(Short.valueOf(bioTemplateMap.get("valid")));
                persBioTemplateItem.setVersion(version);
                bioTemplateList.put(
                    persBioTemplateItem.getTemplateNo() + "_" + persBioTemplateItem.getTemplateNoIndex(),
                    persBioTemplateItem);
                dataJson.put("vislightList", bioTemplateList);
            }
        }
    }

    /**
     * @param pin
     * @param bioTemplateItems
     * @return
     * @Description: 组装数据库中人员指纹数据到map，用于判断是新增还是更新数据
     * <AUTHOR>
     * @date 2018/6/13 11:38
     */
    private Map<String, PersBioTemplateItem> putPersBioTemplateToMap(String pin,
        List<PersBioTemplateItem> bioTemplateItems) {
        Map<String, PersBioTemplateItem> persBioTemplateMap = new HashMap<>();
        for (PersBioTemplateItem bioTemplateItem : bioTemplateItems) {
            String key = "";
            if (bioTemplateItem.getBioType().equals(BaseConstants.BaseBioType.FACE_BIO_TYPE)) {
                key = pin + "_" + bioTemplateItem.getTemplateNoIndex() + "_" + bioTemplateItem.getBioType();
            } else {
                key = pin + "_" + bioTemplateItem.getTemplateNo() + "_" + bioTemplateItem.getBioType();
            }
            if (!persBioTemplateMap.containsKey(key)) {
                persBioTemplateMap.put(key, bioTemplateItem);
            }
        }
        return persBioTemplateMap;
    }

    @Override
    public Map<String, String> filterPersonByVaildCard(String cardNo) {
        PersCardItem persCardItem = persCardService.getItemByCardNo(cardNo);
        Map<String, String> filterMap = Maps.newHashMap();
        String cardNoExist = "false";
        String personPin = "";
        if (Objects.nonNull(persCardItem)) {
            cardNoExist = "true";
            personPin = persCardItem.getPersonPin();
        }
        filterMap.put("cardNoExist", cardNoExist);
        filterMap.put("personPin", personPin);
        return filterMap;
    }

    @Override
    public Pager getNoCardPerson(String sessionId, AccSelectPersonRadioItem accSelectPersonRadioItem, int pageNo,
        int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            accSelectPersonRadioItem.setUserId(userId);
        }
        return getItemsByPage(accSelectPersonRadioItem, pageNo, pageSize);
    }

    @Override
    public Pager getPersonNoCardItemList(String sessionId, AccPersonListNoCardItem accPersonListNoCardItem, int pageNo,
        int pageSize) {
        PersPersonSelectItem persPersonSelectItem = new PersPersonSelectItem();
        persPersonSelectItem.setType(accPersonListNoCardItem.getModelType());
        persPersonSelectItem.setLinkId(accPersonListNoCardItem.getLinkId());
        persPersonSelectItem.setId(accPersonListNoCardItem.getId());
        persPersonSelectItem.setPersPin(accPersonListNoCardItem.getPersonPin());
        persPersonSelectItem.setPersName(accPersonListNoCardItem.getPersonName());
        persPersonSelectItem.setPersLastName(accPersonListNoCardItem.getPersonLastName());
        persPersonSelectItem.setDeptName(accPersonListNoCardItem.getDeptName());
        persPersonSelectItem.setLikeName(accPersonListNoCardItem.getLikeName());
        if ("personPin".equals(accPersonListNoCardItem.getSortName())) {
            persPersonSelectItem.setSortName("persPin");
        } else if ("personName".equals(accPersonListNoCardItem.getSortName())) {
            persPersonSelectItem.setSortName("persName");
        } else {
            persPersonSelectItem.setSortName(accPersonListNoCardItem.getSortName());
        }
        persPersonSelectItem.setSortOrder(accPersonListNoCardItem.getSortOrder());
        Pager pager = persPersonService.findPersonSelectItem(sessionId, persPersonSelectItem, pageNo, pageSize);
        // 将人事数据转换成门禁需要的数据
        List<PersPersonSelectItem> persPersonSelectItemList = (List<PersPersonSelectItem>)pager.getData();
        List<AccPersonListNoCardItem> accPersonListNoCardItemList = new ArrayList<>();
        if (persPersonSelectItemList != null && persPersonSelectItemList.size() > 0) {
            persPersonSelectItemList.stream().forEach(item -> {
                AccPersonListNoCardItem accPersonListNoCardItemNew = new AccPersonListNoCardItem();
                accPersonListNoCardItemNew.setId(item.getId());
                accPersonListNoCardItemNew.setPersonPin(item.getPersPin());
                accPersonListNoCardItemNew.setPersonName(item.getPersName());
                accPersonListNoCardItemNew.setPersonLastName(item.getPersLastName());
                accPersonListNoCardItemNew.setDeptName(item.getDeptName());
                accPersonListNoCardItemList.add(accPersonListNoCardItemNew);
            });
        }
        pager.setData(accPersonListNoCardItemList);
        return pager;
    }

    @Override
    public Pager getPersonItemList(String sessionId, AccPersonListItem accPersonListItem, int pageNo, int pageSize) {
        PersPersonSelectItem persPersonSelectItem = new PersPersonSelectItem();
        persPersonSelectItem.setType(accPersonListItem.getModelType());
        persPersonSelectItem.setLinkId(accPersonListItem.getLinkId());
        persPersonSelectItem.setId(accPersonListItem.getId());
        persPersonSelectItem.setPersPin(accPersonListItem.getPersonPin());
        persPersonSelectItem.setLikeName(accPersonListItem.getLikeName());
        persPersonSelectItem.setCardNo(accPersonListItem.getCardNo());
        persPersonSelectItem.setDeptName(accPersonListItem.getDeptName());
        if ("personPin".equals(accPersonListItem.getSortName())) {
            persPersonSelectItem.setSortName("persPin");
        } else if ("personName".equals(accPersonListItem.getSortName())) {
            persPersonSelectItem.setSortName("persName");
        } else {
            persPersonSelectItem.setSortName(accPersonListItem.getSortName());
        }
        persPersonSelectItem.setSortOrder(accPersonListItem.getSortOrder());
        Pager pager = persPersonService.findPersonSelectItem(sessionId, persPersonSelectItem, pageNo, pageSize);
        // 将人事数据转换成门禁需要的数据
        List<PersPersonSelectItem> persPersonSelectItemList = (List<PersPersonSelectItem>)pager.getData();
        List<AccPersonListItem> accPersonListItemList = new ArrayList<>();
        if (persPersonSelectItemList != null && persPersonSelectItemList.size() > 0) {
            persPersonSelectItemList.stream().forEach(item -> {
                AccPersonListItem accPersonListItemNew = new AccPersonListItem();
                accPersonListItemNew.setId(item.getId());
                accPersonListItemNew.setPersonPin(item.getPersPin());
                accPersonListItemNew.setPersonName(item.getPersName());
                accPersonListItemNew.setPersonLastName(item.getPersLastName());
                accPersonListItemNew.setDeptName(item.getDeptName());
                // accPersonListItemNew.setCardNo(item.getCardNo());
                accPersonListItemList.add(accPersonListItemNew);
            });
        }
        pager.setData(accPersonListItemList);
        return pager;
    }

    @Override
    public List<AccPersonListItem> getExportPersonItemList(String sessionId, AccPersonListItem accPersonListItem,
        int beginIndex, int endIndex) {
        PersPersonSelectItem persPersonSelectItem = new PersPersonSelectItem();
        persPersonSelectItem.setType(accPersonListItem.getModelType());
        persPersonSelectItem.setLinkId(accPersonListItem.getLinkId());
        persPersonSelectItem.setId(accPersonListItem.getId());
        persPersonSelectItem.setPersPin(accPersonListItem.getPersonPin());
        persPersonSelectItem.setPersName(accPersonListItem.getPersonName());
        persPersonSelectItem.setPersLastName(accPersonListItem.getPersonLastName());
        persPersonSelectItem.setCardNo(accPersonListItem.getCardNo());
        persPersonSelectItem.setDeptName(accPersonListItem.getDeptName());
        // 将人事数据转换成门禁需要的数据
        List<PersPersonSelectItem> persPersonSelectItemList =
            persPersonService.getPersonSelectItem(sessionId, persPersonSelectItem, beginIndex, endIndex);
        List<AccPersonListItem> accPersonListItemList = new ArrayList<>();
        if (persPersonSelectItemList != null && persPersonSelectItemList.size() > 0) {
            persPersonSelectItemList.stream().forEach(item -> {
                AccPersonListItem accPersonListItemNew = new AccPersonListItem();
                accPersonListItemNew.setId(item.getId());
                accPersonListItemNew.setPersonPin(item.getPersPin());
                accPersonListItemNew.setPersonName(item.getPersName());
                accPersonListItemNew.setPersonLastName(item.getPersLastName());
                accPersonListItemNew.setDeptName(item.getDeptName());
                accPersonListItemNew.setCardNo(item.getCardNo());
                accPersonListItemList.add(accPersonListItemNew);
            });
        }
        return accPersonListItemList;
    }

    @Override
    public String getDeptIdsByAuthFilter(String sessionId) {
        String deptIds = "";
        // 获取当前登录用户信息
        SecuritySubject securitySubject = authSessionProvider.getSecuritySubject(sessionId);
        if (!securitySubject.getIsSuperuser()) {
            // 非超级用户进行数据权限过滤
            deptIds = StrUtil.collectionToStr(securitySubject.getDepartmentIds());
        }
        return deptIds;
    }

    @Override
    public void setCardNoToPerson(String personId, String cardNo) {
        persCardService.updatePersonCardInfo(personId, cardNo);
        List<String> personIdList = (List<String>)CollectionUtil.strToList(personId);
        setPersonToDev(personIdList);
    }

    @Override
    public void saveParamSet(AccPersonItem item) {
        AccPersonItem accPersonItem = new AccPersonItem();
        ModelUtil.copyPropertiesIgnoreNull(item, accPersonItem);
        saveItem(accPersonItem);
        List<String> personIdList = (List<String>)CollectionUtil.strToList(item.getPersonId());
        setPersonToDev(personIdList);
    }

    private void setPersonToDev(List<String> personIdList) {
        List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonIdIn(personIdList);
        if (levelIdList != null && levelIdList.size() > 0) {
            List<AccDevice> deviceList = accDeviceDao.getDevByLevel(levelIdList);
            if (deviceList != null && deviceList.size() > 0) {
                AccDeviceUtil.moveUpParentDevList(deviceList);
                AccPersonInfoItem personInfoBean = accLevelService.getPersonByIds(personIdList);
                for (AccDevice accDevice : deviceList) {
                    accDevCmdManager.setPersonToDev(accDevice.getSn(), personInfoBean.getPersonList(), false);
                }
            }
        }
    }

    @Override
    public Map<String, String> addPersonFaceRecord(Short commType, String data, String table) {
        Map<String, String> rs = new HashMap<>();
        int count = 0;
        int insert = 0;
        int update = 0;
        String pin = "";
        String faceId = "";
        String faceIdIndex = "";
        String template = "";
        int index = commType == ConstUtil.COMM_HTTP ? 0 : 1;
        String[] recordArr = data.replaceAll(table + " ", "").split("\r\n");
        if (recordArr.length > 0) {
            String recordData = "";
            for (int i = index; i < recordArr.length; i++) {
                recordData = recordArr[i].trim();
                if (commType == ConstUtil.COMM_TCPIP || commType == ConstUtil.COMM_RS485) {
                    String[] recordArray = recordData.split(",");
                    pin = recordArray[1];
                    faceId = recordArray[2];
                    faceIdIndex = recordArray[3];
                    template = recordArray[4];
                } else if (commType == ConstUtil.COMM_HTTP) {
                    String[] recordArray = recordData.split("\t");
                    pin = recordArray[0].split("=")[1];
                    faceId = PersConstants.PERS_BIOTEMPLATE_DEF_NO + "";
                    faceIdIndex = String.valueOf(Integer.valueOf(recordArray[1].split("=")[1]));
                    template = recordArray[4].split("=")[1];
                }
                PersBioTemplateItem persBioTemplate =
                    checkPersonFaceTemplateIsExist(pin, faceId, faceIdIndex, BaseConstants.BaseBioType.FACE_BIO_TYPE);
                if (persBioTemplate == null) {
                    if (addPersonFaceTemplate(pin, faceId, faceIdIndex, template) && "0".equals(faceIdIndex)) {
                        // 当人脸的索引为0时，记录到新增操作中
                        insert++;
                    }
                } else {
                    persBioTemplate.setTemplate(template);
                    updatePersonFaceBioTemplate(persBioTemplate);
                    if ("0".equals(faceIdIndex)) {
                        // 当人脸的索引为0时，记录到更新操作中
                        update++;
                    }
                }
            }
            // 设置总数
            count = insert + update;
        }
        rs.put("count", count + "");
        rs.put("insert", insert + "");
        rs.put("update", update + "");
        return rs;
    }

    private boolean addPersonFaceTemplate(String pin, String faceId, String faceIdIndex, String template) {
        PersPersonItem person = persPersonService.getItemByPin(pin);
        if (person == null) {
            return false;
        } else {
            JSONObject bioTemplateJson = new JSONObject();
            Map<String, PersBioTemplateItem> bioTemplateList = new HashMap<>();
            PersBioTemplateItem persBioTemplate = new PersBioTemplateItem();
            // 指静脉固定7
            persBioTemplate.setBioType(BaseConstants.BaseBioType.FACE_BIO_TYPE);
            persBioTemplate.setTemplateNo(Short.valueOf(faceId));
            persBioTemplate.setTemplate(template);
            persBioTemplate.setTemplateNoIndex(Short.valueOf(faceIdIndex));
            persBioTemplate.setValidType((short)1);
            // 人脸算法
            persBioTemplate.setVersion(BaseConstants.BaseBioType.FACE_BIO_VERSION);
            persBioTemplate.setPersonId(person.getId());
            bioTemplateList.put(persBioTemplate.getTemplateNo() + "_" + persBioTemplate.getTemplateNoIndex(),
                persBioTemplate);
            bioTemplateJson.put("faceList", bioTemplateList);// 面部数据
            persBioTemplateService.updateBioTemplateJson(persBioTemplate.getPersonId(), bioTemplateJson.toJSONString());
            return true;
        }
    }

    /**
     * @param persBioTemplate
     * @return
     * @Description: 组装人员面部数据
     * <AUTHOR>
     * @date 2018/6/28 20:03
     */
    private void updatePersonFaceBioTemplate(PersBioTemplateItem persBioTemplate) {
        JSONObject bioTemplateJson = new JSONObject();
        Map<String, PersBioTemplateItem> bioTemplateList = new HashMap<>();
        bioTemplateList.put(persBioTemplate.getTemplateNo() + "_" + persBioTemplate.getTemplateNoIndex(),
            persBioTemplate);
        bioTemplateJson.put("faceList", bioTemplateList);// 面部数据
        persBioTemplateService.updateBioTemplateJson(persBioTemplate.getPersonId(), bioTemplateJson.toJSONString());
    }

    private PersBioTemplateItem checkPersonFaceTemplateIsExist(String pin, String faceId, String faceIdIndex,
        Short faceBioType) {
        PersPersonItem person = persPersonService.getItemByPin(pin);
        if (Objects.nonNull(person)) {
            PersBioTemplateItem persBioTemplateItem = new PersBioTemplateItem();
            persBioTemplateItem.setPersonId(person.getId());
            persBioTemplateItem.setTemplateNo(Short.valueOf(faceId));
            persBioTemplateItem.setTemplateNoIndex(Short.valueOf(faceIdIndex));
            persBioTemplateItem.setBioType(faceBioType);
            List<PersBioTemplateItem> bioTemplateItems = persBioTemplateService.getByCondition(persBioTemplateItem);
            if (!bioTemplateItems.isEmpty()) {
                return bioTemplateItems.get(0);
            }
        }
        return null;
    }

    @Override
    public Map<String, String> dealExtuserInfo(Short commType, Long cmdId, String sn, String data, String table) {
        Map<String, String> ret = Maps.newHashMap();
        Map<String, Map<String, String>> resolveMap = Maps.newHashMap();
        if (commType == ConstUtil.COMM_TCPIP || commType == ConstUtil.COMM_RS485) {
            resolveMap = getDataMapByPull(table, data);
        } else if (commType == ConstUtil.COMM_HTTP) {
            resolveMap = getDataMapByPush(table, data);
        }
        // 更新 acc_person
        int updateCount = 0;
        Map<String, Map<String, String>> updateAccData = Maps.newHashMap();
        if (resolveMap != null && resolveMap.size() > 0) {
            // String delayPassage = "";
            List<PersPersonItem> persPersonList = persPersonService.getItemsByPins(resolveMap.keySet());
            Map<String, PersPersonItem> persPersonMap =
                CollectionUtil.listToKeyMap(persPersonList, PersPersonItem::getPin);
            for (String tempKey : resolveMap.keySet()) {
                Map<String, String> curr = resolveMap.get(tempKey);
                Map<String, String> temp = Maps.newHashMap();
                temp.put("id", curr.get("pin"));// 保存pin
                // delayPassage = AccDataUtil.getFunswitchByIndex(curr.get("funswitch"), 0);
                // temp.put("delayPassage", delayPassage);
                temp.put("lastName", curr.containsKey("lastname") ? curr.get("lastname") : "");
                temp.put("firstName", curr.containsKey("firstname") ? curr.get("firstname") : "");
                updateAccData.put(curr.get("pin"), temp);
            }
            updateCount = updateExtPerson(updateAccData, persPersonMap);
        }
        // 以下三个值，新增，统一跟人员处理代码风格，后续统计可能会用到，add by max 20160809
        ret.put("count", resolveMap.size() + "");
        ret.put("insert", "0");
        ret.put("update", updateCount + "");
        return ret;
    }

    /**
     * 更新人员扩展表数据
     *
     * @param updateAccData
     * @param persPersonMap
     */
    private int updateExtPerson(Map<String, Map<String, String>> updateAccData,
        Map<String, PersPersonItem> persPersonMap) {
        int updateCount = 0;
        Set<String> keySet = updateAccData.keySet();
        List<PersPersonItem> personItems = new ArrayList<>();
        List<String> personIdList = new ArrayList<>();
        for (String key : persPersonMap.keySet()) {
            PersPersonItem temp = persPersonMap.get(key);
            if (StringUtils.isNotBlank(temp.getId())) {
                personIdList.add(temp.getId());
            }
        }
        // List<AccPerson> accPersonList = accPersonDao.findByPersonIdIn(personIdList);
        // Map<String, AccPerson> accPersonMap = CollectionUtil.listToKeyMap(accPersonList, AccPerson::getPersonId);
        for (String key : keySet) {
            Map<String, String> fieldMap = updateAccData.get(key);
            if (!fieldMap.isEmpty()) {
                if (fieldMap.containsKey("id")) {
                    PersPersonItem persPerson = null;
                    if (persPersonMap.containsKey(key)) {
                        persPerson = persPersonMap.get(key);
                    }
                    if (Objects.nonNull(persPerson)) {
                        if (!checkExtUserIsChange(fieldMap.get("lastName"), fieldMap.get("firstName"), persPerson)) {
                            continue;
                        }
                        if (StringUtils.isBlank(persPerson.getLastName()) && fieldMap.containsKey("lastName")
                            && StringUtils.isNotBlank(fieldMap.get("lastName"))) {
                            persPerson.setLastName(fieldMap.get("lastName"));
                        }
                        if (StringUtils.isBlank(persPerson.getName()) && fieldMap.containsKey("firstName")
                            && StringUtils.isNotBlank(fieldMap.get("firstName"))) {
                            persPerson.setName(fieldMap.get("firstName"));
                        }
                        updateCount++;
                        personItems.add(persPerson);
                    }
                }
            }
        }
        if (personItems.size() > 0) {
            persPersonService.batchSaveItem(personItems, ConstUtil.SYSTEM_MODULE_ACC);
        }
        return updateCount;
    }

    private boolean checkExtUserIsChange(String lastName, String firstName, PersPersonItem personItem) {
        lastName = StringUtils.isBlank(lastName) ? "" : lastName;
        firstName = StringUtils.isBlank(firstName) ? "" : firstName;
        String checkLastName = StringUtils.isBlank(personItem.getLastName()) ? "" : personItem.getLastName();
        String checkFirstName = StringUtils.isBlank(personItem.getName()) ? "" : personItem.getName();
        return !lastName.equals(checkLastName) || !firstName.equals(checkFirstName);
    }

    @Override
    public Map<String, String> dealMulCardUserInfo(Short commType, Long cmdId, String data, String table) {
        Map<String, Map<String, String>> resolveMap = Maps.newHashMap();
        // 解析数据
        if (commType == ConstUtil.COMM_TCPIP || commType == ConstUtil.COMM_RS485) {
            resolveMap = getDataMapByPull(table, data);
        } else if (commType == ConstUtil.COMM_HTTP) {
            resolveMap = getDataMapByPush(table, data);
        } else if (commType == AccConstants.COMM_TYPE_BEST_MQTT) {
            // Mqtt add by colin 2020-11-2 14:06:01
            resolveMap = getDataMapByMqtt(table, data);
        }
        // 从解析的数据中获取卡号集合用于查询
        Set<String> cardNoSet = Sets.newHashSet();
        Set<String> pinSet = Sets.newHashSet();
        for (String key : resolveMap.keySet()) {
            Map<String, String> cardData = resolveMap.get(key);
            String cardNo = cardData.get("cardno");
            pinSet.add(cardData.get("pin"));
            if (StringUtils.isNotBlank(cardNo)) {
                // set本身已经去重功能，没必要加contain modfied by max 20151120优化代码；
                cardNoSet.add(cardNo);
            }
        }
        return insertMulCardRecord(resolveMap, cmdId, pinSet, cardNoSet);
    }

    private Map<String, String> insertMulCardRecord(Map<String, Map<String, String>> personData, Long cmdId,
        Set<String> pinSet, Set<String> cardNoSet) {
        Map<String, String> ret = Maps.newHashMap();
        List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(pinSet);// 获取软件中人员信息
        Map<String, PersPersonItem> personItemMap =
            CollectionUtil.listToKeyMap(persPersonItemList, PersPersonItem::getPin);// 获取人员对象集合
        List<PersCardItem> persMainCardList = persCardService
            .getMasterCardByPersonIdList((List<String>)CollectionUtil.getItemIdsList(persPersonItemList));// 根据人员id获取软件中的卡
        Map<String, PersCardItem> persCardItemMap =
            CollectionUtil.listToKeyMap(persMainCardList, PersCardItem::getPersonPin);// 获取卡号对象集合
        Map<String, Set<String>> persCardPinMap = getPersonCardPinMap(cardNoSet);
        Map<String, Map<String, String>> updateData = Maps.newHashMap();
        Map<String, Map<String, String>> insertData = Maps.newHashMap();
        List<String> cardList = Lists.newArrayList();// 存放当批次卡号，用于判断卡号重复
        ret.put("count", String.valueOf(personData.size()));
        List<String> conflictCardList = new ArrayList<>();// 和软件中卡号有冲突(重复)
        for (String pinCard : personData.keySet()) {
            boolean isUpdate = false;
            Map<String, String> personMap = personData.get(pinCard);
            if (Objects.nonNull(personMap)) {
                String pin = pinCard.split("_")[0];
                if (Objects.isNull(personItemMap.get(pin))) {
                    continue;// 表示多卡中的pin号无对应人员，是异常数据
                }
                if (personMap.containsKey("cardno") && StringUtils.isNotBlank(personMap.get("cardno"))) {
                    String cardNo = personMap.get("cardno");
                    if (cardList.contains(cardNo)) {// 判断当前批次处理人员是否存在卡号重复
                        personMap.put("exceptionFlag", String.valueOf(PersConstants.PERSON_ISEXCEPTION));// 卡号重复
                        if (!conflictCardList.contains(cardNo)) {
                            conflictCardList.add(cardNo);
                        }
                    } else {
                        cardList.add(cardNo);
                    }
                    if (Objects.nonNull(persCardPinMap.get(cardNo))) {
                        Set<String> pins = persCardPinMap.get(cardNo);
                        if (pins.contains(pin)) {
                            continue;// 表示该pin号人员已经有此卡
                        } else if (pins.size() > 1 || (pins.size() == 1 && !pins.contains(pin))) {
                            personMap.put("exceptionFlag", String.valueOf(PersConstants.PERSON_ISEXCEPTION));// 卡号重复
                            if (!conflictCardList.contains(cardNo)) {
                                conflictCardList.add(cardNo);
                            }
                        }
                    }
                    personMap.put("cardType", personMap.get("cardtype"));
                    // 上传卡类型为主卡、且人员存在主卡,为更新数据,修复重复获取人员信息,都为插入数据,出现卡号重复的问题
                    if (personMap.get("cardtype").equals(PersConstants.MAIN_CARD + "")
                        && persCardItemMap.get(pin) != null) {
                        isUpdate = true;
                    }
                }
                personData.put(pinCard, personMap);// 将修改的数据重新放到map中
                if (isUpdate) {
                    updateData.put(pinCard, personData.get(pinCard));
                } else {
                    insertData.put(pinCard, personData.get(pinCard));
                }
            }
        }
        if (conflictCardList.size() > 0) {
            setConflictCardToCache(conflictCardList, String.valueOf(cmdId));
        }
        ret.put("insert", String.valueOf(insertData.size()));
        ret.put("update", String.valueOf(updateData.size()));
        // pers_card插入和更新
        persCard(insertData, updateData, personItemMap, persCardItemMap);
        return ret;
    }

    /**
     * @param cardNoSet
     * @return
     * @Description: 获取人员[cardNo-pin]Map
     * <AUTHOR>
     * @date 2018/8/2 14:07
     */
    private Map<String, Set<String>> getPersonCardPinMap(Set<String> cardNoSet) {
        Map<String, Set<String>> persCardPinMap = Maps.newHashMap();
        List<PersCardItem> persCardItemList = Lists.newArrayList();
        List<List<String>> cardNosList = CollectionUtil.split(cardNoSet, AccConstants.LEVEL_SPLIT_COUNT);
        for (List<String> cardNoList : cardNosList) {
            persCardItemList.addAll(persCardService.getCardByCardNos(cardNoList));
        }
        for (PersCardItem persCardItem : persCardItemList) {
            Set<String> pinSet = null;
            if (!persCardPinMap.containsKey(persCardItem.getCardNo())) {
                pinSet = Sets.newHashSet();
            } else {
                pinSet = persCardPinMap.get(persCardItem.getCardNo());
            }
            pinSet.add(persCardItem.getPersonPin());
            persCardPinMap.put(persCardItem.getCardNo(), pinSet);
        }
        return persCardPinMap;
    }

    @Override
    public Map<String, String> addPersonFvTempalteRecord(String table, String data, Short commType) {
        // 0 1 2 3 4 5 6 7
        // UID,PIN2,FingerID,Fv_ID_Index,Duress,Size,Fv,UserCode
        // 1,2,7,1,1,701,WkZWAQwNAAAAAAAAEAkIASABAAAfWDtYGlgbXAI8AfwBPoOcWj6DHFTug04CzgLOM043yxdOFYYTHZtdWRwZXDA+Ud5xPkEccD4SHHAugkxwnqo8I06nnE8PpQ8bHxtfkR0JX7E5gJn4PMSscC5mPDi+sn0xuqctqxonCE8aJwoTNg8fES+HHxEuht8xrmSfkL4HPzG6YikxqjMpIzwzCV8ppwoTJ5cXEycXFzOvFhezp5ST46vgm/O78psxqvIroiujG2cpMygbJh0yGz4dkxknlSM5LjAnGruUtzOrspMjvoMvZzXnD28cVwx7Nl0TWTJ5MxknOS8ZIzAvEaMpjxurkgsyl5NPJxXHFUsZDQ3rI+0ReTLtsHki9TEZorE5WaOxKRmGMgs2nhZLN5UPDQMNzw03NzIJAAAAAAA+sDAADAAAHOAwAAcAAADgcaEDAAYAwHsAAwAGAMF/PgMAAABAfT6DAQAAAACehzEAAAAAD587gAWAOIABA+D/QRyAAQOP/ws+AAEAAP+//wEgAADw//8BAAAA4OP/PwAAAADA/38AAwAAwOP4AwMAAMBjgAMAAADg4wABAAB+8MEDAACH//gAAIAA//9/AMABAP/3/wDmAQAAwP8D5AEAAIAPAgAAAACAAQAAgAEAAAAAwIEDAAAACPAZAwAEABzwHwMADGAf8AMAAMD7n/EBAADg/4/7AQAA4P+H/2AYAAD8B/9xMAAAuAf+PwAAABwA7j8AAAAeAOY/AAAADgD4PwAIAAcAcHwA/gEHBAA8AM+HBwAAUACH/4MBPwEAAP6H7z8DAAD44/8fYzAA8Pf7jwcAAPDj8Q8PAAD4wcEDDAEA+AMAARgBAJwDPAAAAwAOAn8AuAMAHhh4AIAhAAAY+ACAMQAAHPgDhhkAAD94AgYAAAA4OAAAAAAAMDAAAAA=,0
        // 以上是pull上传数据

        // push
        // 0 1 2 3 4 5
        // fvtemplate pin=1 fingerid=6 index=1 size=701 duress=1 template=WkZWAQwN\r\nfvtemplate pin=1 fingerid=6
        // index=1 size=701 valid=1 template=WkZWAQwNA

        Map<String, String> rs = Maps.newHashMap();
        int count = 0;
        int insert = 0;
        int update = 0;
        String pin = "";
        String fingetId = "";
        String fvIdIndex = "";
        String template = "";
        String duress = "";
        int index = commType == ConstUtil.COMM_HTTP ? 0 : 1;
        String[] recordArr = data.replaceAll(table + " ", "").split("\r\n");
        if (recordArr != null && recordArr.length > 0) {
            String recordData = "";
            for (int i = index; i < recordArr.length; i++) {
                recordData = recordArr[i].trim();
                if (commType == ConstUtil.COMM_TCPIP || commType == ConstUtil.COMM_RS485) {
                    String[] recordArray = recordData.split(",");
                    pin = recordArray[1];
                    fingetId = recordArray[2];
                    if (StringUtils.isNotBlank(recordArray[3])) {
                        fvIdIndex = (Integer.parseInt(recordArray[3]) - 1) + "";
                    }
                    duress = recordArray[4];
                    template = recordArray[6];
                } else if (commType == ConstUtil.COMM_HTTP) {
                    String[] recordArray = recordData.split("\t");
                    pin = recordArray[0].split("=")[1];
                    fingetId = recordArray[1].split("=")[1];
                    if (StringUtils.isNotBlank(recordArray[2].split("=")[1])) {
                        fvIdIndex = (Integer.parseInt(recordArray[2].split("=")[1]) - 1) + "";
                    }
                    duress = recordArray[4].split("=")[1];
                    template = recordArray[5].split("=", 2)[1];
                }
                PersBioTemplateItem persBioTemplate =
                    checkPersonFvTemplateIsExit(pin, fingetId, fvIdIndex, BaseConstants.BaseBioType.VEIN_BIO_TYPE);
                if (persBioTemplate == null) {
                    if (addPersonFvTemplate(pin, fingetId, fvIdIndex, template, duress) && "1".equals(fvIdIndex)) {
                        // 当指静脉的索引为1时，记录到新增操作
                        insert++;
                    }
                } else {
                    persBioTemplate.setTemplate(template);
                    // 设置是否胁迫或有效
                    persBioTemplate.setValidType(Short.valueOf(duress));
                    updatePersonFvBioTemplate(persBioTemplate);
                    if ("1".equals(fvIdIndex)) {
                        // 当指静脉的索引为1时，记录到更新操作
                        update++;
                    }
                }
            }
            // 设置总数
            count = insert + update;
        } else {
            log.info("AccPersonBiz.addPersonFvTempalteRecord method: record null");
        }
        rs.put("count", count + "");
        rs.put("insert", insert + "");
        rs.put("update", update + "");
        return rs;
    }

    /**
     * @param persBioTemplate
     * @return
     * @Description: 更新人员指静脉数据
     * <AUTHOR>
     * @date 2018/7/4 14:03
     */
    private void updatePersonFvBioTemplate(PersBioTemplateItem persBioTemplate) {
        JSONObject bioTemplateJson = new JSONObject();
        Map<String, PersBioTemplateItem> bioTemplateList = new HashMap<>();
        bioTemplateList.put(persBioTemplate.getTemplateNo() + "_" + persBioTemplate.getTemplateNoIndex(),
            persBioTemplate);
        bioTemplateJson.put("fvList", bioTemplateList);// 指静脉数据
        persBioTemplateService.updateBioTemplateJson(persBioTemplate.getPersonId(), bioTemplateJson.toJSONString());
    }

    private boolean addPersonFvTemplate(String pin, String fingetId, String fvIdIndex, String template, String duress) {
        PersPersonItem person = persPersonService.getItemByPin(pin);
        if (person == null) {
            return false;
        } else {
            JSONObject bioTemplateJson = new JSONObject();
            Map<String, PersBioTemplateItem> bioTemplateList = Maps.newHashMap();
            PersBioTemplateItem persBioTemplate = new PersBioTemplateItem();
            // 指静脉固定7
            persBioTemplate.setBioType(BaseConstants.BaseBioType.VEIN_BIO_TYPE);
            persBioTemplate.setTemplateNo(Short.valueOf(fingetId));
            persBioTemplate.setTemplate(template);
            persBioTemplate.setTemplateNoIndex(Short.valueOf(fvIdIndex));
            persBioTemplate.setValidType(Short.valueOf(duress));
            // 人脸算法
            persBioTemplate.setVersion(BaseConstants.BaseBioType.VEIN_BIO_VERSION);
            persBioTemplate.setPersonId(person.getId());
            bioTemplateList.put(persBioTemplate.getTemplateNo() + "_" + persBioTemplate.getTemplateNoIndex(),
                persBioTemplate);
            bioTemplateJson.put("fvList", bioTemplateList);// 面部数据
            persBioTemplateService.updateBioTemplateJson(persBioTemplate.getPersonId(), bioTemplateJson.toJSONString());
            return true;
        }
    }

    private PersBioTemplateItem checkPersonFvTemplateIsExit(String pin, String fingetId, String fvIdIndex,
        Short veinBioType) {
        PersPersonItem person = persPersonService.getItemByPin(pin);
        if (Objects.nonNull(person)) {
            PersBioTemplateItem persBioTemplateItem = new PersBioTemplateItem();
            persBioTemplateItem.setPersonId(person.getId());
            persBioTemplateItem.setTemplateNo(Short.valueOf(fingetId));
            persBioTemplateItem.setTemplateNoIndex(Short.valueOf(fvIdIndex));
            persBioTemplateItem.setBioType(veinBioType);
            List<PersBioTemplateItem> bioTemplateItems = persBioTemplateService.getByCondition(persBioTemplateItem);
            if (!bioTemplateItems.isEmpty()) {
                return bioTemplateItems.get(0);
            }
        }
        return null;
    }

    @Override
    public void saveIdentityCardInfo(Map<String, String> idCardInfoMap, String systemFilePath) {
        String physicalNo = idCardInfoMap.get("SN_Num");// 物理卡号
        PersIdentityCardInfoItem idCardInfo = persIdentityCardInfoService.findByPhysicalNo(physicalNo);
        if (Objects.isNull(idCardInfo)) {
            idCardInfo = new PersIdentityCardInfoItem();
            idCardInfo.setIdCard(idCardInfoMap.get("ID_Num"));// 身份证
            idCardInfo.setPhysicalNo(idCardInfoMap.get("SN_Num"));// 物理卡号
            idCardInfo.setName(idCardInfoMap.get("Name"));// 姓名
            idCardInfo.setAddress(idCardInfoMap.get("Address"));// 地址
            idCardInfo.setGender(idCardInfoMap.get("Gender"));// 性别
            idCardInfo.setNation(idCardInfoMap.get("Nation"));// 民族
            idCardInfo.setIssuedOrgan(idCardInfoMap.get("Issuer"));// 签发机关
            String photoPath = saveIdentityCardPhoto(systemFilePath, idCardInfoMap.get("PhotoJPG"));
            idCardInfo.setPhotoPath(photoPath);// 图片路径
            idCardInfo.setBirthday(DateUtil.stringToDate(idCardInfoMap.get("Birthday"), DateUtil.DateStyle.YYYYMMDD));// 生日
            String validInfo = idCardInfoMap.get("Valid_Info");// 有效期
            idCardInfo.setStartDate(DateUtil.stringToDate(validInfo.split("-")[0], DateUtil.DateStyle.YYYYMMDD));
            // 身份证有效期为长期，设备传输的Valid_Info值只为有效开始时间，如：20100702或者20100702-,因此加此判断防止数据解析错误
            if (validInfo.split("-").length == 2) {
                idCardInfo.setEndDate(DateUtil.stringToDate(validInfo.split("-")[1], DateUtil.DateStyle.YYYYMMDD));
            }
            persIdentityCardInfoService.saveItem(idCardInfo);
        }
    }

    private String saveIdentityCardPhoto(String systemFilePath, String picBase64) {
        String photoPath = systemFilePath + "/pers/photo/";
        long nowTime = System.currentTimeMillis();
        String photoName = nowTime + ".jpg";
        File eventSoundFilePath = new File(photoPath);
        if (!eventSoundFilePath.isAbsolute()) { // 判断是否是绝对路径
            photoPath = ClassUtil.getRootPath() + "/" + photoPath;
            eventSoundFilePath = new File(photoPath);
        }
        if (!eventSoundFilePath.exists()) {
            eventSoundFilePath.mkdirs();
        }
        photoPath = photoPath + "/" + photoName;
        Base64Util.generateImage(picBase64, photoPath);// 生成图片
        return systemFilePath + "/pers/photo/" + photoName;
    }

    @Override
    public void savePersonPhoto(String data) {
        String pin = StringUtils.substringBetween(data, "pin=", "\t");
        PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
        if (persPersonItem != null) {
            String picBase64 = StringUtils.substringAfter(data, "content=");
            persPersonService.saveUserPhoto(pin, picBase64);
        }
    }

    @Override
    public List<String> splitPersonIds(String personIds, int splitSize) {
        List<String> idsList = new ArrayList<>();
        String splitStr = ",";
        // 说明含有时间;
        boolean containsTime = personIds.indexOf(";") > 0;
        if (containsTime) {
            splitStr = ";";
        }
        String[] idsArray = personIds.split(splitStr);
        StringBuilder sb = new StringBuilder();
        for (int j = 0; j < idsArray.length; j++) {
            sb.append(idsArray[j] + splitStr);
            if ((j + 1) % splitSize == 0) {
                idsList.add(sb.substring(0, sb.length() - 1));
                sb = new StringBuilder();
            }
        }
        if (sb.length() > 0) {
            idsList.add(containsTime ? sb.substring(0, sb.length()) : sb.substring(0, sb.length() - 1));
        }
        return idsList;
    }

    @Override
    public Map<String, String> addPersonBiophotoRecord(Short commType, String data, String table) {
        Map<String, String> rs = new HashMap<>();
        int count = 0;
        int insert = 0;
        int update = 0;
        String pin = "";
        String fileName = "";
        String picBase64 = "";
        String type = "";
        PersBioPhotoSaveItem item;
        // biophoto协议上是用\n连接多条数据，虽然正常都是一个包一条数据
        String[] dataArray = StringUtils.split(data.trim(), "\n");
        if (Objects.nonNull(dataArray)) {
            for (String record : dataArray) {
                pin = StringUtils.substringBetween(record, "pin=", "\t");
                PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
                if (Objects.nonNull(persPersonItem)) {
                    fileName = StringUtils.substringBetween(record, "filename=", "\t");
                    picBase64 = StringUtils.substringAfter(record, "content=");
                    type = StringUtils.substringBetween(record, "type=", "\t");
                    item = new PersBioPhotoSaveItem();
                    item.setPersonPin(pin);
                    item.setBioType(Short.parseShort(type));
                    item.setFileName(fileName);
                    item.setPhotoBase64(picBase64);
                    if (BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE.equals(item.getBioType())) {
                        // 可见光人脸类型才需要同时保存成人员头像
                        persBioPhotoService.saveBioPhotoAndUpdateAvatar(item);
                    } else {
                        persBioPhotoService.saveBioPhoto(item);
                    }
                    update++;
                }
            }
            // 设置总数
            count = insert + update;
        } else {
            log.info("accPersonBiz.addPersonBiophotoRecord method:record null");
        }
        rs.put("count", String.valueOf(count));
        rs.put("insert", String.valueOf(insert));
        rs.put("update", String.valueOf(update));
        return rs;
    }

    @Override
    public int getBiophotoCount(String devId) {
        int photoCount = 0;
        List<String> personIdList = accLevelService.searchPersonByDev(devId);
        if (personIdList != null && personIdList.size() > 0) {
            List<PersPersonItem> persPersonItemList = persPersonService.getItemsByIds(personIdList);
            for (PersPersonItem personItem : persPersonItemList) {
                String cropFacePath = FileUtil.getCropFacePath(personItem.getPin());
                File file = new File(
                    ClassUtil.getRootPath() + File.separator + "BioSecurityFile" + File.separator + cropFacePath);
                File[] files = file.listFiles();// 获取目录下的所有文件或文件夹
                if (files != null) {
                    // 遍历，目录下的所有文件
                    for (File f : files) {
                        if (f.isFile()) {
                            photoCount++;
                        }
                    }
                }
            }
        }
        return photoCount;
    }

    @Override
    public Map<String, String> addPersonBiodataRecord(Short commType, String data, String table) {
        Map<String, String> rs = new HashMap<>();
        int count = 0;
        int insert = 0;
        int update = 0;
        String pin = "";
        String type = "";
        String tempId = "";
        String tmpIndex = "";
        String version = "";
        String minorVer = "";
        String template = "";
        String duress = "";

        int index = commType == ConstUtil.COMM_HTTP ? 0 : 1;
        String[] recordArr = data.replaceAll(table + " ", "").split("\r\n");
        if (recordArr.length > 0) {
            String recordData = "";
            for (int i = index; i < recordArr.length; i++) {
                recordData = recordArr[i].trim();
                if (commType == ConstUtil.COMM_HTTP) {
                    String[] recordArray = recordData.split("\t");
                    pin = recordArray[0].split("=")[1];
                    type = recordArray[5].split("=")[1];
                    tempId = recordArray[1].split("=")[1];
                    tmpIndex = recordArray[2].split("=")[1];
                    version = recordArray[6].split("=")[1];
                    minorVer = recordArray[7].split("=")[1];
                    // 需要处理副版本字段，由于副版本不同时，模版是不兼容的，原来都没有处理副版本，故副版本为0时不处理，否则会有数据兼容问题
                    if (minorVer != null && !minorVer.equals("0")) {
                        version += "." + minorVer;
                    }
                    template = recordArray[9].split("=", 2)[1];
                    duress = recordArray[4].split("=")[1];
                }
                PersBioTemplateItem persBioTemplate =
                    checkPersonBiodataTemplateIsExist(pin, tempId, tmpIndex, type, version);
                if (persBioTemplate == null) {
                    if (addPersonBiodataTemplate(pin, tempId, tmpIndex, Short.parseShort(type), version, template,
                        duress) && "0".equals(tmpIndex)) {
                        insert++;
                    }
                } else {
                    persBioTemplate.setTemplate(template);
                    persBioTemplate.setDuress("1".equals(duress));
                    updatePersonBiodataTemplate(persBioTemplate);
                    if ("0".equals(tmpIndex)) {
                        update++;
                    }
                }
            }
            // 设置总数
            count = insert + update;
        } else {
            log.info("accPersonServiceImpl.addPersonBiodataRecord method:record null");
        }
        rs.put("count", count + "");
        rs.put("insert", insert + "");
        rs.put("update", update + "");
        return rs;
    }

    @Override
    public void handlerTransfer(List<AccPersonItem> accPersonItems) {
        // 数据量大的时候处理，分批处理
        List<List<AccPersonItem>> accPersonItemList = CollectionUtil.split(accPersonItems, CollectionUtil.splitSize);
        // 获取数据库已存在的门禁人员数据
        List<AccPerson> accPersonList = accPersonDao.findAll();
        Map<String, AccPerson> accPersonMap = CollectionUtil.listToKeyMap(accPersonList, AccPerson::getPersonId);
        // 获取人员集合 add by hql 20190805
        List<PersPersonItem> personItems = persPersonService.getByCondition(new PersPersonItem());
        Map<String, PersPersonItem> persPersonItemMap =
            CollectionUtil.listToKeyMap(personItems, PersPersonItem::getPin);
        for (List<AccPersonItem> itemList : accPersonItemList) {
            // 获取人员集合 根据pin
            // Collection<String> pins = CollectionUtil.getPropertyList(itemList, AccPersonItem::getPersonPin, "-1");
            // List<PersPersonItem> personItems = persPersonService.getItemsByPins(pins);
            // Map<String, PersPersonItem> persPersonItemMap = CollectionUtil.listToKeyMap(personItems,
            // PersPersonItem::getPin);
            // 获取多人开门人员对象
            for (AccPersonItem accPersonItem : itemList) {
                // 获取人员对象
                PersPersonItem persPersonItem = persPersonItemMap.get(accPersonItem.getPersonPin());
                AccPerson accPerson = accPersonMap.remove(persPersonItem.getId());
                if (Objects.isNull(accPerson)) {
                    accPerson = new AccPerson();
                }
                ModelUtil.copyPropertiesIgnoreNullWithProperties(accPersonItem, accPerson, "id");
                if (Objects.nonNull(persPersonItem)) {
                    accPerson.setPersonId(persPersonItem.getId());
                    accPersonDao.save(accPerson);
                }
            }
        }
        persPersonItemMap = null;
        accPersonMap = null;
        accPersonItemList = null;
    }

    @Override
    public void sendTemplateToDevs(String table, String data, Short commType, String sn) {
        // 指纹数据 templatev10 size=1312 uid=5 pin=3 fingerid=6 valid=1 template=SpV
        // 指静脉数据 fvtemplate pin=2 fingerid=6 index=2 size=936 duress=1 template=W
        // 人脸数据 facev7 pin=90000 faceid=11 size=1648 valid=1 face=AAAAA
        if (ConstUtil.TEMPLATEV10.equals(table) || AccConstants.FVTEMPLATE.equals(table)
            || ConstUtil.FACEV7.equals(table) || AccConstants.BIODATA.equals(table)) {
            // 根据devCQDataBean获取组装好的人员或生物模板的数据
            int index = (commType == ConstUtil.COMM_HTTP || ConstUtil.TEMPLATEV10.equals(table)) ? 0 : 1;
            String[] recordArr = data.replaceAll(table + " ", "").split("\r\n");
            List<PersBioTemplate2OtherItem> persBioTemplate2OtherItems = new ArrayList<>();

            if (recordArr != null && recordArr.length > 0) {
                String recordData = "";
                for (int i = index; i < recordArr.length; i++) {
                    Map<String, String> devMap = new HashMap<>();
                    recordData = recordArr[i].trim();
                    // 1.组装生物模板
                    AccBioTemplateItem bioTem = getBioByTableAndData(table, recordData, commType);
                    List<AccBioTemplateItem> bioTems = new ArrayList<>();
                    bioTems.add(bioTem);
                    PersPersonItem persItem = persPersonService.getItemByPin(bioTem.getPin());
                    if (persItem != null) {
                        List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonId(persItem.getId());
                        if (levelIdList != null && levelIdList.size() > 0) {
                            List<AccDevice> deviceList = accDeviceDao.getDevByLevel(levelIdList);
                            // 2.下发命令给该用户权限组下的设备
                            for (AccDevice device : deviceList) {
                                if (!device.getSn().equals(sn)) {
                                    // 下发生物模板信息
                                    accDevCmdManager.setPersonBioTemplateToDev(device.getSn(), bioTems, false);
                                }
                            }
                        }
                        PersBioTemplate2OtherItem persBioTemplate2OtherItem = new PersBioTemplate2OtherItem();
                        persBioTemplate2OtherItem.setPersonId(persItem.getId());
                        persBioTemplate2OtherItem.setPin(persItem.getPin());
                        persBioTemplate2OtherItem.setVersion(bioTem.getVersion());
                        persBioTemplate2OtherItem.setBioType(bioTem.getType());
                        persBioTemplate2OtherItem.setTemplateNo(bioTem.getTemplateId());
                        persBioTemplate2OtherItem.setTemplateNoIndex(bioTem.getTemplateIndex());
                        persBioTemplate2OtherItem.setTemplate(bioTem.getTemplate());
                        persBioTemplate2OtherItem.setDuress(bioTem.getDuress());
                        persBioTemplate2OtherItem.setValidType((short)1);
                        persBioTemplate2OtherItems.add(persBioTemplate2OtherItem);
                    }
                }
            }
            if (persBioTemplate2OtherItems.size() > 0) {
                pers2OtherService.setBioTemplate2OtherModule(persBioTemplate2OtherItems, ConstUtil.SYSTEM_MODULE_ACC);
            }
        }
    }

    @Override
    public void sendTemplateToDevs(String table, List<Map<String, String>> resolveMap, String sn) {
        Map<String, List<AccBioTemplateItem>> accBioTemplateItemMap = new HashMap<>();
        List<PersBioTemplate2OtherItem> persBioTemplate2OtherItems = new ArrayList<>();
        Set<String> pinSet = new HashSet<>();
        for (Map<String, String> data : resolveMap) {
            String pin = data.get("pin");
            if (StringUtils.isNotBlank(pin)) {
                pinSet.add(pin);
            }
        }
        Map<String, String> pinIds = persPersonService.getPinsAndIdsByPins(pinSet);
        String pin = "";
        String no = "";
        String index = "";
        String type = "";
        String version = "";
        boolean duress = false;
        String tmp = "";
        String valid = "";
        AccBioTemplateItem bioTemplateItem = null;
        List<AccBioTemplateItem> bioTemplateItemList = null;
        PersBioTemplate2OtherItem persBioTemplate2OtherItem = null;
        for (Map<String, String> data : resolveMap) {
            pin = data.get("pin");
            // 出现人员数据库中不存在的pin号模版不处理
            if (!pinIds.containsKey(pin)) {
                continue;
            }
            // 其他表传的模版先不处理，先只写biodata逻辑
            if (table.equals(ConstUtil.TEMPLATEV10)) {
                duress = false;
                no = data.get("fingerid");
                // 早期协议没有传index字段，默认为0
                index = "0";
                if (Integer.parseInt(no) > 10) {
                    // 如果协议传大于10的值，是做了+16处理，代表胁迫指纹
                    no = Integer.toString((Integer.parseInt(no) - 16));
                    duress = true;
                }
                type = String.valueOf(BaseConstants.BaseBioType.FP_BIO_TYPE);
                // TEMPLATEV10表存储的指纹版本固定为10
                version = BaseConstants.BaseBioType.FP_BIO_VERSION;
                valid = data.get("valid");
                // 协议上如果传3为胁迫指纹，需要处理为1存储，表示有效模版
                if ("3".equals(valid)) {
                    duress = true;
                    valid = "1";
                }
                tmp = data.get("template");
            } else if (table.equals(ConstUtil.FACEV7)) {
                // 人脸每人只有1个，早期协议设备没存no，默认为0
                no = "0";
                index = data.get("faceid");
                duress = false;
                type = String.valueOf(BaseConstants.BaseBioType.FACE_BIO_TYPE);
                version = BaseConstants.BaseBioType.FACE_BIO_VERSION;
                valid = data.get("valid");
                tmp = data.get("face");
            } else if (table.equals(AccConstants.FVTEMPLATE)) {
                no = data.get("fingerid");
                // 由于设备传的index从1开始，软件需要统一从0开始
                index = Integer.toString(Integer.parseInt(data.get("index")) - 1);
                duress = false;
                type = String.valueOf(BaseConstants.BaseBioType.VEIN_BIO_TYPE);
                version = BaseConstants.BaseBioType.VEIN_BIO_VERSION;
                // 设备没传该字段默认为1
                valid = "1";
                tmp = data.get("template");
            } else if (AccConstants.BIODATA.equals(table)) {
                no = data.get("no");
                index = data.get("index");
                type = data.get("type");
                version = data.get("majorver");
                if (!"0".equals(data.get("minorver"))) {
                    version += "." + data.get("minorver");
                }
                duress = false;// 0：非胁迫，1：胁迫，默认为0
                if (Objects.nonNull(data.get("duress")) && "1".equals(data.get("duress"))) {
                    duress = true;
                }
                valid = data.get("valid");
                tmp = data.get("tmp");
            }
            // 构造本模块下发数据
            bioTemplateItem = new AccBioTemplateItem();
            bioTemplateItem.setDuress(duress);
            bioTemplateItem.setPin(pin);
            bioTemplateItem.setTemplate(tmp);
            bioTemplateItem.setTemplateId(Short.valueOf(no));
            bioTemplateItem.setTemplateIndex(Short.valueOf(index));
            bioTemplateItem.setType(Short.valueOf(type));
            bioTemplateItem.setVersion(version);
            bioTemplateItem.setFormat(AccBioTemplateItem.FORMAT_BASE64);
            bioTemplateItemList = accBioTemplateItemMap.get(pin);
            if (bioTemplateItemList == null) {
                bioTemplateItemList = new ArrayList<>();
            }
            bioTemplateItemList.add(bioTemplateItem);
            accBioTemplateItemMap.put(pin, bioTemplateItemList);
            // 构造发送给其他模块数据
            persBioTemplate2OtherItem = new PersBioTemplate2OtherItem();
            persBioTemplate2OtherItem.setPersonId(pinIds.get(pin));
            persBioTemplate2OtherItem.setPin(pin);
            persBioTemplate2OtherItem.setVersion(version);
            persBioTemplate2OtherItem.setBioType(Short.valueOf(type));
            persBioTemplate2OtherItem.setTemplateNo(Short.valueOf(no));
            persBioTemplate2OtherItem.setTemplateNoIndex(Short.valueOf(index));
            persBioTemplate2OtherItem.setTemplate(tmp);
            persBioTemplate2OtherItem.setDuress(duress);
            persBioTemplate2OtherItem.setValidType(Short.valueOf(valid));
            persBioTemplate2OtherItems.add(persBioTemplate2OtherItem);
        }
        // 下发给本模块
        if (accBioTemplateItemMap.size() > 0) {
            for (String keyPin : accBioTemplateItemMap.keySet()) {
                List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonId(pinIds.get(keyPin));
                if (levelIdList != null && levelIdList.size() > 0) {
                    List<AccDevice> deviceList = accDeviceDao.getDevByLevel(levelIdList);
                    // 2.下发命令给该用户权限组下的设备
                    for (AccDevice device : deviceList) {
                        if (!device.getSn().equals(sn)) {
                            // 下发生物模板信息
                            accDevCmdManager.setPersonBioTemplateToDev(device.getSn(),
                                accBioTemplateItemMap.get(keyPin), false);
                        }
                    }
                }
            }
        }
        // 下发给其他模块
        if (persBioTemplate2OtherItems.size() > 0) {
            pers2OtherService.setBioTemplate2OtherModule(persBioTemplate2OtherItems, ConstUtil.SYSTEM_MODULE_ACC);
        }
    }

    /**
     * 根据表名称和数据组装生物模板item
     *
     * @return
     * <AUTHOR>
     * @Param [table, recordData, commType]
     * @since 2018-12-19 10:54
     */
    private AccBioTemplateItem getBioByTableAndData(String table, String recordData, Short commType) {
        String pin = "";
        String biotemplateNo = "";
        String biotemplateNoIndex = "1";
        String template = "";
        String valid = "";
        String version = "";
        // 副版本
        String minorVer = "";
        boolean isDuress = true;
        short type = BaseConstants.BaseBioType.FP_BIO_TYPE;
        if (ConstUtil.TEMPLATEV10.equals(table)) {
            String[] recordArray = recordData.split("\t");
            pin = recordArray[2].split("=")[1];
            biotemplateNo = recordArray[3].split("=")[1];
            template = recordArray[5].split("=", 2)[1];
            valid = recordArray[4].split("=", 2)[1];
            if (valid == null || "1".equals(valid)) {
                isDuress = false;
            }
            version = BaseConstants.BaseBioType.FP_BIO_VERSION;
        } else if (ConstUtil.FACEV7.equals(table)) {
            if (commType == ConstUtil.COMM_TCPIP || commType == ConstUtil.COMM_RS485) {
                String[] recordArray = recordData.split(",");
                pin = recordArray[1];
                biotemplateNo = recordArray[2];
                biotemplateNoIndex = recordArray[3];
                template = recordArray[4];
            } else if (commType == ConstUtil.COMM_HTTP) {
                String[] recordArray = recordData.split("\t");
                pin = recordArray[0].split("=")[1];
                biotemplateNo = "1";
                biotemplateNoIndex = String.valueOf(Integer.valueOf(recordArray[1].split("=")[1]));
                template = recordArray[4].split("=")[1];
            }
            isDuress = false;
            type = BaseConstants.BaseBioType.FACE_BIO_TYPE;
            version = BaseConstants.BaseBioType.FACE_BIO_VERSION;
        } else if (AccConstants.FVTEMPLATE.equals(table)) {
            if (commType == ConstUtil.COMM_TCPIP || commType == ConstUtil.COMM_RS485) {
                String[] recordArray = recordData.split(",");
                pin = recordArray[1];
                biotemplateNo = recordArray[2];
                // biotemplateNoIndex = recordArray[3];
                if (StringUtils.isNotBlank(recordArray[3])) {
                    biotemplateNoIndex = (Integer.parseInt(recordArray[3]) - 1) + "";
                }
                isDuress = "1".equals(recordArray[4]);
                template = recordArray[6];
            } else if (commType == ConstUtil.COMM_HTTP) {
                String[] recordArray = recordData.split("\t");
                pin = recordArray[0].split("=")[1];
                biotemplateNo = recordArray[1].split("=")[1];
                // biotemplateNoIndex = recordArray[2].split("=")[1];
                if (StringUtils.isNotBlank(recordArray[2].split("=")[1])) {
                    biotemplateNoIndex = (Integer.parseInt(recordArray[2].split("=")[1]) - 1) + "";
                }
                isDuress = "1".equals(recordArray[4].split("=")[1]);
                template = recordArray[5].split("=", 2)[1];
            }
            type = BaseConstants.BaseBioType.VEIN_BIO_TYPE;
            version = BaseConstants.BaseBioType.VEIN_BIO_VERSION;
        } else if (AccConstants.BIODATA.equals(table)) {
            String[] recordArray = recordData.split("\t");
            pin = recordArray[0].split("=")[1];
            biotemplateNo = recordArray[1].split("=")[1];
            biotemplateNoIndex = String.valueOf(Integer.valueOf(recordArray[2].split("=")[1]));
            template = recordArray[9].split("=", 2)[1];
            type = Short.parseShort(recordArray[5].split("=")[1]);
            version = recordArray[6].split("=")[1];
            minorVer = recordArray[7].split("=")[1];
            if (!"0".equals(minorVer)) {
                version += "." + minorVer;
            }
            isDuress = "1".equals(recordArray[4].split("=")[1]);
        }
        AccBioTemplateItem bioTemplateItem = new AccBioTemplateItem();
        bioTemplateItem.setDuress(isDuress);
        bioTemplateItem.setPin(pin);
        bioTemplateItem.setTemplate(template);
        bioTemplateItem.setTemplateId(Short.valueOf(biotemplateNo));
        bioTemplateItem.setTemplateIndex(Short.valueOf(biotemplateNoIndex));
        bioTemplateItem.setType(type);
        bioTemplateItem.setVersion(version);
        bioTemplateItem.setFormat(AccBioTemplateItem.FORMAT_BASE64);
        return bioTemplateItem;
    }

    @Override
    public void sendPersToDevs(String table, String data, Short commType, String sn) {
        String[] personDataArray = StringUtils.split(data.trim(), "\r\n");
        List<PersPersonInfo2OtherItem> persPersonInfo2OtherItems = new ArrayList<>();
        for (String recordData : personDataArray) {
            // 1.组装人员信息
            AccPersonOptItem persOptItem = getPersByTableAndData(table, recordData, commType, sn);
            PersPersonItem persPersonItem = persPersonService.getItemByPin(persOptItem.getPin());
            // 启用的人员才需要处理
            if (persPersonItem != null && persPersonItem.getEnabledCredential()) {
                PersCardItem persCard = persCardService.getMasterCardByPersonId(persPersonItem.getId());
                if (persCard != null) {
                    persOptItem.setCardNo(persCard.getCardNo());
                    persOptItem.setCardState(PersConstants.CARD_VALID);
                }
                persOptItem.setName(persPersonItem.getName());
                persOptItem.setLastName(persPersonItem.getLastName());
                List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonId(persPersonItem.getId());
                if (levelIdList != null && levelIdList.size() > 0) {
                    List<AccDevice> deviceList = accDeviceDao.getDevByLevel(levelIdList);
                    // 2.下发命令给该用户权限组下的设备
                    for (AccDevice device : deviceList) {
                        if (!device.getSn().equals(sn)) {
                            accDevCmdManager.sendPersToDev(device, persOptItem);
                        }
                    }
                }
                PersPersonInfo2OtherItem persPersonInfo2OtherItem = new PersPersonInfo2OtherItem();
                persPersonInfo2OtherItem.setPersonId(persPersonItem.getId());
                persPersonInfo2OtherItem.setPin(persOptItem.getPin());
                persPersonInfo2OtherItem.setName(persPersonItem.getName());
                persPersonInfo2OtherItem.setLastName(persPersonItem.getLastName());
                persPersonInfo2OtherItem.setCardNo(persOptItem.getCardNo());
                persPersonInfo2OtherItem.setPersonPwd(persOptItem.getPersonPwd());
                persPersonInfo2OtherItems.add(persPersonInfo2OtherItem);
            }
        }
        if (persPersonInfo2OtherItems.size() > 0) {
            // 给其他模块分发人员数据
            pers2OtherService.setPersonInfo2OtherModule(persPersonInfo2OtherItems, ConstUtil.SYSTEM_MODULE_ACC);
        }
    }

    /**
     * 组装下发人员的信息
     *
     * @return
     * <AUTHOR>
     * @Param [table, recordData, commType, sn]
     * @since 2018-12-19 10:55
     */
    private AccPersonOptItem getPersByTableAndData(String table, String recordData, Short commType, String sn) {
        AccPersonOptItem accPersonOptItem = new AccPersonOptItem();
        String[] fieldArr = StringUtils.split(recordData.split(table)[1].trim(), "\t");
        // SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            if (AccConstants.MUL_CARD_USER.equals(table)) {
                String[] keyValArr = fieldArr[0].trim().split("=", 2);
                String pin = keyValArr[1];
                PersPersonItem person = persPersonService.getItemByPin(pin);
                if (person != null) {
                    AccPersonInfoItem accPersonInfoItem =
                        accLevelService.getPersonByIds(StrUtil.strToList(person.getId()));
                    if (accPersonInfoItem != null && accPersonInfoItem.getPersonList().size() > 0) {
                        accPersonOptItem = accPersonInfoItem.getPersonList().get(0);
                    }
                }
            } else if (ConstUtil.USER.equals(table)) {
                String[] keyValArr = fieldArr[2].trim().split("=", 2);
                PersPersonItem person = persPersonService.getItemByPin(keyValArr[1]);
                if (person != null) {
                    // 开始结束时间、黑名单、设备操作权限等不处理，只处理基本信息
                    /*String startTime = fieldArr[5].split("=")[1];
                    String endTime = fieldArr[6].split("=")[1];
                    
                    //AccDevice dev = accDeviceDao.findBySn(sn);
                    if (!"0".equals(startTime) && StringUtils.isNotBlank(startTime)) {
                        accPersonOptItem.setStartTime(dateTimeFormat.parse(startTime));
                    } else {
                        accPersonOptItem.setStartTime(null);
                    }
                    if (!"0".equals(endTime) && StringUtils.isNotBlank(endTime)) {
                        accPersonOptItem.setEndTime(dateTimeFormat.parse(endTime));
                    } else {
                        accPersonOptItem.setEndTime(null);
                    }*/
                    accPersonOptItem.setPin(keyValArr[1]);
                    keyValArr = fieldArr[7].trim().split("=", 2);
                    accPersonOptItem.setName(keyValArr[1]);
                    keyValArr = fieldArr[3].trim().split("=", 2);
                    accPersonOptItem.setPersonPwd(keyValArr[1]);
                    /*keyValArr = fieldArr[9].trim().split("=", 2);
                    accPersonOptItem.setDisabled("1".equals(keyValArr[1]));
                    keyValArr = fieldArr[8].trim().split("=", 2);
                    accPersonOptItem.setPrivilege(Short.parseShort(keyValArr[1]));
                    keyValArr = fieldArr[4].trim().split("=", 2);
                    accPersonOptItem.setGroupId(Long.parseLong(keyValArr[1]));*/
                }
            }

        } catch (Exception e) {
            log.error("getPersByTableAndData dateFormat error", e);
        }

        return accPersonOptItem;
    }

    @Override
    public void sendMulCardUserInfoToDevs(String table, String data, String sn) {
        String[] mulCardDataArray = StringUtils.split(data.trim(), "\r\n");
        for (String mulCardData : mulCardDataArray) {
            String valueStr;
            String cardNo = "";
            Short cardType = 0;
            String pin = "";
            String cardNoOld = "";
            String[] fieldArr = StringUtils.split(mulCardData.split(table)[1].trim(), "\t");
            for (int i = 0; i < fieldArr.length; i++) {
                valueStr = "";
                if (fieldArr[i].trim().split("=").length > 1) {
                    valueStr = fieldArr[i].trim().split("=")[1];
                }
                if (fieldArr[i].contains("pin")) {
                    pin = valueStr;
                } else if (fieldArr[i].contains("cardno")) {
                    cardNo = valueStr;
                } else if (fieldArr[i].contains("cardtype")) {
                    cardType = Short.parseShort(valueStr);
                }
            }

            // 1.组装人员信息
            AccPersonOptItem persOptItem = getPersByTableAndData(table, mulCardData, (short)3, sn);
            PersPersonItem persItem = null;
            persItem = persPersonService.getItemByPin(pin);
            String persId = "";
            if (persItem != null) {
                persId = persItem.getId();
                persOptItem.setId(persItem.getId());
                PersCardItem persCard = null;
                persCard = persCardService.getMasterCardByPersonId(persId);
                if (persCard != null) {
                    cardNoOld = persCard.getCardNo();
                }
                // 2.下发命令给该用户权限组下的设备
                List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonId(persId);
                if (levelIdList != null && levelIdList.size() > 0) {
                    List<AccDevice> deviceList = accDeviceDao.getDevByLevel(levelIdList);
                    for (AccDevice device : deviceList) {
                        String tempCardNo = cardNo;
                        boolean cardDecimal = false;
                        if (!device.getSn().equals(sn)) {
                            AccPersonOptItem optItem = new AccPersonOptItem();
                            optItem.setPin(pin);
                            optItem.setCardType(cardType);
                            ModelUtil.copyProperties(persOptItem, optItem);
                            boolean isMulCardUser = accDeviceOptionService.getAccSupportFunListVal(device.getId(), 7);
                            if (StringUtils.isNotBlank(tempCardNo)) {
                                if (!isMulCardUser)// 表示当前不支持多卡并且是16进制卡，此类固件需要下发10进制数据
                                {
                                    tempCardNo = AccDeviceUtil.convertCardNo(tempCardNo, 16, 10);
                                    cardDecimal = true; // 表示当前卡号已经变成十进制
                                }
                            }
                            if (!isMulCardUser) {
                                if (cardType == PersConstants.DEPUTY_CARD) {
                                    tempCardNo = cardNoOld;
                                }
                                optItem.setCardNo(tempCardNo);
                                accDevCmdManager.sendCmdUserInfoToDev(device, optItem, false);
                            } else if (isMulCardUser && !StringUtils.isEmpty(tempCardNo)) {// 根据参数判断是否支持一人多卡表
                                if (cardDecimal) {
                                    tempCardNo = AccDeviceUtil.convertCardNo(tempCardNo, 10, 16);
                                }
                                optItem.setCardNo(tempCardNo);
                                accDevCmdManager.sendMulCardUserInfoToDev(device, optItem, false);
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public void sendExtInfoToDevs(String table, String data, String sn) {
        String[] personDataArray = StringUtils.split(data.trim(), "\r\n");
        String pin = "";
        String funSwitch = "";
        String firstName = "";
        String lastName = "";
        String personAlvs = "";
        String valueStr;
        // 当前系统卡号进制
        String cardhex = baseSysParamService.getValByName("pers.cardHex");
        for (String recordData : personDataArray) {
            String[] fieldArr = StringUtils.split(recordData.split(table)[1].trim(), "\t");
            for (int i = 0; i < fieldArr.length; i++) {
                valueStr = "";
                if (fieldArr[i].trim().split("=").length > 1) {
                    valueStr = fieldArr[i].trim().split("=")[1];
                }
                if (fieldArr[i].contains("pin")) {
                    pin = valueStr;
                } else if (fieldArr[i].contains("funswitch")) {
                    funSwitch = valueStr;
                } else if (fieldArr[i].contains("firstname")) {
                    firstName = valueStr;
                } else if (fieldArr[i].contains("lastname")) {
                    lastName = valueStr;
                } else if (fieldArr[i].contains("personalvs")) {
                    personAlvs = valueStr;
                }
            }
            PersPersonItem persItem = persPersonService.getItemByPin(pin);
            if (persItem != null) {
                List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonId(persItem.getId());
                if (levelIdList != null && levelIdList.size() > 0) {
                    List<AccDevice> deviceList = accDeviceDao.getDevByLevel(levelIdList);
                    AccPersonOptItem accPersonOptItem = new AccPersonOptItem();
                    // 2.下发命令给该用户权限组下的设备
                    for (AccDevice device : deviceList) {
                        if (!device.getSn().equals(sn)) {
                            boolean isLongNameOn = accDeviceOptionService.getAccSupportFunListVal(device.getId(), 5);
                            String delayPassage = "";
                            delayPassage = AccDataUtil.getFunswitchByIndex(funSwitch, 0);
                            accPersonOptItem.setDelayPassage("1".equals(delayPassage));
                            String tempUser = "";
                            tempUser = AccDataUtil.getFunswitchByIndex(funSwitch, 1);
                            accPersonOptItem.setTempUser("1".equals(tempUser));
                            firstName = persItem.getName();
                            lastName = persItem.getLastName();
                            accPersonOptItem.setName(firstName);
                            accPersonOptItem.setLastName(lastName);
                            accPersonOptItem.setPin(pin);
                            String name = "";
                            if (!isLongNameOn
                                && (StringUtils.isNotBlank(firstName) || StringUtils.isNotBlank(lastName))) {
                                AccPersonInfoItem accPersonInfoItem =
                                    accLevelService.getPersonByIds(StrUtil.strToList(persItem.getId()));
                                if (accPersonInfoItem != null && accPersonInfoItem.getPersonList().size() > 0) {
                                    accPersonOptItem = accPersonInfoItem.getPersonList().get(0);
                                }
                                name = (firstName + " " + lastName).trim();
                                accPersonOptItem.setName(name);
                            }
                            // 是否支持多卡
                            boolean isMulCardUser = accDeviceOptionService.isSupportFunList(device.getSn(), 7);
                            String cardNo = "";
                            if (StringUtils.isNotBlank(persItem.getCardNos())) {
                                List<String> cardNoList = Arrays
                                    .asList(StringUtils.split(persItem.getCardNos(), PersConstants.IMPORT_CARD_SPLIT))
                                    .stream().distinct().collect(Collectors.toList());
                                // 主卡卡号
                                cardNo = cardNoList.get(0);
                            }
                            if (StringUtils.isNotBlank(cardNo)) {
                                if (isMulCardUser) {
                                    if (cardhex.equals("0")) {
                                        // 表示当前支持多卡并且是10进制卡，此类固件需要下发16进制数据
                                        cardNo = AccDeviceUtil.convertCardNo(cardNo, 10, 16);
                                    }
                                } else if (cardhex.equals("1")) {
                                    // 表示当前不支持多卡并且是16进制卡，此类固件需要下发10进制数据
                                    cardNo = AccDeviceUtil.convertCardNo(cardNo, 16, 10);
                                }
                                accPersonOptItem.setCardNo(cardNo);
                            }
                            accDevCmdManager.sendCmdUserInfoToDev(device, accPersonOptItem, false);
                            accDevCmdManager.sendExtInfoToDev(device, accPersonOptItem, false);
                        }
                    }
                }
            }
        }
    }

    private void updatePersonBiodataTemplate(PersBioTemplateItem persBioTemplate) {
        List<PersBioTemplateItem> bioTemplateItemList = new ArrayList<>();
        bioTemplateItemList.add(persBioTemplate);
        persBioTemplateService.updateBioTemplate(bioTemplateItemList);
    }

    private boolean addPersonBiodataTemplate(String pin, String tempId, String tmpIndex, short veinBioType,
        String version, String template, String duress) {
        PersPersonItem person = persPersonService.getItemByPin(pin);
        if (person == null) {
            return false;
        } else {
            List<PersBioTemplateItem> bioTemplateList = new ArrayList<>();
            PersBioTemplateItem persBioTemplate = new PersBioTemplateItem();
            persBioTemplate.setBioType(veinBioType);
            persBioTemplate.setTemplateNo(Short.valueOf(tempId));
            persBioTemplate.setTemplate(template);
            persBioTemplate.setTemplateNoIndex(Short.valueOf(tmpIndex));
            persBioTemplate.setValidType((short)1);
            persBioTemplate.setDuress("1".equals(duress));
            persBioTemplate.setVersion(version);
            persBioTemplate.setPersonId(person.getId());
            persBioTemplate.setPersonPin(pin);
            bioTemplateList.add(persBioTemplate);
            persBioTemplateService.updateBioTemplate(bioTemplateList);
            return true;
        }
    }

    private PersBioTemplateItem checkPersonBiodataTemplateIsExist(String pin, String tempId, String tmpIndex,
        String veinBioType, String version) {
        PersBioTemplateItem persBioTemplateItem = new PersBioTemplateItem();
        persBioTemplateItem.setTemplateNo(Short.valueOf(tempId));
        persBioTemplateItem.setTemplateNoIndex(Short.valueOf(tmpIndex));
        persBioTemplateItem.setBioType(Short.valueOf(veinBioType));
        persBioTemplateItem.setVersion(version);
        persBioTemplateItem.setPersonPin(pin);
        List<PersBioTemplateItem> bioTemplateItems = persBioTemplateService.getByCondition(persBioTemplateItem);
        if (!bioTemplateItems.isEmpty()) {
            return bioTemplateItems.get(0);
        }
        return null;
    }

    /**
     * 发送对比图片到设备（先保存数据库再下发权限组其他设备）
     *
     * @param table
     * @param data
     * @param sn
     */
    @Override
    public void sendBioPhotoInfoToDevs(String table, String data, String sn) {
        String[] personDataArray = StringUtils.split(data.trim(), "\r\n");
        String pin = "";
        String picBase64 = "";
        String bioType = "";
        List<PersBioPhoto2OtherItem> items = new ArrayList<>();
        for (String recordData : personDataArray) {
            pin = StringUtils.substringBetween(recordData, "pin=", "\t");
            picBase64 = StringUtils.substringAfter(recordData, "content=");
            bioType = StringUtils.substringBetween(recordData, "type=", "\t");
            // 1.组装 pin,Content(BioPhotoPath),id用户id
            AccPersonOptItem bioPhotoItem = new AccPersonOptItem();
            bioPhotoItem.setPin(pin);
            bioPhotoItem.setBioPhotoBase64(picBase64);
            // 2.通过pin获取用户
            PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
            // 3.通过用户获取权限组
            if (persPersonItem != null) {
                List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonId(persPersonItem.getId());
                if (levelIdList != null && levelIdList.size() > 0) {
                    List<AccDevice> deviceList = accDeviceDao.getDevByLevel(levelIdList);
                    // 2.下发命令给该用户权限组下的设备
                    for (AccDevice device : deviceList) {
                        if (!device.getSn().equals(sn)) {
                            accDevCmdManager.sendBioPhotoInfoToDev(device.getSn(), bioPhotoItem, (short)0, false);
                            // 拓展接口，为了兼容华为pad设备下发downLoad命令通知设备已下发完成的功能 by mingfa.zheng 20200727
                            accDevCmdManager.downloadOver(device.getSn(), false);
                        }
                    }
                }
                PersBioPhoto2OtherItem persBioPhoto2OtherItem = new PersBioPhoto2OtherItem();
                persBioPhoto2OtherItem.setPersonId(persPersonItem.getId());
                persBioPhoto2OtherItem.setPin(pin);
                persBioPhoto2OtherItem.setBioType(Short.parseShort(bioType));
                persBioPhoto2OtherItem.setContent(picBase64);
                items.add(persBioPhoto2OtherItem);
            }
        }
        if (items.size() > 0) {
            pers2OtherService.setBioPhoto2OtherModule(items, ConstUtil.SYSTEM_MODULE_ACC);
        }
    }

    @Override
    public String getPersPersonPhotoPathByPin(String pin) {
        return persPersonService.getPersonPhotoPathByPin(pin);
    }

    @Override
    public void sendUserPicToDevs(String table, String data, String sn) {
        String[] personDataArray = StringUtils.split(data.trim(), "\n");
        String pin = "";
        String picBase64 = "";
        List<PersUserPic2OtherItem> items = new ArrayList<>();
        for (String recordData : personDataArray) {
            pin = StringUtils.substringBetween(recordData, "pin=", "\t");
            picBase64 = StringUtils.substringAfter(data, "content=");
            AccPersonOptItem accPersonOptItem = new AccPersonOptItem();
            accPersonOptItem.setPin(pin);
            accPersonOptItem.setPhotoBase64(picBase64);
            // 2.通过pin获取用户
            PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
            // 3.通过用户获取用户所在权限组下的设备
            if (persPersonItem != null) {
                List<AccDevice> accDeviceList = accDeviceDao.findByPersonId(persPersonItem.getId());
                // 下发命令给该用户权限组下的设备
                for (AccDevice device : accDeviceList) {
                    if (!device.getSn().equals(sn)) {
                        accDevCmdManager.sendUserPicToDev(device.getSn(), accPersonOptItem, false);
                    }
                }
                PersUserPic2OtherItem item = new PersUserPic2OtherItem();
                item.setPersonId(persPersonItem.getId());
                item.setPin(pin);
                item.setContent(picBase64);
                items.add(item);
            }
        }
        if (items.size() > 0) {
            pers2OtherService.setUserPic2OtherModule(items, ConstUtil.SYSTEM_MODULE_ACC);
        }
    }

    @Override
    public boolean checkShowSMS() {
        return persPersonService.checkShowSMS();
    }

    @Override
    public Map<String, Integer> dealBioTemplate(String table, List<Map<String, String>> resolveMap) {
        Map<String, Integer> retMap = new HashMap<>();
        Set<String> pinSet = new HashSet<>();
        List<PersBioTemplateItem> updateData = new ArrayList<>(); // 将已经插入数据库中的记录放到updateDate的Map中
        List<PersBioTemplateItem> insertData = new ArrayList<>(); // 需要新增记录记录放到insertData的Map中
        int insertCount = 0;
        int updateCount = 0;
        for (Map<String, String> data : resolveMap) {
            String pin = data.get("pin");
            if (StringUtils.isNotBlank(pin)) {
                pinSet.add(pin);
            }
        }
        Map<String, String> pinIds = persPersonService.getPinsAndIdsByPins(pinSet);
        List<PersBioTemplateItem> bioTemplateItemList =
            persBioTemplateService.getItemByPersonIdList(new ArrayList<>(pinIds.values()));
        Map<String, PersBioTemplateItem> persBioTemplateMap = putPersBioTemplateToMap(bioTemplateItemList);
        String compareKey = "";
        String pin = "";
        String no = "";
        String index = "";
        String type = "";
        String version = "";
        boolean duress = false;
        String tmp = "";
        String valid = "";
        PersBioTemplateItem item = null;
        for (Map<String, String> data : resolveMap) {
            pin = data.get("pin");
            // 出现人员数据库中不存在的pin号模版不处理
            if (!pinIds.containsKey(pin)) {
                continue;
            }
            // 其他表传的模版先不处理，先只写biodata逻辑
            if (table.equals(ConstUtil.TEMPLATEV10)) {
                duress = false;
                no = data.get("fingerid");
                // 早期协议没有传index字段，默认为0
                index = "0";
                if (Integer.parseInt(no) > 10) {
                    // 如果协议传大于10的值，是做了+16处理，代表胁迫指纹
                    no = Integer.toString((Integer.parseInt(no) - 16));
                    duress = true;
                }
                type = String.valueOf(BaseConstants.BaseBioType.FP_BIO_TYPE);
                // TEMPLATEV10表存储的指纹版本固定为10
                version = BaseConstants.BaseBioType.FP_BIO_VERSION;
                valid = data.get("valid");
                // 协议上如果传3为胁迫指纹，需要处理为1存储，表示有效模版
                if ("3".equals(valid)) {
                    duress = true;
                    valid = "1";
                }
                tmp = data.get("template");
            } else if (table.equals(ConstUtil.FACEV7)) {
                // 人脸每人只有1个，早期协议设备没存no，默认为0
                no = "0";
                index = data.get("faceid");
                duress = false;
                type = String.valueOf(BaseConstants.BaseBioType.FACE_BIO_TYPE);
                version = BaseConstants.BaseBioType.FACE_BIO_VERSION;
                valid = data.get("valid");
                tmp = data.get("face");
            } else if (table.equals(AccConstants.FVTEMPLATE)) {
                no = data.get("fingerid");
                // 由于设备传的index从1开始，软件需要统一从0开始
                index = Integer.toString(Integer.parseInt(data.get("index")) - 1);
                duress = false;
                type = String.valueOf(BaseConstants.BaseBioType.VEIN_BIO_TYPE);
                version = BaseConstants.BaseBioType.VEIN_BIO_VERSION;
                // 设备没传该字段默认为1
                valid = "1";
                tmp = data.get("template");
            } else if (AccConstants.BIODATA.equals(table)) {
                no = data.get("no");
                index = data.get("index");
                type = data.get("type");
                version = data.get("majorver");
                if (!"0".equals(data.get("minorver"))) {
                    version += "." + data.get("minorver");
                }
                duress = false;// 0：非胁迫，1：胁迫，默认为0
                if (Objects.nonNull(data.get("duress")) && "1".equals(data.get("duress"))) {
                    duress = true;
                }
                valid = data.get("valid");
                tmp = data.get("tmp");
            }
            compareKey = pin + "_" + no + "_" + index + "_" + type + "_" + version;
            if (persBioTemplateMap.containsKey(compareKey)) {
                item = persBioTemplateMap.get(compareKey);
                item.setTemplate(tmp);
                item.setDuress(duress);
                updateData.add(item);
                updateCount++;
            } else {
                item = new PersBioTemplateItem();
                item.setPersonPin(pin);
                item.setPersonId(pinIds.get(pin));
                item.setTemplateNo(Short.valueOf(no));
                item.setTemplateNoIndex(Short.valueOf(index));
                item.setBioType(Short.valueOf(type));
                item.setDuress(duress);
                item.setTemplate(tmp);
                item.setVersion(version);
                item.setValidType(Short.valueOf(valid));
                insertData.add(item);
                insertCount++;
            }
        }
        if (insertData.size() > 0) {
            persBioTemplateService.updateBioTemplate(insertData);// 更新人员指纹模板,保存到数据库
        }
        if (updateData.size() > 0) {
            persBioTemplateService.updateBioTemplate(updateData);// 更新人员指纹模板,保存到数据库
        }
        retMap.put("count", resolveMap.size());
        retMap.put("insert", insertCount);
        retMap.put("update", updateCount);
        return retMap;
    }

    /**
     * 组装集合用于模版新增，更新比较
     *
     * @param bioTemplateItems
     * @return
     */
    private Map<String, PersBioTemplateItem> putPersBioTemplateToMap(List<PersBioTemplateItem> bioTemplateItems) {
        Map<String, PersBioTemplateItem> persBioTemplateMap = new HashMap<>();
        for (PersBioTemplateItem bioTemplateItem : bioTemplateItems) {
            String key = bioTemplateItem.getPin() + "_" + bioTemplateItem.getTemplateNo() + "_"
                + bioTemplateItem.getTemplateNoIndex() + "_" + bioTemplateItem.getBioType() + "_"
                + bioTemplateItem.getVersion();
            persBioTemplateMap.put(key, bioTemplateItem);
        }
        return persBioTemplateMap;
    }

    /**
     * 解析mqtt设备上传人员数据
     *
     * @param data
     * @return
     * <AUTHOR>
     */
    private Map<String, Map<String, String>> getDataMapByMqtt(String table, String data) {
        Map<String, Map<String, String>> retMap = new HashMap<>();
        Map<String, String> fieldMap;
        if (StringUtils.isNoneBlank(data)) {
            JSONArray jsonArr = JSONObject.parseArray(data);
            for (int i = 0; i < jsonArr.size(); i++) {
                fieldMap = new HashMap<>();
                JSONObject json = jsonArr.getJSONObject(i);
                for (String key : MQTT_PUSH_PARAM.keySet()) {
                    if (json.containsKey(key)) {
                        String val = json.getString(key);
                        String pushKey = MQTT_PUSH_PARAM.get(key);
                        fieldMap.put(pushKey, val);
                    }
                }
                // 过滤访客人员，访客pin号为8开头的9位数字
                if (!(fieldMap.get("pin").trim().length() == 9 && fieldMap.get("pin").trim().startsWith("8"))) {
                    Map<String, String> getMap = getExtendMap(table, fieldMap);
                    if (getMap.containsKey("compareKey")) {
                        retMap.put(getMap.get("compareKey"), getMap);
                    }
                }
            }
        }
        return retMap;
    }

    @Override
    public Pager getNoExistLevelPerson(String sessionId, AccSelectPersonItem condition, int pageNo, int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        condition.setEnabledCredential(true);
        return getItemsByPage(condition, pageNo, pageSize);
    }

    @Override
    public Pager getFirstOpenPersonItemList(String sessionId, AccPersonFirstOpenItem condition, int pageNo,
        int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        Pager pager = accPersonFirstOpenDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo,
            pageSize);
        List<AccPersonFirstOpenItem> items = (List<AccPersonFirstOpenItem>)pager.getData();
        for (AccPersonFirstOpenItem item : items) {
            item.setPersonPin(item.getPersonPin());
        }
        return pager;
    }

    @Override
    public Pager getCombOpenPersonItemList(String sessionId, AccPersonCombOpenPersonItem condition, int pageNo,
        int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        Pager pager =
            accPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        List<AccPersonCombOpenPersonItem> items = (List<AccPersonCombOpenPersonItem>)pager.getData();
        for (AccPersonCombOpenPersonItem item : items) {
            item.setPersonPin(item.getPersonPin());
        }
        return pager;
    }

    @Override
    public Pager getPersonVerifyModeRuleItemList(String sessionId, AccPersonVerifyModeRuleItem condition, int pageNo,
        int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        Pager pager =
            accPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        List<AccPersonVerifyModeRuleItem> items = (List<AccPersonVerifyModeRuleItem>)pager.getData();
        for (AccPersonVerifyModeRuleItem item : items) {
            item.setPersonPin(item.getPersonPin());
        }
        return pager;
    }

    @Override
    public List<AccPersonLevelByLevelExportItem> getExportLevelPersonItemListByAuthFilter(
        AccPersonLevelByLevelExportItem accLevelPersonItem, int beginIndex, int endIndex, String sessionId) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            accLevelPersonItem.setUserId(userId);
        }
        return accPersonDao.getItemsDataBySql(AccPersonLevelByLevelExportItem.class,
            SQLUtil.getSqlByItem(accLevelPersonItem), beginIndex, endIndex, true);
    }

    @Override
    public Pager getDoorPersonByAuthFilter(String sessionId, AccTransactionDoorPersonItem condition, int pageNo,
        int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        Pager pager =
            accLevelDoorDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        return pager;
    }
}