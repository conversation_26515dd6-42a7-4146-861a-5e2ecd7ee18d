/**
 * File Name: AccReaderServiceImpl Created by GenerationTools on 2018-03-13 上午10:06 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccDoorDao;
import com.zkteco.zkbiosecurity.acc.dao.AccReaderDao;
import com.zkteco.zkbiosecurity.acc.dao.AccReaderOptionDao;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.model.AccDoor;
import com.zkteco.zkbiosecurity.acc.model.AccReader;
import com.zkteco.zkbiosecurity.acc.model.AccReaderOption;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.license.provider.BaseLicenseProvider;
import com.zkteco.zkbiosecurity.system.service.ModuleInfoService;
import com.zkteco.zkbiosecurity.vid.service.Vid4OtherGetVidChannel2EntityService;

/**
 * 对应百傲瑞达 AccReaderServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-13 上午10:06
 * @version v1.0
 */
@Service
@Transactional
public class AccReaderServiceImpl implements AccReaderService {
    @Autowired
    private AccReaderDao accReaderDao;
    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AccReaderOptionDao accReaderOptionDao;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private ModuleInfoService moduleInfoService;
    @Autowired(required = false)
    private Vid4OtherGetVidChannel2EntityService vid4OtherGetVidChannel2EntityService;
    @Autowired(required = false)
    private Acc4PatrolPointService acc4PatrolPointService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private BaseLicenseProvider baseLicenseProvider;
    @Autowired
    private AccExtDeviceService accExtDeviceService;
    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private AccChannelService accChannelService;
    @Autowired(required = false)
    private AccReader4OtherService[] accReader4OtherServices;

    @Override
    public AccReaderItem saveItem(AccReaderItem item, String applyTo, String readerModel, String readMode) {
        AccReader accReader = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accReaderDao.findById(id)).orElse(new AccReader());
        ModelUtil.copyPropertiesIgnoreNull(item, accReader);
        accReaderDao.save(accReader);
        updateReaderCache(accReader);

        List<AccReader> readerList =
            accReaderDao.getOtherReaderByDevId(accReader.getAccDoor().getDevice().getId(), accReader.getId());
        // 读头加密设置需要复制到同一设备所有读头
        if (accReader.getCommType() != null) {
            Boolean isEncrypt = accReader.getReaderEncrypt();
            for (AccReader accReaderOther : readerList) {
                if (applyTo.equals("1")) {
                    accReaderOther.setCommType(accReader.getCommType());

                }
                accReaderOther.setReaderEncrypt(isEncrypt);
                accReaderDao.save(accReaderOther);
            }
        }

        List<AccReader> accReaderList = new ArrayList<>();
        accReaderList.add(accReader);
        if (applyTo.equals("1")) {
            accReaderList.addAll(readerList);
        }
        accDevCmdManager.setReaderOptToDev(accReader.getAccDoor().getDevice(), accReaderList, false);

        // TCP读头需要配置参数
        if (accReader.getCommType() != null && accReader.getCommType() == AccConstants.READER_TYPE_NETWORK) {
            AccReaderOption readerModelOpt =
                accReaderOptionDao.findByAccReader_IdAndName(accReader.getId(), "ReaderModel");
            if (readerModelOpt == null) {
                readerModelOpt = new AccReaderOption();
                readerModelOpt.setAccReader(accReader);
                readerModelOpt.setName("ReaderModel");
                readerModelOpt.setType((short)0);
                accReader.getAccReaderOptionSet().add(readerModelOpt);
            }
            readerModelOpt.setValue(readerModel);
            accReaderOptionDao.save(readerModelOpt);

            if ("FR5200".equals(readerModel)) {
                AccReaderOption accReaderOpt =
                    accReaderOptionDao.findByAccReader_IdAndName(accReader.getId(), "IdentityCardVerifyMode");
                if (accReaderOpt == null) {
                    accReaderOpt = new AccReaderOption();
                    accReaderOpt.setAccReader(accReader);
                    accReaderOpt.setName("IdentityCardVerifyMode");
                    accReaderOpt.setType((short)1);
                    accReader.getAccReaderOptionSet().add(accReaderOpt);
                }
                int mode = Integer.parseInt(readMode);
                if (AccConstants.READER_READMODE_NORMAL == mode)// 普通模式
                {
                    accReaderOpt.setValue(String.valueOf(AccConstants.READER_READMODE_NORMAL));
                } else if (AccConstants.READER_READMODE_IDCARD_PASS == mode)// 身份证通行模式
                {
                    accReaderOpt.setValue(String.valueOf(AccConstants.READER_READMODE_IDCARD_PASS));
                }
                accReaderOptionDao.save(accReaderOpt);
                accDevCmdManager.setReaderParametersToDev(accReader.getAccDoor().getDevice(),
                    Lists.newArrayList(accReaderOpt), false);
            }
        }

        // 发送巡更模块，更新读头名称
        if (acc4PatrolPointService != null) {
            Acc4PatrolPointItem acc4PatrolPointItem = new Acc4PatrolPointItem();
            acc4PatrolPointItem.setId(accReader.getId());
            acc4PatrolPointItem.setName(accReader.getName());
            acc4PatrolPointService.updatePointByReaderId(acc4PatrolPointItem);
        }

        item.setId(accReader.getId());
        return item;
    }

    private void updateReaderCache(AccReader reader) {
        final AccDevice dev = reader.getAccDoor().getDevice();
        accDeviceService.updateDevInfoWithDoorAndAuxBySn(dev.getSn());
    }

    private void updateReaderCache(List<AccReader> readers) {
        final Map<String, List<AccReader>> devMap =
            readers.stream().collect(Collectors.groupingBy(reader -> reader.getAccDoor().getDevice().getSn()));
        for (String sn : devMap.keySet()) {
            accDeviceService.updateDevInfoWithDoorAndAuxBySn(sn);
        }
    }

    @Override
    public AccReaderItem saveItem(AccReaderItem item, String applyTo) {
        AccReader accReader = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accReaderDao.findById(id)).orElse(new AccReader());
        ModelUtil.copyPropertiesIgnoreNull(item, accReader);
        if (AccConstants.READER_TYPE_RS485 != item.getCommType() && AccConstants.READER_TYPE_RS485_OR_WGFMT != item.getCommType()) {
            accReader.setCommAddress(null);
        }
        accReaderDao.save(accReader);
        updateQueryReader(accReader);
        // 获取该设备的其他读头
        List<AccReader> readerList =
            accReaderDao.getOtherReaderByDevId(accReader.getAccDoor().getDevice().getId(), accReader.getId());

        // 读头加密设置和485协议类型设置需要复制到同一设备所有读头
        Boolean isEncrypt = accReader.getReaderEncrypt();
        Short rs485ProtocolType = accReader.getRs485ProtocolType();
        for (AccReader accReaderOther : readerList) {
            accReaderOther.setReaderEncrypt(isEncrypt);
            accReaderOther.setRs485ProtocolType(rs485ProtocolType);
            if (applyTo.equals("1")) {
                accReaderOther.setUserLockFun(accReader.getUserLockFun());
            }
            accReaderDao.save(accReaderOther);
        }
        // 同一设备的所有读头
        List<AccReader> accReaderList = new ArrayList<>();
        accReaderList.add(accReader);
        if (applyTo.equals("1")) {
            accReaderList.addAll(readerList);
        }

        // 是否支持设置人员锁定
        boolean isSupportUserLockFun =
            accDeviceOptionService.isSupportFunList(accReader.getAccDoor().getDevice().getSn(), 50);
        AccReaderOption accReaderOpt = null;
        if (isSupportUserLockFun) {
            for (AccReader reader : accReaderList) {
                accReaderOpt = accReaderOptionDao.findByAccReader_IdAndName(reader.getId(), "UserLockFun");
                if (Objects.isNull(accReaderOpt)) {
                    accReaderOpt = new AccReaderOption();
                    accReaderOpt.setAccReader(accReader);
                    accReaderOpt.setName("UserLockFun");
                    accReaderOpt.setType(accReader.getCommType());
                    accReader.getAccReaderOptionSet().add(accReaderOpt);
                }
                accReaderOpt.setValue(item.getUserLockFun() + "");
                accReaderOptionDao.save(accReaderOpt);
            }
        }

        // 是否支持给读头配置隐藏部分人员信息功能
        boolean isSupportInfoRevealFun =
            accDeviceOptionService.isSupportFunList(accReader.getAccDoor().getDevice().getSn(), 51);
        if (StringUtils.isNotBlank(item.getUserInfoReveal()) && isSupportInfoRevealFun) {
            // 复制到同一设备其他读头
            /*if (!applyTo.equals("1")) {
                accReaderList.addAll(readerList);
            }*/
            for (AccReader reader : accReaderList) {
                accReaderOpt = accReaderOptionDao.findByAccReader_IdAndName(reader.getId(), "IsSupportInfoReveal");
                if (accReaderOpt == null) {
                    accReaderOpt = new AccReaderOption();
                    accReaderOpt.setAccReader(reader);
                    accReaderOpt.setName("IsSupportInfoReveal");
                    accReaderOpt.setType((short)1);
                    accReader.getAccReaderOptionSet().add(accReaderOpt);
                }
                accReaderOpt.setValue(item.getUserInfoReveal());
                accReaderOptionDao.save(accReaderOpt);
            }
        }

        // TCP读头需要配置参数
        if (accReader.getCommType() != null && accReader.getCommType() == AccConstants.READER_TYPE_NETWORK) {
            if (StringUtils.isNotBlank(item.getIdCardMode())) {
                AccReaderOption accReaderOption =
                    accReaderOptionDao.findByAccReader_IdAndName(accReader.getId(), "IdentityCardVerifyMode");
                if (accReaderOption == null) {
                    accReaderOption = new AccReaderOption();
                    accReaderOption.setAccReader(accReader);
                    accReaderOption.setName("IdentityCardVerifyMode");
                    accReaderOption.setType((short)1);
                    accReader.getAccReaderOptionSet().add(accReaderOption);
                }
                int mode = Integer.parseInt(item.getIdCardMode());
                // 普通模式
                if (AccConstants.READER_READMODE_NORMAL == mode) {
                    accReaderOption.setValue(String.valueOf(AccConstants.READER_READMODE_NORMAL));
                } else if (AccConstants.READER_READMODE_IDCARD_PASS == mode) {
                    // 身份证通行模式
                    accReaderOption.setValue(String.valueOf(AccConstants.READER_READMODE_IDCARD_PASS));
                }
                accReaderOptionDao.save(accReaderOption);
            }
        }
        accDevCmdManager.delReaderPropertyFromDev(accReader.getAccDoor().getDevice(), accReaderList, false);
        accDevCmdManager.setReaderOptToDev(accReader.getAccDoor().getDevice(), accReaderList, false);

        // 发送巡更模块，更新读头名称
        if (acc4PatrolPointService != null) {
            Acc4PatrolPointItem acc4PatrolPointItem = new Acc4PatrolPointItem();
            acc4PatrolPointItem.setId(accReader.getId());
            acc4PatrolPointItem.setName(accReader.getName());
            acc4PatrolPointService.updatePointByReaderId(acc4PatrolPointItem);
        }
        updateThirdReader(accReader);
        item.setId(accReader.getId());
        return item;
    }

    /**
     * 更新其他模块读头信息
     *
     * @param accReader:
     * @return void
     * <AUTHOR>
     * @date 2023-04-27 15:13
     * @since 1.0.0
     */
    private void updateThirdReader(AccReader accReader) {
        if (accReader4OtherServices != null) {
            AccReader4OtherItem accReader4OtherItem = ModelUtil.copyProperties(accReader, new AccReader4OtherItem());
            Arrays.stream(accReader4OtherServices)
                .forEach(accDevice4OtherService -> accDevice4OtherService.editAccReaderInfo(accReader4OtherItem));
        }
    }

    /**
     * 更新缓存的读头信息
     * 
     * @param accReader:
     * @return void
     * <AUTHOR>
     * @throws @date 2022-05-25 15:57
     * @since 1.0.0
     */
    private void updateQueryReader(AccReader accReader) {
        AccDevice dev = accReader.getAccDoor().getDevice();
        final AccQueryDeviceItem queryDev = accDeviceService.getQueryItemBySn(dev.getSn());
        accDeviceService.updateDoorByQueryItem(queryDev);
        accCacheManager.putDeviceInfo(queryDev);
    }

    @Override
    public List<AccReaderItem> getByCondition(AccReaderItem condition) {
        return (List<AccReaderItem>)accReaderDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        Pager pager = accReaderDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        List<AccReaderItem> itemList = (List<AccReaderItem>)pager.getData();
        createVidInfo(itemList);
        String extDevIds = CollectionUtil.getPropertys(itemList, AccReaderItem::getExtDevId);
        List<AccExtDeviceItem> accExtDeviceItems = accExtDeviceService.getItemByIds(extDevIds);
        Map<String, AccExtDeviceItem> accExtDeviceItemMap = CollectionUtil.itemListToIdMap(accExtDeviceItems);
        if (accExtDeviceItemMap.size() > 0) {
            itemList.forEach(item -> {
                if (StringUtils.isNotBlank(item.getExtDevId())) {
                    item.setExtDevName(accExtDeviceItemMap.get(item.getExtDevId()).getAlias());
                }
            });
        }
        pager.setData(itemList);
        return pager;
    }

    private void createVidInfo(List<AccReaderItem> itemList) {
        if (vid4OtherGetVidChannel2EntityService == null) {
            return;
        }
        List<String> idList = new ArrayList<>();
        for (AccReaderItem accReaderItem : itemList) {
            idList.add(accReaderItem.getId());
        }

        Map<String, String> channelMap =
            vid4OtherGetVidChannel2EntityService.getBindChannelNames(idList, AccReader.class.getSimpleName());
        if (!channelMap.isEmpty()) {
            for (AccReaderItem accReaderItem : itemList) {
                if (channelMap.containsKey(accReaderItem.getId())) {
                    accReaderItem.setChannelName(channelMap.get(accReaderItem.getId()));
                }
            }
        }
    }

    @Override
    public Pager getSelectItemByPage(AccSelectReaderItem condition, int pageNo, int pageSize) {
        return accReaderDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accReaderDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AccReaderItem getItemById(String id) {
        List<AccReaderItem> items = getByCondition(new AccReaderItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public boolean isExist(String name) {
        AccReader accReader = accReaderDao.findByName(name);
        if (accReader == null) {
            return true;
        }
        return false;
    }

    @Override
    public boolean isExistVid() {
        return moduleInfoService.isExistModuleByCode(ConstUtil.SYSTEM_MODULE_VID)
            || moduleInfoService.isExistModuleByCode(ConstUtil.SYSTEM_MODULE_VMS)
            || moduleInfoService.isExistModuleByCode(ConstUtil.SYSTEM_MODULE_IVS);
    }

    @Override
    public boolean accMapIsShowVid() {
        return moduleInfoService.isExistModuleByCode(ConstUtil.SYSTEM_MODULE_VID)
            || moduleInfoService.isExistModuleByCode(ConstUtil.SYSTEM_MODULE_VMS);
    }

    @Override
    public AccReaderItem getByReaderStateAndDoorId(short readerState, String doorId) {
        AccReader accReader = accReaderDao.findByReaderStateAndAccDoor_Id(readerState, doorId);
        return Optional.ofNullable(accReader).map(reader -> ModelUtil.copyProperties(reader, new AccReaderItem()))
            .orElse(null);
    }

    @Override
    public List<AccReaderItem> getItemListByDevId(String deviceId) {
        AccReaderItem accReaderItem = new AccReaderItem();
        accReaderItem.setDeviceId(deviceId);
        return getByCondition(accReaderItem);
    }

    @Override
    public boolean isExistIP(String ip) {
        AccReader accReader = accReaderDao.findByIpAndCommType(ip, AccConstants.READER_TYPE_NETWORK);
        // item.setCommType(AccConstants.READER_TYPE_NETWORK);

        if (accReader == null) {
            return true;
        }
        return false;
    }

    @Override
    public boolean readerCommAddressExist(AccReaderItem item) {
        String deviceId = accReaderDao.getDevIdByReaderId(item.getId());
        List<AccReader> accReaderList = accReaderDao
            .findByCommTypeAndCommAddressAndAccDoor_Device_Id(item.getCommType(), item.getCommAddress(), deviceId);
        if (accReaderList.isEmpty()) {
            return false;
        }
        return true;
    }

    @Override
    public Pager loadPagerByAuthFilter(String sessionId, AccReaderItem condition, int pageNo, int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        return getItemsByPage(condition, pageNo, pageSize);
    }

    @Override
    public boolean checkCardControl() {
        return baseLicenseProvider.checkCardControl();
    }

    @Override
    public Pager getWGReaderFilterList(AccSelectReaderRadioItem condition, int pageNo, int pageSize) {
        List<AccReader> accReaderList =
            accReaderDao.findByAccDoor_Device_MachineType(AccConstants.DEVICE_ACCESS_CONTROL);
        String filterIds = CollectionUtil.getModelIds(accReaderList);
        if (StringUtils.isBlank(condition.getSelectId())) {
            condition.setSelectId("-1");
        }
        if (condition.getType().equals("noSelected")) {
            if (StringUtils.isNotBlank(filterIds)) {
                filterIds = condition.getSelectId() + "," + filterIds;
                condition.setSelectId(filterIds);
            }
            condition.setNotInId(condition.getSelectId());
        } else if (condition.getType().equals("selected")) {
            condition.setInId(condition.getSelectId());
        }
        return accReaderDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
    }

    @Override
    public Pager getReaderZoneItemByPage(AccSelectReaderItem condition, int pageNo, int pageSize) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(SQLUtil.getSqlByItem(condition));
        int orderByIndex = stringBuilder.indexOf("ORDER BY");
        stringBuilder.insert(orderByIndex, "AND t.ID NOT IN (SELECT ACC_READER_ID FROM ACC_READER_ZONE) ");
        stringBuilder.insert(orderByIndex,
            String.format("AND dev.COMM_TYPE NOT IN (%d,%d) AND dev.COMM_TYPE = %d AND dev.MACHINE_TYPE != %d ",
                ConstUtil.COMM_TCPIP, ConstUtil.COMM_RS485, ConstUtil.COMM_HTTP, AccConstants.DEVICE_WIRELESS_LOCK));
        return accReaderDao.getItemsBySql(condition.getClass(), stringBuilder.toString(), pageNo, pageSize);
    }

    @Override
    public List<String> getPullReaderIds() {
        List<Short> pullCommType = Lists.newArrayList(AccConstants.COMM_TCPIP, AccConstants.COMM_RS485);
        return accReaderDao.getReaderIdByDevCommTypeIn(pullCommType);
    }

    @Override
    public List<String> getDoorIdByReaderId(List<String> readerIds) {
        return accReaderDao.getDoorIdByReaderId(readerIds);
    }

    @Override
    public Pager selectReaderBindAiDeviceList(AccSelectReaderRadioItem condition, int pageNo, int pageSize) {
        Pager pager =
            accReaderDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        List<AccSelectReaderRadioItem> accSelectReaderRadioItems = Lists.newArrayList();
        List<AccSelectReaderRadioItem> itemList = (List<AccSelectReaderRadioItem>)pager.getData();
        if (itemList != null && itemList.size() > 0) {
            for (AccSelectReaderRadioItem item : itemList) {
                if (accDeviceOptionService.isSupportFunList(item.getDevSn(), 18)
                    || accDeviceOptionService.isSupportFunList(item.getDevSn(), 44)) {
                    accSelectReaderRadioItems.add(item);
                }
            }
        }
        pager.setData(accSelectReaderRadioItems);
        pager.setTotal(accSelectReaderRadioItems.size());
        return pager;
    }

    @Override
    public List<String> getDevIdByReaderId(List<String> readerIds) {
        return accReaderDao.getDevIdByReaderId(readerIds);
    }

    @Override
    public void handlerTransfer(List<AccReaderItem> readerItems) {
        // 获取所有的读头信息
        Map<String, AccReader> optionAllMap = new HashMap<String, AccReader>();
        List<AccReader> readers = accReaderDao.findAll();
        for (AccReader reader : readers) {
            String key = reader.getAccDoor().getDevice().getSn() + "_" + reader.getAccDoor().getDoorNo() + "_"
                + reader.getReaderNo();
            optionAllMap.put(key, reader);
        }
        // 获取已有的所有门,批量获取用于判断
        Map<String, AccDoor> deviceAllMap = new HashMap<String, AccDoor>();
        // 按设备sn分组， key：pin value:
        Map<String, List<AccReaderItem>> deviceOpMap =
            readerItems.stream().collect(Collectors.groupingBy(AccReaderItem::getDeviceSn));
        // 获取数据库中原有的设备参数取出，用于比较
        List<List<String>> snsList = CollectionUtil.split(deviceOpMap.keySet(), CollectionUtil.splitSize);
        for (List<String> sns : snsList) {
            // 將所有的設備查詢出來
            List<AccDoor> doors = accDoorDao.findByDevice_SnIn(sns);
            for (AccDoor door : doors) {
                deviceAllMap.put(door.getDevice().getSn() + "_" + door.getDoorNo(), door);
            }
        }
        System.out.println(deviceAllMap.keySet());
        // 检测判断数据后保存更新
        for (AccReaderItem readerItem : readerItems) {
            AccReader reader = optionAllMap
                .remove(readerItem.getDeviceSn() + "_" + readerItem.getDoorNo() + "_" + readerItem.getReaderNo());
            if (Objects.isNull(reader)) {
                reader = new AccReader();
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(readerItem, reader, "id");
            System.out.println(readerItem.getDeviceSn() + "_" + readerItem.getDoorNo());
            reader.setAccDoor(deviceAllMap.get(readerItem.getDeviceSn() + "_" + readerItem.getDoorNo()));
            accReaderDao.save(reader);
        }
        optionAllMap = null;
        deviceAllMap = null;
    }

    @Override
    public Pager getTcpReaderFilterList(AccSelectReaderRadioItem condition, int pageNo, int pageSize) {
        List<AccReader> accReaderList = accReaderDao.findByCommType(AccConstants.READER_TYPE_NETWORK);
        String filterIds = CollectionUtil.getModelIds(accReaderList);
        if (StringUtils.isBlank(condition.getSelectId())) {
            condition.setSelectId("-1");
        }
        if (condition.getType().equals("noSelected")) {
            if (StringUtils.isNotBlank(filterIds)) {
                filterIds = condition.getSelectId() + "," + filterIds;
                condition.setSelectId(filterIds);
            }
            condition.setNotInId(condition.getSelectId());
        } else if (condition.getType().equals("selected")) {
            condition.setInId(condition.getSelectId());
        }
        return accReaderDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
    }

    @Override
    public String startReaderIDCard(String readerId) {
        // 读头验证方式
        AccReaderOption accReaderOpt = accReaderOptionDao.findByAccReader_IdAndName(readerId, "IdentityCardVerifyMode");
        AccReader accReader = accReaderDao.findById(readerId).orElse(null);
        if (accReader != null && AccConstants.READER_READMODE_NORMAL == Byte.parseByte(accReaderOpt.getValue())) {
            return String.valueOf(accDevCmdManager.setIdCardRegistrationMode(accReader.getAccDoor().getDevice(), 1,
                accReader.getAccDoor().getDoorNo(), 60));
        }
        return "-1";
    }

    @Override
    public Pager getWGReaderFilterListByAuthFilter(String sessionId, AccSelectReaderRadioItem condition, int pageNo,
        int pageSize) {
        List<AccReader> accReaderList =
            accReaderDao.findByAccDoor_Device_MachineType(AccConstants.DEVICE_ACCESS_CONTROL);
        String filterIds = CollectionUtil.getModelIds(accReaderList);
        if (StringUtils.isBlank(condition.getSelectId())) {
            condition.setSelectId("-1");
        }
        if (condition.getType().equals("noSelected")) {
            if (StringUtils.isNotBlank(filterIds)) {
                filterIds = condition.getSelectId() + "," + filterIds;
                condition.setSelectId(filterIds);
            }
            condition.setNotInId(condition.getSelectId());
        } else if (condition.getType().equals("selected")) {
            condition.setInId(condition.getSelectId());
        }
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        return accReaderDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
    }

    @Override
    public List<AccReaderItem> getItemListByDevSnIn(List<String> sns) {
        List<AccReader> accReaderList = accReaderDao.findByAccDoor_Device_snIn(sns);
        List<AccReaderItem> accReaderItems = new ArrayList<AccReaderItem>();
        AccReaderItem accReaderItem = null;
        for (AccReader accReader : accReaderList) {
            accReaderItem = new AccReaderItem();
            accReaderItem = ModelUtil.copyProperties(accReader, accReaderItem);
            accReaderItem.setDeviceSn(accReader.getAccDoor().getDevice().getSn());
            accReaderItems.add(accReaderItem);
        }
        return accReaderItems;
    }

    @Override
    public List<AccReaderItem> saveReaderItemList(List<AccReaderItem> items) {
        // 组装门信息map
        String doorIds = CollectionUtil.getPropertys(items, AccReaderItem::getDoorId);
        List<AccDoor> accDoorList = accDoorDao.findByIdList(StrUtil.strToList(doorIds));
        Map<String, AccDoor> doorMap = CollectionUtil.listToKeyMap(accDoorList, AccDoor::getId);

        for (AccReaderItem item : items) {
            AccReader accReader = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
                .flatMap(id -> accReaderDao.findById(id)).orElse(new AccReader());
            if (StringUtils.isBlank(accReader.getId())) {
                AccDoor accDoor = doorMap.get(item.getDoorId());
                if (Objects.nonNull(accDoor)) {
                    accReader.setAccDoor(accDoor);
                }
            }
            ModelUtil.copyPropertiesIgnoreNull(item, accReader);
            accReaderDao.save(accReader);
            item.setId(accReader.getId());
        }
        return items;
    }

    @Override
    public List<AccReaderItem> getItemsByIdList(List<String> idList) {
        List<AccReader> readerList = accReaderDao.findByIdList(idList);
        List<AccReaderItem> accReaderItemList = null;
        if (Objects.nonNull(readerList)) {
            // 将实体对象集合组装成VO对象集合
            accReaderItemList = buildItemList(readerList);
        }
        return accReaderItemList;
    }

    @Override
    public List<AccReaderItem> getItemsByAccDoorId(String doorId) {
        List<AccReader> readerList = accReaderDao.findByAccDoor_Id(doorId);
        List<AccReaderItem> accReaderItemList = null;
        if (Objects.nonNull(readerList)) {
            // 将实体对象集合组装成VO对象集合
            accReaderItemList = buildItemList(readerList);
        }
        return accReaderItemList;
    }

    private List<AccReaderItem> buildItemList(List<AccReader> readerList) {
        List<AccReaderItem> accReaderItemList = null;
        if (Objects.nonNull(readerList)) {
            accReaderItemList = new ArrayList<>();
            for (AccReader accReader : readerList) {
                AccReaderItem item = new AccReaderItem();
                ModelUtil.copyProperties(accReader, item);
                AccDoor door = accReader.getAccDoor();
                AccDevice device = door.getDevice();
                item.setDeviceId(door.getDevice().getId());
                item.setDeviceSn(door.getDevice().getSn());
                item.setDoorId(door.getId());
                item.setDoorName(door.getName());
                item.setDoorNo(door.getDoorNo() + "");
                item.setBusinessId(device.getBusinessId());
                accReaderItemList.add(item);
            }
        }
        return accReaderItemList;
    }

    @Override
    public Pager getSimpleItemsByPage(BaseItem condition, int page, int size) {
        return accReaderDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public AccReaderItem saveSimpleItem(AccReaderItem item) {
        AccReader accReader = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accReaderDao.findById(id)).orElse(new AccReader());
        ModelUtil.copyPropertiesIgnoreNull(item, accReader);
        // 端口可能需要为空
        accReader.setSerialPort(item.getSerialPort());
        if (StringUtils.isNotBlank(item.getDoorId())) {
            AccDoor accDoor = accDoorDao.findById(item.getDoorId()).orElse(null);
            if (Objects.nonNull(accDoor)) {
                accReader.setAccDoor(accDoor);
                item.setDoorId(accDoor.getId());
                item.setDoorNo(accDoor.getDoorNo() + "");
            }
        }
        accReaderDao.save(accReader);
        item.setId(accReader.getId());
        return item;
    }

    @Override
    public int countReaderNumByDevId(String devId) {
        return accReaderDao.countByAccDoor_Device_Id(devId);
    }

    @Override
    public AccReaderItem getReaderByReaderNoAndDevId(Short readerNo, String devId) {
        AccReader accReader = accReaderDao.getByReaderNoAndDevId(readerNo, devId);
        AccReaderItem accReaderItem = null;
        if (Objects.nonNull(accReader)) {
            accReaderItem = new AccReaderItem();
            ModelUtil.copyProperties(accReader, accReaderItem);
            AccDoor door = accReader.getAccDoor();
            accReaderItem.setDeviceId(door.getDevice().getId());
            accReaderItem.setDeviceSn(door.getDevice().getSn());
            accReaderItem.setDoorId(door.getId());
            accReaderItem.setDoorName(door.getName());
            accReaderItem.setDoorNo(door.getDoorNo() + "");
        }
        return accReaderItem;
    }

    @Override
    public String getAccReaderSimpleName() {
        return AccReader.class.getSimpleName();
    }

    @Override
    public Map<String, AccReaderItem> getItemsMapByReaderIds(List<String> readerList) {
        List<AccReaderItem> accReaderItemList = getItemsByIdList(readerList);
        return CollectionUtil.listToKeyMap(accReaderItemList, AccReaderItem::getId);
    }

    @Override
    public String getAccReaderBindVidChannel(String readerId) {
        String channelIds = accCacheManager.getAccReaderBindVidChannel(readerId);
        if (StringUtils.isBlank(channelIds)) {
            channelIds =
                accChannelService.getBindChannelIds(StrUtil.strToList(readerId), AccReader.class.getSimpleName());
            if (StringUtils.isNotBlank(channelIds)) {
                accCacheManager.putEntityBindVidChannel(AccReader.class.getSimpleName(), readerId, channelIds);
            }
        }
        return channelIds;
    }

    @Override
    public List<AccReaderItem> getItemsByDoorIdIn(String doorIds) {
        AccReaderItem accProReaderItem = new AccReaderItem();
        accProReaderItem.setDoorIdIn(doorIds);
        Pager pager = getSimpleItemsByPage(accProReaderItem, 0, 0);
        List<AccReaderItem> itemList = (List<AccReaderItem>)pager.getData();
        return itemList;
    }
}