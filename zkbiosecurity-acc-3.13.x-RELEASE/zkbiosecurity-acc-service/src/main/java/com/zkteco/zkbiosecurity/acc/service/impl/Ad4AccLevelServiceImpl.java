package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.vo.AccLevelItem;
import com.zkteco.zkbiosecurity.ad.service.Ad4AccLevelService;
import com.zkteco.zkbiosecurity.ad.vo.Ad4AccLevelItem;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Transactional
public class Ad4AccLevelServiceImpl implements Ad4AccLevelService {
    @Autowired
    private AccLevelService accLevelService;
    @Override
    public List<String> getAllAccLevelName() {
        List<String> levelNameList = accLevelService.getAllLevelNames();
        return levelNameList;
    }

    @Override
    public Ad4AccLevelItem getMasterLevel() {
        AccLevelItem accLevelItem = accLevelService.getMasterLevel();
        return ModelUtil.copyProperties(accLevelItem, new Ad4AccLevelItem());
    }

    @Override
    public void delLevelByIds(List<String> delLevelIds) {
        if (delLevelIds != null && delLevelIds.size() > 0) {
            List<String> tempPersonIdList = new ArrayList<>();
            for (int i=0,levelSize=delLevelIds.size(); i < levelSize; i++) {
                List<String> personIdList = accLevelService.getPersonIdsByLevelId(delLevelIds.get(i)); //根据权限组id获取该权限组下的人员ids
                if (personIdList != null && personIdList.size() > 0) {
                    List<String> deviceIdList = accLevelService.getDevIdsByLevelId(delLevelIds.get(i));// 查询权限组中的设备
                    for (String deviceId : deviceIdList) {
                        for (int j = 0, personSize = personIdList.size(); j < personSize; j++) {
                            tempPersonIdList.add(personIdList.get(j));
                            //对人员进行分批处理
                            if (tempPersonIdList.size() % AccConstants.LEVEL_SPLIT_COUNT == 0) {
                                accLevelService.immeDelLevel(deviceId, delLevelIds.get(i), tempPersonIdList);
                                tempPersonIdList = new ArrayList<>();
                            }
                        }
                        if (tempPersonIdList.size() > 0) {
                            accLevelService.immeDelLevel(deviceId, delLevelIds.get(i), tempPersonIdList);
                        }
                    }
                } else {
                    accLevelService.delAccLevelDoorByLevelId(delLevelIds.get(i));  //删除权限组和门的中间表数据
                }
            }
            accLevelService.deleteByIds(StrUtil.collectionToStr(delLevelIds)); //删除权限组
        }
    }

    @Override
    public Ad4AccLevelItem saveItem(Ad4AccLevelItem ad4AccLevelItem) {
        AccLevelItem item =  new AccLevelItem();
        item = accLevelService.saveItem(ModelUtil.copyProperties(ad4AccLevelItem, item));
        return ModelUtil.copyProperties(item, ad4AccLevelItem);
    }

    @Override
    public List<Ad4AccLevelItem> getLevelByPersonId(String personId) {
        List<AccLevelItem> accLevelItems= accLevelService.getLevelByPersonId(personId);
        return ModelUtil.copyListProperties(accLevelItems, Ad4AccLevelItem.class);
    }

    @Override
    public Map<String, String> getLevelNameIdMap() {
        Map<String, String> levelNameIdMap = accLevelService.getLevelNameIdMap();
        return levelNameIdMap;
    }

    @Override
    public void immeDelPersonLevel(List<String> delAccLevelIds, String personId) {
        accLevelService.delBatchLevel(delAccLevelIds, StrUtil.strToList(personId));
    }

    @Override
    public void addPersonLevel(List<String> accLevelIds, String personId) {
        for (String levelId : accLevelIds) {
            accLevelService.addLevelByParamIds(personId, levelId, "person");// 添加人员权限到数据库
            boolean isSetToDev = accLevelService.getDoorCountByLevelId(levelId) > 0;//判断权限组中是否已经有添加门
            if (isSetToDev) {
                accLevelService.setPersonLevelToDev(levelId, personId);
            }
        }
    }
}
