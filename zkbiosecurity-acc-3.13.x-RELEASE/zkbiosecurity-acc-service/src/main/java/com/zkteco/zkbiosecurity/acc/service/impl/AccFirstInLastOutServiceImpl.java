/**
 * <AUTHOR>
 * @date 2020/3/27 11:25
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.constants.AccCacheKeyConstants;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccFirstInLastOutDao;
import com.zkteco.zkbiosecurity.acc.dao.AccTransactionDao;
import com.zkteco.zkbiosecurity.acc.model.AccFirstInLastOut;
import com.zkteco.zkbiosecurity.acc.model.AccTransaction;
import com.zkteco.zkbiosecurity.acc.service.AccFirstInLastOutService;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.vo.AccFirstInLastOutItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.auth.provider.AuthSessionProvider;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.config.DataSourceConfig;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">caiyun.chen</a>
 * @version v1.0
 */
@Service
@Transactional
public class AccFirstInLastOutServiceImpl implements AccFirstInLastOutService {
    @Autowired
    private AccFirstInLastOutDao accFirstInLastOutDao;
    @Autowired
    private AuthSessionProvider authSessionProvider;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private AccTransactionDao accTransactionDao;
    @Autowired
    private AccTransactionService accTransactionService;
    @Autowired
    private AuthUserService authUserService;

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accFirstInLastOutDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size, long limit) {
        return accFirstInLastOutDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size,
            limit);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accFirstInLastOutDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public void saveFirstInLastOutHandle(AccTransactionItem accTransactionItem) {
        // 过滤联动及全局联动事件
        if (StringUtils.isNotBlank(accTransactionItem.getPin())
            && !(accTransactionItem.getEventNo().equals(Short.parseShort(AccConstants.EVENT_LINKCONTROL.toString())))
            && accTransactionItem.getEventNo() != AccConstants.CUSTOM_EVENT_NORMAL_GLOBALLINKAGE) {
            Date todayBeginTime = DateUtil.getDayBeginTime(accTransactionItem.getEventTime());
            Date todayEndTime = DateUtil.getDayEndTime(accTransactionItem.getEventTime());
            List<AccFirstInLastOut> accFirstInLastOutList =
                accFirstInLastOutDao.findByPinAndFirstInTimeGreaterThanEqualAndFirstInTimeLessThanEqual(
                    accTransactionItem.getPin(), todayBeginTime, todayEndTime);
            AccFirstInLastOut accFirstInLastOut = (accFirstInLastOutList != null && !accFirstInLastOutList.isEmpty())
                ? accFirstInLastOutList.get(0) : null;
            if (accFirstInLastOut == null) {
                accFirstInLastOut = new AccFirstInLastOut();
                ModelUtil.copyPropertiesIgnoreNullWithProperties(accTransactionItem, accFirstInLastOut, "id");
                accFirstInLastOut.setFirstInTime(accTransactionItem.getEventTime());
                accFirstInLastOut.setReaderNameIn(accTransactionItem.getReaderName());
            } else if (Objects.isNull(accFirstInLastOut.getLastOutTime())
                || accFirstInLastOut.getLastOutTime().compareTo(accTransactionItem.getEventTime()) < 0) {
                ModelUtil.copyPropertiesIgnoreNullWithProperties(accTransactionItem, accFirstInLastOut, "id");
                accFirstInLastOut.setLastOutTime(accTransactionItem.getEventTime());
                accFirstInLastOut.setReaderNameOut(accTransactionItem.getReaderName());
            }
            accFirstInLastOutDao.save(accFirstInLastOut);
        }
    }

    @Override
    public void deleteAllData() {
        accFirstInLastOutDao.truncateAll();
    }

    @Override
    public List<AccFirstInLastOutItem> getItemData(Class<AccFirstInLastOutItem> eleFirstInLastOutItemClass,
        BaseItem condition, int beginIndex, int endIndex) {
        return accFirstInLastOutDao.getItemsDataBySql(eleFirstInLastOutItemClass, SQLUtil.getSqlByItem(condition),
            beginIndex, endIndex, true);
    }

    @Override
    public List<AccFirstInLastOutItem> getExportItemData(String sessionId, AccFirstInLastOutItem condition,
        int beginIndex, int endIndex) {
        // 根据用户权限过滤
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setDeptCodeInByUserId(userId);
        }
        return accFirstInLastOutDao.getItemsDataBySql(AccFirstInLastOutItem.class, SQLUtil.getSqlByItem(condition),
            beginIndex, endIndex, true);
    }

    @Override
    public Pager loadTransactionByAuthUserFilter(String sessionId, AccFirstInLastOutItem condition, int pageNo,
        int pageSize) {
        // 根据用户权限过滤
        condition.setDeptCodeIn(accTransactionService.getDeptCodesBySessionId(sessionId));
        return getItemsByPage(condition, pageNo, pageSize);
    }

    @Override
    public Pager loadTransactionByAuthUserFilter(String sessionId, AccFirstInLastOutItem condition, int pageNo,
        int pageSize, long limitCount) {
        // 根据用户权限过滤
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setDeptCodeInByUserId(userId);
        }
        return getItemsByPage(condition, pageNo, pageSize, limitCount);
    }

    @Override
    public void saveFirstInLastOutByCacheTime() {
        String lastTransactionCreateTime =
            accCacheManager.getCacheValueByKey(AccCacheKeyConstants.ACC_FIRSTINLASTOUT_TIME);
        Date startDateTime;
        if (StringUtils.isBlank(lastTransactionCreateTime)) {
            startDateTime = DateUtil.getTodayBeginTime();
        } else {
            startDateTime = new Date(Long.parseLong(lastTransactionCreateTime));
        }
        // 更新最大拉取记录的时间
        accCacheManager.putFirstInLastOutTime(AccCacheKeyConstants.ACC_FIRSTINLASTOUT_TIME,
            String.valueOf(System.currentTimeMillis()));
        List<AccTransaction> accTransactionList = new ArrayList<>();
        String dbType = DataSourceConfig.getDbType();
        if (ZKConstant.ORACLE.equals(dbType)) {
            accTransactionList = accTransactionDao.getTransactionByCreateTimeInOracle(startDateTime);
        } else {
            accTransactionList = accTransactionDao.getTransactionByCreateTime(startDateTime);
        }
        Map<String, List<AccTransaction>> pinAndTransactionMap =
            accTransactionList.stream().collect(Collectors.groupingBy(AccTransaction::getPin));
        for (String pin : pinAndTransactionMap.keySet()) {
            List<AccTransaction> accTransactions = pinAndTransactionMap.get(pin);
            // 该天最大事件时间记录
            AccTransaction maxAccTransaction = null;
            // 该天最小事件时间记录
            AccTransaction minAccTransaction = null;
            // 最大/最小事件记录
            Map<Date, AccTransaction> eventTimeAndTransactionMap = new HashMap<>();
            for (AccTransaction accTransaction : accTransactions) {
                if (maxAccTransaction == null) {
                    maxAccTransaction = accTransaction;
                    eventTimeAndTransactionMap.put(maxAccTransaction.getEventTime(), maxAccTransaction);
                } else {
                    if (DateUtil.isSameDay(maxAccTransaction.getEventTime(), accTransaction.getEventTime())) {
                        minAccTransaction = accTransaction;
                    } else {
                        maxAccTransaction = accTransaction;
                        eventTimeAndTransactionMap.put(maxAccTransaction.getEventTime(), maxAccTransaction);
                        if (minAccTransaction != null) {
                            eventTimeAndTransactionMap.put(minAccTransaction.getEventTime(), minAccTransaction);
                            minAccTransaction = null;
                        }
                    }
                }
            }
            if (minAccTransaction != null) {
                eventTimeAndTransactionMap.put(minAccTransaction.getEventTime(), minAccTransaction);
            }

            for (Map.Entry<Date, AccTransaction> entry : eventTimeAndTransactionMap.entrySet()) {
                AccTransaction accTransaction = entry.getValue();
                Date eventBeginTime = DateUtil.getDayBeginTime(accTransaction.getEventTime());
                Date eventEndTime = DateUtil.getDayEndTime(accTransaction.getEventTime());
                List<AccFirstInLastOut> accFirstInLastOutList =
                    accFirstInLastOutDao.findByPinAndFirstInTimeGreaterThanEqualAndFirstInTimeLessThanEqual(
                        accTransaction.getPin(), eventBeginTime, eventEndTime);
                AccFirstInLastOut accFirstInLastOut =
                    (accFirstInLastOutList != null && !accFirstInLastOutList.isEmpty()) ? accFirstInLastOutList.get(0)
                        : null;

                if (accFirstInLastOut == null) {
                    accFirstInLastOut = new AccFirstInLastOut();
                    ModelUtil.copyPropertiesIgnoreNullWithProperties(accTransaction, accFirstInLastOut, "id");
                    accFirstInLastOut.setFirstInTime(accTransaction.getEventTime());
                    accFirstInLastOut.setReaderNameIn(accTransaction.getReaderName());
                } else {
                    if (!Objects.isNull(accFirstInLastOut.getFirstInTime())
                        && accFirstInLastOut.getFirstInTime().compareTo(accTransaction.getEventTime()) > 0) {
                        accFirstInLastOut.setLastOutTime(accFirstInLastOut.getFirstInTime());
                        accFirstInLastOut.setReaderNameOut(accFirstInLastOut.getReaderNameIn());
                        accFirstInLastOut.setFirstInTime(accTransaction.getEventTime());
                        accFirstInLastOut.setReaderNameIn(accTransaction.getReaderName());
                    } else if (Objects.isNull(accFirstInLastOut.getLastOutTime())
                        || accFirstInLastOut.getLastOutTime().compareTo(accTransaction.getEventTime()) < 0) {
                        accFirstInLastOut.setLastOutTime(accTransaction.getEventTime());
                        accFirstInLastOut.setReaderNameOut(accTransaction.getReaderName());
                    }
                }
                accFirstInLastOutDao.saveAndFlush(accFirstInLastOut);
            }
        }
    }
}
