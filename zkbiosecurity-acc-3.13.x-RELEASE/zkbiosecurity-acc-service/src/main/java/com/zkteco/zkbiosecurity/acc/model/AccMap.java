/**
 * File Name: AccMap
 * Created by GenerationTools on 2018-03-20 下午02:07
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 对应百傲瑞达实体 AccMap
 * <AUTHOR>
 * @date:	2018-03-20 下午02:07
 * @version v1.0
 */
@Entity
@Table(name = "ACC_MAP")
@Getter
@Setter
@Accessors(chain=true)
public class AccMap extends BaseModel implements Serializable {

	/** */
	private static final long serialVersionUID = 1L;

	/**  */
	@Column(name="AUTH_AREA_ID")
	private String authAreaId;

	/**  */
	@Column(name="NAME",length=50,nullable=false)
	private String name;

	/**  */
	@Column(name="MAP_PATH",nullable=false)
	private String mapPath;

	/**  */
	@Column(name="WIDTH")
	private Double width;

	/**  */
	@Column(name="HEIGHT")
	private Double height;

	/**  */
	@OneToMany(mappedBy="accMap", cascade = CascadeType.REMOVE)
	private List<AccMapPos> accMapPosList = new ArrayList<>();

}