package com.zkteco.zkbiosecurity.acc.processor;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

import javax.annotation.PostConstruct;

import org.springframework.stereotype.Component;

/**
 * 定制阻塞线程池
 *
 * <AUTHOR>
 * @date 2021/11/05 15:36
 * @since 1.0.0
 */
@Component
public class CustomThreadPoolExecutor {

    private static int corePoolSize;
    private static int maxPoolSize;

    private CustomThreadPoolExecutor() {
        super();
    };

    @PostConstruct
    private void init() {
        // 获取设置的核心线程数量，默认2
        corePoolSize = 2;
        // 获取设置的最大线程数量，默认10
        maxPoolSize = 10;
    }

    public static ThreadPoolExecutor getCustomThreadPoolExecutor() {
        // 懒加载获取单例的线程池
        return CustomThreadPoolExecutorHolder.pool;
    }

    public static void setCorePoolSize(Integer corePoolSize) {
        if (corePoolSize != null) {
            CustomThreadPoolExecutorHolder.pool.setCorePoolSize(corePoolSize);
        }
    }

    public static void setMaxPoolSize(Integer maxPoolSize) {
        if (maxPoolSize != null && maxPoolSize > 0) {
            CustomThreadPoolExecutorHolder.pool.setMaximumPoolSize(maxPoolSize);
        }
    }

    public static void destory() {
        CustomThreadPoolExecutorHolder.pool.shutdownNow();
    }

    /**
     * 使用内部类的方式设计成单例模式
     */
    private static class CustomThreadPoolExecutorHolder {

        /**
         * 初始化线程池
         * 
         * @params corePoolSize 核心线程数量
         * @params maximumPoolSize 最大线程数量
         * @params keepAliveTime 活跃线程数大于核心线程数时，空闲的多余线程最大存活时间
         * @params unit 线程存活时间的单位
         * @params workQueue 存放任务的队列
         * @params threadFactory 线程工厂
         * @params handler 超出线程范围和队列容量的任务的处理程序，即当超出maxmumPoolSize+workQueue之和时，任务交给RejectedExecutionHandler处理
         */
        private static final ThreadPoolExecutor pool = new ThreadPoolExecutor(CustomThreadPoolExecutor.corePoolSize,
            CustomThreadPoolExecutor.maxPoolSize, 30, TimeUnit.MINUTES, new ArrayBlockingQueue<>(10),
            new CustomThreadFactory(), new CustomRejectedExecutionHandler());

        static final class CustomThreadFactory implements ThreadFactory {

            private AtomicInteger count = new AtomicInteger(0);

            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(r);
                String threadName = CustomThreadPoolExecutor.class.getSimpleName() + count.addAndGet(1);
                // System.out.println(threadName);
                t.setName(threadName);
                return t;
            }
        }

        static final class CustomRejectedExecutionHandler implements RejectedExecutionHandler {

            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
                try {
                    // 核心改造点，由blockingqueue的offer改成put阻塞方法
                    executor.getQueue().put(r);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
