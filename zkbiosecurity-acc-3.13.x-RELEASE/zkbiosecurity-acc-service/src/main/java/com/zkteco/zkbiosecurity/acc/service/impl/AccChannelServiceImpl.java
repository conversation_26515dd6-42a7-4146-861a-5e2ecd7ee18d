package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.model.AccAuxIn;
import com.zkteco.zkbiosecurity.acc.model.AccReader;
import com.zkteco.zkbiosecurity.acc.service.AccAuxInService;
import com.zkteco.zkbiosecurity.acc.service.AccChannelService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.acc.service.AccReaderService;
import com.zkteco.zkbiosecurity.acc.vo.Acc4VidChannelItem;
import com.zkteco.zkbiosecurity.acc.vo.AccAuxInItem;
import com.zkteco.zkbiosecurity.acc.vo.AccChannelSelectItem;
import com.zkteco.zkbiosecurity.acc.vo.AccReaderItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.ivs.service.Ivs4OtherGetIvsChannelService;
import com.zkteco.zkbiosecurity.ivs.vo.IvsChannel4OtherItem;
import com.zkteco.zkbiosecurity.vid.service.Vid4OtherChannelService;
import com.zkteco.zkbiosecurity.vid.service.Vid4OtherGetVidChannelService;
import com.zkteco.zkbiosecurity.vid.vo.Vid4OtherChannelItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Transactional
public class AccChannelServiceImpl implements AccChannelService {

    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired(required = false)
    private Vid4OtherChannelService vid4OtherChannelService;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccReaderService accReaderService;
    @Autowired
    private AccAuxInService accAuxInService;
    @Autowired(required = false)
    private Vid4OtherGetVidChannelService vid4OtherGetVidChannelService;
    @Autowired(required = false)
    private Ivs4OtherGetIvsChannelService ivs4OtherGetIvsChannelService;

    @Override
    public ZKResultMsg isExistVidChannel() {
        return vid4OtherChannelService.isExistVidChannel();
    }

    @Override
    public String getBindChannelIds(Collection<String> entityIds, String entityClassName) {
        String channelIds = StringUtils.EMPTY;
        if (Objects.nonNull(vid4OtherChannelService)) {
            channelIds = vid4OtherChannelService.getBindChannelIds(entityIds, entityClassName);
        }
        return channelIds;
    }

    @Override
    public ZKResultMsg bindOrUnbindChannel(String deviceSn, String channelIds, String entityName, String entityId) {
        ZKResultMsg ret = vid4OtherChannelService.bindOrUnbindChannel(channelIds, entityName, entityId);

        if (ivs4OtherGetIvsChannelService != null && accDeviceOptionService.isDeviceSupportIPCLink(deviceSn)) {
            // 根据通道id查询获取通道编号集合
            IvsChannel4OtherItem item = new IvsChannel4OtherItem();
            item.setInId(StringUtils.defaultIfBlank(channelIds, "-1"));
            List<IvsChannel4OtherItem> channelItems = ivs4OtherGetIvsChannelService.getChannel(item);
            List<String> channelNoList =
                channelItems.stream().map(IvsChannel4OtherItem::getChannelNo).collect(Collectors.toList());
            // 支持IPC联动，先删后下
            if (AccReader.class.getSimpleName().equals(entityName)) {
                AccReaderItem accReaderItem = accReaderService.getItemById(entityId);
                accDevCmdManager.delReaderCamBindInfo(deviceSn, accReaderItem.getReaderNo(), false);
                accDevCmdManager.setReaderCamBindInfo(deviceSn, accReaderItem, channelNoList, false);
            } else if (AccAuxIn.class.getSimpleName().equals(entityName)) {
                AccAuxInItem accAuxInItem = accAuxInService.getItemById(entityId);
                accDevCmdManager.delAuxInCamBindInfo(deviceSn, accAuxInItem.getAuxNo(), false);
                accDevCmdManager.setAuxInCamBindInfo(deviceSn, accAuxInItem, channelNoList, false);
            }
        }
        accCacheManager.putEntityBindVidChannel(entityName, entityId, channelIds);
        return ret;
    }

    @Override
    public Pager loadPagerByAuthFilter(String sessionId, AccChannelSelectItem condition, int pageNo, int pageSize) {
        Pager pager = new Pager();
        if (Objects.nonNull(vid4OtherGetVidChannelService)) {
            Vid4OtherChannelItem vid4OtherChannelItem = new Vid4OtherChannelItem();
            ModelUtil.copyProperties(condition, vid4OtherChannelItem);
            // 支持IPC联动按设备序列号查询
            boolean supportIPCLink = accDeviceOptionService.isDeviceSupportIPCLink(condition.getDeviceSn());
            if (supportIPCLink) {
                vid4OtherChannelItem.setDomainCode(condition.getDeviceSn());
            }
            pager =
                vid4OtherGetVidChannelService.loadPagerByAuthFilter(sessionId, vid4OtherChannelItem, pageNo, pageSize);
            List<Acc4VidChannelItem> acc4VidChannelItemList = (List<Acc4VidChannelItem>)pager.getData();
            List<AccChannelSelectItem> accChannelSelectItemList =
                ModelUtil.copyListProperties(acc4VidChannelItemList, AccChannelSelectItem.class);
            pager.setData(accChannelSelectItemList);
        }
        return pager;
    }
}
