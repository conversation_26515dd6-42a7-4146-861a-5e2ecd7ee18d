package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.dao.AccLevelDao;
import com.zkteco.zkbiosecurity.acc.dao.AccMapDao;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.vo.AccQueryDeviceItem;
import com.zkteco.zkbiosecurity.auth.service.AuthArea4OtherService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;

/**
 * <AUTHOR>
 * @date 2021/3/8 9:47
 * @since 1.0.0
 */
@Service
public class AccAreaExtServiceImpl implements AuthArea4OtherService {

    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccLevelDao accLevelDao;
    @Autowired
    private AccMapDao accMapDao;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccCacheManager accCacheManager;

    @Override
    public void checkAreaIsUsed(String areaId) {
        Long count = accDeviceDao.countByAuthAreaId(areaId);
        if (count > 0) {
            throw ZKBusinessException
                .warnException(I18nUtil.i18nCode("base_area_isUsed", I18nUtil.i18nCode("acc_module")));
        }
        count = accLevelDao.countByAuthAreaId(areaId);
        if (count > 0) {
            throw ZKBusinessException
                .warnException(I18nUtil.i18nCode("base_area_isUsed", I18nUtil.i18nCode("acc_module")));
        }
        count = accMapDao.countByAuthAreaId(areaId);
        if (count > 0) {
            throw ZKBusinessException
                .warnException(I18nUtil.i18nCode("base_area_isUsed", I18nUtil.i18nCode("acc_module")));
        }
    }

    @Override
    public void editAuthArea(AuthAreaItem authAreaItem) {
        AccQueryDeviceItem queryItem = new AccQueryDeviceItem();
        queryItem.setAuthAreaId(authAreaItem.getId());
        List<AccQueryDeviceItem> accDeviceItemList = (List<AccQueryDeviceItem>)accDeviceDao
            .getItemsBySql(AccQueryDeviceItem.class, SQLUtil.getSqlByItem(queryItem));
        if (accDeviceItemList != null && accDeviceItemList.size() > 0) {
            for (AccQueryDeviceItem accQueryDeviceItem : accDeviceItemList) {
                // 需要查出全量的设备缓存
                AccQueryDeviceItem queryDeviceItem = accCacheManager.getDeviceInfo(accQueryDeviceItem.getSn());
                if (queryDeviceItem != null && !authAreaItem.getName().equals(queryDeviceItem.getAuthAreaName())) {
                    queryDeviceItem.setAuthAreaName(authAreaItem.getName());
                    accCacheManager.putDeviceInfo(queryDeviceItem);
                }
            }
        }
    }
}
