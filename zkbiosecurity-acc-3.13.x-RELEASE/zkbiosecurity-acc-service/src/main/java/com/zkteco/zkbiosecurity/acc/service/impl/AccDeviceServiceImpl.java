/**
 * File Name: AccDeviceServiceImpl Created by GenerationTools on 2018-03-08 下午02:41 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.base.utils.Base64Util;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zkteco.zkbiosecurity.acc.bean.AccDevCQDataBean;
import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.constants.AccCacheKeyConstants;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.*;
import com.zkteco.zkbiosecurity.acc.model.*;
import com.zkteco.zkbiosecurity.acc.operate.AccDealDevCQDataOperate;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.utils.AccDataUtil;
import com.zkteco.zkbiosecurity.acc.utils.AccDeviceUtil;
import com.zkteco.zkbiosecurity.acc.utils.AccQueryRulesUtil;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.adms.service.*;
import com.zkteco.zkbiosecurity.adms.vo.AdmsAuthDeviceItem;
import com.zkteco.zkbiosecurity.adms.vo.AdmsDeviceItem;
import com.zkteco.zkbiosecurity.auth.provider.AuthSessionProvider;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.cmd.sec.constants.CmdSecConstants;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.ivs.service.Ivs4OtherGetIvsChannelService;
import com.zkteco.zkbiosecurity.ivs.vo.IvsChannel4OtherItem;
import com.zkteco.zkbiosecurity.license.provider.BaseLicenseProvider;
import com.zkteco.zkbiosecurity.license.vo.bean.ResultCode;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersBioTemplateService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.service.PersWiegandFmtService;
import com.zkteco.zkbiosecurity.pers.vo.PersWiegandFmtItem;
import com.zkteco.zkbiosecurity.system.service.BaseMediaFileService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseMediaFileItem;
import com.zkteco.zkbiosecurity.vdb.constants.Vdb4OtherConstants;
import com.zkteco.zkbiosecurity.vdb.service.VdbDevice4OtherService;
import com.zkteco.zkbiosecurity.vdb.vo.VdbDevice4OtherItem;
import com.zkteco.zkbiosecurity.vid.service.Vid4OtherChannel2EntityService;

/**
 * 对应百傲瑞达 AccDeviceServiceImpl
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-03-08 下午02:41
 */
@Service
@Transactional
public class AccDeviceServiceImpl implements AccDeviceService {

    Logger logger = LoggerFactory.getLogger(AccDeviceServiceImpl.class);
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired(required = false)
    private AdmsAuthDeviceService admsAuthDeviceService;
    @Autowired(required = false)
    private AdmsDeviceService admsDeviceService;
    @Autowired(required = false)
    private AdmsDevCmdService admsDevCmdService;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccDeviceEventService accDeviceEventService;
    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private AccDeviceBIdDao accDeviceBIdDao;
    @Autowired
    private AccCombOpenDoorDao accCombOpenDoorDao;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private AccDeviceOptionDao accDeviceOptionDao;
    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AccLevelDao accLevelDao;
    @Autowired
    private AccTransactionDao accTransactionDao;
    @Autowired
    private AccAuxInDao accAuxInDao;
    @Autowired
    private AccAuxInService accAuxInService;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccMapPosService accMapPosService;
    @Autowired
    private AccReaderDao accReaderDao;
    @Autowired
    private AccFirstOpenService accFirstOpenService;
    @Autowired
    private AccFirstOpenDao accFirstOpenDao;
    @Autowired
    private AccBaseDictionaryService accBaseDictionaryService;
    @Autowired(required = false)
    private Vid4OtherChannel2EntityService vid4OtherChannel2EntityService;
    @Autowired
    private AccHolidayService accHolidayService;
    @Autowired
    private AccDSTimeDao accDSTimeDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AccInterlockService accInterlockService;
    @Autowired
    private AccAntiPassbackService accAntiPassbackService;
    @Autowired
    private AccVerifyModeRuleService accVerifyModeRuleService;
    @Autowired
    private PersWiegandFmtService persWiegandFmtService;
    @Autowired
    private BaseMediaFileService baseMediaFileService;
    @Autowired
    private AccDeviceEventDao accDeviceEventDao;
    @Autowired
    private AccDeviceVerifyModeDao accDeviceVerifyModeDao;
    @Autowired
    private AccAuxOutDao accAuxOutDao;
    @Autowired
    private AccAuxOutService accAuxOutService;
    @Autowired
    private AuthSessionProvider authSessionProvider;
    @Autowired
    private AccDealDevCQDataOperate accDealDevCQDataOperate;
    @Autowired
    private PersBioTemplateService persBioTemplateService;
    @Autowired
    private Acc4PersDeviceService acc4PersDeviceService;
    @Autowired
    private BaseLicenseProvider baseLicenseProvider;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccPersonService accPersonService;
    @Autowired
    private AccTimeSegService accTimeSegService;
    @Autowired
    private AccLinkageService accLinkageService;
    @Autowired
    private AccCombOpenDoorService accCombOpenDoorService;
    @Autowired(required = false)
    private Acc4AccGlobalLinkageService acc4AccGlobalLinkageService;
    @Autowired(required = false)
    private AccGetAccGlobalApbService accGetAccGlobalApbService;
    @Autowired(required = false)
    private Acc4AccReaderZoneService acc4AccReaderZoneService;
    @Autowired(required = false)
    private Acc4AccGlobalInterlockService acc4AccGlobalInterlockService;
    @Autowired(required = false)
    private AccGetAccGlobalInterlockService accGetAccGlobalInterlockService;
    @Autowired(required = false)
    private AccGetAccGlobalLinkageService accGetAccGlobalLinkageService;
    @Autowired(required = false)
    private Acc4ParkAccDoorService acc4ParkAccDoorService;
    @Autowired(required = false)
    private AccGetAccOccupancyService accGetAccOccupancyService;
    @Autowired(required = false)
    private AccGetAccPersonLimitService accGetAccPersonLimitService;
    @Autowired(required = false)
    private Acc4IVideoControlEntityService acc4IVideoControlEntityService;
    @Autowired(required = false)
    private Acc4VmsGlobalLinkageService acc4VmsGlobalLinkageService;
    @Autowired(required = false)
    private Acc4VmsDeviceService acc4VmsDeviceService;
    @Autowired(required = false)
    private AccDevice4OtherService[] accDevice4OtherServices;
    @Autowired(required = false)
    private AccDoor4OtherService[] accDoor4OtherServices;
    @Autowired(required = false)
    private AccReader4OtherService[] accReader4OtherServices;
    @Autowired
    private AccExtDeviceDao accExtDeviceDao;
    @Autowired
    private AccCloudService accCloudService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AccSupportFuncService accSupportFuncService;
    @Autowired(required = false)
    private AdmsCacheService admsCacheService;
    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private AccReaderService accReaderService;
    @Autowired
    private AccLicensePointsCheckService accLicensePointsCheckService;
    @Autowired
    private AuthUserService authUserService;
    @Autowired(required = false)
    private AccProExtService accProExtService;
    @Autowired(required = false)
    private Acc4IvsDeviceService acc4IvsDeviceService;
    @Autowired(required = false)
    private AdmsDeviceOptionService admsDeviceOptionService;
    @Autowired(required = false)
    private VdbDevice4OtherService vdbDevice4OtherService;
    @Autowired(required = false)
    private Ivs4OtherGetIvsChannelService ivs4OtherGetIvsChannelService;
    @Autowired
    private AccExtDeviceService accExtDeviceService;

    @Value("${system.language}")
    private String language;
    @Value("${system.securityLevel:0}")
    private int securityLevel;

    Map<String, Short> commTypeMap = new HashMap<>();

    @Override
    public AccDeviceItem saveItem(AccDeviceItem item) {
        AccDevice accDevice = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNoneBlank)
            .flatMap(id -> accDeviceDao.findById(id)).orElse(new AccDevice());
        String oldWgReaderId = accDevice.getWgReaderId();
        ModelUtil.copyPropertiesIgnoreNull(item, accDevice);
        if (StringUtils.isBlank(accDevice.getWgReaderId())) {
            accDevice.setWgReaderId(null);
        }
        if (StringUtils.isNotBlank(item.getWgReaderId()) && StringUtils.isNotBlank(oldWgReaderId)) {
            // 取消读头一体机设置的时候需要判断当前点数是否够
            if (accLicensePointsCheckService.isZKBioCVV6000Server()) {
                ZKResultMsg checkRet = accLicensePointsCheckService.check();
                if (!checkRet.isSuccess()) {
                    throw ZKBusinessException.warnException("acc_device_wgDevMaxCount");
                }
            } else {
                Map<String, Integer> countMap = getAfterLicensedCount(1, 1, accDevice.getCommType());
                ResultCode resultCode =
                    baseLicenseProvider.isCountOutRangeAcc(accDevice.getCommType() == AccConstants.COMM_TYPE_PUSH_HTTP
                        ? ConstUtil.LICENSE_MODULE_ACC_PUSH : ConstUtil.LICENSE_MODULE_ACC_PULL, countMap);
                if (ResultCode.SUCCESS != resultCode) {
                    throw ZKBusinessException.warnException("acc_device_wgDevMaxCount");
                }
            }
        }
        String oldMasterSlave = accDeviceOptionService.getMasterSlave(accDevice.getSn());
        if (StringUtils.isNotBlank(oldMasterSlave) && StringUtils.isNotBlank(item.getMasterSlave())
            && !(item.getMasterSlave()).equals(oldMasterSlave)) {
            Long retFlag = accDevCmdManager.updateConfigMasterSlave(accDevice, item.getMasterSlave(), false);// 一体机配置主从设备
            if (retFlag != null && retFlag > 0) {
                accDevCmdManager.rebootDev(accDevice.getSn(), true);// 强制重启设备,重启才能生效
            }
        }
        AccDevice newDev = accDeviceDao.save(accDevice);
        if (StringUtils.isNotBlank(item.getMasterSlave())) {
            accDeviceOptionService.setMasterSlaveOption(newDev.getSn(), item.getMasterSlave());
        }
        if (accDeviceOptionService.isSupportFun(newDev.getSn(), "MachineTZFunOn")) {
            setTimeZoneToDev(newDev, newDev.getTimeZone());
        }
        // if (StringUtils.isNotBlank(newDev.getWgReaderId()) && !newDev.getWgReaderId().equals(oldWgReaderId)) {
        // //获取用作当前一体机做韦根读头的设备的权限组，下发下去
        // accLevelService.setLevelToDevAsReader(newDev.getId(), newDev.getWgReaderId());
        // }
        updateThirdDev(accDevice);// 更新其他模块设备信息
        putQueryItemToCache(accDevice);
        // pull设备需要同步修改后的密码给adms
        if (ConstUtil.COMM_TCPIP == newDev.getCommType() || ConstUtil.COMM_RS485 == newDev.getCommType()) {
            updateAdmsDeviecPwd(newDev.getSn(), newDev.getCommPwd());
        }
        item.setId(accDevice.getId());
        return item;
    }

    @Override
    public AccDeviceItem saveAccDevice(AccDeviceItem item) {
        AccDevice accDevice = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accDeviceDao.findById(id)).orElse(new AccDevice());
        ModelUtil.copyPropertiesIgnoreNull(item, accDevice);
        if (StringUtils.isNotEmpty(item.getParentDeviceId())) {
            AccDevice parentDev = accDeviceDao.findById(item.getParentDeviceId()).orElse(null);
            if (Objects.nonNull(parentDev)) {
                accDevice.setParentDevice(parentDev);
            }
        }
        if (StringUtils.isNotBlank(item.getAccDSTimeId())) {
            if (accDevice.getAccDSTime() != null && !item.getAccDSTimeId().equals(accDevice.getAccDSTime().getId())) {
                final Optional<AccDSTime> accDSTime = accDSTimeDao.findById(item.getAccDSTimeId());
                if (accDSTime.isPresent()) {
                    accDevice.setAccDSTime(accDSTime.get());
                }
            }
        } else {
            accDevice.setAccDSTime(null);
        }
        accDeviceDao.save(accDevice);
        item.setId(accDevice.getId());
        return item;
    }

    /**
     * 更新其他模块设备信息
     *
     * @param accDevice
     */
    private void updateThirdDev(AccDevice accDevice) {
        if (accDevice4OtherServices != null) {
            AccDevice4OtherItem accDevice4OtherItem = buildAccDevice4OtherItem(accDevice);
            Arrays.stream(accDevice4OtherServices).forEach(accDevice4OtherService -> {
                accDevice4OtherService.editAccDeviceInfo(accDevice4OtherItem);
            });
        }
    }

    /**
     * 组装对接其他模块的门禁VO对象
     *
     * @param accDevice:设备信息
     * @return com.zkteco.zkbiosecurity.acc.vo.AccDevice4OtherItem
     * <AUTHOR>
     * @date 2021-03-08 16:33
     * @since 1.0.0
     */
    private AccDevice4OtherItem buildAccDevice4OtherItem(AccDevice accDevice) {
        AccDevice4OtherItem accDevice4OtherItem = ModelUtil.copyProperties(accDevice, new AccDevice4OtherItem());
        List<String> doorIds = (List<String>)CollectionUtil.getModelIdsList(accDevice.getAccDoorList());
        accDevice4OtherItem.setDoorIds(doorIds);
        return accDevice4OtherItem;
    }

    @Override
    public List<AccDeviceItem> getByCondition(AccDeviceItem condition) {
        return (List<AccDeviceItem>)accDeviceDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        Pager pager = accDeviceDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        if (condition instanceof AccDeviceItem) {
            List<AccDeviceItem> deviceItems = (List<AccDeviceItem>)pager.getData();
            buildDeviveInfo(deviceItems);
            pager.setData(deviceItems);
        }
        return pager;
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            // 判断门禁设备是否在其他模块使用
            isUsedAccDevInOtherModule(ids);
            // 删除vms模块可视对讲设备
            delIntercomDeviceByAccDevIds(ids);

            List<AccDevice> wgDevList =
                accDeviceDao.getAsWGReaderDevByDevId((List<String>)CollectionUtil.strToList(ids));
            for (AccDevice wgDev : wgDevList) {
                wgDev.setWgReaderId(null);
                accDeviceDao.save(wgDev);
                // 同时把对应韦根读头一体机从原来设备的权限组中删除
                accLevelService.delLevelByDevId(wgDev.getId());
            }
            List<String> devSnList = Lists.newArrayList();
            List<AccDevice> devList = accDeviceDao.findByIdIn((List<String>)CollectionUtil.strToList(ids));
            StringBuffer parentIds = new StringBuffer();
            StringBuffer devIds = new StringBuffer();
            Map<AccDevice, List<String>> delAuthMap = new HashMap<>();
            for (AccDevice dev : devList) {
                // 有子设备存在，需要先删除子设备 by juvenile.li 20170907
                if (!dev.getChildDevices().isEmpty()) {
                    for (AccDevice childDev : dev.getChildDevices()) {
                        devSnList.add(childDev.getSn());
                        delDevice(childDev);
                        devIds.append(childDev.getId() + ",");
                        // 修改子设备授权标记, 防止子设备重新上报, 导致删除失败
                        admsAuthDeviceService.resetAuthDevice(childDev.getSn());
                    }
                    // 关联删除子设备时, 往父设备下发解绑子设备命令
                    accDevCmdManager.delChildDevice(dev, devSnList, true);
                }
                parentIds.append(dev.getId() + ",");
                // 上面子设备可能已经被删除了，不需要再次删除
                if (!devSnList.contains(dev.getSn())) {
                    devSnList.add(dev.getSn());
                    delDevice(dev);
                    AccDevice parentDev = dev.getParentDevice();
                    // 删除子设备，要往父设备下发解绑子设备命令 by juvenile.li add 20170816
                    if (parentDev != null) {
                        parentDev.getChildDevices().remove(dev);
                        // 修改子设备授权标记, 防止子设备重新上报, 导致删除失败
                        admsAuthDeviceService.resetAuthDevice(dev.getSn());
                        if (delAuthMap.containsKey(parentDev)) {
                            List<String> childSnList = delAuthMap.get(parentDev);
                            if (!childSnList.contains(dev.getSn())) {
                                childSnList.add(dev.getSn());
                            }
                        } else {
                            List<String> childSnList = new ArrayList<>();
                            childSnList.add(dev.getSn());
                            delAuthMap.put(parentDev, childSnList);
                        }
                        // 父设备的权限组需要更新一下 TODO 暂时注释掉
                        // Set<AccLevel> accLevelSet = Sets.newHashSet();
                        // for (AccDevice accDevice : parentDev.getChildDevices())
                        // {
                        // accLevelSet.addAll(accDevice.getAccLevelSet());
                        // }
                        // parentDev.setAccLevelSet(accLevelSet);
                        // merge(parentDev);
                    }
                }
            }
            // 下发删除子设备授权信息
            delAuthMap.forEach((parentDev, childSnList) -> {
                accDevCmdManager.delChildDevice(parentDev, childSnList, true);
            });

            // 先删除非父设备，在删除父设备，不然会导致级联删除出错 by juvenile.li add 20171013
            if (devIds.length() > 0) {
                deleteByDevIds(devIds.substring(0, devIds.length() - 1));
            }
            if (parentIds.length() > 0) {
                deleteByDevIds(parentIds.substring(0, parentIds.length() - 1));
            }
            // 删除设备后再删除adms设备数据
            if (devSnList.size() > 0) {
                devSnList.forEach(devSn -> {
                    admsDeviceService.delBySn(devSn);
                    accCacheManager.delDeviceInfo(devSn);
                    accCacheManager.delDeviceOptionInfo(devSn);
                    accCacheManager.deleteDevState(devSn);
                    accCacheManager.delete(AccCacheKeyConstants.ACC_DEVICE_EVENT + devSn);
                });
                accCloudService.deleteCloudDevice(devSnList);
            }
            return true;
        }
        return false;
    }

    /**
     * 删除vms模块中的可视对讲设备
     *
     * @param devIds: 设备id
     * @return void
     * <AUTHOR>
     * @date 2020-11-02 17:28
     * @since 1.0.0
     */
    private void delIntercomDeviceByAccDevIds(String devIds) {
        if (Objects.nonNull(acc4VmsDeviceService) && StringUtils.isNotBlank(devIds)) {
            List<AccDevice> accDeviceList = accDeviceDao.findByIdIn(StrUtil.strToList(devIds));
            if (Objects.nonNull(accDeviceList) && !accDeviceList.isEmpty()) {
                String devSns = CollectionUtil.getPropertys(accDeviceList, AccDevice::getSn);
                acc4VmsDeviceService.deleteDeviceBySn(devSns);
            }
        }
        if (Objects.nonNull(acc4IvsDeviceService) && StringUtils.isNotBlank(devIds)) {
            List<String> devSns = accDeviceDao.getSnByIdIn(StrUtil.strToList(devIds));
            if (Objects.nonNull(devSns) && !devSns.isEmpty()) {
                List<String> supportNVRSns = new ArrayList<>();
                List<String> supportVisualIntercomFunOnSns = new ArrayList<>();
                for (String sn : devSns) {
                    // 判断设备是否支持可视对讲功能
                    boolean visualIntercomFunOn = accDeviceOptionService.isSupportFun(sn, "VisualIntercomFunOn");
                    boolean supportNvr = accDeviceOptionService.isSupportFun(sn, "SupportNVR");
                    if (visualIntercomFunOn) {
                        supportVisualIntercomFunOnSns.add(sn);
                    } else if (supportNvr)
                        supportNVRSns.add(sn);
                }
                if (supportVisualIntercomFunOnSns.size() > 0) {
                    acc4IvsDeviceService.deleteDeviceBySn(supportVisualIntercomFunOnSns, "IPC");
                }
                if (supportNVRSns.size() > 0) {
                    acc4IvsDeviceService.deleteDeviceBySn(supportNVRSns, "NVR");

                }
            }
        }
    }

    /**
     * 删除时判断门禁设备在其他模块是否被占用
     *
     * @param ids
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-19 11:59
     */
    private void isUsedAccDevInOtherModule(String ids) {
        if (accDevice4OtherServices != null) {
            Arrays.stream(accDevice4OtherServices).forEach(accDevice4OtherService -> {
                accDevice4OtherService.checkAccDeviceIsUsedByDevIds(ids);
            });
        }
        if (accDoor4OtherServices != null) {
            List<AccDoorItem> accDoors = accDoorService.getItemsByDevIds(StrUtil.strToList(ids));
            String doorIds = CollectionUtil.getItemIds(accDoors);
            Arrays.stream(accDoor4OtherServices).forEach(accDoor4OtherService -> {
                accDoor4OtherService.checkAccDoorIsUsed(doorIds);
            });
        }
        // 存在停车场判断是否使用门禁设备控制；有则提示不让删除
        /*if (Objects.nonNull(acc4ParkAccDoorService)) {
            if (acc4ParkAccDoorService.isUsedAccDeviceInPark(ids)) {
                throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_WARN, "park_accDoor_accDeviceIsUsed");
            }
        }
        if (Objects.nonNull(acc4LineAccDoorService)) {
            List<AccDoorItem> accDoors = accDoorService.getItemsByDevIds(StrUtil.strToList(ids));
            if (acc4LineAccDoorService.isUsedAccReaderInLine(CollectionUtil.getItemIds(accDoors))) {
                throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_WARN, "common_dev_delDevFail");
            }
        }*/
    }

    private void deleteByDevIds(String devIds) {
        String[] idArray = StringUtils.split(devIds, ",");
        for (String id : idArray) {
            accDeviceDao.deleteById(id);
        }
        // 更新许可
        accLicensePointsCheckService.update();
    }

    private void delDevice(AccDevice dev) {
        if (accDevice4OtherServices != null) {
            Arrays.stream(accDevice4OtherServices).forEach(accDevice4OtherService -> {
                accDevice4OtherService.checkAccDeviceIsUsedByDevSn(dev.getSn());
            });
        }
        if (accReader4OtherServices != null) {
            List<AccReader> accReaders = accReaderDao.findByAccDoor_Device_Id(dev.getId());
            List<String> readerIds = (List<String>)CollectionUtil.getModelIdsList(accReaders);
            Arrays.stream(accReader4OtherServices).forEach(accReader4OtherService -> {
                accReader4OtherService.checkAccReaderIsUsed(readerIds);
            });
        }
        if (accDevice4OtherServices != null) {
            Arrays.stream(accDevice4OtherServices).forEach(accDevice4OtherService -> {
                accDevice4OtherService.delDeviceByDevSn(dev.getSn());
            });
        }

        vmsDeleteGLink(dev);
        // 扩展板要先删除
        List<AccExtDevice> extDeviceList = accExtDeviceDao.findByDevIdIn(CollectionUtil.strToList(dev.getId()));
        accExtDeviceDao.delete(extDeviceList);
        String doorIds = CollectionUtil.getModelIds(dev.getAccDoorList());
        delDoorAndAuxOfMap(dev);// 删除地图上的辅助输入输出和门
        delChannelBind(dev);// 删除摄像头绑定
        delFirstOpen(doorIds);// 删除首人开门
        delInterLock(dev);// 删除互锁
        delAntipassback(dev);// 删除反潜
        // delDevOfGLinkFromCache(dev);//删除设备时，需要删除全局联动中关于该设备的相关参数，和redis中的值。
        // 删除Redis中高级门禁数据----读头定义，路线定义，全局互锁，全局反潜
        delAdvanceData(dev);
        // 删除pro模块中门禁数据 ---- 验证规则读头、pro模块设备缓存
        delProExtData(dev);
        accCacheManager.delDevLinkInfo(dev);
        accCacheManager.delDeviceLastError(dev.getSn());// 移除设备最近异常状态
        accCacheManager.delDeviceLastRTLog(dev.getSn());// 移除设备上一条实时记录
    }

    private void vmsDeleteGLink(AccDevice dev) {
        if (Objects.nonNull(acc4VmsGlobalLinkageService)) {
            List<String> accAuxOutIds =
                dev.getAccAuxOutList().stream().map(AccAuxOut::getId).collect(Collectors.toList());
            List<String> accDoorIds = dev.getAccDoorList().stream().map(AccDoor::getId).collect(Collectors.toList());
            if (accDoorIds.size() > 0) {
                acc4VmsGlobalLinkageService.deleteGlinkDoor(accDoorIds);
            }
            if (accAuxOutIds.size() > 0) {
                acc4VmsGlobalLinkageService.deleteGlinkAuxOut(accAuxOutIds);
            }
        }
    }

    private void delAntipassback(AccDevice dev) {
        accAntiPassbackService.delAntipassbackByDevId(dev.getId());
    }

    private void delInterLock(AccDevice dev) {
        accInterlockService.delInterLockByDevId(dev.getId());
    }

    private void delFirstOpen(String doorIds) {
        List<AccFirstOpen> accFirstOpenList =
            accFirstOpenDao.findByAccDoor_IdIn((List<String>)CollectionUtil.strToList(doorIds));
        if (Objects.nonNull(accFirstOpenList) && accFirstOpenList.size() > 0) {
            accFirstOpenService.deleteByIds(CollectionUtil.getModelIds(accFirstOpenList));
        }
    }

    private void delAdvanceData(AccDevice dev) {
        if (acc4AccGlobalLinkageService != null) {
            acc4AccGlobalLinkageService.delGlinkByDevId(dev.getId());
        }
        String doorIds = CollectionUtil.getModelIds(dev.getAccDoorList());
        // DU门可以全部删除，执行下面会引起出错 by juvenile add 20160720
        if (StringUtils.isNotBlank(doorIds)) {
            if (acc4AccGlobalInterlockService != null) {
                acc4AccGlobalInterlockService.delGlobalInterlockDoorByDoorId(doorIds);// 删除全局互锁
            }
            List<AccReader> accReaderList = accReaderDao.findByAccDoor_Device_Id(dev.getId());
            for (AccReader reader : accReaderList) {
                if (acc4AccReaderZoneService != null) {
                    acc4AccReaderZoneService.delReaderZoneByReaderId(reader.getId());
                }
            }
        }
    }

    private void delChannelBind(AccDevice dev) {
        if (Objects.nonNull(vid4OtherChannel2EntityService)) {
            StringBuffer delIdBuffer = new StringBuffer();
            for (AccDoor accDoor : dev.getAccDoorList()) {
                for (AccReader reader : accDoor.getAccReaderList()) {
                    delIdBuffer.append(reader.getId()).append(",");
                }
            }
            if (delIdBuffer.length() > 0) { // 删除设备的摄像头绑定
                vid4OtherChannel2EntityService.deleteByEntityIdAndEntityClassName(
                    delIdBuffer.substring(0, delIdBuffer.length() - 1), AccReader.class.getSimpleName());
            }
            String delAuxInIds = CollectionUtil.getModelIds(dev.getAccAuxInList());
            if (delAuxInIds.length() > 0) { // 删除辅助输入的摄像头绑定
                vid4OtherChannel2EntityService.deleteByEntityIdAndEntityClassName(delAuxInIds,
                    AccAuxIn.class.getSimpleName());
            }
        }
    }

    private void delDoorAndAuxOfMap(AccDevice dev) {
        List<String> delIdList = new ArrayList<>();
        for (AccDoor door : dev.getAccDoorList()) {
            delIdList.add(door.getId());
        }
        if (delIdList.size() > 0) {
            accMapPosService.delMapPosByEntityId(delIdList, AccDoor.class.getSimpleName());
        }
        delIdList = new ArrayList<>();
        for (AccAuxIn auxIn : dev.getAccAuxInList()) {
            delIdList.add(auxIn.getId());
        }
        if (delIdList.size() > 0) {
            accMapPosService.delMapPosByEntityId(delIdList, AccAuxIn.class.getSimpleName());
        }
        delIdList = new ArrayList<>();
        for (AccAuxOut auxOut : dev.getAccAuxOutList()) {
            delIdList.add(auxOut.getId());
        }
        if (delIdList.size() > 0) {
            accMapPosService.delMapPosByEntityId(delIdList, AccAuxOut.class.getSimpleName());
        }
        delIdList = new ArrayList<>();
        for (AccAuxOut auxOut : dev.getAccAuxOutList()) {
            delIdList.add(auxOut.getId());
        }
        if (delIdList.size() > 0) {
            accMapPosService.delMapPosByEntityId(delIdList, AccAuxOut.class.getSimpleName());
        }
    }

    @Override
    public AccDeviceItem getItemById(String id) {
        List<AccDeviceItem> items = getByCondition(new AccDeviceItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public List<String> getDevIdByAuthAndTrigger(List<Integer> triggerNoList) {
        List<AccDevice> httpDevList = accDeviceDao.getDevByTypeAndAuth(ConstUtil.COMM_HTTP);
        List<AccDevice> tcpipDevList = accDeviceDao.getDevByTypeAndAuth(ConstUtil.COMM_TCPIP);
        List<AccDevice> rs485DevList = accDeviceDao.getDevByTypeAndAuth(ConstUtil.COMM_RS485);
        List<String> filterDevIdList = new ArrayList<>();
        for (AccDevice accDevice : httpDevList) {
            List<AccDeviceEvent> eventList = new ArrayList<>();
            eventList.addAll(accDevice.getAccDeviceEventList());
            // 需要过滤的设备
            if (getFilteredDev(triggerNoList, eventList)) {
                filterDevIdList.add(accDevice.getId());
            }
        }
        for (AccDevice accDevice : tcpipDevList)// 过滤所有pull设备
        {
            filterDevIdList.add(accDevice.getId());
        }
        for (AccDevice accDevice : rs485DevList)// 过滤所有pull设备
        {
            filterDevIdList.add(accDevice.getId());
        }
        return filterDevIdList;
    }

    private boolean getFilteredDev(List<Integer> triggerNoList, List<AccDeviceEvent> eventList) {
        boolean isDevFilter = false;// 是否需要过滤该设备 true：过滤 false：不过滤
        for (int i = 0; i < triggerNoList.size(); i++) {
            boolean isFilter = false;// 事件是否包含此事件编号
            int triggerNo = triggerNoList.get(i);
            for (AccDeviceEvent event : eventList) {
                if (triggerNo == event.getEventNo()) {
                    isFilter = true;
                }
            }
            // 一个设备对应的事件中包含了此事件编号和名称
            if (!isFilter) {
                isDevFilter = true;
                break;
            }
        }
        return isDevFilter;
    }

    @Override
    public List<AccDeviceItem> getDevByLevel(String levelIds) {
        List<AccDevice> devList = new ArrayList<AccDevice>();
        List<AccDeviceItem> deviceItemList = new ArrayList<AccDeviceItem>();
        if (levelIds != null && !"".equals(levelIds)) {
            devList = accDeviceDao.getDevByLevel((List<String>)CollectionUtil.strToList(levelIds));
        }
        devList.forEach(dev -> {
            AccDeviceItem accDeviceItem = new AccDeviceItem();
            ModelUtil.copyPropertiesIgnoreNull(dev, accDeviceItem);
            deviceItemList.add(accDeviceItem);
        });
        return deviceItemList;
    }

    @Override
    public List<AccSearchDeviceItem> searchDeviceList() {
        List<AdmsAuthDeviceItem> admsAuthDeviceItems = admsAuthDeviceService.searchDevice(BaseConstants.ACC);
        List<AccSearchDeviceItem> searchDeviceItems = Lists.newArrayList();
        if (!admsAuthDeviceItems.isEmpty()) {
            searchDeviceItems = ModelUtil.copyListProperties(admsAuthDeviceItems, AccSearchDeviceItem.class);

            searchDeviceItems.forEach(accSearchDeviceItem -> {
                String accSupportFunList = accSearchDeviceItem.getAccSupportFunList();
                if (StringUtils.isNotBlank(accSupportFunList)) {
                    accSearchDeviceItem
                        .setIsSupportMultiCard(accDeviceOptionService.getAccSupportFunListVal(7, accSupportFunList));
                }
                if (StringUtils.isNotBlank(accSearchDeviceItem.getParentSn())) {
                    AccDevice parentDev = accDeviceDao.findBySn(accSearchDeviceItem.getParentSn());
                    if (parentDev != null) {
                        accSearchDeviceItem.setParentDevId(parentDev.getId());
                        accSearchDeviceItem.setParentDevAlias(parentDev.getAlias());
                    }
                }
            });
        }
        return searchDeviceItems;
    }

    @Override
    public int modifyDeviceOptions(String options, String pwd) {
        return admsDeviceService.modifyDeviceOptions(options, pwd);
    }

    @Override
    public boolean authDevice(String sn, AccSearchAddDeviceItem item) {
        AdmsAuthDeviceItem admsAuthDeviceItem = admsAuthDeviceService.authDevice(sn);
        accCacheManager.putTempDevice2Cache(sn, item);
        return admsAuthDeviceItem.getAuthFlag() != null ? admsAuthDeviceItem.getAuthFlag() : false;
    }

    @Override
    public boolean setPushDevice(String deviceJsonStr) {
        JSONObject devInfo = JSONObject.parseObject(deviceJsonStr);
        AccSearchAddDeviceItem tempInfo = accCacheManager.getTempDevFromCache(devInfo.getString("SN"));
        String ret = "";
        boolean dealResult = false;
        // 如果设备支持时区，在devInfo中添加一个时区键值对
        if (devInfo.containsKey("MachineTZFunOn")) {
            Calendar cal = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("Z");
            devInfo.put("TimeZone", sdf.format(cal.getTime()));
        }

        ret = updateDevInfo(devInfo, tempInfo);
        if (!"noLicense".equals(ret)) {
            dealResult = true;
            // 设备添加命令下发
            AccDevice accDevice = accDeviceDao.findBySn(devInfo.getString("SN"));
            // 含有ParentSN 是属于授权进来的，默认也清除用户数据
            if ((tempInfo != null && (String.valueOf(AccConstants.ENABLE)).equals(tempInfo.getClearAllData())
                || devInfo.containsKey("ParentSN"))) {
                clearAllDataFromDev(accDevice); // 清空设备数据
            }
            if ("add".equals(ret)) {
                initDevConfig(accDevice);// 初始化设备中表和参数设置
                if (Objects.nonNull(tempInfo)) {
                    if (StringUtils.isNotBlank(tempInfo.getLevelId())) {
                        List<AccDoor> doorList = accDoorDao.findByDevice_Id(accDevice.getId());
                        List<String> doorIdList = (List<String>)CollectionUtil.getModelIdsList(doorList);
                        if (doorIdList.size() > 0) {
                            AccLevel accLevel = accLevelDao.findById(tempInfo.getLevelId()).orElse(null);
                            if (accLevel != null) {
                                String doorIds = String.join(",", doorIdList);
                                accLevelService.addLevelByParamIds(doorIds, accLevel.getId(), "door");
                                accLevelService.immeAddLevelDoor(accLevel.getId(), doorIdList, true);
                                // 拓展接口，为了兼容华为pad设备下发downLoad命令通知设备已下发完成的功能 by mingfa.zheng 20200727
                                accDevCmdManager.downloadOver(accDevice.getSn(), false);
                            }
                        }
                    }
                    accCacheManager.delTempDevFromCache(tempInfo.getSn());
                }
            }
        }
        return dealResult;
    }

    /**
     * 设备信息入库
     *
     * @param devInfo
     * @param tempInfo
     * @return
     */
    private String updateDevInfo(JSONObject devInfo, AccSearchAddDeviceItem tempInfo) {
        AccDevice device = accDeviceDao.findBySn(devInfo.getString("SN"));
        if (device == null) {
            logger.info("----AccDevice addDevInfo---begin----" + devInfo.getString("SN"));
            short commType = devInfo.getShortValue("CommType");
            short machineType = devInfo.getShortValue("MachineType");
            if (!"zh_CN".equals(language) && commType == ConstUtil.COMM_HTTP) {// push机器校验准入机型，只针对海外版起作用
                String regDeviceType = devInfo.getString("RegDeviceType");
                if (!validDevRegDeviceType(regDeviceType)) {// 设备准入机型不符合
                    return "noLicense";
                }
            }
            if (commType == ConstUtil.COMM_HTTP && machineType != AccConstants.DEVICE_BIOIR_9000)// push
            {
                if (accLicensePointsCheckService.isZKBioCVV6000Server()) {
                    ZKResultMsg checkRet = accLicensePointsCheckService.check();
                    if (!checkRet.isSuccess()) {
                        return "noLicense";
                    }
                } else {
                    Map<String, Integer> countMap = getAfterLicensedCount(1, 1, AccConstants.COMM_TYPE_PUSH_HTTP);// 门数量修改为1，至少有一个点数都可以添加
                    logger.info("--------------------------pull license:"
                        + baseLicenseProvider.getDeviceCount(ConstUtil.LICENSE_MODULE_ACC_PULL));
                    logger.info("--------------------------push license:"
                        + baseLicenseProvider.getDeviceCount(ConstUtil.LICENSE_MODULE_ACC_PUSH));
                    logger.info("-------------0=hy-1=gn-------------language:" + baseLicenseProvider.getLanguagePack());
                    ResultCode resultCode =
                        baseLicenseProvider.isCountOutRangeAcc(ConstUtil.LICENSE_MODULE_ACC_PUSH, countMap);
                    logger.info("-------------add push dev-------------resultCode:" + resultCode);
                    if (ResultCode.SUCCESS != resultCode) {
                        logger.info("-------pushDev_acc" + devInfo.getString("SN")
                            + " auto add failed for license, and licenseCount is "
                            + baseLicenseProvider.getDeviceCount(ConstUtil.LICENSE_MODULE_ACC_PUSH)
                            + ", current pushGateCount is " + getBeforeLicensedCount().get("pushGateCount")
                            + ", current pushDevCount is " + getBeforeLicensedCount().get("pushDevCount"));
                        return "noLicense";
                    }
                }
            }
            device = new AccDevice();
            // 子设备注册信息过来，会带上parentSN
            if (devInfo.containsKey("ParentSN")) {
                // 子设备注册成功了，需往父设备下发子设备授权成功了
                AccDevice parentDev = accDeviceDao.findBySn(devInfo.getString("ParentSN"));
                device.setParentDevice(parentDev);
                accDevCmdManager.authorizeChildDevice(parentDev, Lists.newArrayList(devInfo.getString("SN")),
                    AccConstants.DEVICE_AUTHORIZED, true);
                accCacheManager.delDeviceAuthorizeInfo(parentDev.getSn());
            }
            if (tempInfo != null && tempInfo.getIconType() != null) {
                device.setIconType(Short.parseShort(tempInfo.getIconType()));// 设置图标类型
            }
            device.setMachineType(machineType);// 设备类型
            if (commType != ConstUtil.COMM_RS485) {
                device.setIpAddress(devInfo.getString("IPAddress"));// ip地址
                if (devInfo.containsKey("IpPort")) {
                    device.setIpPort(devInfo.getIntValue("IpPort"));// ip 端口
                }
                if (devInfo.containsKey("Alias"))// 设备名称
                {
                    device.setAlias(devInfo.getString("Alias"));
                } else {
                    // push设备的设备名称从redis中取值 by dyl
                    if (tempInfo != null && tempInfo.getDevName() != null) {
                        device.setAlias(tempInfo.getDevName());
                    } else {
                        device.setAlias(devInfo.getString("IPAddress")); // redis中没有值则设置为ip
                    }
                }
            } else {
                // 485连接方式，使用”串口号_机器号“的方式显示设备别名
                String alias = String.format("COM%s_%s", devInfo.getString("ComPort"), devInfo.getString("ComAddress"));
                device.setAlias(devInfo.containsKey("Alias") ? devInfo.getString("Alias") : alias);
                device.setComPort((short)devInfo.getIntValue("ComPort"));
                device.setBaudrate(devInfo.getIntValue("Baudrate"));
                device.setComAddress((short)devInfo.getIntValue("ComAddress"));
            }

            if (machineType == AccConstants.DEVICE_C3_400_TO_200) {
                device.setAcpanelType((short)4); // 如果是4门转2门设备需要写死设备类型是4门的
                device.setFourToTwo(true);
            } else if (machineType == AccConstants.DEVICE_ACCESS_CONTROL) {
                device.setAcpanelType(AccConstants.ACCESS_CONTROL_DEVICE);
                device.setFourToTwo(false);
            } else {
                device.setAcpanelType(Short.parseShort(devInfo.getString("LockCount")));
                device.setFourToTwo(false);
            }

            device.setSn(devInfo.getString("SN"));// 将sn添加到对象
            device.setDeviceName(devInfo.getString("~DeviceName"));// 将设备型号添加到对象
            device.setCommType(commType);// 通信方式
            String commPwd = devInfo.containsKey("CommPwd") ? devInfo.getString("CommPwd")
                : (tempInfo != null && tempInfo.getCommPwd() != null ? tempInfo.getCommPwd() : "");
            device.setCommPwd(commPwd);
            device.setGateway(devInfo.getString("GATEIPAddress"));// 网关
            device.setSubnetMask(devInfo.getString("NetMask"));// 子网掩码
            device.setFwVersion(devInfo.getString("FirmVer"));// 固件版本

            if (devInfo.containsKey("IconType")) {
                device.setIconType(Short.valueOf(devInfo.getString("IconType")));// 图标类型
            }
            String pushAreaId;// 默认区域id
            if (tempInfo != null && tempInfo.getAuthAreaId() != null) {
                pushAreaId = tempInfo.getAuthAreaId();
            } else {
                AuthAreaItem authAreaItem = new AuthAreaItem();
                authAreaItem.setInitFlag(true);
                pushAreaId = authAreaService.getByCondition(authAreaItem).get(0).getId();// 获取初始化区域id
            }
            device.setAuthAreaId(devInfo.containsKey("BaseArea") ? devInfo.getString("BaseArea") : pushAreaId);
            device.setEnabled(true);// 是否启用
            device.setIsRegistrationDevice(false);// 是否为登记机
            device.setTimeZone(devInfo.containsKey("TimeZone") ? devInfo.getString("TimeZone") : "");
            if (device.getBusinessId() == null) {
                device.setBusinessId(createBId());
            }

            logger.info("----device added to the database--- begin----" + devInfo.getString("SN"));
            accDeviceDao.save(device);// 将对象保存到数据库
            logger.info("----device added to the database---end----" + devInfo.getString("SN"));
            boolean updateRet = accLicensePointsCheckService.update();
            if (!updateRet) {
                logger.warn("acc license points over");
                // 数据回滚
                accDeviceDao.delete(device);
                // 许可点数不足
                return "noLicense";
            } else {
                // 调用保存参数的方法---设备如果参数变化了。想另外的办法来触发该方法。
                logger.info("----AccDevice---addDevOpt(devInfo)----begin---" + devInfo.getString("SN"));
                List<AccDeviceOption> accDeviceOptionList = addDevOpt(devInfo, device);// 设备参数

                logger.info("----AccDevice---addDevOpt(devInfo)----end---" + devInfo.getString("SN"));
                // 获取第二网卡的IP，子网掩码，网关 juvenile.li add 20171219
                String accSupportFunList =
                    devInfo.containsKey("AccSupportFunList") ? devInfo.getString("AccSupportFunList") : "";
                if (accDeviceOptionService.getAccSupportFunListVal(28, accSupportFunList)) {
                    List<String> optionsList = Lists.newArrayList();
                    optionsList.add("IPAddress1");
                    optionsList.add("NetMask1");
                    optionsList.add("GATEIPAddress1");
                    accDevCmdManager.getDeviceOption(device, optionsList, true);
                }
                // 添加门和读头
                List<AccDoor> accDoorList = addDoorAndReader(devInfo, device);
                logger.info("Add Doors and Readers");
                // 添加辅助输入
                List<AccAuxIn> accAuxInList = addAuxIn(devInfo, device);
                logger.info("Add AuxIn");
                List<AccAuxOut> accAuxOutList = addAuxOut(devInfo, device);
                logger.info("Add AuxOut");

                // 将参数信息保存到缓存
                if (Objects.nonNull(accDeviceOptionList) && !accDeviceOptionList.isEmpty()) {
                    device.setAccDeviceOptionList(accDeviceOptionList);
                }
                if (Objects.nonNull(accAuxInList) && !accAuxInList.isEmpty()) {
                    device.setAccAuxInList(accAuxInList);
                }
                if (Objects.nonNull(accAuxOutList) && !accAuxOutList.isEmpty()) {
                    device.setAccAuxOutList(accAuxOutList);
                }
                if (Objects.nonNull(accDoorList) && !accDoorList.isEmpty()) {
                    device.setAccDoorList(accDoorList);
                }
                // 设置完门、辅助输入、辅助输出之后再将对象保存到缓存中
                putQueryItemToCache(device, true);
                List<AccDeviceEvent> accDeviceEventList = addEvent(devInfo, device);// 添加事件类型
                logger.info("Add addEvent");
                List<AccDeviceVerifyMode> accDeviceVerifyModeList = addVerifyMode(devInfo, device);// 添加验证方式
                logger.info("Add addVerifyMode");
                // 将参数信息保存到缓存
                if (Objects.nonNull(accDeviceEventList) && !accDeviceEventList.isEmpty()) {
                    device.setAccDeviceEventList(accDeviceEventList);
                }
                if (Objects.nonNull(accDeviceVerifyModeList) && !accDeviceVerifyModeList.isEmpty()) {
                    device.setAccDeviceVerifyModeList(accDeviceVerifyModeList);
                }
                /*if (StringUtils.isNotBlank(visualIntercomFunOn)
                    && AccConstants.DISABLE_OPTION != Short.parseShort(visualIntercomFunOn)
                    && "4".equals(bitwiseANDWith4 + "")) {
                    logger.info("-----add Intercom device , device IP is -----" + devInfo.getString("IPAddress"));
                    addIntercomDeviceToOtherModule(devInfo, device.getAlias(), pushAreaId);
                }*/
                if (AccConstants.ACC_DEVICE_SUPPORTNVR.equals(devInfo.getString("SupportNVR"))) {
                    logger.info("-----add NVR device, device IP is -----{}", devInfo.getString("IPAddress"));
                    addNVRDeviceToOtherModule(devInfo, tempInfo);
                }
                // best协议用于判断支持sip可视对讲
                boolean sipOn = "1".equals(devInfo.getString("sipOn"));
                // push协议用于判断支持sip可视对讲
                boolean supportVisualIntercom = "1".equals(devInfo.getString("VisualIntercomFunOn"))
                    && ((devInfo.getIntValue("VideoProtocol") & 8) == 8);
                if (sipOn || supportVisualIntercom) {
                    logger.info("-----add sip device, device IP is -----{}", devInfo.getString("IPAddress"));
                    addSipDevice(devInfo, tempInfo);
                }

                logger.info("----AccDevice---addDevInfo---end----{}", devInfo.getString("SN"));
            }
            return "add";
        } else {
            logger.info("----AccDevice updateDevInfo---begin----" + devInfo.getString("SN"));
            // 配置主设备，转移过来的 by juvenile.li add 20170817
            if (devInfo.containsKey("ParentSN") && device.getParentDevice() != null) {
                AccDevice parentDev = device.getParentDevice();
                device.setParentDevice(parentDev);
                accDevCmdManager.authorizeChildDevice(parentDev, Lists.newArrayList(devInfo.getString("SN")),
                    AccConstants.DEVICE_AUTHORIZED, true);
                // 删除redis中虚拟待授权子设备
                accCacheManager.delDeviceAuthorizeInfo(parentDev.getSn());
            }
            // 修改设备信息
            modifyDeviceInfo(device, devInfo);
            accDeviceDao.save(device);
            // 更新设备参数值
            updateDevOpt(devInfo.toString(), device);
            if (accCacheManager.exists("adms:deviceReplace:" + devInfo.getString("SN"))) {
                accCacheManager.delete("adms:deviceReplace:" + devInfo.getString("SN"));
                logger.info("----AccDevice---replaceDevInfo---end----" + devInfo.getString("SN"));
            }
            logger.info("----AccDevice---updateDevInfo---end----" + devInfo.getString("SN"));
            return "edit";
        }
    }

    /**
     * 修改设备信息
     */
    private void modifyDeviceInfo(AccDevice accDevice, JSONObject devInfo) {
        if (Objects.nonNull(devInfo)) {
            // 更新设备参数
            Map<String, String> devInfoMap = new HashMap<>();
            for (String key : devInfo.keySet()) {
                devInfoMap.put(key, devInfo.get(key) + "");
            }
            updateDevOption(accDevice, devInfoMap);
        }
    }

    /**
     * 更新设备参数
     */
    private void updateDevOpt(String devInfoStr, AccDevice device) {
        JSONObject devInfo = JSONObject.parseObject(devInfoStr);
        String sn = device.getSn();
        Map<String, String> optionMap = accDeviceOptionService.getDevOptionBySn(sn);
        List<AccDeviceOption> optionList = new ArrayList<>();
        for (String key : devInfo.keySet()) {
            String newVal = devInfo.getString(key);
            if (StringUtils.isBlank(newVal)) {
                continue;
            }
            if (optionMap.containsKey(key)) {
                String optValue = optionMap.get(key);
                if (!optValue.equals(newVal)) {
                    AccDeviceOption accDeviceOption = accDeviceOptionDao.findByAccDevice_SnAndName(sn, key);
                    accDeviceOption.setValue(newVal);
                    optionList.add(accDeviceOption);
                }
            } else {
                AccDeviceOption accDevOpt = new AccDeviceOption();
                accDevOpt.setAccDevice(device);
                accDevOpt.setName(key);
                accDevOpt.setValue(newVal);
                if (AccConstants.DEV_COMM_KEY_PARAM_LIST.contains(key)) {
                    // 表示该参数为通信关键参数.
                    accDevOpt.setType(ConstUtil.DEV_COMM_PARAM);
                } else if (AccConstants.DEV_FUN_KEY_PARAM_LIST.contains(key)) {
                    // 表示该参数为设备扩展功能关键参数.
                    accDevOpt.setType(ConstUtil.DEV_FUN_PARAM);
                }
                optionList.add(accDevOpt);
            }
        }
        if (optionList.size() > 0) {
            accDeviceOptionDao.saveAll(optionList);
            for (AccDeviceOption accDevOpt : optionList) {
                accDeviceOptionService.updateDevOptions(sn, accDevOpt.getName(), accDevOpt.getValue());
            }
        }
    }

    /**
     * 添加可视对讲设备到其他模块
     *
     * @param devInfo:
     * @param pushAreaId:
     * @return void
     * <AUTHOR>
     * @date 2020-11-02 17:38
     * @since 1.0.0
     */
    private void addIntercomDeviceToOtherModule(JSONObject devInfo, String devName, String pushAreaId) {
        if (Objects.nonNull(acc4VmsDeviceService)) {
            Acc4VmsDeviceItem acc4VmsDeviceItem = new Acc4VmsDeviceItem();
            acc4VmsDeviceItem.setUsername(devInfo.getString("VMSUserName"));
            acc4VmsDeviceItem.setCommPwd(devInfo.getString("VMSPasswd"));
            acc4VmsDeviceItem.setHost(devInfo.getString("IPAddress"));
            acc4VmsDeviceItem.setPort(80);
            acc4VmsDeviceItem.setAlias(devInfo.getString("IPAddress"));
            // ZKTECO协议
            acc4VmsDeviceItem.setProtocol(Short.valueOf("2"));
            acc4VmsDeviceItem.setSn(devInfo.getString("SN"));
            acc4VmsDeviceItem.setAreaId(pushAreaId);
            acc4VmsDeviceService.editVmsDevice(acc4VmsDeviceItem);
        }
        if (acc4IvsDeviceService != null) {
            Acc4IntercomDeviceItem acc4IntercomDeviceItem = new Acc4IntercomDeviceItem();
            acc4IntercomDeviceItem.setUsername(devInfo.getString("VMSUserName"));
            acc4IntercomDeviceItem.setCommPwd(devInfo.getString("VMSPasswd"));
            acc4IntercomDeviceItem.setIp(devInfo.getString("IPAddress"));
            acc4IntercomDeviceItem.setPort(80);
            acc4IntercomDeviceItem.setName(devName);
            // ZKTECO协议
            acc4IntercomDeviceItem.setProtocolType("ZKTECO");
            acc4IntercomDeviceItem.setSn(devInfo.getString("SN"));
            acc4IntercomDeviceItem.setAreaId(pushAreaId);
            acc4IvsDeviceService.addIntercomDevice(acc4IntercomDeviceItem);
        }
    }

    /**
     * 添加NVR设备
     *
     * @param devInfo:
     * @param tempInfo:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-01-23 9:55
     * @since 1.0.0
     */
    private void addNVRDeviceToOtherModule(JSONObject devInfo, AccSearchAddDeviceItem tempInfo) {
        if (acc4IvsDeviceService != null) {
            Acc4NVRDeviceItem acc4NVRDeviceItem = new Acc4NVRDeviceItem();
            acc4NVRDeviceItem.setIp(devInfo.getString("IPAddress"));
            acc4NVRDeviceItem.setPort("8081");
            // ZKNVR
            acc4NVRDeviceItem.setType("7");
            acc4NVRDeviceItem.setName(devInfo.getString("IPAddress"));
            acc4NVRDeviceItem.setUser(tempInfo.getAccUsername());
            acc4NVRDeviceItem.setPassword(tempInfo.getAccPassword());
            acc4NVRDeviceItem.setDevSn(devInfo.getString("SN"));
            acc4IvsDeviceService.addNvrDevice(acc4NVRDeviceItem);
        }
    }

    /**
     * sip类设备添加可视对讲设备数据
     *
     * @param devInfo
     * @param tempInfo
     */
    private void addSipDevice(JSONObject devInfo, AccSearchAddDeviceItem tempInfo) {
        if (vdbDevice4OtherService != null) {
            List<VdbDevice4OtherItem> vdbDevice4OtherItems = new ArrayList<>();
            VdbDevice4OtherItem device = new VdbDevice4OtherItem();
            device.setName(devInfo.getString("IPAddress"));
            device.setManufacturer(Vdb4OtherConstants.VDB_MANUFACTURE_ZK);
            device.setDeviceType(devInfo.getShortValue("SipDeviceType"));
            device.setIpAddress(devInfo.getString("IPAddress"));
            device.setDeviceSn(devInfo.getString("SN"));
            device.setEnableUnit(devInfo.getString("SipEnableUnit"));
            if (tempInfo != null) {
                device.setAuthAreaId(tempInfo.getAuthAreaId());
            }
            vdbDevice4OtherItems.add(device);
            vdbDevice4OtherService.addVdbDevice(vdbDevice4OtherItems);
        }
    }

    private List<AccAuxOut> addAuxOut(JSONObject devInfo, AccDevice accDevice) {
        List<AccAuxOut> accAuxOutList = Lists.newArrayList();
        String machineTypeStr = devInfo.getString("MachineType");
        short machineType = StringUtils.isNotBlank(machineTypeStr) ? Short.parseShort(machineTypeStr) : (short)0;
        String auxOutCountStr = devInfo.getString("AuxOutCount");
        int auxOutCount = StringUtils.isNotBlank(auxOutCountStr) ? Integer.parseInt(auxOutCountStr) : 0;
        Map<String, String> outAddress = new HashMap<>();
        for (int i = 1; i <= auxOutCount; i++) {
            outAddress.put(String.valueOf(i), "AUX OUT" + i);
        }

        int auxOutNo = 1;// 辅助输出编号
        for (String key : outAddress.keySet()) {
            AccAuxOut auxOut = new AccAuxOut();
            auxOut.setAccDevice(accDevice);
            auxOut.setAuxNo(Short.parseShort(key));
            auxOut.setName(I18nUtil.i18nCode(AccConstants.AUX_OUT_NAME) + "-" + auxOutNo);
            auxOut.setPrinterNumber(outAddress.get(key));
            accAuxOutList.add(auxOut);
            auxOutNo++;
        }
        if (!accAuxOutList.isEmpty()) {
            accAuxOutDao.save(accAuxOutList);
        }
        return accAuxOutList;
    }

    private List<AccAuxIn> addAuxIn(JSONObject devInfo, AccDevice accDevice) {
        List<AccAuxIn> accAuxInList = Lists.newArrayList();
        String machineTypeStr = devInfo.getString("MachineType");
        short machineType =
            (!"".equals(machineTypeStr) && machineTypeStr != null) ? Short.parseShort(machineTypeStr) : (short)0;
        String auxInCountStr = devInfo.getString("AuxInCount");
        int auxInCount = StringUtils.isNotBlank(auxInCountStr) ? Integer.parseInt(auxInCountStr) : 0;
        String AccSupportFunList =
            devInfo.containsKey("AccSupportFunList") ? devInfo.getString("AccSupportFunList") : "";
        boolean isInoutIOSet = accDeviceOptionService.getAccSupportFunListVal(8, AccSupportFunList);// 是否支持辅助输入受时间段控制
        Map<String, String> inAddress = new HashMap<>();

        for (int i = 1; i <= auxInCount; i++) {
            inAddress.put(String.valueOf(i), "AUX IN" + i);
        }

        int auxInNo = 1;// 辅助输入编号
        String timeSegId = accTimeSegService.getInitTimeSegId();
        for (String key : inAddress.keySet()) {
            AccAuxIn auxIn = new AccAuxIn();
            auxIn.setAccDevice(accDevice);
            auxIn.setAuxNo(Short.parseShort(key));
            auxIn.setName(I18nUtil.i18nCode(AccConstants.AUX_IN_NAME) + "-" + auxInNo);
            auxIn.setPrinterNumber(inAddress.get(key));
            if (isInoutIOSet) {
                auxIn.setTimeSegId(timeSegId);
            }
            accAuxInList.add(auxIn);
            auxInNo++;
        }
        if (!accAuxInList.isEmpty()) {
            accAuxInDao.save(accAuxInList);
        }
        return accAuxInList;
    }

    /**
     * 设备初始化验证方式
     *
     * @param devInfo
     * @param accDevice
     * @return
     */
    private List<AccDeviceVerifyMode> addVerifyMode(JSONObject devInfo, AccDevice accDevice) {
        List<AccDeviceVerifyMode> verifyModeList = Lists.newArrayList();// AccVerifyMode
        String simpleEvent = devInfo.containsKey("SimpleEventType") ? devInfo.getString("SimpleEventType") : null;// 合并事件类型，只要带着个参数，值必定为1
        String verifyModeHex = devInfo.containsKey("VerifyStyles") ? devInfo.getString("VerifyStyles") : null;// 返回十六进制,"VerifyStyles":"0CDF",
        // 兼容新验证方式,NewVFStyles:0000111000011010
        String newVfStyles = devInfo.containsKey("NewVFStyles") ? devInfo.getString("NewVFStyles") : null;
        if (StringUtils.isNotBlank(newVfStyles)) {
            // 解析组装新验证方式
            char[] newBinaryVfStyles = AccDeviceUtil.newVerifyModeStrToBinary(newVfStyles);
            for (int i = 0; i < newBinaryVfStyles.length; i++) {
                // 包含且支持新验证方式
                if (AccConstants.NEW_VERIFY_MODE.containsKey(i) && AccConstants.ENABLE == newBinaryVfStyles[i]) {
                    String name = AccConstants.NEW_VERIFY_MODE.get(i);
                    AccDeviceVerifyMode verifyMode = new AccDeviceVerifyMode((short)i, name, accDevice);
                    verifyModeList.add(verifyMode);
                }
            }
        } else if (StringUtils.isNotBlank(simpleEvent) && StringUtils.isNotBlank(verifyModeHex)) { // 设备上传验证方式
            char[] binaryArry = AccDeviceUtil.getBinary(verifyModeHex);// 十六进制转化为二进制
            for (int i = 0; i < binaryArry.length; i++) {
                if (binaryArry[i] == AccConstants.ENABLED) { // 1 代表支持这个验证方式
                    if (AccConstants.VERIFY_MODE.containsKey(i)) {
                        String name = AccConstants.VERIFY_MODE.get(i);
                        AccDeviceVerifyMode verifyMode = new AccDeviceVerifyMode((short)i, name, accDevice);
                        verifyModeList.add(verifyMode);
                    }
                }
            }
        } else {
            // 旧设备验证方式
            List<Integer> tempList = new ArrayList<>();
            if (accDevice.getMachineType() == AccConstants.DEVICE_ACCESS_CONTROL) { // 一体机 TF1700老
                tempList.addAll(AccConstants.OLD_FP_VERIFY_MODE_ACD);
            } else if (!(devInfo.containsKey(AccConstants.ONLY_RF_MACHINE)
                && devInfo.get(AccConstants.ONLY_RF_MACHINE).equals(AccConstants.ENABLE.toString()))) { // inbio控制器-指纹设备
                tempList.addAll(AccConstants.OLD_FP_VERIFY_MODE_ACP);
            } else {
                tempList.addAll(AccConstants.OLD_VERIFY_MODE);// c3等
            }
            for (Integer mode : tempList) {
                if (AccConstants.VERIFY_MODE.containsKey(mode)) {
                    String name = AccConstants.VERIFY_MODE.get(mode);
                    AccDeviceVerifyMode verifyMode = new AccDeviceVerifyMode(mode.shortValue(), name, accDevice);
                    verifyModeList.add(verifyMode);
                }
            }
        }
        accDeviceVerifyModeDao.save(verifyModeList);
        return verifyModeList;
    }

    private List<AccDeviceEvent> addEvent(JSONObject devInfo, AccDevice accDevice) {
        List<AccDeviceEvent> accDeviceEventList = Lists.newArrayList();
        String simpleEvent = devInfo.containsKey("SimpleEventType") ? devInfo.getString("SimpleEventType") : null;// 合并事件类型，只要带着个参数，值必定为1
        String eventHex = devInfo.containsKey("EventTypes") ? devInfo.getString("EventTypes") : null;// 返回十六进制
        if (eventHex != null && eventHex.length() < 64 && devInfo.containsKey("~DeviceName")
            && "F2".equals(devInfo.getString("~DeviceName"))) {
            eventHex = "FFFFFF3FF0B601000000000070000000000000000000000000F70FF006000000";
        }
        if (StringUtils.isNotBlank(simpleEvent) && StringUtils.isNotBlank(eventHex)) { // 设备上传事件类型，两个参数确定合并事件
            char[] binaryArry = AccDeviceUtil.getBinary(eventHex);// 十六进制转化为二进制
            for (int i = 0; i < binaryArry.length; i++) { // 遍历，判断设备是否支持这个事件类型
                if (binaryArry[i] == AccConstants.ENABLED) { // 支持这个事件类型
                    // 事件类型国际化取值未定义
                    if (AccConstants.SIMPLE_EVENT.containsKey(i)) {
                        String eventName = AccConstants.SIMPLE_EVENT.get(i);
                        short level = accDeviceEventService.getDefaultEventTypeById(i);
                        AccDeviceEvent accEvent = new AccDeviceEvent((short)i,
                            "".equals(eventName) ? I18nUtil.i18nCode(AccConstants.EVENT_NO_UNDEFINED) : eventName,
                            accDevice, level);
                        accDeviceEventList.add(accEvent);
                    }
                }
            }
        } else { // 旧设备事件类型
            Map<Integer, String> eventMap = new HashMap<>();
            eventMap.putAll(AccConstants.OLD_EVENT);// 其他事件
            if (!(devInfo.containsKey("~IsOnlyRFMachine")
                && devInfo.get("~IsOnlyRFMachine").equals(AccConstants.ENABLED))) {
                eventMap.putAll(AccConstants.OLD_FP_EVENT);// 指纹事件
            }
            if (accDevice.getMachineType() == AccConstants.DEVICE_ACCESS_CONTROL) {
                eventMap.putAll(AccConstants.DEVICE_ACCESS_CONTROL_EVENT);// 一体机事件
                for (Integer key : AccConstants.DEVICE_ACCESS_CONTROL_EVENT_NO_SUPPORT.keySet()) {
                    eventMap.remove(key);
                }
            }
            // 判断是否支持黑名单功能
            if (!(devInfo.containsKey("DisableUserFunOn") && devInfo.get("DisableUserFunOn").equals("1"))) {
                eventMap.remove(39);
            }
            // 判断是否支持超级用户
            if (!(devInfo.containsKey("~SupAuthrizeFunOn") && devInfo.get("~SupAuthrizeFunOn").equals("1"))) {
                eventMap.remove(208);
            }
            // 判断是否支持出门开关锁定功能
            if (!(devInfo.containsKey("~REXInputFunOn") && devInfo.get("~REXInputFunOn").equals("1"))) {
                eventMap.remove(209);
            }
            for (Integer key : eventMap.keySet()) {
                short i = key.shortValue();
                short level = accDeviceEventService.getDefaultEventTypeById(i);
                AccDeviceEvent accEvent = new AccDeviceEvent(key.shortValue(), eventMap.get(key), accDevice, level);
                accDeviceEventList.add(accEvent);
            }
        }

        // 组装需入库的新事件数据
        buildNewEventData(devInfo, accDevice, accDeviceEventList);

        for (Integer eventNo : AccConstants.ACC_MONITOR_DEV_EVENT.keySet()) {
            AccDeviceEvent accEvent = new AccDeviceEvent(eventNo.shortValue(),
                AccConstants.ACC_MONITOR_DEV_EVENT.get(eventNo), accDevice, AccConstants.EVENT_ALARM);
            accDeviceEventList.add(accEvent);
        }
        if (devInfo.containsKey("AutoServerFunOn") && devInfo.get("AutoServerFunOn").equals("1")) {
            for (Integer eventNo : AccConstants.CUSTOM_EVENT_NORMAL.keySet()) { // 存入全局联动事件
                AccDeviceEvent accEvent = new AccDeviceEvent(eventNo.shortValue(),
                    AccConstants.CUSTOM_EVENT_NORMAL.get(eventNo), accDevice, AccConstants.EVENT_NORMAL);
                accDeviceEventList.add(accEvent);
            }
            for (Integer eventNo : AccConstants.ACC_ADVANCE_VALID.keySet()) {
                AccDeviceEvent accEvent = new AccDeviceEvent(eventNo.shortValue(),
                    AccConstants.ACC_ADVANCE_VALID.get(eventNo), accDevice, AccConstants.EVENT_WARNING);
                accDeviceEventList.add(accEvent);
            }
        }
        if (!accDeviceEventList.isEmpty()) {
            accDeviceEventDao.save(accDeviceEventList);
        }
        return accDeviceEventList;
    }

    private void buildNewEventData(JSONObject devInfo, AccDevice accDevice, List<AccDeviceEvent> accDeviceEventList) {
        // 遍历扩展事件参数
        for (String newEventParam : AccConstants.NEW_EVENT_OFFSET.keySet()) {
            // 新事件参数值
            String newEventTypes = devInfo.getString(newEventParam);
            if (StringUtils.isNotBlank(newEventTypes)) {
                // 事件偏移值
                Integer eventOffset = AccConstants.NEW_EVENT_OFFSET.get(newEventParam);
                // 将新事件十六进制转二进制
                char[] binaryArry = AccDeviceUtil.getBinary(newEventTypes);
                for (int i = 0; i < binaryArry.length; i++) {
                    // 支持这个事件类型
                    if (binaryArry[i] == AccConstants.ENABLED) {
                        int newEventNo = eventOffset + i;
                        if (AccConstants.NEW_EVENT.containsKey(newEventNo)) {
                            String eventName = AccConstants.NEW_EVENT.get(newEventNo);
                            short level = accDeviceEventService.getDefaultEventTypeById(newEventNo);
                            AccDeviceEvent eleEvent = new AccDeviceEvent((short)newEventNo,
                                "".equals(eventName) ? I18nUtil.i18nCode(AccConstants.EVENT_NO_UNDEFINED) : eventName,
                                accDevice, level);
                            accDeviceEventList.add(eleEvent);
                        }
                    }
                }
            }
        }
    }

    private List<AccDoor> addDoorAndReader(JSONObject devInfo, AccDevice accDevice) {
        List<AccDoor> accDoorList = Lists.newArrayList();
        List<AccReader> accReaderList = Lists.newArrayList();
        // 添加门
        String doorCountStr = devInfo.getString("LockCount");
        int doorCount = StringUtils.isNotBlank(doorCountStr) ? Integer.parseInt(doorCountStr) : 0;

        String machineTypeStr = devInfo.getString("MachineType");
        int machineType = (!"".equals(machineTypeStr) && machineTypeStr != null) ? Integer.parseInt(machineTypeStr) : 0;
        Short verifyModeNo = null;
        // 将新验证方式二进制数转为十进制数保存
        if (devInfo.containsKey("NewVFStyles")) {
            String newVerifyStyles = devInfo.getString("NewVFStyles");
            verifyModeNo = (short)AccDeviceUtil.binaryToDecimal(newVerifyStyles);
        } else if (devInfo.containsKey("VerifyStyles")) {
            String verifyModeHex = devInfo.getString("VerifyStyles");
            char[] binaryArry = AccDeviceUtil.getBinary(verifyModeHex);// 十六进制转化为二进制
            // 判断设备是否支持0号验证方式（自动识别）
            if (binaryArry.length > 0 && binaryArry[0] == AccConstants.ENABLED) {
                verifyModeNo = ConstUtil.VERIFY_MODE_CARDORFPORPWD;
            }
        }
        // 上面代码没能进行赋值说明老设备通过是否仅卡设备来决定默认验证方式
        if (verifyModeNo == null) {
            if (!(devInfo.containsKey(AccConstants.ONLY_RF_MACHINE)
                && devInfo.getString(AccConstants.ONLY_RF_MACHINE).equals(AccConstants.ENABLE.toString()))) {
                verifyModeNo = ConstUtil.VERIFY_MODE_CARDANDFP;
            } else {
                verifyModeNo = ConstUtil.VERIFY_MODE_ONLYCARD;
            }
        }

        String initTimeSegId = accTimeSegService.getInitTimeSegId();
        String ext485ReaderFunOnStr =
            devInfo.containsKey("~Ext485ReaderFunOn") ? devInfo.getString("~Ext485ReaderFunOn") : "";
        int ext485ReaderFunOn = 0;// 是否支持485读头
        if (!"".equals(ext485ReaderFunOnStr)) { // 避免固件bug，软件挂掉
            ext485ReaderFunOn = Integer.parseInt(ext485ReaderFunOnStr);
        }

        int readerNo = 0;// 读头编号
        List<PersWiegandFmtItem> defalutWiegandFmt = persWiegandFmtService.getAutoMatch();
        Collections.reverse(defalutWiegandFmt);
        int noLicensedCount = 0;
        if (baseLicenseProvider.getLanguagePack() != 1) {
            Map<String, Integer> countMap = getAfterLicensedCount(1, doorCount, AccConstants.COMM_TYPE_PUSH_HTTP);
            noLicensedCount = countMap.get("pushGateCount") + countMap.get("pullGateCount")
                - baseLicenseProvider.getDeviceCount(ConstUtil.LICENSE_MODULE_ACC_PUSH);
        }
        String accSupportFunList =
            devInfo.containsKey("AccSupportFunList") ? devInfo.getString("AccSupportFunList") : "";
        boolean isReaderDisable = accDeviceOptionService.getAccSupportFunListVal(0, accSupportFunList);// 是否支持读头禁用
        boolean isInoutIOSet = accDeviceOptionService.getAccSupportFunListVal(8, accSupportFunList);// 是否支持出门按钮受时间段控制
        boolean isEncrypt = accDeviceOptionService.getAccSupportFunListVal(1, accSupportFunList);// 是否支持读头加密功能
        boolean isSupportReaderProtity = accDeviceOptionService.getAccSupportFunListVal(18, accSupportFunList);// 是否支持读头属性
        boolean isSupportWgOrRS485 = accDeviceOptionService.getAccSupportFunListVal(32, accSupportFunList);// 是否支持混合通讯类型，韦根或RS485
        Boolean uidcard = baseLicenseProvider.checkCardControl();
        boolean isSupportOSDP = accDeviceOptionService.getAccSupportFunListVal(49, accSupportFunList);// 是否支持osdp
        boolean isSupportDM20 = accDeviceOptionService.getAccSupportFunListVal(67, accSupportFunList);// 是否支持dm20
        // 兼容机型处理完毕；
        short commAddress = 1;
        // 1：485，2：韦根，4：tcp，8：zigbee
        String supportReaderType = devInfo.getString("SupportReaderType");
        for (short i = 1; i <= doorCount; i++) {
            AccDoor accDoor = new AccDoor();
            accDoor.setDevice(accDevice);
            accDoor.setDoorNo(i);
            accDoor.setName(accDevice.getAlias() + "-" + String.valueOf(i));// 设备别名（IP）加上连字符以及门编号！！！
            accDoor.setBackLock(false);
            if (AccConstants.DEVICE_ACCESS_CONTROL == accDevice.getMachineType()) {
                accDoor.setActionInterval((short)0);// 一体机没有刷卡间隔
            } else {
                accDoor.setActionInterval((short)2);// 刷卡间隔，默认2秒
            }
            accDoor.setLockDelay((short)5);// 锁驱动时长，默认5秒
            accDoor.setVerifyMode(verifyModeNo);// 验证方式
            accDoor.setDoorSensorStatus((short)0);
            accDoor.setSensorDelay((short)15);
            accDoor.setReaderType((short)22);
            accDoor.setLatchDoorType((short)1);// 出门按钮不锁定
            if (isInoutIOSet) {
                accDoor.setLatchTimeSegId(initTimeSegId);
            }
            accDoor.setInApbDuration((short)0);
            accDoor.setWgInputFmtId(persWiegandFmtService.getDefAutoMatch().getId());
            accDoor.setWgOutputFmtId(defalutWiegandFmt.get(0).getId());
            accDoor.setDelayOpenTime((short)0);// 初始化开门延时时间（需要固件支持，否则该参数无效）
            accDoor.setExtDelayDrivertime((short)5);//// 针对残疾人，特定的开门时间
            accDoor.setIsDisableAudio(false);
            if (noLicensedCount > 0 && i > doorCount - noLicensedCount) {
                accDoor.setEnabled(false);
            } else {
                accDoor.setEnabled(true);
            }
            accDoor.setCombOpenInterval((short)10);
            // 门有效时间段，默认24小时通行
            if (StringUtils.isNoneBlank(initTimeSegId)) {
                accDoor.setActiveTimeSegId(initTimeSegId);
            }
            accDoor.setWgReversed((short)0);// 设置默认wg反转模式，0为不反转
            accDoor.setAllowSUAccessLock("0");
            // accdoorList.add(accDoor);

            List<AccReader> accReaderSet = new ArrayList<AccReader>();
            String readerCountStr = devInfo.getString("ReaderCount");
            int readerCount = StringUtils.isBlank(readerCountStr) ? 0 : Integer.parseInt(readerCountStr);
            if (readerCount > 0) {
                // add reader 入
                AccReader accReaderIn = new AccReader();
                accReaderIn.setAccDoor(accDoor);
                readerNo = readerNo + 1;
                accReaderIn.setReaderNo((short)readerNo);
                accReaderIn.setName(accDoor.getName() + "-" + I18nUtil.i18nCode(AccConstants.READER_IN_NAME));
                accReaderIn.setReaderState(AccConstants.STATE_IN);
                if (isReaderDisable) {
                    if (ext485ReaderFunOn != 0) {
                        accReaderIn.setCommType(AccConstants.READER_TYPE_RS485_OR_WGFMT);
                    } else {
                        accReaderIn.setCommType(AccConstants.READER_TYPE_WGFMT);
                    }
                }
                if (isSupportReaderProtity) { // 支持读头属性
                    // 先默认设置为韦根
                    accReaderIn.setCommType(AccConstants.READER_TYPE_WGFMT);
                    if (isSupportWgOrRS485) {
                        accReaderIn.setCommType(AccConstants.READER_TYPE_RS485_OR_WGFMT);
                    } else if (StringUtils.isNotBlank(supportReaderType)) {
                        accReaderIn.setCommType(getDefaultReaderCommType(supportReaderType));
                    }
                    accReaderIn.setCommAddress(commAddress);
                    /*if (doorCount < 4) { // 一门，两门控制器默认都是韦根，通讯地址要增加
                        commAddress++;
                    }*/
                    commAddress++;
                    accReaderIn.setIp(StringUtils.EMPTY);
                    accReaderIn.setMac(StringUtils.EMPTY);
                    accReaderIn.setMulticast(StringUtils.EMPTY);
                    accReaderIn.setPort(AccConstants.READER_DEFAULT_PORT);
                    if (isSupportDM20) {
                        // dm20门禁单元固定入读头地址1
                        accReaderIn.setCommAddress((short)1);
                    }
                }
                if (isEncrypt) {
                    accReaderIn.setReaderEncrypt(uidcard);// 如果系统存在控卡，则读头默认设置为加密（moduleBean！=null表示存在控卡）
                }
                if (isSupportOSDP) {
                    // 支持osdp设置的话默认设置为zk485协议类型
                    accReaderIn.setRs485ProtocolType((short)1);
                }
                accReaderSet.add(accReaderIn);
                accReaderList.add(accReaderIn);

                // add reader 出
                // C3-400和DCP-400外的设备有出读头，，如果C3-400、DCP-400支持485读头，给配置出读头
                if (machineType != AccConstants.DEVICE_C3_400 || ext485ReaderFunOn != 0) {
                    AccReader accReaderOut = new AccReader();
                    accReaderOut.setAccDoor(accDoor);
                    readerNo = readerNo + 1;
                    accReaderOut.setReaderNo((short)readerNo);
                    accReaderOut.setName(accDoor.getName() + "-" + I18nUtil.i18nCode(AccConstants.READER_OUT_NAME));// "门"需要国际化！！！
                    accReaderOut.setReaderState(AccConstants.STATE_OUT);
                    if (isReaderDisable) {
                        if (ext485ReaderFunOn != 0) {
                            accReaderOut.setCommType(AccConstants.READER_TYPE_RS485_OR_WGFMT);
                        } else {
                            accReaderOut.setCommType(AccConstants.READER_TYPE_WGFMT);
                        }
                    }
                    if (isSupportReaderProtity) { // 支持读头属性
                        accReaderOut.setCommType(AccConstants.READER_TYPE_RS485);
                        if (isSupportWgOrRS485) {
                            if (doorCount != 4) {
                                accReaderOut.setCommType(AccConstants.READER_TYPE_RS485_OR_WGFMT);
                            } else {
                                accReaderOut.setCommType(AccConstants.READER_TYPE_RS485);
                            }
                        } else if (StringUtils.isNotBlank(supportReaderType)) {
                            accReaderOut.setCommType(getDefaultReaderCommType(supportReaderType));
                        }
                        /*if (doorCount < 4) {
                            accReaderOut.setCommAddress(commAddress);
                        } else {
                            accReaderOut.setCommAddress((short)(4 + commAddress));
                        }*/
                        accReaderOut.setCommAddress(commAddress);
                        commAddress++;
                        accReaderOut.setIp(StringUtils.EMPTY);
                        accReaderOut.setMac(StringUtils.EMPTY);
                        accReaderOut.setMulticast(StringUtils.EMPTY);
                        accReaderOut.setPort(AccConstants.READER_DEFAULT_PORT);
                        if (isSupportDM20) {
                            // dm20门禁单元固定出读头地址2
                            accReaderOut.setCommAddress((short)2);
                        }
                    }
                    if (isEncrypt) {
                        accReaderOut.setReaderEncrypt(uidcard);
                    }
                    if (isSupportOSDP) {
                        // 支持osdp设置的话默认设置为zk485协议类型
                        accReaderOut.setRs485ProtocolType((short)1);
                    }
                    accReaderSet.add(accReaderOut);
                    accReaderList.add(accReaderOut);
                }
            }
            if (!accReaderSet.isEmpty()) {
                accDoor.setAccReaderList(accReaderSet);
            }
            accDoorList.add(accDoor);
        }
        if (!accDoorList.isEmpty()) {
            accDoorDao.save(accDoorList);
        }
        if (!accReaderList.isEmpty()) {
            accReaderDao.save(accReaderList);
        }
        return accDoorList;
    }

    /**
     * 计算默认的读头通信方式
     *
     * @param supportReaderType
     * @return
     */
    private short getDefaultReaderCommType(String supportReaderType) {
        short commType = AccConstants.READER_TYPE_NOMINAL;
        short supportReaderTypeNum = Short.parseShort(supportReaderType);
        if ((supportReaderTypeNum & 2) == 2) {
            commType = AccConstants.READER_TYPE_WGFMT;
        } else if ((supportReaderTypeNum & 1) == 1) {
            commType = AccConstants.READER_TYPE_RS485;
        } else if ((supportReaderTypeNum & 4) == 4) {
            commType = AccConstants.READER_TYPE_NETWORK;
        } else if ((supportReaderTypeNum & 8) == 8) {
            commType = AccConstants.READER_TYPE_ZIGBEE;
        }
        return commType;
    }

    private List<AccDeviceOption> addDevOpt(JSONObject devInfo, AccDevice accDevice) {
        List<AccDeviceOption> optionList = Lists.newArrayList();
        AccDeviceOption accDevOpt = null;
        JSONObject optionJson = new JSONObject();
        for (String key : devInfo.keySet()) {
            accDevOpt = new AccDeviceOption();
            // 过滤掉一些重复的参数 PushVersion SN IPAddress
            String optValue = devInfo.getString(key);
            if (StringUtils.isBlank(optValue)) {
                continue;
            }
            accDevOpt.setAccDevice(accDevice);
            accDevOpt.setName(key);
            accDevOpt.setValue(optValue);
            optionJson.put(key, optValue);
            if (AccConstants.DEV_COMM_KEY_PARAM_LIST.contains(key)) {
                accDevOpt.setType(ConstUtil.DEV_COMM_PARAM);// 表示该参数为通信关键参数.
            } else if (AccConstants.DEV_FUN_KEY_PARAM_LIST.contains(key)) {
                accDevOpt.setType(ConstUtil.DEV_FUN_PARAM);// 表示该参数为设备扩展功能关键参数.
            }
            optionList.add(accDevOpt);
        }
        if (!optionList.isEmpty()) {
            accDeviceOptionDao.save(optionList);
            accCacheManager.putDeviceOptionInfo(accDevice.getSn(), optionJson.toJSONString());
        }
        // push设备注册时不会传密码，但是如果单独获取密码为空的时候，命令会返回负值，所以加上sn号
        if (ConstUtil.COMM_HTTP == accDevice.getCommType()) {
            List<String> optionsList = Lists.newArrayList();
            //optionsList.add("ComPwd");
            optionsList.add("PhotoFunOn");// 用户照片功能参数
            optionsList.add("AutoServerFunOn");// 后台验证是否支持参数
            // 获取防疫参数
            optionsList.add("MaskDetectionFunOn");
            optionsList.add("IRTempDetectionFunOn");
            optionsList.add("EnalbeIRTempDetection");
            optionsList.add("EnableNormalIRTempPass");
            optionsList.add("EnalbeMaskDetection");
            optionsList.add("EnableWearMaskPass");
            optionsList.add("IRTempThreshold");
            optionsList.add("IRTempUnit");
            optionsList.add("EnableUnregisterPass");
            optionsList.add("EnableTriggerAlarm");
            optionsList.add("IRTempCorrection");
            // 生物识别相关
            optionsList.add("MThreshold");
            optionsList.add("~FaceMThr");
            optionsList.add("PvMThreshold");
            accDevCmdManager.getDeviceOption(accDevice, optionsList, false);
        }
        return optionList;
    }

    @Override
    public AccDeviceItem getItemByDevSn(String devSn) {
        AccDeviceItem accDeviceItem = new AccDeviceItem();
        accDeviceItem.setSn(devSn);
        List<AccDeviceItem> items = getByCondition(accDeviceItem);
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Cacheable(value = {"acc:devEventMap"}, key = "#sn", condition = "#sn != null")
    @Override
    public Map<String, String> getDevEvent(String sn) {
        Map<String, String> eventMap = Maps.newHashMap();
        if (StringUtils.isNotBlank(sn)) {
            List<AccDeviceEvent> eventList = accDeviceEventDao.findByAccDevice_Sn(sn);
            for (AccDeviceEvent event : eventList) {
                eventMap.put(event.getEventNo().toString(), event.getName());
            }
        }
        return eventMap;
    }

    @Cacheable(value = {"acc:verifyModeMap"}, key = "#devSn", condition = "#devSn != null")
    @Override
    public Map<String, String> getVerifyMode(String devSn) {
        Map<String, String> verifyModeMap = null;
        if (StringUtils.isNotBlank(devSn)) {
            verifyModeMap = Maps.newHashMap();
            List<AccDeviceVerifyMode> verifyModeList = accDeviceVerifyModeDao.findByAccDevice_Sn(devSn);
            for (AccDeviceVerifyMode verifyMode : verifyModeList) {
                verifyModeMap.put(verifyMode.getVerifyNo().toString(), verifyMode.getName());
            }
        }
        return verifyModeMap;
    }

    @Override
    public String getStatus(String sn) {
        String devJson = accCacheManager.getAdmsDeviceInfo(sn);
        if (StringUtils.isNotBlank(devJson)) {
            JSONObject json = JSON.parseObject(devJson);
            boolean enabled = json.getBooleanValue("enabled");
            if (enabled) {
                boolean isOnline = isOnline(sn);
                if (isOnline) {
                    return "1";
                } else {
                    return "0";
                }
            } else {
                return "2";
            }
        } else {
            return "2";
        }
    }

    /**
     * 判断设备是否在线或跟服务器是否链接相通
     *
     * @param sn:
     * @return boolean
     * @throws <AUTHOR>
     * @date 2023-03-29 15:13
     * @since 1.0.0
     */
    public boolean isOnline(String sn) {
        return accCacheManager.getOnlineStatus(sn).equals("1");
    }

    @Override
    public void setDevEnable(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            setDevStatus(ids, true);
        }
    }

    private void setDevStatus(String ids, boolean isEnable) {
        List<String> idList = (List<String>)CollectionUtil.strToList(ids);
        List<AccDevice> accDeviceList = accDeviceDao.findByIdIn(idList);
        accDeviceList = sortTopParentDevList(accDeviceList);
        boolean ret = false;
        for (AccDevice accDevice : accDeviceList) {
            if (accDevice.getEnabled() != isEnable) {
                List<AccDevice> childDevices = accDevice.getChildDevices();
                if (isEnable) { // 启用
                    // 启用子设备
                    AccDevice parentDev = accDevice.getParentDevice();
                    if (parentDev != null) {
                        if (!parentDev.getEnabled()) {
                            throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_WARN,
                                "acc_dev_parentEnable");
                        }
                        accDevCmdManager.getDevRTState(accDevice, false);
                    }
                    if (!childDevices.isEmpty()) {
                        accDevCmdManager.getDevRTState(accDevice, false);
                    }
                    ret = admsDeviceService.enableDevice(accDevice.getSn());
                } else {
                    // 禁用父设备，需要禁用所有子设备
                    if (!childDevices.isEmpty()) {
                        for (AccDevice childDev : childDevices) {
                            if (childDev.getEnabled()) {
                                admsDeviceService.disableDevice(childDev.getSn());
                                childDev.setEnabled(isEnable);
                                accDeviceDao.save(childDev);
                                putQueryItemToCache(accDevice);// 更新缓存设备信息
                            }
                        }
                    }
                    ret = admsDeviceService.disableDevice(accDevice.getSn());
                }
                // 获取返回值
                if (!ret) {
                    throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_ERROR, "common_op_failed");
                }
                accDevice.setEnabled(isEnable);
                accDeviceDao.save(accDevice);
                putQueryItemToCache(accDevice);// 更新缓存设备信息
            }
        }
    }

    @Override
    public void setDevDisable(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            setDevStatus(ids, false);
        }
    }

    @Override
    public List<AccDeviceItem> getItemByIds(String ids) {
        AccDeviceItem accDeviceItem = new AccDeviceItem();
        accDeviceItem.setIds(ids);
        return getByCondition(accDeviceItem);
    }

    @Override
    public boolean isEnabled(String sn) {
        AccDevice accDevice = accDeviceDao.findBySn(sn);
        return accDevice.getEnabled();
    }

    @Override
    public boolean isNewAccessControlDevice(short machineType, String devName) {
        boolean flag = false;
        if (AccConstants.DEVICE_ACCESS_CONTROL == machineType && !AccConstants.DEVICE_TF1700.equals(devName)) {
            flag = true;
        }
        return flag;
    }

    @Override
    public String getFingerFvFaceFunOn(String sn) {
        StringBuffer authority = new StringBuffer("");
        authority.append(accDeviceOptionService.isSupportFun(sn, AccConstants.FINGER_FUN_ON) ? "1" : "0");
        authority.append(accDeviceOptionService.isSupportFun(sn, AccConstants.FV_FUN_ON) ? "1" : "0");
        if (accDeviceOptionService.isSupportFun(sn, AccConstants.BIOPHOTO_FUN_ON)) {
            authority.append("0");
        } else {
            authority.append(accDeviceOptionService.isSupportFun(sn, AccConstants.FACE_FUN_ON) ? "1" : "0");
        }
        authority.append("0");// 掌静脉预留
        return authority.toString();
    }

    @Override
    public String checkCombOpenDoor(String devIds) {
        List<AccDevice> deviceList = accDeviceDao.findByIdIn((List<String>)CollectionUtil.strToList(devIds));
        String devAlias = "";
        for (AccDevice accDevice : deviceList) {
            // IR9000没有门，不需要检测组合开门规则
            if (accDevice.getMachineType() == AccConstants.DEVICE_BIOIR_9000) {
                continue;
            }
            List<AccDoor> accDoorList = accDevice.getAccDoorList();
            List<String> doorIdList = (List<String>)CollectionUtil.getModelIdsList(accDoorList);
            List<AccCombOpenDoor> accCombOpenDoorList = accCombOpenDoorDao.findByAccDoor_IdIn(doorIdList);// 根据doorId获取多人开门规则信息
            if (accCombOpenDoorList.size() > 0) {
                devAlias = accDevice.getAlias() + ",";
                continue;
            }
        }
        return devAlias;
    }

    @Override
    public List<AccDeviceItem> getDevAsWGReaderByDevId(String id) {
        List<AccDevice> accDeviceList = accDeviceDao.getDevAsWGReaderByDevId(id);
        List<AccDeviceItem> accDeviceItemList = ModelUtil.copyListProperties(accDeviceList, AccDeviceItem.class);
        return accDeviceItemList;
    }

    @Override
    public boolean isOneWay(String sn) {
        int doorCount = Integer.parseInt(accDeviceOptionService.getOptVal(sn, "LockCount"));
        int readerCount = Integer.parseInt(accDeviceOptionService.getOptVal(sn, "ReaderCount"));
        // 双向控制器 ，门个数=读头个数 即为单向控制器（一体机为单向控制器：仅韦根读头）
        boolean oneWay = readerCount == doorCount;
        return oneWay;
    }

    @Override
    public long issueVerifyParamToDev(String id, String verifyParam, String offlineRule, boolean isImme) {
        AccDevice accDevice = accDeviceDao.findById(id).orElse(null);
        long ret = -1;
        if (accDevice != null) {
            ret = accDevCmdManager.issueVerifyParamToDev(accDevice.getSn(), verifyParam, offlineRule, isImme);
        }
        return ret;
    }

    @Override
    public void syncDataToDev(String sn, String[] optBox) {
        AccDevice accDevice = accDeviceDao.findBySn(sn);
        boolean isClearData = false;
        // 同步时是否删除设备中数据
        if (Arrays.asList(optBox).contains("clearData")) {
            isClearData = true;
        }
        for (String opt : optBox) {
            if (opt.equals(AccConstants.AccSyncDataType.ACCLEVEL.getValue())) {
                if (isClearData) {
                    accDevCmdManager.delAllPersonBioTemplateFromDev(accDevice.getSn(), false);
                    accDevCmdManager.delAllPersonLevelFromDev(accDevice.getSn(), false);
                    accDevCmdManager.delAllPersonFromDev(accDevice.getSn(), false);
                }

                List<String> personIdList = accLevelDao.getPersonIdByDevIdOrParentDevId(accDevice.getId());// 如果没有设置权限，获取不到人员personIdList为空
                // 是否要在前台提示为人员分配权限
                List<String> levelIdList = accLevelDao.getLevelIdsByDeviceId(accDevice.getId()); // 根据设备id获取所属权限组
                if (levelIdList != null && levelIdList.size() > 0) {
                    if (personIdList != null && personIdList.size() > 0) {
                        List<List<String>> personIds =
                            CollectionUtil.split(personIdList, AccConstants.LEVEL_SPLIT_COUNT);
                        personIds.forEach(personArrayId -> {
                            AccPersonInfoItem accPersonInfoItem = accLevelService.getPersonByIds(personArrayId);
                            accLevelService.setPersonLevelToDevice(accDevice.getId(), accPersonInfoItem, true);
                        });
                    }
                    // 同步访客权限到设备
                    accLevelService.syncVisitorToDevByLevelIdsAndDevId(levelIdList, accDevice.getId());
                    // 拓展接口，为了兼容华为pad设备下发downLoad命令通知设备已下发完成的功能 by mingfa.zheng 20200727
                    accDevCmdManager.downloadOver(accDevice.getSn(), false);
                }
            } else if (opt.equals(AccConstants.AccSyncDataType.TIMEZONEANDHOLIDAY.getValue())) {// timeZoneAndHoliday
                // 时间段、节假日
                if (isClearData) {
                    accDevCmdManager.delAllTimeSegFromDev(accDevice.getSn(), false);
                }
                accDevCmdManager.setTimeSegToDev(accDevice.getSn(),
                    accTimeSegService.getByCondition(new AccTimeSegItem()), false);
                if (isClearData) {
                    accDevCmdManager.delAllHolidayFromDev(accDevice.getSn(), false);
                }
                accDevCmdManager.setHolidayToDev(accDevice.getSn(),
                    accHolidayService.getByCondition(new AccHolidayItem()), false);
            } else if (opt.equals(AccConstants.AccSyncDataType.DOOROPT.getValue())) {
                if (isClearData) {
                    accDevCmdManager.delAllDoorParametersFromDev(accDevice.getSn(), false);
                    accDevCmdManager.delAllReaderWGFormatFromDev(accDevice.getSn(), false);
                }
                List<AccDoorItem> doorList = accDoorService.getItemsByDevSn(accDevice.getSn());
                doorList.forEach(accDoor -> accDevCmdManager.setDoorOptionToDev(accDoor, false));
            } else if (opt.equals(AccConstants.AccSyncDataType.WIEGANDFMT.getValue())) {
                accDevCmdManager.setWGFmtWithSitecodeOn(accDevice.getSn(), true, false);
                if (isClearData) {
                    accDevCmdManager.delAllDefWGFormat(accDevice.getSn(), false);
                }
                accDevCmdManager.setDefWGFormatToDev(accDevice.getSn(), persWiegandFmtService.getAutoMatch(), false);
                if (isClearData) {
                    accDevCmdManager.delAllWGFormat(accDevice.getSn(), false);
                }
                accDevCmdManager.setCusWGFormatToDev(accDevice.getSn(),
                    persWiegandFmtService.getByCondition(new PersWiegandFmtItem()), false);
            } else if (opt.equals(AccConstants.AccSyncDataType.LINKAGE.getValue())) {
                if (isClearData) {
                    // 删除联动设置
                    accDevCmdManager.delAllLinkageFromDev(accDevice.getSn(), false);// 删除联动设置
                }
                for (AccLinkage accLink : accDevice.getAccLinkageSet()) {
                    accDevCmdManager.setLinkageToDev(accDevice, accLink, false);
                }
            } else if (opt.equals(AccConstants.AccSyncDataType.ANTIPASSBACK.getValue())) {// antiPassback 反潜
                if (isClearData) {
                    // 删除反潜
                    accDevCmdManager.delAllAntipassbackFromDev(accDevice.getSn(), false);
                }
                List<AccAntiPassbackItem> accAntiPassbackItemList =
                    accAntiPassbackService.getItemsByDevId(accDevice.getId());
                for (AccAntiPassbackItem accAntiPassback : accAntiPassbackItemList) {
                    accDevCmdManager.setAntiPassbackToDev(accDevice.getSn(), accAntiPassback, false);// 下发反潜
                }
            } else if (opt.equals(AccConstants.AccSyncDataType.INTERLOCK.getValue())) {// interlock 互锁
                if (isClearData) {
                    // 删除互锁
                    accDevCmdManager.delAllInterlockFromDev(accDevice.getSn(), false);
                }
                for (AccInterlock accInterlock : accDevice.getAccInterlockList()) {
                    accDevCmdManager.setInterlockToDev(accDevice.getSn(), accInterlock, false);// 下发互锁规则
                }
            } else if (opt.equals(AccConstants.AccSyncDataType.FIRSTPERSON.getValue())) {
                if (isClearData) {
                    accDevCmdManager.delAllFirstOpenFromDev(accDevice.getSn(), false);
                }
                accDevice.getAccDoorList().stream().forEach(door -> {
                    door.getAccFirstOpenList().forEach(accFirstOpen -> {
                        if (accFirstOpen.getAccPersonFirstOpenSet() != null
                            && accFirstOpen.getAccPersonFirstOpenSet().size() > 0) {
                            List<String> personIds =
                                (List<String>)CollectionUtil.getPropertyList(accFirstOpen.getAccPersonFirstOpenSet(),
                                    AccPersonFirstOpen::getPersPersonId, "-1");
                            List<String> pinList =
                                new ArrayList<>(persPersonService.getPinsByPersonIds(personIds).values());
                            if (pinList.size() > 0) {
                                accDevCmdManager.setFirstOpenToDev(accFirstOpen, pinList, false);
                            }
                        }
                    });
                });
            } else if (opt.equals(AccConstants.AccSyncDataType.MULTIPERSON.getValue())) {// multiPerson 多人开门
                if (isClearData) {
                    accDevCmdManager.delAllCombOpenDoorFromDev(accDevice.getSn(), false);
                }
                accDevice.getAccDoorList().stream().forEach(door -> {
                    door.getAccCombOpenDoorList()
                        .forEach(comOpen -> accDevCmdManager.setCombOpenDoorToDev(comOpen, false));
                });
            } else if (opt.equals(AccConstants.AccSyncDataType.OUTRELAYSET.getValue())) {
                if (isClearData) {
                    accDevCmdManager.delAllAuxOutOptFromDev(accDevice.getSn(), false);
                }
                accDevCmdManager.setAuxOutOptToDev(accDevice, accDevice.getAccAuxOutList(), false);
            } else if (opt.equals(AccConstants.AccSyncDataType.BACKGROUNDVERIFYPARAM.getValue())) {
                if (accDevice.getCommType().equals(ConstUtil.COMM_HTTP)) {
                    String enabled = accDeviceOptionService.getValueByNameAndDevSn(accDevice.getSn(), "AutoServerMode");
                    String offlineRule =
                        accDeviceOptionService.getValueByNameAndDevSn(accDevice.getSn(), "ReaderOfflineRule");
                    accDevCmdManager.issueVerifyParamToDev(accDevice.getSn(), enabled, offlineRule, true);// 同步设备后台验证
                }
            } else if (opt.equals(AccConstants.AccSyncDataType.AUXINSET.getValue())) {
                accDevCmdManager.setAuxInOptToDev(accDevice, accDevice.getAccAuxInList(), false);
            } else if (opt.equals(AccConstants.AccSyncDataType.VERIFYMODERULE.getValue())) {
                if (isClearData) {
                    accDevCmdManager.delAllVerifyModeRuleFromDev(accDevice.getSn(), false);
                }
                accVerifyModeRuleService.syncVerifyModeRuleToDev(accDevice.getId());
            }
        }
    }

    @Override
    public List<String> getByMachineType(Short machineType) {
        List<String> devIdList = null;
        // 设备类型大于等于101为一体机设备，小于101为控制器，目前按照这个分为两大类
        if (machineType >= AccConstants.DEVICE_ACCESS_CONTROL) {
            devIdList = accDeviceDao.getAccessControlIds();
        } else {
            devIdList = accDeviceDao.getControlIds();
        }
        return devIdList;
    }

    @Override
    public Map<String, Integer> getAfterLicensedCount(int deviceCount, int doorCount, short commType) {
        Map<String, Integer> countParams = new HashMap<String, Integer>();
        // 系统中的pull的门数量
        List<Short> commTypeList = new ArrayList<>();
        commTypeList.add(AccConstants.COMM_TYPE_PULL_TCPIP);
        commTypeList.add(AccConstants.COMM_TYPE_PULL_RS485);
        long pullDoorCount = accDeviceDao.getDoorCountByCommType(commTypeList);
        countParams.put("pullGateCount",
            (int)(commType != AccConstants.COMM_TYPE_PUSH_HTTP ? (pullDoorCount + doorCount) : pullDoorCount));
        // 系统中的pull的设备数量
        long pullDevCount = accDeviceDao.getDevCountByCommType(commTypeList);
        countParams.put("pullDevCount",
            (int)(commType != AccConstants.COMM_TYPE_PUSH_HTTP ? (pullDevCount + deviceCount) : pullDevCount));
        // 系统中的push的门数量
        commTypeList.clear();
        commTypeList.add(AccConstants.COMM_TYPE_PUSH_HTTP);
        // Mqtt设备算在Push点数里
        commTypeList.add(AccConstants.COMM_TYPE_BEST_MQTT);
        long pushDoorCount = accDeviceDao.getDoorCountByCommType(commTypeList);
        countParams.put("pushGateCount",
            (int)(commType == AccConstants.COMM_TYPE_PUSH_HTTP ? (pushDoorCount + doorCount) : pushDoorCount));
        // 系统中的push的设备数量
        long pushDevCount = accDeviceDao.getDevCountByCommType(commTypeList);
        countParams.put("pushDevCount",
            (int)(commType == AccConstants.COMM_TYPE_PUSH_HTTP ? (pushDevCount + deviceCount) : pushDevCount));
        return countParams;
    }

    @Override
    public Map<String, Integer> getBeforeLicensedCount() {
        Map<String, Integer> countParams = new HashMap<String, Integer>();
        // 系统中的pull的门数量
        List<Short> commTypeList = new ArrayList<>();
        commTypeList.add(AccConstants.COMM_TYPE_PULL_TCPIP);
        commTypeList.add(AccConstants.COMM_TYPE_PULL_RS485);
        long pullDoorCount = accDeviceDao.getDoorCountByCommType(commTypeList);
        countParams.put("pullGateCount", (int)pullDoorCount);
        // 系统中的pull的设备数量
        long pullDevCount = accDeviceDao.getDevCountByCommType(commTypeList);
        countParams.put("pullDevCount", (int)pullDevCount);
        // 系统中的push的门数量
        commTypeList.clear();
        commTypeList.add(AccConstants.COMM_TYPE_PUSH_HTTP);
        // Mqtt设备算在Push点数里
        commTypeList.add(AccConstants.COMM_TYPE_BEST_MQTT);
        long pushDoorCount = accDeviceDao.getDoorCountByCommType(commTypeList);
        countParams.put("pushGateCount", (int)pushDoorCount);
        // 系统中的push的设备数量
        long pushDevCount = accDeviceDao.getDevCountByCommType(commTypeList);
        countParams.put("pushDevCount", (int)pushDevCount);
        long c3DevCount = accDeviceDao.getC3DevCount();
        long k2DevCount = accDeviceDao.getK2DevCount();
        if (c3DevCount - k2DevCount > 0) {
            countParams.put("hasC3", 1);
        } else {
            countParams.put("hasC3", 0);
        }
        return countParams;
    }

    @Override
    public Long createBId() {
        AccDeviceBId accDeviceBId = new AccDeviceBId();
        accDeviceBId = accDeviceBIdDao.save(accDeviceBId);
        return accDeviceBId.getId();
    }

    @Override
    public Map<String, String> uploadTransaction(AccDeviceUploadTransactionItem item) {
        AccDevice accDevice = accDeviceDao.findById(item.getDevId()).orElse(null);
        Map<String, String> devDataMap = Maps.newHashMap();
        List<Long> cmdIdList = Lists.newArrayList();
        if (accDevice != null) {
            boolean newLog = AccConstants.NEW_RECORDS.equals(item.getRecords());
            if (!AccConstants.CHECK_NEW_RECORDS.equals(item.getRecords())) {
                cmdIdList.add(accDevCmdManager.getTransaction(accDevice.getSn(), newLog, true));
                // if ("true".equals(item.getSetValidDate()) && (item.getStartTime() != null || item.getEndTime() !=
                // null)) {
                accCacheManager.setTransactionValidTime(accDevice.getSn(), item);
                // }
            } else {
                cmdIdList.addAll(checkUpdateAndGetTrans(accDevice, true));
            }
            devDataMap.put("cmdId", StringUtils.join(cmdIdList, ","));
            devDataMap.put("alias", accDevice.getAlias());
        }
        return devDataMap;
    }

    @Override
    public Long getIdentityCardInfo(String devId) {
        AccDevice accDevice = accDeviceDao.findById(devId).orElse(null);
        if (Objects.nonNull(accDevice)) {
            // 支持身份登记设备，获取身份证表数据
            return accDevCmdManager.getIdentityCardInfo(accDevice, true);
        }
        return -1L;
    }

    private List<Long> checkUpdateAndGetTrans(AccDevice device, boolean isImme) {
        List<Long> cmdList = new ArrayList<>();
        Integer maxIndex = accTransactionDao.getMaxLogId(device.getSn());
        Integer minIndex = accTransactionDao.getMinLogId(device.getSn());
        Integer indexCount = accTransactionDao.getLogIdCount(device.getSn(), 0);
        maxIndex = maxIndex == null ? 0 : maxIndex;
        minIndex = minIndex == null ? 0 : minIndex;
        indexCount = maxIndex == null ? 0 : indexCount;
        int maxIndexTemp = 0;
        // 2.向设备获取软件中缺少的事件记录,
        if (accDeviceOptionService.isSupportFunList(device.getSn(), 11)
            || isNewAccessControlDevice(device.getMachineType(), device.getDeviceName())) {
            // 新完善的push控制器 或 非TF1700的一体机（新架构一体机是支持的）
            maxIndexTemp = calculateMaxIndex(minIndex, maxIndex, indexCount, device.getSn());
        }
        long cmdId = accDevCmdManager.getTransactionByMaxIndex(device.getSn(), maxIndexTemp, isImme);
        if (cmdId > 0) {
            cmdList.add(cmdId);
        }
        return cmdList;
    }

    /**
     * 统计设备中的确实的maxIndex值，不一定准确
     *
     * @param minIndex
     * @param maxIndex
     * @param indexCount
     * @param devSn
     * @return
     * <AUTHOR>
     * @since 2016年11月10日 下午3:00:38
     */
    private int calculateMaxIndex(int minIndex, int maxIndex, int indexCount, String devSn) {
        int maxIndexTemp = minIndex;
        // 一旦最大索引和最小索引一样，并且不是就一条情况下，就下发maxIndex=0给设备 add by max 20161109
        if (maxIndex == minIndex && indexCount != 1) {
            maxIndexTemp = 0;
        } else {
            if ((indexCount <= maxIndex) && indexCount == (maxIndex - minIndex + 1)) {
                // 不存在缺漏，至少在设备接入软件以后，软件接收的第一条事件开始就不存在缺漏 add by max 20161109
                maxIndexTemp = maxIndex;
            } else {
                // 防止下发的maxIndex过小，后续考虑统计计算方式优化，目前先防止下发过小，导致设备上传重复数据过多,采取最多重复获取前5w条 add by max 20161110
                if (maxIndex - minIndex > 50000) {
                    maxIndexTemp = maxIndex - 50000;
                } else {
                    maxIndexTemp = minIndex;
                }
            }
        }
        return maxIndexTemp;
    }

    @Override
    public Long syncTime(String devSn) {
        // add by colin 2020-4-7 10:32:56 下发时间
        if (accDeviceOptionService.isSupportFun(devSn, "MachineTZFunOn")) {
            AccDevice device = accDeviceDao.findBySn(devSn);
            return setTimeZoneAndTIme(device, device.getTimeZone());
        } else {
            // 下发同步时间命令
            return accDevCmdManager.syncTime(devSn, new Date(), true);
        }
    }

    @Override
    public boolean isExistsIp(String id, String ipAddress) {
        boolean isExist = true;
        AccDevice accDevice = accDeviceDao.findById(id).orElse(null);
        if (accDevice != null) {
            if (accDevice.getParentDevice() != null) {
                AccDeviceOptionItem devOpt =
                    accDeviceOptionService.getDevOptValueBySnAndName(accDevice.getParentDevice().getSn(), "IPAddress1");
                if (devOpt != null && ipAddress.equals(devOpt.getValue())) {
                    isExist = false;
                }
            } else {
                AccDevice tempDev = accDeviceDao.getAccDeviceByIpAddressAndDevId(id, ipAddress);
                isExist = tempDev == null;
            }
        } else {
            throw new ZKBusinessException("acc_dev_IllegalDevice");
        }
        return isExist;
    }

    @Override
    public boolean isExistsSecondIp(String id, String newIp) {
        boolean isExist = true;
        AccDevice accDevice = accDeviceDao.findById(id).orElse(null);
        if (accDevice != null) {
            AccDeviceOptionItem accDeviceOption =
                accDeviceOptionService.getDevOptValueBySnAndName(accDevice.getSn(), "IPAddress1");
            if (accDeviceOption != null && StringUtils.isNotBlank(accDeviceOption.getValue())
                && accDeviceOption.getValue().equals(newIp)) {
                isExist = true;
            } else {
                if (StringUtils.isNotBlank(newIp)) {
                    if (accDevice.getMachineType().shortValue() == AccConstants.DEVICE_BIOIR_9000) {
                        AccDevice tempDev = accDeviceDao.getAccDeviceByIpAddressAndDevId(id, newIp);
                        isExist = tempDev == null;
                    } else {
                        if (accDevice.getParentDevice() != null) {
                            long count =
                                accDeviceOptionDao.isExistsSecondIp(accDevice.getParentDevice().getId(), newIp);
                            if (count > 0) {
                                isExist = false;
                            }
                        }
                    }
                }
            }
        } else {
            throw new ZKBusinessException("acc_dev_IllegalDevice");
        }
        return isExist;
    }

    @Override
    public Long updateIpAddr(String devId, String ipAddress, String subnetMask, String gateway) {
        AccDevice device = accDeviceDao.findById(devId).orElse(null);
        long cmdId = -1;
        if (device != null) {
            // 发送修改ip的命令到设备
            cmdId = accDevCmdManager.setDeviceIP(device, ipAddress, subnetMask, gateway, true);
        }
        return cmdId;
    }

    @Override
    public void updateAdmsDevIpAddr(String deviceSn, String ipAddress, String subnetMask, String gateway) {
        admsDeviceService.updateIpAddr(deviceSn, ipAddress, subnetMask, gateway);
    }

    @Override
    public Long updateIpAddrEx(String devId, String ipAddressSec, String netMaskSec, String gateIPAddressSec,
        String serverNetworkCard) {
        AccDevice accDevice = accDeviceDao.findById(devId).orElse(null);
        Long cmdId = null;
        if (accDevice != null) {
            // 主控需要多设置 serverCommIP
            String serverCommIP = "";
            if (accDeviceOptionService.isSupportFun(accDevice.getSn(), "MasterControlOn")) {
                if ("firstCard".equals(serverNetworkCard)) {
                    serverCommIP = accDevice.getIpAddress();
                } else {
                    serverCommIP = ipAddressSec;
                }
            }
            cmdId = accDevCmdManager.setNetworkCardIpAddr(accDevice, ipAddressSec, gateIPAddressSec, netMaskSec,
                serverCommIP, true);
        }
        return cmdId;
    }

    @Override
    public void setRegistrationDevice(String devId, boolean isRegist) {
        AccDevice accDevice = accDeviceDao.findById(devId).orElse(null);
        if (accDevice != null && accDevice.getIsRegistrationDevice() != isRegist) {
            accDevice.setIsRegistrationDevice(isRegist);
            putQueryItemToCache(accDevice);
            accDeviceDao.save(accDevice);
        }
    }

    /**
     * 查询规则id集合
     *
     * @param accDevice
     * @return
     */
    private Map<String, List<String>> queryIdMapByDev(AccDevice accDevice) {
        if (Objects.nonNull(accDevice)) {
            Map<String, List<String>> queryIdMap = Maps.newHashMap();
            List<String> doorIdList = Lists.newArrayList();// 门
            List<String> auxInIdList = Lists.newArrayList();// 辅助输入
            List<String> readerIdList = Lists.newArrayList();// 读头id
            List<AccDoor> accDoorList = accDevice.getAccDoorList();
            accDoorList.stream().forEach(door -> {
                doorIdList.add(door.getId());
                door.getAccReaderList().stream().forEach(reader -> readerIdList.add(reader.getId()));
            });
            List<AccAuxIn> accAuxInList = accDevice.getAccAuxInList();// 根据设备id获取辅助输入信息
            for (AccAuxIn accAuxIn : accAuxInList) {
                auxInIdList.add(accAuxIn.getId());
            }
            accAuxInList.stream().forEach(accAuxIn -> auxInIdList.add(accAuxIn.getId()));
            queryIdMap.put("door", doorIdList);
            queryIdMap.put("auxIn", auxInIdList);
            queryIdMap.put("reader", readerIdList);
            return queryIdMap;
        }
        return null;
    }

    @Override
    public List<List<String>> queryDevUsage(String ids) {
        List<List<String>> queryList = new ArrayList<>();
        List<AccDevice> accDeviceList = accDeviceDao.findByIdIn((List<String>)CollectionUtil.strToList(ids));
        for (AccDevice accDevice : accDeviceList) {
            List<String> tempList = new ArrayList<>();
            // 1.根据设备的id获取人员ids
            List<String> personIdList = null;
            if (accDevice.getMachineType() != AccConstants.DEVICE_BIOIR_9000) {
                personIdList = accLevelDao.getPersonIdByDevId(accDevice.getId());
            } else {
                personIdList = accLevelDao.getPersonIdByDevIdAndParentDevId(accDevice.getId());// IR9000设备查询子设备人员id
            }
            int persCount = personIdList.size();// 人员数量
            Map<Short, Integer> personBioTemplateCount = null;
            // 2.根据人员ids获取生物模板数量
            if (persCount > 0) {
                personBioTemplateCount = Maps.newHashMap();
                List<List<String>> personIdSplitList =
                    CollectionUtil.split(personIdList, AccConstants.LEVEL_SPLIT_COUNT);
                for (List<String> personIds : personIdSplitList) {
                    Map<Short, Integer> tempPersonBioCount =
                        persBioTemplateService.countBioTemplateCountByPersonIdList(personIds);
                    for (Short bioType : tempPersonBioCount.keySet()) {
                        if (personBioTemplateCount.containsKey(bioType)) {
                            personBioTemplateCount.put(bioType,
                                personBioTemplateCount.get(bioType) + tempPersonBioCount.get(bioType));
                        } else {
                            personBioTemplateCount.put(bioType, tempPersonBioCount.get(bioType));
                        }
                    }
                }
            }
            AccDeviceOption multiBioDataSupport =
                accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), "MultiBioDataSupport");
            AccDeviceOption multiBioPhotoSupport =
                accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), "MultiBioPhotoSupport");
            AccDeviceOption maxMultiBioPhotoCount =
                accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), "MaxMultiBioPhotoCount");
            AccDeviceOption maxMultiBioDataCount =
                accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), "MaxMultiBioDataCount");

            int fingerCount = 0;// 指纹数量
            boolean supportFinger = false;
            if (multiBioDataSupport != null) {
                supportFinger =
                    "1".equals(multiBioDataSupport.getValue().split(":")[BaseConstants.BaseBioType.FP_BIO_TYPE]);
            } else {
                AccDeviceOption fingerFunOn =
                    accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), AccConstants.FINGER_FUN_ON);
                AccDeviceOption isOnlyRFMachine =
                    accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), AccConstants.ONLY_RF_MACHINE);
                if (fingerFunOn != null) {
                    supportFinger = "1".equals(fingerFunOn.getValue());
                } else if (isOnlyRFMachine != null) {
                    supportFinger = !"1".equals(isOnlyRFMachine.getValue());
                }
            }
            if (supportFinger && persCount > 0 && personBioTemplateCount != null
                && personBioTemplateCount.containsKey(BaseConstants.BaseBioType.FP_BIO_TYPE)) {
                fingerCount = personBioTemplateCount.get(BaseConstants.BaseBioType.FP_BIO_TYPE);
            }

            int fvCount = 0;
            boolean supportFv = false;
            if (multiBioDataSupport != null) {
                supportFv =
                    "1".equals(multiBioDataSupport.getValue().split(":")[BaseConstants.BaseBioType.VEIN_BIO_TYPE]);
            } else {
                supportFv = accDeviceOptionService.isSupportFun(accDevice.getSn(), AccConstants.FV_FUN_ON);
            }
            if (supportFv && persCount > 0 && personBioTemplateCount != null
                && personBioTemplateCount.containsKey(BaseConstants.BaseBioType.VEIN_BIO_TYPE)) {
                fvCount = personBioTemplateCount.get(BaseConstants.BaseBioType.VEIN_BIO_TYPE);
            }

            // 支持可见光比对照片
            int bioPhotoCount = 0;
            boolean supportBiophoto = false;
            if (multiBioPhotoSupport != null) {
                supportBiophoto =
                    "1".equals(multiBioPhotoSupport.getValue().split(":")[BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE]);
            } else {
                supportBiophoto = accDeviceOptionService.isSupportFun(accDevice.getSn(), AccConstants.BIOPHOTO_FUN_ON);
            }
            // TDB03 支持比对照片，获取比对照片数量，否则获取面部数量
            if (supportBiophoto && persCount > 0) {
                bioPhotoCount = accPersonService.getBiophotoCount(accDevice.getId());
            }

            int faceCount = 0;
            boolean supportFace = false;
            if (multiBioDataSupport != null) {
                supportFace =
                    "1".equals(multiBioDataSupport.getValue().split(":")[BaseConstants.BaseBioType.FACE_BIO_TYPE]);
            } else {
                // 支持面部，且不支持比对照片才获取，解决可见光设备FaceFunOn和BioPhotoFun两个功能参数都支持的情况下，界面上都显示/都去下发获取的问题
                supportFace = accDeviceOptionService.isSupportFun(accDevice.getSn(), AccConstants.FACE_FUN_ON)
                    && !supportBiophoto;
            }
            if (supportFace && persCount > 0 && personBioTemplateCount != null
                && personBioTemplateCount.containsKey(BaseConstants.BaseBioType.FACE_BIO_TYPE)) {
                faceCount = personBioTemplateCount.get(BaseConstants.BaseBioType.FACE_BIO_TYPE);
            }

            // 支持掌纹
            int palmCount = 0;
            boolean supportPalm = false;
            if (multiBioDataSupport != null) {
                supportPalm =
                    "1".equals(multiBioDataSupport.getValue().split(":")[BaseConstants.BaseBioType.PALM_BIO_TYPE]);
            } else {
                supportPalm = accDeviceOptionService.isSupportFun(accDevice.getSn(), AccConstants.PV_FUN_ON);
            }
            if (supportPalm && persCount > 0 && personBioTemplateCount != null
                && personBioTemplateCount.containsKey(BaseConstants.BaseBioType.PALM_BIO_TYPE)) {
                // 补充根据模板类型获取掌纹数量
                palmCount = personBioTemplateCount.get(BaseConstants.BaseBioType.PALM_BIO_TYPE);
            }

            // 可见光人脸模版数
            int vislightFaceTemp = 0;
            boolean supportvislightFaceTemp = false;
            if (multiBioDataSupport != null) {
                supportvislightFaceTemp =
                    "1".equals(multiBioDataSupport.getValue().split(":")[BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE]);
            }
            if (supportvislightFaceTemp && persCount > 0 && personBioTemplateCount != null
                && personBioTemplateCount.containsKey(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE)) {
                // 补充根据模板类型获取掌纹数量
                vislightFaceTemp = personBioTemplateCount.get(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
            }

            AccDeviceOption maxUserCountOpt =
                accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), "~MaxUserCount");
            // 最大人员数量默认值,push设备9000，pull设备30000.
            int maxUserCount = Integer.parseInt(maxUserCountOpt != null ? maxUserCountOpt.getValue()
                : accDevice.getCommType() == ConstUtil.COMM_HTTP ? "90" : "300") * 100;

            int maxFingerCount = 0;
            if (maxMultiBioDataCount != null) {
                maxFingerCount =
                    Integer.parseInt(maxMultiBioDataCount.getValue().split(":")[BaseConstants.BaseBioType.FP_BIO_TYPE]);
            } else {
                AccDeviceOption maxFingerCountOpt =
                    accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), "~MaxFingerCount");
                if (maxFingerCountOpt != null) {
                    maxFingerCount = Integer.parseInt(maxFingerCountOpt.getValue()) * 100;
                }
            }

            // 查询设备容量添加人脸和指静脉 modify by qingj.qiu
            int maxFvCount = 0;
            if (maxMultiBioDataCount != null) {
                maxFvCount = Integer
                    .parseInt(maxMultiBioDataCount.getValue().split(":")[BaseConstants.BaseBioType.VEIN_BIO_TYPE]);
            } else {
                AccDeviceOption maxFvCountOpt =
                    accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), "~MaxFvCount");
                if (maxFvCountOpt != null) {
                    maxFvCount = Integer.parseInt(maxFvCountOpt.getValue()) * 100;
                }
            }

            // 最大比对照片数
            int maxBioPhotoCount = 0;
            if (maxMultiBioPhotoCount != null) {
                maxBioPhotoCount = Integer
                    .parseInt(maxMultiBioPhotoCount.getValue().split(":")[BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE]);
            } else {
                AccDeviceOption maxBiophotoCountOpt =
                    accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), "~MaxBioPhotoCount");
                if (maxBiophotoCountOpt != null) {
                    maxBioPhotoCount = Integer.parseInt(maxBiophotoCountOpt.getValue());
                }
            }

            // 最大人脸数
            int maxFaceCount = 0;
            if (maxMultiBioDataCount != null) {
                maxFaceCount = Integer
                    .parseInt(maxMultiBioDataCount.getValue().split(":")[BaseConstants.BaseBioType.FACE_BIO_TYPE]);
            } else {
                AccDeviceOption maxFaceCountOpt =
                    accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), "~MaxFaceCount");
                if (maxFaceCountOpt != null) {
                    maxFaceCount = Integer.parseInt(maxFaceCountOpt.getValue());
                }
            }

            // 最大手掌数
            int maxPalmCount = 0;
            if (maxMultiBioDataCount != null) {
                maxPalmCount = Integer
                    .parseInt(maxMultiBioDataCount.getValue().split(":")[BaseConstants.BaseBioType.PALM_BIO_TYPE]);
            } else {
                AccDeviceOption maxPalmCountOpt =
                    accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), "~MaxPvCount");
                if (maxPalmCountOpt != null) {
                    maxPalmCount = Integer.parseInt(maxPalmCountOpt.getValue());
                }
            }

            // 最大可见光人脸模版数
            int maxVislightFaceTempCount = 0;
            if (maxMultiBioDataCount != null) {
                maxVislightFaceTempCount = Integer
                    .parseInt(maxMultiBioDataCount.getValue().split(":")[BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE]);
            }

            tempList.add(accDevice.getAlias());
            tempList.add(persCount + "/" + maxUserCount);
            tempList.add(supportFinger ? fingerCount + "/" + maxFingerCount : "false");
            tempList.add(supportFv ? fvCount + "/" + maxFvCount : "false");
            tempList.add(supportFace ? faceCount + "/" + maxFaceCount : "false");
            tempList.add(supportBiophoto ? bioPhotoCount + "/" + maxBioPhotoCount : "false");
            tempList.add(supportPalm ? palmCount + "/" + maxPalmCount : "false");
            tempList.add(supportvislightFaceTemp ? vislightFaceTemp + "/" + maxVislightFaceTempCount : "false");
            tempList.add(accDevice.getId() + "");
            if (!getStatus(accDevice.getSn()).equals(String.valueOf(ConstUtil.DEV_STATE_ONLINE))) {
                tempList.add("disabled = 'disabled'");
            } else {
                tempList.add("");// 判断禁用，放入空字符串
            }
            queryList.add(tempList);
        }
        return queryList;
    }

    @Override
    public List<List<String>> queryDevRule(String id) {
        AccDevice dev = accDeviceDao.getOne(id);
        List<List<String>> ruleList = Lists.newArrayList();
        Map<String, List<String>> queryIdMap = queryIdMapByDev(dev);
        if (Objects.nonNull(queryIdMap)) {
            ruleList = queryAccessRules(dev, queryIdMap.get("door"));
        }
        if (ConstUtil.COMM_HTTP == Short.valueOf(dev.getCommType()).shortValue()
            && accSupportFuncService.isSupportAccAdvanced()) {
            ruleList = queryAdvancedRules(ruleList, dev, queryIdMap.get("door"), queryIdMap.get("auxIn"),
                queryIdMap.get("reader"));// 添加高级门禁规则
        }
        return ruleList;
    }

    /**
     * @Description: 获取普通门禁规则（互锁、反潜、联动……）
     * <AUTHOR>
     * @date 2018/6/1 8:51
     * @param devId
     * @param doorIdList
     * @return
     */
    /**
     * 获取普通门禁规则（互锁、反潜、联动……）
     *
     * @param dev
     * @param doorIdList
     * @return
     */
    private List<List<String>> queryAccessRules(AccDevice dev, List<String> doorIdList) {
        List<List<String>> ruleList = Lists.newArrayList();
        List<String> tempList = null;
        // 互锁规则
        AccInterlockItem accInterlockItem = new AccInterlockItem();
        accInterlockItem.setDeviceId(dev.getId());
        List<AccInterlockItem> accInterlockList = accInterlockService.getByCondition(accInterlockItem);
        String content = new String("");
        if (Objects.nonNull(accInterlockList) && !accInterlockList.isEmpty()) {
            accInterlockItem = accInterlockList.get(0);
            content = accInterlockService.convertInterlockRule(accInterlockItem.getId());
        }
        tempList = AccQueryRulesUtil.setRuleContent(accInterlockList.size(),
            I18nUtil.i18nCode("acc_leftMenu_interlock"), content, 0);
        ruleList.add(tempList);
        // 联动规则
        AccLinkageItem accLinkageItem = new AccLinkageItem();
        accLinkageItem.setDeviceId(dev.getId());
        List<AccLinkageItem> accLinkageItemList = accLinkageService.getByCondition(accLinkageItem);
        StringBuffer rules = new StringBuffer("");
        if (Objects.nonNull(accLinkageItemList) && !accLinkageItemList.isEmpty()) {
            accLinkageItemList.stream().forEach(linkage -> rules.append(linkage.getName()).append(" | "));
        }
        tempList = AccQueryRulesUtil.setRuleContent(I18nUtil.i18nCode("common_leftMenu_linkage"), rules.toString(), 0);
        ruleList.add(tempList);
        // 反潜规则
        content = new String("");
        AccAntiPassbackItem accAntiPassbackItem = new AccAntiPassbackItem();
        accAntiPassbackItem.setDeviceId(dev.getId());
        List<AccAntiPassbackItem> accApbList = accAntiPassbackService.getByCondition(accAntiPassbackItem);
        if (Objects.nonNull(accApbList) && !accApbList.isEmpty()) {
            accAntiPassbackItem = accApbList.get(0);
            content = accAntiPassbackService.convertAntiPassbackRule(accAntiPassbackItem.getId());
        }
        tempList = AccQueryRulesUtil.setRuleContent(accApbList.size(), I18nUtil.i18nCode("acc_leftMenu_antiPassback"),
            content, 0);
        ruleList.add(tempList);

        // 首人开门
        List<AccFirstOpenItem> accFirstOpenItemList = accFirstOpenService.getItemsByDoorIds(doorIdList);
        List<String> nameList = Lists.newArrayList();
        if (Objects.nonNull(accFirstOpenItemList) && !accFirstOpenItemList.isEmpty()) {
            for (AccFirstOpenItem accFirstOpenItem : accFirstOpenItemList) {
                AccDoorItem doorItem = accDoorService.getItemById(accFirstOpenItem.getDoorId());
                AccTimeSegItem timeSegItem = accTimeSegService.getItemById(accFirstOpenItem.getTimeSegId());
                nameList.add(doorItem.getName() + ", " + I18nUtil.i18nCode("acc_door_timeSeg", timeSegItem.getName()));
            }
        }
        tempList = AccQueryRulesUtil.buildRuleName(I18nUtil.i18nCode("acc_leftMenu_firstOpen"),
            accFirstOpenItemList.size(), doorIdList, nameList);
        ruleList.add(tempList);
        // 多人开门
        nameList = Lists.newArrayList();
        List<AccCombOpenDoorItem> accCombOpenDoorItemList = accCombOpenDoorService.getItemsByDoorIds(doorIdList);
        List<String> accCombOpenDoorIdList = Lists.newArrayList();
        if (Objects.nonNull(accCombOpenDoorItemList) && !accCombOpenDoorItemList.isEmpty()) {
            for (AccCombOpenDoorItem accCombOpenDoorItem : accCombOpenDoorItemList) {
                nameList.add(accCombOpenDoorItem.getName());
                if (!accCombOpenDoorIdList.contains(accCombOpenDoorItem.getId())) {
                    accCombOpenDoorIdList.add(accCombOpenDoorItem.getId());
                }
            }
        }
        tempList = AccQueryRulesUtil.buildRuleName(I18nUtil.i18nCode("acc_leftMenu_combOpen"),
            accCombOpenDoorIdList.size(), doorIdList, nameList);
        ruleList.add(tempList);
        // 获取门磁规则名称内容
        List<AccDoorItem> accDoorItemList = accDoorService.getItemsByIdsAndSensorStatus(doorIdList);
        nameList = AccQueryRulesUtil.getSensorRuleName(accDoorItemList);// 获取门磁规则名称内容
        tempList = AccQueryRulesUtil.buildRuleName(I18nUtil.i18nCode("acc_door_sensor"), accDoorItemList.size(),
            doorIdList, nameList);
        ruleList.add(tempList);
        // 获取有效时间段设置
        Map<String, String> dataMap = accTimeSegService.getActiveTimeSegByDoorId(doorIdList);
        nameList = AccQueryRulesUtil.getTimeSegRuleName(dataMap);// 获取时间段规则名称内容
        tempList = AccQueryRulesUtil.buildRuleName(I18nUtil.i18nCode("acc_door_activeTimeZone"), dataMap.size(),
            doorIdList, nameList);
        ruleList.add(tempList);
        // 获取常开时间段设置
        dataMap = accTimeSegService.getPassmodeTimeSegByDoorId(doorIdList);
        nameList = AccQueryRulesUtil.getTimeSegRuleName(dataMap);// 获取时间段规则名称内容
        tempList = AccQueryRulesUtil.buildRuleName(I18nUtil.i18nCode("acc_door_passageModeTimeZone"), dataMap.size(),
            doorIdList, nameList);
        ruleList.add(tempList);
        // 获取设备权限组设置
        List<AccLevelDoorItem> accLevelDoorItems = accLevelService.getLevelDoorItemsByDevSn(dev.getSn());
        Map<String, String> levelDoorMap = new LinkedHashMap<>();
        for (AccLevelDoorItem item : accLevelDoorItems) {
            String levelName = item.getAccLevelName();
            if (levelDoorMap.containsKey(item.getName())) {
                levelName = levelDoorMap.get(item.getName()) + "," + levelName;
            }
            levelDoorMap.put(item.getName(), levelName);
        }
        List<String> levelDoorNameList = Lists.newArrayList();
        levelDoorMap.forEach((doorName, levelNames) -> {
            levelDoorNameList.add(doorName + ", " + levelNames);
        });
        tempList = AccQueryRulesUtil.buildRuleName(I18nUtil.i18nCode("pers_person_authGroup"), levelDoorMap.size(),
            doorIdList, levelDoorNameList);
        ruleList.add(tempList);
        return ruleList;
    }

    /**
     * @param ruleList
     * @param tempDev
     * @param doorIdList
     * @param auxInIdList
     * @param readerIdList
     * @return
     * @Description: 获取高级门禁规则（全局互锁、全局反潜、全局联动、人员有效性、人数控制）
     * <AUTHOR>
     * @date 2018/6/1 8:50
     */
    private List<List<String>> queryAdvancedRules(List<List<String>> ruleList, AccDevice tempDev,
        List<String> doorIdList, List<String> auxInIdList, List<String> readerIdList) {
        String tempName = "";
        List<String> tempList = Lists.newArrayList();// [全局联动, 未开通, 无]
        String sn = tempDev.getSn();
        Short machineType = tempDev.getMachineType();
        if (StringUtils.isNotBlank(sn)) {
            // 后台验证
            if (accDeviceOptionService.isSupportDevParam(tempDev.getSn(), "AutoServerMode")) {
                tempList.add(I18nUtil.i18nCode("acc_dev_backgroundVerify"));
                tempList.add(I18nUtil.i18nCode("acc_common_hasBeenOpened"));
                tempList.add(I18nUtil.i18nCode("acc_common_hasBeenOpened"));
            } else {
                tempList.add(I18nUtil.i18nCode("acc_dev_backgroundVerify"));
                tempList.add(I18nUtil.i18nCode("acc_common_notOpened"));
                tempList.add(I18nUtil.i18nCode("acc_common_notOpened"));
            }
            ruleList.add(tempList);
        }
        if (accGetAccGlobalApbService != null) {
            tempList = accGetAccGlobalApbService.getGapbNameByReaderZoneId(readerIdList);// 全局反潜
            tempList = AccQueryRulesUtil.buildRuleInfo(tempList, I18nUtil.i18nCode("acc_leftMenu_gapbSet"));
            ruleList.add(tempList);
        }
        if (accGetAccGlobalInterlockService != null) {
            tempList = accGetAccGlobalInterlockService.getGInterlockNameByDoorId(doorIdList);// 全局互锁
            tempList = AccQueryRulesUtil.buildRuleInfo(tempList, I18nUtil.i18nCode("acc_leftMenu_globalInterlock"));
            ruleList.add(tempList);
        }
        if (accGetAccGlobalLinkageService != null) {// 全局联动
            tempList = accGetAccGlobalLinkageService.getGLinkageNameByParam(doorIdList, "AccDoor");
            List<String> linkageNameList = null;
            if (auxInIdList.size() > 0) {
                linkageNameList = accGetAccGlobalLinkageService.getGLinkageNameByParam(auxInIdList, "AccAuxIn");
            }
            if (Objects.nonNull(tempList)) {
                tempName += AccQueryRulesUtil.nameListToString(tempList);
            }
            if (Objects.nonNull(linkageNameList)) {
                tempName += AccQueryRulesUtil.nameListToString(linkageNameList);
            }
            tempList = AccQueryRulesUtil.setRuleContent(I18nUtil.i18nCode("acc_leftMenu_globalLinkage"), tempName, 1);
            ruleList.add(tempList);
        }
        if (accGetAccPersonLimitService != null) {// 人员有效性
            tempList = accGetAccPersonLimitService.getPersonLimitZoneNameByReaderId(readerIdList);// 人员有效性
            tempList = AccQueryRulesUtil.buildRuleInfo(tempList, I18nUtil.i18nCode("acc_leftMenu_personLimit"));
            ruleList.add(tempList);
        }
        if (accGetAccOccupancyService != null) {// 人数控制
            tempList = accGetAccOccupancyService.getOccupancyNameByReaderId(readerIdList);// 人数控制
            tempList = AccQueryRulesUtil.buildRuleInfo(tempList, I18nUtil.i18nCode("acc_leftMenu_occupancy"));
            ruleList.add(tempList);
        }
        return ruleList;
    }

    private JSONObject getQueryCmdIdData(long cmdId) {
        JSONObject dataCountJson = null;
        if (cmdId > 0) {
            Map<String, String> resultMap = getCmdResultById(cmdId, 20);
            if (Objects.nonNull(resultMap)) {
                Integer ret = Integer.valueOf(resultMap.get("result"));
                if (ret >= 0) {
                    if ("".equals(getQueryData(AccCacheKeyConstants.ERROR_USER))) {
                        if (!"".equals(getQueryData(AccCacheKeyConstants.QUERY_DATA_COUNT + cmdId))) {
                            dataCountJson =
                                JSONObject.parseObject(getQueryData(AccCacheKeyConstants.QUERY_DATA_COUNT + cmdId));
                        }
                        delQueryData(AccCacheKeyConstants.SUM_RECORD_USER);
                    }
                    delQueryData(AccCacheKeyConstants.ERROR_USER);
                }
            }
        }
        return dataCountJson;
    }

    @Override
    public Map<String, Long> getDevUserAndTempCounts(String devId) {
        AccDevice accDevice = accDeviceDao.findById(devId).orElse(null);
        JSONObject retObject = new JSONObject();
        retObject.put("id", accDevice.getId());
        Map<String, Long> cmdMap = new HashMap<>();
        if (Objects.nonNull(accDevice)) {
            long cmdId = accDevCmdManager.getPersonCountFromDev(accDevice.getSn(), true);
            cmdMap.put(ConstUtil.USER, cmdId);
            AccDeviceOption multiBioDataSupport =
                accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), "MultiBioDataSupport");
            AccDeviceOption multiBioPhotoSupport =
                accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), "MultiBioPhotoSupport");
            String[] multiBioVersionAry = null;
            if (multiBioDataSupport != null) {
                multiBioVersionAry = multiBioDataSupport.getValue().split(":");
            }
            // 指纹
            if (multiBioDataSupport != null) {
                if ("1".equals(getMultiBioVersion(multiBioVersionAry, BaseConstants.BaseBioType.FP_BIO_TYPE))) {
                    cmdId = accDevCmdManager.getPersonBioTemplateCountFromDev(accDevice.getSn(),
                        CmdSecConstants.TEMPLATE_FP, true);
                    cmdMap.put(AccConstants.BIODATA + "_" + CmdSecConstants.TEMPLATE_FP, cmdId);
                }
            } else {
                boolean supportFinger = false;
                AccDeviceOption fingerFunOn =
                    accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), AccConstants.FINGER_FUN_ON);
                AccDeviceOption isOnlyRFMachine =
                    accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), AccConstants.ONLY_RF_MACHINE);
                if (fingerFunOn != null) {
                    supportFinger = "1".equals(fingerFunOn.getValue());
                } else if (isOnlyRFMachine != null) {
                    supportFinger = !"1".equals(isOnlyRFMachine.getValue());
                }
                if (supportFinger) {
                    String fingerVersion =
                        accDeviceOptionService.getValueByNameAndDevSn(accDevice.getSn(), "~ZKFPVersion");// 设备指纹版本
                    // 下发获取指纹数量
                    cmdId = accDevCmdManager.getPersonBioTemplateCountFromDev(accDevice.getSn(),
                        CmdSecConstants.TEMPLATE_FP, true);
                    if (CmdSecConstants.FP_VERSION_12.equals(fingerVersion)) {
                        cmdMap.put(AccConstants.BIODATA + "_" + CmdSecConstants.TEMPLATE_FP, cmdId);
                    } else {
                        cmdMap.put(ConstUtil.TEMPLATEV10, cmdId);
                    }
                }
            }

            // 指静脉

            if (multiBioDataSupport != null) {
                if ("1".equals(getMultiBioVersion(multiBioVersionAry, BaseConstants.BaseBioType.VEIN_BIO_TYPE))) {
                    cmdId = accDevCmdManager.getPersonBioTemplateCountFromDev(accDevice.getSn(),
                        CmdSecConstants.TEMPLATE_VEIN, true);
                    cmdMap.put(AccConstants.BIODATA + "_" + CmdSecConstants.TEMPLATE_VEIN, cmdId);
                }
            } else if (accDeviceOptionService.isSupportFun(accDevice.getSn(), AccConstants.FV_FUN_ON)) {
                // 下发指静脉
                cmdId = accDevCmdManager.getPersonBioTemplateCountFromDev(accDevice.getSn(),
                    CmdSecConstants.TEMPLATE_VEIN, true);
                cmdMap.put(AccConstants.FVTEMPLATE, cmdId);
            }

            // 可见光人脸
            boolean supportBioPhoto =
                accDeviceOptionService.isSupportFun(accDevice.getSn(), AccConstants.BIOPHOTO_FUN_ON);
            boolean supportFace = accDeviceOptionService.isSupportFun(accDevice.getSn(), AccConstants.FACE_FUN_ON);
            if (multiBioDataSupport != null) {
                if ("1".equals(getMultiBioVersion(multiBioVersionAry, BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE))) {
                    cmdId = accDevCmdManager.getPersonBioTemplateCountFromDev(accDevice.getSn(),
                        CmdSecConstants.TEMPLATE_BIOPHOTO, true);
                    cmdMap.put(AccConstants.BIODATA + "_" + CmdSecConstants.TEMPLATE_BIOPHOTO, cmdId);
                }
                // 新协议照片获取相关使用新参数
                /*if (multiBioPhotoSupport != null) {
                    if ("1".equals(
                        multiBioPhotoSupport.getValue().split(":")[BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE])) {
                        cmdId = accDevCmdManager.getPersonBioPhotoCountFromDev(accDevice.getSn(), true);
                        cmdMap.put(AccConstants.BIOPHOTO, cmdId);
                    }
                }*/
            } else if (supportBioPhoto) {
                /*cmdId = accDevCmdManager.getPersonBioPhotoCountFromDev(accDevice.getSn(), true);
                cmdMap.put(AccConstants.BIOPHOTO, cmdId);*/
                // 获取可见光人脸模板数量
                cmdId = accDevCmdManager.getBiodataCountByFaceFromDev(accDevice.getSn());
                cmdMap.put(AccConstants.BIODATA + "_" + CmdSecConstants.TEMPLATE_BIOPHOTO, cmdId);
            }

            // 近红外人脸
            if (multiBioDataSupport != null) {
                if ("1".equals(getMultiBioVersion(multiBioVersionAry, BaseConstants.BaseBioType.FACE_BIO_TYPE))) {
                    cmdId = accDevCmdManager.getPersonBioTemplateCountFromDev(accDevice.getSn(),
                        CmdSecConstants.TEMPLATE_FACE, true);
                    cmdMap.put(AccConstants.BIODATA + "_" + CmdSecConstants.TEMPLATE_FACE, cmdId);
                }
            } else if (supportFace && !supportBioPhoto) {
                // 下发获取人脸数量
                cmdId = accDevCmdManager.getPersonBioTemplateCountFromDev(accDevice.getSn(),
                    CmdSecConstants.TEMPLATE_FACE, true);
                cmdMap.put(ConstUtil.FACEV7, cmdId);
            }

            if (multiBioDataSupport != null) {
                if ("1".equals(getMultiBioVersion(multiBioVersionAry, BaseConstants.BaseBioType.PALM_BIO_TYPE))) {
                    cmdId = accDevCmdManager.getPersonBioTemplateCountFromDev(accDevice.getSn(),
                        CmdSecConstants.TEMPLATE_PALM, true);
                    cmdMap.put(AccConstants.BIODATA + "_" + CmdSecConstants.TEMPLATE_PALM, cmdId);
                } else if ("1".equals(getMultiBioVersion(multiBioVersionAry, AccConstants.TEMPLATE_VISILIGHT_PALM))) {
                    cmdId = accDevCmdManager.getPersonBioTemplateCountFromDev(accDevice.getSn(),
                        AccConstants.TEMPLATE_VISILIGHT_PALM, true);
                    cmdMap.put(AccConstants.BIODATA + "_" + AccConstants.TEMPLATE_VISILIGHT_PALM, cmdId);
                }
            } else if (accDeviceOptionService.isSupportFun(accDevice.getSn(), AccConstants.PV_FUN_ON)) {
                cmdId = accDevCmdManager.getPersonBioTemplateCountFromDev(accDevice.getSn(),
                    CmdSecConstants.TEMPLATE_PALM, true);
                cmdMap.put(AccConstants.BIODATA + "_" + CmdSecConstants.TEMPLATE_PALM, cmdId);
            }
            //虹膜
            if (multiBioDataSupport != null) {
                if ("1".equals(getMultiBioVersion(multiBioVersionAry, AccConstants.TEMPLATE_VISILIGHT_IRIS))) {
                    cmdId = accDevCmdManager.getPersonBioTemplateCountFromDev(accDevice.getSn(),
                            CmdSecConstants.TEMPLATE_IRIS, true);
                    cmdMap.put(AccConstants.BIODATA + "_" + CmdSecConstants.TEMPLATE_IRIS, cmdId);
                }
            }
        }
        return cmdMap;
    }

    @Override
    public String getMultiBioVersion(String[] multiBioVersionAry, Short bioType) {
        if (multiBioVersionAry != null) {
            int length = multiBioVersionAry.length;
            if (length > bioType) {
                return multiBioVersionAry[bioType].equals("0") ? "" : multiBioVersionAry[bioType];
            }
        }
        return "";
    }

    @Override
    public boolean getNtpStatusById(String id) {
        AccDeviceOptionItem option = accDeviceOptionService.getItemByDevIdAndName(id, AccConstants.NTP_KRY);
        if (option != null) {
            return StringUtils.isNotBlank(option.getValue());
        }
        return false;
    }

    @Override
    public void setNtpServer(String ids, Short status, String serverAddress) {
        if (StringUtils.isNotBlank(ids)) {
            List<AccDeviceItem> deviceItems = getItemByIds(ids);
            if (deviceItems != null && deviceItems.size() > 0) {
                for (AccDeviceItem device : deviceItems) {
                    status = status != null ? status : Short.valueOf("1");
                    accDeviceOptionService.setDevOptValByName(device.getId(), AccConstants.NTP_FUN_ON,
                        status.toString());
                    accDevCmdManager.setNtpFunOn(device.getSn(), status == 1, false);
                    List<String> addressList = null;
                    if (StringUtils.isNotBlank(serverAddress)) {
                        addressList = StrUtil.strToList(serverAddress.replaceAll(";", ","));
                    }
                    accDevCmdManager.setNtpAddress(device.getSn(), addressList, false);
                    // 保存参数
                    accDeviceOptionService.saveByDevIdAndOptionNameAndValue(device.getId(), AccConstants.NTP_KRY,
                        serverAddress);
                }
            }
        }
    }

    @Override
    public AccDeviceItem getItemByIp(String devIp) {
        AccDeviceItem accDeviceItem = new AccDeviceItem();
        accDeviceItem.setIpAddress(devIp);
        List<AccDeviceItem> accDeviceItemList = getByCondition(accDeviceItem);
        if (accDeviceItemList != null && accDeviceItemList.size() > 0) {
            return accDeviceItemList.get(0);
        }
        return null;
    }

    @Override
    public List<AccDeviceExportItem> getExportItemList(AccDeviceExportItem condition, int beginIndex, int endIndex) {
        return accDeviceDao.getItemsDataBySql(AccDeviceExportItem.class, SQLUtil.getSqlByItem(condition), beginIndex,
            endIndex, true);
    }

    @Override
    public Map<String, String> getAuthAreaByDev(List<AccDeviceExportItem> accDeviceItemList) {
        List<String> authAreaIdList =
            (List<String>)CollectionUtil.getPropertyList(accDeviceItemList, AccDeviceExportItem::getAuthAreaId, "-1");
        List<AuthAreaItem> authAreaItemList = authAreaService.getItemsByIds(authAreaIdList);
        return (Map)Optional.ofNullable(authAreaItemList).filter((l) -> {
            return !l.isEmpty();
        }).map((l) -> {
            return (Map)l.stream().collect(Collectors.toMap(AuthAreaItem::getId, AuthAreaItem::getName));
        }).orElse(Collections.emptyMap());
    }

    @Override
    public Long updateMThreshold(Acc4UpdateMThreshold acc4UpdateMThreshold) {
        AccDevice accDevice = accDeviceDao.findById(acc4UpdateMThreshold.getDevId()).orElse(null);
        Long cmdId = null;
        if (accDevice != null) {
            cmdId = accDevCmdManager.setMThreshold(accDevice.getSn(), acc4UpdateMThreshold, true);
        }
        return cmdId;
    }

    @Override
    public Long updateCommPwd(String devId, String newCommPwd) {
        AccDevice device = accDeviceDao.findById(devId).orElse(null);
        long cmdId = -1;
        if (device != null) {
            // 发送修改ip的命令到设备
            cmdId = accDevCmdManager.setCommPwd(device, newCommPwd, true);
        }
        return cmdId;
    }

    @Override
    public void updateAdmsDeviecPwd(String deviceSn, String newCommPwd) {
        admsDeviceService.updateCommPwd(deviceSn, newCommPwd);
    }

    @Override
    public boolean isExistAlias(String alias) {
        AccDevice accDevice = accDeviceDao.findByAlias(alias);
        return accDevice == null;
    }

    @Override
    public boolean isExistIpAddress(String ipAddress) {
        AccDevice accDevice = accDeviceDao.findByIpAddress(ipAddress);
        return accDevice == null;
    }

    @Override
    public List<Long> rebootDevice(List<String> devIdList, boolean isImme) {
        List<AccDevice> accDeviceList = accDeviceDao.findByIdIn(devIdList);
        List<Long> cmdIdList = Lists.newArrayList();
        accDeviceList.stream().forEach(dev -> {
            cmdIdList.add(accDevCmdManager.rebootDev(dev.getSn(), isImme));
        });
        return cmdIdList;
    }

    @Override
    public void clearCmdCache(String sn, String... actionType) {
        List<String> snList = (List<String>)CollectionUtil.strToList(sn);
        if (Objects.nonNull(snList) && !snList.isEmpty()) {
            snList.stream().forEach(devSn -> admsDeviceService.clearDevCmd(devSn, actionType));// 清空设备在缓存中存在的命令
        }
    }

    @Override
    public List<AccDeviceItem> getItemByIdsAndMoveUpParentDev(String devIds) {
        List<AccDevice> accDeviceList = accDeviceDao.findByIdIn((List<String>)CollectionUtil.strToList(devIds));
        AccDeviceUtil.moveUpParentDevList(accDeviceList);
        return ModelUtil.copyListProperties(accDeviceList, AccDeviceItem.class);
    }

    @Override
    public void setDeviceTimeZoneToDev(AccDeviceItem devItem, String timeZone) {
        AccDevice accDevice = accDeviceDao.findById(devItem.getId()).orElse(null);
        // 同步时间和时区不往子设备下发，固件自己同步 by juvenile.li add 20171219
        if (accDevice != null && accDevice.getParentDevice() == null) {
            setTimeZoneToDev(accDevice, timeZone);
        }
    }

    private void setTimeZoneToDev(AccDevice accDevice, String timeZone) {
        setTimeZoneAndTIme(accDevice, timeZone);
    }

    private Long setTimeZoneAndTIme(AccDevice accDevice, String timeZone) {
        accDevCmdManager.setTimeZone(accDevice.getSn(), timeZone, true);// 设置时区
        Long cmdId = accDevCmdManager.syncTime(accDevice.getSn(), AccDataUtil.getGMT(), true);// 同步时间
        accDevice.setTimeZone(timeZone);
        accDeviceDao.save(accDevice);
        setAdmsDeviceTimeZone(accDevice.getSn(), timeZone);
        return cmdId;
    }

    private void setAdmsDeviceTimeZone(String devSn, String timeZone) {
        final String TIMEZONE_KEY = "timezone";
        String deviceInfo = admsCacheService.getDeviceInfo(devSn);
        JSONObject devJson;
        if (StringUtils.isNotBlank(deviceInfo)) {
            devJson = JSONObject.parseObject(deviceInfo);
        } else {
            devJson = new JSONObject();
        }
        devJson.put(TIMEZONE_KEY, timeZone);
        admsCacheService.cacheDeviceInfo(devSn, devJson.toJSONString());
    }

    @Override
    public void saveDSTime(AccDeviceItem dev, AccDSTimeItem accDSTimeItem) {
        AccDevice accDevice = accDeviceDao.findBySn(dev.getSn());
        if (accDSTimeItem == null) {
            accDevice.setAccDSTime(null);
        } else {
            AccDSTime accDSTime = accDSTimeDao.findById(accDSTimeItem.getId()).orElse(null);
            accDevice.setAccDSTime(accDSTime);
        }
        accDeviceDao.save(accDevice);
    }

    @Override
    public Long updateRs485Addr(String devId, String comAddr) {
        AccDevice dev = accDeviceDao.findById(devId).orElse(null);
        Long cmdId = null;
        if (dev != null) {
            dev.setComAddress(Short.parseShort(comAddr));
            cmdId = accDevCmdManager.updateRs485Addr(dev, comAddr, true);
        }
        return cmdId;
    }

    @Override
    public Long switchNetWorkTest(String devId, String currentMode, String netConnectMode, String wirelessSSID,
        String wirelessKey) {
        AccDevice accDevice = accDeviceDao.findById(devId).orElse(null);
        Long cmdId = null;
        if (accDevice != null) {
            if ("2".equals(netConnectMode)) {
                accDevCmdManager.setTempWIFI(accDevice, wirelessSSID, wirelessKey, false);
            }
            /** openOrClose: 1:有线切换4G 2：有线切WIFI 3： 4G切有线 4： WIFI切有线； */
            int openOrClose = 4;
            if (currentMode.equals("0") && netConnectMode.equals("1")) {
                openOrClose = 1;
            } else if (!currentMode.equals("1") && netConnectMode.equals("2")) {
                openOrClose = 2;
            } else if (currentMode.equals("1") && netConnectMode.equals("0")) {
                openOrClose = 3;
            }
            cmdId = accDevCmdManager.switchNetWorkTest(accDevice, openOrClose, true);
        }
        return cmdId;
    }

    @Override
    public void setOrDelDST(AccDeviceItem dev, AccDSTimeItem accDSTimeItem, String type) {
        AccDevice accDevice = accDeviceDao.findById(dev.getId()).orElse(null);
        if (accDevice != null) {
            accDevCmdManager.delDSTime(accDevice.getSn(), false);
            if ("set".equals(type)) {
                accDevCmdManager.setDSTime(accDevice.getSn(), accDSTimeItem, false);
            }

            if (accDevice.getParentDevice() == null)// 非子设备需要同步一下时间，子设备交给IR9000自己同步 by juvenile.li add 20171206
            {
                syncTime(dev.getSn());
            }
        }
    }

    @Override
    public Map<String, String> getCmdResultById(Long cmdId, int timeout) {
        int sleepTime = 500;// 单位ms
        ExecutorService threadPool = Executors.newSingleThreadExecutor();
        // 必须采用异步线程才能得到值
        Future<Map<String, String>> future = threadPool.submit(new Callable<Map<String, String>>() {
            public Map<String, String> call() throws Exception {
                Map<String, String> rs = null;
                for (int i = 0; i < timeout * 1000 / sleepTime; i++) {
                    rs = admsDevCmdService.getResultMap(cmdId);
                    if (rs != null) {
                        return rs;
                    } else {
                        try {
                            Thread.sleep(sleepTime);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }
                }
                return null;
            }
        });

        Map<String, String> ret = null;
        try {
            ret = future.get();
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        return ret;
    }

    @Override
    public Long updateNetConnectMode(String devId, String netConnectMode, String wirelessSSID, String wirelessKey) {
        AccDevice accDevice = accDeviceDao.findById(devId).orElse(null);
        Long cmdId = null;
        if (accDevice != null) {
            String mode = "ethernet";
            if (netConnectMode.equals("1")) {
                mode = "usb-4G-modem";
            } else if (netConnectMode.equals("2")) {
                mode = "serial-wireless";
            }
            cmdId = accDevCmdManager.setNetConnectMode(accDevice, mode, wirelessSSID, wirelessKey, true);
        }
        return cmdId;
    }

    @Override
    public void grantAuthority(AccSearchAddDeviceItem item) {
        admsAuthDeviceService.authDevice(item.getSn());
        accCacheManager.putTempDevice2Cache(item.getSn(), item);
    }

    /**
     * 添加设备后清空数据用
     *
     * @param accDevice
     */
    private void clearAllDataFromDev(AccDevice accDevice) {
        accDevCmdManager.delAllPersonBioTemplateFromDev(accDevice.getSn(), false);
        accDevCmdManager.delAllPersonLevelFromDev(accDevice.getSn(), false);
        accDevCmdManager.delAllPersonFromDev(accDevice.getSn(), false);
        accDevCmdManager.delAllLinkageFromDev(accDevice.getSn(), false);
        accDevCmdManager.delAllTriggerList(accDevice.getSn(), false);
        accDevCmdManager.delAllInterlockFromDev(accDevice.getSn(), false);// 删除互锁
        accDevCmdManager.delAllAntipassbackFromDev(accDevice.getSn(), false);// 删除反潜
        accDevCmdManager.delAllFirstOpenFromDev(accDevice.getSn(), false);// 删除首人开门
        accDevCmdManager.delDSTime(accDevice.getSn(), false);// 删除夏令时设置
        accDevCmdManager.delAllAuxOutOptFromDev(accDevice.getSn(), false);
        accDevCmdManager.delAllVerifyModeRuleFromDev(accDevice.getSn(), false);
        // accDevCmdManager.delAllBiodataFromDev(accDevice.getSn(), false);// 删除一体化模板表
    }

    /**
     * 添加设备后初始化配置
     *
     * @param accDevice
     */
    private void initDevConfig(AccDevice accDevice) {
        accDevCmdManager.setDevPropertyToDev(accDevice, false);
        accDevCmdManager.delAllDevParametersFromDev(accDevice.getSn(), false);
        accDevCmdManager.delAllDoorPropertyFromDev(accDevice.getSn(), false);
        accDevCmdManager.setDoorPropertyToDev(accDevice, accDevice.getAccDoorList(), false);
        accDevCmdManager.delAllDoorParametersFromDev(accDevice.getSn(), false);
        accDevCmdManager.delAllReaderWGFormatFromDev(accDevice.getSn(), false);
        accDevCmdManager.delAllInputIOSettingFromDev(accDevice, false);
        List<AccDoorItem> doorList = accDoorService.getItemsByDevSn(accDevice.getSn());
        doorList.forEach(accDoor -> accDevCmdManager.setDoorOptionToDev(accDoor, false));

        accDevCmdManager.delAllAuxInPropertyFromDev(accDevice.getSn(), false);
        accDevCmdManager.setAuxInPropertyToDev(accDevice, accDevice.getAccAuxInList(), false);
        accDevCmdManager.setAuxInOptToDev(accDevice, accDevice.getAccAuxInList(), false);

        accDevCmdManager.delAllAuxOutPropertyFromDev(accDevice, false);
        accDevCmdManager.setAuxOutPropertyToDev(accDevice, accDevice.getAccAuxOutList(), false);
        accDevCmdManager.delAllAuxOutOptFromDev(accDevice.getSn(), false);

        accDevCmdManager.delAllReaderPropertyFromDev(accDevice, false);
        accDevCmdManager.delAllReaderParametersFromDev(accDevice, false);
        accDevCmdManager.setReaderOptToDev(accDevice, accReaderDao.findByAccDoor_Device_Id(accDevice.getId()), false);

        accDevCmdManager.delAllExtBoardPropertyFromDev(accDevice.getSn(), false);
        accDevCmdManager.delAllExtBoardRelationListFromDev(accDevice, false);

        if (accDeviceOptionService.isSupportFun(accDevice.getSn(), "AutoServerFunOn")) {
            // 下发后台验证参数
            long ret = accDevCmdManager.issueVerifyParamToDev(accDevice.getSn(), String.valueOf(AccConstants.DISABLE),
                AccConstants.OFFLINE_RULE_STANDARDLEVEL_STRING, false);// 初始化后台验证参数(默认后台验证不启用，标准同行权限)
            if (ret >= 0) {
                AccDeviceOption autoServerModeOption =
                    accDeviceOptionDao.findByAccDevice_SnAndName(accDevice.getSn(), "AutoServerMode");
                if (autoServerModeOption != null) {
                    autoServerModeOption.setValue(String.valueOf(AccConstants.DISABLE));
                    accDeviceOptionDao.save(autoServerModeOption);// 更新数据库中后台验证参数为禁用
                } else {
                    autoServerModeOption = new AccDeviceOption();
                    autoServerModeOption.setAccDevice(accDevice);
                    autoServerModeOption.setName("AutoServerMode");
                    autoServerModeOption.setValue(String.valueOf(AccConstants.DISABLE));
                    autoServerModeOption.setType(ConstUtil.DEV_FUN_PARAM);
                    accDeviceOptionDao.save(autoServerModeOption);
                }
            }
        }
        if (accDevice.getCommType() == ConstUtil.COMM_TCPIP || accDevice.getCommType() == ConstUtil.COMM_RS485) {
            // pull设备支持混合生物识别协议需要软件回发参数给设备
            AccDeviceOption multiBioDataSupport =
                accDeviceOptionDao.findByAccDevice_SnAndName(accDevice.getSn(), "MultiBioDataSupport");
            AccDeviceOption multiBioPhotoSupport =
                accDeviceOptionDao.findByAccDevice_SnAndName(accDevice.getSn(), "MultiBioPhotoSupport");
            Map<String, String> optionMap = new HashMap<>();
            if (multiBioDataSupport != null && StringUtils.isNotBlank(multiBioDataSupport.getValue())) {
                optionMap.put("MultiBioDataSupport", multiBioDataSupport.getValue());
            }
            if (multiBioPhotoSupport != null && StringUtils.isNotBlank(multiBioPhotoSupport.getValue())) {
                optionMap.put("MultiBioPhotoSupport", multiBioPhotoSupport.getValue());
            }
            accDevCmdManager.setOption(accDevice.getSn(), optionMap, false);
        }

        accDevCmdManager.delAllTimeSegFromDev(accDevice.getSn(), false);
        accDevCmdManager.setTimeSegToDev(accDevice.getSn(), accTimeSegService.getByCondition(new AccTimeSegItem()),
            false);
        accDevCmdManager.delAllHolidayFromDev(accDevice.getSn(), false);
        accDevCmdManager.setHolidayToDev(accDevice.getSn(), accHolidayService.getByCondition(new AccHolidayItem()),
            false);
        accDevCmdManager.setTimeZone(accDevice.getSn(), accDevice.getTimeZone(), true);
        // 同步时间需使用紧急命令形式，不然时间会存在误差
        accDevCmdManager.syncTime(accDevice.getSn(), new Date(), true);

        accDevCmdManager.setWGFmtWithSitecodeOn(accDevice.getSn(), true, false);
        List<PersWiegandFmtItem> defWGList = persWiegandFmtService.getAutoMatch();
        if (accDevice.getMachineType() == AccConstants.DEVICE_ACCESS_CONTROL) {
            accDevCmdManager.delAllCardFormat(accDevice, false);
            accDevCmdManager.setCardFormatToDev(accDevice, defWGList, false);
        } else {
            accDevCmdManager.delAllDefWGFormat(accDevice.getSn(), false);
            accDevCmdManager.setDefWGFormatToDev(accDevice.getSn(), defWGList, false);
            accDevCmdManager.delAllWGFormat(accDevice.getSn(), false);
            accDevCmdManager.setCusWGFormatToDev(accDevice.getSn(),
                persWiegandFmtService.getByCondition(new PersWiegandFmtItem()), false);
        }

        accDevCmdManager.setUserWithMulCardFunOn(accDevice, true, false);
        accDevCmdManager.setDoorMaskFlag(accDevice, accDevice.getAccDoorList(), false, false);
        accDevCmdManager.delAllChildDevice(accDevice, false);
        accDevCmdManager.delAllCombOpenDoorFromDev(accDevice.getSn(), false);// 默认清除多人开门设置，为了兼容一体机，有时候会机器多人开门参数是打开的，需统一关闭处理
    }

    @Override
    public int getCountByDSTimeId(String ids) {
        return accDeviceDao.findByAccDSTime_IdIn((List<String>)CollectionUtil.strToList(ids)).size();
    }

    @Override
    public String getDeviceAuthorize(String devSn) {
        String ret = null;
        if (accCacheManager.exists(AccCacheKeyConstants.DEVICE_AUTHORIZE_KEY + devSn)) {
            ret = accCacheManager.getDeviceAuthorizeInfo(devSn);
        }
        return ret;
    }

    @Override
    public Long queryAuthorizeListFromDev(String devId) {
        Long cmdId = 0L;
        AccDevice accDevice = accDeviceDao.findById(devId).orElse(null);
        if (Objects.nonNull(accDevice)) {
            accCacheManager.delDeviceAuthorizeInfo(accDevice.getSn());
            cmdId = accDevCmdManager.queryAuthorizeListFromDev(accDevice, true);
        }
        return cmdId;
    }

    @Override
    public void updateItemByParam(AccDeviceItem accDeviceItem) {
        AccDevice accDevice = Optional.ofNullable(accDeviceItem).map(i -> i.getId()).filter(StringUtils::isNoneBlank)
            .flatMap(id -> accDeviceDao.findById(id)).orElse(new AccDevice());
        ModelUtil.copyPropertiesIgnoreNull(accDeviceItem, accDevice);
        if ("".equals(accDevice.getCommPwd())) {
            accDevice.setCommPwd(null);
        }
        accDeviceDao.save(accDevice);
    }

    @Override
    public List<AccDeviceItem> getByMachineTypeAndAuth(String sessionId, Short machineType) {
        AccDeviceItem accDeviceItem = new AccDeviceItem();
        accDeviceItem.setMachineType(machineType);
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            accDeviceItem.setUserId(userId);
        }
        return getByCondition(accDeviceItem);
    }

    @Override
    public boolean isRegistrationDevice(String sn, String data) {
        boolean isRegDev = false;
        AccDevice accDevice = accDeviceDao.findBySn(sn);
        JSONObject dataJson = JSONObject.parseObject(data);
        if ((!accDevice.getDeviceName().equals(AccConstants.DEVICE_TF1700) && accDevice.getIsRegistrationDevice())
            || AccConstants.DEVICE_TF1700.equals(accDevice.getDeviceName())
            || AccConstants.ATTPHOTO.equals(dataJson.getString("table"))
            || ConstUtil.COUNT.equals(dataJson.getString("type"))
            || ConstUtil.OPTIONS.equals(dataJson.getString("type"))
            || accDevice.getMachineType() != AccConstants.DEVICE_ACCESS_CONTROL) {
            isRegDev = true;
        }
        return isRegDev;
    }

    @Override
    public Long configParentDevice(String id, String parentDevId, String webServerURL) {
        AccDevice dev = accDeviceDao.findById(id).orElse(null);
        if (Objects.isNull(dev)) {
            throw new ZKBusinessException("common_dev_notExistDev");
        }
        AccDevice oldParentDev = dev.getParentDevice();
        Long cmdId = null;
        if (StringUtils.isBlank(parentDevId)) { // 从子设备切成独立主控设备
            if (oldParentDev == null) { // 子设备的父设备不存在
                throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_ERROR,
                    "common_prompt_exception_datanoexists");
            }
            oldParentDev.getChildDevices().remove(dev);
            cmdId = accDevCmdManager.setChildWebServerUrl(dev, false, false, webServerURL, true);
        } else {
            AccDevice parentDev = accDeviceDao.findById(parentDevId).orElse(null);
            if (Objects.nonNull(parentDev)) {
                AccDeviceOption devOpt = accDeviceOptionDao.findByAccDevice_SnAndName(parentDev.getSn(), "MaxSubCount");
                if (Objects.isNull(devOpt)) {
                    throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_ERROR,
                        "common_commStatus_senateIsEmpty");
                }
                Integer maxSubCount = Integer.valueOf(devOpt.getValue());
                if (parentDev.getChildDevices().size() + 1 > maxSubCount) {
                    return Long.valueOf(-1 * maxSubCount);
                }
                AccDeviceOption devOptIp =
                    accDeviceOptionDao.findByAccDevice_SnAndName(parentDev.getSn(), "ServerCommIP");
                AccDeviceOption devOptPort =
                    accDeviceOptionDao.findByAccDevice_SnAndName(parentDev.getSn(), "ServerCommPort");
                String bestSvrAddress = "ws://" + devOptIp.getValue() + ":" + devOptPort.getValue();
                cmdId = accDevCmdManager.setBestSvrAddress(dev, false, true, bestSvrAddress, true);// 设置BestSvrAddress
            }
        }
        return cmdId;
    }

    @Override
    public void updateDevOptions(Map<String, String> optionMap) {
        AccDevice device = accDeviceDao.findBySn(optionMap.get("~SerialNumber"));// 获取设备参数必须有sn字段。否则会报错
        if (Objects.nonNull(device)) {
            JSONObject optionJson = accCacheManager.getDeviceOptionInfo(device.getSn());
            boolean isUpdate = false;
            if (optionJson != null) {
                isUpdate = true;
            }
            String oldDevFWVer = device.getFwVersion();// 原先保存在数据库的版本号
            device = updateDevOption(device, optionMap);// 更新设备参数
            accDeviceDao.save(device);
            List<AccDeviceVerifyMode> verifyModeList = Lists.newArrayList();
            List<AccDeviceEvent> deviceEventList = Lists.newArrayList();
            List<AccDeviceOption> deviceOptionList = Lists.newArrayList();
            for (String key : optionMap.keySet()) {
                // 更新设备验证方式和事件；新增或修改设备的事件类型，固件版本一致不需要更新
                // 不同设备不支持新验证方式功能，NewVFStyles参数获取上来值可能为0或者""，这边做兼容处理
                if ("NewVFStyles".equals(key) && StringUtils.isNotBlank(optionMap.get(key))
                    && !"0".equals(optionMap.get(key)) && !optionMap.get("FirmVer").equals(oldDevFWVer)) {
                    verifyModeList = saveDevNewVerifyMode(optionMap, device);
                } else if ("VerifyStyles".equals(key) && !optionMap.get("FirmVer").equals(oldDevFWVer)
                    && (StringUtils.isBlank(optionMap.get("NewVFStyles")) || "0".equals(optionMap.get(key)))) {
                    verifyModeList = saveDevVerifyMode(optionMap, device);
                } else if ("EventTypes".equals(key) && !optionMap.get("FirmVer").equals(device.getFwVersion())) {
                    deviceEventList = saveDevEventType(optionJson, optionMap, device);// 更新设备事件类型
                }
                if (StringUtils.isNotBlank(optionMap.get(key))) { // 空值不处理
                    AccDeviceOption deviceOption = saveDevOption(device, key, optionMap.get(key));// 更新设备参数
                    if (Objects.nonNull(deviceOption)) {
                        deviceOptionList.add(deviceOption);
                    }
                    if (isUpdate) {
                        optionJson.put(deviceOption.getName(), deviceOption.getValue());
                    }
                }
            }
            if (isUpdate) {
                accCacheManager.putDeviceOptionInfo(device.getSn(), optionJson.toJSONString());// 更新设备参数信息
            }
            if (verifyModeList.size() > 0) {
                device.setAccDeviceVerifyModeList(verifyModeList);// 更新到缓存
            }
            if (deviceEventList.size() > 0) {
                // 删除设备事件类型，获取时会重新查询再放入缓存
                accCacheManager.delete(AccCacheKeyConstants.ACC_DEVICE_EVENT + device.getSn());
                device.setAccDeviceEventList(deviceEventList);// 更新到缓存
            }
            if (deviceOptionList.size() > 0) {
                device.setAccDeviceOptionList(deviceOptionList);// 更新到缓存
            }
        }
    }

    /**
     * 更新获取的新验证方式参数
     *
     * @param optionMap:参数
     * @param accDevice:设备信息
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.model.AccDeviceVerifyMode>
     * <AUTHOR>
     * @date 2020-12-09 9:16
     * @since 1.0.0
     */
    private List<AccDeviceVerifyMode> saveDevNewVerifyMode(Map<String, String> optionMap, AccDevice accDevice) {
        List<AccDeviceVerifyMode> verifyModeList = Lists.newArrayList();
        String newVerifyMode = optionMap.get("NewVFStyles");
        // 删除当前设备的旧数据
        accDeviceVerifyModeDao.deleteByAccDevice_Sn(accDevice.getSn());
        // 解析组装新验证方式
        char[] newBinaryVfStyles = AccDeviceUtil.newVerifyModeStrToBinary(newVerifyMode);
        for (int i = 0; i < newBinaryVfStyles.length; i++) {
            // 包含且支持新验证方式
            if (AccConstants.NEW_VERIFY_MODE.containsKey(i) && AccConstants.ENABLE == newBinaryVfStyles[i]) {
                String name = AccConstants.NEW_VERIFY_MODE.get(i);
                AccDeviceVerifyMode verifyMode = new AccDeviceVerifyMode((short)i, name, accDevice);
                verifyModeList.add(verifyMode);
            }
        }
        accDeviceVerifyModeDao.save(verifyModeList);
        return verifyModeList;
    }

    private AccDeviceOption saveDevOption(AccDevice device, String key, String value) {
        AccDeviceOption devOpt = accDeviceOptionDao.findByAccDevice_IdAndName(device.getId(), key);
        if (Objects.nonNull(devOpt)) { // 存在，更新对应参数值
            if (!devOpt.getValue().equals(value)) {
                devOpt.setValue(value);
                accDeviceOptionDao.save(devOpt);
            }
        } else { // 不存在则新增
            devOpt = new AccDeviceOption();
            devOpt.setAccDevice(device);
            devOpt.setName(key);
            devOpt.setValue(value);
            if (AccConstants.DEV_COMM_KEY_PARAM_LIST.contains(key)) {
                devOpt.setType(ConstUtil.DEV_COMM_PARAM);// 表示该参数为通信关键参数.
            } else if (AccConstants.DEV_FUN_KEY_PARAM_LIST.contains(key)) {
                devOpt.setType(ConstUtil.DEV_FUN_PARAM);// 表示该参数为设备扩展功能关键参数.
            } else {
                devOpt.setType(ConstUtil.DEV_GENERAL_PARAM);
            }
            accDeviceOptionDao.save(devOpt);
        }
        return devOpt;
    }

    /**
     * @param optionMap
     * @param device
     * @return
     * @Description: 新增或修改设备的事件类型表
     * <AUTHOR>
     * @date 2018/6/19 11:01
     */
    private List<AccDeviceEvent> saveDevEventType(JSONObject optionJson, Map<String, String> optionMap,
        AccDevice device) {
        List<AccDeviceEvent> accDeviceEventList = Lists.newArrayList();
        String eventHex = optionMap.get("EventTypes");
        if ("".equals(eventHex)) {
            // 直接删除并更新事件表(老固件的事件)
            accDeviceEventDao.deleteByAccDevice_Sn(device.getSn());
            Map<Integer, String> eventMap = Maps.newHashMap();
            eventMap.putAll(AccConstants.OLD_EVENT);
            if (!accDeviceOptionService.isSupportFun(device.getSn(), AccConstants.ONLY_RF_MACHINE)) {
                eventMap.putAll(AccConstants.OLD_FP_EVENT);// 指纹事件
            }
            if (device.getMachineType() == AccConstants.DEVICE_ACCESS_CONTROL) {
                eventMap.putAll(AccConstants.DEVICE_ACCESS_CONTROL_EVENT);// 一体机事件
                for (Integer key : AccConstants.DEVICE_ACCESS_CONTROL_EVENT_NO_SUPPORT.keySet()) {
                    eventMap.remove(key);// 移除一体机不支持事件
                }
            }
            for (Integer key : eventMap.keySet()) {
                short eventNo = key.shortValue();
                short level = accDeviceEventService.getDefaultEventTypeById(eventNo);
                AccDeviceEvent accEvent = new AccDeviceEvent(eventNo, eventMap.get(key), device, level);
                accDeviceEventList.add(accEvent);
            }
        } else {
            accDeviceEventDao.deleteByAccDevice_Sn(device.getSn());
            char[] binaryArry = AccDeviceUtil.getBinary(eventHex);// 十六进制转化为二进制
            for (int i = 0; i < binaryArry.length; i++) {// 遍历，判断设备是否支持这个事件类型
                if (binaryArry[i] == AccConstants.ENABLED) { // 支持这个事件类型
                    if (AccConstants.SIMPLE_EVENT.containsKey(i)) {
                        String eventName = AccConstants.SIMPLE_EVENT.get(i);
                        short level = accDeviceEventService.getDefaultEventTypeById(i);
                        AccDeviceEvent accEvent = new AccDeviceEvent((short)i,
                            "".equals(eventName) ? I18nUtil.i18nCode(AccConstants.EVENT_NO_UNDEFINED) : eventName,
                            device, level);
                        accDeviceEventList.add(accEvent);
                    }
                }
            }
        }

        // 构建需入库的新事件数据
        buildNewEventData(optionJson, device, accDeviceEventList);

        for (Integer eventNo : AccConstants.ACC_MONITOR_DEV_EVENT.keySet()) {
            AccDeviceEvent accEvent = new AccDeviceEvent(eventNo.shortValue(),
                AccConstants.ACC_MONITOR_DEV_EVENT.get(eventNo), device, AccConstants.EVENT_ALARM);
            accDeviceEventList.add(accEvent);
        }
        if (device.getCommType().equals(AccConstants.COMM_TYPE_PUSH_HTTP)) {
            for (Integer eventNo : AccConstants.CUSTOM_EVENT_NORMAL.keySet()) {// 存入全局联动事件
                AccDeviceEvent accEvent = new AccDeviceEvent(eventNo.shortValue(),
                    AccConstants.CUSTOM_EVENT_NORMAL.get(eventNo), device, AccConstants.EVENT_NORMAL);
                accDeviceEventList.add(accEvent);
            }
            for (Integer eventNo : AccConstants.ACC_ADVANCE_VALID.keySet()) {
                AccDeviceEvent accEvent = new AccDeviceEvent(eventNo.shortValue(),
                    AccConstants.ACC_ADVANCE_VALID.get(eventNo), device, AccConstants.EVENT_WARNING);
                accDeviceEventList.add(accEvent);
            }
        }
        BaseMediaFileItem baseMediaFileItem = baseMediaFileService.getItemByInitFlag();
        accDeviceEventList.stream().forEach(deviceEvent -> {// 统一设置报警事件声音
            if (AccConstants.EVENT_ALARM == deviceEvent.getEventLevel()) {
                deviceEvent.setBaseMediaFileId(baseMediaFileItem.getId());
            }
        });
        accDeviceEventDao.save(accDeviceEventList);
        return accDeviceEventList;
    }

    private List<AccDeviceVerifyMode> saveDevVerifyMode(Map<String, String> optionMap, AccDevice device) {
        List<AccDeviceVerifyMode> verifyModeList = Lists.newArrayList();
        AccDeviceOption oldDevOpt = accDeviceOptionDao.findByAccDevice_SnAndName(device.getSn(), "VerifyStyles");
        String verifyModeHex = optionMap.get("VerifyStyles");
        AccDeviceVerifyMode verifyMode = null;
        accDeviceVerifyModeDao.deleteByAccDevice_Sn(device.getSn());// 删除当前设备的旧数据
        if ("".equals(verifyModeHex)) {
            // 由高版本降到低版本
            if (oldDevOpt != null) {
                // 保存新的验证方式（老固件）
                List<Integer> tempList = new ArrayList<>();
                if (device.getMachineType() == AccConstants.DEVICE_ACCESS_CONTROL) {
                    // 一体机 TF1700老
                    tempList.addAll(AccConstants.OLD_FP_VERIFY_MODE_ACD);
                } else if (!accDeviceOptionService.isSupportFun(device.getSn(), AccConstants.ONLY_RF_MACHINE)) {
                    // inbio控制器-指纹设备
                    tempList.addAll(AccConstants.OLD_FP_VERIFY_MODE_ACP);
                } else {
                    tempList.addAll(AccConstants.OLD_VERIFY_MODE);// c3等
                }
                for (Integer mode : tempList) {
                    if (AccConstants.VERIFY_MODE.containsKey(mode)) {
                        verifyMode =
                            new AccDeviceVerifyMode(mode.shortValue(), AccConstants.VERIFY_MODE.get(mode), device);
                        verifyModeList.add(verifyMode);
                    }
                }
                accDeviceVerifyModeDao.save(verifyModeList);
            }
        } else {// 由低版本升到高版本，或新固件的高低版本替换
            // 保存新（新固件）的验证方式
            char[] binaryArry = AccDeviceUtil.getBinary(verifyModeHex);// 十六进制转化为二进制
            for (int i = 0; i < binaryArry.length; i++) {
                if (binaryArry[i] == AccConstants.ENABLED) { // 1 代表支持这个验证方式
                    if (AccConstants.VERIFY_MODE.containsKey(i)) {
                        String name = AccConstants.VERIFY_MODE.get(i);
                        verifyMode = new AccDeviceVerifyMode((short)i,
                            "".equals(name) ? AccConstants.VERIFY_MODE_UNDEFINED : name, device);
                        verifyModeList.add(verifyMode);
                    }
                }
            }
            accDeviceVerifyModeDao.save(verifyModeList);
        }
        return verifyModeList;
    }

    /**
     * @param device
     * @param optJson
     * @return
     * @Description: 更新设备参数
     * <AUTHOR>
     * @date 2018/6/19 10:02
     */
    private AccDevice updateDevOption(AccDevice device, Map<String, String> optJson) {
        String oldDevFWVer = device.getFwVersion();// 原先保存在数据库的版本号
        if (optJson.containsKey("FirmVer") && !optJson.get("FirmVer").equals(oldDevFWVer)) {
            device.setFwVersion(optJson.get("FirmVer"));
            logger.info("FirmVer from " + oldDevFWVer + " to " + optJson.get("FirmVer"));
        }
        // 判断设备IP地址和型号有没有修改；有则通知其他模块进行修改
        boolean updateModify =
            (optJson.containsKey("IPAddress") && !optJson.get("IPAddress").equals(device.getIpAddress()))
                || (optJson.containsKey("~DeviceName") && !optJson.get("~DeviceName").equals(device.getDeviceName()));
        if (optJson.containsKey("IPAddress") && !optJson.get("IPAddress").equals(device.getIpAddress())) {
            device.setIpAddress(optJson.get("IPAddress"));
        }
        if (optJson.containsKey("GATEIPAddress") && !optJson.get("GATEIPAddress").equals(device.getGateway())) {
            device.setGateway(optJson.get("GATEIPAddress"));
        }
        if (optJson.containsKey("NetMask") && !optJson.get("NetMask").equals(device.getSubnetMask())) {
            device.setSubnetMask(optJson.get("NetMask"));
        }
        if (optJson.containsKey("MachineType")
            && !optJson.get("MachineType").equals(device.getMachineType().toString())) {
            device.setMachineType(Short.valueOf(optJson.get("MachineType")));
        }
        if (optJson.containsKey("LockCount") && !optJson.get("LockCount").equals(device.getAcpanelType().toString())) {
            device.setAcpanelType(Short.valueOf(optJson.get("LockCount")));
        }
        if (optJson.containsKey("~DeviceName") && !optJson.get("~DeviceName").equals(device.getDeviceName())) {
            device.setDeviceName(optJson.get("~DeviceName"));
        }
        if (optJson.containsKey("ComPwd") && !optJson.get("ComPwd").equals(device.getCommPwd())) {
            device.setCommPwd(optJson.get("ComPwd"));
        }
        // 有更新
        if (updateModify) {
            // 通知ivs模块更新可视对讲设备ip（判断是否可视对讲设备，以及是否有ivs模块）
            String visualIntercomFunOn = optJson.get("VisualIntercomFunOn");
            if (StringUtils.isNotBlank(visualIntercomFunOn)
                && AccConstants.DISABLE_OPTION != Short.valueOf(visualIntercomFunOn)
                && Objects.nonNull(acc4IvsDeviceService)) {
                acc4IvsDeviceService.updateIvsDeviceIp(device.getSn(), optJson.get("IPAddress"));
            }
            //有更新则通知其他模块更新设备信息
            updateThirdDev(device);
        }

        return device;
    }

    @Override
    public Pager loadPagerByAuthFilter(String sessionId, AccDeviceItem condition, int pageNo, int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        return getDevItemsByPage(condition, pageNo, pageSize);
    }

    /**
     * 使用前端分页
     * 
     * @param condition:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @throws @date 2023-03-31 15:34
     * @since 1.0.0
     */
    private Pager getDevItemsByPage(AccDeviceItem condition, int pageNo, int pageSize) {
        String connectState = condition.getConnectState();
        // 离线状态设备
        Set<String> offlineSns = new HashSet<>();
        // 在线状态设备
        Set<String> onLineSns = new HashSet<>();
        // 查询禁用设备
        if (StringUtils.isNotBlank(connectState) && connectState.equals(AccConstants.DEV_STATE_DISABLE + "")) {
            condition.setEnabled(false);
        } else {
            // 不带条件查询或者查询的是非禁用设备
            // 获取redis缓存中的门禁设备信息keys,从而获取所有设备信息
            Map<Short, Set<String>> snsMap = getDevSnsByRedis();
            offlineSns = snsMap.get(AccConstants.DEV_STATE_OFFLINE);
            onLineSns = snsMap.get(AccConstants.DEV_STATE_ONLINE);
            // 添加查询状态条件
            if (StringUtils.isNotBlank(connectState)) {
                condition.setEnabled(true);
                if (connectState.equals(AccConstants.DEV_STATE_ONLINE + "")) {
                    condition.setSnsNotIn(StringUtils.join(offlineSns, ","));
                } else {
                    condition.setSns(StringUtils.join(offlineSns, ","));
                }
            }
        }

        Pager pager =
            accDeviceDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        List<AccDeviceItem> items = (List<AccDeviceItem>)pager.getData();
        for (AccDeviceItem item : items) {
            if (item.getEnabled()) {
                if (offlineSns.size() > 0 && offlineSns.contains(item.getSn())) {
                    item.setConnectState("0");
                } else if (onLineSns.size() > 0 && onLineSns.contains(item.getSn())) {
                    item.setConnectState("1");
                }
            } else {
                item.setConnectState("2");
            }
            item.setCmdCount(getCmdCount(item.getSn()));
            item.setNetConnectMode(accDeviceOptionService.getValueByNameAndDevSn(item.getSn(), "NetConnectMode"));
            // 设置rs485参数数据
            if (Objects.nonNull(item.getComPort()) && Objects.nonNull(item.getComAddress())
                && Objects.nonNull(item.getBaudrate())) {
                item.setRs485Param(
                    String.format("COM%s(%s) %s", item.getComPort(), item.getComAddress(), item.getBaudrate()));
            }
        }
        return pager;
    }

    @Override
    public Map<Short, Set<String>> getDevSnsByRedis() {
        // 离线状态设备
        Set<String> offlineSns = new HashSet<>();
        // 在线状态设备
        Set<String> onLineSns = new HashSet<>();
        List<String> snList = accDeviceDao.getAllSn();

        Set<String> onLineKeys = accCacheManager.getCacheKeySet(AccCacheKeyConstants.ADMS_DEV_HEARTBEAT + "*");
        if (onLineKeys.size() > 0) {
            for (String key : onLineKeys) {
                String sn = key.split(":")[2];
                onLineSns.add(sn);
            }
        }
        if (snList.size() > 0) {
            offlineSns.addAll(snList);
            // 去掉在线的设备，其余就是离线的
            offlineSns.removeAll(onLineSns);
        }
        if (offlineSns.isEmpty()) {
            offlineSns.add("-1");
        }
        Map<Short, Set<String>> devSns = new HashMap<>();
        devSns.put(AccConstants.DEV_STATE_ONLINE, onLineSns);
        devSns.put(AccConstants.DEV_STATE_OFFLINE, offlineSns);
        return devSns;
    }

    @Override
    public List<SelectItem> getSupportWiegandFmtDevices() {
        List<SelectItem> selectItemList = new ArrayList<SelectItem>();
        List<AccDevice> accDeviceList = accDeviceDao.findAll();
        List<String> deviceSns = (List<String>)CollectionUtil.getPropertyList(accDeviceList, AccDevice::getSn, null);
        if (deviceSns != null) {
            List<AccDeviceOption> accDeviceOptionList =
                accDeviceOptionDao.findByAccDevice_SnInAndName(deviceSns, "AccSupportFunList");
            Map<AccDevice, AccDeviceOption> deviceOptionMap =
                CollectionUtil.listToKeyMap(accDeviceOptionList, AccDeviceOption::getAccDevice);
            SelectItem selectItem = null;
            AccDeviceOption accDeviceOption = null;
            for (AccDevice accDevice : accDeviceList) {
                if (deviceOptionMap.containsKey(accDevice)) {
                    accDeviceOption = deviceOptionMap.get(accDevice);
                    String val = accDeviceOption.getValue();
                    boolean supportFun = accDeviceOptionService.getAccSupportFunListVal(10, val);
                    if (supportFun) {
                        selectItem = new SelectItem();
                        selectItem.setValue(accDevice.getId());
                        selectItem.setText(accDevice.getAlias());
                        selectItemList.add(selectItem);
                    }
                }
            }
        }
        return selectItemList;
    }

    @Override
    public List<AccDeviceEventItem> getDevEventBySn(String sn) {
        List<AccDeviceEventItem> accDeviceEventItemList = accCacheManager.getDeviceEvent(sn);
        if (ObjectUtils.isEmpty(accDeviceEventItemList) && StringUtils.isNotBlank(sn)) {
            List<AccDeviceEvent> eventList = accDeviceEventDao.findByAccDevice_Sn(sn);
            accDeviceEventItemList = ModelUtil.copyListProperties(eventList, AccDeviceEventItem.class);
            accCacheManager.putDeviceEvent2Cache(sn, accDeviceEventItemList);
        }
        return accDeviceEventItemList;
    }

    @Override
    public void clearCmdCacheByIds(String ids) {
        List<String> sns = accDeviceDao.getSnByIdIn(StrUtil.strToList(ids));
        clearCmdCache(StringUtils.join(sns, ","), "-1112");
    }

    @Override
    public ZKResultMsg validIvsUerInfo(String userName, String userPassword, String ipAddress) {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        if (Objects.nonNull(acc4IvsDeviceService)) {
            Acc4NVRDeviceItem acc4NVRDeviceItem = new Acc4NVRDeviceItem();
            acc4NVRDeviceItem.setIp(ipAddress);
            acc4NVRDeviceItem.setPort("8081");
            // ZKNVR
            acc4NVRDeviceItem.setType("7");
            acc4NVRDeviceItem.setName(userName);
            acc4NVRDeviceItem.setPassword(userPassword);
            return acc4IvsDeviceService.loginCheck(acc4NVRDeviceItem);
        }
        return resultMsg;
    }

    @Override
    public boolean isBestDevBySn(String sn) {
        Short commType = null;
        if (commTypeMap.containsKey(sn)) {
            commType = commTypeMap.get(sn);
        } else {
            AccQueryDeviceItem item = getQueryItemBySn(sn);
            if (item != null) {
                commTypeMap.put(sn, item.getCommType());
                commType = item.getCommType();
            }
        }
        if (commType != null) {
            return AccConstants.COMM_TYPE_BEST_MQTT == commType || AccConstants.COMM_TYPE_BEST_WS == commType;
        }
        return false;
    }

    @Override
    public boolean setBestDevice(String deviceJsonStr) {
        JSONObject devInfo = JSONObject.parseObject(deviceJsonStr);
        AccSearchAddDeviceItem tempInfo = accCacheManager.getTempDevFromCache(devInfo.getString("SN"));
        String ret = "";
        boolean dealResult = false;
        ret = updateDevInfo(devInfo, tempInfo);
        if (!"noLicense".equals(ret)) {
            dealResult = true;
            // 设备添加命令下发
            AccDevice accDevice = accDeviceDao.findBySn(devInfo.getString("SN"));
            // 含有ParentSN 是属于授权进来的，默认也清除用户数据
            if ((tempInfo != null && (String.valueOf(AccConstants.ENABLE)).equals(tempInfo.getClearAllData()))) {
                clearAllDataFromDev(accDevice); // 清空设备数据
            }
            if ("add".equals(ret)) {
                initDevConfig(accDevice);// 初始化设备中表和参数设置
                if (Objects.nonNull(tempInfo)) {
                    if (StringUtils.isNotBlank(tempInfo.getLevelId())) {
                        List<AccDoor> doorList = accDoorDao.findByDevice_Id(accDevice.getId());
                        List<String> doorIdList = (List<String>)CollectionUtil.getModelIdsList(doorList);
                        if (doorIdList.size() > 0) {
                            AccLevel accLevel = accLevelDao.findById(tempInfo.getLevelId()).orElse(null);
                            if (accLevel != null) {
                                String doorIds = String.join(",", doorIdList);
                                accLevelService.addLevelByParamIds(doorIds, accLevel.getId(), "door");
                                accLevelService.immeAddLevelDoor(accLevel.getId(), doorIdList, true);
                            }
                        }
                    }
                    accCacheManager.delTempDevFromCache(tempInfo.getSn());
                }
            }
        }
        return dealResult;
    }

    @Override
    public ZKResultMsg validPushDevCount(String sn, String machineType) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        String ret = "ok";
        String msg = "";
        Integer dataCount = null;
        if (StringUtils.isBlank(machineType)
            || StringUtils.isNotBlank(machineType) && Short.parseShort(machineType) != AccConstants.DEVICE_BIOIR_9000) {
            logger.info("----------------0=hy-1=gn----------LicenseCheckUtil.getLanguagePack():"
                + baseLicenseProvider.getLanguagePack() + " ==========map:" + getBeforeLicensedCount());
            if (accLicensePointsCheckService.isZKBioCVV6000Server()) {
                ZKResultMsg checkRet = accLicensePointsCheckService.check();
                if (!checkRet.isSuccess()) {
                    ret = "noLicense";
                    msg = checkRet.getMsg();
                }
            } else {
                Map<String, Integer> countMap = getAfterLicensedCount(1, 1, AccConstants.COMM_TYPE_PUSH_HTTP);
                ResultCode resultCode =
                    baseLicenseProvider.isCountOutRangeAcc(ConstUtil.LICENSE_MODULE_ACC_PUSH, countMap);
                Map<String, Integer> beforeCountMap = getBeforeLicensedCount();
                int count = beforeCountMap.get("pushGateCount") + beforeCountMap.get("pullGateCount");// 许可为push、pull总和
                if (ResultCode.SUCCESS == resultCode) {
                    ret = "ok";
                    msg = "acc_dev_rebootAfterOperate";
                    if (baseLicenseProvider.getLanguagePack() != 1
                        && baseLicenseProvider.getDeviceCount(ConstUtil.LICENSE_MODULE_ACC_PUSH) - count < 4) {
                        // 您当前许可点数只剩XX点，确认后超过点数的门将会自动禁用，是否继续操作?
                        ret = "message";
                        msg = "acc_dev_maybeDisabled";
                        dataCount = baseLicenseProvider.getDeviceCount(ConstUtil.LICENSE_MODULE_ACC_PUSH)
                            - (beforeCountMap.get("pushGateCount") + beforeCountMap.get("pullGateCount"));
                    }
                } else {
                    if (ResultCode.OVER == resultCode) {

                        if (baseLicenseProvider.getLanguagePack() == 1)// 国内授权(中文、繁体)
                        {
                            // 系统中已存在%s台设备，达到许可上限，无法添加设备！
                            msg = "acc_device_pushMaxCount";
                            dataCount = beforeCountMap.get("pushDevCount");
                            logger.info("-------pushDev_acc_gn" + sn + " add failed for license, and licenseCount is "
                                + baseLicenseProvider.getDeviceCount(ConstUtil.LICENSE_MODULE_ACC_PUSH)
                                + ", current pushDevCount is " + beforeCountMap.get("pushDevCount"));
                        } else
                        // 海外授权(外文)
                        {
                            // 系统中已存在%s个门，当前许可数量已达到许可上限，请先授权!
                            msg = "acc_door_pushMaxCount";
                            dataCount = beforeCountMap.get("pushGateCount") + beforeCountMap.get("pullGateCount");
                            logger.info("-------pullAndPushGateCount_acc_hw" + sn
                                + " add failed for license, and licenseCount is "
                                + baseLicenseProvider.getDeviceCount(ConstUtil.LICENSE_MODULE_ACC_PUSH)
                                + ", current pullAndPushGateCount is "
                                + (beforeCountMap.get("pullGateCount") + beforeCountMap.get("pushGateCount")));
                        }
                    } else if (ResultCode.TRIALEXPIRED == resultCode) {
                        msg = "common_license_trialExpired";
                    } else if (ResultCode.EXPIRED == resultCode) {
                        msg = "common_license_expired";
                    } else if (ResultCode.UNAUTH == resultCode) {
                        msg = "auth_license_noLicense";
                    } else {
                        msg = "auth_license_checkError";
                    }
                    ret = "noLicense";
                }
            }
        }
        resultMsg.setRet(ret);
        resultMsg.setMsg(msg);
        resultMsg.setI18nArgs(dataCount);
        return resultMsg;
    }

    @Override
    public ZKResultMsg validChildDevCount(int devCount, int doorCount) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        String retStr = "ok";
        String msgStr = "common_op_succeed";
        Integer dataCount = null;
        if (accLicensePointsCheckService.isZKBioCVV6000Server()) {
            ZKResultMsg checkRet = accLicensePointsCheckService.check();
            if (!checkRet.isSuccess()) {
                retStr = "noLicense";
                msgStr = checkRet.getMsg();
            }
        } else {
            Map<String, Integer> countMap =
                getAfterLicensedCount(devCount, doorCount, AccConstants.COMM_TYPE_PUSH_HTTP);
            ResultCode resultCode = baseLicenseProvider.isCountOutRangeAcc(ConstUtil.LICENSE_MODULE_ACC_PUSH, countMap);
            Map<String, Integer> beforeCountMap = getBeforeLicensedCount();
            if (ResultCode.SUCCESS == resultCode) {
                int count = beforeCountMap.get("pushGateCount") + beforeCountMap.get("pullGateCount");// 许可为push、pull总和
                if (baseLicenseProvider.getLanguagePack() != 1
                    && baseLicenseProvider.getDeviceCount(ConstUtil.LICENSE_MODULE_ACC_PUSH) - count < 4) {
                    // 您当前许可点数只剩XX点，确认后超过点数的门将会自动禁用，是否继续操作?
                    retStr = "message";
                    msgStr = "acc_dev_maybeDisabled";
                    dataCount = baseLicenseProvider.getDeviceCount(ConstUtil.LICENSE_MODULE_ACC_PUSH) - count;
                }
            } else {
                if (ResultCode.OVER == resultCode) {
                    if (baseLicenseProvider.getLanguagePack() == 1) {// 国内授权(中文、繁体)
                        // 系统中已存在%s台设备，达到许可上限，无法添加设备！
                        msgStr = "acc_device_pushMaxCount";
                        dataCount = beforeCountMap.get("pushDevCount");
                    } else {// 海外授权(外文)
                        // 系统中已存在%s个门，当前许可数量已达到许可上限，请先授权!
                        msgStr = "acc_door_pushMaxCount";
                        dataCount = beforeCountMap.get("pushGateCount") + beforeCountMap.get("pullGateCount");
                    }
                }
                retStr = "noLicense";
            }
        }
        resultMsg.setRet(retStr);
        resultMsg.setMsg(msgStr);
        resultMsg.setI18nArgs(dataCount);
        return resultMsg;
    }

    @Override
    public Map<String, String> getPersonInfoFromDev(String devId, String dataType) {
        AccDevice dev = accDeviceDao.findById(devId).orElse(null);
        List<String> cmdIdList = Lists.newArrayList();
        Map<String, String> dataMap = Maps.newHashMap();
        if (Objects.nonNull(dev)) {
            if (dataType.equals("9") || dataType.equals("10")) {
                // 可见光人脸、可见光手掌支持获取比对照片
                Long cmdId = accDevCmdManager.getPersonBioPhotoFromDev(dev.getSn(), Short.valueOf(dataType), true);
                if (cmdId != null && cmdId > 0) {
                    cmdIdList.add(String.valueOf(cmdId));
                }
                cmdId = accDevCmdManager.getPersonBioTemplateFromDev(dev.getSn(), Short.valueOf(dataType), true);
                if (cmdId != null && cmdId > 0) {
                    cmdIdList.add(String.valueOf(cmdId));
                }
            } else {
                Long cmdId = accDevCmdManager.getPersonBioTemplateFromDev(dev.getSn(), Short.valueOf(dataType), true);
                if (cmdId != null && cmdId > 0) {
                    cmdIdList.add(String.valueOf(cmdId));
                }
            }
            dataMap.put("cmdIds", cmdIdList.size() > 0 ? StringUtils.join(cmdIdList, ",") : "");
            dataMap.put("devName", dev.getAlias());
        }
        return dataMap;
    }

    @Override
    public String getAreaIdsByAuthFilter(String sessionId) {
        return authSessionProvider.getAreaIdsNoSubNodesByAuthFilter(sessionId);
    }

    @Override
    public void dealAccountDataFromRedis(List<String> cmdIdList) {
        String sn = null;
        Integer result = null;
        for (String cmdId : cmdIdList) {
            Map<String, String> resultMap = getCmdResultById(Long.valueOf(cmdId), 20);
            result = Integer.valueOf(resultMap.get("result"));
            sn = resultMap.get("sn");
            if (result != null && result > 0) {
                boolean isExistData = false;
                String key = String.format(AccCacheKeyConstants.ACCOUNT_DATA_ACC, sn, cmdId);
                while (accCacheManager.exists(key)) {
                    JSONObject accountJson = accCacheManager.getAccountData(sn, cmdId);// 取出redis中设备上传查询数据
                    isExistData = true;
                    if (Objects.nonNull(accountJson)) {
                        // 区分控制器和一体机的account指令，主要区别在于一体机是一次就一个完整包（可能一个包太大），控制的分包文件上传（考虑大数据）
                        // admsPushDevInfo#dealDevAccountData|dealDevQueryData add by max ********
                        int accountCmdType = accountJson.getIntValue("accountCmdType");// 1为一体机的account 2为控制器新扩展的account
                        // 1.处理获取到的数据
                        String table = accountJson.getString("table");
                        String devSn = accountJson.getString("sn");
                        // 文件数
                        int fileCount = accountJson.getIntValue("filecount");
                        // 总条数
                        int rowCount = accountJson.getIntValue("rowcount");
                        // 文件序号
                        int fileSeq = accountJson.getIntValue("fileseq");
                        // 当前文件包条数
                        int count = accountJson.getIntValue("count");
                        String data = accountJson.getString("data");
                        if (1 == accountCmdType) {
                            // 一体机 1.获取文件内容数据
                            if (StringUtils.isEmpty(data)) {
                                accountJson.put("data", data);
                                accountJson.put("packCnt", "1");// 包的总个数
                                accountJson.put("packIdx", "1");// 当前包序号
                                accountJson.put("commType", ConstUtil.COMM_HTTP);// 设备通信方式
                                AccDevCQDataBean devCQDataBean = new AccDevCQDataBean(accountJson);
                                accDealDevCQDataOperate.dealDataByQueryData(devCQDataBean);
                            } else {
                                // 将文件按1万一批的数量切割成多个文件再处理
                                // List<String> filePathList = AccDataUtil.decodeStrAndGenerateTxt(data, devSn, table,
                                // ConstUtil.SYSTEM_MODULE_ACC);
                                // accountJson.put("packCnt", filePathList.size() * fileCount);//包的总个数
                                // for (int i = 0; i < filePathList.size(); i++)
                                // {
                                // accountJson.put("data", AccDataUtil.readTxtFile(filePathList.get(i)));
                                // accountJson.put("packIdx", (i + 1) * fileSeq);//当前包序号
                                // accountJson.put("commType", ConstUtil.COMM_HTTP);//设备通信方式
                                // AccDevCQDataBean devCQDataBean = new AccDevCQDataBean(accountJson);
                                // accDealDevCQDataOperate.dealDataByQueryData(devCQDataBean);
                                // }
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public String getQueryData(String key) {
        if (accCacheManager.exists(key)) {
            return accCacheManager.getCacheValueByKey(key);
        }
        return "";
    }

    @Override
    public void delQueryData(String key) {
        accCacheManager.delete(key);
    }

    @Override
    public Map<String, String> searchWifiListFromDev(String devId) {
        AccDevice dev = accDeviceDao.findById(devId).orElse(null);
        Map<String, String> dataMap = Maps.newHashMap();
        if (Objects.nonNull(dev)) {
            String devConnectState = getStatus(dev.getSn());
            if (!devConnectState.equals(String.valueOf(AccConstants.DEV_STATE_ONLINE))) {
                dataMap.put("failedReason", devConnectState);
            } else {
                long cmdId = accDevCmdManager.searchWifiListFromDev(dev.getSn(), true);
                dataMap.put("cmdId", String.valueOf(cmdId));
            }
        }
        return dataMap;
    }

    @Override
    public ZKResultMsg getWifiList(String cmdId) {
        JSONObject json = JSON.parseObject(accCacheManager.getWifiListInfo(Long.valueOf(cmdId)));
        ZKResultMsg resultMsg = new ZKResultMsg();
        if (Objects.nonNull(json) && !json.isEmpty()) {
            accCacheManager.delWifiListInfo(cmdId);
            resultMsg.setData(json.getJSONArray("data"));
        }
        return resultMsg;
    }

    @Override
    public List<AccAuthorizeChildDevItem> getAuthorizeChildDevList(AccAuthorizeChildDevItem accAuthorizeChildDevItem) {
        List<AccAuthorizeChildDevItem> devList = Lists.newArrayList();
        long cmdId = accAuthorizeChildDevItem.getCmdId();
        if (cmdId > 0) {
            Map<String, String> resultMap = getCmdResultById(cmdId, 20);
            if (Objects.nonNull(resultMap)) {
                int ret = Integer.parseInt(resultMap.get("result"));
                if (ret >= 0) {
                    String sn = resultMap.get("sn");
                    String devData = accCacheManager.getDeviceAuthorizeInfo(sn);
                    if (StringUtils.isNotBlank(devData)) {
                        JSONObject json = JSON.parseObject(devData);
                        JSONArray jsonArr = json.getJSONArray("data");
                        if (jsonArr.size() > 0) {
                            List<String> sns = Lists.newArrayList();
                            List<AccAuthorizeChildDevItem> tempList = Lists.newArrayList();
                            for (int i = 0; i < jsonArr.size(); i++) {
                                JSONObject devJson = jsonArr.getJSONObject(i);
                                if (devJson.getIntValue("DevState") == ConstUtil.DEV_STATE_ONLINE) {
                                    sns.add(devJson.getString("SN"));
                                    AccAuthorizeChildDevItem dev = new AccAuthorizeChildDevItem();
                                    dev.setId(devJson.getString("SN"));
                                    dev.setMac(devJson.getString("MAC"));
                                    dev.setIpAddress(devJson.getString("IPAddress"));
                                    dev.setDevSn(devJson.getString("SN"));
                                    dev.setDevName(devJson.getString("~DeviceName"));
                                    dev.setDevType(devJson.getString("DeviceType"));
                                    dev.setGateway(devJson.getString("GATEIPAddress"));
                                    dev.setSubnetMask(devJson.getString("NetMask"));
                                    dev.setVer(devJson.getString("FirmVer"));
                                    tempList.add(dev);
                                    // arr.put(devJson);
                                }
                            }
                            // 以下是过滤一下已添加进来的设备。不显示到前端
                            if (!sns.isEmpty()) {
                                List<AccDevice> accDeviceList = accDeviceDao.findBySnIn(sns);
                                List<String> snList =
                                    (List<String>)CollectionUtil.getPropertyList(accDeviceList, AccDevice::getSn, "-1");
                                AdmsAuthDeviceItem admsAuthDevice = null;
                                for (AccAuthorizeChildDevItem childDevItem : tempList) {
                                    if (!snList.contains(childDevItem.getDevSn())) {
                                        admsAuthDevice = buildAdmsAuthDevice(childDevItem);
                                        admsAuthDeviceService.setAuthDeviceInfo(admsAuthDevice);// 将待授权子设备保存到adms中
                                        devList.add(childDevItem);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return devList;
    }

    private AdmsAuthDeviceItem buildAdmsAuthDevice(AccAuthorizeChildDevItem childDevItem) {
        AdmsAuthDeviceItem admsAuthDevice = new AdmsAuthDeviceItem();
        admsAuthDevice.setDeviceType(childDevItem.getDevType());
        admsAuthDevice.setDeviceName(childDevItem.getDevName());
        admsAuthDevice.setSn(childDevItem.getDevSn());
        admsAuthDevice.setVer(childDevItem.getVer());
        admsAuthDevice.setAuthFlag(false);
        admsAuthDevice.setSubnetMask(childDevItem.getSubnetMask());
        admsAuthDevice.setGateway(childDevItem.getGateway());
        admsAuthDevice.setMacAddress(childDevItem.getMac());
        admsAuthDevice.setIp(childDevItem.getIpAddress());
        admsAuthDevice.setProtype("push");
        return admsAuthDevice;
    }

    private AdmsAuthDeviceItem buildAdmsAuthDevice(AccDevice dev) {
        AdmsAuthDeviceItem admsAuthDevice = new AdmsAuthDeviceItem();
        admsAuthDevice.setDeviceType("acc");
        admsAuthDevice.setDeviceName(dev.getAlias());
        admsAuthDevice.setSn(dev.getSn());
        admsAuthDevice.setVer(dev.getFwVersion());
        admsAuthDevice.setAuthFlag(false);
        admsAuthDevice.setSubnetMask(dev.getSubnetMask());
        admsAuthDevice.setGateway(dev.getGateway());
        admsAuthDevice.setMacAddress(dev.getMacAddress());
        admsAuthDevice.setIp(dev.getIpAddress());
        admsAuthDevice.setProtype("push");
        return admsAuthDevice;
    }

    @Override
    public ZKResultMsg authorizeChildDevice(String devId, String devSns) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        AccDevice dev = accDeviceDao.findById(devId).orElse(null);
        if (Objects.nonNull(dev)) {
            String[] snArray = devSns.split(",");
            AccDeviceOption devOpt = accDeviceOptionDao.findByAccDevice_SnAndName(dev.getSn(), "MaxSubCount");
            if (Objects.isNull(devOpt)) {
                resultMsg.setRet("subEmpty");
                resultMsg.setMsg("common_commStatus_senateIsEmpty");
                return resultMsg;
            }
            Integer maxSubCount = Integer.valueOf(devOpt.getValue());
            if (dev.getChildDevices().size() + snArray.length > maxSubCount) {
                resultMsg.setRet("noLicense");
                resultMsg.setMsg("acc_dev_maxSubCount");
                resultMsg.setData(maxSubCount);
                return resultMsg;
            }
            List<String> snList = Lists.newArrayList();
            for (String sn : snArray) {
                snList.add(sn);
                accCacheManager.setAuthAccDeviceInfo(sn);
                admsAuthDeviceService.authDevice(sn);// adms授权设备
            }
            accDevCmdManager.authorizeChildDevice(dev, snList, AccConstants.DEVICE_AUTHORIZING, false);
        }
        return resultMsg;
    }

    @Override
    public ZKResultMsg checkServerConnection(String devId, String parentDevId, String serverAddress) {
        ZKResultMsg resultMsg = ZKResultMsg.failMsg();
        AccDevice accDevice = accDeviceDao.findById(devId).orElse(null);
        if (Objects.nonNull(accDevice)) {
            if (StringUtils.isNotBlank(parentDevId)) { // 父设备id不为空，判断是否可以添加子设备
                AccDevice parentDev = accDeviceDao.findById(parentDevId).orElse(null);
                AccDeviceOption devOpt = accDeviceOptionDao.findByAccDevice_SnAndName(parentDev.getSn(), "MaxSubCount");
                if (Objects.isNull(devOpt)) { // 不能为空，固件参数没传上来，不能继续执行
                    resultMsg.setMsg("common_commStatus_senateIsEmpty");
                    return resultMsg;
                }
                Integer maxSubCount = Integer.valueOf(devOpt.getValue());
                if (parentDev.getChildDevices().size() + 1 > maxSubCount) { // 判断子设备数量是否超过设备最大限制数量
                    resultMsg.setMsg("acc_dev_maxSubCount");
                    resultMsg.setData(maxSubCount);
                    return resultMsg;
                }
                AccDeviceOption devOptIp =
                    accDeviceOptionDao.findByAccDevice_SnAndName(parentDev.getSn(), "ServerCommIP");
                AccDeviceOption devOptPort =
                    accDeviceOptionDao.findByAccDevice_SnAndName(parentDev.getSn(), "ServerCommPort");
                serverAddress = devOptIp.getValue() + ":" + devOptPort.getValue();
            }
            serverAddress = serverAddress.replaceAll("http[s]?://", "");// 把http或https前缀去除
            long cmdId = accDevCmdManager.checkServerConnect(accDevice, serverAddress, true);
            resultMsg.setRet("ok");
            resultMsg.setMsg("common_op_succeed");
            resultMsg.setData(cmdId);
        }
        return resultMsg;
    }

    @Override
    public void changeParentDevice(String id) {
        AccDevice dev = accDeviceDao.findById(id).orElse(null);
        if (Objects.nonNull(dev)) {
            AccDevice parentDev = dev.getParentDevice();
            if (Objects.nonNull(parentDev)) {
                delDevice(dev);
                parentDev.getChildDevices().remove(dev);
                accDevCmdManager.delChildDevice(parentDev, Lists.newArrayList(dev.getSn()), true);
                AdmsAuthDeviceItem admsAuthDeviceItem = buildAdmsAuthDevice(dev);
                admsAuthDeviceService.setAuthDeviceInfo(admsAuthDeviceItem);// 添加adms授权设备
                admsAuthDeviceService.authDevice(admsAuthDeviceItem.getSn());// 添加设备授权
                accDeviceDao.deleteById(dev.getId());// 删除门禁设备
                putDevice2Cache(dev);
            }
        }
    }

    private void putDevice2Cache(AccDevice dev) {
        AccSearchAddDeviceItem addDeviceItem = new AccSearchAddDeviceItem();
        addDeviceItem.setAuthAreaId(dev.getAuthAreaId());
        addDeviceItem.setDevName(dev.getAlias());
        addDeviceItem.setIconType(String.valueOf(dev.getIconType()));
        addDeviceItem.setGateway(dev.getGateway());
        addDeviceItem.setSn(dev.getSn());
        addDeviceItem.setMac(dev.getMacAddress());
        addDeviceItem.setSubnetMask(dev.getSubnetMask());
        accCacheManager.putTempDevice2Cache(dev.getSn(), addDeviceItem);
    }

    @Override
    public void updateParentDevice(String id, String parentDevId) {
        AccDevice dev = accDeviceDao.findById(id).orElse(null);
        AccDevice parentDev = accDeviceDao.findById(parentDevId).orElse(null);
        if (Objects.nonNull(dev) && Objects.nonNull(parentDev)) {
            AccDevice oldParentDev = dev.getParentDevice();
            dev.setParentDevice(parentDev);
            parentDev.getChildDevices().add(dev);
            if (Objects.nonNull(oldParentDev)) {
                delDevice(dev);
                oldParentDev.getChildDevices().remove(dev);
                accDevCmdManager.delChildDevice(oldParentDev, Lists.newArrayList(dev.getSn()), true);
            }
            AdmsAuthDeviceItem admsAuthDeviceItem = buildAdmsAuthDevice(dev);
            admsAuthDeviceService.setAuthDeviceInfo(admsAuthDeviceItem);
            authorizeChildDevice(parentDevId, dev.getSn());
        }
    }

    @Override
    public String openWgFmtTestForDevice(String devId, boolean openOrClose) {
        AccDevice dev = accDeviceDao.findById(devId).orElse(null);
        if (dev != null && accDeviceOptionService.isSupportFunList(dev.getSn(), 10)) {
            accDevCmdManager.ctrlWGTest(dev.getSn(), openOrClose, true);
            return "success";
        } else {
            return "acc_dev_devNotSupportFunction";
        }
    }

    @Override
    public String getAllWgFmtTestFilterId() {
        StringBuffer idsStr = new StringBuffer();
        List<AccDevice> accDeviceList = accDeviceDao.findAll();
        for (AccDevice accDevice : accDeviceList) {
            // 不支持韦根测试的设备
            if (!accDeviceOptionService.isSupportFunList(accDevice.getSn(), 10)) {
                idsStr.append(accDevice.getId()).append(",");
            }
        }
        return idsStr.toString().equals("") ? "" : idsStr.toString().substring(0, idsStr.length() - 1);

    }

    @Override
    public void getChildDevStatus(String sn) {
        AccDevice accDevice = accDeviceDao.findBySn(sn);
        if (Objects.nonNull(accDevice) && accDevice.getChildDevices().size() > 0) {
            accDevCmdManager.getDevRTState(accDevice, true);
        }
    }

    @Override
    public Pager getSelectDeviceItemByPage(Acc4PersDeviceSelectItem condition, int page, int size) {
        Pager pager = new Pager();
        AccDeviceConditionItem accDeviceConditionItem = new AccDeviceConditionItem();
        ModelUtil.copyPropertiesIgnoreNull(condition, accDeviceConditionItem);
        List<Acc4PersDeviceItem> acc4PersDeviceItemList =
            acc4PersDeviceService.getItemsByPage(accDeviceConditionItem, page, size);
        List<Acc4PersDeviceSelectItem> itemList = new ArrayList<>();
        acc4PersDeviceItemList.forEach(item -> {
            Acc4PersDeviceSelectItem acc4PersDeviceSelectItem = new Acc4PersDeviceSelectItem();
            ModelUtil.copyPropertiesIgnoreNull(item, acc4PersDeviceSelectItem);
            itemList.add(acc4PersDeviceSelectItem);
        });
        pager.setData(itemList);
        return pager;
    }

    @Override
    public ZKResultMsg getDevInfo(AccDeviceParamItem item) {
        ZKResultMsg zkResultMsg = connectDev(item);
        if (zkResultMsg.isSuccess()) {
            String data = (String)zkResultMsg.getData();
            JSONObject optionJson = new JSONObject();
            String[] dataArray = data.split(",");
            for (String dataStr : dataArray) {
                String[] dataStrArray = dataStr.split("=");
                optionJson.put(dataStrArray[0], dataStrArray.length > 1 ? dataStrArray[1] : "");
            }
            String machineType = optionJson.getString("MachineType");
            if (StringUtils.isNotBlank(machineType)) {
                logger.info("--------------------------pull license:"
                    + baseLicenseProvider.getDeviceCount(ConstUtil.LICENSE_MODULE_ACC_PULL));
                logger.info("--------------------------c3 license:"
                    + baseLicenseProvider.isSupportFunction(ConstUtil.LICENSE_MODULE_ACC_C3));
                boolean dcpMachineFlag = false;// 是否dcp 过滤k2
                if (optionJson.containsKey("DCPMachine")
                    && (String.valueOf(AccConstants.ENABLE)).equals(optionJson.get("DCPMachine"))) {
                    dcpMachineFlag = true;
                }
                // String machineType = optionJson.getString("MachineType");
                logger.info("--------------------------SN:" + optionJson.getString("~SerialNumber")
                    + "--------------------------machineType:" + machineType);
                if (!dcpMachineFlag && ((String.valueOf(AccConstants.DEVICE_C3_100)).equals(machineType)
                    || (String.valueOf(AccConstants.DEVICE_C3_200)).equals(machineType)
                    || (String.valueOf(AccConstants.DEVICE_C3_400)).equals(machineType))) {
                    // 国内pull设备外均判断c3授权
                    ResultCode resultCode = baseLicenseProvider.isSupportFunction(ConstUtil.LICENSE_MODULE_ACC_C3);
                    logger.info("-------------add C3 dev zh_CN-------------resultCode:" + resultCode);
                    // 判断是否支持C3
                    if (ResultCode.SUCCESS == resultCode) {
                        zkResultMsg = validDevCount(zkResultMsg, optionJson);
                    } else {// 其它集中返回值状态需要和平台确认后再改
                        if (ResultCode.UNSUPPORT == resultCode) {
                            String deviceName =
                                optionJson.containsKey("~DeviceName") ? optionJson.getString("~DeviceName") : "";
                            // 无法新增该类型设备（%s）,请先授权！
                            zkResultMsg.setMsg("acc_dev_noC3LicenseTip");
                            zkResultMsg.setI18nArgs(deviceName);
                        } else if (ResultCode.TRIALEXPIRED == resultCode) {
                            zkResultMsg.setMsg("common_license_trialExpired");
                        } else if (ResultCode.EXPIRED == resultCode) {
                            zkResultMsg.setMsg("common_license_expired");
                        } else if (ResultCode.UNAUTH == resultCode) {
                            zkResultMsg.setMsg("auth_license_noLicense");
                        } else {
                            zkResultMsg.setMsg("auth_license_checkError");
                        }
                        zkResultMsg.setRet("noLicense");
                    }
                } else {
                    zkResultMsg = validDevCount(zkResultMsg, optionJson);
                }
            } else {
                zkResultMsg.setRet("paramError");
                zkResultMsg.setMsg("acc_dev_addError");
                zkResultMsg.setI18nArgs("MachineType");
            }
        }
        return zkResultMsg;
    }

    private ZKResultMsg connectDev(AccDeviceParamItem item) {
        AdmsDeviceItem admsDeviceItem = new AdmsDeviceItem();
        ModelUtil.copyProperties(item, admsDeviceItem);
        admsDeviceItem.setCommPwd(StringUtils.isNotBlank(item.getCommPwd()) ? Base64Util.getFromBase64(item.getCommPwd()) : "");
        ZKResultMsg zkResultMsg = admsDeviceService.getDevInfo(admsDeviceItem);
        if (zkResultMsg.isSuccess()) {
            String data = (String)zkResultMsg.getData();
            JSONObject optionJson = new JSONObject();
            String[] dataArray = data.split(",");
            for (String dataStr : dataArray) {
                String[] dataStrArray = dataStr.split("=");
                optionJson.put(dataStrArray[0], dataStrArray.length > 1 ? dataStrArray[1] : "");
            }
            String machineType = optionJson.getString("MachineType");
            if (StringUtils.isNotBlank(machineType)) {
                // 判断是否要进行四门转两门的参数下发
                boolean sendFourToTwo = false;
                if ((String.valueOf(AccConstants.DEVICE_C3_460).equals(machineType)
                    || String.valueOf(AccConstants.DEVICE_C3_400).equals(machineType))
                    && "4".equals(item.getAcpanelType()) && "1".equals(item.getFourToTwo())) {
                    sendFourToTwo = true;
                } else if (String.valueOf(AccConstants.DEVICE_C3_400_TO_200).equals(machineType)
                    && "4".equals(item.getAcpanelType()) && "0".equals(item.getFourToTwo())) {
                    sendFourToTwo = true;
                }
                if (sendFourToTwo) {
                    zkResultMsg = admsDeviceService.setDoor4ToDoor2(admsDeviceItem, item.getFourToTwo());
                    // 四门转两门参数设置成功后，需要重新连接设备获取参数
                    if (zkResultMsg.isSuccess()) {
                        zkResultMsg = admsDeviceService.getDevInfo(admsDeviceItem);
                    }
                }
            }
        }
        if (!zkResultMsg.isSuccess()) {
            zkResultMsg.setMsg(accBaseDictionaryService.getCommReason(Integer.parseInt(zkResultMsg.getRet())));
        }
        return zkResultMsg;
    }

    /**
     * 验证许可数量
     *
     * @param optionJson
     */
    private ZKResultMsg validDevCount(ZKResultMsg zkResultMsg, JSONObject optionJson) {
        logger.info("----------------0=hy-1=gn----------LicenseCheckUtil.getLanguagePack():"
            + baseLicenseProvider.getLanguagePack() + " ==========map:" + getBeforeLicensedCount());
        ResultCode resultCode = null;
        ResultCode resultDoorCode = null;
        ResultCode resultDeviceCode = null;
        if (accLicensePointsCheckService.isZKBioCVV6000Server()) {
            ZKResultMsg checkRet = accLicensePointsCheckService.check();
            if (!checkRet.isSuccess()) {
                zkResultMsg.setRet("noLicense");
                zkResultMsg.setMsg(checkRet.getMsg());
            }
        } else {
            if (baseLicenseProvider.getLanguagePack() != 1) {
                // 判断是否还有许可点数，可以添加门
                Map<String, Integer> countMap = getAfterLicensedCount(1, 1, AccConstants.COMM_TYPE_PUSH_HTTP);
                resultCode = baseLicenseProvider.isCountOutRangeAcc(ConstUtil.LICENSE_MODULE_ACC_PUSH, countMap);
                // 判断设备中的门全部添加后，会不会超过许可点数
                Map<String, Integer> countDoorMap = getAfterLicensedCount(1,
                    Integer.parseInt(optionJson.getString("LockCount")), AccConstants.COMM_TYPE_PUSH_HTTP);
                resultDoorCode =
                    baseLicenseProvider.isCountOutRangeAcc(ConstUtil.LICENSE_MODULE_ACC_PUSH, countDoorMap);
                // 判断pull的设备许可数，是否超过许可
                Map<String, Integer> countDeviceMap = getAfterLicensedCount(1,
                    Integer.parseInt(optionJson.getString("LockCount")), AccConstants.COMM_TYPE_PULL_TCPIP);
                resultDeviceCode =
                    baseLicenseProvider.isCountOutRangeAcc(ConstUtil.LICENSE_MODULE_ACC_PULL, countDeviceMap);
            } else {
                Map<String, Integer> countMap = getAfterLicensedCount(1,
                    Integer.parseInt(optionJson.getString("LockCount")), AccConstants.COMM_TYPE_PULL_TCPIP);
                resultCode = baseLicenseProvider.isCountOutRangeAcc(ConstUtil.LICENSE_MODULE_ACC_PULL, countMap);
            }
            String sn = optionJson.getString("~SerialNumber");
            if (ResultCode.SUCCESS == resultCode) {// 还有剩余点数可以添加门
                Map<String, Integer> beforeCountMap = getBeforeLicensedCount();
                // pull设备数超过许可
                // 修改在试用期过后激活许可没有对可以添加pull设备的问题。原来的对over的判断在试用期过后无法判断 by dyl
                if (baseLicenseProvider.getLanguagePack() != 1 && ResultCode.SUCCESS != resultDeviceCode) {
                    zkResultMsg.setRet("deviceLimit");
                    if (baseLicenseProvider.getDeviceCount(ConstUtil.LICENSE_MODULE_ACC_PULL) > 0) {
                        // 系统里已经存在%s台PULL设备，不允许继续添加！要继续当前操作，请联系销售人员！ modified by dyl
                        zkResultMsg.setMsg("acc_dev_notContinueAddPullDevice");
                        zkResultMsg.setI18nArgs(beforeCountMap.get("pullDevCount"));
                    } else {
                        // 当前无法添加pull设备 modified by yx
                        zkResultMsg.setMsg("acc_dev_cannotAddPullDevice");
                    }
                } else if (baseLicenseProvider.getLanguagePack() != 1 && ResultCode.OVER == resultDoorCode) {// 如果全部添加，海外门数超过许可
                    zkResultMsg.setRet("message");
                    // 您当前许可点数只剩XX点，确认后超过点数的门将会自动禁用，是否继续操作 modified by yx
                    zkResultMsg.setMsg("acc_dev_maybeDisabled");
                    zkResultMsg.setI18nArgs(baseLicenseProvider.getDeviceCount(ConstUtil.LICENSE_MODULE_ACC_PUSH)
                        - (beforeCountMap.get("pushGateCount") + beforeCountMap.get("pullGateCount")));
                }
            } else {
                if (ResultCode.OVER == resultCode) {
                    Map<String, Integer> beforeCountMap = getBeforeLicensedCount();
                    if (baseLicenseProvider.getLanguagePack() == 1) {// 国内授权(中文、繁体)
                        if (beforeCountMap.get("pullDevCount") > 0) {
                            // 系统中已存在{0}台设备，达到许可上限，请先授权！
                            zkResultMsg.setMsg("common_dev_maxCount");
                            zkResultMsg.setI18nArgs(beforeCountMap.get("pullDevCount"));
                        } else {
                            // 不允许添加pull设备
                            zkResultMsg.setMsg("acc_dev_cannotAddPullDevice");
                        }
                        logger.info("-------pullDev_acc_gn" + optionJson.getString("~SerialNumber")
                            + " add failed for license, and licenseCount is "
                            + baseLicenseProvider.getDeviceCount(ConstUtil.LICENSE_MODULE_ACC_PULL)
                            + ", current pullDevCount is " + beforeCountMap.get("pullDevCount"));
                    } else {// 海外授权(外文)
                        // 系统中已存在%s个门，当前许可数量已达到许可上限，请先授权!
                        zkResultMsg.setMsg("acc_door_pushMaxCount");
                        zkResultMsg
                            .setI18nArgs(beforeCountMap.get("pushGateCount") + beforeCountMap.get("pullGateCount"));
                        logger.info("-------pullDoor_acc_hw" + optionJson.getString("~SerialNumber")
                            + " add failed for license, and licenseCount is "
                            + baseLicenseProvider.getDeviceCount(ConstUtil.LICENSE_MODULE_ACC_PULL)
                            + ", current pullDeviceCount is "
                            + (beforeCountMap.get("pullDevCount") + beforeCountMap.get("pushGateCount")));
                    }
                } else if (ResultCode.TRIALEXPIRED == resultCode) {
                    zkResultMsg.setMsg("common_license_trialExpired");
                } else if (ResultCode.EXPIRED == resultCode) {
                    zkResultMsg.setMsg("common_license_expired");
                } else if (ResultCode.UNAUTH == resultCode) {
                    zkResultMsg.setMsg("auth_license_noLicense");
                } else {
                    zkResultMsg.setMsg("auth_license_checkError");
                }
                zkResultMsg.setRet("noLicense");
            }
        }
        return zkResultMsg;
    }

    @Override
    public void addPullDevice(AccDeviceItem item, String devInfo, AccSearchAddDeviceItem tempInfo) {
        // 基础版不允许添加pull设备
        if (BaseConstants.ZKBIOCV_SECURITY_FOUNDATION.equals(baseLicenseProvider.getProductCode())) {
            throw new ZKBusinessException("acc_dev_regDeviceTypeTip");
        }
        String sn = "";
        try {
            JSONObject optionJson = JSONObject.parseObject(devInfo);
            sn = optionJson.containsKey("~SerialNumber") ? optionJson.getString("~SerialNumber") : "";
            if (StringUtils.isEmpty(sn)) {
                throw new ZKBusinessException("common_dev_snNull");
            }
            // 查询数据库 sn=不能相同。pull
            AccDevice dev = accDeviceDao.findBySn(sn);
            if (dev != null) {
                throw new ZKBusinessException("common_dev_devRepeat");
            }
            optionJson.put("Alias", item.getAlias());
            optionJson.put("SN", sn);
            optionJson.put("CommType", item.getCommType());
            optionJson.put("IPAddress", item.getIpAddress());
            optionJson.put("IpPort", item.getIpPort());
            optionJson.put("CommPwd", item.getCommPwd());
            optionJson.put("ComAddress", item.getComAddress());
            optionJson.put("ComPort", item.getComPort());
            optionJson.put("Baudrate", item.getBaudrate());
            optionJson.put("BaseArea", item.getAuthAreaId());
            optionJson.put("door4ToDoor2", item.getFourToTwo() ? "1" : "0");
            optionJson.put("IconType", item.getIconType());// 图标类型
            if (optionJson.containsKey("MachineTZFunOn")) {// 包含时区参数
                optionJson.put("TimeZone", item.getTimeZone());
            }
            Map<String, String> optionMap = JSONObject.parseObject(devInfo, Map.class);
            optionMap.put("SN", sn);
            optionMap.put("CommType", item.getCommType().toString());
            optionMap.put("IPAddress", item.getIpAddress());
            optionMap.put("IpPort", Objects.nonNull(item.getIpPort()) ? item.getIpPort().toString() : "");
            optionMap.put("CommPwd", item.getCommPwd());
            optionMap.put("ComAddress", item.getComAddress());
            optionMap.put("ComPort", item.getComPort());
            optionMap.put("Baudrate", item.getBaudrate());
            optionMap.put("enabled", "true");
            optionMap.put("DeviceType", "acc");
            String results = updateDevInfo(optionJson, tempInfo);// 加设备
            if (!"noLicense".equals(results)) {
                // 设备添加命令下发
                AccDevice accDevice = accDeviceDao.findBySn(sn);
                // 含有ParentSN 是属于授权进来的，默认也清除用户数据
                if (tempInfo != null && (AccConstants.ENABLE + "").equals(tempInfo.getClearAllData())) {
                    clearAllDataFromDev(accDevice); // 清空设备数据
                }
                if ("add".equals(results)) {
                    // 添加成功后,设备才添加到pull监控
                    admsDeviceService.addDevToPull(sn, optionMap);
                    initDevConfig(accDevice);// 初始化设备中表和参数设置
                    if (Objects.nonNull(tempInfo) && StringUtils.isNotBlank(tempInfo.getLevelId())) {
                        List<AccDoor> doorList = accDoorDao.findByDevice_Id(accDevice.getId());
                        List<String> doorIdList = (List<String>)CollectionUtil.getModelIdsList(doorList);
                        if (doorIdList.size() > 0) {
                            AccLevel accLevel = accLevelDao.findById(tempInfo.getLevelId()).orElse(null);
                            if (accLevel != null) {
                                String doorIds = String.join(",", doorIdList);
                                accLevelService.addLevelByParamIds(doorIds, accLevel.getId(), "door");
                                accLevelService.immeAddLevelDoor(accLevel.getId(), doorIdList, true);
                                // List<String> persPersonIdList =
                                // accLevelPersonDao.getPersonIdByLevelId(accLevel.getId());
                                // accLevelService.addPersonLevel(accLevel.getId(), String.join(",",
                                // persPersonIdList));//下发通用权限组的人员权限到设备----add by linzj 20160114
                            }
                        }
                    }
                }
            }
        } catch (ZKBusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("exception", e);
        } finally {
            AccDevice dev = accDeviceDao.findBySn(sn);
            if (dev == null) {
                admsDeviceService.delBySn(sn);
            }
        }
    }

    @Override
    public Pager getItemByAuthFilter(String sessionId, AccSelectDeviceItem condition, int pageNo, int pageSize) {
        condition.setUserId(authUserService.getUserIdBySessionId(sessionId));
        condition.setMachineTypeNoEquals(AccConstants.DEVICE_BIOIR_9000);
        return getItemsByPage(condition, pageNo, pageSize);
    }

    @Override
    public List<AccDeviceItem> getChildDevList(String parentDevId) {
        List<AccDeviceItem> accDeviceItemList = Lists.newArrayList();
        if (StringUtils.isNotBlank(parentDevId)) {
            AccDeviceItem dev = new AccDeviceItem();
            dev.setParentDeviceId(parentDevId);
            accDeviceItemList = getByCondition(dev);
            buildDeviveInfo(accDeviceItemList);
        }
        return accDeviceItemList;
    }

    private void buildDeviveInfo(List<AccDeviceItem> accDeviceItemList) {
        accDeviceItemList.forEach(item -> {
            item.setConnectState(getStatus(item.getSn()));
            item.setCmdCount(getCmdCount(item.getSn()));
            item.setNetConnectMode(accDeviceOptionService.getValueByNameAndDevSn(item.getSn(), "NetConnectMode"));
            // 设置rs485参数数据
            if (Objects.nonNull(item.getComPort()) && Objects.nonNull(item.getComAddress())
                && Objects.nonNull(item.getBaudrate())) {
                item.setRs485Param(
                    String.format("COM%s(%s) %s", item.getComPort(), item.getComAddress(), item.getBaudrate()));
            }
        });
    }

    @Override
    public AccQueryDeviceItem getQueryItemBySn(String devSn) {
        if (StringUtils.isNotBlank(devSn)) {
            AccQueryDeviceItem queryDeviceItem = accCacheManager.getDeviceInfo(devSn);
            if (queryDeviceItem == null) {
                queryDeviceItem = new AccQueryDeviceItem();
                queryDeviceItem.setSn(devSn);
                List<AccQueryDeviceItem> queryDeviceItemList = (List<AccQueryDeviceItem>)accDeviceDao
                    .getItemsBySql(queryDeviceItem.getClass(), SQLUtil.getSqlByItem(queryDeviceItem));
                queryDeviceItem = Optional.ofNullable(queryDeviceItemList).filter(list -> !list.isEmpty())
                    .map(list -> list.get(0)).orElse(null);
                if (queryDeviceItem != null) {
                    AuthAreaItem authAreaItem = authAreaService.getItemById(queryDeviceItem.getAuthAreaId());
                    queryDeviceItem.setAuthAreaName(authAreaItem.getName());
                    fillDoorAndAuxByQueryItem(queryDeviceItem);
                    accCacheManager.putDeviceInfo(queryDeviceItem);
                }
            }
            return queryDeviceItem;
        }
        return null;
    }

    /**
     * 给AccQueryDeviceItem填充门、辅助输入、辅助输出属性
     *
     * @param item
     */
    private void fillDoorAndAuxByQueryItem(AccQueryDeviceItem item, AccDevice dev) {
        List<String> devIds = new ArrayList<>();
        devIds.add(item.getId());
        List<AccDoor> accDoorList = dev.getAccDoorList();
        if (accDoorList != null && accDoorList.size() > 0) {
            List<AccQueryDoorItem> queryDoorItems = new ArrayList<>(accDoorList.size() + 1);
            for (AccDoor accDoor : accDoorList) {
                AccQueryDoorItem queryDoor = ModelUtil.copyProperties(accDoor, new AccQueryDoorItem());
                if (accDoor.getAccReaderList() != null && accDoor.getAccReaderList().size() > 0) {
                    List<AccQueryReaderItem> queryReaderList = new ArrayList<>(accDoor.getAccReaderList().size());
                    for (AccReader reader : accDoor.getAccReaderList()) {
                        queryReaderList.add(ModelUtil.copyProperties(reader, new AccQueryReaderItem()));
                    }
                    queryDoor.setAccReaderItemList(queryReaderList);
                }
                queryDoorItems.add(queryDoor);
            }
            item.setAccDoorItemList(queryDoorItems);
        }
        List<AccAuxIn> accAuxInList = dev.getAccAuxInList();
        if (accAuxInList != null && accAuxInList.size() > 0) {
            final List<AccQueryAuxInItem> queryAuxInItems = accAuxInList.stream()
                .map(auxIn -> ModelUtil.copyProperties(auxIn, new AccQueryAuxInItem())).collect(Collectors.toList());
            item.setAccAuxInItemList(queryAuxInItems);
        }
        List<AccAuxOut> accAuxOutList = dev.getAccAuxOutList();
        if (accAuxOutList != null && accAuxOutList.size() > 0) {
            final List<AccQueryAuxOutItem> queryAuxOutItems = accAuxOutList.stream()
                .map(auxOut -> ModelUtil.copyProperties(auxOut, new AccQueryAuxOutItem())).collect(Collectors.toList());
            item.setAccAuxOutItemList(queryAuxOutItems);
        }
    }

    /**
     * 给AccQueryDeviceItem填充门、辅助输入、辅助输出属性
     *
     * @param item
     */
    private void fillDoorAndAuxByQueryItem(AccQueryDeviceItem item) {
        updateExtDeviceCache(item);
        updateDoorByQueryItem(item);
        updateAuxInByQueryItem(item);
        updateAuxOutByQueryItem(item);
    }

    /**
     * 更新QueryDeviceItem的门状态
     *
     * @param item
     */
    @Override
    public void updateDoorByQueryItem(AccQueryDeviceItem item) {
        List<String> devIds = new ArrayList<>();
        devIds.add(item.getId());
        List<AccDoorItem> accDoorItemList = accDoorService.getItemsByDevIds(devIds);
        if (accDoorItemList != null && accDoorItemList.size() > 0) {
            List<AccQueryDoorItem> queryDoorItems = new ArrayList<>();
            for (AccDoorItem doorItem : accDoorItemList) {
                AccQueryDoorItem accQueryDoorItem = new AccQueryDoorItem();
                ModelUtil.copyProperties(doorItem, accQueryDoorItem);
                List<AccReaderItem> accReaderItemList = accReaderService.getItemsByAccDoorId(doorItem.getId());
                if (!CollectionUtil.isEmpty(accReaderItemList)) {
                    accQueryDoorItem.setAccReaderItemList(
                        ModelUtil.copyListProperties(accReaderItemList, AccQueryReaderItem.class));
                }
                queryDoorItems.add(accQueryDoorItem);
            }
            item.setAccDoorItemList(queryDoorItems);
        }
    }

    /**
     * 更新QueryDeviceItem的辅助输入状态
     *
     * @param item
     */
    @Override
    public void updateAuxInByQueryItem(AccQueryDeviceItem item) {
        List<String> devIds = new ArrayList<>();
        devIds.add(item.getId());
        List<AccAuxInItem> accAuxInItemList = accAuxInService.getItemsByDevIds(devIds);
        if (accAuxInItemList != null && accAuxInItemList.size() > 0) {
            final List<AccQueryAuxInItem> queryAuxInItems =
                accAuxInItemList.stream().map(auxInItem -> ModelUtil.copyProperties(auxInItem, new AccQueryAuxInItem()))
                    .collect(Collectors.toList());
            item.setAccAuxInItemList(queryAuxInItems);
        }
    }

    /**
     * 更新QueryDeviceItem的辅助输出状态
     *
     * @param item
     */
    @Override
    public void updateAuxOutByQueryItem(AccQueryDeviceItem item) {
        List<String> devIds = new ArrayList<>();
        devIds.add(item.getId());
        List<AccAuxOutItem> accAuxOutItemList = accAuxOutService.getItemsByDevIds(devIds);
        if (accAuxOutItemList != null && accAuxOutItemList.size() > 0) {
            final List<AccQueryAuxOutItem> queryAuxOutItems = accAuxOutItemList.stream()
                .map(auxOutItem -> ModelUtil.copyProperties(auxOutItem, new AccQueryAuxOutItem()))
                .collect(Collectors.toList());
            item.setAccAuxOutItemList(queryAuxOutItems);
        }
    }

    /**
     * 缓存设备信息
     *
     * @param dev
     */
    private void putQueryItemToCache(AccDevice dev) {
        putQueryItemToCache(dev, false);
    }

    /**
     * 缓存设备信息
     *
     * @param dev
     * @param isUpdateDoorAndAux 更新缓存时是否更新门和辅助输入、辅助输出
     */
    private void putQueryItemToCache(AccDevice dev, boolean isUpdateDoorAndAux) {
        if (dev != null) {
            AccQueryDeviceItem queryDeviceItem = accCacheManager.getDeviceInfo(dev.getSn());
            boolean isUpdateArea = true;
            if (queryDeviceItem == null) {
                queryDeviceItem = new AccQueryDeviceItem();
                isUpdateArea = true;
                // 初始化时需要填充门、辅助输入、辅助输出
                isUpdateDoorAndAux = true;
            }
            // 判断是否需要更新区域信息
            if (StringUtils.isNotBlank(queryDeviceItem.getAuthAreaId())
                && queryDeviceItem.getAuthAreaId().equals(dev.getAuthAreaId())) {
                isUpdateArea = false;
            }
            ModelUtil.copyProperties(dev, queryDeviceItem);
            if (isUpdateArea) {
                AuthAreaItem authAreaItem = authAreaService.getItemById(dev.getAuthAreaId());
                queryDeviceItem.setAuthAreaName(authAreaItem.getName());
            }
            if (isUpdateDoorAndAux) {
                fillDoorAndAuxByQueryItem(queryDeviceItem, dev);
            }
            accCacheManager.putDeviceInfo(queryDeviceItem);
        }
    }

    @Override
    public List<AccQueryDeviceItem> getQueryItemsByAuth(String filterAreaIds) {
        AccQueryDeviceItem queryDeviceItem = new AccQueryDeviceItem();
        if (StringUtils.isNotBlank(filterAreaIds)) {
            queryDeviceItem.setAreaIdIn(filterAreaIds);
        }
        List<AccQueryDeviceItem> queryDeviceItems = (List<AccQueryDeviceItem>)accDeviceDao
            .getItemsBySql(queryDeviceItem.getClass(), SQLUtil.getSqlByItem(queryDeviceItem));
        if (queryDeviceItems != null && queryDeviceItems.size() > 0) {
            String areaIds = CollectionUtil.getPropertys(queryDeviceItems, AccQueryDeviceItem::getAuthAreaId);
            List<AuthAreaItem> authAreaItemList = authAreaService.getItemsByIds(areaIds);
            Map<String, AuthAreaItem> authAreaItemMap = CollectionUtil.itemListToIdMap(authAreaItemList);
            queryDeviceItems.forEach(dev -> dev.setAuthAreaName(authAreaItemMap.get(dev.getAuthAreaId()).getName()));
        }
        return queryDeviceItems;
    }

    @Override
    public List<Long> upgradeFirmware(String devIds, File file, String host, int port) {
        List<AccDevice> accDeviceList = accDeviceDao.findByIdIn((List<String>)CollectionUtil.strToList(devIds));
        List<Long> cmdIdList = Lists.newArrayList();
        for (AccDevice dev : accDeviceList) {
            try {
                cmdIdList.add(accDevCmdManager.upgradeFirmware(dev.getSn(), file, host, port, true));// 下发升级固件命令
            } catch (Exception e) {
                throw new ZKBusinessException("common_dev_upgradeFail");
            }
        }
        return cmdIdList;
    }

    @Override
    public List<Long> getOptFromDev(String sn, int cmdIndex) {
        List<Long> cmdIdList = Lists.newArrayList();
        AccDevice dev = accDeviceDao.findBySn(sn);
        String optCmd = "";
        if (cmdIndex == AccConstants.BASECMD) {
            optCmd += AccConstants.DEV_ACC_BASEOPTION;
        } else if (cmdIndex == AccConstants.FUNCMD) {
            optCmd += AccConstants.DEV_ACC_FUNOPTION;
        } else if (cmdIndex == AccConstants.EXFUNCMD) {
            if (isNewAccessControlDevice(dev.getMachineType(), dev.getDeviceName())) {
                optCmd += AccConstants.DEV_ACC_EXFUNOPTION;
            } else {
                cmdIdList.add(0L);
            }
        }
        if (StringUtils.isNotBlank(optCmd)) {
            List<String> optList = Lists.newArrayList(optCmd.split(","));
            cmdIdList.addAll(accDevCmdManager.getDeviceOption(dev, optList, true));
        }
        return cmdIdList;
    }

    @Override
    public long getDevicesCount() {
        return accDeviceDao.count();
    }

    @Override
    public List<String> getUploadCloudDevices(int page, int pageSize) {
        List<String> devInfoList = Lists.newArrayList();
        JSONObject devInfo = null;
        Pager pager = getItemsByPage(new AccDeviceItem(), page, pageSize);
        if (pager.getData() != null && pager.getData().size() > 0) {
            List<AccDeviceItem> accDeviceItems = (List<AccDeviceItem>)pager.getData();
            List<String> devIds = (List<String>)CollectionUtil.getItemIdsList(accDeviceItems);
            String areaIds = CollectionUtil.getPropertys(accDeviceItems, AccDeviceItem::getAuthAreaId);
            List<AccDoorItem> accDoorItemList = accDoorService.getItemsByDevIds(devIds);
            List<AuthAreaItem> authAreaItems = authAreaService.getItemsByIds(areaIds);
            Map<String, List<AccDoorItem>> accDoorMap =
                accDoorItemList.stream().collect(Collectors.groupingBy(AccDoorItem::getDeviceId));
            Map<String, AuthAreaItem> authAreaItemMap = CollectionUtil.itemListToIdMap(authAreaItems);
            for (AccDeviceItem dev : accDeviceItems) {
                devInfo = new JSONObject();
                AuthAreaItem area = authAreaItemMap.get(dev.getAuthAreaId());
                devInfo.put("id", dev.getId());
                devInfo.put("sn", dev.getSn());
                devInfo.put("alias", dev.getAlias());
                devInfo.put("ipAddress", dev.getIpAddress());
                devInfo.put("states", getStatus(dev.getSn()));
                devInfo.put("deviceName", dev.getDeviceName());
                devInfo.put("enabled", dev.getEnabled());
                devInfo.put("parentDevId", dev.getParentDeviceId());
                devInfo.put("authAreaName", area.getName());
                devInfo.put("authAreaCode", area.getCode());
                if (StringUtils.isNotBlank(area.getParentAreaCode())) {
                    devInfo.put("areaParentCode", area.getParentAreaCode());
                }
                if (accDoorMap.containsKey(dev.getId())) { // IR9000设备没有门
                    JSONArray doors = new JSONArray();// 组装门数据
                    List<AccDoorItem> doorList = accDoorMap.get(dev.getId());
                    doorList.forEach(accDoorItem -> {
                        JSONObject door = new JSONObject();
                        door.put("doorNo", accDoorItem.getDoorNo());
                        door.put("devSn", accDoorItem.getDeviceSn());
                        door.put("name", accDoorItem.getName());
                        door.put("verifyMode", accDoorItem.getVerifyMode());
                        door.put("enabled", accDoorItem.getEnabled());
                        doors.add(door);
                    });
                    devInfo.put("doors", doors);
                }
                devInfoList.add(devInfo.toJSONString());
            }
        }
        return devInfoList;
    }

    @Override
    public ZKResultMsg clearAdministrator(String devIds) {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        if (StringUtils.isNotBlank(devIds)) {
            List<AccDevice> accDeviceList = accDeviceDao.findByIdList(StrUtil.strToList(devIds));
            if (Objects.nonNull(accDeviceList)) {
                for (AccDevice accDevice : accDeviceList) {
                    if (!getStatus(accDevice.getSn()).equals(String.valueOf(ConstUtil.DEV_STATE_ONLINE))) {
                        resultMsg.setRet("400");
                        resultMsg.setMsg(accDevice.getAlias() + "," + I18nUtil.i18nCode("common_dev_offlinePrompt"));
                    } else {
                        // 如果没有设置权限，获取不到人员personIdList为空 ,是否要在前台提示为人员分配权限
                        List<String> personIdList = new ArrayList<String>();
                        personIdList = accLevelService.searchPersonByDev(accDevice.getId());
                        if (personIdList != null && personIdList.size() > 0) {
                            AccPersonInfoItem personInfoBean = accLevelService.getPersonByIds(personIdList);
                            List<AccPersonOptItem> sendPersonOptBeans = new ArrayList<>();
                            List<AccPersonOptItem> allPersOptBeans = personInfoBean.getPersonList();
                            for (int i = 0; i < allPersOptBeans.size(); i++) {
                                allPersOptBeans.get(i).setPrivilege((short)0);
                                sendPersonOptBeans.add(allPersOptBeans.get(i));
                                if ((i + 1) % 200 == 0) {
                                    accDevCmdManager.setPersonToDev(accDevice.getSn(), sendPersonOptBeans, false);// 下发人员信息
                                    sendPersonOptBeans = new ArrayList<>();
                                }
                            }
                            if (sendPersonOptBeans != null && sendPersonOptBeans.size() > 0) {
                                accDevCmdManager.setPersonToDev(accDevice.getSn(), sendPersonOptBeans, false);// 下发人员信息
                            }
                        }
                    }
                }
            }
        }
        return resultMsg;
    }

    @Override
    public Long setDevIOState(String devId, String devIOState) {
        AccDevice device = accDeviceDao.findById(devId).orElse(null);
        long cmdId = -1;
        if (device != null) {
            // 发送设置设备进出状态的命令到设备
            cmdId = accDevCmdManager.setDevIOState(device, Integer.parseInt(devIOState), true);
        }
        return cmdId;
    }

    @Override
    public List<String> getDevIdsByCommType(List<Short> commTypeList) {
        return accDeviceDao.findDevIdByCommTypeIn(commTypeList);
    }

    @Override
    public void handlerTransfer(List<AccDeviceItem> deviceItems) {
        // 数据量大的时候处理，分批处理，现将数据保存起来在处理主从设备
        Map<String, AccDevice> deviceAllMap = new HashMap<String, AccDevice>();
        List<List<AccDeviceItem>> deviceItemList = CollectionUtil.split(deviceItems, CollectionUtil.splitSize);
        for (List<AccDeviceItem> items : deviceItemList) {
            // 获取已存在的设备，批量获取，减少不必要的查询
            Collection<String> sns = CollectionUtil.getPropertyList(items, AccDeviceItem::getSn, "-1");
            List<AccDevice> devList = accDeviceDao.findBySnIn(new ArrayList(sns));
            Map<String, AccDevice> devMap = CollectionUtil.listToKeyMap(devList, AccDevice::getSn);
            for (AccDeviceItem devItem : items) {
                AccDevice dev = devMap.remove(devItem.getSn());
                if (Objects.isNull(dev)) {
                    dev = new AccDevice();
                }
                ModelUtil.copyPropertiesIgnoreNullWithProperties(devItem, dev, "id");
                AccDSTime dsTime = accDSTimeDao.findByName(devItem.getAccDSTimeName());
                dev.setAccDSTime(dsTime);
                AccReader accReader = accReaderDao.findByName(devItem.getReaderName());
                if (Objects.nonNull(accReader)) {
                    dev.setWgReaderId(accReader.getId());
                }
                AuthAreaItem authArea = authAreaService.getItemByCode(devItem.getAuthAreaCode());
                dev.setAuthAreaId(authArea != null ? authArea.getId() : null);
                // 通过BusinessId查询出设备 不为空则存在该设备 做更新操作 没有设置id值的话 会造成businessId冲突(不同库迁移的时候)
                AccDevice accDevice = accDeviceDao.findBySn(dev.getSn());
                if (Objects.nonNull(accDevice)) {
                    ModelUtil.copyPropertiesIgnoreNullWithProperties(dev, accDevice, "id");
                    accDeviceDao.saveAndFlush(accDevice);
                    deviceAllMap.put(devItem.getId(), accDevice);
                } else {
                    // 保存BusinessId
                    dev.setBusinessId(createBId());
                    accDeviceDao.saveAndFlush(dev);
                    deviceAllMap.put(devItem.getId(), dev);
                }
            }
        }
        // 处理主从设备
        for (AccDeviceItem devItem : deviceItems) {
            if (StringUtils.isNotBlank(devItem.getParentDeviceId())) {
                AccDevice dev = deviceAllMap.get(devItem.getId());
                dev.setParentDevice(deviceAllMap.get(devItem.getParentDeviceId()));
                // 保存BusinessId
                dev.setBusinessId(createBId());
                accDeviceDao.saveAndFlush(dev);
            }
        }
    }

    @Override
    public Boolean validDevRegDeviceType(String regDeviceType) {
        boolean regDeviceTypeMatch = true;
        if (StringUtils.isNotBlank(regDeviceType)) {
            Map<String, Object> regDeviceTypeMap = baseLicenseProvider.getAccPushDeviceType();
            switch (regDeviceType) {
                case "0":
                    regDeviceTypeMatch = regDeviceTypeMap.get(ConstUtil.LICENSE_ACC_NONE_PROJECT_DEVICE).equals('1');
                    break;
                case "1":
                    regDeviceTypeMatch = regDeviceTypeMap.get(ConstUtil.LICENSE_ACC_PROJECT_DEVICE).equals('1');
                    break;
                default:
                    regDeviceTypeMatch = false;
                    break;
            }
        }
        return regDeviceTypeMatch;
    }

    @Override
    public Long remoteRegistration(String devId, int templateType, int templateNo, String pin) {
        AccDevice device = accDeviceDao.findById(devId).orElse(null);
        long cmdId = -1;
        if (device != null) {
            cmdId = accDevCmdManager.remoteRegistration(device, templateType, templateNo, pin, true);
        }
        return cmdId;
    }

    @Override
    public ZKResultMsg getRemoteTemplate(String personPin, String bioType, long sendTime) {
        List<Acc4RemoteTemplate> rs = new ArrayList<>();
        try {
            int second = 0;
            Set<String> keySet = new HashSet<>();
            int fpvTypeInt = Integer.parseInt(bioType);

            while (second <= 30) {
                if ((fpvTypeInt == 1 && !keySet.isEmpty()) || (fpvTypeInt == 2 && keySet.size() > 1)) {
                    break;
                }
                Thread.sleep(1000);
                keySet = accCacheManager.getCacheKeySet(AccCacheKeyConstants.ACC_REMOTE_TEMPLATE + personPin + "_*");
                Set<String> removeKeySet = new HashSet<>();
                for (String key : keySet) {
                    String[] keyData = StringUtils.split(key, "_");
                    long dataTime = Long.parseLong(keyData[keyData.length - 1]);
                    if (sendTime > dataTime) {
                        removeKeySet.add(key);
                    }
                }
                if (!removeKeySet.isEmpty()) {
                    keySet.removeAll(removeKeySet);
                }
                second++;
            }
            if (!keySet.isEmpty()) {
                for (String key : keySet) {
                    String bioDataStr = accCacheManager.getCacheValueByKey(key);
                    // 全部推送给前端解析
                    // JSONObject obj = new JSONObject();
                    String[] dataArray = bioDataStr.split("\t");
                    // 用map或者json直接转换会导致照片数据过大时被截断
                    Acc4RemoteTemplate remoteTemplate = new Acc4RemoteTemplate();
                    for (String bioData : dataArray) {
                        String[] tempData = bioData.split("=", 2);
                        if (tempData.length != 2) {
                            continue;
                        }
                        String dataKey = tempData[0];
                        String dataVal = tempData[1];
                        if ("PIN".equals(dataKey)) {
                            remoteTemplate.setPin(dataVal);
                        } else if ("TYPE".equals(dataKey)) {
                            remoteTemplate.setType(dataVal);
                        } else if ("MAJOR_VER".equals(dataKey)) {
                            remoteTemplate.setMajorVer(dataVal);
                        } else if ("MINOR_VER".equals(dataKey)) {
                            remoteTemplate.setMinorVer(dataVal);
                        } else if ("NO".equals(dataKey)) {
                            remoteTemplate.setNo(dataVal);
                        } else if ("INDEX".equals(dataKey)) {
                            remoteTemplate.setIndex(dataVal);
                        } else if ("TMP".equals(dataKey)) {
                            remoteTemplate.setTmp(dataVal);
                        } else if ("BIOPHOTO".equals(dataKey)) {
                            remoteTemplate.setBioPhoto(dataVal);
                        }
                    }
                    if (StringUtils.isNotBlank(remoteTemplate.getMajorVer())) {
                        String version = remoteTemplate.getMajorVer();
                        if (StringUtils.isNotBlank(remoteTemplate.getMinorVer())) {
                            version = version + "." + remoteTemplate.getMinorVer();
                        }
                        remoteTemplate.setVersion(version);
                    }
                    rs.add(remoteTemplate);
                }
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return new ZKResultMsg(rs);
    }

    @Override
    public String getDevSnByReaderId(String accReaderId) {
        return accDeviceDao.getDevSnByReaderId(accReaderId);
    }

    @Override
    public void updateAdmsComAddress(String sn, String comAddr) {
        admsDeviceService.updateComAddress(sn, Short.parseShort(comAddr));
    }

    @Override
    public ZKResultMsg getReadIDCardInfo(Long cmdId, String type) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        if (cmdId > 0) {
            Map<String, String> resultMap = getCmdResultById(cmdId, 60);// 获取命令返回值
            if ("identityCard".equals(type)) {
                // 获取结果 大于0为成功
                if (Objects.nonNull(resultMap) && Integer.parseInt(resultMap.get("result")) > 0) {
                    // 获取身份证信息，把身份证信息也返回出去。
                    String key = AccCacheKeyConstants.IDENTITY_CARD_INFO_KEY + cmdId;
                    JSONObject jsonData = accCacheManager.getIdentityCardInfo(key);
                    if (jsonData != null && jsonData.containsKey("data")) {
                        JSONArray jsonArray = jsonData.getJSONArray("data");
                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                        resultMap.put("identityCardInfo", jsonObject.toJSONString());
                        zkResultMsg.setData(resultMap);
                    }
                }
            } else if ("testNetConnect".equals(type))// 测试网络相关的，0为成功
            {
                if (Objects.nonNull(resultMap) && Integer.parseInt(resultMap.get("result")) != 0) {// 获取结果 0为成功
                    {
                        if (Integer.parseInt(resultMap.get("result")) == Integer.parseInt(ConstUtil.CMD_TIMEOUT)) {
                            zkResultMsg.setRet("500");
                        } else {
                            zkResultMsg.setRet("400");
                        }
                    }
                }
            }
        }
        return zkResultMsg;
    }

    @Override
    public Long getLastPushTime() {
        String lastPushTime = baseSysParamService.getValByName("acc.device.lastPushTime");
        return StringUtils.isNotBlank(lastPushTime) ? Long.parseLong(lastPushTime) : null;
    }

    @Override
    public List<String> getUploadCloudDevicesByLastPushTime(Date lastUpdate) {
        List<String> devInfoList = Lists.newArrayList();
        AccDeviceItem accDeviceItem = new AccDeviceItem();
        accDeviceItem.setLastUpdateDate(lastUpdate);
        List<AccDeviceItem> accDeviceItemList = getByCondition(accDeviceItem);
        if (accDeviceItemList != null && accDeviceItemList.size() > 0) {
            devInfoList.addAll(buildUploadDevice(accDeviceItemList));
        }
        return devInfoList;
    }

    private List<String> buildUploadDevice(List<AccDeviceItem> accDeviceItems) {
        List<String> devInfoList = Lists.newArrayList();
        List<String> devIds = (List<String>)CollectionUtil.getItemIdsList(accDeviceItems);
        String areaIds = CollectionUtil.getPropertys(accDeviceItems, AccDeviceItem::getAuthAreaId);
        List<AccDoorItem> accDoorItemList = accDoorService.getItemsByDevIds(devIds);
        List<AuthAreaItem> authAreaItems = authAreaService.getItemsByIds(areaIds);
        Map<String, List<AccDoorItem>> accDoorMap =
            accDoorItemList.stream().collect(Collectors.groupingBy(AccDoorItem::getDeviceId));
        Map<String, AuthAreaItem> authAreaItemMap = CollectionUtil.itemListToIdMap(authAreaItems);
        JSONObject devInfo = null;
        for (AccDeviceItem dev : accDeviceItems) {
            devInfo = new JSONObject();
            AuthAreaItem area = authAreaItemMap.get(dev.getAuthAreaId());
            devInfo.put("id", dev.getId());
            devInfo.put("sn", dev.getSn());
            devInfo.put("alias", dev.getAlias());
            devInfo.put("ipAddress", dev.getIpAddress());
            devInfo.put("states", getStatus(dev.getSn()));
            devInfo.put("deviceName", dev.getDeviceName());
            devInfo.put("enabled", dev.getEnabled());
            devInfo.put("parentDevId", dev.getParentDeviceId());
            devInfo.put("authAreaName", area.getName());
            devInfo.put("authAreaCode", area.getCode());
            if (StringUtils.isNotBlank(area.getParentAreaCode())) {
                devInfo.put("areaParentCode", area.getParentAreaCode());
            }
            if (accDoorMap.containsKey(dev.getId())) { // IR9000设备没有门
                JSONArray doors = new JSONArray();// 组装门数据
                List<AccDoorItem> doorList = accDoorMap.get(dev.getId());
                doorList.forEach(accDoorItem -> {
                    JSONObject door = new JSONObject();
                    door.put("doorNo", accDoorItem.getDoorNo());
                    door.put("devSn", accDoorItem.getDeviceSn());
                    door.put("name", accDoorItem.getName());
                    door.put("verifyMode", accDoorItem.getVerifyMode());
                    door.put("enabled", accDoorItem.getEnabled());
                    doors.add(door);
                });
                devInfo.put("doors", doors);
            }
            devInfoList.add(devInfo.toJSONString());
        }
        return devInfoList;
    }

    @Override
    public void setLastPushTime() {
        baseSysParamService.saveValueByName("acc.device.lastPushTime", System.currentTimeMillis() + "");
    }

    @Override
    public List<AccDeviceItem> getItemsByDevSnIn(List<String> sns) {
        List<AccDevice> devList = accDeviceDao.findBySnIn(new ArrayList(sns));
        if (devList != null && devList.size() > 0) {
            return ModelUtil.copyListProperties(devList, AccDeviceItem.class);
        }
        return null;
    }

    @Override
    public Pager getDeviceSelectItem(String sessionId, AccDeviceSelectItem condition, int pageNo, int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        Pager pager = getItemsByPage(condition, pageNo, pageSize);
        List<AccDeviceSelectItem> accSelectDeviceItems = (List<AccDeviceSelectItem>)pager.getData();
        if (accSelectDeviceItems != null && accSelectDeviceItems.size() > 0) {
            String devAreaIds = CollectionUtil.getPropertys(accSelectDeviceItems, AccDeviceSelectItem::getAuthAreaId);
            Map<String, AuthAreaItem> authAreaItemMap =
                CollectionUtil.itemListToIdMap(authAreaService.getItemsByIds(devAreaIds));
            accSelectDeviceItems.forEach(accSelectItem -> {
                AuthAreaItem authAreaItem = authAreaItemMap.get(accSelectItem.getAuthAreaId());
                if (Objects.nonNull(authAreaItem)) {
                    accSelectItem.setAuthAreaName(authAreaItem.getName());
                }
            });
        }
        pager.setData(accSelectDeviceItems);
        return pager;
    }

    @Override
    public Long getAllPersonFromDev(AccDeviceItem accDeviceItem) {
        return accDevCmdManager.getAllPersonFromDev(accDeviceItem.getSn(), true);
    }

    @Override
    public Long getAllPersonExtInfoFromDev(AccDeviceItem accDeviceItem) {
        AccDevice dev = accDeviceDao.getOne(accDeviceItem.getId());
        return accDevCmdManager.getAllPersonExtInfoFromDev(dev.getSn(), true);
    }

    @Override
    public Long getAllPersonCardFromDev(AccDeviceItem accDeviceItem) {
        AccDevice dev = accDeviceDao.getOne(accDeviceItem.getId());
        return accDevCmdManager.getAllPersonCardFromDev(dev.getSn(), true);
    }

    @Override
    public List<List<String>> queryDevInfo(String ids) {
        List<List<String>> queryList = new ArrayList<>();
        List<AccDevice> accDeviceList = accDeviceDao.findByIdIn((List<String>)CollectionUtil.strToList(ids));
        for (AccDevice accDevice : accDeviceList) {
            List<String> tempList = new ArrayList<>();
            AccDeviceOption multiBioDataSupport =
                accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), "MultiBioDataSupport");
            AccDeviceOption multiBioPhotoSupport =
                accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), "MultiBioPhotoSupport");
            boolean supportFinger = false;
            if (multiBioDataSupport != null) {
                supportFinger =
                    "1".equals(multiBioDataSupport.getValue().split(":")[BaseConstants.BaseBioType.FP_BIO_TYPE]);
            } else {
                AccDeviceOption fingerFunOn =
                    accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), AccConstants.FINGER_FUN_ON);
                AccDeviceOption isOnlyRFMachine =
                    accDeviceOptionDao.findByAccDevice_IdAndName(accDevice.getId(), AccConstants.ONLY_RF_MACHINE);
                if (fingerFunOn != null) {
                    supportFinger = "1".equals(fingerFunOn.getValue());
                } else if (isOnlyRFMachine != null) {
                    supportFinger = !"1".equals(isOnlyRFMachine.getValue());
                }
            }
            boolean supportFv = false;
            if (multiBioDataSupport != null) {
                supportFv =
                    "1".equals(multiBioDataSupport.getValue().split(":")[BaseConstants.BaseBioType.VEIN_BIO_TYPE]);
            } else {
                supportFv = accDeviceOptionService.isSupportFun(accDevice.getSn(), AccConstants.FV_FUN_ON);
            }

            boolean supportBiophoto = false;
            if (multiBioPhotoSupport != null) {
                supportBiophoto =
                    "1".equals(multiBioPhotoSupport.getValue().split(":")[BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE]);
            } else {
                supportBiophoto = accDeviceOptionService.isSupportFun(accDevice.getSn(), AccConstants.BIOPHOTO_FUN_ON);
            }

            boolean supportFace = false;
            if (multiBioDataSupport != null) {
                supportFace =
                    "1".equals(multiBioDataSupport.getValue().split(":")[BaseConstants.BaseBioType.FACE_BIO_TYPE]);
                if (!supportFace) {
                    supportFace = "1"
                        .equals(multiBioDataSupport.getValue().split(":")[BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE]);
                }
            } else {
                // 支持面部，且不支持比对照片才获取，解决可见光设备FaceFunOn和BioPhotoFun两个功能参数都支持的情况下，界面上都显示/都去下发获取的问题
                supportFace = accDeviceOptionService.isSupportFun(accDevice.getSn(), AccConstants.FACE_FUN_ON)
                    && !supportBiophoto;
            }

            boolean supportPalm = false;
            if (multiBioDataSupport != null) {
                supportPalm =
                    "1".equals(multiBioDataSupport.getValue().split(":")[BaseConstants.BaseBioType.PALM_BIO_TYPE]);
                if (!supportPalm) {
                    supportPalm = "1".equals(getMultiBioVersion(multiBioDataSupport.getValue().split(":"),
                        AccConstants.TEMPLATE_VISILIGHT_PALM));
                }
            } else {
                supportPalm = accDeviceOptionService.isSupportFun(accDevice.getSn(), AccConstants.PV_FUN_ON);
            }

            /*boolean supportvislightFaceTemp = false;
            if (multiBioDataSupport != null) {
                supportvislightFaceTemp =
                    "1".equals(multiBioDataSupport.getValue().split(":")[BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE]);
            }*/

            tempList.add(accDevice.getAlias());
            tempList.add(supportFinger ? "true" : "false");
            tempList.add(supportFv ? "true" : "false");
            tempList.add(supportFace ? "true" : "false");
            // tempList.add(supportBiophoto ? "true" : "false");
            tempList.add(supportPalm ? "true" : "false");
            // tempList.add(supportvislightFaceTemp ? "true" : "false");
            tempList.add(accDevice.getId() + "");
            if (!getStatus(accDevice.getSn()).equals(String.valueOf(ConstUtil.DEV_STATE_ONLINE))) {
                tempList.add("disabled = 'disabled'");
            } else {
                tempList.add("");// 判断禁用，放入空字符串
            }
            queryList.add(tempList);
        }
        return queryList;
    }

    @Override
    public ZKResultMsg updateDevExtendParam(Map<String, String> params) {
        if (!params.isEmpty()) {
            String devId = params.get("devId");
            AccDevice accDevice = accDeviceDao.findById(devId).orElse(null);
            if (Objects.nonNull(accDevice)) {
                Map<String, String> optionMap = new HashMap<>(16);
                for (String key : params.keySet()) {
                    AccDeviceOption devOpt =
                        accDeviceOptionDao.findByAccDevice_IdAndNameLike(accDevice.getId(), "%" + key + "%");
                    // 存在，更新对应参数值
                    if (Objects.nonNull(devOpt)) {
                        optionMap.put(devOpt.getName(), params.get(key));
                        devOpt.setValue(params.get(key));
                        accDeviceOptionDao.save(devOpt);
                    } /* else if (AccConstants.DEV_EXTEND_VIDEOINTERCOMOPTION.contains(key)) {
                         // 可视对讲参数有部分没有直接传参数给软件
                         AccDeviceOption newDevOpt = new AccDeviceOption();
                         newDevOpt.setName(key);
                         newDevOpt.setValue(params.get(key));
                         newDevOpt.setAccDevice(accDevice);
                         accDeviceOptionDao.save(newDevOpt);
                         optionMap.put(newDevOpt.getName(), params.get(key));
                      }*/
                }
                // 下发扩展参数
                accDevCmdManager.setOption(accDevice.getSn(), optionMap, false);
                return ZKResultMsg.successMsg();
            }
        }
        return ZKResultMsg.failMsg();
    }

    @Override
    public void updateDevIpAddrForOtherModule(String sn, String ipAddress) {
        if (Objects.nonNull(acc4VmsDeviceService)) {
            acc4VmsDeviceService.updateVmsDeviceIp(sn, ipAddress);
        }

        if (Objects.nonNull(acc4IvsDeviceService)) {
            acc4IvsDeviceService.updateIvsDeviceIp(sn, ipAddress);
        }
    }

    @Override
    public List<AccDeviceItem> getItemBySns(String sns) {
        AccDeviceItem accDeviceItem = new AccDeviceItem();
        accDeviceItem.setSns(sns);
        return getByCondition(accDeviceItem);
    }

    @Override
    public List<AccDeviceItem> getItemsByDSTimeId(List<String> dsTimeId) {
        List<AccDeviceItem> accDeviceItemList = new ArrayList<>();
        if (!dsTimeId.isEmpty()) {
            List<AccDevice> accDeviceList = accDeviceDao.findByAccDSTime_IdIn(dsTimeId);
            if (Objects.nonNull(accDeviceList) && !accDeviceList.isEmpty()) {
                accDeviceItemList = ModelUtil.copyListProperties(accDeviceList, AccDeviceItem.class);
            }
        }
        return accDeviceItemList;
    }

    @Override
    public Map<String, AccDeviceItem> getItemsMapByDevIds(List<String> devIdList) {
        List<AccDeviceItem> accDeviceItemList = getItemByIds(StrUtil.collectionToStr(devIdList));
        return CollectionUtil.listToKeyMap(accDeviceItemList, AccDeviceItem::getId);
    }

    @Override
    public AccDeviceItem getParentDeviceByDevSn(String deviceSn) {
        AccDeviceItem item = null;
        if (StringUtils.isNotBlank(deviceSn)) {
            AccDevice accDevice = accDeviceDao.getParentDeviceByDevSn(deviceSn);
            if (Objects.nonNull(accDevice)) {
                item = ModelUtil.copyProperties(accDevice, new AccDeviceItem());
            }
        }
        return item;
    }

    @Override
    public List<AccDeviceItem> getDevItemByDoorIds(List<String> doorIds) {
        List<AccDevice> accDeviceList = accDeviceDao.getDevByDoorIds(doorIds);
        List<AccDeviceItem> accDeviceItemList = new ArrayList<>();
        if (Objects.nonNull(accDeviceList) && !accDeviceList.isEmpty()) {
            accDeviceItemList = ModelUtil.copyListProperties(accDeviceList, AccDeviceItem.class);
        }
        return accDeviceItemList;
    }

    @Override
    public AccDeviceItem getDevItemByDoorId(String doorId) {
        AccDevice accDevice = accDeviceDao.getDeviceByDoorId(doorId);
        AccDeviceItem item = null;
        if (Objects.nonNull(accDevice)) {
            item = ModelUtil.copyProperties(accDevice, new AccDeviceItem());
        }
        return item;
    }

    @Override
    public List<?> getItemsByCondition(BaseItem condition) {
        return accDeviceDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public List<AccDeviceItem> getAllDeviceItems() {
        List<AccDevice> accDeviceList = accDeviceDao.findAll();
        List<AccDeviceItem> accDeviceItems = new ArrayList<>();
        if (Objects.nonNull(accDeviceList) && !accDeviceList.isEmpty()) {
            accDeviceItems = ModelUtil.copyListProperties(accDeviceList, AccDeviceItem.class);
        }
        return accDeviceItems;
    }

    @Override
    public List<AccDeviceItem> getDevItemByReaderIds(List<String> readerIds) {
        List<AccDevice> accDeviceList = accDeviceDao.getDevByReaderIds(readerIds);
        List<AccDeviceItem> accDeviceItemList = new ArrayList<>();
        if (Objects.nonNull(accDeviceList) && !accDeviceList.isEmpty()) {
            accDeviceItemList = ModelUtil.copyListProperties(accDeviceList, AccDeviceItem.class);
        }
        return accDeviceItemList;
    }

    @Override
    public void informOtherModuleSyncData(List<String> devIds) {
        // 通知其他模块同步所有数据
        if (accDevice4OtherServices != null && devIds.size() > 0) {
            Arrays.stream(accDevice4OtherServices)
                .forEach(accDevice4OtherService -> accDevice4OtherService.syncData2Dev(devIds));
        }
    }

    @Override
    public boolean checkPermissionByMenuCode(String menuCode) {
        AuthPermissionItem subMenuItem = authPermissionService.getItemByCode(menuCode);
        if (Objects.nonNull(subMenuItem)) {
            return true;
        }
        return false;
    }

    @Override
    public boolean checkPermission(String sessionId, String persmission) {
        return authPermissionService.checkPermission(sessionId, persmission);
    }

    @Override
    public void updateDevInfoWithDoorAndAuxBySn(String sn) {
        final AccDevice device = accDeviceDao.findBySn(sn);
        if (device != null) {
            putQueryItemToCache(device, true);
        }
    }

    @Override
    public AccQueryDeviceItem updateCacheDeviceInfo(AccDeviceItem accDeviceItem) {
        AccQueryDeviceItem accQueryDeviceItem = ModelUtil.copyProperties(accDeviceItem, new AccQueryDeviceItem());
        if (accDeviceItem != null) {
            AuthAreaItem authAreaItem = authAreaService.getItemById(accQueryDeviceItem.getAuthAreaId());
            accQueryDeviceItem.setAuthAreaName(authAreaItem.getName());
            fillDoorAndAuxByQueryItem(accQueryDeviceItem);
            accCacheManager.putDeviceInfo(accQueryDeviceItem);
        }
        return accQueryDeviceItem;
    }

    /**
     * 把父设备调整到列表最前面
     *
     * @param devList
     * @return
     * <AUTHOR>
     * @Date 2021/11/22 16:49
     */
    private List<AccDevice> sortTopParentDevList(List<AccDevice> devList) {
        Set<AccDevice> parentDevSets = Sets.newHashSet();
        for (AccDevice dev : devList) {
            if (dev.getParentDevice() != null && devList.contains(dev.getParentDevice())) {
                parentDevSets.add(dev.getParentDevice());
            }
        }
        if (!parentDevSets.isEmpty()) {
            // devList包含了父设备, 需要对父设备去重
            devList.removeAll(parentDevSets);
            devList.addAll(0, parentDevSets);
        }
        return devList;
    }

    /**
     * 删除pro模块的数据
     *
     * @param dev
     * @return
     * <AUTHOR>
     * @Date 2021/11/30 10:56
     */
    private void delProExtData(AccDevice dev) {
        if (Objects.nonNull(accProExtService)) {
            AccDeviceItem accDeviceItem = ModelUtil.copyProperties(dev, new AccDeviceItem());
            accProExtService.deleteProExtData(accDeviceItem);
        }
    }

    @Override
    public void replaceDevInfo(String devId, String newSn) {
        AdmsAuthDeviceItem authInfo = admsAuthDeviceService.getAuthDeviceInfo(newSn);
        AccDevice accDevice = accDeviceDao.findOne(devId);
        String machineType = "";
        if (authInfo != null && StringUtils.isNotBlank(authInfo.getExParam())) {
            String[] exparams = authInfo.getExParam().split(",");
            for (String ex : exparams) {
                String[] tempEx = ex.split("=");
                if (tempEx != null && StringUtils.isNotBlank(tempEx[0]) && "MachineType".equals(tempEx[0])
                    && StringUtils.isNotBlank(tempEx[1])) {
                    machineType = tempEx[1];
                    break;
                }
            }
        }
        if (!(StringUtils.isNotBlank(machineType) && machineType.equals(accDevice.getMachineType() + ""))) {
            throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_WARN, "acc_dev_replaceFail");
        }
        if (accDevice != null) {
            // 被替换的sn
            String oldSn = accDevice.getSn();
            accCacheManager.delDevLinkInfo(accDevice);
            // 删除门禁redis规则缓存信息
            deleteAccDevCacheInfo(accDevice.getSn());
            accDevice.setSn(newSn);
            // 保证设备是启用状态
            accDevice.setEnabled(true);
            accDeviceDao.save(accDevice);
            // 通知其他模块进行sn修改
            noticeOtherModelsModify(accDevice, oldSn);
            if (authInfo == null) {
                authInfo = new AdmsAuthDeviceItem();
                authInfo.setIp(accDevice.getIpAddress());
                authInfo.setSn(newSn);
            }
            authInfo.setAuthFlag(true);
            admsAuthDeviceService.saveItem(authInfo);
            admsCacheService.setReplaceDevInfo(newSn, accDevice.getMachineType());
            admsAuthDeviceService.authDevice(newSn);
        }
    }

    /**
     * 通知其他模块进行sn修改
     *
     * @param accDevice:
     * @param oldSn:
     * @return void
     * <AUTHOR>
     * @date 2022-09-05 17:14
     * @since 1.0.0
     */
    private void noticeOtherModelsModify(AccDevice accDevice, String oldSn) {
        if (accDevice4OtherServices != null) {
            Arrays.stream(accDevice4OtherServices).forEach(accDevice4OtherService -> {
                accDevice4OtherService.modifyDeviceSn(oldSn, accDevice.getSn());
            });
        }
    }

    /**
     * 删除redis中缓存的相关数据
     *
     * @param sn:
     * @return void
     * <AUTHOR>
     * @date 2022-08-12 14:26
     * @since 1.0.0
     */
    private void deleteAccDevCacheInfo(String sn) {
        // 移除设备最近异常状态
        accCacheManager.delDeviceLastError(sn);
        // 移除设备上一条实时记录
        accCacheManager.delDeviceLastRTLog(sn);
        accCacheManager.delDeviceInfo(sn);
        accCacheManager.delDeviceOptionInfo(sn);
        accCacheManager.deleteDevState(sn);
        // 删除adms缓存信息
        admsDeviceService.delBySn(sn);
        admsAuthDeviceService.deleteAuthDevice(sn);
    }

    @Override
    public boolean validSn(String newSn) {
        AccDevice accDevice = new AccDevice();
        accDevice.setSn(newSn);
        accDevice = accDeviceDao.findOneByExample(accDevice);
        return accDevice == null;
    }

    @Override
    public Pager loadInterlockSelectDeviceByAuthFilter(String sessionId, AccInterlockSelectDeviceItem condition,
        int pageNo, int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        return getItemsByPage(condition, pageNo, pageSize);
    }

    @Override
    public ZKResultMsg getRemoteRegistDev(String optionNames, String sessionId) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        List<TreeItem> items = new ArrayList<TreeItem>();
        TreeItem item = null;
        String areaIds = authAreaService.getAreaIdsByAuthFilter(sessionId);
        // 支持biodata的设备
        List<AccDeviceOption> supportBioDevOpts = new ArrayList<>();
        List<String> optionNameList = new ArrayList<>();
        if (StringUtils.isNotBlank(optionNames)) {
            optionNameList = StrUtil.strToList(optionNames);
        }
        String bioTypeName = "MultiBioDataSupport";
        // 如果是人脸登记的话，查询参数用的是 “MultiBioPhotoSupport”
        if (optionNameList.contains(AccConstants.BIOPHOTO_FUN_ON)) {
            bioTypeName = "MultiBioPhotoSupport";
        }
        // 支持指定功能参数的设备
        List<AccDevice> supportFunDevs = new ArrayList<>();
        String funOptionName = "";
        // 查找出有指定参数的设备参数数据，进而找出设备数据
        if (StringUtils.isNotBlank(areaIds)) {
            List<String> areaIdList = StrUtil.strToList(areaIds);
            supportBioDevOpts = accDeviceOptionDao.findByNameAndAccDevice_AuthAreaIdIn(bioTypeName, areaIdList);
            if (optionNameList.contains(AccConstants.FINGER_FUN_ON)) {
                supportFunDevs = accDeviceOptionDao.getDevByOptNameAndOptValAndDevAreaIdInAndNameNotEq(optionNameList,
                    "1", AccConstants.FV_FUN_ON, "1", areaIdList);
            } else {
                supportFunDevs =
                    accDeviceOptionDao.getDevByOptNameAndOptValAndDevAreaIdIn(optionNameList, "1", areaIdList);
            }
        } else {
            supportBioDevOpts = accDeviceOptionDao.findByName(bioTypeName);
            if (optionNameList.contains(AccConstants.FINGER_FUN_ON)) {
                supportFunDevs = accDeviceOptionDao.getDevByOptNameAndOptValAndNameNotEq(optionNameList, "1",
                    AccConstants.FV_FUN_ON, "1");
            } else {
                supportFunDevs = accDeviceOptionDao.getDevByOptNameAndOptVal(optionNameList, "1");
            }
        }

        List<AccDevice> resultDev = new ArrayList<>();
        resultDev.addAll(supportFunDevs);
        for (AccDeviceOption deviceOption : supportBioDevOpts) {
            if (!supportFunDevs.contains(deviceOption.getAccDevice())) {
                if (optionNameList.contains(AccConstants.FINGER_FUN_ON)) {
                    boolean support =
                        "1".equals(deviceOption.getValue().split(":")[BaseConstants.BaseBioType.FP_BIO_TYPE]);
                    if (support) {
                        resultDev.add(deviceOption.getAccDevice());
                    }
                } else if (optionNameList.contains(AccConstants.FV_FUN_ON)) {
                    boolean support =
                        "1".equals(deviceOption.getValue().split(":")[BaseConstants.BaseBioType.VEIN_BIO_TYPE]);
                    if (support) {
                        resultDev.add(deviceOption.getAccDevice());
                    }
                } else if (optionNameList.contains(AccConstants.PV_FUN_ON)) {
                    boolean support =
                        "1".equals(deviceOption.getValue().split(":")[BaseConstants.BaseBioType.PALM_BIO_TYPE]);
                    if (support) {
                        resultDev.add(deviceOption.getAccDevice());
                    }
                } else if (optionNameList.contains(AccConstants.BIOPHOTO_FUN_ON)) {
                    boolean support =
                        "1".equals(deviceOption.getValue().split(":")[BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE]);
                    if (support) {
                        resultDev.add(deviceOption.getAccDevice());
                    }
                } else if (optionNameList.contains(AccConstants.LIVE_PV_FUN_ON)) {
                    boolean support = "1".equals(deviceOption.getValue().split(":")[PersConstants.PALM_BIO_TYPE_10]);
                    if (support) {
                        resultDev.add(deviceOption.getAccDevice());
                    }
                } else if (optionNameList.contains(AccConstants.IRIS_FUN_ON)) {
                    boolean support = "1".equals(deviceOption.getValue().split(":")[PersConstants.IRIS_BIO_TYPE]);
                    if (support) {
                        resultDev.add(deviceOption.getAccDevice());
                    }
                }

            }
        }
        for (AccDevice device : resultDev) {
            String status = getStatus(device.getSn());
            // 判断设备是否支持远程登记并且设备在线
            if (accDeviceOptionService.getAccSupportFunListVal(device.getId(), 40)
                && status.equals(String.valueOf(AccConstants.DEV_STATE_ONLINE))) {
                item = new TreeItem();
                item.setId(device.getId());
                item.setText(device.getAlias());
                items.add(item);
            }
        }
        if (items.size() > 0) {
            zkResultMsg = new ZKResultMsg(items);
        }
        return zkResultMsg;
    }

    @Override
    public Map<Short, String> getBioTemplateVersionBySn(String sn) {
        Map<Short, String> bioTemplateVersion = new HashMap<>();
        Map<String, String> devOptionMap = accDeviceOptionService.getDevOptionBySn(sn);
        // 设备指纹版本
        String fingerVersion =
            getTemplateVersion(sn, devOptionMap, "~ZKFPVersion", BaseConstants.BaseBioType.FP_BIO_TYPE);
        // String fingerVersion = accDeviceOptionService.getValueByNameAndDevSn(sn, );
        // 设备指静脉版本
        String fvVersion = getTemplateVersion(sn, devOptionMap, "FvVersion", BaseConstants.BaseBioType.VEIN_BIO_TYPE);
        // 设备人脸版本
        String faceVersion =
            getTemplateVersion(sn, devOptionMap, "FaceVersion", BaseConstants.BaseBioType.FACE_BIO_TYPE);
        // 设备掌纹版本
        String pvVersion = getTemplateVersion(sn, devOptionMap, "PvVersion", BaseConstants.BaseBioType.PALM_BIO_TYPE);
        // 设备可见光版本
        String vislightFaceVersion =
            getTemplateVersion(sn, devOptionMap, "PvVersion", BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
        // 设备可见光掌纹版本
        String vislightPvVersion = getTemplateVersion(sn, devOptionMap, "", AccConstants.TEMPLATE_VISILIGHT_PALM);
        // 设备虹膜版本
        String irisVersion =
                getTemplateVersion(sn, devOptionMap, "", AccConstants.TEMPLATE_VISILIGHT_IRIS);
        bioTemplateVersion.put(BaseConstants.BaseBioType.FP_BIO_TYPE, fingerVersion);
        bioTemplateVersion.put(BaseConstants.BaseBioType.VEIN_BIO_TYPE, fvVersion);
        bioTemplateVersion.put(BaseConstants.BaseBioType.FACE_BIO_TYPE, faceVersion);
        bioTemplateVersion.put(BaseConstants.BaseBioType.PALM_BIO_TYPE, pvVersion);
        bioTemplateVersion.put(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE, vislightFaceVersion);
        bioTemplateVersion.put(AccConstants.TEMPLATE_VISILIGHT_PALM, vislightPvVersion);
        bioTemplateVersion.put(AccConstants.TEMPLATE_VISILIGHT_IRIS, irisVersion);
        return bioTemplateVersion;
    }

    @Override
    public String getTemplateVersion(String sn, Map<String, String> devOptionMap, String optionName, Short bioType) {
        String version = "";
        if (devOptionMap.containsKey("MultiBioVersion")) {
            String multiBioVersion = devOptionMap.get("MultiBioVersion");
            String[] multiBioVersionAry = multiBioVersion.split(":");
            if (multiBioVersionAry != null) {
                version = getMultiBioVersion(multiBioVersionAry, bioType);
            }
        } else if (devOptionMap.containsKey(optionName)) {
            version = devOptionMap.get(optionName);
        }
        version = StringUtils.isNotBlank(version) && version.indexOf(".") != -1 ? version : version + ".0";
        return version;
    }

    @Override
    public Long getCmdCount(String sn) {
        return accCacheManager.getDevCmdCount(sn);
    }

    @Override
    public void updateAdmsDevOption(AccDeviceItem item) {
        if (admsDeviceOptionService != null) {
            Map<String, String> updateOptions = new HashMap<>();
            updateOptions.put("IPAddress", item.getIpAddress());
            updateOptions.put("GATEIPAddress", item.getGateway());
            updateOptions.put("NetMask", item.getSubnetMask());
            admsDeviceOptionService.updateDevOptions(item.getSn(), updateOptions);
        }
    }

    @Override
    public String getParentSnByChildSns(List<String> snList) {
        String parentSn = "";
        if (!CollectionUtil.isEmpty(snList)) {
            String sns = snList.stream().collect(Collectors.joining(","));
            List<AccDeviceItem> accDeviceItems = getItemBySns(sns);
            for (AccDeviceItem accDeviceItem : accDeviceItems) {
                if (StringUtils.isNotEmpty(accDeviceItem.getParentDeviceId())) {
                    AccDeviceItem parentDevice = getItemById(accDeviceItem.getParentDeviceId());
                    return parentDevice.getSn();
                }
            }
        }
        return parentSn;
    }

    @Override
    public List<SelectItem> getNamesByfileTypes(List<String> fileTypes) {
        List<SelectItem> files = baseMediaFileService.getNamesByfileTypes(fileTypes);
        return files;
    }

    @Override
    public boolean uploadAdResourceFile(Acc4UploadAdFileItem item) {
        AccDeviceItem deviceItem = getItemById(item.getDevId());
        if (deviceItem != null) {
            accDevCmdManager.setAd(deviceItem.getSn(), item, false);
        }
        return true;
    }

    @Override
    public List<SelectItem> getDevLinkIPC(String devSn) {
        List<SelectItem> selectItemList = new ArrayList<>();
        IvsChannel4OtherItem item = new IvsChannel4OtherItem();
        item.setDomainCode(devSn);
        List<IvsChannel4OtherItem> channelItems = ivs4OtherGetIvsChannelService.getChannel(item);
        for (IvsChannel4OtherItem channel : channelItems) {
            selectItemList
                .add(new SelectItem(channel.getName() + "(" + channel.getChannelNo() + ")", channel.getChannelNo()));
        }
        return selectItemList;
    }

    @Override
    public String getDevNtpById(String id) {
        AccDeviceOptionItem option = accDeviceOptionService.getItemByDevIdAndName(id, AccConstants.NTP_KRY);
        if (option != null) {
            return option.getValue();
        }
        return null;
    }

    @Override
    public void updateExtDeviceCache(AccQueryDeviceItem item) {
        List<AccExtDeviceItem> accExtDeviceItemList = accExtDeviceService.getItemsByDevId(item.getId());
        if (accExtDeviceItemList != null && !accExtDeviceItemList.isEmpty()) {
            List<AccQueryExtDeviceItem> queryExtDeviceItems = new ArrayList<>();
            for (AccExtDeviceItem extDeviceItem : accExtDeviceItemList) {
                AccQueryExtDeviceItem accQueryExtDeviceItem = new AccQueryExtDeviceItem();
                ModelUtil.copyProperties(extDeviceItem, accQueryExtDeviceItem);
                queryExtDeviceItems.add(accQueryExtDeviceItem);
            }
            item.setAccQueryExtDeviceItemList(queryExtDeviceItems);
        }
    }

    @Override
    public void setFaceVerifyServer(Acc4SetFaceVerifyServer item) {
        if (StringUtils.isNotBlank(item.getIds())) {
            List<AccDeviceItem> deviceItems = getItemByIds(item.getIds());
            if (deviceItems != null && !deviceItems.isEmpty()) {
                for (AccDeviceItem device : deviceItems) {
                    accDevCmdManager.setFaceVerifyServer(device.getSn(), item, false);
                    accDeviceOptionService.setDevOptValByName(device.getId(), "FaceBgServerType", item.getFaceBgServerType());
                    accDeviceOptionService.setDevOptValByName(device.getId(), "IsAccessLogicFunOn", item.getIsAccessLogic());
                    accDeviceOptionService.setDevOptValByName(device.getId(), "FaceVerifyMode", item.getFaceVerifyMode());
                    accDeviceOptionService.setDevOptValByName(device.getId(), "FaceThreshold", item.getFaceThreshold());
                    accDeviceOptionService.setDevOptValByName(device.getId(), "FaceBgServerURL", item.getFaceBgServerURL());
                    accDeviceOptionService.setDevOptValByName(device.getId(), "FaceBgServerSecret", item.getFaceBgServerSecret());
                }
            }
        }
    }
}