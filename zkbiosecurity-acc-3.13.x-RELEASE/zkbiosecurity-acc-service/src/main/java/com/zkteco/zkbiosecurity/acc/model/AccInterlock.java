/**
 * File Name: AccInterlock Created by GenerationTools on 2018-03-13 上午09:53 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 AccInterlock
 * 
 * <AUTHOR>
 * @date: 2018-03-13 上午09:53
 * @version v1.0
 */
@Entity
@Table(name = "ACC_INTERLOCK")
@Getter
@Setter
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class AccInterlock extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**  */
    @ManyToOne(optional = false)
    @JoinColumn(name = "DEV_ID")
    private AccDevice accDevice;

    /**  */
    @Column(name = "INTERLOCK_RULE")
    private Short interlockRule;

    /** 业务id */
    @Column(name = "BUSINESS_ID")
    private Long businessId;

    /** 名称 */
    @Column(name = "NAME", length = 100)
    private String name;

    /** 1组间互锁 2组内互锁 */
    @Column(name = "LOCK_RULE")
    private Short lockRule;

    /** 组1 */
    @OneToOne(cascade = CascadeType.REMOVE)
    @JoinColumn(name = "TRIGGER_GROUP1", referencedColumnName = "ID")
    private AccTriggerGroup triggerGroup1;

    /** 组2 */
    @OneToOne(cascade = CascadeType.REMOVE)
    @JoinColumn(name = "TRIGGER_GROUP2", referencedColumnName = "ID")
    private AccTriggerGroup triggerGroup2;
}