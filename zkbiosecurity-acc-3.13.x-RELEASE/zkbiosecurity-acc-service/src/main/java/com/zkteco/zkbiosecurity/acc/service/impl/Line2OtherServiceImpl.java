package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.AccLinkageMediaService;
import com.zkteco.zkbiosecurity.acc.vo.AccLinkageMediaItem;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.line.service.Line2OtherService;

/**
 * <AUTHOR>
 * @date 2022/2/17 15:58
 */
@Service
@Transactional
public class Line2OtherServiceImpl implements Line2OtherService {
    @Autowired
    private AccLinkageMediaService accLinkageMediaService;

    @Override
    public boolean checkDelLineContact(String contactIds) {
        List<AccLinkageMediaItem> linkageMediaItemList =
            accLinkageMediaService.getItemsByCondition(contactIds, AccConstants.LINE);
        if (linkageMediaItemList != null && linkageMediaItemList.size() > 0) {
            throw new ZKBusinessException(
                I18nUtil.i18nCode("common_prompt_canNotDel", I18nUtil.i18nCode("acc_module")));
        }
        return true;
    }

}
