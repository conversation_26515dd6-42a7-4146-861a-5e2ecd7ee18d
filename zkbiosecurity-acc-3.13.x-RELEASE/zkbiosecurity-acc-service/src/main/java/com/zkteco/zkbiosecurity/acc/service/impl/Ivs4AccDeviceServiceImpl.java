package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.ivs.service.Ivs4AccDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 智能视频调用门禁业务接口
 *
 * <AUTHOR>
 * @date 2022/5/9 17:49
 */
@Service
public class Ivs4AccDeviceServiceImpl implements Ivs4AccDeviceService {

    @Autowired
    private AccDeviceService accDeviceService;

    @Override
    public Boolean checkDeviceExistBySn(List<String> devSnList) {
        List<AccDeviceItem> accDeviceItemList = accDeviceService.getItemsByDevSnIn(devSnList);
        return Objects.nonNull(accDeviceItemList) && !accDeviceItemList.isEmpty();
    }
}
