package com.zkteco.zkbiosecurity.acc.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zkteco.zkbiosecurity.acc.service.WechatService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import lombok.Getter;
import lombok.Setter;
import lombok.Value;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Getter
@Setter
@Service
public class WechatServiceImpl implements WechatService {

    @Autowired
    PersPersonService persPersonService;

    // 默认值
    private static final String DEFAULT_APP_ID = "wx3ff68d5fada0a6d5";
    private static final String DEFAULT_APP_SECRET = "a014b0bd62f1c7fe918e1f9f25d42360";

    private static final String DEFAULT_TEMPLATE_ID_ACC = "qn2NGLIRMznalRH6o0jJSVzduTjE4D3xVFa7l4YaEck";
    private static final String DEFAULT_TEMPLATE_ID_ATT = "qn2NGLIRMznalRH6o0jJSVzduTjE4D3xVFa7l4YaEck";
    private  static final String DEFAULT_DOMAIN="https://fx.iotechn.com";


//    private static final String DEFAULT_APP_ID = "wxc71eb85bdaee3f9e";//测试号
//     private static final String DEFAULT_APP_SECRET =
//     "2e6d9203214db1c5412adbf9f70327d2";//测试号
//
//     private static final String DEFAULT_TEMPLATE_ID_ACC =
//     "dQvd4wPDGEx7tE0YN8HOD0zAIcRiiYpsrDQYIYhucJ8";//测试号
//     private static final String DEFAULT_TEMPLATE_ID_ATT =
//     "dQvd4wPDGEx7tE0YN8HOD0zAIcRiiYpsrDQYIYhucJ8";//测试号
//    private  static final String DEFAULT_DOMAIN="http://113.108.97.180:8098";

    private static final String DEFAULT_REDIRECT_URL = "/api/v3/wechatAuth/exceptionRecordDetail";

    // 获取微信公众号 access_token 的地址
    private String tokenUrl = "https://api.weixin.qq.com/cgi-bin/token";

    // token类型
    private String tokenGrantType = "client_credential";

    // 发送模板消息的地址
    private String templateUrl = "https://api.weixin.qq.com/cgi-bin/message/template/send";

    // 获取用户列表的url，主要是 openId
    private String userListUrl = "https://api.weixin.qq.com/cgi-bin/user/get";
    // 获取用户详细信息的 url
    private String userInfoUrl = "https://api.weixin.qq.com/cgi-bin/user/info";

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 从Redis获取参数，如果Redis为空则使用默认值
     */
    @Override
    public String getAppId() {
        String appId = stringRedisTemplate.opsForValue().get("wechat_app_id");
        return StringUtils.isNotBlank(appId) ? appId : DEFAULT_APP_ID;
    }

    private String getAppSecret() {
        String appSecret = stringRedisTemplate.opsForValue().get("wechat_app_secret");
        return StringUtils.isNotBlank(appSecret) ? appSecret : DEFAULT_APP_SECRET;
    }

    private String getTemplateIdAcc() {
        String templateId = stringRedisTemplate.opsForValue().get("wechat_template_id_acc");
        return StringUtils.isNotBlank(templateId) ? templateId : DEFAULT_TEMPLATE_ID_ACC;
    }

    private String getTemplateIdAtt() {
        String templateId = stringRedisTemplate.opsForValue().get("wechat_template_id_att");
        return StringUtils.isNotBlank(templateId) ? templateId : DEFAULT_TEMPLATE_ID_ATT;
    }

    private String getRedirectUrl() {
        String redirectUrl = stringRedisTemplate.opsForValue().get("wechat_redirect_url");
        return StringUtils.isNotBlank(redirectUrl) ? redirectUrl : DEFAULT_REDIRECT_URL;
    }

    /**
     * 获取公众平台Token
     *
     * @return
     */
    @Override
    public String getAccessToken() {
        // 先判断 Redis 中是否存在
        String wechat_access_token = stringRedisTemplate.opsForValue().get("wechat_access_token");
        if (StringUtils.isEmpty(wechat_access_token)) {
            String accessTokenUrl = tokenUrl + "?grant_type=client_credential&";
            String requestUrl = accessTokenUrl + "appid=" + getAppId() + "&secret=" + getAppSecret();

            log.info("requestUrl {}",requestUrl);
            try {
                Map response = restTemplate.getForObject(requestUrl, Map.class);

                if (response != null) {
                    log.info(response.toString());
                    String accessToken = (String) response.get("access_token");
                    wechat_access_token = accessToken;
                    log.info("wechat_access_token:{}", accessToken);
                    // 将 access_token 存入 Redis
                    stringRedisTemplate.opsForValue().set("wechat_access_token", accessToken, 7200, TimeUnit.SECONDS);

                }
            } catch (Exception e) {

                log.info("getAccessToken {}", e.getMessage(), e);
            }
        }
        return wechat_access_token;
    }

    @Override
    public List<String> getWechatUserList() {

        String accessToken = getAccessToken();
        String requestUrl = userListUrl + "?access_token=" + accessToken + "&next_openid=";

        // 发送请求并获取JSON字符串响应
        String jsonResponse = restTemplate.getForObject(requestUrl, String.class);

        // 将JSON字符串转换为JSONObject
        JSONObject jsonObject = JSONObject.parseObject(jsonResponse);

        // 提取各个字段
        String nextOpenid = jsonObject.getString("next_openid");

        // 提取data对象中的openid数组
        JSONObject data = jsonObject.getJSONObject("data");
        JSONArray openidArray = data.getJSONArray("openid");

        // 将JSONArray转换为List<String>
        List<String> openidList = new ArrayList<>();
        for (int i = 0; i < openidArray.size(); i++) {
            openidList.add(openidArray.getString(i));
        }
        return openidList;
    }

    @Override
    public JSONObject getWechatUserInfo(String openId) {
        return null;
    }

    @Override
    public JSONObject sendWechatMessage(JSONObject messageJSONObject, String openId, String templateType,
            String recordId) {
        // 日志记录
        log.info("messageJSONObject:{}", messageJSONObject);
        String accessToken = getAccessToken();

        // 构建请求URL
        String templateUrl = this.templateUrl + "?access_token=" + accessToken;

        // 构建重定向URL，如果提供了记录ID，则添加到URL中
        String redirectUrl = getRedirectUrl();
        // 获取当前域名，这里需要根据实际情况配置
        String domain = stringRedisTemplate.opsForValue().get("wechat_domain");
        if (StringUtils.isBlank(domain)) {
            domain = DEFAULT_DOMAIN; // 默认域名，需要根据实际情况修改
            // domain = "http://113.108.97.180:8098";
        }

        if (StringUtils.isNotBlank(recordId)) {
            redirectUrl = domain + redirectUrl + "?id=" + recordId;
        } else {
            redirectUrl = domain + redirectUrl;
        }

        // 构建请求体
        JSONObject jsonContent = new JSONObject();
        jsonContent.put("touser", openId);
        jsonContent.put("url", redirectUrl);
        if ("1".equals(templateType)) {
            jsonContent.put("template_id", getTemplateIdAcc());

        } else {
            jsonContent.put("template_id", getTemplateIdAtt());
        }
        jsonContent.put("data", messageJSONObject);
        log.info("jsonContent:{}", jsonContent);

        // 使用RestTemplate发送POST请求
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 创建请求实体
        HttpEntity<String> request = new HttpEntity<>(jsonContent.toJSONString(), headers);

        // 发送请求并获取响应
        String result = restTemplate.postForObject(templateUrl, request, String.class);

        // 处理响应
        log.info("wechat_sendWechatMessage:{}", result);
        JSONObject jsonResult = JSONObject.parseObject(result);
        return jsonResult;

    }

    public void wechatSendMessageTest() {

        // List<java.lang.String> wechatUserList = this.getWechatUserList();
        // JSONObject jsonObject = new JSONObject();
        // // jsonObject.put("character_string1",
        // // JSONUtil.createObj().putOpt("value","ZF20251314520521"));
        // // jsonObject.put("amount2",JSONUtil.createObj().putOpt("value","100"));
        // // jsonObject.put("thing3", JSONUtil.createObj().putOpt("value","知否君"));
        // // jsonObject.put("time4",JSONUtil.createObj().putOpt("value","2025年05月27日
        // // 15:00"));
        // log.info("jsonObject:{}", jsonObject);
        //
        // for (String openid : wechatUserList) {
        // this.sendWechatMessage(jsonObject, openid,
        // "FgbhKdFrfi8aw_aRcEDtdx3ly-5c04JUpmZ4A0UZp_I",
        // "https://www.baidu.com");
        // }

    }

    @Override
    public String getOpenId(String code) {
        try {
            // 构建请求URL
            String requestUrl = "https://api.weixin.qq.com/sns/oauth2/access_token" +
                    "?appid=" + getAppId() +
                    "&secret=" + getAppSecret() +
                    "&code=" + code +
                    "&grant_type=authorization_code";

            log.info("请求微信接口获取openid，URL: {}", requestUrl);

            // 发送GET请求
            String response = restTemplate.getForObject(requestUrl, String.class);
            log.info("微信接口响应: {}", response);

            // 解析响应
            JSONObject jsonResponse = JSONObject.parseObject(response);

            // 检查是否有错误
            if (jsonResponse.containsKey("errcode")) {
                String errcode = jsonResponse.getString("errcode");
                String errmsg = jsonResponse.getString("errmsg");
                log.error("微信接口返回错误: errcode={}, errmsg={}", errcode, errmsg);
                return null;
            }

            // 提取openid
            String openid = jsonResponse.getString("openid");
            log.info("成功获取openid: {}", openid);
            return openid;

        } catch (Exception e) {
            log.error("获取openid失败，code: {}, 错误信息: {}", code, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public void saveUserOpenId(String pin, String openId) {
        try {
            if (StringUtils.isNotBlank(pin) && StringUtils.isNotBlank(openId)) {
                persPersonService.updatePersonByWechatOpenId(pin, openId);
                log.info("成功保存用户openid，pin: {}, openId: {}", pin, openId);
            }
        } catch (Exception e) {
            log.error("保存用户openid失败，pin: {}, openId: {}, 错误信息: {}", pin, openId, e.getMessage(), e);
        }
    }

    @Override
    public void clearUserOpenId(String pin) {
        try {
            if (StringUtils.isNotBlank(pin)) {
                // 清除用户的微信openid，设置为null或空字符串
                persPersonService.updatePersonByWechatOpenId(pin, null);
                log.info("成功清除用户openid，pin: {}", pin);
            }
        } catch (Exception e) {
            log.error("清除用户openid失败，pin: {}, 错误信息: {}", pin, e.getMessage(), e);
        }
    }
}
