package com.zkteco.zkbiosecurity.acc.utils;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 门禁规则查询--普通门禁和高级门禁
 * <AUTHOR>
 * @version 
 * @date 2014-7-31
 *
 */
public class AccQueryRulesUtil 
{
	private static final Short DEVICE_RULE_NAME_LENGTH = 30;//规则详情内容显示最大长度
	
	/**
	 * @description 将list转String
	 * @param nameList
	 * @return
	 */
	public static String nameListToString(List<String> nameList)
	{
		StringBuffer nameStr = new StringBuffer("");
		if (Objects.nonNull(nameList)) {
			nameList.stream().forEach(name -> nameStr.append(name + " | "));
		}
		return nameStr.toString();
	}
	
	/**
	 * @description 截取规则详情内容显示--最大长度DEVICE_RULE_NAME_LENGTH
	 * <AUTHOR>
	 * @data 2014-7-30
	 */
	public static String subStringRuleName(String ruleName)
	{
		String name = "";
		if(ruleName.length() >= DEVICE_RULE_NAME_LENGTH)
		{
			name = ruleName.substring(0, DEVICE_RULE_NAME_LENGTH) + "...";
		}
		else
		{
			name = ruleName;
		}
		return name;
	}

	/**
	 * 组装门禁规则数据
	 * @param size
	 * @param ruleType
	 * @param ruleContent
	 * @param messageType
	 * @return
	 */
	public static List<String> setRuleContent(int size, String ruleType, String ruleContent, int messageType)
	{
		List<String> tempList = Lists.newArrayList();
		tempList.add(ruleType);
		if (messageType == 0) {
			if (size > 0) {
				tempList.add(I18nUtil.i18nCode("acc_common_hasBeanSet"));
				tempList.add(ruleContent);
			}
			else {
				tempList.add(I18nUtil.i18nCode("acc_common_notSet"));
				tempList.add(I18nUtil.i18nCode("common_none"));
			}
		}
		else if (messageType == 1) {
			if (size > 0) {
				tempList.add(I18nUtil.i18nCode("acc_common_hasBeenOpened"));
				tempList.add(ruleContent);
			}
			else {
				tempList.add(I18nUtil.i18nCode("acc_common_notOpened"));
				tempList.add(I18nUtil.i18nCode("common_none"));
			}
		}
		else if (messageType == 2) {
			tempList.add(I18nUtil.i18nCode("acc_common_partSet"));
			tempList.add(ruleContent.substring(0, ruleContent.length() - 3));
		}
		return tempList;
	}

	/**
	 * 组装门禁规则数据
	 * 已设置 未设置 已开启 未开通
	 * 部分设置 已设置 未设置--首人、多人、时间段、门磁
	 * @param ruleType
	 * @param ruleContent
	 * @param messageType
	 * @return
	 */
	public static List<String> setRuleContent(String ruleType, String ruleContent, int messageType)
	{
		List<String> tempList = Lists.newArrayList();
		tempList.add(ruleType);
		if (messageType == 0) {
			if (!"".equals(ruleContent.trim())) {
				tempList.add(I18nUtil.i18nCode("acc_common_hasBeanSet"));
				tempList.add(ruleContent.substring(0, ruleContent.length() - 3));
			}
			else {
				tempList.add(I18nUtil.i18nCode("acc_common_notSet"));
				tempList.add(I18nUtil.i18nCode("common_none"));
			}
		}
		else if (messageType == 1) {
			if (!"".equals(ruleContent.trim())) {
				tempList.add(I18nUtil.i18nCode("acc_common_hasBeenOpened"));
				tempList.add(ruleContent.substring(0, ruleContent.length() - 3));
			}
			else {
				tempList.add(I18nUtil.i18nCode("acc_common_notOpened"));
				tempList.add(I18nUtil.i18nCode("common_none"));
			}
		}
		else if (messageType == 2) {
			tempList.add(I18nUtil.i18nCode("acc_common_partSet"));
			tempList.add(ruleContent.substring(0, ruleContent.length() - 3));
		}
		return tempList;
	}

	/**
	 * @Description: 组装规则名称
	 * @param ruleName
	 * @param dataSize
	 * @param doorIdList
	 * @param nameList
	 * @return
	 */
	public static List<String> buildRuleName(String ruleName, int dataSize, List<String> doorIdList, List<String> nameList) {
		List<String> tempList = Lists.newArrayList();
		//判断部分设置的情况
		if (dataSize > 0 && dataSize < doorIdList.size()) {
			tempList = setRuleContent(ruleName, nameListToString(nameList), 2);
		}
		else if (dataSize == 0 || dataSize >= doorIdList.size()) {
			tempList = setRuleContent(ruleName, nameListToString(nameList), 0);
		}
		return tempList;
	}

	/**
	 * @Description: 获取门磁规则
	 * @param accDoorItemList
	 * @return
	 */
	public static List<String> getSensorRuleName(List<AccDoorItem> accDoorItemList) {
		List<String> nameList = Lists.newArrayList();
		String sensorStatus = "acc_door_sensorStatus";//门磁类型
		String sensorDelay = "acc_door_sensorDelaySeconds";//门磁延时
		for (AccDoorItem door : accDoorItemList) {
			String sensorStatusInfo = "";
			String sensorDelayInfo = "";
			if (String.valueOf(door.getDoorSensorStatus()).equals("0")) {
				sensorStatusInfo = I18nUtil.i18nCode(sensorStatus, I18nUtil.i18nCode("common_none"));
			}
			else if (String.valueOf(door.getDoorSensorStatus()).equals("1")) {
				sensorStatusInfo = I18nUtil.i18nCode(sensorStatus, I18nUtil.i18nCode("acc_door_normalOpen"));//常开
			}
			else if (String.valueOf(door.getDoorSensorStatus()).equals("2")) {
				sensorStatusInfo = I18nUtil.i18nCode(sensorStatus, I18nUtil.i18nCode("acc_door_normalClose"));//常闭
			}
			sensorDelayInfo = I18nUtil.i18nCode(sensorDelay, door.getSensorDelay().toString());
			nameList.add(door.getName() + ", " + sensorStatusInfo + ", " + sensorDelayInfo);
		}
		return nameList;
	}

	public static List<String> getTimeSegRuleName(Map<String, String> dataMap) {
		List<String> nameList = Lists.newArrayList();
		dataMap.forEach((k, v) -> nameList.add(k + ", " + I18nUtil.i18nCode("acc_door_timeSeg", v)));
		return nameList;
	}


	/**
	 * 组装规则信息
	 * @return List<String>
	 */
	public static List<String> buildRuleInfo(List<String> tempList, String ruleName)
	{
		String tempName = "";
		if (Objects.nonNull(tempList)) {
			tempName = AccQueryRulesUtil.nameListToString(tempList);
		}
		return setRuleContent(ruleName, tempName, 1);
	}
}
