package com.zkteco.zkbiosecurity.acc.data.upgrade;

import java.util.List;
import java.util.Objects;

import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/2/28 14:44
 * @since 1.0.0
 */
@Slf4j
@Component
public class AccVer3_8_0 implements UpgradeVersionManager {

    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AccDeviceService accDeviceService;

    @Override
    public String getVersion() {
        return "v3.8.0";
    }

    @Override
    public String getModule() {
        return BaseConstants.ACC;
    }

    @Override
    public boolean executeUpgrade() {
        // 升级菜单
        updateMenu();
        // 升级新增参数初始化
        updateParams();
        // 更新adms设备参数
        updateAdmsDeviceOptions();
        return false;
    }

    private void updateParams() {
        String value = baseSysParamService.getValByName("accMessageNotification");
        if (StringUtils.isBlank(value)) {
            JSONObject notificationValues = new JSONObject();
            notificationValues.put("Email", "1");
            notificationValues.put("SMS", "1");
            notificationValues.put("Whatsapp", "1");
            notificationValues.put("Line", "1");
            BaseSysParamItem item = new BaseSysParamItem("accMessageNotification", notificationValues.toString(),
                "base_dataClean_accTrans");
            baseSysParamService.initData(item);
        }
    }

    private void updateMenu() {
        AuthPermissionItem subMenuItem = authPermissionService.getItemByCode("AccRTMonitor");
        if (Objects.nonNull(subMenuItem)) {
            AuthPermissionItem subButtonItem = authPermissionService.getItemByCode("AccRTMonitorLiveView");
            if (Objects.isNull(subButtonItem)) {
                // 实时监控-视频预览
                subButtonItem = new AuthPermissionItem("AccRTMonitorLiveView", "system_module_videoPreview",
                    "acc:rtMonitor:liveView", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 17);
                subButtonItem.setParentId(subMenuItem.getId());
                authPermissionService.initData(subButtonItem);
            }
        }
        // 屏蔽设备监测菜单
        subMenuItem = authPermissionService.getItemByCode("AccDeviceMonitor");
        if (Objects.nonNull(subMenuItem)) {
            subMenuItem.setAvailable(ZKConstant.FALSE);
            authPermissionService.saveItem(subMenuItem);
        }
        subMenuItem = authPermissionService.getItemByCode("AccDevice");
        AuthPermissionItem subButtonItem = authPermissionService.getItemByCode("AccDeviceClearCmd");
        if (Objects.nonNull(subMenuItem) && Objects.isNull(subButtonItem)) {
            subButtonItem = new AuthPermissionItem("AccDeviceClearCmd", "common_devMonitor_clearCmdCache",
                "acc:device:clearCmdCache", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 37);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);
        }
    }

    /**
     * 更新adms设备参数,修复升级前更改过ip等信息，admsDeviceService initCacheInfo方法重置缓存,导致服务重启pull设备连接失败
     *
     * @return void
     * <AUTHOR>
     * @date 2023-06-02 11:43
     * @since 1.0.0
     */
    private void updateAdmsDeviceOptions() {
        List<AccDeviceItem> accDeviceItemList = accDeviceService.getAllDeviceItems();
        if (accDeviceItemList.isEmpty()) {
            return;
        }
        for (AccDeviceItem item : accDeviceItemList) {
            accDeviceService.updateAdmsDevOption(item);
        }
    }
}
