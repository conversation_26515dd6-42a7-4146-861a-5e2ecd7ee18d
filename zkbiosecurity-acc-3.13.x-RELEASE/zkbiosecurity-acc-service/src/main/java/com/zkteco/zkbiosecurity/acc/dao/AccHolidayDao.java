/**
 * File Name: AccHoliday
 * Created by GenerationTools on 2018-02-26 下午05:53
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccHoliday;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import java.util.Collection;
import java.util.List;

/**
 * 对应百傲瑞达 AccHolidayDao
 * <AUTHOR>
 * @date:	2018-02-26 下午05:53
 * @version v1.0
 */
public interface AccHolidayDao extends BaseDao<AccHoliday, String> {
    AccHoliday findByName(String name);

    /**
     * @Description:    根据名称进行in查询
     * @Author:         Abel.huang
     * @CreateDate:     2018/12/13 11:26
     * @Version:        1.0
     */
    List<AccHoliday> findByNameIn(Collection<String> names);
}