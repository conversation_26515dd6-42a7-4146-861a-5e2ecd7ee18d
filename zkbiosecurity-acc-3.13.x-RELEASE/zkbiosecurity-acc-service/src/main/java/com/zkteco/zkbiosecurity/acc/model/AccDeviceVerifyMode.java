/**
 * File Name: AccDeviceVerifyMode
 * Created by GenerationTools on 2018-03-20 下午04:20
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 对应百傲瑞达实体 AccDeviceVerifyMode
 * <AUTHOR>
 * @date:	2018-03-20 下午04:20
 * @version v1.0
 */
@Entity
@Table(name = "ACC_DEVICE_VERIFYMODE")
@Getter
@Setter
@Accessors(chain=true)
public class AccDeviceVerifyMode extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;

	/**  */
	@ManyToOne
	@JoinColumn(name="DEV_ID",nullable = false)
	private AccDevice accDevice;

	/**  */
	@Column(name="VERIFY_NO",nullable=false)
	private Short verifyNo;

	/**  */
	@Column(name="NAME",length=100,nullable=false)
	private String name;
	public AccDeviceVerifyMode()
	{
	}

	public AccDeviceVerifyMode(Short verifyNo, String name, AccDevice accDevice)
	{
		this.verifyNo = verifyNo;
		this.name = name;
		this.accDevice = accDevice;
	}
}