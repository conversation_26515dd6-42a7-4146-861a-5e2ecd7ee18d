package com.zkteco.zkbiosecurity.acc.license;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.core.guard.annotation.InmutableClassSign;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.license.vo.IModuleAuthAcc;

@Component
@InmutableClassSign(module = ConstUtil.SYSTEM_MODULE_ACC)
public class AccModuleAuthCheck implements IModuleAuthAcc {
    @Autowired
    private AccDeviceService accDeviceService;

    @Override
    public boolean enable() {
        return true;
    }

    @Override
    public String module() {
        return ConstUtil.SYSTEM_MODULE_ACC;
    }

    @Override
    public Map<String, Integer> controlCount() {
        return accDeviceService.getBeforeLicensedCount();
    }
}
