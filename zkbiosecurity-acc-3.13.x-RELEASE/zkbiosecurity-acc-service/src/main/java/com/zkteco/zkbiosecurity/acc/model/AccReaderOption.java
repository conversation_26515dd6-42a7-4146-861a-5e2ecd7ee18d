/**
 * File Name: AccReaderOption Created by GenerationTools on 2018-03-13 上午10:06 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 AccReaderOption
 * 
 * <AUTHOR>
 * @date: 2018-03-13 上午10:06
 * @version v1.0
 */
@Entity
@Table(name = "ACC_READER_OPTION")
@Getter
@Setter
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class AccReaderOption extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**  */
    @ManyToOne
    @JoinColumn(name = "READER_ID")
    private AccReader accReader;

    /**  */
    @Column(name = "OPTION_NAME", length = 50, nullable = false)
    private String name;

    /**  */
    @Column(name = "OPTION_VALUE", length = 150, nullable = false)
    private String value;

    /**  */
    @Column(name = "OPTION_TYPE", nullable = false)
    private Short type;
}