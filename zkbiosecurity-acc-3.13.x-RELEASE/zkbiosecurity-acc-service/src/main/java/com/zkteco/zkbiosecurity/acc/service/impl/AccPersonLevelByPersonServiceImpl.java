package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.dao.AccLevelDao;
import com.zkteco.zkbiosecurity.acc.dao.AccPersonDao;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonLevelByPersonService;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelByPersonItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelItem;
import com.zkteco.zkbiosecurity.auth.provider.AuthSessionProvider;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2018/3/14 11:12
 */
@Service
@Transactional
public class AccPersonLevelByPersonServiceImpl implements AccPersonLevelByPersonService {

    @Autowired
    private AccPersonDao accPersonDao;
    @Autowired
    private AccLevelDao accLevelDao;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private AuthSessionProvider authSessionProvider;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private AuthUserService authUserService;

    @Override
    public List<AccPersonLevelByPersonItem> getByCondition(AccPersonLevelByPersonItem condition) {
        return (List<AccPersonLevelByPersonItem>)accPersonDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accPersonDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AccPersonLevelByPersonItem getItemById(String id) {
        List<AccPersonLevelByPersonItem> items = getByCondition(new AccPersonLevelByPersonItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public List<AccPersonLevelItem> getExportItemList(AccPersonLevelItem accPersonLevelItem, int beginIndex,
        int endIndex) {
        return accLevelDao.getItemsDataBySql(AccPersonLevelItem.class, SQLUtil.getSqlByItem(accPersonLevelItem),
            beginIndex, endIndex, true);
    }

    @Override
    public Map<String, String> getAuthAreaByPersonLevel(List<AccPersonLevelItem> accPersonLevelItemList) {
        List<String> authAreaIdList = (List<String>)CollectionUtil.getPropertyList(accPersonLevelItemList,
            AccPersonLevelItem::getAuthAreaId, "-1");
        List<AuthAreaItem> authAreaItemList = authAreaService.getItemsByIds(authAreaIdList);
        return (Map)Optional.ofNullable(authAreaItemList).filter((l) -> {
            return !l.isEmpty();
        }).map((l) -> {
            return (Map)l.stream().collect(Collectors.toMap(AuthAreaItem::getId, AuthAreaItem::getName));
        }).orElse(Collections.emptyMap());
    }

    @Override
    public Pager loadPagerByAuthFilter(String sessionId, AccPersonLevelByPersonItem condition, int pageNo,
        int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        condition.setEnabledCredential(true);
        return getItemsByPage(condition, pageNo, pageSize);
    }

    @Override
    public ZKResultMsg syncPersonLevel(String sessionId, String personIds) {
        String authAreaIds = authSessionProvider.getAreaIdsNoSubNodesByAuthFilter(sessionId);
        List<AccPersonLevelItem> accPersonLevelItemLists =
            accLevelService.getAccPersonLevelItemsByCondition(authAreaIds, personIds);
        Map<String, List<AccPersonLevelItem>> persLevelMap =
            accPersonLevelItemLists.stream().collect(Collectors.groupingBy(AccPersonLevelItem::getPersonId));
        if (Objects.nonNull(persLevelMap)) {
            for (String personId : persLevelMap.keySet()) {
                List<AccPersonLevelItem> accPersonLevelItems = persLevelMap.get(personId);
                String levelIds = CollectionUtil.getItemIds(accPersonLevelItems);
                String[] levelIdArray = levelIds.split(",");
                for (int i = 0, total = levelIdArray.length; i < total; i++) {
                    // 判断权限组中是否已经有添加门
                    boolean isSetToDev = accLevelService.getDoorCountByLevelId(levelIdArray[i]) > 0;
                    if (isSetToDev) {
                        accLevelService.setPersonLevelTimesToDev(levelIdArray[i], personId);
                    }
                    progressCache.setProcess(new ProcessBean(0, (int)((i + 1) * 1.0 / total * 100)));
                }
            }
            progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish"));
        }
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }
}
