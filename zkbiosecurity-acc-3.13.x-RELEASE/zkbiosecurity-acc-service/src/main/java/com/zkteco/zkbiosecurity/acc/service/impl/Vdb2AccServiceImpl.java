package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.dao.AccLinkageMediaDao;
import com.zkteco.zkbiosecurity.acc.model.AccLinkageMedia;
import com.zkteco.zkbiosecurity.acc.service.AccLinkageMediaService;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;
import com.zkteco.zkbiosecurity.vdb.service.Vdb2OtherService;

/**
 * <AUTHOR>
 * @date 2024/3/26 13:46
 */
@Service
public class Vdb2AccServiceImpl implements Vdb2OtherService {
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccLinkageMediaDao accLinkageMediaDao;
    @Autowired
    private AccLinkageMediaService accLinkageMediaService;

    @Override
    public void checkVdbDeviceIsUsedByDevSns(List<String> devSn) {
        Integer count = accDeviceDao.countBySnIn(devSn);
        if (count > 0) {
            throw new ZKBusinessException(
                I18nUtil.i18nCode("common_prompt_canNotDel", I18nUtil.i18nCode("acc_module")));
        }
    }

    @Override
    public void deleteVdbIvrByIds(String ivrIds) {
        if (StringUtils.isNotBlank(ivrIds)) {
            List<String> ivrIdList = StrUtil.strToList(ivrIds);
            List<AccLinkageMedia> deleteLinkageMedias = new ArrayList<>();
            List<AccLinkageMedia> ivrLinkageMediaList =
                accLinkageMediaDao.findByMediaContentInAndMediaType(ivrIdList, AccConstants.VDB_IVR);
            if (ivrLinkageMediaList != null && ivrLinkageMediaList.size() > 0) {
                List<AccLinkageMedia> linkageMedia4Notify =
                    accLinkageMediaDao.getVdbLinkageMedias(ivrIdList, AccConstants.VDB_IVR, AccConstants.VDB);
                deleteLinkageMedias.addAll(ivrLinkageMediaList);
                deleteLinkageMedias.addAll(linkageMedia4Notify);
                // 删除关于ivr的联动信息
                accLinkageMediaDao.delete(deleteLinkageMedias);
            }
        }
    }

    @Override
    public void deleteVdbExtensionByIds(String extensionIds) {
        if (StringUtils.isNotBlank(extensionIds)) {
            List<String> extensionIdList = StrUtil.strToList(extensionIds);
            // 找出分机所在的联动下的所有分机和ivr信息
            List<AccLinkageMedia> extensionLinkageMediaList =
                accLinkageMediaDao.getVdbLinkageMediasByParam(extensionIdList, AccConstants.VDB_IVR, AccConstants.VDB);
            Map<String, List<AccLinkageMedia>> extensionLinkageMediaMap = extensionLinkageMediaList.stream().collect(
                Collectors.groupingBy(accLinkageMedia -> accLinkageMedia.getAccLinkage().getId(), Collectors.toList()));
            List<AccLinkageMedia> deleteLinkageMedias = new ArrayList<>();
            extensionLinkageMediaMap.forEach((accLinkageId, accLinkageMediaList) -> {
                List<String> linkageExtensionIds = new ArrayList<>();
                Map<String, AccLinkageMedia> accLinkageMediaMap =
                    CollectionUtil.listToKeyMap(accLinkageMediaList, AccLinkageMedia::getMediaContent);
                for (AccLinkageMedia accLinkageMedia : accLinkageMediaList) {
                    if (AccConstants.VDB == accLinkageMedia.getMediaType()) {
                        linkageExtensionIds.add(accLinkageMedia.getMediaContent());
                    }
                }
                // 复制一份id用于存放已有的分机id，用于取交集
                List<String> tempIdList = new ArrayList<>(extensionIdList);
                tempIdList.retainAll(linkageExtensionIds);
                // 原有权限组id去除交集部分得出为需要删除的权限组id
                linkageExtensionIds.removeAll(tempIdList);
                if (linkageExtensionIds != null && linkageExtensionIds.size() == 0) {
                    deleteLinkageMedias.addAll(accLinkageMediaList);
                } else {
                    for (String extensionId : tempIdList) {
                        deleteLinkageMedias.add(accLinkageMediaMap.get(extensionId));
                    }
                }
            });
            accLinkageMediaDao.delete(deleteLinkageMedias);
        }
    }
}
