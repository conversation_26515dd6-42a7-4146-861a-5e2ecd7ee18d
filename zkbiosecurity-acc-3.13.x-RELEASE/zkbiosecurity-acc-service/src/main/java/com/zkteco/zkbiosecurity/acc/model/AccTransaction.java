package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.convert.EncryptConverter;
import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 AccTransaction
 * 
 * <AUTHOR>
 * @date: 2018-03-07 11:16:25
 * @version v1.0
 */
@Entity
@Table(name = "ACC_TRANSACTION",
    indexes = {@Index(name = "ACC_TRANSACTION_CRT_IDX", columnList = "CREATE_TIME"),
        @Index(name = "ACC_TRANSACTION_UPT_IDX", columnList = "UPDATE_TIME"),
        @Index(name = "ACC_TRANSACTION_EVT_IDX", columnList = "EVENT_TIME")})
@Getter
@Setter
@Accessors(chain = true)
public class AccTransaction extends BaseModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 事件唯一标识 */
    @Column(name = "UNIQUE_KEY", length = 250, unique = true)
    private String uniqueKey;

    /** 事件id */
    @Column(name = "LOG_ID")
    private Integer logId;

    /** 事件时间 */
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "EVENT_TIME", nullable = false)
    private Date eventTime;

    /** 人员编号 */
    @Column(name = "PIN", length = 30)
    private String pin;

    /** 姓名 */
    @Column(name = "NAME", length = 50)
    private String name;

    /** 姓氏 */
    @Column(name = "LAST_NAME", length = 50)
    private String lastName;

    /** 部门编号 */
    @Column(name = "DEPT_CODE", length = 100)
    private String deptCode;

    /** 部门名称 */
    @Column(name = "DEPT_NAME", length = 100)
    private String deptName;

    /** 区域名称 */
    @Column(name = "AREA_NAME", length = 100)
    private String areaName;

    /** 人员卡号 */
    @Convert(converter = EncryptConverter.class)
    @Column(name = "CARD_NO")
    private String cardNo;

    /** 设备id */
    @Column(name = "DEV_ID")
    private String devId;

    /** 设备序列号 */
    @Column(name = "DEV_SN", length = 30)
    private String devSn;

    /** 设备名称 */
    @Column(name = "DEV_ALIAS", length = 100)
    private String devAlias;

    /** 验证方式编号 */
    @Column(name = "VERIFY_MODE_NO")
    private Short verifyModeNo;

    /** 验证方式名称 */
    @Column(name = "VERIFY_MODE_NAME", length = 500)
    private String verifyModeName;

    /** 事件编号 */
    @Column(name = "EVENT_NO", nullable = false)
    private Short eventNo;

    /** 事件名称 */
    @Column(name = "EVENT_NAME", length = 100)
    private String eventName;

    /** 事件点类型 */
    @Column(name = "EVENT_POINT_TYPE")
    private Short eventPointType;

    /** 事件点id */
    @Column(name = "EVENT_POINT_ID")
    private String eventPointId;

    /** 事件点名称 */
    @Column(name = "EVENT_POINT_NAME", length = 100)
    private String eventPointName;

    /** 读头状态 */
    @Column(name = "READER_STATE")
    private Short readerState;

    /** 读头名称 */
    @Column(name = "READER_NAME", length = 100)
    private String readerName;

    /** 联动触发条件 */
    @Column(name = "TRIGGER_COND")
    private Short triggerCond;

    /** 描述 */
    @Column(name = "DESCRIPTION", length = 200)
    private String description;

    /** 媒体文件 */
    @Column(name = "VID_LINKAGE_HANDLE", length = 256)
    private String vidLinkageHandle;

    /** 抓拍照片路径 */
    @Column(name = "CAPTURE_PHOTO_PATH", length = 200)
    private String capturePhotoPath;

    /** 门禁区域名称 */
    @Column(name = "ACC_ZONE", length = 30)
    private String accZone;

    /** 门禁区域编号 */
    @Column(name = "ACC_ZONE_CODE", length = 30)
    private String accZoneCode;

    /** 事件点地址 */
    @Column(name = "EVENT_ADDR")
    private Short eventAddr;

    /**
     * 是否带口罩 0：表示没有佩戴口罩；1：表示有佩戴口罩
     */
    @Column(name = "MASK_FLAG", length = 10)
    private String maskFlag;

    /** 体温 */
    @Column(name = "TEMPERATURE", length = 50)
    private String temperature;

    /**
     * 优先级 0-3
     */
    @Column(name = "EVENT_PRIORITY")
    private Short eventPriority;
}
