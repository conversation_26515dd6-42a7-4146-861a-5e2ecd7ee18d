/**
 * File Name: AccCombOpenDoor
 * Created by GenerationTools on 2018-03-14 下午03:02
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 对应百傲瑞达实体 AccCombOpenDoor
 * <AUTHOR>
 * @date:	2018-03-14 下午03:02
 * @version v1.0
 */
@Entity
@Table(name = "ACC_COMBOPEN_DOOR")
@Getter
@Setter
@Accessors(chain=true)
public class AccCombOpenDoor extends BaseModel implements Serializable {

	/** */
	private static final long serialVersionUID = 1L;

	/**  */
	@ManyToOne
	@JoinColumn(name="DOOR_ID",nullable = false)
	private AccDoor accDoor;
	@Column(name = "BUSINESS_ID",unique = true)
	private Long businessId;

	/**  */
	@Column(name="NAME",length=30,nullable=false,unique = true)
	private String name;

	/**  */
	@OneToMany(mappedBy="accCombOpenDoor",cascade = CascadeType.ALL)
	private List<AccCombOpenComb> accCombOpenCombList = new ArrayList<>();

}