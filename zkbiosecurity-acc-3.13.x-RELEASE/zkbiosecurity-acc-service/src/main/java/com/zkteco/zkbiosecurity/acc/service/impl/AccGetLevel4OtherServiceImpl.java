package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.dao.AccLevelDoorDao;
import com.zkteco.zkbiosecurity.acc.dao.AccPersonCombOpenPersonDao;
import com.zkteco.zkbiosecurity.acc.dao.AccPersonDao;
import com.zkteco.zkbiosecurity.acc.model.AccPerson;
import com.zkteco.zkbiosecurity.acc.model.AccPersonCombOpenPerson;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonCacheService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/3/9 15:13
 * @since 1.0.0
 */
@Service
public class AccGetLevel4OtherServiceImpl implements AccGetLevel4OtherService {

    @Autowired
    private AccTimeSegService accTimeSegService;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccPersonDao accPersonDao;
    @Autowired
    private AccPersonCombOpenPersonDao accPersonCombOpenPersonDao;
    @Autowired
    private AccLevelDoorDao accLevelDoorDao;
    @Autowired
    private PersPersonCacheService persPersonCacheService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AccLevelService accLevelService;

    @Override
    public void setPersonLevel2Dev(AccPersonInfo4OtherItem item, List<String> doorIds) {
        String timeSegId = accTimeSegService.getInitTimeSegId();
        // 时间段业务ID
        Long timeSegBId = accTimeSegService.getBusinessIdByTimeSegId(timeSegId);

        // 门信息
        List<AccDoorItem> accDoorItemList = accDoorService.getItemsByIds(doorIds);
        List<Short> doorNoList = new ArrayList<>();
        accDoorItemList.forEach(doorItem -> {
            doorNoList.add(doorItem.getDoorNo());
        });
        AccPersonInfoItem personInfoItem = buildAccPersonOptItem(item);
        List<AccDeviceItem> devItemList = accDeviceService.getDevItemByDoorIds(doorIds);
        List<AccPersonLevelOptItem> accPersonLevelOptItemList = new ArrayList<>();
        // 组装门禁所需字段
        List<AccPersonOptItem> personOptItemList = setAccPersonOptItem(personInfoItem.getPersonList());

        List<AccBioTemplateItem> bioTemplateItemList = personInfoItem.getAccBioTemplateItemList();
        devItemList.forEach(devItem -> {
            personOptItemList.forEach(personOpt -> {
                AccPersonLevelOptItem accPersonLevelOptItemNew = new AccPersonLevelOptItem();
                accPersonLevelOptItemNew.setDeviceId(devItem.getBusinessId());
                accPersonLevelOptItemNew.setTimeSegId(timeSegBId);
                accPersonLevelOptItemNew.setDoorNoList(doorNoList);
                accPersonLevelOptItemNew.setPin(personOpt.getPin());
                accPersonLevelOptItemList.add(accPersonLevelOptItemNew);
            });
            if (accPersonLevelOptItemList.size() > 0) {
                accDevCmdManager.setPersonLevelToDev(devItem.getSn(), accPersonLevelOptItemList, false);
            }
            if (bioTemplateItemList.size() > 0) {
                accDevCmdManager.setPersonBioTemplateToDev(devItem.getSn(), bioTemplateItemList, false);
            }
            if (personOptItemList.size() > 0) {
                accDevCmdManager.setPersonToDev(devItem.getSn(), personOptItemList, false);
            }
        });
    }

    /**
     * 组装门禁参数
     *
     * @param personOptItemList:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccPersonOptItem>
     * <AUTHOR>
     * @date 2021-03-16 9:08
     * @since 1.0.0
     */
    private List<AccPersonOptItem> setAccPersonOptItem(List<AccPersonOptItem> personOptItemList) {
        // 门禁人员信息
        List<String> personIdList =
            (List<String>)CollectionUtil.getPropertyList(personOptItemList, AccPersonOptItem::getId, "-1");
        List<AccPerson> accPersonList = accPersonDao.findByPersonIdIn(personIdList);
        Map<String, AccPerson> accPersonMap = CollectionUtil.listToKeyMap(accPersonList, AccPerson::getId);

        // 查询是否有设置多人开门组
        List<AccPersonCombOpenPerson> accPersonCombOpenPersonAllList =
            accPersonCombOpenPersonDao.findByPersPersonIdIn(personIdList);
        Map<String, AccPersonCombOpenPerson> accPersonCombOpenPersonMap =
            CollectionUtil.listToKeyMap(accPersonCombOpenPersonAllList, AccPersonCombOpenPerson::getPersPersonId);

        for (AccPersonOptItem accPersonOptItem : personOptItemList) {
            AccPerson accPerson = accPersonMap.get(accPersonOptItem.getId());
            if (accPerson != null) {
                accPersonOptItem.setStartTime(accPerson.getStartTime());
                accPersonOptItem.setEndTime(accPerson.getEndTime());
                accPersonOptItem.setSuperAuth(accPerson.getSuperAuth());
                accPersonOptItem.setDisabled(accPerson.getDisabled());
                accPersonOptItem.setPrivilege(accPerson.getPrivilege());
                accPersonOptItem.setDelayPassage(accPerson.getDelayPassage() != null
                    ? "true".equals(accPerson.getDelayPassage().toString()) ? true : false : false);
            } else {
                accPersonOptItem.setStartTime(null);
                accPersonOptItem.setEndTime(null);
                accPersonOptItem.setSuperAuth((short)0);
                accPersonOptItem.setDisabled(false);
                accPersonOptItem.setPrivilege((short)0);
                accPersonOptItem.setDelayPassage(false);
            }
            AccPersonCombOpenPerson accPersonCombOpenPerson = accPersonCombOpenPersonMap.get(accPersonOptItem.getId());
            accPersonOptItem.setGroupId(
                accPersonCombOpenPerson != null ? accPersonCombOpenPerson.getAccCombOpenPerson().getBusinessId() : 0);
        }
        return personOptItemList;
    }

    @Override
    public void delPersonLevel2Dev(List<String> personPinList, List<String> doorIdList, boolean isSyncLevelData) {
        if (!personPinList.isEmpty()) {
            List<AccDeviceItem> devItemList = accDeviceService.getDevItemByDoorIds(doorIdList);
            if (isSyncLevelData) {
                List<PersPersonCacheItem> personCacheItemList =
                    persPersonCacheService.getPersonCacheByPins(personPinList);
                Map<String, Collection<String>> personInfoMap = setLevelMap(personCacheItemList);
                accLevelService.delPersonLevelToDevice(devItemList, personInfoMap);
            } else {
                for (AccDeviceItem devItem : devItemList) {
                    // 删除权限命令
                    accDevCmdManager.delPersonLevelFromDev(devItem.getSn(), personPinList, false);
                    // 删除指纹
                    accDevCmdManager.delPersonBioTemplateFromDev(devItem.getSn(), personPinList, false);
                    // 删除人员信息
                    accDevCmdManager.delPersonFromDev(devItem.getSn(), personPinList, false);
                }
            }
        }
    }

    private Map<String, Collection<String>> setLevelMap(List<PersPersonCacheItem> personCacheItemList) {
        Map<String, Collection<String>> delParamMap = new HashMap<>();
        List<String> idList = new ArrayList<>();
        List<String> pinList = new ArrayList<>();
        for (PersPersonCacheItem personInfo : personCacheItemList) {
            idList.add(personInfo.getId());
            pinList.add(personInfo.getPin());
        }
        delParamMap.put("id", idList);
        delParamMap.put("pin", pinList);
        return delParamMap;
    }

    /**
     * 组装人员信息
     *
     * @param item:
     * @return com.zkteco.zkbiosecurity.acc.vo.AccPersonInfoItem
     * <AUTHOR>
     * @date 2021-03-14 13:20
     * @since 1.0.0
     */
    private AccPersonInfoItem buildAccPersonOptItem(AccPersonInfo4OtherItem item) {
        AccPersonInfoItem personInfoItem = new AccPersonInfoItem();
        // 组装人员信息
        List<AccPerson4OtherItem> person4OtherItems = item.getPersonList();
        List<AccPersonOptItem> accPersonOptItems = new ArrayList<>();
        person4OtherItems.forEach(person -> {
            AccPersonOptItem personOptItem = new AccPersonOptItem();
            ModelUtil.copyProperties(person, personOptItem);
            accPersonOptItems.add(personOptItem);
        });
        if (accPersonOptItems.size() > 0) {
            personInfoItem.setPersonList(accPersonOptItems);
        }
        // 组装人员生物模板
        List<AccBioTemplateItem> accBioTemplateItems = new ArrayList<>();
        List<AccBioTemplate4OtherItem> bioTemplate4OtherItems = item.getBioTemplateItemList();
        bioTemplate4OtherItems.forEach(bioTemplate -> {
            AccBioTemplateItem bioTemplateItem = new AccBioTemplateItem();
            ModelUtil.copyProperties(bioTemplate, bioTemplateItem);
            accBioTemplateItems.add(bioTemplateItem);
        });
        if (accBioTemplateItems.size() > 0) {
            personInfoItem.setAccBioTemplateItemList(accBioTemplateItems);
        }
        return personInfoItem;
    }
}
