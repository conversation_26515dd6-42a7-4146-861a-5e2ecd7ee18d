package com.zkteco.zkbiosecurity.acc.model;

import javax.persistence.*;

import org.hibernate.annotations.GenericGenerator;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date Created In 10:36 2020/3/31
 */
@Entity
@Table(name = "ACC_ALARM_BEVENTNUM")
@Getter
@Setter
@Accessors(chain = true)
public class AccAlarmMonitorBEventNum {
    /**
     * 时间编号（自增）
     */
    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "seq_acc_alarmMonitor")
    @GenericGenerator(name = "seq_acc_alarmMonitor", strategy = "org.hibernate.id.enhanced.SequenceStyleGenerator",
        parameters = {@org.hibernate.annotations.Parameter(name = "sequence_name", value = "seq_acc_alarmMonitor")})
    private Long id;
}
