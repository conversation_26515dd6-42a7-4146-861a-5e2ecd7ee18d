package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccDoor;
import com.zkteco.zkbiosecurity.acc.model.AccDoorVerifyModeRule;
import com.zkteco.zkbiosecurity.acc.model.AccVerifyModeRule;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface AccDoorVerifyModeRuleDao extends BaseDao<AccDoorVerifyModeRule, String> {

    @Modifying
    @Query("delete from AccDoorVerifyModeRule where accVerifyModeRule.id in ?1 and accDoor.id in ?2")
    void delDoorByRuleIdAndDoorId(List<String> verifyModeRuleId, List<String> doorIds);

    /**
     * 获取已经设置过规则的doorid
     *
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2016年10月14日 上午9:59:15
     * @return
     */
    @Query(value = "select e.accDoor.id from AccDoorVerifyModeRule e")
    List<String> getDoorIdByVerifyRule();

    @Query(value = "select e.accDoor from AccDoorVerifyModeRule e where e.accVerifyModeRule.id = ?1")
    List<AccDoor> getAccDoorByVerifyRuleId(String verifyRuleId);

    /**
     * 根据验证方式规则id和设备id查询出在该验证方式规则下的accDoorList
     * @author: mingfa.zheng
     * @date: 2018/5/30 18:02
     * @return:
     */
    @Query(value = "select e.accDoor from AccDoorVerifyModeRule e where e.accVerifyModeRule.id = ?1 and e.accDoor.device.id = ?2")
    List<AccDoor> getAccDoorByVerifyModeRuleIdAndDeviceId(String verifyModeRuleId, String deviceId);
}
