package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.park.service.Park4AccDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 门禁当停车场设备相关接口实现
 * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
 * @date 2019/1/14 9:40
 */
@Service
public class Park4AccDeviceServiceImpl implements Park4AccDeviceService {

    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;

    @Override
    public boolean enableBgVerify(String accDeviceId) {
        AccDevice accDevice = accDeviceDao.findById(accDeviceId).orElse(null);
        boolean backgroundVerifyFlag = false;
        if (Objects.nonNull(accDevice)) {
            String autoServerMode = accDeviceOptionService.getValueByNameAndDevSn(accDevice.getSn(),"AutoServerMode");
            if (AccConstants.ENABLE == Short.valueOf(autoServerMode)) {
                backgroundVerifyFlag = true;
            }
        }
        return backgroundVerifyFlag;
    }
}
