package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 对应百傲瑞达实体 置顶门model
 * <AUTHOR>
 * @date:	2019-07-11 上午10:27
 * @version v1.0
 */
@Entity
@Table(name = "ACC_TOPDOOR_BY_PERSON")
@Getter
@Setter
@Accessors(chain=true)
public class AccAppTopDoorByPerson extends BaseModel implements Serializable {
    /** */
    private static final long serialVersionUID = 1L;

    /** 人员ID */
    @Column(name="PERSON_ID", nullable=false)
    private String personId;

    /** 门ID */
    @Column(name="DOOR_ID", nullable=false)
    private String doorId;
}
