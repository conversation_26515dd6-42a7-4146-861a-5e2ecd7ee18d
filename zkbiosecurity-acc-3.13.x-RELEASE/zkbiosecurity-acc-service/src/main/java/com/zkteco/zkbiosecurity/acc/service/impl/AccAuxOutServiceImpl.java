/**
 * File Name: AccAuxOutServiceImpl Created by GenerationTools on 2018-03-14 上午09:38 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccAuxOutDao;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.dao.AccTimeSegDao;
import com.zkteco.zkbiosecurity.acc.model.AccAuxOut;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.model.AccTimeSeg;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.Acc4IVideoControlEntityItem;
import com.zkteco.zkbiosecurity.acc.vo.AccAuxOutItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccQueryDeviceItem;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;

/**
 * 对应百傲瑞达 AccAuxOutServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-14 上午09:38
 * @version v1.0
 */
@Service
@Transactional
public class AccAuxOutServiceImpl implements AccAuxOutService {
    @Autowired
    private AccAuxOutDao accAuxOutDao;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccTimeSegDao accTimeSegDao;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccExtDeviceService accExtDeviceService;
    @Autowired
    private AuthUserService authUserService;
    @Autowired(required = false)
    private Acc4IVideoControlEntityService acc4IVideoControlEntityService;

    @Override
    public AccAuxOutItem saveItem(AccAuxOutItem item) {
        AccAuxOut accAuxOut = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accAuxOutDao.findById(id)).orElse(new AccAuxOut());
        ModelUtil.copyPropertiesIgnoreNull(item, accAuxOut);
        if (StringUtils.isNotBlank(item.getAccTimeSegId())) {
            accAuxOut.setTimeSegId(item.getAccTimeSegId());// 设置常开时间段
        } else {
            accAuxOut.setTimeSegId(null);
        }
        // 保存入库
        accAuxOut.setRemark(accAuxOut.getRemark().replace("\r\n", ""));

        accAuxOutDao.save(accAuxOut);


        accDeviceService.updateDevInfoWithDoorAndAuxBySn(accAuxOut.getAccDevice().getSn());
        // 下发参数到设备
        if (accDeviceOptionService.isSupportFun(accAuxOut.getAccDevice().getSn(), "OutRelaySetFunOn")) {
            if (StringUtils.isNotBlank(accAuxOut.getTimeSegId())) {
                List<AccAuxOutItem> accAuxOutList = new ArrayList<>();
                item.setAuxNo(accAuxOut.getAuxNo());
                accAuxOutList.add(item);
                accDevCmdManager.setAuxOutOptToDev(accAuxOut.getAccDevice().getSn(), accAuxOutList, false);
            } else {
                List<Short> accAuxOutNoList = new ArrayList<>();
                accAuxOutNoList.add(accAuxOut.getAuxNo());
                // 下发删除辅助输出设置命令
                accDevCmdManager.delAuxOutOptFromDev(accAuxOut.getAccDevice().getSn(), accAuxOutNoList, false);
            }
        }
        // 发送视频集成模块，更新辅助输出名称
        if (acc4IVideoControlEntityService != null) {
            Acc4IVideoControlEntityItem acc4IVideoControlEntityItem = new Acc4IVideoControlEntityItem();
            acc4IVideoControlEntityItem.setEntityId(accAuxOut.getId());
            acc4IVideoControlEntityItem.setEntityName(accAuxOut.getName());
            acc4IVideoControlEntityItem.setEntityClassName("AccAuxOut");
            acc4IVideoControlEntityService.updateEntityNameByEntityId(acc4IVideoControlEntityItem);
        }
        item.setId(accAuxOut.getId());
        return item;
    }

    @Override
    public List<AccAuxOutItem> getByCondition(AccAuxOutItem condition) {
        return (List<AccAuxOutItem>)accAuxOutDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accAuxOutDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accAuxOutDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AccAuxOutItem getItemById(String id) {
        List<AccAuxOutItem> items = getByCondition(new AccAuxOutItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public Map<String, String> getAuxOutIds(List<String> idList) {
        List<AccAuxOut> auxOutList = accAuxOutDao.findByIdIn(idList);
        StringBuffer auxOutsName = new StringBuffer();
        StringBuffer disabledAuxOutsName = new StringBuffer();
        StringBuffer offlineAuxOutsName = new StringBuffer();
        // 用于操作辅助输出系统日志所需
        StringBuffer auxOutNames = new StringBuffer();
        AccAuxOut accAuxOut = null;
        StringBuffer retIds = new StringBuffer();
        Map<String, String> retMap = new HashMap<String, String>();// 用于存放（设备名：辅助输出名）的结果
        Map<String, String> disabledMap = new HashMap<String, String>();// 用于存放（设备名：辅助输出名）的结果
        Map<String, String> offlineMap = new HashMap<String, String>();// 用于存放（设备名：辅助输出名）的结果
        for (int i = 0; i < auxOutList.size(); i++) {
            accAuxOut = auxOutList.get(i);
            AccDevice dev = accAuxOut.getAccDevice();
            short connect = ConstUtil.DEV_STATE_OFFLINE;
            String devState = accDeviceService.getStatus(dev.getSn());
            // String devStateRet = accCacheManager.getString(ConstUtil.DEV_RTMONITOR_STATE + dev.getSn());
            // JSONObject retJson = JSON.parseObject(devStateRet);
            if (StringUtils.isNotBlank(devState)) {
                connect = Short.valueOf(devState);
            }
            if (connect == AccConstants.DEV_STATE_ONLINE) {
                retIds.append(accAuxOut.getId()).append(",");
                // 若辅助输出所对应的设备已经存在于Map中，则在对应value后追加辅助输出名称
                if (retMap.containsKey(dev.getAlias())) {
                    retMap.put(dev.getAlias(), retMap.get(dev.getAlias()) + ", " + accAuxOut.getName());
                } else {
                    retMap.put(dev.getAlias(), accAuxOut.getName());
                }
            } else if (connect == AccConstants.DEV_STATE_DISABLE) {
                if (disabledMap.containsKey(dev.getAlias())) {
                    disabledMap.put(dev.getAlias(), disabledMap.get(dev.getAlias()) + ", " + accAuxOut.getName());
                } else {
                    disabledMap.put(dev.getAlias(), accAuxOut.getName());
                }
            } else {
                if (offlineMap.containsKey(dev.getAlias())) {
                    offlineMap.put(dev.getAlias(), offlineMap.get(dev.getAlias()) + ", " + accAuxOut.getName());
                } else {
                    offlineMap.put(dev.getAlias(), accAuxOut.getName());
                }
            }
        }
        for (String key : retMap.keySet()) {
            auxOutsName.append(key).append("=").append(retMap.get(key)).append("#");
            auxOutNames.append(retMap.get(key)).append(",");
        }

        for (String key : disabledMap.keySet()) {
            disabledAuxOutsName.append(key).append("=").append(disabledMap.get(key)).append("#");
        }

        for (String key : offlineMap.keySet()) {
            offlineAuxOutsName.append(key).append("=").append(offlineMap.get(key)).append("#");
        }
        retMap.put("retIds", retIds.toString().equals("") ? "" : retIds.substring(0, retIds.length() - 1));
        retMap.put("auxOutsName",
            StringUtils.isNotBlank(auxOutsName) ? auxOutsName.substring(0, auxOutsName.length() - 1) : "");
        retMap.put("disabledAuxOutsName", StringUtils.isNotBlank(disabledAuxOutsName)
            ? disabledAuxOutsName.substring(0, disabledAuxOutsName.length() - 1) : "");
        retMap.put("offlineAuxOutsName", StringUtils.isNotBlank(offlineAuxOutsName)
            ? offlineAuxOutsName.substring(0, offlineAuxOutsName.length() - 1) : "");
        retMap.put("auxOutNames",
            StringUtils.isNotBlank(auxOutNames) ? auxOutNames.substring(0, auxOutNames.length() - 1) : "");
        return retMap;
    }

    @Override
    public boolean isExist(AccAuxOutItem item) {
        AccAuxOut accAuxOut = accAuxOutDao.findByNameAndAccDevice_Id(item.getName(), item.getDevId());
        if (accAuxOut == null) {
            return true;
        }
        return false;
    }

    @Override
    public boolean isSupportOutRelaySet(String devSn) {
        return accDeviceOptionService.isSupportFun(devSn, "OutRelaySetFunOn");
    }

    @Override
    public ZKResultMsg getAuxOutStatus(String sessionId) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        AccAuxOutItem item = new AccAuxOutItem();
        if (StringUtils.isNotBlank(userId)) {
            item.setUserId(userId);
        }
        List<AccAuxOutItem> accAuxOutList =
            (List<AccAuxOutItem>)accAuxOutDao.getItemsBySql(item.getClass(), SQLUtil.getSqlByItem(item));
        JSONArray dataArray = new JSONArray();
        if (Objects.nonNull(accAuxOutList) && !accAuxOutList.isEmpty()) {
            JSONObject objJson = null;
            for (AccAuxOutItem auxOut : accAuxOutList) {
                objJson = new JSONObject();
                AccQueryDeviceItem devItem = accDeviceService.getQueryItemBySn(auxOut.getDevSn());
                if (devItem != null) {
                    objJson.put("id", auxOut.getId());
                    objJson.put("areaId", devItem.getAuthAreaId());
                    objJson.put("devAlias", devItem.getAlias());
                    objJson.put("devSn", devItem.getSn());
                    objJson.put("no", auxOut.getAuxNo());
                    objJson.put("name", auxOut.getName());
                    objJson.put("image", "default");
                    dataArray.add(objJson);
                }
            }
        }
        return new ZKResultMsg(dataArray);
    }

    @Override
    public Pager loadPagerByAuthFilter(String sessionId, AccAuxOutItem condition, int pageNo, int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        return getItemsByPage(condition, pageNo, pageSize);
    }

    @Override
    public List<AccAuxOutItem> getItemsByDevIds(List<String> devIdList) {
        List<AccAuxOutItem> auxOutItems = new ArrayList<>();
        if (devIdList != null && devIdList.size() > 0) {
            AccAuxOutItem auxOutItem = new AccAuxOutItem();
            auxOutItem.setDevIdIn(StringUtils.join(devIdList, ","));
            auxOutItems = getByCondition(auxOutItem);
        }
        return auxOutItems;
    }

    @Override
    public List<AccAuxOutItem> getItemsByIds(List<String> auxOutIdList) {
        AccAuxOutItem condition = new AccAuxOutItem();
        condition.setInId(StringUtils.join(auxOutIdList, ","));
        return getByCondition(condition);
    }

    @Override
    public void handlerTransfer(List<AccAuxOutItem> auxoutItems) {
        // 按设备sn分组， key：pin value:
        Map<String, List<AccAuxOutItem>> deviceOpMap =
            auxoutItems.stream().collect(Collectors.groupingBy(AccAuxOutItem::getDevSn));
        // 获取数据库中原有的设备参数取出，用于比较
        List<List<String>> snsList = CollectionUtil.split(deviceOpMap.keySet(), CollectionUtil.splitSize);
        Map<String, AccAuxOut> optionAllMap = new HashMap<String, AccAuxOut>();
        Map<String, AccDevice> deviceAllMap = new HashMap<String, AccDevice>();
        // 数据大时分批处理
        for (List<String> sns : snsList) {
            List<AccAuxOut> ins = accAuxOutDao.findByAccDevice_SnIn(sns);
            for (AccAuxOut in : ins) {
                String key = in.getAccDevice().getSn() + "_" + in.getAuxNo();
                optionAllMap.put(key, in);
            }
            // 將所有的設備查詢出來
            List<AccDevice> devices = accDeviceDao.findBySnIn(sns);
            for (AccDevice accDevice : devices) {
                deviceAllMap.put(accDevice.getSn(), accDevice);
            }
        }
        // 检测判断数据后保存更新
        for (AccAuxOutItem inItem : auxoutItems) {
            AccAuxOut in = optionAllMap.remove(inItem.getDevSn() + "_" + inItem.getAuxNo());
            if (Objects.isNull(in)) {
                in = new AccAuxOut();
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(inItem, in, "id");
            in.setAccDevice(deviceAllMap.get(inItem.getDevSn()));
            // 这里的businessId就类似于编码，这个和旧架构的id是一样的 时间段
            if (StringUtils.isNotBlank(inItem.getAccTimeSegId())) {
                AccTimeSeg timeSeg = accTimeSegDao.findByBusinessId(Long.parseLong(inItem.getAccTimeSegId()));
                in.setTimeSegId(timeSeg.getId());
            }
            accAuxOutDao.save(in);
        }
        optionAllMap = null;
        deviceAllMap = null;
    }

    @Override
    public boolean checkTimeSegUsed(String timeSegId) {
        return accAuxOutDao.countByTimeSegId(timeSegId) > 0;
    }

    @Override
    public void saveAuxOutList(AccDeviceItem accDeviceItem, List<AccAuxOutItem> accAuxOutList) {
        List<AccAuxOut> accAuxOuts = new ArrayList<>();
        AccDevice accDevice = accDeviceDao.findOne(accDeviceItem.getId());
        for (AccAuxOutItem item : accAuxOutList) {
            AccAuxOut auxOut = ModelUtil.copyProperties(item, new AccAuxOut());
            auxOut.setTimeSegId(item.getAccTimeSegId());
            if (null != accDevice) {
                auxOut.setAccDevice(accDevice);
            }
            accAuxOuts.add(auxOut);
        }
        if (!accAuxOuts.isEmpty()) {
            accAuxOutDao.save(accAuxOuts);
            accDevice.setAccAuxOutList(accAuxOuts);
            accDeviceDao.save(accDevice);
        }
    }

    @Override
    public AccAuxOutItem saveSimpleItem(AccAuxOutItem item) {
        AccAuxOut accAuxOut = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accAuxOutDao.findById(id)).orElse(new AccAuxOut());
        ModelUtil.copyPropertiesIgnoreNull(item, accAuxOut);
        if (StringUtils.isNotBlank(item.getAccTimeSegId())) {
            // 设置常开时间段
            accAuxOut.setTimeSegId(item.getAccTimeSegId());
        } else {
            accAuxOut.setTimeSegId(null);
        }
        if (StringUtils.isNotBlank(item.getDevId())) {
            AccDevice accDevice = accDeviceDao.findById(item.getDevId()).orElse(null);
            if (Objects.nonNull(accDevice)) {
                accAuxOut.setAccDevice(accDevice);
            }
        }
        // 保存入库
        accAuxOutDao.save(accAuxOut);
        item.setAuxNo(accAuxOut.getAuxNo());
        item.setId(accAuxOut.getId());
        return item;
    }

    @Override
    public AccAuxOutItem getItemByAuxNoAndDevId(short auxNo, String devId) {
        AccAuxOut accAuxOut = accAuxOutDao.findByAuxNoAndAccDevice_Id(auxNo, devId);
        AccAuxOutItem accAuxOutItem = null;
        if (Objects.nonNull(accAuxOut)) {
            accAuxOutItem = ModelUtil.copyProperties(accAuxOut, new AccAuxOutItem());
            AccDevice accDevice = accAuxOut.getAccDevice();
            accAuxOutItem.setDevId(accDevice.getId());
            accAuxOutItem.setDevSn(accDevice.getSn());
            accAuxOutItem.setDevAlias(accDevice.getSn());
        }
        return accAuxOutItem;
    }

    @Override
    public List<Short> getAuxOutNoByDevId(String devId) {
        return accAuxOutDao.findAuxOutNoByDevId(devId);
    }

    @Override
    public int countByTimeSegId(String timeSegId) {
        return accAuxOutDao.countByTimeSegId(timeSegId);
    }

    @Override
    public String getAuxOutSimpleName() {
        return AccAuxOut.class.getSimpleName();
    }
}