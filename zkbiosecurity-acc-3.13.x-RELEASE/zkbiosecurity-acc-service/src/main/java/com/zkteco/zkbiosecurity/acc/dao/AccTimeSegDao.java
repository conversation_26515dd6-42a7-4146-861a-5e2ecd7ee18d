/**
 * File Name: AccTimeSeg Created by GenerationTools on 2018-02-24 上午10:18 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.acc.model.AccTimeSeg;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 对应百傲瑞达 AccTimeSegDao
 * 
 * <AUTHOR>
 * @date: 2018-02-24 上午10:18
 * @version v1.0
 */
public interface AccTimeSegDao extends BaseDao<AccTimeSeg, String> {
    AccTimeSeg findByInitFlag(Boolean initFlag);

    List<AccTimeSeg> findByIdNotIn(List<String> ids);

    AccTimeSeg findByName(String name);

    @Query(
        value = "SELECT d.name as doorName, t.name as timeSegName FROM acc_door d LEFT JOIN acc_timeseg t ON t.id = d.active_timeseg_id WHERE d.id IN (?1)",
        nativeQuery = true)
    List<Object[]> getActiveTimeSegByDoorId(List<String> doorIdList);

    @Query(
        value = "SELECT d.name as doorName, t.name as timeSegName FROM acc_door d JOIN acc_timeseg t ON t.id = d.passmode_timeseg_id WHERE d.id IN (?1)",
        nativeQuery = true)
    List<Object[]> getPassmodeTimeSegByDoorId(List<String> doorIdList);

    /**
     * 根据businessId获取时间段
     * 
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午10:38:24
     * @param businessId
     * @return
     */
    AccTimeSeg findByBusinessId(Long businessId);

    /**
     * 根据ids获取时间段
     * 
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午10:38:24
     * @param businessIds
     * @return
     */
    List<AccTimeSeg> findByBusinessIdIn(List<Long> businessIds);

    /**
     * 根据名称获取时间段
     * 
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午10:38:24
     * @param names
     * @return
     */
    List<AccTimeSeg> findByNameIn(Collection<String> names);

    /**
     * 根据时间段id获取业务id
     * 
     * @param timeSegId
     * @return
     */
    @Query(value = "select businessId from AccTimeSeg where id = ?1")
    Long getBusinessIdByTimeSegId(String timeSegId);

}