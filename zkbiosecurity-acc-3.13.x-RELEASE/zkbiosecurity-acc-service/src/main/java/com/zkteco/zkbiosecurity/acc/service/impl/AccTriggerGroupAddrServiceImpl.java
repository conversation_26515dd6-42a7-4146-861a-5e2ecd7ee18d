package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.dao.AccTriggerGroupAddrDao;
import com.zkteco.zkbiosecurity.acc.service.AccTriggerGroupAddrService;
import com.zkteco.zkbiosecurity.acc.vo.AccTriggerGroupAddrItem;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;

/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2024/3/8 14:19
 * @since 1.0.0
 */
@Service
@Transactional
public class AccTriggerGroupAddrServiceImpl implements AccTriggerGroupAddrService {
    @Autowired
    private AccTriggerGroupAddrDao accTriggerGroupAddrDao;

    @Override
    public List<AccTriggerGroupAddrItem> getItemsByTriggerGroupId(String groupId) {
        AccTriggerGroupAddrItem accTriggerGroupAddrItem = new AccTriggerGroupAddrItem();
        accTriggerGroupAddrItem.setTriggerGroupId(groupId);
        return (List<AccTriggerGroupAddrItem>)accTriggerGroupAddrDao.getItemsBySql(accTriggerGroupAddrItem.getClass(),
            SQLUtil.getSqlByItem(accTriggerGroupAddrItem));
    }
}
