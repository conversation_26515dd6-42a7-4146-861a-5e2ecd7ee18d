package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.vms.service.Vms4AccDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020/11/2 16:49
 * @since 1.0.0
 */
@Component
public class Vms4AccDeviceServiceImpl implements Vms4AccDeviceService {

    @Autowired
    private AccDeviceService accDeviceService;

    @Override
    public Boolean checkDeviceExistBySn(List<String> devSnList) {
        List<AccDeviceItem> accDeviceItemList = accDeviceService.getItemsByDevSnIn(devSnList);
        return Objects.nonNull(accDeviceItemList) && !accDeviceItemList.isEmpty();
    }
}
