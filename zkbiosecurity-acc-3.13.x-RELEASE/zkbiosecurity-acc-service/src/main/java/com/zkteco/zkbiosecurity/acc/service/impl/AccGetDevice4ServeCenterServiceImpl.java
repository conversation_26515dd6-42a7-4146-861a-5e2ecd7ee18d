package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.constants.AccCacheKeyConstants;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccRTMonitorService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccQueryDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccQueryDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccQueryReaderItem;
import com.zkteco.zkbiosecurity.adms.service.AdmsDeviceService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.device.constants.DeviceCenterConstants;
import com.zkteco.zkbiosecurity.device.service.Other2DeviceCenterService;
import com.zkteco.zkbiosecurity.device.vo.DeviceInfo4OtherItem;
import com.zkteco.zkbiosecurity.device.vo.DeviceInfoStatus4OtherItem;
import com.zkteco.zkbiosecurity.device.vo.DeviceSubsetInfo4OtherItem;

/**
 * <AUTHOR>
 * @date 2021/9/1 16:54
 * @since 1.0.0
 */
@Service
public class AccGetDevice4ServeCenterServiceImpl implements Other2DeviceCenterService {

    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private AccRTMonitorService accRTMonitorService;
    @Autowired(required = false)
    private AdmsDeviceService admsDeviceService;

    @Override
    public List<DeviceInfo4OtherItem> getDeviceFromModule() {
        List<DeviceInfo4OtherItem> deviceInfo4OtherItems = new ArrayList<>();
        // 获取redis缓存中的门禁设备信息keys,从而获取所有设备信息
        Set<String> keys = accCacheManager.getCacheKeySet(AccCacheKeyConstants.ACC_DEVICE_INFO + "*");
        if (keys.size() > 0) {
            List<String> snList = new ArrayList<>();
            for (String key : keys) {
                String sn = key.split(":")[2];
                if (admsDeviceService.existDevice(sn)) {
                    snList.add(sn);
                }
            }
            if (snList.size() > 0) {
                for (String devSn : snList) {
                    AccQueryDeviceItem item = accDeviceService.getQueryItemBySn(devSn);
                    List<AccQueryDoorItem> doorItemList = item.getAccDoorItemList();
                    ZKResultMsg doorStateRet = accRTMonitorService.getDoorState(item, doorItemList);
                    JSONArray doorStateArray = (JSONArray)doorStateRet.getData();
                    // 构建门状态和门的关系
                    Map<String, JSONObject> doorStatusMap = buildDoorStatusAndDoorMap(doorStateArray);
                    deviceInfo4OtherItems.add(buildDeviceInfo4OtherItem(item, doorItemList, doorStatusMap));
                }
            }
        }
        return deviceInfo4OtherItems;
    }

    /**
     * 构建门状态和门的关系
     *
     * @param doorStateArray:
     * @return java.util.Map<java.lang.String,com.alibaba.fastjson.JSONObject>
     * <AUTHOR>
     * @date 2021-09-02 9:41
     * @since 1.0.0
     */
    private Map<String, JSONObject> buildDoorStatusAndDoorMap(JSONArray doorStateArray) {
        Map<String, JSONObject> doorStatusAndDoorMap = new HashMap<>();
        if (Objects.nonNull(doorStateArray) && doorStateArray.size() > 0) {
            for (int i = 0; i < doorStateArray.size(); i++) {
                JSONObject doorStatus = doorStateArray.getJSONObject(i);
                String devSn = doorStatus.getString("devSn");
                String no = doorStatus.getString("no");
                doorStatusAndDoorMap.put(devSn + "_" + DeviceCenterConstants.SUBSET_TYPE_DOOR + "_" + no, doorStatus);
            }
        }
        return doorStatusAndDoorMap;
    }

    /**
     * 组装设备信息
     *
     * @param accDeviceItem:
     * @param accDoorItemList:
     * @param doorStatusMap:
     * @return com.zkteco.zkbiosecurity.device.vo.DeviceInfo4OtherItem
     * <AUTHOR>
     * @date 2021-09-02 9:40
     * @since 1.0.0
     */
    private DeviceInfo4OtherItem buildDeviceInfo4OtherItem(AccQueryDeviceItem accDeviceItem,
        List<AccQueryDoorItem> accDoorItemList, Map<String, JSONObject> doorStatusMap) {
        DeviceInfo4OtherItem deviceInfo4OtherItem = new DeviceInfo4OtherItem();
        deviceInfo4OtherItem.setDeviceSn(accDeviceItem.getSn());
        deviceInfo4OtherItem.setDeviceName(accDeviceItem.getDeviceName());
        deviceInfo4OtherItem.setDeviceAlias(accDeviceItem.getAlias());
        deviceInfo4OtherItem.setDeviceModelName(accDeviceItem.getDeviceName());
        deviceInfo4OtherItem.setDeviceVersion(accDeviceItem.getFwVersion());
        deviceInfo4OtherItem.setDeviceIp(accDeviceItem.getIpAddress());
        deviceInfo4OtherItem.setDevicePort(accDeviceItem.getIpPort() + "");
        deviceInfo4OtherItem.setAreaId(accDeviceItem.getAuthAreaId());
        deviceInfo4OtherItem.setAreaName(accDeviceItem.getAuthAreaName());
        deviceInfo4OtherItem.setSourceModule(ConstUtil.SYSTEM_MODULE_ACC);
        deviceInfo4OtherItem.setStatus(admsDeviceService.getDevStatus(accDeviceItem.getSn()));
        deviceInfo4OtherItem.setReserve(accDeviceItem.getId());
        if (!accDoorItemList.isEmpty()) {
            List<DeviceSubsetInfo4OtherItem> deviceSubnetInfoList = new ArrayList<>();
            List<DeviceSubsetInfo4OtherItem> readerInfoList = new ArrayList<>();
            for (AccQueryDoorItem accDoorItem : accDoorItemList) {
                DeviceSubsetInfo4OtherItem doorInfo = new DeviceSubsetInfo4OtherItem();
                doorInfo.setSubsetName(accDoorItem.getName());
                doorInfo.setSubsetNo(String.valueOf(accDoorItem.getDoorNo()));
                doorInfo.setReserve(accDoorItem.getId());
                doorInfo.setSubsetType(DeviceCenterConstants.SUBSET_TYPE_DOOR);
                doorInfo.setStatus(doorStatusMap.get(accDeviceItem.getSn() + "_"
                    + DeviceCenterConstants.SUBSET_TYPE_DOOR + "_" + accDoorItem.getDoorNo()));
                deviceSubnetInfoList.add(doorInfo);
                List<AccQueryReaderItem> readerItemList = accDoorItem.getAccReaderItemList();
                if (readerItemList != null && !readerItemList.isEmpty()) {
                    for (AccQueryReaderItem readerItem : readerItemList) {
                        DeviceSubsetInfo4OtherItem readerInfo = new DeviceSubsetInfo4OtherItem();
                        readerInfo.setSubsetName(readerItem.getName());
                        readerInfo.setSubsetNo(readerItem.getReaderNo() + "");
                        readerInfo.setReserve(readerItem.getId());
                        readerInfo.setParentSubSetId(accDoorItem.getId());
                        readerInfo.setSubsetType(DeviceCenterConstants.SUBSET_TYPE_READER);
                        readerInfoList.add(readerInfo);
                    }
                }
            }
            deviceSubnetInfoList.addAll(readerInfoList);
            deviceInfo4OtherItem.setDeviceSubsetInfo4OtherItems(deviceSubnetInfoList);
        }
        return deviceInfo4OtherItem;
    }

    @Override
    public List<DeviceInfoStatus4OtherItem> getDeviceStatusFromModule() {
        List<DeviceInfoStatus4OtherItem> deviceInfoStatus4OtherItemList = new ArrayList<>();
        Set<String> keys = accCacheManager.getCacheKeySet(AccCacheKeyConstants.ACC_DEVICE_INFO + "*");
        if (Objects.nonNull(keys) && keys.size() > 0) {
            for (String key : keys) {
                String[] keyArr = key.split(":", 3);
                AccQueryDeviceItem accQueryDeviceItem = accCacheManager.getDeviceInfo(keyArr[2]);
                DeviceInfoStatus4OtherItem deviceInfoStatus4OtherItem =
                    buildDeviceInfoStatus4OtherItem(accQueryDeviceItem);
                deviceInfoStatus4OtherItemList.add(deviceInfoStatus4OtherItem);
                buildDoorInfoStatus(deviceInfoStatus4OtherItemList, accQueryDeviceItem);
            }
        } else {
            // redis不存在，查询数据库，并更新缓存设备信息
            List<AccDeviceItem> accDeviceItemList = accDeviceService.getAllDeviceItems();
            if (Objects.nonNull(accDeviceItemList) && accDeviceItemList.size() > 0) {
                for (AccDeviceItem accDeviceItem : accDeviceItemList) {
                    // 更新缓存设备信息
                    AccQueryDeviceItem accQueryDeviceItem = accDeviceService.updateCacheDeviceInfo(accDeviceItem);
                    DeviceInfoStatus4OtherItem deviceInfoStatus4OtherItem =
                        buildDeviceInfoStatus4OtherItem(accQueryDeviceItem);
                    deviceInfoStatus4OtherItemList.add(deviceInfoStatus4OtherItem);
                    buildDoorInfoStatus(deviceInfoStatus4OtherItemList, accQueryDeviceItem);
                }
            }
        }
        return deviceInfoStatus4OtherItemList;
    }

    private DeviceInfoStatus4OtherItem buildDeviceInfoStatus4OtherItem(AccQueryDeviceItem accQueryDeviceItem) {
        DeviceInfoStatus4OtherItem deviceInfoStatus4OtherItem = new DeviceInfoStatus4OtherItem();
        deviceInfoStatus4OtherItem.setId(accQueryDeviceItem.getId());
        deviceInfoStatus4OtherItem.setStatus(admsDeviceService.getDevStatus(accQueryDeviceItem.getSn()));
        return deviceInfoStatus4OtherItem;
    }

    private void buildDoorInfoStatus(List<DeviceInfoStatus4OtherItem> deviceInfoStatus4OtherItemList,
        AccQueryDeviceItem item) {
        List<AccQueryDoorItem> doorItemList = item.getAccDoorItemList();
        ZKResultMsg doorStateRet = accRTMonitorService.getDoorState(item, doorItemList);
        JSONArray doorStateArray = (JSONArray)doorStateRet.getData();
        // 构建门状态和门的关系
        Map<String, JSONObject> doorStatusMap = buildDoorStatusAndDoorMap(doorStateArray);
        for (AccQueryDoorItem accDoorItem : doorItemList) {
            DeviceInfoStatus4OtherItem doorInfo = new DeviceInfoStatus4OtherItem();
            doorInfo.setId(accDoorItem.getId());
            doorInfo.setIsSubset(true);
            doorInfo.setSubsetStatus(doorStatusMap
                .get(item.getSn() + "_" + DeviceCenterConstants.SUBSET_TYPE_DOOR + "_" + accDoorItem.getDoorNo()));
            deviceInfoStatus4OtherItemList.add(doorInfo);
        }
    }

    @Override
    public String getServiceModule() {
        return ConstUtil.SYSTEM_MODULE_ACC;
    }
}
