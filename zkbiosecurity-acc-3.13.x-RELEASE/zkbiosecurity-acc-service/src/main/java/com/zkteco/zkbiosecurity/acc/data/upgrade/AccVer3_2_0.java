package com.zkteco.zkbiosecurity.acc.data.upgrade;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.acc.service.AccDSTimeService;
import com.zkteco.zkbiosecurity.acc.vo.AccDSTimeItem;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2021-11-23 18:18
 * @since 1.0.0
 */
@Slf4j
@Component
public class AccVer3_2_0 implements UpgradeVersionManager {

    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private AccDSTimeService accDSTimeService;

    @Override
    public String getModule() {
        return BaseConstants.ACC;
    }

    @Override
    public String getVersion() {
        return "v3.2.0";
    }

    @Override
    public boolean executeUpgrade() {
        // 夏令时-设置夏令时
        AuthPermissionItem accDsTime = authPermissionService.getItemByCode("AccDSTimeSetDSTime");
        if (accDsTime != null) {
            accDsTime.setAvailable(ZKConstant.FALSE);
            authPermissionService.saveItem(accDsTime);
        }
        // 设备-设置夏令时
        final AuthPermissionItem setDSTime = authPermissionService.getItemByCode("AccDeviceSetDSTime");
        if (setDSTime != null) {
            setDSTime.setAvailable(ZKConstant.FALSE);
            authPermissionService.saveItem(accDsTime);
        }

        AccDSTimeItem condition = new AccDSTimeItem();
        condition.setInitFlag(true);
        List<AccDSTimeItem> accDSTimeItems = accDSTimeService.getByCondition(condition);
        if (accDSTimeItems != null) {
            accDSTimeItems.stream().map(item -> {
                item.setInitFlag(false);
                return item;
            }).forEach(accDSTimeItem -> accDSTimeService.saveItem(accDSTimeItem));
        }

        // 增加和时区绑定的夏令时
        AccDSTimeItem accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc1000_0", "10010002", "04010003", "+1000",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc1000_1", "10010002", "04010003", "+1000",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0330_0", "03020002", "11010002", "-0330",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_1000_0", "03020002", "11010002", "-1000",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0200_0", "03050002", "09050002", "-0200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0930_0", "10010002", "04010003", "+0930",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0100_0", "03050000", "10050001", "-0100",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0400_0", "03020002", "11010002", "-0400",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0400_1", "09010000", "04010000", "-0400",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0400_2", "10010000", "03050000", "-0400",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0300_0", "03050622", "10050623", "-0300",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0300_1", "03020002", "11010002", "-0300",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0200_0", "03050002", "10050003", "+0200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0200_1", "03050003", "10050004", "+0200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0200_2", "03050003", "10050004", "+0200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0200_3", "03050502", "10050002", "+0200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0200_4", "03050500", "10050501", "+0200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0200_5", "03050000", "10050000", "+0200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0200_6", "03050500", "10050500", "+0200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0200_7", "03050600", "10050601", "+0200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        // accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0200_8", "01010500", "01050100", "+0200",
        // AccDSTimeItem.DSTIME_MODE_TWO, true);
        // accDSTimeService.initData()(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0600_0", "03020002", "11010002", "-0600",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0600_1", "04010002", "10050002", "-0600",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0600_2", "09010622", "04010622", "-0600",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc1300_0", "09050003", "04010004", "+1300",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0500_0", "03020000", "11010001", "-0500",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0500_1", "03020002", "11010002", "-0500",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0500_2", "03020002", "11010002", "-0500",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0500_3", "03020002", "11010002", "-0500",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0500_4", "03020002", "11010002", "-0500",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0800_0", "03020002", "11010002", "-0800",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0800_1", "03020002", "11010002", "-0800",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0330_0", "03040100", "09030300", "+0330",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0000_0", "03050001", "10050002", "+0000",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc1200_0", "11020002", "01030003", "+1200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc1200_1", "03050002", "10050003", "+1200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc1200_2", "09050002", "04010003", "+1200",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc1100_0", "10010002", "04010003", "+1100",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0700_0", "04010002", "10050002", "-0700",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0700_1", "03020002", "11010002", "-0700",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0100_0", "03050002", "10050003", "+0100",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0100_1", "03050002", "10050003", "+0100",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0100_2", "05030002", "04020003", "+0100",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0100_3", "03050002", "10050003", "+0100",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc0100_4", "03050002", "10050003", "+0100",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        accDSTimeItem = new AccDSTimeItem("acc_dsTimeUtc_0900_0", "03020002", "11010002", "-0900",
            AccDSTimeItem.DSTIME_MODE_TWO, true);
        accDSTimeService.initData(accDSTimeItem);
        return true;
    }
}
