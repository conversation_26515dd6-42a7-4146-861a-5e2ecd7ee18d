package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.dao.AccLevelDao;
import com.zkteco.zkbiosecurity.acc.service.Acc4OtherService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class Acc4OtherServiceImpl implements Acc4OtherService {
    @Autowired
    private AccLevelDao accLevelDao;

    @Override
    public List getLevelByItem(BaseItem condition) {
        return accLevelDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }
}
