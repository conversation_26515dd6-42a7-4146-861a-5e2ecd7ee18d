/**
 * File Name: AccDSTime Created by GenerationTools on 2018-02-28 下午02:21 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 门禁夏令时model
 *
 * <AUTHOR>
 * @date: 2018-02-28 下午02:21
 * @version v1.0
 */
@Entity
@Table(name = "ACC_DSTIME")
@Getter
@Setter
@Accessors(chain = true)
public class AccDSTime extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**  */
    @Column(name = "NAME", length = 100, nullable = false)
    private String name;

    /**  */
    @Column(name = "DSTIME_MODE", nullable = false)
    private Short dstimeMode;

    /**  */
    @Column(name = "START_TIME", length = 20, nullable = false)
    private String startTime;

    /**  */
    @Column(name = "END_TIME", length = 20, nullable = false)
    private String endTime;

    /**  */
    @Column(name = "INIT_FLAG")
    private Boolean initFlag;

    /**
     * 所属时区
     */
    @Column(name = "TIME_ZONE")
    private String timeZone;

    // pro
    /** 夏令时开始年份 */
    @Column(name = "START_YEAR", length = 20)
    private String startYear;

    /** 夏令时结束年份 */
    @Column(name = "END_YEAR", length = 20)
    private String endYear;

    /** 业务id */
    @Column(name = "BUSSINESS_ID")
    private Long bussinessId;
}