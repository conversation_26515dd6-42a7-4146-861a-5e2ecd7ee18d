/**
 * File Name: AccAntiPassback Created by GenerationTools on 2018-03-13 上午10:27 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.acc.model.AccAntiPassback;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 对应百傲瑞达 AccAntipassbackDao
 * 
 * <AUTHOR>
 * @date: 2018-03-13 上午10:27
 * @version v1.0
 */
public interface AccAntiPassbackDao extends BaseDao<AccAntiPassback, String> {
    List<AccAntiPassback> findByAccDevice_Id(String devId);

    Long countAccAntiPassbackByAccDevice_IdIn(List<String> devIdList);

    /**
     * 查询是否存在名字
     *
     * @param name
     * @return
     */
    boolean existsByName(String name);

    /**
     * 查询当前最大业务id
     *
     * @return
     */
    @Query("SELECT MAX(t.businessId) FROM AccAntiPassback t")
    Long findMaxBusinessId();
}