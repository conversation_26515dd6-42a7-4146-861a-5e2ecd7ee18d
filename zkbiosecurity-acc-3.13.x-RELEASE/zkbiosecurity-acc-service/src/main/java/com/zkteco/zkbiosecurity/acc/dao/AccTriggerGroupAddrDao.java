package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccTriggerGroupAddr;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2024/3/8 14:21
 * @since 1.0.0
 */
public interface AccTriggerGroupAddrDao extends BaseDao<AccTriggerGroupAddr, String> {

    /**
     * 通过触发组Id以及触发点查找数据
     * 
     * @param groupId
     * @param address
     * @return
     */
    AccTriggerGroupAddr findByAccTriggerGroup_IdAndAddress(String groupId, String address);
}
