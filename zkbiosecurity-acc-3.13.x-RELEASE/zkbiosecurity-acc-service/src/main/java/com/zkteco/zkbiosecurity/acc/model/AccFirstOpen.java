/**
 * File Name: AccFirstOpen Created by GenerationTools on 2018-03-14 下午03:02 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 AccFirstOpen
 * 
 * <AUTHOR>
 * @date: 2018-03-14 下午03:02
 * @version v1.0
 */
@Entity
@Table(name = "ACC_FIRSTOPEN")
@Getter
@Setter
@Accessors(chain = true)
public class AccFirstOpen extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**  */
    @Column(name = "TIMESEG_ID", nullable = false)
    private String timeSegId;

    /**  */
    @ManyToOne
    @JoinColumn(name = "DOOR_ID", nullable = false)
    private AccDoor accDoor;

    /**  */
    @OneToMany(mappedBy = "accFirstOpen")
    private Set<AccPersonFirstOpen> accPersonFirstOpenSet = new HashSet<>();

}