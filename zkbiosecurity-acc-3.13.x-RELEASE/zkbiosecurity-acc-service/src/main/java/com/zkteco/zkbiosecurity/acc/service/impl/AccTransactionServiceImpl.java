package com.zkteco.zkbiosecurity.acc.service.impl;

import java.io.File;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.nio.file.attribute.FileTime;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import com.zkteco.zkbiosecurity.acc.model.AccExceptionRecord;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.pers.service.PersPositionService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zkteco.zkbiosecurity.acc.api.vo.AccApiFirstInLastOutItem;
import com.zkteco.zkbiosecurity.acc.api.vo.AccApiTransactionItem;
import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.constants.AccCacheKeyConstants;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.*;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.model.AccDoor;
import com.zkteco.zkbiosecurity.acc.model.AccTransaction;
import com.zkteco.zkbiosecurity.acc.operate.AccLinkageOperate;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.utils.AccDataUtil;
import com.zkteco.zkbiosecurity.acc.utils.AccDeviceUtil;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.auth.provider.AuthSessionProvider;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.config.DataSourceConfig;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.esdc.service.Acc4EsdcTransactionService;
import com.zkteco.zkbiosecurity.esdc.vo.Acc4EsdcTransactionItem;
import com.zkteco.zkbiosecurity.event.constants.EventCenterConstants;
import com.zkteco.zkbiosecurity.event.service.Other2EventCenterService;
import com.zkteco.zkbiosecurity.event.vo.Other2EventCenterItem;
import com.zkteco.zkbiosecurity.guard.foldex.utils.FoldexUtil;
import com.zkteco.zkbiosecurity.hep.service.Hep4OtherTransactionService;
import com.zkteco.zkbiosecurity.hep.vo.Hep4OtherTransactionItem;
import com.zkteco.zkbiosecurity.license.provider.BaseLicenseProvider;
import com.zkteco.zkbiosecurity.line.service.Line4OtherService;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersIdentityCardInfoService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonCacheService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.util.PersRegularUtil;
import com.zkteco.zkbiosecurity.pers.vo.PersIdentityCardInfoItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem;
import com.zkteco.zkbiosecurity.push.enums.PushCenter4OtherEnum;
import com.zkteco.zkbiosecurity.push.service.PushCenter4OtherService;
import com.zkteco.zkbiosecurity.push.vo.PushCenter4OtherItem;
import com.zkteco.zkbiosecurity.sms.modem.service.SmsModem4OtherService;
import com.zkteco.zkbiosecurity.system.constants.BaseDataConstants;
import com.zkteco.zkbiosecurity.system.service.BaseDataCleanService;
import com.zkteco.zkbiosecurity.system.service.BaseMessageNotificationService;
import com.zkteco.zkbiosecurity.system.service.BaseSendMailService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseMessageNotificationItem;
import com.zkteco.zkbiosecurity.vid.service.Vid4OtherGetVidLinkageEventService;
import com.zkteco.zkbiosecurity.vid.service.Vid4OtherGetVidTransactionService;

import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Transactional
@Slf4j
public class AccTransactionServiceImpl implements AccTransactionService {

    @Value("${system.filePath:BioSecurityFile}")
    private String systemFilePath;
    @Value("${system.skin:default}")
    private String systemSkin;
    @Autowired
    private AccTransactionDao accTransactionDao;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccDeviceVerifyModeService accDeviceVerifyModeService;
    @Autowired
    private AccLinkageService accLinkageService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersIdentityCardInfoService persIdentityCardInfoService;
    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AccAuxOutDao accAuxOutDao;
    @Autowired
    private AccAuxInDao accAuxInDao;
    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private AccParamService accParamService;
    @Autowired(required = false)
    private Acc4PatrolTransactionService acc4PatrolTransactionService;
    @Autowired
    private AccLinkageOperate accLinkageOperate;
    @Autowired
    private AccLinkageTriggerDao accLinkageTriggerDao;
    @Autowired
    PersPositionService persPositionService;
    @Autowired
    private AccMonitorRedirectService accMonitorRedirectService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private BaseSendMailService baseSendMailService;
    @Autowired(required = false)
    private Acc4AccGlobalLinkageService acc4AccGlobalLinkageService;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private AccDeviceEventService accDeviceEventService;
    @Autowired
    private AccAlarmMonitorService accAlarmMonitorService;
    @Autowired(required = false)
    private AccGetVisTransactionService accGetVisTransactionService;
    @Autowired
    private AccRTMonitorService accRTMonitorService;
    @Autowired(required = false)
    private AccGetAccZoneService accGetAccZoneService;
    @Autowired(required = false)
    private Acc4AccZonePersonService acc4ZonePersonService;
    @Autowired
    private BaseLicenseProvider baseLicenseProvider;
    @Autowired(required = false)
    private AccCloudService accCloudService;
    @Autowired(required = false)
    private Acc4IVideoTransactionService acc4IVideoTransactionService;
    @Autowired(required = false)
    private Vid4OtherGetVidLinkageEventService Vid4OtherGetVidLinkageEventService;
    @Autowired(required = false)
    private Vid4OtherGetVidTransactionService vid4OtherGetVidTransactionService;
    @Autowired
    private BaseDataCleanService baseDataCleanService;
    @Autowired(required = false)
    private Acc4CvaTransactionService acc4CvaTransactionService;
    @Autowired(required = false)
    private Acc4LineTransactionService acc4LineTransactionService;
    @Autowired(required = false)
    private SmsModem4OtherService smsModem4OtherService;
    @Autowired(required = false)
    private Hep4OtherTransactionService hep4OtherTransactionService;
    @Autowired(required = false)
    private Acc4EsdcTransactionService acc4EsdcTransactionService;
    @Autowired
    private AuthSessionProvider authSessionProvider;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired(required = false)
    private Acc4VisVisitorLastAddr acc4VisVisitorLastAddr;
    @Autowired
    AccExceptionRecordService accExceptionRecordService;
    @Autowired
    AccExceptionRecordDao accExceptionRecordDao;
    @Autowired
    private AccFirstInLastOutService accFirstInLastOutService;
    @Autowired(required = false)
    private AccGetIVideoService accGetIVideoService;
    @Autowired
    private PersPersonCacheService persPersonCacheService;
    @Autowired(required = false)
    private Other2EventCenterService other2EventCenterService;
    @Autowired(required = false)
    private PushCenter4OtherService pushCenter4OtherService;
    @Autowired(required = false)
    private Line4OtherService line4OtherService;
    @Autowired(required = false)
    private AccTransaction4OtherService[] accTransaction4OtherServices;
    @Autowired
    private AccFirstInLastOutDao accFirstInLastOutDao;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private BaseMessageNotificationService baseMessageNotificationService;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccReaderService accReaderService;
    @Autowired
    Acc4AttTransactionExternalService acc4AttTransactionExternalService;
    @Autowired
    WechatService wechatService;

    @Override
    public AccTransactionItem saveItem(AccTransactionItem item) {
        AccTransaction accTransaction = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
                .flatMap(id -> accTransactionDao.findById(id)).orElse(new AccTransaction());
        ModelUtil.copyProperties(item, accTransaction);
        accTransactionDao.save(accTransaction);
        item.setId(accTransaction.getId());
        return item;
    }

    @Override
    public void deleteAllData() {
        accTransactionDao.truncateAll();
        // 清除一体机上传的考勤照片
        FileUtils.deleteDirectory(FileUtils.getLocalFullPath(AccConstants.LINKAGE_PHOTO_PATH));
    }

    @Override
    public void deleteAllDataByToday() {
        Date date = DateUtil.getTodayBeginTime();
        accTransactionDao.deleteAllByToday(date);
    }

    @Override
    public void deleteAllExceptionData() {
        accTransactionDao.deleteAllExceptionData();
    }

    @Override
    public void deleteAllExceptionDataExpand() {
        accTransactionDao.deleteAllExceptionDataExpand();
    }

    @Override
    public List<?> getItemData(Class targetClass, BaseItem condition, int beginIndex, int endIndex) {
        Class itemCls = condition.getClass();
        Field eventLevel = null;
        try {
            eventLevel = itemCls.getDeclaredField("eventLevel");
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        }
        String eventLevelVal = ZkBeanUtil.getProperty(condition, eventLevel.getName());
        StringBuilder sql = new StringBuilder();
        sql.append(SQLUtil.getSqlByItem(condition));
        if (eventLevelVal != null) {
            int orderByIndex = sql.indexOf("ORDER BY");
            if (String.valueOf(AccConstants.EVENT_NORMAL).equals(eventLevelVal)) {
                sql.insert(orderByIndex,
                        "AND (t.EVENT_NO < 20 AND t.EVENT_NO != 700 OR (t.EVENT_NO >= 200 AND t.EVENT_NO < 500) OR (t.EVENT_NO >= 4000 AND t.EVENT_NO < 5000))");
            } else if (String.valueOf(AccConstants.EVENT_WARNING).equals(eventLevelVal)) {
                sql.insert(orderByIndex,
                        "AND (t.EVENT_NO >= 20 AND t.EVENT_NO < 100 AND t.EVENT_NO != 28 OR (t.EVENT_NO >= 500 AND t.EVENT_NO != 700) OR (t.EVENT_NO >=5000 AND t.EVENT_NO <6000))");
            } else if (String.valueOf(AccConstants.EVENT_ALARM).equals(eventLevelVal)) {
                sql.insert(orderByIndex,
                        "AND (t.EVENT_NO >= 100 AND t.EVENT_NO < 200 OR t.EVENT_NO = 28 OR t.EVENT_NO = 700 OR (t.EVENT_NO >=6000 AND t.EVENT_NO <7000))");
            }
        }
        List<?> transactionList = accTransactionDao.getItemsDataBySql(targetClass, sql.toString(),
                beginIndex, endIndex, true);
        try {
            Map<Short, String> eventLevelValue = Maps.newHashMap();
            // 事件级别
            eventLevelValue.put((short) 0, I18nUtil.i18nCode("common_normal"));
            eventLevelValue.put((short) 1, I18nUtil.i18nCode("common_exception"));
            eventLevelValue.put((short) 2, I18nUtil.i18nCode("common_alarm"));
            for (int i = 0; i < transactionList.size(); i++) {
                BaseItem baseItem = (BaseItem) transactionList.get(i);
                // 获取对象验证方式名称属性
                Field field = itemCls.getDeclaredField("verifyModeName");
                String fieldValue = ZkBeanUtil.getProperty(baseItem, field.getName());
                if (StringUtils.isNotBlank(fieldValue)) {
                    String[] verifyModeNameArray = fieldValue.split(",");
                    String verifyModeName = "";
                    for (String name : verifyModeNameArray) {
                        if (AccConstants.NEW_VERIFY_MODE.get(AccConstants.NEW_VERIFY_MODE_LOGIC).equals(name)) {
                            continue;
                        }
                        verifyModeName += I18nUtil.i18nCode(name) + "+";
                    }
                    // 获取Item的Set方法，反射给对象属性赋值，兼容新验证方式国际化
                    Method setVerifyModeName = itemCls.getMethod("setVerifyModeName", String.class);
                    setVerifyModeName.invoke(baseItem, verifyModeName.substring(0, verifyModeName.length() - 1));
                }
                Field eventNo = itemCls.getDeclaredField("eventNo");
                String eventNoValue = ZkBeanUtil.getProperty(baseItem, eventNo.getName());
                Method setLevelAndEventPriority = itemCls.getMethod("setLevelAndEventPriority", String.class);
                setLevelAndEventPriority.invoke(baseItem,
                        eventLevelValue
                                .get(accDeviceEventService.getDefaultEventTypeById(Short.parseShort(eventNoValue))));
            }
        } catch (Exception e) {
            log.error("AccTransactionServiceImpl getItemData is error : ", e);
        }
        return transactionList;
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accTransactionDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size, long limit) {
        return accTransactionDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size,
                limit);
    }

    @Override
    public List<AccTransactionItem> getByCondition(AccTransactionItem condition) {
        return (List<AccTransactionItem>) accTransactionDao.getItemsBySql(condition.getClass(),
                SQLUtil.getSqlByItem(condition));
    }

    @Override
    public AccTransactionItem getItemById(String id) {
        List<AccTransactionItem> items = getByCondition(new AccTransactionItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accTransactionDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public void handleRTLog(String rtLogStr, String sn) {
        Map<String, String> fieldMap = new HashMap<>();
        if (StringUtils.isNotBlank(rtLogStr)) {
            log.debug(rtLogStr);
            List<AccTransactionItem> accLogList = new ArrayList<>();
            AccQueryDeviceItem accDevice = accDeviceService.getQueryItemBySn(sn);// 提高代码效率从缓存获取设备信息
            if (accDevice != null) {
                // Map<String, JSONObject> devInfoMap = getDetailDevSn(accDevice);
                // JSONObject detailDevInfo = devInfoMap.get(sn);// detailDevInfo
                Map<String, List<AccDeviceEventItem>> devEvents = getAllEvent(accDevice);// 配置集合缓存
                // 记录信息为值value------将记录分割为多条
                // String[] logsStrArray = rtLogStr.split("\r\n");// \n?
                // String tempDevSn = "";
                String devId = "";
                // JSONObject tempDevInfo = null;
                List<AccDeviceEventItem> devEventList = devEvents.get(sn);
                Map<Short, AccDeviceEventItem> devEvent = CollectionUtil.listToKeyMap(devEventList,
                        AccDeviceEventItem::getEventNo);
                Map<String, String> devFunMap = accDeviceOptionService.getDevOptionBySn(sn);// 配置集合缓存
                // for (String log : logsStrArray) {
                // tempDevSn = sn;
                // tempDevInfo = detailDevInfo;
                AccTransactionItem accTransaction = new AccTransactionItem();
                getFieldMap(fieldMap, rtLogStr);
                /*
                 * if (tempDevInfo == null || !tempDevInfo.getBoolean("enabled")) {
                 * continue;
                 * }
                 */
                boolean isSuportLogId = false;
                if (devFunMap.containsKey("LogIDFunOn")) {
                    isSuportLogId = "1".equals(devFunMap.get("LogIDFunOn")) ? true : false;
                }
                String accSupportFunList = devFunMap.containsKey("AccSupportFunList")
                        ? devFunMap.get("AccSupportFunList")
                        : "";
                boolean isMultiCard = accDeviceOptionService.getAccSupportFunListVal(7, accSupportFunList);
                // devId = tempDevInfo.getString("id");
                devId = accDevice.getId();
                // accTransaction
                // .setAreaName(tempDevInfo.containsKey("areaName") ?
                // tempDevInfo.getString("areaName") : "");
                accTransaction.setAreaName(
                        StringUtils.isNotBlank(accDevice.getAuthAreaName()) ? accDevice.getAuthAreaName() : "");
                // accTransaction.setAreaId(tempDevInfo.containsKey("areaId") ?
                // tempDevInfo.getString("areaId") : "");
                accTransaction
                        .setAreaId(StringUtils.isNotBlank(accDevice.getAuthAreaId()) ? accDevice.getAuthAreaId() : "");
                accTransaction.setDevId(devId);
                accTransaction.setDevSn(sn);
                accTransaction.setDevAlias(accDevice.getAlias());
                String logTime = fieldMap.get("time");// time
                Date date = DateUtil.stringToDate(logTime, "yyyy-MM-dd HH:mm:ss");
                accTransaction.setEventTime(date);
                short eventNo = !"".equals(fieldMap.get("event")) ? Short.parseShort(fieldMap.get("event")) : -1;// 事件类型
                AccDeviceEventItem eventItem = devEvent.get(eventNo);
                if (eventItem != null) {
                    accTransaction.setEventName(eventItem.getName());
                    if (AccConstants.EVENT_ALARM == eventItem.getEventLevel()
                            && ObjectUtils.isEmpty(eventItem.getEventPriority())) {
                        accTransaction.setEventPriority((short) 0);
                    } else if (ObjectUtils.isNotEmpty(eventItem.getEventPriority())) {
                        accTransaction.setEventPriority(eventItem.getEventPriority());
                    }
                } else {
                    accTransaction
                            .setEventName(I18nUtil.i18nCode(AccConstants.EVENT_NO_UNDEFINED) + "<" + eventNo + ">");
                }
                accTransaction.setEventNo(eventNo);

                String verifyModeNo = (devFunMap.containsKey("NewVFStyles")
                        && StringUtils.isNotBlank(devFunMap.get("NewVFStyles"))
                        && !"0".equals(devFunMap.get("NewVFStyles"))
                        && !String.valueOf(AccConstants.VERIFY_MODE_OTHERS).equals(fieldMap.get("verifytype")))
                        ? String.valueOf(AccDeviceUtil.binaryToDecimal(fieldMap.get("verifytype")))
                        : fieldMap.get("verifytype");// 验证方式，固件上传
                String pin = !"0".equals(fieldMap.get("pin")) ? fieldMap.get("pin") : "";
                String cardNo = "";
                if (fieldMap.containsKey("cardno")) {
                    cardNo = !"0".equals(fieldMap.get("cardno")) ? fieldMap.get("cardno") : "";
                    boolean isIdCardNo = isIdCardNumberCheck(cardNo);
                    // 判断是否支持一人多卡，设备传上来的是16进制;卡号是否为身份证号码，卡号为身份证号码不做转换
                    if (isMultiCard && StringUtils.isNotBlank(cardNo) && !isIdCardNo) {
                        cardNo = AccDeviceUtil.convertCardNo(cardNo, 16, 10);
                    }
                }

                // 设置是否佩戴口罩
                accTransaction.setMaskFlag(MapUtils.getString(fieldMap, "maskflag"));
                // 设置体温
                accTransaction.setTemperature(MapUtils.getString(fieldMap, "temperature"));

                // 联动//push上来的实时事件已经不再复用。比如联动。但是pull上来的实时监控仍然需要按照复用的解析。
                int linkId = AccConstants.FUNCTION_UNSUPPORT;// -1代表不支持
                if (fieldMap.containsKey("linkid")) {
                    String linkIdStr = fieldMap.get("linkid");
                    if (isMultiCard && StringUtils.isNotBlank(linkIdStr)) {
                        linkIdStr = AccDeviceUtil.convertCardNo(linkIdStr, 16, 10);
                    }
                    linkId = Integer.parseInt(linkIdStr);
                }
                int linkEvent = fieldMap.containsKey("linkevent") ? Integer.valueOf(fieldMap.get("linkevent"))
                        : devFunMap.containsKey("NewVFStyles") ? AccConstants.FUNCTION_UNSUPPORT
                        : Integer.valueOf(fieldMap.get("verifytype"));// -1代表不支持
                if (eventNo == AccConstants.EVENT_LINKCONTROL) {// pull设备需另加考虑
                    accTransaction.setTriggerCond((short) linkEvent);
                }
                int alarmType = fieldMap.containsKey("alarmtype") ? Integer.valueOf(fieldMap.get("alarmtype"))
                        : AccConstants.FUNCTION_UNSUPPORT;// -1代表不支持
                Map<String, Object> dataMap = getEventTypeDataRT(sn, devId, eventNo, verifyModeNo, pin, cardNo, linkId,
                        linkEvent, alarmType);// 解析事件类型、验证方式、人员信息
                String deptCode = dataMap.containsKey("deptCode") ? dataMap.get("deptCode").toString() : "";
                String deptName = dataMap.containsKey("deptName") ? dataMap.get("deptName").toString() : "";
                cardNo = dataMap.containsKey("cardNo") ? dataMap.get("cardNo").toString() : "";
                int eventPointType = dataMap.containsKey("eventPointType") ? (Integer) dataMap.get("eventPointType")
                        : AccConstants.EVENT_POINT_TYPE_NONE;// 事件点类型
                String description = dataMap.containsKey("description") ? dataMap.get("description").toString() : "";
                verifyModeNo = dataMap.containsKey("verifyModeNo") ? dataMap.get("verifyModeNo").toString()
                        : verifyModeNo;
                String verifyModeName = dataMap.containsKey("verifyModeName") ? dataMap.get("verifyModeName").toString()
                        : AccConstants.VERIFY_MODE_UNDEFINED + "<" + verifyModeNo + ">";
                // 设置人员信息
                accTransaction.setName(dataMap.containsKey("name") ? dataMap.get("name").toString() : "");
                accTransaction.setCardNo(cardNo);
                accTransaction.setLastName(dataMap.containsKey("lastName") ? dataMap.get("lastName").toString() : "");
                accTransaction.setPin(dataMap.containsKey("pin") ? dataMap.get("pin").toString() : "");
                accTransaction.setDeptCode(deptCode);
                accTransaction.setDeptName(deptName);
                accTransaction.setDeptId(dataMap.containsKey("deptId") ? dataMap.get("deptId").toString() : "");
                accTransaction
                        .setPersPersonId(
                                dataMap.containsKey("persPersonId") ? dataMap.get("persPersonId").toString() : "");

                // 身份证通行模式下，需要设置姓名 by juvenile.li add 20171212
                if (eventNo == AccConstants.EVENT_IDENTITY_CARD_PASS) { // 身份证通行
                    String key = AccCacheKeyConstants.IDENTITY_CARD_INFO_KEY + cardNo;// cardNo 当前为物理卡号
                    JSONObject jsonData = accCacheManager.getIdentityCardInfo(key);
                    if (jsonData != null && jsonData.containsKey("data")) {
                        JSONArray jsonArray = jsonData.getJSONArray("data");
                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                        accTransaction.setName(jsonObject.getString("Name"));// 设置姓名
                        accTransaction.setPin(jsonObject.getString("ID_Num"));// 身份证
                    }
                }

                // 验证方式
                accTransaction.setVerifyModeNo(Short.parseShort(verifyModeNo));
                accTransaction.setVerifyModeName(verifyModeName);
                // 事件内容、事件描述
                accTransaction.setDescription(description);

                // 事件点相关-类型和地址
                int eventAddr = fieldMap.containsKey("eventaddr") ? Integer.parseInt(fieldMap.get("eventaddr")) : -1;
                accTransaction.setEventAddr((short) eventAddr);
                Map<String, String> eventPointMap = getEventPointInfo(sn, eventPointType, eventAddr);// 获取事件点信息：
                // eventPointId,
                // eventPointName
                String eventPointId = eventPointMap.get("eventPointId");
                String eventPointName = eventPointMap.get("eventPointName");
                accTransaction.setEventPointType((short) eventPointType);
                accTransaction.setEventPointId(eventPointId);
                accTransaction.setEventPointName(eventPointName);
                accTransaction.setDoorName(eventPointMap.get("doorName"));
                accTransaction.setDoorId(eventPointMap.get("doorId"));

                // 出入状态
                if (fieldMap.containsKey("inoutstatus")) {
                    short state = Short.parseShort(fieldMap.get("inoutstatus"));
                    accTransaction.setReaderState(state);
                    String readerName = I18nUtil.i18nCode("common_reader_state_2");// 其他
                    if (state != 2) {
                        final AccQueryReaderItem accReader = getReaderCacheByDevSnAndDoorNoAndReaderState(sn,
                                eventAddr + "", state);
                        if (accReader != null) {
                            readerName = accReader.getName();
                            accTransaction.setReaderId(accReader.getId());
                            if (accGetAccZoneService != null) {
                                AccZoneInfo zone = accGetAccZoneService.getZoneByReaderId(accReader.getId());
                                if (zone != null) {
                                    accTransaction.setAccZone(zone.getZoneName());
                                    accTransaction.setAccZoneCode(zone.getZoneCode());
                                    accTransaction.setZoneId(zone.getId());
                                }
                            }
                        }
                    }
                    accTransaction.setReaderName(readerName);
                }

                int index = getIndex(fieldMap, isSuportLogId);
                String uniqueKey = getUniqueKey(accTransaction, sn, eventNo, logTime,
                        dataMap.get("linkIndex").toString(), isSuportLogId, index);

                accTransaction.setLogId(index);
                accTransaction.setUniqueKey(uniqueKey);
                // 硬联动执行发送邮件或者视频联动入口
                if (eventNo == AccConstants.EVENT_LINKCONTROL) {
                    accTransaction = linkageOperate(accTransaction, fieldMap, devId, linkId);
                } else {
                    // 一体机事件拍照事件则关联图片
                    if (devFunMap.containsKey("CameraOpen") && "1".equals(devFunMap.get("CameraOpen"))) {
                        accTransaction.setCapturePhotoPath(getAttPhoto(sn, logTime, pin));
                    }
                    // 逻辑联动
                    logicLinkage(accDevice, accTransaction);
                }

                accCacheManager.putLastRtLogToCache(accTransaction);
                Map<String, String> userData = getUserData(dataMap);
                if (eventNo != AccConstants.BACK_VERIFY_FAIL && eventNo != AccConstants.BACK_VERIFY) {
                    accLogList.add(accTransaction);
                    // 做为控制器读头的一体机事件不显示在监控页面
                    if ((accDevice.getWgReaderId() == null || "".equals(accDevice.getWgReaderId()))) {
                        // 添加到各个客户端监控列表里
                        int eventLevel = 0;
                        AccDeviceEventItem tempDevEvent = null;
                        if (StringUtils.isNotBlank(accTransaction.getDevId())) {
                            tempDevEvent = accDeviceEventService.getItemByDeviceIdAndEventNo(accTransaction.getDevId(),
                                    accTransaction.getEventNo());
                            eventLevel = getEventLevel(tempDevEvent, accTransaction.getEventNo());
                            if (eventLevel == AccConstants.EVENT_ALARM) { // 如果是报警事件则发送邮件
                                accAlarmMonitorService.sendAlarmMail("acc_rtMonitor_alarmEvent", accTransaction,
                                        "alarmEvent");
                                if (smsModem4OtherService != null) {
                                    accAlarmMonitorService.sendAlarmSMS(accTransaction, "alarmEvent");
                                }
                            }
                        } else {
                            eventLevel = getEventLevel(null, accTransaction.getEventNo());// 软件自定义事件
                        }
                        accTransaction.setEventLevel(eventLevel);// 事件级别

                        // 拷贝临时对象，修复敏感信息处理修改原本对象信息
                        AccTransactionItem tempTransactionItem = ModelUtil.copyProperties(accTransaction,
                                new AccTransactionItem());
                        accMonitorRedirectService.sendAccTransaction2RTMonitor(tempTransactionItem, userData,
                                tempDevEvent);// 发送到实时监控显示
                        accCacheManager.setTransactionToClient(
                                JSON.toJSONStringWithDateFormat(accTransaction, "yyyy-MM-dd HH:mm:ss"));// 设置事件记录到实时监控客户端中，api接口获取实时事件使用。

                        sendDataToPanelBoard(accTransaction);
                        // //将数据发送到LCD实时监控页面
                        if (eventNo != AccConstants.EVENT_LINKCONTROL) {
                            String photoPath = "";
                            if (dataMap.containsKey("photoPath")
                                    && StringUtils.isNotBlank(dataMap.get("photoPath") + "")) {
                                photoPath = dataMap.get("photoPath").toString();
                            }
                            accMonitorRedirectService.sendDataToLcdRtMonitoring(tempTransactionItem, photoPath);
                        }
                        // 推送支持温度检测口罩佩戴和红外温度的记录到防疫模块 add by lambert.li 20200224
                        if (Objects.nonNull(hep4OtherTransactionService)
                                && (accDeviceOptionService.isSupportFun(accTransaction.getDevSn(), "MaskDetectionFunOn")
                                || accDeviceOptionService.isSupportFun(accTransaction.getDevSn(),
                                "IRTempDetectionFunOn"))) {
                            Hep4OtherTransactionItem acc4HepTransactionItem = new Hep4OtherTransactionItem();
                            ModelUtil.copyProperties(accTransaction, acc4HepTransactionItem);
                            // 设置数据来源---acc
                            acc4HepTransactionItem.setIsFrom(ConstUtil.SYSTEM_MODULE_ACC);
                            hep4OtherTransactionService.pushTransactionsToHep(acc4HepTransactionItem);
                        }
                    }
                    // 往第三方发送数据
                    sendDataToThird(accTransaction);// 调整到这个位置，因为后台验证失败和后台验证事件不需要传给其他模块使用
                }
                if (acc4AccGlobalLinkageService != null) {
                    AccTransactionInfo tempTransaction = new AccTransactionInfo();
                    ModelUtil.copyPropertiesIgnoreNull(accTransaction, tempTransaction);
                    tempTransaction = acc4AccGlobalLinkageService.globalLinkage(tempTransaction, userData);// 全局联动入口
                    if (tempTransaction != null) {
                        AccTransactionItem globalLinkageTransaction = new AccTransactionItem();
                        ModelUtil.copyPropertiesIgnoreNull(tempTransaction, globalLinkageTransaction);
                        accLogList.add(globalLinkageTransaction);
                        // 推送视频集成模块(Digifort需要跟acc全局联动)
                        if (Objects.nonNull(acc4IVideoTransactionService)
                                && (baseLicenseProvider.licenseAvaiable(ConstUtil.SYSTEM_MODULE_DIGIFORT))) {
                            Acc4IVideoTransactionItem transactionItem = new Acc4IVideoTransactionItem();
                            ModelUtil.copyProperties(globalLinkageTransaction, transactionItem);
                            acc4IVideoTransactionService.pushIVideoTransactions(transactionItem);
                        }
                    }
                }
                // 消息推送，发送邮件、SMS等信息
                sendMessage(accTransaction, dataMap);
            }
            // }
            // 批量插入 改成 先往redis里放，后面慢慢保存到数据库里 优化事件实时性
            putAccTransactionsToCache(accLogList);
            // 上传门禁记录到云端最新调整：门禁记录不再上传至云端，调整为通过API接口方式到对应线下获取门禁记录 ----modify by
            // zhixiong.huang 2021-08-20 10:33
            // 触发推送门禁报表记录到云端
            // if (Objects.nonNull(accCloudService)) {
            // for (AccTransactionItem accTrans : accLogList) {
            // accCloudService.asyncPushTransactionToCloud(Collections.singletonList(accTrans));
            // }
            // }
            // 门禁记录推送到事件中心 modified by bob.liu 2021-02-19
            if (Objects.nonNull(other2EventCenterService)) {
                Other2EventCenterItem other2EventCenterItem = null;
                for (AccTransactionItem accTrans : accLogList) {
                    other2EventCenterItem = new Other2EventCenterItem();
                    conversionEventItem(other2EventCenterItem, accTrans);
                    other2EventCenterService.push2EventCenter(other2EventCenterItem);
                }
            }
        }
    }

    /**
     * 消息推送，发送邮件、SMS等信息
     *
     * @param accTransaction:
     * @param dataMap:
     * @return void
     * <AUTHOR>
     * @date 2023-03-24 15:08
     * @since 1.0.0
     */
    private void sendMessage(AccTransactionItem accTransaction, Map<String, Object> dataMap) {
        String pin = accTransaction.getPin();
        if (null != pin && !"".equals(pin.trim())) {
            BaseMessageNotificationItem baseMessageNotificationItem = new BaseMessageNotificationItem();
            baseMessageNotificationItem.setPin(pin);
            baseMessageNotificationItem.setModule(ConstUtil.SYSTEM_MODULE_ACC);
            // Email
            if (Objects.nonNull(dataMap.get("email"))) {
                String subject = I18nUtil.i18nCode(accTransaction.getEventName());
                baseMessageNotificationItem.setIsSendMail(Boolean.parseBoolean(dataMap.get("isSendEmail") + ""));
                baseMessageNotificationItem.setEmail((String) dataMap.get("email"));
                baseMessageNotificationItem.setSubject(subject);
            }
            // SMS
            if (Objects.nonNull(dataMap.get("mobilePhone"))) {
                String mobilePhone = dataMap.get("mobilePhone").toString().replace("-", "");
                baseMessageNotificationItem.setSendSMS(Boolean.parseBoolean(dataMap.get("sendSMS") + ""));
                baseMessageNotificationItem.setMobilePhone(mobilePhone);
            }
            // Whatsapp
            if (Objects.nonNull(dataMap.get("whatsappMobileNo"))) {
                String whatsappMobileNo = (String) dataMap.get("whatsappMobileNo");
                baseMessageNotificationItem.setSendWhatsapp(Boolean.parseBoolean(dataMap.get("isSendWhatsapp") + ""));
                baseMessageNotificationItem.setWhatsappMobileNo(whatsappMobileNo);
            }
            // 内容
            String content = getMailContent(accTransaction);
            baseMessageNotificationItem.setContent(content);
            baseMessageNotificationService.sendMessage(baseMessageNotificationItem);
        }
    }

    /**
     * 逻辑联动；设置了输出点执行硬联动，未设置输出点执行逻辑联动
     *
     * @param accDevice:设备信息
     * @param accTransaction:事件信息
     * @return void
     * <AUTHOR>
     * @date 2022-07-19 11:12
     * @since 1.0.0
     */
    private void logicLinkage(AccQueryDeviceItem accDevice, AccTransactionItem accTransaction) {
        String devSn = accDevice.getSn();
        // 1.获取设置的联动信息
        List<AccQueryLinkageItem> linkageItemList = accLinkageService.getAccQueryLinkageItemList(devSn);
        if (linkageItemList.isEmpty()) {
            return;
        }
        // 2.联动信息中输出点为空、且该事件设置了逻辑联动的linkageId
        String linkageId = StringUtils.EMPTY;
        Integer linkageIndex = null;
        for (AccQueryLinkageItem queryLinkageItem : linkageItemList) {
            // 联动配置的触发条件
            List<AccLinkageTriggerItem> triggerItemList = queryLinkageItem.getAccLinkageTriggerList();
            Map<Short, AccLinkageTriggerItem> triggerMap = CollectionUtil.listToKeyMap(triggerItemList,
                    AccLinkageTriggerItem::getTriggerCond);
            // 触发条件中不包含该事件
            if (!triggerMap.containsKey(accTransaction.getEventNo())) {
                continue;
            }

            // 联动配置的输入输出
            List<AccLinkageInOutItem> linkageInOutItemList = queryLinkageItem.getAccLinkageInOutList();
            for (AccLinkageInOutItem inOutItem : linkageInOutItemList) {
                // 逻辑联动outputId和outputType为空,所以过滤
                if (StringUtils.isNotBlank(inOutItem.getOutputId())
                        || StringUtils.isNotBlank(inOutItem.getOutputType())) {
                    break;
                }
                // 此事件设置了逻辑联动,且输入点与事件点/读头ID匹配
                if (inOutItem.getInputId().equals(accTransaction.getEventPointId())
                        || inOutItem.getInputId().equals(accTransaction.getReaderId())
                        || inOutItem.getInputId().equals(AccConstants.LINKAGE_ANY)) {
                    linkageId = queryLinkageItem.getId();
                    // 取触发事件的联动下标,修复获取事件错误
                    linkageIndex = triggerMap.get(accTransaction.getEventNo()).getLinkageIndex();
                    break;
                }
            }
        }
        // 3.触发联动
        if (StringUtils.isNotBlank(linkageId) && linkageIndex != null) {
            log.info("execute logic linkage and linkageId = " + linkageId);
            ZKResultMsg linkageResult = accLinkageOperate.executeLogicLinkage(linkageId, accTransaction);
            // 联动描述
            Map<String, String> retMap = accLinkageService.getLinkageAction(linkageIndex, accDevice.getId());
            if (retMap.containsKey("description")) {
                accTransaction.setDescription(retMap.get("description"));
            }
            // 设置事件联动结果信息
            setTransactionLinkageInfo(linkageResult, accTransaction);
        }
    }

    /**
     * 设置事件联动信息
     *
     * @param linkageResult:联动结果
     * @param accTransaction:事件信息
     * @return void
     * <AUTHOR>
     * @date 2022-07-19 15:46
     * @since 1.0.0
     */
    private AccTransactionItem setTransactionLinkageInfo(ZKResultMsg linkageResult, AccTransactionItem accTransaction) {
        if (Objects.nonNull(linkageResult.getData())) {
            Map<String, Object> retMap = (Map<String, Object>) linkageResult.getData();
            accTransaction.setVidLinkageHandle(retMap.get("type") + "_" + retMap.get("vidLinkageHandle"));
            accTransaction.setVidLinkageData(retMap.get("photoFile"));
            accTransaction.setVidLinkageFilePathData(retMap.get("photoFilePath"));
            accTransaction.setCaptureTime(accTransaction.getCaptureTime());
            accTransaction.setVidLinkageVideoChannelId(retMap.get("videoChannelId"));
            // 判断是否有digifort事件触发
            if (retMap.containsKey("digifortEventNames")) {
                String digifortDescription = (StringUtils.isNotBlank(accTransaction.getDescription())
                        ? accTransaction.getDescription() + ";"
                        : "") + "digifortEventNames:" + retMap.get("digifortEventNames");
                accTransaction.setDescription(digifortDescription);
            }
        }
        return accTransaction;
    }

    /**
     * 检测卡号是否为身份证号码
     *
     * @param cardNo:卡号
     * @return boolean
     * <AUTHOR>
     * @date 2022-05-19 17:02
     * @since 1.0.0
     */
    private boolean isIdCardNumberCheck(String cardNo) {
        boolean isIdCardNo = false;
        if (StringUtils.isNotBlank(cardNo)) {
            isIdCardNo = PersRegularUtil.idNumberCheck(cardNo);
        }
        return isIdCardNo;
    }

    /**
     * 发送数据到首页控制面板
     *
     * @param accTransaction
     * @return
     * @auther lambert.li
     * @date 2018/12/12 16:30
     */
    private void sendDataToPanelBoard(AccTransactionItem accTransaction) {
        // TODO 面板监控
        JSONObject retJson = new JSONObject();
        // 人未登记时显示卡号
        if (accTransaction.getEventNo() == 27) {
            retJson.put("personName", accTransaction.getCardNo());
        } else if (StringUtils.isNotBlank(accTransaction.getPin())) {
            String name = "";
            if (StringUtils.isNotBlank(accTransaction.getName())
                    || StringUtils.isNotBlank(accTransaction.getLastName())) {
                // 中文下不显示lastName
                name = (accTransaction.getName()
                        + (LocaleMessageSourceUtil.language.equals("zh_CN") ? "" : " " + accTransaction.getLastName()))
                        .trim();
                name = "(" + name + ")";
            }
            // 人员pin+名称
            retJson.put("personName", accTransaction.getPin() + name);
        } else {
            // 非人员相关事件显示设备名称
            retJson.put("personName", accTransaction.getDevAlias());
        }
        // 事件点（门）
        retJson.put("eventPointName", accTransaction.getEventPointName());
        // 事件名称
        retJson.put("eventName", I18nUtil.i18nCode(accTransaction.getEventName()));
        retJson.put("status", (accTransaction.getEventLevel() == AccConstants.EVENT_NORMAL ? "normal"
                : (accTransaction.getEventLevel() == AccConstants.EVENT_WARNING ? "warning" : "alarm")));
        // 事件时间
        retJson.put("eventTime",
                DateUtil.getDate(accTransaction.getEventTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        retJson.put("pin", accTransaction.getPin());
        // app需要的数据
        String appPersonName = "";
        if (StringUtils.isNotBlank(accTransaction.getName()) || StringUtils.isNotBlank(accTransaction.getLastName())) {
            // 中文下不显示lastName
            appPersonName = (accTransaction.getName()
                    + (LocaleMessageSourceUtil.language.equals("zh_CN") ? "" : " " + accTransaction.getLastName()))
                    .trim();
        }
        // 人员名称
        retJson.put("name", appPersonName);
        // 区域名称
        retJson.put("areaName", accTransaction.getAreaName());
        // 事件uniqueKey
        retJson.put("uniqueKey", accTransaction.getUniqueKey());
        // 设备名称
        retJson.put("devAlias", accTransaction.getDevAlias());
        // 卡号
        retJson.put("cardNo", accTransaction.getCardNo());
        // 读头名称
        retJson.put("readerName", accTransaction.getReaderName());
        // 验证方式编号
        retJson.put("verifyModeNo", accTransaction.getVerifyModeNo());
        // 验证类型
        retJson.put("verifyModeName", I18nUtil.i18nCode(accTransaction.getVerifyModeName()));
        AccDoor door = accDoorDao.findById(accTransaction.getEventPointId()).orElse(null);
        if (door != null && !door.getEnabled()) {
            return;
        }
    }

    private int getEventLevel(AccDeviceEventItem devEvent, Short eventNo) {
        short level = 0;// 事件等级
        if (devEvent != null) { // 不为null时，取对象设置的值
            level = devEvent.getEventLevel();
        } else { // 默认设置
            if ((eventNo >= 20 && eventNo < 100 && eventNo != 28) || (eventNo >= 500 && eventNo < 700)) {
                level = 1;
            } else if (eventNo >= 100 && eventNo < 200 || eventNo == 28 || eventNo >= 700) {
                level = 2;
            }
        }
        return level;
    }

    private Map<String, String> getUserData(Map<String, Object> dataMap) {
        Map<String, String> userData = Maps.newHashMap();
        String photoPath = "";
        String photoBase64 = "";
        if (dataMap.containsKey("photoPath") && !"false".equals(dataMap.get("photoPath"))) {
            if ("".equals(dataMap.get("photoPath"))) {
                String skin = (StringUtils.isBlank(systemSkin) || "default".equals(systemSkin)) ? "" : systemSkin;
                photoPath = "images/" + skin + "/userImage.gif";
            } else {
                photoPath = dataMap.get("photoPath").toString();
            }
            photoBase64 = AccConstants.PHOTO_BASE64_PREFIX + accParamService.getAvatarBase64ByPath(photoPath);
        }
        userData.put("photoPath", photoPath);
        userData.put("photoBase64", photoBase64);
        return userData;
    }

    private void putAccTransactionsToCache(List<AccTransactionItem> accLogList) {
        if (!accLogList.isEmpty()) {
            for (AccTransactionItem accTransaction : accLogList) {
                accCacheManager.putTransactionToDb(JSON.toJSONString(accTransaction));
                // 推送异常记录处理，判断是否是异常记录

                // wechatService.wechatSendMessageTest();
                this.accExceptionRecordHandle(accTransaction);
                // 区域内人员功能修改为不需要开后台验证也可以使用，由于led项目中有反馈开后台验证开门速度较慢，所以有此改动 2017.12.13
                if (acc4ZonePersonService != null && StringUtils.isNotBlank(accTransaction.getPin())
                        && accTransaction.getEventPointType() == AccConstants.EVENT_POINT_TYPE_DOOR.shortValue()
                        && AccConstants.TRANSACTION_VERIFY_SUCCESS.contains(accTransaction.getEventNo())) {
                    AccTransactionInfo accTransactionInfo = new AccTransactionInfo();
                    ModelUtil.copyPropertiesIgnoreNull(accTransaction, accTransactionInfo);
                    acc4ZonePersonService.rtUpdatePersonInsideData(accTransactionInfo);
                }
            }
        }
    }

    private void sendDataToThird(AccTransactionItem accTransaction) {
        CompletableFuture.runAsync(() -> {
            try {
                // 判断是否有巡更项目，有的话反射调用巡更项目的实时事件入库----add leo.liu 20160113
                // 存在巡更模块（访客、酒店人员不推送给第三方模块（巡更）
                if (Objects.nonNull(acc4PatrolTransactionService)) {
                    short eventNo = accTransaction.getEventNo();
                    // 排除 后台验证失败、后台验证、联动事件
                    if (StringUtils.isNotBlank(accTransaction.getPin()) && eventNo != AccConstants.EVENT_LINKCONTROL) {
                        Acc4PatrolTransactionItem item = new Acc4PatrolTransactionItem();
                        ModelUtil.copyPropertiesIgnoreNull(accTransaction, item);
                        acc4PatrolTransactionService.pushPatrolTransactions(item);
                    }
                }

                // add by xjing.huang 2019.09.27 推送门禁记录到cva模块
                if (acc4CvaTransactionService != null && StringUtils.isNotBlank(accTransaction.getPin())
                        && (short) accTransaction.getEventNo() != AccConstants.EVENT_LINKCONTROL) {
                    Acc4CvaTransactionItem acc4CvaTransactionItem = new Acc4CvaTransactionItem();
                    ModelUtil.copyProperties(accTransaction, acc4CvaTransactionItem);
                    acc4CvaTransactionService.pushTransactionsToCva(acc4CvaTransactionItem);
                }
                // 存在视频集成模块
                if (Objects.nonNull(acc4IVideoTransactionService)) {
                    Acc4IVideoTransactionItem transactionItem = new Acc4IVideoTransactionItem();
                    ModelUtil.copyProperties(accTransaction, transactionItem);
                    acc4IVideoTransactionService.pushIVideoTransactions(transactionItem);
                }
                // add by seven.wu 2019.10.16 推送门禁事件记录到Line模块
                if (acc4LineTransactionService != null) {
                    Acc4LineTransactionItem acc4LineTransactionItem = new Acc4LineTransactionItem();
                    ModelUtil.copyProperties(accTransaction, acc4LineTransactionItem);
                    // 图片发送暂不开启
                    /*
                     * String vidLinkageHandle = accTransaction.getVidLinkageHandle();
                     * if (StringUtils.isNotBlank(vidLinkageHandle)) {
                     * if (!(vidLinkageHandle.indexOf("/") < 0 && vidLinkageHandle.indexOf("pers")
                     * != 0 && vidLinkageHandle.indexOf("event") != 0)) {
                     * if (isFileExist(vidLinkageHandle)) {
                     * acc4LineTransactionItem.setAttPhotoUrl(vidLinkageHandle);
                     * }
                     * }
                     * }
                     */
                    acc4LineTransactionService.pushTransactionsToLine(acc4LineTransactionItem);
                }

                if (Objects.nonNull(acc4VisVisitorLastAddr) && accTransaction.getPin().startsWith("8")
                        && accTransaction.getPin().length() == 9
                        && (short) accTransaction.getEventNo() != AccConstants.EVENT_LINKCONTROL) { // 根据pin判断是否是访客人员，是则保存保存访客最后访问位置
                    Acc4VisVisitorLastAddrItem visitorLastAddrItem = new Acc4VisVisitorLastAddrItem();
                    ModelUtil.copyPropertiesIgnoreNullWithProperties(accTransaction, visitorLastAddrItem, "id");
                    visitorLastAddrItem.setReaderId(accTransaction.getReaderId());
                    acc4VisVisitorLastAddr.saveVisLastAddr(visitorLastAddrItem);
                }
                // 推送门禁记录到esdc模块
                if (Objects.nonNull(acc4EsdcTransactionService)) {
                    Acc4EsdcTransactionItem acc4EsdcTransactionItem = new Acc4EsdcTransactionItem();
                    ModelUtil.copyProperties(accTransaction, acc4EsdcTransactionItem);
                    acc4EsdcTransactionService.pushTransactionsToEsdc(acc4EsdcTransactionItem);
                }

                // 推送人员编号不为空、非联动事件到其他业务模块
                if (Objects.nonNull(accTransaction4OtherServices) && StringUtils.isNotBlank(accTransaction.getPin())
                        && (short) accTransaction.getEventNo() != AccConstants.EVENT_LINKCONTROL) {
                    AccTransaction4OtherItem accTransaction4OtherItem = new AccTransaction4OtherItem();
                    ModelUtil.copyProperties(accTransaction, accTransaction4OtherItem);
                    // 是否为可以通过的事件
                    boolean verifySuccess = false;
                    if (AccConstants.TRANSACTION_VERIFY_SUCCESS.contains(accTransaction.getEventNo())) {
                        verifySuccess = true;
                    }
                    accTransaction4OtherItem.setVerifySuccess(verifySuccess);
                    Arrays.stream(accTransaction4OtherServices)
                            .forEach(accTransaction4OtherService -> accTransaction4OtherService
                                    .pushPersonTransactionData(accTransaction4OtherItem));
                }

                // 推送门禁记录到其他业务模块
                if (Objects.nonNull(accTransaction4OtherServices)) {
                    AccTransaction4OtherItem accTransaction4OtherItem = new AccTransaction4OtherItem();
                    ModelUtil.copyProperties(accTransaction, accTransaction4OtherItem);
                    Arrays.stream(accTransaction4OtherServices)
                            .forEach(accTransaction4OtherService -> accTransaction4OtherService
                                    .pushTransactionData(accTransaction4OtherItem));
                }

                // 推送至数据推送中心
                if (Objects.nonNull(pushCenter4OtherService)) {
                    PushCenter4OtherItem pushCenter4OtherItem = new PushCenter4OtherItem();
                    pushCenter4OtherItem.setPushCenter4OtherEnum(PushCenter4OtherEnum.ACC_TRANSACTION);
                    AccTransactionItem accTransactionItem = new AccTransactionItem();
                    BeanUtils.copyProperties(accTransaction, accTransactionItem);
                    pushCenter4OtherItem.setContent(JSON.toJSONString(accTransactionItem));
                    pushCenter4OtherService.pushData(pushCenter4OtherItem);
                }
            } catch (Exception e) {
                log.error("accTransaction sendDataToThird error", e);
            }
        });
    }

    /**
     * 判断联动并执行联动动作
     *
     * @param accTransaction
     * @param fieldMap
     * @param devId
     * @param linkId
     * @return AccTransaction
     * <AUTHOR>
     * @since 2018年5月4日 10:17:49
     */
    private AccTransactionItem linkageOperate(AccTransactionItem accTransaction, Map<String, String> fieldMap,
                                              String devId, int linkId) {
        AccTransactionItem accLinkageLog = new AccTransactionItem();
        try {
            ModelUtil.copyPropertiesIgnoreNull(accTransaction, accLinkageLog);
            // Map<String, AccTransactionItem> lastRtLogMap = ;
            AccTransactionItem lastRtlog = accCacheManager.getLastRtLogFromCache(accTransaction.getDevSn());
            if (lastRtlog != null) {
                // 添加联动信息
                accTransaction.setDeptName(lastRtlog.getDeptName());
                accTransaction.setDeptCode(lastRtlog.getDeptCode());
                accTransaction.setName(lastRtlog.getName());
                accTransaction.setLastName(lastRtlog.getLastName());
                accTransaction.setCardNo(lastRtlog.getCardNo());
                accTransaction.setPin(lastRtlog.getPin());
                accTransaction.setReaderName(lastRtlog.getReaderName());
                accTransaction.setVerifyModeName(lastRtlog.getVerifyModeName());
                accTransaction.setReaderState(lastRtlog.getReaderState());
                if (lastRtlog.getEventNo().intValue() != AccConstants.EVENT_LINKCONTROL.intValue()) {// 防止一台设备一个联动触发的多条联动事件产生多次视频、邮件联动
                    int linkIndex = (linkId == AccConstants.FUNCTION_UNSUPPORT)
                            ? Integer.parseInt(fieldMap.get("cardno"))
                            : linkId;
                    String linkageId = accLinkageService.getLinkageIdByIndexAndDevId(linkIndex, devId);
                    // 执行软联动，视频和邮件
                    if (StringUtils.isNotBlank(linkageId)) {
                        ZKResultMsg ret = accLinkageOperate.executeExtendLinkage(linkageId, lastRtlog, accTransaction);// 执行联动动作使用线程处理?
                        accTransaction.setVidDevices(lastRtlog.getVidDevices());
                        accTransaction.setCaptureTime(lastRtlog.getCaptureTime());
                        // 抽出公共方法；设置事件联动信息
                        setTransactionLinkageInfo(ret, accTransaction);
                    }
                }
            }
        } catch (Exception e) {
            log.error("--------------linkageOperate error:", e);
            if (null != accLinkageLog) {
                accTransaction = accLinkageLog;// 防止上面代码，导致写内存导致记录内容改变
            }
        }
        return accTransaction;
    }

    /**
     * 生成uniqueKey
     *
     * @param accTransaction
     * @param devSn
     * @param eventNo
     * @param logTime
     * @param linkIndex
     * @param isSuportLogId
     * @param index
     * @return uniqueKey
     * <AUTHOR>
     * @modify wenxin
     * @since 2015年5月14日 下午3:41:18
     */
    private String getUniqueKey(AccTransactionItem accTransaction, String devSn, short eventNo, String logTime,
                                String linkIndex, boolean isSuportLogId, int index) {
        StringBuffer uniqueKey = new StringBuffer();
        if (isSuportLogId && index >= 0) {
            // 避免设备事件表被清空后导致事件插入失败问题 2015.12.11 modified by dyl
            uniqueKey.append(devSn).append("_").append(index).append("_").append(logTime);
        } else if (AccConstants.CARD_PERSON_REPEATED_EVENT_FILTER.contains((int) eventNo)) {
            // 避免卡相关未验证通过的事件重复插入
            uniqueKey.append(accTransaction.getDevSn()).append("_").append(logTime).append("_").append(eventNo)
                    .append("_").append(accTransaction.getVerifyModeNo()).append("_").append(accTransaction.getCardNo())
                    .append("_").append(accTransaction.getEventPointType()).append("_")
                    .append(accTransaction.getEventPointId()).append("_").append(accTransaction.getReaderState())
                    .append("_").append(linkIndex).append("_")
                    .append(accTransaction.getTriggerCond() == null ? "" : accTransaction.getTriggerCond());
        } else {
            uniqueKey.append(accTransaction.getDevSn()).append("_").append(logTime).append("_").append(eventNo)
                    .append("_").append(accTransaction.getVerifyModeNo()).append("_").append(accTransaction.getPin())
                    .append("_").append(accTransaction.getCardNo()).append("_")
                    .append(accTransaction.getEventPointType())
                    .append("_").append(accTransaction.getEventPointId()).append("_")
                    .append(accTransaction.getReaderState()).append("_").append(linkIndex).append("_")
                    .append(accTransaction.getTriggerCond() == null ? "" : accTransaction.getTriggerCond());
        }
        return uniqueKey.toString();
    }

    private int getIndex(Map<String, String> fieldMap, boolean isSuportLogId) {
        int index = -1;
        if (isSuportLogId && fieldMap.containsKey("index")) {
            // 如果支持LogId,Map肯定会有Index/index--固件的index即软件的logId Sn_index
            index = Integer.parseInt(fieldMap.get("index"));
        }
        return index;
    }

    private Map<String, String> getEventPointInfo(String sn, int eventPointType, int eventAddr) {
        Map<String, String> dataMap = new HashMap<>();
        String eventPointName = "";
        String eventPointId = "-1";
        String doorName = "";
        String doorId = "";
        Short addr = Short.valueOf(eventAddr + "");
        AccQueryDeviceItem accDevice = accDeviceService.getQueryItemBySn(sn);
        if (eventPointType == AccConstants.EVENT_POINT_TYPE_DOOR) { // 门事件点
            if (eventAddr / 100 > 0) { // 读头联动
                Short readerAddr = (short) (eventAddr % 100);
                final List<AccQueryDoorItem> doorList = accDevice.getAccDoorItemList();
                if (doorList != null && !doorList.isEmpty()) {
                    for (AccQueryDoorItem queryDoorItem : doorList) {
                        final List<AccQueryReaderItem> queryReaderList = queryDoorItem.getAccReaderItemList();
                        if (queryReaderList != null && !queryReaderList.isEmpty()) {
                            for (AccQueryReaderItem accReader : queryReaderList) {
                                if (readerAddr.equals(accReader.getReaderNo())) {
                                    eventPointName = accReader.getName();
                                    eventPointId = accReader.getId();
                                    doorName = queryDoorItem.getName();
                                    doorId = queryDoorItem.getId();
                                    break;
                                }
                            }
                        }
                    }
                }
            } else {
                final List<AccQueryDoorItem> doorList = accDevice.getAccDoorItemList();
                if (doorList != null && !doorList.isEmpty()) {
                    for (AccQueryDoorItem accDoor : doorList) {
                        if (addr.equals(accDoor.getDoorNo())) {
                            eventPointName = accDoor.getName();
                            eventPointId = accDoor.getId();
                            doorName = accDoor.getName();
                            doorId = accDoor.getId();
                            break;
                        }
                    }
                }
            }
        } else if (eventPointType == AccConstants.EVENT_POINT_TYPE_AUX_IN) {
            // 辅助输入点事件
            final List<AccQueryAuxInItem> auxInList = accDevice.getAccAuxInItemList();
            if (auxInList != null && !auxInList.isEmpty()) {
                for (AccQueryAuxInItem accAuxIn : auxInList) {
                    if (addr.equals(accAuxIn.getAuxNo())) {
                        eventPointName = accAuxIn.getName();
                        eventPointId = accAuxIn.getId();
                        break;
                    }
                }
            }
        } else if (eventPointType == AccConstants.EVENT_POINT_TYPE_AUX_OUT) {
            // 辅助输出点事件
            final List<AccQueryAuxOutItem> auxOutList = accDevice.getAccAuxOutItemList();
            if (auxOutList != null && !auxOutList.isEmpty()) {
                for (AccQueryAuxOutItem accAuxOut : auxOutList) {
                    if (addr.equals(accAuxOut.getAuxNo())) {
                        eventPointName = accAuxOut.getName();
                        eventPointId = accAuxOut.getId();
                        break;
                    }
                }
            }
        } else if (AccConstants.EVENT_POINT_TYPE_EXTBOARD == eventPointType) {
            // 扩展板事件点
            List<AccQueryExtDeviceItem> extDeviceList = accDevice.getAccQueryExtDeviceItemList();
            if (extDeviceList != null && !extDeviceList.isEmpty()) {
                for (AccQueryExtDeviceItem accExtDevice : extDeviceList) {
                    if (addr.equals(accExtDevice.getExtBoardNo())) {
                        eventPointName = accExtDevice.getAlias();
                        eventPointId = accExtDevice.getId();
                        break;
                    }
                }
            }
        }
        dataMap.put("eventPointId", String.valueOf(eventPointId));
        dataMap.put("eventPointName", eventPointName);
        dataMap.put("doorName", doorName);
        dataMap.put("doorId", doorId);
        return dataMap;
    }

    private Map<String, Object> getEventTypeDataRT(String devSn, String devId, short eventNo, String verifyModeNo,
                                                   String pin, String cardNo, int linkId, int linkEvent, int alarmType) {
        Map<String, Object> dataMap = new HashMap<>();
        int eventPointType = AccConstants.EVENT_POINT_TYPE_NONE;// 事件点类型
        String description = "";// 事件描述
        // String addrNo = "";//输出点name
        String linkIndex = "";// 固件联动id
        if (AccConstants.AUXOUT_EVENT.contains((int) eventNo)) {
            // 开启、关闭辅助输出事件
            eventPointType = AccConstants.EVENT_POINT_TYPE_AUX_OUT;
        } else if (AccConstants.AUXIN_EVENT.contains((int) eventNo)) {
            // 辅助输入点短路、断路事件
            eventPointType = AccConstants.EVENT_POINT_TYPE_AUX_IN;
        } else if (eventNo == AccConstants.EVENT_LINKCONTROL) {
            // 联动时，返回触发的事件类型，pull时复用了验证方式。push时不再复用。这里的不支持指的是上来的时间里没有实际字段，即复用了。
            linkEvent = (linkEvent == AccConstants.FUNCTION_UNSUPPORT) ? Integer.parseInt(verifyModeNo) : linkEvent;
            if (AccConstants.AUXIN_EVENT.contains(linkEvent)) {
                // 辅助输入短路、断路触发联动
                eventPointType = AccConstants.EVENT_POINT_TYPE_AUX_IN;
            } else {
                // 门触发联动
                eventPointType = AccConstants.EVENT_POINT_TYPE_DOOR;
            }
            verifyModeNo = AccConstants.VERIFY_MODE_OTHERS.toString();// 验证方式为"其他"
            linkId = (linkId == AccConstants.FUNCTION_UNSUPPORT) ? Integer.parseInt(cardNo.equals("") ? "0" : cardNo)
                    : linkId;// fieldMap.get("cardno");// 联动事件时，卡号位复用为联动id
            Map<String, String> retMap = accLinkageService.getLinkageAction(linkId, devId);
            if (retMap.containsKey("description")) {
                description = retMap.get("description");// 联动描述
            }
            linkIndex = String.valueOf(linkId);
        } else if (AccConstants.EXTBOARD_EVENT.contains((int) eventNo)) {
            // 扩展板事件
            eventPointType = AccConstants.EVENT_POINT_TYPE_EXTBOARD;
        } else {
            // 设备、门相关事件
            eventPointType = AccConstants.EVENT_POINT_TYPE_DOOR;// 门事件
            dataMap.put("personInfo", true);// 这个参数表示需要返回人员的名称、卡号、部门名称信息
            // 卡号是否为身份证号码，卡号为身份证号码不做转换
            boolean isIdCardNo = isIdCardNumberCheck(cardNo);
            if (!"".equals(cardNo) && "1".equals(accParamService.getParamValByName("pers.cardHex")) && !isIdCardNo) {
                cardNo = AccDeviceUtil.convertCardNo(cardNo, 10, 16);
            }
            if ("".equals(pin) && !"".equals(cardNo))// 查询软件上卡号是否已经发给了人员
            {
                pin = persPersonService.getPersonPinByCardNo(cardNo);
            }
            Map<String, String> personInfoMap = getPersonInfo(pin);
            dataMap.put("pin", pin);
            dataMap.put("cardNo", cardNo);
            dataMap.put("name", personInfoMap.containsKey("name") ? personInfoMap.get("name") : "");
            dataMap.put("lastName", personInfoMap.containsKey("lastName") ? personInfoMap.get("lastName") : "");
            dataMap.put("photoPath", personInfoMap.containsKey("photoPath") ? personInfoMap.get("photoPath") : "false");// 设置为false，表示软件没有这个人员
            dataMap.put("deptCode", personInfoMap.containsKey("deptCode") ? personInfoMap.get("deptCode") : "");
            dataMap.put("deptName", personInfoMap.containsKey("deptName") ? personInfoMap.get("deptName") : "");
            dataMap.put("pinName", personInfoMap.containsKey("pinName") ? personInfoMap.get("pinName") : "");
            dataMap.put("email", personInfoMap.get("email"));
            dataMap.put("isSendEmail", personInfoMap.get("isSendEmail"));
            dataMap.put("mobilePhone", personInfoMap.get("mobilePhone"));
            dataMap.put("sendSMS", personInfoMap.get("sendSMS"));
            dataMap.put("isSendWhatsapp", personInfoMap.get("isSendWhatsapp"));
            dataMap.put("whatsappMobileNo", personInfoMap.get("whatsappMobileNo"));
            dataMap.put("deptId", personInfoMap.containsKey("deptId") ? personInfoMap.get("deptId") : "");
            dataMap.put("persPersonId",
                    personInfoMap.containsKey("persPersonId") ? personInfoMap.get("persPersonId") : "");
        }
        dataMap.put("eventPointType", eventPointType);
        dataMap.put("description", description);
        dataMap.put("verifyModeNo", verifyModeNo);
        dataMap.put("verifyModeName",
                accDeviceVerifyModeService.getVerifyModeNameByDevSnAndVerifyModeNo(devSn, verifyModeNo));
        dataMap.put("linkIndex", linkIndex);
        return dataMap;
    }

    @Override
    public Map<String, String> getPersonInfo(String pin) {
        Map<String, String> dataMap = new HashMap<>();
        String name = "";// 姓名
        if (StringUtils.isNotBlank(pin)) {
            if (isVisVisitor(pin) && Objects.nonNull(accGetVisTransactionService)) {// 访客信息单独获取
                Acc4VisTransactionItem visTransactionItem = accGetVisTransactionService.getVisitorInfoByPin(pin);
                if (visTransactionItem != null) {
                    name = visTransactionItem.getVisEmpName() == null ? "" : visTransactionItem.getVisEmpName();// 姓名
                    dataMap.put("name", name);
                    dataMap.put("lastName", StringUtils.isNotBlank(visTransactionItem.getVisEmpLastName())
                            ? visTransactionItem.getVisEmpLastName()
                            : "");
                    dataMap.put("photoPath",
                            visTransactionItem.getCertPhoto() != null ? visTransactionItem.getCertPhoto() : "");
                    dataMap.put("deptCode", "");
                    dataMap.put("deptName", "");
                    dataMap.put("email", "");
                    dataMap.put("isSendEmail", "false");
                    dataMap.put("mobilePhone", "");
                    dataMap.put("sendSMS", "false");
                    dataMap.put("isSendWhatsapp", "false");
                    dataMap.put("whatsappMobileNo", "");
                    dataMap.put("deptId", "visitorDept");
                    dataMap.put("persPersonId", visTransactionItem.getId());
                }
            } else {
                final PersPersonCacheItem persPersonItem = persPersonCacheService.getPersonCacheByPin(pin);
                if (persPersonItem != null) {
                    name = persPersonItem.getName() == null ? "" : persPersonItem.getName();// 姓名
                    dataMap.put("name", name);
                    dataMap.put("lastName",
                            StringUtils.isNotBlank(persPersonItem.getLastName()) ? persPersonItem.getLastName() : "");
                    dataMap.put("photoPath",
                            persPersonItem.getPhotoPath() != null ? persPersonItem.getPhotoPath() : "");
                    dataMap.put("deptCode", persPersonItem.getDeptCode());
                    dataMap.put("deptName",
                            StringUtils.isNotBlank(persPersonItem.getDeptName()) ? persPersonItem.getDeptName() : "");
                    dataMap.put("email", persPersonItem.getEmail());
                    dataMap.put("isSendEmail", persPersonItem.getIsSendMail() != null
                            ? (persPersonItem.getIsSendMail() ? "true" : "false")
                            : "false");
                    dataMap.put("mobilePhone", persPersonItem.getMobilePhone());
                    dataMap.put("sendSMS", persPersonItem.getSendSMS() != null
                            ? (persPersonItem.getSendSMS() ? "true" : "false")
                            : "false");
                    dataMap.put("isSendWhatsapp", persPersonItem.getSendWhatsapp() != null
                            ? (persPersonItem.getSendWhatsapp() ? "true" : "false")
                            : "false");
                    dataMap.put("whatsappMobileNo", persPersonItem.getWhatsappMobileNo());
                    dataMap.put("deptId", persPersonItem.getDeptId());
                    dataMap.put("persPersonId", persPersonItem.getId());
                }
            }
        }
        String pinName = pin + ((!"".equals(name)) ? String.format("(%s)", name) : "");
        dataMap.put("pinName", pinName);
        return dataMap;
    }

    /**
     * 通过eventNo获取eventName
     *
     * @param devEvent
     * @param eventNo
     * @return eventName
     * <AUTHOR>
     * @since 2015年5月14日 下午2:14:15
     */
    private String getEventNameByEventNo(Map<String, String> devEvent, short eventNo) {
        String eventName = "";
        if (devEvent.containsKey(eventNo + "")) {
            eventName = devEvent.get(eventNo + "");
        } else {
            eventName = I18nUtil.i18nCode(AccConstants.EVENT_NO_UNDEFINED) + "<" + eventNo + ">";
        }
        return eventName;
    }

    private Map<String, List<AccDeviceEventItem>> getAllEvent(AccQueryDeviceItem dev) {
        Map<String, List<AccDeviceEventItem>> devEventMap = Maps.newHashMap();
        List<AccDeviceEventItem> deviceEventItemList = accDeviceService.getDevEventBySn(dev.getSn());
        devEventMap.put(dev.getSn(), deviceEventItemList);
        return devEventMap;
    }

    private void getFieldMap(Map<String, String> logMap, String logInfo) {
        // String[] logArray = log.split("\t");
        // String[] tempFieldStr = null;
        log.info("************AccTransactionServiceImpl getFieldMap logInfo : ", logInfo);
        JSONObject logJson = JSONObject.parseObject(logInfo);
        for (String key : logJson.keySet()) {
            // tempFieldStr = logStr.split("=", 2);
            logMap.put(key, logJson.getString(key));
        }
    }

    @Override
    public AccDeviceEventItem getEventByEventNo(List<AccDeviceEventItem> devEvent, short eventNo) {
        AccDeviceEventItem item = null;
        if (ObjectUtils.isNotEmpty(devEvent) && ObjectUtils.isNotEmpty(eventNo)) {
            Map<Short, AccDeviceEventItem> deviceEventItemMap = CollectionUtil.listToKeyMap(devEvent,
                    AccDeviceEventItem::getEventNo);
            item = deviceEventItemMap.get(eventNo);
        }
        if (ObjectUtils.isEmpty(item)) {
            item = new AccDeviceEventItem();
            item.setEventNo(eventNo);
            item.setName(I18nUtil.i18nCode(AccConstants.EVENT_NO_UNDEFINED) + "<" + eventNo + ">");
        }
        return item;
    }

    @Override
    public AccTransactionItem getItemByLogId(Integer logId) {
        AccTransactionItem condition = new AccTransactionItem();
        condition.setLogId(logId);
        List<AccTransactionItem> items = getByCondition(condition);
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public boolean isFileExist(String filePath) {
        // 获取项目的绝对路径
        String photoPath = ClassUtil.getRootPath() + "/" + systemFilePath + "/" + filePath;
        File tempFile = new File(photoPath);
        return tempFile.exists();
    }

    /**
     * 发送邮件
     *
     * @author: mingfa.zheng
     * @date: 2018/6/14 14:57
     * @return:
     */
    private void sendMail(AccTransactionItem accTransaction, Map<String, Object> dataMap) {
        String content = getMailContent(accTransaction);
        String subject = I18nUtil.i18nCode(accTransaction.getEventName());
        baseSendMailService.sendSampleHtmlMail((String) dataMap.get("email"), subject, content, "");
    }

    @Override
    public Pager getTransactionsTodayLast(AccTransactionItem accTransactionItem) {
        int total = Integer.parseInt(baseSysParamService.getValByName("acc.queryDoorEventLastLimit"));
        Pager pager = accTransactionDao.getItemsBySql(accTransactionItem.getClass(),
                SQLUtil.getSqlByItem(accTransactionItem), 0, total);
        List<AccTransactionItem> accTransactionItemList = (List<AccTransactionItem>) pager.getData();
        for (AccTransactionItem item : accTransactionItemList) {
            // 构建媒体文件字段
            String jsonStr = buildVidLinkageHandle(item.getCapturePhotoPath(), item.getVidLinkageHandle());
            item.setVidLinkageHandle(jsonStr);
            item.setPin(item.getPin());
            item.setCardNo(item.getCardNo());
            // 事件级别
            short eventLevel = accDeviceEventService.getDefaultEventTypeById(item.getEventNo());
            // 如果是报警事件，则拼接报警事件等级
            String levelAndEventPriority = eventLevel + "";
            if (AccConstants.EVENT_ALARM == eventLevel && Objects.nonNull(item.getEventPriority())) {
                levelAndEventPriority += "#" + item.getEventPriority();
            }
            item.setLevelAndEventPriority(levelAndEventPriority);
        }
        pager.setTotal(pager.getData().size());
        return pager;
    }

    @Override
    public List<AccTransactionCardNoItem> getCurrIssueCardNoByReader(String readerIds, Date time) {
        List<AccTransactionCardNoItem> accTransactionCardNoItemList = null;
        if (StringUtils.isNotBlank(readerIds)) {
            AccTransactionCardNoItem accTransactionCardNoItem = new AccTransactionCardNoItem();
            accTransactionCardNoItem.setReaderIdIn(readerIds);
            accTransactionCardNoItem.setCreatTime(time);
            accTransactionCardNoItemList = (List<AccTransactionCardNoItem>) accTransactionDao.getItemsDataBySql(
                    accTransactionCardNoItem.getClass(),
                    SQLUtil.getSqlByItem(accTransactionCardNoItem), 0, 1, true);
        }
        return accTransactionCardNoItemList;
    }

    @Override
    public Map<String, String> dealQueryTransaction(int commType, String data, String sn) {
        Map<String, Map<String, String>> resolveMap = Maps.newHashMap();
        Map<String, String> dataMap = null;
        AccDevice dev = accDeviceDao.findBySn(sn);
        if (dev != null) {
            AuthAreaItem authAreaItem = authAreaService.getItemById(dev.getAuthAreaId());
            if (commType == ConstUtil.COMM_TCPIP || commType == ConstUtil.COMM_RS485) {
                resolveMap = getDataMapByPull(data, dev, authAreaItem.getName());
                dataMap = insertTransRecord(resolveMap, dev);
            } else if (commType == ConstUtil.COMM_HTTP) {
                resolveMap = getTransactionDataMap(data, dev, authAreaItem.getName());
                dataMap = insertTransRecord(resolveMap, dev);
            } else if (commType == AccConstants.COMM_TYPE_BEST_MQTT) {
                // add by colin Mqtt的事件解析 pro 拓展
                resolveMap = getDataMapByMqtt(data, dev, authAreaItem.getName());
                dataMap = insertTransRecordByMqtt(resolveMap, dev);
            }
        }
        return dataMap;
    }

    @Override
    public void sendAlarmMail(String subject, String content) {
        String receiver = accCacheManager.getCacheValueByKey(AccCacheKeyConstants.ACC_ALARM_RECEIVER);
        if (receiver == null || "".equals(receiver.trim())) {
            receiver = FoldexUtil.decryptByRandomSey(baseSysParamService.getValByName("acc.receiver"));
            if (receiver != null && !"".equals(receiver.trim())) {
                accCacheManager.set(AccCacheKeyConstants.ACC_ALARM_RECEIVER, receiver);
            }
        }
        if (receiver != null && !"".equals(receiver.trim())) {
            for (String tempReceiver : receiver.split(",")) {// 修改为单封发送
                baseSendMailService.sendSampleHtmlMail(tempReceiver.trim(), subject, content, "");// 附件暂不处理
            }
        }
    }

    /**
     * 将解析后的事件记录插入数据库
     *
     * @param transDataMap
     * @param dev
     */
    private Map<String, String> insertTransRecord(Map<String, Map<String, String>> transDataMap, AccDevice dev) {
        Map<String, String> dataMap = Maps.newHashMap();
        if (transDataMap.size() > 0) {
            String minFmtDate = transDataMap.get("minTimestamp").get("minTimestamp");
            transDataMap.remove("minTimestamp"); // 删除掉最小日期
            int count = transDataMap.size();
            dataMap.put("count", String.valueOf(count));
            List<String> transactionList = null;
            boolean isSupportLogId = accDeviceOptionService.isSupportFun(dev.getSn(), "LogIDFunOn");
            if (isSupportLogId) {
                // IR9000有子控，需要排除子控的重复事件插入 by juvenile.li add 20171116
                List<String> snList = Lists.newArrayList(dev.getSn());
                if (!dev.getChildDevices().isEmpty()) {
                    dev.getChildDevices().forEach(tempDev -> snList.add(tempDev.getSn()));
                }
                transactionList = accTransactionDao.getUniqueBySnAndLogId(snList, Integer.parseInt(minFmtDate));
                for (String transLogId : transactionList) {
                    transDataMap.remove(transLogId);
                }
            } else {
                transactionList = accTransactionDao.getUniqueBySnAndEventTime(dev.getSn(),
                        DateUtil.stringToDate(minFmtDate, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                for (String transUniqueKey : transactionList) {
                    transDataMap.remove(transUniqueKey);
                }
            }
            dataMap.put("insert", String.valueOf(transDataMap.size()));
            dataMap.put("update", String.valueOf(count - transDataMap.size()));
            batchInsertTrans(transDataMap);
        } else {
            dataMap.put("count", "0");
            dataMap.put("insert", "0");
            dataMap.put("update", "0");
        }
        return dataMap;
    }

    // mqtt 将解析后的事件记录插入数据库
    private Map<String, String> insertTransRecordByMqtt(Map<String, Map<String, String>> transDataMap, AccDevice dev) {
        Map<String, String> dataMap = Maps.newHashMap();
        String minFmtDate = transDataMap.get("minTimestamp").get("minTimestamp");
        transDataMap.remove("minTimestamp"); // 删除掉最小日期
        int count = transDataMap.size();
        dataMap.put("count", count + "");
        List<String> transactionList = null;
        List<String> snList = Lists.newArrayList(dev.getSn());
        if (!dev.getChildDevices().isEmpty()) {
            dev.getChildDevices().stream().forEach(tempDev -> snList.add(tempDev.getSn()));
        }
        // mqtt 的事件类型都是2000+,所以这里去掉了事件编号<=255的限制
        transactionList = accTransactionDao.getUniqueBySnAndLogIdOfAllEventNo(snList, Integer.parseInt(minFmtDate));
        for (String transLogId : transactionList) {
            if (transDataMap.containsKey(transLogId)) {
                transDataMap.remove(transLogId);
            }
        }
        dataMap.put("insert", transDataMap.size() + "");
        dataMap.put("update", String.valueOf(count - transDataMap.size()));
        batchInsertTrans(transDataMap);
        return dataMap;
    }

    /**
     * @param transDataMap
     * @return
     * @Description: 批量插入事件记录
     * <AUTHOR>
     * @date 2018/7/4 18:25
     */
    private void batchInsertTrans(Map<String, Map<String, String>> transDataMap) {
        List<AccTransaction> accTransactionList = Lists.newArrayList();
        for (String key : transDataMap.keySet()) {
            AccTransaction accTransaction = buildAccTransaction(transDataMap.get(key));
            accTransactionList.add(accTransaction);
        }
        if (!accTransactionList.isEmpty()) {
            accTransactionDao.save(accTransactionList);
        }
    }

    /**
     * @param dataMap
     * @return
     * @Description: 组装事件记录数据
     * <AUTHOR>
     * @date 2018/7/4 19:53
     */
    private AccTransaction buildAccTransaction(Map<String, String> dataMap) {
        AccTransaction accTransaction = new AccTransaction();
        accTransaction.setPin(dataMap.get("pin"));
        accTransaction.setUniqueKey(dataMap.get("unique"));
        accTransaction.setLogId(Integer.valueOf(dataMap.get("index")));
        accTransaction.setCardNo(dataMap.get("cardno"));
        accTransaction.setAreaName(dataMap.get("area"));
        accTransaction.setDeptCode(dataMap.get("deptCode"));
        accTransaction.setDeptName(dataMap.get("deptName"));
        accTransaction.setName(dataMap.get("name"));
        accTransaction.setLastName(dataMap.get("lastName"));
        accTransaction.setDevId(dataMap.get("devId"));
        accTransaction.setDevSn(dataMap.get("devSn"));
        accTransaction.setDevAlias(dataMap.get("devAlias"));
        accTransaction.setEventNo(
                StringUtils.isNotBlank(dataMap.get("eventtype")) ? Short.valueOf(dataMap.get("eventtype")) : null);
        accTransaction.setEventName(dataMap.get("eventName"));
        accTransaction.setEventPointId(dataMap.get("eventPointId"));
        accTransaction.setEventPointName(dataMap.get("eventPointName"));
        accTransaction.setEventPointType(StringUtils.isNotBlank(dataMap.get("eventPointType"))
                ? Short.valueOf(dataMap.get("eventPointType"))
                : null);
        accTransaction.setReaderState(
                StringUtils.isNotBlank(dataMap.get("inoutstate")) ? Short.valueOf(dataMap.get("inoutstate")) : null);
        accTransaction.setDescription(dataMap.get("description"));
        accTransaction.setVerifyModeNo(
                StringUtils.isNotBlank(dataMap.get("verifyModeNo")) ? Short.valueOf(dataMap.get("verifyModeNo"))
                        : null);
        accTransaction.setVerifyModeName(dataMap.get("verifyModeName"));
        accTransaction
                .setEventTime(
                        DateUtil.stringToDate(dataMap.get("time_second"), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        accTransaction.setTriggerCond(
                StringUtils.isNotBlank(dataMap.get("triggercond")) ? Short.valueOf(dataMap.get("triggercond")) : null);
        accTransaction.setReaderName(dataMap.get("readerName"));
        accTransaction.setEventAddr(
                StringUtils.isNotBlank(dataMap.get("eventAddr")) ? Short.valueOf(dataMap.get("eventAddr")) : null);
        accTransaction.setMaskFlag(StringUtils.isNotBlank(dataMap.get("maskflag")) ? dataMap.get("maskflag") : null);
        accTransaction
                .setTemperature(StringUtils.isNotBlank(dataMap.get("temperature")) ? dataMap.get("temperature") : null);
        if (dataMap.containsKey("eventPriority") && StringUtils.isNotBlank(dataMap.get("eventPriority"))) {
            accTransaction.setEventPriority(Short.valueOf(dataMap.get("eventPriority")));
        }
        return accTransaction;
    }

    /**
     * @param transData
     * @param dev
     * @param areaName
     * @return
     * @Description: push事件解析
     * <AUTHOR>
     * @date 2018/7/4 15:22
     */
    private Map<String, Map<String, String>> getTransactionDataMap(String transData, AccDevice dev, String areaName) {
        Map<String, Map<String, String>> retMap = Maps.newHashMap();
        Map<String, AccDevice> tempDevMap = Maps.newHashMap();
        tempDevMap.put(dev.getSn(), dev);
        if (!dev.getChildDevices().isEmpty()) {
            dev.getChildDevices().forEach(tempDev -> tempDevMap.put(tempDev.getSn(), tempDev));
        }
        // 记录事件的最早时间，用来后续移除标记判断 add by max 添加注解
        Date minTimestamp = null;
        String minLogId = null;
        String[] dataArray = transData.split("\r\n");
        String[] fieldArr = null;
        Map<String, String> fieldMap = null;
        String[] keyValArr = null;
        boolean isSupportLogId = accDeviceOptionService.isSupportFun(dev.getSn(), "LogIDFunOn");
        boolean isMultiCard = accDeviceOptionService.isSupportFunList(dev.getSn(), 7);// 是否支持一人多卡
        Timestamp currentTime = new Timestamp(System.currentTimeMillis());
        String createTime = DateUtil.dateToString(currentTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
        AccDevice tempDev = null;
        for (String tempRecord : dataArray) {
            fieldMap = Maps.newHashMap();
            fieldArr = StringUtils.split(tempRecord.split("transaction ")[1], "\t");
            for (String field : fieldArr) {
                keyValArr = field.trim().split("=", 2);
                if (keyValArr[0].equals("time_second")) {
                    Timestamp time = AccDataUtil.transformTime(keyValArr[1]);
                    fieldMap.put("time_second", DateUtil.dateToString(time, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));

                } else if (keyValArr[0].equals("pin") || keyValArr[0].equals("cardno")) {
                    fieldMap.put(keyValArr[0], keyValArr[1].equals("0") ? "" : keyValArr[1]);
                } else {
                    fieldMap.put(keyValArr[0], keyValArr[1]);
                }
            }
            // 获取事件记录按照事件过滤事件功能
            String eventTimeStr = fieldMap.get("time_second");
            if (StringUtils.isNotBlank(eventTimeStr)) {
                Date eventTime = DateUtil.stringToDate(eventTimeStr, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
                AccDeviceUploadTransactionItem deviceUploadTransactionItem = accCacheManager
                        .getTransactionValidTime(dev.getSn());
                if (deviceUploadTransactionItem != null
                        && "true".equals(deviceUploadTransactionItem.getSetValidDate())) {
                    Date startTime = deviceUploadTransactionItem.getStartTime();
                    Date endTime = deviceUploadTransactionItem.getEndTime();
                    // 事件时间小于开始时间
                    if (startTime != null && AccDataUtil.compareTime(eventTime, startTime) < 0) {
                        continue;
                    }
                    // 事件时间小于结束时间
                    if (endTime != null && AccDataUtil.compareTime(eventTime, endTime) > 0) {
                        continue;
                    }
                }
                if (!isSupportLogId) {
                    if (minTimestamp == null) {
                        minTimestamp = eventTime;
                    }
                    minTimestamp = AccDataUtil.compareTime(minTimestamp, eventTime) >= 0 ? eventTime : minTimestamp;
                }
            }
            String logIndex = fieldMap.get("index");
            if (StringUtils.isNotBlank(logIndex) && isSupportLogId) {
                if (minLogId == null) {
                    minLogId = logIndex;
                }
                minLogId = (Integer.parseInt(minLogId) > Integer.parseInt(logIndex)) ? logIndex : minLogId;
            }

            tempDev = dev;
            if (fieldMap.containsKey("sn")) { // 子设备事件带有SN
                tempDev = tempDevMap.get(fieldMap.get("sn"));
                if (tempDev == null) {
                    tempDev = dev;
                }
            }

            // ----------------------------------
            int eventType = Integer.parseInt(fieldMap.get("eventtype"));
            String cardno = fieldMap.get("cardno") != null ? fieldMap.get("cardno") : "";
            // 卡号是否为身份证号码，卡号为身份证号码不做转换
            boolean isIdCardNo = isIdCardNumberCheck(cardno);
            // 新控制器获取事件记录，解析卡号 by juvenile add 20160629
            if (isMultiCard && StringUtils.isNotBlank(cardno) && !isIdCardNo) {
                cardno = AccDeviceUtil.convertCardNo(cardno, 16, 10);
            }
            Map<String, Object> eventPersonMap = getEventTypeData(Integer.parseInt(fieldMap.get("eventtype")),
                    fieldMap.get("verified"), fieldMap.get("pin"), cardno, tempDev.getSn(), tempDev.getId());
            Map<String, String> eventPointMap = getEventPointInfo(dev.getSn(),
                    Integer.parseInt(String.valueOf(eventPersonMap.get("eventPointType"))),
                    Integer.parseInt(fieldMap.get("doorid")));
            fieldMap.put("verifyModeNo",
                    eventPersonMap.containsKey("verifyModeNo") ? eventPersonMap.get("verifyModeNo").toString() : "");
            fieldMap.put("verifyModeName",
                    eventPersonMap.containsKey("verifyModeName") ? eventPersonMap.get("verifyModeName").toString()
                            : "");
            fieldMap.put("devId", tempDev.getId());
            fieldMap.put("devSn", tempDev.getSn());
            fieldMap.put("devAlias", tempDev.getAlias());
            fieldMap.put("area", areaName);
            fieldMap.put("eventPointType",
                    eventPersonMap.containsKey("eventPointType") ? eventPersonMap.get("eventPointType").toString()
                            : "");
            fieldMap.put("description",
                    eventPersonMap.containsKey("description") ? eventPersonMap.get("description").toString() : "");
            fieldMap.put("name", eventPersonMap.containsKey("name") ? eventPersonMap.get("name").toString() : "");
            fieldMap.put("lastName",
                    eventPersonMap.containsKey("lastName") ? eventPersonMap.get("lastName").toString() : "");
            fieldMap.put("deptCode",
                    eventPersonMap.containsKey("deptCode") ? eventPersonMap.get("deptCode").toString() : "");
            fieldMap.put("deptName",
                    eventPersonMap.containsKey("deptName") ? eventPersonMap.get("deptName").toString() : "");
            fieldMap.put("eventPointId", eventPointMap.get("eventPointId"));
            fieldMap.put("eventPointName", eventPointMap.get("eventPointName"));
            List<AccDeviceEventItem> deviceEventItemList = accDeviceService.getDevEventBySn(dev.getSn());
            AccDeviceEventItem deviceEventItem = getEventByEventNo(deviceEventItemList,
                    Short.parseShort(fieldMap.get("eventtype")));
            String eventName = deviceEventItem.getName();
            fieldMap.put("eventName", eventName);
            if (ObjectUtils.isNotEmpty(deviceEventItem.getEventPriority())) {
                fieldMap.put("eventPriority", String.valueOf(deviceEventItem.getEventPriority()));
            } else if (ObjectUtils.isNotEmpty(deviceEventItem.getEventLevel())
                    && AccConstants.EVENT_ALARM == deviceEventItem.getEventLevel()) {
                fieldMap.put("eventPriority", "0");
            }
            fieldMap.put("createTime", createTime);// 插入当前时间做为创建时间
            // 身份证通行模式下，需要更新设置姓名
            if (eventType == AccConstants.EVENT_IDENTITY_CARD_PASS) {
                // cardNo 当前为物理卡号
                JSONObject jsonData = getIdentityCardInfo(cardno);
                if (Objects.isNull(jsonData)) {
                    jsonData = cacheIdentityCardInfo(cardno, tempDev);
                }
                if (jsonData != null && jsonData.containsKey("data")) {
                    JSONArray jsonArray = jsonData.getJSONArray("data");
                    JSONObject jsonObject = jsonArray.getJSONObject(0);
                    // 设置姓名
                    fieldMap.put("name", jsonObject.getString("Name"));
                    // 身份证号码
                    fieldMap.put("pin", jsonObject.getString("ID_Num"));
                }
            }
            // 判断事件是否是联动
            fieldMap.put("triggercond", fieldMap.get("eventtype").equals("6") ? fieldMap.get("verified") : "");
            fieldMap.put("cardno", fieldMap.get("eventtype").equals("6") ? ""
                    : eventPersonMap.get("cardNo") != null ? eventPersonMap.get("cardNo").toString() : "");
            fieldMap.put("eventAddr", fieldMap.get("doorid"));
            // cardno, pin, eventno, readerState, eventtime, sn, verifymodeno eventpointid,
            // eventpointtype triggercond
            if (fieldMap.containsKey("inoutstate")) {
                short state = Short.parseShort(fieldMap.get("inoutstate"));
                String readerName = I18nUtil.i18nCode("common_reader_state_2");// 此处请参照国际化标签
                if (state != 2) { // 有进出状态,将状态改成读头名称
                    final AccQueryReaderItem accReader = getReaderCacheByDevSnAndDoorNoAndReaderState(tempDev.getSn(),
                            fieldMap.get("doorid"), state);
                    // AccReader accReader = accReaderDao.getReaderByStateAndDoorNoAndDevId(state,
                    // Short.parseShort(fieldMap.get("doorid")), tempDev.getId());
                    if (Objects.nonNull(accReader)) {
                        readerName = accReader.getName();
                    }
                }
                fieldMap.put("readerName", readerName);
            }
            StringBuffer keyStrBuf = new StringBuffer();
            String index = "-1";// 不支持的固件默认给-1
            if (isSupportLogId) {
                index = fieldMap.get("index");
                // 避免设备事件表被清空后导致事件插入失败问题 2015.12.11 modified by dyl
                keyStrBuf.append(tempDev.getSn()).append("_").append(fieldMap.get("index")).append("_")
                        .append(fieldMap.get("time_second"));
            } else if (AccConstants.CARD_PERSON_REPEATED_EVENT_FILTER.contains(eventType)) { // 避免卡相关未验证通过的事件重复插入
                // New:
                // Sn_Time_EventNo_VerifyMode_Pin_CardNo_PointType_EventPoint_ReaderState(in/out)_TriggerCond
                keyStrBuf.append(fieldMap.get("devSn")).append("_").append(fieldMap.get("time_second")).append("_")
                        .append(fieldMap.get("eventtype")).append("_").append(fieldMap.get("verifyModeNo")).append("_")
                        .append(fieldMap.get("cardno")).append("_").append(fieldMap.get("eventPointType")).append("_")
                        .append(fieldMap.get("eventPointId")).append("_").append(fieldMap.get("inoutstate")).append("_")
                        .append(eventPersonMap.get("linkIndex")).append("_").append(fieldMap.get("triggercond"));
            } else {
                // New:
                // Sn_Time_EventNo_VerifyMode_Pin_CardNo_PointType_EventPoint_ReaderState(in/out)_TriggerCond
                keyStrBuf.append(fieldMap.get("devSn")).append("_").append(fieldMap.get("time_second")).append("_")
                        .append(fieldMap.get("eventtype")).append("_").append(fieldMap.get("verifyModeNo")).append("_")
                        .append(fieldMap.get("pin")).append("_").append(fieldMap.get("cardno")).append("_")
                        .append(fieldMap.get("eventPointType")).append("_").append(fieldMap.get("eventPointId"))
                        .append("_")
                        .append(fieldMap.get("inoutstate")).append("_").append(eventPersonMap.get("linkIndex"))
                        .append("_")
                        .append(fieldMap.get("triggercond"));
                // keyStrBuf.append(fieldMap.get("cardno")).append("_").append(fieldMap.get("pin")).append("_").append(fieldMap.get("eventtype")).append("_")
                // .append(fieldMap.get("inoutstate")).append("_").append(fieldMap.get("time_second")).append("_").append(fieldMap.get("devSn")).append("_")
                // .append(fieldMap.get("verifyModeNo")).append("_").append(fieldMap.get("eventPointId")).append("_").append(fieldMap.get("eventPointType"))
                // .append("_").append(fieldMap.get("triggercond"));
            }
            fieldMap.put("index", index);
            fieldMap.put("unique", keyStrBuf.toString());
            // 判定设备上传事件记录是否index重复
            // if (retMap.containsKey(keyStrBuf.toString()))
            // {
            // logger.info(keyStrBuf);
            // }
            // fieldMap.put("maskFlag", )
            // fieldMap.put("temperature", )
            retMap.put(keyStrBuf.toString(), fieldMap);
        }
        // 将最小时间返回
        if (retMap.size() > 0) {
            Map<String, String> minDateTime = Maps.newHashMap();
            if (!isSupportLogId) {
                minDateTime.put("minTimestamp",
                        DateUtil.dateToString(minTimestamp, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                retMap.put("minTimestamp", minDateTime);
            } else {
                minDateTime.put("minTimestamp", minLogId);
                retMap.put("minTimestamp", minDateTime);
            }
            minDateTime = null;
        }
        return retMap;
    }

    /**
     * 缓存身份证表信息
     *
     * @param physicalNo:身份证物理卡号
     * @param accDevice:门禁设备信息
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date 2020-09-22 16:39
     * @since 1.0.0
     */
    private JSONObject cacheIdentityCardInfo(String physicalNo, AccDevice accDevice) {
        JSONObject json = null;
        if (StringUtils.isNotBlank(physicalNo)) {
            PersIdentityCardInfoItem persIdentityCardInfoItem = persIdentityCardInfoService
                    .findByPhysicalNo(physicalNo);
            if (Objects.nonNull(persIdentityCardInfoItem)) {
                json = new JSONObject();
                json.put("table", AccConstants.IDENTITY_CARD_INFO);
                json.put("sn", accDevice.getSn());
                JSONArray dataJson = new JSONArray();
                Map<String, String> map = new HashMap<>();
                map.put("Nation", persIdentityCardInfoItem.getNation());
                map.put("Address", persIdentityCardInfoItem.getAddress());
                map.put("Issuer", persIdentityCardInfoItem.getIssuedOrgan());
                map.put("PhotoJPG",
                        FileUtil.getFileBase64Str(FileUtil.getLocalFullPath(persIdentityCardInfoItem.getPhotoPath())));
                map.put("Birthday", String.valueOf(persIdentityCardInfoItem.getBirthday()).replace("-", ""));
                map.put("Gender", persIdentityCardInfoItem.getGender());
                map.put("Name", persIdentityCardInfoItem.getName());
                map.put("Valid_Info",
                        DateUtil.dateToString(persIdentityCardInfoItem.getStartDate(), DateUtil.DateStyle.YYYYMMDD)
                                + "-"
                                + DateUtil.dateToString(persIdentityCardInfoItem.getEndDate(),
                                DateUtil.DateStyle.YYYYMMDD));
                map.put("SN_Num", persIdentityCardInfoItem.getPhysicalNo());
                map.put("ID_Num", persIdentityCardInfoItem.getIdCard());
                dataJson.add(map);
                json.put("data", dataJson);
                accCacheManager.setIdentityCardInfo(AccCacheKeyConstants.IDENTITY_CARD_INFO_KEY + physicalNo,
                        json.toJSONString());
            }
        }
        return json;
    }

    private String getEventName(AccDevice dev, String eventNo) {
        String ret = "";
        if (StringUtils.isNotBlank(dev.getSn()) && StringUtils.isNotBlank(eventNo)) {
            Map<String, String> eventMap = accDeviceService.getDevEvent(dev.getSn());
            if (!eventMap.isEmpty()) {
                if (eventMap.containsKey(eventNo)) {
                    ret = eventMap.get(eventNo);
                }
            }
        }
        if ("".equals(ret)) {
            ret = I18nUtil.i18nCode(AccConstants.EVENT_NO_UNDEFINED) + "<" + eventNo + ">";
        }
        return ret;
    }

    /**
     * 按事件类型解析：验证方式、事件点、事件名称
     *
     * @param eventNo
     * @param verifyModeNo
     * @param pin
     * @param cardNo
     * @param devSn
     * @param devId
     */
    private Map<String, Object> getEventTypeData(int eventNo, String verifyModeNo, String pin, String cardNo,
                                                 String devSn, String devId) {
        Map<String, Object> dataMap = Maps.newHashMap();
        int eventPointType = AccConstants.EVENT_POINT_TYPE_NONE;// 事件点类型
        String description = "";// 事件描述
        String linkIndex = "";// 固件联动id
        if (AccConstants.AUXOUT_EVENT.contains(eventNo)) {// 开启、关闭辅助输出事件
            eventPointType = AccConstants.EVENT_POINT_TYPE_AUX_OUT;
        } else if (AccConstants.AUXIN_EVENT.contains(eventNo)) {
            eventPointType = AccConstants.EVENT_POINT_TYPE_AUX_IN;
        } else if (eventNo == AccConstants.EVENT_LINKCONTROL) { // 联动事件
            int auxLinkageType = Integer.parseInt(verifyModeNo);// 联动时，返回触发的事件类型，复用了验证方式
            if (AccConstants.AUXIN_EVENT.contains(auxLinkageType)) { // 辅助输入短路、断路触发联动
                eventPointType = AccConstants.EVENT_POINT_TYPE_AUX_IN;
            }
            // else if(){}// 辅助输出事件
            else { // 门触发联动
                eventPointType = AccConstants.EVENT_POINT_TYPE_DOOR;
            }
            verifyModeNo = AccConstants.VERIFY_MODE_OTHERS.toString();// 验证方式为"其他"
            String linkageId = StringUtils.isBlank(cardNo) ? "0" : cardNo;// fieldMap.get("cardno");// 联动事件时，卡号位复用为联动id
            Map<String, String> retMap = accLinkageService.getLinkageAction(Integer.parseInt(linkageId), devId);
            if (retMap.containsKey("description")) {
                description = retMap.get("description");// 联动描述
            }
            linkIndex = linkageId;
        }
        /*
         * else if (eventNo == AccConstants.CANCEL_ALARM) { // 取消报警事件
         * eventPointType = AccConstants.EVENT_POINT_TYPE_DOOR;
         * int cancelAlarmType = Integer.parseInt(verifyModeNo);// 取消报警后返回的报警类型，复用了验证方式
         * if (AccConstants.CANCEL_ALARM_VALUE.contains(cancelAlarmType)) {
         * description =
         * AccConstants.CANCEL_ALARM_TYPE.get(Integer.parseInt(verifyModeNo));// 报警事件描述
         * verifyModeNo = AccConstants.VERIFY_MODE_OTHERS.toString();// 验证方式为"其他"
         * }
         * }
         */
        else { // 设备、门相关事件
            eventPointType = AccConstants.EVENT_POINT_TYPE_DOOR;// 门事件
            dataMap.put("personInfo", true);// 这个参数表示需要返回人员的名称、卡号、部门名称信息
            // 卡号是否为身份证号码，卡号为身份证号码不做转换
            boolean isIdCardNo = isIdCardNumberCheck(cardNo);
            if (!"".equals(cardNo) && "1".equals(accParamService.getParamValByName("pers.cardHex")) && !isIdCardNo) {
                cardNo = AccDeviceUtil.convertCardNo(cardNo, 10, 16);
            }
            if ("".equals(pin) && !"".equals(cardNo)) {// 查询软件上卡号是否已经发给了人员
                pin = persPersonService.getPersonPinByCardNo(cardNo);
            }
            Map<String, String> personInfoMap = getPersonInfo(pin);
            dataMap.put("pin", pin);
            dataMap.put("cardNo", cardNo);
            dataMap.put("name", personInfoMap.containsKey("name") ? personInfoMap.get("name") : "");
            dataMap.put("lastName", personInfoMap.containsKey("lastName") ? personInfoMap.get("lastName") : "");
            dataMap.put("photoPath", personInfoMap.containsKey("photoPath") ? personInfoMap.get("photoPath") : "");
            dataMap.put("deptCode", personInfoMap.containsKey("deptCode") ? personInfoMap.get("deptCode") : "");
            dataMap.put("deptName", personInfoMap.containsKey("deptName") ? personInfoMap.get("deptName") : "");
            dataMap.put("pinName", personInfoMap.containsKey("pinName") ? personInfoMap.get("pinName") : "");
            dataMap.put("email", personInfoMap.get("email"));
        }
        dataMap.put("eventPointType", eventPointType);
        dataMap.put("description", description);
        dataMap.put("verifyModeNo", verifyModeNo);
        dataMap.put("verifyModeName",
                accDeviceVerifyModeService.getVerifyModeNameByDevSnAndVerifyModeNo(devSn, verifyModeNo));
        dataMap.put("linkIndex", linkIndex);
        return dataMap;
    }

    /**
     * @param data
     * @param dev
     * @param areaName
     * @return
     * @Description: pull事件解析
     * <AUTHOR>
     * @date 2018/7/4 15:21
     */
    private Map<String, Map<String, String>> getDataMapByPull(String data, AccDevice dev, String areaName) {
        Map<String, Map<String, String>> retMap = Maps.newHashMap();
        String[] recordArr = StringUtils.split(data, "\r\n");
        Date minTimestamp = null;
        String minLogId = null;
        // 表头
        List<String> headList = new ArrayList<>();
        String[] headArray = recordArr[0].split(",");
        for (String head : headArray) {
            headList.add(head.toLowerCase());
        }
        // 数据
        String devId = dev.getId();
        boolean isSupportLogId = accDeviceOptionService.isSupportFun(dev.getSn(), "LogIDFunOn");
        String sn = dev.getSn();
        String[] infoArr = null;
        Map<String, String> tempMap = null;
        Map<String, Object> eventPersonMap = null;
        Map<String, String> eventPointMap = null;
        Timestamp currentTime = new Timestamp(System.currentTimeMillis());
        String createTime = DateUtil.dateToString(currentTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
        for (int i = 1, len = recordArr.length; i < len; i++) {
            tempMap = Maps.newHashMap();
            infoArr = recordArr[i].split(",");
            for (int j = 0, innerLen = infoArr.length; j < innerLen; j++) {
                String headStr = headList.get(j);
                if (headStr.equals("time_second")) {
                    Timestamp time = AccDataUtil.transformTime(infoArr[j]);
                    tempMap.put(headStr, DateUtil.dateToString(time, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                } else if (headStr.equals("pin") || headStr.equals("cardno")) {
                    tempMap.put(headStr, infoArr[j].equals("0") ? "" : infoArr[j]);
                } else {
                    tempMap.put(headStr, infoArr[j]);
                }
            }
            // 获取事件记录按照事件过滤事件功能
            String eventTimeStr = tempMap.get("time_second");
            if (StringUtils.isNotBlank(eventTimeStr)) {
                Date eventTime = DateUtil.stringToDate(eventTimeStr, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
                AccDeviceUploadTransactionItem deviceUploadTransactionItem = accCacheManager
                        .getTransactionValidTime(dev.getSn());
                if (deviceUploadTransactionItem != null
                        && "true".equals(deviceUploadTransactionItem.getSetValidDate())) {
                    Date startTime = deviceUploadTransactionItem.getStartTime();
                    Date endTime = deviceUploadTransactionItem.getEndTime();
                    // 事件时间小于开始时间
                    if (startTime != null && AccDataUtil.compareTime(eventTime, startTime) < 0) {
                        continue;
                    }
                    // 事件时间小于结束时间
                    if (endTime != null && AccDataUtil.compareTime(eventTime, endTime) > 0) {
                        continue;
                    }
                }
                if (!isSupportLogId) {
                    if (minTimestamp == null) {
                        minTimestamp = eventTime;
                    }
                    minTimestamp = AccDataUtil.compareTime(minTimestamp, eventTime) >= 0 ? eventTime : minTimestamp;
                }
            }
            String logIndex = tempMap.get("index");
            if (StringUtils.isNotBlank(logIndex) && isSupportLogId) {
                if (minLogId == null) {
                    minLogId = logIndex;
                }
                minLogId = (Integer.parseInt(minLogId) > Integer.parseInt(logIndex)) ? logIndex : minLogId;
            }
            // ----------------------------------
            int eventType = Integer.parseInt(tempMap.get("eventtype"));
            eventPersonMap = getEventTypeData(Integer.parseInt(tempMap.get("eventtype")), tempMap.get("verified"),
                    tempMap.get("pin"), tempMap.get("cardno"), dev.getSn(), devId);
            eventPointMap = getEventPointInfo(sn, Integer.parseInt(eventPersonMap.get("eventPointType") + ""),
                    Integer.parseInt(tempMap.get("doorid")));
            tempMap.put("verifyModeNo", (String) eventPersonMap.get("verifyModeNo"));
            tempMap.put("verifyModeName", (String) eventPersonMap.get("verifyModeName"));
            tempMap.put("area", areaName);
            tempMap.put("devId", String.valueOf(devId));
            tempMap.put("devSn", sn);
            tempMap.put("devAlias", dev.getAlias());
            tempMap.put("eventPointType",
                    eventPersonMap.containsKey("eventPointType") ? eventPersonMap.get("eventPointType").toString()
                            : "");
            tempMap.put("description",
                    eventPersonMap.containsKey("description") ? eventPersonMap.get("description").toString() : "");
            tempMap.put("name", eventPersonMap.containsKey("name") ? eventPersonMap.get("name").toString() : "");
            tempMap.put("lastName",
                    eventPersonMap.containsKey("lastName") ? eventPersonMap.get("lastName").toString() : "");
            tempMap.put("deptCode",
                    eventPersonMap.containsKey("deptCode") ? eventPersonMap.get("deptCode").toString() : "");
            tempMap.put("deptName",
                    eventPersonMap.containsKey("deptName") ? eventPersonMap.get("deptName").toString() : "");
            tempMap.put("eventPointId", eventPointMap.get("eventPointId"));
            tempMap.put("eventPointName", eventPointMap.get("eventPointName"));
            tempMap.put("eventName", getEventName(dev, String.valueOf(tempMap.get("eventtype"))));
            tempMap.put("createTime", createTime);// 插入当前时间做为创建时间
            tempMap.put("eventAddr", tempMap.get("doorid"));
            // 判断事件是否是联动
            tempMap.put("triggercond", tempMap.get("eventtype").equals("6") ? tempMap.get("verified") : "");
            tempMap.put("cardno", tempMap.get("eventtype").equals("6") ? ""
                    : eventPersonMap.get("cardNo") != null ? eventPersonMap.get("cardNo").toString() : "");
            if (tempMap.containsKey("inoutstate")) {
                short state = Short.parseShort(tempMap.get("inoutstate"));
                String readerName = I18nUtil.i18nCode("common_reader_state_2");// 此处请参照国际化标签
                // jsonRTLog.put("state", readerName);
                if (state != 2) { // 有进出状态,将状态改成读头名称
                    // AccReader accReader = accReaderBiz.findUniqueByProperties(new String[]
                    // {"readerState",
                    // "door.doorNo", "door.device.id"}, new Object[] {state,
                    // Short.parseShort(tempMap.get("doorid")),
                    // dev.getId()});

                    final AccQueryReaderItem accReader = getReaderCacheByDevSnAndDoorNoAndReaderState(sn,
                            tempMap.get("doorid"), state);
                    // AccReader accReader = accReaderDao.findByReaderStateAndAccDoor_Id(state,
                    // dev.getId());
                    if (Objects.nonNull(accReader)) {
                        readerName = accReader.getName();
                    }
                }
                tempMap.put("readerName", readerName);
            }
            StringBuffer keyStrBuf = new StringBuffer();
            String index = "-1";// 不支持的固件默认给-1
            if (isSupportLogId) {
                index = tempMap.get("index");
                // 避免设备事件表被清空后导致事件插入失败问题 2015.12.11 modified by dyl
                keyStrBuf.append(sn).append("_").append(index).append("_").append(tempMap.get("time_second"));
            } else if (AccConstants.CARD_PERSON_REPEATED_EVENT_FILTER.contains(eventType)) {// 避免卡相关未验证通过的事件重复插入
                // New:
                // Sn_Time_EventNo_VerifyMode_Pin_CardNo_PointType_EventPoint_ReaderState(in/out)_TriggerCond
                keyStrBuf.append(tempMap.get("devSn")).append("_").append(tempMap.get("time_second")).append("_")
                        .append(tempMap.get("eventtype")).append("_").append(tempMap.get("verifyModeNo")).append("_")
                        .append(tempMap.get("cardno")).append("_").append(tempMap.get("eventPointType")).append("_")
                        .append(tempMap.get("eventPointId")).append("_").append(tempMap.get("inoutstate")).append("_")
                        .append(eventPersonMap.get("linkIndex")).append("_").append(tempMap.get("triggercond"));
            } else {
                // New:
                // Sn_Time_EventNo_VerifyMode_Pin_CardNo_PointType_EventPoint_ReaderState(in/out)_TriggerCond
                keyStrBuf.append(tempMap.get("devSn")).append("_").append(tempMap.get("time_second")).append("_")
                        .append(tempMap.get("eventtype")).append("_").append(tempMap.get("verifyModeNo")).append("_")
                        .append(tempMap.get("pin")).append("_").append(tempMap.get("cardno")).append("_")
                        .append(tempMap.get("eventPointType")).append("_").append(tempMap.get("eventPointId"))
                        .append("_")
                        .append(tempMap.get("inoutstate")).append("_").append(eventPersonMap.get("linkIndex"))
                        .append("_")
                        .append(tempMap.get("triggercond"));
                // keyStrBuf.append(tempMap.get("cardno")).append("_").append(tempMap.get("pin")).append("_").append(tempMap.get("eventtype")).append("_")
                // .append(tempMap.get("inoutstate")).append("_").append(tempMap.get("time_second")).append("_").append(tempMap.get("devSn")).append("_")
                // .append(tempMap.get("verifyModeNo")).append("_").append(tempMap.get("eventPointId")).append("_").append(tempMap.get("eventPointType"))
                // .append("_").append(tempMap.get("triggercond"));
            }
            tempMap.put("unique", keyStrBuf.toString());
            tempMap.put("index", index);
            retMap.put(keyStrBuf.toString(), tempMap);
            tempMap = eventPointMap = null;
            keyStrBuf = null;
            eventPersonMap = null;
        }
        // 将最小时间返回
        if (retMap.size() > 0) {
            Map<String, String> minDateTime = new HashMap<>();
            if (!isSupportLogId) {
                minDateTime.put("minTimestamp",
                        DateUtil.dateToString(minTimestamp, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                retMap.put("minTimestamp", minDateTime);
            } else {
                minDateTime.put("minTimestamp", minLogId);
                retMap.put("minTimestamp", minDateTime);
            }
            minDateTime = null;
        }
        return retMap;
    }

    @Override
    public int countByDeviceSnAndDoorNo(String deviceSn, Short doorNo, Date startDatetime, Date endDatetime) {
        return accTransactionDao.countByDeviceSnAndDoorNo(deviceSn, doorNo, startDatetime, endDatetime);
    }

    /**
     * @param pin
     * @return
     * @Description: 判断是否是访客人员
     * <AUTHOR>
     * @date 2018/8/14 11:57
     */
    private boolean isVisVisitor(String pin) {
        if (pin.startsWith("8") && pin.length() == 9) {
            return true;
        }
        return false;
    }

    /**
     * @param pin
     * @return
     * @Description: 判断是否是酒店人员
     * <AUTHOR>
     * @date 2018/8/14 11:57
     */
    private boolean isHotelPerson(String pin) {
        if (pin.startsWith("9") && pin.length() == 9) {
            return true;
        }
        return false;
    }

    @Override
    public List<AccTransactionItem> getBySNAndDoorNo(String deviceSn, Short doorNo, Date startDatetime,
                                                     Date endDatetime) {
        AccTransactionSNandDoorItem item = new AccTransactionSNandDoorItem();
        item.setDevSn(deviceSn);
        item.setEventAddr(doorNo);
        item.setBeginDate(startDatetime);
        item.setEndDate(endDatetime);
        List<AccTransactionSNandDoorItem> items = (List<AccTransactionSNandDoorItem>) accTransactionDao
                .getItemsBySql(item.getClass(), SQLUtil.getSqlByItem(item));

        List<AccTransactionItem> accTransactionItems = new ArrayList<>();
        for (AccTransactionSNandDoorItem transactionSNandDoorItem : items) {
            AccTransactionItem accTransactionItem = new AccTransactionItem();
            accTransactionItems.add(ModelUtil.copyPropertiesIgnoreNull(transactionSNandDoorItem, accTransactionItem));
        }
        return accTransactionItems;
    }

    @Override
    public Pager getAccTransactionBySnAndDoorNo(String deviceSn, Short doorNo, Date startDatetime, Date endDatetime,
                                                int page, int size) {
        Pager pager = new Pager(page, size);
        List<AccTransaction> accTransactions = accTransactionDao.getTransactionsForAtt(deviceSn, doorNo, startDatetime,
                endDatetime);
        if (accTransactions != null && accTransactions.size() > 0) {
            List<AccTransactionItem> accTransactionItems = ModelUtil.copyListProperties(accTransactions,
                    AccTransactionItem.class);
            pager.setData(accTransactionItems);
        }
        return pager;
    }

    @Override
    public void handlerTransfer(List<AccTransactionItem> accTransactionItems) {
        // 以防大数据时进行分批数据
        List<List<AccTransactionItem>> accTransactionItemList = CollectionUtil.split(accTransactionItems,
                CollectionUtil.splitSize);
        for (List<AccTransactionItem> accTransItems : accTransactionItemList) {
            // 获取全部记录,封装map 减少查询
            Collection<String> keys = CollectionUtil.getPropertyList(accTransItems, AccTransactionItem::getUniqueKey,
                    "-1");
            List<AccTransaction> accTransactions = accTransactionDao.findByUniqueKeyIn(keys);
            Map<String, AccTransaction> accLinkageMap = CollectionUtil.listToKeyMap(accTransactions,
                    AccTransaction::getUniqueKey);
            for (AccTransactionItem accTransactionItem : accTransItems) {
                AccTransaction accTransaction = accLinkageMap.remove(accTransactionItem.getUniqueKey());
                if (Objects.isNull(accTransaction)) {
                    accTransaction = new AccTransaction();
                }
                ModelUtil.copyPropertiesIgnoreNullWithProperties(accTransactionItem, accTransaction, "id");
                accTransactionDao.save(accTransaction);
            }
            accLinkageMap = null;
        }
        accTransactionItemList = null;
    }

    public Map<String, JSONObject> getDetailDevSn(AccQueryDeviceItem dev) {
        Map<String, JSONObject> devMap = Maps.newHashMap();
        if (Objects.nonNull(dev)) {
            devMap.put(dev.getSn(), getDevInfo(dev));
        }
        return devMap;
    }

    private JSONObject getDevInfo(AccQueryDeviceItem accDevice) {
        JSONObject devJson = new JSONObject();
        devJson.put("id", accDevice.getId());
        devJson.put("alias", accDevice.getAlias());
        devJson.put("sn", accDevice.getSn());
        devJson.put("machineType", accDevice.getMachineType());
        devJson.put("enabled", accDevice.getEnabled());
        // max 添加设备区域相关信息-20140717 add
        devJson.put("areaId", accDevice.getAuthAreaId());
        devJson.put("areaName", accDevice.getAuthAreaName());
        return devJson;
    }

    @Override
    public long getTransactionsCount() {
        return accTransactionDao.count();
    }

    @Override
    public void addRTLog(String sn, String tempLog) {
        accCacheManager.setAccRTLogToCache(sn, tempLog);
    }

    @Override
    public ApiResultMessage getAccApiTransactionsBySn(String deviceSn, Date startDate, Date endDate, int pageNo,
                                                      int pageSize) {
        if (StringUtils.isBlank(deviceSn)) {
            return ApiResultMessage.message(AccConstants.ACC_TRANSACTION_SNNOTNULL,
                    I18nUtil.i18nCode("acc_api_devSnNotNull"));
        }
        AccTransactionItem accTransactionItem = new AccTransactionItem();
        accTransactionItem.setDevSnEqual(deviceSn);
        if (startDate != null && endDate != null) {
            accTransactionItem.setStartTime(startDate);
            accTransactionItem.setEndTime(endDate);
        }
        List<AccApiTransactionItem> apiTransactionItemList = Lists.newArrayList();
        int beginIndex = (pageNo - 1) * pageSize;
        int endIndex = pageNo * pageSize - 1;
        List<AccTransactionItem> accTransactionItemList = accTransactionDao.getItemsDataBySql(AccTransactionItem.class,
                SQLUtil.getSqlByItem(accTransactionItem), beginIndex, endIndex, true);
        if (!accTransactionItemList.isEmpty()) {
            accTransactionItemList.forEach(accTrans -> apiTransactionItemList.add(buildApiTransaction(accTrans)));
        }
        return ApiResultMessage.successMessage(apiTransactionItemList);
    }

    @Override
    public ApiResultMessage getAccApiTransactionsByPin(String pin, Date startDate, Date endDate, int pageNo,
                                                       int pageSize) {
        if (StringUtils.isBlank(pin)) {
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                    I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }
        AccTransactionItem accTransactionItem = new AccTransactionItem();
        accTransactionItem.setPinEqual(pin);
        if (startDate != null && endDate != null) {
            accTransactionItem.setStartTime(startDate);
            accTransactionItem.setEndTime(endDate);
        }
        List<AccApiTransactionItem> apiTransactionItemList = Lists.newArrayList();
        int beginIndex = (pageNo - 1) * pageSize;
        int endIndex = pageNo * pageSize - 1;
        List<AccTransactionItem> accTransactionItemList = accTransactionDao.getItemsDataBySql(AccTransactionItem.class,
                SQLUtil.getSqlByItem(accTransactionItem), beginIndex, endIndex, true);
        if (!accTransactionItemList.isEmpty()) {
            accTransactionItemList.forEach(accTrans -> apiTransactionItemList.add(buildApiTransaction(accTrans)));
        }
        return ApiResultMessage.successMessage(apiTransactionItemList);
    }

    @Override
    public List<AccApiTransactionItem> getAccApiTransactionList(String pin, Date startDate, Date endDate, int pageNo,
                                                                int pageSize) {
        AccTransactionItem accTransactionItem = new AccTransactionItem();
        // 设置查询条件
        if (StringUtils.isNotBlank(pin)) {
            accTransactionItem.setPinEqual(pin);
        }
        if (startDate != null && endDate != null) {
            accTransactionItem.setStartTime(startDate);
            accTransactionItem.setEndTime(endDate);
        }
        int beginIndex = (pageNo - 1) * pageSize;
        int endIndex = pageNo * pageSize - 1;
        List<AccTransactionItem> accTransactionItemList = accTransactionDao.getItemsDataBySql(AccTransactionItem.class,
                SQLUtil.getSqlByItem(accTransactionItem), beginIndex, endIndex, true);
        List<AccApiTransactionItem> apiTransactionItemList = Lists.newArrayList();
        if (!accTransactionItemList.isEmpty()) {
            accTransactionItemList.forEach(accTrans -> apiTransactionItemList.add(buildApiTransaction(accTrans)));
        }
        return apiTransactionItemList;
    }

    @Override
    public List<AccApiTransactionItem> getRTMonitorData(String timestamp) {
        List<AccApiTransactionItem> apiTransactionItemList = Lists.newArrayList();
        List<AccTransactionItem> accTransactionItemList = accRTMonitorService.getRTMonitorData(timestamp);
        if (!accTransactionItemList.isEmpty()) {
            accTransactionItemList.forEach(accTrans -> apiTransactionItemList.add(buildApiTransaction(accTrans)));
        }
        return apiTransactionItemList;
    }

    /**
     * 组装门禁记录数据
     *
     * @param
     * @return
     * @auther lambert.li
     * @date 2018/11/15 11:54
     */
    private AccApiTransactionItem buildApiTransaction(AccTransactionItem accTransactionItem) {
        AccApiTransactionItem apiTransactionItem = new AccApiTransactionItem();
        ModelUtil.copyPropertiesIgnoreNull(accTransactionItem, apiTransactionItem);
        apiTransactionItem.setEventTime(
                DateUtil.dateToString(accTransactionItem.getEventTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        apiTransactionItem.setDevName(accTransactionItem.getDevAlias());
        apiTransactionItem.setEventName(I18nUtil.i18nCode(accTransactionItem.getEventName()));
        apiTransactionItem.setVerifyModeName(I18nUtil.i18nCode(accTransactionItem.getVerifyModeName()));
        apiTransactionItem.setEventLevel(getEventLevel(null, accTransactionItem.getEventNo()));
        apiTransactionItem.setDoorName(accTransactionItem.getEventPointName());
        String capturePhotoBase64 = FileEncryptUtil.getDecryptFileBase64(accTransactionItem.getCapturePhotoPath());
        apiTransactionItem.setCapturePhotoBase64(capturePhotoBase64);
        apiTransactionItem.setDoorNumber(accTransactionItem.getEventAddr());
        apiTransactionItem.setReaderState(accTransactionItem.getReaderState());
        return apiTransactionItem;
    }

    @Override
    public List<Object[]> findByEventTime(Date startDate, Date endDate) {
        return accTransactionDao.findByEventTime(startDate, endDate);
    }

    @Override
    public JSONObject getIdentityCardInfo(String cardNo) {
        JSONObject resultData = accCacheManager
                .getIdentityCardInfo(AccCacheKeyConstants.IDENTITY_CARD_INFO_KEY + cardNo);
        return resultData;
    }

    @Override
    public ZKResultMsg viewVidLinkData(String vidLinkageHandle, String fileType) {
        ZKResultMsg ret = new ZKResultMsg();
        if (Objects.nonNull(Vid4OtherGetVidLinkageEventService)) {
            ret = Vid4OtherGetVidLinkageEventService.getVidLinkageEventData(vidLinkageHandle, fileType);
        }
        return ret;
    }

    @Override
    public Map<String, Object> getVideoFileValidate(String transactionId) {
        if (Objects.nonNull(vid4OtherGetVidTransactionService)) {
            return vid4OtherGetVidTransactionService.getVideoFileValidate(transactionId);
        }
        return null;
    }

    @Override
    public void executeAccDataClean(String paramValue) {
        // 清除门禁记录
        baseDataCleanService.execute("AccTransaction", "eventTime", paramValue);
        // 清除人员进出记录
        baseDataCleanService.execute("AccFirstInLastOut", "firstInTime", paramValue);
        // 清除一体机抓拍照片
        delOldPhotos(paramValue);
    }

    /**
     * 定时清理门禁一体机抓拍的旧照片
     *
     * @return
     * <AUTHOR>
     * @Param [keptMonth]
     * @since 2019-09-03 17:25
     */
    private void delOldPhotos(String paramValue) {
        String photoPath = systemFilePath + File.separator + AccConstants.LINKAGE_PHOTO_PATH + File.separator;
        File photoFile = new File(photoPath);
        if (!photoFile.isAbsolute()) { // 判断是否是绝对路径
            photoPath = ClassUtil.getRootPath() + "/" + photoPath;
        }
        delPhoto(photoPath, paramValue);
    }

    /**
     * 递归删除文件
     *
     * @return
     * <AUTHOR>
     * @Param [photoPath, keptMonth]
     * @since 2019-09-04 17:00
     */
    private void delPhoto(String photoPath, String paramValue) {
        JSONObject json = JSON.parseObject(paramValue);
        String keptPhotoType = BaseDataConstants.DATA_CLEAN_KEPTMONTH;
        if (json.containsKey("keptPhotoType")) {
            keptPhotoType = json.getString("keptPhotoType");
        }
        File photoFile = new File(photoPath);
        // 遍历目录下的所有文件夹
        if (photoFile.exists()) {
            File[] files = photoFile.listFiles();
            for (File file : files) {
                if (file.isDirectory()) {
                    delPhoto(file.getAbsolutePath(), paramValue);
                } else {
                    try {
                        Date beforeDate = null;
                        String keptPhoto = json.getString("keptPhoto");
                        if (BaseDataConstants.DATA_CLEAN_KEPTDAY.equals(keptPhotoType)) {
                            beforeDate = DateUtil.addDays(new Date(), -(Integer.parseInt(keptPhoto)));
                        } else if (BaseDataConstants.DATA_CLEAN_KEPWEEK.equals(keptPhotoType)) {
                            beforeDate = DateUtil.addWeeks(new Date(), -(Integer.parseInt(keptPhoto)));
                        } else {
                            beforeDate = DateUtil.addMonth(new Date(), -(Integer.parseInt(keptPhoto)));
                        }
                        FileTime time = Files
                                .readAttributes(Paths.get(file.getAbsolutePath()), BasicFileAttributes.class)
                                .creationTime();
                        if (time.toMillis() < beforeDate.getTime()) { // 保存天数之前的所有文件都删除
                            file.delete();
                        }
                    } catch (Exception e) {
                        log.error("delPhoto error", e);
                    }
                }
            }
        }
    }

    @Override
    public void sendAlarmSMS(String content) {
        String smsReceiver = accCacheManager.getCacheValueByKey(AccCacheKeyConstants.ACC_ALARM_SMSRECEIVER);
        if (smsReceiver == null || "".equals(smsReceiver.trim())) {
            smsReceiver = FoldexUtil.decryptByRandomSey(baseSysParamService.getValByName("acc.smsReceiver"));
            if (smsReceiver != null && !"".equals(smsReceiver.trim())) {
                accCacheManager.set(AccCacheKeyConstants.ACC_ALARM_SMSRECEIVER, smsReceiver);
            }
        }
        if (smsModem4OtherService != null && smsReceiver != null && !"".equals(smsReceiver.trim())) {
            // 短信一条一条发送
            for (String tempReceiver : smsReceiver.split(",")) {
                smsModem4OtherService.sendSMS(tempReceiver.trim(), content, BaseConstants.ACC);
            }
        }
    }

    @Override
    public List getItemAfterTime(Date time, int page, int pageSize) {
        AccTransactionItem filter = new AccTransactionItem();
        filter.setAfterTime(time);
        Pager pager = accTransactionDao.getItemsBySql(filter.getClass(), SQLUtil.getSqlByItem(filter), page, pageSize);
        return pager.getData();
    }

    @Override
    public String getMailContent(AccTransactionItem accTransactionItem) {
        String name = getPersonAllName(accTransactionItem.getName(), accTransactionItem.getLastName());
        name = StringUtils.isNotBlank(name) ? accTransactionItem.getPin() + "(" + name + ")"
                : accTransactionItem.getPin();

        String eventName = accTransactionItem.getEventName();
        if (AccConstants.CUSTOM_EVENT_NORMAL_GLOBALLINKAGE == accTransactionItem.getEventNo()
                || accDeviceEventService.isLinkageEventByNo(accTransactionItem.getEventNo())) {
            Map<String, String> eventMap = accDeviceService.getDevEvent(accTransactionItem.getDevSn());
            eventName = getEventNameByEventNo(eventMap, accTransactionItem.getTriggerCond());
        }
        String[] verifyModeName = accTransactionItem.getVerifyModeName().split(",");
        String newVerifyModeName = "";
        for (String verifyMode : verifyModeName) {
            newVerifyModeName += I18nUtil.i18nCode(verifyMode) + ",";
        }
        return I18nUtil.i18nCode("common_time") + ": "
                + DateUtil.dateToString(accTransactionItem.getEventTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS)
                + "<br/>"
                + I18nUtil.i18nCode("base_area_name") + ": " + accTransactionItem.getAreaName() + "<br/>"
                + I18nUtil.i18nCode("common_dev_name") + ": " + accTransactionItem.getDevAlias() + "<br/>"
                + I18nUtil.i18nCode("common_eventPoint") + ": " + accTransactionItem.getEventPointName() + "<br/>"
                + I18nUtil.i18nCode("common_eventDescription") + ": " + I18nUtil.i18nCode(eventName) + "<br/>"
                + I18nUtil.i18nCode("pers_card_cardNo") + ": " + accTransactionItem.getCardNo() + "<br/>"
                + I18nUtil.i18nCode("pers_person") + ": " + name + "<br/>"
                + I18nUtil.i18nCode("acc_readerDefine_readerName") + ": " + accTransactionItem.getReaderName() + "<br/>"
                + I18nUtil.i18nCode("common_verifyMode_entiy") + ": "
                + newVerifyModeName.substring(0, newVerifyModeName.length() - 1);
    }

    @Override
    public ZKResultMsg getDigifortVideoFile(String cameraName, long eventTime) {
        if (Objects.nonNull(accGetIVideoService)) {
            return accGetIVideoService.getDigifortVideoFile(cameraName, eventTime);
        }
        return ZKResultMsg.failMsg();
    }

    @Override
    public Pager getAccTransactionByDoorIds(String doorIds, Date startDatetime, Date endDatetime, int page, int size) {
        AccTransactionItem accTransactionItem = new AccTransactionItem();
        accTransactionItem.setStartTime(startDatetime);
        accTransactionItem.setEndTime(endDatetime);
        accTransactionItem.setInEventPointId(doorIds);
        // 人员编号不为空
        String sqlStr = getSqlByPinNotNull(accTransactionItem);
        return accTransactionDao.getItemsBySql(accTransactionItem.getClass(), sqlStr, page, size);
    }

    @Override
    public Pager getAccTransactionByDoorIdsAndCreateTime(String doorIds, Date startCreateTime, Date endCreateTime,
                                                         int page, int size) {
        AccTransactionItem accTransactionItem = new AccTransactionItem();
        accTransactionItem.setStartCreateTime(startCreateTime);
        accTransactionItem.setEndCreateTime(endCreateTime);
        accTransactionItem.setInEventPointId(doorIds);
        // 人员编号不为空
        String sqlStr = getSqlByPinNotNull(accTransactionItem);
        return accTransactionDao.getItemsBySql(accTransactionItem.getClass(), sqlStr, page, size);
    }

    @Override
    public String getSqlByPinNotNull(AccTransactionItem condition) {
        // 过滤联动事件
        String eventNoFilter = AccConstants.EVENT_LINKCONTROL + "," + AccConstants.CUSTOM_EVENT_NORMAL_GLOBALLINKAGE;
        condition.setNotInEventNo(eventNoFilter);
        // 表示门事件
        condition.setEventPointType(Short.valueOf(AccConstants.EVENT_POINT_TYPE_DOOR + ""));
        // 人员编号不为空
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(SQLUtil.getSqlByItem(condition));
        int orderByIndex = stringBuilder.indexOf("ORDER BY");
        String dbType = DataSourceConfig.getDbType();
        String sqlStr;
        if (ZKConstant.ORACLE.equals(dbType)) {
            sqlStr = "t.PIN" + " is not null";
        } else {
            sqlStr = "t.PIN" + " <> '' and " + "t.PIN" + " is not null";
        }
        stringBuilder.insert(orderByIndex, " AND (" + sqlStr + ")");
        return stringBuilder.toString();
    }

    /**
     * 发送短信给该人员
     *
     * @param accTransaction
     * @param dataMap
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-15 14:43
     */
    private void sendSMS(AccTransactionItem accTransaction, Map<String, Object> dataMap) {
        String content = getSMSContent(accTransaction);
        String smsReceiver = dataMap.get("mobilePhone") + "";
        if (StringUtils.isNotBlank(smsReceiver) && smsModem4OtherService != null && smsReceiver != null) {
            smsModem4OtherService.sendSMS(smsReceiver.trim(), content, BaseConstants.ACC);
        }
    }

    @Override
    public Pager loadAlarmTransactionByAuthUserFilter(String sessionId, AccAlarmTransactionItem condition, int pageNo,
                                                      int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setDeptCodeInByUserId(userId);
            condition.setAreaNameByUserId(userId);
        }
        Pager pager = getItemsByPage(condition, pageNo, pageSize);
        List<AccAlarmTransactionItem> accAlarmTransactionItemList = (List<AccAlarmTransactionItem>) pager.getData();
        for (AccAlarmTransactionItem accAlarmTransactionItem : accAlarmTransactionItemList) {
            // 构建媒体文件字段
            String jsonStr = buildVidLinkageHandle(accAlarmTransactionItem.getCapturePhotoPath(),
                    accAlarmTransactionItem.getVidLinkageHandle());
            accAlarmTransactionItem.setVidLinkageHandle(jsonStr);
        }
        return pager;
    }

    @Override
    public Pager loadAlarmTransactionByAuthUserFilter(String sessionId, AccAlarmTransactionItem condition, int pageNo,
                                                      int pageSize, long limitCount) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setDeptCodeInByUserId(userId);
            condition.setAreaNameByUserId(userId);
        }
        Pager pager = new Pager();
        StringBuilder sql = new StringBuilder();
        if (condition.getEventLevel() != null) {
            sql.append(SQLUtil.getSqlByItem(condition));
            int orderByIndex = sql.indexOf("ORDER BY");
            if (AccConstants.EVENT_NORMAL == condition.getEventLevel()) {
                sql.insert(orderByIndex,
                        "AND (t.EVENT_NO < 20 AND t.EVENT_NO != 700 OR (t.EVENT_NO >= 200 AND t.EVENT_NO < 500) OR (t.EVENT_NO >= 4000 AND t.EVENT_NO < 5000))");
            } else if (AccConstants.EVENT_WARNING == condition.getEventLevel()) {
                sql.insert(orderByIndex,
                        "AND (t.EVENT_NO >= 20 AND t.EVENT_NO < 100 AND t.EVENT_NO != 28 OR (t.EVENT_NO >= 500 AND t.EVENT_NO != 700) OR (t.EVENT_NO >=5000 AND t.EVENT_NO <6000))");
            } else if (AccConstants.EVENT_ALARM == condition.getEventLevel()) {
                sql.insert(orderByIndex,
                        "AND (t.EVENT_NO >= 100 AND t.EVENT_NO < 200 OR t.EVENT_NO = 28 OR t.EVENT_NO = 700 OR (t.EVENT_NO >=6000 AND t.EVENT_NO <7000))");
            }
            pager = accTransactionDao.getItemsBySql(condition.getClass(), sql.toString(), pageNo, pageSize);
        } else {
            pager = getItemsByPage(condition, pageNo, pageSize, limitCount);
        }
        List<AccAlarmTransactionItem> accAlarmTransactionItemList = (List<AccAlarmTransactionItem>) pager.getData();
        for (AccAlarmTransactionItem accAlarmTransactionItem : accAlarmTransactionItemList) {
            // 构建媒体文件字段
            String jsonStr = buildVidLinkageHandle(accAlarmTransactionItem.getCapturePhotoPath(),
                    accAlarmTransactionItem.getVidLinkageHandle());
            accAlarmTransactionItem.setVidLinkageHandle(jsonStr);
            accAlarmTransactionItem.setPin(accAlarmTransactionItem.getPin());
            accAlarmTransactionItem.setCardNo(accAlarmTransactionItem.getCardNo());
            // 事件级别
            short eventLevel = accDeviceEventService.getDefaultEventTypeById(accAlarmTransactionItem.getEventNo());
            // 如果是报警事件，则拼接报警事件等级
            String levelAndEventPriority = eventLevel + "";
            if (AccConstants.EVENT_ALARM == eventLevel && Objects.nonNull(accAlarmTransactionItem.getEventPriority())) {
                levelAndEventPriority += "#" + accAlarmTransactionItem.getEventPriority();
            }
            accAlarmTransactionItem.setLevelAndEventPriority(levelAndEventPriority);
            accAlarmTransactionItem
                    .setDevAlias(
                            accAlarmTransactionItem.getDevAlias() + "(" + accAlarmTransactionItem.getDevSn() + ")");
        }
        return pager;
    }

    @Override
    public Pager loadTodayTransactionByAuthUserFilter(String sessionId, AccTransactionTodayItem condition, int pageNo,
                                                      int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setDeptCodeInByUserId(userId);
            condition.setAreaNameByUserId(userId);
        }
        Pager pager = getItemsByPage(condition, pageNo, pageSize);
        List<AccTransactionTodayItem> accTransactionTodayItemList = (List<AccTransactionTodayItem>) pager.getData();
        for (AccTransactionTodayItem accTransactionTodayItem : accTransactionTodayItemList) {
            // 构建媒体文件字段
            String jsonStr = buildVidLinkageHandle(accTransactionTodayItem.getCapturePhotoPath(),
                    accTransactionTodayItem.getVidLinkageHandle());
            accTransactionTodayItem.setVidLinkageHandle(jsonStr);
        }
        return pager;
    }

    @Override
    public Pager loadTodayTransactionByAuthUserFilter(String sessionId, AccTransactionTodayItem condition, int pageNo,
                                                      int pageSize, long limitCount) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setDeptCodeInByUserId(userId);
            condition.setAreaNameByUserId(userId);
        }
        Pager pager = new Pager();
        StringBuilder sql = new StringBuilder();
        if (condition.getEventLevel() != null) {
            sql.append(SQLUtil.getSqlByItem(condition));
            int orderByIndex = sql.indexOf("ORDER BY");
            if (AccConstants.EVENT_NORMAL == condition.getEventLevel()) {
                sql.insert(orderByIndex,
                        "AND (t.EVENT_NO < 20 AND t.EVENT_NO != 700 OR (t.EVENT_NO >= 200 AND t.EVENT_NO < 500) OR (t.EVENT_NO >= 4000 AND t.EVENT_NO < 5000))");
            } else if (AccConstants.EVENT_WARNING == condition.getEventLevel()) {
                sql.insert(orderByIndex,
                        "AND (t.EVENT_NO >= 20 AND t.EVENT_NO < 100 AND t.EVENT_NO != 28 OR (t.EVENT_NO >= 500 AND t.EVENT_NO != 700) OR (t.EVENT_NO >=5000 AND t.EVENT_NO <6000))");
            } else if (AccConstants.EVENT_ALARM == condition.getEventLevel()) {
                sql.insert(orderByIndex,
                        "AND (t.EVENT_NO >= 100 AND t.EVENT_NO < 200 OR t.EVENT_NO = 28 OR t.EVENT_NO = 700 OR (t.EVENT_NO >=6000 AND t.EVENT_NO <7000))");
            }
            pager = accTransactionDao.getItemsBySql(condition.getClass(), sql.toString(), pageNo, pageSize);
        } else {
            pager = getItemsByPage(condition, pageNo, pageSize, limitCount);
        }
        List<AccTransactionTodayItem> accTransactionTodayItemList = (List<AccTransactionTodayItem>) pager.getData();
        for (AccTransactionTodayItem accTransactionTodayItem : accTransactionTodayItemList) {
            // 构建媒体文件字段
            String jsonStr = buildVidLinkageHandle(accTransactionTodayItem.getCapturePhotoPath(),
                    accTransactionTodayItem.getVidLinkageHandle());
            accTransactionTodayItem.setVidLinkageHandle(jsonStr);
            accTransactionTodayItem.setPin(accTransactionTodayItem.getPin());
            accTransactionTodayItem.setCardNo(accTransactionTodayItem.getCardNo());
            // 事件级别
            short eventLevel = accDeviceEventService.getDefaultEventTypeById(accTransactionTodayItem.getEventNo());
            // 如果是报警事件，则拼接报警事件等级
            String levelAndEventPriority = eventLevel + "";
            if (AccConstants.EVENT_ALARM == eventLevel && Objects.nonNull(accTransactionTodayItem.getEventPriority())) {
                levelAndEventPriority += "#" + accTransactionTodayItem.getEventPriority();
            }
            accTransactionTodayItem.setLevelAndEventPriority(levelAndEventPriority);
            accTransactionTodayItem
                    .setDevAlias(
                            accTransactionTodayItem.getDevAlias() + "(" + accTransactionTodayItem.getDevSn() + ")");
        }
        return pager;
    }

    @Override
    public ApiResultMessage getAccApiTransactionsById(String id) {
        AccTransactionItem accTransactionItem = getItemById(id);
        AccApiTransactionItem apiTransactionItem = null;
        if (accTransactionItem != null) {
            apiTransactionItem = buildApiTransaction(accTransactionItem);
        }
        return ApiResultMessage.successMessage(apiTransactionItem);
    }

    @Override
    public Pager loadTransactionByAuthUserFilter(String sessionId, AccTransactionItem condition, int pageNo,
                                                 int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setDeptCodeInByUserId(userId);
            condition.setAreaNameByUserId(userId);
        }
        Pager pager = getItemsByPage(condition, pageNo, pageSize);
        List<AccTransactionItem> accTransactionItemList = (List<AccTransactionItem>) pager.getData();
        for (AccTransactionItem accTransactionItem : accTransactionItemList) {
            // 构建媒体文件字段
            String jsonStr = buildVidLinkageHandle(accTransactionItem.getCapturePhotoPath(),
                    accTransactionItem.getVidLinkageHandle());
            accTransactionItem.setVidLinkageHandle(jsonStr);
        }
        return pager;
    }

    @Override
    public Pager loadTransactionByAuthUserFilter(String sessionId, AccTransactionItem condition, int pageNo,
                                                 int pageSize, long limitCount) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setDeptCodeInByUserId(userId);
            condition.setAreaNameByUserId(userId);
        }
        Pager pager = new Pager();
        StringBuilder sql = new StringBuilder();
        if (condition.getEventLevel() != null) {
            sql.append(SQLUtil.getSqlByItem(condition));
            int orderByIndex = sql.indexOf("ORDER BY");
            if (AccConstants.EVENT_NORMAL == condition.getEventLevel()) {
                sql.insert(orderByIndex,
                        "AND (t.EVENT_NO < 20 AND t.EVENT_NO != 700 OR (t.EVENT_NO >= 200 AND t.EVENT_NO < 500) OR (t.EVENT_NO >= 4000 AND t.EVENT_NO < 5000))");
            } else if (AccConstants.EVENT_WARNING == condition.getEventLevel()) {
                sql.insert(orderByIndex,
                        "AND (t.EVENT_NO >= 20 AND t.EVENT_NO < 100 AND t.EVENT_NO != 28 OR (t.EVENT_NO >= 500 AND t.EVENT_NO != 700) OR (t.EVENT_NO >=5000 AND t.EVENT_NO <6000))");
            } else if (AccConstants.EVENT_ALARM == condition.getEventLevel()) {
                sql.insert(orderByIndex,
                        "AND (t.EVENT_NO >= 100 AND t.EVENT_NO < 200 OR t.EVENT_NO = 28 OR t.EVENT_NO = 700 OR (t.EVENT_NO >=6000 AND t.EVENT_NO <7000))");
            }
            pager = accTransactionDao.getItemsBySql(condition.getClass(), sql.toString(), pageNo, pageSize);
        } else {
            pager = getItemsByPage(condition, pageNo, pageSize, limitCount);
        }
        List<AccTransactionItem> accTransactionItemList = (List<AccTransactionItem>) pager.getData();
        for (AccTransactionItem accTransactionItem : accTransactionItemList) {
            // 构建媒体文件字段
            String jsonStr = buildVidLinkageHandle(accTransactionItem.getCapturePhotoPath(),
                    accTransactionItem.getVidLinkageHandle());
            accTransactionItem.setVidLinkageHandle(jsonStr);
            accTransactionItem.setPin(accTransactionItem.getPin());
            accTransactionItem.setCardNo(accTransactionItem.getCardNo());
            // 事件级别
            short eventLevel = accDeviceEventService.getDefaultEventTypeById(accTransactionItem.getEventNo());
            // 如果是报警事件，则拼接报警事件等级
            String levelAndEventPriority = eventLevel + "";
            if (AccConstants.EVENT_ALARM == eventLevel && Objects.nonNull(accTransactionItem.getEventPriority())) {
                levelAndEventPriority += "#" + accTransactionItem.getEventPriority();
            }
            accTransactionItem.setLevelAndEventPriority(levelAndEventPriority);
            accTransactionItem
                    .setDevAlias(accTransactionItem.getDevAlias() + "(" + accTransactionItem.getDevSn() + ")");
        }
        return pager;
    }

    /**
     * 设置媒体文件字段JSON对象；兼容硬联动、逻辑联动，前端解析
     *
     * @return void
     * <AUTHOR>
     * @date 2022-07-20 11:17
     * @since 1.0.0
     */
    private String buildVidLinkageHandle(String photoPath, String vidLinkageHandle) {
        JSONObject data = new JSONObject();
        // 兼容升级，一体机抓拍图标显示和查看
        if (StringUtils.isNotBlank(vidLinkageHandle) && vidLinkageHandle.startsWith(AccConstants.LINKAGE_PHOTO_PATH)) {
            if (FileUtils.fileExists(vidLinkageHandle)) {
                vidLinkageHandle = AccConstants.FILE_EXIST + "_" + vidLinkageHandle;
            }
        }
        if (StringUtils.isNotBlank(photoPath) && photoPath.startsWith(AccConstants.LINKAGE_PHOTO_PATH)) {
            if (FileUtils.fileExists(photoPath)) {
                data.put("photoPath", AccConstants.FILE_EXIST + "_" + photoPath);
            }
        }
        data.put("vidLinkageHandle", vidLinkageHandle);
        return data.toJSONString();
    }

    @Override
    public List<AccTransactionPhotoItem> getPhotoFilePathList(String sessionId, AccTransactionPhotoItem condition) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setDeptCodeInByUserId(userId);
            condition.setAreaNameByUserId(userId);
        }

        // 过滤不是图片类型的事件
        List<String> notInEventNoList = new ArrayList<>();
        // 触发联动事件
        notInEventNoList.add("" + AccConstants.EVENT_LINKCONTROL);
        // 触发全局联动
        notInEventNoList.add("" + AccConstants.CUSTOM_EVENT_NORMAL_GLOBALLINKAGE);
        condition.setNotInEventNo(StringUtils.join(notInEventNoList, ","));

        // 查询事件图片路径分多批次查询
        Pager pager = accTransactionDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), 0,
                CollectionUtil.splitSize);
        // 获取总条数计算分批次数
        int total = pager.getTotal();
        int pageCount = total / CollectionUtil.splitSize;
        if (total % CollectionUtil.splitSize > 0) {
            pageCount++;
        }

        Set<String> filePathSet = new HashSet<>();
        List<AccTransactionPhotoItem> accTransactionPhotoItems = new ArrayList<>();

        List<AccTransactionPhotoItem> itemList = (List<AccTransactionPhotoItem>) pager.getData();
        if (!CollectionUtil.isEmpty(itemList)) {
            buildExportTransactionPhotoItem(filePathSet, accTransactionPhotoItems, itemList);
        }
        // 查询剩余页数
        for (int i = 1; i < pageCount; i++) {
            pager = accTransactionDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), i,
                    CollectionUtil.splitSize);
            itemList = (List<AccTransactionPhotoItem>) pager.getData();
            buildExportTransactionPhotoItem(filePathSet, accTransactionPhotoItems, itemList);
        }

        return accTransactionPhotoItems;
    }

    /**
     * 设置导出照片事件的抓拍照片
     *
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccTransactionPhotoItem>
     * <AUTHOR>
     * @date 2023-02-10 14:12
     * @since 1.0.0
     */
    public List<AccTransactionPhotoItem> buildExportTransactionPhotoItem(Set<String> filePathSet,
                                                                         List<AccTransactionPhotoItem> accTransactionPhotoItems, List<AccTransactionPhotoItem> itemList) {
        if (!CollectionUtil.isEmpty(itemList)) {
            itemList.forEach(item -> {
                String capturePhotoPath = item.getCapturePhotoPath();
                if (StringUtils.isBlank(capturePhotoPath)) {
                    capturePhotoPath = item.getVidLinkageHandle();
                }
                if (StringUtils.isNotBlank(capturePhotoPath)) {
                    if (filePathSet.contains(capturePhotoPath)) {
                        return;
                    }
                    item.setCapturePhotoPath(capturePhotoPath);
                    filePathSet.add(capturePhotoPath);
                    // 压缩目录 设备SN/日期/
                    String compressDirectory = item.getDevSn() + File.separator
                            + DateUtil.dateToString(item.getEventTime(), DateUtil.DateStyle.YYYY_MM_DD)
                            + File.separator;
                    item.setCompressDirectory(compressDirectory);
                    accTransactionPhotoItems.add(item);
                }
            });
        }
        return accTransactionPhotoItems;
    }

    @Override
    public String getAreaNamesBySessionId(String sessionId) {
        return authSessionProvider.getAreaNamesNoSubNodesByAuthFilter(sessionId);
    }

    @Override
    public String getDeptCodesBySessionId(String sessionId) {
        return authSessionProvider.getDeptCodeNoSubNodesByAuthFilter(sessionId);
    }

    @Override
    public Long countAccTransactionByCondition(AccTransactionItem accTransactionItem) {
        List<Short> eventNoIn = new ArrayList<>();
        if (StringUtils.isNotBlank(accTransactionItem.getInEventNo())) {
            List<String> eventNos = StrUtil.strToList(accTransactionItem.getInEventNo());
            for (String eventNo : eventNos) {
                eventNoIn.add(Short.parseShort(eventNo));
            }
        }
        if (accTransactionItem.getStartCreateTime() != null) {
            return accTransactionDao.countByCondition(accTransactionItem.getDevSn(),
                    accTransactionItem.getStartCreateTime(), accTransactionItem.getEndCreateTime(), eventNoIn);
        } else {
            return accTransactionDao.countByProperties(accTransactionItem.getDevSn(),
                    accTransactionItem.getEndCreateTime(), eventNoIn);
        }
    }

    /**
     * 拼装一体机照片路径
     *
     * @param tempDevSn
     * @param logTime
     * @param pin
     * @return
     */
    private String getAttPhoto(String tempDevSn, String logTime, String pin) {
        String photoName = logTime.replace("-", "").replace(":", "").replace(" ", "");
        /*
         * if (StringUtils.isNotBlank(pin)) {
         * photoName += "-" + pin + ".jpg";
         * } else {
         */
        // 由于设备照片文件名是否带pin规则过于复杂且不好兼容，所以统一只使用时间来命名文件，处理照片的代码统一修改
        photoName += ".jpg";
        // }
        return AccConstants.LINKAGE_PHOTO_PATH + "/" + tempDevSn + "/" + photoName;
    }

    @Override
    public String getSMSContent(AccTransactionItem accTransactionItem) {
        String name = getPersonAllName(accTransactionItem.getName(), accTransactionItem.getLastName());
        name = StringUtils.isNotBlank(name) ? accTransactionItem.getPin() + "(" + name + ")"
                : accTransactionItem.getPin();

        String eventName = accTransactionItem.getEventName();
        if (AccConstants.CUSTOM_EVENT_NORMAL_GLOBALLINKAGE == (short) accTransactionItem.getEventNo()
                || AccConstants.EVENT_LINKCONTROL == (short) accTransactionItem.getEventNo()) {
            Map<String, String> eventMap = accDeviceService.getDevEvent(accTransactionItem.getDevSn());
            eventName = getEventNameByEventNo(eventMap, accTransactionItem.getTriggerCond());
        }
        String[] verifyModeName = accTransactionItem.getVerifyModeName().split(",");
        String newVerifyModeName = "";
        for (String verifyMode : verifyModeName) {
            newVerifyModeName += I18nUtil.i18nCode(verifyMode) + ",";
        }
        return I18nUtil.i18nCode("common_time") + ": "
                + DateUtil.dateToString(accTransactionItem.getEventTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS)
                + "\n"
                + I18nUtil.i18nCode("base_area_name") + ": " + accTransactionItem.getAreaName() + "\n"
                + I18nUtil.i18nCode("common_dev_name") + ": " + accTransactionItem.getDevAlias() + "\n"
                + I18nUtil.i18nCode("common_eventPoint") + ": " + accTransactionItem.getEventPointName() + "\n"
                + I18nUtil.i18nCode("common_eventDescription") + ": " + I18nUtil.i18nCode(eventName) + "\n"
                + I18nUtil.i18nCode("pers_card_cardNo") + ": " + accTransactionItem.getCardNo() + "\n"
                + I18nUtil.i18nCode("pers_person") + ": " + name + "\n"
                + I18nUtil.i18nCode("acc_readerDefine_readerName")
                + ": " + accTransactionItem.getReaderName() + "\n" + I18nUtil.i18nCode("common_verifyMode_entiy") + ": "
                + newVerifyModeName.substring(0, newVerifyModeName.length() - 1);
    }

    @Override
    public ApiResultMessage getAccApiFirstInLastOutByPin(String pin, Date startDate, Date endDate, int pageNo,
                                                         int pageSize) {
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.failedMessage(AccConstants.API_WRONG_PAGE);
        } else if (StringUtils.isBlank(pin)) {
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                    I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }
        AccFirstInLastOutItem accFirstInLastOutItem = new AccFirstInLastOutItem();
        accFirstInLastOutItem.setPinEqual(pin);
        if (startDate != null && endDate != null) {
            accFirstInLastOutItem.setStartTime(startDate);
            accFirstInLastOutItem.setEndTime(endDate);
        }
        List<AccApiFirstInLastOutItem> apiFirstInLastOutItemList = Lists.newArrayList();
        int beginIndex = (pageNo - 1) * pageSize;
        int endIndex = pageNo * pageSize - 1;
        List<AccFirstInLastOutItem> accFirstInLastOutItemList = accFirstInLastOutDao.getItemsDataBySql(
                AccFirstInLastOutItem.class, SQLUtil.getSqlByItem(accFirstInLastOutItem), beginIndex, endIndex, true);
        if (!accFirstInLastOutItemList.isEmpty()) {
            accFirstInLastOutItemList.forEach(item -> apiFirstInLastOutItemList.add(buildApiFirstInLastOut(item)));
        }
        return ApiResultMessage.successMessage(apiFirstInLastOutItemList);
    }

    /**
     * 组装门禁记录数据
     *
     * @param
     * @return
     * @auther lambert.li
     * @date 2018/11/15 11:54
     */
    private AccApiFirstInLastOutItem buildApiFirstInLastOut(AccFirstInLastOutItem accFirstInLastOutItem) {
        AccApiFirstInLastOutItem apiFirstInLastOutItem = new AccApiFirstInLastOutItem();
        ModelUtil.copyPropertiesIgnoreNull(accFirstInLastOutItem, apiFirstInLastOutItem);
        apiFirstInLastOutItem.setFirstInTime(
                DateUtil.dateToString(accFirstInLastOutItem.getFirstInTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        apiFirstInLastOutItem.setLastOutTime(
                DateUtil.dateToString(accFirstInLastOutItem.getLastOutTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        return apiFirstInLastOutItem;
    }

    /**
     * 解析Best协议事件（Mqtt Pro拓展）
     *
     * @param data
     * @param dev
     * @param areaName
     * @return
     */
    private Map<String, Map<String, String>> getDataMapByMqtt(String data, AccDevice dev, String areaName) {
        AccDevice tempDev = null;
        String minLogId = null;
        Map<String, Map<String, String>> retMap = Maps.newHashMap();
        Map<String, AccDevice> tempDevMap = Maps.newHashMap();
        tempDevMap.put(dev.getSn(), dev);
        String createTime = DateUtil.dateToString(new Timestamp(System.currentTimeMillis()),
                DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
        Map<String, String> fieldMap = null;
        Long minTimestamp = null;
        JSONArray jsonArray = JSONObject.parseArray(data);
        for (int i = 0; i < jsonArray.size(); i++) {
            fieldMap = Maps.newHashMap();
            JSONObject val = jsonArray.getJSONObject(i);
            Long timestamp = getTimestamp(val.getString("time"));
            if (minTimestamp == null) {
                minTimestamp = timestamp;
            } else {
                minTimestamp = Math.min(minTimestamp, timestamp);
            }
            fieldMap.put("time_second", formatTime(val.getString("time")));
            String pin = val.getString("operator");
            fieldMap.put("pin", pin);
            String cardno = val.getString("card");
            fieldMap.put("cardno", val.getString("card"));
            fieldMap.put("index", val.getString("logNum"));
            String eventType = val.getString("eventType");
            fieldMap.put("eventtype", eventType);
            String verified = val.getString("verifyStyle");
            if (StringUtils.isNoneBlank(verified) && !StringUtils.equals("0", verified)) {
                // 二进制转十进制
                Integer verifyInt = Integer.valueOf(verified, 2);
                if (verifyInt != null) {
                    verified = verifyInt.toString();
                    fieldMap.put("verified", verified);
                }
            } else {
                fieldMap.put("verified", verified);
            }
            String doorId = val.getString("doorNum");
            fieldMap.put("doorid", doorId);
            if (minLogId == null) {
                minLogId = val.getString("linkageNum");
            } else {
                int linkageNum = Math.min(Integer.parseInt(minLogId), Integer.parseInt(val.getString("linkageNum")));
                minLogId = linkageNum + "";
            }
            tempDev = dev;
            // cardno = AccDeviceUtil.convertCardNo(cardno, 16, 10);
            Map<String, Object> eventPersonMap = getEventTypeData(Integer.parseInt(eventType), verified, pin, cardno,
                    tempDev.getSn(), tempDev.getId());
            Map<String, String> eventPointMap = getEventPointInfo(tempDev.getSn(),
                    Integer.parseInt(eventPersonMap.get("eventPointType") + ""), Integer.parseInt(doorId));
            fieldMap.put("verifyModeNo",
                    eventPersonMap.containsKey("verifyModeNo") ? eventPersonMap.get("verifyModeNo").toString() : "");
            fieldMap.put("verifyModeName",
                    eventPersonMap.containsKey("verifyModeName") ? eventPersonMap.get("verifyModeName").toString()
                            : "");
            fieldMap.put("devId", tempDev.getId().toString());
            fieldMap.put("devSn", tempDev.getSn());
            fieldMap.put("devAlias", tempDev.getAlias());
            fieldMap.put("area", areaName);
            fieldMap.put("eventPointType",
                    eventPersonMap.containsKey("eventPointType") ? eventPersonMap.get("eventPointType").toString()
                            : "");
            fieldMap.put("description",
                    eventPersonMap.containsKey("description") ? eventPersonMap.get("description").toString() : "");
            fieldMap.put("name", eventPersonMap.containsKey("name") ? eventPersonMap.get("name").toString() : "");
            fieldMap.put("lastName",
                    eventPersonMap.containsKey("lastName") ? eventPersonMap.get("lastName").toString() : "");
            fieldMap.put("deptName",
                    eventPersonMap.containsKey("deptName") ? eventPersonMap.get("deptName").toString() : "");
            fieldMap.put("eventPointId", eventPointMap.get("eventPointId"));
            fieldMap.put("eventPointName", eventPointMap.get("eventPointName"));
            String eventName = getEventName(tempDev, String.valueOf(fieldMap.get("eventtype")));
            fieldMap.put("eventName", eventName);
            // 插入当前时间做为创建时间
            fieldMap.put("createTime", createTime);
            fieldMap.put("triggercond", fieldMap.get("eventtype").equals("6") ? fieldMap.get("verified") : "");
            // fieldMap.put("cardno", fieldMap.get("eventtype").equals("6") ? ""
            // : eventPersonMap.get("cardNo") != null ?
            // eventPersonMap.get("cardNo").toString() : "");

            fieldMap.put("eventAddr", getAddrByJsonVal(val, "auxInNum", "auxOutNum", "readerNum", "doorNum "));
            if (val.containsKey("readerNum") && StringUtils.isNotBlank(val.getString("readerNum"))) {
                String readerNum = val.getString("readerNum");
                final AccQueryReaderItem accReader = getReaderCacheByDevSnAndReaderNum(tempDev.getSn(), readerNum);
                // AccReader accReader =
                // accReaderDao.getReaderByReaderNoAndDevId(Short.valueOf(readerNum),
                // tempDev.getId());
                if (Objects.nonNull(accReader)) {
                    fieldMap.put("readerName", accReader.getName());
                }
            }
            String index = fieldMap.get("index");
            StringBuffer keyStrBuf = new StringBuffer();
            keyStrBuf.append(tempDev.getSn()).append("_").append(fieldMap.get("index")).append("_")
                    .append(fieldMap.get("time_second"));
            fieldMap.put("index", index);
            fieldMap.put("unique", keyStrBuf.toString());
            retMap.put(keyStrBuf.toString(), fieldMap);
        }

        // 将最小时间返回
        if (retMap.size() > 0) {
            Map<String, String> minDateTime = Maps.newHashMap();
            minDateTime.put("minTimestamp", minLogId);
            retMap.put("minTimestamp", minDateTime);
            minDateTime = null;
        }
        return retMap;
    }

    /**
     * "yyyy-MM-dd'T'HH:mm:ssXXX" 转 "MM-dd HH:mm:ss"
     *
     * @param time
     * @return
     */
    private String formatTime(String time) {
        // 移除时区信息, 使用设备的时间
        if (StringUtils.isNotBlank(time)) {
            return time.substring(0, 19).replace("T", " ");
        }
        return DateUtil.dateToString(new Date(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 处理带时区的时间格式，转换成毫秒
     *
     * @param time
     * @return
     */
    private static Long getTimestamp(String time) {
        String[] times = new String[2];
        int i;
        if (time.lastIndexOf("+") > 0) {
            i = time.lastIndexOf("+");
        } else if (time.lastIndexOf("-") > 0) {
            i = time.lastIndexOf("-");
        } else {
            throw new ZKBusinessException("error time");
        }
        times[0] = StringUtils.substring(time, 0, i);
        times[1] = StringUtils.substring(time, i, time.length());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
        LocalDateTime local = LocalDateTime.parse(times[0], formatter);
        ZonedDateTime zonedDateTime = ZonedDateTime.of(local, ZoneId.of(times[1]));
        return zonedDateTime.toInstant().toEpochMilli();
    }

    /**
     * 返回第一个满足的
     *
     * @param val
     * @param keys
     * @return
     */
    private String getAddrByJsonVal(JSONObject val, String... keys) {
        for (int i = 0; i < keys.length; i++) {
            if (val.containsKey(keys[i]) && StringUtils.isNotBlank(val.getString(keys[i]))) {
                return val.getString(keys[i]);
            }
        }
        return "";
    }

    @Override
    public ZKResultMsg getAccApiTransactionsBySnAndPin(String deviceSn, String pin, Date startDate, Date endDate,
                                                       int pageNo, int pageSize) {
        if (StringUtils.isBlank(pin)) {
            return ZKResultMsg.failMsg(I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        AccTransactionItem accTransactionItem = new AccTransactionItem();
        // 根据人员编号进行过滤
        accTransactionItem.setPinEqual(pin);
        // 根据设备编号进行过滤，deviceSn为空则获取人员所有门禁事件记录，不为空则获取人员在某台设备的门禁事件记录
        if (StringUtils.isNotBlank(deviceSn)) {
            accTransactionItem.setDevSnEqual(deviceSn);
        }
        if (startDate != null && endDate != null) {
            accTransactionItem.setStartTime(startDate);
            accTransactionItem.setEndTime(endDate);
        }
        List<AccApiTransactionItem> apiTransactionItemList = Lists.newArrayList();
        int beginIndex = (pageNo - 1) * pageSize;
        int endIndex = pageNo * pageSize - 1;
        List<AccTransactionItem> accTransactionItemList = accTransactionDao.getItemsDataBySql(AccTransactionItem.class,
                SQLUtil.getSqlByItem(accTransactionItem), beginIndex, endIndex, true);
        if (!accTransactionItemList.isEmpty()) {
            accTransactionItemList.forEach(accTrans -> apiTransactionItemList.add(buildApiTransaction(accTrans)));
            resultMsg.setData(apiTransactionItemList);
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public ZKResultMsg getAccApiTransactionsByFilterAndType(String deviceSn, String filter, String type, String eventNo,
                                                            Date startDate, Date endDate, int pageNo, int pageSize) {
        // if (StringUtils.isBlank(deviceSn)) {
        // return ZKResultMsg.failMsg(I18nUtil.i18nCode("acc_api_devSnNotNull"));
        // }
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        AccTransactionItem accTransactionItem = new AccTransactionItem();
        // 根据设备编号进行过滤
        accTransactionItem.setDevSnEqual(deviceSn);
        // 根据开始时间、结束时间进行过滤
        if (startDate != null && endDate != null) {
            accTransactionItem.setStartTime(startDate);
            accTransactionItem.setEndTime(endDate);
        }
        // 根据事件编号进行过滤
        if (StringUtils.isNotBlank(eventNo)) {
            accTransactionItem.setEventNo(Short.valueOf(eventNo));
        }
        // 根据人员编号或姓名、事件类型进行过滤
        String querySql = getTransactionSqlByFilter(accTransactionItem, filter, type);

        List<AccApiTransactionItem> apiTransactionItemList = Lists.newArrayList();
        int beginIndex = (pageNo - 1) * pageSize;
        int endIndex = pageNo * pageSize - 1;
        List<AccTransactionItem> accTransactionItemList = accTransactionDao
                .getItemsDataBySql(accTransactionItem.getClass(), querySql, beginIndex, endIndex, true);
        if (!accTransactionItemList.isEmpty()) {
            accTransactionItemList.forEach(accTrans -> apiTransactionItemList.add(buildApiTransaction(accTrans)));
            resultMsg.setData(apiTransactionItemList);
        }

        return I18nUtil.i18nMsg(resultMsg);
    }

    /**
     * 根据过滤条件组装事件记录查询sql
     *
     * @param accTransactionItem
     * @param type
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021-06-15 16:42
     * @since 1.0.0
     */
    private String getTransactionSqlByFilter(AccTransactionItem accTransactionItem, String filter, String type) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(SQLUtil.getSqlByItem(accTransactionItem));
        int orderByIndex = stringBuilder.indexOf("ORDER BY");
        // 根据人员编号或姓名进行过滤
        if (StringUtils.isNotBlank(filter)) {
            stringBuilder.insert(orderByIndex, MessageFormat.format(
                    "AND (t.PIN LIKE ''%{0}%'' OR  t.NAME LIKE ''%{0}%'' " + "OR t.LAST_NAME LIKE ''%{0}%'')", filter));
        }
        // 根据事件类型进行过滤
        if (StringUtils.isNotBlank(type)) {
            if ("normal".equals(type)) {
                stringBuilder.insert(orderByIndex,
                        "AND (t.EVENT_NO < 20 AND t.EVENT_NO != 700 OR (t.EVENT_NO >= 200 AND t.EVENT_NO < 500)) ");
            } else if ("warning".equals(type)) {
                stringBuilder.insert(orderByIndex,
                        "AND (t.EVENT_NO >= 20 AND t.EVENT_NO < 100 AND t.EVENT_NO != 28 OR (t.EVENT_NO >= 500 AND t.EVENT_NO != 700))");
            } else if ("alarm".equals(type)) {
                stringBuilder.insert(orderByIndex,
                        "AND (t.EVENT_NO >= 100 AND t.EVENT_NO < 200 OR t.EVENT_NO = 28 OR t.EVENT_NO = 700)");
            }
        }
        return stringBuilder.toString();
    }

    @Override
    public ZKResultMsg getAccApiTransactionDetailById(String id) {
        if (StringUtils.isBlank(id)) {
            return ZKResultMsg.failMsg();
        }
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        AccTransactionItem accTransactionItem = getItemById(id);
        if (Objects.nonNull(accTransactionItem)) {
            resultMsg.setData(buildApiTransaction(accTransactionItem));
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public boolean isExistCardOp(Short eventNo) {
        return eventNo != null && eventNo == 27;
    }

    @Override
    public String getLineContent(AccTransactionItem newTransaction) {
        String name = getPersonAllName(newTransaction.getName(), newTransaction.getLastName());
        name = StringUtils.isNotBlank(name) ? newTransaction.getPin() + "(" + name + ")" : newTransaction.getPin();

        String eventName = newTransaction.getEventName();
        if (AccConstants.CUSTOM_EVENT_NORMAL_GLOBALLINKAGE == newTransaction.getEventNo()
                || accDeviceEventService.isLinkageEventByNo(newTransaction.getEventNo())) {
            Map<String, String> eventMap = accDeviceService.getDevEvent(newTransaction.getDevSn());
            eventName = getEventNameByEventNo(eventMap, newTransaction.getTriggerCond());
        }
        String[] verifyModeName = newTransaction.getVerifyModeName().split(",");
        String newVerifyModeName = "";
        for (String verifyMode : verifyModeName) {
            newVerifyModeName += I18nUtil.i18nCode(verifyMode) + ",";
        }
        String lineContent = "DateTime："
                + DateUtil.dateToString(newTransaction.getEventTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS) + "\n"
                + "Area:" + newTransaction.getAreaName() + "\n" + "Event Description:" + I18nUtil.i18nCode(eventName)
                + "\n"
                + "Person:" + name + "\n" + "Verification Mode:" + newVerifyModeName + "\n" + "Reader Name:"
                + newTransaction.getReaderName() + "\n" + "Door Name:" + newTransaction.getDoorName() + "\n"
                + "Device: "
                + newTransaction.getDevAlias() + "(" + newTransaction.getDevSn() + ")\n";
        return lineContent;
    }

    /**
     * 门禁事件Item转换为事件中心Item
     *
     * @param other2EventCenterItem
     * @param accTransactionItem
     * @return void
     * <AUTHOR> xiao
     * @Date 2021-01-11 18:12
     */
    private void conversionEventItem(Other2EventCenterItem other2EventCenterItem,
                                     AccTransactionItem accTransactionItem) {
        // 没有事件点为门禁控制发生的事件
        if (String.valueOf(AccConstants.EVENT_POINT_TYPE_NONE).equals(accTransactionItem.getEventPointId())) {
            other2EventCenterItem.setObjName(accTransactionItem.getDevAlias());
            other2EventCenterItem.setObjType(EventCenterConstants.EVENT_OBJ_TYPE_DEV);
            other2EventCenterItem.setObjKey(accTransactionItem.getDevId());
            other2EventCenterItem.setSourceKey(accTransactionItem.getDevId());
            other2EventCenterItem.setSourceName(accTransactionItem.getDevAlias());
        } else {
            // 门 = 设备 读头，辅助输入输出=部件
            String objType = "";
            if (StringUtils.isNotBlank(accTransactionItem.getPin())) {
                String personName = getPersonAllName(accTransactionItem.getName(), accTransactionItem.getLastName());
                other2EventCenterItem.setObjName(personName);
                other2EventCenterItem.setObjKey(accTransactionItem.getPin());
                objType = EventCenterConstants.EVENT_OBJ_TYPE_PER;
            } else {
                other2EventCenterItem.setObjName(accTransactionItem.getEventPointName());
                other2EventCenterItem.setObjKey(accTransactionItem.getEventPointId());
                Integer eventPointType = Integer.valueOf(accTransactionItem.getEventPointType());
                if (eventPointType.equals(AccConstants.EVENT_POINT_TYPE_DOOR)) {
                    objType = EventCenterConstants.EVENT_OBJ_TYPE_DEV;
                } else if (eventPointType.equals(AccConstants.EVENT_POINT_TYPE_AUX_IN)
                        || eventPointType.equals(AccConstants.EVENT_POINT_TYPE_AUX_OUT)) {
                    objType = EventCenterConstants.EVENT_OBJ_TYPE_SUBSET;
                }
            }
            other2EventCenterItem.setObjType(objType);
            other2EventCenterItem.setSourceKey(accTransactionItem.getEventPointId());
            other2EventCenterItem.setSourceName(accTransactionItem.getEventPointName());
        }
        other2EventCenterItem.setDeviceName(accTransactionItem.getDevAlias());
        other2EventCenterItem.setEventNameKey(accTransactionItem.getEventName());
        String eventName = buildToEventCenterEventName(accTransactionItem);
        other2EventCenterItem.setEventName(eventName);
        other2EventCenterItem.setEventTime(accTransactionItem.getEventTime());
        other2EventCenterItem.setAreaId(accTransactionItem.getAreaId());
        other2EventCenterItem.setAreaName(accTransactionItem.getAreaName());
        other2EventCenterItem.setSourceModule(ConstUtil.SYSTEM_MODULE_ACC);
        Short eventNo = accTransactionItem.getEventNo();
        other2EventCenterItem.setEventTypeCode(String.valueOf(eventNo));
        other2EventCenterItem.setInOutStatus(accTransactionItem.getReaderState());
        other2EventCenterItem.setEventLevelVal(convertPushEventCenterEventLevel(accTransactionItem.getEventLevel()));
        List<String> videoChannelIds = (List<String>) accTransactionItem.getVidLinkageVideoChannelId();
        if (!CollectionUtil.isEmpty(videoChannelIds)) {
            // 绑定多个摄像机，取一个做预览、回放
            other2EventCenterItem.setVideoChannelId(videoChannelIds.get(0));
        }
        String getCapturePhotoPath = accTransactionItem.getCapturePhotoPath();
        if (StringUtils.isNotBlank(getCapturePhotoPath)) {
            other2EventCenterItem.setPanoramaImgURL(getCapturePhotoPath);
        } else {
            List<String> vidLinkageFilePathData = (List<String>) accTransactionItem.getVidLinkageFilePathData();
            if (!CollectionUtil.isEmpty(vidLinkageFilePathData)) {
                // 读头绑定多个摄像机、取一张图片作为抓拍资源
                other2EventCenterItem.setPanoramaImgURL(vidLinkageFilePathData.get(0));
            }
        }
    }

    /**
     * 转换推送事件中心的事件等级
     *
     * @param eventLevel:门禁事件等级
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021-10-14 12:02
     * @since 1.0.0
     */
    private String convertPushEventCenterEventLevel(Integer eventLevel) {
        String newEventLevel = EventCenterConstants.EVENT_LEVEL_NORMAL;
        if (Objects.nonNull(eventLevel)) {
            if (AccConstants.EVENT_WARNING == eventLevel) {
                newEventLevel = EventCenterConstants.EVENT_LEVEL_WARNING;
            } else if (AccConstants.EVENT_ALARM == eventLevel) {
                newEventLevel = EventCenterConstants.EVENT_LEVEL_EMERGENCY;
            }
        }
        return newEventLevel;
    }

    /**
     * 获取触发联动事件-事件名称组合值； 目的：触发联动事件设置为告警或异常事件，推送地图中心的触发联动事件有对应的事件名称
     *
     * @param accTransactionItem:
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022-10-28 17:07
     * @since 1.0.0
     */
    private String buildToEventCenterEventName(AccTransactionItem accTransactionItem) {
        String eventName = I18nUtil.i18nCode(accTransactionItem.getEventName());
        String description = accTransactionItem.getDescription();
        short eventNo = accTransactionItem.getEventNo();
        if (StringUtils.isNotBlank(description) && (eventNo == AccConstants.EVENT_LINKCONTROL
                || eventNo == AccConstants.CUSTOM_EVENT_NORMAL_GLOBALLINKAGE)) {
            // 1.获取触发联动的事件名称
            String triggerConditionName = description.split(";")[1].split(":")[1];
            // 2.组合联动事件名称：触发联动事件[事件名称]
            if (StringUtils.isNotBlank(triggerConditionName)) {
                eventName += "[" + I18nUtil.i18nCode(triggerConditionName) + "]";
            }
        }
        return eventName;
    }

    private AccQueryReaderItem getReaderCacheByDevSnAndReaderNum(String sn, String readerNum) {
        final AccQueryDeviceItem dev = accDeviceService.getQueryItemBySn(sn);
        final Optional<AccQueryReaderItem> readerItemOptional = dev.getAccDoorItemList().stream()
                .flatMap(list -> list.getAccReaderItemList().stream())
                .filter(reader -> readerNum.equals(reader.getReaderNo() + "")).findFirst();
        return readerItemOptional.orElse(null);
    }

    @Override
    public AccQueryReaderItem getReaderCacheByDevSnAndDoorNoAndReaderState(String sn, String doorNo,
                                                                           Short readerState) {
        final AccQueryDeviceItem dev = accDeviceService.getQueryItemBySn(sn);
        final Optional<AccQueryDoorItem> doorCache = dev.getAccDoorItemList().stream()
                .filter(door -> doorNo.equals(door.getDoorNo() + "")).findFirst();
        return doorCache.flatMap(accQueryDoorItem -> accQueryDoorItem.getAccReaderItemList().stream()
                .filter(reader -> readerState.equals(reader.getReaderState())).findFirst()).orElse(null);
    }

    @Override
    public Pager getAccApiTransactionsBySnAndPage(String deviceSn, Date startTime, Date endTime, Integer pageNo,
                                                  Integer pageSize) {

        AccTransactionItem accTransactionItem = new AccTransactionItem();
        accTransactionItem.setDevSnEqual(deviceSn);
        if (startTime != null && endTime != null) {
            accTransactionItem.setStartTime(startTime);
            accTransactionItem.setEndTime(endTime);
        }
        List<AccApiTransactionItem> apiTransactionItemList = Lists.newArrayList();
        Pager pager = getItemsByPage(accTransactionItem, pageNo - 1, pageSize);
        List<AccTransactionItem> accTransactionItemList = (List<AccTransactionItem>) pager.getData();
        if (!accTransactionItemList.isEmpty()) {
            accTransactionItemList.forEach(accTrans -> apiTransactionItemList.add(buildApiTransaction(accTrans)));
        }
        pager.setData(apiTransactionItemList);
        return pager;
    }

    @Override
    public Pager getAccApiTransactionsByPinAndPage(String pin, Date startTime, Date endTime, Integer pageNo,
                                                   Integer pageSize) {
        AccTransactionItem accTransactionItem = new AccTransactionItem();
        accTransactionItem.setPinEqual(pin);
        if (startTime != null && endTime != null) {
            accTransactionItem.setStartTime(startTime);
            accTransactionItem.setEndTime(endTime);
        }
        List<AccApiTransactionItem> apiTransactionItemList = Lists.newArrayList();
        Pager pager = getItemsByPage(accTransactionItem, pageNo - 1, pageSize);
        List<AccTransactionItem> accTransactionItemList = (List<AccTransactionItem>) pager.getData();
        if (!accTransactionItemList.isEmpty()) {
            accTransactionItemList.forEach(accTrans -> apiTransactionItemList.add(buildApiTransaction(accTrans)));
        }
        pager.setData(apiTransactionItemList);
        return pager;
    }

    @Override
    public Pager getAccApiTransactionListByPage(String personPin, Date startTime, Date endTime, Integer pageNo,
                                                Integer pageSize) {
        AccTransactionItem accTransactionItem = new AccTransactionItem();
        // 设置查询条件
        if (StringUtils.isNotBlank(personPin)) {
            accTransactionItem.setPinEqual(personPin);
        }
        if (startTime != null && endTime != null) {
            accTransactionItem.setStartTime(startTime);
            accTransactionItem.setEndTime(endTime);
        }
        Pager pager = getItemsByPage(accTransactionItem, pageNo - 1, pageSize);
        List<AccTransactionItem> accTransactionItemList = (List<AccTransactionItem>) pager.getData();
        List<AccApiTransactionItem> apiTransactionItemList = Lists.newArrayList();
        if (!accTransactionItemList.isEmpty()) {
            accTransactionItemList.forEach(accTrans -> apiTransactionItemList.add(buildApiTransaction(accTrans)));
        }
        pager.setData(apiTransactionItemList);
        return pager;
    }

    @Override
    public Pager getAccApiFirstInLastOutByPinAndPage(String pin, Date startTime, Date endTime, Integer pageNo,
                                                     Integer pageSize) {
        AccFirstInLastOutItem accFirstInLastOutItem = new AccFirstInLastOutItem();
        accFirstInLastOutItem.setPinEqual(pin);
        if (startTime != null && endTime != null) {
            accFirstInLastOutItem.setStartTime(startTime);
            accFirstInLastOutItem.setEndTime(endTime);
        }
        List<AccApiFirstInLastOutItem> apiFirstInLastOutItemList = Lists.newArrayList();
        Pager pager = accFirstInLastOutService.getItemsByPage(accFirstInLastOutItem, pageNo - 1, pageSize);
        List<AccFirstInLastOutItem> accFirstInLastOutItemList = (List<AccFirstInLastOutItem>) pager.getData();
        if (!accFirstInLastOutItemList.isEmpty()) {
            accFirstInLastOutItemList.forEach(item -> apiFirstInLastOutItemList.add(buildApiFirstInLastOut(item)));
        }
        pager.setData(apiFirstInLastOutItemList);
        return pager;
    }

    @Override
    public ZKResultMsg getBySnAndPinAndPage(String devSn, String personPin, Date startDate, Date endDate,
                                            Integer pageNo, Integer pageSize) {
        if (StringUtils.isBlank(personPin)) {
            return ZKResultMsg.failMsg(I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        AccTransactionItem accTransactionItem = new AccTransactionItem();
        // 根据人员编号进行过滤
        accTransactionItem.setPinEqual(personPin);
        // 根据设备编号进行过滤，deviceSn为空则获取人员所有门禁事件记录，不为空则获取人员在某台设备的门禁事件记录
        if (StringUtils.isNotBlank(devSn)) {
            accTransactionItem.setDevSnEqual(devSn);
        }
        if (startDate != null && endDate != null) {
            accTransactionItem.setStartTime(startDate);
            accTransactionItem.setEndTime(endDate);
        }
        List<AccApiTransactionItem> apiTransactionItemList = Lists.newArrayList();
        Pager pager = getItemsByPage(accTransactionItem, pageNo - 1, pageSize);
        List<AccTransactionItem> accTransactionItemList = (List<AccTransactionItem>) pager.getData();
        if (!accTransactionItemList.isEmpty()) {
            accTransactionItemList.forEach(accTrans -> apiTransactionItemList.add(buildApiTransaction(accTrans)));
            pager.setData(apiTransactionItemList);
            resultMsg.setData(pager);
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public ZKResultMsg getByFilterAndTypeAndPage(String devSn, String filter, String type, String eventNo,
                                                 Date startDate, Date endDate, Integer pageNo, Integer pageSize) {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        AccTransactionItem accTransactionItem = new AccTransactionItem();
        // 根据设备编号进行过滤
        accTransactionItem.setDevSnEqual(devSn);
        // 根据开始时间、结束时间进行过滤
        if (startDate != null && endDate != null) {
            accTransactionItem.setStartTime(startDate);
            accTransactionItem.setEndTime(endDate);
        }
        // 根据事件编号进行过滤
        if (StringUtils.isNotBlank(eventNo)) {
            accTransactionItem.setEventNo(Short.valueOf(eventNo));
        }
        // 根据人员编号或姓名、事件类型进行过滤
        String querySql = getTransactionSqlByFilter(accTransactionItem, filter, type);

        List<AccApiTransactionItem> apiTransactionItemList = Lists.newArrayList();
        Pager pager = accTransactionDao.getItemsBySql(accTransactionItem.getClass(), querySql, pageNo - 1, pageSize);
        List<AccTransactionItem> accTransactionItemList = (List<AccTransactionItem>) pager.getData();
        if (!accTransactionItemList.isEmpty()) {
            accTransactionItemList.forEach(accTrans -> apiTransactionItemList.add(buildApiTransaction(accTrans)));
            pager.setData(apiTransactionItemList);
            resultMsg.setData(pager);
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    public String getPersonAllName(String firstName, String lastName) {
        String name = "";
        if (StringUtils.isNotBlank(firstName)) {
            name = firstName.trim();
        }
        // 中文下不显示lastname，非中文且lastname不为空时，拼接姓名，解决先前lastname为null时也拼接下去的问题
        if (!accRTMonitorService.checkIsChinaLanguage() && StringUtils.isNotBlank(lastName)) {
            name = StringUtil.isNotBlank(firstName) ? (firstName + " " + lastName).trim() : lastName.trim();
        }
        return name;
    }

    /**
     * @Description:判断是否是异常记录
     * @return:
     * @Author:
     * @date: 2025/7/22 11:31
     */

    public void accExceptionRecordHandle(AccTransactionItem accTransaction) {

        // 是否是推送设备

        List<AccDeviceItem> accDeviceItemList = accDeviceService.getItemBySns(accTransaction.getDevSn());

        if (accDeviceItemList.isEmpty() || !"1".equals(accDeviceItemList.get(0).getIsPushException())) {
            log.info("sn {} 未设置推送属性 ", accTransaction.getDevSn());
            return;
        }
        PersPersonItem persPersonItem = persPersonService.getItemByPin(accTransaction.getPin());
        if (persPersonItem == null) {
            return;
        }
        BaseSysParamItem restOutTime = baseSysParamService.findByParamName("acc.restOutTime");
        BaseSysParamItem workOutTime = baseSysParamService.findByParamName("acc.workOutTime");
        BaseSysParamItem workAndRestOutTime = baseSysParamService.findByParamName("acc.workAndRestOutTime");

        int attBareakTime = 0;
        if (restOutTime != null) {
            attBareakTime = Integer.parseInt(restOutTime.getParamValue()) * 60 * 1000;
        }
        int workTime = 0;
        if (workOutTime != null) {
            workTime = Integer.parseInt(workOutTime.getParamValue()) * 60 * 1000;
        }
        int workAndAttBareakTime = 0;
        if (workAndRestOutTime != null) {
            workAndAttBareakTime = Integer.parseInt(workAndRestOutTime.getParamValue()) * 60 * 1000;
        }

        // 判断进出
        if (accTransaction.getReaderState() == 0) {
            // 进场状态
            PersPersonItem personItemZZ = findPositionPerson("组长", persPersonItem.getDeptId());
            PersPersonItem personItemKZ = findPositionPerson("课长", persPersonItem.getDeptId());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-hh");
            //是否存在未闭环已推送的记录  对于这些记录需要进行闭环处理
            List<AccExceptionRecord> unclosedAndPushRecordsList = accExceptionRecordDao
                    .findUnclosedAndPushRecordsAndIsNullAndPin(accTransaction.getPin());
            //进入闭环处理逻辑
            if (!unclosedAndPushRecordsList.isEmpty()) {
                List<AccExceptionRecordItem> accExceptionRecordList=  ModelUtil.copyListProperties(unclosedAndPushRecordsList,AccExceptionRecordItem.class);
                //前面已经推送过了，所以认定为异常记录，不用再重新判断是否异常，直接推送
                //时间是否一致
                Date uncloseData=accExceptionRecordList.get(0).getExitTime();
                for (AccExceptionRecordItem item : accExceptionRecordList) {
                    if(uncloseData.getTime()!=item.getExitTime().getTime()){
                        //不一致说明不是组长和课长
                        break;
                    }
                    item.setStatus((short) 1);// 是否需要发送
                    item.setSendTime(new Date());
                    item.setExceptionStatus("1");
                    item.setEnterTime(accTransaction.getEventTime());
                    String msg ="ok";
                    if("组长".equals(item.getReceiverPosition())){
                         msg = pushWechat("1", personItemZZ, item);
                    }else {
                         msg = pushWechat("1", personItemKZ, item);
                    }
                    if (!"ok".equals(msg)) {
                        item.setStatus((short) 2);// 否
                        log.info(msg);
                    }
                    accExceptionRecordService.saveItem(item);
                }
                return;
            }


            List<AccExceptionRecord> accExceptionRecordList = accExceptionRecordDao
                    .findUnclosedRecordsAndIsNullAndPin(accTransaction.getPin());

            if (!accExceptionRecordList.isEmpty()) {
                AccExceptionRecord accExceptionRecord = accExceptionRecordList.get(0);

                log.info(
                        "accTransaction.getPin() = {} accExceptionRecord.getExitTime() = {}  accTransaction.getEventTime() = {}",
                        accTransaction.getPin(), accExceptionRecord.getExitTime(), accTransaction.getEventTime());
                List<Acc4AttIsExceptionTimeItem> acc4AttIsExceptionTimeItems = acc4AttTransactionExternalService
                        .isExceptionRecord(accTransaction.getPin(), accExceptionRecord.getExitTime(),
                                accTransaction.getEventTime());
                log.info("acc4AttIsExceptionTimeItems {}", acc4AttIsExceptionTimeItems.toString());
                accExceptionRecord.setStatus((short) 3);// 不需要发送

                AccExceptionRecordItem accExceptionRecordItemZZ = new AccExceptionRecordItem();
                ModelUtil.copyProperties(accExceptionRecord, accExceptionRecordItemZZ);

                accExceptionRecordItemZZ.setPin(accTransaction.getPin());
                accExceptionRecordItemZZ.setDeptCode(persPersonItem.getDeptCode());
                accExceptionRecordItemZZ.setDeptName(persPersonItem.getDeptName());
                accExceptionRecordItemZZ.setReaderName(accTransaction.getReaderName());
                accExceptionRecordItemZZ.setEnterTime(accTransaction.getEventTime());
                accExceptionRecordItemZZ.setExceptionStatus("1");
                accExceptionRecordItemZZ.setSubject("1");
                accExceptionRecordItemZZ.setAreaName(accTransaction.getAreaName());
                accExceptionRecordItemZZ.setName(persPersonItem.getName());
                accExceptionRecordItemZZ.setExceptionStatus("2");

                if (!acc4AttIsExceptionTimeItems.isEmpty()) {


                    boolean flagZZ = false;// 推送组长
                    boolean flagkZ = false;// 推送课长

                    //
                    for (Acc4AttIsExceptionTimeItem acc4AttIsExceptionTimeItem : acc4AttIsExceptionTimeItems) {
                        // 休息内外出时间
                        if (acc4AttIsExceptionTimeItem.getAttBreakTimeLong() > attBareakTime) {

                            flagZZ = true;
                            flagkZ = true;
                            accExceptionRecordItemZZ.setErrorMessage("休息时间段外出超" + (attBareakTime / 60 / 1000) + "分钟");
                            break;
                        }
                        // 上班外出时间 超过时间x分钟时同时推送组长
                        if (acc4AttIsExceptionTimeItem.getWorkLong() > workTime) {
                            flagZZ = true;
                            accExceptionRecordItemZZ.setErrorMessage("上班外出时间超" + (workTime / 60 / 1000) + "分钟");
                            break;
                        }
                        //                     // 上班外出时间 超过时间x分钟时同时推送课长
                        if (acc4AttIsExceptionTimeItem.getWorkLong() > workAndAttBareakTime) {
                            flagZZ = true;
                            flagkZ = true;
                            accExceptionRecordItemZZ
                                    .setErrorMessage("上班外出时间超" + (workAndAttBareakTime / 60 / 1000) + "分钟");
                            break;
                        }
                    }
                    if (flagZZ) {
                        if (personItemZZ != null) {
                            // 推送组长
                            accExceptionRecordItemZZ.setReceiverPosition("组长");
                            accExceptionRecordItemZZ.setSendTime(new Date());
                            accExceptionRecordItemZZ.setStatus((short) 1);// 是否需要发送
                            accExceptionRecordItemZZ.setExceptionStatus("1");

                            String msg = pushWechat("1", personItemZZ, accExceptionRecordItemZZ);
                            if (!"ok".equals(msg)) {
                                accExceptionRecordItemZZ.setStatus((short) 2);// 是
                                log.info(msg);
                            }
                            accExceptionRecordItemZZ.setPushErrorMessage(msg);

                        } else {
                            log.info("persPersonItem code =  {}  没有组长", persPersonItem.getDeptCode());
                        }
                    }
                    if (flagkZ) {
                        if (personItemKZ != null) {
                            // 推送课长
                            AccExceptionRecordItem accExceptionRecordItemKZ = new AccExceptionRecordItem();
                            ModelUtil.copyProperties(accExceptionRecordItemZZ, accExceptionRecordItemKZ);
                            accExceptionRecordItemKZ.setId("");
                            accExceptionRecordItemKZ.setStatus((short) 1);// 是否需要发送
                            accExceptionRecordItemKZ.setSendTime(new Date());
                            accExceptionRecordItemKZ.setExceptionStatus("1");
                            accExceptionRecordItemKZ.setReceiverPosition("课长");
                            String msg = pushWechat("1", personItemKZ, accExceptionRecordItemKZ);
                            if (!"ok".equals(msg)) {
                                accExceptionRecordItemKZ.setStatus((short) 2);// 是
                                log.info(msg);

                            }
                            accExceptionRecordItemKZ.setPushErrorMessage(msg);

                            accExceptionRecordService.saveItem(accExceptionRecordItemKZ);
                        } else {
                            log.info("persPersonItem code =  {}  没有课长", persPersonItem.getDeptCode());
                        }
                    }

                }
                if (accExceptionRecordItemZZ.getStatus() != null && accExceptionRecordItemZZ.getStatus()==3) {
                    //删除
                    accExceptionRecordService.deleteByIds(accExceptionRecordItemZZ.getId());
                }else {
                    accExceptionRecordService.saveItem(accExceptionRecordItemZZ);

                }
            }

        } else {

            // 出场 判断是记录表中是否有该条记录 ，没有则进行存储
            AccExceptionRecordItem accExceptionRecordItem = new AccExceptionRecordItem();
            accExceptionRecordItem.setEqlPin(accTransaction.getPin());
            accExceptionRecordItem.setEqlExitTime(accTransaction.getEventTime());

            List<AccExceptionRecord> accExceptionRecordList=accExceptionRecordDao.getItemByPinAndExceptionStatus(accTransaction.getPin());
            if (accExceptionRecordList.isEmpty()) {
                accExceptionRecordItem.setPin(accTransaction.getPin());
                accExceptionRecordItem.setName(persPersonItem.getName());
                accExceptionRecordItem.setDeptCode(persPersonItem.getDeptCode());
                accExceptionRecordItem.setDeptName(persPersonItem.getDeptName());
                accExceptionRecordItem.setReaderName(accTransaction.getReaderName());
                accExceptionRecordItem.setExitTime(accTransaction.getEventTime());
                accExceptionRecordItem.setAreaName(accTransaction.getAreaName());
                accExceptionRecordItem.setSubject("1");
                accExceptionRecordItem.setAreaName(accTransaction.getAreaName());
            }else {
                log.info("accTransaction.getPin() = {} accExceptionRecord.getExitTime() = {}  accTransaction.getEventTime() = {}",
                        accTransaction.getPin(), accExceptionRecordList.get(0).getExitTime(), accTransaction.getEventTime());
                //替换第一条

                AccExceptionRecord accExceptionRecord= accExceptionRecordList.get(0);
                ModelUtil.copyProperties(accExceptionRecord, accExceptionRecordItem);

                accExceptionRecordItem.setName(persPersonItem.getName());
                accExceptionRecordItem.setDeptCode(persPersonItem.getDeptCode());
                accExceptionRecordItem.setDeptName(persPersonItem.getDeptName());
                accExceptionRecordItem.setReaderName(accTransaction.getReaderName());
                accExceptionRecordItem.setExitTime(accTransaction.getEventTime());
                accExceptionRecordItem.setAreaName(accTransaction.getAreaName());
                accExceptionRecordItem.setSubject("1");
            }
            accExceptionRecordItem.setSn(accTransaction.getDevSn());
            accExceptionRecordService.saveItem(accExceptionRecordItem);

        }
    }

    public PersPersonItem findPositionPerson(String positionName, String deptId) {

        PersPersonItem personItem = new PersPersonItem();
        personItem.setEqlPositionName(positionName);
        personItem.setEqlDeptId(deptId);

        List<PersPersonItem> persPersonItemList = persPersonService.getByCondition(personItem);

        if (persPersonItemList.isEmpty()) {
            // 查找上级部门
            AuthDepartmentItem authDepartmentItem = authDepartmentService.getItemById(deptId);

            if (authDepartmentItem != null && authDepartmentItem.getParentId() != null) {
                return findPositionPerson(positionName, authDepartmentItem.getParentId());

            } else {
                return null;
            }
        }

        return persPersonItemList.get(0);

    }

    public String pushWechat(String type, PersPersonItem personItemItem,
                             AccExceptionRecordItem accExceptionRecordItem) {

        JSONObject jsonObject = new JSONObject();
        JSONObject data = new JSONObject();

        // 构建微信模板消息格式

        // keyword1 - 工号
        JSONObject keyword1 = new JSONObject();
        keyword1.put("value", accExceptionRecordItem.getPin());
        data.put("character_string7", keyword1);

        // keyword2 - 姓名
        JSONObject keyword2 = new JSONObject();
        keyword2.put("value", personItemItem.getName());
        data.put("thing1", keyword2);

        // keyword3 - 部门
        JSONObject keyword3 = new JSONObject();
        keyword3.put("value", personItemItem.getDeptName());
        data.put("thing6", keyword3);

        // keyword4 - 读卡器名称
        JSONObject keyword4 = new JSONObject();
        keyword4.put("value", accExceptionRecordItem.getReaderName());
        data.put("thing5", keyword4);

        // keyword5 - 时间
        JSONObject keyword5 = new JSONObject();
        keyword5.put("value", accExceptionRecordItem.getEnterTime());
        data.put("thing4", keyword5);

        jsonObject.put("data", data);

        log.info("jsonObject:{}", jsonObject);

        if (StringUtils.isNotBlank(personItemItem.getWechatOpenId())) {
            wechatService.sendWechatMessage(jsonObject, personItemItem.getWechatOpenId(), type,
                    accExceptionRecordItem.getId());
            return "ok";
        } else {
            return "该人员未进行微信授权";
        }

    }

    public void checkExceptionRecord() {

        BaseSysParamItem restOutTime = baseSysParamService.findByParamName("acc.restOutTime");
        BaseSysParamItem workOutTime = baseSysParamService.findByParamName("acc.workOutTime");
        BaseSysParamItem workAndRestOutTime = baseSysParamService.findByParamName("acc.workAndRestOutTime");

        int attBareakTime = 0;
        if (restOutTime != null) {
            attBareakTime = Integer.parseInt(restOutTime.getParamValue()) * 60 * 1000;
        }
        int workTime = 0;
        if (workOutTime != null) {
            workTime = Integer.parseInt(workOutTime.getParamValue()) * 60 * 1000;
        }
        int workAndAttBareakTime = 0;
        if (workAndRestOutTime != null) {
            workAndAttBareakTime = Integer.parseInt(workAndRestOutTime.getParamValue()) * 60 * 1000;
        }
        // 查询未处理的记录
        // 因为如果是前一天进入的，那当前时间已经算是在打卡点外了，不进行计算，所以只查询后3天的记录 防止数据量太多
        Date date = getDateMinusFiveDays();

        List<AccExceptionRecord> accExceptionRecordList = accExceptionRecordDao.findUnclosedRecordsAndIsNull(date);

        //
        for (AccExceptionRecord item : accExceptionRecordList) {
            boolean flagZZ = false;// 推送组长
            boolean flagkZ = false;// 推送课长
            boolean pushflagZZ = false;// 已经推送
            boolean pushflagKZ = false;// 已经推送

            //是否已经推送 如果已经推送组长就不需要再次推送
            //是否已经推送 如果已经推送课长就不需要再次推送
            if (item.getReceiverPosition() != null && item.getReceiverPosition().equals("课长")
            ) {
                //已经推送给课长 说明已经推送完成了，等待人员进入才能完成闭环
                log.info("已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环");

                continue;
            } else {
                //查询数据库有没有
                Long number = accExceptionRecordDao.countPushRecordsByReceiverPosition(item.getPin(), "课长", item.getExitTime().getTime());
                if (number > 0) {
                    //已经推送给课长 说明已经推送完成了，等待人员进入才能完成闭环
                    log.info("已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环");
                    continue;
                }
            }

            if (item.getReceiverPosition() != null && item.getReceiverPosition().equals("组长")
            ) {
                pushflagZZ = true;
            }

            PersPersonItem persPersonItem = persPersonService.getItemByPin(item.getPin());

            // 是否是推送设备

            List<AccDeviceItem> accDeviceItemList = accDeviceService.getItemBySns(item.getSn());

            if (accDeviceItemList.isEmpty() || !"1".equals(accDeviceItemList.get(0).getIsPushException())) {
                log.info("sn {} 未设置推送属性 ", item.getSn());
                return;
            }

            List<Acc4AttIsExceptionTimeItem> acc4AttIsExceptionTimeItems = acc4AttTransactionExternalService
                    .isExceptionRecord(item.getPin(), item.getExitTime(), null);// null为使用当前时间
            if (!acc4AttIsExceptionTimeItems.isEmpty()) {
                PersPersonItem personItemZZ = findPositionPerson("组长", persPersonItem.getDeptId());
                PersPersonItem personItemKZ = findPositionPerson("课长", persPersonItem.getDeptId());


                //
                String errorMessage = "";
                for (Acc4AttIsExceptionTimeItem acc4AttIsExceptionTimeItem : acc4AttIsExceptionTimeItems) {
                    // 休息时间段：超过时间x分钟时同时推送至组长和课长
                    if (acc4AttIsExceptionTimeItem.getAttBreakTimeLong() > attBareakTime) {

                        flagZZ = true;
                        flagkZ = true;
                        errorMessage = ("休息时间段外出超" + (attBareakTime / 60 / 1000) + "分钟");

                        break;
                    }
                    // 上班外出时间 超过时间x分钟时同时推送至组长
                    if (acc4AttIsExceptionTimeItem.getWorkLong() > workTime) {
                        flagZZ = true;
                        errorMessage = "上班外出时间超" + (workTime / 60 / 1000) + "分钟";
                    }
                    // 上班外出时间 超过时间x分钟时同时推送课长
                    if (acc4AttIsExceptionTimeItem.getWorkLong() > workAndAttBareakTime) {
                        flagkZ = true;
                        flagZZ = true;
                        errorMessage = "上班外出时间超" + (workAndAttBareakTime / 60 / 1000) + "分钟";

                        break;
                    }
                }
                if (!pushflagZZ && flagZZ) {
                    if (personItemZZ != null) {
                        // 推送组长
                        AccExceptionRecordItem accExceptionRecordItemZZ = new AccExceptionRecordItem();

                        ModelUtil.copyProperties(item, accExceptionRecordItemZZ);
                        accExceptionRecordItemZZ.setReceiverPosition("组长");
                        accExceptionRecordItemZZ.setSendTime(new Date());
                        accExceptionRecordItemZZ.setStatus((short) 1);// 是否需要发送
                        accExceptionRecordItemZZ.setPin(item.getPin());
                        accExceptionRecordItemZZ.setDeptCode(persPersonItem.getDeptCode());
                        accExceptionRecordItemZZ.setDeptName(persPersonItem.getDeptName());
                        accExceptionRecordItemZZ.setReaderName(item.getReaderName());
//                        accExceptionRecordItemZZ.setEnterTime(new Date());
                        accExceptionRecordItemZZ.setSubject("1");
                        accExceptionRecordItemZZ.setName(persPersonItem.getName());
                        accExceptionRecordItemZZ.setExceptionStatus("2");
                        accExceptionRecordItemZZ.setErrorMessage(errorMessage);
                       String msg= pushWechat("1", personItemZZ, accExceptionRecordItemZZ);
                        if (!"ok".equals(msg)) {
                            accExceptionRecordItemZZ.setStatus((short) 2);// 是
                            log.info(msg);

                        }
                        accExceptionRecordItemZZ.setPushErrorMessage(msg);
                        accExceptionRecordService.saveItem(accExceptionRecordItemZZ);
                    } else {
                        log.info("persPersonItem code =  {}  没有组长", persPersonItem.getDeptCode());
                    }
                }

                if (!pushflagKZ && flagkZ) {
                    if (personItemKZ != null) {
                        // 推送课长

                        AccExceptionRecordItem accExceptionRecordItemKZ = new AccExceptionRecordItem();
                        accExceptionRecordItemKZ.setStatus((short) 1);// 是否需要发送
                        accExceptionRecordItemKZ.setReceiverPosition("课长");
                        accExceptionRecordItemKZ.setSendTime(new Date());
                        accExceptionRecordItemKZ.setPin(item.getPin());
                        accExceptionRecordItemKZ.setDeptCode(persPersonItem.getDeptCode());
                        accExceptionRecordItemKZ.setDeptName(persPersonItem.getDeptName());
                        accExceptionRecordItemKZ.setReaderName(item.getReaderName());
                        accExceptionRecordItemKZ.setExitTime(item.getExitTime());
//                        accExceptionRecordItemKZ.setEnterTime(new Date());
                        accExceptionRecordItemKZ.setSubject("1");
                        accExceptionRecordItemKZ.setExceptionStatus("2");
                        accExceptionRecordItemKZ.setAreaName(item.getAreaName());
                        accExceptionRecordItemKZ.setName(persPersonItem.getName());
                        accExceptionRecordItemKZ.setErrorMessage(errorMessage);
                        String msg=pushWechat("1", personItemKZ, accExceptionRecordItemKZ);
                        if (!"ok".equals(msg)) {
                            accExceptionRecordItemKZ.setStatus((short) 2);// 是
                            log.info(msg);

                        }
                        accExceptionRecordItemKZ.setPushErrorMessage(msg);

                        accExceptionRecordService.saveItem(accExceptionRecordItemKZ);
                    } else {
                        log.info("persPersonItem code =  {}  没有课长", persPersonItem.getDeptCode());
                    }

                }
            }
        }

    }

    public static Date getDateMinusFiveDays() {
        LocalDateTime dateTime = LocalDateTime.now().minusDays(3);
        return Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public String pushWechat(String type, String pin,
                             AccExceptionRecordItem accExceptionRecordItem){
        PersPersonItem personItem= persPersonService.getItemByPin(pin);
        PersPersonItem personItemZZ = findPositionPerson("组长", personItem.getDeptId());
//        PersPersonItem personItemKZ = findPositionPerson("课长", personItem.getDeptId());
        return pushWechat(type, personItemZZ, accExceptionRecordItem);

    }
}
