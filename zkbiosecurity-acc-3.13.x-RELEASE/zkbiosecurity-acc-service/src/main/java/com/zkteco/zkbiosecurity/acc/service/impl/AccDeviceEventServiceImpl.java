/**
 * File Name: AccDeviceEventServiceImpl Created by GenerationTools on 2018-03-14 下午02:44 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceEventDao;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.model.AccDeviceEvent;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceEventService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceEventItem;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.license.provider.BaseLicenseProvider;
import com.zkteco.zkbiosecurity.system.service.BaseMediaFileService;
import com.zkteco.zkbiosecurity.system.vo.BaseMediaFileItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 对应百傲瑞达 AccDeviceEventServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-14 下午02:44
 * @version v1.0
 */
@Service
public class AccDeviceEventServiceImpl implements AccDeviceEventService {
    @Autowired
    private AccDeviceEventDao accDeviceEventDao;
    @Autowired
    private BaseMediaFileService baseMediaFileService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private BaseLicenseProvider baseLicenseProvider;

    @Override
    @Transactional
    public AccDeviceEventItem saveItem(AccDeviceEventItem item, String fileOpType, String syncAllDevice) {
        AccDeviceEvent accDeviceEvent = accDeviceEventDao.getOne(item.getId());

        BaseMediaFileItem baseMediaFileItem = null;
        if ("select".equals(fileOpType)) {// 通过下拉框选择的音频文件
            if (StringUtils.isNotBlank(item.getBaseMediaFileId())) {
                baseMediaFileItem = baseMediaFileService.getItemById(item.getBaseMediaFileId());
                if (Objects.nonNull(baseMediaFileItem)) {
                    accDeviceEvent.setBaseMediaFileId(baseMediaFileItem.getId());
                } else {
                    accDeviceEvent.setBaseMediaFileId(null);
                }
            } else {
                accDeviceEvent.setBaseMediaFileId(null);
            }
        } else if ("upload".equals(fileOpType)) {// 通过上传的音频文件
            if (StringUtils.isNotBlank(item.getBaseMediaFileName())
                && StringUtils.isNotBlank(item.getBaseMediaFilePath())) {
                baseMediaFileItem = new BaseMediaFileItem();
                String mediaFileName =
                    baseMediaFileService.checkMediaFileName(item.getBaseMediaFileName(), item.getBaseMediaFilePath());
                baseMediaFileItem.setName(mediaFileName);
                baseMediaFileItem.setPath(item.getBaseMediaFilePath());
                baseMediaFileItem.setFileSize(item.getBaseMediaFileSize());
                baseMediaFileItem.setSuffix(item.getBaseMediaFileSuffix());
                baseMediaFileItem = baseMediaFileService.saveItem(baseMediaFileItem);
                accDeviceEvent.setBaseMediaFileId(baseMediaFileItem.getId());
            } else {
                accDeviceEvent.setBaseMediaFileId(null);
            }
        }
        accDeviceEvent.setEventLevel(item.getEventLevel());
        if (item.getEventLevel() == AccConstants.EVENT_ALARM) {
            accDeviceEvent.setEventPriority(Optional.ofNullable(item.getEventPriority()).orElse(Short.valueOf("0")));
        } else {
            accDeviceEvent.setEventPriority(null);
        }
        accDeviceEventDao.save(accDeviceEvent);
        // 是否把音频同步到所有设备的相同事件类型
        if ((AccConstants.ENABLE + "").equals(syncAllDevice)) {
            accDeviceEventDao.syncAllDeviceEvent(accDeviceEvent.getBaseMediaFileId(), accDeviceEvent.getEventNo());
            // 同步报警事件的优先级
            if (accDeviceEvent.getEventPriority() != null) {
                accDeviceEventDao.syncAllDeviceAlarmEvent(accDeviceEvent.getEventPriority(),
                    accDeviceEvent.getEventNo());
                syncAllDeviceAlarmEventToCache(accDeviceEvent.getAccDevice().getSn());
            }
        }
        if (item.getEventLevel() == AccConstants.EVENT_ALARM) {
            List<AccDeviceEvent> eventList =
                accDeviceEventDao.findByAccDevice_Sn(accDeviceEvent.getAccDevice().getSn());
            List<AccDeviceEventItem> accDeviceEventItemList =
                ModelUtil.copyListProperties(eventList, AccDeviceEventItem.class);
            accCacheManager.putDeviceEvent2Cache(accDeviceEvent.getAccDevice().getSn(), accDeviceEventItemList);
        }
        item.setId(accDeviceEvent.getId());
        return item;
    }

    /**
     * 同步事件缓存
     * 
     * @param devSn:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2023-09-22 16:09
     * @since 1.0.0
     */
    private void syncAllDeviceAlarmEventToCache(String devSn) {
        List<AccDeviceEvent> eventList = accDeviceEventDao.findByAccDeviceSnNotEq(devSn);
        Map<String, List<AccDeviceEvent>> deviceEventMap = eventList.stream()
            .collect(Collectors.groupingBy(deviceEvent -> deviceEvent.getAccDevice().getSn(), Collectors.toList()));
        for (Map.Entry entry : deviceEventMap.entrySet()) {
            List<AccDeviceEvent> accDeviceEventList = (List<AccDeviceEvent>)entry.getValue();
            List<AccDeviceEventItem> accDeviceEventItemList =
                ModelUtil.copyListProperties(eventList, AccDeviceEventItem.class);
            accCacheManager.putDeviceEvent2Cache(entry.getKey() + "", accDeviceEventItemList);
        }
    }

    @Override
    public List<AccDeviceEventItem> getByCondition(AccDeviceEventItem condition) {
        return (List<AccDeviceEventItem>)accDeviceEventDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accDeviceEventDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    @Transactional
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accDeviceEventDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AccDeviceEventItem getItemById(String id) {
        List<AccDeviceEventItem> items = getByCondition(new AccDeviceEventItem(id));
        AccDeviceEventItem item =
            Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
        if (Objects.nonNull(item)) {
            // 由于存储了国际化key所以显示需要国际化翻译
            item.setName(I18nUtil.i18nCode(item.getName()));
            if (StringUtils.isNotBlank(item.getBaseMediaFileId())) {
                BaseMediaFileItem baseMediaFileItem = baseMediaFileService.getItemById(item.getBaseMediaFileId());
                if (Objects.nonNull(baseMediaFileItem)) {
                    item.setBaseMediaFileSuffix(baseMediaFileItem.getSuffix());
                    item.setBaseMediaFileSize(baseMediaFileItem.getFileSize());
                    item.setBaseMediaFilePath(baseMediaFileItem.getPath());
                    item.setBaseMediaFileName(baseMediaFileItem.getName());
                }
            }
            if (Objects.isNull(item.getEventPriority())) {
                item.setEventPriority(AccConstants.DEV_ALARMEVENTPRIORITY_LOW);
            }
        }
        return item;
    }

    @Override
    @Transactional
    public void setSound(String ids, String fileOpType, String baseMediaFileId, String baseMediaFileName,
        String baseMediaFilePath, String baseMediaFileSize, String baseMediaFileSuffix) {
        BaseMediaFileItem baseMediaFileItem = null;
        if ("select".equals(fileOpType) && StringUtils.isNotBlank(baseMediaFileId))// 通过下拉框选择的音频文件
        {
            baseMediaFileItem = baseMediaFileService.getItemById(baseMediaFileId);
        } else if ("upload".equals(fileOpType) && StringUtils.isNotBlank(baseMediaFileName))// 通过上传的音频文件
        {
            baseMediaFileItem = new BaseMediaFileItem();
            String mediaFileName = baseMediaFileService.checkMediaFileName(baseMediaFileName, baseMediaFilePath);
            baseMediaFileItem.setName(mediaFileName);
            baseMediaFileItem.setPath(baseMediaFilePath);
            baseMediaFileItem.setFileSize(baseMediaFileSize);
            baseMediaFileItem.setSuffix(baseMediaFileSuffix);

            baseMediaFileItem = baseMediaFileService.saveItem(baseMediaFileItem);
        }

        accDeviceEventDao.syncSoundToEvent(Objects.nonNull(baseMediaFileItem) ? baseMediaFileItem.getId() : null,
            (List<String>)CollectionUtil.strToList(ids));
    }

    @Override
    public boolean isAuxInEvent(List<Integer> triggerNoList) {
        boolean auxInEvent = false;
        for (int triggerNo : triggerNoList) {
            if (AccConstants.AUXIN_EVENT.contains(triggerNo)) {
                auxInEvent = true;
                break;
            }
        }
        return auxInEvent;
    }

    @Override
    public short getDefaultEventTypeById(int eventNo) {
        short level = AccConstants.EVENT_NORMAL;
        if (eventNo >= 100 && eventNo < 200) {
            level = AccConstants.EVENT_ALARM;
        } else if (eventNo >= 20 && eventNo < 100) {
            level = AccConstants.EVENT_WARNING;
        } else if (eventNo >= 500 && eventNo < 600) {
            // 软件自定义的异常事件
            level = AccConstants.EVENT_WARNING;
        } else if (eventNo >= 700 && eventNo < 800) {
            // 软件自定义的报警事件
            level = AccConstants.EVENT_ALARM;
        } else if (eventNo >= 5000 && eventNo < 6000) {
            level = AccConstants.EVENT_WARNING;
        } else if (eventNo >= 6000 && eventNo < 7000) {
            level = AccConstants.EVENT_ALARM;
        }
        return level;
    }

    @Override
    public AccDeviceEventItem getItemByDeviceIdAndEventNo(String devId, Short eventNo) {
        AccDeviceEvent accDeviceEvent = accDeviceEventDao.findByAccDevice_IdAndEventNo(devId, eventNo);
        AccDeviceEventItem accDeviceEventItem = null;
        if (accDeviceEvent != null) {
            accDeviceEventItem = new AccDeviceEventItem();
            ModelUtil.copyProperties(accDeviceEvent, accDeviceEventItem);
        }
        return accDeviceEventItem;
    }

    @Override
    public Pager loadPagerByAuthFilter(String sessionId, AccDeviceEventItem condition, int pageNo, int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        return getItemsByPage(condition, pageNo, pageSize);
    }

    @Override
    public List<AccDeviceEventItem> getItemsByNameAndDoorId(String alarmName, String doorId) {
        List<AccDeviceEvent> accDeviceEventList = accDeviceEventDao.getByNameAndDoorId(alarmName, doorId);
        if (Objects.nonNull(accDeviceEventList) && accDeviceEventList.size() > 0) {
            List<AccDeviceEventItem> accDeviceEventItemList = Lists.newArrayList();
            accDeviceEventList.stream().forEach(devEvent -> {
                AccDeviceEventItem accDeviceEventItem = new AccDeviceEventItem();
                ModelUtil.copyProperties(devEvent, accDeviceEventItem);
                if (StringUtils.isNotBlank(accDeviceEventItem.getBaseMediaFileId())) {
                    BaseMediaFileItem baseMediaFileItem =
                        baseMediaFileService.getItemById(accDeviceEventItem.getBaseMediaFileId());
                    accDeviceEventItem.setBaseMediaFileName(baseMediaFileItem.getName());
                    accDeviceEventItem.setBaseMediaFilePath(baseMediaFileItem.getPath());
                    accDeviceEventItem.setBaseMediaFileSize(baseMediaFileItem.getFileSize());
                    accDeviceEventItem.setBaseMediaFileSuffix(baseMediaFileItem.getSuffix());
                }
                accDeviceEventItemList.add(accDeviceEventItem);
            });
            return accDeviceEventItemList;
        }
        return null;
    }

    @Override
    public List<AccDeviceEventItem> getGlobalLinkTriggerEvent() {
        List<Short> filterEventList = new ArrayList<>();
        for (Integer eventNo : AccConstants.GLOBAL_LINKAGE_TRIGGER_FILTER) {
            filterEventList.add(eventNo.shortValue());
        }
        List<AccDeviceEvent> eventList =
            accDeviceEventDao.getGlobalLinkTriggerEvent(ConstUtil.COMM_HTTP, filterEventList);
        return ModelUtil.copyListProperties(eventList, AccDeviceEventItem.class);
    }

    @Override
    @Transactional
    public void handlerTransfer(List<AccDeviceEventItem> eventItems) {
        // 按设备sn分组， key：pin value:
        // Map<String, List<AccDeviceEventItem>> deviceOpMap =
        // eventItems.stream().collect(Collectors.groupingBy(AccDeviceEventItem::getDevSn));
        // 获取数据库中原有的设备参数取出，用于比较
        // List<List<String>> snsList = CollectionUtil.split(deviceOpMap.keySet(), CollectionUtil.splitSize);
        Map<String, AccDeviceEvent> optionAllMap = new HashMap<String, AccDeviceEvent>();
        Map<String, AccDevice> deviceAllMap = new HashMap<String, AccDevice>();
        // 获取所有设备的集合 add by hql 20190808
        List<AccDevice> devices = accDeviceDao.findAll();
        for (AccDevice accDevice : devices) {
            deviceAllMap.put(accDevice.getSn(), accDevice);
        }
        // 获取所有门禁设备事件
        List<AccDeviceEvent> events = accDeviceEventDao.findAll();
        for (AccDeviceEvent event : events) {
            String key = event.getAccDevice().getSn() + "_" + event.getEventNo();
            optionAllMap.put(key, event);
        }
        // 数据大时分批处理
        // for (List<String> sns : snsList) {
        // List<AccDeviceEvent> events = accDeviceEventDao.findByAccDevice_SnIn(sns);
        // for (AccDeviceEvent event : events) {
        // String key = event.getAccDevice().getSn() + "_" + event.getEventNo();
        // optionAllMap.put(key, event);
        // }
        // 將所有的設備查詢出來
        // List<AccDevice> devices = accDeviceDao.findBySnIn(sns);
        // for (AccDevice accDevice : devices)
        // {
        // deviceAllMap.put(accDevice.getSn(), accDevice);
        // }
        // }
        // 检测判断数据后保存更新
        for (AccDeviceEventItem eventItem : eventItems) {
            AccDeviceEvent event = optionAllMap.remove(eventItem.getDevSn() + "_" + eventItem.getEventNo());
            if (Objects.isNull(event)) {
                event = new AccDeviceEvent();
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(eventItem, event, "id");
            event.setAccDevice(deviceAllMap.get(eventItem.getDevSn()));
            accDeviceEventDao.save(event);
        }
        optionAllMap = null;
        deviceAllMap = null;
    }

    @Override
    public List<AccDeviceEventItem> getAllEventNameDistinct() {
        List<AccDeviceEvent> events = accDeviceEventDao.getAllEventNameDistinct();
        return ModelUtil.copyListProperties(events, AccDeviceEventItem.class);
    }

    @Override
    public Set<String> getAllEventNameSet() {
        Set<String> eventNames = new LinkedHashSet<>();
        eventNames.addAll(new LinkedHashSet<>(AccConstants.SIMPLE_EVENT.values()));
        eventNames.addAll(new LinkedHashSet<>(AccConstants.OLD_EVENT.values()));
        eventNames.addAll(new LinkedHashSet<>(AccConstants.NEW_EVENT.values()));
        eventNames.addAll(new LinkedHashSet<>(AccConstants.ACC_ADVANCE_VALID.values()));
        eventNames.addAll(new LinkedHashSet<>(AccConstants.ACC_MONITOR_DEV_EVENT.values()));
        eventNames.addAll(new LinkedHashSet<>(AccConstants.NEW_EVENT.values()));
        return eventNames;
    }

    @Override
    public boolean isAuxinEventByNo(Integer eventNo) {
        return AccConstants.AUXIN_EVENT.contains(eventNo);
    }

    @Override
    public boolean isDeviceEventByNo(Integer eventNo) {
        return AccConstants.DEVICE_EVENT.contains(eventNo);
    }

    @Override
    public boolean isAuxOutEventByNo(Integer eventNo) {
        return AccConstants.AUXOUT_EVENT.contains(eventNo);
    }

    @Override
    public boolean isExtBoardEventByNo(Integer eventNo) {
        return AccConstants.EXTBOARD_EVENT.contains(eventNo);
    }

    @Override
    public boolean isReaderEventByNo(Integer eventNo) {
        return AccConstants.READER_EVENT.contains(eventNo);
    }

    @Override
    public boolean isLinkTriggerFilterEventByNo(Integer eventNo) {
        return AccConstants.LINKAGE_TRIGGER_FILTER.contains(eventNo);
    }

    @Override
    public boolean isLinkageEventByNo(short eventNo) {
        return AccConstants.EVENT_LINKCONTROL == eventNo;
    }

    @Override
    public AccDeviceEventItem saveSimpleItem(AccDeviceEventItem item) {
        AccDeviceEvent accDeviceEvent = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accDeviceEventDao.findById(id)).orElse(new AccDeviceEvent());
        ModelUtil.copyPropertiesIgnoreNull(item, accDeviceEvent);
        if (StringUtils.isNotBlank(item.getDevId())) {
            AccDevice accDevice = accDeviceDao.findById(item.getDevId()).orElse(null);
            if (Objects.nonNull(accDevice)) {
                accDeviceEvent.setAccDevice(accDevice);
            }
        }
        // 保存入库
        accDeviceEventDao.save(accDeviceEvent);

        item.setId(accDeviceEvent.getId());
        return item;
    }

    @Override
    public void syncAllDeviceEvent(String baseMediaFileId, Short eventNo) {
        if (StringUtils.isNotBlank(baseMediaFileId) && Objects.nonNull(eventNo)) {
            accDeviceEventDao.syncAllDeviceEvent(baseMediaFileId, eventNo);
        }
    }

    @Override
    public void saveAccDeviceEventList(List<AccDeviceEventItem> eventItemList) {
        // 组装设备map信息
        List<String> devIdList =
            (List<String>)CollectionUtil.getPropertyList(eventItemList, AccDeviceEventItem::getDevId, "-1");
        List<AccDevice> accDeviceList = accDeviceDao.findByIdList(devIdList);
        Map<String, AccDevice> deviceMap = CollectionUtil.listToKeyMap(accDeviceList, AccDevice::getId);

        List<AccDeviceEvent> accDeviceEventList = new ArrayList<>();
        for (AccDeviceEventItem item : eventItemList) {
            AccDeviceEvent deviceEvent;
            if (StringUtils.isNotBlank(item.getId())) {
                deviceEvent = accDeviceEventDao.findById(item.getId()).orElse(new AccDeviceEvent());
            } else {
                deviceEvent = new AccDeviceEvent();
            }
            AccDeviceEvent event = ModelUtil.copyProperties(item, deviceEvent);
            AccDevice accDevice = deviceMap.get(item.getDevId());
            if (Objects.nonNull(accDevice)) {
                event.setAccDevice(accDevice);
            }
            accDeviceEventList.add(event);
        }
        if (!accDeviceEventList.isEmpty()) {
            accDeviceEventDao.save(accDeviceEventList);
        }
    }

    @Override
    public List<AccDeviceEventItem> getGlobalLinkTriggerEvent(short commType, List<Short> filterEventList) {
        List<AccDeviceEvent> eventList = accDeviceEventDao.getGlobalLinkTriggerEvent(commType, filterEventList);
        return ModelUtil.copyListProperties(eventList, AccDeviceEventItem.class);
    }

    @Override
    public boolean isPersTriggerFilter(Integer eventNo) {
        // 判断全局联动触发条件中是否含有与人相关的条件
        if (AccConstants.GLOBAL_LINKAGE_TRIGGER_PER_FILTER.contains(eventNo)) {
            return true;
        }
        return false;
    }

    @Override
    public Short getEventPriorityByDevIdAndEventNo(String devId, Short eventNo) {
        AccDeviceEventItem event = getItemByDeviceIdAndEventNo(devId, eventNo);
        if (event != null) {
            if (event.getEventLevel() == AccConstants.EVENT_ALARM) {
                Short priority = event.getEventPriority();
                if (priority == null) {
                    event.setEventPriority((short)0);
                    event = saveSimpleItem(event);
                    return event.getEventPriority();
                }
                return priority;
            }
        }
        return null;
    }

    @Override
    public Short getEventPriorityByDevSnAndEventNo(String devSn, Short eventNo) {
        AccDevice accDevice = accDeviceDao.findBySn(devSn);
        return getEventPriorityByDevIdAndEventNo(accDevice.getId(), eventNo);
    }
}