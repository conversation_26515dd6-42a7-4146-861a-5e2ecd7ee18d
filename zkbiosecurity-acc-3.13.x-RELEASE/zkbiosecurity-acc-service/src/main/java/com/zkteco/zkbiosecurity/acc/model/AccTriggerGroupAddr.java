package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;
import java.util.Objects;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2024/3/7 17:27
 * @since 1.0.0
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@Table(name = "ACC_TRIGGERGROUP_ADDR")
public class AccTriggerGroupAddr extends BaseModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 属于那个触发点组 */
    @ManyToOne
    @JoinColumn(name = "GROUP_ID", nullable = false)
    private AccTriggerGroup accTriggerGroup;

    /** 触发点id */
    @Column(name = "ADDRESS", length = 50)
    private String address;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        AccTriggerGroupAddr that = (AccTriggerGroupAddr)o;
        // accTriggerGroup对比id,防止循环调用
        return Objects.equals(accTriggerGroup.getId(), that.accTriggerGroup.getId())
            && Objects.equals(address, that.address);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), accTriggerGroup.getId(), address);
    }
}
