package com.zkteco.zkbiosecurity.acc.operate;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccReaderDao;
import com.zkteco.zkbiosecurity.acc.model.AccReader;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class AccLinkageVidOperate {

    @Autowired
    private AccReaderDao accReaderDao;


    /**
     * 通过事件获取联动的出发点，组装成视频联动方法所需的参数
     *
     * <AUTHOR>
     * @since 2018年5月4日 下午2:41:41
     * @param accTransaction 门禁事件
     * @return Map<String, String>
     */
    public Map<String, String> getVidLinkageParam(AccTransactionItem accTransaction)
    {
        Map<String, String> paramMap = new HashMap<String, String>();
        String entityIds = "";
        String entityClassName = "";
        short eventPointType = accTransaction.getEventPointType();
        if (eventPointType == AccConstants.EVENT_POINT_TYPE_DOOR)// 事件是门事件
        {
            entityClassName = "AccReader";// 若事件是门事件则将读头id传入
            String doorId = accTransaction.getEventPointId();
            List<AccReader> readerList = Lists.newArrayList();
            if (!(accTransaction.getReaderState() == null || accTransaction.getReaderState().equals(AccConstants.STATE_NO)))
            {
                short state = accTransaction.getReaderState();
                AccReader accReader = accReaderDao.findByReaderStateAndAccDoor_Id(state,doorId);
                readerList.add(accReader);
            }
            else
            {
                readerList.addAll(accReaderDao.findByAccDoor_Id(doorId));
            }
            for (AccReader reader : readerList)
            {
                entityIds += reader.getId().toString() + ",";
            }
            entityIds = entityIds.length() > 0 ? entityIds.substring(0, entityIds.length() - 1) : entityIds;
        }
        else if (eventPointType == AccConstants.EVENT_POINT_TYPE_AUX_IN)// 事件是辅助输入事件
        {
            entityClassName = "AccAuxIn";
            entityIds = accTransaction.getEventPointId().toString();
        }
        paramMap.put("entityIds", entityIds);
        paramMap.put("entityClassName", entityClassName);
        return paramMap;
    }
}
