package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.dao.AccLinkageIasDao;
import com.zkteco.zkbiosecurity.acc.model.AccLinkageIas;
import com.zkteco.zkbiosecurity.ias.service.Ias4OtherNotifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/7 10:12
 * @since 1.0.0
 */
@Component
public class Ias4AccServiceImpl implements Ias4OtherNotifyService {

    @Autowired
    private AccLinkageIasDao accLinkageIasDao;

    @Override
    public void deletePartitionNotify(List<String> partitionIdList) {
        if (!partitionIdList.isEmpty()) {
            AccLinkageIas linkageIas = new AccLinkageIas();
            for (String partitionId : partitionIdList) {
                linkageIas.setPartitionId(partitionId);
                accLinkageIasDao.deleteByExample(linkageIas);
            }
        }
    }
}
