package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.Acc4PersWiegandFmtService;
import com.zkteco.zkbiosecurity.acc.service.AccWiegandFmtService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
 * @date $date$ $time$
 * $params$
 * @return $returns$
 */
@Service
@Transactional
public class AccWiegandFmtServiceImpl implements AccWiegandFmtService {
    @Autowired
    private Acc4PersWiegandFmtService acc4PersWiegandFmtService;
    @Override
    public String start(String deviceId) {
        return acc4PersWiegandFmtService.start(deviceId);
    }

    @Override
    public String readerCard(String deviceId) {
        return acc4PersWiegandFmtService.readerCard(deviceId);
    }

    @Override
    public String recommendFmt(String deviceId, String sizeCode, String cardNo, String orgCardNo, String bitscount, String withSizeCode) {
        return acc4PersWiegandFmtService.recommendFmt(deviceId,sizeCode,cardNo,orgCardNo,bitscount,withSizeCode);
    }

    @Override
    public String getAllFilterId() {
        return acc4PersWiegandFmtService.getAllFilterId();
    }

    @Override
    public String stop(String deviceId) {
        return acc4PersWiegandFmtService.stop(deviceId);
    }
}
