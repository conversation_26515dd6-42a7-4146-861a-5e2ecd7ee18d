package com.zkteco.zkbiosecurity.acc.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccAlarmMonitorBEventNumDao;
import com.zkteco.zkbiosecurity.acc.dao.AccAlarmMonitorDao;
import com.zkteco.zkbiosecurity.acc.dao.AccAlarmMonitorHistoryDao;
import com.zkteco.zkbiosecurity.acc.model.AccAlarmMonitor;
import com.zkteco.zkbiosecurity.acc.model.AccAlarmMonitorBEventNum;
import com.zkteco.zkbiosecurity.acc.model.AccAlarmMonitorHistory;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.AccAlarmMonitorHistoryItem;
import com.zkteco.zkbiosecurity.acc.vo.AccAlarmMonitorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.pers.service.PersParamsService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.security.SecurityService;
import com.zkteco.zkbiosecurity.system.service.BaseSendMailService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AccAlarmMonitorServiceImpl implements AccAlarmMonitorService {
    @Autowired
    private AccTransactionService accTransactionService;
    @Autowired
    private AccAlarmMonitorDao accAlarmMonitorDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private SimpMessagingTemplate messagingTemplate;
    @Autowired
    private AccCacheService accCacheService;
    @Autowired
    private AccAlarmMonitorCacheListService accAlarmMonitorCacheListService;
    @Autowired
    private AccAlarmMonitorHistoryDao accAlarmMonitorHistoryDao;
    @Autowired
    private AccDeviceEventService accDeviceEventService;
    @Autowired
    private AccAlarmMonitorBEventNumDao accAlarmMonitorBEventNumDao;
    @Autowired
    private AccParamService accParamService;
    @Autowired
    private BaseSendMailService baseSendMailService;
    @Autowired
    private PersParamsService persParamsService;
    @Autowired
    private SecurityService securityService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Value("${system.skin:default}")
    private String systemSkin;

    /**
     * 是否消息推送是否采用 发布/订阅 模式
     */
    @Value("${acc.message.publish:false}")
    private boolean ACC_MESSAGE_PUBLISH;

    // 实时监控状态
    private static final short UNHANDLED_STATUS = 0;
    private static final short IN_PROGRESS_STATUS = 1;
    private static final short ACKNOWLEDGED_STATUS = 2;
    // 优先级
    private static final short PRIORITY_LOW = 0;
    private static final short PRIORITY_MIDDLE = 1;
    private static final short PRIORITY_HIGH = 2;
    private static final short PRIORITY_CRITICAL = 3;

    @Override
    public void sendAlarmMail(String subject, AccTransactionItem accTransactionItem, String type) {
        // 组装要发送的内容
        String content = forMateSendInfo(accTransactionItem, type);
        accTransactionService.sendAlarmMail(I18nUtil.i18nCode(subject), content);
    }

    /**
     * 组装邮件发送的内容
     *
     * @param accTransactionItem
     * @param type
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2019-11-22 11:56
     */
    private String forMateSendInfo(AccTransactionItem accTransactionItem, String type) {
        StringBuilder content = new StringBuilder();
        SimpleDateFormat longDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 取消报警
        if ("cancelAlarm".equals(type)) {
            content.append(
                I18nUtil.i18nCode("acc_rtMonitor_ackAlarmTime") + ": " + longDateFormat.format(new Date()) + "<br/>");
            content.append(I18nUtil.i18nCode("acc_rtMonitor_alarmEvent") + ": " + "<br/>");
        }
        content.append(I18nUtil.i18nCode("common_time") + ": "
            + longDateFormat.format(accTransactionItem.getEventTime()) + "<br/>");
        content.append(I18nUtil.i18nCode("common_dev_name") + ": " + accTransactionItem.getDevAlias() + "<br/>");
        content
            .append(I18nUtil.i18nCode("common_eventPoint") + ": " + accTransactionItem.getEventPointName() + "<br/>");
        content.append(I18nUtil.i18nCode("common_eventDescription") + ": "
            + I18nUtil.i18nCode(accTransactionItem.getEventName()) + "<br/>");
        if (StringUtils.isNotBlank(accTransactionItem.getPin())) {
            String name = "";
            if (StringUtils.isNotBlank(accTransactionItem.getName())) {
                name = accTransactionItem.getName().trim();
                // 中文下不显示lastname，非中文且lastname不为空时，拼接姓名，解决先前lastname为null时也拼接下去的问题
                if (!"zh_CN".equals(LocaleMessageSourceUtil.language)
                    && StringUtils.isNotBlank(accTransactionItem.getLastName())) {
                    name = (accTransactionItem.getName() + " " + accTransactionItem.getLastName()).trim();
                }
                name = "(" + name + ")";
            }
            content.append(I18nUtil.i18nCode("pers_person") + ": " + accTransactionItem.getPin() + name + "<br/>");
        }
        if (StringUtils.isNotBlank(accTransactionItem.getCardNo())) {
            content.append(I18nUtil.i18nCode("pers_card_cardNo") + ": " + accTransactionItem.getCardNo() + "<br/>");
        }
        return content.toString();
    }

    @Override
    public List<AccAlarmMonitorItem> getAll() {
        // 从后向前取100条
        return accAlarmMonitorCacheListService.getMonitorList();
    }

    private long countAll() {
        return accAlarmMonitorDao.countByStatus((short)0);
    }

    private PersPersonItem getPersItemByPin(String pin) {
        return Optional.ofNullable(pin).filter(StringUtils::isNotBlank).map(persPersonService::getItemByPin)
            .orElse(null);
    }

    @Override
    public AccAlarmMonitorItem saveItem(AccAlarmMonitorItem accAlarmMonitorItem) {
        AccAlarmMonitor alarmMonitor =
            Optional.ofNullable(accAlarmMonitorItem).filter(Objects::nonNull).map(v -> v.getId())
                .filter(StringUtils::isNotBlank).map(accAlarmMonitorDao::getOne).orElse(new AccAlarmMonitor());
        ModelUtil.copyProperties(accAlarmMonitorItem, alarmMonitor);
        AccAlarmMonitorBEventNum eventNum = accAlarmMonitorBEventNumDao.save(new AccAlarmMonitorBEventNum());
        alarmMonitor.setEventNum(eventNum.getId());
        alarmMonitor = accAlarmMonitorDao.save(alarmMonitor);
        accAlarmMonitorItem.setId(alarmMonitor.getId());
        accAlarmMonitorCacheListService.add(accAlarmMonitorItem);
        // 广播通知
        sendAllAlarmMonitorWS(true);
        JSONObject jsonObject = new JSONObject();
        List<AccAlarmMonitorItem> dataList = new ArrayList<>();
        dataList.add(accAlarmMonitorItem);
        jsonObject.put("alarm", createJsonFromItems(dataList));
        sendAllAlarmMonitorWS(new ZKResultMsg(jsonObject));
        return accAlarmMonitorItem;
    }

    @Override
    public JSONObject createAppData(AccAlarmMonitorItem accAlarmMonitorItem) {
        JSONObject retJson = new JSONObject();
        // java8的时间转换
        retJson.put("eventTime",
            DateUtil.getDate(accAlarmMonitorItem.getEventTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        // 设备名称
        retJson.put("devAlias", accAlarmMonitorItem.getDevAlias());
        // 事件点（门）
        retJson.put("eventPointName", accAlarmMonitorItem.getEventPointName());
        // 事件名称
        retJson.put("eventName", I18nUtil.i18nCode(accAlarmMonitorItem.getEventName()));
        // 人员名称
        retJson.put("personName", accAlarmMonitorItem.getName());
        // 读头名称
        retJson.put("readerName", accAlarmMonitorItem.getReaderName());

        // 区域名称
        retJson.put("areaName", "Area");
        // 卡号
        retJson.put("cardNo", "");
        retJson.put("personPin", "");
        retJson.put("verifyModeName", "");
        retJson.put("name", "");
        retJson.put("uniqueKey", accAlarmMonitorItem.getId());
        return retJson;
    }

    @Override
    public JSONObject getAllJson(boolean newMess) {
        JSONObject res = new JSONObject();
        JSONObject dxGrid = new JSONObject();
        JSONArray rows = new JSONArray();
        List<AccAlarmMonitorItem> list = getAll();
        /************** 构造数据 *******************/
        list.forEach(item -> {
            JSONObject row = new JSONObject();
            row.put("id", item.getId());
            JSONObject userdata = new JSONObject();
            String skin = (StringUtils.isBlank(systemSkin) || "default".equals(systemSkin)) ? "" : systemSkin;
            String photo = "/images/" + skin + "userImage.gif";
            if (StringUtils.isNotBlank(item.getPin())) {
                PersPersonItem pers = getPersItemByPin(item.getPin());
                if (pers != null) {
                    if (StringUtils.isNotBlank(pers.getPhotoPath())) {
                        if (FileUtils.fileExists(pers.getPhotoPath())) {
                            // 图片解密
                            String photoBase64 = accParamService.getAvatarBase64ByPath(pers.getPhotoPath());
                            // 设置base64数据
                            photo = AccConstants.PHOTO_BASE64_PREFIX + photoBase64;
                        }
                    }
                    if (StringUtils.isNotBlank(pers.getName())) {
                        item.setName(pers.getPin() + "(" + pers.getName() + ")");
                    } else {
                        item.setName(pers.getPin());
                    }
                }
            }
            if (StringUtils.isNotBlank(item.getName())) {
                userdata.put("photoPath", photo);
                userdata.put("status", "alarm");
            }
            row.put("userdata", userdata);
            row.put("style", "color:red");
            row.put("dataLevel", AccConstants.EVENT_ALARM);
            JSONArray data = createData(item);
            row.put("data", data);
            rows.add(row);
        });
        /**********************************************/
        dxGrid.put("rows", rows);
        dxGrid.put("total_count", rows.size());
        res.put("dxGrid", dxGrid);
        res.put("newMess", newMess);
        res.put("countAll", countAll());
        return res;
    }

    @Override
    public void sendAllAlarmMonitorWS(boolean newMess) {
        if (!accAlarmMonitorCacheListService.acquire()) {
            // 有最新的消息因为限流而未推送
            accAlarmMonitorCacheListService.markMonitorListChange(newMess);
            return;
        }
        // 最新的已经推送
        accAlarmMonitorCacheListService.delMonitorListChangeMark();
        // add by colin.cheng 2021-12-14 消息推送改为基于redis的发布/订阅，兼容多实例。
        if (ACC_MESSAGE_PUBLISH) {
            accCacheService.sendMessageToTopic(newMess + "", AccConstants.ALARM_CHANNEL);
        } else {
            // 广播消息
            messagingTemplate.convertAndSend("/topic/accAlarmMonitor/getEventData", getAllJson(newMess));
        }
    }

    public void convertAndSendAlarm(String message) {
        if (StringUtils.isNotBlank(message)) {
            final boolean newMess = Boolean.parseBoolean(message);
            messagingTemplate.convertAndSend("/topic/accAlarmMonitor/getEventData", getAllJson(newMess));
        }
    }

    /**
     * 构建数据json
     *
     * @param accAlarmMonitorItem
     * @return
     */
    private JSONArray createData(AccAlarmMonitorItem accAlarmMonitorItem) {
        JSONArray data = new JSONArray();
        data.add("");
        // data.add(accAlarmMonitorItem.getEventNum());
        // java8的时间转换
        Instant instant = accAlarmMonitorItem.getEventTime().toInstant();
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        data.add(dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        String devAlias = accAlarmMonitorItem.getDevAlias();
        if (StringUtils.isNotBlank(accAlarmMonitorItem.getDevSn())) {
            devAlias = accAlarmMonitorItem.getDevAlias() + "(" + accAlarmMonitorItem.getDevSn() + ")";
        }
        data.add(devAlias);
        data.add(accAlarmMonitorItem.getEventPointName());
        String eventName =
            StringUtils.isNotBlank(accAlarmMonitorItem.getEventName()) ? accAlarmMonitorItem.getEventName() : "";
        data.add(eventName);
        data.add(accAlarmMonitorItem.getName());
        data.add(accAlarmMonitorItem.getAreaName());
        data.add(priorityToString(accAlarmMonitorItem.getPriority()));
        data.add(statusToString(accAlarmMonitorItem.getStatus()));
        data.add("0,1");
        data.add(accAlarmMonitorItem.getPin());
        return data;
    }

    /**
     * 危险状态优先级格式化
     * 
     * @param priority:
     * @return java.lang.String
     * <AUTHOR>
     * @throws
     * @date 2022-06-23 11:07
     * @since 1.0.0
     */
    private String priorityToString(Short priority) {
        switch (priority + "") {
            case "0":
                return "auth_security_strengthLevel0";
            case "1":
                return "auth_security_strengthLevel1";
            case "2":
                return "auth_security_strengthLevel2";
            case "3":
                return "acc_musterPointReport_danger";
            default:
                return "common_unknown";
        }
    }

    @Override
    public ZKResultMsg checkAlarmMonitor(String ids) {
        String[] idArray = StringUtils.split(ids, ",");
        List<String> idList = Arrays.asList(idArray);
        idList.stream().filter(StringUtils::isNotBlank).map(accAlarmMonitorDao::findById).map(v -> v.orElse(null))
            .filter(Objects::nonNull).forEach(v -> {
                v.setStatus(new Integer(1).shortValue());
                accAlarmMonitorDao.save(v);
                accAlarmMonitorCacheListService.remove(v.getId());
            });
        return ZKResultMsg.successMsg();
    }

    @Override
    public void sendAlarmSMS(AccTransactionItem accTransactionItem, String type) {
        StringBuilder content = new StringBuilder();
        SimpleDateFormat longDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 取消报警
        if ("cancelAlarm".equals(type)) {
            content.append(
                I18nUtil.i18nCode("acc_rtMonitor_ackAlarmTime") + ": " + longDateFormat.format(new Date()) + "\n");
            content.append(I18nUtil.i18nCode("acc_rtMonitor_alarmEvent") + ": " + "\n");
        }
        content.append(I18nUtil.i18nCode("common_time") + ": "
            + longDateFormat.format(accTransactionItem.getEventTime()) + "<br/>");
        content.append(I18nUtil.i18nCode("common_dev_name") + ": " + accTransactionItem.getDevAlias() + "\n");
        content.append(I18nUtil.i18nCode("common_eventPoint") + ": " + accTransactionItem.getEventPointName() + "\n");
        content.append(I18nUtil.i18nCode("common_eventDescription") + ": "
            + I18nUtil.i18nCode(accTransactionItem.getEventName()) + "\n");
        if (StringUtils.isNotBlank(accTransactionItem.getPin())) {
            String name = "";
            if (StringUtils.isNotBlank(accTransactionItem.getName())) {
                name = accTransactionItem.getName().trim();
                // 中文下不显示lastname，非中文且lastname不为空时，拼接姓名，解决先前lastname为null时也拼接下去的问题
                if (!"zh_CN".equals(LocaleMessageSourceUtil.language)
                    && StringUtils.isNotBlank(accTransactionItem.getLastName())) {
                    name = (accTransactionItem.getName() + " " + accTransactionItem.getLastName()).trim();
                }
                name = "(" + name + ")";
            }
            content.append(I18nUtil.i18nCode("pers_person") + ": " + accTransactionItem.getPin() + name + "\n");
        }
        if (StringUtils.isNotBlank(accTransactionItem.getCardNo())) {
            content.append(I18nUtil.i18nCode("pers_card_cardNo") + ": " + accTransactionItem.getCardNo() + "\n");
        }
        accTransactionService.sendAlarmSMS(content.toString());
    }

    @Override
    public boolean addByTransaction(AccTransactionItem accTransaction) {
        AccAlarmMonitorItem item = new AccAlarmMonitorItem();
        item.setPriority(getPriorityByDevIdAndEventNo(accTransaction.getDevId(), accTransaction.getEventNo()));
        item.setAreaName(accTransaction.getAreaName());
        item.setEventTime(accTransaction.getEventTime());
        item.setEventName(accTransaction.getEventName());
        item.setDevAlias(accTransaction.getDevAlias());
        item.setEventPointName(accTransaction.getEventPointName());
        item.setEventName(accTransaction.getEventName());
        item.setName(accTransactionService.getPersonAllName(accTransaction.getName(), accTransaction.getLastName()));
        item.setPin(accTransaction.getPin());
        item.setReaderName(accTransaction.getReaderName());
        item.setStatus((short)0);
        item.setDevSn(accTransaction.getDevSn());
        saveItem(item);
        return true;
    }

    /**
     * 获取报警事件优先级
     *
     * @param devId:
     * @param eventNo:
     * @return java.lang.Short
     * @throws
     * <AUTHOR>
     * @date 2022-06-22 15:27
     * @since 1.0.0
     */
    private Short getPriorityByDevIdAndEventNo(String devId, Short eventNo) {
        if (eventNo != null) {
            Short priority = accDeviceEventService.getEventPriorityByDevIdAndEventNo(devId, eventNo);
            return priority == null ? Short.valueOf("0") : priority;
        }
        return (short)0;
    }

    @Override
    public List<AccAlarmMonitorItem> getAlarmEventByCondition(AccAlarmMonitorItem item) {
        Pager itemPager = accAlarmMonitorDao.getItemsBySql(item.getClass(), SQLUtil.getSqlByItem(item), 0, 100);
        return (List<AccAlarmMonitorItem>)itemPager.getData();
    }

    @Override
    public List<AccAlarmMonitorItem> getUncheckAlarmListBefore(String id, int length) {
        final Optional<AccAlarmMonitor> targetAlarmOpt = accAlarmMonitorDao.findById(id);
        if (targetAlarmOpt.isPresent()) {
            final AccAlarmMonitor targetAlarm = targetAlarmOpt.get();
            final Long count = accAlarmMonitorDao.countByEventTime(targetAlarm.getEventTime());
            final AccAlarmMonitorItem condition = new AccAlarmMonitorItem();
            condition.setAfterEventTime(targetAlarm.getEventTime());
            condition.setStatus((short)0);
            final Pager pager = accAlarmMonitorDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition),
                0, length + count.intValue());
            return (List<AccAlarmMonitorItem>)pager.getData();
        }
        return new LinkedList<>();
    }

    @Override
    public List<AccAlarmMonitorItem> getDataByEventNum(Map filter) {
        String sql = getSqlByAlarm(filter, null);
        int page = 0;
        Pager alarmMonitorPage = accAlarmMonitorDao.getItemsBySql(AccAlarmMonitorItem.class, sql, page, 60);
        List alarmList = alarmMonitorPage.getData();
        List totalList = new ArrayList(alarmList);
        while (alarmList != null && alarmList.size() >= 60) {
            page++;
            alarmMonitorPage = accAlarmMonitorDao.getItemsBySql(AccAlarmMonitorItem.class, sql, page, 60);
            alarmList = alarmMonitorPage.getData();
            totalList = ListUtils.sum(totalList, alarmList);
        }
        return totalList.size() > 0 ? totalList : null;
    }

    @Override
    public List<AccAlarmMonitorItem> getNotAcknowledgedItemByPage(Map<String, String> filter, int page, long target) {
        filter.put("eventNum", target + "");
        String sql = getSqlByAlarm(filter, true);
        Pager alarmMonitorPage = accAlarmMonitorDao.getItemsBySql(AccAlarmMonitorItem.class, sql, page, 60);
        if (alarmMonitorPage.getData().size() > 0) {
            List alarmList = alarmMonitorPage.getData();
            return alarmList;
        }
        return null;
    }

    @Override
    public JSONObject createJsonFromItems(List<AccAlarmMonitorItem> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        JSONObject res = new JSONObject();
        JSONObject dxGrid = new JSONObject();
        JSONArray rows = new JSONArray();
        AccAlarmMonitorItem item;
        for (int i = list.size() - 1; i >= 0; i--) {
            item = list.get(i);
            JSONObject row = new JSONObject();
            row.put("id", item.getId());
            row.put("eventNum", item.getEventNum());
            JSONObject userdata = new JSONObject();
            if (StringUtils.isNotBlank(item.getPin())) {
                //String pin = persParamsService.getEncryptPin(item.getPin());
                PersPersonItem pers = getPersItemByPin(item.getPin());
                item.setPin(item.getPin());
                if (Objects.nonNull(pers)) {
                    //String firstName = persParamsService.getEncryptName(pers.getName());
                    //String lastName = persParamsService.getEncryptLastName(pers.getLastName());
                    //String name = accTransactionService.getPersonAllName(firstName, lastName);
                    item.setName((pers.getName() + " " + pers.getLastName()).trim());
                }
            }
            String priorityColor = item.getPriority() + "";
            String statusColor = item.getStatus() + "";
            row.put("userdata", userdata);
            row.put("priorityStyle", priorityColor);
            row.put("statusStyle", statusColor);
            JSONArray data = createData(item);
            row.put("data", data);
            rows.add(row);
        }
        dxGrid.put("rows", rows);
        dxGrid.put("total_count", rows.size());
        res.put("dxGrid", dxGrid);
        return res;
    }

    @Override
    public JSONObject getAnalysis() {
        int critical = accAlarmMonitorDao.countByPriorityAndStatusNot(PRIORITY_CRITICAL, ACKNOWLEDGED_STATUS);
        int high = accAlarmMonitorDao.countByPriorityAndStatusNot(PRIORITY_HIGH, ACKNOWLEDGED_STATUS);
        int middle = accAlarmMonitorDao.countByPriorityAndStatusNot(PRIORITY_MIDDLE, ACKNOWLEDGED_STATUS);
        int low = accAlarmMonitorDao.countByPriorityAndStatusNot(PRIORITY_LOW, ACKNOWLEDGED_STATUS);
        Date weeTime = getWeeHours();
        int unhandled = accAlarmMonitorDao.countByEventTimeGreaterThanEqualAndStatus(weeTime, UNHANDLED_STATUS);
        int progress = accAlarmMonitorDao.countByEventTimeGreaterThanEqualAndStatus(weeTime, IN_PROGRESS_STATUS);
        int acknowledged = accAlarmMonitorDao.countByEventTimeGreaterThanEqualAndStatus(weeTime, ACKNOWLEDGED_STATUS);
        JSONObject analysis = new JSONObject();
        ArrayList chartValue = new ArrayList();
        analysis.put("chartValue", chartValue);
        chartValue.add(setNameAndVal(I18nUtil.i18nCode("auth_security_strengthLevel0"), low));
        chartValue.add(setNameAndVal(I18nUtil.i18nCode("auth_security_strengthLevel1"), middle));
        chartValue.add(setNameAndVal(I18nUtil.i18nCode("auth_security_strengthLevel2"), high));
        chartValue.add(setNameAndVal(I18nUtil.i18nCode("acc_musterPointReport_danger"), critical));
        JSONObject todayValue = new JSONObject();
        analysis.put("todayValue", todayValue);
        todayValue.put("unhandled", unhandled);
        todayValue.put("progress", progress);
        todayValue.put("acknowledged", acknowledged);
        List<Object[]> top5 = accAlarmMonitorDao.getalarmTop5();
        for (Object[] map : top5) {
            map[0] = I18nUtil.i18nCode(map[0] + "");
        }
        analysis.put("top5Value", top5);
        return analysis;
    }

    private JSONObject setNameAndVal(String name, int val) {
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("value", val);
        return json;
    }

    /**
     * 获取时分秒
     *
     * @return java.util.Date
     * <AUTHOR>
     * @throws
     * @date 2022-06-23 11:08
     * @since 1.0.0
     */
    private Date getWeeHours() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    @Override
    public String getDescription(String id) {
        Optional<AccAlarmMonitor> alarmOption = accAlarmMonitorDao.findById(id);
        if (alarmOption.isPresent()) {
            AccAlarmMonitor accAlarmMonitor = alarmOption.get();
            String eventName = accAlarmMonitor.getEventName();
            String deviceName = accAlarmMonitor.getDevAlias();
            String doorName = accAlarmMonitor.getReaderName();
            String description = I18nUtil.i18nCode(eventName) + "<" + deviceName + "," + doorName + ">";
            return description.trim();
        }
        return null;
    }

    @Override
    public ZKResultMsg changeStatus(String id, Short status, String acknowledgement) {
        Optional<AccAlarmMonitor> accAlarmMonitorOptional = accAlarmMonitorDao.findById(id);
        if (accAlarmMonitorOptional.isPresent()) {
            AccAlarmMonitor accAlarmMonitor = accAlarmMonitorOptional.get();
            accAlarmMonitor.setStatus(status);
            AccAlarmMonitorHistory accAlarmMonitorHistory = new AccAlarmMonitorHistory();
            accAlarmMonitorHistory.setStatus(status);
            accAlarmMonitorHistory.setAcknowledgement(acknowledgement);
            accAlarmMonitorHistory.setAccAlarmMonitor(accAlarmMonitor);
            accAlarmMonitorHistoryDao.save(accAlarmMonitorHistory);
            accAlarmMonitorDao.save(accAlarmMonitor);
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", id);
        jsonObject.put("status", status);
        sendAllAlarmMonitorWS(new ZKResultMsg(jsonObject));
        return ZKResultMsg.successMsg();
    }

    private void sendAllAlarmMonitorWS(ZKResultMsg res) {
        // 广播消息
        messagingTemplate.convertAndSend("/topic/accAlarmMonitor/changeEventDataStatus", res);
    }

    @Override
    public ZKResultMsg sendAlarmStatusEmail(String id, Short status, String acknowledgement, String emails) {
        String info = getDescription(id);
        info = I18nUtil.i18nCode("acc_alarm_eventDescription") + ":" + info + "\n" + I18nUtil.i18nCode("common_status")
            + ":" + statusToString(status) + "\n" + I18nUtil.i18nCode("acc_alarm_acknowledgement") + ":"
            + acknowledgement;
        String[] emailList = emails.split("[,;]");
        for (String email : emailList) {
            baseSendMailService.sendSampleTextMail(email, I18nUtil.i18nCode("acc_alarm_emailSubject"), info);
        }
        return ZKResultMsg.successMsg();
    }

    private String statusToString(Short status) {
        switch (status + "") {
            case "0":
                return "acc_alarm_unhandled";
            case "1":
                return "acc_alarm_inProcess";
            case "2":
                return "acc_alarm_acknowledged";
            default:
                return "";
        }
    }

    @Override
    public AccAlarmMonitorItem getById(String id) {
        Optional<AccAlarmMonitor> accAlarmMonitorOptional = accAlarmMonitorDao.findById(id);
        if (accAlarmMonitorOptional.isPresent()) {
            AccAlarmMonitor accAlarmMonitor = accAlarmMonitorOptional.get();
            List<AccAlarmMonitorHistory> historyList = accAlarmMonitor.getAccAlarmMonitorHistoryList();
            AccAlarmMonitorItem item = new AccAlarmMonitorItem();
            ModelUtil.copyProperties(accAlarmMonitor, item);
            List<AccAlarmMonitorHistoryItem> historyItems =
                historyList.stream().map(history -> ModelUtil.copyProperties(history, new AccAlarmMonitorHistoryItem()))
                    .collect(Collectors.toList());
            item.setHistoryItemList(historyItems);
            return item;
        }
        return null;
    }

    @Override
    public Pager getHistoryByPage(BaseItem condition, int page, int size, long limit) {
        Pager pager = accAlarmMonitorHistoryDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition),
            page, size, limit);
        List<AccAlarmMonitorHistoryItem> items = (List<AccAlarmMonitorHistoryItem>)pager.getData();
        for (AccAlarmMonitorHistoryItem item : items) {
            if (StringUtils.isNotBlank(item.getDevSn())) {
                item.setDeviceName(item.getDeviceName() + "(" + item.getDevSn() + ")");
            }
        }
        pager.setData(items);
        return pager;
    }

    @Override
    public List getHistoryData(Class targetClass, BaseItem condition, int beginIndex, int endIndex) {
        return accAlarmMonitorHistoryDao.getItemsDataBySql(targetClass, SQLUtil.getSqlByItem(condition), beginIndex,
            endIndex, true);
    }

    @Override
    public ZKResultMsg clearHistoryData() {
        accAlarmMonitorHistoryDao.deleteAll();
        return ZKResultMsg.successMsg();
    }

    @Override
    public List<AccAlarmMonitorItem> getFirstPage(Map filters) {
        String sql = getSqlByAlarm(filters, null);
        Pager alarmMonitorPage = accAlarmMonitorDao.getItemsBySql(AccAlarmMonitorItem.class, sql, 0, 60);
        if (alarmMonitorPage.getData().size() > 0) {
            List alarmList = alarmMonitorPage.getData();
            return alarmList;
        }
        return null;
    }

    @Override
    public Pager getPager(String sessionId, AccAlarmMonitorItem condition, int page, int size, long limit) {
        Pager pager =
            accAlarmMonitorDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size, limit);
        List<AccAlarmMonitorItem> items = (List<AccAlarmMonitorItem>)pager.getData();
        boolean pinEncrypt = securityService.checkPermissionExceptSupperUser(sessionId, "acc:pin:encryptProp");
        String pinEncryptMode = baseSysParamService.getValByName("pers.pin.encryptMode");
        boolean nameEncrypt = securityService.checkPermissionExceptSupperUser(sessionId, "acc:name:encryptProp");
        String nameEncryptMode = baseSysParamService.getValByName("pers.name.encryptMode");
        for (AccAlarmMonitorItem item : items) {
            if (StringUtils.isNotBlank(item.getDevSn())) {
                item.setDevAlias(item.getDevAlias() + "(" + item.getDevSn() + ")");
            }
            String pin = item.getPin();
            if (StringUtils.isNotBlank(pin) && pinEncrypt) {
                pin = StrUtil.convertToEncrypt(pin, pinEncryptMode);
            }
            String name = item.getName();
            if (StringUtils.isNotBlank(name) && nameEncrypt) {
                name = StrUtil.convertToEncrypt(name, nameEncryptMode);
            }
            item.setName(StringUtils.isNotBlank(name) ? pin + "(" + name + ")" : pin);
        }
        pager.setData(items);
        return pager;
    }

    @Override
    public List<?> getItemData(Class targetClass, BaseItem condition, int beginIndex, int endIndex) {
        return accAlarmMonitorDao.getItemsDataBySql(targetClass, SQLUtil.getSqlByItem(condition), beginIndex, endIndex,
            true);
    }

    @Override
    public JSONObject getAnalysisByAuthFilter(String sessionId) {
        String areaName = accTransactionService.getAreaNamesBySessionId(sessionId);
        int critical = 0;
        int high = 0;
        int middle = 0;
        int low = 0;
        int unhandled = 0;
        int progress = 0;
        int acknowledged = 0;
        Date weeTime = getWeeHours();
        List<Object[]> topEvent = new ArrayList<>();
        List<Object[]> top5 = new ArrayList<>();
        if (StringUtils.isNotBlank(areaName)) {
            List<String> areaNameList = StrUtil.strToList(areaName);
            critical = accAlarmMonitorDao.countByPriorityAndStatusNotAndAreaNameIn(PRIORITY_CRITICAL,
                ACKNOWLEDGED_STATUS, areaNameList);
            high = accAlarmMonitorDao.countByPriorityAndStatusNotAndAreaNameIn(PRIORITY_HIGH, ACKNOWLEDGED_STATUS,
                areaNameList);
            middle = accAlarmMonitorDao.countByPriorityAndStatusNotAndAreaNameIn(PRIORITY_MIDDLE, ACKNOWLEDGED_STATUS,
                areaNameList);
            low = accAlarmMonitorDao.countByPriorityAndStatusNotAndAreaNameIn(PRIORITY_LOW, ACKNOWLEDGED_STATUS,
                areaNameList);
            unhandled = accAlarmMonitorDao.countByEventTimeGreaterThanEqualAndStatusAndAreaNameIn(weeTime,
                UNHANDLED_STATUS, areaNameList);
            progress = accAlarmMonitorDao.countByEventTimeGreaterThanEqualAndStatusAndAreaNameIn(weeTime,
                IN_PROGRESS_STATUS, areaNameList);
            acknowledged = accAlarmMonitorDao.countByEventTimeGreaterThanEqualAndStatusAndAreaNameIn(weeTime,
                ACKNOWLEDGED_STATUS, areaNameList);
            topEvent = accAlarmMonitorDao.getalarmTop5ByAuthFilter(areaNameList);
        } else {
            critical = accAlarmMonitorDao.countByPriorityAndStatusNot(PRIORITY_CRITICAL, ACKNOWLEDGED_STATUS);
            high = accAlarmMonitorDao.countByPriorityAndStatusNot(PRIORITY_HIGH, ACKNOWLEDGED_STATUS);
            middle = accAlarmMonitorDao.countByPriorityAndStatusNot(PRIORITY_MIDDLE, ACKNOWLEDGED_STATUS);
            low = accAlarmMonitorDao.countByPriorityAndStatusNot(PRIORITY_LOW, ACKNOWLEDGED_STATUS);
            unhandled = accAlarmMonitorDao.countByEventTimeGreaterThanEqualAndStatus(weeTime, UNHANDLED_STATUS);
            progress = accAlarmMonitorDao.countByEventTimeGreaterThanEqualAndStatus(weeTime, IN_PROGRESS_STATUS);
            acknowledged = accAlarmMonitorDao.countByEventTimeGreaterThanEqualAndStatus(weeTime, ACKNOWLEDGED_STATUS);
            topEvent = accAlarmMonitorDao.getalarmTop5();
        }
        JSONObject analysis = new JSONObject();
        ArrayList chartValue = new ArrayList();
        analysis.put("chartValue", chartValue);
        chartValue.add(setNameAndVal(I18nUtil.i18nCode("auth_security_strengthLevel0"), low));
        chartValue.add(setNameAndVal(I18nUtil.i18nCode("auth_security_strengthLevel1"), middle));
        chartValue.add(setNameAndVal(I18nUtil.i18nCode("auth_security_strengthLevel2"), high));
        chartValue.add(setNameAndVal(I18nUtil.i18nCode("acc_musterPointReport_danger"), critical));
        JSONObject todayValue = new JSONObject();
        analysis.put("todayValue", todayValue);
        todayValue.put("unhandled", unhandled);
        todayValue.put("progress", progress);
        todayValue.put("acknowledged", acknowledged);
        int top = 0;
        for (Object[] map : topEvent) {
            if (top < 5) {
                top5.add(map);
                map[0] = I18nUtil.i18nCode(map[0] + "");
                top++;
            }
        }
        analysis.put("top5Value", top5);
        return analysis;
    }

    @Override
    public JSONObject getAccAlarmData(String sessionId) {
        JSONObject jsonObject = new JSONObject();
        String areaName = accTransactionService.getAreaNamesBySessionId(sessionId);
        int critical = 0;
        int high = 0;
        int middle = 0;
        int low = 0;
        AccAlarmMonitorItem accAlarmMonitorItem = new AccAlarmMonitorItem();
        if (StringUtils.isNotBlank(areaName)) {
            accAlarmMonitorItem.setAreaNameIn(areaName);
            List<String> areaNameList = StrUtil.strToList(areaName);
            critical = accAlarmMonitorDao.countByPriorityAndAreaNameIn(PRIORITY_CRITICAL, areaNameList);
            high = accAlarmMonitorDao.countByPriorityAndAreaNameIn(PRIORITY_HIGH, areaNameList);
            middle = accAlarmMonitorDao.countByPriorityAndAreaNameIn(PRIORITY_MIDDLE, areaNameList);
            low = accAlarmMonitorDao.countByPriorityAndAreaNameIn(PRIORITY_LOW, areaNameList);
        } else {
            critical = accAlarmMonitorDao.countByPriority(PRIORITY_CRITICAL);
            high = accAlarmMonitorDao.countByPriority(PRIORITY_HIGH);
            middle = accAlarmMonitorDao.countByPriority(PRIORITY_MIDDLE);
            low = accAlarmMonitorDao.countByPriority(PRIORITY_LOW);
        }
        jsonObject.put("critical", critical);
        jsonObject.put("high", high);
        jsonObject.put("middle", middle);
        jsonObject.put("low", low);
        return jsonObject;
    }

    private List<AccAlarmMonitorItem> getAlarmDataByPager(BaseItem condition, int page, int size, long limit) {
        Pager pager =
            accAlarmMonitorDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size, limit);
        List<AccAlarmMonitorItem> items = (List<AccAlarmMonitorItem>)pager.getData();
        for (AccAlarmMonitorItem item : items) {
            if (StringUtils.isNotBlank(item.getDevSn())) {
                item.setDevAlias(item.getDevAlias() + "(" + item.getDevSn() + ")");
            }
            if (StringUtils.isNotBlank(item.getEventName())) {
                item.setEventName(I18nUtil.i18nCode(item.getEventName()));
            }
        }
        return items;
    }

    private String getSqlByAlarm(Map<String, String> filters, Boolean before) {
        AccAlarmMonitorItem condition = new AccAlarmMonitorItem();
        condition.setAreaNameIn(accTransactionService.getAreaNamesBySessionId(filters.get("sessionId")));
        String baseSql = SQLUtil.getSqlByItem(condition);
        baseSql = StringUtils.substring(baseSql, 0, baseSql.indexOf("ORDER"));
        baseSql = baseSql + "and STATUS!=2";
        for (String name : filters.keySet()) {
            boolean flag = !BooleanUtils.toBoolean(filters.get(name));
            if (flag) {
                switch (name) {
                    case "unhandled":
                        baseSql = baseSql + " AND STATUS!=0 ";
                        break;
                    case "inProgess":
                        baseSql = baseSql + " AND STATUS!=1 ";
                        break;
                    case "Low":
                        baseSql = baseSql + " AND PRIORITY!=0";
                        break;
                    case "Medium":
                        baseSql = baseSql + " AND PRIORITY!=1";
                        break;
                    case "Higher":
                        baseSql = baseSql + " AND PRIORITY!=2";
                        break;
                    case "Critical":
                        baseSql = baseSql + " AND PRIORITY!=3";
                        break;
                    default:
                }
            }
        }
        /*Long num = null;
        String eventNum = filters.get("eventNum");
        if (StringUtils.isNotBlank(eventNum)) {
            try {
                num = Long.valueOf(eventNum);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        if (before == null) {
            baseSql = baseSql + " ORDER BY PRIORITY DESC";
        } else if (before) {
            baseSql = baseSql + " AND EVENT_NUM<=" + num + " ORDER BY EVENT_NUM DESC";
        } else {
            baseSql = baseSql + " AND EVENT_NUM>" + num + " ORDER BY EVENT_NUM ASC";
        }*/

        baseSql = baseSql + " ORDER BY PRIORITY  desc , event_time desc ";
        return baseSql;
    }
}
