package com.zkteco.zkbiosecurity.acc.data.upgrade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class AccVer3_0_3 implements UpgradeVersionManager {
    @Autowired
    private AuthPermissionService authPermissionService;
    @Value("${system.language:zh_CN}")
    private String language;
    @Value("${system.isCloud:false}")
    private Boolean isCloud;

    @Override
    public String getModule() {
        return BaseConstants.ACC;
    }

    @Override
    public String getVersion() {
        return "v3.0.3";
    }

    @Override
    public boolean executeUpgrade() {
        if (!"zh_CN".equals(language) && !isCloud) {
            AuthPermissionItem appModuleItem = null;
            AuthPermissionItem appMenuItem = null;
            AuthPermissionItem appButtonItem = null;

            appModuleItem = authPermissionService.getItemByCode("App");
            if (null == appModuleItem) {
                // 管理员APP模块
                appModuleItem = new AuthPermissionItem("App", "app_module", "app",
                    AuthContants.RESOURCE_TYPE_APP_SYSTEM, ZKConstant.TRUE, 998);
                authPermissionService.initData(appModuleItem);
            }
            appMenuItem = new AuthPermissionItem("AppAcc", "app_acc", "app:APPacc", AuthContants.RESOURCE_TYPE_APP_MENU,
                ZKConstant.TRUE, 2);
            appMenuItem.setParentId(appModuleItem.getId());
            appMenuItem = authPermissionService.initData(appMenuItem);
            // 门禁--实时监控
            appButtonItem = new AuthPermissionItem("AppAccMonitor", "app_acc_monitor", "app:APPaccMonitor",
                AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 1);
            appButtonItem.setParentId(appMenuItem.getId());
            authPermissionService.initData(appButtonItem);
            // 门禁--设备
            appButtonItem = new AuthPermissionItem("AppAccDevice", "app_acc_device", "app:APPaccDevice",
                AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 2);
            appButtonItem.setParentId(appMenuItem.getId());
            authPermissionService.initData(appButtonItem);
            // 门禁--报警监控
            appButtonItem = new AuthPermissionItem("AppAccAlarm", "app_acc_alarm", "app:APPaccAlarm",
                AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 3);
            appButtonItem.setParentId(appMenuItem.getId());
            authPermissionService.initData(appButtonItem);
            // 门禁--报表
            appButtonItem = new AuthPermissionItem("AppAccReport", "app_acc_report", "app:APPaccReport",
                AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 4);
            appButtonItem.setParentId(appMenuItem.getId());
            authPermissionService.initData(appButtonItem);
        }
        return true;
    }
}
