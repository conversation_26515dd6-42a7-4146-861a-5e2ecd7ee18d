/**
 * File Name: AccDSTimeServiceImpl Created by GenerationTools on 2018-02-28 下午02:21 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;
import java.util.concurrent.TimeUnit;

import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.acc.utils.AccDataUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.dao.AccDSTimeBIdDao;
import com.zkteco.zkbiosecurity.acc.dao.AccDSTimeDao;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.model.AccDSTime;
import com.zkteco.zkbiosecurity.acc.model.AccDSTimeBId;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.service.AccDSTimeService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.vo.AccDSTimeItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;

/**
 * 门禁夏令时service实现类
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-28 下午02:21
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class AccDSTimeServiceImpl implements AccDSTimeService {
    @Autowired
    private AccDSTimeDao accDSTimeDao;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDSTimeBIdDao accDSTimeBIdDao;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;

    /**
     * 保存item实体，一般会有复杂业务逻辑
     *
     * @param item
     */
    @Override
    public AccDSTimeItem saveItem(AccDSTimeItem item) {
        AccDSTime accDSTime = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accDSTimeDao.findById(id)).orElse(new AccDSTime());
        ModelUtil.copyPropertiesIgnoreNull(item, accDSTime);
        if (accDSTime.getBussinessId() == null) {
            final AccDSTimeBId bid = accDSTimeBIdDao.save(new AccDSTimeBId());
            accDSTime.setBussinessId(bid.getId());
        }
        accDSTimeDao.save(accDSTime);
        if (StringUtils.isNotBlank(item.getId())) {
            List<AccDevice> accDeviceList =
                accDeviceDao.findByAccDSTime_IdIn((List<String>)CollectionUtil.strToList(item.getId()));
            for (AccDevice dev : accDeviceList) {
                accDevCmdManager.delDSTime(dev.getSn(), false);
                accDevCmdManager.setDSTime(dev.getSn(), item, false);
            }
        }
        item.setId(accDSTime.getId());
        return item;
    }

    /**
     * 根据条件查询
     *
     * @param condition
     * @return
     */
    @Override
    public List<AccDSTimeItem> getByCondition(AccDSTimeItem condition) {
        return (List<AccDSTimeItem>)accDSTimeDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accDSTimeDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                final Optional<AccDSTime> accDSTime = accDSTimeDao.findById(id);
                if (accDSTime.isPresent()) {
                    if (Boolean.TRUE.equals(accDSTime.get().getInitFlag())) {
                        throw new ZKBusinessException("common_prompt_initDataCanNotDel");
                    }
                    accDSTimeDao.deleteById(id);
                }
            }
        }
        return false;
    }

    @Override
    public AccDSTimeItem getItemById(String id) {
        List<AccDSTimeItem> items = getByCondition(new AccDSTimeItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public AccDSTimeItem initData(AccDSTimeItem item) {
        // 通过名称判断是否已存在
        final AccDSTime dsTime = accDSTimeDao.findByName(item.getName());
        if (dsTime == null) {
            return this.saveItem(item);
        } else {
            item.setId(dsTime.getId());
            return item;
        }
    }

    @Override
    public boolean isExistName(String name) {
        AccDSTime accDSTime = accDSTimeDao.findByName(name);
        return accDSTime == null;
    }

    @Override
    public AccDSTimeItem getItemByInitFlag() {
        AccDSTimeItem accDSTimeItem = new AccDSTimeItem();
        accDSTimeItem.setInitFlag(true);
        return getByCondition(accDSTimeItem).get(0);
    }

    @Override
    public String setDSTime(String dSTimeId, String devId) {
        AccDevice accDevice = accDeviceDao.findById(devId).orElse(null);
        AccDSTime accDsTime = StringUtils.isNoneBlank(dSTimeId) ? accDSTimeDao.findById(dSTimeId).orElse(null) : null;
        String devAlisa = "";
        if (Objects.nonNull(accDevice)) {
            accDevCmdManager.delDSTime(accDevice.getSn(), false);
            if (Objects.nonNull(accDsTime)) {
                AccDSTimeItem dsTimeItem = ModelUtil.copyProperties(accDsTime, new AccDSTimeItem());
                accDevCmdManager.setDSTime(accDevice.getSn(), dsTimeItem, false);
                // 非子设备需要同步一下时间，子设备交给IR9000自己同步 by juvenile.li add
                if (Objects.isNull(accDevice.getParentDevice())) {
                    // 同时同步设备时间，支持时区的设备同步时间需要发送gmt时间
                    if (accDeviceOptionService.isSupportFun(accDevice.getSn(), "MachineTZFunOn")) {
                        accDevCmdManager.syncTime(accDevice.getSn(), AccDataUtil.getGMT(), false);
                    } else {
                        accDevCmdManager.syncTime(accDevice.getSn(), new Date(), false);
                    }
                }
            }
            accDevice.setAccDSTime(accDsTime);
            accDeviceDao.save(accDevice);
            devAlisa = accDevice.getAlias();
        }
        return devAlisa;
    }

    @Override
    public void handlerTransfer(List<AccDSTimeItem> dstimeItems) {
        // 夏令时数据一般不会太多这里就不进行分批处理
        for (AccDSTimeItem item : dstimeItems) {
            AccDSTime dsTime = accDSTimeDao.findByName(item.getName());
            if (Objects.isNull(dsTime)) {
                dsTime = new AccDSTime();
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(item, dsTime, "id");
            accDSTimeDao.save(dsTime);
        }
    }

    @Override
    public Object isExistByTimeZone(String timeZone) {
        return accDSTimeDao.existsByTimeZone(timeZone);
    }

    @Override
    public String getDefaultTimeZone() {
        Calendar ca = Calendar.getInstance();
        TimeZone tz = ca.getTimeZone();
        int second = tz.getRawOffset();
        long hour = TimeUnit.MILLISECONDS.toHours(second);
        long minute = Math.abs(TimeUnit.MILLISECONDS.toMinutes(second)) % 60;
        String sign = hour >= 0 ? "+" : "-";
        hour = Math.abs(hour);
        String res = sign + String.format("%02d", hour) + String.format("%02d", minute);
        return res;
    }
}