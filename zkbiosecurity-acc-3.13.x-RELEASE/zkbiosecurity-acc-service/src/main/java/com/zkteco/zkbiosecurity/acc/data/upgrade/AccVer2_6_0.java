package com.zkteco.zkbiosecurity.acc.data.upgrade;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * 升级到2.6.0
 * 
 * <AUTHOR>
 * @DATE 2020-05-27 11:57
 * @since 1.0.0
 */
@Slf4j
@Component
public class AccVer2_6_0 implements UpgradeVersionManager {
    @Autowired
    private AuthPermissionService authPermissionService;

    @Override
    public String getModule() {
        return BaseConstants.ACC;
    }

    @Override
    public String getVersion() {
        return "v2.6.0";
    }

    @Override
    public boolean executeUpgrade() {
        // 添加FirstInLastOut菜单
        AuthPermissionItem subMenuItem = authPermissionService.getItemByCode("AccFirstInLastOut");
        if (Objects.isNull(subMenuItem)) {
            AuthPermissionItem topMenuItem = authPermissionService.getItemByCode("AccReports");
            if (Objects.nonNull(topMenuItem)) {

                subMenuItem = new AuthPermissionItem("AccFirstInLastOut", "acc_leftMenu_firstInLastOut",
                    "acc:firstInLastOut", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 7);
                subMenuItem.setParentId(topMenuItem.getId());
                subMenuItem.setActionLink("accFirstInLastOut.do");
                subMenuItem = authPermissionService.initData(subMenuItem);

                AuthPermissionItem subButtonItem =
                    new AuthPermissionItem("AccFirstInLastOutRefresh", "common_op_refresh",
                        "acc:firstInLastOut:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);

                subButtonItem =
                    new AuthPermissionItem("AccFirstInLastOutClearData", I18nUtil.i18nCode("common_op_clearData"),
                        "acc:firstInLastOut:clearData", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.saveItem(subButtonItem);

                subButtonItem = new AuthPermissionItem("AccFirstInLastOutExport", "common_op_export",
                    "acc:firstInLastOut:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);
            }
        }

        // 隐藏最后访问位置菜单
        subMenuItem = authPermissionService.getItemByCode("AccPersonLastAddr");
        if (Objects.nonNull(subMenuItem)) {
            subMenuItem.setAvailable(ZKConstant.FALSE);
            authPermissionService.saveItem(subMenuItem);
        }

        return true;
    }
}
