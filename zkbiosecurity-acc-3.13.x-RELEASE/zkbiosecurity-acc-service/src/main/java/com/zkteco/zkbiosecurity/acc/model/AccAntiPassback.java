/**
 * File Name: AccAntiPassback Created by GenerationTools on 2018-03-13 上午10:27 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 AccAntiPassback
 * 
 * <AUTHOR>
 * @date: 2018-03-13 上午10:27
 * @version v1.0
 */
@Entity
@Table(name = "ACC_ANTIPASSBACK")
@Getter
@Setter
@Accessors(chain = true)
public class AccAntiPassback extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;
    /** 业务id */
    @Column(name = "BUSINESS_ID")
    private Long businessId;
    /** 反潜名称 */
    @Column(name = "NAME", length = 100)
    private String name;
    /**  */
    @ManyToOne(optional = false)
    @JoinColumn(name = "DEV_ID", nullable = false)
    private AccDevice accDevice;

    /**  */
    @Column(name = "APB_RULE")
    private Short apbRule;

    /** 规则类型 0:门反潜 1:读头反潜*/
    @Column(name = "RULE_TYPE")
    private Short apbRuleType;

    /** 组1 */
    @OneToOne(cascade = CascadeType.REMOVE)
    @JoinColumn(name = "TRIGGER_GROUP1", referencedColumnName = "ID")
    private AccTriggerGroup triggerGroup1;

    /** 组2 */
    @OneToOne(cascade = CascadeType.REMOVE)
    @JoinColumn(name = "TRIGGER_GROUP2", referencedColumnName = "ID")
    private AccTriggerGroup triggerGroup2;

}