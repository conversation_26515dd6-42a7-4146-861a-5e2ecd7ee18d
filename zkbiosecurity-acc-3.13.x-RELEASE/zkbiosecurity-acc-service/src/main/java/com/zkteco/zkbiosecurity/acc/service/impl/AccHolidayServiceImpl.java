/**
 * File Name: AccHolidayServiceImpl Created by GenerationTools on 2018-02-26 下午05:53 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.dao.AccHolidayDao;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.model.AccHoliday;
import com.zkteco.zkbiosecurity.acc.service.AccHolidayService;
import com.zkteco.zkbiosecurity.acc.vo.AccHolidayItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 对应百傲瑞达 AccHolidayServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-02-26 下午05:53
 * @version v1.0
 */
@Service
@Transactional
public class AccHolidayServiceImpl implements AccHolidayService {
    @Autowired
    private AccHolidayDao accHolidayDao;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccDevCmdManager accDevCmdManager;

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    @Override
    public AccHolidayItem saveItem(AccHolidayItem item) {
        AccHoliday accHoliday = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accHolidayDao.findById(id)).orElse(new AccHoliday());
        Boolean isModified = false;
        if ((accHoliday.getId() == null)) {
            isModified = true;
        } else {
            AccHoliday newAccHoliday = new AccHoliday();
            ModelUtil.copyPropertiesIgnoreNull(item, newAccHoliday);
            newAccHoliday.setStartDate(item.getStartDate());
            newAccHoliday.setEndDate(item.getEndDate());
            if (!accHoliday.equals(newAccHoliday)) {
                isModified = true;
            }
        }
        ModelUtil.copyPropertiesIgnoreNull(item, accHoliday);
        accHoliday.setStartDate(item.getStartDate());
        accHoliday.setEndDate(item.getEndDate());
        accHolidayDao.save(accHoliday);
        // 重新同步节假日到设备
        if (isModified) { // 判断是否修改
            syncHolidayToDev();
        }
        item.setId(accHoliday.getId());
        return item;
    }

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    @Override
    public List<AccHolidayItem> getByCondition(AccHolidayItem condition) {
        return (List<AccHolidayItem>)accHolidayDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accHolidayDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accHolidayDao.deleteById(id);
            }
            // 重新同步节假日到设备
            syncHolidayToDev();
        }
        return false;
    }

    /* (non-Javadoc)
     * @see com.jmax.acc.service.AccHolidayService#getItemById(java.lang.String)
     */
    @Override
    public AccHolidayItem getItemById(String id) {
        List<AccHolidayItem> items = getByCondition(new AccHolidayItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public String isExist(String name) {
        AccHoliday accHoliday = accHolidayDao.findByName(name);
        return accHoliday == null ? "true" : "false";
    }

    @Override
    public short isInHoliday(Date eventDate) {
        List<AccHoliday> holidaysList = accHolidayDao.findAll();
        short holidayType = 0;
        for (AccHoliday holiday : holidaysList) {
            holidayType = isInHoliday(eventDate, holiday);
            if (holidayType > 0) {
                break;
            }
        }
        return holidayType;
    }

    @Override
    public void handlerTransfer(List<AccHolidayItem> accHolidayItems) {
        // 数据量不是很大,没必要分批处理
        // 根据名称获取节假日对象集合 名称去重
        Collection<String> names = CollectionUtil.getPropertyList(accHolidayItems, AccHolidayItem::getName, "-1");
        List<AccHoliday> accHolidayList = accHolidayDao.findByNameIn(names);
        Map<String, AccHoliday> accHolidayMap = CollectionUtil.listToKeyMap(accHolidayList, AccHoliday::getName);
        for (AccHolidayItem accHolidayItem : accHolidayItems) {
            AccHoliday accHoliday = accHolidayMap.remove(accHolidayItem.getName());
            if (Objects.isNull(accHoliday)) {
                accHoliday = new AccHoliday();
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(accHolidayItem, accHoliday, "id");
            // 开始时间设置
            accHoliday.setStartDate(accHolidayItem.getStartDate());
            // 结束时间设置
            accHoliday.setEndDate(accHolidayItem.getEndDate());
            accHolidayDao.save(accHoliday);
            // 是否下发命令到设备
        }

    }

    /**
     * @Description: 判断是否属于节假日，是则返回节假日类型
     * <AUTHOR>
     * @date 2018/6/15 15:02
     * @param eventDate
     * @param holiday
     * @return
     */
    private Short isInHoliday(Date eventDate, AccHoliday holiday) {
        short holidayType = 0;
        Date startDate = new Date(holiday.getStartDate().getTime());
        Date endDate = new Date(holiday.getEndDate().getTime());
        boolean isLoopByYear = holiday.getIsLoopByYear();
        if (isLoopByYear && DateUtil.getYear(eventDate) > DateUtil.getYear(startDate)) {
            startDate = DateUtil.setYears(startDate, DateUtil.getYear(eventDate));
            endDate = DateUtil.setYears(endDate, DateUtil.getYear(eventDate));
        }
        endDate.setDate(endDate.getDate() + 1);// 由于节假日日期设置时，没有保存时分秒，从数据库获取出来的结束时间相当于是前一天
        if (eventDate.getTime() >= startDate.getTime() && eventDate.getTime() < endDate.getTime()) {
            holidayType = holiday.getHolidayType();
        }
        return holidayType;
    }

    /**
     * 同步节假日到所有设备
     * 
     * @author: mingfa.zheng
     * @date: 2018/5/25 13:20
     * @return:
     */
    private void syncHolidayToDev() {
        List<AccDevice> accDeviceList = accDeviceDao.findAll();
        accDeviceList.stream().forEach(accDevice -> {
            accDevCmdManager.delAllHolidayFromDev(accDevice.getSn(), false);
            List<AccHoliday> accHolidayList = accHolidayDao.findAll();
            List<AccHolidayItem> accHolidayItemList = getByCondition(new AccHolidayItem());
            if (accHolidayList != null && accHolidayList.size() > 0) {
                accDevCmdManager.setHolidayToDev(accDevice.getSn(), accHolidayItemList, false);
            }
        });
    }
}