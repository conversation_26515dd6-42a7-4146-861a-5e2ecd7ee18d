/**
 * File Name: AccLinkageInOut
 * Created by GenerationTools on 2018-03-16 下午04:41
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * 对应百傲瑞达实体 AccLinkageInOut
 * <AUTHOR>
 * @date:	2018-03-16 下午04:41
 * @version v1.0
 */
@Entity
@Table(name = "ACC_LINKAGE_INOUT")
@Getter
@Setter
@Accessors(chain=true)
public class AccLinkageInOut extends BaseModel implements Serializable {

	/** */
	private static final long serialVersionUID = 1L;

	/**  */
	@ManyToOne
	@JoinColumn(name="LINKAGE_ID")
	private AccLinkage accLinkage;

	/**  */
	@Column(name="INPUT_TYPE",length=30,nullable=false)
	private String inputType;

	/**  */
	@Column(name="INPUT_ID",nullable=false)
	private String inputId;

	/**  */
	@Column(name="OUTPUT_TYPE",length=30)
	private String outputType;

	/**  */
	@Column(name="OUTPUT_ID")
	private String outputId;

	/**  */
	@Column(name="ACTION_TYPE",nullable=false)
	private Short actionType;

	/**  */
	@Column(name="ACTION_TIME",nullable=false)
	private Short actionTime;

	/**  */
	@OneToMany(mappedBy="accLinkageInOut",cascade = CascadeType.REMOVE)
	private Set<AccLinkageTrigger> accLinkageTriggerSet = new HashSet<AccLinkageTrigger>();

	public AccLinkageInOut(){

	}

	public AccLinkageInOut(AccLinkage linkage, String inputId, String inputType,
						   String outputId, String outputType, Short actionTime, Short actionType)
	{
		this.accLinkage = linkage;
		this.inputId = inputId;
		this.inputType = inputType;
		this.outputId = outputId;
		this.outputType = outputType;
		this.actionTime = actionTime;
		this.actionType = actionType;
	}
}