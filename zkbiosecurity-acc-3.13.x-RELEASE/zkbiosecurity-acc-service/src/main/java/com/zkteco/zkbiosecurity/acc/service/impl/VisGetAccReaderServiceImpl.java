package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.service.AccReaderService;
import com.zkteco.zkbiosecurity.acc.vo.AccReaderItem;
import com.zkteco.zkbiosecurity.acc.vo.AccSelectReaderItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.vis.service.VisGetAccReaderService;
import com.zkteco.zkbiosecurity.vis.vo.Vis4AccReaderItem;
import com.zkteco.zkbiosecurity.vis.vo.VisGetAccSelectReaderItem;

@Service
@Transactional
public class VisGetAccReaderServiceImpl implements VisGetAccReaderService
{
	@Autowired
	private AccReaderService accReaderService;

	@Override
	public Vis4AccReaderItem getReaderById(String pointId)
	{
		AccReaderItem accReaderItem = accReaderService.getItemById(pointId);
		if(accReaderItem != null)
		{
			return ModelUtil.copyProperties(accReaderItem, new Vis4AccReaderItem());
		}
		return null;
	}

	@Override
	public Pager getSelectItemByPage(VisGetAccSelectReaderItem visGetAccSelectReaderItem, int pageNo, int pageSize)
	{
		AccSelectReaderItem accSelectReaderItem = new AccSelectReaderItem();
		ModelUtil.copyProperties(visGetAccSelectReaderItem,accSelectReaderItem);
		Pager pager = accReaderService.getSelectItemByPage(accSelectReaderItem, pageNo, pageSize);
		List<AccSelectReaderItem> accSelectReaderItemList = (List<AccSelectReaderItem>) pager.getData();
		List<VisGetAccSelectReaderItem> visGetAccSelectReaderItemList = ModelUtil.copyListProperties(accSelectReaderItemList, VisGetAccSelectReaderItem.class);
		pager.setData(visGetAccSelectReaderItemList);
		return pager;
	}

	@Override
	public Vis4AccReaderItem getByReaderStateAndDoorId(short readerState, String doorId) {
		AccReaderItem accReaderItem = accReaderService.getByReaderStateAndDoorId(readerState, doorId);
		if(accReaderItem != null)
		{
			return ModelUtil.copyProperties(accReaderItem, new Vis4AccReaderItem());
		}
		return null;
	}
}
