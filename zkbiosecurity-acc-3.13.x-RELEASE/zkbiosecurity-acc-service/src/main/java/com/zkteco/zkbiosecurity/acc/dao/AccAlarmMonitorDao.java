package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccAlarmMonitor;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 警报监控
 * 
 * <AUTHOR>
 * @date Created In 17:55 2019/10/17
 */
@Repository
public interface AccAlarmMonitorDao extends BaseDao<AccAlarmMonitor, String> {
    /**
     * 分页查找
     * 
     * @param status
     * @param pageable
     * @return
     */
    Page<AccAlarmMonitor> findByStatus(short status, Pageable pageable);

    /**
     * 未确认数量
     * 
     * @param status
     * @return
     */
    Long countByStatus(short status);

    /**
     * 统计有多少个时间是在统一时间产生的
     * 
     * @param eventTime
     * @return
     */
    Long countByEventTime(Date eventTime);

    /**
     * 统计某一时间之后指定状态的数量
     *
     * @param status
     * @return
     */
    int countByEventTimeGreaterThanEqualAndStatus(Date createTime, short status);

    /**
     * 统计优先级下非指定状态的数量
     *
     * @param priority
     * @param status
     * @return
     */
    int countByPriorityAndStatusNot(short priority, short status);

    default List getalarmTop5() {
        return this.createSqlQueryPage(
            "select event_name as name, count(*) as value from acc_alarm_monitor where status!=2 group by event_name order by value desc",
            0, 5);
    }

    /**
     * 根据登录用户所有区域权限，统计某一时间之后指定状态的数量
     * 
     * @param priorityCritical:
     * @param acknowledgedStatus:
     * @param areaNameList:
     * @return int
     * <AUTHOR>
     * @throws
     * @date 2022-07-21 14:08
     * @since 1.0.0
     */
    int countByPriorityAndStatusNotAndAreaNameIn(short priorityCritical, short acknowledgedStatus,
        List<String> areaNameList);

    /**
     * 根据登录用户所有区域权限，统计优先级下非指定状态的数量
     * 
     * @param weeTime:
     * @param unhandledStatus:
     * @param areaNameList:
     * @return int
     * <AUTHOR>
     * @throws
     * @date 2022-07-21 14:09
     * @since 1.0.0
     */
    int countByEventTimeGreaterThanEqualAndStatusAndAreaNameIn(Date weeTime, short unhandledStatus,
        List<String> areaNameList);

    /**
     * 根据登录用户所有区域权限，获取前5事件
     * 
     * @param areaNameList:
     * @return java.util.List
     * <AUTHOR>
     * @throws
     * @date 2022-07-21 14:40
     * @since 1.0.0
     */
    @Query(
        value = "select event_name as name, count(*) as value from acc_alarm_monitor where status!=2 and area_name in (?1) group by event_name order by value desc",
        nativeQuery = true)
    List<Object[]> getalarmTop5ByAuthFilter(List<String> areaNameList);

    /**
     * 统计各类型告警数量
     *
     * @param priorityCritical:
     * @param areaNameList:
     * @return int
     * <AUTHOR>
     * @date  2024/3/7 14:11
     * @since 1.0.0
     */
    int countByPriorityAndAreaNameIn(short priorityCritical, List<String> areaNameList);

    /**
     * 统计各类型告警数量
     *
     * @param priorityCritical:
     * @return int
     * <AUTHOR>
     * @date  2024/3/7 14:25
     * @since 1.0.0
     */
    int countByPriority(short priorityCritical);
}
