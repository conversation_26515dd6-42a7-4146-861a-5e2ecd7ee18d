/**
 * File Name: AccLinkageMedia Created by GenerationTools on 2018-03-16 下午04:41 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.acc.model.AccLinkageMedia;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 对应百傲瑞达 AccLinkageMediaDao
 * 
 * <AUTHOR>
 * @date: 2018-03-16 下午04:41
 * @version v1.0
 */
public interface AccLinkageMediaDao extends BaseDao<AccLinkageMedia, String> {

    List<AccLinkageMedia> findByAccLinkage_Id(String linkageId);

    @Query(value = "SELECT t.mediaContent FROM AccLinkageMedia t WHERE t.accLinkage.id=?1 and t.mediaType=?2")
    List<String> getMediaContentByAccLinkageIdAndMediaType(String linkageId, Short mediaType);

    List<AccLinkageMedia> findByAccLinkage_IdAndMediaTypeNot(String linkageId, Short mediaType);

    List<AccLinkageMedia> findByMediaContentInAndMediaType(List<String> strToList, short mediaType);

    @Query(
        value = "SELECT t FROM AccLinkageMedia t WHERE t.accLinkage.id in (select e.accLinkage.id from AccLinkageMedia e where e.mediaContent in (?1) and e.mediaType = ?2) and t.mediaType = ?3")
    List<AccLinkageMedia> getVdbLinkageMedias(List<String> ivrIds, Short mediaType, Short vdbType);

    @Query(
        value = "SELECT t FROM AccLinkageMedia t WHERE t.accLinkage.id in (select e.accLinkage.id from AccLinkageMedia e where e.mediaContent in (?1) and e.mediaType = ?3) and (t.mediaType = ?3 or t.mediaType = ?2)")
    List<AccLinkageMedia> getVdbLinkageMediasByParam(List<String> extensionIdList, Short ivrType, Short extensionType);
}