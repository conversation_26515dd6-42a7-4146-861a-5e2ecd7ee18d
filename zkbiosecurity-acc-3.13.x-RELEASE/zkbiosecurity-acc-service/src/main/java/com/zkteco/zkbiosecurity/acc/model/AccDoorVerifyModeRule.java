package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;

@Entity
@Table(name = "ACC_DOOR_VERIFYMODERULE")
@Getter
@Setter
@Accessors(chain=true)
public class AccDoorVerifyModeRule extends BaseModel implements Serializable{

    @ManyToOne
    @JoinColumn(name = "ACC_DOOR_ID", nullable = false)
    private AccDoor accDoor;

    @ManyToOne
    @JoinColumn(name = "ACC_VERIFYMODERULE_ID", nullable = false)
    private AccVerifyModeRule accVerifyModeRule;

}
