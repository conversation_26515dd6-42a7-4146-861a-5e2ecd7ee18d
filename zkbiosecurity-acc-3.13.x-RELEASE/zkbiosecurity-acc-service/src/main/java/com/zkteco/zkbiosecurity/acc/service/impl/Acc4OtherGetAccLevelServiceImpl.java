package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.Acc4OtherGetAccLevelService;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.vo.AccLevelDoorItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.List;

/**
 * 其他模块获取权限组接口
 *
 * <AUTHOR>
 * @date 2021/6/8 13:48
 * @since 1.0.0
 */
@Service
@Transactional
public class Acc4OtherGetAccLevelServiceImpl implements Acc4OtherGetAccLevelService {
    @Autowired
    private AccLevelService accLevelService;

    @Override
    public List<String> getDoorIdsByLevelIds(List<String> levelIds) {
        List<String> doorIds = null;
        if (!CollectionUtil.isEmpty(levelIds)){
            doorIds = new ArrayList<>();
            for (String levelId : levelIds) {
                // 根据权限组id查询权限组-门中间表信息
                List<AccLevelDoorItem> accLevelDoorItems = accLevelService.getLevelDoorItemsByLevelId(levelId);
                if (!CollectionUtil.isEmpty(accLevelDoorItems)){
                    doorIds.addAll(CollectionUtil.getPropertyList(accLevelDoorItems, AccLevelDoorItem::getAccDoorId, "-1"));
                }
            }
        }
        return doorIds;
    }

    @Override
    public List<String> getLevelIdsByDoorIds(List<String> doorIds) {
        List<String> levelIds = null;
        if (!CollectionUtil.isEmpty(doorIds)){
            levelIds = new ArrayList<>();
            for (String doorId : doorIds) {
                // 根据门id获取该门所在的权限组id集合
                levelIds.addAll(accLevelService.getLevelIdByDoorId(doorId));
            }
        }
        return levelIds;
    }
}
