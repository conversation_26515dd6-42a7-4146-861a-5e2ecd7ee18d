package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.dashboard.service.Dashboard4OtherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/1/4 11:44
 * @since 1.0.0
 */
@Component
public class AccDashboardExtServiceImpl implements Dashboard4OtherService {

    @Autowired
    AccDeviceService accDeviceService;

    @Override
    public long getDeviceCount() {
        return accDeviceService.getDevicesCount();
    }
}
