package com.zkteco.zkbiosecurity.acc.data.upgrade;

import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/10/20 14:52
 * @since 1.0.0
 */
@Slf4j
@Component
public class AccVer3_6_0 implements UpgradeVersionManager {

    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public String getVersion() {
        return "v3.6.0";
    }

    @Override
    public String getModule() {
        return BaseConstants.ACC;
    }

    @Override
    public boolean executeUpgrade() {

        // 个人敏感信息保护-抓拍照片（true启用、false禁用、默认启用）
        baseSysParamService.initData(new BaseSysParamItem("acc.capturePhoto.encryptProp", "true", "个人敏感信息保护-抓拍照片"));

        return false;
    }
}
