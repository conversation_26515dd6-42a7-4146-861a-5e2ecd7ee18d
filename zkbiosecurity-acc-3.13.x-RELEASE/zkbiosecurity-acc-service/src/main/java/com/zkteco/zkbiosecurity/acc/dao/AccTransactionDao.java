package com.zkteco.zkbiosecurity.acc.dao;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.acc.model.AccTransaction;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 对应百傲瑞达 AccTransactionDao
 * 
 * <AUTHOR>
 * @date: 2018-03-07 16:21:30
 * @version v1.0
 */
public interface AccTransactionDao extends BaseDao<AccTransaction, String> {

    @Modifying
    @Query(value = "DELETE FROM AccTransaction WHERE eventTime >= ?1")
    void deleteAllByToday(Date todayBeginTime);

    @Modifying
    @Query(
        value = "DELETE FROM AccTransaction WHERE eventNo >= 20 AND eventNo < 200 OR eventNo >= 500 AND eventNo < 4000 OR eventNo >= 5000")
    void deleteAllExceptionData();

    @Modifying
    @Query(
        value = "DELETE FROM AccTransaction WHERE eventNo >= 20 and eventNo < 200 OR eventNo >= 500 and eventNo < 1000 OR eventNo >= 2000 and eventNo < 4000")
    void deleteAllExceptionDataExpand();

    @Query(value = "select max(e.logId) from AccTransaction e where e.logId > 0 and e.devSn = ?1")
    Integer getMaxLogId(String devSn);

    @Query(value = "select min(e.logId) from AccTransaction e where e.logId > 0 and e.devSn = ?1")
    Integer getMinLogId(String devSn);

    @Query(value = "select count(e.logId) from AccTransaction e where e.logId > ?2 and e.devSn = ?1")
    Integer getLogIdCount(String devSn, int index);

    @Query(value = "select e.uniqueKey from AccTransaction e where e.devSn in (?1) and e.logId >= ?2")
    List<String> getUniqueBySnAndLogId(List<String> snList, Integer logId);

    @Query(value = "select e.uniqueKey from AccTransaction e where e.devSn in (?1) and e.logId >= ?2")
    List<String> getUniqueBySnAndLogIdOfAllEventNo(List<String> snList, Integer logId);

    @Query(value = "select e.uniqueKey from AccTransaction e where e.devSn = ?1 and e.eventTime >= ?2")
    List<String> getUniqueBySnAndEventTime(String sn, Date eventTime);

    @Deprecated
    @Query(
        value = "select count(id) from AccTransaction t where t.devSn = ?1 and t.eventAddr = ?2 and t.eventTime >= ?3 and t.eventTime <= ?4 and (t.pin !='' and t.pin is not null) and t.eventNo not in ('6','300') and t.eventPointType = 0 ")
    int countByDeviceSnAndDoorNo(String deviceSn, Short doorNo, Date startDatetime, Date endDatetime);

    @Deprecated
    @Query(
        value = "select t from AccTransaction t where t.devSn = ?1 and t.eventAddr = ?2 and t.eventTime >= ?3 and t.eventTime <= ?4 and (t.pin !='' and t.pin is not null) and t.eventNo not in ('6','300') and t.eventPointType = 0 ")
    List<AccTransaction> getTransactionsForAtt(String deviceSn, Short doorNo, Date startDatetime, Date endDatetime);

    @Query(value = "select t from AccTransaction t where t.pin !='' and t.areaName in ?1 order by t.eventTime desc")
    List<AccTransaction> getLast5Transaction(List<String> areaName, Pageable pageable);

    /**
     * 通过uniqueKey值IN查询出门禁记录
     * 
     * <AUTHOR>
     * @Date 2018/12/24 10:14
     * @param uniqueKeys
     * @return
     */
    List<AccTransaction> findByUniqueKeyIn(Collection<String> uniqueKeys);

    /**
     * 面板-门禁模块-事件趋势：根据时间获取事件记录的事件时间和数量
     * 
     * <AUTHOR>
     * @since 2019-01-09 17:37
     * @Param [startDate, endDate]
     * @return
     */
    @Query(
        value = "select count(id), t.eventTime from AccTransaction t where t.eventTime >= ?1 and t.eventTime <= ?2 GROUP BY t.eventTime")
    List<Object[]> findByEventTime(Date startDate, Date endDate);

    /**
     * 统计各异常事件数
     * 
     * <AUTHOR>
     * @Date 2019/1/4 17:22
     * @param
     * @return 下标 0:为 EVENT_NAME事件名称 1：为 count(t.EVENT_NO)事件数
     */
    @Query(
        value = "SELECT t.EVENT_NAME, count(t.EVENT_NO) FROM ACC_TRANSACTION t WHERE (t.EVENT_NO >= 20 and t.EVENT_NO < 200 OR t.EVENT_NO >= 500) GROUP BY t.EVENT_NO,t.EVENT_NAME ORDER BY count(t.EVENT_NO) DESC",
        nativeQuery = true)
    List<Object[]> getExceptionEventCount();

    /**
     * 统计正常、异常、报警事件数
     * 
     * <AUTHOR>
     * @Date 2019/1/4 17:22
     * @param
     * @return 下标 0:为 event_level 1：为 count
     */
    @Query(value = "SELECT t.event_no, count(t.id) FROM acc_transaction t GROUP BY t.event_no", nativeQuery = true)
    List<Object[]> getEventCount();

    /**
     * 查询指定事件事件之后的事情
     * 
     * @param eventTime
     * @return
     */
    List<AccTransaction> findByEventTimeAfter(Date eventTime, Pageable pageable);

    /**
     * 根据条件获取事件记录数
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-08-11 10:10
     * @param devSn
     * @param startCreateTime
     * @param endCreateTime
     * @param inEventNo
     * @return java.lang.Long
     */
    @Query(
        value = "select count(id) from AccTransaction t where t.devSn = ?1 and t.createTime >= ?2 and t.createTime <= ?3 and t.eventNo in (?4)")
    Long countByCondition(String devSn, Date startCreateTime, Date endCreateTime, List<Short> inEventNo);

    /**
     * 根据条件获取事件记录数
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-08-12 9:38
     * @param devSn
     * @param endCreateTime
     * @param inEventNo
     * @return java.lang.Long
     */
    @Query(
        value = "select count(id) from AccTransaction t where t.devSn = ?1 and t.createTime <= ?2 and t.eventNo in (?3)")
    Long countByProperties(String devSn, Date endCreateTime, List<Short> inEventNo);

    /**
     * 根据创建时间获取记录
     * 
     * @param createTime:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.model.AccTransaction>
     * <AUTHOR>
     * @throws
     * @date 2022-05-12 17:55
     * @since 1.0.0
     */
    @Query(
        value = "select t.* from acc_transaction t where t.create_time >= ?1 and t.pin !='' and t.pin is not null and t.event_no not in ('6','300') order by t.event_time desc",
        nativeQuery = true)
    List<AccTransaction> getTransactionByCreateTime(Date createTime);

    @Query(
        value = "select t.* from acc_transaction t where t.create_time >= ?1 and t.pin is not null and t.event_no not in ('6','300') order by t.event_time desc",
        nativeQuery = true)
    List<AccTransaction> getTransactionByCreateTimeInOracle(Date createTime);

    @Query(
        value = "select t from AccTransaction t where t.pin is not null and t.areaName in ?1 order by t.eventTime desc")
    List<AccTransaction> getLast5TransactionWhenOracle(List<String> areaName, Pageable pageable);

    // 根据pin和出厂记录查找对应的人员，用于迟到异常记录的填充
    @Query(value = "SELECT * FROM acc_transaction  WHERE pin=?1 and    EXTRACT(EPOCH FROM event_time AT TIME ZONE 'Asia/Shanghai' AT TIME ZONE 'UTC') * 1000  = ?2 limit 1", nativeQuery = true)
    AccTransaction getItemByPinAndExitTime(String pin, long EXIT_TIME);




}
