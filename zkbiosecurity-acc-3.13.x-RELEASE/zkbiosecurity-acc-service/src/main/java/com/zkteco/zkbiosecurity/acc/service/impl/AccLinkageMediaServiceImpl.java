/**
 * File Name: AccLinkageMediaServiceImpl Created by GenerationTools on 2018-03-16 下午04:41 Copyright:Copyright ©
 * 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.dao.AccLinkageMediaDao;
import com.zkteco.zkbiosecurity.acc.model.AccLinkageMedia;
import com.zkteco.zkbiosecurity.acc.service.AccLinkageMediaService;
import com.zkteco.zkbiosecurity.acc.vo.AccLinkageMediaItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;

/**
 * 对应百傲瑞达 AccLinkageMediaServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-16 下午04:41
 * @version v1.0
 */
@Service
@Transactional
public class AccLinkageMediaServiceImpl implements AccLinkageMediaService {
    @Autowired
    private AccLinkageMediaDao accLinkageMediaDao;

    @Override
    public AccLinkageMediaItem saveItem(AccLinkageMediaItem item) {
        AccLinkageMedia accLinkageMedia = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accLinkageMediaDao.findById(id)).orElse(new AccLinkageMedia());

        ModelUtil.copyProperties(item, accLinkageMedia);
        accLinkageMediaDao.save(accLinkageMedia);
        item.setId(accLinkageMedia.getId());
        return item;
    }

    @Override
    public List<AccLinkageMediaItem> getByCondition(AccLinkageMediaItem condition) {
        return (List<AccLinkageMediaItem>)accLinkageMediaDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accLinkageMediaDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accLinkageMediaDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AccLinkageMediaItem getItemById(String id) {
        List<AccLinkageMediaItem> items = getByCondition(new AccLinkageMediaItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public List<AccLinkageMediaItem> getItemsByCondition(String contactIds, short mediaType) {
        List<AccLinkageMedia> accLinkageMediaList =
            accLinkageMediaDao.findByMediaContentInAndMediaType(StrUtil.strToList(contactIds), mediaType);
        List<AccLinkageMediaItem> accLinkageMediaItems = null;
        if (accLinkageMediaList != null && accLinkageMediaList.size() > 0) {
            accLinkageMediaItems = ModelUtil.copyListProperties(accLinkageMediaList, AccLinkageMediaItem.class);
        }
        return accLinkageMediaItems;
    }

}