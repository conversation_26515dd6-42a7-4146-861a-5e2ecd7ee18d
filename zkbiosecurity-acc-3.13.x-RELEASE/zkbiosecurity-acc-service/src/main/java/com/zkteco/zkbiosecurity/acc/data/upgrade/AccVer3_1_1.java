package com.zkteco.zkbiosecurity.acc.data.upgrade;

import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.jdbc.JdbcOperateTemplate;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/8/5 9:40
 * @since 1.0.0
 */
@Slf4j
@Component
public class AccVer3_1_1 implements UpgradeVersionManager {
    @Autowired
    private JdbcOperateTemplate jdbcOperateTemplate;

    @Override
    public String getModule() {
        return BaseConstants.ACC;
    }

    @Override
    public String getVersion() {
        return "v3.1.1";
    }

    @Override
    public boolean executeUpgrade() {
        // 表字段升级
        alterTableCharLength();
        return true;
    }

    private void alterTableCharLength() {
        jdbcOperateTemplate.alterTableCharLen("ACC_TRANSACTION", "VERIFY_MODE_NAME", "500");
    }
}
