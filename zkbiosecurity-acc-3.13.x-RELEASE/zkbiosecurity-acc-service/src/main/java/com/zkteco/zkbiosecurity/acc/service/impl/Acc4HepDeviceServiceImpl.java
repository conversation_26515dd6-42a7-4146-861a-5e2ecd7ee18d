package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.service.Acc4HepDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;

/**
 * <AUTHOR>
 * @date 2020-08-07 10:11
 */
@Service
@Transactional
public class Acc4HepDeviceServiceImpl implements Acc4HepDeviceService {
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;

    @Override
    public List<String> hepGetAccDevSnList() {
        return accDeviceOptionService.getDevSnByOptNames("MaskDetectionFunOn", "IRTempDetectionFunOn");
    }
}
