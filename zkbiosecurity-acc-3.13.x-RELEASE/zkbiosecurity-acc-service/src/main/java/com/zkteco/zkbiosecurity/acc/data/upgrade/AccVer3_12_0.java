package com.zkteco.zkbiosecurity.acc.data.upgrade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.system.constants.BaseDataConstants;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2024/7/2 9:05
 * @since 1.0.0
 */
@Slf4j
@Component
public class AccVer3_12_0 implements UpgradeVersionManager {
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AuthPermissionService authPermissionService;

    @Override
    public String getModule() {
        return BaseConstants.ACC;
    }

    @Override
    public String getVersion() {
        return "v3.12.0";
    }

    @Override
    public boolean executeUpgrade() {
        AuthPermissionItem systemItem = authPermissionService.getItemByCode("Acc");
        if (systemItem != null) {
            AuthPermissionItem subMenuItem = new AuthPermissionItem("AccEncryptProp", "common_param_infoProtection", "acc:encryptProp",
                    AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 999);
            subMenuItem.setParentId(systemItem.getId());
            subMenuItem = authPermissionService.initData(subMenuItem);

            AuthPermissionItem subButtonItem = new AuthPermissionItem("AccPinEncryptProp", "pers_person_pin", "acc:pin:encryptProp",
                    AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 1);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);

            subButtonItem = new AuthPermissionItem("AccNameEncryptProp", "pers_person_wholeName", "acc:name:encryptProp",
                    AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 2);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);

            subButtonItem = new AuthPermissionItem("AccCardNoEncryptProp", "pers_card_cardNo", "acc:cardNo:encryptProp",
                    AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 3);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);

            subButtonItem = new AuthPermissionItem("AccMobilePhoneEncryptProp", "pers_person_mobilePhone",
                    "acc:mobilePhone:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 4);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);

            subButtonItem = new AuthPermissionItem("AccHeadPortraitEncryptProp", "pers_person_photo",
                    "acc:headPortrait:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 5);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);

            subButtonItem = new AuthPermissionItem("AccCapturePhotoEncryptProp", "pers_capture_catchPhoto",
                    "acc:capturePhoto:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 6);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);
        }

        BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("accReportDataClean");
        if (StringUtils.isNotBlank(baseSysParamItem.getId())) {
            String paramValue = baseSysParamItem.getParamValue();
            JSONObject json = JSON.parseObject(paramValue);
            json.put("keptPhoto", "15");
            json.put("keptPhotoType", BaseDataConstants.DATA_CLEAN_KEPTMONTH);
            baseSysParamItem.setParamValue(json.toString());
            baseSysParamService.saveItem(baseSysParamItem);
        }

        updateAccMessageNotification();

        return true;
    }

    /**
     * 门禁事件记录app推送
     * 
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-09-06 15:05
     * @since 1.0.0
     */
    private void updateAccMessageNotification() {
        BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("accMessageNotification");
        if (StringUtils.isNotBlank(baseSysParamItem.getId())) {
            String value = baseSysParamItem.getParamValue();
            JSONObject notificationValues = JSON.parseObject(value);
            notificationValues.put("APP", "1");
            baseSysParamItem.setParamValue(notificationValues.toString());
            baseSysParamService.saveItem(baseSysParamItem);
        }
    }
}
