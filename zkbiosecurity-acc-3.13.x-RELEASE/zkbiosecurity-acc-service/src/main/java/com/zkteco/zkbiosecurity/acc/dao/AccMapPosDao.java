/**
 * File Name: AccMapPos
 * Created by GenerationTools on 2018-03-20 下午02:07
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.acc.model.AccMapPos;

/**
 * 对应百傲瑞达 BaseMapPosDao
 * <AUTHOR>
 * @date:	2018-03-20 下午02:07
 * @version v1.0
 */
public interface AccMapPosDao extends BaseDao<AccMapPos, String> {
	
	@Query(value="SELECT ENTITY_ID FROM ACC_MAP_POS WHERE MAP_ID = ?1 AND ENTITY_TYPE = ?2 ", nativeQuery = true)
	List<String> getEntityIdByMapIdAndEntityType(String mapId, String entityType);

	@Modifying
	@Query(value = "DELETE FROM AccMapPos e WHERE e.entityId in (?1) and e.entityType = ?2")
    void delMapPosByEntityId(List<String> delIdList, String entityType);

	@Query(value="SELECT e FROM AccMapPos e WHERE e.entityType='AccDoor' AND e.entityId = (SELECT eventPointId FROM AccPersonLastAddr WHERE pin = ?1) ")
    AccMapPos getByPersonLastAddrPin(String pin);
}