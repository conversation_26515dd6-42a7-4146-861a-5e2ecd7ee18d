package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.core.convert.EncryptConverter;
import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 门禁设备model
 *
 * <AUTHOR>
 * @date: 2018-03-07 上午10:31
 * @version v1.0
 */
@Entity
@Table(name = "ACC_DEVICE")
@Getter
@Setter
@Accessors(chain = true)
public class AccDevice extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 系统区域 */
    @Column(name = "AUTH_AREA_ID")
    private String authAreaId;

    /* BusinessID 下发设备中使用 */
    @Column(name = "BUSINESS_ID", unique = true)
    private Long businessId;

    @Column(name = "SN", length = 30, nullable = false, unique = true)
    private String sn;

    @Column(name = "DEV_ALIAS", length = 100, nullable = false)
    private String alias;

    @Column(name = "FW_VERSION", length = 50)
    private String fwVersion;

    @Column(name = "DEVICE_NAME", length = 30)
    private String deviceName;

    @Column(name = "ACPANEL_TYPE", nullable = false)
    private Short acpanelType;

    @Column(name = "COMM_TYPE", nullable = false)
    private Short commType;

    @Column(name = "IP_ADDRESS", length = 15)
    private String ipAddress;

    @Column(name = "IP_PORT")
    private Integer ipPort;

    @Column(name = "SUBNET_MASK", length = 15)
    private String subnetMask;

    @Column(name = "GATEWAY", length = 15)
    private String gateway;

    @Column(name = "COM_PORT")
    private Short comPort;

    @Column(name = "BAUDRATE")
    private Integer baudrate;

    @Column(name = "COM_ADDRESS")
    private Short comAddress;

    @Convert(converter = EncryptConverter.class)
    @Column(name = "COMM_PWD")
    private String commPwd;

    @Column(name = "TIME_ZONE", length = 10)
    private String timeZone;

    @Column(name = "ENABLED", nullable = false)
    private Boolean enabled;

    @Column(name = "FOUR_TO_TWO")
    private Boolean fourToTwo;

    @Column(name = "MACHINE_TYPE", nullable = false)
    private Short machineType;

    @Column(name = "IS_REGISTRATIONDEVICE", nullable = false)
    private Boolean isRegistrationDevice;

    @Column(name = "MAC_ADDRESS", length = 30)
    private String macAddress;

    @Column(name = "ICON_TYPE")
    private Short iconType;

    @Column(name = "WG_READER_ID")
    private String wgReaderId;

    @com.zkteco.zkbiosecurity.base.annotation.Column(name = "is_push_exception")
    private String isPushException;

    @ManyToOne
    @JoinColumn(name = "DSTIME_ID")
    private AccDSTime accDSTime;

    @ManyToOne
    @JoinColumn(name = "PARENT_ID")
    private AccDevice parentDevice;

    @OneToMany(mappedBy = "parentDevice")
    private List<AccDevice> childDevices = new ArrayList<AccDevice>();

    @OneToMany(mappedBy = "accDevice", cascade = CascadeType.REMOVE)
    private List<AccAuxIn> accAuxInList = new ArrayList<AccAuxIn>();

    @OneToMany(mappedBy = "accDevice", cascade = CascadeType.REMOVE)
    private List<AccAuxOut> accAuxOutList = new ArrayList<AccAuxOut>();

    @OneToMany(mappedBy = "accDevice")
    private List<AccInterlock> accInterlockList = new ArrayList<AccInterlock>();

    @OneToMany(mappedBy = "accDevice")
    private List<AccAntiPassback> accAntiPassbackList = new ArrayList<AccAntiPassback>();

    @OneToMany(mappedBy = "accDevice", cascade = CascadeType.REMOVE)
    private List<AccDeviceEvent> accDeviceEventList = new ArrayList<AccDeviceEvent>();

    @OneToMany(mappedBy = "accDevice", cascade = CascadeType.REMOVE)
    private List<AccDeviceOption> accDeviceOptionList = new ArrayList<AccDeviceOption>();

    @OneToMany(mappedBy = "device", cascade = CascadeType.REMOVE)
    private List<AccDoor> accDoorList = new ArrayList<AccDoor>();

    @OneToMany(mappedBy = "accDevice", cascade = CascadeType.REMOVE)
    private List<AccDeviceVerifyMode> accDeviceVerifyModeList = new ArrayList<AccDeviceVerifyMode>();

    @OneToMany(mappedBy = "accDevice", cascade = CascadeType.REMOVE)
    private Set<AccLinkage> accLinkageSet = new HashSet<AccLinkage>();

    @OneToMany(mappedBy = "accDevice", cascade = CascadeType.REMOVE)
    private Set<AccLinkageIndex> linkageIndex = new HashSet<AccLinkageIndex>();

}
