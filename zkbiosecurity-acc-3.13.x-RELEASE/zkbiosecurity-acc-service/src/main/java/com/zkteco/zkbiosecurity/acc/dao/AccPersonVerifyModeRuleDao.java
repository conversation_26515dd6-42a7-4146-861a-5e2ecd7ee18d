package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccPersonVerifyModeRule;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface AccPersonVerifyModeRuleDao extends BaseDao<AccPersonVerifyModeRule, String> {

    @Modifying
    @Query("delete from AccPersonVerifyModeRule where accVerifyModeRule.id in ?1 and persPersonId in ?2")
    void delPersonByRuleIdAndPersId(List<String> verifyModeRuleGroupId, List<String> personIds);

    /**
     * 查询出验证方式规则人员组已经存在的人员ID
     * @author: mingfa.zheng
     * @date: 2018/4/24 15:53
     * @return:
     */
    @Query("select persPersonId from AccPersonVerifyModeRule")
    List<String> getPersonIdByCombPersonId();

    void deleteAccPersonVerifyModeRuleByPersPersonIdIn(List<String> personIds);

    @Query("select e.persPersonId from AccPersonVerifyModeRule e where e.accVerifyModeRule.id = ?1")
    List<String> getPersonIdByAccVerifyModeRuleId(String verifyModeRuleId);
}
