package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.dao.AccExceptionRecordDao;
import com.zkteco.zkbiosecurity.acc.dao.AccTransactionDao;
import com.zkteco.zkbiosecurity.acc.model.AccExceptionRecord;
import com.zkteco.zkbiosecurity.acc.model.AccTransaction;
import com.zkteco.zkbiosecurity.acc.service.AccExceptionRecordService;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.service.WechatService;
import com.zkteco.zkbiosecurity.acc.vo.AccExceptionRecordItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.att.service.AttGetAccTransactionService;
import com.zkteco.zkbiosecurity.att.vo.Att4AccTransaction;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.dao.ext.SqlParser;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 门禁门当考勤获取门禁记录
 * 
 * <AUTHOR>
 * @date 2018-11-19 20:12
 */
@Service
@Transactional
@Slf4j
public class Att4AccTransactionServiceImpl implements AttGetAccTransactionService {
    @Autowired
    AccExceptionRecordService  accExceptionRecordService;
    @Autowired
    AccExceptionRecordDao accExceptionRecordDao;
    @Autowired
    private AccTransactionService accTransactionService;
    @Autowired
    private AccTransactionDao accTransactionDao;
    @Autowired
    WechatService wechatService;
    @Autowired
    PersPersonService persPersonService;

    @Override
    public Pager getAccTransactionList(String devSn, Short doorNo, Date startDatetime, Date endDatetime, int page,
            int size) {
        Pager pager = accTransactionService.getAccTransactionBySnAndDoorNo(devSn, doorNo, startDatetime, endDatetime,
                page, size);
        List<AccTransactionItem> items = (List<AccTransactionItem>) pager.getData();
        List<Att4AccTransaction> accTransactions = new ArrayList<>();
        Att4AccTransaction att4AccTransaction = null;
        for (AccTransactionItem item : items) {
            att4AccTransaction = new Att4AccTransaction();
            accTransactions.add(ModelUtil.copyProperties(item, att4AccTransaction));
        }
        pager.setData(accTransactions);
        return pager;
    }

    @Override
    public int getAccTransactionCount(String devSn, Short doorNo, Date startDatetime, Date endDatetime) {
        return accTransactionService.countByDeviceSnAndDoorNo(devSn, doorNo, startDatetime, endDatetime);
    }

    @Override
    public Pager getAccTransactionList(String doorIds, Date startDatetime, Date endDatetime, int page, int size) {
        Pager pager = accTransactionService.getAccTransactionByDoorIds(doorIds, startDatetime, endDatetime, page, size);
        List<AccTransactionItem> accTransactions = (List<AccTransactionItem>) pager.getData();
        List<Att4AccTransaction> att4AccTransactions = buildAtt4AccTransaction(accTransactions);
        pager.setData(att4AccTransactions);
        return pager;
    }

    @Override
    public int getAccTransactionCount(String doorIds, Date startDatetime, Date endDatetime) {
        AccTransactionItem condition = new AccTransactionItem();
        condition.setStartTime(startDatetime);
        condition.setEndTime(endDatetime);
        condition.setInEventPointId(doorIds);
        String sql = accTransactionService.getSqlByPinNotNull(condition);
        String countSql = new SqlParser().getSmartCountSql(sql);
        List<Object[]> countList = accTransactionDao.getArrayData(countSql);
        int count = 0;
        if (countList != null && countList.size() > 0 && Objects.nonNull(countList.get(0)[0])) {
            count = Integer.parseInt(countList.get(0)[0] + "");
        }
        return count;
    }

    @Override
    public Integer pullAccTransactionCount(String doorIds, Date startCreateTime, Date endCreateTime) {
        AccTransactionItem condition = new AccTransactionItem();
        condition.setStartCreateTime(startCreateTime);
        condition.setEndCreateTime(endCreateTime);
        condition.setInEventPointId(doorIds);
        String sql = accTransactionService.getSqlByPinNotNull(condition);
        String countSql = new SqlParser().getSmartCountSql(sql);
        List<Object[]> countList = accTransactionDao.getArrayData(countSql);
        int count = 0;
        if (countList != null && countList.size() > 0 && Objects.nonNull(countList.get(0)[0])) {
            count = Integer.parseInt(countList.get(0)[0] + "");
        }
        return count;
    }

    @Override
    public Pager pullAccTransactionList(String doorIds, Date startCreateTime, Date endCreateTime, int page, int size) {
        Pager pager = accTransactionService.getAccTransactionByDoorIdsAndCreateTime(doorIds, startCreateTime,
                endCreateTime, page, size);
        List<AccTransactionItem> accTransactions = (List<AccTransactionItem>) pager.getData();
        List<Att4AccTransaction> att4AccTransactions = buildAtt4AccTransaction(accTransactions);
        pager.setData(att4AccTransactions);
        return pager;
    }

    @Override
    public void pushAttLateToAcc(String pin, Date attDate,String timeSlotToWorkTime) {

        // 查找门禁记录，不查异常记录表
        AccTransaction accTransaction = accTransactionDao.getItemByPinAndExitTime(pin, attDate.getTime());

        PersPersonItem personItem = persPersonService.getItemByPin(pin);
        // 新增一条迟到异常记录。
        AccExceptionRecordItem accExceptionRecordItem = new AccExceptionRecordItem();
        accExceptionRecordItem.setSubject("2");
        accExceptionRecordItem.setId("");
        accExceptionRecordItem.setSendTime(new Date());
        accExceptionRecordItem.setStatus((short) 1);
        accExceptionRecordItem.setPin(pin);
        accExceptionRecordItem.setName(personItem.getName());
        accExceptionRecordItem.setDeptName(personItem.getDeptName());
        accExceptionRecordItem.setDeptCode(personItem.getName());
        accExceptionRecordItem.setReceiverPosition("组长");
        accExceptionRecordItem.setSn(accTransaction.getDevSn());
        accExceptionRecordItem.setReaderName(accTransaction.getReaderName());
        accExceptionRecordItem.setErrorMessage("上班时间段 "+timeSlotToWorkTime+"迟到");
        accExceptionRecordItem.setEnterTime(attDate);
        AccExceptionRecordItem accExceptionRecordItem1= accExceptionRecordService.saveItem(accExceptionRecordItem);
        accExceptionRecordItem.setId(accExceptionRecordItem.getId());
        String msg = accTransactionService.pushWechat("2", pin, accExceptionRecordItem);
        if (!"ok".equals(msg)) {
            accExceptionRecordItem.setStatus((short) 2);// 是
            log.info(msg);
        }
        accExceptionRecordItem.setPushErrorMessage(msg);
        accExceptionRecordService.saveItem(accExceptionRecordItem);
    }

    /**
     * 组装考勤VO对象信息
     *
     * @param accTransactions:门禁事件
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.Att4AccTransaction>
     * <AUTHOR>
     * @date 2021-06-16 10:31
     * @since 1.0.0
     */
    private List<Att4AccTransaction> buildAtt4AccTransaction(List<AccTransactionItem> accTransactions) {
        List<Att4AccTransaction> att4AccTransactions = new ArrayList<>();
        if (accTransactions != null && accTransactions.size() > 0) {
            for (AccTransactionItem item : accTransactions) {
                if (StringUtils.isNotBlank(item.getPin())) {
                    Att4AccTransaction transaction = new Att4AccTransaction();
                    transaction.setDevId(item.getDevId());
                    transaction.setDevSn(item.getDevSn());
                    transaction.setEventTime(item.getEventTime());
                    transaction.setPin(item.getPin());
                    transaction.setEventPointId(item.getEventPointId());
                    transaction.setTransactionPhoto(item.getCapturePhotoPath());
                    transaction.setCreateTime(item.getCreateTime());
                    att4AccTransactions.add(transaction);
                }
            }
        }
        return att4AccTransactions;
    }
}
