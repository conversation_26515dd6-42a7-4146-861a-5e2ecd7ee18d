package com.zkteco.zkbiosecurity.acc.license;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccLicensePointsCheckService;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.license.provider.BaseLicenseProvider;
import com.zkteco.zkbiosecurity.license.vo.bean.ResultCode;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2021/9/6 10:12
 * @since 1.0.0
 */
@Slf4j
@Component
public class AccLicensePointsCheck implements AccLicensePointsCheckService {

    @Value("${system.productCode}")
    private String productCode;

    @Autowired
    private BaseLicenseProvider baseLicenseProvider;
    @Autowired
    private AccDeviceService accDeviceService;

    @Override
    public ZKResultMsg check() {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        // 盒子点数校验
        ResultCode resultCode = baseLicenseProvider.isCountOutDevTotal();
        if (!ResultCode.SUCCESS.equals(resultCode)) {
            zkResultMsg.setRet("fail");
            zkResultMsg.setMsg("common_license_maxCount");
            log.warn("acc license points over");
            return zkResultMsg;
        }
        return zkResultMsg;
    }

    @Override
    public boolean update() {
        if (isZKBioCVV6000Server()) {
            return baseLicenseProvider.updateModuleControlCount(ConstUtil.SYSTEM_MODULE_ACC);
        }
        return true;
    }

    @Override
    public boolean isZKBioCVV6000Server() {
        return BaseConstants.ZKBIOCV_SECURITY_FOUNDATION.equals(baseLicenseProvider.getProductCode())
            || BaseConstants.ZKBIOCV_SERVER_V6000.equals(baseLicenseProvider.getProductCode());
    }
}
