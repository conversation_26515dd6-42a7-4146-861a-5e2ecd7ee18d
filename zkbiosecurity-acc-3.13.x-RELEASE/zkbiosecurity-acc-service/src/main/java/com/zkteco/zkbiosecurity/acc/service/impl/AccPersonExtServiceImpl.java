package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.dao.*;
import com.zkteco.zkbiosecurity.acc.model.*;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.service.AccProExtService;
import com.zkteco.zkbiosecurity.acc.service.AccTimeSegService;
import com.zkteco.zkbiosecurity.acc.utils.AccDataUtil;
import com.zkteco.zkbiosecurity.acc.utils.AccDeviceUtil;
import com.zkteco.zkbiosecurity.acc.utils.AccLevelUtil;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.cmd.sec.constants.CmdSecConstants;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersCardService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonExtService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.*;

/**
 * 实现人事调用其他业务模块实现类
 */
@Service
@Transactional
public class AccPersonExtServiceImpl implements PersPersonExtService {
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccLevelPersonDao accLevelPersonDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccPersonDao accPersonDao;
    @Autowired
    private AccPersonCombOpenPersonDao accPersonCombOpenPersonDao;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private AccLevelDao accLevelDao;
    @Autowired
    private AccLevelDoorDao accLevelDoorDao;
    @Autowired
    private PersCardService persCardService;
    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AccTimeSegService accTimeSegService;
    @Autowired(required = false)
    private AccProExtService accProExtService;
    /**
     * 是否支持权限组时间段功能
     */
    @Value("${system.levelTime.support:false}")
    private boolean isSupportLevelTime;

    @Override
    public void deletePersonBioTemplate(Collection<String> personIds, Collection<Short> bioTemplateTypes) {
        List<String> personIdList = new ArrayList<>(personIds);
        List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonIdIn(personIdList);
        if (levelIdList != null && levelIdList.size() > 0) {
            List<AccDevice> deviceList = accDeviceDao.getDevByLevel(levelIdList);
            if (deviceList != null && deviceList.size() > 0) {
                AccDeviceUtil.moveUpParentDevList(deviceList);
                List<String> pinList = persPersonService.getPinsByIds(personIdList);
                for (AccDevice accDevice : deviceList) {
                    for (Short bioType : bioTemplateTypes) {
                        accDevCmdManager.delPersonBioTemplateByTypeFromDev(accDevice.getSn(), pinList, bioType, false);
                        // 可见光人脸、可见光手掌还需同时删除比对照片
                        if (BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE.equals(bioType)
                            || PersConstants.PALM_BIO_TYPE_10.equals(bioType)) {
                            accDevCmdManager.delBiophotoByPinsAndType(accDevice.getSn(), pinList, bioType, false);
                        }
                    }
                }
            }
        }
    }

    @Override
    public void setOtherPersonInfo2Dev(Collection<PersPersonInfo2OtherItem> items, String module) {
        // 自己模块的数据不处理
        if (ConstUtil.SYSTEM_MODULE_ACC.equals(module)) {
            return;
        }
        List<String> personIds =
            (List<String>)CollectionUtil.getPropertyList(items, PersPersonInfo2OtherItem::getPersonId, "-1");
        List<AccPerson> accPersonList = accPersonDao.findByPersonIdIn(personIds);
        Map<String, AccPerson> accPersonMap = CollectionUtil.listToKeyMap(accPersonList, AccPerson::getPersonId);
        List<AccPersonCombOpenPerson> accPersonCombOpenPersonAllList =
            accPersonCombOpenPersonDao.findByPersPersonIdIn(personIds);
        Map<String, AccPersonCombOpenPerson> accPersonCombOpenPersonMap =
            CollectionUtil.listToKeyMap(accPersonCombOpenPersonAllList, AccPersonCombOpenPerson::getPersPersonId);
        for (PersPersonInfo2OtherItem item : items) {
            List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonId(item.getPersonId());
            if (levelIdList != null && levelIdList.size() > 0) {
                List<AccDevice> deviceList = accDeviceDao.getDevByLevel(levelIdList);
                if (deviceList != null && deviceList.size() > 0) {
                    AccPersonOptItem persOptItem = new AccPersonOptItem();
                    persOptItem.setPin(item.getPin());
                    persOptItem.setName(item.getName());
                    persOptItem.setLastName(item.getLastName());
                    persOptItem.setCardNo(item.getCardNo());
                    if (StringUtils.isNotBlank(persOptItem.getCardNo())) {
                        persOptItem.setCardState(PersConstants.CARD_VALID);
                    }
                    persOptItem.setPersonPwd(item.getPersonPwd());
                    AccPerson accPerson = accPersonMap.get(item.getPersonId());
                    if (accPerson != null) {
                        persOptItem.setStartTime(accPerson.getStartTime());
                        persOptItem.setEndTime(accPerson.getEndTime());
                        persOptItem.setSuperAuth(accPerson.getSuperAuth());
                        persOptItem.setDisabled(accPerson.getDisabled());
                        persOptItem.setPrivilege(accPerson.getPrivilege());
                        persOptItem.setDelayPassage(accPerson.getDelayPassage() != null
                            && ("true".equals(accPerson.getDelayPassage().toString())));
                    } else {
                        persOptItem.setStartTime(null);
                        persOptItem.setEndTime(null);
                        persOptItem.setSuperAuth((short)0);
                        persOptItem.setDisabled(false);
                        persOptItem.setPrivilege((short)0);
                        persOptItem.setDelayPassage(false);
                    }
                    AccPersonCombOpenPerson accPersonCombOpenPerson =
                        accPersonCombOpenPersonMap.get(item.getPersonId());
                    persOptItem.setGroupId(accPersonCombOpenPerson != null
                        ? accPersonCombOpenPerson.getAccCombOpenPerson().getBusinessId() : 0);
                    for (AccDevice device : deviceList) {
                        accDevCmdManager.sendPersToDev(device, persOptItem);
                    }
                }
            }
        }
    }

    @Override
    public void setOtherBioTemplate2Dev(Collection<PersBioTemplate2OtherItem> items, String module) {
        // 自己模块的数据不处理
        if (ConstUtil.SYSTEM_MODULE_ACC.equals(module)) {
            return;
        }
        for (PersBioTemplate2OtherItem item : items) {
            List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonId(item.getPersonId());
            if (levelIdList != null && levelIdList.size() > 0) {
                List<AccDevice> deviceList = accDeviceDao.getDevByLevel(levelIdList);
                List<AccBioTemplateItem> bioTems = new ArrayList<>();
                AccBioTemplateItem bioTem = new AccBioTemplateItem();
                bioTem.setPin(item.getPin());
                bioTem.setType(item.getBioType());
                bioTem.setVersion(item.getVersion());
                bioTem.setTemplateId(item.getTemplateNo());
                bioTem.setTemplateIndex(item.getTemplateNoIndex());
                bioTem.setDuress(item.getDuress());
                bioTem.setTemplate(item.getTemplate());
                bioTems.add(bioTem);
                // 2.下发命令给该用户权限组下的设备
                for (AccDevice device : deviceList) {
                    accDevCmdManager.setPersonBioTemplateToDev(device.getSn(), bioTems, false);
                }
            }
        }
    }

    @Override
    public void setOtherBioPhoto2Dev(Collection<PersBioPhoto2OtherItem> items, String module) {
        // 自己模块的数据不处理
        if (ConstUtil.SYSTEM_MODULE_ACC.equals(module)) {
            return;
        }
        for (PersBioPhoto2OtherItem item : items) {
            List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonId(item.getPersonId());
            if (levelIdList != null && levelIdList.size() > 0) {
                List<AccDevice> deviceList = accDeviceDao.getDevByLevel(levelIdList);
                AccPersonOptItem bioPhotoItem = new AccPersonOptItem();
                bioPhotoItem.setPin(item.getPin());
                bioPhotoItem.setBioPhotoBase64(item.getContent());
                for (AccDevice device : deviceList) {
                    accDevCmdManager.sendBioPhotoInfoToDev(device.getSn(), bioPhotoItem, (short)0, false);
                    // 拓展接口，为了兼容华为pad设备下发downLoad命令通知设备已下发完成的功能 by mingfa.zheng 20200727
                    accDevCmdManager.downloadOver(device.getSn(), false);
                }
            }
        }
    }

    @Override
    public void setOtherUserPic2Dev(Collection<PersUserPic2OtherItem> items, String module) {
        // 自己模块的数据不处理
        if (ConstUtil.SYSTEM_MODULE_ACC.equals(module)) {
            return;
        }
        for (PersUserPic2OtherItem item : items) {
            List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonId(item.getPersonId());
            if (levelIdList != null && levelIdList.size() > 0) {
                List<AccDevice> deviceList = accDeviceDao.getDevByLevel(levelIdList);
                AccPersonOptItem bioPhotoItem = new AccPersonOptItem();
                bioPhotoItem.setPin(item.getPin());
                bioPhotoItem.setPhotoBase64(item.getContent());
                for (AccDevice device : deviceList) {
                    accDevCmdManager.sendUserPicToDev(device.getSn(), bioPhotoItem, false);
                }
            }
        }
    }

    @Override
    public void pushUserPic2Devs(Collection<PersUserPic2OtherItem> items) {
        for (PersUserPic2OtherItem item : items) {
            List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonId(item.getPersonId());
            if (levelIdList != null && levelIdList.size() > 0) {
                List<AccDevice> deviceList = accDeviceDao.getDevByLevel(levelIdList);
                AccPersonOptItem bioPhotoItem = new AccPersonOptItem();
                bioPhotoItem.setPin(item.getPin());
                bioPhotoItem.setPhotoBase64(item.getContent());
                bioPhotoItem.setPhotoPath(item.getPhotoPath());
                for (AccDevice device : deviceList) {
                    accDevCmdManager.pushUserPicToDev(device.getSn(), bioPhotoItem, false);
                }
            }
        }
    }

    @Override
    public void issuedCard(List<PersCardItem> persCardItems) {
        if (persCardItems != null && persCardItems.size() > 0) {
            List<String> personIdList =
                (List<String>)CollectionUtil.getPropertyList(persCardItems, PersCardItem::getPersonId, "-1");
            AccPersonInfoItem accPersonInfoItem = accLevelService.getPersonBasicInfoByIds(personIdList);
            List<AccDevice> accDeviceList = accDoorDao.getDevByPersonLevel(personIdList);
            if (accDeviceList != null && accDeviceList.size() > 0) {
                // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了
                AccDeviceUtil.moveUpParentDevList(accDeviceList);
                accDeviceList.stream().forEach(accDevice -> {
                    accLevelService.setPersonCardToDevice(accDevice.getId(), accPersonInfoItem);
                });
            }
        }
    }

    @Override
    public void pushBioPhoto2Devs(Collection<PersBioPhoto2OtherItem> items) {
        for (PersBioPhoto2OtherItem item : items) {
            List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonId(item.getPersonId());
            if (levelIdList != null && levelIdList.size() > 0) {
                List<AccDevice> deviceList = accDeviceDao.getDevByLevel(levelIdList);
                AccPersonOptItem bioPhotoItem = new AccPersonOptItem();
                bioPhotoItem.setPin(item.getPin());
                bioPhotoItem.setBioPhotoPath(item.getBioPhotoPath());
                for (AccDevice device : deviceList) {
                    accDevCmdManager.sendBioPhotoInfoToDev(device.getSn(), bioPhotoItem, CmdSecConstants.FORMAT_URL,
                        false);
                    // 拓展接口，为了兼容华为pad设备下发downLoad命令通知设备已下发完成的功能 by mingfa.zheng 20200727
                    accDevCmdManager.downloadOver(device.getSn(), false);
                }
            }
        }
    }

    @Override
    public void editPersonExt(PersPersonItem personItem, Map<String, String> extParams) {
        boolean updateAuth =
            MapUtils.getString(extParams, "moduleAuth", StringUtils.EMPTY).contains(ConstUtil.SYSTEM_MODULE_ACC);
        boolean modifyAccInfo = false;
        if (updateAuth) {
            AccPerson tempPerson = accPersonDao.findByPersonId(personItem.getId());
            Boolean delayPassage = MapUtils.getBoolean(extParams, "acc.delayPassage", false);
            // 如果人员存在但是没有key说明不做修改
            if (tempPerson != null && !extParams.containsKey("acc.delayPassage")) {
                delayPassage = tempPerson.getDelayPassage();
            }
            Short superAuth = MapUtils.getShort(extParams, "acc.superAuth", (short)0);
            if (tempPerson != null && !extParams.containsKey("acc.superAuth")) {
                superAuth = tempPerson.getSuperAuth();
            }
            Boolean disabled = MapUtils.getBoolean(extParams, "acc.disabled", false);
            // 如果人员存在但是没有key说明不做修改
            if (tempPerson != null && !extParams.containsKey("acc.disabled")) {
                disabled = tempPerson.getDisabled();
            }
            Short privilege = MapUtils.getShort(extParams, "acc.privilege", (short)0);
            // 如果人员存在但是没有key说明不做修改
            if (tempPerson != null && !extParams.containsKey("acc.privilege")) {
                privilege = tempPerson.getPrivilege();
            }
            Boolean isSetValidTime = MapUtils.getBoolean(extParams, "acc.isSetValidTime", false);
            Date startTime = null;
            Date endTime = null;
            if (isSetValidTime) {
                if (!extParams.containsKey("acc.startTime")) {
                    startTime = tempPerson.getStartTime();
                } else {
                    startTime = DateUtil.stringToDate(MapUtils.getString(extParams, "acc.startTime"),
                        DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
                }
                if (!extParams.containsKey("acc.startTime")) {
                    endTime = tempPerson.getEndTime();
                } else {
                    endTime = DateUtil.stringToDate(MapUtils.getString(extParams, "acc.endTime"),
                        DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
                }
            }
            if (tempPerson == null) {
                tempPerson = new AccPerson();
                modifyAccInfo = true;
            } else if (!(superAuth.equals(tempPerson.getSuperAuth()) && privilege.equals(tempPerson.getPrivilege())
                && delayPassage.equals(tempPerson.getDelayPassage()) && disabled.equals(tempPerson.getDisabled())
                && isSetValidTime.equals(tempPerson.getIsSetValidTime()))) {
                modifyAccInfo = true;
            } else if (!(AccDataUtil.isSameDay(startTime, tempPerson.getStartTime())
                && AccDataUtil.isSameDay(endTime, tempPerson.getEndTime()))) {
                modifyAccInfo = true;
            }
            tempPerson.setDelayPassage(delayPassage);
            tempPerson.setDisabled(disabled);
            tempPerson.setSuperAuth(superAuth);
            tempPerson.setIsSetValidTime(isSetValidTime);
            tempPerson.setStartTime(startTime);
            tempPerson.setEndTime(endTime);
            tempPerson.setPrivilege(privilege);
            tempPerson.setPersonId(personItem.getId());
            accPersonDao.save(tempPerson);
        }
        boolean modifyPersInfo = MapUtils.getBoolean(extParams, "personModify", false);
        List<String> levelIdList = null;
        List<String> exitLevelIds = new ArrayList<>();

        if (modifyPersInfo || updateAuth) {
            exitLevelIds = accLevelPersonDao.getLevelIdByPersonId(personItem.getId());
        }
        boolean accLevelModify = false;
        // 有门禁设置页面
        if (updateAuth && extParams.containsKey("acc.personLevelIds")) {
            // 根据传过来的权限组id再进行一次查询，以数据库为准，避免并发操作导致数据不一致
            // String accPersonLevelIds = MapUtils.getString(extParams, "acc.personLevelIds");
            String accPersonLevelTimeInfos = MapUtils.getString(extParams, "acc.personLevelIds");

            List<AccPersonTimeLevelItem> accPersonTimeLevelList =
                AccLevelUtil.buildAccLevelPersonItem(accPersonLevelTimeInfos, personItem.getId());
            String accPersonLevelIds = "";
            if (!CollectionUtil.isEmpty(accPersonTimeLevelList)) {
                accPersonLevelIds =
                    CollectionUtil.getPropertys(accPersonTimeLevelList, AccPersonTimeLevelItem::getLevelId);
            }
            // 清空人员权限组
            if ("".equals(accPersonLevelIds)) {
                accLevelService.delBatchLevel(exitLevelIds, (List<String>)CollectionUtil.strToList(personItem.getId()));
                // 直接进行删除操作
                delPersonLevelFromDev(exitLevelIds, personItem.getPin());
                accLevelModify = true;
            } else if (accPersonLevelIds != null) {
                // 查出人员关联的权限组人员信息
                List<AccLevelPerson> levelPersonList = accLevelPersonDao.getLevelTimesIdByPersonId(personItem.getId());
                List<String> accPersonLevelIdList =
                    accLevelDao.getLevelIdsByLevelIds(StrUtil.strToList(accPersonLevelIds));
                // 复制一份id用于存放已有权限组id和传入的权限组id，用于取交集
                List<String> tempIdList = new ArrayList<>(accPersonLevelIdList);
                tempIdList.retainAll(exitLevelIds);
                // 原有权限组id去除交集部分得出为需要删除的权限组id
                exitLevelIds.removeAll(tempIdList);
                if (!exitLevelIds.isEmpty()) {
                    accLevelService.delBatchLevel(exitLevelIds, (List<String>)CollectionUtil.strToList(personItem.getId()));
                    delPersonLevelFromDev(exitLevelIds, personItem.getPin());
                    accLevelModify = true;
                }
                // 复制一份用于查询发送数据
                levelIdList = new ArrayList<>(accPersonLevelIdList);
                // 传入的权限组id去除交集id为需要增加的id
                // accPersonLevelIdList.removeAll(tempIdList);
                // 已存在数据
                Map<String, AccLevelPerson> levelPersonMap = new HashMap<>(levelPersonList.size());
                for (AccLevelPerson accLevelPerson : levelPersonList) {
                    levelPersonMap.put(accLevelPerson.getAccLevel().getId(), accLevelPerson);
                }
                // 新增加数据
                Map<String, AccPersonTimeLevelItem> levelPersonTimeMap =
                    CollectionUtil.listToKeyMap(accPersonTimeLevelList, AccPersonTimeLevelItem::getLevelId);

                List<AccPersonTimeLevelItem> addLevelPersonTimes = new ArrayList<>();
                // 新增加的权限组
                for (String levelId : levelPersonTimeMap.keySet()) {
                    if (!levelPersonMap.containsKey(levelId)) {
                        addLevelPersonTimes.add(levelPersonTimeMap.get(levelId));
                    }
                }
                // 开始日期和结束日期不一样的 变更的权限组
                List<String> updateLevelIds = new ArrayList<>();
                for (String levelId : levelPersonMap.keySet()) {
                    if (levelPersonTimeMap.containsKey(levelId)) {
                        AccLevelPerson item = levelPersonMap.get(levelId);
                        Date oldStartTime = item.getStartTime();
                        Date oldEndTime = item.getEndTime();
                        String oldSt = "";
                        String oldEt = "";
                        if (oldStartTime != null && oldEndTime != null) {
                            oldSt = DateUtil.dateToString(oldStartTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
                            oldEt = DateUtil.dateToString(oldEndTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
                        }

                        AccPersonTimeLevelItem newItem = levelPersonTimeMap.get(levelId);
                        String newSt = newItem.getStartTime();
                        String newEt = newItem.getEndTime();
                        // 为null表示没有进行配置
                        if (newSt == null || newEt == null) {
                            continue;
                        }
                        // 只要时间一致就不再更新
                        if (newSt.equals(oldSt) && newEt.equals(oldEt)) {
                            continue;
                        }
                        updateLevelIds.add(levelId);
                        addLevelPersonTimes.add(levelPersonTimeMap.get(levelId));
                    }
                }
                if (!addLevelPersonTimes.isEmpty()) {
                    // 有变更权限组时间的需要先删除人员原先下发的权限组命令
                    if (!CollectionUtil.isEmpty(updateLevelIds)) {
                        // 仅删除人员权限信息
                        delPersonLevelsFromDev(updateLevelIds, personItem.getPin());
                    }
                    accLevelService.addLevelByParamIds(addLevelPersonTimes, "person");
                    accLevelModify = true;
                }
            } else {
                levelIdList = exitLevelIds;
            }
        } else if (modifyPersInfo) {
            levelIdList = exitLevelIds;
        }
        if (levelIdList != null && !levelIdList.isEmpty()) {
            Map<AccDevice, List<Object[]>> levelDoorMap = getLevelDoorMapByLevelIds(levelIdList, personItem);
            List<AccDevice> accDeviceList = new ArrayList<>(levelDoorMap.keySet());
            // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了
            AccDeviceUtil.moveUpParentDevList(accDeviceList);
            // 有修改人员信息、门禁人员信息或者权限组信息就下发人员信息到设备
            if (modifyAccInfo || modifyPersInfo || accLevelModify) {
                AccPersonInfoItem accPersonInfoItem =
                    accLevelService.getPersonByIds((List<String>)CollectionUtil.strToList(personItem.getId()));
                accDeviceList.forEach(accDevice -> {
                    setPersonInfoToDev(accDevice, accPersonInfoItem);
                    // 拓展接口，为了兼容华为pad设备下发downLoad命令通知设备已下发完成的功能 by mingfa.zheng 20200727
                    accDevCmdManager.downloadOver(accDevice.getSn(), false);
                });
            }
            if (accLevelModify) {
                Map<String, List<AccPersonLevelOptItem>> personLevelOptMap = new HashMap<>();
                for (Map.Entry<AccDevice, List<Object[]>> entry : levelDoorMap.entrySet()) {
                    putPersLevelToMap(personItem, personLevelOptMap, entry.getKey(), entry.getValue());
                }
                accDeviceList.forEach(accDevice -> {
                    accDevCmdManager.setPersonLevelToDev(accDevice.getSn(), personLevelOptMap.get(accDevice.getId()),
                        false);
                });
            }
        }
    }

    /**
     * 组装下发的权限组门信息
     *
     * @param levelIdList:
     * @param personItem:
     * @return java.util.Map<com.zkteco.zkbiosecurity.acc.model.AccDevice,java.util.List<java.lang.Object[]>>
     * <AUTHOR>
     * @throws
     * @date 2024-01-12 17:04
     * @since 1.0.0
     */
    private Map<AccDevice, List<Object[]>> getLevelDoorMapByLevelIds(List<String> levelIdList,
        PersPersonItem personItem) {
        List<AccTimeSegItem> timeSegItemList = accTimeSegService.getByCondition(new AccTimeSegItem());
        Map<String, AccTimeSegItem> timeSegItemMap = CollectionUtil.itemListToIdMap(timeSegItemList);
        List<AccLevelPerson> newLevelPersonList = accLevelPersonDao.getLevelTimesIdByPersonId(personItem.getId());
        Map<String, AccLevelPerson> newLevelPersonMap = new HashMap<>(newLevelPersonList.size());
        for (AccLevelPerson accLevelPerson : newLevelPersonList) {
            newLevelPersonMap.put(accLevelPerson.getAccLevel().getId(), accLevelPerson);
        }
        Map<AccDevice, List<Object[]>> levelDoorMap = new HashMap<>();
        final List<AccLevelDoor> doorLevelList = accLevelDoorDao.findByAccLevel_IdIn(levelIdList);
        doorLevelList.forEach(levelDoor -> {
            final AccLevel accLevel = levelDoor.getAccLevel();
            final AccDoor accDoor = levelDoor.getAccDoor();
            AccTimeSegItem timeSegItem = timeSegItemMap.get(accLevel.getTimeSegId());
            Object[] res = new Object[6];
            res[0] = personItem.getPin();
            res[1] = timeSegItem != null ? timeSegItem.getBusinessId() : "";
            res[2] = accDoor.getDoorNo();
            res[3] = accLevel.getBusinessId();
            // 临时权限新增
            // 人员有权限
            if (!CollectionUtil.isEmpty(newLevelPersonMap)) {
                AccLevelPerson accLevelPerson = newLevelPersonMap.get(accLevel.getId());
                if (Objects.nonNull(accLevelPerson)) {
                    // 权限组有时间段
                    if (Objects.nonNull(accLevelPerson.getStartTime())) {
                        res[4] = DateUtil.dateToString(accLevelPerson.getStartTime(),
                            DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
                    }
                    if (Objects.nonNull(accLevelPerson.getEndTime())) {
                        res[5] =
                            DateUtil.dateToString(accLevelPerson.getEndTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
                    }
                }
            }
            final AccDevice device = accDoor.getDevice();

            if (levelDoorMap.containsKey(device)) {
                levelDoorMap.get(device).add(res);
            } else {
                List<Object[]> resList = new ArrayList<>();
                resList.add(res);
                levelDoorMap.put(device, resList);
            }
        });
        return levelDoorMap;
    }

    /**
     * 仅删除人员权限
     *
     * @param levelIds:
     * @param pin:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-01-06 13:54
     * @since 1.0.0
     */
    private void delPersonLevelsFromDev(List<String> levelIds, String pin) {
        if (levelIds != null && levelIds.size() > 0) {
            List<String> pinList = new ArrayList<>();
            pinList.add(pin);
            // 获取设备Set
            List<AccDevice> devList = accDeviceDao.getDevByLevel(levelIds);
            // 删除人员权限命令
            for (AccDevice dev : devList) {
                // 删除权限命令,
                accDevCmdManager.delPersonLevelFromDev(dev.getSn(), pinList, false);
            }
        }
    }

    /**
     * 只下发人员信息，不下发人员权限
     *
     * @param accDevice:
     * @param accPersonInfoItem:
     * @return void
     * @throws @date 2021-12-02 9:40
     * <AUTHOR>
     * @since 1.0.0
     */
    private void setPersonInfoToDev(AccDevice accDevice, AccPersonInfoItem accPersonInfoItem) {
        List<String> pins = new ArrayList<>();
        for (AccPersonOptItem accPersonOptBean : accPersonInfoItem.getPersonList()) {
            pins.add(accPersonOptBean.getPin());
        }
        // 多卡数据类似于指纹，下发之前需要按照pin号全部删除然后再重新下发
        accDevCmdManager.delMulCardUserFromDev(accDevice.getSn(), pins, false);
        // 根据pin号删除比对照片
        accDevCmdManager.delAllBiophotoByPins(accDevice.getSn(), pins, false);
        accDevCmdManager.delPersonBioTemplateFromDev(accDevice.getSn(), pins, false);
        // 下发人员信息
        accDevCmdManager.setPersonToDev(accDevice.getSn(), accPersonInfoItem.getPersonList(), false);
        List<AccBioTemplateItem> allBioTempBeans = accPersonInfoItem.getAccBioTemplateItemList();
        if (allBioTempBeans != null && allBioTempBeans.size() > 0) {
            // 下发生物模板信息
            accDevCmdManager.setPersonBioTemplateToDev(accDevice.getSn(), allBioTempBeans, false);
        }
    }

    /**
     * 构造人员权限组
     *
     * @param persPersonItem
     * @param personLevelOptMap
     * @param dev
     * @param accLevelDoorList
     */
    private void putPersLevelToMap(PersPersonItem persPersonItem,
        Map<String, List<AccPersonLevelOptItem>> personLevelOptMap, AccDevice dev, List<Object[]> accLevelDoorList) {
        List<AccPersonLevelOptItem> personLevelList = new ArrayList<>();
        Map<String, List<Short>> pinTimeSegDoorMap = new HashMap<>();
        if (accLevelDoorList != null) {
            for (Object[] ob : accLevelDoorList) {
                String tempKey = persPersonItem.getPin() + "_" + ob[1];
                // 加入开始时间和结束时间
                String st = ob[4] != null ? ob[4].toString() : "";
                String et = ob[5] != null ? ob[5].toString() : "";
                if (st.length() > 0 && et.length() > 0) {
                    tempKey += "_" + st + "_" + et;
                }
                if (accProExtService != null) {
                    tempKey = "_" + ob[3];
                }
                Short doorNo = Short.parseShort(ob[2] + "");
                if (!pinTimeSegDoorMap.containsKey(tempKey)) {
                    List<Short> doorNoList = new ArrayList<>();
                    doorNoList.add(doorNo);
                    pinTimeSegDoorMap.put(tempKey, doorNoList);
                } else {
                    pinTimeSegDoorMap.get(tempKey).add(doorNo);
                }
            }
        }
        // 拼装下发人员权限的数据。
        for (String key : pinTimeSegDoorMap.keySet()) {
            String[] pinTimeSegId = key.split("_");
            AccPersonLevelOptItem accPersonLevelOptItem = new AccPersonLevelOptItem();
            accPersonLevelOptItem.setPin(pinTimeSegId[0]);
            accPersonLevelOptItem.setTimeSegId(Long.parseLong(pinTimeSegId[1]));
            // 加入开始时间和结束时间
            if (pinTimeSegId.length > 3) {
                accPersonLevelOptItem
                    .setStartTime(DateUtil.stringToDate(pinTimeSegId[2], DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                accPersonLevelOptItem
                    .setEndTime(DateUtil.stringToDate(pinTimeSegId[3], DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
            }
            if (accProExtService != null) {
                if (pinTimeSegId.length > 4) {
                    accPersonLevelOptItem.setLevelId(Long.valueOf(pinTimeSegId[4]));
                } else {
                    accPersonLevelOptItem.setLevelId(Long.valueOf(pinTimeSegId[2]));
                }
            }
            accPersonLevelOptItem.setDeviceId(dev.getBusinessId());
            List<Short> doorNoList = pinTimeSegDoorMap.get(key);
            if (doorNoList != null && doorNoList.size() > 0) {
                accPersonLevelOptItem.setDoorNoList(doorNoList);
                personLevelList.add(accPersonLevelOptItem);
            }
        }
        personLevelOptMap.put(dev.getId(), personLevelList);
    }

    /**
     * 删除人员权限及人员信息
     *
     * @param levelIds:
     * @param pin:
     * @return void
     * @throws @date 2021-12-06 15:43
     * <AUTHOR>
     * @since 1.0.0
     */
    private void delPersonLevelFromDev(List<String> levelIds, String pin) {
        if (levelIds != null && levelIds.size() > 0) {
            List<String> pinList = new ArrayList<>();
            pinList.add(pin);
            // 获取设备Set
            List<AccDevice> devList = accDeviceDao.getDevByLevel(levelIds);
            // 删除人员权限命令
            for (AccDevice dev : devList) {
                // 全删全下权限
                // 删除权限命令,
                accDevCmdManager.delPersonLevelFromDev(dev.getSn(), pinList, false);
                // 删除指纹
                accDevCmdManager.delPersonBioTemplateFromDev(dev.getSn(), pinList, false);
                // 删除人员信息
                accDevCmdManager.delPersonFromDev(dev.getSn(), pinList, false);
            }
        }
    }

    @Override
    public void enabledCredential(String personIds) {
        List<String> personIdList = StrUtil.strToList(personIds);
        for (String personId : personIdList) {
            PersPersonItem personItem = persPersonService.getItemById(personId);
            List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonId(personId);
            if (levelIdList != null && levelIdList.size() > 0) {
                Map<AccDevice, List<Object[]>> levelDoorMap = getLevelDoorMapByLevelIds(levelIdList, personItem);
                List<AccDevice> accDeviceList = new ArrayList<>(levelDoorMap.keySet());
                // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了
                AccDeviceUtil.moveUpParentDevList(accDeviceList);
                AccPersonInfoItem accPersonInfoItem =
                    accLevelService.getPersonByIds((List<String>)CollectionUtil.strToList(personItem.getId()));
                accDeviceList.forEach(accDevice -> {
                    setPersonInfoToDev(accDevice, accPersonInfoItem);
                    // 拓展接口，为了兼容华为pad设备下发downLoad命令通知设备已下发完成的功能 by mingfa.zheng 20200727
                    accDevCmdManager.downloadOver(accDevice.getSn(), false);
                });
                Map<String, List<AccPersonLevelOptItem>> personLevelOptMap = new HashMap<>();
                for (Map.Entry<AccDevice, List<Object[]>> entry : levelDoorMap.entrySet()) {
                    putPersLevelToMap(personItem, personLevelOptMap, entry.getKey(), entry.getValue());
                }
                accDeviceList.forEach(accDevice -> {
                    accDevCmdManager.setPersonLevelToDev(accDevice.getSn(), personLevelOptMap.get(accDevice.getId()),
                        false);
                });
            }
        }
    }

    @Override
    public void disableCredential(String personIds) {
        List<String> personIdList = StrUtil.strToList(personIds);
        for (String personId : personIdList) {
            PersPersonItem personItem = persPersonService.getItemById(personId);
            List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonId(personId);
            if (levelIdList != null && levelIdList.size() > 0) {
                List<AccDevice> levelDeviceList = accDeviceDao.getDevByLevel(levelIdList);
                List<String> pinList = new ArrayList<>();
                pinList.add(personItem.getPin());
                for (AccDevice dev : levelDeviceList) {
                    // 1.禁用，下发删除人员信息
                    accDevCmdManager.delPersonBioTemplateFromDev(dev.getSn(), pinList, false);
                    accDevCmdManager.delPersonFromDev(dev.getSn(), pinList, false);
                    accDevCmdManager.delPersonLevelFromDev(dev.getSn(), pinList, false);
                }
            }
        }
    }
}
