/**
 * File Name: AccLinkageTriggerServiceImpl Created by GenerationTools on 2018-03-16 下午04:41 Copyright:Copyright ©
 * 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.dao.AccLinkageTriggerDao;
import com.zkteco.zkbiosecurity.acc.model.AccLinkageTrigger;
import com.zkteco.zkbiosecurity.acc.service.AccLinkageTriggerService;
import com.zkteco.zkbiosecurity.acc.vo.AccLinkageTriggerItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 对应百傲瑞达 AccLinkageTriggerServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-16 下午04:41
 * @version v1.0
 */
@Service
@Transactional
public class AccLinkageTriggerServiceImpl implements AccLinkageTriggerService {
    @Autowired
    private AccLinkageTriggerDao accLinkageTriggerDao;

    @Override
    public AccLinkageTriggerItem saveItem(AccLinkageTriggerItem item) {
        AccLinkageTrigger accLinkageTrigger =
            Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
                .flatMap(id -> accLinkageTriggerDao.findById(id)).orElse(new AccLinkageTrigger());

        ModelUtil.copyProperties(item, accLinkageTrigger);
        accLinkageTriggerDao.save(accLinkageTrigger);
        item.setId(accLinkageTrigger.getId());
        return item;
    }

    @Override
    public List<AccLinkageTriggerItem> getByCondition(AccLinkageTriggerItem condition) {
        return (List<AccLinkageTriggerItem>)accLinkageTriggerDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accLinkageTriggerDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accLinkageTriggerDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AccLinkageTriggerItem getItemById(String id) {
        List<AccLinkageTriggerItem> items = getByCondition(new AccLinkageTriggerItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public List<AccLinkageTriggerItem> findByLinkageId(String accLinkageId) {
        if (StringUtils.isNoneBlank(accLinkageId)) {
            return accLinkageTriggerDao.findByAccLinkage_Id(accLinkageId).stream()
                .map(trigger -> ModelUtil.copyProperties(trigger, new AccLinkageTriggerItem()))
                .collect(Collectors.toList());
        }
        return new ArrayList<>(0);
    }
    @Override
    public List<AccLinkageTriggerItem> getItemsByLinkageIdAndLinkageInOutId(String linkageId, String linkageInOutId) {
        List<AccLinkageTrigger> triggerList =
            accLinkageTriggerDao.findByAccLinkage_IdAndAccLinkageInOut_Id(linkageId, linkageInOutId);
        List<AccLinkageTriggerItem> triggerItemList = new ArrayList<>();
        if (Objects.nonNull(triggerList) && !triggerList.isEmpty()) {
            for (AccLinkageTrigger trigger : triggerList) {
                AccLinkageTriggerItem triggerItem = new AccLinkageTriggerItem();
                triggerItem.setId(trigger.getId());
                triggerItem.setTriggerCond(trigger.getTriggerCond());
                triggerItem.setLinkageIndex(trigger.getLinkageIndex());
                triggerItem.setLinkageId(trigger.getAccLinkage().getId());
                triggerItem.setLinkageInOutId(trigger.getAccLinkageInOut().getId());
                triggerItemList.add(triggerItem);
            }
        }
        return triggerItemList;
    }
}