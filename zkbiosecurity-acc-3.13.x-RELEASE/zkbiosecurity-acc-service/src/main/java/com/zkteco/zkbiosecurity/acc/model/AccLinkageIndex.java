/**
 * File Name: AccLinkageIndex
 * Created by GenerationTools on 2018-03-16 下午04:41
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 对应百傲瑞达实体 AccLinkageIndex
 * <AUTHOR>
 * @date:	2018-03-16 下午04:41
 * @version v1.0
 */
@Entity
@Table(name = "ACC_LINKAGE_INDEX")
@Getter
@Setter
@Accessors(chain=true)
public class AccLinkageIndex extends BaseModel implements Serializable {

	/** */
	private static final long serialVersionUID = 1L;

	/**  */
	@ManyToOne
	@JoinColumn(name="DEV_ID")
	private AccDevice accDevice;

	/**  */
	@Column(name="MAX_INDEX",nullable=false)
	private Integer maxIndex;

	public AccLinkageIndex(){

	}

	public AccLinkageIndex( AccDevice accDevice, int maxIndex)
	{
		this.accDevice = accDevice;
		this.maxIndex = maxIndex;
	}
}