package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.app.vo.AccAppTopDoorByPersonItem;
import com.zkteco.zkbiosecurity.acc.dao.AccAppTopDoorByPersonDao;
import com.zkteco.zkbiosecurity.acc.model.AccAppTopDoorByPerson;
import com.zkteco.zkbiosecurity.acc.model.AccDoor;
import com.zkteco.zkbiosecurity.acc.model.AccFirstOpen;
import com.zkteco.zkbiosecurity.acc.service.AccAppTopDoorByPersonService;
import com.zkteco.zkbiosecurity.acc.vo.AccFirstOpenItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date: 2019/07/11 17:44
 */
@Service
@Transactional
public class AccAppTopDoorByPersonServiceImpl implements AccAppTopDoorByPersonService {

    @Autowired
    private AccAppTopDoorByPersonDao accAppTopDoorByPersonDao;

    @Override
    public Pager getItemsByPage(BaseItem baseItem, int i, int i1) {
        return null;
    }

    @Override
    public boolean deleteByIds(String s) {
        accAppTopDoorByPersonDao.delete(s);
        return false;
    }

    @Override
    public AccAppTopDoorByPersonItem saveItem(AccAppTopDoorByPersonItem item) {
        AccAppTopDoorByPerson accAppTopDoorByPerson = Optional.ofNullable(item)
                .map(i->i.getId())
                .filter(StringUtils::isNotBlank)
                .flatMap(id->accAppTopDoorByPersonDao.findById(id))
                .orElse(new AccAppTopDoorByPerson());
        ModelUtil.copyPropertiesIgnoreNull(item, accAppTopDoorByPerson);
        accAppTopDoorByPersonDao.save(accAppTopDoorByPerson);
        item.setId(accAppTopDoorByPerson.getId());
        return item;
    }

    @Override
    public AccAppTopDoorByPersonItem delete(AccAppTopDoorByPersonItem item) {
        accAppTopDoorByPersonDao.delete(item.getId());
        return null;
    }

    @Override
    public List<AccAppTopDoorByPersonItem> getByCondition(AccAppTopDoorByPersonItem condition) {
        return (List<AccAppTopDoorByPersonItem>) accAppTopDoorByPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }
}
