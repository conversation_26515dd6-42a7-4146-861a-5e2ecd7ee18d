/*
 * File Name: AccRTMonitorServiceImpl <NAME_EMAIL> on 2018/6/4 10:20. Copyright:Copyright ©
 * 1999-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccAuxOutDao;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.dao.AccDoorDao;
import com.zkteco.zkbiosecurity.acc.dao.AccTransactionDao;
import com.zkteco.zkbiosecurity.acc.model.AccAuxOut;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.model.AccDoor;
import com.zkteco.zkbiosecurity.acc.model.AccTransaction;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.utils.AccDeviceUtil;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.adms.bean.DeviceDoorState;
import com.zkteco.zkbiosecurity.adms.service.AdmsDeviceService;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.config.DataSourceConfig;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.utils.DateUtil.DateStyle;
import com.zkteco.zkbiosecurity.license.provider.BaseLicenseProvider;
import com.zkteco.zkbiosecurity.pers.service.PersParamsService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.security.SecurityService;
import com.zkteco.zkbiosecurity.system.service.BaseMediaFileService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseMediaFileItem;
import jodd.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
@Service
@Transactional
public class AccRTMonitorServiceImpl implements AccRTMonitorService {

    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AccAuxOutDao accAuxOutDao;
    @Autowired(required = false)
    private AdmsDeviceService admsDeviceService;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccMonitorRedirectService accMonitorRedirectService;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private BaseMediaFileService baseMediaFileService;
    @Autowired
    private BaseLicenseProvider baseLicenseProvider;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccAuxInService accAuxInService;
    @Autowired
    private AccAuxOutService accAuxOutService;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private AccTransactionDao accTransactionDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired(required = false)
    private Acc4IVideoDoorStateService acc4IVideoDoorStateService;
    @Autowired
    private AccDeviceVerifyModeService accDeviceVerifyModeService;
    @Autowired
    private AccCloudService accCloudService;
    @Autowired
    private AccParamService accParamService;
    @Autowired
    private PersParamsService persParamsService;
    @Autowired
    private SecurityService securityService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Value("${system.language:zh_CN}")
    private String language;

    @Override
    public ZKResultMsg getDevStateData(String areaIds) {
        AccQueryDeviceItem accDeviceItem = new AccQueryDeviceItem();
        JSONObject devState = new JSONObject();
        JSONArray doorState = new JSONArray();
        JSONArray auxInState = new JSONArray();
        JSONArray auxOutState = new JSONArray();
        if (StringUtils.isNotBlank(areaIds)) {
            accDeviceItem.setAreaIdIn(areaIds);
        }
        List<AccQueryDeviceItem> accDeviceList = (List<AccQueryDeviceItem>)accDeviceDao
            .getItemsBySql(accDeviceItem.getClass(), SQLUtil.getSqlByItem(accDeviceItem));
        if (Objects.nonNull(accDeviceList) && !accDeviceList.isEmpty()) {
            // 一次性查出所有设备下的所有数据，进行分组，避免在循环中反复调用
            List<List<String>> devIds =
                CollectionUtil.getPropertyList(accDeviceList, AccQueryDeviceItem::getId, CollectionUtil.splitSize);
            List<AccDoorItem> accDoorList = new ArrayList<>();
            List<AccAuxInItem> accAuxInList = new ArrayList<>();
            List<AccAuxOutItem> accAuxOutList = new ArrayList<>();
            devIds.forEach(devIdList -> {
                accDoorList.addAll(accDoorService.getItemsByDevIds(devIdList));
                accAuxInList.addAll(accAuxInService.getItemsByDevIds(devIdList));
                accAuxOutList.addAll(accAuxOutService.getItemsByDevIds(devIdList));
            });
            Map<String, List<AccDoorItem>> accDoorMap =
                accDoorList.stream().collect(Collectors.groupingBy(AccDoorItem::getDeviceId));
            Map<String, List<AccAuxInItem>> accAuxInMap =
                accAuxInList.stream().collect(Collectors.groupingBy(AccAuxInItem::getDevId));
            Map<String, List<AccAuxOutItem>> accAuxOutMap =
                accAuxOutList.stream().collect(Collectors.groupingBy(AccAuxOutItem::getDevId));

            for (AccQueryDeviceItem dev : accDeviceList) {
                DeviceDoorState deviceDoorState = admsDeviceService.getDeviceDoorState(dev.getSn());
                doorState
                    .addAll(putDoorStateToMonitorDataByDoorItem(dev, accDoorMap.get(dev.getId()), deviceDoorState));
                auxInState.addAll(putAuxInStateToMonitorDataByAuxInItem(accAuxInMap.get(dev.getId()), deviceDoorState));
                auxOutState
                    .addAll(putAuxOutStateToMonitorDataByAuxOutItem(accAuxOutMap.get(dev.getId()), deviceDoorState));
            }
        }
        devState.put("doorState", doorState);
        devState.put("auxInState", auxInState);
        devState.put("auxOutState", auxOutState);
        return new ZKResultMsg(devState);
    }

    @Override
    public ZKResultMsg getDoorState(AccQueryDeviceItem dev, List<AccQueryDoorItem> doorItemList) {
        JSONArray doorArray = new JSONArray();
        if (Objects.nonNull(doorItemList) && !doorItemList.isEmpty()) {
            DeviceDoorState deviceDoorState = admsDeviceService.getDeviceDoorState(dev.getSn());
            doorArray = putDoorStateToMonitorData(dev, doorItemList, deviceDoorState);
        }
        return new ZKResultMsg(doorArray);
    }

    /**
     * 设置辅助输出状态
     *
     * @param accAuxOutList
     * @param deviceDoorState
     * @return
     */
    private JSONArray putAuxOutStateToMonitorDataByAuxOutItem(List<AccAuxOutItem> accAuxOutList,
        DeviceDoorState deviceDoorState) {
        if (accAuxOutList != null && accAuxOutList.size() > 0) {
            final List<AccQueryAuxOutItem> collect = accAuxOutList.stream()
                .map(item -> ModelUtil.copyProperties(item, new AccQueryAuxOutItem())).collect(Collectors.toList());
            return putAuxOutStateToMonitorData(collect, deviceDoorState);
        }
        return new JSONArray();
    }

    /**
     * 设置辅助输出状态
     * 
     * @param accAuxOutList
     * @param deviceDoorState
     * @return
     */
    private JSONArray putAuxOutStateToMonitorData(List<AccQueryAuxOutItem> accAuxOutList,
        DeviceDoorState deviceDoorState) {
        JSONArray auxOutArray = new JSONArray();
        String connect = deviceDoorState.getConnect();
        if (accAuxOutList != null && accAuxOutList.size() > 0) {
            accAuxOutList.forEach(auxOut -> {
                JSONObject auxOutJson = new JSONObject();
                auxOutJson.put("id", auxOut.getId());
                auxOutJson.put("connect", connect);
                auxOutJson.put("opDisplay",
                    ((StringUtils.isNotBlank(connect) && Short.parseShort(connect) == ConstUtil.DEV_STATE_ONLINE)
                        ? "inline" : "none"));
                auxOutArray.add(auxOutJson);
            });
        }
        return auxOutArray;
    }

    /**
     * 保存辅助输入状态
     * 
     * @param accAuxInList
     * @param deviceDoorState
     * @return
     */
    private JSONArray putAuxInStateToMonitorDataByAuxInItem(List<AccAuxInItem> accAuxInList,
        DeviceDoorState deviceDoorState) {
        if (accAuxInList != null && accAuxInList.size() > 0) {
            final List<AccQueryAuxInItem> collect = accAuxInList.stream()
                .map(accAuxInItem -> ModelUtil.copyProperties(accAuxInItem, new AccQueryAuxInItem()))
                .collect(Collectors.toList());
            return putAuxInStateToMonitorData(collect, deviceDoorState);
        }
        return new JSONArray();
    }

    /**
     * 保存辅助输入状态
     * 
     * @param accAuxInList
     * @param deviceDoorState
     * @return
     */
    private JSONArray putAuxInStateToMonitorData(List<AccQueryAuxInItem> accAuxInList,
        DeviceDoorState deviceDoorState) {
        JSONArray auxInArray = new JSONArray();
        String connect = deviceDoorState.getConnect();
        if (accAuxInList != null && accAuxInList.size() > 0) {
            accAuxInList.forEach(auxIn -> {
                JSONObject auxInJson = new JSONObject();
                auxInJson.put("id", auxIn.getId());
                auxInJson.put("connect", connect);
                auxInArray.add(auxInJson);
            });
        }
        return auxInArray;
    }

    /**
     * 设置门实时状态
     * 
     * @param dev
     * @param accDoorList
     * @param deviceDoorState
     * @return
     */
    private JSONArray putDoorStateToMonitorDataByDoorItem(AccQueryDeviceItem dev, List<AccDoorItem> accDoorList,
        DeviceDoorState deviceDoorState) {
        if (accDoorList != null && accDoorList.size() > 0) {
            final List<AccQueryDoorItem> stateItemList =
                accDoorList.stream().map(accDoorItem -> ModelUtil.copyProperties(accDoorItem, new AccQueryDoorItem()))
                    .collect(Collectors.toList());
            return putDoorStateToMonitorData(dev, stateItemList, deviceDoorState);
        }
        return new JSONArray();
    }

    /**
     * 设置门实时状态
     * 
     * @param dev
     * @param accDoorList
     * @param deviceDoorState
     * @return
     */
    private JSONArray putDoorStateToMonitorData(AccQueryDeviceItem dev, List<AccQueryDoorItem> accDoorList,
        DeviceDoorState deviceDoorState) {
        JSONArray doorArray = new JSONArray();
        String connect = StringUtils.isNotBlank(deviceDoorState.getConnect()) ? deviceDoorState.getConnect()
            : ConstUtil.DEV_STATE_OFFLINE + "";
        // time=2018-06-01 11:06:35 sensor=00 relay=00 alarm=00000000 door=0101
        if (accDoorList != null && accDoorList.size() > 0) {
            accDoorList.forEach(accDoor -> {
                JSONObject doorJson = new JSONObject();
                int index = accDoor.getDoorNo() - 1;// 下标索引
                int sensor = 0;
                if (StringUtils.isNotBlank(deviceDoorState.getSensor())
                    && deviceDoorState.getSensor().split(",").length > index) {
                    sensor = Integer.parseInt(deviceDoorState.getSensor().split(",")[index]);
                }
                int relay = 0;
                if (StringUtils.isNotBlank(deviceDoorState.getRelay())
                    && deviceDoorState.getRelay().split(",").length > index) {
                    relay = Integer.parseInt(deviceDoorState.getRelay().split(",")[index]);
                }
                int alarm = 0;
                if (StringUtils.isNotBlank(deviceDoorState.getAlarm())
                    && deviceDoorState.getAlarm().split(",").length > index) {
                    alarm = Integer.parseInt(deviceDoorState.getAlarm().split(",")[index]);
                }
                int door = Integer.parseInt(connect);
                if (StringUtils.isNotBlank(deviceDoorState.getDoor())
                    && deviceDoorState.getDoor().split(",").length > index) {
                    door = Integer.parseInt(deviceDoorState.getDoor().split(",")[index]);
                }
                int doorState =
                    Short.parseShort(connect) == ConstUtil.DEV_STATE_ONLINE ? door : Short.parseShort(connect);
                short alarmLevel = accDeviceOptionService.isSupportFun(dev.getSn(), "~RelayStateFunOn")
                    ? AccConstants.ENABLE : AccConstants.DISABLE;
                // 门底下读头绑定的视频通道ID
                String doorBindChannelIds = accDoorService.getDoorBindChannelIds(accDoor);
                doorJson.put("id", accDoor.getId());
                doorJson.put("areaId", dev.getAuthAreaId());
                doorJson.put("devAlias", dev.getAlias());
                doorJson.put("devSn", dev.getSn());
                doorJson.put("no", accDoor.getDoorNo());
                doorJson.put("name", accDoor.getName());
                doorJson.put("sensor", sensor);
                doorJson.put("relay", relay);
                doorJson.put("alarm", alarm);
                doorJson.put("iconFolderName", AccDeviceUtil.getIconFolderName(dev));
                doorJson.put("connect", connect);
                doorJson.put("doorState", doorState);
                doorJson.put("lockDisplay", accDeviceOptionService.isSupportFunList(dev.getSn(), 2) ? "block" : "none");
                doorJson.put("alarmLevel", alarmLevel);
                doorJson.put("isNewAccess",
                    accDeviceService.isNewAccessControlDevice(dev.getMachineType(), dev.getDeviceName()));
                doorJson.put("isDisableAudio", accDoor.getIsDisableAudio());
                doorJson.put("channelIds", doorBindChannelIds);
                doorArray.add(doorJson);
            });
        }
        return doorArray;
    }

    @Override
    public void updateAccDeviceDoorStates(String sn) {
        // AccDevice dev = accDeviceDao.findBySn(sn);
        AccQueryDeviceItem dev = accDeviceService.getQueryItemBySn(sn);
        if (Objects.nonNull(dev)) {
            DeviceDoorState deviceDoorState = admsDeviceService.getDeviceDoorState(dev.getSn());
            JSONObject devState = new JSONObject();
            List<String> devIds = Lists.newArrayList(dev.getId());
            // List<AccDoorItem> accDoorItemList = accDoorService.getItemsByDevIds(devIds);
            // List<AccAuxInItem> accAuxInItemList = accAuxInService.getItemsByDevIds(devIds);
            // List<AccAuxOutItem> accAuxOutItemList = accAuxOutService.getItemsByDevIds(devIds);
            List<AccQueryDoorItem> accDoorItemList = dev.getAccDoorItemList();
            List<AccQueryAuxInItem> accAuxInItemList = dev.getAccAuxInItemList();
            List<AccQueryAuxOutItem> accAuxOutItemList = dev.getAccAuxOutItemList();
            JSONArray doorState = putDoorStateToMonitorData(dev, accDoorItemList, deviceDoorState);
            JSONArray auxInState = putAuxInStateToMonitorData(accAuxInItemList, deviceDoorState);
            JSONArray auxOutState = putAuxOutStateToMonitorData(accAuxOutItemList, deviceDoorState);
            devState.put("doorState", doorState);
            devState.put("auxInState", auxInState);
            devState.put("auxOutState", auxOutState);
            ZKResultMsg resultMsg = new ZKResultMsg(devState);
            accMonitorRedirectService.sendDeviceDoorState2RTMonitor(resultMsg);
            if (acc4IVideoDoorStateService != null) {
                Acc4IVideoDoorStateItem iVideoDoorStateItem = new Acc4IVideoDoorStateItem();
                iVideoDoorStateItem.setDevSn(sn);
                iVideoDoorStateItem.setDoorState(doorState.toJSONString());
                acc4IVideoDoorStateService.pushIVideoDoorState(iVideoDoorStateItem);
            }
            // 判断设备状态是否发生变化，发生变化才同步状态到云端，以免大量占用云端通道资源
            String devOldState = accCacheManager.getDevStateFromCache(sn);
            if (!StringUtils.equals(deviceDoorState.getConnect(), devOldState)) {
                // 同步设备状态到云端
                accCloudService.asyncDevStatusToCloud(sn);
                accCacheManager.setDevStateToCache(String.valueOf(deviceDoorState.getConnect()), sn);
            }
        }
    }

    @Override
    public Map<String, String> operateDoor(String opType, String openInterval, String ids) {
        Map<String, String> resultMap = new HashMap<>();
        List<AccDoor> doorList = accDoorDao.findByIdIn((List<String>)CollectionUtil.strToList(ids));
        AccDevice dev = null;
        short doorNo = 0;
        long cmdId = 0;
        StringBuilder offlineDev = new StringBuilder();
        StringBuilder cmdIdBuf = new StringBuilder();
        StringBuilder opFailBuf = new StringBuilder();
        StringBuilder notSupportBuf = new StringBuilder();
        String notExistDev = "";
        for (AccDoor door : doorList) {
            dev = door.getDevice();
            doorNo = door.getDoorNo();
            cmdId = 0;
            if (accDoorService.doorIsOnline(dev.getSn(), doorNo)) {
                if ("openDoor".equals(opType)) {
                    short intervalShort = Short.parseShort(openInterval);// 开门时长（1-254秒，255秒为常开）
                    cmdId = accDevCmdManager.ctrlDoor(dev.getSn(), doorNo, intervalShort, true);// 远程开门
                } else if ("normalOpenDoor".equals(opType)) {
                    cmdId = accDevCmdManager.ctrlDoor(dev.getSn(), doorNo, (short)255, true);// 远程常开
                } else if ("enableNormalOpenDoor".equals(opType)) {
                    cmdId = accDevCmdManager.ctrlNormalOpenTimeZone(dev.getSn(), doorNo, 1, true);// 启用当天常开时间段
                } else if ("closeDoor".equals(opType)) {
                    cmdId = accDevCmdManager.ctrlDoor(dev.getSn(), doorNo, (short)0, true);// 远程关门
                } else if ("disableNormalOpenDoor".equals(opType)) {
                    cmdId = accDevCmdManager.ctrlNormalOpenTimeZone(dev.getSn(), doorNo, 0, true);// 禁用当天常开时间段
                } else if ("cancelAlarm".equals(opType)) {
                    cmdId = accDevCmdManager.cancelAlarm(dev.getSn(), doorNo, true);// 取消报警
                } else if ("lockDoor".equals(opType)) {
                    if (accDeviceOptionService.isSupportFunList(dev.getSn(), 2)) {
                        cmdId = accDevCmdManager.ctrlLockDoor(dev.getSn(), doorNo, 1, true);// 门锁定
                    } else {
                        notSupportBuf.append(door.getName()).append(",");// 不支持门名称
                    }
                } else if ("unLockDoor".equals(opType)) {
                    if (accDeviceOptionService.isSupportFunList(dev.getSn(), 2)) {
                        cmdId = accDevCmdManager.ctrlLockDoor(dev.getSn(), doorNo, 0, true);// 门解锁
                    } else {
                        notSupportBuf.append(door.getName()).append(",");// 不支持门名称
                    }
                }
                if (cmdId > 0) {
                    // 增加返回设备sn和门编号
                    cmdIdBuf.append(cmdId).append("=").append(door.getName()).append("=").append(dev.getSn())
                        .append("=").append(doorNo).append(",");
                } else {
                    opFailBuf.append(door.getName()).append(",");// 发送命令失败门名称集合
                }
            } else {
                offlineDev.append(door.getName()).append(",");// 离线设备门名称集合
            }
        }
        if (doorList.isEmpty()) {
            notExistDev = "true";// 不存在可操作设备
        }
        resultMap.put("offline", offlineDev.length() > 1 ? offlineDev.substring(0, offlineDev.length() - 1) : "");
        resultMap.put("faile", opFailBuf.length() > 1 ? opFailBuf.substring(0, opFailBuf.length() - 1) : "");
        resultMap.put("cmdId", cmdIdBuf.length() > 1 ? cmdIdBuf.substring(0, cmdIdBuf.length() - 1) : "");
        resultMap.put("notSupport",
            notSupportBuf.length() > 1 ? notSupportBuf.substring(0, notSupportBuf.length() - 1) : "");
        resultMap.put("notExistDev", notExistDev);
        return resultMap;
    }

    @Override
    public Map<String, String> operateAuxOut(String opType, String openInterval, String ids) {
        Map<String, String> resultMap = new HashMap<>();
        List<AccAuxOut> auxOutList = accAuxOutDao.findByIdList((List<String>)CollectionUtil.strToList(ids));
        long cmdId;
        StringBuilder offlineDev = new StringBuilder();
        StringBuilder opFailBuf = new StringBuilder();
        StringBuilder cmdIdBuf = new StringBuilder();
        String notExistDev = "";
        for (AccAuxOut auxOut : auxOutList) {
            cmdId = 0;
            AccDevice accDevice = auxOut.getAccDevice();
            if (Short.parseShort(accDeviceService.getStatus(accDevice.getSn())) == AccConstants.DEV_STATE_ONLINE) {
                if ("openAuxOut".equals(opType)) {
                    cmdId = accDevCmdManager.ctrlAuxOut(accDevice.getSn(), auxOut.getAuxNo(),
                        Short.parseShort(openInterval), true);// 打开辅助输出
                } else if ("closeAuxOut".equals(opType)) {
                    cmdId = accDevCmdManager.ctrlAuxOut(accDevice.getSn(), auxOut.getAuxNo(), (short)0, true);// 关闭辅助输出
                } else if ("auxOutNormalOpen".equals(opType)) {
                    cmdId = accDevCmdManager.ctrlAuxOut(accDevice.getSn(), auxOut.getAuxNo(), (short)255, true);// 辅助输出常开
                }
                if (cmdId > 0) {
                    cmdIdBuf.append(cmdId).append("=").append(auxOut.getName()).append(","); // 命令返回值存放
                } else {// 执行命令失败
                    opFailBuf.append(auxOut.getName()).append(",");
                }
            } else {// 设备不在线
                offlineDev.append(auxOut.getName()).append(",");
            }
        }
        if (auxOutList.size() == 0) {
            notExistDev = "true";// 不存在可操作设备
        }
        resultMap.put("offline", offlineDev.length() > 1 ? offlineDev.substring(0, offlineDev.length() - 1) : "");
        resultMap.put("faile", opFailBuf.length() > 1 ? opFailBuf.substring(0, opFailBuf.length() - 1) : "");
        resultMap.put("cmdId", cmdIdBuf.length() > 1 ? cmdIdBuf.substring(0, cmdIdBuf.length() - 1) : "");
        resultMap.put("notSupport", "");
        resultMap.put("notExistDev", notExistDev);
        return resultMap;
    }

    @Override
    public String getAudioPath(String mediaFileId) {
        String audioPath = "";
        if (StringUtils.isNotBlank(mediaFileId)) {
            BaseMediaFileItem baseMediaFileItem = baseMediaFileService.getItemById(mediaFileId);// 获取音频文件信息
            if (Objects.nonNull(baseMediaFileItem)) {
                audioPath = baseMediaFileItem.getPath();
            }
        }
        return audioPath;
    }

    @Override
    public ZKResultMsg getDoorState(String doorIds) {
        List<AccDoorItem> accDoorItemList = accDoorService.getItemsByIds(CollectionUtil.strToList(doorIds));
        JSONArray doorState = new JSONArray();
        if (accDoorItemList != null && accDoorItemList.size() > 0) {
            Map<String, List<AccDoorItem>> doorMap =
                accDoorItemList.stream().collect(Collectors.groupingBy(AccDoorItem::getDeviceSn));
            for (String devSn : doorMap.keySet()) {
                DeviceDoorState deviceDoorState = admsDeviceService.getDeviceDoorState(devSn);
                AccQueryDeviceItem queryDeviceItem = accDeviceService.getQueryItemBySn(devSn);
                doorState
                    .addAll(putDoorStateToMonitorDataByDoorItem(queryDeviceItem, doorMap.get(devSn), deviceDoorState));
            }
        }
        return new ZKResultMsg(doorState);
    }

    @Override
    public boolean checkIsChinaLanguage() {
        return "zh_CN".equals(LocaleMessageSourceUtil.language);
    }

    @Override
    public Map<String, String> getFilterAreaMap(String sessionId) {
        String areaIds = accDeviceService.getAreaIdsByAuthFilter(sessionId);
        Map<String, String> authAreaMap = Maps.newHashMap();
        if (StringUtils.isNotBlank(areaIds)) {
            List<AuthAreaItem> authAreaItemList = authAreaService.getItemsByIds(areaIds);
            String authName = CollectionUtil.getPropertys(authAreaItemList, AuthAreaItem::getName);
            authAreaMap.put("areaIds", areaIds);
            authAreaMap.put("areaName", authName);
        }
        return authAreaMap;
    }

    @Override
    public List<AccTransactionItem> getRTMonitorData(String clientId) {
        List<AccTransactionItem> accTransactionItemList = Lists.newArrayList();
        accCacheManager.setRTMonitorClient(clientId);// 更新客户端信息
        List<String> transactionList = accCacheManager.getClientTransaction(clientId);
        if (transactionList != null && !transactionList.isEmpty()) {
            accTransactionItemList = JSON.parseArray(transactionList.toString(), AccTransactionItem.class);
            return accTransactionItemList;
        }
        return accTransactionItemList;
    }

    @Override
    public ApiResultMessage getDoorStateBySn(String deviceSn) {
        ApiResultMessage rs = ApiResultMessage.successMessage();
        List<AccDoor> accDoorList = accDoorDao.findByDevice_Sn(deviceSn);
        if (accDoorList != null && !accDoorList.isEmpty()) {
            String doorIds = CollectionUtil.getPropertys(accDoorList, AccDoor::getId);
            JSONArray doorState = (JSONArray)getDoorState(doorIds).getData();// 根据门ID获取门状态
            if (Objects.nonNull(doorState) && doorState.size() > 0) {
                rs.setData(doorState);
            }
        }
        return rs;
    }

    @Override
    public Boolean isShowlcdRTMonitor() {
        if ("zh_CN".equals(LocaleMessageSourceUtil.language)) {
            return baseLicenseProvider.licenseAvaiable(ConstUtil.SYSTEM_MODULE_LCD);
        }
        return true;
    }

    @Override
    public String getAreaNamesBySessionId(String sessionId) {
        StringBuilder areaNames = new StringBuilder();
        // 获取当前登录用户信息
        List<AuthAreaItem> areaItemList = authAreaService.findBySubject(sessionId);
        for (AuthAreaItem authAreaItem : areaItemList) {
            areaNames.append(authAreaItem.getName()).append(",");
        }
        if (StringUtils.isNotBlank(areaNames.toString())) {
            areaNames = new StringBuilder(areaNames.substring(0, areaNames.length() - 1));
        }
        return areaNames.toString();
    }

    @Override
    public ZKResultMsg getPersonInfo(String sessionId, String areaNames) {
        List<AccTransaction> accTransactionList = new ArrayList<>();
        Pageable pageable = PageRequest.of(0, 5);
        if (StringUtils.isNotBlank(areaNames)) {
            // 按1000条切割
            List<List<String>> tempAreaNameLists = CollectionUtil.split(StrUtil.strToList(areaNames), 1000);
            tempAreaNameLists.forEach(areaName -> {
                // 获取门禁最近5条事件记录
                String dataSource = DataSourceConfig.getDbType();
                List<AccTransaction> listTransactions = new ArrayList<>();
                if (ZKConstant.ORACLE.equals(dataSource)) {
                    listTransactions = accTransactionDao.getLast5TransactionWhenOracle(areaName, pageable);
                } else {
                    listTransactions = accTransactionDao.getLast5Transaction(areaName, pageable);
                }
                accTransactionList.addAll(listTransactions);
            });
        }
        JSONArray datajsonArray = new JSONArray();
        boolean pinEncrypt = securityService.checkPermissionExceptSupperUser(sessionId, "acc:pin:encryptProp");
        String pinEncryptMode = baseSysParamService.getValByName("pers.pin.encryptMode");
        boolean nameEncrypt = securityService.checkPermissionExceptSupperUser(sessionId, "acc:name:encryptProp");
        String nameEncryptMode = baseSysParamService.getValByName("pers.name.encryptMode");
        boolean photoEncrypt = securityService.checkPermissionExceptSupperUser(sessionId, "acc:headPortrait:encryptProp");
        for (AccTransaction accTransaction : accTransactionList) {
            // 组装前端需要的显示信息
            JSONObject persPersonInfo = new JSONObject();
            persPersonInfo.put("pin", pinEncrypt ? StrUtil.convertToEncrypt(accTransaction.getPin(), pinEncryptMode) : accTransaction.getPin());
            String name = accTransaction.getName();
            String lastName = accTransaction.getLastName();
            String personName = "";
            if (StringUtils.isNotBlank(name)) {
                personName = name.trim();
            }
            // 中文下不显示lastname，非中文且lastname不为空时，拼接姓名，解决先前lastname为null时也拼接下去的问题
            if (!"zh_CN".equals(language) && StringUtils.isNotBlank(lastName)) {
                personName = StringUtil.isNotBlank(name) ? (name + " " + lastName).trim() : lastName.trim();
            }
            persPersonInfo.put("personName", nameEncrypt ? StrUtil.convertToEncrypt(personName, nameEncryptMode) : personName);
            persPersonInfo.put("eventPointName", accTransaction.getEventPointName());
            persPersonInfo.put("eventName", accTransaction.getEventName());
            persPersonInfo.put("eventTime",
                DateUtil.dateToString(accTransaction.getEventTime(), DateStyle.YYYY_MM_DD_HH_MM_SS));
            persPersonInfo.put("cardNo", accTransaction.getCardNo());
            String deptName = accTransaction.getDeptName();
            persPersonInfo.put("PersDepartment", StringUtils.isNotBlank(deptName) ? deptName : "");
            PersPersonItem person = persPersonService.getItemByPin(accTransaction.getPin());

            if (person != null && StringUtils.isNotBlank(person.getPhotoPath())) {
                String photoBase64 = FileEncryptUtil.getDecryptFileBase64(person.getPhotoPath());
                if (StringUtils.isNotBlank(photoBase64) && photoEncrypt) {
                    photoBase64 = ImgEncodeUtil.base64BoxBlurFilter(photoBase64);
                }
                persPersonInfo.put("photoPath",
                        StringUtils.isNotBlank(photoBase64) ? AccConstants.PHOTO_BASE64_PREFIX + photoBase64 : "");
            }
            datajsonArray.add(persPersonInfo);
        }
        return new ZKResultMsg(datajsonArray);
    }

    @Override
    public AccTransactionItem remoteOperateBindPerson(String sn, String doorNo, String operator, String opType) {
        if (StringUtils.isNotBlank(sn) && StringUtils.isNotBlank(doorNo)) {
            Integer eventNo = null;
            switch (opType) {
                case "openDoor":
                    eventNo = AccConstants.OPEN_DOOR;
                    break;
                case "closeDoor":
                    eventNo = AccConstants.CLOSE_DOOR;
                    break;
                case "normalOpenDoor":
                    eventNo = AccConstants.EVENT_NORMAL_OPEN;
                    break;
                case "cancelAlarm":
                    eventNo = AccConstants.CANCEL_ALARM;
                    break;
            }

            if (eventNo != null) {
                AccTransactionItem item = new AccTransactionItem();
                item.setLogId(-1);
                PersPersonItem persPersonItem = persPersonService.getItemByPin(operator);
                if (persPersonItem != null) {
                    item.setPin(operator);
                    item.setName(StringUtils.isNotBlank(persPersonItem.getName()) ? persPersonItem.getName() : "");
                    item.setLastName(
                        StringUtils.isNotBlank(persPersonItem.getLastName()) ? persPersonItem.getLastName() : "");
                    item.setDeptCode(persPersonItem.getDeptCode());
                    item.setDeptName(
                        StringUtils.isNotBlank(persPersonItem.getDeptName()) ? persPersonItem.getDeptName() : "");
                }
                item.setCardNo("");
                Date date = new Date();
                String logTime = DateUtil.dateToString(date, "yyyy-MM-dd HH:mm:ss");
                short verifytype = 200;
                AccQueryDeviceItem accDevice = accDeviceService.getQueryItemBySn(sn);
                AccDoor accDoor = accDoorDao.findByDoorNoAndDevice_Id(Short.parseShort(doorNo), accDevice.getId());
                item.setAreaName(accDevice.getAuthAreaName());
                item.setDevId(accDevice.getId());
                item.setDevSn(accDevice.getSn());
                item.setDevAlias(accDevice.getAlias());
                item.setEventTime(date);
                // 按照acctransaction该字段的入库规则构造
                String uniqueKey = accDevice.getSn() + "_" + logTime + "_" + eventNo + "_" + verifytype + "___"
                    + AccConstants.EVENT_POINT_TYPE_DOOR + "_" + accDoor.getId() + "_2__";
                item.setUniqueKey(uniqueKey);
                // 需要处理的远程类型事件在此map里就定义了
                item.setEventName(AccConstants.PUBLIC_EVENT.get(eventNo));
                item.setEventAddr(Short.valueOf(doorNo));
                item.setEventNo(eventNo.shortValue());
                item.setEventPointType(AccConstants.EVENT_POINT_TYPE_DOOR.shortValue());
                item.setEventPointId(accDoor.getId());
                item.setEventPointName(accDoor.getName());
                item.setReaderState((short)2);
                item.setReaderName(I18nUtil.i18nCode("common_reader_state_2"));

                item.setVerifyModeNo(verifytype);
                item.setVerifyModeName(accDeviceVerifyModeService.getVerifyMode(sn, String.valueOf(verifytype)));
                return item;
            }
        }
        return null;
    }

}
