package com.zkteco.zkbiosecurity.acc.service.impl;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.app.vo.AccAppDeviceItem;
import com.zkteco.zkbiosecurity.acc.app.vo.AccAppDoorItem;
import com.zkteco.zkbiosecurity.acc.app.vo.AccAppTransactionItem;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.*;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.model.AccLevelPerson;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.adms.bean.DeviceDoorState;
import com.zkteco.zkbiosecurity.adms.service.AdmsDeviceService;
import com.zkteco.zkbiosecurity.auth.constants.AppConstant;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.auth.vo.AuthUserItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.AppResultMessage;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.dashboard.vo.DashboardDeviceCountItem;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.system.utils.BaseLanguageUtil;

/**
 * <AUTHOR>
 * @Date: 2018/12/5 17:44
 */
@Service
@Transactional
public class AccAppServiceImpl implements AccAppService {

    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccDeviceEventService accDeviceEventService;
    @Autowired(required = false)
    private AdmsDeviceService admsDeviceService;
    @Autowired
    private AccTransactionDao accTransactionDao;
    @Autowired
    private AccAlarmMonitorService accAlarmMonitorService;
    @Autowired
    private AccRTMonitorService accRTMonitorService;

    @Autowired
    private PersPersonService persPersonService;

    @Autowired
    private AccAppTopDoorByPersonDao accAppTopDoorByPersonDao;

    @Autowired
    private AccLevelService accLevelService;

    @Autowired
    private AccDeviceDao accDeviceDao;

    @Autowired
    private AccTransactionService accTransactionService;

    @Autowired
    private AccLevelDoorDao accLevelDoorDao;
    @Autowired
    private AccLevelPersonDao accLevelPersonDao;

    @Override
    public AppResultMessage getDoorByDoorName(String token, String doorName, int pageNo, int pageSize) {
        return getDoorByDoorName(token, doorName, pageNo, pageSize, null);
    }

    @Deprecated
    @Override
    public AppResultMessage getDoorByDoorName(String token, String doorName, int pageNo, int pageSize,
        String loginType) {
        // modify by xjiang.huang 2023-07-18 新增登录类型
        AccAppDoorItem condition = new AccAppDoorItem();
        condition.setFilter(doorName);
        return getDoorByDoorName(token, condition, pageNo, pageSize, loginType);
    }

    @Override
    public AppResultMessage getDoorByDoorName(String token, AccAppDoorItem accAppDoorItem, int pageNo, int pageSize,
        String loginType) {
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        // modify by xjiang.huang 2023-07-18 新增登录类型
        String querySql = buildDoorCondition(token, accAppDoorItem, loginType);
        if (StringUtils.isNotBlank(querySql)) {
            Pager pager = accTransactionDao.getItemsBySql(AccDoorItem.class, querySql, pageNo - 1, pageSize);
            // Pager pager = accDoorService.getItemsByPage(accDoorItem, pageNo - 1, pageSize);
            List<AccDoorItem> accDoorItemList = (List<AccDoorItem>)pager.getData();
            JSONObject dataJson = new JSONObject();
            JSONArray doorJsonList = new JSONArray();
            if (!accDoorItemList.isEmpty()) {
                String areaIds = CollectionUtil.getPropertys(accDoorItemList, AccDoorItem::getAuthAreaId);
                List<AuthAreaItem> authAreaItemList = authAreaService.getItemsByIds(areaIds);
                Map<String, AuthAreaItem> authAreaItemMap = CollectionUtil.itemListToIdMap(authAreaItemList);
                Map<String, List<AccDoorItem>> accDoorMap =
                    accDoorItemList.stream().collect(Collectors.groupingBy(AccDoorItem::getDeviceSn));
                accDoorMap.forEach((devSn, doorList) -> {
                    DeviceDoorState deviceDoorState = admsDeviceService.getDeviceDoorState(devSn);
                    String connect = accDeviceService.getStatus(devSn);
                    AccQueryDeviceItem accQueryDeviceItem = accDeviceService.getQueryItemBySn(devSn);
                    doorList.forEach(door -> {
                        int index = door.getDoorNo() - 1;
                        short alarmLevel = accDeviceOptionService.isSupportFun(devSn, "~RelayStateFunOn")
                            ? AccConstants.ENABLE : AccConstants.DISABLE;

                        int doorValue = Integer.parseInt(StringUtils.isNotBlank(deviceDoorState.getDoor())
                            ? deviceDoorState.getDoor().split(",")[index] : "0");

                        int alarm = Integer.parseInt(StringUtils.isNotBlank(deviceDoorState.getAlarm())
                            ? deviceDoorState.getAlarm().split(",")[index] : "0");

                        // 继电器 增加继电器、门磁状态返回 modify by xjiang.huang 2023-07-18
                        int relay = Integer.parseInt(StringUtils.isNotBlank(deviceDoorState.getRelay())
                            ? deviceDoorState.getRelay().split(",")[index] : "0");

                        // 门磁
                        int sensor = Integer.parseInt(StringUtils.isNotBlank(deviceDoorState.getSensor())
                            ? deviceDoorState.getSensor().split(",")[index] : "0");

                        JSONObject doorJson = new JSONObject();
                        doorJson.put("id", door.getId());
                        doorJson.put("name", door.getName());
                        doorJson.put("devName", accQueryDeviceItem.getAlias());
                        int doorConnect = Integer.parseInt(connect);
                        if (StringUtils.isNotBlank(deviceDoorState.getDoor())
                            && deviceDoorState.getDoor().split(",").length > index) {
                            doorConnect = Integer.parseInt(deviceDoorState.getDoor().split(",")[index]);
                        }

                        int doorState = Short.parseShort(connect);
                        if (!door.getEnabled()) {
                            doorState = AccConstants.DEV_STATE_DISABLE;
                        } else if (Short.parseShort(connect) == ConstUtil.DEV_STATE_ONLINE) {
                            if ((doorConnect & 1) == ConstUtil.DEV_STATE_OFFLINE) {
                                doorState = ConstUtil.DEV_STATE_OFFLINE;
                            } else {
                                doorState = doorConnect;
                            }
                        }
                        doorJson.put("connect", doorState);
                        doorJson.put("alarmLevel", alarmLevel);
                        doorJson.put("alarm", alarm);
                        // modify by xjiang.huang 2023-07-18 新增返回时区 门磁、继电器状态
                        doorJson.put("timeZone", door.getTimeZone());
                        doorJson.put("relay", relay);
                        doorJson.put("sensor", sensor);
                        doorJson.put("doorValue", doorValue);

                        if (authAreaItemMap.get(door.getAuthAreaId()) != null) {
                            // 区域存在，设置区域名称
                            doorJson.put("areaName", authAreaItemMap.get(door.getAuthAreaId()).getName());
                        }
                        doorJsonList.add(doorJson);
                    });
                });
            }
            dataJson.put("totalCount", pager.getTotal());
            dataJson.put("rows", doorJsonList);
            appResultMessage.setData(dataJson);
        }
        return appResultMessage;
    }

    @Override
    public AppResultMessage getDoorDataByDoorIdAndFilters(String doorId, String filters, int pageNo, int pageSize) {
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        AccTransactionItem accTransactionItem = new AccTransactionItem();
        accTransactionItem.setEventPointId(doorId);
        String querySql = getTransactionSqlByFilter(accTransactionItem, filters);
        Pager pager = accTransactionDao.getItemsBySql(accTransactionItem.getClass(), querySql, pageNo, pageSize);
        List<AccTransactionItem> accTransactionItemList = (List<AccTransactionItem>)pager.getData();
        List<AccAppTransactionItem> accAppTransactionItemList = buildAppTransactions(accTransactionItemList);
        JSONObject data = new JSONObject();
        data.put("totalCount", pager.getTotal());
        data.put("rows", accAppTransactionItemList);
        appResultMessage.setData(data);
        return appResultMessage;
    }

    @Override
    public AppResultMessage getReportByFilters(String doorId, String filters, Date startTime, Date endTime, int pageNo,
        int pageSize) {
        return getReportByFilters(doorId, filters, startTime, endTime, pageNo, pageSize, null, null, null, null);
    }

    private String buildDoorCondition(String token, AccAppDoorItem accAppDoorItem, String loginType) {
        String username = EncrypAESUtil.decryptToString(token).split("_", 2)[0];
        AccDoorItem accDoorItem = new AccDoorItem();

        String areaIds = accAppDoorItem.getSelectAreaId();
        // 原接口只支持管理员用户查询，混合云app新增支持员工查询门列表 add by xjing.huang 2023-07-18
        if ("PERS".equals(loginType)) {
            PersPersonItem persPersonItem = persPersonService.getItemByPin(username);
            if (persPersonItem == null) {
                return null;
            }
            List<String> doorIds = accLevelService.getDoorIdsForApp(persPersonItem.getId());
            if (CollectionUtil.isEmpty(doorIds)) {
                return null;
            }
            accDoorItem.setInId(StringUtils.join(doorIds, ","));

        } else {

            AuthUserItem authUserItem = authUserService.getItemByUsername(username);
            if (authUserItem == null) {
                // 用户不存在，返回空
                return null;
            }
            if (StringUtils.isBlank(areaIds)) {
                if (!authUserItem.getIsSuperuser()) {
                    areaIds = authUserItem.getAreaIds();
                    if (StringUtils.isBlank(areaIds)) {
                        // 非超级用户且未授权区域，返回空
                        return null;
                    }
                    accDoorItem.setAreaIdIn(areaIds);
                }
            } else {
                // app仅展示选中区域的设备
                accDoorItem.setAreaIdIn(areaIds);
            }

        }

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(SQLUtil.getSqlByItem(accDoorItem));
        String filter = accAppDoorItem.getFilter();
        if (StringUtils.isNotBlank(filter)) {
            // 根据区域名称、门名称或设备名称过滤查询门
            AuthAreaItem condition = new AuthAreaItem();
            condition.setName(filter);
            List<AuthAreaItem> areaItems = authAreaService.getByCondition(condition);
            int orderByIndex = stringBuilder.indexOf("ORDER BY");
            if (CollectionUtils.isNotEmpty(areaItems)) {
                List<String> stringList = new ArrayList<>();
                for (AuthAreaItem areaItem : areaItems) {
                    stringList.add("''" + areaItem.getId() + "''");
                }
                areaIds = StringUtils.strip(stringList.toString(), "[]");
                stringBuilder.insert(orderByIndex, MessageFormat.format(
                    "AND (t.NAME LIKE ''%{0}%'' OR d.DEVICE_NAME = ''%{0}%'' OR  d.AUTH_AREA_ID in (" + areaIds + ")) ",
                    filter));
            } else {
                stringBuilder.insert(orderByIndex,
                    MessageFormat.format("AND (t.NAME LIKE ''%{0}%'' OR d.DEVICE_NAME = ''%{0}%'') ", filter));
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 根据过滤条件组装事件记录查询sql
     * 
     * @auther lambert.li
     * @date 2018/12/10 10:39
     * @param accTransactionItem
     * @param filters
     * @return
     */
    private String getTransactionSqlByFilter(AccTransactionItem accTransactionItem, String filters) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(SQLUtil.getSqlByItem(accTransactionItem));
        int orderByIndex = stringBuilder.indexOf("ORDER BY");
        if (StringUtils.isNotBlank(filters)) {
            Set<String> eventList = accDeviceEventService.getAllEventNameSet();
            List<String> eventNameList = new ArrayList<>();
            for (String eventName : eventList) {
                String name = I18nUtil.i18nCode(eventName);
                if (!name.contains(filters)) {
                    continue;
                }
                eventNameList.add(eventName);
            }
            if (eventNameList.isEmpty()) {
                // 设置过滤条件
                stringBuilder.insert(orderByIndex,
                    MessageFormat
                        .format("AND (t.PIN LIKE ''%{0}%'' OR  t.NAME LIKE ''%{0}%'' OR t.AREA_NAME LIKE ''%{0}%''"
                            + "OR t.LAST_NAME LIKE ''%{0}%'' OR t.EVENT_POINT_NAME LIKE ''%{0}%'') ", filters));
            } else {
                // 设置过滤条件
                String eventNames = StringUtils.join(eventNameList, ",");
                eventNames = SQLUtil.withSingleQuote(eventNames, false);
                stringBuilder.insert(orderByIndex, MessageFormat.format(
                    "AND (t.PIN LIKE ''%{0}%'' OR  t.NAME LIKE ''%{0}%'' OR t.AREA_NAME LIKE ''%{0}%''"
                        + "OR t.LAST_NAME LIKE ''%{0}%'' OR t.EVENT_POINT_NAME LIKE ''%{0}%'' OR t.EVENT_NAME in ({1})) ",
                    filters, eventNames));
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 组装门禁事件记录
     * 
     * @auther lambert.li
     * @date 2018/12/10 10:42
     * @param accTransactionItemList
     * @return
     */
    private List<AccAppTransactionItem> buildAppTransactions(List<AccTransactionItem> accTransactionItemList) {
        List<AccAppTransactionItem> accAppTransactionItemList = new ArrayList<>();
        List<PersPersonItem> persPersonItems = persPersonService
            .getItemsByPins(CollectionUtil.getPropertyList(accTransactionItemList, AccTransactionItem::getPin, "-1"));
        Map<String, PersPersonItem> personItemMap =
            CollectionUtil.listToKeyMap(persPersonItems, PersPersonItem::getPin);
        if (!accTransactionItemList.isEmpty()) {
            accTransactionItemList.forEach(trans -> {
                AccAppTransactionItem accAppTransactionItem = new AccAppTransactionItem();
                short eventNo = trans.getEventNo();
                ModelUtil.copyPropertiesIgnoreNull(trans, accAppTransactionItem);
                accAppTransactionItem
                    .setEventTime(DateUtil.dateToString(trans.getEventTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                accAppTransactionItem.setPersonName(trans.getName());
                short eventLevel = accDeviceEventService.getDefaultEventTypeById(eventNo);
                if (eventNo >= AccConstants.BACK_VERIFY_GAPB_LOGICAL && eventNo != AccConstants.EVENT_DISCONNECT) {
                    eventLevel = AccConstants.EVENT_WARNING;
                } else if (eventNo == AccConstants.EVENT_DISCONNECT) {
                    eventLevel = AccConstants.EVENT_ALARM;
                }
                accAppTransactionItem.setStatus(eventLevel == AccConstants.EVENT_NORMAL ? "normal"
                    : (eventLevel == AccConstants.EVENT_WARNING ? "warning" : "alarm"));
                accAppTransactionItem.setEventName(I18nUtil.i18nCode(trans.getEventName()));
                accAppTransactionItem.setVerifyModeName(I18nUtil.i18nCode(trans.getVerifyModeName()));
                String name = StringUtils.isNotBlank(trans.getName()) ? trans.getName() : "";
                if (StringUtils.isNotBlank(trans.getLastName())) {
                    name += " " + trans.getLastName();
                }
                accAppTransactionItem.setName(name);

                String capturePhotoPath = accAppTransactionItem.getCapturePhotoPath();
                if (StringUtils.isNotBlank(capturePhotoPath) && !capturePhotoPath.startsWith(FileUtil.separator)) {
                    capturePhotoPath = FileUtil.separator + capturePhotoPath;
                }
                accAppTransactionItem.setCapturePhotoPath(capturePhotoPath);
                // 新增返回人员头像路径
                if (StringUtils.isNotBlank(trans.getPin()) && Objects.nonNull(personItemMap.get(trans.getPin()))) {
                    String persPhotoPath = FileUtil.getThumbPath(personItemMap.get(trans.getPin()).getPhotoPath());
                    // 照片路径返回格式为：/upload....(例：/upload/pers/user/avatar/2024-06-12/3.jpg)
                    if (StringUtils.isNotBlank(persPhotoPath) && !persPhotoPath.startsWith(FileUtil.separator)) {
                        persPhotoPath = FileUtil.separator + persPhotoPath;
                    }
                    accAppTransactionItem.setPersPhotoPath(persPhotoPath);
                    accAppTransactionItem.setPersPositionName(personItemMap.get(trans.getPin()).getPositionName());
                }

                accAppTransactionItemList.add(accAppTransactionItem);
            });
        }
        return accAppTransactionItemList;
    }

    @Override
    public JSONArray getAlarmEventData(String eventNos) {
        // 从报警监控中获取数据
        JSONArray jsonArray = new JSONArray();
        AccAlarmMonitorItem item = new AccAlarmMonitorItem();
        item.setStatus(Short.valueOf("0"));
        List<AccAlarmMonitorItem> accAlarmMonitorItems = accAlarmMonitorService.getAlarmEventByCondition(item);
        for (AccAlarmMonitorItem accAlarmMonitorItem : accAlarmMonitorItems) {
            JSONObject jsonObject = accAlarmMonitorService.createAppData(accAlarmMonitorItem);
            jsonArray.add(jsonObject);
        }
        return jsonArray;
    }

    @Override
    public List<AccAppTransactionItem> getRTMonitorData(String clientId) {
        List<AccTransactionItem> accTransactionItemList = accRTMonitorService.getRTMonitorData(clientId);
        return buildAppTransactions(accTransactionItemList);
    }

    @Override
    public AppResultMessage getReportByFilters(String doorId, String filters, Date startTime, Date endTime, int pageNo,
        int pageSize, String userName, String userId, String loginType, String inEventNo) {
        AccTransactionItem accTransactionItem = new AccTransactionItem();
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        long limitCount = 200000L;
        if (StringUtils.isNotBlank(doorId)) {
            accTransactionItem.setEventPointId(doorId);
            accTransactionItem.setEquals(true);
        }
        if (startTime != null && endTime != null) {
            accTransactionItem.setStartTime(startTime);
            accTransactionItem.setEndTime(endTime);
        }
        // ------------------新增根据loginType、eventNo条件过滤 start----------------------
        if (StringUtils.isNotBlank(inEventNo)) {
            accTransactionItem.setInEventNo(inEventNo);
        }
        if ("PERS".equals(loginType) && StringUtils.isNotBlank(userName)) {
            accTransactionItem.setPinEqual(userName);
        } else if ("NORMAL".equals(loginType)) {
            AuthUserItem authUserItem = authUserService.getItemByUsername(userName);
            if (authUserItem == null) {
                // 用户不存在，返回空
                return appResultMessage;
            }
            if (!authUserItem.getIsSuperuser()) {

                accTransactionItem.setAreaNameByUserId(userId);
            }
        }
        // ------------------新增根据loginType、eventNo条件过滤 end----------------------
        String querySql = getTransactionSqlByFilter(accTransactionItem, filters);
        Pager pager =
            accTransactionDao.getItemsBySql(accTransactionItem.getClass(), querySql, pageNo - 1, pageSize, limitCount);
        List<AccTransactionItem> accTransactionItemList = (List<AccTransactionItem>)pager.getData();
        List<AccAppTransactionItem> accAppTransactionItemList = buildAppTransactions(accTransactionItemList);
        JSONObject data = new JSONObject();
        data.put("totalCount", pager.getTotal());
        data.put("rows", accAppTransactionItemList);
        appResultMessage.setData(data);
        return appResultMessage;
    }

    @Override
    public AppResultMessage getReportDetail(String id) {
        AppResultMessage resultMessage = AppResultMessage.successMessage();
        AccTransactionItem item = accTransactionService.getItemById(id);
        if (Objects.nonNull(item)) {
            List<AccTransactionItem> items = new ArrayList<>();
            items.add(item);
            AccAppTransactionItem accAppTransactionItem = buildAppTransactions(items).get(0);
            resultMessage.setData(accAppTransactionItem);
        }
        return resultMessage;
    }

    @Override
    public AppResultMessage getAccDeviceCount(String areaIds) {
        DashboardDeviceCountItem deviceCountItem = new DashboardDeviceCountItem();
        List<AccDevice> accDeviceList = accDeviceDao.findAll();
        if (!accDeviceList.isEmpty()) {
            long onlineDeviceCount = 0;
            long offDeviceCount = 0;
            long deviceCount = 0;
            for (AccDevice dev : accDeviceList) {
                if (StringUtils.isNotBlank(areaIds) && !areaIds.contains(dev.getAuthAreaId())) {
                    continue;
                }
                deviceCount++;
                boolean isOnline = admsDeviceService.isOnline(dev.getSn());
                if (isOnline) {
                    onlineDeviceCount++;
                } else {
                    offDeviceCount++;
                }
            }
            deviceCountItem.setDeviceCount(deviceCount);
            deviceCountItem.setOnlineDeviceCount(onlineDeviceCount);
            deviceCountItem.setOfflineDeviceCount(offDeviceCount);
        }
        return AppResultMessage.successMessage().setData(deviceCountItem);
    }

    @Override
    public AppResultMessage getAccDevicesPager(AccDeviceItem condition, int pageNo, int pageSize, String userName) {
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        AuthUserItem authUserItem = authUserService.getItemByUsername(userName);
        if (authUserItem == null) {
            // 用户不存在，返回空
            return appResultMessage;
        }
        if (!authUserItem.getIsSuperuser()) {
            condition.setUserId(authUserItem.getId());
        }
        Pager pager = accDeviceService.loadPagerByAuthFilter(null, condition, pageNo, pageSize);
        List<AccDeviceItem> accDeviceItemList = (List<AccDeviceItem>)pager.getData();
        List<AccAppDeviceItem> accAppDeviceItems = new ArrayList<>();
        if (!accDeviceItemList.isEmpty()) {
            accAppDeviceItems = ModelUtil.copyListProperties(accDeviceItemList, AccAppDeviceItem.class);
        }
        JSONObject data = new JSONObject();
        data.put("totalCount", pager.getTotal());
        data.put("rows", accAppDeviceItems);
        return appResultMessage.setData(data);
    }

    @Override
    public AppResultMessage loadPagerByAuthFilter(String username, AccLevelItem codition, int pageNo, int pageSize) {
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        AuthUserItem authUserItem = authUserService.getItemByUsername(username);
        if (authUserItem == null) {
            // 用户不存在，返回空
            return appResultMessage;
        }
        String areaIds = null;
        if (!authUserItem.getIsSuperuser()) {
            areaIds = authUserItem.getAreaIds();
            if (StringUtils.isBlank(areaIds)) {
                // 非超级用户且未授权区域，返回空
                return appResultMessage;
            }
            if (StringUtils.isNotBlank(codition.getAreaIds())) {
                codition.setAreaIds(codition.getAreaIds());
            } else {
                codition.setAreaIds(areaIds);
            }
        }
        Pager pager = accLevelService.getItemsByPage(codition, pageNo, pageSize);
        List<AccLevelItem> accLevelItemList = (List<AccLevelItem>)pager.getData();
        for (AccLevelItem accLevelItem : accLevelItemList) {
            // 如果不是超级用户登录则根据当前登录用户的区域权限获取门数量 by mingfa.zheng 20190102
            if (StringUtils.isNotBlank(areaIds)) {
                accLevelItem.setDoorCount(String.valueOf(
                    accLevelDoorDao.getDoorCountByLevelIdAndAreaIds(accLevelItem.getId(), StrUtil.strToList(areaIds))));
            } else {
                accLevelItem.setDoorCount(String.valueOf(accLevelDoorDao.getLevelDoorCount(accLevelItem.getId())));
            }
        }
        JSONObject data = new JSONObject();
        data.put("totalCount", pager.getTotal());
        data.put("rows", accLevelItemList);
        return appResultMessage.setData(data);
    }

    @Override
    public AppResultMessage operateDoorByLevel(String userName, String type, String interval, String levelIds) {
        AuthUserItem authUserItem = authUserService.getItemByUsername(userName);
        if (authUserItem == null) {
            // 用户不存在，返回空
            return AppResultMessage.successMessage();
        }
        String levelDoorIds = "";
        String areaIds = "";
        if (!authUserItem.getIsSuperuser()) {
            areaIds = authUserItem.getAreaIds();
        }
        List<String> doorIds = new ArrayList<>();
        if (authUserItem.getIsSuperuser()) {
            doorIds = accLevelDoorDao.getDoorIdsByLevelIdsIn(StrUtil.strToList(levelIds));
        } else if (StringUtils.isNotBlank(areaIds)) {
            doorIds = accLevelDoorDao.getDoorIdsByLevelIdsInAndAreaIdsIn(StrUtil.strToList(levelIds),
                StrUtil.strToList(areaIds));
        }
        if (doorIds != null && doorIds.size() > 0) {
            levelDoorIds = StringUtils.join(doorIds, ",");
        }
        Map<String, String> dataMap = accRTMonitorService.operateDoor(type, interval, levelDoorIds);
        return dealResultData(dataMap);
    }

    @Override
    public AppResultMessage dealResultData(Map<String, String> dataMap) {
        String cmdIdData = dataMap.get("cmdId");
        if (StringUtils.isNotBlank(cmdIdData)) {
            for (String cmdData : cmdIdData.split(",")) {
                String cmdId = cmdData.split("=", 2)[0];
                Map<String, String> resultMap = accDeviceService.getCmdResultById(Long.valueOf(cmdId), 20);
                if (Objects.isNull(resultMap) || Objects.isNull(Integer.parseInt(resultMap.get("result")))
                    || Integer.parseInt(resultMap.get("result")) < 0) {
                    // 命令执行失败
                    return AppResultMessage.failMessage();
                }
            }
        } else {
            if ("true".equals(dataMap.get("notExistDev"))) {
                // 设备不存在
                return AppResultMessage.failMessage().setRet(String.valueOf(AccConstants.ACC_DEV_NOTEXIST));
            }
            if (StringUtils.isNotBlank(dataMap.get("offline"))) {
                // 设备离线
                return AppResultMessage.failMessage().setRet(String.valueOf(AccConstants.ACC_DEV_OFFLINE));
            }
            if (StringUtils.isNotBlank(dataMap.get("notSupport"))) {
                // 设备不支持
                return AppResultMessage.failMessage().setRet(String.valueOf(AccConstants.ACC_DEV_NOTSUPPORTFUNCTION));
            }
            if (StringUtils.isNotBlank(dataMap.get("faile"))) {
                // 下发失败
                return AppResultMessage.failMessage().setRet(String.valueOf(AccConstants.ACC_DEV_ISSUEDFAILED));
            }
        }
        return AppResultMessage.successMessage();
    }

    @Override
    public AppResultMessage getAccLevelsByCodition(String loginType, String username, AccLevelItem codition, int pageNo,
        int pageSize) {
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        if (AppConstant.USER_LOGIN_TYPE.equals(loginType)) {
            AuthUserItem authUserItem = authUserService.getItemByUsername(username);
            if (authUserItem == null) {
                // 用户不存在，返回空
                return appResultMessage;
            }
            if (!authUserItem.getIsSuperuser()) {
                String areaIds = authUserItem.getAreaIds();
                if (StringUtils.isBlank(areaIds)) {
                    // 非超级用户且未授权区域，返回空
                    return appResultMessage;
                }
                codition.setAreaIds(areaIds);
            }
        }

        List<AccLevelItem> accLevelItemList = accLevelService.getByCondition(codition);
        if (!accLevelItemList.isEmpty()) {
            JSONObject data = new JSONObject();
            data.put("totalCount", accLevelItemList.size());
            data.put("rows", accLevelItemList);
            appResultMessage.setData(data);
        }
        return appResultMessage;
    }

    @Override
    public AppResultMessage addLevelPerson(String lang, String pin, String levelIds) {
        AppResultMessage ars = new AppResultMessage();
        // 所加人员非空校验
        if (StringUtils.isBlank(pin)) {
            return AppResultMessage.message(PersConstants.PERSONPIN_ISNULL + "",
                BaseLanguageUtil.getI18nByAppLang(lang, "pers_import_pinNotEmpty"));
        }
        if (StringUtils.isNotBlank(levelIds)) {
            List<String> levelIdList = StrUtil.strToList(levelIds);

            JSONArray array = new JSONArray();
            int error = 0;
            for (String levelId : levelIdList) {
                AppResultMessage rs = new AppResultMessage();
                try {
                    PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
                    rs = addAppPersonLevel(lang, persPersonItem, levelId);
                    if (!"OK".equals(rs.getRet())) {
                        String name = StringUtils
                            .trim(StringUtils.defaultIfEmpty(pin, "") + " " + StringUtils.defaultIfEmpty(levelId, ""));
                        array.add(name + ":" + rs.getMessage());
                        error++;
                    }
                } catch (ZKBusinessException e) {
                    String name = StringUtils
                        .trim(StringUtils.defaultIfEmpty(pin, "") + " " + StringUtils.defaultIfEmpty(levelId, ""));
                    array.add(name + ":" + rs.getMessage());
                    error++;
                } catch (Exception e) {
                    rs = AppResultMessage.message(AccConstants.API_PROGRAM_ERROR + "",
                        BaseLanguageUtil.getI18nByAppLang(lang, "common_api_programError"));
                }
            }
            int success = levelIdList.size() - error;
            ars.setMessage(I18nUtil.i18nCode("pers_import_result", success, error));
            ars.setData(array);
        }
        return ars;
    }

    private AppResultMessage addAppPersonLevel(String lang, PersPersonItem persPersonItem, String levelId) {
        // 权限组ID不为空
        if (StringUtils.isBlank(levelId)) {
            return AppResultMessage.message(AccConstants.LEVEL_ACC_LEVELIDNOTNULL + "",
                BaseLanguageUtil.getI18nByAppLang(lang, "acc_api_levelIdNotNull"));
        }
        // 判断人员是否存在
        if (persPersonItem == null) {
            return AppResultMessage.message(PersConstants.PERSONID_NOTEXIST + "",
                BaseLanguageUtil.getI18nByAppLang(lang, "pers_api_personNotExist"));
        }
        // 人员禁用不可编辑
        if (persPersonItem.getEnabledCredential() != null && !persPersonItem.getEnabledCredential()) {
            return AppResultMessage.message(PersConstants.PERSON_DISABLED_NOPOP + "",
                BaseLanguageUtil.getI18nByAppLang(lang, "pers_person_disabledNotOp"));
        }

        AccLevelItem accLevelItem = accLevelService.getItemById(levelId);

        // 判断权限组是否存在
        if (accLevelItem == null) {
            return AppResultMessage.message(AccConstants.LEVEL_NOTEXIST + "",
                BaseLanguageUtil.getI18nByAppLang(lang, "acc_api_levelNotExist"));
        }
        AccLevelPerson accLevelPerson =
            accLevelPersonDao.findByAccLevel_IdAndPersPersonId(levelId, persPersonItem.getId());
        // 增加
        if (accLevelPerson == null) {
            accLevelService.addPersonLevel(levelId, persPersonItem.getId());
        } else {
            return AppResultMessage.message(AccConstants.LEVEL_ACC_LevelEXIST + "",
                BaseLanguageUtil.getI18nByAppLang(lang, "acc_api_levelExist"));
        }
        return new AppResultMessage();
    }
}
