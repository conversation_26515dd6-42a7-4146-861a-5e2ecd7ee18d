package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import com.zkteco.zkbiosecurity.line.service.LineGetAccDoorService;
import com.zkteco.zkbiosecurity.line.vo.LineGetAccDoorItem;

/**
 * <AUTHOR>
 * @date 2019-10-10 14:08
 */
@Service
@Transactional
public class LineGetAccDoorServiceImpl implements LineGetAccDoorService {
    @Autowired
    private AccDoorService accDoorService;

    @Override
    public List<LineGetAccDoorItem> getAccDoorItems() {
        List<AccDoorItem> accDoorItems = accDoorService.getAccDoorItems();
        List<LineGetAccDoorItem> lineGetAccDoorItems = new ArrayList<LineGetAccDoorItem>();
        LineGetAccDoorItem item = null;
        if (accDoorItems != null) {
            for (AccDoorItem accDoorItem : accDoorItems) {
                item = new LineGetAccDoorItem();
                item.setDevSn(accDoorItem.getDeviceSn());
                item.setDoorId(accDoorItem.getId());
                item.setDoorName(accDoorItem.getName());
                item.setDoorNo(accDoorItem.getDoorNo());
                lineGetAccDoorItems.add(item);
            }
        }
        return lineGetAccDoorItems;
    }

    @Override
    public List<LineGetAccDoorItem> getAccDoorItemByDoorIdIn(Collection<String> eventPointIdList) {
        List<AccDoorItem> accDoorItems = accDoorService.getItemsByIds(eventPointIdList);
        List<LineGetAccDoorItem> lineGetAccDoorItems = new ArrayList<LineGetAccDoorItem>();
        LineGetAccDoorItem item = null;
        for (AccDoorItem accDoorItem : accDoorItems) {
            item = new LineGetAccDoorItem();
            item.setDevSn(accDoorItem.getDeviceSn());
            item.setDoorId(accDoorItem.getId());
            item.setDoorName(accDoorItem.getName());
            item.setDoorNo(accDoorItem.getDoorNo());
            lineGetAccDoorItems.add(item);
        }
        return lineGetAccDoorItems;
    }
}
