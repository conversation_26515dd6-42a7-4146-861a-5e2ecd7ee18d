package com.zkteco.zkbiosecurity.acc.utils;

import com.google.common.collect.Sets;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.vo.AccQueryDeviceItem;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Service
public class AccDeviceUtil {

    /** 门禁图标类型-门类型 */
    private static final String doorType = "door";
    /** 门禁图标类型-车闸类型 */
    private static final String carGateType = "carGate";
    /** 门禁图标类型-通道闸类型 */
    private static final String channelGateType = "channelGate";

    //
    // /**
    // * 获取设备状态
    // *
    // * @param devSn
    // * @return
    // */
    // public static short getDevConnectState(String devSn)
    // {
    // JSONObject retJson = accRedisService.getDevConnectState(AccConstants.DEV_RTMONITOR_STATE + devSn);
    // if (retJson != null )
    // {
    // return retJson.getShort("connect");
    // }
    // return AccConstants.DEV_STATE_ONLINE;
    // }

    /**
     * 将十六进制转化为二进制 "FFFFFF3FF0B601000000000070000000000000000000000000F70FF006000000"
     *
     * @param hexStr
     * @return
     */
    public static char[] getBinary(String hexStr) {
        char[] binaryArray = new char[hexStr.length() * 4];// 固件返回265个事件编号
        int length = 2;// 两个字节转化一次二进制，如 “FF”
        int count = hexStr.length() / length;// eventTypeVal 固件返回的是64个
        int index = 0;
        for (int i = 0; i < count; i++) {
            String tempHexStr = hexStr.substring(i * length, (i + 1) * length);
            String ret = toFullBinaryString(Long.parseLong(tempHexStr, 16));
            char[] tempCharArray = ret.toCharArray();
            for (int j = tempCharArray.length - 1; j >= 0; j--) {
                binaryArray[index] = tempCharArray[j];
                index++;
            }
        }
        return binaryArray;
    }

    /**
     * 将 long 类型数据转成二进制的字符串，不足 8 位数时在前面添“0”以凑足位数
     *
     * @param num
     * @return
     */
    public static String toFullBinaryString(long num) {
        char[] chs = new char[8];
        for (int i = 0; i < 8; i++) {
            chs[8 - 1 - i] = (char)(((num >> i) & 1) + '0');
        }
        return new String(chs);
    }

    /**
     * 把父设备提取出来，并放到列表前面做优先处理
     * 
     * <AUTHOR>
     * @since 2017年11月14日 上午11:31:14
     * @param devList
     * @return
     */
    public static List<AccDevice> moveUpParentDevList(List<AccDevice> devList) {
        Set<AccDevice> parentDevSets = Sets.newHashSet();
        for (AccDevice dev : devList) {
            if (dev.getParentDevice() != null && AccConstants.COMM_TYPE_BEST_MQTT != dev.getCommType()) {
                parentDevSets.add(dev.getParentDevice());
            }
        }
        if (!parentDevSets.isEmpty()) {
            // devList可能已经包含了父设备, 需要对父设备去重
            devList.removeAll(parentDevSets);
            devList.addAll(0, parentDevSets);
        }
        return devList;
    }

    /**
     * 进制卡号转换
     *
     * @param cardNo
     *            卡号
     * @param beforeRadix
     *            当前进制
     * @param afterRadix
     *            目标进制
     * @return
     */
    public static String convertCardNo(String cardNo, int beforeRadix, int afterRadix) {
        cardNo = new BigInteger(cardNo, beforeRadix).toString(afterRadix);// 卡格式为16进制
        return cardNo;
    }

    /**
     * 根据门禁设备图标类型获取图标所在文件夹名称
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2017年11月1日 上午9:25:54
     * @param accDevice
     * @return
     */
    public static String getIconFolderName(AccQueryDeviceItem accDevice) {
        String iconFolderName = "";
        if (Objects.isNull(accDevice.getIconType()) || accDevice.getIconType() == 1) { // 没有图标类型默认“门”图标
            iconFolderName = doorType;
        } else if (accDevice.getIconType() == 2) {
            iconFolderName = carGateType;
        } else if (accDevice.getIconType() == 3) {
            iconFolderName = channelGateType;
        }
        return iconFolderName;
    }

    /**
     * 将二进制数转为十进制数
     *
     * @param binarySource:
     * @return int
     * <AUTHOR>
     * @date 2020-11-16 9:38
     * @since 1.0.0
     */
    public static int binaryToDecimal(String binarySource) {
        // 转换为BigInteger类型
        BigInteger bi = new BigInteger(binarySource, 2);
        // 转换成十进制
        return Integer.parseInt(bi.toString());
    }

    /**
     * 将十进制转为二进制
     *
     * @param decimalData:二进制数
     * @param length:长度
     * @return char[]
     * <AUTHOR>
     * @date 2020-11-23 15:26
     * @since 1.0.0
     */
    public static char[] decimalToBinary(String decimalData, Integer length) {
        char[] binaryArray = new char[length];
        for (int i = 0; i < length; i++) {
            binaryArray[i] = (char)((Integer.parseInt(decimalData) >> i & 1) + '0');
        }
        return binaryArray;
    }

    /**
     * 将设备新验证方式字符串转为正常的二进制格式
     *
     * @param newVerifyMode:新验证方式
     * @return char[]
     * <AUTHOR>
     * @date 2020-12-03 15:47
     * @since 1.0.0
     */
    public static char[] newVerifyModeStrToBinary(String newVerifyMode) {
        char[] newBinaryVfStyles = new char[newVerifyMode.length()];
        int index = 0;
        for (int i = newVerifyMode.length(); i > 0; i--) {
            newBinaryVfStyles[index] = (char)Integer.parseInt(newVerifyMode.substring(i - 1, i));
            index++;
        }
        return newBinaryVfStyles;
    }

    /**
     * 将验证方式转为下发的二进制字符串格式
     *
     * @param verifyMode:验证方式
     * @param isSupportNewVerifyMode 是否支持新验证方式
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020-12-01 14:25
     * @since 1.0.0
     */
    public static String convertVerifyModeToBinary(String verifyMode, boolean isSupportNewVerifyMode) {
        // 支持新验证方式
        if (isSupportNewVerifyMode) {
            // 先转为二进制,在转为通信下发格式
            char[] verify = decimalToBinary(verifyMode, 16);
            String tempVerifyType = "";
            for (int i = verify.length - 1; i >= 0; i--) {
                tempVerifyType += Integer.parseInt(String.valueOf(verify[i]));
            }
            return tempVerifyType;
        }
        return verifyMode;
    }
}
