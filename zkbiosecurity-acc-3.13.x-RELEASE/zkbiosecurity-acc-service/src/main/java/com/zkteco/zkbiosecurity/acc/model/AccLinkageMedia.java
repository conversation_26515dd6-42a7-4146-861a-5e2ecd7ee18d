/**
 * File Name: AccLinkageMedia Created by GenerationTools on 2018-03-16 下午04:41 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.convert.EncryptConverter;
import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 AccLinkageMedia
 * 
 * <AUTHOR>
 * @date: 2018-03-16 下午04:41
 * @version v1.0
 */
@Entity
@Table(name = "ACC_LINKAGE_MEDIA")
@Getter
@Setter
@Accessors(chain = true)
public class AccLinkageMedia extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**  */
    @ManyToOne
    @JoinColumn(name = "LINKAGE_ID", nullable = false)
    private AccLinkage accLinkage;

    /**  */
    @Convert(converter = EncryptConverter.class)
    @Column(name = "MEDIA_CONTENT", nullable = false)
    private String mediaContent;

    /**  */
    @Column(name = "MEDIA_TYPE", nullable = false)
    private Short mediaType;

    public AccLinkageMedia() {

    }

    public AccLinkageMedia(AccLinkage linkage, String mediaContent, Short mediaType) {
        this.accLinkage = linkage;
        this.mediaContent = mediaContent;
        this.mediaType = mediaType;
    }
}