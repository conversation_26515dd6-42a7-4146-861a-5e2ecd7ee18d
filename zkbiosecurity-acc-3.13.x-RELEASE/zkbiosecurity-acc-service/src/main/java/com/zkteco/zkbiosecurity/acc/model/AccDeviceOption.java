/**
 * File Name: AccDeviceOption
 * Created by GenerationTools on 2018-03-20 上午09:48
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 对应百傲瑞达实体 AccDeviceOption
 * <AUTHOR>
 * @date:	2018-03-20 上午09:48
 * @version v1.0
 */
@Entity
@Table(name = "ACC_DEVICE_OPTION")
@Getter
@Setter
@Accessors(chain=true)
public class AccDeviceOption extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;
	@ManyToOne
	@JoinColumn(name="DEV_ID",nullable = false)
	private AccDevice accDevice;

	/**  */
	@Column(name="OPTION_NAME",length=50,nullable=false)
	private String name;

	/**  */
	@Column(name="OPTION_VALUE",length=150,nullable=false)
	private String value;

	/**  */
	@Column(name="OPTION_TYPE",nullable=false)
	private Short type = 0;

}