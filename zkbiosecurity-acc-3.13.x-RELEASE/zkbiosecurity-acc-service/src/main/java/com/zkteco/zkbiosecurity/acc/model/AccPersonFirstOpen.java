package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "ACC_PERSON_FIRSTOPEN")
@Getter
@Setter
@Accessors(chain=true)
public class AccPersonFirstOpen extends BaseModel implements Serializable{

    @Column(name="PERS_PERSON_ID")
    private String persPersonId;

    @ManyToOne
    @JoinColumn(name = "ACC_FIRSTOPEN_ID", nullable = false)
    private AccFirstOpen accFirstOpen;
}
